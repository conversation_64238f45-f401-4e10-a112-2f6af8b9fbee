import React from 'react';
import type { DashboardData, HighRiskEvent } from '@/types/data';
import { useState, useEffect } from 'react';
import EmailNotification from '@/components/EmailNotification';

interface RightSidebarProps {
  dashboardData: DashboardData;
  highRiskEvents: HighRiskEvent[];
  width: number;
}

const RightSidebar: React.FC<RightSidebarProps> = ({
  dashboardData,
  highRiskEvents,
  width,
}) => {
  const [activeTab, setActiveTab] = useState<'attack' | 'host'>('attack');
  const [activeS6000Tab, setActiveS6000Tab] = useState<number>(0); // 0-4 对应5个表格

  // 自动切换标签页
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveTab(prev => prev === 'attack' ? 'host' : 'attack');
    }, 10000); // 每10秒切换一次

    return () => clearInterval(interval);
  }, []);

  // S6000网省联动自动切换
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveS6000Tab(prev => (prev + 1) % 5); // 循环切换0-4
    }, 15000); // 每15秒切换一次

    return () => clearInterval(interval);
  }, []);

  return (
    <section className="flex flex-col h-full gap-3 p-3" style={{ width: `${width}px` }}>
      {/* 邮件通知 */}
      <EmailNotification />
      
      {/* S6000 网省联动 - 瀑布流五个表格 */}
      <div
        className="bg-black p-3 rounded-lg shadow-glow-blue flex-grow min-h-0 border border-[#00d9ff]/20"
        style={{ minHeight: '50%' }}
      >
        <h2 className="text-sm font-medium relative flex items-center justify-between mb-3">
          <span className="text-[#00d9ff] uppercase tracking-widest">S6000 网省联动</span>
        </h2>

        <div className="h-full overflow-hidden pr-1">
          {/* 自动切换显示容器 */}
          <div className="h-full relative">
            {/* 切换指示器 */}
            <div className="absolute top-0 right-0 z-10 flex gap-1 p-2">
              {[0, 1, 2, 3, 4].map((index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full transition-all duration-300 ${
                    activeS6000Tab === index ? 'bg-[#00d9ff]' : 'bg-slate-600'
                  }`}
                />
              ))}
            </div>

            {/* 1. 工作任务 - 三栏布局 */}
            {activeS6000Tab === 0 && (
            <div className="bg-gradient-to-br from-slate-900/60 to-slate-800/40 rounded-lg border border-cyan-400/30 h-full animate-fade-in backdrop-blur-sm shadow-lg shadow-cyan-500/10">
              <h3 className="text-xs font-medium text-cyan-300 px-3 py-2 border-b border-cyan-400/20 bg-gradient-to-r from-cyan-900/20 to-transparent">工作任务</h3>
              {dashboardData.workTasks && dashboardData.workTasks.length > 0 ? (
                <div className="flex flex-col h-96">
                {/* 上栏：状态可视化 */}
                <div className="h-8 px-3 py-1 bg-gradient-to-r from-slate-800/60 to-slate-700/40 border-b border-cyan-400/20 flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-amber-400 rounded-full shadow-sm shadow-amber-400/50"></div>
                      <span className="text-xxs text-amber-300">未反馈: {dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').length}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-emerald-400 rounded-full shadow-sm shadow-emerald-400/50"></div>
                      <span className="text-xxs text-emerald-300">已反馈: {dashboardData.workTasks.filter(t => t.feedback_status === '已反馈').length}</span>
                    </div>
                  </div>
                  {dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').length === 0 && (
                    <div className="flex items-center gap-1">
                      <div className="w-3 h-3 bg-emerald-400 rounded-full flex items-center justify-center shadow-sm shadow-emerald-400/50">
                        <span className="text-slate-900 text-xxs font-bold">✓</span>
                      </div>
                      <span className="text-xxs text-emerald-300 font-medium">全部已反馈</span>
                    </div>
                  )}
                </div>
                
                {/* 中栏：未反馈滑动 */}
                {dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').length > 0 && (
                  <div className="flex-1 overflow-hidden border-b border-cyan-400/20">
                    <div className="h-full overflow-hidden px-1 pt-1">
                      <div
                        className="infinite-scroll-list"
                        style={{
                          animation: `workTasksUnfinishedScroll ${dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').length * 4}s infinite ease-in-out`
                        }}
                      >
                        <style>{`
                          @keyframes workTasksUnfinishedScroll {
                            ${dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').map((_, index) => {
                              const totalItems = dashboardData.workTasks?.filter(t => t.feedback_status === '未反馈').length || 0;
                              const itemDuration = 100 / totalItems;
                              const stayPercent = itemDuration * 0.75;
                              const startPercent = index * itemDuration;
                              const stayEndPercent = startPercent + stayPercent;
                              const endPercent = (index + 1) * itemDuration;
                              const currentPosition = -index * 95;
                              const nextPosition = -(index + 1) * 95;
                              return `
                                ${startPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                                ${stayEndPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                                ${endPercent.toFixed(2)}% { transform: translateY(${nextPosition}px); }
                              `;
                            }).join('')}
                            100% { transform: translateY(-${(dashboardData.workTasks?.filter(t => t.feedback_status === '未反馈').length || 0) * 118}px); }
                          }
                        `}</style>
                        {dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').map((task) => (
                          <div key={`task-unfinished-${task.id}`} className="flex flex-col py-3 px-4 bg-gradient-to-br from-amber-900/40 to-amber-800/20 rounded-lg mb-2 border border-amber-400/30 shadow-sm shadow-amber-400/10" style={{height: '110px', minHeight: '110px'}}>
                            <div className="flex items-start justify-between mb-1">
                              <div className="flex items-center gap-2">
                                <span className="text-xxs font-medium px-2 py-0.5 rounded bg-amber-400/20 text-amber-300 border border-amber-400/30">{task.type}</span>
                                <h3 className="font-semibold text-amber-200 text-sm leading-tight">{task.title}</h3>
                              </div>
                              <div className="flex flex-col items-center">
                                <div className="w-3 h-3 bg-amber-400 rounded-full animate-pulse mb-1 shadow-sm shadow-amber-400/50"></div>
                                <span className="text-xxs text-amber-300 font-medium">未反馈</span>
                              </div>
                            </div>
                            <p className="text-amber-100 text-xxs mb-2 leading-relaxed truncate">{task.description}</p>
                            <div className="flex items-center justify-between text-xxs text-slate-300">
                              <span>编号: {task.task_id}</span>
                              <div className="flex items-center gap-2">
                                <span>发布: {new Date(task.publish_time).toLocaleDateString()}</span>
                                <span className="text-amber-300">截止: {new Date(task.deadline_time).toLocaleDateString()}</span>
                              </div>
                            </div>
                          </div>
                        ))}
                        {dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').map((task) => (
                          <div key={`task-unfinished-repeat-${task.id}`} className="flex flex-col py-3 px-4 bg-gradient-to-br from-amber-900/40 to-amber-800/20 rounded-lg mb-2 border border-amber-400/30 shadow-sm shadow-amber-400/10" style={{height: '110px', minHeight: '110px'}}>
                            <div className="flex items-start justify-between mb-1">
                              <div className="flex items-center gap-2">
                                <span className="text-xxs font-medium px-2 py-0.5 rounded bg-amber-400/20 text-amber-300 border border-amber-400/30">{task.type}</span>
                                <h3 className="font-semibold text-amber-200 text-sm leading-tight">{task.title}</h3>
                              </div>
                              <div className="flex flex-col items-center">
                                <div className="w-3 h-3 bg-amber-400 rounded-full animate-pulse mb-1 shadow-sm shadow-amber-400/50"></div>
                                <span className="text-xxs text-amber-300 font-medium">未反馈</span>
                              </div>
                            </div>
                            <p className="text-amber-100 text-xxs mb-2 leading-relaxed truncate">{task.description}</p>
                            <div className="flex items-center justify-between text-xxs text-slate-300">
                              <span>编号: {task.task_id}</span>
                              <div className="flex items-center gap-2">
                                <span>发布: {new Date(task.publish_time).toLocaleDateString()}</span>
                                <span className="text-amber-300">截止: {new Date(task.deadline_time).toLocaleDateString()}</span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* 警戒线分隔 */}
                <div className="relative py-2">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-dashed border-cyan-400/50"></div>
                  </div>
                  <div className="relative flex justify-center text-xxs">
                    <span className="bg-gradient-to-r from-slate-900 via-black to-slate-900 px-3 text-cyan-300 font-medium">已反馈任务</span>
                  </div>
                </div>

                {/* 下栏：已反馈滑动 */}
                <div className="flex-1 overflow-hidden">
                  <div className="h-full overflow-hidden px-1 pt-1">
                    <div
                      className="infinite-scroll-list"
                      style={{
                        animation: `workTasksFinishedScroll ${dashboardData.workTasks.filter(t => t.feedback_status === '已反馈').length * 4}s infinite ease-in-out`
                      }}
                    >
                      <style>{`
                        @keyframes workTasksFinishedScroll {
                          ${dashboardData.workTasks.filter(t => t.feedback_status === '已反馈').map((_, index) => {
                            const totalItems = dashboardData.workTasks?.filter(t => t.feedback_status === '已反馈').length || 0;
                            const itemDuration = 100 / totalItems;
                            const stayPercent = itemDuration * 0.75;
                            const startPercent = index * itemDuration;
                            const stayEndPercent = startPercent + stayPercent;
                            const endPercent = (index + 1) * itemDuration;
                            const currentPosition = -index * 95;
                            const nextPosition = -(index + 1) * 95;
                            return `
                              ${startPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                              ${stayEndPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                              ${endPercent.toFixed(2)}% { transform: translateY(${nextPosition}px); }
                            `;
                          }).join('')}
                          100% { transform: translateY(-${(dashboardData.workTasks?.filter(t => t.feedback_status === '已反馈').length || 0) * 118}px); }
                        }
                      `}</style>
                      {dashboardData.workTasks.filter(t => t.feedback_status === '已反馈').map((task) => (
                        <div key={`task-finished-${task.id}`} className="flex flex-col py-3 px-4 bg-gradient-to-br from-emerald-900/40 to-emerald-800/20 rounded-lg mb-2 border border-emerald-400/30 shadow-sm shadow-emerald-400/10" style={{height: '110px', minHeight: '110px'}}>
                          <div className="flex items-start justify-between mb-1">
                            <div className="flex items-center gap-2">
                              <span className="text-xxs font-medium px-2 py-0.5 rounded bg-emerald-400/20 text-emerald-300 border border-emerald-400/30">{task.type}</span>
                              <h3 className="font-semibold text-emerald-200 text-sm leading-tight">{task.title}</h3>
                            </div>
                            <div className="flex flex-col items-center">
                              <div className="w-3 h-3 bg-emerald-400 rounded-full mb-1 shadow-sm shadow-emerald-400/50"></div>
                              <span className="text-xxs text-emerald-300 font-medium">已反馈</span>
                            </div>
                          </div>
                          <p className="text-emerald-100 text-xxs mb-2 leading-relaxed truncate">{task.description}</p>
                          <div className="flex items-center justify-between text-xxs text-slate-300">
                            <span>编号: {task.task_id}</span>
                            <div className="flex items-center gap-2">
                              <span>反馈: {task.feedback_person}</span>
                              <span>发布: {new Date(task.publish_time).toLocaleDateString()}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                      {dashboardData.workTasks.filter(t => t.feedback_status === '已反馈').map((task) => (
                        <div key={`task-finished-repeat-${task.id}`} className="flex flex-col py-3 px-4 bg-green-900/30 rounded-lg mb-2 border border-green-500/30" style={{height: '110px', minHeight: '110px'}}>
                          <div className="flex items-start justify-between mb-1">
                            <div className="flex items-center gap-2">
                              <span className="text-xxs font-medium px-2 py-0.5 rounded bg-green-500/25 text-green-300">{task.type}</span>
                              <h3 className="font-semibold text-green-200 text-sm leading-tight">{task.title}</h3>
                            </div>
                            <div className="flex flex-col items-center">
                              <div className="w-3 h-3 bg-green-500 rounded-full mb-1"></div>
                              <span className="text-xxs text-green-400 font-medium">已反馈</span>
                            </div>
                          </div>
                          <p className="text-green-200 text-xxs mb-2 leading-relaxed truncate">{task.description}</p>
                          <div className="flex items-center justify-between text-xxs text-slate-500">
                            <span>编号: {task.task_id}</span>
                            <div className="flex items-center gap-2">
                              <span>反馈: {task.feedback_person}</span>
                              <span>发布: {new Date(task.publish_time).toLocaleDateString()}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-slate-500 text-xs p-3">无工作任务</p>
            )}
          </div>
            )}

            {/* 2. 工作通知 - 单栏滑动 */}
            {activeS6000Tab === 1 && (
            <div className="bg-gradient-to-br from-slate-900/60 to-slate-800/40 rounded-lg border border-blue-400/30 h-full animate-fade-in backdrop-blur-sm shadow-lg shadow-blue-500/10">
              <h3 className="text-xs font-medium text-blue-300 px-3 py-2 border-b border-blue-400/20 bg-gradient-to-r from-blue-900/20 to-transparent">工作通知</h3>
              {dashboardData.workNotifications && dashboardData.workNotifications.length > 0 ? (
                <div className="h-80 overflow-hidden px-1 pt-1">
                <div
                  className="infinite-scroll-list"
                  style={{
                    animation: `workNotificationsScroll ${dashboardData.workNotifications.length * 4}s infinite ease-in-out`
                  }}
                >
                  <style>{`
                    @keyframes workNotificationsScroll {
                      ${dashboardData.workNotifications.map((_, index) => {
                        const totalItems = dashboardData.workNotifications?.length || 0;
                        const itemDuration = 100 / totalItems;
                        const stayPercent = itemDuration * 0.75;
                        const startPercent = index * itemDuration;
                        const stayEndPercent = startPercent + stayPercent;
                        const endPercent = (index + 1) * itemDuration;
                        const currentPosition = -index * 85;
                        const nextPosition = -(index + 1) * 85;
                        return `
                          ${startPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                          ${stayEndPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                          ${endPercent.toFixed(2)}% { transform: translateY(${nextPosition}px); }
                        `;
                      }).join('')}
                      100% { transform: translateY(-${(dashboardData.workNotifications?.length || 0) * 85}px); }
                    }
                  `}</style>
                  {dashboardData.workNotifications.map((notification) => (
                    <div key={`notification-${notification.id}`} className="flex flex-col py-3 px-4 bg-blue-900/30 rounded-lg mb-2 border border-blue-500/30" style={{height: '95px', minHeight: '95px'}}>
                      <div className="flex items-start justify-between mb-1">
                        <div className="flex items-center gap-2">
                          <span className="text-xxs font-medium px-2 py-0.5 rounded bg-blue-500/25 text-blue-300">{notification.type}</span>
                          <h3 className="font-semibold text-blue-200 text-sm leading-tight">{notification.title}</h3>
                        </div>
                      </div>
                      <p className="text-blue-200 text-xxs mb-2 leading-relaxed truncate">{notification.description}</p>
                      <div className="flex items-center justify-between text-xxs text-slate-400">
                        <span>编号: {notification.notification_id}</span>
                        <span>发布: {new Date(notification.publish_time).toLocaleDateString()}</span>
                      </div>
                    </div>
                  ))}
                  {dashboardData.workNotifications.map((notification) => (
                    <div key={`notification-repeat-${notification.id}`} className="flex flex-col py-3 px-4 bg-blue-900/30 rounded-lg mb-2 border border-blue-500/30" style={{height: '95px', minHeight: '95px'}}>
                      <div className="flex items-start justify-between mb-1">
                        <div className="flex items-center gap-2">
                          <span className="text-xxs font-medium px-2 py-0.5 rounded bg-blue-500/25 text-blue-300">{notification.type}</span>
                          <h3 className="font-semibold text-blue-200 text-sm leading-tight">{notification.title}</h3>
                        </div>
                      </div>
                      <p className="text-blue-200 text-xxs mb-2 leading-relaxed truncate">{notification.description}</p>
                      <div className="flex items-center justify-between text-xxs text-slate-400">
                        <span>编号: {notification.notification_id}</span>
                        <span>发布: {new Date(notification.publish_time).toLocaleDateString()}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <p className="text-slate-500 text-xs p-3">无工作通知</p>
            )}
          </div>
            )}

            {/* 3. 攻击源预警 - 单栏滑动 */}
            {activeS6000Tab === 2 && (
            <div className="bg-slate-900/40 rounded-lg border border-slate-700/30 h-full animate-fade-in">
              <h3 className="text-xs font-medium text-red-300 px-3 py-2 border-b border-slate-700/30">攻击源预警</h3>
              {dashboardData.attackSourceWarnings && dashboardData.attackSourceWarnings.length > 0 ? (
                <div className="h-90 overflow-hidden px-1 pt-1">
                <div
                  className="infinite-scroll-list"
                  style={{
                    animation: `attackSourceScroll ${dashboardData.attackSourceWarnings.length * 4}s infinite ease-in-out`
                  }}
                >
                  <style>{`
                    @keyframes attackSourceScroll {
                      ${dashboardData.attackSourceWarnings.map((_, index) => {
                        const totalItems = dashboardData.attackSourceWarnings?.length || 0;
                        const itemDuration = 100 / totalItems;
                        const stayPercent = itemDuration * 0.75;
                        const startPercent = index * itemDuration;
                        const stayEndPercent = startPercent + stayPercent;
                        const endPercent = (index + 1) * itemDuration;
                        const currentPosition = -index * 85;
                        const nextPosition = -(index + 1) * 85;
                        return `
                          ${startPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                          ${stayEndPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                          ${endPercent.toFixed(2)}% { transform: translateY(${nextPosition}px); }
                        `;
                      }).join('')}
                      100% { transform: translateY(-${(dashboardData.attackSourceWarnings?.length || 0) * 85}px); }
                    }
                  `}</style>
                  {dashboardData.attackSourceWarnings.map((warning) => (
                    <div key={`attack-warning-${warning.id}`} className="flex flex-col py-3 px-4 bg-red-900/30 rounded-lg mb-2 border border-red-500/30" style={{height: '95px', minHeight: '95px'}}>
                      <div className="flex items-start justify-between mb-1">
                        <div className="flex items-center gap-2">
                          <span className="text-xxs font-medium px-2 py-0.5 rounded bg-red-500/25 text-red-300">攻击源预警</span>
                          <h3 className="font-semibold text-red-200 text-sm leading-tight">{warning.title}</h3>
                        </div>
                      </div>
                      <p className="text-red-200 text-xxs mb-2 leading-relaxed truncate">{warning.description}</p>
                      <div className="flex items-center justify-between text-xxs text-slate-400">
                        <span>编号: {warning.warning_id}</span>
                        <div className="flex items-center gap-2">
                          <span>点击: {warning.hits}</span>
                          <span>发布: {new Date(warning.publish_time).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                  {dashboardData.attackSourceWarnings.map((warning) => (
                    <div key={`attack-warning-repeat-${warning.id}`} className="flex flex-col py-2 px-3 bg-red-900/30 rounded-lg mb-2 border border-red-500/30" style={{height: '75px', minHeight: '75px'}}>
                      <div className="flex items-start justify-between mb-1">
                        <div className="flex items-center gap-2">
                          <span className="text-xxs font-medium px-2 py-0.5 rounded bg-red-500/25 text-red-300">攻击源预警</span>
                          <h3 className="font-semibold text-red-200 text-sm leading-tight">{warning.title}</h3>
                        </div>
                      </div>
                      <p className="text-red-200 text-xxs mb-2 leading-relaxed truncate">{warning.description}</p>
                      <div className="flex items-center justify-between text-xxs text-slate-400">
                        <span>编号: {warning.warning_id}</span>
                        <div className="flex items-center gap-2">
                          <span>点击: {warning.hits}</span>
                          <span>发布: {new Date(warning.publish_time).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <p className="text-slate-500 text-xs p-3">无攻击源预警</p>
            )}
          </div>
            )}

          {/* 4. 漏洞预警 - 三栏布局 */}
          {activeS6000Tab === 3 && (
          <div className="bg-slate-900/40 rounded-lg border border-slate-700/30 h-full animate-fade-in">
            <h3 className="text-xs font-medium text-orange-300 px-3 py-2 border-b border-slate-700/30">漏洞预警</h3>
            {dashboardData.vulnerabilityWarnings && dashboardData.vulnerabilityWarnings.length > 0 ? (
              <div className="flex flex-col h-90">
                {/* 上栏：状态可视化 */}
                <div className="h-8 px-3 py-1 bg-slate-800/50 border-b border-slate-700/30 flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                      <span className="text-xxs text-orange-300">进行中: {dashboardData.vulnerabilityWarnings.filter(v => v.status === '进行中').length}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-xxs text-green-300">已完成: {dashboardData.vulnerabilityWarnings.filter(v => v.status === '已完成').length}</span>
                    </div>
                  </div>
                  {dashboardData.vulnerabilityWarnings.filter(v => v.status === '进行中').length === 0 && (
                    <div className="flex items-center gap-1">
                      <div className="w-3 h-3 bg-green-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xxs">✓</span>
                      </div>
                      <span className="text-xxs text-green-300 font-medium">全部已完成</span>
                    </div>
                  )}
                </div>

                {/* 中栏：进行中滑动 */}
                {dashboardData.vulnerabilityWarnings.filter(v => v.status === '进行中').length > 0 && (
                  <div className="flex-1 overflow-hidden border-b border-slate-700/30">
                    <div className="h-full overflow-hidden px-1 pt-1">
                      <div
                        className="infinite-scroll-list"
                        style={{
                          animation: `vulnWarningsOngoingScroll ${dashboardData.vulnerabilityWarnings.filter(v => v.status === '进行中').length * 4}s infinite ease-in-out`
                        }}
                      >
                        <style>{`
                          @keyframes vulnWarningsOngoingScroll {
                            ${dashboardData.vulnerabilityWarnings.filter(v => v.status === '进行中').map((_, index) => {
                              const totalItems = dashboardData.vulnerabilityWarnings?.filter(v => v.status === '进行中').length || 0;
                              const itemDuration = 100 / totalItems;
                              const stayPercent = itemDuration * 0.75;
                              const startPercent = index * itemDuration;
                              const stayEndPercent = startPercent + stayPercent;
                              const endPercent = (index + 1) * itemDuration;
                              const currentPosition = -index * 100;
                              const nextPosition = -(index + 1) * 100;
                              return `
                                ${startPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                                ${stayEndPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                                ${endPercent.toFixed(2)}% { transform: translateY(${nextPosition}px); }
                              `;
                            }).join('')}
                            100% { transform: translateY(-${(dashboardData.vulnerabilityWarnings?.filter(v => v.status === '进行中').length || 0) * 100}px); }
                          }
                        `}</style>
                        {dashboardData.vulnerabilityWarnings.filter(v => v.status === '进行中').map((warning) => (
                          <div key={`vuln-ongoing-${warning.id}`} className="flex flex-col py-2 px-3 bg-orange-900/30 rounded-lg mb-2 border border-orange-500/30" style={{height: '90px', minHeight: '90px'}}>
                            <div className="flex items-start justify-between mb-1">
                              <div className="flex items-center gap-2">
                                <span className="text-xxs font-medium px-2 py-0.5 rounded bg-orange-500/25 text-orange-300">{warning.risk_level}</span>
                                <h3 className="font-semibold text-orange-200 text-sm leading-tight">{warning.title.split('\n')[0]}</h3>
                              </div>
                              <div className="flex flex-col items-center">
                                <div className="w-3 h-3 bg-orange-500 rounded-full animate-pulse mb-1"></div>
                                <span className="text-xxs text-orange-400 font-medium">进行中</span>
                              </div>
                            </div>
                            <p className="text-orange-200 text-xxs mb-2 leading-relaxed truncate">下发人员: {warning.publisher}</p>
                            <div className="flex items-center justify-between text-xxs text-slate-400">
                              <span>编号: {warning.warning_id}</span>
                              <div className="flex items-center gap-2">
                                <span>反馈截止: {warning.feedback_deadline.split(' ')[0]}</span>
                                <span>整改截止: {warning.fix_deadline.split(' ')[0]}</span>
                              </div>
                            </div>
                          </div>
                        ))}
                        {dashboardData.vulnerabilityWarnings.filter(v => v.status === '进行中').map((warning) => (
                          <div key={`vuln-ongoing-repeat-${warning.id}`} className="flex flex-col py-2 px-3 bg-orange-900/30 rounded-lg mb-2 border border-orange-500/30" style={{height: '90px', minHeight: '90px'}}>
                            <div className="flex items-start justify-between mb-1">
                              <div className="flex items-center gap-2">
                                <span className="text-xxs font-medium px-2 py-0.5 rounded bg-orange-500/25 text-orange-300">{warning.risk_level}</span>
                                <h3 className="font-semibold text-orange-200 text-sm leading-tight">{warning.title.split('\n')[0]}</h3>
                              </div>
                              <div className="flex flex-col items-center">
                                <div className="w-3 h-3 bg-orange-500 rounded-full animate-pulse mb-1"></div>
                                <span className="text-xxs text-orange-400 font-medium">进行中</span>
                              </div>
                            </div>
                            <p className="text-orange-200 text-xxs mb-2 leading-relaxed truncate">下发人员: {warning.publisher}</p>
                            <div className="flex items-center justify-between text-xxs text-slate-400">
                              <span>编号: {warning.warning_id}</span>
                              <div className="flex items-center gap-2">
                                <span>反馈截止: {warning.feedback_deadline.split(' ')[0]}</span>
                                <span>整改截止: {warning.fix_deadline.split(' ')[0]}</span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* 下栏：已完成滑动 */}
                <div className="flex-1 overflow-hidden">
                  <div className="h-full overflow-hidden px-1 pt-1">
                    <div
                      className="infinite-scroll-list"
                      style={{
                        animation: `vulnWarningsFinishedScroll ${dashboardData.vulnerabilityWarnings.filter(v => v.status === '已完成').length * 4}s infinite ease-in-out`
                      }}
                    >
                      <style>{`
                        @keyframes vulnWarningsFinishedScroll {
                          ${dashboardData.vulnerabilityWarnings.filter(v => v.status === '已完成').map((_, index) => {
                            const totalItems = dashboardData.vulnerabilityWarnings?.filter(v => v.status === '已完成').length || 0;
                            const itemDuration = 100 / totalItems;
                            const stayPercent = itemDuration * 0.75;
                            const startPercent = index * itemDuration;
                            const stayEndPercent = startPercent + stayPercent;
                            const endPercent = (index + 1) * itemDuration;
                            const currentPosition = -index * 100;
                            const nextPosition = -(index + 1) * 100;
                            return `
                              ${startPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                              ${stayEndPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                              ${endPercent.toFixed(2)}% { transform: translateY(${nextPosition}px); }
                            `;
                          }).join('')}
                          100% { transform: translateY(-${(dashboardData.vulnerabilityWarnings?.filter(v => v.status === '已完成').length || 0) * 100}px); }
                        }
                      `}</style>
                      {dashboardData.vulnerabilityWarnings.filter(v => v.status === '已完成').map((warning) => (
                        <div key={`vuln-finished-${warning.id}`} className="flex flex-col py-2 px-3 bg-green-900/30 rounded-lg mb-2 border border-green-500/30" style={{height: '90px', minHeight: '90px'}}>
                          <div className="flex items-start justify-between mb-1">
                            <div className="flex items-center gap-2">
                              <span className="text-xxs font-medium px-2 py-0.5 rounded bg-green-500/25 text-green-300">{warning.risk_level}</span>
                              <h3 className="font-semibold text-green-200 text-sm leading-tight">{warning.title.split('\n')[0]}</h3>
                            </div>
                            <div className="flex flex-col items-center">
                              <div className="w-3 h-3 bg-green-500 rounded-full mb-1"></div>
                              <span className="text-xxs text-green-400 font-medium">已完成</span>
                            </div>
                          </div>
                          <p className="text-green-200 text-xxs mb-2 leading-relaxed truncate">处理人员: {warning.feedback_person}</p>
                          <div className="flex items-center justify-between text-xxs text-slate-500">
                            <span>编号: {warning.warning_id}</span>
                            <div className="flex items-center gap-2">
                              <span>下发: {warning.publisher}</span>
                              {warning.fix_feedback_time && <span>完成: {warning.fix_feedback_time.split(' ')[0]}</span>}
                            </div>
                          </div>
                        </div>
                      ))}
                      {dashboardData.vulnerabilityWarnings.filter(v => v.status === '已完成').map((warning) => (
                        <div key={`vuln-finished-repeat-${warning.id}`} className="flex flex-col py-2 px-3 bg-green-900/30 rounded-lg mb-2 border border-green-500/30" style={{height: '90px', minHeight: '90px'}}>
                          <div className="flex items-start justify-between mb-1">
                            <div className="flex items-center gap-2">
                              <span className="text-xxs font-medium px-2 py-0.5 rounded bg-green-500/25 text-green-300">{warning.risk_level}</span>
                              <h3 className="font-semibold text-green-200 text-sm leading-tight">{warning.title.split('\n')[0]}</h3>
                            </div>
                            <div className="flex flex-col items-center">
                              <div className="w-3 h-3 bg-green-500 rounded-full mb-1"></div>
                              <span className="text-xxs text-green-400 font-medium">已完成</span>
                            </div>
                          </div>
                          <p className="text-green-200 text-xxs mb-2 leading-relaxed truncate">处理人员: {warning.feedback_person}</p>
                          <div className="flex items-center justify-between text-xxs text-slate-500">
                            <span>编号: {warning.warning_id}</span>
                            <div className="flex items-center gap-2">
                              <span>下发: {warning.publisher}</span>
                              {warning.fix_feedback_time && <span>完成: {warning.fix_feedback_time.split(' ')[0]}</span>}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-slate-500 text-xs p-3">无漏洞预警</p>
            )}
          </div>
          )}

          {/* 5. 预警通告 - 单栏滑动 */}
          {activeS6000Tab === 4 && (
          <div className="bg-slate-900/40 rounded-lg border border-slate-700/30 h-full animate-fade-in">
            <h3 className="text-xs font-medium text-purple-300 px-3 py-2 border-b border-slate-700/30">预警通告</h3>
            {dashboardData.warningAnnouncements && dashboardData.warningAnnouncements.length > 0 ? (
              <div className="h-90 overflow-hidden px-1 pt-1">
                <div
                  className="infinite-scroll-list"
                  style={{
                    animation: `announcementsScroll ${dashboardData.warningAnnouncements.length * 4}s infinite ease-in-out`
                  }}
                >
                  <style>{`
                    @keyframes announcementsScroll {
                      ${dashboardData.warningAnnouncements.map((_, index) => {
                        const totalItems = dashboardData.warningAnnouncements?.length || 0;
                        const itemDuration = 100 / totalItems;
                        const stayPercent = itemDuration * 0.75;
                        const startPercent = index * itemDuration;
                        const stayEndPercent = startPercent + stayPercent;
                        const endPercent = (index + 1) * itemDuration;
                        const currentPosition = -index * 85;
                        const nextPosition = -(index + 1) * 85;
                        return `
                          ${startPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                          ${stayEndPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                          ${endPercent.toFixed(2)}% { transform: translateY(${nextPosition}px); }
                        `;
                      }).join('')}
                      100% { transform: translateY(-${(dashboardData.warningAnnouncements?.length || 0) * 85}px); }
                    }
                  `}</style>
                  {dashboardData.warningAnnouncements.map((announcement) => (
                    <div key={`announcement-${announcement.id}`} className="flex flex-col py-2 px-3 bg-purple-900/30 rounded-lg mb-2 border border-purple-500/30" style={{height: '75px', minHeight: '75px'}}>
                      <div className="flex items-start justify-between mb-1">
                        <div className="flex items-center gap-2">
                          <span className="text-xxs font-medium px-2 py-0.5 rounded bg-purple-500/25 text-purple-300">{announcement.type}</span>
                          <h3 className="font-semibold text-purple-200 text-sm leading-tight">{announcement.title}</h3>
                        </div>
                      </div>
                      <p className="text-purple-200 text-xxs mb-2 leading-relaxed truncate">{announcement.description}</p>
                      <div className="flex items-center justify-between text-xxs text-slate-400">
                        <span>编号: {announcement.announcement_id}</span>
                        <div className="flex items-center gap-2">
                          <span>点击: {announcement.hits}</span>
                          <span>发布: {new Date(announcement.publish_time).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                  {dashboardData.warningAnnouncements.map((announcement) => (
                    <div key={`announcement-repeat-${announcement.id}`} className="flex flex-col py-2 px-3 bg-purple-900/30 rounded-lg mb-2 border border-purple-500/30" style={{height: '75px', minHeight: '75px'}}>
                      <div className="flex items-start justify-between mb-1">
                        <div className="flex items-center gap-2">
                          <span className="text-xxs font-medium px-2 py-0.5 rounded bg-purple-500/25 text-purple-300">{announcement.type}</span>
                          <h3 className="font-semibold text-purple-200 text-sm leading-tight">{announcement.title}</h3>
                        </div>
                      </div>
                      <p className="text-purple-200 text-xxs mb-2 leading-relaxed truncate">{announcement.description}</p>
                      <div className="flex items-center justify-between text-xxs text-slate-400">
                        <span>编号: {announcement.announcement_id}</span>
                        <div className="flex items-center gap-2">
                          <span>点击: {announcement.hits}</span>
                          <span>发布: {new Date(announcement.publish_time).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <p className="text-slate-500 text-xs p-3">无预警通告</p>
            )}
          </div>
          )}

          {/* 添加淡入动画样式 */}
          <style>{`
            @keyframes fade-in {
              from { opacity: 0; transform: translateY(10px); }
              to { opacity: 1; transform: translateY(0); }
            }
            .animate-fade-in {
              animation: fade-in 0.5s ease-out;
            }
          `}</style>
        </div>
      </div>
      
      </div>

      {/* 预警平台 */}
      <div
        className="bg-black p-4 rounded-lg shadow-glow-blue flex-grow min-h-0 border border-[#00d9ff]/20"
        style={{ minHeight: '35%' }}
      >
        <h3 className="text-xs font-medium text-[#00d9ff] uppercase tracking-wider mb-2">预警平台</h3>
        {/* 选项卡切换 */}
        <div className="flex mb-3 relative">
          <div className="flex bg-black/80 rounded-lg p-1 relative">
            <button
              onClick={() => setActiveTab('attack')}
              className={`px-3 py-1 text-xs font-medium rounded-md transition-colors duration-200 ${activeTab === 'attack' ? 'bg-[#00d9ff]/20 text-[#00d9ff] shadow-lg' : 'text-slate-400 hover:text-slate-300'}`}
            >高危攻击事件</button>
            <button
              onClick={() => setActiveTab('host')}
              className={`px-3 py-1 text-xs font-medium rounded-md transition-colors duration-200 ${activeTab === 'host' ? 'bg-[#00d9ff]/20 text-[#00d9ff] shadow-lg' : 'text-slate-400 hover:text-slate-300'}`}
            >主机安全事件</button>
          </div>
          {/* 自动切换指示器 */}
          <div className="ml-2 flex items-center">
            <div className="w-2 h-2 bg-[#00d9ff] rounded-full animate-pulse"></div>
            <span className="ml-1 text-xs text-slate-400">自动切换</span>
          </div>
        </div>
        {/* 内容区域 */}
        <div className="overflow-hidden">
          <div className="flex transition-transform duration-500 ease-in-out" style={{ transform: `translateX(${activeTab === 'attack' ? '0%' : '-100%'})` }}>
            {/* 高危攻击事件 */}
            <div className="w-full flex-shrink-0">
              <div className="overflow-hidden">
                <div className="rounded-md overflow-hidden border border-slate-700/30 max-h-48">
                  <div className="overflow-y-auto max-h-48">
                    <table className="min-w-full text-xs table-fixed">
                      <thead className="bg-slate-800/50 sticky top-0">
                        <tr>
                          <th className="px-2 py-1 text-left text-slate-300 font-medium w-1/4">时间</th>
                          <th className="px-2 py-1 text-left text-slate-300 font-medium w-1/4">源IP</th>
                          <th className="px-2 py-1 text-left text-slate-300 font-medium w-1/4">目标IP</th>
                          <th className="px-2 py-1 text-left text-slate-300 font-medium w-1/4">攻击类型</th>
                        </tr>
                      </thead>
                      <tbody>
                        {highRiskEvents.map((event, index) => (
                          <tr key={index} className="border-b border-slate-700/30 hover:bg-slate-800/30">
                            <td className="px-2 py-1 text-slate-400 truncate">{event.alert_time || '--'}</td>
                            <td className="px-2 py-1 text-red-400 truncate">{event.src_ip}</td>
                            <td className="px-2 py-1 text-blue-400 truncate">{event.dst_ip}</td>
                            <td className="px-2 py-1 text-orange-400 truncate">{event.alert_type}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
            {/* 主机安全事件 */}
            <div className="w-full flex-shrink-0">
              <div className="overflow-hidden">
                <div className="rounded-md overflow-hidden border border-slate-700/30 max-h-48">
                  <div className="overflow-y-auto max-h-48">
                    <table className="min-w-full text-xs table-fixed">
                      <thead className="bg-slate-800/50 sticky top-0">
                        <tr>
                          <th className="px-2 py-1 text-left text-slate-300 font-medium w-1/4">时间</th>
                          <th className="px-2 py-1 text-left text-slate-300 font-medium w-1/4">主机</th>
                          <th className="px-2 py-1 text-left text-slate-300 font-medium w-1/4">事件类型</th>
                          <th className="px-2 py-1 text-left text-slate-300 font-medium w-1/4">风险等级</th>
                        </tr>
                      </thead>
                      <tbody>
                        {dashboardData.hostSecurityEvents?.map((event, index) => (
                          <tr key={index} className="border-b border-slate-700/30 hover:bg-slate-800/30">
                            <td className="px-2 py-1 text-slate-400 truncate">{event.alert_time}</td>
                            <td className="px-2 py-1 text-blue-400 truncate">{event.dst_ip}</td>
                            <td className="px-2 py-1 text-yellow-400 truncate">{event.alert_type}</td>
                            <td className="px-2 py-1 text-red-400 truncate">{event.alert_level}</td>
                          </tr>
                        )) || (
                          <tr>
                            <td colSpan={4} className="px-2 py-4 text-center text-slate-500">暂无主机安全事件</td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default RightSidebar;
