// Stub file for three/webgpu module compatibility
// This provides empty exports for modules that don't exist in Three.js 0.152.0

export const WebGPURenderer = class WebGPURenderer {
  constructor() {
    console.warn('WebGPURenderer is not available in Three.js 0.152.0');
  }
};

export const WebGPUBackend = class WebGPUBackend {
  constructor() {
    console.warn('WebGPUBackend is not available in Three.js 0.152.0');
  }
};

export const StorageInstancedBufferAttribute = class StorageInstancedBufferAttribute {
  constructor() {
    console.warn('StorageInstancedBufferAttribute is not available in Three.js 0.152.0');
  }
};

// Export any other WebGPU-related classes that might be imported
export default {};

