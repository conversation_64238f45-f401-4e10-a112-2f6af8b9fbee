import Vector from './base/vector';
import { NumericArray } from '@math.gl/types';
import type Matrix4 from './matrix4';
/**
 * Four-element vector class.
 * Subclass of Array<number>
 */
export default class Vector4 extends Vector {
    static get ZERO(): Vector4;
    constructor(x?: number | Readonly<NumericArray>, y?: number, z?: number, w?: number);
    set(x: number, y: number, z: number, w: number): this;
    copy(array: Readonly<NumericArray>): this;
    fromObject(object: {
        x: number;
        y: number;
        z: number;
        w: number;
    }): this;
    toObject(object: {
        x?: number;
        y?: number;
        z?: number;
        w?: number;
    }): {
        x: number;
        y: number;
        z: number;
        w: number;
    };
    get ELEMENTS(): number;
    get z(): number;
    set z(value: number);
    get w(): number;
    set w(value: number);
    transform(matrix4: Readonly<NumericArray>): this;
    transformByMatrix3(matrix3: Readonly<NumericArray>): this;
    transformByMatrix2(matrix2: Readonly<NumericArray>): this;
    transformByQuaternion(quaternion: Readonly<NumericArray>): this;
    applyMatrix4(m: Matrix4): this;
}
//# sourceMappingURL=vector4.d.ts.map