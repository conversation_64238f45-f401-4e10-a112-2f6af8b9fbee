import { NumberArray } from '@math.gl/types';
export declare function validateVector(v: NumberArray, length: number): boolean;
export declare function checkNumber(value: any): number;
export declare function checkVector<T extends NumberArray>(v: T, length: number, callerName?: string): T;
export declare function deprecated(method: string, version: string): void;
//# sourceMappingURL=validators.d.ts.map