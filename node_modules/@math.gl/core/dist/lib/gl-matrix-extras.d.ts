import { NumericArray } from '@math.gl/types';
export declare function vec2_transformMat4AsVector<T extends NumericArray>(out: T, a: Readonly<NumericArray>, m: Readonly<NumericArray>): T;
export declare function vec3_transformMat4AsVector<T extends NumericArray>(out: T, a: Readonly<NumericArray>, m: Readonly<NumericArray>): T;
export declare function vec3_transformMat2<T extends NumericArray>(out: T, a: Readonly<NumericArray>, m: Readonly<NumericArray>): T;
export declare function vec4_transformMat2<T extends NumericArray>(out: T, a: Readonly<NumericArray>, m: Readonly<NumericArray>): T;
export declare function vec4_transformMat3<T extends NumericArray>(out: T, a: Readonly<NumericArray>, m: Readonly<NumericArray>): T;
//# sourceMappingURL=gl-matrix-extras.d.ts.map