{"version": 3, "sources": ["../../src/index.ts"], "names": ["default", "Vector2", "Vector3", "Vector4", "Matrix3", "Matrix4", "Quaternion", "SphericalCoordinates", "Pose", "<PERSON>uler", "_MathUtils", "assert", "config", "configure", "formatValue", "isArray", "clone", "equals", "exactEquals", "toRadians", "toDegrees", "radians", "degrees", "sin", "cos", "tan", "asin", "acos", "atan", "clamp", "lerp", "withEpsilon", "_SphericalCoordinates", "_Pose", "_<PERSON><PERSON>r"], "mappings": "AAMA,SAAQA,OAAO,IAAIC,OAAnB,QAAiC,mBAAjC;AACA,SAAQD,OAAO,IAAIE,OAAnB,QAAiC,mBAAjC;AACA,SAAQF,OAAO,IAAIG,OAAnB,QAAiC,mBAAjC;AACA,SAAQH,OAAO,IAAII,OAAnB,QAAiC,mBAAjC;AACA,SAAQJ,OAAO,IAAIK,OAAnB,QAAiC,mBAAjC;AACA,SAAQL,OAAO,IAAIM,UAAnB,QAAoC,sBAApC;AAGA,SAAQN,OAAO,IAAIO,oBAAnB,QAA8C,iCAA9C;AACA,SAAQP,OAAO,IAAIQ,IAAnB,QAA8B,gBAA9B;AACA,SAAQR,OAAO,IAAIS,KAAnB,QAA+B,iBAA/B;AAEA,SAAQT,OAAO,IAAIU,UAAnB,QAAoC,kBAApC;AAGA,SAAQV,OAAO,IAAIW,MAAnB,QAAgC,cAAhC;AAEA,SAEEC,MAFF,EAGEC,SAHF,EAIEC,WAJF,EAKEC,OALF,EAMEC,KANF,EAOEC,MAPF,EAQEC,WARF,EASEC,SATF,EAUEC,SAVF,EAYEC,OAZF,EAaEC,OAbF,EAcEC,GAdF,EAeEC,GAfF,EAgBEC,GAhBF,EAiBEC,IAjBF,EAkBEC,IAlBF,EAmBEC,IAnBF,EAoBEC,KApBF,EAqBEC,IArBF,EAsBEC,WAtBF,QAuBO,cAvBP;AA0BA,SAAQ/B,OAAO,IAAIgC,qBAAnB,QAA+C,iCAA/C;AACA,SAAQhC,OAAO,IAAIiC,KAAnB,QAA+B,gBAA/B;AACA,SAAQjC,OAAO,IAAIkC,MAAnB,QAAgC,iBAAhC", "sourcesContent": ["// luma.gl, MIT license\n\n// types\nexport type {TypedArray, NumericArray} from '@math.gl/types';\n\n// classes\nexport {default as Vector2} from './classes/vector2';\nexport {default as Vector3} from './classes/vector3';\nexport {default as Vector4} from './classes/vector4';\nexport {default as Matrix3} from './classes/matrix3';\nexport {default as Matrix4} from './classes/matrix4';\nexport {default as Quaternion} from './classes/quaternion';\n\n// experimental\nexport {default as SphericalCoordinates} from './classes/spherical-coordinates';\nexport {default as Pose} from './classes/pose';\nexport {default as Euler} from './classes/euler';\n\nexport {default as _MathUtils} from './lib/math-utils';\n\n// lib\nexport {default as assert} from './lib/assert';\n\nexport {\n  // math.gl global utility methods\n  config,\n  configure,\n  formatValue,\n  isArray,\n  clone,\n  equals,\n  exactEquals,\n  toRadians,\n  toDegrees,\n  // math.gl \"GLSL\"-style functions\n  radians,\n  degrees,\n  sin,\n  cos,\n  tan,\n  asin,\n  acos,\n  atan,\n  clamp,\n  lerp,\n  withEpsilon\n} from './lib/common';\n\n// DEPRECATED\nexport {default as _SphericalCoordinates} from './classes/spherical-coordinates';\nexport {default as _Pose} from './classes/pose';\nexport {default as _Euler} from './classes/euler';\n"], "file": "index.js"}