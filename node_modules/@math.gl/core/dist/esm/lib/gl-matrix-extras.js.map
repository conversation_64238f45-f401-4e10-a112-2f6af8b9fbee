{"version": 3, "sources": ["../../../src/lib/gl-matrix-extras.ts"], "names": ["vec2_transformMat4AsVector", "out", "a", "m", "x", "y", "w", "vec3_transformMat4AsVector", "z", "vec3_transformMat2", "vec4_transformMat2", "vec4_transformMat3"], "mappings": "AAIA,OAAO,SAASA,0BAAT,CACLC,GADK,EAELC,CAFK,EAGLC,CAHK,EAIF;AACH,QAAMC,CAAC,GAAGF,CAAC,CAAC,CAAD,CAAX;AACA,QAAMG,CAAC,GAAGH,CAAC,CAAC,CAAD,CAAX;AACA,QAAMI,CAAC,GAAGH,CAAC,CAAC,CAAD,CAAD,GAAOC,CAAP,GAAWD,CAAC,CAAC,CAAD,CAAD,GAAOE,CAAlB,IAAuB,GAAjC;AACAJ,EAAAA,GAAG,CAAC,CAAD,CAAH,GAAS,CAACE,CAAC,CAAC,CAAD,CAAD,GAAOC,CAAP,GAAWD,CAAC,CAAC,CAAD,CAAD,GAAOE,CAAnB,IAAwBC,CAAjC;AACAL,EAAAA,GAAG,CAAC,CAAD,CAAH,GAAS,CAACE,CAAC,CAAC,CAAD,CAAD,GAAOC,CAAP,GAAWD,CAAC,CAAC,CAAD,CAAD,GAAOE,CAAnB,IAAwBC,CAAjC;AACA,SAAOL,GAAP;AACD;AAKD,OAAO,SAASM,0BAAT,CACLN,GADK,EAELC,CAFK,EAGLC,CAHK,EAIF;AACH,QAAMC,CAAC,GAAGF,CAAC,CAAC,CAAD,CAAX;AACA,QAAMG,CAAC,GAAGH,CAAC,CAAC,CAAD,CAAX;AACA,QAAMM,CAAC,GAAGN,CAAC,CAAC,CAAD,CAAX;AACA,QAAMI,CAAC,GAAGH,CAAC,CAAC,CAAD,CAAD,GAAOC,CAAP,GAAWD,CAAC,CAAC,CAAD,CAAD,GAAOE,CAAlB,GAAsBF,CAAC,CAAC,EAAD,CAAD,GAAQK,CAA9B,IAAmC,GAA7C;AACAP,EAAAA,GAAG,CAAC,CAAD,CAAH,GAAS,CAACE,CAAC,CAAC,CAAD,CAAD,GAAOC,CAAP,GAAWD,CAAC,CAAC,CAAD,CAAD,GAAOE,CAAlB,GAAsBF,CAAC,CAAC,CAAD,CAAD,GAAOK,CAA9B,IAAmCF,CAA5C;AACAL,EAAAA,GAAG,CAAC,CAAD,CAAH,GAAS,CAACE,CAAC,CAAC,CAAD,CAAD,GAAOC,CAAP,GAAWD,CAAC,CAAC,CAAD,CAAD,GAAOE,CAAlB,GAAsBF,CAAC,CAAC,CAAD,CAAD,GAAOK,CAA9B,IAAmCF,CAA5C;AACAL,EAAAA,GAAG,CAAC,CAAD,CAAH,GAAS,CAACE,CAAC,CAAC,CAAD,CAAD,GAAOC,CAAP,GAAWD,CAAC,CAAC,CAAD,CAAD,GAAOE,CAAlB,GAAsBF,CAAC,CAAC,EAAD,CAAD,GAAQK,CAA/B,IAAoCF,CAA7C;AACA,SAAOL,GAAP;AACD;AAED,OAAO,SAASQ,kBAAT,CACLR,GADK,EAELC,CAFK,EAGLC,CAHK,EAIF;AACH,QAAMC,CAAC,GAAGF,CAAC,CAAC,CAAD,CAAX;AACA,QAAMG,CAAC,GAAGH,CAAC,CAAC,CAAD,CAAX;AACAD,EAAAA,GAAG,CAAC,CAAD,CAAH,GAASE,CAAC,CAAC,CAAD,CAAD,GAAOC,CAAP,GAAWD,CAAC,CAAC,CAAD,CAAD,GAAOE,CAA3B;AACAJ,EAAAA,GAAG,CAAC,CAAD,CAAH,GAASE,CAAC,CAAC,CAAD,CAAD,GAAOC,CAAP,GAAWD,CAAC,CAAC,CAAD,CAAD,GAAOE,CAA3B;AACAJ,EAAAA,GAAG,CAAC,CAAD,CAAH,GAASC,CAAC,CAAC,CAAD,CAAV;AACA,SAAOD,GAAP;AACD;AAID,OAAO,SAASS,kBAAT,CACLT,GADK,EAELC,CAFK,EAGLC,CAHK,EAIF;AACH,QAAMC,CAAC,GAAGF,CAAC,CAAC,CAAD,CAAX;AACA,QAAMG,CAAC,GAAGH,CAAC,CAAC,CAAD,CAAX;AACAD,EAAAA,GAAG,CAAC,CAAD,CAAH,GAASE,CAAC,CAAC,CAAD,CAAD,GAAOC,CAAP,GAAWD,CAAC,CAAC,CAAD,CAAD,GAAOE,CAA3B;AACAJ,EAAAA,GAAG,CAAC,CAAD,CAAH,GAASE,CAAC,CAAC,CAAD,CAAD,GAAOC,CAAP,GAAWD,CAAC,CAAC,CAAD,CAAD,GAAOE,CAA3B;AACAJ,EAAAA,GAAG,CAAC,CAAD,CAAH,GAASC,CAAC,CAAC,CAAD,CAAV;AACAD,EAAAA,GAAG,CAAC,CAAD,CAAH,GAASC,CAAC,CAAC,CAAD,CAAV;AACA,SAAOD,GAAP;AACD;AAED,OAAO,SAASU,kBAAT,CACLV,GADK,EAELC,CAFK,EAGLC,CAHK,EAIF;AACH,QAAMC,CAAC,GAAGF,CAAC,CAAC,CAAD,CAAX;AACA,QAAMG,CAAC,GAAGH,CAAC,CAAC,CAAD,CAAX;AACA,QAAMM,CAAC,GAAGN,CAAC,CAAC,CAAD,CAAX;AACAD,EAAAA,GAAG,CAAC,CAAD,CAAH,GAASE,CAAC,CAAC,CAAD,CAAD,GAAOC,CAAP,GAAWD,CAAC,CAAC,CAAD,CAAD,GAAOE,CAAlB,GAAsBF,CAAC,CAAC,CAAD,CAAD,GAAOK,CAAtC;AACAP,EAAAA,GAAG,CAAC,CAAD,CAAH,GAASE,CAAC,CAAC,CAAD,CAAD,GAAOC,CAAP,GAAWD,CAAC,CAAC,CAAD,CAAD,GAAOE,CAAlB,GAAsBF,CAAC,CAAC,CAAD,CAAD,GAAOK,CAAtC;AACAP,EAAAA,GAAG,CAAC,CAAD,CAAH,GAASE,CAAC,CAAC,CAAD,CAAD,GAAOC,CAAP,GAAWD,CAAC,CAAC,CAAD,CAAD,GAAOE,CAAlB,GAAsBF,CAAC,CAAC,CAAD,CAAD,GAAOK,CAAtC;AACAP,EAAAA,GAAG,CAAC,CAAD,CAAH,GAASC,CAAC,CAAC,CAAD,CAAV;AACA,SAAOD,GAAP;AACD", "sourcesContent": ["/* eslint-disable camelcase */\nimport {NumericArray} from '@math.gl/types';\n// vec2 additions\n\nexport function vec2_transformMat4AsVector<T extends NumericArray>(\n  out: T,\n  a: Readonly<NumericArray>,\n  m: Readonly<NumericArray>\n): T {\n  const x = a[0];\n  const y = a[1];\n  const w = m[3] * x + m[7] * y || 1.0;\n  out[0] = (m[0] * x + m[4] * y) / w;\n  out[1] = (m[1] * x + m[5] * y) / w;\n  return out;\n}\n\n// vec3 additions\n\n// Transform as vector, only uses 3x3 minor matrix\nexport function vec3_transformMat4AsVector<T extends NumericArray>(\n  out: T,\n  a: Readonly<NumericArray>,\n  m: Readonly<NumericArray>\n): T {\n  const x = a[0];\n  const y = a[1];\n  const z = a[2];\n  const w = m[3] * x + m[7] * y + m[11] * z || 1.0;\n  out[0] = (m[0] * x + m[4] * y + m[8] * z) / w;\n  out[1] = (m[1] * x + m[5] * y + m[9] * z) / w;\n  out[2] = (m[2] * x + m[6] * y + m[10] * z) / w;\n  return out;\n}\n\nexport function vec3_transformMat2<T extends NumericArray>(\n  out: T,\n  a: Readonly<NumericArray>,\n  m: Readonly<NumericArray>\n): T {\n  const x = a[0];\n  const y = a[1];\n  out[0] = m[0] * x + m[2] * y;\n  out[1] = m[1] * x + m[3] * y;\n  out[2] = a[2];\n  return out;\n}\n\n// vec4 additions\n\nexport function vec4_transformMat2<T extends NumericArray>(\n  out: T,\n  a: Readonly<NumericArray>,\n  m: Readonly<NumericArray>\n): T {\n  const x = a[0];\n  const y = a[1];\n  out[0] = m[0] * x + m[2] * y;\n  out[1] = m[1] * x + m[3] * y;\n  out[2] = a[2];\n  out[3] = a[3];\n  return out;\n}\n\nexport function vec4_transformMat3<T extends NumericArray>(\n  out: T,\n  a: Readonly<NumericArray>,\n  m: Readonly<NumericArray>\n): T {\n  const x = a[0];\n  const y = a[1];\n  const z = a[2];\n  out[0] = m[0] * x + m[3] * y + m[6] * z;\n  out[1] = m[1] * x + m[4] * y + m[7] * z;\n  out[2] = m[2] * x + m[5] * y + m[8] * z;\n  out[3] = a[3];\n  return out;\n}\n"], "file": "gl-matrix-extras.js"}