{"version": 3, "sources": ["../../../src/lib/common.ts"], "names": ["assert", "RADIANS_TO_DEGREES", "Math", "PI", "DEGREES_TO_RADIANS", "config", "EPSILON", "debug", "precision", "printTypes", "printDegrees", "printRowMajor", "configure", "options", "key", "formatValue", "value", "round", "parseFloat", "toPrecision", "isArray", "Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "DataView", "clone", "array", "slice", "toRadians", "degrees", "radians", "toDegrees", "result", "map", "sin", "angle", "cos", "tan", "asin", "acos", "atan", "clamp", "min", "max", "lerp", "a", "b", "t", "ai", "i", "equals", "epsilon", "oldEpsilon", "length", "abs", "exactEquals", "constructor", "withEpsilon", "func", "oldPrecision", "duplicateArray"], "mappings": "AAGA,OAAOA,MAAP,MAAmB,UAAnB;AAcA,MAAMC,kBAAkB,GAAI,IAAIC,IAAI,CAACC,EAAV,GAAgB,GAA3C;AACA,MAAMC,kBAAkB,GAAI,IAAI,GAAL,GAAYF,IAAI,CAACC,EAA5C;AAGA,OAAO,MAAME,MAA4B,GAAG;AAC1CC,EAAAA,OAAO,EAAE,KADiC;AAE1CC,EAAAA,KAAK,EAAE,KAFmC;AAG1CC,EAAAA,SAAS,EAAE,CAH+B;AAI1CC,EAAAA,UAAU,EAAE,KAJ8B;AAK1CC,EAAAA,YAAY,EAAE,KAL4B;AAM1CC,EAAAA,aAAa,EAAE;AAN2B,CAArC;AASP,OAAO,SAASC,SAAT,CAAmBC,OAAnB,EAAkF;AAEvF,OAAK,MAAMC,GAAX,IAAkBD,OAAlB,EAA2B;AACzBb,IAAAA,MAAM,CAACc,GAAG,IAAIT,MAAR,CAAN;AACAA,IAAAA,MAAM,CAACS,GAAD,CAAN,GAAcD,OAAO,CAACC,GAAD,CAArB;AACD;;AACD,SAAOT,MAAP;AACD;AAQD,OAAO,SAASU,WAAT,CACLC,KADK,EAEL;AAACR,EAAAA,SAAS,GAAGH,MAAM,CAACG;AAApB,IAAuD,EAFlD,EAGG;AACRQ,EAAAA,KAAK,GAAGC,KAAK,CAACD,KAAD,CAAb;AAEA,mBAAUE,UAAU,CAACF,KAAK,CAACG,WAAN,CAAkBX,SAAlB,CAAD,CAApB;AACD;AAQD,OAAO,SAASY,OAAT,CAAiBJ,KAAjB,EAA0C;AAC/C,SAAOK,KAAK,CAACD,OAAN,CAAcJ,KAAd,KAAyBM,WAAW,CAACC,MAAZ,CAAmBP,KAAnB,KAA6B,EAAEA,KAAK,YAAYQ,QAAnB,CAA7D;AACD;AAED,OAAO,SAASC,KAAT,CAAeC,KAAf,EAA8D;AACnE,SAAO,WAAWA,KAAX,GAAmBA,KAAK,CAACD,KAAN,EAAnB,GAAmCC,KAAK,CAACC,KAAN,EAA1C;AACD;AAKD,OAAO,SAASC,SAAT,CAAmBC,OAAnB,EAA0E;AAC/E,SAAOC,OAAO,CAACD,OAAD,CAAd;AACD;AAKD,OAAO,SAASE,SAAT,CAAmBD,OAAnB,EAA0E;AAC/E,SAAOD,OAAO,CAACC,OAAD,CAAd;AACD;AAUD,OAAO,SAASA,OAAT,CACLD,OADK,EAELG,MAFK,EAGkB;AACvB,SAAOC,GAAG,CAACJ,OAAD,EAAWA,OAAD,IAAaA,OAAO,GAAGzB,kBAAjC,EAAqD4B,MAArD,CAAV;AACD;AAQD,OAAO,SAASH,OAAT,CACLC,OADK,EAELE,MAFK,EAGkB;AACvB,SAAOC,GAAG,CAACH,OAAD,EAAWA,OAAD,IAAaA,OAAO,GAAG7B,kBAAjC,EAAqD+B,MAArD,CAAV;AACD;AAMD,OAAO,SAASE,GAAT,CAAaJ,OAAb,EAA6CE,MAA7C,EAA2F;AAChG,SAAOC,GAAG,CAACH,OAAD,EAAWK,KAAD,IAAWjC,IAAI,CAACgC,GAAL,CAASC,KAAT,CAArB,EAAsCH,MAAtC,CAAV;AACD;AAMD,OAAO,SAASI,GAAT,CAAaN,OAAb,EAA6CE,MAA7C,EAA2F;AAChG,SAAOC,GAAG,CAACH,OAAD,EAAWK,KAAD,IAAWjC,IAAI,CAACkC,GAAL,CAASD,KAAT,CAArB,EAAsCH,MAAtC,CAAV;AACD;AAMD,OAAO,SAASK,GAAT,CAAaP,OAAb,EAA6CE,MAA7C,EAA2F;AAChG,SAAOC,GAAG,CAACH,OAAD,EAAWK,KAAD,IAAWjC,IAAI,CAACmC,GAAL,CAASF,KAAT,CAArB,EAAsCH,MAAtC,CAAV;AACD;AAMD,OAAO,SAASM,IAAT,CAAcR,OAAd,EAA8CE,MAA9C,EAA4F;AACjG,SAAOC,GAAG,CAACH,OAAD,EAAWK,KAAD,IAAWjC,IAAI,CAACoC,IAAL,CAAUH,KAAV,CAArB,EAAuCH,MAAvC,CAAV;AACD;AAMD,OAAO,SAASO,IAAT,CAAcT,OAAd,EAA8CE,MAA9C,EAA4F;AACjG,SAAOC,GAAG,CAACH,OAAD,EAAWK,KAAD,IAAWjC,IAAI,CAACqC,IAAL,CAAUJ,KAAV,CAArB,EAAuCH,MAAvC,CAAV;AACD;AAMD,OAAO,SAASQ,IAAT,CAAcV,OAAd,EAA8CE,MAA9C,EAA4F;AACjG,SAAOC,GAAG,CAACH,OAAD,EAAWK,KAAD,IAAWjC,IAAI,CAACsC,IAAL,CAAUL,KAAV,CAArB,EAAuCH,MAAvC,CAAV;AACD;AAQD,OAAO,SAASS,KAAT,CACLzB,KADK,EAEL0B,GAFK,EAGLC,GAHK,EAIkB;AACvB,SAAOV,GAAG,CAACjB,KAAD,EAASA,KAAD,IAAWd,IAAI,CAACyC,GAAL,CAASD,GAAT,EAAcxC,IAAI,CAACwC,GAAL,CAASC,GAAT,EAAc3B,KAAd,CAAd,CAAnB,CAAV;AACD;AAQD,OAAO,SAAS4B,IAAT,CACLC,CADK,EAELC,CAFK,EAGLC,CAHK,EAIkB;AACvB,MAAI3B,OAAO,CAACyB,CAAD,CAAX,EAAgB;AACd,WAAQA,CAAD,CAAoBZ,GAApB,CAAwB,CAACe,EAAD,EAAaC,CAAb,KAA2BL,IAAI,CAACI,EAAD,EAAMF,CAAD,CAAoBG,CAApB,CAAL,EAA6BF,CAA7B,CAAvD,CAAP;AACD;;AACD,SAAOA,CAAC,GAAID,CAAL,GAAoB,CAAC,IAAIC,CAAL,IAAWF,CAAtC;AACD;AAWD,OAAO,SAASK,MAAT,CAAgBL,CAAhB,EAAwBC,CAAxB,EAAgCK,OAAhC,EAA2D;AAChE,QAAMC,UAAU,GAAG/C,MAAM,CAACC,OAA1B;;AACA,MAAI6C,OAAJ,EAAa;AACX9C,IAAAA,MAAM,CAACC,OAAP,GAAiB6C,OAAjB;AACD;;AACD,MAAI;AACF,QAAIN,CAAC,KAAKC,CAAV,EAAa;AACX,aAAO,IAAP;AACD;;AACD,QAAI1B,OAAO,CAACyB,CAAD,CAAP,IAAczB,OAAO,CAAC0B,CAAD,CAAzB,EAA8B;AAC5B,UAAID,CAAC,CAACQ,MAAF,KAAaP,CAAC,CAACO,MAAnB,EAA2B;AACzB,eAAO,KAAP;AACD;;AACD,WAAK,IAAIJ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,CAAC,CAACQ,MAAtB,EAA8B,EAAEJ,CAAhC,EAAmC;AAEjC,YAAI,CAACC,MAAM,CAACL,CAAC,CAACI,CAAD,CAAF,EAAOH,CAAC,CAACG,CAAD,CAAR,CAAX,EAAyB;AACvB,iBAAO,KAAP;AACD;AACF;;AACD,aAAO,IAAP;AACD;;AACD,QAAIJ,CAAC,IAAIA,CAAC,CAACK,MAAX,EAAmB;AACjB,aAAOL,CAAC,CAACK,MAAF,CAASJ,CAAT,CAAP;AACD;;AACD,QAAIA,CAAC,IAAIA,CAAC,CAACI,MAAX,EAAmB;AACjB,aAAOJ,CAAC,CAACI,MAAF,CAASL,CAAT,CAAP;AACD;;AACD,QAAI,OAAOA,CAAP,KAAa,QAAb,IAAyB,OAAOC,CAAP,KAAa,QAA1C,EAAoD;AAClD,aAAO5C,IAAI,CAACoD,GAAL,CAAST,CAAC,GAAGC,CAAb,KAAmBzC,MAAM,CAACC,OAAP,GAAiBJ,IAAI,CAACyC,GAAL,CAAS,CAAT,EAAYzC,IAAI,CAACoD,GAAL,CAAST,CAAT,CAAZ,EAAyB3C,IAAI,CAACoD,GAAL,CAASR,CAAT,CAAzB,CAA3C;AACD;;AACD,WAAO,KAAP;AACD,GA1BD,SA0BU;AACRzC,IAAAA,MAAM,CAACC,OAAP,GAAiB8C,UAAjB;AACD;AACF;AAED,OAAO,SAASG,WAAT,CAAqBV,CAArB,EAA6BC,CAA7B,EAA8C;AACnD,MAAID,CAAC,KAAKC,CAAV,EAAa;AACX,WAAO,IAAP;AACD;;AACD,MAAID,CAAC,IAAI,OAAOA,CAAP,KAAa,QAAlB,IAA8BC,CAA9B,IAAmC,OAAOA,CAAP,KAAa,QAApD,EAA8D;AAC5D,QAAID,CAAC,CAACW,WAAF,KAAkBV,CAAC,CAACU,WAAxB,EAAqC;AACnC,aAAO,KAAP;AACD;;AACD,QAAIX,CAAC,CAACU,WAAN,EAAmB;AACjB,aAAOV,CAAC,CAACU,WAAF,CAAcT,CAAd,CAAP;AACD;AACF;;AACD,MAAI1B,OAAO,CAACyB,CAAD,CAAP,IAAczB,OAAO,CAAC0B,CAAD,CAAzB,EAA8B;AAC5B,QAAID,CAAC,CAACQ,MAAF,KAAaP,CAAC,CAACO,MAAnB,EAA2B;AACzB,aAAO,KAAP;AACD;;AACD,SAAK,IAAIJ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,CAAC,CAACQ,MAAtB,EAA8B,EAAEJ,CAAhC,EAAmC;AACjC,UAAI,CAACM,WAAW,CAACV,CAAC,CAACI,CAAD,CAAF,EAAOH,CAAC,CAACG,CAAD,CAAR,CAAhB,EAA8B;AAC5B,eAAO,KAAP;AACD;AACF;;AACD,WAAO,IAAP;AACD;;AACD,SAAO,KAAP;AACD;AAID,OAAO,SAASQ,WAAT,CAAwBN,OAAxB,EAAyCO,IAAzC,EAA2D;AAChE,QAAMC,YAAY,GAAGtD,MAAM,CAACC,OAA5B;AACAD,EAAAA,MAAM,CAACC,OAAP,GAAiB6C,OAAjB;AACA,MAAInC,KAAJ;;AACA,MAAI;AACFA,IAAAA,KAAK,GAAG0C,IAAI,EAAZ;AACD,GAFD,SAEU;AACRrD,IAAAA,MAAM,CAACC,OAAP,GAAiBqD,YAAjB;AACD;;AACD,SAAO3C,KAAP;AACD;;AAID,SAASC,KAAT,CAAeD,KAAf,EAAsC;AACpC,SAAOd,IAAI,CAACe,KAAL,CAAWD,KAAK,GAAGX,MAAM,CAACC,OAA1B,IAAqCD,MAAM,CAACC,OAAnD;AACD;;AAGD,SAASsD,cAAT,CAAwBlC,KAAxB,EAA2D;AAEzD,SAAOA,KAAK,CAACD,KAAN,GAAcC,KAAK,CAACD,KAAN,EAAd,GAA+B,IAAIJ,KAAJ,CAAUK,KAAK,CAAC2B,MAAhB,CAAtC;AACD;;AAID,SAASpB,GAAT,CACEjB,KADF,EAEE0C,IAFF,EAGE1B,MAHF,EAIyB;AACvB,MAAIZ,OAAO,CAACJ,KAAD,CAAX,EAAoB;AAClB,UAAMU,KAAK,GAAGV,KAAd;AACAgB,IAAAA,MAAM,GAAGA,MAAM,IAAI4B,cAAc,CAAClC,KAAD,CAAjC;;AACA,SAAK,IAAIuB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGjB,MAAM,CAACqB,MAAX,IAAqBJ,CAAC,GAAGvB,KAAK,CAAC2B,MAA/C,EAAuD,EAAEJ,CAAzD,EAA4D;AAC1DjB,MAAAA,MAAM,CAACiB,CAAD,CAAN,GAAYS,IAAI,CAAC1C,KAAK,CAACiC,CAAD,CAAN,EAAWA,CAAX,EAAcjB,MAAd,CAAhB;AACD;;AACD,WAAOA,MAAP;AACD;;AACD,SAAO0B,IAAI,CAAC1C,KAAD,CAAX;AACD", "sourcesContent": ["// Copyright (c) 2017 Uber Technologies, Inc.\n// MIT License\nimport {NumericArray} from '@math.gl/types';\nimport assert from './assert';\n\nimport type MathArray from '../classes/base/math-array';\n\nexport type ConfigurationOptions = {\n  EPSILON: number;\n  debug?: boolean;\n  precision: number;\n  printTypes?: boolean;\n  printDegrees?: boolean;\n  printRowMajor?: boolean;\n  _cartographicRadians?: boolean;\n};\n\nconst RADIANS_TO_DEGREES = (1 / Math.PI) * 180;\nconst DEGREES_TO_RADIANS = (1 / 180) * Math.PI;\n\n// TODO - remove\nexport const config: ConfigurationOptions = {\n  EPSILON: 1e-12,\n  debug: false,\n  precision: 4,\n  printTypes: false,\n  printDegrees: false,\n  printRowMajor: true\n};\n\nexport function configure(options?: Partial<ConfigurationOptions>): ConfigurationOptions {\n  // Only copy existing keys\n  for (const key in options) {\n    assert(key in config);\n    config[key] = options[key];\n  }\n  return config;\n}\n\n/**\n * Formats a value into a string\n * @param value\n * @param param1\n * @returns\n */\nexport function formatValue(\n  value: number,\n  {precision = config.precision}: {precision?: number} = {}\n): string {\n  value = round(value);\n  // get rid of trailing zeros\n  return `${parseFloat(value.toPrecision(precision))}`;\n}\n\n/**\n * Check if value is an \"array\"\n * Returns `true` if value is either an array or a typed array\n *\n * Note: returns `false` for `ArrayBuffer` and `DataView` instances\n */\nexport function isArray(value: unknown): boolean {\n  return Array.isArray(value) || (ArrayBuffer.isView(value) && !(value instanceof DataView));\n}\n\nexport function clone(array: NumericArray | MathArray): NumericArray {\n  return 'clone' in array ? array.clone() : array.slice();\n}\n\nexport function toRadians(degrees: number): number;\nexport function toRadians(degrees: NumericArray): NumericArray;\n\nexport function toRadians(degrees: number | NumericArray): number | NumericArray {\n  return radians(degrees as NumericArray);\n}\n\nexport function toDegrees(degrees: number): number;\nexport function toDegrees(degrees: NumericArray): NumericArray;\n\nexport function toDegrees(radians: number | NumericArray): number | NumericArray {\n  return degrees(radians as NumericArray);\n}\n\n// GLSL math function equivalents - Works on both single values and vectors\n\n/**\n * \"GLSL equivalent\" radians: Works on single values and vectors\n */\nexport function radians(degrees: number): number;\nexport function radians(degrees: NumericArray, result?: NumericArray): NumericArray;\n\nexport function radians(\n  degrees: number | NumericArray,\n  result?: NumericArray\n): number | NumericArray {\n  return map(degrees, (degrees) => degrees * DEGREES_TO_RADIANS, result);\n}\n\n/**\n * \"GLSL equivalent\" degrees: Works on single values and vectors\n */\nexport function degrees(radians: number): number;\nexport function degrees(radians: NumericArray, result?: NumericArray): NumericArray;\n\nexport function degrees(\n  radians: number | NumericArray,\n  result?: NumericArray\n): number | NumericArray {\n  return map(radians, (radians) => radians * RADIANS_TO_DEGREES, result);\n}\n\n/**\n * \"GLSL equivalent\" of `Math.sin`: Works on single values and vectors\n * @deprecated\n */\nexport function sin(radians: number | NumericArray, result?: NumericArray): number | NumericArray {\n  return map(radians, (angle) => Math.sin(angle), result);\n}\n\n/**\n * \"GLSL equivalent\" of `Math.cos`: Works on single values and vectors\n * @deprecated\n */\nexport function cos(radians: number | NumericArray, result?: NumericArray): number | NumericArray {\n  return map(radians, (angle) => Math.cos(angle), result);\n}\n\n/**\n * \"GLSL equivalent\" of `Math.tan`: Works on single values and vectors\n * @deprecated\n */\nexport function tan(radians: number | NumericArray, result?: NumericArray): number | NumericArray {\n  return map(radians, (angle) => Math.tan(angle), result);\n}\n\n/**\n * \"GLSL equivalent\" of `Math.asin`: Works on single values and vectors\n * @deprecated\n */\nexport function asin(radians: number | NumericArray, result?: NumericArray): number | NumericArray {\n  return map(radians, (angle) => Math.asin(angle), result);\n}\n\n/**\n * \"GLSL equivalent\" of `Math.acos`: Works on single values and vectors\n * @deprecated\n */\nexport function acos(radians: number | NumericArray, result?: NumericArray): number | NumericArray {\n  return map(radians, (angle) => Math.acos(angle), result);\n}\n\n/**\n * \"GLSL equivalent\" of `Math.atan`: Works on single values and vectors\n * @deprecated\n */\nexport function atan(radians: number | NumericArray, result?: NumericArray): number | NumericArray {\n  return map(radians, (angle) => Math.atan(angle), result);\n}\n\n/**\n * GLSL style value clamping: Works on single values and vectors\n */\nexport function clamp(value: number, min: number, max: number): number;\nexport function clamp(value: NumericArray, min: number, max: number): NumericArray;\n\nexport function clamp(\n  value: number | NumericArray,\n  min: number,\n  max: number\n): number | NumericArray {\n  return map(value, (value) => Math.max(min, Math.min(max, value)));\n}\n\n/**\n * Interpolate between two numbers or two arrays\n */\nexport function lerp(a: number, b: number, t: number): number;\nexport function lerp(a: NumericArray, b: NumericArray, t: number): NumericArray;\n\nexport function lerp(\n  a: number | NumericArray,\n  b: number | NumericArray,\n  t: number\n): number | NumericArray {\n  if (isArray(a)) {\n    return (a as NumericArray).map((ai: number, i: number) => lerp(ai, (b as NumericArray)[i], t));\n  }\n  return t * (b as number) + (1 - t) * (a as number);\n}\n\n/* eslint-disable */\n\n/**\n * Compares any two math objects, using `equals` method if available.\n * @param a\n * @param b\n * @param epsilon\n * @returns\n */\nexport function equals(a: any, b: any, epsilon?: number): boolean {\n  const oldEpsilon = config.EPSILON;\n  if (epsilon) {\n    config.EPSILON = epsilon;\n  }\n  try {\n    if (a === b) {\n      return true;\n    }\n    if (isArray(a) && isArray(b)) {\n      if (a.length !== b.length) {\n        return false;\n      }\n      for (let i = 0; i < a.length; ++i) {\n        // eslint-disable-next-line max-depth\n        if (!equals(a[i], b[i])) {\n          return false;\n        }\n      }\n      return true;\n    }\n    if (a && a.equals) {\n      return a.equals(b);\n    }\n    if (b && b.equals) {\n      return b.equals(a);\n    }\n    if (typeof a === 'number' && typeof b === 'number') {\n      return Math.abs(a - b) <= config.EPSILON * Math.max(1, Math.abs(a), Math.abs(b));\n    }\n    return false;\n  } finally {\n    config.EPSILON = oldEpsilon;\n  }\n}\n\nexport function exactEquals(a: any, b: any): boolean {\n  if (a === b) {\n    return true;\n  }\n  if (a && typeof a === 'object' && b && typeof b === 'object') {\n    if (a.constructor !== b.constructor) {\n      return false;\n    }\n    if (a.exactEquals) {\n      return a.exactEquals(b);\n    }\n  }\n  if (isArray(a) && isArray(b)) {\n    if (a.length !== b.length) {\n      return false;\n    }\n    for (let i = 0; i < a.length; ++i) {\n      if (!exactEquals(a[i], b[i])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  return false;\n}\n\n/* eslint-enable */\n\nexport function withEpsilon<T>(epsilon: number, func: () => T): T {\n  const oldPrecision = config.EPSILON;\n  config.EPSILON = epsilon;\n  let value: T;\n  try {\n    value = func();\n  } finally {\n    config.EPSILON = oldPrecision;\n  }\n  return value;\n}\n\n// HELPERS\n\nfunction round(value: number): number {\n  return Math.round(value / config.EPSILON) * config.EPSILON;\n}\n\n// If the array has a clone function, calls it, otherwise returns a copy\nfunction duplicateArray(array: NumericArray): NumericArray {\n  // @ts-expect-error We check for math.gl class methods\n  return array.clone ? array.clone() : (new Array(array.length) as number[]);\n}\n\n// If the argument value is an array, applies the func element wise,\n// otherwise applies func to the argument value\nfunction map(\n  value: number | NumericArray,\n  func: (x: number, index?: number, result?: NumericArray) => number,\n  result?: NumericArray\n): number | NumericArray {\n  if (isArray(value)) {\n    const array = value as NumericArray;\n    result = result || duplicateArray(array);\n    for (let i = 0; i < result.length && i < array.length; ++i) {\n      result[i] = func(value[i], i, result);\n    }\n    return result;\n  }\n  return func(value as number);\n}\n"], "file": "common.js"}