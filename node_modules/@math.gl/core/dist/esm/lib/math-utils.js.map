{"version": 3, "sources": ["../../../src/lib/math-utils.ts"], "names": ["EPSILON1", "EPSILON2", "EPSILON3", "EPSILON4", "EPSILON5", "EPSILON6", "EPSILON7", "EPSILON8", "EPSILON9", "EPSILON10", "EPSILON11", "EPSILON12", "EPSILON13", "EPSILON14", "EPSILON15", "EPSILON16", "EPSILON17", "EPSILON18", "EPSILON19", "EPSILON20", "PI_OVER_TWO", "Math", "PI", "PI_OVER_FOUR", "PI_OVER_SIX", "TWO_PI"], "mappings": "AAEA,eAAe;AACbA,EAAAA,QAAQ,EAAE,IADG;AAEbC,EAAAA,QAAQ,EAAE,IAFG;AAGbC,EAAAA,QAAQ,EAAE,IAHG;AAIbC,EAAAA,QAAQ,EAAE,IAJG;AAKbC,EAAAA,QAAQ,EAAE,IALG;AAMbC,EAAAA,QAAQ,EAAE,IANG;AAObC,EAAAA,QAAQ,EAAE,IAPG;AAQbC,EAAAA,QAAQ,EAAE,IARG;AASbC,EAAAA,QAAQ,EAAE,IATG;AAUbC,EAAAA,SAAS,EAAE,KAVE;AAWbC,EAAAA,SAAS,EAAE,KAXE;AAYbC,EAAAA,SAAS,EAAE,KAZE;AAabC,EAAAA,SAAS,EAAE,KAbE;AAcbC,EAAAA,SAAS,EAAE,KAdE;AAebC,EAAAA,SAAS,EAAE,KAfE;AAgBbC,EAAAA,SAAS,EAAE,KAhBE;AAiBbC,EAAAA,SAAS,EAAE,KAjBE;AAkBbC,EAAAA,SAAS,EAAE,KAlBE;AAmBbC,EAAAA,SAAS,EAAE,KAnBE;AAoBbC,EAAAA,SAAS,EAAE,KApBE;AAsBbC,EAAAA,WAAW,EAAEC,IAAI,CAACC,EAAL,GAAU,CAtBV;AAuBbC,EAAAA,YAAY,EAAEF,IAAI,CAACC,EAAL,GAAU,CAvBX;AAwBbE,EAAAA,WAAW,EAAEH,IAAI,CAACC,EAAL,GAAU,CAxBV;AA0BbG,EAAAA,MAAM,EAAEJ,IAAI,CAACC,EAAL,GAAU;AA1BL,CAAf", "sourcesContent": ["// NOTE: Added to make Cesium-derived test cases work\n// TODO: Determine if/how to keep\nexport default {\n  EPSILON1: 1e-1,\n  EPSILON2: 1e-2,\n  EPSILON3: 1e-3,\n  EPSILON4: 1e-4,\n  EPSILON5: 1e-5,\n  EPSILON6: 1e-6,\n  EPSILON7: 1e-7,\n  EPSILON8: 1e-8,\n  EPSILON9: 1e-9,\n  EPSILON10: 1e-10,\n  EPSILON11: 1e-11,\n  EPSILON12: 1e-12,\n  EPSILON13: 1e-13,\n  EPSILON14: 1e-14,\n  EPSILON15: 1e-15,\n  EPSILON16: 1e-16,\n  EPSILON17: 1e-17,\n  EPSILON18: 1e-18,\n  EPSILON19: 1e-19,\n  EPSILON20: 1e-20,\n\n  PI_OVER_TWO: Math.PI / 2,\n  PI_OVER_FOUR: Math.PI / 4,\n  PI_OVER_SIX: Math.PI / 6,\n\n  TWO_PI: Math.PI * 2\n};\n"], "file": "math-utils.js"}