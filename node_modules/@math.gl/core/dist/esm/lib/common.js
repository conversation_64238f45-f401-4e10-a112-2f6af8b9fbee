import assert from './assert';
const RADIANS_TO_DEGREES = 1 / Math.PI * 180;
const DEGREES_TO_RADIANS = 1 / 180 * Math.PI;
export const config = {
  EPSILON: 1e-12,
  debug: false,
  precision: 4,
  printTypes: false,
  printDegrees: false,
  printRowMajor: true
};
export function configure(options) {
  for (const key in options) {
    assert(key in config);
    config[key] = options[key];
  }

  return config;
}
export function formatValue(value, {
  precision = config.precision
} = {}) {
  value = round(value);
  return "".concat(parseFloat(value.toPrecision(precision)));
}
export function isArray(value) {
  return Array.isArray(value) || ArrayBuffer.isView(value) && !(value instanceof DataView);
}
export function clone(array) {
  return 'clone' in array ? array.clone() : array.slice();
}
export function toRadians(degrees) {
  return radians(degrees);
}
export function toDegrees(radians) {
  return degrees(radians);
}
export function radians(degrees, result) {
  return map(degrees, degrees => degrees * DEGREES_TO_RADIANS, result);
}
export function degrees(radians, result) {
  return map(radians, radians => radians * RADIANS_TO_DEGREES, result);
}
export function sin(radians, result) {
  return map(radians, angle => Math.sin(angle), result);
}
export function cos(radians, result) {
  return map(radians, angle => Math.cos(angle), result);
}
export function tan(radians, result) {
  return map(radians, angle => Math.tan(angle), result);
}
export function asin(radians, result) {
  return map(radians, angle => Math.asin(angle), result);
}
export function acos(radians, result) {
  return map(radians, angle => Math.acos(angle), result);
}
export function atan(radians, result) {
  return map(radians, angle => Math.atan(angle), result);
}
export function clamp(value, min, max) {
  return map(value, value => Math.max(min, Math.min(max, value)));
}
export function lerp(a, b, t) {
  if (isArray(a)) {
    return a.map((ai, i) => lerp(ai, b[i], t));
  }

  return t * b + (1 - t) * a;
}
export function equals(a, b, epsilon) {
  const oldEpsilon = config.EPSILON;

  if (epsilon) {
    config.EPSILON = epsilon;
  }

  try {
    if (a === b) {
      return true;
    }

    if (isArray(a) && isArray(b)) {
      if (a.length !== b.length) {
        return false;
      }

      for (let i = 0; i < a.length; ++i) {
        if (!equals(a[i], b[i])) {
          return false;
        }
      }

      return true;
    }

    if (a && a.equals) {
      return a.equals(b);
    }

    if (b && b.equals) {
      return b.equals(a);
    }

    if (typeof a === 'number' && typeof b === 'number') {
      return Math.abs(a - b) <= config.EPSILON * Math.max(1, Math.abs(a), Math.abs(b));
    }

    return false;
  } finally {
    config.EPSILON = oldEpsilon;
  }
}
export function exactEquals(a, b) {
  if (a === b) {
    return true;
  }

  if (a && typeof a === 'object' && b && typeof b === 'object') {
    if (a.constructor !== b.constructor) {
      return false;
    }

    if (a.exactEquals) {
      return a.exactEquals(b);
    }
  }

  if (isArray(a) && isArray(b)) {
    if (a.length !== b.length) {
      return false;
    }

    for (let i = 0; i < a.length; ++i) {
      if (!exactEquals(a[i], b[i])) {
        return false;
      }
    }

    return true;
  }

  return false;
}
export function withEpsilon(epsilon, func) {
  const oldPrecision = config.EPSILON;
  config.EPSILON = epsilon;
  let value;

  try {
    value = func();
  } finally {
    config.EPSILON = oldPrecision;
  }

  return value;
}

function round(value) {
  return Math.round(value / config.EPSILON) * config.EPSILON;
}

function duplicateArray(array) {
  return array.clone ? array.clone() : new Array(array.length);
}

function map(value, func, result) {
  if (isArray(value)) {
    const array = value;
    result = result || duplicateArray(array);

    for (let i = 0; i < result.length && i < array.length; ++i) {
      result[i] = func(value[i], i, result);
    }

    return result;
  }

  return func(value);
}
//# sourceMappingURL=common.js.map