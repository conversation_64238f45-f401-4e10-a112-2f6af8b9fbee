{"version": 3, "sources": ["../../../src/classes/pose.ts"], "names": ["Matrix4", "Vector3", "<PERSON>uler", "Pose", "constructor", "x", "y", "z", "roll", "pitch", "yaw", "position", "orientation", "Array", "isArray", "length", "RollPitchYaw", "value", "getPosition", "getOrientation", "equals", "pose", "exactEquals", "getTransformationMatrix", "sr", "Math", "sin", "sp", "sw", "cr", "cos", "cp", "cw", "setRowMajor", "getTransformationMatrixFromPose", "multiplyRight", "invert", "getTransformationMatrixToPose"], "mappings": ";AAEA,OAAOA,OAAP,MAAoB,WAApB;AACA,OAAOC,OAAP,MAAoB,WAApB;AACA,OAAOC,KAAP,MAAkB,SAAlB;AAcA,eAAe,MAAMC,IAAN,CAAW;AAIxBC,EAAAA,WAAW,CAAC;AACVC,IAAAA,CAAC,GAAG,CADM;AAEVC,IAAAA,CAAC,GAAG,CAFM;AAGVC,IAAAA,CAAC,GAAG,CAHM;AAIVC,IAAAA,IAAI,GAAG,CAJG;AAKVC,IAAAA,KAAK,GAAG,CALE;AAMVC,IAAAA,GAAG,GAAG,CANI;AAOVC,IAAAA,QAPU;AAQVC,IAAAA;AARU,MASK,EATN,EASU;AAAA;;AAAA;;AACnB,QAAIC,KAAK,CAACC,OAAN,CAAcH,QAAd,KAA2BA,QAAQ,CAACI,MAAT,KAAoB,CAAnD,EAAsD;AACpD,WAAKJ,QAAL,GAAgB,IAAIV,OAAJ,CAAYU,QAAZ,CAAhB;AACD,KAFD,MAEO;AACL,WAAKA,QAAL,GAAgB,IAAIV,OAAJ,CAAYI,CAAZ,EAAeC,CAAf,EAAkBC,CAAlB,CAAhB;AACD;;AACD,QAAIM,KAAK,CAACC,OAAN,CAAcF,WAAd,KAA8BA,WAAW,CAACG,MAAZ,KAAuB,CAAzD,EAA4D;AAE1D,WAAKH,WAAL,GAAmB,IAAIV,KAAJ,CAAUU,WAAV,EAAuBA,WAAW,CAAC,CAAD,CAAlC,CAAnB;AACD,KAHD,MAGO;AACL,WAAKA,WAAL,GAAmB,IAAIV,KAAJ,CAAUM,IAAV,EAAgBC,KAAhB,EAAuBC,GAAvB,EAA4BR,KAAK,CAACc,YAAlC,CAAnB;AACD;AACF;;AAEI,MAADX,CAAC,GAAW;AACd,WAAO,KAAKM,QAAL,CAAcN,CAArB;AACD;;AAEI,MAADA,CAAC,CAACY,KAAD,EAAgB;AACnB,SAAKN,QAAL,CAAcN,CAAd,GAAkBY,KAAlB;AACD;;AAEI,MAADX,CAAC,GAAW;AACd,WAAO,KAAKK,QAAL,CAAcL,CAArB;AACD;;AAEI,MAADA,CAAC,CAACW,KAAD,EAAgB;AACnB,SAAKN,QAAL,CAAcL,CAAd,GAAkBW,KAAlB;AACD;;AAEI,MAADV,CAAC,GAAW;AACd,WAAO,KAAKI,QAAL,CAAcJ,CAArB;AACD;;AAEI,MAADA,CAAC,CAACU,KAAD,EAAgB;AACnB,SAAKN,QAAL,CAAcJ,CAAd,GAAkBU,KAAlB;AACD;;AAEO,MAAJT,IAAI,GAAW;AACjB,WAAO,KAAKI,WAAL,CAAiBJ,IAAxB;AACD;;AAEO,MAAJA,IAAI,CAACS,KAAD,EAAgB;AACtB,SAAKL,WAAL,CAAiBJ,IAAjB,GAAwBS,KAAxB;AACD;;AAEQ,MAALR,KAAK,GAAW;AAClB,WAAO,KAAKG,WAAL,CAAiBH,KAAxB;AACD;;AACQ,MAALA,KAAK,CAACQ,KAAD,EAAgB;AACvB,SAAKL,WAAL,CAAiBH,KAAjB,GAAyBQ,KAAzB;AACD;;AAEM,MAAHP,GAAG,GAAW;AAChB,WAAO,KAAKE,WAAL,CAAiBF,GAAxB;AACD;;AAEM,MAAHA,GAAG,CAACO,KAAD,EAAgB;AACrB,SAAKL,WAAL,CAAiBF,GAAjB,GAAuBO,KAAvB;AACD;;AAEDC,EAAAA,WAAW,GAAY;AACrB,WAAO,KAAKP,QAAZ;AACD;;AAEDQ,EAAAA,cAAc,GAAU;AACtB,WAAO,KAAKP,WAAZ;AACD;;AAEDQ,EAAAA,MAAM,CAACC,IAAD,EAAsB;AAC1B,QAAI,CAACA,IAAL,EAAW;AACT,aAAO,KAAP;AACD;;AACD,WAAO,KAAKV,QAAL,CAAcS,MAAd,CAAqBC,IAAI,CAACV,QAA1B,KAAuC,KAAKC,WAAL,CAAiBQ,MAAjB,CAAwBC,IAAI,CAACT,WAA7B,CAA9C;AACD;;AAEDU,EAAAA,WAAW,CAACD,IAAD,EAAsB;AAC/B,QAAI,CAACA,IAAL,EAAW;AACT,aAAO,KAAP;AACD;;AACD,WACE,KAAKV,QAAL,CAAcW,WAAd,CAA0BD,IAAI,CAACV,QAA/B,KAA4C,KAAKC,WAAL,CAAiBU,WAAjB,CAA6BD,IAAI,CAACT,WAAlC,CAD9C;AAGD;;AAEDW,EAAAA,uBAAuB,GAAY;AAEjC,UAAMC,EAAE,GAAGC,IAAI,CAACC,GAAL,CAAS,KAAKlB,IAAd,CAAX;AACA,UAAMmB,EAAE,GAAGF,IAAI,CAACC,GAAL,CAAS,KAAKjB,KAAd,CAAX;AACA,UAAMmB,EAAE,GAAGH,IAAI,CAACC,GAAL,CAAS,KAAKhB,GAAd,CAAX;AACA,UAAMmB,EAAE,GAAGJ,IAAI,CAACK,GAAL,CAAS,KAAKtB,IAAd,CAAX;AACA,UAAMuB,EAAE,GAAGN,IAAI,CAACK,GAAL,CAAS,KAAKrB,KAAd,CAAX;AACA,UAAMuB,EAAE,GAAGP,IAAI,CAACK,GAAL,CAAS,KAAKpB,GAAd,CAAX;AAGA,WAAO,IAAIV,OAAJ,GAAciC,WAAd,CACLD,EAAE,GAAGD,EADA,EAEL,CAACH,EAAD,GAAMC,EAAN,GAAWG,EAAE,GAAGL,EAAL,GAAUH,EAFhB,EAGLI,EAAE,GAAGJ,EAAL,GAAUQ,EAAE,GAAGL,EAAL,GAAUE,EAHf,EAIL,KAAKxB,CAJA,EAKLuB,EAAE,GAAGG,EALA,EAMLC,EAAE,GAAGH,EAAL,GAAUD,EAAE,GAAGD,EAAL,GAAUH,EANf,EAOL,CAACQ,EAAD,GAAMR,EAAN,GAAWI,EAAE,GAAGD,EAAL,GAAUE,EAPhB,EAQL,KAAKvB,CARA,EASL,CAACqB,EATI,EAULI,EAAE,GAAGP,EAVA,EAWLO,EAAE,GAAGF,EAXA,EAYL,KAAKtB,CAZA,EAaL,CAbK,EAcL,CAdK,EAeL,CAfK,EAgBL,CAhBK,CAAP;AAkBD;;AAED2B,EAAAA,+BAA+B,CAACb,IAAD,EAAsB;AACnD,WAAO,IAAIrB,OAAJ,GACJmC,aADI,CACU,KAAKZ,uBAAL,EADV,EAEJY,aAFI,CAEUd,IAAI,CAACE,uBAAL,GAA+Ba,MAA/B,EAFV,CAAP;AAGD;;AAEDC,EAAAA,6BAA6B,CAAChB,IAAD,EAAsB;AACjD,WAAO,IAAIrB,OAAJ,GACJmC,aADI,CACUd,IAAI,CAACE,uBAAL,EADV,EAEJY,aAFI,CAEU,KAAKZ,uBAAL,GAA+Ba,MAA/B,EAFV,CAAP;AAGD;;AA1IuB", "sourcesContent": ["// Copyright (c) 2017 Uber Technologies, Inc.\n// MIT License\nimport Matrix4 from './matrix4';\nimport Vector3 from './vector3';\nimport Euler from './euler';\nimport {NumericArray} from '@math.gl/types';\n\ntype PoseOptions = {\n  position?: Readonly<NumericArray>;\n  orientation?: Readonly<NumericArray>;\n  x?: number;\n  y?: number;\n  z?: number;\n  roll?: number;\n  pitch?: number;\n  yaw?: number;\n};\n\nexport default class Pose {\n  readonly position: Vector3;\n  readonly orientation: Euler;\n\n  constructor({\n    x = 0,\n    y = 0,\n    z = 0,\n    roll = 0,\n    pitch = 0,\n    yaw = 0,\n    position,\n    orientation\n  }: PoseOptions = {}) {\n    if (Array.isArray(position) && position.length === 3) {\n      this.position = new Vector3(position);\n    } else {\n      this.position = new Vector3(x, y, z);\n    }\n    if (Array.isArray(orientation) && orientation.length === 4) {\n      // @ts-expect-error\n      this.orientation = new Euler(orientation, orientation[3]);\n    } else {\n      this.orientation = new Euler(roll, pitch, yaw, Euler.RollPitchYaw);\n    }\n  }\n\n  get x(): number {\n    return this.position.x;\n  }\n\n  set x(value: number) {\n    this.position.x = value;\n  }\n\n  get y(): number {\n    return this.position.y;\n  }\n\n  set y(value: number) {\n    this.position.y = value;\n  }\n\n  get z(): number {\n    return this.position.z;\n  }\n\n  set z(value: number) {\n    this.position.z = value;\n  }\n\n  get roll(): number {\n    return this.orientation.roll;\n  }\n\n  set roll(value: number) {\n    this.orientation.roll = value;\n  }\n\n  get pitch(): number {\n    return this.orientation.pitch;\n  }\n  set pitch(value: number) {\n    this.orientation.pitch = value;\n  }\n\n  get yaw(): number {\n    return this.orientation.yaw;\n  }\n\n  set yaw(value: number) {\n    this.orientation.yaw = value;\n  }\n\n  getPosition(): Vector3 {\n    return this.position;\n  }\n\n  getOrientation(): Euler {\n    return this.orientation;\n  }\n\n  equals(pose: Pose): boolean {\n    if (!pose) {\n      return false;\n    }\n    return this.position.equals(pose.position) && this.orientation.equals(pose.orientation);\n  }\n\n  exactEquals(pose: Pose): boolean {\n    if (!pose) {\n      return false;\n    }\n    return (\n      this.position.exactEquals(pose.position) && this.orientation.exactEquals(pose.orientation)\n    );\n  }\n\n  getTransformationMatrix(): Matrix4 {\n    // setup pre computations for the sin/cos of the angles\n    const sr = Math.sin(this.roll);\n    const sp = Math.sin(this.pitch);\n    const sw = Math.sin(this.yaw);\n    const cr = Math.cos(this.roll);\n    const cp = Math.cos(this.pitch);\n    const cw = Math.cos(this.yaw);\n\n    // Create matrix\n    return new Matrix4().setRowMajor(\n      cw * cp, // 0,0\n      -sw * cr + cw * sp * sr, // 0,1\n      sw * sr + cw * sp * cr, // 0,2\n      this.x, // 0,3\n      sw * cp, // 1,0\n      cw * cr + sw * sp * sr, // 1,1\n      -cw * sr + sw * sp * cr, // 1,2\n      this.y, // 1,3\n      -sp, // 2,0\n      cp * sr, // 2,1\n      cp * cr, // 2,2\n      this.z, // 2,3\n      0,\n      0,\n      0,\n      1\n    );\n  }\n\n  getTransformationMatrixFromPose(pose: Pose): Matrix4 {\n    return new Matrix4()\n      .multiplyRight(this.getTransformationMatrix())\n      .multiplyRight(pose.getTransformationMatrix().invert());\n  }\n\n  getTransformationMatrixToPose(pose: Pose): Matrix4 {\n    return new Matrix4()\n      .multiplyRight(pose.getTransformationMatrix())\n      .multiplyRight(this.getTransformationMatrix().invert());\n  }\n}\n"], "file": "pose.js"}