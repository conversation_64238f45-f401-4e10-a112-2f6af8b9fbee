{"version": 3, "sources": ["../../../src/classes/matrix3.ts"], "names": ["Matrix", "checkVector", "vec4_transformMat3", "mat3", "vec2", "vec3", "INDICES", "IDENTITY_MATRIX", "Object", "freeze", "Matrix3", "IDENTITY", "getIdentityMatrix", "ZERO", "getZeroMatrix", "ELEMENTS", "RANK", "constructor", "array", "args", "arguments", "length", "Array", "isArray", "copy", "identity", "check", "fromObject", "object", "fromQuaternion", "q", "fromQuat", "set", "m00", "m10", "m20", "m01", "m11", "m21", "m02", "m12", "m22", "setRowMajor", "determinant", "transpose", "invert", "multiplyLeft", "a", "multiply", "multiplyRight", "rotate", "radians", "scale", "factor", "translate", "vec", "transform", "vector", "result", "out", "transformMat3", "Error", "transformVector", "transformVector2", "transformVector3", "ZERO_MATRIX3", "IDENTITY_MATRIX3"], "mappings": "AAEA,OAAOA,MAAP,MAAmB,eAAnB;AACA,SAAQC,WAAR,QAA0B,mBAA1B;AAEA,SAAQC,kBAAR,QAAiC,yBAAjC;AACA,OAAO,KAAKC,IAAZ,MAAsB,gBAAtB;AACA,OAAO,KAAKC,IAAZ,MAAsB,gBAAtB;AACA,OAAO,KAAKC,IAAZ,MAAsB,gBAAtB;IAGKC,O;;WAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;GAAAA,O,KAAAA,O;;AAYL,MAAMC,eAAe,GAAGC,MAAM,CAACC,MAAP,CAAc,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,CAAd,CAAxB;AAEA,eAAe,MAAMC,OAAN,SAAsBV,MAAtB,CAA6B;AACvB,aAARW,QAAQ,GAAsB;AACvC,WAAOC,iBAAiB,EAAxB;AACD;;AAEc,aAAJC,IAAI,GAAsB;AACnC,WAAOC,aAAa,EAApB;AACD;;AAEW,MAARC,QAAQ,GAAW;AACrB,WAAO,CAAP;AACD;;AAEO,MAAJC,IAAI,GAAW;AACjB,WAAO,CAAP;AACD;;AAEU,MAAPV,OAAO,GAAmB;AAC5B,WAAOA,OAAP;AACD;;AAMDW,EAAAA,WAAW,CAACC,KAAD,EAA0C,GAAGC,IAA7C,EAA6D;AAEtE,UAAM,CAAC,CAAP,EAAU,CAAC,CAAX,EAAc,CAAC,CAAf,EAAkB,CAAC,CAAnB,EAAsB,CAAC,CAAvB,EAA0B,CAAC,CAA3B,EAA8B,CAAC,CAA/B,EAAkC,CAAC,CAAnC,EAAsC,CAAC,CAAvC;;AACA,QAAIC,SAAS,CAACC,MAAV,KAAqB,CAArB,IAA0BC,KAAK,CAACC,OAAN,CAAcL,KAAd,CAA9B,EAAoD;AAClD,WAAKM,IAAL,CAAUN,KAAV;AACD,KAFD,MAEO,IAAIC,IAAI,CAACE,MAAL,GAAc,CAAlB,EAAqB;AAC1B,WAAKG,IAAL,CAAU,CAACN,KAAD,EAAkB,GAAGC,IAArB,CAAV;AACD,KAFM,MAEA;AACL,WAAKM,QAAL;AACD;AACF;;AAEDD,EAAAA,IAAI,CAACN,KAAD,EAAsC;AAExC,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAO,KAAKQ,KAAL,EAAP;AACD;;AAIDD,EAAAA,QAAQ,GAAS;AACf,WAAO,KAAKD,IAAL,CAAUjB,eAAV,CAAP;AACD;;AAODoB,EAAAA,UAAU,CAACC,MAAD,EAAqC;AAC7C,WAAO,KAAKF,KAAL,EAAP;AACD;;AAIDG,EAAAA,cAAc,CAACC,CAAD,EAAkC;AAC9C3B,IAAAA,IAAI,CAAC4B,QAAL,CAAc,IAAd,EAAoBD,CAApB;AACA,WAAO,KAAKJ,KAAL,EAAP;AACD;;AAMDM,EAAAA,GAAG,CACDC,GADC,EAEDC,GAFC,EAGDC,GAHC,EAIDC,GAJC,EAKDC,GALC,EAMDC,GANC,EAODC,GAPC,EAQDC,GARC,EASDC,GATC,EAUK;AACN,SAAK,CAAL,IAAUR,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,WAAO,KAAKf,KAAL,EAAP;AACD;;AAMDgB,EAAAA,WAAW,CACTT,GADS,EAETG,GAFS,EAGTG,GAHS,EAITL,GAJS,EAKTG,GALS,EAMTG,GANS,EAOTL,GAPS,EAQTG,GARS,EASTG,GATS,EAUH;AACN,SAAK,CAAL,IAAUR,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,WAAO,KAAKf,KAAL,EAAP;AACD;;AAIDiB,EAAAA,WAAW,GAAW;AACpB,WAAOxC,IAAI,CAACwC,WAAL,CAAiB,IAAjB,CAAP;AACD;;AAGDC,EAAAA,SAAS,GAAS;AAChBzC,IAAAA,IAAI,CAACyC,SAAL,CAAe,IAAf,EAAqB,IAArB;AACA,WAAO,KAAKlB,KAAL,EAAP;AACD;;AAGDmB,EAAAA,MAAM,GAAS;AACb1C,IAAAA,IAAI,CAAC0C,MAAL,CAAY,IAAZ,EAAkB,IAAlB;AACA,WAAO,KAAKnB,KAAL,EAAP;AACD;;AAGDoB,EAAAA,YAAY,CAACC,CAAD,EAAwB;AAClC5C,IAAAA,IAAI,CAAC6C,QAAL,CAAc,IAAd,EAAoBD,CAApB,EAAuB,IAAvB;AACA,WAAO,KAAKrB,KAAL,EAAP;AACD;;AAEDuB,EAAAA,aAAa,CAACF,CAAD,EAAwB;AACnC5C,IAAAA,IAAI,CAAC6C,QAAL,CAAc,IAAd,EAAoB,IAApB,EAA0BD,CAA1B;AACA,WAAO,KAAKrB,KAAL,EAAP;AACD;;AAEDwB,EAAAA,MAAM,CAACC,OAAD,EAAgC;AACpChD,IAAAA,IAAI,CAAC+C,MAAL,CAAY,IAAZ,EAAkB,IAAlB,EAAwBC,OAAxB;AACA,WAAO,KAAKzB,KAAL,EAAP;AACD;;AAED0B,EAAAA,KAAK,CAACC,MAAD,EAAsC;AACzC,QAAI/B,KAAK,CAACC,OAAN,CAAc8B,MAAd,CAAJ,EAA2B;AACzBlD,MAAAA,IAAI,CAACiD,KAAL,CAAW,IAAX,EAAiB,IAAjB,EAAuBC,MAAvB;AACD,KAFD,MAEO;AACLlD,MAAAA,IAAI,CAACiD,KAAL,CAAW,IAAX,EAAiB,IAAjB,EAAuB,CAACC,MAAD,EAAmBA,MAAnB,CAAvB;AACD;;AACD,WAAO,KAAK3B,KAAL,EAAP;AACD;;AAED4B,EAAAA,SAAS,CAACC,GAAD,EAA0B;AACjCpD,IAAAA,IAAI,CAACmD,SAAL,CAAe,IAAf,EAAqB,IAArB,EAA2BC,GAA3B;AACA,WAAO,KAAK7B,KAAL,EAAP;AACD;;AAGD8B,EAAAA,SAAS,CAACC,MAAD,EAAiCC,MAAjC,EAAsE;AAC7E,QAAIC,GAAJ;;AACA,YAAQF,MAAM,CAACpC,MAAf;AACE,WAAK,CAAL;AACEsC,QAAAA,GAAG,GAAGvD,IAAI,CAACwD,aAAL,CAAmBF,MAAM,IAAI,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,CAA7B,EAAuCD,MAAvC,EAA+C,IAA/C,CAAN;AACA;;AACF,WAAK,CAAL;AACEE,QAAAA,GAAG,GAAGtD,IAAI,CAACuD,aAAL,CAAmBF,MAAM,IAAI,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,EAAS,CAAC,CAAV,CAA7B,EAA2CD,MAA3C,EAAmD,IAAnD,CAAN;AACA;;AACF,WAAK,CAAL;AACEE,QAAAA,GAAG,GAAGzD,kBAAkB,CAACwD,MAAM,IAAI,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,EAAS,CAAC,CAAV,EAAa,CAAC,CAAd,CAAX,EAA6BD,MAA7B,EAAqC,IAArC,CAAxB;AACA;;AACF;AACE,cAAM,IAAII,KAAJ,CAAU,gBAAV,CAAN;AAXJ;;AAaA5D,IAAAA,WAAW,CAAC0D,GAAD,EAAMF,MAAM,CAACpC,MAAb,CAAX;AACA,WAAOsC,GAAP;AACD;;AAGDG,EAAAA,eAAe,CAACL,MAAD,EAAiCC,MAAjC,EAAsE;AACnF,WAAO,KAAKF,SAAL,CAAeC,MAAf,EAAuBC,MAAvB,CAAP;AACD;;AAGDK,EAAAA,gBAAgB,CAACN,MAAD,EAAiCC,MAAjC,EAAsE;AACpF,WAAO,KAAKF,SAAL,CAAeC,MAAf,EAAuBC,MAAvB,CAAP;AACD;;AAGDM,EAAAA,gBAAgB,CAACP,MAAD,EAAiCC,MAAjC,EAAsE;AACpF,WAAO,KAAKF,SAAL,CAAeC,MAAf,EAAuBC,MAAvB,CAAP;AACD;;AAhNyC;AAmN5C,IAAIO,YAAJ;AACA,IAAIC,gBAAJ;;AAEA,SAASpD,aAAT,GAA4C;AAC1C,MAAI,CAACmD,YAAL,EAAmB;AACjBA,IAAAA,YAAY,GAAG,IAAIvD,OAAJ,CAAY,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,CAAZ,CAAf;AACAF,IAAAA,MAAM,CAACC,MAAP,CAAcwD,YAAd;AACD;;AACD,SAAOA,YAAP;AACD;;AAED,SAASrD,iBAAT,GAAsC;AACpC,MAAI,CAACsD,gBAAL,EAAuB;AACrBA,IAAAA,gBAAgB,GAAG,IAAIxD,OAAJ,EAAnB;AACAF,IAAAA,MAAM,CAACC,MAAP,CAAcyD,gBAAd;AACD;;AACD,SAAOA,gBAAP;AACD", "sourcesContent": ["// Copyright (c) 2017 Uber Technologies, Inc.\n// MIT License\nimport Matrix from './base/matrix';\nimport {checkVector} from '../lib/validators';\n/* eslint-disable camelcase */\nimport {vec4_transformMat3} from '../lib/gl-matrix-extras';\nimport * as mat3 from 'gl-matrix/mat3';\nimport * as vec2 from 'gl-matrix/vec2';\nimport * as vec3 from 'gl-matrix/vec3';\nimport {NumericArray} from '@math.gl/types';\n\nenum INDICES {\n  COL0ROW0 = 0,\n  COL0ROW1 = 1,\n  COL0ROW2 = 2,\n  COL1ROW0 = 3,\n  COL1ROW1 = 4,\n  COL1ROW2 = 5,\n  COL2ROW0 = 6,\n  COL2ROW1 = 7,\n  COL2ROW2 = 8\n}\n\nconst IDENTITY_MATRIX = Object.freeze([1, 0, 0, 0, 1, 0, 0, 0, 1]);\n\nexport default class Matrix3 extends Matrix {\n  static get IDENTITY(): Readonly<Matrix3> {\n    return getIdentityMatrix();\n  }\n\n  static get ZERO(): Readonly<Matrix3> {\n    return getZeroMatrix();\n  }\n\n  get ELEMENTS(): number {\n    return 9;\n  }\n\n  get RANK(): number {\n    return 3;\n  }\n\n  get INDICES(): typeof INDICES {\n    return INDICES;\n  }\n\n  constructor(array?: Readonly<NumericArray>);\n  /** @deprecated */\n  constructor(...args: number[]);\n\n  constructor(array?: number | Readonly<NumericArray>, ...args: number[]) {\n    // PERF NOTE: initialize elements as double precision numbers\n    super(-0, -0, -0, -0, -0, -0, -0, -0, -0);\n    if (arguments.length === 1 && Array.isArray(array)) {\n      this.copy(array);\n    } else if (args.length > 0) {\n      this.copy([array as number, ...args]);\n    } else {\n      this.identity();\n    }\n  }\n\n  copy(array: Readonly<NumericArray>): this {\n    // Element wise copy for performance\n    this[0] = array[0];\n    this[1] = array[1];\n    this[2] = array[2];\n    this[3] = array[3];\n    this[4] = array[4];\n    this[5] = array[5];\n    this[6] = array[6];\n    this[7] = array[7];\n    this[8] = array[8];\n    return this.check();\n  }\n\n  // Constructors\n\n  identity(): this {\n    return this.copy(IDENTITY_MATRIX);\n  }\n\n  /**\n   *\n   * @param object\n   * @returns self\n   */\n  fromObject(object: {[key: string]: any}): this {\n    return this.check();\n  }\n\n  // Calculates a 3x3 matrix from the given quaternion\n  // q quat  Quaternion to create matrix from\n  fromQuaternion(q: Readonly<NumericArray>): this {\n    mat3.fromQuat(this, q);\n    return this.check();\n  }\n\n  /**\n   * accepts column major order, stores in column major order\n   */\n  // eslint-disable-next-line max-params\n  set(\n    m00: number,\n    m10: number,\n    m20: number,\n    m01: number,\n    m11: number,\n    m21: number,\n    m02: number,\n    m12: number,\n    m22: number\n  ): this {\n    this[0] = m00;\n    this[1] = m10;\n    this[2] = m20;\n    this[3] = m01;\n    this[4] = m11;\n    this[5] = m21;\n    this[6] = m02;\n    this[7] = m12;\n    this[8] = m22;\n    return this.check();\n  }\n\n  /**\n   * accepts row major order, stores as column major\n   */\n  // eslint-disable-next-line max-params\n  setRowMajor(\n    m00: number,\n    m01: number,\n    m02: number,\n    m10: number,\n    m11: number,\n    m12: number,\n    m20: number,\n    m21: number,\n    m22: number\n  ): this {\n    this[0] = m00;\n    this[1] = m10;\n    this[2] = m20;\n    this[3] = m01;\n    this[4] = m11;\n    this[5] = m21;\n    this[6] = m02;\n    this[7] = m12;\n    this[8] = m22;\n    return this.check();\n  }\n\n  // Accessors\n\n  determinant(): number {\n    return mat3.determinant(this);\n  }\n\n  // Modifiers\n  transpose(): this {\n    mat3.transpose(this, this);\n    return this.check();\n  }\n\n  /** Invert a matrix. Note that this can fail if the matrix is not invertible */\n  invert(): this {\n    mat3.invert(this, this);\n    return this.check();\n  }\n\n  // Operations\n  multiplyLeft(a: NumericArray): this {\n    mat3.multiply(this, a, this);\n    return this.check();\n  }\n\n  multiplyRight(a: NumericArray): this {\n    mat3.multiply(this, this, a);\n    return this.check();\n  }\n\n  rotate(radians: number): NumericArray {\n    mat3.rotate(this, this, radians);\n    return this.check();\n  }\n\n  scale(factor: NumericArray | number): this {\n    if (Array.isArray(factor)) {\n      mat3.scale(this, this, factor);\n    } else {\n      mat3.scale(this, this, [factor as number, factor as number]);\n    }\n    return this.check();\n  }\n\n  translate(vec: NumericArray): this {\n    mat3.translate(this, this, vec);\n    return this.check();\n  }\n\n  // Transforms\n  transform(vector: Readonly<NumericArray>, result?: NumericArray): NumericArray {\n    let out: NumericArray;\n    switch (vector.length) {\n      case 2:\n        out = vec2.transformMat3(result || [-0, -0], vector, this);\n        break;\n      case 3:\n        out = vec3.transformMat3(result || [-0, -0, -0], vector, this);\n        break;\n      case 4:\n        out = vec4_transformMat3(result || [-0, -0, -0, -0], vector, this);\n        break;\n      default:\n        throw new Error('Illegal vector');\n    }\n    checkVector(out, vector.length);\n    return out;\n  }\n\n  /** @deprecated */\n  transformVector(vector: Readonly<NumericArray>, result?: NumericArray): NumericArray {\n    return this.transform(vector, result);\n  }\n\n  /** @deprecated */\n  transformVector2(vector: Readonly<NumericArray>, result?: NumericArray): NumericArray {\n    return this.transform(vector, result);\n  }\n\n  /** @deprecated */\n  transformVector3(vector: Readonly<NumericArray>, result?: NumericArray): NumericArray {\n    return this.transform(vector, result);\n  }\n}\n\nlet ZERO_MATRIX3;\nlet IDENTITY_MATRIX3;\n\nfunction getZeroMatrix(): Readonly<Matrix3> {\n  if (!ZERO_MATRIX3) {\n    ZERO_MATRIX3 = new Matrix3([0, 0, 0, 0, 0, 0, 0, 0, 0]);\n    Object.freeze(ZERO_MATRIX3);\n  }\n  return ZERO_MATRIX3;\n}\n\nfunction getIdentityMatrix(): Matrix3 {\n  if (!IDENTITY_MATRIX3) {\n    IDENTITY_MATRIX3 = new Matrix3();\n    Object.freeze(IDENTITY_MATRIX3);\n  }\n  return IDENTITY_MATRIX3;\n}\n"], "file": "matrix3.js"}