{"version": 3, "sources": ["../../../src/classes/spherical-coordinates.ts"], "names": ["Vector3", "formatValue", "equals", "config", "degrees", "radians", "clamp", "vec3", "EPSILON", "EARTH_RADIUS_METERS", "SphericalCoordinates", "constructor", "phi", "theta", "radius", "bearing", "pitch", "altitude", "radiusScale", "undefined", "check", "toString", "formatString", "printTypes", "f", "other", "exactEquals", "v", "Math", "PI", "longitude", "latitude", "lng", "lat", "z", "set", "clone", "copy", "fromLngLatZ", "fromVector3", "length", "atan2", "acos", "toVector3", "rotateX", "rotateZ", "makeSafe", "max", "min", "Number", "isFinite", "Error"], "mappings": ";AAGA,OAAOA,OAAP,MAAoB,WAApB;AACA,SAAQC,WAAR,EAAqBC,MAArB,EAA6BC,MAA7B,QAA0C,eAA1C;AACA,SAAQC,OAAR,EAAiBC,OAAjB,EAA0BC,KAA1B,QAAsC,eAAtC;AACA,OAAO,KAAKC,IAAZ,MAAsB,gBAAtB;AAkBA,MAAMC,OAAO,GAAG,QAAhB;AACA,MAAMC,mBAAmB,GAAG,OAA5B;AAOA,eAAe,MAAMC,oBAAN,CAA2B;AA4BxCC,EAAAA,WAAW,CAAC;AACVC,IAAAA,GAAG,GAAG,CADI;AAEVC,IAAAA,KAAK,GAAG,CAFE;AAGVC,IAAAA,MAAM,GAAG,CAHC;AAIVC,IAAAA,OAJU;AAKVC,IAAAA,KALU;AAMVC,IAAAA,QANU;AAOVC,IAAAA,WAAW,GAAGT;AAPJ,MAQqB,EARtB,EAQ0B;AAAA;;AAAA;;AAAA;;AAAA;;AACnC,SAAKG,GAAL,GAAWA,GAAX;AACA,SAAKC,KAAL,GAAaA,KAAb;AAEA,SAAKC,MAAL,GAAcA,MAAM,IAAIG,QAAV,IAAsB,CAApC;AACA,SAAKC,WAAL,GAAmBA,WAAW,IAAI,CAAlC;;AACA,QAAIH,OAAO,KAAKI,SAAhB,EAA2B;AACzB,WAAKJ,OAAL,GAAeA,OAAf;AACD;;AACD,QAAIC,KAAK,KAAKG,SAAd,EAAyB;AACvB,WAAKH,KAAL,GAAaA,KAAb;AACD;;AACD,SAAKI,KAAL;AACD;;AAEDC,EAAAA,QAAQ,GAAW;AACjB,WAAO,KAAKC,YAAL,CAAkBnB,MAAlB,CAAP;AACD;;AAEDmB,EAAAA,YAAY,CAAC;AAACC,IAAAA,UAAU,GAAG;AAAd,GAAD,EAA8C;AACxD,UAAMC,CAAC,GAAGvB,WAAV;AACA,qBAAUsB,UAAU,GAAG,WAAH,GAAiB,EAArC,kBACGC,CAAC,CAAC,KAAKV,MAAN,CADJ,oBAC2BU,CAAC,CAAC,KAAKX,KAAN,CAD5B,kBACgDW,CAAC,CAAC,KAAKZ,GAAN,CADjD;AAED;;AAEDV,EAAAA,MAAM,CAACuB,KAAD,EAAuC;AAC3C,WACEvB,MAAM,CAAC,KAAKY,MAAN,EAAcW,KAAK,CAACX,MAApB,CAAN,IACAZ,MAAM,CAAC,KAAKW,KAAN,EAAaY,KAAK,CAACZ,KAAnB,CADN,IAEAX,MAAM,CAAC,KAAKU,GAAN,EAAWa,KAAK,CAACb,GAAjB,CAHR;AAKD;;AAEDc,EAAAA,WAAW,CAACD,KAAD,EAAuC;AAChD,WAAO,KAAKX,MAAL,KAAgBW,KAAK,CAACX,MAAtB,IAAgC,KAAKD,KAAL,KAAeY,KAAK,CAACZ,KAArD,IAA8D,KAAKD,GAAL,KAAaa,KAAK,CAACb,GAAxF;AACD;;AAIU,MAAPG,OAAO,GAAW;AACpB,WAAO,MAAMX,OAAO,CAAC,KAAKQ,GAAN,CAApB;AACD;;AAEU,MAAPG,OAAO,CAACY,CAAD,EAAY;AACrB,SAAKf,GAAL,GAAWgB,IAAI,CAACC,EAAL,GAAUxB,OAAO,CAACsB,CAAD,CAA5B;AACD;;AAEQ,MAALX,KAAK,GAAW;AAClB,WAAOZ,OAAO,CAAC,KAAKS,KAAN,CAAd;AACD;;AAEQ,MAALG,KAAK,CAACW,CAAD,EAAY;AACnB,SAAKd,KAAL,GAAaR,OAAO,CAACsB,CAAD,CAApB;AACD;;AAMY,MAATG,SAAS,GAAW;AACtB,WAAO1B,OAAO,CAAC,KAAKQ,GAAN,CAAd;AACD;;AAEW,MAARmB,QAAQ,GAAW;AACrB,WAAO3B,OAAO,CAAC,KAAKS,KAAN,CAAd;AACD;;AAEM,MAAHmB,GAAG,GAAW;AAChB,WAAO5B,OAAO,CAAC,KAAKQ,GAAN,CAAd;AACD;;AAEM,MAAHqB,GAAG,GAAW;AAChB,WAAO7B,OAAO,CAAC,KAAKS,KAAN,CAAd;AACD;;AAEI,MAADqB,CAAC,GAAW;AACd,WAAO,CAAC,KAAKpB,MAAL,GAAc,CAAf,IAAoB,KAAKI,WAAhC;AACD;;AAGDiB,EAAAA,GAAG,CAACrB,MAAD,EAAiBF,GAAjB,EAA8BC,KAA9B,EAAmD;AACpD,SAAKC,MAAL,GAAcA,MAAd;AACA,SAAKF,GAAL,GAAWA,GAAX;AACA,SAAKC,KAAL,GAAaA,KAAb;AACA,WAAO,KAAKO,KAAL,EAAP;AACD;;AAEDgB,EAAAA,KAAK,GAAyB;AAC5B,WAAO,IAAI1B,oBAAJ,GAA2B2B,IAA3B,CAAgC,IAAhC,CAAP;AACD;;AAEDA,EAAAA,IAAI,CAACZ,KAAD,EAAoC;AACtC,SAAKX,MAAL,GAAcW,KAAK,CAACX,MAApB;AACA,SAAKF,GAAL,GAAWa,KAAK,CAACb,GAAjB;AACA,SAAKC,KAAL,GAAaY,KAAK,CAACZ,KAAnB;AACA,WAAO,KAAKO,KAAL,EAAP;AACD;;AAEDkB,EAAAA,WAAW,CAAC,CAACN,GAAD,EAAMC,GAAN,EAAWC,CAAX,CAAD,EAAgD;AACzD,SAAKpB,MAAL,GAAc,IAAIoB,CAAC,GAAG,KAAKhB,WAA3B;AACA,SAAKN,GAAL,GAAWP,OAAO,CAAC4B,GAAD,CAAlB;AACA,SAAKpB,KAAL,GAAaR,OAAO,CAAC2B,GAAD,CAApB;AACA,WAAO,KAAKZ,KAAL,EAAP;AACD;;AAEDmB,EAAAA,WAAW,CAACZ,CAAD,EAAkC;AAC3C,SAAKb,MAAL,GAAcP,IAAI,CAACiC,MAAL,CAAYb,CAAZ,CAAd;;AACA,QAAI,KAAKb,MAAL,GAAc,CAAlB,EAAqB;AACnB,WAAKD,KAAL,GAAae,IAAI,CAACa,KAAL,CAAWd,CAAC,CAAC,CAAD,CAAZ,EAAiBA,CAAC,CAAC,CAAD,CAAlB,CAAb;AACA,WAAKf,GAAL,GAAWgB,IAAI,CAACc,IAAL,CAAUpC,KAAK,CAACqB,CAAC,CAAC,CAAD,CAAD,GAAO,KAAKb,MAAb,EAAqB,CAAC,CAAtB,EAAyB,CAAzB,CAAf,CAAX;AACD;;AACD,WAAO,KAAKM,KAAL,EAAP;AACD;;AAEDuB,EAAAA,SAAS,GAAY;AACnB,WAAO,IAAI3C,OAAJ,CAAY,CAAZ,EAAe,CAAf,EAAkB,KAAKc,MAAvB,EACJ8B,OADI,CACI;AAACvC,MAAAA,OAAO,EAAE,KAAKQ;AAAf,KADJ,EAEJgC,OAFI,CAEI;AAACxC,MAAAA,OAAO,EAAE,KAAKO;AAAf,KAFJ,CAAP;AAGD;;AAGDkC,EAAAA,QAAQ,GAAS;AACf,SAAKlC,GAAL,GAAWgB,IAAI,CAACmB,GAAL,CAASvC,OAAT,EAAkBoB,IAAI,CAACoB,GAAL,CAASpB,IAAI,CAACC,EAAL,GAAUrB,OAAnB,EAA4B,KAAKI,GAAjC,CAAlB,CAAX;AACA,WAAO,IAAP;AACD;;AAEDQ,EAAAA,KAAK,GAAS;AAEZ,QAAI,CAAC6B,MAAM,CAACC,QAAP,CAAgB,KAAKtC,GAArB,CAAD,IAA8B,CAACqC,MAAM,CAACC,QAAP,CAAgB,KAAKrC,KAArB,CAA/B,IAA8D,EAAE,KAAKC,MAAL,GAAc,CAAhB,CAAlE,EAAsF;AACpF,YAAM,IAAIqC,KAAJ,CAAU,0DAAV,CAAN;AACD;;AACD,WAAO,IAAP;AACD;;AAxKuC", "sourcesContent": ["// Copyright (c) 2017 Uber Technologies, Inc.\n// MIT License\n// Adaptation of THREE.js Spherical class, under MIT license\nimport Vector3 from './vector3';\nimport {formatValue, equals, config} from '../lib/common';\nimport {degrees, radians, clamp} from '../lib/common';\nimport * as vec3 from 'gl-matrix/vec3';\nimport {NumericArray} from '@math.gl/types';\n\ntype SphericalCoordinatesOptions = {\n  phi?: number;\n  theta?: number;\n  radius?: number;\n  bearing?: number;\n  pitch?: number;\n  altitude?: number;\n  radiusScale?: number;\n};\n\ntype FormatOptions = {\n  printTypes?: boolean;\n};\n\n// TODO - import epsilon\nconst EPSILON = 0.000001;\nconst EARTH_RADIUS_METERS = 6371000;\n\n/**\n * The poles (phi) are at the positive and negative y axis.\n * The equator starts at positive z.\n * @link https://en.wikipedia.org/wiki/Spherical_coordinate_system\n */\nexport default class SphericalCoordinates {\n  phi: number;\n  theta: number;\n  radius: number;\n  radiusScale: number;\n  // bearing: number;\n  // pitch: number;\n  // altitude: number;\n\n  // lnglatZ coordinates\n  // longitude: number;\n  // latitude: number;\n  // lng: number;\n  // lat: number;\n  // z: number;\n\n  /**\n   * Creates a new SphericalCoordinates object\n   * @param options\n   * @param [options.phi] =0 - rotation around X (latitude)\n   * @param [options.theta] =0 - rotation around Y (longitude)\n   * @param [options.radius] =1 - Distance from center\n   * @param [options.bearing]\n   * @param [options.pitch]\n   * @param [options.altitude]\n   * @param [options.radiusScale] =1\n   */\n  // eslint-disable-next-line complexity\n  constructor({\n    phi = 0,\n    theta = 0,\n    radius = 1,\n    bearing,\n    pitch,\n    altitude,\n    radiusScale = EARTH_RADIUS_METERS\n  }: SphericalCoordinatesOptions = {}) {\n    this.phi = phi;\n    this.theta = theta;\n    // TODO - silently accepts illegal 0\n    this.radius = radius || altitude || 1; // radial distance from center\n    this.radiusScale = radiusScale || 1; // Used by lngLatZ\n    if (bearing !== undefined) {\n      this.bearing = bearing; // up / down towards top and bottom pole\n    }\n    if (pitch !== undefined) {\n      this.pitch = pitch; // around the equator of the sphere\n    }\n    this.check();\n  }\n\n  toString(): string {\n    return this.formatString(config);\n  }\n\n  formatString({printTypes = false}: FormatOptions): string {\n    const f = formatValue;\n    return `${printTypes ? 'Spherical' : ''}\\\n[rho:${f(this.radius)},theta:${f(this.theta)},phi:${f(this.phi)}]`;\n  }\n\n  equals(other: SphericalCoordinates): boolean {\n    return (\n      equals(this.radius, other.radius) &&\n      equals(this.theta, other.theta) &&\n      equals(this.phi, other.phi)\n    );\n  }\n\n  exactEquals(other: SphericalCoordinates): boolean {\n    return this.radius === other.radius && this.theta === other.theta && this.phi === other.phi;\n  }\n\n  /* eslint-disable brace-style */\n  // Cartographic (bearing 0 north, pitch 0 look from above)\n  get bearing(): number {\n    return 180 - degrees(this.phi);\n  }\n\n  set bearing(v: number) {\n    this.phi = Math.PI - radians(v);\n  }\n\n  get pitch(): number {\n    return degrees(this.theta);\n  }\n\n  set pitch(v: number) {\n    this.theta = radians(v);\n  }\n\n  // get pitch() { return 90 - degrees(this.phi); }\n  // set pitch(v) { this.phi = radians(v) + Math.PI / 2; }\n  // get altitude() { return this.radius - 1; } // relative altitude\n  // lnglatZ coordinates\n  get longitude(): number {\n    return degrees(this.phi);\n  }\n\n  get latitude(): number {\n    return degrees(this.theta);\n  }\n\n  get lng(): number {\n    return degrees(this.phi);\n  }\n\n  get lat(): number {\n    return degrees(this.theta);\n  }\n\n  get z(): number {\n    return (this.radius - 1) * this.radiusScale;\n  }\n\n  /* eslint-enable brace-style */\n  set(radius: number, phi: number, theta: number): this {\n    this.radius = radius;\n    this.phi = phi;\n    this.theta = theta;\n    return this.check();\n  }\n\n  clone(): SphericalCoordinates {\n    return new SphericalCoordinates().copy(this);\n  }\n\n  copy(other: SphericalCoordinates): this {\n    this.radius = other.radius;\n    this.phi = other.phi;\n    this.theta = other.theta;\n    return this.check();\n  }\n\n  fromLngLatZ([lng, lat, z]: [number, number, number]): this {\n    this.radius = 1 + z / this.radiusScale;\n    this.phi = radians(lat);\n    this.theta = radians(lng);\n    return this.check();\n  }\n\n  fromVector3(v: Readonly<NumericArray>): this {\n    this.radius = vec3.length(v);\n    if (this.radius > 0) {\n      this.theta = Math.atan2(v[0], v[1]); // equator angle around y-up axis\n      this.phi = Math.acos(clamp(v[2] / this.radius, -1, 1)); // polar angle\n    }\n    return this.check();\n  }\n\n  toVector3(): Vector3 {\n    return new Vector3(0, 0, this.radius)\n      .rotateX({radians: this.theta})\n      .rotateZ({radians: this.phi});\n  }\n\n  // restrict phi to be betwee EPS and PI-EPS\n  makeSafe(): this {\n    this.phi = Math.max(EPSILON, Math.min(Math.PI - EPSILON, this.phi));\n    return this;\n  }\n\n  check(): this {\n    // this.makeSafe();\n    if (!Number.isFinite(this.phi) || !Number.isFinite(this.theta) || !(this.radius > 0)) {\n      throw new Error('SphericalCoordinates: some fields set to invalid numbers');\n    }\n    return this;\n  }\n}\n"], "file": "spherical-coordinates.js"}