import Matrix from './base/matrix';
import { checkVector } from '../lib/validators';
import { vec2_transformMat4AsVector, vec3_transformMat4AsVector } from '../lib/gl-matrix-extras';
import * as mat4 from 'gl-matrix/mat4';
import * as vec2 from 'gl-matrix/vec2';
import * as vec3 from 'gl-matrix/vec3';
import * as vec4 from 'gl-matrix/vec4';
var INDICES;

(function (INDICES) {
  INDICES[INDICES["COL0ROW0"] = 0] = "COL0ROW0";
  INDICES[INDICES["COL0ROW1"] = 1] = "COL0ROW1";
  INDICES[INDICES["COL0ROW2"] = 2] = "COL0ROW2";
  INDICES[INDICES["COL0ROW3"] = 3] = "COL0ROW3";
  INDICES[INDICES["COL1ROW0"] = 4] = "COL1ROW0";
  INDICES[INDICES["COL1ROW1"] = 5] = "COL1ROW1";
  INDICES[INDICES["COL1ROW2"] = 6] = "COL1ROW2";
  INDICES[INDICES["COL1ROW3"] = 7] = "COL1ROW3";
  INDICES[INDICES["COL2ROW0"] = 8] = "COL2ROW0";
  INDICES[INDICES["COL2ROW1"] = 9] = "COL2ROW1";
  INDICES[INDICES["COL2ROW2"] = 10] = "COL2ROW2";
  INDICES[INDICES["COL2ROW3"] = 11] = "COL2ROW3";
  INDICES[INDICES["COL3ROW0"] = 12] = "COL3ROW0";
  INDICES[INDICES["COL3ROW1"] = 13] = "COL3ROW1";
  INDICES[INDICES["COL3ROW2"] = 14] = "COL3ROW2";
  INDICES[INDICES["COL3ROW3"] = 15] = "COL3ROW3";
})(INDICES || (INDICES = {}));

const DEFAULT_FOVY = 45 * Math.PI / 180;
const DEFAULT_ASPECT = 1;
const DEFAULT_NEAR = 0.1;
const DEFAULT_FAR = 500;
const IDENTITY_MATRIX = Object.freeze([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]);
export default class Matrix4 extends Matrix {
  static get IDENTITY() {
    return getIdentityMatrix();
  }

  static get ZERO() {
    return getZeroMatrix();
  }

  get ELEMENTS() {
    return 16;
  }

  get RANK() {
    return 4;
  }

  get INDICES() {
    return INDICES;
  }

  constructor(array) {
    super(-0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0);

    if (arguments.length === 1 && Array.isArray(array)) {
      this.copy(array);
    } else {
      this.identity();
    }
  }

  copy(array) {
    this[0] = array[0];
    this[1] = array[1];
    this[2] = array[2];
    this[3] = array[3];
    this[4] = array[4];
    this[5] = array[5];
    this[6] = array[6];
    this[7] = array[7];
    this[8] = array[8];
    this[9] = array[9];
    this[10] = array[10];
    this[11] = array[11];
    this[12] = array[12];
    this[13] = array[13];
    this[14] = array[14];
    this[15] = array[15];
    return this.check();
  }

  set(m00, m10, m20, m30, m01, m11, m21, m31, m02, m12, m22, m32, m03, m13, m23, m33) {
    this[0] = m00;
    this[1] = m10;
    this[2] = m20;
    this[3] = m30;
    this[4] = m01;
    this[5] = m11;
    this[6] = m21;
    this[7] = m31;
    this[8] = m02;
    this[9] = m12;
    this[10] = m22;
    this[11] = m32;
    this[12] = m03;
    this[13] = m13;
    this[14] = m23;
    this[15] = m33;
    return this.check();
  }

  setRowMajor(m00, m01, m02, m03, m10, m11, m12, m13, m20, m21, m22, m23, m30, m31, m32, m33) {
    this[0] = m00;
    this[1] = m10;
    this[2] = m20;
    this[3] = m30;
    this[4] = m01;
    this[5] = m11;
    this[6] = m21;
    this[7] = m31;
    this[8] = m02;
    this[9] = m12;
    this[10] = m22;
    this[11] = m32;
    this[12] = m03;
    this[13] = m13;
    this[14] = m23;
    this[15] = m33;
    return this.check();
  }

  toRowMajor(result) {
    result[0] = this[0];
    result[1] = this[4];
    result[2] = this[8];
    result[3] = this[12];
    result[4] = this[1];
    result[5] = this[5];
    result[6] = this[9];
    result[7] = this[13];
    result[8] = this[2];
    result[9] = this[6];
    result[10] = this[10];
    result[11] = this[14];
    result[12] = this[3];
    result[13] = this[7];
    result[14] = this[11];
    result[15] = this[15];
    return result;
  }

  identity() {
    return this.copy(IDENTITY_MATRIX);
  }

  fromObject(object) {
    return this.check();
  }

  fromQuaternion(quaternion) {
    mat4.fromQuat(this, quaternion);
    return this.check();
  }

  frustum(view) {
    const {
      left,
      right,
      bottom,
      top,
      near = DEFAULT_NEAR,
      far = DEFAULT_FAR
    } = view;

    if (far === Infinity) {
      computeInfinitePerspectiveOffCenter(this, left, right, bottom, top, near);
    } else {
      mat4.frustum(this, left, right, bottom, top, near, far);
    }

    return this.check();
  }

  lookAt(view) {
    const {
      eye,
      center = [0, 0, 0],
      up = [0, 1, 0]
    } = view;
    mat4.lookAt(this, eye, center, up);
    return this.check();
  }

  ortho(view) {
    const {
      left,
      right,
      bottom,
      top,
      near = DEFAULT_NEAR,
      far = DEFAULT_FAR
    } = view;
    mat4.ortho(this, left, right, bottom, top, near, far);
    return this.check();
  }

  orthographic(view) {
    const {
      fovy = DEFAULT_FOVY,
      aspect = DEFAULT_ASPECT,
      focalDistance = 1,
      near = DEFAULT_NEAR,
      far = DEFAULT_FAR
    } = view;
    checkRadians(fovy);
    const halfY = fovy / 2;
    const top = focalDistance * Math.tan(halfY);
    const right = top * aspect;
    return this.ortho({
      left: -right,
      right,
      bottom: -top,
      top,
      near,
      far
    });
  }

  perspective(view) {
    const {
      fovy = 45 * Math.PI / 180,
      aspect = 1,
      near = 0.1,
      far = 500
    } = view;
    checkRadians(fovy);
    mat4.perspective(this, fovy, aspect, near, far);
    return this.check();
  }

  determinant() {
    return mat4.determinant(this);
  }

  getScale(result = [-0, -0, -0]) {
    result[0] = Math.sqrt(this[0] * this[0] + this[1] * this[1] + this[2] * this[2]);
    result[1] = Math.sqrt(this[4] * this[4] + this[5] * this[5] + this[6] * this[6]);
    result[2] = Math.sqrt(this[8] * this[8] + this[9] * this[9] + this[10] * this[10]);
    return result;
  }

  getTranslation(result = [-0, -0, -0]) {
    result[0] = this[12];
    result[1] = this[13];
    result[2] = this[14];
    return result;
  }

  getRotation(result, scaleResult) {
    result = result || [-0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0];
    scaleResult = scaleResult || [-0, -0, -0];
    const scale = this.getScale(scaleResult);
    const inverseScale0 = 1 / scale[0];
    const inverseScale1 = 1 / scale[1];
    const inverseScale2 = 1 / scale[2];
    result[0] = this[0] * inverseScale0;
    result[1] = this[1] * inverseScale1;
    result[2] = this[2] * inverseScale2;
    result[3] = 0;
    result[4] = this[4] * inverseScale0;
    result[5] = this[5] * inverseScale1;
    result[6] = this[6] * inverseScale2;
    result[7] = 0;
    result[8] = this[8] * inverseScale0;
    result[9] = this[9] * inverseScale1;
    result[10] = this[10] * inverseScale2;
    result[11] = 0;
    result[12] = 0;
    result[13] = 0;
    result[14] = 0;
    result[15] = 1;
    return result;
  }

  getRotationMatrix3(result, scaleResult) {
    result = result || [-0, -0, -0, -0, -0, -0, -0, -0, -0];
    scaleResult = scaleResult || [-0, -0, -0];
    const scale = this.getScale(scaleResult);
    const inverseScale0 = 1 / scale[0];
    const inverseScale1 = 1 / scale[1];
    const inverseScale2 = 1 / scale[2];
    result[0] = this[0] * inverseScale0;
    result[1] = this[1] * inverseScale1;
    result[2] = this[2] * inverseScale2;
    result[3] = this[4] * inverseScale0;
    result[4] = this[5] * inverseScale1;
    result[5] = this[6] * inverseScale2;
    result[6] = this[8] * inverseScale0;
    result[7] = this[9] * inverseScale1;
    result[8] = this[10] * inverseScale2;
    return result;
  }

  transpose() {
    mat4.transpose(this, this);
    return this.check();
  }

  invert() {
    mat4.invert(this, this);
    return this.check();
  }

  multiplyLeft(a) {
    mat4.multiply(this, a, this);
    return this.check();
  }

  multiplyRight(a) {
    mat4.multiply(this, this, a);
    return this.check();
  }

  rotateX(radians) {
    mat4.rotateX(this, this, radians);
    return this.check();
  }

  rotateY(radians) {
    mat4.rotateY(this, this, radians);
    return this.check();
  }

  rotateZ(radians) {
    mat4.rotateZ(this, this, radians);
    return this.check();
  }

  rotateXYZ(angleXYZ) {
    return this.rotateX(angleXYZ[0]).rotateY(angleXYZ[1]).rotateZ(angleXYZ[2]);
  }

  rotateAxis(radians, axis) {
    mat4.rotate(this, this, radians, axis);
    return this.check();
  }

  scale(factor) {
    mat4.scale(this, this, Array.isArray(factor) ? factor : [factor, factor, factor]);
    return this.check();
  }

  translate(vector) {
    mat4.translate(this, this, vector);
    return this.check();
  }

  transform(vector, result) {
    if (vector.length === 4) {
      result = vec4.transformMat4(result || [-0, -0, -0, -0], vector, this);
      checkVector(result, 4);
      return result;
    }

    return this.transformAsPoint(vector, result);
  }

  transformAsPoint(vector, result) {
    const {
      length
    } = vector;
    let out;

    switch (length) {
      case 2:
        out = vec2.transformMat4(result || [-0, -0], vector, this);
        break;

      case 3:
        out = vec3.transformMat4(result || [-0, -0, -0], vector, this);
        break;

      default:
        throw new Error('Illegal vector');
    }

    checkVector(out, vector.length);
    return out;
  }

  transformAsVector(vector, result) {
    let out;

    switch (vector.length) {
      case 2:
        out = vec2_transformMat4AsVector(result || [-0, -0], vector, this);
        break;

      case 3:
        out = vec3_transformMat4AsVector(result || [-0, -0, -0], vector, this);
        break;

      default:
        throw new Error('Illegal vector');
    }

    checkVector(out, vector.length);
    return out;
  }

  transformPoint(vector, result) {
    return this.transformAsPoint(vector, result);
  }

  transformVector(vector, result) {
    return this.transformAsPoint(vector, result);
  }

  transformDirection(vector, result) {
    return this.transformAsVector(vector, result);
  }

  makeRotationX(radians) {
    return this.identity().rotateX(radians);
  }

  makeTranslation(x, y, z) {
    return this.identity().translate([x, y, z]);
  }

}
let ZERO;
let IDENTITY;

function getZeroMatrix() {
  if (!ZERO) {
    ZERO = new Matrix4([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]);
    Object.freeze(ZERO);
  }

  return ZERO;
}

function getIdentityMatrix() {
  if (!IDENTITY) {
    IDENTITY = new Matrix4();
    Object.freeze(IDENTITY);
  }

  return IDENTITY;
}

function checkRadians(possiblyDegrees) {
  if (possiblyDegrees > Math.PI * 2) {
    throw Error('expected radians');
  }
}

function computeInfinitePerspectiveOffCenter(result, left, right, bottom, top, near) {
  const column0Row0 = 2 * near / (right - left);
  const column1Row1 = 2 * near / (top - bottom);
  const column2Row0 = (right + left) / (right - left);
  const column2Row1 = (top + bottom) / (top - bottom);
  const column2Row2 = -1;
  const column2Row3 = -1;
  const column3Row2 = -2 * near;
  result[0] = column0Row0;
  result[1] = 0;
  result[2] = 0;
  result[3] = 0;
  result[4] = 0;
  result[5] = column1Row1;
  result[6] = 0;
  result[7] = 0;
  result[8] = column2Row0;
  result[9] = column2Row1;
  result[10] = column2Row2;
  result[11] = column2Row3;
  result[12] = 0;
  result[13] = 0;
  result[14] = column3Row2;
  result[15] = 0;
  return result;
}
//# sourceMappingURL=matrix4.js.map