{"version": 3, "sources": ["../../../../src/classes/base/math-array.ts"], "names": ["config", "formatValue", "equals", "isArray", "MathArray", "Array", "clone", "constructor", "copy", "fromArray", "array", "offset", "i", "ELEMENTS", "check", "toArray", "targetArray", "from", "arrayOrObject", "fromObject", "to", "toObject", "<PERSON><PERSON><PERSON><PERSON>", "target", "toFloat32Array", "Float32Array", "toString", "formatString", "opts", "string", "printTypes", "name", "length", "exactEquals", "negate", "lerp", "a", "b", "t", "undefined", "ai", "min", "vector", "Math", "max", "clamp", "minVector", "maxVector", "add", "vectors", "subtract", "scale", "multiplyByScalar", "scalar", "debug", "validate", "Error", "valid", "Number", "isFinite", "sub", "setScalar", "addScalar", "subScalar", "multiplyScalar", "divideScalar", "clampScalar", "elements"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEA,SAA8BA,MAA9B,EAAsCC,WAAtC,EAAmDC,MAAnD,EAA2DC,OAA3D,QAAyE,kBAAzE;AAGA,eAAe,MAAeC,SAAf,4BAAiCC,KAAjC,EAA+C;AAc5DC,EAAAA,KAAK,GAAS;AAEZ,WAAO,IAAI,KAAKC,WAAT,GAAuBC,IAAvB,CAA4B,IAA5B,CAAP;AACD;;AAEDC,EAAAA,SAAS,CAACC,KAAD,EAAgCC,MAAc,GAAG,CAAjD,EAA0D;AACjE,SAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,WAAKA,CAAL,IAAUF,KAAK,CAACE,CAAC,GAAGD,MAAL,CAAf;AACD;;AACD,WAAO,KAAKG,KAAL,EAAP;AACD;;AAKDC,EAAAA,OAAO,CAACC,WAAyB,GAAG,EAA7B,EAAiCL,MAAc,GAAG,CAAlD,EAAmE;AACxE,SAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtCI,MAAAA,WAAW,CAACL,MAAM,GAAGC,CAAV,CAAX,GAA0B,KAAKA,CAAL,CAA1B;AACD;;AACD,WAAOI,WAAP;AACD;;AAEDC,EAAAA,IAAI,CAACC,aAAD,EAAuD;AACzD,WAAOb,KAAK,CAACF,OAAN,CAAce,aAAd,IAA+B,KAAKV,IAAL,CAAUU,aAAV,CAA/B,GAA0D,KAAKC,UAAL,CAAgBD,aAAhB,CAAjE;AACD;;AAEDE,EAAAA,EAAE,CAAkCF,aAAlC,EAAuD;AAEvD,QAAIA,aAAa,KAAK,IAAtB,EAA4B;AAC1B,aAAO,IAAP;AACD;;AAED,WAAOf,OAAO,CAACe,aAAD,CAAP,GAAyB,KAAKH,OAAL,CAAaG,aAAb,CAAzB,GAAuD,KAAKG,QAAL,CAAcH,aAAd,CAA9D;AACD;;AAEDI,EAAAA,QAAQ,CAACC,MAAD,EAAqB;AAC3B,WAAOA,MAAM,GAAG,KAAKH,EAAL,CAAQG,MAAR,CAAH,GAAqB,IAAlC;AACD;;AAGDC,EAAAA,cAAc,GAAiB;AAC7B,WAAO,IAAIC,YAAJ,CAAiB,IAAjB,CAAP;AACD;;AAEDC,EAAAA,QAAQ,GAAW;AACjB,WAAO,KAAKC,YAAL,CAAkB3B,MAAlB,CAAP;AACD;;AAGD2B,EAAAA,YAAY,CAACC,IAAD,EAAqC;AAC/C,QAAIC,MAAM,GAAG,EAAb;;AACA,SAAK,IAAIjB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtCiB,MAAAA,MAAM,IAAI,CAACjB,CAAC,GAAG,CAAJ,GAAQ,IAAR,GAAe,EAAhB,IAAsBX,WAAW,CAAC,KAAKW,CAAL,CAAD,EAAUgB,IAAV,CAA3C;AACD;;AACD,qBAAUA,IAAI,CAACE,UAAL,GAAkB,KAAKvB,WAAL,CAAiBwB,IAAnC,GAA0C,EAApD,cAA0DF,MAA1D;AACD;;AAED3B,EAAAA,MAAM,CAACQ,KAAD,EAAyC;AAC7C,QAAI,CAACA,KAAD,IAAU,KAAKsB,MAAL,KAAgBtB,KAAK,CAACsB,MAApC,EAA4C;AAC1C,aAAO,KAAP;AACD;;AACD,SAAK,IAAIpB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,UAAI,CAACV,MAAM,CAAC,KAAKU,CAAL,CAAD,EAAUF,KAAK,CAACE,CAAD,CAAf,CAAX,EAAgC;AAC9B,eAAO,KAAP;AACD;AACF;;AACD,WAAO,IAAP;AACD;;AAEDqB,EAAAA,WAAW,CAACvB,KAAD,EAAyC;AAClD,QAAI,CAACA,KAAD,IAAU,KAAKsB,MAAL,KAAgBtB,KAAK,CAACsB,MAApC,EAA4C;AAC1C,aAAO,KAAP;AACD;;AACD,SAAK,IAAIpB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,UAAI,KAAKA,CAAL,MAAYF,KAAK,CAACE,CAAD,CAArB,EAA0B;AACxB,eAAO,KAAP;AACD;AACF;;AACD,WAAO,IAAP;AACD;;AAKDsB,EAAAA,MAAM,GAAS;AACb,SAAK,IAAItB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,WAAKA,CAAL,IAAU,CAAC,KAAKA,CAAL,CAAX;AACD;;AACD,WAAO,KAAKE,KAAL,EAAP;AACD;;AAMDqB,EAAAA,IAAI,CAACC,CAAD,EAA4BC,CAA5B,EAAgEC,CAAhE,EAAkF;AACpF,QAAIA,CAAC,KAAKC,SAAV,EAAqB;AACnB,aAAO,KAAKJ,IAAL,CAAU,IAAV,EAAgBC,CAAhB,EAAmBC,CAAnB,CAAP;AACD;;AACD,SAAK,IAAIzB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,YAAM4B,EAAE,GAAGJ,CAAC,CAACxB,CAAD,CAAZ;AACA,WAAKA,CAAL,IAAU4B,EAAE,GAAGF,CAAC,IAAID,CAAC,CAACzB,CAAD,CAAD,GAAO4B,EAAX,CAAhB;AACD;;AACD,WAAO,KAAK1B,KAAL,EAAP;AACD;;AAGD2B,EAAAA,GAAG,CAACC,MAAD,EAAuC;AACxC,SAAK,IAAI9B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,WAAKA,CAAL,IAAU+B,IAAI,CAACF,GAAL,CAASC,MAAM,CAAC9B,CAAD,CAAf,EAAoB,KAAKA,CAAL,CAApB,CAAV;AACD;;AACD,WAAO,KAAKE,KAAL,EAAP;AACD;;AAGD8B,EAAAA,GAAG,CAACF,MAAD,EAAuC;AACxC,SAAK,IAAI9B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,WAAKA,CAAL,IAAU+B,IAAI,CAACC,GAAL,CAASF,MAAM,CAAC9B,CAAD,CAAf,EAAoB,KAAKA,CAAL,CAApB,CAAV;AACD;;AACD,WAAO,KAAKE,KAAL,EAAP;AACD;;AAED+B,EAAAA,KAAK,CAACC,SAAD,EAAoCC,SAApC,EAA6E;AAChF,SAAK,IAAInC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,WAAKA,CAAL,IAAU+B,IAAI,CAACF,GAAL,CAASE,IAAI,CAACC,GAAL,CAAS,KAAKhC,CAAL,CAAT,EAAkBkC,SAAS,CAAClC,CAAD,CAA3B,CAAT,EAA0CmC,SAAS,CAACnC,CAAD,CAAnD,CAAV;AACD;;AACD,WAAO,KAAKE,KAAL,EAAP;AACD;;AAEDkC,EAAAA,GAAG,CAAC,GAAGC,OAAJ,EAA6C;AAC9C,SAAK,MAAMP,MAAX,IAAqBO,OAArB,EAA8B;AAC5B,WAAK,IAAIrC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,aAAKA,CAAL,KAAW8B,MAAM,CAAC9B,CAAD,CAAjB;AACD;AACF;;AACD,WAAO,KAAKE,KAAL,EAAP;AACD;;AAEDoC,EAAAA,QAAQ,CAAC,GAAGD,OAAJ,EAA6C;AACnD,SAAK,MAAMP,MAAX,IAAqBO,OAArB,EAA8B;AAC5B,WAAK,IAAIrC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,aAAKA,CAAL,KAAW8B,MAAM,CAAC9B,CAAD,CAAjB;AACD;AACF;;AACD,WAAO,KAAKE,KAAL,EAAP;AACD;;AAEDqC,EAAAA,KAAK,CAACA,KAAD,EAA+C;AAClD,QAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;AAC7B,WAAK,IAAIvC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,aAAKA,CAAL,KAAWuC,KAAX;AACD;AACF,KAJD,MAIO;AACL,WAAK,IAAIvC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAT,IAAqBD,CAAC,GAAGuC,KAAK,CAACnB,MAA/C,EAAuD,EAAEpB,CAAzD,EAA4D;AAC1D,aAAKA,CAAL,KAAWuC,KAAK,CAACvC,CAAD,CAAhB;AACD;AACF;;AACD,WAAO,KAAKE,KAAL,EAAP;AACD;;AAMDsC,EAAAA,gBAAgB,CAACC,MAAD,EAAuB;AACrC,SAAK,IAAIzC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,WAAKA,CAAL,KAAWyC,MAAX;AACD;;AACD,WAAO,KAAKvC,KAAL,EAAP;AACD;;AAKDA,EAAAA,KAAK,GAAS;AACZ,QAAId,MAAM,CAACsD,KAAP,IAAgB,CAAC,KAAKC,QAAL,EAArB,EAAsC;AACpC,YAAM,IAAIC,KAAJ,oBAAsB,KAAKjD,WAAL,CAAiBwB,IAAvC,0CAAN;AACD;;AACD,WAAO,IAAP;AACD;;AAGDwB,EAAAA,QAAQ,GAAY;AAClB,QAAIE,KAAK,GAAG,KAAKzB,MAAL,KAAgB,KAAKnB,QAAjC;;AACA,SAAK,IAAID,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC6C,MAAAA,KAAK,GAAGA,KAAK,IAAIC,MAAM,CAACC,QAAP,CAAgB,KAAK/C,CAAL,CAAhB,CAAjB;AACD;;AACD,WAAO6C,KAAP;AACD;;AAKDG,EAAAA,GAAG,CAACxB,CAAD,EAAkC;AACnC,WAAO,KAAKc,QAAL,CAAcd,CAAd,CAAP;AACD;;AAGDyB,EAAAA,SAAS,CAACzB,CAAD,EAAkB;AACzB,SAAK,IAAIxB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,WAAKA,CAAL,IAAUwB,CAAV;AACD;;AACD,WAAO,KAAKtB,KAAL,EAAP;AACD;;AAGDgD,EAAAA,SAAS,CAAC1B,CAAD,EAAkB;AACzB,SAAK,IAAIxB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,WAAKA,CAAL,KAAWwB,CAAX;AACD;;AACD,WAAO,KAAKtB,KAAL,EAAP;AACD;;AAGDiD,EAAAA,SAAS,CAAC3B,CAAD,EAAkB;AACzB,WAAO,KAAK0B,SAAL,CAAe,CAAC1B,CAAhB,CAAP;AACD;;AAGD4B,EAAAA,cAAc,CAACX,MAAD,EAAuB;AAGnC,SAAK,IAAIzC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,WAAKA,CAAL,KAAWyC,MAAX;AACD;;AACD,WAAO,KAAKvC,KAAL,EAAP;AACD;;AAGDmD,EAAAA,YAAY,CAAC7B,CAAD,EAAkB;AAC5B,WAAO,KAAKgB,gBAAL,CAAsB,IAAIhB,CAA1B,CAAP;AACD;;AAGD8B,EAAAA,WAAW,CAACzB,GAAD,EAAcG,GAAd,EAAiC;AAC1C,SAAK,IAAIhC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,WAAKA,CAAL,IAAU+B,IAAI,CAACF,GAAL,CAASE,IAAI,CAACC,GAAL,CAAS,KAAKhC,CAAL,CAAT,EAAkB6B,GAAlB,CAAT,EAAiCG,GAAjC,CAAV;AACD;;AACD,WAAO,KAAK9B,KAAL,EAAP;AACD;;AAGW,MAARqD,QAAQ,GAAiB;AAC3B,WAAO,IAAP;AACD;;AAlQ2D", "sourcesContent": ["// math.gl, MIT License\nimport {NumericArray} from '@math.gl/types';\nimport {ConfigurationOptions, config, formatValue, equals, isArray} from '../../lib/common';\n\n/** Base class for vectors and matrices */\nexport default abstract class MathArray extends Array<number> {\n  /** Number of elements (values) in this array */\n  abstract get ELEMENTS(): number;\n\n  abstract copy(vector: Readonly<NumericArray>): this;\n\n  abstract fromObject(object: object): this;\n\n  // Common methods\n\n  /**\n   * Clone the current object\n   * @returns a new copy of this object\n   */\n  clone(): this {\n    // @ts-expect-error TS2351: Cannot use 'new' with an expression whose type lacks a call or construct signature.\n    return new this.constructor().copy(this); // eslint-disable-line\n  }\n\n  fromArray(array: Readonly<NumericArray>, offset: number = 0): this {\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      this[i] = array[i + offset];\n    }\n    return this.check();\n  }\n\n  toArray<TypedArray>(targetArray: TypedArray, offset?: number): TypedArray;\n  toArray(targetArray?: number[], offset?: number): NumericArray;\n\n  toArray(targetArray: NumericArray = [], offset: number = 0): NumericArray {\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      targetArray[offset + i] = this[i];\n    }\n    return targetArray;\n  }\n\n  from(arrayOrObject: Readonly<NumericArray> | object): this {\n    return Array.isArray(arrayOrObject) ? this.copy(arrayOrObject) : this.fromObject(arrayOrObject);\n  }\n\n  to<T extends NumericArray | object>(arrayOrObject: T): T {\n    // @ts-ignore\n    if (arrayOrObject === this) {\n      return this;\n    }\n    // @ts-expect-error TS2339: Property 'toObject' does not exist on type 'MathArray'.\n    return isArray(arrayOrObject) ? this.toArray(arrayOrObject) : this.toObject(arrayOrObject);\n  }\n\n  toTarget(target: this): this {\n    return target ? this.to(target) : this;\n  }\n\n  /** @deprecated */\n  toFloat32Array(): Float32Array {\n    return new Float32Array(this);\n  }\n\n  toString(): string {\n    return this.formatString(config);\n  }\n\n  /** Formats string according to options */\n  formatString(opts: ConfigurationOptions): string {\n    let string = '';\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      string += (i > 0 ? ', ' : '') + formatValue(this[i], opts);\n    }\n    return `${opts.printTypes ? this.constructor.name : ''}[${string}]`;\n  }\n\n  equals(array: Readonly<NumericArray>): boolean {\n    if (!array || this.length !== array.length) {\n      return false;\n    }\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      if (!equals(this[i], array[i])) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  exactEquals(array: Readonly<NumericArray>): boolean {\n    if (!array || this.length !== array.length) {\n      return false;\n    }\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      if (this[i] !== array[i]) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  // Modifiers\n\n  /** Negates all values in this object */\n  negate(): this {\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      this[i] = -this[i];\n    }\n    return this.check();\n  }\n\n  /** Linearly interpolates between two values */\n  lerp(a: Readonly<NumericArray>, t: number): this;\n  lerp(a: Readonly<NumericArray>, b: Readonly<NumericArray>, t: number): this;\n\n  lerp(a: Readonly<NumericArray>, b: Readonly<NumericArray> | number, t?: number): this {\n    if (t === undefined) {\n      return this.lerp(this, a, b as number);\n    }\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      const ai = a[i];\n      this[i] = ai + t * (b[i] - ai);\n    }\n    return this.check();\n  }\n\n  /** Minimal */\n  min(vector: Readonly<NumericArray>): this {\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      this[i] = Math.min(vector[i], this[i]);\n    }\n    return this.check();\n  }\n\n  /** Maximal */\n  max(vector: Readonly<NumericArray>): this {\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      this[i] = Math.max(vector[i], this[i]);\n    }\n    return this.check();\n  }\n\n  clamp(minVector: Readonly<NumericArray>, maxVector: Readonly<NumericArray>): this {\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      this[i] = Math.min(Math.max(this[i], minVector[i]), maxVector[i]);\n    }\n    return this.check();\n  }\n\n  add(...vectors: Readonly<NumericArray>[]): this {\n    for (const vector of vectors) {\n      for (let i = 0; i < this.ELEMENTS; ++i) {\n        this[i] += vector[i];\n      }\n    }\n    return this.check();\n  }\n\n  subtract(...vectors: Readonly<NumericArray>[]): this {\n    for (const vector of vectors) {\n      for (let i = 0; i < this.ELEMENTS; ++i) {\n        this[i] -= vector[i];\n      }\n    }\n    return this.check();\n  }\n\n  scale(scale: number | Readonly<NumericArray>): this {\n    if (typeof scale === 'number') {\n      for (let i = 0; i < this.ELEMENTS; ++i) {\n        this[i] *= scale;\n      }\n    } else {\n      for (let i = 0; i < this.ELEMENTS && i < scale.length; ++i) {\n        this[i] *= scale[i];\n      }\n    }\n    return this.check();\n  }\n\n  /**\n   * Multiplies all elements by `scale`\n   * Note: `Matrix4.multiplyByScalar` only scales its 3x3 \"minor\"\n   */\n  multiplyByScalar(scalar: number): this {\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      this[i] *= scalar;\n    }\n    return this.check();\n  }\n\n  // Debug checks\n\n  /** Throws an error if array length is incorrect or contains illegal values */\n  check(): this {\n    if (config.debug && !this.validate()) {\n      throw new Error(`math.gl: ${this.constructor.name} some fields set to invalid numbers'`);\n    }\n    return this;\n  }\n\n  /** Returns false if the array length is incorrect or contains illegal values */\n  validate(): boolean {\n    let valid = this.length === this.ELEMENTS;\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      valid = valid && Number.isFinite(this[i]);\n    }\n    return valid;\n  }\n\n  // three.js compatibility\n\n  /** @deprecated */\n  sub(a: Readonly<NumericArray>): this {\n    return this.subtract(a);\n  }\n\n  /** @deprecated */\n  setScalar(a: number): this {\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      this[i] = a;\n    }\n    return this.check();\n  }\n\n  /** @deprecated */\n  addScalar(a: number): this {\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      this[i] += a;\n    }\n    return this.check();\n  }\n\n  /** @deprecated */\n  subScalar(a: number): this {\n    return this.addScalar(-a);\n  }\n\n  /** @deprecated */\n  multiplyScalar(scalar: number): this {\n    // Multiplies all elements\n    // `Matrix4.scale` only scales its 3x3 \"minor\"\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      this[i] *= scalar;\n    }\n    return this.check();\n  }\n\n  /** @deprecated */\n  divideScalar(a: number): this {\n    return this.multiplyByScalar(1 / a);\n  }\n\n  /** @deprecated */\n  clampScalar(min: number, max: number): this {\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      this[i] = Math.min(Math.max(this[i], min), max);\n    }\n    return this.check();\n  }\n\n  /** @deprecated */\n  get elements(): NumericArray {\n    return this;\n  }\n}\n"], "file": "math-array.js"}