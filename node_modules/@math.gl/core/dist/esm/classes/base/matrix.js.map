{"version": 3, "sources": ["../../../../src/classes/base/matrix.ts"], "names": ["MathArray", "checkNumber", "config", "Matrix", "toString", "string", "printRowMajor", "row", "RANK", "col", "i", "ELEMENTS", "getElementIndex", "getElement", "setElement", "value", "getColumn", "columnIndex", "result", "Array", "fill", "firstIndex", "setColumn", "columnVector"], "mappings": "AAGA,OAAOA,SAAP,MAAsB,cAAtB;AACA,SAAQC,WAAR,QAA0B,sBAA1B;AACA,SAAQC,MAAR,QAAqB,kBAArB;AAGA,eAAe,MAAeC,MAAf,SAA8BH,SAA9B,CAAwC;AAcrDI,EAAAA,QAAQ,GAAW;AACjB,QAAIC,MAAM,GAAG,GAAb;;AACA,QAAIH,MAAM,CAACI,aAAX,EAA0B;AACxBD,MAAAA,MAAM,IAAI,YAAV;;AACA,WAAK,IAAIE,GAAG,GAAG,CAAf,EAAkBA,GAAG,GAAG,KAAKC,IAA7B,EAAmC,EAAED,GAArC,EAA0C;AACxC,aAAK,IAAIE,GAAG,GAAG,CAAf,EAAkBA,GAAG,GAAG,KAAKD,IAA7B,EAAmC,EAAEC,GAArC,EAA0C;AACxCJ,UAAAA,MAAM,eAAQ,KAAKI,GAAG,GAAG,KAAKD,IAAX,GAAkBD,GAAvB,CAAR,CAAN;AACD;AACF;AACF,KAPD,MAOO;AACLF,MAAAA,MAAM,IAAI,eAAV;;AACA,WAAK,IAAIK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtCL,QAAAA,MAAM,eAAQ,KAAKK,CAAL,CAAR,CAAN;AACD;AACF;;AACDL,IAAAA,MAAM,IAAI,GAAV;AACA,WAAOA,MAAP;AACD;;AAEDO,EAAAA,eAAe,CAACL,GAAD,EAAcE,GAAd,EAAmC;AAChD,WAAOA,GAAG,GAAG,KAAKD,IAAX,GAAkBD,GAAzB;AACD;;AAGDM,EAAAA,UAAU,CAACN,GAAD,EAAcE,GAAd,EAAmC;AAC3C,WAAO,KAAKA,GAAG,GAAG,KAAKD,IAAX,GAAkBD,GAAvB,CAAP;AACD;;AAGDO,EAAAA,UAAU,CAACP,GAAD,EAAcE,GAAd,EAA2BM,KAA3B,EAAgD;AACxD,SAAKN,GAAG,GAAG,KAAKD,IAAX,GAAkBD,GAAvB,IAA8BN,WAAW,CAACc,KAAD,CAAzC;AACA,WAAO,IAAP;AACD;;AAIDC,EAAAA,SAAS,CAACC,WAAD,EAAsBC,MAAgB,GAAG,IAAIC,KAAJ,CAAU,KAAKX,IAAf,EAAqBY,IAArB,CAA0B,CAAC,CAA3B,CAAzC,EAAkF;AACzF,UAAMC,UAAU,GAAGJ,WAAW,GAAG,KAAKT,IAAtC;;AACA,SAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKF,IAAzB,EAA+B,EAAEE,CAAjC,EAAoC;AAClCQ,MAAAA,MAAM,CAACR,CAAD,CAAN,GAAY,KAAKW,UAAU,GAAGX,CAAlB,CAAZ;AACD;;AACD,WAAOQ,MAAP;AACD;;AAEDI,EAAAA,SAAS,CAACL,WAAD,EAAsBM,YAAtB,EAAkE;AACzE,UAAMF,UAAU,GAAGJ,WAAW,GAAG,KAAKT,IAAtC;;AACA,SAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKF,IAAzB,EAA+B,EAAEE,CAAjC,EAAoC;AAClC,WAAKW,UAAU,GAAGX,CAAlB,IAAuBa,YAAY,CAACb,CAAD,CAAnC;AACD;;AACD,WAAO,IAAP;AACD;;AAhEoD", "sourcesContent": ["// Copyright (c) 2017 Uber Technologies, Inc.\n// MIT License\nimport {NumericArray} from '@math.gl/types';\nimport MathArray from './math-array';\nimport {checkNumber} from '../../lib/validators';\nimport {config} from '../../lib/common';\n\n/** Base class for matrices */\nexport default abstract class Matrix extends MathArray {\n  abstract get RANK(): number;\n\n  // fromObject(object) {\n  //   const array = object.elements;\n  //   return this.fromRowMajor(array);\n  // }\n  // toObject(object) {\n  //   const array = object.elements;\n  //   this.toRowMajor(array);\n  //   return object;\n  // }\n\n  // TODO better override formatString?\n  toString(): string {\n    let string = '[';\n    if (config.printRowMajor) {\n      string += 'row-major:';\n      for (let row = 0; row < this.RANK; ++row) {\n        for (let col = 0; col < this.RANK; ++col) {\n          string += ` ${this[col * this.RANK + row]}`;\n        }\n      }\n    } else {\n      string += 'column-major:';\n      for (let i = 0; i < this.ELEMENTS; ++i) {\n        string += ` ${this[i]}`;\n      }\n    }\n    string += ']';\n    return string;\n  }\n\n  getElementIndex(row: number, col: number): number {\n    return col * this.RANK + row;\n  }\n\n  // By default assumes row major indices\n  getElement(row: number, col: number): number {\n    return this[col * this.RANK + row];\n  }\n\n  // By default assumes row major indices\n  setElement(row: number, col: number, value: number): this {\n    this[col * this.RANK + row] = checkNumber(value);\n    return this;\n  }\n  getColumn<NumArrayT>(columnIndex: number, result: NumArrayT): NumArrayT;\n  getColumn(columnIndex: number): number[];\n\n  getColumn(columnIndex: number, result: number[] = new Array(this.RANK).fill(-0)): number[] {\n    const firstIndex = columnIndex * this.RANK;\n    for (let i = 0; i < this.RANK; ++i) {\n      result[i] = this[firstIndex + i];\n    }\n    return result;\n  }\n\n  setColumn(columnIndex: number, columnVector: Readonly<NumericArray>): this {\n    const firstIndex = columnIndex * this.RANK;\n    for (let i = 0; i < this.RANK; ++i) {\n      this[firstIndex + i] = columnVector[i];\n    }\n    return this;\n  }\n}\n"], "file": "matrix.js"}