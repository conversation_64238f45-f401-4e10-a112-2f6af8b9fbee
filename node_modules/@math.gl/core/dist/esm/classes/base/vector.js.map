{"version": 3, "sources": ["../../../../src/classes/base/vector.ts"], "names": ["MathArray", "checkNumber", "assert", "Vector", "x", "value", "y", "len", "Math", "sqrt", "lengthSquared", "magnitude", "length", "i", "ELEMENTS", "magnitudeSquared", "distance", "<PERSON><PERSON><PERSON><PERSON>", "distanceSquared", "dist", "dot", "product", "normalize", "check", "multiply", "vectors", "vector", "divide", "lengthSq", "distanceTo", "distanceToSquared", "getComponent", "setComponent", "addVectors", "a", "b", "copy", "add", "subVectors", "subtract", "multiplyVectors", "addScaledVector", "constructor", "multiplyScalar"], "mappings": "AAGA,OAAOA,SAAP,MAAsB,cAAtB;AACA,SAAQC,WAAR,QAA0B,sBAA1B;AACA,OAAOC,MAAP,MAAmB,kBAAnB;AAGA,eAAe,MAAeC,MAAf,SAA8BH,SAA9B,CAAwC;AAGhD,MAADI,CAAC,GAAW;AACd,WAAO,KAAK,CAAL,CAAP;AACD;;AAEI,MAADA,CAAC,CAACC,KAAD,EAAgB;AACnB,SAAK,CAAL,IAAUJ,WAAW,CAACI,KAAD,CAArB;AACD;;AAEI,MAADC,CAAC,GAAW;AACd,WAAO,KAAK,CAAL,CAAP;AACD;;AAEI,MAADA,CAAC,CAACD,KAAD,EAAgB;AACnB,SAAK,CAAL,IAAUJ,WAAW,CAACI,KAAD,CAArB;AACD;;AAQDE,EAAAA,GAAG,GAAW;AACZ,WAAOC,IAAI,CAACC,IAAL,CAAU,KAAKC,aAAL,EAAV,CAAP;AACD;;AAKDC,EAAAA,SAAS,GAAW;AAClB,WAAO,KAAKJ,GAAL,EAAP;AACD;;AAKDG,EAAAA,aAAa,GAAW;AACtB,QAAIE,MAAM,GAAG,CAAb;;AACA,SAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtCD,MAAAA,MAAM,IAAI,KAAKC,CAAL,IAAU,KAAKA,CAAL,CAApB;AACD;;AACD,WAAOD,MAAP;AACD;;AAKDG,EAAAA,gBAAgB,GAAW;AACzB,WAAO,KAAKL,aAAL,EAAP;AACD;;AAEDM,EAAAA,QAAQ,CAACC,SAAD,EAA4C;AAClD,WAAOT,IAAI,CAACC,IAAL,CAAU,KAAKS,eAAL,CAAqBD,SAArB,CAAV,CAAP;AACD;;AAEDC,EAAAA,eAAe,CAACD,SAAD,EAA4C;AACzD,QAAIL,MAAM,GAAG,CAAb;;AACA,SAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,YAAMM,IAAI,GAAG,KAAKN,CAAL,IAAUI,SAAS,CAACJ,CAAD,CAAhC;AACAD,MAAAA,MAAM,IAAIO,IAAI,GAAGA,IAAjB;AACD;;AACD,WAAOlB,WAAW,CAACW,MAAD,CAAlB;AACD;;AAEDQ,EAAAA,GAAG,CAACH,SAAD,EAA4C;AAC7C,QAAII,OAAO,GAAG,CAAd;;AACA,SAAK,IAAIR,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtCQ,MAAAA,OAAO,IAAI,KAAKR,CAAL,IAAUI,SAAS,CAACJ,CAAD,CAA9B;AACD;;AACD,WAAOZ,WAAW,CAACoB,OAAD,CAAlB;AACD;;AAIDC,EAAAA,SAAS,GAAS;AAChB,UAAMV,MAAM,GAAG,KAAKD,SAAL,EAAf;;AACA,QAAIC,MAAM,KAAK,CAAf,EAAkB;AAChB,WAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,aAAKA,CAAL,KAAWD,MAAX;AACD;AACF;;AACD,WAAO,KAAKW,KAAL,EAAP;AACD;;AAEDC,EAAAA,QAAQ,CAAC,GAAGC,OAAJ,EAA6C;AACnD,SAAK,MAAMC,MAAX,IAAqBD,OAArB,EAA8B;AAC5B,WAAK,IAAIZ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,aAAKA,CAAL,KAAWa,MAAM,CAACb,CAAD,CAAjB;AACD;AACF;;AACD,WAAO,KAAKU,KAAL,EAAP;AACD;;AAEDI,EAAAA,MAAM,CAAC,GAAGF,OAAJ,EAA6C;AACjD,SAAK,MAAMC,MAAX,IAAqBD,OAArB,EAA8B;AAC5B,WAAK,IAAIZ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,aAAKA,CAAL,KAAWa,MAAM,CAACb,CAAD,CAAjB;AACD;AACF;;AACD,WAAO,KAAKU,KAAL,EAAP;AACD;;AAIDK,EAAAA,QAAQ,GAAW;AACjB,WAAO,KAAKlB,aAAL,EAAP;AACD;;AACDmB,EAAAA,UAAU,CAACH,MAAD,EAAyC;AACjD,WAAO,KAAKV,QAAL,CAAcU,MAAd,CAAP;AACD;;AACDI,EAAAA,iBAAiB,CAACJ,MAAD,EAAyC;AACxD,WAAO,KAAKR,eAAL,CAAqBQ,MAArB,CAAP;AACD;;AAEDK,EAAAA,YAAY,CAAClB,CAAD,EAAoB;AAC9BX,IAAAA,MAAM,CAACW,CAAC,IAAI,CAAL,IAAUA,CAAC,GAAG,KAAKC,QAApB,EAA8B,uBAA9B,CAAN;AACA,WAAOb,WAAW,CAAC,KAAKY,CAAL,CAAD,CAAlB;AACD;;AAEDmB,EAAAA,YAAY,CAACnB,CAAD,EAAYR,KAAZ,EAAiC;AAC3CH,IAAAA,MAAM,CAACW,CAAC,IAAI,CAAL,IAAUA,CAAC,GAAG,KAAKC,QAApB,EAA8B,uBAA9B,CAAN;AACA,SAAKD,CAAL,IAAUR,KAAV;AACA,WAAO,KAAKkB,KAAL,EAAP;AACD;;AAEDU,EAAAA,UAAU,CAACC,CAAD,EAA4BC,CAA5B,EAA6D;AACrE,WAAO,KAAKC,IAAL,CAAUF,CAAV,EAAaG,GAAb,CAAiBF,CAAjB,CAAP;AACD;;AAEDG,EAAAA,UAAU,CAACJ,CAAD,EAA4BC,CAA5B,EAA6D;AACrE,WAAO,KAAKC,IAAL,CAAUF,CAAV,EAAaK,QAAb,CAAsBJ,CAAtB,CAAP;AACD;;AAEDK,EAAAA,eAAe,CAACN,CAAD,EAA4BC,CAA5B,EAA6D;AAC1E,WAAO,KAAKC,IAAL,CAAUF,CAAV,EAAaV,QAAb,CAAsBW,CAAtB,CAAP;AACD;;AAEDM,EAAAA,eAAe,CAACP,CAAD,EAA4BC,CAA5B,EAA6C;AAE1D,WAAO,KAAKE,GAAL,CAAS,IAAI,KAAKK,WAAT,CAAqBR,CAArB,EAAwBS,cAAxB,CAAuCR,CAAvC,CAAT,CAAP;AACD;;AA/IoD", "sourcesContent": ["// Copyright (c) 2017 Uber Technologies, Inc.\n// MIT License\nimport {NumericArray} from '@math.gl/types';\nimport MathArray from './math-array';\nimport {checkNumber} from '../../lib/validators';\nimport assert from '../../lib/assert';\n\n/** Base class for vectors with at least 2 elements */\nexport default abstract class Vector extends MathArray {\n  // ACCESSORS\n\n  get x(): number {\n    return this[0];\n  }\n\n  set x(value: number) {\n    this[0] = checkNumber(value);\n  }\n\n  get y(): number {\n    return this[1];\n  }\n\n  set y(value: number) {\n    this[1] = checkNumber(value);\n  }\n\n  /**\n   * Returns the length of the vector from the origin to the point described by this vector\n   *\n   * @note `length` is a reserved word for Arrays, so `v.length()` will return number of elements\n   * Instead we provide `len` and `magnitude`\n   */\n  len(): number {\n    return Math.sqrt(this.lengthSquared());\n  }\n\n  /**\n   * Returns the length of the vector from the origin to the point described by this vector\n   */\n  magnitude(): number {\n    return this.len();\n  }\n\n  /**\n   * Returns the squared length of the vector from the origin to the point described by this vector\n   */\n  lengthSquared(): number {\n    let length = 0;\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      length += this[i] * this[i];\n    }\n    return length;\n  }\n\n  /**\n   * Returns the squared length of the vector from the origin to the point described by this vector\n   */\n  magnitudeSquared(): number {\n    return this.lengthSquared();\n  }\n\n  distance(mathArray: Readonly<NumericArray>): number {\n    return Math.sqrt(this.distanceSquared(mathArray));\n  }\n\n  distanceSquared(mathArray: Readonly<NumericArray>): number {\n    let length = 0;\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      const dist = this[i] - mathArray[i];\n      length += dist * dist;\n    }\n    return checkNumber(length);\n  }\n\n  dot(mathArray: Readonly<NumericArray>): number {\n    let product = 0;\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      product += this[i] * mathArray[i];\n    }\n    return checkNumber(product);\n  }\n\n  // MODIFIERS\n\n  normalize(): this {\n    const length = this.magnitude();\n    if (length !== 0) {\n      for (let i = 0; i < this.ELEMENTS; ++i) {\n        this[i] /= length;\n      }\n    }\n    return this.check();\n  }\n\n  multiply(...vectors: Readonly<NumericArray>[]): this {\n    for (const vector of vectors) {\n      for (let i = 0; i < this.ELEMENTS; ++i) {\n        this[i] *= vector[i];\n      }\n    }\n    return this.check();\n  }\n\n  divide(...vectors: Readonly<NumericArray>[]): this {\n    for (const vector of vectors) {\n      for (let i = 0; i < this.ELEMENTS; ++i) {\n        this[i] /= vector[i];\n      }\n    }\n    return this.check();\n  }\n\n  // THREE.js compatibility\n\n  lengthSq(): number {\n    return this.lengthSquared();\n  }\n  distanceTo(vector: Readonly<NumericArray>): number {\n    return this.distance(vector);\n  }\n  distanceToSquared(vector: Readonly<NumericArray>): number {\n    return this.distanceSquared(vector);\n  }\n\n  getComponent(i: number): number {\n    assert(i >= 0 && i < this.ELEMENTS, 'index is out of range');\n    return checkNumber(this[i]);\n  }\n\n  setComponent(i: number, value: number): this {\n    assert(i >= 0 && i < this.ELEMENTS, 'index is out of range');\n    this[i] = value;\n    return this.check();\n  }\n\n  addVectors(a: Readonly<NumericArray>, b: Readonly<NumericArray>): this {\n    return this.copy(a).add(b);\n  }\n\n  subVectors(a: Readonly<NumericArray>, b: Readonly<NumericArray>): this {\n    return this.copy(a).subtract(b);\n  }\n\n  multiplyVectors(a: Readonly<NumericArray>, b: Readonly<NumericArray>): this {\n    return this.copy(a).multiply(b);\n  }\n\n  addScaledVector(a: Readonly<NumericArray>, b: number): this {\n    // @ts-expect-error error TS2351: Cannot use 'new' with an expression whose type lacks a call or construct signature.\n    return this.add(new this.constructor(a).multiplyScalar(b));\n  }\n}\n"], "file": "vector.js"}