{"version": 3, "sources": ["../../../src/classes/vector2.ts"], "names": ["Vector", "config", "isArray", "checkNumber", "vec2", "vec2_transformMat4AsVector", "Vector2", "constructor", "x", "y", "arguments", "length", "copy", "debug", "set", "check", "array", "fromObject", "object", "toObject", "ELEMENTS", "horizontalAngle", "Math", "atan2", "verticalAngle", "transform", "matrix4", "transformAsPoint", "transformMat4", "transformAsVector", "transformByMatrix3", "matrix3", "transformMat3", "transformByMatrix2x3", "matrix2x3", "transformMat2d", "transformByMatrix2", "matrix2", "transformMat2"], "mappings": "AAEA,OAAOA,MAAP,MAAmB,eAAnB;AACA,SAAQC,MAAR,EAAgBC,OAAhB,QAA8B,eAA9B;AACA,SAAQC,WAAR,QAA0B,mBAA1B;AACA,OAAO,KAAKC,IAAZ,MAAsB,gBAAtB;AAEA,SAAQC,0BAAR,QAAyC,yBAAzC;AAOA,eAAe,MAAMC,OAAN,SAAsBN,MAAtB,CAA6B;AAE1CO,EAAAA,WAAW,CAACC,CAAkC,GAAG,CAAtC,EAAyCC,CAAS,GAAG,CAArD,EAAwD;AAEjE,UAAM,CAAN;;AACA,QAAIP,OAAO,CAACM,CAAD,CAAP,IAAcE,SAAS,CAACC,MAAV,KAAqB,CAAvC,EAA0C;AACxC,WAAKC,IAAL,CAAUJ,CAAV;AACD,KAFD,MAEO;AACL,UAAIP,MAAM,CAACY,KAAX,EAAkB;AAChBV,QAAAA,WAAW,CAACK,CAAD,CAAX;AACAL,QAAAA,WAAW,CAACM,CAAD,CAAX;AACD;;AACD,WAAK,CAAL,IAAUD,CAAV;AACA,WAAK,CAAL,IAAUC,CAAV;AACD;AACF;;AAEDK,EAAAA,GAAG,CAACN,CAAD,EAAYC,CAAZ,EAA6B;AAC9B,SAAK,CAAL,IAAUD,CAAV;AACA,SAAK,CAAL,IAAUC,CAAV;AACA,WAAO,KAAKM,KAAL,EAAP;AACD;;AAEDH,EAAAA,IAAI,CAACI,KAAD,EAAsC;AACxC,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAO,KAAKD,KAAL,EAAP;AACD;;AAEDE,EAAAA,UAAU,CAACC,MAAD,EAAuC;AAC/C,QAAIjB,MAAM,CAACY,KAAX,EAAkB;AAChBV,MAAAA,WAAW,CAACe,MAAM,CAACV,CAAR,CAAX;AACAL,MAAAA,WAAW,CAACe,MAAM,CAACT,CAAR,CAAX;AACD;;AACD,SAAK,CAAL,IAAUS,MAAM,CAACV,CAAjB;AACA,SAAK,CAAL,IAAUU,MAAM,CAACT,CAAjB;AACA,WAAO,KAAKM,KAAL,EAAP;AACD;;AAEDI,EAAAA,QAAQ,CAACD,MAAD,EAA2D;AACjEA,IAAAA,MAAM,CAACV,CAAP,GAAW,KAAK,CAAL,CAAX;AACAU,IAAAA,MAAM,CAACT,CAAP,GAAW,KAAK,CAAL,CAAX;AACA,WAAOS,MAAP;AACD;;AAIW,MAARE,QAAQ,GAAW;AACrB,WAAO,CAAP;AACD;;AAMDC,EAAAA,eAAe,GAAW;AACxB,WAAOC,IAAI,CAACC,KAAL,CAAW,KAAKd,CAAhB,EAAmB,KAAKD,CAAxB,CAAP;AACD;;AAMDgB,EAAAA,aAAa,GAAW;AACtB,WAAOF,IAAI,CAACC,KAAL,CAAW,KAAKf,CAAhB,EAAmB,KAAKC,CAAxB,CAAP;AACD;;AASDgB,EAAAA,SAAS,CAACC,OAAD,EAAwC;AAC/C,WAAO,KAAKC,gBAAL,CAAsBD,OAAtB,CAAP;AACD;;AAODC,EAAAA,gBAAgB,CAACD,OAAD,EAAwC;AACtDtB,IAAAA,IAAI,CAACwB,aAAL,CAAmB,IAAnB,EAAyB,IAAzB,EAA+BF,OAA/B;AACA,WAAO,KAAKX,KAAL,EAAP;AACD;;AAODc,EAAAA,iBAAiB,CAACH,OAAD,EAAwC;AACvDrB,IAAAA,0BAA0B,CAAC,IAAD,EAAO,IAAP,EAAaqB,OAAb,CAA1B;AACA,WAAO,KAAKX,KAAL,EAAP;AACD;;AAEDe,EAAAA,kBAAkB,CAACC,OAAD,EAAwC;AACxD3B,IAAAA,IAAI,CAAC4B,aAAL,CAAmB,IAAnB,EAAyB,IAAzB,EAA+BD,OAA/B;AACA,WAAO,KAAKhB,KAAL,EAAP;AACD;;AAEDkB,EAAAA,oBAAoB,CAACC,SAAD,EAA0C;AAC5D9B,IAAAA,IAAI,CAAC+B,cAAL,CAAoB,IAApB,EAA0B,IAA1B,EAAgCD,SAAhC;AACA,WAAO,KAAKnB,KAAL,EAAP;AACD;;AAEDqB,EAAAA,kBAAkB,CAACC,OAAD,EAAwC;AACxDjC,IAAAA,IAAI,CAACkC,aAAL,CAAmB,IAAnB,EAAyB,IAAzB,EAA+BD,OAA/B;AACA,WAAO,KAAKtB,KAAL,EAAP;AACD;;AA/GyC", "sourcesContent": ["// Copyright (c) 2017 Uber Technologies, Inc.\n// MIT License\nimport Vector from './base/vector';\nimport {config, isArray} from '../lib/common';\nimport {checkNumber} from '../lib/validators';\nimport * as vec2 from 'gl-matrix/vec2';\n/* eslint-disable camelcase */\nimport {vec2_transformMat4AsVector} from '../lib/gl-matrix-extras';\nimport {NumericArray} from '@math.gl/types';\n\n/**\n * Two-element vector class.\n * Subclass of Array<number>\n */\nexport default class Vector2 extends Vector {\n  // Creates a new, empty vec2\n  constructor(x: number | Readonly<NumericArray> = 0, y: number = 0) {\n    // PERF NOTE: initialize elements as double precision numbers\n    super(2); // -0, -0);\n    if (isArray(x) && arguments.length === 1) {\n      this.copy(x as Readonly<NumericArray>);\n    } else {\n      if (config.debug) {\n        checkNumber(x);\n        checkNumber(y);\n      }\n      this[0] = x as number;\n      this[1] = y;\n    }\n  }\n\n  set(x: number, y: number): this {\n    this[0] = x;\n    this[1] = y;\n    return this.check();\n  }\n\n  copy(array: Readonly<NumericArray>): this {\n    this[0] = array[0];\n    this[1] = array[1];\n    return this.check();\n  }\n\n  fromObject(object: {x: number; y: number}): this {\n    if (config.debug) {\n      checkNumber(object.x);\n      checkNumber(object.y);\n    }\n    this[0] = object.x;\n    this[1] = object.y;\n    return this.check();\n  }\n\n  toObject(object: {x?: number; y?: number}): {x: number; y: number} {\n    object.x = this[0];\n    object.y = this[1];\n    return object as {x: number; y: number};\n  }\n\n  // Getters/setters\n\n  get ELEMENTS(): number {\n    return 2;\n  }\n\n  /**\n   * Returns angle from x axis\n   * @returns\n   */\n  horizontalAngle(): number {\n    return Math.atan2(this.y, this.x);\n  }\n\n  /**\n   * Returns angle from y axis\n   * @returns\n   */\n  verticalAngle(): number {\n    return Math.atan2(this.x, this.y);\n  }\n\n  // Transforms\n\n  /**\n   * Transforms as point\n   * @param matrix4\n   * @returns\n   */\n  transform(matrix4: Readonly<NumericArray>): this {\n    return this.transformAsPoint(matrix4);\n  }\n\n  /**\n   * transforms as point (4th component is implicitly 1)\n   * @param matrix4\n   * @returns\n   */\n  transformAsPoint(matrix4: Readonly<NumericArray>): this {\n    vec2.transformMat4(this, this, matrix4);\n    return this.check();\n  }\n\n  /**\n   * transforms as vector (4th component is implicitly 0, ignores translation. slightly faster)\n   * @param matrix4\n   * @returns\n   */\n  transformAsVector(matrix4: Readonly<NumericArray>): this {\n    vec2_transformMat4AsVector(this, this, matrix4);\n    return this.check();\n  }\n\n  transformByMatrix3(matrix3: Readonly<NumericArray>): this {\n    vec2.transformMat3(this, this, matrix3);\n    return this.check();\n  }\n\n  transformByMatrix2x3(matrix2x3: Readonly<NumericArray>): this {\n    vec2.transformMat2d(this, this, matrix2x3);\n    return this.check();\n  }\n\n  transformByMatrix2(matrix2: Readonly<NumericArray>): this {\n    vec2.transformMat2(this, this, matrix2);\n    return this.check();\n  }\n}\n"], "file": "vector2.js"}