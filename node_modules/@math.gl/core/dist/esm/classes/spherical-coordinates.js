import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
import Vector3 from './vector3';
import { formatValue, equals, config } from '../lib/common';
import { degrees, radians, clamp } from '../lib/common';
import * as vec3 from 'gl-matrix/vec3';
const EPSILON = 0.000001;
const EARTH_RADIUS_METERS = 6371000;
export default class SphericalCoordinates {
  constructor({
    phi = 0,
    theta = 0,
    radius = 1,
    bearing,
    pitch,
    altitude,
    radiusScale = EARTH_RADIUS_METERS
  } = {}) {
    _defineProperty(this, "phi", void 0);

    _defineProperty(this, "theta", void 0);

    _defineProperty(this, "radius", void 0);

    _defineProperty(this, "radiusScale", void 0);

    this.phi = phi;
    this.theta = theta;
    this.radius = radius || altitude || 1;
    this.radiusScale = radiusScale || 1;

    if (bearing !== undefined) {
      this.bearing = bearing;
    }

    if (pitch !== undefined) {
      this.pitch = pitch;
    }

    this.check();
  }

  toString() {
    return this.formatString(config);
  }

  formatString({
    printTypes = false
  }) {
    const f = formatValue;
    return "".concat(printTypes ? 'Spherical' : '', "[rho:").concat(f(this.radius), ",theta:").concat(f(this.theta), ",phi:").concat(f(this.phi), "]");
  }

  equals(other) {
    return equals(this.radius, other.radius) && equals(this.theta, other.theta) && equals(this.phi, other.phi);
  }

  exactEquals(other) {
    return this.radius === other.radius && this.theta === other.theta && this.phi === other.phi;
  }

  get bearing() {
    return 180 - degrees(this.phi);
  }

  set bearing(v) {
    this.phi = Math.PI - radians(v);
  }

  get pitch() {
    return degrees(this.theta);
  }

  set pitch(v) {
    this.theta = radians(v);
  }

  get longitude() {
    return degrees(this.phi);
  }

  get latitude() {
    return degrees(this.theta);
  }

  get lng() {
    return degrees(this.phi);
  }

  get lat() {
    return degrees(this.theta);
  }

  get z() {
    return (this.radius - 1) * this.radiusScale;
  }

  set(radius, phi, theta) {
    this.radius = radius;
    this.phi = phi;
    this.theta = theta;
    return this.check();
  }

  clone() {
    return new SphericalCoordinates().copy(this);
  }

  copy(other) {
    this.radius = other.radius;
    this.phi = other.phi;
    this.theta = other.theta;
    return this.check();
  }

  fromLngLatZ([lng, lat, z]) {
    this.radius = 1 + z / this.radiusScale;
    this.phi = radians(lat);
    this.theta = radians(lng);
    return this.check();
  }

  fromVector3(v) {
    this.radius = vec3.length(v);

    if (this.radius > 0) {
      this.theta = Math.atan2(v[0], v[1]);
      this.phi = Math.acos(clamp(v[2] / this.radius, -1, 1));
    }

    return this.check();
  }

  toVector3() {
    return new Vector3(0, 0, this.radius).rotateX({
      radians: this.theta
    }).rotateZ({
      radians: this.phi
    });
  }

  makeSafe() {
    this.phi = Math.max(EPSILON, Math.min(Math.PI - EPSILON, this.phi));
    return this;
  }

  check() {
    if (!Number.isFinite(this.phi) || !Number.isFinite(this.theta) || !(this.radius > 0)) {
      throw new Error('SphericalCoordinates: some fields set to invalid numbers');
    }

    return this;
  }

}
//# sourceMappingURL=spherical-coordinates.js.map