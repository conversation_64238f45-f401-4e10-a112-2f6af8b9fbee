{"version": 3, "sources": ["../../../src/classes/euler.ts"], "names": ["MathArray", "Quaternion", "clamp", "checkNumber", "ERR_UNKNOWN_ORDER", "ALMOST_ONE", "RotationOrder", "<PERSON>uler", "ZYX", "YXZ", "XZY", "ZXY", "YZX", "XYZ", "RollPitchYaw", "DefaultOrder", "RotationOrders", "rotationOrder", "order", "ELEMENTS", "constructor", "x", "y", "z", "arguments", "length", "Array", "isArray", "fromVector3", "set", "fromQuaternion", "quaternion", "w", "ysqr", "t0", "t1", "t2", "t3", "t4", "roll", "Math", "atan2", "pitch", "asin", "yaw", "fromObject", "object", "Error", "copy", "array", "Number", "isFinite", "check", "validate", "validateOrder", "toArray", "offset", "toArray4", "toVector3", "result", "value", "alpha", "beta", "gamma", "phi", "theta", "psi", "checkOrder", "v", "fromArray", "undefined", "fromRollPitchYaw", "fromRotationMatrix", "m", "_fromRotationMatrix", "getRotationMatrix", "_getRotationMatrix", "getQuaternion", "q", "rotateX", "rotateY", "rotateZ", "m11", "m12", "m13", "m21", "m22", "m23", "m31", "m32", "m33", "abs", "te", "a", "cos", "c", "e", "b", "sin", "d", "f", "ae", "af", "be", "bf", "ce", "cf", "de", "df", "ac", "ad", "bc", "bd", "toQuaternion", "cy", "sy", "cr", "sr", "cp", "sp"], "mappings": "AAEA,OAAOA,SAAP,MAAsB,mBAAtB;AACA,OAAOC,UAAP,MAAuB,cAAvB;AAGA,SAAQC,KAAR,QAAoB,eAApB;AACA,SAAQC,WAAR,QAA0B,mBAA1B;AAGA,MAAMC,iBAAiB,GAAG,2BAA1B;AACA,MAAMC,UAAU,GAAG,OAAnB;IAEKC,a;;WAAAA,a;AAAAA,EAAAA,a,CAAAA,a;AAAAA,EAAAA,a,CAAAA,a;AAAAA,EAAAA,a,CAAAA,a;AAAAA,EAAAA,a,CAAAA,a;AAAAA,EAAAA,a,CAAAA,a;AAAAA,EAAAA,a,CAAAA,a;GAAAA,a,KAAAA,a;;AASL,eAAe,MAAMC,KAAN,SAAoBP,SAApB,CAA8B;AAE7B,aAAHQ,GAAG,GAAkB;AAC9B,WAAOF,aAAa,CAACE,GAArB;AACD;;AACa,aAAHC,GAAG,GAAkB;AAC9B,WAAOH,aAAa,CAACG,GAArB;AACD;;AACa,aAAHC,GAAG,GAAkB;AAC9B,WAAOJ,aAAa,CAACI,GAArB;AACD;;AACa,aAAHC,GAAG,GAAkB;AAC9B,WAAOL,aAAa,CAACK,GAArB;AACD;;AACa,aAAHC,GAAG,GAAkB;AAC9B,WAAON,aAAa,CAACM,GAArB;AACD;;AACa,aAAHC,GAAG,GAAkB;AAC9B,WAAOP,aAAa,CAACO,GAArB;AACD;;AACsB,aAAZC,YAAY,GAAkB;AACvC,WAAOR,aAAa,CAACE,GAArB;AACD;;AACsB,aAAZO,YAAY,GAAkB;AACvC,WAAOT,aAAa,CAACE,GAArB;AACD;;AACwB,aAAdQ,cAAc,GAAyB;AAChD,WAAOV,aAAP;AACD;;AACmB,SAAbW,aAAa,CAACC,KAAD,EAA+B;AACjD,WAAOZ,aAAa,CAACY,KAAD,CAApB;AACD;;AACW,MAARC,QAAQ,GAAW;AACrB,WAAO,CAAP;AACD;;AASDC,EAAAA,WAAW,CAACC,CAAC,GAAG,CAAL,EAAQC,CAAC,GAAG,CAAZ,EAAeC,CAAC,GAAG,CAAnB,EAAsBL,KAAK,GAAGX,KAAK,CAACQ,YAApC,EAAkD;AAE3D,UAAM,CAAC,CAAP,EAAU,CAAC,CAAX,EAAc,CAAC,CAAf,EAAkB,CAAC,CAAnB;;AAEA,QAAIS,SAAS,CAACC,MAAV,GAAmB,CAAnB,IAAwBC,KAAK,CAACC,OAAN,CAAcH,SAAS,CAAC,CAAD,CAAvB,CAA5B,EAAyD;AAGvD,WAAKI,WAAL,CAAiB,GAAGJ,SAApB;AACD,KAJD,MAIO;AACL,WAAKK,GAAL,CAASR,CAAT,EAAYC,CAAZ,EAAeC,CAAf,EAAkBL,KAAlB;AACD;AACF;;AAEDY,EAAAA,cAAc,CAACC,UAAD,EAA2C;AACvD,UAAM,CAACV,CAAD,EAAIC,CAAJ,EAAOC,CAAP,EAAUS,CAAV,IAAeD,UAArB;AACA,UAAME,IAAI,GAAGX,CAAC,GAAGA,CAAjB;AACA,UAAMY,EAAE,GAAG,CAAC,CAAD,IAAMD,IAAI,GAAGV,CAAC,GAAGA,CAAjB,IAAsB,CAAjC;AACA,UAAMY,EAAE,GAAG,CAAC,CAAD,IAAMd,CAAC,GAAGC,CAAJ,GAAQU,CAAC,GAAGT,CAAlB,CAAX;AACA,QAAIa,EAAE,GAAG,CAAC,CAAD,IAAMf,CAAC,GAAGE,CAAJ,GAAQS,CAAC,GAAGV,CAAlB,CAAT;AACA,UAAMe,EAAE,GAAG,CAAC,CAAD,IAAMf,CAAC,GAAGC,CAAJ,GAAQS,CAAC,GAAGX,CAAlB,CAAX;AACA,UAAMiB,EAAE,GAAG,CAAC,CAAD,IAAMjB,CAAC,GAAGA,CAAJ,GAAQY,IAAd,IAAsB,CAAjC;AACAG,IAAAA,EAAE,GAAGA,EAAE,GAAG,CAAL,GAAS,CAAT,GAAaA,EAAlB;AACAA,IAAAA,EAAE,GAAGA,EAAE,GAAG,CAAC,CAAN,GAAU,CAAC,CAAX,GAAeA,EAApB;AACA,UAAMG,IAAI,GAAGC,IAAI,CAACC,KAAL,CAAWJ,EAAX,EAAeC,EAAf,CAAb;AACA,UAAMI,KAAK,GAAGF,IAAI,CAACG,IAAL,CAAUP,EAAV,CAAd;AACA,UAAMQ,GAAG,GAAGJ,IAAI,CAACC,KAAL,CAAWN,EAAX,EAAeD,EAAf,CAAZ;AACA,WAAO,KAAKL,GAAL,CAASU,IAAT,EAAeG,KAAf,EAAsBE,GAAtB,EAA2BrC,KAAK,CAACO,YAAjC,CAAP;AACD;;AAED+B,EAAAA,UAAU,CAACC,MAAD,EAAuB;AAC/B,UAAM,IAAIC,KAAJ,CAAU,iBAAV,CAAN;AAED;;AAODC,EAAAA,IAAI,CAACC,KAAD,EAAsC;AACxC,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AAEA,SAAK,CAAL,IAAUC,MAAM,CAACC,QAAP,CAAgBF,KAAK,CAAC,CAAD,CAArB,KAA6B,KAAK/B,KAA5C;AACA,WAAO,KAAKkC,KAAL,EAAP;AACD;;AAIDvB,EAAAA,GAAG,CAACR,CAAC,GAAG,CAAL,EAAQC,CAAC,GAAG,CAAZ,EAAeC,CAAC,GAAG,CAAnB,EAAsBL,KAAtB,EAAkD;AACnD,SAAK,CAAL,IAAUG,CAAV;AACA,SAAK,CAAL,IAAUC,CAAV;AACA,SAAK,CAAL,IAAUC,CAAV;AACA,SAAK,CAAL,IAAU2B,MAAM,CAACC,QAAP,CAAgBjC,KAAhB,IAAyBA,KAAzB,GAAiC,KAAK,CAAL,CAA3C;AACA,WAAO,KAAKkC,KAAL,EAAP;AACD;;AAEDC,EAAAA,QAAQ,GAAY;AAClB,WACEC,aAAa,CAAC,KAAK,CAAL,CAAD,CAAb,IACAJ,MAAM,CAACC,QAAP,CAAgB,KAAK,CAAL,CAAhB,CADA,IAEAD,MAAM,CAACC,QAAP,CAAgB,KAAK,CAAL,CAAhB,CAFA,IAGAD,MAAM,CAACC,QAAP,CAAgB,KAAK,CAAL,CAAhB,CAJF;AAMD;;AAGDI,EAAAA,OAAO,CAACN,KAAmB,GAAG,EAAvB,EAA2BO,MAAc,GAAG,CAA5C,EAA6D;AAClEP,IAAAA,KAAK,CAACO,MAAD,CAAL,GAAgB,KAAK,CAAL,CAAhB;AACAP,IAAAA,KAAK,CAACO,MAAM,GAAG,CAAV,CAAL,GAAoB,KAAK,CAAL,CAApB;AACAP,IAAAA,KAAK,CAACO,MAAM,GAAG,CAAV,CAAL,GAAoB,KAAK,CAAL,CAApB;AACA,WAAOP,KAAP;AACD;;AAGDQ,EAAAA,QAAQ,CAACR,KAAmB,GAAG,EAAvB,EAA2BO,MAAc,GAAG,CAA5C,EAA6D;AACnEP,IAAAA,KAAK,CAACO,MAAD,CAAL,GAAgB,KAAK,CAAL,CAAhB;AACAP,IAAAA,KAAK,CAACO,MAAM,GAAG,CAAV,CAAL,GAAoB,KAAK,CAAL,CAApB;AACAP,IAAAA,KAAK,CAACO,MAAM,GAAG,CAAV,CAAL,GAAoB,KAAK,CAAL,CAApB;AACAP,IAAAA,KAAK,CAACO,MAAM,GAAG,CAAV,CAAL,GAAoB,KAAK,CAAL,CAApB;AACA,WAAOP,KAAP;AACD;;AAEDS,EAAAA,SAAS,CAACC,MAAoB,GAAG,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,EAAS,CAAC,CAAV,CAAxB,EAAoD;AAC3DA,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,CAAZ;AACAA,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,CAAZ;AACAA,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,CAAZ;AACA,WAAOA,MAAP;AACD;;AAII,MAADtC,CAAC,GAAW;AACd,WAAO,KAAK,CAAL,CAAP;AACD;;AACI,MAADA,CAAC,CAACuC,KAAD,EAAgB;AACnB,SAAK,CAAL,IAAUzD,WAAW,CAACyD,KAAD,CAArB;AACD;;AAEI,MAADtC,CAAC,GAAW;AACd,WAAO,KAAK,CAAL,CAAP;AACD;;AACI,MAADA,CAAC,CAACsC,KAAD,EAAgB;AACnB,SAAK,CAAL,IAAUzD,WAAW,CAACyD,KAAD,CAArB;AACD;;AAEI,MAADrC,CAAC,GAAW;AACd,WAAO,KAAK,CAAL,CAAP;AACD;;AACI,MAADA,CAAC,CAACqC,KAAD,EAAgB;AACnB,SAAK,CAAL,IAAUzD,WAAW,CAACyD,KAAD,CAArB;AACD;;AAEQ,MAALC,KAAK,GAAW;AAClB,WAAO,KAAK,CAAL,CAAP;AACD;;AACQ,MAALA,KAAK,CAACD,KAAD,EAAgB;AACvB,SAAK,CAAL,IAAUzD,WAAW,CAACyD,KAAD,CAArB;AACD;;AAEO,MAAJE,IAAI,GAAW;AACjB,WAAO,KAAK,CAAL,CAAP;AACD;;AACO,MAAJA,IAAI,CAACF,KAAD,EAAgB;AACtB,SAAK,CAAL,IAAUzD,WAAW,CAACyD,KAAD,CAArB;AACD;;AAEQ,MAALG,KAAK,GAAW;AAClB,WAAO,KAAK,CAAL,CAAP;AACD;;AACQ,MAALA,KAAK,CAACH,KAAD,EAAgB;AACvB,SAAK,CAAL,IAAUzD,WAAW,CAACyD,KAAD,CAArB;AACD;;AAGM,MAAHI,GAAG,GAAW;AAChB,WAAO,KAAK,CAAL,CAAP;AACD;;AACM,MAAHA,GAAG,CAACJ,KAAD,EAAgB;AACrB,SAAK,CAAL,IAAUzD,WAAW,CAACyD,KAAD,CAArB;AACD;;AAEQ,MAALK,KAAK,GAAW;AAClB,WAAO,KAAK,CAAL,CAAP;AACD;;AACQ,MAALA,KAAK,CAACL,KAAD,EAAgB;AACvB,SAAK,CAAL,IAAUzD,WAAW,CAACyD,KAAD,CAArB;AACD;;AAEM,MAAHM,GAAG,GAAW;AAChB,WAAO,KAAK,CAAL,CAAP;AACD;;AACM,MAAHA,GAAG,CAACN,KAAD,EAAgB;AACrB,SAAK,CAAL,IAAUzD,WAAW,CAACyD,KAAD,CAArB;AACD;;AAGO,MAAJrB,IAAI,GAAW;AACjB,WAAO,KAAK,CAAL,CAAP;AACD;;AACO,MAAJA,IAAI,CAACqB,KAAD,EAAgB;AACtB,SAAK,CAAL,IAAUzD,WAAW,CAACyD,KAAD,CAArB;AACD;;AAEQ,MAALlB,KAAK,GAAW;AAClB,WAAO,KAAK,CAAL,CAAP;AACD;;AACQ,MAALA,KAAK,CAACkB,KAAD,EAAgB;AACvB,SAAK,CAAL,IAAUzD,WAAW,CAACyD,KAAD,CAArB;AACD;;AAEM,MAAHhB,GAAG,GAAW;AAChB,WAAO,KAAK,CAAL,CAAP;AACD;;AACM,MAAHA,GAAG,CAACgB,KAAD,EAAgB;AACrB,SAAK,CAAL,IAAUzD,WAAW,CAACyD,KAAD,CAArB;AACD;;AAGQ,MAAL1C,KAAK,GAAkB;AACzB,WAAO,KAAK,CAAL,CAAP;AACD;;AACQ,MAALA,KAAK,CAAC0C,KAAD,EAAuB;AAC9B,SAAK,CAAL,IAAUO,UAAU,CAACP,KAAD,CAApB;AACD;;AAGDhC,EAAAA,WAAW,CAACwC,CAAD,EAA4BlD,KAA5B,EAAwD;AACjE,WAAO,KAAKW,GAAL,CAASuC,CAAC,CAAC,CAAD,CAAV,EAAeA,CAAC,CAAC,CAAD,CAAhB,EAAqBA,CAAC,CAAC,CAAD,CAAtB,EAA2BlB,MAAM,CAACC,QAAP,CAAgBjC,KAAhB,IAAyBA,KAAzB,GAAiC,KAAK,CAAL,CAA5D,CAAP;AACD;;AAGDmD,EAAAA,SAAS,CAACpB,KAAD,EAAgCO,MAAc,GAAG,CAAjD,EAA0D;AACjE,SAAK,CAAL,IAAUP,KAAK,CAAC,IAAIO,MAAL,CAAf;AACA,SAAK,CAAL,IAAUP,KAAK,CAAC,IAAIO,MAAL,CAAf;AACA,SAAK,CAAL,IAAUP,KAAK,CAAC,IAAIO,MAAL,CAAf;;AACA,QAAIP,KAAK,CAAC,CAAD,CAAL,KAAaqB,SAAjB,EAA4B;AAC1B,WAAK,CAAL,IAAUrB,KAAK,CAAC,CAAD,CAAf;AACD;;AACD,WAAO,KAAKG,KAAL,EAAP;AACD;;AAGDmB,EAAAA,gBAAgB,CAAChC,IAAD,EAAeG,KAAf,EAA8BE,GAA9B,EAAiD;AAC/D,WAAO,KAAKf,GAAL,CAASU,IAAT,EAAeG,KAAf,EAAsBE,GAAtB,EAA2BtC,aAAa,CAACE,GAAzC,CAAP;AACD;;AAEDgE,EAAAA,kBAAkB,CAACC,CAAD,EAA4BvD,KAAoB,GAAGX,KAAK,CAACQ,YAAzD,EAA6E;AAC7F,SAAK2D,mBAAL,CAAyBD,CAAzB,EAA4BvD,KAA5B;;AACA,WAAO,KAAKkC,KAAL,EAAP;AACD;;AAIDuB,EAAAA,iBAAiB,CAACF,CAAD,EAAgC;AAC/C,WAAO,KAAKG,kBAAL,CAAwBH,CAAxB,CAAP;AACD;;AAGDI,EAAAA,aAAa,GAAe;AAC1B,UAAMC,CAAC,GAAG,IAAI7E,UAAJ,EAAV;;AACA,YAAQ,KAAK,CAAL,CAAR;AACE,WAAKK,aAAa,CAACO,GAAnB;AACE,eAAOiE,CAAC,CAACC,OAAF,CAAU,KAAK,CAAL,CAAV,EAAmBC,OAAnB,CAA2B,KAAK,CAAL,CAA3B,EAAoCC,OAApC,CAA4C,KAAK,CAAL,CAA5C,CAAP;;AACF,WAAK3E,aAAa,CAACG,GAAnB;AACE,eAAOqE,CAAC,CAACE,OAAF,CAAU,KAAK,CAAL,CAAV,EAAmBD,OAAnB,CAA2B,KAAK,CAAL,CAA3B,EAAoCE,OAApC,CAA4C,KAAK,CAAL,CAA5C,CAAP;;AACF,WAAK3E,aAAa,CAACK,GAAnB;AACE,eAAOmE,CAAC,CAACG,OAAF,CAAU,KAAK,CAAL,CAAV,EAAmBF,OAAnB,CAA2B,KAAK,CAAL,CAA3B,EAAoCC,OAApC,CAA4C,KAAK,CAAL,CAA5C,CAAP;;AACF,WAAK1E,aAAa,CAACE,GAAnB;AACE,eAAOsE,CAAC,CAACG,OAAF,CAAU,KAAK,CAAL,CAAV,EAAmBD,OAAnB,CAA2B,KAAK,CAAL,CAA3B,EAAoCD,OAApC,CAA4C,KAAK,CAAL,CAA5C,CAAP;;AACF,WAAKzE,aAAa,CAACM,GAAnB;AACE,eAAOkE,CAAC,CAACE,OAAF,CAAU,KAAK,CAAL,CAAV,EAAmBC,OAAnB,CAA2B,KAAK,CAAL,CAA3B,EAAoCF,OAApC,CAA4C,KAAK,CAAL,CAA5C,CAAP;;AACF,WAAKzE,aAAa,CAACI,GAAnB;AACE,eAAOoE,CAAC,CAACC,OAAF,CAAU,KAAK,CAAL,CAAV,EAAmBE,OAAnB,CAA2B,KAAK,CAAL,CAA3B,EAAoCD,OAApC,CAA4C,KAAK,CAAL,CAA5C,CAAP;;AACF;AACE,cAAM,IAAIjC,KAAJ,CAAU3C,iBAAV,CAAN;AAdJ;AAgBD;;AAUDsE,EAAAA,mBAAmB,CAACD,CAAD,EAA4BvD,KAAK,GAAGX,KAAK,CAACQ,YAA1C,EAA8D;AAE/E,UAAMmE,GAAG,GAAGT,CAAC,CAAC,CAAD,CAAb;AAAA,UACEU,GAAG,GAAGV,CAAC,CAAC,CAAD,CADT;AAAA,UAEEW,GAAG,GAAGX,CAAC,CAAC,CAAD,CAFT;AAGA,UAAMY,GAAG,GAAGZ,CAAC,CAAC,CAAD,CAAb;AAAA,UACEa,GAAG,GAAGb,CAAC,CAAC,CAAD,CADT;AAAA,UAEEc,GAAG,GAAGd,CAAC,CAAC,CAAD,CAFT;AAGA,UAAMe,GAAG,GAAGf,CAAC,CAAC,CAAD,CAAb;AAAA,UACEgB,GAAG,GAAGhB,CAAC,CAAC,CAAD,CADT;AAAA,UAEEiB,GAAG,GAAGjB,CAAC,CAAC,EAAD,CAFT;AAGAvD,IAAAA,KAAK,GAAGA,KAAK,IAAI,KAAK,CAAL,CAAjB;;AACA,YAAQA,KAAR;AACE,WAAKX,KAAK,CAACM,GAAX;AACE,aAAK,CAAL,IAAU2B,IAAI,CAACG,IAAL,CAAUzC,KAAK,CAACkF,GAAD,EAAM,CAAC,CAAP,EAAU,CAAV,CAAf,CAAV;;AACA,YAAI5C,IAAI,CAACmD,GAAL,CAASP,GAAT,IAAgB/E,UAApB,EAAgC;AAC9B,eAAK,CAAL,IAAUmC,IAAI,CAACC,KAAL,CAAW,CAAC8C,GAAZ,EAAiBG,GAAjB,CAAV;AACA,eAAK,CAAL,IAAUlD,IAAI,CAACC,KAAL,CAAW,CAAC0C,GAAZ,EAAiBD,GAAjB,CAAV;AACD,SAHD,MAGO;AACL,eAAK,CAAL,IAAU1C,IAAI,CAACC,KAAL,CAAWgD,GAAX,EAAgBH,GAAhB,CAAV;AACA,eAAK,CAAL,IAAU,CAAV;AACD;;AACD;;AACF,WAAK/E,KAAK,CAACE,GAAX;AACE,aAAK,CAAL,IAAU+B,IAAI,CAACG,IAAL,CAAU,CAACzC,KAAK,CAACqF,GAAD,EAAM,CAAC,CAAP,EAAU,CAAV,CAAhB,CAAV;;AACA,YAAI/C,IAAI,CAACmD,GAAL,CAASJ,GAAT,IAAgBlF,UAApB,EAAgC;AAC9B,eAAK,CAAL,IAAUmC,IAAI,CAACC,KAAL,CAAW2C,GAAX,EAAgBM,GAAhB,CAAV;AACA,eAAK,CAAL,IAAUlD,IAAI,CAACC,KAAL,CAAW4C,GAAX,EAAgBC,GAAhB,CAAV;AACD,SAHD,MAGO;AACL,eAAK,CAAL,IAAU9C,IAAI,CAACC,KAAL,CAAW,CAAC+C,GAAZ,EAAiBN,GAAjB,CAAV;AACA,eAAK,CAAL,IAAU,CAAV;AACD;;AACD;;AACF,WAAK3E,KAAK,CAACI,GAAX;AACE,aAAK,CAAL,IAAU6B,IAAI,CAACG,IAAL,CAAUzC,KAAK,CAACuF,GAAD,EAAM,CAAC,CAAP,EAAU,CAAV,CAAf,CAAV;;AACA,YAAIjD,IAAI,CAACmD,GAAL,CAASF,GAAT,IAAgBpF,UAApB,EAAgC;AAC9B,eAAK,CAAL,IAAUmC,IAAI,CAACC,KAAL,CAAW,CAAC+C,GAAZ,EAAiBE,GAAjB,CAAV;AACA,eAAK,CAAL,IAAUlD,IAAI,CAACC,KAAL,CAAW,CAAC0C,GAAZ,EAAiBG,GAAjB,CAAV;AACD,SAHD,MAGO;AACL,eAAK,CAAL,IAAU,CAAV;AACA,eAAK,CAAL,IAAU9C,IAAI,CAACC,KAAL,CAAW4C,GAAX,EAAgBH,GAAhB,CAAV;AACD;;AACD;;AACF,WAAK3E,KAAK,CAACC,GAAX;AACE,aAAK,CAAL,IAAUgC,IAAI,CAACG,IAAL,CAAU,CAACzC,KAAK,CAACsF,GAAD,EAAM,CAAC,CAAP,EAAU,CAAV,CAAhB,CAAV;;AACA,YAAIhD,IAAI,CAACmD,GAAL,CAASH,GAAT,IAAgBnF,UAApB,EAAgC;AAC9B,eAAK,CAAL,IAAUmC,IAAI,CAACC,KAAL,CAAWgD,GAAX,EAAgBC,GAAhB,CAAV;AACA,eAAK,CAAL,IAAUlD,IAAI,CAACC,KAAL,CAAW4C,GAAX,EAAgBH,GAAhB,CAAV;AACD,SAHD,MAGO;AACL,eAAK,CAAL,IAAU,CAAV;AACA,eAAK,CAAL,IAAU1C,IAAI,CAACC,KAAL,CAAW,CAAC0C,GAAZ,EAAiBG,GAAjB,CAAV;AACD;;AACD;;AACF,WAAK/E,KAAK,CAACK,GAAX;AACE,aAAK,CAAL,IAAU4B,IAAI,CAACG,IAAL,CAAUzC,KAAK,CAACmF,GAAD,EAAM,CAAC,CAAP,EAAU,CAAV,CAAf,CAAV;;AACA,YAAI7C,IAAI,CAACmD,GAAL,CAASN,GAAT,IAAgBhF,UAApB,EAAgC;AAC9B,eAAK,CAAL,IAAUmC,IAAI,CAACC,KAAL,CAAW,CAAC8C,GAAZ,EAAiBD,GAAjB,CAAV;AACA,eAAK,CAAL,IAAU9C,IAAI,CAACC,KAAL,CAAW,CAAC+C,GAAZ,EAAiBN,GAAjB,CAAV;AACD,SAHD,MAGO;AACL,eAAK,CAAL,IAAU,CAAV;AACA,eAAK,CAAL,IAAU1C,IAAI,CAACC,KAAL,CAAW2C,GAAX,EAAgBM,GAAhB,CAAV;AACD;;AACD;;AACF,WAAKnF,KAAK,CAACG,GAAX;AACE,aAAK,CAAL,IAAU8B,IAAI,CAACG,IAAL,CAAU,CAACzC,KAAK,CAACiF,GAAD,EAAM,CAAC,CAAP,EAAU,CAAV,CAAhB,CAAV;;AACA,YAAI3C,IAAI,CAACmD,GAAL,CAASR,GAAT,IAAgB9E,UAApB,EAAgC;AAC9B,eAAK,CAAL,IAAUmC,IAAI,CAACC,KAAL,CAAWgD,GAAX,EAAgBH,GAAhB,CAAV;AACA,eAAK,CAAL,IAAU9C,IAAI,CAACC,KAAL,CAAW2C,GAAX,EAAgBF,GAAhB,CAAV;AACD,SAHD,MAGO;AACL,eAAK,CAAL,IAAU1C,IAAI,CAACC,KAAL,CAAW,CAAC8C,GAAZ,EAAiBG,GAAjB,CAAV;AACA,eAAK,CAAL,IAAU,CAAV;AACD;;AACD;;AACF;AACE,cAAM,IAAI3C,KAAJ,CAAU3C,iBAAV,CAAN;AA9DJ;;AAgEA,SAAK,CAAL,IAAUc,KAAV;AACA,WAAO,IAAP;AACD;;AAED0D,EAAAA,kBAAkB,CAACjB,MAAD,EAAqC;AACrD,UAAMiC,EAAE,GAAGjC,MAAM,IAAI,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,EAAS,CAAC,CAAV,EAAa,CAAC,CAAd,EAAiB,CAAC,CAAlB,EAAqB,CAAC,CAAtB,EAAyB,CAAC,CAA1B,EAA6B,CAAC,CAA9B,EAAiC,CAAC,CAAlC,EAAqC,CAAC,CAAtC,EAAyC,CAAC,CAA1C,EAA6C,CAAC,CAA9C,EAAiD,CAAC,CAAlD,EAAqD,CAAC,CAAtD,EAAyD,CAAC,CAA1D,EAA6D,CAAC,CAA9D,CAArB;AACA,UAAMtC,CAAC,GAAG,KAAKA,CAAf;AAAA,UACEC,CAAC,GAAG,KAAKA,CADX;AAAA,UAEEC,CAAC,GAAG,KAAKA,CAFX;AAGA,UAAMsE,CAAC,GAAGrD,IAAI,CAACsD,GAAL,CAASzE,CAAT,CAAV;AACA,UAAM0E,CAAC,GAAGvD,IAAI,CAACsD,GAAL,CAASxE,CAAT,CAAV;AACA,UAAM0E,CAAC,GAAGxD,IAAI,CAACsD,GAAL,CAASvE,CAAT,CAAV;AACA,UAAM0E,CAAC,GAAGzD,IAAI,CAAC0D,GAAL,CAAS7E,CAAT,CAAV;AACA,UAAM8E,CAAC,GAAG3D,IAAI,CAAC0D,GAAL,CAAS5E,CAAT,CAAV;AACA,UAAM8E,CAAC,GAAG5D,IAAI,CAAC0D,GAAL,CAAS3E,CAAT,CAAV;;AACA,YAAQ,KAAK,CAAL,CAAR;AACE,WAAKhB,KAAK,CAACM,GAAX;AAAgB;AACd,gBAAMwF,EAAE,GAAGR,CAAC,GAAGG,CAAf;AAAA,gBACEM,EAAE,GAAGT,CAAC,GAAGO,CADX;AAAA,gBAEEG,EAAE,GAAGN,CAAC,GAAGD,CAFX;AAAA,gBAGEQ,EAAE,GAAGP,CAAC,GAAGG,CAHX;AAIAR,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQG,CAAC,GAAGC,CAAZ;AACAJ,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQ,CAACG,CAAD,GAAKK,CAAb;AACAR,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQO,CAAR;AACAP,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQU,EAAE,GAAGC,EAAE,GAAGJ,CAAlB;AACAP,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQS,EAAE,GAAGG,EAAE,GAAGL,CAAlB;AACAP,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQ,CAACK,CAAD,GAAKF,CAAb;AACAH,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQY,EAAE,GAAGH,EAAE,GAAGF,CAAlB;AACAP,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQW,EAAE,GAAGD,EAAE,GAAGH,CAAlB;AACAP,UAAAA,EAAE,CAAC,EAAD,CAAF,GAASC,CAAC,GAAGE,CAAb;AACA;AACD;;AACD,WAAKxF,KAAK,CAACE,GAAX;AAAgB;AACd,gBAAMgG,EAAE,GAAGV,CAAC,GAAGC,CAAf;AAAA,gBACEU,EAAE,GAAGX,CAAC,GAAGK,CADX;AAAA,gBAEEO,EAAE,GAAGR,CAAC,GAAGH,CAFX;AAAA,gBAGEY,EAAE,GAAGT,CAAC,GAAGC,CAHX;AAIAR,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQa,EAAE,GAAGG,EAAE,GAAGX,CAAlB;AACAL,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQe,EAAE,GAAGV,CAAL,GAASS,EAAjB;AACAd,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQC,CAAC,GAAGM,CAAZ;AACAP,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQC,CAAC,GAAGO,CAAZ;AACAR,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQC,CAAC,GAAGG,CAAZ;AACAJ,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQ,CAACK,CAAT;AACAL,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQc,EAAE,GAAGT,CAAL,GAASU,EAAjB;AACAf,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQgB,EAAE,GAAGH,EAAE,GAAGR,CAAlB;AACAL,UAAAA,EAAE,CAAC,EAAD,CAAF,GAASC,CAAC,GAAGE,CAAb;AACA;AACD;;AACD,WAAKxF,KAAK,CAACI,GAAX;AAAgB;AACd,gBAAM8F,EAAE,GAAGV,CAAC,GAAGC,CAAf;AAAA,gBACEU,EAAE,GAAGX,CAAC,GAAGK,CADX;AAAA,gBAEEO,EAAE,GAAGR,CAAC,GAAGH,CAFX;AAAA,gBAGEY,EAAE,GAAGT,CAAC,GAAGC,CAHX;AAIAR,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQa,EAAE,GAAGG,EAAE,GAAGX,CAAlB;AACAL,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQ,CAACC,CAAD,GAAKO,CAAb;AACAR,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQe,EAAE,GAAGD,EAAE,GAAGT,CAAlB;AACAL,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQc,EAAE,GAAGC,EAAE,GAAGV,CAAlB;AACAL,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQC,CAAC,GAAGG,CAAZ;AACAJ,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQgB,EAAE,GAAGH,EAAE,GAAGR,CAAlB;AACAL,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQ,CAACC,CAAD,GAAKM,CAAb;AACAP,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQK,CAAR;AACAL,UAAAA,EAAE,CAAC,EAAD,CAAF,GAASC,CAAC,GAAGE,CAAb;AACA;AACD;;AACD,WAAKxF,KAAK,CAACC,GAAX;AAAgB;AACd,gBAAM6F,EAAE,GAAGR,CAAC,GAAGG,CAAf;AAAA,gBACEM,EAAE,GAAGT,CAAC,GAAGO,CADX;AAAA,gBAEEG,EAAE,GAAGN,CAAC,GAAGD,CAFX;AAAA,gBAGEQ,EAAE,GAAGP,CAAC,GAAGG,CAHX;AAIAR,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQG,CAAC,GAAGC,CAAZ;AACAJ,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQW,EAAE,GAAGJ,CAAL,GAASG,EAAjB;AACAV,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQS,EAAE,GAAGF,CAAL,GAASK,EAAjB;AACAZ,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQG,CAAC,GAAGK,CAAZ;AACAR,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQY,EAAE,GAAGL,CAAL,GAASE,EAAjB;AACAT,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQU,EAAE,GAAGH,CAAL,GAASI,EAAjB;AACAX,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQ,CAACO,CAAT;AACAP,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQK,CAAC,GAAGF,CAAZ;AACAH,UAAAA,EAAE,CAAC,EAAD,CAAF,GAASC,CAAC,GAAGE,CAAb;AACA;AACD;;AACD,WAAKxF,KAAK,CAACK,GAAX;AAAgB;AACd,gBAAMiG,EAAE,GAAGhB,CAAC,GAAGE,CAAf;AAAA,gBACEe,EAAE,GAAGjB,CAAC,GAAGM,CADX;AAAA,gBAEEY,EAAE,GAAGd,CAAC,GAAGF,CAFX;AAAA,gBAGEiB,EAAE,GAAGf,CAAC,GAAGE,CAHX;AAIAP,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQG,CAAC,GAAGC,CAAZ;AACAJ,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQoB,EAAE,GAAGH,EAAE,GAAGT,CAAlB;AACAR,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQmB,EAAE,GAAGX,CAAL,GAASU,EAAjB;AACAlB,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQQ,CAAR;AACAR,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQC,CAAC,GAAGG,CAAZ;AACAJ,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQ,CAACK,CAAD,GAAKD,CAAb;AACAJ,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQ,CAACO,CAAD,GAAKH,CAAb;AACAJ,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQkB,EAAE,GAAGV,CAAL,GAASW,EAAjB;AACAnB,UAAAA,EAAE,CAAC,EAAD,CAAF,GAASiB,EAAE,GAAGG,EAAE,GAAGZ,CAAnB;AACA;AACD;;AACD,WAAK7F,KAAK,CAACG,GAAX;AAAgB;AACd,gBAAMmG,EAAE,GAAGhB,CAAC,GAAGE,CAAf;AAAA,gBACEe,EAAE,GAAGjB,CAAC,GAAGM,CADX;AAAA,gBAEEY,EAAE,GAAGd,CAAC,GAAGF,CAFX;AAAA,gBAGEiB,EAAE,GAAGf,CAAC,GAAGE,CAHX;AAIAP,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQG,CAAC,GAAGC,CAAZ;AACAJ,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQ,CAACQ,CAAT;AACAR,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQO,CAAC,GAAGH,CAAZ;AACAJ,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQiB,EAAE,GAAGT,CAAL,GAASY,EAAjB;AACApB,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQC,CAAC,GAAGG,CAAZ;AACAJ,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQkB,EAAE,GAAGV,CAAL,GAASW,EAAjB;AACAnB,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQmB,EAAE,GAAGX,CAAL,GAASU,EAAjB;AACAlB,UAAAA,EAAE,CAAC,CAAD,CAAF,GAAQK,CAAC,GAAGD,CAAZ;AACAJ,UAAAA,EAAE,CAAC,EAAD,CAAF,GAASoB,EAAE,GAAGZ,CAAL,GAASS,EAAlB;AACA;AACD;;AACD;AACE,cAAM,IAAI9D,KAAJ,CAAU3C,iBAAV,CAAN;AAlGJ;;AAqGAwF,IAAAA,EAAE,CAAC,CAAD,CAAF,GAAQ,CAAR;AACAA,IAAAA,EAAE,CAAC,CAAD,CAAF,GAAQ,CAAR;AACAA,IAAAA,EAAE,CAAC,EAAD,CAAF,GAAS,CAAT;AAEAA,IAAAA,EAAE,CAAC,EAAD,CAAF,GAAS,CAAT;AACAA,IAAAA,EAAE,CAAC,EAAD,CAAF,GAAS,CAAT;AACAA,IAAAA,EAAE,CAAC,EAAD,CAAF,GAAS,CAAT;AACAA,IAAAA,EAAE,CAAC,EAAD,CAAF,GAAS,CAAT;AACA,WAAOA,EAAP;AACD;;AAEDqB,EAAAA,YAAY,GAAe;AAEzB,UAAMC,EAAE,GAAG1E,IAAI,CAACsD,GAAL,CAAS,KAAKlD,GAAL,GAAW,GAApB,CAAX;AACA,UAAMuE,EAAE,GAAG3E,IAAI,CAAC0D,GAAL,CAAS,KAAKtD,GAAL,GAAW,GAApB,CAAX;AACA,UAAMwE,EAAE,GAAG5E,IAAI,CAACsD,GAAL,CAAS,KAAKvD,IAAL,GAAY,GAArB,CAAX;AACA,UAAM8E,EAAE,GAAG7E,IAAI,CAAC0D,GAAL,CAAS,KAAK3D,IAAL,GAAY,GAArB,CAAX;AACA,UAAM+E,EAAE,GAAG9E,IAAI,CAACsD,GAAL,CAAS,KAAKpD,KAAL,GAAa,GAAtB,CAAX;AACA,UAAM6E,EAAE,GAAG/E,IAAI,CAAC0D,GAAL,CAAS,KAAKxD,KAAL,GAAa,GAAtB,CAAX;AACA,UAAMV,CAAC,GAAGkF,EAAE,GAAGE,EAAL,GAAUE,EAAV,GAAeH,EAAE,GAAGE,EAAL,GAAUE,EAAnC;AACA,UAAMlG,CAAC,GAAG6F,EAAE,GAAGG,EAAL,GAAUC,EAAV,GAAeH,EAAE,GAAGC,EAAL,GAAUG,EAAnC;AACA,UAAMjG,CAAC,GAAG4F,EAAE,GAAGE,EAAL,GAAUG,EAAV,GAAeJ,EAAE,GAAGE,EAAL,GAAUC,EAAnC;AACA,UAAM/F,CAAC,GAAG4F,EAAE,GAAGC,EAAL,GAAUE,EAAV,GAAeJ,EAAE,GAAGG,EAAL,GAAUE,EAAnC;AACA,WAAO,IAAItH,UAAJ,CAAeoB,CAAf,EAAkBC,CAAlB,EAAqBC,CAArB,EAAwBS,CAAxB,CAAP;AACD;;AA3f0C;;AAggB7C,SAASsB,aAAT,CAAuBM,KAAvB,EAA+C;AAC7C,SAAOA,KAAK,IAAI,CAAT,IAAcA,KAAK,GAAG,CAA7B;AACD;;AAED,SAASO,UAAT,CAAoBP,KAApB,EAAmC;AACjC,MAAIA,KAAK,GAAG,CAAR,IAAaA,KAAK,IAAI,CAA1B,EAA6B;AAC3B,UAAM,IAAIb,KAAJ,CAAU3C,iBAAV,CAAN;AACD;;AACD,SAAOwD,KAAP;AACD", "sourcesContent": ["// Copyright (c) 2017 Uber Technologies, Inc.\n// MIT License\nimport MathArray from './base/math-array';\nimport Quaternion from './quaternion';\nimport {NumericArray} from '@math.gl/types';\n\nimport {clamp} from '../lib/common';\nimport {checkNumber} from '../lib/validators';\n\n// Internal constants\nconst ERR_UNKNOWN_ORDER = 'Unknown Euler angle order';\nconst ALMOST_ONE = 0.99999;\n\nenum RotationOrder {\n  ZYX = 0,\n  YXZ = 1,\n  XZY = 2,\n  ZXY = 3,\n  YZX = 4,\n  XYZ = 5\n}\n\nexport default class Euler extends MathArray {\n  // Constants\n  static get ZYX(): RotationOrder {\n    return RotationOrder.ZYX;\n  }\n  static get YXZ(): RotationOrder {\n    return RotationOrder.YXZ;\n  }\n  static get XZY(): RotationOrder {\n    return RotationOrder.XZY;\n  }\n  static get ZXY(): RotationOrder {\n    return RotationOrder.ZXY;\n  }\n  static get YZX(): RotationOrder {\n    return RotationOrder.YZX;\n  }\n  static get XYZ(): RotationOrder {\n    return RotationOrder.XYZ;\n  }\n  static get RollPitchYaw(): RotationOrder {\n    return RotationOrder.ZYX;\n  }\n  static get DefaultOrder(): RotationOrder {\n    return RotationOrder.ZYX;\n  }\n  static get RotationOrders(): typeof RotationOrder {\n    return RotationOrder;\n  }\n  static rotationOrder(order: RotationOrder): string {\n    return RotationOrder[order];\n  }\n  get ELEMENTS(): number {\n    return 4;\n  }\n\n  /**\n   * @class\n   * @param {Number | Number[]} x\n   * @param {Number=} [y]\n   * @param {Number=} [z]\n   * @param {Number=} [order]\n   */\n  constructor(x = 0, y = 0, z = 0, order = Euler.DefaultOrder) {\n    // PERF NOTE: initialize elements as double precision numbers\n    super(-0, -0, -0, -0);\n    // eslint-disable-next-line prefer-rest-params\n    if (arguments.length > 0 && Array.isArray(arguments[0])) {\n      // eslint-disable-next-line prefer-rest-params\n      // @ts-expect-error\n      this.fromVector3(...arguments);\n    } else {\n      this.set(x, y, z, order);\n    }\n  }\n\n  fromQuaternion(quaternion: Readonly<NumericArray>): this {\n    const [x, y, z, w] = quaternion;\n    const ysqr = y * y;\n    const t0 = -2 * (ysqr + z * z) + 1;\n    const t1 = +2 * (x * y + w * z);\n    let t2 = -2 * (x * z - w * y);\n    const t3 = +2 * (y * z + w * x);\n    const t4 = -2 * (x * x + ysqr) + 1;\n    t2 = t2 > 1 ? 1 : t2;\n    t2 = t2 < -1 ? -1 : t2;\n    const roll = Math.atan2(t3, t4);\n    const pitch = Math.asin(t2);\n    const yaw = Math.atan2(t1, t0);\n    return this.set(roll, pitch, yaw, Euler.RollPitchYaw);\n  }\n\n  fromObject(object: object): this {\n    throw new Error('not implemented');\n    //  return this.set(object.x, object.y, object.z, object.order);\n  }\n\n  // fromQuaternion(q, order) {\n  //   this._fromRotationMat[-0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0];\n  //   return this.check();\n  // }\n  // If copied array does contain fourth element, preserves currently set order\n  copy(array: Readonly<NumericArray>): this {\n    this[0] = array[0];\n    this[1] = array[1];\n    this[2] = array[2];\n    // @ts-expect-error\n    this[3] = Number.isFinite(array[3]) || this.order;\n    return this.check();\n  }\n\n  // Sets the three angles, and optionally sets the rotation order\n  // If order is not specified, preserves currently set order\n  set(x = 0, y = 0, z = 0, order: RotationOrder): this {\n    this[0] = x;\n    this[1] = y;\n    this[2] = z;\n    this[3] = Number.isFinite(order) ? order : this[3];\n    return this.check();\n  }\n\n  validate(): boolean {\n    return (\n      validateOrder(this[3]) &&\n      Number.isFinite(this[0]) &&\n      Number.isFinite(this[1]) &&\n      Number.isFinite(this[2])\n    );\n  }\n\n  // Does not copy the orientation element\n  toArray(array: NumericArray = [], offset: number = 0): NumericArray {\n    array[offset] = this[0];\n    array[offset + 1] = this[1];\n    array[offset + 2] = this[2];\n    return array;\n  }\n\n  // Copies the orientation element\n  toArray4(array: NumericArray = [], offset: number = 0): NumericArray {\n    array[offset] = this[0];\n    array[offset + 1] = this[1];\n    array[offset + 2] = this[2];\n    array[offset + 3] = this[3];\n    return array;\n  }\n\n  toVector3(result: NumericArray = [-0, -0, -0]): NumericArray {\n    result[0] = this[0];\n    result[1] = this[1];\n    result[2] = this[2];\n    return result;\n  }\n  /* eslint-disable no-multi-spaces, brace-style, no-return-assign */\n  // x, y, z angle notation (note: only corresponds to axis in XYZ orientation)\n\n  get x(): number {\n    return this[0];\n  }\n  set x(value: number) {\n    this[0] = checkNumber(value);\n  }\n\n  get y(): number {\n    return this[1];\n  }\n  set y(value: number) {\n    this[1] = checkNumber(value);\n  }\n\n  get z(): number {\n    return this[2];\n  }\n  set z(value: number) {\n    this[2] = checkNumber(value);\n  }\n  // alpha, beta, gamma angle notation\n  get alpha(): number {\n    return this[0];\n  }\n  set alpha(value: number) {\n    this[0] = checkNumber(value);\n  }\n\n  get beta(): number {\n    return this[1];\n  }\n  set beta(value: number) {\n    this[1] = checkNumber(value);\n  }\n\n  get gamma(): number {\n    return this[2];\n  }\n  set gamma(value: number) {\n    this[2] = checkNumber(value);\n  }\n\n  // phi, theta, psi angle notation\n  get phi(): number {\n    return this[0];\n  }\n  set phi(value: number) {\n    this[0] = checkNumber(value);\n  }\n\n  get theta(): number {\n    return this[1];\n  }\n  set theta(value: number) {\n    this[1] = checkNumber(value);\n  }\n\n  get psi(): number {\n    return this[2];\n  }\n  set psi(value: number) {\n    this[2] = checkNumber(value);\n  }\n  // roll, pitch, yaw angle notation\n\n  get roll(): number {\n    return this[0];\n  }\n  set roll(value: number) {\n    this[0] = checkNumber(value);\n  }\n\n  get pitch(): number {\n    return this[1];\n  }\n  set pitch(value: number) {\n    this[1] = checkNumber(value);\n  }\n\n  get yaw(): number {\n    return this[2];\n  }\n  set yaw(value: number) {\n    this[2] = checkNumber(value);\n  }\n\n  // rotation order, in all three angle notations\n  get order(): RotationOrder {\n    return this[3];\n  }\n  set order(value: RotationOrder) {\n    this[3] = checkOrder(value);\n  }\n\n  // Constructors\n  fromVector3(v: Readonly<NumericArray>, order: RotationOrder): this {\n    return this.set(v[0], v[1], v[2], Number.isFinite(order) ? order : this[3]);\n  }\n\n  // TODO - with and without 4th element\n  fromArray(array: Readonly<NumericArray>, offset: number = 0): this {\n    this[0] = array[0 + offset];\n    this[1] = array[1 + offset];\n    this[2] = array[2 + offset];\n    if (array[3] !== undefined) {\n      this[3] = array[3];\n    }\n    return this.check();\n  }\n\n  // Common ZYX rotation order\n  fromRollPitchYaw(roll: number, pitch: number, yaw: number): this {\n    return this.set(roll, pitch, yaw, RotationOrder.ZYX);\n  }\n\n  fromRotationMatrix(m: Readonly<NumericArray>, order: RotationOrder = Euler.DefaultOrder): this {\n    this._fromRotationMatrix(m, order);\n    return this.check();\n  }\n\n  // ACCESSORS\n\n  getRotationMatrix(m: NumericArray): NumericArray {\n    return this._getRotationMatrix(m);\n  }\n\n  // TODO - move to Quaternion\n  getQuaternion(): Quaternion {\n    const q = new Quaternion();\n    switch (this[4]) {\n      case RotationOrder.XYZ:\n        return q.rotateX(this[0]).rotateY(this[1]).rotateZ(this[2]);\n      case RotationOrder.YXZ:\n        return q.rotateY(this[0]).rotateX(this[1]).rotateZ(this[2]);\n      case RotationOrder.ZXY:\n        return q.rotateZ(this[0]).rotateX(this[1]).rotateY(this[2]);\n      case RotationOrder.ZYX:\n        return q.rotateZ(this[0]).rotateY(this[1]).rotateX(this[2]);\n      case RotationOrder.YZX:\n        return q.rotateY(this[0]).rotateZ(this[1]).rotateX(this[2]);\n      case RotationOrder.XZY:\n        return q.rotateX(this[0]).rotateZ(this[1]).rotateY(this[2]);\n      default:\n        throw new Error(ERR_UNKNOWN_ORDER);\n    }\n  }\n\n  // INTERNAL METHODS\n  // Conversion from Euler to rotation matrix and from matrix to Euler\n  // Adapted from three.js under MIT license\n  // // WARNING: this discards revolution information -bhouston\n  // reorder(newOrder) {\n  //   const q = new Quaternion().setFromEuler(this);\n  //   return this.setFromQuaternion(q, newOrder);\n  /* eslint-disable complexity, max-statements, one-var */\n  _fromRotationMatrix(m: Readonly<NumericArray>, order = Euler.DefaultOrder): this {\n    // assumes the upper 3x3 of m is a pure rotation matrix (i.e, unscaled)\n    const m11 = m[0],\n      m12 = m[4],\n      m13 = m[8];\n    const m21 = m[1],\n      m22 = m[5],\n      m23 = m[9];\n    const m31 = m[2],\n      m32 = m[6],\n      m33 = m[10];\n    order = order || this[3];\n    switch (order) {\n      case Euler.XYZ:\n        this[1] = Math.asin(clamp(m13, -1, 1));\n        if (Math.abs(m13) < ALMOST_ONE) {\n          this[0] = Math.atan2(-m23, m33);\n          this[2] = Math.atan2(-m12, m11);\n        } else {\n          this[0] = Math.atan2(m32, m22);\n          this[2] = 0;\n        }\n        break;\n      case Euler.YXZ:\n        this[0] = Math.asin(-clamp(m23, -1, 1));\n        if (Math.abs(m23) < ALMOST_ONE) {\n          this[1] = Math.atan2(m13, m33);\n          this[2] = Math.atan2(m21, m22);\n        } else {\n          this[1] = Math.atan2(-m31, m11);\n          this[2] = 0;\n        }\n        break;\n      case Euler.ZXY:\n        this[0] = Math.asin(clamp(m32, -1, 1));\n        if (Math.abs(m32) < ALMOST_ONE) {\n          this[1] = Math.atan2(-m31, m33);\n          this[2] = Math.atan2(-m12, m22);\n        } else {\n          this[1] = 0;\n          this[2] = Math.atan2(m21, m11);\n        }\n        break;\n      case Euler.ZYX:\n        this[1] = Math.asin(-clamp(m31, -1, 1));\n        if (Math.abs(m31) < ALMOST_ONE) {\n          this[0] = Math.atan2(m32, m33);\n          this[2] = Math.atan2(m21, m11);\n        } else {\n          this[0] = 0;\n          this[2] = Math.atan2(-m12, m22);\n        }\n        break;\n      case Euler.YZX:\n        this[2] = Math.asin(clamp(m21, -1, 1));\n        if (Math.abs(m21) < ALMOST_ONE) {\n          this[0] = Math.atan2(-m23, m22);\n          this[1] = Math.atan2(-m31, m11);\n        } else {\n          this[0] = 0;\n          this[1] = Math.atan2(m13, m33);\n        }\n        break;\n      case Euler.XZY:\n        this[2] = Math.asin(-clamp(m12, -1, 1));\n        if (Math.abs(m12) < ALMOST_ONE) {\n          this[0] = Math.atan2(m32, m22);\n          this[1] = Math.atan2(m13, m11);\n        } else {\n          this[0] = Math.atan2(-m23, m33);\n          this[1] = 0;\n        }\n        break;\n      default:\n        throw new Error(ERR_UNKNOWN_ORDER);\n    }\n    this[3] = order;\n    return this;\n  }\n\n  _getRotationMatrix(result: NumericArray): NumericArray {\n    const te = result || [-0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0];\n    const x = this.x,\n      y = this.y,\n      z = this.z;\n    const a = Math.cos(x);\n    const c = Math.cos(y);\n    const e = Math.cos(z);\n    const b = Math.sin(x);\n    const d = Math.sin(y);\n    const f = Math.sin(z);\n    switch (this[3]) {\n      case Euler.XYZ: {\n        const ae = a * e,\n          af = a * f,\n          be = b * e,\n          bf = b * f;\n        te[0] = c * e;\n        te[4] = -c * f;\n        te[8] = d;\n        te[1] = af + be * d;\n        te[5] = ae - bf * d;\n        te[9] = -b * c;\n        te[2] = bf - ae * d;\n        te[6] = be + af * d;\n        te[10] = a * c;\n        break;\n      }\n      case Euler.YXZ: {\n        const ce = c * e,\n          cf = c * f,\n          de = d * e,\n          df = d * f;\n        te[0] = ce + df * b;\n        te[4] = de * b - cf;\n        te[8] = a * d;\n        te[1] = a * f;\n        te[5] = a * e;\n        te[9] = -b;\n        te[2] = cf * b - de;\n        te[6] = df + ce * b;\n        te[10] = a * c;\n        break;\n      }\n      case Euler.ZXY: {\n        const ce = c * e,\n          cf = c * f,\n          de = d * e,\n          df = d * f;\n        te[0] = ce - df * b;\n        te[4] = -a * f;\n        te[8] = de + cf * b;\n        te[1] = cf + de * b;\n        te[5] = a * e;\n        te[9] = df - ce * b;\n        te[2] = -a * d;\n        te[6] = b;\n        te[10] = a * c;\n        break;\n      }\n      case Euler.ZYX: {\n        const ae = a * e,\n          af = a * f,\n          be = b * e,\n          bf = b * f;\n        te[0] = c * e;\n        te[4] = be * d - af;\n        te[8] = ae * d + bf;\n        te[1] = c * f;\n        te[5] = bf * d + ae;\n        te[9] = af * d - be;\n        te[2] = -d;\n        te[6] = b * c;\n        te[10] = a * c;\n        break;\n      }\n      case Euler.YZX: {\n        const ac = a * c,\n          ad = a * d,\n          bc = b * c,\n          bd = b * d;\n        te[0] = c * e;\n        te[4] = bd - ac * f;\n        te[8] = bc * f + ad;\n        te[1] = f;\n        te[5] = a * e;\n        te[9] = -b * e;\n        te[2] = -d * e;\n        te[6] = ad * f + bc;\n        te[10] = ac - bd * f;\n        break;\n      }\n      case Euler.XZY: {\n        const ac = a * c,\n          ad = a * d,\n          bc = b * c,\n          bd = b * d;\n        te[0] = c * e;\n        te[4] = -f;\n        te[8] = d * e;\n        te[1] = ac * f + bd;\n        te[5] = a * e;\n        te[9] = ad * f - bc;\n        te[2] = bc * f - ad;\n        te[6] = b * e;\n        te[10] = bd * f + ac;\n        break;\n      }\n      default:\n        throw new Error(ERR_UNKNOWN_ORDER);\n    }\n    // last column\n    te[3] = 0;\n    te[7] = 0;\n    te[11] = 0;\n    // bottom row\n    te[12] = 0;\n    te[13] = 0;\n    te[14] = 0;\n    te[15] = 1;\n    return te;\n  }\n\n  toQuaternion(): Quaternion {\n    // Abbreviations for the various angular functions\n    const cy = Math.cos(this.yaw * 0.5);\n    const sy = Math.sin(this.yaw * 0.5);\n    const cr = Math.cos(this.roll * 0.5);\n    const sr = Math.sin(this.roll * 0.5);\n    const cp = Math.cos(this.pitch * 0.5);\n    const sp = Math.sin(this.pitch * 0.5);\n    const w = cy * cr * cp + sy * sr * sp;\n    const x = cy * sr * cp - sy * cr * sp;\n    const y = cy * cr * sp + sy * sr * cp;\n    const z = sy * cr * cp - cy * sr * sp;\n    return new Quaternion(x, y, z, w);\n  }\n}\n\n// HELPER FUNCTIONS\n\nfunction validateOrder(value: number): boolean {\n  return value >= 0 && value < 6;\n}\n\nfunction checkOrder(value: number) {\n  if (value < 0 && value >= 6) {\n    throw new Error(ERR_UNKNOWN_ORDER);\n  }\n  return value;\n}\n"], "file": "euler.js"}