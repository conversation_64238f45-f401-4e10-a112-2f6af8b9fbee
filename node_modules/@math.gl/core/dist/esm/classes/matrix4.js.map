{"version": 3, "sources": ["../../../src/classes/matrix4.ts"], "names": ["Matrix", "checkVector", "vec2_transformMat4AsVector", "vec3_transformMat4AsVector", "mat4", "vec2", "vec3", "vec4", "INDICES", "DEFAULT_FOVY", "Math", "PI", "DEFAULT_ASPECT", "DEFAULT_NEAR", "DEFAULT_FAR", "IDENTITY_MATRIX", "Object", "freeze", "Matrix4", "IDENTITY", "getIdentityMatrix", "ZERO", "getZeroMatrix", "ELEMENTS", "RANK", "constructor", "array", "arguments", "length", "Array", "isArray", "copy", "identity", "check", "set", "m00", "m10", "m20", "m30", "m01", "m11", "m21", "m31", "m02", "m12", "m22", "m32", "m03", "m13", "m23", "m33", "setRowMajor", "toRowMajor", "result", "fromObject", "object", "fromQuaternion", "quaternion", "fromQuat", "frustum", "view", "left", "right", "bottom", "top", "near", "far", "Infinity", "computeInfinitePerspectiveOffCenter", "lookAt", "eye", "center", "up", "ortho", "orthographic", "fovy", "aspect", "focalDistance", "checkRadians", "halfY", "tan", "perspective", "determinant", "getScale", "sqrt", "getTranslation", "getRotation", "scaleResult", "scale", "inverseScale0", "inverseScale1", "inverseScale2", "getRotationMatrix3", "transpose", "invert", "multiplyLeft", "a", "multiply", "multiplyRight", "rotateX", "radians", "rotateY", "rotateZ", "rotateXYZ", "angleXYZ", "rotateAxis", "axis", "rotate", "factor", "translate", "vector", "transform", "transformMat4", "transformAsPoint", "out", "Error", "transformAsVector", "transformPoint", "transformVector", "transformDirection", "makeRotationX", "makeTranslation", "x", "y", "z", "possiblyDegrees", "column0Row0", "column1Row1", "column2Row0", "column2Row1", "column2Row2", "column2Row3", "column3Row2"], "mappings": "AAEA,OAAOA,MAAP,MAAmB,eAAnB;AAEA,SAAQC,WAAR,QAA0B,mBAA1B;AAGA,SAAQC,0BAAR,EAAoCC,0BAApC,QAAqE,yBAArE;AACA,OAAO,KAAKC,IAAZ,MAAsB,gBAAtB;AACA,OAAO,KAAKC,IAAZ,MAAsB,gBAAtB;AACA,OAAO,KAAKC,IAAZ,MAAsB,gBAAtB;AACA,OAAO,KAAKC,IAAZ,MAAsB,gBAAtB;IAEKC,O;;WAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;GAAAA,O,KAAAA,O;;AAmBL,MAAMC,YAAY,GAAI,KAAKC,IAAI,CAACC,EAAX,GAAiB,GAAtC;AACA,MAAMC,cAAc,GAAG,CAAvB;AACA,MAAMC,YAAY,GAAG,GAArB;AACA,MAAMC,WAAW,GAAG,GAApB;AAEA,MAAMC,eAAe,GAAGC,MAAM,CAACC,MAAP,CAAc,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B,EAAkC,CAAlC,EAAqC,CAArC,EAAwC,CAAxC,EAA2C,CAA3C,EAA8C,CAA9C,CAAd,CAAxB;AAGA,eAAe,MAAMC,OAAN,SAAsBlB,MAAtB,CAA6B;AACvB,aAARmB,QAAQ,GAAsB;AACvC,WAAOC,iBAAiB,EAAxB;AACD;;AAEc,aAAJC,IAAI,GAAsB;AACnC,WAAOC,aAAa,EAApB;AACD;;AAEW,MAARC,QAAQ,GAAW;AACrB,WAAO,EAAP;AACD;;AAEO,MAAJC,IAAI,GAAW;AACjB,WAAO,CAAP;AACD;;AAEU,MAAPhB,OAAO,GAAmB;AAC5B,WAAOA,OAAP;AACD;;AAEDiB,EAAAA,WAAW,CAACC,KAAD,EAAiC;AAE1C,UAAM,CAAC,CAAP,EAAU,CAAC,CAAX,EAAc,CAAC,CAAf,EAAkB,CAAC,CAAnB,EAAsB,CAAC,CAAvB,EAA0B,CAAC,CAA3B,EAA8B,CAAC,CAA/B,EAAkC,CAAC,CAAnC,EAAsC,CAAC,CAAvC,EAA0C,CAAC,CAA3C,EAA8C,CAAC,CAA/C,EAAkD,CAAC,CAAnD,EAAsD,CAAC,CAAvD,EAA0D,CAAC,CAA3D,EAA8D,CAAC,CAA/D,EAAkE,CAAC,CAAnE;;AACA,QAAIC,SAAS,CAACC,MAAV,KAAqB,CAArB,IAA0BC,KAAK,CAACC,OAAN,CAAcJ,KAAd,CAA9B,EAAoD;AAClD,WAAKK,IAAL,CAAUL,KAAV;AACD,KAFD,MAEO;AACL,WAAKM,QAAL;AACD;AACF;;AAEDD,EAAAA,IAAI,CAACL,KAAD,EAAsC;AACxC,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,EAAL,IAAWA,KAAK,CAAC,EAAD,CAAhB;AACA,SAAK,EAAL,IAAWA,KAAK,CAAC,EAAD,CAAhB;AACA,SAAK,EAAL,IAAWA,KAAK,CAAC,EAAD,CAAhB;AACA,SAAK,EAAL,IAAWA,KAAK,CAAC,EAAD,CAAhB;AACA,SAAK,EAAL,IAAWA,KAAK,CAAC,EAAD,CAAhB;AACA,SAAK,EAAL,IAAWA,KAAK,CAAC,EAAD,CAAhB;AACA,WAAO,KAAKO,KAAL,EAAP;AACD;;AAGDC,EAAAA,GAAG,CACDC,GADC,EAEDC,GAFC,EAGDC,GAHC,EAIDC,GAJC,EAKDC,GALC,EAMDC,GANC,EAODC,GAPC,EAQDC,GARC,EASDC,GATC,EAUDC,GAVC,EAWDC,GAXC,EAYDC,GAZC,EAaDC,GAbC,EAcDC,GAdC,EAeDC,GAfC,EAgBDC,GAhBC,EAiBK;AACN,SAAK,CAAL,IAAUf,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,EAAL,IAAWC,GAAX;AACA,SAAK,EAAL,IAAWC,GAAX;AACA,SAAK,EAAL,IAAWC,GAAX;AACA,SAAK,EAAL,IAAWC,GAAX;AACA,SAAK,EAAL,IAAWC,GAAX;AACA,SAAK,EAAL,IAAWC,GAAX;AACA,WAAO,KAAKjB,KAAL,EAAP;AACD;;AAIDkB,EAAAA,WAAW,CACThB,GADS,EAETI,GAFS,EAGTI,GAHS,EAITI,GAJS,EAKTX,GALS,EAMTI,GANS,EAOTI,GAPS,EAQTI,GARS,EASTX,GATS,EAUTI,GAVS,EAWTI,GAXS,EAYTI,GAZS,EAaTX,GAbS,EAcTI,GAdS,EAeTI,GAfS,EAgBTI,GAhBS,EAiBH;AACN,SAAK,CAAL,IAAUf,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,CAAL,IAAUC,GAAV;AACA,SAAK,EAAL,IAAWC,GAAX;AACA,SAAK,EAAL,IAAWC,GAAX;AACA,SAAK,EAAL,IAAWC,GAAX;AACA,SAAK,EAAL,IAAWC,GAAX;AACA,SAAK,EAAL,IAAWC,GAAX;AACA,SAAK,EAAL,IAAWC,GAAX;AACA,WAAO,KAAKjB,KAAL,EAAP;AACD;;AAEDmB,EAAAA,UAAU,CAACC,MAAD,EAAqC;AAC7CA,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,CAAZ;AACAA,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,CAAZ;AACAA,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,CAAZ;AACAA,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,EAAL,CAAZ;AACAA,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,CAAZ;AACAA,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,CAAZ;AACAA,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,CAAZ;AACAA,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,EAAL,CAAZ;AACAA,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,CAAZ;AACAA,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,CAAZ;AACAA,IAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,KAAK,EAAL,CAAb;AACAA,IAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,KAAK,EAAL,CAAb;AACAA,IAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,KAAK,CAAL,CAAb;AACAA,IAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,KAAK,CAAL,CAAb;AACAA,IAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,KAAK,EAAL,CAAb;AACAA,IAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,KAAK,EAAL,CAAb;AACA,WAAOA,MAAP;AACD;;AAKDrB,EAAAA,QAAQ,GAAS;AACf,WAAO,KAAKD,IAAL,CAAUhB,eAAV,CAAP;AACD;;AAODuC,EAAAA,UAAU,CAACC,MAAD,EAAqC;AAC7C,WAAO,KAAKtB,KAAL,EAAP;AACD;;AAODuB,EAAAA,cAAc,CAACC,UAAD,EAA2C;AACvDrD,IAAAA,IAAI,CAACsD,QAAL,CAAc,IAAd,EAAoBD,UAApB;AACA,WAAO,KAAKxB,KAAL,EAAP;AACD;;AAYD0B,EAAAA,OAAO,CAACC,IAAD,EAOE;AACP,UAAM;AAACC,MAAAA,IAAD;AAAOC,MAAAA,KAAP;AAAcC,MAAAA,MAAd;AAAsBC,MAAAA,GAAtB;AAA2BC,MAAAA,IAAI,GAAGpD,YAAlC;AAAgDqD,MAAAA,GAAG,GAAGpD;AAAtD,QAAqE8C,IAA3E;;AACA,QAAIM,GAAG,KAAKC,QAAZ,EAAsB;AACpBC,MAAAA,mCAAmC,CAAC,IAAD,EAAOP,IAAP,EAAaC,KAAb,EAAoBC,MAApB,EAA4BC,GAA5B,EAAiCC,IAAjC,CAAnC;AACD,KAFD,MAEO;AACL7D,MAAAA,IAAI,CAACuD,OAAL,CAAa,IAAb,EAAmBE,IAAnB,EAAyBC,KAAzB,EAAgCC,MAAhC,EAAwCC,GAAxC,EAA6CC,IAA7C,EAAmDC,GAAnD;AACD;;AACD,WAAO,KAAKjC,KAAL,EAAP;AACD;;AAUDoC,EAAAA,MAAM,CAACT,IAAD,EAIG;AACP,UAAM;AAACU,MAAAA,GAAD;AAAMC,MAAAA,MAAM,GAAG,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAAf;AAA0BC,MAAAA,EAAE,GAAG,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP;AAA/B,QAA4CZ,IAAlD;AACAxD,IAAAA,IAAI,CAACiE,MAAL,CAAY,IAAZ,EAAkBC,GAAlB,EAAuBC,MAAvB,EAA+BC,EAA/B;AACA,WAAO,KAAKvC,KAAL,EAAP;AACD;;AAaDwC,EAAAA,KAAK,CAACb,IAAD,EAOI;AACP,UAAM;AAACC,MAAAA,IAAD;AAAOC,MAAAA,KAAP;AAAcC,MAAAA,MAAd;AAAsBC,MAAAA,GAAtB;AAA2BC,MAAAA,IAAI,GAAGpD,YAAlC;AAAgDqD,MAAAA,GAAG,GAAGpD;AAAtD,QAAqE8C,IAA3E;AACAxD,IAAAA,IAAI,CAACqE,KAAL,CAAW,IAAX,EAAiBZ,IAAjB,EAAuBC,KAAvB,EAA8BC,MAA9B,EAAsCC,GAAtC,EAA2CC,IAA3C,EAAiDC,GAAjD;AACA,WAAO,KAAKjC,KAAL,EAAP;AACD;;AAYDyC,EAAAA,YAAY,CAACd,IAAD,EAMH;AACP,UAAM;AACJe,MAAAA,IAAI,GAAGlE,YADH;AAEJmE,MAAAA,MAAM,GAAGhE,cAFL;AAGJiE,MAAAA,aAAa,GAAG,CAHZ;AAIJZ,MAAAA,IAAI,GAAGpD,YAJH;AAKJqD,MAAAA,GAAG,GAAGpD;AALF,QAMF8C,IANJ;AAQAkB,IAAAA,YAAY,CAACH,IAAD,CAAZ;AAEA,UAAMI,KAAK,GAAGJ,IAAI,GAAG,CAArB;AACA,UAAMX,GAAG,GAAGa,aAAa,GAAGnE,IAAI,CAACsE,GAAL,CAASD,KAAT,CAA5B;AACA,UAAMjB,KAAK,GAAGE,GAAG,GAAGY,MAApB;AAEA,WAAO,KAAKH,KAAL,CAAW;AAChBZ,MAAAA,IAAI,EAAE,CAACC,KADS;AAEhBA,MAAAA,KAFgB;AAGhBC,MAAAA,MAAM,EAAE,CAACC,GAHO;AAIhBA,MAAAA,GAJgB;AAKhBC,MAAAA,IALgB;AAMhBC,MAAAA;AANgB,KAAX,CAAP;AAQD;;AAUDe,EAAAA,WAAW,CAACrB,IAAD,EAA2E;AACpF,UAAM;AAACe,MAAAA,IAAI,GAAI,KAAKjE,IAAI,CAACC,EAAX,GAAiB,GAAzB;AAA8BiE,MAAAA,MAAM,GAAG,CAAvC;AAA0CX,MAAAA,IAAI,GAAG,GAAjD;AAAsDC,MAAAA,GAAG,GAAG;AAA5D,QAAmEN,IAAzE;AACAkB,IAAAA,YAAY,CAACH,IAAD,CAAZ;AACAvE,IAAAA,IAAI,CAAC6E,WAAL,CAAiB,IAAjB,EAAuBN,IAAvB,EAA6BC,MAA7B,EAAqCX,IAArC,EAA2CC,GAA3C;AACA,WAAO,KAAKjC,KAAL,EAAP;AACD;;AAIDiD,EAAAA,WAAW,GAAW;AACpB,WAAO9E,IAAI,CAAC8E,WAAL,CAAiB,IAAjB,CAAP;AACD;;AAQDC,EAAAA,QAAQ,CAAC9B,MAAoB,GAAG,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,EAAS,CAAC,CAAV,CAAxB,EAAoD;AAE1DA,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY3C,IAAI,CAAC0E,IAAL,CAAU,KAAK,CAAL,IAAU,KAAK,CAAL,CAAV,GAAoB,KAAK,CAAL,IAAU,KAAK,CAAL,CAA9B,GAAwC,KAAK,CAAL,IAAU,KAAK,CAAL,CAA5D,CAAZ;AACA/B,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY3C,IAAI,CAAC0E,IAAL,CAAU,KAAK,CAAL,IAAU,KAAK,CAAL,CAAV,GAAoB,KAAK,CAAL,IAAU,KAAK,CAAL,CAA9B,GAAwC,KAAK,CAAL,IAAU,KAAK,CAAL,CAA5D,CAAZ;AACA/B,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY3C,IAAI,CAAC0E,IAAL,CAAU,KAAK,CAAL,IAAU,KAAK,CAAL,CAAV,GAAoB,KAAK,CAAL,IAAU,KAAK,CAAL,CAA9B,GAAwC,KAAK,EAAL,IAAW,KAAK,EAAL,CAA7D,CAAZ;AAIA,WAAO/B,MAAP;AACD;;AAODgC,EAAAA,cAAc,CAAChC,MAAoB,GAAG,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,EAAS,CAAC,CAAV,CAAxB,EAAoD;AAChEA,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,EAAL,CAAZ;AACAA,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,EAAL,CAAZ;AACAA,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,EAAL,CAAZ;AACA,WAAOA,MAAP;AACD;;AAQDiC,EAAAA,WAAW,CAACjC,MAAD,EAAwBkC,WAAxB,EAAkE;AAC3ElC,IAAAA,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,EAAS,CAAC,CAAV,EAAa,CAAC,CAAd,EAAiB,CAAC,CAAlB,EAAqB,CAAC,CAAtB,EAAyB,CAAC,CAA1B,EAA6B,CAAC,CAA9B,EAAiC,CAAC,CAAlC,EAAqC,CAAC,CAAtC,EAAyC,CAAC,CAA1C,EAA6C,CAAC,CAA9C,EAAiD,CAAC,CAAlD,EAAqD,CAAC,CAAtD,EAAyD,CAAC,CAA1D,EAA6D,CAAC,CAA9D,CAAnB;AACAkC,IAAAA,WAAW,GAAGA,WAAW,IAAI,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,EAAS,CAAC,CAAV,CAA7B;AACA,UAAMC,KAAK,GAAG,KAAKL,QAAL,CAAcI,WAAd,CAAd;AACA,UAAME,aAAa,GAAG,IAAID,KAAK,CAAC,CAAD,CAA/B;AACA,UAAME,aAAa,GAAG,IAAIF,KAAK,CAAC,CAAD,CAA/B;AACA,UAAMG,aAAa,GAAG,IAAIH,KAAK,CAAC,CAAD,CAA/B;AACAnC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUoC,aAAtB;AACApC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUqC,aAAtB;AACArC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUsC,aAAtB;AACAtC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,CAAZ;AACAA,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUoC,aAAtB;AACApC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUqC,aAAtB;AACArC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUsC,aAAtB;AACAtC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,CAAZ;AACAA,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUoC,aAAtB;AACApC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUqC,aAAtB;AACArC,IAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,KAAK,EAAL,IAAWsC,aAAxB;AACAtC,IAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,CAAb;AACAA,IAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,CAAb;AACAA,IAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,CAAb;AACAA,IAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,CAAb;AACAA,IAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,CAAb;AACA,WAAOA,MAAP;AACD;;AAQDuC,EAAAA,kBAAkB,CAACvC,MAAD,EAAwBkC,WAAxB,EAAkE;AAClFlC,IAAAA,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,EAAS,CAAC,CAAV,EAAa,CAAC,CAAd,EAAiB,CAAC,CAAlB,EAAqB,CAAC,CAAtB,EAAyB,CAAC,CAA1B,EAA6B,CAAC,CAA9B,EAAiC,CAAC,CAAlC,CAAnB;AACAkC,IAAAA,WAAW,GAAGA,WAAW,IAAI,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,EAAS,CAAC,CAAV,CAA7B;AACA,UAAMC,KAAK,GAAG,KAAKL,QAAL,CAAcI,WAAd,CAAd;AACA,UAAME,aAAa,GAAG,IAAID,KAAK,CAAC,CAAD,CAA/B;AACA,UAAME,aAAa,GAAG,IAAIF,KAAK,CAAC,CAAD,CAA/B;AACA,UAAMG,aAAa,GAAG,IAAIH,KAAK,CAAC,CAAD,CAA/B;AACAnC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUoC,aAAtB;AACApC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUqC,aAAtB;AACArC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUsC,aAAtB;AACAtC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUoC,aAAtB;AACApC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUqC,aAAtB;AACArC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUsC,aAAtB;AACAtC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUoC,aAAtB;AACApC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUqC,aAAtB;AACArC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,EAAL,IAAWsC,aAAvB;AACA,WAAOtC,MAAP;AACD;;AAIDwC,EAAAA,SAAS,GAAS;AAChBzF,IAAAA,IAAI,CAACyF,SAAL,CAAe,IAAf,EAAqB,IAArB;AACA,WAAO,KAAK5D,KAAL,EAAP;AACD;;AAED6D,EAAAA,MAAM,GAAS;AACb1F,IAAAA,IAAI,CAAC0F,MAAL,CAAY,IAAZ,EAAkB,IAAlB;AACA,WAAO,KAAK7D,KAAL,EAAP;AACD;;AAID8D,EAAAA,YAAY,CAACC,CAAD,EAAkC;AAC5C5F,IAAAA,IAAI,CAAC6F,QAAL,CAAc,IAAd,EAAoBD,CAApB,EAAuB,IAAvB;AACA,WAAO,KAAK/D,KAAL,EAAP;AACD;;AAEDiE,EAAAA,aAAa,CAACF,CAAD,EAAkC;AAC7C5F,IAAAA,IAAI,CAAC6F,QAAL,CAAc,IAAd,EAAoB,IAApB,EAA0BD,CAA1B;AACA,WAAO,KAAK/D,KAAL,EAAP;AACD;;AAGDkE,EAAAA,OAAO,CAACC,OAAD,EAAwB;AAC7BhG,IAAAA,IAAI,CAAC+F,OAAL,CAAa,IAAb,EAAmB,IAAnB,EAAyBC,OAAzB;AAEA,WAAO,KAAKnE,KAAL,EAAP;AACD;;AAGDoE,EAAAA,OAAO,CAACD,OAAD,EAAwB;AAC7BhG,IAAAA,IAAI,CAACiG,OAAL,CAAa,IAAb,EAAmB,IAAnB,EAAyBD,OAAzB;AAEA,WAAO,KAAKnE,KAAL,EAAP;AACD;;AAODqE,EAAAA,OAAO,CAACF,OAAD,EAAwB;AAC7BhG,IAAAA,IAAI,CAACkG,OAAL,CAAa,IAAb,EAAmB,IAAnB,EAAyBF,OAAzB;AAEA,WAAO,KAAKnE,KAAL,EAAP;AACD;;AAODsE,EAAAA,SAAS,CAACC,QAAD,EAAyC;AAChD,WAAO,KAAKL,OAAL,CAAaK,QAAQ,CAAC,CAAD,CAArB,EAA0BH,OAA1B,CAAkCG,QAAQ,CAAC,CAAD,CAA1C,EAA+CF,OAA/C,CAAuDE,QAAQ,CAAC,CAAD,CAA/D,CAAP;AACD;;AAQDC,EAAAA,UAAU,CAACL,OAAD,EAAkBM,IAAlB,EAAsD;AAC9DtG,IAAAA,IAAI,CAACuG,MAAL,CAAY,IAAZ,EAAkB,IAAlB,EAAwBP,OAAxB,EAAiCM,IAAjC;AACA,WAAO,KAAKzE,KAAL,EAAP;AACD;;AAODuD,EAAAA,KAAK,CAACoB,MAAD,EAAgD;AACnDxG,IAAAA,IAAI,CAACoF,KAAL,CAAW,IAAX,EAAiB,IAAjB,EAAuB3D,KAAK,CAACC,OAAN,CAAc8E,MAAd,IAAwBA,MAAxB,GAAiC,CAACA,MAAD,EAASA,MAAT,EAAiBA,MAAjB,CAAxD;AACA,WAAO,KAAK3E,KAAL,EAAP;AACD;;AAOD4E,EAAAA,SAAS,CAACC,MAAD,EAAuC;AAC9C1G,IAAAA,IAAI,CAACyG,SAAL,CAAe,IAAf,EAAqB,IAArB,EAA2BC,MAA3B;AACA,WAAO,KAAK7E,KAAL,EAAP;AACD;;AAUD8E,EAAAA,SAAS,CAACD,MAAD,EAAiCzD,MAAjC,EAAsE;AAC7E,QAAIyD,MAAM,CAAClF,MAAP,KAAkB,CAAtB,EAAyB;AACvByB,MAAAA,MAAM,GAAG9C,IAAI,CAACyG,aAAL,CAAmB3D,MAAM,IAAI,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,EAAS,CAAC,CAAV,EAAa,CAAC,CAAd,CAA7B,EAA+CyD,MAA/C,EAAuD,IAAvD,CAAT;AACA7G,MAAAA,WAAW,CAACoD,MAAD,EAAS,CAAT,CAAX;AACA,aAAOA,MAAP;AACD;;AACD,WAAO,KAAK4D,gBAAL,CAAsBH,MAAtB,EAA8BzD,MAA9B,CAAP;AACD;;AAQD4D,EAAAA,gBAAgB,CAACH,MAAD,EAAiCzD,MAAjC,EAAsE;AACpF,UAAM;AAACzB,MAAAA;AAAD,QAAWkF,MAAjB;AACA,QAAII,GAAJ;;AACA,YAAQtF,MAAR;AACE,WAAK,CAAL;AACEsF,QAAAA,GAAG,GAAG7G,IAAI,CAAC2G,aAAL,CAAmB3D,MAAM,IAAI,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,CAA7B,EAAuCyD,MAAvC,EAA+C,IAA/C,CAAN;AACA;;AACF,WAAK,CAAL;AACEI,QAAAA,GAAG,GAAG5G,IAAI,CAAC0G,aAAL,CAAmB3D,MAAM,IAAI,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,EAAS,CAAC,CAAV,CAA7B,EAA2CyD,MAA3C,EAAmD,IAAnD,CAAN;AACA;;AACF;AACE,cAAM,IAAIK,KAAJ,CAAU,gBAAV,CAAN;AARJ;;AAUAlH,IAAAA,WAAW,CAACiH,GAAD,EAAMJ,MAAM,CAAClF,MAAb,CAAX;AACA,WAAOsF,GAAP;AACD;;AAQDE,EAAAA,iBAAiB,CAACN,MAAD,EAAiCzD,MAAjC,EAAsE;AACrF,QAAI6D,GAAJ;;AACA,YAAQJ,MAAM,CAAClF,MAAf;AACE,WAAK,CAAL;AACEsF,QAAAA,GAAG,GAAGhH,0BAA0B,CAACmD,MAAM,IAAI,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,CAAX,EAAqByD,MAArB,EAA6B,IAA7B,CAAhC;AACA;;AACF,WAAK,CAAL;AACEI,QAAAA,GAAG,GAAG/G,0BAA0B,CAACkD,MAAM,IAAI,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,EAAS,CAAC,CAAV,CAAX,EAAyByD,MAAzB,EAAiC,IAAjC,CAAhC;AACA;;AACF;AACE,cAAM,IAAIK,KAAJ,CAAU,gBAAV,CAAN;AARJ;;AAUAlH,IAAAA,WAAW,CAACiH,GAAD,EAAMJ,MAAM,CAAClF,MAAb,CAAX;AACA,WAAOsF,GAAP;AACD;;AAGDG,EAAAA,cAAc,CAACP,MAAD,EAAiCzD,MAAjC,EAAsE;AAClF,WAAO,KAAK4D,gBAAL,CAAsBH,MAAtB,EAA8BzD,MAA9B,CAAP;AACD;;AAGDiE,EAAAA,eAAe,CAACR,MAAD,EAAiCzD,MAAjC,EAAsE;AACnF,WAAO,KAAK4D,gBAAL,CAAsBH,MAAtB,EAA8BzD,MAA9B,CAAP;AACD;;AAGDkE,EAAAA,kBAAkB,CAACT,MAAD,EAAiCzD,MAAjC,EAAsE;AACtF,WAAO,KAAK+D,iBAAL,CAAuBN,MAAvB,EAA+BzD,MAA/B,CAAP;AACD;;AAIDmE,EAAAA,aAAa,CAACpB,OAAD,EAAwB;AACnC,WAAO,KAAKpE,QAAL,GAAgBmE,OAAhB,CAAwBC,OAAxB,CAAP;AACD;;AAEDqB,EAAAA,eAAe,CAACC,CAAD,EAAYC,CAAZ,EAAuBC,CAAvB,EAAwC;AACrD,WAAO,KAAK5F,QAAL,GAAgB6E,SAAhB,CAA0B,CAACa,CAAD,EAAIC,CAAJ,EAAOC,CAAP,CAA1B,CAAP;AACD;;AArjByC;AAyjB5C,IAAIvG,IAAJ;AACA,IAAIF,QAAJ;;AAEA,SAASG,aAAT,GAA4C;AAC1C,MAAI,CAACD,IAAL,EAAW;AACTA,IAAAA,IAAI,GAAG,IAAIH,OAAJ,CAAY,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B,EAAkC,CAAlC,EAAqC,CAArC,EAAwC,CAAxC,EAA2C,CAA3C,EAA8C,CAA9C,CAAZ,CAAP;AACAF,IAAAA,MAAM,CAACC,MAAP,CAAcI,IAAd;AACD;;AACD,SAAOA,IAAP;AACD;;AAED,SAASD,iBAAT,GAAsC;AACpC,MAAI,CAACD,QAAL,EAAe;AACbA,IAAAA,QAAQ,GAAG,IAAID,OAAJ,EAAX;AACAF,IAAAA,MAAM,CAACC,MAAP,CAAcE,QAAd;AACD;;AACD,SAAOA,QAAP;AACD;;AAID,SAAS2D,YAAT,CAAsB+C,eAAtB,EAA+C;AAC7C,MAAIA,eAAe,GAAGnH,IAAI,CAACC,EAAL,GAAU,CAAhC,EAAmC;AACjC,UAAMwG,KAAK,CAAC,kBAAD,CAAX;AACD;AACF;;AAGD,SAAS/C,mCAAT,CACEf,MADF,EAEEQ,IAFF,EAGEC,KAHF,EAIEC,MAJF,EAKEC,GALF,EAMEC,IANF,EAOgB;AACd,QAAM6D,WAAW,GAAI,IAAI7D,IAAL,IAAcH,KAAK,GAAGD,IAAtB,CAApB;AACA,QAAMkE,WAAW,GAAI,IAAI9D,IAAL,IAAcD,GAAG,GAAGD,MAApB,CAApB;AACA,QAAMiE,WAAW,GAAG,CAAClE,KAAK,GAAGD,IAAT,KAAkBC,KAAK,GAAGD,IAA1B,CAApB;AACA,QAAMoE,WAAW,GAAG,CAACjE,GAAG,GAAGD,MAAP,KAAkBC,GAAG,GAAGD,MAAxB,CAApB;AACA,QAAMmE,WAAW,GAAG,CAAC,CAArB;AACA,QAAMC,WAAW,GAAG,CAAC,CAArB;AACA,QAAMC,WAAW,GAAG,CAAC,CAAD,GAAKnE,IAAzB;AACAZ,EAAAA,MAAM,CAAC,CAAD,CAAN,GAAYyE,WAAZ;AACAzE,EAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,CAAZ;AACAA,EAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,CAAZ;AACAA,EAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,CAAZ;AACAA,EAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,CAAZ;AACAA,EAAAA,MAAM,CAAC,CAAD,CAAN,GAAY0E,WAAZ;AACA1E,EAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,CAAZ;AACAA,EAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,CAAZ;AACAA,EAAAA,MAAM,CAAC,CAAD,CAAN,GAAY2E,WAAZ;AACA3E,EAAAA,MAAM,CAAC,CAAD,CAAN,GAAY4E,WAAZ;AACA5E,EAAAA,MAAM,CAAC,EAAD,CAAN,GAAa6E,WAAb;AACA7E,EAAAA,MAAM,CAAC,EAAD,CAAN,GAAa8E,WAAb;AACA9E,EAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,CAAb;AACAA,EAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,CAAb;AACAA,EAAAA,MAAM,CAAC,EAAD,CAAN,GAAa+E,WAAb;AACA/E,EAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,CAAb;AACA,SAAOA,MAAP;AACD", "sourcesContent": ["// Copyright (c) 2017 Uber Technologies, Inc.\n// MIT License\nimport Matrix from './base/matrix';\nimport {NumericArray} from '@math.gl/types';\nimport {checkVector} from '../lib/validators';\n\n/* eslint-disable camelcase */\nimport {vec2_transformMat4AsVector, vec3_transformMat4AsVector} from '../lib/gl-matrix-extras';\nimport * as mat4 from 'gl-matrix/mat4';\nimport * as vec2 from 'gl-matrix/vec2';\nimport * as vec3 from 'gl-matrix/vec3';\nimport * as vec4 from 'gl-matrix/vec4';\n\nenum INDICES {\n  COL0ROW0 = 0,\n  COL0ROW1 = 1,\n  COL0ROW2 = 2,\n  COL0ROW3 = 3,\n  COL1ROW0 = 4,\n  COL1ROW1 = 5,\n  COL1ROW2 = 6,\n  COL1ROW3 = 7,\n  COL2ROW0 = 8,\n  COL2ROW1 = 9,\n  COL2ROW2 = 10,\n  COL2ROW3 = 11,\n  COL3ROW0 = 12,\n  COL3ROW1 = 13,\n  COL3ROW2 = 14,\n  COL3ROW3 = 15\n}\n\nconst DEFAULT_FOVY = (45 * Math.PI) / 180;\nconst DEFAULT_ASPECT = 1;\nconst DEFAULT_NEAR = 0.1;\nconst DEFAULT_FAR = 500;\n\nconst IDENTITY_MATRIX = Object.freeze([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]);\n\n/** 4x4 matrix */\nexport default class Matrix4 extends Matrix {\n  static get IDENTITY(): Readonly<Matrix4> {\n    return getIdentityMatrix();\n  }\n\n  static get ZERO(): Readonly<Matrix4> {\n    return getZeroMatrix();\n  }\n\n  get ELEMENTS(): number {\n    return 16;\n  }\n\n  get RANK(): number {\n    return 4;\n  }\n\n  get INDICES(): typeof INDICES {\n    return INDICES;\n  }\n\n  constructor(array?: Readonly<NumericArray>) {\n    // PERF NOTE: initialize elements as double precision numbers\n    super(-0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0);\n    if (arguments.length === 1 && Array.isArray(array)) {\n      this.copy(array);\n    } else {\n      this.identity();\n    }\n  }\n\n  copy(array: Readonly<NumericArray>): this {\n    this[0] = array[0];\n    this[1] = array[1];\n    this[2] = array[2];\n    this[3] = array[3];\n    this[4] = array[4];\n    this[5] = array[5];\n    this[6] = array[6];\n    this[7] = array[7];\n    this[8] = array[8];\n    this[9] = array[9];\n    this[10] = array[10];\n    this[11] = array[11];\n    this[12] = array[12];\n    this[13] = array[13];\n    this[14] = array[14];\n    this[15] = array[15];\n    return this.check();\n  }\n\n  // eslint-disable-next-line max-params\n  set(\n    m00: number,\n    m10: number,\n    m20: number,\n    m30: number,\n    m01: number,\n    m11: number,\n    m21: number,\n    m31: number,\n    m02: number,\n    m12: number,\n    m22: number,\n    m32: number,\n    m03: number,\n    m13: number,\n    m23: number,\n    m33: number\n  ): this {\n    this[0] = m00;\n    this[1] = m10;\n    this[2] = m20;\n    this[3] = m30;\n    this[4] = m01;\n    this[5] = m11;\n    this[6] = m21;\n    this[7] = m31;\n    this[8] = m02;\n    this[9] = m12;\n    this[10] = m22;\n    this[11] = m32;\n    this[12] = m03;\n    this[13] = m13;\n    this[14] = m23;\n    this[15] = m33;\n    return this.check();\n  }\n\n  // accepts row major order, stores as column major\n  // eslint-disable-next-line max-params\n  setRowMajor(\n    m00: number,\n    m01: number,\n    m02: number,\n    m03: number,\n    m10: number,\n    m11: number,\n    m12: number,\n    m13: number,\n    m20: number,\n    m21: number,\n    m22: number,\n    m23: number,\n    m30: number,\n    m31: number,\n    m32: number,\n    m33: number\n  ): this {\n    this[0] = m00;\n    this[1] = m10;\n    this[2] = m20;\n    this[3] = m30;\n    this[4] = m01;\n    this[5] = m11;\n    this[6] = m21;\n    this[7] = m31;\n    this[8] = m02;\n    this[9] = m12;\n    this[10] = m22;\n    this[11] = m32;\n    this[12] = m03;\n    this[13] = m13;\n    this[14] = m23;\n    this[15] = m33;\n    return this.check();\n  }\n\n  toRowMajor(result: NumericArray): NumericArray {\n    result[0] = this[0];\n    result[1] = this[4];\n    result[2] = this[8];\n    result[3] = this[12];\n    result[4] = this[1];\n    result[5] = this[5];\n    result[6] = this[9];\n    result[7] = this[13];\n    result[8] = this[2];\n    result[9] = this[6];\n    result[10] = this[10];\n    result[11] = this[14];\n    result[12] = this[3];\n    result[13] = this[7];\n    result[14] = this[11];\n    result[15] = this[15];\n    return result;\n  }\n\n  // Constructors\n\n  /** Set to identity matrix */\n  identity(): this {\n    return this.copy(IDENTITY_MATRIX);\n  }\n\n  /**\n   *\n   * @param object\n   * @returns self\n   */\n  fromObject(object: {[key: string]: any}): this {\n    return this.check();\n  }\n\n  /**\n   * Calculates a 4x4 matrix from the given quaternion\n   * @param quaternion Quaternion to create matrix from\n   * @returns self\n   */\n  fromQuaternion(quaternion: Readonly<NumericArray>): this {\n    mat4.fromQuat(this, quaternion);\n    return this.check();\n  }\n\n  /**\n   * Generates a frustum matrix with the given bounds\n   * @param view.left - Left bound of the frustum\n   * @param view.right - Right bound of the frustum\n   * @param view.bottom - Bottom bound of the frustum\n   * @param view.top - Top bound of the frustum\n   * @param view.near - Near bound of the frustum\n   * @param view.far - Far bound of the frustum. Can be set to Infinity.\n   * @returns self\n   */\n  frustum(view: {\n    left: number;\n    right: number;\n    bottom: number;\n    top: number;\n    near: number;\n    far?: number;\n  }): this {\n    const {left, right, bottom, top, near = DEFAULT_NEAR, far = DEFAULT_FAR} = view;\n    if (far === Infinity) {\n      computeInfinitePerspectiveOffCenter(this, left, right, bottom, top, near);\n    } else {\n      mat4.frustum(this, left, right, bottom, top, near, far);\n    }\n    return this.check();\n  }\n\n  /**\n   * Generates a look-at matrix with the given eye position, focal point,\n   * and up axis\n   * @param view.eye - (vector) Position of the viewer\n   * @param view.center - (vector) Point the viewer is looking at\n   * @param view.up - (vector) Up axis\n   * @returns self\n   */\n  lookAt(view: {\n    eye: Readonly<NumericArray>;\n    center?: Readonly<NumericArray>;\n    up?: Readonly<NumericArray>;\n  }): this {\n    const {eye, center = [0, 0, 0], up = [0, 1, 0]} = view;\n    mat4.lookAt(this, eye, center, up);\n    return this.check();\n  }\n\n  /**\n   * Generates a orthogonal projection matrix with the given bounds\n   * from \"traditional\" view space parameters\n   * @param view.left - Left bound of the frustum\n   * @param view.right number  Right bound of the frustum\n   * @param view.bottom - Bottom bound of the frustum\n   * @param view.top number  Top bound of the frustum\n   * @param view.near - Near bound of the frustum\n   * @param view.far number  Far bound of the frustum\n   * @returns self\n   */\n  ortho(view: {\n    left: number;\n    right: number;\n    bottom: number;\n    top: number;\n    near?: number;\n    far?: number;\n  }): this {\n    const {left, right, bottom, top, near = DEFAULT_NEAR, far = DEFAULT_FAR} = view;\n    mat4.ortho(this, left, right, bottom, top, near, far);\n    return this.check();\n  }\n\n  /**\n   * Generates an orthogonal projection matrix with the same parameters\n   * as a perspective matrix (plus focalDistance)\n   * @param view.fovy Vertical field of view in radians\n   * @param view.aspect Aspect ratio. Typically viewport width / viewport height\n   * @param view.focalDistance Distance in the view frustum used for extent calculations\n   * @param view.near Near bound of the frustum\n   * @param view.far Far bound of the frustum\n   * @returns self\n   */\n  orthographic(view: {\n    fovy?: number;\n    aspect?: number;\n    focalDistance?: number;\n    near?: number;\n    far?: number;\n  }): this {\n    const {\n      fovy = DEFAULT_FOVY,\n      aspect = DEFAULT_ASPECT,\n      focalDistance = 1,\n      near = DEFAULT_NEAR,\n      far = DEFAULT_FAR\n    } = view;\n\n    checkRadians(fovy);\n\n    const halfY = fovy / 2;\n    const top = focalDistance * Math.tan(halfY); // focus_plane is the distance from the camera\n    const right = top * aspect;\n\n    return this.ortho({\n      left: -right,\n      right,\n      bottom: -top,\n      top,\n      near,\n      far\n    });\n  }\n\n  /**\n   * Generates a perspective projection matrix with the given bounds\n   * @param view.fovy Vertical field of view in radians\n   * @param view.aspect Aspect ratio. typically viewport width/height\n   * @param view.near Near bound of the frustum\n   * @param view.far Far bound of the frustum\n   * @returns self\n   */\n  perspective(view: {fovy: number; aspect?: number; near?: number; far?: number}): this {\n    const {fovy = (45 * Math.PI) / 180, aspect = 1, near = 0.1, far = 500} = view;\n    checkRadians(fovy);\n    mat4.perspective(this, fovy, aspect, near, far);\n    return this.check();\n  }\n\n  // Accessors\n\n  determinant(): number {\n    return mat4.determinant(this);\n  }\n\n  /**\n   * Extracts the non-uniform scale assuming the matrix is an affine transformation.\n   * The scales are the \"lengths\" of the column vectors in the upper-left 3x3 matrix.\n   * @param result\n   * @returns self\n   */\n  getScale(result: NumericArray = [-0, -0, -0]): NumericArray {\n    // explicit is faster than hypot...\n    result[0] = Math.sqrt(this[0] * this[0] + this[1] * this[1] + this[2] * this[2]);\n    result[1] = Math.sqrt(this[4] * this[4] + this[5] * this[5] + this[6] * this[6]);\n    result[2] = Math.sqrt(this[8] * this[8] + this[9] * this[9] + this[10] * this[10]);\n    // result[0] = Math.hypot(this[0], this[1], this[2]);\n    // result[1] = Math.hypot(this[4], this[5], this[6]);\n    // result[2] = Math.hypot(this[8], this[9], this[10]);\n    return result;\n  }\n\n  /**\n   * Gets the translation portion, assuming the matrix is a affine transformation matrix.\n   * @param result\n   * @returns self\n   */\n  getTranslation(result: NumericArray = [-0, -0, -0]): NumericArray {\n    result[0] = this[12];\n    result[1] = this[13];\n    result[2] = this[14];\n    return result;\n  }\n\n  /**\n   * Gets upper left 3x3 pure rotation matrix (non-scaling), assume affine transformation matrix\n   * @param result\n   * @param scaleResult\n   * @returns self\n   */\n  getRotation(result?: NumericArray, scaleResult?: NumericArray): NumericArray {\n    result = result || [-0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0];\n    scaleResult = scaleResult || [-0, -0, -0];\n    const scale = this.getScale(scaleResult);\n    const inverseScale0 = 1 / scale[0];\n    const inverseScale1 = 1 / scale[1];\n    const inverseScale2 = 1 / scale[2];\n    result[0] = this[0] * inverseScale0;\n    result[1] = this[1] * inverseScale1;\n    result[2] = this[2] * inverseScale2;\n    result[3] = 0;\n    result[4] = this[4] * inverseScale0;\n    result[5] = this[5] * inverseScale1;\n    result[6] = this[6] * inverseScale2;\n    result[7] = 0;\n    result[8] = this[8] * inverseScale0;\n    result[9] = this[9] * inverseScale1;\n    result[10] = this[10] * inverseScale2;\n    result[11] = 0;\n    result[12] = 0;\n    result[13] = 0;\n    result[14] = 0;\n    result[15] = 1;\n    return result;\n  }\n\n  /**\n   *\n   * @param result\n   * @param scaleResult\n   * @returns self\n   */\n  getRotationMatrix3(result?: NumericArray, scaleResult?: NumericArray): NumericArray {\n    result = result || [-0, -0, -0, -0, -0, -0, -0, -0, -0];\n    scaleResult = scaleResult || [-0, -0, -0];\n    const scale = this.getScale(scaleResult);\n    const inverseScale0 = 1 / scale[0];\n    const inverseScale1 = 1 / scale[1];\n    const inverseScale2 = 1 / scale[2];\n    result[0] = this[0] * inverseScale0;\n    result[1] = this[1] * inverseScale1;\n    result[2] = this[2] * inverseScale2;\n    result[3] = this[4] * inverseScale0;\n    result[4] = this[5] * inverseScale1;\n    result[5] = this[6] * inverseScale2;\n    result[6] = this[8] * inverseScale0;\n    result[7] = this[9] * inverseScale1;\n    result[8] = this[10] * inverseScale2;\n    return result;\n  }\n\n  // Modifiers\n\n  transpose(): this {\n    mat4.transpose(this, this);\n    return this.check();\n  }\n\n  invert(): this {\n    mat4.invert(this, this);\n    return this.check();\n  }\n\n  // Operations\n\n  multiplyLeft(a: Readonly<NumericArray>): this {\n    mat4.multiply(this, a, this);\n    return this.check();\n  }\n\n  multiplyRight(a: Readonly<NumericArray>): this {\n    mat4.multiply(this, this, a);\n    return this.check();\n  }\n\n  // Rotates a matrix by the given angle around the X axis\n  rotateX(radians: number): this {\n    mat4.rotateX(this, this, radians);\n    // mat4.rotate(this, this, radians, [1, 0, 0]);\n    return this.check();\n  }\n\n  // Rotates a matrix by the given angle around the Y axis.\n  rotateY(radians: number): this {\n    mat4.rotateY(this, this, radians);\n    // mat4.rotate(this, this, radians, [0, 1, 0]);\n    return this.check();\n  }\n\n  /**\n   * Rotates a matrix by the given angle around the Z axis.\n   * @param radians\n   * @returns self\n   */\n  rotateZ(radians: number): this {\n    mat4.rotateZ(this, this, radians);\n    // mat4.rotate(this, this, radians, [0, 0, 1]);\n    return this.check();\n  }\n\n  /**\n   *\n   * @param param0\n   * @returns self\n   */\n  rotateXYZ(angleXYZ: Readonly<NumericArray>): this {\n    return this.rotateX(angleXYZ[0]).rotateY(angleXYZ[1]).rotateZ(angleXYZ[2]);\n  }\n\n  /**\n   *\n   * @param radians\n   * @param axis\n   * @returns self\n   */\n  rotateAxis(radians: number, axis: Readonly<NumericArray>): this {\n    mat4.rotate(this, this, radians, axis);\n    return this.check();\n  }\n\n  /**\n   *\n   * @param factor\n   * @returns self\n   */\n  scale(factor: number | Readonly<NumericArray>): this {\n    mat4.scale(this, this, Array.isArray(factor) ? factor : [factor, factor, factor]);\n    return this.check();\n  }\n\n  /**\n   *\n   * @param vec\n   * @returns self\n   */\n  translate(vector: Readonly<NumericArray>): this {\n    mat4.translate(this, this, vector);\n    return this.check();\n  }\n\n  // Transforms\n\n  /**\n   * Transforms any 2, 3 or 4 element vector. 2 and 3 elements are treated as points\n   * @param vector\n   * @param result\n   * @returns self\n   */\n  transform(vector: Readonly<NumericArray>, result?: NumericArray): NumericArray {\n    if (vector.length === 4) {\n      result = vec4.transformMat4(result || [-0, -0, -0, -0], vector, this);\n      checkVector(result, 4);\n      return result;\n    }\n    return this.transformAsPoint(vector, result);\n  }\n\n  /**\n   * Transforms any 2 or 3 element array as point (w implicitly 1)\n   * @param vector\n   * @param result\n   * @returns self\n   */\n  transformAsPoint(vector: Readonly<NumericArray>, result?: NumericArray): NumericArray {\n    const {length} = vector;\n    let out: NumericArray;\n    switch (length) {\n      case 2:\n        out = vec2.transformMat4(result || [-0, -0], vector, this);\n        break;\n      case 3:\n        out = vec3.transformMat4(result || [-0, -0, -0], vector, this);\n        break;\n      default:\n        throw new Error('Illegal vector');\n    }\n    checkVector(out, vector.length);\n    return out;\n  }\n\n  /**\n   * Transforms any 2 or 3 element array as vector (w implicitly 0)\n   * @param vector\n   * @param result\n   * @returns self\n   */\n  transformAsVector(vector: Readonly<NumericArray>, result?: NumericArray): NumericArray {\n    let out: NumericArray;\n    switch (vector.length) {\n      case 2:\n        out = vec2_transformMat4AsVector(result || [-0, -0], vector, this);\n        break;\n      case 3:\n        out = vec3_transformMat4AsVector(result || [-0, -0, -0], vector, this);\n        break;\n      default:\n        throw new Error('Illegal vector');\n    }\n    checkVector(out, vector.length);\n    return out;\n  }\n\n  /** @deprecated */\n  transformPoint(vector: Readonly<NumericArray>, result?: NumericArray): NumericArray {\n    return this.transformAsPoint(vector, result);\n  }\n\n  /** @deprecated */\n  transformVector(vector: Readonly<NumericArray>, result?: NumericArray): NumericArray {\n    return this.transformAsPoint(vector, result);\n  }\n\n  /** @deprecated */\n  transformDirection(vector: Readonly<NumericArray>, result?: NumericArray): NumericArray {\n    return this.transformAsVector(vector, result);\n  }\n\n  // three.js math API compatibility\n\n  makeRotationX(radians: number): this {\n    return this.identity().rotateX(radians);\n  }\n\n  makeTranslation(x: number, y: number, z: number): this {\n    return this.identity().translate([x, y, z]);\n  }\n}\n\n// TODO initializing static members directly is an option, but make sure no tree-shaking issues\nlet ZERO: Matrix4;\nlet IDENTITY: Matrix4;\n\nfunction getZeroMatrix(): Readonly<Matrix4> {\n  if (!ZERO) {\n    ZERO = new Matrix4([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]);\n    Object.freeze(ZERO);\n  }\n  return ZERO;\n}\n\nfunction getIdentityMatrix(): Matrix4 {\n  if (!IDENTITY) {\n    IDENTITY = new Matrix4();\n    Object.freeze(IDENTITY);\n  }\n  return IDENTITY;\n}\n\n// HELPER FUNCTIONS\n\nfunction checkRadians(possiblyDegrees: number) {\n  if (possiblyDegrees > Math.PI * 2) {\n    throw Error('expected radians');\n  }\n}\n\n// eslint-disable-next-line max-params\nfunction computeInfinitePerspectiveOffCenter(\n  result: NumericArray,\n  left: number,\n  right: number,\n  bottom: number,\n  top: number,\n  near: number\n): NumericArray {\n  const column0Row0 = (2 * near) / (right - left);\n  const column1Row1 = (2 * near) / (top - bottom);\n  const column2Row0 = (right + left) / (right - left);\n  const column2Row1 = (top + bottom) / (top - bottom);\n  const column2Row2 = -1;\n  const column2Row3 = -1;\n  const column3Row2 = -2 * near;\n  result[0] = column0Row0;\n  result[1] = 0;\n  result[2] = 0;\n  result[3] = 0;\n  result[4] = 0;\n  result[5] = column1Row1;\n  result[6] = 0;\n  result[7] = 0;\n  result[8] = column2Row0;\n  result[9] = column2Row1;\n  result[10] = column2Row2;\n  result[11] = column2Row3;\n  result[12] = 0;\n  result[13] = 0;\n  result[14] = column3Row2;\n  result[15] = 0;\n  return result;\n}\n"], "file": "matrix4.js"}