{"version": 3, "sources": ["../../../src/classes/vector4.ts"], "names": ["Vector", "config", "isArray", "checkNumber", "vec4", "vec4_transformMat2", "vec4_transformMat3", "ZERO", "Vector4", "Object", "freeze", "constructor", "x", "y", "z", "w", "arguments", "length", "copy", "debug", "set", "check", "array", "fromObject", "object", "toObject", "ELEMENTS", "value", "transform", "matrix4", "transformMat4", "transformByMatrix3", "matrix3", "transformByMatrix2", "matrix2", "transformByQuaternion", "quaternion", "transformQuat", "applyMatrix4", "m"], "mappings": "AAGA,OAAOA,MAAP,MAAmB,eAAnB;AACA,SAAQC,MAAR,EAAgBC,OAAhB,QAA8B,eAA9B;AACA,SAAQC,WAAR,QAA0B,mBAA1B;AACA,OAAO,KAAKC,IAAZ,MAAsB,gBAAtB;AAEA,SAAQC,kBAAR,EAA4BC,kBAA5B,QAAqD,yBAArD;AAKA,IAAIC,IAAJ;AAMA,eAAe,MAAMC,OAAN,SAAsBR,MAAtB,CAA6B;AAC3B,aAAJO,IAAI,GAAY;AACzB,QAAI,CAACA,IAAL,EAAW;AACTA,MAAAA,IAAI,GAAG,IAAIC,OAAJ,CAAY,CAAZ,EAAe,CAAf,EAAkB,CAAlB,EAAqB,CAArB,CAAP;AACAC,MAAAA,MAAM,CAACC,MAAP,CAAcH,IAAd;AACD;;AACD,WAAOA,IAAP;AACD;;AAEDI,EAAAA,WAAW,CAACC,CAAkC,GAAG,CAAtC,EAAyCC,CAAS,GAAG,CAArD,EAAwDC,CAAS,GAAG,CAApE,EAAuEC,CAAS,GAAG,CAAnF,EAAsF;AAE/F,UAAM,CAAC,CAAP,EAAU,CAAC,CAAX,EAAc,CAAC,CAAf,EAAkB,CAAC,CAAnB;;AACA,QAAIb,OAAO,CAACU,CAAD,CAAP,IAAcI,SAAS,CAACC,MAAV,KAAqB,CAAvC,EAA0C;AACxC,WAAKC,IAAL,CAAUN,CAAV;AACD,KAFD,MAEO;AAEL,UAAIX,MAAM,CAACkB,KAAX,EAAkB;AAChBhB,QAAAA,WAAW,CAACS,CAAD,CAAX;AACAT,QAAAA,WAAW,CAACU,CAAD,CAAX;AACAV,QAAAA,WAAW,CAACW,CAAD,CAAX;AACAX,QAAAA,WAAW,CAACY,CAAD,CAAX;AACD;;AACD,WAAK,CAAL,IAAUH,CAAV;AACA,WAAK,CAAL,IAAUC,CAAV;AACA,WAAK,CAAL,IAAUC,CAAV;AACA,WAAK,CAAL,IAAUC,CAAV;AACD;AACF;;AAEDK,EAAAA,GAAG,CAACR,CAAD,EAAYC,CAAZ,EAAuBC,CAAvB,EAAkCC,CAAlC,EAAmD;AACpD,SAAK,CAAL,IAAUH,CAAV;AACA,SAAK,CAAL,IAAUC,CAAV;AACA,SAAK,CAAL,IAAUC,CAAV;AACA,SAAK,CAAL,IAAUC,CAAV;AACA,WAAO,KAAKM,KAAL,EAAP;AACD;;AAEDH,EAAAA,IAAI,CAACI,KAAD,EAAsC;AACxC,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAO,KAAKD,KAAL,EAAP;AACD;;AAEDE,EAAAA,UAAU,CAACC,MAAD,EAA6D;AACrE,QAAIvB,MAAM,CAACkB,KAAX,EAAkB;AAChBhB,MAAAA,WAAW,CAACqB,MAAM,CAACZ,CAAR,CAAX;AACAT,MAAAA,WAAW,CAACqB,MAAM,CAACX,CAAR,CAAX;AACAV,MAAAA,WAAW,CAACqB,MAAM,CAACV,CAAR,CAAX;AACAX,MAAAA,WAAW,CAACqB,MAAM,CAACT,CAAR,CAAX;AACD;;AACD,SAAK,CAAL,IAAUS,MAAM,CAACZ,CAAjB;AACA,SAAK,CAAL,IAAUY,MAAM,CAACX,CAAjB;AACA,SAAK,CAAL,IAAUW,MAAM,CAACV,CAAjB;AACA,SAAK,CAAL,IAAUU,MAAM,CAACT,CAAjB;AACA,WAAO,IAAP;AACD;;AAEDU,EAAAA,QAAQ,CAACD,MAAD,EAKN;AACAA,IAAAA,MAAM,CAACZ,CAAP,GAAW,KAAK,CAAL,CAAX;AACAY,IAAAA,MAAM,CAACX,CAAP,GAAW,KAAK,CAAL,CAAX;AACAW,IAAAA,MAAM,CAACV,CAAP,GAAW,KAAK,CAAL,CAAX;AACAU,IAAAA,MAAM,CAACT,CAAP,GAAW,KAAK,CAAL,CAAX;AACA,WAAOS,MAAP;AAMD;;AAIW,MAARE,QAAQ,GAAW;AACrB,WAAO,CAAP;AACD;;AAEI,MAADZ,CAAC,GAAW;AACd,WAAO,KAAK,CAAL,CAAP;AACD;;AACI,MAADA,CAAC,CAACa,KAAD,EAAgB;AACnB,SAAK,CAAL,IAAUxB,WAAW,CAACwB,KAAD,CAArB;AACD;;AACI,MAADZ,CAAC,GAAW;AACd,WAAO,KAAK,CAAL,CAAP;AACD;;AACI,MAADA,CAAC,CAACY,KAAD,EAAgB;AACnB,SAAK,CAAL,IAAUxB,WAAW,CAACwB,KAAD,CAArB;AACD;;AAEDC,EAAAA,SAAS,CAACC,OAAD,EAAwC;AAC/CzB,IAAAA,IAAI,CAAC0B,aAAL,CAAmB,IAAnB,EAAyB,IAAzB,EAA+BD,OAA/B;AACA,WAAO,KAAKR,KAAL,EAAP;AACD;;AAEDU,EAAAA,kBAAkB,CAACC,OAAD,EAAwC;AACxD1B,IAAAA,kBAAkB,CAAC,IAAD,EAAO,IAAP,EAAa0B,OAAb,CAAlB;AACA,WAAO,KAAKX,KAAL,EAAP;AACD;;AAEDY,EAAAA,kBAAkB,CAACC,OAAD,EAAwC;AACxD7B,IAAAA,kBAAkB,CAAC,IAAD,EAAO,IAAP,EAAa6B,OAAb,CAAlB;AACA,WAAO,KAAKb,KAAL,EAAP;AACD;;AAEDc,EAAAA,qBAAqB,CAACC,UAAD,EAA2C;AAC9DhC,IAAAA,IAAI,CAACiC,aAAL,CAAmB,IAAnB,EAAyB,IAAzB,EAA+BD,UAA/B;AACA,WAAO,KAAKf,KAAL,EAAP;AACD;;AAGDiB,EAAAA,YAAY,CAACC,CAAD,EAAmB;AAC7BA,IAAAA,CAAC,CAACX,SAAF,CAAY,IAAZ,EAAkB,IAAlB;AACA,WAAO,IAAP;AACD;;AAxHyC", "sourcesContent": ["// Copyright (c) 2017 Uber Technologies, Inc.\n// MIT License\n\nimport Vector from './base/vector';\nimport {config, isArray} from '../lib/common';\nimport {checkNumber} from '../lib/validators';\nimport * as vec4 from 'gl-matrix/vec3';\n/* eslint-disable camelcase */\nimport {vec4_transformMat2, vec4_transformMat3} from '../lib/gl-matrix-extras';\nimport {NumericArray} from '@math.gl/types';\n\nimport type Matrix4 from './matrix4';\n\nlet ZERO: Vector4;\n\n/**\n * Four-element vector class.\n * Subclass of Array<number>\n */\nexport default class Vector4 extends Vector {\n  static get ZERO(): Vector4 {\n    if (!ZERO) {\n      ZERO = new Vector4(0, 0, 0, 0);\n      Object.freeze(ZERO);\n    }\n    return ZERO;\n  }\n\n  constructor(x: number | Readonly<NumericArray> = 0, y: number = 0, z: number = 0, w: number = 0) {\n    // PERF NOTE: initialize elements as double precision numbers\n    super(-0, -0, -0, -0);\n    if (isArray(x) && arguments.length === 1) {\n      this.copy(x as Readonly<NumericArray>);\n    } else {\n      // this.set(x, y, z, w);\n      if (config.debug) {\n        checkNumber(x);\n        checkNumber(y);\n        checkNumber(z);\n        checkNumber(w);\n      }\n      this[0] = x as number;\n      this[1] = y;\n      this[2] = z;\n      this[3] = w;\n    }\n  }\n\n  set(x: number, y: number, z: number, w: number): this {\n    this[0] = x;\n    this[1] = y;\n    this[2] = z;\n    this[3] = w;\n    return this.check();\n  }\n\n  copy(array: Readonly<NumericArray>): this {\n    this[0] = array[0];\n    this[1] = array[1];\n    this[2] = array[2];\n    this[3] = array[3];\n    return this.check();\n  }\n\n  fromObject(object: {x: number; y: number; z: number; w: number}): this {\n    if (config.debug) {\n      checkNumber(object.x);\n      checkNumber(object.y);\n      checkNumber(object.z);\n      checkNumber(object.w);\n    }\n    this[0] = object.x;\n    this[1] = object.y;\n    this[2] = object.z;\n    this[3] = object.w;\n    return this;\n  }\n\n  toObject(object: {x?: number; y?: number; z?: number; w?: number}): {\n    x: number;\n    y: number;\n    z: number;\n    w: number;\n  } {\n    object.x = this[0];\n    object.y = this[1];\n    object.z = this[2];\n    object.w = this[3];\n    return object as {\n      x: number;\n      y: number;\n      z: number;\n      w: number;\n    };\n  }\n\n  // Getters/setters\n  /* eslint-disable no-multi-spaces, brace-style, no-return-assign */\n  get ELEMENTS(): number {\n    return 4;\n  }\n\n  get z(): number {\n    return this[2];\n  }\n  set z(value: number) {\n    this[2] = checkNumber(value);\n  }\n  get w(): number {\n    return this[3];\n  }\n  set w(value: number) {\n    this[3] = checkNumber(value);\n  }\n\n  transform(matrix4: Readonly<NumericArray>): this {\n    vec4.transformMat4(this, this, matrix4);\n    return this.check();\n  }\n\n  transformByMatrix3(matrix3: Readonly<NumericArray>): this {\n    vec4_transformMat3(this, this, matrix3);\n    return this.check();\n  }\n\n  transformByMatrix2(matrix2: Readonly<NumericArray>): this {\n    vec4_transformMat2(this, this, matrix2);\n    return this.check();\n  }\n\n  transformByQuaternion(quaternion: Readonly<NumericArray>): this {\n    vec4.transformQuat(this, this, quaternion);\n    return this.check();\n  }\n\n  // three.js compatibility\n  applyMatrix4(m: Matrix4): this {\n    m.transform(this, this);\n    return this;\n  }\n}\n"], "file": "vector4.js"}