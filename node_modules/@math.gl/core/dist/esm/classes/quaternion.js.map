{"version": 3, "sources": ["../../../src/classes/quaternion.ts"], "names": ["MathArray", "checkNumber", "checkVector", "Vector4", "quat", "vec4", "IDENTITY_QUATERNION", "Quaternion", "constructor", "x", "y", "z", "w", "Array", "isArray", "arguments", "length", "copy", "set", "array", "check", "fromObject", "object", "fromMatrix3", "m", "fromMat3", "fromAxisRotation", "axis", "rad", "setAxisAngle", "identity", "ELEMENTS", "value", "len", "lengthSquared", "squared<PERSON>ength", "dot", "a", "rotationTo", "vectorA", "vectorB", "add", "calculateW", "conjugate", "invert", "lerp", "b", "t", "undefined", "multiplyRight", "multiply", "multiplyLeft", "normalize", "l", "rotateX", "rotateY", "rotateZ", "scale", "slerp", "arg0", "arg1", "arg2", "start", "target", "ratio", "transformVector4", "vector", "result", "transformQuat", "lengthSq", "setFromAxisAngle", "premultiply"], "mappings": "AAEA,OAAOA,SAAP,MAAsB,mBAAtB;AACA,SAAQC,WAAR,EAAqBC,WAArB,QAAuC,mBAAvC;AACA,OAAOC,OAAP,MAAoB,WAApB;AACA,OAAO,KAAKC,IAAZ,MAAsB,gBAAtB;AACA,OAAO,KAAKC,IAAZ,MAAsB,gBAAtB;AAGA,MAAMC,mBAAmB,GAAG,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,CAA5B;AAEA,eAAe,MAAMC,UAAN,SAAyBP,SAAzB,CAAmC;AAChDQ,EAAAA,WAAW,CAACC,CAAkC,GAAG,CAAtC,EAAyCC,CAAC,GAAG,CAA7C,EAAgDC,CAAC,GAAG,CAApD,EAAuDC,CAAC,GAAG,CAA3D,EAA8D;AAEvE,UAAM,CAAC,CAAP,EAAU,CAAC,CAAX,EAAc,CAAC,CAAf,EAAkB,CAAC,CAAnB;;AAEA,QAAIC,KAAK,CAACC,OAAN,CAAcL,CAAd,KAAoBM,SAAS,CAACC,MAAV,KAAqB,CAA7C,EAAgD;AAC9C,WAAKC,IAAL,CAAUR,CAAV;AACD,KAFD,MAEO;AACL,WAAKS,GAAL,CAAST,CAAT,EAAsBC,CAAtB,EAAyBC,CAAzB,EAA4BC,CAA5B;AACD;AACF;;AAEDK,EAAAA,IAAI,CAACE,KAAD,EAAsC;AACxC,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAO,KAAKC,KAAL,EAAP;AACD;;AAEDF,EAAAA,GAAG,CAACT,CAAD,EAAYC,CAAZ,EAAuBC,CAAvB,EAAkCC,CAAlC,EAAmD;AACpD,SAAK,CAAL,IAAUH,CAAV;AACA,SAAK,CAAL,IAAUC,CAAV;AACA,SAAK,CAAL,IAAUC,CAAV;AACA,SAAK,CAAL,IAAUC,CAAV;AACA,WAAO,KAAKQ,KAAL,EAAP;AACD;;AAEDC,EAAAA,UAAU,CAACC,MAAD,EAA6D;AACrE,SAAK,CAAL,IAAUA,MAAM,CAACb,CAAjB;AACA,SAAK,CAAL,IAAUa,MAAM,CAACZ,CAAjB;AACA,SAAK,CAAL,IAAUY,MAAM,CAACX,CAAjB;AACA,SAAK,CAAL,IAAUW,MAAM,CAACV,CAAjB;AACA,WAAO,KAAKQ,KAAL,EAAP;AACD;;AASDG,EAAAA,WAAW,CAACC,CAAD,EAAkC;AAC3CpB,IAAAA,IAAI,CAACqB,QAAL,CAAc,IAAd,EAAoBD,CAApB;AACA,WAAO,KAAKJ,KAAL,EAAP;AACD;;AAEDM,EAAAA,gBAAgB,CAACC,IAAD,EAA+BC,GAA/B,EAAkD;AAChExB,IAAAA,IAAI,CAACyB,YAAL,CAAkB,IAAlB,EAAwBF,IAAxB,EAA8BC,GAA9B;AACA,WAAO,KAAKR,KAAL,EAAP;AACD;;AAGDU,EAAAA,QAAQ,GAAS;AACf1B,IAAAA,IAAI,CAAC0B,QAAL,CAAc,IAAd;AACA,WAAO,KAAKV,KAAL,EAAP;AACD;;AASDS,EAAAA,YAAY,CAACF,IAAD,EAA+BC,GAA/B,EAAkD;AAC5D,WAAO,KAAKF,gBAAL,CAAsBC,IAAtB,EAA4BC,GAA5B,CAAP;AACD;;AAGW,MAARG,QAAQ,GAAW;AACrB,WAAO,CAAP;AACD;;AAEI,MAADtB,CAAC,GAAW;AACd,WAAO,KAAK,CAAL,CAAP;AACD;;AACI,MAADA,CAAC,CAACuB,KAAD,EAAgB;AACnB,SAAK,CAAL,IAAU/B,WAAW,CAAC+B,KAAD,CAArB;AACD;;AAEI,MAADtB,CAAC,GAAW;AACd,WAAO,KAAK,CAAL,CAAP;AACD;;AACI,MAADA,CAAC,CAACsB,KAAD,EAAgB;AACnB,SAAK,CAAL,IAAU/B,WAAW,CAAC+B,KAAD,CAArB;AACD;;AAEI,MAADrB,CAAC,GAAW;AACd,WAAO,KAAK,CAAL,CAAP;AACD;;AACI,MAADA,CAAC,CAACqB,KAAD,EAAgB;AACnB,SAAK,CAAL,IAAU/B,WAAW,CAAC+B,KAAD,CAArB;AACD;;AAEI,MAADpB,CAAC,GAAW;AACd,WAAO,KAAK,CAAL,CAAP;AACD;;AACI,MAADA,CAAC,CAACoB,KAAD,EAAgB;AACnB,SAAK,CAAL,IAAU/B,WAAW,CAAC+B,KAAD,CAArB;AACD;;AAGDC,EAAAA,GAAG,GAAW;AACZ,WAAO7B,IAAI,CAACY,MAAL,CAAY,IAAZ,CAAP;AACD;;AAGDkB,EAAAA,aAAa,GAAW;AACtB,WAAO9B,IAAI,CAAC+B,aAAL,CAAmB,IAAnB,CAAP;AACD;;AAIDC,EAAAA,GAAG,CAACC,CAAD,EAAoC;AACrC,WAAOjC,IAAI,CAACgC,GAAL,CAAS,IAAT,EAAeC,CAAf,CAAP;AACD;;AAkBDC,EAAAA,UAAU,CAACC,OAAD,EAAwBC,OAAxB,EAAqD;AAC7DpC,IAAAA,IAAI,CAACkC,UAAL,CAAgB,IAAhB,EAAsBC,OAAtB,EAA+BC,OAA/B;AACA,WAAO,KAAKpB,KAAL,EAAP;AACD;;AAaDqB,EAAAA,GAAG,CAACJ,CAAD,EAAkC;AACnCjC,IAAAA,IAAI,CAACqC,GAAL,CAAS,IAAT,EAAe,IAAf,EAAqBJ,CAArB;AACA,WAAO,KAAKjB,KAAL,EAAP;AACD;;AAIDsB,EAAAA,UAAU,GAAS;AACjBtC,IAAAA,IAAI,CAACsC,UAAL,CAAgB,IAAhB,EAAsB,IAAtB;AACA,WAAO,KAAKtB,KAAL,EAAP;AACD;;AAIDuB,EAAAA,SAAS,GAAS;AAChBvC,IAAAA,IAAI,CAACuC,SAAL,CAAe,IAAf,EAAqB,IAArB;AACA,WAAO,KAAKvB,KAAL,EAAP;AACD;;AAGDwB,EAAAA,MAAM,GAAS;AACbxC,IAAAA,IAAI,CAACwC,MAAL,CAAY,IAAZ,EAAkB,IAAlB;AACA,WAAO,KAAKxB,KAAL,EAAP;AACD;;AAGDyB,EAAAA,IAAI,CAACR,CAAD,EAA4BS,CAA5B,EAAgEC,CAAhE,EAAkF;AACpF,QAAIA,CAAC,KAAKC,SAAV,EAAqB;AACnB,aAAO,KAAKH,IAAL,CAAU,IAAV,EAAgBR,CAAhB,EAAmBS,CAAnB,CAAP;AACD;;AACD1C,IAAAA,IAAI,CAACyC,IAAL,CAAU,IAAV,EAAgBR,CAAhB,EAAmBS,CAAnB,EAAsCC,CAAtC;AACA,WAAO,KAAK3B,KAAL,EAAP;AACD;;AAGD6B,EAAAA,aAAa,CAACZ,CAAD,EAAkC;AAC7CjC,IAAAA,IAAI,CAAC8C,QAAL,CAAc,IAAd,EAAoB,IAApB,EAA0Bb,CAA1B;AACA,WAAO,KAAKjB,KAAL,EAAP;AACD;;AAED+B,EAAAA,YAAY,CAACd,CAAD,EAAkC;AAC5CjC,IAAAA,IAAI,CAAC8C,QAAL,CAAc,IAAd,EAAoBb,CAApB,EAAuB,IAAvB;AACA,WAAO,KAAKjB,KAAL,EAAP;AACD;;AAGDgC,EAAAA,SAAS,GAAS;AAEhB,UAAMpC,MAAM,GAAG,KAAKiB,GAAL,EAAf;AACA,UAAMoB,CAAC,GAAGrC,MAAM,GAAG,CAAT,GAAa,IAAIA,MAAjB,GAA0B,CAApC;AACA,SAAK,CAAL,IAAU,KAAK,CAAL,IAAUqC,CAApB;AACA,SAAK,CAAL,IAAU,KAAK,CAAL,IAAUA,CAApB;AACA,SAAK,CAAL,IAAU,KAAK,CAAL,IAAUA,CAApB;AACA,SAAK,CAAL,IAAU,KAAK,CAAL,IAAUA,CAApB;;AAEA,QAAIrC,MAAM,KAAK,CAAf,EAAkB;AAChB,WAAK,CAAL,IAAU,CAAV;AACD;;AACD,WAAO,KAAKI,KAAL,EAAP;AACD;;AAGDkC,EAAAA,OAAO,CAAC1B,GAAD,EAAoB;AACzBxB,IAAAA,IAAI,CAACkD,OAAL,CAAa,IAAb,EAAmB,IAAnB,EAAyB1B,GAAzB;AACA,WAAO,KAAKR,KAAL,EAAP;AACD;;AAGDmC,EAAAA,OAAO,CAAC3B,GAAD,EAAoB;AACzBxB,IAAAA,IAAI,CAACmD,OAAL,CAAa,IAAb,EAAmB,IAAnB,EAAyB3B,GAAzB;AACA,WAAO,KAAKR,KAAL,EAAP;AACD;;AAGDoC,EAAAA,OAAO,CAAC5B,GAAD,EAAoB;AACzBxB,IAAAA,IAAI,CAACoD,OAAL,CAAa,IAAb,EAAmB,IAAnB,EAAyB5B,GAAzB;AACA,WAAO,KAAKR,KAAL,EAAP;AACD;;AAGDqC,EAAAA,KAAK,CAACX,CAAD,EAAkB;AACrB1C,IAAAA,IAAI,CAACqD,KAAL,CAAW,IAAX,EAAiB,IAAjB,EAAuBX,CAAvB;AACA,WAAO,KAAK1B,KAAL,EAAP;AACD;;AAWDsC,EAAAA,KAAK,CACHC,IADG,EAQHC,IARG,EASHC,IATG,EAUG;AACN,QAAIC,KAAJ;AACA,QAAIC,MAAJ;AACA,QAAIC,KAAJ;;AAEA,YAAQjD,SAAS,CAACC,MAAlB;AACE,WAAK,CAAL;AAEE,SAAC;AACC8C,UAAAA,KAAK,GAAGxD,mBADT;AAECyD,UAAAA,MAFD;AAGCC,UAAAA;AAHD,YAIGL,IAJJ;AASA;;AACF,WAAK,CAAL;AACEG,QAAAA,KAAK,GAAG,IAAR;AACAC,QAAAA,MAAM,GAAGJ,IAAT;AACAK,QAAAA,KAAK,GAAGJ,IAAR;AACA;;AACF;AAEEE,QAAAA,KAAK,GAAGH,IAAR;AACAI,QAAAA,MAAM,GAAGH,IAAT;AACAI,QAAAA,KAAK,GAAGH,IAAR;AAtBJ;;AAwBAzD,IAAAA,IAAI,CAACsD,KAAL,CAAW,IAAX,EAAiBI,KAAjB,EAAwBC,MAAxB,EAAgCC,KAAhC;AACA,WAAO,KAAK5C,KAAL,EAAP;AACD;;AAED6C,EAAAA,gBAAgB,CACdC,MADc,EAEdC,MAAoB,GAAG,IAAIhE,OAAJ,EAFT,EAGA;AACdE,IAAAA,IAAI,CAAC+D,aAAL,CAAmBD,MAAnB,EAA2BD,MAA3B,EAAmC,IAAnC;AACA,WAAOhE,WAAW,CAACiE,MAAD,EAAS,CAAT,CAAlB;AACD;;AAGDE,EAAAA,QAAQ,GAAW;AACjB,WAAO,KAAKnC,aAAL,EAAP;AACD;;AAEDoC,EAAAA,gBAAgB,CAAC3C,IAAD,EAA+BC,GAA/B,EAAkD;AAChE,WAAO,KAAKC,YAAL,CAAkBF,IAAlB,EAAwBC,GAAxB,CAAP;AACD;;AAED2C,EAAAA,WAAW,CAAClC,CAAD,EAAkC;AAC3C,WAAO,KAAKc,YAAL,CAAkBd,CAAlB,CAAP;AACD;;AAEDa,EAAAA,QAAQ,CAACb,CAAD,EAAkC;AACxC,WAAO,KAAKY,aAAL,CAAmBZ,CAAnB,CAAP;AACD;;AAvT+C", "sourcesContent": ["// Copyright (c) 2017 Uber Technologies, Inc.\n// MIT License\nimport MathArray from './base/math-array';\nimport {checkNumber, checkVector} from '../lib/validators';\nimport Vector4 from './vector4';\nimport * as quat from 'gl-matrix/quat';\nimport * as vec4 from 'gl-matrix/vec4';\nimport {NumericArray} from '@math.gl/types';\n\nconst IDENTITY_QUATERNION = [0, 0, 0, 1] as const;\n\nexport default class Quaternion extends MathArray {\n  constructor(x: number | Readonly<NumericArray> = 0, y = 0, z = 0, w = 1) {\n    // PERF NOTE: initialize elements as double precision numbers\n    super(-0, -0, -0, -0);\n    // eslint-disable-next-line prefer-rest-params\n    if (Array.isArray(x) && arguments.length === 1) {\n      this.copy(x);\n    } else {\n      this.set(x as number, y, z, w);\n    }\n  }\n\n  copy(array: Readonly<NumericArray>): this {\n    this[0] = array[0];\n    this[1] = array[1];\n    this[2] = array[2];\n    this[3] = array[3];\n    return this.check();\n  }\n\n  set(x: number, y: number, z: number, w: number): this {\n    this[0] = x;\n    this[1] = y;\n    this[2] = z;\n    this[3] = w;\n    return this.check();\n  }\n\n  fromObject(object: {x: number; y: number; z: number; w: number}): this {\n    this[0] = object.x;\n    this[1] = object.y;\n    this[2] = object.z;\n    this[3] = object.w;\n    return this.check();\n  }\n\n  /**\n   * Creates a quaternion from the given 3x3 rotation matrix.\n   * NOTE: The resultant quaternion is not normalized, so you should\n   * be sure to renormalize the quaternion yourself where necessary.\n   * @param m\n   * @returns\n   */\n  fromMatrix3(m: Readonly<NumericArray>): this {\n    quat.fromMat3(this, m);\n    return this.check();\n  }\n\n  fromAxisRotation(axis: Readonly<NumericArray>, rad: number): this {\n    quat.setAxisAngle(this, axis, rad);\n    return this.check();\n  }\n\n  /** Set a quat to the identity quaternion */\n  identity(): this {\n    quat.identity(this);\n    return this.check();\n  }\n\n  // Set the components of a quat to the given values\n  // set(i, j, k, l) {\n  //   quat.set(this, i, j, k, l);\n  //   return this.check();\n  // }\n\n  // Sets a quat from the given angle and rotation axis, then returns it.\n  setAxisAngle(axis: Readonly<NumericArray>, rad: number): this {\n    return this.fromAxisRotation(axis, rad);\n  }\n\n  // Getters/setters\n  get ELEMENTS(): number {\n    return 4;\n  }\n\n  get x(): number {\n    return this[0];\n  }\n  set x(value: number) {\n    this[0] = checkNumber(value);\n  }\n\n  get y(): number {\n    return this[1];\n  }\n  set y(value: number) {\n    this[1] = checkNumber(value);\n  }\n\n  get z(): number {\n    return this[2];\n  }\n  set z(value: number) {\n    this[2] = checkNumber(value);\n  }\n\n  get w(): number {\n    return this[3];\n  }\n  set w(value: number) {\n    this[3] = checkNumber(value);\n  }\n\n  // Calculates the length of a quat\n  len(): number {\n    return quat.length(this);\n  }\n\n  // Calculates the squared length of a quat\n  lengthSquared(): number {\n    return quat.squaredLength(this);\n  }\n\n  // Calculates the dot product of two quat's\n  // @return {Number}\n  dot(a: Readonly<NumericArray>): number {\n    return quat.dot(this, a);\n  }\n\n  // Gets the rotation axis and angle for a given quaternion.\n  // If a quaternion is created with setAxisAngle, this method will\n  // return the same values as providied in the original parameter\n  // list OR functionally equivalent values.\n  // Example: The quaternion formed by axis [0, 0, 1] and angle -90\n  // is the same as the quaternion formed by [0, 0, 1] and 270.\n  // This method favors the latter.\n  // @return {{[x,y,z], Number}}\n  // getAxisAngle() {\n  //   const axis = [];\n  //   const angle = quat.getAxisAngle(axis, this);\n  //   return {axis, angle};\n  // }\n  // MODIFIERS\n  // Sets a quaternion to represent the shortest rotation from one vector\n  // to another. Both vectors are assumed to be unit length.\n  rotationTo(vectorA: NumericArray, vectorB: NumericArray): this {\n    quat.rotationTo(this, vectorA, vectorB);\n    return this.check();\n  }\n\n  // Sets the specified quaternion with values corresponding to the given axes.\n  // Each axis is a vec3 and is expected to be unit length and perpendicular\n  // to all other specified axes.\n  // setAxes() {\n  //   Number\n  // }\n  // Performs a spherical linear interpolation with two control points\n  // sqlerp() {\n  //   Number;\n  // }\n  // Adds two quat's\n  add(a: Readonly<NumericArray>): this {\n    quat.add(this, this, a);\n    return this.check();\n  }\n\n  // Calculates the W component of a quat from the X, Y, and Z components.\n  // Any existing W component will be ignored.\n  calculateW(): this {\n    quat.calculateW(this, this);\n    return this.check();\n  }\n\n  // Calculates the conjugate of a quat If the quaternion is normalized,\n  // this function is faster than quat.inverse and produces the same result.\n  conjugate(): this {\n    quat.conjugate(this, this);\n    return this.check();\n  }\n\n  // Calculates the inverse of a quat\n  invert(): this {\n    quat.invert(this, this);\n    return this.check();\n  }\n\n  // Performs a linear interpolation between two quat's\n  lerp(a: Readonly<NumericArray>, b: Readonly<NumericArray> | number, t?: number): this {\n    if (t === undefined) {\n      return this.lerp(this, a, b as number);\n    }\n    quat.lerp(this, a, b as NumericArray, t);\n    return this.check();\n  }\n\n  // Multiplies two quat's\n  multiplyRight(a: Readonly<NumericArray>): this {\n    quat.multiply(this, this, a);\n    return this.check();\n  }\n\n  multiplyLeft(a: Readonly<NumericArray>): this {\n    quat.multiply(this, a, this);\n    return this.check();\n  }\n\n  // Normalize a quat\n  normalize(): this {\n    // Handle 0 case\n    const length = this.len();\n    const l = length > 0 ? 1 / length : 0;\n    this[0] = this[0] * l;\n    this[1] = this[1] * l;\n    this[2] = this[2] * l;\n    this[3] = this[3] * l;\n    // Set to [0, 0, 0, 1] if length is 0\n    if (length === 0) {\n      this[3] = 1;\n    }\n    return this.check();\n  }\n\n  // Rotates a quaternion by the given angle about the X axis\n  rotateX(rad: number): this {\n    quat.rotateX(this, this, rad);\n    return this.check();\n  }\n\n  // Rotates a quaternion by the given angle about the Y axis\n  rotateY(rad: number): this {\n    quat.rotateY(this, this, rad);\n    return this.check();\n  }\n\n  // Rotates a quaternion by the given angle about the Z axis\n  rotateZ(rad: number): this {\n    quat.rotateZ(this, this, rad);\n    return this.check();\n  }\n\n  // Scales a quat by a scalar number\n  scale(b: number): this {\n    quat.scale(this, this, b);\n    return this.check();\n  }\n\n  slerp(target: Readonly<NumericArray>, ratio: number): this;\n  slerp(start: Readonly<NumericArray>, target: Readonly<NumericArray>, ratio: number): this;\n  slerp(params: {\n    start: Readonly<NumericArray>;\n    target: Readonly<NumericArray>;\n    ratio: number;\n  }): this;\n\n  // Performs a spherical linear interpolation between two quat\n  slerp(\n    arg0:\n      | Readonly<NumericArray>\n      | {\n          start: Readonly<NumericArray>;\n          target: Readonly<NumericArray>;\n          ratio: number;\n        },\n    arg1?: Readonly<NumericArray> | number,\n    arg2?: number\n  ): this {\n    let start: Readonly<NumericArray>;\n    let target: Readonly<NumericArray>;\n    let ratio: number;\n    // eslint-disable-next-line prefer-rest-params\n    switch (arguments.length) {\n      case 1: // Deprecated signature ({start, target, ratio})\n        // eslint-disable-next-line prefer-rest-params\n        ({\n          start = IDENTITY_QUATERNION,\n          target,\n          ratio\n        } = arg0 as {\n          start: Readonly<NumericArray>;\n          target: Readonly<NumericArray>;\n          ratio: number;\n        });\n        break;\n      case 2: // THREE.js compatibility signature (target, ration)\n        start = this; // eslint-disable-line\n        target = arg0 as Readonly<NumericArray>;\n        ratio = arg1 as number;\n        break;\n      default:\n        // Default signature: (start, target, ratio)\n        start = arg0 as Readonly<NumericArray>;\n        target = arg1 as Readonly<NumericArray>;\n        ratio = arg2;\n    }\n    quat.slerp(this, start, target, ratio);\n    return this.check();\n  }\n\n  transformVector4(\n    vector: Readonly<NumericArray>,\n    result: NumericArray = new Vector4()\n  ): NumericArray {\n    vec4.transformQuat(result, vector, this);\n    return checkVector(result, 4);\n  }\n\n  // THREE.js Math API compatibility\n  lengthSq(): number {\n    return this.lengthSquared();\n  }\n\n  setFromAxisAngle(axis: Readonly<NumericArray>, rad: number): this {\n    return this.setAxisAngle(axis, rad);\n  }\n\n  premultiply(a: Readonly<NumericArray>): this {\n    return this.multiplyLeft(a);\n  }\n\n  multiply(a: Readonly<NumericArray>): this {\n    return this.multiplyRight(a);\n  }\n}\n"], "file": "quaternion.js"}