{"version": 3, "sources": ["../../../src/classes/vector3.ts"], "names": ["Vector", "config", "isArray", "checkNumber", "vec3", "vec3_transformMat2", "vec3_transformMat4AsVector", "ORIGIN", "ZERO", "Vector3", "Object", "freeze", "constructor", "x", "y", "z", "arguments", "length", "copy", "debug", "set", "check", "array", "fromObject", "object", "toObject", "ELEMENTS", "value", "angle", "vector", "cross", "rotateX", "radians", "origin", "rotateY", "rotateZ", "transform", "matrix4", "transformAsPoint", "transformMat4", "transformAsVector", "transformByMatrix3", "matrix3", "transformMat3", "transformByMatrix2", "matrix2", "transformByQuaternion", "quaternion", "transformQuat"], "mappings": "AAGA,OAAOA,MAAP,MAAmB,eAAnB;AACA,SAAQC,MAAR,EAAgBC,OAAhB,QAA8B,eAA9B;AACA,SAAQC,WAAR,QAA0B,mBAA1B;AACA,OAAO,KAAKC,IAAZ,MAAsB,gBAAtB;AAEA,SAAQC,kBAAR,EAA4BC,0BAA5B,QAA6D,yBAA7D;AAEA,MAAMC,MAAM,GAAG,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAAf;AAEA,IAAIC,IAAJ;AAMA,eAAe,MAAMC,OAAN,SAAsBT,MAAtB,CAA6B;AAC3B,aAAJQ,IAAI,GAAY;AACzB,QAAI,CAACA,IAAL,EAAW;AACTA,MAAAA,IAAI,GAAG,IAAIC,OAAJ,CAAY,CAAZ,EAAe,CAAf,EAAkB,CAAlB,CAAP;AACAC,MAAAA,MAAM,CAACC,MAAP,CAAcH,IAAd;AACD;;AACD,WAAOA,IAAP;AACD;;AAQDI,EAAAA,WAAW,CAACC,CAAkC,GAAG,CAAtC,EAAyCC,CAAS,GAAG,CAArD,EAAwDC,CAAS,GAAG,CAApE,EAAuE;AAEhF,UAAM,CAAC,CAAP,EAAU,CAAC,CAAX,EAAc,CAAC,CAAf;;AACA,QAAIC,SAAS,CAACC,MAAV,KAAqB,CAArB,IAA0Bf,OAAO,CAACW,CAAD,CAArC,EAA0C;AACxC,WAAKK,IAAL,CAAUL,CAAV;AACD,KAFD,MAEO;AAEL,UAAIZ,MAAM,CAACkB,KAAX,EAAkB;AAChBhB,QAAAA,WAAW,CAACU,CAAD,CAAX;AACAV,QAAAA,WAAW,CAACW,CAAD,CAAX;AACAX,QAAAA,WAAW,CAACY,CAAD,CAAX;AACD;;AAED,WAAK,CAAL,IAAUF,CAAV;AACA,WAAK,CAAL,IAAUC,CAAV;AACA,WAAK,CAAL,IAAUC,CAAV;AACD;AACF;;AAEDK,EAAAA,GAAG,CAACP,CAAD,EAAYC,CAAZ,EAAuBC,CAAvB,EAAwC;AACzC,SAAK,CAAL,IAAUF,CAAV;AACA,SAAK,CAAL,IAAUC,CAAV;AACA,SAAK,CAAL,IAAUC,CAAV;AACA,WAAO,KAAKM,KAAL,EAAP;AACD;;AAEDH,EAAAA,IAAI,CAACI,KAAD,EAAsC;AACxC,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,SAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAO,KAAKD,KAAL,EAAP;AACD;;AAEDE,EAAAA,UAAU,CAACC,MAAD,EAAkD;AAC1D,QAAIvB,MAAM,CAACkB,KAAX,EAAkB;AAChBhB,MAAAA,WAAW,CAACqB,MAAM,CAACX,CAAR,CAAX;AACAV,MAAAA,WAAW,CAACqB,MAAM,CAACV,CAAR,CAAX;AACAX,MAAAA,WAAW,CAACqB,MAAM,CAACT,CAAR,CAAX;AACD;;AACD,SAAK,CAAL,IAAUS,MAAM,CAACX,CAAjB;AACA,SAAK,CAAL,IAAUW,MAAM,CAACV,CAAjB;AACA,SAAK,CAAL,IAAUU,MAAM,CAACT,CAAjB;AACA,WAAO,KAAKM,KAAL,EAAP;AACD;;AAEDI,EAAAA,QAAQ,CAACD,MAAD,EAAkF;AACxFA,IAAAA,MAAM,CAACX,CAAP,GAAW,KAAK,CAAL,CAAX;AACAW,IAAAA,MAAM,CAACV,CAAP,GAAW,KAAK,CAAL,CAAX;AACAU,IAAAA,MAAM,CAACT,CAAP,GAAW,KAAK,CAAL,CAAX;AACA,WAAOS,MAAP;AACD;;AAIW,MAARE,QAAQ,GAAW;AACrB,WAAO,CAAP;AACD;;AACI,MAADX,CAAC,GAAW;AACd,WAAO,KAAK,CAAL,CAAP;AACD;;AACI,MAADA,CAAC,CAACY,KAAD,EAAgB;AACnB,SAAK,CAAL,IAAUxB,WAAW,CAACwB,KAAD,CAArB;AACD;;AAIDC,EAAAA,KAAK,CAACC,MAAD,EAAyC;AAC5C,WAAOzB,IAAI,CAACwB,KAAL,CAAW,IAAX,EAAiBC,MAAjB,CAAP;AACD;;AAIDC,EAAAA,KAAK,CAACD,MAAD,EAAuC;AAC1CzB,IAAAA,IAAI,CAAC0B,KAAL,CAAW,IAAX,EAAiB,IAAjB,EAAuBD,MAAvB;AACA,WAAO,KAAKR,KAAL,EAAP;AACD;;AAEDU,EAAAA,OAAO,CAAC;AAACC,IAAAA,OAAD;AAAUC,IAAAA,MAAM,GAAG1B;AAAnB,GAAD,EAAuF;AAC5FH,IAAAA,IAAI,CAAC2B,OAAL,CAAa,IAAb,EAAmB,IAAnB,EAAyBE,MAAzB,EAAiCD,OAAjC;AACA,WAAO,KAAKX,KAAL,EAAP;AACD;;AAEDa,EAAAA,OAAO,CAAC;AAACF,IAAAA,OAAD;AAAUC,IAAAA,MAAM,GAAG1B;AAAnB,GAAD,EAAuF;AAC5FH,IAAAA,IAAI,CAAC8B,OAAL,CAAa,IAAb,EAAmB,IAAnB,EAAyBD,MAAzB,EAAiCD,OAAjC;AACA,WAAO,KAAKX,KAAL,EAAP;AACD;;AAEDc,EAAAA,OAAO,CAAC;AAACH,IAAAA,OAAD;AAAUC,IAAAA,MAAM,GAAG1B;AAAnB,GAAD,EAAuF;AAC5FH,IAAAA,IAAI,CAAC+B,OAAL,CAAa,IAAb,EAAmB,IAAnB,EAAyBF,MAAzB,EAAiCD,OAAjC;AACA,WAAO,KAAKX,KAAL,EAAP;AACD;;AAKDe,EAAAA,SAAS,CAACC,OAAD,EAAwC;AAC/C,WAAO,KAAKC,gBAAL,CAAsBD,OAAtB,CAAP;AACD;;AAGDC,EAAAA,gBAAgB,CAACD,OAAD,EAAwC;AACtDjC,IAAAA,IAAI,CAACmC,aAAL,CAAmB,IAAnB,EAAyB,IAAzB,EAA+BF,OAA/B;AACA,WAAO,KAAKhB,KAAL,EAAP;AACD;;AAGDmB,EAAAA,iBAAiB,CAACH,OAAD,EAAwC;AACvD/B,IAAAA,0BAA0B,CAAC,IAAD,EAAO,IAAP,EAAa+B,OAAb,CAA1B;AACA,WAAO,KAAKhB,KAAL,EAAP;AACD;;AAEDoB,EAAAA,kBAAkB,CAACC,OAAD,EAAwC;AACxDtC,IAAAA,IAAI,CAACuC,aAAL,CAAmB,IAAnB,EAAyB,IAAzB,EAA+BD,OAA/B;AACA,WAAO,KAAKrB,KAAL,EAAP;AACD;;AAEDuB,EAAAA,kBAAkB,CAACC,OAAD,EAAwC;AACxDxC,IAAAA,kBAAkB,CAAC,IAAD,EAAO,IAAP,EAAawC,OAAb,CAAlB;AACA,WAAO,KAAKxB,KAAL,EAAP;AACD;;AAEDyB,EAAAA,qBAAqB,CAACC,UAAD,EAA2C;AAC9D3C,IAAAA,IAAI,CAAC4C,aAAL,CAAmB,IAAnB,EAAyB,IAAzB,EAA+BD,UAA/B;AACA,WAAO,KAAK1B,KAAL,EAAP;AACD;;AA3IyC", "sourcesContent": ["// Copyright (c) 2017 Uber Technologies, Inc.\n// MIT License\nimport {NumericArray} from '@math.gl/types';\nimport Vector from './base/vector';\nimport {config, isArray} from '../lib/common';\nimport {checkNumber} from '../lib/validators';\nimport * as vec3 from 'gl-matrix/vec3';\n/* eslint-disable camelcase */\nimport {vec3_transformMat2, vec3_transformMat4AsVector} from '../lib/gl-matrix-extras';\n\nconst ORIGIN = [0, 0, 0];\n\nlet ZERO: Vector3;\n\n/**\n * Three-element vector class.\n * Subclass of Array<number>\n */\nexport default class Vector3 extends Vector {\n  static get ZERO(): Vector3 {\n    if (!ZERO) {\n      ZERO = new Vector3(0, 0, 0);\n      Object.freeze(ZERO);\n    }\n    return ZERO;\n  }\n\n  /**\n   * @class\n   * @param x\n   * @param y\n   * @param z\n   */\n  constructor(x: number | Readonly<NumericArray> = 0, y: number = 0, z: number = 0) {\n    // PERF NOTE: initialize elements as double precision numbers\n    super(-0, -0, -0);\n    if (arguments.length === 1 && isArray(x)) {\n      this.copy(x as NumericArray);\n    } else {\n      // this.set(x, y, z);\n      if (config.debug) {\n        checkNumber(x);\n        checkNumber(y);\n        checkNumber(z);\n      }\n      // @ts-expect-error TS2412: Property '0' of type 'number | [number, number, number]' is not assignable to numeric index type 'number'\n      this[0] = x;\n      this[1] = y;\n      this[2] = z;\n    }\n  }\n\n  set(x: number, y: number, z: number): this {\n    this[0] = x;\n    this[1] = y;\n    this[2] = z;\n    return this.check();\n  }\n\n  copy(array: Readonly<NumericArray>): this {\n    this[0] = array[0];\n    this[1] = array[1];\n    this[2] = array[2];\n    return this.check();\n  }\n\n  fromObject(object: {x: number; y: number; z: number}): this {\n    if (config.debug) {\n      checkNumber(object.x);\n      checkNumber(object.y);\n      checkNumber(object.z);\n    }\n    this[0] = object.x;\n    this[1] = object.y;\n    this[2] = object.z;\n    return this.check();\n  }\n\n  toObject(object: {x?: number; y?: number; z?: number}): {x: number; y: number; z: number} {\n    object.x = this[0];\n    object.y = this[1];\n    object.z = this[2];\n    return object as {x: number; y: number; z: number};\n  }\n\n  // Getters/setters\n\n  get ELEMENTS(): number {\n    return 3;\n  }\n  get z(): number {\n    return this[2];\n  }\n  set z(value: number) {\n    this[2] = checkNumber(value);\n  }\n\n  // ACCESSORS\n\n  angle(vector: Readonly<NumericArray>): number {\n    return vec3.angle(this, vector);\n  }\n\n  // MODIFIERS\n\n  cross(vector: Readonly<NumericArray>): this {\n    vec3.cross(this, this, vector);\n    return this.check();\n  }\n\n  rotateX({radians, origin = ORIGIN}: {radians: number; origin?: Readonly<NumericArray>}): this {\n    vec3.rotateX(this, this, origin, radians);\n    return this.check();\n  }\n\n  rotateY({radians, origin = ORIGIN}: {radians: number; origin?: Readonly<NumericArray>}): this {\n    vec3.rotateY(this, this, origin, radians);\n    return this.check();\n  }\n\n  rotateZ({radians, origin = ORIGIN}: {radians: number; origin?: Readonly<NumericArray>}): this {\n    vec3.rotateZ(this, this, origin, radians);\n    return this.check();\n  }\n\n  // Transforms\n\n  // transforms as point (4th component is implicitly 1)\n  transform(matrix4: Readonly<NumericArray>): this {\n    return this.transformAsPoint(matrix4);\n  }\n\n  // transforms as point (4th component is implicitly 1)\n  transformAsPoint(matrix4: Readonly<NumericArray>): this {\n    vec3.transformMat4(this, this, matrix4);\n    return this.check();\n  }\n\n  // transforms as vector  (4th component is implicitly 0, ignores translation. slightly faster)\n  transformAsVector(matrix4: Readonly<NumericArray>): this {\n    vec3_transformMat4AsVector(this, this, matrix4);\n    return this.check();\n  }\n\n  transformByMatrix3(matrix3: Readonly<NumericArray>): this {\n    vec3.transformMat3(this, this, matrix3);\n    return this.check();\n  }\n\n  transformByMatrix2(matrix2: Readonly<NumericArray>): this {\n    vec3_transformMat2(this, this, matrix2);\n    return this.check();\n  }\n\n  transformByQuaternion(quaternion: Readonly<NumericArray>): this {\n    vec3.transformQuat(this, this, quaternion);\n    return this.check();\n  }\n}\n"], "file": "vector3.js"}