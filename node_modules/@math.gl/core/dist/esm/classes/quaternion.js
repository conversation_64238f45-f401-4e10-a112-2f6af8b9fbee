import MathArray from './base/math-array';
import { checkNumber, checkVector } from '../lib/validators';
import Vector4 from './vector4';
import * as quat from 'gl-matrix/quat';
import * as vec4 from 'gl-matrix/vec4';
const IDENTITY_QUATERNION = [0, 0, 0, 1];
export default class Quaternion extends MathArray {
  constructor(x = 0, y = 0, z = 0, w = 1) {
    super(-0, -0, -0, -0);

    if (Array.isArray(x) && arguments.length === 1) {
      this.copy(x);
    } else {
      this.set(x, y, z, w);
    }
  }

  copy(array) {
    this[0] = array[0];
    this[1] = array[1];
    this[2] = array[2];
    this[3] = array[3];
    return this.check();
  }

  set(x, y, z, w) {
    this[0] = x;
    this[1] = y;
    this[2] = z;
    this[3] = w;
    return this.check();
  }

  fromObject(object) {
    this[0] = object.x;
    this[1] = object.y;
    this[2] = object.z;
    this[3] = object.w;
    return this.check();
  }

  fromMatrix3(m) {
    quat.fromMat3(this, m);
    return this.check();
  }

  fromAxisRotation(axis, rad) {
    quat.setAxisAngle(this, axis, rad);
    return this.check();
  }

  identity() {
    quat.identity(this);
    return this.check();
  }

  setAxisAngle(axis, rad) {
    return this.fromAxisRotation(axis, rad);
  }

  get ELEMENTS() {
    return 4;
  }

  get x() {
    return this[0];
  }

  set x(value) {
    this[0] = checkNumber(value);
  }

  get y() {
    return this[1];
  }

  set y(value) {
    this[1] = checkNumber(value);
  }

  get z() {
    return this[2];
  }

  set z(value) {
    this[2] = checkNumber(value);
  }

  get w() {
    return this[3];
  }

  set w(value) {
    this[3] = checkNumber(value);
  }

  len() {
    return quat.length(this);
  }

  lengthSquared() {
    return quat.squaredLength(this);
  }

  dot(a) {
    return quat.dot(this, a);
  }

  rotationTo(vectorA, vectorB) {
    quat.rotationTo(this, vectorA, vectorB);
    return this.check();
  }

  add(a) {
    quat.add(this, this, a);
    return this.check();
  }

  calculateW() {
    quat.calculateW(this, this);
    return this.check();
  }

  conjugate() {
    quat.conjugate(this, this);
    return this.check();
  }

  invert() {
    quat.invert(this, this);
    return this.check();
  }

  lerp(a, b, t) {
    if (t === undefined) {
      return this.lerp(this, a, b);
    }

    quat.lerp(this, a, b, t);
    return this.check();
  }

  multiplyRight(a) {
    quat.multiply(this, this, a);
    return this.check();
  }

  multiplyLeft(a) {
    quat.multiply(this, a, this);
    return this.check();
  }

  normalize() {
    const length = this.len();
    const l = length > 0 ? 1 / length : 0;
    this[0] = this[0] * l;
    this[1] = this[1] * l;
    this[2] = this[2] * l;
    this[3] = this[3] * l;

    if (length === 0) {
      this[3] = 1;
    }

    return this.check();
  }

  rotateX(rad) {
    quat.rotateX(this, this, rad);
    return this.check();
  }

  rotateY(rad) {
    quat.rotateY(this, this, rad);
    return this.check();
  }

  rotateZ(rad) {
    quat.rotateZ(this, this, rad);
    return this.check();
  }

  scale(b) {
    quat.scale(this, this, b);
    return this.check();
  }

  slerp(arg0, arg1, arg2) {
    let start;
    let target;
    let ratio;

    switch (arguments.length) {
      case 1:
        ({
          start = IDENTITY_QUATERNION,
          target,
          ratio
        } = arg0);
        break;

      case 2:
        start = this;
        target = arg0;
        ratio = arg1;
        break;

      default:
        start = arg0;
        target = arg1;
        ratio = arg2;
    }

    quat.slerp(this, start, target, ratio);
    return this.check();
  }

  transformVector4(vector, result = new Vector4()) {
    vec4.transformQuat(result, vector, this);
    return checkVector(result, 4);
  }

  lengthSq() {
    return this.lengthSquared();
  }

  setFromAxisAngle(axis, rad) {
    return this.setAxisAngle(axis, rad);
  }

  premultiply(a) {
    return this.multiplyLeft(a);
  }

  multiply(a) {
    return this.multiplyRight(a);
  }

}
//# sourceMappingURL=quaternion.js.map