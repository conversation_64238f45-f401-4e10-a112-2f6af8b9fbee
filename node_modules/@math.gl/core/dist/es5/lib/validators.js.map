{"version": 3, "sources": ["../../../src/lib/validators.ts"], "names": ["validateVector", "v", "length", "i", "Number", "isFinite", "checkNumber", "value", "Error", "checkVector", "callerName", "config", "debug", "map", "deprecated", "method", "version", "console", "warn"], "mappings": ";;;;;;;;;;AAoBA;;AAEO,SAASA,cAAT,CAAwBC,CAAxB,EAAwCC,MAAxC,EAAiE;AACtE,MAAID,CAAC,CAACC,MAAF,KAAaA,MAAjB,EAAyB;AACvB,WAAO,KAAP;AACD;;AAED,OAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,CAAC,CAACC,MAAtB,EAA8B,EAAEC,CAAhC,EAAmC;AACjC,QAAI,CAACC,MAAM,CAACC,QAAP,CAAgBJ,CAAC,CAACE,CAAD,CAAjB,CAAL,EAA4B;AAC1B,aAAO,KAAP;AACD;AACF;;AACD,SAAO,IAAP;AACD;;AAEM,SAASG,WAAT,CAAqBC,KAArB,EAAyC;AAC9C,MAAI,CAACH,MAAM,CAACC,QAAP,CAAgBE,KAAhB,CAAL,EAA6B;AAC3B,UAAM,IAAIC,KAAJ,0BAA4BD,KAA5B,EAAN;AACD;;AACD,SAAOA,KAAP;AACD;;AAEM,SAASE,WAAT,CACLR,CADK,EAELC,MAFK,EAIF;AAAA,MADHQ,UACG,uEADkB,EAClB;;AACH,MAAIC,eAAOC,KAAP,IAAgB,CAACZ,cAAc,CAACC,CAAD,EAAIC,MAAJ,CAAnC,EAAgD;AAC9C,UAAM,IAAIM,KAAJ,oBAAsBE,UAAtB,0CAAN;AACD;;AACD,SAAOT,CAAP;AACD;;AAED,IAAMY,GAAG,GAAG,EAAZ;;AAEO,SAASC,UAAT,CAAoBC,MAApB,EAAoCC,OAApC,EAA2D;AAChE,MAAI,CAACH,GAAG,CAACE,MAAD,CAAR,EAAkB;AAChBF,IAAAA,GAAG,CAACE,MAAD,CAAH,GAAc,IAAd;AAEAE,IAAAA,OAAO,CAACC,IAAR,WACKH,MADL,0CAC2CC,OAD3C;AAGD;AACF", "sourcesContent": ["// Copyright (c) 2017 Uber Technologies, Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\nimport {NumberArray} from '@math.gl/types';\nimport {config} from './common';\n\nexport function validateVector(v: NumberArray, length: number): boolean {\n  if (v.length !== length) {\n    return false;\n  }\n  // Could be arguments \"array\" (v.every not availasble)\n  for (let i = 0; i < v.length; ++i) {\n    if (!Number.isFinite(v[i])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport function checkNumber(value: any): number {\n  if (!Number.isFinite(value)) {\n    throw new Error(`Invalid number ${value}`);\n  }\n  return value as number;\n}\n\nexport function checkVector<T extends NumberArray>(\n  v: T,\n  length: number,\n  callerName: string = ''\n): T {\n  if (config.debug && !validateVector(v, length)) {\n    throw new Error(`math.gl: ${callerName} some fields set to invalid numbers'`);\n  }\n  return v;\n}\n\nconst map = {};\n\nexport function deprecated(method: string, version: string): void {\n  if (!map[method]) {\n    map[method] = true;\n    // eslint-disable-next-line\n    console.warn(\n      `${method} has been removed in version ${version}, see upgrade guide for more information`\n    );\n  }\n}\n"], "file": "validators.js"}