{"version": 3, "sources": ["../../../src/lib/assert.ts"], "names": ["assert", "condition", "message", "Error"], "mappings": ";;;;;;;AAAe,SAASA,MAAT,CAAgBC,SAAhB,EAAoCC,OAApC,EAA4D;AACzE,MAAI,CAACD,SAAL,EAAgB;AACd,UAAM,IAAIE,KAAJ,6BAA+BD,OAA/B,EAAN;AACD;AACF", "sourcesContent": ["export default function assert(condition: unknown, message?: string): void {\n  if (!condition) {\n    throw new Error(`math.gl assertion ${message}`);\n  }\n}\n"], "file": "assert.js"}