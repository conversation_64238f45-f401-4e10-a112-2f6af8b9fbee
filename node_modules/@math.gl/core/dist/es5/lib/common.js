"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.configure = configure;
exports.formatValue = formatValue;
exports.isArray = isArray;
exports.clone = clone;
exports.toRadians = toRadians;
exports.toDegrees = toDegrees;
exports.radians = radians;
exports.degrees = degrees;
exports.sin = sin;
exports.cos = cos;
exports.tan = tan;
exports.asin = asin;
exports.acos = acos;
exports.atan = atan;
exports.clamp = clamp;
exports.lerp = lerp;
exports.equals = equals;
exports.exactEquals = exactEquals;
exports.withEpsilon = withEpsilon;
exports.config = void 0;

var _typeof2 = _interopRequireDefault(require("@babel/runtime/helpers/typeof"));

var _assert = _interopRequireDefault(require("./assert"));

var RADIANS_TO_DEGREES = 1 / Math.PI * 180;
var DEGREES_TO_RADIANS = 1 / 180 * Math.PI;
var config = {
  EPSILON: 1e-12,
  debug: false,
  precision: 4,
  printTypes: false,
  printDegrees: false,
  printRowMajor: true
};
exports.config = config;

function configure(options) {
  for (var key in options) {
    (0, _assert.default)(key in config);
    config[key] = options[key];
  }

  return config;
}

function formatValue(value) {
  var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},
      _ref$precision = _ref.precision,
      precision = _ref$precision === void 0 ? config.precision : _ref$precision;

  value = round(value);
  return "".concat(parseFloat(value.toPrecision(precision)));
}

function isArray(value) {
  return Array.isArray(value) || ArrayBuffer.isView(value) && !(value instanceof DataView);
}

function clone(array) {
  return 'clone' in array ? array.clone() : array.slice();
}

function toRadians(degrees) {
  return radians(degrees);
}

function toDegrees(radians) {
  return degrees(radians);
}

function radians(degrees, result) {
  return map(degrees, function (degrees) {
    return degrees * DEGREES_TO_RADIANS;
  }, result);
}

function degrees(radians, result) {
  return map(radians, function (radians) {
    return radians * RADIANS_TO_DEGREES;
  }, result);
}

function sin(radians, result) {
  return map(radians, function (angle) {
    return Math.sin(angle);
  }, result);
}

function cos(radians, result) {
  return map(radians, function (angle) {
    return Math.cos(angle);
  }, result);
}

function tan(radians, result) {
  return map(radians, function (angle) {
    return Math.tan(angle);
  }, result);
}

function asin(radians, result) {
  return map(radians, function (angle) {
    return Math.asin(angle);
  }, result);
}

function acos(radians, result) {
  return map(radians, function (angle) {
    return Math.acos(angle);
  }, result);
}

function atan(radians, result) {
  return map(radians, function (angle) {
    return Math.atan(angle);
  }, result);
}

function clamp(value, min, max) {
  return map(value, function (value) {
    return Math.max(min, Math.min(max, value));
  });
}

function lerp(a, b, t) {
  if (isArray(a)) {
    return a.map(function (ai, i) {
      return lerp(ai, b[i], t);
    });
  }

  return t * b + (1 - t) * a;
}

function equals(a, b, epsilon) {
  var oldEpsilon = config.EPSILON;

  if (epsilon) {
    config.EPSILON = epsilon;
  }

  try {
    if (a === b) {
      return true;
    }

    if (isArray(a) && isArray(b)) {
      if (a.length !== b.length) {
        return false;
      }

      for (var i = 0; i < a.length; ++i) {
        if (!equals(a[i], b[i])) {
          return false;
        }
      }

      return true;
    }

    if (a && a.equals) {
      return a.equals(b);
    }

    if (b && b.equals) {
      return b.equals(a);
    }

    if (typeof a === 'number' && typeof b === 'number') {
      return Math.abs(a - b) <= config.EPSILON * Math.max(1, Math.abs(a), Math.abs(b));
    }

    return false;
  } finally {
    config.EPSILON = oldEpsilon;
  }
}

function exactEquals(a, b) {
  if (a === b) {
    return true;
  }

  if (a && (0, _typeof2.default)(a) === 'object' && b && (0, _typeof2.default)(b) === 'object') {
    if (a.constructor !== b.constructor) {
      return false;
    }

    if (a.exactEquals) {
      return a.exactEquals(b);
    }
  }

  if (isArray(a) && isArray(b)) {
    if (a.length !== b.length) {
      return false;
    }

    for (var i = 0; i < a.length; ++i) {
      if (!exactEquals(a[i], b[i])) {
        return false;
      }
    }

    return true;
  }

  return false;
}

function withEpsilon(epsilon, func) {
  var oldPrecision = config.EPSILON;
  config.EPSILON = epsilon;
  var value;

  try {
    value = func();
  } finally {
    config.EPSILON = oldPrecision;
  }

  return value;
}

function round(value) {
  return Math.round(value / config.EPSILON) * config.EPSILON;
}

function duplicateArray(array) {
  return array.clone ? array.clone() : new Array(array.length);
}

function map(value, func, result) {
  if (isArray(value)) {
    var array = value;
    result = result || duplicateArray(array);

    for (var i = 0; i < result.length && i < array.length; ++i) {
      result[i] = func(value[i], i, result);
    }

    return result;
  }

  return func(value);
}
//# sourceMappingURL=common.js.map