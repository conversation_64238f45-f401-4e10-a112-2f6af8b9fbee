{"version": 3, "sources": ["../../../src/classes/quaternion.ts"], "names": ["IDENTITY_QUATERNION", "Quaternion", "x", "y", "z", "w", "Array", "isArray", "arguments", "length", "copy", "set", "array", "check", "object", "m", "quat", "fromMat3", "axis", "rad", "setAxisAngle", "identity", "fromAxisRotation", "value", "squared<PERSON>ength", "a", "dot", "vectorA", "vectorB", "rotationTo", "add", "calculateW", "conjugate", "invert", "b", "t", "undefined", "lerp", "multiply", "len", "l", "rotateX", "rotateY", "rotateZ", "scale", "arg0", "arg1", "arg2", "start", "target", "ratio", "slerp", "vector", "result", "Vector4", "vec4", "transformQuat", "lengthSquared", "multiplyLeft", "multiplyRight", "MathArray"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAEA;;AACA;;AACA;;AACA;;AACA;;;;;;;;;;AAGA,IAAMA,mBAAmB,GAAG,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,CAA5B;;IAEqBC,U;;;;;AACnB,wBAAyE;AAAA;;AAAA,QAA7DC,CAA6D,uEAAxB,CAAwB;AAAA,QAArBC,CAAqB,uEAAjB,CAAiB;AAAA,QAAdC,CAAc,uEAAV,CAAU;AAAA,QAAPC,CAAO,uEAAH,CAAG;AAAA;AAEvE,8BAAM,CAAC,CAAP,EAAU,CAAC,CAAX,EAAc,CAAC,CAAf,EAAkB,CAAC,CAAnB;;AAEA,QAAIC,KAAK,CAACC,OAAN,CAAcL,CAAd,KAAoBM,SAAS,CAACC,MAAV,KAAqB,CAA7C,EAAgD;AAC9C,YAAKC,IAAL,CAAUR,CAAV;AACD,KAFD,MAEO;AACL,YAAKS,GAAL,CAAST,CAAT,EAAsBC,CAAtB,EAAyBC,CAAzB,EAA4BC,CAA5B;AACD;;AARsE;AASxE;;;;WAED,cAAKO,KAAL,EAA0C;AACxC,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,aAAO,KAAKC,KAAL,EAAP;AACD;;;WAED,aAAIX,CAAJ,EAAeC,CAAf,EAA0BC,CAA1B,EAAqCC,CAArC,EAAsD;AACpD,WAAK,CAAL,IAAUH,CAAV;AACA,WAAK,CAAL,IAAUC,CAAV;AACA,WAAK,CAAL,IAAUC,CAAV;AACA,WAAK,CAAL,IAAUC,CAAV;AACA,aAAO,KAAKQ,KAAL,EAAP;AACD;;;WAED,oBAAWC,MAAX,EAAuE;AACrE,WAAK,CAAL,IAAUA,MAAM,CAACZ,CAAjB;AACA,WAAK,CAAL,IAAUY,MAAM,CAACX,CAAjB;AACA,WAAK,CAAL,IAAUW,MAAM,CAACV,CAAjB;AACA,WAAK,CAAL,IAAUU,MAAM,CAACT,CAAjB;AACA,aAAO,KAAKQ,KAAL,EAAP;AACD;;;WASD,qBAAYE,CAAZ,EAA6C;AAC3CC,MAAAA,IAAI,CAACC,QAAL,CAAc,IAAd,EAAoBF,CAApB;AACA,aAAO,KAAKF,KAAL,EAAP;AACD;;;WAED,0BAAiBK,IAAjB,EAA+CC,GAA/C,EAAkE;AAChEH,MAAAA,IAAI,CAACI,YAAL,CAAkB,IAAlB,EAAwBF,IAAxB,EAA8BC,GAA9B;AACA,aAAO,KAAKN,KAAL,EAAP;AACD;;;WAGD,oBAAiB;AACfG,MAAAA,IAAI,CAACK,QAAL,CAAc,IAAd;AACA,aAAO,KAAKR,KAAL,EAAP;AACD;;;WASD,sBAAaK,IAAb,EAA2CC,GAA3C,EAA8D;AAC5D,aAAO,KAAKG,gBAAL,CAAsBJ,IAAtB,EAA4BC,GAA5B,CAAP;AACD;;;SAGD,eAAuB;AACrB,aAAO,CAAP;AACD;;;SAED,eAAgB;AACd,aAAO,KAAK,CAAL,CAAP;AACD,K;SACD,aAAMI,KAAN,EAAqB;AACnB,WAAK,CAAL,IAAU,6BAAYA,KAAZ,CAAV;AACD;;;SAED,eAAgB;AACd,aAAO,KAAK,CAAL,CAAP;AACD,K;SACD,aAAMA,KAAN,EAAqB;AACnB,WAAK,CAAL,IAAU,6BAAYA,KAAZ,CAAV;AACD;;;SAED,eAAgB;AACd,aAAO,KAAK,CAAL,CAAP;AACD,K;SACD,aAAMA,KAAN,EAAqB;AACnB,WAAK,CAAL,IAAU,6BAAYA,KAAZ,CAAV;AACD;;;SAED,eAAgB;AACd,aAAO,KAAK,CAAL,CAAP;AACD,K;SACD,aAAMA,KAAN,EAAqB;AACnB,WAAK,CAAL,IAAU,6BAAYA,KAAZ,CAAV;AACD;;;WAGD,eAAc;AACZ,aAAOP,IAAI,CAACP,MAAL,CAAY,IAAZ,CAAP;AACD;;;WAGD,yBAAwB;AACtB,aAAOO,IAAI,CAACQ,aAAL,CAAmB,IAAnB,CAAP;AACD;;;WAID,aAAIC,CAAJ,EAAuC;AACrC,aAAOT,IAAI,CAACU,GAAL,CAAS,IAAT,EAAeD,CAAf,CAAP;AACD;;;WAkBD,oBAAWE,OAAX,EAAkCC,OAAlC,EAA+D;AAC7DZ,MAAAA,IAAI,CAACa,UAAL,CAAgB,IAAhB,EAAsBF,OAAtB,EAA+BC,OAA/B;AACA,aAAO,KAAKf,KAAL,EAAP;AACD;;;WAaD,aAAIY,CAAJ,EAAqC;AACnCT,MAAAA,IAAI,CAACc,GAAL,CAAS,IAAT,EAAe,IAAf,EAAqBL,CAArB;AACA,aAAO,KAAKZ,KAAL,EAAP;AACD;;;WAID,sBAAmB;AACjBG,MAAAA,IAAI,CAACe,UAAL,CAAgB,IAAhB,EAAsB,IAAtB;AACA,aAAO,KAAKlB,KAAL,EAAP;AACD;;;WAID,qBAAkB;AAChBG,MAAAA,IAAI,CAACgB,SAAL,CAAe,IAAf,EAAqB,IAArB;AACA,aAAO,KAAKnB,KAAL,EAAP;AACD;;;WAGD,kBAAe;AACbG,MAAAA,IAAI,CAACiB,MAAL,CAAY,IAAZ,EAAkB,IAAlB;AACA,aAAO,KAAKpB,KAAL,EAAP;AACD;;;WAGD,cAAKY,CAAL,EAAgCS,CAAhC,EAAoEC,CAApE,EAAsF;AACpF,UAAIA,CAAC,KAAKC,SAAV,EAAqB;AACnB,eAAO,KAAKC,IAAL,CAAU,IAAV,EAAgBZ,CAAhB,EAAmBS,CAAnB,CAAP;AACD;;AACDlB,MAAAA,IAAI,CAACqB,IAAL,CAAU,IAAV,EAAgBZ,CAAhB,EAAmBS,CAAnB,EAAsCC,CAAtC;AACA,aAAO,KAAKtB,KAAL,EAAP;AACD;;;WAGD,uBAAcY,CAAd,EAA+C;AAC7CT,MAAAA,IAAI,CAACsB,QAAL,CAAc,IAAd,EAAoB,IAApB,EAA0Bb,CAA1B;AACA,aAAO,KAAKZ,KAAL,EAAP;AACD;;;WAED,sBAAaY,CAAb,EAA8C;AAC5CT,MAAAA,IAAI,CAACsB,QAAL,CAAc,IAAd,EAAoBb,CAApB,EAAuB,IAAvB;AACA,aAAO,KAAKZ,KAAL,EAAP;AACD;;;WAGD,qBAAkB;AAEhB,UAAMJ,MAAM,GAAG,KAAK8B,GAAL,EAAf;AACA,UAAMC,CAAC,GAAG/B,MAAM,GAAG,CAAT,GAAa,IAAIA,MAAjB,GAA0B,CAApC;AACA,WAAK,CAAL,IAAU,KAAK,CAAL,IAAU+B,CAApB;AACA,WAAK,CAAL,IAAU,KAAK,CAAL,IAAUA,CAApB;AACA,WAAK,CAAL,IAAU,KAAK,CAAL,IAAUA,CAApB;AACA,WAAK,CAAL,IAAU,KAAK,CAAL,IAAUA,CAApB;;AAEA,UAAI/B,MAAM,KAAK,CAAf,EAAkB;AAChB,aAAK,CAAL,IAAU,CAAV;AACD;;AACD,aAAO,KAAKI,KAAL,EAAP;AACD;;;WAGD,iBAAQM,GAAR,EAA2B;AACzBH,MAAAA,IAAI,CAACyB,OAAL,CAAa,IAAb,EAAmB,IAAnB,EAAyBtB,GAAzB;AACA,aAAO,KAAKN,KAAL,EAAP;AACD;;;WAGD,iBAAQM,GAAR,EAA2B;AACzBH,MAAAA,IAAI,CAAC0B,OAAL,CAAa,IAAb,EAAmB,IAAnB,EAAyBvB,GAAzB;AACA,aAAO,KAAKN,KAAL,EAAP;AACD;;;WAGD,iBAAQM,GAAR,EAA2B;AACzBH,MAAAA,IAAI,CAAC2B,OAAL,CAAa,IAAb,EAAmB,IAAnB,EAAyBxB,GAAzB;AACA,aAAO,KAAKN,KAAL,EAAP;AACD;;;WAGD,eAAMqB,CAAN,EAAuB;AACrBlB,MAAAA,IAAI,CAAC4B,KAAL,CAAW,IAAX,EAAiB,IAAjB,EAAuBV,CAAvB;AACA,aAAO,KAAKrB,KAAL,EAAP;AACD;;;WAWD,eACEgC,IADF,EAQEC,IARF,EASEC,IATF,EAUQ;AACN,UAAIC,KAAJ;AACA,UAAIC,MAAJ;AACA,UAAIC,KAAJ;;AAEA,cAAQ1C,SAAS,CAACC,MAAlB;AACE,aAAK,CAAL;AAAA,qBAMMoC,IANN;AAAA,gCAGIG,KAHJ;AAGIA,UAAAA,KAHJ,2BAGYhD,mBAHZ;AAIIiD,UAAAA,MAJJ,QAIIA,MAJJ;AAKIC,UAAAA,KALJ,QAKIA,KALJ;AAWE;;AACF,aAAK,CAAL;AACEF,UAAAA,KAAK,GAAG,IAAR;AACAC,UAAAA,MAAM,GAAGJ,IAAT;AACAK,UAAAA,KAAK,GAAGJ,IAAR;AACA;;AACF;AAEEE,UAAAA,KAAK,GAAGH,IAAR;AACAI,UAAAA,MAAM,GAAGH,IAAT;AACAI,UAAAA,KAAK,GAAGH,IAAR;AAtBJ;;AAwBA/B,MAAAA,IAAI,CAACmC,KAAL,CAAW,IAAX,EAAiBH,KAAjB,EAAwBC,MAAxB,EAAgCC,KAAhC;AACA,aAAO,KAAKrC,KAAL,EAAP;AACD;;;WAED,0BACEuC,MADF,EAGgB;AAAA,UADdC,MACc,uEADS,IAAIC,eAAJ,EACT;AACdC,MAAAA,IAAI,CAACC,aAAL,CAAmBH,MAAnB,EAA2BD,MAA3B,EAAmC,IAAnC;AACA,aAAO,6BAAYC,MAAZ,EAAoB,CAApB,CAAP;AACD;;;WAGD,oBAAmB;AACjB,aAAO,KAAKI,aAAL,EAAP;AACD;;;WAED,0BAAiBvC,IAAjB,EAA+CC,GAA/C,EAAkE;AAChE,aAAO,KAAKC,YAAL,CAAkBF,IAAlB,EAAwBC,GAAxB,CAAP;AACD;;;WAED,qBAAYM,CAAZ,EAA6C;AAC3C,aAAO,KAAKiC,YAAL,CAAkBjC,CAAlB,CAAP;AACD;;;WAED,kBAASA,CAAT,EAA0C;AACxC,aAAO,KAAKkC,aAAL,CAAmBlC,CAAnB,CAAP;AACD;;;EAvTqCmC,kB", "sourcesContent": ["// Copyright (c) 2017 Uber Technologies, Inc.\n// MIT License\nimport MathArray from './base/math-array';\nimport {checkNumber, checkVector} from '../lib/validators';\nimport Vector4 from './vector4';\nimport * as quat from 'gl-matrix/quat';\nimport * as vec4 from 'gl-matrix/vec4';\nimport {NumericArray} from '@math.gl/types';\n\nconst IDENTITY_QUATERNION = [0, 0, 0, 1] as const;\n\nexport default class Quaternion extends MathArray {\n  constructor(x: number | Readonly<NumericArray> = 0, y = 0, z = 0, w = 1) {\n    // PERF NOTE: initialize elements as double precision numbers\n    super(-0, -0, -0, -0);\n    // eslint-disable-next-line prefer-rest-params\n    if (Array.isArray(x) && arguments.length === 1) {\n      this.copy(x);\n    } else {\n      this.set(x as number, y, z, w);\n    }\n  }\n\n  copy(array: Readonly<NumericArray>): this {\n    this[0] = array[0];\n    this[1] = array[1];\n    this[2] = array[2];\n    this[3] = array[3];\n    return this.check();\n  }\n\n  set(x: number, y: number, z: number, w: number): this {\n    this[0] = x;\n    this[1] = y;\n    this[2] = z;\n    this[3] = w;\n    return this.check();\n  }\n\n  fromObject(object: {x: number; y: number; z: number; w: number}): this {\n    this[0] = object.x;\n    this[1] = object.y;\n    this[2] = object.z;\n    this[3] = object.w;\n    return this.check();\n  }\n\n  /**\n   * Creates a quaternion from the given 3x3 rotation matrix.\n   * NOTE: The resultant quaternion is not normalized, so you should\n   * be sure to renormalize the quaternion yourself where necessary.\n   * @param m\n   * @returns\n   */\n  fromMatrix3(m: Readonly<NumericArray>): this {\n    quat.fromMat3(this, m);\n    return this.check();\n  }\n\n  fromAxisRotation(axis: Readonly<NumericArray>, rad: number): this {\n    quat.setAxisAngle(this, axis, rad);\n    return this.check();\n  }\n\n  /** Set a quat to the identity quaternion */\n  identity(): this {\n    quat.identity(this);\n    return this.check();\n  }\n\n  // Set the components of a quat to the given values\n  // set(i, j, k, l) {\n  //   quat.set(this, i, j, k, l);\n  //   return this.check();\n  // }\n\n  // Sets a quat from the given angle and rotation axis, then returns it.\n  setAxisAngle(axis: Readonly<NumericArray>, rad: number): this {\n    return this.fromAxisRotation(axis, rad);\n  }\n\n  // Getters/setters\n  get ELEMENTS(): number {\n    return 4;\n  }\n\n  get x(): number {\n    return this[0];\n  }\n  set x(value: number) {\n    this[0] = checkNumber(value);\n  }\n\n  get y(): number {\n    return this[1];\n  }\n  set y(value: number) {\n    this[1] = checkNumber(value);\n  }\n\n  get z(): number {\n    return this[2];\n  }\n  set z(value: number) {\n    this[2] = checkNumber(value);\n  }\n\n  get w(): number {\n    return this[3];\n  }\n  set w(value: number) {\n    this[3] = checkNumber(value);\n  }\n\n  // Calculates the length of a quat\n  len(): number {\n    return quat.length(this);\n  }\n\n  // Calculates the squared length of a quat\n  lengthSquared(): number {\n    return quat.squaredLength(this);\n  }\n\n  // Calculates the dot product of two quat's\n  // @return {Number}\n  dot(a: Readonly<NumericArray>): number {\n    return quat.dot(this, a);\n  }\n\n  // Gets the rotation axis and angle for a given quaternion.\n  // If a quaternion is created with setAxisAngle, this method will\n  // return the same values as providied in the original parameter\n  // list OR functionally equivalent values.\n  // Example: The quaternion formed by axis [0, 0, 1] and angle -90\n  // is the same as the quaternion formed by [0, 0, 1] and 270.\n  // This method favors the latter.\n  // @return {{[x,y,z], Number}}\n  // getAxisAngle() {\n  //   const axis = [];\n  //   const angle = quat.getAxisAngle(axis, this);\n  //   return {axis, angle};\n  // }\n  // MODIFIERS\n  // Sets a quaternion to represent the shortest rotation from one vector\n  // to another. Both vectors are assumed to be unit length.\n  rotationTo(vectorA: NumericArray, vectorB: NumericArray): this {\n    quat.rotationTo(this, vectorA, vectorB);\n    return this.check();\n  }\n\n  // Sets the specified quaternion with values corresponding to the given axes.\n  // Each axis is a vec3 and is expected to be unit length and perpendicular\n  // to all other specified axes.\n  // setAxes() {\n  //   Number\n  // }\n  // Performs a spherical linear interpolation with two control points\n  // sqlerp() {\n  //   Number;\n  // }\n  // Adds two quat's\n  add(a: Readonly<NumericArray>): this {\n    quat.add(this, this, a);\n    return this.check();\n  }\n\n  // Calculates the W component of a quat from the X, Y, and Z components.\n  // Any existing W component will be ignored.\n  calculateW(): this {\n    quat.calculateW(this, this);\n    return this.check();\n  }\n\n  // Calculates the conjugate of a quat If the quaternion is normalized,\n  // this function is faster than quat.inverse and produces the same result.\n  conjugate(): this {\n    quat.conjugate(this, this);\n    return this.check();\n  }\n\n  // Calculates the inverse of a quat\n  invert(): this {\n    quat.invert(this, this);\n    return this.check();\n  }\n\n  // Performs a linear interpolation between two quat's\n  lerp(a: Readonly<NumericArray>, b: Readonly<NumericArray> | number, t?: number): this {\n    if (t === undefined) {\n      return this.lerp(this, a, b as number);\n    }\n    quat.lerp(this, a, b as NumericArray, t);\n    return this.check();\n  }\n\n  // Multiplies two quat's\n  multiplyRight(a: Readonly<NumericArray>): this {\n    quat.multiply(this, this, a);\n    return this.check();\n  }\n\n  multiplyLeft(a: Readonly<NumericArray>): this {\n    quat.multiply(this, a, this);\n    return this.check();\n  }\n\n  // Normalize a quat\n  normalize(): this {\n    // Handle 0 case\n    const length = this.len();\n    const l = length > 0 ? 1 / length : 0;\n    this[0] = this[0] * l;\n    this[1] = this[1] * l;\n    this[2] = this[2] * l;\n    this[3] = this[3] * l;\n    // Set to [0, 0, 0, 1] if length is 0\n    if (length === 0) {\n      this[3] = 1;\n    }\n    return this.check();\n  }\n\n  // Rotates a quaternion by the given angle about the X axis\n  rotateX(rad: number): this {\n    quat.rotateX(this, this, rad);\n    return this.check();\n  }\n\n  // Rotates a quaternion by the given angle about the Y axis\n  rotateY(rad: number): this {\n    quat.rotateY(this, this, rad);\n    return this.check();\n  }\n\n  // Rotates a quaternion by the given angle about the Z axis\n  rotateZ(rad: number): this {\n    quat.rotateZ(this, this, rad);\n    return this.check();\n  }\n\n  // Scales a quat by a scalar number\n  scale(b: number): this {\n    quat.scale(this, this, b);\n    return this.check();\n  }\n\n  slerp(target: Readonly<NumericArray>, ratio: number): this;\n  slerp(start: Readonly<NumericArray>, target: Readonly<NumericArray>, ratio: number): this;\n  slerp(params: {\n    start: Readonly<NumericArray>;\n    target: Readonly<NumericArray>;\n    ratio: number;\n  }): this;\n\n  // Performs a spherical linear interpolation between two quat\n  slerp(\n    arg0:\n      | Readonly<NumericArray>\n      | {\n          start: Readonly<NumericArray>;\n          target: Readonly<NumericArray>;\n          ratio: number;\n        },\n    arg1?: Readonly<NumericArray> | number,\n    arg2?: number\n  ): this {\n    let start: Readonly<NumericArray>;\n    let target: Readonly<NumericArray>;\n    let ratio: number;\n    // eslint-disable-next-line prefer-rest-params\n    switch (arguments.length) {\n      case 1: // Deprecated signature ({start, target, ratio})\n        // eslint-disable-next-line prefer-rest-params\n        ({\n          start = IDENTITY_QUATERNION,\n          target,\n          ratio\n        } = arg0 as {\n          start: Readonly<NumericArray>;\n          target: Readonly<NumericArray>;\n          ratio: number;\n        });\n        break;\n      case 2: // THREE.js compatibility signature (target, ration)\n        start = this; // eslint-disable-line\n        target = arg0 as Readonly<NumericArray>;\n        ratio = arg1 as number;\n        break;\n      default:\n        // Default signature: (start, target, ratio)\n        start = arg0 as Readonly<NumericArray>;\n        target = arg1 as Readonly<NumericArray>;\n        ratio = arg2;\n    }\n    quat.slerp(this, start, target, ratio);\n    return this.check();\n  }\n\n  transformVector4(\n    vector: Readonly<NumericArray>,\n    result: NumericArray = new Vector4()\n  ): NumericArray {\n    vec4.transformQuat(result, vector, this);\n    return checkVector(result, 4);\n  }\n\n  // THREE.js Math API compatibility\n  lengthSq(): number {\n    return this.lengthSquared();\n  }\n\n  setFromAxisAngle(axis: Readonly<NumericArray>, rad: number): this {\n    return this.setAxisAngle(axis, rad);\n  }\n\n  premultiply(a: Readonly<NumericArray>): this {\n    return this.multiplyLeft(a);\n  }\n\n  multiply(a: Readonly<NumericArray>): this {\n    return this.multiplyRight(a);\n  }\n}\n"], "file": "quaternion.js"}