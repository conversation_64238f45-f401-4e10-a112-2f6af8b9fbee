{"version": 3, "sources": ["../../../src/classes/vector2.ts"], "names": ["Vector2", "x", "y", "arguments", "length", "copy", "config", "debug", "check", "array", "object", "Math", "atan2", "matrix4", "transformAsPoint", "vec2", "transformMat4", "matrix3", "transformMat3", "matrix2x3", "transformMat2d", "matrix2", "transformMat2", "Vector"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAEA;;AACA;;AACA;;AACA;;AAEA;;;;;;;;;;IAOqBA,O;;;;;AAEnB,qBAAmE;AAAA;;AAAA,QAAvDC,CAAuD,uEAAlB,CAAkB;AAAA,QAAfC,CAAe,uEAAH,CAAG;AAAA;AAEjE,8BAAM,CAAN;;AACA,QAAI,qBAAQD,CAAR,KAAcE,SAAS,CAACC,MAAV,KAAqB,CAAvC,EAA0C;AACxC,YAAKC,IAAL,CAAUJ,CAAV;AACD,KAFD,MAEO;AACL,UAAIK,eAAOC,KAAX,EAAkB;AAChB,qCAAYN,CAAZ;AACA,qCAAYC,CAAZ;AACD;;AACD,YAAK,CAAL,IAAUD,CAAV;AACA,YAAK,CAAL,IAAUC,CAAV;AACD;;AAZgE;AAalE;;;;WAED,aAAID,CAAJ,EAAeC,CAAf,EAAgC;AAC9B,WAAK,CAAL,IAAUD,CAAV;AACA,WAAK,CAAL,IAAUC,CAAV;AACA,aAAO,KAAKM,KAAL,EAAP;AACD;;;WAED,cAAKC,KAAL,EAA0C;AACxC,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,aAAO,KAAKD,KAAL,EAAP;AACD;;;WAED,oBAAWE,MAAX,EAAiD;AAC/C,UAAIJ,eAAOC,KAAX,EAAkB;AAChB,qCAAYG,MAAM,CAACT,CAAnB;AACA,qCAAYS,MAAM,CAACR,CAAnB;AACD;;AACD,WAAK,CAAL,IAAUQ,MAAM,CAACT,CAAjB;AACA,WAAK,CAAL,IAAUS,MAAM,CAACR,CAAjB;AACA,aAAO,KAAKM,KAAL,EAAP;AACD;;;WAED,kBAASE,MAAT,EAAmE;AACjEA,MAAAA,MAAM,CAACT,CAAP,GAAW,KAAK,CAAL,CAAX;AACAS,MAAAA,MAAM,CAACR,CAAP,GAAW,KAAK,CAAL,CAAX;AACA,aAAOQ,MAAP;AACD;;;SAID,eAAuB;AACrB,aAAO,CAAP;AACD;;;WAMD,2BAA0B;AACxB,aAAOC,IAAI,CAACC,KAAL,CAAW,KAAKV,CAAhB,EAAmB,KAAKD,CAAxB,CAAP;AACD;;;WAMD,yBAAwB;AACtB,aAAOU,IAAI,CAACC,KAAL,CAAW,KAAKX,CAAhB,EAAmB,KAAKC,CAAxB,CAAP;AACD;;;WASD,mBAAUW,OAAV,EAAiD;AAC/C,aAAO,KAAKC,gBAAL,CAAsBD,OAAtB,CAAP;AACD;;;WAOD,0BAAiBA,OAAjB,EAAwD;AACtDE,MAAAA,IAAI,CAACC,aAAL,CAAmB,IAAnB,EAAyB,IAAzB,EAA+BH,OAA/B;AACA,aAAO,KAAKL,KAAL,EAAP;AACD;;;WAOD,2BAAkBK,OAAlB,EAAyD;AACvD,sDAA2B,IAA3B,EAAiC,IAAjC,EAAuCA,OAAvC;AACA,aAAO,KAAKL,KAAL,EAAP;AACD;;;WAED,4BAAmBS,OAAnB,EAA0D;AACxDF,MAAAA,IAAI,CAACG,aAAL,CAAmB,IAAnB,EAAyB,IAAzB,EAA+BD,OAA/B;AACA,aAAO,KAAKT,KAAL,EAAP;AACD;;;WAED,8BAAqBW,SAArB,EAA8D;AAC5DJ,MAAAA,IAAI,CAACK,cAAL,CAAoB,IAApB,EAA0B,IAA1B,EAAgCD,SAAhC;AACA,aAAO,KAAKX,KAAL,EAAP;AACD;;;WAED,4BAAmBa,OAAnB,EAA0D;AACxDN,MAAAA,IAAI,CAACO,aAAL,CAAmB,IAAnB,EAAyB,IAAzB,EAA+BD,OAA/B;AACA,aAAO,KAAKb,KAAL,EAAP;AACD;;;EA/GkCe,e", "sourcesContent": ["// Copyright (c) 2017 Uber Technologies, Inc.\n// MIT License\nimport Vector from './base/vector';\nimport {config, isArray} from '../lib/common';\nimport {checkNumber} from '../lib/validators';\nimport * as vec2 from 'gl-matrix/vec2';\n/* eslint-disable camelcase */\nimport {vec2_transformMat4AsVector} from '../lib/gl-matrix-extras';\nimport {NumericArray} from '@math.gl/types';\n\n/**\n * Two-element vector class.\n * Subclass of Array<number>\n */\nexport default class Vector2 extends Vector {\n  // Creates a new, empty vec2\n  constructor(x: number | Readonly<NumericArray> = 0, y: number = 0) {\n    // PERF NOTE: initialize elements as double precision numbers\n    super(2); // -0, -0);\n    if (isArray(x) && arguments.length === 1) {\n      this.copy(x as Readonly<NumericArray>);\n    } else {\n      if (config.debug) {\n        checkNumber(x);\n        checkNumber(y);\n      }\n      this[0] = x as number;\n      this[1] = y;\n    }\n  }\n\n  set(x: number, y: number): this {\n    this[0] = x;\n    this[1] = y;\n    return this.check();\n  }\n\n  copy(array: Readonly<NumericArray>): this {\n    this[0] = array[0];\n    this[1] = array[1];\n    return this.check();\n  }\n\n  fromObject(object: {x: number; y: number}): this {\n    if (config.debug) {\n      checkNumber(object.x);\n      checkNumber(object.y);\n    }\n    this[0] = object.x;\n    this[1] = object.y;\n    return this.check();\n  }\n\n  toObject(object: {x?: number; y?: number}): {x: number; y: number} {\n    object.x = this[0];\n    object.y = this[1];\n    return object as {x: number; y: number};\n  }\n\n  // Getters/setters\n\n  get ELEMENTS(): number {\n    return 2;\n  }\n\n  /**\n   * Returns angle from x axis\n   * @returns\n   */\n  horizontalAngle(): number {\n    return Math.atan2(this.y, this.x);\n  }\n\n  /**\n   * Returns angle from y axis\n   * @returns\n   */\n  verticalAngle(): number {\n    return Math.atan2(this.x, this.y);\n  }\n\n  // Transforms\n\n  /**\n   * Transforms as point\n   * @param matrix4\n   * @returns\n   */\n  transform(matrix4: Readonly<NumericArray>): this {\n    return this.transformAsPoint(matrix4);\n  }\n\n  /**\n   * transforms as point (4th component is implicitly 1)\n   * @param matrix4\n   * @returns\n   */\n  transformAsPoint(matrix4: Readonly<NumericArray>): this {\n    vec2.transformMat4(this, this, matrix4);\n    return this.check();\n  }\n\n  /**\n   * transforms as vector (4th component is implicitly 0, ignores translation. slightly faster)\n   * @param matrix4\n   * @returns\n   */\n  transformAsVector(matrix4: Readonly<NumericArray>): this {\n    vec2_transformMat4AsVector(this, this, matrix4);\n    return this.check();\n  }\n\n  transformByMatrix3(matrix3: Readonly<NumericArray>): this {\n    vec2.transformMat3(this, this, matrix3);\n    return this.check();\n  }\n\n  transformByMatrix2x3(matrix2x3: Readonly<NumericArray>): this {\n    vec2.transformMat2d(this, this, matrix2x3);\n    return this.check();\n  }\n\n  transformByMatrix2(matrix2: Readonly<NumericArray>): this {\n    vec2.transformMat2(this, this, matrix2);\n    return this.check();\n  }\n}\n"], "file": "vector2.js"}