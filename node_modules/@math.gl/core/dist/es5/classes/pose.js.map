{"version": 3, "sources": ["../../../src/classes/pose.ts"], "names": ["Pose", "x", "y", "z", "roll", "pitch", "yaw", "position", "orientation", "Array", "isArray", "length", "Vector3", "<PERSON>uler", "RollPitchYaw", "value", "pose", "equals", "exactEquals", "sr", "Math", "sin", "sp", "sw", "cr", "cos", "cp", "cw", "Matrix4", "setRowMajor", "multiplyRight", "getTransformationMatrix", "invert"], "mappings": ";;;;;;;;;;;;;;;AAEA;;AACA;;AACA;;IAcqBA,I;AAInB,kBASqB;AAAA,mFAAJ,EAAI;AAAA,sBARnBC,CAQmB;AAAA,QARnBA,CAQmB,uBARf,CAQe;AAAA,sBAPnBC,CAOmB;AAAA,QAPnBA,CAOmB,uBAPf,CAOe;AAAA,sBANnBC,CAMmB;AAAA,QANnBA,CAMmB,uBANf,CAMe;AAAA,yBALnBC,IAKmB;AAAA,QALnBA,IAKmB,0BALZ,CAKY;AAAA,0BAJnBC,KAImB;AAAA,QAJnBA,KAImB,2BAJX,CAIW;AAAA,wBAHnBC,GAGmB;AAAA,QAHnBA,GAGmB,yBAHb,CAGa;AAAA,QAFnBC,QAEmB,QAFnBA,QAEmB;AAAA,QADnBC,WACmB,QADnBA,WACmB;;AAAA;AAAA;AAAA;;AACnB,QAAIC,KAAK,CAACC,OAAN,CAAcH,QAAd,KAA2BA,QAAQ,CAACI,MAAT,KAAoB,CAAnD,EAAsD;AACpD,WAAKJ,QAAL,GAAgB,IAAIK,eAAJ,CAAYL,QAAZ,CAAhB;AACD,KAFD,MAEO;AACL,WAAKA,QAAL,GAAgB,IAAIK,eAAJ,CAAYX,CAAZ,EAAeC,CAAf,EAAkBC,CAAlB,CAAhB;AACD;;AACD,QAAIM,KAAK,CAACC,OAAN,CAAcF,WAAd,KAA8BA,WAAW,CAACG,MAAZ,KAAuB,CAAzD,EAA4D;AAE1D,WAAKH,WAAL,GAAmB,IAAIK,cAAJ,CAAUL,WAAV,EAAuBA,WAAW,CAAC,CAAD,CAAlC,CAAnB;AACD,KAHD,MAGO;AACL,WAAKA,WAAL,GAAmB,IAAIK,cAAJ,CAAUT,IAAV,EAAgBC,KAAhB,EAAuBC,GAAvB,EAA4BO,eAAMC,YAAlC,CAAnB;AACD;AACF;;;;SAED,eAAgB;AACd,aAAO,KAAKP,QAAL,CAAcN,CAArB;AACD,K;SAED,aAAMc,KAAN,EAAqB;AACnB,WAAKR,QAAL,CAAcN,CAAd,GAAkBc,KAAlB;AACD;;;SAED,eAAgB;AACd,aAAO,KAAKR,QAAL,CAAcL,CAArB;AACD,K;SAED,aAAMa,KAAN,EAAqB;AACnB,WAAKR,QAAL,CAAcL,CAAd,GAAkBa,KAAlB;AACD;;;SAED,eAAgB;AACd,aAAO,KAAKR,QAAL,CAAcJ,CAArB;AACD,K;SAED,aAAMY,KAAN,EAAqB;AACnB,WAAKR,QAAL,CAAcJ,CAAd,GAAkBY,KAAlB;AACD;;;SAED,eAAmB;AACjB,aAAO,KAAKP,WAAL,CAAiBJ,IAAxB;AACD,K;SAED,aAASW,KAAT,EAAwB;AACtB,WAAKP,WAAL,CAAiBJ,IAAjB,GAAwBW,KAAxB;AACD;;;SAED,eAAoB;AAClB,aAAO,KAAKP,WAAL,CAAiBH,KAAxB;AACD,K;SACD,aAAUU,KAAV,EAAyB;AACvB,WAAKP,WAAL,CAAiBH,KAAjB,GAAyBU,KAAzB;AACD;;;SAED,eAAkB;AAChB,aAAO,KAAKP,WAAL,CAAiBF,GAAxB;AACD,K;SAED,aAAQS,KAAR,EAAuB;AACrB,WAAKP,WAAL,CAAiBF,GAAjB,GAAuBS,KAAvB;AACD;;;WAED,uBAAuB;AACrB,aAAO,KAAKR,QAAZ;AACD;;;WAED,0BAAwB;AACtB,aAAO,KAAKC,WAAZ;AACD;;;WAED,gBAAOQ,IAAP,EAA4B;AAC1B,UAAI,CAACA,IAAL,EAAW;AACT,eAAO,KAAP;AACD;;AACD,aAAO,KAAKT,QAAL,CAAcU,MAAd,CAAqBD,IAAI,CAACT,QAA1B,KAAuC,KAAKC,WAAL,CAAiBS,MAAjB,CAAwBD,IAAI,CAACR,WAA7B,CAA9C;AACD;;;WAED,qBAAYQ,IAAZ,EAAiC;AAC/B,UAAI,CAACA,IAAL,EAAW;AACT,eAAO,KAAP;AACD;;AACD,aACE,KAAKT,QAAL,CAAcW,WAAd,CAA0BF,IAAI,CAACT,QAA/B,KAA4C,KAAKC,WAAL,CAAiBU,WAAjB,CAA6BF,IAAI,CAACR,WAAlC,CAD9C;AAGD;;;WAED,mCAAmC;AAEjC,UAAMW,EAAE,GAAGC,IAAI,CAACC,GAAL,CAAS,KAAKjB,IAAd,CAAX;AACA,UAAMkB,EAAE,GAAGF,IAAI,CAACC,GAAL,CAAS,KAAKhB,KAAd,CAAX;AACA,UAAMkB,EAAE,GAAGH,IAAI,CAACC,GAAL,CAAS,KAAKf,GAAd,CAAX;AACA,UAAMkB,EAAE,GAAGJ,IAAI,CAACK,GAAL,CAAS,KAAKrB,IAAd,CAAX;AACA,UAAMsB,EAAE,GAAGN,IAAI,CAACK,GAAL,CAAS,KAAKpB,KAAd,CAAX;AACA,UAAMsB,EAAE,GAAGP,IAAI,CAACK,GAAL,CAAS,KAAKnB,GAAd,CAAX;AAGA,aAAO,IAAIsB,eAAJ,GAAcC,WAAd,CACLF,EAAE,GAAGD,EADA,EAEL,CAACH,EAAD,GAAMC,EAAN,GAAWG,EAAE,GAAGL,EAAL,GAAUH,EAFhB,EAGLI,EAAE,GAAGJ,EAAL,GAAUQ,EAAE,GAAGL,EAAL,GAAUE,EAHf,EAIL,KAAKvB,CAJA,EAKLsB,EAAE,GAAGG,EALA,EAMLC,EAAE,GAAGH,EAAL,GAAUD,EAAE,GAAGD,EAAL,GAAUH,EANf,EAOL,CAACQ,EAAD,GAAMR,EAAN,GAAWI,EAAE,GAAGD,EAAL,GAAUE,EAPhB,EAQL,KAAKtB,CARA,EASL,CAACoB,EATI,EAULI,EAAE,GAAGP,EAVA,EAWLO,EAAE,GAAGF,EAXA,EAYL,KAAKrB,CAZA,EAaL,CAbK,EAcL,CAdK,EAeL,CAfK,EAgBL,CAhBK,CAAP;AAkBD;;;WAED,yCAAgCa,IAAhC,EAAqD;AACnD,aAAO,IAAIY,eAAJ,GACJE,aADI,CACU,KAAKC,uBAAL,EADV,EAEJD,aAFI,CAEUd,IAAI,CAACe,uBAAL,GAA+BC,MAA/B,EAFV,CAAP;AAGD;;;WAED,uCAA8BhB,IAA9B,EAAmD;AACjD,aAAO,IAAIY,eAAJ,GACJE,aADI,CACUd,IAAI,CAACe,uBAAL,EADV,EAEJD,aAFI,CAEU,KAAKC,uBAAL,GAA+BC,MAA/B,EAFV,CAAP;AAGD", "sourcesContent": ["// Copyright (c) 2017 Uber Technologies, Inc.\n// MIT License\nimport Matrix4 from './matrix4';\nimport Vector3 from './vector3';\nimport Euler from './euler';\nimport {NumericArray} from '@math.gl/types';\n\ntype PoseOptions = {\n  position?: Readonly<NumericArray>;\n  orientation?: Readonly<NumericArray>;\n  x?: number;\n  y?: number;\n  z?: number;\n  roll?: number;\n  pitch?: number;\n  yaw?: number;\n};\n\nexport default class Pose {\n  readonly position: Vector3;\n  readonly orientation: Euler;\n\n  constructor({\n    x = 0,\n    y = 0,\n    z = 0,\n    roll = 0,\n    pitch = 0,\n    yaw = 0,\n    position,\n    orientation\n  }: PoseOptions = {}) {\n    if (Array.isArray(position) && position.length === 3) {\n      this.position = new Vector3(position);\n    } else {\n      this.position = new Vector3(x, y, z);\n    }\n    if (Array.isArray(orientation) && orientation.length === 4) {\n      // @ts-expect-error\n      this.orientation = new Euler(orientation, orientation[3]);\n    } else {\n      this.orientation = new Euler(roll, pitch, yaw, Euler.RollPitchYaw);\n    }\n  }\n\n  get x(): number {\n    return this.position.x;\n  }\n\n  set x(value: number) {\n    this.position.x = value;\n  }\n\n  get y(): number {\n    return this.position.y;\n  }\n\n  set y(value: number) {\n    this.position.y = value;\n  }\n\n  get z(): number {\n    return this.position.z;\n  }\n\n  set z(value: number) {\n    this.position.z = value;\n  }\n\n  get roll(): number {\n    return this.orientation.roll;\n  }\n\n  set roll(value: number) {\n    this.orientation.roll = value;\n  }\n\n  get pitch(): number {\n    return this.orientation.pitch;\n  }\n  set pitch(value: number) {\n    this.orientation.pitch = value;\n  }\n\n  get yaw(): number {\n    return this.orientation.yaw;\n  }\n\n  set yaw(value: number) {\n    this.orientation.yaw = value;\n  }\n\n  getPosition(): Vector3 {\n    return this.position;\n  }\n\n  getOrientation(): Euler {\n    return this.orientation;\n  }\n\n  equals(pose: Pose): boolean {\n    if (!pose) {\n      return false;\n    }\n    return this.position.equals(pose.position) && this.orientation.equals(pose.orientation);\n  }\n\n  exactEquals(pose: Pose): boolean {\n    if (!pose) {\n      return false;\n    }\n    return (\n      this.position.exactEquals(pose.position) && this.orientation.exactEquals(pose.orientation)\n    );\n  }\n\n  getTransformationMatrix(): Matrix4 {\n    // setup pre computations for the sin/cos of the angles\n    const sr = Math.sin(this.roll);\n    const sp = Math.sin(this.pitch);\n    const sw = Math.sin(this.yaw);\n    const cr = Math.cos(this.roll);\n    const cp = Math.cos(this.pitch);\n    const cw = Math.cos(this.yaw);\n\n    // Create matrix\n    return new Matrix4().setRowMajor(\n      cw * cp, // 0,0\n      -sw * cr + cw * sp * sr, // 0,1\n      sw * sr + cw * sp * cr, // 0,2\n      this.x, // 0,3\n      sw * cp, // 1,0\n      cw * cr + sw * sp * sr, // 1,1\n      -cw * sr + sw * sp * cr, // 1,2\n      this.y, // 1,3\n      -sp, // 2,0\n      cp * sr, // 2,1\n      cp * cr, // 2,2\n      this.z, // 2,3\n      0,\n      0,\n      0,\n      1\n    );\n  }\n\n  getTransformationMatrixFromPose(pose: Pose): Matrix4 {\n    return new Matrix4()\n      .multiplyRight(this.getTransformationMatrix())\n      .multiplyRight(pose.getTransformationMatrix().invert());\n  }\n\n  getTransformationMatrixToPose(pose: Pose): Matrix4 {\n    return new Matrix4()\n      .multiplyRight(pose.getTransformationMatrix())\n      .multiplyRight(this.getTransformationMatrix().invert());\n  }\n}\n"], "file": "pose.js"}