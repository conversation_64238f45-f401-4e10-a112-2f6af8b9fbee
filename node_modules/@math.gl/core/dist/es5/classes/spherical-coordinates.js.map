{"version": 3, "sources": ["../../../src/classes/spherical-coordinates.ts"], "names": ["EPSILON", "EARTH_RADIUS_METERS", "SphericalCoordinates", "phi", "theta", "radius", "bearing", "pitch", "altitude", "radiusScale", "undefined", "check", "formatString", "config", "printTypes", "f", "formatValue", "other", "v", "Math", "PI", "copy", "lng", "lat", "z", "vec3", "length", "atan2", "acos", "Vector3", "rotateX", "radians", "rotateZ", "max", "min", "Number", "isFinite", "Error"], "mappings": ";;;;;;;;;;;;;;;;;;;AAGA;;AACA;;AAEA;;;;;;AAkBA,IAAMA,OAAO,GAAG,QAAhB;AACA,IAAMC,mBAAmB,GAAG,OAA5B;;IAOqBC,oB;AA4BnB,kCAQqC;AAAA,mFAAJ,EAAI;AAAA,wBAPnCC,GAOmC;AAAA,QAPnCA,GAOmC,yBAP7B,CAO6B;AAAA,0BANnCC,KAMmC;AAAA,QANnCA,KAMmC,2BAN3B,CAM2B;AAAA,2BALnCC,MAKmC;AAAA,QALnCA,MAKmC,4BAL1B,CAK0B;AAAA,QAJnCC,OAImC,QAJnCA,OAImC;AAAA,QAHnCC,KAGmC,QAHnCA,KAGmC;AAAA,QAFnCC,QAEmC,QAFnCA,QAEmC;AAAA,gCADnCC,WACmC;AAAA,QADnCA,WACmC,iCADrBR,mBACqB;;AAAA;AAAA;AAAA;AAAA;AAAA;AACnC,SAAKE,GAAL,GAAWA,GAAX;AACA,SAAKC,KAAL,GAAaA,KAAb;AAEA,SAAKC,MAAL,GAAcA,MAAM,IAAIG,QAAV,IAAsB,CAApC;AACA,SAAKC,WAAL,GAAmBA,WAAW,IAAI,CAAlC;;AACA,QAAIH,OAAO,KAAKI,SAAhB,EAA2B;AACzB,WAAKJ,OAAL,GAAeA,OAAf;AACD;;AACD,QAAIC,KAAK,KAAKG,SAAd,EAAyB;AACvB,WAAKH,KAAL,GAAaA,KAAb;AACD;;AACD,SAAKI,KAAL;AACD;;;;WAED,oBAAmB;AACjB,aAAO,KAAKC,YAAL,CAAkBC,cAAlB,CAAP;AACD;;;WAED,6BAA0D;AAAA,mCAA5CC,UAA4C;AAAA,UAA5CA,UAA4C,iCAA/B,KAA+B;AACxD,UAAMC,CAAC,GAAGC,mBAAV;AACA,uBAAUF,UAAU,GAAG,WAAH,GAAiB,EAArC,kBACGC,CAAC,CAAC,KAAKV,MAAN,CADJ,oBAC2BU,CAAC,CAAC,KAAKX,KAAN,CAD5B,kBACgDW,CAAC,CAAC,KAAKZ,GAAN,CADjD;AAED;;;WAED,gBAAOc,KAAP,EAA6C;AAC3C,aACE,oBAAO,KAAKZ,MAAZ,EAAoBY,KAAK,CAACZ,MAA1B,KACA,oBAAO,KAAKD,KAAZ,EAAmBa,KAAK,CAACb,KAAzB,CADA,IAEA,oBAAO,KAAKD,GAAZ,EAAiBc,KAAK,CAACd,GAAvB,CAHF;AAKD;;;WAED,qBAAYc,KAAZ,EAAkD;AAChD,aAAO,KAAKZ,MAAL,KAAgBY,KAAK,CAACZ,MAAtB,IAAgC,KAAKD,KAAL,KAAea,KAAK,CAACb,KAArD,IAA8D,KAAKD,GAAL,KAAac,KAAK,CAACd,GAAxF;AACD;;;SAID,eAAsB;AACpB,aAAO,MAAM,qBAAQ,KAAKA,GAAb,CAAb;AACD,K;SAED,aAAYe,CAAZ,EAAuB;AACrB,WAAKf,GAAL,GAAWgB,IAAI,CAACC,EAAL,GAAU,qBAAQF,CAAR,CAArB;AACD;;;SAED,eAAoB;AAClB,aAAO,qBAAQ,KAAKd,KAAb,CAAP;AACD,K;SAED,aAAUc,CAAV,EAAqB;AACnB,WAAKd,KAAL,GAAa,qBAAQc,CAAR,CAAb;AACD;;;SAMD,eAAwB;AACtB,aAAO,qBAAQ,KAAKf,GAAb,CAAP;AACD;;;SAED,eAAuB;AACrB,aAAO,qBAAQ,KAAKC,KAAb,CAAP;AACD;;;SAED,eAAkB;AAChB,aAAO,qBAAQ,KAAKD,GAAb,CAAP;AACD;;;SAED,eAAkB;AAChB,aAAO,qBAAQ,KAAKC,KAAb,CAAP;AACD;;;SAED,eAAgB;AACd,aAAO,CAAC,KAAKC,MAAL,GAAc,CAAf,IAAoB,KAAKI,WAAhC;AACD;;;WAGD,aAAIJ,MAAJ,EAAoBF,GAApB,EAAiCC,KAAjC,EAAsD;AACpD,WAAKC,MAAL,GAAcA,MAAd;AACA,WAAKF,GAAL,GAAWA,GAAX;AACA,WAAKC,KAAL,GAAaA,KAAb;AACA,aAAO,KAAKO,KAAL,EAAP;AACD;;;WAED,iBAA8B;AAC5B,aAAO,IAAIT,oBAAJ,GAA2BmB,IAA3B,CAAgC,IAAhC,CAAP;AACD;;;WAED,cAAKJ,KAAL,EAAwC;AACtC,WAAKZ,MAAL,GAAcY,KAAK,CAACZ,MAApB;AACA,WAAKF,GAAL,GAAWc,KAAK,CAACd,GAAjB;AACA,WAAKC,KAAL,GAAaa,KAAK,CAACb,KAAnB;AACA,aAAO,KAAKO,KAAL,EAAP;AACD;;;WAED,4BAA2D;AAAA;AAAA,UAA9CW,GAA8C;AAAA,UAAzCC,GAAyC;AAAA,UAApCC,CAAoC;;AACzD,WAAKnB,MAAL,GAAc,IAAImB,CAAC,GAAG,KAAKf,WAA3B;AACA,WAAKN,GAAL,GAAW,qBAAQoB,GAAR,CAAX;AACA,WAAKnB,KAAL,GAAa,qBAAQkB,GAAR,CAAb;AACA,aAAO,KAAKX,KAAL,EAAP;AACD;;;WAED,qBAAYO,CAAZ,EAA6C;AAC3C,WAAKb,MAAL,GAAcoB,IAAI,CAACC,MAAL,CAAYR,CAAZ,CAAd;;AACA,UAAI,KAAKb,MAAL,GAAc,CAAlB,EAAqB;AACnB,aAAKD,KAAL,GAAae,IAAI,CAACQ,KAAL,CAAWT,CAAC,CAAC,CAAD,CAAZ,EAAiBA,CAAC,CAAC,CAAD,CAAlB,CAAb;AACA,aAAKf,GAAL,GAAWgB,IAAI,CAACS,IAAL,CAAU,mBAAMV,CAAC,CAAC,CAAD,CAAD,GAAO,KAAKb,MAAlB,EAA0B,CAAC,CAA3B,EAA8B,CAA9B,CAAV,CAAX;AACD;;AACD,aAAO,KAAKM,KAAL,EAAP;AACD;;;WAED,qBAAqB;AACnB,aAAO,IAAIkB,eAAJ,CAAY,CAAZ,EAAe,CAAf,EAAkB,KAAKxB,MAAvB,EACJyB,OADI,CACI;AAACC,QAAAA,OAAO,EAAE,KAAK3B;AAAf,OADJ,EAEJ4B,OAFI,CAEI;AAACD,QAAAA,OAAO,EAAE,KAAK5B;AAAf,OAFJ,CAAP;AAGD;;;WAGD,oBAAiB;AACf,WAAKA,GAAL,GAAWgB,IAAI,CAACc,GAAL,CAASjC,OAAT,EAAkBmB,IAAI,CAACe,GAAL,CAASf,IAAI,CAACC,EAAL,GAAUpB,OAAnB,EAA4B,KAAKG,GAAjC,CAAlB,CAAX;AACA,aAAO,IAAP;AACD;;;WAED,iBAAc;AAEZ,UAAI,CAACgC,MAAM,CAACC,QAAP,CAAgB,KAAKjC,GAArB,CAAD,IAA8B,CAACgC,MAAM,CAACC,QAAP,CAAgB,KAAKhC,KAArB,CAA/B,IAA8D,EAAE,KAAKC,MAAL,GAAc,CAAhB,CAAlE,EAAsF;AACpF,cAAM,IAAIgC,KAAJ,CAAU,0DAAV,CAAN;AACD;;AACD,aAAO,IAAP;AACD", "sourcesContent": ["// Copyright (c) 2017 Uber Technologies, Inc.\n// MIT License\n// Adaptation of THREE.js Spherical class, under MIT license\nimport Vector3 from './vector3';\nimport {formatValue, equals, config} from '../lib/common';\nimport {degrees, radians, clamp} from '../lib/common';\nimport * as vec3 from 'gl-matrix/vec3';\nimport {NumericArray} from '@math.gl/types';\n\ntype SphericalCoordinatesOptions = {\n  phi?: number;\n  theta?: number;\n  radius?: number;\n  bearing?: number;\n  pitch?: number;\n  altitude?: number;\n  radiusScale?: number;\n};\n\ntype FormatOptions = {\n  printTypes?: boolean;\n};\n\n// TODO - import epsilon\nconst EPSILON = 0.000001;\nconst EARTH_RADIUS_METERS = 6371000;\n\n/**\n * The poles (phi) are at the positive and negative y axis.\n * The equator starts at positive z.\n * @link https://en.wikipedia.org/wiki/Spherical_coordinate_system\n */\nexport default class SphericalCoordinates {\n  phi: number;\n  theta: number;\n  radius: number;\n  radiusScale: number;\n  // bearing: number;\n  // pitch: number;\n  // altitude: number;\n\n  // lnglatZ coordinates\n  // longitude: number;\n  // latitude: number;\n  // lng: number;\n  // lat: number;\n  // z: number;\n\n  /**\n   * Creates a new SphericalCoordinates object\n   * @param options\n   * @param [options.phi] =0 - rotation around X (latitude)\n   * @param [options.theta] =0 - rotation around Y (longitude)\n   * @param [options.radius] =1 - Distance from center\n   * @param [options.bearing]\n   * @param [options.pitch]\n   * @param [options.altitude]\n   * @param [options.radiusScale] =1\n   */\n  // eslint-disable-next-line complexity\n  constructor({\n    phi = 0,\n    theta = 0,\n    radius = 1,\n    bearing,\n    pitch,\n    altitude,\n    radiusScale = EARTH_RADIUS_METERS\n  }: SphericalCoordinatesOptions = {}) {\n    this.phi = phi;\n    this.theta = theta;\n    // TODO - silently accepts illegal 0\n    this.radius = radius || altitude || 1; // radial distance from center\n    this.radiusScale = radiusScale || 1; // Used by lngLatZ\n    if (bearing !== undefined) {\n      this.bearing = bearing; // up / down towards top and bottom pole\n    }\n    if (pitch !== undefined) {\n      this.pitch = pitch; // around the equator of the sphere\n    }\n    this.check();\n  }\n\n  toString(): string {\n    return this.formatString(config);\n  }\n\n  formatString({printTypes = false}: FormatOptions): string {\n    const f = formatValue;\n    return `${printTypes ? 'Spherical' : ''}\\\n[rho:${f(this.radius)},theta:${f(this.theta)},phi:${f(this.phi)}]`;\n  }\n\n  equals(other: SphericalCoordinates): boolean {\n    return (\n      equals(this.radius, other.radius) &&\n      equals(this.theta, other.theta) &&\n      equals(this.phi, other.phi)\n    );\n  }\n\n  exactEquals(other: SphericalCoordinates): boolean {\n    return this.radius === other.radius && this.theta === other.theta && this.phi === other.phi;\n  }\n\n  /* eslint-disable brace-style */\n  // Cartographic (bearing 0 north, pitch 0 look from above)\n  get bearing(): number {\n    return 180 - degrees(this.phi);\n  }\n\n  set bearing(v: number) {\n    this.phi = Math.PI - radians(v);\n  }\n\n  get pitch(): number {\n    return degrees(this.theta);\n  }\n\n  set pitch(v: number) {\n    this.theta = radians(v);\n  }\n\n  // get pitch() { return 90 - degrees(this.phi); }\n  // set pitch(v) { this.phi = radians(v) + Math.PI / 2; }\n  // get altitude() { return this.radius - 1; } // relative altitude\n  // lnglatZ coordinates\n  get longitude(): number {\n    return degrees(this.phi);\n  }\n\n  get latitude(): number {\n    return degrees(this.theta);\n  }\n\n  get lng(): number {\n    return degrees(this.phi);\n  }\n\n  get lat(): number {\n    return degrees(this.theta);\n  }\n\n  get z(): number {\n    return (this.radius - 1) * this.radiusScale;\n  }\n\n  /* eslint-enable brace-style */\n  set(radius: number, phi: number, theta: number): this {\n    this.radius = radius;\n    this.phi = phi;\n    this.theta = theta;\n    return this.check();\n  }\n\n  clone(): SphericalCoordinates {\n    return new SphericalCoordinates().copy(this);\n  }\n\n  copy(other: SphericalCoordinates): this {\n    this.radius = other.radius;\n    this.phi = other.phi;\n    this.theta = other.theta;\n    return this.check();\n  }\n\n  fromLngLatZ([lng, lat, z]: [number, number, number]): this {\n    this.radius = 1 + z / this.radiusScale;\n    this.phi = radians(lat);\n    this.theta = radians(lng);\n    return this.check();\n  }\n\n  fromVector3(v: Readonly<NumericArray>): this {\n    this.radius = vec3.length(v);\n    if (this.radius > 0) {\n      this.theta = Math.atan2(v[0], v[1]); // equator angle around y-up axis\n      this.phi = Math.acos(clamp(v[2] / this.radius, -1, 1)); // polar angle\n    }\n    return this.check();\n  }\n\n  toVector3(): Vector3 {\n    return new Vector3(0, 0, this.radius)\n      .rotateX({radians: this.theta})\n      .rotateZ({radians: this.phi});\n  }\n\n  // restrict phi to be betwee EPS and PI-EPS\n  makeSafe(): this {\n    this.phi = Math.max(EPSILON, Math.min(Math.PI - EPSILON, this.phi));\n    return this;\n  }\n\n  check(): this {\n    // this.makeSafe();\n    if (!Number.isFinite(this.phi) || !Number.isFinite(this.theta) || !(this.radius > 0)) {\n      throw new Error('SphericalCoordinates: some fields set to invalid numbers');\n    }\n    return this;\n  }\n}\n"], "file": "spherical-coordinates.js"}