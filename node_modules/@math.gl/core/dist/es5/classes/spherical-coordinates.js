"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");

var _typeof = require("@babel/runtime/helpers/typeof");

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;

var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));

var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));

var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));

var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));

var _vector = _interopRequireDefault(require("./vector3"));

var _common = require("../lib/common");

var vec3 = _interopRequireWildcard(require("gl-matrix/vec3"));

function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }

function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }

var EPSILON = 0.000001;
var EARTH_RADIUS_METERS = 6371000;

var SphericalCoordinates = function () {
  function SphericalCoordinates() {
    var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},
        _ref$phi = _ref.phi,
        phi = _ref$phi === void 0 ? 0 : _ref$phi,
        _ref$theta = _ref.theta,
        theta = _ref$theta === void 0 ? 0 : _ref$theta,
        _ref$radius = _ref.radius,
        radius = _ref$radius === void 0 ? 1 : _ref$radius,
        bearing = _ref.bearing,
        pitch = _ref.pitch,
        altitude = _ref.altitude,
        _ref$radiusScale = _ref.radiusScale,
        radiusScale = _ref$radiusScale === void 0 ? EARTH_RADIUS_METERS : _ref$radiusScale;

    (0, _classCallCheck2.default)(this, SphericalCoordinates);
    (0, _defineProperty2.default)(this, "phi", void 0);
    (0, _defineProperty2.default)(this, "theta", void 0);
    (0, _defineProperty2.default)(this, "radius", void 0);
    (0, _defineProperty2.default)(this, "radiusScale", void 0);
    this.phi = phi;
    this.theta = theta;
    this.radius = radius || altitude || 1;
    this.radiusScale = radiusScale || 1;

    if (bearing !== undefined) {
      this.bearing = bearing;
    }

    if (pitch !== undefined) {
      this.pitch = pitch;
    }

    this.check();
  }

  (0, _createClass2.default)(SphericalCoordinates, [{
    key: "toString",
    value: function toString() {
      return this.formatString(_common.config);
    }
  }, {
    key: "formatString",
    value: function formatString(_ref2) {
      var _ref2$printTypes = _ref2.printTypes,
          printTypes = _ref2$printTypes === void 0 ? false : _ref2$printTypes;
      var f = _common.formatValue;
      return "".concat(printTypes ? 'Spherical' : '', "[rho:").concat(f(this.radius), ",theta:").concat(f(this.theta), ",phi:").concat(f(this.phi), "]");
    }
  }, {
    key: "equals",
    value: function equals(other) {
      return (0, _common.equals)(this.radius, other.radius) && (0, _common.equals)(this.theta, other.theta) && (0, _common.equals)(this.phi, other.phi);
    }
  }, {
    key: "exactEquals",
    value: function exactEquals(other) {
      return this.radius === other.radius && this.theta === other.theta && this.phi === other.phi;
    }
  }, {
    key: "bearing",
    get: function get() {
      return 180 - (0, _common.degrees)(this.phi);
    },
    set: function set(v) {
      this.phi = Math.PI - (0, _common.radians)(v);
    }
  }, {
    key: "pitch",
    get: function get() {
      return (0, _common.degrees)(this.theta);
    },
    set: function set(v) {
      this.theta = (0, _common.radians)(v);
    }
  }, {
    key: "longitude",
    get: function get() {
      return (0, _common.degrees)(this.phi);
    }
  }, {
    key: "latitude",
    get: function get() {
      return (0, _common.degrees)(this.theta);
    }
  }, {
    key: "lng",
    get: function get() {
      return (0, _common.degrees)(this.phi);
    }
  }, {
    key: "lat",
    get: function get() {
      return (0, _common.degrees)(this.theta);
    }
  }, {
    key: "z",
    get: function get() {
      return (this.radius - 1) * this.radiusScale;
    }
  }, {
    key: "set",
    value: function set(radius, phi, theta) {
      this.radius = radius;
      this.phi = phi;
      this.theta = theta;
      return this.check();
    }
  }, {
    key: "clone",
    value: function clone() {
      return new SphericalCoordinates().copy(this);
    }
  }, {
    key: "copy",
    value: function copy(other) {
      this.radius = other.radius;
      this.phi = other.phi;
      this.theta = other.theta;
      return this.check();
    }
  }, {
    key: "fromLngLatZ",
    value: function fromLngLatZ(_ref3) {
      var _ref4 = (0, _slicedToArray2.default)(_ref3, 3),
          lng = _ref4[0],
          lat = _ref4[1],
          z = _ref4[2];

      this.radius = 1 + z / this.radiusScale;
      this.phi = (0, _common.radians)(lat);
      this.theta = (0, _common.radians)(lng);
      return this.check();
    }
  }, {
    key: "fromVector3",
    value: function fromVector3(v) {
      this.radius = vec3.length(v);

      if (this.radius > 0) {
        this.theta = Math.atan2(v[0], v[1]);
        this.phi = Math.acos((0, _common.clamp)(v[2] / this.radius, -1, 1));
      }

      return this.check();
    }
  }, {
    key: "toVector3",
    value: function toVector3() {
      return new _vector.default(0, 0, this.radius).rotateX({
        radians: this.theta
      }).rotateZ({
        radians: this.phi
      });
    }
  }, {
    key: "makeSafe",
    value: function makeSafe() {
      this.phi = Math.max(EPSILON, Math.min(Math.PI - EPSILON, this.phi));
      return this;
    }
  }, {
    key: "check",
    value: function check() {
      if (!Number.isFinite(this.phi) || !Number.isFinite(this.theta) || !(this.radius > 0)) {
        throw new Error('SphericalCoordinates: some fields set to invalid numbers');
      }

      return this;
    }
  }]);
  return SphericalCoordinates;
}();

exports.default = SphericalCoordinates;
//# sourceMappingURL=spherical-coordinates.js.map