{"version": 3, "sources": ["../../../src/classes/matrix3.ts"], "names": ["INDICES", "IDENTITY_MATRIX", "Object", "freeze", "Matrix3", "array", "args", "arguments", "length", "Array", "isArray", "copy", "identity", "check", "object", "q", "mat3", "fromQuat", "m00", "m10", "m20", "m01", "m11", "m21", "m02", "m12", "m22", "determinant", "transpose", "invert", "a", "multiply", "radians", "rotate", "factor", "scale", "vec", "translate", "vector", "result", "out", "vec2", "transformMat3", "vec3", "Error", "transform", "getIdentityMatrix", "getZeroMatrix", "Matrix", "ZERO_MATRIX3", "IDENTITY_MATRIX3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAEA;;AACA;;AAEA;;AACA;;AACA;;AACA;;;;;;;;;;IAGKA,O;;WAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;GAAAA,O,KAAAA,O;;AAYL,IAAMC,eAAe,GAAGC,MAAM,CAACC,MAAP,CAAc,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,CAAd,CAAxB;;IAEqBC,O;;;;;AAyBnB,mBAAYC,KAAZ,EAAwE;AAAA;;AAAA,sCAAhBC,IAAgB;AAAhBA,MAAAA,IAAgB;AAAA;;AAAA;AAEtE,8BAAM,CAAC,CAAP,EAAU,CAAC,CAAX,EAAc,CAAC,CAAf,EAAkB,CAAC,CAAnB,EAAsB,CAAC,CAAvB,EAA0B,CAAC,CAA3B,EAA8B,CAAC,CAA/B,EAAkC,CAAC,CAAnC,EAAsC,CAAC,CAAvC;;AACA,QAAIC,SAAS,CAACC,MAAV,KAAqB,CAArB,IAA0BC,KAAK,CAACC,OAAN,CAAcL,KAAd,CAA9B,EAAoD;AAClD,YAAKM,IAAL,CAAUN,KAAV;AACD,KAFD,MAEO,IAAIC,IAAI,CAACE,MAAL,GAAc,CAAlB,EAAqB;AAC1B,YAAKG,IAAL,EAAWN,KAAX,SAA+BC,IAA/B;AACD,KAFM,MAEA;AACL,YAAKM,QAAL;AACD;;AATqE;AAUvE;;;;SA1BD,eAAuB;AACrB,aAAO,CAAP;AACD;;;SAED,eAAmB;AACjB,aAAO,CAAP;AACD;;;SAED,eAA8B;AAC5B,aAAOZ,OAAP;AACD;;;WAkBD,cAAKK,KAAL,EAA0C;AAExC,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,aAAO,KAAKQ,KAAL,EAAP;AACD;;;WAID,oBAAiB;AACf,aAAO,KAAKF,IAAL,CAAUV,eAAV,CAAP;AACD;;;WAOD,oBAAWa,MAAX,EAA+C;AAC7C,aAAO,KAAKD,KAAL,EAAP;AACD;;;WAID,wBAAeE,CAAf,EAAgD;AAC9CC,MAAAA,IAAI,CAACC,QAAL,CAAc,IAAd,EAAoBF,CAApB;AACA,aAAO,KAAKF,KAAL,EAAP;AACD;;;WAMD,aACEK,GADF,EAEEC,GAFF,EAGEC,GAHF,EAIEC,GAJF,EAKEC,GALF,EAMEC,GANF,EAOEC,GAPF,EAQEC,GARF,EASEC,GATF,EAUQ;AACN,WAAK,CAAL,IAAUR,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,aAAO,KAAKb,KAAL,EAAP;AACD;;;WAMD,qBACEK,GADF,EAEEG,GAFF,EAGEG,GAHF,EAIEL,GAJF,EAKEG,GALF,EAMEG,GANF,EAOEL,GAPF,EAQEG,GARF,EASEG,GATF,EAUQ;AACN,WAAK,CAAL,IAAUR,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,aAAO,KAAKb,KAAL,EAAP;AACD;;;WAID,uBAAsB;AACpB,aAAOG,IAAI,CAACW,WAAL,CAAiB,IAAjB,CAAP;AACD;;;WAGD,qBAAkB;AAChBX,MAAAA,IAAI,CAACY,SAAL,CAAe,IAAf,EAAqB,IAArB;AACA,aAAO,KAAKf,KAAL,EAAP;AACD;;;WAGD,kBAAe;AACbG,MAAAA,IAAI,CAACa,MAAL,CAAY,IAAZ,EAAkB,IAAlB;AACA,aAAO,KAAKhB,KAAL,EAAP;AACD;;;WAGD,sBAAaiB,CAAb,EAAoC;AAClCd,MAAAA,IAAI,CAACe,QAAL,CAAc,IAAd,EAAoBD,CAApB,EAAuB,IAAvB;AACA,aAAO,KAAKjB,KAAL,EAAP;AACD;;;WAED,uBAAciB,CAAd,EAAqC;AACnCd,MAAAA,IAAI,CAACe,QAAL,CAAc,IAAd,EAAoB,IAApB,EAA0BD,CAA1B;AACA,aAAO,KAAKjB,KAAL,EAAP;AACD;;;WAED,gBAAOmB,OAAP,EAAsC;AACpChB,MAAAA,IAAI,CAACiB,MAAL,CAAY,IAAZ,EAAkB,IAAlB,EAAwBD,OAAxB;AACA,aAAO,KAAKnB,KAAL,EAAP;AACD;;;WAED,eAAMqB,MAAN,EAA2C;AACzC,UAAIzB,KAAK,CAACC,OAAN,CAAcwB,MAAd,CAAJ,EAA2B;AACzBlB,QAAAA,IAAI,CAACmB,KAAL,CAAW,IAAX,EAAiB,IAAjB,EAAuBD,MAAvB;AACD,OAFD,MAEO;AACLlB,QAAAA,IAAI,CAACmB,KAAL,CAAW,IAAX,EAAiB,IAAjB,EAAuB,CAACD,MAAD,EAAmBA,MAAnB,CAAvB;AACD;;AACD,aAAO,KAAKrB,KAAL,EAAP;AACD;;;WAED,mBAAUuB,GAAV,EAAmC;AACjCpB,MAAAA,IAAI,CAACqB,SAAL,CAAe,IAAf,EAAqB,IAArB,EAA2BD,GAA3B;AACA,aAAO,KAAKvB,KAAL,EAAP;AACD;;;WAGD,mBAAUyB,MAAV,EAA0CC,MAA1C,EAA+E;AAC7E,UAAIC,GAAJ;;AACA,cAAQF,MAAM,CAAC9B,MAAf;AACE,aAAK,CAAL;AACEgC,UAAAA,GAAG,GAAGC,IAAI,CAACC,aAAL,CAAmBH,MAAM,IAAI,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,CAA7B,EAAuCD,MAAvC,EAA+C,IAA/C,CAAN;AACA;;AACF,aAAK,CAAL;AACEE,UAAAA,GAAG,GAAGG,IAAI,CAACD,aAAL,CAAmBH,MAAM,IAAI,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,EAAS,CAAC,CAAV,CAA7B,EAA2CD,MAA3C,EAAmD,IAAnD,CAAN;AACA;;AACF,aAAK,CAAL;AACEE,UAAAA,GAAG,GAAG,wCAAmBD,MAAM,IAAI,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,EAAS,CAAC,CAAV,EAAa,CAAC,CAAd,CAA7B,EAA+CD,MAA/C,EAAuD,IAAvD,CAAN;AACA;;AACF;AACE,gBAAM,IAAIM,KAAJ,CAAU,gBAAV,CAAN;AAXJ;;AAaA,mCAAYJ,GAAZ,EAAiBF,MAAM,CAAC9B,MAAxB;AACA,aAAOgC,GAAP;AACD;;;WAGD,yBAAgBF,MAAhB,EAAgDC,MAAhD,EAAqF;AACnF,aAAO,KAAKM,SAAL,CAAeP,MAAf,EAAuBC,MAAvB,CAAP;AACD;;;WAGD,0BAAiBD,MAAjB,EAAiDC,MAAjD,EAAsF;AACpF,aAAO,KAAKM,SAAL,CAAeP,MAAf,EAAuBC,MAAvB,CAAP;AACD;;;WAGD,0BAAiBD,MAAjB,EAAiDC,MAAjD,EAAsF;AACpF,aAAO,KAAKM,SAAL,CAAeP,MAAf,EAAuBC,MAAvB,CAAP;AACD;;;SA/MD,eAAyC;AACvC,aAAOO,iBAAiB,EAAxB;AACD;;;SAED,eAAqC;AACnC,aAAOC,aAAa,EAApB;AACD;;;EAPkCC,e;;;AAmNrC,IAAIC,YAAJ;AACA,IAAIC,gBAAJ;;AAEA,SAASH,aAAT,GAA4C;AAC1C,MAAI,CAACE,YAAL,EAAmB;AACjBA,IAAAA,YAAY,GAAG,IAAI7C,OAAJ,CAAY,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,CAAZ,CAAf;AACAF,IAAAA,MAAM,CAACC,MAAP,CAAc8C,YAAd;AACD;;AACD,SAAOA,YAAP;AACD;;AAED,SAASH,iBAAT,GAAsC;AACpC,MAAI,CAACI,gBAAL,EAAuB;AACrBA,IAAAA,gBAAgB,GAAG,IAAI9C,OAAJ,EAAnB;AACAF,IAAAA,MAAM,CAACC,MAAP,CAAc+C,gBAAd;AACD;;AACD,SAAOA,gBAAP;AACD", "sourcesContent": ["// Copyright (c) 2017 Uber Technologies, Inc.\n// MIT License\nimport Matrix from './base/matrix';\nimport {checkVector} from '../lib/validators';\n/* eslint-disable camelcase */\nimport {vec4_transformMat3} from '../lib/gl-matrix-extras';\nimport * as mat3 from 'gl-matrix/mat3';\nimport * as vec2 from 'gl-matrix/vec2';\nimport * as vec3 from 'gl-matrix/vec3';\nimport {NumericArray} from '@math.gl/types';\n\nenum INDICES {\n  COL0ROW0 = 0,\n  COL0ROW1 = 1,\n  COL0ROW2 = 2,\n  COL1ROW0 = 3,\n  COL1ROW1 = 4,\n  COL1ROW2 = 5,\n  COL2ROW0 = 6,\n  COL2ROW1 = 7,\n  COL2ROW2 = 8\n}\n\nconst IDENTITY_MATRIX = Object.freeze([1, 0, 0, 0, 1, 0, 0, 0, 1]);\n\nexport default class Matrix3 extends Matrix {\n  static get IDENTITY(): Readonly<Matrix3> {\n    return getIdentityMatrix();\n  }\n\n  static get ZERO(): Readonly<Matrix3> {\n    return getZeroMatrix();\n  }\n\n  get ELEMENTS(): number {\n    return 9;\n  }\n\n  get RANK(): number {\n    return 3;\n  }\n\n  get INDICES(): typeof INDICES {\n    return INDICES;\n  }\n\n  constructor(array?: Readonly<NumericArray>);\n  /** @deprecated */\n  constructor(...args: number[]);\n\n  constructor(array?: number | Readonly<NumericArray>, ...args: number[]) {\n    // PERF NOTE: initialize elements as double precision numbers\n    super(-0, -0, -0, -0, -0, -0, -0, -0, -0);\n    if (arguments.length === 1 && Array.isArray(array)) {\n      this.copy(array);\n    } else if (args.length > 0) {\n      this.copy([array as number, ...args]);\n    } else {\n      this.identity();\n    }\n  }\n\n  copy(array: Readonly<NumericArray>): this {\n    // Element wise copy for performance\n    this[0] = array[0];\n    this[1] = array[1];\n    this[2] = array[2];\n    this[3] = array[3];\n    this[4] = array[4];\n    this[5] = array[5];\n    this[6] = array[6];\n    this[7] = array[7];\n    this[8] = array[8];\n    return this.check();\n  }\n\n  // Constructors\n\n  identity(): this {\n    return this.copy(IDENTITY_MATRIX);\n  }\n\n  /**\n   *\n   * @param object\n   * @returns self\n   */\n  fromObject(object: {[key: string]: any}): this {\n    return this.check();\n  }\n\n  // Calculates a 3x3 matrix from the given quaternion\n  // q quat  Quaternion to create matrix from\n  fromQuaternion(q: Readonly<NumericArray>): this {\n    mat3.fromQuat(this, q);\n    return this.check();\n  }\n\n  /**\n   * accepts column major order, stores in column major order\n   */\n  // eslint-disable-next-line max-params\n  set(\n    m00: number,\n    m10: number,\n    m20: number,\n    m01: number,\n    m11: number,\n    m21: number,\n    m02: number,\n    m12: number,\n    m22: number\n  ): this {\n    this[0] = m00;\n    this[1] = m10;\n    this[2] = m20;\n    this[3] = m01;\n    this[4] = m11;\n    this[5] = m21;\n    this[6] = m02;\n    this[7] = m12;\n    this[8] = m22;\n    return this.check();\n  }\n\n  /**\n   * accepts row major order, stores as column major\n   */\n  // eslint-disable-next-line max-params\n  setRowMajor(\n    m00: number,\n    m01: number,\n    m02: number,\n    m10: number,\n    m11: number,\n    m12: number,\n    m20: number,\n    m21: number,\n    m22: number\n  ): this {\n    this[0] = m00;\n    this[1] = m10;\n    this[2] = m20;\n    this[3] = m01;\n    this[4] = m11;\n    this[5] = m21;\n    this[6] = m02;\n    this[7] = m12;\n    this[8] = m22;\n    return this.check();\n  }\n\n  // Accessors\n\n  determinant(): number {\n    return mat3.determinant(this);\n  }\n\n  // Modifiers\n  transpose(): this {\n    mat3.transpose(this, this);\n    return this.check();\n  }\n\n  /** Invert a matrix. Note that this can fail if the matrix is not invertible */\n  invert(): this {\n    mat3.invert(this, this);\n    return this.check();\n  }\n\n  // Operations\n  multiplyLeft(a: NumericArray): this {\n    mat3.multiply(this, a, this);\n    return this.check();\n  }\n\n  multiplyRight(a: NumericArray): this {\n    mat3.multiply(this, this, a);\n    return this.check();\n  }\n\n  rotate(radians: number): NumericArray {\n    mat3.rotate(this, this, radians);\n    return this.check();\n  }\n\n  scale(factor: NumericArray | number): this {\n    if (Array.isArray(factor)) {\n      mat3.scale(this, this, factor);\n    } else {\n      mat3.scale(this, this, [factor as number, factor as number]);\n    }\n    return this.check();\n  }\n\n  translate(vec: NumericArray): this {\n    mat3.translate(this, this, vec);\n    return this.check();\n  }\n\n  // Transforms\n  transform(vector: Readonly<NumericArray>, result?: NumericArray): NumericArray {\n    let out: NumericArray;\n    switch (vector.length) {\n      case 2:\n        out = vec2.transformMat3(result || [-0, -0], vector, this);\n        break;\n      case 3:\n        out = vec3.transformMat3(result || [-0, -0, -0], vector, this);\n        break;\n      case 4:\n        out = vec4_transformMat3(result || [-0, -0, -0, -0], vector, this);\n        break;\n      default:\n        throw new Error('Illegal vector');\n    }\n    checkVector(out, vector.length);\n    return out;\n  }\n\n  /** @deprecated */\n  transformVector(vector: Readonly<NumericArray>, result?: NumericArray): NumericArray {\n    return this.transform(vector, result);\n  }\n\n  /** @deprecated */\n  transformVector2(vector: Readonly<NumericArray>, result?: NumericArray): NumericArray {\n    return this.transform(vector, result);\n  }\n\n  /** @deprecated */\n  transformVector3(vector: Readonly<NumericArray>, result?: NumericArray): NumericArray {\n    return this.transform(vector, result);\n  }\n}\n\nlet ZERO_MATRIX3;\nlet IDENTITY_MATRIX3;\n\nfunction getZeroMatrix(): Readonly<Matrix3> {\n  if (!ZERO_MATRIX3) {\n    ZERO_MATRIX3 = new Matrix3([0, 0, 0, 0, 0, 0, 0, 0, 0]);\n    Object.freeze(ZERO_MATRIX3);\n  }\n  return ZERO_MATRIX3;\n}\n\nfunction getIdentityMatrix(): Matrix3 {\n  if (!IDENTITY_MATRIX3) {\n    IDENTITY_MATRIX3 = new Matrix3();\n    Object.freeze(IDENTITY_MATRIX3);\n  }\n  return IDENTITY_MATRIX3;\n}\n"], "file": "matrix3.js"}