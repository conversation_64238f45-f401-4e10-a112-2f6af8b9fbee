"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");

var _typeof = require("@babel/runtime/helpers/typeof");

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;

var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));

var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));

var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));

var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));

var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));

var _matrix = _interopRequireDefault(require("./base/matrix"));

var _validators = require("../lib/validators");

var _glMatrixExtras = require("../lib/gl-matrix-extras");

var mat4 = _interopRequireWildcard(require("gl-matrix/mat4"));

var vec2 = _interopRequireWildcard(require("gl-matrix/vec2"));

var vec3 = _interopRequireWildcard(require("gl-matrix/vec3"));

var vec4 = _interopRequireWildcard(require("gl-matrix/vec4"));

function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }

function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }

function _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = (0, _getPrototypeOf2.default)(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = (0, _getPrototypeOf2.default)(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return (0, _possibleConstructorReturn2.default)(this, result); }; }

function _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }

var INDICES;

(function (INDICES) {
  INDICES[INDICES["COL0ROW0"] = 0] = "COL0ROW0";
  INDICES[INDICES["COL0ROW1"] = 1] = "COL0ROW1";
  INDICES[INDICES["COL0ROW2"] = 2] = "COL0ROW2";
  INDICES[INDICES["COL0ROW3"] = 3] = "COL0ROW3";
  INDICES[INDICES["COL1ROW0"] = 4] = "COL1ROW0";
  INDICES[INDICES["COL1ROW1"] = 5] = "COL1ROW1";
  INDICES[INDICES["COL1ROW2"] = 6] = "COL1ROW2";
  INDICES[INDICES["COL1ROW3"] = 7] = "COL1ROW3";
  INDICES[INDICES["COL2ROW0"] = 8] = "COL2ROW0";
  INDICES[INDICES["COL2ROW1"] = 9] = "COL2ROW1";
  INDICES[INDICES["COL2ROW2"] = 10] = "COL2ROW2";
  INDICES[INDICES["COL2ROW3"] = 11] = "COL2ROW3";
  INDICES[INDICES["COL3ROW0"] = 12] = "COL3ROW0";
  INDICES[INDICES["COL3ROW1"] = 13] = "COL3ROW1";
  INDICES[INDICES["COL3ROW2"] = 14] = "COL3ROW2";
  INDICES[INDICES["COL3ROW3"] = 15] = "COL3ROW3";
})(INDICES || (INDICES = {}));

var DEFAULT_FOVY = 45 * Math.PI / 180;
var DEFAULT_ASPECT = 1;
var DEFAULT_NEAR = 0.1;
var DEFAULT_FAR = 500;
var IDENTITY_MATRIX = Object.freeze([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]);

var Matrix4 = function (_Matrix) {
  (0, _inherits2.default)(Matrix4, _Matrix);

  var _super = _createSuper(Matrix4);

  function Matrix4(array) {
    var _this;

    (0, _classCallCheck2.default)(this, Matrix4);
    _this = _super.call(this, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0);

    if (arguments.length === 1 && Array.isArray(array)) {
      _this.copy(array);
    } else {
      _this.identity();
    }

    return _this;
  }

  (0, _createClass2.default)(Matrix4, [{
    key: "ELEMENTS",
    get: function get() {
      return 16;
    }
  }, {
    key: "RANK",
    get: function get() {
      return 4;
    }
  }, {
    key: "INDICES",
    get: function get() {
      return INDICES;
    }
  }, {
    key: "copy",
    value: function copy(array) {
      this[0] = array[0];
      this[1] = array[1];
      this[2] = array[2];
      this[3] = array[3];
      this[4] = array[4];
      this[5] = array[5];
      this[6] = array[6];
      this[7] = array[7];
      this[8] = array[8];
      this[9] = array[9];
      this[10] = array[10];
      this[11] = array[11];
      this[12] = array[12];
      this[13] = array[13];
      this[14] = array[14];
      this[15] = array[15];
      return this.check();
    }
  }, {
    key: "set",
    value: function set(m00, m10, m20, m30, m01, m11, m21, m31, m02, m12, m22, m32, m03, m13, m23, m33) {
      this[0] = m00;
      this[1] = m10;
      this[2] = m20;
      this[3] = m30;
      this[4] = m01;
      this[5] = m11;
      this[6] = m21;
      this[7] = m31;
      this[8] = m02;
      this[9] = m12;
      this[10] = m22;
      this[11] = m32;
      this[12] = m03;
      this[13] = m13;
      this[14] = m23;
      this[15] = m33;
      return this.check();
    }
  }, {
    key: "setRowMajor",
    value: function setRowMajor(m00, m01, m02, m03, m10, m11, m12, m13, m20, m21, m22, m23, m30, m31, m32, m33) {
      this[0] = m00;
      this[1] = m10;
      this[2] = m20;
      this[3] = m30;
      this[4] = m01;
      this[5] = m11;
      this[6] = m21;
      this[7] = m31;
      this[8] = m02;
      this[9] = m12;
      this[10] = m22;
      this[11] = m32;
      this[12] = m03;
      this[13] = m13;
      this[14] = m23;
      this[15] = m33;
      return this.check();
    }
  }, {
    key: "toRowMajor",
    value: function toRowMajor(result) {
      result[0] = this[0];
      result[1] = this[4];
      result[2] = this[8];
      result[3] = this[12];
      result[4] = this[1];
      result[5] = this[5];
      result[6] = this[9];
      result[7] = this[13];
      result[8] = this[2];
      result[9] = this[6];
      result[10] = this[10];
      result[11] = this[14];
      result[12] = this[3];
      result[13] = this[7];
      result[14] = this[11];
      result[15] = this[15];
      return result;
    }
  }, {
    key: "identity",
    value: function identity() {
      return this.copy(IDENTITY_MATRIX);
    }
  }, {
    key: "fromObject",
    value: function fromObject(object) {
      return this.check();
    }
  }, {
    key: "fromQuaternion",
    value: function fromQuaternion(quaternion) {
      mat4.fromQuat(this, quaternion);
      return this.check();
    }
  }, {
    key: "frustum",
    value: function frustum(view) {
      var left = view.left,
          right = view.right,
          bottom = view.bottom,
          top = view.top,
          _view$near = view.near,
          near = _view$near === void 0 ? DEFAULT_NEAR : _view$near,
          _view$far = view.far,
          far = _view$far === void 0 ? DEFAULT_FAR : _view$far;

      if (far === Infinity) {
        computeInfinitePerspectiveOffCenter(this, left, right, bottom, top, near);
      } else {
        mat4.frustum(this, left, right, bottom, top, near, far);
      }

      return this.check();
    }
  }, {
    key: "lookAt",
    value: function lookAt(view) {
      var eye = view.eye,
          _view$center = view.center,
          center = _view$center === void 0 ? [0, 0, 0] : _view$center,
          _view$up = view.up,
          up = _view$up === void 0 ? [0, 1, 0] : _view$up;
      mat4.lookAt(this, eye, center, up);
      return this.check();
    }
  }, {
    key: "ortho",
    value: function ortho(view) {
      var left = view.left,
          right = view.right,
          bottom = view.bottom,
          top = view.top,
          _view$near2 = view.near,
          near = _view$near2 === void 0 ? DEFAULT_NEAR : _view$near2,
          _view$far2 = view.far,
          far = _view$far2 === void 0 ? DEFAULT_FAR : _view$far2;
      mat4.ortho(this, left, right, bottom, top, near, far);
      return this.check();
    }
  }, {
    key: "orthographic",
    value: function orthographic(view) {
      var _view$fovy = view.fovy,
          fovy = _view$fovy === void 0 ? DEFAULT_FOVY : _view$fovy,
          _view$aspect = view.aspect,
          aspect = _view$aspect === void 0 ? DEFAULT_ASPECT : _view$aspect,
          _view$focalDistance = view.focalDistance,
          focalDistance = _view$focalDistance === void 0 ? 1 : _view$focalDistance,
          _view$near3 = view.near,
          near = _view$near3 === void 0 ? DEFAULT_NEAR : _view$near3,
          _view$far3 = view.far,
          far = _view$far3 === void 0 ? DEFAULT_FAR : _view$far3;
      checkRadians(fovy);
      var halfY = fovy / 2;
      var top = focalDistance * Math.tan(halfY);
      var right = top * aspect;
      return this.ortho({
        left: -right,
        right: right,
        bottom: -top,
        top: top,
        near: near,
        far: far
      });
    }
  }, {
    key: "perspective",
    value: function perspective(view) {
      var _view$fovy2 = view.fovy,
          fovy = _view$fovy2 === void 0 ? 45 * Math.PI / 180 : _view$fovy2,
          _view$aspect2 = view.aspect,
          aspect = _view$aspect2 === void 0 ? 1 : _view$aspect2,
          _view$near4 = view.near,
          near = _view$near4 === void 0 ? 0.1 : _view$near4,
          _view$far4 = view.far,
          far = _view$far4 === void 0 ? 500 : _view$far4;
      checkRadians(fovy);
      mat4.perspective(this, fovy, aspect, near, far);
      return this.check();
    }
  }, {
    key: "determinant",
    value: function determinant() {
      return mat4.determinant(this);
    }
  }, {
    key: "getScale",
    value: function getScale() {
      var result = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [-0, -0, -0];
      result[0] = Math.sqrt(this[0] * this[0] + this[1] * this[1] + this[2] * this[2]);
      result[1] = Math.sqrt(this[4] * this[4] + this[5] * this[5] + this[6] * this[6]);
      result[2] = Math.sqrt(this[8] * this[8] + this[9] * this[9] + this[10] * this[10]);
      return result;
    }
  }, {
    key: "getTranslation",
    value: function getTranslation() {
      var result = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [-0, -0, -0];
      result[0] = this[12];
      result[1] = this[13];
      result[2] = this[14];
      return result;
    }
  }, {
    key: "getRotation",
    value: function getRotation(result, scaleResult) {
      result = result || [-0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0];
      scaleResult = scaleResult || [-0, -0, -0];
      var scale = this.getScale(scaleResult);
      var inverseScale0 = 1 / scale[0];
      var inverseScale1 = 1 / scale[1];
      var inverseScale2 = 1 / scale[2];
      result[0] = this[0] * inverseScale0;
      result[1] = this[1] * inverseScale1;
      result[2] = this[2] * inverseScale2;
      result[3] = 0;
      result[4] = this[4] * inverseScale0;
      result[5] = this[5] * inverseScale1;
      result[6] = this[6] * inverseScale2;
      result[7] = 0;
      result[8] = this[8] * inverseScale0;
      result[9] = this[9] * inverseScale1;
      result[10] = this[10] * inverseScale2;
      result[11] = 0;
      result[12] = 0;
      result[13] = 0;
      result[14] = 0;
      result[15] = 1;
      return result;
    }
  }, {
    key: "getRotationMatrix3",
    value: function getRotationMatrix3(result, scaleResult) {
      result = result || [-0, -0, -0, -0, -0, -0, -0, -0, -0];
      scaleResult = scaleResult || [-0, -0, -0];
      var scale = this.getScale(scaleResult);
      var inverseScale0 = 1 / scale[0];
      var inverseScale1 = 1 / scale[1];
      var inverseScale2 = 1 / scale[2];
      result[0] = this[0] * inverseScale0;
      result[1] = this[1] * inverseScale1;
      result[2] = this[2] * inverseScale2;
      result[3] = this[4] * inverseScale0;
      result[4] = this[5] * inverseScale1;
      result[5] = this[6] * inverseScale2;
      result[6] = this[8] * inverseScale0;
      result[7] = this[9] * inverseScale1;
      result[8] = this[10] * inverseScale2;
      return result;
    }
  }, {
    key: "transpose",
    value: function transpose() {
      mat4.transpose(this, this);
      return this.check();
    }
  }, {
    key: "invert",
    value: function invert() {
      mat4.invert(this, this);
      return this.check();
    }
  }, {
    key: "multiplyLeft",
    value: function multiplyLeft(a) {
      mat4.multiply(this, a, this);
      return this.check();
    }
  }, {
    key: "multiplyRight",
    value: function multiplyRight(a) {
      mat4.multiply(this, this, a);
      return this.check();
    }
  }, {
    key: "rotateX",
    value: function rotateX(radians) {
      mat4.rotateX(this, this, radians);
      return this.check();
    }
  }, {
    key: "rotateY",
    value: function rotateY(radians) {
      mat4.rotateY(this, this, radians);
      return this.check();
    }
  }, {
    key: "rotateZ",
    value: function rotateZ(radians) {
      mat4.rotateZ(this, this, radians);
      return this.check();
    }
  }, {
    key: "rotateXYZ",
    value: function rotateXYZ(angleXYZ) {
      return this.rotateX(angleXYZ[0]).rotateY(angleXYZ[1]).rotateZ(angleXYZ[2]);
    }
  }, {
    key: "rotateAxis",
    value: function rotateAxis(radians, axis) {
      mat4.rotate(this, this, radians, axis);
      return this.check();
    }
  }, {
    key: "scale",
    value: function scale(factor) {
      mat4.scale(this, this, Array.isArray(factor) ? factor : [factor, factor, factor]);
      return this.check();
    }
  }, {
    key: "translate",
    value: function translate(vector) {
      mat4.translate(this, this, vector);
      return this.check();
    }
  }, {
    key: "transform",
    value: function transform(vector, result) {
      if (vector.length === 4) {
        result = vec4.transformMat4(result || [-0, -0, -0, -0], vector, this);
        (0, _validators.checkVector)(result, 4);
        return result;
      }

      return this.transformAsPoint(vector, result);
    }
  }, {
    key: "transformAsPoint",
    value: function transformAsPoint(vector, result) {
      var length = vector.length;
      var out;

      switch (length) {
        case 2:
          out = vec2.transformMat4(result || [-0, -0], vector, this);
          break;

        case 3:
          out = vec3.transformMat4(result || [-0, -0, -0], vector, this);
          break;

        default:
          throw new Error('Illegal vector');
      }

      (0, _validators.checkVector)(out, vector.length);
      return out;
    }
  }, {
    key: "transformAsVector",
    value: function transformAsVector(vector, result) {
      var out;

      switch (vector.length) {
        case 2:
          out = (0, _glMatrixExtras.vec2_transformMat4AsVector)(result || [-0, -0], vector, this);
          break;

        case 3:
          out = (0, _glMatrixExtras.vec3_transformMat4AsVector)(result || [-0, -0, -0], vector, this);
          break;

        default:
          throw new Error('Illegal vector');
      }

      (0, _validators.checkVector)(out, vector.length);
      return out;
    }
  }, {
    key: "transformPoint",
    value: function transformPoint(vector, result) {
      return this.transformAsPoint(vector, result);
    }
  }, {
    key: "transformVector",
    value: function transformVector(vector, result) {
      return this.transformAsPoint(vector, result);
    }
  }, {
    key: "transformDirection",
    value: function transformDirection(vector, result) {
      return this.transformAsVector(vector, result);
    }
  }, {
    key: "makeRotationX",
    value: function makeRotationX(radians) {
      return this.identity().rotateX(radians);
    }
  }, {
    key: "makeTranslation",
    value: function makeTranslation(x, y, z) {
      return this.identity().translate([x, y, z]);
    }
  }], [{
    key: "IDENTITY",
    get: function get() {
      return getIdentityMatrix();
    }
  }, {
    key: "ZERO",
    get: function get() {
      return getZeroMatrix();
    }
  }]);
  return Matrix4;
}(_matrix.default);

exports.default = Matrix4;
var ZERO;
var IDENTITY;

function getZeroMatrix() {
  if (!ZERO) {
    ZERO = new Matrix4([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]);
    Object.freeze(ZERO);
  }

  return ZERO;
}

function getIdentityMatrix() {
  if (!IDENTITY) {
    IDENTITY = new Matrix4();
    Object.freeze(IDENTITY);
  }

  return IDENTITY;
}

function checkRadians(possiblyDegrees) {
  if (possiblyDegrees > Math.PI * 2) {
    throw Error('expected radians');
  }
}

function computeInfinitePerspectiveOffCenter(result, left, right, bottom, top, near) {
  var column0Row0 = 2 * near / (right - left);
  var column1Row1 = 2 * near / (top - bottom);
  var column2Row0 = (right + left) / (right - left);
  var column2Row1 = (top + bottom) / (top - bottom);
  var column2Row2 = -1;
  var column2Row3 = -1;
  var column3Row2 = -2 * near;
  result[0] = column0Row0;
  result[1] = 0;
  result[2] = 0;
  result[3] = 0;
  result[4] = 0;
  result[5] = column1Row1;
  result[6] = 0;
  result[7] = 0;
  result[8] = column2Row0;
  result[9] = column2Row1;
  result[10] = column2Row2;
  result[11] = column2Row3;
  result[12] = 0;
  result[13] = 0;
  result[14] = column3Row2;
  result[15] = 0;
  return result;
}
//# sourceMappingURL=matrix4.js.map