"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");

var _typeof = require("@babel/runtime/helpers/typeof");

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;

var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));

var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));

var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));

var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));

var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));

var _vector = _interopRequireDefault(require("./base/vector"));

var _common = require("../lib/common");

var _validators = require("../lib/validators");

var vec4 = _interopRequireWildcard(require("gl-matrix/vec3"));

var _glMatrixExtras = require("../lib/gl-matrix-extras");

function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }

function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }

function _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = (0, _getPrototypeOf2.default)(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = (0, _getPrototypeOf2.default)(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return (0, _possibleConstructorReturn2.default)(this, result); }; }

function _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }

var ZERO;

var Vector4 = function (_Vector) {
  (0, _inherits2.default)(Vector4, _Vector);

  var _super = _createSuper(Vector4);

  function Vector4() {
    var _this;

    var x = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;
    var y = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
    var z = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;
    var w = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;
    (0, _classCallCheck2.default)(this, Vector4);
    _this = _super.call(this, -0, -0, -0, -0);

    if ((0, _common.isArray)(x) && arguments.length === 1) {
      _this.copy(x);
    } else {
      if (_common.config.debug) {
        (0, _validators.checkNumber)(x);
        (0, _validators.checkNumber)(y);
        (0, _validators.checkNumber)(z);
        (0, _validators.checkNumber)(w);
      }

      _this[0] = x;
      _this[1] = y;
      _this[2] = z;
      _this[3] = w;
    }

    return _this;
  }

  (0, _createClass2.default)(Vector4, [{
    key: "set",
    value: function set(x, y, z, w) {
      this[0] = x;
      this[1] = y;
      this[2] = z;
      this[3] = w;
      return this.check();
    }
  }, {
    key: "copy",
    value: function copy(array) {
      this[0] = array[0];
      this[1] = array[1];
      this[2] = array[2];
      this[3] = array[3];
      return this.check();
    }
  }, {
    key: "fromObject",
    value: function fromObject(object) {
      if (_common.config.debug) {
        (0, _validators.checkNumber)(object.x);
        (0, _validators.checkNumber)(object.y);
        (0, _validators.checkNumber)(object.z);
        (0, _validators.checkNumber)(object.w);
      }

      this[0] = object.x;
      this[1] = object.y;
      this[2] = object.z;
      this[3] = object.w;
      return this;
    }
  }, {
    key: "toObject",
    value: function toObject(object) {
      object.x = this[0];
      object.y = this[1];
      object.z = this[2];
      object.w = this[3];
      return object;
    }
  }, {
    key: "ELEMENTS",
    get: function get() {
      return 4;
    }
  }, {
    key: "z",
    get: function get() {
      return this[2];
    },
    set: function set(value) {
      this[2] = (0, _validators.checkNumber)(value);
    }
  }, {
    key: "w",
    get: function get() {
      return this[3];
    },
    set: function set(value) {
      this[3] = (0, _validators.checkNumber)(value);
    }
  }, {
    key: "transform",
    value: function transform(matrix4) {
      vec4.transformMat4(this, this, matrix4);
      return this.check();
    }
  }, {
    key: "transformByMatrix3",
    value: function transformByMatrix3(matrix3) {
      (0, _glMatrixExtras.vec4_transformMat3)(this, this, matrix3);
      return this.check();
    }
  }, {
    key: "transformByMatrix2",
    value: function transformByMatrix2(matrix2) {
      (0, _glMatrixExtras.vec4_transformMat2)(this, this, matrix2);
      return this.check();
    }
  }, {
    key: "transformByQuaternion",
    value: function transformByQuaternion(quaternion) {
      vec4.transformQuat(this, this, quaternion);
      return this.check();
    }
  }, {
    key: "applyMatrix4",
    value: function applyMatrix4(m) {
      m.transform(this, this);
      return this;
    }
  }], [{
    key: "ZERO",
    get: function get() {
      if (!ZERO) {
        ZERO = new Vector4(0, 0, 0, 0);
        Object.freeze(ZERO);
      }

      return ZERO;
    }
  }]);
  return Vector4;
}(_vector.default);

exports.default = Vector4;
//# sourceMappingURL=vector4.js.map