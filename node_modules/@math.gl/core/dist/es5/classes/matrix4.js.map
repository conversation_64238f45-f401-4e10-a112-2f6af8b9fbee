{"version": 3, "sources": ["../../../src/classes/matrix4.ts"], "names": ["INDICES", "DEFAULT_FOVY", "Math", "PI", "DEFAULT_ASPECT", "DEFAULT_NEAR", "DEFAULT_FAR", "IDENTITY_MATRIX", "Object", "freeze", "Matrix4", "array", "arguments", "length", "Array", "isArray", "copy", "identity", "check", "m00", "m10", "m20", "m30", "m01", "m11", "m21", "m31", "m02", "m12", "m22", "m32", "m03", "m13", "m23", "m33", "result", "object", "quaternion", "mat4", "fromQuat", "view", "left", "right", "bottom", "top", "near", "far", "Infinity", "computeInfinitePerspectiveOffCenter", "frustum", "eye", "center", "up", "lookAt", "ortho", "fovy", "aspect", "focalDistance", "checkRadians", "halfY", "tan", "perspective", "determinant", "sqrt", "scaleResult", "scale", "getScale", "inverseScale0", "inverseScale1", "inverseScale2", "transpose", "invert", "a", "multiply", "radians", "rotateX", "rotateY", "rotateZ", "angleXYZ", "axis", "rotate", "factor", "vector", "translate", "vec4", "transformMat4", "transformAsPoint", "out", "vec2", "vec3", "Error", "transformAsVector", "x", "y", "z", "getIdentityMatrix", "getZeroMatrix", "Matrix", "ZERO", "IDENTITY", "possiblyDegrees", "column0Row0", "column1Row1", "column2Row0", "column2Row1", "column2Row2", "column2Row3", "column3Row2"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAEA;;AAEA;;AAGA;;AACA;;AACA;;AACA;;AACA;;;;;;;;;;IAEKA,O;;WAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;AAAAA,EAAAA,O,CAAAA,O;GAAAA,O,KAAAA,O;;AAmBL,IAAMC,YAAY,GAAI,KAAKC,IAAI,CAACC,EAAX,GAAiB,GAAtC;AACA,IAAMC,cAAc,GAAG,CAAvB;AACA,IAAMC,YAAY,GAAG,GAArB;AACA,IAAMC,WAAW,GAAG,GAApB;AAEA,IAAMC,eAAe,GAAGC,MAAM,CAACC,MAAP,CAAc,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B,EAAkC,CAAlC,EAAqC,CAArC,EAAwC,CAAxC,EAA2C,CAA3C,EAA8C,CAA9C,CAAd,CAAxB;;IAGqBC,O;;;;;AAqBnB,mBAAYC,KAAZ,EAA4C;AAAA;;AAAA;AAE1C,8BAAM,CAAC,CAAP,EAAU,CAAC,CAAX,EAAc,CAAC,CAAf,EAAkB,CAAC,CAAnB,EAAsB,CAAC,CAAvB,EAA0B,CAAC,CAA3B,EAA8B,CAAC,CAA/B,EAAkC,CAAC,CAAnC,EAAsC,CAAC,CAAvC,EAA0C,CAAC,CAA3C,EAA8C,CAAC,CAA/C,EAAkD,CAAC,CAAnD,EAAsD,CAAC,CAAvD,EAA0D,CAAC,CAA3D,EAA8D,CAAC,CAA/D,EAAkE,CAAC,CAAnE;;AACA,QAAIC,SAAS,CAACC,MAAV,KAAqB,CAArB,IAA0BC,KAAK,CAACC,OAAN,CAAcJ,KAAd,CAA9B,EAAoD;AAClD,YAAKK,IAAL,CAAUL,KAAV;AACD,KAFD,MAEO;AACL,YAAKM,QAAL;AACD;;AAPyC;AAQ3C;;;;SApBD,eAAuB;AACrB,aAAO,EAAP;AACD;;;SAED,eAAmB;AACjB,aAAO,CAAP;AACD;;;SAED,eAA8B;AAC5B,aAAOjB,OAAP;AACD;;;WAYD,cAAKW,KAAL,EAA0C;AACxC,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,EAAL,IAAWA,KAAK,CAAC,EAAD,CAAhB;AACA,WAAK,EAAL,IAAWA,KAAK,CAAC,EAAD,CAAhB;AACA,WAAK,EAAL,IAAWA,KAAK,CAAC,EAAD,CAAhB;AACA,WAAK,EAAL,IAAWA,KAAK,CAAC,EAAD,CAAhB;AACA,WAAK,EAAL,IAAWA,KAAK,CAAC,EAAD,CAAhB;AACA,WAAK,EAAL,IAAWA,KAAK,CAAC,EAAD,CAAhB;AACA,aAAO,KAAKO,KAAL,EAAP;AACD;;;WAGD,aACEC,GADF,EAEEC,GAFF,EAGEC,GAHF,EAIEC,GAJF,EAKEC,GALF,EAMEC,GANF,EAOEC,GAPF,EAQEC,GARF,EASEC,GATF,EAUEC,GAVF,EAWEC,GAXF,EAYEC,GAZF,EAaEC,GAbF,EAcEC,GAdF,EAeEC,GAfF,EAgBEC,GAhBF,EAiBQ;AACN,WAAK,CAAL,IAAUf,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,EAAL,IAAWC,GAAX;AACA,WAAK,EAAL,IAAWC,GAAX;AACA,WAAK,EAAL,IAAWC,GAAX;AACA,WAAK,EAAL,IAAWC,GAAX;AACA,WAAK,EAAL,IAAWC,GAAX;AACA,WAAK,EAAL,IAAWC,GAAX;AACA,aAAO,KAAKhB,KAAL,EAAP;AACD;;;WAID,qBACEC,GADF,EAEEI,GAFF,EAGEI,GAHF,EAIEI,GAJF,EAKEX,GALF,EAMEI,GANF,EAOEI,GAPF,EAQEI,GARF,EASEX,GATF,EAUEI,GAVF,EAWEI,GAXF,EAYEI,GAZF,EAaEX,GAbF,EAcEI,GAdF,EAeEI,GAfF,EAgBEI,GAhBF,EAiBQ;AACN,WAAK,CAAL,IAAUf,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,CAAL,IAAUC,GAAV;AACA,WAAK,EAAL,IAAWC,GAAX;AACA,WAAK,EAAL,IAAWC,GAAX;AACA,WAAK,EAAL,IAAWC,GAAX;AACA,WAAK,EAAL,IAAWC,GAAX;AACA,WAAK,EAAL,IAAWC,GAAX;AACA,WAAK,EAAL,IAAWC,GAAX;AACA,aAAO,KAAKhB,KAAL,EAAP;AACD;;;WAED,oBAAWiB,MAAX,EAA+C;AAC7CA,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,CAAZ;AACAA,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,CAAZ;AACAA,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,CAAZ;AACAA,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,EAAL,CAAZ;AACAA,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,CAAZ;AACAA,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,CAAZ;AACAA,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,CAAZ;AACAA,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,EAAL,CAAZ;AACAA,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,CAAZ;AACAA,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,CAAZ;AACAA,MAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,KAAK,EAAL,CAAb;AACAA,MAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,KAAK,EAAL,CAAb;AACAA,MAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,KAAK,CAAL,CAAb;AACAA,MAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,KAAK,CAAL,CAAb;AACAA,MAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,KAAK,EAAL,CAAb;AACAA,MAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,KAAK,EAAL,CAAb;AACA,aAAOA,MAAP;AACD;;;WAKD,oBAAiB;AACf,aAAO,KAAKnB,IAAL,CAAUT,eAAV,CAAP;AACD;;;WAOD,oBAAW6B,MAAX,EAA+C;AAC7C,aAAO,KAAKlB,KAAL,EAAP;AACD;;;WAOD,wBAAemB,UAAf,EAAyD;AACvDC,MAAAA,IAAI,CAACC,QAAL,CAAc,IAAd,EAAoBF,UAApB;AACA,aAAO,KAAKnB,KAAL,EAAP;AACD;;;WAYD,iBAAQsB,IAAR,EAOS;AACP,UAAOC,IAAP,GAA2ED,IAA3E,CAAOC,IAAP;AAAA,UAAaC,KAAb,GAA2EF,IAA3E,CAAaE,KAAb;AAAA,UAAoBC,MAApB,GAA2EH,IAA3E,CAAoBG,MAApB;AAAA,UAA4BC,GAA5B,GAA2EJ,IAA3E,CAA4BI,GAA5B;AAAA,uBAA2EJ,IAA3E,CAAiCK,IAAjC;AAAA,UAAiCA,IAAjC,2BAAwCxC,YAAxC;AAAA,sBAA2EmC,IAA3E,CAAsDM,GAAtD;AAAA,UAAsDA,GAAtD,0BAA4DxC,WAA5D;;AACA,UAAIwC,GAAG,KAAKC,QAAZ,EAAsB;AACpBC,QAAAA,mCAAmC,CAAC,IAAD,EAAOP,IAAP,EAAaC,KAAb,EAAoBC,MAApB,EAA4BC,GAA5B,EAAiCC,IAAjC,CAAnC;AACD,OAFD,MAEO;AACLP,QAAAA,IAAI,CAACW,OAAL,CAAa,IAAb,EAAmBR,IAAnB,EAAyBC,KAAzB,EAAgCC,MAAhC,EAAwCC,GAAxC,EAA6CC,IAA7C,EAAmDC,GAAnD;AACD;;AACD,aAAO,KAAK5B,KAAL,EAAP;AACD;;;WAUD,gBAAOsB,IAAP,EAIS;AACP,UAAOU,GAAP,GAAkDV,IAAlD,CAAOU,GAAP;AAAA,yBAAkDV,IAAlD,CAAYW,MAAZ;AAAA,UAAYA,MAAZ,6BAAqB,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAArB;AAAA,qBAAkDX,IAAlD,CAAgCY,EAAhC;AAAA,UAAgCA,EAAhC,yBAAqC,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAArC;AACAd,MAAAA,IAAI,CAACe,MAAL,CAAY,IAAZ,EAAkBH,GAAlB,EAAuBC,MAAvB,EAA+BC,EAA/B;AACA,aAAO,KAAKlC,KAAL,EAAP;AACD;;;WAaD,eAAMsB,IAAN,EAOS;AACP,UAAOC,IAAP,GAA2ED,IAA3E,CAAOC,IAAP;AAAA,UAAaC,KAAb,GAA2EF,IAA3E,CAAaE,KAAb;AAAA,UAAoBC,MAApB,GAA2EH,IAA3E,CAAoBG,MAApB;AAAA,UAA4BC,GAA5B,GAA2EJ,IAA3E,CAA4BI,GAA5B;AAAA,wBAA2EJ,IAA3E,CAAiCK,IAAjC;AAAA,UAAiCA,IAAjC,4BAAwCxC,YAAxC;AAAA,uBAA2EmC,IAA3E,CAAsDM,GAAtD;AAAA,UAAsDA,GAAtD,2BAA4DxC,WAA5D;AACAgC,MAAAA,IAAI,CAACgB,KAAL,CAAW,IAAX,EAAiBb,IAAjB,EAAuBC,KAAvB,EAA8BC,MAA9B,EAAsCC,GAAtC,EAA2CC,IAA3C,EAAiDC,GAAjD;AACA,aAAO,KAAK5B,KAAL,EAAP;AACD;;;WAYD,sBAAasB,IAAb,EAMS;AACP,uBAMIA,IANJ,CACEe,IADF;AAAA,UACEA,IADF,2BACStD,YADT;AAAA,yBAMIuC,IANJ,CAEEgB,MAFF;AAAA,UAEEA,MAFF,6BAEWpD,cAFX;AAAA,gCAMIoC,IANJ,CAGEiB,aAHF;AAAA,UAGEA,aAHF,oCAGkB,CAHlB;AAAA,wBAMIjB,IANJ,CAIEK,IAJF;AAAA,UAIEA,IAJF,4BAISxC,YAJT;AAAA,uBAMImC,IANJ,CAKEM,GALF;AAAA,UAKEA,GALF,2BAKQxC,WALR;AAQAoD,MAAAA,YAAY,CAACH,IAAD,CAAZ;AAEA,UAAMI,KAAK,GAAGJ,IAAI,GAAG,CAArB;AACA,UAAMX,GAAG,GAAGa,aAAa,GAAGvD,IAAI,CAAC0D,GAAL,CAASD,KAAT,CAA5B;AACA,UAAMjB,KAAK,GAAGE,GAAG,GAAGY,MAApB;AAEA,aAAO,KAAKF,KAAL,CAAW;AAChBb,QAAAA,IAAI,EAAE,CAACC,KADS;AAEhBA,QAAAA,KAAK,EAALA,KAFgB;AAGhBC,QAAAA,MAAM,EAAE,CAACC,GAHO;AAIhBA,QAAAA,GAAG,EAAHA,GAJgB;AAKhBC,QAAAA,IAAI,EAAJA,IALgB;AAMhBC,QAAAA,GAAG,EAAHA;AANgB,OAAX,CAAP;AAQD;;;WAUD,qBAAYN,IAAZ,EAAsF;AACpF,wBAAyEA,IAAzE,CAAOe,IAAP;AAAA,UAAOA,IAAP,4BAAe,KAAKrD,IAAI,CAACC,EAAX,GAAiB,GAA/B;AAAA,0BAAyEqC,IAAzE,CAAoCgB,MAApC;AAAA,UAAoCA,MAApC,8BAA6C,CAA7C;AAAA,wBAAyEhB,IAAzE,CAAgDK,IAAhD;AAAA,UAAgDA,IAAhD,4BAAuD,GAAvD;AAAA,uBAAyEL,IAAzE,CAA4DM,GAA5D;AAAA,UAA4DA,GAA5D,2BAAkE,GAAlE;AACAY,MAAAA,YAAY,CAACH,IAAD,CAAZ;AACAjB,MAAAA,IAAI,CAACuB,WAAL,CAAiB,IAAjB,EAAuBN,IAAvB,EAA6BC,MAA7B,EAAqCX,IAArC,EAA2CC,GAA3C;AACA,aAAO,KAAK5B,KAAL,EAAP;AACD;;;WAID,uBAAsB;AACpB,aAAOoB,IAAI,CAACwB,WAAL,CAAiB,IAAjB,CAAP;AACD;;;WAQD,oBAA4D;AAAA,UAAnD3B,MAAmD,uEAA5B,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,EAAS,CAAC,CAAV,CAA4B;AAE1DA,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAYjC,IAAI,CAAC6D,IAAL,CAAU,KAAK,CAAL,IAAU,KAAK,CAAL,CAAV,GAAoB,KAAK,CAAL,IAAU,KAAK,CAAL,CAA9B,GAAwC,KAAK,CAAL,IAAU,KAAK,CAAL,CAA5D,CAAZ;AACA5B,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAYjC,IAAI,CAAC6D,IAAL,CAAU,KAAK,CAAL,IAAU,KAAK,CAAL,CAAV,GAAoB,KAAK,CAAL,IAAU,KAAK,CAAL,CAA9B,GAAwC,KAAK,CAAL,IAAU,KAAK,CAAL,CAA5D,CAAZ;AACA5B,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAYjC,IAAI,CAAC6D,IAAL,CAAU,KAAK,CAAL,IAAU,KAAK,CAAL,CAAV,GAAoB,KAAK,CAAL,IAAU,KAAK,CAAL,CAA9B,GAAwC,KAAK,EAAL,IAAW,KAAK,EAAL,CAA7D,CAAZ;AAIA,aAAO5B,MAAP;AACD;;;WAOD,0BAAkE;AAAA,UAAnDA,MAAmD,uEAA5B,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,EAAS,CAAC,CAAV,CAA4B;AAChEA,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,EAAL,CAAZ;AACAA,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,EAAL,CAAZ;AACAA,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,EAAL,CAAZ;AACA,aAAOA,MAAP;AACD;;;WAQD,qBAAYA,MAAZ,EAAmC6B,WAAnC,EAA6E;AAC3E7B,MAAAA,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,EAAS,CAAC,CAAV,EAAa,CAAC,CAAd,EAAiB,CAAC,CAAlB,EAAqB,CAAC,CAAtB,EAAyB,CAAC,CAA1B,EAA6B,CAAC,CAA9B,EAAiC,CAAC,CAAlC,EAAqC,CAAC,CAAtC,EAAyC,CAAC,CAA1C,EAA6C,CAAC,CAA9C,EAAiD,CAAC,CAAlD,EAAqD,CAAC,CAAtD,EAAyD,CAAC,CAA1D,EAA6D,CAAC,CAA9D,CAAnB;AACA6B,MAAAA,WAAW,GAAGA,WAAW,IAAI,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,EAAS,CAAC,CAAV,CAA7B;AACA,UAAMC,KAAK,GAAG,KAAKC,QAAL,CAAcF,WAAd,CAAd;AACA,UAAMG,aAAa,GAAG,IAAIF,KAAK,CAAC,CAAD,CAA/B;AACA,UAAMG,aAAa,GAAG,IAAIH,KAAK,CAAC,CAAD,CAA/B;AACA,UAAMI,aAAa,GAAG,IAAIJ,KAAK,CAAC,CAAD,CAA/B;AACA9B,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUgC,aAAtB;AACAhC,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUiC,aAAtB;AACAjC,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUkC,aAAtB;AACAlC,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,CAAZ;AACAA,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUgC,aAAtB;AACAhC,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUiC,aAAtB;AACAjC,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUkC,aAAtB;AACAlC,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,CAAZ;AACAA,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUgC,aAAtB;AACAhC,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUiC,aAAtB;AACAjC,MAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,KAAK,EAAL,IAAWkC,aAAxB;AACAlC,MAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,CAAb;AACAA,MAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,CAAb;AACAA,MAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,CAAb;AACAA,MAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,CAAb;AACAA,MAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,CAAb;AACA,aAAOA,MAAP;AACD;;;WAQD,4BAAmBA,MAAnB,EAA0C6B,WAA1C,EAAoF;AAClF7B,MAAAA,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,EAAS,CAAC,CAAV,EAAa,CAAC,CAAd,EAAiB,CAAC,CAAlB,EAAqB,CAAC,CAAtB,EAAyB,CAAC,CAA1B,EAA6B,CAAC,CAA9B,EAAiC,CAAC,CAAlC,CAAnB;AACA6B,MAAAA,WAAW,GAAGA,WAAW,IAAI,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,EAAS,CAAC,CAAV,CAA7B;AACA,UAAMC,KAAK,GAAG,KAAKC,QAAL,CAAcF,WAAd,CAAd;AACA,UAAMG,aAAa,GAAG,IAAIF,KAAK,CAAC,CAAD,CAA/B;AACA,UAAMG,aAAa,GAAG,IAAIH,KAAK,CAAC,CAAD,CAA/B;AACA,UAAMI,aAAa,GAAG,IAAIJ,KAAK,CAAC,CAAD,CAA/B;AACA9B,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUgC,aAAtB;AACAhC,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUiC,aAAtB;AACAjC,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUkC,aAAtB;AACAlC,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUgC,aAAtB;AACAhC,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUiC,aAAtB;AACAjC,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUkC,aAAtB;AACAlC,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUgC,aAAtB;AACAhC,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,IAAUiC,aAAtB;AACAjC,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,EAAL,IAAWkC,aAAvB;AACA,aAAOlC,MAAP;AACD;;;WAID,qBAAkB;AAChBG,MAAAA,IAAI,CAACgC,SAAL,CAAe,IAAf,EAAqB,IAArB;AACA,aAAO,KAAKpD,KAAL,EAAP;AACD;;;WAED,kBAAe;AACboB,MAAAA,IAAI,CAACiC,MAAL,CAAY,IAAZ,EAAkB,IAAlB;AACA,aAAO,KAAKrD,KAAL,EAAP;AACD;;;WAID,sBAAasD,CAAb,EAA8C;AAC5ClC,MAAAA,IAAI,CAACmC,QAAL,CAAc,IAAd,EAAoBD,CAApB,EAAuB,IAAvB;AACA,aAAO,KAAKtD,KAAL,EAAP;AACD;;;WAED,uBAAcsD,CAAd,EAA+C;AAC7ClC,MAAAA,IAAI,CAACmC,QAAL,CAAc,IAAd,EAAoB,IAApB,EAA0BD,CAA1B;AACA,aAAO,KAAKtD,KAAL,EAAP;AACD;;;WAGD,iBAAQwD,OAAR,EAA+B;AAC7BpC,MAAAA,IAAI,CAACqC,OAAL,CAAa,IAAb,EAAmB,IAAnB,EAAyBD,OAAzB;AAEA,aAAO,KAAKxD,KAAL,EAAP;AACD;;;WAGD,iBAAQwD,OAAR,EAA+B;AAC7BpC,MAAAA,IAAI,CAACsC,OAAL,CAAa,IAAb,EAAmB,IAAnB,EAAyBF,OAAzB;AAEA,aAAO,KAAKxD,KAAL,EAAP;AACD;;;WAOD,iBAAQwD,OAAR,EAA+B;AAC7BpC,MAAAA,IAAI,CAACuC,OAAL,CAAa,IAAb,EAAmB,IAAnB,EAAyBH,OAAzB;AAEA,aAAO,KAAKxD,KAAL,EAAP;AACD;;;WAOD,mBAAU4D,QAAV,EAAkD;AAChD,aAAO,KAAKH,OAAL,CAAaG,QAAQ,CAAC,CAAD,CAArB,EAA0BF,OAA1B,CAAkCE,QAAQ,CAAC,CAAD,CAA1C,EAA+CD,OAA/C,CAAuDC,QAAQ,CAAC,CAAD,CAA/D,CAAP;AACD;;;WAQD,oBAAWJ,OAAX,EAA4BK,IAA5B,EAAgE;AAC9DzC,MAAAA,IAAI,CAAC0C,MAAL,CAAY,IAAZ,EAAkB,IAAlB,EAAwBN,OAAxB,EAAiCK,IAAjC;AACA,aAAO,KAAK7D,KAAL,EAAP;AACD;;;WAOD,eAAM+D,MAAN,EAAqD;AACnD3C,MAAAA,IAAI,CAAC2B,KAAL,CAAW,IAAX,EAAiB,IAAjB,EAAuBnD,KAAK,CAACC,OAAN,CAAckE,MAAd,IAAwBA,MAAxB,GAAiC,CAACA,MAAD,EAASA,MAAT,EAAiBA,MAAjB,CAAxD;AACA,aAAO,KAAK/D,KAAL,EAAP;AACD;;;WAOD,mBAAUgE,MAAV,EAAgD;AAC9C5C,MAAAA,IAAI,CAAC6C,SAAL,CAAe,IAAf,EAAqB,IAArB,EAA2BD,MAA3B;AACA,aAAO,KAAKhE,KAAL,EAAP;AACD;;;WAUD,mBAAUgE,MAAV,EAA0C/C,MAA1C,EAA+E;AAC7E,UAAI+C,MAAM,CAACrE,MAAP,KAAkB,CAAtB,EAAyB;AACvBsB,QAAAA,MAAM,GAAGiD,IAAI,CAACC,aAAL,CAAmBlD,MAAM,IAAI,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,EAAS,CAAC,CAAV,EAAa,CAAC,CAAd,CAA7B,EAA+C+C,MAA/C,EAAuD,IAAvD,CAAT;AACA,qCAAY/C,MAAZ,EAAoB,CAApB;AACA,eAAOA,MAAP;AACD;;AACD,aAAO,KAAKmD,gBAAL,CAAsBJ,MAAtB,EAA8B/C,MAA9B,CAAP;AACD;;;WAQD,0BAAiB+C,MAAjB,EAAiD/C,MAAjD,EAAsF;AACpF,UAAOtB,MAAP,GAAiBqE,MAAjB,CAAOrE,MAAP;AACA,UAAI0E,GAAJ;;AACA,cAAQ1E,MAAR;AACE,aAAK,CAAL;AACE0E,UAAAA,GAAG,GAAGC,IAAI,CAACH,aAAL,CAAmBlD,MAAM,IAAI,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,CAA7B,EAAuC+C,MAAvC,EAA+C,IAA/C,CAAN;AACA;;AACF,aAAK,CAAL;AACEK,UAAAA,GAAG,GAAGE,IAAI,CAACJ,aAAL,CAAmBlD,MAAM,IAAI,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,EAAS,CAAC,CAAV,CAA7B,EAA2C+C,MAA3C,EAAmD,IAAnD,CAAN;AACA;;AACF;AACE,gBAAM,IAAIQ,KAAJ,CAAU,gBAAV,CAAN;AARJ;;AAUA,mCAAYH,GAAZ,EAAiBL,MAAM,CAACrE,MAAxB;AACA,aAAO0E,GAAP;AACD;;;WAQD,2BAAkBL,MAAlB,EAAkD/C,MAAlD,EAAuF;AACrF,UAAIoD,GAAJ;;AACA,cAAQL,MAAM,CAACrE,MAAf;AACE,aAAK,CAAL;AACE0E,UAAAA,GAAG,GAAG,gDAA2BpD,MAAM,IAAI,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,CAArC,EAA+C+C,MAA/C,EAAuD,IAAvD,CAAN;AACA;;AACF,aAAK,CAAL;AACEK,UAAAA,GAAG,GAAG,gDAA2BpD,MAAM,IAAI,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,EAAS,CAAC,CAAV,CAArC,EAAmD+C,MAAnD,EAA2D,IAA3D,CAAN;AACA;;AACF;AACE,gBAAM,IAAIQ,KAAJ,CAAU,gBAAV,CAAN;AARJ;;AAUA,mCAAYH,GAAZ,EAAiBL,MAAM,CAACrE,MAAxB;AACA,aAAO0E,GAAP;AACD;;;WAGD,wBAAeL,MAAf,EAA+C/C,MAA/C,EAAoF;AAClF,aAAO,KAAKmD,gBAAL,CAAsBJ,MAAtB,EAA8B/C,MAA9B,CAAP;AACD;;;WAGD,yBAAgB+C,MAAhB,EAAgD/C,MAAhD,EAAqF;AACnF,aAAO,KAAKmD,gBAAL,CAAsBJ,MAAtB,EAA8B/C,MAA9B,CAAP;AACD;;;WAGD,4BAAmB+C,MAAnB,EAAmD/C,MAAnD,EAAwF;AACtF,aAAO,KAAKwD,iBAAL,CAAuBT,MAAvB,EAA+B/C,MAA/B,CAAP;AACD;;;WAID,uBAAcuC,OAAd,EAAqC;AACnC,aAAO,KAAKzD,QAAL,GAAgB0D,OAAhB,CAAwBD,OAAxB,CAAP;AACD;;;WAED,yBAAgBkB,CAAhB,EAA2BC,CAA3B,EAAsCC,CAAtC,EAAuD;AACrD,aAAO,KAAK7E,QAAL,GAAgBkE,SAAhB,CAA0B,CAACS,CAAD,EAAIC,CAAJ,EAAOC,CAAP,CAA1B,CAAP;AACD;;;SApjBD,eAAyC;AACvC,aAAOC,iBAAiB,EAAxB;AACD;;;SAED,eAAqC;AACnC,aAAOC,aAAa,EAApB;AACD;;;EAPkCC,e;;;AAyjBrC,IAAIC,IAAJ;AACA,IAAIC,QAAJ;;AAEA,SAASH,aAAT,GAA4C;AAC1C,MAAI,CAACE,IAAL,EAAW;AACTA,IAAAA,IAAI,GAAG,IAAIxF,OAAJ,CAAY,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B,EAAkC,CAAlC,EAAqC,CAArC,EAAwC,CAAxC,EAA2C,CAA3C,EAA8C,CAA9C,CAAZ,CAAP;AACAF,IAAAA,MAAM,CAACC,MAAP,CAAcyF,IAAd;AACD;;AACD,SAAOA,IAAP;AACD;;AAED,SAASH,iBAAT,GAAsC;AACpC,MAAI,CAACI,QAAL,EAAe;AACbA,IAAAA,QAAQ,GAAG,IAAIzF,OAAJ,EAAX;AACAF,IAAAA,MAAM,CAACC,MAAP,CAAc0F,QAAd;AACD;;AACD,SAAOA,QAAP;AACD;;AAID,SAASzC,YAAT,CAAsB0C,eAAtB,EAA+C;AAC7C,MAAIA,eAAe,GAAGlG,IAAI,CAACC,EAAL,GAAU,CAAhC,EAAmC;AACjC,UAAMuF,KAAK,CAAC,kBAAD,CAAX;AACD;AACF;;AAGD,SAAS1C,mCAAT,CACEb,MADF,EAEEM,IAFF,EAGEC,KAHF,EAIEC,MAJF,EAKEC,GALF,EAMEC,IANF,EAOgB;AACd,MAAMwD,WAAW,GAAI,IAAIxD,IAAL,IAAcH,KAAK,GAAGD,IAAtB,CAApB;AACA,MAAM6D,WAAW,GAAI,IAAIzD,IAAL,IAAcD,GAAG,GAAGD,MAApB,CAApB;AACA,MAAM4D,WAAW,GAAG,CAAC7D,KAAK,GAAGD,IAAT,KAAkBC,KAAK,GAAGD,IAA1B,CAApB;AACA,MAAM+D,WAAW,GAAG,CAAC5D,GAAG,GAAGD,MAAP,KAAkBC,GAAG,GAAGD,MAAxB,CAApB;AACA,MAAM8D,WAAW,GAAG,CAAC,CAArB;AACA,MAAMC,WAAW,GAAG,CAAC,CAArB;AACA,MAAMC,WAAW,GAAG,CAAC,CAAD,GAAK9D,IAAzB;AACAV,EAAAA,MAAM,CAAC,CAAD,CAAN,GAAYkE,WAAZ;AACAlE,EAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,CAAZ;AACAA,EAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,CAAZ;AACAA,EAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,CAAZ;AACAA,EAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,CAAZ;AACAA,EAAAA,MAAM,CAAC,CAAD,CAAN,GAAYmE,WAAZ;AACAnE,EAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,CAAZ;AACAA,EAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,CAAZ;AACAA,EAAAA,MAAM,CAAC,CAAD,CAAN,GAAYoE,WAAZ;AACApE,EAAAA,MAAM,CAAC,CAAD,CAAN,GAAYqE,WAAZ;AACArE,EAAAA,MAAM,CAAC,EAAD,CAAN,GAAasE,WAAb;AACAtE,EAAAA,MAAM,CAAC,EAAD,CAAN,GAAauE,WAAb;AACAvE,EAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,CAAb;AACAA,EAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,CAAb;AACAA,EAAAA,MAAM,CAAC,EAAD,CAAN,GAAawE,WAAb;AACAxE,EAAAA,MAAM,CAAC,EAAD,CAAN,GAAa,CAAb;AACA,SAAOA,MAAP;AACD", "sourcesContent": ["// Copyright (c) 2017 Uber Technologies, Inc.\n// MIT License\nimport Matrix from './base/matrix';\nimport {NumericArray} from '@math.gl/types';\nimport {checkVector} from '../lib/validators';\n\n/* eslint-disable camelcase */\nimport {vec2_transformMat4AsVector, vec3_transformMat4AsVector} from '../lib/gl-matrix-extras';\nimport * as mat4 from 'gl-matrix/mat4';\nimport * as vec2 from 'gl-matrix/vec2';\nimport * as vec3 from 'gl-matrix/vec3';\nimport * as vec4 from 'gl-matrix/vec4';\n\nenum INDICES {\n  COL0ROW0 = 0,\n  COL0ROW1 = 1,\n  COL0ROW2 = 2,\n  COL0ROW3 = 3,\n  COL1ROW0 = 4,\n  COL1ROW1 = 5,\n  COL1ROW2 = 6,\n  COL1ROW3 = 7,\n  COL2ROW0 = 8,\n  COL2ROW1 = 9,\n  COL2ROW2 = 10,\n  COL2ROW3 = 11,\n  COL3ROW0 = 12,\n  COL3ROW1 = 13,\n  COL3ROW2 = 14,\n  COL3ROW3 = 15\n}\n\nconst DEFAULT_FOVY = (45 * Math.PI) / 180;\nconst DEFAULT_ASPECT = 1;\nconst DEFAULT_NEAR = 0.1;\nconst DEFAULT_FAR = 500;\n\nconst IDENTITY_MATRIX = Object.freeze([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]);\n\n/** 4x4 matrix */\nexport default class Matrix4 extends Matrix {\n  static get IDENTITY(): Readonly<Matrix4> {\n    return getIdentityMatrix();\n  }\n\n  static get ZERO(): Readonly<Matrix4> {\n    return getZeroMatrix();\n  }\n\n  get ELEMENTS(): number {\n    return 16;\n  }\n\n  get RANK(): number {\n    return 4;\n  }\n\n  get INDICES(): typeof INDICES {\n    return INDICES;\n  }\n\n  constructor(array?: Readonly<NumericArray>) {\n    // PERF NOTE: initialize elements as double precision numbers\n    super(-0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0);\n    if (arguments.length === 1 && Array.isArray(array)) {\n      this.copy(array);\n    } else {\n      this.identity();\n    }\n  }\n\n  copy(array: Readonly<NumericArray>): this {\n    this[0] = array[0];\n    this[1] = array[1];\n    this[2] = array[2];\n    this[3] = array[3];\n    this[4] = array[4];\n    this[5] = array[5];\n    this[6] = array[6];\n    this[7] = array[7];\n    this[8] = array[8];\n    this[9] = array[9];\n    this[10] = array[10];\n    this[11] = array[11];\n    this[12] = array[12];\n    this[13] = array[13];\n    this[14] = array[14];\n    this[15] = array[15];\n    return this.check();\n  }\n\n  // eslint-disable-next-line max-params\n  set(\n    m00: number,\n    m10: number,\n    m20: number,\n    m30: number,\n    m01: number,\n    m11: number,\n    m21: number,\n    m31: number,\n    m02: number,\n    m12: number,\n    m22: number,\n    m32: number,\n    m03: number,\n    m13: number,\n    m23: number,\n    m33: number\n  ): this {\n    this[0] = m00;\n    this[1] = m10;\n    this[2] = m20;\n    this[3] = m30;\n    this[4] = m01;\n    this[5] = m11;\n    this[6] = m21;\n    this[7] = m31;\n    this[8] = m02;\n    this[9] = m12;\n    this[10] = m22;\n    this[11] = m32;\n    this[12] = m03;\n    this[13] = m13;\n    this[14] = m23;\n    this[15] = m33;\n    return this.check();\n  }\n\n  // accepts row major order, stores as column major\n  // eslint-disable-next-line max-params\n  setRowMajor(\n    m00: number,\n    m01: number,\n    m02: number,\n    m03: number,\n    m10: number,\n    m11: number,\n    m12: number,\n    m13: number,\n    m20: number,\n    m21: number,\n    m22: number,\n    m23: number,\n    m30: number,\n    m31: number,\n    m32: number,\n    m33: number\n  ): this {\n    this[0] = m00;\n    this[1] = m10;\n    this[2] = m20;\n    this[3] = m30;\n    this[4] = m01;\n    this[5] = m11;\n    this[6] = m21;\n    this[7] = m31;\n    this[8] = m02;\n    this[9] = m12;\n    this[10] = m22;\n    this[11] = m32;\n    this[12] = m03;\n    this[13] = m13;\n    this[14] = m23;\n    this[15] = m33;\n    return this.check();\n  }\n\n  toRowMajor(result: NumericArray): NumericArray {\n    result[0] = this[0];\n    result[1] = this[4];\n    result[2] = this[8];\n    result[3] = this[12];\n    result[4] = this[1];\n    result[5] = this[5];\n    result[6] = this[9];\n    result[7] = this[13];\n    result[8] = this[2];\n    result[9] = this[6];\n    result[10] = this[10];\n    result[11] = this[14];\n    result[12] = this[3];\n    result[13] = this[7];\n    result[14] = this[11];\n    result[15] = this[15];\n    return result;\n  }\n\n  // Constructors\n\n  /** Set to identity matrix */\n  identity(): this {\n    return this.copy(IDENTITY_MATRIX);\n  }\n\n  /**\n   *\n   * @param object\n   * @returns self\n   */\n  fromObject(object: {[key: string]: any}): this {\n    return this.check();\n  }\n\n  /**\n   * Calculates a 4x4 matrix from the given quaternion\n   * @param quaternion Quaternion to create matrix from\n   * @returns self\n   */\n  fromQuaternion(quaternion: Readonly<NumericArray>): this {\n    mat4.fromQuat(this, quaternion);\n    return this.check();\n  }\n\n  /**\n   * Generates a frustum matrix with the given bounds\n   * @param view.left - Left bound of the frustum\n   * @param view.right - Right bound of the frustum\n   * @param view.bottom - Bottom bound of the frustum\n   * @param view.top - Top bound of the frustum\n   * @param view.near - Near bound of the frustum\n   * @param view.far - Far bound of the frustum. Can be set to Infinity.\n   * @returns self\n   */\n  frustum(view: {\n    left: number;\n    right: number;\n    bottom: number;\n    top: number;\n    near: number;\n    far?: number;\n  }): this {\n    const {left, right, bottom, top, near = DEFAULT_NEAR, far = DEFAULT_FAR} = view;\n    if (far === Infinity) {\n      computeInfinitePerspectiveOffCenter(this, left, right, bottom, top, near);\n    } else {\n      mat4.frustum(this, left, right, bottom, top, near, far);\n    }\n    return this.check();\n  }\n\n  /**\n   * Generates a look-at matrix with the given eye position, focal point,\n   * and up axis\n   * @param view.eye - (vector) Position of the viewer\n   * @param view.center - (vector) Point the viewer is looking at\n   * @param view.up - (vector) Up axis\n   * @returns self\n   */\n  lookAt(view: {\n    eye: Readonly<NumericArray>;\n    center?: Readonly<NumericArray>;\n    up?: Readonly<NumericArray>;\n  }): this {\n    const {eye, center = [0, 0, 0], up = [0, 1, 0]} = view;\n    mat4.lookAt(this, eye, center, up);\n    return this.check();\n  }\n\n  /**\n   * Generates a orthogonal projection matrix with the given bounds\n   * from \"traditional\" view space parameters\n   * @param view.left - Left bound of the frustum\n   * @param view.right number  Right bound of the frustum\n   * @param view.bottom - Bottom bound of the frustum\n   * @param view.top number  Top bound of the frustum\n   * @param view.near - Near bound of the frustum\n   * @param view.far number  Far bound of the frustum\n   * @returns self\n   */\n  ortho(view: {\n    left: number;\n    right: number;\n    bottom: number;\n    top: number;\n    near?: number;\n    far?: number;\n  }): this {\n    const {left, right, bottom, top, near = DEFAULT_NEAR, far = DEFAULT_FAR} = view;\n    mat4.ortho(this, left, right, bottom, top, near, far);\n    return this.check();\n  }\n\n  /**\n   * Generates an orthogonal projection matrix with the same parameters\n   * as a perspective matrix (plus focalDistance)\n   * @param view.fovy Vertical field of view in radians\n   * @param view.aspect Aspect ratio. Typically viewport width / viewport height\n   * @param view.focalDistance Distance in the view frustum used for extent calculations\n   * @param view.near Near bound of the frustum\n   * @param view.far Far bound of the frustum\n   * @returns self\n   */\n  orthographic(view: {\n    fovy?: number;\n    aspect?: number;\n    focalDistance?: number;\n    near?: number;\n    far?: number;\n  }): this {\n    const {\n      fovy = DEFAULT_FOVY,\n      aspect = DEFAULT_ASPECT,\n      focalDistance = 1,\n      near = DEFAULT_NEAR,\n      far = DEFAULT_FAR\n    } = view;\n\n    checkRadians(fovy);\n\n    const halfY = fovy / 2;\n    const top = focalDistance * Math.tan(halfY); // focus_plane is the distance from the camera\n    const right = top * aspect;\n\n    return this.ortho({\n      left: -right,\n      right,\n      bottom: -top,\n      top,\n      near,\n      far\n    });\n  }\n\n  /**\n   * Generates a perspective projection matrix with the given bounds\n   * @param view.fovy Vertical field of view in radians\n   * @param view.aspect Aspect ratio. typically viewport width/height\n   * @param view.near Near bound of the frustum\n   * @param view.far Far bound of the frustum\n   * @returns self\n   */\n  perspective(view: {fovy: number; aspect?: number; near?: number; far?: number}): this {\n    const {fovy = (45 * Math.PI) / 180, aspect = 1, near = 0.1, far = 500} = view;\n    checkRadians(fovy);\n    mat4.perspective(this, fovy, aspect, near, far);\n    return this.check();\n  }\n\n  // Accessors\n\n  determinant(): number {\n    return mat4.determinant(this);\n  }\n\n  /**\n   * Extracts the non-uniform scale assuming the matrix is an affine transformation.\n   * The scales are the \"lengths\" of the column vectors in the upper-left 3x3 matrix.\n   * @param result\n   * @returns self\n   */\n  getScale(result: NumericArray = [-0, -0, -0]): NumericArray {\n    // explicit is faster than hypot...\n    result[0] = Math.sqrt(this[0] * this[0] + this[1] * this[1] + this[2] * this[2]);\n    result[1] = Math.sqrt(this[4] * this[4] + this[5] * this[5] + this[6] * this[6]);\n    result[2] = Math.sqrt(this[8] * this[8] + this[9] * this[9] + this[10] * this[10]);\n    // result[0] = Math.hypot(this[0], this[1], this[2]);\n    // result[1] = Math.hypot(this[4], this[5], this[6]);\n    // result[2] = Math.hypot(this[8], this[9], this[10]);\n    return result;\n  }\n\n  /**\n   * Gets the translation portion, assuming the matrix is a affine transformation matrix.\n   * @param result\n   * @returns self\n   */\n  getTranslation(result: NumericArray = [-0, -0, -0]): NumericArray {\n    result[0] = this[12];\n    result[1] = this[13];\n    result[2] = this[14];\n    return result;\n  }\n\n  /**\n   * Gets upper left 3x3 pure rotation matrix (non-scaling), assume affine transformation matrix\n   * @param result\n   * @param scaleResult\n   * @returns self\n   */\n  getRotation(result?: NumericArray, scaleResult?: NumericArray): NumericArray {\n    result = result || [-0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0];\n    scaleResult = scaleResult || [-0, -0, -0];\n    const scale = this.getScale(scaleResult);\n    const inverseScale0 = 1 / scale[0];\n    const inverseScale1 = 1 / scale[1];\n    const inverseScale2 = 1 / scale[2];\n    result[0] = this[0] * inverseScale0;\n    result[1] = this[1] * inverseScale1;\n    result[2] = this[2] * inverseScale2;\n    result[3] = 0;\n    result[4] = this[4] * inverseScale0;\n    result[5] = this[5] * inverseScale1;\n    result[6] = this[6] * inverseScale2;\n    result[7] = 0;\n    result[8] = this[8] * inverseScale0;\n    result[9] = this[9] * inverseScale1;\n    result[10] = this[10] * inverseScale2;\n    result[11] = 0;\n    result[12] = 0;\n    result[13] = 0;\n    result[14] = 0;\n    result[15] = 1;\n    return result;\n  }\n\n  /**\n   *\n   * @param result\n   * @param scaleResult\n   * @returns self\n   */\n  getRotationMatrix3(result?: NumericArray, scaleResult?: NumericArray): NumericArray {\n    result = result || [-0, -0, -0, -0, -0, -0, -0, -0, -0];\n    scaleResult = scaleResult || [-0, -0, -0];\n    const scale = this.getScale(scaleResult);\n    const inverseScale0 = 1 / scale[0];\n    const inverseScale1 = 1 / scale[1];\n    const inverseScale2 = 1 / scale[2];\n    result[0] = this[0] * inverseScale0;\n    result[1] = this[1] * inverseScale1;\n    result[2] = this[2] * inverseScale2;\n    result[3] = this[4] * inverseScale0;\n    result[4] = this[5] * inverseScale1;\n    result[5] = this[6] * inverseScale2;\n    result[6] = this[8] * inverseScale0;\n    result[7] = this[9] * inverseScale1;\n    result[8] = this[10] * inverseScale2;\n    return result;\n  }\n\n  // Modifiers\n\n  transpose(): this {\n    mat4.transpose(this, this);\n    return this.check();\n  }\n\n  invert(): this {\n    mat4.invert(this, this);\n    return this.check();\n  }\n\n  // Operations\n\n  multiplyLeft(a: Readonly<NumericArray>): this {\n    mat4.multiply(this, a, this);\n    return this.check();\n  }\n\n  multiplyRight(a: Readonly<NumericArray>): this {\n    mat4.multiply(this, this, a);\n    return this.check();\n  }\n\n  // Rotates a matrix by the given angle around the X axis\n  rotateX(radians: number): this {\n    mat4.rotateX(this, this, radians);\n    // mat4.rotate(this, this, radians, [1, 0, 0]);\n    return this.check();\n  }\n\n  // Rotates a matrix by the given angle around the Y axis.\n  rotateY(radians: number): this {\n    mat4.rotateY(this, this, radians);\n    // mat4.rotate(this, this, radians, [0, 1, 0]);\n    return this.check();\n  }\n\n  /**\n   * Rotates a matrix by the given angle around the Z axis.\n   * @param radians\n   * @returns self\n   */\n  rotateZ(radians: number): this {\n    mat4.rotateZ(this, this, radians);\n    // mat4.rotate(this, this, radians, [0, 0, 1]);\n    return this.check();\n  }\n\n  /**\n   *\n   * @param param0\n   * @returns self\n   */\n  rotateXYZ(angleXYZ: Readonly<NumericArray>): this {\n    return this.rotateX(angleXYZ[0]).rotateY(angleXYZ[1]).rotateZ(angleXYZ[2]);\n  }\n\n  /**\n   *\n   * @param radians\n   * @param axis\n   * @returns self\n   */\n  rotateAxis(radians: number, axis: Readonly<NumericArray>): this {\n    mat4.rotate(this, this, radians, axis);\n    return this.check();\n  }\n\n  /**\n   *\n   * @param factor\n   * @returns self\n   */\n  scale(factor: number | Readonly<NumericArray>): this {\n    mat4.scale(this, this, Array.isArray(factor) ? factor : [factor, factor, factor]);\n    return this.check();\n  }\n\n  /**\n   *\n   * @param vec\n   * @returns self\n   */\n  translate(vector: Readonly<NumericArray>): this {\n    mat4.translate(this, this, vector);\n    return this.check();\n  }\n\n  // Transforms\n\n  /**\n   * Transforms any 2, 3 or 4 element vector. 2 and 3 elements are treated as points\n   * @param vector\n   * @param result\n   * @returns self\n   */\n  transform(vector: Readonly<NumericArray>, result?: NumericArray): NumericArray {\n    if (vector.length === 4) {\n      result = vec4.transformMat4(result || [-0, -0, -0, -0], vector, this);\n      checkVector(result, 4);\n      return result;\n    }\n    return this.transformAsPoint(vector, result);\n  }\n\n  /**\n   * Transforms any 2 or 3 element array as point (w implicitly 1)\n   * @param vector\n   * @param result\n   * @returns self\n   */\n  transformAsPoint(vector: Readonly<NumericArray>, result?: NumericArray): NumericArray {\n    const {length} = vector;\n    let out: NumericArray;\n    switch (length) {\n      case 2:\n        out = vec2.transformMat4(result || [-0, -0], vector, this);\n        break;\n      case 3:\n        out = vec3.transformMat4(result || [-0, -0, -0], vector, this);\n        break;\n      default:\n        throw new Error('Illegal vector');\n    }\n    checkVector(out, vector.length);\n    return out;\n  }\n\n  /**\n   * Transforms any 2 or 3 element array as vector (w implicitly 0)\n   * @param vector\n   * @param result\n   * @returns self\n   */\n  transformAsVector(vector: Readonly<NumericArray>, result?: NumericArray): NumericArray {\n    let out: NumericArray;\n    switch (vector.length) {\n      case 2:\n        out = vec2_transformMat4AsVector(result || [-0, -0], vector, this);\n        break;\n      case 3:\n        out = vec3_transformMat4AsVector(result || [-0, -0, -0], vector, this);\n        break;\n      default:\n        throw new Error('Illegal vector');\n    }\n    checkVector(out, vector.length);\n    return out;\n  }\n\n  /** @deprecated */\n  transformPoint(vector: Readonly<NumericArray>, result?: NumericArray): NumericArray {\n    return this.transformAsPoint(vector, result);\n  }\n\n  /** @deprecated */\n  transformVector(vector: Readonly<NumericArray>, result?: NumericArray): NumericArray {\n    return this.transformAsPoint(vector, result);\n  }\n\n  /** @deprecated */\n  transformDirection(vector: Readonly<NumericArray>, result?: NumericArray): NumericArray {\n    return this.transformAsVector(vector, result);\n  }\n\n  // three.js math API compatibility\n\n  makeRotationX(radians: number): this {\n    return this.identity().rotateX(radians);\n  }\n\n  makeTranslation(x: number, y: number, z: number): this {\n    return this.identity().translate([x, y, z]);\n  }\n}\n\n// TODO initializing static members directly is an option, but make sure no tree-shaking issues\nlet ZERO: Matrix4;\nlet IDENTITY: Matrix4;\n\nfunction getZeroMatrix(): Readonly<Matrix4> {\n  if (!ZERO) {\n    ZERO = new Matrix4([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]);\n    Object.freeze(ZERO);\n  }\n  return ZERO;\n}\n\nfunction getIdentityMatrix(): Matrix4 {\n  if (!IDENTITY) {\n    IDENTITY = new Matrix4();\n    Object.freeze(IDENTITY);\n  }\n  return IDENTITY;\n}\n\n// HELPER FUNCTIONS\n\nfunction checkRadians(possiblyDegrees: number) {\n  if (possiblyDegrees > Math.PI * 2) {\n    throw Error('expected radians');\n  }\n}\n\n// eslint-disable-next-line max-params\nfunction computeInfinitePerspectiveOffCenter(\n  result: NumericArray,\n  left: number,\n  right: number,\n  bottom: number,\n  top: number,\n  near: number\n): NumericArray {\n  const column0Row0 = (2 * near) / (right - left);\n  const column1Row1 = (2 * near) / (top - bottom);\n  const column2Row0 = (right + left) / (right - left);\n  const column2Row1 = (top + bottom) / (top - bottom);\n  const column2Row2 = -1;\n  const column2Row3 = -1;\n  const column3Row2 = -2 * near;\n  result[0] = column0Row0;\n  result[1] = 0;\n  result[2] = 0;\n  result[3] = 0;\n  result[4] = 0;\n  result[5] = column1Row1;\n  result[6] = 0;\n  result[7] = 0;\n  result[8] = column2Row0;\n  result[9] = column2Row1;\n  result[10] = column2Row2;\n  result[11] = column2Row3;\n  result[12] = 0;\n  result[13] = 0;\n  result[14] = column3Row2;\n  result[15] = 0;\n  return result;\n}\n"], "file": "matrix4.js"}