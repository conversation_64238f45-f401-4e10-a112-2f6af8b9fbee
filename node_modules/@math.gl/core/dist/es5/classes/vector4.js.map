{"version": 3, "sources": ["../../../src/classes/vector4.ts"], "names": ["ZERO", "Vector4", "x", "y", "z", "w", "arguments", "length", "copy", "config", "debug", "check", "array", "object", "value", "matrix4", "vec4", "transformMat4", "matrix3", "matrix2", "quaternion", "transformQuat", "m", "transform", "Object", "freeze", "Vector"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAGA;;AACA;;AACA;;AACA;;AAEA;;;;;;;;;;AAKA,IAAIA,IAAJ;;IAMqBC,O;;;;;AASnB,qBAAiG;AAAA;;AAAA,QAArFC,CAAqF,uEAAhD,CAAgD;AAAA,QAA7CC,CAA6C,uEAAjC,CAAiC;AAAA,QAA9BC,CAA8B,uEAAlB,CAAkB;AAAA,QAAfC,CAAe,uEAAH,CAAG;AAAA;AAE/F,8BAAM,CAAC,CAAP,EAAU,CAAC,CAAX,EAAc,CAAC,CAAf,EAAkB,CAAC,CAAnB;;AACA,QAAI,qBAAQH,CAAR,KAAcI,SAAS,CAACC,MAAV,KAAqB,CAAvC,EAA0C;AACxC,YAAKC,IAAL,CAAUN,CAAV;AACD,KAFD,MAEO;AAEL,UAAIO,eAAOC,KAAX,EAAkB;AAChB,qCAAYR,CAAZ;AACA,qCAAYC,CAAZ;AACA,qCAAYC,CAAZ;AACA,qCAAYC,CAAZ;AACD;;AACD,YAAK,CAAL,IAAUH,CAAV;AACA,YAAK,CAAL,IAAUC,CAAV;AACA,YAAK,CAAL,IAAUC,CAAV;AACA,YAAK,CAAL,IAAUC,CAAV;AACD;;AAjB8F;AAkBhG;;;;WAED,aAAIH,CAAJ,EAAeC,CAAf,EAA0BC,CAA1B,EAAqCC,CAArC,EAAsD;AACpD,WAAK,CAAL,IAAUH,CAAV;AACA,WAAK,CAAL,IAAUC,CAAV;AACA,WAAK,CAAL,IAAUC,CAAV;AACA,WAAK,CAAL,IAAUC,CAAV;AACA,aAAO,KAAKM,KAAL,EAAP;AACD;;;WAED,cAAKC,KAAL,EAA0C;AACxC,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,aAAO,KAAKD,KAAL,EAAP;AACD;;;WAED,oBAAWE,MAAX,EAAuE;AACrE,UAAIJ,eAAOC,KAAX,EAAkB;AAChB,qCAAYG,MAAM,CAACX,CAAnB;AACA,qCAAYW,MAAM,CAACV,CAAnB;AACA,qCAAYU,MAAM,CAACT,CAAnB;AACA,qCAAYS,MAAM,CAACR,CAAnB;AACD;;AACD,WAAK,CAAL,IAAUQ,MAAM,CAACX,CAAjB;AACA,WAAK,CAAL,IAAUW,MAAM,CAACV,CAAjB;AACA,WAAK,CAAL,IAAUU,MAAM,CAACT,CAAjB;AACA,WAAK,CAAL,IAAUS,MAAM,CAACR,CAAjB;AACA,aAAO,IAAP;AACD;;;WAED,kBAASQ,MAAT,EAKE;AACAA,MAAAA,MAAM,CAACX,CAAP,GAAW,KAAK,CAAL,CAAX;AACAW,MAAAA,MAAM,CAACV,CAAP,GAAW,KAAK,CAAL,CAAX;AACAU,MAAAA,MAAM,CAACT,CAAP,GAAW,KAAK,CAAL,CAAX;AACAS,MAAAA,MAAM,CAACR,CAAP,GAAW,KAAK,CAAL,CAAX;AACA,aAAOQ,MAAP;AAMD;;;SAID,eAAuB;AACrB,aAAO,CAAP;AACD;;;SAED,eAAgB;AACd,aAAO,KAAK,CAAL,CAAP;AACD,K;SACD,aAAMC,KAAN,EAAqB;AACnB,WAAK,CAAL,IAAU,6BAAYA,KAAZ,CAAV;AACD;;;SACD,eAAgB;AACd,aAAO,KAAK,CAAL,CAAP;AACD,K;SACD,aAAMA,KAAN,EAAqB;AACnB,WAAK,CAAL,IAAU,6BAAYA,KAAZ,CAAV;AACD;;;WAED,mBAAUC,OAAV,EAAiD;AAC/CC,MAAAA,IAAI,CAACC,aAAL,CAAmB,IAAnB,EAAyB,IAAzB,EAA+BF,OAA/B;AACA,aAAO,KAAKJ,KAAL,EAAP;AACD;;;WAED,4BAAmBO,OAAnB,EAA0D;AACxD,8CAAmB,IAAnB,EAAyB,IAAzB,EAA+BA,OAA/B;AACA,aAAO,KAAKP,KAAL,EAAP;AACD;;;WAED,4BAAmBQ,OAAnB,EAA0D;AACxD,8CAAmB,IAAnB,EAAyB,IAAzB,EAA+BA,OAA/B;AACA,aAAO,KAAKR,KAAL,EAAP;AACD;;;WAED,+BAAsBS,UAAtB,EAAgE;AAC9DJ,MAAAA,IAAI,CAACK,aAAL,CAAmB,IAAnB,EAAyB,IAAzB,EAA+BD,UAA/B;AACA,aAAO,KAAKT,KAAL,EAAP;AACD;;;WAGD,sBAAaW,CAAb,EAA+B;AAC7BA,MAAAA,CAAC,CAACC,SAAF,CAAY,IAAZ,EAAkB,IAAlB;AACA,aAAO,IAAP;AACD;;;SAvHD,eAA2B;AACzB,UAAI,CAACvB,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAIC,OAAJ,CAAY,CAAZ,EAAe,CAAf,EAAkB,CAAlB,EAAqB,CAArB,CAAP;AACAuB,QAAAA,MAAM,CAACC,MAAP,CAAczB,IAAd;AACD;;AACD,aAAOA,IAAP;AACD;;;EAPkC0B,e", "sourcesContent": ["// Copyright (c) 2017 Uber Technologies, Inc.\n// MIT License\n\nimport Vector from './base/vector';\nimport {config, isArray} from '../lib/common';\nimport {checkNumber} from '../lib/validators';\nimport * as vec4 from 'gl-matrix/vec3';\n/* eslint-disable camelcase */\nimport {vec4_transformMat2, vec4_transformMat3} from '../lib/gl-matrix-extras';\nimport {NumericArray} from '@math.gl/types';\n\nimport type Matrix4 from './matrix4';\n\nlet ZERO: Vector4;\n\n/**\n * Four-element vector class.\n * Subclass of Array<number>\n */\nexport default class Vector4 extends Vector {\n  static get ZERO(): Vector4 {\n    if (!ZERO) {\n      ZERO = new Vector4(0, 0, 0, 0);\n      Object.freeze(ZERO);\n    }\n    return ZERO;\n  }\n\n  constructor(x: number | Readonly<NumericArray> = 0, y: number = 0, z: number = 0, w: number = 0) {\n    // PERF NOTE: initialize elements as double precision numbers\n    super(-0, -0, -0, -0);\n    if (isArray(x) && arguments.length === 1) {\n      this.copy(x as Readonly<NumericArray>);\n    } else {\n      // this.set(x, y, z, w);\n      if (config.debug) {\n        checkNumber(x);\n        checkNumber(y);\n        checkNumber(z);\n        checkNumber(w);\n      }\n      this[0] = x as number;\n      this[1] = y;\n      this[2] = z;\n      this[3] = w;\n    }\n  }\n\n  set(x: number, y: number, z: number, w: number): this {\n    this[0] = x;\n    this[1] = y;\n    this[2] = z;\n    this[3] = w;\n    return this.check();\n  }\n\n  copy(array: Readonly<NumericArray>): this {\n    this[0] = array[0];\n    this[1] = array[1];\n    this[2] = array[2];\n    this[3] = array[3];\n    return this.check();\n  }\n\n  fromObject(object: {x: number; y: number; z: number; w: number}): this {\n    if (config.debug) {\n      checkNumber(object.x);\n      checkNumber(object.y);\n      checkNumber(object.z);\n      checkNumber(object.w);\n    }\n    this[0] = object.x;\n    this[1] = object.y;\n    this[2] = object.z;\n    this[3] = object.w;\n    return this;\n  }\n\n  toObject(object: {x?: number; y?: number; z?: number; w?: number}): {\n    x: number;\n    y: number;\n    z: number;\n    w: number;\n  } {\n    object.x = this[0];\n    object.y = this[1];\n    object.z = this[2];\n    object.w = this[3];\n    return object as {\n      x: number;\n      y: number;\n      z: number;\n      w: number;\n    };\n  }\n\n  // Getters/setters\n  /* eslint-disable no-multi-spaces, brace-style, no-return-assign */\n  get ELEMENTS(): number {\n    return 4;\n  }\n\n  get z(): number {\n    return this[2];\n  }\n  set z(value: number) {\n    this[2] = checkNumber(value);\n  }\n  get w(): number {\n    return this[3];\n  }\n  set w(value: number) {\n    this[3] = checkNumber(value);\n  }\n\n  transform(matrix4: Readonly<NumericArray>): this {\n    vec4.transformMat4(this, this, matrix4);\n    return this.check();\n  }\n\n  transformByMatrix3(matrix3: Readonly<NumericArray>): this {\n    vec4_transformMat3(this, this, matrix3);\n    return this.check();\n  }\n\n  transformByMatrix2(matrix2: Readonly<NumericArray>): this {\n    vec4_transformMat2(this, this, matrix2);\n    return this.check();\n  }\n\n  transformByQuaternion(quaternion: Readonly<NumericArray>): this {\n    vec4.transformQuat(this, this, quaternion);\n    return this.check();\n  }\n\n  // three.js compatibility\n  applyMatrix4(m: Matrix4): this {\n    m.transform(this, this);\n    return this;\n  }\n}\n"], "file": "vector4.js"}