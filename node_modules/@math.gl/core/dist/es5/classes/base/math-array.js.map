{"version": 3, "sources": ["../../../../src/classes/base/math-array.ts"], "names": ["MathArray", "constructor", "copy", "array", "offset", "i", "ELEMENTS", "check", "targetArray", "arrayOrObject", "Array", "isArray", "fromObject", "toArray", "toObject", "target", "to", "Float32Array", "formatString", "config", "opts", "string", "printTypes", "name", "length", "a", "b", "t", "undefined", "lerp", "ai", "vector", "Math", "min", "max", "minVector", "maxVector", "vectors", "scale", "scalar", "debug", "validate", "Error", "valid", "Number", "isFinite", "subtract", "addScalar", "multiplyByScalar"], "mappings": ";;;;;;;;;;;;;;;;;;;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAG8BA,S;;;;;;;;;;;;WAc5B,iBAAc;AAEZ,aAAO,IAAI,KAAKC,WAAT,GAAuBC,IAAvB,CAA4B,IAA5B,CAAP;AACD;;;WAED,mBAAUC,KAAV,EAAmE;AAAA,UAA1BC,MAA0B,uEAAT,CAAS;;AACjE,WAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,aAAKA,CAAL,IAAUF,KAAK,CAACE,CAAC,GAAGD,MAAL,CAAf;AACD;;AACD,aAAO,KAAKG,KAAL,EAAP;AACD;;;WAKD,mBAA0E;AAAA,UAAlEC,WAAkE,uEAAtC,EAAsC;AAAA,UAAlCJ,MAAkC,uEAAjB,CAAiB;;AACxE,WAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtCG,QAAAA,WAAW,CAACJ,MAAM,GAAGC,CAAV,CAAX,GAA0B,KAAKA,CAAL,CAA1B;AACD;;AACD,aAAOG,WAAP;AACD;;;WAED,cAAKC,aAAL,EAA2D;AACzD,aAAOC,KAAK,CAACC,OAAN,CAAcF,aAAd,IAA+B,KAAKP,IAAL,CAAUO,aAAV,CAA/B,GAA0D,KAAKG,UAAL,CAAgBH,aAAhB,CAAjE;AACD;;;WAED,YAAoCA,aAApC,EAAyD;AAEvD,UAAIA,aAAa,KAAK,IAAtB,EAA4B;AAC1B,eAAO,IAAP;AACD;;AAED,aAAO,qBAAQA,aAAR,IAAyB,KAAKI,OAAL,CAAaJ,aAAb,CAAzB,GAAuD,KAAKK,QAAL,CAAcL,aAAd,CAA9D;AACD;;;WAED,kBAASM,MAAT,EAA6B;AAC3B,aAAOA,MAAM,GAAG,KAAKC,EAAL,CAAQD,MAAR,CAAH,GAAqB,IAAlC;AACD;;;WAGD,0BAA+B;AAC7B,aAAO,IAAIE,YAAJ,CAAiB,IAAjB,CAAP;AACD;;;WAED,oBAAmB;AACjB,aAAO,KAAKC,YAAL,CAAkBC,cAAlB,CAAP;AACD;;;WAGD,sBAAaC,IAAb,EAAiD;AAC/C,UAAIC,MAAM,GAAG,EAAb;;AACA,WAAK,IAAIhB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtCgB,QAAAA,MAAM,IAAI,CAAChB,CAAC,GAAG,CAAJ,GAAQ,IAAR,GAAe,EAAhB,IAAsB,yBAAY,KAAKA,CAAL,CAAZ,EAAqBe,IAArB,CAAhC;AACD;;AACD,uBAAUA,IAAI,CAACE,UAAL,GAAkB,KAAKrB,WAAL,CAAiBsB,IAAnC,GAA0C,EAApD,cAA0DF,MAA1D;AACD;;;WAED,gBAAOlB,KAAP,EAA+C;AAC7C,UAAI,CAACA,KAAD,IAAU,KAAKqB,MAAL,KAAgBrB,KAAK,CAACqB,MAApC,EAA4C;AAC1C,eAAO,KAAP;AACD;;AACD,WAAK,IAAInB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,YAAI,CAAC,oBAAO,KAAKA,CAAL,CAAP,EAAgBF,KAAK,CAACE,CAAD,CAArB,CAAL,EAAgC;AAC9B,iBAAO,KAAP;AACD;AACF;;AACD,aAAO,IAAP;AACD;;;WAED,qBAAYF,KAAZ,EAAoD;AAClD,UAAI,CAACA,KAAD,IAAU,KAAKqB,MAAL,KAAgBrB,KAAK,CAACqB,MAApC,EAA4C;AAC1C,eAAO,KAAP;AACD;;AACD,WAAK,IAAInB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,YAAI,KAAKA,CAAL,MAAYF,KAAK,CAACE,CAAD,CAArB,EAA0B;AACxB,iBAAO,KAAP;AACD;AACF;;AACD,aAAO,IAAP;AACD;;;WAKD,kBAAe;AACb,WAAK,IAAIA,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,aAAKA,CAAL,IAAU,CAAC,KAAKA,CAAL,CAAX;AACD;;AACD,aAAO,KAAKE,KAAL,EAAP;AACD;;;WAMD,cAAKkB,CAAL,EAAgCC,CAAhC,EAAoEC,CAApE,EAAsF;AACpF,UAAIA,CAAC,KAAKC,SAAV,EAAqB;AACnB,eAAO,KAAKC,IAAL,CAAU,IAAV,EAAgBJ,CAAhB,EAAmBC,CAAnB,CAAP;AACD;;AACD,WAAK,IAAIrB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,YAAMyB,EAAE,GAAGL,CAAC,CAACpB,CAAD,CAAZ;AACA,aAAKA,CAAL,IAAUyB,EAAE,GAAGH,CAAC,IAAID,CAAC,CAACrB,CAAD,CAAD,GAAOyB,EAAX,CAAhB;AACD;;AACD,aAAO,KAAKvB,KAAL,EAAP;AACD;;;WAGD,aAAIwB,MAAJ,EAA0C;AACxC,WAAK,IAAI1B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,aAAKA,CAAL,IAAU2B,IAAI,CAACC,GAAL,CAASF,MAAM,CAAC1B,CAAD,CAAf,EAAoB,KAAKA,CAAL,CAApB,CAAV;AACD;;AACD,aAAO,KAAKE,KAAL,EAAP;AACD;;;WAGD,aAAIwB,MAAJ,EAA0C;AACxC,WAAK,IAAI1B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,aAAKA,CAAL,IAAU2B,IAAI,CAACE,GAAL,CAASH,MAAM,CAAC1B,CAAD,CAAf,EAAoB,KAAKA,CAAL,CAApB,CAAV;AACD;;AACD,aAAO,KAAKE,KAAL,EAAP;AACD;;;WAED,eAAM4B,SAAN,EAAyCC,SAAzC,EAAkF;AAChF,WAAK,IAAI/B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,aAAKA,CAAL,IAAU2B,IAAI,CAACC,GAAL,CAASD,IAAI,CAACE,GAAL,CAAS,KAAK7B,CAAL,CAAT,EAAkB8B,SAAS,CAAC9B,CAAD,CAA3B,CAAT,EAA0C+B,SAAS,CAAC/B,CAAD,CAAnD,CAAV;AACD;;AACD,aAAO,KAAKE,KAAL,EAAP;AACD;;;WAED,eAAgD;AAAA,wCAAzC8B,OAAyC;AAAzCA,QAAAA,OAAyC;AAAA;;AAC9C,kCAAqBA,OAArB,8BAA8B;AAAzB,YAAMN,OAAM,eAAZ;;AACH,aAAK,IAAI1B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,eAAKA,CAAL,KAAW0B,OAAM,CAAC1B,CAAD,CAAjB;AACD;AACF;;AACD,aAAO,KAAKE,KAAL,EAAP;AACD;;;WAED,oBAAqD;AAAA,yCAAzC8B,OAAyC;AAAzCA,QAAAA,OAAyC;AAAA;;AACnD,oCAAqBA,OAArB,iCAA8B;AAAzB,YAAMN,QAAM,iBAAZ;;AACH,aAAK,IAAI1B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,eAAKA,CAAL,KAAW0B,QAAM,CAAC1B,CAAD,CAAjB;AACD;AACF;;AACD,aAAO,KAAKE,KAAL,EAAP;AACD;;;WAED,eAAM+B,MAAN,EAAoD;AAClD,UAAI,OAAOA,MAAP,KAAiB,QAArB,EAA+B;AAC7B,aAAK,IAAIjC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,eAAKA,CAAL,KAAWiC,MAAX;AACD;AACF,OAJD,MAIO;AACL,aAAK,IAAIjC,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAG,KAAKC,QAAT,IAAqBD,GAAC,GAAGiC,MAAK,CAACd,MAA/C,EAAuD,EAAEnB,GAAzD,EAA4D;AAC1D,eAAKA,GAAL,KAAWiC,MAAK,CAACjC,GAAD,CAAhB;AACD;AACF;;AACD,aAAO,KAAKE,KAAL,EAAP;AACD;;;WAMD,0BAAiBgC,MAAjB,EAAuC;AACrC,WAAK,IAAIlC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,aAAKA,CAAL,KAAWkC,MAAX;AACD;;AACD,aAAO,KAAKhC,KAAL,EAAP;AACD;;;WAKD,iBAAc;AACZ,UAAIY,eAAOqB,KAAP,IAAgB,CAAC,KAAKC,QAAL,EAArB,EAAsC;AACpC,cAAM,IAAIC,KAAJ,oBAAsB,KAAKzC,WAAL,CAAiBsB,IAAvC,0CAAN;AACD;;AACD,aAAO,IAAP;AACD;;;WAGD,oBAAoB;AAClB,UAAIoB,KAAK,GAAG,KAAKnB,MAAL,KAAgB,KAAKlB,QAAjC;;AACA,WAAK,IAAID,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtCsC,QAAAA,KAAK,GAAGA,KAAK,IAAIC,MAAM,CAACC,QAAP,CAAgB,KAAKxC,CAAL,CAAhB,CAAjB;AACD;;AACD,aAAOsC,KAAP;AACD;;;WAKD,aAAIlB,CAAJ,EAAqC;AACnC,aAAO,KAAKqB,QAAL,CAAcrB,CAAd,CAAP;AACD;;;WAGD,mBAAUA,CAAV,EAA2B;AACzB,WAAK,IAAIpB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,aAAKA,CAAL,IAAUoB,CAAV;AACD;;AACD,aAAO,KAAKlB,KAAL,EAAP;AACD;;;WAGD,mBAAUkB,CAAV,EAA2B;AACzB,WAAK,IAAIpB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,aAAKA,CAAL,KAAWoB,CAAX;AACD;;AACD,aAAO,KAAKlB,KAAL,EAAP;AACD;;;WAGD,mBAAUkB,CAAV,EAA2B;AACzB,aAAO,KAAKsB,SAAL,CAAe,CAACtB,CAAhB,CAAP;AACD;;;WAGD,wBAAec,MAAf,EAAqC;AAGnC,WAAK,IAAIlC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,aAAKA,CAAL,KAAWkC,MAAX;AACD;;AACD,aAAO,KAAKhC,KAAL,EAAP;AACD;;;WAGD,sBAAakB,CAAb,EAA8B;AAC5B,aAAO,KAAKuB,gBAAL,CAAsB,IAAIvB,CAA1B,CAAP;AACD;;;WAGD,qBAAYQ,GAAZ,EAAyBC,GAAzB,EAA4C;AAC1C,WAAK,IAAI7B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,aAAKA,CAAL,IAAU2B,IAAI,CAACC,GAAL,CAASD,IAAI,CAACE,GAAL,CAAS,KAAK7B,CAAL,CAAT,EAAkB4B,GAAlB,CAAT,EAAiCC,GAAjC,CAAV;AACD;;AACD,aAAO,KAAK3B,KAAL,EAAP;AACD;;;SAGD,eAA6B;AAC3B,aAAO,IAAP;AACD;;;qBAlQ6CG,K", "sourcesContent": ["// math.gl, MIT License\nimport {NumericArray} from '@math.gl/types';\nimport {ConfigurationOptions, config, formatValue, equals, isArray} from '../../lib/common';\n\n/** Base class for vectors and matrices */\nexport default abstract class MathArray extends Array<number> {\n  /** Number of elements (values) in this array */\n  abstract get ELEMENTS(): number;\n\n  abstract copy(vector: Readonly<NumericArray>): this;\n\n  abstract fromObject(object: object): this;\n\n  // Common methods\n\n  /**\n   * Clone the current object\n   * @returns a new copy of this object\n   */\n  clone(): this {\n    // @ts-expect-error TS2351: Cannot use 'new' with an expression whose type lacks a call or construct signature.\n    return new this.constructor().copy(this); // eslint-disable-line\n  }\n\n  fromArray(array: Readonly<NumericArray>, offset: number = 0): this {\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      this[i] = array[i + offset];\n    }\n    return this.check();\n  }\n\n  toArray<TypedArray>(targetArray: TypedArray, offset?: number): TypedArray;\n  toArray(targetArray?: number[], offset?: number): NumericArray;\n\n  toArray(targetArray: NumericArray = [], offset: number = 0): NumericArray {\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      targetArray[offset + i] = this[i];\n    }\n    return targetArray;\n  }\n\n  from(arrayOrObject: Readonly<NumericArray> | object): this {\n    return Array.isArray(arrayOrObject) ? this.copy(arrayOrObject) : this.fromObject(arrayOrObject);\n  }\n\n  to<T extends NumericArray | object>(arrayOrObject: T): T {\n    // @ts-ignore\n    if (arrayOrObject === this) {\n      return this;\n    }\n    // @ts-expect-error TS2339: Property 'toObject' does not exist on type 'MathArray'.\n    return isArray(arrayOrObject) ? this.toArray(arrayOrObject) : this.toObject(arrayOrObject);\n  }\n\n  toTarget(target: this): this {\n    return target ? this.to(target) : this;\n  }\n\n  /** @deprecated */\n  toFloat32Array(): Float32Array {\n    return new Float32Array(this);\n  }\n\n  toString(): string {\n    return this.formatString(config);\n  }\n\n  /** Formats string according to options */\n  formatString(opts: ConfigurationOptions): string {\n    let string = '';\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      string += (i > 0 ? ', ' : '') + formatValue(this[i], opts);\n    }\n    return `${opts.printTypes ? this.constructor.name : ''}[${string}]`;\n  }\n\n  equals(array: Readonly<NumericArray>): boolean {\n    if (!array || this.length !== array.length) {\n      return false;\n    }\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      if (!equals(this[i], array[i])) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  exactEquals(array: Readonly<NumericArray>): boolean {\n    if (!array || this.length !== array.length) {\n      return false;\n    }\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      if (this[i] !== array[i]) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  // Modifiers\n\n  /** Negates all values in this object */\n  negate(): this {\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      this[i] = -this[i];\n    }\n    return this.check();\n  }\n\n  /** Linearly interpolates between two values */\n  lerp(a: Readonly<NumericArray>, t: number): this;\n  lerp(a: Readonly<NumericArray>, b: Readonly<NumericArray>, t: number): this;\n\n  lerp(a: Readonly<NumericArray>, b: Readonly<NumericArray> | number, t?: number): this {\n    if (t === undefined) {\n      return this.lerp(this, a, b as number);\n    }\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      const ai = a[i];\n      this[i] = ai + t * (b[i] - ai);\n    }\n    return this.check();\n  }\n\n  /** Minimal */\n  min(vector: Readonly<NumericArray>): this {\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      this[i] = Math.min(vector[i], this[i]);\n    }\n    return this.check();\n  }\n\n  /** Maximal */\n  max(vector: Readonly<NumericArray>): this {\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      this[i] = Math.max(vector[i], this[i]);\n    }\n    return this.check();\n  }\n\n  clamp(minVector: Readonly<NumericArray>, maxVector: Readonly<NumericArray>): this {\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      this[i] = Math.min(Math.max(this[i], minVector[i]), maxVector[i]);\n    }\n    return this.check();\n  }\n\n  add(...vectors: Readonly<NumericArray>[]): this {\n    for (const vector of vectors) {\n      for (let i = 0; i < this.ELEMENTS; ++i) {\n        this[i] += vector[i];\n      }\n    }\n    return this.check();\n  }\n\n  subtract(...vectors: Readonly<NumericArray>[]): this {\n    for (const vector of vectors) {\n      for (let i = 0; i < this.ELEMENTS; ++i) {\n        this[i] -= vector[i];\n      }\n    }\n    return this.check();\n  }\n\n  scale(scale: number | Readonly<NumericArray>): this {\n    if (typeof scale === 'number') {\n      for (let i = 0; i < this.ELEMENTS; ++i) {\n        this[i] *= scale;\n      }\n    } else {\n      for (let i = 0; i < this.ELEMENTS && i < scale.length; ++i) {\n        this[i] *= scale[i];\n      }\n    }\n    return this.check();\n  }\n\n  /**\n   * Multiplies all elements by `scale`\n   * Note: `Matrix4.multiplyByScalar` only scales its 3x3 \"minor\"\n   */\n  multiplyByScalar(scalar: number): this {\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      this[i] *= scalar;\n    }\n    return this.check();\n  }\n\n  // Debug checks\n\n  /** Throws an error if array length is incorrect or contains illegal values */\n  check(): this {\n    if (config.debug && !this.validate()) {\n      throw new Error(`math.gl: ${this.constructor.name} some fields set to invalid numbers'`);\n    }\n    return this;\n  }\n\n  /** Returns false if the array length is incorrect or contains illegal values */\n  validate(): boolean {\n    let valid = this.length === this.ELEMENTS;\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      valid = valid && Number.isFinite(this[i]);\n    }\n    return valid;\n  }\n\n  // three.js compatibility\n\n  /** @deprecated */\n  sub(a: Readonly<NumericArray>): this {\n    return this.subtract(a);\n  }\n\n  /** @deprecated */\n  setScalar(a: number): this {\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      this[i] = a;\n    }\n    return this.check();\n  }\n\n  /** @deprecated */\n  addScalar(a: number): this {\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      this[i] += a;\n    }\n    return this.check();\n  }\n\n  /** @deprecated */\n  subScalar(a: number): this {\n    return this.addScalar(-a);\n  }\n\n  /** @deprecated */\n  multiplyScalar(scalar: number): this {\n    // Multiplies all elements\n    // `Matrix4.scale` only scales its 3x3 \"minor\"\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      this[i] *= scalar;\n    }\n    return this.check();\n  }\n\n  /** @deprecated */\n  divideScalar(a: number): this {\n    return this.multiplyByScalar(1 / a);\n  }\n\n  /** @deprecated */\n  clampScalar(min: number, max: number): this {\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      this[i] = Math.min(Math.max(this[i], min), max);\n    }\n    return this.check();\n  }\n\n  /** @deprecated */\n  get elements(): NumericArray {\n    return this;\n  }\n}\n"], "file": "math-array.js"}