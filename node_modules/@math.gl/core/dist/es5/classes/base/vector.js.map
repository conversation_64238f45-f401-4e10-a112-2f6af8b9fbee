{"version": 3, "sources": ["../../../../src/classes/base/vector.ts"], "names": ["Vector", "value", "Math", "sqrt", "lengthSquared", "len", "length", "i", "ELEMENTS", "<PERSON><PERSON><PERSON><PERSON>", "distanceSquared", "dist", "product", "magnitude", "check", "vectors", "vector", "distance", "a", "b", "copy", "add", "subtract", "multiply", "constructor", "multiplyScalar", "MathArray"], "mappings": ";;;;;;;;;;;;;;;;;;;AAGA;;AACA;;AACA;;;;;;IAG8BA,M;;;;;;;;;;;;SAG5B,eAAgB;AACd,aAAO,KAAK,CAAL,CAAP;AACD,K;SAED,aAAMC,KAAN,EAAqB;AACnB,WAAK,CAAL,IAAU,6BAAYA,KAAZ,CAAV;AACD;;;SAED,eAAgB;AACd,aAAO,KAAK,CAAL,CAAP;AACD,K;SAED,aAAMA,KAAN,EAAqB;AACnB,WAAK,CAAL,IAAU,6BAAYA,KAAZ,CAAV;AACD;;;WAQD,eAAc;AACZ,aAAOC,IAAI,CAACC,IAAL,CAAU,KAAKC,aAAL,EAAV,CAAP;AACD;;;WAKD,qBAAoB;AAClB,aAAO,KAAKC,GAAL,EAAP;AACD;;;WAKD,yBAAwB;AACtB,UAAIC,MAAM,GAAG,CAAb;;AACA,WAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtCD,QAAAA,MAAM,IAAI,KAAKC,CAAL,IAAU,KAAKA,CAAL,CAApB;AACD;;AACD,aAAOD,MAAP;AACD;;;WAKD,4BAA2B;AACzB,aAAO,KAAKF,aAAL,EAAP;AACD;;;WAED,kBAASK,SAAT,EAAoD;AAClD,aAAOP,IAAI,CAACC,IAAL,CAAU,KAAKO,eAAL,CAAqBD,SAArB,CAAV,CAAP;AACD;;;WAED,yBAAgBA,SAAhB,EAA2D;AACzD,UAAIH,MAAM,GAAG,CAAb;;AACA,WAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,YAAMI,IAAI,GAAG,KAAKJ,CAAL,IAAUE,SAAS,CAACF,CAAD,CAAhC;AACAD,QAAAA,MAAM,IAAIK,IAAI,GAAGA,IAAjB;AACD;;AACD,aAAO,6BAAYL,MAAZ,CAAP;AACD;;;WAED,aAAIG,SAAJ,EAA+C;AAC7C,UAAIG,OAAO,GAAG,CAAd;;AACA,WAAK,IAAIL,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtCK,QAAAA,OAAO,IAAI,KAAKL,CAAL,IAAUE,SAAS,CAACF,CAAD,CAA9B;AACD;;AACD,aAAO,6BAAYK,OAAZ,CAAP;AACD;;;WAID,qBAAkB;AAChB,UAAMN,MAAM,GAAG,KAAKO,SAAL,EAAf;;AACA,UAAIP,MAAM,KAAK,CAAf,EAAkB;AAChB,aAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,eAAKA,CAAL,KAAWD,MAAX;AACD;AACF;;AACD,aAAO,KAAKQ,KAAL,EAAP;AACD;;;WAED,oBAAqD;AAAA,wCAAzCC,OAAyC;AAAzCA,QAAAA,OAAyC;AAAA;;AACnD,kCAAqBA,OAArB,8BAA8B;AAAzB,YAAMC,MAAM,eAAZ;;AACH,aAAK,IAAIT,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,eAAKA,CAAL,KAAWS,MAAM,CAACT,CAAD,CAAjB;AACD;AACF;;AACD,aAAO,KAAKO,KAAL,EAAP;AACD;;;WAED,kBAAmD;AAAA,yCAAzCC,OAAyC;AAAzCA,QAAAA,OAAyC;AAAA;;AACjD,oCAAqBA,OAArB,iCAA8B;AAAzB,YAAMC,MAAM,iBAAZ;;AACH,aAAK,IAAIT,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtC,eAAKA,CAAL,KAAWS,MAAM,CAACT,CAAD,CAAjB;AACD;AACF;;AACD,aAAO,KAAKO,KAAL,EAAP;AACD;;;WAID,oBAAmB;AACjB,aAAO,KAAKV,aAAL,EAAP;AACD;;;WACD,oBAAWY,MAAX,EAAmD;AACjD,aAAO,KAAKC,QAAL,CAAcD,MAAd,CAAP;AACD;;;WACD,2BAAkBA,MAAlB,EAA0D;AACxD,aAAO,KAAKN,eAAL,CAAqBM,MAArB,CAAP;AACD;;;WAED,sBAAaT,CAAb,EAAgC;AAC9B,2BAAOA,CAAC,IAAI,CAAL,IAAUA,CAAC,GAAG,KAAKC,QAA1B,EAAoC,uBAApC;AACA,aAAO,6BAAY,KAAKD,CAAL,CAAZ,CAAP;AACD;;;WAED,sBAAaA,CAAb,EAAwBN,KAAxB,EAA6C;AAC3C,2BAAOM,CAAC,IAAI,CAAL,IAAUA,CAAC,GAAG,KAAKC,QAA1B,EAAoC,uBAApC;AACA,WAAKD,CAAL,IAAUN,KAAV;AACA,aAAO,KAAKa,KAAL,EAAP;AACD;;;WAED,oBAAWI,CAAX,EAAsCC,CAAtC,EAAuE;AACrE,aAAO,KAAKC,IAAL,CAAUF,CAAV,EAAaG,GAAb,CAAiBF,CAAjB,CAAP;AACD;;;WAED,oBAAWD,CAAX,EAAsCC,CAAtC,EAAuE;AACrE,aAAO,KAAKC,IAAL,CAAUF,CAAV,EAAaI,QAAb,CAAsBH,CAAtB,CAAP;AACD;;;WAED,yBAAgBD,CAAhB,EAA2CC,CAA3C,EAA4E;AAC1E,aAAO,KAAKC,IAAL,CAAUF,CAAV,EAAaK,QAAb,CAAsBJ,CAAtB,CAAP;AACD;;;WAED,yBAAgBD,CAAhB,EAA2CC,CAA3C,EAA4D;AAE1D,aAAO,KAAKE,GAAL,CAAS,IAAI,KAAKG,WAAT,CAAqBN,CAArB,EAAwBO,cAAxB,CAAuCN,CAAvC,CAAT,CAAP;AACD;;;EA/I0CO,kB", "sourcesContent": ["// Copyright (c) 2017 Uber Technologies, Inc.\n// MIT License\nimport {NumericArray} from '@math.gl/types';\nimport MathArray from './math-array';\nimport {checkNumber} from '../../lib/validators';\nimport assert from '../../lib/assert';\n\n/** Base class for vectors with at least 2 elements */\nexport default abstract class Vector extends MathArray {\n  // ACCESSORS\n\n  get x(): number {\n    return this[0];\n  }\n\n  set x(value: number) {\n    this[0] = checkNumber(value);\n  }\n\n  get y(): number {\n    return this[1];\n  }\n\n  set y(value: number) {\n    this[1] = checkNumber(value);\n  }\n\n  /**\n   * Returns the length of the vector from the origin to the point described by this vector\n   *\n   * @note `length` is a reserved word for Arrays, so `v.length()` will return number of elements\n   * Instead we provide `len` and `magnitude`\n   */\n  len(): number {\n    return Math.sqrt(this.lengthSquared());\n  }\n\n  /**\n   * Returns the length of the vector from the origin to the point described by this vector\n   */\n  magnitude(): number {\n    return this.len();\n  }\n\n  /**\n   * Returns the squared length of the vector from the origin to the point described by this vector\n   */\n  lengthSquared(): number {\n    let length = 0;\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      length += this[i] * this[i];\n    }\n    return length;\n  }\n\n  /**\n   * Returns the squared length of the vector from the origin to the point described by this vector\n   */\n  magnitudeSquared(): number {\n    return this.lengthSquared();\n  }\n\n  distance(mathArray: Readonly<NumericArray>): number {\n    return Math.sqrt(this.distanceSquared(mathArray));\n  }\n\n  distanceSquared(mathArray: Readonly<NumericArray>): number {\n    let length = 0;\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      const dist = this[i] - mathArray[i];\n      length += dist * dist;\n    }\n    return checkNumber(length);\n  }\n\n  dot(mathArray: Readonly<NumericArray>): number {\n    let product = 0;\n    for (let i = 0; i < this.ELEMENTS; ++i) {\n      product += this[i] * mathArray[i];\n    }\n    return checkNumber(product);\n  }\n\n  // MODIFIERS\n\n  normalize(): this {\n    const length = this.magnitude();\n    if (length !== 0) {\n      for (let i = 0; i < this.ELEMENTS; ++i) {\n        this[i] /= length;\n      }\n    }\n    return this.check();\n  }\n\n  multiply(...vectors: Readonly<NumericArray>[]): this {\n    for (const vector of vectors) {\n      for (let i = 0; i < this.ELEMENTS; ++i) {\n        this[i] *= vector[i];\n      }\n    }\n    return this.check();\n  }\n\n  divide(...vectors: Readonly<NumericArray>[]): this {\n    for (const vector of vectors) {\n      for (let i = 0; i < this.ELEMENTS; ++i) {\n        this[i] /= vector[i];\n      }\n    }\n    return this.check();\n  }\n\n  // THREE.js compatibility\n\n  lengthSq(): number {\n    return this.lengthSquared();\n  }\n  distanceTo(vector: Readonly<NumericArray>): number {\n    return this.distance(vector);\n  }\n  distanceToSquared(vector: Readonly<NumericArray>): number {\n    return this.distanceSquared(vector);\n  }\n\n  getComponent(i: number): number {\n    assert(i >= 0 && i < this.ELEMENTS, 'index is out of range');\n    return checkNumber(this[i]);\n  }\n\n  setComponent(i: number, value: number): this {\n    assert(i >= 0 && i < this.ELEMENTS, 'index is out of range');\n    this[i] = value;\n    return this.check();\n  }\n\n  addVectors(a: Readonly<NumericArray>, b: Readonly<NumericArray>): this {\n    return this.copy(a).add(b);\n  }\n\n  subVectors(a: Readonly<NumericArray>, b: Readonly<NumericArray>): this {\n    return this.copy(a).subtract(b);\n  }\n\n  multiplyVectors(a: Readonly<NumericArray>, b: Readonly<NumericArray>): this {\n    return this.copy(a).multiply(b);\n  }\n\n  addScaledVector(a: Readonly<NumericArray>, b: number): this {\n    // @ts-expect-error error TS2351: Cannot use 'new' with an expression whose type lacks a call or construct signature.\n    return this.add(new this.constructor(a).multiplyScalar(b));\n  }\n}\n"], "file": "vector.js"}