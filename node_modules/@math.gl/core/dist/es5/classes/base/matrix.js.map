{"version": 3, "sources": ["../../../../src/classes/base/matrix.ts"], "names": ["Matrix", "string", "config", "printRowMajor", "row", "RANK", "col", "i", "ELEMENTS", "value", "columnIndex", "result", "Array", "fill", "firstIndex", "columnVector", "MathArray"], "mappings": ";;;;;;;;;;;;;;;;;;;AAGA;;AACA;;AACA;;;;;;IAG8BA,M;;;;;;;;;;;;WAc5B,oBAAmB;AACjB,UAAIC,MAAM,GAAG,GAAb;;AACA,UAAIC,eAAOC,aAAX,EAA0B;AACxBF,QAAAA,MAAM,IAAI,YAAV;;AACA,aAAK,IAAIG,GAAG,GAAG,CAAf,EAAkBA,GAAG,GAAG,KAAKC,IAA7B,EAAmC,EAAED,GAArC,EAA0C;AACxC,eAAK,IAAIE,GAAG,GAAG,CAAf,EAAkBA,GAAG,GAAG,KAAKD,IAA7B,EAAmC,EAAEC,GAArC,EAA0C;AACxCL,YAAAA,MAAM,eAAQ,KAAKK,GAAG,GAAG,KAAKD,IAAX,GAAkBD,GAAvB,CAAR,CAAN;AACD;AACF;AACF,OAPD,MAOO;AACLH,QAAAA,MAAM,IAAI,eAAV;;AACA,aAAK,IAAIM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,QAAzB,EAAmC,EAAED,CAArC,EAAwC;AACtCN,UAAAA,MAAM,eAAQ,KAAKM,CAAL,CAAR,CAAN;AACD;AACF;;AACDN,MAAAA,MAAM,IAAI,GAAV;AACA,aAAOA,MAAP;AACD;;;WAED,yBAAgBG,GAAhB,EAA6BE,GAA7B,EAAkD;AAChD,aAAOA,GAAG,GAAG,KAAKD,IAAX,GAAkBD,GAAzB;AACD;;;WAGD,oBAAWA,GAAX,EAAwBE,GAAxB,EAA6C;AAC3C,aAAO,KAAKA,GAAG,GAAG,KAAKD,IAAX,GAAkBD,GAAvB,CAAP;AACD;;;WAGD,oBAAWA,GAAX,EAAwBE,GAAxB,EAAqCG,KAArC,EAA0D;AACxD,WAAKH,GAAG,GAAG,KAAKD,IAAX,GAAkBD,GAAvB,IAA8B,6BAAYK,KAAZ,CAA9B;AACA,aAAO,IAAP;AACD;;;WAID,mBAAUC,WAAV,EAA2F;AAAA,UAA5DC,MAA4D,uEAAzC,IAAIC,KAAJ,CAAU,KAAKP,IAAf,EAAqBQ,IAArB,CAA0B,CAAC,CAA3B,CAAyC;AACzF,UAAMC,UAAU,GAAGJ,WAAW,GAAG,KAAKL,IAAtC;;AACA,WAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKF,IAAzB,EAA+B,EAAEE,CAAjC,EAAoC;AAClCI,QAAAA,MAAM,CAACJ,CAAD,CAAN,GAAY,KAAKO,UAAU,GAAGP,CAAlB,CAAZ;AACD;;AACD,aAAOI,MAAP;AACD;;;WAED,mBAAUD,WAAV,EAA+BK,YAA/B,EAA2E;AACzE,UAAMD,UAAU,GAAGJ,WAAW,GAAG,KAAKL,IAAtC;;AACA,WAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKF,IAAzB,EAA+B,EAAEE,CAAjC,EAAoC;AAClC,aAAKO,UAAU,GAAGP,CAAlB,IAAuBQ,YAAY,CAACR,CAAD,CAAnC;AACD;;AACD,aAAO,IAAP;AACD;;;EAhE0CS,kB", "sourcesContent": ["// Copyright (c) 2017 Uber Technologies, Inc.\n// MIT License\nimport {NumericArray} from '@math.gl/types';\nimport MathArray from './math-array';\nimport {checkNumber} from '../../lib/validators';\nimport {config} from '../../lib/common';\n\n/** Base class for matrices */\nexport default abstract class Matrix extends MathArray {\n  abstract get RANK(): number;\n\n  // fromObject(object) {\n  //   const array = object.elements;\n  //   return this.fromRowMajor(array);\n  // }\n  // toObject(object) {\n  //   const array = object.elements;\n  //   this.toRowMajor(array);\n  //   return object;\n  // }\n\n  // TODO better override formatString?\n  toString(): string {\n    let string = '[';\n    if (config.printRowMajor) {\n      string += 'row-major:';\n      for (let row = 0; row < this.RANK; ++row) {\n        for (let col = 0; col < this.RANK; ++col) {\n          string += ` ${this[col * this.RANK + row]}`;\n        }\n      }\n    } else {\n      string += 'column-major:';\n      for (let i = 0; i < this.ELEMENTS; ++i) {\n        string += ` ${this[i]}`;\n      }\n    }\n    string += ']';\n    return string;\n  }\n\n  getElementIndex(row: number, col: number): number {\n    return col * this.RANK + row;\n  }\n\n  // By default assumes row major indices\n  getElement(row: number, col: number): number {\n    return this[col * this.RANK + row];\n  }\n\n  // By default assumes row major indices\n  setElement(row: number, col: number, value: number): this {\n    this[col * this.RANK + row] = checkNumber(value);\n    return this;\n  }\n  getColumn<NumArrayT>(columnIndex: number, result: NumArrayT): NumArrayT;\n  getColumn(columnIndex: number): number[];\n\n  getColumn(columnIndex: number, result: number[] = new Array(this.RANK).fill(-0)): number[] {\n    const firstIndex = columnIndex * this.RANK;\n    for (let i = 0; i < this.RANK; ++i) {\n      result[i] = this[firstIndex + i];\n    }\n    return result;\n  }\n\n  setColumn(columnIndex: number, columnVector: Readonly<NumericArray>): this {\n    const firstIndex = columnIndex * this.RANK;\n    for (let i = 0; i < this.RANK; ++i) {\n      this[firstIndex + i] = columnVector[i];\n    }\n    return this;\n  }\n}\n"], "file": "matrix.js"}