{"version": 3, "sources": ["../../../src/classes/vector3.ts"], "names": ["ORIGIN", "ZERO", "Vector3", "x", "y", "z", "arguments", "length", "copy", "config", "debug", "check", "array", "object", "value", "vector", "vec3", "angle", "cross", "radians", "origin", "rotateX", "rotateY", "rotateZ", "matrix4", "transformAsPoint", "transformMat4", "matrix3", "transformMat3", "matrix2", "quaternion", "transformQuat", "Object", "freeze", "Vector"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAGA;;AACA;;AACA;;AACA;;AAEA;;;;;;;;;;AAEA,IAAMA,MAAM,GAAG,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAAf;AAEA,IAAIC,IAAJ;;IAMqBC,O;;;;;AAenB,qBAAkF;AAAA;;AAAA,QAAtEC,CAAsE,uEAAjC,CAAiC;AAAA,QAA9BC,CAA8B,uEAAlB,CAAkB;AAAA,QAAfC,CAAe,uEAAH,CAAG;AAAA;AAEhF,8BAAM,CAAC,CAAP,EAAU,CAAC,CAAX,EAAc,CAAC,CAAf;;AACA,QAAIC,SAAS,CAACC,MAAV,KAAqB,CAArB,IAA0B,qBAAQJ,CAAR,CAA9B,EAA0C;AACxC,YAAKK,IAAL,CAAUL,CAAV;AACD,KAFD,MAEO;AAEL,UAAIM,eAAOC,KAAX,EAAkB;AAChB,qCAAYP,CAAZ;AACA,qCAAYC,CAAZ;AACA,qCAAYC,CAAZ;AACD;;AAED,YAAK,CAAL,IAAUF,CAAV;AACA,YAAK,CAAL,IAAUC,CAAV;AACA,YAAK,CAAL,IAAUC,CAAV;AACD;;AAhB+E;AAiBjF;;;;WAED,aAAIF,CAAJ,EAAeC,CAAf,EAA0BC,CAA1B,EAA2C;AACzC,WAAK,CAAL,IAAUF,CAAV;AACA,WAAK,CAAL,IAAUC,CAAV;AACA,WAAK,CAAL,IAAUC,CAAV;AACA,aAAO,KAAKM,KAAL,EAAP;AACD;;;WAED,cAAKC,KAAL,EAA0C;AACxC,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,aAAO,KAAKD,KAAL,EAAP;AACD;;;WAED,oBAAWE,MAAX,EAA4D;AAC1D,UAAIJ,eAAOC,KAAX,EAAkB;AAChB,qCAAYG,MAAM,CAACV,CAAnB;AACA,qCAAYU,MAAM,CAACT,CAAnB;AACA,qCAAYS,MAAM,CAACR,CAAnB;AACD;;AACD,WAAK,CAAL,IAAUQ,MAAM,CAACV,CAAjB;AACA,WAAK,CAAL,IAAUU,MAAM,CAACT,CAAjB;AACA,WAAK,CAAL,IAAUS,MAAM,CAACR,CAAjB;AACA,aAAO,KAAKM,KAAL,EAAP;AACD;;;WAED,kBAASE,MAAT,EAA0F;AACxFA,MAAAA,MAAM,CAACV,CAAP,GAAW,KAAK,CAAL,CAAX;AACAU,MAAAA,MAAM,CAACT,CAAP,GAAW,KAAK,CAAL,CAAX;AACAS,MAAAA,MAAM,CAACR,CAAP,GAAW,KAAK,CAAL,CAAX;AACA,aAAOQ,MAAP;AACD;;;SAID,eAAuB;AACrB,aAAO,CAAP;AACD;;;SACD,eAAgB;AACd,aAAO,KAAK,CAAL,CAAP;AACD,K;SACD,aAAMC,KAAN,EAAqB;AACnB,WAAK,CAAL,IAAU,6BAAYA,KAAZ,CAAV;AACD;;;WAID,eAAMC,MAAN,EAA8C;AAC5C,aAAOC,IAAI,CAACC,KAAL,CAAW,IAAX,EAAiBF,MAAjB,CAAP;AACD;;;WAID,eAAMA,MAAN,EAA4C;AAC1CC,MAAAA,IAAI,CAACE,KAAL,CAAW,IAAX,EAAiB,IAAjB,EAAuBH,MAAvB;AACA,aAAO,KAAKJ,KAAL,EAAP;AACD;;;WAED,uBAA8F;AAAA,UAArFQ,OAAqF,QAArFA,OAAqF;AAAA,6BAA5EC,MAA4E;AAAA,UAA5EA,MAA4E,4BAAnEpB,MAAmE;AAC5FgB,MAAAA,IAAI,CAACK,OAAL,CAAa,IAAb,EAAmB,IAAnB,EAAyBD,MAAzB,EAAiCD,OAAjC;AACA,aAAO,KAAKR,KAAL,EAAP;AACD;;;WAED,wBAA8F;AAAA,UAArFQ,OAAqF,SAArFA,OAAqF;AAAA,+BAA5EC,MAA4E;AAAA,UAA5EA,MAA4E,6BAAnEpB,MAAmE;AAC5FgB,MAAAA,IAAI,CAACM,OAAL,CAAa,IAAb,EAAmB,IAAnB,EAAyBF,MAAzB,EAAiCD,OAAjC;AACA,aAAO,KAAKR,KAAL,EAAP;AACD;;;WAED,wBAA8F;AAAA,UAArFQ,OAAqF,SAArFA,OAAqF;AAAA,+BAA5EC,MAA4E;AAAA,UAA5EA,MAA4E,6BAAnEpB,MAAmE;AAC5FgB,MAAAA,IAAI,CAACO,OAAL,CAAa,IAAb,EAAmB,IAAnB,EAAyBH,MAAzB,EAAiCD,OAAjC;AACA,aAAO,KAAKR,KAAL,EAAP;AACD;;;WAKD,mBAAUa,OAAV,EAAiD;AAC/C,aAAO,KAAKC,gBAAL,CAAsBD,OAAtB,CAAP;AACD;;;WAGD,0BAAiBA,OAAjB,EAAwD;AACtDR,MAAAA,IAAI,CAACU,aAAL,CAAmB,IAAnB,EAAyB,IAAzB,EAA+BF,OAA/B;AACA,aAAO,KAAKb,KAAL,EAAP;AACD;;;WAGD,2BAAkBa,OAAlB,EAAyD;AACvD,sDAA2B,IAA3B,EAAiC,IAAjC,EAAuCA,OAAvC;AACA,aAAO,KAAKb,KAAL,EAAP;AACD;;;WAED,4BAAmBgB,OAAnB,EAA0D;AACxDX,MAAAA,IAAI,CAACY,aAAL,CAAmB,IAAnB,EAAyB,IAAzB,EAA+BD,OAA/B;AACA,aAAO,KAAKhB,KAAL,EAAP;AACD;;;WAED,4BAAmBkB,OAAnB,EAA0D;AACxD,8CAAmB,IAAnB,EAAyB,IAAzB,EAA+BA,OAA/B;AACA,aAAO,KAAKlB,KAAL,EAAP;AACD;;;WAED,+BAAsBmB,UAAtB,EAAgE;AAC9Dd,MAAAA,IAAI,CAACe,aAAL,CAAmB,IAAnB,EAAyB,IAAzB,EAA+BD,UAA/B;AACA,aAAO,KAAKnB,KAAL,EAAP;AACD;;;SA1ID,eAA2B;AACzB,UAAI,CAACV,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAIC,OAAJ,CAAY,CAAZ,EAAe,CAAf,EAAkB,CAAlB,CAAP;AACA8B,QAAAA,MAAM,CAACC,MAAP,CAAchC,IAAd;AACD;;AACD,aAAOA,IAAP;AACD;;;EAPkCiC,e", "sourcesContent": ["// Copyright (c) 2017 Uber Technologies, Inc.\n// MIT License\nimport {NumericArray} from '@math.gl/types';\nimport Vector from './base/vector';\nimport {config, isArray} from '../lib/common';\nimport {checkNumber} from '../lib/validators';\nimport * as vec3 from 'gl-matrix/vec3';\n/* eslint-disable camelcase */\nimport {vec3_transformMat2, vec3_transformMat4AsVector} from '../lib/gl-matrix-extras';\n\nconst ORIGIN = [0, 0, 0];\n\nlet ZERO: Vector3;\n\n/**\n * Three-element vector class.\n * Subclass of Array<number>\n */\nexport default class Vector3 extends Vector {\n  static get ZERO(): Vector3 {\n    if (!ZERO) {\n      ZERO = new Vector3(0, 0, 0);\n      Object.freeze(ZERO);\n    }\n    return ZERO;\n  }\n\n  /**\n   * @class\n   * @param x\n   * @param y\n   * @param z\n   */\n  constructor(x: number | Readonly<NumericArray> = 0, y: number = 0, z: number = 0) {\n    // PERF NOTE: initialize elements as double precision numbers\n    super(-0, -0, -0);\n    if (arguments.length === 1 && isArray(x)) {\n      this.copy(x as NumericArray);\n    } else {\n      // this.set(x, y, z);\n      if (config.debug) {\n        checkNumber(x);\n        checkNumber(y);\n        checkNumber(z);\n      }\n      // @ts-expect-error TS2412: Property '0' of type 'number | [number, number, number]' is not assignable to numeric index type 'number'\n      this[0] = x;\n      this[1] = y;\n      this[2] = z;\n    }\n  }\n\n  set(x: number, y: number, z: number): this {\n    this[0] = x;\n    this[1] = y;\n    this[2] = z;\n    return this.check();\n  }\n\n  copy(array: Readonly<NumericArray>): this {\n    this[0] = array[0];\n    this[1] = array[1];\n    this[2] = array[2];\n    return this.check();\n  }\n\n  fromObject(object: {x: number; y: number; z: number}): this {\n    if (config.debug) {\n      checkNumber(object.x);\n      checkNumber(object.y);\n      checkNumber(object.z);\n    }\n    this[0] = object.x;\n    this[1] = object.y;\n    this[2] = object.z;\n    return this.check();\n  }\n\n  toObject(object: {x?: number; y?: number; z?: number}): {x: number; y: number; z: number} {\n    object.x = this[0];\n    object.y = this[1];\n    object.z = this[2];\n    return object as {x: number; y: number; z: number};\n  }\n\n  // Getters/setters\n\n  get ELEMENTS(): number {\n    return 3;\n  }\n  get z(): number {\n    return this[2];\n  }\n  set z(value: number) {\n    this[2] = checkNumber(value);\n  }\n\n  // ACCESSORS\n\n  angle(vector: Readonly<NumericArray>): number {\n    return vec3.angle(this, vector);\n  }\n\n  // MODIFIERS\n\n  cross(vector: Readonly<NumericArray>): this {\n    vec3.cross(this, this, vector);\n    return this.check();\n  }\n\n  rotateX({radians, origin = ORIGIN}: {radians: number; origin?: Readonly<NumericArray>}): this {\n    vec3.rotateX(this, this, origin, radians);\n    return this.check();\n  }\n\n  rotateY({radians, origin = ORIGIN}: {radians: number; origin?: Readonly<NumericArray>}): this {\n    vec3.rotateY(this, this, origin, radians);\n    return this.check();\n  }\n\n  rotateZ({radians, origin = ORIGIN}: {radians: number; origin?: Readonly<NumericArray>}): this {\n    vec3.rotateZ(this, this, origin, radians);\n    return this.check();\n  }\n\n  // Transforms\n\n  // transforms as point (4th component is implicitly 1)\n  transform(matrix4: Readonly<NumericArray>): this {\n    return this.transformAsPoint(matrix4);\n  }\n\n  // transforms as point (4th component is implicitly 1)\n  transformAsPoint(matrix4: Readonly<NumericArray>): this {\n    vec3.transformMat4(this, this, matrix4);\n    return this.check();\n  }\n\n  // transforms as vector  (4th component is implicitly 0, ignores translation. slightly faster)\n  transformAsVector(matrix4: Readonly<NumericArray>): this {\n    vec3_transformMat4AsVector(this, this, matrix4);\n    return this.check();\n  }\n\n  transformByMatrix3(matrix3: Readonly<NumericArray>): this {\n    vec3.transformMat3(this, this, matrix3);\n    return this.check();\n  }\n\n  transformByMatrix2(matrix2: Readonly<NumericArray>): this {\n    vec3_transformMat2(this, this, matrix2);\n    return this.check();\n  }\n\n  transformByQuaternion(quaternion: Readonly<NumericArray>): this {\n    vec3.transformQuat(this, this, quaternion);\n    return this.check();\n  }\n}\n"], "file": "vector3.js"}