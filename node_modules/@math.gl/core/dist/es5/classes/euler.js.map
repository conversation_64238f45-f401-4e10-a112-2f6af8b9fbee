{"version": 3, "sources": ["../../../src/classes/euler.ts"], "names": ["ERR_UNKNOWN_ORDER", "ALMOST_ONE", "RotationOrder", "<PERSON>uler", "x", "y", "z", "order", "DefaultOrder", "arguments", "length", "Array", "isArray", "fromVector3", "set", "quaternion", "w", "ysqr", "t0", "t1", "t2", "t3", "t4", "roll", "Math", "atan2", "pitch", "asin", "yaw", "RollPitchYaw", "object", "Error", "array", "Number", "isFinite", "check", "validateOrder", "offset", "result", "value", "checkOrder", "v", "undefined", "ZYX", "m", "_fromRotationMatrix", "_getRotationMatrix", "q", "Quaternion", "XYZ", "rotateX", "rotateY", "rotateZ", "YXZ", "ZXY", "YZX", "XZY", "m11", "m12", "m13", "m21", "m22", "m23", "m31", "m32", "m33", "abs", "te", "a", "cos", "c", "e", "b", "sin", "d", "f", "ae", "af", "be", "bf", "ce", "cf", "de", "df", "ac", "ad", "bc", "bd", "cy", "sy", "cr", "sr", "cp", "sp", "MathArray"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAEA;;AACA;;AAGA;;AACA;;;;;;AAGA,IAAMA,iBAAiB,GAAG,2BAA1B;AACA,IAAMC,UAAU,GAAG,OAAnB;IAEKC,a;;WAAAA,a;AAAAA,EAAAA,a,CAAAA,a;AAAAA,EAAAA,a,CAAAA,a;AAAAA,EAAAA,a,CAAAA,a;AAAAA,EAAAA,a,CAAAA,a;AAAAA,EAAAA,a,CAAAA,a;AAAAA,EAAAA,a,CAAAA,a;GAAAA,a,KAAAA,a;;IASgBC,K;;;;;AA2CnB,mBAA6D;AAAA;;AAAA,QAAjDC,CAAiD,uEAA7C,CAA6C;AAAA,QAA1CC,CAA0C,uEAAtC,CAAsC;AAAA,QAAnCC,CAAmC,uEAA/B,CAA+B;AAAA,QAA5BC,KAA4B,uEAApBJ,KAAK,CAACK,YAAc;AAAA;AAE3D,8BAAM,CAAC,CAAP,EAAU,CAAC,CAAX,EAAc,CAAC,CAAf,EAAkB,CAAC,CAAnB;;AAEA,QAAIC,SAAS,CAACC,MAAV,GAAmB,CAAnB,IAAwBC,KAAK,CAACC,OAAN,CAAcH,SAAS,CAAC,CAAD,CAAvB,CAA5B,EAAyD;AAAA;;AAGvD,uBAAKI,WAAL,eAAoBJ,SAApB;AACD,KAJD,MAIO;AACL,YAAKK,GAAL,CAASV,CAAT,EAAYC,CAAZ,EAAeC,CAAf,EAAkBC,KAAlB;AACD;;AAV0D;AAW5D;;;;SAtBD,eAAuB;AACrB,aAAO,CAAP;AACD;;;WAsBD,wBAAeQ,UAAf,EAAyD;AACvD,qDAAqBA,UAArB;AAAA,UAAOX,CAAP;AAAA,UAAUC,CAAV;AAAA,UAAaC,CAAb;AAAA,UAAgBU,CAAhB;;AACA,UAAMC,IAAI,GAAGZ,CAAC,GAAGA,CAAjB;AACA,UAAMa,EAAE,GAAG,CAAC,CAAD,IAAMD,IAAI,GAAGX,CAAC,GAAGA,CAAjB,IAAsB,CAAjC;AACA,UAAMa,EAAE,GAAG,CAAC,CAAD,IAAMf,CAAC,GAAGC,CAAJ,GAAQW,CAAC,GAAGV,CAAlB,CAAX;AACA,UAAIc,EAAE,GAAG,CAAC,CAAD,IAAMhB,CAAC,GAAGE,CAAJ,GAAQU,CAAC,GAAGX,CAAlB,CAAT;AACA,UAAMgB,EAAE,GAAG,CAAC,CAAD,IAAMhB,CAAC,GAAGC,CAAJ,GAAQU,CAAC,GAAGZ,CAAlB,CAAX;AACA,UAAMkB,EAAE,GAAG,CAAC,CAAD,IAAMlB,CAAC,GAAGA,CAAJ,GAAQa,IAAd,IAAsB,CAAjC;AACAG,MAAAA,EAAE,GAAGA,EAAE,GAAG,CAAL,GAAS,CAAT,GAAaA,EAAlB;AACAA,MAAAA,EAAE,GAAGA,EAAE,GAAG,CAAC,CAAN,GAAU,CAAC,CAAX,GAAeA,EAApB;AACA,UAAMG,IAAI,GAAGC,IAAI,CAACC,KAAL,CAAWJ,EAAX,EAAeC,EAAf,CAAb;AACA,UAAMI,KAAK,GAAGF,IAAI,CAACG,IAAL,CAAUP,EAAV,CAAd;AACA,UAAMQ,GAAG,GAAGJ,IAAI,CAACC,KAAL,CAAWN,EAAX,EAAeD,EAAf,CAAZ;AACA,aAAO,KAAKJ,GAAL,CAASS,IAAT,EAAeG,KAAf,EAAsBE,GAAtB,EAA2BzB,KAAK,CAAC0B,YAAjC,CAAP;AACD;;;WAED,oBAAWC,MAAX,EAAiC;AAC/B,YAAM,IAAIC,KAAJ,CAAU,iBAAV,CAAN;AAED;;;WAOD,cAAKC,KAAL,EAA0C;AACxC,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AACA,WAAK,CAAL,IAAUA,KAAK,CAAC,CAAD,CAAf;AAEA,WAAK,CAAL,IAAUC,MAAM,CAACC,QAAP,CAAgBF,KAAK,CAAC,CAAD,CAArB,KAA6B,KAAKzB,KAA5C;AACA,aAAO,KAAK4B,KAAL,EAAP;AACD;;;WAID,eAAqD;AAAA,UAAjD/B,CAAiD,uEAA7C,CAA6C;AAAA,UAA1CC,CAA0C,uEAAtC,CAAsC;AAAA,UAAnCC,CAAmC,uEAA/B,CAA+B;AAAA,UAA5BC,KAA4B;AACnD,WAAK,CAAL,IAAUH,CAAV;AACA,WAAK,CAAL,IAAUC,CAAV;AACA,WAAK,CAAL,IAAUC,CAAV;AACA,WAAK,CAAL,IAAU2B,MAAM,CAACC,QAAP,CAAgB3B,KAAhB,IAAyBA,KAAzB,GAAiC,KAAK,CAAL,CAA3C;AACA,aAAO,KAAK4B,KAAL,EAAP;AACD;;;WAED,oBAAoB;AAClB,aACEC,aAAa,CAAC,KAAK,CAAL,CAAD,CAAb,IACAH,MAAM,CAACC,QAAP,CAAgB,KAAK,CAAL,CAAhB,CADA,IAEAD,MAAM,CAACC,QAAP,CAAgB,KAAK,CAAL,CAAhB,CAFA,IAGAD,MAAM,CAACC,QAAP,CAAgB,KAAK,CAAL,CAAhB,CAJF;AAMD;;;WAGD,mBAAoE;AAAA,UAA5DF,KAA4D,uEAAtC,EAAsC;AAAA,UAAlCK,MAAkC,uEAAjB,CAAiB;AAClEL,MAAAA,KAAK,CAACK,MAAD,CAAL,GAAgB,KAAK,CAAL,CAAhB;AACAL,MAAAA,KAAK,CAACK,MAAM,GAAG,CAAV,CAAL,GAAoB,KAAK,CAAL,CAApB;AACAL,MAAAA,KAAK,CAACK,MAAM,GAAG,CAAV,CAAL,GAAoB,KAAK,CAAL,CAApB;AACA,aAAOL,KAAP;AACD;;;WAGD,oBAAqE;AAAA,UAA5DA,KAA4D,uEAAtC,EAAsC;AAAA,UAAlCK,MAAkC,uEAAjB,CAAiB;AACnEL,MAAAA,KAAK,CAACK,MAAD,CAAL,GAAgB,KAAK,CAAL,CAAhB;AACAL,MAAAA,KAAK,CAACK,MAAM,GAAG,CAAV,CAAL,GAAoB,KAAK,CAAL,CAApB;AACAL,MAAAA,KAAK,CAACK,MAAM,GAAG,CAAV,CAAL,GAAoB,KAAK,CAAL,CAApB;AACAL,MAAAA,KAAK,CAACK,MAAM,GAAG,CAAV,CAAL,GAAoB,KAAK,CAAL,CAApB;AACA,aAAOL,KAAP;AACD;;;WAED,qBAA6D;AAAA,UAAnDM,MAAmD,uEAA5B,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,EAAS,CAAC,CAAV,CAA4B;AAC3DA,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,CAAZ;AACAA,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,CAAZ;AACAA,MAAAA,MAAM,CAAC,CAAD,CAAN,GAAY,KAAK,CAAL,CAAZ;AACA,aAAOA,MAAP;AACD;;;SAID,eAAgB;AACd,aAAO,KAAK,CAAL,CAAP;AACD,K;SACD,aAAMC,KAAN,EAAqB;AACnB,WAAK,CAAL,IAAU,6BAAYA,KAAZ,CAAV;AACD;;;SAED,eAAgB;AACd,aAAO,KAAK,CAAL,CAAP;AACD,K;SACD,aAAMA,KAAN,EAAqB;AACnB,WAAK,CAAL,IAAU,6BAAYA,KAAZ,CAAV;AACD;;;SAED,eAAgB;AACd,aAAO,KAAK,CAAL,CAAP;AACD,K;SACD,aAAMA,KAAN,EAAqB;AACnB,WAAK,CAAL,IAAU,6BAAYA,KAAZ,CAAV;AACD;;;SAED,eAAoB;AAClB,aAAO,KAAK,CAAL,CAAP;AACD,K;SACD,aAAUA,KAAV,EAAyB;AACvB,WAAK,CAAL,IAAU,6BAAYA,KAAZ,CAAV;AACD;;;SAED,eAAmB;AACjB,aAAO,KAAK,CAAL,CAAP;AACD,K;SACD,aAASA,KAAT,EAAwB;AACtB,WAAK,CAAL,IAAU,6BAAYA,KAAZ,CAAV;AACD;;;SAED,eAAoB;AAClB,aAAO,KAAK,CAAL,CAAP;AACD,K;SACD,aAAUA,KAAV,EAAyB;AACvB,WAAK,CAAL,IAAU,6BAAYA,KAAZ,CAAV;AACD;;;SAGD,eAAkB;AAChB,aAAO,KAAK,CAAL,CAAP;AACD,K;SACD,aAAQA,KAAR,EAAuB;AACrB,WAAK,CAAL,IAAU,6BAAYA,KAAZ,CAAV;AACD;;;SAED,eAAoB;AAClB,aAAO,KAAK,CAAL,CAAP;AACD,K;SACD,aAAUA,KAAV,EAAyB;AACvB,WAAK,CAAL,IAAU,6BAAYA,KAAZ,CAAV;AACD;;;SAED,eAAkB;AAChB,aAAO,KAAK,CAAL,CAAP;AACD,K;SACD,aAAQA,KAAR,EAAuB;AACrB,WAAK,CAAL,IAAU,6BAAYA,KAAZ,CAAV;AACD;;;SAGD,eAAmB;AACjB,aAAO,KAAK,CAAL,CAAP;AACD,K;SACD,aAASA,KAAT,EAAwB;AACtB,WAAK,CAAL,IAAU,6BAAYA,KAAZ,CAAV;AACD;;;SAED,eAAoB;AAClB,aAAO,KAAK,CAAL,CAAP;AACD,K;SACD,aAAUA,KAAV,EAAyB;AACvB,WAAK,CAAL,IAAU,6BAAYA,KAAZ,CAAV;AACD;;;SAED,eAAkB;AAChB,aAAO,KAAK,CAAL,CAAP;AACD,K;SACD,aAAQA,KAAR,EAAuB;AACrB,WAAK,CAAL,IAAU,6BAAYA,KAAZ,CAAV;AACD;;;SAGD,eAA2B;AACzB,aAAO,KAAK,CAAL,CAAP;AACD,K;SACD,aAAUA,KAAV,EAAgC;AAC9B,WAAK,CAAL,IAAUC,UAAU,CAACD,KAAD,CAApB;AACD;;;WAGD,qBAAYE,CAAZ,EAAuClC,KAAvC,EAAmE;AACjE,aAAO,KAAKO,GAAL,CAAS2B,CAAC,CAAC,CAAD,CAAV,EAAeA,CAAC,CAAC,CAAD,CAAhB,EAAqBA,CAAC,CAAC,CAAD,CAAtB,EAA2BR,MAAM,CAACC,QAAP,CAAgB3B,KAAhB,IAAyBA,KAAzB,GAAiC,KAAK,CAAL,CAA5D,CAAP;AACD;;;WAGD,mBAAUyB,KAAV,EAAmE;AAAA,UAA1BK,MAA0B,uEAAT,CAAS;AACjE,WAAK,CAAL,IAAUL,KAAK,CAAC,IAAIK,MAAL,CAAf;AACA,WAAK,CAAL,IAAUL,KAAK,CAAC,IAAIK,MAAL,CAAf;AACA,WAAK,CAAL,IAAUL,KAAK,CAAC,IAAIK,MAAL,CAAf;;AACA,UAAIL,KAAK,CAAC,CAAD,CAAL,KAAaU,SAAjB,EAA4B;AAC1B,aAAK,CAAL,IAAUV,KAAK,CAAC,CAAD,CAAf;AACD;;AACD,aAAO,KAAKG,KAAL,EAAP;AACD;;;WAGD,0BAAiBZ,IAAjB,EAA+BG,KAA/B,EAA8CE,GAA9C,EAAiE;AAC/D,aAAO,KAAKd,GAAL,CAASS,IAAT,EAAeG,KAAf,EAAsBE,GAAtB,EAA2B1B,aAAa,CAACyC,GAAzC,CAAP;AACD;;;WAED,4BAAmBC,CAAnB,EAA+F;AAAA,UAAjDrC,KAAiD,uEAA1BJ,KAAK,CAACK,YAAoB;;AAC7F,WAAKqC,mBAAL,CAAyBD,CAAzB,EAA4BrC,KAA5B;;AACA,aAAO,KAAK4B,KAAL,EAAP;AACD;;;WAID,2BAAkBS,CAAlB,EAAiD;AAC/C,aAAO,KAAKE,kBAAL,CAAwBF,CAAxB,CAAP;AACD;;;WAGD,yBAA4B;AAC1B,UAAMG,CAAC,GAAG,IAAIC,oBAAJ,EAAV;;AACA,cAAQ,KAAK,CAAL,CAAR;AACE,aAAK9C,aAAa,CAAC+C,GAAnB;AACE,iBAAOF,CAAC,CAACG,OAAF,CAAU,KAAK,CAAL,CAAV,EAAmBC,OAAnB,CAA2B,KAAK,CAAL,CAA3B,EAAoCC,OAApC,CAA4C,KAAK,CAAL,CAA5C,CAAP;;AACF,aAAKlD,aAAa,CAACmD,GAAnB;AACE,iBAAON,CAAC,CAACI,OAAF,CAAU,KAAK,CAAL,CAAV,EAAmBD,OAAnB,CAA2B,KAAK,CAAL,CAA3B,EAAoCE,OAApC,CAA4C,KAAK,CAAL,CAA5C,CAAP;;AACF,aAAKlD,aAAa,CAACoD,GAAnB;AACE,iBAAOP,CAAC,CAACK,OAAF,CAAU,KAAK,CAAL,CAAV,EAAmBF,OAAnB,CAA2B,KAAK,CAAL,CAA3B,EAAoCC,OAApC,CAA4C,KAAK,CAAL,CAA5C,CAAP;;AACF,aAAKjD,aAAa,CAACyC,GAAnB;AACE,iBAAOI,CAAC,CAACK,OAAF,CAAU,KAAK,CAAL,CAAV,EAAmBD,OAAnB,CAA2B,KAAK,CAAL,CAA3B,EAAoCD,OAApC,CAA4C,KAAK,CAAL,CAA5C,CAAP;;AACF,aAAKhD,aAAa,CAACqD,GAAnB;AACE,iBAAOR,CAAC,CAACI,OAAF,CAAU,KAAK,CAAL,CAAV,EAAmBC,OAAnB,CAA2B,KAAK,CAAL,CAA3B,EAAoCF,OAApC,CAA4C,KAAK,CAAL,CAA5C,CAAP;;AACF,aAAKhD,aAAa,CAACsD,GAAnB;AACE,iBAAOT,CAAC,CAACG,OAAF,CAAU,KAAK,CAAL,CAAV,EAAmBE,OAAnB,CAA2B,KAAK,CAAL,CAA3B,EAAoCD,OAApC,CAA4C,KAAK,CAAL,CAA5C,CAAP;;AACF;AACE,gBAAM,IAAIpB,KAAJ,CAAU/B,iBAAV,CAAN;AAdJ;AAgBD;;;WAUD,6BAAoB4C,CAApB,EAAiF;AAAA,UAAlCrC,KAAkC,uEAA1BJ,KAAK,CAACK,YAAoB;AAE/E,UAAMiD,GAAG,GAAGb,CAAC,CAAC,CAAD,CAAb;AAAA,UACEc,GAAG,GAAGd,CAAC,CAAC,CAAD,CADT;AAAA,UAEEe,GAAG,GAAGf,CAAC,CAAC,CAAD,CAFT;AAGA,UAAMgB,GAAG,GAAGhB,CAAC,CAAC,CAAD,CAAb;AAAA,UACEiB,GAAG,GAAGjB,CAAC,CAAC,CAAD,CADT;AAAA,UAEEkB,GAAG,GAAGlB,CAAC,CAAC,CAAD,CAFT;AAGA,UAAMmB,GAAG,GAAGnB,CAAC,CAAC,CAAD,CAAb;AAAA,UACEoB,GAAG,GAAGpB,CAAC,CAAC,CAAD,CADT;AAAA,UAEEqB,GAAG,GAAGrB,CAAC,CAAC,EAAD,CAFT;AAGArC,MAAAA,KAAK,GAAGA,KAAK,IAAI,KAAK,CAAL,CAAjB;;AACA,cAAQA,KAAR;AACE,aAAKJ,KAAK,CAAC8C,GAAX;AACE,eAAK,CAAL,IAAUzB,IAAI,CAACG,IAAL,CAAU,mBAAMgC,GAAN,EAAW,CAAC,CAAZ,EAAe,CAAf,CAAV,CAAV;;AACA,cAAInC,IAAI,CAAC0C,GAAL,CAASP,GAAT,IAAgB1D,UAApB,EAAgC;AAC9B,iBAAK,CAAL,IAAUuB,IAAI,CAACC,KAAL,CAAW,CAACqC,GAAZ,EAAiBG,GAAjB,CAAV;AACA,iBAAK,CAAL,IAAUzC,IAAI,CAACC,KAAL,CAAW,CAACiC,GAAZ,EAAiBD,GAAjB,CAAV;AACD,WAHD,MAGO;AACL,iBAAK,CAAL,IAAUjC,IAAI,CAACC,KAAL,CAAWuC,GAAX,EAAgBH,GAAhB,CAAV;AACA,iBAAK,CAAL,IAAU,CAAV;AACD;;AACD;;AACF,aAAK1D,KAAK,CAACkD,GAAX;AACE,eAAK,CAAL,IAAU7B,IAAI,CAACG,IAAL,CAAU,CAAC,mBAAMmC,GAAN,EAAW,CAAC,CAAZ,EAAe,CAAf,CAAX,CAAV;;AACA,cAAItC,IAAI,CAAC0C,GAAL,CAASJ,GAAT,IAAgB7D,UAApB,EAAgC;AAC9B,iBAAK,CAAL,IAAUuB,IAAI,CAACC,KAAL,CAAWkC,GAAX,EAAgBM,GAAhB,CAAV;AACA,iBAAK,CAAL,IAAUzC,IAAI,CAACC,KAAL,CAAWmC,GAAX,EAAgBC,GAAhB,CAAV;AACD,WAHD,MAGO;AACL,iBAAK,CAAL,IAAUrC,IAAI,CAACC,KAAL,CAAW,CAACsC,GAAZ,EAAiBN,GAAjB,CAAV;AACA,iBAAK,CAAL,IAAU,CAAV;AACD;;AACD;;AACF,aAAKtD,KAAK,CAACmD,GAAX;AACE,eAAK,CAAL,IAAU9B,IAAI,CAACG,IAAL,CAAU,mBAAMqC,GAAN,EAAW,CAAC,CAAZ,EAAe,CAAf,CAAV,CAAV;;AACA,cAAIxC,IAAI,CAAC0C,GAAL,CAASF,GAAT,IAAgB/D,UAApB,EAAgC;AAC9B,iBAAK,CAAL,IAAUuB,IAAI,CAACC,KAAL,CAAW,CAACsC,GAAZ,EAAiBE,GAAjB,CAAV;AACA,iBAAK,CAAL,IAAUzC,IAAI,CAACC,KAAL,CAAW,CAACiC,GAAZ,EAAiBG,GAAjB,CAAV;AACD,WAHD,MAGO;AACL,iBAAK,CAAL,IAAU,CAAV;AACA,iBAAK,CAAL,IAAUrC,IAAI,CAACC,KAAL,CAAWmC,GAAX,EAAgBH,GAAhB,CAAV;AACD;;AACD;;AACF,aAAKtD,KAAK,CAACwC,GAAX;AACE,eAAK,CAAL,IAAUnB,IAAI,CAACG,IAAL,CAAU,CAAC,mBAAMoC,GAAN,EAAW,CAAC,CAAZ,EAAe,CAAf,CAAX,CAAV;;AACA,cAAIvC,IAAI,CAAC0C,GAAL,CAASH,GAAT,IAAgB9D,UAApB,EAAgC;AAC9B,iBAAK,CAAL,IAAUuB,IAAI,CAACC,KAAL,CAAWuC,GAAX,EAAgBC,GAAhB,CAAV;AACA,iBAAK,CAAL,IAAUzC,IAAI,CAACC,KAAL,CAAWmC,GAAX,EAAgBH,GAAhB,CAAV;AACD,WAHD,MAGO;AACL,iBAAK,CAAL,IAAU,CAAV;AACA,iBAAK,CAAL,IAAUjC,IAAI,CAACC,KAAL,CAAW,CAACiC,GAAZ,EAAiBG,GAAjB,CAAV;AACD;;AACD;;AACF,aAAK1D,KAAK,CAACoD,GAAX;AACE,eAAK,CAAL,IAAU/B,IAAI,CAACG,IAAL,CAAU,mBAAMiC,GAAN,EAAW,CAAC,CAAZ,EAAe,CAAf,CAAV,CAAV;;AACA,cAAIpC,IAAI,CAAC0C,GAAL,CAASN,GAAT,IAAgB3D,UAApB,EAAgC;AAC9B,iBAAK,CAAL,IAAUuB,IAAI,CAACC,KAAL,CAAW,CAACqC,GAAZ,EAAiBD,GAAjB,CAAV;AACA,iBAAK,CAAL,IAAUrC,IAAI,CAACC,KAAL,CAAW,CAACsC,GAAZ,EAAiBN,GAAjB,CAAV;AACD,WAHD,MAGO;AACL,iBAAK,CAAL,IAAU,CAAV;AACA,iBAAK,CAAL,IAAUjC,IAAI,CAACC,KAAL,CAAWkC,GAAX,EAAgBM,GAAhB,CAAV;AACD;;AACD;;AACF,aAAK9D,KAAK,CAACqD,GAAX;AACE,eAAK,CAAL,IAAUhC,IAAI,CAACG,IAAL,CAAU,CAAC,mBAAM+B,GAAN,EAAW,CAAC,CAAZ,EAAe,CAAf,CAAX,CAAV;;AACA,cAAIlC,IAAI,CAAC0C,GAAL,CAASR,GAAT,IAAgBzD,UAApB,EAAgC;AAC9B,iBAAK,CAAL,IAAUuB,IAAI,CAACC,KAAL,CAAWuC,GAAX,EAAgBH,GAAhB,CAAV;AACA,iBAAK,CAAL,IAAUrC,IAAI,CAACC,KAAL,CAAWkC,GAAX,EAAgBF,GAAhB,CAAV;AACD,WAHD,MAGO;AACL,iBAAK,CAAL,IAAUjC,IAAI,CAACC,KAAL,CAAW,CAACqC,GAAZ,EAAiBG,GAAjB,CAAV;AACA,iBAAK,CAAL,IAAU,CAAV;AACD;;AACD;;AACF;AACE,gBAAM,IAAIlC,KAAJ,CAAU/B,iBAAV,CAAN;AA9DJ;;AAgEA,WAAK,CAAL,IAAUO,KAAV;AACA,aAAO,IAAP;AACD;;;WAED,4BAAmB+B,MAAnB,EAAuD;AACrD,UAAM6B,EAAE,GAAG7B,MAAM,IAAI,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN,EAAS,CAAC,CAAV,EAAa,CAAC,CAAd,EAAiB,CAAC,CAAlB,EAAqB,CAAC,CAAtB,EAAyB,CAAC,CAA1B,EAA6B,CAAC,CAA9B,EAAiC,CAAC,CAAlC,EAAqC,CAAC,CAAtC,EAAyC,CAAC,CAA1C,EAA6C,CAAC,CAA9C,EAAiD,CAAC,CAAlD,EAAqD,CAAC,CAAtD,EAAyD,CAAC,CAA1D,EAA6D,CAAC,CAA9D,CAArB;AACA,UAAMlC,CAAC,GAAG,KAAKA,CAAf;AAAA,UACEC,CAAC,GAAG,KAAKA,CADX;AAAA,UAEEC,CAAC,GAAG,KAAKA,CAFX;AAGA,UAAM8D,CAAC,GAAG5C,IAAI,CAAC6C,GAAL,CAASjE,CAAT,CAAV;AACA,UAAMkE,CAAC,GAAG9C,IAAI,CAAC6C,GAAL,CAAShE,CAAT,CAAV;AACA,UAAMkE,CAAC,GAAG/C,IAAI,CAAC6C,GAAL,CAAS/D,CAAT,CAAV;AACA,UAAMkE,CAAC,GAAGhD,IAAI,CAACiD,GAAL,CAASrE,CAAT,CAAV;AACA,UAAMsE,CAAC,GAAGlD,IAAI,CAACiD,GAAL,CAASpE,CAAT,CAAV;AACA,UAAMsE,CAAC,GAAGnD,IAAI,CAACiD,GAAL,CAASnE,CAAT,CAAV;;AACA,cAAQ,KAAK,CAAL,CAAR;AACE,aAAKH,KAAK,CAAC8C,GAAX;AAAgB;AACd,gBAAM2B,EAAE,GAAGR,CAAC,GAAGG,CAAf;AAAA,gBACEM,EAAE,GAAGT,CAAC,GAAGO,CADX;AAAA,gBAEEG,EAAE,GAAGN,CAAC,GAAGD,CAFX;AAAA,gBAGEQ,EAAE,GAAGP,CAAC,GAAGG,CAHX;AAIAR,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQG,CAAC,GAAGC,CAAZ;AACAJ,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQ,CAACG,CAAD,GAAKK,CAAb;AACAR,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQO,CAAR;AACAP,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQU,EAAE,GAAGC,EAAE,GAAGJ,CAAlB;AACAP,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQS,EAAE,GAAGG,EAAE,GAAGL,CAAlB;AACAP,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQ,CAACK,CAAD,GAAKF,CAAb;AACAH,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQY,EAAE,GAAGH,EAAE,GAAGF,CAAlB;AACAP,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQW,EAAE,GAAGD,EAAE,GAAGH,CAAlB;AACAP,YAAAA,EAAE,CAAC,EAAD,CAAF,GAASC,CAAC,GAAGE,CAAb;AACA;AACD;;AACD,aAAKnE,KAAK,CAACkD,GAAX;AAAgB;AACd,gBAAM2B,EAAE,GAAGV,CAAC,GAAGC,CAAf;AAAA,gBACEU,EAAE,GAAGX,CAAC,GAAGK,CADX;AAAA,gBAEEO,EAAE,GAAGR,CAAC,GAAGH,CAFX;AAAA,gBAGEY,EAAE,GAAGT,CAAC,GAAGC,CAHX;AAIAR,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQa,EAAE,GAAGG,EAAE,GAAGX,CAAlB;AACAL,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQe,EAAE,GAAGV,CAAL,GAASS,EAAjB;AACAd,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQC,CAAC,GAAGM,CAAZ;AACAP,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQC,CAAC,GAAGO,CAAZ;AACAR,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQC,CAAC,GAAGG,CAAZ;AACAJ,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQ,CAACK,CAAT;AACAL,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQc,EAAE,GAAGT,CAAL,GAASU,EAAjB;AACAf,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQgB,EAAE,GAAGH,EAAE,GAAGR,CAAlB;AACAL,YAAAA,EAAE,CAAC,EAAD,CAAF,GAASC,CAAC,GAAGE,CAAb;AACA;AACD;;AACD,aAAKnE,KAAK,CAACmD,GAAX;AAAgB;AACd,gBAAM0B,GAAE,GAAGV,CAAC,GAAGC,CAAf;AAAA,gBACEU,GAAE,GAAGX,CAAC,GAAGK,CADX;AAAA,gBAEEO,GAAE,GAAGR,CAAC,GAAGH,CAFX;AAAA,gBAGEY,GAAE,GAAGT,CAAC,GAAGC,CAHX;;AAIAR,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQa,GAAE,GAAGG,GAAE,GAAGX,CAAlB;AACAL,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQ,CAACC,CAAD,GAAKO,CAAb;AACAR,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQe,GAAE,GAAGD,GAAE,GAAGT,CAAlB;AACAL,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQc,GAAE,GAAGC,GAAE,GAAGV,CAAlB;AACAL,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQC,CAAC,GAAGG,CAAZ;AACAJ,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQgB,GAAE,GAAGH,GAAE,GAAGR,CAAlB;AACAL,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQ,CAACC,CAAD,GAAKM,CAAb;AACAP,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQK,CAAR;AACAL,YAAAA,EAAE,CAAC,EAAD,CAAF,GAASC,CAAC,GAAGE,CAAb;AACA;AACD;;AACD,aAAKnE,KAAK,CAACwC,GAAX;AAAgB;AACd,gBAAMiC,GAAE,GAAGR,CAAC,GAAGG,CAAf;AAAA,gBACEM,GAAE,GAAGT,CAAC,GAAGO,CADX;AAAA,gBAEEG,GAAE,GAAGN,CAAC,GAAGD,CAFX;AAAA,gBAGEQ,GAAE,GAAGP,CAAC,GAAGG,CAHX;;AAIAR,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQG,CAAC,GAAGC,CAAZ;AACAJ,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQW,GAAE,GAAGJ,CAAL,GAASG,GAAjB;AACAV,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQS,GAAE,GAAGF,CAAL,GAASK,GAAjB;AACAZ,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQG,CAAC,GAAGK,CAAZ;AACAR,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQY,GAAE,GAAGL,CAAL,GAASE,GAAjB;AACAT,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQU,GAAE,GAAGH,CAAL,GAASI,GAAjB;AACAX,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQ,CAACO,CAAT;AACAP,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQK,CAAC,GAAGF,CAAZ;AACAH,YAAAA,EAAE,CAAC,EAAD,CAAF,GAASC,CAAC,GAAGE,CAAb;AACA;AACD;;AACD,aAAKnE,KAAK,CAACoD,GAAX;AAAgB;AACd,gBAAM6B,EAAE,GAAGhB,CAAC,GAAGE,CAAf;AAAA,gBACEe,EAAE,GAAGjB,CAAC,GAAGM,CADX;AAAA,gBAEEY,EAAE,GAAGd,CAAC,GAAGF,CAFX;AAAA,gBAGEiB,EAAE,GAAGf,CAAC,GAAGE,CAHX;AAIAP,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQG,CAAC,GAAGC,CAAZ;AACAJ,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQoB,EAAE,GAAGH,EAAE,GAAGT,CAAlB;AACAR,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQmB,EAAE,GAAGX,CAAL,GAASU,EAAjB;AACAlB,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQQ,CAAR;AACAR,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQC,CAAC,GAAGG,CAAZ;AACAJ,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQ,CAACK,CAAD,GAAKD,CAAb;AACAJ,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQ,CAACO,CAAD,GAAKH,CAAb;AACAJ,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQkB,EAAE,GAAGV,CAAL,GAASW,EAAjB;AACAnB,YAAAA,EAAE,CAAC,EAAD,CAAF,GAASiB,EAAE,GAAGG,EAAE,GAAGZ,CAAnB;AACA;AACD;;AACD,aAAKxE,KAAK,CAACqD,GAAX;AAAgB;AACd,gBAAM4B,GAAE,GAAGhB,CAAC,GAAGE,CAAf;AAAA,gBACEe,GAAE,GAAGjB,CAAC,GAAGM,CADX;AAAA,gBAEEY,GAAE,GAAGd,CAAC,GAAGF,CAFX;AAAA,gBAGEiB,GAAE,GAAGf,CAAC,GAAGE,CAHX;;AAIAP,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQG,CAAC,GAAGC,CAAZ;AACAJ,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQ,CAACQ,CAAT;AACAR,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQO,CAAC,GAAGH,CAAZ;AACAJ,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQiB,GAAE,GAAGT,CAAL,GAASY,GAAjB;AACApB,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQC,CAAC,GAAGG,CAAZ;AACAJ,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQkB,GAAE,GAAGV,CAAL,GAASW,GAAjB;AACAnB,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQmB,GAAE,GAAGX,CAAL,GAASU,GAAjB;AACAlB,YAAAA,EAAE,CAAC,CAAD,CAAF,GAAQK,CAAC,GAAGD,CAAZ;AACAJ,YAAAA,EAAE,CAAC,EAAD,CAAF,GAASoB,GAAE,GAAGZ,CAAL,GAASS,GAAlB;AACA;AACD;;AACD;AACE,gBAAM,IAAIrD,KAAJ,CAAU/B,iBAAV,CAAN;AAlGJ;;AAqGAmE,MAAAA,EAAE,CAAC,CAAD,CAAF,GAAQ,CAAR;AACAA,MAAAA,EAAE,CAAC,CAAD,CAAF,GAAQ,CAAR;AACAA,MAAAA,EAAE,CAAC,EAAD,CAAF,GAAS,CAAT;AAEAA,MAAAA,EAAE,CAAC,EAAD,CAAF,GAAS,CAAT;AACAA,MAAAA,EAAE,CAAC,EAAD,CAAF,GAAS,CAAT;AACAA,MAAAA,EAAE,CAAC,EAAD,CAAF,GAAS,CAAT;AACAA,MAAAA,EAAE,CAAC,EAAD,CAAF,GAAS,CAAT;AACA,aAAOA,EAAP;AACD;;;WAED,wBAA2B;AAEzB,UAAMqB,EAAE,GAAGhE,IAAI,CAAC6C,GAAL,CAAS,KAAKzC,GAAL,GAAW,GAApB,CAAX;AACA,UAAM6D,EAAE,GAAGjE,IAAI,CAACiD,GAAL,CAAS,KAAK7C,GAAL,GAAW,GAApB,CAAX;AACA,UAAM8D,EAAE,GAAGlE,IAAI,CAAC6C,GAAL,CAAS,KAAK9C,IAAL,GAAY,GAArB,CAAX;AACA,UAAMoE,EAAE,GAAGnE,IAAI,CAACiD,GAAL,CAAS,KAAKlD,IAAL,GAAY,GAArB,CAAX;AACA,UAAMqE,EAAE,GAAGpE,IAAI,CAAC6C,GAAL,CAAS,KAAK3C,KAAL,GAAa,GAAtB,CAAX;AACA,UAAMmE,EAAE,GAAGrE,IAAI,CAACiD,GAAL,CAAS,KAAK/C,KAAL,GAAa,GAAtB,CAAX;AACA,UAAMV,CAAC,GAAGwE,EAAE,GAAGE,EAAL,GAAUE,EAAV,GAAeH,EAAE,GAAGE,EAAL,GAAUE,EAAnC;AACA,UAAMzF,CAAC,GAAGoF,EAAE,GAAGG,EAAL,GAAUC,EAAV,GAAeH,EAAE,GAAGC,EAAL,GAAUG,EAAnC;AACA,UAAMxF,CAAC,GAAGmF,EAAE,GAAGE,EAAL,GAAUG,EAAV,GAAeJ,EAAE,GAAGE,EAAL,GAAUC,EAAnC;AACA,UAAMtF,CAAC,GAAGmF,EAAE,GAAGC,EAAL,GAAUE,EAAV,GAAeJ,EAAE,GAAGG,EAAL,GAAUE,EAAnC;AACA,aAAO,IAAI7C,oBAAJ,CAAe5C,CAAf,EAAkBC,CAAlB,EAAqBC,CAArB,EAAwBU,CAAxB,CAAP;AACD;;;SAzfD,eAAgC;AAC9B,aAAOd,aAAa,CAACyC,GAArB;AACD;;;SACD,eAAgC;AAC9B,aAAOzC,aAAa,CAACmD,GAArB;AACD;;;SACD,eAAgC;AAC9B,aAAOnD,aAAa,CAACsD,GAArB;AACD;;;SACD,eAAgC;AAC9B,aAAOtD,aAAa,CAACoD,GAArB;AACD;;;SACD,eAAgC;AAC9B,aAAOpD,aAAa,CAACqD,GAArB;AACD;;;SACD,eAAgC;AAC9B,aAAOrD,aAAa,CAAC+C,GAArB;AACD;;;SACD,eAAyC;AACvC,aAAO/C,aAAa,CAACyC,GAArB;AACD;;;SACD,eAAyC;AACvC,aAAOzC,aAAa,CAACyC,GAArB;AACD;;;SACD,eAAkD;AAChD,aAAOzC,aAAP;AACD;;;WACD,uBAAqBK,KAArB,EAAmD;AACjD,aAAOL,aAAa,CAACK,KAAD,CAApB;AACD;;;EA/BgCuF,kB;;;;AAggBnC,SAAS1D,aAAT,CAAuBG,KAAvB,EAA+C;AAC7C,SAAOA,KAAK,IAAI,CAAT,IAAcA,KAAK,GAAG,CAA7B;AACD;;AAED,SAASC,UAAT,CAAoBD,KAApB,EAAmC;AACjC,MAAIA,KAAK,GAAG,CAAR,IAAaA,KAAK,IAAI,CAA1B,EAA6B;AAC3B,UAAM,IAAIR,KAAJ,CAAU/B,iBAAV,CAAN;AACD;;AACD,SAAOuC,KAAP;AACD", "sourcesContent": ["// Copyright (c) 2017 Uber Technologies, Inc.\n// MIT License\nimport MathArray from './base/math-array';\nimport Quaternion from './quaternion';\nimport {NumericArray} from '@math.gl/types';\n\nimport {clamp} from '../lib/common';\nimport {checkNumber} from '../lib/validators';\n\n// Internal constants\nconst ERR_UNKNOWN_ORDER = 'Unknown Euler angle order';\nconst ALMOST_ONE = 0.99999;\n\nenum RotationOrder {\n  ZYX = 0,\n  YXZ = 1,\n  XZY = 2,\n  ZXY = 3,\n  YZX = 4,\n  XYZ = 5\n}\n\nexport default class Euler extends MathArray {\n  // Constants\n  static get ZYX(): RotationOrder {\n    return RotationOrder.ZYX;\n  }\n  static get YXZ(): RotationOrder {\n    return RotationOrder.YXZ;\n  }\n  static get XZY(): RotationOrder {\n    return RotationOrder.XZY;\n  }\n  static get ZXY(): RotationOrder {\n    return RotationOrder.ZXY;\n  }\n  static get YZX(): RotationOrder {\n    return RotationOrder.YZX;\n  }\n  static get XYZ(): RotationOrder {\n    return RotationOrder.XYZ;\n  }\n  static get RollPitchYaw(): RotationOrder {\n    return RotationOrder.ZYX;\n  }\n  static get DefaultOrder(): RotationOrder {\n    return RotationOrder.ZYX;\n  }\n  static get RotationOrders(): typeof RotationOrder {\n    return RotationOrder;\n  }\n  static rotationOrder(order: RotationOrder): string {\n    return RotationOrder[order];\n  }\n  get ELEMENTS(): number {\n    return 4;\n  }\n\n  /**\n   * @class\n   * @param {Number | Number[]} x\n   * @param {Number=} [y]\n   * @param {Number=} [z]\n   * @param {Number=} [order]\n   */\n  constructor(x = 0, y = 0, z = 0, order = Euler.DefaultOrder) {\n    // PERF NOTE: initialize elements as double precision numbers\n    super(-0, -0, -0, -0);\n    // eslint-disable-next-line prefer-rest-params\n    if (arguments.length > 0 && Array.isArray(arguments[0])) {\n      // eslint-disable-next-line prefer-rest-params\n      // @ts-expect-error\n      this.fromVector3(...arguments);\n    } else {\n      this.set(x, y, z, order);\n    }\n  }\n\n  fromQuaternion(quaternion: Readonly<NumericArray>): this {\n    const [x, y, z, w] = quaternion;\n    const ysqr = y * y;\n    const t0 = -2 * (ysqr + z * z) + 1;\n    const t1 = +2 * (x * y + w * z);\n    let t2 = -2 * (x * z - w * y);\n    const t3 = +2 * (y * z + w * x);\n    const t4 = -2 * (x * x + ysqr) + 1;\n    t2 = t2 > 1 ? 1 : t2;\n    t2 = t2 < -1 ? -1 : t2;\n    const roll = Math.atan2(t3, t4);\n    const pitch = Math.asin(t2);\n    const yaw = Math.atan2(t1, t0);\n    return this.set(roll, pitch, yaw, Euler.RollPitchYaw);\n  }\n\n  fromObject(object: object): this {\n    throw new Error('not implemented');\n    //  return this.set(object.x, object.y, object.z, object.order);\n  }\n\n  // fromQuaternion(q, order) {\n  //   this._fromRotationMat[-0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0];\n  //   return this.check();\n  // }\n  // If copied array does contain fourth element, preserves currently set order\n  copy(array: Readonly<NumericArray>): this {\n    this[0] = array[0];\n    this[1] = array[1];\n    this[2] = array[2];\n    // @ts-expect-error\n    this[3] = Number.isFinite(array[3]) || this.order;\n    return this.check();\n  }\n\n  // Sets the three angles, and optionally sets the rotation order\n  // If order is not specified, preserves currently set order\n  set(x = 0, y = 0, z = 0, order: RotationOrder): this {\n    this[0] = x;\n    this[1] = y;\n    this[2] = z;\n    this[3] = Number.isFinite(order) ? order : this[3];\n    return this.check();\n  }\n\n  validate(): boolean {\n    return (\n      validateOrder(this[3]) &&\n      Number.isFinite(this[0]) &&\n      Number.isFinite(this[1]) &&\n      Number.isFinite(this[2])\n    );\n  }\n\n  // Does not copy the orientation element\n  toArray(array: NumericArray = [], offset: number = 0): NumericArray {\n    array[offset] = this[0];\n    array[offset + 1] = this[1];\n    array[offset + 2] = this[2];\n    return array;\n  }\n\n  // Copies the orientation element\n  toArray4(array: NumericArray = [], offset: number = 0): NumericArray {\n    array[offset] = this[0];\n    array[offset + 1] = this[1];\n    array[offset + 2] = this[2];\n    array[offset + 3] = this[3];\n    return array;\n  }\n\n  toVector3(result: NumericArray = [-0, -0, -0]): NumericArray {\n    result[0] = this[0];\n    result[1] = this[1];\n    result[2] = this[2];\n    return result;\n  }\n  /* eslint-disable no-multi-spaces, brace-style, no-return-assign */\n  // x, y, z angle notation (note: only corresponds to axis in XYZ orientation)\n\n  get x(): number {\n    return this[0];\n  }\n  set x(value: number) {\n    this[0] = checkNumber(value);\n  }\n\n  get y(): number {\n    return this[1];\n  }\n  set y(value: number) {\n    this[1] = checkNumber(value);\n  }\n\n  get z(): number {\n    return this[2];\n  }\n  set z(value: number) {\n    this[2] = checkNumber(value);\n  }\n  // alpha, beta, gamma angle notation\n  get alpha(): number {\n    return this[0];\n  }\n  set alpha(value: number) {\n    this[0] = checkNumber(value);\n  }\n\n  get beta(): number {\n    return this[1];\n  }\n  set beta(value: number) {\n    this[1] = checkNumber(value);\n  }\n\n  get gamma(): number {\n    return this[2];\n  }\n  set gamma(value: number) {\n    this[2] = checkNumber(value);\n  }\n\n  // phi, theta, psi angle notation\n  get phi(): number {\n    return this[0];\n  }\n  set phi(value: number) {\n    this[0] = checkNumber(value);\n  }\n\n  get theta(): number {\n    return this[1];\n  }\n  set theta(value: number) {\n    this[1] = checkNumber(value);\n  }\n\n  get psi(): number {\n    return this[2];\n  }\n  set psi(value: number) {\n    this[2] = checkNumber(value);\n  }\n  // roll, pitch, yaw angle notation\n\n  get roll(): number {\n    return this[0];\n  }\n  set roll(value: number) {\n    this[0] = checkNumber(value);\n  }\n\n  get pitch(): number {\n    return this[1];\n  }\n  set pitch(value: number) {\n    this[1] = checkNumber(value);\n  }\n\n  get yaw(): number {\n    return this[2];\n  }\n  set yaw(value: number) {\n    this[2] = checkNumber(value);\n  }\n\n  // rotation order, in all three angle notations\n  get order(): RotationOrder {\n    return this[3];\n  }\n  set order(value: RotationOrder) {\n    this[3] = checkOrder(value);\n  }\n\n  // Constructors\n  fromVector3(v: Readonly<NumericArray>, order: RotationOrder): this {\n    return this.set(v[0], v[1], v[2], Number.isFinite(order) ? order : this[3]);\n  }\n\n  // TODO - with and without 4th element\n  fromArray(array: Readonly<NumericArray>, offset: number = 0): this {\n    this[0] = array[0 + offset];\n    this[1] = array[1 + offset];\n    this[2] = array[2 + offset];\n    if (array[3] !== undefined) {\n      this[3] = array[3];\n    }\n    return this.check();\n  }\n\n  // Common ZYX rotation order\n  fromRollPitchYaw(roll: number, pitch: number, yaw: number): this {\n    return this.set(roll, pitch, yaw, RotationOrder.ZYX);\n  }\n\n  fromRotationMatrix(m: Readonly<NumericArray>, order: RotationOrder = Euler.DefaultOrder): this {\n    this._fromRotationMatrix(m, order);\n    return this.check();\n  }\n\n  // ACCESSORS\n\n  getRotationMatrix(m: NumericArray): NumericArray {\n    return this._getRotationMatrix(m);\n  }\n\n  // TODO - move to Quaternion\n  getQuaternion(): Quaternion {\n    const q = new Quaternion();\n    switch (this[4]) {\n      case RotationOrder.XYZ:\n        return q.rotateX(this[0]).rotateY(this[1]).rotateZ(this[2]);\n      case RotationOrder.YXZ:\n        return q.rotateY(this[0]).rotateX(this[1]).rotateZ(this[2]);\n      case RotationOrder.ZXY:\n        return q.rotateZ(this[0]).rotateX(this[1]).rotateY(this[2]);\n      case RotationOrder.ZYX:\n        return q.rotateZ(this[0]).rotateY(this[1]).rotateX(this[2]);\n      case RotationOrder.YZX:\n        return q.rotateY(this[0]).rotateZ(this[1]).rotateX(this[2]);\n      case RotationOrder.XZY:\n        return q.rotateX(this[0]).rotateZ(this[1]).rotateY(this[2]);\n      default:\n        throw new Error(ERR_UNKNOWN_ORDER);\n    }\n  }\n\n  // INTERNAL METHODS\n  // Conversion from Euler to rotation matrix and from matrix to Euler\n  // Adapted from three.js under MIT license\n  // // WARNING: this discards revolution information -bhouston\n  // reorder(newOrder) {\n  //   const q = new Quaternion().setFromEuler(this);\n  //   return this.setFromQuaternion(q, newOrder);\n  /* eslint-disable complexity, max-statements, one-var */\n  _fromRotationMatrix(m: Readonly<NumericArray>, order = Euler.DefaultOrder): this {\n    // assumes the upper 3x3 of m is a pure rotation matrix (i.e, unscaled)\n    const m11 = m[0],\n      m12 = m[4],\n      m13 = m[8];\n    const m21 = m[1],\n      m22 = m[5],\n      m23 = m[9];\n    const m31 = m[2],\n      m32 = m[6],\n      m33 = m[10];\n    order = order || this[3];\n    switch (order) {\n      case Euler.XYZ:\n        this[1] = Math.asin(clamp(m13, -1, 1));\n        if (Math.abs(m13) < ALMOST_ONE) {\n          this[0] = Math.atan2(-m23, m33);\n          this[2] = Math.atan2(-m12, m11);\n        } else {\n          this[0] = Math.atan2(m32, m22);\n          this[2] = 0;\n        }\n        break;\n      case Euler.YXZ:\n        this[0] = Math.asin(-clamp(m23, -1, 1));\n        if (Math.abs(m23) < ALMOST_ONE) {\n          this[1] = Math.atan2(m13, m33);\n          this[2] = Math.atan2(m21, m22);\n        } else {\n          this[1] = Math.atan2(-m31, m11);\n          this[2] = 0;\n        }\n        break;\n      case Euler.ZXY:\n        this[0] = Math.asin(clamp(m32, -1, 1));\n        if (Math.abs(m32) < ALMOST_ONE) {\n          this[1] = Math.atan2(-m31, m33);\n          this[2] = Math.atan2(-m12, m22);\n        } else {\n          this[1] = 0;\n          this[2] = Math.atan2(m21, m11);\n        }\n        break;\n      case Euler.ZYX:\n        this[1] = Math.asin(-clamp(m31, -1, 1));\n        if (Math.abs(m31) < ALMOST_ONE) {\n          this[0] = Math.atan2(m32, m33);\n          this[2] = Math.atan2(m21, m11);\n        } else {\n          this[0] = 0;\n          this[2] = Math.atan2(-m12, m22);\n        }\n        break;\n      case Euler.YZX:\n        this[2] = Math.asin(clamp(m21, -1, 1));\n        if (Math.abs(m21) < ALMOST_ONE) {\n          this[0] = Math.atan2(-m23, m22);\n          this[1] = Math.atan2(-m31, m11);\n        } else {\n          this[0] = 0;\n          this[1] = Math.atan2(m13, m33);\n        }\n        break;\n      case Euler.XZY:\n        this[2] = Math.asin(-clamp(m12, -1, 1));\n        if (Math.abs(m12) < ALMOST_ONE) {\n          this[0] = Math.atan2(m32, m22);\n          this[1] = Math.atan2(m13, m11);\n        } else {\n          this[0] = Math.atan2(-m23, m33);\n          this[1] = 0;\n        }\n        break;\n      default:\n        throw new Error(ERR_UNKNOWN_ORDER);\n    }\n    this[3] = order;\n    return this;\n  }\n\n  _getRotationMatrix(result: NumericArray): NumericArray {\n    const te = result || [-0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0];\n    const x = this.x,\n      y = this.y,\n      z = this.z;\n    const a = Math.cos(x);\n    const c = Math.cos(y);\n    const e = Math.cos(z);\n    const b = Math.sin(x);\n    const d = Math.sin(y);\n    const f = Math.sin(z);\n    switch (this[3]) {\n      case Euler.XYZ: {\n        const ae = a * e,\n          af = a * f,\n          be = b * e,\n          bf = b * f;\n        te[0] = c * e;\n        te[4] = -c * f;\n        te[8] = d;\n        te[1] = af + be * d;\n        te[5] = ae - bf * d;\n        te[9] = -b * c;\n        te[2] = bf - ae * d;\n        te[6] = be + af * d;\n        te[10] = a * c;\n        break;\n      }\n      case Euler.YXZ: {\n        const ce = c * e,\n          cf = c * f,\n          de = d * e,\n          df = d * f;\n        te[0] = ce + df * b;\n        te[4] = de * b - cf;\n        te[8] = a * d;\n        te[1] = a * f;\n        te[5] = a * e;\n        te[9] = -b;\n        te[2] = cf * b - de;\n        te[6] = df + ce * b;\n        te[10] = a * c;\n        break;\n      }\n      case Euler.ZXY: {\n        const ce = c * e,\n          cf = c * f,\n          de = d * e,\n          df = d * f;\n        te[0] = ce - df * b;\n        te[4] = -a * f;\n        te[8] = de + cf * b;\n        te[1] = cf + de * b;\n        te[5] = a * e;\n        te[9] = df - ce * b;\n        te[2] = -a * d;\n        te[6] = b;\n        te[10] = a * c;\n        break;\n      }\n      case Euler.ZYX: {\n        const ae = a * e,\n          af = a * f,\n          be = b * e,\n          bf = b * f;\n        te[0] = c * e;\n        te[4] = be * d - af;\n        te[8] = ae * d + bf;\n        te[1] = c * f;\n        te[5] = bf * d + ae;\n        te[9] = af * d - be;\n        te[2] = -d;\n        te[6] = b * c;\n        te[10] = a * c;\n        break;\n      }\n      case Euler.YZX: {\n        const ac = a * c,\n          ad = a * d,\n          bc = b * c,\n          bd = b * d;\n        te[0] = c * e;\n        te[4] = bd - ac * f;\n        te[8] = bc * f + ad;\n        te[1] = f;\n        te[5] = a * e;\n        te[9] = -b * e;\n        te[2] = -d * e;\n        te[6] = ad * f + bc;\n        te[10] = ac - bd * f;\n        break;\n      }\n      case Euler.XZY: {\n        const ac = a * c,\n          ad = a * d,\n          bc = b * c,\n          bd = b * d;\n        te[0] = c * e;\n        te[4] = -f;\n        te[8] = d * e;\n        te[1] = ac * f + bd;\n        te[5] = a * e;\n        te[9] = ad * f - bc;\n        te[2] = bc * f - ad;\n        te[6] = b * e;\n        te[10] = bd * f + ac;\n        break;\n      }\n      default:\n        throw new Error(ERR_UNKNOWN_ORDER);\n    }\n    // last column\n    te[3] = 0;\n    te[7] = 0;\n    te[11] = 0;\n    // bottom row\n    te[12] = 0;\n    te[13] = 0;\n    te[14] = 0;\n    te[15] = 1;\n    return te;\n  }\n\n  toQuaternion(): Quaternion {\n    // Abbreviations for the various angular functions\n    const cy = Math.cos(this.yaw * 0.5);\n    const sy = Math.sin(this.yaw * 0.5);\n    const cr = Math.cos(this.roll * 0.5);\n    const sr = Math.sin(this.roll * 0.5);\n    const cp = Math.cos(this.pitch * 0.5);\n    const sp = Math.sin(this.pitch * 0.5);\n    const w = cy * cr * cp + sy * sr * sp;\n    const x = cy * sr * cp - sy * cr * sp;\n    const y = cy * cr * sp + sy * sr * cp;\n    const z = sy * cr * cp - cy * sr * sp;\n    return new Quaternion(x, y, z, w);\n  }\n}\n\n// HELPER FUNCTIONS\n\nfunction validateOrder(value: number): boolean {\n  return value >= 0 && value < 6;\n}\n\nfunction checkOrder(value: number) {\n  if (value < 0 && value >= 6) {\n    throw new Error(ERR_UNKNOWN_ORDER);\n  }\n  return value;\n}\n"], "file": "euler.js"}