{"version": 3, "sources": ["../../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA;;AACA;;AACA;;AACA;;AACA;;AACA;;AAGA;;AACA;;AACA;;AAEA;;AAGA;;AAEA", "sourcesContent": ["// luma.gl, MIT license\n\n// types\nexport type {TypedArray, NumericArray} from '@math.gl/types';\n\n// classes\nexport {default as Vector2} from './classes/vector2';\nexport {default as Vector3} from './classes/vector3';\nexport {default as Vector4} from './classes/vector4';\nexport {default as Matrix3} from './classes/matrix3';\nexport {default as Matrix4} from './classes/matrix4';\nexport {default as Quaternion} from './classes/quaternion';\n\n// experimental\nexport {default as SphericalCoordinates} from './classes/spherical-coordinates';\nexport {default as Pose} from './classes/pose';\nexport {default as Euler} from './classes/euler';\n\nexport {default as _MathUtils} from './lib/math-utils';\n\n// lib\nexport {default as assert} from './lib/assert';\n\nexport {\n  // math.gl global utility methods\n  config,\n  configure,\n  formatValue,\n  isArray,\n  clone,\n  equals,\n  exactEquals,\n  toRadians,\n  toDegrees,\n  // math.gl \"GLSL\"-style functions\n  radians,\n  degrees,\n  sin,\n  cos,\n  tan,\n  asin,\n  acos,\n  atan,\n  clamp,\n  lerp,\n  withEpsilon\n} from './lib/common';\n\n// DEPRECATED\nexport {default as _SphericalCoordinates} from './classes/spherical-coordinates';\nexport {default as _Pose} from './classes/pose';\nexport {default as _Euler} from './classes/euler';\n"], "file": "index.js"}