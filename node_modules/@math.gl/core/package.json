{"name": "@math.gl/core", "description": "Array-based 3D Math Classes optimized for WebGL applications", "license": "MIT", "publishConfig": {"access": "public"}, "version": "3.6.3", "keywords": ["webgl", "javascript", "math", "matrix", "matrix4", "vector", "vector2", "vector3", "vector4", "quaternion", "euler", "spherical", "coordinates", "3d"], "repository": {"type": "git", "url": "https://github.com/uber-web/math.gl.git"}, "types": "dist/index.d.ts", "main": "dist/es5/index.js", "module": "dist/esm/index.js", "files": ["dist", "src"], "sideEffects": ["./src/lib/common.js"], "dependencies": {"@babel/runtime": "^7.12.0", "@math.gl/types": "3.6.3", "gl-matrix": "^3.4.0"}, "gitHead": "0efab394df9babad7ed93027c1003f30528b2090"}