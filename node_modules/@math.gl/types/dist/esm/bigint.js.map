{"version": 3, "sources": ["../../src/bigint.ts"], "names": ["ERR_BIGINT_UNAVAILABLE", "BigIntUnavailable", "Error", "asIntN", "asUintN", "BigInt64ArrayUnavailable", "BYTES_PER_ELEMENT", "of", "from", "constructor", "BigIntAvailable", "BigInt", "BigInt64ArrayAvailable", "BigInt64Array", "BigUint64ArrayAvailable", "BigUint64Array", "BigIntCtor", "BigInt64ArrayCtor", "BigUint64ArrayCtor"], "mappings": "AAMA,MAAMA,sBAAsB,GAAG,6CAA/B;;AAEA,SAASC,iBAAT,GAA6B;AAC3B,QAAM,IAAIC,KAAJ,CAAUF,sBAAV,CAAN;AACD;;AACDC,iBAAiB,CAACE,MAAlB,GAA2B,MAAM;AAC/B,QAAM,IAAID,KAAJ,CAAUF,sBAAV,CAAN;AACD,CAFD;;AAGAC,iBAAiB,CAACG,OAAlB,GAA4B,MAAM;AAChC,QAAM,IAAIF,KAAJ,CAAUF,sBAAV,CAAN;AACD,CAFD;;AAIA,MAAMK,wBAAN,CAA+B;AACD,aAAjBC,iBAAiB,GAAG;AAC7B,WAAO,CAAP;AACD;;AACQ,SAAFC,EAAE,GAAG;AACV,UAAM,IAAIL,KAAJ,CAAUF,sBAAV,CAAN;AACD;;AACU,SAAJQ,IAAI,GAAG;AACZ,UAAM,IAAIN,KAAJ,CAAUF,sBAAV,CAAN;AACD;;AACDS,EAAAA,WAAW,GAAG;AACZ,UAAM,IAAIP,KAAJ,CAAUF,sBAAV,CAAN;AACD;;AAZ4B;;AAe/B,OAAO,MAAMU,eAAe,GAAG,OAAOC,MAAP,KAAkB,WAA1C;AACP,OAAO,MAAMC,sBAAsB,GAAG,OAAOC,aAAP,KAAyB,WAAxD;AACP,OAAO,MAAMC,uBAAuB,GAAG,OAAOC,cAAP,KAA0B,WAA1D;;AAEP,MAAMC,UAAU,GAAG,CAAC,MAAM;AACxB,SAAON,eAAe,GAAGC,MAAH,GAAYV,iBAAlC;AACD,CAFkB,GAAnB;;AAIA,MAAMgB,iBAAiB,GAAG,CAAC,MAAM;AAC/B,SAAOL,sBAAsB,GAAGC,aAAH,GAAmBR,wBAAhD;AACD,CAFyB,GAA1B;;AAIA,MAAMa,kBAAkB,GAAG,CAAC,MAAM;AAChC,SAAOJ,uBAAuB,GAAG,CAACC,cAAD,CAAH,GAAsB,CAACV,wBAAD,CAApD;AACD,CAF0B,GAA3B;;AAIA,SAAQW,UAAU,IAAIL,MAAtB;AACA,SAAQM,iBAAiB,IAAIJ,aAA7B;AACA,SAAQK,kBAAkB,IAAIH,cAA9B", "sourcesContent": ["// BigInt compatibility layer\n// Inspired by ArrowJS (under Apache2 license)\n// https://github.com/apache/arrow/blob/master/js/src/util/compat.ts\n// Requires tsconfig.json: target=esnext or (lib: esnext.bigint)\n// Requires eslint: env: {es2020: true}\n\nconst ERR_BIGINT_UNAVAILABLE = 'BigInt is not available in this environment';\n\nfunction BigIntUnavailable() {\n  throw new Error(ERR_BIGINT_UNAVAILABLE);\n}\nBigIntUnavailable.asIntN = () => {\n  throw new Error(ERR_BIGINT_UNAVAILABLE);\n};\nBigIntUnavailable.asUintN = () => {\n  throw new Error(ERR_BIGINT_UNAVAILABLE);\n};\n\nclass BigInt64ArrayUnavailable {\n  static get BYTES_PER_ELEMENT() {\n    return 8;\n  }\n  static of() {\n    throw new Error(ERR_BIGINT_UNAVAILABLE);\n  }\n  static from() {\n    throw new Error(ERR_BIGINT_UNAVAILABLE);\n  }\n  constructor() {\n    throw new Error(ERR_BIGINT_UNAVAILABLE);\n  }\n}\n\nexport const BigIntAvailable = typeof BigInt !== 'undefined';\nexport const BigInt64ArrayAvailable = typeof BigInt64Array !== 'undefined';\nexport const BigUint64ArrayAvailable = typeof BigUint64Array !== 'undefined';\n\nconst BigIntCtor = (() => {\n  return BigIntAvailable ? BigInt : BigIntUnavailable;\n})();\n\nconst BigInt64ArrayCtor = (() => {\n  return BigInt64ArrayAvailable ? BigInt64Array : BigInt64ArrayUnavailable;\n})();\n\nconst BigUint64ArrayCtor = (() => {\n  return BigUint64ArrayAvailable ? [BigUint64Array] : [BigInt64ArrayUnavailable];\n})();\n\nexport {BigIntCtor as BigInt};\nexport {BigInt64ArrayCtor as BigInt64Array};\nexport {BigUint64ArrayCtor as BigUint64Array};\n"], "file": "bigint.js"}