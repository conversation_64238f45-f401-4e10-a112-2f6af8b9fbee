declare function BigIntUnavailable(): void;
declare namespace BigIntUnavailable {
    var asIntN: () => never;
    var asUintN: () => never;
}
declare class BigInt64ArrayUnavailable {
    static get BYTES_PER_ELEMENT(): number;
    static of(): void;
    static from(): void;
    constructor();
}
export declare const BigIntAvailable: boolean;
export declare const BigInt64ArrayAvailable: boolean;
export declare const BigUint64ArrayAvailable: boolean;
declare const BigIntCtor: typeof BigIntUnavailable | BigIntConstructor;
declare const BigInt64ArrayCtor: typeof BigInt64ArrayUnavailable | BigInt64ArrayConstructor;
declare const BigUint64ArrayCtor: BigUint64ArrayConstructor[] | (typeof BigInt64ArrayUnavailable)[];
export { BigIntCtor as BigInt };
export { BigInt64ArrayCtor as BigInt64Array };
export { BigUint64ArrayCtor as BigUint64Array };
//# sourceMappingURL=bigint.d.ts.map