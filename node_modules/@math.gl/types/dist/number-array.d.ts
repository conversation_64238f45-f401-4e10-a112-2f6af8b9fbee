/**
 * TypeScript type covering all typed arrays
 */
export declare type TypedArray = Int8Array | Uint8Array | Int16Array | Uint16Array | Int32Array | Uint32Array | Uint8ClampedArray | Float32Array | Float64Array;
/**
 * TypeScript type covering all typed arrays and classic arrays consisting of numbers
 */
export declare type NumericArray = TypedArray | number[];
/**
 * TypeScript type covering all typed arrays and classic arrays consisting of numbers
 */
export declare type NumberArray = TypedArray | number[];
//# sourceMappingURL=number-array.d.ts.map