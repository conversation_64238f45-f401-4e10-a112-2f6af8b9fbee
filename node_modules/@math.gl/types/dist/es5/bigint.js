"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.BigUint64Array = exports.BigInt64Array = exports.BigInt = exports.BigUint64ArrayAvailable = exports.BigInt64ArrayAvailable = exports.BigIntAvailable = void 0;

var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));

var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));

var ERR_BIGINT_UNAVAILABLE = 'BigInt is not available in this environment';

function BigIntUnavailable() {
  throw new Error(ERR_BIGINT_UNAVAILABLE);
}

BigIntUnavailable.asIntN = function () {
  throw new Error(ERR_BIGINT_UNAVAILABLE);
};

BigIntUnavailable.asUintN = function () {
  throw new Error(ERR_BIGINT_UNAVAILABLE);
};

var BigInt64ArrayUnavailable = function () {
  function BigInt64ArrayUnavailable() {
    (0, _classCallCheck2.default)(this, BigInt64ArrayUnavailable);
    throw new Error(ERR_BIGINT_UNAVAILABLE);
  }

  (0, _createClass2.default)(BigInt64ArrayUnavailable, null, [{
    key: "BYTES_PER_ELEMENT",
    get: function get() {
      return 8;
    }
  }, {
    key: "of",
    value: function of() {
      throw new Error(ERR_BIGINT_UNAVAILABLE);
    }
  }, {
    key: "from",
    value: function from() {
      throw new Error(ERR_BIGINT_UNAVAILABLE);
    }
  }]);
  return BigInt64ArrayUnavailable;
}();

var BigIntAvailable = typeof BigInt !== 'undefined';
exports.BigIntAvailable = BigIntAvailable;
var BigInt64ArrayAvailable = typeof BigInt64Array !== 'undefined';
exports.BigInt64ArrayAvailable = BigInt64ArrayAvailable;
var BigUint64ArrayAvailable = typeof BigUint64Array !== 'undefined';
exports.BigUint64ArrayAvailable = BigUint64ArrayAvailable;

var BigIntCtor = function () {
  return BigIntAvailable ? BigInt : BigIntUnavailable;
}();

exports.BigInt = BigIntCtor;

var BigInt64ArrayCtor = function () {
  return BigInt64ArrayAvailable ? BigInt64Array : BigInt64ArrayUnavailable;
}();

exports.BigInt64Array = BigInt64ArrayCtor;

var BigUint64ArrayCtor = function () {
  return BigUint64ArrayAvailable ? [BigUint64Array] : [BigInt64ArrayUnavailable];
}();

exports.BigUint64Array = BigUint64ArrayCtor;
//# sourceMappingURL=bigint.js.map