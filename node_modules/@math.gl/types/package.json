{"name": "@math.gl/types", "description": "TypeScript types for math.gl", "license": "MIT", "publishConfig": {"access": "public"}, "version": "3.6.3", "keywords": ["typescript", "javascript", "webgl", "math", "array type"], "repository": {"type": "git", "url": "https://github.com/uber-web/math.gl.git"}, "types": "dist/index.d.ts", "main": "dist/es5/index.js", "module": "dist/esm/index.js", "files": ["dist", "src"], "sideEffects": ["./src/bigint.js"], "gitHead": "0efab394df9babad7ed93027c1003f30528b2090"}