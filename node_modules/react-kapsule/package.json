{"name": "react-kapsule", "version": "2.5.7", "description": "A React wrapper for Kapsule-style web components", "type": "module", "jsdelivr": "dist/react-kapsule.min.js", "unpkg": "dist/react-kapsule.min.js", "main": "dist/react-kapsule.mjs", "module": "dist/react-kapsule.mjs", "types": "dist/react-kapsule.d.ts", "exports": {"types": "./dist/react-kapsule.d.ts", "umd": "./dist/react-kapsule.min.js", "default": "./dist/react-kapsule.mjs"}, "sideEffects": false, "repository": {"type": "git", "url": "git+https://github.com/vasturiano/react-kapsule.git"}, "keywords": ["react", "wrapper", "kapsule", "web", "component"], "author": {"name": "Vasco <PERSON>turiano", "url": "https://github.com/vasturiano"}, "license": "MIT", "bugs": {"url": "https://github.com/vasturiano/react-kapsule/issues"}, "homepage": "https://github.com/vasturiano/react-kapsule", "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -w -c", "prepare": "npm run build"}, "files": ["dist/**/*"], "dependencies": {"jerrypick": "^1.1.1"}, "peerDependencies": {"react": ">=16.13.1"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-terser": "^0.4.4", "@types/react": "^19.0.12", "rimraf": "^6.0.1", "rollup": "^4.36.0", "rollup-plugin-dts": "^6.2.1", "typescript": "^5.8.2"}, "engines": {"node": ">=12"}}