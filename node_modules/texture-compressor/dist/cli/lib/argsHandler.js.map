{"version": 3, "file": "argsHandler.js", "sourceRoot": "", "sources": ["../../../lib/argsHandler.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,SAAS;AACT,uCAA0C;AAE1C,UAAU;AACV,qDAAuC;AAEvC,YAAY;AACZ,2CAcqB;AAErB;;GAEG;AACH,MAAM,qBAAqB,GAAG,GAAa,EAAE;IAC3C,MAAM,MAAM,GAAG,IAAI,yBAAc,CAAC;QAChC,OAAO,EAAE,IAAI;QACb,WAAW,EAAE,GAAG,CAAC,WAAW;QAC5B,OAAO,EAAE,GAAG,CAAC,OAAO;KACrB,CAAC,CAAC;IAEH,kBAAkB;IAClB,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE;QACpC,IAAI,EAAE,2BAA2B;QACjC,QAAQ,EAAE,KAAK;KAChB,CAAC,CAAC;IAEH,mBAAmB;IACnB,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE;QACrC,IAAI,EAAE,gCAAgC;QACtC,QAAQ,EAAE,IAAI;KACf,CAAC,CAAC;IAEH,0BAA0B;IAC1B,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE;QACnC,OAAO,EAAE,oCAAwB;QACjC,IAAI,EAAE,oBAAoB;QAC1B,QAAQ,EAAE,IAAI;KACf,CAAC,CAAC;IAEH,mCAAmC;IACnC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,eAAe,CAAC,EAAE;QAC1C,OAAO,EAAE;YACP,GAAG,CAAC,mBAAO,CAAC,CAAC,CAAC,kCAAsB,CAAC,CAAC,CAAC,EAAE,CAAC;YAC1C,GAAG,CAAC,kBAAM,CAAC,CAAC,CAAC,iCAAqB,CAAC,CAAC,CAAC,EAAE,CAAC;YACxC,GAAG,CAAC,oBAAQ,CAAC,CAAC,CAAC,mCAAuB,CAAC,CAAC,CAAC,EAAE,CAAC;YAC5C,GAAG,CAAC,mBAAO,CAAC,CAAC,CAAC,kCAAsB,CAAC,CAAC,CAAC,EAAE,CAAC;SAC3C;QACD,IAAI,EAAE,6BAA6B;QACnC,QAAQ,EAAE,IAAI;KACf,CAAC,CAAC;IAEH,eAAe;IACf,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,WAAW,CAAC,EAAE;QACtC,OAAO,EAAE;YACP,GAAG,CAAC,mBAAO,CAAC,CAAC,CAAC,8BAAkB,CAAC,CAAC,CAAC,EAAE,CAAC;YACtC,GAAG,CAAC,kBAAM,CAAC,CAAC,CAAC,6BAAiB,CAAC,CAAC,CAAC,EAAE,CAAC;YACpC,GAAG,CAAC,oBAAQ,CAAC,CAAC,CAAC,+BAAmB,CAAC,CAAC,CAAC,EAAE,CAAC;YACxC,GAAG,CAAC,mBAAO,CAAC,CAAC,CAAC,8BAAkB,CAAC,CAAC,CAAC,EAAE,CAAC;SACvC;QACD,IAAI,EAAE,cAAc;QACpB,QAAQ,EAAE,IAAI;KACf,CAAC,CAAC;IAEH,kBAAkB;IAClB,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE;QACrC,MAAM,EAAE,WAAW;QACnB,IAAI,EAAE,mBAAmB;QACzB,QAAQ,EAAE,KAAK;KAChB,CAAC,CAAC;IAEH,qBAAqB;IACrB,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE;QACpC,MAAM,EAAE,WAAW;QACnB,IAAI,EAAE,gCAAgC;QACtC,QAAQ,EAAE,KAAK;KAChB,CAAC,CAAC;IAEH,qBAAqB;IACrB,MAAM,CAAC,WAAW,CAAC,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE;QACtC,OAAO,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC;QACzB,IAAI,EAAE,8CAA8C;QACpD,QAAQ,EAAE,KAAK;KAChB,CAAC,CAAC;IAEH,2BAA2B;IAC3B,MAAM,CAAC,WAAW,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE;QACnC,OAAO,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC;QACzB,IAAI,EAAE,6DAA6D;QACnE,QAAQ,EAAE,KAAK;KAChB,CAAC,CAAC;IAEH,8CAA8C;IAC9C,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE;QACpC,IAAI,EAAE,6DAA6D;QACnE,KAAK,EAAE,GAAG;KACX,CAAC,CAAC;IAEH,kBAAkB;IAClB,MAAM,CAAC,WAAW,CAAC,CAAC,KAAK,EAAE,WAAW,CAAC,EAAE;QACvC,MAAM,EAAE,WAAW;QACnB,IAAI,EAAE,wBAAwB;QAC9B,QAAQ,EAAE,KAAK;KAChB,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;AAC5B,CAAC,CAAC;AAgBW,QAAA,OAAO,GAAG,qBAAqB,EAAE,CAAC"}