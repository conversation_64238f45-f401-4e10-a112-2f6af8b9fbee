{"name": "texture-compressor", "version": "1.0.2", "description": "CLI tool for texture compression using ASTC, ETC, PVRTC and S3TC in a KTX container.", "main": "dist/cli/lib/index.js", "types": "dist/types/lib/index.d.ts", "scripts": {"start": "tsc -w", "lint": "tslint --project tsconfig.json -t codeFrame 'src/**/*.ts' 'test/**/*.ts'", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc"}, "bin": {"texture-compressor": "./bin/texture-compressor.js"}, "author": "<PERSON>", "license": "MIT", "dependencies": {"argparse": "^1.0.10", "image-size": "^0.7.4"}, "devDependencies": {"@types/argparse": "^1.0.36", "@types/node": "^12.0.2", "prettier": "^1.17.1", "rimraf": "^2.6.3", "tslint": "^5.16.0", "tslint-config-prettier": "^1.18.0", "typescript": "^3.4.5"}, "prettier": {"semi": true, "singleQuote": true, "trailingComma": "es5", "printWidth": 100}}