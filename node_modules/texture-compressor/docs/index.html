<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Texture compressor</title>
    <style>
      body,
      html {
        font-family: Arial, Helvetica, sans-serif;
        margin: 0;
        padding: 0;
        width: 100%;
        height: 100%;
        background: #fefefe;
        overflow: hidden;
      }

      #app {
        padding-left: 20px;
      }
    </style>
  </head>

  <body>
    <pre id="app"></pre>

    <script>
      var type = 'flippedY-mipmaps';
    </script>

    <script src="https://rawgit.com/mrdoob/three.js/dev/build/three.min.js"></script>
    <script src="./data/KTXLoader.js"></script>
    <script src="./data/main.js"></script>
  </body>
</html>
