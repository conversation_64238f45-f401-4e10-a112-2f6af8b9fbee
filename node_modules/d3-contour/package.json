{"name": "d3-contour", "version": "4.0.2", "description": "Compute contour polygons using marching squares.", "homepage": "https://d3js.org/d3-contour/", "repository": {"type": "git", "url": "https://github.com/d3/d3-contour.git"}, "keywords": ["d3", "d3-module", "contour", "isoline"], "license": "ISC", "author": {"name": "<PERSON>", "url": "http://bost.ocks.org/mike"}, "type": "module", "files": ["dist/**/*.js", "src/**/*.js"], "module": "src/index.js", "main": "src/index.js", "jsdelivr": "dist/d3-contour.min.js", "unpkg": "dist/d3-contour.min.js", "exports": {"umd": "./dist/d3-contour.min.js", "default": "./src/index.js"}, "_moduleAliases": {"d3-contour": "./src/index.js"}, "sideEffects": false, "dependencies": {"d3-array": "^3.2.0"}, "devDependencies": {"d3-axis": "3", "d3-dsv": "3", "d3-fetch": "3", "d3-geo": "3", "d3-polygon": "3", "d3-scale": "4", "d3-selection": "3", "eslint": "8", "htl": "^0.3.1", "js-beautify": "1", "jsdom": "20", "mocha": "10", "module-alias": "2", "rollup": "3", "rollup-plugin-terser": "7"}, "scripts": {"test": "mkdir -p test/output && mocha -r module-alias/register 'test/**/*-test.js' test/snapshot.js && eslint src test", "prepublishOnly": "rm -rf dist && rollup -c", "postpublish": "git push && git push --tags && cd ../d3.github.com && git pull && cp ../${npm_package_name}/dist/${npm_package_name}.js ${npm_package_name}.v${npm_package_version%%.*}.js && cp ../${npm_package_name}/dist/${npm_package_name}.min.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git add ${npm_package_name}.v${npm_package_version%%.*}.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git commit -m \"${npm_package_name} ${npm_package_version}\" && git push && cd -"}, "engines": {"node": ">=12"}}