{"name": "d3-transition", "version": "3.0.1", "description": "Animated transitions for D3 selections.", "homepage": "https://d3js.org/d3-transition/", "repository": {"type": "git", "url": "https://github.com/d3/d3-transition.git"}, "keywords": ["d3", "d3-module", "dom", "transition", "animation"], "license": "ISC", "author": {"name": "<PERSON>", "url": "https://bost.ocks.org/mike"}, "type": "module", "files": ["dist/**/*.js", "src/**/*.js"], "module": "src/index.js", "main": "src/index.js", "jsdelivr": "dist/d3-transition.min.js", "unpkg": "dist/d3-transition.min.js", "exports": {"umd": "./dist/d3-transition.min.js", "default": "./src/index.js"}, "sideEffects": ["./src/index.js", "./src/selection/index.js"], "dependencies": {"d3-color": "1 - 3", "d3-dispatch": "1 - 3", "d3-ease": "1 - 3", "d3-interpolate": "1 - 3", "d3-timer": "1 - 3"}, "devDependencies": {"d3-selection": "2 - 3", "eslint": "7", "jsdom": "16", "mocha": "9", "rollup": "2", "rollup-plugin-terser": "7"}, "scripts": {"test": "mocha 'test/**/*-test.js' && eslint src test", "prepublishOnly": "rm -rf dist && yarn test && rollup -c && git push", "postpublish": "git push --tags && cd ../d3.github.com && git pull && cp ../${npm_package_name}/dist/${npm_package_name}.js ${npm_package_name}.v${npm_package_version%%.*}.js && cp ../${npm_package_name}/dist/${npm_package_name}.min.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git add ${npm_package_name}.v${npm_package_version%%.*}.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git commit -m \"${npm_package_name} ${npm_package_version}\" && git push && cd -"}, "engines": {"node": ">=12"}, "peerDependencies": {"d3-selection": "2 - 3"}}