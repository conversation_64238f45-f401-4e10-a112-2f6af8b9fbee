var t;!function(t){t[t.NONE=0]="NONE",t[t.BASISLZ=1]="BASISLZ",t[t.ZSTD=2]="ZSTD",t[t.ZLIB=3]="ZLIB"}(t||(t={}));var e=function(){this.vkFormat=0,this.typeSize=-1,this.pixelWidth=-1,this.pixelHeight=-1,this.pixelDepth=-1,this.layerCount=-1,this.faceCount=-1,this.levelCount=-1,this.supercompressionScheme=t.NONE,this.levelIndex=[],this.dataFormatDescriptor=[],this.keyValue=[],this.globalData=null},n=function(){function t(t,e,n,i){this._dataView=new DataView(t,e,n),this._littleEndian=i,this._offset=0}var e=t.prototype;return e._nextUint8=function(){var t=this._dataView.getUint8(this._offset);return this._offset+=1,t},e._nextUint16=function(){var t=this._dataView.getUint16(this._offset,this._littleEndian);return this._offset+=2,t},e._nextUint32=function(){var t=this._dataView.getUint32(this._offset,this._littleEndian);return this._offset+=4,t},e._nextUint64=function(){var t=this._dataView.getUint32(this._offset,this._littleEndian),e=this._dataView.getUint32(this._offset+4,this._littleEndian),n=this._littleEndian?t+Math.pow(2,32)*e:Math.pow(2,32)*t+e;return Number.isSafeInteger(n)||console.warn(n+" exceeds MAX_SAFE_INTEGER. Precision may be lost."),this._offset+=8,n},e._skip=function(t){return this._offset+=t,this},t}();exports.Container=e,exports.read=function(t){var i=new Uint8Array(t,0,12);if(171!==i[0]||75!==i[1]||84!==i[2]||88!==i[3]||32!==i[4]||50!==i[5]||48!==i[6]||187!==i[7]||13!==i[8]||10!==i[9]||26!==i[10]||10!==i[11])throw new Error("Missing KTX 2.0 identifier.");var s=new e,r=17*Uint32Array.BYTES_PER_ELEMENT,a=new n(t,12,r,!0);s.vkFormat=a._nextUint32(),s.typeSize=a._nextUint32(),s.pixelWidth=a._nextUint32(),s.pixelHeight=a._nextUint32(),s.pixelDepth=a._nextUint32(),s.layerCount=a._nextUint32(),s.faceCount=a._nextUint32(),s.levelCount=a._nextUint32(),s.supercompressionScheme=a._nextUint32();for(var o=a._nextUint32(),_=a._nextUint32(),l=(a._nextUint32(),a._nextUint32(),a._nextUint64()),x=a._nextUint64(),h=new n(t,12+r,3*s.levelCount*8,!0),U=0;U<s.levelCount;U++)s.levelIndex.push({data:new Uint8Array(t,h._nextUint64(),h._nextUint64()),uncompressedByteLength:h._nextUint64()});var f=new n(t,o,_,!0),u={vendorId:f._skip(4)._nextUint16(),versionNumber:f._skip(2)._nextUint16(),descriptorBlockSize:f._nextUint16(),colorModel:f._nextUint8(),colorPrimaries:f._nextUint8(),transferFunction:f._nextUint8(),flags:f._nextUint8(),texelBlockDimension:{x:f._nextUint8()+1,y:f._nextUint8()+1,z:f._nextUint8()+1,w:f._nextUint8()+1},bytesPlane0:f._nextUint8(),numSamples:0,samples:[]};u.numSamples=(u.descriptorBlockSize/4-6)/4,f._skip(7);for(var p=0;p<u.numSamples;p++)u.samples[p]={channelID:f._skip(3)._nextUint8()},f._skip(12);if(x<=0)return s;for(var c=new n(t,l,x,!0),d=c._nextUint16(),w=c._nextUint16(),y=c._nextUint32(),v=c._nextUint32(),g=c._nextUint32(),m=c._nextUint32(),S=[],E=0;E<s.levelCount;E++)S.push({imageFlags:c._nextUint32(),rgbSliceByteOffset:c._nextUint32(),rgbSliceByteLength:c._nextUint32(),alphaSliceByteOffset:c._nextUint32(),alphaSliceByteLength:c._nextUint32()});var B=l+c._offset,D=B+y,k=D+v,A=k+g,C=new Uint8Array(t,B,y),L=new Uint8Array(t,D,v),b=new Uint8Array(t,k,g),I=new Uint8Array(t,A,m);return s.globalData={endpointCount:d,selectorCount:w,endpointsByteLength:y,selectorsByteLength:v,tablesByteLength:g,extendedByteLength:m,imageDescs:S,endpointsData:C,selectorsData:L,tablesData:b,extendedData:I},s},exports.write=function(t){return new Uint8Array};
//# sourceMappingURL=ktx2-parse.js.map
