var t;!function(t){t[t.NONE=0]="NONE",t[t.BASISLZ=1]="BASISLZ",t[t.ZSTD=2]="ZSTD",t[t.ZLIB=3]="ZLIB"}(t||(t={}));class e{constructor(){this.vkFormat=0,this.typeSize=-1,this.pixelWidth=-1,this.pixelHeight=-1,this.pixelDepth=-1,this.layerCount=-1,this.faceCount=-1,this.levelCount=-1,this.supercompressionScheme=t.NONE,this.levelIndex=[],this.dataFormatDescriptor=[],this.keyValue=[],this.globalData=null}}class n{constructor(t,e,n,i){this._dataView=new DataView(t,e,n),this._littleEndian=i,this._offset=0}_nextUint8(){const t=this._dataView.getUint8(this._offset);return this._offset+=1,t}_nextUint16(){const t=this._dataView.getUint16(this._offset,this._littleEndian);return this._offset+=2,t}_nextUint32(){const t=this._dataView.getUint32(this._offset,this._littleEndian);return this._offset+=4,t}_nextUint64(){const t=this._dataView.getUint32(this._offset,this._littleEndian),e=this._dataView.getUint32(this._offset+4,this._littleEndian),n=this._littleEndian?t+2**32*e:2**32*t+e;return Number.isSafeInteger(n)||console.warn(n+" exceeds MAX_SAFE_INTEGER. Precision may be lost."),this._offset+=8,n}_skip(t){return this._offset+=t,this}}function i(t){var i=new Uint8Array(t,0,12);if(171!==i[0]||75!==i[1]||84!==i[2]||88!==i[3]||32!==i[4]||50!==i[5]||48!==i[6]||187!==i[7]||13!==i[8]||10!==i[9]||26!==i[10]||10!==i[11])throw new Error("Missing KTX 2.0 identifier.");const s=new e,r=17*Uint32Array.BYTES_PER_ELEMENT,a=new n(t,12,r,!0);s.vkFormat=a._nextUint32(),s.typeSize=a._nextUint32(),s.pixelWidth=a._nextUint32(),s.pixelHeight=a._nextUint32(),s.pixelDepth=a._nextUint32(),s.layerCount=a._nextUint32(),s.faceCount=a._nextUint32(),s.levelCount=a._nextUint32(),s.supercompressionScheme=a._nextUint32();const o=a._nextUint32(),_=a._nextUint32(),l=(a._nextUint32(),a._nextUint32(),a._nextUint64()),x=a._nextUint64(),U=new n(t,12+r,3*s.levelCount*8,!0);for(let e=0;e<s.levelCount;e++)s.levelIndex.push({data:new Uint8Array(t,U._nextUint64(),U._nextUint64()),uncompressedByteLength:U._nextUint64()});const h=new n(t,o,_,!0),c={vendorId:h._skip(4)._nextUint16(),versionNumber:h._skip(2)._nextUint16(),descriptorBlockSize:h._nextUint16(),colorModel:h._nextUint8(),colorPrimaries:h._nextUint8(),transferFunction:h._nextUint8(),flags:h._nextUint8(),texelBlockDimension:{x:h._nextUint8()+1,y:h._nextUint8()+1,z:h._nextUint8()+1,w:h._nextUint8()+1},bytesPlane0:h._nextUint8(),numSamples:0,samples:[]};c.numSamples=(c.descriptorBlockSize/4-6)/4,h._skip(7);for(let t=0;t<c.numSamples;t++)c.samples[t]={channelID:h._skip(3)._nextUint8()},h._skip(12);if(x<=0)return s;const f=new n(t,l,x,!0),p=f._nextUint16(),u=f._nextUint16(),d=f._nextUint32(),y=f._nextUint32(),g=f._nextUint32(),w=f._nextUint32(),m=[];for(let t=0;t<s.levelCount;t++)m.push({imageFlags:f._nextUint32(),rgbSliceByteOffset:f._nextUint32(),rgbSliceByteLength:f._nextUint32(),alphaSliceByteOffset:f._nextUint32(),alphaSliceByteLength:f._nextUint32()});const S=l+f._offset,E=S+d,B=E+y,D=B+g,v=new Uint8Array(t,S,d),k=new Uint8Array(t,E,y),A=new Uint8Array(t,B,g),L=new Uint8Array(t,D,w);return s.globalData={endpointCount:p,selectorCount:u,endpointsByteLength:d,selectorsByteLength:y,tablesByteLength:g,extendedByteLength:w,imageDescs:m,endpointsData:v,selectorsData:k,tablesData:A,extendedData:L},s}function s(t){return new Uint8Array}export{e as Container,i as read,s as write};
//# sourceMappingURL=ktx2-parse.modern.js.map
