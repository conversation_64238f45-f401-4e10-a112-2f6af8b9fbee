{"version": 3, "file": "ktx2-parse.js", "sources": ["../src/ktx2-schema.ts", "../src/container.ts", "../src/buffer-reader.ts", "../src/read.ts", "../src/write.ts"], "sourcesContent": ["///////////////////////////////////////////////////\n// Mip Levels.\n///////////////////////////////////////////////////\n\nexport interface KTX2Level {\n    data: Uint8Array;\n    uncompressedByteLength: number;\n}\n\nexport enum KTX2SupercompressionSchema {\n\tNONE = 0,\n\tBASISLZ = 1,\n\tZSTD = 2,\n\tZLIB = 3,\n}\n\n///////////////////////////////////////////////////\n// Data Format Descriptor (DFD).\n///////////////////////////////////////////////////\n\nexport const KTX2DataFormatDescriptorModel = {\n\tETC1S: 163,\n\tUASTC: 166,\n};\n\nexport const KTX2DataFormatDescriptorChannel = {\n\tETC1S: {\n\t\tRGB: 0,\n\t\tRRR: 3,\n\t\tGGG: 4,\n\t\tAAA: 15,\n\t},\n\tUASTC: {\n\t\tRGB: 0,\n\t\tRGBA: 3,\n\t\tRRR: 4,\n\t\tRRRG: 5\n\t},\n};\n\nexport interface KTX2DataFormatDescriptorTexelBlockDimensions {\n\tx: number;\n\ty: number;\n\tz: number;\n\tw: number;\n}\n\nexport interface KTX2DataFormatDescriptorSample {\n\tchannelID: number;\n\t// ... remainder not implemented.\n}\n\nexport interface KTX2DataFormatDescriptor {\n\tvendorId: number;\n\tversionNumber: number;\n\tdescriptorBlockSize: number;\n\tcolorModel: number;\n\tcolorPrimaries: number;\n\ttransferFunction: number;\n\tflags: number;\n\ttexelBlockDimension: KTX2DataFormatDescriptorTexelBlockDimensions;\n\tbytesPlane0: number;\n\tnumSamples: number;\n\tsamples: KTX2DataFormatDescriptorSample[],\n}\n\n///////////////////////////////////////////////////\n// Key/Value Data (KFD).\n///////////////////////////////////////////////////\n\nexport interface KTX2KeyValue {\n\t// ... remainder not implemented.\n}\n\n///////////////////////////////////////////////////\n// Supercompression Global Data.\n///////////////////////////////////////////////////\n\ninterface KTX2GlobalDataImageDescription {\n\timageFlags: number;\n\trgbSliceByteOffset: number;\n\trgbSliceByteLength: number;\n\talphaSliceByteOffset: number;\n\talphaSliceByteLength: number;\n}\n\nexport interface KTX2GlobalData {\n\tendpointCount: number;\n\tselectorCount: number;\n\tendpointsByteLength: number;\n\tselectorsByteLength: number;\n\ttablesByteLength: number;\n\textendedByteLength: number;\n\timageDescs: KTX2GlobalDataImageDescription[];\n\tendpointsData: Uint8Array;\n\tselectorsData: Uint8Array;\n\ttablesData: Uint8Array;\n\textendedData: Uint8Array;\n}", "import { KTX2DataFormatDescriptor, KTX2GlobalData, KTX2KeyValue, KTX2Level, KTX2SupercompressionSchema } from './ktx2-schema';\n\nexport class Container {\n    // Header.\n    public vkFormat: number = 0x00;\n    public typeSize: number = -1;\n    public pixelWidth: number = -1;\n    public pixelHeight: number = -1;\n    public pixelDepth: number = -1;\n    public layerCount: number = -1;\n    public faceCount: number = -1;\n    public levelCount: number = -1;\n    public supercompressionScheme = KTX2SupercompressionSchema.NONE;\n\n    /** Mip Levels. */\n    public levelIndex: KTX2Level[] = [];\n\n    /** Data Format Descriptor. */\n    public dataFormatDescriptor: KTX2DataFormatDescriptor[] = [];\n\n    /** Key/Value Data. */\n    public keyValue: KTX2KeyValue[] = [];\n\n    /** Supercompression Global Data. */\n    public globalData: KTX2GlobalData | null = null;\n}\n", "export class BufferReader {\n\tprivate _dataView: DataView;\n\tprivate _littleEndian: boolean;\n\tpublic _offset: number;\n\n\tconstructor(data: Uint8Array, byteOffset: number, byteLength: number, littleEndian: boolean ) {\n\t\tthis._dataView = new DataView( data, byteOffset, byteLength );\n\t\tthis._littleEndian = littleEndian;\n\t\tthis._offset = 0;\n\t}\n\n\t_nextUint8() {\n\t\tconst value = this._dataView.getUint8( this._offset );\n\t\tthis._offset += 1;\n\t\treturn value;\n\t}\n\n\t_nextUint16() {\n\t\tconst value = this._dataView.getUint16( this._offset, this._littleEndian );\n\t\tthis._offset += 2;\n\t\treturn value;\n\t}\n\n\t_nextUint32() {\n\t\tconst value = this._dataView.getUint32( this._offset, this._littleEndian );\n\t\tthis._offset += 4;\n\t\treturn value;\n\t}\n\n\t_nextUint64() {\n\t\t// https://stackoverflow.com/questions/53103695/\n\t\tconst left = this._dataView.getUint32( this._offset, this._littleEndian );\n\t\tconst right = this._dataView.getUint32( this._offset + 4, this._littleEndian );\n\t\tconst value = this._littleEndian ? left + ( 2 ** 32 * right ) : ( 2 ** 32 * left ) + right;\n\n\t\tif (!Number.isSafeInteger(value)) {\n\t\t\tconsole.warn(value + ' exceeds MAX_SAFE_INTEGER. Precision may be lost.');\n\t\t}\n\n\t\tthis._offset += 8;\n\t\treturn value;\n\t}\n\n\t_skip(bytes: number) {\n\t\tthis._offset += bytes;\n\t\treturn this;\n\t}\n}\n", "import { B<PERSON><PERSON>Reader } from './buffer-reader';\nimport { Container } from './container';\nimport { KTX2DataFormatDescriptor, KTX2GlobalData } from './ktx2-schema';\n\nexport function read(data: Uint8Array): Container {\n\n\t// Confirm this is a KTX 2.0 file, based on the identifier in the first 12 bytes.\n\tvar idByteLength = 12;\n\tvar id = new Uint8Array(data, 0, idByteLength);\n\tif (id[0] !== 0xAB || // '´'\n\t\tid[ 1 ] !== 0x4B || // 'K'\n\t\tid[ 2 ] !== 0x54 || // 'T'\n\t\tid[ 3 ] !== 0x58 || // 'X'\n\t\tid[ 4 ] !== 0x20 || // ' '\n\t\tid[ 5 ] !== 0x32 || // '2'\n\t\tid[ 6 ] !== 0x30 || // '0'\n\t\tid[ 7 ] !== 0xBB || // 'ª'\n\t\tid[ 8 ] !== 0x0D || // '\\r'\n\t\tid[ 9 ] !== 0x0A || // '\\n'\n\t\tid[ 10 ] !== 0x1A || // '\\x1A'\n\t\tid[ 11 ] !== 0x0A // '\\n'\n\t) {\n\t\tthrow new Error( 'Missing KTX 2.0 identifier.' );\n\t}\n\n\tconst container = new Container();\n\n\t///////////////////////////////////////////////////\n\t// Header.\n\t///////////////////////////////////////////////////\n\n\tconst headerByteLength = 17 * Uint32Array.BYTES_PER_ELEMENT;\n\tconst headerReader = new BufferReader( data, idByteLength, headerByteLength, true );\n\n\tcontainer.vkFormat = headerReader._nextUint32();\n\tcontainer.typeSize = headerReader._nextUint32();\n\tcontainer.pixelWidth = headerReader._nextUint32();\n\tcontainer.pixelHeight = headerReader._nextUint32();\n\tcontainer.pixelDepth = headerReader._nextUint32();\n\tcontainer.layerCount = headerReader._nextUint32();\n\tcontainer.faceCount = headerReader._nextUint32();\n\tcontainer.levelCount = headerReader._nextUint32();\n\tcontainer.supercompressionScheme = headerReader._nextUint32();\n\n\tconst dfdByteOffset = headerReader._nextUint32();\n\tconst dfdByteLength = headerReader._nextUint32();\n\tconst kvdByteOffset = headerReader._nextUint32();\n\tconst kvdByteLength = headerReader._nextUint32();\n\tconst sgdByteOffset = headerReader._nextUint64();\n\tconst sgdByteLength = headerReader._nextUint64();\n\n\t///////////////////////////////////////////////////\n\t// Level index\n\t///////////////////////////////////////////////////\n\n\tconst levelByteLength = container.levelCount * 3 * 8;\n\tconst levelReader = new BufferReader(data, idByteLength + headerByteLength, levelByteLength, true);\n\n\tfor (let i = 0; i < container.levelCount; i ++) {\n\t\tcontainer.levelIndex.push({\n\t\t\tdata: new Uint8Array(data, levelReader._nextUint64(), levelReader._nextUint64()),\n\t\t\tuncompressedByteLength: levelReader._nextUint64(),\n\t\t});\n\t}\n\n\n\t///////////////////////////////////////////////////\n\t// Data Format Descriptor (DFD)\n\t///////////////////////////////////////////////////\n\n\tconst dfdReader = new BufferReader(data, dfdByteOffset, dfdByteLength, true);\n\n\tconst sampleStart = 6;\n\tconst sampleWords = 4;\n\n\tconst dfd: KTX2DataFormatDescriptor = {\n\t\tvendorId: dfdReader._skip(4 /* totalSize */)._nextUint16(),\n\t\tversionNumber: dfdReader._skip(2 /* descriptorType */)._nextUint16(),\n\t\tdescriptorBlockSize: dfdReader._nextUint16(),\n\t\tcolorModel: dfdReader._nextUint8(),\n\t\tcolorPrimaries: dfdReader._nextUint8(),\n\t\ttransferFunction: dfdReader._nextUint8(),\n\t\tflags: dfdReader._nextUint8(),\n\t\ttexelBlockDimension: {\n\t\t\tx: dfdReader._nextUint8() + 1,\n\t\t\ty: dfdReader._nextUint8() + 1,\n\t\t\tz: dfdReader._nextUint8() + 1,\n\t\t\tw: dfdReader._nextUint8() + 1,\n\t\t},\n\t\tbytesPlane0: dfdReader._nextUint8(),\n\t\tnumSamples: 0,\n\t\tsamples: [],\n\t};\n\n\tdfd.numSamples = (dfd.descriptorBlockSize / 4 - sampleStart) / sampleWords;\n\n\tdfdReader._skip(7 /* bytesPlane[1-7] */);\n\n\tfor (let i = 0; i < dfd.numSamples; i ++) {\n\t\tdfd.samples[ i ] = {\n\t\t\tchannelID: dfdReader._skip(3 /* bitOffset + bitLength */)._nextUint8(),\n\t\t\t// ... remainder not implemented.\n\t\t};\n\t\tdfdReader._skip(12 /* samplePosition[0-3], lower, upper */);\n\t}\n\n\t///////////////////////////////////////////////////\n\t// Key/Value Data (KVD)\n\t///////////////////////////////////////////////////\n\n\t// Not implemented.\n\tconst kvd = {};\n\n\n\t///////////////////////////////////////////////////\n\t// Supercompression Global Data (SGD)\n\t///////////////////////////////////////////////////\n\n\tif (sgdByteLength <= 0) return container;\n\n\tconst sgdReader = new BufferReader(\n\t\tdata,\n\t\tsgdByteOffset,\n\t\tsgdByteLength,\n\t\ttrue\n\t);\n\n\tconst endpointCount = sgdReader._nextUint16();\n\tconst selectorCount = sgdReader._nextUint16();\n\tconst endpointsByteLength = sgdReader._nextUint32();\n\tconst selectorsByteLength = sgdReader._nextUint32();\n\tconst tablesByteLength = sgdReader._nextUint32();\n\tconst extendedByteLength = sgdReader._nextUint32();\n\n\tconst imageDescs = [];\n\tfor (let i = 0; i < container.levelCount; i ++) {\n\t\timageDescs.push({\n\t\t\timageFlags: sgdReader._nextUint32(),\n\t\t\trgbSliceByteOffset: sgdReader._nextUint32(),\n\t\t\trgbSliceByteLength: sgdReader._nextUint32(),\n\t\t\talphaSliceByteOffset: sgdReader._nextUint32(),\n\t\t\talphaSliceByteLength: sgdReader._nextUint32(),\n\t\t});\n\t}\n\n\tconst endpointsByteOffset = sgdByteOffset + sgdReader._offset;\n\tconst selectorsByteOffset = endpointsByteOffset + endpointsByteLength;\n\tconst tablesByteOffset = selectorsByteOffset + selectorsByteLength;\n\tconst extendedByteOffset = tablesByteOffset + tablesByteLength;\n\n\tconst endpointsData = new Uint8Array(data, endpointsByteOffset, endpointsByteLength);\n\tconst selectorsData = new Uint8Array(data, selectorsByteOffset, selectorsByteLength);\n\tconst tablesData = new Uint8Array(data, tablesByteOffset, tablesByteLength);\n\tconst extendedData = new Uint8Array(data, extendedByteOffset, extendedByteLength);\n\n\tcontainer.globalData = {\n\t\tendpointCount,\n\t\tselectorCount,\n\t\tendpointsByteLength,\n\t\tselectorsByteLength,\n\t\ttablesByteLength,\n\t\textendedByteLength,\n\t\timageDescs,\n\t\tendpointsData,\n\t\tselectorsData,\n\t\ttablesData,\n\t\textendedData,\n\t};\n\n\treturn container;\n}\n", "import { Container } from './container';\n\nexport function write(container: Container): Uint8Array {\n    return new Uint8Array();\n}\n"], "names": ["KTX2SupercompressionSchema", "Container", "this", "NONE", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "data", "byteOffset", "byteLength", "littleEndian", "_dataView", "DataView", "_<PERSON><PERSON><PERSON><PERSON>", "_offset", "_nextUint8", "value", "getUint8", "_nextUint16", "getUint16", "_nextUint32", "getUint32", "_nextUint64", "left", "right", "Math", "Number", "isSafeInteger", "console", "warn", "_skip", "bytes", "id", "Uint8Array", "Error", "container", "headerByteLength", "Uint32Array", "BYTES_PER_ELEMENT", "headerReader", "vkFormat", "typeSize", "pixelWidth", "pixelHeight", "pixelDepth", "layerCount", "faceCount", "levelCount", "supercompressionScheme", "dfdByteOffset", "dfdByteLength", "sgdByteOffset", "sgdByteLength", "level<PERSON><PERSON>er", "i", "levelIndex", "push", "uncompressedByteLength", "dfd<PERSON><PERSON>er", "dfd", "vendorId", "versionNumber", "descriptorBlockSize", "colorModel", "colorPrimaries", "transferFunction", "flags", "texelBlockDimension", "x", "y", "z", "w", "bytesPlane0", "numSamples", "samples", "channelID", "sgd<PERSON><PERSON>er", "endpointCount", "selectorCount", "endpointsByteLength", "selectorsByteLength", "tablesByteLength", "extendedByteLength", "imageDescs", "imageFlags", "rgbSliceByteOffset", "rgbSliceByteLength", "alphaSliceByteOffset", "alphaSliceByteLength", "endpointsByteOffset", "selectorsByteOffset", "tablesByteOffset", "extendedByteOffset", "endpointsData", "selectorsData", "tablesData", "extendedData", "globalData"], "mappings": "IASYA,GAAZ,SAAYA,GACXA,mBACAA,yBACAA,mBACAA,mBAJD,CAAYA,IAAAA,OCPCC,IAAAA,EAAb,WAEWC,cAAmB,EACnBA,eAAoB,EACpBA,iBAAsB,EACtBA,kBAAuB,EACvBA,iBAAsB,EACtBA,iBAAsB,EACtBA,gBAAqB,EACrBA,iBAAsB,EACtBA,4BAAyBF,EAA2BG,KAGpDD,gBAA0B,GAG1BA,0BAAmD,GAGnDA,cAA2B,GAG3BA,gBAAoC,MCxBlCE,aAKZ,WAAYC,EAAkBC,EAAoBC,EAAoBC,GACrEN,KAAKO,UAAY,IAAIC,SAAUL,EAAMC,EAAYC,GACjDL,KAAKS,cAAgBH,EACrBN,KAAKU,QAAU,EARjB,2BAWCC,WAAA,WACC,IAAMC,EAAQZ,KAAKO,UAAUM,SAAUb,KAAKU,SAE5C,OADAV,KAAKU,SAAW,EACTE,KAGRE,YAAA,WACC,IAAMF,EAAQZ,KAAKO,UAAUQ,UAAWf,KAAKU,QAASV,KAAKS,eAE3D,OADAT,KAAKU,SAAW,EACTE,KAGRI,YAAA,WACC,IAAMJ,EAAQZ,KAAKO,UAAUU,UAAWjB,KAAKU,QAASV,KAAKS,eAE3D,OADAT,KAAKU,SAAW,EACTE,KAGRM,YAAA,WAEC,IAAMC,EAAOnB,KAAKO,UAAUU,UAAWjB,KAAKU,QAASV,KAAKS,eACpDW,EAAQpB,KAAKO,UAAUU,UAAWjB,KAAKU,QAAU,EAAGV,KAAKS,eACzDG,EAAQZ,KAAKS,cAAgBU,EAASE,WAAK,IAAKD,EAAYC,WAAK,IAAKF,EAASC,EAOrF,OALKE,OAAOC,cAAcX,IACzBY,QAAQC,KAAKb,EAAQ,qDAGtBZ,KAAKU,SAAW,EACTE,KAGRc,MAAA,SAAMC,GAEL,OADA3B,KAAKU,SAAWiB,uDCxCGxB,GAGpB,IACIyB,EAAK,IAAIC,WAAW1B,EAAM,EADX,IAEnB,GAAc,MAAVyB,EAAG,IACM,KAAZA,EAAI,IACQ,KAAZA,EAAI,IACQ,KAAZA,EAAI,IACQ,KAAZA,EAAI,IACQ,KAAZA,EAAI,IACQ,KAAZA,EAAI,IACQ,MAAZA,EAAI,IACQ,KAAZA,EAAI,IACQ,KAAZA,EAAI,IACS,KAAbA,EAAI,KACS,KAAbA,EAAI,IAEJ,UAAUE,MAAO,+BAGlB,IAAMC,EAAY,IAAIhC,EAMhBiC,EAAmB,GAAKC,YAAYC,kBACpCC,EAAe,IAAIjC,EAAcC,EAzBpB,GAyBwC6B,GAAkB,GAE7ED,EAAUK,SAAWD,EAAanB,cAClCe,EAAUM,SAAWF,EAAanB,cAClCe,EAAUO,WAAaH,EAAanB,cACpCe,EAAUQ,YAAcJ,EAAanB,cACrCe,EAAUS,WAAaL,EAAanB,cACpCe,EAAUU,WAAaN,EAAanB,cACpCe,EAAUW,UAAYP,EAAanB,cACnCe,EAAUY,WAAaR,EAAanB,cACpCe,EAAUa,uBAAyBT,EAAanB,cAgBhD,IAdA,IAAM6B,EAAgBV,EAAanB,cAC7B8B,EAAgBX,EAAanB,cAG7B+B,GAFgBZ,EAAanB,cACbmB,EAAanB,cACbmB,EAAajB,eAC7B8B,EAAgBb,EAAajB,cAO7B+B,EAAc,IAAI/C,EAAaC,EAjDlB,GAiDuC6B,EADX,EAAvBD,EAAUY,WAAiB,GAC0C,GAEpFO,EAAI,EAAGA,EAAInB,EAAUY,WAAYO,IACzCnB,EAAUoB,WAAWC,KAAK,CACzBjD,KAAM,IAAI0B,WAAW1B,EAAM8C,EAAY/B,cAAe+B,EAAY/B,eAClEmC,uBAAwBJ,EAAY/B,gBAStC,IAAMoC,EAAY,IAAIpD,EAAaC,EAAM0C,EAAeC,GAAe,GAKjES,EAAgC,CACrCC,SAAUF,EAAU5B,MAAM,GAAmBZ,cAC7C2C,cAAeH,EAAU5B,MAAM,GAAwBZ,cACvD4C,oBAAqBJ,EAAUxC,cAC/B6C,WAAYL,EAAU3C,aACtBiD,eAAgBN,EAAU3C,aAC1BkD,iBAAkBP,EAAU3C,aAC5BmD,MAAOR,EAAU3C,aACjBoD,oBAAqB,CACpBC,EAAGV,EAAU3C,aAAe,EAC5BsD,EAAGX,EAAU3C,aAAe,EAC5BuD,EAAGZ,EAAU3C,aAAe,EAC5BwD,EAAGb,EAAU3C,aAAe,GAE7ByD,YAAad,EAAU3C,aACvB0D,WAAY,EACZC,QAAS,IAGVf,EAAIc,YAAcd,EAAIG,oBAAsB,EAtBxB,GACA,EAuBpBJ,EAAU5B,MAAM,GAEhB,IAAK,IAAIwB,EAAI,EAAGA,EAAIK,EAAIc,WAAYnB,IACnCK,EAAIe,QAASpB,GAAM,CAClBqB,UAAWjB,EAAU5B,MAAM,GAA+Bf,cAG3D2C,EAAU5B,MAAM,IAejB,GAAIsB,GAAiB,EAAG,OAAOjB,EAiB/B,IAfA,IAAMyC,EAAY,IAAItE,EACrBC,EACA4C,EACAC,GACA,GAGKyB,EAAgBD,EAAU1D,cAC1B4D,EAAgBF,EAAU1D,cAC1B6D,EAAsBH,EAAUxD,cAChC4D,EAAsBJ,EAAUxD,cAChC6D,EAAmBL,EAAUxD,cAC7B8D,EAAqBN,EAAUxD,cAE/B+D,EAAa,GACV7B,EAAI,EAAGA,EAAInB,EAAUY,WAAYO,IACzC6B,EAAW3B,KAAK,CACf4B,WAAYR,EAAUxD,cACtBiE,mBAAoBT,EAAUxD,cAC9BkE,mBAAoBV,EAAUxD,cAC9BmE,qBAAsBX,EAAUxD,cAChCoE,qBAAsBZ,EAAUxD,gBAIlC,IAAMqE,EAAsBtC,EAAgByB,EAAU9D,QAChD4E,EAAsBD,EAAsBV,EAC5CY,EAAmBD,EAAsBV,EACzCY,EAAqBD,EAAmBV,EAExCY,EAAgB,IAAI5D,WAAW1B,EAAMkF,EAAqBV,GAC1De,EAAgB,IAAI7D,WAAW1B,EAAMmF,EAAqBV,GAC1De,EAAa,IAAI9D,WAAW1B,EAAMoF,EAAkBV,GACpDe,EAAe,IAAI/D,WAAW1B,EAAMqF,EAAoBV,GAgB9D,OAdA/C,EAAU8D,WAAa,CACtBpB,cAAAA,EACAC,cAAAA,EACAC,oBAAAA,EACAC,oBAAAA,EACAC,iBAAAA,EACAC,mBAAAA,EACAC,WAAAA,EACAU,cAAAA,EACAC,cAAAA,EACAC,WAAAA,EACAC,aAAAA,GAGM7D,0BCvKcA,GAClB,WAAWF"}