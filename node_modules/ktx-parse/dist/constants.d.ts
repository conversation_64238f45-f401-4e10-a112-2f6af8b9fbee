export declare const KTX_WRITER: string;
export declare const NUL: Uint8Array;
export declare const KTX2_ID: number[];
export declare const HEADER_BYTE_LENGTH = 68;
export declare enum KTX2SupercompressionScheme {
    NONE = 0,
    BASISLZ = 1,
    ZSTD = 2,
    ZLIB = 3
}
export declare enum KTX2DataFormatType {
    BASICFORMAT = 0
}
export declare const KHR_DF_VENDORID_KHRONOS = 0;
export declare const KHR_DF_VERSION = 2;
export declare const KHR_DF_BLOCKSIZE = 40;
export declare const VK_FORMAT_UNDEFINED = 0;
export declare enum KTX2DataFormatModel {
    UNSPECIFIED = 0,
    ETC1S = 163,
    UASTC = 166
}
export declare enum KTX2DataFormatPrimaries {
    UNSPECIFIED = 0,
    SRGB = 1
}
export declare enum KTX2DataFormatTransfer {
    UNSPECIFIED = 0,
    LINEAR = 1,
    SRGB = 2,
    ITU = 3,
    NTSC = 4,
    SLOG = 5,
    SLOG2 = 6
}
export declare enum KTX2DataFormatFlags {
    ALPHA_STRAIGHT = 0,
    ALPHA_PREMULTIPLIED = 1
}
export declare enum KTX2DataFormatChannelETC1S {
    RGB = 0,
    RRR = 3,
    GGG = 4,
    AAA = 15
}
export declare enum KTX2DataFormatChannelUASTC {
    RGB = 0,
    RGBA = 3,
    RRR = 4,
    RRRG = 5
}
