var t,e,n,i,r,a,s,o,l=new Uint8Array([0]),f=[171,75,84,88,32,50,48,187,13,10,26,10];!function(t){t[t.NONE=0]="NONE",t[t.BASISLZ=1]="BASISLZ",t[t.ZSTD=2]="ZSTD",t[t.ZLIB=3]="ZLIB"}(t||(t={})),function(t){t[t.BASICFORMAT=0]="BASICFORMAT"}(e||(e={})),function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.ETC1S=163]="ETC1S",t[t.UASTC=166]="UASTC"}(n||(n={})),function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.SRGB=1]="SRGB"}(i||(i={})),function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.LINEAR=1]="LINEAR",t[t.SRGB=2]="SRGB",t[t.ITU=3]="ITU",t[t.NTSC=4]="NTSC",t[t.SLOG=5]="SLOG",t[t.SLOG2=6]="SLOG2"}(r||(r={})),function(t){t[t.ALPHA_STRAIGHT=0]="ALPHA_STRAIGHT",t[t.ALPHA_PREMULTIPLIED=1]="ALPHA_PREMULTIPLIED"}(a||(a={})),function(t){t[t.RGB=0]="RGB",t[t.RRR=3]="RRR",t[t.GGG=4]="GGG",t[t.AAA=15]="AAA"}(s||(s={})),function(t){t[t.RGB=0]="RGB",t[t.RGBA=3]="RGBA",t[t.RRR=4]="RRR",t[t.RRRG=5]="RRRG"}(o||(o={}));var U=function(){this.vkFormat=0,this.typeSize=1,this.pixelWidth=0,this.pixelHeight=0,this.pixelDepth=0,this.layerCount=0,this.faceCount=1,this.supercompressionScheme=t.NONE,this.levels=[],this.dataFormatDescriptor=[{vendorId:0,descriptorType:e.BASICFORMAT,versionNumber:2,descriptorBlockSize:40,colorModel:n.UNSPECIFIED,colorPrimaries:i.SRGB,transferFunction:i.SRGB,flags:a.ALPHA_STRAIGHT,texelBlockDimension:{x:4,y:4,z:1,w:1},bytesPlane:[],samples:[]}],this.keyValue={},this.globalData=null},h=function(){function t(t,e,n,i){this._dataView=new DataView(t.buffer,t.byteOffset+e,n),this._littleEndian=i,this._offset=0}var e=t.prototype;return e._nextUint8=function(){var t=this._dataView.getUint8(this._offset);return this._offset+=1,t},e._nextUint16=function(){var t=this._dataView.getUint16(this._offset,this._littleEndian);return this._offset+=2,t},e._nextUint32=function(){var t=this._dataView.getUint32(this._offset,this._littleEndian);return this._offset+=4,t},e._nextUint64=function(){var t=this._dataView.getUint32(this._offset,this._littleEndian),e=this._dataView.getUint32(this._offset+4,this._littleEndian),n=t+Math.pow(2,32)*e;return this._offset+=8,n},e._skip=function(t){return this._offset+=t,this},e._scan=function(t,e){void 0===e&&(e=0);for(var n=this._offset,i=0;this._dataView.getUint8(this._offset)!==e&&i<t;)i++,this._offset++;return i<t&&this._offset++,new Uint8Array(this._dataView.buffer,this._dataView.byteOffset+n,i)},t}();function c(){return(c=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t}).apply(this,arguments)}function u(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function _(t,e){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return u(t,void 0);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?u(t,void 0):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0;return function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}return(n=t[Symbol.iterator]()).next.bind(n)}function p(t){return"undefined"!=typeof TextEncoder?(new TextEncoder).encode(t):Buffer.from(t)}function g(t){return"undefined"!=typeof TextDecoder?(new TextDecoder).decode(t):Buffer.from(t).toString("utf8")}function y(t){for(var e,n=0,i=_(t);!(e=i()).done;)n+=e.value.byteLength;for(var r,a=new Uint8Array(n),s=0,o=_(t);!(r=o()).done;){var l=r.value;a.set(new Uint8Array(l),s),s+=l.byteLength}return a}var x={keepWriter:!1};exports.KTX2Container=U,exports.read=function(t){var e=new Uint8Array(t.buffer,t.byteOffset,f.length);if(e[0]!==f[0]||e[1]!==f[1]||e[2]!==f[2]||e[3]!==f[3]||e[4]!==f[4]||e[5]!==f[5]||e[6]!==f[6]||e[7]!==f[7]||e[8]!==f[8]||e[9]!==f[9]||e[10]!==f[10]||e[11]!==f[11])throw new Error("Missing KTX 2.0 identifier.");var n=new U,i=17*Uint32Array.BYTES_PER_ELEMENT,r=new h(t,f.length,i,!0);n.vkFormat=r._nextUint32(),n.typeSize=r._nextUint32(),n.pixelWidth=r._nextUint32(),n.pixelHeight=r._nextUint32(),n.pixelDepth=r._nextUint32(),n.layerCount=r._nextUint32(),n.faceCount=r._nextUint32();var a=r._nextUint32();n.supercompressionScheme=r._nextUint32();for(var s=r._nextUint32(),o=r._nextUint32(),l=r._nextUint32(),c=r._nextUint32(),u=r._nextUint64(),_=r._nextUint64(),p=new h(t,f.length+i,3*a*8,!0),y=0;y<a;y++)n.levels.push({levelData:new Uint8Array(t.buffer,t.byteOffset+p._nextUint64(),p._nextUint64()),uncompressedByteLength:p._nextUint64()});for(var x=new h(t,s,o,!0),b={vendorId:x._skip(4)._nextUint16(),descriptorType:x._nextUint16(),versionNumber:x._nextUint16(),descriptorBlockSize:x._nextUint16(),colorModel:x._nextUint8(),colorPrimaries:x._nextUint8(),transferFunction:x._nextUint8(),flags:x._nextUint8(),texelBlockDimension:{x:x._nextUint8()+1,y:x._nextUint8()+1,z:x._nextUint8()+1,w:x._nextUint8()+1},bytesPlane:[x._nextUint8(),x._nextUint8(),x._nextUint8(),x._nextUint8(),x._nextUint8(),x._nextUint8(),x._nextUint8(),x._nextUint8()],samples:[]},d=(b.descriptorBlockSize/4-6)/4,v=0;v<d;v++)b.samples[v]={bitOffset:x._nextUint16(),bitLength:x._nextUint8(),channelID:x._nextUint8(),samplePosition:[x._nextUint8(),x._nextUint8(),x._nextUint8(),x._nextUint8()],sampleLower:x._nextUint32(),sampleUpper:x._nextUint32()};n.dataFormatDescriptor.length=0,n.dataFormatDescriptor.push(b);for(var D=new h(t,l,c,!0);D._offset<c;){var m=D._nextUint32(),A=D._scan(m),w=g(A),S=D._scan(m-A.byteLength);n.keyValue[w]=w.match(/^ktx/i)?g(S):S,m%4&&D._skip(4-m%4)}if(_<=0)return n;for(var B=new h(t,u,_,!0),L=B._nextUint16(),I=B._nextUint16(),R=B._nextUint32(),E=B._nextUint32(),T=B._nextUint32(),O=B._nextUint32(),C=[],P=0;P<a;P++)C.push({imageFlags:B._nextUint32(),rgbSliceByteOffset:B._nextUint32(),rgbSliceByteLength:B._nextUint32(),alphaSliceByteOffset:B._nextUint32(),alphaSliceByteLength:B._nextUint32()});var F=u+B._offset,G=F+R,k=G+E,N=k+T,V=new Uint8Array(t.buffer,t.byteOffset+F,R),M=new Uint8Array(t.buffer,t.byteOffset+G,E),H=new Uint8Array(t.buffer,t.byteOffset+k,T),z=new Uint8Array(t.buffer,t.byteOffset+N,O);return n.globalData={endpointCount:L,selectorCount:I,imageDescs:C,endpointsData:V,selectorsData:M,tablesData:H,extendedData:z},n},exports.write=function(t,n){void 0===n&&(n={}),n=c({},x,n);var i=new ArrayBuffer(0);if(t.globalData){var r=new ArrayBuffer(20+5*t.globalData.imageDescs.length*4),a=new DataView(r);a.setUint16(0,t.globalData.endpointCount,!0),a.setUint16(2,t.globalData.selectorCount,!0),a.setUint32(4,t.globalData.endpointsData.byteLength,!0),a.setUint32(8,t.globalData.selectorsData.byteLength,!0),a.setUint32(12,t.globalData.tablesData.byteLength,!0),a.setUint32(16,t.globalData.extendedData.byteLength,!0);for(var s=0;s<t.globalData.imageDescs.length;s++){var o=t.globalData.imageDescs[s];a.setUint32(20+5*s*4+0,o.imageFlags,!0),a.setUint32(20+5*s*4+4,o.rgbSliceByteOffset,!0),a.setUint32(20+5*s*4+8,o.rgbSliceByteLength,!0),a.setUint32(20+5*s*4+12,o.alphaSliceByteOffset,!0),a.setUint32(20+5*s*4+16,o.alphaSliceByteLength,!0)}i=y([r,t.globalData.endpointsData,t.globalData.selectorsData,t.globalData.tablesData,t.globalData.extendedData])}var U=[],h=t.keyValue;for(var u in n.keepWriter||(h=c({},t.keyValue,{KTXwriter:"KTX-Parse v0.0.4"})),h){var _=h[u],g=p(u),b="string"==typeof _?p(_):_,d=g.byteLength+1+b.byteLength+1,v=d%4?4-d%4:0;U.push(y([new Uint32Array([d]),g,l,b,l,new Uint8Array(v).fill(0)]))}var D=y(U),m=new ArrayBuffer(44),A=new DataView(m);if(1!==t.dataFormatDescriptor.length||t.dataFormatDescriptor[0].descriptorType!==e.BASICFORMAT)throw new Error("Only BASICFORMAT Data Format Descriptor output supported.");var w=t.dataFormatDescriptor[0];A.setUint32(0,44,!0),A.setUint16(4,w.vendorId,!0),A.setUint16(6,w.descriptorType,!0),A.setUint16(8,w.versionNumber,!0),A.setUint16(10,w.descriptorBlockSize,!0),A.setUint8(12,w.colorModel),A.setUint8(13,w.colorPrimaries),A.setUint8(14,w.transferFunction),A.setUint8(15,w.flags),A.setUint8(16,w.texelBlockDimension.x-1),A.setUint8(17,w.texelBlockDimension.y-1),A.setUint8(18,w.texelBlockDimension.z-1),A.setUint8(19,w.texelBlockDimension.w-1);for(var S=0;S<8;S++)A.setUint8(20+S,w.bytesPlane[S]);for(var B=0;B<w.samples.length;B++){var L=w.samples[B],I=28+16*B;A.setUint16(I+0,L.bitOffset,!0),A.setUint8(I+2,L.bitLength),A.setUint8(I+3,L.channelID),A.setUint8(I+4,L.samplePosition[0]),A.setUint8(I+5,L.samplePosition[1]),A.setUint8(I+6,L.samplePosition[2]),A.setUint8(I+7,L.samplePosition[3]),A.setUint32(I+8,L.sampleLower,!0),A.setUint32(I+12,L.sampleUpper,!0)}var R=f.length+68+3*t.levels.length*8,E=R+m.byteLength,T=E+D.byteLength;T%8&&(T+=8-T%8);for(var O=[],C=new DataView(new ArrayBuffer(3*t.levels.length*8)),P=T+i.byteLength,F=0;F<t.levels.length;F++){var G=t.levels[F];O.push(G.levelData),C.setBigUint64(24*F+0,BigInt(P),!0),C.setBigUint64(24*F+8,BigInt(G.levelData.byteLength),!0),C.setBigUint64(24*F+16,BigInt(G.uncompressedByteLength),!0),P+=G.levelData.byteLength}var k=new ArrayBuffer(68),N=new DataView(k);return N.setUint32(0,t.vkFormat,!0),N.setUint32(4,t.typeSize,!0),N.setUint32(8,t.pixelWidth,!0),N.setUint32(12,t.pixelHeight,!0),N.setUint32(16,t.pixelDepth,!0),N.setUint32(20,t.layerCount,!0),N.setUint32(24,t.faceCount,!0),N.setUint32(28,t.levels.length,!0),N.setUint32(32,t.supercompressionScheme,!0),N.setUint32(36,R,!0),N.setUint32(40,m.byteLength,!0),N.setUint32(44,E,!0),N.setUint32(48,D.byteLength,!0),N.setBigUint64(52,BigInt(T),!0),N.setBigUint64(60,BigInt(i.byteLength),!0),new Uint8Array(y([new Uint8Array(f).buffer,k,C.buffer,m,D,new ArrayBuffer(T-(E+D.byteLength)),i].concat(O)))};
//# sourceMappingURL=ktx-parse.js.map
