{"version": 3, "file": "ktx-parse.modern.js", "sources": ["../src/constants.ts", "../src/container.ts", "../src/buffer-reader.ts", "../src/util.ts", "../src/read.ts", "../src/write.ts"], "sourcesContent": ["///////////////////////////////////////////////////\n// Common.\n///////////////////////////////////////////////////\n\n// Injected at compile time, from $npm_package_version.\ndeclare const PACKAGE_VERSION: string;\n\nexport const KTX_WRITER = `KTX-Parse v${PACKAGE_VERSION}`;\n\nexport const NUL = new Uint8Array([0x00]);\n\n\n///////////////////////////////////////////////////\n// KTX2 Header.\n///////////////////////////////////////////////////\n\nexport const KTX2_ID = [\n\t// '´', 'K', 'T', 'X', '2', '0', 'ª', '\\r', '\\n', '\\x1A', '\\n'\n\t0xAB, 0x4B, 0x54, 0x58, 0x20, 0x32, 0x30, 0xBB, 0x0D, 0x0A, 0x1A, 0x0A\n];\n\nexport const HEADER_BYTE_LENGTH = 68; // 13 * 4 + 2 * 8\n\nexport enum KTX2SupercompressionScheme {\n\tNONE = 0,\n\tBASISLZ = 1,\n\tZSTD = 2,\n\tZLIB = 3,\n};\n\n\n///////////////////////////////////////////////////\n// Data Format Descriptor (DFD).\n///////////////////////////////////////////////////\n\nexport enum KTX2DataFormatType {\n    BASICFORMAT = 0x00,\n};\n\nexport const KHR_DF_VENDORID_KHRONOS = 0;\n\nexport const KHR_DF_VERSION = 2;\n\nexport const KHR_DF_BLOCKSIZE = 40;\n\nexport const VK_FORMAT_UNDEFINED = 0;\n\nexport enum KTX2DataFormatModel {\n    UNSPECIFIED = 0,\n\tETC1S = 163,\n\tUASTC = 166,\n};\n\nexport enum KTX2DataFormatPrimaries {\n    UNSPECIFIED = 0,\n    SRGB = 1,\n};\n\nexport enum KTX2DataFormatTransfer {\n    UNSPECIFIED = 0,\n    LINEAR = 1,\n    SRGB = 2,\n    ITU = 3,\n    NTSC = 4,\n    SLOG = 5,\n    SLOG2 = 6,\n};\n\nexport enum KTX2DataFormatFlags {\n    ALPHA_STRAIGHT = 0,\n    ALPHA_PREMULTIPLIED = 1,\n};\n\nexport enum KTX2DataFormatChannelETC1S {\n    RGB = 0,\n    RRR = 3,\n    GGG = 4,\n    AAA = 15,\n};\n\nexport enum KTX2DataFormatChannelUASTC {\n    RGB = 0,\n    RGBA = 3,\n    RRR = 4,\n    RRRG = 5,\n};\n", "import { KHR_DF_BLOCKSIZE, KHR_DF_VENDORID_KHRONOS, KHR_DF_VERSION, KTX2DataFormatFlags, KTX2DataFormatModel, KTX2DataFormatPrimaries, KTX2DataFormatType, KTX2SupercompressionScheme, VK_FORMAT_UNDEFINED } from './constants';\n\n/**\n * Represents an unpacked KTX 2.0 texture container. Data for individual mip levels are stored in\n * the `.levels` array, typically compressed in Basis Universal formats. Additional properties\n * provide metadata required to process, transcode, and upload these textures.\n */\nexport class KTX2Container {\n\t/**\n\t * Specifies the image format using Vulkan VkFormat enum values. When using Basis Universal\n\t * texture formats, `vkFormat` must be VK_FORMAT_UNDEFINED.\n\t */\n\tpublic vkFormat = VK_FORMAT_UNDEFINED;\n\n\t/**\n\t * Size of the data type in bytes used to upload the data to a graphics API. When `vkFormat` is\n\t * VK_FORMAT_UNDEFINED, `typeSize` must be 1.\n\t */\n\tpublic typeSize: number = 1;\n\n\t/** Width of the texture image for level 0, in pixels. */\n\tpublic pixelWidth: number = 0;\n\n\t/** Height of the texture image for level 0, in pixels. */\n\tpublic pixelHeight: number = 0;\n\n\t/** Depth of the texture image for level 0, in pixels (3D textures only). */\n\tpublic pixelDepth: number = 0;\n\n\t/** Number of array elements (array textures only). */\n\tpublic layerCount: number = 0;\n\n\t/**\n\t * Number of cubemap faces. For cubemaps and cubemap arrays, `faceCount` must be 6. For all\n\t * other textures, `faceCount` must be 1. Cubemap faces are stored in +X, -X, +Y, -Y, +Z, -Z\n\t * order.\n\t */\n\tpublic faceCount: number = 1;\n\n\t/** Indicates which supercompression scheme has been applied to mip level images, if any. */\n\tpublic supercompressionScheme = KTX2SupercompressionScheme.NONE;\n\n\t/** Mip levels, ordered largest (original) to smallest (~1px). */\n\tpublic levels: KTX2Level[] = [];\n\n\t/** Data Format Descriptor. */\n\tpublic dataFormatDescriptor: KTX2DataFormatDescriptorBasicFormat[] = [{\n\t\tvendorId: KHR_DF_VENDORID_KHRONOS,\n\t\tdescriptorType: KTX2DataFormatType.BASICFORMAT,\n\t\tversionNumber: KHR_DF_VERSION,\n\t\tdescriptorBlockSize: KHR_DF_BLOCKSIZE,\n\t\tcolorModel: KTX2DataFormatModel.UNSPECIFIED,\n\t\tcolorPrimaries: KTX2DataFormatPrimaries.SRGB,\n\t\ttransferFunction: KTX2DataFormatPrimaries.SRGB,\n\t\tflags: KTX2DataFormatFlags.ALPHA_STRAIGHT,\n\t\ttexelBlockDimension: {x: 4, y: 4, z: 1, w: 1},\n\t\tbytesPlane: [],\n\t\tsamples: [],\n\t}];\n\n\t/** Key/Value Data. */\n\tpublic keyValue: {[key: string]: string | Uint8Array} = {};\n\n\t/** Supercompression Global Data. */\n\tpublic globalData: KTX2GlobalDataBasisLZ | null = null;\n}\n\n\n///////////////////////////////////////////////////\n// Mip Levels.\n///////////////////////////////////////////////////\n\nexport interface KTX2Level {\n\t/** Compressed data of the mip level. */\n\tlevelData: Uint8Array;\n\n\t/**\n\t * Size of the mip level after reflation from supercompression, if applicable. When\n\t * `supercompressionType` is BASISLZ, `uncompressedByteLength` must be 0. When\n\t * `supercompressionType` is `NONE`, `uncompressedByteLength` must match the `levelData` byte\n\t * length.\n\t *\n\t * _**NOTICE:** this implies that for formats such as UASTC, `uncompressedByteLength` may\n\t * indicate size after ZSTD reflation (and of transcoded ASTC data), but does _not_ indicate\n\t * size of decoded RGBA32 pixels._\n\t */\n\tuncompressedByteLength: number;\n};\n\n\n///////////////////////////////////////////////////\n// Data Format Descriptor (DFD).\n///////////////////////////////////////////////////\n\nexport interface KTX2DataFormatDescriptorBasicFormat {\n\tvendorId: number;\n\tdescriptorType: number;\n\tversionNumber: number;\n\tdescriptorBlockSize: number;\n\tcolorModel: number;\n\tcolorPrimaries: number;\n\ttransferFunction: number;\n\tflags: number;\n\ttexelBlockDimension: KTX2BasicFormatTexelBlockDimensions;\n\tbytesPlane: number[];\n\tsamples: KTX2BasicFormatSample[],\n};\n\nexport interface KTX2BasicFormatTexelBlockDimensions {\n\tx: number;\n\ty: number;\n\tz: number;\n\tw: number;\n};\n\nexport interface KTX2BasicFormatSample {\n\tbitOffset: number;\n\tbitLength: number;\n\tchannelID: number;\n\tsamplePosition: number[];\n\tsampleLower: number;\n\tsampleUpper: number;\n};\n\n\n///////////////////////////////////////////////////\n// Supercompression Global Data.\n///////////////////////////////////////////////////\n\nexport interface KTX2GlobalDataBasisLZ {\n\tendpointCount: number;\n\tselectorCount: number;\n\timageDescs: KTX2GlobalDataBasisLZImageDesc[];\n\tendpointsData: Uint8Array;\n\tselectorsData: Uint8Array;\n\ttablesData: Uint8Array;\n\textendedData: Uint8Array;\n};\n\ninterface KTX2GlobalDataBasisLZImageDesc {\n\timageFlags: number;\n\trgbSliceByteOffset: number;\n\trgbSliceByteLength: number;\n\talphaSliceByteOffset: number;\n\talphaSliceByteLength: number;\n};\n", "export class BufferReader {\n\tprivate _dataView: DataView;\n\tprivate _littleEndian: boolean;\n\tpublic _offset: number;\n\n\tconstructor(data: Uint8Array, byteOffset: number, byteLength: number, littleEndian: boolean) {\n\t\tthis._dataView = new DataView(data.buffer, data.byteOffset + byteOffset, byteLength);\n\t\tthis._littleEndian = littleEndian;\n\t\tthis._offset = 0;\n\t}\n\n\t_nextUint8() {\n\t\tconst value = this._dataView.getUint8(this._offset);\n\t\tthis._offset += 1;\n\t\treturn value;\n\t}\n\n\t_nextUint16() {\n\t\tconst value = this._dataView.getUint16(this._offset, this._littleEndian);\n\t\tthis._offset += 2;\n\t\treturn value;\n\t}\n\n\t_nextUint32() {\n\t\tconst value = this._dataView.getUint32(this._offset, this._littleEndian);\n\t\tthis._offset += 4;\n\t\treturn value;\n\t}\n\n\t_nextUint64() {\n\t\tconst left = this._dataView.getUint32(this._offset, this._littleEndian);\n\t\tconst right = this._dataView.getUint32(this._offset + 4, this._littleEndian);\n\t\t// TODO(cleanup): Just test this...\n\t\t// const value = this._littleEndian ? left + (2 ** 32 * right) : (2 ** 32 * left) + right;\n\t\tconst value = left + (2 ** 32 * right);\n\t\tthis._offset += 8;\n\t\treturn value;\n\t}\n\n\t_skip(bytes: number) {\n\t\tthis._offset += bytes;\n\t\treturn this;\n\t}\n\n\t_scan(maxByteLength: number, term: number = 0x00): Uint8Array {\n\t\tconst byteOffset = this._offset;\n\t\tlet byteLength = 0;\n\t\twhile (this._dataView.getUint8(this._offset) !== term && byteLength < maxByteLength) {\n\t\t\tbyteLength++;\n\t\t\tthis._offset++;\n\t\t}\n\n\t\tif (byteLength < maxByteLength) this._offset++;\n\n\t\treturn new Uint8Array(\n\t\t\tthis._dataView.buffer,\n\t\t\tthis._dataView.byteOffset + byteOffset,\n\t\t\tbyteLength\n\t\t);\n\t}\n}\n", "\n/** Encodes text to an ArrayBuffer. */\nexport function encodeText(text: string): Uint8Array {\n\tif (typeof TextEncoder !== 'undefined') {\n\t\treturn new TextEncoder().encode(text);\n\t}\n\treturn Buffer.from(text);\n}\n\n/** Decodes an ArrayBuffer to text. */\nexport function decodeText(buffer: Uint8Array): string {\n\tif (typeof TextDecoder !== 'undefined') {\n\t\treturn new TextDecoder().decode(buffer);\n\t}\n\treturn Buffer.from(buffer).toString('utf8');\n}\n\n/** Concatenates N ArrayBuffers. */\nexport function concat (buffers: (ArrayBuffer | Uint8Array)[]): Uint8Array {\n\tlet totalByteLength = 0;\n\tfor (const buffer of buffers) {\n\t\ttotalByteLength += buffer.byteLength;\n\t}\n\n\tconst result = new Uint8Array(totalByteLength);\n\tlet byteOffset = 0;\n\n\tfor (const buffer of buffers) {\n\t\tresult.set(new Uint8Array(buffer), byteOffset);\n\t\tbyteOffset += buffer.byteLength;\n\t}\n\n\treturn result;\n}", "import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './buffer-reader';\nimport { KTX2_ID } from './constants';\nimport { KTX2Container, KTX2DataFormatDescriptorBasicFormat } from './container';\nimport { decodeText } from './util';\n\n/**\n * Parses a KTX 2.0 file, returning an unpacked {@link KTX2Container} instance with all associated\n * data. The container's mip levels and other binary data are pointers into the original file, not\n * copies, so the original file should not be overwritten after reading.\n *\n * @param data Bytes of KTX 2.0 file, as Uint8Array or Buffer.\n */\nexport function read(data: Uint8Array): KTX2Container {\n\n\t///////////////////////////////////////////////////\n\t// KTX 2.0 Identifier.\n\t///////////////////////////////////////////////////\n\n\tconst id = new Uint8Array(data.buffer, data.byteOffset, KTX2_ID.length);\n\tif (id[0] !== KTX2_ID[0] || // '´'\n\t\tid[1] !== KTX2_ID[1] || // 'K'\n\t\tid[2] !== KTX2_ID[2] || // 'T'\n\t\tid[3] !== KTX2_ID[3] || // 'X'\n\t\tid[4] !== KTX2_ID[4] || // ' '\n\t\tid[5] !== KTX2_ID[5] || // '2'\n\t\tid[6] !== KTX2_ID[6] || // '0'\n\t\tid[7] !== KTX2_ID[7] || // 'ª'\n\t\tid[8] !== KTX2_ID[8] || // '\\r'\n\t\tid[9] !== KTX2_ID[9] || // '\\n'\n\t\tid[10] !== KTX2_ID[10] || // '\\x1A'\n\t\tid[11] !== KTX2_ID[11] // '\\n'\n\t) {\n\t\tthrow new Error('Missing KTX 2.0 identifier.');\n\t}\n\n\tconst container = new KTX2Container();\n\n\t///////////////////////////////////////////////////\n\t// Header.\n\t///////////////////////////////////////////////////\n\n\tconst headerByteLength = 17 * Uint32Array.BYTES_PER_ELEMENT;\n\tconst headerReader = new BufferReader(data, KTX2_ID.length, headerByteLength, true);\n\n\tcontainer.vkFormat = headerReader._nextUint32();\n\tcontainer.typeSize = headerReader._nextUint32();\n\tcontainer.pixelWidth = headerReader._nextUint32();\n\tcontainer.pixelHeight = headerReader._nextUint32();\n\tcontainer.pixelDepth = headerReader._nextUint32();\n\tcontainer.layerCount = headerReader._nextUint32();\n\tcontainer.faceCount = headerReader._nextUint32();\n\n\tconst levelCount = headerReader._nextUint32();\n\n\tcontainer.supercompressionScheme = headerReader._nextUint32();\n\n\tconst dfdByteOffset = headerReader._nextUint32();\n\tconst dfdByteLength = headerReader._nextUint32();\n\tconst kvdByteOffset = headerReader._nextUint32();\n\tconst kvdByteLength = headerReader._nextUint32();\n\tconst sgdByteOffset = headerReader._nextUint64();\n\tconst sgdByteLength = headerReader._nextUint64();\n\n\t///////////////////////////////////////////////////\n\t// Level Index.\n\t///////////////////////////////////////////////////\n\n\tconst levelByteLength = levelCount * 3 * 8;\n\tconst levelReader = new BufferReader(data, KTX2_ID.length + headerByteLength, levelByteLength, true);\n\n\tfor (let i = 0; i < levelCount; i ++) {\n\t\tcontainer.levels.push({\n\t\t\tlevelData: new Uint8Array(data.buffer, data.byteOffset + levelReader._nextUint64(), levelReader._nextUint64()),\n\t\t\tuncompressedByteLength: levelReader._nextUint64(),\n\t\t});\n\t}\n\n\n\t///////////////////////////////////////////////////\n\t// Data Format Descriptor (DFD).\n\t///////////////////////////////////////////////////\n\n\tconst dfdReader = new BufferReader(data, dfdByteOffset, dfdByteLength, true);\n\n\tconst dfd: KTX2DataFormatDescriptorBasicFormat = {\n\t\tvendorId: dfdReader._skip(4 /* totalSize */)._nextUint16(),\n\t\tdescriptorType: dfdReader._nextUint16(),\n\t\tversionNumber: dfdReader._nextUint16(),\n\t\tdescriptorBlockSize: dfdReader._nextUint16(),\n\t\tcolorModel: dfdReader._nextUint8(),\n\t\tcolorPrimaries: dfdReader._nextUint8(),\n\t\ttransferFunction: dfdReader._nextUint8(),\n\t\tflags: dfdReader._nextUint8(),\n\t\ttexelBlockDimension: {\n\t\t\tx: dfdReader._nextUint8() + 1,\n\t\t\ty: dfdReader._nextUint8() + 1,\n\t\t\tz: dfdReader._nextUint8() + 1,\n\t\t\tw: dfdReader._nextUint8() + 1,\n\t\t},\n\t\tbytesPlane: [\n\t\t\tdfdReader._nextUint8(),\n\t\t\tdfdReader._nextUint8(),\n\t\t\tdfdReader._nextUint8(),\n\t\t\tdfdReader._nextUint8(),\n\t\t\tdfdReader._nextUint8(),\n\t\t\tdfdReader._nextUint8(),\n\t\t\tdfdReader._nextUint8(),\n\t\t\tdfdReader._nextUint8(),\n\t\t],\n\t\tsamples: [],\n\t};\n\n\tconst sampleStart = 6;\n\tconst sampleWords = 4;\n\tconst numSamples = (dfd.descriptorBlockSize / 4 - sampleStart) / sampleWords;\n\n\tfor (let i = 0; i < numSamples; i ++) {\n\t\tdfd.samples[ i ] = {\n\t\t\tbitOffset: dfdReader._nextUint16(),\n\t\t\tbitLength: dfdReader._nextUint8(),\n\t\t\tchannelID: dfdReader._nextUint8(),\n\t\t\tsamplePosition: [\n\t\t\t\tdfdReader._nextUint8(),\n\t\t\t\tdfdReader._nextUint8(),\n\t\t\t\tdfdReader._nextUint8(),\n\t\t\t\tdfdReader._nextUint8(),\n\t\t\t],\n\t\t\tsampleLower: dfdReader._nextUint32(),\n\t\t\tsampleUpper: dfdReader._nextUint32(),\n\t\t};\n\t}\n\n\tcontainer.dataFormatDescriptor.length = 0;\n\tcontainer.dataFormatDescriptor.push(dfd);\n\n\n\t///////////////////////////////////////////////////\n\t// Key/Value Data (KVD).\n\t///////////////////////////////////////////////////\n\n\tconst kvdReader = new BufferReader(data, kvdByteOffset, kvdByteLength, true);\n\n\twhile (kvdReader._offset < kvdByteLength) {\n\t\tconst keyValueByteLength = kvdReader._nextUint32();\n\t\tconst keyData = kvdReader._scan(keyValueByteLength);\n\t\tconst key = decodeText(keyData);\n\t\tconst valueData = kvdReader._scan(keyValueByteLength - keyData.byteLength);\n\t\tcontainer.keyValue[key] = key.match(/^ktx/i) ? decodeText(valueData) : valueData;\n\n\t\t// 4-byte alignment.\n\t\tif (keyValueByteLength % 4) kvdReader._skip(4 - (keyValueByteLength % 4));\n\t}\n\n\n\t///////////////////////////////////////////////////\n\t// Supercompression Global Data (SGD).\n\t///////////////////////////////////////////////////\n\n\tif (sgdByteLength <= 0) return container;\n\n\tconst sgdReader = new BufferReader(data, sgdByteOffset, sgdByteLength, true);\n\n\tconst endpointCount = sgdReader._nextUint16();\n\tconst selectorCount = sgdReader._nextUint16();\n\tconst endpointsByteLength = sgdReader._nextUint32();\n\tconst selectorsByteLength = sgdReader._nextUint32();\n\tconst tablesByteLength = sgdReader._nextUint32();\n\tconst extendedByteLength = sgdReader._nextUint32();\n\n\tconst imageDescs = [];\n\tfor (let i = 0; i < levelCount; i ++) {\n\t\timageDescs.push({\n\t\t\timageFlags: sgdReader._nextUint32(),\n\t\t\trgbSliceByteOffset: sgdReader._nextUint32(),\n\t\t\trgbSliceByteLength: sgdReader._nextUint32(),\n\t\t\talphaSliceByteOffset: sgdReader._nextUint32(),\n\t\t\talphaSliceByteLength: sgdReader._nextUint32(),\n\t\t});\n\t}\n\n\tconst endpointsByteOffset = sgdByteOffset + sgdReader._offset;\n\tconst selectorsByteOffset = endpointsByteOffset + endpointsByteLength;\n\tconst tablesByteOffset = selectorsByteOffset + selectorsByteLength;\n\tconst extendedByteOffset = tablesByteOffset + tablesByteLength;\n\n\tconst endpointsData = new Uint8Array(data.buffer, data.byteOffset + endpointsByteOffset, endpointsByteLength);\n\tconst selectorsData = new Uint8Array(data.buffer, data.byteOffset + selectorsByteOffset, selectorsByteLength);\n\tconst tablesData = new Uint8Array(data.buffer, data.byteOffset + tablesByteOffset, tablesByteLength);\n\tconst extendedData = new Uint8Array(data.buffer, data.byteOffset + extendedByteOffset, extendedByteLength);\n\n\tcontainer.globalData = {\n\t\tendpointCount,\n\t\tselectorCount,\n\t\timageDescs,\n\t\tendpointsData,\n\t\tselectorsData,\n\t\ttablesData,\n\t\textendedData,\n\t};\n\n\treturn container;\n}\n", "import { HEADER_BYTE_LENGTH, KTX2DataFormatType, KTX2_ID, KTX_WRITER, NUL } from './constants';\nimport { KTX2Container } from './container';\nimport { concat, encodeText } from './util';\n\ninterface WriteOptions {keepWriter?: boolean};\nconst DEFAULT_OPTIONS: WriteOptions = {keepWriter: false};\n\n/**\n * Serializes a {@link KTX2Container} instance to a KTX 2.0 file. Mip levels and other binary data\n * are copied into the resulting Uint8Array, so the original container can safely be edited or\n * destroyed after it is serialized.\n *\n * Options:\n * - keepWriter: If true, 'KTXWriter' key/value field is written as provided by the container.\n * \t\tOtherwise, a string for the current ktx-parse version is generated. Default: false.\n *\n * @param container\n * @param options\n */\nexport function write(container: KTX2Container, options: WriteOptions = {}): Uint8Array {\n\toptions = {...DEFAULT_OPTIONS, ...options};\n\n\t///////////////////////////////////////////////////\n\t// Supercompression Global Data (SGD).\n\t///////////////////////////////////////////////////\n\n\tlet sgdBuffer = new ArrayBuffer(0);\n\tif (container.globalData) {\n\t\tconst sgdHeaderBuffer = new ArrayBuffer(20 + container.globalData.imageDescs.length * 5 * 4);\n\t\tconst sgdHeaderView = new DataView(sgdHeaderBuffer);\n\t\tsgdHeaderView.setUint16(0, container.globalData.endpointCount, true);\n\t\tsgdHeaderView.setUint16(2, container.globalData.selectorCount, true);\n\t\tsgdHeaderView.setUint32(4, container.globalData.endpointsData.byteLength, true);\n\t\tsgdHeaderView.setUint32(8, container.globalData.selectorsData.byteLength, true);\n\t\tsgdHeaderView.setUint32(12, container.globalData.tablesData.byteLength, true);\n\t\tsgdHeaderView.setUint32(16, container.globalData.extendedData.byteLength, true);\n\n\t\tfor (let i = 0; i < container.globalData.imageDescs.length; i++) {\n\t\t\tconst imageDesc = container.globalData.imageDescs[i];\n\t\t\tsgdHeaderView.setUint32(20 + i * 5 * 4 + 0, imageDesc.imageFlags, true);\n\t\t\tsgdHeaderView.setUint32(20 + i * 5 * 4 + 4, imageDesc.rgbSliceByteOffset, true);\n\t\t\tsgdHeaderView.setUint32(20 + i * 5 * 4 + 8, imageDesc.rgbSliceByteLength, true);\n\t\t\tsgdHeaderView.setUint32(20 + i * 5 * 4 + 12, imageDesc.alphaSliceByteOffset, true);\n\t\t\tsgdHeaderView.setUint32(20 + i * 5 * 4 + 16, imageDesc.alphaSliceByteLength, true);\n\t\t}\n\n\t\tsgdBuffer = concat([\n\t\t\tsgdHeaderBuffer,\n\t\t\tcontainer.globalData.endpointsData,\n\t\t\tcontainer.globalData.selectorsData,\n\t\t\tcontainer.globalData.tablesData,\n\t\t\tcontainer.globalData.extendedData,\n\t\t]);\n\t}\n\n\n\t///////////////////////////////////////////////////\n\t// Key/Value Data (KVD).\n\t///////////////////////////////////////////////////\n\n\tconst keyValueData: Uint8Array[] = [];\n\tlet keyValue = container.keyValue;\n\n\tif (!options.keepWriter) {\n\t\tkeyValue = {...container.keyValue, 'KTXwriter': KTX_WRITER};\n\t}\n\n\tfor (const key in keyValue) {\n\t\tconst value = keyValue[key];\n\t\tconst keyData = encodeText(key);\n\t\tconst valueData = typeof value === 'string' ? encodeText(value) : value;\n\t\tconst kvByteLength = keyData.byteLength + 1 + valueData.byteLength + 1;\n\t\tconst kvPadding = kvByteLength % 4 ? (4 - (kvByteLength % 4)) : 0; // align(4)\n\t\tkeyValueData.push(concat([\n\t\t\tnew Uint32Array([kvByteLength]),\n\t\t\tkeyData,\n\t\t\tNUL,\n\t\t\tvalueData,\n\t\t\tNUL,\n\t\t\tnew Uint8Array(kvPadding).fill(0x00), // align(4)\n\t\t]));\n\t}\n\n\tconst kvdBuffer = concat(keyValueData);\n\n\n\t///////////////////////////////////////////////////\n\t// Data Format Descriptor (DFD).\n\t///////////////////////////////////////////////////\n\n\tconst dfdBuffer = new ArrayBuffer(44);\n\tconst dfdView = new DataView(dfdBuffer);\n\n\tif (container.dataFormatDescriptor.length !== 1\n\t\t\t|| container.dataFormatDescriptor[0].descriptorType !== KTX2DataFormatType.BASICFORMAT) {\n\t\tthrow new Error('Only BASICFORMAT Data Format Descriptor output supported.');\n\t}\n\n\tconst dfd = container.dataFormatDescriptor[0];\n\n\tdfdView.setUint32(0, 44, true);\n\tdfdView.setUint16(4, dfd.vendorId, true);\n\tdfdView.setUint16(6, dfd.descriptorType, true);\n\tdfdView.setUint16(8, dfd.versionNumber, true);\n\tdfdView.setUint16(10, dfd.descriptorBlockSize, true);\n\n\tdfdView.setUint8(12, dfd.colorModel);\n\tdfdView.setUint8(13, dfd.colorPrimaries);\n\tdfdView.setUint8(14, dfd.transferFunction);\n\tdfdView.setUint8(15, dfd.flags);\n\n\tdfdView.setUint8(16, dfd.texelBlockDimension.x - 1);\n\tdfdView.setUint8(17, dfd.texelBlockDimension.y - 1);\n\tdfdView.setUint8(18, dfd.texelBlockDimension.z - 1);\n\tdfdView.setUint8(19, dfd.texelBlockDimension.w - 1);\n\n\tfor (let i = 0; i < 8; i++) dfdView.setUint8(20 + i, dfd.bytesPlane[i]);\n\n\tfor (let i = 0; i < dfd.samples.length; i++) {\n\t\tconst sample = dfd.samples[i];\n\t\tconst sampleByteOffset = 28 + i * 16;\n\n\t\tdfdView.setUint16(sampleByteOffset + 0, sample.bitOffset, true);\n\t\tdfdView.setUint8(sampleByteOffset + 2, sample.bitLength);\n\t\tdfdView.setUint8(sampleByteOffset + 3, sample.channelID);\n\n\t\tdfdView.setUint8(sampleByteOffset + 4, sample.samplePosition[0]);\n\t\tdfdView.setUint8(sampleByteOffset + 5, sample.samplePosition[1]);\n\t\tdfdView.setUint8(sampleByteOffset + 6, sample.samplePosition[2]);\n\t\tdfdView.setUint8(sampleByteOffset + 7, sample.samplePosition[3]);\n\n\t\tdfdView.setUint32(sampleByteOffset + 8, sample.sampleLower, true);\n\t\tdfdView.setUint32(sampleByteOffset + 12, sample.sampleUpper, true);\n\t}\n\n\n\t///////////////////////////////////////////////////\n\t// Data alignment.\n\t///////////////////////////////////////////////////\n\n\tconst dfdByteOffset = KTX2_ID.length + HEADER_BYTE_LENGTH + container.levels.length * 3 * 8;\n\tconst kvdByteOffset = dfdByteOffset + dfdBuffer.byteLength;\n\tlet sgdByteOffset = kvdByteOffset + kvdBuffer.byteLength;\n\tif (sgdByteOffset % 8) sgdByteOffset += 8 - (sgdByteOffset % 8); // align(8)\n\n\n\t///////////////////////////////////////////////////\n\t// Level Index.\n\t///////////////////////////////////////////////////\n\n\tconst levelData: Uint8Array[] = [];\n\tconst levelIndex = new DataView(new ArrayBuffer(container.levels.length * 3 * 8));\n\n\tlet levelDataByteOffset = sgdByteOffset + sgdBuffer.byteLength;\n\tfor (let i = 0; i < container.levels.length; i++) {\n\t\tconst level = container.levels[i];\n\t\tlevelData.push(level.levelData);\n\t\tlevelIndex.setBigUint64(i * 24 + 0, BigInt(levelDataByteOffset), true);\n\t\tlevelIndex.setBigUint64(i * 24 + 8, BigInt(level.levelData.byteLength), true);\n\t\tlevelIndex.setBigUint64(i * 24 + 16, BigInt(level.uncompressedByteLength), true);\n\t\tlevelDataByteOffset += level.levelData.byteLength;\n\t}\n\n\n\t///////////////////////////////////////////////////\n\t// Header.\n\t///////////////////////////////////////////////////\n\n\tconst headerBuffer = new ArrayBuffer(HEADER_BYTE_LENGTH);\n\tconst headerView = new DataView(headerBuffer);\n\theaderView.setUint32(0, container.vkFormat, true);\n\theaderView.setUint32(4, container.typeSize, true);\n\theaderView.setUint32(8, container.pixelWidth, true);\n\theaderView.setUint32(12, container.pixelHeight, true);\n\theaderView.setUint32(16, container.pixelDepth, true);\n\theaderView.setUint32(20, container.layerCount, true);\n\theaderView.setUint32(24, container.faceCount, true);\n\theaderView.setUint32(28, container.levels.length, true);\n\theaderView.setUint32(32, container.supercompressionScheme, true);\n\n\theaderView.setUint32(36, dfdByteOffset, true);\n\theaderView.setUint32(40, dfdBuffer.byteLength, true);\n\theaderView.setUint32(44, kvdByteOffset, true);\n\theaderView.setUint32(48, kvdBuffer.byteLength, true);\n\theaderView.setBigUint64(52, BigInt(sgdByteOffset), true);\n\theaderView.setBigUint64(60, BigInt(sgdBuffer.byteLength), true);\n\n\n\t///////////////////////////////////////////////////\n\t// Compose.\n\t///////////////////////////////////////////////////\n\n\treturn new Uint8Array(concat([\n\t\tnew Uint8Array(KTX2_ID).buffer,\n\t\theaderBuffer,\n\t\tlevelIndex.buffer,\n\t\tdfdBuffer,\n\t\tkvdBuffer,\n\t\tnew ArrayBuffer(sgdByteOffset - (kvdByteOffset + kvdBuffer.byteLength)), // align(8)\n\t\tsgdBuffer,\n\t\t...levelData,\n\t]));\n}\n\n"], "names": ["NUL", "Uint8Array", "KTX2_ID", "KTX2SupercompressionScheme", "KTX2DataFormatType", "KTX2DataFormatModel", "KTX2DataFormatPrimaries", "KTX2DataFormatTransfer", "KTX2DataFormatFlags", "KTX2DataFormatChannelETC1S", "KTX2DataFormatChannelUASTC", "KTX2Container", "constructor", "this", "NONE", "vendorId", "descriptorType", "BASICFORMAT", "versionNumber", "descriptorBlockSize", "colorModel", "UNSPECIFIED", "colorPrimaries", "SRGB", "transferFunction", "flags", "ALPHA_STRAIGHT", "texelBlockDimension", "x", "y", "z", "w", "bytesPlane", "samples", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "data", "byteOffset", "byteLength", "littleEndian", "_dataView", "DataView", "buffer", "_<PERSON><PERSON><PERSON><PERSON>", "_offset", "_nextUint8", "value", "getUint8", "_nextUint16", "getUint16", "_nextUint32", "getUint32", "_nextUint64", "_skip", "bytes", "_scan", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "term", "encodeText", "text", "TextEncoder", "encode", "<PERSON><PERSON><PERSON>", "from", "decodeText", "TextDecoder", "decode", "toString", "concat", "buffers", "totalByteLength", "result", "set", "read", "id", "length", "Error", "container", "headerByteLength", "Uint32Array", "BYTES_PER_ELEMENT", "headerReader", "vkFormat", "typeSize", "pixelWidth", "pixelHeight", "pixelDepth", "layerCount", "faceCount", "levelCount", "supercompressionScheme", "dfdByteOffset", "dfdByteLength", "kvdByteOffset", "kvdByteLength", "sgdByteOffset", "sgdByteLength", "level<PERSON><PERSON>er", "i", "levels", "push", "levelData", "uncompressedByteLength", "dfd<PERSON><PERSON>er", "dfd", "numSamples", "bitOffset", "bitLength", "channelID", "samplePosition", "sampleLower", "sampleUpper", "dataFormatDescriptor", "kvdReader", "keyValueByte<PERSON>ength", "keyData", "key", "valueData", "keyValue", "match", "sgd<PERSON><PERSON>er", "endpointCount", "selectorCount", "endpointsByteLength", "selectorsByteLength", "tablesByteLength", "extendedByteLength", "imageDescs", "imageFlags", "rgbSliceByteOffset", "rgbSliceByteLength", "alphaSliceByteOffset", "alphaSliceByteLength", "endpointsByteOffset", "selectorsByteOffset", "tablesByteOffset", "extendedByteOffset", "endpointsData", "selectorsData", "tablesData", "extendedData", "globalData", "DEFAULT_OPTIONS", "keepWriter", "write", "options", "sgdBuffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sgdHeaderBuffer", "sgdHeaderView", "setUint16", "setUint32", "imageDesc", "keyValueData", "KTXwriter", "kvByteLength", "kvPadding", "fill", "kvdBuffer", "dfdBuffer", "dfdView", "setUint8", "sample", "sampleByteOffset", "levelIndex", "levelDataByteOffset", "level", "setBigUint64", "BigInt", "headerBuffer", "headerView"], "mappings": "MASaA,EAAM,IAAIC,WAAW,CAAC,IAOtBC,EAAU,CAEtB,IAAM,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,IAAM,GAAM,GAAM,GAAM,QAKvDC,EAYAC,EAYAC,EAMAC,EAKAC,EAUAC,EAKAC,EAOAC,GAzDZ,SAAYP,GACXA,mBACAA,yBACAA,mBACAA,mBAJD,CAAYA,IAAAA,OAYZ,SAAYC,GACRA,iCADJ,CAAYA,IAAAA,OAYZ,SAAYC,GACRA,iCACHA,uBACAA,uBAHD,CAAYA,IAAAA,OAMZ,SAAYC,GACRA,iCACAA,mBAFJ,CAAYA,IAAAA,OAKZ,SAAYC,GACRA,iCACAA,uBACAA,mBACAA,iBACAA,mBACAA,mBACAA,qBAPJ,CAAYA,IAAAA,OAUZ,SAAYC,GACRA,uCACAA,iDAFJ,CAAYA,IAAAA,OAKZ,SAAYC,GACRA,iBACAA,iBACAA,iBACAA,kBAJJ,CAAYA,IAAAA,OAOZ,SAAYC,GACRA,iBACAA,mBACAA,iBACAA,mBAJJ,CAAYA,IAAAA,aCzECC,EAAbC,cAKQC,cDiC2B,EC3B3BA,cAAmB,EAGnBA,gBAAqB,EAGrBA,iBAAsB,EAGtBA,gBAAqB,EAGrBA,gBAAqB,EAOrBA,eAAoB,EAGpBA,4BAAyBV,EAA2BW,KAGpDD,YAAsB,GAGtBA,0BAA8D,CAAC,CACrEE,SDRqC,ECSrCC,eAAgBZ,EAAmBa,YACnCC,cDR4B,ECS5BC,oBDP8B,GCQ9BC,WAAYf,EAAoBgB,YAChCC,eAAgBhB,EAAwBiB,KACxCC,iBAAkBlB,EAAwBiB,KAC1CE,MAAOjB,EAAoBkB,eAC3BC,oBAAqB,CAACC,EAAG,EAAGC,EAAG,EAAGC,EAAG,EAAGC,EAAG,GAC3CC,WAAY,GACZC,QAAS,KAIHpB,cAAiD,GAGjDA,gBAA2C,YChEtCqB,EAKZtB,YAAYuB,EAAkBC,EAAoBC,EAAoBC,GACrEzB,KAAK0B,UAAY,IAAIC,SAASL,EAAKM,OAAQN,EAAKC,WAAaA,EAAYC,GACzExB,KAAK6B,cAAgBJ,EACrBzB,KAAK8B,QAAU,EAGhBC,aACC,MAAMC,EAAQhC,KAAK0B,UAAUO,SAASjC,KAAK8B,SAE3C,OADA9B,KAAK8B,SAAW,EACTE,EAGRE,cACC,MAAMF,EAAQhC,KAAK0B,UAAUS,UAAUnC,KAAK8B,QAAS9B,KAAK6B,eAE1D,OADA7B,KAAK8B,SAAW,EACTE,EAGRI,cACC,MAAMJ,EAAQhC,KAAK0B,UAAUW,UAAUrC,KAAK8B,QAAS9B,KAAK6B,eAE1D,OADA7B,KAAK8B,SAAW,EACTE,EAGRM,cACC,MAIMN,EAJOhC,KAAK0B,UAAUW,UAAUrC,KAAK8B,QAAS9B,KAAK6B,eAInC,GAAK,GAHb7B,KAAK0B,UAAUW,UAAUrC,KAAK8B,QAAU,EAAG9B,KAAK6B,eAK9D,OADA7B,KAAK8B,SAAW,EACTE,EAGRO,MAAMC,GAEL,OADAxC,KAAK8B,SAAWU,OAIjBC,MAAMC,EAAuBC,EAAe,GAC3C,MAAMpB,EAAavB,KAAK8B,QACxB,IAAIN,EAAa,EACjB,KAAOxB,KAAK0B,UAAUO,SAASjC,KAAK8B,WAAaa,GAAQnB,EAAakB,GACrElB,IACAxB,KAAK8B,UAKN,OAFIN,EAAakB,GAAe1C,KAAK8B,cAE1B1C,WACVY,KAAK0B,UAAUE,OACf5B,KAAK0B,UAAUH,WAAaA,EAC5BC,aCvDaoB,EAAWC,GAC1B,MAA2B,oBAAhBC,iBACCA,aAAcC,OAAOF,GAE1BG,OAAOC,KAAKJ,YAIJK,EAAWtB,GAC1B,MAA2B,oBAAhBuB,iBACCA,aAAcC,OAAOxB,GAE1BoB,OAAOC,KAAKrB,GAAQyB,SAAS,iBAIrBC,EAAQC,GACvB,IAAIC,EAAkB,EACtB,IAAK,MAAM5B,KAAU2B,EACpBC,GAAmB5B,EAAOJ,WAG3B,MAAMiC,EAAS,IAAIrE,WAAWoE,GAC9B,IAAIjC,EAAa,EAEjB,IAAK,MAAMK,KAAU2B,EACpBE,EAAOC,IAAI,IAAItE,WAAWwC,GAASL,GACnCA,GAAcK,EAAOJ,WAGtB,OAAOiC,WCpBQE,EAAKrC,GAMpB,MAAMsC,EAAK,IAAIxE,WAAWkC,EAAKM,OAAQN,EAAKC,WAAYlC,EAAQwE,QAChE,GAAID,EAAG,KAAOvE,EAAQ,IACrBuE,EAAG,KAAOvE,EAAQ,IAClBuE,EAAG,KAAOvE,EAAQ,IAClBuE,EAAG,KAAOvE,EAAQ,IAClBuE,EAAG,KAAOvE,EAAQ,IAClBuE,EAAG,KAAOvE,EAAQ,IAClBuE,EAAG,KAAOvE,EAAQ,IAClBuE,EAAG,KAAOvE,EAAQ,IAClBuE,EAAG,KAAOvE,EAAQ,IAClBuE,EAAG,KAAOvE,EAAQ,IAClBuE,EAAG,MAAQvE,EAAQ,KACnBuE,EAAG,MAAQvE,EAAQ,IAEnB,UAAUyE,MAAM,+BAGjB,MAAMC,EAAY,IAAIjE,EAMhBkE,EAAmB,GAAKC,YAAYC,kBACpCC,EAAe,IAAI9C,EAAaC,EAAMjC,EAAQwE,OAAQG,GAAkB,GAE9ED,EAAUK,SAAWD,EAAa/B,cAClC2B,EAAUM,SAAWF,EAAa/B,cAClC2B,EAAUO,WAAaH,EAAa/B,cACpC2B,EAAUQ,YAAcJ,EAAa/B,cACrC2B,EAAUS,WAAaL,EAAa/B,cACpC2B,EAAUU,WAAaN,EAAa/B,cACpC2B,EAAUW,UAAYP,EAAa/B,cAEnC,MAAMuC,EAAaR,EAAa/B,cAEhC2B,EAAUa,uBAAyBT,EAAa/B,cAEhD,MAAMyC,EAAgBV,EAAa/B,cAC7B0C,EAAgBX,EAAa/B,cAC7B2C,EAAgBZ,EAAa/B,cAC7B4C,EAAgBb,EAAa/B,cAC7B6C,EAAgBd,EAAa7B,cAC7B4C,EAAgBf,EAAa7B,cAO7B6C,EAAc,IAAI9D,EAAaC,EAAMjC,EAAQwE,OAASG,EADvB,EAAbW,EAAiB,GACsD,GAE/F,IAAK,IAAIS,EAAI,EAAGA,EAAIT,EAAYS,IAC/BrB,EAAUsB,OAAOC,KAAK,CACrBC,UAAW,IAAInG,WAAWkC,EAAKM,OAAQN,EAAKC,WAAa4D,EAAY7C,cAAe6C,EAAY7C,eAChGkD,uBAAwBL,EAAY7C,gBAStC,MAAMmD,EAAY,IAAIpE,EAAaC,EAAMuD,EAAeC,GAAe,GAEjEY,EAA2C,CAChDxF,SAAUuF,EAAUlD,MAAM,GAAmBL,cAC7C/B,eAAgBsF,EAAUvD,cAC1B7B,cAAeoF,EAAUvD,cACzB5B,oBAAqBmF,EAAUvD,cAC/B3B,WAAYkF,EAAU1D,aACtBtB,eAAgBgF,EAAU1D,aAC1BpB,iBAAkB8E,EAAU1D,aAC5BnB,MAAO6E,EAAU1D,aACjBjB,oBAAqB,CACpBC,EAAG0E,EAAU1D,aAAe,EAC5Bf,EAAGyE,EAAU1D,aAAe,EAC5Bd,EAAGwE,EAAU1D,aAAe,EAC5Bb,EAAGuE,EAAU1D,aAAe,GAE7BZ,WAAY,CACXsE,EAAU1D,aACV0D,EAAU1D,aACV0D,EAAU1D,aACV0D,EAAU1D,aACV0D,EAAU1D,aACV0D,EAAU1D,aACV0D,EAAU1D,aACV0D,EAAU1D,cAEXX,QAAS,IAKJuE,GAAcD,EAAIpF,oBAAsB,EAF1B,GACA,EAGpB,IAAK,IAAI8E,EAAI,EAAGA,EAAIO,EAAYP,IAC/BM,EAAItE,QAASgE,GAAM,CAClBQ,UAAWH,EAAUvD,cACrB2D,UAAWJ,EAAU1D,aACrB+D,UAAWL,EAAU1D,aACrBgE,eAAgB,CACfN,EAAU1D,aACV0D,EAAU1D,aACV0D,EAAU1D,aACV0D,EAAU1D,cAEXiE,YAAaP,EAAUrD,cACvB6D,YAAaR,EAAUrD,eAIzB2B,EAAUmC,qBAAqBrC,OAAS,EACxCE,EAAUmC,qBAAqBZ,KAAKI,GAOpC,MAAMS,EAAY,IAAI9E,EAAaC,EAAMyD,EAAeC,GAAe,GAEvE,KAAOmB,EAAUrE,QAAUkD,GAAe,CACzC,MAAMoB,EAAqBD,EAAU/D,cAC/BiE,EAAUF,EAAU1D,MAAM2D,GAC1BE,EAAMpD,EAAWmD,GACjBE,EAAYJ,EAAU1D,MAAM2D,EAAqBC,EAAQ7E,YAC/DuC,EAAUyC,SAASF,GAAOA,EAAIG,MAAM,SAAWvD,EAAWqD,GAAaA,EAGnEH,EAAqB,GAAGD,EAAU5D,MAAM,EAAK6D,EAAqB,GAQvE,GAAIlB,GAAiB,EAAG,OAAOnB,EAE/B,MAAM2C,EAAY,IAAIrF,EAAaC,EAAM2D,EAAeC,GAAe,GAEjEyB,EAAgBD,EAAUxE,cAC1B0E,EAAgBF,EAAUxE,cAC1B2E,EAAsBH,EAAUtE,cAChC0E,EAAsBJ,EAAUtE,cAChC2E,EAAmBL,EAAUtE,cAC7B4E,EAAqBN,EAAUtE,cAE/B6E,EAAa,GACnB,IAAK,IAAI7B,EAAI,EAAGA,EAAIT,EAAYS,IAC/B6B,EAAW3B,KAAK,CACf4B,WAAYR,EAAUtE,cACtB+E,mBAAoBT,EAAUtE,cAC9BgF,mBAAoBV,EAAUtE,cAC9BiF,qBAAsBX,EAAUtE,cAChCkF,qBAAsBZ,EAAUtE,gBAIlC,MAAMmF,EAAsBtC,EAAgByB,EAAU5E,QAChD0F,EAAsBD,EAAsBV,EAC5CY,EAAmBD,EAAsBV,EACzCY,EAAqBD,EAAmBV,EAExCY,EAAgB,IAAIvI,WAAWkC,EAAKM,OAAQN,EAAKC,WAAagG,EAAqBV,GACnFe,EAAgB,IAAIxI,WAAWkC,EAAKM,OAAQN,EAAKC,WAAaiG,EAAqBV,GACnFe,EAAa,IAAIzI,WAAWkC,EAAKM,OAAQN,EAAKC,WAAakG,EAAkBV,GAC7Ee,EAAe,IAAI1I,WAAWkC,EAAKM,OAAQN,EAAKC,WAAamG,EAAoBV,GAYvF,OAVAjD,EAAUgE,WAAa,CACtBpB,cAAAA,EACAC,cAAAA,EACAK,WAAAA,EACAU,cAAAA,EACAC,cAAAA,EACAC,WAAAA,EACAC,aAAAA,GAGM/D,iNCnMR,MAAMiE,EAAgC,CAACC,YAAY,YAcnCC,EAAMnE,EAA0BoE,EAAwB,IACvEA,OAAcH,EAAoBG,GAMlC,IAAIC,EAAY,IAAIC,YAAY,GAChC,GAAItE,EAAUgE,WAAY,CACzB,MAAMO,EAAkB,IAAID,YAAY,GAA8C,EAAzCtE,EAAUgE,WAAWd,WAAWpD,OAAa,GACpF0E,EAAgB,IAAI5G,SAAS2G,GACnCC,EAAcC,UAAU,EAAGzE,EAAUgE,WAAWpB,eAAe,GAC/D4B,EAAcC,UAAU,EAAGzE,EAAUgE,WAAWnB,eAAe,GAC/D2B,EAAcE,UAAU,EAAG1E,EAAUgE,WAAWJ,cAAcnG,YAAY,GAC1E+G,EAAcE,UAAU,EAAG1E,EAAUgE,WAAWH,cAAcpG,YAAY,GAC1E+G,EAAcE,UAAU,GAAI1E,EAAUgE,WAAWF,WAAWrG,YAAY,GACxE+G,EAAcE,UAAU,GAAI1E,EAAUgE,WAAWD,aAAatG,YAAY,GAE1E,IAAK,IAAI4D,EAAI,EAAGA,EAAIrB,EAAUgE,WAAWd,WAAWpD,OAAQuB,IAAK,CAChE,MAAMsD,EAAY3E,EAAUgE,WAAWd,WAAW7B,GAClDmD,EAAcE,UAAU,GAAS,EAAJrD,EAAQ,EAAI,EAAGsD,EAAUxB,YAAY,GAClEqB,EAAcE,UAAU,GAAS,EAAJrD,EAAQ,EAAI,EAAGsD,EAAUvB,oBAAoB,GAC1EoB,EAAcE,UAAU,GAAS,EAAJrD,EAAQ,EAAI,EAAGsD,EAAUtB,oBAAoB,GAC1EmB,EAAcE,UAAU,GAAS,EAAJrD,EAAQ,EAAI,GAAIsD,EAAUrB,sBAAsB,GAC7EkB,EAAcE,UAAU,GAAS,EAAJrD,EAAQ,EAAI,GAAIsD,EAAUpB,sBAAsB,GAG9Ec,EAAY9E,EAAO,CAClBgF,EACAvE,EAAUgE,WAAWJ,cACrB5D,EAAUgE,WAAWH,cACrB7D,EAAUgE,WAAWF,WACrB9D,EAAUgE,WAAWD,eASvB,MAAMa,EAA6B,GACnC,IAAInC,EAAWzC,EAAUyC,SAEpB2B,EAAQF,aACZzB,OAAezC,EAAUyC,UAAUoC,gCAGpC,IAAK,MAAMtC,KAAOE,EAAU,CAC3B,MAAMxE,EAAQwE,EAASF,GACjBD,EAAUzD,EAAW0D,GACrBC,EAA6B,iBAAVvE,EAAqBY,EAAWZ,GAASA,EAC5D6G,EAAexC,EAAQ7E,WAAa,EAAI+E,EAAU/E,WAAa,EAC/DsH,EAAYD,EAAe,EAAK,EAAKA,EAAe,EAAM,EAChEF,EAAarD,KAAKhC,EAAO,CACxB,IAAIW,YAAY,CAAC4E,IACjBxC,EACAlH,EACAoH,EACApH,EACA,IAAIC,WAAW0J,GAAWC,KAAK,MAIjC,MAAMC,EAAY1F,EAAOqF,GAOnBM,EAAY,IAAIZ,YAAY,IAC5Ba,EAAU,IAAIvH,SAASsH,GAE7B,GAA8C,IAA1ClF,EAAUmC,qBAAqBrC,QAC9BE,EAAUmC,qBAAqB,GAAG/F,iBAAmBZ,EAAmBa,YAC5E,UAAU0D,MAAM,6DAGjB,MAAM4B,EAAM3B,EAAUmC,qBAAqB,GAE3CgD,EAAQT,UAAU,EAAG,IAAI,GACzBS,EAAQV,UAAU,EAAG9C,EAAIxF,UAAU,GACnCgJ,EAAQV,UAAU,EAAG9C,EAAIvF,gBAAgB,GACzC+I,EAAQV,UAAU,EAAG9C,EAAIrF,eAAe,GACxC6I,EAAQV,UAAU,GAAI9C,EAAIpF,qBAAqB,GAE/C4I,EAAQC,SAAS,GAAIzD,EAAInF,YACzB2I,EAAQC,SAAS,GAAIzD,EAAIjF,gBACzByI,EAAQC,SAAS,GAAIzD,EAAI/E,kBACzBuI,EAAQC,SAAS,GAAIzD,EAAI9E,OAEzBsI,EAAQC,SAAS,GAAIzD,EAAI5E,oBAAoBC,EAAI,GACjDmI,EAAQC,SAAS,GAAIzD,EAAI5E,oBAAoBE,EAAI,GACjDkI,EAAQC,SAAS,GAAIzD,EAAI5E,oBAAoBG,EAAI,GACjDiI,EAAQC,SAAS,GAAIzD,EAAI5E,oBAAoBI,EAAI,GAEjD,IAAK,IAAIkE,EAAI,EAAGA,EAAI,EAAGA,IAAK8D,EAAQC,SAAS,GAAK/D,EAAGM,EAAIvE,WAAWiE,IAEpE,IAAK,IAAIA,EAAI,EAAGA,EAAIM,EAAItE,QAAQyC,OAAQuB,IAAK,CAC5C,MAAMgE,EAAS1D,EAAItE,QAAQgE,GACrBiE,EAAmB,GAAS,GAAJjE,EAE9B8D,EAAQV,UAAUa,EAAmB,EAAGD,EAAOxD,WAAW,GAC1DsD,EAAQC,SAASE,EAAmB,EAAGD,EAAOvD,WAC9CqD,EAAQC,SAASE,EAAmB,EAAGD,EAAOtD,WAE9CoD,EAAQC,SAASE,EAAmB,EAAGD,EAAOrD,eAAe,IAC7DmD,EAAQC,SAASE,EAAmB,EAAGD,EAAOrD,eAAe,IAC7DmD,EAAQC,SAASE,EAAmB,EAAGD,EAAOrD,eAAe,IAC7DmD,EAAQC,SAASE,EAAmB,EAAGD,EAAOrD,eAAe,IAE7DmD,EAAQT,UAAUY,EAAmB,EAAGD,EAAOpD,aAAa,GAC5DkD,EAAQT,UAAUY,EAAmB,GAAID,EAAOnD,aAAa,GAQ9D,MAAMpB,EAAgBxF,EAAQwE,OLvHG,GKuHqD,EAA1BE,EAAUsB,OAAOxB,OAAa,EACpFkB,EAAgBF,EAAgBoE,EAAUzH,WAChD,IAAIyD,EAAgBF,EAAgBiE,EAAUxH,WAC1CyD,EAAgB,IAAGA,GAAiB,EAAKA,EAAgB,GAO7D,MAAMM,EAA0B,GAC1B+D,EAAa,IAAI3H,SAAS,IAAI0G,YAAsC,EAA1BtE,EAAUsB,OAAOxB,OAAa,IAE9E,IAAI0F,EAAsBtE,EAAgBmD,EAAU5G,WACpD,IAAK,IAAI4D,EAAI,EAAGA,EAAIrB,EAAUsB,OAAOxB,OAAQuB,IAAK,CACjD,MAAMoE,EAAQzF,EAAUsB,OAAOD,GAC/BG,EAAUD,KAAKkE,EAAMjE,WACrB+D,EAAWG,aAAiB,GAAJrE,EAAS,EAAGsE,OAAOH,IAAsB,GACjED,EAAWG,aAAiB,GAAJrE,EAAS,EAAGsE,OAAOF,EAAMjE,UAAU/D,aAAa,GACxE8H,EAAWG,aAAiB,GAAJrE,EAAS,GAAIsE,OAAOF,EAAMhE,yBAAyB,GAC3E+D,GAAuBC,EAAMjE,UAAU/D,WAQxC,MAAMmI,EAAe,IAAItB,YLnJQ,IKoJ3BuB,EAAa,IAAIjI,SAASgI,GAuBhC,OAtBAC,EAAWnB,UAAU,EAAG1E,EAAUK,UAAU,GAC5CwF,EAAWnB,UAAU,EAAG1E,EAAUM,UAAU,GAC5CuF,EAAWnB,UAAU,EAAG1E,EAAUO,YAAY,GAC9CsF,EAAWnB,UAAU,GAAI1E,EAAUQ,aAAa,GAChDqF,EAAWnB,UAAU,GAAI1E,EAAUS,YAAY,GAC/CoF,EAAWnB,UAAU,GAAI1E,EAAUU,YAAY,GAC/CmF,EAAWnB,UAAU,GAAI1E,EAAUW,WAAW,GAC9CkF,EAAWnB,UAAU,GAAI1E,EAAUsB,OAAOxB,QAAQ,GAClD+F,EAAWnB,UAAU,GAAI1E,EAAUa,wBAAwB,GAE3DgF,EAAWnB,UAAU,GAAI5D,GAAe,GACxC+E,EAAWnB,UAAU,GAAIQ,EAAUzH,YAAY,GAC/CoI,EAAWnB,UAAU,GAAI1D,GAAe,GACxC6E,EAAWnB,UAAU,GAAIO,EAAUxH,YAAY,GAC/CoI,EAAWH,aAAa,GAAIC,OAAOzE,IAAgB,GACnD2E,EAAWH,aAAa,GAAIC,OAAOtB,EAAU5G,aAAa,OAO/CpC,WAAWkE,EAAO,CAC5B,IAAIlE,WAAWC,GAASuC,OACxB+H,EACAL,EAAW1H,OACXqH,EACAD,EACA,IAAIX,YAAYpD,GAAiBF,EAAgBiE,EAAUxH,aAC3D4G,KACG7C"}