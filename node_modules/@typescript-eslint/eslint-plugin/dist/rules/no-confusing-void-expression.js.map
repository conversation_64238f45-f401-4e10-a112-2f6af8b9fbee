{"version": 3, "file": "no-confusing-void-expression.js", "sourceRoot": "", "sources": ["../../src/rules/no-confusing-void-expression.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,sDAAwC;AACxC,+CAAiC;AAGjC,kCASiB;AAmBjB,kBAAe,IAAA,iBAAU,EAAqB;IAC5C,IAAI,EAAE,8BAA8B;IACpC,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,kEAAkE;YACpE,WAAW,EAAE,QAAQ;YACrB,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,eAAe,EACb,oEAAoE;gBACpE,uCAAuC;YACzC,uBAAuB,EACrB,kDAAkD;gBAClD,qCAAqC;gBACrC,gDAAgD;YAClD,oBAAoB,EAClB,6EAA6E;gBAC7E,0CAA0C;YAC5C,4BAA4B,EAC1B,6DAA6D;gBAC7D,qDAAqD;YACvD,qBAAqB,EACnB,4DAA4D;gBAC5D,+CAA+C;YACjD,yBAAyB,EACvB,4DAA4D;gBAC5D,uCAAuC;YACzC,6BAA6B,EAC3B,4CAA4C;gBAC5C,qDAAqD;YACvD,gBAAgB,EAAE,wCAAwC;SAC3D;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,oBAAoB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;oBACzC,kBAAkB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;iBACxC;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;QACD,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,MAAM;QACf,cAAc,EAAE,IAAI;KACrB;IACD,cAAc,EAAE,CAAC,EAAE,oBAAoB,EAAE,KAAK,EAAE,kBAAkB,EAAE,KAAK,EAAE,CAAC;IAE5E,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,OAAO;YACL,2DAA2D,CACzD,IAGqC;gBAErC,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;gBAC5C,MAAM,IAAI,GAAG,IAAA,mCAA4B,EAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBAC1D,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACxD,wBAAwB;oBACxB,OAAO;gBACT,CAAC;gBAED,MAAM,eAAe,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;gBAClD,IAAI,eAAe,IAAI,IAAI,EAAE,CAAC;oBAC5B,uCAAuC;oBACvC,OAAO;gBACT,CAAC;gBAED,MAAM,WAAW,GAAG,CAAC,KAAyB,EAAoB,EAAE;oBAClE,MAAM,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBAClD,MAAM,WAAW,GAAG,QAAQ,QAAQ,EAAE,CAAC;oBACvC,OAAO,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;gBAC9C,CAAC,CAAC;gBAEF,IAAI,eAAe,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB,EAAE,CAAC;oBACpE,kCAAkC;oBAElC,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;wBAC/B,8BAA8B;wBAC9B,OAAO,OAAO,CAAC,MAAM,CAAC;4BACpB,IAAI;4BACJ,SAAS,EAAE,8BAA8B;4BACzC,GAAG,EAAE,WAAW;yBACjB,CAAC,CAAC;oBACL,CAAC;oBAED,8BAA8B;oBAC9B,MAAM,aAAa,GAAG,eAAe,CAAC;oBACtC,OAAO,OAAO,CAAC,MAAM,CAAC;wBACpB,IAAI;wBACJ,SAAS,EAAE,sBAAsB;wBACjC,GAAG,CAAC,KAAK;4BACP,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC;gCAC3B,OAAO,IAAI,CAAC;4BACd,CAAC;4BACD,MAAM,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC;4BACrC,MAAM,aAAa,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;4BAC5D,MAAM,gBAAgB,GAAG,KAAK,aAAa,KAAK,CAAC;4BACjD,IAAI,IAAA,sBAAe,EAAC,SAAS,EAAE,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;gCACnD,MAAM,gBAAgB,GAAG,IAAA,iBAAU,EACjC,OAAO,CAAC,UAAU,CAAC,cAAc,CAC/B,SAAS,EACT,0BAAmB,CACpB,EACD,wBAAiB,CAAC,YAAY,CAC5B,qBAAqB,EACrB,YAAY,CACb,CACF,CAAC;gCACF,MAAM,gBAAgB,GAAG,IAAA,iBAAU,EACjC,OAAO,CAAC,UAAU,CAAC,aAAa,CAC9B,SAAS,EACT,0BAAmB,CACpB,EACD,wBAAiB,CAAC,YAAY,CAC5B,qBAAqB,EACrB,YAAY,CACb,CACF,CAAC;gCACF,OAAO,KAAK,CAAC,gBAAgB,CAC3B,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACtD,gBAAgB,CACjB,CAAC;4BACJ,CAAC;4BACD,OAAO,KAAK,CAAC,WAAW,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC;wBACxD,CAAC;qBACF,CAAC,CAAC;gBACL,CAAC;gBAED,IAAI,eAAe,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE,CAAC;oBAC5D,0BAA0B;oBAE1B,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;wBAC/B,8BAA8B;wBAC9B,OAAO,OAAO,CAAC,MAAM,CAAC;4BACpB,IAAI;4BACJ,SAAS,EAAE,+BAA+B;4BAC1C,GAAG,EAAE,WAAW;yBACjB,CAAC,CAAC;oBACL,CAAC;oBAED,IAAI,aAAa,CAAC,eAAe,CAAC,EAAE,CAAC;wBACnC,8BAA8B;wBAC9B,OAAO,OAAO,CAAC,MAAM,CAAC;4BACpB,IAAI;4BACJ,SAAS,EAAE,2BAA2B;4BACtC,GAAG,CAAC,KAAK;gCACP,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC;oCAC7B,OAAO,IAAI,CAAC;gCACd,CAAC;gCACD,MAAM,WAAW,GAAG,eAAe,CAAC,QAAQ,CAAC;gCAC7C,MAAM,eAAe,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;gCAChE,IAAI,iBAAiB,GAAG,GAAG,eAAe,GAAG,CAAC;gCAC9C,IAAI,eAAe,CAAC,WAAW,CAAC,EAAE,CAAC;oCACjC,+CAA+C;oCAC/C,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC;gCAC9C,CAAC;gCACD,OAAO,KAAK,CAAC,WAAW,CAAC,eAAe,EAAE,iBAAiB,CAAC,CAAC;4BAC/D,CAAC;yBACF,CAAC,CAAC;oBACL,CAAC;oBAED,mCAAmC;oBACnC,OAAO,OAAO,CAAC,MAAM,CAAC;wBACpB,IAAI;wBACJ,SAAS,EAAE,uBAAuB;wBAClC,GAAG,CAAC,KAAK;4BACP,MAAM,WAAW,GAAG,eAAe,CAAC,QAAQ,CAAC;4BAC7C,MAAM,eAAe,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;4BAChE,IAAI,iBAAiB,GAAG,GAAG,eAAe,WAAW,CAAC;4BACtD,IAAI,eAAe,CAAC,WAAW,CAAC,EAAE,CAAC;gCACjC,+CAA+C;gCAC/C,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC;4BAC9C,CAAC;4BACD,IACE,eAAe,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,EAC7D,CAAC;gCACD,2CAA2C;gCAC3C,mCAAmC;gCACnC,iBAAiB,GAAG,KAAK,iBAAiB,IAAI,CAAC;4BACjD,CAAC;4BACD,OAAO,KAAK,CAAC,WAAW,CAAC,eAAe,EAAE,iBAAiB,CAAC,CAAC;wBAC/D,CAAC;qBACF,CAAC,CAAC;gBACL,CAAC;gBAED,sBAAsB;gBACtB,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;oBAC/B,sDAAsD;oBACtD,OAAO,OAAO,CAAC,MAAM,CAAC;wBACpB,IAAI;wBACJ,SAAS,EAAE,yBAAyB;wBACpC,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,kBAAkB,EAAE,GAAG,EAAE,WAAW,EAAE,CAAC;qBAC/D,CAAC,CAAC;gBACL,CAAC;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,iBAAiB;iBAC7B,CAAC,CAAC;YACL,CAAC;SACF,CAAC;QAWF;;;;;;;WAOG;QACH,SAAS,mBAAmB,CAAC,IAAmB;YAC9C,MAAM,MAAM,GAAG,IAAA,iBAAU,EAAC,IAAI,CAAC,MAAM,EAAE,wBAAiB,CAAC,aAAa,CAAC,CAAC;YACxE,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAAE,CAAC;gBACtD,IAAI,IAAI,KAAK,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC;oBAC/D,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;YAED,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,EAAE,CAAC;gBACvD,iCAAiC;gBACjC,uBAAuB;gBACvB,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE,CAAC;gBACrD,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,EAAE,CAAC;oBAC1B,6BAA6B;oBAC7B,mDAAmD;oBACnD,OAAO,mBAAmB,CAAC,MAAM,CAAC,CAAC;gBACrC,CAAC;YACH,CAAC;YAED,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,qBAAqB,EAAE,CAAC;gBACzD,IAAI,MAAM,CAAC,UAAU,KAAK,IAAI,IAAI,MAAM,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;oBAC5D,uDAAuD;oBACvD,mDAAmD;oBACnD,OAAO,mBAAmB,CAAC,MAAM,CAAC,CAAC;gBACrC,CAAC;YACH,CAAC;YAED,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB,EAAE,CAAC;gBAC3D,kCAAkC;gBAClC,2CAA2C;gBAC3C,IAAI,OAAO,CAAC,oBAAoB,EAAE,CAAC;oBACjC,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;YAED,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE,CAAC;gBACnD,IAAI,MAAM,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;oBAC/B,iCAAiC;oBACjC,2CAA2C;oBAC3C,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;wBAC/B,OAAO,IAAI,CAAC;oBACd,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE,CAAC;gBACnD,6BAA6B;gBAC7B,OAAO,mBAAmB,CAAC,MAAM,CAAC,CAAC;YACrC,CAAC;YAED,+BAA+B;YAC/B,0DAA0D;YAC1D,OAAO,MAAyB,CAAC;QACnC,CAAC;QAED,oFAAoF;QACpF,SAAS,aAAa,CAAC,IAA8B;YACnD,6BAA6B;YAC7B,MAAM,KAAK,GAAG,IAAA,iBAAU,EAAC,IAAI,CAAC,MAAM,EAAE,wBAAiB,CAAC,aAAa,CAAC,CAAC;YACvE,IAAI,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,EAAE,CAAC;gBACjD,4CAA4C;gBAC5C,OAAO,KAAK,CAAC;YACf,CAAC;YAED,wCAAwC;YACxC,MAAM,WAAW,GAAG,IAAA,iBAAU,EAC5B,KAAK,CAAC,MAAM,EACZ,wBAAiB,CAAC,aAAa,CAChC,CAAC;YACF,IACE,CAAC;gBACC,sBAAc,CAAC,mBAAmB;gBAClC,sBAAc,CAAC,kBAAkB;gBACjC,sBAAc,CAAC,uBAAuB;aACvC,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,EAC5B,CAAC;gBACD,+BAA+B;gBAC/B,oCAAoC;gBACpC,OAAO,KAAK,CAAC;YACf,CAAC;YAED,sCAAsC;YACtC,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrD,sCAAsC;gBACtC,OAAO,KAAK,CAAC;YACf,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAED;;;;;WAKG;QACH,SAAS,eAAe,CAAC,IAAyB;YAChD,MAAM,UAAU,GAAG,IAAA,iBAAU,EAC3B,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,EACtC,wBAAiB,CAAC,YAAY,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,CACzD,CAAC;YAEF,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC;QAED,SAAS,MAAM,CACb,IAAoE;YAEpE,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;YAE5C,MAAM,UAAU,GACd,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;gBAC1C,CAAC,CAAC,IAAI,CAAC,QAAQ;gBACf,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;YAEhB,MAAM,IAAI,GAAG,IAAA,mCAA4B,EAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAChE,OAAO,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;CACF,CAAC,CAAC"}