{"version": 3, "file": "no-unnecessary-parameter-property-assignment.js", "sourceRoot": "", "sources": ["../../src/rules/no-unnecessary-parameter-property-assignment.ts"], "names": [], "mappings": ";;AAAA,oEAAkE;AAElE,oDAAoE;AAEpE,kCAAuE;AAEvE,MAAM,qBAAqB,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;AAElE,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,8CAA8C;IACpD,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,mEAAmE;SACtE;QACD,QAAQ,EAAE;YACR,iBAAiB,EACf,sFAAsF;SACzF;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,YAAY;KACnB;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,eAAe,GAOf,EAAE,CAAC;QAET,SAAS,sBAAsB,CAC7B,IAAmB;YAEnB,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBAC7C,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,CACnD,CAAC;QACJ,CAAC;QAED,SAAS,eAAe,CAAC,IAAmB;YAC1C,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE,CAAC;gBACrD,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YAC5B,CAAC;YACD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,OAAO,IAAA,2BAAoB,EAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC7C,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,SAAS,kBAAkB,CACzB,IAA+B;YAM/B,IACE,CAAC,IAAI;gBACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB;gBAChD,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;gBAC/C,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB,EACpD,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACzC,CAAC;QAED,SAAS,4BAA4B,CACnC,IAA+B;YAE/B,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAAE,CAAC;gBAC7D,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,4BAA4B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACnD,CAAC;QAED,SAAS,+BAA+B,CACtC,IAA+B;YAE/B,OAAO,CACL,IAAI,EAAE,IAAI,KAAK,sBAAc,CAAC,kBAAkB;gBAChD,gBAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CACpC,CAAC;QACJ,CAAC;QAED,SAAS,wBAAwB,CAAC,IAAyB;YACzD,MAAM,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEhD,MAAM,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CACpC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CACzC,CAAC;YACF,OAAO,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,8BAAc,CAAC,SAAS,CAAC;QAC3E,CAAC;QAED,SAAS,2BAA2B,CAClC,IAAwB,EACxB,IAAY;YAEZ,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB;gBAChD,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,IAAI,8BAA8B;oBACnF,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,IAAI,CAAC;oBAC7B,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,IAAI,kCAAkC;wBAC7F,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;wBACtD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CACxC,CAAC;QACJ,CAAC;QAED,SAAS,aAAa,CAAC,IAAmB;YACxC,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE,CAAC;gBAC5C,OAAO,IAAI,CAAC;YACd,CAAC;YACD,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;gBAC3C,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,EAChD,CAAC;gBACD,OAAO,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACxC,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,SAAS,WAAW,CAAC,IAAmB;YACtC,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB;gBACpD,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,CACnD,CAAC;QACJ,CAAC;QAED,OAAO;YACL,SAAS;gBACP,eAAe,CAAC,IAAI,CAAC;oBACnB,sBAAsB,EAAE,EAAE;oBAC1B,yBAAyB,EAAE,IAAI,GAAG,EAAE;oBACpC,yBAAyB,EAAE,IAAI,GAAG,EAAE;iBACrC,CAAC,CAAC;YACL,CAAC;YACD,gBAAgB;gBACd,MAAM,EAAE,sBAAsB,EAAE,yBAAyB,EAAE,GACzD,IAAA,iBAAU,EAAC,eAAe,CAAC,GAAG,EAAE,EAAE,4BAA4B,CAAC,CAAC;gBAClE,sBAAsB,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;oBAChD,IAAI,yBAAyB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;wBACxC,OAAO;oBACT,CAAC;oBACD,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,mBAAmB;qBAC/B,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC;YACD,yCAAyC,CACvC,IAAmC;gBAEnC,MAAM,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAExC,IAAI,CAAC,IAAI,EAAE,CAAC;oBACV,OAAO;gBACT,CAAC;gBAED,MAAM,YAAY,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBAC9C,IAAI,YAAY,EAAE,CAAC;oBACjB,IACE,CAAC,CACC,WAAW,CAAC,YAAY,CAAC;wBACzB,4BAA4B,CAAC,IAAI,CAAC,EAAE,KAAK,KAAK,YAAY,CAAC,MAAM,CAClE,EACD,CAAC;wBACD,OAAO;oBACT,CAAC;gBACH,CAAC;gBAED,MAAM,EAAE,yBAAyB,EAAE,GAAG,IAAA,iBAAU,EAC9C,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EACtB,4BAA4B,CAC7B,CAAC;gBACF,yBAAyB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACtC,CAAC;YACD,gFAAgF,CAC9E,IAAmC;gBAEnC,MAAM,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAE5C,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,OAAO;gBACT,CAAC;gBAED,IAAI,YAAY,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBAC5C,IAAI,YAAY,IAAI,WAAW,CAAC,YAAY,CAAC,EAAE,CAAC;oBAC9C,YAAY,GAAG,kBAAkB,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;gBACzD,CAAC;gBAED,IAAI,CAAC,+BAA+B,CAAC,YAAY,CAAC,EAAE,CAAC;oBACnD,OAAO;gBACT,CAAC;gBAED,MAAM,EAAE,yBAAyB,EAAE,sBAAsB,EAAE,GACzD,IAAA,iBAAU,EACR,eAAe,CAAC,EAAE,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,EAC9C,+BAA+B,CAChC,CAAC;gBAEJ,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC9C,yBAAyB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBACxC,OAAO;gBACT,CAAC;gBAED,MAAM,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAE1C,IAAI,QAAQ,KAAK,OAAO,EAAE,IAAI,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,EAAE,CAAC;oBACrE,OAAO;gBACT,CAAC;gBAED,MAAM,oBAAoB,GAAG,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAC5D,2BAA2B,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,CACjD,CAAC;gBAEF,IAAI,oBAAoB,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACrE,sBAAsB,CAAC,IAAI,CAAC;wBAC1B,IAAI,EAAE,QAAQ;wBACd,IAAI;qBACL,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}