import * as scopeManager from '@typescript-eslint/scope-manager';
declare namespace Scope {
    type ScopeManager = scopeManager.ScopeManager;
    type Reference = scopeManager.Reference;
    type Variable = scopeManager.ESLintScopeVariable | scopeManager.Variable;
    type Scope = scopeManager.Scope;
    const ScopeType: typeof scopeManager.ScopeType;
    type DefinitionType = scopeManager.Definition;
    type Definition = scopeManager.Definition;
    const DefinitionType: typeof scopeManager.DefinitionType;
    namespace Definitions {
        type CatchClauseDefinition = scopeManager.CatchClauseDefinition;
        type ClassNameDefinition = scopeManager.ClassNameDefinition;
        type FunctionNameDefinition = scopeManager.FunctionNameDefinition;
        type ImplicitGlobalVariableDefinition = scopeManager.ImplicitGlobalVariableDefinition;
        type ImportBindingDefinition = scopeManager.ImportBindingDefinition;
        type ParameterDefinition = scopeManager.ParameterDefinition;
        type TSEnumMemberDefinition = scopeManager.TSEnumMemberDefinition;
        type TSEnumNameDefinition = scopeManager.TSEnumNameDefinition;
        type TSModuleNameDefinition = scopeManager.TSModuleNameDefinition;
        type TypeDefinition = scopeManager.TypeDefinition;
        type VariableDefinition = scopeManager.VariableDefinition;
    }
    namespace Scopes {
        type BlockScope = scopeManager.BlockScope;
        type CatchScope = scopeManager.CatchScope;
        type ClassScope = scopeManager.ClassScope;
        type ConditionalTypeScope = scopeManager.ConditionalTypeScope;
        type ForScope = scopeManager.ForScope;
        type FunctionExpressionNameScope = scopeManager.FunctionExpressionNameScope;
        type FunctionScope = scopeManager.FunctionScope;
        type FunctionTypeScope = scopeManager.FunctionTypeScope;
        type GlobalScope = scopeManager.GlobalScope;
        type MappedTypeScope = scopeManager.MappedTypeScope;
        type ModuleScope = scopeManager.ModuleScope;
        type SwitchScope = scopeManager.SwitchScope;
        type TSEnumScope = scopeManager.TSEnumScope;
        type TSModuleScope = scopeManager.TSModuleScope;
        type TypeScope = scopeManager.TypeScope;
        type WithScope = scopeManager.WithScope;
    }
}
export { Scope };
//# sourceMappingURL=Scope.d.ts.map