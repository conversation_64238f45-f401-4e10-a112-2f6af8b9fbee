{"version": 3, "file": "applyDefault.js", "sourceRoot": "", "sources": ["../../src/eslint-utils/applyDefault.ts"], "names": [], "mappings": ";;AA2CS,oCAAY;AA3CrB,2CAA0D;AAE1D;;;;;;GAMG;AACH,SAAS,YAAY,CACnB,cAAiC,EACjC,WAAkC;IAElC,iBAAiB;IACjB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CACxB,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CACT,CAAC;IAExB,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;QACxB,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,8BAA8B;IAC9B,sFAAsF;IACrF,OAAqB,CAAC,OAAO,CAAC,CAAC,GAAY,EAAE,CAAS,EAAE,EAAE;QACzD,IAAI,WAAW,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC;YACjC,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YAE/B,IAAI,IAAA,4BAAgB,EAAC,OAAO,CAAC,IAAI,IAAA,4BAAgB,EAAC,GAAG,CAAC,EAAE,CAAC;gBACvD,OAAO,CAAC,CAAC,CAAC,GAAG,IAAA,qBAAS,EAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YACvC,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;YACvB,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,OAAO,CAAC;AACjB,CAAC"}