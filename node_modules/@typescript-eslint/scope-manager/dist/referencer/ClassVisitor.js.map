{"version": 3, "file": "ClassVisitor.js", "sourceRoot": "", "sources": ["../../src/referencer/ClassVisitor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,8CAAyE;AAEzE,+CAA4C;AAC5C,uCAAoC;AAEpC,MAAM,YAAa,SAAQ,iBAAO;IAIhC,YACE,UAAsB,EACtB,IAA0D;QAE1D,KAAK,CAAC,UAAU,CAAC,CAAC;QAPX,0CAAiE;QACjE,2CAAwB;QAO/B,uBAAA,IAAI,4BAAe,UAAU,MAAA,CAAC;QAC9B,uBAAA,IAAI,2BAAc,IAAI,MAAA,CAAC;IACzB,CAAC;IAED,MAAM,CAAC,KAAK,CACV,UAAsB,EACtB,IAA0D;QAE1D,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QACxD,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAED,KAAK,CAAC,IAAsC;QAC1C,+DAA+D;QAC/D,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;YAC9B,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACpB,CAAC;aAAM,CAAC;YACN,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/B,CAAC;IACH,CAAC;IAED,mBAAmB;IACnB,mBAAmB;IACnB,mBAAmB;IAET,UAAU,CAClB,IAA0D;QAE1D,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC;YAC7D,uBAAA,IAAI,gCAAY;iBACb,YAAY,EAAE;iBACd,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,gCAAmB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;QACvE,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAExD,uBAAA,IAAI,gCAAY,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAEnD,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC;YACZ,mDAAmD;YACnD,0EAA0E;YAC1E,uBAAA,IAAI,gCAAY;iBACb,YAAY,EAAE;iBACd,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,gCAAmB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;QACvE,CAAC;QAED,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAExC,oCAAoC;QACpC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACpC,kBAAkB;QAClB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACxC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;QAEpD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEtB,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAES,uBAAuB,CAC/B,IAIyC;QAEzC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC7B;;;;;WAKG;QACH,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IACtC,CAAC;IAES,oCAAoC,CAC5C,IAAwB;QAExB,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,sBAAc,CAAC,iBAAiB;gBACnC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBACzC,MAAM;YACR,KAAK,sBAAc,CAAC,mBAAmB;gBACrC,IAAI,CAAC,oCAAoC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC1D,MAAM;YACR;gBACE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAES,mBAAmB,CAC3B,IAAiC,EACjC,UAAqC;QAErC,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC;YACZ,0DAA0D;YAC1D,+BAA+B;YAC/B,uBAAA,IAAI,gCAAY,CAAC,YAAY,CAAC,+BAA+B,CAAC,IAAI,CAAC,CAAC;QACtE,CAAC;QAED,qDAAqD;QACrD,uBAAA,IAAI,gCAAY,CAAC,YAAY,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAE5D;;;;;;;;WAQG;QACH,IAAI,oBAAoB,GAAG,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC;QAC1D;;;;;;;;;;;;WAYG;QACH,oBAAoB;YAClB,UAAU,CAAC,IAAI,KAAK,KAAK;gBACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACrD,IAAI,CAAC,oBAAoB,IAAI,UAAU,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;YACvD,MAAM,OAAO,GAAG,uBAAuB,CAAC,UAAU,CAAC,CAAC;YAEpD;;;;;;eAMG;YACH,IACE,OAAO,IAAI,IAAI;gBACf,uBAAA,IAAI,+BAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAC5B,CAAC,IAAI,EAAqC,EAAE,CAC1C,IAAI,KAAK,UAAU;oBACnB,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;oBAC7C,kCAAkC;oBAClC,IAAI,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM;oBACjC,uBAAuB,CAAC,IAAI,CAAC,KAAK,OAAO,CAC5C,EAAE,UAAU,CAAC,MAAM,EACpB,CAAC;gBACD,oBAAoB,GAAG,IAAI,CAAC;YAC9B,CAAC;QACH,CAAC;QAED;;;;;WAKG;QACH,IACE,CAAC,oBAAoB;YACrB,UAAU,CAAC,IAAI,KAAK,aAAa;YACjC,uBAAA,IAAI,+BAAW,CAAC,UAAU,CAAC,MAAM,EACjC,CAAC;YACD,oBAAoB,GAAG,IAAI,CAAC;QAC9B,CAAC;QAED,kCAAkC;QAClC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChC,IAAI,CAAC,YAAY,CACf,KAAK,EACL,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE;gBAChB,uBAAA,IAAI,gCAAY;qBACb,YAAY,EAAE;qBACd,gBAAgB,CACf,OAAO,EACP,IAAI,gCAAmB,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAClD,CAAC;gBAEJ,uBAAA,IAAI,gCAAY,CAAC,uBAAuB,CACtC,OAAO,EACP,IAAI,CAAC,WAAW,EAChB,IAAI,EACJ,IAAI,CACL,CAAC;YACJ,CAAC,EACD,EAAE,qBAAqB,EAAE,IAAI,EAAE,CAChC,CAAC;YACF,IAAI,CAAC,oCAAoC,CAAC,KAAK,CAAC,CAAC;YACjD,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAChC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEpC,uBAAA,IAAI,gCAAY,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAES,iBAAiB,CACzB,IAKyC;QAEzC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACnC,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;gBAC/C,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAC7C,CAAC;gBACD,uBAAA,IAAI,gCAAY,CAAC,YAAY,CAAC,8BAA8B,CAC1D,IAAI,CAAC,KAAK,CACX,CAAC;YACJ,CAAC;YAED,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEnC,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;gBAC/C,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAC7C,CAAC;gBACD,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1D,CAAC;IAES,WAAW,CAAC,IAA+B;QACnD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACnC,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAAE,CAAC;YAC1D,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC7C,CAAC;aAAM,CAAC;YACN,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrC,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1D,CAAC;IAES,SAAS,CAAC,IAAsC;QACxD,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO;QACT,CAAC;QACD,yBAAW,CAAC,KAAK,CAAC,uBAAA,IAAI,gCAAY,EAAE,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED,qBAAqB;IACrB,qBAAqB;IACrB,qBAAqB;IAEX,gBAAgB,CAAC,IAA+B;QACxD,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC;IAES,SAAS,CAAC,IAAwB;QAC1C,4EAA4E;QAC5E,qEAAqE;QACrE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IAES,kBAAkB,CAAC,IAAiC;QAC5D,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC;IAES,gBAAgB,CAAC,IAA+B;QACxD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAES,0BAA0B,CAClC,IAAyC;QAEzC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC;IAES,4BAA4B,CACpC,IAA2C;QAE3C,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC;IAES,0BAA0B,CAClC,IAAyC;QAEzC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAES,UAAU,CAAC,IAAyB;QAC5C,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAES,iBAAiB;QACzB,qBAAqB;IACvB,CAAC;IAES,gBAAgB,CAAC,IAA+B;QACxD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IAES,WAAW,CAAC,IAA0B;QAC9C,uBAAA,IAAI,gCAAY,CAAC,YAAY,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;QAE9D,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAEtC,uBAAA,IAAI,gCAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;CACF;AAsCQ,oCAAY;;AApCrB;;;;;;;;;;;;;;;;;;;GAmBG;AACH,SAAS,uBAAuB,CAC9B,IAA+B;IAE/B,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,EAAE,CAAC;QAC9D,IACE,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,KAAK,QAAQ;YAClC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,KAAK,QAAQ,EAClC,CAAC;YACD,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;QACxB,CAAC;IACH,CAAC;SAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE,CAAC;QACzE,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;IACvB,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC"}