export { parse, parseForESLint, ParserOptions } from './parser';
export { ParserServices, ParserServicesWithTypeInformation, ParserServicesWithoutTypeInformation, clearCaches, createProgram, withoutProjectParserOptions, } from '@typescript-eslint/typescript-estree';
export declare const version: string;
export declare const meta: {
    name: string;
    version: string;
};
//# sourceMappingURL=index.d.ts.map