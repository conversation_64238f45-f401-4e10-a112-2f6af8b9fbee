{"name": "three-globe", "version": "2.41.6", "description": "Globe data visualization as a ThreeJS reusable 3D object", "type": "module", "unpkg": "dist/three-globe.min.js", "main": "dist/three-globe.mjs", "module": "dist/three-globe.mjs", "types": "dist/three-globe.d.ts", "exports": {"types": "./dist/three-globe.d.ts", "umd": "./dist/three-globe.min.js", "default": "./dist/three-globe.mjs"}, "sideEffects": false, "repository": {"type": "git", "url": "git+https://github.com/vasturiano/three-globe.git"}, "homepage": "https://github.com/vasturiano/three-globe", "keywords": ["3d", "globe", "webgl", "visualization", "three"], "author": {"name": "Vasco <PERSON>turiano", "url": "https://github.com/vasturiano"}, "license": "MIT", "bugs": {"url": "https://github.com/vasturiano/three-globe/issues"}, "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -w -c rollup.config.dev.js", "prepare": "npm run build"}, "files": ["dist/**/*", "example/**/*"], "dependencies": {"@tweenjs/tween.js": "18 - 25", "accessor-fn": "1", "d3-array": "3", "d3-color": "3", "d3-geo": "3", "d3-interpolate": "3", "d3-scale": "4", "d3-scale-chromatic": "3", "data-bind-mapper": "1", "frame-ticker": "1", "h3-js": "4", "index-array-by": "1", "kapsule": "^1.16", "taichi.js": "^0.0.36", "three-conic-polygon-geometry": "2", "three-geojson-geometry": "2", "three-slippy-map-globe": "1", "tinycolor2": "1"}, "peerDependencies": {"three": ">=0.154"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.0", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.2", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-terser": "^0.4.4", "@types/three": ">=0.154.0", "rimraf": "^6.0.1", "rollup": "^4.30.1", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-taichi": "^0.0.3", "three": "^0.172.0", "typescript": "^5.7.3"}, "engines": {"node": ">=12"}}