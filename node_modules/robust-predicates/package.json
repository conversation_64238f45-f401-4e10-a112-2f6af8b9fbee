{"name": "robust-predicates", "version": "3.0.2", "description": "Fast robust predicates for computational geometry", "keywords": ["computational geometry", "robust arithmetic"], "author": "<PERSON>", "license": "Unlicense", "type": "module", "main": "index.js", "unpkg": "umd/predicates.min.js", "module": "index.js", "exports": "./index.js", "types": "index.d.ts", "scripts": {"build": "mkdirp esm && node compile.js", "lint": "eslint *.js src test/test.js", "test": "npm run lint && npm run build && node test/test.js", "cov": "c8 node test/test.js", "bench": "node bench.js", "prepublishOnly": "npm run test && rollup -c"}, "devDependencies": {"@rollup/plugin-terser": "^0.4.3", "c8": "^7.13.0", "eslint": "^8.41.0", "eslint-config-mourner": "^3.0.0", "mkdirp": "^3.0.1", "nextafter": "^1.0.0", "robust-in-sphere": "^1.2.1", "robust-orientation": "^1.2.1", "rollup": "^3.23.0"}, "files": ["index.js", "index.d.ts", "esm", "umd"], "repository": {"type": "git", "url": "https://github.com/mourner/robust-predicates.git"}, "eslintConfig": {"extends": "mourner", "parserOptions": {"ecmaVersion": 2020}, "rules": {"camelcase": 0, "new-cap": 0, "no-unused-vars": [2, {"varsIgnorePattern": "splitter|bvirt|c|[ab]hi|[ab]lo|_[ijk0]|u3|[st][01]"}], "no-lonely-if": 0}, "globals": {"$Fast_Two_Sum": false, "$Two_Sum": false, "$Two_Diff_Tail": false, "$Split": false, "$Two_Product": false, "$Two_Product_Presplit": false, "$Two_One_Product": false, "$Cross_Product": false, "$Square_Sum": false, "$Two_Product_Sum": false}}}