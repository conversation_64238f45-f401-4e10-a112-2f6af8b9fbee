// Version 1.0.3 data-bind-mapper - https://github.com/vasturiano/data-bind-mapper
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).DataBindMapper=e()}(this,(function(){"use strict";function t(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function e(t,e,n){if("function"==typeof t?t===e:t.has(e))return arguments.length<3?e:n;throw new TypeError("Private element is not present on this object")}function n(t,n){return t.get(e(t,n))}function r(t,e,n){(function(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")})(t,e),e.set(t,n)}function i(t,n,r){return t.set(e(t,n),r),r}function o(t,e,n){return e&&function(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,f(r.key),r)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function a(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o,a,u=[],f=!0,l=!1;try{if(o=(n=n.call(t)).next,0===e);else for(;!(f=(r=o.call(n)).done)&&(u.push(r.value),u.length!==e);f=!0);}catch(t){l=!0,i=t}finally{try{if(!f&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}(t,e)||l(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(e){return function(e){if(Array.isArray(e))return t(e)}(e)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(e)||l(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e);if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==typeof e?e:e+""}function l(e,n){if(e){if("string"==typeof e)return t(e,n);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?t(e,n):void 0}}var c=new WeakMap,s=new WeakMap,y=new WeakMap,h=new WeakMap,p=new WeakMap,v=new WeakMap;return function(){return o((function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),r(this,c,new Map),r(this,s,new Map),r(this,y,(function(t){return t})),r(this,h,(function(){return{}})),r(this,p,(function(){})),r(this,v,(function(){}))}),[{key:"getObj",value:function(t){return n(c,this).get(n(y,this).call(this,t))}},{key:"getData",value:function(t){return n(s,this).get(t)}},{key:"entries",value:function(){return u(n(s,this).entries()).map((function(t){var e=a(t,2),n=e[0];return[e[1],n]}))}},{key:"id",value:function(t){return i(y,this,function(t){return"function"==typeof t?t:"string"==typeof t?function(e){return e[t]}:function(e){return t}}(t)),this}},{key:"onCreateObj",value:function(t){return i(h,this,t),this}},{key:"onUpdateObj",value:function(t){return i(p,this,t),this}},{key:"onRemoveObj",value:function(t){return i(v,this,t),this}},{key:"digest",value:function(t){var e=this;t.filter((function(t){return!n(c,e).has(n(y,e).call(e,t))})).forEach((function(t){var r=n(h,e).call(e,t);n(c,e).set(n(y,e).call(e,t),r),n(s,e).set(r,t)}));var r=new Map(t.map((function(t){return[n(y,e).call(e,t),t]})));return n(c,this).forEach((function(t,i){r.has(i)?n(p,e).call(e,t,r.get(i)):(n(v,e).call(e,t,i),n(c,e).delete(i),n(s,e).delete(t))})),this}},{key:"clear",value:function(){return this.digest([]),this}}])}()}));
