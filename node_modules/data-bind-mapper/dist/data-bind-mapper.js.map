{"version": 3, "file": "data-bind-mapper.js", "sources": ["../node_modules/accessor-fn/dist/accessor-fn.mjs", "../src/index.js"], "sourcesContent": ["var index = (function (p) {\n  return typeof p === 'function' ? p // fn\n  : typeof p === 'string' ? function (obj) {\n    return obj[p];\n  } // property name\n  : function (obj) {\n    return p;\n  };\n}); // constant\n\nexport { index as default };\n", "import accessorFn from 'accessor-fn';\n\nexport default class DataBindMapper {\n  constructor() {}\n\n  getObj(d) { return this.#dataMap.get(this.#id(d)); }\n  getData(o) { return this.#objMap.get(o); }\n  entries() { return [...this.#objMap.entries()].map(([o, d]) => [d, o]); }\n\n  id(p) {\n    this.#id = accessorFn(p);\n    return this;\n  }\n  onCreateObj(fn) {\n    this.#createObj = fn;\n    return this;\n  }\n  onUpdateObj(fn) {\n    this.#updateObj = fn;\n    return this;\n  }\n  onRemoveObj(fn) {\n    this.#removeObj = fn;\n    return this;\n  }\n\n  digest(data) {\n    data.filter(d => !this.#dataMap.has(this.#id(d))).forEach(d => {\n      const obj = this.#createObj(d);\n      this.#dataMap.set(this.#id(d), obj);\n      this.#objMap.set(obj, d);\n    });\n\n    const dataIdsMap = new Map(data.map(d => [this.#id(d), d]));\n    this.#dataMap.forEach((o, dId) => {\n      if (!dataIdsMap.has(dId)) {\n        this.#removeObj(o, dId);\n        this.#dataMap.delete(dId);\n        this.#objMap.delete(o);\n      } else {\n        this.#updateObj(o, dataIdsMap.get(dId));\n      }\n    });\n\n    return this;\n  }\n\n  clear() {\n    this.digest([]);\n    return this;\n  }\n\n  #dataMap = new Map();\n  #objMap = new Map();\n  #id = d => d;\n  #createObj = () => ({});\n  #updateObj = () => {};\n  #removeObj = () => {};\n}\n"], "names": ["_dataMap", "WeakMap", "_objMap", "_id", "_createObj", "_updateObj", "_removeObj", "DataBindMapper", "_classCallCheck", "_classPrivateFieldInitSpec", "Map", "d", "_createClass", "key", "value", "get<PERSON><PERSON>j", "_classPrivateFieldGet", "get", "call", "getData", "o", "entries", "_toConsumableArray", "map", "_ref", "_ref2", "_slicedToArray", "id", "p", "_classPrivateFieldSet", "accessorFn", "onCreateObj", "fn", "onUpdateObj", "onRemoveObj", "digest", "data", "_this", "filter", "has", "for<PERSON>ach", "obj", "set", "dataIdsMap", "dId", "clear"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA,IAAI,KAAK,IAAI,UAAU,CAAC,EAAE;EAC1B,EAAE,OAAO,OAAO,CAAC,KAAK,UAAU,GAAG,CAAC;EACpC,IAAI,OAAO,CAAC,KAAK,QAAQ,GAAG,UAAU,GAAG,EAAE;EAC3C,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC;EACjB,GAAG;EACH,IAAI,UAAU,GAAG,EAAE;EACnB,IAAI,OAAO,CAAC;EACZ,GAAG;EACH,CAAC,CAAC,CAAC;;ECRkC,IAAAA,QAAA,oBAAAC,OAAA,EAAA;EAAA,IAAAC,OAAA,oBAAAD,OAAA,EAAA;EAAA,IAAAE,GAAA,oBAAAF,OAAA,EAAA;EAAA,IAAAG,UAAA,oBAAAH,OAAA,EAAA;EAAA,IAAAI,UAAA,oBAAAJ,OAAA,EAAA;EAAA,IAAAK,UAAA,oBAAAL,OAAA,EAAA;AAAA,MAEhBM,cAAc,gBAAA,YAAA;EACjC,EAAA,SAAAA,iBAAc;EAAAC,IAAAA,eAAA,OAAAD,cAAA,CAAA;EAiDdE,IAAAA,0BAAA,OAAAT,QAAQ,EAAG,IAAIU,GAAG,EAAE,CAAA;EACpBD,IAAAA,0BAAA,OAAAP,OAAO,EAAG,IAAIQ,GAAG,EAAE,CAAA;EACnBD,IAAAA,0BAAA,CAAAN,IAAAA,EAAAA,GAAG,EAAG,UAAAQ,CAAC,EAAA;EAAA,MAAA,OAAIA,CAAC;EAAA,KAAA,CAAA;MACZF,0BAAA,CAAA,IAAA,EAAAL,UAAU,EAAG,YAAA;EAAA,MAAA,OAAO,EAAE;OAAC,CAAA;EACvBK,IAAAA,0BAAA,CAAAJ,IAAAA,EAAAA,UAAU,EAAG,YAAM,EAAE,CAAA;EACrBI,IAAAA,0BAAA,CAAAH,IAAAA,EAAAA,UAAU,EAAG,YAAM,EAAE,CAAA;EAtDN;IAAC,OAAAM,YAAA,CAAAL,cAAA,EAAA,CAAA;MAAAM,GAAA,EAAA,QAAA;EAAAC,IAAAA,KAAA,EAEhB,SAAAC,MAAMA,CAACJ,CAAC,EAAE;QAAE,OAAOK,sBAAA,CAAKhB,QAAQ,EAAb,IAAY,CAAC,CAACiB,GAAG,CAACD,sBAAA,CAAKb,GAAG,EAAR,IAAO,CAAC,CAAAe,IAAA,CAAR,IAAI,EAAKP,CAAC,CAAC,CAAC;EAAE;EAAC,GAAA,EAAA;MAAAE,GAAA,EAAA,SAAA;EAAAC,IAAAA,KAAA,EACpD,SAAAK,OAAOA,CAACC,CAAC,EAAE;QAAE,OAAOJ,sBAAA,CAAKd,OAAO,EAAZ,IAAW,CAAC,CAACe,GAAG,CAACG,CAAC,CAAC;EAAE;EAAC,GAAA,EAAA;MAAAP,GAAA,EAAA,SAAA;EAAAC,IAAAA,KAAA,EAC1C,SAAAO,OAAOA,GAAG;EAAE,MAAA,OAAOC,kBAAA,CAAIN,sBAAA,CAAKd,OAAO,EAAZ,IAAW,CAAC,CAACmB,OAAO,EAAE,CAAA,CAAEE,GAAG,CAAC,UAAAC,IAAA,EAAA;EAAA,QAAA,IAAAC,KAAA,GAAAC,cAAA,CAAAF,IAAA,EAAA,CAAA,CAAA;EAAEJ,UAAAA,CAAC,GAAAK,KAAA,CAAA,CAAA,CAAA;EAAEd,UAAAA,CAAC,GAAAc,KAAA,CAAA,CAAA,CAAA;EAAA,QAAA,OAAM,CAACd,CAAC,EAAES,CAAC,CAAC;SAAC,CAAA;EAAE;EAAC,GAAA,EAAA;MAAAP,GAAA,EAAA,IAAA;EAAAC,IAAAA,KAAA,EAEzE,SAAAa,EAAEA,CAACC,CAAC,EAAE;QACJC,sBAAA,CAAK1B,GAAG,EAAR,IAAI,EAAO2B,KAAU,CAACF,CAAC,CAAhB,CAAC;EACR,MAAA,OAAO,IAAI;EACb;EAAC,GAAA,EAAA;MAAAf,GAAA,EAAA,aAAA;EAAAC,IAAAA,KAAA,EACD,SAAAiB,WAAWA,CAACC,EAAE,EAAE;EACdH,MAAAA,sBAAA,CAAKzB,UAAU,EAAf,IAAI,EAAc4B,EAAJ,CAAC;EACf,MAAA,OAAO,IAAI;EACb;EAAC,GAAA,EAAA;MAAAnB,GAAA,EAAA,aAAA;EAAAC,IAAAA,KAAA,EACD,SAAAmB,WAAWA,CAACD,EAAE,EAAE;EACdH,MAAAA,sBAAA,CAAKxB,UAAU,EAAf,IAAI,EAAc2B,EAAJ,CAAC;EACf,MAAA,OAAO,IAAI;EACb;EAAC,GAAA,EAAA;MAAAnB,GAAA,EAAA,aAAA;EAAAC,IAAAA,KAAA,EACD,SAAAoB,WAAWA,CAACF,EAAE,EAAE;EACdH,MAAAA,sBAAA,CAAKvB,UAAU,EAAf,IAAI,EAAc0B,EAAJ,CAAC;EACf,MAAA,OAAO,IAAI;EACb;EAAC,GAAA,EAAA;MAAAnB,GAAA,EAAA,QAAA;EAAAC,IAAAA,KAAA,EAED,SAAAqB,MAAMA,CAACC,IAAI,EAAE;EAAA,MAAA,IAAAC,KAAA,GAAA,IAAA;EACXD,MAAAA,IAAI,CAACE,MAAM,CAAC,UAAA3B,CAAC,EAAA;UAAA,OAAI,CAACK,sBAAA,CAAKhB,QAAQ,EAAbqC,KAAY,CAAC,CAACE,GAAG,CAACvB,sBAAA,CAAKb,GAAG,EAARkC,KAAO,CAAC,CAAAnB,IAAA,CAARmB,KAAI,EAAK1B,CAAC,CAAC,CAAC;EAAA,OAAA,CAAC,CAAC6B,OAAO,CAAC,UAAA7B,CAAC,EAAI;EAC7D,QAAA,IAAM8B,GAAG,GAAGzB,sBAAA,CAAKZ,UAAU,EAAfiC,KAAc,CAAC,CAAAnB,IAAA,CAAfmB,KAAI,EAAY1B,CAAC,CAAC;UAC9BK,sBAAA,CAAKhB,QAAQ,EAAbqC,KAAY,CAAC,CAACK,GAAG,CAAC1B,sBAAA,CAAKb,GAAG,EAARkC,KAAO,CAAC,CAAAnB,IAAA,CAARmB,KAAI,EAAK1B,CAAC,CAAG8B,EAAAA,GAAG,CAAC;UACnCzB,sBAAA,CAAKd,OAAO,EAAZmC,KAAW,CAAC,CAACK,GAAG,CAACD,GAAG,EAAE9B,CAAC,CAAC;EAC1B,OAAC,CAAC;QAEF,IAAMgC,UAAU,GAAG,IAAIjC,GAAG,CAAC0B,IAAI,CAACb,GAAG,CAAC,UAAAZ,CAAC,EAAA;EAAA,QAAA,OAAI,CAACK,sBAAA,CAAKb,GAAG,EAARkC,KAAO,CAAC,CAAAnB,IAAA,CAARmB,KAAI,EAAK1B,CAAC,CAAA,EAAGA,CAAC,CAAC;EAAA,OAAA,CAAC,CAAC;EAC3DK,MAAAA,sBAAA,CAAKhB,QAAQ,EAAb,IAAY,CAAC,CAACwC,OAAO,CAAC,UAACpB,CAAC,EAAEwB,GAAG,EAAK;EAChC,QAAA,IAAI,CAACD,UAAU,CAACJ,GAAG,CAACK,GAAG,CAAC,EAAE;EACxB5B,UAAAA,sBAAA,CAAKV,UAAU,EAAf+B,KAAc,CAAC,CAAAnB,IAAA,CAAfmB,KAAI,EAAYjB,CAAC,EAAEwB,GAAG,CAAA;YACtB5B,sBAAA,CAAKhB,QAAQ,EAAbqC,KAAY,CAAC,CAAO,QAAA,CAAA,CAACO,GAAG,CAAC;YACzB5B,sBAAA,CAAKd,OAAO,EAAZmC,KAAW,CAAC,CAAO,QAAA,CAAA,CAACjB,CAAC,CAAC;EACxB,SAAC,MAAM;EACLJ,UAAAA,sBAAA,CAAKX,UAAU,EAAfgC,KAAc,CAAC,CAAAnB,IAAA,CAAfmB,KAAI,EAAYjB,CAAC,EAAEuB,UAAU,CAAC1B,GAAG,CAAC2B,GAAG,CAAC,CAAA;EACxC;EACF,OAAC,CAAC;EAEF,MAAA,OAAO,IAAI;EACb;EAAC,GAAA,EAAA;MAAA/B,GAAA,EAAA,OAAA;EAAAC,IAAAA,KAAA,EAED,SAAA+B,KAAKA,GAAG;EACN,MAAA,IAAI,CAACV,MAAM,CAAC,EAAE,CAAC;EACf,MAAA,OAAO,IAAI;EACb;EAAC,GAAA,CAAA,CAAA;EAAA,CAAA;;;;;;;;", "x_google_ignoreList": [0]}