name: publish on release

on: [release]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v1

      - name: install dependencies
        run: yarn

      - name: npm publish
        run: |
          npm config set //registry.npmjs.org/:_authToken=$NODE_AUTH_TOKEN
          npm publish || true
        env:
          CI: true
          NODE_AUTH_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}
