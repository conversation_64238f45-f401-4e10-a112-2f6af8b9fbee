{"files": ["README.md"], "imageSize": 100, "contributorsPerLine": 5, "skipCi": true, "contributors": [{"login": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Anke<PERSON> Maini", "avatar_url": "https://avatars.githubusercontent.com/u/6652823?v=4", "profile": "https://ankeetmaini.dev/", "contributions": ["question", "doc", "code", "review", "maintenance"]}, {"login": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON>", "avatar_url": "https://avatars.githubusercontent.com/u/25670841?v=4", "profile": "https://github.com/iamdarshshah", "contributions": ["infra"]}], "projectName": "react-infinite-scroll-component", "projectOwner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "repoType": "github", "repoHost": "https://github.com"}