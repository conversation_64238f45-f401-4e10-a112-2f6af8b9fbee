{"name": "simplesignal", "version": "2.1.7", "description": "Super-simple signals class", "main": "./dist/SimpleSignal.js", "types": "./dist/src/SimpleSignal.d.ts", "scripts": {"build": "cross-env NODE_ENV=production ./node_modules/webpack/bin/webpack.js --config webpack.config.js --progress --verbose --colors --display-error-details", "dev": "webpack --config webpack.config.js --progress --verbose --colors --display-error-details", "coverage": "nyc npm test", "coverage:report": "nyc npm test && nyc report --reporter=text-lcov | coveralls", "test": "mocha test/.setup.js test/**/*-test-*.js", "test:watch": "mocha -w test/.setup.js test/**/*-test-*.js", "lint": "tslint -c tslint.json src/*.ts", "prepublish": "npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/zeh/simplesignal.git"}, "keywords": ["signals", "events", "callbacks", "minimal", "small", "simple", "tiny", "typescript"], "author": "<PERSON><PERSON> <<EMAIL>> http://zehfernando.com", "license": "MIT", "bugs": {"url": "https://github.com/zeh/simplesignal/issues"}, "homepage": "https://github.com/zeh/simplesignal#readme", "devDependencies": {"babel-core": "^6.20.0", "babel-loader": "^6.2.9", "babel-plugin-external-helpers": "^6.18.0", "babel-preset-es2015": "^6.18.0", "babel-preset-stage-2": "^6.18.0", "chai": "^3.5.0", "core-js": "^2.4.1", "coveralls": "^2.11.15", "cross-env": "^3.1.3", "import-sort": "^2.4.0", "import-sort-style-module": "^2.4.0", "mocha": "^3.2.0", "mocha-lcov-reporter": "^1.2.0", "nyc": "^10.0.0", "rimraf": "^2.5.4", "ts-loader": "^1.3.2", "tslint": "^4.0.2", "tslint-loader": "^3.3.0", "typescript": "^2.1.4", "webpack": "^1.14.0"}, "importSort": {".js, .jsx, .es6, .es": {"parser": "babylon", "style": "module"}, ".ts, .tsx": {"parser": "typescript", "style": "module"}}}