{"name": "@types/stats.js", "version": "0.17.4", "description": "TypeScript definitions for stats.js", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/stats.js", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "gregolai", "url": "https://github.com/gregolai"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/hberntsen"}, {"name": "<PERSON>", "githubUsername": "danvk", "url": "https://github.com/danvk"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/stats.js"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "33db1076f265c7fe8fc161806c4b7bf2b877c76e5896c2a460c0e23b3b6ba840", "typeScriptVersion": "5.1"}