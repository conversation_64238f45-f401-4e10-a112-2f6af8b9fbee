// Type definitions for three 0.152
// Project: https://threejs.org/
// Definitions by: <PERSON> <https://github.com/joshuaellis>
//                 <PERSON> <https://github.com/Methuselah96>
// Definitions: https://github.com/DefinitelyTyped/DefinitelyTyped
// Minimum TypeScript Version: 4.4

// To update three.js type definition, please make changes to the repository at:
// https://github.com/three-types/three-ts-types.
// Periodically, the updates from the repository are pushed to DefinitelyTyped
// and released in the @types/three npm package.

export * from './src/Three';

export as namespace THREE;
