{"name": "@types/three", "version": "0.152.1", "description": "TypeScript definitions for three", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/three", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/joshua<PERSON>s", "githubUsername": "josh<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/Methuselah96", "githubUsername": "Methuselah96"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/three"}, "scripts": {}, "dependencies": {"@tweenjs/tween.js": "~18.6.4", "@types/stats.js": "*", "@types/webxr": "*", "fflate": "~0.6.9", "lil-gui": "~0.17.0"}, "typesPublisherContentHash": "4a1b7a1820d0565498723427b382216d64d91a892d84b4a3ad2c73c41a5fe823", "typeScriptVersion": "4.4", "exports": {".": {"import": "./build/three.module.js", "require": "./build/three.cjs"}, "./examples/fonts/*": "./examples/fonts/*", "./examples/jsm/*": "./examples/jsm/*", "./addons/*": "./examples/jsm/*", "./src/*": "./src/*", "./nodes": "./examples/jsm/nodes/Nodes.js", "./package.json": "./package.json"}}