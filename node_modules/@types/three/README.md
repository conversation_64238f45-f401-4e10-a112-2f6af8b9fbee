# Installation
> `npm install --save @types/three`

# Summary
This package contains type definitions for three (https://threejs.org/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/three.

### Additional Details
 * Last updated: Thu, 25 May 2023 20:34:45 GMT
 * Dependencies: [@types/fflate](https://npmjs.com/package/@types/fflate), [@types/lil-gui](https://npmjs.com/package/@types/lil-gui), [@types/stats.js](https://npmjs.com/package/@types/stats.js), [@types/tweenjs__tween.js](https://npmjs.com/package/@types/tweenjs__tween.js), [@types/webxr](https://npmjs.com/package/@types/webxr)
 * Global values: `THREE`

# Credits
These definitions were written by [<PERSON>](https://github.com/joshua<PERSON>s), and [<PERSON>](https://github.com/Methuselah96).
