{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-invariant/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACWA,wCAAyB;AAczB,SAAS,QAAA,CAAS,KAAA,EAAoD;AACpE,EAAA,GAAA,CAAI,CAAC,KAAA,EAAO;AACV,IAAA,MAAM,IAAI,KAAA,CAAM,mBAAmB,CAAA;AAAA,EACrC;AAEA,EAAA,GAAA,CAAI,CAAC,KAAA,CAAM,OAAA,CAAQ,KAAK,CAAA,EAAG;AACzB,IAAA,GAAA,CACE,KAAA,CAAM,KAAA,IAAS,UAAA,GACf,KAAA,CAAM,SAAA,IAAa,KAAA,GACnB,KAAA,CAAM,QAAA,CAAS,KAAA,IAAS,OAAA,EACxB;AACA,MAAA,OAAO,CAAC,GAAG,KAAA,CAAM,QAAA,CAAS,WAAW,CAAA;AAAA,IACvC;AACA,IAAA,GAAA,CAAI,KAAA,CAAM,KAAA,IAAS,OAAA,EAAS;AAC1B,MAAA,OAAO,CAAC,GAAG,KAAA,CAAM,WAAW,CAAA;AAAA,IAC9B;AAAA,EACF;AACA,EAAA,GAAA,CACE,KAAA,CAAM,OAAA,CAAQ,KAAK,EAAA,GACnB,KAAA,CAAM,OAAA,GAAU,EAAA,GAChB,CAAC,KAAA,CAAM,OAAA,CAAQ,KAAA,CAAM,CAAC,CAAC,EAAA,GACvB,CAAC,KAAA,CAAM,OAAA,CAAQ,KAAA,CAAM,CAAC,CAAC,CAAA,EACvB;AACA,IAAA,OAAO,CAAC,GAAG,KAAK,CAAA;AAAA,EAClB;AAEA,EAAA,MAAM,IAAI,KAAA,CAAM,oDAAoD,CAAA;AACtE;AAcA,SAAS,SAAA,CAQP,MAAA,EAAuC;AACvC,EAAA,GAAA,CAAI,KAAA,CAAM,OAAA,CAAQ,MAAM,CAAA,EAAG;AACzB,IAAA,OAAO,MAAA;AAAA,EACT;AAGA,EAAA,GAAA,CAAI,MAAA,CAAO,KAAA,IAAS,SAAA,EAAW;AAC7B,IAAA,GAAA,CAAI,MAAA,CAAO,SAAA,IAAa,IAAA,EAAM;AAC5B,MAAA,OAAO,MAAA,CAAO,QAAA,CAAS,WAAA;AAAA,IACzB;AAAA,EACF,EAAA,KAAO;AAEL,IAAA,GAAA,CAAI,MAAA,CAAO,WAAA,EAAa;AACtB,MAAA,OAAO,MAAA,CAAO,WAAA;AAAA,IAChB;AAAA,EACF;AAEA,EAAA,MAAM,IAAI,KAAA;AAAA,IACR;AAAA,EACF,CAAA;AACF;AASA,SAAS,cAAA,CAAe,WAAA,EAA6B;AACnD,EAAA,GAAA,CACE,WAAA,CAAY,OAAA,EAAS,EAAA,GACrB,+BAAA,WAAS,CAAY,CAAC,CAAC,EAAA,GACvB,+BAAA,WAAS,CAAY,CAAC,CAAC,CAAA,EACvB;AACA,IAAA,OAAO,IAAA;AAAA,EACT;AAEA,EAAA,GAAA,CAAI,KAAA,CAAM,OAAA,CAAQ,WAAA,CAAY,CAAC,CAAC,EAAA,GAAK,WAAA,CAAY,CAAC,CAAA,CAAE,MAAA,EAAQ;AAC1D,IAAA,OAAO,cAAA,CAAe,WAAA,CAAY,CAAC,CAAC,CAAA;AAAA,EACtC;AACA,EAAA,MAAM,IAAI,KAAA,CAAM,uCAAuC,CAAA;AACzD;AAWA,SAAS,WAAA,CAAY,KAAA,EAAY,IAAA,EAAc,IAAA,EAAoB;AACjE,EAAA,GAAA,CAAI,CAAC,KAAA,GAAQ,CAAC,IAAA,EAAM;AAClB,IAAA,MAAM,IAAI,KAAA,CAAM,wBAAwB,CAAA;AAAA,EAC1C;AAEA,EAAA,GAAA,CAAI,CAAC,MAAA,GAAS,KAAA,CAAM,KAAA,IAAS,IAAA,EAAM;AACjC,IAAA,MAAM,IAAI,KAAA;AAAA,MACR,oBAAA,EACE,KAAA,EACA,eAAA,EACA,KAAA,EACA,WAAA,EACA,KAAA,CAAM;AAAA,IACV,CAAA;AAAA,EACF;AACF;AAYA,SAAS,SAAA,CAAU,OAAA,EAAuB,IAAA,EAAc,IAAA,EAAoB;AAC1E,EAAA,GAAA,CAAI,CAAC,OAAA,EAAS;AACZ,IAAA,MAAM,IAAI,KAAA,CAAM,mBAAmB,CAAA;AAAA,EACrC;AACA,EAAA,GAAA,CAAI,CAAC,IAAA,EAAM;AACT,IAAA,MAAM,IAAI,KAAA,CAAM,8BAA8B,CAAA;AAAA,EAChD;AACA,EAAA,GAAA,CAAI,CAAC,QAAA,GAAW,OAAA,CAAQ,KAAA,IAAS,UAAA,GAAa,CAAC,OAAA,CAAQ,QAAA,EAAU;AAC/D,IAAA,MAAM,IAAI,KAAA;AAAA,MACR,oBAAA,EAAsB,KAAA,EAAO;AAAA,IAC/B,CAAA;AAAA,EACF;AACA,EAAA,GAAA,CAAI,CAAC,OAAA,CAAQ,SAAA,GAAY,OAAA,CAAQ,QAAA,CAAS,KAAA,IAAS,IAAA,EAAM;AACvD,IAAA,MAAM,IAAI,KAAA;AAAA,MACR,oBAAA,EACE,KAAA,EACA,eAAA,EACA,KAAA,EACA,WAAA,EACA,OAAA,CAAQ,QAAA,CAAS;AAAA,IACrB,CAAA;AAAA,EACF;AACF;AAYA,SAAS,YAAA,CACP,iBAAA,EACA,IAAA,EACA,IAAA,EACA;AACA,EAAA,GAAA,CAAI,CAAC,iBAAA,EAAmB;AACtB,IAAA,MAAM,IAAI,KAAA,CAAM,6BAA6B,CAAA;AAAA,EAC/C;AACA,EAAA,GAAA,CAAI,CAAC,IAAA,EAAM;AACT,IAAA,MAAM,IAAI,KAAA,CAAM,iCAAiC,CAAA;AAAA,EACnD;AACA,EAAA,GAAA,CAAI,CAAC,kBAAA,GAAqB,iBAAA,CAAkB,KAAA,IAAS,mBAAA,EAAqB;AACxE,IAAA,MAAM,IAAI,KAAA;AAAA,MACR,oBAAA,EAAsB,KAAA,EAAO;AAAA,IAC/B,CAAA;AAAA,EACF;AACA,EAAA,IAAA,CAAA,MAAW,QAAA,GAAW,iBAAA,CAAkB,QAAA,EAAU;AAChD,IAAA,GAAA,CAAI,CAAC,QAAA,GAAW,OAAA,CAAQ,KAAA,IAAS,UAAA,GAAa,CAAC,OAAA,CAAQ,QAAA,EAAU;AAC/D,MAAA,MAAM,IAAI,KAAA;AAAA,QACR,oBAAA,EAAsB,KAAA,EAAO;AAAA,MAC/B,CAAA;AAAA,IACF;AACA,IAAA,GAAA,CAAI,CAAC,OAAA,CAAQ,SAAA,GAAY,OAAA,CAAQ,QAAA,CAAS,KAAA,IAAS,IAAA,EAAM;AACvD,MAAA,MAAM,IAAI,KAAA;AAAA,QACR,oBAAA,EACE,KAAA,EACA,eAAA,EACA,KAAA,EACA,WAAA,EACA,OAAA,CAAQ,QAAA,CAAS;AAAA,MACrB,CAAA;AAAA,IACF;AAAA,EACF;AACF;AAoBA,SAAS,OAAA,CAA4B,OAAA,EAA4B;AAC/D,EAAA,GAAA,CAAI,OAAA,CAAQ,KAAA,IAAS,SAAA,EAAW;AAC9B,IAAA,OAAO,OAAA,CAAQ,QAAA;AAAA,EACjB;AACA,EAAA,OAAO,OAAA;AACT;AAoBA,SAAS,OAAA,CACP,OAAA,EACA,KAAA,EACQ;AACR,EAAA,GAAA,CAAI,OAAA,CAAQ,KAAA,IAAS,mBAAA,EAAqB;AACxC,IAAA,OAAO,mBAAA;AAAA,EACT;AACA,EAAA,GAAA,CAAI,OAAA,CAAQ,KAAA,IAAS,oBAAA,EAAsB;AACzC,IAAA,OAAO,oBAAA;AAAA,EACT;AACA,EAAA,GAAA,CAAI,OAAA,CAAQ,KAAA,IAAS,UAAA,GAAa,OAAA,CAAQ,SAAA,IAAa,IAAA,EAAM;AAC3D,IAAA,OAAO,OAAA,CAAQ,QAAA,CAAS,IAAA;AAAA,EAC1B;AACA,EAAA,OAAO,OAAA,CAAQ,IAAA;AACjB;ADpKA;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACF,iQAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-invariant/dist/cjs/index.cjs", "sourcesContent": [null, "import {\n  Feature,\n  FeatureCollection,\n  Geometry,\n  LineString,\n  MultiPoint,\n  MultiLineString,\n  MultiPolygon,\n  Point,\n  Polygon,\n} from \"geojson\";\nimport { isNumber } from \"@turf/helpers\";\n\n/**\n * Unwrap a coordinate from a Point Feature, Geometry or a single coordinate.\n *\n * @function\n * @param {Array<number>|Geometry<Point>|Feature<Point>} coord GeoJSON Point or an Array of numbers\n * @returns {Array<number>} coordinates\n * @example\n * var pt = turf.point([10, 10]);\n *\n * var coord = turf.getCoord(pt);\n * //= [10, 10]\n */\nfunction getCoord(coord: Feature<Point> | Point | number[]): number[] {\n  if (!coord) {\n    throw new Error(\"coord is required\");\n  }\n\n  if (!Array.isArray(coord)) {\n    if (\n      coord.type === \"Feature\" &&\n      coord.geometry !== null &&\n      coord.geometry.type === \"Point\"\n    ) {\n      return [...coord.geometry.coordinates];\n    }\n    if (coord.type === \"Point\") {\n      return [...coord.coordinates];\n    }\n  }\n  if (\n    Array.isArray(coord) &&\n    coord.length >= 2 &&\n    !Array.isArray(coord[0]) &&\n    !Array.isArray(coord[1])\n  ) {\n    return [...coord];\n  }\n\n  throw new Error(\"coord must be GeoJSON Point or an Array of numbers\");\n}\n\n/**\n * Unwrap coordinates from a Feature, Geometry Object or an Array\n *\n * @function\n * @param {Array<any>|Geometry|Feature} coords Feature, Geometry Object or an Array\n * @returns {Array<any>} coordinates\n * @example\n * var poly = turf.polygon([[[119.32, -8.7], [119.55, -8.69], [119.51, -8.54], [119.32, -8.7]]]);\n *\n * var coords = turf.getCoords(poly);\n * //= [[[119.32, -8.7], [119.55, -8.69], [119.51, -8.54], [119.32, -8.7]]]\n */\nfunction getCoords<\n  G extends\n    | Point\n    | LineString\n    | Polygon\n    | MultiPoint\n    | MultiLineString\n    | MultiPolygon,\n>(coords: any[] | Feature<G> | G): any[] {\n  if (Array.isArray(coords)) {\n    return coords;\n  }\n\n  // Feature\n  if (coords.type === \"Feature\") {\n    if (coords.geometry !== null) {\n      return coords.geometry.coordinates;\n    }\n  } else {\n    // Geometry\n    if (coords.coordinates) {\n      return coords.coordinates;\n    }\n  }\n\n  throw new Error(\n    \"coords must be GeoJSON Feature, Geometry Object or an Array\"\n  );\n}\n\n/**\n * Checks if coordinates contains a number\n *\n * @function\n * @param {Array<any>} coordinates GeoJSON Coordinates\n * @returns {boolean} true if Array contains a number\n */\nfunction containsNumber(coordinates: any[]): boolean {\n  if (\n    coordinates.length > 1 &&\n    isNumber(coordinates[0]) &&\n    isNumber(coordinates[1])\n  ) {\n    return true;\n  }\n\n  if (Array.isArray(coordinates[0]) && coordinates[0].length) {\n    return containsNumber(coordinates[0]);\n  }\n  throw new Error(\"coordinates must only contain numbers\");\n}\n\n/**\n * Enforce expectations about types of GeoJSON objects for Turf.\n *\n * @function\n * @param {GeoJSON} value any GeoJSON object\n * @param {string} type expected GeoJSON type\n * @param {string} name name of calling function\n * @throws {Error} if value is not the expected type.\n */\nfunction geojsonType(value: any, type: string, name: string): void {\n  if (!type || !name) {\n    throw new Error(\"type and name required\");\n  }\n\n  if (!value || value.type !== type) {\n    throw new Error(\n      \"Invalid input to \" +\n        name +\n        \": must be a \" +\n        type +\n        \", given \" +\n        value.type\n    );\n  }\n}\n\n/**\n * Enforce expectations about types of {@link Feature} inputs for Turf.\n * Internally this uses {@link geojsonType} to judge geometry types.\n *\n * @function\n * @param {Feature} feature a feature with an expected geometry type\n * @param {string} type expected GeoJSON type\n * @param {string} name name of calling function\n * @throws {Error} error if value is not the expected type.\n */\nfunction featureOf(feature: Feature<any>, type: string, name: string): void {\n  if (!feature) {\n    throw new Error(\"No feature passed\");\n  }\n  if (!name) {\n    throw new Error(\".featureOf() requires a name\");\n  }\n  if (!feature || feature.type !== \"Feature\" || !feature.geometry) {\n    throw new Error(\n      \"Invalid input to \" + name + \", Feature with geometry required\"\n    );\n  }\n  if (!feature.geometry || feature.geometry.type !== type) {\n    throw new Error(\n      \"Invalid input to \" +\n        name +\n        \": must be a \" +\n        type +\n        \", given \" +\n        feature.geometry.type\n    );\n  }\n}\n\n/**\n * Enforce expectations about types of {@link FeatureCollection} inputs for Turf.\n * Internally this uses {@link geojsonType} to judge geometry types.\n *\n * @function\n * @param {FeatureCollection} featureCollection a FeatureCollection for which features will be judged\n * @param {string} type expected GeoJSON type\n * @param {string} name name of calling function\n * @throws {Error} if value is not the expected type.\n */\nfunction collectionOf(\n  featureCollection: FeatureCollection<any>,\n  type: string,\n  name: string\n) {\n  if (!featureCollection) {\n    throw new Error(\"No featureCollection passed\");\n  }\n  if (!name) {\n    throw new Error(\".collectionOf() requires a name\");\n  }\n  if (!featureCollection || featureCollection.type !== \"FeatureCollection\") {\n    throw new Error(\n      \"Invalid input to \" + name + \", FeatureCollection required\"\n    );\n  }\n  for (const feature of featureCollection.features) {\n    if (!feature || feature.type !== \"Feature\" || !feature.geometry) {\n      throw new Error(\n        \"Invalid input to \" + name + \", Feature with geometry required\"\n      );\n    }\n    if (!feature.geometry || feature.geometry.type !== type) {\n      throw new Error(\n        \"Invalid input to \" +\n          name +\n          \": must be a \" +\n          type +\n          \", given \" +\n          feature.geometry.type\n      );\n    }\n  }\n}\n\n/**\n * Get Geometry from Feature or Geometry Object\n *\n * @param {Feature|Geometry} geojson GeoJSON Feature or Geometry Object\n * @returns {Geometry|null} GeoJSON Geometry Object\n * @throws {Error} if geojson is not a Feature or Geometry Object\n * @example\n * var point = {\n *   \"type\": \"Feature\",\n *   \"properties\": {},\n *   \"geometry\": {\n *     \"type\": \"Point\",\n *     \"coordinates\": [110, 40]\n *   }\n * }\n * var geom = turf.getGeom(point)\n * //={\"type\": \"Point\", \"coordinates\": [110, 40]}\n */\nfunction getGeom<G extends Geometry>(geojson: Feature<G> | G): G {\n  if (geojson.type === \"Feature\") {\n    return geojson.geometry;\n  }\n  return geojson;\n}\n\n/**\n * Get GeoJSON object's type, Geometry type is prioritize.\n *\n * @param {GeoJSON} geojson GeoJSON object\n * @param {string} [name=\"geojson\"] name of the variable to display in error message (unused)\n * @returns {string} GeoJSON type\n * @example\n * var point = {\n *   \"type\": \"Feature\",\n *   \"properties\": {},\n *   \"geometry\": {\n *     \"type\": \"Point\",\n *     \"coordinates\": [110, 40]\n *   }\n * }\n * var geom = turf.getType(point)\n * //=\"Point\"\n */\nfunction getType(\n  geojson: Feature<any> | FeatureCollection<any> | Geometry,\n  _name?: string\n): string {\n  if (geojson.type === \"FeatureCollection\") {\n    return \"FeatureCollection\";\n  }\n  if (geojson.type === \"GeometryCollection\") {\n    return \"GeometryCollection\";\n  }\n  if (geojson.type === \"Feature\" && geojson.geometry !== null) {\n    return geojson.geometry.type;\n  }\n  return geojson.type;\n}\n\nexport {\n  getCoord,\n  getCoords,\n  containsNumber,\n  geojsonType,\n  featureOf,\n  collectionOf,\n  getGeom,\n  getType,\n};\n// No default export!\n"]}