{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import {\n  Feature,\n  FeatureCollection,\n  Geometry,\n  LineString,\n  MultiPoint,\n  MultiLineString,\n  MultiPolygon,\n  Point,\n  Polygon,\n} from \"geojson\";\nimport { isNumber } from \"@turf/helpers\";\n\n/**\n * Unwrap a coordinate from a Point Feature, Geometry or a single coordinate.\n *\n * @function\n * @param {Array<number>|Geometry<Point>|Feature<Point>} coord GeoJSON Point or an Array of numbers\n * @returns {Array<number>} coordinates\n * @example\n * var pt = turf.point([10, 10]);\n *\n * var coord = turf.getCoord(pt);\n * //= [10, 10]\n */\nfunction getCoord(coord: Feature<Point> | Point | number[]): number[] {\n  if (!coord) {\n    throw new Error(\"coord is required\");\n  }\n\n  if (!Array.isArray(coord)) {\n    if (\n      coord.type === \"Feature\" &&\n      coord.geometry !== null &&\n      coord.geometry.type === \"Point\"\n    ) {\n      return [...coord.geometry.coordinates];\n    }\n    if (coord.type === \"Point\") {\n      return [...coord.coordinates];\n    }\n  }\n  if (\n    Array.isArray(coord) &&\n    coord.length >= 2 &&\n    !Array.isArray(coord[0]) &&\n    !Array.isArray(coord[1])\n  ) {\n    return [...coord];\n  }\n\n  throw new Error(\"coord must be GeoJSON Point or an Array of numbers\");\n}\n\n/**\n * Unwrap coordinates from a Feature, Geometry Object or an Array\n *\n * @function\n * @param {Array<any>|Geometry|Feature} coords Feature, Geometry Object or an Array\n * @returns {Array<any>} coordinates\n * @example\n * var poly = turf.polygon([[[119.32, -8.7], [119.55, -8.69], [119.51, -8.54], [119.32, -8.7]]]);\n *\n * var coords = turf.getCoords(poly);\n * //= [[[119.32, -8.7], [119.55, -8.69], [119.51, -8.54], [119.32, -8.7]]]\n */\nfunction getCoords<\n  G extends\n    | Point\n    | LineString\n    | Polygon\n    | MultiPoint\n    | MultiLineString\n    | MultiPolygon,\n>(coords: any[] | Feature<G> | G): any[] {\n  if (Array.isArray(coords)) {\n    return coords;\n  }\n\n  // Feature\n  if (coords.type === \"Feature\") {\n    if (coords.geometry !== null) {\n      return coords.geometry.coordinates;\n    }\n  } else {\n    // Geometry\n    if (coords.coordinates) {\n      return coords.coordinates;\n    }\n  }\n\n  throw new Error(\n    \"coords must be GeoJSON Feature, Geometry Object or an Array\"\n  );\n}\n\n/**\n * Checks if coordinates contains a number\n *\n * @function\n * @param {Array<any>} coordinates GeoJSON Coordinates\n * @returns {boolean} true if Array contains a number\n */\nfunction containsNumber(coordinates: any[]): boolean {\n  if (\n    coordinates.length > 1 &&\n    isNumber(coordinates[0]) &&\n    isNumber(coordinates[1])\n  ) {\n    return true;\n  }\n\n  if (Array.isArray(coordinates[0]) && coordinates[0].length) {\n    return containsNumber(coordinates[0]);\n  }\n  throw new Error(\"coordinates must only contain numbers\");\n}\n\n/**\n * Enforce expectations about types of GeoJSON objects for Turf.\n *\n * @function\n * @param {GeoJSON} value any GeoJSON object\n * @param {string} type expected GeoJSON type\n * @param {string} name name of calling function\n * @throws {Error} if value is not the expected type.\n */\nfunction geojsonType(value: any, type: string, name: string): void {\n  if (!type || !name) {\n    throw new Error(\"type and name required\");\n  }\n\n  if (!value || value.type !== type) {\n    throw new Error(\n      \"Invalid input to \" +\n        name +\n        \": must be a \" +\n        type +\n        \", given \" +\n        value.type\n    );\n  }\n}\n\n/**\n * Enforce expectations about types of {@link Feature} inputs for Turf.\n * Internally this uses {@link geojsonType} to judge geometry types.\n *\n * @function\n * @param {Feature} feature a feature with an expected geometry type\n * @param {string} type expected GeoJSON type\n * @param {string} name name of calling function\n * @throws {Error} error if value is not the expected type.\n */\nfunction featureOf(feature: Feature<any>, type: string, name: string): void {\n  if (!feature) {\n    throw new Error(\"No feature passed\");\n  }\n  if (!name) {\n    throw new Error(\".featureOf() requires a name\");\n  }\n  if (!feature || feature.type !== \"Feature\" || !feature.geometry) {\n    throw new Error(\n      \"Invalid input to \" + name + \", Feature with geometry required\"\n    );\n  }\n  if (!feature.geometry || feature.geometry.type !== type) {\n    throw new Error(\n      \"Invalid input to \" +\n        name +\n        \": must be a \" +\n        type +\n        \", given \" +\n        feature.geometry.type\n    );\n  }\n}\n\n/**\n * Enforce expectations about types of {@link FeatureCollection} inputs for Turf.\n * Internally this uses {@link geojsonType} to judge geometry types.\n *\n * @function\n * @param {FeatureCollection} featureCollection a FeatureCollection for which features will be judged\n * @param {string} type expected GeoJSON type\n * @param {string} name name of calling function\n * @throws {Error} if value is not the expected type.\n */\nfunction collectionOf(\n  featureCollection: FeatureCollection<any>,\n  type: string,\n  name: string\n) {\n  if (!featureCollection) {\n    throw new Error(\"No featureCollection passed\");\n  }\n  if (!name) {\n    throw new Error(\".collectionOf() requires a name\");\n  }\n  if (!featureCollection || featureCollection.type !== \"FeatureCollection\") {\n    throw new Error(\n      \"Invalid input to \" + name + \", FeatureCollection required\"\n    );\n  }\n  for (const feature of featureCollection.features) {\n    if (!feature || feature.type !== \"Feature\" || !feature.geometry) {\n      throw new Error(\n        \"Invalid input to \" + name + \", Feature with geometry required\"\n      );\n    }\n    if (!feature.geometry || feature.geometry.type !== type) {\n      throw new Error(\n        \"Invalid input to \" +\n          name +\n          \": must be a \" +\n          type +\n          \", given \" +\n          feature.geometry.type\n      );\n    }\n  }\n}\n\n/**\n * Get Geometry from Feature or Geometry Object\n *\n * @param {Feature|Geometry} geojson GeoJSON Feature or Geometry Object\n * @returns {Geometry|null} GeoJSON Geometry Object\n * @throws {Error} if geojson is not a Feature or Geometry Object\n * @example\n * var point = {\n *   \"type\": \"Feature\",\n *   \"properties\": {},\n *   \"geometry\": {\n *     \"type\": \"Point\",\n *     \"coordinates\": [110, 40]\n *   }\n * }\n * var geom = turf.getGeom(point)\n * //={\"type\": \"Point\", \"coordinates\": [110, 40]}\n */\nfunction getGeom<G extends Geometry>(geojson: Feature<G> | G): G {\n  if (geojson.type === \"Feature\") {\n    return geojson.geometry;\n  }\n  return geojson;\n}\n\n/**\n * Get GeoJSON object's type, Geometry type is prioritize.\n *\n * @param {GeoJSON} geojson GeoJSON object\n * @param {string} [name=\"geojson\"] name of the variable to display in error message (unused)\n * @returns {string} GeoJSON type\n * @example\n * var point = {\n *   \"type\": \"Feature\",\n *   \"properties\": {},\n *   \"geometry\": {\n *     \"type\": \"Point\",\n *     \"coordinates\": [110, 40]\n *   }\n * }\n * var geom = turf.getType(point)\n * //=\"Point\"\n */\nfunction getType(\n  geojson: Feature<any> | FeatureCollection<any> | Geometry,\n  _name?: string\n): string {\n  if (geojson.type === \"FeatureCollection\") {\n    return \"FeatureCollection\";\n  }\n  if (geojson.type === \"GeometryCollection\") {\n    return \"GeometryCollection\";\n  }\n  if (geojson.type === \"Feature\" && geojson.geometry !== null) {\n    return geojson.geometry.type;\n  }\n  return geojson.type;\n}\n\nexport {\n  getCoord,\n  getCoords,\n  containsNumber,\n  geojsonType,\n  featureOf,\n  collectionOf,\n  getGeom,\n  getType,\n};\n// No default export!\n"], "mappings": ";AAWA,SAAS,gBAAgB;AAczB,SAAS,SAAS,OAAoD;AACpE,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,MAAM,mBAAmB;AAAA,EACrC;AAEA,MAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,QACE,MAAM,SAAS,aACf,MAAM,aAAa,QACnB,MAAM,SAAS,SAAS,SACxB;AACA,aAAO,CAAC,GAAG,MAAM,SAAS,WAAW;AAAA,IACvC;AACA,QAAI,MAAM,SAAS,SAAS;AAC1B,aAAO,CAAC,GAAG,MAAM,WAAW;AAAA,IAC9B;AAAA,EACF;AACA,MACE,MAAM,QAAQ,KAAK,KACnB,MAAM,UAAU,KAChB,CAAC,MAAM,QAAQ,MAAM,CAAC,CAAC,KACvB,CAAC,MAAM,QAAQ,MAAM,CAAC,CAAC,GACvB;AACA,WAAO,CAAC,GAAG,KAAK;AAAA,EAClB;AAEA,QAAM,IAAI,MAAM,oDAAoD;AACtE;AAcA,SAAS,UAQP,QAAuC;AACvC,MAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,SAAS,WAAW;AAC7B,QAAI,OAAO,aAAa,MAAM;AAC5B,aAAO,OAAO,SAAS;AAAA,IACzB;AAAA,EACF,OAAO;AAEL,QAAI,OAAO,aAAa;AACtB,aAAO,OAAO;AAAA,IAChB;AAAA,EACF;AAEA,QAAM,IAAI;AAAA,IACR;AAAA,EACF;AACF;AASA,SAAS,eAAe,aAA6B;AACnD,MACE,YAAY,SAAS,KACrB,SAAS,YAAY,CAAC,CAAC,KACvB,SAAS,YAAY,CAAC,CAAC,GACvB;AACA,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,QAAQ,YAAY,CAAC,CAAC,KAAK,YAAY,CAAC,EAAE,QAAQ;AAC1D,WAAO,eAAe,YAAY,CAAC,CAAC;AAAA,EACtC;AACA,QAAM,IAAI,MAAM,uCAAuC;AACzD;AAWA,SAAS,YAAY,OAAY,MAAc,MAAoB;AACjE,MAAI,CAAC,QAAQ,CAAC,MAAM;AAClB,UAAM,IAAI,MAAM,wBAAwB;AAAA,EAC1C;AAEA,MAAI,CAAC,SAAS,MAAM,SAAS,MAAM;AACjC,UAAM,IAAI;AAAA,MACR,sBACE,OACA,iBACA,OACA,aACA,MAAM;AAAA,IACV;AAAA,EACF;AACF;AAYA,SAAS,UAAU,SAAuB,MAAc,MAAoB;AAC1E,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,mBAAmB;AAAA,EACrC;AACA,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,MAAM,8BAA8B;AAAA,EAChD;AACA,MAAI,CAAC,WAAW,QAAQ,SAAS,aAAa,CAAC,QAAQ,UAAU;AAC/D,UAAM,IAAI;AAAA,MACR,sBAAsB,OAAO;AAAA,IAC/B;AAAA,EACF;AACA,MAAI,CAAC,QAAQ,YAAY,QAAQ,SAAS,SAAS,MAAM;AACvD,UAAM,IAAI;AAAA,MACR,sBACE,OACA,iBACA,OACA,aACA,QAAQ,SAAS;AAAA,IACrB;AAAA,EACF;AACF;AAYA,SAAS,aACP,mBACA,MACA,MACA;AACA,MAAI,CAAC,mBAAmB;AACtB,UAAM,IAAI,MAAM,6BAA6B;AAAA,EAC/C;AACA,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,MAAM,iCAAiC;AAAA,EACnD;AACA,MAAI,CAAC,qBAAqB,kBAAkB,SAAS,qBAAqB;AACxE,UAAM,IAAI;AAAA,MACR,sBAAsB,OAAO;AAAA,IAC/B;AAAA,EACF;AACA,aAAW,WAAW,kBAAkB,UAAU;AAChD,QAAI,CAAC,WAAW,QAAQ,SAAS,aAAa,CAAC,QAAQ,UAAU;AAC/D,YAAM,IAAI;AAAA,QACR,sBAAsB,OAAO;AAAA,MAC/B;AAAA,IACF;AACA,QAAI,CAAC,QAAQ,YAAY,QAAQ,SAAS,SAAS,MAAM;AACvD,YAAM,IAAI;AAAA,QACR,sBACE,OACA,iBACA,OACA,aACA,QAAQ,SAAS;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AACF;AAoBA,SAAS,QAA4B,SAA4B;AAC/D,MAAI,QAAQ,SAAS,WAAW;AAC9B,WAAO,QAAQ;AAAA,EACjB;AACA,SAAO;AACT;AAoBA,SAAS,QACP,SACA,OACQ;AACR,MAAI,QAAQ,SAAS,qBAAqB;AACxC,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,SAAS,sBAAsB;AACzC,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,SAAS,aAAa,QAAQ,aAAa,MAAM;AAC3D,WAAO,QAAQ,SAAS;AAAA,EAC1B;AACA,SAAO,QAAQ;AACjB;", "names": []}