"use strict";Object.defineProperty(exports, "__esModule", {value: true}); function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }// index.ts
var _pointinpolygonhao = require('point-in-polygon-hao'); var _pointinpolygonhao2 = _interopRequireDefault(_pointinpolygonhao);
var _invariant = require('@turf/invariant');
function booleanPointInPolygon(point, polygon, options = {}) {
  if (!point) {
    throw new Error("point is required");
  }
  if (!polygon) {
    throw new Error("polygon is required");
  }
  const pt = _invariant.getCoord.call(void 0, point);
  const geom = _invariant.getGeom.call(void 0, polygon);
  const type = geom.type;
  const bbox = polygon.bbox;
  let polys = geom.coordinates;
  if (bbox && inBBox(pt, bbox) === false) {
    return false;
  }
  if (type === "Polygon") {
    polys = [polys];
  }
  let result = false;
  for (var i = 0; i < polys.length; ++i) {
    const polyResult = _pointinpolygonhao2.default.call(void 0, pt, polys[i]);
    if (polyResult === 0) return options.ignoreBoundary ? false : true;
    else if (polyResult) result = true;
  }
  return result;
}
function inBBox(pt, bbox) {
  return bbox[0] <= pt[0] && bbox[1] <= pt[1] && bbox[2] >= pt[0] && bbox[3] >= pt[1];
}
var turf_boolean_point_in_polygon_default = booleanPointInPolygon;



exports.booleanPointInPolygon = booleanPointInPolygon; exports.default = turf_boolean_point_in_polygon_default;
//# sourceMappingURL=index.cjs.map