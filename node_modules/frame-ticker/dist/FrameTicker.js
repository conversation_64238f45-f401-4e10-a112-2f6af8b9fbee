!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("FrameTicker",[],t):"object"==typeof exports?exports.FrameTicker=t():e.FrameTicker=t()}(this,function(){return function(e){function t(n){if(i[n])return i[n].exports;var r=i[n]={exports:{},id:n,loaded:!1};return e[n].call(r.exports,r,r.exports,t),r.loaded=!0,r.exports}var i={};return t.m=e,t.c=i,t.p="",t(0)}([function(e,t,i){"use strict";var n=i(1),r=function(){function e(e,t,i){void 0===e&&(e=NaN),void 0===t&&(t=NaN),void 0===i&&(i=!1),this._minFPS=t,this._maxFPS=e,this._timeScale=1,this._currentTick=0,this._currentTime=0,this._tickDeltaTime=0,this._isRunning=!1,this._maxInterval=isNaN(this._minFPS)?NaN:1e3/this._minFPS,this._minInterval=isNaN(this._maxFPS)?NaN:1e3/this._maxFPS,this._onResume=new n.default,this._onPause=new n.default,this._onTick=new n.default,this._onTickOncePerFrame=new n.default,i||this.resume()}return e.prototype.updateOnce=function(e){e(this.currentTimeSeconds,this.tickDeltaTimeSeconds,this.currentTick)},e.prototype.resume=function(){this._isRunning||(this._isRunning=!0,this._lastTimeUpdated=this.getTimer(),this._onResume.dispatch(),this.animateOnce())},e.prototype.pause=function(){this._isRunning&&(this._isRunning=!1,this._onPause.dispatch(),window.cancelAnimationFrame(this._animationFrameHandle))},e.prototype.dispose=function(){this.pause(),this._onResume.removeAll(),this._onPause.removeAll(),this._onTick.removeAll()},Object.defineProperty(e.prototype,"currentTick",{get:function(){return this._currentTick},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"currentTimeSeconds",{get:function(){return this._currentTime/1e3},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"tickDeltaTimeSeconds",{get:function(){return this._tickDeltaTime/1e3},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"timeScale",{get:function(){return this._timeScale},set:function(e){this._timeScale!==e&&(this._timeScale=e)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"onResume",{get:function(){return this._onResume},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"onPause",{get:function(){return this._onPause},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"onTick",{get:function(){return this._onTick},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"onTickOncePerFrame",{get:function(){return this._onTickOncePerFrame},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"isRunning",{get:function(){return this._isRunning},enumerable:!0,configurable:!0}),e.prototype.animateOnce=function(){var e=this;this._animationFrameHandle=window.requestAnimationFrame(function(){return e.onFrame()})},e.prototype.onFrame=function(){if(this._now=this.getTimer(),this._frameDeltaTime=this._now-this._lastTimeUpdated,isNaN(this._minInterval)||this._frameDeltaTime>=this._minInterval)if(isNaN(this._maxInterval))this.update(this._frameDeltaTime*this._timeScale,!0),this._lastTimeUpdated=this._now;else for(this._interval=Math.min(this._frameDeltaTime,this._maxInterval);this._now>=this._lastTimeUpdated+this._interval;)this.update(this._interval*this._timeScale,this._now<=this._lastTimeUpdated+2*this._maxInterval),this._lastTimeUpdated+=this._interval;this._isRunning&&this.animateOnce()},e.prototype.update=function(e,t){void 0===t&&(t=!0),this._currentTick++,this._currentTime+=e,this._tickDeltaTime=e,this._onTick.dispatch(this.currentTimeSeconds,this.tickDeltaTimeSeconds,this.currentTick),t&&this._onTickOncePerFrame.dispatch(this.currentTimeSeconds,this.tickDeltaTimeSeconds,this.currentTick)},e.prototype.getTimer=function(){return Date.now()},e}();Object.defineProperty(t,"__esModule",{value:!0}),t.default=r},function(e,t,i){!function(t,i){e.exports=i()}(this,function(){return function(e){function t(n){if(i[n])return i[n].exports;var r=i[n]={exports:{},id:n,loaded:!1};return e[n].call(r.exports,r,r.exports,t),r.loaded=!0,r.exports}var i={};return t.m=e,t.c=i,t.p="",t(0)}([function(e,t){"use strict";var i=function(){function e(){this.functions=[]}return e.prototype.add=function(e){return this.functions.indexOf(e)===-1&&(this.functions.push(e),!0)},e.prototype.remove=function(e){var t=this.functions.indexOf(e);return t>-1&&(this.functions.splice(t,1),!0)},e.prototype.removeAll=function(){return this.functions.length>0&&(this.functions.length=0,!0)},e.prototype.dispatch=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var i=this.functions.concat();i.forEach(function(t){t.apply(void 0,e)})},Object.defineProperty(e.prototype,"numItems",{get:function(){return this.functions.length},enumerable:!0,configurable:!0}),e}();Object.defineProperty(t,"__esModule",{value:!0}),t.default=i}])})}])});