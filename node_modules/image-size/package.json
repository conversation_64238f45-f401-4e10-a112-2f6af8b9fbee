{"name": "image-size", "version": "0.7.5", "description": "get dimensions of any image file", "main": "lib/index.js", "files": ["bin", "lib"], "engines": {"node": ">=6.9.0"}, "bin": {"image-size": "bin/image-size.js"}, "scripts": {"pretest": "eslint lib specs", "test": "nyc mocha specs", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "repository": "image-size/image-size", "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg", "icns", "ico", "cur"], "author": "netroy <<EMAIL>> (http://netroy.in/)", "license": "MIT", "devDependencies": {"coveralls": "3.0.6", "eslint": "6.4.0", "expect.js": "0.3.1", "glob": "7.1.4", "mocha": "6.2.0", "nyc": "14.1.1", "sinon": "7.4.2"}}