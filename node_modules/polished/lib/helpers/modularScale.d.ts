import { ModularScaleRatio } from '../types/modularScaleRatio';

declare const ratioNames: {
  minorSecond: 1.067;
  majorSecond: 1.125;
  minorThird: 1.2;
  majorThird: 1.25;
  perfectFourth: 1.333;
  augFourth: 1.414;
  perfectFifth: 1.5;
  minorSixth: 1.6;
  goldenSection: 1.618;
  majorSixth: 1.667;
  minorSeventh: 1.778;
  majorSeventh: 1.875;
  octave: 2;
  majorTenth: 2.5;
  majorEleventh: 2.667;
  majorTwelfth: 3;
  doubleOctave: 4;
};
declare function modularScale(
  steps: number,
  base?: number | string,
  ratio?: ModularScaleRatio,
): string;

export { ratioNames };
export default modularScale;
