// @flow

// Note: we define properties with JSdoc since documentation.js doesn't recognize
// exported types yet. See https://github.com/documentationjs/documentation/issues/680

/**
 * @property {Array<string>}  colorStops
 * @property {?string}  toDirection
 * @property {?string}  fallback
 */
export type LinearGradientConfiguration = {
  colorStops: Array<string>,
  toDirection?: string,
  fallback?: string,
}
