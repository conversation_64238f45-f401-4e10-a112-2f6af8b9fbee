// @flow
import type { SideKeyword } from './sideKeyword'

// Note: we define properties with JSdoc since documentation.js doesn't recognize
// exported types yet. See https://github.com/documentationjs/documentation/issues/680

/**
 * @property {number}  backgroundColor
 * @property {number}  foregroundColor
 * @property {number}  height
 * @property {number}  height
 * @property {number}  height
 */
export type TriangleConfiguration = {
  backgroundColor?: string,
  foregroundColor: string,
  height: number | string,
  width: number | string,
  pointingDirection: SideKeyword,
}
