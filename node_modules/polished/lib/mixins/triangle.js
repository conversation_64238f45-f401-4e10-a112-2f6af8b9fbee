"use strict";

exports.__esModule = true;
exports["default"] = triangle;
var _getValueAndUnit = _interopRequireDefault(require("../helpers/getValueAndUnit"));
var _errors = _interopRequireDefault(require("../internalHelpers/_errors"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
var getBorderWidth = function getBorderWidth(pointingDirection, height, width) {
  var fullWidth = "" + width[0] + (width[1] || '');
  var halfWidth = "" + width[0] / 2 + (width[1] || '');
  var fullHeight = "" + height[0] + (height[1] || '');
  var halfHeight = "" + height[0] / 2 + (height[1] || '');
  switch (pointingDirection) {
    case 'top':
      return "0 " + halfWidth + " " + fullHeight + " " + halfWidth;
    case 'topLeft':
      return fullWidth + " " + fullHeight + " 0 0";
    case 'left':
      return halfHeight + " " + fullWidth + " " + halfHeight + " 0";
    case 'bottomLeft':
      return fullWidth + " 0 0 " + fullHeight;
    case 'bottom':
      return fullHeight + " " + halfWidth + " 0 " + halfWidth;
    case 'bottomRight':
      return "0 0 " + fullWidth + " " + fullHeight;
    case 'right':
      return halfHeight + " 0 " + halfHeight + " " + fullWidth;
    case 'topRight':
    default:
      return "0 " + fullWidth + " " + fullHeight + " 0";
  }
};
var getBorderColor = function getBorderColor(pointingDirection, foregroundColor) {
  switch (pointingDirection) {
    case 'top':
    case 'bottomRight':
      return {
        borderBottomColor: foregroundColor
      };
    case 'right':
    case 'bottomLeft':
      return {
        borderLeftColor: foregroundColor
      };
    case 'bottom':
    case 'topLeft':
      return {
        borderTopColor: foregroundColor
      };
    case 'left':
    case 'topRight':
      return {
        borderRightColor: foregroundColor
      };
    default:
      throw new _errors["default"](59);
  }
};

/**
 * CSS to represent triangle with any pointing direction with an optional background color.
 *
 * @example
 * // Styles as object usage
 *
 * const styles = {
 *   ...triangle({ pointingDirection: 'right', width: '100px', height: '100px', foregroundColor: 'red' })
 * }
 *
 *
 * // styled-components usage
 * const div = styled.div`
 *   ${triangle({ pointingDirection: 'right', width: '100px', height: '100px', foregroundColor: 'red' })}
 *
 *
 * // CSS as JS Output
 *
 * div: {
 *  'borderColor': 'transparent transparent transparent red',
 *  'borderStyle': 'solid',
 *  'borderWidth': '50px 0 50px 100px',
 *  'height': '0',
 *  'width': '0',
 * }
 */
function triangle(_ref) {
  var pointingDirection = _ref.pointingDirection,
    height = _ref.height,
    width = _ref.width,
    foregroundColor = _ref.foregroundColor,
    _ref$backgroundColor = _ref.backgroundColor,
    backgroundColor = _ref$backgroundColor === void 0 ? 'transparent' : _ref$backgroundColor;
  var widthAndUnit = (0, _getValueAndUnit["default"])(width);
  var heightAndUnit = (0, _getValueAndUnit["default"])(height);
  if (isNaN(heightAndUnit[0]) || isNaN(widthAndUnit[0])) {
    throw new _errors["default"](60);
  }
  return _extends({
    width: '0',
    height: '0',
    borderColor: backgroundColor
  }, getBorderColor(pointingDirection, foregroundColor), {
    borderStyle: 'solid',
    borderWidth: getBorderWidth(pointingDirection, heightAndUnit, widthAndUnit)
  });
}
module.exports = exports.default;