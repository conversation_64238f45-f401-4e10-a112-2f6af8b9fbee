<head>
  <style> body { margin: 0; } </style>

  <script src="//cdn.jsdelivr.net/npm/globe.gl"></script>
<!--  <script src="../../dist/globe.gl.js"></script>-->
</head>

<body>
  <div id="globeViz"></div>

  <script type="module">
    import { csvParse } from 'https://esm.sh/d3-dsv';

    const world = new Globe(document.getElementById('globeViz'))
      .globeImageUrl('//cdn.jsdelivr.net/npm/three-globe/example/img/earth-night.jpg')
      .heatmapPointLat('lat')
      .heatmapPointLng('lng')
      .heatmapPointWeight('pop')
      .heatmapBandwidth(0.9)
      .heatmapColorSaturation(2.8)
      .enablePointerInteraction(false);

    fetch('../datasets/world_population.csv').then(res => res.text())
      .then(csv => csvParse(csv, ({ lat, lng, pop }) => ({ lat: +lat, lng: +lng, pop: +pop })))
      .then(data => world.heatmapsData([data]));
  </script>
</body>