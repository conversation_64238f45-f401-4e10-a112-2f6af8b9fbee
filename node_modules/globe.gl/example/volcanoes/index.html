<head>
  <style> body { margin: 0; } </style>

  <script src="//cdn.jsdelivr.net/npm/globe.gl"></script>
  <!--<script src="../../dist/globe.gl.js"></script>-->
</head>

<body>
  <div id="globeViz"></div>

  <script type="module">
    import { scaleOrdinal } from 'https://esm.sh/d3-scale';
    import { schemeCategory10 } from 'https://esm.sh/d3-scale-chromatic';
    import { transparentize } from 'https://esm.sh/polished';

    const catColor = scaleOrdinal(schemeCategory10.map(col => transparentize(0.2, col)));

    const getAlt = d => d.elevation * 5e-5;

    const getTooltip = d => `
      <div style="text-align: center">
        <div><b>${d.name}</b>, ${d.country}</div>
        <div>(${d.type})</div>
        <div>Elevation: <em>${d.elevation}</em>m</div>
      </div>
    `;

    const myGlobe = new Globe(document.getElementById('globeViz'))
      .globeImageUrl('//cdn.jsdelivr.net/npm/three-globe/example/img/earth-night.jpg')
      .backgroundImageUrl('//cdn.jsdelivr.net/npm/three-globe/example/img/night-sky.png')
      .pointLat('lat')
      .pointLng('lon')
      .pointAltitude(getAlt)
      .pointRadius(0.12)
      .pointColor(d => catColor(d.type))
      .pointLabel(getTooltip)
      .labelLat('lat')
      .labelLng('lon')
      .labelAltitude(d => getAlt(d) + 1e-6)
      .labelDotRadius(0.12)
      .labelDotOrientation(() => 'bottom')
      .labelColor(d => catColor(d.type))
      .labelText('name')
      .labelSize(0.15)
      .labelResolution(1)
      .labelLabel(getTooltip);

    fetch('../datasets/world_volcanoes.json').then(res => res.json()).then(volcanoes => {
      myGlobe.pointsData(volcanoes)
        .labelsData(volcanoes);
    });

  </script>
</body>