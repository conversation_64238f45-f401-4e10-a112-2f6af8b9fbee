<head>
  <style> body { margin: 0; } </style>

  <script src="//cdn.jsdelivr.net/npm/globe.gl"></script>
<!--  <script src="../../dist/globe.gl.js"></script>-->
</head>

<body>
  <div id="globeViz"></div>

  <script>
    const shieldRing = { lat: 90, lng: 0 };

    const globe = new Globe(document.getElementById('globeViz'))
      .globeImageUrl('//cdn.jsdelivr.net/npm/three-globe/example/img/earth-night.jpg')
      .ringsData([shieldRing])
      .ringAltitude(0.25)
      .ringColor(() => 'lightblue')
      .ringMaxRadius(180)
      .ringPropagationSpeed(20)
      .ringRepeatPeriod(200);
  </script>
</body>