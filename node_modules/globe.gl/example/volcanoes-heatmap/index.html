<head>
  <style> body { margin: 0; } </style>

  <script src="//cdn.jsdelivr.net/npm/globe.gl"></script>
<!--  <script src="../../dist/globe.gl.js"></script>-->
</head>

<body>
  <div id="globeViz"></div>

  <script>
    const world = new Globe(document.getElementById('globeViz'))
      .globeImageUrl('//cdn.jsdelivr.net/npm/three-globe/example/img/earth-blue-marble.jpg')
      .heatmapPointLat('lat')
      .heatmapPointLng('lon')
      .heatmapPointWeight(d => d.elevation * 5e-5)
      .heatmapTopAltitude(0.2)
      .heatmapBandwidth(1.35)
      .heatmapColorSaturation(3.2)
      .enablePointerInteraction(false);

    fetch('../datasets/world_volcanoes.json').then(res => res.json()).then(volcanoes => {
      world.heatmapsData([volcanoes])
    });
  </script>
</body>