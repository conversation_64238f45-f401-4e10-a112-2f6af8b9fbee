<head>
  <style> body { margin: 0; } </style>

  <script src="//cdn.jsdelivr.net/npm/globe.gl"></script>
  <!--<script src="../../dist/globe.gl.js"></script>-->
</head>

<body>
<div id="globeViz"></div>

<script type="module">
  import { scaleOrdinal } from 'https://esm.sh/d3-scale';

  const colorScale = scaleOrdinal(['orangered', 'mediumblue', 'darkgreen', 'yellow']);

  const labelsTopOrientation = new Set(['Apollo 12', 'Luna 2', 'Luna 20', 'Luna 21', 'Luna 24', 'LCROSS Probe']); // avoid label collisions

  const moon = new Globe(document.getElementById('globeViz'))
    .globeImageUrl('./lunar_surface.jpg')
    .bumpImageUrl('./lunar_bumpmap.jpg')
    .backgroundImageUrl('//cdn.jsdelivr.net/npm/three-globe/example/img/night-sky.png')
    .showGraticules(true)
    .showAtmosphere(false) // moon has no atmosphere
    .labelText('label')
    .labelSize(1.7)
    .labelDotRadius(0.4)
    .labelDotOrientation(d => labelsTopOrientation.has(d.label) ? 'top' : 'bottom')
    .labelColor(d => colorScale(d.agency))
    .labelLabel(d => `
        <div><b>${d.label}</b></div>
        <div>${d.agency} - ${d.program} Program</div>
        <div>Landing on <i>${new Date(d.date).toLocaleDateString()}</i></div>
      `)
    .onLabelClick(d => window.open(d.url, '_blank'));

  fetch('./moon_landings.json').then(r => r.json()).then(landingSites => {
    moon.labelsData(landingSites);
  });
</script>
</body>