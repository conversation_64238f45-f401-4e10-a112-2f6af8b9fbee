{"name": "d3-geo-voronoi", "version": "2.1.0", "description": "Spherical Voronoi Diagram and Delaunay Triangulation", "homepage": "https://github.com/Fil/d3-geo-voronoi", "repository": {"type": "git", "url": "https://github.com/Fil/d3-geo-voronoi.git"}, "keywords": ["d3", "d3-module", "d3-geo", "d3-delaunay"], "license": "ISC", "author": {"name": "<PERSON>", "url": "https://github.com/Fil"}, "type": "module", "files": ["dist/**/*.js", "src/**/*.js"], "module": "src/index.js", "main": "src/index.js", "jsdelivr": "dist/d3-geo-voronoi.min.js", "unpkg": "dist/d3-geo-voronoi.min.js", "exports": {"umd": "./dist/d3-geo-voronoi.min.js", "default": "./src/index.js"}, "sideEffects": false, "dependencies": {"d3-array": "3", "d3-delaunay": "6", "d3-geo": "3", "d3-tricontour": "1"}, "devDependencies": {"eslint": "7", "mocha": "10", "package-preamble": "0.1", "rollup": "2", "rollup-plugin-terser": "7"}, "scripts": {"test": "mocha 'test/**/*-test.js' && eslint src test", "prepublishOnly": "rm -rf dist && yarn test && rollup -c"}, "engines": {"node": ">=12"}}