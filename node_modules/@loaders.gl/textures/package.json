{"name": "@loaders.gl/textures", "version": "3.4.15", "description": "Framework-independent loaders for compressed and super compressed (basis) textures ", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/visgl/loaders.gl"}, "keywords": ["webgl", "loader", "3d", "texture", "compressed texture", "gpu texture", "PVR", "ETC", "basis"], "types": "dist/index.d.ts", "main": "dist/es5/index.js", "module": "dist/esm/index.js", "sideEffects": false, "files": ["src", "dist", "bin", "README.md"], "scripts": {"pre-build": "npm run copy-libs && npm run build-worker && npm run build-bundle", "copy-libs": "cp -rf ./src/libs ./dist/libs", "build-bundle": "esbuild src/bundle.ts --bundle --outfile=dist/dist.min.js", "build-worker": "npm run build-basis-worker && npm run build-basis-nodejs-worker && npm run build-npy-worker && npm run build-compressed-texture-worker && npm run build-crunch-worker && npm run build-ktx2-basis-writer-worker && npm run build-ktx2-basis-writer-nodejs-worker", "build-basis-worker": "esbuild src/workers/basis-worker.ts --bundle --outfile=dist/basis-worker.js --define:__VERSION__=\\\"$npm_package_version\\\"", "build-basis-nodejs-worker": "esbuild src/workers/basis-nodejs-worker.ts --platform=node --target=esnext,node12 --bundle --outfile=dist/basis-nodejs-worker.js --define:__VERSION__=\\\"$npm_package_version\\\"", "build-npy-worker": "esbuild src/workers/npy-worker.ts --bundle --outfile=dist/npy-worker.js --define:__VERSION__=\\\"$npm_package_version\\\"", "build-compressed-texture-worker": "esbuild src/workers/compressed-texture-worker.ts --bundle --outfile=dist/compressed-texture-worker.js --define:__VERSION__=\\\"$npm_package_version\\\"", "build-crunch-worker": "esbuild src/workers/crunch-worker.ts --bundle --outfile=dist/crunch-worker.js --define:__VERSION__=\\\"$npm_package_version\\\"", "build-ktx2-basis-writer-worker": "esbuild src/workers/ktx2-basis-writer-worker.ts --bundle --outfile=dist/ktx2-basis-writer-worker.js --define:__VERSION__=\\\"$npm_package_version\\\"", "build-ktx2-basis-writer-nodejs-worker": "esbuild src/workers/ktx2-basis-writer-nodejs-worker.ts --platform=node --target=esnext,node12 --bundle --outfile=dist/ktx2-basis-writer-nodejs-worker.js --define:__VERSION__=\\\"$npm_package_version\\\""}, "dependencies": {"@loaders.gl/images": "3.4.15", "@loaders.gl/loader-utils": "3.4.15", "@loaders.gl/schema": "3.4.15", "@loaders.gl/worker-utils": "3.4.15", "ktx-parse": "^0.0.4", "texture-compressor": "^1.0.2"}, "devDependencies": {"@loaders.gl/polyfills": "3.4.15"}, "gitHead": "19e941d5805568e449ef9092490d6568a4853298"}