(() => {
  var __defProp = Object.defineProperty;
  var __markAsModule = (target) => __defProp(target, "__esModule", { value: true });
  var __export = (target, all) => {
    __markAsModule(target);
    for (var name in all)
      __defProp(target, name, { get: all[name], enumerable: true });
  };

  // ../loader-utils/src/lib/env-utils/assert.ts
  function assert(condition, message) {
    if (!condition) {
      throw new Error(message || "loader assertion failed.");
    }
  }

  // ../worker-utils/src/lib/env-utils/version.ts
  var VERSION = true ? "3.4.15" : DEFAULT_VERSION;
  if (false) {
    console.error("loaders.gl: The __VERSION__ variable is not injected using babel plugin. Latest unstable workers would be fetched from the CDN.");
  }

  // ../worker-utils/src/lib/env-utils/assert.ts
  function assert2(condition, message) {
    if (!condition) {
      throw new Error(message || "loaders.gl assertion failed.");
    }
  }

  // ../worker-utils/src/lib/env-utils/globals.ts
  var globals = {
    self: typeof self !== "undefined" && self,
    window: typeof window !== "undefined" && window,
    global: typeof global !== "undefined" && global,
    document: typeof document !== "undefined" && document
  };
  var self_ = globals.self || globals.window || globals.global || {};
  var window_ = globals.window || globals.self || globals.global || {};
  var global_ = globals.global || globals.self || globals.window || {};
  var document_ = globals.document || {};
  var isBrowser = typeof process !== "object" || String(process) !== "[object process]" || process.browser;
  var isWorker = typeof importScripts === "function";
  var isMobile = typeof window !== "undefined" && typeof window.orientation !== "undefined";
  var matches = typeof process !== "undefined" && process.version && /v([0-9]*)/.exec(process.version);
  var nodeVersion = matches && parseFloat(matches[1]) || 0;

  // ../worker-utils/src/lib/worker-utils/get-transfer-list.ts
  function getTransferList(object, recursive = true, transfers) {
    const transfersSet = transfers || new Set();
    if (!object) {
    } else if (isTransferable(object)) {
      transfersSet.add(object);
    } else if (isTransferable(object.buffer)) {
      transfersSet.add(object.buffer);
    } else if (ArrayBuffer.isView(object)) {
    } else if (recursive && typeof object === "object") {
      for (const key in object) {
        getTransferList(object[key], recursive, transfersSet);
      }
    }
    return transfers === void 0 ? Array.from(transfersSet) : [];
  }
  function isTransferable(object) {
    if (!object) {
      return false;
    }
    if (object instanceof ArrayBuffer) {
      return true;
    }
    if (typeof MessagePort !== "undefined" && object instanceof MessagePort) {
      return true;
    }
    if (typeof ImageBitmap !== "undefined" && object instanceof ImageBitmap) {
      return true;
    }
    if (typeof OffscreenCanvas !== "undefined" && object instanceof OffscreenCanvas) {
      return true;
    }
    return false;
  }

  // ../worker-utils/src/lib/worker-farm/worker-body.ts
  function getParentPort() {
    let parentPort;
    try {
      eval("globalThis.parentPort = require('worker_threads').parentPort");
      parentPort = globalThis.parentPort;
    } catch {
    }
    return parentPort;
  }
  var onMessageWrapperMap = new Map();
  var WorkerBody = class {
    static inWorkerThread() {
      return typeof self !== "undefined" || Boolean(getParentPort());
    }
    static set onmessage(onMessage) {
      function handleMessage(message) {
        const parentPort3 = getParentPort();
        const { type, payload } = parentPort3 ? message : message.data;
        onMessage(type, payload);
      }
      const parentPort2 = getParentPort();
      if (parentPort2) {
        parentPort2.on("message", handleMessage);
        parentPort2.on("exit", () => console.debug("Node worker closing"));
      } else {
        globalThis.onmessage = handleMessage;
      }
    }
    static addEventListener(onMessage) {
      let onMessageWrapper = onMessageWrapperMap.get(onMessage);
      if (!onMessageWrapper) {
        onMessageWrapper = (message) => {
          if (!isKnownMessage(message)) {
            return;
          }
          const parentPort3 = getParentPort();
          const { type, payload } = parentPort3 ? message : message.data;
          onMessage(type, payload);
        };
      }
      const parentPort2 = getParentPort();
      if (parentPort2) {
        console.error("not implemented");
      } else {
        globalThis.addEventListener("message", onMessageWrapper);
      }
    }
    static removeEventListener(onMessage) {
      const onMessageWrapper = onMessageWrapperMap.get(onMessage);
      onMessageWrapperMap.delete(onMessage);
      const parentPort2 = getParentPort();
      if (parentPort2) {
        console.error("not implemented");
      } else {
        globalThis.removeEventListener("message", onMessageWrapper);
      }
    }
    static postMessage(type, payload) {
      const data = { source: "loaders.gl", type, payload };
      const transferList = getTransferList(payload);
      const parentPort2 = getParentPort();
      if (parentPort2) {
        parentPort2.postMessage(data, transferList);
      } else {
        globalThis.postMessage(data, transferList);
      }
    }
  };
  function isKnownMessage(message) {
    const { type, data } = message;
    return type === "message" && data && typeof data.source === "string" && data.source.startsWith("loaders.gl");
  }

  // ../worker-utils/src/lib/node/require-utils.browser.ts
  var require_utils_browser_exports = {};
  __export(require_utils_browser_exports, {
    readFileAsArrayBuffer: () => readFileAsArrayBuffer,
    readFileAsText: () => readFileAsText,
    requireFromFile: () => requireFromFile,
    requireFromString: () => requireFromString
  });
  var readFileAsArrayBuffer = null;
  var readFileAsText = null;
  var requireFromFile = null;
  var requireFromString = null;

  // ../worker-utils/src/lib/library-utils/library-utils.ts
  var LATEST = "latest";
  var VERSION2 = typeof VERSION !== "undefined" ? VERSION : LATEST;
  var loadLibraryPromises = {};
  async function loadLibrary(libraryUrl, moduleName = null, options = {}) {
    if (moduleName) {
      libraryUrl = getLibraryUrl(libraryUrl, moduleName, options);
    }
    loadLibraryPromises[libraryUrl] = loadLibraryPromises[libraryUrl] || loadLibraryFromFile(libraryUrl);
    return await loadLibraryPromises[libraryUrl];
  }
  function getLibraryUrl(library, moduleName, options) {
    if (library.startsWith("http")) {
      return library;
    }
    const modules = options.modules || {};
    if (modules[library]) {
      return modules[library];
    }
    if (!isBrowser) {
      return `modules/${moduleName}/dist/libs/${library}`;
    }
    if (options.CDN) {
      assert2(options.CDN.startsWith("http"));
      return `${options.CDN}/${moduleName}@${VERSION2}/dist/libs/${library}`;
    }
    if (isWorker) {
      return `../src/libs/${library}`;
    }
    return `modules/${moduleName}/src/libs/${library}`;
  }
  async function loadLibraryFromFile(libraryUrl) {
    if (libraryUrl.endsWith("wasm")) {
      const response2 = await fetch(libraryUrl);
      return await response2.arrayBuffer();
    }
    if (!isBrowser) {
      try {
        return require_utils_browser_exports && requireFromFile && await requireFromFile(libraryUrl);
      } catch {
        return null;
      }
    }
    if (isWorker) {
      return importScripts(libraryUrl);
    }
    const response = await fetch(libraryUrl);
    const scriptSource = await response.text();
    return loadLibraryFromString(scriptSource, libraryUrl);
  }
  function loadLibraryFromString(scriptSource, id) {
    if (!isBrowser) {
      return requireFromString && requireFromString(scriptSource, id);
    }
    if (isWorker) {
      eval.call(global_, scriptSource);
      return null;
    }
    const script = document.createElement("script");
    script.id = id;
    try {
      script.appendChild(document.createTextNode(scriptSource));
    } catch (e2) {
      script.text = scriptSource;
    }
    document.body.appendChild(script);
    return null;
  }

  // ../loader-utils/src/lib/worker-loader-utils/create-loader-worker.ts
  var requestId = 0;
  function createLoaderWorker(loader) {
    if (!WorkerBody.inWorkerThread()) {
      return;
    }
    WorkerBody.onmessage = async (type, payload) => {
      switch (type) {
        case "process":
          try {
            const { input, options = {}, context = {} } = payload;
            const result = await parseData({
              loader,
              arrayBuffer: input,
              options,
              context: {
                ...context,
                parse: parseOnMainThread
              }
            });
            WorkerBody.postMessage("done", { result });
          } catch (error) {
            const message = error instanceof Error ? error.message : "";
            WorkerBody.postMessage("error", { error: message });
          }
          break;
        default:
      }
    };
  }
  function parseOnMainThread(arrayBuffer, options) {
    return new Promise((resolve, reject) => {
      const id = requestId++;
      const onMessage = (type, payload2) => {
        if (payload2.id !== id) {
          return;
        }
        switch (type) {
          case "done":
            WorkerBody.removeEventListener(onMessage);
            resolve(payload2.result);
            break;
          case "error":
            WorkerBody.removeEventListener(onMessage);
            reject(payload2.error);
            break;
          default:
        }
      };
      WorkerBody.addEventListener(onMessage);
      const payload = { id, input: arrayBuffer, options };
      WorkerBody.postMessage("process", payload);
    });
  }
  async function parseData({ loader, arrayBuffer, options, context }) {
    let data;
    let parser;
    if (loader.parseSync || loader.parse) {
      data = arrayBuffer;
      parser = loader.parseSync || loader.parse;
    } else if (loader.parseTextSync) {
      const textDecoder = new TextDecoder();
      data = textDecoder.decode(arrayBuffer);
      parser = loader.parseTextSync;
    } else {
      throw new Error(`Could not load data with ${loader.name} loader`);
    }
    options = {
      ...options,
      modules: loader && loader.options && loader.options.modules || {},
      worker: false
    };
    return await parser(data, { ...options }, context, loader);
  }

  // src/lib/utils/version.ts
  var VERSION3 = true ? "3.4.15" : "latest";

  // ../../node_modules/ktx-parse/dist/ktx-parse.modern.js
  var t = new Uint8Array([0]);
  var e = [171, 75, 84, 88, 32, 50, 48, 187, 13, 10, 26, 10];
  var n;
  var i;
  var s;
  var a;
  var r;
  var o;
  var l;
  var f;
  !function(t2) {
    t2[t2.NONE = 0] = "NONE", t2[t2.BASISLZ = 1] = "BASISLZ", t2[t2.ZSTD = 2] = "ZSTD", t2[t2.ZLIB = 3] = "ZLIB";
  }(n || (n = {})), function(t2) {
    t2[t2.BASICFORMAT = 0] = "BASICFORMAT";
  }(i || (i = {})), function(t2) {
    t2[t2.UNSPECIFIED = 0] = "UNSPECIFIED", t2[t2.ETC1S = 163] = "ETC1S", t2[t2.UASTC = 166] = "UASTC";
  }(s || (s = {})), function(t2) {
    t2[t2.UNSPECIFIED = 0] = "UNSPECIFIED", t2[t2.SRGB = 1] = "SRGB";
  }(a || (a = {})), function(t2) {
    t2[t2.UNSPECIFIED = 0] = "UNSPECIFIED", t2[t2.LINEAR = 1] = "LINEAR", t2[t2.SRGB = 2] = "SRGB", t2[t2.ITU = 3] = "ITU", t2[t2.NTSC = 4] = "NTSC", t2[t2.SLOG = 5] = "SLOG", t2[t2.SLOG2 = 6] = "SLOG2";
  }(r || (r = {})), function(t2) {
    t2[t2.ALPHA_STRAIGHT = 0] = "ALPHA_STRAIGHT", t2[t2.ALPHA_PREMULTIPLIED = 1] = "ALPHA_PREMULTIPLIED";
  }(o || (o = {})), function(t2) {
    t2[t2.RGB = 0] = "RGB", t2[t2.RRR = 3] = "RRR", t2[t2.GGG = 4] = "GGG", t2[t2.AAA = 15] = "AAA";
  }(l || (l = {})), function(t2) {
    t2[t2.RGB = 0] = "RGB", t2[t2.RGBA = 3] = "RGBA", t2[t2.RRR = 4] = "RRR", t2[t2.RRRG = 5] = "RRRG";
  }(f || (f = {}));
  var U = class {
    constructor() {
      this.vkFormat = 0, this.typeSize = 1, this.pixelWidth = 0, this.pixelHeight = 0, this.pixelDepth = 0, this.layerCount = 0, this.faceCount = 1, this.supercompressionScheme = n.NONE, this.levels = [], this.dataFormatDescriptor = [{ vendorId: 0, descriptorType: i.BASICFORMAT, versionNumber: 2, descriptorBlockSize: 40, colorModel: s.UNSPECIFIED, colorPrimaries: a.SRGB, transferFunction: a.SRGB, flags: o.ALPHA_STRAIGHT, texelBlockDimension: { x: 4, y: 4, z: 1, w: 1 }, bytesPlane: [], samples: [] }], this.keyValue = {}, this.globalData = null;
    }
  };
  var c = class {
    constructor(t2, e2, n2, i2) {
      this._dataView = new DataView(t2.buffer, t2.byteOffset + e2, n2), this._littleEndian = i2, this._offset = 0;
    }
    _nextUint8() {
      const t2 = this._dataView.getUint8(this._offset);
      return this._offset += 1, t2;
    }
    _nextUint16() {
      const t2 = this._dataView.getUint16(this._offset, this._littleEndian);
      return this._offset += 2, t2;
    }
    _nextUint32() {
      const t2 = this._dataView.getUint32(this._offset, this._littleEndian);
      return this._offset += 4, t2;
    }
    _nextUint64() {
      const t2 = this._dataView.getUint32(this._offset, this._littleEndian) + 2 ** 32 * this._dataView.getUint32(this._offset + 4, this._littleEndian);
      return this._offset += 8, t2;
    }
    _skip(t2) {
      return this._offset += t2, this;
    }
    _scan(t2, e2 = 0) {
      const n2 = this._offset;
      let i2 = 0;
      for (; this._dataView.getUint8(this._offset) !== e2 && i2 < t2; )
        i2++, this._offset++;
      return i2 < t2 && this._offset++, new Uint8Array(this._dataView.buffer, this._dataView.byteOffset + n2, i2);
    }
  };
  function _(t2) {
    return typeof TextDecoder != "undefined" ? new TextDecoder().decode(t2) : Buffer.from(t2).toString("utf8");
  }
  function p(t2) {
    const n2 = new Uint8Array(t2.buffer, t2.byteOffset, e.length);
    if (n2[0] !== e[0] || n2[1] !== e[1] || n2[2] !== e[2] || n2[3] !== e[3] || n2[4] !== e[4] || n2[5] !== e[5] || n2[6] !== e[6] || n2[7] !== e[7] || n2[8] !== e[8] || n2[9] !== e[9] || n2[10] !== e[10] || n2[11] !== e[11])
      throw new Error("Missing KTX 2.0 identifier.");
    const i2 = new U(), s2 = 17 * Uint32Array.BYTES_PER_ELEMENT, a2 = new c(t2, e.length, s2, true);
    i2.vkFormat = a2._nextUint32(), i2.typeSize = a2._nextUint32(), i2.pixelWidth = a2._nextUint32(), i2.pixelHeight = a2._nextUint32(), i2.pixelDepth = a2._nextUint32(), i2.layerCount = a2._nextUint32(), i2.faceCount = a2._nextUint32();
    const r2 = a2._nextUint32();
    i2.supercompressionScheme = a2._nextUint32();
    const o2 = a2._nextUint32(), l2 = a2._nextUint32(), f2 = a2._nextUint32(), h = a2._nextUint32(), g = a2._nextUint64(), p2 = a2._nextUint64(), x = new c(t2, e.length + s2, 3 * r2 * 8, true);
    for (let e2 = 0; e2 < r2; e2++)
      i2.levels.push({ levelData: new Uint8Array(t2.buffer, t2.byteOffset + x._nextUint64(), x._nextUint64()), uncompressedByteLength: x._nextUint64() });
    const u = new c(t2, o2, l2, true), y = { vendorId: u._skip(4)._nextUint16(), descriptorType: u._nextUint16(), versionNumber: u._nextUint16(), descriptorBlockSize: u._nextUint16(), colorModel: u._nextUint8(), colorPrimaries: u._nextUint8(), transferFunction: u._nextUint8(), flags: u._nextUint8(), texelBlockDimension: { x: u._nextUint8() + 1, y: u._nextUint8() + 1, z: u._nextUint8() + 1, w: u._nextUint8() + 1 }, bytesPlane: [u._nextUint8(), u._nextUint8(), u._nextUint8(), u._nextUint8(), u._nextUint8(), u._nextUint8(), u._nextUint8(), u._nextUint8()], samples: [] }, D = (y.descriptorBlockSize / 4 - 6) / 4;
    for (let t3 = 0; t3 < D; t3++)
      y.samples[t3] = { bitOffset: u._nextUint16(), bitLength: u._nextUint8(), channelID: u._nextUint8(), samplePosition: [u._nextUint8(), u._nextUint8(), u._nextUint8(), u._nextUint8()], sampleLower: u._nextUint32(), sampleUpper: u._nextUint32() };
    i2.dataFormatDescriptor.length = 0, i2.dataFormatDescriptor.push(y);
    const b = new c(t2, f2, h, true);
    for (; b._offset < h; ) {
      const t3 = b._nextUint32(), e2 = b._scan(t3), n3 = _(e2), s3 = b._scan(t3 - e2.byteLength);
      i2.keyValue[n3] = n3.match(/^ktx/i) ? _(s3) : s3, t3 % 4 && b._skip(4 - t3 % 4);
    }
    if (p2 <= 0)
      return i2;
    const d = new c(t2, g, p2, true), B = d._nextUint16(), w = d._nextUint16(), A = d._nextUint32(), S = d._nextUint32(), m = d._nextUint32(), L = d._nextUint32(), I = [];
    for (let t3 = 0; t3 < r2; t3++)
      I.push({ imageFlags: d._nextUint32(), rgbSliceByteOffset: d._nextUint32(), rgbSliceByteLength: d._nextUint32(), alphaSliceByteOffset: d._nextUint32(), alphaSliceByteLength: d._nextUint32() });
    const R = g + d._offset, E = R + A, T = E + S, O = T + m, P = new Uint8Array(t2.buffer, t2.byteOffset + R, A), C = new Uint8Array(t2.buffer, t2.byteOffset + E, S), F = new Uint8Array(t2.buffer, t2.byteOffset + T, m), G = new Uint8Array(t2.buffer, t2.byteOffset + O, L);
    return i2.globalData = { endpointCount: B, selectorCount: w, imageDescs: I, endpointsData: P, selectorsData: C, tablesData: F, extendedData: G }, i2;
  }

  // src/lib/utils/extract-mipmap-images.ts
  function extractMipmapImages(data, options) {
    const images = new Array(options.mipMapLevels);
    let levelWidth = options.width;
    let levelHeight = options.height;
    let offset = 0;
    for (let i2 = 0; i2 < options.mipMapLevels; ++i2) {
      const levelSize = getLevelSize(options, levelWidth, levelHeight, data, i2);
      const levelData = getLevelData(data, i2, offset, levelSize);
      images[i2] = {
        compressed: true,
        format: options.internalFormat,
        data: levelData,
        width: levelWidth,
        height: levelHeight,
        levelSize
      };
      levelWidth = Math.max(1, levelWidth >> 1);
      levelHeight = Math.max(1, levelHeight >> 1);
      offset += levelSize;
    }
    return images;
  }
  function getLevelData(data, index, offset, levelSize) {
    if (!Array.isArray(data)) {
      return new Uint8Array(data.buffer, data.byteOffset + offset, levelSize);
    }
    return data[index].levelData;
  }
  function getLevelSize(options, levelWidth, levelHeight, data, index) {
    if (!Array.isArray(data)) {
      return options.sizeFunction(levelWidth, levelHeight);
    }
    return options.sizeFunction(data[index]);
  }

  // src/lib/gl-extensions.ts
  var GL_EXTENSIONS_CONSTANTS = {
    COMPRESSED_RGB_S3TC_DXT1_EXT: 33776,
    COMPRESSED_RGBA_S3TC_DXT1_EXT: 33777,
    COMPRESSED_RGBA_S3TC_DXT3_EXT: 33778,
    COMPRESSED_RGBA_S3TC_DXT5_EXT: 33779,
    COMPRESSED_R11_EAC: 37488,
    COMPRESSED_SIGNED_R11_EAC: 37489,
    COMPRESSED_RG11_EAC: 37490,
    COMPRESSED_SIGNED_RG11_EAC: 37491,
    COMPRESSED_RGB8_ETC2: 37492,
    COMPRESSED_RGBA8_ETC2_EAC: 37493,
    COMPRESSED_SRGB8_ETC2: 37494,
    COMPRESSED_SRGB8_ALPHA8_ETC2_EAC: 37495,
    COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2: 37496,
    COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2: 37497,
    COMPRESSED_RGB_PVRTC_4BPPV1_IMG: 35840,
    COMPRESSED_RGBA_PVRTC_4BPPV1_IMG: 35842,
    COMPRESSED_RGB_PVRTC_2BPPV1_IMG: 35841,
    COMPRESSED_RGBA_PVRTC_2BPPV1_IMG: 35843,
    COMPRESSED_RGB_ETC1_WEBGL: 36196,
    COMPRESSED_RGB_ATC_WEBGL: 35986,
    COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL: 35987,
    COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL: 34798,
    COMPRESSED_RGBA_ASTC_4X4_KHR: 37808,
    COMPRESSED_RGBA_ASTC_5X4_KHR: 37809,
    COMPRESSED_RGBA_ASTC_5X5_KHR: 37810,
    COMPRESSED_RGBA_ASTC_6X5_KHR: 37811,
    COMPRESSED_RGBA_ASTC_6X6_KHR: 37812,
    COMPRESSED_RGBA_ASTC_8X5_KHR: 37813,
    COMPRESSED_RGBA_ASTC_8X6_KHR: 37814,
    COMPRESSED_RGBA_ASTC_8X8_KHR: 37815,
    COMPRESSED_RGBA_ASTC_10X5_KHR: 37816,
    COMPRESSED_RGBA_ASTC_10X6_KHR: 37817,
    COMPRESSED_RGBA_ASTC_10X8_KHR: 37818,
    COMPRESSED_RGBA_ASTC_10X10_KHR: 37819,
    COMPRESSED_RGBA_ASTC_12X10_KHR: 37820,
    COMPRESSED_RGBA_ASTC_12X12_KHR: 37821,
    COMPRESSED_SRGB8_ALPHA8_ASTC_4X4_KHR: 37840,
    COMPRESSED_SRGB8_ALPHA8_ASTC_5X4_KHR: 37841,
    COMPRESSED_SRGB8_ALPHA8_ASTC_5X5_KHR: 37842,
    COMPRESSED_SRGB8_ALPHA8_ASTC_6X5_KHR: 37843,
    COMPRESSED_SRGB8_ALPHA8_ASTC_6X6_KHR: 37844,
    COMPRESSED_SRGB8_ALPHA8_ASTC_8X5_KHR: 37845,
    COMPRESSED_SRGB8_ALPHA8_ASTC_8X6_KHR: 37846,
    COMPRESSED_SRGB8_ALPHA8_ASTC_8X8_KHR: 37847,
    COMPRESSED_SRGB8_ALPHA8_ASTC_10X5_KHR: 37848,
    COMPRESSED_SRGB8_ALPHA8_ASTC_10X6_KHR: 37849,
    COMPRESSED_SRGB8_ALPHA8_ASTC_10X8_KHR: 37850,
    COMPRESSED_SRGB8_ALPHA8_ASTC_10X10_KHR: 37851,
    COMPRESSED_SRGB8_ALPHA8_ASTC_12X10_KHR: 37852,
    COMPRESSED_SRGB8_ALPHA8_ASTC_12X12_KHR: 37853,
    COMPRESSED_RED_RGTC1_EXT: 36283,
    COMPRESSED_SIGNED_RED_RGTC1_EXT: 36284,
    COMPRESSED_RED_GREEN_RGTC2_EXT: 36285,
    COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT: 36286,
    COMPRESSED_SRGB_S3TC_DXT1_EXT: 35916,
    COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT: 35917,
    COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT: 35918,
    COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT: 35919
  };

  // src/lib/utils/ktx-format-helper.ts
  var VULKAN_TO_WEBGL_FORMAT_MAP = {
    131: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_S3TC_DXT1_EXT,
    132: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB_S3TC_DXT1_EXT,
    133: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT1_EXT,
    134: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT,
    135: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT3_EXT,
    136: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT,
    137: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT5_EXT,
    138: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT,
    139: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RED_RGTC1_EXT,
    140: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SIGNED_RED_RGTC1_EXT,
    141: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RED_GREEN_RGTC2_EXT,
    142: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT,
    147: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB8_ETC2,
    148: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ETC2,
    149: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2,
    150: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2,
    151: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA8_ETC2_EAC,
    152: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC,
    153: GL_EXTENSIONS_CONSTANTS.COMPRESSED_R11_EAC,
    154: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SIGNED_R11_EAC,
    155: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RG11_EAC,
    156: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SIGNED_RG11_EAC,
    157: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_4x4_KHR,
    158: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR,
    159: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_5x4_KHR,
    160: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_5X4_KHR,
    161: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_5x5_KHR,
    162: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_5x5_KHR,
    163: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_6x5_KHR,
    164: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_6x5_KHR,
    165: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_6x6_KHR,
    166: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_6x6_KHR,
    167: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8x5_KHR,
    168: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_8x5_KHR,
    169: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8x6_KHR,
    170: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_8x6_KHR,
    171: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8x8_KHR,
    172: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_8x8_KHR,
    173: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x5_KHR,
    174: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10x5_KHR,
    175: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x6_KHR,
    176: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10x6_KHR,
    177: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x8_KHR,
    178: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10x8_KHR,
    179: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x10_KHR,
    180: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10x10_KHR,
    181: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_12x10_KHR,
    182: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_12x10_KHR,
    183: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_12x12_KHR,
    184: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_12x12_KHR,
    1000054e3: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG,
    1000054001: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG,
    1000066e3: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_4x4_KHR,
    1000066001: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_5x4_KHR,
    1000066002: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_5x5_KHR,
    1000066003: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_6x5_KHR,
    1000066004: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_6x6_KHR,
    1000066005: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8x5_KHR,
    1000066006: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8x6_KHR,
    1000066007: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8x8_KHR,
    1000066008: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x5_KHR,
    1000066009: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x6_KHR,
    1000066010: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x8_KHR,
    1000066011: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x10_KHR,
    1000066012: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_12x10_KHR,
    1000066013: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_12x12_KHR
  };
  function mapVkFormatToWebGL(vkFormat) {
    return VULKAN_TO_WEBGL_FORMAT_MAP[vkFormat];
  }

  // src/lib/parsers/parse-ktx.ts
  var KTX2_ID = [
    171,
    75,
    84,
    88,
    32,
    50,
    48,
    187,
    13,
    10,
    26,
    10
  ];
  function isKTX(data) {
    const id = new Uint8Array(data);
    const notKTX = id.byteLength < KTX2_ID.length || id[0] !== KTX2_ID[0] || id[1] !== KTX2_ID[1] || id[2] !== KTX2_ID[2] || id[3] !== KTX2_ID[3] || id[4] !== KTX2_ID[4] || id[5] !== KTX2_ID[5] || id[6] !== KTX2_ID[6] || id[7] !== KTX2_ID[7] || id[8] !== KTX2_ID[8] || id[9] !== KTX2_ID[9] || id[10] !== KTX2_ID[10] || id[11] !== KTX2_ID[11];
    return !notKTX;
  }
  function parseKTX(arrayBuffer) {
    const uint8Array = new Uint8Array(arrayBuffer);
    const ktx = p(uint8Array);
    const mipMapLevels = Math.max(1, ktx.levels.length);
    const width = ktx.pixelWidth;
    const height = ktx.pixelHeight;
    const internalFormat = mapVkFormatToWebGL(ktx.vkFormat);
    return extractMipmapImages(ktx.levels, {
      mipMapLevels,
      width,
      height,
      sizeFunction: (level) => level.uncompressedByteLength,
      internalFormat
    });
  }

  // src/lib/parsers/parse-dds.ts
  var DDS_CONSTANTS = {
    MAGIC_NUMBER: 542327876,
    HEADER_LENGTH: 31,
    MAGIC_NUMBER_INDEX: 0,
    HEADER_SIZE_INDEX: 1,
    HEADER_FLAGS_INDEX: 2,
    HEADER_HEIGHT_INDEX: 3,
    HEADER_WIDTH_INDEX: 4,
    MIPMAPCOUNT_INDEX: 7,
    HEADER_PF_FLAGS_INDEX: 20,
    HEADER_PF_FOURCC_INDEX: 21,
    DDSD_MIPMAPCOUNT: 131072,
    DDPF_FOURCC: 4
  };
  var DDS_PIXEL_FORMATS = {
    DXT1: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_S3TC_DXT1_EXT,
    DXT3: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT3_EXT,
    DXT5: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT5_EXT,
    "ATC ": GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_ATC_WEBGL,
    ATCA: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL,
    ATCI: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL
  };
  var getATCLevelSize = getDxt1LevelSize;
  var getATCALevelSize = getDxtXLevelSize;
  var getATCILevelSize = getDxtXLevelSize;
  var DDS_SIZE_FUNCTIONS = {
    DXT1: getDxt1LevelSize,
    DXT3: getDxtXLevelSize,
    DXT5: getDxtXLevelSize,
    "ATC ": getATCLevelSize,
    ATCA: getATCALevelSize,
    ATCI: getATCILevelSize
  };
  function isDDS(data) {
    const header = new Uint32Array(data, 0, DDS_CONSTANTS.HEADER_LENGTH);
    const magic = header[DDS_CONSTANTS.MAGIC_NUMBER_INDEX];
    return magic === DDS_CONSTANTS.MAGIC_NUMBER;
  }
  function parseDDS(data) {
    const header = new Int32Array(data, 0, DDS_CONSTANTS.HEADER_LENGTH);
    const pixelFormatNumber = header[DDS_CONSTANTS.HEADER_PF_FOURCC_INDEX];
    assert(Boolean(header[DDS_CONSTANTS.HEADER_PF_FLAGS_INDEX] & DDS_CONSTANTS.DDPF_FOURCC), "DDS: Unsupported format, must contain a FourCC code");
    const fourCC = int32ToFourCC(pixelFormatNumber);
    const internalFormat = DDS_PIXEL_FORMATS[fourCC];
    const sizeFunction = DDS_SIZE_FUNCTIONS[fourCC];
    assert(internalFormat && sizeFunction, `DDS: Unknown pixel format ${pixelFormatNumber}`);
    let mipMapLevels = 1;
    if (header[DDS_CONSTANTS.HEADER_FLAGS_INDEX] & DDS_CONSTANTS.DDSD_MIPMAPCOUNT) {
      mipMapLevels = Math.max(1, header[DDS_CONSTANTS.MIPMAPCOUNT_INDEX]);
    }
    const width = header[DDS_CONSTANTS.HEADER_WIDTH_INDEX];
    const height = header[DDS_CONSTANTS.HEADER_HEIGHT_INDEX];
    const dataOffset = header[DDS_CONSTANTS.HEADER_SIZE_INDEX] + 4;
    const image = new Uint8Array(data, dataOffset);
    return extractMipmapImages(image, {
      mipMapLevels,
      width,
      height,
      sizeFunction,
      internalFormat
    });
  }
  function getDxt1LevelSize(width, height) {
    return (width + 3 >> 2) * (height + 3 >> 2) * 8;
  }
  function getDxtXLevelSize(width, height) {
    return (width + 3 >> 2) * (height + 3 >> 2) * 16;
  }
  function int32ToFourCC(value) {
    return String.fromCharCode(value & 255, value >> 8 & 255, value >> 16 & 255, value >> 24 & 255);
  }

  // src/lib/parsers/parse-pvr.ts
  var PVR_CONSTANTS = {
    MAGIC_NUMBER: 55727696,
    MAGIC_NUMBER_EXTRA: 1347834371,
    HEADER_LENGTH: 13,
    HEADER_SIZE: 52,
    MAGIC_NUMBER_INDEX: 0,
    PIXEL_FORMAT_INDEX: 2,
    COLOUR_SPACE_INDEX: 4,
    HEIGHT_INDEX: 6,
    WIDTH_INDEX: 7,
    MIPMAPCOUNT_INDEX: 11,
    METADATA_SIZE_INDEX: 12
  };
  var PVR_PIXEL_FORMATS = {
    0: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_PVRTC_2BPPV1_IMG],
    1: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG],
    2: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_PVRTC_4BPPV1_IMG],
    3: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG],
    6: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_ETC1_WEBGL],
    7: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_S3TC_DXT1_EXT],
    9: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT3_EXT],
    11: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT5_EXT],
    22: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB8_ETC2],
    23: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA8_ETC2_EAC],
    24: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2],
    25: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_R11_EAC],
    26: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RG11_EAC],
    27: [
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_4X4_KHR,
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_4X4_KHR
    ],
    28: [
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_5X4_KHR,
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_5X4_KHR
    ],
    29: [
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_5X5_KHR,
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_5X5_KHR
    ],
    30: [
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_6X5_KHR,
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_6X5_KHR
    ],
    31: [
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_6X6_KHR,
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_6X6_KHR
    ],
    32: [
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8X5_KHR,
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_8X5_KHR
    ],
    33: [
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8X6_KHR,
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_8X6_KHR
    ],
    34: [
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8X8_KHR,
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_8X8_KHR
    ],
    35: [
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10X5_KHR,
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10X5_KHR
    ],
    36: [
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10X6_KHR,
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10X6_KHR
    ],
    37: [
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10X8_KHR,
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10X8_KHR
    ],
    38: [
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10X10_KHR,
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10X10_KHR
    ],
    39: [
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_12X10_KHR,
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_12X10_KHR
    ],
    40: [
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_12X12_KHR,
      GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_12X12_KHR
    ]
  };
  var PVR_SIZE_FUNCTIONS = {
    0: pvrtc2bppSize,
    1: pvrtc2bppSize,
    2: pvrtc4bppSize,
    3: pvrtc4bppSize,
    6: dxtEtcSmallSize,
    7: dxtEtcSmallSize,
    9: dxtEtcAstcBigSize,
    11: dxtEtcAstcBigSize,
    22: dxtEtcSmallSize,
    23: dxtEtcAstcBigSize,
    24: dxtEtcSmallSize,
    25: dxtEtcSmallSize,
    26: dxtEtcAstcBigSize,
    27: dxtEtcAstcBigSize,
    28: atc5x4Size,
    29: atc5x5Size,
    30: atc6x5Size,
    31: atc6x6Size,
    32: atc8x5Size,
    33: atc8x6Size,
    34: atc8x8Size,
    35: atc10x5Size,
    36: atc10x6Size,
    37: atc10x8Size,
    38: atc10x10Size,
    39: atc12x10Size,
    40: atc12x12Size
  };
  function isPVR(data) {
    const header = new Uint32Array(data, 0, PVR_CONSTANTS.HEADER_LENGTH);
    const version = header[PVR_CONSTANTS.MAGIC_NUMBER_INDEX];
    return version === PVR_CONSTANTS.MAGIC_NUMBER || version === PVR_CONSTANTS.MAGIC_NUMBER_EXTRA;
  }
  function parsePVR(data) {
    const header = new Uint32Array(data, 0, PVR_CONSTANTS.HEADER_LENGTH);
    const pvrFormat = header[PVR_CONSTANTS.PIXEL_FORMAT_INDEX];
    const colourSpace = header[PVR_CONSTANTS.COLOUR_SPACE_INDEX];
    const pixelFormats = PVR_PIXEL_FORMATS[pvrFormat] || [];
    const internalFormat = pixelFormats.length > 1 && colourSpace ? pixelFormats[1] : pixelFormats[0];
    const sizeFunction = PVR_SIZE_FUNCTIONS[pvrFormat];
    const mipMapLevels = header[PVR_CONSTANTS.MIPMAPCOUNT_INDEX];
    const width = header[PVR_CONSTANTS.WIDTH_INDEX];
    const height = header[PVR_CONSTANTS.HEIGHT_INDEX];
    const dataOffset = PVR_CONSTANTS.HEADER_SIZE + header[PVR_CONSTANTS.METADATA_SIZE_INDEX];
    const image = new Uint8Array(data, dataOffset);
    return extractMipmapImages(image, {
      mipMapLevels,
      width,
      height,
      sizeFunction,
      internalFormat
    });
  }
  function pvrtc2bppSize(width, height) {
    width = Math.max(width, 16);
    height = Math.max(height, 8);
    return width * height / 4;
  }
  function pvrtc4bppSize(width, height) {
    width = Math.max(width, 8);
    height = Math.max(height, 8);
    return width * height / 2;
  }
  function dxtEtcSmallSize(width, height) {
    return Math.floor((width + 3) / 4) * Math.floor((height + 3) / 4) * 8;
  }
  function dxtEtcAstcBigSize(width, height) {
    return Math.floor((width + 3) / 4) * Math.floor((height + 3) / 4) * 16;
  }
  function atc5x4Size(width, height) {
    return Math.floor((width + 4) / 5) * Math.floor((height + 3) / 4) * 16;
  }
  function atc5x5Size(width, height) {
    return Math.floor((width + 4) / 5) * Math.floor((height + 4) / 5) * 16;
  }
  function atc6x5Size(width, height) {
    return Math.floor((width + 5) / 6) * Math.floor((height + 4) / 5) * 16;
  }
  function atc6x6Size(width, height) {
    return Math.floor((width + 5) / 6) * Math.floor((height + 5) / 6) * 16;
  }
  function atc8x5Size(width, height) {
    return Math.floor((width + 7) / 8) * Math.floor((height + 4) / 5) * 16;
  }
  function atc8x6Size(width, height) {
    return Math.floor((width + 7) / 8) * Math.floor((height + 5) / 6) * 16;
  }
  function atc8x8Size(width, height) {
    return Math.floor((width + 7) / 8) * Math.floor((height + 7) / 8) * 16;
  }
  function atc10x5Size(width, height) {
    return Math.floor((width + 9) / 10) * Math.floor((height + 4) / 5) * 16;
  }
  function atc10x6Size(width, height) {
    return Math.floor((width + 9) / 10) * Math.floor((height + 5) / 6) * 16;
  }
  function atc10x8Size(width, height) {
    return Math.floor((width + 9) / 10) * Math.floor((height + 7) / 8) * 16;
  }
  function atc10x10Size(width, height) {
    return Math.floor((width + 9) / 10) * Math.floor((height + 9) / 10) * 16;
  }
  function atc12x10Size(width, height) {
    return Math.floor((width + 11) / 12) * Math.floor((height + 9) / 10) * 16;
  }
  function atc12x12Size(width, height) {
    return Math.floor((width + 11) / 12) * Math.floor((height + 11) / 12) * 16;
  }

  // src/lib/parsers/parse-compressed-texture.ts
  function parseCompressedTexture(data) {
    if (isKTX(data)) {
      return parseKTX(data);
    }
    if (isDDS(data)) {
      return parseDDS(data);
    }
    if (isPVR(data)) {
      return parsePVR(data);
    }
    throw new Error("Texture container format not recognized");
  }

  // src/lib/parsers/basis-module-loader.ts
  var VERSION4 = true ? "3.4.15" : "latest";
  var BASIS_CDN_ENCODER_WASM = `https://unpkg.com/@loaders.gl/textures@${VERSION4}/dist/libs/basis_encoder.wasm`;
  var BASIS_CDN_ENCODER_JS = `https://unpkg.com/@loaders.gl/textures@${VERSION4}/dist/libs/basis_encoder.js`;
  var loadBasisTranscoderPromise;
  async function loadBasisTrascoderModule(options) {
    const modules = options.modules || {};
    if (modules.basis) {
      return modules.basis;
    }
    loadBasisTranscoderPromise = loadBasisTranscoderPromise || loadBasisTrascoder(options);
    return await loadBasisTranscoderPromise;
  }
  async function loadBasisTrascoder(options) {
    let BASIS = null;
    let wasmBinary = null;
    [BASIS, wasmBinary] = await Promise.all([
      await loadLibrary("basis_transcoder.js", "textures", options),
      await loadLibrary("basis_transcoder.wasm", "textures", options)
    ]);
    BASIS = BASIS || globalThis.BASIS;
    return await initializeBasisTrascoderModule(BASIS, wasmBinary);
  }
  function initializeBasisTrascoderModule(BasisModule, wasmBinary) {
    const options = {};
    if (wasmBinary) {
      options.wasmBinary = wasmBinary;
    }
    return new Promise((resolve) => {
      BasisModule(options).then((module) => {
        const { BasisFile, initializeBasis } = module;
        initializeBasis();
        resolve({ BasisFile });
      });
    });
  }
  var loadBasisEncoderPromise;
  async function loadBasisEncoderModule(options) {
    const modules = options.modules || {};
    if (modules.basisEncoder) {
      return modules.basisEncoder;
    }
    loadBasisEncoderPromise = loadBasisEncoderPromise || loadBasisEncoder(options);
    return await loadBasisEncoderPromise;
  }
  async function loadBasisEncoder(options) {
    let BASIS_ENCODER = null;
    let wasmBinary = null;
    [BASIS_ENCODER, wasmBinary] = await Promise.all([
      await loadLibrary(BASIS_CDN_ENCODER_JS, "textures", options),
      await loadLibrary(BASIS_CDN_ENCODER_WASM, "textures", options)
    ]);
    BASIS_ENCODER = BASIS_ENCODER || globalThis.BASIS;
    return await initializeBasisEncoderModule(BASIS_ENCODER, wasmBinary);
  }
  function initializeBasisEncoderModule(BasisEncoderModule, wasmBinary) {
    const options = {};
    if (wasmBinary) {
      options.wasmBinary = wasmBinary;
    }
    return new Promise((resolve) => {
      BasisEncoderModule(options).then((module) => {
        const { BasisFile, KTX2File, initializeBasis, BasisEncoder } = module;
        initializeBasis();
        resolve({ BasisFile, KTX2File, BasisEncoder });
      });
    });
  }

  // src/lib/utils/texture-formats.ts
  var BROWSER_PREFIXES = ["", "WEBKIT_", "MOZ_"];
  var WEBGL_EXTENSIONS = {
    WEBGL_compressed_texture_s3tc: "dxt",
    WEBGL_compressed_texture_s3tc_srgb: "dxt-srgb",
    WEBGL_compressed_texture_etc1: "etc1",
    WEBGL_compressed_texture_etc: "etc2",
    WEBGL_compressed_texture_pvrtc: "pvrtc",
    WEBGL_compressed_texture_atc: "atc",
    WEBGL_compressed_texture_astc: "astc",
    EXT_texture_compression_rgtc: "rgtc"
  };
  var formats = null;
  function getSupportedGPUTextureFormats(gl) {
    if (!formats) {
      gl = gl || getWebGLContext() || void 0;
      formats = new Set();
      for (const prefix of BROWSER_PREFIXES) {
        for (const extension in WEBGL_EXTENSIONS) {
          if (gl && gl.getExtension(`${prefix}${extension}`)) {
            const gpuTextureFormat = WEBGL_EXTENSIONS[extension];
            formats.add(gpuTextureFormat);
          }
        }
      }
    }
    return formats;
  }
  function getWebGLContext() {
    try {
      const canvas = document.createElement("canvas");
      return canvas.getContext("webgl");
    } catch (error) {
      return null;
    }
  }

  // src/lib/parsers/parse-basis.ts
  var OutputFormat = {
    etc1: {
      basisFormat: 0,
      compressed: true,
      format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_ETC1_WEBGL
    },
    etc2: { basisFormat: 1, compressed: true },
    bc1: {
      basisFormat: 2,
      compressed: true,
      format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_S3TC_DXT1_EXT
    },
    bc3: {
      basisFormat: 3,
      compressed: true,
      format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT5_EXT
    },
    bc4: { basisFormat: 4, compressed: true },
    bc5: { basisFormat: 5, compressed: true },
    "bc7-m6-opaque-only": { basisFormat: 6, compressed: true },
    "bc7-m5": { basisFormat: 7, compressed: true },
    "pvrtc1-4-rgb": {
      basisFormat: 8,
      compressed: true,
      format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_PVRTC_4BPPV1_IMG
    },
    "pvrtc1-4-rgba": {
      basisFormat: 9,
      compressed: true,
      format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG
    },
    "astc-4x4": {
      basisFormat: 10,
      compressed: true,
      format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_4X4_KHR
    },
    "atc-rgb": { basisFormat: 11, compressed: true },
    "atc-rgba-interpolated-alpha": { basisFormat: 12, compressed: true },
    rgba32: { basisFormat: 13, compressed: false },
    rgb565: { basisFormat: 14, compressed: false },
    bgr565: { basisFormat: 15, compressed: false },
    rgba4444: { basisFormat: 16, compressed: false }
  };
  async function parseBasis(data, options) {
    if (options.basis.containerFormat === "auto") {
      if (isKTX(data)) {
        const fileConstructors = await loadBasisEncoderModule(options);
        return parseKTX2File(fileConstructors.KTX2File, data, options);
      }
      const { BasisFile } = await loadBasisTrascoderModule(options);
      return parseBasisFile(BasisFile, data, options);
    }
    switch (options.basis.module) {
      case "encoder":
        const fileConstructors = await loadBasisEncoderModule(options);
        switch (options.basis.containerFormat) {
          case "ktx2":
            return parseKTX2File(fileConstructors.KTX2File, data, options);
          case "basis":
          default:
            return parseBasisFile(fileConstructors.BasisFile, data, options);
        }
      case "transcoder":
      default:
        const { BasisFile } = await loadBasisTrascoderModule(options);
        return parseBasisFile(BasisFile, data, options);
    }
  }
  function parseBasisFile(BasisFile, data, options) {
    const basisFile = new BasisFile(new Uint8Array(data));
    try {
      if (!basisFile.startTranscoding()) {
        throw new Error("Failed to start basis transcoding");
      }
      const imageCount = basisFile.getNumImages();
      const images = [];
      for (let imageIndex = 0; imageIndex < imageCount; imageIndex++) {
        const levelsCount = basisFile.getNumLevels(imageIndex);
        const levels = [];
        for (let levelIndex = 0; levelIndex < levelsCount; levelIndex++) {
          levels.push(transcodeImage(basisFile, imageIndex, levelIndex, options));
        }
        images.push(levels);
      }
      return images;
    } finally {
      basisFile.close();
      basisFile.delete();
    }
  }
  function transcodeImage(basisFile, imageIndex, levelIndex, options) {
    const width = basisFile.getImageWidth(imageIndex, levelIndex);
    const height = basisFile.getImageHeight(imageIndex, levelIndex);
    const hasAlpha = basisFile.getHasAlpha();
    const { compressed, format, basisFormat } = getBasisOptions(options, hasAlpha);
    const decodedSize = basisFile.getImageTranscodedSizeInBytes(imageIndex, levelIndex, basisFormat);
    const decodedData = new Uint8Array(decodedSize);
    if (!basisFile.transcodeImage(decodedData, imageIndex, levelIndex, basisFormat, 0, 0)) {
      throw new Error("failed to start Basis transcoding");
    }
    return {
      width,
      height,
      data: decodedData,
      compressed,
      format,
      hasAlpha
    };
  }
  function parseKTX2File(KTX2File, data, options) {
    const ktx2File = new KTX2File(new Uint8Array(data));
    try {
      if (!ktx2File.startTranscoding()) {
        throw new Error("failed to start KTX2 transcoding");
      }
      const levelsCount = ktx2File.getLevels();
      const levels = [];
      for (let levelIndex = 0; levelIndex < levelsCount; levelIndex++) {
        levels.push(transcodeKTX2Image(ktx2File, levelIndex, options));
        break;
      }
      return [levels];
    } finally {
      ktx2File.close();
      ktx2File.delete();
    }
  }
  function transcodeKTX2Image(ktx2File, levelIndex, options) {
    const { alphaFlag, height, width } = ktx2File.getImageLevelInfo(levelIndex, 0, 0);
    const { compressed, format, basisFormat } = getBasisOptions(options, alphaFlag);
    const decodedSize = ktx2File.getImageTranscodedSizeInBytes(levelIndex, 0, 0, basisFormat);
    const decodedData = new Uint8Array(decodedSize);
    if (!ktx2File.transcodeImage(decodedData, levelIndex, 0, 0, basisFormat, 0, -1, -1)) {
      throw new Error("Failed to transcode KTX2 image");
    }
    return {
      width,
      height,
      data: decodedData,
      compressed,
      levelSize: decodedSize,
      hasAlpha: alphaFlag,
      format
    };
  }
  function getBasisOptions(options, hasAlpha) {
    let format = options && options.basis && options.basis.format;
    if (format === "auto") {
      format = selectSupportedBasisFormat();
    }
    if (typeof format === "object") {
      format = hasAlpha ? format.alpha : format.noAlpha;
    }
    format = format.toLowerCase();
    return OutputFormat[format];
  }
  function selectSupportedBasisFormat() {
    const supportedFormats = getSupportedGPUTextureFormats();
    if (supportedFormats.has("astc")) {
      return "astc-4x4";
    } else if (supportedFormats.has("dxt")) {
      return {
        alpha: "bc3",
        noAlpha: "bc1"
      };
    } else if (supportedFormats.has("pvrtc")) {
      return {
        alpha: "pvrtc1-4-rgba",
        noAlpha: "pvrtc1-4-rgb"
      };
    } else if (supportedFormats.has("etc1")) {
      return "etc1";
    } else if (supportedFormats.has("etc2")) {
      return "etc2";
    }
    return "rgb565";
  }

  // src/compressed-texture-loader.ts
  var DEFAULT_TEXTURE_LOADER_OPTIONS = {
    "compressed-texture": {
      libraryPath: "libs/",
      useBasis: false
    }
  };
  var CompressedTextureWorkerLoader = {
    name: "Texture Containers",
    id: "compressed-texture",
    module: "textures",
    version: VERSION3,
    worker: true,
    extensions: [
      "ktx",
      "ktx2",
      "dds",
      "pvr"
    ],
    mimeTypes: [
      "image/ktx2",
      "image/ktx",
      "image/vnd-ms.dds",
      "image/x-dds",
      "application/octet-stream"
    ],
    binary: true,
    options: DEFAULT_TEXTURE_LOADER_OPTIONS
  };
  var CompressedTextureLoader = {
    ...CompressedTextureWorkerLoader,
    parse: async (arrayBuffer, options) => {
      if (options["compressed-texture"].useBasis) {
        options.basis = {
          format: {
            alpha: "BC3",
            noAlpha: "BC1"
          },
          ...options.basis,
          containerFormat: "ktx2",
          module: "encoder"
        };
        return (await parseBasis(arrayBuffer, options))[0];
      }
      return parseCompressedTexture(arrayBuffer);
    }
  };

  // src/workers/compressed-texture-worker.ts
  createLoaderWorker(CompressedTextureLoader);
})();
