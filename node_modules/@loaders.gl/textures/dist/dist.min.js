(() => {
  var __defProp = Object.defineProperty;
  var __markAsModule = (target) => __defProp(target, "__esModule", { value: true });
  var __esm = (fn, res) => function __init() {
    return fn && (res = (0, fn[Object.keys(fn)[0]])(fn = 0)), res;
  };
  var __commonJS = (cb, mod) => function __require() {
    return mod || (0, cb[Object.keys(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
  };
  var __export = (target, all) => {
    __markAsModule(target);
    for (var name in all)
      __defProp(target, name, { get: all[name], enumerable: true });
  };

  // ../loader-utils/src/lib/env-utils/assert.ts
  function assert(condition, message) {
    if (!condition) {
      throw new Error(message || "loader assertion failed.");
    }
  }
  var init_assert = __esm({
    "../loader-utils/src/lib/env-utils/assert.ts"() {
    }
  });

  // ../loader-utils/src/lib/env-utils/globals.ts
  var globals, self_, window_, global_, document_, isBrowser, matches, nodeVersion;
  var init_globals = __esm({
    "../loader-utils/src/lib/env-utils/globals.ts"() {
      globals = {
        self: typeof self !== "undefined" && self,
        window: typeof window !== "undefined" && window,
        global: typeof global !== "undefined" && global,
        document: typeof document !== "undefined" && document
      };
      self_ = globals.self || globals.window || globals.global || {};
      window_ = globals.window || globals.self || globals.global || {};
      global_ = globals.global || globals.self || globals.window || {};
      document_ = globals.document || {};
      isBrowser = Boolean(typeof process !== "object" || String(process) !== "[object process]" || process.browser);
      matches = typeof process !== "undefined" && process.version && /v([0-9]*)/.exec(process.version);
      nodeVersion = matches && parseFloat(matches[1]) || 0;
    }
  });

  // ../worker-utils/src/lib/env-utils/version.ts
  var DEFAULT_VERSION, VERSION;
  var init_version = __esm({
    "../worker-utils/src/lib/env-utils/version.ts"() {
      DEFAULT_VERSION = "latest";
      VERSION = typeof __VERSION__ !== "undefined" ? __VERSION__ : DEFAULT_VERSION;
      if (typeof __VERSION__ === "undefined") {
        console.error("loaders.gl: The __VERSION__ variable is not injected using babel plugin. Latest unstable workers would be fetched from the CDN.");
      }
    }
  });

  // ../worker-utils/src/lib/env-utils/assert.ts
  function assert2(condition, message) {
    if (!condition) {
      throw new Error(message || "loaders.gl assertion failed.");
    }
  }
  var init_assert2 = __esm({
    "../worker-utils/src/lib/env-utils/assert.ts"() {
    }
  });

  // ../worker-utils/src/lib/env-utils/globals.ts
  var globals2, self_2, window_2, global_2, document_2, isBrowser2, isWorker, isMobile, matches2, nodeVersion2;
  var init_globals2 = __esm({
    "../worker-utils/src/lib/env-utils/globals.ts"() {
      globals2 = {
        self: typeof self !== "undefined" && self,
        window: typeof window !== "undefined" && window,
        global: typeof global !== "undefined" && global,
        document: typeof document !== "undefined" && document
      };
      self_2 = globals2.self || globals2.window || globals2.global || {};
      window_2 = globals2.window || globals2.self || globals2.global || {};
      global_2 = globals2.global || globals2.self || globals2.window || {};
      document_2 = globals2.document || {};
      isBrowser2 = typeof process !== "object" || String(process) !== "[object process]" || process.browser;
      isWorker = typeof importScripts === "function";
      isMobile = typeof window !== "undefined" && typeof window.orientation !== "undefined";
      matches2 = typeof process !== "undefined" && process.version && /v([0-9]*)/.exec(process.version);
      nodeVersion2 = matches2 && parseFloat(matches2[1]) || 0;
    }
  });

  // ../worker-utils/src/lib/node/require-utils.browser.ts
  var require_utils_browser_exports = {};
  __export(require_utils_browser_exports, {
    readFileAsArrayBuffer: () => readFileAsArrayBuffer,
    readFileAsText: () => readFileAsText,
    requireFromFile: () => requireFromFile,
    requireFromString: () => requireFromString
  });
  var readFileAsArrayBuffer, readFileAsText, requireFromFile, requireFromString;
  var init_require_utils_browser = __esm({
    "../worker-utils/src/lib/node/require-utils.browser.ts"() {
      readFileAsArrayBuffer = null;
      readFileAsText = null;
      requireFromFile = null;
      requireFromString = null;
    }
  });

  // ../worker-utils/src/lib/library-utils/library-utils.ts
  async function loadLibrary(libraryUrl, moduleName = null, options = {}) {
    if (moduleName) {
      libraryUrl = getLibraryUrl(libraryUrl, moduleName, options);
    }
    loadLibraryPromises[libraryUrl] = loadLibraryPromises[libraryUrl] || loadLibraryFromFile(libraryUrl);
    return await loadLibraryPromises[libraryUrl];
  }
  function getLibraryUrl(library, moduleName, options) {
    if (library.startsWith("http")) {
      return library;
    }
    const modules = options.modules || {};
    if (modules[library]) {
      return modules[library];
    }
    if (!isBrowser2) {
      return `modules/${moduleName}/dist/libs/${library}`;
    }
    if (options.CDN) {
      assert2(options.CDN.startsWith("http"));
      return `${options.CDN}/${moduleName}@${VERSION2}/dist/libs/${library}`;
    }
    if (isWorker) {
      return `../src/libs/${library}`;
    }
    return `modules/${moduleName}/src/libs/${library}`;
  }
  async function loadLibraryFromFile(libraryUrl) {
    if (libraryUrl.endsWith("wasm")) {
      const response2 = await fetch(libraryUrl);
      return await response2.arrayBuffer();
    }
    if (!isBrowser2) {
      try {
        return require_utils_browser_exports && requireFromFile && await requireFromFile(libraryUrl);
      } catch {
        return null;
      }
    }
    if (isWorker) {
      return importScripts(libraryUrl);
    }
    const response = await fetch(libraryUrl);
    const scriptSource = await response.text();
    return loadLibraryFromString(scriptSource, libraryUrl);
  }
  function loadLibraryFromString(scriptSource, id) {
    if (!isBrowser2) {
      return requireFromString && requireFromString(scriptSource, id);
    }
    if (isWorker) {
      eval.call(global_2, scriptSource);
      return null;
    }
    const script = document.createElement("script");
    script.id = id;
    try {
      script.appendChild(document.createTextNode(scriptSource));
    } catch (e2) {
      script.text = scriptSource;
    }
    document.body.appendChild(script);
    return null;
  }
  var LATEST, VERSION2, loadLibraryPromises;
  var init_library_utils = __esm({
    "../worker-utils/src/lib/library-utils/library-utils.ts"() {
      init_globals2();
      init_require_utils_browser();
      init_assert2();
      init_version();
      LATEST = "latest";
      VERSION2 = typeof VERSION !== "undefined" ? VERSION : LATEST;
      loadLibraryPromises = {};
    }
  });

  // (disabled):../worker-utils/src/lib/process-utils/child-process-proxy
  var init_child_process_proxy = __esm({
    "(disabled):../worker-utils/src/lib/process-utils/child-process-proxy"() {
    }
  });

  // ../worker-utils/src/index.ts
  var init_src = __esm({
    "../worker-utils/src/index.ts"() {
      init_globals2();
      init_library_utils();
      init_child_process_proxy();
    }
  });

  // ../loader-utils/src/lib/path-utils/file-aliases.ts
  function resolvePath(filename) {
    for (const alias in fileAliases) {
      if (filename.startsWith(alias)) {
        const replacement = fileAliases[alias];
        filename = filename.replace(alias, replacement);
      }
    }
    if (!filename.startsWith("http://") && !filename.startsWith("https://")) {
      filename = `${pathPrefix}${filename}`;
    }
    return filename;
  }
  var pathPrefix, fileAliases;
  var init_file_aliases = __esm({
    "../loader-utils/src/lib/path-utils/file-aliases.ts"() {
      pathPrefix = "";
      fileAliases = {};
    }
  });

  // ../loader-utils/src/index.ts
  var init_src2 = __esm({
    "../loader-utils/src/index.ts"() {
      init_assert();
      init_globals();
      init_file_aliases();
    }
  });

  // src/lib/utils/version.ts
  var VERSION3;
  var init_version2 = __esm({
    "src/lib/utils/version.ts"() {
      VERSION3 = typeof __VERSION__ !== "undefined" ? __VERSION__ : "latest";
    }
  });

  // src/lib/parsers/basis-module-loader.ts
  async function loadBasisTrascoderModule(options) {
    const modules = options.modules || {};
    if (modules.basis) {
      return modules.basis;
    }
    loadBasisTranscoderPromise = loadBasisTranscoderPromise || loadBasisTrascoder(options);
    return await loadBasisTranscoderPromise;
  }
  async function loadBasisTrascoder(options) {
    let BASIS = null;
    let wasmBinary = null;
    [BASIS, wasmBinary] = await Promise.all([
      await loadLibrary("basis_transcoder.js", "textures", options),
      await loadLibrary("basis_transcoder.wasm", "textures", options)
    ]);
    BASIS = BASIS || globalThis.BASIS;
    return await initializeBasisTrascoderModule(BASIS, wasmBinary);
  }
  function initializeBasisTrascoderModule(BasisModule, wasmBinary) {
    const options = {};
    if (wasmBinary) {
      options.wasmBinary = wasmBinary;
    }
    return new Promise((resolve) => {
      BasisModule(options).then((module) => {
        const { BasisFile, initializeBasis } = module;
        initializeBasis();
        resolve({ BasisFile });
      });
    });
  }
  async function loadBasisEncoderModule(options) {
    const modules = options.modules || {};
    if (modules.basisEncoder) {
      return modules.basisEncoder;
    }
    loadBasisEncoderPromise = loadBasisEncoderPromise || loadBasisEncoder(options);
    return await loadBasisEncoderPromise;
  }
  async function loadBasisEncoder(options) {
    let BASIS_ENCODER = null;
    let wasmBinary = null;
    [BASIS_ENCODER, wasmBinary] = await Promise.all([
      await loadLibrary(BASIS_CDN_ENCODER_JS, "textures", options),
      await loadLibrary(BASIS_CDN_ENCODER_WASM, "textures", options)
    ]);
    BASIS_ENCODER = BASIS_ENCODER || globalThis.BASIS;
    return await initializeBasisEncoderModule(BASIS_ENCODER, wasmBinary);
  }
  function initializeBasisEncoderModule(BasisEncoderModule, wasmBinary) {
    const options = {};
    if (wasmBinary) {
      options.wasmBinary = wasmBinary;
    }
    return new Promise((resolve) => {
      BasisEncoderModule(options).then((module) => {
        const { BasisFile, KTX2File, initializeBasis, BasisEncoder } = module;
        initializeBasis();
        resolve({ BasisFile, KTX2File, BasisEncoder });
      });
    });
  }
  var VERSION4, BASIS_CDN_ENCODER_WASM, BASIS_CDN_ENCODER_JS, loadBasisTranscoderPromise, loadBasisEncoderPromise;
  var init_basis_module_loader = __esm({
    "src/lib/parsers/basis-module-loader.ts"() {
      init_src();
      VERSION4 = typeof __VERSION__ !== "undefined" ? __VERSION__ : "latest";
      BASIS_CDN_ENCODER_WASM = `https://unpkg.com/@loaders.gl/textures@${VERSION4}/dist/libs/basis_encoder.wasm`;
      BASIS_CDN_ENCODER_JS = `https://unpkg.com/@loaders.gl/textures@${VERSION4}/dist/libs/basis_encoder.js`;
    }
  });

  // src/lib/gl-extensions.ts
  var GL_EXTENSIONS_CONSTANTS;
  var init_gl_extensions = __esm({
    "src/lib/gl-extensions.ts"() {
      GL_EXTENSIONS_CONSTANTS = {
        COMPRESSED_RGB_S3TC_DXT1_EXT: 33776,
        COMPRESSED_RGBA_S3TC_DXT1_EXT: 33777,
        COMPRESSED_RGBA_S3TC_DXT3_EXT: 33778,
        COMPRESSED_RGBA_S3TC_DXT5_EXT: 33779,
        COMPRESSED_R11_EAC: 37488,
        COMPRESSED_SIGNED_R11_EAC: 37489,
        COMPRESSED_RG11_EAC: 37490,
        COMPRESSED_SIGNED_RG11_EAC: 37491,
        COMPRESSED_RGB8_ETC2: 37492,
        COMPRESSED_RGBA8_ETC2_EAC: 37493,
        COMPRESSED_SRGB8_ETC2: 37494,
        COMPRESSED_SRGB8_ALPHA8_ETC2_EAC: 37495,
        COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2: 37496,
        COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2: 37497,
        COMPRESSED_RGB_PVRTC_4BPPV1_IMG: 35840,
        COMPRESSED_RGBA_PVRTC_4BPPV1_IMG: 35842,
        COMPRESSED_RGB_PVRTC_2BPPV1_IMG: 35841,
        COMPRESSED_RGBA_PVRTC_2BPPV1_IMG: 35843,
        COMPRESSED_RGB_ETC1_WEBGL: 36196,
        COMPRESSED_RGB_ATC_WEBGL: 35986,
        COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL: 35987,
        COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL: 34798,
        COMPRESSED_RGBA_ASTC_4X4_KHR: 37808,
        COMPRESSED_RGBA_ASTC_5X4_KHR: 37809,
        COMPRESSED_RGBA_ASTC_5X5_KHR: 37810,
        COMPRESSED_RGBA_ASTC_6X5_KHR: 37811,
        COMPRESSED_RGBA_ASTC_6X6_KHR: 37812,
        COMPRESSED_RGBA_ASTC_8X5_KHR: 37813,
        COMPRESSED_RGBA_ASTC_8X6_KHR: 37814,
        COMPRESSED_RGBA_ASTC_8X8_KHR: 37815,
        COMPRESSED_RGBA_ASTC_10X5_KHR: 37816,
        COMPRESSED_RGBA_ASTC_10X6_KHR: 37817,
        COMPRESSED_RGBA_ASTC_10X8_KHR: 37818,
        COMPRESSED_RGBA_ASTC_10X10_KHR: 37819,
        COMPRESSED_RGBA_ASTC_12X10_KHR: 37820,
        COMPRESSED_RGBA_ASTC_12X12_KHR: 37821,
        COMPRESSED_SRGB8_ALPHA8_ASTC_4X4_KHR: 37840,
        COMPRESSED_SRGB8_ALPHA8_ASTC_5X4_KHR: 37841,
        COMPRESSED_SRGB8_ALPHA8_ASTC_5X5_KHR: 37842,
        COMPRESSED_SRGB8_ALPHA8_ASTC_6X5_KHR: 37843,
        COMPRESSED_SRGB8_ALPHA8_ASTC_6X6_KHR: 37844,
        COMPRESSED_SRGB8_ALPHA8_ASTC_8X5_KHR: 37845,
        COMPRESSED_SRGB8_ALPHA8_ASTC_8X6_KHR: 37846,
        COMPRESSED_SRGB8_ALPHA8_ASTC_8X8_KHR: 37847,
        COMPRESSED_SRGB8_ALPHA8_ASTC_10X5_KHR: 37848,
        COMPRESSED_SRGB8_ALPHA8_ASTC_10X6_KHR: 37849,
        COMPRESSED_SRGB8_ALPHA8_ASTC_10X8_KHR: 37850,
        COMPRESSED_SRGB8_ALPHA8_ASTC_10X10_KHR: 37851,
        COMPRESSED_SRGB8_ALPHA8_ASTC_12X10_KHR: 37852,
        COMPRESSED_SRGB8_ALPHA8_ASTC_12X12_KHR: 37853,
        COMPRESSED_RED_RGTC1_EXT: 36283,
        COMPRESSED_SIGNED_RED_RGTC1_EXT: 36284,
        COMPRESSED_RED_GREEN_RGTC2_EXT: 36285,
        COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT: 36286,
        COMPRESSED_SRGB_S3TC_DXT1_EXT: 35916,
        COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT: 35917,
        COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT: 35918,
        COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT: 35919
      };
    }
  });

  // src/lib/utils/texture-formats.ts
  function getSupportedGPUTextureFormats(gl) {
    if (!formats) {
      gl = gl || getWebGLContext() || void 0;
      formats = new Set();
      for (const prefix of BROWSER_PREFIXES) {
        for (const extension in WEBGL_EXTENSIONS) {
          if (gl && gl.getExtension(`${prefix}${extension}`)) {
            const gpuTextureFormat = WEBGL_EXTENSIONS[extension];
            formats.add(gpuTextureFormat);
          }
        }
      }
    }
    return formats;
  }
  function getWebGLContext() {
    try {
      const canvas = document.createElement("canvas");
      return canvas.getContext("webgl");
    } catch (error) {
      return null;
    }
  }
  var BROWSER_PREFIXES, WEBGL_EXTENSIONS, formats;
  var init_texture_formats = __esm({
    "src/lib/utils/texture-formats.ts"() {
      BROWSER_PREFIXES = ["", "WEBKIT_", "MOZ_"];
      WEBGL_EXTENSIONS = {
        WEBGL_compressed_texture_s3tc: "dxt",
        WEBGL_compressed_texture_s3tc_srgb: "dxt-srgb",
        WEBGL_compressed_texture_etc1: "etc1",
        WEBGL_compressed_texture_etc: "etc2",
        WEBGL_compressed_texture_pvrtc: "pvrtc",
        WEBGL_compressed_texture_atc: "atc",
        WEBGL_compressed_texture_astc: "astc",
        EXT_texture_compression_rgtc: "rgtc"
      };
      formats = null;
    }
  });

  // ../../node_modules/ktx-parse/dist/ktx-parse.modern.js
  function _(t2) {
    return typeof TextDecoder != "undefined" ? new TextDecoder().decode(t2) : Buffer.from(t2).toString("utf8");
  }
  function p(t2) {
    const n2 = new Uint8Array(t2.buffer, t2.byteOffset, e.length);
    if (n2[0] !== e[0] || n2[1] !== e[1] || n2[2] !== e[2] || n2[3] !== e[3] || n2[4] !== e[4] || n2[5] !== e[5] || n2[6] !== e[6] || n2[7] !== e[7] || n2[8] !== e[8] || n2[9] !== e[9] || n2[10] !== e[10] || n2[11] !== e[11])
      throw new Error("Missing KTX 2.0 identifier.");
    const i2 = new U(), s2 = 17 * Uint32Array.BYTES_PER_ELEMENT, a2 = new c(t2, e.length, s2, true);
    i2.vkFormat = a2._nextUint32(), i2.typeSize = a2._nextUint32(), i2.pixelWidth = a2._nextUint32(), i2.pixelHeight = a2._nextUint32(), i2.pixelDepth = a2._nextUint32(), i2.layerCount = a2._nextUint32(), i2.faceCount = a2._nextUint32();
    const r2 = a2._nextUint32();
    i2.supercompressionScheme = a2._nextUint32();
    const o2 = a2._nextUint32(), l2 = a2._nextUint32(), f2 = a2._nextUint32(), h = a2._nextUint32(), g = a2._nextUint64(), p2 = a2._nextUint64(), x = new c(t2, e.length + s2, 3 * r2 * 8, true);
    for (let e2 = 0; e2 < r2; e2++)
      i2.levels.push({ levelData: new Uint8Array(t2.buffer, t2.byteOffset + x._nextUint64(), x._nextUint64()), uncompressedByteLength: x._nextUint64() });
    const u = new c(t2, o2, l2, true), y = { vendorId: u._skip(4)._nextUint16(), descriptorType: u._nextUint16(), versionNumber: u._nextUint16(), descriptorBlockSize: u._nextUint16(), colorModel: u._nextUint8(), colorPrimaries: u._nextUint8(), transferFunction: u._nextUint8(), flags: u._nextUint8(), texelBlockDimension: { x: u._nextUint8() + 1, y: u._nextUint8() + 1, z: u._nextUint8() + 1, w: u._nextUint8() + 1 }, bytesPlane: [u._nextUint8(), u._nextUint8(), u._nextUint8(), u._nextUint8(), u._nextUint8(), u._nextUint8(), u._nextUint8(), u._nextUint8()], samples: [] }, D = (y.descriptorBlockSize / 4 - 6) / 4;
    for (let t3 = 0; t3 < D; t3++)
      y.samples[t3] = { bitOffset: u._nextUint16(), bitLength: u._nextUint8(), channelID: u._nextUint8(), samplePosition: [u._nextUint8(), u._nextUint8(), u._nextUint8(), u._nextUint8()], sampleLower: u._nextUint32(), sampleUpper: u._nextUint32() };
    i2.dataFormatDescriptor.length = 0, i2.dataFormatDescriptor.push(y);
    const b = new c(t2, f2, h, true);
    for (; b._offset < h; ) {
      const t3 = b._nextUint32(), e2 = b._scan(t3), n3 = _(e2), s3 = b._scan(t3 - e2.byteLength);
      i2.keyValue[n3] = n3.match(/^ktx/i) ? _(s3) : s3, t3 % 4 && b._skip(4 - t3 % 4);
    }
    if (p2 <= 0)
      return i2;
    const d = new c(t2, g, p2, true), B = d._nextUint16(), w = d._nextUint16(), A = d._nextUint32(), S = d._nextUint32(), m = d._nextUint32(), L = d._nextUint32(), I = [];
    for (let t3 = 0; t3 < r2; t3++)
      I.push({ imageFlags: d._nextUint32(), rgbSliceByteOffset: d._nextUint32(), rgbSliceByteLength: d._nextUint32(), alphaSliceByteOffset: d._nextUint32(), alphaSliceByteLength: d._nextUint32() });
    const R = g + d._offset, E = R + A, T = E + S, O = T + m, P = new Uint8Array(t2.buffer, t2.byteOffset + R, A), C = new Uint8Array(t2.buffer, t2.byteOffset + E, S), F = new Uint8Array(t2.buffer, t2.byteOffset + T, m), G = new Uint8Array(t2.buffer, t2.byteOffset + O, L);
    return i2.globalData = { endpointCount: B, selectorCount: w, imageDescs: I, endpointsData: P, selectorsData: C, tablesData: F, extendedData: G }, i2;
  }
  var t, e, n, i, s, a, r, o, l, f, U, c;
  var init_ktx_parse_modern = __esm({
    "../../node_modules/ktx-parse/dist/ktx-parse.modern.js"() {
      t = new Uint8Array([0]);
      e = [171, 75, 84, 88, 32, 50, 48, 187, 13, 10, 26, 10];
      !function(t2) {
        t2[t2.NONE = 0] = "NONE", t2[t2.BASISLZ = 1] = "BASISLZ", t2[t2.ZSTD = 2] = "ZSTD", t2[t2.ZLIB = 3] = "ZLIB";
      }(n || (n = {})), function(t2) {
        t2[t2.BASICFORMAT = 0] = "BASICFORMAT";
      }(i || (i = {})), function(t2) {
        t2[t2.UNSPECIFIED = 0] = "UNSPECIFIED", t2[t2.ETC1S = 163] = "ETC1S", t2[t2.UASTC = 166] = "UASTC";
      }(s || (s = {})), function(t2) {
        t2[t2.UNSPECIFIED = 0] = "UNSPECIFIED", t2[t2.SRGB = 1] = "SRGB";
      }(a || (a = {})), function(t2) {
        t2[t2.UNSPECIFIED = 0] = "UNSPECIFIED", t2[t2.LINEAR = 1] = "LINEAR", t2[t2.SRGB = 2] = "SRGB", t2[t2.ITU = 3] = "ITU", t2[t2.NTSC = 4] = "NTSC", t2[t2.SLOG = 5] = "SLOG", t2[t2.SLOG2 = 6] = "SLOG2";
      }(r || (r = {})), function(t2) {
        t2[t2.ALPHA_STRAIGHT = 0] = "ALPHA_STRAIGHT", t2[t2.ALPHA_PREMULTIPLIED = 1] = "ALPHA_PREMULTIPLIED";
      }(o || (o = {})), function(t2) {
        t2[t2.RGB = 0] = "RGB", t2[t2.RRR = 3] = "RRR", t2[t2.GGG = 4] = "GGG", t2[t2.AAA = 15] = "AAA";
      }(l || (l = {})), function(t2) {
        t2[t2.RGB = 0] = "RGB", t2[t2.RGBA = 3] = "RGBA", t2[t2.RRR = 4] = "RRR", t2[t2.RRRG = 5] = "RRRG";
      }(f || (f = {}));
      U = class {
        constructor() {
          this.vkFormat = 0, this.typeSize = 1, this.pixelWidth = 0, this.pixelHeight = 0, this.pixelDepth = 0, this.layerCount = 0, this.faceCount = 1, this.supercompressionScheme = n.NONE, this.levels = [], this.dataFormatDescriptor = [{ vendorId: 0, descriptorType: i.BASICFORMAT, versionNumber: 2, descriptorBlockSize: 40, colorModel: s.UNSPECIFIED, colorPrimaries: a.SRGB, transferFunction: a.SRGB, flags: o.ALPHA_STRAIGHT, texelBlockDimension: { x: 4, y: 4, z: 1, w: 1 }, bytesPlane: [], samples: [] }], this.keyValue = {}, this.globalData = null;
        }
      };
      c = class {
        constructor(t2, e2, n2, i2) {
          this._dataView = new DataView(t2.buffer, t2.byteOffset + e2, n2), this._littleEndian = i2, this._offset = 0;
        }
        _nextUint8() {
          const t2 = this._dataView.getUint8(this._offset);
          return this._offset += 1, t2;
        }
        _nextUint16() {
          const t2 = this._dataView.getUint16(this._offset, this._littleEndian);
          return this._offset += 2, t2;
        }
        _nextUint32() {
          const t2 = this._dataView.getUint32(this._offset, this._littleEndian);
          return this._offset += 4, t2;
        }
        _nextUint64() {
          const t2 = this._dataView.getUint32(this._offset, this._littleEndian) + 2 ** 32 * this._dataView.getUint32(this._offset + 4, this._littleEndian);
          return this._offset += 8, t2;
        }
        _skip(t2) {
          return this._offset += t2, this;
        }
        _scan(t2, e2 = 0) {
          const n2 = this._offset;
          let i2 = 0;
          for (; this._dataView.getUint8(this._offset) !== e2 && i2 < t2; )
            i2++, this._offset++;
          return i2 < t2 && this._offset++, new Uint8Array(this._dataView.buffer, this._dataView.byteOffset + n2, i2);
        }
      };
    }
  });

  // src/lib/utils/extract-mipmap-images.ts
  function extractMipmapImages(data, options) {
    const images = new Array(options.mipMapLevels);
    let levelWidth = options.width;
    let levelHeight = options.height;
    let offset = 0;
    for (let i2 = 0; i2 < options.mipMapLevels; ++i2) {
      const levelSize = getLevelSize(options, levelWidth, levelHeight, data, i2);
      const levelData = getLevelData(data, i2, offset, levelSize);
      images[i2] = {
        compressed: true,
        format: options.internalFormat,
        data: levelData,
        width: levelWidth,
        height: levelHeight,
        levelSize
      };
      levelWidth = Math.max(1, levelWidth >> 1);
      levelHeight = Math.max(1, levelHeight >> 1);
      offset += levelSize;
    }
    return images;
  }
  function getLevelData(data, index, offset, levelSize) {
    if (!Array.isArray(data)) {
      return new Uint8Array(data.buffer, data.byteOffset + offset, levelSize);
    }
    return data[index].levelData;
  }
  function getLevelSize(options, levelWidth, levelHeight, data, index) {
    if (!Array.isArray(data)) {
      return options.sizeFunction(levelWidth, levelHeight);
    }
    return options.sizeFunction(data[index]);
  }
  var init_extract_mipmap_images = __esm({
    "src/lib/utils/extract-mipmap-images.ts"() {
    }
  });

  // src/lib/utils/ktx-format-helper.ts
  function mapVkFormatToWebGL(vkFormat) {
    return VULKAN_TO_WEBGL_FORMAT_MAP[vkFormat];
  }
  var VULKAN_TO_WEBGL_FORMAT_MAP;
  var init_ktx_format_helper = __esm({
    "src/lib/utils/ktx-format-helper.ts"() {
      init_gl_extensions();
      VULKAN_TO_WEBGL_FORMAT_MAP = {
        131: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_S3TC_DXT1_EXT,
        132: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB_S3TC_DXT1_EXT,
        133: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT1_EXT,
        134: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT,
        135: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT3_EXT,
        136: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT,
        137: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT5_EXT,
        138: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT,
        139: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RED_RGTC1_EXT,
        140: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SIGNED_RED_RGTC1_EXT,
        141: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RED_GREEN_RGTC2_EXT,
        142: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT,
        147: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB8_ETC2,
        148: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ETC2,
        149: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2,
        150: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2,
        151: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA8_ETC2_EAC,
        152: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC,
        153: GL_EXTENSIONS_CONSTANTS.COMPRESSED_R11_EAC,
        154: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SIGNED_R11_EAC,
        155: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RG11_EAC,
        156: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SIGNED_RG11_EAC,
        157: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_4x4_KHR,
        158: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR,
        159: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_5x4_KHR,
        160: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_5X4_KHR,
        161: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_5x5_KHR,
        162: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_5x5_KHR,
        163: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_6x5_KHR,
        164: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_6x5_KHR,
        165: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_6x6_KHR,
        166: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_6x6_KHR,
        167: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8x5_KHR,
        168: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_8x5_KHR,
        169: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8x6_KHR,
        170: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_8x6_KHR,
        171: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8x8_KHR,
        172: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_8x8_KHR,
        173: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x5_KHR,
        174: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10x5_KHR,
        175: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x6_KHR,
        176: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10x6_KHR,
        177: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x8_KHR,
        178: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10x8_KHR,
        179: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x10_KHR,
        180: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10x10_KHR,
        181: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_12x10_KHR,
        182: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_12x10_KHR,
        183: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_12x12_KHR,
        184: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_12x12_KHR,
        1000054e3: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG,
        1000054001: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG,
        1000066e3: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_4x4_KHR,
        1000066001: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_5x4_KHR,
        1000066002: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_5x5_KHR,
        1000066003: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_6x5_KHR,
        1000066004: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_6x6_KHR,
        1000066005: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8x5_KHR,
        1000066006: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8x6_KHR,
        1000066007: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8x8_KHR,
        1000066008: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x5_KHR,
        1000066009: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x6_KHR,
        1000066010: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x8_KHR,
        1000066011: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x10_KHR,
        1000066012: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_12x10_KHR,
        1000066013: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_12x12_KHR
      };
    }
  });

  // src/lib/parsers/parse-ktx.ts
  function isKTX(data) {
    const id = new Uint8Array(data);
    const notKTX = id.byteLength < KTX2_ID.length || id[0] !== KTX2_ID[0] || id[1] !== KTX2_ID[1] || id[2] !== KTX2_ID[2] || id[3] !== KTX2_ID[3] || id[4] !== KTX2_ID[4] || id[5] !== KTX2_ID[5] || id[6] !== KTX2_ID[6] || id[7] !== KTX2_ID[7] || id[8] !== KTX2_ID[8] || id[9] !== KTX2_ID[9] || id[10] !== KTX2_ID[10] || id[11] !== KTX2_ID[11];
    return !notKTX;
  }
  function parseKTX(arrayBuffer) {
    const uint8Array = new Uint8Array(arrayBuffer);
    const ktx = p(uint8Array);
    const mipMapLevels = Math.max(1, ktx.levels.length);
    const width = ktx.pixelWidth;
    const height = ktx.pixelHeight;
    const internalFormat = mapVkFormatToWebGL(ktx.vkFormat);
    return extractMipmapImages(ktx.levels, {
      mipMapLevels,
      width,
      height,
      sizeFunction: (level) => level.uncompressedByteLength,
      internalFormat
    });
  }
  var KTX2_ID;
  var init_parse_ktx = __esm({
    "src/lib/parsers/parse-ktx.ts"() {
      init_ktx_parse_modern();
      init_extract_mipmap_images();
      init_ktx_format_helper();
      KTX2_ID = [
        171,
        75,
        84,
        88,
        32,
        50,
        48,
        187,
        13,
        10,
        26,
        10
      ];
    }
  });

  // src/lib/parsers/parse-basis.ts
  async function parseBasis(data, options) {
    if (options.basis.containerFormat === "auto") {
      if (isKTX(data)) {
        const fileConstructors = await loadBasisEncoderModule(options);
        return parseKTX2File(fileConstructors.KTX2File, data, options);
      }
      const { BasisFile } = await loadBasisTrascoderModule(options);
      return parseBasisFile(BasisFile, data, options);
    }
    switch (options.basis.module) {
      case "encoder":
        const fileConstructors = await loadBasisEncoderModule(options);
        switch (options.basis.containerFormat) {
          case "ktx2":
            return parseKTX2File(fileConstructors.KTX2File, data, options);
          case "basis":
          default:
            return parseBasisFile(fileConstructors.BasisFile, data, options);
        }
      case "transcoder":
      default:
        const { BasisFile } = await loadBasisTrascoderModule(options);
        return parseBasisFile(BasisFile, data, options);
    }
  }
  function parseBasisFile(BasisFile, data, options) {
    const basisFile = new BasisFile(new Uint8Array(data));
    try {
      if (!basisFile.startTranscoding()) {
        throw new Error("Failed to start basis transcoding");
      }
      const imageCount = basisFile.getNumImages();
      const images = [];
      for (let imageIndex = 0; imageIndex < imageCount; imageIndex++) {
        const levelsCount = basisFile.getNumLevels(imageIndex);
        const levels = [];
        for (let levelIndex = 0; levelIndex < levelsCount; levelIndex++) {
          levels.push(transcodeImage(basisFile, imageIndex, levelIndex, options));
        }
        images.push(levels);
      }
      return images;
    } finally {
      basisFile.close();
      basisFile.delete();
    }
  }
  function transcodeImage(basisFile, imageIndex, levelIndex, options) {
    const width = basisFile.getImageWidth(imageIndex, levelIndex);
    const height = basisFile.getImageHeight(imageIndex, levelIndex);
    const hasAlpha = basisFile.getHasAlpha();
    const { compressed, format, basisFormat } = getBasisOptions(options, hasAlpha);
    const decodedSize = basisFile.getImageTranscodedSizeInBytes(imageIndex, levelIndex, basisFormat);
    const decodedData = new Uint8Array(decodedSize);
    if (!basisFile.transcodeImage(decodedData, imageIndex, levelIndex, basisFormat, 0, 0)) {
      throw new Error("failed to start Basis transcoding");
    }
    return {
      width,
      height,
      data: decodedData,
      compressed,
      format,
      hasAlpha
    };
  }
  function parseKTX2File(KTX2File, data, options) {
    const ktx2File = new KTX2File(new Uint8Array(data));
    try {
      if (!ktx2File.startTranscoding()) {
        throw new Error("failed to start KTX2 transcoding");
      }
      const levelsCount = ktx2File.getLevels();
      const levels = [];
      for (let levelIndex = 0; levelIndex < levelsCount; levelIndex++) {
        levels.push(transcodeKTX2Image(ktx2File, levelIndex, options));
        break;
      }
      return [levels];
    } finally {
      ktx2File.close();
      ktx2File.delete();
    }
  }
  function transcodeKTX2Image(ktx2File, levelIndex, options) {
    const { alphaFlag, height, width } = ktx2File.getImageLevelInfo(levelIndex, 0, 0);
    const { compressed, format, basisFormat } = getBasisOptions(options, alphaFlag);
    const decodedSize = ktx2File.getImageTranscodedSizeInBytes(levelIndex, 0, 0, basisFormat);
    const decodedData = new Uint8Array(decodedSize);
    if (!ktx2File.transcodeImage(decodedData, levelIndex, 0, 0, basisFormat, 0, -1, -1)) {
      throw new Error("Failed to transcode KTX2 image");
    }
    return {
      width,
      height,
      data: decodedData,
      compressed,
      levelSize: decodedSize,
      hasAlpha: alphaFlag,
      format
    };
  }
  function getBasisOptions(options, hasAlpha) {
    let format = options && options.basis && options.basis.format;
    if (format === "auto") {
      format = selectSupportedBasisFormat();
    }
    if (typeof format === "object") {
      format = hasAlpha ? format.alpha : format.noAlpha;
    }
    format = format.toLowerCase();
    return OutputFormat[format];
  }
  function selectSupportedBasisFormat() {
    const supportedFormats = getSupportedGPUTextureFormats();
    if (supportedFormats.has("astc")) {
      return "astc-4x4";
    } else if (supportedFormats.has("dxt")) {
      return {
        alpha: "bc3",
        noAlpha: "bc1"
      };
    } else if (supportedFormats.has("pvrtc")) {
      return {
        alpha: "pvrtc1-4-rgba",
        noAlpha: "pvrtc1-4-rgb"
      };
    } else if (supportedFormats.has("etc1")) {
      return "etc1";
    } else if (supportedFormats.has("etc2")) {
      return "etc2";
    }
    return "rgb565";
  }
  var OutputFormat;
  var init_parse_basis = __esm({
    "src/lib/parsers/parse-basis.ts"() {
      init_basis_module_loader();
      init_gl_extensions();
      init_texture_formats();
      init_parse_ktx();
      OutputFormat = {
        etc1: {
          basisFormat: 0,
          compressed: true,
          format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_ETC1_WEBGL
        },
        etc2: { basisFormat: 1, compressed: true },
        bc1: {
          basisFormat: 2,
          compressed: true,
          format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_S3TC_DXT1_EXT
        },
        bc3: {
          basisFormat: 3,
          compressed: true,
          format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT5_EXT
        },
        bc4: { basisFormat: 4, compressed: true },
        bc5: { basisFormat: 5, compressed: true },
        "bc7-m6-opaque-only": { basisFormat: 6, compressed: true },
        "bc7-m5": { basisFormat: 7, compressed: true },
        "pvrtc1-4-rgb": {
          basisFormat: 8,
          compressed: true,
          format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_PVRTC_4BPPV1_IMG
        },
        "pvrtc1-4-rgba": {
          basisFormat: 9,
          compressed: true,
          format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG
        },
        "astc-4x4": {
          basisFormat: 10,
          compressed: true,
          format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_4X4_KHR
        },
        "atc-rgb": { basisFormat: 11, compressed: true },
        "atc-rgba-interpolated-alpha": { basisFormat: 12, compressed: true },
        rgba32: { basisFormat: 13, compressed: false },
        rgb565: { basisFormat: 14, compressed: false },
        bgr565: { basisFormat: 15, compressed: false },
        rgba4444: { basisFormat: 16, compressed: false }
      };
    }
  });

  // src/basis-loader.ts
  var BasisWorkerLoader, BasisLoader;
  var init_basis_loader = __esm({
    "src/basis-loader.ts"() {
      init_src();
      init_version2();
      init_parse_basis();
      BasisWorkerLoader = {
        name: "Basis",
        id: isBrowser2 ? "basis" : "basis-nodejs",
        module: "textures",
        version: VERSION3,
        worker: true,
        extensions: ["basis", "ktx2"],
        mimeTypes: ["application/octet-stream", "image/ktx2"],
        tests: ["sB"],
        binary: true,
        options: {
          basis: {
            format: "auto",
            libraryPath: "libs/",
            containerFormat: "auto",
            module: "transcoder"
          }
        }
      };
      BasisLoader = {
        ...BasisWorkerLoader,
        parse: parseBasis
      };
    }
  });

  // src/lib/parsers/parse-dds.ts
  function isDDS(data) {
    const header = new Uint32Array(data, 0, DDS_CONSTANTS.HEADER_LENGTH);
    const magic = header[DDS_CONSTANTS.MAGIC_NUMBER_INDEX];
    return magic === DDS_CONSTANTS.MAGIC_NUMBER;
  }
  function parseDDS(data) {
    const header = new Int32Array(data, 0, DDS_CONSTANTS.HEADER_LENGTH);
    const pixelFormatNumber = header[DDS_CONSTANTS.HEADER_PF_FOURCC_INDEX];
    assert(Boolean(header[DDS_CONSTANTS.HEADER_PF_FLAGS_INDEX] & DDS_CONSTANTS.DDPF_FOURCC), "DDS: Unsupported format, must contain a FourCC code");
    const fourCC = int32ToFourCC(pixelFormatNumber);
    const internalFormat = DDS_PIXEL_FORMATS[fourCC];
    const sizeFunction = DDS_SIZE_FUNCTIONS[fourCC];
    assert(internalFormat && sizeFunction, `DDS: Unknown pixel format ${pixelFormatNumber}`);
    let mipMapLevels = 1;
    if (header[DDS_CONSTANTS.HEADER_FLAGS_INDEX] & DDS_CONSTANTS.DDSD_MIPMAPCOUNT) {
      mipMapLevels = Math.max(1, header[DDS_CONSTANTS.MIPMAPCOUNT_INDEX]);
    }
    const width = header[DDS_CONSTANTS.HEADER_WIDTH_INDEX];
    const height = header[DDS_CONSTANTS.HEADER_HEIGHT_INDEX];
    const dataOffset = header[DDS_CONSTANTS.HEADER_SIZE_INDEX] + 4;
    const image = new Uint8Array(data, dataOffset);
    return extractMipmapImages(image, {
      mipMapLevels,
      width,
      height,
      sizeFunction,
      internalFormat
    });
  }
  function getDxt1LevelSize(width, height) {
    return (width + 3 >> 2) * (height + 3 >> 2) * 8;
  }
  function getDxtXLevelSize(width, height) {
    return (width + 3 >> 2) * (height + 3 >> 2) * 16;
  }
  function int32ToFourCC(value) {
    return String.fromCharCode(value & 255, value >> 8 & 255, value >> 16 & 255, value >> 24 & 255);
  }
  var DDS_CONSTANTS, DDS_PIXEL_FORMATS, getATCLevelSize, getATCALevelSize, getATCILevelSize, DDS_SIZE_FUNCTIONS;
  var init_parse_dds = __esm({
    "src/lib/parsers/parse-dds.ts"() {
      init_src2();
      init_gl_extensions();
      init_extract_mipmap_images();
      DDS_CONSTANTS = {
        MAGIC_NUMBER: 542327876,
        HEADER_LENGTH: 31,
        MAGIC_NUMBER_INDEX: 0,
        HEADER_SIZE_INDEX: 1,
        HEADER_FLAGS_INDEX: 2,
        HEADER_HEIGHT_INDEX: 3,
        HEADER_WIDTH_INDEX: 4,
        MIPMAPCOUNT_INDEX: 7,
        HEADER_PF_FLAGS_INDEX: 20,
        HEADER_PF_FOURCC_INDEX: 21,
        DDSD_MIPMAPCOUNT: 131072,
        DDPF_FOURCC: 4
      };
      DDS_PIXEL_FORMATS = {
        DXT1: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_S3TC_DXT1_EXT,
        DXT3: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT3_EXT,
        DXT5: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT5_EXT,
        "ATC ": GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_ATC_WEBGL,
        ATCA: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL,
        ATCI: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL
      };
      getATCLevelSize = getDxt1LevelSize;
      getATCALevelSize = getDxtXLevelSize;
      getATCILevelSize = getDxtXLevelSize;
      DDS_SIZE_FUNCTIONS = {
        DXT1: getDxt1LevelSize,
        DXT3: getDxtXLevelSize,
        DXT5: getDxtXLevelSize,
        "ATC ": getATCLevelSize,
        ATCA: getATCALevelSize,
        ATCI: getATCILevelSize
      };
    }
  });

  // src/lib/parsers/parse-pvr.ts
  function isPVR(data) {
    const header = new Uint32Array(data, 0, PVR_CONSTANTS.HEADER_LENGTH);
    const version = header[PVR_CONSTANTS.MAGIC_NUMBER_INDEX];
    return version === PVR_CONSTANTS.MAGIC_NUMBER || version === PVR_CONSTANTS.MAGIC_NUMBER_EXTRA;
  }
  function parsePVR(data) {
    const header = new Uint32Array(data, 0, PVR_CONSTANTS.HEADER_LENGTH);
    const pvrFormat = header[PVR_CONSTANTS.PIXEL_FORMAT_INDEX];
    const colourSpace = header[PVR_CONSTANTS.COLOUR_SPACE_INDEX];
    const pixelFormats = PVR_PIXEL_FORMATS[pvrFormat] || [];
    const internalFormat = pixelFormats.length > 1 && colourSpace ? pixelFormats[1] : pixelFormats[0];
    const sizeFunction = PVR_SIZE_FUNCTIONS[pvrFormat];
    const mipMapLevels = header[PVR_CONSTANTS.MIPMAPCOUNT_INDEX];
    const width = header[PVR_CONSTANTS.WIDTH_INDEX];
    const height = header[PVR_CONSTANTS.HEIGHT_INDEX];
    const dataOffset = PVR_CONSTANTS.HEADER_SIZE + header[PVR_CONSTANTS.METADATA_SIZE_INDEX];
    const image = new Uint8Array(data, dataOffset);
    return extractMipmapImages(image, {
      mipMapLevels,
      width,
      height,
      sizeFunction,
      internalFormat
    });
  }
  function pvrtc2bppSize(width, height) {
    width = Math.max(width, 16);
    height = Math.max(height, 8);
    return width * height / 4;
  }
  function pvrtc4bppSize(width, height) {
    width = Math.max(width, 8);
    height = Math.max(height, 8);
    return width * height / 2;
  }
  function dxtEtcSmallSize(width, height) {
    return Math.floor((width + 3) / 4) * Math.floor((height + 3) / 4) * 8;
  }
  function dxtEtcAstcBigSize(width, height) {
    return Math.floor((width + 3) / 4) * Math.floor((height + 3) / 4) * 16;
  }
  function atc5x4Size(width, height) {
    return Math.floor((width + 4) / 5) * Math.floor((height + 3) / 4) * 16;
  }
  function atc5x5Size(width, height) {
    return Math.floor((width + 4) / 5) * Math.floor((height + 4) / 5) * 16;
  }
  function atc6x5Size(width, height) {
    return Math.floor((width + 5) / 6) * Math.floor((height + 4) / 5) * 16;
  }
  function atc6x6Size(width, height) {
    return Math.floor((width + 5) / 6) * Math.floor((height + 5) / 6) * 16;
  }
  function atc8x5Size(width, height) {
    return Math.floor((width + 7) / 8) * Math.floor((height + 4) / 5) * 16;
  }
  function atc8x6Size(width, height) {
    return Math.floor((width + 7) / 8) * Math.floor((height + 5) / 6) * 16;
  }
  function atc8x8Size(width, height) {
    return Math.floor((width + 7) / 8) * Math.floor((height + 7) / 8) * 16;
  }
  function atc10x5Size(width, height) {
    return Math.floor((width + 9) / 10) * Math.floor((height + 4) / 5) * 16;
  }
  function atc10x6Size(width, height) {
    return Math.floor((width + 9) / 10) * Math.floor((height + 5) / 6) * 16;
  }
  function atc10x8Size(width, height) {
    return Math.floor((width + 9) / 10) * Math.floor((height + 7) / 8) * 16;
  }
  function atc10x10Size(width, height) {
    return Math.floor((width + 9) / 10) * Math.floor((height + 9) / 10) * 16;
  }
  function atc12x10Size(width, height) {
    return Math.floor((width + 11) / 12) * Math.floor((height + 9) / 10) * 16;
  }
  function atc12x12Size(width, height) {
    return Math.floor((width + 11) / 12) * Math.floor((height + 11) / 12) * 16;
  }
  var PVR_CONSTANTS, PVR_PIXEL_FORMATS, PVR_SIZE_FUNCTIONS;
  var init_parse_pvr = __esm({
    "src/lib/parsers/parse-pvr.ts"() {
      init_gl_extensions();
      init_extract_mipmap_images();
      PVR_CONSTANTS = {
        MAGIC_NUMBER: 55727696,
        MAGIC_NUMBER_EXTRA: 1347834371,
        HEADER_LENGTH: 13,
        HEADER_SIZE: 52,
        MAGIC_NUMBER_INDEX: 0,
        PIXEL_FORMAT_INDEX: 2,
        COLOUR_SPACE_INDEX: 4,
        HEIGHT_INDEX: 6,
        WIDTH_INDEX: 7,
        MIPMAPCOUNT_INDEX: 11,
        METADATA_SIZE_INDEX: 12
      };
      PVR_PIXEL_FORMATS = {
        0: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_PVRTC_2BPPV1_IMG],
        1: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG],
        2: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_PVRTC_4BPPV1_IMG],
        3: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG],
        6: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_ETC1_WEBGL],
        7: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_S3TC_DXT1_EXT],
        9: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT3_EXT],
        11: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT5_EXT],
        22: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB8_ETC2],
        23: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA8_ETC2_EAC],
        24: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2],
        25: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_R11_EAC],
        26: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RG11_EAC],
        27: [
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_4X4_KHR,
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_4X4_KHR
        ],
        28: [
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_5X4_KHR,
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_5X4_KHR
        ],
        29: [
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_5X5_KHR,
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_5X5_KHR
        ],
        30: [
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_6X5_KHR,
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_6X5_KHR
        ],
        31: [
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_6X6_KHR,
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_6X6_KHR
        ],
        32: [
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8X5_KHR,
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_8X5_KHR
        ],
        33: [
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8X6_KHR,
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_8X6_KHR
        ],
        34: [
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8X8_KHR,
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_8X8_KHR
        ],
        35: [
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10X5_KHR,
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10X5_KHR
        ],
        36: [
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10X6_KHR,
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10X6_KHR
        ],
        37: [
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10X8_KHR,
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10X8_KHR
        ],
        38: [
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10X10_KHR,
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10X10_KHR
        ],
        39: [
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_12X10_KHR,
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_12X10_KHR
        ],
        40: [
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_12X12_KHR,
          GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_12X12_KHR
        ]
      };
      PVR_SIZE_FUNCTIONS = {
        0: pvrtc2bppSize,
        1: pvrtc2bppSize,
        2: pvrtc4bppSize,
        3: pvrtc4bppSize,
        6: dxtEtcSmallSize,
        7: dxtEtcSmallSize,
        9: dxtEtcAstcBigSize,
        11: dxtEtcAstcBigSize,
        22: dxtEtcSmallSize,
        23: dxtEtcAstcBigSize,
        24: dxtEtcSmallSize,
        25: dxtEtcSmallSize,
        26: dxtEtcAstcBigSize,
        27: dxtEtcAstcBigSize,
        28: atc5x4Size,
        29: atc5x5Size,
        30: atc6x5Size,
        31: atc6x6Size,
        32: atc8x5Size,
        33: atc8x6Size,
        34: atc8x8Size,
        35: atc10x5Size,
        36: atc10x6Size,
        37: atc10x8Size,
        38: atc10x10Size,
        39: atc12x10Size,
        40: atc12x12Size
      };
    }
  });

  // src/lib/parsers/parse-compressed-texture.ts
  function parseCompressedTexture(data) {
    if (isKTX(data)) {
      return parseKTX(data);
    }
    if (isDDS(data)) {
      return parseDDS(data);
    }
    if (isPVR(data)) {
      return parsePVR(data);
    }
    throw new Error("Texture container format not recognized");
  }
  var init_parse_compressed_texture = __esm({
    "src/lib/parsers/parse-compressed-texture.ts"() {
      init_parse_ktx();
      init_parse_dds();
      init_parse_pvr();
    }
  });

  // src/compressed-texture-loader.ts
  var DEFAULT_TEXTURE_LOADER_OPTIONS, CompressedTextureWorkerLoader, CompressedTextureLoader;
  var init_compressed_texture_loader = __esm({
    "src/compressed-texture-loader.ts"() {
      init_version2();
      init_parse_compressed_texture();
      init_parse_basis();
      DEFAULT_TEXTURE_LOADER_OPTIONS = {
        "compressed-texture": {
          libraryPath: "libs/",
          useBasis: false
        }
      };
      CompressedTextureWorkerLoader = {
        name: "Texture Containers",
        id: "compressed-texture",
        module: "textures",
        version: VERSION3,
        worker: true,
        extensions: [
          "ktx",
          "ktx2",
          "dds",
          "pvr"
        ],
        mimeTypes: [
          "image/ktx2",
          "image/ktx",
          "image/vnd-ms.dds",
          "image/x-dds",
          "application/octet-stream"
        ],
        binary: true,
        options: DEFAULT_TEXTURE_LOADER_OPTIONS
      };
      CompressedTextureLoader = {
        ...CompressedTextureWorkerLoader,
        parse: async (arrayBuffer, options) => {
          if (options["compressed-texture"].useBasis) {
            options.basis = {
              format: {
                alpha: "BC3",
                noAlpha: "BC1"
              },
              ...options.basis,
              containerFormat: "ktx2",
              module: "encoder"
            };
            return (await parseBasis(arrayBuffer, options))[0];
          }
          return parseCompressedTexture(arrayBuffer);
        }
      };
    }
  });

  // src/crunch-loader.ts
  var CrunchLoader;
  var init_crunch_loader = __esm({
    "src/crunch-loader.ts"() {
      init_version2();
      CrunchLoader = {
        id: "crunch",
        name: "Crunch",
        module: "textures",
        version: VERSION3,
        worker: true,
        extensions: ["crn"],
        mimeTypes: ["image/crn", "image/x-crn", "application/octet-stream"],
        binary: true,
        options: {
          crunch: {
            libraryPath: "libs/"
          }
        }
      };
    }
  });

  // src/lib/parsers/parse-npy.ts
  function systemIsLittleEndian() {
    const a2 = new Uint32Array([305419896]);
    const b = new Uint8Array(a2.buffer, a2.byteOffset, a2.byteLength);
    return !(b[0] === 18);
  }
  function parseNPY(arrayBuffer, options) {
    if (!arrayBuffer) {
      return null;
    }
    const view = new DataView(arrayBuffer);
    const { header, headerEndOffset } = parseHeader(view);
    const numpyType = header.descr;
    const ArrayType = DTYPES[numpyType.slice(1, 3)];
    if (!ArrayType) {
      throw new Error(`Unimplemented type ${numpyType}`);
    }
    const nArrayElements = header.shape?.reduce((a2, b) => a2 * b);
    const arrayByteLength = nArrayElements * ArrayType.BYTES_PER_ELEMENT;
    if (arrayBuffer.byteLength < headerEndOffset + arrayByteLength) {
      throw new Error("Buffer overflow");
    }
    const data = new ArrayType(arrayBuffer.slice(headerEndOffset, headerEndOffset + arrayByteLength));
    if (numpyType[0] === ">" && LITTLE_ENDIAN_OS || numpyType[0] === "<" && !LITTLE_ENDIAN_OS) {
      throw new Error("Incorrect endianness");
    }
    return {
      data,
      header
    };
  }
  function parseHeader(view) {
    const majorVersion = view.getUint8(6);
    let offset = 8;
    let headerLength;
    if (majorVersion >= 2) {
      headerLength = view.getUint32(offset, true);
      offset += 4;
    } else {
      headerLength = view.getUint16(offset, true);
      offset += 2;
    }
    const encoding = majorVersion <= 2 ? "latin1" : "utf-8";
    const decoder = new TextDecoder(encoding);
    const headerArray = new Uint8Array(view.buffer, offset, headerLength);
    const headerText = decoder.decode(headerArray);
    offset += headerLength;
    const header = JSON.parse(headerText.replace(/'/g, '"').replace("False", "false").replace("(", "[").replace(/,*\),*/g, "]"));
    return { header, headerEndOffset: offset };
  }
  var LITTLE_ENDIAN_OS, DTYPES;
  var init_parse_npy = __esm({
    "src/lib/parsers/parse-npy.ts"() {
      LITTLE_ENDIAN_OS = systemIsLittleEndian();
      DTYPES = {
        u1: Uint8Array,
        i1: Int8Array,
        u2: Uint16Array,
        i2: Int16Array,
        u4: Uint32Array,
        i4: Int32Array,
        f4: Float32Array,
        f8: Float64Array
      };
    }
  });

  // src/npy-loader.ts
  var NPY_MAGIC_NUMBER, NPYWorkerLoader, NPYLoader;
  var init_npy_loader = __esm({
    "src/npy-loader.ts"() {
      init_version2();
      init_parse_npy();
      NPY_MAGIC_NUMBER = new Uint8Array([147, 78, 85, 77, 80, 89]);
      NPYWorkerLoader = {
        name: "NPY",
        id: "npy",
        module: "textures",
        version: VERSION3,
        worker: true,
        extensions: ["npy"],
        mimeTypes: [],
        tests: [NPY_MAGIC_NUMBER.buffer],
        options: {
          npy: {}
        }
      };
      NPYLoader = {
        ...NPYWorkerLoader,
        parseSync: parseNPY,
        parse: async (arrayBuffer, options) => parseNPY(arrayBuffer, options)
      };
    }
  });

  // src/lib/encoders/encode-texture.ts
  async function encodeImageURLToCompressedTextureURL(inputUrl, outputUrl, options) {
    const args = [
      "texture-compressor",
      "--type",
      "s3tc",
      "--compression",
      "DXT1",
      "--quality",
      "normal",
      "--input",
      inputUrl,
      "--output",
      outputUrl
    ];
    const childProcess = new import_child_process_proxy.default();
    await childProcess.start({
      command: "npx",
      arguments: args,
      spawn: options
    });
    return outputUrl;
  }
  var init_encode_texture = __esm({
    "src/lib/encoders/encode-texture.ts"() {
      init_src();
    }
  });

  // src/compressed-texture-writer.ts
  var CompressedTextureWriter;
  var init_compressed_texture_writer = __esm({
    "src/compressed-texture-writer.ts"() {
      init_version2();
      init_encode_texture();
      CompressedTextureWriter = {
        name: "DDS Texture Container",
        id: "dds",
        module: "textures",
        version: VERSION3,
        extensions: ["dds"],
        options: {
          texture: {
            format: "auto",
            compression: "auto",
            quality: "auto",
            mipmap: false,
            flipY: false,
            toolFlags: ""
          }
        },
        encodeURLtoURL: encodeImageURLToCompressedTextureURL
      };
    }
  });

  // src/lib/encoders/encode-ktx2-basis-texture.ts
  async function encodeKTX2BasisTexture(image, options = {}) {
    const { useSRGB = false, qualityLevel = 10, encodeUASTC = false, mipmaps = false } = options;
    const { BasisEncoder } = await loadBasisEncoderModule(options);
    const basisEncoder = new BasisEncoder();
    try {
      const basisFileData = new Uint8Array(image.width * image.height * 4);
      basisEncoder.setCreateKTX2File(true);
      basisEncoder.setKTX2UASTCSupercompression(true);
      basisEncoder.setKTX2SRGBTransferFunc(true);
      basisEncoder.setSliceSourceImage(0, image.data, image.width, image.height, false);
      basisEncoder.setPerceptual(useSRGB);
      basisEncoder.setMipSRGB(useSRGB);
      basisEncoder.setQualityLevel(qualityLevel);
      basisEncoder.setUASTC(encodeUASTC);
      basisEncoder.setMipGen(mipmaps);
      const numOutputBytes = basisEncoder.encode(basisFileData);
      const actualKTX2FileData = basisFileData.subarray(0, numOutputBytes).buffer;
      return actualKTX2FileData;
    } catch (error) {
      console.error("Basis Universal Supercompressed GPU Texture encoder Error: ", error);
      throw error;
    } finally {
      basisEncoder.delete();
    }
  }
  var init_encode_ktx2_basis_texture = __esm({
    "src/lib/encoders/encode-ktx2-basis-texture.ts"() {
      init_basis_module_loader();
    }
  });

  // src/ktx2-basis-writer.ts
  var KTX2BasisWriter;
  var init_ktx2_basis_writer = __esm({
    "src/ktx2-basis-writer.ts"() {
      init_version2();
      init_encode_ktx2_basis_texture();
      KTX2BasisWriter = {
        name: "Basis Universal Supercompressed GPU Texture",
        id: "ktx2-basis-writer",
        module: "textures",
        version: VERSION3,
        extensions: ["ktx2"],
        options: {
          useSRGB: false,
          qualityLevel: 10,
          encodeUASTC: false,
          mipmaps: false
        },
        encode: encodeKTX2BasisTexture
      };
    }
  });

  // ../images/src/lib/utils/version.ts
  var VERSION5;
  var init_version3 = __esm({
    "../images/src/lib/utils/version.ts"() {
      VERSION5 = typeof __VERSION__ !== "undefined" ? __VERSION__ : "latest";
    }
  });

  // ../images/src/lib/category-api/image-type.ts
  function isImageTypeSupported(type) {
    switch (type) {
      case "auto":
        return IMAGE_BITMAP_SUPPORTED || IMAGE_SUPPORTED || DATA_SUPPORTED;
      case "imagebitmap":
        return IMAGE_BITMAP_SUPPORTED;
      case "image":
        return IMAGE_SUPPORTED;
      case "data":
        return DATA_SUPPORTED;
      default:
        throw new Error(`@loaders.gl/images: image ${type} not supported in this environment`);
    }
  }
  function getDefaultImageType() {
    if (IMAGE_BITMAP_SUPPORTED) {
      return "imagebitmap";
    }
    if (IMAGE_SUPPORTED) {
      return "image";
    }
    if (DATA_SUPPORTED) {
      return "data";
    }
    throw new Error("Install '@loaders.gl/polyfills' to parse images under Node.js");
  }
  var _parseImageNode, IMAGE_SUPPORTED, IMAGE_BITMAP_SUPPORTED, NODE_IMAGE_SUPPORTED, DATA_SUPPORTED;
  var init_image_type = __esm({
    "../images/src/lib/category-api/image-type.ts"() {
      init_src2();
      ({ _parseImageNode } = globalThis);
      IMAGE_SUPPORTED = typeof Image !== "undefined";
      IMAGE_BITMAP_SUPPORTED = typeof ImageBitmap !== "undefined";
      NODE_IMAGE_SUPPORTED = Boolean(_parseImageNode);
      DATA_SUPPORTED = isBrowser ? true : NODE_IMAGE_SUPPORTED;
    }
  });

  // ../images/src/lib/category-api/parsed-image-api.ts
  function getImageType(image) {
    const format = getImageTypeOrNull(image);
    if (!format) {
      throw new Error("Not an image");
    }
    return format;
  }
  function getImageSize(image) {
    return getImageData(image);
  }
  function getImageData(image) {
    switch (getImageType(image)) {
      case "data":
        return image;
      case "image":
      case "imagebitmap":
        const canvas = document.createElement("canvas");
        const context = canvas.getContext("2d");
        if (!context) {
          throw new Error("getImageData");
        }
        canvas.width = image.width;
        canvas.height = image.height;
        context.drawImage(image, 0, 0);
        return context.getImageData(0, 0, image.width, image.height);
      default:
        throw new Error("getImageData");
    }
  }
  function getImageTypeOrNull(image) {
    if (typeof ImageBitmap !== "undefined" && image instanceof ImageBitmap) {
      return "imagebitmap";
    }
    if (typeof Image !== "undefined" && image instanceof Image) {
      return "image";
    }
    if (image && typeof image === "object" && image.data && image.width && image.height) {
      return "data";
    }
    return null;
  }
  var init_parsed_image_api = __esm({
    "../images/src/lib/category-api/parsed-image-api.ts"() {
    }
  });

  // ../images/src/lib/parsers/svg-utils.ts
  function isSVG(url) {
    return url && (SVG_DATA_URL_PATTERN.test(url) || SVG_URL_PATTERN.test(url));
  }
  function getBlobOrSVGDataUrl(arrayBuffer, url) {
    if (isSVG(url)) {
      const textDecoder = new TextDecoder();
      let xmlText = textDecoder.decode(arrayBuffer);
      try {
        if (typeof unescape === "function" && typeof encodeURIComponent === "function") {
          xmlText = unescape(encodeURIComponent(xmlText));
        }
      } catch (error) {
        throw new Error(error.message);
      }
      const src = `data:image/svg+xml;base64,${btoa(xmlText)}`;
      return src;
    }
    return getBlob(arrayBuffer, url);
  }
  function getBlob(arrayBuffer, url) {
    if (isSVG(url)) {
      throw new Error("SVG cannot be parsed directly to imagebitmap");
    }
    return new Blob([new Uint8Array(arrayBuffer)]);
  }
  var SVG_DATA_URL_PATTERN, SVG_URL_PATTERN;
  var init_svg_utils = __esm({
    "../images/src/lib/parsers/svg-utils.ts"() {
      SVG_DATA_URL_PATTERN = /^data:image\/svg\+xml/;
      SVG_URL_PATTERN = /\.svg((\?|#).*)?$/;
    }
  });

  // ../images/src/lib/parsers/parse-to-image.ts
  async function parseToImage(arrayBuffer, options, url) {
    const blobOrDataUrl = getBlobOrSVGDataUrl(arrayBuffer, url);
    const URL = self.URL || self.webkitURL;
    const objectUrl = typeof blobOrDataUrl !== "string" && URL.createObjectURL(blobOrDataUrl);
    try {
      return await loadToImage(objectUrl || blobOrDataUrl, options);
    } finally {
      if (objectUrl) {
        URL.revokeObjectURL(objectUrl);
      }
    }
  }
  async function loadToImage(url, options) {
    const image = new Image();
    image.src = url;
    if (options.image && options.image.decode && image.decode) {
      await image.decode();
      return image;
    }
    return await new Promise((resolve, reject) => {
      try {
        image.onload = () => resolve(image);
        image.onerror = (err) => reject(new Error(`Could not load image ${url}: ${err}`));
      } catch (error) {
        reject(error);
      }
    });
  }
  var init_parse_to_image = __esm({
    "../images/src/lib/parsers/parse-to-image.ts"() {
      init_svg_utils();
    }
  });

  // ../images/src/lib/parsers/parse-to-image-bitmap.ts
  async function parseToImageBitmap(arrayBuffer, options, url) {
    let blob;
    if (isSVG(url)) {
      const image = await parseToImage(arrayBuffer, options, url);
      blob = image;
    } else {
      blob = getBlob(arrayBuffer, url);
    }
    const imagebitmapOptions = options && options.imagebitmap;
    return await safeCreateImageBitmap(blob, imagebitmapOptions);
  }
  async function safeCreateImageBitmap(blob, imagebitmapOptions = null) {
    if (isEmptyObject(imagebitmapOptions) || !imagebitmapOptionsSupported) {
      imagebitmapOptions = null;
    }
    if (imagebitmapOptions) {
      try {
        return await createImageBitmap(blob, imagebitmapOptions);
      } catch (error) {
        console.warn(error);
        imagebitmapOptionsSupported = false;
      }
    }
    return await createImageBitmap(blob);
  }
  function isEmptyObject(object) {
    for (const key in object || EMPTY_OBJECT) {
      return false;
    }
    return true;
  }
  var EMPTY_OBJECT, imagebitmapOptionsSupported;
  var init_parse_to_image_bitmap = __esm({
    "../images/src/lib/parsers/parse-to-image-bitmap.ts"() {
      init_svg_utils();
      init_parse_to_image();
      EMPTY_OBJECT = {};
      imagebitmapOptionsSupported = true;
    }
  });

  // ../images/src/lib/category-api/parse-isobmff-binary.ts
  function getISOBMFFMediaType(buffer) {
    if (!checkString(buffer, "ftyp", 4)) {
      return null;
    }
    if ((buffer[8] & 96) === 0) {
      return null;
    }
    return decodeMajorBrand(buffer);
  }
  function decodeMajorBrand(buffer) {
    const brandMajor = getUTF8String(buffer, 8, 12).replace("\0", " ").trim();
    switch (brandMajor) {
      case "avif":
      case "avis":
        return { extension: "avif", mimeType: "image/avif" };
      default:
        return null;
    }
  }
  function getUTF8String(array, start, end) {
    return String.fromCharCode(...array.slice(start, end));
  }
  function stringToBytes(string) {
    return [...string].map((character) => character.charCodeAt(0));
  }
  function checkString(buffer, header, offset = 0) {
    const headerBytes = stringToBytes(header);
    for (let i2 = 0; i2 < headerBytes.length; ++i2) {
      if (headerBytes[i2] !== buffer[i2 + offset]) {
        return false;
      }
    }
    return true;
  }
  var init_parse_isobmff_binary = __esm({
    "../images/src/lib/category-api/parse-isobmff-binary.ts"() {
    }
  });

  // ../images/src/lib/category-api/binary-image-api.ts
  function getBinaryImageMetadata(binaryData) {
    const dataView = toDataView(binaryData);
    return getPngMetadata(dataView) || getJpegMetadata(dataView) || getGifMetadata(dataView) || getBmpMetadata(dataView) || getISOBMFFMetadata(dataView);
  }
  function getISOBMFFMetadata(binaryData) {
    const buffer = new Uint8Array(binaryData instanceof DataView ? binaryData.buffer : binaryData);
    const mediaType = getISOBMFFMediaType(buffer);
    if (!mediaType) {
      return null;
    }
    return {
      mimeType: mediaType.mimeType,
      width: 0,
      height: 0
    };
  }
  function getPngMetadata(binaryData) {
    const dataView = toDataView(binaryData);
    const isPng = dataView.byteLength >= 24 && dataView.getUint32(0, BIG_ENDIAN) === 2303741511;
    if (!isPng) {
      return null;
    }
    return {
      mimeType: "image/png",
      width: dataView.getUint32(16, BIG_ENDIAN),
      height: dataView.getUint32(20, BIG_ENDIAN)
    };
  }
  function getGifMetadata(binaryData) {
    const dataView = toDataView(binaryData);
    const isGif = dataView.byteLength >= 10 && dataView.getUint32(0, BIG_ENDIAN) === 1195984440;
    if (!isGif) {
      return null;
    }
    return {
      mimeType: "image/gif",
      width: dataView.getUint16(6, LITTLE_ENDIAN),
      height: dataView.getUint16(8, LITTLE_ENDIAN)
    };
  }
  function getBmpMetadata(binaryData) {
    const dataView = toDataView(binaryData);
    const isBmp = dataView.byteLength >= 14 && dataView.getUint16(0, BIG_ENDIAN) === 16973 && dataView.getUint32(2, LITTLE_ENDIAN) === dataView.byteLength;
    if (!isBmp) {
      return null;
    }
    return {
      mimeType: "image/bmp",
      width: dataView.getUint32(18, LITTLE_ENDIAN),
      height: dataView.getUint32(22, LITTLE_ENDIAN)
    };
  }
  function getJpegMetadata(binaryData) {
    const dataView = toDataView(binaryData);
    const isJpeg = dataView.byteLength >= 3 && dataView.getUint16(0, BIG_ENDIAN) === 65496 && dataView.getUint8(2) === 255;
    if (!isJpeg) {
      return null;
    }
    const { tableMarkers, sofMarkers } = getJpegMarkers();
    let i2 = 2;
    while (i2 + 9 < dataView.byteLength) {
      const marker = dataView.getUint16(i2, BIG_ENDIAN);
      if (sofMarkers.has(marker)) {
        return {
          mimeType: "image/jpeg",
          height: dataView.getUint16(i2 + 5, BIG_ENDIAN),
          width: dataView.getUint16(i2 + 7, BIG_ENDIAN)
        };
      }
      if (!tableMarkers.has(marker)) {
        return null;
      }
      i2 += 2;
      i2 += dataView.getUint16(i2, BIG_ENDIAN);
    }
    return null;
  }
  function getJpegMarkers() {
    const tableMarkers = new Set([65499, 65476, 65484, 65501, 65534]);
    for (let i2 = 65504; i2 < 65520; ++i2) {
      tableMarkers.add(i2);
    }
    const sofMarkers = new Set([
      65472,
      65473,
      65474,
      65475,
      65477,
      65478,
      65479,
      65481,
      65482,
      65483,
      65485,
      65486,
      65487,
      65502
    ]);
    return { tableMarkers, sofMarkers };
  }
  function toDataView(data) {
    if (data instanceof DataView) {
      return data;
    }
    if (ArrayBuffer.isView(data)) {
      return new DataView(data.buffer);
    }
    if (data instanceof ArrayBuffer) {
      return new DataView(data);
    }
    throw new Error("toDataView");
  }
  var BIG_ENDIAN, LITTLE_ENDIAN;
  var init_binary_image_api = __esm({
    "../images/src/lib/category-api/binary-image-api.ts"() {
      init_parse_isobmff_binary();
      BIG_ENDIAN = false;
      LITTLE_ENDIAN = true;
    }
  });

  // ../images/src/lib/parsers/parse-to-node-image.ts
  async function parseToNodeImage(arrayBuffer, options) {
    const { mimeType } = getBinaryImageMetadata(arrayBuffer) || {};
    const _parseImageNode2 = globalThis._parseImageNode;
    assert(_parseImageNode2);
    return await _parseImageNode2(arrayBuffer, mimeType);
  }
  var init_parse_to_node_image = __esm({
    "../images/src/lib/parsers/parse-to-node-image.ts"() {
      init_src2();
      init_binary_image_api();
    }
  });

  // ../images/src/lib/parsers/parse-image.ts
  async function parseImage(arrayBuffer, options, context) {
    options = options || {};
    const imageOptions = options.image || {};
    const imageType = imageOptions.type || "auto";
    const { url } = context || {};
    const loadType = getLoadableImageType(imageType);
    let image;
    switch (loadType) {
      case "imagebitmap":
        image = await parseToImageBitmap(arrayBuffer, options, url);
        break;
      case "image":
        image = await parseToImage(arrayBuffer, options, url);
        break;
      case "data":
        image = await parseToNodeImage(arrayBuffer, options);
        break;
      default:
        assert(false);
    }
    if (imageType === "data") {
      image = getImageData(image);
    }
    return image;
  }
  function getLoadableImageType(type) {
    switch (type) {
      case "auto":
      case "data":
        return getDefaultImageType();
      default:
        isImageTypeSupported(type);
        return type;
    }
  }
  var init_parse_image = __esm({
    "../images/src/lib/parsers/parse-image.ts"() {
      init_src2();
      init_image_type();
      init_parsed_image_api();
      init_parse_to_image();
      init_parse_to_image_bitmap();
      init_parse_to_node_image();
    }
  });

  // ../images/src/image-loader.ts
  var EXTENSIONS, MIME_TYPES, DEFAULT_IMAGE_LOADER_OPTIONS, ImageLoader;
  var init_image_loader = __esm({
    "../images/src/image-loader.ts"() {
      init_version3();
      init_parse_image();
      init_binary_image_api();
      EXTENSIONS = ["png", "jpg", "jpeg", "gif", "webp", "bmp", "ico", "svg", "avif"];
      MIME_TYPES = [
        "image/png",
        "image/jpeg",
        "image/gif",
        "image/webp",
        "image/avif",
        "image/bmp",
        "image/vnd.microsoft.icon",
        "image/svg+xml"
      ];
      DEFAULT_IMAGE_LOADER_OPTIONS = {
        image: {
          type: "auto",
          decode: true
        }
      };
      ImageLoader = {
        id: "image",
        module: "images",
        name: "Images",
        version: VERSION5,
        mimeTypes: MIME_TYPES,
        extensions: EXTENSIONS,
        parse: parseImage,
        tests: [(arrayBuffer) => Boolean(getBinaryImageMetadata(new DataView(arrayBuffer)))],
        options: DEFAULT_IMAGE_LOADER_OPTIONS
      };
    }
  });

  // ../images/src/index.ts
  var init_src3 = __esm({
    "../images/src/index.ts"() {
      init_image_loader();
      init_parsed_image_api();
    }
  });

  // src/lib/texture-api/generate-url.ts
  function generateUrl(getUrl, options, urlOptions) {
    let url = typeof getUrl === "function" ? getUrl({ ...options, ...urlOptions }) : getUrl;
    const baseUrl = options.baseUrl;
    if (baseUrl) {
      url = baseUrl[baseUrl.length - 1] === "/" ? `${baseUrl}${url}` : `${baseUrl}/${url}`;
    }
    return resolvePath(url);
  }
  var init_generate_url = __esm({
    "src/lib/texture-api/generate-url.ts"() {
      init_src2();
    }
  });

  // src/lib/texture-api/async-deep-map.ts
  async function asyncDeepMap(tree, func, options = {}) {
    return await mapSubtree(tree, func, options);
  }
  async function mapSubtree(object, func, options) {
    if (Array.isArray(object)) {
      return await mapArray(object, func, options);
    }
    if (isObject(object)) {
      return await mapObject(object, func, options);
    }
    const url = object;
    return await func(url, options);
  }
  async function mapObject(object, func, options) {
    const promises = [];
    const values = {};
    for (const key in object) {
      const url = object[key];
      const promise = mapSubtree(url, func, options).then((value) => {
        values[key] = value;
      });
      promises.push(promise);
    }
    await Promise.all(promises);
    return values;
  }
  async function mapArray(urlArray, func, options = {}) {
    const promises = urlArray.map((url) => mapSubtree(url, func, options));
    return await Promise.all(promises);
  }
  var isObject;
  var init_async_deep_map = __esm({
    "src/lib/texture-api/async-deep-map.ts"() {
      isObject = (value) => value && typeof value === "object";
    }
  });

  // src/lib/texture-api/deep-load.ts
  async function deepLoad(urlTree, load, options) {
    return await asyncDeepMap(urlTree, (url) => shallowLoad(url, load, options));
  }
  async function shallowLoad(url, load, options) {
    const response = await fetch(url, options.fetch);
    const arrayBuffer = await response.arrayBuffer();
    return await load(arrayBuffer, options);
  }
  var init_deep_load = __esm({
    "src/lib/texture-api/deep-load.ts"() {
      init_async_deep_map();
    }
  });

  // src/lib/texture-api/load-image.ts
  async function loadImageTexture(getUrl, options = {}) {
    const imageUrls = await getImageUrls(getUrl, options);
    return await deepLoad(imageUrls, ImageLoader.parse, options);
  }
  async function getImageUrls(getUrl, options, urlOptions = {}) {
    const mipLevels = options && options.image && options.image.mipLevels || 0;
    return mipLevels !== 0 ? await getMipmappedImageUrls(getUrl, mipLevels, options, urlOptions) : generateUrl(getUrl, options, urlOptions);
  }
  async function getMipmappedImageUrls(getUrl, mipLevels, options, urlOptions) {
    const urls = [];
    if (mipLevels === "auto") {
      const url = generateUrl(getUrl, options, { ...urlOptions, lod: 0 });
      const image = await shallowLoad(url, ImageLoader.parse, options);
      const { width, height } = getImageSize(image);
      mipLevels = getMipLevels({ width, height });
      urls.push(url);
    }
    assert(mipLevels > 0);
    for (let mipLevel = urls.length; mipLevel < mipLevels; ++mipLevel) {
      const url = generateUrl(getUrl, options, { ...urlOptions, lod: mipLevel });
      urls.push(url);
    }
    return urls;
  }
  function getMipLevels(size) {
    return 1 + Math.floor(Math.log2(Math.max(size.width, size.height)));
  }
  var init_load_image = __esm({
    "src/lib/texture-api/load-image.ts"() {
      init_src2();
      init_src3();
      init_generate_url();
      init_deep_load();
    }
  });

  // src/lib/texture-api/load-image-array.ts
  async function loadImageTextureArray(count, getUrl, options = {}) {
    const imageUrls = await getImageArrayUrls(count, getUrl, options);
    return await deepLoad(imageUrls, ImageLoader.parse, options);
  }
  async function getImageArrayUrls(count, getUrl, options = {}) {
    const promises = [];
    for (let index = 0; index < count; index++) {
      const promise = getImageUrls(getUrl, options, { index });
      promises.push(promise);
    }
    return await Promise.all(promises);
  }
  var init_load_image_array = __esm({
    "src/lib/texture-api/load-image-array.ts"() {
      init_src3();
      init_load_image();
      init_deep_load();
    }
  });

  // src/lib/texture-api/load-image-cube.ts
  async function getImageCubeUrls(getUrl, options) {
    const urls = {};
    const promises = [];
    let index = 0;
    for (let i2 = 0; i2 < CUBE_FACES.length; ++i2) {
      const face = CUBE_FACES[index];
      const promise = getImageUrls(getUrl, options, { ...face, index: index++ }).then((url) => {
        urls[face.face] = url;
      });
      promises.push(promise);
    }
    await Promise.all(promises);
    return urls;
  }
  async function loadImageTextureCube(getUrl, options = {}) {
    const urls = await getImageCubeUrls(getUrl, options);
    return await deepLoad(urls, ImageLoader.parse, options);
  }
  var GL_TEXTURE_CUBE_MAP_POSITIVE_X, GL_TEXTURE_CUBE_MAP_NEGATIVE_X, GL_TEXTURE_CUBE_MAP_POSITIVE_Y, GL_TEXTURE_CUBE_MAP_NEGATIVE_Y, GL_TEXTURE_CUBE_MAP_POSITIVE_Z, GL_TEXTURE_CUBE_MAP_NEGATIVE_Z, CUBE_FACES;
  var init_load_image_cube = __esm({
    "src/lib/texture-api/load-image-cube.ts"() {
      init_src3();
      init_load_image();
      init_deep_load();
      GL_TEXTURE_CUBE_MAP_POSITIVE_X = 34069;
      GL_TEXTURE_CUBE_MAP_NEGATIVE_X = 34070;
      GL_TEXTURE_CUBE_MAP_POSITIVE_Y = 34071;
      GL_TEXTURE_CUBE_MAP_NEGATIVE_Y = 34072;
      GL_TEXTURE_CUBE_MAP_POSITIVE_Z = 34073;
      GL_TEXTURE_CUBE_MAP_NEGATIVE_Z = 34074;
      CUBE_FACES = [
        { face: GL_TEXTURE_CUBE_MAP_POSITIVE_X, direction: "right", axis: "x", sign: "positive" },
        { face: GL_TEXTURE_CUBE_MAP_NEGATIVE_X, direction: "left", axis: "x", sign: "negative" },
        { face: GL_TEXTURE_CUBE_MAP_POSITIVE_Y, direction: "top", axis: "y", sign: "positive" },
        { face: GL_TEXTURE_CUBE_MAP_NEGATIVE_Y, direction: "bottom", axis: "y", sign: "negative" },
        { face: GL_TEXTURE_CUBE_MAP_POSITIVE_Z, direction: "front", axis: "z", sign: "positive" },
        { face: GL_TEXTURE_CUBE_MAP_NEGATIVE_Z, direction: "back", axis: "z", sign: "negative" }
      ];
    }
  });

  // src/index.ts
  var src_exports = {};
  __export(src_exports, {
    BasisLoader: () => BasisLoader,
    BasisWorkerLoader: () => BasisWorkerLoader,
    CompressedTextureLoader: () => CompressedTextureLoader,
    CompressedTextureWorkerLoader: () => CompressedTextureWorkerLoader,
    CompressedTextureWriter: () => CompressedTextureWriter,
    CrunchLoader: () => CrunchLoader,
    CrunchWorkerLoader: () => CrunchLoader,
    GL_EXTENSIONS_CONSTANTS: () => GL_EXTENSIONS_CONSTANTS,
    KTX2BasisWriter: () => KTX2BasisWriter,
    KTX2BasisWriterWorker: () => KTX2BasisWriterWorker,
    NPYLoader: () => NPYLoader,
    NPYWorkerLoader: () => NPYWorkerLoader,
    getSupportedGPUTextureFormats: () => getSupportedGPUTextureFormats,
    loadImageTexture: () => loadImageTexture,
    loadImageTextureArray: () => loadImageTextureArray,
    loadImageTextureCube: () => loadImageTextureCube,
    selectSupportedBasisFormat: () => selectSupportedBasisFormat
  });
  var KTX2BasisWriterWorker;
  var init_src4 = __esm({
    "src/index.ts"() {
      init_src2();
      init_version2();
      init_basis_loader();
      init_compressed_texture_loader();
      init_crunch_loader();
      init_npy_loader();
      init_compressed_texture_writer();
      init_ktx2_basis_writer();
      init_load_image();
      init_load_image_array();
      init_load_image_cube();
      init_gl_extensions();
      init_parse_basis();
      init_texture_formats();
      init_crunch_loader();
      KTX2BasisWriterWorker = {
        name: "Basis Universal Supercompressed GPU Texture",
        id: isBrowser ? "ktx2-basis-writer" : "ktx2-basis-writer-nodejs",
        module: "textures",
        version: VERSION3,
        extensions: ["ktx2"],
        worker: true,
        options: {
          useSRGB: false,
          qualityLevel: 10,
          encodeUASTC: false,
          mipmaps: false
        }
      };
    }
  });

  // src/bundle.ts
  var require_bundle = __commonJS({
    "src/bundle.ts"(exports, module) {
      var moduleExports = (init_src4(), src_exports);
      globalThis.loaders = globalThis.loaders || {};
      module.exports = Object.assign(globalThis.loaders, moduleExports);
    }
  });
  require_bundle();
})();
