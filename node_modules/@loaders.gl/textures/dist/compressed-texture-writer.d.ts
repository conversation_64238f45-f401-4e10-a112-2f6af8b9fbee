import type { Writer, WriterOptions } from '@loaders.gl/loader-utils';
import { encodeImageURLToCompressedTextureURL } from './lib/encoders/encode-texture';
export type CompressedTextureWriterOptions = WriterOptions & {
    cwd?: string;
    texture?: {
        format: string;
        compression: string;
        quality: string;
        mipmap: boolean;
        flipY: boolean;
        toolFlags: string;
    };
};
/**
 * DDS Texture Container Exporter
 */
export declare const CompressedTextureWriter: {
    name: string;
    id: string;
    module: string;
    version: any;
    extensions: string[];
    options: {
        texture: {
            format: string;
            compression: string;
            quality: string;
            mipmap: boolean;
            flipY: boolean;
            toolFlags: string;
        };
    };
    encodeURLtoURL: typeof encodeImageURLToCompressedTextureURL;
};
export declare const _TypecheckCompressedTextureWriter: Writer & {
    encodeURLtoURL: (inputUrl: string, outputUrl: string, options?: CompressedTextureWriterOptions) => Promise<string>;
};
//# sourceMappingURL=compressed-texture-writer.d.ts.map