import type { Loader, LoaderWithParser } from '@loaders.gl/loader-utils';
import parseBasis from './lib/parsers/parse-basis';
/**
 * Worker loader for Basis super compressed textures
 */
export declare const BasisWorkerLoader: {
    name: string;
    id: string;
    module: string;
    version: any;
    worker: boolean;
    extensions: string[];
    mimeTypes: string[];
    tests: string[];
    binary: boolean;
    options: {
        basis: {
            format: string;
            libraryPath: string;
            containerFormat: string;
            module: string;
        };
    };
};
/**
 * Loader for Basis super compressed textures
 */
export declare const BasisLoader: {
    parse: typeof parseBasis;
    name: string;
    id: string;
    module: string;
    version: any;
    worker: boolean;
    extensions: string[];
    mimeTypes: string[];
    tests: string[];
    binary: boolean;
    options: {
        basis: {
            format: string;
            libraryPath: string;
            containerFormat: string;
            module: string;
        };
    };
};
export declare const _TypecheckBasisWorkerLoader: Loader;
export declare const _TypecheckBasisLoader: LoaderWithParser;
//# sourceMappingURL=basis-loader.d.ts.map