(() => {
  var __defProp = Object.defineProperty;
  var __markAsModule = (target) => __defProp(target, "__esModule", { value: true });
  var __export = (target, all) => {
    __markAsModule(target);
    for (var name in all)
      __defProp(target, name, { get: all[name], enumerable: true });
  };

  // ../loader-utils/src/lib/env-utils/assert.ts
  function assert(condition, message) {
    if (!condition) {
      throw new Error(message || "loader assertion failed.");
    }
  }

  // ../worker-utils/src/lib/env-utils/version.ts
  var VERSION = true ? "3.4.15" : DEFAULT_VERSION;
  if (false) {
    console.error("loaders.gl: The __VERSION__ variable is not injected using babel plugin. Latest unstable workers would be fetched from the CDN.");
  }

  // ../worker-utils/src/lib/env-utils/assert.ts
  function assert2(condition, message) {
    if (!condition) {
      throw new Error(message || "loaders.gl assertion failed.");
    }
  }

  // ../worker-utils/src/lib/env-utils/globals.ts
  var globals = {
    self: typeof self !== "undefined" && self,
    window: typeof window !== "undefined" && window,
    global: typeof global !== "undefined" && global,
    document: typeof document !== "undefined" && document
  };
  var self_ = globals.self || globals.window || globals.global || {};
  var window_ = globals.window || globals.self || globals.global || {};
  var global_ = globals.global || globals.self || globals.window || {};
  var document_ = globals.document || {};
  var isBrowser = typeof process !== "object" || String(process) !== "[object process]" || process.browser;
  var isWorker = typeof importScripts === "function";
  var isMobile = typeof window !== "undefined" && typeof window.orientation !== "undefined";
  var matches = typeof process !== "undefined" && process.version && /v([0-9]*)/.exec(process.version);
  var nodeVersion = matches && parseFloat(matches[1]) || 0;

  // ../worker-utils/src/lib/worker-utils/get-transfer-list.ts
  function getTransferList(object, recursive = true, transfers) {
    const transfersSet = transfers || new Set();
    if (!object) {
    } else if (isTransferable(object)) {
      transfersSet.add(object);
    } else if (isTransferable(object.buffer)) {
      transfersSet.add(object.buffer);
    } else if (ArrayBuffer.isView(object)) {
    } else if (recursive && typeof object === "object") {
      for (const key in object) {
        getTransferList(object[key], recursive, transfersSet);
      }
    }
    return transfers === void 0 ? Array.from(transfersSet) : [];
  }
  function isTransferable(object) {
    if (!object) {
      return false;
    }
    if (object instanceof ArrayBuffer) {
      return true;
    }
    if (typeof MessagePort !== "undefined" && object instanceof MessagePort) {
      return true;
    }
    if (typeof ImageBitmap !== "undefined" && object instanceof ImageBitmap) {
      return true;
    }
    if (typeof OffscreenCanvas !== "undefined" && object instanceof OffscreenCanvas) {
      return true;
    }
    return false;
  }

  // ../worker-utils/src/lib/worker-farm/worker-body.ts
  function getParentPort() {
    let parentPort;
    try {
      eval("globalThis.parentPort = require('worker_threads').parentPort");
      parentPort = globalThis.parentPort;
    } catch {
    }
    return parentPort;
  }
  var onMessageWrapperMap = new Map();
  var WorkerBody = class {
    static inWorkerThread() {
      return typeof self !== "undefined" || Boolean(getParentPort());
    }
    static set onmessage(onMessage) {
      function handleMessage(message) {
        const parentPort3 = getParentPort();
        const { type, payload } = parentPort3 ? message : message.data;
        onMessage(type, payload);
      }
      const parentPort2 = getParentPort();
      if (parentPort2) {
        parentPort2.on("message", handleMessage);
        parentPort2.on("exit", () => console.debug("Node worker closing"));
      } else {
        globalThis.onmessage = handleMessage;
      }
    }
    static addEventListener(onMessage) {
      let onMessageWrapper = onMessageWrapperMap.get(onMessage);
      if (!onMessageWrapper) {
        onMessageWrapper = (message) => {
          if (!isKnownMessage(message)) {
            return;
          }
          const parentPort3 = getParentPort();
          const { type, payload } = parentPort3 ? message : message.data;
          onMessage(type, payload);
        };
      }
      const parentPort2 = getParentPort();
      if (parentPort2) {
        console.error("not implemented");
      } else {
        globalThis.addEventListener("message", onMessageWrapper);
      }
    }
    static removeEventListener(onMessage) {
      const onMessageWrapper = onMessageWrapperMap.get(onMessage);
      onMessageWrapperMap.delete(onMessage);
      const parentPort2 = getParentPort();
      if (parentPort2) {
        console.error("not implemented");
      } else {
        globalThis.removeEventListener("message", onMessageWrapper);
      }
    }
    static postMessage(type, payload) {
      const data = { source: "loaders.gl", type, payload };
      const transferList = getTransferList(payload);
      const parentPort2 = getParentPort();
      if (parentPort2) {
        parentPort2.postMessage(data, transferList);
      } else {
        globalThis.postMessage(data, transferList);
      }
    }
  };
  function isKnownMessage(message) {
    const { type, data } = message;
    return type === "message" && data && typeof data.source === "string" && data.source.startsWith("loaders.gl");
  }

  // ../worker-utils/src/lib/node/require-utils.browser.ts
  var require_utils_browser_exports = {};
  __export(require_utils_browser_exports, {
    readFileAsArrayBuffer: () => readFileAsArrayBuffer,
    readFileAsText: () => readFileAsText,
    requireFromFile: () => requireFromFile,
    requireFromString: () => requireFromString
  });
  var readFileAsArrayBuffer = null;
  var readFileAsText = null;
  var requireFromFile = null;
  var requireFromString = null;

  // ../worker-utils/src/lib/library-utils/library-utils.ts
  var LATEST = "latest";
  var VERSION2 = typeof VERSION !== "undefined" ? VERSION : LATEST;
  var loadLibraryPromises = {};
  async function loadLibrary(libraryUrl, moduleName = null, options = {}) {
    if (moduleName) {
      libraryUrl = getLibraryUrl(libraryUrl, moduleName, options);
    }
    loadLibraryPromises[libraryUrl] = loadLibraryPromises[libraryUrl] || loadLibraryFromFile(libraryUrl);
    return await loadLibraryPromises[libraryUrl];
  }
  function getLibraryUrl(library, moduleName, options) {
    if (library.startsWith("http")) {
      return library;
    }
    const modules = options.modules || {};
    if (modules[library]) {
      return modules[library];
    }
    if (!isBrowser) {
      return `modules/${moduleName}/dist/libs/${library}`;
    }
    if (options.CDN) {
      assert2(options.CDN.startsWith("http"));
      return `${options.CDN}/${moduleName}@${VERSION2}/dist/libs/${library}`;
    }
    if (isWorker) {
      return `../src/libs/${library}`;
    }
    return `modules/${moduleName}/src/libs/${library}`;
  }
  async function loadLibraryFromFile(libraryUrl) {
    if (libraryUrl.endsWith("wasm")) {
      const response2 = await fetch(libraryUrl);
      return await response2.arrayBuffer();
    }
    if (!isBrowser) {
      try {
        return require_utils_browser_exports && requireFromFile && await requireFromFile(libraryUrl);
      } catch {
        return null;
      }
    }
    if (isWorker) {
      return importScripts(libraryUrl);
    }
    const response = await fetch(libraryUrl);
    const scriptSource = await response.text();
    return loadLibraryFromString(scriptSource, libraryUrl);
  }
  function loadLibraryFromString(scriptSource, id) {
    if (!isBrowser) {
      return requireFromString && requireFromString(scriptSource, id);
    }
    if (isWorker) {
      eval.call(global_, scriptSource);
      return null;
    }
    const script = document.createElement("script");
    script.id = id;
    try {
      script.appendChild(document.createTextNode(scriptSource));
    } catch (e) {
      script.text = scriptSource;
    }
    document.body.appendChild(script);
    return null;
  }

  // ../loader-utils/src/lib/worker-loader-utils/create-loader-worker.ts
  var requestId = 0;
  function createLoaderWorker(loader) {
    if (!WorkerBody.inWorkerThread()) {
      return;
    }
    WorkerBody.onmessage = async (type, payload) => {
      switch (type) {
        case "process":
          try {
            const { input, options = {}, context = {} } = payload;
            const result = await parseData({
              loader,
              arrayBuffer: input,
              options,
              context: {
                ...context,
                parse: parseOnMainThread
              }
            });
            WorkerBody.postMessage("done", { result });
          } catch (error) {
            const message = error instanceof Error ? error.message : "";
            WorkerBody.postMessage("error", { error: message });
          }
          break;
        default:
      }
    };
  }
  function parseOnMainThread(arrayBuffer, options) {
    return new Promise((resolve, reject) => {
      const id = requestId++;
      const onMessage = (type, payload2) => {
        if (payload2.id !== id) {
          return;
        }
        switch (type) {
          case "done":
            WorkerBody.removeEventListener(onMessage);
            resolve(payload2.result);
            break;
          case "error":
            WorkerBody.removeEventListener(onMessage);
            reject(payload2.error);
            break;
          default:
        }
      };
      WorkerBody.addEventListener(onMessage);
      const payload = { id, input: arrayBuffer, options };
      WorkerBody.postMessage("process", payload);
    });
  }
  async function parseData({ loader, arrayBuffer, options, context }) {
    let data;
    let parser;
    if (loader.parseSync || loader.parse) {
      data = arrayBuffer;
      parser = loader.parseSync || loader.parse;
    } else if (loader.parseTextSync) {
      const textDecoder = new TextDecoder();
      data = textDecoder.decode(arrayBuffer);
      parser = loader.parseTextSync;
    } else {
      throw new Error(`Could not load data with ${loader.name} loader`);
    }
    options = {
      ...options,
      modules: loader && loader.options && loader.options.modules || {},
      worker: false
    };
    return await parser(data, { ...options }, context, loader);
  }

  // src/lib/utils/version.ts
  var VERSION3 = true ? "3.4.15" : "latest";

  // src/crunch-loader.ts
  var CrunchLoader = {
    id: "crunch",
    name: "Crunch",
    module: "textures",
    version: VERSION3,
    worker: true,
    extensions: ["crn"],
    mimeTypes: ["image/crn", "image/x-crn", "application/octet-stream"],
    binary: true,
    options: {
      crunch: {
        libraryPath: "libs/"
      }
    }
  };

  // src/lib/parsers/crunch-module-loader.ts
  async function loadCrunchModule(options) {
    const modules = options.modules || {};
    if (modules.crunch) {
      return modules.crunch;
    }
    return loadCrunch(options);
  }
  var crunchModule;
  async function loadCrunch(options) {
    if (crunchModule) {
      return crunchModule;
    }
    let loadCrunchDecoder = await loadLibrary("crunch.js", "textures", options);
    loadCrunchDecoder = loadCrunchDecoder || globalThis.LoadCrunchDecoder;
    crunchModule = loadCrunchDecoder();
    return crunchModule;
  }

  // src/lib/gl-extensions.ts
  var GL_EXTENSIONS_CONSTANTS = {
    COMPRESSED_RGB_S3TC_DXT1_EXT: 33776,
    COMPRESSED_RGBA_S3TC_DXT1_EXT: 33777,
    COMPRESSED_RGBA_S3TC_DXT3_EXT: 33778,
    COMPRESSED_RGBA_S3TC_DXT5_EXT: 33779,
    COMPRESSED_R11_EAC: 37488,
    COMPRESSED_SIGNED_R11_EAC: 37489,
    COMPRESSED_RG11_EAC: 37490,
    COMPRESSED_SIGNED_RG11_EAC: 37491,
    COMPRESSED_RGB8_ETC2: 37492,
    COMPRESSED_RGBA8_ETC2_EAC: 37493,
    COMPRESSED_SRGB8_ETC2: 37494,
    COMPRESSED_SRGB8_ALPHA8_ETC2_EAC: 37495,
    COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2: 37496,
    COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2: 37497,
    COMPRESSED_RGB_PVRTC_4BPPV1_IMG: 35840,
    COMPRESSED_RGBA_PVRTC_4BPPV1_IMG: 35842,
    COMPRESSED_RGB_PVRTC_2BPPV1_IMG: 35841,
    COMPRESSED_RGBA_PVRTC_2BPPV1_IMG: 35843,
    COMPRESSED_RGB_ETC1_WEBGL: 36196,
    COMPRESSED_RGB_ATC_WEBGL: 35986,
    COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL: 35987,
    COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL: 34798,
    COMPRESSED_RGBA_ASTC_4X4_KHR: 37808,
    COMPRESSED_RGBA_ASTC_5X4_KHR: 37809,
    COMPRESSED_RGBA_ASTC_5X5_KHR: 37810,
    COMPRESSED_RGBA_ASTC_6X5_KHR: 37811,
    COMPRESSED_RGBA_ASTC_6X6_KHR: 37812,
    COMPRESSED_RGBA_ASTC_8X5_KHR: 37813,
    COMPRESSED_RGBA_ASTC_8X6_KHR: 37814,
    COMPRESSED_RGBA_ASTC_8X8_KHR: 37815,
    COMPRESSED_RGBA_ASTC_10X5_KHR: 37816,
    COMPRESSED_RGBA_ASTC_10X6_KHR: 37817,
    COMPRESSED_RGBA_ASTC_10X8_KHR: 37818,
    COMPRESSED_RGBA_ASTC_10X10_KHR: 37819,
    COMPRESSED_RGBA_ASTC_12X10_KHR: 37820,
    COMPRESSED_RGBA_ASTC_12X12_KHR: 37821,
    COMPRESSED_SRGB8_ALPHA8_ASTC_4X4_KHR: 37840,
    COMPRESSED_SRGB8_ALPHA8_ASTC_5X4_KHR: 37841,
    COMPRESSED_SRGB8_ALPHA8_ASTC_5X5_KHR: 37842,
    COMPRESSED_SRGB8_ALPHA8_ASTC_6X5_KHR: 37843,
    COMPRESSED_SRGB8_ALPHA8_ASTC_6X6_KHR: 37844,
    COMPRESSED_SRGB8_ALPHA8_ASTC_8X5_KHR: 37845,
    COMPRESSED_SRGB8_ALPHA8_ASTC_8X6_KHR: 37846,
    COMPRESSED_SRGB8_ALPHA8_ASTC_8X8_KHR: 37847,
    COMPRESSED_SRGB8_ALPHA8_ASTC_10X5_KHR: 37848,
    COMPRESSED_SRGB8_ALPHA8_ASTC_10X6_KHR: 37849,
    COMPRESSED_SRGB8_ALPHA8_ASTC_10X8_KHR: 37850,
    COMPRESSED_SRGB8_ALPHA8_ASTC_10X10_KHR: 37851,
    COMPRESSED_SRGB8_ALPHA8_ASTC_12X10_KHR: 37852,
    COMPRESSED_SRGB8_ALPHA8_ASTC_12X12_KHR: 37853,
    COMPRESSED_RED_RGTC1_EXT: 36283,
    COMPRESSED_SIGNED_RED_RGTC1_EXT: 36284,
    COMPRESSED_RED_GREEN_RGTC2_EXT: 36285,
    COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT: 36286,
    COMPRESSED_SRGB_S3TC_DXT1_EXT: 35916,
    COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT: 35917,
    COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT: 35918,
    COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT: 35919
  };

  // src/lib/utils/extract-mipmap-images.ts
  function extractMipmapImages(data, options) {
    const images = new Array(options.mipMapLevels);
    let levelWidth = options.width;
    let levelHeight = options.height;
    let offset = 0;
    for (let i = 0; i < options.mipMapLevels; ++i) {
      const levelSize = getLevelSize(options, levelWidth, levelHeight, data, i);
      const levelData = getLevelData(data, i, offset, levelSize);
      images[i] = {
        compressed: true,
        format: options.internalFormat,
        data: levelData,
        width: levelWidth,
        height: levelHeight,
        levelSize
      };
      levelWidth = Math.max(1, levelWidth >> 1);
      levelHeight = Math.max(1, levelHeight >> 1);
      offset += levelSize;
    }
    return images;
  }
  function getLevelData(data, index, offset, levelSize) {
    if (!Array.isArray(data)) {
      return new Uint8Array(data.buffer, data.byteOffset + offset, levelSize);
    }
    return data[index].levelData;
  }
  function getLevelSize(options, levelWidth, levelHeight, data, index) {
    if (!Array.isArray(data)) {
      return options.sizeFunction(levelWidth, levelHeight);
    }
    return options.sizeFunction(data[index]);
  }

  // src/lib/parsers/parse-dds.ts
  var DDS_PIXEL_FORMATS = {
    DXT1: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_S3TC_DXT1_EXT,
    DXT3: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT3_EXT,
    DXT5: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT5_EXT,
    "ATC ": GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_ATC_WEBGL,
    ATCA: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL,
    ATCI: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL
  };
  function getDxt1LevelSize(width, height) {
    return (width + 3 >> 2) * (height + 3 >> 2) * 8;
  }
  function getDxtXLevelSize(width, height) {
    return (width + 3 >> 2) * (height + 3 >> 2) * 16;
  }

  // src/lib/parsers/parse-crunch.ts
  var CRN_FORMAT = {
    cCRNFmtInvalid: -1,
    cCRNFmtDXT1: 0,
    cCRNFmtDXT3: 1,
    cCRNFmtDXT5: 2
  };
  var DXT_FORMAT_MAP = {
    [CRN_FORMAT.cCRNFmtDXT1]: {
      pixelFormat: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_S3TC_DXT1_EXT,
      sizeFunction: getDxt1LevelSize
    },
    [CRN_FORMAT.cCRNFmtDXT3]: {
      pixelFormat: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT3_EXT,
      sizeFunction: getDxtXLevelSize
    },
    [CRN_FORMAT.cCRNFmtDXT5]: {
      pixelFormat: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT5_EXT,
      sizeFunction: getDxtXLevelSize
    }
  };
  var cachedDstSize = 0;
  var dst;
  async function parseCrunch(data, options) {
    const crunchModule2 = await loadCrunchModule(options);
    const srcSize = data.byteLength;
    const bytes = new Uint8Array(data);
    const src = crunchModule2._malloc(srcSize);
    arrayBufferCopy(bytes, crunchModule2.HEAPU8, src, srcSize);
    const format = crunchModule2._crn_get_dxt_format(src, srcSize);
    assert(Boolean(DXT_FORMAT_MAP[format]), "Unsupported format");
    const mipMapLevels = crunchModule2._crn_get_levels(src, srcSize);
    const width = crunchModule2._crn_get_width(src, srcSize);
    const height = crunchModule2._crn_get_height(src, srcSize);
    const sizeFunction = DXT_FORMAT_MAP[format].sizeFunction;
    let dstSize = 0;
    for (let i = 0; i < mipMapLevels; ++i) {
      dstSize += sizeFunction(width >> i, height >> i);
    }
    if (cachedDstSize < dstSize) {
      if (dst) {
        crunchModule2._free(dst);
      }
      dst = crunchModule2._malloc(dstSize);
      cachedDstSize = dstSize;
    }
    crunchModule2._crn_decompress(src, srcSize, dst, dstSize, 0, mipMapLevels);
    crunchModule2._free(src);
    const image = new Uint8Array(crunchModule2.HEAPU8.buffer, dst, dstSize).slice();
    return extractMipmapImages(image, {
      mipMapLevels,
      width,
      height,
      sizeFunction,
      internalFormat: DXT_FORMAT_MAP[format].pixelFormat
    });
  }
  function arrayBufferCopy(srcData, dstData, dstByteOffset, numBytes) {
    let i;
    const dst32Offset = dstByteOffset / 4;
    const tail = numBytes % 4;
    const src32 = new Uint32Array(srcData.buffer, 0, (numBytes - tail) / 4);
    const dst32 = new Uint32Array(dstData.buffer);
    for (i = 0; i < src32.length; i++) {
      dst32[dst32Offset + i] = src32[i];
    }
    for (i = numBytes - tail; i < numBytes; i++) {
      dstData[dstByteOffset + i] = srcData[i];
    }
  }

  // src/workers/crunch-worker.ts
  var CrunchLoaderWithParser = {
    ...CrunchLoader,
    parse: parseCrunch
  };
  createLoaderWorker(CrunchLoaderWithParser);
})();
