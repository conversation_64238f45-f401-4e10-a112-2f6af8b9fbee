{"version": 3, "file": "ktx2-basis-writer-nodejs.js", "names": ["KTX2BasisWriter", "KTX2BasisWriterNodeJS"], "sources": ["../../src/ktx2-basis-writer-nodejs.ts"], "sourcesContent": ["// Polyfills increases the bundle size significantly. Use it for NodeJS worker only\nimport '@loaders.gl/polyfills';\n\nexport {KTX2BasisWriter as KTX2BasisWriterNodeJS} from './ktx2-basis-writer';\n"], "mappings": "AACA,OAAO,uBAAuB;AAE9B,SAAQA,eAAe,IAAIC,qBAAqB,QAAO,qBAAqB"}