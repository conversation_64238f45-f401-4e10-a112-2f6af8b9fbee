import { isBrowser } from '@loaders.gl/loader-utils';
import { VERSION } from './lib/utils/version';
export { Ba<PERSON><PERSON>oa<PERSON>, <PERSON><PERSON><PERSON>orkerLoader } from './basis-loader';
export { CompressedTextureLoader, CompressedTextureWorkerLoader } from './compressed-texture-loader';
export { CrunchLoader } from './crunch-loader';
export { NPYLoader, NPYWorkerLoader } from './npy-loader';
export { CompressedTextureWriter } from './compressed-texture-writer';
export { KTX2BasisWriter } from './ktx2-basis-writer';
export const KTX2BasisWriterWorker = {
  name: 'Basis Universal Supercompressed GPU Texture',
  id: isBrowser ? 'ktx2-basis-writer' : 'ktx2-basis-writer-nodejs',
  module: 'textures',
  version: VERSION,
  extensions: ['ktx2'],
  worker: true,
  options: {
    useSRGB: false,
    qualityLevel: 10,
    encodeUASTC: false,
    mipmaps: false
  }
};
export { loadImageTexture } from './lib/texture-api/load-image';
export { loadImageTextureArray } from './lib/texture-api/load-image-array';
export { loadImageTextureCube } from './lib/texture-api/load-image-cube';
export { GL_EXTENSIONS_CONSTANTS } from './lib/gl-extensions';
export { selectSupportedBasisFormat } from './lib/parsers/parse-basis';
export { getSupportedGPUTextureFormats } from './lib/utils/texture-formats';
export { CrunchLoader as CrunchWorkerLoader } from './crunch-loader';
//# sourceMappingURL=index.js.map