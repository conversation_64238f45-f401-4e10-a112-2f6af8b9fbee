{"version": 3, "file": "basis-loader.js", "names": ["<PERSON><PERSON><PERSON><PERSON>", "VERSION", "parseBasis", "BasisWorkerLoader", "name", "id", "module", "version", "worker", "extensions", "mimeTypes", "tests", "binary", "options", "basis", "format", "libraryPath", "containerFormat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parse", "_TypecheckBasisWorkerLoader", "_TypecheckBasisLoader"], "sources": ["../../src/basis-loader.ts"], "sourcesContent": ["import type {Loader, LoaderWithParser} from '@loaders.gl/loader-utils';\nimport {isBrowser} from '@loaders.gl/worker-utils';\nimport {VERSION} from './lib/utils/version';\nimport parseBasis from './lib/parsers/parse-basis';\n\n/**\n * Worker loader for Basis super compressed textures\n */\nexport const BasisWorkerLoader = {\n  name: 'Basis',\n  id: isBrowser ? 'basis' : 'basis-nodejs',\n  module: 'textures',\n  version: VERSION,\n  worker: true,\n  extensions: ['basis', 'ktx2'],\n  mimeTypes: ['application/octet-stream', 'image/ktx2'],\n  tests: ['sB'],\n  binary: true,\n  options: {\n    basis: {\n      format: 'auto', // gl context doesn't exist on a worker thread\n      libraryPath: 'libs/',\n      containerFormat: 'auto', // 'basis' || 'ktx2' || 'auto'\n      module: 'transcoder' // 'transcoder' || 'encoder'\n    }\n  }\n};\n\n/**\n * Loader for Basis super compressed textures\n */\nexport const BasisLoader = {\n  ...BasisWorkerLoader,\n  parse: parseBasis\n};\n\n// TYPE TESTS - TODO find a better way than exporting junk\nexport const _TypecheckBasisWorkerLoader: Loader = BasisWorkerLoader;\nexport const _TypecheckBasisLoader: LoaderWithParser = BasisLoader;\n"], "mappings": "AACA,SAAQA,SAAS,QAAO,0BAA0B;AAClD,SAAQC,OAAO,QAAO,qBAAqB;AAC3C,OAAOC,UAAU,MAAM,2BAA2B;AAKlD,OAAO,MAAMC,iBAAiB,GAAG;EAC/BC,IAAI,EAAE,OAAO;EACbC,EAAE,EAAEL,SAAS,GAAG,OAAO,GAAG,cAAc;EACxCM,MAAM,EAAE,UAAU;EAClBC,OAAO,EAAEN,OAAO;EAChBO,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;EAC7BC,SAAS,EAAE,CAAC,0BAA0B,EAAE,YAAY,CAAC;EACrDC,KAAK,EAAE,CAAC,IAAI,CAAC;EACbC,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE;IACPC,KAAK,EAAE;MACLC,MAAM,EAAE,MAAM;MACdC,WAAW,EAAE,OAAO;MACpBC,eAAe,EAAE,MAAM;MACvBX,MAAM,EAAE;IACV;EACF;AACF,CAAC;AAKD,OAAO,MAAMY,WAAW,GAAG;EACzB,GAAGf,iBAAiB;EACpBgB,KAAK,EAAEjB;AACT,CAAC;AAGD,OAAO,MAAMkB,2BAAmC,GAAGjB,iBAAiB;AACpE,OAAO,MAAMkB,qBAAuC,GAAGH,WAAW"}