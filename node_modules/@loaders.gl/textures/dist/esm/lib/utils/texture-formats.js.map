{"version": 3, "file": "texture-formats.js", "names": ["BROWSER_PREFIXES", "WEBGL_EXTENSIONS", "WEBGL_compressed_texture_s3tc", "WEBGL_compressed_texture_s3tc_srgb", "WEBGL_compressed_texture_etc1", "WEBGL_compressed_texture_etc", "WEBGL_compressed_texture_pvrtc", "WEBGL_compressed_texture_atc", "WEBGL_compressed_texture_astc", "EXT_texture_compression_rgtc", "formats", "getSupportedGPUTextureFormats", "gl", "getWebGLContext", "undefined", "Set", "prefix", "extension", "getExtension", "concat", "gpuTextureFormat", "add", "canvas", "document", "createElement", "getContext", "error"], "sources": ["../../../../src/lib/utils/texture-formats.ts"], "sourcesContent": ["import type {GPUTextureFormat} from '@loaders.gl/schema';\n\nconst BROWSER_PREFIXES = ['', 'WEBKIT_', 'MOZ_'];\n\nconst WEBGL_EXTENSIONS: {[key: string]: GPUTextureFormat} = {\n  /* eslint-disable camelcase */\n  WEBGL_compressed_texture_s3tc: 'dxt',\n  WEBGL_compressed_texture_s3tc_srgb: 'dxt-srgb',\n  WEBGL_compressed_texture_etc1: 'etc1',\n  WEBGL_compressed_texture_etc: 'etc2',\n  WEBGL_compressed_texture_pvrtc: 'pvrtc',\n  WEBGL_compressed_texture_atc: 'atc',\n  WEBGL_compressed_texture_astc: 'astc',\n  EXT_texture_compression_rgtc: 'rgtc'\n  /* eslint-enable camelcase */\n};\n\nlet formats: Set<GPUTextureFormat> | null = null;\n\n/**\n * Returns a list of formats.\n * Creates a temporary WebGLRenderingContext if none is provided.\n *\n * @param gl - Optional context.\n */\nexport function getSupportedGPUTextureFormats(gl?: WebGLRenderingContext): Set<string> {\n  if (!formats) {\n    gl = gl || getWebGLContext() || undefined;\n\n    formats = new Set<GPUTextureFormat>();\n\n    for (const prefix of BROWSER_PREFIXES) {\n      for (const extension in WEBGL_EXTENSIONS) {\n        if (gl && gl.getExtension(`${prefix}${extension}`)) {\n          const gpuTextureFormat = WEBGL_EXTENSIONS[extension];\n          formats.add(gpuTextureFormat);\n        }\n      }\n    }\n  }\n\n  return formats;\n}\n\n/**\n * @returns {WebGLRenderingContext?}\n */\nfunction getWebGLContext() {\n  try {\n    const canvas = document.createElement('canvas');\n    return canvas.getContext('webgl');\n  } catch (error) {\n    return null;\n  }\n}\n"], "mappings": "AAEA,MAAMA,gBAAgB,GAAG,CAAC,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC;AAEhD,MAAMC,gBAAmD,GAAG;EAE1DC,6BAA6B,EAAE,KAAK;EACpCC,kCAAkC,EAAE,UAAU;EAC9CC,6BAA6B,EAAE,MAAM;EACrCC,4BAA4B,EAAE,MAAM;EACpCC,8BAA8B,EAAE,OAAO;EACvCC,4BAA4B,EAAE,KAAK;EACnCC,6BAA6B,EAAE,MAAM;EACrCC,4BAA4B,EAAE;AAEhC,CAAC;AAED,IAAIC,OAAqC,GAAG,IAAI;AAQhD,OAAO,SAASC,6BAA6BA,CAACC,EAA0B,EAAe;EACrF,IAAI,CAACF,OAAO,EAAE;IACZE,EAAE,GAAGA,EAAE,IAAIC,eAAe,CAAC,CAAC,IAAIC,SAAS;IAEzCJ,OAAO,GAAG,IAAIK,GAAG,CAAmB,CAAC;IAErC,KAAK,MAAMC,MAAM,IAAIhB,gBAAgB,EAAE;MACrC,KAAK,MAAMiB,SAAS,IAAIhB,gBAAgB,EAAE;QACxC,IAAIW,EAAE,IAAIA,EAAE,CAACM,YAAY,IAAAC,MAAA,CAAIH,MAAM,EAAAG,MAAA,CAAGF,SAAS,CAAE,CAAC,EAAE;UAClD,MAAMG,gBAAgB,GAAGnB,gBAAgB,CAACgB,SAAS,CAAC;UACpDP,OAAO,CAACW,GAAG,CAACD,gBAAgB,CAAC;QAC/B;MACF;IACF;EACF;EAEA,OAAOV,OAAO;AAChB;AAKA,SAASG,eAAeA,CAAA,EAAG;EACzB,IAAI;IACF,MAAMS,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/C,OAAOF,MAAM,CAACG,UAAU,CAAC,OAAO,CAAC;EACnC,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAO,IAAI;EACb;AACF"}