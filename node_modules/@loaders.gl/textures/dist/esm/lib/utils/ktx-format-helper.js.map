{"version": 3, "file": "ktx-format-helper.js", "names": ["GL_EXTENSIONS_CONSTANTS", "VULKAN_TO_WEBGL_FORMAT_MAP", "COMPRESSED_RGB_S3TC_DXT1_EXT", "COMPRESSED_SRGB_S3TC_DXT1_EXT", "COMPRESSED_RGBA_S3TC_DXT1_EXT", "COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT", "COMPRESSED_RGBA_S3TC_DXT3_EXT", "COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT", "COMPRESSED_RGBA_S3TC_DXT5_EXT", "COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT", "COMPRESSED_RED_RGTC1_EXT", "COMPRESSED_SIGNED_RED_RGTC1_EXT", "COMPRESSED_RED_GREEN_RGTC2_EXT", "COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT", "COMPRESSED_RGB8_ETC2", "COMPRESSED_SRGB8_ETC2", "COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2", "COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2", "COMPRESSED_RGBA8_ETC2_EAC", "COMPRESSED_SRGB8_ALPHA8_ETC2_EAC", "COMPRESSED_R11_EAC", "COMPRESSED_SIGNED_R11_EAC", "COMPRESSED_RG11_EAC", "COMPRESSED_SIGNED_RG11_EAC", "COMPRESSED_RGBA_ASTC_4x4_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR", "COMPRESSED_RGBA_ASTC_5x4_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_5X4_KHR", "COMPRESSED_RGBA_ASTC_5x5_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_5x5_KHR", "COMPRESSED_RGBA_ASTC_6x5_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_6x5_KHR", "COMPRESSED_RGBA_ASTC_6x6_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_6x6_KHR", "COMPRESSED_RGBA_ASTC_8x5_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_8x5_KHR", "COMPRESSED_RGBA_ASTC_8x6_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_8x6_KHR", "COMPRESSED_RGBA_ASTC_8x8_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_8x8_KHR", "COMPRESSED_RGBA_ASTC_10x5_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_10x5_KHR", "COMPRESSED_RGBA_ASTC_10x6_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_10x6_KHR", "COMPRESSED_RGBA_ASTC_10x8_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_10x8_KHR", "COMPRESSED_RGBA_ASTC_10x10_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_10x10_KHR", "COMPRESSED_RGBA_ASTC_12x10_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_12x10_KHR", "COMPRESSED_RGBA_ASTC_12x12_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_12x12_KHR", "COMPRESSED_RGBA_PVRTC_2BPPV1_IMG", "COMPRESSED_RGBA_PVRTC_4BPPV1_IMG", "mapVkFormatToWebGL", "vkFormat"], "sources": ["../../../../src/lib/utils/ktx-format-helper.ts"], "sourcesContent": ["import {GL_EXTENSIONS_CONSTANTS} from '../gl-extensions';\n\nconst VULKAN_TO_WEBGL_FORMAT_MAP: Record<number, number> = {\n  131: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_S3TC_DXT1_EXT,\n  132: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB_S3TC_DXT1_EXT,\n  133: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT1_EXT,\n  134: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT,\n  135: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT3_EXT,\n  136: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT,\n  137: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT5_EXT,\n  138: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT,\n  139: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RED_RGTC1_EXT,\n  140: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SIGNED_RED_RGTC1_EXT,\n  141: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RED_GREEN_RGTC2_EXT,\n  142: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT,\n  147: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB8_ETC2,\n  148: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ETC2,\n  149: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2,\n  150: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2,\n  151: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA8_ETC2_EAC,\n  152: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC,\n  153: GL_EXTENSIONS_CONSTANTS.COMPRESSED_R11_EAC,\n  154: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SIGNED_R11_EAC,\n  155: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RG11_EAC,\n  156: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SIGNED_RG11_EAC,\n  // @ts-ignore\n  157: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_4x4_KHR,\n  // @ts-ignore\n  158: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR,\n  // @ts-ignore\n  159: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_5x4_KHR,\n  // @ts-ignore\n  160: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_5X4_KHR,\n  // @ts-ignore\n  161: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_5x5_KHR,\n  // @ts-ignore\n  162: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_5x5_KHR,\n  // @ts-ignore\n  163: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_6x5_KHR,\n  // @ts-ignore\n  164: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_6x5_KHR,\n  // @ts-ignore\n  165: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_6x6_KHR,\n  // @ts-ignore\n  166: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_6x6_KHR,\n  // @ts-ignore\n  167: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8x5_KHR,\n  // @ts-ignore\n  168: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_8x5_KHR,\n  // @ts-ignore\n  169: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8x6_KHR,\n  // @ts-ignore\n  170: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_8x6_KHR,\n  // @ts-ignore\n  171: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8x8_KHR,\n  // @ts-ignore\n  172: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_8x8_KHR,\n  // @ts-ignore\n  173: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x5_KHR,\n  // @ts-ignore\n  174: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10x5_KHR,\n  // @ts-ignore\n  175: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x6_KHR,\n  // @ts-ignore\n  176: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10x6_KHR,\n  // @ts-ignore\n  177: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x8_KHR,\n  // @ts-ignore\n  178: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10x8_KHR,\n  // @ts-ignore\n  179: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x10_KHR,\n  // @ts-ignore\n  180: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10x10_KHR,\n  // @ts-ignore\n  181: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_12x10_KHR,\n  // @ts-ignore\n  182: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_12x10_KHR,\n  // @ts-ignore\n  183: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_12x12_KHR,\n  // @ts-ignore\n  184: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_12x12_KHR,\n  1000054000: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG,\n  1000054001: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG,\n  // @ts-ignore\n  1000066000: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_4x4_KHR,\n  // @ts-ignore\n  1000066001: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_5x4_KHR,\n  // @ts-ignore\n  1000066002: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_5x5_KHR,\n  // @ts-ignore\n  1000066003: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_6x5_KHR,\n  // @ts-ignore\n  1000066004: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_6x6_KHR,\n  // @ts-ignore\n  1000066005: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8x5_KHR,\n  // @ts-ignore\n  1000066006: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8x6_KHR,\n  // @ts-ignore\n  1000066007: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8x8_KHR,\n  // @ts-ignore\n  1000066008: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x5_KHR,\n  // @ts-ignore\n  1000066009: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x6_KHR,\n  // @ts-ignore\n  1000066010: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x8_KHR,\n  // @ts-ignore\n  1000066011: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x10_KHR,\n  // @ts-ignore\n  1000066012: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_12x10_KHR,\n  // @ts-ignore\n  1000066013: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_12x12_KHR\n};\n\n/**\n * Returns WebGl format based on Vulkan format\n * Vulkan to WebGl format mapping provided here http://github.khronos.org/KTX-Specification/#formatMapping\n * Vulkan name to format number mapping provided here: https://www.khronos.org/registry/vulkan/specs/1.2-extensions/man/html/VkFormat.html\n * @param vkFormat\n * @returns WebGL / OpenGL constant\n */\nexport function mapVkFormatToWebGL(vkFormat: number): number {\n  return VULKAN_TO_WEBGL_FORMAT_MAP[vkFormat];\n}\n"], "mappings": "AAAA,SAAQA,uBAAuB,QAAO,kBAAkB;AAExD,MAAMC,0BAAkD,GAAG;EACzD,GAAG,EAAED,uBAAuB,CAACE,4BAA4B;EACzD,GAAG,EAAEF,uBAAuB,CAACG,6BAA6B;EAC1D,GAAG,EAAEH,uBAAuB,CAACI,6BAA6B;EAC1D,GAAG,EAAEJ,uBAAuB,CAACK,mCAAmC;EAChE,GAAG,EAAEL,uBAAuB,CAACM,6BAA6B;EAC1D,GAAG,EAAEN,uBAAuB,CAACO,mCAAmC;EAChE,GAAG,EAAEP,uBAAuB,CAACQ,6BAA6B;EAC1D,GAAG,EAAER,uBAAuB,CAACS,mCAAmC;EAChE,GAAG,EAAET,uBAAuB,CAACU,wBAAwB;EACrD,GAAG,EAAEV,uBAAuB,CAACW,+BAA+B;EAC5D,GAAG,EAAEX,uBAAuB,CAACY,8BAA8B;EAC3D,GAAG,EAAEZ,uBAAuB,CAACa,qCAAqC;EAClE,GAAG,EAAEb,uBAAuB,CAACc,oBAAoB;EACjD,GAAG,EAAEd,uBAAuB,CAACe,qBAAqB;EAClD,GAAG,EAAEf,uBAAuB,CAACgB,wCAAwC;EACrE,GAAG,EAAEhB,uBAAuB,CAACiB,yCAAyC;EACtE,GAAG,EAAEjB,uBAAuB,CAACkB,yBAAyB;EACtD,GAAG,EAAElB,uBAAuB,CAACmB,gCAAgC;EAC7D,GAAG,EAAEnB,uBAAuB,CAACoB,kBAAkB;EAC/C,GAAG,EAAEpB,uBAAuB,CAACqB,yBAAyB;EACtD,GAAG,EAAErB,uBAAuB,CAACsB,mBAAmB;EAChD,GAAG,EAAEtB,uBAAuB,CAACuB,0BAA0B;EAEvD,GAAG,EAAEvB,uBAAuB,CAACwB,4BAA4B;EAEzD,GAAG,EAAExB,uBAAuB,CAACyB,oCAAoC;EAEjE,GAAG,EAAEzB,uBAAuB,CAAC0B,4BAA4B;EAEzD,GAAG,EAAE1B,uBAAuB,CAAC2B,oCAAoC;EAEjE,GAAG,EAAE3B,uBAAuB,CAAC4B,4BAA4B;EAEzD,GAAG,EAAE5B,uBAAuB,CAAC6B,oCAAoC;EAEjE,GAAG,EAAE7B,uBAAuB,CAAC8B,4BAA4B;EAEzD,GAAG,EAAE9B,uBAAuB,CAAC+B,oCAAoC;EAEjE,GAAG,EAAE/B,uBAAuB,CAACgC,4BAA4B;EAEzD,GAAG,EAAEhC,uBAAuB,CAACiC,oCAAoC;EAEjE,GAAG,EAAEjC,uBAAuB,CAACkC,4BAA4B;EAEzD,GAAG,EAAElC,uBAAuB,CAACmC,oCAAoC;EAEjE,GAAG,EAAEnC,uBAAuB,CAACoC,4BAA4B;EAEzD,GAAG,EAAEpC,uBAAuB,CAACqC,oCAAoC;EAEjE,GAAG,EAAErC,uBAAuB,CAACsC,4BAA4B;EAEzD,GAAG,EAAEtC,uBAAuB,CAACuC,oCAAoC;EAEjE,GAAG,EAAEvC,uBAAuB,CAACwC,6BAA6B;EAE1D,GAAG,EAAExC,uBAAuB,CAACyC,qCAAqC;EAElE,GAAG,EAAEzC,uBAAuB,CAAC0C,6BAA6B;EAE1D,GAAG,EAAE1C,uBAAuB,CAAC2C,qCAAqC;EAElE,GAAG,EAAE3C,uBAAuB,CAAC4C,6BAA6B;EAE1D,GAAG,EAAE5C,uBAAuB,CAAC6C,qCAAqC;EAElE,GAAG,EAAE7C,uBAAuB,CAAC8C,8BAA8B;EAE3D,GAAG,EAAE9C,uBAAuB,CAAC+C,sCAAsC;EAEnE,GAAG,EAAE/C,uBAAuB,CAACgD,8BAA8B;EAE3D,GAAG,EAAEhD,uBAAuB,CAACiD,sCAAsC;EAEnE,GAAG,EAAEjD,uBAAuB,CAACkD,8BAA8B;EAE3D,GAAG,EAAElD,uBAAuB,CAACmD,sCAAsC;EACnE,UAAU,EAAEnD,uBAAuB,CAACoD,gCAAgC;EACpE,UAAU,EAAEpD,uBAAuB,CAACqD,gCAAgC;EAEpE,UAAU,EAAErD,uBAAuB,CAACwB,4BAA4B;EAEhE,UAAU,EAAExB,uBAAuB,CAAC0B,4BAA4B;EAEhE,UAAU,EAAE1B,uBAAuB,CAAC4B,4BAA4B;EAEhE,UAAU,EAAE5B,uBAAuB,CAAC8B,4BAA4B;EAEhE,UAAU,EAAE9B,uBAAuB,CAACgC,4BAA4B;EAEhE,UAAU,EAAEhC,uBAAuB,CAACkC,4BAA4B;EAEhE,UAAU,EAAElC,uBAAuB,CAACoC,4BAA4B;EAEhE,UAAU,EAAEpC,uBAAuB,CAACsC,4BAA4B;EAEhE,UAAU,EAAEtC,uBAAuB,CAACwC,6BAA6B;EAEjE,UAAU,EAAExC,uBAAuB,CAAC0C,6BAA6B;EAEjE,UAAU,EAAE1C,uBAAuB,CAAC4C,6BAA6B;EAEjE,UAAU,EAAE5C,uBAAuB,CAAC8C,8BAA8B;EAElE,UAAU,EAAE9C,uBAAuB,CAACgD,8BAA8B;EAElE,UAAU,EAAEhD,uBAAuB,CAACkD;AACtC,CAAC;AASD,OAAO,SAASI,kBAAkBA,CAACC,QAAgB,EAAU;EAC3D,OAAOtD,0BAA0B,CAACsD,QAAQ,CAAC;AAC7C"}