{"version": 3, "file": "extract-mipmap-images.js", "names": ["extractMipmapImages", "data", "options", "images", "Array", "mipMapLevels", "levelWidth", "width", "levelHeight", "height", "offset", "i", "levelSize", "getLevelSize", "levelData", "getLevelData", "compressed", "format", "internalFormat", "Math", "max", "index", "isArray", "Uint8Array", "buffer", "byteOffset", "sizeFunction"], "sources": ["../../../../src/lib/utils/extract-mipmap-images.ts"], "sourcesContent": ["import type {TextureLevel} from '@loaders.gl/schema';\n\nexport type CompressedTextureExtractOptions = {\n  mipMapLevels: number;\n  width: number;\n  height: number;\n  sizeFunction: Function;\n  internalFormat: number;\n};\n\n/**\n * Extract mipmap images from compressed texture buffer\n * @param data - binary data of compressed texture or Array of level objects\n * @param options.mipMapLevels - number of mipmap level inside image\n * @param options.width - width of 0 - level\n * @param options.height - height of 0 - level\n * @param options.sizeFunction - format-related function to calculate level size in bytes\n * @param options.internalFormat - WebGL compatible format code\n * @returns Array of the texture levels\n */\nexport function extractMipmapImages(\n  data: Uint8Array | object[],\n  options: CompressedTextureExtractOptions\n): TextureLevel[] {\n  const images = new Array(options.mipMapLevels);\n\n  let levelWidth = options.width;\n  let levelHeight = options.height;\n  let offset = 0;\n\n  for (let i = 0; i < options.mipMapLevels; ++i) {\n    // @ts-expect-error\n    const levelSize = getLevelSize(options, levelWidth, levelHeight, data, i);\n    // @ts-expect-error\n    const levelData = getLevelData(data, i, offset, levelSize);\n\n    images[i] = {\n      compressed: true,\n      format: options.internalFormat,\n      data: levelData,\n      width: levelWidth,\n      height: levelHeight,\n      levelSize\n    };\n\n    levelWidth = Math.max(1, levelWidth >> 1);\n    levelHeight = Math.max(1, levelHeight >> 1);\n\n    offset += levelSize;\n  }\n  return images;\n}\n\nfunction getLevelData(\n  data: Uint8Array,\n  index: number,\n  offset: number,\n  levelSize: number\n): Uint8Array {\n  if (!Array.isArray(data)) {\n    return new Uint8Array(data.buffer, data.byteOffset + offset, levelSize);\n  }\n\n  return data[index].levelData;\n}\n\nfunction getLevelSize(\n  options: CompressedTextureExtractOptions,\n  levelWidth: number,\n  levelHeight: number,\n  data: Uint8Array[] | object[],\n  index: number\n): number {\n  if (!Array.isArray(data)) {\n    return options.sizeFunction(levelWidth, levelHeight);\n  }\n  return options.sizeFunction(data[index]);\n}\n"], "mappings": "AAoBA,OAAO,SAASA,mBAAmBA,CACjCC,IAA2B,EAC3BC,OAAwC,EACxB;EAChB,MAAMC,MAAM,GAAG,IAAIC,KAAK,CAACF,OAAO,CAACG,YAAY,CAAC;EAE9C,IAAIC,UAAU,GAAGJ,OAAO,CAACK,KAAK;EAC9B,IAAIC,WAAW,GAAGN,OAAO,CAACO,MAAM;EAChC,IAAIC,MAAM,GAAG,CAAC;EAEd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,OAAO,CAACG,YAAY,EAAE,EAAEM,CAAC,EAAE;IAE7C,MAAMC,SAAS,GAAGC,YAAY,CAACX,OAAO,EAAEI,UAAU,EAAEE,WAAW,EAAEP,IAAI,EAAEU,CAAC,CAAC;IAEzE,MAAMG,SAAS,GAAGC,YAAY,CAACd,IAAI,EAAEU,CAAC,EAAED,MAAM,EAAEE,SAAS,CAAC;IAE1DT,MAAM,CAACQ,CAAC,CAAC,GAAG;MACVK,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAEf,OAAO,CAACgB,cAAc;MAC9BjB,IAAI,EAAEa,SAAS;MACfP,KAAK,EAAED,UAAU;MACjBG,MAAM,EAAED,WAAW;MACnBI;IACF,CAAC;IAEDN,UAAU,GAAGa,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEd,UAAU,IAAI,CAAC,CAAC;IACzCE,WAAW,GAAGW,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEZ,WAAW,IAAI,CAAC,CAAC;IAE3CE,MAAM,IAAIE,SAAS;EACrB;EACA,OAAOT,MAAM;AACf;AAEA,SAASY,YAAYA,CACnBd,IAAgB,EAChBoB,KAAa,EACbX,MAAc,EACdE,SAAiB,EACL;EACZ,IAAI,CAACR,KAAK,CAACkB,OAAO,CAACrB,IAAI,CAAC,EAAE;IACxB,OAAO,IAAIsB,UAAU,CAACtB,IAAI,CAACuB,MAAM,EAAEvB,IAAI,CAACwB,UAAU,GAAGf,MAAM,EAAEE,SAAS,CAAC;EACzE;EAEA,OAAOX,IAAI,CAACoB,KAAK,CAAC,CAACP,SAAS;AAC9B;AAEA,SAASD,YAAYA,CACnBX,OAAwC,EACxCI,UAAkB,EAClBE,WAAmB,EACnBP,IAA6B,EAC7BoB,KAAa,EACL;EACR,IAAI,CAACjB,KAAK,CAACkB,OAAO,CAACrB,IAAI,CAAC,EAAE;IACxB,OAAOC,OAAO,CAACwB,YAAY,CAACpB,UAAU,EAAEE,WAAW,CAAC;EACtD;EACA,OAAON,OAAO,CAACwB,YAAY,CAACzB,IAAI,CAACoB,KAAK,CAAC,CAAC;AAC1C"}