{"version": 3, "file": "load-image-array.js", "names": ["ImageLoader", "getImageUrls", "deepLoad", "loadImageTextureArray", "count", "getUrl", "options", "arguments", "length", "undefined", "imageUrls", "getImageArrayUrls", "parse", "promises", "index", "promise", "push", "Promise", "all"], "sources": ["../../../../src/lib/texture-api/load-image-array.ts"], "sourcesContent": ["// loaders.gl, MIT license\nimport {ImageLoader} from '@loaders.gl/images';\nimport type {GetUrl} from './texture-api-types';\nimport {getImageUrls} from './load-image';\nimport {deepLoad} from './deep-load';\n\nexport async function loadImageTextureArray(\n  count: number,\n  getUrl: GetUrl,\n  options = {}\n): Promise<any> {\n  const imageUrls = await getImageArrayUrls(count, getUrl, options);\n  return await deepLoad(imageUrls, ImageLoader.parse, options);\n}\n\nexport async function getImageArrayUrls(count: number, getUrl: GetUrl, options = {}): Promise<any> {\n  const promises: Promise<any>[] = [];\n  for (let index = 0; index < count; index++) {\n    const promise = getImageUrls(getUrl, options, {index});\n    promises.push(promise);\n  }\n  return await Promise.all(promises);\n}\n"], "mappings": "AACA,SAAQA,WAAW,QAAO,oBAAoB;AAE9C,SAAQC,YAAY,QAAO,cAAc;AACzC,SAAQC,QAAQ,QAAO,aAAa;AAEpC,OAAO,eAAeC,qBAAqBA,CACzCC,KAAa,EACbC,MAAc,EAEA;EAAA,IADdC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAEZ,MAAMG,SAAS,GAAG,MAAMC,iBAAiB,CAACP,KAAK,EAAEC,MAAM,EAAEC,OAAO,CAAC;EACjE,OAAO,MAAMJ,QAAQ,CAACQ,SAAS,EAAEV,WAAW,CAACY,KAAK,EAAEN,OAAO,CAAC;AAC9D;AAEA,OAAO,eAAeK,iBAAiBA,CAACP,KAAa,EAAEC,MAAc,EAA8B;EAAA,IAA5BC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACjF,MAAMM,QAAwB,GAAG,EAAE;EACnC,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGV,KAAK,EAAEU,KAAK,EAAE,EAAE;IAC1C,MAAMC,OAAO,GAAGd,YAAY,CAACI,MAAM,EAAEC,OAAO,EAAE;MAACQ;IAAK,CAAC,CAAC;IACtDD,QAAQ,CAACG,IAAI,CAACD,OAAO,CAAC;EACxB;EACA,OAAO,MAAME,OAAO,CAACC,GAAG,CAACL,QAAQ,CAAC;AACpC"}