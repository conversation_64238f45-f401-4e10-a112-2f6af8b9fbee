{"version": 3, "file": "async-deep-map.js", "names": ["isObject", "value", "asyncDeepMap", "tree", "func", "options", "arguments", "length", "undefined", "mapSubtree", "object", "Array", "isArray", "mapArray", "mapObject", "url", "promises", "values", "key", "promise", "then", "push", "Promise", "all", "urlArray", "map"], "sources": ["../../../../src/lib/texture-api/async-deep-map.ts"], "sourcesContent": ["// loaders.gl, MIT license\n/*\nAsynchronously maps a deep structure of values (e.g. objects and arrays of urls).\n\nE.g. a mipmapped cubemap\n{\n  [CUBE_FACE_FRONT]: [\n    \"image-front-0.jpg\",\n    \"image-front-1.jpg\",\n    \"image-front-2.jpg\",\n  ],\n  [CUBE_MAP_BACK]: [\n    ...\n  ]\n}\n*/\nexport type Options = Record<string, any>;\nexport type Func = (url: string, options: Options) => unknown;\n\nconst isObject = (value: any): boolean => value && typeof value === 'object';\n\n// Loads a deep structure of urls (objects and arrays of urls)\n// Returns an object with six key-value pairs containing the images (or image mip arrays)\n// for each cube face\nexport async function asyncDeepMap(tree: unknown, func: Func, options: Options = {}) {\n  return await mapSubtree(tree, func, options);\n}\n\nexport async function mapSubtree(object: unknown, func: Func, options: Options) {\n  if (Array.isArray(object)) {\n    return await mapArray(object, func, options);\n  }\n\n  if (isObject(object)) {\n    return await mapObject(object as object, func, options);\n  }\n\n  // TODO - ignore non-urls, non-arraybuffers?\n  const url = object as string;\n  return await func(url, options);\n}\n\n// HELPERS\n\nasync function mapObject(\n  object: Record<string, any>,\n  func: Func,\n  options: Options\n): Promise<Record<string, any>> {\n  const promises: Promise<any>[] = [];\n  const values: Record<string, any> = {};\n\n  for (const key in object) {\n    const url = object[key];\n    const promise = mapSubtree(url, func, options).then((value) => {\n      values[key] = value;\n    });\n    promises.push(promise);\n  }\n\n  await Promise.all(promises);\n\n  return values;\n}\n\nasync function mapArray(urlArray: string[], func: Func, options = {}): Promise<any[]> {\n  const promises = urlArray.map((url) => mapSubtree(url, func, options));\n  return await Promise.all(promises);\n}\n"], "mappings": "AAmBA,MAAMA,QAAQ,GAAIC,KAAU,IAAcA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ;AAK5E,OAAO,eAAeC,YAAYA,CAACC,IAAa,EAAEC,IAAU,EAAyB;EAAA,IAAvBC,OAAgB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACjF,OAAO,MAAMG,UAAU,CAACN,IAAI,EAAEC,IAAI,EAAEC,OAAO,CAAC;AAC9C;AAEA,OAAO,eAAeI,UAAUA,CAACC,MAAe,EAAEN,IAAU,EAAEC,OAAgB,EAAE;EAC9E,IAAIM,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;IACzB,OAAO,MAAMG,QAAQ,CAACH,MAAM,EAAEN,IAAI,EAAEC,OAAO,CAAC;EAC9C;EAEA,IAAIL,QAAQ,CAACU,MAAM,CAAC,EAAE;IACpB,OAAO,MAAMI,SAAS,CAACJ,MAAM,EAAYN,IAAI,EAAEC,OAAO,CAAC;EACzD;EAGA,MAAMU,GAAG,GAAGL,MAAgB;EAC5B,OAAO,MAAMN,IAAI,CAACW,GAAG,EAAEV,OAAO,CAAC;AACjC;AAIA,eAAeS,SAASA,CACtBJ,MAA2B,EAC3BN,IAAU,EACVC,OAAgB,EACc;EAC9B,MAAMW,QAAwB,GAAG,EAAE;EACnC,MAAMC,MAA2B,GAAG,CAAC,CAAC;EAEtC,KAAK,MAAMC,GAAG,IAAIR,MAAM,EAAE;IACxB,MAAMK,GAAG,GAAGL,MAAM,CAACQ,GAAG,CAAC;IACvB,MAAMC,OAAO,GAAGV,UAAU,CAACM,GAAG,EAAEX,IAAI,EAAEC,OAAO,CAAC,CAACe,IAAI,CAAEnB,KAAK,IAAK;MAC7DgB,MAAM,CAACC,GAAG,CAAC,GAAGjB,KAAK;IACrB,CAAC,CAAC;IACFe,QAAQ,CAACK,IAAI,CAACF,OAAO,CAAC;EACxB;EAEA,MAAMG,OAAO,CAACC,GAAG,CAACP,QAAQ,CAAC;EAE3B,OAAOC,MAAM;AACf;AAEA,eAAeJ,QAAQA,CAACW,QAAkB,EAAEpB,IAAU,EAAgC;EAAA,IAA9BC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAClE,MAAMU,QAAQ,GAAGQ,QAAQ,CAACC,GAAG,CAAEV,GAAG,IAAKN,UAAU,CAACM,GAAG,EAAEX,IAAI,EAAEC,OAAO,CAAC,CAAC;EACtE,OAAO,MAAMiB,OAAO,CAACC,GAAG,CAACP,QAAQ,CAAC;AACpC"}