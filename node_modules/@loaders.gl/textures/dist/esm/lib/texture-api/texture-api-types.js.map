{"version": 3, "file": "texture-api-types.js", "names": [], "sources": ["../../../../src/lib/texture-api/texture-api-types.ts"], "sourcesContent": ["// loaders.gl, MIT license\n\nexport type {ImageType} from '@loaders.gl/images';\n\nexport type UrlOptions = {\n  baseUrl?: string;\n  index?: number;\n  face?: number;\n  lod?: number;\n  direction?: string;\n};\nexport type GetUrl = (options: UrlOptions) => string;\n"], "mappings": ""}