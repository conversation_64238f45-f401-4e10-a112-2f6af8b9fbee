{"version": 3, "file": "generate-url.js", "names": ["<PERSON><PERSON><PERSON>", "generateUrl", "getUrl", "options", "urlOptions", "url", "baseUrl", "length", "concat"], "sources": ["../../../../src/lib/texture-api/generate-url.ts"], "sourcesContent": ["// loaders.gl, MIT license\nimport {resolvePath} from '@loaders.gl/loader-utils';\nimport type {GetUrl, UrlOptions} from './texture-api-types';\n\n// Generate a url by calling getUrl with mix of options, applying options.baseUrl\nexport function generateUrl(\n  getUrl: string | GetUrl,\n  options: UrlOptions,\n  urlOptions: Record<string, any>\n): string {\n  // Get url\n  let url = typeof getUrl === 'function' ? getUrl({...options, ...urlOptions}) : getUrl;\n\n  // Apply options.baseUrl\n  const baseUrl = options.baseUrl;\n  if (baseUrl) {\n    url = baseUrl[baseUrl.length - 1] === '/' ? `${baseUrl}${url}` : `${baseUrl}/${url}`;\n  }\n\n  return resolvePath(url);\n}\n"], "mappings": "AACA,SAAQA,WAAW,QAAO,0BAA0B;AAIpD,OAAO,SAASC,WAAWA,CACzBC,MAAuB,EACvBC,OAAmB,EACnBC,UAA+B,EACvB;EAER,IAAIC,GAAG,GAAG,OAAOH,MAAM,KAAK,UAAU,GAAGA,MAAM,CAAC;IAAC,GAAGC,OAAO;IAAE,GAAGC;EAAU,CAAC,CAAC,GAAGF,MAAM;EAGrF,MAAMI,OAAO,GAAGH,OAAO,CAACG,OAAO;EAC/B,IAAIA,OAAO,EAAE;IACXD,GAAG,GAAGC,OAAO,CAACA,OAAO,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,MAAAC,MAAA,CAAMF,OAAO,EAAAE,MAAA,CAAGH,GAAG,OAAAG,MAAA,CAAQF,OAAO,OAAAE,MAAA,CAAIH,GAAG,CAAE;EACtF;EAEA,OAAOL,WAAW,CAACK,GAAG,CAAC;AACzB"}