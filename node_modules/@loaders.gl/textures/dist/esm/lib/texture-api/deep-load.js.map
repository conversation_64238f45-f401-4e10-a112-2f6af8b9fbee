{"version": 3, "file": "deep-load.js", "names": ["asyncDeepMap", "deepLoad", "urlTree", "load", "options", "url", "shallowLoad", "response", "fetch", "arrayBuffer"], "sources": ["../../../../src/lib/texture-api/deep-load.ts"], "sourcesContent": ["// loaders.gl, MIT license\nimport {asyncDeepMap} from './async-deep-map';\n\nexport type LoadOptions = Record<string, any>;\nexport type Load = (data: ArrayBuffer, options: Record<string, any>) => Promise<any>;\n\nexport async function deepLoad(urlTree: unknown, load: Load, options: LoadOptions) {\n  return await asyncDeepMap(urlTree, (url: string) => shallowLoad(url, load, options));\n}\n\nexport async function shallowLoad(url: string, load: Load, options: LoadOptions): Promise<any> {\n  // console.error('loading', url);\n  const response = await fetch(url, options.fetch);\n  const arrayBuffer = await response.arrayBuffer();\n  return await load(arrayBuffer, options);\n}\n"], "mappings": "AACA,SAAQA,YAAY,QAAO,kBAAkB;AAK7C,OAAO,eAAeC,QAAQA,CAACC,OAAgB,EAAEC,IAAU,EAAEC,OAAoB,EAAE;EACjF,OAAO,MAAMJ,YAAY,CAACE,OAAO,EAAGG,GAAW,IAAKC,WAAW,CAACD,GAAG,EAAEF,IAAI,EAAEC,OAAO,CAAC,CAAC;AACtF;AAEA,OAAO,eAAeE,WAAWA,CAACD,GAAW,EAAEF,IAAU,EAAEC,OAAoB,EAAgB;EAE7F,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAACH,GAAG,EAAED,OAAO,CAACI,KAAK,CAAC;EAChD,MAAMC,WAAW,GAAG,MAAMF,QAAQ,CAACE,WAAW,CAAC,CAAC;EAChD,OAAO,MAAMN,IAAI,CAACM,WAAW,EAAEL,OAAO,CAAC;AACzC"}