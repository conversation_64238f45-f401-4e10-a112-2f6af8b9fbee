{"version": 3, "file": "load-image.js", "names": ["assert", "ImageLoader", "getImageSize", "generateUrl", "deepLoad", "shallowLoad", "loadImageTexture", "getUrl", "options", "arguments", "length", "undefined", "imageUrls", "getImageUrls", "parse", "urlOptions", "mipLevels", "image", "getMipmappedImageUrls", "urls", "url", "lod", "width", "height", "getMipLevels", "push", "mipLevel", "size", "Math", "floor", "log2", "max"], "sources": ["../../../../src/lib/texture-api/load-image.ts"], "sourcesContent": ["import {assert} from '@loaders.gl/loader-utils';\nimport {ImageLoader, getImageSize} from '@loaders.gl/images';\nimport type {GetUrl, UrlOptions} from './texture-api-types';\nimport {generateUrl} from './generate-url';\nimport {deepLoad, shallowLoad} from './deep-load';\n\nexport async function loadImageTexture(getUrl: string | GetUrl, options = {}): Promise<any> {\n  const imageUrls = await getImageUrls(getUrl, options);\n  return await deepLoad(imageUrls, ImageLoader.parse, options);\n}\n\nexport async function getImageUrls(\n  getUrl: string | GetUrl,\n  options: any,\n  urlOptions: UrlOptions = {}\n): Promise<any> {\n  const mipLevels = (options && options.image && options.image.mipLevels) || 0;\n  return mipLevels !== 0\n    ? await getMipmappedImageUrls(getUrl, mipLevels, options, urlOptions)\n    : generateUrl(getUrl, options, urlOptions);\n}\n\nasync function getMipmappedImageUrls(\n  getUrl: string | GetUrl,\n  mipLevels: number | 'auto',\n  options: any,\n  urlOptions: UrlOptions\n): Promise<string[]> {\n  const urls: string[] = [];\n\n  // If no mip levels supplied, we need to load the level 0 image and calculate based on size\n  if (mipLevels === 'auto') {\n    const url = generateUrl(getUrl, options, {...urlOptions, lod: 0});\n    const image = await shallowLoad(url, ImageLoader.parse, options);\n\n    const {width, height} = getImageSize(image);\n    mipLevels = getMipLevels({width, height});\n\n    // TODO - push image and make `deepLoad` pass through non-url values, avoid loading twice?\n    urls.push(url);\n  }\n\n  // We now know how many mipLevels we need, remaining image urls can now be constructed\n  assert(mipLevels > 0);\n\n  for (let mipLevel = urls.length; mipLevel < mipLevels; ++mipLevel) {\n    const url = generateUrl(getUrl, options, {...urlOptions, lod: mipLevel});\n    urls.push(url);\n  }\n\n  return urls;\n}\n\n// Calculates number of mipmaps based on texture size (log2)\nexport function getMipLevels(size: {width: number; height: number}): number {\n  return 1 + Math.floor(Math.log2(Math.max(size.width, size.height)));\n}\n"], "mappings": "AAAA,SAAQA,MAAM,QAAO,0BAA0B;AAC/C,SAAQC,WAAW,EAAEC,YAAY,QAAO,oBAAoB;AAE5D,SAAQC,WAAW,QAAO,gBAAgB;AAC1C,SAAQC,QAAQ,EAAEC,WAAW,QAAO,aAAa;AAEjD,OAAO,eAAeC,gBAAgBA,CAACC,MAAuB,EAA8B;EAAA,IAA5BC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC1E,MAAMG,SAAS,GAAG,MAAMC,YAAY,CAACN,MAAM,EAAEC,OAAO,CAAC;EACrD,OAAO,MAAMJ,QAAQ,CAACQ,SAAS,EAAEX,WAAW,CAACa,KAAK,EAAEN,OAAO,CAAC;AAC9D;AAEA,OAAO,eAAeK,YAAYA,CAChCN,MAAuB,EACvBC,OAAY,EAEE;EAAA,IADdO,UAAsB,GAAAN,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAE3B,MAAMO,SAAS,GAAIR,OAAO,IAAIA,OAAO,CAACS,KAAK,IAAIT,OAAO,CAACS,KAAK,CAACD,SAAS,IAAK,CAAC;EAC5E,OAAOA,SAAS,KAAK,CAAC,GAClB,MAAME,qBAAqB,CAACX,MAAM,EAAES,SAAS,EAAER,OAAO,EAAEO,UAAU,CAAC,GACnEZ,WAAW,CAACI,MAAM,EAAEC,OAAO,EAAEO,UAAU,CAAC;AAC9C;AAEA,eAAeG,qBAAqBA,CAClCX,MAAuB,EACvBS,SAA0B,EAC1BR,OAAY,EACZO,UAAsB,EACH;EACnB,MAAMI,IAAc,GAAG,EAAE;EAGzB,IAAIH,SAAS,KAAK,MAAM,EAAE;IACxB,MAAMI,GAAG,GAAGjB,WAAW,CAACI,MAAM,EAAEC,OAAO,EAAE;MAAC,GAAGO,UAAU;MAAEM,GAAG,EAAE;IAAC,CAAC,CAAC;IACjE,MAAMJ,KAAK,GAAG,MAAMZ,WAAW,CAACe,GAAG,EAAEnB,WAAW,CAACa,KAAK,EAAEN,OAAO,CAAC;IAEhE,MAAM;MAACc,KAAK;MAAEC;IAAM,CAAC,GAAGrB,YAAY,CAACe,KAAK,CAAC;IAC3CD,SAAS,GAAGQ,YAAY,CAAC;MAACF,KAAK;MAAEC;IAAM,CAAC,CAAC;IAGzCJ,IAAI,CAACM,IAAI,CAACL,GAAG,CAAC;EAChB;EAGApB,MAAM,CAACgB,SAAS,GAAG,CAAC,CAAC;EAErB,KAAK,IAAIU,QAAQ,GAAGP,IAAI,CAACT,MAAM,EAAEgB,QAAQ,GAAGV,SAAS,EAAE,EAAEU,QAAQ,EAAE;IACjE,MAAMN,GAAG,GAAGjB,WAAW,CAACI,MAAM,EAAEC,OAAO,EAAE;MAAC,GAAGO,UAAU;MAAEM,GAAG,EAAEK;IAAQ,CAAC,CAAC;IACxEP,IAAI,CAACM,IAAI,CAACL,GAAG,CAAC;EAChB;EAEA,OAAOD,IAAI;AACb;AAGA,OAAO,SAASK,YAAYA,CAACG,IAAqC,EAAU;EAC1E,OAAO,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,IAAI,CAACF,IAAI,CAACG,GAAG,CAACJ,IAAI,CAACL,KAAK,EAAEK,IAAI,CAACJ,MAAM,CAAC,CAAC,CAAC;AACrE"}