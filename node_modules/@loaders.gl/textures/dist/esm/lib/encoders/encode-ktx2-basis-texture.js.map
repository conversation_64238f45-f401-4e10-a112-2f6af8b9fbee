{"version": 3, "file": "encode-ktx2-basis-texture.js", "names": ["loadBasisEncoderModule", "encodeKTX2BasisTexture", "image", "options", "arguments", "length", "undefined", "useSRGB", "qualityLevel", "encodeUASTC", "mipmaps", "Basis<PERSON><PERSON>der", "basisEncoder", "basisFileData", "Uint8Array", "width", "height", "setCreateKTX2File", "setKTX2UASTCSupercompression", "setKTX2SRGBTransferFunc", "setSliceSourceImage", "data", "setPerceptual", "setMipSRGB", "setQualityLevel", "setUASTC", "setMipGen", "numOutputBytes", "encode", "actualKTX2FileData", "subarray", "buffer", "error", "console", "delete"], "sources": ["../../../../src/lib/encoders/encode-ktx2-basis-texture.ts"], "sourcesContent": ["import type {ImageDataType} from '@loaders.gl/images';\nimport {loadBasisEncoderModule} from '../parsers/basis-module-loader';\n\n/**\n * Encodes image to Basis Universal Supercompressed GPU Texture.\n * Code example is taken from here - https://github.com/BinomialLLC/basis_universal/blob/master/webgl/ktx2_encode_test/index.html#L279\n * BasisEncoder API - https://github.com/BinomialLLC/basis_universal/blob/master/webgl/transcoder/basis_wrappers.cpp#L1712\n * @param image\n * @param options\n */\nexport async function encodeKTX2BasisTexture(\n  image: ImageDataType,\n  options: any = {}\n): Promise<ArrayBuffer> {\n  // TODO remove default values after writer options will be normalized like it done in load module.\n  const {useSRGB = false, qualityLevel = 10, encodeUASTC = false, mipmaps = false} = options;\n  const {BasisEncoder} = await loadBasisEncoderModule(options);\n  const basisEncoder = new BasisEncoder();\n\n  try {\n    const basisFileData = new Uint8Array(image.width * image.height * 4);\n    basisEncoder.setCreateKTX2File(true);\n    basisEncoder.setKTX2UASTCSupercompression(true);\n    basisEncoder.setKTX2SRGBTransferFunc(true);\n\n    basisEncoder.setSliceSourceImage(0, image.data, image.width, image.height, false);\n    basisEncoder.setPerceptual(useSRGB);\n    basisEncoder.setMipSRGB(useSRGB);\n    basisEncoder.setQualityLevel(qualityLevel);\n    basisEncoder.setUASTC(encodeUASTC);\n    basisEncoder.setMipGen(mipmaps);\n\n    const numOutputBytes = basisEncoder.encode(basisFileData);\n\n    const actualKTX2FileData = basisFileData.subarray(0, numOutputBytes).buffer;\n    return actualKTX2FileData;\n  } catch (error) {\n    // eslint-disable-next-line no-console\n    console.error('Basis Universal Supercompressed GPU Texture encoder Error: ', error);\n    throw error;\n  } finally {\n    basisEncoder.delete();\n  }\n}\n"], "mappings": "AACA,SAAQA,sBAAsB,QAAO,gCAAgC;AASrE,OAAO,eAAeC,sBAAsBA,CAC1CC,KAAoB,EAEE;EAAA,IADtBC,OAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAGjB,MAAM;IAACG,OAAO,GAAG,KAAK;IAAEC,YAAY,GAAG,EAAE;IAAEC,WAAW,GAAG,KAAK;IAAEC,OAAO,GAAG;EAAK,CAAC,GAAGP,OAAO;EAC1F,MAAM;IAACQ;EAAY,CAAC,GAAG,MAAMX,sBAAsB,CAACG,OAAO,CAAC;EAC5D,MAAMS,YAAY,GAAG,IAAID,YAAY,CAAC,CAAC;EAEvC,IAAI;IACF,MAAME,aAAa,GAAG,IAAIC,UAAU,CAACZ,KAAK,CAACa,KAAK,GAAGb,KAAK,CAACc,MAAM,GAAG,CAAC,CAAC;IACpEJ,YAAY,CAACK,iBAAiB,CAAC,IAAI,CAAC;IACpCL,YAAY,CAACM,4BAA4B,CAAC,IAAI,CAAC;IAC/CN,YAAY,CAACO,uBAAuB,CAAC,IAAI,CAAC;IAE1CP,YAAY,CAACQ,mBAAmB,CAAC,CAAC,EAAElB,KAAK,CAACmB,IAAI,EAAEnB,KAAK,CAACa,KAAK,EAAEb,KAAK,CAACc,MAAM,EAAE,KAAK,CAAC;IACjFJ,YAAY,CAACU,aAAa,CAACf,OAAO,CAAC;IACnCK,YAAY,CAACW,UAAU,CAAChB,OAAO,CAAC;IAChCK,YAAY,CAACY,eAAe,CAAChB,YAAY,CAAC;IAC1CI,YAAY,CAACa,QAAQ,CAAChB,WAAW,CAAC;IAClCG,YAAY,CAACc,SAAS,CAAChB,OAAO,CAAC;IAE/B,MAAMiB,cAAc,GAAGf,YAAY,CAACgB,MAAM,CAACf,aAAa,CAAC;IAEzD,MAAMgB,kBAAkB,GAAGhB,aAAa,CAACiB,QAAQ,CAAC,CAAC,EAAEH,cAAc,CAAC,CAACI,MAAM;IAC3E,OAAOF,kBAAkB;EAC3B,CAAC,CAAC,OAAOG,KAAK,EAAE;IAEdC,OAAO,CAACD,KAAK,CAAC,6DAA6D,EAAEA,KAAK,CAAC;IACnF,MAAMA,KAAK;EACb,CAAC,SAAS;IACRpB,YAAY,CAACsB,MAAM,CAAC,CAAC;EACvB;AACF"}