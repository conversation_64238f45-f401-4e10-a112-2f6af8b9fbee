{"version": 3, "file": "encode-ktx.js", "names": ["read", "encodeKTX", "texture", "ktx"], "sources": ["../../../../src/lib/encoders/encode-ktx.ts"], "sourcesContent": ["import {read} from 'ktx-parse';\n\nexport function encodeKTX(texture) {\n  const ktx = read(texture);\n  // post process\n  return ktx;\n}\n"], "mappings": "AAAA,SAAQA,IAAI,QAAO,WAAW;AAE9B,OAAO,SAASC,SAASA,CAACC,OAAO,EAAE;EACjC,MAAMC,GAAG,GAAGH,IAAI,CAACE,OAAO,CAAC;EAEzB,OAAOC,GAAG;AACZ"}