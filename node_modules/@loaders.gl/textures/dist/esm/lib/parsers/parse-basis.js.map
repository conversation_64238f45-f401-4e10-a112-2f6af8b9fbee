{"version": 3, "file": "parse-basis.js", "names": ["loadBasisEncoderModule", "loadBasisTrascoderModule", "GL_EXTENSIONS_CONSTANTS", "getSupportedGPUTextureFormats", "isKTX", "OutputFormat", "etc1", "basisFormat", "compressed", "format", "COMPRESSED_RGB_ETC1_WEBGL", "etc2", "bc1", "COMPRESSED_RGB_S3TC_DXT1_EXT", "bc3", "COMPRESSED_RGBA_S3TC_DXT5_EXT", "bc4", "bc5", "COMPRESSED_RGB_PVRTC_4BPPV1_IMG", "COMPRESSED_RGBA_PVRTC_4BPPV1_IMG", "COMPRESSED_RGBA_ASTC_4X4_KHR", "rgba32", "rgb565", "bgr565", "rgba4444", "parseBasis", "data", "options", "basis", "containerFormat", "fileConstructors", "parseKTX2File", "KTX2File", "BasisFile", "parseBasisFile", "module", "basisFile", "Uint8Array", "startTranscoding", "Error", "imageCount", "getNumImages", "images", "imageIndex", "levelsCount", "getNumLevels", "levels", "levelIndex", "push", "transcodeImage", "close", "delete", "width", "getImageWidth", "height", "getImageHeight", "has<PERSON><PERSON><PERSON>", "getHasAlpha", "getBasisOptions", "decodedSize", "getImageTranscodedSizeInBytes", "decodedData", "ktx2File", "getLevels", "transcodeKTX2Image", "alphaFlag", "getImageLevelInfo", "levelSize", "selectSupportedBasisFormat", "alpha", "noAlpha", "toLowerCase", "supportedFormats", "has"], "sources": ["../../../../src/lib/parsers/parse-basis.ts"], "sourcesContent": ["/* eslint-disable indent */\nimport type {TextureLevel} from '@loaders.gl/schema';\nimport {loadBasisEncoderModule, loadBasisTrascoderModule} from './basis-module-loader';\nimport {GL_EXTENSIONS_CONSTANTS} from '../gl-extensions';\nimport {getSupportedGPUTextureFormats} from '../utils/texture-formats';\nimport {isKTX} from './parse-ktx';\n\nexport type BasisFormat =\n  | 'etc1'\n  | 'etc2'\n  | 'bc1'\n  | 'bc3'\n  | 'bc4'\n  | 'bc5'\n  | 'bc7-m6-opaque-only'\n  | 'bc7-m5'\n  | 'pvrtc1-4-rgb'\n  | 'pvrtc1-4-rgba'\n  | 'astc-4x4'\n  | 'atc-rgb'\n  | 'atc-rgba-interpolated-alpha'\n  | 'rgba32'\n  | 'rgb565'\n  | 'bgr565'\n  | 'rgba4444';\n\ntype BasisOutputOptions = {\n  basisFormat: number;\n  compressed: boolean;\n  format?: number;\n};\n\nconst OutputFormat: Record<string, BasisOutputOptions> = {\n  etc1: {\n    basisFormat: 0,\n    compressed: true,\n    format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_ETC1_WEBGL\n  },\n  etc2: {basisFormat: 1, compressed: true},\n  bc1: {\n    basisFormat: 2,\n    compressed: true,\n    format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_S3TC_DXT1_EXT\n  },\n  bc3: {\n    basisFormat: 3,\n    compressed: true,\n    format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT5_EXT\n  },\n  bc4: {basisFormat: 4, compressed: true},\n  bc5: {basisFormat: 5, compressed: true},\n  'bc7-m6-opaque-only': {basisFormat: 6, compressed: true},\n  'bc7-m5': {basisFormat: 7, compressed: true},\n  'pvrtc1-4-rgb': {\n    basisFormat: 8,\n    compressed: true,\n    format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_PVRTC_4BPPV1_IMG\n  },\n  'pvrtc1-4-rgba': {\n    basisFormat: 9,\n    compressed: true,\n    format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG\n  },\n  'astc-4x4': {\n    basisFormat: 10,\n    compressed: true,\n    format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_4X4_KHR\n  },\n  'atc-rgb': {basisFormat: 11, compressed: true},\n  'atc-rgba-interpolated-alpha': {basisFormat: 12, compressed: true},\n  rgba32: {basisFormat: 13, compressed: false},\n  rgb565: {basisFormat: 14, compressed: false},\n  bgr565: {basisFormat: 15, compressed: false},\n  rgba4444: {basisFormat: 16, compressed: false}\n};\n\n/**\n * parse data with a Binomial Basis_Universal module\n * @param data\n * @param options\n * @returns compressed texture data\n */\nexport default async function parseBasis(data: ArrayBuffer, options): Promise<TextureLevel[][]> {\n  if (options.basis.containerFormat === 'auto') {\n    if (isKTX(data)) {\n      const fileConstructors = await loadBasisEncoderModule(options);\n      return parseKTX2File(fileConstructors.KTX2File, data, options);\n    }\n    const {BasisFile} = await loadBasisTrascoderModule(options);\n    return parseBasisFile(BasisFile, data, options);\n  }\n  switch (options.basis.module) {\n    case 'encoder':\n      const fileConstructors = await loadBasisEncoderModule(options);\n      switch (options.basis.containerFormat) {\n        case 'ktx2':\n          return parseKTX2File(fileConstructors.KTX2File, data, options);\n        case 'basis':\n        default:\n          return parseBasisFile(fileConstructors.BasisFile, data, options);\n      }\n    case 'transcoder':\n    default:\n      const {BasisFile} = await loadBasisTrascoderModule(options);\n      return parseBasisFile(BasisFile, data, options);\n  }\n}\n\n/**\n * Parse *.basis file data\n * @param BasisFile - initialized transcoder module\n * @param data\n * @param options\n * @returns compressed texture data\n */\nfunction parseBasisFile(BasisFile, data, options): TextureLevel[][] {\n  const basisFile = new BasisFile(new Uint8Array(data));\n\n  try {\n    if (!basisFile.startTranscoding()) {\n      throw new Error('Failed to start basis transcoding');\n    }\n\n    const imageCount = basisFile.getNumImages();\n    const images: TextureLevel[][] = [];\n\n    for (let imageIndex = 0; imageIndex < imageCount; imageIndex++) {\n      const levelsCount = basisFile.getNumLevels(imageIndex);\n      const levels: TextureLevel[] = [];\n\n      for (let levelIndex = 0; levelIndex < levelsCount; levelIndex++) {\n        levels.push(transcodeImage(basisFile, imageIndex, levelIndex, options));\n      }\n\n      images.push(levels);\n    }\n\n    return images;\n  } finally {\n    basisFile.close();\n    basisFile.delete();\n  }\n}\n\n/**\n * Parse the particular level image of a basis file\n * @param basisFile\n * @param imageIndex\n * @param levelIndex\n * @param options\n * @returns compressed texture data\n */\nfunction transcodeImage(basisFile, imageIndex, levelIndex, options): TextureLevel {\n  const width = basisFile.getImageWidth(imageIndex, levelIndex);\n  const height = basisFile.getImageHeight(imageIndex, levelIndex);\n\n  // See https://github.com/BinomialLLC/basis_universal/pull/83\n  const hasAlpha = basisFile.getHasAlpha(/* imageIndex, levelIndex */);\n\n  // Check options for output format etc\n  const {compressed, format, basisFormat} = getBasisOptions(options, hasAlpha);\n\n  const decodedSize = basisFile.getImageTranscodedSizeInBytes(imageIndex, levelIndex, basisFormat);\n  const decodedData = new Uint8Array(decodedSize);\n\n  if (!basisFile.transcodeImage(decodedData, imageIndex, levelIndex, basisFormat, 0, 0)) {\n    throw new Error('failed to start Basis transcoding');\n  }\n\n  return {\n    // standard loaders.gl image category payload\n    width,\n    height,\n    data: decodedData,\n    compressed,\n    format,\n\n    // Additional fields\n    // Add levelSize field.\n    hasAlpha\n  };\n}\n\n/**\n * Parse *.ktx2 file data\n * @param KTX2File\n * @param data\n * @param options\n * @returns compressed texture data\n */\nfunction parseKTX2File(KTX2File, data: ArrayBuffer, options): TextureLevel[][] {\n  const ktx2File = new KTX2File(new Uint8Array(data));\n\n  try {\n    if (!ktx2File.startTranscoding()) {\n      throw new Error('failed to start KTX2 transcoding');\n    }\n    const levelsCount = ktx2File.getLevels();\n    const levels: TextureLevel[] = [];\n\n    for (let levelIndex = 0; levelIndex < levelsCount; levelIndex++) {\n      levels.push(transcodeKTX2Image(ktx2File, levelIndex, options));\n      break; // texture app can only show one level for some reason\n    }\n\n    return [levels];\n  } finally {\n    ktx2File.close();\n    ktx2File.delete();\n  }\n}\n\n/**\n * Parse the particular level image of a ktx2 file\n * @param ktx2File\n * @param levelIndex\n * @param options\n * @returns\n */\nfunction transcodeKTX2Image(ktx2File, levelIndex: number, options): TextureLevel {\n  const {alphaFlag, height, width} = ktx2File.getImageLevelInfo(levelIndex, 0, 0);\n\n  // Check options for output format etc\n  const {compressed, format, basisFormat} = getBasisOptions(options, alphaFlag);\n\n  const decodedSize = ktx2File.getImageTranscodedSizeInBytes(\n    levelIndex,\n    0 /* layerIndex */,\n    0 /* faceIndex */,\n    basisFormat\n  );\n  const decodedData = new Uint8Array(decodedSize);\n\n  if (\n    !ktx2File.transcodeImage(\n      decodedData,\n      levelIndex,\n      0 /* layerIndex */,\n      0 /* faceIndex */,\n      basisFormat,\n      0,\n      -1 /* channel0 */,\n      -1 /* channel1 */\n    )\n  ) {\n    throw new Error('Failed to transcode KTX2 image');\n  }\n\n  return {\n    // standard loaders.gl image category payload\n    width,\n    height,\n    data: decodedData,\n    compressed,\n\n    // Additional fields\n    levelSize: decodedSize,\n    hasAlpha: alphaFlag,\n    format\n  };\n}\n\n/**\n * Get BasisFormat by loader format option\n * @param options\n * @param hasAlpha\n * @returns BasisFormat data\n */\nfunction getBasisOptions(options, hasAlpha: boolean): BasisOutputOptions {\n  let format = options && options.basis && options.basis.format;\n  if (format === 'auto') {\n    format = selectSupportedBasisFormat();\n  }\n  if (typeof format === 'object') {\n    format = hasAlpha ? format.alpha : format.noAlpha;\n  }\n  format = format.toLowerCase();\n  return OutputFormat[format];\n}\n\n/**\n * Select transcode format from the list of supported formats\n * @returns key for OutputFormat map\n */\nexport function selectSupportedBasisFormat():\n  | BasisFormat\n  | {\n      alpha: BasisFormat;\n      noAlpha: BasisFormat;\n    } {\n  const supportedFormats = getSupportedGPUTextureFormats();\n  if (supportedFormats.has('astc')) {\n    return 'astc-4x4';\n  } else if (supportedFormats.has('dxt')) {\n    return {\n      alpha: 'bc3',\n      noAlpha: 'bc1'\n    };\n  } else if (supportedFormats.has('pvrtc')) {\n    return {\n      alpha: 'pvrtc1-4-rgba',\n      noAlpha: 'pvrtc1-4-rgb'\n    };\n  } else if (supportedFormats.has('etc1')) {\n    return 'etc1';\n  } else if (supportedFormats.has('etc2')) {\n    return 'etc2';\n  }\n  return 'rgb565';\n}\n"], "mappings": "AAEA,SAAQA,sBAAsB,EAAEC,wBAAwB,QAAO,uBAAuB;AACtF,SAAQC,uBAAuB,QAAO,kBAAkB;AACxD,SAAQC,6BAA6B,QAAO,0BAA0B;AACtE,SAAQC,KAAK,QAAO,aAAa;AA2BjC,MAAMC,YAAgD,GAAG;EACvDC,IAAI,EAAE;IACJC,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAEP,uBAAuB,CAACQ;EAClC,CAAC;EACDC,IAAI,EAAE;IAACJ,WAAW,EAAE,CAAC;IAAEC,UAAU,EAAE;EAAI,CAAC;EACxCI,GAAG,EAAE;IACHL,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAEP,uBAAuB,CAACW;EAClC,CAAC;EACDC,GAAG,EAAE;IACHP,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAEP,uBAAuB,CAACa;EAClC,CAAC;EACDC,GAAG,EAAE;IAACT,WAAW,EAAE,CAAC;IAAEC,UAAU,EAAE;EAAI,CAAC;EACvCS,GAAG,EAAE;IAACV,WAAW,EAAE,CAAC;IAAEC,UAAU,EAAE;EAAI,CAAC;EACvC,oBAAoB,EAAE;IAACD,WAAW,EAAE,CAAC;IAAEC,UAAU,EAAE;EAAI,CAAC;EACxD,QAAQ,EAAE;IAACD,WAAW,EAAE,CAAC;IAAEC,UAAU,EAAE;EAAI,CAAC;EAC5C,cAAc,EAAE;IACdD,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAEP,uBAAuB,CAACgB;EAClC,CAAC;EACD,eAAe,EAAE;IACfX,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAEP,uBAAuB,CAACiB;EAClC,CAAC;EACD,UAAU,EAAE;IACVZ,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAEP,uBAAuB,CAACkB;EAClC,CAAC;EACD,SAAS,EAAE;IAACb,WAAW,EAAE,EAAE;IAAEC,UAAU,EAAE;EAAI,CAAC;EAC9C,6BAA6B,EAAE;IAACD,WAAW,EAAE,EAAE;IAAEC,UAAU,EAAE;EAAI,CAAC;EAClEa,MAAM,EAAE;IAACd,WAAW,EAAE,EAAE;IAAEC,UAAU,EAAE;EAAK,CAAC;EAC5Cc,MAAM,EAAE;IAACf,WAAW,EAAE,EAAE;IAAEC,UAAU,EAAE;EAAK,CAAC;EAC5Ce,MAAM,EAAE;IAAChB,WAAW,EAAE,EAAE;IAAEC,UAAU,EAAE;EAAK,CAAC;EAC5CgB,QAAQ,EAAE;IAACjB,WAAW,EAAE,EAAE;IAAEC,UAAU,EAAE;EAAK;AAC/C,CAAC;AAQD,eAAe,eAAeiB,UAAUA,CAACC,IAAiB,EAAEC,OAAO,EAA6B;EAC9F,IAAIA,OAAO,CAACC,KAAK,CAACC,eAAe,KAAK,MAAM,EAAE;IAC5C,IAAIzB,KAAK,CAACsB,IAAI,CAAC,EAAE;MACf,MAAMI,gBAAgB,GAAG,MAAM9B,sBAAsB,CAAC2B,OAAO,CAAC;MAC9D,OAAOI,aAAa,CAACD,gBAAgB,CAACE,QAAQ,EAAEN,IAAI,EAAEC,OAAO,CAAC;IAChE;IACA,MAAM;MAACM;IAAS,CAAC,GAAG,MAAMhC,wBAAwB,CAAC0B,OAAO,CAAC;IAC3D,OAAOO,cAAc,CAACD,SAAS,EAAEP,IAAI,EAAEC,OAAO,CAAC;EACjD;EACA,QAAQA,OAAO,CAACC,KAAK,CAACO,MAAM;IAC1B,KAAK,SAAS;MACZ,MAAML,gBAAgB,GAAG,MAAM9B,sBAAsB,CAAC2B,OAAO,CAAC;MAC9D,QAAQA,OAAO,CAACC,KAAK,CAACC,eAAe;QACnC,KAAK,MAAM;UACT,OAAOE,aAAa,CAACD,gBAAgB,CAACE,QAAQ,EAAEN,IAAI,EAAEC,OAAO,CAAC;QAChE,KAAK,OAAO;QACZ;UACE,OAAOO,cAAc,CAACJ,gBAAgB,CAACG,SAAS,EAAEP,IAAI,EAAEC,OAAO,CAAC;MACpE;IACF,KAAK,YAAY;IACjB;MACE,MAAM;QAACM;MAAS,CAAC,GAAG,MAAMhC,wBAAwB,CAAC0B,OAAO,CAAC;MAC3D,OAAOO,cAAc,CAACD,SAAS,EAAEP,IAAI,EAAEC,OAAO,CAAC;EACnD;AACF;AASA,SAASO,cAAcA,CAACD,SAAS,EAAEP,IAAI,EAAEC,OAAO,EAAoB;EAClE,MAAMS,SAAS,GAAG,IAAIH,SAAS,CAAC,IAAII,UAAU,CAACX,IAAI,CAAC,CAAC;EAErD,IAAI;IACF,IAAI,CAACU,SAAS,CAACE,gBAAgB,CAAC,CAAC,EAAE;MACjC,MAAM,IAAIC,KAAK,CAAC,mCAAmC,CAAC;IACtD;IAEA,MAAMC,UAAU,GAAGJ,SAAS,CAACK,YAAY,CAAC,CAAC;IAC3C,MAAMC,MAAwB,GAAG,EAAE;IAEnC,KAAK,IAAIC,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAGH,UAAU,EAAEG,UAAU,EAAE,EAAE;MAC9D,MAAMC,WAAW,GAAGR,SAAS,CAACS,YAAY,CAACF,UAAU,CAAC;MACtD,MAAMG,MAAsB,GAAG,EAAE;MAEjC,KAAK,IAAIC,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAGH,WAAW,EAAEG,UAAU,EAAE,EAAE;QAC/DD,MAAM,CAACE,IAAI,CAACC,cAAc,CAACb,SAAS,EAAEO,UAAU,EAAEI,UAAU,EAAEpB,OAAO,CAAC,CAAC;MACzE;MAEAe,MAAM,CAACM,IAAI,CAACF,MAAM,CAAC;IACrB;IAEA,OAAOJ,MAAM;EACf,CAAC,SAAS;IACRN,SAAS,CAACc,KAAK,CAAC,CAAC;IACjBd,SAAS,CAACe,MAAM,CAAC,CAAC;EACpB;AACF;AAUA,SAASF,cAAcA,CAACb,SAAS,EAAEO,UAAU,EAAEI,UAAU,EAAEpB,OAAO,EAAgB;EAChF,MAAMyB,KAAK,GAAGhB,SAAS,CAACiB,aAAa,CAACV,UAAU,EAAEI,UAAU,CAAC;EAC7D,MAAMO,MAAM,GAAGlB,SAAS,CAACmB,cAAc,CAACZ,UAAU,EAAEI,UAAU,CAAC;EAG/D,MAAMS,QAAQ,GAAGpB,SAAS,CAACqB,WAAW,CAA6B,CAAC;EAGpE,MAAM;IAACjD,UAAU;IAAEC,MAAM;IAAEF;EAAW,CAAC,GAAGmD,eAAe,CAAC/B,OAAO,EAAE6B,QAAQ,CAAC;EAE5E,MAAMG,WAAW,GAAGvB,SAAS,CAACwB,6BAA6B,CAACjB,UAAU,EAAEI,UAAU,EAAExC,WAAW,CAAC;EAChG,MAAMsD,WAAW,GAAG,IAAIxB,UAAU,CAACsB,WAAW,CAAC;EAE/C,IAAI,CAACvB,SAAS,CAACa,cAAc,CAACY,WAAW,EAAElB,UAAU,EAAEI,UAAU,EAAExC,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;IACrF,MAAM,IAAIgC,KAAK,CAAC,mCAAmC,CAAC;EACtD;EAEA,OAAO;IAELa,KAAK;IACLE,MAAM;IACN5B,IAAI,EAAEmC,WAAW;IACjBrD,UAAU;IACVC,MAAM;IAIN+C;EACF,CAAC;AACH;AASA,SAASzB,aAAaA,CAACC,QAAQ,EAAEN,IAAiB,EAAEC,OAAO,EAAoB;EAC7E,MAAMmC,QAAQ,GAAG,IAAI9B,QAAQ,CAAC,IAAIK,UAAU,CAACX,IAAI,CAAC,CAAC;EAEnD,IAAI;IACF,IAAI,CAACoC,QAAQ,CAACxB,gBAAgB,CAAC,CAAC,EAAE;MAChC,MAAM,IAAIC,KAAK,CAAC,kCAAkC,CAAC;IACrD;IACA,MAAMK,WAAW,GAAGkB,QAAQ,CAACC,SAAS,CAAC,CAAC;IACxC,MAAMjB,MAAsB,GAAG,EAAE;IAEjC,KAAK,IAAIC,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAGH,WAAW,EAAEG,UAAU,EAAE,EAAE;MAC/DD,MAAM,CAACE,IAAI,CAACgB,kBAAkB,CAACF,QAAQ,EAAEf,UAAU,EAAEpB,OAAO,CAAC,CAAC;MAC9D;IACF;IAEA,OAAO,CAACmB,MAAM,CAAC;EACjB,CAAC,SAAS;IACRgB,QAAQ,CAACZ,KAAK,CAAC,CAAC;IAChBY,QAAQ,CAACX,MAAM,CAAC,CAAC;EACnB;AACF;AASA,SAASa,kBAAkBA,CAACF,QAAQ,EAAEf,UAAkB,EAAEpB,OAAO,EAAgB;EAC/E,MAAM;IAACsC,SAAS;IAAEX,MAAM;IAAEF;EAAK,CAAC,GAAGU,QAAQ,CAACI,iBAAiB,CAACnB,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;EAG/E,MAAM;IAACvC,UAAU;IAAEC,MAAM;IAAEF;EAAW,CAAC,GAAGmD,eAAe,CAAC/B,OAAO,EAAEsC,SAAS,CAAC;EAE7E,MAAMN,WAAW,GAAGG,QAAQ,CAACF,6BAA6B,CACxDb,UAAU,EACV,CAAC,EACD,CAAC,EACDxC,WACF,CAAC;EACD,MAAMsD,WAAW,GAAG,IAAIxB,UAAU,CAACsB,WAAW,CAAC;EAE/C,IACE,CAACG,QAAQ,CAACb,cAAc,CACtBY,WAAW,EACXd,UAAU,EACV,CAAC,EACD,CAAC,EACDxC,WAAW,EACX,CAAC,EACD,CAAC,CAAC,EACF,CAAC,CACH,CAAC,EACD;IACA,MAAM,IAAIgC,KAAK,CAAC,gCAAgC,CAAC;EACnD;EAEA,OAAO;IAELa,KAAK;IACLE,MAAM;IACN5B,IAAI,EAAEmC,WAAW;IACjBrD,UAAU;IAGV2D,SAAS,EAAER,WAAW;IACtBH,QAAQ,EAAES,SAAS;IACnBxD;EACF,CAAC;AACH;AAQA,SAASiD,eAAeA,CAAC/B,OAAO,EAAE6B,QAAiB,EAAsB;EACvE,IAAI/C,MAAM,GAAGkB,OAAO,IAAIA,OAAO,CAACC,KAAK,IAAID,OAAO,CAACC,KAAK,CAACnB,MAAM;EAC7D,IAAIA,MAAM,KAAK,MAAM,EAAE;IACrBA,MAAM,GAAG2D,0BAA0B,CAAC,CAAC;EACvC;EACA,IAAI,OAAO3D,MAAM,KAAK,QAAQ,EAAE;IAC9BA,MAAM,GAAG+C,QAAQ,GAAG/C,MAAM,CAAC4D,KAAK,GAAG5D,MAAM,CAAC6D,OAAO;EACnD;EACA7D,MAAM,GAAGA,MAAM,CAAC8D,WAAW,CAAC,CAAC;EAC7B,OAAOlE,YAAY,CAACI,MAAM,CAAC;AAC7B;AAMA,OAAO,SAAS2D,0BAA0BA,CAAA,EAKpC;EACJ,MAAMI,gBAAgB,GAAGrE,6BAA6B,CAAC,CAAC;EACxD,IAAIqE,gBAAgB,CAACC,GAAG,CAAC,MAAM,CAAC,EAAE;IAChC,OAAO,UAAU;EACnB,CAAC,MAAM,IAAID,gBAAgB,CAACC,GAAG,CAAC,KAAK,CAAC,EAAE;IACtC,OAAO;MACLJ,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE;IACX,CAAC;EACH,CAAC,MAAM,IAAIE,gBAAgB,CAACC,GAAG,CAAC,OAAO,CAAC,EAAE;IACxC,OAAO;MACLJ,KAAK,EAAE,eAAe;MACtBC,OAAO,EAAE;IACX,CAAC;EACH,CAAC,MAAM,IAAIE,gBAAgB,CAACC,GAAG,CAAC,MAAM,CAAC,EAAE;IACvC,OAAO,MAAM;EACf,CAAC,MAAM,IAAID,gBAAgB,CAACC,GAAG,CAAC,MAAM,CAAC,EAAE;IACvC,OAAO,MAAM;EACf;EACA,OAAO,QAAQ;AACjB"}