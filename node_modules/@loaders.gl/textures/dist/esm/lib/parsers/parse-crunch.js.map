{"version": 3, "file": "parse-crunch.js", "names": ["loadCrunchModule", "GL_EXTENSIONS_CONSTANTS", "assert", "getDxt1LevelSize", "getDxtXLevelSize", "extractMipmapImages", "CRN_FORMAT", "cCRNFmtInvalid", "cCRNFmtDXT1", "cCRNFmtDXT3", "cCRNFmtDXT5", "DXT_FORMAT_MAP", "pixelFormat", "COMPRESSED_RGB_S3TC_DXT1_EXT", "sizeFunction", "COMPRESSED_RGBA_S3TC_DXT3_EXT", "COMPRESSED_RGBA_S3TC_DXT5_EXT", "cachedDstSize", "dst", "parseCrunch", "data", "options", "crunchModule", "srcSize", "byteLength", "bytes", "Uint8Array", "src", "_malloc", "arrayBufferCopy", "HEAPU8", "format", "_crn_get_dxt_format", "Boolean", "mipMapLevels", "_crn_get_levels", "width", "_crn_get_width", "height", "_crn_get_height", "dstSize", "i", "_free", "_crn_decompress", "image", "buffer", "slice", "internalFormat", "srcData", "dstData", "dstByteOffset", "numBytes", "dst32Offset", "tail", "src32", "Uint32Array", "dst32", "length"], "sources": ["../../../../src/lib/parsers/parse-crunch.ts"], "sourcesContent": ["import type {TextureLevel} from '@loaders.gl/schema';\nimport {loadCrunchModule} from './crunch-module-loader';\nimport {GL_EXTENSIONS_CONSTANTS} from '../gl-extensions';\nimport {assert} from '@loaders.gl/loader-utils';\nimport {getDxt1LevelSize, getDxtXLevelSize} from './parse-dds';\nimport {extractMipmapImages} from '../utils/extract-mipmap-images';\n\n// Taken from crnlib.h\nconst CRN_FORMAT = {\n  cCRNFmtInvalid: -1,\n\n  cCRNFmtDXT1: 0,\n  // cCRNFmtDXT3 is not currently supported when writing to CRN - only DDS.\n  cCRNFmtDXT3: 1,\n  cCRNFmtDXT5: 2\n\n  // Crunch supports more formats than this.\n};\n\n/** Mapping of Crunch formats to DXT formats. */\nconst DXT_FORMAT_MAP = {\n  [CRN_FORMAT.cCRNFmtDXT1]: {\n    pixelFormat: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_S3TC_DXT1_EXT,\n    sizeFunction: getDxt1LevelSize\n  },\n  [CRN_FORMAT.cCRNFmtDXT3]: {\n    pixelFormat: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT3_EXT,\n    sizeFunction: getDxtXLevelSize\n  },\n  [CRN_FORMAT.cCRNFmtDXT5]: {\n    pixelFormat: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT5_EXT,\n    sizeFunction: getDxtXLevelSize\n  }\n};\n\nlet cachedDstSize = 0;\nlet dst: number;\n\n/**\n * Parse texture data as \"CRN\" format.\n * Function is \"async\" as emscriptified decoder module is loaded asyncronously\n * @param data - binary data of compressed texture\n * @returns Promise of Array of the texture levels\n */\nexport async function parseCrunch(data, options: any): Promise<TextureLevel[]> {\n  const crunchModule = await loadCrunchModule(options);\n\n  // Copy the contents of the arrayBuffer into emscriptens heap.\n  const srcSize = data.byteLength;\n  const bytes = new Uint8Array(data);\n  const src = crunchModule._malloc(srcSize);\n  arrayBufferCopy(bytes, crunchModule.HEAPU8, src, srcSize);\n\n  // Determine what type of compressed data the file contains.\n  const format = crunchModule._crn_get_dxt_format(src, srcSize);\n  assert(Boolean(DXT_FORMAT_MAP[format]), 'Unsupported format');\n\n  // Gather basic metrics about the DXT data.\n  const mipMapLevels = crunchModule._crn_get_levels(src, srcSize);\n  const width = crunchModule._crn_get_width(src, srcSize);\n  const height = crunchModule._crn_get_height(src, srcSize);\n  // const bytesPerBlock = crunchModule._crn_get_bytes_per_block(src, srcSize);\n\n  // Determine the size of the decoded DXT data.\n  const sizeFunction = DXT_FORMAT_MAP[format].sizeFunction;\n  let dstSize = 0;\n  for (let i = 0; i < mipMapLevels; ++i) {\n    dstSize += sizeFunction(width >> i, height >> i);\n  }\n\n  // Allocate enough space on the emscripten heap to hold the decoded DXT data\n  // or reuse the existing allocation if a previous call to this function has\n  // already acquired a large enough buffer.\n  if (cachedDstSize < dstSize) {\n    if (dst) {\n      crunchModule._free(dst);\n    }\n    dst = crunchModule._malloc(dstSize);\n    cachedDstSize = dstSize;\n  }\n\n  // Decompress the DXT data from the Crunch file into the allocated space.\n  crunchModule._crn_decompress(src, srcSize, dst, dstSize, 0, mipMapLevels);\n\n  // Release the crunch file data from the emscripten heap.\n  crunchModule._free(src);\n\n  const image = new Uint8Array(crunchModule.HEAPU8.buffer, dst, dstSize).slice();\n  return extractMipmapImages(image, {\n    mipMapLevels,\n    width,\n    height,\n    sizeFunction,\n    internalFormat: DXT_FORMAT_MAP[format].pixelFormat\n  });\n}\n\n/**\n * Copy an array of bytes into or out of the emscripten heap\n * @param {Uint8Array} srcData - Source data array\n * @param {Uint8Array} dstData - Destination data array\n * @param {number} dstByteOffset - Destination data offset\n * @param {number} numBytes - number of bytes to copy\n * @returns {void}\n */\nfunction arrayBufferCopy(srcData, dstData, dstByteOffset, numBytes) {\n  let i;\n  const dst32Offset = dstByteOffset / 4;\n  const tail = numBytes % 4;\n  const src32 = new Uint32Array(srcData.buffer, 0, (numBytes - tail) / 4);\n  const dst32 = new Uint32Array(dstData.buffer);\n  for (i = 0; i < src32.length; i++) {\n    dst32[dst32Offset + i] = src32[i];\n  }\n  for (i = numBytes - tail; i < numBytes; i++) {\n    dstData[dstByteOffset + i] = srcData[i];\n  }\n}\n"], "mappings": "AACA,SAAQA,gBAAgB,QAAO,wBAAwB;AACvD,SAAQC,uBAAuB,QAAO,kBAAkB;AACxD,SAAQC,MAAM,QAAO,0BAA0B;AAC/C,SAAQC,gBAAgB,EAAEC,gBAAgB,QAAO,aAAa;AAC9D,SAAQC,mBAAmB,QAAO,gCAAgC;AAGlE,MAAMC,UAAU,GAAG;EACjBC,cAAc,EAAE,CAAC,CAAC;EAElBC,WAAW,EAAE,CAAC;EAEdC,WAAW,EAAE,CAAC;EACdC,WAAW,EAAE;AAGf,CAAC;AAGD,MAAMC,cAAc,GAAG;EACrB,CAACL,UAAU,CAACE,WAAW,GAAG;IACxBI,WAAW,EAAEX,uBAAuB,CAACY,4BAA4B;IACjEC,YAAY,EAAEX;EAChB,CAAC;EACD,CAACG,UAAU,CAACG,WAAW,GAAG;IACxBG,WAAW,EAAEX,uBAAuB,CAACc,6BAA6B;IAClED,YAAY,EAAEV;EAChB,CAAC;EACD,CAACE,UAAU,CAACI,WAAW,GAAG;IACxBE,WAAW,EAAEX,uBAAuB,CAACe,6BAA6B;IAClEF,YAAY,EAAEV;EAChB;AACF,CAAC;AAED,IAAIa,aAAa,GAAG,CAAC;AACrB,IAAIC,GAAW;AAQf,OAAO,eAAeC,WAAWA,CAACC,IAAI,EAAEC,OAAY,EAA2B;EAC7E,MAAMC,YAAY,GAAG,MAAMtB,gBAAgB,CAACqB,OAAO,CAAC;EAGpD,MAAME,OAAO,GAAGH,IAAI,CAACI,UAAU;EAC/B,MAAMC,KAAK,GAAG,IAAIC,UAAU,CAACN,IAAI,CAAC;EAClC,MAAMO,GAAG,GAAGL,YAAY,CAACM,OAAO,CAACL,OAAO,CAAC;EACzCM,eAAe,CAACJ,KAAK,EAAEH,YAAY,CAACQ,MAAM,EAAEH,GAAG,EAAEJ,OAAO,CAAC;EAGzD,MAAMQ,MAAM,GAAGT,YAAY,CAACU,mBAAmB,CAACL,GAAG,EAAEJ,OAAO,CAAC;EAC7DrB,MAAM,CAAC+B,OAAO,CAACtB,cAAc,CAACoB,MAAM,CAAC,CAAC,EAAE,oBAAoB,CAAC;EAG7D,MAAMG,YAAY,GAAGZ,YAAY,CAACa,eAAe,CAACR,GAAG,EAAEJ,OAAO,CAAC;EAC/D,MAAMa,KAAK,GAAGd,YAAY,CAACe,cAAc,CAACV,GAAG,EAAEJ,OAAO,CAAC;EACvD,MAAMe,MAAM,GAAGhB,YAAY,CAACiB,eAAe,CAACZ,GAAG,EAAEJ,OAAO,CAAC;EAIzD,MAAMT,YAAY,GAAGH,cAAc,CAACoB,MAAM,CAAC,CAACjB,YAAY;EACxD,IAAI0B,OAAO,GAAG,CAAC;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,YAAY,EAAE,EAAEO,CAAC,EAAE;IACrCD,OAAO,IAAI1B,YAAY,CAACsB,KAAK,IAAIK,CAAC,EAAEH,MAAM,IAAIG,CAAC,CAAC;EAClD;EAKA,IAAIxB,aAAa,GAAGuB,OAAO,EAAE;IAC3B,IAAItB,GAAG,EAAE;MACPI,YAAY,CAACoB,KAAK,CAACxB,GAAG,CAAC;IACzB;IACAA,GAAG,GAAGI,YAAY,CAACM,OAAO,CAACY,OAAO,CAAC;IACnCvB,aAAa,GAAGuB,OAAO;EACzB;EAGAlB,YAAY,CAACqB,eAAe,CAAChB,GAAG,EAAEJ,OAAO,EAAEL,GAAG,EAAEsB,OAAO,EAAE,CAAC,EAAEN,YAAY,CAAC;EAGzEZ,YAAY,CAACoB,KAAK,CAACf,GAAG,CAAC;EAEvB,MAAMiB,KAAK,GAAG,IAAIlB,UAAU,CAACJ,YAAY,CAACQ,MAAM,CAACe,MAAM,EAAE3B,GAAG,EAAEsB,OAAO,CAAC,CAACM,KAAK,CAAC,CAAC;EAC9E,OAAOzC,mBAAmB,CAACuC,KAAK,EAAE;IAChCV,YAAY;IACZE,KAAK;IACLE,MAAM;IACNxB,YAAY;IACZiC,cAAc,EAAEpC,cAAc,CAACoB,MAAM,CAAC,CAACnB;EACzC,CAAC,CAAC;AACJ;AAUA,SAASiB,eAAeA,CAACmB,OAAO,EAAEC,OAAO,EAAEC,aAAa,EAAEC,QAAQ,EAAE;EAClE,IAAIV,CAAC;EACL,MAAMW,WAAW,GAAGF,aAAa,GAAG,CAAC;EACrC,MAAMG,IAAI,GAAGF,QAAQ,GAAG,CAAC;EACzB,MAAMG,KAAK,GAAG,IAAIC,WAAW,CAACP,OAAO,CAACH,MAAM,EAAE,CAAC,EAAE,CAACM,QAAQ,GAAGE,IAAI,IAAI,CAAC,CAAC;EACvE,MAAMG,KAAK,GAAG,IAAID,WAAW,CAACN,OAAO,CAACJ,MAAM,CAAC;EAC7C,KAAKJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,KAAK,CAACG,MAAM,EAAEhB,CAAC,EAAE,EAAE;IACjCe,KAAK,CAACJ,WAAW,GAAGX,CAAC,CAAC,GAAGa,KAAK,CAACb,CAAC,CAAC;EACnC;EACA,KAAKA,CAAC,GAAGU,QAAQ,GAAGE,IAAI,EAAEZ,CAAC,GAAGU,QAAQ,EAAEV,CAAC,EAAE,EAAE;IAC3CQ,OAAO,CAACC,aAAa,GAAGT,CAAC,CAAC,GAAGO,OAAO,CAACP,CAAC,CAAC;EACzC;AACF"}