{"version": 3, "file": "basis-module-loader.js", "names": ["VERSION", "loadLibrary", "BASIS_CDN_ENCODER_WASM", "concat", "BASIS_CDN_ENCODER_JS", "loadBasisTranscoderPromise", "loadBasisTrascoderModule", "options", "modules", "basis", "loadBasisTrascoder", "BASIS", "wasmBinary", "Promise", "all", "globalThis", "initializeBasisTrascoderModule", "BasisModule", "resolve", "then", "module", "BasisFile", "initializeBasis", "loadBasisEncoderPromise", "loadBasisEncoderModule", "basisEncoder", "loadBasisEncoder", "BASIS_ENCODER", "initializeBasisEncoderModule", "BasisEncoderModule", "KTX2File", "Basis<PERSON><PERSON>der"], "sources": ["../../../../src/lib/parsers/basis-module-loader.ts"], "sourcesContent": ["// __VERSION__ is injected by babel-plugin-version-inline\n// @ts-ignore TS2304: Cannot find name '__VERSION__'.\nconst VERSION = typeof __VERSION__ !== 'undefined' ? __VERSION__ : 'latest';\n\n// @ts-nocheck\nimport {loadLibrary} from '@loaders.gl/worker-utils';\n\nconst BASIS_CDN_ENCODER_WASM = `https://unpkg.com/@loaders.gl/textures@${VERSION}/dist/libs/basis_encoder.wasm`;\nconst BASIS_CDN_ENCODER_JS = `https://unpkg.com/@loaders.gl/textures@${VERSION}/dist/libs/basis_encoder.js`;\n\nlet loadBasisTranscoderPromise;\n\n/**\n * Loads wasm transcoder module\n * @param options\n * @returns {BasisFile} promise\n */\nexport async function loadBasisTrascoderModule(options) {\n  const modules = options.modules || {};\n  if (modules.basis) {\n    return modules.basis;\n  }\n\n  loadBasisTranscoderPromise = loadBasisTranscoderPromise || loadBasisTrascoder(options);\n  return await loadBasisTranscoderPromise;\n}\n\n/**\n * Loads wasm transcoder module\n * @param options\n * @returns {BasisFile} promise\n */\nasync function loadBasisTrascoder(options) {\n  let BASIS = null;\n  let wasmBinary = null;\n\n  [BASIS, wasmBinary] = await Promise.all([\n    await loadLibrary('basis_transcoder.js', 'textures', options),\n    await loadLibrary('basis_transcoder.wasm', 'textures', options)\n  ]);\n\n  // Depends on how import happened...\n  // @ts-ignore TS2339: Property does not exist on type\n  BASIS = BASIS || globalThis.BASIS;\n  return await initializeBasisTrascoderModule(BASIS, wasmBinary);\n}\n\n/**\n * Initialize wasm transcoder module\n * @param BasisModule - js part of the module\n * @param wasmBinary - wasm part of the module\n * @returns {BasisFile} promise\n */\nfunction initializeBasisTrascoderModule(BasisModule, wasmBinary) {\n  const options: {wasmBinary?} = {};\n\n  if (wasmBinary) {\n    options.wasmBinary = wasmBinary;\n  }\n\n  return new Promise((resolve) => {\n    // if you try to return BasisModule the browser crashes!\n    BasisModule(options).then((module) => {\n      const {BasisFile, initializeBasis} = module;\n      initializeBasis();\n      resolve({BasisFile});\n    });\n  });\n}\n\nlet loadBasisEncoderPromise;\n\n/**\n * Loads wasm encoder module\n * @param options\n * @returns {BasisFile, KTX2File} promise\n */\nexport async function loadBasisEncoderModule(options) {\n  const modules = options.modules || {};\n  if (modules.basisEncoder) {\n    return modules.basisEncoder;\n  }\n\n  loadBasisEncoderPromise = loadBasisEncoderPromise || loadBasisEncoder(options);\n  return await loadBasisEncoderPromise;\n}\n\n/**\n * Loads wasm encoder module\n * @param options\n * @returns {BasisFile, KTX2File} promise\n */\nasync function loadBasisEncoder(options) {\n  let BASIS_ENCODER = null;\n  let wasmBinary = null;\n\n  [BASIS_ENCODER, wasmBinary] = await Promise.all([\n    await loadLibrary(BASIS_CDN_ENCODER_JS, 'textures', options),\n    await loadLibrary(BASIS_CDN_ENCODER_WASM, 'textures', options)\n  ]);\n\n  // Depends on how import happened...\n  // @ts-ignore TS2339: Property does not exist on type\n  BASIS_ENCODER = BASIS_ENCODER || globalThis.BASIS;\n  return await initializeBasisEncoderModule(BASIS_ENCODER, wasmBinary);\n}\n\n/**\n * Initialize wasm transcoder module\n * @param BasisEncoderModule - js part of the module\n * @param wasmBinary - wasm part of the module\n * @returns {BasisFile, KTX2File} promise\n */\nfunction initializeBasisEncoderModule(BasisEncoderModule, wasmBinary) {\n  const options: {wasmBinary?} = {};\n\n  if (wasmBinary) {\n    options.wasmBinary = wasmBinary;\n  }\n\n  return new Promise((resolve) => {\n    // if you try to return BasisModule the browser crashes!\n    BasisEncoderModule(options).then((module) => {\n      const {BasisFile, KTX2File, initializeBasis, BasisEncoder} = module;\n      initializeBasis();\n      resolve({BasisFile, KTX2File, BasisEncoder});\n    });\n  });\n}\n"], "mappings": "AAEA,MAAMA,OAAO,GAAG,eAAkB,KAAK,WAAW,cAAiB,QAAQ;AAG3E,SAAQC,WAAW,QAAO,0BAA0B;AAEpD,MAAMC,sBAAsB,6CAAAC,MAAA,CAA6CH,OAAO,kCAA+B;AAC/G,MAAMI,oBAAoB,6CAAAD,MAAA,CAA6CH,OAAO,gCAA6B;AAE3G,IAAIK,0BAA0B;AAO9B,OAAO,eAAeC,wBAAwBA,CAACC,OAAO,EAAE;EACtD,MAAMC,OAAO,GAAGD,OAAO,CAACC,OAAO,IAAI,CAAC,CAAC;EACrC,IAAIA,OAAO,CAACC,KAAK,EAAE;IACjB,OAAOD,OAAO,CAACC,KAAK;EACtB;EAEAJ,0BAA0B,GAAGA,0BAA0B,IAAIK,kBAAkB,CAACH,OAAO,CAAC;EACtF,OAAO,MAAMF,0BAA0B;AACzC;AAOA,eAAeK,kBAAkBA,CAACH,OAAO,EAAE;EACzC,IAAII,KAAK,GAAG,IAAI;EAChB,IAAIC,UAAU,GAAG,IAAI;EAErB,CAACD,KAAK,EAAEC,UAAU,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACtC,MAAMb,WAAW,CAAC,qBAAqB,EAAE,UAAU,EAAEM,OAAO,CAAC,EAC7D,MAAMN,WAAW,CAAC,uBAAuB,EAAE,UAAU,EAAEM,OAAO,CAAC,CAChE,CAAC;EAIFI,KAAK,GAAGA,KAAK,IAAII,UAAU,CAACJ,KAAK;EACjC,OAAO,MAAMK,8BAA8B,CAACL,KAAK,EAAEC,UAAU,CAAC;AAChE;AAQA,SAASI,8BAA8BA,CAACC,WAAW,EAAEL,UAAU,EAAE;EAC/D,MAAML,OAAsB,GAAG,CAAC,CAAC;EAEjC,IAAIK,UAAU,EAAE;IACdL,OAAO,CAACK,UAAU,GAAGA,UAAU;EACjC;EAEA,OAAO,IAAIC,OAAO,CAAEK,OAAO,IAAK;IAE9BD,WAAW,CAACV,OAAO,CAAC,CAACY,IAAI,CAAEC,MAAM,IAAK;MACpC,MAAM;QAACC,SAAS;QAAEC;MAAe,CAAC,GAAGF,MAAM;MAC3CE,eAAe,CAAC,CAAC;MACjBJ,OAAO,CAAC;QAACG;MAAS,CAAC,CAAC;IACtB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,IAAIE,uBAAuB;AAO3B,OAAO,eAAeC,sBAAsBA,CAACjB,OAAO,EAAE;EACpD,MAAMC,OAAO,GAAGD,OAAO,CAACC,OAAO,IAAI,CAAC,CAAC;EACrC,IAAIA,OAAO,CAACiB,YAAY,EAAE;IACxB,OAAOjB,OAAO,CAACiB,YAAY;EAC7B;EAEAF,uBAAuB,GAAGA,uBAAuB,IAAIG,gBAAgB,CAACnB,OAAO,CAAC;EAC9E,OAAO,MAAMgB,uBAAuB;AACtC;AAOA,eAAeG,gBAAgBA,CAACnB,OAAO,EAAE;EACvC,IAAIoB,aAAa,GAAG,IAAI;EACxB,IAAIf,UAAU,GAAG,IAAI;EAErB,CAACe,aAAa,EAAEf,UAAU,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC9C,MAAMb,WAAW,CAACG,oBAAoB,EAAE,UAAU,EAAEG,OAAO,CAAC,EAC5D,MAAMN,WAAW,CAACC,sBAAsB,EAAE,UAAU,EAAEK,OAAO,CAAC,CAC/D,CAAC;EAIFoB,aAAa,GAAGA,aAAa,IAAIZ,UAAU,CAACJ,KAAK;EACjD,OAAO,MAAMiB,4BAA4B,CAACD,aAAa,EAAEf,UAAU,CAAC;AACtE;AAQA,SAASgB,4BAA4BA,CAACC,kBAAkB,EAAEjB,UAAU,EAAE;EACpE,MAAML,OAAsB,GAAG,CAAC,CAAC;EAEjC,IAAIK,UAAU,EAAE;IACdL,OAAO,CAACK,UAAU,GAAGA,UAAU;EACjC;EAEA,OAAO,IAAIC,OAAO,CAAEK,OAAO,IAAK;IAE9BW,kBAAkB,CAACtB,OAAO,CAAC,CAACY,IAAI,CAAEC,MAAM,IAAK;MAC3C,MAAM;QAACC,SAAS;QAAES,QAAQ;QAAER,eAAe;QAAES;MAAY,CAAC,GAAGX,MAAM;MACnEE,eAAe,CAAC,CAAC;MACjBJ,OAAO,CAAC;QAACG,SAAS;QAAES,QAAQ;QAAEC;MAAY,CAAC,CAAC;IAC9C,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ"}