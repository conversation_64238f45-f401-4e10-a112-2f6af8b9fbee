{"version": 3, "file": "parse-npy.js", "names": ["systemIsLittleEndian", "a", "Uint32Array", "b", "Uint8Array", "buffer", "byteOffset", "byteLength", "LITTLE_ENDIAN_OS", "DTYPES", "u1", "i1", "Int8Array", "u2", "Uint16Array", "i2", "Int16Array", "u4", "i4", "Int32Array", "f4", "Float32Array", "f8", "Float64Array", "parseNPY", "arrayBuffer", "options", "_header$shape", "view", "DataView", "header", "headerEndOffset", "parse<PERSON><PERSON><PERSON>", "numpyType", "descr", "ArrayType", "slice", "Error", "concat", "nArrayElements", "shape", "reduce", "arrayByteLength", "BYTES_PER_ELEMENT", "data", "majorVersion", "getUint8", "offset", "<PERSON><PERSON><PERSON><PERSON>", "getUint32", "getUint16", "encoding", "decoder", "TextDecoder", "headerArray", "headerText", "decode", "JSON", "parse", "replace"], "sources": ["../../../../src/lib/parsers/parse-npy.ts"], "sourcesContent": ["// import type {TextureLevel} from '@loaders.gl/schema';\n\ntype NumpyHeader = {descr: string; shape: number[]};\ntype TypedArrayConstructor =\n  | typeof Int8Array\n  | typeof Uint8Array\n  | typeof Int16Array\n  | typeof Uint16Array\n  | typeof Int32Array\n  | typeof Uint32Array\n  | typeof Int32Array\n  | typeof Uint32Array\n  | typeof Float32Array\n  | typeof Float64Array;\n\nfunction systemIsLittleEndian() {\n  const a = new Uint32Array([0x12345678]);\n  const b = new Uint8Array(a.buffer, a.byteOffset, a.byteLength);\n  return !(b[0] === 0x12);\n}\n\nconst LITTLE_ENDIAN_OS = systemIsLittleEndian();\n\n// The basic string format consists of 3 characters:\n// 1. a character describing the byteorder of the data (<: little-endian, >: big-endian, |: not-relevant)\n// 2. a character code giving the basic type of the array\n// 3. an integer providing the number of bytes the type uses.\n// https://numpy.org/doc/stable/reference/arrays.interface.html\n//\n// Here I only include the second and third characters, and check endianness\n// separately\nconst DTYPES: Record<string, TypedArrayConstructor> = {\n  u1: Uint8Array,\n  i1: Int8Array,\n  u2: Uint16Array,\n  i2: Int16Array,\n  u4: Uint32Array,\n  i4: Int32Array,\n  f4: Float32Array,\n  f8: Float64Array\n};\n\nexport function parseNPY(arrayBuffer: ArrayBuffer, options?: unknown) {\n  if (!arrayBuffer) {\n    return null;\n  }\n\n  const view = new DataView(arrayBuffer);\n  const {header, headerEndOffset} = parseHeader(view);\n\n  const numpyType = header.descr;\n  const ArrayType = DTYPES[numpyType.slice(1, 3)];\n  if (!ArrayType) {\n    throw new Error(`Unimplemented type ${numpyType}`);\n  }\n\n  const nArrayElements = header.shape?.reduce((a: number, b: number): number => a * b);\n  const arrayByteLength = nArrayElements * ArrayType.BYTES_PER_ELEMENT;\n\n  if (arrayBuffer.byteLength < headerEndOffset + arrayByteLength) {\n    throw new Error('Buffer overflow');\n  }\n  const data = new ArrayType(arrayBuffer.slice(headerEndOffset, headerEndOffset + arrayByteLength));\n\n  // Swap endianness if needed\n  if ((numpyType[0] === '>' && LITTLE_ENDIAN_OS) || (numpyType[0] === '<' && !LITTLE_ENDIAN_OS)) {\n    throw new Error('Incorrect endianness');\n  }\n\n  return {\n    data,\n    header\n  };\n}\n\n/**\n * Parse NPY header\n *\n * @param  view\n * @return\n */\nfunction parseHeader(view: DataView): {header: NumpyHeader; headerEndOffset: number} {\n  const majorVersion = view.getUint8(6);\n  // const minorVersion = view.getUint8(7);\n\n  let offset = 8;\n  let headerLength: number;\n  if (majorVersion >= 2) {\n    headerLength = view.getUint32(offset, true);\n    offset += 4;\n  } else {\n    headerLength = view.getUint16(offset, true);\n    offset += 2;\n  }\n\n  const encoding = majorVersion <= 2 ? 'latin1' : 'utf-8';\n  const decoder = new TextDecoder(encoding);\n  const headerArray = new Uint8Array(view.buffer, offset, headerLength);\n  const headerText = decoder.decode(headerArray);\n  offset += headerLength;\n\n  const header = JSON.parse(\n    headerText\n      .replace(/'/g, '\"')\n      .replace('False', 'false')\n      .replace('(', '[')\n      .replace(/,*\\),*/g, ']')\n  );\n\n  return {header, headerEndOffset: offset};\n}\n"], "mappings": "AAeA,SAASA,oBAAoBA,CAAA,EAAG;EAC9B,MAAMC,CAAC,GAAG,IAAIC,WAAW,CAAC,CAAC,UAAU,CAAC,CAAC;EACvC,MAAMC,CAAC,GAAG,IAAIC,UAAU,CAACH,CAAC,CAACI,MAAM,EAAEJ,CAAC,CAACK,UAAU,EAAEL,CAAC,CAACM,UAAU,CAAC;EAC9D,OAAO,EAAEJ,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;AACzB;AAEA,MAAMK,gBAAgB,GAAGR,oBAAoB,CAAC,CAAC;AAU/C,MAAMS,MAA6C,GAAG;EACpDC,EAAE,EAAEN,UAAU;EACdO,EAAE,EAAEC,SAAS;EACbC,EAAE,EAAEC,WAAW;EACfC,EAAE,EAAEC,UAAU;EACdC,EAAE,EAAEf,WAAW;EACfgB,EAAE,EAAEC,UAAU;EACdC,EAAE,EAAEC,YAAY;EAChBC,EAAE,EAAEC;AACN,CAAC;AAED,OAAO,SAASC,QAAQA,CAACC,WAAwB,EAAEC,OAAiB,EAAE;EAAA,IAAAC,aAAA;EACpE,IAAI,CAACF,WAAW,EAAE;IAChB,OAAO,IAAI;EACb;EAEA,MAAMG,IAAI,GAAG,IAAIC,QAAQ,CAACJ,WAAW,CAAC;EACtC,MAAM;IAACK,MAAM;IAAEC;EAAe,CAAC,GAAGC,WAAW,CAACJ,IAAI,CAAC;EAEnD,MAAMK,SAAS,GAAGH,MAAM,CAACI,KAAK;EAC9B,MAAMC,SAAS,GAAG1B,MAAM,CAACwB,SAAS,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC/C,IAAI,CAACD,SAAS,EAAE;IACd,MAAM,IAAIE,KAAK,uBAAAC,MAAA,CAAuBL,SAAS,CAAE,CAAC;EACpD;EAEA,MAAMM,cAAc,IAAAZ,aAAA,GAAGG,MAAM,CAACU,KAAK,cAAAb,aAAA,uBAAZA,aAAA,CAAcc,MAAM,CAAC,CAACxC,CAAS,EAAEE,CAAS,KAAaF,CAAC,GAAGE,CAAC,CAAC;EACpF,MAAMuC,eAAe,GAAGH,cAAc,GAAGJ,SAAS,CAACQ,iBAAiB;EAEpE,IAAIlB,WAAW,CAAClB,UAAU,GAAGwB,eAAe,GAAGW,eAAe,EAAE;IAC9D,MAAM,IAAIL,KAAK,CAAC,iBAAiB,CAAC;EACpC;EACA,MAAMO,IAAI,GAAG,IAAIT,SAAS,CAACV,WAAW,CAACW,KAAK,CAACL,eAAe,EAAEA,eAAe,GAAGW,eAAe,CAAC,CAAC;EAGjG,IAAKT,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIzB,gBAAgB,IAAMyB,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAACzB,gBAAiB,EAAE;IAC7F,MAAM,IAAI6B,KAAK,CAAC,sBAAsB,CAAC;EACzC;EAEA,OAAO;IACLO,IAAI;IACJd;EACF,CAAC;AACH;AAQA,SAASE,WAAWA,CAACJ,IAAc,EAAkD;EACnF,MAAMiB,YAAY,GAAGjB,IAAI,CAACkB,QAAQ,CAAC,CAAC,CAAC;EAGrC,IAAIC,MAAM,GAAG,CAAC;EACd,IAAIC,YAAoB;EACxB,IAAIH,YAAY,IAAI,CAAC,EAAE;IACrBG,YAAY,GAAGpB,IAAI,CAACqB,SAAS,CAACF,MAAM,EAAE,IAAI,CAAC;IAC3CA,MAAM,IAAI,CAAC;EACb,CAAC,MAAM;IACLC,YAAY,GAAGpB,IAAI,CAACsB,SAAS,CAACH,MAAM,EAAE,IAAI,CAAC;IAC3CA,MAAM,IAAI,CAAC;EACb;EAEA,MAAMI,QAAQ,GAAGN,YAAY,IAAI,CAAC,GAAG,QAAQ,GAAG,OAAO;EACvD,MAAMO,OAAO,GAAG,IAAIC,WAAW,CAACF,QAAQ,CAAC;EACzC,MAAMG,WAAW,GAAG,IAAIlD,UAAU,CAACwB,IAAI,CAACvB,MAAM,EAAE0C,MAAM,EAAEC,YAAY,CAAC;EACrE,MAAMO,UAAU,GAAGH,OAAO,CAACI,MAAM,CAACF,WAAW,CAAC;EAC9CP,MAAM,IAAIC,YAAY;EAEtB,MAAMlB,MAAM,GAAG2B,IAAI,CAACC,KAAK,CACvBH,UAAU,CACPI,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAClBA,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CACzBA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CACjBA,OAAO,CAAC,SAAS,EAAE,GAAG,CAC3B,CAAC;EAED,OAAO;IAAC7B,MAAM;IAAEC,eAAe,EAAEgB;EAAM,CAAC;AAC1C"}