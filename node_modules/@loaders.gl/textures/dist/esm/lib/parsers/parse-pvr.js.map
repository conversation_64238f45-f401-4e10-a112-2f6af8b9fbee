{"version": 3, "file": "parse-pvr.js", "names": ["GL_EXTENSIONS_CONSTANTS", "extractMipmapImages", "PVR_CONSTANTS", "MAGIC_NUMBER", "MAGIC_NUMBER_EXTRA", "HEADER_LENGTH", "HEADER_SIZE", "MAGIC_NUMBER_INDEX", "PIXEL_FORMAT_INDEX", "COLOUR_SPACE_INDEX", "HEIGHT_INDEX", "WIDTH_INDEX", "MIPMAPCOUNT_INDEX", "METADATA_SIZE_INDEX", "PVR_PIXEL_FORMATS", "COMPRESSED_RGB_PVRTC_2BPPV1_IMG", "COMPRESSED_RGBA_PVRTC_2BPPV1_IMG", "COMPRESSED_RGB_PVRTC_4BPPV1_IMG", "COMPRESSED_RGBA_PVRTC_4BPPV1_IMG", "COMPRESSED_RGB_ETC1_WEBGL", "COMPRESSED_RGB_S3TC_DXT1_EXT", "COMPRESSED_RGBA_S3TC_DXT3_EXT", "COMPRESSED_RGBA_S3TC_DXT5_EXT", "COMPRESSED_RGB8_ETC2", "COMPRESSED_RGBA8_ETC2_EAC", "COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2", "COMPRESSED_R11_EAC", "COMPRESSED_RG11_EAC", "COMPRESSED_RGBA_ASTC_4X4_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_4X4_KHR", "COMPRESSED_RGBA_ASTC_5X4_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_5X4_KHR", "COMPRESSED_RGBA_ASTC_5X5_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_5X5_KHR", "COMPRESSED_RGBA_ASTC_6X5_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_6X5_KHR", "COMPRESSED_RGBA_ASTC_6X6_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_6X6_KHR", "COMPRESSED_RGBA_ASTC_8X5_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_8X5_KHR", "COMPRESSED_RGBA_ASTC_8X6_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_8X6_KHR", "COMPRESSED_RGBA_ASTC_8X8_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_8X8_KHR", "COMPRESSED_RGBA_ASTC_10X5_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_10X5_KHR", "COMPRESSED_RGBA_ASTC_10X6_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_10X6_KHR", "COMPRESSED_RGBA_ASTC_10X8_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_10X8_KHR", "COMPRESSED_RGBA_ASTC_10X10_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_10X10_KHR", "COMPRESSED_RGBA_ASTC_12X10_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_12X10_KHR", "COMPRESSED_RGBA_ASTC_12X12_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_12X12_KHR", "PVR_SIZE_FUNCTIONS", "pvrtc2bppSize", "pvrtc4bppSize", "dxtEtcSmallSize", "dxtEtcAstcBigSize", "atc5x4Size", "atc5x5Size", "atc6x5Size", "atc6x6Size", "atc8x5Size", "atc8x6Size", "atc8x8Size", "atc10x5Size", "atc10x6Size", "atc10x8Size", "atc10x10Size", "atc12x10Size", "atc12x12Size", "isPVR", "data", "header", "Uint32Array", "version", "parsePVR", "pvrFormat", "colourSpace", "pixelFormats", "internalFormat", "length", "sizeFunction", "mipMapLevels", "width", "height", "dataOffset", "image", "Uint8Array", "Math", "max", "floor"], "sources": ["../../../../src/lib/parsers/parse-pvr.ts"], "sourcesContent": ["/* eslint-disable camelcase */\n// Forked from PicoGL: https://github.com/tsherif/picogl.js/blob/master/examples/utils/utils.js\n// Copyright (c) 2017 <PERSON><PERSON> Sherif, The MIT License (MIT)\n\nimport type {TextureLevel} from '@loaders.gl/schema';\nimport {GL_EXTENSIONS_CONSTANTS} from '../gl-extensions';\nimport {extractMipmapImages} from '../utils/extract-mipmap-images';\n\nconst PVR_CONSTANTS: Record<string, number> = {\n  MAGIC_NUMBER: 0x03525650,\n  MAGIC_NUMBER_EXTRA: 0x50565203,\n  HEADER_LENGTH: 13,\n  HEADER_SIZE: 52,\n  MAGIC_NUMBER_INDEX: 0,\n  PIXEL_FORMAT_INDEX: 2,\n  COLOUR_SPACE_INDEX: 4,\n  HEIGHT_INDEX: 6,\n  WIDTH_INDEX: 7,\n  MIPMAPCOUNT_INDEX: 11,\n  METADATA_SIZE_INDEX: 12\n};\n\nconst PVR_PIXEL_FORMATS: Record<number, number[]> = {\n  0: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_PVRTC_2BPPV1_IMG],\n  1: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG],\n  2: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_PVRTC_4BPPV1_IMG],\n  3: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG],\n  6: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_ETC1_WEBGL],\n  7: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_S3TC_DXT1_EXT],\n  9: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT3_EXT],\n  11: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT5_EXT],\n  22: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB8_ETC2],\n  23: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA8_ETC2_EAC],\n  24: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2],\n  25: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_R11_EAC],\n  26: [GL_EXTENSIONS_CONSTANTS.COMPRESSED_RG11_EAC],\n  27: [\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_4X4_KHR,\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_4X4_KHR\n  ],\n  28: [\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_5X4_KHR,\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_5X4_KHR\n  ],\n  29: [\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_5X5_KHR,\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_5X5_KHR\n  ],\n  30: [\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_6X5_KHR,\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_6X5_KHR\n  ],\n  31: [\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_6X6_KHR,\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_6X6_KHR\n  ],\n  32: [\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8X5_KHR,\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_8X5_KHR\n  ],\n  33: [\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8X6_KHR,\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_8X6_KHR\n  ],\n  34: [\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8X8_KHR,\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_8X8_KHR\n  ],\n  35: [\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10X5_KHR,\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10X5_KHR\n  ],\n  36: [\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10X6_KHR,\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10X6_KHR\n  ],\n  37: [\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10X8_KHR,\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10X8_KHR\n  ],\n  38: [\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10X10_KHR,\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10X10_KHR\n  ],\n  39: [\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_12X10_KHR,\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_12X10_KHR\n  ],\n  40: [\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_12X12_KHR,\n    GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_12X12_KHR\n  ]\n};\n\nconst PVR_SIZE_FUNCTIONS: Record<number, (width: number, height: number) => number> = {\n  0: pvrtc2bppSize,\n  1: pvrtc2bppSize,\n  2: pvrtc4bppSize,\n  3: pvrtc4bppSize,\n  6: dxtEtcSmallSize,\n  7: dxtEtcSmallSize,\n  9: dxtEtcAstcBigSize,\n  11: dxtEtcAstcBigSize,\n  22: dxtEtcSmallSize,\n  23: dxtEtcAstcBigSize,\n  24: dxtEtcSmallSize,\n  25: dxtEtcSmallSize,\n  26: dxtEtcAstcBigSize,\n  27: dxtEtcAstcBigSize,\n  28: atc5x4Size,\n  29: atc5x5Size,\n  30: atc6x5Size,\n  31: atc6x6Size,\n  32: atc8x5Size,\n  33: atc8x6Size,\n  34: atc8x8Size,\n  35: atc10x5Size,\n  36: atc10x6Size,\n  37: atc10x8Size,\n  38: atc10x10Size,\n  39: atc12x10Size,\n  40: atc12x12Size\n};\n\n/**\n * Check if data is in \"PVR\" format by its magic number\n * @param data - binary data of compressed texture\n * @returns true - data in \"PVR\" format, else - false\n */\nexport function isPVR(data: ArrayBuffer): boolean {\n  const header = new Uint32Array(data, 0, PVR_CONSTANTS.HEADER_LENGTH);\n  const version = header[PVR_CONSTANTS.MAGIC_NUMBER_INDEX];\n\n  return version === PVR_CONSTANTS.MAGIC_NUMBER || version === PVR_CONSTANTS.MAGIC_NUMBER_EXTRA;\n}\n\n/**\n * Parse texture data as \"PVR\" format\n * @param data - binary data of compressed texture\n * @returns Array of the texture levels\n * @see http://cdn.imgtec.com/sdk-documentation/PVR+File+Format.Specification.pdf\n */\nexport function parsePVR(data: ArrayBuffer): TextureLevel[] {\n  const header = new Uint32Array(data, 0, PVR_CONSTANTS.HEADER_LENGTH);\n\n  const pvrFormat = header[PVR_CONSTANTS.PIXEL_FORMAT_INDEX];\n  const colourSpace = header[PVR_CONSTANTS.COLOUR_SPACE_INDEX];\n  const pixelFormats = PVR_PIXEL_FORMATS[pvrFormat] || [];\n  const internalFormat = pixelFormats.length > 1 && colourSpace ? pixelFormats[1] : pixelFormats[0];\n\n  const sizeFunction = PVR_SIZE_FUNCTIONS[pvrFormat];\n\n  const mipMapLevels = header[PVR_CONSTANTS.MIPMAPCOUNT_INDEX];\n\n  const width = header[PVR_CONSTANTS.WIDTH_INDEX];\n  const height = header[PVR_CONSTANTS.HEIGHT_INDEX];\n\n  const dataOffset = PVR_CONSTANTS.HEADER_SIZE + header[PVR_CONSTANTS.METADATA_SIZE_INDEX];\n\n  const image = new Uint8Array(data, dataOffset);\n\n  return extractMipmapImages(image, {\n    mipMapLevels,\n    width,\n    height,\n    sizeFunction,\n    internalFormat\n  });\n}\n\n// https://www.khronos.org/registry/webgl/extensions/WEBGL_compressed_texture_pvrtc/\nfunction pvrtc2bppSize(width: number, height: number): number {\n  width = Math.max(width, 16);\n  height = Math.max(height, 8);\n\n  return (width * height) / 4;\n}\n\n// https://www.khronos.org/registry/webgl/extensions/WEBGL_compressed_texture_pvrtc/\nfunction pvrtc4bppSize(width: number, height: number): number {\n  width = Math.max(width, 8);\n  height = Math.max(height, 8);\n\n  return (width * height) / 2;\n}\n\n// https://www.khronos.org/registry/webgl/extensions/WEBGL_compressed_texture_s3tc/\n// https://www.khronos.org/registry/webgl/extensions/WEBGL_compressed_texture_etc/\n// Size for:\n// COMPRESSED_RGB_S3TC_DXT1_EXT\n// COMPRESSED_R11_EAC\n// COMPRESSED_SIGNED_R11_EAC\n// COMPRESSED_RGB8_ETC2\n// COMPRESSED_SRGB8_ETC2\n// COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2\n// COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2\nfunction dxtEtcSmallSize(width: number, height: number): number {\n  return Math.floor((width + 3) / 4) * Math.floor((height + 3) / 4) * 8;\n}\n\n// https://www.khronos.org/registry/webgl/extensions/WEBGL_compressed_texture_s3tc/\n// https://www.khronos.org/registry/webgl/extensions/WEBGL_compressed_texture_etc/\n// https://www.khronos.org/registry/webgl/extensions/WEBGL_compressed_texture_astc/\n// Size for:\n// COMPRESSED_RGBA_S3TC_DXT3_EXT\n// COMPRESSED_RGBA_S3TC_DXT5_EXT\n// COMPRESSED_RG11_EAC\n// COMPRESSED_SIGNED_RG11_EAC\n// COMPRESSED_RGBA8_ETC2_EAC\n// COMPRESSED_SRGB8_ALPHA8_ETC2_EAC\n// COMPRESSED_RGBA_ASTC_4x4_KHR\nfunction dxtEtcAstcBigSize(width: number, height: number): number {\n  return Math.floor((width + 3) / 4) * Math.floor((height + 3) / 4) * 16;\n}\n\n// https://www.khronos.org/registry/webgl/extensions/WEBGL_compressed_texture_astc/\nfunction atc5x4Size(width: number, height: number): number {\n  return Math.floor((width + 4) / 5) * Math.floor((height + 3) / 4) * 16;\n}\n\n// https://www.khronos.org/registry/webgl/extensions/WEBGL_compressed_texture_astc/\nfunction atc5x5Size(width: number, height: number): number {\n  return Math.floor((width + 4) / 5) * Math.floor((height + 4) / 5) * 16;\n}\n\n// https://www.khronos.org/registry/webgl/extensions/WEBGL_compressed_texture_astc/\nfunction atc6x5Size(width: number, height: number): number {\n  return Math.floor((width + 5) / 6) * Math.floor((height + 4) / 5) * 16;\n}\n\n// https://www.khronos.org/registry/webgl/extensions/WEBGL_compressed_texture_astc/\nfunction atc6x6Size(width: number, height: number): number {\n  return Math.floor((width + 5) / 6) * Math.floor((height + 5) / 6) * 16;\n}\n\n// https://www.khronos.org/registry/webgl/extensions/WEBGL_compressed_texture_astc/\nfunction atc8x5Size(width: number, height: number): number {\n  return Math.floor((width + 7) / 8) * Math.floor((height + 4) / 5) * 16;\n}\n\n// https://www.khronos.org/registry/webgl/extensions/WEBGL_compressed_texture_astc/\nfunction atc8x6Size(width: number, height: number): number {\n  return Math.floor((width + 7) / 8) * Math.floor((height + 5) / 6) * 16;\n}\n\n// https://www.khronos.org/registry/webgl/extensions/WEBGL_compressed_texture_astc/\nfunction atc8x8Size(width: number, height: number): number {\n  return Math.floor((width + 7) / 8) * Math.floor((height + 7) / 8) * 16;\n}\n\n// https://www.khronos.org/registry/webgl/extensions/WEBGL_compressed_texture_astc/\nfunction atc10x5Size(width: number, height: number): number {\n  return Math.floor((width + 9) / 10) * Math.floor((height + 4) / 5) * 16;\n}\n\n// https://www.khronos.org/registry/webgl/extensions/WEBGL_compressed_texture_astc/\nfunction atc10x6Size(width: number, height: number): number {\n  return Math.floor((width + 9) / 10) * Math.floor((height + 5) / 6) * 16;\n}\n\n// https://www.khronos.org/registry/webgl/extensions/WEBGL_compressed_texture_astc/\nfunction atc10x8Size(width: number, height: number): number {\n  return Math.floor((width + 9) / 10) * Math.floor((height + 7) / 8) * 16;\n}\n\n// https://www.khronos.org/registry/webgl/extensions/WEBGL_compressed_texture_astc/\nfunction atc10x10Size(width: number, height: number): number {\n  return Math.floor((width + 9) / 10) * Math.floor((height + 9) / 10) * 16;\n}\n\n// https://www.khronos.org/registry/webgl/extensions/WEBGL_compressed_texture_astc/\nfunction atc12x10Size(width: number, height: number): number {\n  return Math.floor((width + 11) / 12) * Math.floor((height + 9) / 10) * 16;\n}\n\n// https://www.khronos.org/registry/webgl/extensions/WEBGL_compressed_texture_astc/\nfunction atc12x12Size(width: number, height: number): number {\n  return Math.floor((width + 11) / 12) * Math.floor((height + 11) / 12) * 16;\n}\n"], "mappings": "AAKA,SAAQA,uBAAuB,QAAO,kBAAkB;AACxD,SAAQC,mBAAmB,QAAO,gCAAgC;AAElE,MAAMC,aAAqC,GAAG;EAC5CC,YAAY,EAAE,UAAU;EACxBC,kBAAkB,EAAE,UAAU;EAC9BC,aAAa,EAAE,EAAE;EACjBC,WAAW,EAAE,EAAE;EACfC,kBAAkB,EAAE,CAAC;EACrBC,kBAAkB,EAAE,CAAC;EACrBC,kBAAkB,EAAE,CAAC;EACrBC,YAAY,EAAE,CAAC;EACfC,WAAW,EAAE,CAAC;EACdC,iBAAiB,EAAE,EAAE;EACrBC,mBAAmB,EAAE;AACvB,CAAC;AAED,MAAMC,iBAA2C,GAAG;EAClD,CAAC,EAAE,CAACd,uBAAuB,CAACe,+BAA+B,CAAC;EAC5D,CAAC,EAAE,CAACf,uBAAuB,CAACgB,gCAAgC,CAAC;EAC7D,CAAC,EAAE,CAAChB,uBAAuB,CAACiB,+BAA+B,CAAC;EAC5D,CAAC,EAAE,CAACjB,uBAAuB,CAACkB,gCAAgC,CAAC;EAC7D,CAAC,EAAE,CAAClB,uBAAuB,CAACmB,yBAAyB,CAAC;EACtD,CAAC,EAAE,CAACnB,uBAAuB,CAACoB,4BAA4B,CAAC;EACzD,CAAC,EAAE,CAACpB,uBAAuB,CAACqB,6BAA6B,CAAC;EAC1D,EAAE,EAAE,CAACrB,uBAAuB,CAACsB,6BAA6B,CAAC;EAC3D,EAAE,EAAE,CAACtB,uBAAuB,CAACuB,oBAAoB,CAAC;EAClD,EAAE,EAAE,CAACvB,uBAAuB,CAACwB,yBAAyB,CAAC;EACvD,EAAE,EAAE,CAACxB,uBAAuB,CAACyB,wCAAwC,CAAC;EACtE,EAAE,EAAE,CAACzB,uBAAuB,CAAC0B,kBAAkB,CAAC;EAChD,EAAE,EAAE,CAAC1B,uBAAuB,CAAC2B,mBAAmB,CAAC;EACjD,EAAE,EAAE,CACF3B,uBAAuB,CAAC4B,4BAA4B,EACpD5B,uBAAuB,CAAC6B,oCAAoC,CAC7D;EACD,EAAE,EAAE,CACF7B,uBAAuB,CAAC8B,4BAA4B,EACpD9B,uBAAuB,CAAC+B,oCAAoC,CAC7D;EACD,EAAE,EAAE,CACF/B,uBAAuB,CAACgC,4BAA4B,EACpDhC,uBAAuB,CAACiC,oCAAoC,CAC7D;EACD,EAAE,EAAE,CACFjC,uBAAuB,CAACkC,4BAA4B,EACpDlC,uBAAuB,CAACmC,oCAAoC,CAC7D;EACD,EAAE,EAAE,CACFnC,uBAAuB,CAACoC,4BAA4B,EACpDpC,uBAAuB,CAACqC,oCAAoC,CAC7D;EACD,EAAE,EAAE,CACFrC,uBAAuB,CAACsC,4BAA4B,EACpDtC,uBAAuB,CAACuC,oCAAoC,CAC7D;EACD,EAAE,EAAE,CACFvC,uBAAuB,CAACwC,4BAA4B,EACpDxC,uBAAuB,CAACyC,oCAAoC,CAC7D;EACD,EAAE,EAAE,CACFzC,uBAAuB,CAAC0C,4BAA4B,EACpD1C,uBAAuB,CAAC2C,oCAAoC,CAC7D;EACD,EAAE,EAAE,CACF3C,uBAAuB,CAAC4C,6BAA6B,EACrD5C,uBAAuB,CAAC6C,qCAAqC,CAC9D;EACD,EAAE,EAAE,CACF7C,uBAAuB,CAAC8C,6BAA6B,EACrD9C,uBAAuB,CAAC+C,qCAAqC,CAC9D;EACD,EAAE,EAAE,CACF/C,uBAAuB,CAACgD,6BAA6B,EACrDhD,uBAAuB,CAACiD,qCAAqC,CAC9D;EACD,EAAE,EAAE,CACFjD,uBAAuB,CAACkD,8BAA8B,EACtDlD,uBAAuB,CAACmD,sCAAsC,CAC/D;EACD,EAAE,EAAE,CACFnD,uBAAuB,CAACoD,8BAA8B,EACtDpD,uBAAuB,CAACqD,sCAAsC,CAC/D;EACD,EAAE,EAAE,CACFrD,uBAAuB,CAACsD,8BAA8B,EACtDtD,uBAAuB,CAACuD,sCAAsC;AAElE,CAAC;AAED,MAAMC,kBAA6E,GAAG;EACpF,CAAC,EAAEC,aAAa;EAChB,CAAC,EAAEA,aAAa;EAChB,CAAC,EAAEC,aAAa;EAChB,CAAC,EAAEA,aAAa;EAChB,CAAC,EAAEC,eAAe;EAClB,CAAC,EAAEA,eAAe;EAClB,CAAC,EAAEC,iBAAiB;EACpB,EAAE,EAAEA,iBAAiB;EACrB,EAAE,EAAED,eAAe;EACnB,EAAE,EAAEC,iBAAiB;EACrB,EAAE,EAAED,eAAe;EACnB,EAAE,EAAEA,eAAe;EACnB,EAAE,EAAEC,iBAAiB;EACrB,EAAE,EAAEA,iBAAiB;EACrB,EAAE,EAAEC,UAAU;EACd,EAAE,EAAEC,UAAU;EACd,EAAE,EAAEC,UAAU;EACd,EAAE,EAAEC,UAAU;EACd,EAAE,EAAEC,UAAU;EACd,EAAE,EAAEC,UAAU;EACd,EAAE,EAAEC,UAAU;EACd,EAAE,EAAEC,WAAW;EACf,EAAE,EAAEC,WAAW;EACf,EAAE,EAAEC,WAAW;EACf,EAAE,EAAEC,YAAY;EAChB,EAAE,EAAEC,YAAY;EAChB,EAAE,EAAEC;AACN,CAAC;AAOD,OAAO,SAASC,KAAKA,CAACC,IAAiB,EAAW;EAChD,MAAMC,MAAM,GAAG,IAAIC,WAAW,CAACF,IAAI,EAAE,CAAC,EAAEzE,aAAa,CAACG,aAAa,CAAC;EACpE,MAAMyE,OAAO,GAAGF,MAAM,CAAC1E,aAAa,CAACK,kBAAkB,CAAC;EAExD,OAAOuE,OAAO,KAAK5E,aAAa,CAACC,YAAY,IAAI2E,OAAO,KAAK5E,aAAa,CAACE,kBAAkB;AAC/F;AAQA,OAAO,SAAS2E,QAAQA,CAACJ,IAAiB,EAAkB;EAC1D,MAAMC,MAAM,GAAG,IAAIC,WAAW,CAACF,IAAI,EAAE,CAAC,EAAEzE,aAAa,CAACG,aAAa,CAAC;EAEpE,MAAM2E,SAAS,GAAGJ,MAAM,CAAC1E,aAAa,CAACM,kBAAkB,CAAC;EAC1D,MAAMyE,WAAW,GAAGL,MAAM,CAAC1E,aAAa,CAACO,kBAAkB,CAAC;EAC5D,MAAMyE,YAAY,GAAGpE,iBAAiB,CAACkE,SAAS,CAAC,IAAI,EAAE;EACvD,MAAMG,cAAc,GAAGD,YAAY,CAACE,MAAM,GAAG,CAAC,IAAIH,WAAW,GAAGC,YAAY,CAAC,CAAC,CAAC,GAAGA,YAAY,CAAC,CAAC,CAAC;EAEjG,MAAMG,YAAY,GAAG7B,kBAAkB,CAACwB,SAAS,CAAC;EAElD,MAAMM,YAAY,GAAGV,MAAM,CAAC1E,aAAa,CAACU,iBAAiB,CAAC;EAE5D,MAAM2E,KAAK,GAAGX,MAAM,CAAC1E,aAAa,CAACS,WAAW,CAAC;EAC/C,MAAM6E,MAAM,GAAGZ,MAAM,CAAC1E,aAAa,CAACQ,YAAY,CAAC;EAEjD,MAAM+E,UAAU,GAAGvF,aAAa,CAACI,WAAW,GAAGsE,MAAM,CAAC1E,aAAa,CAACW,mBAAmB,CAAC;EAExF,MAAM6E,KAAK,GAAG,IAAIC,UAAU,CAAChB,IAAI,EAAEc,UAAU,CAAC;EAE9C,OAAOxF,mBAAmB,CAACyF,KAAK,EAAE;IAChCJ,YAAY;IACZC,KAAK;IACLC,MAAM;IACNH,YAAY;IACZF;EACF,CAAC,CAAC;AACJ;AAGA,SAAS1B,aAAaA,CAAC8B,KAAa,EAAEC,MAAc,EAAU;EAC5DD,KAAK,GAAGK,IAAI,CAACC,GAAG,CAACN,KAAK,EAAE,EAAE,CAAC;EAC3BC,MAAM,GAAGI,IAAI,CAACC,GAAG,CAACL,MAAM,EAAE,CAAC,CAAC;EAE5B,OAAQD,KAAK,GAAGC,MAAM,GAAI,CAAC;AAC7B;AAGA,SAAS9B,aAAaA,CAAC6B,KAAa,EAAEC,MAAc,EAAU;EAC5DD,KAAK,GAAGK,IAAI,CAACC,GAAG,CAACN,KAAK,EAAE,CAAC,CAAC;EAC1BC,MAAM,GAAGI,IAAI,CAACC,GAAG,CAACL,MAAM,EAAE,CAAC,CAAC;EAE5B,OAAQD,KAAK,GAAGC,MAAM,GAAI,CAAC;AAC7B;AAYA,SAAS7B,eAAeA,CAAC4B,KAAa,EAAEC,MAAc,EAAU;EAC9D,OAAOI,IAAI,CAACE,KAAK,CAAC,CAACP,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAGK,IAAI,CAACE,KAAK,CAAC,CAACN,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;AACvE;AAaA,SAAS5B,iBAAiBA,CAAC2B,KAAa,EAAEC,MAAc,EAAU;EAChE,OAAOI,IAAI,CAACE,KAAK,CAAC,CAACP,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAGK,IAAI,CAACE,KAAK,CAAC,CAACN,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE;AACxE;AAGA,SAAS3B,UAAUA,CAAC0B,KAAa,EAAEC,MAAc,EAAU;EACzD,OAAOI,IAAI,CAACE,KAAK,CAAC,CAACP,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAGK,IAAI,CAACE,KAAK,CAAC,CAACN,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE;AACxE;AAGA,SAAS1B,UAAUA,CAACyB,KAAa,EAAEC,MAAc,EAAU;EACzD,OAAOI,IAAI,CAACE,KAAK,CAAC,CAACP,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAGK,IAAI,CAACE,KAAK,CAAC,CAACN,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE;AACxE;AAGA,SAASzB,UAAUA,CAACwB,KAAa,EAAEC,MAAc,EAAU;EACzD,OAAOI,IAAI,CAACE,KAAK,CAAC,CAACP,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAGK,IAAI,CAACE,KAAK,CAAC,CAACN,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE;AACxE;AAGA,SAASxB,UAAUA,CAACuB,KAAa,EAAEC,MAAc,EAAU;EACzD,OAAOI,IAAI,CAACE,KAAK,CAAC,CAACP,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAGK,IAAI,CAACE,KAAK,CAAC,CAACN,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE;AACxE;AAGA,SAASvB,UAAUA,CAACsB,KAAa,EAAEC,MAAc,EAAU;EACzD,OAAOI,IAAI,CAACE,KAAK,CAAC,CAACP,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAGK,IAAI,CAACE,KAAK,CAAC,CAACN,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE;AACxE;AAGA,SAAStB,UAAUA,CAACqB,KAAa,EAAEC,MAAc,EAAU;EACzD,OAAOI,IAAI,CAACE,KAAK,CAAC,CAACP,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAGK,IAAI,CAACE,KAAK,CAAC,CAACN,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE;AACxE;AAGA,SAASrB,UAAUA,CAACoB,KAAa,EAAEC,MAAc,EAAU;EACzD,OAAOI,IAAI,CAACE,KAAK,CAAC,CAACP,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAGK,IAAI,CAACE,KAAK,CAAC,CAACN,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE;AACxE;AAGA,SAASpB,WAAWA,CAACmB,KAAa,EAAEC,MAAc,EAAU;EAC1D,OAAOI,IAAI,CAACE,KAAK,CAAC,CAACP,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,GAAGK,IAAI,CAACE,KAAK,CAAC,CAACN,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE;AACzE;AAGA,SAASnB,WAAWA,CAACkB,KAAa,EAAEC,MAAc,EAAU;EAC1D,OAAOI,IAAI,CAACE,KAAK,CAAC,CAACP,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,GAAGK,IAAI,CAACE,KAAK,CAAC,CAACN,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE;AACzE;AAGA,SAASlB,WAAWA,CAACiB,KAAa,EAAEC,MAAc,EAAU;EAC1D,OAAOI,IAAI,CAACE,KAAK,CAAC,CAACP,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,GAAGK,IAAI,CAACE,KAAK,CAAC,CAACN,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE;AACzE;AAGA,SAASjB,YAAYA,CAACgB,KAAa,EAAEC,MAAc,EAAU;EAC3D,OAAOI,IAAI,CAACE,KAAK,CAAC,CAACP,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,GAAGK,IAAI,CAACE,KAAK,CAAC,CAACN,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE;AAC1E;AAGA,SAAShB,YAAYA,CAACe,KAAa,EAAEC,MAAc,EAAU;EAC3D,OAAOI,IAAI,CAACE,KAAK,CAAC,CAACP,KAAK,GAAG,EAAE,IAAI,EAAE,CAAC,GAAGK,IAAI,CAACE,KAAK,CAAC,CAACN,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE;AAC3E;AAGA,SAASf,YAAYA,CAACc,KAAa,EAAEC,MAAc,EAAU;EAC3D,OAAOI,IAAI,CAACE,KAAK,CAAC,CAACP,KAAK,GAAG,EAAE,IAAI,EAAE,CAAC,GAAGK,IAAI,CAACE,KAAK,CAAC,CAACN,MAAM,GAAG,EAAE,IAAI,EAAE,CAAC,GAAG,EAAE;AAC5E"}