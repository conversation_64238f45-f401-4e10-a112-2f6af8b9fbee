{"version": 3, "file": "parse-compressed-texture.js", "names": ["isKTX", "parseKTX", "isDDS", "parseDDS", "isPVR", "parsePVR", "parseCompressedTexture", "data", "Error"], "sources": ["../../../../src/lib/parsers/parse-compressed-texture.ts"], "sourcesContent": ["import type {TextureLevel} from '@loaders.gl/schema';\nimport {isKTX, parseKTX} from './parse-ktx';\nimport {isDDS, parseDDS} from './parse-dds';\nimport {isPVR, parsePVR} from './parse-pvr';\n\n/**\n * Deduces format and parses compressed texture loaded in ArrayBuffer\n * @param data - binary data of compressed texture\n * @returns Array of the texture levels\n */\nexport function parseCompressedTexture(data: ArrayBuffer): TextureLevel[] {\n  if (isKTX(data)) {\n    // TODO: remove @ts-ignore when `parseKTX` output is normalized to loaders.gl texture format\n    // @ts-ignore\n    return parseKTX(data);\n  }\n  if (isDDS(data)) {\n    return parseDDS(data);\n  }\n  if (isPVR(data)) {\n    return parsePVR(data);\n  }\n  throw new Error('Texture container format not recognized');\n}\n"], "mappings": "AACA,SAAQA,KAAK,EAAEC,QAAQ,QAAO,aAAa;AAC3C,SAAQC,KAAK,EAAEC,QAAQ,QAAO,aAAa;AAC3C,SAAQC,KAAK,EAAEC,QAAQ,QAAO,aAAa;AAO3C,OAAO,SAASC,sBAAsBA,CAACC,IAAiB,EAAkB;EACxE,IAAIP,KAAK,CAACO,IAAI,CAAC,EAAE;IAGf,OAAON,QAAQ,CAACM,IAAI,CAAC;EACvB;EACA,IAAIL,KAAK,CAACK,IAAI,CAAC,EAAE;IACf,OAAOJ,QAAQ,CAACI,IAAI,CAAC;EACvB;EACA,IAAIH,KAAK,CAACG,IAAI,CAAC,EAAE;IACf,OAAOF,QAAQ,CAACE,IAAI,CAAC;EACvB;EACA,MAAM,IAAIC,KAAK,CAAC,yCAAyC,CAAC;AAC5D"}