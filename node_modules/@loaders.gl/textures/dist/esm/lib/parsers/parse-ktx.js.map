{"version": 3, "file": "parse-ktx.js", "names": ["read", "extractMipmapImages", "mapVkFormatToWebGL", "KTX2_ID", "isKTX", "data", "id", "Uint8Array", "notKTX", "byteLength", "length", "parseKTX", "arrayBuffer", "uint8Array", "ktx", "mipMapLevels", "Math", "max", "levels", "width", "pixelWidth", "height", "pixelHeight", "internalFormat", "vkFormat", "sizeFunction", "level", "uncompressedByteLength"], "sources": ["../../../../src/lib/parsers/parse-ktx.ts"], "sourcesContent": ["import type {TextureLevel} from '@loaders.gl/schema';\nimport {read} from 'ktx-parse';\nimport {extractMipmapImages} from '../utils/extract-mipmap-images';\nimport {mapVkFormatToWebGL} from '../utils/ktx-format-helper';\n\nconst KTX2_ID = [\n  // '´', 'K', 'T', 'X', '2', '0', 'ª', '\\r', '\\n', '\\x1A', '\\n'\n  0xab, 0x4b, 0x54, 0x58, 0x20, 0x32, 0x30, 0xbb, 0x0d, 0x0a, 0x1a, 0x0a\n];\n\n// eslint-disable-next-line complexity\nexport function isKTX(data: ArrayBuffer) {\n  // const id = new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n  const id = new Uint8Array(data);\n  const notKTX =\n    id.byteLength < KTX2_ID.length ||\n    id[0] !== KTX2_ID[0] || // '´'\n    id[1] !== KTX2_ID[1] || // 'K'\n    id[2] !== KTX2_ID[2] || // 'T'\n    id[3] !== KTX2_ID[3] || // 'X'\n    id[4] !== KTX2_ID[4] || // ' '\n    id[5] !== KTX2_ID[5] || // '2'\n    id[6] !== KTX2_ID[6] || // '0'\n    id[7] !== KTX2_ID[7] || // 'ª'\n    id[8] !== KTX2_ID[8] || // '\\r'\n    id[9] !== KTX2_ID[9] || // '\\n'\n    id[10] !== KTX2_ID[10] || // '\\x1A'\n    id[11] !== KTX2_ID[11]; // '\\n'\n\n  return !notKTX;\n}\n\nexport function parseKTX(arrayBuffer: ArrayBuffer): TextureLevel[] {\n  const uint8Array = new Uint8Array(arrayBuffer);\n  const ktx = read(uint8Array);\n  const mipMapLevels = Math.max(1, ktx.levels.length);\n  const width = ktx.pixelWidth;\n  const height = ktx.pixelHeight;\n  const internalFormat = mapVkFormatToWebGL(ktx.vkFormat);\n\n  return extractMipmapImages(ktx.levels, {\n    mipMapLevels,\n    width,\n    height,\n    sizeFunction: (level: any): number => level.uncompressedByteLength,\n    internalFormat\n  });\n}\n"], "mappings": "AACA,SAAQA,IAAI,QAAO,WAAW;AAC9B,SAAQC,mBAAmB,QAAO,gCAAgC;AAClE,SAAQC,kBAAkB,QAAO,4BAA4B;AAE7D,MAAMC,OAAO,GAAG,CAEd,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CACvE;AAGD,OAAO,SAASC,KAAKA,CAACC,IAAiB,EAAE;EAEvC,MAAMC,EAAE,GAAG,IAAIC,UAAU,CAACF,IAAI,CAAC;EAC/B,MAAMG,MAAM,GACVF,EAAE,CAACG,UAAU,GAAGN,OAAO,CAACO,MAAM,IAC9BJ,EAAE,CAAC,CAAC,CAAC,KAAKH,OAAO,CAAC,CAAC,CAAC,IACpBG,EAAE,CAAC,CAAC,CAAC,KAAKH,OAAO,CAAC,CAAC,CAAC,IACpBG,EAAE,CAAC,CAAC,CAAC,KAAKH,OAAO,CAAC,CAAC,CAAC,IACpBG,EAAE,CAAC,CAAC,CAAC,KAAKH,OAAO,CAAC,CAAC,CAAC,IACpBG,EAAE,CAAC,CAAC,CAAC,KAAKH,OAAO,CAAC,CAAC,CAAC,IACpBG,EAAE,CAAC,CAAC,CAAC,KAAKH,OAAO,CAAC,CAAC,CAAC,IACpBG,EAAE,CAAC,CAAC,CAAC,KAAKH,OAAO,CAAC,CAAC,CAAC,IACpBG,EAAE,CAAC,CAAC,CAAC,KAAKH,OAAO,CAAC,CAAC,CAAC,IACpBG,EAAE,CAAC,CAAC,CAAC,KAAKH,OAAO,CAAC,CAAC,CAAC,IACpBG,EAAE,CAAC,CAAC,CAAC,KAAKH,OAAO,CAAC,CAAC,CAAC,IACpBG,EAAE,CAAC,EAAE,CAAC,KAAKH,OAAO,CAAC,EAAE,CAAC,IACtBG,EAAE,CAAC,EAAE,CAAC,KAAKH,OAAO,CAAC,EAAE,CAAC;EAExB,OAAO,CAACK,MAAM;AAChB;AAEA,OAAO,SAASG,QAAQA,CAACC,WAAwB,EAAkB;EACjE,MAAMC,UAAU,GAAG,IAAIN,UAAU,CAACK,WAAW,CAAC;EAC9C,MAAME,GAAG,GAAGd,IAAI,CAACa,UAAU,CAAC;EAC5B,MAAME,YAAY,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEH,GAAG,CAACI,MAAM,CAACR,MAAM,CAAC;EACnD,MAAMS,KAAK,GAAGL,GAAG,CAACM,UAAU;EAC5B,MAAMC,MAAM,GAAGP,GAAG,CAACQ,WAAW;EAC9B,MAAMC,cAAc,GAAGrB,kBAAkB,CAACY,GAAG,CAACU,QAAQ,CAAC;EAEvD,OAAOvB,mBAAmB,CAACa,GAAG,CAACI,MAAM,EAAE;IACrCH,YAAY;IACZI,KAAK;IACLE,MAAM;IACNI,YAAY,EAAGC,KAAU,IAAaA,KAAK,CAACC,sBAAsB;IAClEJ;EACF,CAAC,CAAC;AACJ"}