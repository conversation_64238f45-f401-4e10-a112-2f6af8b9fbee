{"version": 3, "file": "parse-dds.js", "names": ["assert", "GL_EXTENSIONS_CONSTANTS", "extractMipmapImages", "DDS_CONSTANTS", "MAGIC_NUMBER", "HEADER_LENGTH", "MAGIC_NUMBER_INDEX", "HEADER_SIZE_INDEX", "HEADER_FLAGS_INDEX", "HEADER_HEIGHT_INDEX", "HEADER_WIDTH_INDEX", "MIPMAPCOUNT_INDEX", "HEADER_PF_FLAGS_INDEX", "HEADER_PF_FOURCC_INDEX", "DDSD_MIPMAPCOUNT", "DDPF_FOURCC", "DDS_PIXEL_FORMATS", "DXT1", "COMPRESSED_RGB_S3TC_DXT1_EXT", "DXT3", "COMPRESSED_RGBA_S3TC_DXT3_EXT", "DXT5", "COMPRESSED_RGBA_S3TC_DXT5_EXT", "COMPRESSED_RGB_ATC_WEBGL", "ATCA", "COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL", "ATCI", "COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL", "getATCLevelSize", "getDxt1LevelSize", "getATCALevelSize", "getDxtXLevelSize", "getATCILevelSize", "DDS_SIZE_FUNCTIONS", "isDDS", "data", "header", "Uint32Array", "magic", "parseDDS", "Int32Array", "pixelFormatNumber", "Boolean", "fourCC", "int32ToFourCC", "internalFormat", "sizeFunction", "concat", "mipMapLevels", "Math", "max", "width", "height", "dataOffset", "image", "Uint8Array", "value", "String", "fromCharCode"], "sources": ["../../../../src/lib/parsers/parse-dds.ts"], "sourcesContent": ["import type {TextureLevel} from '@loaders.gl/schema';\nimport {assert} from '@loaders.gl/loader-utils';\nimport {GL_EXTENSIONS_CONSTANTS} from '../gl-extensions';\nimport {extractMipmapImages} from '../utils/extract-mipmap-images';\n\nconst DDS_CONSTANTS = {\n  MAGIC_NUMBER: 0x20534444,\n  HEADER_LENGTH: 31,\n  MAGIC_NUMBER_INDEX: 0,\n  HEADER_SIZE_INDEX: 1,\n  HEADER_FLAGS_INDEX: 2,\n  HEADER_HEIGHT_INDEX: 3,\n  HEADER_WIDTH_INDEX: 4,\n  MIPMAPCOUNT_INDEX: 7,\n  HEADER_PF_FLAGS_INDEX: 20,\n  HEADER_PF_FOURCC_INDEX: 21,\n  DDSD_MIPMAPCOUNT: 0x20000,\n  DDPF_FOURCC: 0x4\n};\n\nconst DDS_PIXEL_FORMATS: Record<string, number> = {\n  DXT1: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_S3TC_DXT1_EXT,\n  DXT3: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT3_EXT,\n  DXT5: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT5_EXT,\n  'ATC ': GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_ATC_WEBGL,\n  ATCA: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL,\n  ATCI: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL\n};\n\nconst getATCLevelSize = getDxt1LevelSize;\nconst getATCALevelSize = getDxtXLevelSize;\nconst getATCILevelSize = getDxtXLevelSize;\n\nconst DDS_SIZE_FUNCTIONS: Record<string, (width: number, height: number) => number> = {\n  DXT1: getDxt1LevelSize,\n  DXT3: getDxtXLevelSize,\n  DXT5: getDxtXLevelSize,\n  'ATC ': getATCLevelSize,\n  ATCA: getATCALevelSize,\n  ATCI: getATCILevelSize\n};\n\n/**\n * Check if data is in \"DDS\" format by its magic number\n * @param data - binary data of compressed texture\n * @returns true - data in \"DDS\" format, else - false\n */\nexport function isDDS(data: ArrayBuffer): boolean {\n  const header = new Uint32Array(data, 0, DDS_CONSTANTS.HEADER_LENGTH);\n  const magic = header[DDS_CONSTANTS.MAGIC_NUMBER_INDEX];\n  return magic === DDS_CONSTANTS.MAGIC_NUMBER;\n}\n\n/**\n * Parse texture data as \"DDS\" format\n * @param data - binary data of compressed texture\n * @returns Array of the texture levels\n */\nexport function parseDDS(data: ArrayBuffer): TextureLevel[] {\n  const header = new Int32Array(data, 0, DDS_CONSTANTS.HEADER_LENGTH);\n  const pixelFormatNumber = header[DDS_CONSTANTS.HEADER_PF_FOURCC_INDEX];\n  assert(\n    Boolean(header[DDS_CONSTANTS.HEADER_PF_FLAGS_INDEX] & DDS_CONSTANTS.DDPF_FOURCC),\n    'DDS: Unsupported format, must contain a FourCC code'\n  );\n  const fourCC = int32ToFourCC(pixelFormatNumber);\n  const internalFormat = DDS_PIXEL_FORMATS[fourCC];\n  const sizeFunction = DDS_SIZE_FUNCTIONS[fourCC];\n  assert(internalFormat && sizeFunction, `DDS: Unknown pixel format ${pixelFormatNumber}`);\n\n  let mipMapLevels = 1;\n  if (header[DDS_CONSTANTS.HEADER_FLAGS_INDEX] & DDS_CONSTANTS.DDSD_MIPMAPCOUNT) {\n    mipMapLevels = Math.max(1, header[DDS_CONSTANTS.MIPMAPCOUNT_INDEX]);\n  }\n  const width = header[DDS_CONSTANTS.HEADER_WIDTH_INDEX];\n  const height = header[DDS_CONSTANTS.HEADER_HEIGHT_INDEX];\n  const dataOffset = header[DDS_CONSTANTS.HEADER_SIZE_INDEX] + 4;\n  const image = new Uint8Array(data, dataOffset);\n\n  return extractMipmapImages(image, {\n    mipMapLevels,\n    width,\n    height,\n    sizeFunction,\n    internalFormat\n  });\n}\n\n/**\n * DXT1 applicable function to calculate level size\n * @param width - level width\n * @param height - level height\n * @returns level size in bytes\n */\nexport function getDxt1LevelSize(width: number, height: number): number {\n  return ((width + 3) >> 2) * ((height + 3) >> 2) * 8;\n}\n\n/**\n * DXT3 & DXT5 applicable function to calculate level size\n * @param width - level width\n * @param height - level height\n * @returns level size in bytes\n */\nexport function getDxtXLevelSize(width: number, height: number): number {\n  return ((width + 3) >> 2) * ((height + 3) >> 2) * 16;\n}\n\n/**\n * Convert every byte of Int32 value to char\n * @param value - Int32 number\n * @returns string of 4 characters\n */\nfunction int32ToFourCC(value: number): string {\n  return String.fromCharCode(\n    value & 0xff,\n    (value >> 8) & 0xff,\n    (value >> 16) & 0xff,\n    (value >> 24) & 0xff\n  );\n}\n"], "mappings": "AACA,SAAQA,MAAM,QAAO,0BAA0B;AAC/C,SAAQC,uBAAuB,QAAO,kBAAkB;AACxD,SAAQC,mBAAmB,QAAO,gCAAgC;AAElE,MAAMC,aAAa,GAAG;EACpBC,YAAY,EAAE,UAAU;EACxBC,aAAa,EAAE,EAAE;EACjBC,kBAAkB,EAAE,CAAC;EACrBC,iBAAiB,EAAE,CAAC;EACpBC,kBAAkB,EAAE,CAAC;EACrBC,mBAAmB,EAAE,CAAC;EACtBC,kBAAkB,EAAE,CAAC;EACrBC,iBAAiB,EAAE,CAAC;EACpBC,qBAAqB,EAAE,EAAE;EACzBC,sBAAsB,EAAE,EAAE;EAC1BC,gBAAgB,EAAE,OAAO;EACzBC,WAAW,EAAE;AACf,CAAC;AAED,MAAMC,iBAAyC,GAAG;EAChDC,IAAI,EAAEhB,uBAAuB,CAACiB,4BAA4B;EAC1DC,IAAI,EAAElB,uBAAuB,CAACmB,6BAA6B;EAC3DC,IAAI,EAAEpB,uBAAuB,CAACqB,6BAA6B;EAC3D,MAAM,EAAErB,uBAAuB,CAACsB,wBAAwB;EACxDC,IAAI,EAAEvB,uBAAuB,CAACwB,wCAAwC;EACtEC,IAAI,EAAEzB,uBAAuB,CAAC0B;AAChC,CAAC;AAED,MAAMC,eAAe,GAAGC,gBAAgB;AACxC,MAAMC,gBAAgB,GAAGC,gBAAgB;AACzC,MAAMC,gBAAgB,GAAGD,gBAAgB;AAEzC,MAAME,kBAA6E,GAAG;EACpFhB,IAAI,EAAEY,gBAAgB;EACtBV,IAAI,EAAEY,gBAAgB;EACtBV,IAAI,EAAEU,gBAAgB;EACtB,MAAM,EAAEH,eAAe;EACvBJ,IAAI,EAAEM,gBAAgB;EACtBJ,IAAI,EAAEM;AACR,CAAC;AAOD,OAAO,SAASE,KAAKA,CAACC,IAAiB,EAAW;EAChD,MAAMC,MAAM,GAAG,IAAIC,WAAW,CAACF,IAAI,EAAE,CAAC,EAAEhC,aAAa,CAACE,aAAa,CAAC;EACpE,MAAMiC,KAAK,GAAGF,MAAM,CAACjC,aAAa,CAACG,kBAAkB,CAAC;EACtD,OAAOgC,KAAK,KAAKnC,aAAa,CAACC,YAAY;AAC7C;AAOA,OAAO,SAASmC,QAAQA,CAACJ,IAAiB,EAAkB;EAC1D,MAAMC,MAAM,GAAG,IAAII,UAAU,CAACL,IAAI,EAAE,CAAC,EAAEhC,aAAa,CAACE,aAAa,CAAC;EACnE,MAAMoC,iBAAiB,GAAGL,MAAM,CAACjC,aAAa,CAACU,sBAAsB,CAAC;EACtEb,MAAM,CACJ0C,OAAO,CAACN,MAAM,CAACjC,aAAa,CAACS,qBAAqB,CAAC,GAAGT,aAAa,CAACY,WAAW,CAAC,EAChF,qDACF,CAAC;EACD,MAAM4B,MAAM,GAAGC,aAAa,CAACH,iBAAiB,CAAC;EAC/C,MAAMI,cAAc,GAAG7B,iBAAiB,CAAC2B,MAAM,CAAC;EAChD,MAAMG,YAAY,GAAGb,kBAAkB,CAACU,MAAM,CAAC;EAC/C3C,MAAM,CAAC6C,cAAc,IAAIC,YAAY,+BAAAC,MAAA,CAA+BN,iBAAiB,CAAE,CAAC;EAExF,IAAIO,YAAY,GAAG,CAAC;EACpB,IAAIZ,MAAM,CAACjC,aAAa,CAACK,kBAAkB,CAAC,GAAGL,aAAa,CAACW,gBAAgB,EAAE;IAC7EkC,YAAY,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEd,MAAM,CAACjC,aAAa,CAACQ,iBAAiB,CAAC,CAAC;EACrE;EACA,MAAMwC,KAAK,GAAGf,MAAM,CAACjC,aAAa,CAACO,kBAAkB,CAAC;EACtD,MAAM0C,MAAM,GAAGhB,MAAM,CAACjC,aAAa,CAACM,mBAAmB,CAAC;EACxD,MAAM4C,UAAU,GAAGjB,MAAM,CAACjC,aAAa,CAACI,iBAAiB,CAAC,GAAG,CAAC;EAC9D,MAAM+C,KAAK,GAAG,IAAIC,UAAU,CAACpB,IAAI,EAAEkB,UAAU,CAAC;EAE9C,OAAOnD,mBAAmB,CAACoD,KAAK,EAAE;IAChCN,YAAY;IACZG,KAAK;IACLC,MAAM;IACNN,YAAY;IACZD;EACF,CAAC,CAAC;AACJ;AAQA,OAAO,SAAShB,gBAAgBA,CAACsB,KAAa,EAAEC,MAAc,EAAU;EACtE,OAAO,CAAED,KAAK,GAAG,CAAC,IAAK,CAAC,KAAMC,MAAM,GAAG,CAAC,IAAK,CAAC,CAAC,GAAG,CAAC;AACrD;AAQA,OAAO,SAASrB,gBAAgBA,CAACoB,KAAa,EAAEC,MAAc,EAAU;EACtE,OAAO,CAAED,KAAK,GAAG,CAAC,IAAK,CAAC,KAAMC,MAAM,GAAG,CAAC,IAAK,CAAC,CAAC,GAAG,EAAE;AACtD;AAOA,SAASR,aAAaA,CAACY,KAAa,EAAU;EAC5C,OAAOC,MAAM,CAACC,YAAY,CACxBF,KAAK,GAAG,IAAI,EACXA,KAAK,IAAI,CAAC,GAAI,IAAI,EAClBA,KAAK,IAAI,EAAE,GAAI,IAAI,EACnBA,KAAK,IAAI,EAAE,GAAI,IAClB,CAAC;AACH"}