import { isBrowser } from '@loaders.gl/worker-utils';
import { VERSION } from './lib/utils/version';
import parseBasis from './lib/parsers/parse-basis';
export const BasisWorkerLoader = {
  name: 'Basis',
  id: isBrowser ? 'basis' : 'basis-nodejs',
  module: 'textures',
  version: VERSION,
  worker: true,
  extensions: ['basis', 'ktx2'],
  mimeTypes: ['application/octet-stream', 'image/ktx2'],
  tests: ['sB'],
  binary: true,
  options: {
    basis: {
      format: 'auto',
      libraryPath: 'libs/',
      containerFormat: 'auto',
      module: 'transcoder'
    }
  }
};
export const BasisLoader = {
  ...BasisWorkerLoader,
  parse: parseBasis
};
export const _TypecheckBasisWorkerLoader = BasisWorkerLoader;
export const _TypecheckBasisLoader = BasisLoader;
//# sourceMappingURL=basis-loader.js.map