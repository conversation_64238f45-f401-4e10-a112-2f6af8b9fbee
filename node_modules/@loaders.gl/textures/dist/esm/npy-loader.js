import { VERSION } from './lib/utils/version';
import { parseNPY } from './lib/parsers/parse-npy';
const NPY_MAGIC_NUMBER = new Uint8Array([147, 78, 85, 77, 80, 89]);
export const NPYWorkerLoader = {
  name: 'NPY',
  id: 'npy',
  module: 'textures',
  version: VERSION,
  worker: true,
  extensions: ['npy'],
  mimeTypes: [],
  tests: [NPY_MAGIC_NUMBER.buffer],
  options: {
    npy: {}
  }
};
export const NPYLoader = {
  ...NPYWorkerLoader,
  parseSync: parseNPY,
  parse: async (arrayBuffer, options) => parseNPY(arrayBuffer, options)
};
export const _TypecheckNPYWorkerLoader = NPYWorkerLoader;
export const _TypecheckNPYLoader = NPYLoader;
//# sourceMappingURL=npy-loader.js.map