{"version": 3, "file": "compressed-texture-writer.js", "names": ["VERSION", "encodeImageURLToCompressedTextureURL", "CompressedTextureWriter", "name", "id", "module", "version", "extensions", "options", "texture", "format", "compression", "quality", "mipmap", "flipY", "toolFlags", "encodeURLtoURL", "_TypecheckCompressedTextureWriter"], "sources": ["../../src/compressed-texture-writer.ts"], "sourcesContent": ["import type {Writer, WriterOptions} from '@loaders.gl/loader-utils';\nimport {VERSION} from './lib/utils/version';\nimport {encodeImageURLToCompressedTextureURL} from './lib/encoders/encode-texture';\n\nexport type CompressedTextureWriterOptions = WriterOptions & {\n  cwd?: string;\n  texture?: {\n    format: string;\n    compression: string;\n    quality: string;\n    mipmap: boolean;\n    flipY: boolean;\n    toolFlags: string;\n  };\n};\n\n/**\n * DDS Texture Container Exporter\n */\nexport const CompressedTextureWriter = {\n  name: 'DDS Texture Container',\n  id: 'dds',\n  module: 'textures',\n  version: VERSION,\n\n  extensions: ['dds'],\n\n  options: {\n    texture: {\n      format: 'auto',\n      compression: 'auto',\n      quality: 'auto',\n      mipmap: false,\n      flipY: false,\n      toolFlags: ''\n    }\n  },\n\n  encodeURLtoURL: encodeImageURLToCompressedTextureURL\n};\n\n// TYPE TESTS - TODO find a better way than exporting junk\nexport const _TypecheckCompressedTextureWriter: Writer & {\n  encodeURLtoURL: (\n    inputUrl: string,\n    outputUrl: string,\n    options?: CompressedTextureWriterOptions\n  ) => Promise<string>;\n} = CompressedTextureWriter;\n"], "mappings": "AACA,SAAQA,OAAO,QAAO,qBAAqB;AAC3C,SAAQC,oCAAoC,QAAO,+BAA+B;AAiBlF,OAAO,MAAMC,uBAAuB,GAAG;EACrCC,IAAI,EAAE,uBAAuB;EAC7BC,EAAE,EAAE,KAAK;EACTC,MAAM,EAAE,UAAU;EAClBC,OAAO,EAAEN,OAAO;EAEhBO,UAAU,EAAE,CAAC,KAAK,CAAC;EAEnBC,OAAO,EAAE;IACPC,OAAO,EAAE;MACPC,MAAM,EAAE,MAAM;MACdC,WAAW,EAAE,MAAM;MACnBC,OAAO,EAAE,MAAM;MACfC,MAAM,EAAE,KAAK;MACbC,KAAK,EAAE,KAAK;MACZC,SAAS,EAAE;IACb;EACF,CAAC;EAEDC,cAAc,EAAEf;AAClB,CAAC;AAGD,OAAO,MAAMgB,iCAMZ,GAAGf,uBAAuB"}