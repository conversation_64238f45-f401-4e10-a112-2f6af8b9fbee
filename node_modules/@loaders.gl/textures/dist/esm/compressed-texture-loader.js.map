{"version": 3, "file": "compressed-texture-loader.js", "names": ["VERSION", "parseCompressedTexture", "parseBasis", "DEFAULT_TEXTURE_LOADER_OPTIONS", "libraryPath", "useBasis", "CompressedTextureWorkerLoader", "name", "id", "module", "version", "worker", "extensions", "mimeTypes", "binary", "options", "CompressedTextureLoader", "parse", "arrayBuffer", "basis", "format", "alpha", "noAlpha", "containerFormat", "_TypecheckCompressedTextureWorkerLoader", "_TypecheckCompressedTextureLoader"], "sources": ["../../src/compressed-texture-loader.ts"], "sourcesContent": ["import type {Loader, LoaderWithParser} from '@loaders.gl/loader-utils';\nimport {VERSION} from './lib/utils/version';\nimport {parseCompressedTexture} from './lib/parsers/parse-compressed-texture';\nimport parseBasis from './lib/parsers/parse-basis';\n\nexport type TextureLoaderOptions = {\n  'compressed-texture'?: {\n    libraryPath?: string;\n    useBasis?: boolean;\n  };\n};\n\nconst DEFAULT_TEXTURE_LOADER_OPTIONS = {\n  'compressed-texture': {\n    libraryPath: 'libs/',\n    useBasis: false\n  }\n};\n\n/**\n * Worker Loader for KTX, DDS, and PVR texture container formats\n */\nexport const CompressedTextureWorkerLoader = {\n  name: 'Texture Containers',\n  id: 'compressed-texture',\n  module: 'textures',\n  version: VERSION,\n  worker: true,\n  extensions: [\n    'ktx',\n    'ktx2',\n    'dds', // WEBGL_compressed_texture_s3tc, WEBGL_compressed_texture_atc\n    'pvr' // WEBGL_compressed_texture_pvrtc\n  ],\n  mimeTypes: [\n    'image/ktx2',\n    'image/ktx',\n    'image/vnd-ms.dds',\n    'image/x-dds',\n    'application/octet-stream'\n  ],\n  binary: true,\n  options: DEFAULT_TEXTURE_LOADER_OPTIONS\n};\n\n/**\n * Loader for KTX, DDS, and PVR texture container formats\n */\nexport const CompressedTextureLoader = {\n  ...CompressedTextureWorkerLoader,\n  parse: async (arrayBuffer, options) => {\n    if (options['compressed-texture'].useBasis) {\n      options.basis = {\n        format: {\n          alpha: 'BC3',\n          noAlpha: 'BC1'\n        },\n        ...options.basis,\n        containerFormat: 'ktx2',\n        module: 'encoder'\n      };\n      return (await parseBasis(arrayBuffer, options))[0];\n    }\n    return parseCompressedTexture(arrayBuffer);\n  }\n};\n\n// TYPE TESTS - TODO find a better way than exporting junk\nexport const _TypecheckCompressedTextureWorkerLoader: Loader = CompressedTextureWorkerLoader;\nexport const _TypecheckCompressedTextureLoader: LoaderWithParser = CompressedTextureLoader;\n"], "mappings": "AACA,SAAQA,OAAO,QAAO,qBAAqB;AAC3C,SAAQC,sBAAsB,QAAO,wCAAwC;AAC7E,OAAOC,UAAU,MAAM,2BAA2B;AASlD,MAAMC,8BAA8B,GAAG;EACrC,oBAAoB,EAAE;IACpBC,WAAW,EAAE,OAAO;IACpBC,QAAQ,EAAE;EACZ;AACF,CAAC;AAKD,OAAO,MAAMC,6BAA6B,GAAG;EAC3CC,IAAI,EAAE,oBAAoB;EAC1BC,EAAE,EAAE,oBAAoB;EACxBC,MAAM,EAAE,UAAU;EAClBC,OAAO,EAAEV,OAAO;EAChBW,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,CACV,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,CACN;EACDC,SAAS,EAAE,CACT,YAAY,EACZ,WAAW,EACX,kBAAkB,EAClB,aAAa,EACb,0BAA0B,CAC3B;EACDC,MAAM,EAAE,IAAI;EACZC,OAAO,EAAEZ;AACX,CAAC;AAKD,OAAO,MAAMa,uBAAuB,GAAG;EACrC,GAAGV,6BAA6B;EAChCW,KAAK,EAAE,MAAAA,CAAOC,WAAW,EAAEH,OAAO,KAAK;IACrC,IAAIA,OAAO,CAAC,oBAAoB,CAAC,CAACV,QAAQ,EAAE;MAC1CU,OAAO,CAACI,KAAK,GAAG;QACdC,MAAM,EAAE;UACNC,KAAK,EAAE,KAAK;UACZC,OAAO,EAAE;QACX,CAAC;QACD,GAAGP,OAAO,CAACI,KAAK;QAChBI,eAAe,EAAE,MAAM;QACvBd,MAAM,EAAE;MACV,CAAC;MACD,OAAO,CAAC,MAAMP,UAAU,CAACgB,WAAW,EAAEH,OAAO,CAAC,EAAE,CAAC,CAAC;IACpD;IACA,OAAOd,sBAAsB,CAACiB,WAAW,CAAC;EAC5C;AACF,CAAC;AAGD,OAAO,MAAMM,uCAA+C,GAAGlB,6BAA6B;AAC5F,OAAO,MAAMmB,iCAAmD,GAAGT,uBAAuB"}