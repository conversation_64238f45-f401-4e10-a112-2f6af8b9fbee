import { VERSION } from './lib/utils/version';
import { encodeImageURLToCompressedTextureURL } from './lib/encoders/encode-texture';
export const CompressedTextureWriter = {
  name: 'DDS Texture Container',
  id: 'dds',
  module: 'textures',
  version: VERSION,
  extensions: ['dds'],
  options: {
    texture: {
      format: 'auto',
      compression: 'auto',
      quality: 'auto',
      mipmap: false,
      flipY: false,
      toolFlags: ''
    }
  },
  encodeURLtoURL: encodeImageURLToCompressedTextureURL
};
export const _TypecheckCompressedTextureWriter = CompressedTextureWriter;
//# sourceMappingURL=compressed-texture-writer.js.map