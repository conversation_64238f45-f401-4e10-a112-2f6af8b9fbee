{"version": 3, "file": "npy-loader.js", "names": ["VERSION", "parseNPY", "NPY_MAGIC_NUMBER", "Uint8Array", "NPYWorkerLoader", "name", "id", "module", "version", "worker", "extensions", "mimeTypes", "tests", "buffer", "options", "npy", "NPYLoader", "parseSync", "parse", "arrayBuffer", "_TypecheckNPYWorkerLoader", "_TypecheckNPYLoader"], "sources": ["../../src/npy-loader.ts"], "sourcesContent": ["import type {Loader, LoaderWithParser, LoaderOptions} from '@loaders.gl/loader-utils';\nimport {VERSION} from './lib/utils/version';\nimport {parseNPY} from './lib/parsers/parse-npy';\n\n// \\x93NUMPY\nconst NPY_MAGIC_NUMBER = new Uint8Array([147, 78, 85, 77, 80, 89]);\n\n/**\n * Worker loader for numpy \"tiles\"\n */\nexport const NPYWorkerLoader = {\n  name: 'NPY',\n  id: 'npy',\n  module: 'textures',\n  version: VERSION,\n  worker: true,\n  extensions: ['npy'],\n  mimeTypes: [],\n  tests: [NPY_MAGIC_NUMBER.buffer],\n  options: {\n    npy: {}\n  }\n};\n\n/**\n * Loader for numpy \"tiles\"\n */\nexport const NPYLoader = {\n  ...NPYWorkerLoader,\n  parseSync: parseNPY,\n  parse: async (arrayBuffer: ArrayBuffer, options?: LoaderOptions) => parseNPY(arrayBuffer, options)\n};\n\n// TYPE TESTS - TODO find a better way than exporting junk\nexport const _TypecheckNPYWorkerLoader: Loader = NPYWorkerLoader;\nexport const _TypecheckNPYLoader: LoaderWithParser = NPYLoader;\n"], "mappings": "AACA,SAAQA,OAAO,QAAO,qBAAqB;AAC3C,SAAQC,QAAQ,QAAO,yBAAyB;AAGhD,MAAMC,gBAAgB,GAAG,IAAIC,UAAU,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AAKlE,OAAO,MAAMC,eAAe,GAAG;EAC7BC,IAAI,EAAE,KAAK;EACXC,EAAE,EAAE,KAAK;EACTC,MAAM,EAAE,UAAU;EAClBC,OAAO,EAAER,OAAO;EAChBS,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,CAAC,KAAK,CAAC;EACnBC,SAAS,EAAE,EAAE;EACbC,KAAK,EAAE,CAACV,gBAAgB,CAACW,MAAM,CAAC;EAChCC,OAAO,EAAE;IACPC,GAAG,EAAE,CAAC;EACR;AACF,CAAC;AAKD,OAAO,MAAMC,SAAS,GAAG;EACvB,GAAGZ,eAAe;EAClBa,SAAS,EAAEhB,QAAQ;EACnBiB,KAAK,EAAE,MAAAA,CAAOC,WAAwB,EAAEL,OAAuB,KAAKb,QAAQ,CAACkB,WAAW,EAAEL,OAAO;AACnG,CAAC;AAGD,OAAO,MAAMM,yBAAiC,GAAGhB,eAAe;AAChE,OAAO,MAAMiB,mBAAqC,GAAGL,SAAS"}