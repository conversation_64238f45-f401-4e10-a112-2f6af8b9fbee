{"version": 3, "file": "index.js", "names": ["<PERSON><PERSON><PERSON><PERSON>", "VERSION", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BasisWorkerLoader", "CompressedTextureLoader", "CompressedTextureWorkerLoader", "CrunchLoader", "NPYLoader", "NPYWorkerLoader", "CompressedTextureWriter", "KTX2BasisWriter", "KTX2BasisWriterWorker", "name", "id", "module", "version", "extensions", "worker", "options", "useSRGB", "qualityLevel", "encodeUASTC", "mipmaps", "loadImageTexture", "loadImageTextureArray", "loadImageTextureCube", "GL_EXTENSIONS_CONSTANTS", "selectSupportedBasisFormat", "getSupportedGPUTextureFormats", "CrunchWorkerLoader"], "sources": ["../../src/index.ts"], "sourcesContent": ["import {isBrowser} from '@loaders.gl/loader-utils';\nimport {VERSION} from './lib/utils/version';\n\n// Types\nexport type {GPUTextureFormat} from '@loaders.gl/schema';\nexport type {TextureLoaderOptions} from './compressed-texture-loader';\n\n// Loaders\nexport {<PERSON><PERSON><PERSON>oa<PERSON>, BasisWorkerLoader} from './basis-loader';\nexport {CompressedTextureLoader, CompressedTextureWorkerLoader} from './compressed-texture-loader';\nexport {CrunchLoader} from './crunch-loader';\nexport {NPYLoader, NPYWorkerLoader} from './npy-loader';\n\n// Writers\nexport {CompressedTextureWriter} from './compressed-texture-writer';\nexport {KTX2BasisWriter} from './ktx2-basis-writer';\n\nexport const KTX2BasisWriterWorker = {\n  name: 'Basis Universal Supercompressed GPU Texture',\n  id: isBrowser ? 'ktx2-basis-writer' : 'ktx2-basis-writer-nodejs',\n  module: 'textures',\n  version: VERSION,\n  extensions: ['ktx2'],\n  worker: true,\n  options: {\n    useSRGB: false,\n    qualityLevel: 10,\n    encodeUASTC: false,\n    mipmaps: false\n  }\n};\n\n// Texture Loading API\nexport {loadImageTexture} from './lib/texture-api/load-image';\nexport {loadImageTextureArray} from './lib/texture-api/load-image-array';\nexport {loadImageTextureCube} from './lib/texture-api/load-image-cube';\n\n// Utilities\nexport {GL_EXTENSIONS_CONSTANTS} from './lib/gl-extensions';\nexport {selectSupportedBasisFormat} from './lib/parsers/parse-basis';\nexport {getSupportedGPUTextureFormats} from './lib/utils/texture-formats';\n\n// DEPRECATED\nexport {CrunchLoader as CrunchWorkerLoader} from './crunch-loader';\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,0BAA0B;AAClD,SAAQC,OAAO,QAAO,qBAAqB;AAO3C,SAAQC,WAAW,EAAEC,iBAAiB,QAAO,gBAAgB;AAC7D,SAAQC,uBAAuB,EAAEC,6BAA6B,QAAO,6BAA6B;AAClG,SAAQC,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,SAAS,EAAEC,eAAe,QAAO,cAAc;AAGvD,SAAQC,uBAAuB,QAAO,6BAA6B;AACnE,SAAQC,eAAe,QAAO,qBAAqB;AAEnD,OAAO,MAAMC,qBAAqB,GAAG;EACnCC,IAAI,EAAE,6CAA6C;EACnDC,EAAE,EAAEb,SAAS,GAAG,mBAAmB,GAAG,0BAA0B;EAChEc,MAAM,EAAE,UAAU;EAClBC,OAAO,EAAEd,OAAO;EAChBe,UAAU,EAAE,CAAC,MAAM,CAAC;EACpBC,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE;IACPC,OAAO,EAAE,KAAK;IACdC,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE,KAAK;IAClBC,OAAO,EAAE;EACX;AACF,CAAC;AAGD,SAAQC,gBAAgB,QAAO,8BAA8B;AAC7D,SAAQC,qBAAqB,QAAO,oCAAoC;AACxE,SAAQC,oBAAoB,QAAO,mCAAmC;AAGtE,SAAQC,uBAAuB,QAAO,qBAAqB;AAC3D,SAAQC,0BAA0B,QAAO,2BAA2B;AACpE,SAAQC,6BAA6B,QAAO,6BAA6B;AAGzE,SAAQtB,YAAY,IAAIuB,kBAAkB,QAAO,iBAAiB"}