{"version": 3, "file": "crunch-worker.js", "names": ["createLoaderWorker", "CrunchLoader", "parseCrunch", "CrunchLoaderWithParser", "parse"], "sources": ["../../../src/workers/crunch-worker.ts"], "sourcesContent": ["import {createLoaderWorker} from '@loaders.gl/loader-utils';\nimport {CrunchLoader} from '../crunch-loader';\nimport {parseCrunch} from '../lib/parsers/parse-crunch';\n\n/**\n * Loader for the Crunch compressed texture container format\n */\nexport const CrunchLoaderWithParser = {\n  ...CrunchLoader,\n  parse: parseCrunch\n};\n\ncreateLoaderWorker(CrunchLoaderWithParser);\n"], "mappings": "AAAA,SAAQA,kBAAkB,QAAO,0BAA0B;AAC3D,SAAQC,YAAY,QAAO,kBAAkB;AAC7C,SAAQC,WAAW,QAAO,6BAA6B;AAKvD,OAAO,MAAMC,sBAAsB,GAAG;EACpC,GAAGF,YAAY;EACfG,KAAK,EAAEF;AACT,CAAC;AAEDF,kBAAkB,CAACG,sBAAsB,CAAC"}