{"version": 3, "file": "compressed-texture-worker.js", "names": ["createLoaderWorker", "CompressedTextureLoader"], "sources": ["../../../src/workers/compressed-texture-worker.ts"], "sourcesContent": ["import {createLoaderWorker} from '@loaders.gl/loader-utils';\nimport {CompressedTextureLoader} from '../compressed-texture-loader';\n\ncreateLoaderWorker(CompressedTextureLoader);\n"], "mappings": "AAAA,SAAQA,kBAAkB,QAAO,0BAA0B;AAC3D,SAAQC,uBAAuB,QAAO,8BAA8B;AAEpED,kBAAkB,CAACC,uBAAuB,CAAC"}