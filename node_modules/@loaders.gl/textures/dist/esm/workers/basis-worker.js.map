{"version": 3, "file": "basis-worker.js", "names": ["createLoaderWorker", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["../../../src/workers/basis-worker.ts"], "sourcesContent": ["import {createLoaderWorker} from '@loaders.gl/loader-utils';\nimport {BasisLoader} from '../basis-loader';\n\ncreateLoaderWorker(BasisLoader);\n"], "mappings": "AAAA,SAAQA,kBAAkB,QAAO,0BAA0B;AAC3D,SAAQC,WAAW,QAAO,iBAAiB;AAE3CD,kBAAkB,CAACC,WAAW,CAAC"}