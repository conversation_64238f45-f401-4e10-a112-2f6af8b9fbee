{"version": 3, "file": "ktx2-basis-writer-worker.js", "names": ["WorkerBody", "KTX2BasisWriter", "inWorkerThread", "onmessage", "type", "payload", "input", "options", "result", "encode", "postMessage", "error", "message", "Error"], "sources": ["../../../src/workers/ktx2-basis-writer-worker.ts"], "sourcesContent": ["import {WorkerBody, WorkerMessagePayload} from '@loaders.gl/worker-utils';\nimport {KTX2BasisWriter} from '../ktx2-basis-writer';\n\n(() => {\n  // Check that we are actually in a worker thread\n  if (!WorkerBody.inWorkerThread()) {\n    return;\n  }\n\n  WorkerBody.onmessage = async (type, payload: WorkerMessagePayload) => {\n    switch (type) {\n      case 'process':\n        try {\n          const {input, options} = payload;\n          const result = await KTX2BasisWriter.encode(input, options);\n          WorkerBody.postMessage('done', {result});\n        } catch (error) {\n          const message = error instanceof Error ? error.message : '';\n          WorkerBody.postMessage('error', {error: message});\n        }\n        break;\n      default:\n    }\n  };\n})();\n"], "mappings": "AAAA,SAAQA,UAAU,QAA6B,0BAA0B;AACzE,SAAQC,eAAe,QAAO,sBAAsB;AAEpD,CAAC,MAAM;EAEL,IAAI,CAACD,UAAU,CAACE,cAAc,CAAC,CAAC,EAAE;IAChC;EACF;EAEAF,UAAU,CAACG,SAAS,GAAG,OAAOC,IAAI,EAAEC,OAA6B,KAAK;IACpE,QAAQD,IAAI;MACV,KAAK,SAAS;QACZ,IAAI;UACF,MAAM;YAACE,KAAK;YAAEC;UAAO,CAAC,GAAGF,OAAO;UAChC,MAAMG,MAAM,GAAG,MAAMP,eAAe,CAACQ,MAAM,CAACH,KAAK,EAAEC,OAAO,CAAC;UAC3DP,UAAU,CAACU,WAAW,CAAC,MAAM,EAAE;YAACF;UAAM,CAAC,CAAC;QAC1C,CAAC,CAAC,OAAOG,KAAK,EAAE;UACd,MAAMC,OAAO,GAAGD,KAAK,YAAYE,KAAK,GAAGF,KAAK,CAACC,OAAO,GAAG,EAAE;UAC3DZ,UAAU,CAACU,WAAW,CAAC,OAAO,EAAE;YAACC,KAAK,EAAEC;UAAO,CAAC,CAAC;QACnD;QACA;MACF;IACF;EACF,CAAC;AACH,CAAC,EAAE,CAAC"}