import { WorkerBody } from '@loaders.gl/worker-utils';
import { KTX2BasisWriter } from '../ktx2-basis-writer';
(() => {
  if (!WorkerBody.inWorkerThread()) {
    return;
  }
  WorkerBody.onmessage = async (type, payload) => {
    switch (type) {
      case 'process':
        try {
          const {
            input,
            options
          } = payload;
          const result = await KTX2BasisWriter.encode(input, options);
          WorkerBody.postMessage('done', {
            result
          });
        } catch (error) {
          const message = error instanceof Error ? error.message : '';
          WorkerBody.postMessage('error', {
            error: message
          });
        }
        break;
      default:
    }
  };
})();
//# sourceMappingURL=ktx2-basis-writer-worker.js.map