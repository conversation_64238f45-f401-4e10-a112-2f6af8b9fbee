{"version": 3, "file": "crunch-loader.js", "names": ["_version", "require", "CrunchLoader", "id", "name", "module", "version", "VERSION", "worker", "extensions", "mimeTypes", "binary", "options", "crunch", "libraryPath", "exports", "_TypecheckCrunchLoader"], "sources": ["../../src/crunch-loader.ts"], "sourcesContent": ["import type {Loader} from '@loaders.gl/loader-utils';\nimport {VERSION} from './lib/utils/version';\n\n/**\n * Worker loader for the Crunch compressed texture container format\n */\nexport const CrunchLoader = {\n  id: 'crunch',\n  name: 'Crunch',\n  module: 'textures',\n  version: VERSION,\n  worker: true,\n  extensions: ['crn'],\n  mimeTypes: ['image/crn', 'image/x-crn', 'application/octet-stream'],\n  binary: true,\n  options: {\n    crunch: {\n      libraryPath: 'libs/'\n    }\n  }\n};\n\n// We avoid bundling crunch - rare format, only offer worker loader\n\n// TYPE TESTS - TODO find a better way than exporting junk\nexport const _TypecheckCrunchLoader: Loader = CrunchLoader;\n"], "mappings": ";;;;;;AACA,IAAAA,QAAA,GAAAC,OAAA;AAKO,MAAMC,YAAY,GAAG;EAC1BC,EAAE,EAAE,QAAQ;EACZC,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE,UAAU;EAClBC,OAAO,EAAEC,gBAAO;EAChBC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,CAAC,KAAK,CAAC;EACnBC,SAAS,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,0BAA0B,CAAC;EACnEC,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE;IACPC,MAAM,EAAE;MACNC,WAAW,EAAE;IACf;EACF;AACF,CAAC;AAACC,OAAA,CAAAb,YAAA,GAAAA,YAAA;AAKK,MAAMc,sBAA8B,GAAGd,YAAY;AAACa,OAAA,CAAAC,sBAAA,GAAAA,sBAAA"}