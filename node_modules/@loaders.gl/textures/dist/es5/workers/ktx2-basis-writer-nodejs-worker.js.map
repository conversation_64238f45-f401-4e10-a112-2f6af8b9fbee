{"version": 3, "file": "ktx2-basis-writer-nodejs-worker.js", "names": ["_workerUtils", "require", "_ktx2BasisWriterNodejs", "WorkerBody", "inWorkerThread", "onmessage", "type", "payload", "input", "options", "result", "KTX2BasisWriterNodeJS", "encode", "postMessage", "error", "message", "Error"], "sources": ["../../../src/workers/ktx2-basis-writer-nodejs-worker.ts"], "sourcesContent": ["import {WorkerBody, WorkerMessagePayload} from '@loaders.gl/worker-utils';\nimport {KTX2BasisWriterNodeJS} from '../ktx2-basis-writer-nodejs';\n\n(() => {\n  // Check that we are actually in a worker thread\n  if (!WorkerBody.inWorkerThread()) {\n    return;\n  }\n\n  WorkerBody.onmessage = async (type, payload: WorkerMessagePayload) => {\n    switch (type) {\n      case 'process':\n        try {\n          const {input, options} = payload;\n          const result = await KTX2BasisWriterNodeJS.encode(input, options);\n          WorkerBody.postMessage('done', {result});\n        } catch (error) {\n          const message = error instanceof Error ? error.message : '';\n          WorkerBody.postMessage('error', {error: message});\n        }\n        break;\n      default:\n    }\n  };\n})();\n"], "mappings": ";;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,sBAAA,GAAAD,OAAA;AAEA,CAAC,MAAM;EAEL,IAAI,CAACE,uBAAU,CAACC,cAAc,CAAC,CAAC,EAAE;IAChC;EACF;EAEAD,uBAAU,CAACE,SAAS,GAAG,OAAOC,IAAI,EAAEC,OAA6B,KAAK;IACpE,QAAQD,IAAI;MACV,KAAK,SAAS;QACZ,IAAI;UACF,MAAM;YAACE,KAAK;YAAEC;UAAO,CAAC,GAAGF,OAAO;UAChC,MAAMG,MAAM,GAAG,MAAMC,4CAAqB,CAACC,MAAM,CAACJ,KAAK,EAAEC,OAAO,CAAC;UACjEN,uBAAU,CAACU,WAAW,CAAC,MAAM,EAAE;YAACH;UAAM,CAAC,CAAC;QAC1C,CAAC,CAAC,OAAOI,KAAK,EAAE;UACd,MAAMC,OAAO,GAAGD,KAAK,YAAYE,KAAK,GAAGF,KAAK,CAACC,OAAO,GAAG,EAAE;UAC3DZ,uBAAU,CAACU,WAAW,CAAC,OAAO,EAAE;YAACC,KAAK,EAAEC;UAAO,CAAC,CAAC;QACnD;QACA;MACF;IACF;EACF,CAAC;AACH,CAAC,EAAE,CAAC"}