{"version": 3, "file": "npy-worker.js", "names": ["_n<PERSON><PERSON><PERSON><PERSON>", "require", "_loaderUtils", "createLoaderWorker", "NPYLoader"], "sources": ["../../../src/workers/npy-worker.ts"], "sourcesContent": ["import {NPYLoader} from '../npy-loader';\nimport {createLoaderWorker} from '@loaders.gl/loader-utils';\n\ncreateLoaderWorker(NPYLoader);\n"], "mappings": ";;AAAA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,+BAAkB,EAACC,oBAAS,CAAC"}