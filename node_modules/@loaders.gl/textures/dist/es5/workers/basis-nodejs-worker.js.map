{"version": 3, "file": "basis-nodejs-worker.js", "names": ["_loaderUtils", "require", "_basisLoader", "createLoaderWorker", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["../../../src/workers/basis-nodejs-worker.ts"], "sourcesContent": ["import {createLoaderWorker} from '@loaders.gl/loader-utils';\nimport '@loaders.gl/polyfills';\nimport {BasisLoader} from '../basis-loader';\n\ncreateLoaderWorker(BasisLoader);\n"], "mappings": ";;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACAA,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,+BAAkB,EAACC,wBAAW,CAAC"}