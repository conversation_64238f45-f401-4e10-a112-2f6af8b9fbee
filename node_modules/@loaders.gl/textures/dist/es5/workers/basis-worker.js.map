{"version": 3, "file": "basis-worker.js", "names": ["_loaderUtils", "require", "_basisLoader", "createLoaderWorker", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["../../../src/workers/basis-worker.ts"], "sourcesContent": ["import {createLoaderWorker} from '@loaders.gl/loader-utils';\nimport {BasisLoader} from '../basis-loader';\n\ncreateLoaderWorker(BasisLoader);\n"], "mappings": ";;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,+BAAkB,EAACC,wBAAW,CAAC"}