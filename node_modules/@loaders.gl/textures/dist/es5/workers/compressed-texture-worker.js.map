{"version": 3, "file": "compressed-texture-worker.js", "names": ["_loaderUtils", "require", "_compressedTextureLoader", "createLoaderWorker", "CompressedTextureLoader"], "sources": ["../../../src/workers/compressed-texture-worker.ts"], "sourcesContent": ["import {createLoaderWorker} from '@loaders.gl/loader-utils';\nimport {CompressedTextureLoader} from '../compressed-texture-loader';\n\ncreateLoaderWorker(CompressedTextureLoader);\n"], "mappings": ";;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,wBAAA,GAAAD,OAAA;AAEA,IAAAE,+BAAkB,EAACC,gDAAuB,CAAC"}