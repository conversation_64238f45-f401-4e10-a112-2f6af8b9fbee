{"version": 3, "file": "crunch-worker.js", "names": ["_loaderUtils", "require", "_crunchLoader", "_parseCrunch", "CrunchLoaderWithParser", "CrunchLoader", "parse", "parseCrunch", "exports", "createLoaderWorker"], "sources": ["../../../src/workers/crunch-worker.ts"], "sourcesContent": ["import {createLoaderWorker} from '@loaders.gl/loader-utils';\nimport {CrunchLoader} from '../crunch-loader';\nimport {parseCrunch} from '../lib/parsers/parse-crunch';\n\n/**\n * Loader for the Crunch compressed texture container format\n */\nexport const CrunchLoaderWithParser = {\n  ...CrunchLoader,\n  parse: parseCrunch\n};\n\ncreateLoaderWorker(CrunchLoaderWithParser);\n"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,aAAA,GAAAD,OAAA;AACA,IAAAE,YAAA,GAAAF,OAAA;AAKO,MAAMG,sBAAsB,GAAG;EACpC,GAAGC,0BAAY;EACfC,KAAK,EAAEC;AACT,CAAC;AAACC,OAAA,CAAAJ,sBAAA,GAAAA,sBAAA;AAEF,IAAAK,+BAAkB,EAACL,sBAAsB,CAAC"}