{"version": 3, "file": "ktx2-basis-writer-worker.js", "names": ["_workerUtils", "require", "_ktx2BasisWriter", "WorkerBody", "inWorkerThread", "onmessage", "type", "payload", "input", "options", "result", "KTX2BasisWriter", "encode", "postMessage", "error", "message", "Error"], "sources": ["../../../src/workers/ktx2-basis-writer-worker.ts"], "sourcesContent": ["import {WorkerBody, WorkerMessagePayload} from '@loaders.gl/worker-utils';\nimport {KTX2BasisWriter} from '../ktx2-basis-writer';\n\n(() => {\n  // Check that we are actually in a worker thread\n  if (!WorkerBody.inWorkerThread()) {\n    return;\n  }\n\n  WorkerBody.onmessage = async (type, payload: WorkerMessagePayload) => {\n    switch (type) {\n      case 'process':\n        try {\n          const {input, options} = payload;\n          const result = await KTX2BasisWriter.encode(input, options);\n          WorkerBody.postMessage('done', {result});\n        } catch (error) {\n          const message = error instanceof Error ? error.message : '';\n          WorkerBody.postMessage('error', {error: message});\n        }\n        break;\n      default:\n    }\n  };\n})();\n"], "mappings": ";;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAD,OAAA;AAEA,CAAC,MAAM;EAEL,IAAI,CAACE,uBAAU,CAACC,cAAc,CAAC,CAAC,EAAE;IAChC;EACF;EAEAD,uBAAU,CAACE,SAAS,GAAG,OAAOC,IAAI,EAAEC,OAA6B,KAAK;IACpE,QAAQD,IAAI;MACV,KAAK,SAAS;QACZ,IAAI;UACF,MAAM;YAACE,KAAK;YAAEC;UAAO,CAAC,GAAGF,OAAO;UAChC,MAAMG,MAAM,GAAG,MAAMC,gCAAe,CAACC,MAAM,CAACJ,KAAK,EAAEC,OAAO,CAAC;UAC3DN,uBAAU,CAACU,WAAW,CAAC,MAAM,EAAE;YAACH;UAAM,CAAC,CAAC;QAC1C,CAAC,CAAC,OAAOI,KAAK,EAAE;UACd,MAAMC,OAAO,GAAGD,KAAK,YAAYE,KAAK,GAAGF,KAAK,CAACC,OAAO,GAAG,EAAE;UAC3DZ,uBAAU,CAACU,WAAW,CAAC,OAAO,EAAE;YAACC,KAAK,EAAEC;UAAO,CAAC,CAAC;QACnD;QACA;MACF;IACF;EACF,CAAC;AACH,CAAC,EAAE,CAAC"}