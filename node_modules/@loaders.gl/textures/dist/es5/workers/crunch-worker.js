"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.CrunchLoaderWithParser = void 0;
var _loaderUtils = require("@loaders.gl/loader-utils");
var _crunchLoader = require("../crunch-loader");
var _parseCrunch = require("../lib/parsers/parse-crunch");
const CrunchLoaderWithParser = {
  ..._crunchLoader.CrunchLoader,
  parse: _parseCrunch.parseCrunch
};
exports.CrunchLoaderWithParser = CrunchLoaderWithParser;
(0, _loaderUtils.createLoaderWorker)(CrunchLoaderWithParser);
//# sourceMappingURL=crunch-worker.js.map