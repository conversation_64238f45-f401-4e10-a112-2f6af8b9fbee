"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports._TypecheckNPYWorkerLoader = exports._TypecheckNPYLoader = exports.NPYWorkerLoader = exports.NPYLoader = void 0;
var _version = require("./lib/utils/version");
var _parseNpy = require("./lib/parsers/parse-npy");
const NPY_MAGIC_NUMBER = new Uint8Array([147, 78, 85, 77, 80, 89]);
const NPYWorkerLoader = {
  name: 'NPY',
  id: 'npy',
  module: 'textures',
  version: _version.VERSION,
  worker: true,
  extensions: ['npy'],
  mimeTypes: [],
  tests: [NPY_MAGIC_NUMBER.buffer],
  options: {
    npy: {}
  }
};
exports.NPYWorkerLoader = NPYWorkerLoader;
const NPYLoader = {
  ...NPYWorkerLoader,
  parseSync: _parseNpy.parseNPY,
  parse: async (arrayBuffer, options) => (0, _parseNpy.parseNPY)(arrayBuffer, options)
};
exports.NPYLoader = NPYLoader;
const _TypecheckNPYWorkerLoader = NPYWorkerLoader;
exports._TypecheckNPYWorkerLoader = _TypecheckNPYWorkerLoader;
const _TypecheckNPYLoader = NPYLoader;
exports._TypecheckNPYLoader = _TypecheckNPYLoader;
//# sourceMappingURL=npy-loader.js.map