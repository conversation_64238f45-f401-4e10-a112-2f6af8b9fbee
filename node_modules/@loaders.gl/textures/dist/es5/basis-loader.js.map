{"version": 3, "file": "basis-loader.js", "names": ["_workerUtils", "require", "_version", "_parseBasis", "_interopRequireDefault", "BasisWorkerLoader", "name", "id", "<PERSON><PERSON><PERSON><PERSON>", "module", "version", "VERSION", "worker", "extensions", "mimeTypes", "tests", "binary", "options", "basis", "format", "libraryPath", "containerFormat", "exports", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parse", "parseBasis", "_TypecheckBasisWorkerLoader", "_TypecheckBasisLoader"], "sources": ["../../src/basis-loader.ts"], "sourcesContent": ["import type {Loader, LoaderWithParser} from '@loaders.gl/loader-utils';\nimport {isBrowser} from '@loaders.gl/worker-utils';\nimport {VERSION} from './lib/utils/version';\nimport parseBasis from './lib/parsers/parse-basis';\n\n/**\n * Worker loader for Basis super compressed textures\n */\nexport const BasisWorkerLoader = {\n  name: 'Basis',\n  id: isBrowser ? 'basis' : 'basis-nodejs',\n  module: 'textures',\n  version: VERSION,\n  worker: true,\n  extensions: ['basis', 'ktx2'],\n  mimeTypes: ['application/octet-stream', 'image/ktx2'],\n  tests: ['sB'],\n  binary: true,\n  options: {\n    basis: {\n      format: 'auto', // gl context doesn't exist on a worker thread\n      libraryPath: 'libs/',\n      containerFormat: 'auto', // 'basis' || 'ktx2' || 'auto'\n      module: 'transcoder' // 'transcoder' || 'encoder'\n    }\n  }\n};\n\n/**\n * Loader for Basis super compressed textures\n */\nexport const BasisLoader = {\n  ...BasisWorkerLoader,\n  parse: parseBasis\n};\n\n// TYPE TESTS - TODO find a better way than exporting junk\nexport const _TypecheckBasisWorkerLoader: Loader = BasisWorkerLoader;\nexport const _TypecheckBasisLoader: LoaderWithParser = BasisLoader;\n"], "mappings": ";;;;;;;AACA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,WAAA,GAAAC,sBAAA,CAAAH,OAAA;AAKO,MAAMI,iBAAiB,GAAG;EAC/BC,IAAI,EAAE,OAAO;EACbC,EAAE,EAAEC,sBAAS,GAAG,OAAO,GAAG,cAAc;EACxCC,MAAM,EAAE,UAAU;EAClBC,OAAO,EAAEC,gBAAO;EAChBC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;EAC7BC,SAAS,EAAE,CAAC,0BAA0B,EAAE,YAAY,CAAC;EACrDC,KAAK,EAAE,CAAC,IAAI,CAAC;EACbC,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE;IACPC,KAAK,EAAE;MACLC,MAAM,EAAE,MAAM;MACdC,WAAW,EAAE,OAAO;MACpBC,eAAe,EAAE,MAAM;MACvBZ,MAAM,EAAE;IACV;EACF;AACF,CAAC;AAACa,OAAA,CAAAjB,iBAAA,GAAAA,iBAAA;AAKK,MAAMkB,WAAW,GAAG;EACzB,GAAGlB,iBAAiB;EACpBmB,KAAK,EAAEC;AACT,CAAC;AAACH,OAAA,CAAAC,WAAA,GAAAA,WAAA;AAGK,MAAMG,2BAAmC,GAAGrB,iBAAiB;AAACiB,OAAA,CAAAI,2BAAA,GAAAA,2BAAA;AAC9D,MAAMC,qBAAuC,GAAGJ,WAAW;AAACD,OAAA,CAAAK,qBAAA,GAAAA,qBAAA"}