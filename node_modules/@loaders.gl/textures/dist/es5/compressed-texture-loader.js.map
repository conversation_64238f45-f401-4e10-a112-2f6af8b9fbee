{"version": 3, "file": "compressed-texture-loader.js", "names": ["_version", "require", "_parseCompressedTexture", "_parseBasis", "_interopRequireDefault", "DEFAULT_TEXTURE_LOADER_OPTIONS", "libraryPath", "useBasis", "CompressedTextureWorkerLoader", "name", "id", "module", "version", "VERSION", "worker", "extensions", "mimeTypes", "binary", "options", "exports", "CompressedTextureLoader", "parse", "arrayBuffer", "basis", "format", "alpha", "noAlpha", "containerFormat", "parseBasis", "parseCompressedTexture", "_TypecheckCompressedTextureWorkerLoader", "_TypecheckCompressedTextureLoader"], "sources": ["../../src/compressed-texture-loader.ts"], "sourcesContent": ["import type {Loader, LoaderWithParser} from '@loaders.gl/loader-utils';\nimport {VERSION} from './lib/utils/version';\nimport {parseCompressedTexture} from './lib/parsers/parse-compressed-texture';\nimport parseBasis from './lib/parsers/parse-basis';\n\nexport type TextureLoaderOptions = {\n  'compressed-texture'?: {\n    libraryPath?: string;\n    useBasis?: boolean;\n  };\n};\n\nconst DEFAULT_TEXTURE_LOADER_OPTIONS = {\n  'compressed-texture': {\n    libraryPath: 'libs/',\n    useBasis: false\n  }\n};\n\n/**\n * Worker Loader for KTX, DDS, and PVR texture container formats\n */\nexport const CompressedTextureWorkerLoader = {\n  name: 'Texture Containers',\n  id: 'compressed-texture',\n  module: 'textures',\n  version: VERSION,\n  worker: true,\n  extensions: [\n    'ktx',\n    'ktx2',\n    'dds', // WEBGL_compressed_texture_s3tc, WEBGL_compressed_texture_atc\n    'pvr' // WEBGL_compressed_texture_pvrtc\n  ],\n  mimeTypes: [\n    'image/ktx2',\n    'image/ktx',\n    'image/vnd-ms.dds',\n    'image/x-dds',\n    'application/octet-stream'\n  ],\n  binary: true,\n  options: DEFAULT_TEXTURE_LOADER_OPTIONS\n};\n\n/**\n * Loader for KTX, DDS, and PVR texture container formats\n */\nexport const CompressedTextureLoader = {\n  ...CompressedTextureWorkerLoader,\n  parse: async (arrayBuffer, options) => {\n    if (options['compressed-texture'].useBasis) {\n      options.basis = {\n        format: {\n          alpha: 'BC3',\n          noAlpha: 'BC1'\n        },\n        ...options.basis,\n        containerFormat: 'ktx2',\n        module: 'encoder'\n      };\n      return (await parseBasis(arrayBuffer, options))[0];\n    }\n    return parseCompressedTexture(arrayBuffer);\n  }\n};\n\n// TYPE TESTS - TODO find a better way than exporting junk\nexport const _TypecheckCompressedTextureWorkerLoader: Loader = CompressedTextureWorkerLoader;\nexport const _TypecheckCompressedTextureLoader: LoaderWithParser = CompressedTextureLoader;\n"], "mappings": ";;;;;;;AACA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAD,OAAA;AACA,IAAAE,WAAA,GAAAC,sBAAA,CAAAH,OAAA;AASA,MAAMI,8BAA8B,GAAG;EACrC,oBAAoB,EAAE;IACpBC,WAAW,EAAE,OAAO;IACpBC,QAAQ,EAAE;EACZ;AACF,CAAC;AAKM,MAAMC,6BAA6B,GAAG;EAC3CC,IAAI,EAAE,oBAAoB;EAC1BC,EAAE,EAAE,oBAAoB;EACxBC,MAAM,EAAE,UAAU;EAClBC,OAAO,EAAEC,gBAAO;EAChBC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,CACV,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,CACN;EACDC,SAAS,EAAE,CACT,YAAY,EACZ,WAAW,EACX,kBAAkB,EAClB,aAAa,EACb,0BAA0B,CAC3B;EACDC,MAAM,EAAE,IAAI;EACZC,OAAO,EAAEb;AACX,CAAC;AAACc,OAAA,CAAAX,6BAAA,GAAAA,6BAAA;AAKK,MAAMY,uBAAuB,GAAG;EACrC,GAAGZ,6BAA6B;EAChCa,KAAK,EAAE,MAAAA,CAAOC,WAAW,EAAEJ,OAAO,KAAK;IACrC,IAAIA,OAAO,CAAC,oBAAoB,CAAC,CAACX,QAAQ,EAAE;MAC1CW,OAAO,CAACK,KAAK,GAAG;QACdC,MAAM,EAAE;UACNC,KAAK,EAAE,KAAK;UACZC,OAAO,EAAE;QACX,CAAC;QACD,GAAGR,OAAO,CAACK,KAAK;QAChBI,eAAe,EAAE,MAAM;QACvBhB,MAAM,EAAE;MACV,CAAC;MACD,OAAO,CAAC,MAAM,IAAAiB,mBAAU,EAACN,WAAW,EAAEJ,OAAO,CAAC,EAAE,CAAC,CAAC;IACpD;IACA,OAAO,IAAAW,8CAAsB,EAACP,WAAW,CAAC;EAC5C;AACF,CAAC;AAACH,OAAA,CAAAC,uBAAA,GAAAA,uBAAA;AAGK,MAAMU,uCAA+C,GAAGtB,6BAA6B;AAACW,OAAA,CAAAW,uCAAA,GAAAA,uCAAA;AACtF,MAAMC,iCAAmD,GAAGX,uBAAuB;AAACD,OAAA,CAAAY,iCAAA,GAAAA,iCAAA"}