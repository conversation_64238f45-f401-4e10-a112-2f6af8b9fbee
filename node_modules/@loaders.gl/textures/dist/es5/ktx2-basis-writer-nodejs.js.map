{"version": 3, "file": "ktx2-basis-writer-nodejs.js", "names": ["require", "_ktx2BasisWriter"], "sources": ["../../src/ktx2-basis-writer-nodejs.ts"], "sourcesContent": ["// Polyfills increases the bundle size significantly. Use it for NodeJS worker only\nimport '@loaders.gl/polyfills';\n\nexport {KTX2BasisWriter as KTX2BasisWriterNodeJS} from './ktx2-basis-writer';\n"], "mappings": ";;;;;;;;;;;AACAA,OAAA;AAEA,IAAAC,gBAAA,GAAAD,OAAA"}