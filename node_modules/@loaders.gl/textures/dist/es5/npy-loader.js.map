{"version": 3, "file": "npy-loader.js", "names": ["_version", "require", "_parseNpy", "NPY_MAGIC_NUMBER", "Uint8Array", "NPYWorkerLoader", "name", "id", "module", "version", "VERSION", "worker", "extensions", "mimeTypes", "tests", "buffer", "options", "npy", "exports", "NPYLoader", "parseSync", "parseNPY", "parse", "arrayBuffer", "_TypecheckNPYWorkerLoader", "_TypecheckNPYLoader"], "sources": ["../../src/npy-loader.ts"], "sourcesContent": ["import type {Loader, LoaderWithParser, LoaderOptions} from '@loaders.gl/loader-utils';\nimport {VERSION} from './lib/utils/version';\nimport {parseNPY} from './lib/parsers/parse-npy';\n\n// \\x93NUMPY\nconst NPY_MAGIC_NUMBER = new Uint8Array([147, 78, 85, 77, 80, 89]);\n\n/**\n * Worker loader for numpy \"tiles\"\n */\nexport const NPYWorkerLoader = {\n  name: 'NPY',\n  id: 'npy',\n  module: 'textures',\n  version: VERSION,\n  worker: true,\n  extensions: ['npy'],\n  mimeTypes: [],\n  tests: [NPY_MAGIC_NUMBER.buffer],\n  options: {\n    npy: {}\n  }\n};\n\n/**\n * Loader for numpy \"tiles\"\n */\nexport const NPYLoader = {\n  ...NPYWorkerLoader,\n  parseSync: parseNPY,\n  parse: async (arrayBuffer: ArrayBuffer, options?: LoaderOptions) => parseNPY(arrayBuffer, options)\n};\n\n// TYPE TESTS - TODO find a better way than exporting junk\nexport const _TypecheckNPYWorkerLoader: Loader = NPYWorkerLoader;\nexport const _TypecheckNPYLoader: LoaderWithParser = NPYLoader;\n"], "mappings": ";;;;;;AACA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AAGA,MAAME,gBAAgB,GAAG,IAAIC,UAAU,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AAK3D,MAAMC,eAAe,GAAG;EAC7BC,IAAI,EAAE,KAAK;EACXC,EAAE,EAAE,KAAK;EACTC,MAAM,EAAE,UAAU;EAClBC,OAAO,EAAEC,gBAAO;EAChBC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,CAAC,KAAK,CAAC;EACnBC,SAAS,EAAE,EAAE;EACbC,KAAK,EAAE,CAACX,gBAAgB,CAACY,MAAM,CAAC;EAChCC,OAAO,EAAE;IACPC,GAAG,EAAE,CAAC;EACR;AACF,CAAC;AAACC,OAAA,CAAAb,eAAA,GAAAA,eAAA;AAKK,MAAMc,SAAS,GAAG;EACvB,GAAGd,eAAe;EAClBe,SAAS,EAAEC,kBAAQ;EACnBC,KAAK,EAAE,MAAAA,CAAOC,WAAwB,EAAEP,OAAuB,KAAK,IAAAK,kBAAQ,EAACE,WAAW,EAAEP,OAAO;AACnG,CAAC;AAACE,OAAA,CAAAC,SAAA,GAAAA,SAAA;AAGK,MAAMK,yBAAiC,GAAGnB,eAAe;AAACa,OAAA,CAAAM,yBAAA,GAAAA,yBAAA;AAC1D,MAAMC,mBAAqC,GAAGN,SAAS;AAACD,OAAA,CAAAO,mBAAA,GAAAA,mBAAA"}