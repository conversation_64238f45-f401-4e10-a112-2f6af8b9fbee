"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports._TypecheckCompressedTextureWorkerLoader = exports._TypecheckCompressedTextureLoader = exports.CompressedTextureWorkerLoader = exports.CompressedTextureLoader = void 0;
var _version = require("./lib/utils/version");
var _parseCompressedTexture = require("./lib/parsers/parse-compressed-texture");
var _parseBasis = _interopRequireDefault(require("./lib/parsers/parse-basis"));
const DEFAULT_TEXTURE_LOADER_OPTIONS = {
  'compressed-texture': {
    libraryPath: 'libs/',
    useBasis: false
  }
};
const CompressedTextureWorkerLoader = {
  name: 'Texture Containers',
  id: 'compressed-texture',
  module: 'textures',
  version: _version.VERSION,
  worker: true,
  extensions: ['ktx', 'ktx2', 'dds', 'pvr'],
  mimeTypes: ['image/ktx2', 'image/ktx', 'image/vnd-ms.dds', 'image/x-dds', 'application/octet-stream'],
  binary: true,
  options: DEFAULT_TEXTURE_LOADER_OPTIONS
};
exports.CompressedTextureWorkerLoader = CompressedTextureWorkerLoader;
const CompressedTextureLoader = {
  ...CompressedTextureWorkerLoader,
  parse: async (arrayBuffer, options) => {
    if (options['compressed-texture'].useBasis) {
      options.basis = {
        format: {
          alpha: 'BC3',
          noAlpha: 'BC1'
        },
        ...options.basis,
        containerFormat: 'ktx2',
        module: 'encoder'
      };
      return (await (0, _parseBasis.default)(arrayBuffer, options))[0];
    }
    return (0, _parseCompressedTexture.parseCompressedTexture)(arrayBuffer);
  }
};
exports.CompressedTextureLoader = CompressedTextureLoader;
const _TypecheckCompressedTextureWorkerLoader = CompressedTextureWorkerLoader;
exports._TypecheckCompressedTextureWorkerLoader = _TypecheckCompressedTextureWorkerLoader;
const _TypecheckCompressedTextureLoader = CompressedTextureLoader;
exports._TypecheckCompressedTextureLoader = _TypecheckCompressedTextureLoader;
//# sourceMappingURL=compressed-texture-loader.js.map