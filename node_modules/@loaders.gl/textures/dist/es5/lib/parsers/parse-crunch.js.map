{"version": 3, "file": "parse-crunch.js", "names": ["_crunchModuleLoader", "require", "_glExtensions", "_loaderUtils", "_parseDds", "_extractMipmapImages", "CRN_FORMAT", "cCRNFmtInvalid", "cCRNFmtDXT1", "cCRNFmtDXT3", "cCRNFmtDXT5", "DXT_FORMAT_MAP", "pixelFormat", "GL_EXTENSIONS_CONSTANTS", "COMPRESSED_RGB_S3TC_DXT1_EXT", "sizeFunction", "getDxt1LevelSize", "COMPRESSED_RGBA_S3TC_DXT3_EXT", "getDxtXLevelSize", "COMPRESSED_RGBA_S3TC_DXT5_EXT", "cachedDstSize", "dst", "parseCrunch", "data", "options", "crunchModule", "loadCrunchModule", "srcSize", "byteLength", "bytes", "Uint8Array", "src", "_malloc", "arrayBufferCopy", "HEAPU8", "format", "_crn_get_dxt_format", "assert", "Boolean", "mipMapLevels", "_crn_get_levels", "width", "_crn_get_width", "height", "_crn_get_height", "dstSize", "i", "_free", "_crn_decompress", "image", "buffer", "slice", "extractMipmapImages", "internalFormat", "srcData", "dstData", "dstByteOffset", "numBytes", "dst32Offset", "tail", "src32", "Uint32Array", "dst32", "length"], "sources": ["../../../../src/lib/parsers/parse-crunch.ts"], "sourcesContent": ["import type {TextureLevel} from '@loaders.gl/schema';\nimport {loadCrunchModule} from './crunch-module-loader';\nimport {GL_EXTENSIONS_CONSTANTS} from '../gl-extensions';\nimport {assert} from '@loaders.gl/loader-utils';\nimport {getDxt1LevelSize, getDxtXLevelSize} from './parse-dds';\nimport {extractMipmapImages} from '../utils/extract-mipmap-images';\n\n// Taken from crnlib.h\nconst CRN_FORMAT = {\n  cCRNFmtInvalid: -1,\n\n  cCRNFmtDXT1: 0,\n  // cCRNFmtDXT3 is not currently supported when writing to CRN - only DDS.\n  cCRNFmtDXT3: 1,\n  cCRNFmtDXT5: 2\n\n  // Crunch supports more formats than this.\n};\n\n/** Mapping of Crunch formats to DXT formats. */\nconst DXT_FORMAT_MAP = {\n  [CRN_FORMAT.cCRNFmtDXT1]: {\n    pixelFormat: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_S3TC_DXT1_EXT,\n    sizeFunction: getDxt1LevelSize\n  },\n  [CRN_FORMAT.cCRNFmtDXT3]: {\n    pixelFormat: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT3_EXT,\n    sizeFunction: getDxtXLevelSize\n  },\n  [CRN_FORMAT.cCRNFmtDXT5]: {\n    pixelFormat: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT5_EXT,\n    sizeFunction: getDxtXLevelSize\n  }\n};\n\nlet cachedDstSize = 0;\nlet dst: number;\n\n/**\n * Parse texture data as \"CRN\" format.\n * Function is \"async\" as emscriptified decoder module is loaded asyncronously\n * @param data - binary data of compressed texture\n * @returns Promise of Array of the texture levels\n */\nexport async function parseCrunch(data, options: any): Promise<TextureLevel[]> {\n  const crunchModule = await loadCrunchModule(options);\n\n  // Copy the contents of the arrayBuffer into emscriptens heap.\n  const srcSize = data.byteLength;\n  const bytes = new Uint8Array(data);\n  const src = crunchModule._malloc(srcSize);\n  arrayBufferCopy(bytes, crunchModule.HEAPU8, src, srcSize);\n\n  // Determine what type of compressed data the file contains.\n  const format = crunchModule._crn_get_dxt_format(src, srcSize);\n  assert(Boolean(DXT_FORMAT_MAP[format]), 'Unsupported format');\n\n  // Gather basic metrics about the DXT data.\n  const mipMapLevels = crunchModule._crn_get_levels(src, srcSize);\n  const width = crunchModule._crn_get_width(src, srcSize);\n  const height = crunchModule._crn_get_height(src, srcSize);\n  // const bytesPerBlock = crunchModule._crn_get_bytes_per_block(src, srcSize);\n\n  // Determine the size of the decoded DXT data.\n  const sizeFunction = DXT_FORMAT_MAP[format].sizeFunction;\n  let dstSize = 0;\n  for (let i = 0; i < mipMapLevels; ++i) {\n    dstSize += sizeFunction(width >> i, height >> i);\n  }\n\n  // Allocate enough space on the emscripten heap to hold the decoded DXT data\n  // or reuse the existing allocation if a previous call to this function has\n  // already acquired a large enough buffer.\n  if (cachedDstSize < dstSize) {\n    if (dst) {\n      crunchModule._free(dst);\n    }\n    dst = crunchModule._malloc(dstSize);\n    cachedDstSize = dstSize;\n  }\n\n  // Decompress the DXT data from the Crunch file into the allocated space.\n  crunchModule._crn_decompress(src, srcSize, dst, dstSize, 0, mipMapLevels);\n\n  // Release the crunch file data from the emscripten heap.\n  crunchModule._free(src);\n\n  const image = new Uint8Array(crunchModule.HEAPU8.buffer, dst, dstSize).slice();\n  return extractMipmapImages(image, {\n    mipMapLevels,\n    width,\n    height,\n    sizeFunction,\n    internalFormat: DXT_FORMAT_MAP[format].pixelFormat\n  });\n}\n\n/**\n * Copy an array of bytes into or out of the emscripten heap\n * @param {Uint8Array} srcData - Source data array\n * @param {Uint8Array} dstData - Destination data array\n * @param {number} dstByteOffset - Destination data offset\n * @param {number} numBytes - number of bytes to copy\n * @returns {void}\n */\nfunction arrayBufferCopy(srcData, dstData, dstByteOffset, numBytes) {\n  let i;\n  const dst32Offset = dstByteOffset / 4;\n  const tail = numBytes % 4;\n  const src32 = new Uint32Array(srcData.buffer, 0, (numBytes - tail) / 4);\n  const dst32 = new Uint32Array(dstData.buffer);\n  for (i = 0; i < src32.length; i++) {\n    dst32[dst32Offset + i] = src32[i];\n  }\n  for (i = numBytes - tail; i < numBytes; i++) {\n    dstData[dstByteOffset + i] = srcData[i];\n  }\n}\n"], "mappings": ";;;;;;AACA,IAAAA,mBAAA,GAAAC,OAAA;AACA,IAAAC,aAAA,GAAAD,OAAA;AACA,IAAAE,YAAA,GAAAF,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AACA,IAAAI,oBAAA,GAAAJ,OAAA;AAGA,MAAMK,UAAU,GAAG;EACjBC,cAAc,EAAE,CAAC,CAAC;EAElBC,WAAW,EAAE,CAAC;EAEdC,WAAW,EAAE,CAAC;EACdC,WAAW,EAAE;AAGf,CAAC;AAGD,MAAMC,cAAc,GAAG;EACrB,CAACL,UAAU,CAACE,WAAW,GAAG;IACxBI,WAAW,EAAEC,qCAAuB,CAACC,4BAA4B;IACjEC,YAAY,EAAEC;EAChB,CAAC;EACD,CAACV,UAAU,CAACG,WAAW,GAAG;IACxBG,WAAW,EAAEC,qCAAuB,CAACI,6BAA6B;IAClEF,YAAY,EAAEG;EAChB,CAAC;EACD,CAACZ,UAAU,CAACI,WAAW,GAAG;IACxBE,WAAW,EAAEC,qCAAuB,CAACM,6BAA6B;IAClEJ,YAAY,EAAEG;EAChB;AACF,CAAC;AAED,IAAIE,aAAa,GAAG,CAAC;AACrB,IAAIC,GAAW;AAQR,eAAeC,WAAWA,CAACC,IAAI,EAAEC,OAAY,EAA2B;EAC7E,MAAMC,YAAY,GAAG,MAAM,IAAAC,oCAAgB,EAACF,OAAO,CAAC;EAGpD,MAAMG,OAAO,GAAGJ,IAAI,CAACK,UAAU;EAC/B,MAAMC,KAAK,GAAG,IAAIC,UAAU,CAACP,IAAI,CAAC;EAClC,MAAMQ,GAAG,GAAGN,YAAY,CAACO,OAAO,CAACL,OAAO,CAAC;EACzCM,eAAe,CAACJ,KAAK,EAAEJ,YAAY,CAACS,MAAM,EAAEH,GAAG,EAAEJ,OAAO,CAAC;EAGzD,MAAMQ,MAAM,GAAGV,YAAY,CAACW,mBAAmB,CAACL,GAAG,EAAEJ,OAAO,CAAC;EAC7D,IAAAU,mBAAM,EAACC,OAAO,CAAC3B,cAAc,CAACwB,MAAM,CAAC,CAAC,EAAE,oBAAoB,CAAC;EAG7D,MAAMI,YAAY,GAAGd,YAAY,CAACe,eAAe,CAACT,GAAG,EAAEJ,OAAO,CAAC;EAC/D,MAAMc,KAAK,GAAGhB,YAAY,CAACiB,cAAc,CAACX,GAAG,EAAEJ,OAAO,CAAC;EACvD,MAAMgB,MAAM,GAAGlB,YAAY,CAACmB,eAAe,CAACb,GAAG,EAAEJ,OAAO,CAAC;EAIzD,MAAMZ,YAAY,GAAGJ,cAAc,CAACwB,MAAM,CAAC,CAACpB,YAAY;EACxD,IAAI8B,OAAO,GAAG,CAAC;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,YAAY,EAAE,EAAEO,CAAC,EAAE;IACrCD,OAAO,IAAI9B,YAAY,CAAC0B,KAAK,IAAIK,CAAC,EAAEH,MAAM,IAAIG,CAAC,CAAC;EAClD;EAKA,IAAI1B,aAAa,GAAGyB,OAAO,EAAE;IAC3B,IAAIxB,GAAG,EAAE;MACPI,YAAY,CAACsB,KAAK,CAAC1B,GAAG,CAAC;IACzB;IACAA,GAAG,GAAGI,YAAY,CAACO,OAAO,CAACa,OAAO,CAAC;IACnCzB,aAAa,GAAGyB,OAAO;EACzB;EAGApB,YAAY,CAACuB,eAAe,CAACjB,GAAG,EAAEJ,OAAO,EAAEN,GAAG,EAAEwB,OAAO,EAAE,CAAC,EAAEN,YAAY,CAAC;EAGzEd,YAAY,CAACsB,KAAK,CAAChB,GAAG,CAAC;EAEvB,MAAMkB,KAAK,GAAG,IAAInB,UAAU,CAACL,YAAY,CAACS,MAAM,CAACgB,MAAM,EAAE7B,GAAG,EAAEwB,OAAO,CAAC,CAACM,KAAK,CAAC,CAAC;EAC9E,OAAO,IAAAC,wCAAmB,EAACH,KAAK,EAAE;IAChCV,YAAY;IACZE,KAAK;IACLE,MAAM;IACN5B,YAAY;IACZsC,cAAc,EAAE1C,cAAc,CAACwB,MAAM,CAAC,CAACvB;EACzC,CAAC,CAAC;AACJ;AAUA,SAASqB,eAAeA,CAACqB,OAAO,EAAEC,OAAO,EAAEC,aAAa,EAAEC,QAAQ,EAAE;EAClE,IAAIX,CAAC;EACL,MAAMY,WAAW,GAAGF,aAAa,GAAG,CAAC;EACrC,MAAMG,IAAI,GAAGF,QAAQ,GAAG,CAAC;EACzB,MAAMG,KAAK,GAAG,IAAIC,WAAW,CAACP,OAAO,CAACJ,MAAM,EAAE,CAAC,EAAE,CAACO,QAAQ,GAAGE,IAAI,IAAI,CAAC,CAAC;EACvE,MAAMG,KAAK,GAAG,IAAID,WAAW,CAACN,OAAO,CAACL,MAAM,CAAC;EAC7C,KAAKJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,KAAK,CAACG,MAAM,EAAEjB,CAAC,EAAE,EAAE;IACjCgB,KAAK,CAACJ,WAAW,GAAGZ,CAAC,CAAC,GAAGc,KAAK,CAACd,CAAC,CAAC;EACnC;EACA,KAAKA,CAAC,GAAGW,QAAQ,GAAGE,IAAI,EAAEb,CAAC,GAAGW,QAAQ,EAAEX,CAAC,EAAE,EAAE;IAC3CS,OAAO,CAACC,aAAa,GAAGV,CAAC,CAAC,GAAGQ,OAAO,CAACR,CAAC,CAAC;EACzC;AACF"}