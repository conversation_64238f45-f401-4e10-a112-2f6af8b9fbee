"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.parseNPY = parseNPY;
function systemIsLittleEndian() {
  const a = new Uint32Array([0x12345678]);
  const b = new Uint8Array(a.buffer, a.byteOffset, a.byteLength);
  return !(b[0] === 0x12);
}
const LITTLE_ENDIAN_OS = systemIsLittleEndian();
const DTYPES = {
  u1: Uint8Array,
  i1: Int8Array,
  u2: Uint16Array,
  i2: Int16Array,
  u4: Uint32Array,
  i4: Int32Array,
  f4: Float32Array,
  f8: Float64Array
};
function parseNPY(arrayBuffer, options) {
  var _header$shape;
  if (!arrayBuffer) {
    return null;
  }
  const view = new DataView(arrayBuffer);
  const {
    header,
    headerEndOffset
  } = parseHeader(view);
  const numpyType = header.descr;
  const ArrayType = DTYPES[numpyType.slice(1, 3)];
  if (!ArrayType) {
    throw new Error("Unimplemented type ".concat(numpyType));
  }
  const nArrayElements = (_header$shape = header.shape) === null || _header$shape === void 0 ? void 0 : _header$shape.reduce((a, b) => a * b);
  const arrayByteLength = nArrayElements * ArrayType.BYTES_PER_ELEMENT;
  if (arrayBuffer.byteLength < headerEndOffset + arrayByteLength) {
    throw new Error('Buffer overflow');
  }
  const data = new ArrayType(arrayBuffer.slice(headerEndOffset, headerEndOffset + arrayByteLength));
  if (numpyType[0] === '>' && LITTLE_ENDIAN_OS || numpyType[0] === '<' && !LITTLE_ENDIAN_OS) {
    throw new Error('Incorrect endianness');
  }
  return {
    data,
    header
  };
}
function parseHeader(view) {
  const majorVersion = view.getUint8(6);
  let offset = 8;
  let headerLength;
  if (majorVersion >= 2) {
    headerLength = view.getUint32(offset, true);
    offset += 4;
  } else {
    headerLength = view.getUint16(offset, true);
    offset += 2;
  }
  const encoding = majorVersion <= 2 ? 'latin1' : 'utf-8';
  const decoder = new TextDecoder(encoding);
  const headerArray = new Uint8Array(view.buffer, offset, headerLength);
  const headerText = decoder.decode(headerArray);
  offset += headerLength;
  const header = JSON.parse(headerText.replace(/'/g, '"').replace('False', 'false').replace('(', '[').replace(/,*\),*/g, ']'));
  return {
    header,
    headerEndOffset: offset
  };
}
//# sourceMappingURL=parse-npy.js.map