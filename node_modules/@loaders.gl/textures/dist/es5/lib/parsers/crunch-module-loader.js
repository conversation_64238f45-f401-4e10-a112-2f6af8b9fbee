"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.loadCrunchModule = loadCrunchModule;
var _workerUtils = require("@loaders.gl/worker-utils");
async function loadCrunchModule(options) {
  const modules = options.modules || {};
  if (modules.crunch) {
    return modules.crunch;
  }
  return loadCrunch(options);
}
let crunchModule;
async function loadCrunch(options) {
  if (crunchModule) {
    return crunchModule;
  }
  let loadCrunchDecoder = await (0, _workerUtils.loadLibrary)('crunch.js', 'textures', options);
  loadCrunchDecoder = loadCrunchDecoder || globalThis.LoadCrunchDecoder;
  crunchModule = loadCrunchDecoder();
  return crunchModule;
}
//# sourceMappingURL=crunch-module-loader.js.map