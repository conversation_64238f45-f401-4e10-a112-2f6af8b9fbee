{"version": 3, "file": "basis-module-loader.js", "names": ["_workerUtils", "require", "VERSION", "BASIS_CDN_ENCODER_WASM", "concat", "BASIS_CDN_ENCODER_JS", "loadBasisTranscoderPromise", "loadBasisTrascoderModule", "options", "modules", "basis", "loadBasisTrascoder", "BASIS", "wasmBinary", "Promise", "all", "loadLibrary", "globalThis", "initializeBasisTrascoderModule", "BasisModule", "resolve", "then", "module", "BasisFile", "initializeBasis", "loadBasisEncoderPromise", "loadBasisEncoderModule", "basisEncoder", "loadBasisEncoder", "BASIS_ENCODER", "initializeBasisEncoderModule", "BasisEncoderModule", "KTX2File", "Basis<PERSON><PERSON>der"], "sources": ["../../../../src/lib/parsers/basis-module-loader.ts"], "sourcesContent": ["// __VERSION__ is injected by babel-plugin-version-inline\n// @ts-ignore TS2304: Cannot find name '__VERSION__'.\nconst VERSION = typeof __VERSION__ !== 'undefined' ? __VERSION__ : 'latest';\n\n// @ts-nocheck\nimport {loadLibrary} from '@loaders.gl/worker-utils';\n\nconst BASIS_CDN_ENCODER_WASM = `https://unpkg.com/@loaders.gl/textures@${VERSION}/dist/libs/basis_encoder.wasm`;\nconst BASIS_CDN_ENCODER_JS = `https://unpkg.com/@loaders.gl/textures@${VERSION}/dist/libs/basis_encoder.js`;\n\nlet loadBasisTranscoderPromise;\n\n/**\n * Loads wasm transcoder module\n * @param options\n * @returns {BasisFile} promise\n */\nexport async function loadBasisTrascoderModule(options) {\n  const modules = options.modules || {};\n  if (modules.basis) {\n    return modules.basis;\n  }\n\n  loadBasisTranscoderPromise = loadBasisTranscoderPromise || loadBasisTrascoder(options);\n  return await loadBasisTranscoderPromise;\n}\n\n/**\n * Loads wasm transcoder module\n * @param options\n * @returns {BasisFile} promise\n */\nasync function loadBasisTrascoder(options) {\n  let BASIS = null;\n  let wasmBinary = null;\n\n  [BASIS, wasmBinary] = await Promise.all([\n    await loadLibrary('basis_transcoder.js', 'textures', options),\n    await loadLibrary('basis_transcoder.wasm', 'textures', options)\n  ]);\n\n  // Depends on how import happened...\n  // @ts-ignore TS2339: Property does not exist on type\n  BASIS = BASIS || globalThis.BASIS;\n  return await initializeBasisTrascoderModule(BASIS, wasmBinary);\n}\n\n/**\n * Initialize wasm transcoder module\n * @param BasisModule - js part of the module\n * @param wasmBinary - wasm part of the module\n * @returns {BasisFile} promise\n */\nfunction initializeBasisTrascoderModule(BasisModule, wasmBinary) {\n  const options: {wasmBinary?} = {};\n\n  if (wasmBinary) {\n    options.wasmBinary = wasmBinary;\n  }\n\n  return new Promise((resolve) => {\n    // if you try to return BasisModule the browser crashes!\n    BasisModule(options).then((module) => {\n      const {BasisFile, initializeBasis} = module;\n      initializeBasis();\n      resolve({BasisFile});\n    });\n  });\n}\n\nlet loadBasisEncoderPromise;\n\n/**\n * Loads wasm encoder module\n * @param options\n * @returns {BasisFile, KTX2File} promise\n */\nexport async function loadBasisEncoderModule(options) {\n  const modules = options.modules || {};\n  if (modules.basisEncoder) {\n    return modules.basisEncoder;\n  }\n\n  loadBasisEncoderPromise = loadBasisEncoderPromise || loadBasisEncoder(options);\n  return await loadBasisEncoderPromise;\n}\n\n/**\n * Loads wasm encoder module\n * @param options\n * @returns {BasisFile, KTX2File} promise\n */\nasync function loadBasisEncoder(options) {\n  let BASIS_ENCODER = null;\n  let wasmBinary = null;\n\n  [BASIS_ENCODER, wasmBinary] = await Promise.all([\n    await loadLibrary(BASIS_CDN_ENCODER_JS, 'textures', options),\n    await loadLibrary(BASIS_CDN_ENCODER_WASM, 'textures', options)\n  ]);\n\n  // Depends on how import happened...\n  // @ts-ignore TS2339: Property does not exist on type\n  BASIS_ENCODER = BASIS_ENCODER || globalThis.BASIS;\n  return await initializeBasisEncoderModule(BASIS_ENCODER, wasmBinary);\n}\n\n/**\n * Initialize wasm transcoder module\n * @param BasisEncoderModule - js part of the module\n * @param wasmBinary - wasm part of the module\n * @returns {BasisFile, KTX2File} promise\n */\nfunction initializeBasisEncoderModule(BasisEncoderModule, wasmBinary) {\n  const options: {wasmBinary?} = {};\n\n  if (wasmBinary) {\n    options.wasmBinary = wasmBinary;\n  }\n\n  return new Promise((resolve) => {\n    // if you try to return BasisModule the browser crashes!\n    BasisEncoderModule(options).then((module) => {\n      const {BasisFile, KTX2File, initializeBasis, BasisEncoder} = module;\n      initializeBasis();\n      resolve({BasisFile, KTX2File, BasisEncoder});\n    });\n  });\n}\n"], "mappings": ";;;;;;;AAKA,IAAAA,YAAA,GAAAC,OAAA;AAHA,MAAMC,OAAO,GAAG,eAAkB,KAAK,WAAW,cAAiB,QAAQ;AAK3E,MAAMC,sBAAsB,6CAAAC,MAAA,CAA6CF,OAAO,kCAA+B;AAC/G,MAAMG,oBAAoB,6CAAAD,MAAA,CAA6CF,OAAO,gCAA6B;AAE3G,IAAII,0BAA0B;AAOvB,eAAeC,wBAAwBA,CAACC,OAAO,EAAE;EACtD,MAAMC,OAAO,GAAGD,OAAO,CAACC,OAAO,IAAI,CAAC,CAAC;EACrC,IAAIA,OAAO,CAACC,KAAK,EAAE;IACjB,OAAOD,OAAO,CAACC,KAAK;EACtB;EAEAJ,0BAA0B,GAAGA,0BAA0B,IAAIK,kBAAkB,CAACH,OAAO,CAAC;EACtF,OAAO,MAAMF,0BAA0B;AACzC;AAOA,eAAeK,kBAAkBA,CAACH,OAAO,EAAE;EACzC,IAAII,KAAK,GAAG,IAAI;EAChB,IAAIC,UAAU,GAAG,IAAI;EAErB,CAACD,KAAK,EAAEC,UAAU,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACtC,MAAM,IAAAC,wBAAW,EAAC,qBAAqB,EAAE,UAAU,EAAER,OAAO,CAAC,EAC7D,MAAM,IAAAQ,wBAAW,EAAC,uBAAuB,EAAE,UAAU,EAAER,OAAO,CAAC,CAChE,CAAC;EAIFI,KAAK,GAAGA,KAAK,IAAIK,UAAU,CAACL,KAAK;EACjC,OAAO,MAAMM,8BAA8B,CAACN,KAAK,EAAEC,UAAU,CAAC;AAChE;AAQA,SAASK,8BAA8BA,CAACC,WAAW,EAAEN,UAAU,EAAE;EAC/D,MAAML,OAAsB,GAAG,CAAC,CAAC;EAEjC,IAAIK,UAAU,EAAE;IACdL,OAAO,CAACK,UAAU,GAAGA,UAAU;EACjC;EAEA,OAAO,IAAIC,OAAO,CAAEM,OAAO,IAAK;IAE9BD,WAAW,CAACX,OAAO,CAAC,CAACa,IAAI,CAAEC,MAAM,IAAK;MACpC,MAAM;QAACC,SAAS;QAAEC;MAAe,CAAC,GAAGF,MAAM;MAC3CE,eAAe,CAAC,CAAC;MACjBJ,OAAO,CAAC;QAACG;MAAS,CAAC,CAAC;IACtB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,IAAIE,uBAAuB;AAOpB,eAAeC,sBAAsBA,CAAClB,OAAO,EAAE;EACpD,MAAMC,OAAO,GAAGD,OAAO,CAACC,OAAO,IAAI,CAAC,CAAC;EACrC,IAAIA,OAAO,CAACkB,YAAY,EAAE;IACxB,OAAOlB,OAAO,CAACkB,YAAY;EAC7B;EAEAF,uBAAuB,GAAGA,uBAAuB,IAAIG,gBAAgB,CAACpB,OAAO,CAAC;EAC9E,OAAO,MAAMiB,uBAAuB;AACtC;AAOA,eAAeG,gBAAgBA,CAACpB,OAAO,EAAE;EACvC,IAAIqB,aAAa,GAAG,IAAI;EACxB,IAAIhB,UAAU,GAAG,IAAI;EAErB,CAACgB,aAAa,EAAEhB,UAAU,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC9C,MAAM,IAAAC,wBAAW,EAACX,oBAAoB,EAAE,UAAU,EAAEG,OAAO,CAAC,EAC5D,MAAM,IAAAQ,wBAAW,EAACb,sBAAsB,EAAE,UAAU,EAAEK,OAAO,CAAC,CAC/D,CAAC;EAIFqB,aAAa,GAAGA,aAAa,IAAIZ,UAAU,CAACL,KAAK;EACjD,OAAO,MAAMkB,4BAA4B,CAACD,aAAa,EAAEhB,UAAU,CAAC;AACtE;AAQA,SAASiB,4BAA4BA,CAACC,kBAAkB,EAAElB,UAAU,EAAE;EACpE,MAAML,OAAsB,GAAG,CAAC,CAAC;EAEjC,IAAIK,UAAU,EAAE;IACdL,OAAO,CAACK,UAAU,GAAGA,UAAU;EACjC;EAEA,OAAO,IAAIC,OAAO,CAAEM,OAAO,IAAK;IAE9BW,kBAAkB,CAACvB,OAAO,CAAC,CAACa,IAAI,CAAEC,MAAM,IAAK;MAC3C,MAAM;QAACC,SAAS;QAAES,QAAQ;QAAER,eAAe;QAAES;MAAY,CAAC,GAAGX,MAAM;MACnEE,eAAe,CAAC,CAAC;MACjBJ,OAAO,CAAC;QAACG,SAAS;QAAES,QAAQ;QAAEC;MAAY,CAAC,CAAC;IAC9C,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ"}