{"version": 3, "file": "crunch-module-loader.js", "names": ["_workerUtils", "require", "loadCrunchModule", "options", "modules", "crunch", "loadCrunch", "crunchModule", "loadCrunchDecoder", "loadLibrary", "globalThis", "LoadCrunchDecoder"], "sources": ["../../../../src/lib/parsers/crunch-module-loader.ts"], "sourcesContent": ["// @ts-nocheck\nimport {loadLibrary} from '@loaders.gl/worker-utils';\n\n/**\n * Load crunch decoder module\n * @param options - loader options\n * @returns Promise of module object\n */\nexport async function loadCrunchModule(options): Promise<any> {\n  const modules = options.modules || {};\n  if (modules.crunch) {\n    return modules.crunch;\n  }\n\n  return loadCrunch(options);\n}\n\nlet crunchModule;\n\n/**\n * Load crunch decoder module\n * @param {any} options - Loader options\n * @returns {Promise<any>} Promise of Module object\n */\nasync function loadCrunch(options) {\n  if (crunchModule) {\n    return crunchModule;\n  }\n\n  let loadCrunchDecoder = await loadLibrary('crunch.js', 'textures', options);\n\n  // Depends on how import happened...\n  // @ts-ignore TS2339: Property does not exist on type\n  loadCrunchDecoder = loadCrunchDecoder || globalThis.LoadCrunchDecoder;\n  crunchModule = loadCrunchDecoder();\n  return crunchModule;\n}\n"], "mappings": ";;;;;;AACA,IAAAA,YAAA,GAAAC,OAAA;AAOO,eAAeC,gBAAgBA,CAACC,OAAO,EAAgB;EAC5D,MAAMC,OAAO,GAAGD,OAAO,CAACC,OAAO,IAAI,CAAC,CAAC;EACrC,IAAIA,OAAO,CAACC,MAAM,EAAE;IAClB,OAAOD,OAAO,CAACC,MAAM;EACvB;EAEA,OAAOC,UAAU,CAACH,OAAO,CAAC;AAC5B;AAEA,IAAII,YAAY;AAOhB,eAAeD,UAAUA,CAACH,OAAO,EAAE;EACjC,IAAII,YAAY,EAAE;IAChB,OAAOA,YAAY;EACrB;EAEA,IAAIC,iBAAiB,GAAG,MAAM,IAAAC,wBAAW,EAAC,WAAW,EAAE,UAAU,EAAEN,OAAO,CAAC;EAI3EK,iBAAiB,GAAGA,iBAAiB,IAAIE,UAAU,CAACC,iBAAiB;EACrEJ,YAAY,GAAGC,iBAAiB,CAAC,CAAC;EAClC,OAAOD,YAAY;AACrB"}