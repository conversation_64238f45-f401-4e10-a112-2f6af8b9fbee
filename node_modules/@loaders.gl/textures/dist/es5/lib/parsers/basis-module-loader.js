"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.loadBasisEncoderModule = loadBasisEncoderModule;
exports.loadBasisTrascoderModule = loadBasisTrascoderModule;
var _workerUtils = require("@loaders.gl/worker-utils");
const VERSION = typeof "3.4.15" !== 'undefined' ? "3.4.15" : 'latest';
const BASIS_CDN_ENCODER_WASM = "https://unpkg.com/@loaders.gl/textures@".concat(VERSION, "/dist/libs/basis_encoder.wasm");
const BASIS_CDN_ENCODER_JS = "https://unpkg.com/@loaders.gl/textures@".concat(VERSION, "/dist/libs/basis_encoder.js");
let loadBasisTranscoderPromise;
async function loadBasisTrascoderModule(options) {
  const modules = options.modules || {};
  if (modules.basis) {
    return modules.basis;
  }
  loadBasisTranscoderPromise = loadBasisTranscoderPromise || loadBasisTrascoder(options);
  return await loadBasisTranscoderPromise;
}
async function loadBasisTrascoder(options) {
  let BASIS = null;
  let wasmBinary = null;
  [BASIS, wasmBinary] = await Promise.all([await (0, _workerUtils.loadLibrary)('basis_transcoder.js', 'textures', options), await (0, _workerUtils.loadLibrary)('basis_transcoder.wasm', 'textures', options)]);
  BASIS = BASIS || globalThis.BASIS;
  return await initializeBasisTrascoderModule(BASIS, wasmBinary);
}
function initializeBasisTrascoderModule(BasisModule, wasmBinary) {
  const options = {};
  if (wasmBinary) {
    options.wasmBinary = wasmBinary;
  }
  return new Promise(resolve => {
    BasisModule(options).then(module => {
      const {
        BasisFile,
        initializeBasis
      } = module;
      initializeBasis();
      resolve({
        BasisFile
      });
    });
  });
}
let loadBasisEncoderPromise;
async function loadBasisEncoderModule(options) {
  const modules = options.modules || {};
  if (modules.basisEncoder) {
    return modules.basisEncoder;
  }
  loadBasisEncoderPromise = loadBasisEncoderPromise || loadBasisEncoder(options);
  return await loadBasisEncoderPromise;
}
async function loadBasisEncoder(options) {
  let BASIS_ENCODER = null;
  let wasmBinary = null;
  [BASIS_ENCODER, wasmBinary] = await Promise.all([await (0, _workerUtils.loadLibrary)(BASIS_CDN_ENCODER_JS, 'textures', options), await (0, _workerUtils.loadLibrary)(BASIS_CDN_ENCODER_WASM, 'textures', options)]);
  BASIS_ENCODER = BASIS_ENCODER || globalThis.BASIS;
  return await initializeBasisEncoderModule(BASIS_ENCODER, wasmBinary);
}
function initializeBasisEncoderModule(BasisEncoderModule, wasmBinary) {
  const options = {};
  if (wasmBinary) {
    options.wasmBinary = wasmBinary;
  }
  return new Promise(resolve => {
    BasisEncoderModule(options).then(module => {
      const {
        BasisFile,
        KTX2File,
        initializeBasis,
        BasisEncoder
      } = module;
      initializeBasis();
      resolve({
        BasisFile,
        KTX2File,
        BasisEncoder
      });
    });
  });
}
//# sourceMappingURL=basis-module-loader.js.map