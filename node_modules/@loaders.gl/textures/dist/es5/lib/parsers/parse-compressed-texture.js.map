{"version": 3, "file": "parse-compressed-texture.js", "names": ["_parseKtx", "require", "_parseDds", "_parsePvr", "parseCompressedTexture", "data", "isKTX", "parseKTX", "isDDS", "parseDDS", "isPVR", "parsePVR", "Error"], "sources": ["../../../../src/lib/parsers/parse-compressed-texture.ts"], "sourcesContent": ["import type {TextureLevel} from '@loaders.gl/schema';\nimport {isKTX, parseKTX} from './parse-ktx';\nimport {isDDS, parseDDS} from './parse-dds';\nimport {isPVR, parsePVR} from './parse-pvr';\n\n/**\n * Deduces format and parses compressed texture loaded in ArrayBuffer\n * @param data - binary data of compressed texture\n * @returns Array of the texture levels\n */\nexport function parseCompressedTexture(data: ArrayBuffer): TextureLevel[] {\n  if (isKTX(data)) {\n    // TODO: remove @ts-ignore when `parseKTX` output is normalized to loaders.gl texture format\n    // @ts-ignore\n    return parseKTX(data);\n  }\n  if (isDDS(data)) {\n    return parseDDS(data);\n  }\n  if (isPVR(data)) {\n    return parsePVR(data);\n  }\n  throw new Error('Texture container format not recognized');\n}\n"], "mappings": ";;;;;;AACA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AAOO,SAASG,sBAAsBA,CAACC,IAAiB,EAAkB;EACxE,IAAI,IAAAC,eAAK,EAACD,IAAI,CAAC,EAAE;IAGf,OAAO,IAAAE,kBAAQ,EAACF,IAAI,CAAC;EACvB;EACA,IAAI,IAAAG,eAAK,EAACH,IAAI,CAAC,EAAE;IACf,OAAO,IAAAI,kBAAQ,EAACJ,IAAI,CAAC;EACvB;EACA,IAAI,IAAAK,eAAK,EAACL,IAAI,CAAC,EAAE;IACf,OAAO,IAAAM,kBAAQ,EAACN,IAAI,CAAC;EACvB;EACA,MAAM,IAAIO,KAAK,CAAC,yCAAyC,CAAC;AAC5D"}