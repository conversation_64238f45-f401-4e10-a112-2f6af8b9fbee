{"version": 3, "file": "parse-basis.js", "names": ["_basisModuleLoader", "require", "_glExtensions", "_textureFormats", "_parseKtx", "OutputFormat", "etc1", "basisFormat", "compressed", "format", "GL_EXTENSIONS_CONSTANTS", "COMPRESSED_RGB_ETC1_WEBGL", "etc2", "bc1", "COMPRESSED_RGB_S3TC_DXT1_EXT", "bc3", "COMPRESSED_RGBA_S3TC_DXT5_EXT", "bc4", "bc5", "COMPRESSED_RGB_PVRTC_4BPPV1_IMG", "COMPRESSED_RGBA_PVRTC_4BPPV1_IMG", "COMPRESSED_RGBA_ASTC_4X4_KHR", "rgba32", "rgb565", "bgr565", "rgba4444", "parseBasis", "data", "options", "basis", "containerFormat", "isKTX", "fileConstructors", "loadBasisEncoderModule", "parseKTX2File", "KTX2File", "BasisFile", "loadBasisTrascoderModule", "parseBasisFile", "module", "basisFile", "Uint8Array", "startTranscoding", "Error", "imageCount", "getNumImages", "images", "imageIndex", "levelsCount", "getNumLevels", "levels", "levelIndex", "push", "transcodeImage", "close", "delete", "width", "getImageWidth", "height", "getImageHeight", "has<PERSON><PERSON><PERSON>", "getHasAlpha", "getBasisOptions", "decodedSize", "getImageTranscodedSizeInBytes", "decodedData", "ktx2File", "getLevels", "transcodeKTX2Image", "alphaFlag", "getImageLevelInfo", "levelSize", "selectSupportedBasisFormat", "alpha", "noAlpha", "toLowerCase", "supportedFormats", "getSupportedGPUTextureFormats", "has"], "sources": ["../../../../src/lib/parsers/parse-basis.ts"], "sourcesContent": ["/* eslint-disable indent */\nimport type {TextureLevel} from '@loaders.gl/schema';\nimport {loadBasisEncoderModule, loadBasisTrascoderModule} from './basis-module-loader';\nimport {GL_EXTENSIONS_CONSTANTS} from '../gl-extensions';\nimport {getSupportedGPUTextureFormats} from '../utils/texture-formats';\nimport {isKTX} from './parse-ktx';\n\nexport type BasisFormat =\n  | 'etc1'\n  | 'etc2'\n  | 'bc1'\n  | 'bc3'\n  | 'bc4'\n  | 'bc5'\n  | 'bc7-m6-opaque-only'\n  | 'bc7-m5'\n  | 'pvrtc1-4-rgb'\n  | 'pvrtc1-4-rgba'\n  | 'astc-4x4'\n  | 'atc-rgb'\n  | 'atc-rgba-interpolated-alpha'\n  | 'rgba32'\n  | 'rgb565'\n  | 'bgr565'\n  | 'rgba4444';\n\ntype BasisOutputOptions = {\n  basisFormat: number;\n  compressed: boolean;\n  format?: number;\n};\n\nconst OutputFormat: Record<string, BasisOutputOptions> = {\n  etc1: {\n    basisFormat: 0,\n    compressed: true,\n    format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_ETC1_WEBGL\n  },\n  etc2: {basisFormat: 1, compressed: true},\n  bc1: {\n    basisFormat: 2,\n    compressed: true,\n    format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_S3TC_DXT1_EXT\n  },\n  bc3: {\n    basisFormat: 3,\n    compressed: true,\n    format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT5_EXT\n  },\n  bc4: {basisFormat: 4, compressed: true},\n  bc5: {basisFormat: 5, compressed: true},\n  'bc7-m6-opaque-only': {basisFormat: 6, compressed: true},\n  'bc7-m5': {basisFormat: 7, compressed: true},\n  'pvrtc1-4-rgb': {\n    basisFormat: 8,\n    compressed: true,\n    format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_PVRTC_4BPPV1_IMG\n  },\n  'pvrtc1-4-rgba': {\n    basisFormat: 9,\n    compressed: true,\n    format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG\n  },\n  'astc-4x4': {\n    basisFormat: 10,\n    compressed: true,\n    format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_4X4_KHR\n  },\n  'atc-rgb': {basisFormat: 11, compressed: true},\n  'atc-rgba-interpolated-alpha': {basisFormat: 12, compressed: true},\n  rgba32: {basisFormat: 13, compressed: false},\n  rgb565: {basisFormat: 14, compressed: false},\n  bgr565: {basisFormat: 15, compressed: false},\n  rgba4444: {basisFormat: 16, compressed: false}\n};\n\n/**\n * parse data with a Binomial Basis_Universal module\n * @param data\n * @param options\n * @returns compressed texture data\n */\nexport default async function parseBasis(data: ArrayBuffer, options): Promise<TextureLevel[][]> {\n  if (options.basis.containerFormat === 'auto') {\n    if (isKTX(data)) {\n      const fileConstructors = await loadBasisEncoderModule(options);\n      return parseKTX2File(fileConstructors.KTX2File, data, options);\n    }\n    const {BasisFile} = await loadBasisTrascoderModule(options);\n    return parseBasisFile(BasisFile, data, options);\n  }\n  switch (options.basis.module) {\n    case 'encoder':\n      const fileConstructors = await loadBasisEncoderModule(options);\n      switch (options.basis.containerFormat) {\n        case 'ktx2':\n          return parseKTX2File(fileConstructors.KTX2File, data, options);\n        case 'basis':\n        default:\n          return parseBasisFile(fileConstructors.BasisFile, data, options);\n      }\n    case 'transcoder':\n    default:\n      const {BasisFile} = await loadBasisTrascoderModule(options);\n      return parseBasisFile(BasisFile, data, options);\n  }\n}\n\n/**\n * Parse *.basis file data\n * @param BasisFile - initialized transcoder module\n * @param data\n * @param options\n * @returns compressed texture data\n */\nfunction parseBasisFile(BasisFile, data, options): TextureLevel[][] {\n  const basisFile = new BasisFile(new Uint8Array(data));\n\n  try {\n    if (!basisFile.startTranscoding()) {\n      throw new Error('Failed to start basis transcoding');\n    }\n\n    const imageCount = basisFile.getNumImages();\n    const images: TextureLevel[][] = [];\n\n    for (let imageIndex = 0; imageIndex < imageCount; imageIndex++) {\n      const levelsCount = basisFile.getNumLevels(imageIndex);\n      const levels: TextureLevel[] = [];\n\n      for (let levelIndex = 0; levelIndex < levelsCount; levelIndex++) {\n        levels.push(transcodeImage(basisFile, imageIndex, levelIndex, options));\n      }\n\n      images.push(levels);\n    }\n\n    return images;\n  } finally {\n    basisFile.close();\n    basisFile.delete();\n  }\n}\n\n/**\n * Parse the particular level image of a basis file\n * @param basisFile\n * @param imageIndex\n * @param levelIndex\n * @param options\n * @returns compressed texture data\n */\nfunction transcodeImage(basisFile, imageIndex, levelIndex, options): TextureLevel {\n  const width = basisFile.getImageWidth(imageIndex, levelIndex);\n  const height = basisFile.getImageHeight(imageIndex, levelIndex);\n\n  // See https://github.com/BinomialLLC/basis_universal/pull/83\n  const hasAlpha = basisFile.getHasAlpha(/* imageIndex, levelIndex */);\n\n  // Check options for output format etc\n  const {compressed, format, basisFormat} = getBasisOptions(options, hasAlpha);\n\n  const decodedSize = basisFile.getImageTranscodedSizeInBytes(imageIndex, levelIndex, basisFormat);\n  const decodedData = new Uint8Array(decodedSize);\n\n  if (!basisFile.transcodeImage(decodedData, imageIndex, levelIndex, basisFormat, 0, 0)) {\n    throw new Error('failed to start Basis transcoding');\n  }\n\n  return {\n    // standard loaders.gl image category payload\n    width,\n    height,\n    data: decodedData,\n    compressed,\n    format,\n\n    // Additional fields\n    // Add levelSize field.\n    hasAlpha\n  };\n}\n\n/**\n * Parse *.ktx2 file data\n * @param KTX2File\n * @param data\n * @param options\n * @returns compressed texture data\n */\nfunction parseKTX2File(KTX2File, data: ArrayBuffer, options): TextureLevel[][] {\n  const ktx2File = new KTX2File(new Uint8Array(data));\n\n  try {\n    if (!ktx2File.startTranscoding()) {\n      throw new Error('failed to start KTX2 transcoding');\n    }\n    const levelsCount = ktx2File.getLevels();\n    const levels: TextureLevel[] = [];\n\n    for (let levelIndex = 0; levelIndex < levelsCount; levelIndex++) {\n      levels.push(transcodeKTX2Image(ktx2File, levelIndex, options));\n      break; // texture app can only show one level for some reason\n    }\n\n    return [levels];\n  } finally {\n    ktx2File.close();\n    ktx2File.delete();\n  }\n}\n\n/**\n * Parse the particular level image of a ktx2 file\n * @param ktx2File\n * @param levelIndex\n * @param options\n * @returns\n */\nfunction transcodeKTX2Image(ktx2File, levelIndex: number, options): TextureLevel {\n  const {alphaFlag, height, width} = ktx2File.getImageLevelInfo(levelIndex, 0, 0);\n\n  // Check options for output format etc\n  const {compressed, format, basisFormat} = getBasisOptions(options, alphaFlag);\n\n  const decodedSize = ktx2File.getImageTranscodedSizeInBytes(\n    levelIndex,\n    0 /* layerIndex */,\n    0 /* faceIndex */,\n    basisFormat\n  );\n  const decodedData = new Uint8Array(decodedSize);\n\n  if (\n    !ktx2File.transcodeImage(\n      decodedData,\n      levelIndex,\n      0 /* layerIndex */,\n      0 /* faceIndex */,\n      basisFormat,\n      0,\n      -1 /* channel0 */,\n      -1 /* channel1 */\n    )\n  ) {\n    throw new Error('Failed to transcode KTX2 image');\n  }\n\n  return {\n    // standard loaders.gl image category payload\n    width,\n    height,\n    data: decodedData,\n    compressed,\n\n    // Additional fields\n    levelSize: decodedSize,\n    hasAlpha: alphaFlag,\n    format\n  };\n}\n\n/**\n * Get BasisFormat by loader format option\n * @param options\n * @param hasAlpha\n * @returns BasisFormat data\n */\nfunction getBasisOptions(options, hasAlpha: boolean): BasisOutputOptions {\n  let format = options && options.basis && options.basis.format;\n  if (format === 'auto') {\n    format = selectSupportedBasisFormat();\n  }\n  if (typeof format === 'object') {\n    format = hasAlpha ? format.alpha : format.noAlpha;\n  }\n  format = format.toLowerCase();\n  return OutputFormat[format];\n}\n\n/**\n * Select transcode format from the list of supported formats\n * @returns key for OutputFormat map\n */\nexport function selectSupportedBasisFormat():\n  | BasisFormat\n  | {\n      alpha: BasisFormat;\n      noAlpha: BasisFormat;\n    } {\n  const supportedFormats = getSupportedGPUTextureFormats();\n  if (supportedFormats.has('astc')) {\n    return 'astc-4x4';\n  } else if (supportedFormats.has('dxt')) {\n    return {\n      alpha: 'bc3',\n      noAlpha: 'bc1'\n    };\n  } else if (supportedFormats.has('pvrtc')) {\n    return {\n      alpha: 'pvrtc1-4-rgba',\n      noAlpha: 'pvrtc1-4-rgb'\n    };\n  } else if (supportedFormats.has('etc1')) {\n    return 'etc1';\n  } else if (supportedFormats.has('etc2')) {\n    return 'etc2';\n  }\n  return 'rgb565';\n}\n"], "mappings": ";;;;;;;AAEA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,aAAA,GAAAD,OAAA;AACA,IAAAE,eAAA,GAAAF,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AA2BA,MAAMI,YAAgD,GAAG;EACvDC,IAAI,EAAE;IACJC,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAEC,qCAAuB,CAACC;EAClC,CAAC;EACDC,IAAI,EAAE;IAACL,WAAW,EAAE,CAAC;IAAEC,UAAU,EAAE;EAAI,CAAC;EACxCK,GAAG,EAAE;IACHN,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAEC,qCAAuB,CAACI;EAClC,CAAC;EACDC,GAAG,EAAE;IACHR,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAEC,qCAAuB,CAACM;EAClC,CAAC;EACDC,GAAG,EAAE;IAACV,WAAW,EAAE,CAAC;IAAEC,UAAU,EAAE;EAAI,CAAC;EACvCU,GAAG,EAAE;IAACX,WAAW,EAAE,CAAC;IAAEC,UAAU,EAAE;EAAI,CAAC;EACvC,oBAAoB,EAAE;IAACD,WAAW,EAAE,CAAC;IAAEC,UAAU,EAAE;EAAI,CAAC;EACxD,QAAQ,EAAE;IAACD,WAAW,EAAE,CAAC;IAAEC,UAAU,EAAE;EAAI,CAAC;EAC5C,cAAc,EAAE;IACdD,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAEC,qCAAuB,CAACS;EAClC,CAAC;EACD,eAAe,EAAE;IACfZ,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAEC,qCAAuB,CAACU;EAClC,CAAC;EACD,UAAU,EAAE;IACVb,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAEC,qCAAuB,CAACW;EAClC,CAAC;EACD,SAAS,EAAE;IAACd,WAAW,EAAE,EAAE;IAAEC,UAAU,EAAE;EAAI,CAAC;EAC9C,6BAA6B,EAAE;IAACD,WAAW,EAAE,EAAE;IAAEC,UAAU,EAAE;EAAI,CAAC;EAClEc,MAAM,EAAE;IAACf,WAAW,EAAE,EAAE;IAAEC,UAAU,EAAE;EAAK,CAAC;EAC5Ce,MAAM,EAAE;IAAChB,WAAW,EAAE,EAAE;IAAEC,UAAU,EAAE;EAAK,CAAC;EAC5CgB,MAAM,EAAE;IAACjB,WAAW,EAAE,EAAE;IAAEC,UAAU,EAAE;EAAK,CAAC;EAC5CiB,QAAQ,EAAE;IAAClB,WAAW,EAAE,EAAE;IAAEC,UAAU,EAAE;EAAK;AAC/C,CAAC;AAQc,eAAekB,UAAUA,CAACC,IAAiB,EAAEC,OAAO,EAA6B;EAC9F,IAAIA,OAAO,CAACC,KAAK,CAACC,eAAe,KAAK,MAAM,EAAE;IAC5C,IAAI,IAAAC,eAAK,EAACJ,IAAI,CAAC,EAAE;MACf,MAAMK,gBAAgB,GAAG,MAAM,IAAAC,yCAAsB,EAACL,OAAO,CAAC;MAC9D,OAAOM,aAAa,CAACF,gBAAgB,CAACG,QAAQ,EAAER,IAAI,EAAEC,OAAO,CAAC;IAChE;IACA,MAAM;MAACQ;IAAS,CAAC,GAAG,MAAM,IAAAC,2CAAwB,EAACT,OAAO,CAAC;IAC3D,OAAOU,cAAc,CAACF,SAAS,EAAET,IAAI,EAAEC,OAAO,CAAC;EACjD;EACA,QAAQA,OAAO,CAACC,KAAK,CAACU,MAAM;IAC1B,KAAK,SAAS;MACZ,MAAMP,gBAAgB,GAAG,MAAM,IAAAC,yCAAsB,EAACL,OAAO,CAAC;MAC9D,QAAQA,OAAO,CAACC,KAAK,CAACC,eAAe;QACnC,KAAK,MAAM;UACT,OAAOI,aAAa,CAACF,gBAAgB,CAACG,QAAQ,EAAER,IAAI,EAAEC,OAAO,CAAC;QAChE,KAAK,OAAO;QACZ;UACE,OAAOU,cAAc,CAACN,gBAAgB,CAACI,SAAS,EAAET,IAAI,EAAEC,OAAO,CAAC;MACpE;IACF,KAAK,YAAY;IACjB;MACE,MAAM;QAACQ;MAAS,CAAC,GAAG,MAAM,IAAAC,2CAAwB,EAACT,OAAO,CAAC;MAC3D,OAAOU,cAAc,CAACF,SAAS,EAAET,IAAI,EAAEC,OAAO,CAAC;EACnD;AACF;AASA,SAASU,cAAcA,CAACF,SAAS,EAAET,IAAI,EAAEC,OAAO,EAAoB;EAClE,MAAMY,SAAS,GAAG,IAAIJ,SAAS,CAAC,IAAIK,UAAU,CAACd,IAAI,CAAC,CAAC;EAErD,IAAI;IACF,IAAI,CAACa,SAAS,CAACE,gBAAgB,CAAC,CAAC,EAAE;MACjC,MAAM,IAAIC,KAAK,CAAC,mCAAmC,CAAC;IACtD;IAEA,MAAMC,UAAU,GAAGJ,SAAS,CAACK,YAAY,CAAC,CAAC;IAC3C,MAAMC,MAAwB,GAAG,EAAE;IAEnC,KAAK,IAAIC,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAGH,UAAU,EAAEG,UAAU,EAAE,EAAE;MAC9D,MAAMC,WAAW,GAAGR,SAAS,CAACS,YAAY,CAACF,UAAU,CAAC;MACtD,MAAMG,MAAsB,GAAG,EAAE;MAEjC,KAAK,IAAIC,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAGH,WAAW,EAAEG,UAAU,EAAE,EAAE;QAC/DD,MAAM,CAACE,IAAI,CAACC,cAAc,CAACb,SAAS,EAAEO,UAAU,EAAEI,UAAU,EAAEvB,OAAO,CAAC,CAAC;MACzE;MAEAkB,MAAM,CAACM,IAAI,CAACF,MAAM,CAAC;IACrB;IAEA,OAAOJ,MAAM;EACf,CAAC,SAAS;IACRN,SAAS,CAACc,KAAK,CAAC,CAAC;IACjBd,SAAS,CAACe,MAAM,CAAC,CAAC;EACpB;AACF;AAUA,SAASF,cAAcA,CAACb,SAAS,EAAEO,UAAU,EAAEI,UAAU,EAAEvB,OAAO,EAAgB;EAChF,MAAM4B,KAAK,GAAGhB,SAAS,CAACiB,aAAa,CAACV,UAAU,EAAEI,UAAU,CAAC;EAC7D,MAAMO,MAAM,GAAGlB,SAAS,CAACmB,cAAc,CAACZ,UAAU,EAAEI,UAAU,CAAC;EAG/D,MAAMS,QAAQ,GAAGpB,SAAS,CAACqB,WAAW,CAA6B,CAAC;EAGpE,MAAM;IAACrD,UAAU;IAAEC,MAAM;IAAEF;EAAW,CAAC,GAAGuD,eAAe,CAAClC,OAAO,EAAEgC,QAAQ,CAAC;EAE5E,MAAMG,WAAW,GAAGvB,SAAS,CAACwB,6BAA6B,CAACjB,UAAU,EAAEI,UAAU,EAAE5C,WAAW,CAAC;EAChG,MAAM0D,WAAW,GAAG,IAAIxB,UAAU,CAACsB,WAAW,CAAC;EAE/C,IAAI,CAACvB,SAAS,CAACa,cAAc,CAACY,WAAW,EAAElB,UAAU,EAAEI,UAAU,EAAE5C,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;IACrF,MAAM,IAAIoC,KAAK,CAAC,mCAAmC,CAAC;EACtD;EAEA,OAAO;IAELa,KAAK;IACLE,MAAM;IACN/B,IAAI,EAAEsC,WAAW;IACjBzD,UAAU;IACVC,MAAM;IAINmD;EACF,CAAC;AACH;AASA,SAAS1B,aAAaA,CAACC,QAAQ,EAAER,IAAiB,EAAEC,OAAO,EAAoB;EAC7E,MAAMsC,QAAQ,GAAG,IAAI/B,QAAQ,CAAC,IAAIM,UAAU,CAACd,IAAI,CAAC,CAAC;EAEnD,IAAI;IACF,IAAI,CAACuC,QAAQ,CAACxB,gBAAgB,CAAC,CAAC,EAAE;MAChC,MAAM,IAAIC,KAAK,CAAC,kCAAkC,CAAC;IACrD;IACA,MAAMK,WAAW,GAAGkB,QAAQ,CAACC,SAAS,CAAC,CAAC;IACxC,MAAMjB,MAAsB,GAAG,EAAE;IAEjC,KAAK,IAAIC,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAGH,WAAW,EAAEG,UAAU,EAAE,EAAE;MAC/DD,MAAM,CAACE,IAAI,CAACgB,kBAAkB,CAACF,QAAQ,EAAEf,UAAU,EAAEvB,OAAO,CAAC,CAAC;MAC9D;IACF;IAEA,OAAO,CAACsB,MAAM,CAAC;EACjB,CAAC,SAAS;IACRgB,QAAQ,CAACZ,KAAK,CAAC,CAAC;IAChBY,QAAQ,CAACX,MAAM,CAAC,CAAC;EACnB;AACF;AASA,SAASa,kBAAkBA,CAACF,QAAQ,EAAEf,UAAkB,EAAEvB,OAAO,EAAgB;EAC/E,MAAM;IAACyC,SAAS;IAAEX,MAAM;IAAEF;EAAK,CAAC,GAAGU,QAAQ,CAACI,iBAAiB,CAACnB,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;EAG/E,MAAM;IAAC3C,UAAU;IAAEC,MAAM;IAAEF;EAAW,CAAC,GAAGuD,eAAe,CAAClC,OAAO,EAAEyC,SAAS,CAAC;EAE7E,MAAMN,WAAW,GAAGG,QAAQ,CAACF,6BAA6B,CACxDb,UAAU,EACV,CAAC,EACD,CAAC,EACD5C,WACF,CAAC;EACD,MAAM0D,WAAW,GAAG,IAAIxB,UAAU,CAACsB,WAAW,CAAC;EAE/C,IACE,CAACG,QAAQ,CAACb,cAAc,CACtBY,WAAW,EACXd,UAAU,EACV,CAAC,EACD,CAAC,EACD5C,WAAW,EACX,CAAC,EACD,CAAC,CAAC,EACF,CAAC,CACH,CAAC,EACD;IACA,MAAM,IAAIoC,KAAK,CAAC,gCAAgC,CAAC;EACnD;EAEA,OAAO;IAELa,KAAK;IACLE,MAAM;IACN/B,IAAI,EAAEsC,WAAW;IACjBzD,UAAU;IAGV+D,SAAS,EAAER,WAAW;IACtBH,QAAQ,EAAES,SAAS;IACnB5D;EACF,CAAC;AACH;AAQA,SAASqD,eAAeA,CAAClC,OAAO,EAAEgC,QAAiB,EAAsB;EACvE,IAAInD,MAAM,GAAGmB,OAAO,IAAIA,OAAO,CAACC,KAAK,IAAID,OAAO,CAACC,KAAK,CAACpB,MAAM;EAC7D,IAAIA,MAAM,KAAK,MAAM,EAAE;IACrBA,MAAM,GAAG+D,0BAA0B,CAAC,CAAC;EACvC;EACA,IAAI,OAAO/D,MAAM,KAAK,QAAQ,EAAE;IAC9BA,MAAM,GAAGmD,QAAQ,GAAGnD,MAAM,CAACgE,KAAK,GAAGhE,MAAM,CAACiE,OAAO;EACnD;EACAjE,MAAM,GAAGA,MAAM,CAACkE,WAAW,CAAC,CAAC;EAC7B,OAAOtE,YAAY,CAACI,MAAM,CAAC;AAC7B;AAMO,SAAS+D,0BAA0BA,CAAA,EAKpC;EACJ,MAAMI,gBAAgB,GAAG,IAAAC,6CAA6B,EAAC,CAAC;EACxD,IAAID,gBAAgB,CAACE,GAAG,CAAC,MAAM,CAAC,EAAE;IAChC,OAAO,UAAU;EACnB,CAAC,MAAM,IAAIF,gBAAgB,CAACE,GAAG,CAAC,KAAK,CAAC,EAAE;IACtC,OAAO;MACLL,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE;IACX,CAAC;EACH,CAAC,MAAM,IAAIE,gBAAgB,CAACE,GAAG,CAAC,OAAO,CAAC,EAAE;IACxC,OAAO;MACLL,KAAK,EAAE,eAAe;MACtBC,OAAO,EAAE;IACX,CAAC;EACH,CAAC,MAAM,IAAIE,gBAAgB,CAACE,GAAG,CAAC,MAAM,CAAC,EAAE;IACvC,OAAO,MAAM;EACf,CAAC,MAAM,IAAIF,gBAAgB,CAACE,GAAG,CAAC,MAAM,CAAC,EAAE;IACvC,OAAO,MAAM;EACf;EACA,OAAO,QAAQ;AACjB"}