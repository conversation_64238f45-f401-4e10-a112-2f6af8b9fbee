{"version": 3, "file": "load-image-cube.js", "names": ["_images", "require", "_loadImage", "_deepLoad", "GL_TEXTURE_CUBE_MAP_POSITIVE_X", "GL_TEXTURE_CUBE_MAP_NEGATIVE_X", "GL_TEXTURE_CUBE_MAP_POSITIVE_Y", "GL_TEXTURE_CUBE_MAP_NEGATIVE_Y", "GL_TEXTURE_CUBE_MAP_POSITIVE_Z", "GL_TEXTURE_CUBE_MAP_NEGATIVE_Z", "CUBE_FACES", "face", "direction", "axis", "sign", "getImageCubeUrls", "getUrl", "options", "urls", "promises", "index", "i", "length", "promise", "getImageUrls", "then", "url", "push", "Promise", "all", "loadImageTextureCube", "arguments", "undefined", "deepLoad", "ImageLoader", "parse"], "sources": ["../../../../src/lib/texture-api/load-image-cube.ts"], "sourcesContent": ["// loaders.gl, MIT license\nimport {ImageLoader} from '@loaders.gl/images';\nimport type {GetUrl, UrlOptions} from './texture-api-types';\nimport {getImageUrls} from './load-image';\nimport {deepLoad} from './deep-load';\n\n// Returned map will be have keys corresponding to GL cubemap constants\nconst GL_TEXTURE_CUBE_MAP_POSITIVE_X = 0x8515;\nconst GL_TEXTURE_CUBE_MAP_NEGATIVE_X = 0x8516;\nconst GL_TEXTURE_CUBE_MAP_POSITIVE_Y = 0x8517;\nconst GL_TEXTURE_CUBE_MAP_NEGATIVE_Y = 0x8518;\nconst GL_TEXTURE_CUBE_MAP_POSITIVE_Z = 0x8519;\nconst GL_TEXTURE_CUBE_MAP_NEGATIVE_Z = 0x851a;\n\nconst CUBE_FACES = [\n  {face: GL_TEXTURE_CUBE_MAP_POSITIVE_X, direction: 'right', axis: 'x', sign: 'positive'},\n  {face: GL_TEXTURE_CUBE_MAP_NEGATIVE_X, direction: 'left', axis: 'x', sign: 'negative'},\n  {face: GL_TEXTURE_CUBE_MAP_POSITIVE_Y, direction: 'top', axis: 'y', sign: 'positive'},\n  {face: GL_TEXTURE_CUBE_MAP_NEGATIVE_Y, direction: 'bottom', axis: 'y', sign: 'negative'},\n  {face: GL_TEXTURE_CUBE_MAP_POSITIVE_Z, direction: 'front', axis: 'z', sign: 'positive'},\n  {face: GL_TEXTURE_CUBE_MAP_NEGATIVE_Z, direction: 'back', axis: 'z', sign: 'negative'}\n];\n\nexport type ImageCubeTexture = {\n  GL_TEXTURE_CUBE_MAP_POSITIVE_X: any;\n  GL_TEXTURE_CUBE_MAP_NEGATIVE_X: any;\n  GL_TEXTURE_CUBE_MAP_POSITIVE_Y: any;\n  GL_TEXTURE_CUBE_MAP_NEGATIVE_Y: any;\n  GL_TEXTURE_CUBE_MAP_POSITIVE_Z: any;\n  GL_TEXTURE_CUBE_MAP_NEGATIVE_Z: any;\n};\n\n// Returns an object with six key-value pairs containing the urls (or url mip arrays)\n// for each cube face\nexport async function getImageCubeUrls(getUrl: GetUrl, options: UrlOptions) {\n  // Calculate URLs\n  const urls: Record<number, string | string[]> = {};\n  const promises: Promise<any>[] = [];\n\n  let index = 0;\n  for (let i = 0; i < CUBE_FACES.length; ++i) {\n    const face = CUBE_FACES[index];\n    const promise = getImageUrls(getUrl, options, {...face, index: index++}).then((url) => {\n      urls[face.face] = url;\n    });\n    promises.push(promise);\n  }\n\n  await Promise.all(promises);\n\n  return urls;\n}\n\n// Returns an object with six key-value pairs containing the images (or image mip arrays)\n// for each cube face\nexport async function loadImageTextureCube(\n  getUrl: GetUrl,\n  options = {}\n): Promise<ImageCubeTexture> {\n  const urls = await getImageCubeUrls(getUrl, options);\n  return (await deepLoad(urls, ImageLoader.parse, options)) as ImageCubeTexture;\n}\n"], "mappings": ";;;;;;;AACA,IAAAA,OAAA,GAAAC,OAAA;AAEA,IAAAC,UAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AAGA,MAAMG,8BAA8B,GAAG,MAAM;AAC7C,MAAMC,8BAA8B,GAAG,MAAM;AAC7C,MAAMC,8BAA8B,GAAG,MAAM;AAC7C,MAAMC,8BAA8B,GAAG,MAAM;AAC7C,MAAMC,8BAA8B,GAAG,MAAM;AAC7C,MAAMC,8BAA8B,GAAG,MAAM;AAE7C,MAAMC,UAAU,GAAG,CACjB;EAACC,IAAI,EAAEP,8BAA8B;EAAEQ,SAAS,EAAE,OAAO;EAAEC,IAAI,EAAE,GAAG;EAAEC,IAAI,EAAE;AAAU,CAAC,EACvF;EAACH,IAAI,EAAEN,8BAA8B;EAAEO,SAAS,EAAE,MAAM;EAAEC,IAAI,EAAE,GAAG;EAAEC,IAAI,EAAE;AAAU,CAAC,EACtF;EAACH,IAAI,EAAEL,8BAA8B;EAAEM,SAAS,EAAE,KAAK;EAAEC,IAAI,EAAE,GAAG;EAAEC,IAAI,EAAE;AAAU,CAAC,EACrF;EAACH,IAAI,EAAEJ,8BAA8B;EAAEK,SAAS,EAAE,QAAQ;EAAEC,IAAI,EAAE,GAAG;EAAEC,IAAI,EAAE;AAAU,CAAC,EACxF;EAACH,IAAI,EAAEH,8BAA8B;EAAEI,SAAS,EAAE,OAAO;EAAEC,IAAI,EAAE,GAAG;EAAEC,IAAI,EAAE;AAAU,CAAC,EACvF;EAACH,IAAI,EAAEF,8BAA8B;EAAEG,SAAS,EAAE,MAAM;EAAEC,IAAI,EAAE,GAAG;EAAEC,IAAI,EAAE;AAAU,CAAC,CACvF;AAaM,eAAeC,gBAAgBA,CAACC,MAAc,EAAEC,OAAmB,EAAE;EAE1E,MAAMC,IAAuC,GAAG,CAAC,CAAC;EAClD,MAAMC,QAAwB,GAAG,EAAE;EAEnC,IAAIC,KAAK,GAAG,CAAC;EACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,UAAU,CAACY,MAAM,EAAE,EAAED,CAAC,EAAE;IAC1C,MAAMV,IAAI,GAAGD,UAAU,CAACU,KAAK,CAAC;IAC9B,MAAMG,OAAO,GAAG,IAAAC,uBAAY,EAACR,MAAM,EAAEC,OAAO,EAAE;MAAC,GAAGN,IAAI;MAAES,KAAK,EAAEA,KAAK;IAAE,CAAC,CAAC,CAACK,IAAI,CAAEC,GAAG,IAAK;MACrFR,IAAI,CAACP,IAAI,CAACA,IAAI,CAAC,GAAGe,GAAG;IACvB,CAAC,CAAC;IACFP,QAAQ,CAACQ,IAAI,CAACJ,OAAO,CAAC;EACxB;EAEA,MAAMK,OAAO,CAACC,GAAG,CAACV,QAAQ,CAAC;EAE3B,OAAOD,IAAI;AACb;AAIO,eAAeY,oBAAoBA,CACxCd,MAAc,EAEa;EAAA,IAD3BC,OAAO,GAAAc,SAAA,CAAAT,MAAA,QAAAS,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;EAEZ,MAAMb,IAAI,GAAG,MAAMH,gBAAgB,CAACC,MAAM,EAAEC,OAAO,CAAC;EACpD,OAAQ,MAAM,IAAAgB,kBAAQ,EAACf,IAAI,EAAEgB,mBAAW,CAACC,KAAK,EAAElB,OAAO,CAAC;AAC1D"}