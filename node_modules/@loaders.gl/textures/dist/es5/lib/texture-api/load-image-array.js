"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getImageArrayUrls = getImageArrayUrls;
exports.loadImageTextureArray = loadImageTextureArray;
var _images = require("@loaders.gl/images");
var _loadImage = require("./load-image");
var _deepLoad = require("./deep-load");
async function loadImageTextureArray(count, getUrl) {
  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
  const imageUrls = await getImageArrayUrls(count, getUrl, options);
  return await (0, _deepLoad.deepLoad)(imageUrls, _images.ImageLoader.parse, options);
}
async function getImageArrayUrls(count, getUrl) {
  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
  const promises = [];
  for (let index = 0; index < count; index++) {
    const promise = (0, _loadImage.getImageUrls)(getUrl, options, {
      index
    });
    promises.push(promise);
  }
  return await Promise.all(promises);
}
//# sourceMappingURL=load-image-array.js.map