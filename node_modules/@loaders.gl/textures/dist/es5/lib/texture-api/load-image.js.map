{"version": 3, "file": "load-image.js", "names": ["_loaderUtils", "require", "_images", "_generateUrl", "_deepLoad", "loadImageTexture", "getUrl", "options", "arguments", "length", "undefined", "imageUrls", "getImageUrls", "deepLoad", "ImageLoader", "parse", "urlOptions", "mipLevels", "image", "getMipmappedImageUrls", "generateUrl", "urls", "url", "lod", "shallowLoad", "width", "height", "getImageSize", "getMipLevels", "push", "assert", "mipLevel", "size", "Math", "floor", "log2", "max"], "sources": ["../../../../src/lib/texture-api/load-image.ts"], "sourcesContent": ["import {assert} from '@loaders.gl/loader-utils';\nimport {ImageLoader, getImageSize} from '@loaders.gl/images';\nimport type {GetUrl, UrlOptions} from './texture-api-types';\nimport {generateUrl} from './generate-url';\nimport {deepLoad, shallowLoad} from './deep-load';\n\nexport async function loadImageTexture(getUrl: string | GetUrl, options = {}): Promise<any> {\n  const imageUrls = await getImageUrls(getUrl, options);\n  return await deepLoad(imageUrls, ImageLoader.parse, options);\n}\n\nexport async function getImageUrls(\n  getUrl: string | GetUrl,\n  options: any,\n  urlOptions: UrlOptions = {}\n): Promise<any> {\n  const mipLevels = (options && options.image && options.image.mipLevels) || 0;\n  return mipLevels !== 0\n    ? await getMipmappedImageUrls(getUrl, mipLevels, options, urlOptions)\n    : generateUrl(getUrl, options, urlOptions);\n}\n\nasync function getMipmappedImageUrls(\n  getUrl: string | GetUrl,\n  mipLevels: number | 'auto',\n  options: any,\n  urlOptions: UrlOptions\n): Promise<string[]> {\n  const urls: string[] = [];\n\n  // If no mip levels supplied, we need to load the level 0 image and calculate based on size\n  if (mipLevels === 'auto') {\n    const url = generateUrl(getUrl, options, {...urlOptions, lod: 0});\n    const image = await shallowLoad(url, ImageLoader.parse, options);\n\n    const {width, height} = getImageSize(image);\n    mipLevels = getMipLevels({width, height});\n\n    // TODO - push image and make `deepLoad` pass through non-url values, avoid loading twice?\n    urls.push(url);\n  }\n\n  // We now know how many mipLevels we need, remaining image urls can now be constructed\n  assert(mipLevels > 0);\n\n  for (let mipLevel = urls.length; mipLevel < mipLevels; ++mipLevel) {\n    const url = generateUrl(getUrl, options, {...urlOptions, lod: mipLevel});\n    urls.push(url);\n  }\n\n  return urls;\n}\n\n// Calculates number of mipmaps based on texture size (log2)\nexport function getMipLevels(size: {width: number; height: number}): number {\n  return 1 + Math.floor(Math.log2(Math.max(size.width, size.height)));\n}\n"], "mappings": ";;;;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AAEA,IAAAE,YAAA,GAAAF,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AAEO,eAAeI,gBAAgBA,CAACC,MAAuB,EAA8B;EAAA,IAA5BC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC1E,MAAMG,SAAS,GAAG,MAAMC,YAAY,CAACN,MAAM,EAAEC,OAAO,CAAC;EACrD,OAAO,MAAM,IAAAM,kBAAQ,EAACF,SAAS,EAAEG,mBAAW,CAACC,KAAK,EAAER,OAAO,CAAC;AAC9D;AAEO,eAAeK,YAAYA,CAChCN,MAAuB,EACvBC,OAAY,EAEE;EAAA,IADdS,UAAsB,GAAAR,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAE3B,MAAMS,SAAS,GAAIV,OAAO,IAAIA,OAAO,CAACW,KAAK,IAAIX,OAAO,CAACW,KAAK,CAACD,SAAS,IAAK,CAAC;EAC5E,OAAOA,SAAS,KAAK,CAAC,GAClB,MAAME,qBAAqB,CAACb,MAAM,EAAEW,SAAS,EAAEV,OAAO,EAAES,UAAU,CAAC,GACnE,IAAAI,wBAAW,EAACd,MAAM,EAAEC,OAAO,EAAES,UAAU,CAAC;AAC9C;AAEA,eAAeG,qBAAqBA,CAClCb,MAAuB,EACvBW,SAA0B,EAC1BV,OAAY,EACZS,UAAsB,EACH;EACnB,MAAMK,IAAc,GAAG,EAAE;EAGzB,IAAIJ,SAAS,KAAK,MAAM,EAAE;IACxB,MAAMK,GAAG,GAAG,IAAAF,wBAAW,EAACd,MAAM,EAAEC,OAAO,EAAE;MAAC,GAAGS,UAAU;MAAEO,GAAG,EAAE;IAAC,CAAC,CAAC;IACjE,MAAML,KAAK,GAAG,MAAM,IAAAM,qBAAW,EAACF,GAAG,EAAER,mBAAW,CAACC,KAAK,EAAER,OAAO,CAAC;IAEhE,MAAM;MAACkB,KAAK;MAAEC;IAAM,CAAC,GAAG,IAAAC,oBAAY,EAACT,KAAK,CAAC;IAC3CD,SAAS,GAAGW,YAAY,CAAC;MAACH,KAAK;MAAEC;IAAM,CAAC,CAAC;IAGzCL,IAAI,CAACQ,IAAI,CAACP,GAAG,CAAC;EAChB;EAGA,IAAAQ,mBAAM,EAACb,SAAS,GAAG,CAAC,CAAC;EAErB,KAAK,IAAIc,QAAQ,GAAGV,IAAI,CAACZ,MAAM,EAAEsB,QAAQ,GAAGd,SAAS,EAAE,EAAEc,QAAQ,EAAE;IACjE,MAAMT,GAAG,GAAG,IAAAF,wBAAW,EAACd,MAAM,EAAEC,OAAO,EAAE;MAAC,GAAGS,UAAU;MAAEO,GAAG,EAAEQ;IAAQ,CAAC,CAAC;IACxEV,IAAI,CAACQ,IAAI,CAACP,GAAG,CAAC;EAChB;EAEA,OAAOD,IAAI;AACb;AAGO,SAASO,YAAYA,CAACI,IAAqC,EAAU;EAC1E,OAAO,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,IAAI,CAACF,IAAI,CAACG,GAAG,CAACJ,IAAI,CAACP,KAAK,EAAEO,IAAI,CAACN,MAAM,CAAC,CAAC,CAAC;AACrE"}