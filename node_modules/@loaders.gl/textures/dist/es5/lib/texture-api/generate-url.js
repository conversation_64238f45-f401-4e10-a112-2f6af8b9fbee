"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.generateUrl = generateUrl;
var _loaderUtils = require("@loaders.gl/loader-utils");
function generateUrl(getUrl, options, urlOptions) {
  let url = typeof getUrl === 'function' ? getUrl({
    ...options,
    ...urlOptions
  }) : getUrl;
  const baseUrl = options.baseUrl;
  if (baseUrl) {
    url = baseUrl[baseUrl.length - 1] === '/' ? "".concat(baseUrl).concat(url) : "".concat(baseUrl, "/").concat(url);
  }
  return (0, _loaderUtils.resolvePath)(url);
}
//# sourceMappingURL=generate-url.js.map