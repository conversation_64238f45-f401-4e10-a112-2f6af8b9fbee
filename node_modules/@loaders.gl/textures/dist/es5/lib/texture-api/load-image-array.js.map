{"version": 3, "file": "load-image-array.js", "names": ["_images", "require", "_loadImage", "_deepLoad", "loadImageTextureArray", "count", "getUrl", "options", "arguments", "length", "undefined", "imageUrls", "getImageArrayUrls", "deepLoad", "ImageLoader", "parse", "promises", "index", "promise", "getImageUrls", "push", "Promise", "all"], "sources": ["../../../../src/lib/texture-api/load-image-array.ts"], "sourcesContent": ["// loaders.gl, MIT license\nimport {ImageLoader} from '@loaders.gl/images';\nimport type {GetUrl} from './texture-api-types';\nimport {getImageUrls} from './load-image';\nimport {deepLoad} from './deep-load';\n\nexport async function loadImageTextureArray(\n  count: number,\n  getUrl: GetUrl,\n  options = {}\n): Promise<any> {\n  const imageUrls = await getImageArrayUrls(count, getUrl, options);\n  return await deepLoad(imageUrls, ImageLoader.parse, options);\n}\n\nexport async function getImageArrayUrls(count: number, getUrl: GetUrl, options = {}): Promise<any> {\n  const promises: Promise<any>[] = [];\n  for (let index = 0; index < count; index++) {\n    const promise = getImageUrls(getUrl, options, {index});\n    promises.push(promise);\n  }\n  return await Promise.all(promises);\n}\n"], "mappings": ";;;;;;;AACA,IAAAA,OAAA,GAAAC,OAAA;AAEA,IAAAC,UAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AAEO,eAAeG,qBAAqBA,CACzCC,KAAa,EACbC,MAAc,EAEA;EAAA,IADdC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAEZ,MAAMG,SAAS,GAAG,MAAMC,iBAAiB,CAACP,KAAK,EAAEC,MAAM,EAAEC,OAAO,CAAC;EACjE,OAAO,MAAM,IAAAM,kBAAQ,EAACF,SAAS,EAAEG,mBAAW,CAACC,KAAK,EAAER,OAAO,CAAC;AAC9D;AAEO,eAAeK,iBAAiBA,CAACP,KAAa,EAAEC,MAAc,EAA8B;EAAA,IAA5BC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACjF,MAAMQ,QAAwB,GAAG,EAAE;EACnC,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGZ,KAAK,EAAEY,KAAK,EAAE,EAAE;IAC1C,MAAMC,OAAO,GAAG,IAAAC,uBAAY,EAACb,MAAM,EAAEC,OAAO,EAAE;MAACU;IAAK,CAAC,CAAC;IACtDD,QAAQ,CAACI,IAAI,CAACF,OAAO,CAAC;EACxB;EACA,OAAO,MAAMG,OAAO,CAACC,GAAG,CAACN,QAAQ,CAAC;AACpC"}