{"version": 3, "file": "deep-load.js", "names": ["_asyncDeepMap", "require", "deepLoad", "urlTree", "load", "options", "asyncDeepMap", "url", "shallowLoad", "response", "fetch", "arrayBuffer"], "sources": ["../../../../src/lib/texture-api/deep-load.ts"], "sourcesContent": ["// loaders.gl, MIT license\nimport {asyncDeepMap} from './async-deep-map';\n\nexport type LoadOptions = Record<string, any>;\nexport type Load = (data: ArrayBuffer, options: Record<string, any>) => Promise<any>;\n\nexport async function deepLoad(urlTree: unknown, load: Load, options: LoadOptions) {\n  return await asyncDeepMap(urlTree, (url: string) => shallowLoad(url, load, options));\n}\n\nexport async function shallowLoad(url: string, load: Load, options: LoadOptions): Promise<any> {\n  // console.error('loading', url);\n  const response = await fetch(url, options.fetch);\n  const arrayBuffer = await response.arrayBuffer();\n  return await load(arrayBuffer, options);\n}\n"], "mappings": ";;;;;;;AACA,IAAAA,aAAA,GAAAC,OAAA;AAKO,eAAeC,QAAQA,CAACC,OAAgB,EAAEC,IAAU,EAAEC,OAAoB,EAAE;EACjF,OAAO,MAAM,IAAAC,0BAAY,EAACH,OAAO,EAAGI,GAAW,IAAKC,WAAW,CAACD,GAAG,EAAEH,IAAI,EAAEC,OAAO,CAAC,CAAC;AACtF;AAEO,eAAeG,WAAWA,CAACD,GAAW,EAAEH,IAAU,EAAEC,OAAoB,EAAgB;EAE7F,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAACH,GAAG,EAAEF,OAAO,CAACK,KAAK,CAAC;EAChD,MAAMC,WAAW,GAAG,MAAMF,QAAQ,CAACE,WAAW,CAAC,CAAC;EAChD,OAAO,MAAMP,IAAI,CAACO,WAAW,EAAEN,OAAO,CAAC;AACzC"}