"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getImageUrls = getImageUrls;
exports.getMipLevels = getMipLevels;
exports.loadImageTexture = loadImageTexture;
var _loaderUtils = require("@loaders.gl/loader-utils");
var _images = require("@loaders.gl/images");
var _generateUrl = require("./generate-url");
var _deepLoad = require("./deep-load");
async function loadImageTexture(getUrl) {
  let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  const imageUrls = await getImageUrls(getUrl, options);
  return await (0, _deepLoad.deepLoad)(imageUrls, _images.ImageLoader.parse, options);
}
async function getImageUrls(getUrl, options) {
  let urlOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
  const mipLevels = options && options.image && options.image.mipLevels || 0;
  return mipLevels !== 0 ? await getMipmappedImageUrls(getUrl, mipLevels, options, urlOptions) : (0, _generateUrl.generateUrl)(getUrl, options, urlOptions);
}
async function getMipmappedImageUrls(getUrl, mipLevels, options, urlOptions) {
  const urls = [];
  if (mipLevels === 'auto') {
    const url = (0, _generateUrl.generateUrl)(getUrl, options, {
      ...urlOptions,
      lod: 0
    });
    const image = await (0, _deepLoad.shallowLoad)(url, _images.ImageLoader.parse, options);
    const {
      width,
      height
    } = (0, _images.getImageSize)(image);
    mipLevels = getMipLevels({
      width,
      height
    });
    urls.push(url);
  }
  (0, _loaderUtils.assert)(mipLevels > 0);
  for (let mipLevel = urls.length; mipLevel < mipLevels; ++mipLevel) {
    const url = (0, _generateUrl.generateUrl)(getUrl, options, {
      ...urlOptions,
      lod: mipLevel
    });
    urls.push(url);
  }
  return urls;
}
function getMipLevels(size) {
  return 1 + Math.floor(Math.log2(Math.max(size.width, size.height)));
}
//# sourceMappingURL=load-image.js.map