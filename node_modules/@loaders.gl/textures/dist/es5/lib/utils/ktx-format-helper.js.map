{"version": 3, "file": "ktx-format-helper.js", "names": ["_glExtensions", "require", "VULKAN_TO_WEBGL_FORMAT_MAP", "GL_EXTENSIONS_CONSTANTS", "COMPRESSED_RGB_S3TC_DXT1_EXT", "COMPRESSED_SRGB_S3TC_DXT1_EXT", "COMPRESSED_RGBA_S3TC_DXT1_EXT", "COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT", "COMPRESSED_RGBA_S3TC_DXT3_EXT", "COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT", "COMPRESSED_RGBA_S3TC_DXT5_EXT", "COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT", "COMPRESSED_RED_RGTC1_EXT", "COMPRESSED_SIGNED_RED_RGTC1_EXT", "COMPRESSED_RED_GREEN_RGTC2_EXT", "COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT", "COMPRESSED_RGB8_ETC2", "COMPRESSED_SRGB8_ETC2", "COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2", "COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2", "COMPRESSED_RGBA8_ETC2_EAC", "COMPRESSED_SRGB8_ALPHA8_ETC2_EAC", "COMPRESSED_R11_EAC", "COMPRESSED_SIGNED_R11_EAC", "COMPRESSED_RG11_EAC", "COMPRESSED_SIGNED_RG11_EAC", "COMPRESSED_RGBA_ASTC_4x4_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR", "COMPRESSED_RGBA_ASTC_5x4_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_5X4_KHR", "COMPRESSED_RGBA_ASTC_5x5_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_5x5_KHR", "COMPRESSED_RGBA_ASTC_6x5_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_6x5_KHR", "COMPRESSED_RGBA_ASTC_6x6_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_6x6_KHR", "COMPRESSED_RGBA_ASTC_8x5_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_8x5_KHR", "COMPRESSED_RGBA_ASTC_8x6_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_8x6_KHR", "COMPRESSED_RGBA_ASTC_8x8_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_8x8_KHR", "COMPRESSED_RGBA_ASTC_10x5_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_10x5_KHR", "COMPRESSED_RGBA_ASTC_10x6_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_10x6_KHR", "COMPRESSED_RGBA_ASTC_10x8_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_10x8_KHR", "COMPRESSED_RGBA_ASTC_10x10_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_10x10_KHR", "COMPRESSED_RGBA_ASTC_12x10_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_12x10_KHR", "COMPRESSED_RGBA_ASTC_12x12_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_12x12_KHR", "COMPRESSED_RGBA_PVRTC_2BPPV1_IMG", "COMPRESSED_RGBA_PVRTC_4BPPV1_IMG", "mapVkFormatToWebGL", "vkFormat"], "sources": ["../../../../src/lib/utils/ktx-format-helper.ts"], "sourcesContent": ["import {GL_EXTENSIONS_CONSTANTS} from '../gl-extensions';\n\nconst VULKAN_TO_WEBGL_FORMAT_MAP: Record<number, number> = {\n  131: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_S3TC_DXT1_EXT,\n  132: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB_S3TC_DXT1_EXT,\n  133: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT1_EXT,\n  134: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT,\n  135: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT3_EXT,\n  136: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT,\n  137: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT5_EXT,\n  138: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT,\n  139: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RED_RGTC1_EXT,\n  140: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SIGNED_RED_RGTC1_EXT,\n  141: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RED_GREEN_RGTC2_EXT,\n  142: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT,\n  147: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB8_ETC2,\n  148: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ETC2,\n  149: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2,\n  150: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2,\n  151: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA8_ETC2_EAC,\n  152: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC,\n  153: GL_EXTENSIONS_CONSTANTS.COMPRESSED_R11_EAC,\n  154: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SIGNED_R11_EAC,\n  155: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RG11_EAC,\n  156: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SIGNED_RG11_EAC,\n  // @ts-ignore\n  157: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_4x4_KHR,\n  // @ts-ignore\n  158: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR,\n  // @ts-ignore\n  159: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_5x4_KHR,\n  // @ts-ignore\n  160: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_5X4_KHR,\n  // @ts-ignore\n  161: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_5x5_KHR,\n  // @ts-ignore\n  162: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_5x5_KHR,\n  // @ts-ignore\n  163: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_6x5_KHR,\n  // @ts-ignore\n  164: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_6x5_KHR,\n  // @ts-ignore\n  165: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_6x6_KHR,\n  // @ts-ignore\n  166: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_6x6_KHR,\n  // @ts-ignore\n  167: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8x5_KHR,\n  // @ts-ignore\n  168: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_8x5_KHR,\n  // @ts-ignore\n  169: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8x6_KHR,\n  // @ts-ignore\n  170: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_8x6_KHR,\n  // @ts-ignore\n  171: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8x8_KHR,\n  // @ts-ignore\n  172: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_8x8_KHR,\n  // @ts-ignore\n  173: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x5_KHR,\n  // @ts-ignore\n  174: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10x5_KHR,\n  // @ts-ignore\n  175: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x6_KHR,\n  // @ts-ignore\n  176: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10x6_KHR,\n  // @ts-ignore\n  177: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x8_KHR,\n  // @ts-ignore\n  178: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10x8_KHR,\n  // @ts-ignore\n  179: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x10_KHR,\n  // @ts-ignore\n  180: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_10x10_KHR,\n  // @ts-ignore\n  181: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_12x10_KHR,\n  // @ts-ignore\n  182: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_12x10_KHR,\n  // @ts-ignore\n  183: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_12x12_KHR,\n  // @ts-ignore\n  184: GL_EXTENSIONS_CONSTANTS.COMPRESSED_SRGB8_ALPHA8_ASTC_12x12_KHR,\n  1000054000: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG,\n  1000054001: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG,\n  // @ts-ignore\n  1000066000: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_4x4_KHR,\n  // @ts-ignore\n  1000066001: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_5x4_KHR,\n  // @ts-ignore\n  1000066002: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_5x5_KHR,\n  // @ts-ignore\n  1000066003: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_6x5_KHR,\n  // @ts-ignore\n  1000066004: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_6x6_KHR,\n  // @ts-ignore\n  1000066005: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8x5_KHR,\n  // @ts-ignore\n  1000066006: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8x6_KHR,\n  // @ts-ignore\n  1000066007: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_8x8_KHR,\n  // @ts-ignore\n  1000066008: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x5_KHR,\n  // @ts-ignore\n  1000066009: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x6_KHR,\n  // @ts-ignore\n  1000066010: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x8_KHR,\n  // @ts-ignore\n  1000066011: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_10x10_KHR,\n  // @ts-ignore\n  1000066012: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_12x10_KHR,\n  // @ts-ignore\n  1000066013: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_12x12_KHR\n};\n\n/**\n * Returns WebGl format based on Vulkan format\n * Vulkan to WebGl format mapping provided here http://github.khronos.org/KTX-Specification/#formatMapping\n * Vulkan name to format number mapping provided here: https://www.khronos.org/registry/vulkan/specs/1.2-extensions/man/html/VkFormat.html\n * @param vkFormat\n * @returns WebGL / OpenGL constant\n */\nexport function mapVkFormatToWebGL(vkFormat: number): number {\n  return VULKAN_TO_WEBGL_FORMAT_MAP[vkFormat];\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,aAAA,GAAAC,OAAA;AAEA,MAAMC,0BAAkD,GAAG;EACzD,GAAG,EAAEC,qCAAuB,CAACC,4BAA4B;EACzD,GAAG,EAAED,qCAAuB,CAACE,6BAA6B;EAC1D,GAAG,EAAEF,qCAAuB,CAACG,6BAA6B;EAC1D,GAAG,EAAEH,qCAAuB,CAACI,mCAAmC;EAChE,GAAG,EAAEJ,qCAAuB,CAACK,6BAA6B;EAC1D,GAAG,EAAEL,qCAAuB,CAACM,mCAAmC;EAChE,GAAG,EAAEN,qCAAuB,CAACO,6BAA6B;EAC1D,GAAG,EAAEP,qCAAuB,CAACQ,mCAAmC;EAChE,GAAG,EAAER,qCAAuB,CAACS,wBAAwB;EACrD,GAAG,EAAET,qCAAuB,CAACU,+BAA+B;EAC5D,GAAG,EAAEV,qCAAuB,CAACW,8BAA8B;EAC3D,GAAG,EAAEX,qCAAuB,CAACY,qCAAqC;EAClE,GAAG,EAAEZ,qCAAuB,CAACa,oBAAoB;EACjD,GAAG,EAAEb,qCAAuB,CAACc,qBAAqB;EAClD,GAAG,EAAEd,qCAAuB,CAACe,wCAAwC;EACrE,GAAG,EAAEf,qCAAuB,CAACgB,yCAAyC;EACtE,GAAG,EAAEhB,qCAAuB,CAACiB,yBAAyB;EACtD,GAAG,EAAEjB,qCAAuB,CAACkB,gCAAgC;EAC7D,GAAG,EAAElB,qCAAuB,CAACmB,kBAAkB;EAC/C,GAAG,EAAEnB,qCAAuB,CAACoB,yBAAyB;EACtD,GAAG,EAAEpB,qCAAuB,CAACqB,mBAAmB;EAChD,GAAG,EAAErB,qCAAuB,CAACsB,0BAA0B;EAEvD,GAAG,EAAEtB,qCAAuB,CAACuB,4BAA4B;EAEzD,GAAG,EAAEvB,qCAAuB,CAACwB,oCAAoC;EAEjE,GAAG,EAAExB,qCAAuB,CAACyB,4BAA4B;EAEzD,GAAG,EAAEzB,qCAAuB,CAAC0B,oCAAoC;EAEjE,GAAG,EAAE1B,qCAAuB,CAAC2B,4BAA4B;EAEzD,GAAG,EAAE3B,qCAAuB,CAAC4B,oCAAoC;EAEjE,GAAG,EAAE5B,qCAAuB,CAAC6B,4BAA4B;EAEzD,GAAG,EAAE7B,qCAAuB,CAAC8B,oCAAoC;EAEjE,GAAG,EAAE9B,qCAAuB,CAAC+B,4BAA4B;EAEzD,GAAG,EAAE/B,qCAAuB,CAACgC,oCAAoC;EAEjE,GAAG,EAAEhC,qCAAuB,CAACiC,4BAA4B;EAEzD,GAAG,EAAEjC,qCAAuB,CAACkC,oCAAoC;EAEjE,GAAG,EAAElC,qCAAuB,CAACmC,4BAA4B;EAEzD,GAAG,EAAEnC,qCAAuB,CAACoC,oCAAoC;EAEjE,GAAG,EAAEpC,qCAAuB,CAACqC,4BAA4B;EAEzD,GAAG,EAAErC,qCAAuB,CAACsC,oCAAoC;EAEjE,GAAG,EAAEtC,qCAAuB,CAACuC,6BAA6B;EAE1D,GAAG,EAAEvC,qCAAuB,CAACwC,qCAAqC;EAElE,GAAG,EAAExC,qCAAuB,CAACyC,6BAA6B;EAE1D,GAAG,EAAEzC,qCAAuB,CAAC0C,qCAAqC;EAElE,GAAG,EAAE1C,qCAAuB,CAAC2C,6BAA6B;EAE1D,GAAG,EAAE3C,qCAAuB,CAAC4C,qCAAqC;EAElE,GAAG,EAAE5C,qCAAuB,CAAC6C,8BAA8B;EAE3D,GAAG,EAAE7C,qCAAuB,CAAC8C,sCAAsC;EAEnE,GAAG,EAAE9C,qCAAuB,CAAC+C,8BAA8B;EAE3D,GAAG,EAAE/C,qCAAuB,CAACgD,sCAAsC;EAEnE,GAAG,EAAEhD,qCAAuB,CAACiD,8BAA8B;EAE3D,GAAG,EAAEjD,qCAAuB,CAACkD,sCAAsC;EACnE,UAAU,EAAElD,qCAAuB,CAACmD,gCAAgC;EACpE,UAAU,EAAEnD,qCAAuB,CAACoD,gCAAgC;EAEpE,UAAU,EAAEpD,qCAAuB,CAACuB,4BAA4B;EAEhE,UAAU,EAAEvB,qCAAuB,CAACyB,4BAA4B;EAEhE,UAAU,EAAEzB,qCAAuB,CAAC2B,4BAA4B;EAEhE,UAAU,EAAE3B,qCAAuB,CAAC6B,4BAA4B;EAEhE,UAAU,EAAE7B,qCAAuB,CAAC+B,4BAA4B;EAEhE,UAAU,EAAE/B,qCAAuB,CAACiC,4BAA4B;EAEhE,UAAU,EAAEjC,qCAAuB,CAACmC,4BAA4B;EAEhE,UAAU,EAAEnC,qCAAuB,CAACqC,4BAA4B;EAEhE,UAAU,EAAErC,qCAAuB,CAACuC,6BAA6B;EAEjE,UAAU,EAAEvC,qCAAuB,CAACyC,6BAA6B;EAEjE,UAAU,EAAEzC,qCAAuB,CAAC2C,6BAA6B;EAEjE,UAAU,EAAE3C,qCAAuB,CAAC6C,8BAA8B;EAElE,UAAU,EAAE7C,qCAAuB,CAAC+C,8BAA8B;EAElE,UAAU,EAAE/C,qCAAuB,CAACiD;AACtC,CAAC;AASM,SAASI,kBAAkBA,CAACC,QAAgB,EAAU;EAC3D,OAAOvD,0BAA0B,CAACuD,QAAQ,CAAC;AAC7C"}