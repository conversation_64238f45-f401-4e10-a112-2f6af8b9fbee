{"version": 3, "file": "gl-extensions.js", "names": ["GL_EXTENSIONS_CONSTANTS", "COMPRESSED_RGB_S3TC_DXT1_EXT", "COMPRESSED_RGBA_S3TC_DXT1_EXT", "COMPRESSED_RGBA_S3TC_DXT3_EXT", "COMPRESSED_RGBA_S3TC_DXT5_EXT", "COMPRESSED_R11_EAC", "COMPRESSED_SIGNED_R11_EAC", "COMPRESSED_RG11_EAC", "COMPRESSED_SIGNED_RG11_EAC", "COMPRESSED_RGB8_ETC2", "COMPRESSED_RGBA8_ETC2_EAC", "COMPRESSED_SRGB8_ETC2", "COMPRESSED_SRGB8_ALPHA8_ETC2_EAC", "COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2", "COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2", "COMPRESSED_RGB_PVRTC_4BPPV1_IMG", "COMPRESSED_RGBA_PVRTC_4BPPV1_IMG", "COMPRESSED_RGB_PVRTC_2BPPV1_IMG", "COMPRESSED_RGBA_PVRTC_2BPPV1_IMG", "COMPRESSED_RGB_ETC1_WEBGL", "COMPRESSED_RGB_ATC_WEBGL", "COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL", "COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL", "COMPRESSED_RGBA_ASTC_4X4_KHR", "COMPRESSED_RGBA_ASTC_5X4_KHR", "COMPRESSED_RGBA_ASTC_5X5_KHR", "COMPRESSED_RGBA_ASTC_6X5_KHR", "COMPRESSED_RGBA_ASTC_6X6_KHR", "COMPRESSED_RGBA_ASTC_8X5_KHR", "COMPRESSED_RGBA_ASTC_8X6_KHR", "COMPRESSED_RGBA_ASTC_8X8_KHR", "COMPRESSED_RGBA_ASTC_10X5_KHR", "COMPRESSED_RGBA_ASTC_10X6_KHR", "COMPRESSED_RGBA_ASTC_10X8_KHR", "COMPRESSED_RGBA_ASTC_10X10_KHR", "COMPRESSED_RGBA_ASTC_12X10_KHR", "COMPRESSED_RGBA_ASTC_12X12_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_4X4_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_5X4_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_5X5_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_6X5_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_6X6_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_8X5_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_8X6_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_8X8_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_10X5_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_10X6_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_10X8_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_10X10_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_12X10_KHR", "COMPRESSED_SRGB8_ALPHA8_ASTC_12X12_KHR", "COMPRESSED_RED_RGTC1_EXT", "COMPRESSED_SIGNED_RED_RGTC1_EXT", "COMPRESSED_RED_GREEN_RGTC2_EXT", "COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT", "COMPRESSED_SRGB_S3TC_DXT1_EXT", "COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT", "COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT", "COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT", "exports"], "sources": ["../../../src/lib/gl-extensions.ts"], "sourcesContent": ["/* eslint-disable camelcase */\nexport const GL_EXTENSIONS_CONSTANTS = {\n  // WEBGL_compressed_texture_s3tc\n\n  COMPRESSED_RGB_S3TC_DXT1_EXT: 0x83f0,\n  COMPRESSED_RGBA_S3TC_DXT1_EXT: 0x83f1,\n  COMPRESSED_RGBA_S3TC_DXT3_EXT: 0x83f2,\n  COMPRESSED_RGBA_S3TC_DXT5_EXT: 0x83f3,\n\n  // WEBGL_compressed_texture_es3\n\n  COMPRESSED_R11_EAC: 0x9270,\n  COMPRESSED_SIGNED_R11_EAC: 0x9271,\n  COMPRESSED_RG11_EAC: 0x9272,\n  COMPRESSED_SIGNED_RG11_EAC: 0x9273,\n  COMPRESSED_RGB8_ETC2: 0x9274,\n  COMPRESSED_RGBA8_ETC2_EAC: 0x9275,\n  COMPRESSED_SRGB8_ETC2: 0x9276,\n  COMPRESSED_SRGB8_ALPHA8_ETC2_EAC: 0x9277,\n  COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2: 0x9278,\n  COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2: 0x9279,\n\n  // WEBGL_compressed_texture_pvrtc\n\n  COMPRESSED_RGB_PVRTC_4BPPV1_IMG: 0x8c00,\n  COMPRESSED_RGBA_PVRTC_4BPPV1_IMG: 0x8c02,\n  COMPRESSED_RGB_PVRTC_2BPPV1_IMG: 0x8c01,\n  COMPRESSED_RGBA_PVRTC_2BPPV1_IMG: 0x8c03,\n\n  // WEBGL_compressed_texture_etc1\n\n  COMPRESSED_RGB_ETC1_WEBGL: 0x8d64,\n\n  // WEBGL_compressed_texture_atc\n\n  COMPRESSED_RGB_ATC_WEBGL: 0x8c92,\n  COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL: 0x8c93,\n  COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL: 0x87ee,\n\n  // WEBGL_compressed_texture_astc\n\n  COMPRESSED_RGBA_ASTC_4X4_KHR: 0x93b0,\n  COMPRESSED_RGBA_ASTC_5X4_KHR: 0x93b1,\n  COMPRESSED_RGBA_ASTC_5X5_KHR: 0x93b2,\n  COMPRESSED_RGBA_ASTC_6X5_KHR: 0x93b3,\n  COMPRESSED_RGBA_ASTC_6X6_KHR: 0x93b4,\n  COMPRESSED_RGBA_ASTC_8X5_KHR: 0x93b5,\n  COMPRESSED_RGBA_ASTC_8X6_KHR: 0x93b6,\n  COMPRESSED_RGBA_ASTC_8X8_KHR: 0x93b7,\n  COMPRESSED_RGBA_ASTC_10X5_KHR: 0x93b8,\n  COMPRESSED_RGBA_ASTC_10X6_KHR: 0x93b9,\n  COMPRESSED_RGBA_ASTC_10X8_KHR: 0x93ba,\n  COMPRESSED_RGBA_ASTC_10X10_KHR: 0x93bb,\n  COMPRESSED_RGBA_ASTC_12X10_KHR: 0x93bc,\n  COMPRESSED_RGBA_ASTC_12X12_KHR: 0x93bd,\n  COMPRESSED_SRGB8_ALPHA8_ASTC_4X4_KHR: 0x93d0,\n  COMPRESSED_SRGB8_ALPHA8_ASTC_5X4_KHR: 0x93d1,\n  COMPRESSED_SRGB8_ALPHA8_ASTC_5X5_KHR: 0x93d2,\n  COMPRESSED_SRGB8_ALPHA8_ASTC_6X5_KHR: 0x93d3,\n  COMPRESSED_SRGB8_ALPHA8_ASTC_6X6_KHR: 0x93d4,\n  COMPRESSED_SRGB8_ALPHA8_ASTC_8X5_KHR: 0x93d5,\n  COMPRESSED_SRGB8_ALPHA8_ASTC_8X6_KHR: 0x93d6,\n  COMPRESSED_SRGB8_ALPHA8_ASTC_8X8_KHR: 0x93d7,\n  COMPRESSED_SRGB8_ALPHA8_ASTC_10X5_KHR: 0x93d8,\n  COMPRESSED_SRGB8_ALPHA8_ASTC_10X6_KHR: 0x93d9,\n  COMPRESSED_SRGB8_ALPHA8_ASTC_10X8_KHR: 0x93da,\n  COMPRESSED_SRGB8_ALPHA8_ASTC_10X10_KHR: 0x93db,\n  COMPRESSED_SRGB8_ALPHA8_ASTC_12X10_KHR: 0x93dc,\n  COMPRESSED_SRGB8_ALPHA8_ASTC_12X12_KHR: 0x93dd,\n\n  // EXT_texture_compression_rgtc\n\n  COMPRESSED_RED_RGTC1_EXT: 0x8dbb,\n  COMPRESSED_SIGNED_RED_RGTC1_EXT: 0x8dbc,\n  COMPRESSED_RED_GREEN_RGTC2_EXT: 0x8dbd,\n  COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT: 0x8dbe,\n\n  // WEBGL_compressed_texture_s3tc_srgb\n\n  COMPRESSED_SRGB_S3TC_DXT1_EXT: 0x8c4c,\n  COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT: 0x8c4d,\n  COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT: 0x8c4e,\n  COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT: 0x8c4f\n};\n"], "mappings": ";;;;;;AACO,MAAMA,uBAAuB,GAAG;EAGrCC,4BAA4B,EAAE,MAAM;EACpCC,6BAA6B,EAAE,MAAM;EACrCC,6BAA6B,EAAE,MAAM;EACrCC,6BAA6B,EAAE,MAAM;EAIrCC,kBAAkB,EAAE,MAAM;EAC1BC,yBAAyB,EAAE,MAAM;EACjCC,mBAAmB,EAAE,MAAM;EAC3BC,0BAA0B,EAAE,MAAM;EAClCC,oBAAoB,EAAE,MAAM;EAC5BC,yBAAyB,EAAE,MAAM;EACjCC,qBAAqB,EAAE,MAAM;EAC7BC,gCAAgC,EAAE,MAAM;EACxCC,wCAAwC,EAAE,MAAM;EAChDC,yCAAyC,EAAE,MAAM;EAIjDC,+BAA+B,EAAE,MAAM;EACvCC,gCAAgC,EAAE,MAAM;EACxCC,+BAA+B,EAAE,MAAM;EACvCC,gCAAgC,EAAE,MAAM;EAIxCC,yBAAyB,EAAE,MAAM;EAIjCC,wBAAwB,EAAE,MAAM;EAChCC,wCAAwC,EAAE,MAAM;EAChDC,4CAA4C,EAAE,MAAM;EAIpDC,4BAA4B,EAAE,MAAM;EACpCC,4BAA4B,EAAE,MAAM;EACpCC,4BAA4B,EAAE,MAAM;EACpCC,4BAA4B,EAAE,MAAM;EACpCC,4BAA4B,EAAE,MAAM;EACpCC,4BAA4B,EAAE,MAAM;EACpCC,4BAA4B,EAAE,MAAM;EACpCC,4BAA4B,EAAE,MAAM;EACpCC,6BAA6B,EAAE,MAAM;EACrCC,6BAA6B,EAAE,MAAM;EACrCC,6BAA6B,EAAE,MAAM;EACrCC,8BAA8B,EAAE,MAAM;EACtCC,8BAA8B,EAAE,MAAM;EACtCC,8BAA8B,EAAE,MAAM;EACtCC,oCAAoC,EAAE,MAAM;EAC5CC,oCAAoC,EAAE,MAAM;EAC5CC,oCAAoC,EAAE,MAAM;EAC5CC,oCAAoC,EAAE,MAAM;EAC5CC,oCAAoC,EAAE,MAAM;EAC5CC,oCAAoC,EAAE,MAAM;EAC5CC,oCAAoC,EAAE,MAAM;EAC5CC,oCAAoC,EAAE,MAAM;EAC5CC,qCAAqC,EAAE,MAAM;EAC7CC,qCAAqC,EAAE,MAAM;EAC7CC,qCAAqC,EAAE,MAAM;EAC7CC,sCAAsC,EAAE,MAAM;EAC9CC,sCAAsC,EAAE,MAAM;EAC9CC,sCAAsC,EAAE,MAAM;EAI9CC,wBAAwB,EAAE,MAAM;EAChCC,+BAA+B,EAAE,MAAM;EACvCC,8BAA8B,EAAE,MAAM;EACtCC,qCAAqC,EAAE,MAAM;EAI7CC,6BAA6B,EAAE,MAAM;EACrCC,mCAAmC,EAAE,MAAM;EAC3CC,mCAAmC,EAAE,MAAM;EAC3CC,mCAAmC,EAAE;AACvC,CAAC;AAACC,OAAA,CAAA3D,uBAAA,GAAAA,uBAAA"}