"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.encodeImageURLToCompressedTextureURL = encodeImageURLToCompressedTextureURL;
var _workerUtils = require("@loaders.gl/worker-utils");
async function encodeImageURLToCompressedTextureURL(inputUrl, outputUrl, options) {
  const args = ['texture-compressor', '--type', 's3tc', '--compression', 'DXT1', '--quality', 'normal', '--input', inputUrl, '--output', outputUrl];
  const childProcess = new _workerUtils.ChildProcessProxy();
  await childProcess.start({
    command: 'npx',
    arguments: args,
    spawn: options
  });
  return outputUrl;
}
//# sourceMappingURL=encode-texture.js.map