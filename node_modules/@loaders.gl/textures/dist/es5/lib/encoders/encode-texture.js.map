{"version": 3, "file": "encode-texture.js", "names": ["_workerUtils", "require", "encodeImageURLToCompressedTextureURL", "inputUrl", "outputUrl", "options", "args", "childProcess", "ChildProcessProxy", "start", "command", "arguments", "spawn"], "sources": ["../../../../src/lib/encoders/encode-texture.ts"], "sourcesContent": ["import {ChildProcessProxy} from '@loaders.gl/worker-utils';\nimport {CompressedTextureWriterOptions} from '../../compressed-texture-writer';\n\n/*\n * @see https://github.com/TimvanScherpenzeel/texture-compressor\n */\nexport async function encodeImageURLToCompressedTextureURL(\n  inputUrl: string,\n  outputUrl: string,\n  options?: CompressedTextureWriterOptions\n): Promise<string> {\n  // prettier-ignore\n  const args = [\n    // Note: our actual executable is `npx`, so `texture-compressor` is an argument\n    'texture-compressor',\n    '--type', 's3tc',\n    '--compression', 'DXT1',\n    '--quality', 'normal',\n    '--input', inputUrl,\n    '--output', outputUrl\n  ];\n  const childProcess = new ChildProcessProxy();\n  await childProcess.start({\n    command: 'npx',\n    arguments: args,\n    spawn: options\n  });\n  return outputUrl;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAMO,eAAeC,oCAAoCA,CACxDC,QAAgB,EAChBC,SAAiB,EACjBC,OAAwC,EACvB;EAEjB,MAAMC,IAAI,GAAG,CAEX,oBAAoB,EACpB,QAAQ,EAAE,MAAM,EAChB,eAAe,EAAE,MAAM,EACvB,WAAW,EAAE,QAAQ,EACrB,SAAS,EAAEH,QAAQ,EACnB,UAAU,EAAEC,SAAS,CACtB;EACD,MAAMG,YAAY,GAAG,IAAIC,8BAAiB,CAAC,CAAC;EAC5C,MAAMD,YAAY,CAACE,KAAK,CAAC;IACvBC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAEL,IAAI;IACfM,KAAK,EAAEP;EACT,CAAC,CAAC;EACF,OAAOD,SAAS;AAClB"}