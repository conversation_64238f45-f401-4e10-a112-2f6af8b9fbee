{"version": 3, "file": "encode-ktx.js", "names": ["_ktxParse", "require", "encodeKTX", "texture", "ktx", "read"], "sources": ["../../../../src/lib/encoders/encode-ktx.ts"], "sourcesContent": ["import {read} from 'ktx-parse';\n\nexport function encodeKTX(texture) {\n  const ktx = read(texture);\n  // post process\n  return ktx;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,SAAA,GAAAC,OAAA;AAEO,SAASC,SAASA,CAACC,OAAO,EAAE;EACjC,MAAMC,GAAG,GAAG,IAAAC,cAAI,EAACF,OAAO,CAAC;EAEzB,OAAOC,GAAG;AACZ"}