"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "BasisLoader", {
  enumerable: true,
  get: function () {
    return _basisLoader.BasisLoader;
  }
});
Object.defineProperty(exports, "BasisWorkerLoader", {
  enumerable: true,
  get: function () {
    return _basisLoader.BasisWorkerLoader;
  }
});
Object.defineProperty(exports, "CompressedTextureLoader", {
  enumerable: true,
  get: function () {
    return _compressedTextureLoader.CompressedTextureLoader;
  }
});
Object.defineProperty(exports, "CompressedTextureWorkerLoader", {
  enumerable: true,
  get: function () {
    return _compressedTextureLoader.CompressedTextureWorkerLoader;
  }
});
Object.defineProperty(exports, "CompressedTextureWriter", {
  enumerable: true,
  get: function () {
    return _compressedTextureWriter.CompressedTextureWriter;
  }
});
Object.defineProperty(exports, "CrunchLoader", {
  enumerable: true,
  get: function () {
    return _crunchLoader.CrunchLoader;
  }
});
Object.defineProperty(exports, "CrunchWorkerLoader", {
  enumerable: true,
  get: function () {
    return _crunchLoader.CrunchLoader;
  }
});
Object.defineProperty(exports, "GL_EXTENSIONS_CONSTANTS", {
  enumerable: true,
  get: function () {
    return _glExtensions.GL_EXTENSIONS_CONSTANTS;
  }
});
Object.defineProperty(exports, "KTX2BasisWriter", {
  enumerable: true,
  get: function () {
    return _ktx2BasisWriter.KTX2BasisWriter;
  }
});
exports.KTX2BasisWriterWorker = void 0;
Object.defineProperty(exports, "NPYLoader", {
  enumerable: true,
  get: function () {
    return _npyLoader.NPYLoader;
  }
});
Object.defineProperty(exports, "NPYWorkerLoader", {
  enumerable: true,
  get: function () {
    return _npyLoader.NPYWorkerLoader;
  }
});
Object.defineProperty(exports, "getSupportedGPUTextureFormats", {
  enumerable: true,
  get: function () {
    return _textureFormats.getSupportedGPUTextureFormats;
  }
});
Object.defineProperty(exports, "loadImageTexture", {
  enumerable: true,
  get: function () {
    return _loadImage.loadImageTexture;
  }
});
Object.defineProperty(exports, "loadImageTextureArray", {
  enumerable: true,
  get: function () {
    return _loadImageArray.loadImageTextureArray;
  }
});
Object.defineProperty(exports, "loadImageTextureCube", {
  enumerable: true,
  get: function () {
    return _loadImageCube.loadImageTextureCube;
  }
});
Object.defineProperty(exports, "selectSupportedBasisFormat", {
  enumerable: true,
  get: function () {
    return _parseBasis.selectSupportedBasisFormat;
  }
});
var _loaderUtils = require("@loaders.gl/loader-utils");
var _version = require("./lib/utils/version");
var _basisLoader = require("./basis-loader");
var _compressedTextureLoader = require("./compressed-texture-loader");
var _crunchLoader = require("./crunch-loader");
var _npyLoader = require("./npy-loader");
var _compressedTextureWriter = require("./compressed-texture-writer");
var _ktx2BasisWriter = require("./ktx2-basis-writer");
var _loadImage = require("./lib/texture-api/load-image");
var _loadImageArray = require("./lib/texture-api/load-image-array");
var _loadImageCube = require("./lib/texture-api/load-image-cube");
var _glExtensions = require("./lib/gl-extensions");
var _parseBasis = require("./lib/parsers/parse-basis");
var _textureFormats = require("./lib/utils/texture-formats");
const KTX2BasisWriterWorker = {
  name: 'Basis Universal Supercompressed GPU Texture',
  id: _loaderUtils.isBrowser ? 'ktx2-basis-writer' : 'ktx2-basis-writer-nodejs',
  module: 'textures',
  version: _version.VERSION,
  extensions: ['ktx2'],
  worker: true,
  options: {
    useSRGB: false,
    qualityLevel: 10,
    encodeUASTC: false,
    mipmaps: false
  }
};
exports.KTX2BasisWriterWorker = KTX2BasisWriterWorker;
//# sourceMappingURL=index.js.map