"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports._TypecheckBasisWorkerLoader = exports._TypecheckBasisLoader = exports.BasisWorkerLoader = exports.BasisLoader = void 0;
var _workerUtils = require("@loaders.gl/worker-utils");
var _version = require("./lib/utils/version");
var _parseBasis = _interopRequireDefault(require("./lib/parsers/parse-basis"));
const BasisWorkerLoader = {
  name: 'Basis',
  id: _workerUtils.isBrowser ? 'basis' : 'basis-nodejs',
  module: 'textures',
  version: _version.VERSION,
  worker: true,
  extensions: ['basis', 'ktx2'],
  mimeTypes: ['application/octet-stream', 'image/ktx2'],
  tests: ['sB'],
  binary: true,
  options: {
    basis: {
      format: 'auto',
      libraryPath: 'libs/',
      containerFormat: 'auto',
      module: 'transcoder'
    }
  }
};
exports.BasisWorkerLoader = BasisWorkerLoader;
const BasisLoader = {
  ...BasisWorkerLoader,
  parse: _parseBasis.default
};
exports.BasisLoader = BasisLoader;
const _TypecheckBasisWorkerLoader = BasisWorkerLoader;
exports._TypecheckBasisWorkerLoader = _TypecheckBasisWorkerLoader;
const _TypecheckBasisLoader = BasisLoader;
exports._TypecheckBasisLoader = _TypecheckBasisLoader;
//# sourceMappingURL=basis-loader.js.map