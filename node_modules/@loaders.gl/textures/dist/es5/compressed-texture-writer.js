"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports._TypecheckCompressedTextureWriter = exports.CompressedTextureWriter = void 0;
var _version = require("./lib/utils/version");
var _encodeTexture = require("./lib/encoders/encode-texture");
const CompressedTextureWriter = {
  name: 'DDS Texture Container',
  id: 'dds',
  module: 'textures',
  version: _version.VERSION,
  extensions: ['dds'],
  options: {
    texture: {
      format: 'auto',
      compression: 'auto',
      quality: 'auto',
      mipmap: false,
      flipY: false,
      toolFlags: ''
    }
  },
  encodeURLtoURL: _encodeTexture.encodeImageURLToCompressedTextureURL
};
exports.CompressedTextureWriter = CompressedTextureWriter;
const _TypecheckCompressedTextureWriter = CompressedTextureWriter;
exports._TypecheckCompressedTextureWriter = _TypecheckCompressedTextureWriter;
//# sourceMappingURL=compressed-texture-writer.js.map