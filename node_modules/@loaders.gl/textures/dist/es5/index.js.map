{"version": 3, "file": "index.js", "names": ["_loaderUtils", "require", "_version", "_basisLoader", "_compressedTextureLoader", "_crunchLoader", "_n<PERSON><PERSON><PERSON><PERSON>", "_compressedTextureWriter", "_ktx2BasisWriter", "_loadImage", "_loadImageArray", "_loadImageCube", "_glExtensions", "_parseBasis", "_textureFormats", "KTX2BasisWriterWorker", "name", "id", "<PERSON><PERSON><PERSON><PERSON>", "module", "version", "VERSION", "extensions", "worker", "options", "useSRGB", "qualityLevel", "encodeUASTC", "mipmaps", "exports"], "sources": ["../../src/index.ts"], "sourcesContent": ["import {isBrowser} from '@loaders.gl/loader-utils';\nimport {VERSION} from './lib/utils/version';\n\n// Types\nexport type {GPUTextureFormat} from '@loaders.gl/schema';\nexport type {TextureLoaderOptions} from './compressed-texture-loader';\n\n// Loaders\nexport {<PERSON><PERSON><PERSON>oa<PERSON>, BasisWorkerLoader} from './basis-loader';\nexport {CompressedTextureLoader, CompressedTextureWorkerLoader} from './compressed-texture-loader';\nexport {CrunchLoader} from './crunch-loader';\nexport {NPYLoader, NPYWorkerLoader} from './npy-loader';\n\n// Writers\nexport {CompressedTextureWriter} from './compressed-texture-writer';\nexport {KTX2BasisWriter} from './ktx2-basis-writer';\n\nexport const KTX2BasisWriterWorker = {\n  name: 'Basis Universal Supercompressed GPU Texture',\n  id: isBrowser ? 'ktx2-basis-writer' : 'ktx2-basis-writer-nodejs',\n  module: 'textures',\n  version: VERSION,\n  extensions: ['ktx2'],\n  worker: true,\n  options: {\n    useSRGB: false,\n    qualityLevel: 10,\n    encodeUASTC: false,\n    mipmaps: false\n  }\n};\n\n// Texture Loading API\nexport {loadImageTexture} from './lib/texture-api/load-image';\nexport {loadImageTextureArray} from './lib/texture-api/load-image-array';\nexport {loadImageTextureCube} from './lib/texture-api/load-image-cube';\n\n// Utilities\nexport {GL_EXTENSIONS_CONSTANTS} from './lib/gl-extensions';\nexport {selectSupportedBasisFormat} from './lib/parsers/parse-basis';\nexport {getSupportedGPUTextureFormats} from './lib/utils/texture-formats';\n\n// DEPRECATED\nexport {CrunchLoader as CrunchWorkerLoader} from './crunch-loader';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AAOA,IAAAE,YAAA,GAAAF,OAAA;AACA,IAAAG,wBAAA,GAAAH,OAAA;AACA,IAAAI,aAAA,GAAAJ,OAAA;AACA,IAAAK,UAAA,GAAAL,OAAA;AAGA,IAAAM,wBAAA,GAAAN,OAAA;AACA,IAAAO,gBAAA,GAAAP,OAAA;AAkBA,IAAAQ,UAAA,GAAAR,OAAA;AACA,IAAAS,eAAA,GAAAT,OAAA;AACA,IAAAU,cAAA,GAAAV,OAAA;AAGA,IAAAW,aAAA,GAAAX,OAAA;AACA,IAAAY,WAAA,GAAAZ,OAAA;AACA,IAAAa,eAAA,GAAAb,OAAA;AAvBO,MAAMc,qBAAqB,GAAG;EACnCC,IAAI,EAAE,6CAA6C;EACnDC,EAAE,EAAEC,sBAAS,GAAG,mBAAmB,GAAG,0BAA0B;EAChEC,MAAM,EAAE,UAAU;EAClBC,OAAO,EAAEC,gBAAO;EAChBC,UAAU,EAAE,CAAC,MAAM,CAAC;EACpBC,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE;IACPC,OAAO,EAAE,KAAK;IACdC,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE,KAAK;IAClBC,OAAO,EAAE;EACX;AACF,CAAC;AAACC,OAAA,CAAAd,qBAAA,GAAAA,qBAAA"}