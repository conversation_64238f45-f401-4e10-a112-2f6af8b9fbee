{"version": 3, "file": "ktx2-basis-writer.js", "names": ["_version", "require", "_encodeKtx2BasisTexture", "KTX2BasisWriter", "name", "id", "module", "version", "VERSION", "extensions", "options", "useSRGB", "qualityLevel", "encodeUASTC", "mipmaps", "encode", "encodeKTX2BasisTexture", "exports", "_TypecheckKTX2TextureWriter"], "sources": ["../../src/ktx2-basis-writer.ts"], "sourcesContent": ["import type {Writer} from '@loaders.gl/loader-utils';\nimport {VERSION} from './lib/utils/version';\nimport {encodeKTX2BasisTexture} from './lib/encoders/encode-ktx2-basis-texture';\n\n/**\n *  Basis Universal Supercompressed GPU Texture.\n *  Spec - https://github.com/Esri/i3s-spec/blob/master/docs/1.8/textureSetDefinitionFormat.cmn.md\n */\nexport const KTX2BasisWriter = {\n  name: 'Basis Universal Supercompressed GPU Texture',\n  id: 'ktx2-basis-writer',\n  module: 'textures',\n  version: VERSION,\n\n  extensions: ['ktx2'],\n  options: {\n    useSRGB: false,\n    qualityLevel: 10,\n    encodeUASTC: false,\n    mipmaps: false\n  },\n\n  encode: encodeKTX2BasisTexture\n};\n\n// TYPE TESTS - TODO find a better way than exporting junk\nexport const _TypecheckKTX2TextureWriter: Writer = KTX2BasisWriter;\n"], "mappings": ";;;;;;AACA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAD,OAAA;AAMO,MAAME,eAAe,GAAG;EAC7BC,IAAI,EAAE,6CAA6C;EACnDC,EAAE,EAAE,mBAAmB;EACvBC,MAAM,EAAE,UAAU;EAClBC,OAAO,EAAEC,gBAAO;EAEhBC,UAAU,EAAE,CAAC,MAAM,CAAC;EACpBC,OAAO,EAAE;IACPC,OAAO,EAAE,KAAK;IACdC,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE,KAAK;IAClBC,OAAO,EAAE;EACX,CAAC;EAEDC,MAAM,EAAEC;AACV,CAAC;AAACC,OAAA,CAAAd,eAAA,GAAAA,eAAA;AAGK,MAAMe,2BAAmC,GAAGf,eAAe;AAACc,OAAA,CAAAC,2BAAA,GAAAA,2BAAA"}