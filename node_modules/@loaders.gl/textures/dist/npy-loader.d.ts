import type { Loader, LoaderWithParser, LoaderOptions } from '@loaders.gl/loader-utils';
import { parseNPY } from './lib/parsers/parse-npy';
/**
 * Worker loader for numpy "tiles"
 */
export declare const NPYWorkerLoader: {
    name: string;
    id: string;
    module: string;
    version: any;
    worker: boolean;
    extensions: string[];
    mimeTypes: never[];
    tests: ArrayBufferLike[];
    options: {
        npy: {};
    };
};
/**
 * Loader for numpy "tiles"
 */
export declare const NPYLoader: {
    parseSync: typeof parseNPY;
    parse: (arrayBuffer: ArrayBuffer, options?: LoaderOptions) => Promise<{
        data: Uint8Array | Uint32Array | Int32Array | Int8Array | Int16Array | Uint16Array | Float32Array | Float64Array;
        header: {
            descr: string;
            shape: number[];
        };
    } | null>;
    name: string;
    id: string;
    module: string;
    version: any;
    worker: boolean;
    extensions: string[];
    mimeTypes: never[];
    tests: ArrayBufferLike[];
    options: {
        npy: {};
    };
};
export declare const _TypecheckNPYWorkerLoader: Loader;
export declare const _TypecheckNPYLoader: LoaderWithParser;
//# sourceMappingURL=npy-loader.d.ts.map