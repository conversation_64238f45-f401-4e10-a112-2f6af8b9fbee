export declare const GL_EXTENSIONS_CONSTANTS: {
    COMPRESSED_RGB_S3TC_DXT1_EXT: number;
    COMPRESSED_RGBA_S3TC_DXT1_EXT: number;
    COMPRESSED_RGBA_S3TC_DXT3_EXT: number;
    COMPRESSED_RGBA_S3TC_DXT5_EXT: number;
    COMPRESSED_R11_EAC: number;
    COMPRESSED_SIGNED_R11_EAC: number;
    COMPRESSED_RG11_EAC: number;
    COMPRESSED_SIGNED_RG11_EAC: number;
    COMPRESSED_RGB8_ETC2: number;
    COMPRESSED_RGBA8_ETC2_EAC: number;
    COMPRESSED_SRGB8_ETC2: number;
    COMPRESSED_SRGB8_ALPHA8_ETC2_EAC: number;
    COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2: number;
    COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2: number;
    COMPRESSED_RGB_PVRTC_4BPPV1_IMG: number;
    COMPRESSED_RGBA_PVRTC_4BPPV1_IMG: number;
    COMPRESSED_RGB_PVRTC_2BPPV1_IMG: number;
    COMPRESSED_RGBA_PVRTC_2BPPV1_IMG: number;
    COMPRESSED_RGB_ETC1_WEBGL: number;
    COMPRESSED_RGB_ATC_WEBGL: number;
    COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL: number;
    COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL: number;
    COMPRESSED_RGBA_ASTC_4X4_KHR: number;
    COMPRESSED_RGBA_ASTC_5X4_KHR: number;
    COMPRESSED_RGBA_ASTC_5X5_KHR: number;
    COMPRESSED_RGBA_ASTC_6X5_KHR: number;
    COMPRESSED_RGBA_ASTC_6X6_KHR: number;
    COMPRESSED_RGBA_ASTC_8X5_KHR: number;
    COMPRESSED_RGBA_ASTC_8X6_KHR: number;
    COMPRESSED_RGBA_ASTC_8X8_KHR: number;
    COMPRESSED_RGBA_ASTC_10X5_KHR: number;
    COMPRESSED_RGBA_ASTC_10X6_KHR: number;
    COMPRESSED_RGBA_ASTC_10X8_KHR: number;
    COMPRESSED_RGBA_ASTC_10X10_KHR: number;
    COMPRESSED_RGBA_ASTC_12X10_KHR: number;
    COMPRESSED_RGBA_ASTC_12X12_KHR: number;
    COMPRESSED_SRGB8_ALPHA8_ASTC_4X4_KHR: number;
    COMPRESSED_SRGB8_ALPHA8_ASTC_5X4_KHR: number;
    COMPRESSED_SRGB8_ALPHA8_ASTC_5X5_KHR: number;
    COMPRESSED_SRGB8_ALPHA8_ASTC_6X5_KHR: number;
    COMPRESSED_SRGB8_ALPHA8_ASTC_6X6_KHR: number;
    COMPRESSED_SRGB8_ALPHA8_ASTC_8X5_KHR: number;
    COMPRESSED_SRGB8_ALPHA8_ASTC_8X6_KHR: number;
    COMPRESSED_SRGB8_ALPHA8_ASTC_8X8_KHR: number;
    COMPRESSED_SRGB8_ALPHA8_ASTC_10X5_KHR: number;
    COMPRESSED_SRGB8_ALPHA8_ASTC_10X6_KHR: number;
    COMPRESSED_SRGB8_ALPHA8_ASTC_10X8_KHR: number;
    COMPRESSED_SRGB8_ALPHA8_ASTC_10X10_KHR: number;
    COMPRESSED_SRGB8_ALPHA8_ASTC_12X10_KHR: number;
    COMPRESSED_SRGB8_ALPHA8_ASTC_12X12_KHR: number;
    COMPRESSED_RED_RGTC1_EXT: number;
    COMPRESSED_SIGNED_RED_RGTC1_EXT: number;
    COMPRESSED_RED_GREEN_RGTC2_EXT: number;
    COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT: number;
    COMPRESSED_SRGB_S3TC_DXT1_EXT: number;
    COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT: number;
    COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT: number;
    COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT: number;
};
//# sourceMappingURL=gl-extensions.d.ts.map