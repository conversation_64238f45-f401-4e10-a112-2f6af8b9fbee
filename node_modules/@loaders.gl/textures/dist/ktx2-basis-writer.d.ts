import type { Writer } from '@loaders.gl/loader-utils';
import { encodeKTX2BasisTexture } from './lib/encoders/encode-ktx2-basis-texture';
/**
 *  Basis Universal Supercompressed GPU Texture.
 *  Spec - https://github.com/Esri/i3s-spec/blob/master/docs/1.8/textureSetDefinitionFormat.cmn.md
 */
export declare const KTX2BasisWriter: {
    name: string;
    id: string;
    module: string;
    version: any;
    extensions: string[];
    options: {
        useSRGB: boolean;
        qualityLevel: number;
        encodeUASTC: boolean;
        mipmaps: boolean;
    };
    encode: typeof encodeKTX2BasisTexture;
};
export declare const _TypecheckKTX2TextureWriter: Writer;
//# sourceMappingURL=ktx2-basis-writer.d.ts.map