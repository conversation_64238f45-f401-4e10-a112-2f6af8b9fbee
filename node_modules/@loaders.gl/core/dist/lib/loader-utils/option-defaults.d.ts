import type { LoaderOptions } from '@loaders.gl/loader-utils';
export declare const DEFAULT_LOADER_OPTIONS: LoaderOptions;
export declare const REMOVED_LOADER_OPTIONS: {
    throws: string;
    dataType: string;
    uri: string;
    method: string;
    headers: string;
    body: string;
    mode: string;
    credentials: string;
    cache: string;
    redirect: string;
    referrer: string;
    referrerPolicy: string;
    integrity: string;
    keepalive: string;
    signal: string;
};
//# sourceMappingURL=option-defaults.d.ts.map