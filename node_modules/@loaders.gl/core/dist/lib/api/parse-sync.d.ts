import type { SyncDataType, Loader, LoaderContext, LoaderOptions } from '@loaders.gl/loader-utils';
/**
 * Parses `data` synchronously using a specified loader
 * @param data
 * @param loaders
 * @param options
 * @param context
 */
export declare function parseSync(data: SyncDataType, loaders?: Loader | Loader[] | LoaderOptions, options?: LoaderOptions, context?: LoaderContext): any;
//# sourceMappingURL=parse-sync.d.ts.map