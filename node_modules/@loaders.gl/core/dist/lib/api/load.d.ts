import type { DataType, Loader, LoaderContext, LoaderOptions } from '@loaders.gl/loader-utils';
/**
 * Parses `data` using a specified loader
 * Note: Load does duplicate a lot of parse.
 * it can also call fetchFile on string urls, which `parse` won't do.
 * @param data
 * @param loaders
 * @param options
 * @param context
 */
export declare function load(url: string | DataType, loaders?: Loader | Loader[] | LoaderOptions, options?: LoaderOptions, context?: LoaderContext): Promise<any>;
//# sourceMappingURL=load.d.ts.map