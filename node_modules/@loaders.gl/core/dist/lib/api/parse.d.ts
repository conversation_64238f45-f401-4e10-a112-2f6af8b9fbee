import type { DataType, Loader, LoaderContext, LoaderOptions } from '@loaders.gl/loader-utils';
/**
 * Parses `data` using a specified loader
 * @param data
 * @param loaders
 * @param options
 * @param context
 */
export declare function parse(data: DataType | Promise<DataType>, loaders?: Loader | Loader[] | LoaderOptions, options?: LoaderOptions, context?: LoaderContext): Promise<any>;
//# sourceMappingURL=parse.d.ts.map