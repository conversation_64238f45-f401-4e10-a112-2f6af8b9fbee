{"version": 3, "file": "null-worker.js", "names": ["_loaderUtils", "require", "_nullLoader", "createLoaderWorker", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["../../../src/workers/null-worker.ts"], "sourcesContent": ["import {createLoaderWorker} from '@loaders.gl/loader-utils';\nimport {NullLoader} from '../null-loader';\n\ncreateLoaderWorker(NullLoader);\n"], "mappings": ";;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AAEA,IAAAE,+BAAkB,EAACC,sBAAU,CAAC"}