{"version": 3, "file": "is-type.js", "names": ["isBoolean", "x", "isFunction", "isObject", "exports", "isPureObject", "constructor", "isPromise", "then", "isIterable", "Symbol", "iterator", "isAsyncIterable", "asyncIterator", "isIterator", "next", "isResponse", "Response", "arrayBuffer", "text", "json", "isFile", "File", "isBlob", "Blob", "<PERSON><PERSON><PERSON><PERSON>", "isWritableDOMStream", "abort", "getWriter", "isReadableDOMStream", "ReadableStream", "tee", "cancel", "<PERSON><PERSON><PERSON><PERSON>", "isWritableNodeStream", "end", "write", "writable", "isReadableNodeStream", "read", "pipe", "readable", "isReadableStream", "isWritableStream"], "sources": ["../../../src/javascript-utils/is-type.ts"], "sourcesContent": ["import type {Readable} from 'stream';\n\n/** A DOM or Node readable stream */\nexport type ReadableStreamType = ReadableStream | Readable;\n\nconst isBoolean: (x: any) => boolean = (x) => typeof x === 'boolean';\nconst isFunction: (x: any) => boolean = (x) => typeof x === 'function';\n\nexport const isObject: (x: any) => boolean = (x) => x !== null && typeof x === 'object';\nexport const isPureObject: (x: any) => boolean = (x) =>\n  isObject(x) && x.constructor === {}.constructor;\nexport const isPromise: (x: any) => boolean = (x) => isObject(x) && isFunction(x.then);\n\nexport const isIterable: (x: any) => boolean = (x) => x && typeof x[Symbol.iterator] === 'function';\nexport const isAsyncIterable: (x: any) => boolean = (x) =>\n  x && typeof x[Symbol.asyncIterator] === 'function';\nexport const isIterator: (x: any) => boolean = (x) => x && isFunction(x.next);\n\nexport const isResponse: (x: any) => boolean = (x) =>\n  (typeof Response !== 'undefined' && x instanceof Response) ||\n  (x && x.arrayBuffer && x.text && x.json);\n\nexport const isFile: (x: any) => boolean = (x) => typeof File !== 'undefined' && x instanceof File;\nexport const isBlob: (x: any) => boolean = (x) => typeof Blob !== 'undefined' && x instanceof Blob;\n\n/** Check for Node.js `Buffer` without triggering bundler to include buffer polyfill */\nexport const isBuffer: (x: any) => boolean = (x) => x && typeof x === 'object' && x.isBuffer;\n\nexport const isWritableDOMStream: (x: any) => boolean = (x) =>\n  isObject(x) && isFunction(x.abort) && isFunction(x.getWriter);\n\nexport const isReadableDOMStream: (x: any) => boolean = (x) =>\n  (typeof ReadableStream !== 'undefined' && x instanceof ReadableStream) ||\n  (isObject(x) && isFunction(x.tee) && isFunction(x.cancel) && isFunction(x.getReader));\n// Not implemented in Firefox: && isFunction(x.pipeTo)\n\nexport const isWritableNodeStream: (x: any) => boolean = (x) =>\n  isObject(x) && isFunction(x.end) && isFunction(x.write) && isBoolean(x.writable);\nexport const isReadableNodeStream: (x: any) => boolean = (x) =>\n  isObject(x) && isFunction(x.read) && isFunction(x.pipe) && isBoolean(x.readable);\nexport const isReadableStream: (x: any) => boolean = (x) =>\n  isReadableDOMStream(x) || isReadableNodeStream(x);\nexport const isWritableStream: (x: any) => boolean = (x) =>\n  isWritableDOMStream(x) || isWritableNodeStream(x);\n"], "mappings": ";;;;;;AAKA,MAAMA,SAA8B,GAAIC,CAAC,IAAK,OAAOA,CAAC,KAAK,SAAS;AACpE,MAAMC,UAA+B,GAAID,CAAC,IAAK,OAAOA,CAAC,KAAK,UAAU;AAE/D,MAAME,QAA6B,GAAIF,CAAC,IAAKA,CAAC,KAAK,IAAI,IAAI,OAAOA,CAAC,KAAK,QAAQ;AAACG,OAAA,CAAAD,QAAA,GAAAA,QAAA;AACjF,MAAME,YAAiC,GAAIJ,CAAC,IACjDE,QAAQ,CAACF,CAAC,CAAC,IAAIA,CAAC,CAACK,WAAW,KAAK,CAAC,CAAC,CAACA,WAAW;AAACF,OAAA,CAAAC,YAAA,GAAAA,YAAA;AAC3C,MAAME,SAA8B,GAAIN,CAAC,IAAKE,QAAQ,CAACF,CAAC,CAAC,IAAIC,UAAU,CAACD,CAAC,CAACO,IAAI,CAAC;AAACJ,OAAA,CAAAG,SAAA,GAAAA,SAAA;AAEhF,MAAME,UAA+B,GAAIR,CAAC,IAAKA,CAAC,IAAI,OAAOA,CAAC,CAACS,MAAM,CAACC,QAAQ,CAAC,KAAK,UAAU;AAACP,OAAA,CAAAK,UAAA,GAAAA,UAAA;AAC7F,MAAMG,eAAoC,GAAIX,CAAC,IACpDA,CAAC,IAAI,OAAOA,CAAC,CAACS,MAAM,CAACG,aAAa,CAAC,KAAK,UAAU;AAACT,OAAA,CAAAQ,eAAA,GAAAA,eAAA;AAC9C,MAAME,UAA+B,GAAIb,CAAC,IAAKA,CAAC,IAAIC,UAAU,CAACD,CAAC,CAACc,IAAI,CAAC;AAACX,OAAA,CAAAU,UAAA,GAAAA,UAAA;AAEvE,MAAME,UAA+B,GAAIf,CAAC,IAC9C,OAAOgB,QAAQ,KAAK,WAAW,IAAIhB,CAAC,YAAYgB,QAAQ,IACxDhB,CAAC,IAAIA,CAAC,CAACiB,WAAW,IAAIjB,CAAC,CAACkB,IAAI,IAAIlB,CAAC,CAACmB,IAAK;AAAChB,OAAA,CAAAY,UAAA,GAAAA,UAAA;AAEpC,MAAMK,MAA2B,GAAIpB,CAAC,IAAK,OAAOqB,IAAI,KAAK,WAAW,IAAIrB,CAAC,YAAYqB,IAAI;AAAClB,OAAA,CAAAiB,MAAA,GAAAA,MAAA;AAC5F,MAAME,MAA2B,GAAItB,CAAC,IAAK,OAAOuB,IAAI,KAAK,WAAW,IAAIvB,CAAC,YAAYuB,IAAI;AAACpB,OAAA,CAAAmB,MAAA,GAAAA,MAAA;AAG5F,MAAME,QAA6B,GAAIxB,CAAC,IAAKA,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAACwB,QAAQ;AAACrB,OAAA,CAAAqB,QAAA,GAAAA,QAAA;AAEtF,MAAMC,mBAAwC,GAAIzB,CAAC,IACxDE,QAAQ,CAACF,CAAC,CAAC,IAAIC,UAAU,CAACD,CAAC,CAAC0B,KAAK,CAAC,IAAIzB,UAAU,CAACD,CAAC,CAAC2B,SAAS,CAAC;AAACxB,OAAA,CAAAsB,mBAAA,GAAAA,mBAAA;AAEzD,MAAMG,mBAAwC,GAAI5B,CAAC,IACvD,OAAO6B,cAAc,KAAK,WAAW,IAAI7B,CAAC,YAAY6B,cAAc,IACpE3B,QAAQ,CAACF,CAAC,CAAC,IAAIC,UAAU,CAACD,CAAC,CAAC8B,GAAG,CAAC,IAAI7B,UAAU,CAACD,CAAC,CAAC+B,MAAM,CAAC,IAAI9B,UAAU,CAACD,CAAC,CAACgC,SAAS,CAAE;AAAC7B,OAAA,CAAAyB,mBAAA,GAAAA,mBAAA;AAGjF,MAAMK,oBAAyC,GAAIjC,CAAC,IACzDE,QAAQ,CAACF,CAAC,CAAC,IAAIC,UAAU,CAACD,CAAC,CAACkC,GAAG,CAAC,IAAIjC,UAAU,CAACD,CAAC,CAACmC,KAAK,CAAC,IAAIpC,SAAS,CAACC,CAAC,CAACoC,QAAQ,CAAC;AAACjC,OAAA,CAAA8B,oBAAA,GAAAA,oBAAA;AAC5E,MAAMI,oBAAyC,GAAIrC,CAAC,IACzDE,QAAQ,CAACF,CAAC,CAAC,IAAIC,UAAU,CAACD,CAAC,CAACsC,IAAI,CAAC,IAAIrC,UAAU,CAACD,CAAC,CAACuC,IAAI,CAAC,IAAIxC,SAAS,CAACC,CAAC,CAACwC,QAAQ,CAAC;AAACrC,OAAA,CAAAkC,oBAAA,GAAAA,oBAAA;AAC5E,MAAMI,gBAAqC,GAAIzC,CAAC,IACrD4B,mBAAmB,CAAC5B,CAAC,CAAC,IAAIqC,oBAAoB,CAACrC,CAAC,CAAC;AAACG,OAAA,CAAAsC,gBAAA,GAAAA,gBAAA;AAC7C,MAAMC,gBAAqC,GAAI1C,CAAC,IACrDyB,mBAAmB,CAACzB,CAAC,CAAC,IAAIiC,oBAAoB,CAACjC,CAAC,CAAC;AAACG,OAAA,CAAAuC,gBAAA,GAAAA,gBAAA"}