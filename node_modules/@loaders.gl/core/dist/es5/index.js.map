{"version": 3, "file": "index.js", "names": ["_fetchFile", "require", "_readA<PERSON><PERSON><PERSON><PERSON><PERSON>", "_readFile", "_writeFile", "_loaderOptions", "_registerLoaders", "_select<PERSON><PERSON><PERSON>", "_parse", "_parseSync", "_parseInBatches", "_load", "_loadInBatches", "_encode", "_save", "_loaderUtils", "_makeIterator", "_makeNodeStream", "_nullLoader", "_fetchProgress", "_interopRequireDefault", "_browserFilesystem", "_isType"], "sources": ["../../src/index.ts"], "sourcesContent": ["// loaders.gl, MIT license\n\n// TYPES\nexport type {\n  Loader,\n  LoaderWithParser,\n  LoaderContext,\n  LoaderOptions,\n  Writer,\n  WriterOptions,\n  DataType,\n  SyncDataType,\n  BatchableDataType,\n  IFileSystem,\n  IRandomAccessReadFileSystem\n} from '@loaders.gl/loader-utils';\n\n// FILE READING AND WRITING\nexport {fetchFile} from './lib/fetch/fetch-file';\n\nexport {readArrayBuffer} from './lib/fetch/read-array-buffer';\nexport {readFileSync} from './lib/fetch/read-file';\nexport {writeFile, writeFileSync} from './lib/fetch/write-file';\n\n// CONFIGURATION\nexport {setLoaderOptions, getLoaderOptions} from './lib/api/loader-options';\nexport {registerLoaders} from './lib/api/register-loaders';\nexport {selectLoader, selectLoaderSync} from './lib/api/select-loader';\n\n// LOADING (READING + PARSING)\nexport {parse} from './lib/api/parse';\nexport {parseSync} from './lib/api/parse-sync';\nexport {parseInBatches} from './lib/api/parse-in-batches';\n\nexport {load} from './lib/api/load';\nexport {loadInBatches} from './lib/api/load-in-batches';\n\n// ENCODING (ENCODING AND WRITING)\nexport {encode, encodeSync, encodeInBatches, encodeText, encodeURLtoURL} from './lib/api/encode';\nexport {save, saveSync} from './lib/api/save';\n\n// CORE UTILS SHARED WITH LOADERS (RE-EXPORTED FROM LOADER-UTILS)\nexport {setPathPrefix, getPathPrefix, resolvePath} from '@loaders.gl/loader-utils';\nexport {RequestScheduler} from '@loaders.gl/loader-utils';\n\n// ITERATOR UTILS\nexport {makeIterator} from './iterators/make-iterator/make-iterator';\nexport {makeStream} from './iterators/make-stream/make-node-stream';\n\n// CORE LOADERS\nexport {NullWorkerLoader, NullLoader} from './null-loader';\nexport {JSONLoader} from '@loaders.gl/loader-utils';\n\n// EXPERIMENTAL\nexport {default as _fetchProgress} from './lib/progress/fetch-progress';\nexport {default as _BrowserFileSystem} from './lib/filesystems/browser-filesystem';\n\n// FOR TESTING\nexport {_unregisterLoaders} from './lib/api/register-loaders';\n\n//\n// TODO - MOVE TO LOADER-UTILS AND DEPRECATE IN CORE?\n//\n\nexport {isBrowser, isWorker, self, window, global, document} from '@loaders.gl/loader-utils';\nexport {assert} from '@loaders.gl/loader-utils';\nexport {forEach, concatenateArrayBuffersAsync} from '@loaders.gl/loader-utils';\n\nexport {\n  makeTextDecoderIterator,\n  makeTextEncoderIterator,\n  makeLineIterator,\n  makeNumberedLineIterator\n} from '@loaders.gl/loader-utils';\n\n// \"JAVASCRIPT\" UTILS - move to loader-utils?\nexport {\n  isPureObject,\n  isPromise,\n  isIterable,\n  isAsyncIterable,\n  isIterator,\n  isResponse,\n  isReadableStream,\n  isWritableStream\n} from './javascript-utils/is-type';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBA,IAAAA,UAAA,GAAAC,OAAA;AAEA,IAAAC,gBAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AAGA,IAAAI,cAAA,GAAAJ,OAAA;AACA,IAAAK,gBAAA,GAAAL,OAAA;AACA,IAAAM,aAAA,GAAAN,OAAA;AAGA,IAAAO,MAAA,GAAAP,OAAA;AACA,IAAAQ,UAAA,GAAAR,OAAA;AACA,IAAAS,eAAA,GAAAT,OAAA;AAEA,IAAAU,KAAA,GAAAV,OAAA;AACA,IAAAW,cAAA,GAAAX,OAAA;AAGA,IAAAY,OAAA,GAAAZ,OAAA;AACA,IAAAa,KAAA,GAAAb,OAAA;AAGA,IAAAc,YAAA,GAAAd,OAAA;AAIA,IAAAe,aAAA,GAAAf,OAAA;AACA,IAAAgB,eAAA,GAAAhB,OAAA;AAGA,IAAAiB,WAAA,GAAAjB,OAAA;AAIA,IAAAkB,cAAA,GAAAC,sBAAA,CAAAnB,OAAA;AACA,IAAAoB,kBAAA,GAAAD,sBAAA,CAAAnB,OAAA;AAqBA,IAAAqB,OAAA,GAAArB,OAAA"}