"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.fetchFile = fetchFile;
var _loaderUtils = require("@loaders.gl/loader-utils");
var _responseUtils = require("../utils/response-utils");
async function fetchFile(url, options) {
  if (typeof url === 'string') {
    url = (0, _loaderUtils.resolvePath)(url);
    let fetchOptions = options;
    if (options !== null && options !== void 0 && options.fetch && typeof (options === null || options === void 0 ? void 0 : options.fetch) !== 'function') {
      fetchOptions = options.fetch;
    }
    return await fetch(url, fetchOptions);
  }
  return await (0, _responseUtils.makeResponse)(url);
}
//# sourceMappingURL=fetch-file.js.map