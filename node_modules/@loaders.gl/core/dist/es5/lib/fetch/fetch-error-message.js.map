{"version": 3, "file": "fetch-error-message.js", "names": ["getErrorMessageFromResponseSync", "response", "concat", "url", "status", "statusText", "getErrorMessageFromResponse", "message", "contentType", "headers", "get", "includes", "text", "error"], "sources": ["../../../../src/lib/fetch/fetch-error-message.ts"], "sourcesContent": ["export function getErrorMessageFromResponseSync(response: Response): string {\n  return `Failed to fetch resource ${response.url}(${response.status}): ${response.statusText} `;\n}\n\nexport async function getErrorMessageFromResponse(response: Response): Promise<string> {\n  let message = `Failed to fetch resource ${response.url} (${response.status}): `;\n  try {\n    const contentType = response.headers.get('Content-Type') || '';\n    if (contentType.includes('application/json')) {\n      message += await response.text();\n    } else {\n      message += response.statusText;\n    }\n  } catch (error) {\n    // eslint forbids return in finally statement\n    return message;\n  }\n  return message;\n}\n"], "mappings": ";;;;;;;AAAO,SAASA,+BAA+BA,CAACC,QAAkB,EAAU;EAC1E,mCAAAC,MAAA,CAAmCD,QAAQ,CAACE,GAAG,OAAAD,MAAA,CAAID,QAAQ,CAACG,MAAM,SAAAF,MAAA,CAAMD,QAAQ,CAACI,UAAU;AAC7F;AAEO,eAAeC,2BAA2BA,CAACL,QAAkB,EAAmB;EACrF,IAAIM,OAAO,+BAAAL,MAAA,CAA+BD,QAAQ,CAACE,GAAG,QAAAD,MAAA,CAAKD,QAAQ,CAACG,MAAM,QAAK;EAC/E,IAAI;IACF,MAAMI,WAAW,GAAGP,QAAQ,CAACQ,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE;IAC9D,IAAIF,WAAW,CAACG,QAAQ,CAAC,kBAAkB,CAAC,EAAE;MAC5CJ,OAAO,IAAI,MAAMN,QAAQ,CAACW,IAAI,CAAC,CAAC;IAClC,CAAC,MAAM;MACLL,OAAO,IAAIN,QAAQ,CAACI,UAAU;IAChC;EACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;IAEd,OAAON,OAAO;EAChB;EACA,OAAOA,OAAO;AAChB"}