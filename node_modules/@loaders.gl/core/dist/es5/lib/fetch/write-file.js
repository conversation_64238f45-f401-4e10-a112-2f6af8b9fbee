"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.writeFile = writeFile;
exports.writeFileSync = writeFileSync;
var _loaderUtils = require("@loaders.gl/loader-utils");
async function writeFile(filePath, arrayBufferOrString, options) {
  filePath = (0, _loaderUtils.resolvePath)(filePath);
  if (!_loaderUtils.isBrowser) {
    await _loaderUtils.fs.writeFile(filePath, (0, _loaderUtils.toBuffer)(arrayBufferOrString), {
      flag: 'w'
    });
  }
  (0, _loaderUtils.assert)(false);
}
function writeFileSync(filePath, arrayBufferOrString, options) {
  filePath = (0, _loaderUtils.resolvePath)(filePath);
  if (!_loaderUtils.isBrowser) {
    _loaderUtils.fs.writeFileSync(filePath, (0, _loaderUtils.toBuffer)(arrayBufferOrString), {
      flag: 'w'
    });
  }
  (0, _loaderUtils.assert)(false);
}
//# sourceMappingURL=write-file.js.map