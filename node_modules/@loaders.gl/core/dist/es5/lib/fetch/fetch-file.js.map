{"version": 3, "file": "fetch-file.js", "names": ["_loaderUtils", "require", "_responseUtils", "fetchFile", "url", "options", "<PERSON><PERSON><PERSON>", "fetchOptions", "fetch", "makeResponse"], "sources": ["../../../../src/lib/fetch/fetch-file.ts"], "sourcesContent": ["import {resolvePath} from '@loaders.gl/loader-utils';\nimport {makeResponse} from '../utils/response-utils';\n// import {getErrorMessageFromResponse} from './fetch-error-message';\n\n/**\n * fetch compatible function\n * Reads file data from:\n * - http/http urls\n * - data urls\n * - File/Blob objects\n * Leverages `@loaders.gl/polyfills` for Node.js support\n * Respects pathPrefix and file aliases\n */\nexport async function fetchFile(\n  url: string | Blob,\n  options?: RequestInit & {fetch?: RequestInit | Function}\n): Promise<Response> {\n  if (typeof url === 'string') {\n    url = resolvePath(url);\n\n    let fetchOptions: RequestInit = options as RequestInit;\n    if (options?.fetch && typeof options?.fetch !== 'function') {\n      fetchOptions = options.fetch;\n    }\n\n    return await fetch(url, fetchOptions);\n  }\n\n  return await makeResponse(url);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AAYO,eAAeE,SAASA,CAC7BC,GAAkB,EAClBC,OAAwD,EACrC;EACnB,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;IAC3BA,GAAG,GAAG,IAAAE,wBAAW,EAACF,GAAG,CAAC;IAEtB,IAAIG,YAAyB,GAAGF,OAAsB;IACtD,IAAIA,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEG,KAAK,IAAI,QAAOH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,KAAK,MAAK,UAAU,EAAE;MAC1DD,YAAY,GAAGF,OAAO,CAACG,KAAK;IAC9B;IAEA,OAAO,MAAMA,KAAK,CAACJ,GAAG,EAAEG,YAAY,CAAC;EACvC;EAEA,OAAO,MAAM,IAAAE,2BAAY,EAACL,GAAG,CAAC;AAChC"}