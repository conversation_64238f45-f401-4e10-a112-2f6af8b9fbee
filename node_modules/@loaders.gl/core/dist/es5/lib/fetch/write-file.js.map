{"version": 3, "file": "write-file.js", "names": ["_loaderUtils", "require", "writeFile", "filePath", "arrayBufferOrString", "options", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "fs", "<PERSON><PERSON><PERSON><PERSON>", "flag", "assert", "writeFileSync"], "sources": ["../../../../src/lib/fetch/write-file.ts"], "sourcesContent": ["// file write\nimport {isBrowser, assert, resolvePath} from '@loaders.gl/loader-utils';\nimport {fs, toBuffer} from '@loaders.gl/loader-utils';\n\nexport async function writeFile(\n  filePath: string,\n  arrayBufferOrString: ArrayBuffer | string,\n  options?\n): Promise<void> {\n  filePath = resolvePath(filePath);\n  if (!isBrowser) {\n    await fs.writeFile(filePath, toBuffer(arrayBufferOrString), {flag: 'w'});\n  }\n  assert(false);\n}\n\nexport function writeFileSync(\n  filePath: string,\n  arrayBufferOrString: ArrayBuffer | string,\n  options?\n): void {\n  filePath = resolvePath(filePath);\n  if (!isBrowser) {\n    fs.writeFileSync(filePath, toBuffer(arrayBufferOrString), {flag: 'w'});\n  }\n  assert(false);\n}\n"], "mappings": ";;;;;;;AACA,IAAAA,YAAA,GAAAC,OAAA;AAGO,eAAeC,SAASA,CAC7BC,QAAgB,EAChBC,mBAAyC,EACzCC,OAAQ,EACO;EACfF,QAAQ,GAAG,IAAAG,wBAAW,EAACH,QAAQ,CAAC;EAChC,IAAI,CAACI,sBAAS,EAAE;IACd,MAAMC,eAAE,CAACN,SAAS,CAACC,QAAQ,EAAE,IAAAM,qBAAQ,EAACL,mBAAmB,CAAC,EAAE;MAACM,IAAI,EAAE;IAAG,CAAC,CAAC;EAC1E;EACA,IAAAC,mBAAM,EAAC,KAAK,CAAC;AACf;AAEO,SAASC,aAAaA,CAC3BT,QAAgB,EAChBC,mBAAyC,EACzCC,OAAQ,EACF;EACNF,QAAQ,GAAG,IAAAG,wBAAW,EAACH,QAAQ,CAAC;EAChC,IAAI,CAACI,sBAAS,EAAE;IACdC,eAAE,CAACI,aAAa,CAACT,QAAQ,EAAE,IAAAM,qBAAQ,EAACL,mBAAmB,CAAC,EAAE;MAACM,IAAI,EAAE;IAAG,CAAC,CAAC;EACxE;EACA,IAAAC,mBAAM,EAAC,KAAK,CAAC;AACf"}