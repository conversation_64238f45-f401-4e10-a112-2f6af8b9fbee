{"version": 3, "file": "read-array-buffer.js", "names": ["_loaderUtils", "require", "read<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "file", "start", "length", "fs", "_readToArrayBuffer", "Blob", "slice", "readBlob", "blob", "Promise", "resolve", "reject", "fileReader", "FileReader", "onload", "event", "_event$target", "target", "result", "onerror", "error", "readAsA<PERSON>y<PERSON><PERSON>er"], "sources": ["../../../../src/lib/fetch/read-array-buffer.ts"], "sourcesContent": ["//\nimport {fs} from '@loaders.gl/loader-utils';\n\n/**\n * Reads a chunk from a random access file\n * @param file\n * @param start\n * @param length\n * @returns\n */\nexport async function readArrayBuffer(\n  file: Blob | ArrayBuffer | string | number,\n  start: number,\n  length: number\n): Promise<ArrayBuffer> {\n  if (typeof file === 'number') {\n    return await fs._readToArrayBuffer(file, start, length);\n  }\n  // TODO - we can do better for ArrayBuffer and string\n  if (!(file instanceof Blob)) {\n    file = new Blob([file]);\n  }\n  const slice = file.slice(start, start + length);\n  return await readBlob(slice);\n}\n\n/**\n * Read a slice of a Blob or File, without loading the entire file into memory\n * The trick when reading File objects is to read successive \"slices\" of the File\n * Per spec https://w3c.github.io/FileAPI/, slicing a File only updates the start and end fields\n * Actually reading from file happens in `readAsArrayBuffer`\n * @param blob to read\n */\nexport async function readBlob(blob: Blob): Promise<ArrayBuffer> {\n  return await new Promise((resolve, reject) => {\n    const fileReader = new FileReader();\n    fileReader.onload = (event: ProgressEvent<FileReader>) =>\n      resolve(event?.target?.result as ArrayBuffer);\n    // TODO - reject with a proper Error\n    fileReader.onerror = (error: ProgressEvent<FileReader>) => reject(error);\n    fileReader.readAsArrayBuffer(blob);\n  });\n}\n"], "mappings": ";;;;;;;AACA,IAAAA,YAAA,GAAAC,OAAA;AASO,eAAeC,eAAeA,CACnCC,IAA0C,EAC1CC,KAAa,EACbC,MAAc,EACQ;EACtB,IAAI,OAAOF,IAAI,KAAK,QAAQ,EAAE;IAC5B,OAAO,MAAMG,eAAE,CAACC,kBAAkB,CAACJ,IAAI,EAAEC,KAAK,EAAEC,MAAM,CAAC;EACzD;EAEA,IAAI,EAAEF,IAAI,YAAYK,IAAI,CAAC,EAAE;IAC3BL,IAAI,GAAG,IAAIK,IAAI,CAAC,CAACL,IAAI,CAAC,CAAC;EACzB;EACA,MAAMM,KAAK,GAAGN,IAAI,CAACM,KAAK,CAACL,KAAK,EAAEA,KAAK,GAAGC,MAAM,CAAC;EAC/C,OAAO,MAAMK,QAAQ,CAACD,KAAK,CAAC;AAC9B;AASO,eAAeC,QAAQA,CAACC,IAAU,EAAwB;EAC/D,OAAO,MAAM,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IAC5C,MAAMC,UAAU,GAAG,IAAIC,UAAU,CAAC,CAAC;IACnCD,UAAU,CAACE,MAAM,GAAIC,KAAgC;MAAA,IAAAC,aAAA;MAAA,OACnDN,OAAO,CAACK,KAAK,aAALA,KAAK,wBAAAC,aAAA,GAALD,KAAK,CAAEE,MAAM,cAAAD,aAAA,uBAAbA,aAAA,CAAeE,MAAqB,CAAC;IAAA;IAE/CN,UAAU,CAACO,OAAO,GAAIC,KAAgC,IAAKT,MAAM,CAACS,KAAK,CAAC;IACxER,UAAU,CAACS,iBAAiB,CAACb,IAAI,CAAC;EACpC,CAAC,CAAC;AACJ"}