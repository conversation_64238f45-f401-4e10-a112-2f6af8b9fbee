"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getLoaderContext = getLoaderContext;
exports.getLoadersFromContext = getLoadersFromContext;
var _getFetchFunction = require("./get-fetch-function");
var _urlUtils = require("../utils/url-utils");
var _loaderUtils = require("@loaders.gl/loader-utils");
function getLoaderContext(context, options, parentContext) {
  if (parentContext) {
    return parentContext;
  }
  const newContext = {
    fetch: (0, _getFetchFunction.getFetchFunction)(options, context),
    ...context
  };
  if (newContext.url) {
    const baseUrl = (0, _urlUtils.stripQueryString)(newContext.url);
    newContext.baseUrl = baseUrl;
    newContext.queryString = (0, _urlUtils.extractQueryString)(newContext.url);
    newContext.filename = _loaderUtils.path.filename(baseUrl);
    newContext.baseUrl = _loaderUtils.path.dirname(baseUrl);
  }
  if (!Array.isArray(newContext.loaders)) {
    newContext.loaders = null;
  }
  return newContext;
}
function getLoadersFromContext(loaders, context) {
  if (!context && loaders && !Array.isArray(loaders)) {
    return loaders;
  }
  let candidateLoaders;
  if (loaders) {
    candidateLoaders = Array.isArray(loaders) ? loaders : [loaders];
  }
  if (context && context.loaders) {
    const contextLoaders = Array.isArray(context.loaders) ? context.loaders : [context.loaders];
    candidateLoaders = candidateLoaders ? [...candidateLoaders, ...contextLoaders] : contextLoaders;
  }
  return candidateLoaders && candidateLoaders.length ? candidateLoaders : null;
}
//# sourceMappingURL=loader-context.js.map