{"version": 3, "file": "loader-context.js", "names": ["_getFetchFunction", "require", "_urlUtils", "_loaderUtils", "getLoaderContext", "context", "options", "parentContext", "newContext", "fetch", "getFetchFunction", "url", "baseUrl", "stripQueryString", "queryString", "extractQueryString", "filename", "path", "dirname", "Array", "isArray", "loaders", "getLoadersFromContext", "<PERSON><PERSON><PERSON><PERSON>", "contextLoaders", "length"], "sources": ["../../../../src/lib/loader-utils/loader-context.ts"], "sourcesContent": ["import type {Loader, LoaderOptions, LoaderContext} from '@loaders.gl/loader-utils';\nimport {getFetchFunction} from './get-fetch-function';\nimport {extractQueryString, stripQueryString} from '../utils/url-utils';\nimport {path} from '@loaders.gl/loader-utils';\n\n/**\n * \"sub\" loaders invoked by other loaders get a \"context\" injected on `this`\n * The context will inject core methods like `parse` and contain information\n * about loaders and options passed in to the top-level `parse` call.\n *\n * @param context\n * @param options\n * @param previousContext\n */\nexport function getLoaderContext(\n  context: Omit<LoaderContext, 'fetch'> & Partial<Pick<LoaderContext, 'fetch'>>,\n  options: LoaderOptions,\n  parentContext: LoaderContext | null\n): LoaderContext {\n  // For recursive calls, we already have a context\n  // TODO - add any additional loaders to context?\n  if (parentContext) {\n    return parentContext;\n  }\n\n  const newContext: LoaderContext = {\n    fetch: getFetchFunction(options, context),\n    ...context\n  };\n\n  // Parse URLs so that subloaders can easily generate correct strings\n  if (newContext.url) {\n    const baseUrl = stripQueryString(newContext.url);\n    newContext.baseUrl = baseUrl;\n    newContext.queryString = extractQueryString(newContext.url);\n    newContext.filename = path.filename(baseUrl);\n    newContext.baseUrl = path.dirname(baseUrl);\n  }\n\n  // Recursive loading does not use single loader\n  if (!Array.isArray(newContext.loaders)) {\n    newContext.loaders = null;\n  }\n\n  return newContext;\n}\n\n// eslint-disable-next-line complexity\nexport function getLoadersFromContext(\n  loaders: Loader[] | Loader | undefined,\n  context?: LoaderContext\n) {\n  // A single non-array loader is force selected, but only on top-level (context === null)\n  if (!context && loaders && !Array.isArray(loaders)) {\n    return loaders;\n  }\n\n  // Create a merged list\n  let candidateLoaders;\n  if (loaders) {\n    candidateLoaders = Array.isArray(loaders) ? loaders : [loaders];\n  }\n  if (context && context.loaders) {\n    const contextLoaders = Array.isArray(context.loaders) ? context.loaders : [context.loaders];\n    candidateLoaders = candidateLoaders ? [...candidateLoaders, ...contextLoaders] : contextLoaders;\n  }\n  // If no loaders, return null to look in globally registered loaders\n  return candidateLoaders && candidateLoaders.length ? candidateLoaders : null;\n}\n"], "mappings": ";;;;;;;AACA,IAAAA,iBAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,YAAA,GAAAF,OAAA;AAWO,SAASG,gBAAgBA,CAC9BC,OAA6E,EAC7EC,OAAsB,EACtBC,aAAmC,EACpB;EAGf,IAAIA,aAAa,EAAE;IACjB,OAAOA,aAAa;EACtB;EAEA,MAAMC,UAAyB,GAAG;IAChCC,KAAK,EAAE,IAAAC,kCAAgB,EAACJ,OAAO,EAAED,OAAO,CAAC;IACzC,GAAGA;EACL,CAAC;EAGD,IAAIG,UAAU,CAACG,GAAG,EAAE;IAClB,MAAMC,OAAO,GAAG,IAAAC,0BAAgB,EAACL,UAAU,CAACG,GAAG,CAAC;IAChDH,UAAU,CAACI,OAAO,GAAGA,OAAO;IAC5BJ,UAAU,CAACM,WAAW,GAAG,IAAAC,4BAAkB,EAACP,UAAU,CAACG,GAAG,CAAC;IAC3DH,UAAU,CAACQ,QAAQ,GAAGC,iBAAI,CAACD,QAAQ,CAACJ,OAAO,CAAC;IAC5CJ,UAAU,CAACI,OAAO,GAAGK,iBAAI,CAACC,OAAO,CAACN,OAAO,CAAC;EAC5C;EAGA,IAAI,CAACO,KAAK,CAACC,OAAO,CAACZ,UAAU,CAACa,OAAO,CAAC,EAAE;IACtCb,UAAU,CAACa,OAAO,GAAG,IAAI;EAC3B;EAEA,OAAOb,UAAU;AACnB;AAGO,SAASc,qBAAqBA,CACnCD,OAAsC,EACtChB,OAAuB,EACvB;EAEA,IAAI,CAACA,OAAO,IAAIgB,OAAO,IAAI,CAACF,KAAK,CAACC,OAAO,CAACC,OAAO,CAAC,EAAE;IAClD,OAAOA,OAAO;EAChB;EAGA,IAAIE,gBAAgB;EACpB,IAAIF,OAAO,EAAE;IACXE,gBAAgB,GAAGJ,KAAK,CAACC,OAAO,CAACC,OAAO,CAAC,GAAGA,OAAO,GAAG,CAACA,OAAO,CAAC;EACjE;EACA,IAAIhB,OAAO,IAAIA,OAAO,CAACgB,OAAO,EAAE;IAC9B,MAAMG,cAAc,GAAGL,KAAK,CAACC,OAAO,CAACf,OAAO,CAACgB,OAAO,CAAC,GAAGhB,OAAO,CAACgB,OAAO,GAAG,CAAChB,OAAO,CAACgB,OAAO,CAAC;IAC3FE,gBAAgB,GAAGA,gBAAgB,GAAG,CAAC,GAAGA,gBAAgB,EAAE,GAAGC,cAAc,CAAC,GAAGA,cAAc;EACjG;EAEA,OAAOD,gBAAgB,IAAIA,gBAAgB,CAACE,MAAM,GAAGF,gBAAgB,GAAG,IAAI;AAC9E"}