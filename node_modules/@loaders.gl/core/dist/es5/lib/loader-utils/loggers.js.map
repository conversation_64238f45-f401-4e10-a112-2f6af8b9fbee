{"version": 3, "file": "loggers.js", "names": ["_log", "require", "probeLog", "Log", "id", "exports", "<PERSON><PERSON><PERSON><PERSON>", "log", "info", "warn", "error", "ConsoleLog", "constructor", "_defineProperty2", "default", "console", "_len", "arguments", "length", "args", "Array", "_key", "bind", "_len2", "_key2", "_len3", "_key3", "_len4", "_key4"], "sources": ["../../../../src/lib/loader-utils/loggers.ts"], "sourcesContent": ["// probe.gl Log compatible loggers\nimport {Log} from '@probe.gl/log';\n\nexport const probeLog = new Log({id: 'loaders.gl'});\n\n// Logs nothing\nexport class NullLog {\n  log() {\n    return () => {};\n  }\n  info() {\n    return () => {};\n  }\n  warn() {\n    return () => {};\n  }\n  error() {\n    return () => {};\n  }\n}\n\n// Logs to console\nexport class ConsoleLog {\n  console;\n\n  constructor() {\n    this.console = console; // eslint-disable-line\n  }\n  log(...args) {\n    return this.console.log.bind(this.console, ...args);\n  }\n  info(...args) {\n    return this.console.info.bind(this.console, ...args);\n  }\n  warn(...args) {\n    return this.console.warn.bind(this.console, ...args);\n  }\n  error(...args) {\n    return this.console.error.bind(this.console, ...args);\n  }\n}\n"], "mappings": ";;;;;;;;AACA,IAAAA,IAAA,GAAAC,OAAA;AAEO,MAAMC,QAAQ,GAAG,IAAIC,QAAG,CAAC;EAACC,EAAE,EAAE;AAAY,CAAC,CAAC;AAACC,OAAA,CAAAH,QAAA,GAAAA,QAAA;AAG7C,MAAMI,OAAO,CAAC;EACnBC,GAAGA,CAAA,EAAG;IACJ,OAAO,MAAM,CAAC,CAAC;EACjB;EACAC,IAAIA,CAAA,EAAG;IACL,OAAO,MAAM,CAAC,CAAC;EACjB;EACAC,IAAIA,CAAA,EAAG;IACL,OAAO,MAAM,CAAC,CAAC;EACjB;EACAC,KAAKA,CAAA,EAAG;IACN,OAAO,MAAM,CAAC,CAAC;EACjB;AACF;AAACL,OAAA,CAAAC,OAAA,GAAAA,OAAA;AAGM,MAAMK,UAAU,CAAC;EAGtBC,WAAWA,CAAA,EAAG;IAAA,IAAAC,gBAAA,CAAAC,OAAA;IACZ,IAAI,CAACC,OAAO,GAAGA,OAAO;EACxB;EACAR,GAAGA,CAAA,EAAU;IAAA,SAAAS,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IACT,OAAO,IAAI,CAACN,OAAO,CAACR,GAAG,CAACe,IAAI,CAAC,IAAI,CAACP,OAAO,EAAE,GAAGI,IAAI,CAAC;EACrD;EACAX,IAAIA,CAAA,EAAU;IAAA,SAAAe,KAAA,GAAAN,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAG,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJL,IAAI,CAAAK,KAAA,IAAAP,SAAA,CAAAO,KAAA;IAAA;IACV,OAAO,IAAI,CAACT,OAAO,CAACP,IAAI,CAACc,IAAI,CAAC,IAAI,CAACP,OAAO,EAAE,GAAGI,IAAI,CAAC;EACtD;EACAV,IAAIA,CAAA,EAAU;IAAA,SAAAgB,KAAA,GAAAR,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAK,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJP,IAAI,CAAAO,KAAA,IAAAT,SAAA,CAAAS,KAAA;IAAA;IACV,OAAO,IAAI,CAACX,OAAO,CAACN,IAAI,CAACa,IAAI,CAAC,IAAI,CAACP,OAAO,EAAE,GAAGI,IAAI,CAAC;EACtD;EACAT,KAAKA,CAAA,EAAU;IAAA,SAAAiB,KAAA,GAAAV,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAO,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJT,IAAI,CAAAS,KAAA,IAAAX,SAAA,CAAAW,KAAA;IAAA;IACX,OAAO,IAAI,CAACb,OAAO,CAACL,KAAK,CAACY,IAAI,CAAC,IAAI,CAACP,OAAO,EAAE,GAAGI,IAAI,CAAC;EACvD;AACF;AAACd,OAAA,CAAAM,UAAA,GAAAA,UAAA"}