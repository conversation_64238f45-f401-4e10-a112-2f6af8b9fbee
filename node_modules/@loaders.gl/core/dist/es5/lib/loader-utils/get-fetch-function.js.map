{"version": 3, "file": "get-fetch-function.js", "names": ["_isType", "require", "_fetchFile", "_optionUtils", "getFetchFunction", "options", "context", "globalOptions", "getGlobalLoaderOptions", "fetchOptions", "fetch", "isObject", "url", "fetchFile"], "sources": ["../../../../src/lib/loader-utils/get-fetch-function.ts"], "sourcesContent": ["// loaders.gl, MIT license\n\nimport type {LoaderContext, LoaderOptions} from '@loaders.gl/loader-utils';\nimport {isObject} from '../../javascript-utils/is-type';\nimport {fetchFile} from '../fetch/fetch-file';\nimport {getGlobalLoaderOptions} from './option-utils';\n\n/**\n * Gets the current fetch function from options and context\n * @param options\n * @param context\n */\nexport function getFetchFunction(\n  options?: LoaderOptions,\n  context?: Omit<LoaderContext, 'fetch'> & Partial<Pick<LoaderContext, 'fetch'>>\n) {\n  const globalOptions = getGlobalLoaderOptions();\n\n  const fetchOptions = options || globalOptions;\n\n  // options.fetch can be a function\n  if (typeof fetchOptions.fetch === 'function') {\n    return fetchOptions.fetch;\n  }\n\n  // options.fetch can be an options object\n  if (isObject(fetchOptions.fetch)) {\n    return (url) => fetchFile(url, fetchOptions as RequestInit);\n  }\n\n  // else refer to context (from parent loader) if available\n  if (context?.fetch) {\n    return context?.fetch;\n  }\n\n  // else return the default fetch function\n  return fetchFile;\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;AACA,IAAAE,YAAA,GAAAF,OAAA;AAOO,SAASG,gBAAgBA,CAC9BC,OAAuB,EACvBC,OAA8E,EAC9E;EACA,MAAMC,aAAa,GAAG,IAAAC,mCAAsB,EAAC,CAAC;EAE9C,MAAMC,YAAY,GAAGJ,OAAO,IAAIE,aAAa;EAG7C,IAAI,OAAOE,YAAY,CAACC,KAAK,KAAK,UAAU,EAAE;IAC5C,OAAOD,YAAY,CAACC,KAAK;EAC3B;EAGA,IAAI,IAAAC,gBAAQ,EAACF,YAAY,CAACC,KAAK,CAAC,EAAE;IAChC,OAAQE,GAAG,IAAK,IAAAC,oBAAS,EAACD,GAAG,EAAEH,YAA2B,CAAC;EAC7D;EAGA,IAAIH,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEI,KAAK,EAAE;IAClB,OAAOJ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEI,KAAK;EACvB;EAGA,OAAOG,oBAAS;AAClB"}