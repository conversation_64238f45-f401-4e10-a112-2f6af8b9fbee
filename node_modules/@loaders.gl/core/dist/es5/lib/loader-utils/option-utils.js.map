{"version": 3, "file": "option-utils.js", "names": ["_isType", "require", "_loggers", "_optionDefaults", "getGlobalLoaderState", "globalThis", "loaders", "_state", "getGlobalLoaderOptions", "state", "globalOptions", "DEFAULT_LOADER_OPTIONS", "exports", "setGlobalOptions", "options", "normalizeOptionsInternal", "normalizeOptions", "loader", "url", "Array", "isArray", "validateOptions", "validateOptionsObject", "REMOVED_LOADER_OPTIONS", "idOptions", "id", "loaderOptions", "deprecatedOptions", "defaultOptions", "loaderName", "prefix", "concat", "key", "isSubOptions", "isObject", "isBaseUriOption", "isWorkerUrlOption", "probeLog", "warn", "suggestion", "findSimilarOption", "optionKey", "lowerCaseOptionKey", "toLowerCase", "bestSuggestion", "lowerCaseKey", "isPartialMatch", "startsWith", "loaderDefaultOptions", "mergedOptions", "addUrlOptions", "log", "<PERSON><PERSON><PERSON><PERSON>", "mergeNestedFields", "value", "isPureObject", "baseUri"], "sources": ["../../../../src/lib/loader-utils/option-utils.ts"], "sourcesContent": ["// loaders.gl, MIT license\n\nimport type {Loader, LoaderOptions} from '@loaders.gl/loader-utils';\nimport {isPureObject, isObject} from '../../javascript-utils/is-type';\nimport {probeLog, NullLog} from './loggers';\nimport {DEFAULT_LOADER_OPTIONS, REMOVED_LOADER_OPTIONS} from './option-defaults';\n\n/**\n * Global state for loaders.gl. Stored on `global.loaders._state`\n */\ntype GlobalLoaderState = {\n  loaderRegistry: Loader[];\n  globalOptions: LoaderOptions;\n};\n\n/**\n * Helper for safely accessing global loaders.gl variables\n * Wraps initialization of global variable in function to defeat overly aggressive tree-shakers\n */\nexport function getGlobalLoaderState(): GlobalLoaderState {\n  // @ts-ignore\n  globalThis.loaders = globalThis.loaders || {};\n  // @ts-ignore\n  const {loaders} = globalThis;\n\n  // Add _state object to keep separate from modules added to globalThis.loaders\n  loaders._state = loaders._state || {};\n  return loaders._state;\n}\n\n/**\n * Store global loader options on the global object to increase chances of cross loaders-version interoperability\n * NOTE: This use case is not reliable but can help when testing new versions of loaders.gl with existing frameworks\n * @returns global loader options merged with default loader options\n */\nexport const getGlobalLoaderOptions = (): LoaderOptions => {\n  const state = getGlobalLoaderState();\n  // Ensure all default loader options from this library are mentioned\n  state.globalOptions = state.globalOptions || {...DEFAULT_LOADER_OPTIONS};\n  return state.globalOptions;\n};\n\n/**\n * Set global loader options\n * @param options\n */\nexport function setGlobalOptions(options: LoaderOptions): void {\n  const state = getGlobalLoaderState();\n  const globalOptions = getGlobalLoaderOptions();\n  state.globalOptions = normalizeOptionsInternal(globalOptions, options);\n}\n\n/**\n * Merges options with global opts and loader defaults, also injects baseUri\n * @param options\n * @param loader\n * @param loaders\n * @param url\n */\nexport function normalizeOptions(\n  options: LoaderOptions,\n  loader: Loader,\n  loaders?: Loader[],\n  url?: string\n): LoaderOptions {\n  loaders = loaders || [];\n  loaders = Array.isArray(loaders) ? loaders : [loaders];\n\n  validateOptions(options, loaders);\n  return normalizeOptionsInternal(loader, options, url);\n}\n\n// VALIDATE OPTIONS\n\n/**\n * Warn for unsupported options\n * @param options\n * @param loaders\n */\nfunction validateOptions(options: LoaderOptions, loaders: Loader[]) {\n  // Check top level options\n  validateOptionsObject(options, null, DEFAULT_LOADER_OPTIONS, REMOVED_LOADER_OPTIONS, loaders);\n  for (const loader of loaders) {\n    // Get the scoped, loader specific options from the user supplied options\n    const idOptions = (options && options[loader.id]) || {};\n\n    // Get scoped, loader specific default and deprecated options from the selected loader\n    const loaderOptions = (loader.options && loader.options[loader.id]) || {};\n    const deprecatedOptions =\n      (loader.deprecatedOptions && loader.deprecatedOptions[loader.id]) || {};\n\n    // Validate loader specific options\n    validateOptionsObject(idOptions, loader.id, loaderOptions, deprecatedOptions, loaders);\n  }\n}\n\n// eslint-disable-next-line max-params, complexity\nfunction validateOptionsObject(\n  options,\n  id: string | null,\n  defaultOptions,\n  deprecatedOptions,\n  loaders: Loader[]\n) {\n  const loaderName = id || 'Top level';\n  const prefix = id ? `${id}.` : '';\n\n  for (const key in options) {\n    // If top level option value is an object it could options for a loader, so ignore\n    const isSubOptions = !id && isObject(options[key]);\n    const isBaseUriOption = key === 'baseUri' && !id;\n    const isWorkerUrlOption = key === 'workerUrl' && id;\n    // <loader>.workerUrl requires special handling as it is now auto-generated and no longer specified as a default option.\n    if (!(key in defaultOptions) && !isBaseUriOption && !isWorkerUrlOption) {\n      // Issue deprecation warnings\n      if (key in deprecatedOptions) {\n        probeLog.warn(\n          `${loaderName} loader option \\'${prefix}${key}\\' no longer supported, use \\'${deprecatedOptions[key]}\\'`\n        )();\n      } else if (!isSubOptions) {\n        const suggestion = findSimilarOption(key, loaders);\n        probeLog.warn(\n          `${loaderName} loader option \\'${prefix}${key}\\' not recognized. ${suggestion}`\n        )();\n      }\n    }\n  }\n}\n\nfunction findSimilarOption(optionKey, loaders) {\n  const lowerCaseOptionKey = optionKey.toLowerCase();\n  let bestSuggestion = '';\n  for (const loader of loaders) {\n    for (const key in loader.options) {\n      if (optionKey === key) {\n        return `Did you mean \\'${loader.id}.${key}\\'?`;\n      }\n      const lowerCaseKey = key.toLowerCase();\n      const isPartialMatch =\n        lowerCaseOptionKey.startsWith(lowerCaseKey) || lowerCaseKey.startsWith(lowerCaseOptionKey);\n      if (isPartialMatch) {\n        bestSuggestion = bestSuggestion || `Did you mean \\'${loader.id}.${key}\\'?`;\n      }\n    }\n  }\n  return bestSuggestion;\n}\n\nfunction normalizeOptionsInternal(loader, options, url?: string) {\n  const loaderDefaultOptions = loader.options || {};\n\n  const mergedOptions = {...loaderDefaultOptions};\n\n  addUrlOptions(mergedOptions, url);\n\n  // LOGGING: options.log can be set to `null` to defeat logging\n  if (mergedOptions.log === null) {\n    mergedOptions.log = new NullLog();\n  }\n\n  mergeNestedFields(mergedOptions, getGlobalLoaderOptions());\n  mergeNestedFields(mergedOptions, options);\n\n  return mergedOptions;\n}\n\n// Merge nested options objects\nfunction mergeNestedFields(mergedOptions, options) {\n  for (const key in options) {\n    // Check for nested options\n    // object in options => either no key in defaultOptions or object in defaultOptions\n    if (key in options) {\n      const value = options[key];\n      if (isPureObject(value) && isPureObject(mergedOptions[key])) {\n        mergedOptions[key] = {\n          ...mergedOptions[key],\n          ...options[key]\n        };\n      } else {\n        mergedOptions[key] = options[key];\n      }\n    }\n    // else: No need to merge nested opts, and the initial merge already copied over the nested options\n  }\n}\n\n/**\n * Harvest information from the url\n * @deprecated This is mainly there to support a hack in the GLTFLoader\n * TODO - baseUri should be a directory, i.e. remove file component from baseUri\n * TODO - extract extension?\n * TODO - extract query parameters?\n * TODO - should these be injected on context instead of options?\n */\nfunction addUrlOptions(options, url?: string) {\n  if (url && !('baseUri' in options)) {\n    options.baseUri = url;\n  }\n}\n"], "mappings": ";;;;;;;;;AAGA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,eAAA,GAAAF,OAAA;AAcO,SAASG,oBAAoBA,CAAA,EAAsB;EAExDC,UAAU,CAACC,OAAO,GAAGD,UAAU,CAACC,OAAO,IAAI,CAAC,CAAC;EAE7C,MAAM;IAACA;EAAO,CAAC,GAAGD,UAAU;EAG5BC,OAAO,CAACC,MAAM,GAAGD,OAAO,CAACC,MAAM,IAAI,CAAC,CAAC;EACrC,OAAOD,OAAO,CAACC,MAAM;AACvB;AAOO,MAAMC,sBAAsB,GAAGA,CAAA,KAAqB;EACzD,MAAMC,KAAK,GAAGL,oBAAoB,CAAC,CAAC;EAEpCK,KAAK,CAACC,aAAa,GAAGD,KAAK,CAACC,aAAa,IAAI;IAAC,GAAGC;EAAsB,CAAC;EACxE,OAAOF,KAAK,CAACC,aAAa;AAC5B,CAAC;AAACE,OAAA,CAAAJ,sBAAA,GAAAA,sBAAA;AAMK,SAASK,gBAAgBA,CAACC,OAAsB,EAAQ;EAC7D,MAAML,KAAK,GAAGL,oBAAoB,CAAC,CAAC;EACpC,MAAMM,aAAa,GAAGF,sBAAsB,CAAC,CAAC;EAC9CC,KAAK,CAACC,aAAa,GAAGK,wBAAwB,CAACL,aAAa,EAAEI,OAAO,CAAC;AACxE;AASO,SAASE,gBAAgBA,CAC9BF,OAAsB,EACtBG,MAAc,EACdX,OAAkB,EAClBY,GAAY,EACG;EACfZ,OAAO,GAAGA,OAAO,IAAI,EAAE;EACvBA,OAAO,GAAGa,KAAK,CAACC,OAAO,CAACd,OAAO,CAAC,GAAGA,OAAO,GAAG,CAACA,OAAO,CAAC;EAEtDe,eAAe,CAACP,OAAO,EAAER,OAAO,CAAC;EACjC,OAAOS,wBAAwB,CAACE,MAAM,EAAEH,OAAO,EAAEI,GAAG,CAAC;AACvD;AASA,SAASG,eAAeA,CAACP,OAAsB,EAAER,OAAiB,EAAE;EAElEgB,qBAAqB,CAACR,OAAO,EAAE,IAAI,EAAEH,sCAAsB,EAAEY,sCAAsB,EAAEjB,OAAO,CAAC;EAC7F,KAAK,MAAMW,MAAM,IAAIX,OAAO,EAAE;IAE5B,MAAMkB,SAAS,GAAIV,OAAO,IAAIA,OAAO,CAACG,MAAM,CAACQ,EAAE,CAAC,IAAK,CAAC,CAAC;IAGvD,MAAMC,aAAa,GAAIT,MAAM,CAACH,OAAO,IAAIG,MAAM,CAACH,OAAO,CAACG,MAAM,CAACQ,EAAE,CAAC,IAAK,CAAC,CAAC;IACzE,MAAME,iBAAiB,GACpBV,MAAM,CAACU,iBAAiB,IAAIV,MAAM,CAACU,iBAAiB,CAACV,MAAM,CAACQ,EAAE,CAAC,IAAK,CAAC,CAAC;IAGzEH,qBAAqB,CAACE,SAAS,EAAEP,MAAM,CAACQ,EAAE,EAAEC,aAAa,EAAEC,iBAAiB,EAAErB,OAAO,CAAC;EACxF;AACF;AAGA,SAASgB,qBAAqBA,CAC5BR,OAAO,EACPW,EAAiB,EACjBG,cAAc,EACdD,iBAAiB,EACjBrB,OAAiB,EACjB;EACA,MAAMuB,UAAU,GAAGJ,EAAE,IAAI,WAAW;EACpC,MAAMK,MAAM,GAAGL,EAAE,MAAAM,MAAA,CAAMN,EAAE,SAAM,EAAE;EAEjC,KAAK,MAAMO,GAAG,IAAIlB,OAAO,EAAE;IAEzB,MAAMmB,YAAY,GAAG,CAACR,EAAE,IAAI,IAAAS,gBAAQ,EAACpB,OAAO,CAACkB,GAAG,CAAC,CAAC;IAClD,MAAMG,eAAe,GAAGH,GAAG,KAAK,SAAS,IAAI,CAACP,EAAE;IAChD,MAAMW,iBAAiB,GAAGJ,GAAG,KAAK,WAAW,IAAIP,EAAE;IAEnD,IAAI,EAAEO,GAAG,IAAIJ,cAAc,CAAC,IAAI,CAACO,eAAe,IAAI,CAACC,iBAAiB,EAAE;MAEtE,IAAIJ,GAAG,IAAIL,iBAAiB,EAAE;QAC5BU,iBAAQ,CAACC,IAAI,IAAAP,MAAA,CACRF,UAAU,sBAAAE,MAAA,CAAoBD,MAAM,EAAAC,MAAA,CAAGC,GAAG,kCAAAD,MAAA,CAAiCJ,iBAAiB,CAACK,GAAG,CAAC,MACtG,CAAC,CAAC,CAAC;MACL,CAAC,MAAM,IAAI,CAACC,YAAY,EAAE;QACxB,MAAMM,UAAU,GAAGC,iBAAiB,CAACR,GAAG,EAAE1B,OAAO,CAAC;QAClD+B,iBAAQ,CAACC,IAAI,IAAAP,MAAA,CACRF,UAAU,sBAAAE,MAAA,CAAoBD,MAAM,EAAAC,MAAA,CAAGC,GAAG,wBAAAD,MAAA,CAAsBQ,UAAU,CAC/E,CAAC,CAAC,CAAC;MACL;IACF;EACF;AACF;AAEA,SAASC,iBAAiBA,CAACC,SAAS,EAAEnC,OAAO,EAAE;EAC7C,MAAMoC,kBAAkB,GAAGD,SAAS,CAACE,WAAW,CAAC,CAAC;EAClD,IAAIC,cAAc,GAAG,EAAE;EACvB,KAAK,MAAM3B,MAAM,IAAIX,OAAO,EAAE;IAC5B,KAAK,MAAM0B,GAAG,IAAIf,MAAM,CAACH,OAAO,EAAE;MAChC,IAAI2B,SAAS,KAAKT,GAAG,EAAE;QACrB,wBAAAD,MAAA,CAAyBd,MAAM,CAACQ,EAAE,OAAAM,MAAA,CAAIC,GAAG;MAC3C;MACA,MAAMa,YAAY,GAAGb,GAAG,CAACW,WAAW,CAAC,CAAC;MACtC,MAAMG,cAAc,GAClBJ,kBAAkB,CAACK,UAAU,CAACF,YAAY,CAAC,IAAIA,YAAY,CAACE,UAAU,CAACL,kBAAkB,CAAC;MAC5F,IAAII,cAAc,EAAE;QAClBF,cAAc,GAAGA,cAAc,qBAAAb,MAAA,CAAsBd,MAAM,CAACQ,EAAE,OAAAM,MAAA,CAAIC,GAAG,OAAK;MAC5E;IACF;EACF;EACA,OAAOY,cAAc;AACvB;AAEA,SAAS7B,wBAAwBA,CAACE,MAAM,EAAEH,OAAO,EAAEI,GAAY,EAAE;EAC/D,MAAM8B,oBAAoB,GAAG/B,MAAM,CAACH,OAAO,IAAI,CAAC,CAAC;EAEjD,MAAMmC,aAAa,GAAG;IAAC,GAAGD;EAAoB,CAAC;EAE/CE,aAAa,CAACD,aAAa,EAAE/B,GAAG,CAAC;EAGjC,IAAI+B,aAAa,CAACE,GAAG,KAAK,IAAI,EAAE;IAC9BF,aAAa,CAACE,GAAG,GAAG,IAAIC,gBAAO,CAAC,CAAC;EACnC;EAEAC,iBAAiB,CAACJ,aAAa,EAAEzC,sBAAsB,CAAC,CAAC,CAAC;EAC1D6C,iBAAiB,CAACJ,aAAa,EAAEnC,OAAO,CAAC;EAEzC,OAAOmC,aAAa;AACtB;AAGA,SAASI,iBAAiBA,CAACJ,aAAa,EAAEnC,OAAO,EAAE;EACjD,KAAK,MAAMkB,GAAG,IAAIlB,OAAO,EAAE;IAGzB,IAAIkB,GAAG,IAAIlB,OAAO,EAAE;MAClB,MAAMwC,KAAK,GAAGxC,OAAO,CAACkB,GAAG,CAAC;MAC1B,IAAI,IAAAuB,oBAAY,EAACD,KAAK,CAAC,IAAI,IAAAC,oBAAY,EAACN,aAAa,CAACjB,GAAG,CAAC,CAAC,EAAE;QAC3DiB,aAAa,CAACjB,GAAG,CAAC,GAAG;UACnB,GAAGiB,aAAa,CAACjB,GAAG,CAAC;UACrB,GAAGlB,OAAO,CAACkB,GAAG;QAChB,CAAC;MACH,CAAC,MAAM;QACLiB,aAAa,CAACjB,GAAG,CAAC,GAAGlB,OAAO,CAACkB,GAAG,CAAC;MACnC;IACF;EAEF;AACF;AAUA,SAASkB,aAAaA,CAACpC,OAAO,EAAEI,GAAY,EAAE;EAC5C,IAAIA,GAAG,IAAI,EAAE,SAAS,IAAIJ,OAAO,CAAC,EAAE;IAClCA,OAAO,CAAC0C,OAAO,GAAGtC,GAAG;EACvB;AACF"}