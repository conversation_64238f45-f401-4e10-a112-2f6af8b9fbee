"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getArrayBufferOrStringFromData = getArrayBufferOrStringFromData;
exports.getArrayBufferOrStringFromDataSync = getArrayBufferOrStringFromDataSync;
exports.getAsyncIterableFromData = getAsyncIterableFromData;
exports.getReadableStream = getReadableStream;
var _loaderUtils = require("@loaders.gl/loader-utils");
var _isType = require("../../javascript-utils/is-type");
var _makeIterator = require("../../iterators/make-iterator/make-iterator");
var _responseUtils = require("../utils/response-utils");
const ERR_DATA = 'Cannot convert supplied data type';
function getArrayBufferOrStringFromDataSync(data, loader, options) {
  if (loader.text && typeof data === 'string') {
    return data;
  }
  if ((0, _isType.isBuffer)(data)) {
    data = data.buffer;
  }
  if (data instanceof ArrayBuffer) {
    const arrayBuffer = data;
    if (loader.text && !loader.binary) {
      const textDecoder = new TextDecoder('utf8');
      return textDecoder.decode(arrayBuffer);
    }
    return arrayBuffer;
  }
  if (ArrayBuffer.isView(data)) {
    if (loader.text && !loader.binary) {
      const textDecoder = new TextDecoder('utf8');
      return textDecoder.decode(data);
    }
    let arrayBuffer = data.buffer;
    const byteLength = data.byteLength || data.length;
    if (data.byteOffset !== 0 || byteLength !== arrayBuffer.byteLength) {
      arrayBuffer = arrayBuffer.slice(data.byteOffset, data.byteOffset + byteLength);
    }
    return arrayBuffer;
  }
  throw new Error(ERR_DATA);
}
async function getArrayBufferOrStringFromData(data, loader, options) {
  const isArrayBuffer = data instanceof ArrayBuffer || ArrayBuffer.isView(data);
  if (typeof data === 'string' || isArrayBuffer) {
    return getArrayBufferOrStringFromDataSync(data, loader, options);
  }
  if ((0, _isType.isBlob)(data)) {
    data = await (0, _responseUtils.makeResponse)(data);
  }
  if ((0, _isType.isResponse)(data)) {
    const response = data;
    await (0, _responseUtils.checkResponse)(response);
    return loader.binary ? await response.arrayBuffer() : await response.text();
  }
  if ((0, _isType.isReadableStream)(data)) {
    data = (0, _makeIterator.makeIterator)(data, options);
  }
  if ((0, _isType.isIterable)(data) || (0, _isType.isAsyncIterable)(data)) {
    return (0, _loaderUtils.concatenateArrayBuffersAsync)(data);
  }
  throw new Error(ERR_DATA);
}
async function getAsyncIterableFromData(data, options) {
  if ((0, _isType.isIterator)(data)) {
    return data;
  }
  if ((0, _isType.isResponse)(data)) {
    const response = data;
    await (0, _responseUtils.checkResponse)(response);
    const body = await response.body;
    return (0, _makeIterator.makeIterator)(body, options);
  }
  if ((0, _isType.isBlob)(data) || (0, _isType.isReadableStream)(data)) {
    return (0, _makeIterator.makeIterator)(data, options);
  }
  if ((0, _isType.isAsyncIterable)(data)) {
    return data[Symbol.asyncIterator]();
  }
  return getIterableFromData(data);
}
async function getReadableStream(data) {
  if ((0, _isType.isReadableStream)(data)) {
    return data;
  }
  if ((0, _isType.isResponse)(data)) {
    return data.body;
  }
  const response = await (0, _responseUtils.makeResponse)(data);
  return response.body;
}
function getIterableFromData(data) {
  if (ArrayBuffer.isView(data)) {
    return function* oneChunk() {
      yield data.buffer;
    }();
  }
  if (data instanceof ArrayBuffer) {
    return function* oneChunk() {
      yield data;
    }();
  }
  if ((0, _isType.isIterator)(data)) {
    return data;
  }
  if ((0, _isType.isIterable)(data)) {
    return data[Symbol.iterator]();
  }
  throw new Error(ERR_DATA);
}
//# sourceMappingURL=get-data.js.map