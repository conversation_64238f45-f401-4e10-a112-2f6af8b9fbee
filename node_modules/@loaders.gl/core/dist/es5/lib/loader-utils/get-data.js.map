{"version": 3, "file": "get-data.js", "names": ["_loaderUtils", "require", "_isType", "_makeIterator", "_responseUtils", "ERR_DATA", "getArrayBufferOrStringFromDataSync", "data", "loader", "options", "text", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arrayBuffer", "binary", "textDecoder", "TextDecoder", "decode", "<PERSON><PERSON><PERSON><PERSON>", "byteLength", "length", "byteOffset", "slice", "Error", "getArrayBufferOrStringFromData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isBlob", "makeResponse", "isResponse", "response", "checkResponse", "isReadableStream", "makeIterator", "isIterable", "isAsyncIterable", "concatenateArrayBuffersAsync", "getAsyncIterableFromData", "isIterator", "body", "Symbol", "asyncIterator", "getIterableFromData", "getReadableStream", "oneChunk", "iterator"], "sources": ["../../../../src/lib/loader-utils/get-data.ts"], "sourcesContent": ["import type {\n  DataType,\n  SyncDataType,\n  BatchableDataType,\n  Loader,\n  LoaderOptions\n} from '@loaders.gl/loader-utils';\nimport {concatenateArrayBuffersAsync} from '@loaders.gl/loader-utils';\nimport {\n  isResponse,\n  isReadableStream,\n  isAsyncIterable,\n  isIterable,\n  isIterator,\n  isBlob,\n  isBuffer\n} from '../../javascript-utils/is-type';\nimport {makeIterator} from '../../iterators/make-iterator/make-iterator';\nimport {checkResponse, makeResponse} from '../utils/response-utils';\n\nconst ERR_DATA = 'Cannot convert supplied data type';\n\n// eslint-disable-next-line complexity\nexport function getArrayBufferOrStringFromDataSync(\n  data: SyncDataType,\n  loader: Loader,\n  options: LoaderOptions\n): ArrayBuffer | string {\n  if (loader.text && typeof data === 'string') {\n    return data;\n  }\n\n  if (isBuffer(data)) {\n    // @ts-ignore\n    data = data.buffer;\n  }\n\n  if (data instanceof ArrayBuffer) {\n    const arrayBuffer = data;\n    if (loader.text && !loader.binary) {\n      const textDecoder = new TextDecoder('utf8');\n      return textDecoder.decode(arrayBuffer);\n    }\n    return arrayBuffer;\n  }\n\n  // We may need to handle offsets\n  if (ArrayBuffer.isView(data)) {\n    // TextDecoder is invoked on typed arrays and will handle offsets\n    if (loader.text && !loader.binary) {\n      const textDecoder = new TextDecoder('utf8');\n      return textDecoder.decode(data);\n    }\n\n    let arrayBuffer = data.buffer;\n\n    // Since we are returning the underlying arrayBuffer, we must create a new copy\n    // if this typed array / Buffer is a partial view into the ArryayBuffer\n    // TODO - this is a potentially unnecessary copy\n    const byteLength = data.byteLength || data.length;\n    if (data.byteOffset !== 0 || byteLength !== arrayBuffer.byteLength) {\n      // console.warn(`loaders.gl copying arraybuffer of length ${byteLength}`);\n      arrayBuffer = arrayBuffer.slice(data.byteOffset, data.byteOffset + byteLength);\n    }\n    return arrayBuffer;\n  }\n\n  throw new Error(ERR_DATA);\n}\n\n// Convert async iterator to a promise\nexport async function getArrayBufferOrStringFromData(\n  data: DataType,\n  loader: Loader,\n  options: LoaderOptions\n): Promise<ArrayBuffer | string> {\n  const isArrayBuffer = data instanceof ArrayBuffer || ArrayBuffer.isView(data);\n  if (typeof data === 'string' || isArrayBuffer) {\n    return getArrayBufferOrStringFromDataSync(data as string | ArrayBuffer, loader, options);\n  }\n\n  // Blobs and files are FileReader compatible\n  if (isBlob(data)) {\n    data = await makeResponse(data);\n  }\n\n  if (isResponse(data)) {\n    const response = data as Response;\n    await checkResponse(response);\n    return loader.binary ? await response.arrayBuffer() : await response.text();\n  }\n\n  if (isReadableStream(data)) {\n    // @ts-expect-error TS2559 options type\n    data = makeIterator(data as ReadableStream, options);\n  }\n\n  if (isIterable(data) || isAsyncIterable(data)) {\n    // Assume arrayBuffer iterator - attempt to concatenate\n    return concatenateArrayBuffersAsync(data as AsyncIterable<ArrayBuffer>);\n  }\n\n  throw new Error(ERR_DATA);\n}\n\nexport async function getAsyncIterableFromData(\n  data: BatchableDataType,\n  options: LoaderOptions\n): Promise<AsyncIterable<ArrayBuffer> | Iterable<ArrayBuffer>> {\n  if (isIterator(data)) {\n    return data as AsyncIterable<ArrayBuffer>;\n  }\n\n  if (isResponse(data)) {\n    const response = data as Response;\n    // Note Since this function is not async, we currently can't load error message, just status\n    await checkResponse(response);\n    // TODO - bug in polyfill, body can be a Promise under Node.js\n    // eslint-disable-next-line @typescript-eslint/await-thenable\n    const body = await response.body;\n    // TODO - body can be null?\n    return makeIterator(body as ReadableStream<Uint8Array>, options as any);\n  }\n\n  if (isBlob(data) || isReadableStream(data)) {\n    return makeIterator(data as Blob | ReadableStream, options as any);\n  }\n\n  if (isAsyncIterable(data)) {\n    return data[Symbol.asyncIterator]();\n  }\n\n  return getIterableFromData(data);\n}\n\nexport async function getReadableStream(data: BatchableDataType): Promise<ReadableStream> {\n  if (isReadableStream(data)) {\n    return data as ReadableStream;\n  }\n  if (isResponse(data)) {\n    // @ts-ignore\n    return data.body;\n  }\n  const response = await makeResponse(data);\n  // @ts-ignore\n  return response.body;\n}\n\n// HELPERS\n\nfunction getIterableFromData(data) {\n  // generate an iterator that emits a single chunk\n  if (ArrayBuffer.isView(data)) {\n    return (function* oneChunk() {\n      yield data.buffer;\n    })();\n  }\n\n  if (data instanceof ArrayBuffer) {\n    return (function* oneChunk() {\n      yield data;\n    })();\n  }\n\n  if (isIterator(data)) {\n    return data;\n  }\n\n  if (isIterable(data)) {\n    return data[Symbol.iterator]();\n  }\n\n  throw new Error(ERR_DATA);\n}\n"], "mappings": ";;;;;;;;;AAOA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AASA,IAAAE,aAAA,GAAAF,OAAA;AACA,IAAAG,cAAA,GAAAH,OAAA;AAEA,MAAMI,QAAQ,GAAG,mCAAmC;AAG7C,SAASC,kCAAkCA,CAChDC,IAAkB,EAClBC,MAAc,EACdC,OAAsB,EACA;EACtB,IAAID,MAAM,CAACE,IAAI,IAAI,OAAOH,IAAI,KAAK,QAAQ,EAAE;IAC3C,OAAOA,IAAI;EACb;EAEA,IAAI,IAAAI,gBAAQ,EAACJ,IAAI,CAAC,EAAE;IAElBA,IAAI,GAAGA,IAAI,CAACK,MAAM;EACpB;EAEA,IAAIL,IAAI,YAAYM,WAAW,EAAE;IAC/B,MAAMC,WAAW,GAAGP,IAAI;IACxB,IAAIC,MAAM,CAACE,IAAI,IAAI,CAACF,MAAM,CAACO,MAAM,EAAE;MACjC,MAAMC,WAAW,GAAG,IAAIC,WAAW,CAAC,MAAM,CAAC;MAC3C,OAAOD,WAAW,CAACE,MAAM,CAACJ,WAAW,CAAC;IACxC;IACA,OAAOA,WAAW;EACpB;EAGA,IAAID,WAAW,CAACM,MAAM,CAACZ,IAAI,CAAC,EAAE;IAE5B,IAAIC,MAAM,CAACE,IAAI,IAAI,CAACF,MAAM,CAACO,MAAM,EAAE;MACjC,MAAMC,WAAW,GAAG,IAAIC,WAAW,CAAC,MAAM,CAAC;MAC3C,OAAOD,WAAW,CAACE,MAAM,CAACX,IAAI,CAAC;IACjC;IAEA,IAAIO,WAAW,GAAGP,IAAI,CAACK,MAAM;IAK7B,MAAMQ,UAAU,GAAGb,IAAI,CAACa,UAAU,IAAIb,IAAI,CAACc,MAAM;IACjD,IAAId,IAAI,CAACe,UAAU,KAAK,CAAC,IAAIF,UAAU,KAAKN,WAAW,CAACM,UAAU,EAAE;MAElEN,WAAW,GAAGA,WAAW,CAACS,KAAK,CAAChB,IAAI,CAACe,UAAU,EAAEf,IAAI,CAACe,UAAU,GAAGF,UAAU,CAAC;IAChF;IACA,OAAON,WAAW;EACpB;EAEA,MAAM,IAAIU,KAAK,CAACnB,QAAQ,CAAC;AAC3B;AAGO,eAAeoB,8BAA8BA,CAClDlB,IAAc,EACdC,MAAc,EACdC,OAAsB,EACS;EAC/B,MAAMiB,aAAa,GAAGnB,IAAI,YAAYM,WAAW,IAAIA,WAAW,CAACM,MAAM,CAACZ,IAAI,CAAC;EAC7E,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAImB,aAAa,EAAE;IAC7C,OAAOpB,kCAAkC,CAACC,IAAI,EAA0BC,MAAM,EAAEC,OAAO,CAAC;EAC1F;EAGA,IAAI,IAAAkB,cAAM,EAACpB,IAAI,CAAC,EAAE;IAChBA,IAAI,GAAG,MAAM,IAAAqB,2BAAY,EAACrB,IAAI,CAAC;EACjC;EAEA,IAAI,IAAAsB,kBAAU,EAACtB,IAAI,CAAC,EAAE;IACpB,MAAMuB,QAAQ,GAAGvB,IAAgB;IACjC,MAAM,IAAAwB,4BAAa,EAACD,QAAQ,CAAC;IAC7B,OAAOtB,MAAM,CAACO,MAAM,GAAG,MAAMe,QAAQ,CAAChB,WAAW,CAAC,CAAC,GAAG,MAAMgB,QAAQ,CAACpB,IAAI,CAAC,CAAC;EAC7E;EAEA,IAAI,IAAAsB,wBAAgB,EAACzB,IAAI,CAAC,EAAE;IAE1BA,IAAI,GAAG,IAAA0B,0BAAY,EAAC1B,IAAI,EAAoBE,OAAO,CAAC;EACtD;EAEA,IAAI,IAAAyB,kBAAU,EAAC3B,IAAI,CAAC,IAAI,IAAA4B,uBAAe,EAAC5B,IAAI,CAAC,EAAE;IAE7C,OAAO,IAAA6B,yCAA4B,EAAC7B,IAAkC,CAAC;EACzE;EAEA,MAAM,IAAIiB,KAAK,CAACnB,QAAQ,CAAC;AAC3B;AAEO,eAAegC,wBAAwBA,CAC5C9B,IAAuB,EACvBE,OAAsB,EACuC;EAC7D,IAAI,IAAA6B,kBAAU,EAAC/B,IAAI,CAAC,EAAE;IACpB,OAAOA,IAAI;EACb;EAEA,IAAI,IAAAsB,kBAAU,EAACtB,IAAI,CAAC,EAAE;IACpB,MAAMuB,QAAQ,GAAGvB,IAAgB;IAEjC,MAAM,IAAAwB,4BAAa,EAACD,QAAQ,CAAC;IAG7B,MAAMS,IAAI,GAAG,MAAMT,QAAQ,CAACS,IAAI;IAEhC,OAAO,IAAAN,0BAAY,EAACM,IAAI,EAAgC9B,OAAc,CAAC;EACzE;EAEA,IAAI,IAAAkB,cAAM,EAACpB,IAAI,CAAC,IAAI,IAAAyB,wBAAgB,EAACzB,IAAI,CAAC,EAAE;IAC1C,OAAO,IAAA0B,0BAAY,EAAC1B,IAAI,EAA2BE,OAAc,CAAC;EACpE;EAEA,IAAI,IAAA0B,uBAAe,EAAC5B,IAAI,CAAC,EAAE;IACzB,OAAOA,IAAI,CAACiC,MAAM,CAACC,aAAa,CAAC,CAAC,CAAC;EACrC;EAEA,OAAOC,mBAAmB,CAACnC,IAAI,CAAC;AAClC;AAEO,eAAeoC,iBAAiBA,CAACpC,IAAuB,EAA2B;EACxF,IAAI,IAAAyB,wBAAgB,EAACzB,IAAI,CAAC,EAAE;IAC1B,OAAOA,IAAI;EACb;EACA,IAAI,IAAAsB,kBAAU,EAACtB,IAAI,CAAC,EAAE;IAEpB,OAAOA,IAAI,CAACgC,IAAI;EAClB;EACA,MAAMT,QAAQ,GAAG,MAAM,IAAAF,2BAAY,EAACrB,IAAI,CAAC;EAEzC,OAAOuB,QAAQ,CAACS,IAAI;AACtB;AAIA,SAASG,mBAAmBA,CAACnC,IAAI,EAAE;EAEjC,IAAIM,WAAW,CAACM,MAAM,CAACZ,IAAI,CAAC,EAAE;IAC5B,OAAQ,UAAUqC,QAAQA,CAAA,EAAG;MAC3B,MAAMrC,IAAI,CAACK,MAAM;IACnB,CAAC,CAAE,CAAC;EACN;EAEA,IAAIL,IAAI,YAAYM,WAAW,EAAE;IAC/B,OAAQ,UAAU+B,QAAQA,CAAA,EAAG;MAC3B,MAAMrC,IAAI;IACZ,CAAC,CAAE,CAAC;EACN;EAEA,IAAI,IAAA+B,kBAAU,EAAC/B,IAAI,CAAC,EAAE;IACpB,OAAOA,IAAI;EACb;EAEA,IAAI,IAAA2B,kBAAU,EAAC3B,IAAI,CAAC,EAAE;IACpB,OAAOA,IAAI,CAACiC,MAAM,CAACK,QAAQ,CAAC,CAAC,CAAC;EAChC;EAEA,MAAM,IAAIrB,KAAK,CAACnB,QAAQ,CAAC;AAC3B"}