"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.parseMIMEType = parseMIMEType;
exports.parseMIMETypeFromURL = parseMIMETypeFromURL;
const DATA_URL_PATTERN = /^data:([-\w.]+\/[-\w.+]+)(;|,)/;
const MIME_TYPE_PATTERN = /^([-\w.]+\/[-\w.+]+)/;
function parseMIMEType(mimeString) {
  const matches = MIME_TYPE_PATTERN.exec(mimeString);
  if (matches) {
    return matches[1];
  }
  return mimeString;
}
function parseMIMETypeFromURL(url) {
  const matches = DATA_URL_PATTERN.exec(url);
  if (matches) {
    return matches[1];
  }
  return '';
}
//# sourceMappingURL=mime-type-utils.js.map