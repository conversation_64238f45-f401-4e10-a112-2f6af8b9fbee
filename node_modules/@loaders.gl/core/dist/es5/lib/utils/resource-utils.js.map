{"version": 3, "file": "resource-utils.js", "names": ["_isType", "require", "_mimeTypeUtils", "_urlUtils", "getResourceUrl", "resource", "isResponse", "response", "url", "isBlob", "blob", "name", "getResourceMIMEType", "contentTypeHeader", "headers", "get", "noQueryUrl", "stripQueryString", "parseMIMEType", "parseMIMETypeFromURL", "type", "getResourceContentLength", "size", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "byteLength", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["../../../../src/lib/utils/resource-utils.ts"], "sourcesContent": ["// loaders.gl, MIT license\n\nimport {isResponse, isBlob} from '../../javascript-utils/is-type';\nimport {parseMIMEType, parseMIMETypeFromURL} from './mime-type-utils';\nimport {stripQueryString} from './url-utils';\n\n/**\n * A loadable resource. Includes:\n * `Response`, `Blob` (`File` is a subclass), string URLs and data URLs\n */\nexport type Resource = Response | Blob | string;\n\n/**\n * Returns the URL associated with this resource.\n * The returned value may include a query string and need further processing.\n * If it cannot determine url, the corresponding value will be an empty string\n *\n * @todo string parameters are assumed to be URLs\n */\nexport function getResourceUrl(resource: unknown): string {\n  // If resource is a `Response`, it contains the information directly as a field\n  if (isResponse(resource)) {\n    const response = resource as Response;\n    return response.url;\n  }\n\n  // If the resource is a Blob or a File (subclass of Blob)\n  if (isBlob(resource)) {\n    const blob = resource as Blob;\n    // File objects have a \"name\" property. Blob objects don't have any\n    // url (name) information\n    return blob.name || '';\n  }\n\n  if (typeof resource === 'string') {\n    return resource;\n  }\n\n  // Unknown\n  return '';\n}\n\n/**\n * Returns the URL associated with this resource.\n * The returned value may include a query string and need further processing.\n * If it cannot determine url, the corresponding value will be an empty string\n *\n * @todo string parameters are assumed to be URLs\n */\nexport function getResourceMIMEType(resource: unknown): string {\n  // If resource is a response, it contains the information directly\n  if (isResponse(resource)) {\n    const response = resource as Response;\n    const contentTypeHeader = response.headers.get('content-type') || '';\n    const noQueryUrl = stripQueryString(response.url);\n    return parseMIMEType(contentTypeHeader) || parseMIMETypeFromURL(noQueryUrl);\n  }\n\n  // If the resource is a Blob or a File (subclass of Blob)\n  if (isBlob(resource)) {\n    const blob = resource as Blob;\n    return blob.type || '';\n  }\n\n  if (typeof resource === 'string') {\n    return parseMIMETypeFromURL(resource);\n  }\n\n  // Unknown\n  return '';\n}\n\n/**\n  * Returns (approximate) content length for a resource if it can be determined.\n  * Returns -1 if content length cannot be determined.\n  * @param resource\n\n  * @note string parameters are NOT assumed to be URLs\n  */\nexport function getResourceContentLength(resource: unknown): number {\n  if (isResponse(resource)) {\n    const response = resource as Response;\n    return response.headers['content-length'] || -1;\n  }\n  if (isBlob(resource)) {\n    const blob = resource as Blob;\n    return blob.size;\n  }\n  if (typeof resource === 'string') {\n    // TODO - handle data URL?\n    return resource.length;\n  }\n  if (resource instanceof ArrayBuffer) {\n    return resource.byteLength;\n  }\n  if (ArrayBuffer.isView(resource)) {\n    return resource.byteLength;\n  }\n  return -1;\n}\n"], "mappings": ";;;;;;;;AAEA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AAeO,SAASG,cAAcA,CAACC,QAAiB,EAAU;EAExD,IAAI,IAAAC,kBAAU,EAACD,QAAQ,CAAC,EAAE;IACxB,MAAME,QAAQ,GAAGF,QAAoB;IACrC,OAAOE,QAAQ,CAACC,GAAG;EACrB;EAGA,IAAI,IAAAC,cAAM,EAACJ,QAAQ,CAAC,EAAE;IACpB,MAAMK,IAAI,GAAGL,QAAgB;IAG7B,OAAOK,IAAI,CAACC,IAAI,IAAI,EAAE;EACxB;EAEA,IAAI,OAAON,QAAQ,KAAK,QAAQ,EAAE;IAChC,OAAOA,QAAQ;EACjB;EAGA,OAAO,EAAE;AACX;AASO,SAASO,mBAAmBA,CAACP,QAAiB,EAAU;EAE7D,IAAI,IAAAC,kBAAU,EAACD,QAAQ,CAAC,EAAE;IACxB,MAAME,QAAQ,GAAGF,QAAoB;IACrC,MAAMQ,iBAAiB,GAAGN,QAAQ,CAACO,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE;IACpE,MAAMC,UAAU,GAAG,IAAAC,0BAAgB,EAACV,QAAQ,CAACC,GAAG,CAAC;IACjD,OAAO,IAAAU,4BAAa,EAACL,iBAAiB,CAAC,IAAI,IAAAM,mCAAoB,EAACH,UAAU,CAAC;EAC7E;EAGA,IAAI,IAAAP,cAAM,EAACJ,QAAQ,CAAC,EAAE;IACpB,MAAMK,IAAI,GAAGL,QAAgB;IAC7B,OAAOK,IAAI,CAACU,IAAI,IAAI,EAAE;EACxB;EAEA,IAAI,OAAOf,QAAQ,KAAK,QAAQ,EAAE;IAChC,OAAO,IAAAc,mCAAoB,EAACd,QAAQ,CAAC;EACvC;EAGA,OAAO,EAAE;AACX;AASO,SAASgB,wBAAwBA,CAAChB,QAAiB,EAAU;EAClE,IAAI,IAAAC,kBAAU,EAACD,QAAQ,CAAC,EAAE;IACxB,MAAME,QAAQ,GAAGF,QAAoB;IACrC,OAAOE,QAAQ,CAACO,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;EACjD;EACA,IAAI,IAAAL,cAAM,EAACJ,QAAQ,CAAC,EAAE;IACpB,MAAMK,IAAI,GAAGL,QAAgB;IAC7B,OAAOK,IAAI,CAACY,IAAI;EAClB;EACA,IAAI,OAAOjB,QAAQ,KAAK,QAAQ,EAAE;IAEhC,OAAOA,QAAQ,CAACkB,MAAM;EACxB;EACA,IAAIlB,QAAQ,YAAYmB,WAAW,EAAE;IACnC,OAAOnB,QAAQ,CAACoB,UAAU;EAC5B;EACA,IAAID,WAAW,CAACE,MAAM,CAACrB,QAAQ,CAAC,EAAE;IAChC,OAAOA,QAAQ,CAACoB,UAAU;EAC5B;EACA,OAAO,CAAC,CAAC;AACX"}