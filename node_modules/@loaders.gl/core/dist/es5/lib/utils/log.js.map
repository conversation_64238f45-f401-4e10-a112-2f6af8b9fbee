{"version": 3, "file": "log.js", "names": ["_log", "require", "log", "Log", "id", "exports"], "sources": ["../../../../src/lib/utils/log.ts"], "sourcesContent": ["// loaders.gl, MIT license\nimport {Log} from '@probe.gl/log';\n\nexport const log = new Log({id: 'loaders.gl'});\n"], "mappings": ";;;;;;AACA,IAAAA,IAAA,GAAAC,OAAA;AAEO,MAAMC,GAAG,GAAG,IAAIC,QAAG,CAAC;EAACC,EAAE,EAAE;AAAY,CAAC,CAAC;AAACC,OAAA,CAAAH,GAAA,GAAAA,GAAA"}