{"version": 3, "file": "read-array-buffer.js", "names": ["read<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "file", "start", "length", "Blob", "slice", "arrayBuffer", "read"], "sources": ["../../../../src/lib/filesystems/read-array-buffer.ts"], "sourcesContent": ["// Random-Access read\n\nexport async function readArrayBuffer(\n  file: Blob | ArrayBuffer | any,\n  start: number,\n  length: number\n): Promise<ArrayBuffer> {\n  if (file instanceof Blob) {\n    const slice = file.slice(start, start + length);\n    return await slice.arrayBuffer();\n  }\n  return await file.read(start, start + length);\n}\n\n/**\n * Read a slice of a Blob or File, without loading the entire file into memory\n * The trick when reading File objects is to read successive \"slices\" of the File\n * Per spec https://w3c.github.io/FileAPI/, slicing a File only updates the start and end fields\n * Actually reading from file happens in `readAsArrayBuffer`\n * @param blob to read\n export async function readBlob(blob: Blob): Promise<ArrayBuffer> {\n  return await new Promise((resolve, reject) => {\n    const fileReader = new FileReader();\n    fileReader.onload = (event: ProgressEvent<FileReader>) =>\n      resolve(event?.target?.result as ArrayBuffer);\n    // TODO - reject with a proper Error\n    fileReader.onerror = (error: ProgressEvent<FileReader>) => reject(error);\n    fileReader.readAsArrayBuffer(blob);\n  });\n}\n*/\n"], "mappings": ";;;;;;AAEO,eAAeA,eAAeA,CACnCC,IAA8B,EAC9BC,KAAa,EACbC,MAAc,EACQ;EACtB,IAAIF,IAAI,YAAYG,IAAI,EAAE;IACxB,MAAMC,KAAK,GAAGJ,IAAI,CAACI,KAAK,CAACH,KAAK,EAAEA,KAAK,GAAGC,MAAM,CAAC;IAC/C,OAAO,MAAME,KAAK,CAACC,WAAW,CAAC,CAAC;EAClC;EACA,OAAO,MAAML,IAAI,CAACM,IAAI,CAACL,KAAK,EAAEA,KAAK,GAAGC,MAAM,CAAC;AAC/C"}