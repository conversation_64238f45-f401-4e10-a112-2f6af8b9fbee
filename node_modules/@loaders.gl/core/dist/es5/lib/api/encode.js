"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.encode = encode;
exports.encodeInBatches = encodeInBatches;
exports.encodeSync = encodeSync;
exports.encodeText = encodeText;
exports.encodeURLtoURL = encodeURLtoURL;
var _loaderUtils = require("@loaders.gl/loader-utils");
var _workerUtils = require("@loaders.gl/worker-utils");
var _writeFile = require("../fetch/write-file");
var _fetchFile = require("../fetch/fetch-file");
var _loaderOptions = require("./loader-options");
async function encode(data, writer, options) {
  const globalOptions = (0, _loaderOptions.getLoaderOptions)();
  options = {
    ...globalOptions,
    ...options
  };
  if ((0, _loaderUtils.canEncodeWithWorker)(writer, options)) {
    return await (0, _workerUtils.processOnWorker)(writer, data, options);
  }
  if (writer.encode) {
    return await writer.encode(data, options);
  }
  if (writer.encodeSync) {
    return writer.encodeSync(data, options);
  }
  if (writer.encodeText) {
    return new TextEncoder().encode(await writer.encodeText(data, options));
  }
  if (writer.encodeInBatches) {
    const batches = encodeInBatches(data, writer, options);
    const chunks = [];
    for await (const batch of batches) {
      chunks.push(batch);
    }
    return (0, _loaderUtils.concatenateArrayBuffers)(...chunks);
  }
  if (!_loaderUtils.isBrowser && writer.encodeURLtoURL) {
    const tmpInputFilename = getTemporaryFilename('input');
    await (0, _writeFile.writeFile)(tmpInputFilename, data);
    const tmpOutputFilename = getTemporaryFilename('output');
    const outputFilename = await encodeURLtoURL(tmpInputFilename, tmpOutputFilename, writer, options);
    const response = await (0, _fetchFile.fetchFile)(outputFilename);
    return response.arrayBuffer();
  }
  throw new Error('Writer could not encode data');
}
function encodeSync(data, writer, options) {
  if (writer.encodeSync) {
    return writer.encodeSync(data, options);
  }
  throw new Error('Writer could not synchronously encode data');
}
async function encodeText(data, writer, options) {
  if (writer.text && writer.encodeText) {
    return await writer.encodeText(data, options);
  }
  if (writer.text && (writer.encode || writer.encodeInBatches)) {
    const arrayBuffer = await encode(data, writer, options);
    return new TextDecoder().decode(arrayBuffer);
  }
  throw new Error('Writer could not encode data as text');
}
function encodeInBatches(data, writer, options) {
  if (writer.encodeInBatches) {
    const dataIterator = getIterator(data);
    return writer.encodeInBatches(dataIterator, options);
  }
  throw new Error('Writer could not encode data in batches');
}
async function encodeURLtoURL(inputUrl, outputUrl, writer, options) {
  inputUrl = (0, _loaderUtils.resolvePath)(inputUrl);
  outputUrl = (0, _loaderUtils.resolvePath)(outputUrl);
  if (_loaderUtils.isBrowser || !writer.encodeURLtoURL) {
    throw new Error();
  }
  const outputFilename = await writer.encodeURLtoURL(inputUrl, outputUrl, options);
  return outputFilename;
}
function getIterator(data) {
  const dataIterator = [{
    table: data,
    start: 0,
    end: data.length
  }];
  return dataIterator;
}
function getTemporaryFilename(filename) {
  return "/tmp/".concat(filename);
}
//# sourceMappingURL=encode.js.map