"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.parseInBatches = parseInBatches;
var _loaderUtils = require("@loaders.gl/loader-utils");
var _normalizeLoader = require("../loader-utils/normalize-loader");
var _optionUtils = require("../loader-utils/option-utils");
var _loaderContext = require("../loader-utils/loader-context");
var _getData = require("../loader-utils/get-data");
var _resourceUtils = require("../utils/resource-utils");
var _selectLoader = require("./select-loader");
var _parse = require("./parse");
async function parseInBatches(data, loaders, options, context) {
  (0, _loaderUtils.assert)(!context || typeof context === 'object');
  const loaderArray = Array.isArray(loaders) ? loaders : undefined;
  if (!Array.isArray(loaders) && !(0, _normalizeLoader.isLoaderObject)(loaders)) {
    context = undefined;
    options = loaders;
    loaders = undefined;
  }
  data = await data;
  options = options || {};
  const url = (0, _resourceUtils.getResourceUrl)(data);
  const loader = await (0, _selectLoader.selectLoader)(data, loaders, options);
  if (!loader) {
    return null;
  }
  options = (0, _optionUtils.normalizeOptions)(options, loader, loaderArray, url);
  context = (0, _loaderContext.getLoaderContext)({
    url,
    parseInBatches,
    parse: _parse.parse,
    loaders: loaderArray
  }, options, context || null);
  return await parseWithLoaderInBatches(loader, data, options, context);
}
async function parseWithLoaderInBatches(loader, data, options, context) {
  const outputIterator = await parseToOutputIterator(loader, data, options, context);
  if (!options.metadata) {
    return outputIterator;
  }
  const metadataBatch = {
    batchType: 'metadata',
    metadata: {
      _loader: loader,
      _context: context
    },
    data: [],
    bytesUsed: 0
  };
  async function* makeMetadataBatchIterator(iterator) {
    yield metadataBatch;
    yield* iterator;
  }
  return makeMetadataBatchIterator(outputIterator);
}
async function parseToOutputIterator(loader, data, options, context) {
  const inputIterator = await (0, _getData.getAsyncIterableFromData)(data, options);
  const transformedIterator = await applyInputTransforms(inputIterator, (options === null || options === void 0 ? void 0 : options.transforms) || []);
  if (loader.parseInBatches) {
    return loader.parseInBatches(transformedIterator, options, context);
  }
  async function* parseChunkInBatches() {
    const arrayBuffer = await (0, _loaderUtils.concatenateArrayBuffersAsync)(transformedIterator);
    const parsedData = await (0, _parse.parse)(arrayBuffer, loader, {
      ...options,
      mimeType: loader.mimeTypes[0]
    }, context);
    const batch = {
      mimeType: loader.mimeTypes[0],
      shape: Array.isArray(parsedData) ? 'row-table' : 'unknown',
      batchType: 'data',
      data: parsedData,
      length: Array.isArray(parsedData) ? parsedData.length : 1
    };
    yield batch;
  }
  return parseChunkInBatches();
}
async function applyInputTransforms(inputIterator) {
  let transforms = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];
  let iteratorChain = inputIterator;
  for await (const transformBatches of transforms) {
    iteratorChain = transformBatches(iteratorChain);
  }
  return iteratorChain;
}
//# sourceMappingURL=parse-in-batches.js.map