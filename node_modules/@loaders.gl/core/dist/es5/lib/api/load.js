"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.load = load;
var _isType = require("../../javascript-utils/is-type");
var _normalizeLoader = require("../loader-utils/normalize-loader");
var _getFetchFunction = require("../loader-utils/get-fetch-function");
var _parse = require("./parse");
async function load(url, loaders, options, context) {
  if (!Array.isArray(loaders) && !(0, _normalizeLoader.isLoaderObject)(loaders)) {
    context = undefined;
    options = loaders;
    loaders = undefined;
  }
  const fetch = (0, _getFetchFunction.getFetchFunction)(options);
  let data = url;
  if (typeof url === 'string') {
    data = await fetch(url);
  }
  if ((0, _isType.isBlob)(url)) {
    data = await fetch(url);
  }
  return await (0, _parse.parse)(data, loaders, options);
}
//# sourceMappingURL=load.js.map