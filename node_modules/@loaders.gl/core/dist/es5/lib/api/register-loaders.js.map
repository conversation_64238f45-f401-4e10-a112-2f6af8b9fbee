{"version": 3, "file": "register-loaders.js", "names": ["_normalize<PERSON><PERSON>der", "require", "_optionUtils", "getGlobalLoaderRegistry", "state", "getGlobalLoaderState", "loaderRegistry", "registerLoaders", "loaders", "Array", "isArray", "loader", "normalizedLoader", "normalize<PERSON><PERSON><PERSON>", "find", "registeredLoader", "unshift", "getRegisteredLoaders", "_unregisterLoaders"], "sources": ["../../../../src/lib/api/register-loaders.ts"], "sourcesContent": ["import {Loader} from '@loaders.gl/loader-utils';\nimport {normalizeLoader} from '../loader-utils/normalize-loader';\nimport {getGlobalLoaderState} from '../loader-utils/option-utils';\n\n// Store global registered loaders on the global object to increase chances of cross loaders-version interoperability\n// This use case is not reliable but can help when testing new versions of loaders.gl with existing frameworks\nconst getGlobalLoaderRegistry = () => {\n  const state = getGlobalLoaderState();\n  state.loaderRegistry = state.loaderRegistry || [];\n  return state.loaderRegistry;\n};\n\n/** Register a list of global loaders */\nexport function registerLoaders(loaders: Loader[] | Loader) {\n  const loaderRegistry = getGlobalLoaderRegistry();\n\n  loaders = Array.isArray(loaders) ? loaders : [loaders];\n\n  for (const loader of loaders) {\n    const normalizedLoader = normalizeLoader(loader);\n    if (!loaderRegistry.find((registeredLoader) => normalizedLoader === registeredLoader)) {\n      // add to the beginning of the loaderRegistry, so the last registeredLoader get picked\n      loaderRegistry.unshift(normalizedLoader);\n    }\n  }\n}\n\nexport function getRegisteredLoaders(): Loader[] {\n  return getGlobalLoaderRegistry();\n}\n\n/** @deprecated For testing only  */\nexport function _unregisterLoaders() {\n  const state = getGlobalLoaderState();\n  state.loaderRegistry = [];\n}\n"], "mappings": ";;;;;;;;AACA,IAAAA,gBAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAIA,MAAME,uBAAuB,GAAGA,CAAA,KAAM;EACpC,MAAMC,KAAK,GAAG,IAAAC,iCAAoB,EAAC,CAAC;EACpCD,KAAK,CAACE,cAAc,GAAGF,KAAK,CAACE,cAAc,IAAI,EAAE;EACjD,OAAOF,KAAK,CAACE,cAAc;AAC7B,CAAC;AAGM,SAASC,eAAeA,CAACC,OAA0B,EAAE;EAC1D,MAAMF,cAAc,GAAGH,uBAAuB,CAAC,CAAC;EAEhDK,OAAO,GAAGC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,GAAGA,OAAO,GAAG,CAACA,OAAO,CAAC;EAEtD,KAAK,MAAMG,MAAM,IAAIH,OAAO,EAAE;IAC5B,MAAMI,gBAAgB,GAAG,IAAAC,gCAAe,EAACF,MAAM,CAAC;IAChD,IAAI,CAACL,cAAc,CAACQ,IAAI,CAAEC,gBAAgB,IAAKH,gBAAgB,KAAKG,gBAAgB,CAAC,EAAE;MAErFT,cAAc,CAACU,OAAO,CAACJ,gBAAgB,CAAC;IAC1C;EACF;AACF;AAEO,SAASK,oBAAoBA,CAAA,EAAa;EAC/C,OAAOd,uBAAuB,CAAC,CAAC;AAClC;AAGO,SAASe,kBAAkBA,CAAA,EAAG;EACnC,MAAMd,KAAK,GAAG,IAAAC,iCAAoB,EAAC,CAAC;EACpCD,KAAK,CAACE,cAAc,GAAG,EAAE;AAC3B"}