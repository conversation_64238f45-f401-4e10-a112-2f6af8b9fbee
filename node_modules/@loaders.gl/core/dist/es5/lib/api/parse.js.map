{"version": 3, "file": "parse.js", "names": ["_workerUtils", "require", "_loaderUtils", "_normalize<PERSON><PERSON>der", "_isType", "_optionUtils", "_getData", "_loaderContext", "_resourceUtils", "_select<PERSON><PERSON><PERSON>", "parse", "data", "loaders", "options", "context", "assert", "Array", "isArray", "isLoaderObject", "undefined", "url", "getResourceUrl", "typed<PERSON>oa<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "getLoadersFromContext", "loader", "<PERSON><PERSON><PERSON><PERSON>", "normalizeOptions", "getLoaderContext", "parseWith<PERSON><PERSON>der", "validateWorkerVersion", "isResponse", "response", "ok", "redirected", "status", "statusText", "type", "headers", "Object", "fromEntries", "entries", "getArrayBufferOrStringFromData", "parseTextSync", "dataType", "canParseWithWorker", "parseWithWorker", "parseText", "parseSync", "Error", "concat", "id"], "sources": ["../../../../src/lib/api/parse.ts"], "sourcesContent": ["import type {<PERSON>Type, Loader, LoaderContext, LoaderOptions} from '@loaders.gl/loader-utils';\nimport {assert, validateWorkerVersion} from '@loaders.gl/worker-utils';\nimport {parseWithWorker, canParseWithWorker} from '@loaders.gl/loader-utils';\nimport {isLoaderObject} from '../loader-utils/normalize-loader';\nimport {isResponse} from '../../javascript-utils/is-type';\nimport {normalizeOptions} from '../loader-utils/option-utils';\nimport {getArrayBufferOrStringFromData} from '../loader-utils/get-data';\nimport {getLoaderContext, getLoadersFromContext} from '../loader-utils/loader-context';\nimport {getResourceUrl} from '../utils/resource-utils';\nimport {selectLoader} from './select-loader';\n\n/**\n * Parses `data` using a specified loader\n * @param data\n * @param loaders\n * @param options\n * @param context\n */\nexport async function parse(\n  data: DataType | Promise<DataType>,\n  loaders?: Loader | Loader[] | LoaderOptions,\n  options?: LoaderOptions,\n  context?: LoaderContext\n): Promise<any> {\n  assert(!context || typeof context === 'object'); // parse no longer accepts final url\n\n  // Signature: parse(data, options, context | url)\n  // Uses registered loaders\n  if (loaders && !Array.isArray(loaders) && !isLoaderObject(loaders)) {\n    context = undefined; // context not supported in short signature\n    options = loaders as LoaderOptions;\n    loaders = undefined;\n  }\n\n  data = await data; // Resolve any promise\n  options = options || {};\n\n  // Extract a url for auto detection\n  const url = getResourceUrl(data);\n\n  // Chooses a loader (and normalizes it)\n  // Also use any loaders in the context, new loaders take priority\n  const typedLoaders = loaders as Loader | Loader[] | undefined;\n  const candidateLoaders = getLoadersFromContext(typedLoaders, context);\n  // todo hacky type cast\n  const loader = await selectLoader(data as ArrayBuffer, candidateLoaders, options);\n  // Note: if no loader was found, if so just return null\n  if (!loader) {\n    return null;\n  }\n\n  // Normalize options\n  options = normalizeOptions(options, loader, candidateLoaders, url);\n\n  // Get a context (if already present, will be unchanged)\n  context = getLoaderContext({url, parse, loaders: candidateLoaders}, options, context || null);\n\n  return await parseWithLoader(loader, data, options, context);\n}\n\n// TODO: support progress and abort\n// TODO - should accept loader.parseAsyncIterator and concatenate.\nasync function parseWithLoader(loader, data, options, context) {\n  validateWorkerVersion(loader);\n\n  if (isResponse(data)) {\n    // Serialize to support passing the response to web worker\n    const response = data as Response;\n    const {ok, redirected, status, statusText, type, url} = response;\n    const headers = Object.fromEntries(response.headers.entries());\n    context.response = {headers, ok, redirected, status, statusText, type, url};\n  }\n\n  data = await getArrayBufferOrStringFromData(data, loader, options);\n\n  // First check for synchronous text parser, wrap results in promises\n  if (loader.parseTextSync && typeof data === 'string') {\n    options.dataType = 'text';\n    return loader.parseTextSync(data, options, context, loader);\n  }\n\n  // If we have a workerUrl and the loader can parse the given options efficiently in a worker\n  if (canParseWithWorker(loader, options)) {\n    return await parseWithWorker(loader, data, options, context, parse);\n  }\n\n  // Check for asynchronous parser\n  if (loader.parseText && typeof data === 'string') {\n    return await loader.parseText(data, options, context, loader);\n  }\n\n  if (loader.parse) {\n    return await loader.parse(data, options, context, loader);\n  }\n\n  // This should not happen, all sync loaders should also offer `parse` function\n  assert(!loader.parseSync);\n\n  // TBD - If asynchronous parser not available, return null\n  throw new Error(`${loader.id} loader - no parser found and worker is disabled`);\n}\n"], "mappings": ";;;;;;AACA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,gBAAA,GAAAF,OAAA;AACA,IAAAG,OAAA,GAAAH,OAAA;AACA,IAAAI,YAAA,GAAAJ,OAAA;AACA,IAAAK,QAAA,GAAAL,OAAA;AACA,IAAAM,cAAA,GAAAN,OAAA;AACA,IAAAO,cAAA,GAAAP,OAAA;AACA,IAAAQ,aAAA,GAAAR,OAAA;AASO,eAAeS,KAAKA,CACzBC,IAAkC,EAClCC,OAA2C,EAC3CC,OAAuB,EACvBC,OAAuB,EACT;EACd,IAAAC,mBAAM,EAAC,CAACD,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,CAAC;EAI/C,IAAIF,OAAO,IAAI,CAACI,KAAK,CAACC,OAAO,CAACL,OAAO,CAAC,IAAI,CAAC,IAAAM,+BAAc,EAACN,OAAO,CAAC,EAAE;IAClEE,OAAO,GAAGK,SAAS;IACnBN,OAAO,GAAGD,OAAwB;IAClCA,OAAO,GAAGO,SAAS;EACrB;EAEAR,IAAI,GAAG,MAAMA,IAAI;EACjBE,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAGvB,MAAMO,GAAG,GAAG,IAAAC,6BAAc,EAACV,IAAI,CAAC;EAIhC,MAAMW,YAAY,GAAGV,OAAwC;EAC7D,MAAMW,gBAAgB,GAAG,IAAAC,oCAAqB,EAACF,YAAY,EAAER,OAAO,CAAC;EAErE,MAAMW,MAAM,GAAG,MAAM,IAAAC,0BAAY,EAACf,IAAI,EAAiBY,gBAAgB,EAAEV,OAAO,CAAC;EAEjF,IAAI,CAACY,MAAM,EAAE;IACX,OAAO,IAAI;EACb;EAGAZ,OAAO,GAAG,IAAAc,6BAAgB,EAACd,OAAO,EAAEY,MAAM,EAAEF,gBAAgB,EAAEH,GAAG,CAAC;EAGlEN,OAAO,GAAG,IAAAc,+BAAgB,EAAC;IAACR,GAAG;IAAEV,KAAK;IAAEE,OAAO,EAAEW;EAAgB,CAAC,EAAEV,OAAO,EAAEC,OAAO,IAAI,IAAI,CAAC;EAE7F,OAAO,MAAMe,eAAe,CAACJ,MAAM,EAAEd,IAAI,EAAEE,OAAO,EAAEC,OAAO,CAAC;AAC9D;AAIA,eAAee,eAAeA,CAACJ,MAAM,EAAEd,IAAI,EAAEE,OAAO,EAAEC,OAAO,EAAE;EAC7D,IAAAgB,kCAAqB,EAACL,MAAM,CAAC;EAE7B,IAAI,IAAAM,kBAAU,EAACpB,IAAI,CAAC,EAAE;IAEpB,MAAMqB,QAAQ,GAAGrB,IAAgB;IACjC,MAAM;MAACsB,EAAE;MAAEC,UAAU;MAAEC,MAAM;MAAEC,UAAU;MAAEC,IAAI;MAAEjB;IAAG,CAAC,GAAGY,QAAQ;IAChE,MAAMM,OAAO,GAAGC,MAAM,CAACC,WAAW,CAACR,QAAQ,CAACM,OAAO,CAACG,OAAO,CAAC,CAAC,CAAC;IAC9D3B,OAAO,CAACkB,QAAQ,GAAG;MAACM,OAAO;MAAEL,EAAE;MAAEC,UAAU;MAAEC,MAAM;MAAEC,UAAU;MAAEC,IAAI;MAAEjB;IAAG,CAAC;EAC7E;EAEAT,IAAI,GAAG,MAAM,IAAA+B,uCAA8B,EAAC/B,IAAI,EAAEc,MAAM,EAAEZ,OAAO,CAAC;EAGlE,IAAIY,MAAM,CAACkB,aAAa,IAAI,OAAOhC,IAAI,KAAK,QAAQ,EAAE;IACpDE,OAAO,CAAC+B,QAAQ,GAAG,MAAM;IACzB,OAAOnB,MAAM,CAACkB,aAAa,CAAChC,IAAI,EAAEE,OAAO,EAAEC,OAAO,EAAEW,MAAM,CAAC;EAC7D;EAGA,IAAI,IAAAoB,+BAAkB,EAACpB,MAAM,EAAEZ,OAAO,CAAC,EAAE;IACvC,OAAO,MAAM,IAAAiC,4BAAe,EAACrB,MAAM,EAAEd,IAAI,EAAEE,OAAO,EAAEC,OAAO,EAAEJ,KAAK,CAAC;EACrE;EAGA,IAAIe,MAAM,CAACsB,SAAS,IAAI,OAAOpC,IAAI,KAAK,QAAQ,EAAE;IAChD,OAAO,MAAMc,MAAM,CAACsB,SAAS,CAACpC,IAAI,EAAEE,OAAO,EAAEC,OAAO,EAAEW,MAAM,CAAC;EAC/D;EAEA,IAAIA,MAAM,CAACf,KAAK,EAAE;IAChB,OAAO,MAAMe,MAAM,CAACf,KAAK,CAACC,IAAI,EAAEE,OAAO,EAAEC,OAAO,EAAEW,MAAM,CAAC;EAC3D;EAGA,IAAAV,mBAAM,EAAC,CAACU,MAAM,CAACuB,SAAS,CAAC;EAGzB,MAAM,IAAIC,KAAK,IAAAC,MAAA,CAAIzB,MAAM,CAAC0B,EAAE,qDAAkD,CAAC;AACjF"}