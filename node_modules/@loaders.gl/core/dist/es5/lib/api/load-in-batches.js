"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.loadInBatches = loadInBatches;
var _normalizeLoader = require("../loader-utils/normalize-loader");
var _getFetchFunction = require("../loader-utils/get-fetch-function");
var _parseInBatches = require("./parse-in-batches");
function loadInBatches(files, loaders, options, context) {
  if (!Array.isArray(loaders) && !(0, _normalizeLoader.isLoaderObject)(loaders)) {
    context = undefined;
    options = loaders;
    loaders = null;
  }
  const fetch = (0, _getFetchFunction.getFetchFunction)(options || {});
  if (!Array.isArray(files)) {
    return loadOneFileInBatches(files, loaders, options, fetch);
  }
  const promises = files.map(file => loadOneFileInBatches(file, loaders, options, fetch));
  return promises;
}
async function loadOneFileInBatches(file, loaders, options, fetch) {
  if (typeof file === 'string') {
    const url = file;
    const response = await fetch(url);
    return await (0, _parseInBatches.parseInBatches)(response, loaders, options);
  }
  return await (0, _parseInBatches.parseInBatches)(file, loaders, options);
}
//# sourceMappingURL=load-in-batches.js.map