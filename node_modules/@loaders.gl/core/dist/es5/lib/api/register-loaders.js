"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports._unregisterLoaders = _unregisterLoaders;
exports.getRegisteredLoaders = getRegisteredLoaders;
exports.registerLoaders = registerLoaders;
var _normalizeLoader = require("../loader-utils/normalize-loader");
var _optionUtils = require("../loader-utils/option-utils");
const getGlobalLoaderRegistry = () => {
  const state = (0, _optionUtils.getGlobalLoaderState)();
  state.loaderRegistry = state.loaderRegistry || [];
  return state.loaderRegistry;
};
function registerLoaders(loaders) {
  const loaderRegistry = getGlobalLoaderRegistry();
  loaders = Array.isArray(loaders) ? loaders : [loaders];
  for (const loader of loaders) {
    const normalizedLoader = (0, _normalizeLoader.normalizeLoader)(loader);
    if (!loaderRegistry.find(registeredLoader => normalizedLoader === registeredLoader)) {
      loaderRegistry.unshift(normalizedLoader);
    }
  }
}
function getRegisteredLoaders() {
  return getGlobalLoaderRegistry();
}
function _unregisterLoaders() {
  const state = (0, _optionUtils.getGlobalLoaderState)();
  state.loaderRegistry = [];
}
//# sourceMappingURL=register-loaders.js.map