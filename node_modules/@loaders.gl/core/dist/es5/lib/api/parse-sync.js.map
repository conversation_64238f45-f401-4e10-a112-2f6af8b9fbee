{"version": 3, "file": "parse-sync.js", "names": ["_loaderUtils", "require", "_select<PERSON><PERSON><PERSON>", "_normalize<PERSON><PERSON>der", "_optionUtils", "_getData", "_loaderContext", "_resourceUtils", "parseSync", "data", "loaders", "options", "context", "assert", "Array", "isArray", "isLoaderObject", "undefined", "typed<PERSON>oa<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "getLoadersFromContext", "loader", "selectLoaderSync", "normalizeOptions", "url", "getResourceUrl", "parse", "Error", "getLoaderContext", "parseWithLoaderSync", "getArrayBufferOrStringFromDataSync", "parseTextSync", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "concat", "name"], "sources": ["../../../../src/lib/api/parse-sync.ts"], "sourcesContent": ["import type {\n  SyncD<PERSON>Type,\n  Loader,\n  LoaderWithParser,\n  LoaderContext,\n  LoaderOptions\n} from '@loaders.gl/loader-utils';\nimport {assert} from '@loaders.gl/loader-utils';\nimport {selectLoaderSync} from './select-loader';\nimport {isLoaderObject} from '../loader-utils/normalize-loader';\nimport {normalizeOptions} from '../loader-utils/option-utils';\nimport {getArrayBufferOrStringFromDataSync} from '../loader-utils/get-data';\nimport {getLoaderContext, getLoadersFromContext} from '../loader-utils/loader-context';\nimport {getResourceUrl} from '../utils/resource-utils';\n\n/**\n * Parses `data` synchronously using a specified loader\n * @param data\n * @param loaders\n * @param options\n * @param context\n */\nexport function parseSync(\n  data: SyncDataType,\n  loaders?: Loader | Loader[] | LoaderOptions,\n  options?: LoaderOptions,\n  context?: LoaderContext\n): any {\n  assert(!context || typeof context === 'object'); // parseSync no longer accepts final url\n\n  // Signature: parseSync(data, options)\n  // Uses registered loaders\n  if (!Array.isArray(loaders) && !isLoaderObject(loaders)) {\n    context = undefined; // context not supported in short signature\n    options = loaders as LoaderOptions;\n    loaders = undefined;\n  }\n\n  options = options || {};\n\n  // Chooses a loader (and normalizes it)\n  // Also use any loaders in the context, new loaders take priority\n  const typedLoaders = loaders as Loader | Loader[] | undefined;\n  const candidateLoaders = getLoadersFromContext(typedLoaders, context);\n  const loader = selectLoaderSync(data, candidateLoaders, options);\n  // Note: if nothrow option was set, it is possible that no loader was found, if so just return null\n  if (!loader) {\n    return null;\n  }\n\n  // Normalize options\n  options = normalizeOptions(options, loader, candidateLoaders);\n\n  // Extract a url for auto detection\n  const url = getResourceUrl(data);\n\n  const parse = () => {\n    throw new Error('parseSync called parse (which is async');\n  };\n  context = getLoaderContext(\n    {url, parseSync, parse, loaders: loaders as Loader[]},\n    options,\n    context || null\n  );\n\n  return parseWithLoaderSync(loader as LoaderWithParser, data, options, context);\n}\n\n// TODO - should accept loader.parseSync/parse and generate 1 chunk asyncIterator\nfunction parseWithLoaderSync(\n  loader: LoaderWithParser,\n  data: SyncDataType,\n  options: LoaderOptions,\n  context: LoaderContext\n) {\n  data = getArrayBufferOrStringFromDataSync(data, loader, options);\n\n  if (loader.parseTextSync && typeof data === 'string') {\n    return loader.parseTextSync(data, options); // , context, loader);\n  }\n\n  if (loader.parseSync && data instanceof ArrayBuffer) {\n    return loader.parseSync(data, options, context); // , loader);\n  }\n\n  // TBD - If synchronous parser not available, return null\n  throw new Error(\n    `${loader.name} loader: 'parseSync' not supported by this loader, use 'parse' instead. ${\n      context.url || ''\n    }`\n  );\n}\n"], "mappings": ";;;;;;AAOA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,aAAA,GAAAD,OAAA;AACA,IAAAE,gBAAA,GAAAF,OAAA;AACA,IAAAG,YAAA,GAAAH,OAAA;AACA,IAAAI,QAAA,GAAAJ,OAAA;AACA,IAAAK,cAAA,GAAAL,OAAA;AACA,IAAAM,cAAA,GAAAN,OAAA;AASO,SAASO,SAASA,CACvBC,IAAkB,EAClBC,OAA2C,EAC3CC,OAAuB,EACvBC,OAAuB,EAClB;EACL,IAAAC,mBAAM,EAAC,CAACD,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,CAAC;EAI/C,IAAI,CAACE,KAAK,CAACC,OAAO,CAACL,OAAO,CAAC,IAAI,CAAC,IAAAM,+BAAc,EAACN,OAAO,CAAC,EAAE;IACvDE,OAAO,GAAGK,SAAS;IACnBN,OAAO,GAAGD,OAAwB;IAClCA,OAAO,GAAGO,SAAS;EACrB;EAEAN,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAIvB,MAAMO,YAAY,GAAGR,OAAwC;EAC7D,MAAMS,gBAAgB,GAAG,IAAAC,oCAAqB,EAACF,YAAY,EAAEN,OAAO,CAAC;EACrE,MAAMS,MAAM,GAAG,IAAAC,8BAAgB,EAACb,IAAI,EAAEU,gBAAgB,EAAER,OAAO,CAAC;EAEhE,IAAI,CAACU,MAAM,EAAE;IACX,OAAO,IAAI;EACb;EAGAV,OAAO,GAAG,IAAAY,6BAAgB,EAACZ,OAAO,EAAEU,MAAM,EAAEF,gBAAgB,CAAC;EAG7D,MAAMK,GAAG,GAAG,IAAAC,6BAAc,EAAChB,IAAI,CAAC;EAEhC,MAAMiB,KAAK,GAAGA,CAAA,KAAM;IAClB,MAAM,IAAIC,KAAK,CAAC,wCAAwC,CAAC;EAC3D,CAAC;EACDf,OAAO,GAAG,IAAAgB,+BAAgB,EACxB;IAACJ,GAAG;IAAEhB,SAAS;IAAEkB,KAAK;IAAEhB,OAAO,EAAEA;EAAmB,CAAC,EACrDC,OAAO,EACPC,OAAO,IAAI,IACb,CAAC;EAED,OAAOiB,mBAAmB,CAACR,MAAM,EAAsBZ,IAAI,EAAEE,OAAO,EAAEC,OAAO,CAAC;AAChF;AAGA,SAASiB,mBAAmBA,CAC1BR,MAAwB,EACxBZ,IAAkB,EAClBE,OAAsB,EACtBC,OAAsB,EACtB;EACAH,IAAI,GAAG,IAAAqB,2CAAkC,EAACrB,IAAI,EAAEY,MAAM,EAAEV,OAAO,CAAC;EAEhE,IAAIU,MAAM,CAACU,aAAa,IAAI,OAAOtB,IAAI,KAAK,QAAQ,EAAE;IACpD,OAAOY,MAAM,CAACU,aAAa,CAACtB,IAAI,EAAEE,OAAO,CAAC;EAC5C;EAEA,IAAIU,MAAM,CAACb,SAAS,IAAIC,IAAI,YAAYuB,WAAW,EAAE;IACnD,OAAOX,MAAM,CAACb,SAAS,CAACC,IAAI,EAAEE,OAAO,EAAEC,OAAO,CAAC;EACjD;EAGA,MAAM,IAAIe,KAAK,IAAAM,MAAA,CACVZ,MAAM,CAACa,IAAI,8EAAAD,MAAA,CACZrB,OAAO,CAACY,GAAG,IAAI,EAAE,CAErB,CAAC;AACH"}