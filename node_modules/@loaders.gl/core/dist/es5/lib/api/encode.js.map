{"version": 3, "file": "encode.js", "names": ["_loaderUtils", "require", "_workerUtils", "_writeFile", "_fetchFile", "_loaderOptions", "encode", "data", "writer", "options", "globalOptions", "getLoaderOptions", "canEncodeWithWorker", "processOnWorker", "encodeSync", "encodeText", "TextEncoder", "encodeInBatches", "batches", "chunks", "batch", "push", "concatenateArrayBuffers", "<PERSON><PERSON><PERSON><PERSON>", "encodeURLtoURL", "tmpInputFilename", "getTemporaryFilename", "writeFile", "tmpOutputFilename", "outputFilename", "response", "fetchFile", "arrayBuffer", "Error", "text", "TextDecoder", "decode", "dataIterator", "getIterator", "inputUrl", "outputUrl", "<PERSON><PERSON><PERSON>", "table", "start", "end", "length", "filename", "concat"], "sources": ["../../../../src/lib/api/encode.ts"], "sourcesContent": ["import {Writer, WriterOptions, canEncodeWithWorker} from '@loaders.gl/loader-utils';\nimport {processOnWorker} from '@loaders.gl/worker-utils';\nimport {concatenateArrayBuffers, resolvePath} from '@loaders.gl/loader-utils';\nimport {isBrowser} from '@loaders.gl/loader-utils';\nimport {writeFile} from '../fetch/write-file';\nimport {fetchFile} from '../fetch/fetch-file';\nimport {getLoaderOptions} from './loader-options';\n\n/**\n * Encode loaded data into a binary ArrayBuffer using the specified Writer.\n */\nexport async function encode(\n  data: any,\n  writer: Writer,\n  options?: WriterOptions\n): Promise<ArrayBuffer> {\n  const globalOptions = getLoaderOptions() as WriterOptions;\n  // const globalOptions: WriterOptions = {}; // getWriterOptions();\n  options = {...globalOptions, ...options};\n  if (canEncodeWithWorker(writer, options)) {\n    return await processOn<PERSON>orker(writer, data, options);\n  }\n\n  // TODO Merge default writer options with options argument like it is done in load module.\n  if (writer.encode) {\n    return await writer.encode(data, options);\n  }\n\n  if (writer.encodeSync) {\n    return writer.encodeSync(data, options);\n  }\n\n  if (writer.encodeText) {\n    return new TextEncoder().encode(await writer.encodeText(data, options));\n  }\n\n  if (writer.encodeInBatches) {\n    // Create an iterator representing the data\n    // TODO - Assumes this is a table\n    const batches = encodeInBatches(data, writer, options);\n\n    // Concatenate the output\n    const chunks: any[] = [];\n    for await (const batch of batches) {\n      chunks.push(batch);\n    }\n    // @ts-ignore\n    return concatenateArrayBuffers(...chunks);\n  }\n\n  if (!isBrowser && writer.encodeURLtoURL) {\n    // TODO - how to generate filenames with correct extensions?\n    const tmpInputFilename = getTemporaryFilename('input');\n    await writeFile(tmpInputFilename, data);\n\n    const tmpOutputFilename = getTemporaryFilename('output');\n\n    const outputFilename = await encodeURLtoURL(\n      tmpInputFilename,\n      tmpOutputFilename,\n      writer,\n      options\n    );\n\n    const response = await fetchFile(outputFilename);\n    return response.arrayBuffer();\n  }\n\n  throw new Error('Writer could not encode data');\n}\n\n/**\n * Encode loaded data into a binary ArrayBuffer using the specified Writer.\n */\nexport function encodeSync(data: any, writer: Writer, options?: WriterOptions): ArrayBuffer {\n  if (writer.encodeSync) {\n    return writer.encodeSync(data, options);\n  }\n  throw new Error('Writer could not synchronously encode data');\n}\n\n/**\n * Encode loaded data to text using the specified Writer\n * @note This is a convenience function not intended for production use on large input data.\n * It is not optimized for performance. Data maybe converted from text to binary and back.\n * @throws if the writer does not generate text output\n */\nexport async function encodeText(\n  data: any,\n  writer: Writer,\n  options?: WriterOptions\n): Promise<string> {\n  if (writer.text && writer.encodeText) {\n    return await writer.encodeText(data, options);\n  }\n\n  if (writer.text && (writer.encode || writer.encodeInBatches)) {\n    const arrayBuffer = await encode(data, writer, options);\n    return new TextDecoder().decode(arrayBuffer);\n  }\n\n  throw new Error('Writer could not encode data as text');\n}\n\n/**\n * Encode loaded data into a sequence (iterator) of binary ArrayBuffers using the specified Writer.\n */\nexport function encodeInBatches(\n  data: any,\n  writer: Writer,\n  options?: WriterOptions\n): AsyncIterable<ArrayBuffer> {\n  if (writer.encodeInBatches) {\n    const dataIterator = getIterator(data);\n    return writer.encodeInBatches(dataIterator, options);\n  }\n  // TODO -fall back to atomic encode?\n  throw new Error('Writer could not encode data in batches');\n}\n\n/**\n * Encode data stored in a file (on disk) to another file.\n * @note Node.js only. This function enables using command-line converters as \"writers\".\n */\nexport async function encodeURLtoURL(\n  inputUrl,\n  outputUrl,\n  writer: Writer,\n  options\n): Promise<string> {\n  inputUrl = resolvePath(inputUrl);\n  outputUrl = resolvePath(outputUrl);\n  if (isBrowser || !writer.encodeURLtoURL) {\n    throw new Error();\n  }\n  const outputFilename = await writer.encodeURLtoURL(inputUrl, outputUrl, options);\n  return outputFilename;\n}\n\n/**\n * @todo TODO - this is an unacceptable hack!!!\n */\nfunction getIterator(data) {\n  const dataIterator = [{table: data, start: 0, end: data.length}];\n  return dataIterator;\n}\n\n/**\n * @todo Move to utils\n */\nfunction getTemporaryFilename(filename: string): string {\n  return `/tmp/${filename}`;\n}\n"], "mappings": ";;;;;;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAGA,IAAAE,UAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AACA,IAAAI,cAAA,GAAAJ,OAAA;AAKO,eAAeK,MAAMA,CAC1BC,IAAS,EACTC,MAAc,EACdC,OAAuB,EACD;EACtB,MAAMC,aAAa,GAAG,IAAAC,+BAAgB,EAAC,CAAkB;EAEzDF,OAAO,GAAG;IAAC,GAAGC,aAAa;IAAE,GAAGD;EAAO,CAAC;EACxC,IAAI,IAAAG,gCAAmB,EAACJ,MAAM,EAAEC,OAAO,CAAC,EAAE;IACxC,OAAO,MAAM,IAAAI,4BAAe,EAACL,MAAM,EAAED,IAAI,EAAEE,OAAO,CAAC;EACrD;EAGA,IAAID,MAAM,CAACF,MAAM,EAAE;IACjB,OAAO,MAAME,MAAM,CAACF,MAAM,CAACC,IAAI,EAAEE,OAAO,CAAC;EAC3C;EAEA,IAAID,MAAM,CAACM,UAAU,EAAE;IACrB,OAAON,MAAM,CAACM,UAAU,CAACP,IAAI,EAAEE,OAAO,CAAC;EACzC;EAEA,IAAID,MAAM,CAACO,UAAU,EAAE;IACrB,OAAO,IAAIC,WAAW,CAAC,CAAC,CAACV,MAAM,CAAC,MAAME,MAAM,CAACO,UAAU,CAACR,IAAI,EAAEE,OAAO,CAAC,CAAC;EACzE;EAEA,IAAID,MAAM,CAACS,eAAe,EAAE;IAG1B,MAAMC,OAAO,GAAGD,eAAe,CAACV,IAAI,EAAEC,MAAM,EAAEC,OAAO,CAAC;IAGtD,MAAMU,MAAa,GAAG,EAAE;IACxB,WAAW,MAAMC,KAAK,IAAIF,OAAO,EAAE;MACjCC,MAAM,CAACE,IAAI,CAACD,KAAK,CAAC;IACpB;IAEA,OAAO,IAAAE,oCAAuB,EAAC,GAAGH,MAAM,CAAC;EAC3C;EAEA,IAAI,CAACI,sBAAS,IAAIf,MAAM,CAACgB,cAAc,EAAE;IAEvC,MAAMC,gBAAgB,GAAGC,oBAAoB,CAAC,OAAO,CAAC;IACtD,MAAM,IAAAC,oBAAS,EAACF,gBAAgB,EAAElB,IAAI,CAAC;IAEvC,MAAMqB,iBAAiB,GAAGF,oBAAoB,CAAC,QAAQ,CAAC;IAExD,MAAMG,cAAc,GAAG,MAAML,cAAc,CACzCC,gBAAgB,EAChBG,iBAAiB,EACjBpB,MAAM,EACNC,OACF,CAAC;IAED,MAAMqB,QAAQ,GAAG,MAAM,IAAAC,oBAAS,EAACF,cAAc,CAAC;IAChD,OAAOC,QAAQ,CAACE,WAAW,CAAC,CAAC;EAC/B;EAEA,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;AACjD;AAKO,SAASnB,UAAUA,CAACP,IAAS,EAAEC,MAAc,EAAEC,OAAuB,EAAe;EAC1F,IAAID,MAAM,CAACM,UAAU,EAAE;IACrB,OAAON,MAAM,CAACM,UAAU,CAACP,IAAI,EAAEE,OAAO,CAAC;EACzC;EACA,MAAM,IAAIwB,KAAK,CAAC,4CAA4C,CAAC;AAC/D;AAQO,eAAelB,UAAUA,CAC9BR,IAAS,EACTC,MAAc,EACdC,OAAuB,EACN;EACjB,IAAID,MAAM,CAAC0B,IAAI,IAAI1B,MAAM,CAACO,UAAU,EAAE;IACpC,OAAO,MAAMP,MAAM,CAACO,UAAU,CAACR,IAAI,EAAEE,OAAO,CAAC;EAC/C;EAEA,IAAID,MAAM,CAAC0B,IAAI,KAAK1B,MAAM,CAACF,MAAM,IAAIE,MAAM,CAACS,eAAe,CAAC,EAAE;IAC5D,MAAMe,WAAW,GAAG,MAAM1B,MAAM,CAACC,IAAI,EAAEC,MAAM,EAAEC,OAAO,CAAC;IACvD,OAAO,IAAI0B,WAAW,CAAC,CAAC,CAACC,MAAM,CAACJ,WAAW,CAAC;EAC9C;EAEA,MAAM,IAAIC,KAAK,CAAC,sCAAsC,CAAC;AACzD;AAKO,SAAShB,eAAeA,CAC7BV,IAAS,EACTC,MAAc,EACdC,OAAuB,EACK;EAC5B,IAAID,MAAM,CAACS,eAAe,EAAE;IAC1B,MAAMoB,YAAY,GAAGC,WAAW,CAAC/B,IAAI,CAAC;IACtC,OAAOC,MAAM,CAACS,eAAe,CAACoB,YAAY,EAAE5B,OAAO,CAAC;EACtD;EAEA,MAAM,IAAIwB,KAAK,CAAC,yCAAyC,CAAC;AAC5D;AAMO,eAAeT,cAAcA,CAClCe,QAAQ,EACRC,SAAS,EACThC,MAAc,EACdC,OAAO,EACU;EACjB8B,QAAQ,GAAG,IAAAE,wBAAW,EAACF,QAAQ,CAAC;EAChCC,SAAS,GAAG,IAAAC,wBAAW,EAACD,SAAS,CAAC;EAClC,IAAIjB,sBAAS,IAAI,CAACf,MAAM,CAACgB,cAAc,EAAE;IACvC,MAAM,IAAIS,KAAK,CAAC,CAAC;EACnB;EACA,MAAMJ,cAAc,GAAG,MAAMrB,MAAM,CAACgB,cAAc,CAACe,QAAQ,EAAEC,SAAS,EAAE/B,OAAO,CAAC;EAChF,OAAOoB,cAAc;AACvB;AAKA,SAASS,WAAWA,CAAC/B,IAAI,EAAE;EACzB,MAAM8B,YAAY,GAAG,CAAC;IAACK,KAAK,EAAEnC,IAAI;IAAEoC,KAAK,EAAE,CAAC;IAAEC,GAAG,EAAErC,IAAI,CAACsC;EAAM,CAAC,CAAC;EAChE,OAAOR,YAAY;AACrB;AAKA,SAASX,oBAAoBA,CAACoB,QAAgB,EAAU;EACtD,eAAAC,MAAA,CAAeD,QAAQ;AACzB"}