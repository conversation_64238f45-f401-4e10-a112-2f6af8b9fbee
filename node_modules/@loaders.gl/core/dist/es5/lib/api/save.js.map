{"version": 3, "file": "save.js", "names": ["_encode", "require", "_writeFile", "save", "data", "url", "writer", "options", "encodedData", "encode", "writeFile", "saveSync", "encodeSync", "writeFileSync"], "sources": ["../../../../src/lib/api/save.ts"], "sourcesContent": ["import type {Writer, WriterOptions} from '@loaders.gl/loader-utils';\nimport {encode, encodeSync} from './encode';\nimport {writeFile, writeFileSync} from '../fetch/write-file';\n\nexport async function save(data, url, writer: Writer, options: WriterOptions) {\n  const encodedData = await encode(data, writer, options);\n  return await writeFile(url, encodedData);\n}\n\nexport function saveSync(data, url, writer, options) {\n  const encodedData = encodeSync(data, writer, options);\n  return writeFileSync(url, encodedData);\n}\n"], "mappings": ";;;;;;;AACA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;AAEO,eAAeE,IAAIA,CAACC,IAAI,EAAEC,GAAG,EAAEC,MAAc,EAAEC,OAAsB,EAAE;EAC5E,MAAMC,WAAW,GAAG,MAAM,IAAAC,cAAM,EAACL,IAAI,EAAEE,MAAM,EAAEC,OAAO,CAAC;EACvD,OAAO,MAAM,IAAAG,oBAAS,EAACL,GAAG,EAAEG,WAAW,CAAC;AAC1C;AAEO,SAASG,QAAQA,CAACP,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACnD,MAAMC,WAAW,GAAG,IAAAI,kBAAU,EAACR,IAAI,EAAEE,MAAM,EAAEC,OAAO,CAAC;EACrD,OAAO,IAAAM,wBAAa,EAACR,GAAG,EAAEG,WAAW,CAAC;AACxC"}