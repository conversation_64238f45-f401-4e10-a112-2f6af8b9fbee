{"version": 3, "file": "parse-in-batches.js", "names": ["_loaderUtils", "require", "_normalize<PERSON><PERSON>der", "_optionUtils", "_loaderContext", "_getData", "_resourceUtils", "_select<PERSON><PERSON><PERSON>", "_parse", "parseInBatches", "data", "loaders", "options", "context", "assert", "loaderArray", "Array", "isArray", "undefined", "isLoaderObject", "url", "getResourceUrl", "loader", "<PERSON><PERSON><PERSON><PERSON>", "normalizeOptions", "getLoaderContext", "parse", "parseWithLoaderInBatches", "outputIterator", "parseToOutputIterator", "metadata", "metadataBatch", "batchType", "_loader", "_context", "bytesUsed", "makeMetadataBatchIterator", "iterator", "inputIterator", "getAsyncIterableFromData", "transformedIterator", "applyInputTransforms", "transforms", "parseChunkInBatches", "arrayBuffer", "concatenateArrayBuffersAsync", "parsedData", "mimeType", "mimeTypes", "batch", "shape", "length", "arguments", "iterator<PERSON><PERSON><PERSON>", "transformBatches"], "sources": ["../../../../src/lib/api/parse-in-batches.ts"], "sourcesContent": ["import type {Batch} from '@loaders.gl/schema';\nimport type {\n  BatchableDataType,\n  Loader,\n  LoaderWithParser,\n  LoaderContext,\n  LoaderOptions\n} from '@loaders.gl/loader-utils';\nimport {assert, concatenateArrayBuffersAsync} from '@loaders.gl/loader-utils';\nimport {isLoaderObject} from '../loader-utils/normalize-loader';\nimport {normalizeOptions} from '../loader-utils/option-utils';\nimport {getLoaderContext} from '../loader-utils/loader-context';\nimport {getAsyncIterableFromData} from '../loader-utils/get-data';\nimport {getResourceUrl} from '../utils/resource-utils';\nimport {selectLoader} from './select-loader';\n\n// Ensure `parse` is available in context if loader falls back to `parse`\nimport {parse} from './parse';\n\n/**\n * Parses `data` using a specified loader\n * @param data\n * @param loaders\n * @param options\n * @param context\n */\nexport async function parseInBatches(\n  data: BatchableDataType,\n  loaders?: Loader | Loader[] | LoaderOptions,\n  options?: LoaderOptions,\n  context?: LoaderContext\n): Promise<AsyncIterable<any>> {\n  assert(!context || typeof context === 'object'); // parseInBatches no longer accepts final url\n\n  const loaderArray = Array.isArray(loaders) ? loaders : undefined;\n\n  // Signature: parseInBatches(data, options, url) - Uses registered loaders\n  if (!Array.isArray(loaders) && !isLoaderObject(loaders)) {\n    context = undefined; // context not supported in short signature\n    options = loaders as LoaderOptions;\n    loaders = undefined;\n  }\n\n  data = await data; // Resolve any promise\n  options = options || {};\n\n  // Extract a url for auto detection\n  const url = getResourceUrl(data);\n\n  // Chooses a loader and normalizes it\n  // Note - only uses URL and contentType for streams and iterator inputs\n  const loader = await selectLoader(data as ArrayBuffer, loaders as Loader | Loader[], options);\n  // Note: if options.nothrow was set, it is possible that no loader was found, if so just return null\n  if (!loader) {\n    // @ts-ignore\n    return null;\n  }\n\n  // Normalize options\n  options = normalizeOptions(options, loader, loaderArray, url);\n  context = getLoaderContext(\n    {url, parseInBatches, parse, loaders: loaderArray},\n    options,\n    context || null\n  );\n\n  return await parseWithLoaderInBatches(loader as LoaderWithParser, data, options, context);\n}\n\n/**\n * Loader has been selected and context has been prepared, see if we need to emit a metadata batch\n */\nasync function parseWithLoaderInBatches(\n  loader: LoaderWithParser,\n  data: BatchableDataType,\n  options: LoaderOptions,\n  context: LoaderContext\n): Promise<AsyncIterable<any>> {\n  const outputIterator = await parseToOutputIterator(loader, data, options, context);\n\n  // Generate metadata batch if requested\n  if (!options.metadata) {\n    return outputIterator;\n  }\n\n  const metadataBatch = {\n    batchType: 'metadata',\n    metadata: {\n      _loader: loader,\n      _context: context\n    },\n    // Populate with some default fields to avoid crashing\n    data: [],\n    bytesUsed: 0\n  };\n\n  async function* makeMetadataBatchIterator(iterator) {\n    yield metadataBatch;\n    yield* iterator;\n  }\n\n  return makeMetadataBatchIterator(outputIterator);\n}\n\n/**\n * Prep work is done, now it is time to start parsing into an output operator\n * The approach depends on which parse function the loader exposes\n * `parseInBatches` (preferred), `parse` (fallback)\n */\nasync function parseToOutputIterator(\n  loader: LoaderWithParser,\n  data: BatchableDataType,\n  options: LoaderOptions,\n  context: LoaderContext\n): Promise<AsyncIterable<any>> {\n  // Get an iterator from the input\n  const inputIterator = await getAsyncIterableFromData(data, options);\n\n  // Apply any iterator transforms (options.transforms)\n  const transformedIterator = await applyInputTransforms(inputIterator, options?.transforms || []);\n\n  // If loader supports parseInBatches, we are done\n  if (loader.parseInBatches) {\n    return loader.parseInBatches(transformedIterator, options, context);\n  }\n\n  // Fallback: load atomically using `parse` concatenating input iterator into single chunk\n  async function* parseChunkInBatches() {\n    const arrayBuffer = await concatenateArrayBuffersAsync(transformedIterator);\n    // Call `parse` instead of `loader.parse` to ensure we can call workers etc.\n    const parsedData = await parse(\n      arrayBuffer,\n      loader,\n      // TODO - Hack: supply loaders MIME type to ensure we match it\n      {...options, mimeType: loader.mimeTypes[0]},\n      context\n    );\n    // yield a single batch, the output from loader.parse()\n    // TODO - run through batch builder to apply options etc...\n    const batch: Batch = {\n      mimeType: loader.mimeTypes[0],\n      shape: Array.isArray(parsedData) ? 'row-table' : 'unknown',\n      batchType: 'data',\n      data: parsedData,\n      length: Array.isArray(parsedData) ? parsedData.length : 1\n    };\n    yield batch;\n  }\n\n  return parseChunkInBatches();\n}\n\ntype TransformBatches = (\n  asyncIterator: AsyncIterable<ArrayBuffer> | Iterable<ArrayBuffer>\n) => AsyncIterable<ArrayBuffer>;\n\n/**\n * Create an iterator chain with any transform iterators (crypto, decompression)\n * @param inputIterator\n * @param options\n */\nasync function applyInputTransforms(\n  inputIterator: AsyncIterable<ArrayBuffer> | Iterable<ArrayBuffer>,\n  transforms: TransformBatches[] = []\n): Promise<AsyncIterable<ArrayBuffer> | Iterable<ArrayBuffer>> {\n  let iteratorChain = inputIterator;\n  for await (const transformBatches of transforms) {\n    iteratorChain = transformBatches(iteratorChain);\n  }\n  return iteratorChain;\n}\n"], "mappings": ";;;;;;AAQA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAD,OAAA;AACA,IAAAE,YAAA,GAAAF,OAAA;AACA,IAAAG,cAAA,GAAAH,OAAA;AACA,IAAAI,QAAA,GAAAJ,OAAA;AACA,IAAAK,cAAA,GAAAL,OAAA;AACA,IAAAM,aAAA,GAAAN,OAAA;AAGA,IAAAO,MAAA,GAAAP,OAAA;AASO,eAAeQ,cAAcA,CAClCC,IAAuB,EACvBC,OAA2C,EAC3CC,OAAuB,EACvBC,OAAuB,EACM;EAC7B,IAAAC,mBAAM,EAAC,CAACD,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,CAAC;EAE/C,MAAME,WAAW,GAAGC,KAAK,CAACC,OAAO,CAACN,OAAO,CAAC,GAAGA,OAAO,GAAGO,SAAS;EAGhE,IAAI,CAACF,KAAK,CAACC,OAAO,CAACN,OAAO,CAAC,IAAI,CAAC,IAAAQ,+BAAc,EAACR,OAAO,CAAC,EAAE;IACvDE,OAAO,GAAGK,SAAS;IACnBN,OAAO,GAAGD,OAAwB;IAClCA,OAAO,GAAGO,SAAS;EACrB;EAEAR,IAAI,GAAG,MAAMA,IAAI;EACjBE,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAGvB,MAAMQ,GAAG,GAAG,IAAAC,6BAAc,EAACX,IAAI,CAAC;EAIhC,MAAMY,MAAM,GAAG,MAAM,IAAAC,0BAAY,EAACb,IAAI,EAAiBC,OAAO,EAAuBC,OAAO,CAAC;EAE7F,IAAI,CAACU,MAAM,EAAE;IAEX,OAAO,IAAI;EACb;EAGAV,OAAO,GAAG,IAAAY,6BAAgB,EAACZ,OAAO,EAAEU,MAAM,EAAEP,WAAW,EAAEK,GAAG,CAAC;EAC7DP,OAAO,GAAG,IAAAY,+BAAgB,EACxB;IAACL,GAAG;IAAEX,cAAc;IAAEiB,KAAK,EAALA,YAAK;IAAEf,OAAO,EAAEI;EAAW,CAAC,EAClDH,OAAO,EACPC,OAAO,IAAI,IACb,CAAC;EAED,OAAO,MAAMc,wBAAwB,CAACL,MAAM,EAAsBZ,IAAI,EAAEE,OAAO,EAAEC,OAAO,CAAC;AAC3F;AAKA,eAAec,wBAAwBA,CACrCL,MAAwB,EACxBZ,IAAuB,EACvBE,OAAsB,EACtBC,OAAsB,EACO;EAC7B,MAAMe,cAAc,GAAG,MAAMC,qBAAqB,CAACP,MAAM,EAAEZ,IAAI,EAAEE,OAAO,EAAEC,OAAO,CAAC;EAGlF,IAAI,CAACD,OAAO,CAACkB,QAAQ,EAAE;IACrB,OAAOF,cAAc;EACvB;EAEA,MAAMG,aAAa,GAAG;IACpBC,SAAS,EAAE,UAAU;IACrBF,QAAQ,EAAE;MACRG,OAAO,EAAEX,MAAM;MACfY,QAAQ,EAAErB;IACZ,CAAC;IAEDH,IAAI,EAAE,EAAE;IACRyB,SAAS,EAAE;EACb,CAAC;EAED,gBAAgBC,yBAAyBA,CAACC,QAAQ,EAAE;IAClD,MAAMN,aAAa;IACnB,OAAOM,QAAQ;EACjB;EAEA,OAAOD,yBAAyB,CAACR,cAAc,CAAC;AAClD;AAOA,eAAeC,qBAAqBA,CAClCP,MAAwB,EACxBZ,IAAuB,EACvBE,OAAsB,EACtBC,OAAsB,EACO;EAE7B,MAAMyB,aAAa,GAAG,MAAM,IAAAC,iCAAwB,EAAC7B,IAAI,EAAEE,OAAO,CAAC;EAGnE,MAAM4B,mBAAmB,GAAG,MAAMC,oBAAoB,CAACH,aAAa,EAAE,CAAA1B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE8B,UAAU,KAAI,EAAE,CAAC;EAGhG,IAAIpB,MAAM,CAACb,cAAc,EAAE;IACzB,OAAOa,MAAM,CAACb,cAAc,CAAC+B,mBAAmB,EAAE5B,OAAO,EAAEC,OAAO,CAAC;EACrE;EAGA,gBAAgB8B,mBAAmBA,CAAA,EAAG;IACpC,MAAMC,WAAW,GAAG,MAAM,IAAAC,yCAA4B,EAACL,mBAAmB,CAAC;IAE3E,MAAMM,UAAU,GAAG,MAAM,IAAApB,YAAK,EAC5BkB,WAAW,EACXtB,MAAM,EAEN;MAAC,GAAGV,OAAO;MAAEmC,QAAQ,EAAEzB,MAAM,CAAC0B,SAAS,CAAC,CAAC;IAAC,CAAC,EAC3CnC,OACF,CAAC;IAGD,MAAMoC,KAAY,GAAG;MACnBF,QAAQ,EAAEzB,MAAM,CAAC0B,SAAS,CAAC,CAAC,CAAC;MAC7BE,KAAK,EAAElC,KAAK,CAACC,OAAO,CAAC6B,UAAU,CAAC,GAAG,WAAW,GAAG,SAAS;MAC1Dd,SAAS,EAAE,MAAM;MACjBtB,IAAI,EAAEoC,UAAU;MAChBK,MAAM,EAAEnC,KAAK,CAACC,OAAO,CAAC6B,UAAU,CAAC,GAAGA,UAAU,CAACK,MAAM,GAAG;IAC1D,CAAC;IACD,MAAMF,KAAK;EACb;EAEA,OAAON,mBAAmB,CAAC,CAAC;AAC9B;AAWA,eAAeF,oBAAoBA,CACjCH,aAAiE,EAEJ;EAAA,IAD7DI,UAA8B,GAAAU,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAlC,SAAA,GAAAkC,SAAA,MAAG,EAAE;EAEnC,IAAIC,aAAa,GAAGf,aAAa;EACjC,WAAW,MAAMgB,gBAAgB,IAAIZ,UAAU,EAAE;IAC/CW,aAAa,GAAGC,gBAAgB,CAACD,aAAa,CAAC;EACjD;EACA,OAAOA,aAAa;AACtB"}