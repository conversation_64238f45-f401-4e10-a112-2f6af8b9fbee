"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.parse = parse;
var _workerUtils = require("@loaders.gl/worker-utils");
var _loaderUtils = require("@loaders.gl/loader-utils");
var _normalizeLoader = require("../loader-utils/normalize-loader");
var _isType = require("../../javascript-utils/is-type");
var _optionUtils = require("../loader-utils/option-utils");
var _getData = require("../loader-utils/get-data");
var _loaderContext = require("../loader-utils/loader-context");
var _resourceUtils = require("../utils/resource-utils");
var _selectLoader = require("./select-loader");
async function parse(data, loaders, options, context) {
  (0, _workerUtils.assert)(!context || typeof context === 'object');
  if (loaders && !Array.isArray(loaders) && !(0, _normalizeLoader.isLoaderObject)(loaders)) {
    context = undefined;
    options = loaders;
    loaders = undefined;
  }
  data = await data;
  options = options || {};
  const url = (0, _resourceUtils.getResourceUrl)(data);
  const typedLoaders = loaders;
  const candidateLoaders = (0, _loaderContext.getLoadersFromContext)(typedLoaders, context);
  const loader = await (0, _selectLoader.selectLoader)(data, candidateLoaders, options);
  if (!loader) {
    return null;
  }
  options = (0, _optionUtils.normalizeOptions)(options, loader, candidateLoaders, url);
  context = (0, _loaderContext.getLoaderContext)({
    url,
    parse,
    loaders: candidateLoaders
  }, options, context || null);
  return await parseWithLoader(loader, data, options, context);
}
async function parseWithLoader(loader, data, options, context) {
  (0, _workerUtils.validateWorkerVersion)(loader);
  if ((0, _isType.isResponse)(data)) {
    const response = data;
    const {
      ok,
      redirected,
      status,
      statusText,
      type,
      url
    } = response;
    const headers = Object.fromEntries(response.headers.entries());
    context.response = {
      headers,
      ok,
      redirected,
      status,
      statusText,
      type,
      url
    };
  }
  data = await (0, _getData.getArrayBufferOrStringFromData)(data, loader, options);
  if (loader.parseTextSync && typeof data === 'string') {
    options.dataType = 'text';
    return loader.parseTextSync(data, options, context, loader);
  }
  if ((0, _loaderUtils.canParseWithWorker)(loader, options)) {
    return await (0, _loaderUtils.parseWithWorker)(loader, data, options, context, parse);
  }
  if (loader.parseText && typeof data === 'string') {
    return await loader.parseText(data, options, context, loader);
  }
  if (loader.parse) {
    return await loader.parse(data, options, context, loader);
  }
  (0, _workerUtils.assert)(!loader.parseSync);
  throw new Error("".concat(loader.id, " loader - no parser found and worker is disabled"));
}
//# sourceMappingURL=parse.js.map