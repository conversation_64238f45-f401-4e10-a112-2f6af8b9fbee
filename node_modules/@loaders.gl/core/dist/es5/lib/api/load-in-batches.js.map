{"version": 3, "file": "load-in-batches.js", "names": ["_normalize<PERSON><PERSON>der", "require", "_getFetchFunction", "_parseInBatches", "loadInBatches", "files", "loaders", "options", "context", "Array", "isArray", "isLoaderObject", "undefined", "fetch", "getFetchFunction", "loadOneFileInBatches", "promises", "map", "file", "url", "response", "parseInBatches"], "sources": ["../../../../src/lib/api/load-in-batches.ts"], "sourcesContent": ["import type {LoaderWithParser, LoaderOptions, LoaderContext} from '@loaders.gl/loader-utils';\nimport {isLoaderObject} from '../loader-utils/normalize-loader';\nimport {getFetchFunction} from '../loader-utils/get-fetch-function';\n\nimport {parseInBatches} from './parse-in-batches';\n\ntype FileType = string | File | Blob | Response | (string | File | Blob | Response)[] | FileList;\n\n/**\n * Parses `data` using a specified loader\n * @param data\n * @param loaders\n * @param options\n * @param context\n */\nexport function loadInBatches(\n  files: FileType,\n  loaders?: LoaderWithParser | LoaderWithParser[] | LoaderOptions,\n  options?: LoaderOptions,\n  context?: LoaderContext\n): Promise<AsyncIterable<any>>;\n\nexport function loadInBatches(\n  files: FileType[] | FileList,\n  loaders?: LoaderWithParser | LoaderWithParser[] | LoaderOptions,\n  options?: LoaderOptions,\n  context?: LoaderContext\n): Promise<AsyncIterable<any>>;\n\nexport function loadInBatches(files, loaders, options, context) {\n  // Signature: load(url, options)\n  if (!Array.isArray(loaders) && !isLoaderObject(loaders)) {\n    context = undefined; // context not supported in short signature\n    options = loaders;\n    loaders = null;\n  }\n\n  // Select fetch function\n  const fetch = getFetchFunction(options || {});\n\n  // Single url/file\n  if (!Array.isArray(files)) {\n    return loadOneFileInBatches(files, loaders, options, fetch);\n  }\n\n  // Multiple URLs / files\n  const promises = files.map((file) => loadOneFileInBatches(file, loaders, options, fetch));\n\n  // No point in waiting here for all responses before starting to stream individual streams?\n  return promises;\n}\n\nasync function loadOneFileInBatches(file, loaders, options, fetch) {\n  if (typeof file === 'string') {\n    const url = file;\n    const response = await fetch(url);\n    return await parseInBatches(response, loaders, options);\n  }\n  return await parseInBatches(file, loaders, options);\n}\n"], "mappings": ";;;;;;AACA,IAAAA,gBAAA,GAAAC,OAAA;AACA,IAAAC,iBAAA,GAAAD,OAAA;AAEA,IAAAE,eAAA,GAAAF,OAAA;AAyBO,SAASG,aAAaA,CAACC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAE;EAE9D,IAAI,CAACC,KAAK,CAACC,OAAO,CAACJ,OAAO,CAAC,IAAI,CAAC,IAAAK,+BAAc,EAACL,OAAO,CAAC,EAAE;IACvDE,OAAO,GAAGI,SAAS;IACnBL,OAAO,GAAGD,OAAO;IACjBA,OAAO,GAAG,IAAI;EAChB;EAGA,MAAMO,KAAK,GAAG,IAAAC,kCAAgB,EAACP,OAAO,IAAI,CAAC,CAAC,CAAC;EAG7C,IAAI,CAACE,KAAK,CAACC,OAAO,CAACL,KAAK,CAAC,EAAE;IACzB,OAAOU,oBAAoB,CAACV,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEM,KAAK,CAAC;EAC7D;EAGA,MAAMG,QAAQ,GAAGX,KAAK,CAACY,GAAG,CAAEC,IAAI,IAAKH,oBAAoB,CAACG,IAAI,EAAEZ,OAAO,EAAEC,OAAO,EAAEM,KAAK,CAAC,CAAC;EAGzF,OAAOG,QAAQ;AACjB;AAEA,eAAeD,oBAAoBA,CAACG,IAAI,EAAEZ,OAAO,EAAEC,OAAO,EAAEM,KAAK,EAAE;EACjE,IAAI,OAAOK,IAAI,KAAK,QAAQ,EAAE;IAC5B,MAAMC,GAAG,GAAGD,IAAI;IAChB,MAAME,QAAQ,GAAG,MAAMP,KAAK,CAACM,GAAG,CAAC;IACjC,OAAO,MAAM,IAAAE,8BAAc,EAACD,QAAQ,EAAEd,OAAO,EAAEC,OAAO,CAAC;EACzD;EACA,OAAO,MAAM,IAAAc,8BAAc,EAACH,IAAI,EAAEZ,OAAO,EAAEC,OAAO,CAAC;AACrD"}