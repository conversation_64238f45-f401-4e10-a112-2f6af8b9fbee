{"version": 3, "file": "load.js", "names": ["_isType", "require", "_normalize<PERSON><PERSON>der", "_getFetchFunction", "_parse", "load", "url", "loaders", "options", "context", "Array", "isArray", "isLoaderObject", "undefined", "fetch", "getFetchFunction", "data", "isBlob", "parse"], "sources": ["../../../../src/lib/api/load.ts"], "sourcesContent": ["import type {DataType, Loader, LoaderContext, LoaderOptions} from '@loaders.gl/loader-utils';\nimport {isBlob} from '../../javascript-utils/is-type';\nimport {isLoaderObject} from '../loader-utils/normalize-loader';\nimport {getFetchFunction} from '../loader-utils/get-fetch-function';\n\nimport {parse} from './parse';\n\n/**\n * Parses `data` using a specified loader\n * Note: Load does duplicate a lot of parse.\n * it can also call fetchFile on string urls, which `parse` won't do.\n * @param data\n * @param loaders\n * @param options\n * @param context\n */\n// implementation signature\nexport async function load(\n  url: string | DataType,\n  loaders?: Loader | Loader[] | LoaderOptions,\n  options?: LoaderOptions,\n  context?: LoaderContext\n): Promise<any> {\n  // Signature: load(url, options)\n  if (!Array.isArray(loaders) && !isLoaderObject(loaders)) {\n    context = undefined; // context not supported in short signature\n    options = loaders as LoaderOptions;\n    loaders = undefined;\n  }\n\n  // Select fetch function\n  const fetch = getFetchFunction(options);\n\n  // at this point, `url` could be already loaded binary data\n  let data = url;\n  // url is a string, fetch the url\n  if (typeof url === 'string') {\n    data = await fetch(url);\n    // URL is Blob or File, fetchFile handles it (alt: we could generate ObjectURL here)\n  }\n\n  if (isBlob(url)) {\n    // The fetch response object will contain blob.name\n    // @ts-expect-error TODO - This may not work for overridden fetch functions\n    data = await fetch(url);\n  }\n\n  // Data is loaded (at least we have a `Response` object) so time to hand over to `parse`\n  return await parse(data, loaders as Loader[], options);\n}\n"], "mappings": ";;;;;;AACA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAD,OAAA;AACA,IAAAE,iBAAA,GAAAF,OAAA;AAEA,IAAAG,MAAA,GAAAH,OAAA;AAYO,eAAeI,IAAIA,CACxBC,GAAsB,EACtBC,OAA2C,EAC3CC,OAAuB,EACvBC,OAAuB,EACT;EAEd,IAAI,CAACC,KAAK,CAACC,OAAO,CAACJ,OAAO,CAAC,IAAI,CAAC,IAAAK,+BAAc,EAACL,OAAO,CAAC,EAAE;IACvDE,OAAO,GAAGI,SAAS;IACnBL,OAAO,GAAGD,OAAwB;IAClCA,OAAO,GAAGM,SAAS;EACrB;EAGA,MAAMC,KAAK,GAAG,IAAAC,kCAAgB,EAACP,OAAO,CAAC;EAGvC,IAAIQ,IAAI,GAAGV,GAAG;EAEd,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAC3BU,IAAI,GAAG,MAAMF,KAAK,CAACR,GAAG,CAAC;EAEzB;EAEA,IAAI,IAAAW,cAAM,EAACX,GAAG,CAAC,EAAE;IAGfU,IAAI,GAAG,MAAMF,KAAK,CAACR,GAAG,CAAC;EACzB;EAGA,OAAO,MAAM,IAAAY,YAAK,EAACF,IAAI,EAAET,OAAO,EAAcC,OAAO,CAAC;AACxD"}