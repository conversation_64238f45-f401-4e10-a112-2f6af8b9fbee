{"version": 3, "file": "select-loader.js", "names": ["_loaderUtils", "require", "_normalize<PERSON><PERSON>der", "_log", "_resourceUtils", "_registerLoaders", "_isType", "_urlUtils", "EXT_PATTERN", "<PERSON><PERSON><PERSON><PERSON>", "data", "loaders", "arguments", "length", "undefined", "options", "context", "validHTTPResponse", "loader", "selectLoaderSync", "nothrow", "isBlob", "slice", "arrayBuffer", "Error", "getNoValidLoaderMessage", "Array", "isArray", "normalize<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "concat", "ignoreRegisteredLoaders", "push", "getRegisteredLoaders", "normalizeLoaders", "selectLoaderInternal", "url", "getResourceUrl", "type", "getResourceMIMEType", "testUrl", "stripQueryString", "reason", "mimeType", "findLoaderByMIMEType", "findLoaderByUrl", "findLoaderByInitialBytes", "getFirstCharacters", "fallbackMimeType", "_loader", "log", "name", "Response", "status", "message", "path", "filename", "firstCharacters", "match", "exec", "extension", "findLoaderByExtension", "toLowerCase", "loaderExtension", "extensions", "mimeTypes", "includes", "id", "testDataAgainstText", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "testDataAgainstBinary", "buffer", "byteOffset", "testText", "tests", "some", "test", "startsWith", "testBinary", "compareArrayBuffers", "byteLength", "magic", "getMagicString", "dataView", "DataView", "i", "String", "fromCharCode", "getUint8"], "sources": ["../../../../src/lib/api/select-loader.ts"], "sourcesContent": ["import type {LoaderContext, LoaderOptions, Loader} from '@loaders.gl/loader-utils';\nimport {compareArrayBuffers, path} from '@loaders.gl/loader-utils';\nimport {normalizeLoader} from '../loader-utils/normalize-loader';\nimport {log} from '../utils/log';\nimport {getResourceUrl, getResourceMIMEType} from '../utils/resource-utils';\nimport {getRegisteredLoaders} from './register-loaders';\nimport {isBlob} from '../../javascript-utils/is-type';\nimport {stripQueryString} from '../utils/url-utils';\n\nconst EXT_PATTERN = /\\.([^.]+)$/;\n\n// TODO - Need a variant that peeks at streams for parseInBatches\n// TODO - Detect multiple matching loaders? Use heuristics to grade matches?\n// TODO - Allow apps to pass context to disambiguate between multiple matches (e.g. multiple .json formats)?\n\n/**\n * Find a loader that matches file extension and/or initial file content\n * Search the loaders array argument for a loader that matches url extension or initial data\n * Returns: a normalized loader\n * @param data data to assist\n * @param loaders\n * @param options\n * @param context used internally, applications should not provide this parameter\n */\nexport async function selectLoader(\n  data: Response | Blob | ArrayBuffer | string,\n  loaders: Loader[] | Loader = [],\n  options?: LoaderOptions,\n  context?: LoaderContext\n): Promise<Loader | null> {\n  if (!validHTTPResponse(data)) {\n    return null;\n  }\n\n  // First make a sync attempt, disabling exceptions\n  let loader = selectLoaderSync(data, loaders, {...options, nothrow: true}, context);\n  if (loader) {\n    return loader;\n  }\n\n  // For Blobs and Files, try to asynchronously read a small initial slice and test again with that\n  // to see if we can detect by initial content\n  if (isBlob(data)) {\n    data = await (data as Blob).slice(0, 10).arrayBuffer();\n    loader = selectLoaderSync(data, loaders, options, context);\n  }\n\n  // no loader available\n  if (!loader && !options?.nothrow) {\n    throw new Error(getNoValidLoaderMessage(data));\n  }\n\n  return loader;\n}\n\n/**\n * Find a loader that matches file extension and/or initial file content\n * Search the loaders array argument for a loader that matches url extension or initial data\n * Returns: a normalized loader\n * @param data data to assist\n * @param loaders\n * @param options\n * @param context used internally, applications should not provide this parameter\n */\nexport function selectLoaderSync(\n  data: Response | Blob | ArrayBuffer | string,\n  loaders: Loader[] | Loader = [],\n  options?: LoaderOptions,\n  context?: LoaderContext\n): Loader | null {\n  if (!validHTTPResponse(data)) {\n    return null;\n  }\n\n  // eslint-disable-next-line complexity\n  // if only a single loader was provided (not as array), force its use\n  // TODO - Should this behavior be kept and documented?\n  if (loaders && !Array.isArray(loaders)) {\n    // TODO - remove support for legacy loaders\n    return normalizeLoader(loaders);\n  }\n\n  // Build list of candidate loaders that will be searched in order for a match\n  let candidateLoaders: Loader[] = [];\n  // First search supplied loaders\n  if (loaders) {\n    candidateLoaders = candidateLoaders.concat(loaders);\n  }\n  // Then fall back to registered loaders\n  if (!options?.ignoreRegisteredLoaders) {\n    candidateLoaders.push(...getRegisteredLoaders());\n  }\n\n  // TODO - remove support for legacy loaders\n  normalizeLoaders(candidateLoaders);\n\n  const loader = selectLoaderInternal(data, candidateLoaders, options, context);\n\n  // no loader available\n  if (!loader && !options?.nothrow) {\n    throw new Error(getNoValidLoaderMessage(data));\n  }\n\n  return loader;\n}\n\n/** Implements loaders selection logic */\n// eslint-disable-next-line complexity\nfunction selectLoaderInternal(\n  data: Response | Blob | ArrayBuffer | string,\n  loaders: Loader[],\n  options?: LoaderOptions,\n  context?: LoaderContext\n) {\n  const url = getResourceUrl(data);\n  const type = getResourceMIMEType(data);\n\n  const testUrl = stripQueryString(url) || context?.url;\n\n  let loader: Loader | null = null;\n  let reason: string = '';\n\n  // if options.mimeType is supplied, it takes precedence\n  if (options?.mimeType) {\n    loader = findLoaderByMIMEType(loaders, options?.mimeType);\n    reason = `match forced by supplied MIME type ${options?.mimeType}`;\n  }\n\n  // Look up loader by url\n  loader = loader || findLoaderByUrl(loaders, testUrl);\n  reason = reason || (loader ? `matched url ${testUrl}` : '');\n\n  // Look up loader by mime type\n  loader = loader || findLoaderByMIMEType(loaders, type);\n  reason = reason || (loader ? `matched MIME type ${type}` : '');\n\n  // Look for loader via initial bytes (Note: not always accessible (e.g. Response, stream, async iterator)\n  loader = loader || findLoaderByInitialBytes(loaders, data);\n  reason = reason || (loader ? `matched initial data ${getFirstCharacters(data)}` : '');\n\n  // Look up loader by fallback mime type\n  loader = loader || findLoaderByMIMEType(loaders, options?.fallbackMimeType);\n  reason = reason || (loader ? `matched fallback MIME type ${type}` : '');\n\n  if (reason) {\n    log.log(1, `selectLoader selected ${loader?.name}: ${reason}.`);\n  }\n\n  return loader;\n}\n\n/** Check HTTP Response */\nfunction validHTTPResponse(data: any): boolean {\n  // HANDLE HTTP status\n  if (data instanceof Response) {\n    // 204 - NO CONTENT. This handles cases where e.g. a tile server responds with 204 for a missing tile\n    if (data.status === 204) {\n      return false;\n    }\n  }\n  return true;\n}\n\n/** Generate a helpful message to help explain why loader selection failed. */\nfunction getNoValidLoaderMessage(data): string {\n  const url = getResourceUrl(data);\n  const type = getResourceMIMEType(data);\n\n  let message = 'No valid loader found (';\n  message += url ? `${path.filename(url)}, ` : 'no url provided, ';\n  message += `MIME type: ${type ? `\"${type}\"` : 'not provided'}, `;\n  // First characters are only accessible when called on data (string or arrayBuffer).\n  const firstCharacters: string = data ? getFirstCharacters(data) : '';\n  message += firstCharacters ? ` first bytes: \"${firstCharacters}\"` : 'first bytes: not available';\n  message += ')';\n  return message;\n}\n\nfunction normalizeLoaders(loaders: Loader[]): void {\n  for (const loader of loaders) {\n    normalizeLoader(loader);\n  }\n}\n\n// TODO - Would be nice to support http://example.com/file.glb?parameter=1\n// E.g: x = new URL('http://example.com/file.glb?load=1'; x.pathname\nfunction findLoaderByUrl(loaders: Loader[], url?: string): Loader | null {\n  // Get extension\n  const match = url && EXT_PATTERN.exec(url);\n  const extension = match && match[1];\n  return extension ? findLoaderByExtension(loaders, extension) : null;\n}\n\nfunction findLoaderByExtension(loaders: Loader[], extension: string): Loader | null {\n  extension = extension.toLowerCase();\n\n  for (const loader of loaders) {\n    for (const loaderExtension of loader.extensions) {\n      if (loaderExtension.toLowerCase() === extension) {\n        return loader;\n      }\n    }\n  }\n  return null;\n}\n\nfunction findLoaderByMIMEType(loaders, mimeType) {\n  for (const loader of loaders) {\n    if (loader.mimeTypes && loader.mimeTypes.includes(mimeType)) {\n      return loader;\n    }\n\n    // Support referring to loaders using the \"unregistered tree\"\n    // https://en.wikipedia.org/wiki/Media_type#Unregistered_tree\n    if (mimeType === `application/x.${loader.id}`) {\n      return loader;\n    }\n  }\n  return null;\n}\n\nfunction findLoaderByInitialBytes(loaders, data) {\n  if (!data) {\n    return null;\n  }\n\n  for (const loader of loaders) {\n    if (typeof data === 'string') {\n      if (testDataAgainstText(data, loader)) {\n        return loader;\n      }\n    } else if (ArrayBuffer.isView(data)) {\n      // Typed Arrays can have offsets into underlying buffer\n      if (testDataAgainstBinary(data.buffer, data.byteOffset, loader)) {\n        return loader;\n      }\n    } else if (data instanceof ArrayBuffer) {\n      const byteOffset = 0;\n      if (testDataAgainstBinary(data, byteOffset, loader)) {\n        return loader;\n      }\n    }\n    // TODO Handle streaming case (requires creating a new AsyncIterator)\n  }\n  return null;\n}\n\nfunction testDataAgainstText(data, loader) {\n  if (loader.testText) {\n    return loader.testText(data);\n  }\n\n  const tests = Array.isArray(loader.tests) ? loader.tests : [loader.tests];\n  return tests.some((test) => data.startsWith(test));\n}\n\nfunction testDataAgainstBinary(data, byteOffset, loader) {\n  const tests = Array.isArray(loader.tests) ? loader.tests : [loader.tests];\n  return tests.some((test) => testBinary(data, byteOffset, loader, test));\n}\n\nfunction testBinary(data, byteOffset, loader, test) {\n  if (test instanceof ArrayBuffer) {\n    return compareArrayBuffers(test, data, test.byteLength);\n  }\n  switch (typeof test) {\n    case 'function':\n      return test(data, loader);\n\n    case 'string':\n      // Magic bytes check: If `test` is a string, check if binary data starts with that strings\n      const magic = getMagicString(data, byteOffset, test.length);\n      return test === magic;\n\n    default:\n      return false;\n  }\n}\n\nfunction getFirstCharacters(data, length: number = 5) {\n  if (typeof data === 'string') {\n    return data.slice(0, length);\n  } else if (ArrayBuffer.isView(data)) {\n    // Typed Arrays can have offsets into underlying buffer\n    return getMagicString(data.buffer, data.byteOffset, length);\n  } else if (data instanceof ArrayBuffer) {\n    const byteOffset = 0;\n    return getMagicString(data, byteOffset, length);\n  }\n  return '';\n}\n\nfunction getMagicString(arrayBuffer, byteOffset, length) {\n  if (arrayBuffer.byteLength < byteOffset + length) {\n    return '';\n  }\n  const dataView = new DataView(arrayBuffer);\n  let magic = '';\n  for (let i = 0; i < length; i++) {\n    magic += String.fromCharCode(dataView.getUint8(byteOffset + i));\n  }\n  return magic;\n}\n"], "mappings": ";;;;;;;AACA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAD,OAAA;AACA,IAAAE,IAAA,GAAAF,OAAA;AACA,IAAAG,cAAA,GAAAH,OAAA;AACA,IAAAI,gBAAA,GAAAJ,OAAA;AACA,IAAAK,OAAA,GAAAL,OAAA;AACA,IAAAM,SAAA,GAAAN,OAAA;AAEA,MAAMO,WAAW,GAAG,YAAY;AAezB,eAAeC,YAAYA,CAChCC,IAA4C,EAIpB;EAAA,IAHxBC,OAA0B,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAAA,IAC/BG,OAAuB,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,IACvBE,OAAuB,GAAAJ,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAEvB,IAAI,CAACG,iBAAiB,CAACP,IAAI,CAAC,EAAE;IAC5B,OAAO,IAAI;EACb;EAGA,IAAIQ,MAAM,GAAGC,gBAAgB,CAACT,IAAI,EAAEC,OAAO,EAAE;IAAC,GAAGI,OAAO;IAAEK,OAAO,EAAE;EAAI,CAAC,EAAEJ,OAAO,CAAC;EAClF,IAAIE,MAAM,EAAE;IACV,OAAOA,MAAM;EACf;EAIA,IAAI,IAAAG,cAAM,EAACX,IAAI,CAAC,EAAE;IAChBA,IAAI,GAAG,MAAOA,IAAI,CAAUY,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;IACtDL,MAAM,GAAGC,gBAAgB,CAACT,IAAI,EAAEC,OAAO,EAAEI,OAAO,EAAEC,OAAO,CAAC;EAC5D;EAGA,IAAI,CAACE,MAAM,IAAI,EAACH,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEK,OAAO,GAAE;IAChC,MAAM,IAAII,KAAK,CAACC,uBAAuB,CAACf,IAAI,CAAC,CAAC;EAChD;EAEA,OAAOQ,MAAM;AACf;AAWO,SAASC,gBAAgBA,CAC9BT,IAA4C,EAI7B;EAAA,IAHfC,OAA0B,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAAA,IAC/BG,OAAuB,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,IACvBE,OAAuB,GAAAJ,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAEvB,IAAI,CAACG,iBAAiB,CAACP,IAAI,CAAC,EAAE;IAC5B,OAAO,IAAI;EACb;EAKA,IAAIC,OAAO,IAAI,CAACe,KAAK,CAACC,OAAO,CAAChB,OAAO,CAAC,EAAE;IAEtC,OAAO,IAAAiB,gCAAe,EAACjB,OAAO,CAAC;EACjC;EAGA,IAAIkB,gBAA0B,GAAG,EAAE;EAEnC,IAAIlB,OAAO,EAAE;IACXkB,gBAAgB,GAAGA,gBAAgB,CAACC,MAAM,CAACnB,OAAO,CAAC;EACrD;EAEA,IAAI,EAACI,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEgB,uBAAuB,GAAE;IACrCF,gBAAgB,CAACG,IAAI,CAAC,GAAG,IAAAC,qCAAoB,EAAC,CAAC,CAAC;EAClD;EAGAC,gBAAgB,CAACL,gBAAgB,CAAC;EAElC,MAAMX,MAAM,GAAGiB,oBAAoB,CAACzB,IAAI,EAAEmB,gBAAgB,EAAEd,OAAO,EAAEC,OAAO,CAAC;EAG7E,IAAI,CAACE,MAAM,IAAI,EAACH,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEK,OAAO,GAAE;IAChC,MAAM,IAAII,KAAK,CAACC,uBAAuB,CAACf,IAAI,CAAC,CAAC;EAChD;EAEA,OAAOQ,MAAM;AACf;AAIA,SAASiB,oBAAoBA,CAC3BzB,IAA4C,EAC5CC,OAAiB,EACjBI,OAAuB,EACvBC,OAAuB,EACvB;EACA,MAAMoB,GAAG,GAAG,IAAAC,6BAAc,EAAC3B,IAAI,CAAC;EAChC,MAAM4B,IAAI,GAAG,IAAAC,kCAAmB,EAAC7B,IAAI,CAAC;EAEtC,MAAM8B,OAAO,GAAG,IAAAC,0BAAgB,EAACL,GAAG,CAAC,KAAIpB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoB,GAAG;EAErD,IAAIlB,MAAqB,GAAG,IAAI;EAChC,IAAIwB,MAAc,GAAG,EAAE;EAGvB,IAAI3B,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAE4B,QAAQ,EAAE;IACrBzB,MAAM,GAAG0B,oBAAoB,CAACjC,OAAO,EAAEI,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE4B,QAAQ,CAAC;IACzDD,MAAM,yCAAAZ,MAAA,CAAyCf,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE4B,QAAQ,CAAE;EACpE;EAGAzB,MAAM,GAAGA,MAAM,IAAI2B,eAAe,CAAClC,OAAO,EAAE6B,OAAO,CAAC;EACpDE,MAAM,GAAGA,MAAM,KAAKxB,MAAM,kBAAAY,MAAA,CAAkBU,OAAO,IAAK,EAAE,CAAC;EAG3DtB,MAAM,GAAGA,MAAM,IAAI0B,oBAAoB,CAACjC,OAAO,EAAE2B,IAAI,CAAC;EACtDI,MAAM,GAAGA,MAAM,KAAKxB,MAAM,wBAAAY,MAAA,CAAwBQ,IAAI,IAAK,EAAE,CAAC;EAG9DpB,MAAM,GAAGA,MAAM,IAAI4B,wBAAwB,CAACnC,OAAO,EAAED,IAAI,CAAC;EAC1DgC,MAAM,GAAGA,MAAM,KAAKxB,MAAM,2BAAAY,MAAA,CAA2BiB,kBAAkB,CAACrC,IAAI,CAAC,IAAK,EAAE,CAAC;EAGrFQ,MAAM,GAAGA,MAAM,IAAI0B,oBAAoB,CAACjC,OAAO,EAAEI,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEiC,gBAAgB,CAAC;EAC3EN,MAAM,GAAGA,MAAM,KAAKxB,MAAM,iCAAAY,MAAA,CAAiCQ,IAAI,IAAK,EAAE,CAAC;EAEvE,IAAII,MAAM,EAAE;IAAA,IAAAO,OAAA;IACVC,QAAG,CAACA,GAAG,CAAC,CAAC,2BAAApB,MAAA,EAAAmB,OAAA,GAA2B/B,MAAM,cAAA+B,OAAA,uBAANA,OAAA,CAAQE,IAAI,QAAArB,MAAA,CAAKY,MAAM,MAAG,CAAC;EACjE;EAEA,OAAOxB,MAAM;AACf;AAGA,SAASD,iBAAiBA,CAACP,IAAS,EAAW;EAE7C,IAAIA,IAAI,YAAY0C,QAAQ,EAAE;IAE5B,IAAI1C,IAAI,CAAC2C,MAAM,KAAK,GAAG,EAAE;MACvB,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb;AAGA,SAAS5B,uBAAuBA,CAACf,IAAI,EAAU;EAC7C,MAAM0B,GAAG,GAAG,IAAAC,6BAAc,EAAC3B,IAAI,CAAC;EAChC,MAAM4B,IAAI,GAAG,IAAAC,kCAAmB,EAAC7B,IAAI,CAAC;EAEtC,IAAI4C,OAAO,GAAG,yBAAyB;EACvCA,OAAO,IAAIlB,GAAG,MAAAN,MAAA,CAAMyB,iBAAI,CAACC,QAAQ,CAACpB,GAAG,CAAC,UAAO,mBAAmB;EAChEkB,OAAO,kBAAAxB,MAAA,CAAkBQ,IAAI,QAAAR,MAAA,CAAOQ,IAAI,UAAM,cAAc,OAAI;EAEhE,MAAMmB,eAAuB,GAAG/C,IAAI,GAAGqC,kBAAkB,CAACrC,IAAI,CAAC,GAAG,EAAE;EACpE4C,OAAO,IAAIG,eAAe,sBAAA3B,MAAA,CAAqB2B,eAAe,UAAM,4BAA4B;EAChGH,OAAO,IAAI,GAAG;EACd,OAAOA,OAAO;AAChB;AAEA,SAASpB,gBAAgBA,CAACvB,OAAiB,EAAQ;EACjD,KAAK,MAAMO,MAAM,IAAIP,OAAO,EAAE;IAC5B,IAAAiB,gCAAe,EAACV,MAAM,CAAC;EACzB;AACF;AAIA,SAAS2B,eAAeA,CAAClC,OAAiB,EAAEyB,GAAY,EAAiB;EAEvE,MAAMsB,KAAK,GAAGtB,GAAG,IAAI5B,WAAW,CAACmD,IAAI,CAACvB,GAAG,CAAC;EAC1C,MAAMwB,SAAS,GAAGF,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC;EACnC,OAAOE,SAAS,GAAGC,qBAAqB,CAAClD,OAAO,EAAEiD,SAAS,CAAC,GAAG,IAAI;AACrE;AAEA,SAASC,qBAAqBA,CAAClD,OAAiB,EAAEiD,SAAiB,EAAiB;EAClFA,SAAS,GAAGA,SAAS,CAACE,WAAW,CAAC,CAAC;EAEnC,KAAK,MAAM5C,MAAM,IAAIP,OAAO,EAAE;IAC5B,KAAK,MAAMoD,eAAe,IAAI7C,MAAM,CAAC8C,UAAU,EAAE;MAC/C,IAAID,eAAe,CAACD,WAAW,CAAC,CAAC,KAAKF,SAAS,EAAE;QAC/C,OAAO1C,MAAM;MACf;IACF;EACF;EACA,OAAO,IAAI;AACb;AAEA,SAAS0B,oBAAoBA,CAACjC,OAAO,EAAEgC,QAAQ,EAAE;EAC/C,KAAK,MAAMzB,MAAM,IAAIP,OAAO,EAAE;IAC5B,IAAIO,MAAM,CAAC+C,SAAS,IAAI/C,MAAM,CAAC+C,SAAS,CAACC,QAAQ,CAACvB,QAAQ,CAAC,EAAE;MAC3D,OAAOzB,MAAM;IACf;IAIA,IAAIyB,QAAQ,sBAAAb,MAAA,CAAsBZ,MAAM,CAACiD,EAAE,CAAE,EAAE;MAC7C,OAAOjD,MAAM;IACf;EACF;EACA,OAAO,IAAI;AACb;AAEA,SAAS4B,wBAAwBA,CAACnC,OAAO,EAAED,IAAI,EAAE;EAC/C,IAAI,CAACA,IAAI,EAAE;IACT,OAAO,IAAI;EACb;EAEA,KAAK,MAAMQ,MAAM,IAAIP,OAAO,EAAE;IAC5B,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;MAC5B,IAAI0D,mBAAmB,CAAC1D,IAAI,EAAEQ,MAAM,CAAC,EAAE;QACrC,OAAOA,MAAM;MACf;IACF,CAAC,MAAM,IAAImD,WAAW,CAACC,MAAM,CAAC5D,IAAI,CAAC,EAAE;MAEnC,IAAI6D,qBAAqB,CAAC7D,IAAI,CAAC8D,MAAM,EAAE9D,IAAI,CAAC+D,UAAU,EAAEvD,MAAM,CAAC,EAAE;QAC/D,OAAOA,MAAM;MACf;IACF,CAAC,MAAM,IAAIR,IAAI,YAAY2D,WAAW,EAAE;MACtC,MAAMI,UAAU,GAAG,CAAC;MACpB,IAAIF,qBAAqB,CAAC7D,IAAI,EAAE+D,UAAU,EAAEvD,MAAM,CAAC,EAAE;QACnD,OAAOA,MAAM;MACf;IACF;EAEF;EACA,OAAO,IAAI;AACb;AAEA,SAASkD,mBAAmBA,CAAC1D,IAAI,EAAEQ,MAAM,EAAE;EACzC,IAAIA,MAAM,CAACwD,QAAQ,EAAE;IACnB,OAAOxD,MAAM,CAACwD,QAAQ,CAAChE,IAAI,CAAC;EAC9B;EAEA,MAAMiE,KAAK,GAAGjD,KAAK,CAACC,OAAO,CAACT,MAAM,CAACyD,KAAK,CAAC,GAAGzD,MAAM,CAACyD,KAAK,GAAG,CAACzD,MAAM,CAACyD,KAAK,CAAC;EACzE,OAAOA,KAAK,CAACC,IAAI,CAAEC,IAAI,IAAKnE,IAAI,CAACoE,UAAU,CAACD,IAAI,CAAC,CAAC;AACpD;AAEA,SAASN,qBAAqBA,CAAC7D,IAAI,EAAE+D,UAAU,EAAEvD,MAAM,EAAE;EACvD,MAAMyD,KAAK,GAAGjD,KAAK,CAACC,OAAO,CAACT,MAAM,CAACyD,KAAK,CAAC,GAAGzD,MAAM,CAACyD,KAAK,GAAG,CAACzD,MAAM,CAACyD,KAAK,CAAC;EACzE,OAAOA,KAAK,CAACC,IAAI,CAAEC,IAAI,IAAKE,UAAU,CAACrE,IAAI,EAAE+D,UAAU,EAAEvD,MAAM,EAAE2D,IAAI,CAAC,CAAC;AACzE;AAEA,SAASE,UAAUA,CAACrE,IAAI,EAAE+D,UAAU,EAAEvD,MAAM,EAAE2D,IAAI,EAAE;EAClD,IAAIA,IAAI,YAAYR,WAAW,EAAE;IAC/B,OAAO,IAAAW,gCAAmB,EAACH,IAAI,EAAEnE,IAAI,EAAEmE,IAAI,CAACI,UAAU,CAAC;EACzD;EACA,QAAQ,OAAOJ,IAAI;IACjB,KAAK,UAAU;MACb,OAAOA,IAAI,CAACnE,IAAI,EAAEQ,MAAM,CAAC;IAE3B,KAAK,QAAQ;MAEX,MAAMgE,KAAK,GAAGC,cAAc,CAACzE,IAAI,EAAE+D,UAAU,EAAEI,IAAI,CAAChE,MAAM,CAAC;MAC3D,OAAOgE,IAAI,KAAKK,KAAK;IAEvB;MACE,OAAO,KAAK;EAChB;AACF;AAEA,SAASnC,kBAAkBA,CAACrC,IAAI,EAAsB;EAAA,IAApBG,MAAc,GAAAD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAClD,IAAI,OAAOF,IAAI,KAAK,QAAQ,EAAE;IAC5B,OAAOA,IAAI,CAACY,KAAK,CAAC,CAAC,EAAET,MAAM,CAAC;EAC9B,CAAC,MAAM,IAAIwD,WAAW,CAACC,MAAM,CAAC5D,IAAI,CAAC,EAAE;IAEnC,OAAOyE,cAAc,CAACzE,IAAI,CAAC8D,MAAM,EAAE9D,IAAI,CAAC+D,UAAU,EAAE5D,MAAM,CAAC;EAC7D,CAAC,MAAM,IAAIH,IAAI,YAAY2D,WAAW,EAAE;IACtC,MAAMI,UAAU,GAAG,CAAC;IACpB,OAAOU,cAAc,CAACzE,IAAI,EAAE+D,UAAU,EAAE5D,MAAM,CAAC;EACjD;EACA,OAAO,EAAE;AACX;AAEA,SAASsE,cAAcA,CAAC5D,WAAW,EAAEkD,UAAU,EAAE5D,MAAM,EAAE;EACvD,IAAIU,WAAW,CAAC0D,UAAU,GAAGR,UAAU,GAAG5D,MAAM,EAAE;IAChD,OAAO,EAAE;EACX;EACA,MAAMuE,QAAQ,GAAG,IAAIC,QAAQ,CAAC9D,WAAW,CAAC;EAC1C,IAAI2D,KAAK,GAAG,EAAE;EACd,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzE,MAAM,EAAEyE,CAAC,EAAE,EAAE;IAC/BJ,KAAK,IAAIK,MAAM,CAACC,YAAY,CAACJ,QAAQ,CAACK,QAAQ,CAAChB,UAAU,GAAGa,CAAC,CAAC,CAAC;EACjE;EACA,OAAOJ,KAAK;AACd"}