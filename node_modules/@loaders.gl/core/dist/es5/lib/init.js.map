{"version": 3, "file": "init.js", "names": ["_log", "require", "version", "globalThis", "loaders", "log", "concat", "Object", "assign", "VERSION", "_default", "exports", "default"], "sources": ["../../../src/lib/init.ts"], "sourcesContent": ["// loaders.gl, MIT license\nimport {log} from './utils/log';\n\n// @ts-ignore TS2304: Cannot find name '__VERSION__'.\nconst version = typeof __VERSION__ !== 'undefined' ? __VERSION__ : 'latest';\n\n// @ts-ignore\nif (!globalThis.loaders) {\n  log.log(1, `loaders.gl ${version}`)();\n\n  globalThis.loaders = Object.assign(globalThis.loaders || {}, {\n    VERSION: version,\n    log\n  });\n}\n// @ts-ignore\nexport default globalThis.loaders;\n"], "mappings": ";;;;;;AACA,IAAAA,IAAA,GAAAC,OAAA;AAGA,MAAMC,OAAO,GAAG,eAAkB,KAAK,WAAW,cAAiB,QAAQ;AAG3E,IAAI,CAACC,UAAU,CAACC,OAAO,EAAE;EACvBC,QAAG,CAACA,GAAG,CAAC,CAAC,gBAAAC,MAAA,CAAgBJ,OAAO,CAAE,CAAC,CAAC,CAAC;EAErCC,UAAU,CAACC,OAAO,GAAGG,MAAM,CAACC,MAAM,CAACL,UAAU,CAACC,OAAO,IAAI,CAAC,CAAC,EAAE;IAC3DK,OAAO,EAAEP,OAAO;IAChBG,GAAG,EAAHA;EACF,CAAC,CAAC;AACJ;AAAC,IAAAK,QAAA,GAEcP,UAAU,CAACC,OAAO;AAAAO,OAAA,CAAAC,OAAA,GAAAF,QAAA"}