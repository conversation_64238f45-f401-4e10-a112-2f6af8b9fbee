"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _log = require("./utils/log");
const version = typeof "3.4.15" !== 'undefined' ? "3.4.15" : 'latest';
if (!globalThis.loaders) {
  _log.log.log(1, "loaders.gl ".concat(version))();
  globalThis.loaders = Object.assign(globalThis.loaders || {}, {
    VERSION: version,
    log: _log.log
  });
}
var _default = globalThis.loaders;
exports.default = _default;
//# sourceMappingURL=init.js.map