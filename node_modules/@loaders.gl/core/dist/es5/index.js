"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "JSONLoader", {
  enumerable: true,
  get: function () {
    return _loaderUtils.JSONLoader;
  }
});
Object.defineProperty(exports, "NullLoader", {
  enumerable: true,
  get: function () {
    return _nullLoader.NullLoader;
  }
});
Object.defineProperty(exports, "NullWorkerLoader", {
  enumerable: true,
  get: function () {
    return _nullLoader.NullWorkerLoader;
  }
});
Object.defineProperty(exports, "RequestScheduler", {
  enumerable: true,
  get: function () {
    return _loaderUtils.RequestScheduler;
  }
});
Object.defineProperty(exports, "_BrowserFileSystem", {
  enumerable: true,
  get: function () {
    return _browserFilesystem.default;
  }
});
Object.defineProperty(exports, "_fetchProgress", {
  enumerable: true,
  get: function () {
    return _fetchProgress.default;
  }
});
Object.defineProperty(exports, "_unregisterLoaders", {
  enumerable: true,
  get: function () {
    return _registerLoaders._unregisterLoaders;
  }
});
Object.defineProperty(exports, "assert", {
  enumerable: true,
  get: function () {
    return _loaderUtils.assert;
  }
});
Object.defineProperty(exports, "concatenateArrayBuffersAsync", {
  enumerable: true,
  get: function () {
    return _loaderUtils.concatenateArrayBuffersAsync;
  }
});
Object.defineProperty(exports, "document", {
  enumerable: true,
  get: function () {
    return _loaderUtils.document;
  }
});
Object.defineProperty(exports, "encode", {
  enumerable: true,
  get: function () {
    return _encode.encode;
  }
});
Object.defineProperty(exports, "encodeInBatches", {
  enumerable: true,
  get: function () {
    return _encode.encodeInBatches;
  }
});
Object.defineProperty(exports, "encodeSync", {
  enumerable: true,
  get: function () {
    return _encode.encodeSync;
  }
});
Object.defineProperty(exports, "encodeText", {
  enumerable: true,
  get: function () {
    return _encode.encodeText;
  }
});
Object.defineProperty(exports, "encodeURLtoURL", {
  enumerable: true,
  get: function () {
    return _encode.encodeURLtoURL;
  }
});
Object.defineProperty(exports, "fetchFile", {
  enumerable: true,
  get: function () {
    return _fetchFile.fetchFile;
  }
});
Object.defineProperty(exports, "forEach", {
  enumerable: true,
  get: function () {
    return _loaderUtils.forEach;
  }
});
Object.defineProperty(exports, "getLoaderOptions", {
  enumerable: true,
  get: function () {
    return _loaderOptions.getLoaderOptions;
  }
});
Object.defineProperty(exports, "getPathPrefix", {
  enumerable: true,
  get: function () {
    return _loaderUtils.getPathPrefix;
  }
});
Object.defineProperty(exports, "global", {
  enumerable: true,
  get: function () {
    return _loaderUtils.global;
  }
});
Object.defineProperty(exports, "isAsyncIterable", {
  enumerable: true,
  get: function () {
    return _isType.isAsyncIterable;
  }
});
Object.defineProperty(exports, "isBrowser", {
  enumerable: true,
  get: function () {
    return _loaderUtils.isBrowser;
  }
});
Object.defineProperty(exports, "isIterable", {
  enumerable: true,
  get: function () {
    return _isType.isIterable;
  }
});
Object.defineProperty(exports, "isIterator", {
  enumerable: true,
  get: function () {
    return _isType.isIterator;
  }
});
Object.defineProperty(exports, "isPromise", {
  enumerable: true,
  get: function () {
    return _isType.isPromise;
  }
});
Object.defineProperty(exports, "isPureObject", {
  enumerable: true,
  get: function () {
    return _isType.isPureObject;
  }
});
Object.defineProperty(exports, "isReadableStream", {
  enumerable: true,
  get: function () {
    return _isType.isReadableStream;
  }
});
Object.defineProperty(exports, "isResponse", {
  enumerable: true,
  get: function () {
    return _isType.isResponse;
  }
});
Object.defineProperty(exports, "isWorker", {
  enumerable: true,
  get: function () {
    return _loaderUtils.isWorker;
  }
});
Object.defineProperty(exports, "isWritableStream", {
  enumerable: true,
  get: function () {
    return _isType.isWritableStream;
  }
});
Object.defineProperty(exports, "load", {
  enumerable: true,
  get: function () {
    return _load.load;
  }
});
Object.defineProperty(exports, "loadInBatches", {
  enumerable: true,
  get: function () {
    return _loadInBatches.loadInBatches;
  }
});
Object.defineProperty(exports, "makeIterator", {
  enumerable: true,
  get: function () {
    return _makeIterator.makeIterator;
  }
});
Object.defineProperty(exports, "makeLineIterator", {
  enumerable: true,
  get: function () {
    return _loaderUtils.makeLineIterator;
  }
});
Object.defineProperty(exports, "makeNumberedLineIterator", {
  enumerable: true,
  get: function () {
    return _loaderUtils.makeNumberedLineIterator;
  }
});
Object.defineProperty(exports, "makeStream", {
  enumerable: true,
  get: function () {
    return _makeNodeStream.makeStream;
  }
});
Object.defineProperty(exports, "makeTextDecoderIterator", {
  enumerable: true,
  get: function () {
    return _loaderUtils.makeTextDecoderIterator;
  }
});
Object.defineProperty(exports, "makeTextEncoderIterator", {
  enumerable: true,
  get: function () {
    return _loaderUtils.makeTextEncoderIterator;
  }
});
Object.defineProperty(exports, "parse", {
  enumerable: true,
  get: function () {
    return _parse.parse;
  }
});
Object.defineProperty(exports, "parseInBatches", {
  enumerable: true,
  get: function () {
    return _parseInBatches.parseInBatches;
  }
});
Object.defineProperty(exports, "parseSync", {
  enumerable: true,
  get: function () {
    return _parseSync.parseSync;
  }
});
Object.defineProperty(exports, "readArrayBuffer", {
  enumerable: true,
  get: function () {
    return _readArrayBuffer.readArrayBuffer;
  }
});
Object.defineProperty(exports, "readFileSync", {
  enumerable: true,
  get: function () {
    return _readFile.readFileSync;
  }
});
Object.defineProperty(exports, "registerLoaders", {
  enumerable: true,
  get: function () {
    return _registerLoaders.registerLoaders;
  }
});
Object.defineProperty(exports, "resolvePath", {
  enumerable: true,
  get: function () {
    return _loaderUtils.resolvePath;
  }
});
Object.defineProperty(exports, "save", {
  enumerable: true,
  get: function () {
    return _save.save;
  }
});
Object.defineProperty(exports, "saveSync", {
  enumerable: true,
  get: function () {
    return _save.saveSync;
  }
});
Object.defineProperty(exports, "selectLoader", {
  enumerable: true,
  get: function () {
    return _selectLoader.selectLoader;
  }
});
Object.defineProperty(exports, "selectLoaderSync", {
  enumerable: true,
  get: function () {
    return _selectLoader.selectLoaderSync;
  }
});
Object.defineProperty(exports, "self", {
  enumerable: true,
  get: function () {
    return _loaderUtils.self;
  }
});
Object.defineProperty(exports, "setLoaderOptions", {
  enumerable: true,
  get: function () {
    return _loaderOptions.setLoaderOptions;
  }
});
Object.defineProperty(exports, "setPathPrefix", {
  enumerable: true,
  get: function () {
    return _loaderUtils.setPathPrefix;
  }
});
Object.defineProperty(exports, "window", {
  enumerable: true,
  get: function () {
    return _loaderUtils.window;
  }
});
Object.defineProperty(exports, "writeFile", {
  enumerable: true,
  get: function () {
    return _writeFile.writeFile;
  }
});
Object.defineProperty(exports, "writeFileSync", {
  enumerable: true,
  get: function () {
    return _writeFile.writeFileSync;
  }
});
var _fetchFile = require("./lib/fetch/fetch-file");
var _readArrayBuffer = require("./lib/fetch/read-array-buffer");
var _readFile = require("./lib/fetch/read-file");
var _writeFile = require("./lib/fetch/write-file");
var _loaderOptions = require("./lib/api/loader-options");
var _registerLoaders = require("./lib/api/register-loaders");
var _selectLoader = require("./lib/api/select-loader");
var _parse = require("./lib/api/parse");
var _parseSync = require("./lib/api/parse-sync");
var _parseInBatches = require("./lib/api/parse-in-batches");
var _load = require("./lib/api/load");
var _loadInBatches = require("./lib/api/load-in-batches");
var _encode = require("./lib/api/encode");
var _save = require("./lib/api/save");
var _loaderUtils = require("@loaders.gl/loader-utils");
var _makeIterator = require("./iterators/make-iterator/make-iterator");
var _makeNodeStream = require("./iterators/make-stream/make-node-stream");
var _nullLoader = require("./null-loader");
var _fetchProgress = _interopRequireDefault(require("./lib/progress/fetch-progress"));
var _browserFilesystem = _interopRequireDefault(require("./lib/filesystems/browser-filesystem"));
var _isType = require("./javascript-utils/is-type");
//# sourceMappingURL=index.js.map