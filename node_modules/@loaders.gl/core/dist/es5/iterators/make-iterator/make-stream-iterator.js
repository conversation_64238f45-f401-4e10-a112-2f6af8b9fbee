"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.makeStreamIterator = makeStreamIterator;
var _loaderUtils = require("@loaders.gl/loader-utils");
function makeStreamIterator(stream, options) {
  return _loaderUtils.isBrowser ? makeBrowserStreamIterator(stream, options) : makeNodeStreamIterator(stream, options);
}
async function* makeBrowserStreamIterator(stream, options) {
  const reader = stream.getReader();
  let nextBatchPromise;
  try {
    while (true) {
      const currentBatchPromise = nextBatchPromise || reader.read();
      if (options !== null && options !== void 0 && options._streamReadAhead) {
        nextBatchPromise = reader.read();
      }
      const {
        done,
        value
      } = await currentBatchPromise;
      if (done) {
        return;
      }
      yield (0, _loaderUtils.toArrayBuffer)(value);
    }
  } catch (error) {
    reader.releaseLock();
  }
}
async function* makeNodeStreamIterator(stream, options) {
  for await (const chunk of stream) {
    yield (0, _loaderUtils.toArrayBuffer)(chunk);
  }
}
//# sourceMappingURL=make-stream-iterator.js.map