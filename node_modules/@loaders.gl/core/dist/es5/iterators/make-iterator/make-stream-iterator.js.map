{"version": 3, "file": "make-stream-iterator.js", "names": ["_loaderUtils", "require", "makeStreamIterator", "stream", "options", "<PERSON><PERSON><PERSON><PERSON>", "makeBrowserStreamIterator", "makeNodeStreamIterator", "reader", "<PERSON><PERSON><PERSON><PERSON>", "nextBatchPromise", "currentBatchPromise", "read", "_streamReadAhead", "done", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error", "releaseLock", "chunk"], "sources": ["../../../../src/iterators/make-iterator/make-stream-iterator.ts"], "sourcesContent": ["import type {Readable} from 'stream';\nimport {isBrowser, toArrayBuffer} from '@loaders.gl/loader-utils';\n\nexport type StreamIteratorOptions = {\n  _streamReadAhead?: boolean;\n};\n\n/**\n * Returns an async iterable that reads from a stream (works in both Node.js and browsers)\n * @param stream stream to iterator over\n */\nexport function makeStreamIterator(\n  stream: ReadableStream | Readable,\n  options?: StreamIteratorOptions\n): AsyncIterable<ArrayBuffer> {\n  return isBrowser\n    ? makeBrowserStreamIterator(stream as ReadableStream, options)\n    : makeNodeStreamIterator(stream as Readable, options);\n}\n\n/**\n * Returns an async iterable that reads from a DOM (browser) stream\n * @param stream stream to iterate from\n * @see https://jakearchibald.com/2017/async-iterators-and-generators/#making-streams-iterate\n */\nasync function* makeBrowserStreamIterator(\n  stream: ReadableStream,\n  options?: StreamIteratorOptions\n): AsyncIterable<ArrayBuffer> {\n  // WhatWG: stream is supposed to have a `getIterator` method\n  // if (typeof stream.getIterator === 'function') {\n  //   return stream.getIterator();\n  // }\n  // if (typeof stream[Symbol.asyncIterator] === 'function') {\n  //   return makeToArrayBufferIterator(stream);\n  // }\n\n  // In the browser, we first need to get a lock on the stream\n  const reader = stream.getReader();\n\n  let nextBatchPromise: Promise<{done?: boolean; value?: Uint8Array}> | undefined;\n\n  try {\n    // eslint-disable-next-line no-constant-condition\n    while (true) {\n      const currentBatchPromise = nextBatchPromise || reader.read();\n      // Issue a read for an additional batch, while we await the next batch\n      // Idea is to make fetching happen in parallel with processing / parsing\n      if (options?._streamReadAhead) {\n        nextBatchPromise = reader.read();\n      }\n      // Read from the stream\n      // value is a Uint8Array\n      const {done, value} = await currentBatchPromise;\n      // Exit if we're done\n      if (done) {\n        return;\n      }\n      // Else yield the chunk\n      yield toArrayBuffer(value);\n    }\n  } catch (error) {\n    // TODO - examples makes it look like this should always be called,\n    // but that generates exceptions so only call it if we do not reach the end\n    reader.releaseLock();\n  }\n}\n\n/**\n * Returns an async iterable that reads from a DOM (browser) stream\n * @param stream stream to iterate from\n * @note Requires Node.js >= 10\n */\nasync function* makeNodeStreamIterator(\n  stream: Readable,\n  options?: StreamIteratorOptions\n): AsyncIterable<ArrayBuffer> {\n  // Hacky test for node version to ensure we don't call bad polyfills\n  // NODE 10+: stream is an asyncIterator\n  for await (const chunk of stream) {\n    yield toArrayBuffer(chunk); // Coerce each chunk to ArrayBuffer\n  }\n}\n/* TODO - remove NODE < 10\n * @see https://github.com/bustle/streaming-iterables, MIT license\n *\n  if (typeof stream[Symbol.asyncIterator] === 'function') {\n    return;\n  }\n\n  // TODO - check if is this ever used in Node 10+?\n  // eslint-disable-next-line no-constant-condition\n  while (true) {\n    const data = stream.read();\n    if (data !== null) {\n      yield toArrayBuffer(data);\n      // eslint-disable-next-line no-continue\n      continue;\n    }\n    if (stream._readableState?.ended) {\n      return;\n    }\n    await onceReadable(stream);\n  }\n\nasync function onceReadable(stream: Readable): Promise<any> {\n  return new Promise((resolve) => {\n    stream.once('readable', resolve);\n  });\n}\n  */\n"], "mappings": ";;;;;;AACA,IAAAA,YAAA,GAAAC,OAAA;AAUO,SAASC,kBAAkBA,CAChCC,MAAiC,EACjCC,OAA+B,EACH;EAC5B,OAAOC,sBAAS,GACZC,yBAAyB,CAACH,MAAM,EAAoBC,OAAO,CAAC,GAC5DG,sBAAsB,CAACJ,MAAM,EAAcC,OAAO,CAAC;AACzD;AAOA,gBAAgBE,yBAAyBA,CACvCH,MAAsB,EACtBC,OAA+B,EACH;EAU5B,MAAMI,MAAM,GAAGL,MAAM,CAACM,SAAS,CAAC,CAAC;EAEjC,IAAIC,gBAA2E;EAE/E,IAAI;IAEF,OAAO,IAAI,EAAE;MACX,MAAMC,mBAAmB,GAAGD,gBAAgB,IAAIF,MAAM,CAACI,IAAI,CAAC,CAAC;MAG7D,IAAIR,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAES,gBAAgB,EAAE;QAC7BH,gBAAgB,GAAGF,MAAM,CAACI,IAAI,CAAC,CAAC;MAClC;MAGA,MAAM;QAACE,IAAI;QAAEC;MAAK,CAAC,GAAG,MAAMJ,mBAAmB;MAE/C,IAAIG,IAAI,EAAE;QACR;MACF;MAEA,MAAM,IAAAE,0BAAa,EAACD,KAAK,CAAC;IAC5B;EACF,CAAC,CAAC,OAAOE,KAAK,EAAE;IAGdT,MAAM,CAACU,WAAW,CAAC,CAAC;EACtB;AACF;AAOA,gBAAgBX,sBAAsBA,CACpCJ,MAAgB,EAChBC,OAA+B,EACH;EAG5B,WAAW,MAAMe,KAAK,IAAIhB,MAAM,EAAE;IAChC,MAAM,IAAAa,0BAAa,EAACG,KAAK,CAAC;EAC5B;AACF"}