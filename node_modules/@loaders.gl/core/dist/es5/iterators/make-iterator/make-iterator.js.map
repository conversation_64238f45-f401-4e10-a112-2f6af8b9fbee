{"version": 3, "file": "make-iterator.js", "names": ["_makeStringIterator", "require", "_makeArrayBufferIterator", "_makeBlobIterator", "_makeStreamIterator", "_isType", "makeIterator", "data", "options", "makeStringIterator", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "makeArrayBufferIterator", "isBlob", "makeBlobIterator", "isReadableStream", "makeStreamIterator", "isResponse", "response", "body", "Error"], "sources": ["../../../../src/iterators/make-iterator/make-iterator.ts"], "sourcesContent": ["import type {ReadStream} from 'fs';\n\nimport {makeStringIterator} from './make-string-iterator';\nimport {makeArrayBufferIterator} from './make-array-buffer-iterator';\nimport {makeBlobIterator} from './make-blob-iterator';\nimport type {StreamIteratorOptions} from './make-stream-iterator';\nimport {makeStreamIterator} from './make-stream-iterator';\nimport {isBlob, isReadableStream, isResponse} from '../../javascript-utils/is-type';\n\n/**\n * @param [options.chunkSize]\n */\nexport type IteratorOptions = StreamIteratorOptions & {\n  chunkSize?: number;\n};\n\n/**\n * Returns an iterator that breaks its input into chunks and yields them one-by-one.\n * @param data\n * @param options\n * @returns\n * This function can e.g. be used to enable data sources that can only be read atomically\n * (such as `Blob` and `File` via `FileReader`) to still be parsed in batches.\n */\nexport function makeIterator(\n  data: ArrayBuffer | string | Blob | Response | ReadableStream | ReadStream,\n  options?: IteratorOptions\n): AsyncIterable<ArrayBuffer> | Iterable<ArrayBuffer> {\n  if (typeof data === 'string') {\n    // Note: Converts string chunks to binary\n    return makeStringIterator(data, options);\n  }\n  if (data instanceof ArrayBuffer) {\n    return makeArrayBufferIterator(data, options);\n  }\n  if (isBlob(data)) {\n    return makeBlobIterator(data as Blob, options);\n  }\n  if (isReadableStream(data)) {\n    return makeStreamIterator(data as ReadableStream, options);\n  }\n  if (isResponse(data)) {\n    const response = data as Response;\n    return makeStreamIterator(response.body as ReadableStream, options);\n  }\n  throw new Error('makeIterator');\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,mBAAA,GAAAC,OAAA;AACA,IAAAC,wBAAA,GAAAD,OAAA;AACA,IAAAE,iBAAA,GAAAF,OAAA;AAEA,IAAAG,mBAAA,GAAAH,OAAA;AACA,IAAAI,OAAA,GAAAJ,OAAA;AAiBO,SAASK,YAAYA,CAC1BC,IAA0E,EAC1EC,OAAyB,EAC2B;EACpD,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;IAE5B,OAAO,IAAAE,sCAAkB,EAACF,IAAI,EAAEC,OAAO,CAAC;EAC1C;EACA,IAAID,IAAI,YAAYG,WAAW,EAAE;IAC/B,OAAO,IAAAC,gDAAuB,EAACJ,IAAI,EAAEC,OAAO,CAAC;EAC/C;EACA,IAAI,IAAAI,cAAM,EAACL,IAAI,CAAC,EAAE;IAChB,OAAO,IAAAM,kCAAgB,EAACN,IAAI,EAAUC,OAAO,CAAC;EAChD;EACA,IAAI,IAAAM,wBAAgB,EAACP,IAAI,CAAC,EAAE;IAC1B,OAAO,IAAAQ,sCAAkB,EAACR,IAAI,EAAoBC,OAAO,CAAC;EAC5D;EACA,IAAI,IAAAQ,kBAAU,EAACT,IAAI,CAAC,EAAE;IACpB,MAAMU,QAAQ,GAAGV,IAAgB;IACjC,OAAO,IAAAQ,sCAAkB,EAACE,QAAQ,CAACC,IAAI,EAAoBV,OAAO,CAAC;EACrE;EACA,MAAM,IAAIW,KAAK,CAAC,cAAc,CAAC;AACjC"}