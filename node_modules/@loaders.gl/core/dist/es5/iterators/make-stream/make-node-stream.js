"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.makeStream = makeStream;
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var Stream = _interopRequireWildcard(require("stream"));
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
class _Readable {}
const Readable = Stream.Readable || _Readable;
function makeStream(source, options) {
  const iterator = source[Symbol.asyncIterator] ? source[Symbol.asyncIterator]() : source[Symbol.iterator]();
  return new AsyncIterableReadable(iterator, options);
}
class AsyncIterableReadable extends Readable {
  constructor(it, options) {
    super(options);
    (0, _defineProperty2.default)(this, "_pulling", void 0);
    (0, _defineProperty2.default)(this, "_bytesMode", void 0);
    (0, _defineProperty2.default)(this, "_iterator", void 0);
    this._iterator = it;
    this._pulling = false;
    this._bytesMode = !options || !options.objectMode;
  }
  async _read(size) {
    if (!this._pulling) {
      this._pulling = true;
      this._pulling = await this._pull(size, this._iterator);
    }
  }
  async _destroy(error, cb) {
    if (!this._iterator) {
      return;
    }
    if (error) {
      var _this$_iterator, _this$_iterator$throw;
      await ((_this$_iterator = this._iterator) === null || _this$_iterator === void 0 ? void 0 : (_this$_iterator$throw = _this$_iterator.throw) === null || _this$_iterator$throw === void 0 ? void 0 : _this$_iterator$throw.call(_this$_iterator, error));
    } else {
      var _this$_iterator2, _this$_iterator2$retu;
      await ((_this$_iterator2 = this._iterator) === null || _this$_iterator2 === void 0 ? void 0 : (_this$_iterator2$retu = _this$_iterator2.return) === null || _this$_iterator2$retu === void 0 ? void 0 : _this$_iterator2$retu.call(_this$_iterator2, error));
    }
    cb === null || cb === void 0 ? void 0 : cb(null);
  }
  async _pull(size, it) {
    var _r;
    const bm = this._bytesMode;
    let r = null;
    while (this.readable && !(r = await it.next()).done) {
      if (size !== null) {
        size -= bm && ArrayBuffer.isView(r.value) ? r.value.byteLength : 1;
      }
      if (!this.push(new Uint8Array(r.value)) || size <= 0) {
        break;
      }
    }
    if (((_r = r) !== null && _r !== void 0 && _r.done || !this.readable) && (this.push(null) || true)) {
      var _it$return;
      it === null || it === void 0 ? void 0 : (_it$return = it.return) === null || _it$return === void 0 ? void 0 : _it$return.call(it);
    }
    return !this.readable;
  }
}
//# sourceMappingURL=make-node-stream.js.map