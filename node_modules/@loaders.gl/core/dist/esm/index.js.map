{"version": 3, "file": "index.js", "names": ["fetchFile", "read<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "readFileSync", "writeFile", "writeFileSync", "setLoaderOptions", "getLoaderOptions", "registerLoaders", "<PERSON><PERSON><PERSON><PERSON>", "selectLoaderSync", "parse", "parseSync", "parseInBatches", "load", "loadInBatches", "encode", "encodeSync", "encodeInBatches", "encodeText", "encodeURLtoURL", "save", "saveSync", "setPathPrefix", "getPathPrefix", "<PERSON><PERSON><PERSON>", "RequestScheduler", "makeIterator", "makeStream", "NullWorkerLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "JSONLoader", "default", "_fetchProgress", "_BrowserFileSystem", "_unregisterLoaders", "<PERSON><PERSON><PERSON><PERSON>", "isWorker", "self", "window", "global", "document", "assert", "for<PERSON>ach", "concatenateArrayBuffersAsync", "makeTextDecoderIterator", "makeTextEncoderIterator", "makeLineIterator", "makeNumberedLineIterator", "isPureObject", "isPromise", "isIterable", "isAsyncIterable", "isIterator", "isResponse", "isReadableStream", "isWritableStream"], "sources": ["../../src/index.ts"], "sourcesContent": ["// loaders.gl, MIT license\n\n// TYPES\nexport type {\n  Loader,\n  LoaderWithParser,\n  LoaderContext,\n  LoaderOptions,\n  Writer,\n  WriterOptions,\n  DataType,\n  SyncDataType,\n  BatchableDataType,\n  IFileSystem,\n  IRandomAccessReadFileSystem\n} from '@loaders.gl/loader-utils';\n\n// FILE READING AND WRITING\nexport {fetchFile} from './lib/fetch/fetch-file';\n\nexport {readArrayBuffer} from './lib/fetch/read-array-buffer';\nexport {readFileSync} from './lib/fetch/read-file';\nexport {writeFile, writeFileSync} from './lib/fetch/write-file';\n\n// CONFIGURATION\nexport {setLoaderOptions, getLoaderOptions} from './lib/api/loader-options';\nexport {registerLoaders} from './lib/api/register-loaders';\nexport {selectLoader, selectLoaderSync} from './lib/api/select-loader';\n\n// LOADING (READING + PARSING)\nexport {parse} from './lib/api/parse';\nexport {parseSync} from './lib/api/parse-sync';\nexport {parseInBatches} from './lib/api/parse-in-batches';\n\nexport {load} from './lib/api/load';\nexport {loadInBatches} from './lib/api/load-in-batches';\n\n// ENCODING (ENCODING AND WRITING)\nexport {encode, encodeSync, encodeInBatches, encodeText, encodeURLtoURL} from './lib/api/encode';\nexport {save, saveSync} from './lib/api/save';\n\n// CORE UTILS SHARED WITH LOADERS (RE-EXPORTED FROM LOADER-UTILS)\nexport {setPathPrefix, getPathPrefix, resolvePath} from '@loaders.gl/loader-utils';\nexport {RequestScheduler} from '@loaders.gl/loader-utils';\n\n// ITERATOR UTILS\nexport {makeIterator} from './iterators/make-iterator/make-iterator';\nexport {makeStream} from './iterators/make-stream/make-node-stream';\n\n// CORE LOADERS\nexport {NullWorkerLoader, NullLoader} from './null-loader';\nexport {JSONLoader} from '@loaders.gl/loader-utils';\n\n// EXPERIMENTAL\nexport {default as _fetchProgress} from './lib/progress/fetch-progress';\nexport {default as _BrowserFileSystem} from './lib/filesystems/browser-filesystem';\n\n// FOR TESTING\nexport {_unregisterLoaders} from './lib/api/register-loaders';\n\n//\n// TODO - MOVE TO LOADER-UTILS AND DEPRECATE IN CORE?\n//\n\nexport {isBrowser, isWorker, self, window, global, document} from '@loaders.gl/loader-utils';\nexport {assert} from '@loaders.gl/loader-utils';\nexport {forEach, concatenateArrayBuffersAsync} from '@loaders.gl/loader-utils';\n\nexport {\n  makeTextDecoderIterator,\n  makeTextEncoderIterator,\n  makeLineIterator,\n  makeNumberedLineIterator\n} from '@loaders.gl/loader-utils';\n\n// \"JAVASCRIPT\" UTILS - move to loader-utils?\nexport {\n  isPureObject,\n  isPromise,\n  isIterable,\n  isAsyncIterable,\n  isIterator,\n  isResponse,\n  isReadableStream,\n  isWritableStream\n} from './javascript-utils/is-type';\n"], "mappings": "AAkBA,SAAQA,SAAS,QAAO,wBAAwB;AAEhD,SAAQC,eAAe,QAAO,+BAA+B;AAC7D,SAAQC,YAAY,QAAO,uBAAuB;AAClD,SAAQC,SAAS,EAAEC,aAAa,QAAO,wBAAwB;AAG/D,SAAQC,gBAAgB,EAAEC,gBAAgB,QAAO,0BAA0B;AAC3E,SAAQC,eAAe,QAAO,4BAA4B;AAC1D,SAAQC,YAAY,EAAEC,gBAAgB,QAAO,yBAAyB;AAGtE,SAAQC,KAAK,QAAO,iBAAiB;AACrC,SAAQC,SAAS,QAAO,sBAAsB;AAC9C,SAAQC,cAAc,QAAO,4BAA4B;AAEzD,SAAQC,IAAI,QAAO,gBAAgB;AACnC,SAAQC,aAAa,QAAO,2BAA2B;AAGvD,SAAQC,MAAM,EAAEC,UAAU,EAAEC,eAAe,EAAEC,UAAU,EAAEC,cAAc,QAAO,kBAAkB;AAChG,SAAQC,IAAI,EAAEC,QAAQ,QAAO,gBAAgB;AAG7C,SAAQC,aAAa,EAAEC,aAAa,EAAEC,WAAW,QAAO,0BAA0B;AAClF,SAAQC,gBAAgB,QAAO,0BAA0B;AAGzD,SAAQC,YAAY,QAAO,yCAAyC;AACpE,SAAQC,UAAU,QAAO,0CAA0C;AAGnE,SAAQC,gBAAgB,EAAEC,UAAU,QAAO,eAAe;AAC1D,SAAQC,UAAU,QAAO,0BAA0B;AAGnD,SAAQC,OAAO,IAAIC,cAAc,QAAO,+BAA+B;AACvE,SAAQD,OAAO,IAAIE,kBAAkB,QAAO,sCAAsC;AAGlF,SAAQC,kBAAkB,QAAO,4BAA4B;AAM7D,SAAQC,SAAS,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,QAAO,0BAA0B;AAC5F,SAAQC,MAAM,QAAO,0BAA0B;AAC/C,SAAQC,OAAO,EAAEC,4BAA4B,QAAO,0BAA0B;AAE9E,SACEC,uBAAuB,EACvBC,uBAAuB,EACvBC,gBAAgB,EAChBC,wBAAwB,QACnB,0BAA0B;AAGjC,SACEC,YAAY,EACZC,SAAS,EACTC,UAAU,EACVC,eAAe,EACfC,UAAU,EACVC,UAAU,EACVC,gBAAgB,EAChBC,gBAAgB,QACX,4BAA4B"}