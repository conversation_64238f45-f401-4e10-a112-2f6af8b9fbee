const VERSION = typeof "3.4.15" !== 'undefined' ? "3.4.15" : 'latest';
export const NullWorkerLoader = {
  name: 'Null loader',
  id: 'null',
  module: 'core',
  version: VERSION,
  worker: true,
  mimeTypes: ['application/x.empty'],
  extensions: ['null'],
  tests: [() => false],
  options: {
    null: {}
  }
};
function parseSync(arrayBuffer, options, context) {
  if (!options.null.echoParameters) return null;
  context = context && JSON.parse(JSON.stringify(context));
  return {
    arrayBuffer,
    options,
    context
  };
}
export const NullLoader = {
  name: 'Null loader',
  id: 'null',
  module: 'core',
  version: VERSION,
  mimeTypes: ['application/x.empty'],
  extensions: ['null'],
  parse: async (arrayBuffer, options, context) => parseSync(arrayBuffer, options, context),
  parseSync,
  parseInBatches: async function* generator(asyncIterator, options, context) {
    for await (const batch of asyncIterator) {
      yield parseSync(batch, options, context);
    }
  },
  tests: [() => false],
  options: {
    null: {
      echoParameters: false
    }
  }
};
//# sourceMappingURL=null-loader.js.map