{"version": 3, "file": "timed-batch-iterator.js", "names": ["timedBatchIterator", "batchIterator", "timeout", "start", "Date", "now", "batches", "batch", "push"], "sources": ["../../../../src/iterators/batch-iterators/timed-batch-iterator.ts"], "sourcesContent": ["/**\n * \"Debounces\" batches and returns them in groups\n */\nexport async function* timedBatchIterator<Batch>(batchIterator: AsyncIterable<Batch>, timeout) {\n  let start = Date.now();\n  let batches: Batch[] = [];\n  for await (const batch of batchIterator) {\n    batches.push(batch);\n    if (Date.now() - start > timeout) {\n      yield batches;\n      start = Date.now();\n      batches = [];\n    }\n  }\n\n  if (batches) {\n    yield batches;\n  }\n}\n"], "mappings": "AAGA,OAAO,gBAAgBA,kBAAkBA,CAAQC,aAAmC,EAAEC,OAAO,EAAE;EAC7F,IAAIC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;EACtB,IAAIC,OAAgB,GAAG,EAAE;EACzB,WAAW,MAAMC,KAAK,IAAIN,aAAa,EAAE;IACvCK,OAAO,CAACE,IAAI,CAACD,KAAK,CAAC;IACnB,IAAIH,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,KAAK,GAAGD,OAAO,EAAE;MAChC,MAAMI,OAAO;MACbH,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MAClBC,OAAO,GAAG,EAAE;IACd;EACF;EAEA,IAAIA,OAAO,EAAE;IACX,MAAMA,OAAO;EACf;AACF"}