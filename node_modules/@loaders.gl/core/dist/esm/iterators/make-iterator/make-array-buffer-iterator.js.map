{"version": 3, "file": "make-array-buffer-iterator.js", "names": ["DEFAULT_CHUNK_SIZE", "makeArrayBufferIterator", "arrayBuffer", "options", "arguments", "length", "undefined", "chunkSize", "byteOffset", "byteLength", "chunkByteLength", "Math", "min", "chunk", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceArray", "Uint8Array", "chunkArray", "set"], "sources": ["../../../../src/iterators/make-iterator/make-array-buffer-iterator.ts"], "sourcesContent": ["import type {IteratorOptions} from './make-iterator';\n\nconst DEFAULT_CHUNK_SIZE = 256 * 1024;\n\n/**\n * Returns an iterator that breaks a big ArrayBuffer into chunks and yields them one-by-one\n * @param blob ArrayBuffer to iterate over\n * @param options\n * @param options.chunkSize\n */\nexport function* makeArrayBufferIterator(\n  arrayBuffer: ArrayBuffer,\n  options: IteratorOptions = {}\n): Iterable<ArrayBuffer> {\n  const {chunkSize = DEFAULT_CHUNK_SIZE} = options;\n\n  let byteOffset = 0;\n\n  while (byteOffset < arrayBuffer.byteLength) {\n    // Create a chunk of the right size\n    const chunkByteLength = Math.min(arrayBuffer.byteLength - byteOffset, chunkSize);\n    const chunk = new ArrayBuffer(chunkByteLength);\n\n    // Copy data from the big chunk\n    const sourceArray = new Uint8Array(arrayBuffer, byteOffset, chunkByteLength);\n    const chunkArray = new Uint8Array(chunk);\n    chunkArray.set(sourceArray);\n\n    // yield the chunk\n    byteOffset += chunkByteLength;\n    yield chunk;\n  }\n}\n"], "mappings": "AAEA,MAAMA,kBAAkB,GAAG,GAAG,GAAG,IAAI;AAQrC,OAAO,SAAUC,uBAAuBA,CACtCC,WAAwB;EAAA,IACxBC,OAAwB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAAA,oBACN;IACvB,MAAM;MAACG,SAAS,GAAGP;IAAkB,CAAC,GAAGG,OAAO;IAEhD,IAAIK,UAAU,GAAG,CAAC;IAElB,OAAOA,UAAU,GAAGN,WAAW,CAACO,UAAU,EAAE;MAE1C,MAAMC,eAAe,GAAGC,IAAI,CAACC,GAAG,CAACV,WAAW,CAACO,UAAU,GAAGD,UAAU,EAAED,SAAS,CAAC;MAChF,MAAMM,KAAK,GAAG,IAAIC,WAAW,CAACJ,eAAe,CAAC;MAG9C,MAAMK,WAAW,GAAG,IAAIC,UAAU,CAACd,WAAW,EAAEM,UAAU,EAAEE,eAAe,CAAC;MAC5E,MAAMO,UAAU,GAAG,IAAID,UAAU,CAACH,KAAK,CAAC;MACxCI,UAAU,CAACC,GAAG,CAACH,WAAW,CAAC;MAG3BP,UAAU,IAAIE,eAAe;MAC7B,MAAMG,KAAK;IACb;EACF,CAAC;AAAA"}