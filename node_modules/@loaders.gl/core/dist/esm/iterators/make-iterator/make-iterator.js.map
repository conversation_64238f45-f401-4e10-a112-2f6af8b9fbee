{"version": 3, "file": "make-iterator.js", "names": ["makeStringIterator", "makeArrayBufferIterator", "makeBlobIterator", "makeStreamIterator", "isBlob", "isReadableStream", "isResponse", "makeIterator", "data", "options", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "response", "body", "Error"], "sources": ["../../../../src/iterators/make-iterator/make-iterator.ts"], "sourcesContent": ["import type {ReadStream} from 'fs';\n\nimport {makeStringIterator} from './make-string-iterator';\nimport {makeArrayBufferIterator} from './make-array-buffer-iterator';\nimport {makeBlobIterator} from './make-blob-iterator';\nimport type {StreamIteratorOptions} from './make-stream-iterator';\nimport {makeStreamIterator} from './make-stream-iterator';\nimport {isBlob, isReadableStream, isResponse} from '../../javascript-utils/is-type';\n\n/**\n * @param [options.chunkSize]\n */\nexport type IteratorOptions = StreamIteratorOptions & {\n  chunkSize?: number;\n};\n\n/**\n * Returns an iterator that breaks its input into chunks and yields them one-by-one.\n * @param data\n * @param options\n * @returns\n * This function can e.g. be used to enable data sources that can only be read atomically\n * (such as `Blob` and `File` via `FileReader`) to still be parsed in batches.\n */\nexport function makeIterator(\n  data: ArrayBuffer | string | Blob | Response | ReadableStream | ReadStream,\n  options?: IteratorOptions\n): AsyncIterable<ArrayBuffer> | Iterable<ArrayBuffer> {\n  if (typeof data === 'string') {\n    // Note: Converts string chunks to binary\n    return makeStringIterator(data, options);\n  }\n  if (data instanceof ArrayBuffer) {\n    return makeArrayBufferIterator(data, options);\n  }\n  if (isBlob(data)) {\n    return makeBlobIterator(data as Blob, options);\n  }\n  if (isReadableStream(data)) {\n    return makeStreamIterator(data as ReadableStream, options);\n  }\n  if (isResponse(data)) {\n    const response = data as Response;\n    return makeStreamIterator(response.body as ReadableStream, options);\n  }\n  throw new Error('makeIterator');\n}\n"], "mappings": "AAEA,SAAQA,kBAAkB,QAAO,wBAAwB;AACzD,SAAQC,uBAAuB,QAAO,8BAA8B;AACpE,SAAQC,gBAAgB,QAAO,sBAAsB;AAErD,SAAQC,kBAAkB,QAAO,wBAAwB;AACzD,SAAQC,MAAM,EAAEC,gBAAgB,EAAEC,UAAU,QAAO,gCAAgC;AAiBnF,OAAO,SAASC,YAAYA,CAC1BC,IAA0E,EAC1EC,OAAyB,EAC2B;EACpD,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;IAE5B,OAAOR,kBAAkB,CAACQ,IAAI,EAAEC,OAAO,CAAC;EAC1C;EACA,IAAID,IAAI,YAAYE,WAAW,EAAE;IAC/B,OAAOT,uBAAuB,CAACO,IAAI,EAAEC,OAAO,CAAC;EAC/C;EACA,IAAIL,MAAM,CAACI,IAAI,CAAC,EAAE;IAChB,OAAON,gBAAgB,CAACM,IAAI,EAAUC,OAAO,CAAC;EAChD;EACA,IAAIJ,gBAAgB,CAACG,IAAI,CAAC,EAAE;IAC1B,OAAOL,kBAAkB,CAACK,IAAI,EAAoBC,OAAO,CAAC;EAC5D;EACA,IAAIH,UAAU,CAACE,IAAI,CAAC,EAAE;IACpB,MAAMG,QAAQ,GAAGH,IAAgB;IACjC,OAAOL,kBAAkB,CAACQ,QAAQ,CAACC,IAAI,EAAoBH,OAAO,CAAC;EACrE;EACA,MAAM,IAAII,KAAK,CAAC,cAAc,CAAC;AACjC"}