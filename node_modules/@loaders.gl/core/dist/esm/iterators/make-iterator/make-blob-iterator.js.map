{"version": 3, "file": "make-blob-iterator.js", "names": ["DEFAULT_CHUNK_SIZE", "makeBlobIterator", "blob", "options", "chunkSize", "offset", "size", "end", "chunk", "slice", "arrayBuffer"], "sources": ["../../../../src/iterators/make-iterator/make-blob-iterator.ts"], "sourcesContent": ["import type {IteratorOptions} from './make-iterator';\n\nconst DEFAULT_CHUNK_SIZE = 1024 * 1024; // 1MB — biggest value that keeps UI responsive\n\n/**\n * Returns an iterator that breaks a big Blob into chunks and yields them one-by-one\n * @param blob Blob or File object\n * @param options\n * @param options.chunkSize\n */\nexport async function* makeBlobIterator(\n  blob: Blob,\n  options?: IteratorOptions\n): AsyncIterable<ArrayBuffer> {\n  const chunkSize = options?.chunkSize || DEFAULT_CHUNK_SIZE;\n\n  let offset = 0;\n  while (offset < blob.size) {\n    const end = offset + chunkSize;\n\n    const chunk = await blob.slice(offset, end).arrayBuffer();\n\n    offset = end;\n    yield chunk;\n  }\n}\n"], "mappings": "AAEA,MAAMA,kBAAkB,GAAG,IAAI,GAAG,IAAI;AAQtC,OAAO,gBAAgBC,gBAAgBA,CACrCC,IAAU,EACVC,OAAyB,EACG;EAC5B,MAAMC,SAAS,GAAG,CAAAD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEC,SAAS,KAAIJ,kBAAkB;EAE1D,IAAIK,MAAM,GAAG,CAAC;EACd,OAAOA,MAAM,GAAGH,IAAI,CAACI,IAAI,EAAE;IACzB,MAAMC,GAAG,GAAGF,MAAM,GAAGD,SAAS;IAE9B,MAAMI,KAAK,GAAG,MAAMN,IAAI,CAACO,KAAK,CAACJ,MAAM,EAAEE,GAAG,CAAC,CAACG,WAAW,CAAC,CAAC;IAEzDL,MAAM,GAAGE,GAAG;IACZ,MAAMC,KAAK;EACb;AACF"}