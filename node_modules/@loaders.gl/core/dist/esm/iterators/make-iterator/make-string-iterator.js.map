{"version": 3, "file": "make-string-iterator.js", "names": ["DEFAULT_CHUNK_SIZE", "makeStringIterator", "string", "options", "chunkSize", "offset", "textEncoder", "TextEncoder", "length", "chunkLength", "Math", "min", "chunk", "slice", "encode"], "sources": ["../../../../src/iterators/make-iterator/make-string-iterator.ts"], "sourcesContent": ["import type {IteratorOptions} from './make-iterator';\n\nconst DEFAULT_CHUNK_SIZE = 256 * 1024;\n\n/**\n * Returns an iterator that breaks a big string into chunks and yields them one-by-one as ArrayBuffers\n * @param blob string to iterate over\n * @param options\n * @param options.chunkSize\n */\nexport function* makeStringIterator(\n  string: string,\n  options?: IteratorOptions\n): Iterable<ArrayBuffer> {\n  const chunkSize = options?.chunkSize || DEFAULT_CHUNK_SIZE;\n\n  let offset = 0;\n  const textEncoder = new TextEncoder();\n  while (offset < string.length) {\n    // Create a chunk of the right size\n    const chunkLength = Math.min(string.length - offset, chunkSize);\n    const chunk = string.slice(offset, offset + chunkLength);\n    offset += chunkLength;\n\n    // yield an ArrayBuffer chunk\n    yield textEncoder.encode(chunk);\n  }\n}\n"], "mappings": "AAEA,MAAMA,kBAAkB,GAAG,GAAG,GAAG,IAAI;AAQrC,OAAO,UAAUC,kBAAkBA,CACjCC,MAAc,EACdC,OAAyB,EACF;EACvB,MAAMC,SAAS,GAAG,CAAAD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEC,SAAS,KAAIJ,kBAAkB;EAE1D,IAAIK,MAAM,GAAG,CAAC;EACd,MAAMC,WAAW,GAAG,IAAIC,WAAW,CAAC,CAAC;EACrC,OAAOF,MAAM,GAAGH,MAAM,CAACM,MAAM,EAAE;IAE7B,MAAMC,WAAW,GAAGC,IAAI,CAACC,GAAG,CAACT,MAAM,CAACM,MAAM,GAAGH,MAAM,EAAED,SAAS,CAAC;IAC/D,MAAMQ,KAAK,GAAGV,MAAM,CAACW,KAAK,CAACR,MAAM,EAAEA,MAAM,GAAGI,WAAW,CAAC;IACxDJ,MAAM,IAAII,WAAW;IAGrB,MAAMH,WAAW,CAACQ,MAAM,CAACF,KAAK,CAAC;EACjC;AACF"}