{"version": 3, "file": "make-dom-stream.js", "names": ["makeStream", "source", "options", "iterator", "Symbol", "asyncIterator", "ReadableStream", "type", "pull", "controller", "done", "value", "next", "close", "enqueue", "Uint8Array", "error", "cancel", "_iterator$return", "return", "call", "highWaterMark"], "sources": ["../../../../src/iterators/make-stream/make-dom-stream.ts"], "sourcesContent": ["export type MakeStreamOptions = {\n  /** Stream allocates an arrayBuffer. Enables use of a default reader. */\n  autoAllocateChunkSize?: number;\n  /** Total number of chunks in queue before back pressure is applied */\n  highWaterMark?: number;\n};\n\n/**\n * Builds a DOM stream from an iterator\n * This stream is currently used in browsers only,\n * but note that Web stream support is present in Node from Node 16\n * https://nodejs.org/api/webstreams.html#webstreams_web_streams_api\n */\nexport function makeStream<ArrayBuffer>(\n  source: Iterable<ArrayBuffer> | AsyncIterable<ArrayBuffer>,\n  options?: MakeStreamOptions\n): ReadableStream {\n  const iterator = source[Symbol.asyncIterator]\n    ? (source as AsyncIterable<ArrayBuffer>)[Symbol.asyncIterator]()\n    : (source as Iterable<ArrayBuffer>)[Symbol.iterator]();\n\n  return new ReadableStream<Uint8Array>(\n    {\n      // Create a byte stream (enables `Response(stream).arrayBuffer()`)\n      // Only supported on Chrome\n      // See: https://developer.mozilla.org/en-US/docs/Web/API/ReadableByteStreamController\n      type: 'bytes',\n\n      async pull(controller) {\n        try {\n          const {done, value} = await iterator.next();\n          if (done) {\n            controller.close();\n          } else {\n            // TODO - ignores controller.desiredSize\n            // @ts-expect-error Unclear why value is not correctly typed\n            controller.enqueue(new Uint8Array(value));\n          }\n        } catch (error) {\n          controller.error(error);\n        }\n      },\n\n      async cancel() {\n        await iterator?.return?.();\n      }\n    },\n    // options: QueingStrategy<Uint8Array>\n    {\n      // This is bytes, not chunks\n      highWaterMark: 2 ** 24,\n      ...options\n    }\n  );\n}\n"], "mappings": "AAaA,OAAO,SAASA,UAAUA,CACxBC,MAA0D,EAC1DC,OAA2B,EACX;EAChB,MAAMC,QAAQ,GAAGF,MAAM,CAACG,MAAM,CAACC,aAAa,CAAC,GACxCJ,MAAM,CAAgCG,MAAM,CAACC,aAAa,CAAC,CAAC,CAAC,GAC7DJ,MAAM,CAA2BG,MAAM,CAACD,QAAQ,CAAC,CAAC,CAAC;EAExD,OAAO,IAAIG,cAAc,CACvB;IAIEC,IAAI,EAAE,OAAO;IAEb,MAAMC,IAAIA,CAACC,UAAU,EAAE;MACrB,IAAI;QACF,MAAM;UAACC,IAAI;UAAEC;QAAK,CAAC,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;QAC3C,IAAIF,IAAI,EAAE;UACRD,UAAU,CAACI,KAAK,CAAC,CAAC;QACpB,CAAC,MAAM;UAGLJ,UAAU,CAACK,OAAO,CAAC,IAAIC,UAAU,CAACJ,KAAK,CAAC,CAAC;QAC3C;MACF,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdP,UAAU,CAACO,KAAK,CAACA,KAAK,CAAC;MACzB;IACF,CAAC;IAED,MAAMC,MAAMA,CAAA,EAAG;MAAA,IAAAC,gBAAA;MACb,OAAMf,QAAQ,aAARA,QAAQ,wBAAAe,gBAAA,GAARf,QAAQ,CAAEgB,MAAM,cAAAD,gBAAA,uBAAhBA,gBAAA,CAAAE,IAAA,CAAAjB,QAAmB,CAAC;IAC5B;EACF,CAAC,EAED;IAEEkB,aAAa,EAAE,CAAC,IAAI,EAAE;IACtB,GAAGnB;EACL,CACF,CAAC;AACH"}