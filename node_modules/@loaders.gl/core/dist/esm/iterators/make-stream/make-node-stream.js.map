{"version": 3, "file": "make-node-stream.js", "names": ["Stream", "_Readable", "Readable", "makeStream", "source", "options", "iterator", "Symbol", "asyncIterator", "AsyncIterableReadable", "constructor", "it", "_defineProperty", "_iterator", "_pulling", "_bytesMode", "objectMode", "_read", "size", "_pull", "_destroy", "error", "cb", "_this$_iterator", "_this$_iterator$throw", "throw", "call", "_this$_iterator2", "_this$_iterator2$retu", "return", "_r", "bm", "r", "readable", "next", "done", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "value", "byteLength", "push", "Uint8Array", "_it$return"], "sources": ["../../../../src/iterators/make-stream/make-node-stream.ts"], "sourcesContent": ["import type {ReadableOptions} from 'stream';\nimport * as Stream from 'stream';\n\nclass _Readable {}\n\ntype ReadableType = Stream.Readable | _Readable;\nconst Readable = Stream.Readable || _Readable;\n\nexport type MakeStreamOptions = ReadableOptions;\n\n/** Builds a node stream from an iterator */\nexport function makeStream<ArrayBuffer>(\n  source: Iterable<ArrayBuffer> | AsyncIterable<ArrayBuffer>,\n  options?: ReadableOptions\n): ReadableType {\n  const iterator = source[Symbol.asyncIterator]\n    ? source[Symbol.asyncIterator]()\n    : source[Symbol.iterator]();\n  return new AsyncIterableReadable(iterator, options);\n}\n\nclass AsyncIterableReadable extends Readable {\n  private _pulling: boolean;\n  private _bytesMode: boolean;\n  private _iterator: AsyncIterator<ArrayBuffer>;\n\n  constructor(it: AsyncIterator<ArrayBuffer>, options?: ReadableOptions) {\n    super(options);\n    this._iterator = it;\n    this._pulling = false;\n    this._bytesMode = !options || !options.objectMode;\n  }\n\n  async _read(size: number): Promise<void> {\n    if (!this._pulling) {\n      this._pulling = true;\n      this._pulling = await this._pull(size, this._iterator);\n    }\n  }\n\n  async _destroy(error: Error | null, cb: (e: Error | null) => void): Promise<void> {\n    if (!this._iterator) {\n      return;\n    }\n    if (error) {\n      await this._iterator?.throw?.(error);\n    } else {\n      await this._iterator?.return?.(error);\n    }\n    cb?.(null);\n  }\n\n  // eslint-disable-next-line complexity\n  private async _pull(size: number, it: AsyncIterator<ArrayBuffer>): Promise<boolean> {\n    const bm = this._bytesMode;\n    let r: IteratorResult<ArrayBuffer> | null = null;\n    // while (this.readable && !(r = await it.next(bm ? size : null)).done) {\n    while (this.readable && !(r = await it.next()).done) {\n      if (size !== null) {\n        size -= bm && ArrayBuffer.isView(r.value) ? r.value.byteLength : 1;\n      }\n      if (!this.push(new Uint8Array(r.value)) || size <= 0) {\n        break;\n      }\n    }\n    if ((r?.done || !this.readable) && (this.push(null) || true)) {\n      it?.return?.();\n    }\n    return !this.readable;\n  }\n}\n"], "mappings": ";AACA,OAAO,KAAKA,MAAM,MAAM,QAAQ;AAEhC,MAAMC,SAAS,CAAC;AAGhB,MAAMC,QAAQ,GAAGF,MAAM,CAACE,QAAQ,IAAID,SAAS;AAK7C,OAAO,SAASE,UAAUA,CACxBC,MAA0D,EAC1DC,OAAyB,EACX;EACd,MAAMC,QAAQ,GAAGF,MAAM,CAACG,MAAM,CAACC,aAAa,CAAC,GACzCJ,MAAM,CAACG,MAAM,CAACC,aAAa,CAAC,CAAC,CAAC,GAC9BJ,MAAM,CAACG,MAAM,CAACD,QAAQ,CAAC,CAAC,CAAC;EAC7B,OAAO,IAAIG,qBAAqB,CAACH,QAAQ,EAAED,OAAO,CAAC;AACrD;AAEA,MAAMI,qBAAqB,SAASP,QAAQ,CAAC;EAK3CQ,WAAWA,CAACC,EAA8B,EAAEN,OAAyB,EAAE;IACrE,KAAK,CAACA,OAAO,CAAC;IAACO,eAAA;IAAAA,eAAA;IAAAA,eAAA;IACf,IAAI,CAACC,SAAS,GAAGF,EAAE;IACnB,IAAI,CAACG,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,UAAU,GAAG,CAACV,OAAO,IAAI,CAACA,OAAO,CAACW,UAAU;EACnD;EAEA,MAAMC,KAAKA,CAACC,IAAY,EAAiB;IACvC,IAAI,CAAC,IAAI,CAACJ,QAAQ,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACA,QAAQ,GAAG,MAAM,IAAI,CAACK,KAAK,CAACD,IAAI,EAAE,IAAI,CAACL,SAAS,CAAC;IACxD;EACF;EAEA,MAAMO,QAAQA,CAACC,KAAmB,EAAEC,EAA6B,EAAiB;IAChF,IAAI,CAAC,IAAI,CAACT,SAAS,EAAE;MACnB;IACF;IACA,IAAIQ,KAAK,EAAE;MAAA,IAAAE,eAAA,EAAAC,qBAAA;MACT,QAAAD,eAAA,GAAM,IAAI,CAACV,SAAS,cAAAU,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBE,KAAK,cAAAD,qBAAA,uBAArBA,qBAAA,CAAAE,IAAA,CAAAH,eAAA,EAAwBF,KAAK,CAAC;IACtC,CAAC,MAAM;MAAA,IAAAM,gBAAA,EAAAC,qBAAA;MACL,QAAAD,gBAAA,GAAM,IAAI,CAACd,SAAS,cAAAc,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBE,MAAM,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAAF,IAAA,CAAAC,gBAAA,EAAyBN,KAAK,CAAC;IACvC;IACAC,EAAE,aAAFA,EAAE,uBAAFA,EAAE,CAAG,IAAI,CAAC;EACZ;EAGA,MAAcH,KAAKA,CAACD,IAAY,EAAEP,EAA8B,EAAoB;IAAA,IAAAmB,EAAA;IAClF,MAAMC,EAAE,GAAG,IAAI,CAAChB,UAAU;IAC1B,IAAIiB,CAAqC,GAAG,IAAI;IAEhD,OAAO,IAAI,CAACC,QAAQ,IAAI,CAAC,CAACD,CAAC,GAAG,MAAMrB,EAAE,CAACuB,IAAI,CAAC,CAAC,EAAEC,IAAI,EAAE;MACnD,IAAIjB,IAAI,KAAK,IAAI,EAAE;QACjBA,IAAI,IAAIa,EAAE,IAAIK,WAAW,CAACC,MAAM,CAACL,CAAC,CAACM,KAAK,CAAC,GAAGN,CAAC,CAACM,KAAK,CAACC,UAAU,GAAG,CAAC;MACpE;MACA,IAAI,CAAC,IAAI,CAACC,IAAI,CAAC,IAAIC,UAAU,CAACT,CAAC,CAACM,KAAK,CAAC,CAAC,IAAIpB,IAAI,IAAI,CAAC,EAAE;QACpD;MACF;IACF;IACA,IAAI,CAAC,CAAAY,EAAA,GAAAE,CAAC,cAAAF,EAAA,eAADA,EAAA,CAAGK,IAAI,IAAI,CAAC,IAAI,CAACF,QAAQ,MAAM,IAAI,CAACO,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE;MAAA,IAAAE,UAAA;MAC5D/B,EAAE,aAAFA,EAAE,wBAAA+B,UAAA,GAAF/B,EAAE,CAAEkB,MAAM,cAAAa,UAAA,uBAAVA,UAAA,CAAAhB,IAAA,CAAAf,EAAa,CAAC;IAChB;IACA,OAAO,CAAC,IAAI,CAACsB,QAAQ;EACvB;AACF"}