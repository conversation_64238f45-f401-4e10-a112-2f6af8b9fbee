{"version": 3, "file": "null-loader.js", "names": ["VERSION", "NullWorkerLoader", "name", "id", "module", "version", "worker", "mimeTypes", "extensions", "tests", "options", "null", "parseSync", "arrayBuffer", "context", "echoParameters", "JSON", "parse", "stringify", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseInBatches", "generator", "asyncIterator", "batch"], "sources": ["../../src/null-loader.ts"], "sourcesContent": ["// __VERSION__ is injected by babel-plugin-version-inline\n// @ts-ignore TS2304: Cannot find name '__VERSION__'.\nconst VERSION = typeof __VERSION__ !== 'undefined' ? __VERSION__ : 'latest';\n\nimport {Loader, LoaderWithParser} from '@loaders.gl/loader-utils';\n\n/**\n * Loads any data and returns null (or optionally passes through data unparsed)\n */\nexport const NullWorkerLoader: Loader = {\n  name: 'Null loader',\n  id: 'null',\n  module: 'core',\n  version: VERSION,\n  worker: true,\n  mimeTypes: ['application/x.empty'],\n  extensions: ['null'],\n  tests: [() => false],\n  options: {\n    null: {}\n  }\n};\n\n/**\n * Returns arguments passed to the parse API in a format that can be transfered to a\n * web worker. The `context` parameter is stripped using JSON.stringify & parse.\n */\nfunction parseSync(arrayBuffer, options, context) {\n  if (!options.null.echoParameters) return null;\n  context = context && JSON.parse(JSON.stringify(context));\n  return {arrayBuffer, options, context};\n}\n\n/**\n * Loads any data and returns null (or optionally passes through data unparsed)\n */\nexport const NullLoader: LoaderWithParser = {\n  name: 'Null loader',\n  id: 'null',\n  module: 'core',\n  version: VERSION,\n  mimeTypes: ['application/x.empty'],\n  extensions: ['null'],\n  parse: async (arrayBuffer, options, context) => parseSync(arrayBuffer, options, context),\n  parseSync,\n  parseInBatches: async function* generator(asyncIterator, options, context) {\n    for await (const batch of asyncIterator) {\n      yield parseSync(batch, options, context);\n    }\n  },\n  tests: [() => false],\n  options: {\n    null: {\n      echoParameters: false\n    }\n  }\n};\n"], "mappings": "AAEA,MAAMA,OAAO,GAAG,eAAkB,KAAK,WAAW,cAAiB,QAAQ;AAO3E,OAAO,MAAMC,gBAAwB,GAAG;EACtCC,IAAI,EAAE,aAAa;EACnBC,EAAE,EAAE,MAAM;EACVC,MAAM,EAAE,MAAM;EACdC,OAAO,EAAEL,OAAO;EAChBM,MAAM,EAAE,IAAI;EACZC,SAAS,EAAE,CAAC,qBAAqB,CAAC;EAClCC,UAAU,EAAE,CAAC,MAAM,CAAC;EACpBC,KAAK,EAAE,CAAC,MAAM,KAAK,CAAC;EACpBC,OAAO,EAAE;IACPC,IAAI,EAAE,CAAC;EACT;AACF,CAAC;AAMD,SAASC,SAASA,CAACC,WAAW,EAAEH,OAAO,EAAEI,OAAO,EAAE;EAChD,IAAI,CAACJ,OAAO,CAACC,IAAI,CAACI,cAAc,EAAE,OAAO,IAAI;EAC7CD,OAAO,GAAGA,OAAO,IAAIE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACJ,OAAO,CAAC,CAAC;EACxD,OAAO;IAACD,WAAW;IAAEH,OAAO;IAAEI;EAAO,CAAC;AACxC;AAKA,OAAO,MAAMK,UAA4B,GAAG;EAC1CjB,IAAI,EAAE,aAAa;EACnBC,EAAE,EAAE,MAAM;EACVC,MAAM,EAAE,MAAM;EACdC,OAAO,EAAEL,OAAO;EAChBO,SAAS,EAAE,CAAC,qBAAqB,CAAC;EAClCC,UAAU,EAAE,CAAC,MAAM,CAAC;EACpBS,KAAK,EAAE,MAAAA,CAAOJ,WAAW,EAAEH,OAAO,EAAEI,OAAO,KAAKF,SAAS,CAACC,WAAW,EAAEH,OAAO,EAAEI,OAAO,CAAC;EACxFF,SAAS;EACTQ,cAAc,EAAE,gBAAgBC,SAASA,CAACC,aAAa,EAAEZ,OAAO,EAAEI,OAAO,EAAE;IACzE,WAAW,MAAMS,KAAK,IAAID,aAAa,EAAE;MACvC,MAAMV,SAAS,CAACW,KAAK,EAAEb,OAAO,EAAEI,OAAO,CAAC;IAC1C;EACF,CAAC;EACDL,KAAK,EAAE,CAAC,MAAM,KAAK,CAAC;EACpBC,OAAO,EAAE;IACPC,IAAI,EAAE;MACJI,cAAc,EAAE;IAClB;EACF;AACF,CAAC"}