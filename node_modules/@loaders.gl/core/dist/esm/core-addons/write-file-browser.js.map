{"version": 3, "file": "write-file-browser.js", "names": [], "sources": ["../../../src/core-addons/write-file-browser.ts"], "sourcesContent": ["// A browser implementation of the Node.js `fs` module's `fs.writeFile` method.\n// NOTE: WAS COMMENTED OUT TO GET NODE 8 TESTS RUNNING\n\n/*\n/* global global *\n/* global Blob *\nimport {isBrowser} from '@loaders.gl/core';\n\n// TODO hack - trick filesaver.js to skip loading under node\nconst savedNavigatorExists = 'navigator' in global;\nconst savedNavigator = global.navigator;\nif (!isBrowser) {\n  global.navigator = {userAgent: 'MSIE 9.'};\n}\n\n// Need to use `require` to ensure our modification of global code above happens first\nconst saveAs = require('filesaver.js');\n\nif (!isBrowser) {\n  if (savedNavigatorExists) {\n    global.navigator = savedNavigator;\n  } else {\n    delete global.navigator;\n  }\n}\n// END hack\n\n/**\n * File system write function for the browser, similar to Node's fs.writeFile\n *\n * Saves a file by downloading it with the given file name.\n *\n * @param {String} file - file name\n * @param {String|Blob} data - data to be written to file\n * @param {String|Object} options -\n * @param {Function} callback - Standard node (err, data) callback\n * @return {Promise} - promise, can be used instead of callback\n *\nasync export function writeFile(file, data, options, callback = () => {}) {\n  // options is optional\n  if (callback === undefined && typeof options === 'function') {\n    options = undefined;\n    callback = options;\n  }\n  if (typeof data === 'string') {\n    data = new Blob(data);\n  }\n  return new Promise((resolve, reject) => {\n    let result;\n    try {\n      result = saveAs(data, file, options);\n    } catch (error) {\n      reject(error);\n      return callback(error, null);\n    }\n    resolve();\n    return callback(null, result);\n  });\n}\n*/\n"], "mappings": ""}