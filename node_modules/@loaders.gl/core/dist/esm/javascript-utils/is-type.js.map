{"version": 3, "file": "is-type.js", "names": ["isBoolean", "x", "isFunction", "isObject", "isPureObject", "constructor", "isPromise", "then", "isIterable", "Symbol", "iterator", "isAsyncIterable", "asyncIterator", "isIterator", "next", "isResponse", "Response", "arrayBuffer", "text", "json", "isFile", "File", "isBlob", "Blob", "<PERSON><PERSON><PERSON><PERSON>", "isWritableDOMStream", "abort", "getWriter", "isReadableDOMStream", "ReadableStream", "tee", "cancel", "<PERSON><PERSON><PERSON><PERSON>", "isWritableNodeStream", "end", "write", "writable", "isReadableNodeStream", "read", "pipe", "readable", "isReadableStream", "isWritableStream"], "sources": ["../../../src/javascript-utils/is-type.ts"], "sourcesContent": ["import type {Readable} from 'stream';\n\n/** A DOM or Node readable stream */\nexport type ReadableStreamType = ReadableStream | Readable;\n\nconst isBoolean: (x: any) => boolean = (x) => typeof x === 'boolean';\nconst isFunction: (x: any) => boolean = (x) => typeof x === 'function';\n\nexport const isObject: (x: any) => boolean = (x) => x !== null && typeof x === 'object';\nexport const isPureObject: (x: any) => boolean = (x) =>\n  isObject(x) && x.constructor === {}.constructor;\nexport const isPromise: (x: any) => boolean = (x) => isObject(x) && isFunction(x.then);\n\nexport const isIterable: (x: any) => boolean = (x) => x && typeof x[Symbol.iterator] === 'function';\nexport const isAsyncIterable: (x: any) => boolean = (x) =>\n  x && typeof x[Symbol.asyncIterator] === 'function';\nexport const isIterator: (x: any) => boolean = (x) => x && isFunction(x.next);\n\nexport const isResponse: (x: any) => boolean = (x) =>\n  (typeof Response !== 'undefined' && x instanceof Response) ||\n  (x && x.arrayBuffer && x.text && x.json);\n\nexport const isFile: (x: any) => boolean = (x) => typeof File !== 'undefined' && x instanceof File;\nexport const isBlob: (x: any) => boolean = (x) => typeof Blob !== 'undefined' && x instanceof Blob;\n\n/** Check for Node.js `Buffer` without triggering bundler to include buffer polyfill */\nexport const isBuffer: (x: any) => boolean = (x) => x && typeof x === 'object' && x.isBuffer;\n\nexport const isWritableDOMStream: (x: any) => boolean = (x) =>\n  isObject(x) && isFunction(x.abort) && isFunction(x.getWriter);\n\nexport const isReadableDOMStream: (x: any) => boolean = (x) =>\n  (typeof ReadableStream !== 'undefined' && x instanceof ReadableStream) ||\n  (isObject(x) && isFunction(x.tee) && isFunction(x.cancel) && isFunction(x.getReader));\n// Not implemented in Firefox: && isFunction(x.pipeTo)\n\nexport const isWritableNodeStream: (x: any) => boolean = (x) =>\n  isObject(x) && isFunction(x.end) && isFunction(x.write) && isBoolean(x.writable);\nexport const isReadableNodeStream: (x: any) => boolean = (x) =>\n  isObject(x) && isFunction(x.read) && isFunction(x.pipe) && isBoolean(x.readable);\nexport const isReadableStream: (x: any) => boolean = (x) =>\n  isReadableDOMStream(x) || isReadableNodeStream(x);\nexport const isWritableStream: (x: any) => boolean = (x) =>\n  isWritableDOMStream(x) || isWritableNodeStream(x);\n"], "mappings": "AAKA,MAAMA,SAA8B,GAAIC,CAAC,IAAK,OAAOA,CAAC,KAAK,SAAS;AACpE,MAAMC,UAA+B,GAAID,CAAC,IAAK,OAAOA,CAAC,KAAK,UAAU;AAEtE,OAAO,MAAME,QAA6B,GAAIF,CAAC,IAAKA,CAAC,KAAK,IAAI,IAAI,OAAOA,CAAC,KAAK,QAAQ;AACvF,OAAO,MAAMG,YAAiC,GAAIH,CAAC,IACjDE,QAAQ,CAACF,CAAC,CAAC,IAAIA,CAAC,CAACI,WAAW,KAAK,CAAC,CAAC,CAACA,WAAW;AACjD,OAAO,MAAMC,SAA8B,GAAIL,CAAC,IAAKE,QAAQ,CAACF,CAAC,CAAC,IAAIC,UAAU,CAACD,CAAC,CAACM,IAAI,CAAC;AAEtF,OAAO,MAAMC,UAA+B,GAAIP,CAAC,IAAKA,CAAC,IAAI,OAAOA,CAAC,CAACQ,MAAM,CAACC,QAAQ,CAAC,KAAK,UAAU;AACnG,OAAO,MAAMC,eAAoC,GAAIV,CAAC,IACpDA,CAAC,IAAI,OAAOA,CAAC,CAACQ,MAAM,CAACG,aAAa,CAAC,KAAK,UAAU;AACpD,OAAO,MAAMC,UAA+B,GAAIZ,CAAC,IAAKA,CAAC,IAAIC,UAAU,CAACD,CAAC,CAACa,IAAI,CAAC;AAE7E,OAAO,MAAMC,UAA+B,GAAId,CAAC,IAC9C,OAAOe,QAAQ,KAAK,WAAW,IAAIf,CAAC,YAAYe,QAAQ,IACxDf,CAAC,IAAIA,CAAC,CAACgB,WAAW,IAAIhB,CAAC,CAACiB,IAAI,IAAIjB,CAAC,CAACkB,IAAK;AAE1C,OAAO,MAAMC,MAA2B,GAAInB,CAAC,IAAK,OAAOoB,IAAI,KAAK,WAAW,IAAIpB,CAAC,YAAYoB,IAAI;AAClG,OAAO,MAAMC,MAA2B,GAAIrB,CAAC,IAAK,OAAOsB,IAAI,KAAK,WAAW,IAAItB,CAAC,YAAYsB,IAAI;AAGlG,OAAO,MAAMC,QAA6B,GAAIvB,CAAC,IAAKA,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAACuB,QAAQ;AAE5F,OAAO,MAAMC,mBAAwC,GAAIxB,CAAC,IACxDE,QAAQ,CAACF,CAAC,CAAC,IAAIC,UAAU,CAACD,CAAC,CAACyB,KAAK,CAAC,IAAIxB,UAAU,CAACD,CAAC,CAAC0B,SAAS,CAAC;AAE/D,OAAO,MAAMC,mBAAwC,GAAI3B,CAAC,IACvD,OAAO4B,cAAc,KAAK,WAAW,IAAI5B,CAAC,YAAY4B,cAAc,IACpE1B,QAAQ,CAACF,CAAC,CAAC,IAAIC,UAAU,CAACD,CAAC,CAAC6B,GAAG,CAAC,IAAI5B,UAAU,CAACD,CAAC,CAAC8B,MAAM,CAAC,IAAI7B,UAAU,CAACD,CAAC,CAAC+B,SAAS,CAAE;AAGvF,OAAO,MAAMC,oBAAyC,GAAIhC,CAAC,IACzDE,QAAQ,CAACF,CAAC,CAAC,IAAIC,UAAU,CAACD,CAAC,CAACiC,GAAG,CAAC,IAAIhC,UAAU,CAACD,CAAC,CAACkC,KAAK,CAAC,IAAInC,SAAS,CAACC,CAAC,CAACmC,QAAQ,CAAC;AAClF,OAAO,MAAMC,oBAAyC,GAAIpC,CAAC,IACzDE,QAAQ,CAACF,CAAC,CAAC,IAAIC,UAAU,CAACD,CAAC,CAACqC,IAAI,CAAC,IAAIpC,UAAU,CAACD,CAAC,CAACsC,IAAI,CAAC,IAAIvC,SAAS,CAACC,CAAC,CAACuC,QAAQ,CAAC;AAClF,OAAO,MAAMC,gBAAqC,GAAIxC,CAAC,IACrD2B,mBAAmB,CAAC3B,CAAC,CAAC,IAAIoC,oBAAoB,CAACpC,CAAC,CAAC;AACnD,OAAO,MAAMyC,gBAAqC,GAAIzC,CAAC,IACrDwB,mBAAmB,CAACxB,CAAC,CAAC,IAAIgC,oBAAoB,CAAChC,CAAC,CAAC"}