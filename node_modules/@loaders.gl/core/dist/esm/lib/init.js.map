{"version": 3, "file": "init.js", "names": ["log", "version", "globalThis", "loaders", "concat", "Object", "assign", "VERSION"], "sources": ["../../../src/lib/init.ts"], "sourcesContent": ["// loaders.gl, MIT license\nimport {log} from './utils/log';\n\n// @ts-ignore TS2304: Cannot find name '__VERSION__'.\nconst version = typeof __VERSION__ !== 'undefined' ? __VERSION__ : 'latest';\n\n// @ts-ignore\nif (!globalThis.loaders) {\n  log.log(1, `loaders.gl ${version}`)();\n\n  globalThis.loaders = Object.assign(globalThis.loaders || {}, {\n    VERSION: version,\n    log\n  });\n}\n// @ts-ignore\nexport default globalThis.loaders;\n"], "mappings": "AACA,SAAQA,GAAG,QAAO,aAAa;AAG/B,MAAMC,OAAO,GAAG,eAAkB,KAAK,WAAW,cAAiB,QAAQ;AAG3E,IAAI,CAACC,UAAU,CAACC,OAAO,EAAE;EACvBH,GAAG,CAACA,GAAG,CAAC,CAAC,gBAAAI,MAAA,CAAgBH,OAAO,CAAE,CAAC,CAAC,CAAC;EAErCC,UAAU,CAACC,OAAO,GAAGE,MAAM,CAACC,MAAM,CAACJ,UAAU,CAACC,OAAO,IAAI,CAAC,CAAC,EAAE;IAC3DI,OAAO,EAAEN,OAAO;IAChBD;EACF,CAAC,CAAC;AACJ;AAEA,eAAeE,UAAU,CAACC,OAAO"}