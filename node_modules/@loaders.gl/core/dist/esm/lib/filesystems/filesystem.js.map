{"version": 3, "file": "filesystem.js", "names": [], "sources": ["../../../../src/lib/filesystems/filesystem.ts"], "sourcesContent": ["export type ReadOptions = {};\n\nexport type Stat = {\n  size: number;\n  isDirectory: () => boolean;\n};\n\n/**\n * A FileSystem interface can encapsulate various file sources,\n * a FileList, a ZipFile, a GoogleDrive etc.\n */\nexport interface FileSystem {\n  /**\n   * Return a list of file names\n   * @param dirname directory name. file system root directory if omitted\n   */\n  readdir(dirname?: string, options?: {recursive?: boolean}): Promise<string[]>;\n\n  /**\n   * Gets information from a local file from the filesystem\n   * @param filename file name to stat\n   * @param options currently unused\n   * @throws if filename is not in local filesystem\n   */\n  stat(filename: string, options?: object): Promise<{size: number}>;\n\n  /**\n   * Fetches a local file from the filesystem (or a URL)\n   * @param filename\n   * @param options\n   */\n  fetch(filename: RequestInfo, options?: RequestInit): Promise<Response>;\n}\n\n/**\n * A random access file system\n */\nexport interface RandomAccessReadFileSystem extends FileSystem {\n  open(path: string, flags, mode?): Promise<any>;\n  close(fd: any): Promise<void>;\n  fstat(fd: any): Promise<Stat>;\n  read(\n    fd: any,\n    buffer: ArrayBuffer | ArrayBufferView,\n    offset?: number,\n    length?: number,\n    position?: number\n  ): Promise<{bytesRead: number; buffer: ArrayBuffer}>;\n}\n"], "mappings": ""}