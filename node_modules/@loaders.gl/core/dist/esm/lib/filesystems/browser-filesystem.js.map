{"version": 3, "file": "browser-filesystem.js", "names": ["BrowserFileSystem", "constructor", "files", "options", "_defineProperty", "_fetch", "fetch", "i", "length", "file", "name", "lowerCaseFiles", "toLowerCase", "usedFiles", "bind", "path", "includes", "Response", "status", "statusText", "headers", "Headers", "range", "get", "bytes", "exec", "start", "parseInt", "end", "data", "slice", "arrayBuffer", "response", "Object", "defineProperty", "value", "readdir", "dirname", "push", "stat", "Error", "size", "unlink", "open", "pathname", "flags", "mode", "read", "fd", "buffer", "offset", "arguments", "undefined", "byteLength", "position", "startPosition", "bytesRead", "close", "_getFile", "used"], "sources": ["../../../../src/lib/filesystems/browser-filesystem.ts"], "sourcesContent": ["import type {FileSystem} from './filesystem';\n\ntype BrowserFileSystemOptions = {\n  fetch?: typeof fetch;\n};\n\n/**\n * FileSystem adapter for a browser FileList.\n * Holds a list of browser 'File' objects.\n */\nexport default class BrowserFileSystem implements FileSystem {\n  private _fetch: typeof fetch;\n  private files: {[filename: string]: File} = {};\n  private lowerCaseFiles: {[filename: string]: File} = {};\n  private usedFiles: {[filename: string]: boolean} = {};\n\n  /**\n   * A FileSystem API wrapper around a list of browser 'File' objects\n   * @param files\n   * @param options\n   */\n  constructor(files: FileList | File[], options?: BrowserFileSystemOptions) {\n    this._fetch = options?.fetch || fetch;\n\n    for (let i = 0; i < files.length; ++i) {\n      const file = files[i];\n      this.files[file.name] = file;\n      this.lowerCaseFiles[file.name.toLowerCase()] = file;\n      this.usedFiles[file.name] = false;\n    }\n\n    this.fetch = this.fetch.bind(this);\n  }\n\n  // implements IFileSystem\n\n  /**\n   * Implementation of fetch against this file system\n   * Delegates to global fetch for http{s}:// or data://\n   */\n  async fetch(path: string, options?: RequestInit): Promise<Response> {\n    // Fallback to handle https:/http:/data: etc fetches\n    if (path.includes('://')) {\n      return this._fetch(path, options);\n    }\n\n    // Local fetches are served from the list of files\n    const file = this.files[path];\n    if (!file) {\n      return new Response(path, {status: 400, statusText: 'NOT FOUND'});\n    }\n\n    const headers = new Headers(options?.headers);\n    const range = headers.get('Range');\n    const bytes = range && /bytes=($1)-($2)/.exec(range);\n\n    if (bytes) {\n      const start = parseInt(bytes[1]);\n      const end = parseInt(bytes[2]);\n      // The trick when reading File objects is to read successive \"slices\" of the File\n      // Per spec https://w3c.github.io/FileAPI/, slicing a File should only update the start and end fields\n      // Actually reading from file should happen in `readAsArrayBuffer` (and as far we can tell it does)\n      const data = await file.slice(start, end).arrayBuffer();\n      const response = new Response(data);\n      Object.defineProperty(response, 'url', {value: path});\n      return response;\n    }\n\n    // return makeResponse()\n    const response = new Response(file);\n    Object.defineProperty(response, 'url', {value: path});\n    return response;\n  }\n\n  /**\n   * List filenames in this filesystem\n   * @param dirname\n   * @returns\n   */\n  async readdir(dirname?: string): Promise<string[]> {\n    const files: string[] = [];\n    for (const path in this.files) {\n      files.push(path);\n    }\n    // TODO filter by dirname\n    return files;\n  }\n\n  /**\n   * Return information (size) about files in this file system\n   */\n  async stat(path: string, options?: object): Promise<{size: number}> {\n    const file = this.files[path];\n    if (!file) {\n      throw new Error(path);\n    }\n    return {size: file.size};\n  }\n\n  /**\n   * Just removes the file from the list\n   */\n  async unlink(path: string): Promise<void> {\n    delete this.files[path];\n    delete this.lowerCaseFiles[path];\n    this.usedFiles[path] = true;\n  }\n\n  // implements IRandomAccessFileSystem\n\n  // RANDOM ACCESS\n  async open(pathname: string, flags, mode?): Promise<any> {\n    return this.files[pathname];\n  }\n\n  /**\n   * Read a range into a buffer\n   * @todo - handle position memory\n   * @param buffer is the buffer that the data (read from the fd) will be written to.\n   * @param offset is the offset in the buffer to start writing at.\n   * @param length is an integer specifying the number of bytes to read.\n   * @param position is an argument specifying where to begin reading from in the file. If position is null, data will be read from the current file position, and the file position will be updated. If position is an integer, the file position will remain unchanged.\n   */\n  async read(\n    fd: any,\n    buffer: ArrayBuffer,\n    offset: number = 0,\n    length: number = buffer.byteLength,\n    position: number | null = null\n  ): Promise<{bytesRead: number; buffer: ArrayBuffer}> {\n    const file = fd as File;\n    const startPosition = 0; // position\n    const arrayBuffer = await file.slice(startPosition, startPosition + length).arrayBuffer();\n    // copy into target buffer\n    return {bytesRead: length, buffer: arrayBuffer};\n  }\n\n  async close(fd: number): Promise<void> {\n    // NO OP\n  }\n\n  // fstat(fd: number): Promise<object>; // Stat\n\n  // PRIVATE\n\n  // Supports case independent paths, and file usage tracking\n  _getFile(path, used) {\n    // Prefer case match, but fall back to case indepent.\n    const file = this.files[path] || this.lowerCaseFiles[path];\n    if (file && used) {\n      this.usedFiles[path] = true;\n    }\n    return file;\n  }\n}\n"], "mappings": ";AAUA,eAAe,MAAMA,iBAAiB,CAAuB;EAW3DC,WAAWA,CAACC,KAAwB,EAAEC,OAAkC,EAAE;IAAAC,eAAA;IAAAA,eAAA,gBAT9B,CAAC,CAAC;IAAAA,eAAA,yBACO,CAAC,CAAC;IAAAA,eAAA,oBACJ,CAAC,CAAC;IAQnD,IAAI,CAACC,MAAM,GAAG,CAAAF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,KAAK,KAAIA,KAAK;IAErC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,CAACM,MAAM,EAAE,EAAED,CAAC,EAAE;MACrC,MAAME,IAAI,GAAGP,KAAK,CAACK,CAAC,CAAC;MACrB,IAAI,CAACL,KAAK,CAACO,IAAI,CAACC,IAAI,CAAC,GAAGD,IAAI;MAC5B,IAAI,CAACE,cAAc,CAACF,IAAI,CAACC,IAAI,CAACE,WAAW,CAAC,CAAC,CAAC,GAAGH,IAAI;MACnD,IAAI,CAACI,SAAS,CAACJ,IAAI,CAACC,IAAI,CAAC,GAAG,KAAK;IACnC;IAEA,IAAI,CAACJ,KAAK,GAAG,IAAI,CAACA,KAAK,CAACQ,IAAI,CAAC,IAAI,CAAC;EACpC;EAQA,MAAMR,KAAKA,CAACS,IAAY,EAAEZ,OAAqB,EAAqB;IAElE,IAAIY,IAAI,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;MACxB,OAAO,IAAI,CAACX,MAAM,CAACU,IAAI,EAAEZ,OAAO,CAAC;IACnC;IAGA,MAAMM,IAAI,GAAG,IAAI,CAACP,KAAK,CAACa,IAAI,CAAC;IAC7B,IAAI,CAACN,IAAI,EAAE;MACT,OAAO,IAAIQ,QAAQ,CAACF,IAAI,EAAE;QAACG,MAAM,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAW,CAAC,CAAC;IACnE;IAEA,MAAMC,OAAO,GAAG,IAAIC,OAAO,CAAClB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEiB,OAAO,CAAC;IAC7C,MAAME,KAAK,GAAGF,OAAO,CAACG,GAAG,CAAC,OAAO,CAAC;IAClC,MAAMC,KAAK,GAAGF,KAAK,IAAI,iBAAiB,CAACG,IAAI,CAACH,KAAK,CAAC;IAEpD,IAAIE,KAAK,EAAE;MACT,MAAME,KAAK,GAAGC,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;MAChC,MAAMI,GAAG,GAAGD,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;MAI9B,MAAMK,IAAI,GAAG,MAAMpB,IAAI,CAACqB,KAAK,CAACJ,KAAK,EAAEE,GAAG,CAAC,CAACG,WAAW,CAAC,CAAC;MACvD,MAAMC,QAAQ,GAAG,IAAIf,QAAQ,CAACY,IAAI,CAAC;MACnCI,MAAM,CAACC,cAAc,CAACF,QAAQ,EAAE,KAAK,EAAE;QAACG,KAAK,EAAEpB;MAAI,CAAC,CAAC;MACrD,OAAOiB,QAAQ;IACjB;IAGA,MAAMA,QAAQ,GAAG,IAAIf,QAAQ,CAACR,IAAI,CAAC;IACnCwB,MAAM,CAACC,cAAc,CAACF,QAAQ,EAAE,KAAK,EAAE;MAACG,KAAK,EAAEpB;IAAI,CAAC,CAAC;IACrD,OAAOiB,QAAQ;EACjB;EAOA,MAAMI,OAAOA,CAACC,OAAgB,EAAqB;IACjD,MAAMnC,KAAe,GAAG,EAAE;IAC1B,KAAK,MAAMa,IAAI,IAAI,IAAI,CAACb,KAAK,EAAE;MAC7BA,KAAK,CAACoC,IAAI,CAACvB,IAAI,CAAC;IAClB;IAEA,OAAOb,KAAK;EACd;EAKA,MAAMqC,IAAIA,CAACxB,IAAY,EAAEZ,OAAgB,EAA2B;IAClE,MAAMM,IAAI,GAAG,IAAI,CAACP,KAAK,CAACa,IAAI,CAAC;IAC7B,IAAI,CAACN,IAAI,EAAE;MACT,MAAM,IAAI+B,KAAK,CAACzB,IAAI,CAAC;IACvB;IACA,OAAO;MAAC0B,IAAI,EAAEhC,IAAI,CAACgC;IAAI,CAAC;EAC1B;EAKA,MAAMC,MAAMA,CAAC3B,IAAY,EAAiB;IACxC,OAAO,IAAI,CAACb,KAAK,CAACa,IAAI,CAAC;IACvB,OAAO,IAAI,CAACJ,cAAc,CAACI,IAAI,CAAC;IAChC,IAAI,CAACF,SAAS,CAACE,IAAI,CAAC,GAAG,IAAI;EAC7B;EAKA,MAAM4B,IAAIA,CAACC,QAAgB,EAAEC,KAAK,EAAEC,IAAK,EAAgB;IACvD,OAAO,IAAI,CAAC5C,KAAK,CAAC0C,QAAQ,CAAC;EAC7B;EAUA,MAAMG,IAAIA,CACRC,EAAO,EACPC,MAAmB,EAIgC;IAAA,IAHnDC,MAAc,GAAAC,SAAA,CAAA3C,MAAA,QAAA2C,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC;IAAA,IAClB3C,MAAc,GAAA2C,SAAA,CAAA3C,MAAA,QAAA2C,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGF,MAAM,CAACI,UAAU;IAAA,IAClCC,QAAuB,GAAAH,SAAA,CAAA3C,MAAA,QAAA2C,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,IAAI;IAE9B,MAAM1C,IAAI,GAAGuC,EAAU;IACvB,MAAMO,aAAa,GAAG,CAAC;IACvB,MAAMxB,WAAW,GAAG,MAAMtB,IAAI,CAACqB,KAAK,CAACyB,aAAa,EAAEA,aAAa,GAAG/C,MAAM,CAAC,CAACuB,WAAW,CAAC,CAAC;IAEzF,OAAO;MAACyB,SAAS,EAAEhD,MAAM;MAAEyC,MAAM,EAAElB;IAAW,CAAC;EACjD;EAEA,MAAM0B,KAAKA,CAACT,EAAU,EAAiB,CAEvC;EAOAU,QAAQA,CAAC3C,IAAI,EAAE4C,IAAI,EAAE;IAEnB,MAAMlD,IAAI,GAAG,IAAI,CAACP,KAAK,CAACa,IAAI,CAAC,IAAI,IAAI,CAACJ,cAAc,CAACI,IAAI,CAAC;IAC1D,IAAIN,IAAI,IAAIkD,IAAI,EAAE;MAChB,IAAI,CAAC9C,SAAS,CAACE,IAAI,CAAC,GAAG,IAAI;IAC7B;IACA,OAAON,IAAI;EACb;AACF"}