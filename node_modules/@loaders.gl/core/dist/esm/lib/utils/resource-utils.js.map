{"version": 3, "file": "resource-utils.js", "names": ["isResponse", "isBlob", "parseMIMEType", "parseMIMETypeFromURL", "stripQueryString", "getResourceUrl", "resource", "response", "url", "blob", "name", "getResourceMIMEType", "contentTypeHeader", "headers", "get", "noQueryUrl", "type", "getResourceContentLength", "size", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "byteLength", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["../../../../src/lib/utils/resource-utils.ts"], "sourcesContent": ["// loaders.gl, MIT license\n\nimport {isResponse, isBlob} from '../../javascript-utils/is-type';\nimport {parseMIMEType, parseMIMETypeFromURL} from './mime-type-utils';\nimport {stripQueryString} from './url-utils';\n\n/**\n * A loadable resource. Includes:\n * `Response`, `Blob` (`File` is a subclass), string URLs and data URLs\n */\nexport type Resource = Response | Blob | string;\n\n/**\n * Returns the URL associated with this resource.\n * The returned value may include a query string and need further processing.\n * If it cannot determine url, the corresponding value will be an empty string\n *\n * @todo string parameters are assumed to be URLs\n */\nexport function getResourceUrl(resource: unknown): string {\n  // If resource is a `Response`, it contains the information directly as a field\n  if (isResponse(resource)) {\n    const response = resource as Response;\n    return response.url;\n  }\n\n  // If the resource is a Blob or a File (subclass of Blob)\n  if (isBlob(resource)) {\n    const blob = resource as Blob;\n    // File objects have a \"name\" property. Blob objects don't have any\n    // url (name) information\n    return blob.name || '';\n  }\n\n  if (typeof resource === 'string') {\n    return resource;\n  }\n\n  // Unknown\n  return '';\n}\n\n/**\n * Returns the URL associated with this resource.\n * The returned value may include a query string and need further processing.\n * If it cannot determine url, the corresponding value will be an empty string\n *\n * @todo string parameters are assumed to be URLs\n */\nexport function getResourceMIMEType(resource: unknown): string {\n  // If resource is a response, it contains the information directly\n  if (isResponse(resource)) {\n    const response = resource as Response;\n    const contentTypeHeader = response.headers.get('content-type') || '';\n    const noQueryUrl = stripQueryString(response.url);\n    return parseMIMEType(contentTypeHeader) || parseMIMETypeFromURL(noQueryUrl);\n  }\n\n  // If the resource is a Blob or a File (subclass of Blob)\n  if (isBlob(resource)) {\n    const blob = resource as Blob;\n    return blob.type || '';\n  }\n\n  if (typeof resource === 'string') {\n    return parseMIMETypeFromURL(resource);\n  }\n\n  // Unknown\n  return '';\n}\n\n/**\n  * Returns (approximate) content length for a resource if it can be determined.\n  * Returns -1 if content length cannot be determined.\n  * @param resource\n\n  * @note string parameters are NOT assumed to be URLs\n  */\nexport function getResourceContentLength(resource: unknown): number {\n  if (isResponse(resource)) {\n    const response = resource as Response;\n    return response.headers['content-length'] || -1;\n  }\n  if (isBlob(resource)) {\n    const blob = resource as Blob;\n    return blob.size;\n  }\n  if (typeof resource === 'string') {\n    // TODO - handle data URL?\n    return resource.length;\n  }\n  if (resource instanceof ArrayBuffer) {\n    return resource.byteLength;\n  }\n  if (ArrayBuffer.isView(resource)) {\n    return resource.byteLength;\n  }\n  return -1;\n}\n"], "mappings": "AAEA,SAAQA,UAAU,EAAEC,MAAM,QAAO,gCAAgC;AACjE,SAAQC,aAAa,EAAEC,oBAAoB,QAAO,mBAAmB;AACrE,SAAQC,gBAAgB,QAAO,aAAa;AAe5C,OAAO,SAASC,cAAcA,CAACC,QAAiB,EAAU;EAExD,IAAIN,UAAU,CAACM,QAAQ,CAAC,EAAE;IACxB,MAAMC,QAAQ,GAAGD,QAAoB;IACrC,OAAOC,QAAQ,CAACC,GAAG;EACrB;EAGA,IAAIP,MAAM,CAACK,QAAQ,CAAC,EAAE;IACpB,MAAMG,IAAI,GAAGH,QAAgB;IAG7B,OAAOG,IAAI,CAACC,IAAI,IAAI,EAAE;EACxB;EAEA,IAAI,OAAOJ,QAAQ,KAAK,QAAQ,EAAE;IAChC,OAAOA,QAAQ;EACjB;EAGA,OAAO,EAAE;AACX;AASA,OAAO,SAASK,mBAAmBA,CAACL,QAAiB,EAAU;EAE7D,IAAIN,UAAU,CAACM,QAAQ,CAAC,EAAE;IACxB,MAAMC,QAAQ,GAAGD,QAAoB;IACrC,MAAMM,iBAAiB,GAAGL,QAAQ,CAACM,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE;IACpE,MAAMC,UAAU,GAAGX,gBAAgB,CAACG,QAAQ,CAACC,GAAG,CAAC;IACjD,OAAON,aAAa,CAACU,iBAAiB,CAAC,IAAIT,oBAAoB,CAACY,UAAU,CAAC;EAC7E;EAGA,IAAId,MAAM,CAACK,QAAQ,CAAC,EAAE;IACpB,MAAMG,IAAI,GAAGH,QAAgB;IAC7B,OAAOG,IAAI,CAACO,IAAI,IAAI,EAAE;EACxB;EAEA,IAAI,OAAOV,QAAQ,KAAK,QAAQ,EAAE;IAChC,OAAOH,oBAAoB,CAACG,QAAQ,CAAC;EACvC;EAGA,OAAO,EAAE;AACX;AASA,OAAO,SAASW,wBAAwBA,CAACX,QAAiB,EAAU;EAClE,IAAIN,UAAU,CAACM,QAAQ,CAAC,EAAE;IACxB,MAAMC,QAAQ,GAAGD,QAAoB;IACrC,OAAOC,QAAQ,CAACM,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;EACjD;EACA,IAAIZ,MAAM,CAACK,QAAQ,CAAC,EAAE;IACpB,MAAMG,IAAI,GAAGH,QAAgB;IAC7B,OAAOG,IAAI,CAACS,IAAI;EAClB;EACA,IAAI,OAAOZ,QAAQ,KAAK,QAAQ,EAAE;IAEhC,OAAOA,QAAQ,CAACa,MAAM;EACxB;EACA,IAAIb,QAAQ,YAAYc,WAAW,EAAE;IACnC,OAAOd,QAAQ,CAACe,UAAU;EAC5B;EACA,IAAID,WAAW,CAACE,MAAM,CAAChB,QAAQ,CAAC,EAAE;IAChC,OAAOA,QAAQ,CAACe,UAAU;EAC5B;EACA,OAAO,CAAC,CAAC;AACX"}