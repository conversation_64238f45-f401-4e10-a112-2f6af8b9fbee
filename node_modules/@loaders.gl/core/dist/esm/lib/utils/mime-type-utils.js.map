{"version": 3, "file": "mime-type-utils.js", "names": ["DATA_URL_PATTERN", "MIME_TYPE_PATTERN", "parseMIMEType", "mimeString", "matches", "exec", "parseMIMETypeFromURL", "url"], "sources": ["../../../../src/lib/utils/mime-type-utils.ts"], "sourcesContent": ["// TODO - build/integrate proper MIME type parsing\n// https://mimesniff.spec.whatwg.org/\n\nconst DATA_URL_PATTERN = /^data:([-\\w.]+\\/[-\\w.+]+)(;|,)/;\nconst MIME_TYPE_PATTERN = /^([-\\w.]+\\/[-\\w.+]+)/;\n\n/**\n * Remove extra data like `charset` from MIME types\n * @param mimeString\n * @returns A clean MIME type, or an empty string\n *\n * @todo - handle more advanced MIMETYpes, multiple types\n * @todo - extract charset etc\n */\nexport function parseMIMEType(mimeString: string): string {\n  // If resource is a data url, extract any embedded mime type\n  const matches = MIME_TYPE_PATTERN.exec(mimeString);\n  if (matches) {\n    return matches[1];\n  }\n  return mimeString;\n}\n\n/**\n * Extract MIME type from data URL\n *\n * @param mimeString\n * @returns A clean MIME type, or an empty string\n *\n * @todo - handle more advanced MIMETYpes, multiple types\n * @todo - extract charset etc\n */\nexport function parseMIMETypeFromURL(url: string): string {\n  // If resource is a data URL, extract any embedded mime type\n  const matches = DATA_URL_PATTERN.exec(url);\n  if (matches) {\n    return matches[1];\n  }\n  return '';\n}\n"], "mappings": "AAGA,MAAMA,gBAAgB,GAAG,gCAAgC;AACzD,MAAMC,iBAAiB,GAAG,sBAAsB;AAUhD,OAAO,SAASC,aAAaA,CAACC,UAAkB,EAAU;EAExD,MAAMC,OAAO,GAAGH,iBAAiB,CAACI,IAAI,CAACF,UAAU,CAAC;EAClD,IAAIC,OAAO,EAAE;IACX,OAAOA,OAAO,CAAC,CAAC,CAAC;EACnB;EACA,OAAOD,UAAU;AACnB;AAWA,OAAO,SAASG,oBAAoBA,CAACC,GAAW,EAAU;EAExD,MAAMH,OAAO,GAAGJ,gBAAgB,CAACK,IAAI,CAACE,GAAG,CAAC;EAC1C,IAAIH,OAAO,EAAE;IACX,OAAOA,OAAO,CAAC,CAAC,CAAC;EACnB;EACA,OAAO,EAAE;AACX"}