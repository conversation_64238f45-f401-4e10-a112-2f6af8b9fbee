{"version": 3, "file": "url-utils.js", "names": ["QUERY_STRING_PATTERN", "extractQueryString", "url", "matches", "match", "stripQueryString", "replace"], "sources": ["../../../../src/lib/utils/url-utils.ts"], "sourcesContent": ["// loaders.gl, MIT license\n\nconst QUERY_STRING_PATTERN = /\\?.*/;\n\nexport function extractQueryString(url): string {\n  const matches = url.match(QUERY_STRING_PATTERN);\n  return matches && matches[0];\n}\n\nexport function stripQueryString(url): string {\n  return url.replace(QUERY_STRING_PATTERN, '');\n}\n"], "mappings": "AAEA,MAAMA,oBAAoB,GAAG,MAAM;AAEnC,OAAO,SAASC,kBAAkBA,CAACC,GAAG,EAAU;EAC9C,MAAMC,OAAO,GAAGD,GAAG,CAACE,KAAK,CAACJ,oBAAoB,CAAC;EAC/C,OAAOG,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;AAC9B;AAEA,OAAO,SAASE,gBAAgBA,CAACH,GAAG,EAAU;EAC5C,OAAOA,GAAG,CAACI,OAAO,CAACN,oBAAoB,EAAE,EAAE,CAAC;AAC9C"}