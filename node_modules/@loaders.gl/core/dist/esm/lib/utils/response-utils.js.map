{"version": 3, "file": "response-utils.js", "names": ["isResponse", "getResourceContentLength", "getResourceUrl", "getResourceMIMEType", "makeResponse", "resource", "headers", "contentLength", "String", "url", "type", "initialDataUrl", "getInitialDataUrl", "TextEncoder", "encode", "response", "Response", "Object", "defineProperty", "value", "checkResponse", "ok", "message", "getResponseError", "Error", "checkResponseSync", "concat", "status", "statusText", "length", "slice", "contentType", "get", "text", "includes", "error", "INITIAL_DATA_LENGTH", "Blob", "blobSlice", "Promise", "resolve", "reader", "FileReader", "onload", "event", "_event$target", "target", "result", "readAsDataURL", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "base64", "arrayBufferToBase64", "buffer", "binary", "bytes", "Uint8Array", "i", "byteLength", "fromCharCode", "btoa"], "sources": ["../../../../src/lib/utils/response-utils.ts"], "sourcesContent": ["import {isResponse} from '../../javascript-utils/is-type';\nimport {getResourceContentLength, getResourceUrl, getResourceMIMEType} from './resource-utils';\n\n/**\n * Returns a Response object\n * Adds content-length header when possible\n *\n * @param resource\n */\nexport async function makeResponse(resource: any): Promise<Response> {\n  if (isResponse(resource)) {\n    return resource;\n  }\n\n  // Add content-length header if possible\n  const headers: {[header: string]: string} = {};\n\n  const contentLength = getResourceContentLength(resource);\n  if (contentLength >= 0) {\n    headers['content-length'] = String(contentLength);\n  }\n\n  // `new Response(File)` does not preserve content-type and URL\n  // so we add them here\n  const url = getResourceUrl(resource);\n  const type = getResourceMIMEType(resource);\n  if (type) {\n    headers['content-type'] = type;\n  }\n\n  // Add a custom header with initial bytes if available\n  const initialDataUrl = await getInitialDataUrl(resource);\n  if (initialDataUrl) {\n    headers['x-first-bytes'] = initialDataUrl;\n  }\n\n  // TODO - is this the best way of handling strings?\n  // Maybe package as data URL instead?\n  if (typeof resource === 'string') {\n    // Convert to ArrayBuffer to avoid Response treating it as a URL\n    resource = new TextEncoder().encode(resource);\n  }\n\n  // Attempt to create a Response from the resource, adding headers and setting url\n  const response = new Response(resource, {headers});\n  // We can't control `Response.url` via constructor, use a property override to record URL.\n  Object.defineProperty(response, 'url', {value: url});\n  return response;\n}\n\n/**\n * Checks response status (async) and throws a helpful error message if status is not OK.\n * @param response\n */\nexport async function checkResponse(response: Response): Promise<void> {\n  if (!response.ok) {\n    const message = await getResponseError(response);\n    throw new Error(message);\n  }\n}\n\n/**\n * Checks response status (sync) and throws a helpful error message if status is not OK.\n * @param response\n */\nexport function checkResponseSync(response: Response): void {\n  if (!response.ok) {\n    let message = `${response.status} ${response.statusText}`;\n    message = message.length > 60 ? `${message.slice(0, 60)}...` : message;\n    throw new Error(message);\n  }\n}\n\n// HELPERS\n\nasync function getResponseError(response): Promise<string> {\n  let message = `Failed to fetch resource ${response.url} (${response.status}): `;\n  try {\n    const contentType = response.headers.get('Content-Type');\n    let text = response.statusText;\n    if (contentType.includes('application/json')) {\n      text += ` ${await response.text()}`;\n    }\n    message += text;\n    message = message.length > 60 ? `${message.slice(0, 60)}...` : message;\n  } catch (error) {\n    // eslint forbids return in a finally statement, so we just catch here\n  }\n  return message;\n}\n\nasync function getInitialDataUrl(resource): Promise<string | null> {\n  const INITIAL_DATA_LENGTH = 5;\n  if (typeof resource === 'string') {\n    return `data:,${resource.slice(0, INITIAL_DATA_LENGTH)}`;\n  }\n  if (resource instanceof Blob) {\n    const blobSlice = resource.slice(0, 5);\n    return await new Promise((resolve) => {\n      const reader = new FileReader();\n      reader.onload = (event) => resolve(event?.target?.result as string);\n      reader.readAsDataURL(blobSlice);\n    });\n  }\n  if (resource instanceof ArrayBuffer) {\n    const slice = resource.slice(0, INITIAL_DATA_LENGTH);\n    const base64 = arrayBufferToBase64(slice);\n    return `data:base64,${base64}`;\n  }\n  return null;\n}\n\n// https://stackoverflow.com/questions/9267899/arraybuffer-to-base64-encoded-string\nfunction arrayBufferToBase64(buffer) {\n  let binary = '';\n  const bytes = new Uint8Array(buffer);\n  for (let i = 0; i < bytes.byteLength; i++) {\n    binary += String.fromCharCode(bytes[i]);\n  }\n  return btoa(binary);\n}\n"], "mappings": "AAAA,SAAQA,UAAU,QAAO,gCAAgC;AACzD,SAAQC,wBAAwB,EAAEC,cAAc,EAAEC,mBAAmB,QAAO,kBAAkB;AAQ9F,OAAO,eAAeC,YAAYA,CAACC,QAAa,EAAqB;EACnE,IAAIL,UAAU,CAACK,QAAQ,CAAC,EAAE;IACxB,OAAOA,QAAQ;EACjB;EAGA,MAAMC,OAAmC,GAAG,CAAC,CAAC;EAE9C,MAAMC,aAAa,GAAGN,wBAAwB,CAACI,QAAQ,CAAC;EACxD,IAAIE,aAAa,IAAI,CAAC,EAAE;IACtBD,OAAO,CAAC,gBAAgB,CAAC,GAAGE,MAAM,CAACD,aAAa,CAAC;EACnD;EAIA,MAAME,GAAG,GAAGP,cAAc,CAACG,QAAQ,CAAC;EACpC,MAAMK,IAAI,GAAGP,mBAAmB,CAACE,QAAQ,CAAC;EAC1C,IAAIK,IAAI,EAAE;IACRJ,OAAO,CAAC,cAAc,CAAC,GAAGI,IAAI;EAChC;EAGA,MAAMC,cAAc,GAAG,MAAMC,iBAAiB,CAACP,QAAQ,CAAC;EACxD,IAAIM,cAAc,EAAE;IAClBL,OAAO,CAAC,eAAe,CAAC,GAAGK,cAAc;EAC3C;EAIA,IAAI,OAAON,QAAQ,KAAK,QAAQ,EAAE;IAEhCA,QAAQ,GAAG,IAAIQ,WAAW,CAAC,CAAC,CAACC,MAAM,CAACT,QAAQ,CAAC;EAC/C;EAGA,MAAMU,QAAQ,GAAG,IAAIC,QAAQ,CAACX,QAAQ,EAAE;IAACC;EAAO,CAAC,CAAC;EAElDW,MAAM,CAACC,cAAc,CAACH,QAAQ,EAAE,KAAK,EAAE;IAACI,KAAK,EAAEV;EAAG,CAAC,CAAC;EACpD,OAAOM,QAAQ;AACjB;AAMA,OAAO,eAAeK,aAAaA,CAACL,QAAkB,EAAiB;EACrE,IAAI,CAACA,QAAQ,CAACM,EAAE,EAAE;IAChB,MAAMC,OAAO,GAAG,MAAMC,gBAAgB,CAACR,QAAQ,CAAC;IAChD,MAAM,IAAIS,KAAK,CAACF,OAAO,CAAC;EAC1B;AACF;AAMA,OAAO,SAASG,iBAAiBA,CAACV,QAAkB,EAAQ;EAC1D,IAAI,CAACA,QAAQ,CAACM,EAAE,EAAE;IAChB,IAAIC,OAAO,MAAAI,MAAA,CAAMX,QAAQ,CAACY,MAAM,OAAAD,MAAA,CAAIX,QAAQ,CAACa,UAAU,CAAE;IACzDN,OAAO,GAAGA,OAAO,CAACO,MAAM,GAAG,EAAE,MAAAH,MAAA,CAAMJ,OAAO,CAACQ,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,WAAQR,OAAO;IACtE,MAAM,IAAIE,KAAK,CAACF,OAAO,CAAC;EAC1B;AACF;AAIA,eAAeC,gBAAgBA,CAACR,QAAQ,EAAmB;EACzD,IAAIO,OAAO,+BAAAI,MAAA,CAA+BX,QAAQ,CAACN,GAAG,QAAAiB,MAAA,CAAKX,QAAQ,CAACY,MAAM,QAAK;EAC/E,IAAI;IACF,MAAMI,WAAW,GAAGhB,QAAQ,CAACT,OAAO,CAAC0B,GAAG,CAAC,cAAc,CAAC;IACxD,IAAIC,IAAI,GAAGlB,QAAQ,CAACa,UAAU;IAC9B,IAAIG,WAAW,CAACG,QAAQ,CAAC,kBAAkB,CAAC,EAAE;MAC5CD,IAAI,QAAAP,MAAA,CAAQ,MAAMX,QAAQ,CAACkB,IAAI,CAAC,CAAC,CAAE;IACrC;IACAX,OAAO,IAAIW,IAAI;IACfX,OAAO,GAAGA,OAAO,CAACO,MAAM,GAAG,EAAE,MAAAH,MAAA,CAAMJ,OAAO,CAACQ,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,WAAQR,OAAO;EACxE,CAAC,CAAC,OAAOa,KAAK,EAAE,CAEhB;EACA,OAAOb,OAAO;AAChB;AAEA,eAAeV,iBAAiBA,CAACP,QAAQ,EAA0B;EACjE,MAAM+B,mBAAmB,GAAG,CAAC;EAC7B,IAAI,OAAO/B,QAAQ,KAAK,QAAQ,EAAE;IAChC,gBAAAqB,MAAA,CAAgBrB,QAAQ,CAACyB,KAAK,CAAC,CAAC,EAAEM,mBAAmB,CAAC;EACxD;EACA,IAAI/B,QAAQ,YAAYgC,IAAI,EAAE;IAC5B,MAAMC,SAAS,GAAGjC,QAAQ,CAACyB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACtC,OAAO,MAAM,IAAIS,OAAO,CAAEC,OAAO,IAAK;MACpC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,KAAK;QAAA,IAAAC,aAAA;QAAA,OAAKL,OAAO,CAACI,KAAK,aAALA,KAAK,wBAAAC,aAAA,GAALD,KAAK,CAAEE,MAAM,cAAAD,aAAA,uBAAbA,aAAA,CAAeE,MAAgB,CAAC;MAAA;MACnEN,MAAM,CAACO,aAAa,CAACV,SAAS,CAAC;IACjC,CAAC,CAAC;EACJ;EACA,IAAIjC,QAAQ,YAAY4C,WAAW,EAAE;IACnC,MAAMnB,KAAK,GAAGzB,QAAQ,CAACyB,KAAK,CAAC,CAAC,EAAEM,mBAAmB,CAAC;IACpD,MAAMc,MAAM,GAAGC,mBAAmB,CAACrB,KAAK,CAAC;IACzC,sBAAAJ,MAAA,CAAsBwB,MAAM;EAC9B;EACA,OAAO,IAAI;AACb;AAGA,SAASC,mBAAmBA,CAACC,MAAM,EAAE;EACnC,IAAIC,MAAM,GAAG,EAAE;EACf,MAAMC,KAAK,GAAG,IAAIC,UAAU,CAACH,MAAM,CAAC;EACpC,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,UAAU,EAAED,CAAC,EAAE,EAAE;IACzCH,MAAM,IAAI7C,MAAM,CAACkD,YAAY,CAACJ,KAAK,CAACE,CAAC,CAAC,CAAC;EACzC;EACA,OAAOG,IAAI,CAACN,MAAM,CAAC;AACrB"}