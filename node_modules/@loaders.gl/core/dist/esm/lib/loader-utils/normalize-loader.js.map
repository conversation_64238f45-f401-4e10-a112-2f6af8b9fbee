{"version": 3, "file": "normalize-loader.js", "names": ["assert", "isLoaderObject", "loader", "_loader", "Array", "isArray", "hasExtensions", "extensions", "normalize<PERSON><PERSON><PERSON>", "_loader2", "_loader3", "options", "parseTextSync", "parseText", "text", "binary"], "sources": ["../../../../src/lib/loader-utils/normalize-loader.ts"], "sourcesContent": ["import type {Loader} from '@loaders.gl/loader-utils';\nimport {assert} from '@loaders.gl/loader-utils';\n\nexport function isLoaderObject(loader?: any): boolean {\n  if (!loader) {\n    return false;\n  }\n\n  if (Array.isArray(loader)) {\n    loader = loader[0];\n  }\n\n  const hasExtensions = Array.isArray(loader?.extensions);\n\n  /* Now handled by types and worker loaders do not have these\n  let hasParser =\n    loader.parseTextSync ||\n    loader.parseSync ||\n    loader.parse ||\n    loader.parseStream || // TODO Remove, Replace with parseInBatches\n    loader.parseInBatches;\n  */\n\n  return hasExtensions;\n}\n\nexport function normalizeLoader(loader: Loader): Loader {\n  // This error is fairly easy to trigger by mixing up import statements etc\n  // So we make an exception and add a developer error message for this case\n  // To help new users from getting stuck here\n  assert(loader, 'null loader');\n  assert(isLoaderObject(loader), 'invalid loader');\n\n  // NORMALIZE [LOADER, OPTIONS] => LOADER\n\n  // If [loader, options], create a new loaders object with options merged in\n  let options;\n  if (Array.isArray(loader)) {\n    options = loader[1];\n    loader = loader[0];\n    loader = {\n      ...loader,\n      options: {...loader.options, ...options}\n    };\n  }\n\n  // NORMALIZE text and binary flags\n  // Ensure at least one of text/binary flags are properly set\n\n  // @ts-expect-error\n  if (loader?.parseTextSync || loader?.parseText) {\n    loader.text = true;\n  }\n\n  if (!loader.text) {\n    loader.binary = true;\n  }\n\n  return loader;\n}\n"], "mappings": "AACA,SAAQA,MAAM,QAAO,0BAA0B;AAE/C,OAAO,SAASC,cAAcA,CAACC,MAAY,EAAW;EAAA,IAAAC,OAAA;EACpD,IAAI,CAACD,MAAM,EAAE;IACX,OAAO,KAAK;EACd;EAEA,IAAIE,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;IACzBA,MAAM,GAAGA,MAAM,CAAC,CAAC,CAAC;EACpB;EAEA,MAAMI,aAAa,GAAGF,KAAK,CAACC,OAAO,EAAAF,OAAA,GAACD,MAAM,cAAAC,OAAA,uBAANA,OAAA,CAAQI,UAAU,CAAC;EAWvD,OAAOD,aAAa;AACtB;AAEA,OAAO,SAASE,eAAeA,CAACN,MAAc,EAAU;EAAA,IAAAO,QAAA,EAAAC,QAAA;EAItDV,MAAM,CAACE,MAAM,EAAE,aAAa,CAAC;EAC7BF,MAAM,CAACC,cAAc,CAACC,MAAM,CAAC,EAAE,gBAAgB,CAAC;EAKhD,IAAIS,OAAO;EACX,IAAIP,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;IACzBS,OAAO,GAAGT,MAAM,CAAC,CAAC,CAAC;IACnBA,MAAM,GAAGA,MAAM,CAAC,CAAC,CAAC;IAClBA,MAAM,GAAG;MACP,GAAGA,MAAM;MACTS,OAAO,EAAE;QAAC,GAAGT,MAAM,CAACS,OAAO;QAAE,GAAGA;MAAO;IACzC,CAAC;EACH;EAMA,IAAI,CAAAF,QAAA,GAAAP,MAAM,cAAAO,QAAA,eAANA,QAAA,CAAQG,aAAa,KAAAF,QAAA,GAAIR,MAAM,cAAAQ,QAAA,eAANA,QAAA,CAAQG,SAAS,EAAE;IAC9CX,MAAM,CAACY,IAAI,GAAG,IAAI;EACpB;EAEA,IAAI,CAACZ,MAAM,CAACY,IAAI,EAAE;IAChBZ,MAAM,CAACa,MAAM,GAAG,IAAI;EACtB;EAEA,OAAOb,MAAM;AACf"}