{"version": 3, "file": "check-errors.js", "names": ["checkFetchResponseStatus", "response", "ok", "errorMessage", "concat", "status", "statusText", "text", "getErrorText", "error", "Error", "checkFetchResponseStatusSync", "matches", "match", "slice"], "sources": ["../../../../src/lib/loader-utils/check-errors.ts"], "sourcesContent": ["export async function checkFetchResponseStatus(response) {\n  if (!response.ok) {\n    let errorMessage = `fetch failed ${response.status} ${response.statusText}`;\n    try {\n      const text = await response.text();\n      if (text) {\n        errorMessage += `: ${getErrorText(text)}`;\n      }\n    } catch (error) {\n      // ignore error\n    }\n    throw new Error(errorMessage);\n  }\n}\n\nexport function checkFetchResponseStatusSync(response) {\n  if (!response.ok) {\n    throw new Error(`fetch failed ${response.status}`);\n  }\n}\n\nfunction getErrorText(text) {\n  // Look for HTML error texts\n  const matches = text.match('<pre>(.*)</pre>');\n  return matches ? matches[1] : ` ${text.slice(0, 10)}...`;\n}\n"], "mappings": "AAAA,OAAO,eAAeA,wBAAwBA,CAACC,QAAQ,EAAE;EACvD,IAAI,CAACA,QAAQ,CAACC,EAAE,EAAE;IAChB,IAAIC,YAAY,mBAAAC,MAAA,CAAmBH,QAAQ,CAACI,MAAM,OAAAD,MAAA,CAAIH,QAAQ,CAACK,UAAU,CAAE;IAC3E,IAAI;MACF,MAAMC,IAAI,GAAG,MAAMN,QAAQ,CAACM,IAAI,CAAC,CAAC;MAClC,IAAIA,IAAI,EAAE;QACRJ,YAAY,SAAAC,MAAA,CAASI,YAAY,CAACD,IAAI,CAAC,CAAE;MAC3C;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE,CAEhB;IACA,MAAM,IAAIC,KAAK,CAACP,YAAY,CAAC;EAC/B;AACF;AAEA,OAAO,SAASQ,4BAA4BA,CAACV,QAAQ,EAAE;EACrD,IAAI,CAACA,QAAQ,CAACC,EAAE,EAAE;IAChB,MAAM,IAAIQ,KAAK,iBAAAN,MAAA,CAAiBH,QAAQ,CAACI,MAAM,CAAE,CAAC;EACpD;AACF;AAEA,SAASG,YAAYA,CAACD,IAAI,EAAE;EAE1B,MAAMK,OAAO,GAAGL,IAAI,CAACM,KAAK,CAAC,iBAAiB,CAAC;EAC7C,OAAOD,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC,OAAAR,MAAA,CAAOG,IAAI,CAACO,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,QAAK;AAC1D"}