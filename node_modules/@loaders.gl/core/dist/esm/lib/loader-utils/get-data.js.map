{"version": 3, "file": "get-data.js", "names": ["concatenateArrayBuffersAsync", "isResponse", "isReadableStream", "isAsyncIterable", "isIterable", "isIterator", "isBlob", "<PERSON><PERSON><PERSON><PERSON>", "makeIterator", "checkResponse", "makeResponse", "ERR_DATA", "getArrayBufferOrStringFromDataSync", "data", "loader", "options", "text", "buffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arrayBuffer", "binary", "textDecoder", "TextDecoder", "decode", "<PERSON><PERSON><PERSON><PERSON>", "byteLength", "length", "byteOffset", "slice", "Error", "getArrayBufferOrStringFromData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "response", "getAsyncIterableFromData", "body", "Symbol", "asyncIterator", "getIterableFromData", "getReadableStream", "oneChunk", "iterator"], "sources": ["../../../../src/lib/loader-utils/get-data.ts"], "sourcesContent": ["import type {\n  DataType,\n  SyncDataType,\n  BatchableDataType,\n  Loader,\n  LoaderOptions\n} from '@loaders.gl/loader-utils';\nimport {concatenateArrayBuffersAsync} from '@loaders.gl/loader-utils';\nimport {\n  isResponse,\n  isReadableStream,\n  isAsyncIterable,\n  isIterable,\n  isIterator,\n  isBlob,\n  isBuffer\n} from '../../javascript-utils/is-type';\nimport {makeIterator} from '../../iterators/make-iterator/make-iterator';\nimport {checkResponse, makeResponse} from '../utils/response-utils';\n\nconst ERR_DATA = 'Cannot convert supplied data type';\n\n// eslint-disable-next-line complexity\nexport function getArrayBufferOrStringFromDataSync(\n  data: SyncDataType,\n  loader: Loader,\n  options: LoaderOptions\n): ArrayBuffer | string {\n  if (loader.text && typeof data === 'string') {\n    return data;\n  }\n\n  if (isBuffer(data)) {\n    // @ts-ignore\n    data = data.buffer;\n  }\n\n  if (data instanceof ArrayBuffer) {\n    const arrayBuffer = data;\n    if (loader.text && !loader.binary) {\n      const textDecoder = new TextDecoder('utf8');\n      return textDecoder.decode(arrayBuffer);\n    }\n    return arrayBuffer;\n  }\n\n  // We may need to handle offsets\n  if (ArrayBuffer.isView(data)) {\n    // TextDecoder is invoked on typed arrays and will handle offsets\n    if (loader.text && !loader.binary) {\n      const textDecoder = new TextDecoder('utf8');\n      return textDecoder.decode(data);\n    }\n\n    let arrayBuffer = data.buffer;\n\n    // Since we are returning the underlying arrayBuffer, we must create a new copy\n    // if this typed array / Buffer is a partial view into the ArryayBuffer\n    // TODO - this is a potentially unnecessary copy\n    const byteLength = data.byteLength || data.length;\n    if (data.byteOffset !== 0 || byteLength !== arrayBuffer.byteLength) {\n      // console.warn(`loaders.gl copying arraybuffer of length ${byteLength}`);\n      arrayBuffer = arrayBuffer.slice(data.byteOffset, data.byteOffset + byteLength);\n    }\n    return arrayBuffer;\n  }\n\n  throw new Error(ERR_DATA);\n}\n\n// Convert async iterator to a promise\nexport async function getArrayBufferOrStringFromData(\n  data: DataType,\n  loader: Loader,\n  options: LoaderOptions\n): Promise<ArrayBuffer | string> {\n  const isArrayBuffer = data instanceof ArrayBuffer || ArrayBuffer.isView(data);\n  if (typeof data === 'string' || isArrayBuffer) {\n    return getArrayBufferOrStringFromDataSync(data as string | ArrayBuffer, loader, options);\n  }\n\n  // Blobs and files are FileReader compatible\n  if (isBlob(data)) {\n    data = await makeResponse(data);\n  }\n\n  if (isResponse(data)) {\n    const response = data as Response;\n    await checkResponse(response);\n    return loader.binary ? await response.arrayBuffer() : await response.text();\n  }\n\n  if (isReadableStream(data)) {\n    // @ts-expect-error TS2559 options type\n    data = makeIterator(data as ReadableStream, options);\n  }\n\n  if (isIterable(data) || isAsyncIterable(data)) {\n    // Assume arrayBuffer iterator - attempt to concatenate\n    return concatenateArrayBuffersAsync(data as AsyncIterable<ArrayBuffer>);\n  }\n\n  throw new Error(ERR_DATA);\n}\n\nexport async function getAsyncIterableFromData(\n  data: BatchableDataType,\n  options: LoaderOptions\n): Promise<AsyncIterable<ArrayBuffer> | Iterable<ArrayBuffer>> {\n  if (isIterator(data)) {\n    return data as AsyncIterable<ArrayBuffer>;\n  }\n\n  if (isResponse(data)) {\n    const response = data as Response;\n    // Note Since this function is not async, we currently can't load error message, just status\n    await checkResponse(response);\n    // TODO - bug in polyfill, body can be a Promise under Node.js\n    // eslint-disable-next-line @typescript-eslint/await-thenable\n    const body = await response.body;\n    // TODO - body can be null?\n    return makeIterator(body as ReadableStream<Uint8Array>, options as any);\n  }\n\n  if (isBlob(data) || isReadableStream(data)) {\n    return makeIterator(data as Blob | ReadableStream, options as any);\n  }\n\n  if (isAsyncIterable(data)) {\n    return data[Symbol.asyncIterator]();\n  }\n\n  return getIterableFromData(data);\n}\n\nexport async function getReadableStream(data: BatchableDataType): Promise<ReadableStream> {\n  if (isReadableStream(data)) {\n    return data as ReadableStream;\n  }\n  if (isResponse(data)) {\n    // @ts-ignore\n    return data.body;\n  }\n  const response = await makeResponse(data);\n  // @ts-ignore\n  return response.body;\n}\n\n// HELPERS\n\nfunction getIterableFromData(data) {\n  // generate an iterator that emits a single chunk\n  if (ArrayBuffer.isView(data)) {\n    return (function* oneChunk() {\n      yield data.buffer;\n    })();\n  }\n\n  if (data instanceof ArrayBuffer) {\n    return (function* oneChunk() {\n      yield data;\n    })();\n  }\n\n  if (isIterator(data)) {\n    return data;\n  }\n\n  if (isIterable(data)) {\n    return data[Symbol.iterator]();\n  }\n\n  throw new Error(ERR_DATA);\n}\n"], "mappings": "AAOA,SAAQA,4BAA4B,QAAO,0BAA0B;AACrE,SACEC,UAAU,EACVC,gBAAgB,EAChBC,eAAe,EACfC,UAAU,EACVC,UAAU,EACVC,MAAM,EACNC,QAAQ,QACH,gCAAgC;AACvC,SAAQC,YAAY,QAAO,6CAA6C;AACxE,SAAQC,aAAa,EAAEC,YAAY,QAAO,yBAAyB;AAEnE,MAAMC,QAAQ,GAAG,mCAAmC;AAGpD,OAAO,SAASC,kCAAkCA,CAChDC,IAAkB,EAClBC,MAAc,EACdC,OAAsB,EACA;EACtB,IAAID,MAAM,CAACE,IAAI,IAAI,OAAOH,IAAI,KAAK,QAAQ,EAAE;IAC3C,OAAOA,IAAI;EACb;EAEA,IAAIN,QAAQ,CAACM,IAAI,CAAC,EAAE;IAElBA,IAAI,GAAGA,IAAI,CAACI,MAAM;EACpB;EAEA,IAAIJ,IAAI,YAAYK,WAAW,EAAE;IAC/B,MAAMC,WAAW,GAAGN,IAAI;IACxB,IAAIC,MAAM,CAACE,IAAI,IAAI,CAACF,MAAM,CAACM,MAAM,EAAE;MACjC,MAAMC,WAAW,GAAG,IAAIC,WAAW,CAAC,MAAM,CAAC;MAC3C,OAAOD,WAAW,CAACE,MAAM,CAACJ,WAAW,CAAC;IACxC;IACA,OAAOA,WAAW;EACpB;EAGA,IAAID,WAAW,CAACM,MAAM,CAACX,IAAI,CAAC,EAAE;IAE5B,IAAIC,MAAM,CAACE,IAAI,IAAI,CAACF,MAAM,CAACM,MAAM,EAAE;MACjC,MAAMC,WAAW,GAAG,IAAIC,WAAW,CAAC,MAAM,CAAC;MAC3C,OAAOD,WAAW,CAACE,MAAM,CAACV,IAAI,CAAC;IACjC;IAEA,IAAIM,WAAW,GAAGN,IAAI,CAACI,MAAM;IAK7B,MAAMQ,UAAU,GAAGZ,IAAI,CAACY,UAAU,IAAIZ,IAAI,CAACa,MAAM;IACjD,IAAIb,IAAI,CAACc,UAAU,KAAK,CAAC,IAAIF,UAAU,KAAKN,WAAW,CAACM,UAAU,EAAE;MAElEN,WAAW,GAAGA,WAAW,CAACS,KAAK,CAACf,IAAI,CAACc,UAAU,EAAEd,IAAI,CAACc,UAAU,GAAGF,UAAU,CAAC;IAChF;IACA,OAAON,WAAW;EACpB;EAEA,MAAM,IAAIU,KAAK,CAAClB,QAAQ,CAAC;AAC3B;AAGA,OAAO,eAAemB,8BAA8BA,CAClDjB,IAAc,EACdC,MAAc,EACdC,OAAsB,EACS;EAC/B,MAAMgB,aAAa,GAAGlB,IAAI,YAAYK,WAAW,IAAIA,WAAW,CAACM,MAAM,CAACX,IAAI,CAAC;EAC7E,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIkB,aAAa,EAAE;IAC7C,OAAOnB,kCAAkC,CAACC,IAAI,EAA0BC,MAAM,EAAEC,OAAO,CAAC;EAC1F;EAGA,IAAIT,MAAM,CAACO,IAAI,CAAC,EAAE;IAChBA,IAAI,GAAG,MAAMH,YAAY,CAACG,IAAI,CAAC;EACjC;EAEA,IAAIZ,UAAU,CAACY,IAAI,CAAC,EAAE;IACpB,MAAMmB,QAAQ,GAAGnB,IAAgB;IACjC,MAAMJ,aAAa,CAACuB,QAAQ,CAAC;IAC7B,OAAOlB,MAAM,CAACM,MAAM,GAAG,MAAMY,QAAQ,CAACb,WAAW,CAAC,CAAC,GAAG,MAAMa,QAAQ,CAAChB,IAAI,CAAC,CAAC;EAC7E;EAEA,IAAId,gBAAgB,CAACW,IAAI,CAAC,EAAE;IAE1BA,IAAI,GAAGL,YAAY,CAACK,IAAI,EAAoBE,OAAO,CAAC;EACtD;EAEA,IAAIX,UAAU,CAACS,IAAI,CAAC,IAAIV,eAAe,CAACU,IAAI,CAAC,EAAE;IAE7C,OAAOb,4BAA4B,CAACa,IAAkC,CAAC;EACzE;EAEA,MAAM,IAAIgB,KAAK,CAAClB,QAAQ,CAAC;AAC3B;AAEA,OAAO,eAAesB,wBAAwBA,CAC5CpB,IAAuB,EACvBE,OAAsB,EACuC;EAC7D,IAAIV,UAAU,CAACQ,IAAI,CAAC,EAAE;IACpB,OAAOA,IAAI;EACb;EAEA,IAAIZ,UAAU,CAACY,IAAI,CAAC,EAAE;IACpB,MAAMmB,QAAQ,GAAGnB,IAAgB;IAEjC,MAAMJ,aAAa,CAACuB,QAAQ,CAAC;IAG7B,MAAME,IAAI,GAAG,MAAMF,QAAQ,CAACE,IAAI;IAEhC,OAAO1B,YAAY,CAAC0B,IAAI,EAAgCnB,OAAc,CAAC;EACzE;EAEA,IAAIT,MAAM,CAACO,IAAI,CAAC,IAAIX,gBAAgB,CAACW,IAAI,CAAC,EAAE;IAC1C,OAAOL,YAAY,CAACK,IAAI,EAA2BE,OAAc,CAAC;EACpE;EAEA,IAAIZ,eAAe,CAACU,IAAI,CAAC,EAAE;IACzB,OAAOA,IAAI,CAACsB,MAAM,CAACC,aAAa,CAAC,CAAC,CAAC;EACrC;EAEA,OAAOC,mBAAmB,CAACxB,IAAI,CAAC;AAClC;AAEA,OAAO,eAAeyB,iBAAiBA,CAACzB,IAAuB,EAA2B;EACxF,IAAIX,gBAAgB,CAACW,IAAI,CAAC,EAAE;IAC1B,OAAOA,IAAI;EACb;EACA,IAAIZ,UAAU,CAACY,IAAI,CAAC,EAAE;IAEpB,OAAOA,IAAI,CAACqB,IAAI;EAClB;EACA,MAAMF,QAAQ,GAAG,MAAMtB,YAAY,CAACG,IAAI,CAAC;EAEzC,OAAOmB,QAAQ,CAACE,IAAI;AACtB;AAIA,SAASG,mBAAmBA,CAACxB,IAAI,EAAE;EAEjC,IAAIK,WAAW,CAACM,MAAM,CAACX,IAAI,CAAC,EAAE;IAC5B,OAAQ,UAAU0B,QAAQA,CAAA,EAAG;MAC3B,MAAM1B,IAAI,CAACI,MAAM;IACnB,CAAC,CAAE,CAAC;EACN;EAEA,IAAIJ,IAAI,YAAYK,WAAW,EAAE;IAC/B,OAAQ,UAAUqB,QAAQA,CAAA,EAAG;MAC3B,MAAM1B,IAAI;IACZ,CAAC,CAAE,CAAC;EACN;EAEA,IAAIR,UAAU,CAACQ,IAAI,CAAC,EAAE;IACpB,OAAOA,IAAI;EACb;EAEA,IAAIT,UAAU,CAACS,IAAI,CAAC,EAAE;IACpB,OAAOA,IAAI,CAACsB,MAAM,CAACK,QAAQ,CAAC,CAAC,CAAC;EAChC;EAEA,MAAM,IAAIX,KAAK,CAAClB,QAAQ,CAAC;AAC3B"}