{"version": 3, "file": "loader-context.js", "names": ["getFetchFunction", "extractQueryString", "stripQueryString", "path", "getLoaderContext", "context", "options", "parentContext", "newContext", "fetch", "url", "baseUrl", "queryString", "filename", "dirname", "Array", "isArray", "loaders", "getLoadersFromContext", "<PERSON><PERSON><PERSON><PERSON>", "contextLoaders", "length"], "sources": ["../../../../src/lib/loader-utils/loader-context.ts"], "sourcesContent": ["import type {Loader, LoaderOptions, LoaderContext} from '@loaders.gl/loader-utils';\nimport {getFetchFunction} from './get-fetch-function';\nimport {extractQueryString, stripQueryString} from '../utils/url-utils';\nimport {path} from '@loaders.gl/loader-utils';\n\n/**\n * \"sub\" loaders invoked by other loaders get a \"context\" injected on `this`\n * The context will inject core methods like `parse` and contain information\n * about loaders and options passed in to the top-level `parse` call.\n *\n * @param context\n * @param options\n * @param previousContext\n */\nexport function getLoaderContext(\n  context: Omit<LoaderContext, 'fetch'> & Partial<Pick<LoaderContext, 'fetch'>>,\n  options: LoaderOptions,\n  parentContext: LoaderContext | null\n): LoaderContext {\n  // For recursive calls, we already have a context\n  // TODO - add any additional loaders to context?\n  if (parentContext) {\n    return parentContext;\n  }\n\n  const newContext: LoaderContext = {\n    fetch: getFetchFunction(options, context),\n    ...context\n  };\n\n  // Parse URLs so that subloaders can easily generate correct strings\n  if (newContext.url) {\n    const baseUrl = stripQueryString(newContext.url);\n    newContext.baseUrl = baseUrl;\n    newContext.queryString = extractQueryString(newContext.url);\n    newContext.filename = path.filename(baseUrl);\n    newContext.baseUrl = path.dirname(baseUrl);\n  }\n\n  // Recursive loading does not use single loader\n  if (!Array.isArray(newContext.loaders)) {\n    newContext.loaders = null;\n  }\n\n  return newContext;\n}\n\n// eslint-disable-next-line complexity\nexport function getLoadersFromContext(\n  loaders: Loader[] | Loader | undefined,\n  context?: LoaderContext\n) {\n  // A single non-array loader is force selected, but only on top-level (context === null)\n  if (!context && loaders && !Array.isArray(loaders)) {\n    return loaders;\n  }\n\n  // Create a merged list\n  let candidateLoaders;\n  if (loaders) {\n    candidateLoaders = Array.isArray(loaders) ? loaders : [loaders];\n  }\n  if (context && context.loaders) {\n    const contextLoaders = Array.isArray(context.loaders) ? context.loaders : [context.loaders];\n    candidateLoaders = candidateLoaders ? [...candidateLoaders, ...contextLoaders] : contextLoaders;\n  }\n  // If no loaders, return null to look in globally registered loaders\n  return candidateLoaders && candidateLoaders.length ? candidateLoaders : null;\n}\n"], "mappings": "AACA,SAAQA,gBAAgB,QAAO,sBAAsB;AACrD,SAAQC,kBAAkB,EAAEC,gBAAgB,QAAO,oBAAoB;AACvE,SAAQC,IAAI,QAAO,0BAA0B;AAW7C,OAAO,SAASC,gBAAgBA,CAC9BC,OAA6E,EAC7EC,OAAsB,EACtBC,aAAmC,EACpB;EAGf,IAAIA,aAAa,EAAE;IACjB,OAAOA,aAAa;EACtB;EAEA,MAAMC,UAAyB,GAAG;IAChCC,KAAK,EAAET,gBAAgB,CAACM,OAAO,EAAED,OAAO,CAAC;IACzC,GAAGA;EACL,CAAC;EAGD,IAAIG,UAAU,CAACE,GAAG,EAAE;IAClB,MAAMC,OAAO,GAAGT,gBAAgB,CAACM,UAAU,CAACE,GAAG,CAAC;IAChDF,UAAU,CAACG,OAAO,GAAGA,OAAO;IAC5BH,UAAU,CAACI,WAAW,GAAGX,kBAAkB,CAACO,UAAU,CAACE,GAAG,CAAC;IAC3DF,UAAU,CAACK,QAAQ,GAAGV,IAAI,CAACU,QAAQ,CAACF,OAAO,CAAC;IAC5CH,UAAU,CAACG,OAAO,GAAGR,IAAI,CAACW,OAAO,CAACH,OAAO,CAAC;EAC5C;EAGA,IAAI,CAACI,KAAK,CAACC,OAAO,CAACR,UAAU,CAACS,OAAO,CAAC,EAAE;IACtCT,UAAU,CAACS,OAAO,GAAG,IAAI;EAC3B;EAEA,OAAOT,UAAU;AACnB;AAGA,OAAO,SAASU,qBAAqBA,CACnCD,OAAsC,EACtCZ,OAAuB,EACvB;EAEA,IAAI,CAACA,OAAO,IAAIY,OAAO,IAAI,CAACF,KAAK,CAACC,OAAO,CAACC,OAAO,CAAC,EAAE;IAClD,OAAOA,OAAO;EAChB;EAGA,IAAIE,gBAAgB;EACpB,IAAIF,OAAO,EAAE;IACXE,gBAAgB,GAAGJ,KAAK,CAACC,OAAO,CAACC,OAAO,CAAC,GAAGA,OAAO,GAAG,CAACA,OAAO,CAAC;EACjE;EACA,IAAIZ,OAAO,IAAIA,OAAO,CAACY,OAAO,EAAE;IAC9B,MAAMG,cAAc,GAAGL,KAAK,CAACC,OAAO,CAACX,OAAO,CAACY,OAAO,CAAC,GAAGZ,OAAO,CAACY,OAAO,GAAG,CAACZ,OAAO,CAACY,OAAO,CAAC;IAC3FE,gBAAgB,GAAGA,gBAAgB,GAAG,CAAC,GAAGA,gBAAgB,EAAE,GAAGC,cAAc,CAAC,GAAGA,cAAc;EACjG;EAEA,OAAOD,gBAAgB,IAAIA,gBAAgB,CAACE,MAAM,GAAGF,gBAAgB,GAAG,IAAI;AAC9E"}