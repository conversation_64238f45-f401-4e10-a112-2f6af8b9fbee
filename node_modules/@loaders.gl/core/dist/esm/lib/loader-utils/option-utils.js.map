{"version": 3, "file": "option-utils.js", "names": ["isPureObject", "isObject", "probeLog", "<PERSON><PERSON><PERSON><PERSON>", "DEFAULT_LOADER_OPTIONS", "REMOVED_LOADER_OPTIONS", "getGlobalLoaderState", "globalThis", "loaders", "_state", "getGlobalLoaderOptions", "state", "globalOptions", "setGlobalOptions", "options", "normalizeOptionsInternal", "normalizeOptions", "loader", "url", "Array", "isArray", "validateOptions", "validateOptionsObject", "idOptions", "id", "loaderOptions", "deprecatedOptions", "defaultOptions", "loaderName", "prefix", "concat", "key", "isSubOptions", "isBaseUriOption", "isWorkerUrlOption", "warn", "suggestion", "findSimilarOption", "optionKey", "lowerCaseOptionKey", "toLowerCase", "bestSuggestion", "lowerCaseKey", "isPartialMatch", "startsWith", "loaderDefaultOptions", "mergedOptions", "addUrlOptions", "log", "mergeNestedFields", "value", "baseUri"], "sources": ["../../../../src/lib/loader-utils/option-utils.ts"], "sourcesContent": ["// loaders.gl, MIT license\n\nimport type {Loader, LoaderOptions} from '@loaders.gl/loader-utils';\nimport {isPureObject, isObject} from '../../javascript-utils/is-type';\nimport {probeLog, NullLog} from './loggers';\nimport {DEFAULT_LOADER_OPTIONS, REMOVED_LOADER_OPTIONS} from './option-defaults';\n\n/**\n * Global state for loaders.gl. Stored on `global.loaders._state`\n */\ntype GlobalLoaderState = {\n  loaderRegistry: Loader[];\n  globalOptions: LoaderOptions;\n};\n\n/**\n * Helper for safely accessing global loaders.gl variables\n * Wraps initialization of global variable in function to defeat overly aggressive tree-shakers\n */\nexport function getGlobalLoaderState(): GlobalLoaderState {\n  // @ts-ignore\n  globalThis.loaders = globalThis.loaders || {};\n  // @ts-ignore\n  const {loaders} = globalThis;\n\n  // Add _state object to keep separate from modules added to globalThis.loaders\n  loaders._state = loaders._state || {};\n  return loaders._state;\n}\n\n/**\n * Store global loader options on the global object to increase chances of cross loaders-version interoperability\n * NOTE: This use case is not reliable but can help when testing new versions of loaders.gl with existing frameworks\n * @returns global loader options merged with default loader options\n */\nexport const getGlobalLoaderOptions = (): LoaderOptions => {\n  const state = getGlobalLoaderState();\n  // Ensure all default loader options from this library are mentioned\n  state.globalOptions = state.globalOptions || {...DEFAULT_LOADER_OPTIONS};\n  return state.globalOptions;\n};\n\n/**\n * Set global loader options\n * @param options\n */\nexport function setGlobalOptions(options: LoaderOptions): void {\n  const state = getGlobalLoaderState();\n  const globalOptions = getGlobalLoaderOptions();\n  state.globalOptions = normalizeOptionsInternal(globalOptions, options);\n}\n\n/**\n * Merges options with global opts and loader defaults, also injects baseUri\n * @param options\n * @param loader\n * @param loaders\n * @param url\n */\nexport function normalizeOptions(\n  options: LoaderOptions,\n  loader: Loader,\n  loaders?: Loader[],\n  url?: string\n): LoaderOptions {\n  loaders = loaders || [];\n  loaders = Array.isArray(loaders) ? loaders : [loaders];\n\n  validateOptions(options, loaders);\n  return normalizeOptionsInternal(loader, options, url);\n}\n\n// VALIDATE OPTIONS\n\n/**\n * Warn for unsupported options\n * @param options\n * @param loaders\n */\nfunction validateOptions(options: LoaderOptions, loaders: Loader[]) {\n  // Check top level options\n  validateOptionsObject(options, null, DEFAULT_LOADER_OPTIONS, REMOVED_LOADER_OPTIONS, loaders);\n  for (const loader of loaders) {\n    // Get the scoped, loader specific options from the user supplied options\n    const idOptions = (options && options[loader.id]) || {};\n\n    // Get scoped, loader specific default and deprecated options from the selected loader\n    const loaderOptions = (loader.options && loader.options[loader.id]) || {};\n    const deprecatedOptions =\n      (loader.deprecatedOptions && loader.deprecatedOptions[loader.id]) || {};\n\n    // Validate loader specific options\n    validateOptionsObject(idOptions, loader.id, loaderOptions, deprecatedOptions, loaders);\n  }\n}\n\n// eslint-disable-next-line max-params, complexity\nfunction validateOptionsObject(\n  options,\n  id: string | null,\n  defaultOptions,\n  deprecatedOptions,\n  loaders: Loader[]\n) {\n  const loaderName = id || 'Top level';\n  const prefix = id ? `${id}.` : '';\n\n  for (const key in options) {\n    // If top level option value is an object it could options for a loader, so ignore\n    const isSubOptions = !id && isObject(options[key]);\n    const isBaseUriOption = key === 'baseUri' && !id;\n    const isWorkerUrlOption = key === 'workerUrl' && id;\n    // <loader>.workerUrl requires special handling as it is now auto-generated and no longer specified as a default option.\n    if (!(key in defaultOptions) && !isBaseUriOption && !isWorkerUrlOption) {\n      // Issue deprecation warnings\n      if (key in deprecatedOptions) {\n        probeLog.warn(\n          `${loaderName} loader option \\'${prefix}${key}\\' no longer supported, use \\'${deprecatedOptions[key]}\\'`\n        )();\n      } else if (!isSubOptions) {\n        const suggestion = findSimilarOption(key, loaders);\n        probeLog.warn(\n          `${loaderName} loader option \\'${prefix}${key}\\' not recognized. ${suggestion}`\n        )();\n      }\n    }\n  }\n}\n\nfunction findSimilarOption(optionKey, loaders) {\n  const lowerCaseOptionKey = optionKey.toLowerCase();\n  let bestSuggestion = '';\n  for (const loader of loaders) {\n    for (const key in loader.options) {\n      if (optionKey === key) {\n        return `Did you mean \\'${loader.id}.${key}\\'?`;\n      }\n      const lowerCaseKey = key.toLowerCase();\n      const isPartialMatch =\n        lowerCaseOptionKey.startsWith(lowerCaseKey) || lowerCaseKey.startsWith(lowerCaseOptionKey);\n      if (isPartialMatch) {\n        bestSuggestion = bestSuggestion || `Did you mean \\'${loader.id}.${key}\\'?`;\n      }\n    }\n  }\n  return bestSuggestion;\n}\n\nfunction normalizeOptionsInternal(loader, options, url?: string) {\n  const loaderDefaultOptions = loader.options || {};\n\n  const mergedOptions = {...loaderDefaultOptions};\n\n  addUrlOptions(mergedOptions, url);\n\n  // LOGGING: options.log can be set to `null` to defeat logging\n  if (mergedOptions.log === null) {\n    mergedOptions.log = new NullLog();\n  }\n\n  mergeNestedFields(mergedOptions, getGlobalLoaderOptions());\n  mergeNestedFields(mergedOptions, options);\n\n  return mergedOptions;\n}\n\n// Merge nested options objects\nfunction mergeNestedFields(mergedOptions, options) {\n  for (const key in options) {\n    // Check for nested options\n    // object in options => either no key in defaultOptions or object in defaultOptions\n    if (key in options) {\n      const value = options[key];\n      if (isPureObject(value) && isPureObject(mergedOptions[key])) {\n        mergedOptions[key] = {\n          ...mergedOptions[key],\n          ...options[key]\n        };\n      } else {\n        mergedOptions[key] = options[key];\n      }\n    }\n    // else: No need to merge nested opts, and the initial merge already copied over the nested options\n  }\n}\n\n/**\n * Harvest information from the url\n * @deprecated This is mainly there to support a hack in the GLTFLoader\n * TODO - baseUri should be a directory, i.e. remove file component from baseUri\n * TODO - extract extension?\n * TODO - extract query parameters?\n * TODO - should these be injected on context instead of options?\n */\nfunction addUrlOptions(options, url?: string) {\n  if (url && !('baseUri' in options)) {\n    options.baseUri = url;\n  }\n}\n"], "mappings": "AAGA,SAAQA,YAAY,EAAEC,QAAQ,QAAO,gCAAgC;AACrE,SAAQC,QAAQ,EAAEC,OAAO,QAAO,WAAW;AAC3C,SAAQC,sBAAsB,EAAEC,sBAAsB,QAAO,mBAAmB;AAchF,OAAO,SAASC,oBAAoBA,CAAA,EAAsB;EAExDC,UAAU,CAACC,OAAO,GAAGD,UAAU,CAACC,OAAO,IAAI,CAAC,CAAC;EAE7C,MAAM;IAACA;EAAO,CAAC,GAAGD,UAAU;EAG5BC,OAAO,CAACC,MAAM,GAAGD,OAAO,CAACC,MAAM,IAAI,CAAC,CAAC;EACrC,OAAOD,OAAO,CAACC,MAAM;AACvB;AAOA,OAAO,MAAMC,sBAAsB,GAAGA,CAAA,KAAqB;EACzD,MAAMC,KAAK,GAAGL,oBAAoB,CAAC,CAAC;EAEpCK,KAAK,CAACC,aAAa,GAAGD,KAAK,CAACC,aAAa,IAAI;IAAC,GAAGR;EAAsB,CAAC;EACxE,OAAOO,KAAK,CAACC,aAAa;AAC5B,CAAC;AAMD,OAAO,SAASC,gBAAgBA,CAACC,OAAsB,EAAQ;EAC7D,MAAMH,KAAK,GAAGL,oBAAoB,CAAC,CAAC;EACpC,MAAMM,aAAa,GAAGF,sBAAsB,CAAC,CAAC;EAC9CC,KAAK,CAACC,aAAa,GAAGG,wBAAwB,CAACH,aAAa,EAAEE,OAAO,CAAC;AACxE;AASA,OAAO,SAASE,gBAAgBA,CAC9BF,OAAsB,EACtBG,MAAc,EACdT,OAAkB,EAClBU,GAAY,EACG;EACfV,OAAO,GAAGA,OAAO,IAAI,EAAE;EACvBA,OAAO,GAAGW,KAAK,CAACC,OAAO,CAACZ,OAAO,CAAC,GAAGA,OAAO,GAAG,CAACA,OAAO,CAAC;EAEtDa,eAAe,CAACP,OAAO,EAAEN,OAAO,CAAC;EACjC,OAAOO,wBAAwB,CAACE,MAAM,EAAEH,OAAO,EAAEI,GAAG,CAAC;AACvD;AASA,SAASG,eAAeA,CAACP,OAAsB,EAAEN,OAAiB,EAAE;EAElEc,qBAAqB,CAACR,OAAO,EAAE,IAAI,EAAEV,sBAAsB,EAAEC,sBAAsB,EAAEG,OAAO,CAAC;EAC7F,KAAK,MAAMS,MAAM,IAAIT,OAAO,EAAE;IAE5B,MAAMe,SAAS,GAAIT,OAAO,IAAIA,OAAO,CAACG,MAAM,CAACO,EAAE,CAAC,IAAK,CAAC,CAAC;IAGvD,MAAMC,aAAa,GAAIR,MAAM,CAACH,OAAO,IAAIG,MAAM,CAACH,OAAO,CAACG,MAAM,CAACO,EAAE,CAAC,IAAK,CAAC,CAAC;IACzE,MAAME,iBAAiB,GACpBT,MAAM,CAACS,iBAAiB,IAAIT,MAAM,CAACS,iBAAiB,CAACT,MAAM,CAACO,EAAE,CAAC,IAAK,CAAC,CAAC;IAGzEF,qBAAqB,CAACC,SAAS,EAAEN,MAAM,CAACO,EAAE,EAAEC,aAAa,EAAEC,iBAAiB,EAAElB,OAAO,CAAC;EACxF;AACF;AAGA,SAASc,qBAAqBA,CAC5BR,OAAO,EACPU,EAAiB,EACjBG,cAAc,EACdD,iBAAiB,EACjBlB,OAAiB,EACjB;EACA,MAAMoB,UAAU,GAAGJ,EAAE,IAAI,WAAW;EACpC,MAAMK,MAAM,GAAGL,EAAE,MAAAM,MAAA,CAAMN,EAAE,SAAM,EAAE;EAEjC,KAAK,MAAMO,GAAG,IAAIjB,OAAO,EAAE;IAEzB,MAAMkB,YAAY,GAAG,CAACR,EAAE,IAAIvB,QAAQ,CAACa,OAAO,CAACiB,GAAG,CAAC,CAAC;IAClD,MAAME,eAAe,GAAGF,GAAG,KAAK,SAAS,IAAI,CAACP,EAAE;IAChD,MAAMU,iBAAiB,GAAGH,GAAG,KAAK,WAAW,IAAIP,EAAE;IAEnD,IAAI,EAAEO,GAAG,IAAIJ,cAAc,CAAC,IAAI,CAACM,eAAe,IAAI,CAACC,iBAAiB,EAAE;MAEtE,IAAIH,GAAG,IAAIL,iBAAiB,EAAE;QAC5BxB,QAAQ,CAACiC,IAAI,IAAAL,MAAA,CACRF,UAAU,sBAAAE,MAAA,CAAoBD,MAAM,EAAAC,MAAA,CAAGC,GAAG,kCAAAD,MAAA,CAAiCJ,iBAAiB,CAACK,GAAG,CAAC,MACtG,CAAC,CAAC,CAAC;MACL,CAAC,MAAM,IAAI,CAACC,YAAY,EAAE;QACxB,MAAMI,UAAU,GAAGC,iBAAiB,CAACN,GAAG,EAAEvB,OAAO,CAAC;QAClDN,QAAQ,CAACiC,IAAI,IAAAL,MAAA,CACRF,UAAU,sBAAAE,MAAA,CAAoBD,MAAM,EAAAC,MAAA,CAAGC,GAAG,wBAAAD,MAAA,CAAsBM,UAAU,CAC/E,CAAC,CAAC,CAAC;MACL;IACF;EACF;AACF;AAEA,SAASC,iBAAiBA,CAACC,SAAS,EAAE9B,OAAO,EAAE;EAC7C,MAAM+B,kBAAkB,GAAGD,SAAS,CAACE,WAAW,CAAC,CAAC;EAClD,IAAIC,cAAc,GAAG,EAAE;EACvB,KAAK,MAAMxB,MAAM,IAAIT,OAAO,EAAE;IAC5B,KAAK,MAAMuB,GAAG,IAAId,MAAM,CAACH,OAAO,EAAE;MAChC,IAAIwB,SAAS,KAAKP,GAAG,EAAE;QACrB,wBAAAD,MAAA,CAAyBb,MAAM,CAACO,EAAE,OAAAM,MAAA,CAAIC,GAAG;MAC3C;MACA,MAAMW,YAAY,GAAGX,GAAG,CAACS,WAAW,CAAC,CAAC;MACtC,MAAMG,cAAc,GAClBJ,kBAAkB,CAACK,UAAU,CAACF,YAAY,CAAC,IAAIA,YAAY,CAACE,UAAU,CAACL,kBAAkB,CAAC;MAC5F,IAAII,cAAc,EAAE;QAClBF,cAAc,GAAGA,cAAc,qBAAAX,MAAA,CAAsBb,MAAM,CAACO,EAAE,OAAAM,MAAA,CAAIC,GAAG,OAAK;MAC5E;IACF;EACF;EACA,OAAOU,cAAc;AACvB;AAEA,SAAS1B,wBAAwBA,CAACE,MAAM,EAAEH,OAAO,EAAEI,GAAY,EAAE;EAC/D,MAAM2B,oBAAoB,GAAG5B,MAAM,CAACH,OAAO,IAAI,CAAC,CAAC;EAEjD,MAAMgC,aAAa,GAAG;IAAC,GAAGD;EAAoB,CAAC;EAE/CE,aAAa,CAACD,aAAa,EAAE5B,GAAG,CAAC;EAGjC,IAAI4B,aAAa,CAACE,GAAG,KAAK,IAAI,EAAE;IAC9BF,aAAa,CAACE,GAAG,GAAG,IAAI7C,OAAO,CAAC,CAAC;EACnC;EAEA8C,iBAAiB,CAACH,aAAa,EAAEpC,sBAAsB,CAAC,CAAC,CAAC;EAC1DuC,iBAAiB,CAACH,aAAa,EAAEhC,OAAO,CAAC;EAEzC,OAAOgC,aAAa;AACtB;AAGA,SAASG,iBAAiBA,CAACH,aAAa,EAAEhC,OAAO,EAAE;EACjD,KAAK,MAAMiB,GAAG,IAAIjB,OAAO,EAAE;IAGzB,IAAIiB,GAAG,IAAIjB,OAAO,EAAE;MAClB,MAAMoC,KAAK,GAAGpC,OAAO,CAACiB,GAAG,CAAC;MAC1B,IAAI/B,YAAY,CAACkD,KAAK,CAAC,IAAIlD,YAAY,CAAC8C,aAAa,CAACf,GAAG,CAAC,CAAC,EAAE;QAC3De,aAAa,CAACf,GAAG,CAAC,GAAG;UACnB,GAAGe,aAAa,CAACf,GAAG,CAAC;UACrB,GAAGjB,OAAO,CAACiB,GAAG;QAChB,CAAC;MACH,CAAC,MAAM;QACLe,aAAa,CAACf,GAAG,CAAC,GAAGjB,OAAO,CAACiB,GAAG,CAAC;MACnC;IACF;EAEF;AACF;AAUA,SAASgB,aAAaA,CAACjC,OAAO,EAAEI,GAAY,EAAE;EAC5C,IAAIA,GAAG,IAAI,EAAE,SAAS,IAAIJ,OAAO,CAAC,EAAE;IAClCA,OAAO,CAACqC,OAAO,GAAGjC,GAAG;EACvB;AACF"}