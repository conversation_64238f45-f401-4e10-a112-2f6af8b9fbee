{"version": 3, "file": "option-defaults.js", "names": ["<PERSON><PERSON><PERSON><PERSON>", "ConsoleLog", "DEFAULT_LOADER_OPTIONS", "fetch", "mimeType", "undefined", "nothrow", "log", "CDN", "worker", "maxConcurrency", "maxMobileConcurrency", "reuseWorkers", "_nodeWorkers", "_workerType", "limit", "_limitMB", "batchSize", "batchDebounceMs", "metadata", "transforms", "REMOVED_LOADER_OPTIONS", "throws", "dataType", "uri", "method", "headers", "body", "mode", "credentials", "cache", "redirect", "referrer", "referrerPolicy", "integrity", "keepalive", "signal"], "sources": ["../../../../src/lib/loader-utils/option-defaults.ts"], "sourcesContent": ["import type {LoaderOptions} from '@loaders.gl/loader-utils';\nimport {isBrowser} from '@loaders.gl/loader-utils';\nimport {ConsoleLog} from './loggers';\n\nexport const DEFAULT_LOADER_OPTIONS: LoaderOptions = {\n  // baseUri\n  fetch: null,\n  mimeType: undefined,\n  nothrow: false,\n  log: new ConsoleLog(), // A probe.gl compatible (`log.log()()` syntax) that just logs to console\n\n  CDN: 'https://unpkg.com/@loaders.gl',\n  worker: true, // By default, use worker if provided by loader.\n  maxConcurrency: 3, // How many worker instances should be created for each loader.\n  maxMobileConcurrency: 1, // How many worker instances should be created for each loader on mobile devices.\n  reuseWorkers: isBrowser, // By default reuse workers in browser (Node.js refuses to terminate if browsers are running)\n  _nodeWorkers: false, // By default do not support node workers\n  _workerType: '', // 'test' to use locally generated workers\n\n  limit: 0,\n  _limitMB: 0,\n  batchSize: 'auto',\n  batchDebounceMs: 0,\n  metadata: false, // TODO - currently only implemented for parseInBatches, adds initial metadata batch,\n  transforms: []\n};\n\nexport const REMOVED_LOADER_OPTIONS = {\n  throws: 'nothrow',\n  dataType: '(no longer used)',\n  uri: 'baseUri',\n  // Warn if fetch options are used on top-level\n  method: 'fetch.method',\n  headers: 'fetch.headers',\n  body: 'fetch.body',\n  mode: 'fetch.mode',\n  credentials: 'fetch.credentials',\n  cache: 'fetch.cache',\n  redirect: 'fetch.redirect',\n  referrer: 'fetch.referrer',\n  referrerPolicy: 'fetch.referrerPolicy',\n  integrity: 'fetch.integrity',\n  keepalive: 'fetch.keepalive',\n  signal: 'fetch.signal'\n};\n"], "mappings": "AACA,SAAQA,SAAS,QAAO,0BAA0B;AAClD,SAAQC,UAAU,QAAO,WAAW;AAEpC,OAAO,MAAMC,sBAAqC,GAAG;EAEnDC,KAAK,EAAE,IAAI;EACXC,QAAQ,EAAEC,SAAS;EACnBC,OAAO,EAAE,KAAK;EACdC,GAAG,EAAE,IAAIN,UAAU,CAAC,CAAC;EAErBO,GAAG,EAAE,+BAA+B;EACpCC,MAAM,EAAE,IAAI;EACZC,cAAc,EAAE,CAAC;EACjBC,oBAAoB,EAAE,CAAC;EACvBC,YAAY,EAAEZ,SAAS;EACvBa,YAAY,EAAE,KAAK;EACnBC,WAAW,EAAE,EAAE;EAEfC,KAAK,EAAE,CAAC;EACRC,QAAQ,EAAE,CAAC;EACXC,SAAS,EAAE,MAAM;EACjBC,eAAe,EAAE,CAAC;EAClBC,QAAQ,EAAE,KAAK;EACfC,UAAU,EAAE;AACd,CAAC;AAED,OAAO,MAAMC,sBAAsB,GAAG;EACpCC,MAAM,EAAE,SAAS;EACjBC,QAAQ,EAAE,kBAAkB;EAC5BC,GAAG,EAAE,SAAS;EAEdC,MAAM,EAAE,cAAc;EACtBC,OAAO,EAAE,eAAe;EACxBC,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,YAAY;EAClBC,WAAW,EAAE,mBAAmB;EAChCC,KAAK,EAAE,aAAa;EACpBC,QAAQ,EAAE,gBAAgB;EAC1BC,QAAQ,EAAE,gBAAgB;EAC1BC,cAAc,EAAE,sBAAsB;EACtCC,SAAS,EAAE,iBAAiB;EAC5BC,SAAS,EAAE,iBAAiB;EAC5BC,MAAM,EAAE;AACV,CAAC"}