{"version": 3, "file": "parse-sync.js", "names": ["assert", "selectLoaderSync", "isLoaderObject", "normalizeOptions", "getArrayBufferOrStringFromDataSync", "getLoaderContext", "getLoadersFromContext", "getResourceUrl", "parseSync", "data", "loaders", "options", "context", "Array", "isArray", "undefined", "typed<PERSON>oa<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "loader", "url", "parse", "Error", "parseWithLoaderSync", "parseTextSync", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "concat", "name"], "sources": ["../../../../src/lib/api/parse-sync.ts"], "sourcesContent": ["import type {\n  SyncD<PERSON>Type,\n  Loader,\n  LoaderWithParser,\n  LoaderContext,\n  LoaderOptions\n} from '@loaders.gl/loader-utils';\nimport {assert} from '@loaders.gl/loader-utils';\nimport {selectLoaderSync} from './select-loader';\nimport {isLoaderObject} from '../loader-utils/normalize-loader';\nimport {normalizeOptions} from '../loader-utils/option-utils';\nimport {getArrayBufferOrStringFromDataSync} from '../loader-utils/get-data';\nimport {getLoaderContext, getLoadersFromContext} from '../loader-utils/loader-context';\nimport {getResourceUrl} from '../utils/resource-utils';\n\n/**\n * Parses `data` synchronously using a specified loader\n * @param data\n * @param loaders\n * @param options\n * @param context\n */\nexport function parseSync(\n  data: SyncDataType,\n  loaders?: Loader | Loader[] | LoaderOptions,\n  options?: LoaderOptions,\n  context?: LoaderContext\n): any {\n  assert(!context || typeof context === 'object'); // parseSync no longer accepts final url\n\n  // Signature: parseSync(data, options)\n  // Uses registered loaders\n  if (!Array.isArray(loaders) && !isLoaderObject(loaders)) {\n    context = undefined; // context not supported in short signature\n    options = loaders as LoaderOptions;\n    loaders = undefined;\n  }\n\n  options = options || {};\n\n  // Chooses a loader (and normalizes it)\n  // Also use any loaders in the context, new loaders take priority\n  const typedLoaders = loaders as Loader | Loader[] | undefined;\n  const candidateLoaders = getLoadersFromContext(typedLoaders, context);\n  const loader = selectLoaderSync(data, candidateLoaders, options);\n  // Note: if nothrow option was set, it is possible that no loader was found, if so just return null\n  if (!loader) {\n    return null;\n  }\n\n  // Normalize options\n  options = normalizeOptions(options, loader, candidateLoaders);\n\n  // Extract a url for auto detection\n  const url = getResourceUrl(data);\n\n  const parse = () => {\n    throw new Error('parseSync called parse (which is async');\n  };\n  context = getLoaderContext(\n    {url, parseSync, parse, loaders: loaders as Loader[]},\n    options,\n    context || null\n  );\n\n  return parseWithLoaderSync(loader as LoaderWithParser, data, options, context);\n}\n\n// TODO - should accept loader.parseSync/parse and generate 1 chunk asyncIterator\nfunction parseWithLoaderSync(\n  loader: LoaderWithParser,\n  data: SyncDataType,\n  options: LoaderOptions,\n  context: LoaderContext\n) {\n  data = getArrayBufferOrStringFromDataSync(data, loader, options);\n\n  if (loader.parseTextSync && typeof data === 'string') {\n    return loader.parseTextSync(data, options); // , context, loader);\n  }\n\n  if (loader.parseSync && data instanceof ArrayBuffer) {\n    return loader.parseSync(data, options, context); // , loader);\n  }\n\n  // TBD - If synchronous parser not available, return null\n  throw new Error(\n    `${loader.name} loader: 'parseSync' not supported by this loader, use 'parse' instead. ${\n      context.url || ''\n    }`\n  );\n}\n"], "mappings": "AAOA,SAAQA,MAAM,QAAO,0BAA0B;AAC/C,SAAQC,gBAAgB,QAAO,iBAAiB;AAChD,SAAQC,cAAc,QAAO,kCAAkC;AAC/D,SAAQC,gBAAgB,QAAO,8BAA8B;AAC7D,SAAQC,kCAAkC,QAAO,0BAA0B;AAC3E,SAAQC,gBAAgB,EAAEC,qBAAqB,QAAO,gCAAgC;AACtF,SAAQC,cAAc,QAAO,yBAAyB;AAStD,OAAO,SAASC,SAASA,CACvBC,IAAkB,EAClBC,OAA2C,EAC3CC,OAAuB,EACvBC,OAAuB,EAClB;EACLZ,MAAM,CAAC,CAACY,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,CAAC;EAI/C,IAAI,CAACC,KAAK,CAACC,OAAO,CAACJ,OAAO,CAAC,IAAI,CAACR,cAAc,CAACQ,OAAO,CAAC,EAAE;IACvDE,OAAO,GAAGG,SAAS;IACnBJ,OAAO,GAAGD,OAAwB;IAClCA,OAAO,GAAGK,SAAS;EACrB;EAEAJ,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAIvB,MAAMK,YAAY,GAAGN,OAAwC;EAC7D,MAAMO,gBAAgB,GAAGX,qBAAqB,CAACU,YAAY,EAAEJ,OAAO,CAAC;EACrE,MAAMM,MAAM,GAAGjB,gBAAgB,CAACQ,IAAI,EAAEQ,gBAAgB,EAAEN,OAAO,CAAC;EAEhE,IAAI,CAACO,MAAM,EAAE;IACX,OAAO,IAAI;EACb;EAGAP,OAAO,GAAGR,gBAAgB,CAACQ,OAAO,EAAEO,MAAM,EAAED,gBAAgB,CAAC;EAG7D,MAAME,GAAG,GAAGZ,cAAc,CAACE,IAAI,CAAC;EAEhC,MAAMW,KAAK,GAAGA,CAAA,KAAM;IAClB,MAAM,IAAIC,KAAK,CAAC,wCAAwC,CAAC;EAC3D,CAAC;EACDT,OAAO,GAAGP,gBAAgB,CACxB;IAACc,GAAG;IAAEX,SAAS;IAAEY,KAAK;IAAEV,OAAO,EAAEA;EAAmB,CAAC,EACrDC,OAAO,EACPC,OAAO,IAAI,IACb,CAAC;EAED,OAAOU,mBAAmB,CAACJ,MAAM,EAAsBT,IAAI,EAAEE,OAAO,EAAEC,OAAO,CAAC;AAChF;AAGA,SAASU,mBAAmBA,CAC1BJ,MAAwB,EACxBT,IAAkB,EAClBE,OAAsB,EACtBC,OAAsB,EACtB;EACAH,IAAI,GAAGL,kCAAkC,CAACK,IAAI,EAAES,MAAM,EAAEP,OAAO,CAAC;EAEhE,IAAIO,MAAM,CAACK,aAAa,IAAI,OAAOd,IAAI,KAAK,QAAQ,EAAE;IACpD,OAAOS,MAAM,CAACK,aAAa,CAACd,IAAI,EAAEE,OAAO,CAAC;EAC5C;EAEA,IAAIO,MAAM,CAACV,SAAS,IAAIC,IAAI,YAAYe,WAAW,EAAE;IACnD,OAAON,MAAM,CAACV,SAAS,CAACC,IAAI,EAAEE,OAAO,EAAEC,OAAO,CAAC;EACjD;EAGA,MAAM,IAAIS,KAAK,IAAAI,MAAA,CACVP,MAAM,CAACQ,IAAI,8EAAAD,MAAA,CACZb,OAAO,CAACO,GAAG,IAAI,EAAE,CAErB,CAAC;AACH"}