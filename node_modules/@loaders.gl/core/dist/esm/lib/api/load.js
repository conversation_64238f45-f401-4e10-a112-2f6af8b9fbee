import { isBlob } from '../../javascript-utils/is-type';
import { isLoaderObject } from '../loader-utils/normalize-loader';
import { getFetchFunction } from '../loader-utils/get-fetch-function';
import { parse } from './parse';
export async function load(url, loaders, options, context) {
  if (!Array.isArray(loaders) && !isLoaderObject(loaders)) {
    context = undefined;
    options = loaders;
    loaders = undefined;
  }
  const fetch = getFetchFunction(options);
  let data = url;
  if (typeof url === 'string') {
    data = await fetch(url);
  }
  if (isBlob(url)) {
    data = await fetch(url);
  }
  return await parse(data, loaders, options);
}
//# sourceMappingURL=load.js.map