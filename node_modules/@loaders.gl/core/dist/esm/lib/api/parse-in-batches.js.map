{"version": 3, "file": "parse-in-batches.js", "names": ["assert", "concatenateArrayBuffersAsync", "isLoaderObject", "normalizeOptions", "getLoaderContext", "getAsyncIterableFromData", "getResourceUrl", "<PERSON><PERSON><PERSON><PERSON>", "parse", "parseInBatches", "data", "loaders", "options", "context", "loaderArray", "Array", "isArray", "undefined", "url", "loader", "parseWithLoaderInBatches", "outputIterator", "parseToOutputIterator", "metadata", "metadataBatch", "batchType", "_loader", "_context", "bytesUsed", "makeMetadataBatchIterator", "iterator", "inputIterator", "transformedIterator", "applyInputTransforms", "transforms", "parseChunkInBatches", "arrayBuffer", "parsedData", "mimeType", "mimeTypes", "batch", "shape", "length", "arguments", "iterator<PERSON><PERSON><PERSON>", "transformBatches"], "sources": ["../../../../src/lib/api/parse-in-batches.ts"], "sourcesContent": ["import type {Batch} from '@loaders.gl/schema';\nimport type {\n  BatchableDataType,\n  Loader,\n  LoaderWithParser,\n  LoaderContext,\n  LoaderOptions\n} from '@loaders.gl/loader-utils';\nimport {assert, concatenateArrayBuffersAsync} from '@loaders.gl/loader-utils';\nimport {isLoaderObject} from '../loader-utils/normalize-loader';\nimport {normalizeOptions} from '../loader-utils/option-utils';\nimport {getLoaderContext} from '../loader-utils/loader-context';\nimport {getAsyncIterableFromData} from '../loader-utils/get-data';\nimport {getResourceUrl} from '../utils/resource-utils';\nimport {selectLoader} from './select-loader';\n\n// Ensure `parse` is available in context if loader falls back to `parse`\nimport {parse} from './parse';\n\n/**\n * Parses `data` using a specified loader\n * @param data\n * @param loaders\n * @param options\n * @param context\n */\nexport async function parseInBatches(\n  data: BatchableDataType,\n  loaders?: Loader | Loader[] | LoaderOptions,\n  options?: LoaderOptions,\n  context?: LoaderContext\n): Promise<AsyncIterable<any>> {\n  assert(!context || typeof context === 'object'); // parseInBatches no longer accepts final url\n\n  const loaderArray = Array.isArray(loaders) ? loaders : undefined;\n\n  // Signature: parseInBatches(data, options, url) - Uses registered loaders\n  if (!Array.isArray(loaders) && !isLoaderObject(loaders)) {\n    context = undefined; // context not supported in short signature\n    options = loaders as LoaderOptions;\n    loaders = undefined;\n  }\n\n  data = await data; // Resolve any promise\n  options = options || {};\n\n  // Extract a url for auto detection\n  const url = getResourceUrl(data);\n\n  // Chooses a loader and normalizes it\n  // Note - only uses URL and contentType for streams and iterator inputs\n  const loader = await selectLoader(data as ArrayBuffer, loaders as Loader | Loader[], options);\n  // Note: if options.nothrow was set, it is possible that no loader was found, if so just return null\n  if (!loader) {\n    // @ts-ignore\n    return null;\n  }\n\n  // Normalize options\n  options = normalizeOptions(options, loader, loaderArray, url);\n  context = getLoaderContext(\n    {url, parseInBatches, parse, loaders: loaderArray},\n    options,\n    context || null\n  );\n\n  return await parseWithLoaderInBatches(loader as LoaderWithParser, data, options, context);\n}\n\n/**\n * Loader has been selected and context has been prepared, see if we need to emit a metadata batch\n */\nasync function parseWithLoaderInBatches(\n  loader: LoaderWithParser,\n  data: BatchableDataType,\n  options: LoaderOptions,\n  context: LoaderContext\n): Promise<AsyncIterable<any>> {\n  const outputIterator = await parseToOutputIterator(loader, data, options, context);\n\n  // Generate metadata batch if requested\n  if (!options.metadata) {\n    return outputIterator;\n  }\n\n  const metadataBatch = {\n    batchType: 'metadata',\n    metadata: {\n      _loader: loader,\n      _context: context\n    },\n    // Populate with some default fields to avoid crashing\n    data: [],\n    bytesUsed: 0\n  };\n\n  async function* makeMetadataBatchIterator(iterator) {\n    yield metadataBatch;\n    yield* iterator;\n  }\n\n  return makeMetadataBatchIterator(outputIterator);\n}\n\n/**\n * Prep work is done, now it is time to start parsing into an output operator\n * The approach depends on which parse function the loader exposes\n * `parseInBatches` (preferred), `parse` (fallback)\n */\nasync function parseToOutputIterator(\n  loader: LoaderWithParser,\n  data: BatchableDataType,\n  options: LoaderOptions,\n  context: LoaderContext\n): Promise<AsyncIterable<any>> {\n  // Get an iterator from the input\n  const inputIterator = await getAsyncIterableFromData(data, options);\n\n  // Apply any iterator transforms (options.transforms)\n  const transformedIterator = await applyInputTransforms(inputIterator, options?.transforms || []);\n\n  // If loader supports parseInBatches, we are done\n  if (loader.parseInBatches) {\n    return loader.parseInBatches(transformedIterator, options, context);\n  }\n\n  // Fallback: load atomically using `parse` concatenating input iterator into single chunk\n  async function* parseChunkInBatches() {\n    const arrayBuffer = await concatenateArrayBuffersAsync(transformedIterator);\n    // Call `parse` instead of `loader.parse` to ensure we can call workers etc.\n    const parsedData = await parse(\n      arrayBuffer,\n      loader,\n      // TODO - Hack: supply loaders MIME type to ensure we match it\n      {...options, mimeType: loader.mimeTypes[0]},\n      context\n    );\n    // yield a single batch, the output from loader.parse()\n    // TODO - run through batch builder to apply options etc...\n    const batch: Batch = {\n      mimeType: loader.mimeTypes[0],\n      shape: Array.isArray(parsedData) ? 'row-table' : 'unknown',\n      batchType: 'data',\n      data: parsedData,\n      length: Array.isArray(parsedData) ? parsedData.length : 1\n    };\n    yield batch;\n  }\n\n  return parseChunkInBatches();\n}\n\ntype TransformBatches = (\n  asyncIterator: AsyncIterable<ArrayBuffer> | Iterable<ArrayBuffer>\n) => AsyncIterable<ArrayBuffer>;\n\n/**\n * Create an iterator chain with any transform iterators (crypto, decompression)\n * @param inputIterator\n * @param options\n */\nasync function applyInputTransforms(\n  inputIterator: AsyncIterable<ArrayBuffer> | Iterable<ArrayBuffer>,\n  transforms: TransformBatches[] = []\n): Promise<AsyncIterable<ArrayBuffer> | Iterable<ArrayBuffer>> {\n  let iteratorChain = inputIterator;\n  for await (const transformBatches of transforms) {\n    iteratorChain = transformBatches(iteratorChain);\n  }\n  return iteratorChain;\n}\n"], "mappings": "AAQA,SAAQA,MAAM,EAAEC,4BAA4B,QAAO,0BAA0B;AAC7E,SAAQC,cAAc,QAAO,kCAAkC;AAC/D,SAAQC,gBAAgB,QAAO,8BAA8B;AAC7D,SAAQC,gBAAgB,QAAO,gCAAgC;AAC/D,SAAQC,wBAAwB,QAAO,0BAA0B;AACjE,SAAQC,cAAc,QAAO,yBAAyB;AACtD,SAAQC,YAAY,QAAO,iBAAiB;AAG5C,SAAQC,KAAK,QAAO,SAAS;AAS7B,OAAO,eAAeC,cAAcA,CAClCC,IAAuB,EACvBC,OAA2C,EAC3CC,OAAuB,EACvBC,OAAuB,EACM;EAC7Bb,MAAM,CAAC,CAACa,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,CAAC;EAE/C,MAAMC,WAAW,GAAGC,KAAK,CAACC,OAAO,CAACL,OAAO,CAAC,GAAGA,OAAO,GAAGM,SAAS;EAGhE,IAAI,CAACF,KAAK,CAACC,OAAO,CAACL,OAAO,CAAC,IAAI,CAACT,cAAc,CAACS,OAAO,CAAC,EAAE;IACvDE,OAAO,GAAGI,SAAS;IACnBL,OAAO,GAAGD,OAAwB;IAClCA,OAAO,GAAGM,SAAS;EACrB;EAEAP,IAAI,GAAG,MAAMA,IAAI;EACjBE,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAGvB,MAAMM,GAAG,GAAGZ,cAAc,CAACI,IAAI,CAAC;EAIhC,MAAMS,MAAM,GAAG,MAAMZ,YAAY,CAACG,IAAI,EAAiBC,OAAO,EAAuBC,OAAO,CAAC;EAE7F,IAAI,CAACO,MAAM,EAAE;IAEX,OAAO,IAAI;EACb;EAGAP,OAAO,GAAGT,gBAAgB,CAACS,OAAO,EAAEO,MAAM,EAAEL,WAAW,EAAEI,GAAG,CAAC;EAC7DL,OAAO,GAAGT,gBAAgB,CACxB;IAACc,GAAG;IAAET,cAAc;IAAED,KAAK;IAAEG,OAAO,EAAEG;EAAW,CAAC,EAClDF,OAAO,EACPC,OAAO,IAAI,IACb,CAAC;EAED,OAAO,MAAMO,wBAAwB,CAACD,MAAM,EAAsBT,IAAI,EAAEE,OAAO,EAAEC,OAAO,CAAC;AAC3F;AAKA,eAAeO,wBAAwBA,CACrCD,MAAwB,EACxBT,IAAuB,EACvBE,OAAsB,EACtBC,OAAsB,EACO;EAC7B,MAAMQ,cAAc,GAAG,MAAMC,qBAAqB,CAACH,MAAM,EAAET,IAAI,EAAEE,OAAO,EAAEC,OAAO,CAAC;EAGlF,IAAI,CAACD,OAAO,CAACW,QAAQ,EAAE;IACrB,OAAOF,cAAc;EACvB;EAEA,MAAMG,aAAa,GAAG;IACpBC,SAAS,EAAE,UAAU;IACrBF,QAAQ,EAAE;MACRG,OAAO,EAAEP,MAAM;MACfQ,QAAQ,EAAEd;IACZ,CAAC;IAEDH,IAAI,EAAE,EAAE;IACRkB,SAAS,EAAE;EACb,CAAC;EAED,gBAAgBC,yBAAyBA,CAACC,QAAQ,EAAE;IAClD,MAAMN,aAAa;IACnB,OAAOM,QAAQ;EACjB;EAEA,OAAOD,yBAAyB,CAACR,cAAc,CAAC;AAClD;AAOA,eAAeC,qBAAqBA,CAClCH,MAAwB,EACxBT,IAAuB,EACvBE,OAAsB,EACtBC,OAAsB,EACO;EAE7B,MAAMkB,aAAa,GAAG,MAAM1B,wBAAwB,CAACK,IAAI,EAAEE,OAAO,CAAC;EAGnE,MAAMoB,mBAAmB,GAAG,MAAMC,oBAAoB,CAACF,aAAa,EAAE,CAAAnB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsB,UAAU,KAAI,EAAE,CAAC;EAGhG,IAAIf,MAAM,CAACV,cAAc,EAAE;IACzB,OAAOU,MAAM,CAACV,cAAc,CAACuB,mBAAmB,EAAEpB,OAAO,EAAEC,OAAO,CAAC;EACrE;EAGA,gBAAgBsB,mBAAmBA,CAAA,EAAG;IACpC,MAAMC,WAAW,GAAG,MAAMnC,4BAA4B,CAAC+B,mBAAmB,CAAC;IAE3E,MAAMK,UAAU,GAAG,MAAM7B,KAAK,CAC5B4B,WAAW,EACXjB,MAAM,EAEN;MAAC,GAAGP,OAAO;MAAE0B,QAAQ,EAAEnB,MAAM,CAACoB,SAAS,CAAC,CAAC;IAAC,CAAC,EAC3C1B,OACF,CAAC;IAGD,MAAM2B,KAAY,GAAG;MACnBF,QAAQ,EAAEnB,MAAM,CAACoB,SAAS,CAAC,CAAC,CAAC;MAC7BE,KAAK,EAAE1B,KAAK,CAACC,OAAO,CAACqB,UAAU,CAAC,GAAG,WAAW,GAAG,SAAS;MAC1DZ,SAAS,EAAE,MAAM;MACjBf,IAAI,EAAE2B,UAAU;MAChBK,MAAM,EAAE3B,KAAK,CAACC,OAAO,CAACqB,UAAU,CAAC,GAAGA,UAAU,CAACK,MAAM,GAAG;IAC1D,CAAC;IACD,MAAMF,KAAK;EACb;EAEA,OAAOL,mBAAmB,CAAC,CAAC;AAC9B;AAWA,eAAeF,oBAAoBA,CACjCF,aAAiE,EAEJ;EAAA,IAD7DG,UAA8B,GAAAS,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAA1B,SAAA,GAAA0B,SAAA,MAAG,EAAE;EAEnC,IAAIC,aAAa,GAAGb,aAAa;EACjC,WAAW,MAAMc,gBAAgB,IAAIX,UAAU,EAAE;IAC/CU,aAAa,GAAGC,gBAAgB,CAACD,aAAa,CAAC;EACjD;EACA,OAAOA,aAAa;AACtB"}