{"version": 3, "file": "loader-options.js", "names": ["setGlobalOptions", "setLoaderOptions", "getGlobalLoaderOptions", "getLoaderOptions"], "sources": ["../../../../src/lib/api/loader-options.ts"], "sourcesContent": ["export {setGlobalOptions as setLoaderOptions} from '../loader-utils/option-utils';\nexport {getGlobalLoaderOptions as getLoaderOptions} from '../loader-utils/option-utils';\n"], "mappings": "AAAA,SAAQA,gBAAgB,IAAIC,gBAAgB,QAAO,8BAA8B;AACjF,SAAQC,sBAAsB,IAAIC,gBAAgB,QAAO,8BAA8B"}