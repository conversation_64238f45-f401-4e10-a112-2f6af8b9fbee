{"version": 3, "file": "save.js", "names": ["encode", "encodeSync", "writeFile", "writeFileSync", "save", "data", "url", "writer", "options", "encodedData", "saveSync"], "sources": ["../../../../src/lib/api/save.ts"], "sourcesContent": ["import type {Writer, WriterOptions} from '@loaders.gl/loader-utils';\nimport {encode, encodeSync} from './encode';\nimport {writeFile, writeFileSync} from '../fetch/write-file';\n\nexport async function save(data, url, writer: Writer, options: WriterOptions) {\n  const encodedData = await encode(data, writer, options);\n  return await writeFile(url, encodedData);\n}\n\nexport function saveSync(data, url, writer, options) {\n  const encodedData = encodeSync(data, writer, options);\n  return writeFileSync(url, encodedData);\n}\n"], "mappings": "AACA,SAAQA,MAAM,EAAEC,UAAU,QAAO,UAAU;AAC3C,SAAQC,SAAS,EAAEC,aAAa,QAAO,qBAAqB;AAE5D,OAAO,eAAeC,IAAIA,CAACC,IAAI,EAAEC,GAAG,EAAEC,MAAc,EAAEC,OAAsB,EAAE;EAC5E,MAAMC,WAAW,GAAG,MAAMT,MAAM,CAACK,IAAI,EAAEE,MAAM,EAAEC,OAAO,CAAC;EACvD,OAAO,MAAMN,SAAS,CAACI,GAAG,EAAEG,WAAW,CAAC;AAC1C;AAEA,OAAO,SAASC,QAAQA,CAACL,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACnD,MAAMC,WAAW,GAAGR,UAAU,CAACI,IAAI,EAAEE,MAAM,EAAEC,OAAO,CAAC;EACrD,OAAOL,aAAa,CAACG,GAAG,EAAEG,WAAW,CAAC;AACxC"}