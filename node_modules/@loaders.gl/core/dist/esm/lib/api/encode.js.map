{"version": 3, "file": "encode.js", "names": ["canEncodeWithWorker", "processOnWorker", "concatenateArrayBuffers", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "writeFile", "fetchFile", "getLoaderOptions", "encode", "data", "writer", "options", "globalOptions", "encodeSync", "encodeText", "TextEncoder", "encodeInBatches", "batches", "chunks", "batch", "push", "encodeURLtoURL", "tmpInputFilename", "getTemporaryFilename", "tmpOutputFilename", "outputFilename", "response", "arrayBuffer", "Error", "text", "TextDecoder", "decode", "dataIterator", "getIterator", "inputUrl", "outputUrl", "table", "start", "end", "length", "filename", "concat"], "sources": ["../../../../src/lib/api/encode.ts"], "sourcesContent": ["import {Writer, WriterOptions, canEncodeWithWorker} from '@loaders.gl/loader-utils';\nimport {processOnWorker} from '@loaders.gl/worker-utils';\nimport {concatenateArrayBuffers, resolvePath} from '@loaders.gl/loader-utils';\nimport {isBrowser} from '@loaders.gl/loader-utils';\nimport {writeFile} from '../fetch/write-file';\nimport {fetchFile} from '../fetch/fetch-file';\nimport {getLoaderOptions} from './loader-options';\n\n/**\n * Encode loaded data into a binary ArrayBuffer using the specified Writer.\n */\nexport async function encode(\n  data: any,\n  writer: Writer,\n  options?: WriterOptions\n): Promise<ArrayBuffer> {\n  const globalOptions = getLoaderOptions() as WriterOptions;\n  // const globalOptions: WriterOptions = {}; // getWriterOptions();\n  options = {...globalOptions, ...options};\n  if (canEncodeWithWorker(writer, options)) {\n    return await processOn<PERSON>orker(writer, data, options);\n  }\n\n  // TODO Merge default writer options with options argument like it is done in load module.\n  if (writer.encode) {\n    return await writer.encode(data, options);\n  }\n\n  if (writer.encodeSync) {\n    return writer.encodeSync(data, options);\n  }\n\n  if (writer.encodeText) {\n    return new TextEncoder().encode(await writer.encodeText(data, options));\n  }\n\n  if (writer.encodeInBatches) {\n    // Create an iterator representing the data\n    // TODO - Assumes this is a table\n    const batches = encodeInBatches(data, writer, options);\n\n    // Concatenate the output\n    const chunks: any[] = [];\n    for await (const batch of batches) {\n      chunks.push(batch);\n    }\n    // @ts-ignore\n    return concatenateArrayBuffers(...chunks);\n  }\n\n  if (!isBrowser && writer.encodeURLtoURL) {\n    // TODO - how to generate filenames with correct extensions?\n    const tmpInputFilename = getTemporaryFilename('input');\n    await writeFile(tmpInputFilename, data);\n\n    const tmpOutputFilename = getTemporaryFilename('output');\n\n    const outputFilename = await encodeURLtoURL(\n      tmpInputFilename,\n      tmpOutputFilename,\n      writer,\n      options\n    );\n\n    const response = await fetchFile(outputFilename);\n    return response.arrayBuffer();\n  }\n\n  throw new Error('Writer could not encode data');\n}\n\n/**\n * Encode loaded data into a binary ArrayBuffer using the specified Writer.\n */\nexport function encodeSync(data: any, writer: Writer, options?: WriterOptions): ArrayBuffer {\n  if (writer.encodeSync) {\n    return writer.encodeSync(data, options);\n  }\n  throw new Error('Writer could not synchronously encode data');\n}\n\n/**\n * Encode loaded data to text using the specified Writer\n * @note This is a convenience function not intended for production use on large input data.\n * It is not optimized for performance. Data maybe converted from text to binary and back.\n * @throws if the writer does not generate text output\n */\nexport async function encodeText(\n  data: any,\n  writer: Writer,\n  options?: WriterOptions\n): Promise<string> {\n  if (writer.text && writer.encodeText) {\n    return await writer.encodeText(data, options);\n  }\n\n  if (writer.text && (writer.encode || writer.encodeInBatches)) {\n    const arrayBuffer = await encode(data, writer, options);\n    return new TextDecoder().decode(arrayBuffer);\n  }\n\n  throw new Error('Writer could not encode data as text');\n}\n\n/**\n * Encode loaded data into a sequence (iterator) of binary ArrayBuffers using the specified Writer.\n */\nexport function encodeInBatches(\n  data: any,\n  writer: Writer,\n  options?: WriterOptions\n): AsyncIterable<ArrayBuffer> {\n  if (writer.encodeInBatches) {\n    const dataIterator = getIterator(data);\n    return writer.encodeInBatches(dataIterator, options);\n  }\n  // TODO -fall back to atomic encode?\n  throw new Error('Writer could not encode data in batches');\n}\n\n/**\n * Encode data stored in a file (on disk) to another file.\n * @note Node.js only. This function enables using command-line converters as \"writers\".\n */\nexport async function encodeURLtoURL(\n  inputUrl,\n  outputUrl,\n  writer: Writer,\n  options\n): Promise<string> {\n  inputUrl = resolvePath(inputUrl);\n  outputUrl = resolvePath(outputUrl);\n  if (isBrowser || !writer.encodeURLtoURL) {\n    throw new Error();\n  }\n  const outputFilename = await writer.encodeURLtoURL(inputUrl, outputUrl, options);\n  return outputFilename;\n}\n\n/**\n * @todo TODO - this is an unacceptable hack!!!\n */\nfunction getIterator(data) {\n  const dataIterator = [{table: data, start: 0, end: data.length}];\n  return dataIterator;\n}\n\n/**\n * @todo Move to utils\n */\nfunction getTemporaryFilename(filename: string): string {\n  return `/tmp/${filename}`;\n}\n"], "mappings": "AAAA,SAA+BA,mBAAmB,QAAO,0BAA0B;AACnF,SAAQC,eAAe,QAAO,0BAA0B;AACxD,SAAQC,uBAAuB,EAAEC,WAAW,QAAO,0BAA0B;AAC7E,SAAQC,SAAS,QAAO,0BAA0B;AAClD,SAAQC,SAAS,QAAO,qBAAqB;AAC7C,SAAQC,SAAS,QAAO,qBAAqB;AAC7C,SAAQC,gBAAgB,QAAO,kBAAkB;AAKjD,OAAO,eAAeC,MAAMA,CAC1BC,IAAS,EACTC,MAAc,EACdC,OAAuB,EACD;EACtB,MAAMC,aAAa,GAAGL,gBAAgB,CAAC,CAAkB;EAEzDI,OAAO,GAAG;IAAC,GAAGC,aAAa;IAAE,GAAGD;EAAO,CAAC;EACxC,IAAIX,mBAAmB,CAACU,MAAM,EAAEC,OAAO,CAAC,EAAE;IACxC,OAAO,MAAMV,eAAe,CAACS,MAAM,EAAED,IAAI,EAAEE,OAAO,CAAC;EACrD;EAGA,IAAID,MAAM,CAACF,MAAM,EAAE;IACjB,OAAO,MAAME,MAAM,CAACF,MAAM,CAACC,IAAI,EAAEE,OAAO,CAAC;EAC3C;EAEA,IAAID,MAAM,CAACG,UAAU,EAAE;IACrB,OAAOH,MAAM,CAACG,UAAU,CAACJ,IAAI,EAAEE,OAAO,CAAC;EACzC;EAEA,IAAID,MAAM,CAACI,UAAU,EAAE;IACrB,OAAO,IAAIC,WAAW,CAAC,CAAC,CAACP,MAAM,CAAC,MAAME,MAAM,CAACI,UAAU,CAACL,IAAI,EAAEE,OAAO,CAAC,CAAC;EACzE;EAEA,IAAID,MAAM,CAACM,eAAe,EAAE;IAG1B,MAAMC,OAAO,GAAGD,eAAe,CAACP,IAAI,EAAEC,MAAM,EAAEC,OAAO,CAAC;IAGtD,MAAMO,MAAa,GAAG,EAAE;IACxB,WAAW,MAAMC,KAAK,IAAIF,OAAO,EAAE;MACjCC,MAAM,CAACE,IAAI,CAACD,KAAK,CAAC;IACpB;IAEA,OAAOjB,uBAAuB,CAAC,GAAGgB,MAAM,CAAC;EAC3C;EAEA,IAAI,CAACd,SAAS,IAAIM,MAAM,CAACW,cAAc,EAAE;IAEvC,MAAMC,gBAAgB,GAAGC,oBAAoB,CAAC,OAAO,CAAC;IACtD,MAAMlB,SAAS,CAACiB,gBAAgB,EAAEb,IAAI,CAAC;IAEvC,MAAMe,iBAAiB,GAAGD,oBAAoB,CAAC,QAAQ,CAAC;IAExD,MAAME,cAAc,GAAG,MAAMJ,cAAc,CACzCC,gBAAgB,EAChBE,iBAAiB,EACjBd,MAAM,EACNC,OACF,CAAC;IAED,MAAMe,QAAQ,GAAG,MAAMpB,SAAS,CAACmB,cAAc,CAAC;IAChD,OAAOC,QAAQ,CAACC,WAAW,CAAC,CAAC;EAC/B;EAEA,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;AACjD;AAKA,OAAO,SAASf,UAAUA,CAACJ,IAAS,EAAEC,MAAc,EAAEC,OAAuB,EAAe;EAC1F,IAAID,MAAM,CAACG,UAAU,EAAE;IACrB,OAAOH,MAAM,CAACG,UAAU,CAACJ,IAAI,EAAEE,OAAO,CAAC;EACzC;EACA,MAAM,IAAIiB,KAAK,CAAC,4CAA4C,CAAC;AAC/D;AAQA,OAAO,eAAed,UAAUA,CAC9BL,IAAS,EACTC,MAAc,EACdC,OAAuB,EACN;EACjB,IAAID,MAAM,CAACmB,IAAI,IAAInB,MAAM,CAACI,UAAU,EAAE;IACpC,OAAO,MAAMJ,MAAM,CAACI,UAAU,CAACL,IAAI,EAAEE,OAAO,CAAC;EAC/C;EAEA,IAAID,MAAM,CAACmB,IAAI,KAAKnB,MAAM,CAACF,MAAM,IAAIE,MAAM,CAACM,eAAe,CAAC,EAAE;IAC5D,MAAMW,WAAW,GAAG,MAAMnB,MAAM,CAACC,IAAI,EAAEC,MAAM,EAAEC,OAAO,CAAC;IACvD,OAAO,IAAImB,WAAW,CAAC,CAAC,CAACC,MAAM,CAACJ,WAAW,CAAC;EAC9C;EAEA,MAAM,IAAIC,KAAK,CAAC,sCAAsC,CAAC;AACzD;AAKA,OAAO,SAASZ,eAAeA,CAC7BP,IAAS,EACTC,MAAc,EACdC,OAAuB,EACK;EAC5B,IAAID,MAAM,CAACM,eAAe,EAAE;IAC1B,MAAMgB,YAAY,GAAGC,WAAW,CAACxB,IAAI,CAAC;IACtC,OAAOC,MAAM,CAACM,eAAe,CAACgB,YAAY,EAAErB,OAAO,CAAC;EACtD;EAEA,MAAM,IAAIiB,KAAK,CAAC,yCAAyC,CAAC;AAC5D;AAMA,OAAO,eAAeP,cAAcA,CAClCa,QAAQ,EACRC,SAAS,EACTzB,MAAc,EACdC,OAAO,EACU;EACjBuB,QAAQ,GAAG/B,WAAW,CAAC+B,QAAQ,CAAC;EAChCC,SAAS,GAAGhC,WAAW,CAACgC,SAAS,CAAC;EAClC,IAAI/B,SAAS,IAAI,CAACM,MAAM,CAACW,cAAc,EAAE;IACvC,MAAM,IAAIO,KAAK,CAAC,CAAC;EACnB;EACA,MAAMH,cAAc,GAAG,MAAMf,MAAM,CAACW,cAAc,CAACa,QAAQ,EAAEC,SAAS,EAAExB,OAAO,CAAC;EAChF,OAAOc,cAAc;AACvB;AAKA,SAASQ,WAAWA,CAACxB,IAAI,EAAE;EACzB,MAAMuB,YAAY,GAAG,CAAC;IAACI,KAAK,EAAE3B,IAAI;IAAE4B,KAAK,EAAE,CAAC;IAAEC,GAAG,EAAE7B,IAAI,CAAC8B;EAAM,CAAC,CAAC;EAChE,OAAOP,YAAY;AACrB;AAKA,SAAST,oBAAoBA,CAACiB,QAAgB,EAAU;EACtD,eAAAC,MAAA,CAAeD,QAAQ;AACzB"}