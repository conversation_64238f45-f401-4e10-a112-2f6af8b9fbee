{"version": 3, "file": "parse.js", "names": ["assert", "validateWorkerVersion", "parseWithWorker", "canParseWithWorker", "isLoaderObject", "isResponse", "normalizeOptions", "getArrayBufferOrStringFromData", "getLoaderContext", "getLoadersFromContext", "getResourceUrl", "<PERSON><PERSON><PERSON><PERSON>", "parse", "data", "loaders", "options", "context", "Array", "isArray", "undefined", "url", "typed<PERSON>oa<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "loader", "parseWith<PERSON><PERSON>der", "response", "ok", "redirected", "status", "statusText", "type", "headers", "Object", "fromEntries", "entries", "parseTextSync", "dataType", "parseText", "parseSync", "Error", "concat", "id"], "sources": ["../../../../src/lib/api/parse.ts"], "sourcesContent": ["import type {<PERSON>Type, Loader, LoaderContext, LoaderOptions} from '@loaders.gl/loader-utils';\nimport {assert, validateWorkerVersion} from '@loaders.gl/worker-utils';\nimport {parseWithWorker, canParseWithWorker} from '@loaders.gl/loader-utils';\nimport {isLoaderObject} from '../loader-utils/normalize-loader';\nimport {isResponse} from '../../javascript-utils/is-type';\nimport {normalizeOptions} from '../loader-utils/option-utils';\nimport {getArrayBufferOrStringFromData} from '../loader-utils/get-data';\nimport {getLoaderContext, getLoadersFromContext} from '../loader-utils/loader-context';\nimport {getResourceUrl} from '../utils/resource-utils';\nimport {selectLoader} from './select-loader';\n\n/**\n * Parses `data` using a specified loader\n * @param data\n * @param loaders\n * @param options\n * @param context\n */\nexport async function parse(\n  data: DataType | Promise<DataType>,\n  loaders?: Loader | Loader[] | LoaderOptions,\n  options?: LoaderOptions,\n  context?: LoaderContext\n): Promise<any> {\n  assert(!context || typeof context === 'object'); // parse no longer accepts final url\n\n  // Signature: parse(data, options, context | url)\n  // Uses registered loaders\n  if (loaders && !Array.isArray(loaders) && !isLoaderObject(loaders)) {\n    context = undefined; // context not supported in short signature\n    options = loaders as LoaderOptions;\n    loaders = undefined;\n  }\n\n  data = await data; // Resolve any promise\n  options = options || {};\n\n  // Extract a url for auto detection\n  const url = getResourceUrl(data);\n\n  // Chooses a loader (and normalizes it)\n  // Also use any loaders in the context, new loaders take priority\n  const typedLoaders = loaders as Loader | Loader[] | undefined;\n  const candidateLoaders = getLoadersFromContext(typedLoaders, context);\n  // todo hacky type cast\n  const loader = await selectLoader(data as ArrayBuffer, candidateLoaders, options);\n  // Note: if no loader was found, if so just return null\n  if (!loader) {\n    return null;\n  }\n\n  // Normalize options\n  options = normalizeOptions(options, loader, candidateLoaders, url);\n\n  // Get a context (if already present, will be unchanged)\n  context = getLoaderContext({url, parse, loaders: candidateLoaders}, options, context || null);\n\n  return await parseWithLoader(loader, data, options, context);\n}\n\n// TODO: support progress and abort\n// TODO - should accept loader.parseAsyncIterator and concatenate.\nasync function parseWithLoader(loader, data, options, context) {\n  validateWorkerVersion(loader);\n\n  if (isResponse(data)) {\n    // Serialize to support passing the response to web worker\n    const response = data as Response;\n    const {ok, redirected, status, statusText, type, url} = response;\n    const headers = Object.fromEntries(response.headers.entries());\n    context.response = {headers, ok, redirected, status, statusText, type, url};\n  }\n\n  data = await getArrayBufferOrStringFromData(data, loader, options);\n\n  // First check for synchronous text parser, wrap results in promises\n  if (loader.parseTextSync && typeof data === 'string') {\n    options.dataType = 'text';\n    return loader.parseTextSync(data, options, context, loader);\n  }\n\n  // If we have a workerUrl and the loader can parse the given options efficiently in a worker\n  if (canParseWithWorker(loader, options)) {\n    return await parseWithWorker(loader, data, options, context, parse);\n  }\n\n  // Check for asynchronous parser\n  if (loader.parseText && typeof data === 'string') {\n    return await loader.parseText(data, options, context, loader);\n  }\n\n  if (loader.parse) {\n    return await loader.parse(data, options, context, loader);\n  }\n\n  // This should not happen, all sync loaders should also offer `parse` function\n  assert(!loader.parseSync);\n\n  // TBD - If asynchronous parser not available, return null\n  throw new Error(`${loader.id} loader - no parser found and worker is disabled`);\n}\n"], "mappings": "AACA,SAAQA,MAAM,EAAEC,qBAAqB,QAAO,0BAA0B;AACtE,SAAQC,eAAe,EAAEC,kBAAkB,QAAO,0BAA0B;AAC5E,SAAQC,cAAc,QAAO,kCAAkC;AAC/D,SAAQC,UAAU,QAAO,gCAAgC;AACzD,SAAQC,gBAAgB,QAAO,8BAA8B;AAC7D,SAAQC,8BAA8B,QAAO,0BAA0B;AACvE,SAAQC,gBAAgB,EAAEC,qBAAqB,QAAO,gCAAgC;AACtF,SAAQC,cAAc,QAAO,yBAAyB;AACtD,SAAQC,YAAY,QAAO,iBAAiB;AAS5C,OAAO,eAAeC,KAAKA,CACzBC,IAAkC,EAClCC,OAA2C,EAC3CC,OAAuB,EACvBC,OAAuB,EACT;EACdhB,MAAM,CAAC,CAACgB,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,CAAC;EAI/C,IAAIF,OAAO,IAAI,CAACG,KAAK,CAACC,OAAO,CAACJ,OAAO,CAAC,IAAI,CAACV,cAAc,CAACU,OAAO,CAAC,EAAE;IAClEE,OAAO,GAAGG,SAAS;IACnBJ,OAAO,GAAGD,OAAwB;IAClCA,OAAO,GAAGK,SAAS;EACrB;EAEAN,IAAI,GAAG,MAAMA,IAAI;EACjBE,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAGvB,MAAMK,GAAG,GAAGV,cAAc,CAACG,IAAI,CAAC;EAIhC,MAAMQ,YAAY,GAAGP,OAAwC;EAC7D,MAAMQ,gBAAgB,GAAGb,qBAAqB,CAACY,YAAY,EAAEL,OAAO,CAAC;EAErE,MAAMO,MAAM,GAAG,MAAMZ,YAAY,CAACE,IAAI,EAAiBS,gBAAgB,EAAEP,OAAO,CAAC;EAEjF,IAAI,CAACQ,MAAM,EAAE;IACX,OAAO,IAAI;EACb;EAGAR,OAAO,GAAGT,gBAAgB,CAACS,OAAO,EAAEQ,MAAM,EAAED,gBAAgB,EAAEF,GAAG,CAAC;EAGlEJ,OAAO,GAAGR,gBAAgB,CAAC;IAACY,GAAG;IAAER,KAAK;IAAEE,OAAO,EAAEQ;EAAgB,CAAC,EAAEP,OAAO,EAAEC,OAAO,IAAI,IAAI,CAAC;EAE7F,OAAO,MAAMQ,eAAe,CAACD,MAAM,EAAEV,IAAI,EAAEE,OAAO,EAAEC,OAAO,CAAC;AAC9D;AAIA,eAAeQ,eAAeA,CAACD,MAAM,EAAEV,IAAI,EAAEE,OAAO,EAAEC,OAAO,EAAE;EAC7Df,qBAAqB,CAACsB,MAAM,CAAC;EAE7B,IAAIlB,UAAU,CAACQ,IAAI,CAAC,EAAE;IAEpB,MAAMY,QAAQ,GAAGZ,IAAgB;IACjC,MAAM;MAACa,EAAE;MAAEC,UAAU;MAAEC,MAAM;MAAEC,UAAU;MAAEC,IAAI;MAAEV;IAAG,CAAC,GAAGK,QAAQ;IAChE,MAAMM,OAAO,GAAGC,MAAM,CAACC,WAAW,CAACR,QAAQ,CAACM,OAAO,CAACG,OAAO,CAAC,CAAC,CAAC;IAC9DlB,OAAO,CAACS,QAAQ,GAAG;MAACM,OAAO;MAAEL,EAAE;MAAEC,UAAU;MAAEC,MAAM;MAAEC,UAAU;MAAEC,IAAI;MAAEV;IAAG,CAAC;EAC7E;EAEAP,IAAI,GAAG,MAAMN,8BAA8B,CAACM,IAAI,EAAEU,MAAM,EAAER,OAAO,CAAC;EAGlE,IAAIQ,MAAM,CAACY,aAAa,IAAI,OAAOtB,IAAI,KAAK,QAAQ,EAAE;IACpDE,OAAO,CAACqB,QAAQ,GAAG,MAAM;IACzB,OAAOb,MAAM,CAACY,aAAa,CAACtB,IAAI,EAAEE,OAAO,EAAEC,OAAO,EAAEO,MAAM,CAAC;EAC7D;EAGA,IAAIpB,kBAAkB,CAACoB,MAAM,EAAER,OAAO,CAAC,EAAE;IACvC,OAAO,MAAMb,eAAe,CAACqB,MAAM,EAAEV,IAAI,EAAEE,OAAO,EAAEC,OAAO,EAAEJ,KAAK,CAAC;EACrE;EAGA,IAAIW,MAAM,CAACc,SAAS,IAAI,OAAOxB,IAAI,KAAK,QAAQ,EAAE;IAChD,OAAO,MAAMU,MAAM,CAACc,SAAS,CAACxB,IAAI,EAAEE,OAAO,EAAEC,OAAO,EAAEO,MAAM,CAAC;EAC/D;EAEA,IAAIA,MAAM,CAACX,KAAK,EAAE;IAChB,OAAO,MAAMW,MAAM,CAACX,KAAK,CAACC,IAAI,EAAEE,OAAO,EAAEC,OAAO,EAAEO,MAAM,CAAC;EAC3D;EAGAvB,MAAM,CAAC,CAACuB,MAAM,CAACe,SAAS,CAAC;EAGzB,MAAM,IAAIC,KAAK,IAAAC,MAAA,CAAIjB,MAAM,CAACkB,EAAE,qDAAkD,CAAC;AACjF"}