{"version": 3, "file": "load.js", "names": ["isBlob", "isLoaderObject", "getFetchFunction", "parse", "load", "url", "loaders", "options", "context", "Array", "isArray", "undefined", "fetch", "data"], "sources": ["../../../../src/lib/api/load.ts"], "sourcesContent": ["import type {DataType, Loader, LoaderContext, LoaderOptions} from '@loaders.gl/loader-utils';\nimport {isBlob} from '../../javascript-utils/is-type';\nimport {isLoaderObject} from '../loader-utils/normalize-loader';\nimport {getFetchFunction} from '../loader-utils/get-fetch-function';\n\nimport {parse} from './parse';\n\n/**\n * Parses `data` using a specified loader\n * Note: Load does duplicate a lot of parse.\n * it can also call fetchFile on string urls, which `parse` won't do.\n * @param data\n * @param loaders\n * @param options\n * @param context\n */\n// implementation signature\nexport async function load(\n  url: string | DataType,\n  loaders?: Loader | Loader[] | LoaderOptions,\n  options?: LoaderOptions,\n  context?: LoaderContext\n): Promise<any> {\n  // Signature: load(url, options)\n  if (!Array.isArray(loaders) && !isLoaderObject(loaders)) {\n    context = undefined; // context not supported in short signature\n    options = loaders as LoaderOptions;\n    loaders = undefined;\n  }\n\n  // Select fetch function\n  const fetch = getFetchFunction(options);\n\n  // at this point, `url` could be already loaded binary data\n  let data = url;\n  // url is a string, fetch the url\n  if (typeof url === 'string') {\n    data = await fetch(url);\n    // URL is Blob or File, fetchFile handles it (alt: we could generate ObjectURL here)\n  }\n\n  if (isBlob(url)) {\n    // The fetch response object will contain blob.name\n    // @ts-expect-error TODO - This may not work for overridden fetch functions\n    data = await fetch(url);\n  }\n\n  // Data is loaded (at least we have a `Response` object) so time to hand over to `parse`\n  return await parse(data, loaders as Loader[], options);\n}\n"], "mappings": "AACA,SAAQA,MAAM,QAAO,gCAAgC;AACrD,SAAQC,cAAc,QAAO,kCAAkC;AAC/D,SAAQC,gBAAgB,QAAO,oCAAoC;AAEnE,SAAQC,KAAK,QAAO,SAAS;AAY7B,OAAO,eAAeC,IAAIA,CACxBC,GAAsB,EACtBC,OAA2C,EAC3CC,OAAuB,EACvBC,OAAuB,EACT;EAEd,IAAI,CAACC,KAAK,CAACC,OAAO,CAACJ,OAAO,CAAC,IAAI,CAACL,cAAc,CAACK,OAAO,CAAC,EAAE;IACvDE,OAAO,GAAGG,SAAS;IACnBJ,OAAO,GAAGD,OAAwB;IAClCA,OAAO,GAAGK,SAAS;EACrB;EAGA,MAAMC,KAAK,GAAGV,gBAAgB,CAACK,OAAO,CAAC;EAGvC,IAAIM,IAAI,GAAGR,GAAG;EAEd,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAC3BQ,IAAI,GAAG,MAAMD,KAAK,CAACP,GAAG,CAAC;EAEzB;EAEA,IAAIL,MAAM,CAACK,GAAG,CAAC,EAAE;IAGfQ,IAAI,GAAG,MAAMD,KAAK,CAACP,GAAG,CAAC;EACzB;EAGA,OAAO,MAAMF,KAAK,CAACU,IAAI,EAAEP,OAAO,EAAcC,OAAO,CAAC;AACxD"}