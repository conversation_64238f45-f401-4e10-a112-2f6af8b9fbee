{"version": 3, "file": "register-loaders.js", "names": ["normalize<PERSON><PERSON><PERSON>", "getGlobalLoaderState", "getGlobalLoaderRegistry", "state", "loaderRegistry", "registerLoaders", "loaders", "Array", "isArray", "loader", "normalizedLoader", "find", "registeredLoader", "unshift", "getRegisteredLoaders", "_unregisterLoaders"], "sources": ["../../../../src/lib/api/register-loaders.ts"], "sourcesContent": ["import {Loader} from '@loaders.gl/loader-utils';\nimport {normalizeLoader} from '../loader-utils/normalize-loader';\nimport {getGlobalLoaderState} from '../loader-utils/option-utils';\n\n// Store global registered loaders on the global object to increase chances of cross loaders-version interoperability\n// This use case is not reliable but can help when testing new versions of loaders.gl with existing frameworks\nconst getGlobalLoaderRegistry = () => {\n  const state = getGlobalLoaderState();\n  state.loaderRegistry = state.loaderRegistry || [];\n  return state.loaderRegistry;\n};\n\n/** Register a list of global loaders */\nexport function registerLoaders(loaders: Loader[] | Loader) {\n  const loaderRegistry = getGlobalLoaderRegistry();\n\n  loaders = Array.isArray(loaders) ? loaders : [loaders];\n\n  for (const loader of loaders) {\n    const normalizedLoader = normalizeLoader(loader);\n    if (!loaderRegistry.find((registeredLoader) => normalizedLoader === registeredLoader)) {\n      // add to the beginning of the loaderRegistry, so the last registeredLoader get picked\n      loaderRegistry.unshift(normalizedLoader);\n    }\n  }\n}\n\nexport function getRegisteredLoaders(): Loader[] {\n  return getGlobalLoaderRegistry();\n}\n\n/** @deprecated For testing only  */\nexport function _unregisterLoaders() {\n  const state = getGlobalLoaderState();\n  state.loaderRegistry = [];\n}\n"], "mappings": "AACA,SAAQA,eAAe,QAAO,kCAAkC;AAChE,SAAQC,oBAAoB,QAAO,8BAA8B;AAIjE,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;EACpC,MAAMC,KAAK,GAAGF,oBAAoB,CAAC,CAAC;EACpCE,KAAK,CAACC,cAAc,GAAGD,KAAK,CAACC,cAAc,IAAI,EAAE;EACjD,OAAOD,KAAK,CAACC,cAAc;AAC7B,CAAC;AAGD,OAAO,SAASC,eAAeA,CAACC,OAA0B,EAAE;EAC1D,MAAMF,cAAc,GAAGF,uBAAuB,CAAC,CAAC;EAEhDI,OAAO,GAAGC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,GAAGA,OAAO,GAAG,CAACA,OAAO,CAAC;EAEtD,KAAK,MAAMG,MAAM,IAAIH,OAAO,EAAE;IAC5B,MAAMI,gBAAgB,GAAGV,eAAe,CAACS,MAAM,CAAC;IAChD,IAAI,CAACL,cAAc,CAACO,IAAI,CAAEC,gBAAgB,IAAKF,gBAAgB,KAAKE,gBAAgB,CAAC,EAAE;MAErFR,cAAc,CAACS,OAAO,CAACH,gBAAgB,CAAC;IAC1C;EACF;AACF;AAEA,OAAO,SAASI,oBAAoBA,CAAA,EAAa;EAC/C,OAAOZ,uBAAuB,CAAC,CAAC;AAClC;AAGA,OAAO,SAASa,kBAAkBA,CAAA,EAAG;EACnC,MAAMZ,KAAK,GAAGF,oBAAoB,CAAC,CAAC;EACpCE,KAAK,CAACC,cAAc,GAAG,EAAE;AAC3B"}