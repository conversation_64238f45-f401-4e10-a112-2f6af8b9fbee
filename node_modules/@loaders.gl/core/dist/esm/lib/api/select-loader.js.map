{"version": 3, "file": "select-loader.js", "names": ["compareArrayBuffers", "path", "normalize<PERSON><PERSON><PERSON>", "log", "getResourceUrl", "getResourceMIMEType", "getRegisteredLoaders", "isBlob", "stripQueryString", "EXT_PATTERN", "<PERSON><PERSON><PERSON><PERSON>", "data", "loaders", "arguments", "length", "undefined", "options", "context", "validHTTPResponse", "loader", "selectLoaderSync", "nothrow", "slice", "arrayBuffer", "Error", "getNoValidLoaderMessage", "Array", "isArray", "<PERSON><PERSON><PERSON><PERSON>", "concat", "ignoreRegisteredLoaders", "push", "normalizeLoaders", "selectLoaderInternal", "url", "type", "testUrl", "reason", "mimeType", "findLoaderByMIMEType", "findLoaderByUrl", "findLoaderByInitialBytes", "getFirstCharacters", "fallbackMimeType", "_loader", "name", "Response", "status", "message", "filename", "firstCharacters", "match", "exec", "extension", "findLoaderByExtension", "toLowerCase", "loaderExtension", "extensions", "mimeTypes", "includes", "id", "testDataAgainstText", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "testDataAgainstBinary", "buffer", "byteOffset", "testText", "tests", "some", "test", "startsWith", "testBinary", "byteLength", "magic", "getMagicString", "dataView", "DataView", "i", "String", "fromCharCode", "getUint8"], "sources": ["../../../../src/lib/api/select-loader.ts"], "sourcesContent": ["import type {LoaderContext, LoaderOptions, Loader} from '@loaders.gl/loader-utils';\nimport {compareArrayBuffers, path} from '@loaders.gl/loader-utils';\nimport {normalizeLoader} from '../loader-utils/normalize-loader';\nimport {log} from '../utils/log';\nimport {getResourceUrl, getResourceMIMEType} from '../utils/resource-utils';\nimport {getRegisteredLoaders} from './register-loaders';\nimport {isBlob} from '../../javascript-utils/is-type';\nimport {stripQueryString} from '../utils/url-utils';\n\nconst EXT_PATTERN = /\\.([^.]+)$/;\n\n// TODO - Need a variant that peeks at streams for parseInBatches\n// TODO - Detect multiple matching loaders? Use heuristics to grade matches?\n// TODO - Allow apps to pass context to disambiguate between multiple matches (e.g. multiple .json formats)?\n\n/**\n * Find a loader that matches file extension and/or initial file content\n * Search the loaders array argument for a loader that matches url extension or initial data\n * Returns: a normalized loader\n * @param data data to assist\n * @param loaders\n * @param options\n * @param context used internally, applications should not provide this parameter\n */\nexport async function selectLoader(\n  data: Response | Blob | ArrayBuffer | string,\n  loaders: Loader[] | Loader = [],\n  options?: LoaderOptions,\n  context?: LoaderContext\n): Promise<Loader | null> {\n  if (!validHTTPResponse(data)) {\n    return null;\n  }\n\n  // First make a sync attempt, disabling exceptions\n  let loader = selectLoaderSync(data, loaders, {...options, nothrow: true}, context);\n  if (loader) {\n    return loader;\n  }\n\n  // For Blobs and Files, try to asynchronously read a small initial slice and test again with that\n  // to see if we can detect by initial content\n  if (isBlob(data)) {\n    data = await (data as Blob).slice(0, 10).arrayBuffer();\n    loader = selectLoaderSync(data, loaders, options, context);\n  }\n\n  // no loader available\n  if (!loader && !options?.nothrow) {\n    throw new Error(getNoValidLoaderMessage(data));\n  }\n\n  return loader;\n}\n\n/**\n * Find a loader that matches file extension and/or initial file content\n * Search the loaders array argument for a loader that matches url extension or initial data\n * Returns: a normalized loader\n * @param data data to assist\n * @param loaders\n * @param options\n * @param context used internally, applications should not provide this parameter\n */\nexport function selectLoaderSync(\n  data: Response | Blob | ArrayBuffer | string,\n  loaders: Loader[] | Loader = [],\n  options?: LoaderOptions,\n  context?: LoaderContext\n): Loader | null {\n  if (!validHTTPResponse(data)) {\n    return null;\n  }\n\n  // eslint-disable-next-line complexity\n  // if only a single loader was provided (not as array), force its use\n  // TODO - Should this behavior be kept and documented?\n  if (loaders && !Array.isArray(loaders)) {\n    // TODO - remove support for legacy loaders\n    return normalizeLoader(loaders);\n  }\n\n  // Build list of candidate loaders that will be searched in order for a match\n  let candidateLoaders: Loader[] = [];\n  // First search supplied loaders\n  if (loaders) {\n    candidateLoaders = candidateLoaders.concat(loaders);\n  }\n  // Then fall back to registered loaders\n  if (!options?.ignoreRegisteredLoaders) {\n    candidateLoaders.push(...getRegisteredLoaders());\n  }\n\n  // TODO - remove support for legacy loaders\n  normalizeLoaders(candidateLoaders);\n\n  const loader = selectLoaderInternal(data, candidateLoaders, options, context);\n\n  // no loader available\n  if (!loader && !options?.nothrow) {\n    throw new Error(getNoValidLoaderMessage(data));\n  }\n\n  return loader;\n}\n\n/** Implements loaders selection logic */\n// eslint-disable-next-line complexity\nfunction selectLoaderInternal(\n  data: Response | Blob | ArrayBuffer | string,\n  loaders: Loader[],\n  options?: LoaderOptions,\n  context?: LoaderContext\n) {\n  const url = getResourceUrl(data);\n  const type = getResourceMIMEType(data);\n\n  const testUrl = stripQueryString(url) || context?.url;\n\n  let loader: Loader | null = null;\n  let reason: string = '';\n\n  // if options.mimeType is supplied, it takes precedence\n  if (options?.mimeType) {\n    loader = findLoaderByMIMEType(loaders, options?.mimeType);\n    reason = `match forced by supplied MIME type ${options?.mimeType}`;\n  }\n\n  // Look up loader by url\n  loader = loader || findLoaderByUrl(loaders, testUrl);\n  reason = reason || (loader ? `matched url ${testUrl}` : '');\n\n  // Look up loader by mime type\n  loader = loader || findLoaderByMIMEType(loaders, type);\n  reason = reason || (loader ? `matched MIME type ${type}` : '');\n\n  // Look for loader via initial bytes (Note: not always accessible (e.g. Response, stream, async iterator)\n  loader = loader || findLoaderByInitialBytes(loaders, data);\n  reason = reason || (loader ? `matched initial data ${getFirstCharacters(data)}` : '');\n\n  // Look up loader by fallback mime type\n  loader = loader || findLoaderByMIMEType(loaders, options?.fallbackMimeType);\n  reason = reason || (loader ? `matched fallback MIME type ${type}` : '');\n\n  if (reason) {\n    log.log(1, `selectLoader selected ${loader?.name}: ${reason}.`);\n  }\n\n  return loader;\n}\n\n/** Check HTTP Response */\nfunction validHTTPResponse(data: any): boolean {\n  // HANDLE HTTP status\n  if (data instanceof Response) {\n    // 204 - NO CONTENT. This handles cases where e.g. a tile server responds with 204 for a missing tile\n    if (data.status === 204) {\n      return false;\n    }\n  }\n  return true;\n}\n\n/** Generate a helpful message to help explain why loader selection failed. */\nfunction getNoValidLoaderMessage(data): string {\n  const url = getResourceUrl(data);\n  const type = getResourceMIMEType(data);\n\n  let message = 'No valid loader found (';\n  message += url ? `${path.filename(url)}, ` : 'no url provided, ';\n  message += `MIME type: ${type ? `\"${type}\"` : 'not provided'}, `;\n  // First characters are only accessible when called on data (string or arrayBuffer).\n  const firstCharacters: string = data ? getFirstCharacters(data) : '';\n  message += firstCharacters ? ` first bytes: \"${firstCharacters}\"` : 'first bytes: not available';\n  message += ')';\n  return message;\n}\n\nfunction normalizeLoaders(loaders: Loader[]): void {\n  for (const loader of loaders) {\n    normalizeLoader(loader);\n  }\n}\n\n// TODO - Would be nice to support http://example.com/file.glb?parameter=1\n// E.g: x = new URL('http://example.com/file.glb?load=1'; x.pathname\nfunction findLoaderByUrl(loaders: Loader[], url?: string): Loader | null {\n  // Get extension\n  const match = url && EXT_PATTERN.exec(url);\n  const extension = match && match[1];\n  return extension ? findLoaderByExtension(loaders, extension) : null;\n}\n\nfunction findLoaderByExtension(loaders: Loader[], extension: string): Loader | null {\n  extension = extension.toLowerCase();\n\n  for (const loader of loaders) {\n    for (const loaderExtension of loader.extensions) {\n      if (loaderExtension.toLowerCase() === extension) {\n        return loader;\n      }\n    }\n  }\n  return null;\n}\n\nfunction findLoaderByMIMEType(loaders, mimeType) {\n  for (const loader of loaders) {\n    if (loader.mimeTypes && loader.mimeTypes.includes(mimeType)) {\n      return loader;\n    }\n\n    // Support referring to loaders using the \"unregistered tree\"\n    // https://en.wikipedia.org/wiki/Media_type#Unregistered_tree\n    if (mimeType === `application/x.${loader.id}`) {\n      return loader;\n    }\n  }\n  return null;\n}\n\nfunction findLoaderByInitialBytes(loaders, data) {\n  if (!data) {\n    return null;\n  }\n\n  for (const loader of loaders) {\n    if (typeof data === 'string') {\n      if (testDataAgainstText(data, loader)) {\n        return loader;\n      }\n    } else if (ArrayBuffer.isView(data)) {\n      // Typed Arrays can have offsets into underlying buffer\n      if (testDataAgainstBinary(data.buffer, data.byteOffset, loader)) {\n        return loader;\n      }\n    } else if (data instanceof ArrayBuffer) {\n      const byteOffset = 0;\n      if (testDataAgainstBinary(data, byteOffset, loader)) {\n        return loader;\n      }\n    }\n    // TODO Handle streaming case (requires creating a new AsyncIterator)\n  }\n  return null;\n}\n\nfunction testDataAgainstText(data, loader) {\n  if (loader.testText) {\n    return loader.testText(data);\n  }\n\n  const tests = Array.isArray(loader.tests) ? loader.tests : [loader.tests];\n  return tests.some((test) => data.startsWith(test));\n}\n\nfunction testDataAgainstBinary(data, byteOffset, loader) {\n  const tests = Array.isArray(loader.tests) ? loader.tests : [loader.tests];\n  return tests.some((test) => testBinary(data, byteOffset, loader, test));\n}\n\nfunction testBinary(data, byteOffset, loader, test) {\n  if (test instanceof ArrayBuffer) {\n    return compareArrayBuffers(test, data, test.byteLength);\n  }\n  switch (typeof test) {\n    case 'function':\n      return test(data, loader);\n\n    case 'string':\n      // Magic bytes check: If `test` is a string, check if binary data starts with that strings\n      const magic = getMagicString(data, byteOffset, test.length);\n      return test === magic;\n\n    default:\n      return false;\n  }\n}\n\nfunction getFirstCharacters(data, length: number = 5) {\n  if (typeof data === 'string') {\n    return data.slice(0, length);\n  } else if (ArrayBuffer.isView(data)) {\n    // Typed Arrays can have offsets into underlying buffer\n    return getMagicString(data.buffer, data.byteOffset, length);\n  } else if (data instanceof ArrayBuffer) {\n    const byteOffset = 0;\n    return getMagicString(data, byteOffset, length);\n  }\n  return '';\n}\n\nfunction getMagicString(arrayBuffer, byteOffset, length) {\n  if (arrayBuffer.byteLength < byteOffset + length) {\n    return '';\n  }\n  const dataView = new DataView(arrayBuffer);\n  let magic = '';\n  for (let i = 0; i < length; i++) {\n    magic += String.fromCharCode(dataView.getUint8(byteOffset + i));\n  }\n  return magic;\n}\n"], "mappings": "AACA,SAAQA,mBAAmB,EAAEC,IAAI,QAAO,0BAA0B;AAClE,SAAQC,eAAe,QAAO,kCAAkC;AAChE,SAAQC,GAAG,QAAO,cAAc;AAChC,SAAQC,cAAc,EAAEC,mBAAmB,QAAO,yBAAyB;AAC3E,SAAQC,oBAAoB,QAAO,oBAAoB;AACvD,SAAQC,MAAM,QAAO,gCAAgC;AACrD,SAAQC,gBAAgB,QAAO,oBAAoB;AAEnD,MAAMC,WAAW,GAAG,YAAY;AAehC,OAAO,eAAeC,YAAYA,CAChCC,IAA4C,EAIpB;EAAA,IAHxBC,OAA0B,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAAA,IAC/BG,OAAuB,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,IACvBE,OAAuB,GAAAJ,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAEvB,IAAI,CAACG,iBAAiB,CAACP,IAAI,CAAC,EAAE;IAC5B,OAAO,IAAI;EACb;EAGA,IAAIQ,MAAM,GAAGC,gBAAgB,CAACT,IAAI,EAAEC,OAAO,EAAE;IAAC,GAAGI,OAAO;IAAEK,OAAO,EAAE;EAAI,CAAC,EAAEJ,OAAO,CAAC;EAClF,IAAIE,MAAM,EAAE;IACV,OAAOA,MAAM;EACf;EAIA,IAAIZ,MAAM,CAACI,IAAI,CAAC,EAAE;IAChBA,IAAI,GAAG,MAAOA,IAAI,CAAUW,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;IACtDJ,MAAM,GAAGC,gBAAgB,CAACT,IAAI,EAAEC,OAAO,EAAEI,OAAO,EAAEC,OAAO,CAAC;EAC5D;EAGA,IAAI,CAACE,MAAM,IAAI,EAACH,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEK,OAAO,GAAE;IAChC,MAAM,IAAIG,KAAK,CAACC,uBAAuB,CAACd,IAAI,CAAC,CAAC;EAChD;EAEA,OAAOQ,MAAM;AACf;AAWA,OAAO,SAASC,gBAAgBA,CAC9BT,IAA4C,EAI7B;EAAA,IAHfC,OAA0B,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAAA,IAC/BG,OAAuB,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,IACvBE,OAAuB,GAAAJ,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAEvB,IAAI,CAACG,iBAAiB,CAACP,IAAI,CAAC,EAAE;IAC5B,OAAO,IAAI;EACb;EAKA,IAAIC,OAAO,IAAI,CAACc,KAAK,CAACC,OAAO,CAACf,OAAO,CAAC,EAAE;IAEtC,OAAOV,eAAe,CAACU,OAAO,CAAC;EACjC;EAGA,IAAIgB,gBAA0B,GAAG,EAAE;EAEnC,IAAIhB,OAAO,EAAE;IACXgB,gBAAgB,GAAGA,gBAAgB,CAACC,MAAM,CAACjB,OAAO,CAAC;EACrD;EAEA,IAAI,EAACI,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEc,uBAAuB,GAAE;IACrCF,gBAAgB,CAACG,IAAI,CAAC,GAAGzB,oBAAoB,CAAC,CAAC,CAAC;EAClD;EAGA0B,gBAAgB,CAACJ,gBAAgB,CAAC;EAElC,MAAMT,MAAM,GAAGc,oBAAoB,CAACtB,IAAI,EAAEiB,gBAAgB,EAAEZ,OAAO,EAAEC,OAAO,CAAC;EAG7E,IAAI,CAACE,MAAM,IAAI,EAACH,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEK,OAAO,GAAE;IAChC,MAAM,IAAIG,KAAK,CAACC,uBAAuB,CAACd,IAAI,CAAC,CAAC;EAChD;EAEA,OAAOQ,MAAM;AACf;AAIA,SAASc,oBAAoBA,CAC3BtB,IAA4C,EAC5CC,OAAiB,EACjBI,OAAuB,EACvBC,OAAuB,EACvB;EACA,MAAMiB,GAAG,GAAG9B,cAAc,CAACO,IAAI,CAAC;EAChC,MAAMwB,IAAI,GAAG9B,mBAAmB,CAACM,IAAI,CAAC;EAEtC,MAAMyB,OAAO,GAAG5B,gBAAgB,CAAC0B,GAAG,CAAC,KAAIjB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEiB,GAAG;EAErD,IAAIf,MAAqB,GAAG,IAAI;EAChC,IAAIkB,MAAc,GAAG,EAAE;EAGvB,IAAIrB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEsB,QAAQ,EAAE;IACrBnB,MAAM,GAAGoB,oBAAoB,CAAC3B,OAAO,EAAEI,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsB,QAAQ,CAAC;IACzDD,MAAM,yCAAAR,MAAA,CAAyCb,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsB,QAAQ,CAAE;EACpE;EAGAnB,MAAM,GAAGA,MAAM,IAAIqB,eAAe,CAAC5B,OAAO,EAAEwB,OAAO,CAAC;EACpDC,MAAM,GAAGA,MAAM,KAAKlB,MAAM,kBAAAU,MAAA,CAAkBO,OAAO,IAAK,EAAE,CAAC;EAG3DjB,MAAM,GAAGA,MAAM,IAAIoB,oBAAoB,CAAC3B,OAAO,EAAEuB,IAAI,CAAC;EACtDE,MAAM,GAAGA,MAAM,KAAKlB,MAAM,wBAAAU,MAAA,CAAwBM,IAAI,IAAK,EAAE,CAAC;EAG9DhB,MAAM,GAAGA,MAAM,IAAIsB,wBAAwB,CAAC7B,OAAO,EAAED,IAAI,CAAC;EAC1D0B,MAAM,GAAGA,MAAM,KAAKlB,MAAM,2BAAAU,MAAA,CAA2Ba,kBAAkB,CAAC/B,IAAI,CAAC,IAAK,EAAE,CAAC;EAGrFQ,MAAM,GAAGA,MAAM,IAAIoB,oBAAoB,CAAC3B,OAAO,EAAEI,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2B,gBAAgB,CAAC;EAC3EN,MAAM,GAAGA,MAAM,KAAKlB,MAAM,iCAAAU,MAAA,CAAiCM,IAAI,IAAK,EAAE,CAAC;EAEvE,IAAIE,MAAM,EAAE;IAAA,IAAAO,OAAA;IACVzC,GAAG,CAACA,GAAG,CAAC,CAAC,2BAAA0B,MAAA,EAAAe,OAAA,GAA2BzB,MAAM,cAAAyB,OAAA,uBAANA,OAAA,CAAQC,IAAI,QAAAhB,MAAA,CAAKQ,MAAM,MAAG,CAAC;EACjE;EAEA,OAAOlB,MAAM;AACf;AAGA,SAASD,iBAAiBA,CAACP,IAAS,EAAW;EAE7C,IAAIA,IAAI,YAAYmC,QAAQ,EAAE;IAE5B,IAAInC,IAAI,CAACoC,MAAM,KAAK,GAAG,EAAE;MACvB,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb;AAGA,SAAStB,uBAAuBA,CAACd,IAAI,EAAU;EAC7C,MAAMuB,GAAG,GAAG9B,cAAc,CAACO,IAAI,CAAC;EAChC,MAAMwB,IAAI,GAAG9B,mBAAmB,CAACM,IAAI,CAAC;EAEtC,IAAIqC,OAAO,GAAG,yBAAyB;EACvCA,OAAO,IAAId,GAAG,MAAAL,MAAA,CAAM5B,IAAI,CAACgD,QAAQ,CAACf,GAAG,CAAC,UAAO,mBAAmB;EAChEc,OAAO,kBAAAnB,MAAA,CAAkBM,IAAI,QAAAN,MAAA,CAAOM,IAAI,UAAM,cAAc,OAAI;EAEhE,MAAMe,eAAuB,GAAGvC,IAAI,GAAG+B,kBAAkB,CAAC/B,IAAI,CAAC,GAAG,EAAE;EACpEqC,OAAO,IAAIE,eAAe,sBAAArB,MAAA,CAAqBqB,eAAe,UAAM,4BAA4B;EAChGF,OAAO,IAAI,GAAG;EACd,OAAOA,OAAO;AAChB;AAEA,SAAShB,gBAAgBA,CAACpB,OAAiB,EAAQ;EACjD,KAAK,MAAMO,MAAM,IAAIP,OAAO,EAAE;IAC5BV,eAAe,CAACiB,MAAM,CAAC;EACzB;AACF;AAIA,SAASqB,eAAeA,CAAC5B,OAAiB,EAAEsB,GAAY,EAAiB;EAEvE,MAAMiB,KAAK,GAAGjB,GAAG,IAAIzB,WAAW,CAAC2C,IAAI,CAAClB,GAAG,CAAC;EAC1C,MAAMmB,SAAS,GAAGF,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC;EACnC,OAAOE,SAAS,GAAGC,qBAAqB,CAAC1C,OAAO,EAAEyC,SAAS,CAAC,GAAG,IAAI;AACrE;AAEA,SAASC,qBAAqBA,CAAC1C,OAAiB,EAAEyC,SAAiB,EAAiB;EAClFA,SAAS,GAAGA,SAAS,CAACE,WAAW,CAAC,CAAC;EAEnC,KAAK,MAAMpC,MAAM,IAAIP,OAAO,EAAE;IAC5B,KAAK,MAAM4C,eAAe,IAAIrC,MAAM,CAACsC,UAAU,EAAE;MAC/C,IAAID,eAAe,CAACD,WAAW,CAAC,CAAC,KAAKF,SAAS,EAAE;QAC/C,OAAOlC,MAAM;MACf;IACF;EACF;EACA,OAAO,IAAI;AACb;AAEA,SAASoB,oBAAoBA,CAAC3B,OAAO,EAAE0B,QAAQ,EAAE;EAC/C,KAAK,MAAMnB,MAAM,IAAIP,OAAO,EAAE;IAC5B,IAAIO,MAAM,CAACuC,SAAS,IAAIvC,MAAM,CAACuC,SAAS,CAACC,QAAQ,CAACrB,QAAQ,CAAC,EAAE;MAC3D,OAAOnB,MAAM;IACf;IAIA,IAAImB,QAAQ,sBAAAT,MAAA,CAAsBV,MAAM,CAACyC,EAAE,CAAE,EAAE;MAC7C,OAAOzC,MAAM;IACf;EACF;EACA,OAAO,IAAI;AACb;AAEA,SAASsB,wBAAwBA,CAAC7B,OAAO,EAAED,IAAI,EAAE;EAC/C,IAAI,CAACA,IAAI,EAAE;IACT,OAAO,IAAI;EACb;EAEA,KAAK,MAAMQ,MAAM,IAAIP,OAAO,EAAE;IAC5B,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;MAC5B,IAAIkD,mBAAmB,CAAClD,IAAI,EAAEQ,MAAM,CAAC,EAAE;QACrC,OAAOA,MAAM;MACf;IACF,CAAC,MAAM,IAAI2C,WAAW,CAACC,MAAM,CAACpD,IAAI,CAAC,EAAE;MAEnC,IAAIqD,qBAAqB,CAACrD,IAAI,CAACsD,MAAM,EAAEtD,IAAI,CAACuD,UAAU,EAAE/C,MAAM,CAAC,EAAE;QAC/D,OAAOA,MAAM;MACf;IACF,CAAC,MAAM,IAAIR,IAAI,YAAYmD,WAAW,EAAE;MACtC,MAAMI,UAAU,GAAG,CAAC;MACpB,IAAIF,qBAAqB,CAACrD,IAAI,EAAEuD,UAAU,EAAE/C,MAAM,CAAC,EAAE;QACnD,OAAOA,MAAM;MACf;IACF;EAEF;EACA,OAAO,IAAI;AACb;AAEA,SAAS0C,mBAAmBA,CAAClD,IAAI,EAAEQ,MAAM,EAAE;EACzC,IAAIA,MAAM,CAACgD,QAAQ,EAAE;IACnB,OAAOhD,MAAM,CAACgD,QAAQ,CAACxD,IAAI,CAAC;EAC9B;EAEA,MAAMyD,KAAK,GAAG1C,KAAK,CAACC,OAAO,CAACR,MAAM,CAACiD,KAAK,CAAC,GAAGjD,MAAM,CAACiD,KAAK,GAAG,CAACjD,MAAM,CAACiD,KAAK,CAAC;EACzE,OAAOA,KAAK,CAACC,IAAI,CAAEC,IAAI,IAAK3D,IAAI,CAAC4D,UAAU,CAACD,IAAI,CAAC,CAAC;AACpD;AAEA,SAASN,qBAAqBA,CAACrD,IAAI,EAAEuD,UAAU,EAAE/C,MAAM,EAAE;EACvD,MAAMiD,KAAK,GAAG1C,KAAK,CAACC,OAAO,CAACR,MAAM,CAACiD,KAAK,CAAC,GAAGjD,MAAM,CAACiD,KAAK,GAAG,CAACjD,MAAM,CAACiD,KAAK,CAAC;EACzE,OAAOA,KAAK,CAACC,IAAI,CAAEC,IAAI,IAAKE,UAAU,CAAC7D,IAAI,EAAEuD,UAAU,EAAE/C,MAAM,EAAEmD,IAAI,CAAC,CAAC;AACzE;AAEA,SAASE,UAAUA,CAAC7D,IAAI,EAAEuD,UAAU,EAAE/C,MAAM,EAAEmD,IAAI,EAAE;EAClD,IAAIA,IAAI,YAAYR,WAAW,EAAE;IAC/B,OAAO9D,mBAAmB,CAACsE,IAAI,EAAE3D,IAAI,EAAE2D,IAAI,CAACG,UAAU,CAAC;EACzD;EACA,QAAQ,OAAOH,IAAI;IACjB,KAAK,UAAU;MACb,OAAOA,IAAI,CAAC3D,IAAI,EAAEQ,MAAM,CAAC;IAE3B,KAAK,QAAQ;MAEX,MAAMuD,KAAK,GAAGC,cAAc,CAAChE,IAAI,EAAEuD,UAAU,EAAEI,IAAI,CAACxD,MAAM,CAAC;MAC3D,OAAOwD,IAAI,KAAKI,KAAK;IAEvB;MACE,OAAO,KAAK;EAChB;AACF;AAEA,SAAShC,kBAAkBA,CAAC/B,IAAI,EAAsB;EAAA,IAApBG,MAAc,GAAAD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAClD,IAAI,OAAOF,IAAI,KAAK,QAAQ,EAAE;IAC5B,OAAOA,IAAI,CAACW,KAAK,CAAC,CAAC,EAAER,MAAM,CAAC;EAC9B,CAAC,MAAM,IAAIgD,WAAW,CAACC,MAAM,CAACpD,IAAI,CAAC,EAAE;IAEnC,OAAOgE,cAAc,CAAChE,IAAI,CAACsD,MAAM,EAAEtD,IAAI,CAACuD,UAAU,EAAEpD,MAAM,CAAC;EAC7D,CAAC,MAAM,IAAIH,IAAI,YAAYmD,WAAW,EAAE;IACtC,MAAMI,UAAU,GAAG,CAAC;IACpB,OAAOS,cAAc,CAAChE,IAAI,EAAEuD,UAAU,EAAEpD,MAAM,CAAC;EACjD;EACA,OAAO,EAAE;AACX;AAEA,SAAS6D,cAAcA,CAACpD,WAAW,EAAE2C,UAAU,EAAEpD,MAAM,EAAE;EACvD,IAAIS,WAAW,CAACkD,UAAU,GAAGP,UAAU,GAAGpD,MAAM,EAAE;IAChD,OAAO,EAAE;EACX;EACA,MAAM8D,QAAQ,GAAG,IAAIC,QAAQ,CAACtD,WAAW,CAAC;EAC1C,IAAImD,KAAK,GAAG,EAAE;EACd,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhE,MAAM,EAAEgE,CAAC,EAAE,EAAE;IAC/BJ,KAAK,IAAIK,MAAM,CAACC,YAAY,CAACJ,QAAQ,CAACK,QAAQ,CAACf,UAAU,GAAGY,CAAC,CAAC,CAAC;EACjE;EACA,OAAOJ,KAAK;AACd"}