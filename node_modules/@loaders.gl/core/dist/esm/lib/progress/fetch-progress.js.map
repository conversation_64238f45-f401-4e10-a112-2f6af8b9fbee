{"version": 3, "file": "fetch-progress.js", "names": ["fetchProgress", "response", "onProgress", "onDone", "arguments", "length", "undefined", "onError", "ok", "body", "contentLength", "headers", "get", "totalBytes", "parseInt", "ReadableStream", "<PERSON><PERSON><PERSON><PERSON>", "progressStream", "start", "controller", "reader", "read", "Response", "loadedBytes", "done", "value", "close", "byteLength", "percent", "Math", "round", "enqueue", "error"], "sources": ["../../../../src/lib/progress/fetch-progress.ts"], "sourcesContent": ["// Forked from github AnthumChris/fetch-progress-indicators under MIT license\n\n/**\n * Intercepts the Response stream and creates a new Response\n */\nexport default async function fetchProgress(\n  response: Response | Promise<Response>,\n  onProgress: any, // TODO better callback types\n  onDone = () => {},\n  onError = () => {}\n) {\n  response = await response;\n  if (!response.ok) {\n    // ERROR checking needs to be done separately\n    return response;\n  }\n  const body = response.body;\n  if (!body) {\n    // 'ReadableStream not yet supported in this browser.\n    return response;\n  }\n  const contentLength = response.headers.get('content-length') || 0;\n  const totalBytes = contentLength ? parseInt(contentLength) : 0;\n  if (!(totalBytes > 0)) {\n    return response;\n  }\n  // Currently override only implemented in browser\n  if (typeof ReadableStream === 'undefined' || !body.getReader) {\n    return response;\n  }\n\n  // Create a new stream that invisbly wraps original stream\n  const progressStream = new ReadableStream({\n    async start(controller) {\n      const reader = body.getReader();\n      await read(controller, reader, 0, totalBytes, onProgress, onDone, onError);\n    }\n  });\n\n  return new Response(progressStream);\n}\n\n// Forward to original streams controller\n// TODO - this causes a crazy deep \"async stack\"... rewrite as async iterator?\n// eslint-disable-next-line max-params\nasync function read(controller, reader, loadedBytes, totalBytes, onProgress, onDone, onError) {\n  try {\n    const {done, value} = await reader.read();\n    if (done) {\n      onDone();\n      controller.close();\n      return;\n    }\n    loadedBytes += value.byteLength;\n    const percent = Math.round((loadedBytes / totalBytes) * 100);\n    onProgress(percent, {loadedBytes, totalBytes});\n    controller.enqueue(value);\n    await read(controller, reader, loadedBytes, totalBytes, onProgress, onDone, onError);\n  } catch (error) {\n    controller.error(error);\n    onError(error);\n  }\n}\n"], "mappings": "AAKA,eAAe,eAAeA,aAAaA,CACzCC,QAAsC,EACtCC,UAAe,EAGf;EAAA,IAFAC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,MAAM,CAAC,CAAC;EAAA,IACjBG,OAAO,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,MAAM,CAAC,CAAC;EAElBH,QAAQ,GAAG,MAAMA,QAAQ;EACzB,IAAI,CAACA,QAAQ,CAACO,EAAE,EAAE;IAEhB,OAAOP,QAAQ;EACjB;EACA,MAAMQ,IAAI,GAAGR,QAAQ,CAACQ,IAAI;EAC1B,IAAI,CAACA,IAAI,EAAE;IAET,OAAOR,QAAQ;EACjB;EACA,MAAMS,aAAa,GAAGT,QAAQ,CAACU,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC;EACjE,MAAMC,UAAU,GAAGH,aAAa,GAAGI,QAAQ,CAACJ,aAAa,CAAC,GAAG,CAAC;EAC9D,IAAI,EAAEG,UAAU,GAAG,CAAC,CAAC,EAAE;IACrB,OAAOZ,QAAQ;EACjB;EAEA,IAAI,OAAOc,cAAc,KAAK,WAAW,IAAI,CAACN,IAAI,CAACO,SAAS,EAAE;IAC5D,OAAOf,QAAQ;EACjB;EAGA,MAAMgB,cAAc,GAAG,IAAIF,cAAc,CAAC;IACxC,MAAMG,KAAKA,CAACC,UAAU,EAAE;MACtB,MAAMC,MAAM,GAAGX,IAAI,CAACO,SAAS,CAAC,CAAC;MAC/B,MAAMK,IAAI,CAACF,UAAU,EAAEC,MAAM,EAAE,CAAC,EAAEP,UAAU,EAAEX,UAAU,EAAEC,MAAM,EAAEI,OAAO,CAAC;IAC5E;EACF,CAAC,CAAC;EAEF,OAAO,IAAIe,QAAQ,CAACL,cAAc,CAAC;AACrC;AAKA,eAAeI,IAAIA,CAACF,UAAU,EAAEC,MAAM,EAAEG,WAAW,EAAEV,UAAU,EAAEX,UAAU,EAAEC,MAAM,EAAEI,OAAO,EAAE;EAC5F,IAAI;IACF,MAAM;MAACiB,IAAI;MAAEC;IAAK,CAAC,GAAG,MAAML,MAAM,CAACC,IAAI,CAAC,CAAC;IACzC,IAAIG,IAAI,EAAE;MACRrB,MAAM,CAAC,CAAC;MACRgB,UAAU,CAACO,KAAK,CAAC,CAAC;MAClB;IACF;IACAH,WAAW,IAAIE,KAAK,CAACE,UAAU;IAC/B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAEP,WAAW,GAAGV,UAAU,GAAI,GAAG,CAAC;IAC5DX,UAAU,CAAC0B,OAAO,EAAE;MAACL,WAAW;MAAEV;IAAU,CAAC,CAAC;IAC9CM,UAAU,CAACY,OAAO,CAACN,KAAK,CAAC;IACzB,MAAMJ,IAAI,CAACF,UAAU,EAAEC,MAAM,EAAEG,WAAW,EAAEV,UAAU,EAAEX,UAAU,EAAEC,MAAM,EAAEI,OAAO,CAAC;EACtF,CAAC,CAAC,OAAOyB,KAAK,EAAE;IACdb,UAAU,CAACa,KAAK,CAACA,KAAK,CAAC;IACvBzB,OAAO,CAACyB,KAAK,CAAC;EAChB;AACF"}