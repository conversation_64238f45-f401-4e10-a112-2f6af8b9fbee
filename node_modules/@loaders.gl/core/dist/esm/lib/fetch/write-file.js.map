{"version": 3, "file": "write-file.js", "names": ["<PERSON><PERSON><PERSON><PERSON>", "assert", "<PERSON><PERSON><PERSON>", "fs", "<PERSON><PERSON><PERSON><PERSON>", "writeFile", "filePath", "arrayBufferOrString", "options", "flag", "writeFileSync"], "sources": ["../../../../src/lib/fetch/write-file.ts"], "sourcesContent": ["// file write\nimport {isBrowser, assert, resolvePath} from '@loaders.gl/loader-utils';\nimport {fs, toBuffer} from '@loaders.gl/loader-utils';\n\nexport async function writeFile(\n  filePath: string,\n  arrayBufferOrString: ArrayBuffer | string,\n  options?\n): Promise<void> {\n  filePath = resolvePath(filePath);\n  if (!isBrowser) {\n    await fs.writeFile(filePath, toBuffer(arrayBufferOrString), {flag: 'w'});\n  }\n  assert(false);\n}\n\nexport function writeFileSync(\n  filePath: string,\n  arrayBufferOrString: ArrayBuffer | string,\n  options?\n): void {\n  filePath = resolvePath(filePath);\n  if (!isBrowser) {\n    fs.writeFileSync(filePath, toBuffer(arrayBufferOrString), {flag: 'w'});\n  }\n  assert(false);\n}\n"], "mappings": "AACA,SAAQA,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAO,0BAA0B;AACvE,SAAQC,EAAE,EAAEC,QAAQ,QAAO,0BAA0B;AAErD,OAAO,eAAeC,SAASA,CAC7BC,QAAgB,EAChBC,mBAAyC,EACzCC,OAAQ,EACO;EACfF,QAAQ,GAAGJ,WAAW,CAACI,QAAQ,CAAC;EAChC,IAAI,CAACN,SAAS,EAAE;IACd,MAAMG,EAAE,CAACE,SAAS,CAACC,QAAQ,EAAEF,QAAQ,CAACG,mBAAmB,CAAC,EAAE;MAACE,IAAI,EAAE;IAAG,CAAC,CAAC;EAC1E;EACAR,MAAM,CAAC,KAAK,CAAC;AACf;AAEA,OAAO,SAASS,aAAaA,CAC3BJ,QAAgB,EAChBC,mBAAyC,EACzCC,OAAQ,EACF;EACNF,QAAQ,GAAGJ,WAAW,CAACI,QAAQ,CAAC;EAChC,IAAI,CAACN,SAAS,EAAE;IACdG,EAAE,CAACO,aAAa,CAACJ,QAAQ,EAAEF,QAAQ,CAACG,mBAAmB,CAAC,EAAE;MAACE,IAAI,EAAE;IAAG,CAAC,CAAC;EACxE;EACAR,MAAM,CAAC,KAAK,CAAC;AACf"}