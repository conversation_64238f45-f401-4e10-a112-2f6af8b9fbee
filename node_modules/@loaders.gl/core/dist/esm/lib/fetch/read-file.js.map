{"version": 3, "file": "read-file.js", "names": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "fs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "assert", "readFileSync", "url", "options", "arguments", "length", "undefined", "buffer", "nothrow"], "sources": ["../../../../src/lib/fetch/read-file.ts"], "sourcesContent": ["// File read\nimport {isBrowser, resolveP<PERSON>, fs, toA<PERSON><PERSON><PERSON>uffer} from '@loaders.gl/loader-utils';\nimport {assert} from '@loaders.gl/loader-utils';\n\n// TODO - this is not tested\n// const isDataURL = (url) => url.startsWith('data:');\n\n/**\n * In a few cases (data URIs, node.js) \"files\" can be read synchronously\n */\nexport function readFileSync(url: string, options: object = {}) {\n  url = resolvePath(url);\n\n  // Only support this if we can also support sync data URL decoding in browser\n  // if (isDataURL(url)) {\n  //   return decodeDataUri(url);\n  // }\n\n  if (!isBrowser) {\n    const buffer = fs.readFileSync(url, options);\n    return typeof buffer !== 'string' ? toArrayBuffer(buffer) : buffer;\n  }\n\n  // @ts-ignore\n  if (!options.nothrow) {\n    // throw new Error('Cant load URI synchronously');\n    assert(false);\n  }\n\n  return null;\n}\n"], "mappings": "AACA,SAAQA,SAAS,EAAEC,WAAW,EAAEC,EAAE,EAAEC,aAAa,QAAO,0BAA0B;AAClF,SAAQC,MAAM,QAAO,0BAA0B;AAQ/C,OAAO,SAASC,YAAYA,CAACC,GAAW,EAAwB;EAAA,IAAtBC,OAAe,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC5DF,GAAG,GAAGL,WAAW,CAACK,GAAG,CAAC;EAOtB,IAAI,CAACN,SAAS,EAAE;IACd,MAAMW,MAAM,GAAGT,EAAE,CAACG,YAAY,CAACC,GAAG,EAAEC,OAAO,CAAC;IAC5C,OAAO,OAAOI,MAAM,KAAK,QAAQ,GAAGR,aAAa,CAACQ,MAAM,CAAC,GAAGA,MAAM;EACpE;EAGA,IAAI,CAACJ,OAAO,CAACK,OAAO,EAAE;IAEpBR,MAAM,CAAC,KAAK,CAAC;EACf;EAEA,OAAO,IAAI;AACb"}