{"version": 3, "file": "fetch-file.js", "names": ["<PERSON><PERSON><PERSON>", "makeResponse", "fetchFile", "url", "options", "fetchOptions", "fetch"], "sources": ["../../../../src/lib/fetch/fetch-file.ts"], "sourcesContent": ["import {resolvePath} from '@loaders.gl/loader-utils';\nimport {makeResponse} from '../utils/response-utils';\n// import {getErrorMessageFromResponse} from './fetch-error-message';\n\n/**\n * fetch compatible function\n * Reads file data from:\n * - http/http urls\n * - data urls\n * - File/Blob objects\n * Leverages `@loaders.gl/polyfills` for Node.js support\n * Respects pathPrefix and file aliases\n */\nexport async function fetchFile(\n  url: string | Blob,\n  options?: RequestInit & {fetch?: RequestInit | Function}\n): Promise<Response> {\n  if (typeof url === 'string') {\n    url = resolvePath(url);\n\n    let fetchOptions: RequestInit = options as RequestInit;\n    if (options?.fetch && typeof options?.fetch !== 'function') {\n      fetchOptions = options.fetch;\n    }\n\n    return await fetch(url, fetchOptions);\n  }\n\n  return await makeResponse(url);\n}\n"], "mappings": "AAAA,SAAQA,WAAW,QAAO,0BAA0B;AACpD,SAAQC,YAAY,QAAO,yBAAyB;AAYpD,OAAO,eAAeC,SAASA,CAC7BC,GAAkB,EAClBC,OAAwD,EACrC;EACnB,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;IAC3BA,GAAG,GAAGH,WAAW,CAACG,GAAG,CAAC;IAEtB,IAAIE,YAAyB,GAAGD,OAAsB;IACtD,IAAIA,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEE,KAAK,IAAI,QAAOF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEE,KAAK,MAAK,UAAU,EAAE;MAC1DD,YAAY,GAAGD,OAAO,CAACE,KAAK;IAC9B;IAEA,OAAO,MAAMA,KAAK,CAACH,GAAG,EAAEE,YAAY,CAAC;EACvC;EAEA,OAAO,MAAMJ,YAAY,CAACE,GAAG,CAAC;AAChC"}