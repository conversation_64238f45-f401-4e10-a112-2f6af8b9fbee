{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAGA,YAAY,EACV,MAAM,EACN,gBAAgB,EAChB,aAAa,EACb,aAAa,EACb,MAAM,EACN,aAAa,EACb,QAAQ,EACR,YAAY,EACZ,iBAAiB,EACjB,WAAW,EACX,2BAA2B,EAC5B,MAAM,0BAA0B,CAAC;AAGlC,OAAO,EAAC,SAAS,EAAC,MAAM,wBAAwB,CAAC;AAEjD,OAAO,EAAC,eAAe,EAAC,MAAM,+BAA+B,CAAC;AAC9D,OAAO,EAAC,YAAY,EAAC,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAC,SAAS,EAAE,aAAa,EAAC,MAAM,wBAAwB,CAAC;AAGhE,OAAO,EAAC,gBAAgB,EAAE,gBAAgB,EAAC,MAAM,0BAA0B,CAAC;AAC5E,OAAO,EAAC,eAAe,EAAC,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAC,YAAY,EAAE,gBAAgB,EAAC,MAAM,yBAAyB,CAAC;AAGvE,OAAO,EAAC,KAAK,EAAC,MAAM,iBAAiB,CAAC;AACtC,OAAO,EAAC,SAAS,EAAC,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAC,cAAc,EAAC,MAAM,4BAA4B,CAAC;AAE1D,OAAO,EAAC,IAAI,EAAC,MAAM,gBAAgB,CAAC;AACpC,OAAO,EAAC,aAAa,EAAC,MAAM,2BAA2B,CAAC;AAGxD,OAAO,EAAC,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,UAAU,EAAE,cAAc,EAAC,MAAM,kBAAkB,CAAC;AACjG,OAAO,EAAC,IAAI,EAAE,QAAQ,EAAC,MAAM,gBAAgB,CAAC;AAG9C,OAAO,EAAC,aAAa,EAAE,aAAa,EAAE,WAAW,EAAC,MAAM,0BAA0B,CAAC;AACnF,OAAO,EAAC,gBAAgB,EAAC,MAAM,0BAA0B,CAAC;AAG1D,OAAO,EAAC,YAAY,EAAC,MAAM,yCAAyC,CAAC;AACrE,OAAO,EAAC,UAAU,EAAC,MAAM,0CAA0C,CAAC;AAGpE,OAAO,EAAC,gBAAgB,EAAE,UAAU,EAAC,MAAM,eAAe,CAAC;AAC3D,OAAO,EAAC,UAAU,EAAC,MAAM,0BAA0B,CAAC;AAGpD,OAAO,EAAC,OAAO,IAAI,cAAc,EAAC,MAAM,+BAA+B,CAAC;AACxE,OAAO,EAAC,OAAO,IAAI,kBAAkB,EAAC,MAAM,sCAAsC,CAAC;AAGnF,OAAO,EAAC,kBAAkB,EAAC,MAAM,4BAA4B,CAAC;AAM9D,OAAO,EAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAC,MAAM,0BAA0B,CAAC;AAC7F,OAAO,EAAC,MAAM,EAAC,MAAM,0BAA0B,CAAC;AAChD,OAAO,EAAC,OAAO,EAAE,4BAA4B,EAAC,MAAM,0BAA0B,CAAC;AAE/E,OAAO,EACL,uBAAuB,EACvB,uBAAuB,EACvB,gBAAgB,EAChB,wBAAwB,EACzB,MAAM,0BAA0B,CAAC;AAGlC,OAAO,EACL,YAAY,EACZ,SAAS,EACT,UAAU,EACV,eAAe,EACf,UAAU,EACV,UAAU,EACV,gBAAgB,EAChB,gBAAgB,EACjB,MAAM,4BAA4B,CAAC"}