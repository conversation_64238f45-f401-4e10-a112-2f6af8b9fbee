(() => {
  var __defProp = Object.defineProperty;
  var __markAsModule = (target) => __defProp(target, "__esModule", { value: true });
  var __esm = (fn, res) => function __init() {
    return fn && (res = (0, fn[Object.keys(fn)[0]])(fn = 0)), res;
  };
  var __commonJS = (cb, mod) => function __require() {
    return mod || (0, cb[Object.keys(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
  };
  var __export = (target, all) => {
    __markAsModule(target);
    for (var name in all)
      __defProp(target, name, { get: all[name], enumerable: true });
  };

  // ../loader-utils/src/lib/env-utils/assert.ts
  function assert(condition, message) {
    if (!condition) {
      throw new Error(message || "loader assertion failed.");
    }
  }
  var init_assert = __esm({
    "../loader-utils/src/lib/env-utils/assert.ts"() {
    }
  });

  // ../loader-utils/src/lib/env-utils/globals.ts
  var globals, self_, window_, global_, document_, isBrowser, isWorker, matches, nodeVersion;
  var init_globals = __esm({
    "../loader-utils/src/lib/env-utils/globals.ts"() {
      globals = {
        self: typeof self !== "undefined" && self,
        window: typeof window !== "undefined" && window,
        global: typeof global !== "undefined" && global,
        document: typeof document !== "undefined" && document
      };
      self_ = globals.self || globals.window || globals.global || {};
      window_ = globals.window || globals.self || globals.global || {};
      global_ = globals.global || globals.self || globals.window || {};
      document_ = globals.document || {};
      isBrowser = Boolean(typeof process !== "object" || String(process) !== "[object process]" || process.browser);
      isWorker = typeof importScripts === "function";
      matches = typeof process !== "undefined" && process.version && /v([0-9]*)/.exec(process.version);
      nodeVersion = matches && parseFloat(matches[1]) || 0;
    }
  });

  // ../worker-utils/src/lib/env-utils/version.ts
  var DEFAULT_VERSION, VERSION;
  var init_version = __esm({
    "../worker-utils/src/lib/env-utils/version.ts"() {
      DEFAULT_VERSION = "latest";
      VERSION = typeof __VERSION__ !== "undefined" ? __VERSION__ : DEFAULT_VERSION;
      if (typeof __VERSION__ === "undefined") {
        console.error("loaders.gl: The __VERSION__ variable is not injected using babel plugin. Latest unstable workers would be fetched from the CDN.");
      }
    }
  });

  // ../worker-utils/src/lib/env-utils/assert.ts
  function assert2(condition, message) {
    if (!condition) {
      throw new Error(message || "loaders.gl assertion failed.");
    }
  }
  var init_assert2 = __esm({
    "../worker-utils/src/lib/env-utils/assert.ts"() {
    }
  });

  // ../worker-utils/src/lib/env-utils/globals.ts
  var globals2, self_2, window_2, global_2, document_2, isBrowser2, isMobile, matches2, nodeVersion2;
  var init_globals2 = __esm({
    "../worker-utils/src/lib/env-utils/globals.ts"() {
      globals2 = {
        self: typeof self !== "undefined" && self,
        window: typeof window !== "undefined" && window,
        global: typeof global !== "undefined" && global,
        document: typeof document !== "undefined" && document
      };
      self_2 = globals2.self || globals2.window || globals2.global || {};
      window_2 = globals2.window || globals2.self || globals2.global || {};
      global_2 = globals2.global || globals2.self || globals2.window || {};
      document_2 = globals2.document || {};
      isBrowser2 = typeof process !== "object" || String(process) !== "[object process]" || process.browser;
      isMobile = typeof window !== "undefined" && typeof window.orientation !== "undefined";
      matches2 = typeof process !== "undefined" && process.version && /v([0-9]*)/.exec(process.version);
      nodeVersion2 = matches2 && parseFloat(matches2[1]) || 0;
    }
  });

  // ../worker-utils/src/lib/worker-farm/worker-job.ts
  var WorkerJob;
  var init_worker_job = __esm({
    "../worker-utils/src/lib/worker-farm/worker-job.ts"() {
      init_assert2();
      WorkerJob = class {
        constructor(jobName, workerThread) {
          this.isRunning = true;
          this._resolve = () => {
          };
          this._reject = () => {
          };
          this.name = jobName;
          this.workerThread = workerThread;
          this.result = new Promise((resolve2, reject) => {
            this._resolve = resolve2;
            this._reject = reject;
          });
        }
        postMessage(type, payload) {
          this.workerThread.postMessage({
            source: "loaders.gl",
            type,
            payload
          });
        }
        done(value) {
          assert2(this.isRunning);
          this.isRunning = false;
          this._resolve(value);
        }
        error(error) {
          assert2(this.isRunning);
          this.isRunning = false;
          this._reject(error);
        }
      };
    }
  });

  // ../worker-utils/src/lib/node/worker_threads-browser.ts
  var Worker2;
  var init_worker_threads_browser = __esm({
    "../worker-utils/src/lib/node/worker_threads-browser.ts"() {
      Worker2 = class {
        terminate() {
        }
      };
    }
  });

  // ../worker-utils/src/lib/worker-utils/get-loadable-worker-url.ts
  function getLoadableWorkerURL(props) {
    assert2(props.source && !props.url || !props.source && props.url);
    let workerURL = workerURLCache.get(props.source || props.url);
    if (!workerURL) {
      if (props.url) {
        workerURL = getLoadableWorkerURLFromURL(props.url);
        workerURLCache.set(props.url, workerURL);
      }
      if (props.source) {
        workerURL = getLoadableWorkerURLFromSource(props.source);
        workerURLCache.set(props.source, workerURL);
      }
    }
    assert2(workerURL);
    return workerURL;
  }
  function getLoadableWorkerURLFromURL(url) {
    if (!url.startsWith("http")) {
      return url;
    }
    const workerSource = buildScriptSource(url);
    return getLoadableWorkerURLFromSource(workerSource);
  }
  function getLoadableWorkerURLFromSource(workerSource) {
    const blob = new Blob([workerSource], { type: "application/javascript" });
    return URL.createObjectURL(blob);
  }
  function buildScriptSource(workerUrl) {
    return `try {
  importScripts('${workerUrl}');
} catch (error) {
  console.error(error);
  throw error;
}`;
  }
  var workerURLCache;
  var init_get_loadable_worker_url = __esm({
    "../worker-utils/src/lib/worker-utils/get-loadable-worker-url.ts"() {
      init_assert2();
      workerURLCache = new Map();
    }
  });

  // ../worker-utils/src/lib/worker-utils/get-transfer-list.ts
  function getTransferList(object, recursive = true, transfers) {
    const transfersSet = transfers || new Set();
    if (!object) {
    } else if (isTransferable(object)) {
      transfersSet.add(object);
    } else if (isTransferable(object.buffer)) {
      transfersSet.add(object.buffer);
    } else if (ArrayBuffer.isView(object)) {
    } else if (recursive && typeof object === "object") {
      for (const key in object) {
        getTransferList(object[key], recursive, transfersSet);
      }
    }
    return transfers === void 0 ? Array.from(transfersSet) : [];
  }
  function isTransferable(object) {
    if (!object) {
      return false;
    }
    if (object instanceof ArrayBuffer) {
      return true;
    }
    if (typeof MessagePort !== "undefined" && object instanceof MessagePort) {
      return true;
    }
    if (typeof ImageBitmap !== "undefined" && object instanceof ImageBitmap) {
      return true;
    }
    if (typeof OffscreenCanvas !== "undefined" && object instanceof OffscreenCanvas) {
      return true;
    }
    return false;
  }
  function getTransferListForWriter(object) {
    if (object === null) {
      return {};
    }
    const clone = Object.assign({}, object);
    Object.keys(clone).forEach((key) => {
      if (typeof object[key] === "object" && !ArrayBuffer.isView(object[key]) && !(object[key] instanceof Array)) {
        clone[key] = getTransferListForWriter(object[key]);
      } else if (typeof clone[key] === "function" || clone[key] instanceof RegExp) {
        clone[key] = {};
      } else {
        clone[key] = object[key];
      }
    });
    return clone;
  }
  var init_get_transfer_list = __esm({
    "../worker-utils/src/lib/worker-utils/get-transfer-list.ts"() {
    }
  });

  // ../worker-utils/src/lib/worker-farm/worker-thread.ts
  var NOOP, WorkerThread;
  var init_worker_thread = __esm({
    "../worker-utils/src/lib/worker-farm/worker-thread.ts"() {
      init_worker_threads_browser();
      init_globals2();
      init_assert2();
      init_get_loadable_worker_url();
      init_get_transfer_list();
      NOOP = () => {
      };
      WorkerThread = class {
        constructor(props) {
          this.terminated = false;
          this._loadableURL = "";
          const { name, source, url } = props;
          assert2(source || url);
          this.name = name;
          this.source = source;
          this.url = url;
          this.onMessage = NOOP;
          this.onError = (error) => console.log(error);
          this.worker = isBrowser2 ? this._createBrowserWorker() : this._createNodeWorker();
        }
        static isSupported() {
          return typeof Worker !== "undefined" && isBrowser2 || typeof Worker2 !== "undefined" && !isBrowser2;
        }
        destroy() {
          this.onMessage = NOOP;
          this.onError = NOOP;
          this.worker.terminate();
          this.terminated = true;
        }
        get isRunning() {
          return Boolean(this.onMessage);
        }
        postMessage(data, transferList) {
          transferList = transferList || getTransferList(data);
          this.worker.postMessage(data, transferList);
        }
        _getErrorFromErrorEvent(event) {
          let message = "Failed to load ";
          message += `worker ${this.name} from ${this.url}. `;
          if (event.message) {
            message += `${event.message} in `;
          }
          if (event.lineno) {
            message += `:${event.lineno}:${event.colno}`;
          }
          return new Error(message);
        }
        _createBrowserWorker() {
          this._loadableURL = getLoadableWorkerURL({ source: this.source, url: this.url });
          const worker = new Worker(this._loadableURL, { name: this.name });
          worker.onmessage = (event) => {
            if (!event.data) {
              this.onError(new Error("No data received"));
            } else {
              this.onMessage(event.data);
            }
          };
          worker.onerror = (error) => {
            this.onError(this._getErrorFromErrorEvent(error));
            this.terminated = true;
          };
          worker.onmessageerror = (event) => console.error(event);
          return worker;
        }
        _createNodeWorker() {
          let worker;
          if (this.url) {
            const absolute = this.url.includes(":/") || this.url.startsWith("/");
            const url = absolute ? this.url : `./${this.url}`;
            worker = new Worker2(url, { eval: false });
          } else if (this.source) {
            worker = new Worker2(this.source, { eval: true });
          } else {
            throw new Error("no worker");
          }
          worker.on("message", (data) => {
            this.onMessage(data);
          });
          worker.on("error", (error) => {
            this.onError(error);
          });
          worker.on("exit", (code) => {
          });
          return worker;
        }
      };
    }
  });

  // ../worker-utils/src/lib/worker-farm/worker-pool.ts
  var WorkerPool;
  var init_worker_pool = __esm({
    "../worker-utils/src/lib/worker-farm/worker-pool.ts"() {
      init_globals2();
      init_worker_thread();
      init_worker_job();
      WorkerPool = class {
        constructor(props) {
          this.name = "unnamed";
          this.maxConcurrency = 1;
          this.maxMobileConcurrency = 1;
          this.onDebug = () => {
          };
          this.reuseWorkers = true;
          this.props = {};
          this.jobQueue = [];
          this.idleQueue = [];
          this.count = 0;
          this.isDestroyed = false;
          this.source = props.source;
          this.url = props.url;
          this.setProps(props);
        }
        static isSupported() {
          return WorkerThread.isSupported();
        }
        destroy() {
          this.idleQueue.forEach((worker) => worker.destroy());
          this.isDestroyed = true;
        }
        setProps(props) {
          this.props = { ...this.props, ...props };
          if (props.name !== void 0) {
            this.name = props.name;
          }
          if (props.maxConcurrency !== void 0) {
            this.maxConcurrency = props.maxConcurrency;
          }
          if (props.maxMobileConcurrency !== void 0) {
            this.maxMobileConcurrency = props.maxMobileConcurrency;
          }
          if (props.reuseWorkers !== void 0) {
            this.reuseWorkers = props.reuseWorkers;
          }
          if (props.onDebug !== void 0) {
            this.onDebug = props.onDebug;
          }
        }
        async startJob(name, onMessage3 = (job, type, data) => job.done(data), onError = (job, error) => job.error(error)) {
          const startPromise = new Promise((onStart) => {
            this.jobQueue.push({ name, onMessage: onMessage3, onError, onStart });
            return this;
          });
          this._startQueuedJob();
          return await startPromise;
        }
        async _startQueuedJob() {
          if (!this.jobQueue.length) {
            return;
          }
          const workerThread = this._getAvailableWorker();
          if (!workerThread) {
            return;
          }
          const queuedJob = this.jobQueue.shift();
          if (queuedJob) {
            this.onDebug({
              message: "Starting job",
              name: queuedJob.name,
              workerThread,
              backlog: this.jobQueue.length
            });
            const job = new WorkerJob(queuedJob.name, workerThread);
            workerThread.onMessage = (data) => queuedJob.onMessage(job, data.type, data.payload);
            workerThread.onError = (error) => queuedJob.onError(job, error);
            queuedJob.onStart(job);
            try {
              await job.result;
            } finally {
              this.returnWorkerToQueue(workerThread);
            }
          }
        }
        returnWorkerToQueue(worker) {
          const shouldDestroyWorker = this.isDestroyed || !this.reuseWorkers || this.count > this._getMaxConcurrency();
          if (shouldDestroyWorker) {
            worker.destroy();
            this.count--;
          } else {
            this.idleQueue.push(worker);
          }
          if (!this.isDestroyed) {
            this._startQueuedJob();
          }
        }
        _getAvailableWorker() {
          if (this.idleQueue.length > 0) {
            return this.idleQueue.shift() || null;
          }
          if (this.count < this._getMaxConcurrency()) {
            this.count++;
            const name = `${this.name.toLowerCase()} (#${this.count} of ${this.maxConcurrency})`;
            return new WorkerThread({ name, source: this.source, url: this.url });
          }
          return null;
        }
        _getMaxConcurrency() {
          return isMobile ? this.maxMobileConcurrency : this.maxConcurrency;
        }
      };
    }
  });

  // ../worker-utils/src/lib/worker-farm/worker-farm.ts
  var DEFAULT_PROPS, WorkerFarm;
  var init_worker_farm = __esm({
    "../worker-utils/src/lib/worker-farm/worker-farm.ts"() {
      init_worker_pool();
      init_worker_thread();
      DEFAULT_PROPS = {
        maxConcurrency: 3,
        maxMobileConcurrency: 1,
        reuseWorkers: true,
        onDebug: () => {
        }
      };
      WorkerFarm = class {
        constructor(props) {
          this.workerPools = new Map();
          this.props = { ...DEFAULT_PROPS };
          this.setProps(props);
          this.workerPools = new Map();
        }
        static isSupported() {
          return WorkerThread.isSupported();
        }
        static getWorkerFarm(props = {}) {
          WorkerFarm._workerFarm = WorkerFarm._workerFarm || new WorkerFarm({});
          WorkerFarm._workerFarm.setProps(props);
          return WorkerFarm._workerFarm;
        }
        destroy() {
          for (const workerPool of this.workerPools.values()) {
            workerPool.destroy();
          }
          this.workerPools = new Map();
        }
        setProps(props) {
          this.props = { ...this.props, ...props };
          for (const workerPool of this.workerPools.values()) {
            workerPool.setProps(this._getWorkerPoolProps());
          }
        }
        getWorkerPool(options) {
          const { name, source, url } = options;
          let workerPool = this.workerPools.get(name);
          if (!workerPool) {
            workerPool = new WorkerPool({
              name,
              source,
              url
            });
            workerPool.setProps(this._getWorkerPoolProps());
            this.workerPools.set(name, workerPool);
          }
          return workerPool;
        }
        _getWorkerPoolProps() {
          return {
            maxConcurrency: this.props.maxConcurrency,
            maxMobileConcurrency: this.props.maxMobileConcurrency,
            reuseWorkers: this.props.reuseWorkers,
            onDebug: this.props.onDebug
          };
        }
      };
    }
  });

  // ../worker-utils/src/lib/worker-api/get-worker-url.ts
  function getWorkerName(worker) {
    const warning = worker.version !== VERSION2 ? ` (worker-utils@${VERSION2})` : "";
    return `${worker.name}@${worker.version}${warning}`;
  }
  function getWorkerURL(worker, options = {}) {
    const workerOptions = options[worker.id] || {};
    const workerFile = `${worker.id}-worker.js`;
    let url = workerOptions.workerUrl;
    if (!url && worker.id === "compression") {
      url = options.workerUrl;
    }
    if (options._workerType === "test") {
      url = `modules/${worker.module}/dist/${workerFile}`;
    }
    if (!url) {
      let version = worker.version;
      if (version === "latest") {
        version = NPM_TAG;
      }
      const versionTag = version ? `@${version}` : "";
      url = `https://unpkg.com/@loaders.gl/${worker.module}${versionTag}/dist/${workerFile}`;
    }
    assert2(url);
    return url;
  }
  var NPM_TAG, VERSION2;
  var init_get_worker_url = __esm({
    "../worker-utils/src/lib/worker-api/get-worker-url.ts"() {
      init_assert2();
      init_version();
      NPM_TAG = "latest";
      VERSION2 = typeof VERSION !== "undefined" ? VERSION : NPM_TAG;
    }
  });

  // ../worker-utils/src/lib/worker-api/process-on-worker.ts
  async function processOnWorker(worker, data, options = {}, context = {}) {
    const name = getWorkerName(worker);
    const workerFarm = WorkerFarm.getWorkerFarm(options);
    const { source } = options;
    const workerPoolProps = { name, source };
    if (!source) {
      workerPoolProps.url = getWorkerURL(worker, options);
    }
    const workerPool = workerFarm.getWorkerPool(workerPoolProps);
    const jobName = options.jobName || worker.name;
    const job = await workerPool.startJob(jobName, onMessage.bind(null, context));
    const transferableOptions = getTransferListForWriter(options);
    job.postMessage("process", { input: data, options: transferableOptions });
    const result = await job.result;
    return result.result;
  }
  async function onMessage(context, job, type, payload) {
    switch (type) {
      case "done":
        job.done(payload);
        break;
      case "error":
        job.error(new Error(payload.error));
        break;
      case "process":
        const { id, input, options } = payload;
        try {
          if (!context.process) {
            job.postMessage("error", { id, error: "Worker not set up to process on main thread" });
            return;
          }
          const result = await context.process(input, options);
          job.postMessage("done", { id, result });
        } catch (error) {
          const message = error instanceof Error ? error.message : "unknown error";
          job.postMessage("error", { id, error: message });
        }
        break;
      default:
        console.warn(`process-on-worker: unknown message ${type}`);
    }
  }
  var init_process_on_worker = __esm({
    "../worker-utils/src/lib/worker-api/process-on-worker.ts"() {
      init_worker_farm();
      init_get_worker_url();
      init_get_transfer_list();
    }
  });

  // ../worker-utils/src/lib/worker-api/validate-worker-version.ts
  function validateWorkerVersion(worker, coreVersion = VERSION) {
    assert2(worker, "no worker provided");
    const workerVersion = worker.version;
    if (!coreVersion || !workerVersion) {
      return false;
    }
    return true;
  }
  var init_validate_worker_version = __esm({
    "../worker-utils/src/lib/worker-api/validate-worker-version.ts"() {
      init_assert2();
      init_version();
    }
  });

  // ../worker-utils/src/index.ts
  var init_src = __esm({
    "../worker-utils/src/index.ts"() {
      init_assert2();
      init_globals2();
      init_worker_farm();
      init_process_on_worker();
      init_get_worker_url();
      init_validate_worker_version();
    }
  });

  // ../loader-utils/src/lib/worker-loader-utils/parse-with-worker.ts
  function canParseWithWorker(loader, options) {
    if (!WorkerFarm.isSupported()) {
      return false;
    }
    if (!isBrowser2 && !options?._nodeWorkers) {
      return false;
    }
    return loader.worker && options?.worker;
  }
  async function parseWithWorker(loader, data, options, context, parseOnMainThread) {
    const name = loader.id;
    const url = getWorkerURL(loader, options);
    const workerFarm = WorkerFarm.getWorkerFarm(options);
    const workerPool = workerFarm.getWorkerPool({ name, url });
    options = JSON.parse(JSON.stringify(options));
    context = JSON.parse(JSON.stringify(context || {}));
    const job = await workerPool.startJob("process-on-worker", onMessage2.bind(null, parseOnMainThread));
    job.postMessage("process", {
      input: data,
      options,
      context
    });
    const result = await job.result;
    return await result.result;
  }
  async function onMessage2(parseOnMainThread, job, type, payload) {
    switch (type) {
      case "done":
        job.done(payload);
        break;
      case "error":
        job.error(new Error(payload.error));
        break;
      case "process":
        const { id, input, options } = payload;
        try {
          const result = await parseOnMainThread(input, options);
          job.postMessage("done", { id, result });
        } catch (error) {
          const message = error instanceof Error ? error.message : "unknown error";
          job.postMessage("error", { id, error: message });
        }
        break;
      default:
        console.warn(`parse-with-worker unknown message ${type}`);
    }
  }
  var init_parse_with_worker = __esm({
    "../loader-utils/src/lib/worker-loader-utils/parse-with-worker.ts"() {
      init_src();
      init_src();
    }
  });

  // ../loader-utils/src/lib/worker-loader-utils/encode-with-worker.ts
  function canEncodeWithWorker(writer, options) {
    if (!WorkerFarm.isSupported()) {
      return false;
    }
    if (!isBrowser && !options?._nodeWorkers) {
      return false;
    }
    return writer.worker && options?.worker;
  }
  var init_encode_with_worker = __esm({
    "../loader-utils/src/lib/worker-loader-utils/encode-with-worker.ts"() {
      init_src();
      init_globals();
    }
  });

  // ../loader-utils/src/lib/binary-utils/array-buffer-utils.ts
  function compareArrayBuffers(arrayBuffer1, arrayBuffer2, byteLength) {
    byteLength = byteLength || arrayBuffer1.byteLength;
    if (arrayBuffer1.byteLength < byteLength || arrayBuffer2.byteLength < byteLength) {
      return false;
    }
    const array1 = new Uint8Array(arrayBuffer1);
    const array2 = new Uint8Array(arrayBuffer2);
    for (let i = 0; i < array1.length; ++i) {
      if (array1[i] !== array2[i]) {
        return false;
      }
    }
    return true;
  }
  function concatenateArrayBuffers(...sources) {
    const sourceArrays = sources.map((source2) => source2 instanceof ArrayBuffer ? new Uint8Array(source2) : source2);
    const byteLength = sourceArrays.reduce((length, typedArray) => length + typedArray.byteLength, 0);
    const result = new Uint8Array(byteLength);
    let offset = 0;
    for (const sourceArray of sourceArrays) {
      result.set(sourceArray, offset);
      offset += sourceArray.byteLength;
    }
    return result.buffer;
  }
  var init_array_buffer_utils = __esm({
    "../loader-utils/src/lib/binary-utils/array-buffer-utils.ts"() {
    }
  });

  // ../loader-utils/src/lib/iterators/text-iterators.ts
  async function* makeTextDecoderIterator(arrayBufferIterator, options = {}) {
    const textDecoder = new TextDecoder(void 0, options);
    for await (const arrayBuffer of arrayBufferIterator) {
      yield typeof arrayBuffer === "string" ? arrayBuffer : textDecoder.decode(arrayBuffer, { stream: true });
    }
  }
  async function* makeTextEncoderIterator(textIterator) {
    const textEncoder = new TextEncoder();
    for await (const text of textIterator) {
      yield typeof text === "string" ? textEncoder.encode(text) : text;
    }
  }
  async function* makeLineIterator(textIterator) {
    let previous = "";
    for await (const textChunk of textIterator) {
      previous += textChunk;
      let eolIndex;
      while ((eolIndex = previous.indexOf("\n")) >= 0) {
        const line = previous.slice(0, eolIndex + 1);
        previous = previous.slice(eolIndex + 1);
        yield line;
      }
    }
    if (previous.length > 0) {
      yield previous;
    }
  }
  async function* makeNumberedLineIterator(lineIterator) {
    let counter = 1;
    for await (const line of lineIterator) {
      yield { counter, line };
      counter++;
    }
  }
  var init_text_iterators = __esm({
    "../loader-utils/src/lib/iterators/text-iterators.ts"() {
    }
  });

  // ../loader-utils/src/lib/iterators/async-iteration.ts
  async function forEach(iterator, visitor) {
    while (true) {
      const { done, value } = await iterator.next();
      if (done) {
        iterator.return();
        return;
      }
      const cancel = visitor(value);
      if (cancel) {
        return;
      }
    }
  }
  async function concatenateArrayBuffersAsync(asyncIterator) {
    const arrayBuffers = [];
    for await (const chunk of asyncIterator) {
      arrayBuffers.push(chunk);
    }
    return concatenateArrayBuffers(...arrayBuffers);
  }
  var init_async_iteration = __esm({
    "../loader-utils/src/lib/iterators/async-iteration.ts"() {
      init_array_buffer_utils();
    }
  });

  // ../../node_modules/@babel/runtime/helpers/esm/typeof.js
  function _typeof(obj) {
    "@babel/helpers - typeof";
    return _typeof = typeof Symbol == "function" && typeof Symbol.iterator == "symbol" ? function(obj2) {
      return typeof obj2;
    } : function(obj2) {
      return obj2 && typeof Symbol == "function" && obj2.constructor === Symbol && obj2 !== Symbol.prototype ? "symbol" : typeof obj2;
    }, _typeof(obj);
  }
  var init_typeof = __esm({
    "../../node_modules/@babel/runtime/helpers/esm/typeof.js"() {
    }
  });

  // ../../node_modules/@babel/runtime/helpers/esm/toPrimitive.js
  function _toPrimitive(input, hint) {
    if (_typeof(input) !== "object" || input === null)
      return input;
    var prim = input[Symbol.toPrimitive];
    if (prim !== void 0) {
      var res = prim.call(input, hint || "default");
      if (_typeof(res) !== "object")
        return res;
      throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return (hint === "string" ? String : Number)(input);
  }
  var init_toPrimitive = __esm({
    "../../node_modules/@babel/runtime/helpers/esm/toPrimitive.js"() {
      init_typeof();
    }
  });

  // ../../node_modules/@babel/runtime/helpers/esm/toPropertyKey.js
  function _toPropertyKey(arg) {
    var key = _toPrimitive(arg, "string");
    return _typeof(key) === "symbol" ? key : String(key);
  }
  var init_toPropertyKey = __esm({
    "../../node_modules/@babel/runtime/helpers/esm/toPropertyKey.js"() {
      init_typeof();
      init_toPrimitive();
    }
  });

  // ../../node_modules/@babel/runtime/helpers/esm/defineProperty.js
  function _defineProperty(obj, key, value) {
    key = _toPropertyKey(key);
    if (key in obj) {
      Object.defineProperty(obj, key, {
        value,
        enumerable: true,
        configurable: true,
        writable: true
      });
    } else {
      obj[key] = value;
    }
    return obj;
  }
  var init_defineProperty = __esm({
    "../../node_modules/@babel/runtime/helpers/esm/defineProperty.js"() {
      init_toPropertyKey();
    }
  });

  // ../../node_modules/@probe.gl/stats/dist/esm/utils/hi-res-timestamp.js
  function getHiResTimestamp() {
    let timestamp;
    if (typeof window !== "undefined" && window.performance) {
      timestamp = window.performance.now();
    } else if (typeof process !== "undefined" && process.hrtime) {
      const timeParts = process.hrtime();
      timestamp = timeParts[0] * 1e3 + timeParts[1] / 1e6;
    } else {
      timestamp = Date.now();
    }
    return timestamp;
  }
  var init_hi_res_timestamp = __esm({
    "../../node_modules/@probe.gl/stats/dist/esm/utils/hi-res-timestamp.js"() {
    }
  });

  // ../../node_modules/@probe.gl/stats/dist/esm/lib/stat.js
  var Stat;
  var init_stat = __esm({
    "../../node_modules/@probe.gl/stats/dist/esm/lib/stat.js"() {
      init_defineProperty();
      init_hi_res_timestamp();
      Stat = class {
        constructor(name, type) {
          _defineProperty(this, "name", void 0);
          _defineProperty(this, "type", void 0);
          _defineProperty(this, "sampleSize", 1);
          _defineProperty(this, "time", void 0);
          _defineProperty(this, "count", void 0);
          _defineProperty(this, "samples", void 0);
          _defineProperty(this, "lastTiming", void 0);
          _defineProperty(this, "lastSampleTime", void 0);
          _defineProperty(this, "lastSampleCount", void 0);
          _defineProperty(this, "_count", 0);
          _defineProperty(this, "_time", 0);
          _defineProperty(this, "_samples", 0);
          _defineProperty(this, "_startTime", 0);
          _defineProperty(this, "_timerPending", false);
          this.name = name;
          this.type = type;
          this.reset();
        }
        setSampleSize(samples) {
          this.sampleSize = samples;
          return this;
        }
        incrementCount() {
          this.addCount(1);
          return this;
        }
        decrementCount() {
          this.subtractCount(1);
          return this;
        }
        addCount(value) {
          this._count += value;
          this._samples++;
          this._checkSampling();
          return this;
        }
        subtractCount(value) {
          this._count -= value;
          this._samples++;
          this._checkSampling();
          return this;
        }
        addTime(time) {
          this._time += time;
          this.lastTiming = time;
          this._samples++;
          this._checkSampling();
          return this;
        }
        timeStart() {
          this._startTime = getHiResTimestamp();
          this._timerPending = true;
          return this;
        }
        timeEnd() {
          if (!this._timerPending) {
            return this;
          }
          this.addTime(getHiResTimestamp() - this._startTime);
          this._timerPending = false;
          this._checkSampling();
          return this;
        }
        getSampleAverageCount() {
          return this.sampleSize > 0 ? this.lastSampleCount / this.sampleSize : 0;
        }
        getSampleAverageTime() {
          return this.sampleSize > 0 ? this.lastSampleTime / this.sampleSize : 0;
        }
        getSampleHz() {
          return this.lastSampleTime > 0 ? this.sampleSize / (this.lastSampleTime / 1e3) : 0;
        }
        getAverageCount() {
          return this.samples > 0 ? this.count / this.samples : 0;
        }
        getAverageTime() {
          return this.samples > 0 ? this.time / this.samples : 0;
        }
        getHz() {
          return this.time > 0 ? this.samples / (this.time / 1e3) : 0;
        }
        reset() {
          this.time = 0;
          this.count = 0;
          this.samples = 0;
          this.lastTiming = 0;
          this.lastSampleTime = 0;
          this.lastSampleCount = 0;
          this._count = 0;
          this._time = 0;
          this._samples = 0;
          this._startTime = 0;
          this._timerPending = false;
          return this;
        }
        _checkSampling() {
          if (this._samples === this.sampleSize) {
            this.lastSampleTime = this._time;
            this.lastSampleCount = this._count;
            this.count += this._count;
            this.time += this._time;
            this.samples += this._samples;
            this._time = 0;
            this._count = 0;
            this._samples = 0;
          }
        }
      };
    }
  });

  // ../../node_modules/@probe.gl/stats/dist/esm/lib/stats.js
  var Stats;
  var init_stats = __esm({
    "../../node_modules/@probe.gl/stats/dist/esm/lib/stats.js"() {
      init_defineProperty();
      init_stat();
      Stats = class {
        constructor(options) {
          _defineProperty(this, "id", void 0);
          _defineProperty(this, "stats", {});
          this.id = options.id;
          this.stats = {};
          this._initializeStats(options.stats);
          Object.seal(this);
        }
        get(name) {
          let type = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "count";
          return this._getOrCreate({
            name,
            type
          });
        }
        get size() {
          return Object.keys(this.stats).length;
        }
        reset() {
          for (const key in this.stats) {
            this.stats[key].reset();
          }
          return this;
        }
        forEach(fn) {
          for (const key in this.stats) {
            fn(this.stats[key]);
          }
        }
        getTable() {
          const table = {};
          this.forEach((stat2) => {
            table[stat2.name] = {
              time: stat2.time || 0,
              count: stat2.count || 0,
              average: stat2.getAverageTime() || 0,
              hz: stat2.getHz() || 0
            };
          });
          return table;
        }
        _initializeStats() {
          let stats = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];
          stats.forEach((stat2) => this._getOrCreate(stat2));
        }
        _getOrCreate(stat2) {
          if (!stat2 || !stat2.name) {
            return null;
          }
          const {
            name,
            type
          } = stat2;
          if (!this.stats[name]) {
            if (stat2 instanceof Stat) {
              this.stats[name] = stat2;
            } else {
              this.stats[name] = new Stat(name, type);
            }
          }
          return this.stats[name];
        }
      };
    }
  });

  // ../../node_modules/@probe.gl/stats/dist/esm/index.js
  var init_esm = __esm({
    "../../node_modules/@probe.gl/stats/dist/esm/index.js"() {
      init_stats();
      init_stat();
      init_hi_res_timestamp();
    }
  });

  // ../loader-utils/src/lib/request-utils/request-scheduler.ts
  var STAT_QUEUED_REQUESTS, STAT_ACTIVE_REQUESTS, STAT_CANCELLED_REQUESTS, STAT_QUEUED_REQUESTS_EVER, STAT_ACTIVE_REQUESTS_EVER, DEFAULT_PROPS2, RequestScheduler;
  var init_request_scheduler = __esm({
    "../loader-utils/src/lib/request-utils/request-scheduler.ts"() {
      init_esm();
      STAT_QUEUED_REQUESTS = "Queued Requests";
      STAT_ACTIVE_REQUESTS = "Active Requests";
      STAT_CANCELLED_REQUESTS = "Cancelled Requests";
      STAT_QUEUED_REQUESTS_EVER = "Queued Requests Ever";
      STAT_ACTIVE_REQUESTS_EVER = "Active Requests Ever";
      DEFAULT_PROPS2 = {
        id: "request-scheduler",
        throttleRequests: true,
        maxRequests: 6
      };
      RequestScheduler = class {
        constructor(props = {}) {
          this.activeRequestCount = 0;
          this.requestQueue = [];
          this.requestMap = new Map();
          this.deferredUpdate = null;
          this.props = { ...DEFAULT_PROPS2, ...props };
          this.stats = new Stats({ id: this.props.id });
          this.stats.get(STAT_QUEUED_REQUESTS);
          this.stats.get(STAT_ACTIVE_REQUESTS);
          this.stats.get(STAT_CANCELLED_REQUESTS);
          this.stats.get(STAT_QUEUED_REQUESTS_EVER);
          this.stats.get(STAT_ACTIVE_REQUESTS_EVER);
        }
        scheduleRequest(handle, getPriority = () => 0) {
          if (!this.props.throttleRequests) {
            return Promise.resolve({ done: () => {
            } });
          }
          if (this.requestMap.has(handle)) {
            return this.requestMap.get(handle);
          }
          const request = { handle, priority: 0, getPriority };
          const promise = new Promise((resolve2) => {
            request.resolve = resolve2;
            return request;
          });
          this.requestQueue.push(request);
          this.requestMap.set(handle, promise);
          this._issueNewRequests();
          return promise;
        }
        _issueRequest(request) {
          const { handle, resolve: resolve2 } = request;
          let isDone = false;
          const done = () => {
            if (!isDone) {
              isDone = true;
              this.requestMap.delete(handle);
              this.activeRequestCount--;
              this._issueNewRequests();
            }
          };
          this.activeRequestCount++;
          return resolve2 ? resolve2({ done }) : Promise.resolve({ done });
        }
        _issueNewRequests() {
          if (!this.deferredUpdate) {
            this.deferredUpdate = setTimeout(() => this._issueNewRequestsAsync(), 0);
          }
        }
        _issueNewRequestsAsync() {
          this.deferredUpdate = null;
          const freeSlots = Math.max(this.props.maxRequests - this.activeRequestCount, 0);
          if (freeSlots === 0) {
            return;
          }
          this._updateAllRequests();
          for (let i = 0; i < freeSlots; ++i) {
            const request = this.requestQueue.shift();
            if (request) {
              this._issueRequest(request);
            }
          }
        }
        _updateAllRequests() {
          const requestQueue = this.requestQueue;
          for (let i = 0; i < requestQueue.length; ++i) {
            const request = requestQueue[i];
            if (!this._updateRequest(request)) {
              requestQueue.splice(i, 1);
              this.requestMap.delete(request.handle);
              i--;
            }
          }
          requestQueue.sort((a, b) => a.priority - b.priority);
        }
        _updateRequest(request) {
          request.priority = request.getPriority(request.handle);
          if (request.priority < 0) {
            request.resolve(null);
            return false;
          }
          return true;
        }
      };
    }
  });

  // ../loader-utils/src/lib/path-utils/file-aliases.ts
  function setPathPrefix(prefix) {
    pathPrefix = prefix;
  }
  function getPathPrefix() {
    return pathPrefix;
  }
  function resolvePath(filename2) {
    for (const alias in fileAliases) {
      if (filename2.startsWith(alias)) {
        const replacement = fileAliases[alias];
        filename2 = filename2.replace(alias, replacement);
      }
    }
    if (!filename2.startsWith("http://") && !filename2.startsWith("https://")) {
      filename2 = `${pathPrefix}${filename2}`;
    }
    return filename2;
  }
  var pathPrefix, fileAliases;
  var init_file_aliases = __esm({
    "../loader-utils/src/lib/path-utils/file-aliases.ts"() {
      pathPrefix = "";
      fileAliases = {};
    }
  });

  // ../loader-utils/src/json-loader.ts
  function parseTextSync(text) {
    return JSON.parse(text);
  }
  var VERSION3, JSONLoader;
  var init_json_loader = __esm({
    "../loader-utils/src/json-loader.ts"() {
      VERSION3 = typeof __VERSION__ !== "undefined" ? __VERSION__ : "latest";
      JSONLoader = {
        name: "JSON",
        id: "json",
        module: "json",
        version: VERSION3,
        extensions: ["json", "geojson"],
        mimeTypes: ["application/json"],
        category: "json",
        text: true,
        parseTextSync,
        parse: async (arrayBuffer) => parseTextSync(new TextDecoder().decode(arrayBuffer)),
        options: {}
      };
    }
  });

  // ../loader-utils/src/lib/node/buffer.browser.ts
  function toArrayBuffer(buffer) {
    return buffer;
  }
  function toBuffer(binaryData) {
    throw new Error("Buffer not supported in browser");
  }
  var init_buffer_browser = __esm({
    "../loader-utils/src/lib/node/buffer.browser.ts"() {
    }
  });

  // ../loader-utils/src/lib/binary-utils/memory-conversion-utils.ts
  function isBuffer(value) {
    return value && typeof value === "object" && value.isBuffer;
  }
  function toBuffer2(data) {
    return toBuffer ? toBuffer(data) : data;
  }
  function toArrayBuffer2(data) {
    if (isBuffer(data)) {
      return toArrayBuffer(data);
    }
    if (data instanceof ArrayBuffer) {
      return data;
    }
    if (ArrayBuffer.isView(data)) {
      if (data.byteOffset === 0 && data.byteLength === data.buffer.byteLength) {
        return data.buffer;
      }
      return data.buffer.slice(data.byteOffset, data.byteOffset + data.byteLength);
    }
    if (typeof data === "string") {
      const text = data;
      const uint8Array = new TextEncoder().encode(text);
      return uint8Array.buffer;
    }
    if (data && typeof data === "object" && data._toArrayBuffer) {
      return data._toArrayBuffer();
    }
    throw new Error("toArrayBuffer");
  }
  var init_memory_conversion_utils = __esm({
    "../loader-utils/src/lib/binary-utils/memory-conversion-utils.ts"() {
      init_buffer_browser();
    }
  });

  // ../loader-utils/src/lib/path-utils/get-cwd.ts
  function getCWD() {
    if (typeof process !== "undefined" && typeof process.cwd !== "undefined") {
      return process.cwd();
    }
    const pathname = window.location?.pathname;
    return pathname?.slice(0, pathname.lastIndexOf("/") + 1) || "";
  }
  var init_get_cwd = __esm({
    "../loader-utils/src/lib/path-utils/get-cwd.ts"() {
    }
  });

  // ../loader-utils/src/lib/path-utils/path.ts
  var path_exports = {};
  __export(path_exports, {
    dirname: () => dirname,
    filename: () => filename,
    join: () => join,
    resolve: () => resolve
  });
  function filename(url) {
    const slashIndex = url ? url.lastIndexOf("/") : -1;
    return slashIndex >= 0 ? url.substr(slashIndex + 1) : "";
  }
  function dirname(url) {
    const slashIndex = url ? url.lastIndexOf("/") : -1;
    return slashIndex >= 0 ? url.substr(0, slashIndex) : "";
  }
  function join(...parts) {
    const separator = "/";
    parts = parts.map((part, index) => {
      if (index) {
        part = part.replace(new RegExp(`^${separator}`), "");
      }
      if (index !== parts.length - 1) {
        part = part.replace(new RegExp(`${separator}$`), "");
      }
      return part;
    });
    return parts.join(separator);
  }
  function resolve(...components) {
    const paths = [];
    for (let _i = 0; _i < components.length; _i++) {
      paths[_i] = components[_i];
    }
    let resolvedPath = "";
    let resolvedAbsolute = false;
    let cwd;
    for (let i = paths.length - 1; i >= -1 && !resolvedAbsolute; i--) {
      let path;
      if (i >= 0) {
        path = paths[i];
      } else {
        if (cwd === void 0) {
          cwd = getCWD();
        }
        path = cwd;
      }
      if (path.length === 0) {
        continue;
      }
      resolvedPath = `${path}/${resolvedPath}`;
      resolvedAbsolute = path.charCodeAt(0) === SLASH;
    }
    resolvedPath = normalizeStringPosix(resolvedPath, !resolvedAbsolute);
    if (resolvedAbsolute) {
      return `/${resolvedPath}`;
    } else if (resolvedPath.length > 0) {
      return resolvedPath;
    }
    return ".";
  }
  function normalizeStringPosix(path, allowAboveRoot) {
    let res = "";
    let lastSlash = -1;
    let dots = 0;
    let code;
    let isAboveRoot = false;
    for (let i = 0; i <= path.length; ++i) {
      if (i < path.length) {
        code = path.charCodeAt(i);
      } else if (code === SLASH) {
        break;
      } else {
        code = SLASH;
      }
      if (code === SLASH) {
        if (lastSlash === i - 1 || dots === 1) {
        } else if (lastSlash !== i - 1 && dots === 2) {
          if (res.length < 2 || !isAboveRoot || res.charCodeAt(res.length - 1) !== DOT || res.charCodeAt(res.length - 2) !== DOT) {
            if (res.length > 2) {
              const start = res.length - 1;
              let j = start;
              for (; j >= 0; --j) {
                if (res.charCodeAt(j) === SLASH) {
                  break;
                }
              }
              if (j !== start) {
                res = j === -1 ? "" : res.slice(0, j);
                lastSlash = i;
                dots = 0;
                isAboveRoot = false;
                continue;
              }
            } else if (res.length === 2 || res.length === 1) {
              res = "";
              lastSlash = i;
              dots = 0;
              isAboveRoot = false;
              continue;
            }
          }
          if (allowAboveRoot) {
            if (res.length > 0) {
              res += "/..";
            } else {
              res = "..";
            }
            isAboveRoot = true;
          }
        } else {
          const slice = path.slice(lastSlash + 1, i);
          if (res.length > 0) {
            res += `/${slice}`;
          } else {
            res = slice;
          }
          isAboveRoot = false;
        }
        lastSlash = i;
        dots = 0;
      } else if (code === DOT && dots !== -1) {
        ++dots;
      } else {
        dots = -1;
      }
    }
    return res;
  }
  var SLASH, DOT;
  var init_path = __esm({
    "../loader-utils/src/lib/path-utils/path.ts"() {
      init_get_cwd();
      SLASH = 47;
      DOT = 46;
    }
  });

  // ../loader-utils/src/lib/node/fs.browser.ts
  var fs_browser_exports = {};
  __export(fs_browser_exports, {
    _readToArrayBuffer: () => _readToArrayBuffer,
    close: () => close,
    createWriteStream: () => createWriteStream,
    fstat: () => fstat,
    isSupported: () => isSupported,
    open: () => open,
    read: () => read,
    readFile: () => readFile,
    readFileSync: () => readFileSync,
    readdir: () => readdir,
    stat: () => stat,
    writeFile: () => writeFile,
    writeFileSync: () => writeFileSync
  });
  var readdir, stat, readFile, readFileSync, writeFile, writeFileSync, open, close, read, fstat, createWriteStream, _readToArrayBuffer, isSupported;
  var init_fs_browser = __esm({
    "../loader-utils/src/lib/node/fs.browser.ts"() {
      readdir = null;
      stat = null;
      readFile = null;
      readFileSync = null;
      writeFile = null;
      writeFileSync = null;
      open = null;
      close = null;
      read = null;
      fstat = null;
      createWriteStream = null;
      _readToArrayBuffer = null;
      isSupported = false;
    }
  });

  // ../loader-utils/src/index.ts
  var init_src2 = __esm({
    "../loader-utils/src/index.ts"() {
      init_assert();
      init_globals();
      init_parse_with_worker();
      init_encode_with_worker();
      init_array_buffer_utils();
      init_text_iterators();
      init_async_iteration();
      init_request_scheduler();
      init_file_aliases();
      init_json_loader();
      init_memory_conversion_utils();
      init_path();
      init_fs_browser();
    }
  });

  // src/javascript-utils/is-type.ts
  var isBoolean, isFunction, isObject, isPureObject, isPromise, isIterable, isAsyncIterable, isIterator, isResponse, isBlob, isBuffer2, isWritableDOMStream, isReadableDOMStream, isWritableNodeStream, isReadableNodeStream, isReadableStream, isWritableStream;
  var init_is_type = __esm({
    "src/javascript-utils/is-type.ts"() {
      isBoolean = (x) => typeof x === "boolean";
      isFunction = (x) => typeof x === "function";
      isObject = (x) => x !== null && typeof x === "object";
      isPureObject = (x) => isObject(x) && x.constructor === {}.constructor;
      isPromise = (x) => isObject(x) && isFunction(x.then);
      isIterable = (x) => x && typeof x[Symbol.iterator] === "function";
      isAsyncIterable = (x) => x && typeof x[Symbol.asyncIterator] === "function";
      isIterator = (x) => x && isFunction(x.next);
      isResponse = (x) => typeof Response !== "undefined" && x instanceof Response || x && x.arrayBuffer && x.text && x.json;
      isBlob = (x) => typeof Blob !== "undefined" && x instanceof Blob;
      isBuffer2 = (x) => x && typeof x === "object" && x.isBuffer;
      isWritableDOMStream = (x) => isObject(x) && isFunction(x.abort) && isFunction(x.getWriter);
      isReadableDOMStream = (x) => typeof ReadableStream !== "undefined" && x instanceof ReadableStream || isObject(x) && isFunction(x.tee) && isFunction(x.cancel) && isFunction(x.getReader);
      isWritableNodeStream = (x) => isObject(x) && isFunction(x.end) && isFunction(x.write) && isBoolean(x.writable);
      isReadableNodeStream = (x) => isObject(x) && isFunction(x.read) && isFunction(x.pipe) && isBoolean(x.readable);
      isReadableStream = (x) => isReadableDOMStream(x) || isReadableNodeStream(x);
      isWritableStream = (x) => isWritableDOMStream(x) || isWritableNodeStream(x);
    }
  });

  // src/lib/utils/mime-type-utils.ts
  function parseMIMEType(mimeString) {
    const matches3 = MIME_TYPE_PATTERN.exec(mimeString);
    if (matches3) {
      return matches3[1];
    }
    return mimeString;
  }
  function parseMIMETypeFromURL(url) {
    const matches3 = DATA_URL_PATTERN.exec(url);
    if (matches3) {
      return matches3[1];
    }
    return "";
  }
  var DATA_URL_PATTERN, MIME_TYPE_PATTERN;
  var init_mime_type_utils = __esm({
    "src/lib/utils/mime-type-utils.ts"() {
      DATA_URL_PATTERN = /^data:([-\w.]+\/[-\w.+]+)(;|,)/;
      MIME_TYPE_PATTERN = /^([-\w.]+\/[-\w.+]+)/;
    }
  });

  // src/lib/utils/url-utils.ts
  function extractQueryString(url) {
    const matches3 = url.match(QUERY_STRING_PATTERN);
    return matches3 && matches3[0];
  }
  function stripQueryString(url) {
    return url.replace(QUERY_STRING_PATTERN, "");
  }
  var QUERY_STRING_PATTERN;
  var init_url_utils = __esm({
    "src/lib/utils/url-utils.ts"() {
      QUERY_STRING_PATTERN = /\?.*/;
    }
  });

  // src/lib/utils/resource-utils.ts
  function getResourceUrl(resource) {
    if (isResponse(resource)) {
      const response = resource;
      return response.url;
    }
    if (isBlob(resource)) {
      const blob = resource;
      return blob.name || "";
    }
    if (typeof resource === "string") {
      return resource;
    }
    return "";
  }
  function getResourceMIMEType(resource) {
    if (isResponse(resource)) {
      const response = resource;
      const contentTypeHeader = response.headers.get("content-type") || "";
      const noQueryUrl = stripQueryString(response.url);
      return parseMIMEType(contentTypeHeader) || parseMIMETypeFromURL(noQueryUrl);
    }
    if (isBlob(resource)) {
      const blob = resource;
      return blob.type || "";
    }
    if (typeof resource === "string") {
      return parseMIMETypeFromURL(resource);
    }
    return "";
  }
  function getResourceContentLength(resource) {
    if (isResponse(resource)) {
      const response = resource;
      return response.headers["content-length"] || -1;
    }
    if (isBlob(resource)) {
      const blob = resource;
      return blob.size;
    }
    if (typeof resource === "string") {
      return resource.length;
    }
    if (resource instanceof ArrayBuffer) {
      return resource.byteLength;
    }
    if (ArrayBuffer.isView(resource)) {
      return resource.byteLength;
    }
    return -1;
  }
  var init_resource_utils = __esm({
    "src/lib/utils/resource-utils.ts"() {
      init_is_type();
      init_mime_type_utils();
      init_url_utils();
    }
  });

  // src/lib/utils/response-utils.ts
  async function makeResponse(resource) {
    if (isResponse(resource)) {
      return resource;
    }
    const headers = {};
    const contentLength = getResourceContentLength(resource);
    if (contentLength >= 0) {
      headers["content-length"] = String(contentLength);
    }
    const url = getResourceUrl(resource);
    const type = getResourceMIMEType(resource);
    if (type) {
      headers["content-type"] = type;
    }
    const initialDataUrl = await getInitialDataUrl(resource);
    if (initialDataUrl) {
      headers["x-first-bytes"] = initialDataUrl;
    }
    if (typeof resource === "string") {
      resource = new TextEncoder().encode(resource);
    }
    const response = new Response(resource, { headers });
    Object.defineProperty(response, "url", { value: url });
    return response;
  }
  async function checkResponse(response) {
    if (!response.ok) {
      const message = await getResponseError(response);
      throw new Error(message);
    }
  }
  async function getResponseError(response) {
    let message = `Failed to fetch resource ${response.url} (${response.status}): `;
    try {
      const contentType = response.headers.get("Content-Type");
      let text = response.statusText;
      if (contentType.includes("application/json")) {
        text += ` ${await response.text()}`;
      }
      message += text;
      message = message.length > 60 ? `${message.slice(0, 60)}...` : message;
    } catch (error) {
    }
    return message;
  }
  async function getInitialDataUrl(resource) {
    const INITIAL_DATA_LENGTH = 5;
    if (typeof resource === "string") {
      return `data:,${resource.slice(0, INITIAL_DATA_LENGTH)}`;
    }
    if (resource instanceof Blob) {
      const blobSlice = resource.slice(0, 5);
      return await new Promise((resolve2) => {
        const reader = new FileReader();
        reader.onload = (event) => resolve2(event?.target?.result);
        reader.readAsDataURL(blobSlice);
      });
    }
    if (resource instanceof ArrayBuffer) {
      const slice = resource.slice(0, INITIAL_DATA_LENGTH);
      const base64 = arrayBufferToBase64(slice);
      return `data:base64,${base64}`;
    }
    return null;
  }
  function arrayBufferToBase64(buffer) {
    let binary = "";
    const bytes = new Uint8Array(buffer);
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }
  var init_response_utils = __esm({
    "src/lib/utils/response-utils.ts"() {
      init_is_type();
      init_resource_utils();
    }
  });

  // src/lib/fetch/fetch-file.ts
  async function fetchFile(url, options) {
    if (typeof url === "string") {
      url = resolvePath(url);
      let fetchOptions = options;
      if (options?.fetch && typeof options?.fetch !== "function") {
        fetchOptions = options.fetch;
      }
      return await fetch(url, fetchOptions);
    }
    return await makeResponse(url);
  }
  var init_fetch_file = __esm({
    "src/lib/fetch/fetch-file.ts"() {
      init_src2();
      init_response_utils();
    }
  });

  // src/lib/fetch/read-array-buffer.ts
  async function readArrayBuffer(file, start, length) {
    if (typeof file === "number") {
      return await fs_browser_exports._readToArrayBuffer(file, start, length);
    }
    if (!(file instanceof Blob)) {
      file = new Blob([file]);
    }
    const slice = file.slice(start, start + length);
    return await readBlob(slice);
  }
  async function readBlob(blob) {
    return await new Promise((resolve2, reject) => {
      const fileReader = new FileReader();
      fileReader.onload = (event) => resolve2(event?.target?.result);
      fileReader.onerror = (error) => reject(error);
      fileReader.readAsArrayBuffer(blob);
    });
  }
  var init_read_array_buffer = __esm({
    "src/lib/fetch/read-array-buffer.ts"() {
      init_src2();
    }
  });

  // src/lib/fetch/read-file.ts
  function readFileSync2(url, options = {}) {
    url = resolvePath(url);
    if (!isBrowser) {
      const buffer = fs_browser_exports.readFileSync(url, options);
      return typeof buffer !== "string" ? toArrayBuffer2(buffer) : buffer;
    }
    if (!options.nothrow) {
      assert(false);
    }
    return null;
  }
  var init_read_file = __esm({
    "src/lib/fetch/read-file.ts"() {
      init_src2();
      init_src2();
    }
  });

  // src/lib/fetch/write-file.ts
  async function writeFile2(filePath, arrayBufferOrString, options) {
    filePath = resolvePath(filePath);
    if (!isBrowser) {
      await fs_browser_exports.writeFile(filePath, toBuffer2(arrayBufferOrString), { flag: "w" });
    }
    assert(false);
  }
  function writeFileSync2(filePath, arrayBufferOrString, options) {
    filePath = resolvePath(filePath);
    if (!isBrowser) {
      fs_browser_exports.writeFileSync(filePath, toBuffer2(arrayBufferOrString), { flag: "w" });
    }
    assert(false);
  }
  var init_write_file = __esm({
    "src/lib/fetch/write-file.ts"() {
      init_src2();
      init_src2();
    }
  });

  // ../../node_modules/@probe.gl/env/dist/esm/lib/is-electron.js
  function isElectron(mockUserAgent) {
    if (typeof window !== "undefined" && typeof window.process === "object" && window.process.type === "renderer") {
      return true;
    }
    if (typeof process !== "undefined" && typeof process.versions === "object" && Boolean(process.versions["electron"])) {
      return true;
    }
    const realUserAgent = typeof navigator === "object" && typeof navigator.userAgent === "string" && navigator.userAgent;
    const userAgent = mockUserAgent || realUserAgent;
    if (userAgent && userAgent.indexOf("Electron") >= 0) {
      return true;
    }
    return false;
  }
  var init_is_electron = __esm({
    "../../node_modules/@probe.gl/env/dist/esm/lib/is-electron.js"() {
    }
  });

  // ../../node_modules/@probe.gl/env/dist/esm/lib/is-browser.js
  function isBrowser3() {
    const isNode = typeof process === "object" && String(process) === "[object process]" && !process.browser;
    return !isNode || isElectron();
  }
  var init_is_browser = __esm({
    "../../node_modules/@probe.gl/env/dist/esm/lib/is-browser.js"() {
      init_is_electron();
    }
  });

  // ../../node_modules/@probe.gl/env/dist/esm/lib/globals.js
  var globals3, self_3, window_3, document_3, process_;
  var init_globals3 = __esm({
    "../../node_modules/@probe.gl/env/dist/esm/lib/globals.js"() {
      globals3 = {
        self: typeof self !== "undefined" && self,
        window: typeof window !== "undefined" && window,
        global: typeof global !== "undefined" && global,
        document: typeof document !== "undefined" && document,
        process: typeof process === "object" && process
      };
      self_3 = globals3.self || globals3.window || globals3.global;
      window_3 = globals3.window || globals3.self || globals3.global;
      document_3 = globals3.document || {};
      process_ = globals3.process || {};
    }
  });

  // ../../node_modules/@probe.gl/env/dist/esm/utils/globals.js
  var VERSION4, isBrowser4;
  var init_globals4 = __esm({
    "../../node_modules/@probe.gl/env/dist/esm/utils/globals.js"() {
      init_is_browser();
      VERSION4 = typeof __VERSION__ !== "undefined" ? __VERSION__ : "untranspiled source";
      isBrowser4 = isBrowser3();
    }
  });

  // ../../node_modules/@probe.gl/env/dist/esm/index.js
  var init_esm2 = __esm({
    "../../node_modules/@probe.gl/env/dist/esm/index.js"() {
      init_globals4();
      init_globals3();
      init_is_browser();
    }
  });

  // ../../node_modules/@probe.gl/log/dist/esm/utils/local-storage.js
  function getStorage(type) {
    try {
      const storage = window[type];
      const x = "__storage_test__";
      storage.setItem(x, x);
      storage.removeItem(x);
      return storage;
    } catch (e) {
      return null;
    }
  }
  var LocalStorage;
  var init_local_storage = __esm({
    "../../node_modules/@probe.gl/log/dist/esm/utils/local-storage.js"() {
      init_defineProperty();
      LocalStorage = class {
        constructor(id, defaultConfig) {
          let type = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : "sessionStorage";
          _defineProperty(this, "storage", void 0);
          _defineProperty(this, "id", void 0);
          _defineProperty(this, "config", void 0);
          this.storage = getStorage(type);
          this.id = id;
          this.config = defaultConfig;
          this._loadConfiguration();
        }
        getConfiguration() {
          return this.config;
        }
        setConfiguration(configuration) {
          Object.assign(this.config, configuration);
          if (this.storage) {
            const serialized = JSON.stringify(this.config);
            this.storage.setItem(this.id, serialized);
          }
        }
        _loadConfiguration() {
          let configuration = {};
          if (this.storage) {
            const serializedConfiguration = this.storage.getItem(this.id);
            configuration = serializedConfiguration ? JSON.parse(serializedConfiguration) : {};
          }
          Object.assign(this.config, configuration);
          return this;
        }
      };
    }
  });

  // ../../node_modules/@probe.gl/log/dist/esm/utils/formatters.js
  function formatTime(ms) {
    let formatted;
    if (ms < 10) {
      formatted = "".concat(ms.toFixed(2), "ms");
    } else if (ms < 100) {
      formatted = "".concat(ms.toFixed(1), "ms");
    } else if (ms < 1e3) {
      formatted = "".concat(ms.toFixed(0), "ms");
    } else {
      formatted = "".concat((ms / 1e3).toFixed(2), "s");
    }
    return formatted;
  }
  function leftPad(string) {
    let length = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 8;
    const padLength = Math.max(length - string.length, 0);
    return "".concat(" ".repeat(padLength)).concat(string);
  }
  function formatImage(image, message, scale) {
    let maxWidth = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 600;
    const imageUrl = image.src.replace(/\(/g, "%28").replace(/\)/g, "%29");
    if (image.width > maxWidth) {
      scale = Math.min(scale, maxWidth / image.width);
    }
    const width = image.width * scale;
    const height = image.height * scale;
    const style = ["font-size:1px;", "padding:".concat(Math.floor(height / 2), "px ").concat(Math.floor(width / 2), "px;"), "line-height:".concat(height, "px;"), "background:url(".concat(imageUrl, ");"), "background-size:".concat(width, "px ").concat(height, "px;"), "color:transparent;"].join("");
    return ["".concat(message, " %c+"), style];
  }
  var init_formatters = __esm({
    "../../node_modules/@probe.gl/log/dist/esm/utils/formatters.js"() {
    }
  });

  // ../../node_modules/@probe.gl/log/dist/esm/utils/color.js
  function getColor(color) {
    return typeof color === "string" ? COLOR[color.toUpperCase()] || COLOR.WHITE : color;
  }
  function addColor(string, color, background) {
    if (!isBrowser3 && typeof string === "string") {
      if (color) {
        color = getColor(color);
        string = "[".concat(color, "m").concat(string, "[39m");
      }
      if (background) {
        color = getColor(background);
        string = "[".concat(background + 10, "m").concat(string, "[49m");
      }
    }
    return string;
  }
  var COLOR;
  var init_color = __esm({
    "../../node_modules/@probe.gl/log/dist/esm/utils/color.js"() {
      init_esm2();
      (function(COLOR2) {
        COLOR2[COLOR2["BLACK"] = 30] = "BLACK";
        COLOR2[COLOR2["RED"] = 31] = "RED";
        COLOR2[COLOR2["GREEN"] = 32] = "GREEN";
        COLOR2[COLOR2["YELLOW"] = 33] = "YELLOW";
        COLOR2[COLOR2["BLUE"] = 34] = "BLUE";
        COLOR2[COLOR2["MAGENTA"] = 35] = "MAGENTA";
        COLOR2[COLOR2["CYAN"] = 36] = "CYAN";
        COLOR2[COLOR2["WHITE"] = 37] = "WHITE";
        COLOR2[COLOR2["BRIGHT_BLACK"] = 90] = "BRIGHT_BLACK";
        COLOR2[COLOR2["BRIGHT_RED"] = 91] = "BRIGHT_RED";
        COLOR2[COLOR2["BRIGHT_GREEN"] = 92] = "BRIGHT_GREEN";
        COLOR2[COLOR2["BRIGHT_YELLOW"] = 93] = "BRIGHT_YELLOW";
        COLOR2[COLOR2["BRIGHT_BLUE"] = 94] = "BRIGHT_BLUE";
        COLOR2[COLOR2["BRIGHT_MAGENTA"] = 95] = "BRIGHT_MAGENTA";
        COLOR2[COLOR2["BRIGHT_CYAN"] = 96] = "BRIGHT_CYAN";
        COLOR2[COLOR2["BRIGHT_WHITE"] = 97] = "BRIGHT_WHITE";
      })(COLOR || (COLOR = {}));
    }
  });

  // ../../node_modules/@probe.gl/log/dist/esm/utils/autobind.js
  function autobind(obj) {
    let predefined = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : ["constructor"];
    const proto = Object.getPrototypeOf(obj);
    const propNames = Object.getOwnPropertyNames(proto);
    for (const key of propNames) {
      if (typeof obj[key] === "function") {
        if (!predefined.find((name) => key === name)) {
          obj[key] = obj[key].bind(obj);
        }
      }
    }
  }
  var init_autobind = __esm({
    "../../node_modules/@probe.gl/log/dist/esm/utils/autobind.js"() {
    }
  });

  // ../../node_modules/@probe.gl/log/dist/esm/utils/assert.js
  function assert3(condition, message) {
    if (!condition) {
      throw new Error(message || "Assertion failed");
    }
  }
  var init_assert3 = __esm({
    "../../node_modules/@probe.gl/log/dist/esm/utils/assert.js"() {
    }
  });

  // ../../node_modules/@probe.gl/log/dist/esm/utils/hi-res-timestamp.js
  function getHiResTimestamp2() {
    let timestamp;
    if (isBrowser3 && "performance" in window_3) {
      var _window$performance, _window$performance$n;
      timestamp = window_3 === null || window_3 === void 0 ? void 0 : (_window$performance = window_3.performance) === null || _window$performance === void 0 ? void 0 : (_window$performance$n = _window$performance.now) === null || _window$performance$n === void 0 ? void 0 : _window$performance$n.call(_window$performance);
    } else if ("hrtime" in process_) {
      var _process$hrtime;
      const timeParts = process_ === null || process_ === void 0 ? void 0 : (_process$hrtime = process_.hrtime) === null || _process$hrtime === void 0 ? void 0 : _process$hrtime.call(process_);
      timestamp = timeParts[0] * 1e3 + timeParts[1] / 1e6;
    } else {
      timestamp = Date.now();
    }
    return timestamp;
  }
  var init_hi_res_timestamp2 = __esm({
    "../../node_modules/@probe.gl/log/dist/esm/utils/hi-res-timestamp.js"() {
      init_esm2();
    }
  });

  // ../../node_modules/@probe.gl/log/dist/esm/log.js
  function noop() {
  }
  function normalizeLogLevel(logLevel) {
    if (!logLevel) {
      return 0;
    }
    let resolvedLevel;
    switch (typeof logLevel) {
      case "number":
        resolvedLevel = logLevel;
        break;
      case "object":
        resolvedLevel = logLevel.logLevel || logLevel.priority || 0;
        break;
      default:
        return 0;
    }
    assert3(Number.isFinite(resolvedLevel) && resolvedLevel >= 0);
    return resolvedLevel;
  }
  function normalizeArguments(opts) {
    const {
      logLevel,
      message
    } = opts;
    opts.logLevel = normalizeLogLevel(logLevel);
    const args = opts.args ? Array.from(opts.args) : [];
    while (args.length && args.shift() !== message) {
    }
    switch (typeof logLevel) {
      case "string":
      case "function":
        if (message !== void 0) {
          args.unshift(message);
        }
        opts.message = logLevel;
        break;
      case "object":
        Object.assign(opts, logLevel);
        break;
      default:
    }
    if (typeof opts.message === "function") {
      opts.message = opts.message();
    }
    const messageType = typeof opts.message;
    assert3(messageType === "string" || messageType === "object");
    return Object.assign(opts, {
      args
    }, opts.opts);
  }
  function decorateMessage(id, message, opts) {
    if (typeof message === "string") {
      const time = opts.time ? leftPad(formatTime(opts.total)) : "";
      message = opts.time ? "".concat(id, ": ").concat(time, "  ").concat(message) : "".concat(id, ": ").concat(message);
      message = addColor(message, opts.color, opts.background);
    }
    return message;
  }
  function logImageInNode(_ref2) {
    let {
      image,
      message = "",
      scale = 1
    } = _ref2;
    console.warn("removed");
    return noop;
  }
  function logImageInBrowser(_ref3) {
    let {
      image,
      message = "",
      scale = 1
    } = _ref3;
    if (typeof image === "string") {
      const img = new Image();
      img.onload = () => {
        const args = formatImage(img, message, scale);
        console.log(...args);
      };
      img.src = image;
      return noop;
    }
    const element = image.nodeName || "";
    if (element.toLowerCase() === "img") {
      console.log(...formatImage(image, message, scale));
      return noop;
    }
    if (element.toLowerCase() === "canvas") {
      const img = new Image();
      img.onload = () => console.log(...formatImage(img, message, scale));
      img.src = image.toDataURL();
      return noop;
    }
    return noop;
  }
  function getTableHeader(table) {
    for (const key in table) {
      for (const title in table[key]) {
        return title || "untitled";
      }
    }
    return "empty";
  }
  var originalConsole, DEFAULT_SETTINGS, cache, ONCE, Log;
  var init_log = __esm({
    "../../node_modules/@probe.gl/log/dist/esm/log.js"() {
      init_defineProperty();
      init_esm2();
      init_local_storage();
      init_formatters();
      init_color();
      init_autobind();
      init_assert3();
      init_hi_res_timestamp2();
      originalConsole = {
        debug: isBrowser3 ? console.debug || console.log : console.log,
        log: console.log,
        info: console.info,
        warn: console.warn,
        error: console.error
      };
      DEFAULT_SETTINGS = {
        enabled: true,
        level: 0
      };
      cache = {};
      ONCE = {
        once: true
      };
      Log = class {
        constructor() {
          let {
            id
          } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {
            id: ""
          };
          _defineProperty(this, "id", void 0);
          _defineProperty(this, "VERSION", VERSION4);
          _defineProperty(this, "_startTs", getHiResTimestamp2());
          _defineProperty(this, "_deltaTs", getHiResTimestamp2());
          _defineProperty(this, "_storage", void 0);
          _defineProperty(this, "userData", {});
          _defineProperty(this, "LOG_THROTTLE_TIMEOUT", 0);
          this.id = id;
          this.userData = {};
          this._storage = new LocalStorage("__probe-".concat(this.id, "__"), DEFAULT_SETTINGS);
          this.timeStamp("".concat(this.id, " started"));
          autobind(this);
          Object.seal(this);
        }
        set level(newLevel) {
          this.setLevel(newLevel);
        }
        get level() {
          return this.getLevel();
        }
        isEnabled() {
          return this._storage.config.enabled;
        }
        getLevel() {
          return this._storage.config.level;
        }
        getTotal() {
          return Number((getHiResTimestamp2() - this._startTs).toPrecision(10));
        }
        getDelta() {
          return Number((getHiResTimestamp2() - this._deltaTs).toPrecision(10));
        }
        set priority(newPriority) {
          this.level = newPriority;
        }
        get priority() {
          return this.level;
        }
        getPriority() {
          return this.level;
        }
        enable() {
          let enabled = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;
          this._storage.setConfiguration({
            enabled
          });
          return this;
        }
        setLevel(level) {
          this._storage.setConfiguration({
            level
          });
          return this;
        }
        get(setting) {
          return this._storage.config[setting];
        }
        set(setting, value) {
          this._storage.setConfiguration({
            [setting]: value
          });
        }
        settings() {
          if (console.table) {
            console.table(this._storage.config);
          } else {
            console.log(this._storage.config);
          }
        }
        assert(condition, message) {
          assert3(condition, message);
        }
        warn(message) {
          return this._getLogFunction(0, message, originalConsole.warn, arguments, ONCE);
        }
        error(message) {
          return this._getLogFunction(0, message, originalConsole.error, arguments);
        }
        deprecated(oldUsage, newUsage) {
          return this.warn("`".concat(oldUsage, "` is deprecated and will be removed in a later version. Use `").concat(newUsage, "` instead"));
        }
        removed(oldUsage, newUsage) {
          return this.error("`".concat(oldUsage, "` has been removed. Use `").concat(newUsage, "` instead"));
        }
        probe(logLevel, message) {
          return this._getLogFunction(logLevel, message, originalConsole.log, arguments, {
            time: true,
            once: true
          });
        }
        log(logLevel, message) {
          return this._getLogFunction(logLevel, message, originalConsole.debug, arguments);
        }
        info(logLevel, message) {
          return this._getLogFunction(logLevel, message, console.info, arguments);
        }
        once(logLevel, message) {
          for (var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {
            args[_key - 2] = arguments[_key];
          }
          return this._getLogFunction(logLevel, message, originalConsole.debug || originalConsole.info, arguments, ONCE);
        }
        table(logLevel, table, columns) {
          if (table) {
            return this._getLogFunction(logLevel, table, console.table || noop, columns && [columns], {
              tag: getTableHeader(table)
            });
          }
          return noop;
        }
        image(_ref) {
          let {
            logLevel,
            priority,
            image,
            message = "",
            scale = 1
          } = _ref;
          if (!this._shouldLog(logLevel || priority)) {
            return noop;
          }
          return isBrowser3 ? logImageInBrowser({
            image,
            message,
            scale
          }) : logImageInNode({
            image,
            message,
            scale
          });
        }
        time(logLevel, message) {
          return this._getLogFunction(logLevel, message, console.time ? console.time : console.info);
        }
        timeEnd(logLevel, message) {
          return this._getLogFunction(logLevel, message, console.timeEnd ? console.timeEnd : console.info);
        }
        timeStamp(logLevel, message) {
          return this._getLogFunction(logLevel, message, console.timeStamp || noop);
        }
        group(logLevel, message) {
          let opts = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {
            collapsed: false
          };
          const options = normalizeArguments({
            logLevel,
            message,
            opts
          });
          const {
            collapsed
          } = opts;
          options.method = (collapsed ? console.groupCollapsed : console.group) || console.info;
          return this._getLogFunction(options);
        }
        groupCollapsed(logLevel, message) {
          let opts = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};
          return this.group(logLevel, message, Object.assign({}, opts, {
            collapsed: true
          }));
        }
        groupEnd(logLevel) {
          return this._getLogFunction(logLevel, "", console.groupEnd || noop);
        }
        withGroup(logLevel, message, func) {
          this.group(logLevel, message)();
          try {
            func();
          } finally {
            this.groupEnd(logLevel)();
          }
        }
        trace() {
          if (console.trace) {
            console.trace();
          }
        }
        _shouldLog(logLevel) {
          return this.isEnabled() && this.getLevel() >= normalizeLogLevel(logLevel);
        }
        _getLogFunction(logLevel, message, method, args, opts) {
          if (this._shouldLog(logLevel)) {
            opts = normalizeArguments({
              logLevel,
              message,
              args,
              opts
            });
            method = method || opts.method;
            assert3(method);
            opts.total = this.getTotal();
            opts.delta = this.getDelta();
            this._deltaTs = getHiResTimestamp2();
            const tag = opts.tag || opts.message;
            if (opts.once) {
              if (!cache[tag]) {
                cache[tag] = getHiResTimestamp2();
              } else {
                return noop;
              }
            }
            message = decorateMessage(this.id, opts.message, opts);
            return method.bind(console, message, ...opts.args);
          }
          return noop;
        }
      };
      _defineProperty(Log, "VERSION", VERSION4);
    }
  });

  // ../../node_modules/@probe.gl/log/dist/esm/index.js
  var esm_default;
  var init_esm3 = __esm({
    "../../node_modules/@probe.gl/log/dist/esm/index.js"() {
      init_log();
      init_log();
      esm_default = new Log({
        id: "@probe.gl/log"
      });
    }
  });

  // src/lib/loader-utils/loggers.ts
  var probeLog, NullLog, ConsoleLog;
  var init_loggers = __esm({
    "src/lib/loader-utils/loggers.ts"() {
      init_esm3();
      probeLog = new Log({ id: "loaders.gl" });
      NullLog = class {
        log() {
          return () => {
          };
        }
        info() {
          return () => {
          };
        }
        warn() {
          return () => {
          };
        }
        error() {
          return () => {
          };
        }
      };
      ConsoleLog = class {
        constructor() {
          this.console = console;
        }
        log(...args) {
          return this.console.log.bind(this.console, ...args);
        }
        info(...args) {
          return this.console.info.bind(this.console, ...args);
        }
        warn(...args) {
          return this.console.warn.bind(this.console, ...args);
        }
        error(...args) {
          return this.console.error.bind(this.console, ...args);
        }
      };
    }
  });

  // src/lib/loader-utils/option-defaults.ts
  var DEFAULT_LOADER_OPTIONS, REMOVED_LOADER_OPTIONS;
  var init_option_defaults = __esm({
    "src/lib/loader-utils/option-defaults.ts"() {
      init_src2();
      init_loggers();
      DEFAULT_LOADER_OPTIONS = {
        fetch: null,
        mimeType: void 0,
        nothrow: false,
        log: new ConsoleLog(),
        CDN: "https://unpkg.com/@loaders.gl",
        worker: true,
        maxConcurrency: 3,
        maxMobileConcurrency: 1,
        reuseWorkers: isBrowser,
        _nodeWorkers: false,
        _workerType: "",
        limit: 0,
        _limitMB: 0,
        batchSize: "auto",
        batchDebounceMs: 0,
        metadata: false,
        transforms: []
      };
      REMOVED_LOADER_OPTIONS = {
        throws: "nothrow",
        dataType: "(no longer used)",
        uri: "baseUri",
        method: "fetch.method",
        headers: "fetch.headers",
        body: "fetch.body",
        mode: "fetch.mode",
        credentials: "fetch.credentials",
        cache: "fetch.cache",
        redirect: "fetch.redirect",
        referrer: "fetch.referrer",
        referrerPolicy: "fetch.referrerPolicy",
        integrity: "fetch.integrity",
        keepalive: "fetch.keepalive",
        signal: "fetch.signal"
      };
    }
  });

  // src/lib/loader-utils/option-utils.ts
  function getGlobalLoaderState() {
    globalThis.loaders = globalThis.loaders || {};
    const { loaders } = globalThis;
    loaders._state = loaders._state || {};
    return loaders._state;
  }
  function setGlobalOptions(options) {
    const state = getGlobalLoaderState();
    const globalOptions = getGlobalLoaderOptions();
    state.globalOptions = normalizeOptionsInternal(globalOptions, options);
  }
  function normalizeOptions(options, loader, loaders, url) {
    loaders = loaders || [];
    loaders = Array.isArray(loaders) ? loaders : [loaders];
    validateOptions(options, loaders);
    return normalizeOptionsInternal(loader, options, url);
  }
  function validateOptions(options, loaders) {
    validateOptionsObject(options, null, DEFAULT_LOADER_OPTIONS, REMOVED_LOADER_OPTIONS, loaders);
    for (const loader of loaders) {
      const idOptions = options && options[loader.id] || {};
      const loaderOptions = loader.options && loader.options[loader.id] || {};
      const deprecatedOptions = loader.deprecatedOptions && loader.deprecatedOptions[loader.id] || {};
      validateOptionsObject(idOptions, loader.id, loaderOptions, deprecatedOptions, loaders);
    }
  }
  function validateOptionsObject(options, id, defaultOptions, deprecatedOptions, loaders) {
    const loaderName = id || "Top level";
    const prefix = id ? `${id}.` : "";
    for (const key in options) {
      const isSubOptions = !id && isObject(options[key]);
      const isBaseUriOption = key === "baseUri" && !id;
      const isWorkerUrlOption = key === "workerUrl" && id;
      if (!(key in defaultOptions) && !isBaseUriOption && !isWorkerUrlOption) {
        if (key in deprecatedOptions) {
          probeLog.warn(`${loaderName} loader option '${prefix}${key}' no longer supported, use '${deprecatedOptions[key]}'`)();
        } else if (!isSubOptions) {
          const suggestion = findSimilarOption(key, loaders);
          probeLog.warn(`${loaderName} loader option '${prefix}${key}' not recognized. ${suggestion}`)();
        }
      }
    }
  }
  function findSimilarOption(optionKey, loaders) {
    const lowerCaseOptionKey = optionKey.toLowerCase();
    let bestSuggestion = "";
    for (const loader of loaders) {
      for (const key in loader.options) {
        if (optionKey === key) {
          return `Did you mean '${loader.id}.${key}'?`;
        }
        const lowerCaseKey = key.toLowerCase();
        const isPartialMatch = lowerCaseOptionKey.startsWith(lowerCaseKey) || lowerCaseKey.startsWith(lowerCaseOptionKey);
        if (isPartialMatch) {
          bestSuggestion = bestSuggestion || `Did you mean '${loader.id}.${key}'?`;
        }
      }
    }
    return bestSuggestion;
  }
  function normalizeOptionsInternal(loader, options, url) {
    const loaderDefaultOptions = loader.options || {};
    const mergedOptions = { ...loaderDefaultOptions };
    addUrlOptions(mergedOptions, url);
    if (mergedOptions.log === null) {
      mergedOptions.log = new NullLog();
    }
    mergeNestedFields(mergedOptions, getGlobalLoaderOptions());
    mergeNestedFields(mergedOptions, options);
    return mergedOptions;
  }
  function mergeNestedFields(mergedOptions, options) {
    for (const key in options) {
      if (key in options) {
        const value = options[key];
        if (isPureObject(value) && isPureObject(mergedOptions[key])) {
          mergedOptions[key] = {
            ...mergedOptions[key],
            ...options[key]
          };
        } else {
          mergedOptions[key] = options[key];
        }
      }
    }
  }
  function addUrlOptions(options, url) {
    if (url && !("baseUri" in options)) {
      options.baseUri = url;
    }
  }
  var getGlobalLoaderOptions;
  var init_option_utils = __esm({
    "src/lib/loader-utils/option-utils.ts"() {
      init_is_type();
      init_loggers();
      init_option_defaults();
      getGlobalLoaderOptions = () => {
        const state = getGlobalLoaderState();
        state.globalOptions = state.globalOptions || { ...DEFAULT_LOADER_OPTIONS };
        return state.globalOptions;
      };
    }
  });

  // src/lib/api/loader-options.ts
  var init_loader_options = __esm({
    "src/lib/api/loader-options.ts"() {
      init_option_utils();
      init_option_utils();
    }
  });

  // src/lib/loader-utils/normalize-loader.ts
  function isLoaderObject(loader) {
    if (!loader) {
      return false;
    }
    if (Array.isArray(loader)) {
      loader = loader[0];
    }
    const hasExtensions = Array.isArray(loader?.extensions);
    return hasExtensions;
  }
  function normalizeLoader(loader) {
    assert(loader, "null loader");
    assert(isLoaderObject(loader), "invalid loader");
    let options;
    if (Array.isArray(loader)) {
      options = loader[1];
      loader = loader[0];
      loader = {
        ...loader,
        options: { ...loader.options, ...options }
      };
    }
    if (loader?.parseTextSync || loader?.parseText) {
      loader.text = true;
    }
    if (!loader.text) {
      loader.binary = true;
    }
    return loader;
  }
  var init_normalize_loader = __esm({
    "src/lib/loader-utils/normalize-loader.ts"() {
      init_src2();
    }
  });

  // src/lib/api/register-loaders.ts
  function registerLoaders(loaders) {
    const loaderRegistry = getGlobalLoaderRegistry();
    loaders = Array.isArray(loaders) ? loaders : [loaders];
    for (const loader of loaders) {
      const normalizedLoader = normalizeLoader(loader);
      if (!loaderRegistry.find((registeredLoader) => normalizedLoader === registeredLoader)) {
        loaderRegistry.unshift(normalizedLoader);
      }
    }
  }
  function getRegisteredLoaders() {
    return getGlobalLoaderRegistry();
  }
  function _unregisterLoaders() {
    const state = getGlobalLoaderState();
    state.loaderRegistry = [];
  }
  var getGlobalLoaderRegistry;
  var init_register_loaders = __esm({
    "src/lib/api/register-loaders.ts"() {
      init_normalize_loader();
      init_option_utils();
      getGlobalLoaderRegistry = () => {
        const state = getGlobalLoaderState();
        state.loaderRegistry = state.loaderRegistry || [];
        return state.loaderRegistry;
      };
    }
  });

  // src/lib/utils/log.ts
  var log;
  var init_log2 = __esm({
    "src/lib/utils/log.ts"() {
      init_esm3();
      log = new Log({ id: "loaders.gl" });
    }
  });

  // src/lib/api/select-loader.ts
  async function selectLoader(data, loaders = [], options, context) {
    if (!validHTTPResponse(data)) {
      return null;
    }
    let loader = selectLoaderSync(data, loaders, { ...options, nothrow: true }, context);
    if (loader) {
      return loader;
    }
    if (isBlob(data)) {
      data = await data.slice(0, 10).arrayBuffer();
      loader = selectLoaderSync(data, loaders, options, context);
    }
    if (!loader && !options?.nothrow) {
      throw new Error(getNoValidLoaderMessage(data));
    }
    return loader;
  }
  function selectLoaderSync(data, loaders = [], options, context) {
    if (!validHTTPResponse(data)) {
      return null;
    }
    if (loaders && !Array.isArray(loaders)) {
      return normalizeLoader(loaders);
    }
    let candidateLoaders = [];
    if (loaders) {
      candidateLoaders = candidateLoaders.concat(loaders);
    }
    if (!options?.ignoreRegisteredLoaders) {
      candidateLoaders.push(...getRegisteredLoaders());
    }
    normalizeLoaders(candidateLoaders);
    const loader = selectLoaderInternal(data, candidateLoaders, options, context);
    if (!loader && !options?.nothrow) {
      throw new Error(getNoValidLoaderMessage(data));
    }
    return loader;
  }
  function selectLoaderInternal(data, loaders, options, context) {
    const url = getResourceUrl(data);
    const type = getResourceMIMEType(data);
    const testUrl = stripQueryString(url) || context?.url;
    let loader = null;
    let reason = "";
    if (options?.mimeType) {
      loader = findLoaderByMIMEType(loaders, options?.mimeType);
      reason = `match forced by supplied MIME type ${options?.mimeType}`;
    }
    loader = loader || findLoaderByUrl(loaders, testUrl);
    reason = reason || (loader ? `matched url ${testUrl}` : "");
    loader = loader || findLoaderByMIMEType(loaders, type);
    reason = reason || (loader ? `matched MIME type ${type}` : "");
    loader = loader || findLoaderByInitialBytes(loaders, data);
    reason = reason || (loader ? `matched initial data ${getFirstCharacters(data)}` : "");
    loader = loader || findLoaderByMIMEType(loaders, options?.fallbackMimeType);
    reason = reason || (loader ? `matched fallback MIME type ${type}` : "");
    if (reason) {
      log.log(1, `selectLoader selected ${loader?.name}: ${reason}.`);
    }
    return loader;
  }
  function validHTTPResponse(data) {
    if (data instanceof Response) {
      if (data.status === 204) {
        return false;
      }
    }
    return true;
  }
  function getNoValidLoaderMessage(data) {
    const url = getResourceUrl(data);
    const type = getResourceMIMEType(data);
    let message = "No valid loader found (";
    message += url ? `${path_exports.filename(url)}, ` : "no url provided, ";
    message += `MIME type: ${type ? `"${type}"` : "not provided"}, `;
    const firstCharacters = data ? getFirstCharacters(data) : "";
    message += firstCharacters ? ` first bytes: "${firstCharacters}"` : "first bytes: not available";
    message += ")";
    return message;
  }
  function normalizeLoaders(loaders) {
    for (const loader of loaders) {
      normalizeLoader(loader);
    }
  }
  function findLoaderByUrl(loaders, url) {
    const match = url && EXT_PATTERN.exec(url);
    const extension = match && match[1];
    return extension ? findLoaderByExtension(loaders, extension) : null;
  }
  function findLoaderByExtension(loaders, extension) {
    extension = extension.toLowerCase();
    for (const loader of loaders) {
      for (const loaderExtension of loader.extensions) {
        if (loaderExtension.toLowerCase() === extension) {
          return loader;
        }
      }
    }
    return null;
  }
  function findLoaderByMIMEType(loaders, mimeType) {
    for (const loader of loaders) {
      if (loader.mimeTypes && loader.mimeTypes.includes(mimeType)) {
        return loader;
      }
      if (mimeType === `application/x.${loader.id}`) {
        return loader;
      }
    }
    return null;
  }
  function findLoaderByInitialBytes(loaders, data) {
    if (!data) {
      return null;
    }
    for (const loader of loaders) {
      if (typeof data === "string") {
        if (testDataAgainstText(data, loader)) {
          return loader;
        }
      } else if (ArrayBuffer.isView(data)) {
        if (testDataAgainstBinary(data.buffer, data.byteOffset, loader)) {
          return loader;
        }
      } else if (data instanceof ArrayBuffer) {
        const byteOffset = 0;
        if (testDataAgainstBinary(data, byteOffset, loader)) {
          return loader;
        }
      }
    }
    return null;
  }
  function testDataAgainstText(data, loader) {
    if (loader.testText) {
      return loader.testText(data);
    }
    const tests = Array.isArray(loader.tests) ? loader.tests : [loader.tests];
    return tests.some((test) => data.startsWith(test));
  }
  function testDataAgainstBinary(data, byteOffset, loader) {
    const tests = Array.isArray(loader.tests) ? loader.tests : [loader.tests];
    return tests.some((test) => testBinary(data, byteOffset, loader, test));
  }
  function testBinary(data, byteOffset, loader, test) {
    if (test instanceof ArrayBuffer) {
      return compareArrayBuffers(test, data, test.byteLength);
    }
    switch (typeof test) {
      case "function":
        return test(data, loader);
      case "string":
        const magic = getMagicString(data, byteOffset, test.length);
        return test === magic;
      default:
        return false;
    }
  }
  function getFirstCharacters(data, length = 5) {
    if (typeof data === "string") {
      return data.slice(0, length);
    } else if (ArrayBuffer.isView(data)) {
      return getMagicString(data.buffer, data.byteOffset, length);
    } else if (data instanceof ArrayBuffer) {
      const byteOffset = 0;
      return getMagicString(data, byteOffset, length);
    }
    return "";
  }
  function getMagicString(arrayBuffer, byteOffset, length) {
    if (arrayBuffer.byteLength < byteOffset + length) {
      return "";
    }
    const dataView = new DataView(arrayBuffer);
    let magic = "";
    for (let i = 0; i < length; i++) {
      magic += String.fromCharCode(dataView.getUint8(byteOffset + i));
    }
    return magic;
  }
  var EXT_PATTERN;
  var init_select_loader = __esm({
    "src/lib/api/select-loader.ts"() {
      init_src2();
      init_normalize_loader();
      init_log2();
      init_resource_utils();
      init_register_loaders();
      init_is_type();
      init_url_utils();
      EXT_PATTERN = /\.([^.]+)$/;
    }
  });

  // src/iterators/make-iterator/make-string-iterator.ts
  function* makeStringIterator(string, options) {
    const chunkSize = options?.chunkSize || DEFAULT_CHUNK_SIZE;
    let offset = 0;
    const textEncoder = new TextEncoder();
    while (offset < string.length) {
      const chunkLength = Math.min(string.length - offset, chunkSize);
      const chunk = string.slice(offset, offset + chunkLength);
      offset += chunkLength;
      yield textEncoder.encode(chunk);
    }
  }
  var DEFAULT_CHUNK_SIZE;
  var init_make_string_iterator = __esm({
    "src/iterators/make-iterator/make-string-iterator.ts"() {
      DEFAULT_CHUNK_SIZE = 256 * 1024;
    }
  });

  // src/iterators/make-iterator/make-array-buffer-iterator.ts
  function* makeArrayBufferIterator(arrayBuffer, options = {}) {
    const { chunkSize = DEFAULT_CHUNK_SIZE2 } = options;
    let byteOffset = 0;
    while (byteOffset < arrayBuffer.byteLength) {
      const chunkByteLength = Math.min(arrayBuffer.byteLength - byteOffset, chunkSize);
      const chunk = new ArrayBuffer(chunkByteLength);
      const sourceArray = new Uint8Array(arrayBuffer, byteOffset, chunkByteLength);
      const chunkArray = new Uint8Array(chunk);
      chunkArray.set(sourceArray);
      byteOffset += chunkByteLength;
      yield chunk;
    }
  }
  var DEFAULT_CHUNK_SIZE2;
  var init_make_array_buffer_iterator = __esm({
    "src/iterators/make-iterator/make-array-buffer-iterator.ts"() {
      DEFAULT_CHUNK_SIZE2 = 256 * 1024;
    }
  });

  // src/iterators/make-iterator/make-blob-iterator.ts
  async function* makeBlobIterator(blob, options) {
    const chunkSize = options?.chunkSize || DEFAULT_CHUNK_SIZE3;
    let offset = 0;
    while (offset < blob.size) {
      const end = offset + chunkSize;
      const chunk = await blob.slice(offset, end).arrayBuffer();
      offset = end;
      yield chunk;
    }
  }
  var DEFAULT_CHUNK_SIZE3;
  var init_make_blob_iterator = __esm({
    "src/iterators/make-iterator/make-blob-iterator.ts"() {
      DEFAULT_CHUNK_SIZE3 = 1024 * 1024;
    }
  });

  // src/iterators/make-iterator/make-stream-iterator.ts
  function makeStreamIterator(stream, options) {
    return isBrowser ? makeBrowserStreamIterator(stream, options) : makeNodeStreamIterator(stream, options);
  }
  async function* makeBrowserStreamIterator(stream, options) {
    const reader = stream.getReader();
    let nextBatchPromise;
    try {
      while (true) {
        const currentBatchPromise = nextBatchPromise || reader.read();
        if (options?._streamReadAhead) {
          nextBatchPromise = reader.read();
        }
        const { done, value } = await currentBatchPromise;
        if (done) {
          return;
        }
        yield toArrayBuffer2(value);
      }
    } catch (error) {
      reader.releaseLock();
    }
  }
  async function* makeNodeStreamIterator(stream, options) {
    for await (const chunk of stream) {
      yield toArrayBuffer2(chunk);
    }
  }
  var init_make_stream_iterator = __esm({
    "src/iterators/make-iterator/make-stream-iterator.ts"() {
      init_src2();
    }
  });

  // src/iterators/make-iterator/make-iterator.ts
  function makeIterator(data, options) {
    if (typeof data === "string") {
      return makeStringIterator(data, options);
    }
    if (data instanceof ArrayBuffer) {
      return makeArrayBufferIterator(data, options);
    }
    if (isBlob(data)) {
      return makeBlobIterator(data, options);
    }
    if (isReadableStream(data)) {
      return makeStreamIterator(data, options);
    }
    if (isResponse(data)) {
      const response = data;
      return makeStreamIterator(response.body, options);
    }
    throw new Error("makeIterator");
  }
  var init_make_iterator = __esm({
    "src/iterators/make-iterator/make-iterator.ts"() {
      init_make_string_iterator();
      init_make_array_buffer_iterator();
      init_make_blob_iterator();
      init_make_stream_iterator();
      init_is_type();
    }
  });

  // src/lib/loader-utils/get-data.ts
  function getArrayBufferOrStringFromDataSync(data, loader, options) {
    if (loader.text && typeof data === "string") {
      return data;
    }
    if (isBuffer2(data)) {
      data = data.buffer;
    }
    if (data instanceof ArrayBuffer) {
      const arrayBuffer = data;
      if (loader.text && !loader.binary) {
        const textDecoder = new TextDecoder("utf8");
        return textDecoder.decode(arrayBuffer);
      }
      return arrayBuffer;
    }
    if (ArrayBuffer.isView(data)) {
      if (loader.text && !loader.binary) {
        const textDecoder = new TextDecoder("utf8");
        return textDecoder.decode(data);
      }
      let arrayBuffer = data.buffer;
      const byteLength = data.byteLength || data.length;
      if (data.byteOffset !== 0 || byteLength !== arrayBuffer.byteLength) {
        arrayBuffer = arrayBuffer.slice(data.byteOffset, data.byteOffset + byteLength);
      }
      return arrayBuffer;
    }
    throw new Error(ERR_DATA);
  }
  async function getArrayBufferOrStringFromData(data, loader, options) {
    const isArrayBuffer = data instanceof ArrayBuffer || ArrayBuffer.isView(data);
    if (typeof data === "string" || isArrayBuffer) {
      return getArrayBufferOrStringFromDataSync(data, loader, options);
    }
    if (isBlob(data)) {
      data = await makeResponse(data);
    }
    if (isResponse(data)) {
      const response = data;
      await checkResponse(response);
      return loader.binary ? await response.arrayBuffer() : await response.text();
    }
    if (isReadableStream(data)) {
      data = makeIterator(data, options);
    }
    if (isIterable(data) || isAsyncIterable(data)) {
      return concatenateArrayBuffersAsync(data);
    }
    throw new Error(ERR_DATA);
  }
  async function getAsyncIterableFromData(data, options) {
    if (isIterator(data)) {
      return data;
    }
    if (isResponse(data)) {
      const response = data;
      await checkResponse(response);
      const body = await response.body;
      return makeIterator(body, options);
    }
    if (isBlob(data) || isReadableStream(data)) {
      return makeIterator(data, options);
    }
    if (isAsyncIterable(data)) {
      return data[Symbol.asyncIterator]();
    }
    return getIterableFromData(data);
  }
  function getIterableFromData(data) {
    if (ArrayBuffer.isView(data)) {
      return function* oneChunk() {
        yield data.buffer;
      }();
    }
    if (data instanceof ArrayBuffer) {
      return function* oneChunk() {
        yield data;
      }();
    }
    if (isIterator(data)) {
      return data;
    }
    if (isIterable(data)) {
      return data[Symbol.iterator]();
    }
    throw new Error(ERR_DATA);
  }
  var ERR_DATA;
  var init_get_data = __esm({
    "src/lib/loader-utils/get-data.ts"() {
      init_src2();
      init_is_type();
      init_make_iterator();
      init_response_utils();
      ERR_DATA = "Cannot convert supplied data type";
    }
  });

  // src/lib/loader-utils/get-fetch-function.ts
  function getFetchFunction(options, context) {
    const globalOptions = getGlobalLoaderOptions();
    const fetchOptions = options || globalOptions;
    if (typeof fetchOptions.fetch === "function") {
      return fetchOptions.fetch;
    }
    if (isObject(fetchOptions.fetch)) {
      return (url) => fetchFile(url, fetchOptions);
    }
    if (context?.fetch) {
      return context?.fetch;
    }
    return fetchFile;
  }
  var init_get_fetch_function = __esm({
    "src/lib/loader-utils/get-fetch-function.ts"() {
      init_is_type();
      init_fetch_file();
      init_option_utils();
    }
  });

  // src/lib/loader-utils/loader-context.ts
  function getLoaderContext(context, options, parentContext) {
    if (parentContext) {
      return parentContext;
    }
    const newContext = {
      fetch: getFetchFunction(options, context),
      ...context
    };
    if (newContext.url) {
      const baseUrl = stripQueryString(newContext.url);
      newContext.baseUrl = baseUrl;
      newContext.queryString = extractQueryString(newContext.url);
      newContext.filename = path_exports.filename(baseUrl);
      newContext.baseUrl = path_exports.dirname(baseUrl);
    }
    if (!Array.isArray(newContext.loaders)) {
      newContext.loaders = null;
    }
    return newContext;
  }
  function getLoadersFromContext(loaders, context) {
    if (!context && loaders && !Array.isArray(loaders)) {
      return loaders;
    }
    let candidateLoaders;
    if (loaders) {
      candidateLoaders = Array.isArray(loaders) ? loaders : [loaders];
    }
    if (context && context.loaders) {
      const contextLoaders = Array.isArray(context.loaders) ? context.loaders : [context.loaders];
      candidateLoaders = candidateLoaders ? [...candidateLoaders, ...contextLoaders] : contextLoaders;
    }
    return candidateLoaders && candidateLoaders.length ? candidateLoaders : null;
  }
  var init_loader_context = __esm({
    "src/lib/loader-utils/loader-context.ts"() {
      init_get_fetch_function();
      init_url_utils();
      init_src2();
    }
  });

  // src/lib/api/parse.ts
  async function parse(data, loaders, options, context) {
    assert2(!context || typeof context === "object");
    if (loaders && !Array.isArray(loaders) && !isLoaderObject(loaders)) {
      context = void 0;
      options = loaders;
      loaders = void 0;
    }
    data = await data;
    options = options || {};
    const url = getResourceUrl(data);
    const typedLoaders = loaders;
    const candidateLoaders = getLoadersFromContext(typedLoaders, context);
    const loader = await selectLoader(data, candidateLoaders, options);
    if (!loader) {
      return null;
    }
    options = normalizeOptions(options, loader, candidateLoaders, url);
    context = getLoaderContext({ url, parse, loaders: candidateLoaders }, options, context || null);
    return await parseWithLoader(loader, data, options, context);
  }
  async function parseWithLoader(loader, data, options, context) {
    validateWorkerVersion(loader);
    if (isResponse(data)) {
      const response = data;
      const { ok, redirected, status, statusText, type, url } = response;
      const headers = Object.fromEntries(response.headers.entries());
      context.response = { headers, ok, redirected, status, statusText, type, url };
    }
    data = await getArrayBufferOrStringFromData(data, loader, options);
    if (loader.parseTextSync && typeof data === "string") {
      options.dataType = "text";
      return loader.parseTextSync(data, options, context, loader);
    }
    if (canParseWithWorker(loader, options)) {
      return await parseWithWorker(loader, data, options, context, parse);
    }
    if (loader.parseText && typeof data === "string") {
      return await loader.parseText(data, options, context, loader);
    }
    if (loader.parse) {
      return await loader.parse(data, options, context, loader);
    }
    assert2(!loader.parseSync);
    throw new Error(`${loader.id} loader - no parser found and worker is disabled`);
  }
  var init_parse = __esm({
    "src/lib/api/parse.ts"() {
      init_src();
      init_src2();
      init_normalize_loader();
      init_is_type();
      init_option_utils();
      init_get_data();
      init_loader_context();
      init_resource_utils();
      init_select_loader();
    }
  });

  // src/lib/api/parse-sync.ts
  function parseSync(data, loaders, options, context) {
    assert(!context || typeof context === "object");
    if (!Array.isArray(loaders) && !isLoaderObject(loaders)) {
      context = void 0;
      options = loaders;
      loaders = void 0;
    }
    options = options || {};
    const typedLoaders = loaders;
    const candidateLoaders = getLoadersFromContext(typedLoaders, context);
    const loader = selectLoaderSync(data, candidateLoaders, options);
    if (!loader) {
      return null;
    }
    options = normalizeOptions(options, loader, candidateLoaders);
    const url = getResourceUrl(data);
    const parse2 = () => {
      throw new Error("parseSync called parse (which is async");
    };
    context = getLoaderContext({ url, parseSync, parse: parse2, loaders }, options, context || null);
    return parseWithLoaderSync(loader, data, options, context);
  }
  function parseWithLoaderSync(loader, data, options, context) {
    data = getArrayBufferOrStringFromDataSync(data, loader, options);
    if (loader.parseTextSync && typeof data === "string") {
      return loader.parseTextSync(data, options);
    }
    if (loader.parseSync && data instanceof ArrayBuffer) {
      return loader.parseSync(data, options, context);
    }
    throw new Error(`${loader.name} loader: 'parseSync' not supported by this loader, use 'parse' instead. ${context.url || ""}`);
  }
  var init_parse_sync = __esm({
    "src/lib/api/parse-sync.ts"() {
      init_src2();
      init_select_loader();
      init_normalize_loader();
      init_option_utils();
      init_get_data();
      init_loader_context();
      init_resource_utils();
    }
  });

  // src/lib/api/parse-in-batches.ts
  async function parseInBatches(data, loaders, options, context) {
    assert(!context || typeof context === "object");
    const loaderArray = Array.isArray(loaders) ? loaders : void 0;
    if (!Array.isArray(loaders) && !isLoaderObject(loaders)) {
      context = void 0;
      options = loaders;
      loaders = void 0;
    }
    data = await data;
    options = options || {};
    const url = getResourceUrl(data);
    const loader = await selectLoader(data, loaders, options);
    if (!loader) {
      return null;
    }
    options = normalizeOptions(options, loader, loaderArray, url);
    context = getLoaderContext({ url, parseInBatches, parse, loaders: loaderArray }, options, context || null);
    return await parseWithLoaderInBatches(loader, data, options, context);
  }
  async function parseWithLoaderInBatches(loader, data, options, context) {
    const outputIterator = await parseToOutputIterator(loader, data, options, context);
    if (!options.metadata) {
      return outputIterator;
    }
    const metadataBatch = {
      batchType: "metadata",
      metadata: {
        _loader: loader,
        _context: context
      },
      data: [],
      bytesUsed: 0
    };
    async function* makeMetadataBatchIterator(iterator) {
      yield metadataBatch;
      yield* iterator;
    }
    return makeMetadataBatchIterator(outputIterator);
  }
  async function parseToOutputIterator(loader, data, options, context) {
    const inputIterator = await getAsyncIterableFromData(data, options);
    const transformedIterator = await applyInputTransforms(inputIterator, options?.transforms || []);
    if (loader.parseInBatches) {
      return loader.parseInBatches(transformedIterator, options, context);
    }
    async function* parseChunkInBatches() {
      const arrayBuffer = await concatenateArrayBuffersAsync(transformedIterator);
      const parsedData = await parse(arrayBuffer, loader, { ...options, mimeType: loader.mimeTypes[0] }, context);
      const batch = {
        mimeType: loader.mimeTypes[0],
        shape: Array.isArray(parsedData) ? "row-table" : "unknown",
        batchType: "data",
        data: parsedData,
        length: Array.isArray(parsedData) ? parsedData.length : 1
      };
      yield batch;
    }
    return parseChunkInBatches();
  }
  async function applyInputTransforms(inputIterator, transforms = []) {
    let iteratorChain = inputIterator;
    for await (const transformBatches of transforms) {
      iteratorChain = transformBatches(iteratorChain);
    }
    return iteratorChain;
  }
  var init_parse_in_batches = __esm({
    "src/lib/api/parse-in-batches.ts"() {
      init_src2();
      init_normalize_loader();
      init_option_utils();
      init_loader_context();
      init_get_data();
      init_resource_utils();
      init_select_loader();
      init_parse();
    }
  });

  // src/lib/api/load.ts
  async function load(url, loaders, options, context) {
    if (!Array.isArray(loaders) && !isLoaderObject(loaders)) {
      context = void 0;
      options = loaders;
      loaders = void 0;
    }
    const fetch2 = getFetchFunction(options);
    let data = url;
    if (typeof url === "string") {
      data = await fetch2(url);
    }
    if (isBlob(url)) {
      data = await fetch2(url);
    }
    return await parse(data, loaders, options);
  }
  var init_load = __esm({
    "src/lib/api/load.ts"() {
      init_is_type();
      init_normalize_loader();
      init_get_fetch_function();
      init_parse();
    }
  });

  // src/lib/api/load-in-batches.ts
  function loadInBatches(files, loaders, options, context) {
    if (!Array.isArray(loaders) && !isLoaderObject(loaders)) {
      context = void 0;
      options = loaders;
      loaders = null;
    }
    const fetch2 = getFetchFunction(options || {});
    if (!Array.isArray(files)) {
      return loadOneFileInBatches(files, loaders, options, fetch2);
    }
    const promises = files.map((file) => loadOneFileInBatches(file, loaders, options, fetch2));
    return promises;
  }
  async function loadOneFileInBatches(file, loaders, options, fetch2) {
    if (typeof file === "string") {
      const url = file;
      const response = await fetch2(url);
      return await parseInBatches(response, loaders, options);
    }
    return await parseInBatches(file, loaders, options);
  }
  var init_load_in_batches = __esm({
    "src/lib/api/load-in-batches.ts"() {
      init_normalize_loader();
      init_get_fetch_function();
      init_parse_in_batches();
    }
  });

  // src/lib/api/encode.ts
  async function encode(data, writer, options) {
    const globalOptions = getGlobalLoaderOptions();
    options = { ...globalOptions, ...options };
    if (canEncodeWithWorker(writer, options)) {
      return await processOnWorker(writer, data, options);
    }
    if (writer.encode) {
      return await writer.encode(data, options);
    }
    if (writer.encodeSync) {
      return writer.encodeSync(data, options);
    }
    if (writer.encodeText) {
      return new TextEncoder().encode(await writer.encodeText(data, options));
    }
    if (writer.encodeInBatches) {
      const batches = encodeInBatches(data, writer, options);
      const chunks = [];
      for await (const batch of batches) {
        chunks.push(batch);
      }
      return concatenateArrayBuffers(...chunks);
    }
    if (!isBrowser && writer.encodeURLtoURL) {
      const tmpInputFilename = getTemporaryFilename("input");
      await writeFile2(tmpInputFilename, data);
      const tmpOutputFilename = getTemporaryFilename("output");
      const outputFilename = await encodeURLtoURL(tmpInputFilename, tmpOutputFilename, writer, options);
      const response = await fetchFile(outputFilename);
      return response.arrayBuffer();
    }
    throw new Error("Writer could not encode data");
  }
  function encodeSync(data, writer, options) {
    if (writer.encodeSync) {
      return writer.encodeSync(data, options);
    }
    throw new Error("Writer could not synchronously encode data");
  }
  async function encodeText(data, writer, options) {
    if (writer.text && writer.encodeText) {
      return await writer.encodeText(data, options);
    }
    if (writer.text && (writer.encode || writer.encodeInBatches)) {
      const arrayBuffer = await encode(data, writer, options);
      return new TextDecoder().decode(arrayBuffer);
    }
    throw new Error("Writer could not encode data as text");
  }
  function encodeInBatches(data, writer, options) {
    if (writer.encodeInBatches) {
      const dataIterator = getIterator(data);
      return writer.encodeInBatches(dataIterator, options);
    }
    throw new Error("Writer could not encode data in batches");
  }
  async function encodeURLtoURL(inputUrl, outputUrl, writer, options) {
    inputUrl = resolvePath(inputUrl);
    outputUrl = resolvePath(outputUrl);
    if (isBrowser || !writer.encodeURLtoURL) {
      throw new Error();
    }
    const outputFilename = await writer.encodeURLtoURL(inputUrl, outputUrl, options);
    return outputFilename;
  }
  function getIterator(data) {
    const dataIterator = [{ table: data, start: 0, end: data.length }];
    return dataIterator;
  }
  function getTemporaryFilename(filename2) {
    return `/tmp/${filename2}`;
  }
  var init_encode = __esm({
    "src/lib/api/encode.ts"() {
      init_src2();
      init_src();
      init_src2();
      init_src2();
      init_write_file();
      init_fetch_file();
      init_loader_options();
    }
  });

  // src/lib/api/save.ts
  async function save(data, url, writer, options) {
    const encodedData = await encode(data, writer, options);
    return await writeFile2(url, encodedData);
  }
  function saveSync(data, url, writer, options) {
    const encodedData = encodeSync(data, writer, options);
    return writeFileSync2(url, encodedData);
  }
  var init_save = __esm({
    "src/lib/api/save.ts"() {
      init_encode();
      init_write_file();
    }
  });

  // src/iterators/make-stream/make-dom-stream.ts
  function makeStream(source, options) {
    const iterator = source[Symbol.asyncIterator] ? source[Symbol.asyncIterator]() : source[Symbol.iterator]();
    return new ReadableStream({
      type: "bytes",
      async pull(controller) {
        try {
          const { done, value } = await iterator.next();
          if (done) {
            controller.close();
          } else {
            controller.enqueue(new Uint8Array(value));
          }
        } catch (error) {
          controller.error(error);
        }
      },
      async cancel() {
        await iterator?.return?.();
      }
    }, {
      highWaterMark: 2 ** 24,
      ...options
    });
  }
  var init_make_dom_stream = __esm({
    "src/iterators/make-stream/make-dom-stream.ts"() {
    }
  });

  // src/null-loader.ts
  function parseSync2(arrayBuffer, options, context) {
    if (!options.null.echoParameters)
      return null;
    context = context && JSON.parse(JSON.stringify(context));
    return { arrayBuffer, options, context };
  }
  var VERSION5, NullWorkerLoader, NullLoader;
  var init_null_loader = __esm({
    "src/null-loader.ts"() {
      VERSION5 = typeof __VERSION__ !== "undefined" ? __VERSION__ : "latest";
      NullWorkerLoader = {
        name: "Null loader",
        id: "null",
        module: "core",
        version: VERSION5,
        worker: true,
        mimeTypes: ["application/x.empty"],
        extensions: ["null"],
        tests: [() => false],
        options: {
          null: {}
        }
      };
      NullLoader = {
        name: "Null loader",
        id: "null",
        module: "core",
        version: VERSION5,
        mimeTypes: ["application/x.empty"],
        extensions: ["null"],
        parse: async (arrayBuffer, options, context) => parseSync2(arrayBuffer, options, context),
        parseSync: parseSync2,
        parseInBatches: async function* generator(asyncIterator, options, context) {
          for await (const batch of asyncIterator) {
            yield parseSync2(batch, options, context);
          }
        },
        tests: [() => false],
        options: {
          null: {
            echoParameters: false
          }
        }
      };
    }
  });

  // src/lib/progress/fetch-progress.ts
  async function fetchProgress(response, onProgress, onDone = () => {
  }, onError = () => {
  }) {
    response = await response;
    if (!response.ok) {
      return response;
    }
    const body = response.body;
    if (!body) {
      return response;
    }
    const contentLength = response.headers.get("content-length") || 0;
    const totalBytes = contentLength ? parseInt(contentLength) : 0;
    if (!(totalBytes > 0)) {
      return response;
    }
    if (typeof ReadableStream === "undefined" || !body.getReader) {
      return response;
    }
    const progressStream = new ReadableStream({
      async start(controller) {
        const reader = body.getReader();
        await read2(controller, reader, 0, totalBytes, onProgress, onDone, onError);
      }
    });
    return new Response(progressStream);
  }
  async function read2(controller, reader, loadedBytes, totalBytes, onProgress, onDone, onError) {
    try {
      const { done, value } = await reader.read();
      if (done) {
        onDone();
        controller.close();
        return;
      }
      loadedBytes += value.byteLength;
      const percent = Math.round(loadedBytes / totalBytes * 100);
      onProgress(percent, { loadedBytes, totalBytes });
      controller.enqueue(value);
      await read2(controller, reader, loadedBytes, totalBytes, onProgress, onDone, onError);
    } catch (error) {
      controller.error(error);
      onError(error);
    }
  }
  var init_fetch_progress = __esm({
    "src/lib/progress/fetch-progress.ts"() {
    }
  });

  // src/lib/filesystems/browser-filesystem.ts
  var BrowserFileSystem;
  var init_browser_filesystem = __esm({
    "src/lib/filesystems/browser-filesystem.ts"() {
      BrowserFileSystem = class {
        constructor(files, options) {
          this.files = {};
          this.lowerCaseFiles = {};
          this.usedFiles = {};
          this._fetch = options?.fetch || fetch;
          for (let i = 0; i < files.length; ++i) {
            const file = files[i];
            this.files[file.name] = file;
            this.lowerCaseFiles[file.name.toLowerCase()] = file;
            this.usedFiles[file.name] = false;
          }
          this.fetch = this.fetch.bind(this);
        }
        async fetch(path, options) {
          if (path.includes("://")) {
            return this._fetch(path, options);
          }
          const file = this.files[path];
          if (!file) {
            return new Response(path, { status: 400, statusText: "NOT FOUND" });
          }
          const headers = new Headers(options?.headers);
          const range = headers.get("Range");
          const bytes = range && /bytes=($1)-($2)/.exec(range);
          if (bytes) {
            const start = parseInt(bytes[1]);
            const end = parseInt(bytes[2]);
            const data = await file.slice(start, end).arrayBuffer();
            const response2 = new Response(data);
            Object.defineProperty(response2, "url", { value: path });
            return response2;
          }
          const response = new Response(file);
          Object.defineProperty(response, "url", { value: path });
          return response;
        }
        async readdir(dirname2) {
          const files = [];
          for (const path in this.files) {
            files.push(path);
          }
          return files;
        }
        async stat(path, options) {
          const file = this.files[path];
          if (!file) {
            throw new Error(path);
          }
          return { size: file.size };
        }
        async unlink(path) {
          delete this.files[path];
          delete this.lowerCaseFiles[path];
          this.usedFiles[path] = true;
        }
        async open(pathname, flags, mode) {
          return this.files[pathname];
        }
        async read(fd, buffer, offset = 0, length = buffer.byteLength, position = null) {
          const file = fd;
          const startPosition = 0;
          const arrayBuffer = await file.slice(startPosition, startPosition + length).arrayBuffer();
          return { bytesRead: length, buffer: arrayBuffer };
        }
        async close(fd) {
        }
        _getFile(path, used) {
          const file = this.files[path] || this.lowerCaseFiles[path];
          if (file && used) {
            this.usedFiles[path] = true;
          }
          return file;
        }
      };
    }
  });

  // src/index.ts
  var src_exports = {};
  __export(src_exports, {
    JSONLoader: () => JSONLoader,
    NullLoader: () => NullLoader,
    NullWorkerLoader: () => NullWorkerLoader,
    RequestScheduler: () => RequestScheduler,
    _BrowserFileSystem: () => BrowserFileSystem,
    _fetchProgress: () => fetchProgress,
    _unregisterLoaders: () => _unregisterLoaders,
    assert: () => assert,
    concatenateArrayBuffersAsync: () => concatenateArrayBuffersAsync,
    document: () => document_,
    encode: () => encode,
    encodeInBatches: () => encodeInBatches,
    encodeSync: () => encodeSync,
    encodeText: () => encodeText,
    encodeURLtoURL: () => encodeURLtoURL,
    fetchFile: () => fetchFile,
    forEach: () => forEach,
    getLoaderOptions: () => getGlobalLoaderOptions,
    getPathPrefix: () => getPathPrefix,
    global: () => global_,
    isAsyncIterable: () => isAsyncIterable,
    isBrowser: () => isBrowser,
    isIterable: () => isIterable,
    isIterator: () => isIterator,
    isPromise: () => isPromise,
    isPureObject: () => isPureObject,
    isReadableStream: () => isReadableStream,
    isResponse: () => isResponse,
    isWorker: () => isWorker,
    isWritableStream: () => isWritableStream,
    load: () => load,
    loadInBatches: () => loadInBatches,
    makeIterator: () => makeIterator,
    makeLineIterator: () => makeLineIterator,
    makeNumberedLineIterator: () => makeNumberedLineIterator,
    makeStream: () => makeStream,
    makeTextDecoderIterator: () => makeTextDecoderIterator,
    makeTextEncoderIterator: () => makeTextEncoderIterator,
    parse: () => parse,
    parseInBatches: () => parseInBatches,
    parseSync: () => parseSync,
    readArrayBuffer: () => readArrayBuffer,
    readFileSync: () => readFileSync2,
    registerLoaders: () => registerLoaders,
    resolvePath: () => resolvePath,
    save: () => save,
    saveSync: () => saveSync,
    selectLoader: () => selectLoader,
    selectLoaderSync: () => selectLoaderSync,
    self: () => self_,
    setLoaderOptions: () => setGlobalOptions,
    setPathPrefix: () => setPathPrefix,
    window: () => window_,
    writeFile: () => writeFile2,
    writeFileSync: () => writeFileSync2
  });
  var init_src3 = __esm({
    "src/index.ts"() {
      init_fetch_file();
      init_read_array_buffer();
      init_read_file();
      init_write_file();
      init_loader_options();
      init_register_loaders();
      init_select_loader();
      init_parse();
      init_parse_sync();
      init_parse_in_batches();
      init_load();
      init_load_in_batches();
      init_encode();
      init_save();
      init_src2();
      init_src2();
      init_make_iterator();
      init_make_dom_stream();
      init_null_loader();
      init_src2();
      init_fetch_progress();
      init_browser_filesystem();
      init_register_loaders();
      init_src2();
      init_src2();
      init_src2();
      init_src2();
      init_is_type();
    }
  });

  // src/bundle.ts
  var require_bundle = __commonJS({
    "src/bundle.ts"(exports, module) {
      var moduleExports = (init_src3(), src_exports);
      globalThis.loaders = globalThis.loaders || {};
      module.exports = Object.assign(globalThis.loaders, moduleExports);
    }
  });
  require_bundle();
})();
