{"name": "@loaders.gl/core", "version": "3.4.15", "description": "The core API for working with loaders.gl loaders and writers", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/visgl/loaders.gl"}, "keywords": ["webgl", "loader", "3d", "mesh", "point cloud"], "types": "dist/index.d.ts", "main": "dist/es5/index.js", "module": "dist/esm/index.js", "sideEffects": false, "browser": {"fs": false, "stream": false, "./src/iterators/make-stream/make-node-stream.ts": "./src/iterators/make-stream/make-dom-stream.ts", "./dist/es5/iterators/make-stream/make-node-stream.js": "./dist/es5/iterators/make-stream/make-dom-stream.js", "./dist/esm/iterators/make-stream/make-node-stream.js": "./dist/esm/iterators/make-stream/make-dom-stream.js"}, "files": ["src", "dist", "README.md"], "scripts": {"pre-build": "npm run build-bundle && npm run build-worker", "build-bundle": "esbuild src/bundle.ts --bundle --outfile=dist/dist.min.js", "build-worker": "esbuild src/workers/null-worker.ts --bundle --outfile=dist/null-worker.js --define:__VERSION__=\\\"$npm_package_version\\\""}, "dependencies": {"@babel/runtime": "^7.3.1", "@loaders.gl/loader-utils": "3.4.15", "@loaders.gl/worker-utils": "3.4.15", "@probe.gl/log": "^3.5.0"}, "gitHead": "19e941d5805568e449ef9092490d6568a4853298"}