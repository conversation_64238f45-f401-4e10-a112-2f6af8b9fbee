{"name": "@loaders.gl/gltf", "version": "3.4.15", "description": "Framework-independent loader for the glTF format", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/visgl/loaders.gl"}, "keywords": ["webgl", "loader", "3d", "mesh", "point cloud", "GLB", "glTF"], "types": "dist/index.d.ts", "main": "dist/es5/index.js", "module": "dist/esm/index.js", "sideEffects": false, "files": ["src", "dist", "bin", "README.md"], "scripts": {"pre-build": "npm run build-bundle", "build-bundle": "esbuild src/bundle.ts --bundle --outfile=dist/dist.min.js"}, "dependencies": {"@loaders.gl/draco": "3.4.15", "@loaders.gl/images": "3.4.15", "@loaders.gl/loader-utils": "3.4.15", "@loaders.gl/textures": "3.4.15", "@math.gl/core": "^3.5.1"}, "gitHead": "19e941d5805568e449ef9092490d6568a4853298"}