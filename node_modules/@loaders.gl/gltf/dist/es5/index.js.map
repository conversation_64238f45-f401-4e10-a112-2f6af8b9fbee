{"version": 3, "file": "index.js", "names": ["_gltfLoader", "require", "_gltfWriter", "_glb<PERSON><PERSON>der", "_glbWriter", "_gltfScenegraph", "_interopRequireDefault", "_postProcessGltf", "_gltfUtils"], "sources": ["../../src/index.ts"], "sourcesContent": ["/* eslint-disable camelcase, indent */\nexport type {GLB} from './lib/types/glb-types';\nexport type {\n  GLTF,\n  GLTFAccessor,\n  GLTFBuffer,\n  GLTFBufferView,\n  GLTFMeshPrimitive,\n  GLTFMesh,\n  GLTFNode,\n  GLTFMaterial,\n  GLTFSampler,\n  GLTFScene,\n  GLTFSkin,\n  GLTFTexture,\n  GLTFImage,\n  GLTFPostprocessed,\n  GLTFAccessorPostprocessed,\n  GLTFNodePostprocessed,\n  GLTFMaterialPostprocessed,\n  GLTFMeshPostprocessed,\n  GLTFMeshPrimitivePostprocessed,\n  GLTFImagePostprocessed,\n  GLTFTexturePostprocessed,\n  // The following extensions are handled by the GLTFLoader and removed from the parsed glTF (disable via options.gltf.excludeExtensions)\n  GLTF_KHR_binary_glTF,\n  GLTF_KHR_draco_mesh_compression,\n  GLTF_KHR_texture_basisu,\n  GLTF_EXT_meshopt_compression,\n  GLTF_EXT_texture_webp,\n  GLTF_EXT_feature_metadata,\n  GLTF_EXT_feature_metadata_primitive,\n  GLTF_EXT_feature_metadata_attribute\n} from './lib/types/gltf-types';\n\n// glTF loader/writer definition objects\nexport {GLTFLoader} from './gltf-loader';\nexport {GLTFWriter} from './gltf-writer';\n\n// GLB Loader & Writer (for custom formats that want to leverage the GLB binary \"envelope\")\nexport {GLBLoader} from './glb-loader';\nexport {GLBWriter} from './glb-writer';\n\n// glTF Data Access Helper Class\nexport {default as GLTFScenegraph} from './lib/api/gltf-scenegraph';\nexport {postProcessGLTF} from './lib/api/post-process-gltf';\nexport {getMemoryUsageGLTF as _getMemoryUsageGLTF} from './lib/gltf-utils/gltf-utils';\nexport type {Mesh} from './lib/types/gltf-json-schema';\nexport type {GLTFObject} from './lib/types/gltf-types';\nexport type {Node, Accessor, Image} from './lib/types/gltf-postprocessed-schema';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA,IAAAA,WAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AAGA,IAAAE,UAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AAGA,IAAAI,eAAA,GAAAC,sBAAA,CAAAL,OAAA;AACA,IAAAM,gBAAA,GAAAN,OAAA;AACA,IAAAO,UAAA,GAAAP,OAAA"}