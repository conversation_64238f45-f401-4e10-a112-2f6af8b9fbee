"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports._TypecheckGLBLoader = exports.GLTFWriter = void 0;
var _version = require("./lib/utils/version");
var _encodeGltf = require("./lib/encoders/encode-gltf");
const GLTFWriter = {
  name: 'glTF',
  id: 'gltf',
  module: 'gltf',
  version: _version.VERSION,
  extensions: ['glb'],
  mimeTypes: ['model/gltf-binary'],
  binary: true,
  encodeSync,
  options: {
    gltf: {}
  }
};
exports.GLTFWriter = GLTFWriter;
function encodeSync(gltf) {
  let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  const {
    byteOffset = 0
  } = options;
  const byteLength = (0, _encodeGltf.encodeGLTFSync)(gltf, null, byteOffset, options);
  const arrayBuffer = new ArrayBuffer(byteLength);
  const dataView = new DataView(arrayBuffer);
  (0, _encodeGltf.encodeGLTFSync)(gltf, dataView, byteOffset, options);
  return arrayBuffer;
}
const _TypecheckGLBLoader = GLTFWriter;
exports._TypecheckGLBLoader = _TypecheckGLBLoader;
//# sourceMappingURL=gltf-writer.js.map