{"version": 3, "file": "gltf-writer.js", "names": ["_version", "require", "_encodeGltf", "GLTFWriter", "name", "id", "module", "version", "VERSION", "extensions", "mimeTypes", "binary", "encodeSync", "options", "gltf", "exports", "arguments", "length", "undefined", "byteOffset", "byteLength", "encodeGLTFSync", "arrayBuffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dataView", "DataView", "_TypecheckGLBLoader"], "sources": ["../../src/gltf-writer.ts"], "sourcesContent": ["import type {Writer, WriterOptions} from '@loaders.gl/loader-utils';\nimport {VERSION} from './lib/utils/version';\nimport {encodeGLTFSync} from './lib/encoders/encode-gltf';\n\nexport type GLTFWriterOptions = WriterOptions & {\n  gltf?: {};\n  byteOffset?: number;\n};\n\n/**\n * GLTF exporter\n */\nexport const GLTFWriter = {\n  name: 'glTF',\n  id: 'gltf',\n  module: 'gltf',\n  version: VERSION,\n\n  extensions: ['glb'], // We only support encoding to binary GLB, not to JSON GLTF\n  mimeTypes: ['model/gltf-binary'], // 'model/gltf+json',\n  binary: true,\n\n  encodeSync,\n\n  options: {\n    gltf: {}\n  }\n};\n\nfunction encodeSync(gltf, options: GLTFWriterOptions = {}) {\n  const {byteOffset = 0} = options;\n\n  // Calculate length, then create arraybuffer and encode\n  const byteLength = encodeGLTFSync(gltf, null, byteOffset, options);\n  const arrayBuffer = new ArrayBuffer(byteLength);\n  const dataView = new DataView(arrayBuffer);\n  encodeGLTFSync(gltf, dataView, byteOffset, options);\n\n  return arrayBuffer;\n}\n\n// TYPE TESTS - TODO find a better way than exporting junk\nexport const _TypecheckGLBLoader: Writer = GLTFWriter;\n"], "mappings": ";;;;;;AACA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AAUO,MAAME,UAAU,GAAG;EACxBC,IAAI,EAAE,MAAM;EACZC,EAAE,EAAE,MAAM;EACVC,MAAM,EAAE,MAAM;EACdC,OAAO,EAAEC,gBAAO;EAEhBC,UAAU,EAAE,CAAC,KAAK,CAAC;EACnBC,SAAS,EAAE,CAAC,mBAAmB,CAAC;EAChCC,MAAM,EAAE,IAAI;EAEZC,UAAU;EAEVC,OAAO,EAAE;IACPC,IAAI,EAAE,CAAC;EACT;AACF,CAAC;AAACC,OAAA,CAAAZ,UAAA,GAAAA,UAAA;AAEF,SAASS,UAAUA,CAACE,IAAI,EAAmC;EAAA,IAAjCD,OAA0B,GAAAG,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACvD,MAAM;IAACG,UAAU,GAAG;EAAC,CAAC,GAAGN,OAAO;EAGhC,MAAMO,UAAU,GAAG,IAAAC,0BAAc,EAACP,IAAI,EAAE,IAAI,EAAEK,UAAU,EAAEN,OAAO,CAAC;EAClE,MAAMS,WAAW,GAAG,IAAIC,WAAW,CAACH,UAAU,CAAC;EAC/C,MAAMI,QAAQ,GAAG,IAAIC,QAAQ,CAACH,WAAW,CAAC;EAC1C,IAAAD,0BAAc,EAACP,IAAI,EAAEU,QAAQ,EAAEL,UAAU,EAAEN,OAAO,CAAC;EAEnD,OAAOS,WAAW;AACpB;AAGO,MAAMI,mBAA2B,GAAGvB,UAAU;AAACY,OAAA,CAAAW,mBAAA,GAAAA,mBAAA"}