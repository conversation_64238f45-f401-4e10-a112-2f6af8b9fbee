"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.decode = decode;
exports.encode = encode;
exports.name = void 0;
exports.preprocess = preprocess;
var _draco = require("@loaders.gl/draco");
var _loaderUtils = require("@loaders.gl/loader-utils");
var _gltfScenegraph = _interopRequireDefault(require("../api/gltf-scenegraph"));
var _gltfAttributeUtils = require("../gltf-utils/gltf-attribute-utils");
const KHR_DRACO_MESH_COMPRESSION = 'KHR_draco_mesh_compression';
const name = KHR_DRACO_MESH_COMPRESSION;
exports.name = name;
function preprocess(gltfData, options, context) {
  const scenegraph = new _gltfScenegraph.default(gltfData);
  for (const primitive of makeMeshPrimitiveIterator(scenegraph)) {
    if (scenegraph.getObjectExtension(primitive, KHR_DRACO_MESH_COMPRESSION)) {}
  }
}
async function decode(gltfData, options, context) {
  var _options$gltf;
  if (!(options !== null && options !== void 0 && (_options$gltf = options.gltf) !== null && _options$gltf !== void 0 && _options$gltf.decompressMeshes)) {
    return;
  }
  const scenegraph = new _gltfScenegraph.default(gltfData);
  const promises = [];
  for (const primitive of makeMeshPrimitiveIterator(scenegraph)) {
    if (scenegraph.getObjectExtension(primitive, KHR_DRACO_MESH_COMPRESSION)) {
      promises.push(decompressPrimitive(scenegraph, primitive, options, context));
    }
  }
  await Promise.all(promises);
  scenegraph.removeExtension(KHR_DRACO_MESH_COMPRESSION);
}
function encode(gltfData) {
  let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  const scenegraph = new _gltfScenegraph.default(gltfData);
  for (const mesh of scenegraph.json.meshes || []) {
    compressMesh(mesh, options);
    scenegraph.addRequiredExtension(KHR_DRACO_MESH_COMPRESSION);
  }
}
async function decompressPrimitive(scenegraph, primitive, options, context) {
  const dracoExtension = scenegraph.getObjectExtension(primitive, KHR_DRACO_MESH_COMPRESSION);
  if (!dracoExtension) {
    return;
  }
  const buffer = scenegraph.getTypedArrayForBufferView(dracoExtension.bufferView);
  const bufferCopy = (0, _loaderUtils.sliceArrayBuffer)(buffer.buffer, buffer.byteOffset);
  const {
    parse
  } = context;
  const dracoOptions = {
    ...options
  };
  delete dracoOptions['3d-tiles'];
  const decodedData = await parse(bufferCopy, _draco.DracoLoader, dracoOptions, context);
  const decodedAttributes = (0, _gltfAttributeUtils.getGLTFAccessors)(decodedData.attributes);
  for (const [attributeName, decodedAttribute] of Object.entries(decodedAttributes)) {
    if (attributeName in primitive.attributes) {
      const accessorIndex = primitive.attributes[attributeName];
      const accessor = scenegraph.getAccessor(accessorIndex);
      if (accessor !== null && accessor !== void 0 && accessor.min && accessor !== null && accessor !== void 0 && accessor.max) {
        decodedAttribute.min = accessor.min;
        decodedAttribute.max = accessor.max;
      }
    }
  }
  primitive.attributes = decodedAttributes;
  if (decodedData.indices) {
    primitive.indices = (0, _gltfAttributeUtils.getGLTFAccessor)(decodedData.indices);
  }
  checkPrimitive(primitive);
}
function compressMesh(attributes, indices) {
  var _context$parseSync;
  let mode = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 4;
  let options = arguments.length > 3 ? arguments[3] : undefined;
  let context = arguments.length > 4 ? arguments[4] : undefined;
  if (!options.DracoWriter) {
    throw new Error('options.gltf.DracoWriter not provided');
  }
  const compressedData = options.DracoWriter.encodeSync({
    attributes
  });
  const decodedData = context === null || context === void 0 ? void 0 : (_context$parseSync = context.parseSync) === null || _context$parseSync === void 0 ? void 0 : _context$parseSync.call(context, {
    attributes
  });
  const fauxAccessors = options._addFauxAttributes(decodedData.attributes);
  const bufferViewIndex = options.addBufferView(compressedData);
  const glTFMesh = {
    primitives: [{
      attributes: fauxAccessors,
      mode,
      extensions: {
        [KHR_DRACO_MESH_COMPRESSION]: {
          bufferView: bufferViewIndex,
          attributes: fauxAccessors
        }
      }
    }]
  };
  return glTFMesh;
}
function checkPrimitive(primitive) {
  if (!primitive.attributes && Object.keys(primitive.attributes).length > 0) {
    throw new Error('glTF: Empty primitive detected: Draco decompression failure?');
  }
}
function* makeMeshPrimitiveIterator(scenegraph) {
  for (const mesh of scenegraph.json.meshes || []) {
    for (const primitive of mesh.primitives) {
      yield primitive;
    }
  }
}
//# sourceMappingURL=KHR_draco_mesh_compression.js.map