{"version": 3, "file": "EXT_texture_webp.js", "names": ["_images", "require", "_gltfScenegraph", "_interopRequireDefault", "EXT_TEXTURE_WEBP", "name", "exports", "preprocess", "gltfData", "options", "scenegraph", "GLTFScenegraph", "isImageFormatSupported", "getRequiredExtensions", "includes", "Error", "concat", "json", "texture", "textures", "extension", "getObjectExtension", "source", "removeObjectExtension", "removeExtension"], "sources": ["../../../../src/lib/extensions/EXT_texture_webp.ts"], "sourcesContent": ["// GLTF EXTENSION: EXT_TEXTURE_WEBP\n// https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/EXT_TEXTURE_WEBP\n/* eslint-disable camelcase */\n\nimport type {GLTF, GLTF_EXT_texture_webp} from '../types/gltf-types';\nimport type {GLTFLoaderOptions} from '../../gltf-loader';\n\nimport {isImageFormatSupported} from '@loaders.gl/images';\nimport GLTFScenegraph from '../api/gltf-scenegraph';\n\nconst EXT_TEXTURE_WEBP = 'EXT_texture_webp';\n\n/** Extension name */\nexport const name = EXT_TEXTURE_WEBP;\n\n/**\n * Replaces a texture source reference with the extension texture\n * Done in preprocess() to prevent load of default image\n */\nexport function preprocess(gltfData: {json: GLTF}, options: GLTFLoaderOptions): void {\n  const scenegraph = new GLTFScenegraph(gltfData);\n\n  if (!isImageFormatSupported('image/webp')) {\n    if (scenegraph.getRequiredExtensions().includes(EXT_TEXTURE_WEBP)) {\n      throw new Error(`gltf: Required extension ${EXT_TEXTURE_WEBP} not supported by browser`);\n    }\n    return;\n  }\n\n  const {json} = scenegraph;\n\n  for (const texture of json.textures || []) {\n    const extension = scenegraph.getObjectExtension<GLTF_EXT_texture_webp>(\n      texture,\n      EXT_TEXTURE_WEBP\n    );\n    if (extension) {\n      // TODO - if multiple texture extensions are present which one wins?\n      texture.source = extension.source;\n    }\n    scenegraph.removeObjectExtension(texture, EXT_TEXTURE_WEBP);\n  }\n\n  // Remove the top-level extension\n  scenegraph.removeExtension(EXT_TEXTURE_WEBP);\n}\n"], "mappings": ";;;;;;;;AAOA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,eAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEA,MAAMG,gBAAgB,GAAG,kBAAkB;AAGpC,MAAMC,IAAI,GAAGD,gBAAgB;AAACE,OAAA,CAAAD,IAAA,GAAAA,IAAA;AAM9B,SAASE,UAAUA,CAACC,QAAsB,EAAEC,OAA0B,EAAQ;EACnF,MAAMC,UAAU,GAAG,IAAIC,uBAAc,CAACH,QAAQ,CAAC;EAE/C,IAAI,CAAC,IAAAI,8BAAsB,EAAC,YAAY,CAAC,EAAE;IACzC,IAAIF,UAAU,CAACG,qBAAqB,CAAC,CAAC,CAACC,QAAQ,CAACV,gBAAgB,CAAC,EAAE;MACjE,MAAM,IAAIW,KAAK,6BAAAC,MAAA,CAA6BZ,gBAAgB,8BAA2B,CAAC;IAC1F;IACA;EACF;EAEA,MAAM;IAACa;EAAI,CAAC,GAAGP,UAAU;EAEzB,KAAK,MAAMQ,OAAO,IAAID,IAAI,CAACE,QAAQ,IAAI,EAAE,EAAE;IACzC,MAAMC,SAAS,GAAGV,UAAU,CAACW,kBAAkB,CAC7CH,OAAO,EACPd,gBACF,CAAC;IACD,IAAIgB,SAAS,EAAE;MAEbF,OAAO,CAACI,MAAM,GAAGF,SAAS,CAACE,MAAM;IACnC;IACAZ,UAAU,CAACa,qBAAqB,CAACL,OAAO,EAAEd,gBAAgB,CAAC;EAC7D;EAGAM,UAAU,CAACc,eAAe,CAACpB,gBAAgB,CAAC;AAC9C"}