"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.name = void 0;
exports.preprocess = preprocess;
var _gltfScenegraph = _interopRequireDefault(require("../api/gltf-scenegraph"));
const KHR_TEXTURE_BASISU = 'KHR_texture_basisu';
const name = KHR_TEXTURE_BASISU;
exports.name = name;
function preprocess(gltfData, options) {
  const scene = new _gltfScenegraph.default(gltfData);
  const {
    json
  } = scene;
  for (const texture of json.textures || []) {
    const extension = scene.getObjectExtension(texture, KHR_TEXTURE_BASISU);
    if (extension) {
      texture.source = extension.source;
    }
    scene.removeObjectExtension(texture, KHR_TEXTURE_BASISU);
  }
  scene.removeExtension(KHR_TEXTURE_BASISU);
}
//# sourceMappingURL=KHR_texture_basisu.js.map