{"version": 3, "file": "KHR_draco_mesh_compression.js", "names": ["_draco", "require", "_loaderUtils", "_gltfScenegraph", "_interopRequireDefault", "_gltfAttributeUtils", "KHR_DRACO_MESH_COMPRESSION", "name", "exports", "preprocess", "gltfData", "options", "context", "scenegraph", "Scenegraph", "primitive", "makeMeshPrimitiveIterator", "getObjectExtension", "decode", "_options$gltf", "gltf", "decompressMeshes", "promises", "push", "decompressPrimitive", "Promise", "all", "removeExtension", "encode", "arguments", "length", "undefined", "mesh", "json", "meshes", "compressMesh", "addRequiredExtension", "dracoExtension", "buffer", "getTypedArrayForBufferView", "bufferView", "bufferCopy", "sliceArrayBuffer", "byteOffset", "parse", "dracoOptions", "decodedData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "decodedAttributes", "getGLTFAccessors", "attributes", "attributeName", "decodedAttribute", "Object", "entries", "accessorIndex", "accessor", "getAccessor", "min", "max", "indices", "getGLTFAccessor", "checkPrimitive", "_context$parseSync", "mode", "DracoWriter", "Error", "compressedData", "encodeSync", "parseSync", "call", "fauxAccessors", "_addFauxAttributes", "bufferViewIndex", "addBufferView", "glTFMesh", "primitives", "extensions", "keys"], "sources": ["../../../../src/lib/extensions/KHR_draco_mesh_compression.ts"], "sourcesContent": ["// https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_draco_mesh_compression\n// Only TRIANGLES: 0x0004 and TRIANGLE_STRIP: 0x0005 are supported\n/* eslint-disable camelcase */\n\n/* eslint-disable camelcase */\nimport type {\n  GLTF,\n  GLTFAccessor,\n  GLTFMeshPrimitive,\n  GLTF_KHR_draco_mesh_compression\n} from '../types/gltf-types';\nimport type {GLTFLoaderOptions} from '../../gltf-loader';\n\nimport type {LoaderContext} from '@loaders.gl/loader-utils';\nimport {DracoLoader} from '@loaders.gl/draco';\nimport {DracoLoaderOptions, DracoMesh} from '@loaders.gl/draco';\nimport {sliceArrayBuffer} from '@loaders.gl/loader-utils';\nimport {default as Scenegraph} from '../api/gltf-scenegraph';\nimport {getGLTFAccessors, getGLTFAccessor} from '../gltf-utils/gltf-attribute-utils';\n\nconst KHR_DRACO_MESH_COMPRESSION = 'KHR_draco_mesh_compression';\n\n/** Extension name */\nexport const name = KHR_DRACO_MESH_COMPRESSION;\n\nexport function preprocess(\n  gltfData: {json: GLTF},\n  options: GLTFLoaderOptions,\n  context: LoaderContext\n): void {\n  const scenegraph = new Scenegraph(gltfData);\n  for (const primitive of makeMeshPrimitiveIterator(scenegraph)) {\n    if (scenegraph.getObjectExtension(primitive, KHR_DRACO_MESH_COMPRESSION)) {\n      // TODO - Remove fallback accessors to make sure we don't load unnecessary buffers\n    }\n  }\n}\n\nexport async function decode(\n  gltfData: {json: GLTF},\n  options: GLTFLoaderOptions,\n  context: LoaderContext\n): Promise<void> {\n  if (!options?.gltf?.decompressMeshes) {\n    return;\n  }\n\n  const scenegraph = new Scenegraph(gltfData);\n  const promises: Promise<void>[] = [];\n  for (const primitive of makeMeshPrimitiveIterator(scenegraph)) {\n    if (scenegraph.getObjectExtension(primitive, KHR_DRACO_MESH_COMPRESSION)) {\n      promises.push(decompressPrimitive(scenegraph, primitive, options, context));\n    }\n  }\n\n  // Decompress meshes in parallel\n  await Promise.all(promises);\n\n  // We have now decompressed all primitives, so remove the top-level extension\n  scenegraph.removeExtension(KHR_DRACO_MESH_COMPRESSION);\n}\n\nexport function encode(gltfData, options: GLTFLoaderOptions = {}): void {\n  const scenegraph = new Scenegraph(gltfData);\n\n  for (const mesh of scenegraph.json.meshes || []) {\n    // eslint-disable-next-line camelcase\n    // @ts-ignore\n    compressMesh(mesh, options);\n    // NOTE: Only add the extension if something was actually compressed\n    scenegraph.addRequiredExtension(KHR_DRACO_MESH_COMPRESSION);\n  }\n}\n\n// DECODE\n\n// Unpacks one mesh primitive and removes the extension from the primitive\n// DracoDecoder needs to be imported and registered by app\n// Returns: Promise that resolves when all pending draco decoder jobs for this mesh complete\n\n// TODO - Implement fallback behavior per KHR_DRACO_MESH_COMPRESSION spec\n\nasync function decompressPrimitive(\n  scenegraph: Scenegraph,\n  primitive: GLTFMeshPrimitive,\n  options: GLTFLoaderOptions,\n  context: LoaderContext\n): Promise<void> {\n  const dracoExtension = scenegraph.getObjectExtension<GLTF_KHR_draco_mesh_compression>(\n    primitive,\n    KHR_DRACO_MESH_COMPRESSION\n  );\n  if (!dracoExtension) {\n    return;\n  }\n\n  const buffer = scenegraph.getTypedArrayForBufferView(dracoExtension.bufferView);\n  // TODO - parse does not yet deal well with byte offsets embedded in typed arrays. Copy buffer\n  // TODO - remove when `parse` is fixed to handle `byteOffset`s\n  const bufferCopy = sliceArrayBuffer(buffer.buffer, buffer.byteOffset); // , buffer.byteLength);\n\n  const {parse} = context;\n  const dracoOptions: DracoLoaderOptions = {...options};\n\n  // TODO - remove hack: The entire tileset might be included, too expensive to serialize\n  delete dracoOptions['3d-tiles'];\n  const decodedData = (await parse(bufferCopy, DracoLoader, dracoOptions, context)) as DracoMesh;\n\n  const decodedAttributes: {[key: string]: GLTFAccessor} = getGLTFAccessors(decodedData.attributes);\n\n  // Restore min/max values\n  for (const [attributeName, decodedAttribute] of Object.entries(decodedAttributes)) {\n    if (attributeName in primitive.attributes) {\n      const accessorIndex: number = primitive.attributes[attributeName];\n      const accessor = scenegraph.getAccessor(accessorIndex);\n      if (accessor?.min && accessor?.max) {\n        decodedAttribute.min = accessor.min;\n        decodedAttribute.max = accessor.max;\n      }\n    }\n  }\n\n  // @ts-ignore\n  primitive.attributes = decodedAttributes;\n  if (decodedData.indices) {\n    // @ts-ignore\n    primitive.indices = getGLTFAccessor(decodedData.indices);\n  }\n\n  // Extension has been processed, delete it\n  // delete primitive.extensions[KHR_DRACO_MESH_COMPRESSION];\n\n  checkPrimitive(primitive);\n}\n\n// ENCODE\n\n// eslint-disable-next-line max-len\n// Only TRIANGLES: 0x0004 and TRIANGLE_STRIP: 0x0005 are supported\nfunction compressMesh(attributes, indices, mode: number = 4, options, context: LoaderContext) {\n  if (!options.DracoWriter) {\n    throw new Error('options.gltf.DracoWriter not provided');\n  }\n\n  // TODO - use DracoWriter using encode w/ registered DracoWriter...\n  const compressedData = options.DracoWriter.encodeSync({attributes});\n\n  // Draco compression may change the order and number of vertices in a mesh.\n  // To satisfy the requirement that accessors properties be correct for both\n  // compressed and uncompressed data, generators should create uncompressed\n  // attributes and indices using data that has been decompressed from the Draco buffer,\n  // rather than the original source data.\n  // @ts-ignore TODO this needs to be fixed\n  const decodedData = context?.parseSync?.({attributes});\n  const fauxAccessors = options._addFauxAttributes(decodedData.attributes);\n\n  const bufferViewIndex = options.addBufferView(compressedData);\n\n  const glTFMesh = {\n    primitives: [\n      {\n        attributes: fauxAccessors, // TODO - verify with spec\n        mode, // GL.POINTS\n        extensions: {\n          [KHR_DRACO_MESH_COMPRESSION]: {\n            bufferView: bufferViewIndex,\n            attributes: fauxAccessors // TODO - verify with spec\n          }\n        }\n      }\n    ]\n  };\n\n  return glTFMesh;\n}\n\n// UTILS\n\nfunction checkPrimitive(primitive: GLTFMeshPrimitive) {\n  if (!primitive.attributes && Object.keys(primitive.attributes).length > 0) {\n    throw new Error('glTF: Empty primitive detected: Draco decompression failure?');\n  }\n}\n\nfunction* makeMeshPrimitiveIterator(scenegraph) {\n  for (const mesh of scenegraph.json.meshes || []) {\n    for (const primitive of mesh.primitives) {\n      yield primitive;\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;AAcA,IAAAA,MAAA,GAAAC,OAAA;AAEA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,eAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,mBAAA,GAAAJ,OAAA;AAEA,MAAMK,0BAA0B,GAAG,4BAA4B;AAGxD,MAAMC,IAAI,GAAGD,0BAA0B;AAACE,OAAA,CAAAD,IAAA,GAAAA,IAAA;AAExC,SAASE,UAAUA,CACxBC,QAAsB,EACtBC,OAA0B,EAC1BC,OAAsB,EAChB;EACN,MAAMC,UAAU,GAAG,IAAIC,uBAAU,CAACJ,QAAQ,CAAC;EAC3C,KAAK,MAAMK,SAAS,IAAIC,yBAAyB,CAACH,UAAU,CAAC,EAAE;IAC7D,IAAIA,UAAU,CAACI,kBAAkB,CAACF,SAAS,EAAET,0BAA0B,CAAC,EAAE,CAE1E;EACF;AACF;AAEO,eAAeY,MAAMA,CAC1BR,QAAsB,EACtBC,OAA0B,EAC1BC,OAAsB,EACP;EAAA,IAAAO,aAAA;EACf,IAAI,EAACR,OAAO,aAAPA,OAAO,gBAAAQ,aAAA,GAAPR,OAAO,CAAES,IAAI,cAAAD,aAAA,eAAbA,aAAA,CAAeE,gBAAgB,GAAE;IACpC;EACF;EAEA,MAAMR,UAAU,GAAG,IAAIC,uBAAU,CAACJ,QAAQ,CAAC;EAC3C,MAAMY,QAAyB,GAAG,EAAE;EACpC,KAAK,MAAMP,SAAS,IAAIC,yBAAyB,CAACH,UAAU,CAAC,EAAE;IAC7D,IAAIA,UAAU,CAACI,kBAAkB,CAACF,SAAS,EAAET,0BAA0B,CAAC,EAAE;MACxEgB,QAAQ,CAACC,IAAI,CAACC,mBAAmB,CAACX,UAAU,EAAEE,SAAS,EAAEJ,OAAO,EAAEC,OAAO,CAAC,CAAC;IAC7E;EACF;EAGA,MAAMa,OAAO,CAACC,GAAG,CAACJ,QAAQ,CAAC;EAG3BT,UAAU,CAACc,eAAe,CAACrB,0BAA0B,CAAC;AACxD;AAEO,SAASsB,MAAMA,CAAClB,QAAQ,EAAyC;EAAA,IAAvCC,OAA0B,GAAAkB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC9D,MAAMhB,UAAU,GAAG,IAAIC,uBAAU,CAACJ,QAAQ,CAAC;EAE3C,KAAK,MAAMsB,IAAI,IAAInB,UAAU,CAACoB,IAAI,CAACC,MAAM,IAAI,EAAE,EAAE;IAG/CC,YAAY,CAACH,IAAI,EAAErB,OAAO,CAAC;IAE3BE,UAAU,CAACuB,oBAAoB,CAAC9B,0BAA0B,CAAC;EAC7D;AACF;AAUA,eAAekB,mBAAmBA,CAChCX,UAAsB,EACtBE,SAA4B,EAC5BJ,OAA0B,EAC1BC,OAAsB,EACP;EACf,MAAMyB,cAAc,GAAGxB,UAAU,CAACI,kBAAkB,CAClDF,SAAS,EACTT,0BACF,CAAC;EACD,IAAI,CAAC+B,cAAc,EAAE;IACnB;EACF;EAEA,MAAMC,MAAM,GAAGzB,UAAU,CAAC0B,0BAA0B,CAACF,cAAc,CAACG,UAAU,CAAC;EAG/E,MAAMC,UAAU,GAAG,IAAAC,6BAAgB,EAACJ,MAAM,CAACA,MAAM,EAAEA,MAAM,CAACK,UAAU,CAAC;EAErE,MAAM;IAACC;EAAK,CAAC,GAAGhC,OAAO;EACvB,MAAMiC,YAAgC,GAAG;IAAC,GAAGlC;EAAO,CAAC;EAGrD,OAAOkC,YAAY,CAAC,UAAU,CAAC;EAC/B,MAAMC,WAAW,GAAI,MAAMF,KAAK,CAACH,UAAU,EAAEM,kBAAW,EAAEF,YAAY,EAAEjC,OAAO,CAAe;EAE9F,MAAMoC,iBAAgD,GAAG,IAAAC,oCAAgB,EAACH,WAAW,CAACI,UAAU,CAAC;EAGjG,KAAK,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACN,iBAAiB,CAAC,EAAE;IACjF,IAAIG,aAAa,IAAIpC,SAAS,CAACmC,UAAU,EAAE;MACzC,MAAMK,aAAqB,GAAGxC,SAAS,CAACmC,UAAU,CAACC,aAAa,CAAC;MACjE,MAAMK,QAAQ,GAAG3C,UAAU,CAAC4C,WAAW,CAACF,aAAa,CAAC;MACtD,IAAIC,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEE,GAAG,IAAIF,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEG,GAAG,EAAE;QAClCP,gBAAgB,CAACM,GAAG,GAAGF,QAAQ,CAACE,GAAG;QACnCN,gBAAgB,CAACO,GAAG,GAAGH,QAAQ,CAACG,GAAG;MACrC;IACF;EACF;EAGA5C,SAAS,CAACmC,UAAU,GAAGF,iBAAiB;EACxC,IAAIF,WAAW,CAACc,OAAO,EAAE;IAEvB7C,SAAS,CAAC6C,OAAO,GAAG,IAAAC,mCAAe,EAACf,WAAW,CAACc,OAAO,CAAC;EAC1D;EAKAE,cAAc,CAAC/C,SAAS,CAAC;AAC3B;AAMA,SAASoB,YAAYA,CAACe,UAAU,EAAEU,OAAO,EAAqD;EAAA,IAAAG,kBAAA;EAAA,IAAnDC,IAAY,GAAAnC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAElB,OAAO,GAAAkB,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,IAAEnB,OAAsB,GAAAiB,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAC1F,IAAI,CAACpB,OAAO,CAACsD,WAAW,EAAE;IACxB,MAAM,IAAIC,KAAK,CAAC,uCAAuC,CAAC;EAC1D;EAGA,MAAMC,cAAc,GAAGxD,OAAO,CAACsD,WAAW,CAACG,UAAU,CAAC;IAAClB;EAAU,CAAC,CAAC;EAQnE,MAAMJ,WAAW,GAAGlC,OAAO,aAAPA,OAAO,wBAAAmD,kBAAA,GAAPnD,OAAO,CAAEyD,SAAS,cAAAN,kBAAA,uBAAlBA,kBAAA,CAAAO,IAAA,CAAA1D,OAAO,EAAc;IAACsC;EAAU,CAAC,CAAC;EACtD,MAAMqB,aAAa,GAAG5D,OAAO,CAAC6D,kBAAkB,CAAC1B,WAAW,CAACI,UAAU,CAAC;EAExE,MAAMuB,eAAe,GAAG9D,OAAO,CAAC+D,aAAa,CAACP,cAAc,CAAC;EAE7D,MAAMQ,QAAQ,GAAG;IACfC,UAAU,EAAE,CACV;MACE1B,UAAU,EAAEqB,aAAa;MACzBP,IAAI;MACJa,UAAU,EAAE;QACV,CAACvE,0BAA0B,GAAG;UAC5BkC,UAAU,EAAEiC,eAAe;UAC3BvB,UAAU,EAAEqB;QACd;MACF;IACF,CAAC;EAEL,CAAC;EAED,OAAOI,QAAQ;AACjB;AAIA,SAASb,cAAcA,CAAC/C,SAA4B,EAAE;EACpD,IAAI,CAACA,SAAS,CAACmC,UAAU,IAAIG,MAAM,CAACyB,IAAI,CAAC/D,SAAS,CAACmC,UAAU,CAAC,CAACpB,MAAM,GAAG,CAAC,EAAE;IACzE,MAAM,IAAIoC,KAAK,CAAC,8DAA8D,CAAC;EACjF;AACF;AAEA,UAAUlD,yBAAyBA,CAACH,UAAU,EAAE;EAC9C,KAAK,MAAMmB,IAAI,IAAInB,UAAU,CAACoB,IAAI,CAACC,MAAM,IAAI,EAAE,EAAE;IAC/C,KAAK,MAAMnB,SAAS,IAAIiB,IAAI,CAAC4C,UAAU,EAAE;MACvC,MAAM7D,SAAS;IACjB;EACF;AACF"}