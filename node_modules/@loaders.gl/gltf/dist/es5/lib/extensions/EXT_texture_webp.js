"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.name = void 0;
exports.preprocess = preprocess;
var _images = require("@loaders.gl/images");
var _gltfScenegraph = _interopRequireDefault(require("../api/gltf-scenegraph"));
const EXT_TEXTURE_WEBP = 'EXT_texture_webp';
const name = EXT_TEXTURE_WEBP;
exports.name = name;
function preprocess(gltfData, options) {
  const scenegraph = new _gltfScenegraph.default(gltfData);
  if (!(0, _images.isImageFormatSupported)('image/webp')) {
    if (scenegraph.getRequiredExtensions().includes(EXT_TEXTURE_WEBP)) {
      throw new Error("gltf: Required extension ".concat(EXT_TEXTURE_WEBP, " not supported by browser"));
    }
    return;
  }
  const {
    json
  } = scenegraph;
  for (const texture of json.textures || []) {
    const extension = scenegraph.getObjectExtension(texture, EXT_TEXTURE_WEBP);
    if (extension) {
      texture.source = extension.source;
    }
    scenegraph.removeObjectExtension(texture, EXT_TEXTURE_WEBP);
  }
  scenegraph.removeExtension(EXT_TEXTURE_WEBP);
}
//# sourceMappingURL=EXT_texture_webp.js.map