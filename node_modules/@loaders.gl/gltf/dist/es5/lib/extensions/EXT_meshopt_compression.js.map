{"version": 3, "file": "EXT_meshopt_compression.js", "names": ["_gltfScenegraph", "_interopRequireDefault", "require", "_meshoptDecoder", "DEFAULT_MESHOPT_OPTIONS", "byteOffset", "filter", "EXT_MESHOPT_COMPRESSION", "name", "exports", "decode", "gltfData", "options", "_options$gltf", "scenegraph", "GLTFScenegraph", "gltf", "decompressMeshes", "promises", "bufferViewIndex", "json", "bufferViews", "push", "decodeMeshoptBufferView", "Promise", "all", "removeExtension", "bufferView", "meshoptExtension", "getObjectExtension", "byteLength", "byteStride", "count", "mode", "buffer", "bufferIndex", "buffers", "source", "Uint8Array", "arrayBuffer", "result", "meshoptDecodeGltfBuffer"], "sources": ["../../../../src/lib/extensions/EXT_meshopt_compression.ts"], "sourcesContent": ["/* eslint-disable camelcase */\nimport type {GLTF, GLTFBufferView, GLTF_EXT_meshopt_compression} from '../types/gltf-types';\nimport type {GLTFLoaderOptions} from '../../gltf-loader';\nimport GLTFScenegraph from '../api/gltf-scenegraph';\nimport {meshoptDecodeGltfBuffer} from '../../meshopt/meshopt-decoder';\n\n// @ts-ignore\n// eslint-disable-next-line\nconst DEFAULT_MESHOPT_OPTIONS = {\n  byteOffset: 0,\n  filter: 'NONE'\n};\n\n/** Extension name */\nconst EXT_MESHOPT_COMPRESSION = 'EXT_meshopt_compression';\n\nexport const name = EXT_MESHOPT_COMPRESSION;\n\nexport async function decode(gltfData: {json: GLTF}, options: GLTFLoaderOptions) {\n  const scenegraph = new GLTFScenegraph(gltfData);\n\n  if (!options?.gltf?.decompressMeshes) {\n    return;\n  }\n\n  const promises: Promise<any>[] = [];\n  for (const bufferViewIndex of gltfData.json.bufferViews || []) {\n    promises.push(decodeMeshoptBufferView(scenegraph, bufferViewIndex));\n  }\n\n  // Decompress meshes in parallel\n  await Promise.all(promises);\n\n  // We have now decompressed all primitives, so remove the top-level extension\n  scenegraph.removeExtension(EXT_MESHOPT_COMPRESSION);\n}\n\n/** Decode one meshopt buffer view */\nasync function decodeMeshoptBufferView(\n  scenegraph: GLTFScenegraph,\n  bufferView: GLTFBufferView\n): Promise<ArrayBuffer | null> {\n  const meshoptExtension = scenegraph.getObjectExtension<GLTF_EXT_meshopt_compression>(\n    bufferView,\n    EXT_MESHOPT_COMPRESSION\n  );\n  if (meshoptExtension) {\n    const {\n      byteOffset = 0,\n      byteLength = 0,\n      byteStride,\n      count,\n      mode,\n      filter = 'NONE',\n      buffer: bufferIndex\n    } = meshoptExtension;\n    const buffer = scenegraph.gltf.buffers[bufferIndex];\n\n    const source = new Uint8Array(buffer.arrayBuffer, buffer.byteOffset + byteOffset, byteLength);\n    const result = new Uint8Array(\n      scenegraph.gltf.buffers[bufferView.buffer].arrayBuffer,\n      bufferView.byteOffset,\n      bufferView.byteLength\n    );\n    await meshoptDecodeGltfBuffer(result, count, byteStride, source, mode, filter);\n    return result;\n  }\n\n  return null;\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,eAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,eAAA,GAAAD,OAAA;AAIA,MAAME,uBAAuB,GAAG;EAC9BC,UAAU,EAAE,CAAC;EACbC,MAAM,EAAE;AACV,CAAC;AAGD,MAAMC,uBAAuB,GAAG,yBAAyB;AAElD,MAAMC,IAAI,GAAGD,uBAAuB;AAACE,OAAA,CAAAD,IAAA,GAAAA,IAAA;AAErC,eAAeE,MAAMA,CAACC,QAAsB,EAAEC,OAA0B,EAAE;EAAA,IAAAC,aAAA;EAC/E,MAAMC,UAAU,GAAG,IAAIC,uBAAc,CAACJ,QAAQ,CAAC;EAE/C,IAAI,EAACC,OAAO,aAAPA,OAAO,gBAAAC,aAAA,GAAPD,OAAO,CAAEI,IAAI,cAAAH,aAAA,eAAbA,aAAA,CAAeI,gBAAgB,GAAE;IACpC;EACF;EAEA,MAAMC,QAAwB,GAAG,EAAE;EACnC,KAAK,MAAMC,eAAe,IAAIR,QAAQ,CAACS,IAAI,CAACC,WAAW,IAAI,EAAE,EAAE;IAC7DH,QAAQ,CAACI,IAAI,CAACC,uBAAuB,CAACT,UAAU,EAAEK,eAAe,CAAC,CAAC;EACrE;EAGA,MAAMK,OAAO,CAACC,GAAG,CAACP,QAAQ,CAAC;EAG3BJ,UAAU,CAACY,eAAe,CAACnB,uBAAuB,CAAC;AACrD;AAGA,eAAegB,uBAAuBA,CACpCT,UAA0B,EAC1Ba,UAA0B,EACG;EAC7B,MAAMC,gBAAgB,GAAGd,UAAU,CAACe,kBAAkB,CACpDF,UAAU,EACVpB,uBACF,CAAC;EACD,IAAIqB,gBAAgB,EAAE;IACpB,MAAM;MACJvB,UAAU,GAAG,CAAC;MACdyB,UAAU,GAAG,CAAC;MACdC,UAAU;MACVC,KAAK;MACLC,IAAI;MACJ3B,MAAM,GAAG,MAAM;MACf4B,MAAM,EAAEC;IACV,CAAC,GAAGP,gBAAgB;IACpB,MAAMM,MAAM,GAAGpB,UAAU,CAACE,IAAI,CAACoB,OAAO,CAACD,WAAW,CAAC;IAEnD,MAAME,MAAM,GAAG,IAAIC,UAAU,CAACJ,MAAM,CAACK,WAAW,EAAEL,MAAM,CAAC7B,UAAU,GAAGA,UAAU,EAAEyB,UAAU,CAAC;IAC7F,MAAMU,MAAM,GAAG,IAAIF,UAAU,CAC3BxB,UAAU,CAACE,IAAI,CAACoB,OAAO,CAACT,UAAU,CAACO,MAAM,CAAC,CAACK,WAAW,EACtDZ,UAAU,CAACtB,UAAU,EACrBsB,UAAU,CAACG,UACb,CAAC;IACD,MAAM,IAAAW,uCAAuB,EAACD,MAAM,EAAER,KAAK,EAAED,UAAU,EAAEM,MAAM,EAAEJ,IAAI,EAAE3B,MAAM,CAAC;IAC9E,OAAOkC,MAAM;EACf;EAEA,OAAO,IAAI;AACb"}