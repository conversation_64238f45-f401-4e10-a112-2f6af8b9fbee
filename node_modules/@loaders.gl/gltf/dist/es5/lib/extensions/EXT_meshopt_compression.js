"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.decode = decode;
exports.name = void 0;
var _gltfScenegraph = _interopRequireDefault(require("../api/gltf-scenegraph"));
var _meshoptDecoder = require("../../meshopt/meshopt-decoder");
const DEFAULT_MESHOPT_OPTIONS = {
  byteOffset: 0,
  filter: 'NONE'
};
const EXT_MESHOPT_COMPRESSION = 'EXT_meshopt_compression';
const name = EXT_MESHOPT_COMPRESSION;
exports.name = name;
async function decode(gltfData, options) {
  var _options$gltf;
  const scenegraph = new _gltfScenegraph.default(gltfData);
  if (!(options !== null && options !== void 0 && (_options$gltf = options.gltf) !== null && _options$gltf !== void 0 && _options$gltf.decompressMeshes)) {
    return;
  }
  const promises = [];
  for (const bufferViewIndex of gltfData.json.bufferViews || []) {
    promises.push(decodeMeshoptBufferView(scenegraph, bufferViewIndex));
  }
  await Promise.all(promises);
  scenegraph.removeExtension(EXT_MESHOPT_COMPRESSION);
}
async function decodeMeshoptBufferView(scenegraph, bufferView) {
  const meshoptExtension = scenegraph.getObjectExtension(bufferView, EXT_MESHOPT_COMPRESSION);
  if (meshoptExtension) {
    const {
      byteOffset = 0,
      byteLength = 0,
      byteStride,
      count,
      mode,
      filter = 'NONE',
      buffer: bufferIndex
    } = meshoptExtension;
    const buffer = scenegraph.gltf.buffers[bufferIndex];
    const source = new Uint8Array(buffer.arrayBuffer, buffer.byteOffset + byteOffset, byteLength);
    const result = new Uint8Array(scenegraph.gltf.buffers[bufferView.buffer].arrayBuffer, bufferView.byteOffset, bufferView.byteLength);
    await (0, _meshoptDecoder.meshoptDecodeGltfBuffer)(result, count, byteStride, source, mode, filter);
    return result;
  }
  return null;
}
//# sourceMappingURL=EXT_meshopt_compression.js.map