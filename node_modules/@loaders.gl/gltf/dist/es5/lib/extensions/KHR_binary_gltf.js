"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.name = void 0;
exports.preprocess = preprocess;
var _gltfScenegraph = _interopRequireDefault(require("../api/gltf-scenegraph"));
const KHR_BINARY_GLTF = 'KHR_binary_glTF';
const name = KHR_BINARY_GLTF;
exports.name = name;
function preprocess(gltfData) {
  const gltfScenegraph = new _gltfScenegraph.default(gltfData);
  const {
    json
  } = gltfScenegraph;
  for (const image of json.images || []) {
    const extension = gltfScenegraph.getObjectExtension(image, KHR_BINARY_GLTF);
    if (extension) {
      Object.assign(image, extension);
    }
    gltfScenegraph.removeObjectExtension(image, KHR_BINARY_GLTF);
  }
  if (json.buffers && json.buffers[0]) {
    delete json.buffers[0].uri;
  }
  gltfScenegraph.removeExtension(KHR_BINARY_GLTF);
}
//# sourceMappingURL=KHR_binary_gltf.js.map