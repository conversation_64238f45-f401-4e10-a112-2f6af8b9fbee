{"version": 3, "file": "KHR_texture_basisu.js", "names": ["_gltfScenegraph", "_interopRequireDefault", "require", "KHR_TEXTURE_BASISU", "name", "exports", "preprocess", "gltfData", "options", "scene", "GLTFScenegraph", "json", "texture", "textures", "extension", "getObjectExtension", "source", "removeObjectExtension", "removeExtension"], "sources": ["../../../../src/lib/extensions/KHR_texture_basisu.ts"], "sourcesContent": ["// GLTF EXTENSION: KHR_texture_basisu\n// https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_texture_basisu\n/* eslint-disable camelcase */\n\nimport type {GLTF, GLTF_KHR_texture_basisu} from '../types/gltf-types';\nimport type {GLTFLoaderOptions} from '../../gltf-loader';\n\nimport GLTFScenegraph from '../api/gltf-scenegraph';\n\nconst KHR_TEXTURE_BASISU = 'KHR_texture_basisu';\n\n/** Extension name */\nexport const name = KHR_TEXTURE_BASISU;\n\n/**\n * Replaces a texture source reference with the extension texture\n * Done in preprocess() to prevent load of default image\n */\nexport function preprocess(gltfData: {json: GLTF}, options: GLTFLoaderOptions): void {\n  const scene = new GLTFScenegraph(gltfData);\n  const {json} = scene;\n\n  for (const texture of json.textures || []) {\n    const extension = scene.getObjectExtension<GLTF_KHR_texture_basisu>(\n      texture,\n      KHR_TEXTURE_BASISU\n    );\n    if (extension) {\n      // TODO - if multiple texture extensions are present which one wins?\n      texture.source = extension.source;\n    }\n    scene.removeObjectExtension(texture, KHR_TEXTURE_BASISU);\n  }\n\n  // Remove the top-level extension\n  scene.removeExtension(KHR_TEXTURE_BASISU);\n}\n"], "mappings": ";;;;;;;;AAOA,IAAAA,eAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,MAAMC,kBAAkB,GAAG,oBAAoB;AAGxC,MAAMC,IAAI,GAAGD,kBAAkB;AAACE,OAAA,CAAAD,IAAA,GAAAA,IAAA;AAMhC,SAASE,UAAUA,CAACC,QAAsB,EAAEC,OAA0B,EAAQ;EACnF,MAAMC,KAAK,GAAG,IAAIC,uBAAc,CAACH,QAAQ,CAAC;EAC1C,MAAM;IAACI;EAAI,CAAC,GAAGF,KAAK;EAEpB,KAAK,MAAMG,OAAO,IAAID,IAAI,CAACE,QAAQ,IAAI,EAAE,EAAE;IACzC,MAAMC,SAAS,GAAGL,KAAK,CAACM,kBAAkB,CACxCH,OAAO,EACPT,kBACF,CAAC;IACD,IAAIW,SAAS,EAAE;MAEbF,OAAO,CAACI,MAAM,GAAGF,SAAS,CAACE,MAAM;IACnC;IACAP,KAAK,CAACQ,qBAAqB,CAACL,OAAO,EAAET,kBAAkB,CAAC;EAC1D;EAGAM,KAAK,CAACS,eAAe,CAACf,kBAAkB,CAAC;AAC3C"}