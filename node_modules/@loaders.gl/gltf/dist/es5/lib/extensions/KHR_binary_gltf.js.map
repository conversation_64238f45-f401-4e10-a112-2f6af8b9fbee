{"version": 3, "file": "KHR_binary_gltf.js", "names": ["_gltfScenegraph", "_interopRequireDefault", "require", "KHR_BINARY_GLTF", "name", "exports", "preprocess", "gltfData", "gltfScenegraph", "GLTFScenegraph", "json", "image", "images", "extension", "getObjectExtension", "Object", "assign", "removeObjectExtension", "buffers", "uri", "removeExtension"], "sources": ["../../../../src/lib/extensions/KHR_binary_gltf.ts"], "sourcesContent": ["// GLTF 1.0 EXTENSION: KHR_binary_glTF\n// https://github.com/KhronosGroup/glTF/tree/master/extensions/1.0/Khronos/KHR_binary_glTF\n/* eslint-disable camelcase */\n\nimport type {GLTF, GLTF_KHR_binary_glTF} from '../types/gltf-types';\n\nimport GLTFScenegraph from '../api/gltf-scenegraph';\n\nconst KHR_BINARY_GLTF = 'KHR_binary_glTF';\n\n/** Extension name */\nexport const name = KHR_BINARY_GLTF;\n\nexport function preprocess(gltfData: {json: GLTF}): void {\n  const gltfScenegraph = new GLTFScenegraph(gltfData);\n  const {json} = gltfScenegraph;\n\n  // Note: json.buffers.binary_glTF also needs to be replaced\n  // This is currently done during gltf normalization\n\n  // Image and shader nodes can have the extension\n  // https://github.com/KhronosGroup/glTF/blob/master/extensions/1.0/Khronos/KHR_binary_glTF/schema/image.KHR_binary_glTF.schema.json\n  for (const image of json.images || []) {\n    const extension = gltfScenegraph.getObjectExtension<GLTF_KHR_binary_glTF>(\n      image,\n      KHR_BINARY_GLTF\n    );\n    // The data in the extension is valid as glTF 2.0 data inside the object, so just copy it in\n    if (extension) {\n      Object.assign(image, extension);\n    }\n    gltfScenegraph.removeObjectExtension(image, KHR_BINARY_GLTF);\n  }\n\n  // TODO shaders - At least traverse and throw error if used?\n  // https://github.com/KhronosGroup/glTF/blob/master/extensions/1.0/Khronos/KHR_binary_glTF/schema/shader.KHR_binary_glTF.schema.json\n\n  // glTF v1 one files have a partially formed URI field that is not expected in (and causes problems in) 2.0\n  if (json.buffers && json.buffers[0]) {\n    delete json.buffers[0].uri;\n  }\n\n  // Remove the top-level extension as it has now been processed\n  gltfScenegraph.removeExtension(KHR_BINARY_GLTF);\n}\n\n// KHR_binary_gltf is a 1.0 extension that is supported natively by 2.0\n// export function encode() {\n//   throw new Error(KHR_BINARY_GLTF);\n// }\n"], "mappings": ";;;;;;;;AAMA,IAAAA,eAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,MAAMC,eAAe,GAAG,iBAAiB;AAGlC,MAAMC,IAAI,GAAGD,eAAe;AAACE,OAAA,CAAAD,IAAA,GAAAA,IAAA;AAE7B,SAASE,UAAUA,CAACC,QAAsB,EAAQ;EACvD,MAAMC,cAAc,GAAG,IAAIC,uBAAc,CAACF,QAAQ,CAAC;EACnD,MAAM;IAACG;EAAI,CAAC,GAAGF,cAAc;EAO7B,KAAK,MAAMG,KAAK,IAAID,IAAI,CAACE,MAAM,IAAI,EAAE,EAAE;IACrC,MAAMC,SAAS,GAAGL,cAAc,CAACM,kBAAkB,CACjDH,KAAK,EACLR,eACF,CAAC;IAED,IAAIU,SAAS,EAAE;MACbE,MAAM,CAACC,MAAM,CAACL,KAAK,EAAEE,SAAS,CAAC;IACjC;IACAL,cAAc,CAACS,qBAAqB,CAACN,KAAK,EAAER,eAAe,CAAC;EAC9D;EAMA,IAAIO,IAAI,CAACQ,OAAO,IAAIR,IAAI,CAACQ,OAAO,CAAC,CAAC,CAAC,EAAE;IACnC,OAAOR,IAAI,CAACQ,OAAO,CAAC,CAAC,CAAC,CAACC,GAAG;EAC5B;EAGAX,cAAc,CAACY,eAAe,CAACjB,eAAe,CAAC;AACjD"}