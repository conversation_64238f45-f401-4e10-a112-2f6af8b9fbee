{"version": 3, "file": "KHR_lights_punctual.js", "names": ["_assert", "require", "_gltfScenegraph", "_interopRequireDefault", "KHR_LIGHTS_PUNCTUAL", "name", "exports", "decode", "gltfData", "gltfScenegraph", "GLTFScenegraph", "json", "extension", "getExtension", "lights", "removeExtension", "node", "nodes", "nodeExtension", "getObjectExtension", "light", "removeObjectExtension", "encode", "addExtension", "assert", "addObjectExtension"], "sources": ["../../../../../src/lib/extensions/deprecated/KHR_lights_punctual.ts"], "sourcesContent": ["// GLTF EXTENSION: KHR_lights_punctual\n// https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_lights_punctual\n\nimport type {GLTF} from '../../types/gltf-types';\n\nimport {assert} from '../../utils/assert';\nimport GLTFScenegraph from '../../api/gltf-scenegraph';\n\nconst KHR_LIGHTS_PUNCTUAL = 'KHR_lights_punctual';\n\nexport const name = KHR_LIGHTS_PUNCTUAL;\n\nexport async function decode(gltfData: {json: GLTF}): Promise<void> {\n  const gltfScenegraph = new GLTFScenegraph(gltfData);\n  const {json} = gltfScenegraph;\n\n  // Move the light array out of the extension and remove the extension\n  const extension = gltfScenegraph.getExtension(KHR_LIGHTS_PUNCTUAL);\n  if (extension) {\n    // @ts-ignore\n    gltfScenegraph.json.lights = extension.lights;\n    gltfScenegraph.removeExtension(KHR_LIGHTS_PUNCTUAL);\n  }\n\n  // Any nodes that have the extension, add lights field pointing to light object\n  // and remove the extension\n  for (const node of json.nodes || []) {\n    const nodeExtension = gltfScenegraph.getObjectExtension(node, KHR_LIGHTS_PUNCTUAL);\n    if (nodeExtension) {\n      // @ts-ignore\n      node.light = nodeExtension.light;\n    }\n    gltfScenegraph.removeObjectExtension(node, KHR_LIGHTS_PUNCTUAL);\n  }\n}\n\n// Move the light ar ray out of the extension and remove the extension\nexport async function encode(gltfData): Promise<void> {\n  const gltfScenegraph = new GLTFScenegraph(gltfData);\n  const {json} = gltfScenegraph;\n\n  // @ts-ignore\n  if (json.lights) {\n    const extension = gltfScenegraph.addExtension(KHR_LIGHTS_PUNCTUAL);\n    // @ts-ignore\n    assert(!extension.lights);\n    // @ts-ignore\n    extension.lights = json.lights;\n    // @ts-ignore\n    delete json.lights;\n  }\n\n  // Any nodes that have lights field pointing to light object\n  // add the extension\n  // @ts-ignore\n  if (gltfScenegraph.json.lights) {\n    // @ts-ignore\n    for (const light of gltfScenegraph.json.lights) {\n      const node = light.node;\n      gltfScenegraph.addObjectExtension(node, KHR_LIGHTS_PUNCTUAL, light);\n    }\n    // @ts-ignore\n    delete gltfScenegraph.json.lights;\n  }\n}\n"], "mappings": ";;;;;;;;;AAKA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,eAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEA,MAAMG,mBAAmB,GAAG,qBAAqB;AAE1C,MAAMC,IAAI,GAAGD,mBAAmB;AAACE,OAAA,CAAAD,IAAA,GAAAA,IAAA;AAEjC,eAAeE,MAAMA,CAACC,QAAsB,EAAiB;EAClE,MAAMC,cAAc,GAAG,IAAIC,uBAAc,CAACF,QAAQ,CAAC;EACnD,MAAM;IAACG;EAAI,CAAC,GAAGF,cAAc;EAG7B,MAAMG,SAAS,GAAGH,cAAc,CAACI,YAAY,CAACT,mBAAmB,CAAC;EAClE,IAAIQ,SAAS,EAAE;IAEbH,cAAc,CAACE,IAAI,CAACG,MAAM,GAAGF,SAAS,CAACE,MAAM;IAC7CL,cAAc,CAACM,eAAe,CAACX,mBAAmB,CAAC;EACrD;EAIA,KAAK,MAAMY,IAAI,IAAIL,IAAI,CAACM,KAAK,IAAI,EAAE,EAAE;IACnC,MAAMC,aAAa,GAAGT,cAAc,CAACU,kBAAkB,CAACH,IAAI,EAAEZ,mBAAmB,CAAC;IAClF,IAAIc,aAAa,EAAE;MAEjBF,IAAI,CAACI,KAAK,GAAGF,aAAa,CAACE,KAAK;IAClC;IACAX,cAAc,CAACY,qBAAqB,CAACL,IAAI,EAAEZ,mBAAmB,CAAC;EACjE;AACF;AAGO,eAAekB,MAAMA,CAACd,QAAQ,EAAiB;EACpD,MAAMC,cAAc,GAAG,IAAIC,uBAAc,CAACF,QAAQ,CAAC;EACnD,MAAM;IAACG;EAAI,CAAC,GAAGF,cAAc;EAG7B,IAAIE,IAAI,CAACG,MAAM,EAAE;IACf,MAAMF,SAAS,GAAGH,cAAc,CAACc,YAAY,CAACnB,mBAAmB,CAAC;IAElE,IAAAoB,cAAM,EAAC,CAACZ,SAAS,CAACE,MAAM,CAAC;IAEzBF,SAAS,CAACE,MAAM,GAAGH,IAAI,CAACG,MAAM;IAE9B,OAAOH,IAAI,CAACG,MAAM;EACpB;EAKA,IAAIL,cAAc,CAACE,IAAI,CAACG,MAAM,EAAE;IAE9B,KAAK,MAAMM,KAAK,IAAIX,cAAc,CAACE,IAAI,CAACG,MAAM,EAAE;MAC9C,MAAME,IAAI,GAAGI,KAAK,CAACJ,IAAI;MACvBP,cAAc,CAACgB,kBAAkB,CAACT,IAAI,EAAEZ,mBAAmB,EAAEgB,KAAK,CAAC;IACrE;IAEA,OAAOX,cAAc,CAACE,IAAI,CAACG,MAAM;EACnC;AACF"}