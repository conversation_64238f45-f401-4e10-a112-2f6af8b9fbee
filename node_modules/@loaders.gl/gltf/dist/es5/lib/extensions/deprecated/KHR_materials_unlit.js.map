{"version": 3, "file": "KHR_materials_unlit.js", "names": ["_gltfScenegraph", "_interopRequireDefault", "require", "KHR_MATERIALS_UNLIT", "name", "exports", "decode", "gltfData", "gltfScenegraph", "GLTFScenegraph", "json", "material", "materials", "extension", "extensions", "KHR_materials_unlit", "unlit", "removeObjectExtension", "removeExtension", "encode", "addObjectExtension", "addExtension"], "sources": ["../../../../../src/lib/extensions/deprecated/KHR_materials_unlit.ts"], "sourcesContent": ["// GLTF EXTENSION: KHR_materials_unlit\n// https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_unlit\n\nimport type {GLTF} from '../../types/gltf-types';\n\nimport GLTFScenegraph from '../../api/gltf-scenegraph';\n\nconst KHR_MATERIALS_UNLIT = 'KHR_materials_unlit';\n\nexport const name = KHR_MATERIALS_UNLIT;\n\nexport async function decode(gltfData: {json: GLTF}): Promise<void> {\n  const gltfScenegraph = new GLTFScenegraph(gltfData);\n  const {json} = gltfScenegraph;\n\n  // Any nodes that have the extension, add lights field pointing to light object\n  // and remove the extension\n  for (const material of json.materials || []) {\n    const extension = material.extensions && material.extensions.KHR_materials_unlit;\n    if (extension) {\n      // @ts-ignore TODO\n      material.unlit = true;\n    }\n    gltfScenegraph.removeObjectExtension(material, KHR_MATERIALS_UNLIT);\n  }\n\n  // Remove the top-level extension\n  gltfScenegraph.removeExtension(KHR_MATERIALS_UNLIT);\n}\n\nexport function encode(gltfData) {\n  const gltfScenegraph = new GLTFScenegraph(gltfData);\n  const {json} = gltfScenegraph;\n\n  // Any nodes that have lights field pointing to light object\n  // add the extension\n  // @ts-ignore\n  if (gltfScenegraph.materials) {\n    for (const material of json.materials || []) {\n      // @ts-ignore\n      if (material.unlit) {\n        // @ts-ignore\n        delete material.unlit;\n        gltfScenegraph.addObjectExtension(material, KHR_MATERIALS_UNLIT, {});\n        gltfScenegraph.addExtension(KHR_MATERIALS_UNLIT);\n      }\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;AAKA,IAAAA,eAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,MAAMC,mBAAmB,GAAG,qBAAqB;AAE1C,MAAMC,IAAI,GAAGD,mBAAmB;AAACE,OAAA,CAAAD,IAAA,GAAAA,IAAA;AAEjC,eAAeE,MAAMA,CAACC,QAAsB,EAAiB;EAClE,MAAMC,cAAc,GAAG,IAAIC,uBAAc,CAACF,QAAQ,CAAC;EACnD,MAAM;IAACG;EAAI,CAAC,GAAGF,cAAc;EAI7B,KAAK,MAAMG,QAAQ,IAAID,IAAI,CAACE,SAAS,IAAI,EAAE,EAAE;IAC3C,MAAMC,SAAS,GAAGF,QAAQ,CAACG,UAAU,IAAIH,QAAQ,CAACG,UAAU,CAACC,mBAAmB;IAChF,IAAIF,SAAS,EAAE;MAEbF,QAAQ,CAACK,KAAK,GAAG,IAAI;IACvB;IACAR,cAAc,CAACS,qBAAqB,CAACN,QAAQ,EAAER,mBAAmB,CAAC;EACrE;EAGAK,cAAc,CAACU,eAAe,CAACf,mBAAmB,CAAC;AACrD;AAEO,SAASgB,MAAMA,CAACZ,QAAQ,EAAE;EAC/B,MAAMC,cAAc,GAAG,IAAIC,uBAAc,CAACF,QAAQ,CAAC;EACnD,MAAM;IAACG;EAAI,CAAC,GAAGF,cAAc;EAK7B,IAAIA,cAAc,CAACI,SAAS,EAAE;IAC5B,KAAK,MAAMD,QAAQ,IAAID,IAAI,CAACE,SAAS,IAAI,EAAE,EAAE;MAE3C,IAAID,QAAQ,CAACK,KAAK,EAAE;QAElB,OAAOL,QAAQ,CAACK,KAAK;QACrBR,cAAc,CAACY,kBAAkB,CAACT,QAAQ,EAAER,mBAAmB,EAAE,CAAC,CAAC,CAAC;QACpEK,cAAc,CAACa,YAAY,CAAClB,mBAAmB,CAAC;MAClD;IACF;EACF;AACF"}