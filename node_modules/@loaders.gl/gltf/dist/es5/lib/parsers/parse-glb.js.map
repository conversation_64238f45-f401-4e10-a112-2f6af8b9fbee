{"version": 3, "file": "parse-glb.js", "names": ["_loaderUtils", "require", "MAGIC_glTF", "GLB_FILE_HEADER_SIZE", "GLB_CHUNK_HEADER_SIZE", "GLB_CHUNK_TYPE_JSON", "GLB_CHUNK_TYPE_BIN", "GLB_CHUNK_TYPE_JSON_XVIZ_DEPRECATED", "GLB_CHUNK_TYPE_BIX_XVIZ_DEPRECATED", "GLB_V1_CONTENT_FORMAT_JSON", "LE", "getMagicString", "dataView", "byteOffset", "arguments", "length", "undefined", "concat", "String", "fromCharCode", "getUint8", "isGLB", "arrayBuffer", "options", "DataView", "magic", "magic1", "getUint32", "parseGLBSync", "glb", "type", "version", "byteLength", "Object", "assign", "header", "hasBinChunk", "json", "binChunks", "parseGLBV1", "parseGLBV2", "Error", "assert", "contentLength", "contentFormat", "parseJSONChunk", "parseBINChunk", "parseGLBChunksSync", "chunkLength", "chunkFormat", "strict", "padToNBytes", "jsonChunk", "Uint8Array", "buffer", "textDecoder", "TextDecoder", "jsonText", "decode", "JSON", "parse", "push"], "sources": ["../../../../src/lib/parsers/parse-glb.ts"], "sourcesContent": ["/* eslint-disable camelcase, max-statements */\n// https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#glb-file-format-specification\n// https://github.com/KhronosGroup/glTF/tree/master/extensions/1.0/Khronos/KHR_binary_glTF\nimport type {GLB} from '../types/glb-types';\nimport {padToNBytes, assert} from '@loaders.gl/loader-utils';\n\nexport type GLBParseOptions = {\n  magic?: number;\n  strict?: boolean;\n};\n\nconst MAGIC_glTF = 0x676c5446; // glTF in Big-Endian ASCII\n\nconst GLB_FILE_HEADER_SIZE = 12;\nconst GLB_CHUNK_HEADER_SIZE = 8;\n\nconst GLB_CHUNK_TYPE_JSON = 0x4e4f534a;\nconst GLB_CHUNK_TYPE_BIN = 0x004e4942;\nconst GLB_CHUNK_TYPE_JSON_XVIZ_DEPRECATED = 0; // DEPRECATED - Backward compatibility for old xviz files\nconst GLB_CHUNK_TYPE_BIX_XVIZ_DEPRECATED = 1; // DEPRECATED - Backward compatibility for old xviz files\n\nconst GLB_V1_CONTENT_FORMAT_JSON = 0x0;\n\nconst LE = true; // Binary GLTF is little endian.\n\nfunction getMagicString(dataView, byteOffset = 0) {\n  return `\\\n${String.fromCharCode(dataView.getUint8(byteOffset + 0))}\\\n${String.fromCharCode(dataView.getUint8(byteOffset + 1))}\\\n${String.fromCharCode(dataView.getUint8(byteOffset + 2))}\\\n${String.fromCharCode(dataView.getUint8(byteOffset + 3))}`;\n}\n\n// Check if a data view is a GLB\nexport function isGLB(\n  arrayBuffer: ArrayBuffer,\n  byteOffset: number = 0,\n  options: GLBParseOptions = {}\n): boolean {\n  const dataView = new DataView(arrayBuffer);\n  // Check that GLB Header starts with the magic number\n  const {magic = MAGIC_glTF} = options;\n  const magic1 = dataView.getUint32(byteOffset, false);\n  return magic1 === magic || magic1 === MAGIC_glTF;\n}\n\nexport default function parseGLBSync(\n  glb: GLB,\n  arrayBuffer: ArrayBuffer,\n  byteOffset: number = 0,\n  options: GLBParseOptions = {}\n) {\n  // Check that GLB Header starts with the magic number\n  const dataView = new DataView(arrayBuffer);\n\n  // Compare format with GLBLoader documentation\n  const type = getMagicString(dataView, byteOffset + 0);\n  const version = dataView.getUint32(byteOffset + 4, LE); // Version 2 of binary glTF container format\n  const byteLength = dataView.getUint32(byteOffset + 8, LE); // Total byte length of binary file\n\n  Object.assign(glb, {\n    // Put less important stuff in a header, to avoid clutter\n    header: {\n      byteOffset, // Byte offset into the initial arrayBuffer\n      byteLength,\n      hasBinChunk: false\n    },\n\n    type,\n    version,\n\n    json: {},\n    binChunks: []\n  } as GLB);\n\n  byteOffset += GLB_FILE_HEADER_SIZE;\n\n  switch (glb.version) {\n    case 1:\n      // eslint-disable-next-line\n      return parseGLBV1(glb, dataView, byteOffset);\n    case 2:\n      // eslint-disable-next-line\n      return parseGLBV2(glb, dataView, byteOffset, (options = {}));\n    default:\n      throw new Error(`Invalid GLB version ${glb.version}. Only supports v1 and v2.`);\n  }\n}\n\nfunction parseGLBV1(glb: GLB, dataView: DataView, byteOffset: number): number {\n  // Sanity: ensure file is big enough to hold at least the headers\n  assert(glb.header.byteLength > GLB_FILE_HEADER_SIZE + GLB_CHUNK_HEADER_SIZE);\n\n  // Explanation of GLB structure:\n  // https://cloud.githubusercontent.com/assets/3479527/22600725/36b87122-ea55-11e6-9d40-6fd42819fcab.png\n  const contentLength = dataView.getUint32(byteOffset + 0, LE); // Byte length of chunk\n  const contentFormat = dataView.getUint32(byteOffset + 4, LE); // Chunk format as uint32\n  byteOffset += GLB_CHUNK_HEADER_SIZE;\n\n  // GLB v1 only supports a single chunk type\n  assert(contentFormat === GLB_V1_CONTENT_FORMAT_JSON);\n\n  parseJSONChunk(glb, dataView, byteOffset, contentLength);\n  // No need to call the function padToBytes() from parseJSONChunk()\n  byteOffset += contentLength;\n  byteOffset += parseBINChunk(glb, dataView, byteOffset, glb.header.byteLength);\n\n  return byteOffset;\n}\n\nfunction parseGLBV2(\n  glb: GLB,\n  dataView: DataView,\n  byteOffset: number,\n  options: GLBParseOptions\n): number {\n  // Sanity: ensure file is big enough to hold at least the first chunk header\n  assert(glb.header.byteLength > GLB_FILE_HEADER_SIZE + GLB_CHUNK_HEADER_SIZE);\n\n  parseGLBChunksSync(glb, dataView, byteOffset, options);\n\n  return byteOffset + glb.header.byteLength;\n}\n\nfunction parseGLBChunksSync(\n  glb: GLB,\n  dataView: DataView,\n  byteOffset: number,\n  options: GLBParseOptions\n) {\n  // Per spec we must iterate over chunks, ignoring all except JSON and BIN\n  // Iterate as long as there is space left for another chunk header\n  while (byteOffset + 8 <= glb.header.byteLength) {\n    const chunkLength = dataView.getUint32(byteOffset + 0, LE); // Byte length of chunk\n    const chunkFormat = dataView.getUint32(byteOffset + 4, LE); // Chunk format as uint32\n    byteOffset += GLB_CHUNK_HEADER_SIZE;\n\n    // Per spec we must iterate over chunks, ignoring all except JSON and BIN\n    switch (chunkFormat) {\n      case GLB_CHUNK_TYPE_JSON:\n        parseJSONChunk(glb, dataView, byteOffset, chunkLength);\n        break;\n      case GLB_CHUNK_TYPE_BIN:\n        parseBINChunk(glb, dataView, byteOffset, chunkLength);\n        break;\n\n      // Backward compatibility for very old xviz files\n      case GLB_CHUNK_TYPE_JSON_XVIZ_DEPRECATED:\n        if (!options.strict) {\n          parseJSONChunk(glb, dataView, byteOffset, chunkLength);\n        }\n        break;\n      case GLB_CHUNK_TYPE_BIX_XVIZ_DEPRECATED:\n        if (!options.strict) {\n          parseBINChunk(glb, dataView, byteOffset, chunkLength);\n        }\n        break;\n\n      default:\n        // Ignore, per spec\n        // console.warn(`Unknown GLB chunk type`); // eslint-disable-line\n        break;\n    }\n\n    byteOffset += padToNBytes(chunkLength, 4);\n  }\n\n  return byteOffset;\n}\n\n// Parse a GLB JSON chunk\nfunction parseJSONChunk(glb: GLB, dataView: DataView, byteOffset: number, chunkLength: number) {\n  // 1. Create a \"view\" of the binary encoded JSON data inside the GLB\n  const jsonChunk = new Uint8Array(dataView.buffer, byteOffset, chunkLength);\n\n  // 2. Decode the JSON binary array into clear text\n  const textDecoder = new TextDecoder('utf8');\n  const jsonText = textDecoder.decode(jsonChunk);\n\n  // 3. Parse the JSON text into a JavaScript data structure\n  glb.json = JSON.parse(jsonText);\n\n  return padToNBytes(chunkLength, 4);\n}\n\n// Parse a GLB BIN chunk\nfunction parseBINChunk(glb: GLB, dataView, byteOffset, chunkLength) {\n  // Note: BIN chunk can be optional\n  glb.header.hasBinChunk = true;\n  glb.binChunks.push({\n    byteOffset,\n    byteLength: chunkLength,\n    arrayBuffer: dataView.buffer\n    // TODO - copy, or create typed array view?\n  });\n\n  return padToNBytes(chunkLength, 4);\n}\n"], "mappings": ";;;;;;;AAIA,IAAAA,YAAA,GAAAC,OAAA;AAOA,MAAMC,UAAU,GAAG,UAAU;AAE7B,MAAMC,oBAAoB,GAAG,EAAE;AAC/B,MAAMC,qBAAqB,GAAG,CAAC;AAE/B,MAAMC,mBAAmB,GAAG,UAAU;AACtC,MAAMC,kBAAkB,GAAG,UAAU;AACrC,MAAMC,mCAAmC,GAAG,CAAC;AAC7C,MAAMC,kCAAkC,GAAG,CAAC;AAE5C,MAAMC,0BAA0B,GAAG,GAAG;AAEtC,MAAMC,EAAE,GAAG,IAAI;AAEf,SAASC,cAAcA,CAACC,QAAQ,EAAkB;EAAA,IAAhBC,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAC9C,UAAAG,MAAA,CACAC,MAAM,CAACC,YAAY,CAACP,QAAQ,CAACQ,QAAQ,CAACP,UAAU,GAAG,CAAC,CAAC,CAAC,EAAAI,MAAA,CACtDC,MAAM,CAACC,YAAY,CAACP,QAAQ,CAACQ,QAAQ,CAACP,UAAU,GAAG,CAAC,CAAC,CAAC,EAAAI,MAAA,CACtDC,MAAM,CAACC,YAAY,CAACP,QAAQ,CAACQ,QAAQ,CAACP,UAAU,GAAG,CAAC,CAAC,CAAC,EAAAI,MAAA,CACtDC,MAAM,CAACC,YAAY,CAACP,QAAQ,CAACQ,QAAQ,CAACP,UAAU,GAAG,CAAC,CAAC,CAAC;AACxD;AAGO,SAASQ,KAAKA,CACnBC,WAAwB,EAGf;EAAA,IAFTT,UAAkB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IACtBS,OAAwB,GAAAT,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAE7B,MAAMF,QAAQ,GAAG,IAAIY,QAAQ,CAACF,WAAW,CAAC;EAE1C,MAAM;IAACG,KAAK,GAAGvB;EAAU,CAAC,GAAGqB,OAAO;EACpC,MAAMG,MAAM,GAAGd,QAAQ,CAACe,SAAS,CAACd,UAAU,EAAE,KAAK,CAAC;EACpD,OAAOa,MAAM,KAAKD,KAAK,IAAIC,MAAM,KAAKxB,UAAU;AAClD;AAEe,SAAS0B,YAAYA,CAClCC,GAAQ,EACRP,WAAwB,EAGxB;EAAA,IAFAT,UAAkB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IACtBS,OAAwB,GAAAT,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAG7B,MAAMF,QAAQ,GAAG,IAAIY,QAAQ,CAACF,WAAW,CAAC;EAG1C,MAAMQ,IAAI,GAAGnB,cAAc,CAACC,QAAQ,EAAEC,UAAU,GAAG,CAAC,CAAC;EACrD,MAAMkB,OAAO,GAAGnB,QAAQ,CAACe,SAAS,CAACd,UAAU,GAAG,CAAC,EAAEH,EAAE,CAAC;EACtD,MAAMsB,UAAU,GAAGpB,QAAQ,CAACe,SAAS,CAACd,UAAU,GAAG,CAAC,EAAEH,EAAE,CAAC;EAEzDuB,MAAM,CAACC,MAAM,CAACL,GAAG,EAAE;IAEjBM,MAAM,EAAE;MACNtB,UAAU;MACVmB,UAAU;MACVI,WAAW,EAAE;IACf,CAAC;IAEDN,IAAI;IACJC,OAAO;IAEPM,IAAI,EAAE,CAAC,CAAC;IACRC,SAAS,EAAE;EACb,CAAQ,CAAC;EAETzB,UAAU,IAAIV,oBAAoB;EAElC,QAAQ0B,GAAG,CAACE,OAAO;IACjB,KAAK,CAAC;MAEJ,OAAOQ,UAAU,CAACV,GAAG,EAAEjB,QAAQ,EAAEC,UAAU,CAAC;IAC9C,KAAK,CAAC;MAEJ,OAAO2B,UAAU,CAACX,GAAG,EAAEjB,QAAQ,EAAEC,UAAU,EAAGU,OAAO,GAAG,CAAC,CAAE,CAAC;IAC9D;MACE,MAAM,IAAIkB,KAAK,wBAAAxB,MAAA,CAAwBY,GAAG,CAACE,OAAO,+BAA4B,CAAC;EACnF;AACF;AAEA,SAASQ,UAAUA,CAACV,GAAQ,EAAEjB,QAAkB,EAAEC,UAAkB,EAAU;EAE5E,IAAA6B,mBAAM,EAACb,GAAG,CAACM,MAAM,CAACH,UAAU,GAAG7B,oBAAoB,GAAGC,qBAAqB,CAAC;EAI5E,MAAMuC,aAAa,GAAG/B,QAAQ,CAACe,SAAS,CAACd,UAAU,GAAG,CAAC,EAAEH,EAAE,CAAC;EAC5D,MAAMkC,aAAa,GAAGhC,QAAQ,CAACe,SAAS,CAACd,UAAU,GAAG,CAAC,EAAEH,EAAE,CAAC;EAC5DG,UAAU,IAAIT,qBAAqB;EAGnC,IAAAsC,mBAAM,EAACE,aAAa,KAAKnC,0BAA0B,CAAC;EAEpDoC,cAAc,CAAChB,GAAG,EAAEjB,QAAQ,EAAEC,UAAU,EAAE8B,aAAa,CAAC;EAExD9B,UAAU,IAAI8B,aAAa;EAC3B9B,UAAU,IAAIiC,aAAa,CAACjB,GAAG,EAAEjB,QAAQ,EAAEC,UAAU,EAAEgB,GAAG,CAACM,MAAM,CAACH,UAAU,CAAC;EAE7E,OAAOnB,UAAU;AACnB;AAEA,SAAS2B,UAAUA,CACjBX,GAAQ,EACRjB,QAAkB,EAClBC,UAAkB,EAClBU,OAAwB,EAChB;EAER,IAAAmB,mBAAM,EAACb,GAAG,CAACM,MAAM,CAACH,UAAU,GAAG7B,oBAAoB,GAAGC,qBAAqB,CAAC;EAE5E2C,kBAAkB,CAAClB,GAAG,EAAEjB,QAAQ,EAAEC,UAAU,EAAEU,OAAO,CAAC;EAEtD,OAAOV,UAAU,GAAGgB,GAAG,CAACM,MAAM,CAACH,UAAU;AAC3C;AAEA,SAASe,kBAAkBA,CACzBlB,GAAQ,EACRjB,QAAkB,EAClBC,UAAkB,EAClBU,OAAwB,EACxB;EAGA,OAAOV,UAAU,GAAG,CAAC,IAAIgB,GAAG,CAACM,MAAM,CAACH,UAAU,EAAE;IAC9C,MAAMgB,WAAW,GAAGpC,QAAQ,CAACe,SAAS,CAACd,UAAU,GAAG,CAAC,EAAEH,EAAE,CAAC;IAC1D,MAAMuC,WAAW,GAAGrC,QAAQ,CAACe,SAAS,CAACd,UAAU,GAAG,CAAC,EAAEH,EAAE,CAAC;IAC1DG,UAAU,IAAIT,qBAAqB;IAGnC,QAAQ6C,WAAW;MACjB,KAAK5C,mBAAmB;QACtBwC,cAAc,CAAChB,GAAG,EAAEjB,QAAQ,EAAEC,UAAU,EAAEmC,WAAW,CAAC;QACtD;MACF,KAAK1C,kBAAkB;QACrBwC,aAAa,CAACjB,GAAG,EAAEjB,QAAQ,EAAEC,UAAU,EAAEmC,WAAW,CAAC;QACrD;MAGF,KAAKzC,mCAAmC;QACtC,IAAI,CAACgB,OAAO,CAAC2B,MAAM,EAAE;UACnBL,cAAc,CAAChB,GAAG,EAAEjB,QAAQ,EAAEC,UAAU,EAAEmC,WAAW,CAAC;QACxD;QACA;MACF,KAAKxC,kCAAkC;QACrC,IAAI,CAACe,OAAO,CAAC2B,MAAM,EAAE;UACnBJ,aAAa,CAACjB,GAAG,EAAEjB,QAAQ,EAAEC,UAAU,EAAEmC,WAAW,CAAC;QACvD;QACA;MAEF;QAGE;IACJ;IAEAnC,UAAU,IAAI,IAAAsC,wBAAW,EAACH,WAAW,EAAE,CAAC,CAAC;EAC3C;EAEA,OAAOnC,UAAU;AACnB;AAGA,SAASgC,cAAcA,CAAChB,GAAQ,EAAEjB,QAAkB,EAAEC,UAAkB,EAAEmC,WAAmB,EAAE;EAE7F,MAAMI,SAAS,GAAG,IAAIC,UAAU,CAACzC,QAAQ,CAAC0C,MAAM,EAAEzC,UAAU,EAAEmC,WAAW,CAAC;EAG1E,MAAMO,WAAW,GAAG,IAAIC,WAAW,CAAC,MAAM,CAAC;EAC3C,MAAMC,QAAQ,GAAGF,WAAW,CAACG,MAAM,CAACN,SAAS,CAAC;EAG9CvB,GAAG,CAACQ,IAAI,GAAGsB,IAAI,CAACC,KAAK,CAACH,QAAQ,CAAC;EAE/B,OAAO,IAAAN,wBAAW,EAACH,WAAW,EAAE,CAAC,CAAC;AACpC;AAGA,SAASF,aAAaA,CAACjB,GAAQ,EAAEjB,QAAQ,EAAEC,UAAU,EAAEmC,WAAW,EAAE;EAElEnB,GAAG,CAACM,MAAM,CAACC,WAAW,GAAG,IAAI;EAC7BP,GAAG,CAACS,SAAS,CAACuB,IAAI,CAAC;IACjBhD,UAAU;IACVmB,UAAU,EAAEgB,WAAW;IACvB1B,WAAW,EAAEV,QAAQ,CAAC0C;EAExB,CAAC,CAAC;EAEF,OAAO,IAAAH,wBAAW,EAACH,WAAW,EAAE,CAAC,CAAC;AACpC"}