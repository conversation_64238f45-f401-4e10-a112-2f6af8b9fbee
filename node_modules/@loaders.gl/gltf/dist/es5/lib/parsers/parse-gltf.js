"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.isGLTF = isGLTF;
exports.parseGLTF = parseGLTF;
var _textures = require("@loaders.gl/textures");
var _images = require("@loaders.gl/images");
var _loaderUtils = require("@loaders.gl/loader-utils");
var _assert = require("../utils/assert");
var _resolveUrl = require("../gltf-utils/resolve-url");
var _getTypedArray = require("../gltf-utils/get-typed-array");
var _gltfExtensions = require("../api/gltf-extensions");
var _normalizeGltfV = require("../api/normalize-gltf-v1");
var _postProcessGltf = require("../api/post-process-gltf");
var _parseGlb = _interopRequireWildcard(require("./parse-glb"));
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
function isGLTF(arrayBuffer, options) {
  const byteOffset = 0;
  return (0, _parseGlb.isGLB)(arrayBuffer, byteOffset, options);
}
async function parseGLTF(gltf, arrayBufferOrString) {
  var _options$gltf, _options$gltf2, _options$gltf3, _options$gltf4;
  let byteOffset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;
  let options = arguments.length > 3 ? arguments[3] : undefined;
  let context = arguments.length > 4 ? arguments[4] : undefined;
  parseGLTFContainerSync(gltf, arrayBufferOrString, byteOffset, options);
  (0, _normalizeGltfV.normalizeGLTFV1)(gltf, {
    normalize: options === null || options === void 0 ? void 0 : (_options$gltf = options.gltf) === null || _options$gltf === void 0 ? void 0 : _options$gltf.normalize
  });
  (0, _gltfExtensions.preprocessExtensions)(gltf, options, context);
  const promises = [];
  if (options !== null && options !== void 0 && (_options$gltf2 = options.gltf) !== null && _options$gltf2 !== void 0 && _options$gltf2.loadBuffers && gltf.json.buffers) {
    await loadBuffers(gltf, options, context);
  }
  if (options !== null && options !== void 0 && (_options$gltf3 = options.gltf) !== null && _options$gltf3 !== void 0 && _options$gltf3.loadImages) {
    const promise = loadImages(gltf, options, context);
    promises.push(promise);
  }
  const promise = (0, _gltfExtensions.decodeExtensions)(gltf, options, context);
  promises.push(promise);
  await Promise.all(promises);
  return options !== null && options !== void 0 && (_options$gltf4 = options.gltf) !== null && _options$gltf4 !== void 0 && _options$gltf4.postProcess ? (0, _postProcessGltf.postProcessGLTF)(gltf, options) : gltf;
}
function parseGLTFContainerSync(gltf, data, byteOffset, options) {
  if (options.uri) {
    gltf.baseUri = options.uri;
  }
  if (data instanceof ArrayBuffer && !(0, _parseGlb.isGLB)(data, byteOffset, options)) {
    const textDecoder = new TextDecoder();
    data = textDecoder.decode(data);
  }
  if (typeof data === 'string') {
    gltf.json = (0, _loaderUtils.parseJSON)(data);
  } else if (data instanceof ArrayBuffer) {
    const glb = {};
    byteOffset = (0, _parseGlb.default)(glb, data, byteOffset, options.glb);
    (0, _assert.assert)(glb.type === 'glTF', "Invalid GLB magic string ".concat(glb.type));
    gltf._glb = glb;
    gltf.json = glb.json;
  } else {
    (0, _assert.assert)(false, 'GLTF: must be ArrayBuffer or string');
  }
  const buffers = gltf.json.buffers || [];
  gltf.buffers = new Array(buffers.length).fill(null);
  if (gltf._glb && gltf._glb.header.hasBinChunk) {
    const {
      binChunks
    } = gltf._glb;
    gltf.buffers[0] = {
      arrayBuffer: binChunks[0].arrayBuffer,
      byteOffset: binChunks[0].byteOffset,
      byteLength: binChunks[0].byteLength
    };
  }
  const images = gltf.json.images || [];
  gltf.images = new Array(images.length).fill({});
}
async function loadBuffers(gltf, options, context) {
  const buffers = gltf.json.buffers || [];
  for (let i = 0; i < buffers.length; ++i) {
    const buffer = buffers[i];
    if (buffer.uri) {
      var _context$fetch, _response$arrayBuffer;
      const {
        fetch
      } = context;
      (0, _assert.assert)(fetch);
      const uri = (0, _resolveUrl.resolveUrl)(buffer.uri, options);
      const response = await (context === null || context === void 0 ? void 0 : (_context$fetch = context.fetch) === null || _context$fetch === void 0 ? void 0 : _context$fetch.call(context, uri));
      const arrayBuffer = await (response === null || response === void 0 ? void 0 : (_response$arrayBuffer = response.arrayBuffer) === null || _response$arrayBuffer === void 0 ? void 0 : _response$arrayBuffer.call(response));
      gltf.buffers[i] = {
        arrayBuffer,
        byteOffset: 0,
        byteLength: arrayBuffer.byteLength
      };
      delete buffer.uri;
    } else if (gltf.buffers[i] === null) {
      gltf.buffers[i] = {
        arrayBuffer: new ArrayBuffer(buffer.byteLength),
        byteOffset: 0,
        byteLength: buffer.byteLength
      };
    }
  }
}
async function loadImages(gltf, options, context) {
  const imageIndices = getReferencesImageIndices(gltf);
  const images = gltf.json.images || [];
  const promises = [];
  for (const imageIndex of imageIndices) {
    promises.push(loadImage(gltf, images[imageIndex], imageIndex, options, context));
  }
  return await Promise.all(promises);
}
function getReferencesImageIndices(gltf) {
  const imageIndices = new Set();
  const textures = gltf.json.textures || [];
  for (const texture of textures) {
    if (texture.source !== undefined) {
      imageIndices.add(texture.source);
    }
  }
  return Array.from(imageIndices).sort();
}
async function loadImage(gltf, image, index, options, context) {
  const {
    fetch,
    parse
  } = context;
  let arrayBuffer;
  if (image.uri && !image.hasOwnProperty('bufferView')) {
    const uri = (0, _resolveUrl.resolveUrl)(image.uri, options);
    const response = await fetch(uri);
    arrayBuffer = await response.arrayBuffer();
    image.bufferView = {
      data: arrayBuffer
    };
  }
  if (Number.isFinite(image.bufferView)) {
    const array = (0, _getTypedArray.getTypedArrayForBufferView)(gltf.json, gltf.buffers, image.bufferView);
    arrayBuffer = (0, _loaderUtils.sliceArrayBuffer)(array.buffer, array.byteOffset, array.byteLength);
  }
  (0, _assert.assert)(arrayBuffer, 'glTF image has no data');
  let parsedImage = await parse(arrayBuffer, [_images.ImageLoader, _textures.BasisLoader], {
    mimeType: image.mimeType,
    basis: options.basis || {
      format: (0, _textures.selectSupportedBasisFormat)()
    }
  }, context);
  if (parsedImage && parsedImage[0]) {
    parsedImage = {
      compressed: true,
      mipmaps: false,
      width: parsedImage[0].width,
      height: parsedImage[0].height,
      data: parsedImage[0]
    };
  }
  gltf.images = gltf.images || [];
  gltf.images[index] = parsedImage;
}
//# sourceMappingURL=parse-gltf.js.map