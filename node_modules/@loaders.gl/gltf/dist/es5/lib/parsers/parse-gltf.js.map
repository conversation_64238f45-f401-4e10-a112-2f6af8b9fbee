{"version": 3, "file": "parse-gltf.js", "names": ["_textures", "require", "_images", "_loaderUtils", "_assert", "_resolveUrl", "_getTypedArray", "_gltfExtensions", "_normalizeGltfV", "_postProcessGltf", "_parseGlb", "_interopRequireWildcard", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "isGLTF", "arrayBuffer", "options", "byteOffset", "isGLB", "parseGLTF", "gltf", "arrayBufferOrString", "_options$gltf", "_options$gltf2", "_options$gltf3", "_options$gltf4", "arguments", "length", "undefined", "context", "parseGLTFContainerSync", "normalizeGLTFV1", "normalize", "preprocessExtensions", "promises", "loadBuffers", "json", "buffers", "loadImages", "promise", "push", "decodeExtensions", "Promise", "all", "postProcess", "postProcessGLTF", "data", "uri", "baseUri", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "textDecoder", "TextDecoder", "decode", "parseJSON", "glb", "parseGLBSync", "assert", "type", "concat", "_glb", "Array", "fill", "header", "hasBinChunk", "binChunks", "byteLength", "images", "i", "buffer", "_context$fetch", "_response$arrayBuffer", "fetch", "resolveUrl", "response", "imageIndices", "getReferencesImageIndices", "imageIndex", "loadImage", "Set", "textures", "texture", "source", "add", "from", "sort", "image", "index", "parse", "bufferView", "Number", "isFinite", "array", "getTypedArrayForBufferView", "sliceArrayBuffer", "parsedImage", "ImageLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mimeType", "basis", "format", "selectSupportedBasisFormat", "compressed", "mipmaps", "width", "height"], "sources": ["../../../../src/lib/parsers/parse-gltf.ts"], "sourcesContent": ["/* eslint-disable camelcase, max-statements, no-restricted-globals */\nimport type {LoaderContext} from '@loaders.gl/loader-utils';\nimport {BasisLoader, selectSupportedBasisFormat} from '@loaders.gl/textures';\nimport type {GLTFLoaderOptions} from '../../gltf-loader';\nimport type {GLB} from '../types/glb-types';\nimport type {GLTFWithBuffers} from '../types/gltf-types';\n\nimport {ImageLoader} from '@loaders.gl/images';\nimport {parseJSON, sliceArrayBuffer} from '@loaders.gl/loader-utils';\nimport {assert} from '../utils/assert';\nimport {resolveUrl} from '../gltf-utils/resolve-url';\nimport {getTypedArrayForBufferView} from '../gltf-utils/get-typed-array';\nimport {preprocessExtensions, decodeExtensions} from '../api/gltf-extensions';\nimport {normalizeGLTFV1} from '../api/normalize-gltf-v1';\nimport {postProcessGLTF} from '../api/post-process-gltf';\nimport parseGLBSync, {isGLB} from './parse-glb';\n\nexport type GLTFParseOptions = {\n  normalize?: boolean;\n  loadImages?: boolean;\n  loadBuffers?: boolean;\n  decompressMeshes?: boolean;\n  postProcess?: boolean;\n  excludeExtensions?: string[];\n};\n\n// export type GLTFOptions = {\n//   gltf?: GLTFParseOptions;\n// };\n\nexport function isGLTF(arrayBuffer, options?): boolean {\n  const byteOffset = 0;\n  return isGLB(arrayBuffer, byteOffset, options);\n}\n\nexport async function parseGLTF(\n  gltf: GLTFWithBuffers,\n  arrayBufferOrString,\n  byteOffset = 0,\n  options: GLTFLoaderOptions,\n  context: LoaderContext\n) {\n  parseGLTFContainerSync(gltf, arrayBufferOrString, byteOffset, options);\n\n  normalizeGLTFV1(gltf, {normalize: options?.gltf?.normalize});\n\n  preprocessExtensions(gltf, options, context);\n\n  const promises: Promise<any>[] = [];\n\n  // Load linked buffers asynchronously and decodes base64 buffers in parallel\n  if (options?.gltf?.loadBuffers && gltf.json.buffers) {\n    await loadBuffers(gltf, options, context);\n  }\n\n  if (options?.gltf?.loadImages) {\n    const promise = loadImages(gltf, options, context);\n    promises.push(promise);\n  }\n\n  const promise = decodeExtensions(gltf, options, context);\n  promises.push(promise);\n\n  // Parallelize image loading and buffer loading/extension decoding\n  await Promise.all(promises);\n\n  // Post processing resolves indices to objects, buffers\n  return options?.gltf?.postProcess ? postProcessGLTF(gltf, options) : gltf;\n}\n\n// `data` - can be ArrayBuffer (GLB), ArrayBuffer (Binary JSON), String (JSON), or Object (parsed JSON)\nfunction parseGLTFContainerSync(gltf, data, byteOffset, options) {\n  // Initialize gltf container\n  if (options.uri) {\n    gltf.baseUri = options.uri;\n  }\n\n  // If data is binary and starting with magic bytes, assume binary JSON text, convert to string\n  if (data instanceof ArrayBuffer && !isGLB(data, byteOffset, options)) {\n    const textDecoder = new TextDecoder();\n    data = textDecoder.decode(data);\n  }\n\n  if (typeof data === 'string') {\n    // If string, try to parse as JSON\n    gltf.json = parseJSON(data);\n  } else if (data instanceof ArrayBuffer) {\n    // If still ArrayBuffer, parse as GLB container\n    const glb: GLB = {} as GLB;\n    byteOffset = parseGLBSync(glb, data, byteOffset, options.glb);\n\n    assert(glb.type === 'glTF', `Invalid GLB magic string ${glb.type}`);\n\n    gltf._glb = glb;\n    gltf.json = glb.json;\n  } else {\n    assert(false, 'GLTF: must be ArrayBuffer or string');\n  }\n\n  // Populate buffers\n  // Create an external buffers array to hold binary data\n  const buffers = gltf.json.buffers || [];\n  gltf.buffers = new Array(buffers.length).fill(null);\n\n  // Populates JSON and some bin chunk info\n  if (gltf._glb && gltf._glb.header.hasBinChunk) {\n    const {binChunks} = gltf._glb;\n    gltf.buffers[0] = {\n      arrayBuffer: binChunks[0].arrayBuffer,\n      byteOffset: binChunks[0].byteOffset,\n      byteLength: binChunks[0].byteLength\n    };\n\n    // TODO - this modifies JSON and is a post processing thing\n    // gltf.json.buffers[0].data = gltf.buffers[0].arrayBuffer;\n    // gltf.json.buffers[0].byteOffset = gltf.buffers[0].byteOffset;\n  }\n\n  // Populate images\n  const images = gltf.json.images || [];\n  gltf.images = new Array(images.length).fill({});\n}\n\n/** Asynchronously fetch and parse buffers, store in buffers array outside of json\n * TODO - traverse gltf and determine which buffers are actually needed\n */\nasync function loadBuffers(gltf: GLTFWithBuffers, options, context: LoaderContext) {\n  // TODO\n  const buffers = gltf.json.buffers || [];\n  for (let i = 0; i < buffers.length; ++i) {\n    const buffer = buffers[i];\n    if (buffer.uri) {\n      const {fetch} = context;\n      assert(fetch);\n\n      const uri = resolveUrl(buffer.uri, options);\n      const response = await context?.fetch?.(uri);\n      const arrayBuffer = await response?.arrayBuffer?.();\n\n      gltf.buffers[i] = {\n        arrayBuffer,\n        byteOffset: 0,\n        byteLength: arrayBuffer.byteLength\n      };\n\n      delete buffer.uri;\n    } else if (gltf.buffers[i] === null) {\n      gltf.buffers[i] = {\n        arrayBuffer: new ArrayBuffer(buffer.byteLength),\n        byteOffset: 0,\n        byteLength: buffer.byteLength\n      };\n    }\n  }\n}\n\n/**\n * Loads all images\n * TODO - traverse gltf and determine which images are actually needed\n * @param gltf\n * @param options\n * @param context\n * @returns\n */\nasync function loadImages(gltf: GLTFWithBuffers, options, context: LoaderContext) {\n  const imageIndices = getReferencesImageIndices(gltf);\n\n  const images = gltf.json.images || [];\n\n  const promises: Promise<any>[] = [];\n  for (const imageIndex of imageIndices) {\n    promises.push(loadImage(gltf, images[imageIndex], imageIndex, options, context));\n  }\n\n  return await Promise.all(promises);\n}\n\n/** Make sure we only load images that are actually referenced by textures */\nfunction getReferencesImageIndices(gltf: GLTFWithBuffers): number[] {\n  const imageIndices = new Set<number>();\n\n  const textures = gltf.json.textures || [];\n  for (const texture of textures) {\n    if (texture.source !== undefined) {\n      imageIndices.add(texture.source);\n    }\n  }\n\n  return Array.from(imageIndices).sort();\n}\n\n/** Asynchronously fetches and parses one image, store in images array outside of json */\nasync function loadImage(\n  gltf: GLTFWithBuffers,\n  image,\n  index: number,\n  options,\n  context: LoaderContext\n) {\n  const {fetch, parse} = context;\n\n  let arrayBuffer;\n\n  if (image.uri && !image.hasOwnProperty('bufferView')) {\n    const uri = resolveUrl(image.uri, options);\n    const response = await fetch(uri);\n    arrayBuffer = await response.arrayBuffer();\n    image.bufferView = {\n      data: arrayBuffer\n    };\n  }\n\n  if (Number.isFinite(image.bufferView)) {\n    const array = getTypedArrayForBufferView(gltf.json, gltf.buffers, image.bufferView);\n    arrayBuffer = sliceArrayBuffer(array.buffer, array.byteOffset, array.byteLength);\n  }\n\n  assert(arrayBuffer, 'glTF image has no data');\n\n  // Call `parse`\n  let parsedImage = await parse(\n    arrayBuffer,\n    [ImageLoader, BasisLoader],\n    {mimeType: image.mimeType, basis: options.basis || {format: selectSupportedBasisFormat()}},\n    context\n  );\n\n  if (parsedImage && parsedImage[0]) {\n    parsedImage = {\n      compressed: true,\n      mipmaps: false,\n      width: parsedImage[0].width,\n      height: parsedImage[0].height,\n      data: parsedImage[0]\n    };\n  }\n  // TODO making sure ImageLoader is overridable by using array of loaders\n  // const parsedImage = await parse(arrayBuffer, [ImageLoader]);\n\n  // Store the loaded image\n  gltf.images = gltf.images || [];\n  gltf.images[index] = parsedImage;\n}\n"], "mappings": ";;;;;;;AAEA,IAAAA,SAAA,GAAAC,OAAA;AAKA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,YAAA,GAAAF,OAAA;AACA,IAAAG,OAAA,GAAAH,OAAA;AACA,IAAAI,WAAA,GAAAJ,OAAA;AACA,IAAAK,cAAA,GAAAL,OAAA;AACA,IAAAM,eAAA,GAAAN,OAAA;AACA,IAAAO,eAAA,GAAAP,OAAA;AACA,IAAAQ,gBAAA,GAAAR,OAAA;AACA,IAAAS,SAAA,GAAAC,uBAAA,CAAAV,OAAA;AAAgD,SAAAW,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAF,wBAAAM,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAezC,SAASW,MAAMA,CAACC,WAAW,EAAEC,OAAQ,EAAW;EACrD,MAAMC,UAAU,GAAG,CAAC;EACpB,OAAO,IAAAC,eAAK,EAACH,WAAW,EAAEE,UAAU,EAAED,OAAO,CAAC;AAChD;AAEO,eAAeG,SAASA,CAC7BC,IAAqB,EACrBC,mBAAmB,EAInB;EAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA;EAAA,IAHAR,UAAU,GAAAS,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IACdV,OAA0B,GAAAU,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,IAC1BC,OAAsB,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAEtBE,sBAAsB,CAACV,IAAI,EAAEC,mBAAmB,EAAEJ,UAAU,EAAED,OAAO,CAAC;EAEtE,IAAAe,+BAAe,EAACX,IAAI,EAAE;IAACY,SAAS,EAAEhB,OAAO,aAAPA,OAAO,wBAAAM,aAAA,GAAPN,OAAO,CAAEI,IAAI,cAAAE,aAAA,uBAAbA,aAAA,CAAeU;EAAS,CAAC,CAAC;EAE5D,IAAAC,oCAAoB,EAACb,IAAI,EAAEJ,OAAO,EAAEa,OAAO,CAAC;EAE5C,MAAMK,QAAwB,GAAG,EAAE;EAGnC,IAAIlB,OAAO,aAAPA,OAAO,gBAAAO,cAAA,GAAPP,OAAO,CAAEI,IAAI,cAAAG,cAAA,eAAbA,cAAA,CAAeY,WAAW,IAAIf,IAAI,CAACgB,IAAI,CAACC,OAAO,EAAE;IACnD,MAAMF,WAAW,CAACf,IAAI,EAAEJ,OAAO,EAAEa,OAAO,CAAC;EAC3C;EAEA,IAAIb,OAAO,aAAPA,OAAO,gBAAAQ,cAAA,GAAPR,OAAO,CAAEI,IAAI,cAAAI,cAAA,eAAbA,cAAA,CAAec,UAAU,EAAE;IAC7B,MAAMC,OAAO,GAAGD,UAAU,CAAClB,IAAI,EAAEJ,OAAO,EAAEa,OAAO,CAAC;IAClDK,QAAQ,CAACM,IAAI,CAACD,OAAO,CAAC;EACxB;EAEA,MAAMA,OAAO,GAAG,IAAAE,gCAAgB,EAACrB,IAAI,EAAEJ,OAAO,EAAEa,OAAO,CAAC;EACxDK,QAAQ,CAACM,IAAI,CAACD,OAAO,CAAC;EAGtB,MAAMG,OAAO,CAACC,GAAG,CAACT,QAAQ,CAAC;EAG3B,OAAOlB,OAAO,aAAPA,OAAO,gBAAAS,cAAA,GAAPT,OAAO,CAAEI,IAAI,cAAAK,cAAA,eAAbA,cAAA,CAAemB,WAAW,GAAG,IAAAC,gCAAe,EAACzB,IAAI,EAAEJ,OAAO,CAAC,GAAGI,IAAI;AAC3E;AAGA,SAASU,sBAAsBA,CAACV,IAAI,EAAE0B,IAAI,EAAE7B,UAAU,EAAED,OAAO,EAAE;EAE/D,IAAIA,OAAO,CAAC+B,GAAG,EAAE;IACf3B,IAAI,CAAC4B,OAAO,GAAGhC,OAAO,CAAC+B,GAAG;EAC5B;EAGA,IAAID,IAAI,YAAYG,WAAW,IAAI,CAAC,IAAA/B,eAAK,EAAC4B,IAAI,EAAE7B,UAAU,EAAED,OAAO,CAAC,EAAE;IACpE,MAAMkC,WAAW,GAAG,IAAIC,WAAW,CAAC,CAAC;IACrCL,IAAI,GAAGI,WAAW,CAACE,MAAM,CAACN,IAAI,CAAC;EACjC;EAEA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAE5B1B,IAAI,CAACgB,IAAI,GAAG,IAAAiB,sBAAS,EAACP,IAAI,CAAC;EAC7B,CAAC,MAAM,IAAIA,IAAI,YAAYG,WAAW,EAAE;IAEtC,MAAMK,GAAQ,GAAG,CAAC,CAAQ;IAC1BrC,UAAU,GAAG,IAAAsC,iBAAY,EAACD,GAAG,EAAER,IAAI,EAAE7B,UAAU,EAAED,OAAO,CAACsC,GAAG,CAAC;IAE7D,IAAAE,cAAM,EAACF,GAAG,CAACG,IAAI,KAAK,MAAM,8BAAAC,MAAA,CAA8BJ,GAAG,CAACG,IAAI,CAAE,CAAC;IAEnErC,IAAI,CAACuC,IAAI,GAAGL,GAAG;IACflC,IAAI,CAACgB,IAAI,GAAGkB,GAAG,CAAClB,IAAI;EACtB,CAAC,MAAM;IACL,IAAAoB,cAAM,EAAC,KAAK,EAAE,qCAAqC,CAAC;EACtD;EAIA,MAAMnB,OAAO,GAAGjB,IAAI,CAACgB,IAAI,CAACC,OAAO,IAAI,EAAE;EACvCjB,IAAI,CAACiB,OAAO,GAAG,IAAIuB,KAAK,CAACvB,OAAO,CAACV,MAAM,CAAC,CAACkC,IAAI,CAAC,IAAI,CAAC;EAGnD,IAAIzC,IAAI,CAACuC,IAAI,IAAIvC,IAAI,CAACuC,IAAI,CAACG,MAAM,CAACC,WAAW,EAAE;IAC7C,MAAM;MAACC;IAAS,CAAC,GAAG5C,IAAI,CAACuC,IAAI;IAC7BvC,IAAI,CAACiB,OAAO,CAAC,CAAC,CAAC,GAAG;MAChBtB,WAAW,EAAEiD,SAAS,CAAC,CAAC,CAAC,CAACjD,WAAW;MACrCE,UAAU,EAAE+C,SAAS,CAAC,CAAC,CAAC,CAAC/C,UAAU;MACnCgD,UAAU,EAAED,SAAS,CAAC,CAAC,CAAC,CAACC;IAC3B,CAAC;EAKH;EAGA,MAAMC,MAAM,GAAG9C,IAAI,CAACgB,IAAI,CAAC8B,MAAM,IAAI,EAAE;EACrC9C,IAAI,CAAC8C,MAAM,GAAG,IAAIN,KAAK,CAACM,MAAM,CAACvC,MAAM,CAAC,CAACkC,IAAI,CAAC,CAAC,CAAC,CAAC;AACjD;AAKA,eAAe1B,WAAWA,CAACf,IAAqB,EAAEJ,OAAO,EAAEa,OAAsB,EAAE;EAEjF,MAAMQ,OAAO,GAAGjB,IAAI,CAACgB,IAAI,CAACC,OAAO,IAAI,EAAE;EACvC,KAAK,IAAI8B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9B,OAAO,CAACV,MAAM,EAAE,EAAEwC,CAAC,EAAE;IACvC,MAAMC,MAAM,GAAG/B,OAAO,CAAC8B,CAAC,CAAC;IACzB,IAAIC,MAAM,CAACrB,GAAG,EAAE;MAAA,IAAAsB,cAAA,EAAAC,qBAAA;MACd,MAAM;QAACC;MAAK,CAAC,GAAG1C,OAAO;MACvB,IAAA2B,cAAM,EAACe,KAAK,CAAC;MAEb,MAAMxB,GAAG,GAAG,IAAAyB,sBAAU,EAACJ,MAAM,CAACrB,GAAG,EAAE/B,OAAO,CAAC;MAC3C,MAAMyD,QAAQ,GAAG,OAAM5C,OAAO,aAAPA,OAAO,wBAAAwC,cAAA,GAAPxC,OAAO,CAAE0C,KAAK,cAAAF,cAAA,uBAAdA,cAAA,CAAA1D,IAAA,CAAAkB,OAAO,EAAUkB,GAAG,CAAC;MAC5C,MAAMhC,WAAW,GAAG,OAAM0D,QAAQ,aAARA,QAAQ,wBAAAH,qBAAA,GAARG,QAAQ,CAAE1D,WAAW,cAAAuD,qBAAA,uBAArBA,qBAAA,CAAA3D,IAAA,CAAA8D,QAAwB,CAAC;MAEnDrD,IAAI,CAACiB,OAAO,CAAC8B,CAAC,CAAC,GAAG;QAChBpD,WAAW;QACXE,UAAU,EAAE,CAAC;QACbgD,UAAU,EAAElD,WAAW,CAACkD;MAC1B,CAAC;MAED,OAAOG,MAAM,CAACrB,GAAG;IACnB,CAAC,MAAM,IAAI3B,IAAI,CAACiB,OAAO,CAAC8B,CAAC,CAAC,KAAK,IAAI,EAAE;MACnC/C,IAAI,CAACiB,OAAO,CAAC8B,CAAC,CAAC,GAAG;QAChBpD,WAAW,EAAE,IAAIkC,WAAW,CAACmB,MAAM,CAACH,UAAU,CAAC;QAC/ChD,UAAU,EAAE,CAAC;QACbgD,UAAU,EAAEG,MAAM,CAACH;MACrB,CAAC;IACH;EACF;AACF;AAUA,eAAe3B,UAAUA,CAAClB,IAAqB,EAAEJ,OAAO,EAAEa,OAAsB,EAAE;EAChF,MAAM6C,YAAY,GAAGC,yBAAyB,CAACvD,IAAI,CAAC;EAEpD,MAAM8C,MAAM,GAAG9C,IAAI,CAACgB,IAAI,CAAC8B,MAAM,IAAI,EAAE;EAErC,MAAMhC,QAAwB,GAAG,EAAE;EACnC,KAAK,MAAM0C,UAAU,IAAIF,YAAY,EAAE;IACrCxC,QAAQ,CAACM,IAAI,CAACqC,SAAS,CAACzD,IAAI,EAAE8C,MAAM,CAACU,UAAU,CAAC,EAAEA,UAAU,EAAE5D,OAAO,EAAEa,OAAO,CAAC,CAAC;EAClF;EAEA,OAAO,MAAMa,OAAO,CAACC,GAAG,CAACT,QAAQ,CAAC;AACpC;AAGA,SAASyC,yBAAyBA,CAACvD,IAAqB,EAAY;EAClE,MAAMsD,YAAY,GAAG,IAAII,GAAG,CAAS,CAAC;EAEtC,MAAMC,QAAQ,GAAG3D,IAAI,CAACgB,IAAI,CAAC2C,QAAQ,IAAI,EAAE;EACzC,KAAK,MAAMC,OAAO,IAAID,QAAQ,EAAE;IAC9B,IAAIC,OAAO,CAACC,MAAM,KAAKrD,SAAS,EAAE;MAChC8C,YAAY,CAACQ,GAAG,CAACF,OAAO,CAACC,MAAM,CAAC;IAClC;EACF;EAEA,OAAOrB,KAAK,CAACuB,IAAI,CAACT,YAAY,CAAC,CAACU,IAAI,CAAC,CAAC;AACxC;AAGA,eAAeP,SAASA,CACtBzD,IAAqB,EACrBiE,KAAK,EACLC,KAAa,EACbtE,OAAO,EACPa,OAAsB,EACtB;EACA,MAAM;IAAC0C,KAAK;IAAEgB;EAAK,CAAC,GAAG1D,OAAO;EAE9B,IAAId,WAAW;EAEf,IAAIsE,KAAK,CAACtC,GAAG,IAAI,CAACsC,KAAK,CAAC3E,cAAc,CAAC,YAAY,CAAC,EAAE;IACpD,MAAMqC,GAAG,GAAG,IAAAyB,sBAAU,EAACa,KAAK,CAACtC,GAAG,EAAE/B,OAAO,CAAC;IAC1C,MAAMyD,QAAQ,GAAG,MAAMF,KAAK,CAACxB,GAAG,CAAC;IACjChC,WAAW,GAAG,MAAM0D,QAAQ,CAAC1D,WAAW,CAAC,CAAC;IAC1CsE,KAAK,CAACG,UAAU,GAAG;MACjB1C,IAAI,EAAE/B;IACR,CAAC;EACH;EAEA,IAAI0E,MAAM,CAACC,QAAQ,CAACL,KAAK,CAACG,UAAU,CAAC,EAAE;IACrC,MAAMG,KAAK,GAAG,IAAAC,yCAA0B,EAACxE,IAAI,CAACgB,IAAI,EAAEhB,IAAI,CAACiB,OAAO,EAAEgD,KAAK,CAACG,UAAU,CAAC;IACnFzE,WAAW,GAAG,IAAA8E,6BAAgB,EAACF,KAAK,CAACvB,MAAM,EAAEuB,KAAK,CAAC1E,UAAU,EAAE0E,KAAK,CAAC1B,UAAU,CAAC;EAClF;EAEA,IAAAT,cAAM,EAACzC,WAAW,EAAE,wBAAwB,CAAC;EAG7C,IAAI+E,WAAW,GAAG,MAAMP,KAAK,CAC3BxE,WAAW,EACX,CAACgF,mBAAW,EAAEC,qBAAW,CAAC,EAC1B;IAACC,QAAQ,EAAEZ,KAAK,CAACY,QAAQ;IAAEC,KAAK,EAAElF,OAAO,CAACkF,KAAK,IAAI;MAACC,MAAM,EAAE,IAAAC,oCAA0B,EAAC;IAAC;EAAC,CAAC,EAC1FvE,OACF,CAAC;EAED,IAAIiE,WAAW,IAAIA,WAAW,CAAC,CAAC,CAAC,EAAE;IACjCA,WAAW,GAAG;MACZO,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,KAAK;MACdC,KAAK,EAAET,WAAW,CAAC,CAAC,CAAC,CAACS,KAAK;MAC3BC,MAAM,EAAEV,WAAW,CAAC,CAAC,CAAC,CAACU,MAAM;MAC7B1D,IAAI,EAAEgD,WAAW,CAAC,CAAC;IACrB,CAAC;EACH;EAKA1E,IAAI,CAAC8C,MAAM,GAAG9C,IAAI,CAAC8C,MAAM,IAAI,EAAE;EAC/B9C,IAAI,CAAC8C,MAAM,CAACoB,KAAK,CAAC,GAAGQ,WAAW;AAClC"}