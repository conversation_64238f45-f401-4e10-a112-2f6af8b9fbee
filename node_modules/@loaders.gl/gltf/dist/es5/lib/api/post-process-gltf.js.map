{"version": 3, "file": "post-process-gltf.js", "names": ["_assert", "require", "_gltfUtils", "COMPONENTS", "SCALAR", "VEC2", "VEC3", "VEC4", "MAT2", "MAT3", "MAT4", "BYTES", "GL_SAMPLER", "TEXTURE_MAG_FILTER", "TEXTURE_MIN_FILTER", "TEXTURE_WRAP_S", "TEXTURE_WRAP_T", "REPEAT", "LINEAR", "NEAREST_MIPMAP_LINEAR", "SAMPLER_PARAMETER_GLTF_TO_GL", "magFilter", "minFilter", "wrapS", "wrapT", "DEFAULT_SAMPLER", "getBytesFromComponentType", "componentType", "getSizeFromAccessorType", "type", "GLTFPostProcessor", "constructor", "_defineProperty2", "default", "postProcess", "gltf", "options", "arguments", "length", "undefined", "json", "buffers", "images", "baseUri", "assert", "_resolveTree", "bufferViews", "map", "b<PERSON><PERSON><PERSON><PERSON>", "i", "_resolve<PERSON><PERSON><PERSON><PERSON>iew", "image", "_resolveImage", "samplers", "sampler", "_resolveSampler", "textures", "texture", "_resolveTexture", "accessors", "accessor", "_resolveAccessor", "materials", "material", "_resolveMaterial", "meshes", "mesh", "_resolve<PERSON><PERSON>", "nodes", "node", "_resolveNode", "skins", "skin", "_resolveSkin", "scenes", "scene", "_resolveScene", "getScene", "index", "_get", "getNode", "getSkin", "<PERSON><PERSON><PERSON>", "getMaterial", "getAccessor", "getCamera", "getTexture", "getSampler", "getImage", "getBufferView", "<PERSON><PERSON><PERSON><PERSON>", "array", "object", "console", "warn", "concat", "id", "children", "child", "reduce", "accum", "meshIndex", "primitives", "camera", "inverseBindMatrices", "primitive", "attributes", "attribute", "indices", "normalTexture", "occlusionTexture", "occlustionTexture", "emissiveTexture", "emmisiveTexture", "emissiveFactor", "pbrMetallicRoughness", "mr", "baseColorTexture", "metallicRoughnessTexture", "bufferView", "bytesPerComponent", "components", "bytesPerElement", "buffer", "ArrayType", "byteLength", "getAccessorArrayTypeAndLength", "byteOffset", "cutBuffer", "arrayBuffer", "slice", "byteStride", "_getValueFromInterleavedBuffer", "count", "value", "result", "Uint8Array", "elementOffset", "set", "source", "parameters", "key", "glEnum", "_enumSamplerParameter", "preloadedImage", "bufferIndex", "data", "_resolveCamera", "perspective", "orthographic", "postProcessGLTF"], "sources": ["../../../../src/lib/api/post-process-gltf.ts"], "sourcesContent": ["import {assert} from '../utils/assert';\nimport {getAccessorArrayTypeAndLength} from '../gltf-utils/gltf-utils';\nimport {<PERSON><PERSON>erView} from '../types/gltf-json-schema';\nimport {BufferView as BufferViewPostprocessed} from '../types/gltf-postprocessed-schema';\n\n// This is a post processor for loaded glTF files\n// The goal is to make the loaded data easier to use in WebGL applications\n//\n// Functions:\n// * Resolve indexed arrays structure of glTF into a linked tree.\n// * Translate stringified enum keys and values into WebGL constants.\n// * Load images (optional)\n\n// ENUM LOOKUP\n\nconst COMPONENTS = {\n  SCALAR: 1,\n  VEC2: 2,\n  VEC3: 3,\n  VEC4: 4,\n  MAT2: 4,\n  MAT3: 9,\n  MAT4: 16\n};\n\nconst BYTES = {\n  5120: 1, // BYTE\n  5121: 1, // UNSIGNED_BYTE\n  5122: 2, // SHORT\n  5123: 2, // UNSIGNED_SHORT\n  5125: 4, // UNSIGNED_INT\n  5126: 4 // FLOAT\n};\n\nconst GL_SAMPLER = {\n  // Sampler parameters\n  TEXTURE_MAG_FILTER: 0x2800,\n  TEXTURE_MIN_FILTER: 0x2801,\n  TEXTURE_WRAP_S: 0x2802,\n  TEXTURE_WRAP_T: 0x2803,\n\n  // Sampler default values\n  REPEAT: 0x2901,\n  LINEAR: 0x2601,\n  NEAREST_MIPMAP_LINEAR: 0x2702\n};\n\nconst SAMPLER_PARAMETER_GLTF_TO_GL = {\n  magFilter: GL_SAMPLER.TEXTURE_MAG_FILTER,\n  minFilter: GL_SAMPLER.TEXTURE_MIN_FILTER,\n  wrapS: GL_SAMPLER.TEXTURE_WRAP_S,\n  wrapT: GL_SAMPLER.TEXTURE_WRAP_T\n};\n\n// When undefined, a sampler with repeat wrapping and auto filtering should be used.\n// https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#texture\nconst DEFAULT_SAMPLER = {\n  [GL_SAMPLER.TEXTURE_MAG_FILTER]: GL_SAMPLER.LINEAR,\n  [GL_SAMPLER.TEXTURE_MIN_FILTER]: GL_SAMPLER.NEAREST_MIPMAP_LINEAR,\n  [GL_SAMPLER.TEXTURE_WRAP_S]: GL_SAMPLER.REPEAT,\n  [GL_SAMPLER.TEXTURE_WRAP_T]: GL_SAMPLER.REPEAT\n};\n\nfunction getBytesFromComponentType(componentType) {\n  return BYTES[componentType];\n}\n\nfunction getSizeFromAccessorType(type) {\n  return COMPONENTS[type];\n}\n\nclass GLTFPostProcessor {\n  baseUri: string = '';\n  json: Record<string, any> = {};\n  buffers: [] = [];\n  images: [] = [];\n\n  postProcess(gltf, options = {}) {\n    const {json, buffers = [], images = [], baseUri = ''} = gltf;\n    assert(json);\n\n    this.baseUri = baseUri;\n    this.json = json;\n    this.buffers = buffers;\n    this.images = images;\n\n    this._resolveTree(this.json, options);\n\n    return this.json;\n  }\n\n  // Convert indexed glTF structure into tree structure\n  // cross-link index resolution, enum lookup, convenience calculations\n  // eslint-disable-next-line complexity\n  _resolveTree(json, options = {}) {\n    if (json.bufferViews) {\n      json.bufferViews = json.bufferViews.map((bufView, i) => this._resolveBufferView(bufView, i));\n    }\n    if (json.images) {\n      json.images = json.images.map((image, i) => this._resolveImage(image, i));\n    }\n    if (json.samplers) {\n      json.samplers = json.samplers.map((sampler, i) => this._resolveSampler(sampler, i));\n    }\n    if (json.textures) {\n      json.textures = json.textures.map((texture, i) => this._resolveTexture(texture, i));\n    }\n    if (json.accessors) {\n      json.accessors = json.accessors.map((accessor, i) => this._resolveAccessor(accessor, i));\n    }\n    if (json.materials) {\n      json.materials = json.materials.map((material, i) => this._resolveMaterial(material, i));\n    }\n    if (json.meshes) {\n      json.meshes = json.meshes.map((mesh, i) => this._resolveMesh(mesh, i));\n    }\n    if (json.nodes) {\n      json.nodes = json.nodes.map((node, i) => this._resolveNode(node, i));\n    }\n    if (json.skins) {\n      json.skins = json.skins.map((skin, i) => this._resolveSkin(skin, i));\n    }\n    if (json.scenes) {\n      json.scenes = json.scenes.map((scene, i) => this._resolveScene(scene, i));\n    }\n    if (json.scene !== undefined) {\n      json.scene = json.scenes[this.json.scene];\n    }\n  }\n\n  getScene(index) {\n    return this._get('scenes', index);\n  }\n\n  getNode(index) {\n    return this._get('nodes', index);\n  }\n\n  getSkin(index) {\n    return this._get('skins', index);\n  }\n\n  getMesh(index) {\n    return this._get('meshes', index);\n  }\n\n  getMaterial(index) {\n    return this._get('materials', index);\n  }\n\n  getAccessor(index) {\n    return this._get('accessors', index);\n  }\n\n  getCamera(index) {\n    return null; // TODO: fix this\n  }\n\n  getTexture(index) {\n    return this._get('textures', index);\n  }\n\n  getSampler(index) {\n    return this._get('samplers', index);\n  }\n\n  getImage(index) {\n    return this._get('images', index);\n  }\n\n  getBufferView(index) {\n    return this._get('bufferViews', index);\n  }\n\n  getBuffer(index) {\n    return this._get('buffers', index);\n  }\n\n  _get(array, index) {\n    // check if already resolved\n    if (typeof index === 'object') {\n      return index;\n    }\n    const object = this.json[array] && this.json[array][index];\n    if (!object) {\n      console.warn(`glTF file error: Could not find ${array}[${index}]`); // eslint-disable-line\n    }\n    return object;\n  }\n\n  // PARSING HELPERS\n\n  _resolveScene(scene, index) {\n    // scene = {...scene};\n    scene.id = scene.id || `scene-${index}`;\n    scene.nodes = (scene.nodes || []).map((node) => this.getNode(node));\n    return scene;\n  }\n\n  _resolveNode(node, index) {\n    // node = {...node};\n    node.id = node.id || `node-${index}`;\n    if (node.children) {\n      node.children = node.children.map((child) => this.getNode(child));\n    }\n    if (node.mesh !== undefined) {\n      node.mesh = this.getMesh(node.mesh);\n    } else if (node.meshes !== undefined && node.meshes.length) {\n      node.mesh = node.meshes.reduce(\n        (accum, meshIndex) => {\n          const mesh = this.getMesh(meshIndex);\n          accum.id = mesh.id;\n          accum.primitives = accum.primitives.concat(mesh.primitives);\n          return accum;\n        },\n        {primitives: []}\n      );\n    }\n    if (node.camera !== undefined) {\n      node.camera = this.getCamera(node.camera);\n    }\n    if (node.skin !== undefined) {\n      node.skin = this.getSkin(node.skin);\n    }\n    return node;\n  }\n\n  _resolveSkin(skin, index) {\n    // skin = {...skin};\n    skin.id = skin.id || `skin-${index}`;\n    skin.inverseBindMatrices = this.getAccessor(skin.inverseBindMatrices);\n    return skin;\n  }\n\n  _resolveMesh(mesh, index) {\n    // mesh = {...mesh};\n    mesh.id = mesh.id || `mesh-${index}`;\n    if (mesh.primitives) {\n      mesh.primitives = mesh.primitives.map((primitive) => {\n        primitive = {...primitive};\n        const attributes = primitive.attributes;\n        primitive.attributes = {};\n        for (const attribute in attributes) {\n          primitive.attributes[attribute] = this.getAccessor(attributes[attribute]);\n        }\n        if (primitive.indices !== undefined) {\n          primitive.indices = this.getAccessor(primitive.indices);\n        }\n        if (primitive.material !== undefined) {\n          primitive.material = this.getMaterial(primitive.material);\n        }\n        return primitive;\n      });\n    }\n    return mesh;\n  }\n\n  _resolveMaterial(material, index) {\n    // material = {...material};\n    material.id = material.id || `material-${index}`;\n    if (material.normalTexture) {\n      material.normalTexture = {...material.normalTexture};\n      material.normalTexture.texture = this.getTexture(material.normalTexture.index);\n    }\n    if (material.occlusionTexture) {\n      material.occlustionTexture = {...material.occlustionTexture};\n      material.occlusionTexture.texture = this.getTexture(material.occlusionTexture.index);\n    }\n    if (material.emissiveTexture) {\n      material.emmisiveTexture = {...material.emmisiveTexture};\n      material.emissiveTexture.texture = this.getTexture(material.emissiveTexture.index);\n    }\n    if (!material.emissiveFactor) {\n      material.emissiveFactor = material.emmisiveTexture ? [1, 1, 1] : [0, 0, 0];\n    }\n\n    if (material.pbrMetallicRoughness) {\n      material.pbrMetallicRoughness = {...material.pbrMetallicRoughness};\n      const mr = material.pbrMetallicRoughness;\n      if (mr.baseColorTexture) {\n        mr.baseColorTexture = {...mr.baseColorTexture};\n        mr.baseColorTexture.texture = this.getTexture(mr.baseColorTexture.index);\n      }\n      if (mr.metallicRoughnessTexture) {\n        mr.metallicRoughnessTexture = {...mr.metallicRoughnessTexture};\n        mr.metallicRoughnessTexture.texture = this.getTexture(mr.metallicRoughnessTexture.index);\n      }\n    }\n    return material;\n  }\n\n  _resolveAccessor(accessor, index) {\n    // accessor = {...accessor};\n    accessor.id = accessor.id || `accessor-${index}`;\n    if (accessor.bufferView !== undefined) {\n      // Draco encoded meshes don't have bufferView\n      accessor.bufferView = this.getBufferView(accessor.bufferView);\n    }\n\n    // Look up enums\n    accessor.bytesPerComponent = getBytesFromComponentType(accessor.componentType);\n    accessor.components = getSizeFromAccessorType(accessor.type);\n    accessor.bytesPerElement = accessor.bytesPerComponent * accessor.components;\n\n    // Create TypedArray for the accessor\n    // Note: The canonical way to instantiate is to ignore this array and create\n    // WebGLBuffer's using the bufferViews.\n    if (accessor.bufferView) {\n      const buffer = accessor.bufferView.buffer;\n      const {ArrayType, byteLength} = getAccessorArrayTypeAndLength(accessor, accessor.bufferView);\n      const byteOffset =\n        (accessor.bufferView.byteOffset || 0) + (accessor.byteOffset || 0) + buffer.byteOffset;\n      let cutBuffer = buffer.arrayBuffer.slice(byteOffset, byteOffset + byteLength);\n      if (accessor.bufferView.byteStride) {\n        cutBuffer = this._getValueFromInterleavedBuffer(\n          buffer,\n          byteOffset,\n          accessor.bufferView.byteStride,\n          accessor.bytesPerElement,\n          accessor.count\n        );\n      }\n      accessor.value = new ArrayType(cutBuffer);\n    }\n\n    return accessor;\n  }\n\n  /**\n   * Take values of particular accessor from interleaved buffer\n   * various parts of the buffer\n   * @param buffer\n   * @param byteOffset\n   * @param byteStride\n   * @param bytesPerElement\n   * @param count\n   * @returns\n   */\n  _getValueFromInterleavedBuffer(buffer, byteOffset, byteStride, bytesPerElement, count) {\n    const result = new Uint8Array(count * bytesPerElement);\n    for (let i = 0; i < count; i++) {\n      const elementOffset = byteOffset + i * byteStride;\n      result.set(\n        new Uint8Array(buffer.arrayBuffer.slice(elementOffset, elementOffset + bytesPerElement)),\n        i * bytesPerElement\n      );\n    }\n    return result.buffer;\n  }\n\n  _resolveTexture(texture, index) {\n    // texture = {...texture};\n    texture.id = texture.id || `texture-${index}`;\n    texture.sampler = 'sampler' in texture ? this.getSampler(texture.sampler) : DEFAULT_SAMPLER;\n    texture.source = this.getImage(texture.source);\n    return texture;\n  }\n\n  _resolveSampler(sampler, index) {\n    // sampler = {...sampler};\n    sampler.id = sampler.id || `sampler-${index}`;\n    // Map textual parameters to GL parameter values\n    sampler.parameters = {};\n    for (const key in sampler) {\n      const glEnum = this._enumSamplerParameter(key);\n      if (glEnum !== undefined) {\n        sampler.parameters[glEnum] = sampler[key];\n      }\n    }\n    return sampler;\n  }\n\n  _enumSamplerParameter(key) {\n    return SAMPLER_PARAMETER_GLTF_TO_GL[key];\n  }\n\n  _resolveImage(image, index) {\n    // image = {...image};\n    image.id = image.id || `image-${index}`;\n    if (image.bufferView !== undefined) {\n      image.bufferView = this.getBufferView(image.bufferView);\n    }\n\n    // Check if image has been preloaded by the GLTFLoader\n    // If so, link it into the JSON and drop the URI\n    const preloadedImage = this.images[index];\n    if (preloadedImage) {\n      image.image = preloadedImage;\n    }\n\n    return image;\n  }\n\n  _resolveBufferView(bufferView: BufferView, index: number): BufferViewPostprocessed {\n    // bufferView = {...bufferView};\n    const bufferIndex = bufferView.buffer;\n    const result: BufferViewPostprocessed = {\n      id: `bufferView-${index}`,\n      ...bufferView,\n      buffer: this.buffers[bufferIndex]\n    };\n\n    // @ts-expect-error\n    const arrayBuffer = this.buffers[bufferIndex].arrayBuffer;\n    // @ts-expect-error\n    let byteOffset = this.buffers[bufferIndex].byteOffset || 0;\n\n    if ('byteOffset' in bufferView) {\n      byteOffset += bufferView.byteOffset;\n    }\n\n    result.data = new Uint8Array(arrayBuffer, byteOffset, bufferView.byteLength);\n    return result;\n  }\n\n  _resolveCamera(camera, index) {\n    camera.id = camera.id || `camera-${index}`;\n    // TODO - create 4x4 matrices\n    if (camera.perspective) {\n      // camera.matrix = createPerspectiveMatrix(camera.perspective);\n    }\n    if (camera.orthographic) {\n      // camera.matrix = createOrthographicMatrix(camera.orthographic);\n    }\n    return camera;\n  }\n}\n\nexport function postProcessGLTF(gltf, options?) {\n  return new GLTFPostProcessor().postProcess(gltf, options);\n}\n"], "mappings": ";;;;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;AAcA,MAAME,UAAU,GAAG;EACjBC,MAAM,EAAE,CAAC;EACTC,IAAI,EAAE,CAAC;EACPC,IAAI,EAAE,CAAC;EACPC,IAAI,EAAE,CAAC;EACPC,IAAI,EAAE,CAAC;EACPC,IAAI,EAAE,CAAC;EACPC,IAAI,EAAE;AACR,CAAC;AAED,MAAMC,KAAK,GAAG;EACZ,IAAI,EAAE,CAAC;EACP,IAAI,EAAE,CAAC;EACP,IAAI,EAAE,CAAC;EACP,IAAI,EAAE,CAAC;EACP,IAAI,EAAE,CAAC;EACP,IAAI,EAAE;AACR,CAAC;AAED,MAAMC,UAAU,GAAG;EAEjBC,kBAAkB,EAAE,MAAM;EAC1BC,kBAAkB,EAAE,MAAM;EAC1BC,cAAc,EAAE,MAAM;EACtBC,cAAc,EAAE,MAAM;EAGtBC,MAAM,EAAE,MAAM;EACdC,MAAM,EAAE,MAAM;EACdC,qBAAqB,EAAE;AACzB,CAAC;AAED,MAAMC,4BAA4B,GAAG;EACnCC,SAAS,EAAET,UAAU,CAACC,kBAAkB;EACxCS,SAAS,EAAEV,UAAU,CAACE,kBAAkB;EACxCS,KAAK,EAAEX,UAAU,CAACG,cAAc;EAChCS,KAAK,EAAEZ,UAAU,CAACI;AACpB,CAAC;AAID,MAAMS,eAAe,GAAG;EACtB,CAACb,UAAU,CAACC,kBAAkB,GAAGD,UAAU,CAACM,MAAM;EAClD,CAACN,UAAU,CAACE,kBAAkB,GAAGF,UAAU,CAACO,qBAAqB;EACjE,CAACP,UAAU,CAACG,cAAc,GAAGH,UAAU,CAACK,MAAM;EAC9C,CAACL,UAAU,CAACI,cAAc,GAAGJ,UAAU,CAACK;AAC1C,CAAC;AAED,SAASS,yBAAyBA,CAACC,aAAa,EAAE;EAChD,OAAOhB,KAAK,CAACgB,aAAa,CAAC;AAC7B;AAEA,SAASC,uBAAuBA,CAACC,IAAI,EAAE;EACrC,OAAO1B,UAAU,CAAC0B,IAAI,CAAC;AACzB;AAEA,MAAMC,iBAAiB,CAAC;EAAAC,YAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,mBACJ,EAAE;IAAA,IAAAD,gBAAA,CAAAC,OAAA,gBACQ,CAAC,CAAC;IAAA,IAAAD,gBAAA,CAAAC,OAAA,mBAChB,EAAE;IAAA,IAAAD,gBAAA,CAAAC,OAAA,kBACH,EAAE;EAAA;EAEfC,WAAWA,CAACC,IAAI,EAAgB;IAAA,IAAdC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAC5B,MAAM;MAACG,IAAI;MAAEC,OAAO,GAAG,EAAE;MAAEC,MAAM,GAAG,EAAE;MAAEC,OAAO,GAAG;IAAE,CAAC,GAAGR,IAAI;IAC5D,IAAAS,cAAM,EAACJ,IAAI,CAAC;IAEZ,IAAI,CAACG,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACH,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,MAAM,GAAGA,MAAM;IAEpB,IAAI,CAACG,YAAY,CAAC,IAAI,CAACL,IAAI,EAAEJ,OAAO,CAAC;IAErC,OAAO,IAAI,CAACI,IAAI;EAClB;EAKAK,YAAYA,CAACL,IAAI,EAAgB;IAAA,IAAdJ,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAC7B,IAAIG,IAAI,CAACM,WAAW,EAAE;MACpBN,IAAI,CAACM,WAAW,GAAGN,IAAI,CAACM,WAAW,CAACC,GAAG,CAAC,CAACC,OAAO,EAAEC,CAAC,KAAK,IAAI,CAACC,kBAAkB,CAACF,OAAO,EAAEC,CAAC,CAAC,CAAC;IAC9F;IACA,IAAIT,IAAI,CAACE,MAAM,EAAE;MACfF,IAAI,CAACE,MAAM,GAAGF,IAAI,CAACE,MAAM,CAACK,GAAG,CAAC,CAACI,KAAK,EAAEF,CAAC,KAAK,IAAI,CAACG,aAAa,CAACD,KAAK,EAAEF,CAAC,CAAC,CAAC;IAC3E;IACA,IAAIT,IAAI,CAACa,QAAQ,EAAE;MACjBb,IAAI,CAACa,QAAQ,GAAGb,IAAI,CAACa,QAAQ,CAACN,GAAG,CAAC,CAACO,OAAO,EAAEL,CAAC,KAAK,IAAI,CAACM,eAAe,CAACD,OAAO,EAAEL,CAAC,CAAC,CAAC;IACrF;IACA,IAAIT,IAAI,CAACgB,QAAQ,EAAE;MACjBhB,IAAI,CAACgB,QAAQ,GAAGhB,IAAI,CAACgB,QAAQ,CAACT,GAAG,CAAC,CAACU,OAAO,EAAER,CAAC,KAAK,IAAI,CAACS,eAAe,CAACD,OAAO,EAAER,CAAC,CAAC,CAAC;IACrF;IACA,IAAIT,IAAI,CAACmB,SAAS,EAAE;MAClBnB,IAAI,CAACmB,SAAS,GAAGnB,IAAI,CAACmB,SAAS,CAACZ,GAAG,CAAC,CAACa,QAAQ,EAAEX,CAAC,KAAK,IAAI,CAACY,gBAAgB,CAACD,QAAQ,EAAEX,CAAC,CAAC,CAAC;IAC1F;IACA,IAAIT,IAAI,CAACsB,SAAS,EAAE;MAClBtB,IAAI,CAACsB,SAAS,GAAGtB,IAAI,CAACsB,SAAS,CAACf,GAAG,CAAC,CAACgB,QAAQ,EAAEd,CAAC,KAAK,IAAI,CAACe,gBAAgB,CAACD,QAAQ,EAAEd,CAAC,CAAC,CAAC;IAC1F;IACA,IAAIT,IAAI,CAACyB,MAAM,EAAE;MACfzB,IAAI,CAACyB,MAAM,GAAGzB,IAAI,CAACyB,MAAM,CAAClB,GAAG,CAAC,CAACmB,IAAI,EAAEjB,CAAC,KAAK,IAAI,CAACkB,YAAY,CAACD,IAAI,EAAEjB,CAAC,CAAC,CAAC;IACxE;IACA,IAAIT,IAAI,CAAC4B,KAAK,EAAE;MACd5B,IAAI,CAAC4B,KAAK,GAAG5B,IAAI,CAAC4B,KAAK,CAACrB,GAAG,CAAC,CAACsB,IAAI,EAAEpB,CAAC,KAAK,IAAI,CAACqB,YAAY,CAACD,IAAI,EAAEpB,CAAC,CAAC,CAAC;IACtE;IACA,IAAIT,IAAI,CAAC+B,KAAK,EAAE;MACd/B,IAAI,CAAC+B,KAAK,GAAG/B,IAAI,CAAC+B,KAAK,CAACxB,GAAG,CAAC,CAACyB,IAAI,EAAEvB,CAAC,KAAK,IAAI,CAACwB,YAAY,CAACD,IAAI,EAAEvB,CAAC,CAAC,CAAC;IACtE;IACA,IAAIT,IAAI,CAACkC,MAAM,EAAE;MACflC,IAAI,CAACkC,MAAM,GAAGlC,IAAI,CAACkC,MAAM,CAAC3B,GAAG,CAAC,CAAC4B,KAAK,EAAE1B,CAAC,KAAK,IAAI,CAAC2B,aAAa,CAACD,KAAK,EAAE1B,CAAC,CAAC,CAAC;IAC3E;IACA,IAAIT,IAAI,CAACmC,KAAK,KAAKpC,SAAS,EAAE;MAC5BC,IAAI,CAACmC,KAAK,GAAGnC,IAAI,CAACkC,MAAM,CAAC,IAAI,CAAClC,IAAI,CAACmC,KAAK,CAAC;IAC3C;EACF;EAEAE,QAAQA,CAACC,KAAK,EAAE;IACd,OAAO,IAAI,CAACC,IAAI,CAAC,QAAQ,EAAED,KAAK,CAAC;EACnC;EAEAE,OAAOA,CAACF,KAAK,EAAE;IACb,OAAO,IAAI,CAACC,IAAI,CAAC,OAAO,EAAED,KAAK,CAAC;EAClC;EAEAG,OAAOA,CAACH,KAAK,EAAE;IACb,OAAO,IAAI,CAACC,IAAI,CAAC,OAAO,EAAED,KAAK,CAAC;EAClC;EAEAI,OAAOA,CAACJ,KAAK,EAAE;IACb,OAAO,IAAI,CAACC,IAAI,CAAC,QAAQ,EAAED,KAAK,CAAC;EACnC;EAEAK,WAAWA,CAACL,KAAK,EAAE;IACjB,OAAO,IAAI,CAACC,IAAI,CAAC,WAAW,EAAED,KAAK,CAAC;EACtC;EAEAM,WAAWA,CAACN,KAAK,EAAE;IACjB,OAAO,IAAI,CAACC,IAAI,CAAC,WAAW,EAAED,KAAK,CAAC;EACtC;EAEAO,SAASA,CAACP,KAAK,EAAE;IACf,OAAO,IAAI;EACb;EAEAQ,UAAUA,CAACR,KAAK,EAAE;IAChB,OAAO,IAAI,CAACC,IAAI,CAAC,UAAU,EAAED,KAAK,CAAC;EACrC;EAEAS,UAAUA,CAACT,KAAK,EAAE;IAChB,OAAO,IAAI,CAACC,IAAI,CAAC,UAAU,EAAED,KAAK,CAAC;EACrC;EAEAU,QAAQA,CAACV,KAAK,EAAE;IACd,OAAO,IAAI,CAACC,IAAI,CAAC,QAAQ,EAAED,KAAK,CAAC;EACnC;EAEAW,aAAaA,CAACX,KAAK,EAAE;IACnB,OAAO,IAAI,CAACC,IAAI,CAAC,aAAa,EAAED,KAAK,CAAC;EACxC;EAEAY,SAASA,CAACZ,KAAK,EAAE;IACf,OAAO,IAAI,CAACC,IAAI,CAAC,SAAS,EAAED,KAAK,CAAC;EACpC;EAEAC,IAAIA,CAACY,KAAK,EAAEb,KAAK,EAAE;IAEjB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAOA,KAAK;IACd;IACA,MAAMc,MAAM,GAAG,IAAI,CAACpD,IAAI,CAACmD,KAAK,CAAC,IAAI,IAAI,CAACnD,IAAI,CAACmD,KAAK,CAAC,CAACb,KAAK,CAAC;IAC1D,IAAI,CAACc,MAAM,EAAE;MACXC,OAAO,CAACC,IAAI,oCAAAC,MAAA,CAAoCJ,KAAK,OAAAI,MAAA,CAAIjB,KAAK,MAAG,CAAC;IACpE;IACA,OAAOc,MAAM;EACf;EAIAhB,aAAaA,CAACD,KAAK,EAAEG,KAAK,EAAE;IAE1BH,KAAK,CAACqB,EAAE,GAAGrB,KAAK,CAACqB,EAAE,aAAAD,MAAA,CAAajB,KAAK,CAAE;IACvCH,KAAK,CAACP,KAAK,GAAG,CAACO,KAAK,CAACP,KAAK,IAAI,EAAE,EAAErB,GAAG,CAAEsB,IAAI,IAAK,IAAI,CAACW,OAAO,CAACX,IAAI,CAAC,CAAC;IACnE,OAAOM,KAAK;EACd;EAEAL,YAAYA,CAACD,IAAI,EAAES,KAAK,EAAE;IAExBT,IAAI,CAAC2B,EAAE,GAAG3B,IAAI,CAAC2B,EAAE,YAAAD,MAAA,CAAYjB,KAAK,CAAE;IACpC,IAAIT,IAAI,CAAC4B,QAAQ,EAAE;MACjB5B,IAAI,CAAC4B,QAAQ,GAAG5B,IAAI,CAAC4B,QAAQ,CAAClD,GAAG,CAAEmD,KAAK,IAAK,IAAI,CAAClB,OAAO,CAACkB,KAAK,CAAC,CAAC;IACnE;IACA,IAAI7B,IAAI,CAACH,IAAI,KAAK3B,SAAS,EAAE;MAC3B8B,IAAI,CAACH,IAAI,GAAG,IAAI,CAACgB,OAAO,CAACb,IAAI,CAACH,IAAI,CAAC;IACrC,CAAC,MAAM,IAAIG,IAAI,CAACJ,MAAM,KAAK1B,SAAS,IAAI8B,IAAI,CAACJ,MAAM,CAAC3B,MAAM,EAAE;MAC1D+B,IAAI,CAACH,IAAI,GAAGG,IAAI,CAACJ,MAAM,CAACkC,MAAM,CAC5B,CAACC,KAAK,EAAEC,SAAS,KAAK;QACpB,MAAMnC,IAAI,GAAG,IAAI,CAACgB,OAAO,CAACmB,SAAS,CAAC;QACpCD,KAAK,CAACJ,EAAE,GAAG9B,IAAI,CAAC8B,EAAE;QAClBI,KAAK,CAACE,UAAU,GAAGF,KAAK,CAACE,UAAU,CAACP,MAAM,CAAC7B,IAAI,CAACoC,UAAU,CAAC;QAC3D,OAAOF,KAAK;MACd,CAAC,EACD;QAACE,UAAU,EAAE;MAAE,CACjB,CAAC;IACH;IACA,IAAIjC,IAAI,CAACkC,MAAM,KAAKhE,SAAS,EAAE;MAC7B8B,IAAI,CAACkC,MAAM,GAAG,IAAI,CAAClB,SAAS,CAAChB,IAAI,CAACkC,MAAM,CAAC;IAC3C;IACA,IAAIlC,IAAI,CAACG,IAAI,KAAKjC,SAAS,EAAE;MAC3B8B,IAAI,CAACG,IAAI,GAAG,IAAI,CAACS,OAAO,CAACZ,IAAI,CAACG,IAAI,CAAC;IACrC;IACA,OAAOH,IAAI;EACb;EAEAI,YAAYA,CAACD,IAAI,EAAEM,KAAK,EAAE;IAExBN,IAAI,CAACwB,EAAE,GAAGxB,IAAI,CAACwB,EAAE,YAAAD,MAAA,CAAYjB,KAAK,CAAE;IACpCN,IAAI,CAACgC,mBAAmB,GAAG,IAAI,CAACpB,WAAW,CAACZ,IAAI,CAACgC,mBAAmB,CAAC;IACrE,OAAOhC,IAAI;EACb;EAEAL,YAAYA,CAACD,IAAI,EAAEY,KAAK,EAAE;IAExBZ,IAAI,CAAC8B,EAAE,GAAG9B,IAAI,CAAC8B,EAAE,YAAAD,MAAA,CAAYjB,KAAK,CAAE;IACpC,IAAIZ,IAAI,CAACoC,UAAU,EAAE;MACnBpC,IAAI,CAACoC,UAAU,GAAGpC,IAAI,CAACoC,UAAU,CAACvD,GAAG,CAAE0D,SAAS,IAAK;QACnDA,SAAS,GAAG;UAAC,GAAGA;QAAS,CAAC;QAC1B,MAAMC,UAAU,GAAGD,SAAS,CAACC,UAAU;QACvCD,SAAS,CAACC,UAAU,GAAG,CAAC,CAAC;QACzB,KAAK,MAAMC,SAAS,IAAID,UAAU,EAAE;UAClCD,SAAS,CAACC,UAAU,CAACC,SAAS,CAAC,GAAG,IAAI,CAACvB,WAAW,CAACsB,UAAU,CAACC,SAAS,CAAC,CAAC;QAC3E;QACA,IAAIF,SAAS,CAACG,OAAO,KAAKrE,SAAS,EAAE;UACnCkE,SAAS,CAACG,OAAO,GAAG,IAAI,CAACxB,WAAW,CAACqB,SAAS,CAACG,OAAO,CAAC;QACzD;QACA,IAAIH,SAAS,CAAC1C,QAAQ,KAAKxB,SAAS,EAAE;UACpCkE,SAAS,CAAC1C,QAAQ,GAAG,IAAI,CAACoB,WAAW,CAACsB,SAAS,CAAC1C,QAAQ,CAAC;QAC3D;QACA,OAAO0C,SAAS;MAClB,CAAC,CAAC;IACJ;IACA,OAAOvC,IAAI;EACb;EAEAF,gBAAgBA,CAACD,QAAQ,EAAEe,KAAK,EAAE;IAEhCf,QAAQ,CAACiC,EAAE,GAAGjC,QAAQ,CAACiC,EAAE,gBAAAD,MAAA,CAAgBjB,KAAK,CAAE;IAChD,IAAIf,QAAQ,CAAC8C,aAAa,EAAE;MAC1B9C,QAAQ,CAAC8C,aAAa,GAAG;QAAC,GAAG9C,QAAQ,CAAC8C;MAAa,CAAC;MACpD9C,QAAQ,CAAC8C,aAAa,CAACpD,OAAO,GAAG,IAAI,CAAC6B,UAAU,CAACvB,QAAQ,CAAC8C,aAAa,CAAC/B,KAAK,CAAC;IAChF;IACA,IAAIf,QAAQ,CAAC+C,gBAAgB,EAAE;MAC7B/C,QAAQ,CAACgD,iBAAiB,GAAG;QAAC,GAAGhD,QAAQ,CAACgD;MAAiB,CAAC;MAC5DhD,QAAQ,CAAC+C,gBAAgB,CAACrD,OAAO,GAAG,IAAI,CAAC6B,UAAU,CAACvB,QAAQ,CAAC+C,gBAAgB,CAAChC,KAAK,CAAC;IACtF;IACA,IAAIf,QAAQ,CAACiD,eAAe,EAAE;MAC5BjD,QAAQ,CAACkD,eAAe,GAAG;QAAC,GAAGlD,QAAQ,CAACkD;MAAe,CAAC;MACxDlD,QAAQ,CAACiD,eAAe,CAACvD,OAAO,GAAG,IAAI,CAAC6B,UAAU,CAACvB,QAAQ,CAACiD,eAAe,CAAClC,KAAK,CAAC;IACpF;IACA,IAAI,CAACf,QAAQ,CAACmD,cAAc,EAAE;MAC5BnD,QAAQ,CAACmD,cAAc,GAAGnD,QAAQ,CAACkD,eAAe,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5E;IAEA,IAAIlD,QAAQ,CAACoD,oBAAoB,EAAE;MACjCpD,QAAQ,CAACoD,oBAAoB,GAAG;QAAC,GAAGpD,QAAQ,CAACoD;MAAoB,CAAC;MAClE,MAAMC,EAAE,GAAGrD,QAAQ,CAACoD,oBAAoB;MACxC,IAAIC,EAAE,CAACC,gBAAgB,EAAE;QACvBD,EAAE,CAACC,gBAAgB,GAAG;UAAC,GAAGD,EAAE,CAACC;QAAgB,CAAC;QAC9CD,EAAE,CAACC,gBAAgB,CAAC5D,OAAO,GAAG,IAAI,CAAC6B,UAAU,CAAC8B,EAAE,CAACC,gBAAgB,CAACvC,KAAK,CAAC;MAC1E;MACA,IAAIsC,EAAE,CAACE,wBAAwB,EAAE;QAC/BF,EAAE,CAACE,wBAAwB,GAAG;UAAC,GAAGF,EAAE,CAACE;QAAwB,CAAC;QAC9DF,EAAE,CAACE,wBAAwB,CAAC7D,OAAO,GAAG,IAAI,CAAC6B,UAAU,CAAC8B,EAAE,CAACE,wBAAwB,CAACxC,KAAK,CAAC;MAC1F;IACF;IACA,OAAOf,QAAQ;EACjB;EAEAF,gBAAgBA,CAACD,QAAQ,EAAEkB,KAAK,EAAE;IAEhClB,QAAQ,CAACoC,EAAE,GAAGpC,QAAQ,CAACoC,EAAE,gBAAAD,MAAA,CAAgBjB,KAAK,CAAE;IAChD,IAAIlB,QAAQ,CAAC2D,UAAU,KAAKhF,SAAS,EAAE;MAErCqB,QAAQ,CAAC2D,UAAU,GAAG,IAAI,CAAC9B,aAAa,CAAC7B,QAAQ,CAAC2D,UAAU,CAAC;IAC/D;IAGA3D,QAAQ,CAAC4D,iBAAiB,GAAG9F,yBAAyB,CAACkC,QAAQ,CAACjC,aAAa,CAAC;IAC9EiC,QAAQ,CAAC6D,UAAU,GAAG7F,uBAAuB,CAACgC,QAAQ,CAAC/B,IAAI,CAAC;IAC5D+B,QAAQ,CAAC8D,eAAe,GAAG9D,QAAQ,CAAC4D,iBAAiB,GAAG5D,QAAQ,CAAC6D,UAAU;IAK3E,IAAI7D,QAAQ,CAAC2D,UAAU,EAAE;MACvB,MAAMI,MAAM,GAAG/D,QAAQ,CAAC2D,UAAU,CAACI,MAAM;MACzC,MAAM;QAACC,SAAS;QAAEC;MAAU,CAAC,GAAG,IAAAC,wCAA6B,EAAClE,QAAQ,EAAEA,QAAQ,CAAC2D,UAAU,CAAC;MAC5F,MAAMQ,UAAU,GACd,CAACnE,QAAQ,CAAC2D,UAAU,CAACQ,UAAU,IAAI,CAAC,KAAKnE,QAAQ,CAACmE,UAAU,IAAI,CAAC,CAAC,GAAGJ,MAAM,CAACI,UAAU;MACxF,IAAIC,SAAS,GAAGL,MAAM,CAACM,WAAW,CAACC,KAAK,CAACH,UAAU,EAAEA,UAAU,GAAGF,UAAU,CAAC;MAC7E,IAAIjE,QAAQ,CAAC2D,UAAU,CAACY,UAAU,EAAE;QAClCH,SAAS,GAAG,IAAI,CAACI,8BAA8B,CAC7CT,MAAM,EACNI,UAAU,EACVnE,QAAQ,CAAC2D,UAAU,CAACY,UAAU,EAC9BvE,QAAQ,CAAC8D,eAAe,EACxB9D,QAAQ,CAACyE,KACX,CAAC;MACH;MACAzE,QAAQ,CAAC0E,KAAK,GAAG,IAAIV,SAAS,CAACI,SAAS,CAAC;IAC3C;IAEA,OAAOpE,QAAQ;EACjB;EAYAwE,8BAA8BA,CAACT,MAAM,EAAEI,UAAU,EAAEI,UAAU,EAAET,eAAe,EAAEW,KAAK,EAAE;IACrF,MAAME,MAAM,GAAG,IAAIC,UAAU,CAACH,KAAK,GAAGX,eAAe,CAAC;IACtD,KAAK,IAAIzE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoF,KAAK,EAAEpF,CAAC,EAAE,EAAE;MAC9B,MAAMwF,aAAa,GAAGV,UAAU,GAAG9E,CAAC,GAAGkF,UAAU;MACjDI,MAAM,CAACG,GAAG,CACR,IAAIF,UAAU,CAACb,MAAM,CAACM,WAAW,CAACC,KAAK,CAACO,aAAa,EAAEA,aAAa,GAAGf,eAAe,CAAC,CAAC,EACxFzE,CAAC,GAAGyE,eACN,CAAC;IACH;IACA,OAAOa,MAAM,CAACZ,MAAM;EACtB;EAEAjE,eAAeA,CAACD,OAAO,EAAEqB,KAAK,EAAE;IAE9BrB,OAAO,CAACuC,EAAE,GAAGvC,OAAO,CAACuC,EAAE,eAAAD,MAAA,CAAejB,KAAK,CAAE;IAC7CrB,OAAO,CAACH,OAAO,GAAG,SAAS,IAAIG,OAAO,GAAG,IAAI,CAAC8B,UAAU,CAAC9B,OAAO,CAACH,OAAO,CAAC,GAAG7B,eAAe;IAC3FgC,OAAO,CAACkF,MAAM,GAAG,IAAI,CAACnD,QAAQ,CAAC/B,OAAO,CAACkF,MAAM,CAAC;IAC9C,OAAOlF,OAAO;EAChB;EAEAF,eAAeA,CAACD,OAAO,EAAEwB,KAAK,EAAE;IAE9BxB,OAAO,CAAC0C,EAAE,GAAG1C,OAAO,CAAC0C,EAAE,eAAAD,MAAA,CAAejB,KAAK,CAAE;IAE7CxB,OAAO,CAACsF,UAAU,GAAG,CAAC,CAAC;IACvB,KAAK,MAAMC,GAAG,IAAIvF,OAAO,EAAE;MACzB,MAAMwF,MAAM,GAAG,IAAI,CAACC,qBAAqB,CAACF,GAAG,CAAC;MAC9C,IAAIC,MAAM,KAAKvG,SAAS,EAAE;QACxBe,OAAO,CAACsF,UAAU,CAACE,MAAM,CAAC,GAAGxF,OAAO,CAACuF,GAAG,CAAC;MAC3C;IACF;IACA,OAAOvF,OAAO;EAChB;EAEAyF,qBAAqBA,CAACF,GAAG,EAAE;IACzB,OAAOzH,4BAA4B,CAACyH,GAAG,CAAC;EAC1C;EAEAzF,aAAaA,CAACD,KAAK,EAAE2B,KAAK,EAAE;IAE1B3B,KAAK,CAAC6C,EAAE,GAAG7C,KAAK,CAAC6C,EAAE,aAAAD,MAAA,CAAajB,KAAK,CAAE;IACvC,IAAI3B,KAAK,CAACoE,UAAU,KAAKhF,SAAS,EAAE;MAClCY,KAAK,CAACoE,UAAU,GAAG,IAAI,CAAC9B,aAAa,CAACtC,KAAK,CAACoE,UAAU,CAAC;IACzD;IAIA,MAAMyB,cAAc,GAAG,IAAI,CAACtG,MAAM,CAACoC,KAAK,CAAC;IACzC,IAAIkE,cAAc,EAAE;MAClB7F,KAAK,CAACA,KAAK,GAAG6F,cAAc;IAC9B;IAEA,OAAO7F,KAAK;EACd;EAEAD,kBAAkBA,CAACqE,UAAsB,EAAEzC,KAAa,EAA2B;IAEjF,MAAMmE,WAAW,GAAG1B,UAAU,CAACI,MAAM;IACrC,MAAMY,MAA+B,GAAG;MACtCvC,EAAE,gBAAAD,MAAA,CAAgBjB,KAAK,CAAE;MACzB,GAAGyC,UAAU;MACbI,MAAM,EAAE,IAAI,CAAClF,OAAO,CAACwG,WAAW;IAClC,CAAC;IAGD,MAAMhB,WAAW,GAAG,IAAI,CAACxF,OAAO,CAACwG,WAAW,CAAC,CAAChB,WAAW;IAEzD,IAAIF,UAAU,GAAG,IAAI,CAACtF,OAAO,CAACwG,WAAW,CAAC,CAAClB,UAAU,IAAI,CAAC;IAE1D,IAAI,YAAY,IAAIR,UAAU,EAAE;MAC9BQ,UAAU,IAAIR,UAAU,CAACQ,UAAU;IACrC;IAEAQ,MAAM,CAACW,IAAI,GAAG,IAAIV,UAAU,CAACP,WAAW,EAAEF,UAAU,EAAER,UAAU,CAACM,UAAU,CAAC;IAC5E,OAAOU,MAAM;EACf;EAEAY,cAAcA,CAAC5C,MAAM,EAAEzB,KAAK,EAAE;IAC5ByB,MAAM,CAACP,EAAE,GAAGO,MAAM,CAACP,EAAE,cAAAD,MAAA,CAAcjB,KAAK,CAAE;IAE1C,IAAIyB,MAAM,CAAC6C,WAAW,EAAE,CAExB;IACA,IAAI7C,MAAM,CAAC8C,YAAY,EAAE,CAEzB;IACA,OAAO9C,MAAM;EACf;AACF;AAEO,SAAS+C,eAAeA,CAACnH,IAAI,EAAEC,OAAQ,EAAE;EAC9C,OAAO,IAAIN,iBAAiB,CAAC,CAAC,CAACI,WAAW,CAACC,IAAI,EAAEC,OAAO,CAAC;AAC3D"}