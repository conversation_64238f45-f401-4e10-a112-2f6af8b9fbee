"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.EXTENSIONS = void 0;
exports.decodeExtensions = decodeExtensions;
exports.preprocessExtensions = preprocessExtensions;
var EXT_meshopt_compression = _interopRequireWildcard(require("../extensions/EXT_meshopt_compression"));
var EXT_texture_webp = _interopRequireWildcard(require("../extensions/EXT_texture_webp"));
var KHR_texture_basisu = _interopRequireWildcard(require("../extensions/KHR_texture_basisu"));
var KHR_draco_mesh_compression = _interopRequireWildcard(require("../extensions/KHR_draco_mesh_compression"));
var KHR_texture_transform = _interopRequireWildcard(require("../extensions/KHR_texture_transform"));
var KHR_lights_punctual = _interopRequireWildcard(require("../extensions/deprecated/KHR_lights_punctual"));
var KHR_materials_unlit = _interopRequireWildcard(require("../extensions/deprecated/KHR_materials_unlit"));
var KHR_techniques_webgl = _interopRequireWildcard(require("../extensions/deprecated/KHR_techniques_webgl"));
var EXT_feature_metadata = _interopRequireWildcard(require("../extensions/deprecated/EXT_feature_metadata"));
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
const EXTENSIONS = [EXT_meshopt_compression, EXT_texture_webp, KHR_texture_basisu, KHR_draco_mesh_compression, KHR_lights_punctual, KHR_materials_unlit, KHR_techniques_webgl, KHR_texture_transform, EXT_feature_metadata];
exports.EXTENSIONS = EXTENSIONS;
function preprocessExtensions(gltf) {
  let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  let context = arguments.length > 2 ? arguments[2] : undefined;
  const extensions = EXTENSIONS.filter(extension => useExtension(extension.name, options));
  for (const extension of extensions) {
    var _extension$preprocess;
    (_extension$preprocess = extension.preprocess) === null || _extension$preprocess === void 0 ? void 0 : _extension$preprocess.call(extension, gltf, options, context);
  }
}
async function decodeExtensions(gltf) {
  let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  let context = arguments.length > 2 ? arguments[2] : undefined;
  const extensions = EXTENSIONS.filter(extension => useExtension(extension.name, options));
  for (const extension of extensions) {
    var _extension$decode;
    await ((_extension$decode = extension.decode) === null || _extension$decode === void 0 ? void 0 : _extension$decode.call(extension, gltf, options, context));
  }
}
function useExtension(extensionName, options) {
  var _options$gltf;
  const excludes = (options === null || options === void 0 ? void 0 : (_options$gltf = options.gltf) === null || _options$gltf === void 0 ? void 0 : _options$gltf.excludeExtensions) || {};
  const exclude = extensionName in excludes && !excludes[extensionName];
  return !exclude;
}
//# sourceMappingURL=gltf-extensions.js.map