"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.postProcessGLTF = postProcessGLTF;
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _assert = require("../utils/assert");
var _gltfUtils = require("../gltf-utils/gltf-utils");
const COMPONENTS = {
  SCALAR: 1,
  VEC2: 2,
  VEC3: 3,
  VEC4: 4,
  MAT2: 4,
  MAT3: 9,
  MAT4: 16
};
const BYTES = {
  5120: 1,
  5121: 1,
  5122: 2,
  5123: 2,
  5125: 4,
  5126: 4
};
const GL_SAMPLER = {
  TEXTURE_MAG_FILTER: 0x2800,
  TEXTURE_MIN_FILTER: 0x2801,
  TEXTURE_WRAP_S: 0x2802,
  TEXTURE_WRAP_T: 0x2803,
  REPEAT: 0x2901,
  LINEAR: 0x2601,
  NEAREST_MIPMAP_LINEAR: 0x2702
};
const SAMPLER_PARAMETER_GLTF_TO_GL = {
  magFilter: GL_SAMPLER.TEXTURE_MAG_FILTER,
  minFilter: GL_SAMPLER.TEXTURE_MIN_FILTER,
  wrapS: GL_SAMPLER.TEXTURE_WRAP_S,
  wrapT: GL_SAMPLER.TEXTURE_WRAP_T
};
const DEFAULT_SAMPLER = {
  [GL_SAMPLER.TEXTURE_MAG_FILTER]: GL_SAMPLER.LINEAR,
  [GL_SAMPLER.TEXTURE_MIN_FILTER]: GL_SAMPLER.NEAREST_MIPMAP_LINEAR,
  [GL_SAMPLER.TEXTURE_WRAP_S]: GL_SAMPLER.REPEAT,
  [GL_SAMPLER.TEXTURE_WRAP_T]: GL_SAMPLER.REPEAT
};
function getBytesFromComponentType(componentType) {
  return BYTES[componentType];
}
function getSizeFromAccessorType(type) {
  return COMPONENTS[type];
}
class GLTFPostProcessor {
  constructor() {
    (0, _defineProperty2.default)(this, "baseUri", '');
    (0, _defineProperty2.default)(this, "json", {});
    (0, _defineProperty2.default)(this, "buffers", []);
    (0, _defineProperty2.default)(this, "images", []);
  }
  postProcess(gltf) {
    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    const {
      json,
      buffers = [],
      images = [],
      baseUri = ''
    } = gltf;
    (0, _assert.assert)(json);
    this.baseUri = baseUri;
    this.json = json;
    this.buffers = buffers;
    this.images = images;
    this._resolveTree(this.json, options);
    return this.json;
  }
  _resolveTree(json) {
    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    if (json.bufferViews) {
      json.bufferViews = json.bufferViews.map((bufView, i) => this._resolveBufferView(bufView, i));
    }
    if (json.images) {
      json.images = json.images.map((image, i) => this._resolveImage(image, i));
    }
    if (json.samplers) {
      json.samplers = json.samplers.map((sampler, i) => this._resolveSampler(sampler, i));
    }
    if (json.textures) {
      json.textures = json.textures.map((texture, i) => this._resolveTexture(texture, i));
    }
    if (json.accessors) {
      json.accessors = json.accessors.map((accessor, i) => this._resolveAccessor(accessor, i));
    }
    if (json.materials) {
      json.materials = json.materials.map((material, i) => this._resolveMaterial(material, i));
    }
    if (json.meshes) {
      json.meshes = json.meshes.map((mesh, i) => this._resolveMesh(mesh, i));
    }
    if (json.nodes) {
      json.nodes = json.nodes.map((node, i) => this._resolveNode(node, i));
    }
    if (json.skins) {
      json.skins = json.skins.map((skin, i) => this._resolveSkin(skin, i));
    }
    if (json.scenes) {
      json.scenes = json.scenes.map((scene, i) => this._resolveScene(scene, i));
    }
    if (json.scene !== undefined) {
      json.scene = json.scenes[this.json.scene];
    }
  }
  getScene(index) {
    return this._get('scenes', index);
  }
  getNode(index) {
    return this._get('nodes', index);
  }
  getSkin(index) {
    return this._get('skins', index);
  }
  getMesh(index) {
    return this._get('meshes', index);
  }
  getMaterial(index) {
    return this._get('materials', index);
  }
  getAccessor(index) {
    return this._get('accessors', index);
  }
  getCamera(index) {
    return null;
  }
  getTexture(index) {
    return this._get('textures', index);
  }
  getSampler(index) {
    return this._get('samplers', index);
  }
  getImage(index) {
    return this._get('images', index);
  }
  getBufferView(index) {
    return this._get('bufferViews', index);
  }
  getBuffer(index) {
    return this._get('buffers', index);
  }
  _get(array, index) {
    if (typeof index === 'object') {
      return index;
    }
    const object = this.json[array] && this.json[array][index];
    if (!object) {
      console.warn("glTF file error: Could not find ".concat(array, "[").concat(index, "]"));
    }
    return object;
  }
  _resolveScene(scene, index) {
    scene.id = scene.id || "scene-".concat(index);
    scene.nodes = (scene.nodes || []).map(node => this.getNode(node));
    return scene;
  }
  _resolveNode(node, index) {
    node.id = node.id || "node-".concat(index);
    if (node.children) {
      node.children = node.children.map(child => this.getNode(child));
    }
    if (node.mesh !== undefined) {
      node.mesh = this.getMesh(node.mesh);
    } else if (node.meshes !== undefined && node.meshes.length) {
      node.mesh = node.meshes.reduce((accum, meshIndex) => {
        const mesh = this.getMesh(meshIndex);
        accum.id = mesh.id;
        accum.primitives = accum.primitives.concat(mesh.primitives);
        return accum;
      }, {
        primitives: []
      });
    }
    if (node.camera !== undefined) {
      node.camera = this.getCamera(node.camera);
    }
    if (node.skin !== undefined) {
      node.skin = this.getSkin(node.skin);
    }
    return node;
  }
  _resolveSkin(skin, index) {
    skin.id = skin.id || "skin-".concat(index);
    skin.inverseBindMatrices = this.getAccessor(skin.inverseBindMatrices);
    return skin;
  }
  _resolveMesh(mesh, index) {
    mesh.id = mesh.id || "mesh-".concat(index);
    if (mesh.primitives) {
      mesh.primitives = mesh.primitives.map(primitive => {
        primitive = {
          ...primitive
        };
        const attributes = primitive.attributes;
        primitive.attributes = {};
        for (const attribute in attributes) {
          primitive.attributes[attribute] = this.getAccessor(attributes[attribute]);
        }
        if (primitive.indices !== undefined) {
          primitive.indices = this.getAccessor(primitive.indices);
        }
        if (primitive.material !== undefined) {
          primitive.material = this.getMaterial(primitive.material);
        }
        return primitive;
      });
    }
    return mesh;
  }
  _resolveMaterial(material, index) {
    material.id = material.id || "material-".concat(index);
    if (material.normalTexture) {
      material.normalTexture = {
        ...material.normalTexture
      };
      material.normalTexture.texture = this.getTexture(material.normalTexture.index);
    }
    if (material.occlusionTexture) {
      material.occlustionTexture = {
        ...material.occlustionTexture
      };
      material.occlusionTexture.texture = this.getTexture(material.occlusionTexture.index);
    }
    if (material.emissiveTexture) {
      material.emmisiveTexture = {
        ...material.emmisiveTexture
      };
      material.emissiveTexture.texture = this.getTexture(material.emissiveTexture.index);
    }
    if (!material.emissiveFactor) {
      material.emissiveFactor = material.emmisiveTexture ? [1, 1, 1] : [0, 0, 0];
    }
    if (material.pbrMetallicRoughness) {
      material.pbrMetallicRoughness = {
        ...material.pbrMetallicRoughness
      };
      const mr = material.pbrMetallicRoughness;
      if (mr.baseColorTexture) {
        mr.baseColorTexture = {
          ...mr.baseColorTexture
        };
        mr.baseColorTexture.texture = this.getTexture(mr.baseColorTexture.index);
      }
      if (mr.metallicRoughnessTexture) {
        mr.metallicRoughnessTexture = {
          ...mr.metallicRoughnessTexture
        };
        mr.metallicRoughnessTexture.texture = this.getTexture(mr.metallicRoughnessTexture.index);
      }
    }
    return material;
  }
  _resolveAccessor(accessor, index) {
    accessor.id = accessor.id || "accessor-".concat(index);
    if (accessor.bufferView !== undefined) {
      accessor.bufferView = this.getBufferView(accessor.bufferView);
    }
    accessor.bytesPerComponent = getBytesFromComponentType(accessor.componentType);
    accessor.components = getSizeFromAccessorType(accessor.type);
    accessor.bytesPerElement = accessor.bytesPerComponent * accessor.components;
    if (accessor.bufferView) {
      const buffer = accessor.bufferView.buffer;
      const {
        ArrayType,
        byteLength
      } = (0, _gltfUtils.getAccessorArrayTypeAndLength)(accessor, accessor.bufferView);
      const byteOffset = (accessor.bufferView.byteOffset || 0) + (accessor.byteOffset || 0) + buffer.byteOffset;
      let cutBuffer = buffer.arrayBuffer.slice(byteOffset, byteOffset + byteLength);
      if (accessor.bufferView.byteStride) {
        cutBuffer = this._getValueFromInterleavedBuffer(buffer, byteOffset, accessor.bufferView.byteStride, accessor.bytesPerElement, accessor.count);
      }
      accessor.value = new ArrayType(cutBuffer);
    }
    return accessor;
  }
  _getValueFromInterleavedBuffer(buffer, byteOffset, byteStride, bytesPerElement, count) {
    const result = new Uint8Array(count * bytesPerElement);
    for (let i = 0; i < count; i++) {
      const elementOffset = byteOffset + i * byteStride;
      result.set(new Uint8Array(buffer.arrayBuffer.slice(elementOffset, elementOffset + bytesPerElement)), i * bytesPerElement);
    }
    return result.buffer;
  }
  _resolveTexture(texture, index) {
    texture.id = texture.id || "texture-".concat(index);
    texture.sampler = 'sampler' in texture ? this.getSampler(texture.sampler) : DEFAULT_SAMPLER;
    texture.source = this.getImage(texture.source);
    return texture;
  }
  _resolveSampler(sampler, index) {
    sampler.id = sampler.id || "sampler-".concat(index);
    sampler.parameters = {};
    for (const key in sampler) {
      const glEnum = this._enumSamplerParameter(key);
      if (glEnum !== undefined) {
        sampler.parameters[glEnum] = sampler[key];
      }
    }
    return sampler;
  }
  _enumSamplerParameter(key) {
    return SAMPLER_PARAMETER_GLTF_TO_GL[key];
  }
  _resolveImage(image, index) {
    image.id = image.id || "image-".concat(index);
    if (image.bufferView !== undefined) {
      image.bufferView = this.getBufferView(image.bufferView);
    }
    const preloadedImage = this.images[index];
    if (preloadedImage) {
      image.image = preloadedImage;
    }
    return image;
  }
  _resolveBufferView(bufferView, index) {
    const bufferIndex = bufferView.buffer;
    const result = {
      id: "bufferView-".concat(index),
      ...bufferView,
      buffer: this.buffers[bufferIndex]
    };
    const arrayBuffer = this.buffers[bufferIndex].arrayBuffer;
    let byteOffset = this.buffers[bufferIndex].byteOffset || 0;
    if ('byteOffset' in bufferView) {
      byteOffset += bufferView.byteOffset;
    }
    result.data = new Uint8Array(arrayBuffer, byteOffset, bufferView.byteLength);
    return result;
  }
  _resolveCamera(camera, index) {
    camera.id = camera.id || "camera-".concat(index);
    if (camera.perspective) {}
    if (camera.orthographic) {}
    return camera;
  }
}
function postProcessGLTF(gltf, options) {
  return new GLTFPostProcessor().postProcess(gltf, options);
}
//# sourceMappingURL=post-process-gltf.js.map