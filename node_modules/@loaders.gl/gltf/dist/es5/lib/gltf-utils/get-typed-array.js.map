{"version": 3, "file": "get-typed-array.js", "names": ["_assert", "require", "getTypedArrayForBufferView", "json", "buffers", "bufferViewIndex", "bufferView", "bufferViews", "assert", "bufferIndex", "buffer", "binChunk", "byteOffset", "Uint8Array", "arrayBuffer", "byteLength", "getTypedArrayForImageData", "imageIndex", "image", "images"], "sources": ["../../../../src/lib/gltf-utils/get-typed-array.ts"], "sourcesContent": ["// TODO - GLTFScenegraph should use these\nimport {assert} from '../utils/assert';\n\n// accepts buffer view index or buffer view object\n// returns a `Uint8Array`\nexport function getTypedArrayForBufferView(json, buffers, bufferViewIndex) {\n  const bufferView = json.bufferViews[bufferViewIndex];\n  assert(bufferView);\n\n  // Get hold of the arrayBuffer\n  const bufferIndex = bufferView.buffer;\n  const binChunk = buffers[bufferIndex];\n  assert(binChunk);\n\n  const byteOffset = (bufferView.byteOffset || 0) + binChunk.byteOffset;\n  return new Uint8Array(binChunk.arrayBuffer, byteOffset, bufferView.byteLength);\n}\n\n// accepts accessor index or accessor object\n// returns a `Uint8Array`\nexport function getTypedArrayForImageData(json, buffers, imageIndex) {\n  const image = json.images[imageIndex];\n  const bufferViewIndex = json.bufferViews[image.bufferView];\n  return getTypedArrayForBufferView(json, buffers, bufferViewIndex);\n}\n\n/*\n// accepts accessor index or accessor object\n// returns a typed array with type that matches the types\nexport function getTypedArrayForAccessor(accessor) {\n  accessor = this.getAccessor(accessor);\n  const bufferView = this.getBufferView(accessor.bufferView);\n  const buffer = this.getBuffer(bufferView.buffer);\n  const arrayBuffer = buffer.data;\n\n  // Create a new typed array as a view into the combined buffer\n  const {ArrayType, length} = getAccessorArrayTypeAndLength(accessor, bufferView);\n  const byteOffset = bufferView.byteOffset + accessor.byteOffset;\n  return new ArrayType(arrayBuffer, byteOffset, length);\n}\n*/\n"], "mappings": ";;;;;;;AACA,IAAAA,OAAA,GAAAC,OAAA;AAIO,SAASC,0BAA0BA,CAACC,IAAI,EAAEC,OAAO,EAAEC,eAAe,EAAE;EACzE,MAAMC,UAAU,GAAGH,IAAI,CAACI,WAAW,CAACF,eAAe,CAAC;EACpD,IAAAG,cAAM,EAACF,UAAU,CAAC;EAGlB,MAAMG,WAAW,GAAGH,UAAU,CAACI,MAAM;EACrC,MAAMC,QAAQ,GAAGP,OAAO,CAACK,WAAW,CAAC;EACrC,IAAAD,cAAM,EAACG,QAAQ,CAAC;EAEhB,MAAMC,UAAU,GAAG,CAACN,UAAU,CAACM,UAAU,IAAI,CAAC,IAAID,QAAQ,CAACC,UAAU;EACrE,OAAO,IAAIC,UAAU,CAACF,QAAQ,CAACG,WAAW,EAAEF,UAAU,EAAEN,UAAU,CAACS,UAAU,CAAC;AAChF;AAIO,SAASC,yBAAyBA,CAACb,IAAI,EAAEC,OAAO,EAAEa,UAAU,EAAE;EACnE,MAAMC,KAAK,GAAGf,IAAI,CAACgB,MAAM,CAACF,UAAU,CAAC;EACrC,MAAMZ,eAAe,GAAGF,IAAI,CAACI,WAAW,CAACW,KAAK,CAACZ,UAAU,CAAC;EAC1D,OAAOJ,0BAA0B,CAACC,IAAI,EAAEC,OAAO,EAAEC,eAAe,CAAC;AACnE"}