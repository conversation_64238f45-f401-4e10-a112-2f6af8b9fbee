{"version": 3, "file": "gltf-constants.js", "names": ["COMPONENTS", "SCALAR", "VEC2", "VEC3", "VEC4", "MAT2", "MAT3", "MAT4", "exports", "BYTES", "getBytesFromComponentType", "componentType", "getSizeFromAccessorType", "type", "getGLEnumFromSamplerParameter", "parameter", "GL_TEXTURE_MAG_FILTER", "GL_TEXTURE_MIN_FILTER", "GL_TEXTURE_WRAP_S", "GL_TEXTURE_WRAP_T", "PARAMETER_MAP", "magFilter", "minFilter", "wrapS", "wrapT"], "sources": ["../../../../src/lib/gltf-utils/gltf-constants.ts"], "sourcesContent": ["export const COMPONENTS = {\n  SCALAR: 1,\n  VEC2: 2,\n  VEC3: 3,\n  VEC4: 4,\n  MAT2: 4,\n  MAT3: 9,\n  MAT4: 16\n};\n\nexport const BYTES = {\n  5120: 1, // BYTE\n  5121: 1, // UNSIGNED_BYTE\n  5122: 2, // SHORT\n  5123: 2, // UNSIGNED_SHORT\n  5125: 4, // UNSIGNED_INT\n  5126: 4 // FLOAT\n};\n\n// ENUM LOOKUP\n\nexport function getBytesFromComponentType(componentType) {\n  return BYTES[componentType];\n}\n\nexport function getSizeFromAccessorType(type) {\n  return COMPONENTS[type];\n}\n\nexport function getGLEnumFromSamplerParameter(parameter) {\n  const GL_TEXTURE_MAG_FILTER = 0x2800;\n  const GL_TEXTURE_MIN_FILTER = 0x2801;\n  const GL_TEXTURE_WRAP_S = 0x2802;\n  const GL_TEXTURE_WRAP_T = 0x2803;\n\n  const PARAMETER_MAP = {\n    magFilter: GL_TEXTURE_MAG_FILTER,\n    minFilter: GL_TEXTURE_MIN_FILTER,\n    wrapS: GL_TEXTURE_WRAP_S,\n    wrapT: GL_TEXTURE_WRAP_T\n  };\n\n  return PARAMETER_MAP[parameter];\n}\n"], "mappings": ";;;;;;;;;AAAO,MAAMA,UAAU,GAAG;EACxBC,MAAM,EAAE,CAAC;EACTC,IAAI,EAAE,CAAC;EACPC,IAAI,EAAE,CAAC;EACPC,IAAI,EAAE,CAAC;EACPC,IAAI,EAAE,CAAC;EACPC,IAAI,EAAE,CAAC;EACPC,IAAI,EAAE;AACR,CAAC;AAACC,OAAA,CAAAR,UAAA,GAAAA,UAAA;AAEK,MAAMS,KAAK,GAAG;EACnB,IAAI,EAAE,CAAC;EACP,IAAI,EAAE,CAAC;EACP,IAAI,EAAE,CAAC;EACP,IAAI,EAAE,CAAC;EACP,IAAI,EAAE,CAAC;EACP,IAAI,EAAE;AACR,CAAC;AAACD,OAAA,CAAAC,KAAA,GAAAA,KAAA;AAIK,SAASC,yBAAyBA,CAACC,aAAa,EAAE;EACvD,OAAOF,KAAK,CAACE,aAAa,CAAC;AAC7B;AAEO,SAASC,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,OAAOb,UAAU,CAACa,IAAI,CAAC;AACzB;AAEO,SAASC,6BAA6BA,CAACC,SAAS,EAAE;EACvD,MAAMC,qBAAqB,GAAG,MAAM;EACpC,MAAMC,qBAAqB,GAAG,MAAM;EACpC,MAAMC,iBAAiB,GAAG,MAAM;EAChC,MAAMC,iBAAiB,GAAG,MAAM;EAEhC,MAAMC,aAAa,GAAG;IACpBC,SAAS,EAAEL,qBAAqB;IAChCM,SAAS,EAAEL,qBAAqB;IAChCM,KAAK,EAAEL,iBAAiB;IACxBM,KAAK,EAAEL;EACT,CAAC;EAED,OAAOC,aAAa,CAACL,SAAS,CAAC;AACjC"}