{"version": 3, "file": "gltf-attribute-utils.js", "names": ["_gltfUtils", "require", "getGLTFAccessors", "attributes", "accessors", "name", "attribute", "glTFAccessor", "getGLTFAccessor", "buffer", "size", "count", "getAccessorData", "value", "byteOffset", "type", "getAccessorTypeFromSize", "componentType", "getComponentTypeFromArray", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "toTypedA<PERSON>y", "Float32Array", "length", "array", "ArrayType", "convertTypedArrays", "arguments", "undefined", "Array", "isArray"], "sources": ["../../../../src/lib/gltf-utils/gltf-attribute-utils.ts"], "sourcesContent": ["// import type {TypedArray} from '../types/loader-utils';\nimport type {GLTFAccessor} from '../types/gltf-types';\n// TODO - remove\nimport {getAccessorTypeFromSize, getComponentTypeFromArray} from './gltf-utils';\n\n// Returns a fresh attributes object with glTF-standardized attributes names\n// Attributes that cannot be identified will not be included\n// Removes `indices` if present, as it should be stored separately from the attributes\nexport function getGLTFAccessors(attributes): {[key: string]: GLTFAccessor} {\n  const accessors = {};\n  for (const name in attributes) {\n    const attribute = attributes[name];\n    if (name !== 'indices') {\n      const glTFAccessor = getGLTFAccessor(attribute);\n      accessors[name] = glTFAccessor;\n    }\n  }\n  return accessors;\n}\n\n// Fix up a single accessor.\n// Input: typed array or a partial accessor object\n// Return: accessor object\nexport function getGLTFAccessor(attribute) {\n  const {buffer, size, count} = getAccessorData(attribute);\n\n  const glTFAccessor: GLTFAccessor = {\n    // glTF Accessor values\n    // TODO: Instead of a bufferView index we could have an actual buffer (typed array)\n    // bufferView: null,\n    // TODO: Deprecate `value` in favor of bufferView?\n    // @ts-ignore\n    value: buffer,\n    size, // Decoded `type` (e.g. SCALAR)\n\n    byteOffset: 0,\n    count,\n    type: getAccessorTypeFromSize(size),\n    componentType: getComponentTypeFromArray(buffer)\n  };\n\n  return glTFAccessor;\n}\n\n// export function getGLTFAttribute(data, gltfAttributeName): GLTFAccessor {\n//   return data.attributes[data.glTFAttributeMap[gltfAttributeName]];\n// }\n\nfunction getAccessorData(attribute) {\n  let buffer = attribute;\n  let size = 1;\n  let count = 0;\n\n  if (attribute && attribute.value) {\n    buffer = attribute.value;\n    size = attribute.size || 1;\n  }\n\n  if (buffer) {\n    if (!ArrayBuffer.isView(buffer)) {\n      buffer = toTypedArray(buffer, Float32Array);\n    }\n    count = buffer.length / size;\n  }\n\n  return {buffer, size, count};\n}\n\n// Convert non-typed arrays to arrays of specified format\nfunction toTypedArray(array, ArrayType, convertTypedArrays = false) {\n  if (!array) {\n    return null;\n  }\n  if (Array.isArray(array)) {\n    return new ArrayType(array);\n  }\n  if (convertTypedArrays && !(array instanceof ArrayType)) {\n    return new ArrayType(array);\n  }\n  return array;\n}\n"], "mappings": ";;;;;;;AAGA,IAAAA,UAAA,GAAAC,OAAA;AAKO,SAASC,gBAAgBA,CAACC,UAAU,EAAiC;EAC1E,MAAMC,SAAS,GAAG,CAAC,CAAC;EACpB,KAAK,MAAMC,IAAI,IAAIF,UAAU,EAAE;IAC7B,MAAMG,SAAS,GAAGH,UAAU,CAACE,IAAI,CAAC;IAClC,IAAIA,IAAI,KAAK,SAAS,EAAE;MACtB,MAAME,YAAY,GAAGC,eAAe,CAACF,SAAS,CAAC;MAC/CF,SAAS,CAACC,IAAI,CAAC,GAAGE,YAAY;IAChC;EACF;EACA,OAAOH,SAAS;AAClB;AAKO,SAASI,eAAeA,CAACF,SAAS,EAAE;EACzC,MAAM;IAACG,MAAM;IAAEC,IAAI;IAAEC;EAAK,CAAC,GAAGC,eAAe,CAACN,SAAS,CAAC;EAExD,MAAMC,YAA0B,GAAG;IAMjCM,KAAK,EAAEJ,MAAM;IACbC,IAAI;IAEJI,UAAU,EAAE,CAAC;IACbH,KAAK;IACLI,IAAI,EAAE,IAAAC,kCAAuB,EAACN,IAAI,CAAC;IACnCO,aAAa,EAAE,IAAAC,oCAAyB,EAACT,MAAM;EACjD,CAAC;EAED,OAAOF,YAAY;AACrB;AAMA,SAASK,eAAeA,CAACN,SAAS,EAAE;EAClC,IAAIG,MAAM,GAAGH,SAAS;EACtB,IAAII,IAAI,GAAG,CAAC;EACZ,IAAIC,KAAK,GAAG,CAAC;EAEb,IAAIL,SAAS,IAAIA,SAAS,CAACO,KAAK,EAAE;IAChCJ,MAAM,GAAGH,SAAS,CAACO,KAAK;IACxBH,IAAI,GAAGJ,SAAS,CAACI,IAAI,IAAI,CAAC;EAC5B;EAEA,IAAID,MAAM,EAAE;IACV,IAAI,CAACU,WAAW,CAACC,MAAM,CAACX,MAAM,CAAC,EAAE;MAC/BA,MAAM,GAAGY,YAAY,CAACZ,MAAM,EAAEa,YAAY,CAAC;IAC7C;IACAX,KAAK,GAAGF,MAAM,CAACc,MAAM,GAAGb,IAAI;EAC9B;EAEA,OAAO;IAACD,MAAM;IAAEC,IAAI;IAAEC;EAAK,CAAC;AAC9B;AAGA,SAASU,YAAYA,CAACG,KAAK,EAAEC,SAAS,EAA8B;EAAA,IAA5BC,kBAAkB,GAAAC,SAAA,CAAAJ,MAAA,QAAAI,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK;EAChE,IAAI,CAACH,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EACA,IAAIK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,EAAE;IACxB,OAAO,IAAIC,SAAS,CAACD,KAAK,CAAC;EAC7B;EACA,IAAIE,kBAAkB,IAAI,EAAEF,KAAK,YAAYC,SAAS,CAAC,EAAE;IACvD,OAAO,IAAIA,SAAS,CAACD,KAAK,CAAC;EAC7B;EACA,OAAOA,KAAK;AACd"}