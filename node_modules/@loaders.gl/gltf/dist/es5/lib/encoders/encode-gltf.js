"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.encodeGLTFSync = encodeGLTFSync;
var _encodeGlb = _interopRequireDefault(require("./encode-glb"));
function encodeGLTFSync(gltf, arrayBuffer, byteOffset, options) {
  convertBuffersToBase64(gltf);
  return (0, _encodeGlb.default)(gltf, arrayBuffer, byteOffset, options);
}
function convertBuffersToBase64(gltf) {
  let {
    firstBuffer = 0
  } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  if (gltf.buffers && gltf.buffers.length > firstBuffer) {
    throw new Error('encodeGLTF: multiple buffers not yet implemented');
  }
}
//# sourceMappingURL=encode-gltf.js.map