{"version": 3, "file": "encode-glb.js", "names": ["_loaderUtils", "require", "MAGIC_glTF", "MAGIC_JSON", "MAGIC_BIN", "LE", "encodeGLBSync", "glb", "dataView", "byteOffset", "arguments", "length", "undefined", "options", "magic", "version", "json", "binary", "byteOffsetStart", "setUint32", "byteOffsetFileLength", "byteOffsetJsonHeader", "jsonString", "JSON", "stringify", "copyPaddedStringToDataView", "jsonByteLength", "byteOffsetBinHeader", "copyPaddedArrayBufferToDataView", "binByteLength", "fileByteLength"], "sources": ["../../../../src/lib/encoders/encode-glb.ts"], "sourcesContent": ["/* eslint-disable camelcase, max-statements */\nimport {\n  copyPaddedStringToDataView,\n  copyPaddedArrayBufferToDataView\n} from '@loaders.gl/loader-utils';\n\nconst MAGIC_glTF = 0x46546c67; // glTF in ASCII\nconst MAGIC_JSON = 0x4e4f534a; // JSON in ASCII\nconst MAGIC_BIN = 0x004e4942; // BIN\\0 in ASCII\n\nconst LE = true; // Binary GLTF is little endian.\n\n// Encode the full GLB buffer with header etc\n// https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#\n// glb-file-format-specification\nexport default function encodeGLBSync(glb, dataView, byteOffset = 0, options = {}) {\n  const {magic = MAGIC_glTF, version = 2, json = {}, binary} = glb;\n\n  const byteOffsetStart = byteOffset;\n\n  // Write GLB Header\n  if (dataView) {\n    dataView.setUint32(byteOffset + 0, magic, LE); // Magic number (the ASCII string 'glTF').\n    dataView.setUint32(byteOffset + 4, version, LE); // Version 2 of binary glTF container format uint32\n    dataView.setUint32(byteOffset + 8, 0, LE); // Total byte length of generated file (uint32), will be set last\n  }\n  const byteOffsetFileLength = byteOffset + 8;\n  byteOffset += 12; // GLB_FILE_HEADER_SIZE\n\n  // Write the JSON chunk header\n  const byteOffsetJsonHeader = byteOffset;\n  if (dataView) {\n    dataView.setUint32(byteOffset + 0, 0, LE); // Byte length of json chunk (will be written later)\n    dataView.setUint32(byteOffset + 4, MAGIC_JSON, LE); // Chunk type\n  }\n  byteOffset += 8; // GLB_CHUNK_HEADER_SIZE\n\n  // Write the JSON chunk\n  const jsonString = JSON.stringify(json);\n  byteOffset = copyPaddedStringToDataView(dataView, byteOffset, jsonString, 4);\n\n  // Now we know the JSON chunk length so we can write it.\n  if (dataView) {\n    const jsonByteLength = byteOffset - byteOffsetJsonHeader - 8; // GLB_CHUNK_HEADER_SIZE\n    dataView.setUint32(byteOffsetJsonHeader + 0, jsonByteLength, LE); // Byte length of json chunk (uint32)\n  }\n\n  // Write the BIN chunk if present. The BIN chunk is optional.\n  if (binary) {\n    const byteOffsetBinHeader = byteOffset;\n\n    // Write the BIN chunk header\n    if (dataView) {\n      dataView.setUint32(byteOffset + 0, 0, LE); // Byte length BIN (uint32)\n      dataView.setUint32(byteOffset + 4, MAGIC_BIN, LE); // Chunk type\n    }\n    byteOffset += 8; // GLB_CHUNK_HEADER_SIZE\n\n    byteOffset = copyPaddedArrayBufferToDataView(dataView, byteOffset, binary, 4);\n\n    // Now we know the BIN chunk length so we can write it.\n    if (dataView) {\n      const binByteLength = byteOffset - byteOffsetBinHeader - 8; // GLB_CHUNK_HEADER_SIZE\n      dataView.setUint32(byteOffsetBinHeader + 0, binByteLength, LE); // Byte length BIN (uint32)\n    }\n  }\n\n  // Now we know the glb file length so we can write it.\n  if (dataView) {\n    const fileByteLength = byteOffset - byteOffsetStart;\n    dataView.setUint32(byteOffsetFileLength, fileByteLength, LE); // Total byte length of generated file (uint32)\n  }\n\n  return byteOffset;\n}\n"], "mappings": ";;;;;;AACA,IAAAA,YAAA,GAAAC,OAAA;AAKA,MAAMC,UAAU,GAAG,UAAU;AAC7B,MAAMC,UAAU,GAAG,UAAU;AAC7B,MAAMC,SAAS,GAAG,UAAU;AAE5B,MAAMC,EAAE,GAAG,IAAI;AAKA,SAASC,aAAaA,CAACC,GAAG,EAAEC,QAAQ,EAAgC;EAAA,IAA9BC,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEG,OAAO,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC/E,MAAM;IAACI,KAAK,GAAGZ,UAAU;IAAEa,OAAO,GAAG,CAAC;IAAEC,IAAI,GAAG,CAAC,CAAC;IAAEC;EAAM,CAAC,GAAGV,GAAG;EAEhE,MAAMW,eAAe,GAAGT,UAAU;EAGlC,IAAID,QAAQ,EAAE;IACZA,QAAQ,CAACW,SAAS,CAACV,UAAU,GAAG,CAAC,EAAEK,KAAK,EAAET,EAAE,CAAC;IAC7CG,QAAQ,CAACW,SAAS,CAACV,UAAU,GAAG,CAAC,EAAEM,OAAO,EAAEV,EAAE,CAAC;IAC/CG,QAAQ,CAACW,SAAS,CAACV,UAAU,GAAG,CAAC,EAAE,CAAC,EAAEJ,EAAE,CAAC;EAC3C;EACA,MAAMe,oBAAoB,GAAGX,UAAU,GAAG,CAAC;EAC3CA,UAAU,IAAI,EAAE;EAGhB,MAAMY,oBAAoB,GAAGZ,UAAU;EACvC,IAAID,QAAQ,EAAE;IACZA,QAAQ,CAACW,SAAS,CAACV,UAAU,GAAG,CAAC,EAAE,CAAC,EAAEJ,EAAE,CAAC;IACzCG,QAAQ,CAACW,SAAS,CAACV,UAAU,GAAG,CAAC,EAAEN,UAAU,EAAEE,EAAE,CAAC;EACpD;EACAI,UAAU,IAAI,CAAC;EAGf,MAAMa,UAAU,GAAGC,IAAI,CAACC,SAAS,CAACR,IAAI,CAAC;EACvCP,UAAU,GAAG,IAAAgB,uCAA0B,EAACjB,QAAQ,EAAEC,UAAU,EAAEa,UAAU,EAAE,CAAC,CAAC;EAG5E,IAAId,QAAQ,EAAE;IACZ,MAAMkB,cAAc,GAAGjB,UAAU,GAAGY,oBAAoB,GAAG,CAAC;IAC5Db,QAAQ,CAACW,SAAS,CAACE,oBAAoB,GAAG,CAAC,EAAEK,cAAc,EAAErB,EAAE,CAAC;EAClE;EAGA,IAAIY,MAAM,EAAE;IACV,MAAMU,mBAAmB,GAAGlB,UAAU;IAGtC,IAAID,QAAQ,EAAE;MACZA,QAAQ,CAACW,SAAS,CAACV,UAAU,GAAG,CAAC,EAAE,CAAC,EAAEJ,EAAE,CAAC;MACzCG,QAAQ,CAACW,SAAS,CAACV,UAAU,GAAG,CAAC,EAAEL,SAAS,EAAEC,EAAE,CAAC;IACnD;IACAI,UAAU,IAAI,CAAC;IAEfA,UAAU,GAAG,IAAAmB,4CAA+B,EAACpB,QAAQ,EAAEC,UAAU,EAAEQ,MAAM,EAAE,CAAC,CAAC;IAG7E,IAAIT,QAAQ,EAAE;MACZ,MAAMqB,aAAa,GAAGpB,UAAU,GAAGkB,mBAAmB,GAAG,CAAC;MAC1DnB,QAAQ,CAACW,SAAS,CAACQ,mBAAmB,GAAG,CAAC,EAAEE,aAAa,EAAExB,EAAE,CAAC;IAChE;EACF;EAGA,IAAIG,QAAQ,EAAE;IACZ,MAAMsB,cAAc,GAAGrB,UAAU,GAAGS,eAAe;IACnDV,QAAQ,CAACW,SAAS,CAACC,oBAAoB,EAAEU,cAAc,EAAEzB,EAAE,CAAC;EAC9D;EAEA,OAAOI,UAAU;AACnB"}