{"version": 3, "file": "encode-gltf.js", "names": ["_encodeGlb", "_interopRequireDefault", "require", "encodeGLTFSync", "gltf", "arrayBuffer", "byteOffset", "options", "convertBuffersToBase64", "encodeGLBSync", "firstBuffer", "arguments", "length", "undefined", "buffers", "Error"], "sources": ["../../../../src/lib/encoders/encode-gltf.ts"], "sourcesContent": ["import encodeGLBSync from './encode-glb';\n\n// Encode the full glTF file as a binary GLB file\n// Returns an ArrayBuffer that represents the complete GLB image that can be saved to file\n//\n// TODO - Does not support encoding to non-GLB versions of glTF format\n// - Encode as a textual JSON file with binary data in base64 data URLs.\n// - Encode as a JSON with all images (and buffers?) in separate binary files\n//\n// glb-file-format-specification\n// https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#\n\nexport function encodeGLTFSync(gltf, arrayBuffer, byteOffset, options) {\n  convertBuffersToBase64(gltf);\n\n  // TODO: Copy buffers to binary\n\n  return encodeGLBSync(gltf, arrayBuffer, byteOffset, options);\n}\n\nfunction convertBuffersToBase64(gltf, {firstBuffer = 0} = {}) {\n  if (gltf.buffers && gltf.buffers.length > firstBuffer) {\n    throw new Error('encodeGLTF: multiple buffers not yet implemented');\n  }\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AAYO,SAASC,cAAcA,CAACC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,OAAO,EAAE;EACrEC,sBAAsB,CAACJ,IAAI,CAAC;EAI5B,OAAO,IAAAK,kBAAa,EAACL,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,OAAO,CAAC;AAC9D;AAEA,SAASC,sBAAsBA,CAACJ,IAAI,EAA0B;EAAA,IAAxB;IAACM,WAAW,GAAG;EAAC,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC1D,IAAIP,IAAI,CAACU,OAAO,IAAIV,IAAI,CAACU,OAAO,CAACF,MAAM,GAAGF,WAAW,EAAE;IACrD,MAAM,IAAIK,KAAK,CAAC,kDAAkD,CAAC;EACrE;AACF"}