"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = encodeGLBSync;
var _loaderUtils = require("@loaders.gl/loader-utils");
const MAGIC_glTF = 0x46546c67;
const MAGIC_JSON = 0x4e4f534a;
const MAGIC_BIN = 0x004e4942;
const LE = true;
function encodeGLBSync(glb, dataView) {
  let byteOffset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;
  let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};
  const {
    magic = MAGIC_glTF,
    version = 2,
    json = {},
    binary
  } = glb;
  const byteOffsetStart = byteOffset;
  if (dataView) {
    dataView.setUint32(byteOffset + 0, magic, LE);
    dataView.setUint32(byteOffset + 4, version, LE);
    dataView.setUint32(byteOffset + 8, 0, LE);
  }
  const byteOffsetFileLength = byteOffset + 8;
  byteOffset += 12;
  const byteOffsetJsonHeader = byteOffset;
  if (dataView) {
    dataView.setUint32(byteOffset + 0, 0, LE);
    dataView.setUint32(byteOffset + 4, MAGIC_JSON, LE);
  }
  byteOffset += 8;
  const jsonString = JSON.stringify(json);
  byteOffset = (0, _loaderUtils.copyPaddedStringToDataView)(dataView, byteOffset, jsonString, 4);
  if (dataView) {
    const jsonByteLength = byteOffset - byteOffsetJsonHeader - 8;
    dataView.setUint32(byteOffsetJsonHeader + 0, jsonByteLength, LE);
  }
  if (binary) {
    const byteOffsetBinHeader = byteOffset;
    if (dataView) {
      dataView.setUint32(byteOffset + 0, 0, LE);
      dataView.setUint32(byteOffset + 4, MAGIC_BIN, LE);
    }
    byteOffset += 8;
    byteOffset = (0, _loaderUtils.copyPaddedArrayBufferToDataView)(dataView, byteOffset, binary, 4);
    if (dataView) {
      const binByteLength = byteOffset - byteOffsetBinHeader - 8;
      dataView.setUint32(byteOffsetBinHeader + 0, binByteLength, LE);
    }
  }
  if (dataView) {
    const fileByteLength = byteOffset - byteOffsetStart;
    dataView.setUint32(byteOffsetFileLength, fileByteLength, LE);
  }
  return byteOffset;
}
//# sourceMappingURL=encode-glb.js.map