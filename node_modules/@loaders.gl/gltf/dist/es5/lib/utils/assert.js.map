{"version": 3, "file": "assert.js", "names": ["assert", "condition", "message", "Error"], "sources": ["../../../../src/lib/utils/assert.ts"], "sourcesContent": ["// Replacement for the external assert method to reduce bundle size\n// Note: We don't use the second \"message\" argument in calling code,\n// so no need to support it here\nexport function assert(condition: unknown, message?: string): void {\n  if (!condition) {\n    throw new Error(message || 'assert failed: gltf');\n  }\n}\n"], "mappings": ";;;;;;AAGO,SAASA,MAAMA,CAACC,SAAkB,EAAEC,OAAgB,EAAQ;EACjE,IAAI,CAACD,SAAS,EAAE;IACd,MAAM,IAAIE,KAAK,CAACD,OAAO,IAAI,qBAAqB,CAAC;EACnD;AACF"}