{"version": 3, "file": "webp.js", "names": ["WEBP_TEST_IMAGES", "lossy", "lossless", "alpha", "animation", "WEBP_FEATURES", "isWebPSupported", "features", "arguments", "length", "undefined", "promises", "map", "feature", "checkWebPFeature", "statuses", "Promise", "all", "every", "_", "Image", "resolve", "reject", "img", "onload", "width", "height", "onerror", "src", "concat"], "sources": ["../../../src/webp/webp.ts"], "sourcesContent": ["const WEBP_TEST_IMAGES = {\n  lossy: 'UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA',\n  lossless: 'UklGRhoAAABXRUJQVlA4TA0AAAAvAAAAEAcQERGIiP4HAA==',\n  alpha:\n    'UklGRkoAAABXRUJQVlA4WAoAAAAQAAAAAAAAAAAAQUxQSAwAAAARBxAR/Q9ERP8DAABWUDggGAAAABQBAJ0BKgEAAQAAAP4AAA3AAP7mtQAAAA==',\n  animation:\n    'UklGRlIAAABXRUJQVlA4WAoAAAASAAAAAAAAAAAAQU5JTQYAAAD/////AABBTk1GJgAAAAAAAAAAAAAAAAAAAGQAAABWUDhMDQAAAC8AAAAQBxAREYiI/gcA'\n};\n\nexport type WebPFeature = 'lossy' | 'lossless' | 'alpha' | 'animation';\n\nconst WEBP_FEATURES: WebPFeature[] = ['lossy', 'lossless', 'alpha', 'animation'];\n\n/**\n * Checks if WebP is supported\n * @param features Array, can include 'lossy', 'lossless', 'alpha' or 'animation'\n */\nexport async function isWebPSupported(features: WebPFeature[] = WEBP_FEATURES): Promise<boolean> {\n  const promises = features.map((feature) => checkWebPFeature(feature));\n  const statuses = await Promise.all(promises);\n  return statuses.every((_) => _);\n}\n\n/**\n * Checks if a specific WebP feature is supported\n * @param feature 'feature' can be one of 'lossy', 'lossless', 'alpha' or 'animation'.\n *\n * Based on Google's official recommended checks\n * @see https://developers.google.com/speed/webp/faq#how_can_i_detect_browser_support_for_webp\n */\nasync function checkWebPFeature(feature: WebPFeature): Promise<boolean> {\n  if (typeof Image === 'undefined') {\n    return false;\n  }\n  return await new Promise((resolve, reject) => {\n    const img = new Image();\n    img.onload = () => resolve(img.width > 0 && img.height > 0);\n    img.onerror = () => resolve(false);\n    img.src = `data:image/webp;base64,${WEBP_TEST_IMAGES[feature]}`;\n  });\n}\n"], "mappings": ";;;;;;AAAA,MAAMA,gBAAgB,GAAG;EACvBC,KAAK,EAAE,0DAA0D;EACjEC,QAAQ,EAAE,kDAAkD;EAC5DC,KAAK,EACH,kHAAkH;EACpHC,SAAS,EACP;AACJ,CAAC;AAID,MAAMC,aAA4B,GAAG,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,CAAC;AAMzE,eAAeC,eAAeA,CAAA,EAA4D;EAAA,IAA3DC,QAAuB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGH,aAAa;EAC3E,MAAMM,QAAQ,GAAGJ,QAAQ,CAACK,GAAG,CAAEC,OAAO,IAAKC,gBAAgB,CAACD,OAAO,CAAC,CAAC;EACrE,MAAME,QAAQ,GAAG,MAAMC,OAAO,CAACC,GAAG,CAACN,QAAQ,CAAC;EAC5C,OAAOI,QAAQ,CAACG,KAAK,CAAEC,CAAC,IAAKA,CAAC,CAAC;AACjC;AASA,eAAeL,gBAAgBA,CAACD,OAAoB,EAAoB;EACtE,IAAI,OAAOO,KAAK,KAAK,WAAW,EAAE;IAChC,OAAO,KAAK;EACd;EACA,OAAO,MAAM,IAAIJ,OAAO,CAAC,CAACK,OAAO,EAAEC,MAAM,KAAK;IAC5C,MAAMC,GAAG,GAAG,IAAIH,KAAK,CAAC,CAAC;IACvBG,GAAG,CAACC,MAAM,GAAG,MAAMH,OAAO,CAACE,GAAG,CAACE,KAAK,GAAG,CAAC,IAAIF,GAAG,CAACG,MAAM,GAAG,CAAC,CAAC;IAC3DH,GAAG,CAACI,OAAO,GAAG,MAAMN,OAAO,CAAC,KAAK,CAAC;IAClCE,GAAG,CAACK,GAAG,6BAAAC,MAAA,CAA6B7B,gBAAgB,CAACa,OAAO,CAAC,CAAE;EACjE,CAAC,CAAC;AACJ"}