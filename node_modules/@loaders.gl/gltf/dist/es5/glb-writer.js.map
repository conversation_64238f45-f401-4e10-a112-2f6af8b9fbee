{"version": 3, "file": "glb-writer.js", "names": ["_version", "require", "_encodeGlb", "_interopRequireDefault", "GLBWriter", "name", "id", "module", "version", "VERSION", "extensions", "mimeTypes", "binary", "encodeSync", "options", "glb", "exports", "byteOffset", "byteLength", "encodeGLBSync", "arrayBuffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dataView", "DataView", "_TypecheckGLBLoader"], "sources": ["../../src/glb-writer.ts"], "sourcesContent": ["import type {Writer} from '@loaders.gl/loader-utils';\nimport {VERSION} from './lib/utils/version';\nimport encodeGLBSync from './lib/encoders/encode-glb';\n\n/**\n * GLB exporter\n * GLB is the binary container format for GLTF\n */\nexport const GLBWriter = {\n  name: 'GL<PERSON>',\n  id: 'glb',\n  module: 'gltf',\n  version: VERSION,\n\n  extensions: ['glb'],\n  mimeTypes: ['model/gltf-binary'],\n  binary: true,\n\n  encodeSync,\n\n  options: {\n    glb: {}\n  }\n};\n\nfunction encodeSync(glb, options) {\n  const {byteOffset = 0} = options;\n\n  // Calculate length and allocate buffer\n  const byteLength = encodeGLBSync(glb, null, byteOffset, options);\n  const arrayBuffer = new ArrayBuffer(byteLength);\n\n  // Encode into buffer\n  const dataView = new DataView(arrayBuffer);\n  encodeGLBSync(glb, dataView, byteOffset, options);\n\n  return arrayBuffer;\n}\n\n// TYPE TESTS - TODO find a better way than exporting junk\nexport const _TypecheckGLBLoader: Writer = GLBWriter;\n"], "mappings": ";;;;;;;AACA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAC,sBAAA,CAAAF,OAAA;AAMO,MAAMG,SAAS,GAAG;EACvBC,IAAI,EAAE,KAAK;EACXC,EAAE,EAAE,KAAK;EACTC,MAAM,EAAE,MAAM;EACdC,OAAO,EAAEC,gBAAO;EAEhBC,UAAU,EAAE,CAAC,KAAK,CAAC;EACnBC,SAAS,EAAE,CAAC,mBAAmB,CAAC;EAChCC,MAAM,EAAE,IAAI;EAEZC,UAAU;EAEVC,OAAO,EAAE;IACPC,GAAG,EAAE,CAAC;EACR;AACF,CAAC;AAACC,OAAA,CAAAZ,SAAA,GAAAA,SAAA;AAEF,SAASS,UAAUA,CAACE,GAAG,EAAED,OAAO,EAAE;EAChC,MAAM;IAACG,UAAU,GAAG;EAAC,CAAC,GAAGH,OAAO;EAGhC,MAAMI,UAAU,GAAG,IAAAC,kBAAa,EAACJ,GAAG,EAAE,IAAI,EAAEE,UAAU,EAAEH,OAAO,CAAC;EAChE,MAAMM,WAAW,GAAG,IAAIC,WAAW,CAACH,UAAU,CAAC;EAG/C,MAAMI,QAAQ,GAAG,IAAIC,QAAQ,CAACH,WAAW,CAAC;EAC1C,IAAAD,kBAAa,EAACJ,GAAG,EAAEO,QAAQ,EAAEL,UAAU,EAAEH,OAAO,CAAC;EAEjD,OAAOM,WAAW;AACpB;AAGO,MAAMI,mBAA2B,GAAGpB,SAAS;AAACY,OAAA,CAAAQ,mBAAA,GAAAA,mBAAA"}