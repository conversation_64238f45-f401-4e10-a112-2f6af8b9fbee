"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports._TypecheckGLBLoader = exports.GLBWriter = void 0;
var _version = require("./lib/utils/version");
var _encodeGlb = _interopRequireDefault(require("./lib/encoders/encode-glb"));
const GLBWriter = {
  name: 'GLB',
  id: 'glb',
  module: 'gltf',
  version: _version.VERSION,
  extensions: ['glb'],
  mimeTypes: ['model/gltf-binary'],
  binary: true,
  encodeSync,
  options: {
    glb: {}
  }
};
exports.GLBWriter = GLBWriter;
function encodeSync(glb, options) {
  const {
    byteOffset = 0
  } = options;
  const byteLength = (0, _encodeGlb.default)(glb, null, byteOffset, options);
  const arrayBuffer = new ArrayBuffer(byteLength);
  const dataView = new DataView(arrayBuffer);
  (0, _encodeGlb.default)(glb, dataView, byteOffset, options);
  return arrayBuffer;
}
const _TypecheckGLBLoader = GLBWriter;
exports._TypecheckGLBLoader = _TypecheckGLBLoader;
//# sourceMappingURL=glb-writer.js.map