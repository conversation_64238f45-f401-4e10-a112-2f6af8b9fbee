{"version": 3, "file": "glb-loader.js", "names": ["_version", "require", "_parseGlb", "_interopRequireDefault", "GLBL<PERSON>der", "name", "id", "module", "version", "VERSION", "extensions", "mimeTypes", "binary", "parse", "parseSync", "options", "glb", "strict", "exports", "arrayBuffer", "byteOffset", "parseGLBSync", "_TypecheckGLBLoader"], "sources": ["../../src/glb-loader.ts"], "sourcesContent": ["import type {LoaderWithParser, LoaderOptions} from '@loaders.gl/loader-utils';\nimport type {GLB} from './lib/types/glb-types';\nimport type {GLBParseOptions} from './lib/parsers/parse-glb';\nimport {VERSION} from './lib/utils/version';\nimport parseGLBSync from './lib/parsers/parse-glb';\n\nexport type GLBLoaderOptions = LoaderOptions & {\n  glb?: GLBParseOptions;\n  byteOffset?: number;\n};\n\n/**\n * GLB Loader -\n * GLB is the binary container format for GLTF\n */\nexport const GLBLoader: LoaderWithParser = {\n  name: 'GLB',\n  id: 'glb',\n  module: 'gltf',\n  version: VERSION,\n  extensions: ['glb'],\n  mimeTypes: ['model/gltf-binary'],\n  binary: true,\n  parse,\n  parseSync,\n  options: {\n    glb: {\n      strict: false // Enables deprecated XVIZ support (illegal CHUNK formats)\n    }\n  }\n};\n\nasync function parse(arrayBuffer: ArrayBuffer, options?: GLBLoaderOptions): Promise<GLB> {\n  return parseSync(arrayBuffer, options);\n}\n\nfunction parseSync(arrayBuffer: ArrayBuffer, options?: GLBLoaderOptions): GLB {\n  const {byteOffset = 0} = options || {};\n  const glb: GLB = {} as GLB;\n  parseGLBSync(glb, arrayBuffer, byteOffset, options?.glb);\n  return glb;\n}\n\n// TYPE TESTS - TODO find a better way than exporting junk\nexport const _TypecheckGLBLoader: LoaderWithParser = GLBLoader;\n"], "mappings": ";;;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAC,sBAAA,CAAAF,OAAA;AAWO,MAAMG,SAA2B,GAAG;EACzCC,IAAI,EAAE,KAAK;EACXC,EAAE,EAAE,KAAK;EACTC,MAAM,EAAE,MAAM;EACdC,OAAO,EAAEC,gBAAO;EAChBC,UAAU,EAAE,CAAC,KAAK,CAAC;EACnBC,SAAS,EAAE,CAAC,mBAAmB,CAAC;EAChCC,MAAM,EAAE,IAAI;EACZC,KAAK;EACLC,SAAS;EACTC,OAAO,EAAE;IACPC,GAAG,EAAE;MACHC,MAAM,EAAE;IACV;EACF;AACF,CAAC;AAACC,OAAA,CAAAd,SAAA,GAAAA,SAAA;AAEF,eAAeS,KAAKA,CAACM,WAAwB,EAAEJ,OAA0B,EAAgB;EACvF,OAAOD,SAAS,CAACK,WAAW,EAAEJ,OAAO,CAAC;AACxC;AAEA,SAASD,SAASA,CAACK,WAAwB,EAAEJ,OAA0B,EAAO;EAC5E,MAAM;IAACK,UAAU,GAAG;EAAC,CAAC,GAAGL,OAAO,IAAI,CAAC,CAAC;EACtC,MAAMC,GAAQ,GAAG,CAAC,CAAQ;EAC1B,IAAAK,iBAAY,EAACL,GAAG,EAAEG,WAAW,EAAEC,UAAU,EAAEL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEC,GAAG,CAAC;EACxD,OAAOA,GAAG;AACZ;AAGO,MAAMM,mBAAqC,GAAGlB,SAAS;AAACc,OAAA,CAAAI,mBAAA,GAAAA,mBAAA"}