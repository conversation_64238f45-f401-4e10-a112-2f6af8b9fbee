"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports._TypecheckGLBLoader = exports.GLBLoader = void 0;
var _version = require("./lib/utils/version");
var _parseGlb = _interopRequireDefault(require("./lib/parsers/parse-glb"));
const GLBLoader = {
  name: 'GLB',
  id: 'glb',
  module: 'gltf',
  version: _version.VERSION,
  extensions: ['glb'],
  mimeTypes: ['model/gltf-binary'],
  binary: true,
  parse,
  parseSync,
  options: {
    glb: {
      strict: false
    }
  }
};
exports.GLBLoader = GLBLoader;
async function parse(arrayBuffer, options) {
  return parseSync(arrayBuffer, options);
}
function parseSync(arrayBuffer, options) {
  const {
    byteOffset = 0
  } = options || {};
  const glb = {};
  (0, _parseGlb.default)(glb, arrayBuffer, byteOffset, options === null || options === void 0 ? void 0 : options.glb);
  return glb;
}
const _TypecheckGLBLoader = GLBLoader;
exports._TypecheckGLBLoader = _TypecheckGLBLoader;
//# sourceMappingURL=glb-loader.js.map