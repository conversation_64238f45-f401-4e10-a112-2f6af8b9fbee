"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "GLBLoader", {
  enumerable: true,
  get: function () {
    return _glbLoader.GLBLoader;
  }
});
Object.defineProperty(exports, "GLBWriter", {
  enumerable: true,
  get: function () {
    return _glbWriter.GLBWriter;
  }
});
Object.defineProperty(exports, "GLTFLoader", {
  enumerable: true,
  get: function () {
    return _gltfLoader.GLTFLoader;
  }
});
Object.defineProperty(exports, "GLTFScenegraph", {
  enumerable: true,
  get: function () {
    return _gltfScenegraph.default;
  }
});
Object.defineProperty(exports, "GLTFWriter", {
  enumerable: true,
  get: function () {
    return _gltfWriter.GLTFWriter;
  }
});
Object.defineProperty(exports, "_getMemoryUsageGLTF", {
  enumerable: true,
  get: function () {
    return _gltfUtils.getMemoryUsageGLTF;
  }
});
Object.defineProperty(exports, "postProcessGLTF", {
  enumerable: true,
  get: function () {
    return _postProcessGltf.postProcessGLTF;
  }
});
var _gltfLoader = require("./gltf-loader");
var _gltfWriter = require("./gltf-writer");
var _glbLoader = require("./glb-loader");
var _glbWriter = require("./glb-writer");
var _gltfScenegraph = _interopRequireDefault(require("./lib/api/gltf-scenegraph"));
var _postProcessGltf = require("./lib/api/post-process-gltf");
var _gltfUtils = require("./lib/gltf-utils/gltf-utils");
//# sourceMappingURL=index.js.map