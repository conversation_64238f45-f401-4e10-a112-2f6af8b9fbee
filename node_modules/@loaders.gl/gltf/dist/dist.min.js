(() => {
  var __defProp = Object.defineProperty;
  var __markAsModule = (target) => __defProp(target, "__esModule", { value: true });
  var __esm = (fn, res) => function __init() {
    return fn && (res = (0, fn[Object.keys(fn)[0]])(fn = 0)), res;
  };
  var __commonJS = (cb, mod) => function __require() {
    return mod || (0, cb[Object.keys(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
  };
  var __export = (target, all) => {
    __markAsModule(target);
    for (var name10 in all)
      __defProp(target, name10, { get: all[name10], enumerable: true });
  };

  // src/lib/utils/version.ts
  var VERSION;
  var init_version = __esm({
    "src/lib/utils/version.ts"() {
      VERSION = typeof __VERSION__ !== "undefined" ? __VERSION__ : "latest";
    }
  });

  // ../loader-utils/src/lib/env-utils/assert.ts
  function assert(condition, message) {
    if (!condition) {
      throw new Error(message || "loader assertion failed.");
    }
  }
  var init_assert = __esm({
    "../loader-utils/src/lib/env-utils/assert.ts"() {
    }
  });

  // ../loader-utils/src/lib/env-utils/globals.ts
  var globals, self_, window_, global_, document_, isBrowser, matches, nodeVersion;
  var init_globals = __esm({
    "../loader-utils/src/lib/env-utils/globals.ts"() {
      globals = {
        self: typeof self !== "undefined" && self,
        window: typeof window !== "undefined" && window,
        global: typeof global !== "undefined" && global,
        document: typeof document !== "undefined" && document
      };
      self_ = globals.self || globals.window || globals.global || {};
      window_ = globals.window || globals.self || globals.global || {};
      global_ = globals.global || globals.self || globals.window || {};
      document_ = globals.document || {};
      isBrowser = Boolean(typeof process !== "object" || String(process) !== "[object process]" || process.browser);
      matches = typeof process !== "undefined" && process.version && /v([0-9]*)/.exec(process.version);
      nodeVersion = matches && parseFloat(matches[1]) || 0;
    }
  });

  // ../worker-utils/src/lib/env-utils/version.ts
  var DEFAULT_VERSION, VERSION2;
  var init_version2 = __esm({
    "../worker-utils/src/lib/env-utils/version.ts"() {
      DEFAULT_VERSION = "latest";
      VERSION2 = typeof __VERSION__ !== "undefined" ? __VERSION__ : DEFAULT_VERSION;
      if (typeof __VERSION__ === "undefined") {
        console.error("loaders.gl: The __VERSION__ variable is not injected using babel plugin. Latest unstable workers would be fetched from the CDN.");
      }
    }
  });

  // ../worker-utils/src/lib/env-utils/assert.ts
  function assert2(condition, message) {
    if (!condition) {
      throw new Error(message || "loaders.gl assertion failed.");
    }
  }
  var init_assert2 = __esm({
    "../worker-utils/src/lib/env-utils/assert.ts"() {
    }
  });

  // ../worker-utils/src/lib/env-utils/globals.ts
  var globals2, self_2, window_2, global_2, document_2, isBrowser2, isWorker, isMobile, matches2, nodeVersion2;
  var init_globals2 = __esm({
    "../worker-utils/src/lib/env-utils/globals.ts"() {
      globals2 = {
        self: typeof self !== "undefined" && self,
        window: typeof window !== "undefined" && window,
        global: typeof global !== "undefined" && global,
        document: typeof document !== "undefined" && document
      };
      self_2 = globals2.self || globals2.window || globals2.global || {};
      window_2 = globals2.window || globals2.self || globals2.global || {};
      global_2 = globals2.global || globals2.self || globals2.window || {};
      document_2 = globals2.document || {};
      isBrowser2 = typeof process !== "object" || String(process) !== "[object process]" || process.browser;
      isWorker = typeof importScripts === "function";
      isMobile = typeof window !== "undefined" && typeof window.orientation !== "undefined";
      matches2 = typeof process !== "undefined" && process.version && /v([0-9]*)/.exec(process.version);
      nodeVersion2 = matches2 && parseFloat(matches2[1]) || 0;
    }
  });

  // ../worker-utils/src/lib/node/require-utils.browser.ts
  var require_utils_browser_exports = {};
  __export(require_utils_browser_exports, {
    readFileAsArrayBuffer: () => readFileAsArrayBuffer,
    readFileAsText: () => readFileAsText,
    requireFromFile: () => requireFromFile,
    requireFromString: () => requireFromString
  });
  var readFileAsArrayBuffer, readFileAsText, requireFromFile, requireFromString;
  var init_require_utils_browser = __esm({
    "../worker-utils/src/lib/node/require-utils.browser.ts"() {
      readFileAsArrayBuffer = null;
      readFileAsText = null;
      requireFromFile = null;
      requireFromString = null;
    }
  });

  // ../worker-utils/src/lib/library-utils/library-utils.ts
  async function loadLibrary(libraryUrl, moduleName = null, options = {}) {
    if (moduleName) {
      libraryUrl = getLibraryUrl(libraryUrl, moduleName, options);
    }
    loadLibraryPromises[libraryUrl] = loadLibraryPromises[libraryUrl] || loadLibraryFromFile(libraryUrl);
    return await loadLibraryPromises[libraryUrl];
  }
  function getLibraryUrl(library, moduleName, options) {
    if (library.startsWith("http")) {
      return library;
    }
    const modules = options.modules || {};
    if (modules[library]) {
      return modules[library];
    }
    if (!isBrowser2) {
      return `modules/${moduleName}/dist/libs/${library}`;
    }
    if (options.CDN) {
      assert2(options.CDN.startsWith("http"));
      return `${options.CDN}/${moduleName}@${VERSION3}/dist/libs/${library}`;
    }
    if (isWorker) {
      return `../src/libs/${library}`;
    }
    return `modules/${moduleName}/src/libs/${library}`;
  }
  async function loadLibraryFromFile(libraryUrl) {
    if (libraryUrl.endsWith("wasm")) {
      const response2 = await fetch(libraryUrl);
      return await response2.arrayBuffer();
    }
    if (!isBrowser2) {
      try {
        return require_utils_browser_exports && requireFromFile && await requireFromFile(libraryUrl);
      } catch {
        return null;
      }
    }
    if (isWorker) {
      return importScripts(libraryUrl);
    }
    const response = await fetch(libraryUrl);
    const scriptSource = await response.text();
    return loadLibraryFromString(scriptSource, libraryUrl);
  }
  function loadLibraryFromString(scriptSource, id) {
    if (!isBrowser2) {
      return requireFromString && requireFromString(scriptSource, id);
    }
    if (isWorker) {
      eval.call(global_2, scriptSource);
      return null;
    }
    const script = document.createElement("script");
    script.id = id;
    try {
      script.appendChild(document.createTextNode(scriptSource));
    } catch (e) {
      script.text = scriptSource;
    }
    document.body.appendChild(script);
    return null;
  }
  var LATEST, VERSION3, loadLibraryPromises;
  var init_library_utils = __esm({
    "../worker-utils/src/lib/library-utils/library-utils.ts"() {
      init_globals2();
      init_require_utils_browser();
      init_assert2();
      init_version2();
      LATEST = "latest";
      VERSION3 = typeof VERSION2 !== "undefined" ? VERSION2 : LATEST;
      loadLibraryPromises = {};
    }
  });

  // ../worker-utils/src/index.ts
  var init_src = __esm({
    "../worker-utils/src/index.ts"() {
      init_globals2();
      init_library_utils();
    }
  });

  // ../loader-utils/src/lib/binary-utils/get-first-characters.ts
  function getFirstCharacters(data, length = 5) {
    if (typeof data === "string") {
      return data.slice(0, length);
    } else if (ArrayBuffer.isView(data)) {
      return getMagicString(data.buffer, data.byteOffset, length);
    } else if (data instanceof ArrayBuffer) {
      const byteOffset = 0;
      return getMagicString(data, byteOffset, length);
    }
    return "";
  }
  function getMagicString(arrayBuffer, byteOffset, length) {
    if (arrayBuffer.byteLength <= byteOffset + length) {
      return "";
    }
    const dataView = new DataView(arrayBuffer);
    let magic = "";
    for (let i2 = 0; i2 < length; i2++) {
      magic += String.fromCharCode(dataView.getUint8(byteOffset + i2));
    }
    return magic;
  }
  var init_get_first_characters = __esm({
    "../loader-utils/src/lib/binary-utils/get-first-characters.ts"() {
    }
  });

  // ../loader-utils/src/lib/parser-utils/parse-json.ts
  function parseJSON(string) {
    try {
      return JSON.parse(string);
    } catch (_) {
      throw new Error(`Failed to parse JSON from data starting with "${getFirstCharacters(string)}"`);
    }
  }
  var init_parse_json = __esm({
    "../loader-utils/src/lib/parser-utils/parse-json.ts"() {
      init_get_first_characters();
    }
  });

  // ../loader-utils/src/lib/binary-utils/array-buffer-utils.ts
  function sliceArrayBuffer(arrayBuffer, byteOffset, byteLength) {
    const subArray = byteLength !== void 0 ? new Uint8Array(arrayBuffer).subarray(byteOffset, byteOffset + byteLength) : new Uint8Array(arrayBuffer).subarray(byteOffset);
    const arrayCopy = new Uint8Array(subArray);
    return arrayCopy.buffer;
  }
  var init_array_buffer_utils = __esm({
    "../loader-utils/src/lib/binary-utils/array-buffer-utils.ts"() {
    }
  });

  // ../loader-utils/src/lib/binary-utils/memory-copy-utils.ts
  function padToNBytes(byteLength, padding) {
    assert(byteLength >= 0);
    assert(padding > 0);
    return byteLength + (padding - 1) & ~(padding - 1);
  }
  function copyToArray(source, target, targetOffset) {
    let sourceArray;
    if (source instanceof ArrayBuffer) {
      sourceArray = new Uint8Array(source);
    } else {
      const srcByteOffset = source.byteOffset;
      const srcByteLength = source.byteLength;
      sourceArray = new Uint8Array(source.buffer || source.arrayBuffer, srcByteOffset, srcByteLength);
    }
    target.set(sourceArray, targetOffset);
    return targetOffset + padToNBytes(sourceArray.byteLength, 4);
  }
  var init_memory_copy_utils = __esm({
    "../loader-utils/src/lib/binary-utils/memory-copy-utils.ts"() {
      init_assert();
    }
  });

  // ../loader-utils/src/lib/binary-utils/dataview-copy-utils.ts
  function copyPaddedArrayBufferToDataView(dataView, byteOffset, sourceBuffer, padding) {
    const paddedLength = padToNBytes(sourceBuffer.byteLength, padding);
    const padLength = paddedLength - sourceBuffer.byteLength;
    if (dataView) {
      const targetArray = new Uint8Array(dataView.buffer, dataView.byteOffset + byteOffset, sourceBuffer.byteLength);
      const sourceArray = new Uint8Array(sourceBuffer);
      targetArray.set(sourceArray);
      for (let i2 = 0; i2 < padLength; ++i2) {
        dataView.setUint8(byteOffset + sourceBuffer.byteLength + i2, 32);
      }
    }
    byteOffset += paddedLength;
    return byteOffset;
  }
  function copyPaddedStringToDataView(dataView, byteOffset, string, padding) {
    const textEncoder = new TextEncoder();
    const stringBuffer = textEncoder.encode(string);
    byteOffset = copyPaddedArrayBufferToDataView(dataView, byteOffset, stringBuffer, padding);
    return byteOffset;
  }
  var init_dataview_copy_utils = __esm({
    "../loader-utils/src/lib/binary-utils/dataview-copy-utils.ts"() {
      init_memory_copy_utils();
    }
  });

  // ../loader-utils/src/index.ts
  var init_src2 = __esm({
    "../loader-utils/src/index.ts"() {
      init_assert();
      init_globals();
      init_parse_json();
      init_array_buffer_utils();
      init_memory_copy_utils();
      init_dataview_copy_utils();
    }
  });

  // ../textures/src/lib/utils/version.ts
  var VERSION4;
  var init_version3 = __esm({
    "../textures/src/lib/utils/version.ts"() {
      VERSION4 = typeof __VERSION__ !== "undefined" ? __VERSION__ : "latest";
    }
  });

  // ../textures/src/lib/parsers/basis-module-loader.ts
  async function loadBasisTrascoderModule(options) {
    const modules = options.modules || {};
    if (modules.basis) {
      return modules.basis;
    }
    loadBasisTranscoderPromise = loadBasisTranscoderPromise || loadBasisTrascoder(options);
    return await loadBasisTranscoderPromise;
  }
  async function loadBasisTrascoder(options) {
    let BASIS = null;
    let wasmBinary = null;
    [BASIS, wasmBinary] = await Promise.all([
      await loadLibrary("basis_transcoder.js", "textures", options),
      await loadLibrary("basis_transcoder.wasm", "textures", options)
    ]);
    BASIS = BASIS || globalThis.BASIS;
    return await initializeBasisTrascoderModule(BASIS, wasmBinary);
  }
  function initializeBasisTrascoderModule(BasisModule, wasmBinary) {
    const options = {};
    if (wasmBinary) {
      options.wasmBinary = wasmBinary;
    }
    return new Promise((resolve) => {
      BasisModule(options).then((module) => {
        const { BasisFile, initializeBasis } = module;
        initializeBasis();
        resolve({ BasisFile });
      });
    });
  }
  async function loadBasisEncoderModule(options) {
    const modules = options.modules || {};
    if (modules.basisEncoder) {
      return modules.basisEncoder;
    }
    loadBasisEncoderPromise = loadBasisEncoderPromise || loadBasisEncoder(options);
    return await loadBasisEncoderPromise;
  }
  async function loadBasisEncoder(options) {
    let BASIS_ENCODER = null;
    let wasmBinary = null;
    [BASIS_ENCODER, wasmBinary] = await Promise.all([
      await loadLibrary(BASIS_CDN_ENCODER_JS, "textures", options),
      await loadLibrary(BASIS_CDN_ENCODER_WASM, "textures", options)
    ]);
    BASIS_ENCODER = BASIS_ENCODER || globalThis.BASIS;
    return await initializeBasisEncoderModule(BASIS_ENCODER, wasmBinary);
  }
  function initializeBasisEncoderModule(BasisEncoderModule, wasmBinary) {
    const options = {};
    if (wasmBinary) {
      options.wasmBinary = wasmBinary;
    }
    return new Promise((resolve) => {
      BasisEncoderModule(options).then((module) => {
        const { BasisFile, KTX2File, initializeBasis, BasisEncoder } = module;
        initializeBasis();
        resolve({ BasisFile, KTX2File, BasisEncoder });
      });
    });
  }
  var VERSION5, BASIS_CDN_ENCODER_WASM, BASIS_CDN_ENCODER_JS, loadBasisTranscoderPromise, loadBasisEncoderPromise;
  var init_basis_module_loader = __esm({
    "../textures/src/lib/parsers/basis-module-loader.ts"() {
      init_src();
      VERSION5 = typeof __VERSION__ !== "undefined" ? __VERSION__ : "latest";
      BASIS_CDN_ENCODER_WASM = `https://unpkg.com/@loaders.gl/textures@${VERSION5}/dist/libs/basis_encoder.wasm`;
      BASIS_CDN_ENCODER_JS = `https://unpkg.com/@loaders.gl/textures@${VERSION5}/dist/libs/basis_encoder.js`;
    }
  });

  // ../textures/src/lib/gl-extensions.ts
  var GL_EXTENSIONS_CONSTANTS;
  var init_gl_extensions = __esm({
    "../textures/src/lib/gl-extensions.ts"() {
      GL_EXTENSIONS_CONSTANTS = {
        COMPRESSED_RGB_S3TC_DXT1_EXT: 33776,
        COMPRESSED_RGBA_S3TC_DXT1_EXT: 33777,
        COMPRESSED_RGBA_S3TC_DXT3_EXT: 33778,
        COMPRESSED_RGBA_S3TC_DXT5_EXT: 33779,
        COMPRESSED_R11_EAC: 37488,
        COMPRESSED_SIGNED_R11_EAC: 37489,
        COMPRESSED_RG11_EAC: 37490,
        COMPRESSED_SIGNED_RG11_EAC: 37491,
        COMPRESSED_RGB8_ETC2: 37492,
        COMPRESSED_RGBA8_ETC2_EAC: 37493,
        COMPRESSED_SRGB8_ETC2: 37494,
        COMPRESSED_SRGB8_ALPHA8_ETC2_EAC: 37495,
        COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2: 37496,
        COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2: 37497,
        COMPRESSED_RGB_PVRTC_4BPPV1_IMG: 35840,
        COMPRESSED_RGBA_PVRTC_4BPPV1_IMG: 35842,
        COMPRESSED_RGB_PVRTC_2BPPV1_IMG: 35841,
        COMPRESSED_RGBA_PVRTC_2BPPV1_IMG: 35843,
        COMPRESSED_RGB_ETC1_WEBGL: 36196,
        COMPRESSED_RGB_ATC_WEBGL: 35986,
        COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL: 35987,
        COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL: 34798,
        COMPRESSED_RGBA_ASTC_4X4_KHR: 37808,
        COMPRESSED_RGBA_ASTC_5X4_KHR: 37809,
        COMPRESSED_RGBA_ASTC_5X5_KHR: 37810,
        COMPRESSED_RGBA_ASTC_6X5_KHR: 37811,
        COMPRESSED_RGBA_ASTC_6X6_KHR: 37812,
        COMPRESSED_RGBA_ASTC_8X5_KHR: 37813,
        COMPRESSED_RGBA_ASTC_8X6_KHR: 37814,
        COMPRESSED_RGBA_ASTC_8X8_KHR: 37815,
        COMPRESSED_RGBA_ASTC_10X5_KHR: 37816,
        COMPRESSED_RGBA_ASTC_10X6_KHR: 37817,
        COMPRESSED_RGBA_ASTC_10X8_KHR: 37818,
        COMPRESSED_RGBA_ASTC_10X10_KHR: 37819,
        COMPRESSED_RGBA_ASTC_12X10_KHR: 37820,
        COMPRESSED_RGBA_ASTC_12X12_KHR: 37821,
        COMPRESSED_SRGB8_ALPHA8_ASTC_4X4_KHR: 37840,
        COMPRESSED_SRGB8_ALPHA8_ASTC_5X4_KHR: 37841,
        COMPRESSED_SRGB8_ALPHA8_ASTC_5X5_KHR: 37842,
        COMPRESSED_SRGB8_ALPHA8_ASTC_6X5_KHR: 37843,
        COMPRESSED_SRGB8_ALPHA8_ASTC_6X6_KHR: 37844,
        COMPRESSED_SRGB8_ALPHA8_ASTC_8X5_KHR: 37845,
        COMPRESSED_SRGB8_ALPHA8_ASTC_8X6_KHR: 37846,
        COMPRESSED_SRGB8_ALPHA8_ASTC_8X8_KHR: 37847,
        COMPRESSED_SRGB8_ALPHA8_ASTC_10X5_KHR: 37848,
        COMPRESSED_SRGB8_ALPHA8_ASTC_10X6_KHR: 37849,
        COMPRESSED_SRGB8_ALPHA8_ASTC_10X8_KHR: 37850,
        COMPRESSED_SRGB8_ALPHA8_ASTC_10X10_KHR: 37851,
        COMPRESSED_SRGB8_ALPHA8_ASTC_12X10_KHR: 37852,
        COMPRESSED_SRGB8_ALPHA8_ASTC_12X12_KHR: 37853,
        COMPRESSED_RED_RGTC1_EXT: 36283,
        COMPRESSED_SIGNED_RED_RGTC1_EXT: 36284,
        COMPRESSED_RED_GREEN_RGTC2_EXT: 36285,
        COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT: 36286,
        COMPRESSED_SRGB_S3TC_DXT1_EXT: 35916,
        COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT: 35917,
        COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT: 35918,
        COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT: 35919
      };
    }
  });

  // ../textures/src/lib/utils/texture-formats.ts
  function getSupportedGPUTextureFormats(gl) {
    if (!formats) {
      gl = gl || getWebGLContext() || void 0;
      formats = new Set();
      for (const prefix of BROWSER_PREFIXES) {
        for (const extension in WEBGL_EXTENSIONS) {
          if (gl && gl.getExtension(`${prefix}${extension}`)) {
            const gpuTextureFormat = WEBGL_EXTENSIONS[extension];
            formats.add(gpuTextureFormat);
          }
        }
      }
    }
    return formats;
  }
  function getWebGLContext() {
    try {
      const canvas = document.createElement("canvas");
      return canvas.getContext("webgl");
    } catch (error) {
      return null;
    }
  }
  var BROWSER_PREFIXES, WEBGL_EXTENSIONS, formats;
  var init_texture_formats = __esm({
    "../textures/src/lib/utils/texture-formats.ts"() {
      BROWSER_PREFIXES = ["", "WEBKIT_", "MOZ_"];
      WEBGL_EXTENSIONS = {
        WEBGL_compressed_texture_s3tc: "dxt",
        WEBGL_compressed_texture_s3tc_srgb: "dxt-srgb",
        WEBGL_compressed_texture_etc1: "etc1",
        WEBGL_compressed_texture_etc: "etc2",
        WEBGL_compressed_texture_pvrtc: "pvrtc",
        WEBGL_compressed_texture_atc: "atc",
        WEBGL_compressed_texture_astc: "astc",
        EXT_texture_compression_rgtc: "rgtc"
      };
      formats = null;
    }
  });

  // ../../node_modules/ktx-parse/dist/ktx-parse.modern.js
  var t, n, i, s, a, r, o, l, f;
  var init_ktx_parse_modern = __esm({
    "../../node_modules/ktx-parse/dist/ktx-parse.modern.js"() {
      t = new Uint8Array([0]);
      !function(t2) {
        t2[t2.NONE = 0] = "NONE", t2[t2.BASISLZ = 1] = "BASISLZ", t2[t2.ZSTD = 2] = "ZSTD", t2[t2.ZLIB = 3] = "ZLIB";
      }(n || (n = {})), function(t2) {
        t2[t2.BASICFORMAT = 0] = "BASICFORMAT";
      }(i || (i = {})), function(t2) {
        t2[t2.UNSPECIFIED = 0] = "UNSPECIFIED", t2[t2.ETC1S = 163] = "ETC1S", t2[t2.UASTC = 166] = "UASTC";
      }(s || (s = {})), function(t2) {
        t2[t2.UNSPECIFIED = 0] = "UNSPECIFIED", t2[t2.SRGB = 1] = "SRGB";
      }(a || (a = {})), function(t2) {
        t2[t2.UNSPECIFIED = 0] = "UNSPECIFIED", t2[t2.LINEAR = 1] = "LINEAR", t2[t2.SRGB = 2] = "SRGB", t2[t2.ITU = 3] = "ITU", t2[t2.NTSC = 4] = "NTSC", t2[t2.SLOG = 5] = "SLOG", t2[t2.SLOG2 = 6] = "SLOG2";
      }(r || (r = {})), function(t2) {
        t2[t2.ALPHA_STRAIGHT = 0] = "ALPHA_STRAIGHT", t2[t2.ALPHA_PREMULTIPLIED = 1] = "ALPHA_PREMULTIPLIED";
      }(o || (o = {})), function(t2) {
        t2[t2.RGB = 0] = "RGB", t2[t2.RRR = 3] = "RRR", t2[t2.GGG = 4] = "GGG", t2[t2.AAA = 15] = "AAA";
      }(l || (l = {})), function(t2) {
        t2[t2.RGB = 0] = "RGB", t2[t2.RGBA = 3] = "RGBA", t2[t2.RRR = 4] = "RRR", t2[t2.RRRG = 5] = "RRRG";
      }(f || (f = {}));
    }
  });

  // ../textures/src/lib/parsers/parse-ktx.ts
  function isKTX(data) {
    const id = new Uint8Array(data);
    const notKTX = id.byteLength < KTX2_ID.length || id[0] !== KTX2_ID[0] || id[1] !== KTX2_ID[1] || id[2] !== KTX2_ID[2] || id[3] !== KTX2_ID[3] || id[4] !== KTX2_ID[4] || id[5] !== KTX2_ID[5] || id[6] !== KTX2_ID[6] || id[7] !== KTX2_ID[7] || id[8] !== KTX2_ID[8] || id[9] !== KTX2_ID[9] || id[10] !== KTX2_ID[10] || id[11] !== KTX2_ID[11];
    return !notKTX;
  }
  var KTX2_ID;
  var init_parse_ktx = __esm({
    "../textures/src/lib/parsers/parse-ktx.ts"() {
      init_ktx_parse_modern();
      KTX2_ID = [
        171,
        75,
        84,
        88,
        32,
        50,
        48,
        187,
        13,
        10,
        26,
        10
      ];
    }
  });

  // ../textures/src/lib/parsers/parse-basis.ts
  async function parseBasis(data, options) {
    if (options.basis.containerFormat === "auto") {
      if (isKTX(data)) {
        const fileConstructors = await loadBasisEncoderModule(options);
        return parseKTX2File(fileConstructors.KTX2File, data, options);
      }
      const { BasisFile } = await loadBasisTrascoderModule(options);
      return parseBasisFile(BasisFile, data, options);
    }
    switch (options.basis.module) {
      case "encoder":
        const fileConstructors = await loadBasisEncoderModule(options);
        switch (options.basis.containerFormat) {
          case "ktx2":
            return parseKTX2File(fileConstructors.KTX2File, data, options);
          case "basis":
          default:
            return parseBasisFile(fileConstructors.BasisFile, data, options);
        }
      case "transcoder":
      default:
        const { BasisFile } = await loadBasisTrascoderModule(options);
        return parseBasisFile(BasisFile, data, options);
    }
  }
  function parseBasisFile(BasisFile, data, options) {
    const basisFile = new BasisFile(new Uint8Array(data));
    try {
      if (!basisFile.startTranscoding()) {
        throw new Error("Failed to start basis transcoding");
      }
      const imageCount = basisFile.getNumImages();
      const images = [];
      for (let imageIndex = 0; imageIndex < imageCount; imageIndex++) {
        const levelsCount = basisFile.getNumLevels(imageIndex);
        const levels = [];
        for (let levelIndex = 0; levelIndex < levelsCount; levelIndex++) {
          levels.push(transcodeImage(basisFile, imageIndex, levelIndex, options));
        }
        images.push(levels);
      }
      return images;
    } finally {
      basisFile.close();
      basisFile.delete();
    }
  }
  function transcodeImage(basisFile, imageIndex, levelIndex, options) {
    const width = basisFile.getImageWidth(imageIndex, levelIndex);
    const height = basisFile.getImageHeight(imageIndex, levelIndex);
    const hasAlpha = basisFile.getHasAlpha();
    const { compressed, format, basisFormat } = getBasisOptions(options, hasAlpha);
    const decodedSize = basisFile.getImageTranscodedSizeInBytes(imageIndex, levelIndex, basisFormat);
    const decodedData = new Uint8Array(decodedSize);
    if (!basisFile.transcodeImage(decodedData, imageIndex, levelIndex, basisFormat, 0, 0)) {
      throw new Error("failed to start Basis transcoding");
    }
    return {
      width,
      height,
      data: decodedData,
      compressed,
      format,
      hasAlpha
    };
  }
  function parseKTX2File(KTX2File, data, options) {
    const ktx2File = new KTX2File(new Uint8Array(data));
    try {
      if (!ktx2File.startTranscoding()) {
        throw new Error("failed to start KTX2 transcoding");
      }
      const levelsCount = ktx2File.getLevels();
      const levels = [];
      for (let levelIndex = 0; levelIndex < levelsCount; levelIndex++) {
        levels.push(transcodeKTX2Image(ktx2File, levelIndex, options));
        break;
      }
      return [levels];
    } finally {
      ktx2File.close();
      ktx2File.delete();
    }
  }
  function transcodeKTX2Image(ktx2File, levelIndex, options) {
    const { alphaFlag, height, width } = ktx2File.getImageLevelInfo(levelIndex, 0, 0);
    const { compressed, format, basisFormat } = getBasisOptions(options, alphaFlag);
    const decodedSize = ktx2File.getImageTranscodedSizeInBytes(levelIndex, 0, 0, basisFormat);
    const decodedData = new Uint8Array(decodedSize);
    if (!ktx2File.transcodeImage(decodedData, levelIndex, 0, 0, basisFormat, 0, -1, -1)) {
      throw new Error("Failed to transcode KTX2 image");
    }
    return {
      width,
      height,
      data: decodedData,
      compressed,
      levelSize: decodedSize,
      hasAlpha: alphaFlag,
      format
    };
  }
  function getBasisOptions(options, hasAlpha) {
    let format = options && options.basis && options.basis.format;
    if (format === "auto") {
      format = selectSupportedBasisFormat();
    }
    if (typeof format === "object") {
      format = hasAlpha ? format.alpha : format.noAlpha;
    }
    format = format.toLowerCase();
    return OutputFormat[format];
  }
  function selectSupportedBasisFormat() {
    const supportedFormats = getSupportedGPUTextureFormats();
    if (supportedFormats.has("astc")) {
      return "astc-4x4";
    } else if (supportedFormats.has("dxt")) {
      return {
        alpha: "bc3",
        noAlpha: "bc1"
      };
    } else if (supportedFormats.has("pvrtc")) {
      return {
        alpha: "pvrtc1-4-rgba",
        noAlpha: "pvrtc1-4-rgb"
      };
    } else if (supportedFormats.has("etc1")) {
      return "etc1";
    } else if (supportedFormats.has("etc2")) {
      return "etc2";
    }
    return "rgb565";
  }
  var OutputFormat;
  var init_parse_basis = __esm({
    "../textures/src/lib/parsers/parse-basis.ts"() {
      init_basis_module_loader();
      init_gl_extensions();
      init_texture_formats();
      init_parse_ktx();
      OutputFormat = {
        etc1: {
          basisFormat: 0,
          compressed: true,
          format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_ETC1_WEBGL
        },
        etc2: { basisFormat: 1, compressed: true },
        bc1: {
          basisFormat: 2,
          compressed: true,
          format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_S3TC_DXT1_EXT
        },
        bc3: {
          basisFormat: 3,
          compressed: true,
          format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_S3TC_DXT5_EXT
        },
        bc4: { basisFormat: 4, compressed: true },
        bc5: { basisFormat: 5, compressed: true },
        "bc7-m6-opaque-only": { basisFormat: 6, compressed: true },
        "bc7-m5": { basisFormat: 7, compressed: true },
        "pvrtc1-4-rgb": {
          basisFormat: 8,
          compressed: true,
          format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGB_PVRTC_4BPPV1_IMG
        },
        "pvrtc1-4-rgba": {
          basisFormat: 9,
          compressed: true,
          format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG
        },
        "astc-4x4": {
          basisFormat: 10,
          compressed: true,
          format: GL_EXTENSIONS_CONSTANTS.COMPRESSED_RGBA_ASTC_4X4_KHR
        },
        "atc-rgb": { basisFormat: 11, compressed: true },
        "atc-rgba-interpolated-alpha": { basisFormat: 12, compressed: true },
        rgba32: { basisFormat: 13, compressed: false },
        rgb565: { basisFormat: 14, compressed: false },
        bgr565: { basisFormat: 15, compressed: false },
        rgba4444: { basisFormat: 16, compressed: false }
      };
    }
  });

  // ../textures/src/basis-loader.ts
  var BasisWorkerLoader, BasisLoader;
  var init_basis_loader = __esm({
    "../textures/src/basis-loader.ts"() {
      init_src();
      init_version3();
      init_parse_basis();
      BasisWorkerLoader = {
        name: "Basis",
        id: isBrowser2 ? "basis" : "basis-nodejs",
        module: "textures",
        version: VERSION4,
        worker: true,
        extensions: ["basis", "ktx2"],
        mimeTypes: ["application/octet-stream", "image/ktx2"],
        tests: ["sB"],
        binary: true,
        options: {
          basis: {
            format: "auto",
            libraryPath: "libs/",
            containerFormat: "auto",
            module: "transcoder"
          }
        }
      };
      BasisLoader = {
        ...BasisWorkerLoader,
        parse: parseBasis
      };
    }
  });

  // ../images/src/lib/utils/version.ts
  var VERSION6;
  var init_version4 = __esm({
    "../images/src/lib/utils/version.ts"() {
      VERSION6 = typeof __VERSION__ !== "undefined" ? __VERSION__ : "latest";
    }
  });

  // ../images/src/lib/category-api/image-type.ts
  function isImageTypeSupported(type) {
    switch (type) {
      case "auto":
        return IMAGE_BITMAP_SUPPORTED || IMAGE_SUPPORTED || DATA_SUPPORTED;
      case "imagebitmap":
        return IMAGE_BITMAP_SUPPORTED;
      case "image":
        return IMAGE_SUPPORTED;
      case "data":
        return DATA_SUPPORTED;
      default:
        throw new Error(`@loaders.gl/images: image ${type} not supported in this environment`);
    }
  }
  function getDefaultImageType() {
    if (IMAGE_BITMAP_SUPPORTED) {
      return "imagebitmap";
    }
    if (IMAGE_SUPPORTED) {
      return "image";
    }
    if (DATA_SUPPORTED) {
      return "data";
    }
    throw new Error("Install '@loaders.gl/polyfills' to parse images under Node.js");
  }
  var _parseImageNode, IMAGE_SUPPORTED, IMAGE_BITMAP_SUPPORTED, NODE_IMAGE_SUPPORTED, DATA_SUPPORTED;
  var init_image_type = __esm({
    "../images/src/lib/category-api/image-type.ts"() {
      init_src2();
      ({ _parseImageNode } = globalThis);
      IMAGE_SUPPORTED = typeof Image !== "undefined";
      IMAGE_BITMAP_SUPPORTED = typeof ImageBitmap !== "undefined";
      NODE_IMAGE_SUPPORTED = Boolean(_parseImageNode);
      DATA_SUPPORTED = isBrowser ? true : NODE_IMAGE_SUPPORTED;
    }
  });

  // ../images/src/lib/category-api/parsed-image-api.ts
  function getImageType(image) {
    const format = getImageTypeOrNull(image);
    if (!format) {
      throw new Error("Not an image");
    }
    return format;
  }
  function getImageData(image) {
    switch (getImageType(image)) {
      case "data":
        return image;
      case "image":
      case "imagebitmap":
        const canvas = document.createElement("canvas");
        const context = canvas.getContext("2d");
        if (!context) {
          throw new Error("getImageData");
        }
        canvas.width = image.width;
        canvas.height = image.height;
        context.drawImage(image, 0, 0);
        return context.getImageData(0, 0, image.width, image.height);
      default:
        throw new Error("getImageData");
    }
  }
  function getImageTypeOrNull(image) {
    if (typeof ImageBitmap !== "undefined" && image instanceof ImageBitmap) {
      return "imagebitmap";
    }
    if (typeof Image !== "undefined" && image instanceof Image) {
      return "image";
    }
    if (image && typeof image === "object" && image.data && image.width && image.height) {
      return "data";
    }
    return null;
  }
  var init_parsed_image_api = __esm({
    "../images/src/lib/category-api/parsed-image-api.ts"() {
    }
  });

  // ../images/src/lib/parsers/svg-utils.ts
  function isSVG(url) {
    return url && (SVG_DATA_URL_PATTERN.test(url) || SVG_URL_PATTERN.test(url));
  }
  function getBlobOrSVGDataUrl(arrayBuffer, url) {
    if (isSVG(url)) {
      const textDecoder = new TextDecoder();
      let xmlText = textDecoder.decode(arrayBuffer);
      try {
        if (typeof unescape === "function" && typeof encodeURIComponent === "function") {
          xmlText = unescape(encodeURIComponent(xmlText));
        }
      } catch (error) {
        throw new Error(error.message);
      }
      const src = `data:image/svg+xml;base64,${btoa(xmlText)}`;
      return src;
    }
    return getBlob(arrayBuffer, url);
  }
  function getBlob(arrayBuffer, url) {
    if (isSVG(url)) {
      throw new Error("SVG cannot be parsed directly to imagebitmap");
    }
    return new Blob([new Uint8Array(arrayBuffer)]);
  }
  var SVG_DATA_URL_PATTERN, SVG_URL_PATTERN;
  var init_svg_utils = __esm({
    "../images/src/lib/parsers/svg-utils.ts"() {
      SVG_DATA_URL_PATTERN = /^data:image\/svg\+xml/;
      SVG_URL_PATTERN = /\.svg((\?|#).*)?$/;
    }
  });

  // ../images/src/lib/parsers/parse-to-image.ts
  async function parseToImage(arrayBuffer, options, url) {
    const blobOrDataUrl = getBlobOrSVGDataUrl(arrayBuffer, url);
    const URL = self.URL || self.webkitURL;
    const objectUrl = typeof blobOrDataUrl !== "string" && URL.createObjectURL(blobOrDataUrl);
    try {
      return await loadToImage(objectUrl || blobOrDataUrl, options);
    } finally {
      if (objectUrl) {
        URL.revokeObjectURL(objectUrl);
      }
    }
  }
  async function loadToImage(url, options) {
    const image = new Image();
    image.src = url;
    if (options.image && options.image.decode && image.decode) {
      await image.decode();
      return image;
    }
    return await new Promise((resolve, reject) => {
      try {
        image.onload = () => resolve(image);
        image.onerror = (err) => reject(new Error(`Could not load image ${url}: ${err}`));
      } catch (error) {
        reject(error);
      }
    });
  }
  var init_parse_to_image = __esm({
    "../images/src/lib/parsers/parse-to-image.ts"() {
      init_svg_utils();
    }
  });

  // ../images/src/lib/parsers/parse-to-image-bitmap.ts
  async function parseToImageBitmap(arrayBuffer, options, url) {
    let blob;
    if (isSVG(url)) {
      const image = await parseToImage(arrayBuffer, options, url);
      blob = image;
    } else {
      blob = getBlob(arrayBuffer, url);
    }
    const imagebitmapOptions = options && options.imagebitmap;
    return await safeCreateImageBitmap(blob, imagebitmapOptions);
  }
  async function safeCreateImageBitmap(blob, imagebitmapOptions = null) {
    if (isEmptyObject(imagebitmapOptions) || !imagebitmapOptionsSupported) {
      imagebitmapOptions = null;
    }
    if (imagebitmapOptions) {
      try {
        return await createImageBitmap(blob, imagebitmapOptions);
      } catch (error) {
        console.warn(error);
        imagebitmapOptionsSupported = false;
      }
    }
    return await createImageBitmap(blob);
  }
  function isEmptyObject(object) {
    for (const key in object || EMPTY_OBJECT) {
      return false;
    }
    return true;
  }
  var EMPTY_OBJECT, imagebitmapOptionsSupported;
  var init_parse_to_image_bitmap = __esm({
    "../images/src/lib/parsers/parse-to-image-bitmap.ts"() {
      init_svg_utils();
      init_parse_to_image();
      EMPTY_OBJECT = {};
      imagebitmapOptionsSupported = true;
    }
  });

  // ../images/src/lib/category-api/parse-isobmff-binary.ts
  function getISOBMFFMediaType(buffer) {
    if (!checkString(buffer, "ftyp", 4)) {
      return null;
    }
    if ((buffer[8] & 96) === 0) {
      return null;
    }
    return decodeMajorBrand(buffer);
  }
  function decodeMajorBrand(buffer) {
    const brandMajor = getUTF8String(buffer, 8, 12).replace("\0", " ").trim();
    switch (brandMajor) {
      case "avif":
      case "avis":
        return { extension: "avif", mimeType: "image/avif" };
      default:
        return null;
    }
  }
  function getUTF8String(array, start, end) {
    return String.fromCharCode(...array.slice(start, end));
  }
  function stringToBytes(string) {
    return [...string].map((character) => character.charCodeAt(0));
  }
  function checkString(buffer, header, offset = 0) {
    const headerBytes = stringToBytes(header);
    for (let i2 = 0; i2 < headerBytes.length; ++i2) {
      if (headerBytes[i2] !== buffer[i2 + offset]) {
        return false;
      }
    }
    return true;
  }
  var init_parse_isobmff_binary = __esm({
    "../images/src/lib/category-api/parse-isobmff-binary.ts"() {
    }
  });

  // ../images/src/lib/category-api/binary-image-api.ts
  function getBinaryImageMetadata(binaryData) {
    const dataView = toDataView(binaryData);
    return getPngMetadata(dataView) || getJpegMetadata(dataView) || getGifMetadata(dataView) || getBmpMetadata(dataView) || getISOBMFFMetadata(dataView);
  }
  function getISOBMFFMetadata(binaryData) {
    const buffer = new Uint8Array(binaryData instanceof DataView ? binaryData.buffer : binaryData);
    const mediaType = getISOBMFFMediaType(buffer);
    if (!mediaType) {
      return null;
    }
    return {
      mimeType: mediaType.mimeType,
      width: 0,
      height: 0
    };
  }
  function getPngMetadata(binaryData) {
    const dataView = toDataView(binaryData);
    const isPng = dataView.byteLength >= 24 && dataView.getUint32(0, BIG_ENDIAN) === 2303741511;
    if (!isPng) {
      return null;
    }
    return {
      mimeType: "image/png",
      width: dataView.getUint32(16, BIG_ENDIAN),
      height: dataView.getUint32(20, BIG_ENDIAN)
    };
  }
  function getGifMetadata(binaryData) {
    const dataView = toDataView(binaryData);
    const isGif = dataView.byteLength >= 10 && dataView.getUint32(0, BIG_ENDIAN) === 1195984440;
    if (!isGif) {
      return null;
    }
    return {
      mimeType: "image/gif",
      width: dataView.getUint16(6, LITTLE_ENDIAN),
      height: dataView.getUint16(8, LITTLE_ENDIAN)
    };
  }
  function getBmpMetadata(binaryData) {
    const dataView = toDataView(binaryData);
    const isBmp = dataView.byteLength >= 14 && dataView.getUint16(0, BIG_ENDIAN) === 16973 && dataView.getUint32(2, LITTLE_ENDIAN) === dataView.byteLength;
    if (!isBmp) {
      return null;
    }
    return {
      mimeType: "image/bmp",
      width: dataView.getUint32(18, LITTLE_ENDIAN),
      height: dataView.getUint32(22, LITTLE_ENDIAN)
    };
  }
  function getJpegMetadata(binaryData) {
    const dataView = toDataView(binaryData);
    const isJpeg = dataView.byteLength >= 3 && dataView.getUint16(0, BIG_ENDIAN) === 65496 && dataView.getUint8(2) === 255;
    if (!isJpeg) {
      return null;
    }
    const { tableMarkers, sofMarkers } = getJpegMarkers();
    let i2 = 2;
    while (i2 + 9 < dataView.byteLength) {
      const marker = dataView.getUint16(i2, BIG_ENDIAN);
      if (sofMarkers.has(marker)) {
        return {
          mimeType: "image/jpeg",
          height: dataView.getUint16(i2 + 5, BIG_ENDIAN),
          width: dataView.getUint16(i2 + 7, BIG_ENDIAN)
        };
      }
      if (!tableMarkers.has(marker)) {
        return null;
      }
      i2 += 2;
      i2 += dataView.getUint16(i2, BIG_ENDIAN);
    }
    return null;
  }
  function getJpegMarkers() {
    const tableMarkers = new Set([65499, 65476, 65484, 65501, 65534]);
    for (let i2 = 65504; i2 < 65520; ++i2) {
      tableMarkers.add(i2);
    }
    const sofMarkers = new Set([
      65472,
      65473,
      65474,
      65475,
      65477,
      65478,
      65479,
      65481,
      65482,
      65483,
      65485,
      65486,
      65487,
      65502
    ]);
    return { tableMarkers, sofMarkers };
  }
  function toDataView(data) {
    if (data instanceof DataView) {
      return data;
    }
    if (ArrayBuffer.isView(data)) {
      return new DataView(data.buffer);
    }
    if (data instanceof ArrayBuffer) {
      return new DataView(data);
    }
    throw new Error("toDataView");
  }
  var BIG_ENDIAN, LITTLE_ENDIAN;
  var init_binary_image_api = __esm({
    "../images/src/lib/category-api/binary-image-api.ts"() {
      init_parse_isobmff_binary();
      BIG_ENDIAN = false;
      LITTLE_ENDIAN = true;
    }
  });

  // ../images/src/lib/parsers/parse-to-node-image.ts
  async function parseToNodeImage(arrayBuffer, options) {
    const { mimeType } = getBinaryImageMetadata(arrayBuffer) || {};
    const _parseImageNode2 = globalThis._parseImageNode;
    assert(_parseImageNode2);
    return await _parseImageNode2(arrayBuffer, mimeType);
  }
  var init_parse_to_node_image = __esm({
    "../images/src/lib/parsers/parse-to-node-image.ts"() {
      init_src2();
      init_binary_image_api();
    }
  });

  // ../images/src/lib/parsers/parse-image.ts
  async function parseImage(arrayBuffer, options, context) {
    options = options || {};
    const imageOptions = options.image || {};
    const imageType = imageOptions.type || "auto";
    const { url } = context || {};
    const loadType = getLoadableImageType(imageType);
    let image;
    switch (loadType) {
      case "imagebitmap":
        image = await parseToImageBitmap(arrayBuffer, options, url);
        break;
      case "image":
        image = await parseToImage(arrayBuffer, options, url);
        break;
      case "data":
        image = await parseToNodeImage(arrayBuffer, options);
        break;
      default:
        assert(false);
    }
    if (imageType === "data") {
      image = getImageData(image);
    }
    return image;
  }
  function getLoadableImageType(type) {
    switch (type) {
      case "auto":
      case "data":
        return getDefaultImageType();
      default:
        isImageTypeSupported(type);
        return type;
    }
  }
  var init_parse_image = __esm({
    "../images/src/lib/parsers/parse-image.ts"() {
      init_src2();
      init_image_type();
      init_parsed_image_api();
      init_parse_to_image();
      init_parse_to_image_bitmap();
      init_parse_to_node_image();
    }
  });

  // ../images/src/image-loader.ts
  var EXTENSIONS, MIME_TYPES, DEFAULT_IMAGE_LOADER_OPTIONS, ImageLoader;
  var init_image_loader = __esm({
    "../images/src/image-loader.ts"() {
      init_version4();
      init_parse_image();
      init_binary_image_api();
      EXTENSIONS = ["png", "jpg", "jpeg", "gif", "webp", "bmp", "ico", "svg", "avif"];
      MIME_TYPES = [
        "image/png",
        "image/jpeg",
        "image/gif",
        "image/webp",
        "image/avif",
        "image/bmp",
        "image/vnd.microsoft.icon",
        "image/svg+xml"
      ];
      DEFAULT_IMAGE_LOADER_OPTIONS = {
        image: {
          type: "auto",
          decode: true
        }
      };
      ImageLoader = {
        id: "image",
        module: "images",
        name: "Images",
        version: VERSION6,
        mimeTypes: MIME_TYPES,
        extensions: EXTENSIONS,
        parse: parseImage,
        tests: [(arrayBuffer) => Boolean(getBinaryImageMetadata(new DataView(arrayBuffer)))],
        options: DEFAULT_IMAGE_LOADER_OPTIONS
      };
    }
  });

  // ../images/src/lib/category-api/image-format.ts
  function isImageFormatSupported(mimeType) {
    if (mimeTypeSupportedSync[mimeType] === void 0) {
      const supported = isBrowser ? checkBrowserImageFormatSupport(mimeType) : checkNodeImageFormatSupport(mimeType);
      mimeTypeSupportedSync[mimeType] = supported;
    }
    return mimeTypeSupportedSync[mimeType];
  }
  function checkNodeImageFormatSupport(mimeType) {
    const NODE_FORMAT_SUPPORT = ["image/png", "image/jpeg", "image/gif"];
    const { _parseImageNode: _parseImageNode2, _imageFormatsNode = NODE_FORMAT_SUPPORT } = globalThis;
    return Boolean(_parseImageNode2) && _imageFormatsNode.includes(mimeType);
  }
  function checkBrowserImageFormatSupport(mimeType) {
    switch (mimeType) {
      case "image/avif":
      case "image/webp":
        return testBrowserImageFormatSupport(mimeType);
      default:
        return true;
    }
  }
  function testBrowserImageFormatSupport(mimeType) {
    try {
      const element = document.createElement("canvas");
      const dataURL = element.toDataURL(mimeType);
      return dataURL.indexOf(`data:${mimeType}`) === 0;
    } catch {
      return false;
    }
  }
  var mimeTypeSupportedSync;
  var init_image_format = __esm({
    "../images/src/lib/category-api/image-format.ts"() {
      init_src2();
      mimeTypeSupportedSync = {};
    }
  });

  // ../images/src/index.ts
  var init_src3 = __esm({
    "../images/src/index.ts"() {
      init_image_loader();
      init_binary_image_api();
      init_image_format();
    }
  });

  // ../textures/src/index.ts
  var init_src4 = __esm({
    "../textures/src/index.ts"() {
      init_basis_loader();
      init_parse_basis();
    }
  });

  // src/lib/utils/assert.ts
  function assert3(condition, message) {
    if (!condition) {
      throw new Error(message || "assert failed: gltf");
    }
  }
  var init_assert3 = __esm({
    "src/lib/utils/assert.ts"() {
    }
  });

  // src/lib/gltf-utils/resolve-url.ts
  function resolveUrl(url, options) {
    const absolute = url.startsWith("data:") || url.startsWith("http:") || url.startsWith("https:");
    if (absolute) {
      return url;
    }
    const baseUrl = options.baseUri || options.uri;
    if (!baseUrl) {
      throw new Error(`'baseUri' must be provided to resolve relative url ${url}`);
    }
    return baseUrl.substr(0, baseUrl.lastIndexOf("/") + 1) + url;
  }
  var init_resolve_url = __esm({
    "src/lib/gltf-utils/resolve-url.ts"() {
    }
  });

  // src/lib/gltf-utils/get-typed-array.ts
  function getTypedArrayForBufferView(json, buffers, bufferViewIndex) {
    const bufferView = json.bufferViews[bufferViewIndex];
    assert3(bufferView);
    const bufferIndex = bufferView.buffer;
    const binChunk = buffers[bufferIndex];
    assert3(binChunk);
    const byteOffset = (bufferView.byteOffset || 0) + binChunk.byteOffset;
    return new Uint8Array(binChunk.arrayBuffer, byteOffset, bufferView.byteLength);
  }
  var init_get_typed_array = __esm({
    "src/lib/gltf-utils/get-typed-array.ts"() {
      init_assert3();
    }
  });

  // src/lib/gltf-utils/gltf-utils.ts
  function getAccessorTypeFromSize(size) {
    const type = TYPES[size - 1];
    return type || TYPES[0];
  }
  function getComponentTypeFromArray(typedArray) {
    const componentType = ARRAY_TO_COMPONENT_TYPE.get(typedArray.constructor);
    if (!componentType) {
      throw new Error("Illegal typed array");
    }
    return componentType;
  }
  function getAccessorArrayTypeAndLength(accessor, bufferView) {
    const ArrayType = ATTRIBUTE_COMPONENT_TYPE_TO_ARRAY[accessor.componentType];
    const components = ATTRIBUTE_TYPE_TO_COMPONENTS[accessor.type];
    const bytesPerComponent = ATTRIBUTE_COMPONENT_TYPE_TO_BYTE_SIZE[accessor.componentType];
    const length = accessor.count * components;
    const byteLength = accessor.count * components * bytesPerComponent;
    assert3(byteLength >= 0 && byteLength <= bufferView.byteLength);
    return { ArrayType, length, byteLength };
  }
  function getMemoryUsageGLTF(gltf) {
    let { images, bufferViews } = gltf;
    images = images || [];
    bufferViews = bufferViews || [];
    const imageBufferViews = images.map((i2) => i2.bufferView);
    bufferViews = bufferViews.filter((view) => !imageBufferViews.includes(view));
    const bufferMemory = bufferViews.reduce((acc, view) => acc + view.byteLength, 0);
    const pixelCount = images.reduce((acc, image) => {
      const { width, height } = image.image;
      return acc + width * height;
    }, 0);
    return bufferMemory + Math.ceil(4 * pixelCount * 1.33);
  }
  var TYPES, ARRAY_CONSTRUCTOR_TO_WEBGL_CONSTANT, ARRAY_TO_COMPONENT_TYPE, ATTRIBUTE_TYPE_TO_COMPONENTS, ATTRIBUTE_COMPONENT_TYPE_TO_BYTE_SIZE, ATTRIBUTE_COMPONENT_TYPE_TO_ARRAY;
  var init_gltf_utils = __esm({
    "src/lib/gltf-utils/gltf-utils.ts"() {
      init_assert3();
      TYPES = ["SCALAR", "VEC2", "VEC3", "VEC4"];
      ARRAY_CONSTRUCTOR_TO_WEBGL_CONSTANT = [
        [Int8Array, 5120],
        [Uint8Array, 5121],
        [Int16Array, 5122],
        [Uint16Array, 5123],
        [Uint32Array, 5125],
        [Float32Array, 5126],
        [Float64Array, 5130]
      ];
      ARRAY_TO_COMPONENT_TYPE = new Map(ARRAY_CONSTRUCTOR_TO_WEBGL_CONSTANT);
      ATTRIBUTE_TYPE_TO_COMPONENTS = {
        SCALAR: 1,
        VEC2: 2,
        VEC3: 3,
        VEC4: 4,
        MAT2: 4,
        MAT3: 9,
        MAT4: 16
      };
      ATTRIBUTE_COMPONENT_TYPE_TO_BYTE_SIZE = {
        5120: 1,
        5121: 1,
        5122: 2,
        5123: 2,
        5125: 4,
        5126: 4
      };
      ATTRIBUTE_COMPONENT_TYPE_TO_ARRAY = {
        5120: Int8Array,
        5121: Uint8Array,
        5122: Int16Array,
        5123: Uint16Array,
        5125: Uint32Array,
        5126: Float32Array
      };
    }
  });

  // src/lib/api/gltf-scenegraph.ts
  var DEFAULT_GLTF_JSON, GLTFScenegraph;
  var init_gltf_scenegraph = __esm({
    "src/lib/api/gltf-scenegraph.ts"() {
      init_src3();
      init_src2();
      init_assert3();
      init_gltf_utils();
      DEFAULT_GLTF_JSON = {
        asset: {
          version: "2.0",
          generator: "loaders.gl"
        },
        buffers: []
      };
      GLTFScenegraph = class {
        constructor(gltf) {
          this.gltf = gltf || {
            json: { ...DEFAULT_GLTF_JSON },
            buffers: []
          };
          this.sourceBuffers = [];
          this.byteLength = 0;
          if (this.gltf.buffers && this.gltf.buffers[0]) {
            this.byteLength = this.gltf.buffers[0].byteLength;
            this.sourceBuffers = [this.gltf.buffers[0]];
          }
        }
        get json() {
          return this.gltf.json;
        }
        getApplicationData(key) {
          const data = this.json[key];
          return data;
        }
        getExtraData(key) {
          const extras = this.json.extras || {};
          return extras[key];
        }
        getExtension(extensionName) {
          const isExtension = this.getUsedExtensions().find((name10) => name10 === extensionName);
          const extensions = this.json.extensions || {};
          return isExtension ? extensions[extensionName] || true : null;
        }
        getRequiredExtension(extensionName) {
          const isRequired = this.getRequiredExtensions().find((name10) => name10 === extensionName);
          return isRequired ? this.getExtension(extensionName) : null;
        }
        getRequiredExtensions() {
          return this.json.extensionsRequired || [];
        }
        getUsedExtensions() {
          return this.json.extensionsUsed || [];
        }
        getRemovedExtensions() {
          return this.json.extensionsRemoved || [];
        }
        getObjectExtension(object, extensionName) {
          const extensions = object.extensions || {};
          return extensions[extensionName];
        }
        getScene(index) {
          return this.getObject("scenes", index);
        }
        getNode(index) {
          return this.getObject("nodes", index);
        }
        getSkin(index) {
          return this.getObject("skins", index);
        }
        getMesh(index) {
          return this.getObject("meshes", index);
        }
        getMaterial(index) {
          return this.getObject("materials", index);
        }
        getAccessor(index) {
          return this.getObject("accessors", index);
        }
        getTexture(index) {
          return this.getObject("textures", index);
        }
        getSampler(index) {
          return this.getObject("samplers", index);
        }
        getImage(index) {
          return this.getObject("images", index);
        }
        getBufferView(index) {
          return this.getObject("bufferViews", index);
        }
        getBuffer(index) {
          return this.getObject("buffers", index);
        }
        getObject(array, index) {
          if (typeof index === "object") {
            return index;
          }
          const object = this.json[array] && this.json[array][index];
          if (!object) {
            throw new Error(`glTF file error: Could not find ${array}[${index}]`);
          }
          return object;
        }
        getTypedArrayForBufferView(bufferView) {
          bufferView = this.getBufferView(bufferView);
          const bufferIndex = bufferView.buffer;
          const binChunk = this.gltf.buffers[bufferIndex];
          assert3(binChunk);
          const byteOffset = (bufferView.byteOffset || 0) + binChunk.byteOffset;
          return new Uint8Array(binChunk.arrayBuffer, byteOffset, bufferView.byteLength);
        }
        getTypedArrayForAccessor(accessor) {
          accessor = this.getAccessor(accessor);
          const bufferView = this.getBufferView(accessor.bufferView);
          const buffer = this.getBuffer(bufferView.buffer);
          const arrayBuffer = buffer.data;
          const { ArrayType, length } = getAccessorArrayTypeAndLength(accessor, bufferView);
          const byteOffset = bufferView.byteOffset + accessor.byteOffset;
          return new ArrayType(arrayBuffer, byteOffset, length);
        }
        getTypedArrayForImageData(image) {
          image = this.getAccessor(image);
          const bufferView = this.getBufferView(image.bufferView);
          const buffer = this.getBuffer(bufferView.buffer);
          const arrayBuffer = buffer.data;
          const byteOffset = bufferView.byteOffset || 0;
          return new Uint8Array(arrayBuffer, byteOffset, bufferView.byteLength);
        }
        addApplicationData(key, data) {
          this.json[key] = data;
          return this;
        }
        addExtraData(key, data) {
          this.json.extras = this.json.extras || {};
          this.json.extras[key] = data;
          return this;
        }
        addObjectExtension(object, extensionName, data) {
          object.extensions = object.extensions || {};
          object.extensions[extensionName] = data;
          this.registerUsedExtension(extensionName);
          return this;
        }
        setObjectExtension(object, extensionName, data) {
          const extensions = object.extensions || {};
          extensions[extensionName] = data;
        }
        removeObjectExtension(object, extensionName) {
          const extensions = object.extensions || {};
          const extension = extensions[extensionName];
          delete extensions[extensionName];
          return extension;
        }
        addExtension(extensionName, extensionData = {}) {
          assert3(extensionData);
          this.json.extensions = this.json.extensions || {};
          this.json.extensions[extensionName] = extensionData;
          this.registerUsedExtension(extensionName);
          return extensionData;
        }
        addRequiredExtension(extensionName, extensionData = {}) {
          assert3(extensionData);
          this.addExtension(extensionName, extensionData);
          this.registerRequiredExtension(extensionName);
          return extensionData;
        }
        registerUsedExtension(extensionName) {
          this.json.extensionsUsed = this.json.extensionsUsed || [];
          if (!this.json.extensionsUsed.find((ext) => ext === extensionName)) {
            this.json.extensionsUsed.push(extensionName);
          }
        }
        registerRequiredExtension(extensionName) {
          this.registerUsedExtension(extensionName);
          this.json.extensionsRequired = this.json.extensionsRequired || [];
          if (!this.json.extensionsRequired.find((ext) => ext === extensionName)) {
            this.json.extensionsRequired.push(extensionName);
          }
        }
        removeExtension(extensionName) {
          if (!this.getExtension(extensionName)) {
            return;
          }
          if (this.json.extensionsRequired) {
            this._removeStringFromArray(this.json.extensionsRequired, extensionName);
          }
          if (this.json.extensionsUsed) {
            this._removeStringFromArray(this.json.extensionsUsed, extensionName);
          }
          if (this.json.extensions) {
            delete this.json.extensions[extensionName];
          }
          if (!Array.isArray(this.json.extensionsRemoved)) {
            this.json.extensionsRemoved = [];
          }
          const extensionsRemoved = this.json.extensionsRemoved;
          if (!extensionsRemoved.includes(extensionName)) {
            extensionsRemoved.push(extensionName);
          }
        }
        setDefaultScene(sceneIndex) {
          this.json.scene = sceneIndex;
        }
        addScene(scene) {
          const { nodeIndices } = scene;
          this.json.scenes = this.json.scenes || [];
          this.json.scenes.push({ nodes: nodeIndices });
          return this.json.scenes.length - 1;
        }
        addNode(node) {
          const { meshIndex, matrix } = node;
          this.json.nodes = this.json.nodes || [];
          const nodeData = { mesh: meshIndex };
          if (matrix) {
            nodeData.matrix = matrix;
          }
          this.json.nodes.push(nodeData);
          return this.json.nodes.length - 1;
        }
        addMesh(mesh) {
          const { attributes, indices, material, mode = 4 } = mesh;
          const accessors = this._addAttributes(attributes);
          const glTFMesh = {
            primitives: [
              {
                attributes: accessors,
                mode
              }
            ]
          };
          if (indices) {
            const indicesAccessor = this._addIndices(indices);
            glTFMesh.primitives[0].indices = indicesAccessor;
          }
          if (Number.isFinite(material)) {
            glTFMesh.primitives[0].material = material;
          }
          this.json.meshes = this.json.meshes || [];
          this.json.meshes.push(glTFMesh);
          return this.json.meshes.length - 1;
        }
        addPointCloud(attributes) {
          const accessorIndices = this._addAttributes(attributes);
          const glTFMesh = {
            primitives: [
              {
                attributes: accessorIndices,
                mode: 0
              }
            ]
          };
          this.json.meshes = this.json.meshes || [];
          this.json.meshes.push(glTFMesh);
          return this.json.meshes.length - 1;
        }
        addImage(imageData, mimeTypeOpt) {
          const metadata = getBinaryImageMetadata(imageData);
          const mimeType = mimeTypeOpt || metadata?.mimeType;
          const bufferViewIndex = this.addBufferView(imageData);
          const glTFImage = {
            bufferView: bufferViewIndex,
            mimeType
          };
          this.json.images = this.json.images || [];
          this.json.images.push(glTFImage);
          return this.json.images.length - 1;
        }
        addBufferView(buffer) {
          const byteLength = buffer.byteLength;
          assert3(Number.isFinite(byteLength));
          this.sourceBuffers = this.sourceBuffers || [];
          this.sourceBuffers.push(buffer);
          const glTFBufferView = {
            buffer: 0,
            byteOffset: this.byteLength,
            byteLength
          };
          this.byteLength += padToNBytes(byteLength, 4);
          this.json.bufferViews = this.json.bufferViews || [];
          this.json.bufferViews.push(glTFBufferView);
          return this.json.bufferViews.length - 1;
        }
        addAccessor(bufferViewIndex, accessor) {
          const glTFAccessor = {
            bufferView: bufferViewIndex,
            type: getAccessorTypeFromSize(accessor.size),
            componentType: accessor.componentType,
            count: accessor.count,
            max: accessor.max,
            min: accessor.min
          };
          this.json.accessors = this.json.accessors || [];
          this.json.accessors.push(glTFAccessor);
          return this.json.accessors.length - 1;
        }
        addBinaryBuffer(sourceBuffer, accessor = { size: 3 }) {
          const bufferViewIndex = this.addBufferView(sourceBuffer);
          let minMax = { min: accessor.min, max: accessor.max };
          if (!minMax.min || !minMax.max) {
            minMax = this._getAccessorMinMax(sourceBuffer, accessor.size);
          }
          const accessorDefaults = {
            size: accessor.size,
            componentType: getComponentTypeFromArray(sourceBuffer),
            count: Math.round(sourceBuffer.length / accessor.size),
            min: minMax.min,
            max: minMax.max
          };
          return this.addAccessor(bufferViewIndex, Object.assign(accessorDefaults, accessor));
        }
        addTexture(texture) {
          const { imageIndex } = texture;
          const glTFTexture = {
            source: imageIndex
          };
          this.json.textures = this.json.textures || [];
          this.json.textures.push(glTFTexture);
          return this.json.textures.length - 1;
        }
        addMaterial(pbrMaterialInfo) {
          this.json.materials = this.json.materials || [];
          this.json.materials.push(pbrMaterialInfo);
          return this.json.materials.length - 1;
        }
        createBinaryChunk() {
          this.gltf.buffers = [];
          const totalByteLength = this.byteLength;
          const arrayBuffer = new ArrayBuffer(totalByteLength);
          const targetArray = new Uint8Array(arrayBuffer);
          let dstByteOffset = 0;
          for (const sourceBuffer of this.sourceBuffers || []) {
            dstByteOffset = copyToArray(sourceBuffer, targetArray, dstByteOffset);
          }
          if (this.json?.buffers?.[0]) {
            this.json.buffers[0].byteLength = totalByteLength;
          } else {
            this.json.buffers = [{ byteLength: totalByteLength }];
          }
          this.gltf.binary = arrayBuffer;
          this.sourceBuffers = [arrayBuffer];
        }
        _removeStringFromArray(array, string) {
          let found = true;
          while (found) {
            const index = array.indexOf(string);
            if (index > -1) {
              array.splice(index, 1);
            } else {
              found = false;
            }
          }
        }
        _addAttributes(attributes = {}) {
          const result = {};
          for (const attributeKey in attributes) {
            const attributeData = attributes[attributeKey];
            const attrName = this._getGltfAttributeName(attributeKey);
            const accessor = this.addBinaryBuffer(attributeData.value, attributeData);
            result[attrName] = accessor;
          }
          return result;
        }
        _addIndices(indices) {
          return this.addBinaryBuffer(indices, { size: 1 });
        }
        _getGltfAttributeName(attributeName) {
          switch (attributeName.toLowerCase()) {
            case "position":
            case "positions":
            case "vertices":
              return "POSITION";
            case "normal":
            case "normals":
              return "NORMAL";
            case "color":
            case "colors":
              return "COLOR_0";
            case "texcoord":
            case "texcoords":
              return "TEXCOORD_0";
            default:
              return attributeName;
          }
        }
        _getAccessorMinMax(buffer, size) {
          const result = { min: null, max: null };
          if (buffer.length < size) {
            return result;
          }
          result.min = [];
          result.max = [];
          const initValues = buffer.subarray(0, size);
          for (const value of initValues) {
            result.min.push(value);
            result.max.push(value);
          }
          for (let index = size; index < buffer.length; index += size) {
            for (let componentIndex = 0; componentIndex < size; componentIndex++) {
              result.min[0 + componentIndex] = Math.min(result.min[0 + componentIndex], buffer[index + componentIndex]);
              result.max[0 + componentIndex] = Math.max(result.max[0 + componentIndex], buffer[index + componentIndex]);
            }
          }
          return result;
        }
      };
    }
  });

  // src/meshopt/meshopt-decoder.ts
  async function meshoptDecodeGltfBuffer(target, count, size, source, mode, filter = "NONE") {
    const instance = await loadWasmInstance();
    decode(instance, instance.exports[DECODERS[mode]], target, count, size, source, instance.exports[FILTERS[filter || "NONE"]]);
  }
  async function loadWasmInstance() {
    if (!wasmPromise) {
      wasmPromise = loadWasmModule();
    }
    return wasmPromise;
  }
  async function loadWasmModule() {
    let wasm = wasm_base;
    if (WebAssembly.validate(detector)) {
      wasm = wasm_simd;
      console.log("Warning: meshopt_decoder is using experimental SIMD support");
    }
    const result = await WebAssembly.instantiate(unpack(wasm), {});
    await result.instance.exports.__wasm_call_ctors();
    return result.instance;
  }
  function unpack(data) {
    const result = new Uint8Array(data.length);
    for (let i2 = 0; i2 < data.length; ++i2) {
      const ch = data.charCodeAt(i2);
      result[i2] = ch > 96 ? ch - 71 : ch > 64 ? ch - 65 : ch > 47 ? ch + 4 : ch > 46 ? 63 : 62;
    }
    let write = 0;
    for (let i2 = 0; i2 < data.length; ++i2) {
      result[write++] = result[i2] < 60 ? wasmpack[result[i2]] : (result[i2] - 60) * 64 + result[++i2];
    }
    return result.buffer.slice(0, write);
  }
  function decode(instance, fun, target, count, size, source, filter) {
    const sbrk = instance.exports.sbrk;
    const count4 = count + 3 & ~3;
    const tp = sbrk(count4 * size);
    const sp = sbrk(source.length);
    const heap = new Uint8Array(instance.exports.memory.buffer);
    heap.set(source, sp);
    const res = fun(tp, count, size, sp, source.length);
    if (res === 0 && filter) {
      filter(tp, count4, size);
    }
    target.set(heap.subarray(tp, tp + count * size));
    sbrk(tp - sbrk(0));
    if (res !== 0) {
      throw new Error(`Malformed buffer data: ${res}`);
    }
  }
  var wasm_base, wasm_simd, detector, wasmpack, FILTERS, DECODERS, wasmPromise;
  var init_meshopt_decoder = __esm({
    "src/meshopt/meshopt-decoder.ts"() {
      wasm_base = "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";
      wasm_simd = "B9h9z9tFBBBF8dL9gBB9gLaaaaaFa9gEaaaB9gGaaB9gFaFaEQSBBFBFFGEGEGIILF9wFFFLEFBFKNFaFCx/aFMO/LFVK9tv9t9vq95GBt9f9f939h9z9t9f9j9h9s9s9f9jW9vq9zBBp9tv9z9o9v9wW9f9kv9j9v9kv9WvqWv94h919m9mvqBG8Z9tv9z9o9v9wW9f9kv9j9v9kv9J9u9kv94h919m9mvqBIy9tv9z9o9v9wW9f9kv9j9v9kv9J9u9kv949TvZ91v9u9jvBLn9tv9z9o9v9wW9f9kv9j9v9kv69p9sWvq9P9jWBKi9tv9z9o9v9wW9f9kv9j9v9kv69p9sWvq9R919hWBNn9tv9z9o9v9wW9f9kv9j9v9kv69p9sWvq9F949wBcI9z9iqlBMc/j9JSIBTEM9+FLa8jUUUUBCTlRBCBRFEXCBRGCBREEXABCNJAGJAECUaAFAGrCFZHIy86BBAEAIJREAGCFJHGCN9HQBMAFCx+YUUBJAE86BBAFCEWCxkUUBJAB8pEN83EBAFCFJHFCUG9HQBMMkRIbaG97FaK978jUUUUBCU/KBlHL8kUUUUBC9+RKGXAGCFJAI9LQBCaRKAE2BBC+gF9HQBALAEAIJHOAGlAG/8cBBCUoBAG9uC/wgBZHKCUGAKCUG9JyRNAECFJRKCBRVGXEXAVAF9PQFANAFAVlAVANJAF9JyRcGXGXAG9FQBAcCbJHIC9wZHMCE9sRSAMCFWRQAICIrCEJCGrRfCBRbEXAKRTCBRtGXEXGXAOATlAf9PQBCBRKSLMALCU/CBJAtAM9sJRmATAfJRKCBREGXAMCoB9JQBAOAKlC/gB9JQBCBRIEXAmAIJREGXGXGXGXGXATAICKrJ2BBHYCEZfIBFGEBMAECBDtDMIBSEMAEAKDBBIAKDBBBHPCID+MFAPDQBTFtGmEYIPLdKeOnHPCGD+MFAPDQBTFtGmEYIPLdKeOnC0+G+MiDtD9OHdCEDbD8jHPD8dBhUg/8/4/w/goB9+h84k7HeCEWCxkUUBJDBEBAeCx+YUUBJDBBBHnAnDQBBBBBBBBBBBBBBBBAPD8dFhUg/8/4/w/goB9+h84k7HeCEWCxkUUBJDBEBD9uDQBFGEILKOTtmYPdenDfAdAPD9SDMIBAKCIJAnDeBJAeCx+YUUBJ2BBJRKSGMAEAKDBBNAKDBBBHPCID+MFAPDQBTFtGmEYIPLdKeOnC+P+e+8/4BDtD9OHdCbDbD8jHPD8dBhUg/8/4/w/goB9+h84k7HeCEWCxkUUBJDBEBAeCx+YUUBJDBBBHnAnDQBBBBBBBBBBBBBBBBAPD8dFhUg/8/4/w/goB9+h84k7HeCEWCxkUUBJDBEBD9uDQBFGEILKOTtmYPdenDfAdAPD9SDMIBAKCNJAnDeBJAeCx+YUUBJ2BBJRKSFMAEAKDBBBDMIBAKCTJRKMGXGXGXGXGXAYCGrCEZfIBFGEBMAECBDtDMITSEMAEAKDBBIAKDBBBHPCID+MFAPDQBTFtGmEYIPLdKeOnHPCGD+MFAPDQBTFtGmEYIPLdKeOnC0+G+MiDtD9OHdCEDbD8jHPD8dBhUg/8/4/w/goB9+h84k7HeCEWCxkUUBJDBEBAeCx+YUUBJDBBBHnAnDQBBBBBBBBBBBBBBBBAPD8dFhUg/8/4/w/goB9+h84k7HeCEWCxkUUBJDBEBD9uDQBFGEILKOTtmYPdenDfAdAPD9SDMITAKCIJAnDeBJAeCx+YUUBJ2BBJRKSGMAEAKDBBNAKDBBBHPCID+MFAPDQBTFtGmEYIPLdKeOnC+P+e+8/4BDtD9OHdCbDbD8jHPD8dBhUg/8/4/w/goB9+h84k7HeCEWCxkUUBJDBEBAeCx+YUUBJDBBBHnAnDQBBBBBBBBBBBBBBBBAPD8dFhUg/8/4/w/goB9+h84k7HeCEWCxkUUBJDBEBD9uDQBFGEILKOTtmYPdenDfAdAPD9SDMITAKCNJAnDeBJAeCx+YUUBJ2BBJRKSFMAEAKDBBBDMITAKCTJRKMGXGXGXGXGXAYCIrCEZfIBFGEBMAECBDtDMIASEMAEAKDBBIAKDBBBHPCID+MFAPDQBTFtGmEYIPLdKeOnHPCGD+MFAPDQBTFtGmEYIPLdKeOnC0+G+MiDtD9OHdCEDbD8jHPD8dBhUg/8/4/w/goB9+h84k7HeCEWCxkUUBJDBEBAeCx+YUUBJDBBBHnAnDQBBBBBBBBBBBBBBBBAPD8dFhUg/8/4/w/goB9+h84k7HeCEWCxkUUBJDBEBD9uDQBFGEILKOTtmYPdenDfAdAPD9SDMIAAKCIJAnDeBJAeCx+YUUBJ2BBJRKSGMAEAKDBBNAKDBBBHPCID+MFAPDQBTFtGmEYIPLdKeOnC+P+e+8/4BDtD9OHdCbDbD8jHPD8dBhUg/8/4/w/goB9+h84k7HeCEWCxkUUBJDBEBAeCx+YUUBJDBBBHnAnDQBBBBBBBBBBBBBBBBAPD8dFhUg/8/4/w/goB9+h84k7HeCEWCxkUUBJDBEBD9uDQBFGEILKOTtmYPdenDfAdAPD9SDMIAAKCNJAnDeBJAeCx+YUUBJ2BBJRKSFMAEAKDBBBDMIAAKCTJRKMGXGXGXGXGXAYCKrfIBFGEBMAECBDtDMI8wSEMAEAKDBBIAKDBBBHPCID+MFAPDQBTFtGmEYIPLdKeOnHPCGD+MFAPDQBTFtGmEYIPLdKeOnC0+G+MiDtD9OHdCEDbD8jHPD8dBhUg/8/4/w/goB9+h84k7HYCEWCxkUUBJDBEBAYCx+YUUBJDBBBHnAnDQBBBBBBBBBBBBBBBBAPD8dFhUg/8/4/w/goB9+h84k7HYCEWCxkUUBJDBEBD9uDQBFGEILKOTtmYPdenDfAdAPD9SDMI8wAKCIJAnDeBJAYCx+YUUBJ2BBJRKSGMAEAKDBBNAKDBBBHPCID+MFAPDQBTFtGmEYIPLdKeOnC+P+e+8/4BDtD9OHdCbDbD8jHPD8dBhUg/8/4/w/goB9+h84k7HYCEWCxkUUBJDBEBAYCx+YUUBJDBBBHnAnDQBBBBBBBBBBBBBBBBAPD8dFhUg/8/4/w/goB9+h84k7HYCEWCxkUUBJDBEBD9uDQBFGEILKOTtmYPdenDfAdAPD9SDMI8wAKCNJAnDeBJAYCx+YUUBJ2BBJRKSFMAEAKDBBBDMI8wAKCTJRKMAICoBJREAICUFJAM9LQFAERIAOAKlC/fB9LQBMMGXAEAM9PQBAECErRIEXGXAOAKlCi9PQBCBRKSOMAmAEJRYGXGXGXGXGXATAECKrJ2BBAICKZrCEZfIBFGEBMAYCBDtDMIBSEMAYAKDBBIAKDBBBHPCID+MFAPDQBTFtGmEYIPLdKeOnHPCGD+MFAPDQBTFtGmEYIPLdKeOnC0+G+MiDtD9OHdCEDbD8jHPD8dBhUg/8/4/w/goB9+h84k7HeCEWCxkUUBJDBEBAeCx+YUUBJDBBBHnAnDQBBBBBBBBBBBBBBBBAPD8dFhUg/8/4/w/goB9+h84k7HeCEWCxkUUBJDBEBD9uDQBFGEILKOTtmYPdenDfAdAPD9SDMIBAKCIJAnDeBJAeCx+YUUBJ2BBJRKSGMAYAKDBBNAKDBBBHPCID+MFAPDQBTFtGmEYIPLdKeOnC+P+e+8/4BDtD9OHdCbDbD8jHPD8dBhUg/8/4/w/goB9+h84k7HeCEWCxkUUBJDBEBAeCx+YUUBJDBBBHnAnDQBBBBBBBBBBBBBBBBAPD8dFhUg/8/4/w/goB9+h84k7HeCEWCxkUUBJDBEBD9uDQBFGEILKOTtmYPdenDfAdAPD9SDMIBAKCNJAnDeBJAeCx+YUUBJ2BBJRKSFMAYAKDBBBDMIBAKCTJRKMAICGJRIAECTJHEAM9JQBMMGXAK9FQBAKRTAtCFJHtCI6QGSFMMCBRKSEMGXAM9FQBALCUGJAbJREALAbJDBGBRnCBRYEXAEALCU/CBJAYJHIDBIBHdCFD9tAdCFDbHPD9OD9hD9RHdAIAMJDBIBHiCFD9tAiAPD9OD9hD9RHiDQBTFtGmEYIPLdKeOnH8ZAIAQJDBIBHpCFD9tApAPD9OD9hD9RHpAIASJDBIBHyCFD9tAyAPD9OD9hD9RHyDQBTFtGmEYIPLdKeOnH8cDQBFTtGEmYILPdKOenHPAPDQBFGEBFGEBFGEBFGEAnD9uHnDyBjGBAEAGJHIAnAPAPDQILKOILKOILKOILKOD9uHnDyBjGBAIAGJHIAnAPAPDQNVcMNVcMNVcMNVcMD9uHnDyBjGBAIAGJHIAnAPAPDQSQfbSQfbSQfbSQfbD9uHnDyBjGBAIAGJHIAnA8ZA8cDQNVi8ZcMpySQ8c8dfb8e8fHPAPDQBFGEBFGEBFGEBFGED9uHnDyBjGBAIAGJHIAnAPAPDQILKOILKOILKOILKOD9uHnDyBjGBAIAGJHIAnAPAPDQNVcMNVcMNVcMNVcMD9uHnDyBjGBAIAGJHIAnAPAPDQSQfbSQfbSQfbSQfbD9uHnDyBjGBAIAGJHIAnAdAiDQNiV8ZcpMyS8cQ8df8eb8fHdApAyDQNiV8ZcpMyS8cQ8df8eb8fHiDQBFTtGEmYILPdKOenHPAPDQBFGEBFGEBFGEBFGED9uHnDyBjGBAIAGJHIAnAPAPDQILKOILKOILKOILKOD9uHnDyBjGBAIAGJHIAnAPAPDQNVcMNVcMNVcMNVcMD9uHnDyBjGBAIAGJHIAnAPAPDQSQfbSQfbSQfbSQfbD9uHnDyBjGBAIAGJHIAnAdAiDQNVi8ZcMpySQ8c8dfb8e8fHPAPDQBFGEBFGEBFGEBFGED9uHnDyBjGBAIAGJHIAnAPAPDQILKOILKOILKOILKOD9uHnDyBjGBAIAGJHIAnAPAPDQNVcMNVcMNVcMNVcMD9uHnDyBjGBAIAGJHIAnAPAPDQSQfbSQfbSQfbSQfbD9uHnDyBjGBAIAGJREAYCTJHYAM9JQBMMAbCIJHbAG9JQBMMABAVAG9sJALCUGJAcAG9s/8cBBALALCUGJAcCaJAG9sJAG/8cBBMAcCBAKyAVJRVAKQBMC9+RKSFMCBC99AOAKlAGCAAGCA9Ly6yRKMALCU/KBJ8kUUUUBAKMNBT+BUUUBM+KmFTa8jUUUUBCoFlHL8kUUUUBC9+RKGXAFCE9uHOCtJAI9LQBCaRKAE2BBHNC/wFZC/gF9HQBANCbZHVCF9LQBALCoBJCgFCUF/8MBALC84Jha83EBALC8wJha83EBALC8oJha83EBALCAJha83EBALCiJha83EBALCTJha83EBALha83ENALha83EBAEAIJC9wJRcAECFJHNAOJRMGXAF9FQBCQCbAVCF6yRSABRECBRVCBRQCBRfCBRICBRKEXGXAMAcuQBC9+RKSEMGXGXAN2BBHOC/vF9LQBALCoBJAOCIrCa9zAKJCbZCEWJHb8oGIRTAb8oGBRtGXAOCbZHbAS9PQBALAOCa9zAIJCbZCGWJ8oGBAVAbyROAb9FRbGXGXAGCG9HQBABAt87FBABCIJAO87FBABCGJAT87FBSFMAEAtjGBAECNJAOjGBAECIJATjGBMAVAbJRVALCoBJAKCEWJHmAOjGBAmATjGIALAICGWJAOjGBALCoBJAKCFJCbZHKCEWJHTAtjGBATAOjGIAIAbJRIAKCFJRKSGMGXGXAbCb6QBAQAbJAbC989zJCFJRQSFMAM1BBHbCgFZROGXGXAbCa9MQBAMCFJRMSFMAM1BFHbCgBZCOWAOCgBZqROGXAbCa9MQBAMCGJRMSFMAM1BGHbCgBZCfWAOqROGXAbCa9MQBAMCEJRMSFMAM1BEHbCgBZCdWAOqROGXAbCa9MQBAMCIJRMSFMAM2BIC8cWAOqROAMCLJRMMAOCFrCBAOCFZl9zAQJRQMGXGXAGCG9HQBABAt87FBABCIJAQ87FBABCGJAT87FBSFMAEAtjGBAECNJAQjGBAECIJATjGBMALCoBJAKCEWJHOAQjGBAOATjGIALAICGWJAQjGBALCoBJAKCFJCbZHKCEWJHOAtjGBAOAQjGIAICFJRIAKCFJRKSFMGXAOCDF9LQBALAIAcAOCbZJ2BBHbCIrHTlCbZCGWJ8oGBAVCFJHtATyROALAIAblCbZCGWJ8oGBAtAT9FHmJHtAbCbZHTyRbAT9FRTGXGXAGCG9HQBABAV87FBABCIJAb87FBABCGJAO87FBSFMAEAVjGBAECNJAbjGBAECIJAOjGBMALAICGWJAVjGBALCoBJAKCEWJHYAOjGBAYAVjGIALAICFJHICbZCGWJAOjGBALCoBJAKCFJCbZCEWJHYAbjGBAYAOjGIALAIAmJCbZHICGWJAbjGBALCoBJAKCGJCbZHKCEWJHOAVjGBAOAbjGIAKCFJRKAIATJRIAtATJRVSFMAVCBAM2BBHYyHTAOC/+F6HPJROAYCbZRtGXGXAYCIrHmQBAOCFJRbSFMAORbALAIAmlCbZCGWJ8oGBROMGXGXAtQBAbCFJRVSFMAbRVALAIAYlCbZCGWJ8oGBRbMGXGXAP9FQBAMCFJRYSFMAM1BFHYCgFZRTGXGXAYCa9MQBAMCGJRYSFMAM1BGHYCgBZCOWATCgBZqRTGXAYCa9MQBAMCEJRYSFMAM1BEHYCgBZCfWATqRTGXAYCa9MQBAMCIJRYSFMAM1BIHYCgBZCdWATqRTGXAYCa9MQBAMCLJRYSFMAMCKJRYAM2BLC8cWATqRTMATCFrCBATCFZl9zAQJHQRTMGXGXAmCb6QBAYRPSFMAY1BBHMCgFZROGXGXAMCa9MQBAYCFJRPSFMAY1BFHMCgBZCOWAOCgBZqROGXAMCa9MQBAYCGJRPSFMAY1BGHMCgBZCfWAOqROGXAMCa9MQBAYCEJRPSFMAY1BEHMCgBZCdWAOqROGXAMCa9MQBAYCIJRPSFMAYCLJRPAY2BIC8cWAOqROMAOCFrCBAOCFZl9zAQJHQROMGXGXAtCb6QBAPRMSFMAP1BBHMCgFZRbGXGXAMCa9MQBAPCFJRMSFMAP1BFHMCgBZCOWAbCgBZqRbGXAMCa9MQBAPCGJRMSFMAP1BGHMCgBZCfWAbqRbGXAMCa9MQBAPCEJRMSFMAP1BEHMCgBZCdWAbqRbGXAMCa9MQBAPCIJRMSFMAPCLJRMAP2BIC8cWAbqRbMAbCFrCBAbCFZl9zAQJHQRbMGXGXAGCG9HQBABAT87FBABCIJAb87FBABCGJAO87FBSFMAEATjGBAECNJAbjGBAECIJAOjGBMALCoBJAKCEWJHYAOjGBAYATjGIALAICGWJATjGBALCoBJAKCFJCbZCEWJHYAbjGBAYAOjGIALAICFJHICbZCGWJAOjGBALCoBJAKCGJCbZCEWJHOATjGBAOAbjGIALAIAm9FAmCb6qJHICbZCGWJAbjGBAIAt9FAtCb6qJRIAKCEJRKMANCFJRNABCKJRBAECSJREAKCbZRKAICbZRIAfCEJHfAF9JQBMMCBC99AMAc6yRKMALCoFJ8kUUUUBAKM/tIFGa8jUUUUBCTlRLC9+RKGXAFCLJAI9LQBCaRKAE2BBC/+FZC/QF9HQBALhB83ENAECFJRKAEAIJC98JREGXAF9FQBGXAGCG6QBEXGXAKAE9JQBC9+bMAK1BBHGCgFZRIGXGXAGCa9MQBAKCFJRKSFMAK1BFHGCgBZCOWAICgBZqRIGXAGCa9MQBAKCGJRKSFMAK1BGHGCgBZCfWAIqRIGXAGCa9MQBAKCEJRKSFMAK1BEHGCgBZCdWAIqRIGXAGCa9MQBAKCIJRKSFMAK2BIC8cWAIqRIAKCLJRKMALCNJAICFZCGWqHGAICGrCBAICFrCFZl9zAG8oGBJHIjGBABAIjGBABCIJRBAFCaJHFQBSGMMEXGXAKAE9JQBC9+bMAK1BBHGCgFZRIGXGXAGCa9MQBAKCFJRKSFMAK1BFHGCgBZCOWAICgBZqRIGXAGCa9MQBAKCGJRKSFMAK1BGHGCgBZCfWAIqRIGXAGCa9MQBAKCEJRKSFMAK1BEHGCgBZCdWAIqRIGXAGCa9MQBAKCIJRKSFMAK2BIC8cWAIqRIAKCLJRKMABAICGrCBAICFrCFZl9zALCNJAICFZCGWqHI8oGBJHG87FBAIAGjGBABCGJRBAFCaJHFQBMMCBC99AKAE6yRKMAKM/xLGEaK978jUUUUBCAlHE8kUUUUBGXGXAGCI9HQBGXAFC98ZHI9FQBABRGCBRLEXAGAGDBBBHKCiD+rFCiD+sFD/6FHOAKCND+rFCiD+sFD/6FAOD/gFAKCTD+rFCiD+sFD/6FHND/gFD/kFD/lFHVCBDtD+2FHcAOCUUUU94DtHMD9OD9RD/kFHO9DBB/+hDYAOAOD/mFAVAVD/mFANAcANAMD9OD9RD/kFHOAOD/mFD/kFD/kFD/jFD/nFHND/mF9DBBX9LDYHcD/kFCgFDtD9OAKCUUU94DtD9OD9QAOAND/mFAcD/kFCND+rFCU/+EDtD9OD9QAVAND/mFAcD/kFCTD+rFCUU/8ODtD9OD9QDMBBAGCTJRGALCIJHLAI9JQBMMAIAF9PQFAEAFCEZHLCGWHGqCBCTAGl/8MBAEABAICGWJHIAG/8cBBGXAL9FQBAEAEDBIBHKCiD+rFCiD+sFD/6FHOAKCND+rFCiD+sFD/6FAOD/gFAKCTD+rFCiD+sFD/6FHND/gFD/kFD/lFHVCBDtD+2FHcAOCUUUU94DtHMD9OD9RD/kFHO9DBB/+hDYAOAOD/mFAVAVD/mFANAcANAMD9OD9RD/kFHOAOD/mFD/kFD/kFD/jFD/nFHND/mF9DBBX9LDYHcD/kFCgFDtD9OAKCUUU94DtD9OD9QAOAND/mFAcD/kFCND+rFCU/+EDtD9OD9QAVAND/mFAcD/kFCTD+rFCUU/8ODtD9OD9QDMIBMAIAEAG/8cBBSFMABAFC98ZHGT+HUUUBAGAF9PQBAEAFCEZHICEWHLJCBCAALl/8MBAEABAGCEWJHGAL/8cBBAEAIT+HUUUBAGAEAL/8cBBMAECAJ8kUUUUBM+yEGGaO97GXAF9FQBCBRGEXABCTJHEAEDBBBHICBDtHLCUU98D8cFCUU98D8cEHKD9OABDBBBHOAIDQILKOSQfbPden8c8d8e8fCggFDtD9OD/6FAOAIDQBFGENVcMTtmYi8ZpyHICTD+sFD/6FHND/gFAICTD+rFCTD+sFD/6FHVD/gFD/kFD/lFHI9DB/+g6DYAVAIALD+2FHLAVCUUUU94DtHcD9OD9RD/kFHVAVD/mFAIAID/mFANALANAcD9OD9RD/kFHIAID/mFD/kFD/kFD/jFD/nFHND/mF9DBBX9LDYHLD/kFCTD+rFAVAND/mFALD/kFCggEDtD9OD9QHVAIAND/mFALD/kFCaDbCBDnGCBDnECBDnKCBDnOCBDncCBDnMCBDnfCBDnbD9OHIDQNVi8ZcMpySQ8c8dfb8e8fD9QDMBBABAOAKD9OAVAIDQBFTtGEmYILPdKOenD9QDMBBABCAJRBAGCIJHGAF9JQBMMM94FEa8jUUUUBCAlHE8kUUUUBABAFC98ZHIT+JUUUBGXAIAF9PQBAEAFCEZHLCEWHFJCBCAAFl/8MBAEABAICEWJHBAF/8cBBAEALT+JUUUBABAEAF/8cBBMAECAJ8kUUUUBM/hEIGaF97FaL978jUUUUBCTlRGGXAF9FQBCBREEXAGABDBBBHIABCTJHLDBBBHKDQILKOSQfbPden8c8d8e8fHOCTD+sFHNCID+rFDMIBAB9DBBU8/DY9D/zI818/DYANCEDtD9QD/6FD/nFHNAIAKDQBFGENVcMTtmYi8ZpyHICTD+rFCTD+sFD/6FD/mFHKAKD/mFANAICTD+sFD/6FD/mFHVAVD/mFANAOCTD+rFCTD+sFD/6FD/mFHOAOD/mFD/kFD/kFD/lFCBDtD+4FD/jF9DB/+g6DYHND/mF9DBBX9LDYHID/kFCggEDtHcD9OAVAND/mFAID/kFCTD+rFD9QHVAOAND/mFAID/kFCTD+rFAKAND/mFAID/kFAcD9OD9QHNDQBFTtGEmYILPdKOenHID8dBAGDBIBDyB+t+J83EBABCNJAID8dFAGDBIBDyF+t+J83EBALAVANDQNVi8ZcMpySQ8c8dfb8e8fHND8dBAGDBIBDyG+t+J83EBABCiJAND8dFAGDBIBDyE+t+J83EBABCAJRBAECIJHEAF9JQBMMM/3FGEaF978jUUUUBCoBlREGXAGCGrAF9sHIC98ZHL9FQBCBRGABRFEXAFAFDBBBHKCND+rFCND+sFD/6FAKCiD+sFCnD+rFCUUU/8EDtD+uFD/mFDMBBAFCTJRFAGCIJHGAL9JQBMMGXALAI9PQBAEAICEZHGCGWHFqCBCoBAFl/8MBAEABALCGWJHLAF/8cBBGXAG9FQBAEAEDBIBHKCND+rFCND+sFD/6FAKCiD+sFCnD+rFCUUU/8EDtD+uFD/mFDMIBMALAEAF/8cBBMM9TFEaCBCB8oGUkUUBHFABCEJC98ZJHBjGUkUUBGXGXAB8/BCTWHGuQBCaREABAGlCggEJCTrXBCa6QFMAFREMAEMMMFBCUNMIT9tBB";
      detector = new Uint8Array([
        0,
        97,
        115,
        109,
        1,
        0,
        0,
        0,
        1,
        4,
        1,
        96,
        0,
        0,
        3,
        3,
        2,
        0,
        0,
        5,
        3,
        1,
        0,
        1,
        12,
        1,
        0,
        10,
        22,
        2,
        12,
        0,
        65,
        0,
        65,
        0,
        65,
        0,
        252,
        10,
        0,
        0,
        11,
        7,
        0,
        65,
        0,
        253,
        15,
        26,
        11
      ]);
      wasmpack = new Uint8Array([
        32,
        0,
        65,
        253,
        3,
        1,
        2,
        34,
        4,
        106,
        6,
        5,
        11,
        8,
        7,
        20,
        13,
        33,
        12,
        16,
        128,
        9,
        116,
        64,
        19,
        113,
        127,
        15,
        10,
        21,
        22,
        14,
        255,
        66,
        24,
        54,
        136,
        107,
        18,
        23,
        192,
        26,
        114,
        118,
        132,
        17,
        77,
        101,
        130,
        144,
        27,
        87,
        131,
        44,
        45,
        74,
        156,
        154,
        70,
        167
      ]);
      FILTERS = {
        0: "",
        1: "meshopt_decodeFilterOct",
        2: "meshopt_decodeFilterQuat",
        3: "meshopt_decodeFilterExp",
        NONE: "",
        OCTAHEDRAL: "meshopt_decodeFilterOct",
        QUATERNION: "meshopt_decodeFilterQuat",
        EXPONENTIAL: "meshopt_decodeFilterExp"
      };
      DECODERS = {
        0: "meshopt_decodeVertexBuffer",
        1: "meshopt_decodeIndexBuffer",
        2: "meshopt_decodeIndexSequence",
        ATTRIBUTES: "meshopt_decodeVertexBuffer",
        TRIANGLES: "meshopt_decodeIndexBuffer",
        INDICES: "meshopt_decodeIndexSequence"
      };
    }
  });

  // src/lib/extensions/EXT_meshopt_compression.ts
  var EXT_meshopt_compression_exports = {};
  __export(EXT_meshopt_compression_exports, {
    decode: () => decode2,
    name: () => name
  });
  async function decode2(gltfData, options) {
    const scenegraph = new GLTFScenegraph(gltfData);
    if (!options?.gltf?.decompressMeshes) {
      return;
    }
    const promises = [];
    for (const bufferViewIndex of gltfData.json.bufferViews || []) {
      promises.push(decodeMeshoptBufferView(scenegraph, bufferViewIndex));
    }
    await Promise.all(promises);
    scenegraph.removeExtension(EXT_MESHOPT_COMPRESSION);
  }
  async function decodeMeshoptBufferView(scenegraph, bufferView) {
    const meshoptExtension = scenegraph.getObjectExtension(bufferView, EXT_MESHOPT_COMPRESSION);
    if (meshoptExtension) {
      const {
        byteOffset = 0,
        byteLength = 0,
        byteStride,
        count,
        mode,
        filter = "NONE",
        buffer: bufferIndex
      } = meshoptExtension;
      const buffer = scenegraph.gltf.buffers[bufferIndex];
      const source = new Uint8Array(buffer.arrayBuffer, buffer.byteOffset + byteOffset, byteLength);
      const result = new Uint8Array(scenegraph.gltf.buffers[bufferView.buffer].arrayBuffer, bufferView.byteOffset, bufferView.byteLength);
      await meshoptDecodeGltfBuffer(result, count, byteStride, source, mode, filter);
      return result;
    }
    return null;
  }
  var EXT_MESHOPT_COMPRESSION, name;
  var init_EXT_meshopt_compression = __esm({
    "src/lib/extensions/EXT_meshopt_compression.ts"() {
      init_gltf_scenegraph();
      init_meshopt_decoder();
      EXT_MESHOPT_COMPRESSION = "EXT_meshopt_compression";
      name = EXT_MESHOPT_COMPRESSION;
    }
  });

  // src/lib/extensions/EXT_texture_webp.ts
  var EXT_texture_webp_exports = {};
  __export(EXT_texture_webp_exports, {
    name: () => name2,
    preprocess: () => preprocess
  });
  function preprocess(gltfData, options) {
    const scenegraph = new GLTFScenegraph(gltfData);
    if (!isImageFormatSupported("image/webp")) {
      if (scenegraph.getRequiredExtensions().includes(EXT_TEXTURE_WEBP)) {
        throw new Error(`gltf: Required extension ${EXT_TEXTURE_WEBP} not supported by browser`);
      }
      return;
    }
    const { json } = scenegraph;
    for (const texture of json.textures || []) {
      const extension = scenegraph.getObjectExtension(texture, EXT_TEXTURE_WEBP);
      if (extension) {
        texture.source = extension.source;
      }
      scenegraph.removeObjectExtension(texture, EXT_TEXTURE_WEBP);
    }
    scenegraph.removeExtension(EXT_TEXTURE_WEBP);
  }
  var EXT_TEXTURE_WEBP, name2;
  var init_EXT_texture_webp = __esm({
    "src/lib/extensions/EXT_texture_webp.ts"() {
      init_src3();
      init_gltf_scenegraph();
      EXT_TEXTURE_WEBP = "EXT_texture_webp";
      name2 = EXT_TEXTURE_WEBP;
    }
  });

  // src/lib/extensions/KHR_texture_basisu.ts
  var KHR_texture_basisu_exports = {};
  __export(KHR_texture_basisu_exports, {
    name: () => name3,
    preprocess: () => preprocess2
  });
  function preprocess2(gltfData, options) {
    const scene = new GLTFScenegraph(gltfData);
    const { json } = scene;
    for (const texture of json.textures || []) {
      const extension = scene.getObjectExtension(texture, KHR_TEXTURE_BASISU);
      if (extension) {
        texture.source = extension.source;
      }
      scene.removeObjectExtension(texture, KHR_TEXTURE_BASISU);
    }
    scene.removeExtension(KHR_TEXTURE_BASISU);
  }
  var KHR_TEXTURE_BASISU, name3;
  var init_KHR_texture_basisu = __esm({
    "src/lib/extensions/KHR_texture_basisu.ts"() {
      init_gltf_scenegraph();
      KHR_TEXTURE_BASISU = "KHR_texture_basisu";
      name3 = KHR_TEXTURE_BASISU;
    }
  });

  // ../draco/src/lib/utils/version.ts
  var VERSION7;
  var init_version5 = __esm({
    "../draco/src/lib/utils/version.ts"() {
      VERSION7 = typeof __VERSION__ !== "undefined" ? __VERSION__ : "latest";
    }
  });

  // ../draco/src/draco-loader.ts
  var DEFAULT_DRACO_OPTIONS, DracoLoader;
  var init_draco_loader = __esm({
    "../draco/src/draco-loader.ts"() {
      init_src();
      init_version5();
      DEFAULT_DRACO_OPTIONS = {
        draco: {
          decoderType: typeof WebAssembly === "object" ? "wasm" : "js",
          libraryPath: "libs/",
          extraAttributes: {},
          attributeNameEntry: void 0
        }
      };
      DracoLoader = {
        name: "Draco",
        id: isBrowser2 ? "draco" : "draco-nodejs",
        module: "draco",
        shapes: ["mesh"],
        version: VERSION7,
        worker: true,
        extensions: ["drc"],
        mimeTypes: ["application/octet-stream"],
        binary: true,
        tests: ["DRACO"],
        options: DEFAULT_DRACO_OPTIONS
      };
    }
  });

  // ../schema/src/category/mesh/mesh-utils.ts
  function getMeshBoundingBox(attributes) {
    let minX = Infinity;
    let minY = Infinity;
    let minZ = Infinity;
    let maxX = -Infinity;
    let maxY = -Infinity;
    let maxZ = -Infinity;
    const positions = attributes.POSITION ? attributes.POSITION.value : [];
    const len = positions && positions.length;
    for (let i2 = 0; i2 < len; i2 += 3) {
      const x = positions[i2];
      const y = positions[i2 + 1];
      const z = positions[i2 + 2];
      minX = x < minX ? x : minX;
      minY = y < minY ? y : minY;
      minZ = z < minZ ? z : minZ;
      maxX = x > maxX ? x : maxX;
      maxY = y > maxY ? y : maxY;
      maxZ = z > maxZ ? z : maxZ;
    }
    return [
      [minX, minY, minZ],
      [maxX, maxY, maxZ]
    ];
  }
  var init_mesh_utils = __esm({
    "../schema/src/category/mesh/mesh-utils.ts"() {
    }
  });

  // ../schema/src/lib/utils/assert.ts
  function assert4(condition, message) {
    if (!condition) {
      throw new Error(message || "loader assertion failed.");
    }
  }
  var init_assert4 = __esm({
    "../schema/src/lib/utils/assert.ts"() {
    }
  });

  // ../schema/src/lib/schema/impl/schema.ts
  function checkNames(fields) {
    const usedNames = {};
    for (const field of fields) {
      if (usedNames[field.name]) {
        console.warn("Schema: duplicated field name", field.name, field);
      }
      usedNames[field.name] = true;
    }
  }
  function mergeMaps(m1, m2) {
    return new Map([...m1 || new Map(), ...m2 || new Map()]);
  }
  var Schema;
  var init_schema = __esm({
    "../schema/src/lib/schema/impl/schema.ts"() {
      init_assert4();
      Schema = class {
        constructor(fields, metadata) {
          assert4(Array.isArray(fields));
          checkNames(fields);
          this.fields = fields;
          this.metadata = metadata || new Map();
        }
        compareTo(other) {
          if (this.metadata !== other.metadata) {
            return false;
          }
          if (this.fields.length !== other.fields.length) {
            return false;
          }
          for (let i2 = 0; i2 < this.fields.length; ++i2) {
            if (!this.fields[i2].compareTo(other.fields[i2])) {
              return false;
            }
          }
          return true;
        }
        select(...columnNames) {
          const nameMap = Object.create(null);
          for (const name10 of columnNames) {
            nameMap[name10] = true;
          }
          const selectedFields = this.fields.filter((field) => nameMap[field.name]);
          return new Schema(selectedFields, this.metadata);
        }
        selectAt(...columnIndices) {
          const selectedFields = columnIndices.map((index) => this.fields[index]).filter(Boolean);
          return new Schema(selectedFields, this.metadata);
        }
        assign(schemaOrFields) {
          let fields;
          let metadata = this.metadata;
          if (schemaOrFields instanceof Schema) {
            const otherSchema = schemaOrFields;
            fields = otherSchema.fields;
            metadata = mergeMaps(mergeMaps(new Map(), this.metadata), otherSchema.metadata);
          } else {
            fields = schemaOrFields;
          }
          const fieldMap = Object.create(null);
          for (const field of this.fields) {
            fieldMap[field.name] = field;
          }
          for (const field of fields) {
            fieldMap[field.name] = field;
          }
          const mergedFields = Object.values(fieldMap);
          return new Schema(mergedFields, metadata);
        }
      };
    }
  });

  // ../schema/src/lib/schema/impl/field.ts
  var Field;
  var init_field = __esm({
    "../schema/src/lib/schema/impl/field.ts"() {
      Field = class {
        constructor(name10, type, nullable = false, metadata = new Map()) {
          this.name = name10;
          this.type = type;
          this.nullable = nullable;
          this.metadata = metadata;
        }
        get typeId() {
          return this.type && this.type.typeId;
        }
        clone() {
          return new Field(this.name, this.type, this.nullable, this.metadata);
        }
        compareTo(other) {
          return this.name === other.name && this.type === other.type && this.nullable === other.nullable && this.metadata === other.metadata;
        }
        toString() {
          return `${this.type}${this.nullable ? ", nullable" : ""}${this.metadata ? `, metadata: ${this.metadata}` : ""}`;
        }
      };
    }
  });

  // ../schema/src/lib/schema/impl/enum.ts
  var Type;
  var init_enum = __esm({
    "../schema/src/lib/schema/impl/enum.ts"() {
      (function(Type2) {
        Type2[Type2["NONE"] = 0] = "NONE";
        Type2[Type2["Null"] = 1] = "Null";
        Type2[Type2["Int"] = 2] = "Int";
        Type2[Type2["Float"] = 3] = "Float";
        Type2[Type2["Binary"] = 4] = "Binary";
        Type2[Type2["Utf8"] = 5] = "Utf8";
        Type2[Type2["Bool"] = 6] = "Bool";
        Type2[Type2["Decimal"] = 7] = "Decimal";
        Type2[Type2["Date"] = 8] = "Date";
        Type2[Type2["Time"] = 9] = "Time";
        Type2[Type2["Timestamp"] = 10] = "Timestamp";
        Type2[Type2["Interval"] = 11] = "Interval";
        Type2[Type2["List"] = 12] = "List";
        Type2[Type2["Struct"] = 13] = "Struct";
        Type2[Type2["Union"] = 14] = "Union";
        Type2[Type2["FixedSizeBinary"] = 15] = "FixedSizeBinary";
        Type2[Type2["FixedSizeList"] = 16] = "FixedSizeList";
        Type2[Type2["Map"] = 17] = "Map";
        Type2[Type2["Dictionary"] = -1] = "Dictionary";
        Type2[Type2["Int8"] = -2] = "Int8";
        Type2[Type2["Int16"] = -3] = "Int16";
        Type2[Type2["Int32"] = -4] = "Int32";
        Type2[Type2["Int64"] = -5] = "Int64";
        Type2[Type2["Uint8"] = -6] = "Uint8";
        Type2[Type2["Uint16"] = -7] = "Uint16";
        Type2[Type2["Uint32"] = -8] = "Uint32";
        Type2[Type2["Uint64"] = -9] = "Uint64";
        Type2[Type2["Float16"] = -10] = "Float16";
        Type2[Type2["Float32"] = -11] = "Float32";
        Type2[Type2["Float64"] = -12] = "Float64";
        Type2[Type2["DateDay"] = -13] = "DateDay";
        Type2[Type2["DateMillisecond"] = -14] = "DateMillisecond";
        Type2[Type2["TimestampSecond"] = -15] = "TimestampSecond";
        Type2[Type2["TimestampMillisecond"] = -16] = "TimestampMillisecond";
        Type2[Type2["TimestampMicrosecond"] = -17] = "TimestampMicrosecond";
        Type2[Type2["TimestampNanosecond"] = -18] = "TimestampNanosecond";
        Type2[Type2["TimeSecond"] = -19] = "TimeSecond";
        Type2[Type2["TimeMillisecond"] = -20] = "TimeMillisecond";
        Type2[Type2["TimeMicrosecond"] = -21] = "TimeMicrosecond";
        Type2[Type2["TimeNanosecond"] = -22] = "TimeNanosecond";
        Type2[Type2["DenseUnion"] = -23] = "DenseUnion";
        Type2[Type2["SparseUnion"] = -24] = "SparseUnion";
        Type2[Type2["IntervalDayTime"] = -25] = "IntervalDayTime";
        Type2[Type2["IntervalYearMonth"] = -26] = "IntervalYearMonth";
      })(Type || (Type = {}));
    }
  });

  // ../schema/src/lib/schema/impl/type.ts
  var DataType, Null, Bool, Int, Int8, Int16, Int32, Uint8, Uint16, Uint32, Precision, Float, Float32, Float64, Binary, Utf8, DateUnit, Date, TimeUnit, Time, Timestamp, IntervalUnit, Interval, FixedSizeList, Struct;
  var init_type = __esm({
    "../schema/src/lib/schema/impl/type.ts"() {
      init_enum();
      DataType = class {
        static isNull(x) {
          return x && x.typeId === Type.Null;
        }
        static isInt(x) {
          return x && x.typeId === Type.Int;
        }
        static isFloat(x) {
          return x && x.typeId === Type.Float;
        }
        static isBinary(x) {
          return x && x.typeId === Type.Binary;
        }
        static isUtf8(x) {
          return x && x.typeId === Type.Utf8;
        }
        static isBool(x) {
          return x && x.typeId === Type.Bool;
        }
        static isDecimal(x) {
          return x && x.typeId === Type.Decimal;
        }
        static isDate(x) {
          return x && x.typeId === Type.Date;
        }
        static isTime(x) {
          return x && x.typeId === Type.Time;
        }
        static isTimestamp(x) {
          return x && x.typeId === Type.Timestamp;
        }
        static isInterval(x) {
          return x && x.typeId === Type.Interval;
        }
        static isList(x) {
          return x && x.typeId === Type.List;
        }
        static isStruct(x) {
          return x && x.typeId === Type.Struct;
        }
        static isUnion(x) {
          return x && x.typeId === Type.Union;
        }
        static isFixedSizeBinary(x) {
          return x && x.typeId === Type.FixedSizeBinary;
        }
        static isFixedSizeList(x) {
          return x && x.typeId === Type.FixedSizeList;
        }
        static isMap(x) {
          return x && x.typeId === Type.Map;
        }
        static isDictionary(x) {
          return x && x.typeId === Type.Dictionary;
        }
        get typeId() {
          return Type.NONE;
        }
        compareTo(other) {
          return this === other;
        }
      };
      Null = class extends DataType {
        get typeId() {
          return Type.Null;
        }
        get [Symbol.toStringTag]() {
          return "Null";
        }
        toString() {
          return "Null";
        }
      };
      Bool = class extends DataType {
        get typeId() {
          return Type.Bool;
        }
        get [Symbol.toStringTag]() {
          return "Bool";
        }
        toString() {
          return "Bool";
        }
      };
      Int = class extends DataType {
        constructor(isSigned, bitWidth) {
          super();
          this.isSigned = isSigned;
          this.bitWidth = bitWidth;
        }
        get typeId() {
          return Type.Int;
        }
        get [Symbol.toStringTag]() {
          return "Int";
        }
        toString() {
          return `${this.isSigned ? "I" : "Ui"}nt${this.bitWidth}`;
        }
      };
      Int8 = class extends Int {
        constructor() {
          super(true, 8);
        }
      };
      Int16 = class extends Int {
        constructor() {
          super(true, 16);
        }
      };
      Int32 = class extends Int {
        constructor() {
          super(true, 32);
        }
      };
      Uint8 = class extends Int {
        constructor() {
          super(false, 8);
        }
      };
      Uint16 = class extends Int {
        constructor() {
          super(false, 16);
        }
      };
      Uint32 = class extends Int {
        constructor() {
          super(false, 32);
        }
      };
      Precision = {
        HALF: 16,
        SINGLE: 32,
        DOUBLE: 64
      };
      Float = class extends DataType {
        constructor(precision) {
          super();
          this.precision = precision;
        }
        get typeId() {
          return Type.Float;
        }
        get [Symbol.toStringTag]() {
          return "Float";
        }
        toString() {
          return `Float${this.precision}`;
        }
      };
      Float32 = class extends Float {
        constructor() {
          super(Precision.SINGLE);
        }
      };
      Float64 = class extends Float {
        constructor() {
          super(Precision.DOUBLE);
        }
      };
      Binary = class extends DataType {
        constructor() {
          super();
        }
        get typeId() {
          return Type.Binary;
        }
        toString() {
          return "Binary";
        }
        get [Symbol.toStringTag]() {
          return "Binary";
        }
      };
      Utf8 = class extends DataType {
        get typeId() {
          return Type.Utf8;
        }
        get [Symbol.toStringTag]() {
          return "Utf8";
        }
        toString() {
          return "Utf8";
        }
      };
      DateUnit = {
        DAY: 0,
        MILLISECOND: 1
      };
      Date = class extends DataType {
        constructor(unit) {
          super();
          this.unit = unit;
        }
        get typeId() {
          return Type.Date;
        }
        get [Symbol.toStringTag]() {
          return "Date";
        }
        toString() {
          return `Date${(this.unit + 1) * 32}<${DateUnit[this.unit]}>`;
        }
      };
      TimeUnit = {
        SECOND: 1,
        MILLISECOND: 1e3,
        MICROSECOND: 1e6,
        NANOSECOND: 1e9
      };
      Time = class extends DataType {
        constructor(unit, bitWidth) {
          super();
          this.unit = unit;
          this.bitWidth = bitWidth;
        }
        get typeId() {
          return Type.Time;
        }
        toString() {
          return `Time${this.bitWidth}<${TimeUnit[this.unit]}>`;
        }
        get [Symbol.toStringTag]() {
          return "Time";
        }
      };
      Timestamp = class extends DataType {
        constructor(unit, timezone = null) {
          super();
          this.unit = unit;
          this.timezone = timezone;
        }
        get typeId() {
          return Type.Timestamp;
        }
        get [Symbol.toStringTag]() {
          return "Timestamp";
        }
        toString() {
          return `Timestamp<${TimeUnit[this.unit]}${this.timezone ? `, ${this.timezone}` : ""}>`;
        }
      };
      IntervalUnit = {
        DAY_TIME: 0,
        YEAR_MONTH: 1
      };
      Interval = class extends DataType {
        constructor(unit) {
          super();
          this.unit = unit;
        }
        get typeId() {
          return Type.Interval;
        }
        get [Symbol.toStringTag]() {
          return "Interval";
        }
        toString() {
          return `Interval<${IntervalUnit[this.unit]}>`;
        }
      };
      FixedSizeList = class extends DataType {
        constructor(listSize, child) {
          super();
          this.listSize = listSize;
          this.children = [child];
        }
        get typeId() {
          return Type.FixedSizeList;
        }
        get valueType() {
          return this.children[0].type;
        }
        get valueField() {
          return this.children[0];
        }
        get [Symbol.toStringTag]() {
          return "FixedSizeList";
        }
        toString() {
          return `FixedSizeList[${this.listSize}]<${this.valueType}>`;
        }
      };
      Struct = class extends DataType {
        constructor(children) {
          super();
          this.children = children;
        }
        get typeId() {
          return Type.Struct;
        }
        toString() {
          return `Struct<{${this.children.map((f2) => `${f2.name}:${f2.type}`).join(", ")}}>`;
        }
        get [Symbol.toStringTag]() {
          return "Struct";
        }
      };
    }
  });

  // ../schema/src/lib/schema/schema.ts
  var init_schema2 = __esm({
    "../schema/src/lib/schema/schema.ts"() {
      init_schema();
      init_field();
      init_type();
    }
  });

  // ../schema/src/lib/arrow/arrow-like-type-utils.ts
  function getArrowTypeFromTypedArray(array) {
    switch (array.constructor) {
      case Int8Array:
        return new Int8();
      case Uint8Array:
        return new Uint8();
      case Int16Array:
        return new Int16();
      case Uint16Array:
        return new Uint16();
      case Int32Array:
        return new Int32();
      case Uint32Array:
        return new Uint32();
      case Float32Array:
        return new Float32();
      case Float64Array:
        return new Float64();
      default:
        throw new Error("array type not supported");
    }
  }
  var init_arrow_like_type_utils = __esm({
    "../schema/src/lib/arrow/arrow-like-type-utils.ts"() {
      init_schema2();
    }
  });

  // ../schema/src/category/mesh/deduce-mesh-schema.ts
  function deduceMeshField(attributeName, attribute, optionalMetadata) {
    const type = getArrowTypeFromTypedArray(attribute.value);
    const metadata = optionalMetadata ? optionalMetadata : makeMeshAttributeMetadata(attribute);
    const field = new Field(attributeName, new FixedSizeList(attribute.size, new Field("value", type)), false, metadata);
    return field;
  }
  function makeMeshAttributeMetadata(attribute) {
    const result = new Map();
    if ("byteOffset" in attribute) {
      result.set("byteOffset", attribute.byteOffset.toString(10));
    }
    if ("byteStride" in attribute) {
      result.set("byteStride", attribute.byteStride.toString(10));
    }
    if ("normalized" in attribute) {
      result.set("normalized", attribute.normalized.toString());
    }
    return result;
  }
  var init_deduce_mesh_schema = __esm({
    "../schema/src/category/mesh/deduce-mesh-schema.ts"() {
      init_schema2();
      init_arrow_like_type_utils();
    }
  });

  // ../schema/src/index.ts
  var init_src5 = __esm({
    "../schema/src/index.ts"() {
      init_mesh_utils();
      init_deduce_mesh_schema();
      init_schema2();
    }
  });

  // ../draco/src/lib/utils/get-draco-schema.ts
  function getDracoSchema(attributes, loaderData, indices) {
    const metadataMap = makeMetadata(loaderData.metadata);
    const fields = [];
    const namedLoaderDataAttributes = transformAttributesLoaderData(loaderData.attributes);
    for (const attributeName in attributes) {
      const attribute = attributes[attributeName];
      const field = getArrowFieldFromAttribute(attributeName, attribute, namedLoaderDataAttributes[attributeName]);
      fields.push(field);
    }
    if (indices) {
      const indicesField = getArrowFieldFromAttribute("indices", indices);
      fields.push(indicesField);
    }
    return new Schema(fields, metadataMap);
  }
  function transformAttributesLoaderData(loaderData) {
    const result = {};
    for (const key in loaderData) {
      const dracoAttribute = loaderData[key];
      result[dracoAttribute.name || "undefined"] = dracoAttribute;
    }
    return result;
  }
  function getArrowFieldFromAttribute(attributeName, attribute, loaderData) {
    const metadataMap = loaderData ? makeMetadata(loaderData.metadata) : void 0;
    const field = deduceMeshField(attributeName, attribute, metadataMap);
    return field;
  }
  function makeMetadata(metadata) {
    const metadataMap = new Map();
    for (const key in metadata) {
      metadataMap.set(`${key}.string`, JSON.stringify(metadata[key]));
    }
    return metadataMap;
  }
  var init_get_draco_schema = __esm({
    "../draco/src/lib/utils/get-draco-schema.ts"() {
      init_src5();
      init_src5();
    }
  });

  // ../draco/src/lib/draco-parser.ts
  function getDracoDataType(draco, attributeType) {
    switch (attributeType) {
      case Float32Array:
        return draco.DT_FLOAT32;
      case Int8Array:
        return draco.DT_INT8;
      case Int16Array:
        return draco.DT_INT16;
      case Int32Array:
        return draco.DT_INT32;
      case Uint8Array:
        return draco.DT_UINT8;
      case Uint16Array:
        return draco.DT_UINT16;
      case Uint32Array:
        return draco.DT_UINT32;
      default:
        return draco.DT_INVALID;
    }
  }
  function getInt32Array(dracoArray) {
    const numValues = dracoArray.size();
    const intArray = new Int32Array(numValues);
    for (let i2 = 0; i2 < numValues; i2++) {
      intArray[i2] = dracoArray.GetValue(i2);
    }
    return intArray;
  }
  function getUint32Array(dracoArray) {
    const numValues = dracoArray.size();
    const intArray = new Int32Array(numValues);
    for (let i2 = 0; i2 < numValues; i2++) {
      intArray[i2] = dracoArray.GetValue(i2);
    }
    return intArray;
  }
  var DRACO_TO_GLTF_ATTRIBUTE_NAME_MAP, DRACO_DATA_TYPE_TO_TYPED_ARRAY_MAP, INDEX_ITEM_SIZE, DracoParser;
  var init_draco_parser = __esm({
    "../draco/src/lib/draco-parser.ts"() {
      init_src5();
      init_get_draco_schema();
      DRACO_TO_GLTF_ATTRIBUTE_NAME_MAP = {
        POSITION: "POSITION",
        NORMAL: "NORMAL",
        COLOR: "COLOR_0",
        TEX_COORD: "TEXCOORD_0"
      };
      DRACO_DATA_TYPE_TO_TYPED_ARRAY_MAP = {
        1: Int8Array,
        2: Uint8Array,
        3: Int16Array,
        4: Uint16Array,
        5: Int32Array,
        6: Uint32Array,
        9: Float32Array
      };
      INDEX_ITEM_SIZE = 4;
      DracoParser = class {
        constructor(draco) {
          this.draco = draco;
          this.decoder = new this.draco.Decoder();
          this.metadataQuerier = new this.draco.MetadataQuerier();
        }
        destroy() {
          this.draco.destroy(this.decoder);
          this.draco.destroy(this.metadataQuerier);
        }
        parseSync(arrayBuffer, options = {}) {
          const buffer = new this.draco.DecoderBuffer();
          buffer.Init(new Int8Array(arrayBuffer), arrayBuffer.byteLength);
          this._disableAttributeTransforms(options);
          const geometry_type = this.decoder.GetEncodedGeometryType(buffer);
          const dracoGeometry = geometry_type === this.draco.TRIANGULAR_MESH ? new this.draco.Mesh() : new this.draco.PointCloud();
          try {
            let dracoStatus;
            switch (geometry_type) {
              case this.draco.TRIANGULAR_MESH:
                dracoStatus = this.decoder.DecodeBufferToMesh(buffer, dracoGeometry);
                break;
              case this.draco.POINT_CLOUD:
                dracoStatus = this.decoder.DecodeBufferToPointCloud(buffer, dracoGeometry);
                break;
              default:
                throw new Error("DRACO: Unknown geometry type.");
            }
            if (!dracoStatus.ok() || !dracoGeometry.ptr) {
              const message = `DRACO decompression failed: ${dracoStatus.error_msg()}`;
              throw new Error(message);
            }
            const loaderData = this._getDracoLoaderData(dracoGeometry, geometry_type, options);
            const geometry = this._getMeshData(dracoGeometry, loaderData, options);
            const boundingBox = getMeshBoundingBox(geometry.attributes);
            const schema = getDracoSchema(geometry.attributes, loaderData, geometry.indices);
            const data = {
              loader: "draco",
              loaderData,
              header: {
                vertexCount: dracoGeometry.num_points(),
                boundingBox
              },
              ...geometry,
              schema
            };
            return data;
          } finally {
            this.draco.destroy(buffer);
            if (dracoGeometry) {
              this.draco.destroy(dracoGeometry);
            }
          }
        }
        _getDracoLoaderData(dracoGeometry, geometry_type, options) {
          const metadata = this._getTopLevelMetadata(dracoGeometry);
          const attributes = this._getDracoAttributes(dracoGeometry, options);
          return {
            geometry_type,
            num_attributes: dracoGeometry.num_attributes(),
            num_points: dracoGeometry.num_points(),
            num_faces: dracoGeometry instanceof this.draco.Mesh ? dracoGeometry.num_faces() : 0,
            metadata,
            attributes
          };
        }
        _getDracoAttributes(dracoGeometry, options) {
          const dracoAttributes = {};
          for (let attributeId = 0; attributeId < dracoGeometry.num_attributes(); attributeId++) {
            const dracoAttribute = this.decoder.GetAttribute(dracoGeometry, attributeId);
            const metadata = this._getAttributeMetadata(dracoGeometry, attributeId);
            dracoAttributes[dracoAttribute.unique_id()] = {
              unique_id: dracoAttribute.unique_id(),
              attribute_type: dracoAttribute.attribute_type(),
              data_type: dracoAttribute.data_type(),
              num_components: dracoAttribute.num_components(),
              byte_offset: dracoAttribute.byte_offset(),
              byte_stride: dracoAttribute.byte_stride(),
              normalized: dracoAttribute.normalized(),
              attribute_index: attributeId,
              metadata
            };
            const quantization = this._getQuantizationTransform(dracoAttribute, options);
            if (quantization) {
              dracoAttributes[dracoAttribute.unique_id()].quantization_transform = quantization;
            }
            const octahedron = this._getOctahedronTransform(dracoAttribute, options);
            if (octahedron) {
              dracoAttributes[dracoAttribute.unique_id()].octahedron_transform = octahedron;
            }
          }
          return dracoAttributes;
        }
        _getMeshData(dracoGeometry, loaderData, options) {
          const attributes = this._getMeshAttributes(loaderData, dracoGeometry, options);
          const positionAttribute = attributes.POSITION;
          if (!positionAttribute) {
            throw new Error("DRACO: No position attribute found.");
          }
          if (dracoGeometry instanceof this.draco.Mesh) {
            switch (options.topology) {
              case "triangle-strip":
                return {
                  topology: "triangle-strip",
                  mode: 4,
                  attributes,
                  indices: {
                    value: this._getTriangleStripIndices(dracoGeometry),
                    size: 1
                  }
                };
              case "triangle-list":
              default:
                return {
                  topology: "triangle-list",
                  mode: 5,
                  attributes,
                  indices: {
                    value: this._getTriangleListIndices(dracoGeometry),
                    size: 1
                  }
                };
            }
          }
          return {
            topology: "point-list",
            mode: 0,
            attributes
          };
        }
        _getMeshAttributes(loaderData, dracoGeometry, options) {
          const attributes = {};
          for (const loaderAttribute of Object.values(loaderData.attributes)) {
            const attributeName = this._deduceAttributeName(loaderAttribute, options);
            loaderAttribute.name = attributeName;
            const { value, size } = this._getAttributeValues(dracoGeometry, loaderAttribute);
            attributes[attributeName] = {
              value,
              size,
              byteOffset: loaderAttribute.byte_offset,
              byteStride: loaderAttribute.byte_stride,
              normalized: loaderAttribute.normalized
            };
          }
          return attributes;
        }
        _getTriangleListIndices(dracoGeometry) {
          const numFaces = dracoGeometry.num_faces();
          const numIndices = numFaces * 3;
          const byteLength = numIndices * INDEX_ITEM_SIZE;
          const ptr = this.draco._malloc(byteLength);
          try {
            this.decoder.GetTrianglesUInt32Array(dracoGeometry, byteLength, ptr);
            return new Uint32Array(this.draco.HEAPF32.buffer, ptr, numIndices).slice();
          } finally {
            this.draco._free(ptr);
          }
        }
        _getTriangleStripIndices(dracoGeometry) {
          const dracoArray = new this.draco.DracoInt32Array();
          try {
            this.decoder.GetTriangleStripsFromMesh(dracoGeometry, dracoArray);
            return getUint32Array(dracoArray);
          } finally {
            this.draco.destroy(dracoArray);
          }
        }
        _getAttributeValues(dracoGeometry, attribute) {
          const TypedArrayCtor = DRACO_DATA_TYPE_TO_TYPED_ARRAY_MAP[attribute.data_type];
          const numComponents = attribute.num_components;
          const numPoints = dracoGeometry.num_points();
          const numValues = numPoints * numComponents;
          const byteLength = numValues * TypedArrayCtor.BYTES_PER_ELEMENT;
          const dataType = getDracoDataType(this.draco, TypedArrayCtor);
          let value;
          const ptr = this.draco._malloc(byteLength);
          try {
            const dracoAttribute = this.decoder.GetAttribute(dracoGeometry, attribute.attribute_index);
            this.decoder.GetAttributeDataArrayForAllPoints(dracoGeometry, dracoAttribute, dataType, byteLength, ptr);
            value = new TypedArrayCtor(this.draco.HEAPF32.buffer, ptr, numValues).slice();
          } finally {
            this.draco._free(ptr);
          }
          return { value, size: numComponents };
        }
        _deduceAttributeName(attribute, options) {
          const uniqueId = attribute.unique_id;
          for (const [attributeName, attributeUniqueId] of Object.entries(options.extraAttributes || {})) {
            if (attributeUniqueId === uniqueId) {
              return attributeName;
            }
          }
          const thisAttributeType = attribute.attribute_type;
          for (const dracoAttributeConstant in DRACO_TO_GLTF_ATTRIBUTE_NAME_MAP) {
            const attributeType = this.draco[dracoAttributeConstant];
            if (attributeType === thisAttributeType) {
              return DRACO_TO_GLTF_ATTRIBUTE_NAME_MAP[dracoAttributeConstant];
            }
          }
          const entryName = options.attributeNameEntry || "name";
          if (attribute.metadata[entryName]) {
            return attribute.metadata[entryName].string;
          }
          return `CUSTOM_ATTRIBUTE_${uniqueId}`;
        }
        _getTopLevelMetadata(dracoGeometry) {
          const dracoMetadata = this.decoder.GetMetadata(dracoGeometry);
          return this._getDracoMetadata(dracoMetadata);
        }
        _getAttributeMetadata(dracoGeometry, attributeId) {
          const dracoMetadata = this.decoder.GetAttributeMetadata(dracoGeometry, attributeId);
          return this._getDracoMetadata(dracoMetadata);
        }
        _getDracoMetadata(dracoMetadata) {
          if (!dracoMetadata || !dracoMetadata.ptr) {
            return {};
          }
          const result = {};
          const numEntries = this.metadataQuerier.NumEntries(dracoMetadata);
          for (let entryIndex = 0; entryIndex < numEntries; entryIndex++) {
            const entryName = this.metadataQuerier.GetEntryName(dracoMetadata, entryIndex);
            result[entryName] = this._getDracoMetadataField(dracoMetadata, entryName);
          }
          return result;
        }
        _getDracoMetadataField(dracoMetadata, entryName) {
          const dracoArray = new this.draco.DracoInt32Array();
          try {
            this.metadataQuerier.GetIntEntryArray(dracoMetadata, entryName, dracoArray);
            const intArray = getInt32Array(dracoArray);
            return {
              int: this.metadataQuerier.GetIntEntry(dracoMetadata, entryName),
              string: this.metadataQuerier.GetStringEntry(dracoMetadata, entryName),
              double: this.metadataQuerier.GetDoubleEntry(dracoMetadata, entryName),
              intArray
            };
          } finally {
            this.draco.destroy(dracoArray);
          }
        }
        _disableAttributeTransforms(options) {
          const { quantizedAttributes = [], octahedronAttributes = [] } = options;
          const skipAttributes = [...quantizedAttributes, ...octahedronAttributes];
          for (const dracoAttributeName of skipAttributes) {
            this.decoder.SkipAttributeTransform(this.draco[dracoAttributeName]);
          }
        }
        _getQuantizationTransform(dracoAttribute, options) {
          const { quantizedAttributes = [] } = options;
          const attribute_type = dracoAttribute.attribute_type();
          const skip = quantizedAttributes.map((type) => this.decoder[type]).includes(attribute_type);
          if (skip) {
            const transform = new this.draco.AttributeQuantizationTransform();
            try {
              if (transform.InitFromAttribute(dracoAttribute)) {
                return {
                  quantization_bits: transform.quantization_bits(),
                  range: transform.range(),
                  min_values: new Float32Array([1, 2, 3]).map((i2) => transform.min_value(i2))
                };
              }
            } finally {
              this.draco.destroy(transform);
            }
          }
          return null;
        }
        _getOctahedronTransform(dracoAttribute, options) {
          const { octahedronAttributes = [] } = options;
          const attribute_type = dracoAttribute.attribute_type();
          const octahedron = octahedronAttributes.map((type) => this.decoder[type]).includes(attribute_type);
          if (octahedron) {
            const transform = new this.draco.AttributeQuantizationTransform();
            try {
              if (transform.InitFromAttribute(dracoAttribute)) {
                return {
                  quantization_bits: transform.quantization_bits()
                };
              }
            } finally {
              this.draco.destroy(transform);
            }
          }
          return null;
        }
      };
    }
  });

  // ../draco/src/lib/draco-module-loader.ts
  async function loadDracoDecoderModule(options) {
    const modules = options.modules || {};
    if (modules.draco3d) {
      loadDecoderPromise = loadDecoderPromise || modules.draco3d.createDecoderModule({}).then((draco) => {
        return { draco };
      });
    } else {
      loadDecoderPromise = loadDecoderPromise || loadDracoDecoder(options);
    }
    return await loadDecoderPromise;
  }
  async function loadDracoDecoder(options) {
    let DracoDecoderModule;
    let wasmBinary;
    switch (options.draco && options.draco.decoderType) {
      case "js":
        DracoDecoderModule = await loadLibrary(DRACO_JS_DECODER_URL, "draco", options);
        break;
      case "wasm":
      default:
        [DracoDecoderModule, wasmBinary] = await Promise.all([
          await loadLibrary(DRACO_WASM_WRAPPER_URL, "draco", options),
          await loadLibrary(DRACO_WASM_DECODER_URL, "draco", options)
        ]);
    }
    DracoDecoderModule = DracoDecoderModule || globalThis.DracoDecoderModule;
    return await initializeDracoDecoder(DracoDecoderModule, wasmBinary);
  }
  function initializeDracoDecoder(DracoDecoderModule, wasmBinary) {
    const options = {};
    if (wasmBinary) {
      options.wasmBinary = wasmBinary;
    }
    return new Promise((resolve) => {
      DracoDecoderModule({
        ...options,
        onModuleLoaded: (draco) => resolve({ draco })
      });
    });
  }
  var DRACO_DECODER_VERSION, DRACO_ENCODER_VERSION, STATIC_DECODER_URL, DRACO_JS_DECODER_URL, DRACO_WASM_WRAPPER_URL, DRACO_WASM_DECODER_URL, DRACO_ENCODER_URL, loadDecoderPromise;
  var init_draco_module_loader = __esm({
    "../draco/src/lib/draco-module-loader.ts"() {
      init_src();
      DRACO_DECODER_VERSION = "1.5.5";
      DRACO_ENCODER_VERSION = "1.4.1";
      STATIC_DECODER_URL = `https://www.gstatic.com/draco/versioned/decoders/${DRACO_DECODER_VERSION}`;
      DRACO_JS_DECODER_URL = `${STATIC_DECODER_URL}/draco_decoder.js`;
      DRACO_WASM_WRAPPER_URL = `${STATIC_DECODER_URL}/draco_wasm_wrapper.js`;
      DRACO_WASM_DECODER_URL = `${STATIC_DECODER_URL}/draco_decoder.wasm`;
      DRACO_ENCODER_URL = `https://raw.githubusercontent.com/google/draco/${DRACO_ENCODER_VERSION}/javascript/draco_encoder.js`;
    }
  });

  // ../draco/src/index.ts
  async function parse(arrayBuffer, options) {
    const { draco } = await loadDracoDecoderModule(options);
    const dracoParser = new DracoParser(draco);
    try {
      return dracoParser.parseSync(arrayBuffer, options?.draco);
    } finally {
      dracoParser.destroy();
    }
  }
  var DracoLoader2;
  var init_src6 = __esm({
    "../draco/src/index.ts"() {
      init_draco_loader();
      init_draco_parser();
      init_draco_module_loader();
      DracoLoader2 = {
        ...DracoLoader,
        parse
      };
    }
  });

  // src/lib/gltf-utils/gltf-attribute-utils.ts
  function getGLTFAccessors(attributes) {
    const accessors = {};
    for (const name10 in attributes) {
      const attribute = attributes[name10];
      if (name10 !== "indices") {
        const glTFAccessor = getGLTFAccessor(attribute);
        accessors[name10] = glTFAccessor;
      }
    }
    return accessors;
  }
  function getGLTFAccessor(attribute) {
    const { buffer, size, count } = getAccessorData(attribute);
    const glTFAccessor = {
      value: buffer,
      size,
      byteOffset: 0,
      count,
      type: getAccessorTypeFromSize(size),
      componentType: getComponentTypeFromArray(buffer)
    };
    return glTFAccessor;
  }
  function getAccessorData(attribute) {
    let buffer = attribute;
    let size = 1;
    let count = 0;
    if (attribute && attribute.value) {
      buffer = attribute.value;
      size = attribute.size || 1;
    }
    if (buffer) {
      if (!ArrayBuffer.isView(buffer)) {
        buffer = toTypedArray(buffer, Float32Array);
      }
      count = buffer.length / size;
    }
    return { buffer, size, count };
  }
  function toTypedArray(array, ArrayType, convertTypedArrays = false) {
    if (!array) {
      return null;
    }
    if (Array.isArray(array)) {
      return new ArrayType(array);
    }
    if (convertTypedArrays && !(array instanceof ArrayType)) {
      return new ArrayType(array);
    }
    return array;
  }
  var init_gltf_attribute_utils = __esm({
    "src/lib/gltf-utils/gltf-attribute-utils.ts"() {
      init_gltf_utils();
    }
  });

  // src/lib/extensions/KHR_draco_mesh_compression.ts
  var KHR_draco_mesh_compression_exports = {};
  __export(KHR_draco_mesh_compression_exports, {
    decode: () => decode3,
    encode: () => encode,
    name: () => name4,
    preprocess: () => preprocess3
  });
  function preprocess3(gltfData, options, context) {
    const scenegraph = new GLTFScenegraph(gltfData);
    for (const primitive of makeMeshPrimitiveIterator(scenegraph)) {
      if (scenegraph.getObjectExtension(primitive, KHR_DRACO_MESH_COMPRESSION)) {
      }
    }
  }
  async function decode3(gltfData, options, context) {
    if (!options?.gltf?.decompressMeshes) {
      return;
    }
    const scenegraph = new GLTFScenegraph(gltfData);
    const promises = [];
    for (const primitive of makeMeshPrimitiveIterator(scenegraph)) {
      if (scenegraph.getObjectExtension(primitive, KHR_DRACO_MESH_COMPRESSION)) {
        promises.push(decompressPrimitive(scenegraph, primitive, options, context));
      }
    }
    await Promise.all(promises);
    scenegraph.removeExtension(KHR_DRACO_MESH_COMPRESSION);
  }
  function encode(gltfData, options = {}) {
    const scenegraph = new GLTFScenegraph(gltfData);
    for (const mesh of scenegraph.json.meshes || []) {
      compressMesh(mesh, options);
      scenegraph.addRequiredExtension(KHR_DRACO_MESH_COMPRESSION);
    }
  }
  async function decompressPrimitive(scenegraph, primitive, options, context) {
    const dracoExtension = scenegraph.getObjectExtension(primitive, KHR_DRACO_MESH_COMPRESSION);
    if (!dracoExtension) {
      return;
    }
    const buffer = scenegraph.getTypedArrayForBufferView(dracoExtension.bufferView);
    const bufferCopy = sliceArrayBuffer(buffer.buffer, buffer.byteOffset);
    const { parse: parse4 } = context;
    const dracoOptions = { ...options };
    delete dracoOptions["3d-tiles"];
    const decodedData = await parse4(bufferCopy, DracoLoader2, dracoOptions, context);
    const decodedAttributes = getGLTFAccessors(decodedData.attributes);
    for (const [attributeName, decodedAttribute] of Object.entries(decodedAttributes)) {
      if (attributeName in primitive.attributes) {
        const accessorIndex = primitive.attributes[attributeName];
        const accessor = scenegraph.getAccessor(accessorIndex);
        if (accessor?.min && accessor?.max) {
          decodedAttribute.min = accessor.min;
          decodedAttribute.max = accessor.max;
        }
      }
    }
    primitive.attributes = decodedAttributes;
    if (decodedData.indices) {
      primitive.indices = getGLTFAccessor(decodedData.indices);
    }
    checkPrimitive(primitive);
  }
  function compressMesh(attributes, indices, mode = 4, options, context) {
    if (!options.DracoWriter) {
      throw new Error("options.gltf.DracoWriter not provided");
    }
    const compressedData = options.DracoWriter.encodeSync({ attributes });
    const decodedData = context?.parseSync?.({ attributes });
    const fauxAccessors = options._addFauxAttributes(decodedData.attributes);
    const bufferViewIndex = options.addBufferView(compressedData);
    const glTFMesh = {
      primitives: [
        {
          attributes: fauxAccessors,
          mode,
          extensions: {
            [KHR_DRACO_MESH_COMPRESSION]: {
              bufferView: bufferViewIndex,
              attributes: fauxAccessors
            }
          }
        }
      ]
    };
    return glTFMesh;
  }
  function checkPrimitive(primitive) {
    if (!primitive.attributes && Object.keys(primitive.attributes).length > 0) {
      throw new Error("glTF: Empty primitive detected: Draco decompression failure?");
    }
  }
  function* makeMeshPrimitiveIterator(scenegraph) {
    for (const mesh of scenegraph.json.meshes || []) {
      for (const primitive of mesh.primitives) {
        yield primitive;
      }
    }
  }
  var KHR_DRACO_MESH_COMPRESSION, name4;
  var init_KHR_draco_mesh_compression = __esm({
    "src/lib/extensions/KHR_draco_mesh_compression.ts"() {
      init_src6();
      init_src2();
      init_gltf_scenegraph();
      init_gltf_attribute_utils();
      KHR_DRACO_MESH_COMPRESSION = "KHR_draco_mesh_compression";
      name4 = KHR_DRACO_MESH_COMPRESSION;
    }
  });

  // ../../node_modules/@math.gl/core/dist/esm/lib/assert.js
  function assert5(condition, message) {
    if (!condition) {
      throw new Error("math.gl assertion ".concat(message));
    }
  }
  var init_assert5 = __esm({
    "../../node_modules/@math.gl/core/dist/esm/lib/assert.js"() {
    }
  });

  // ../../node_modules/@math.gl/core/dist/esm/lib/common.js
  function formatValue(value, {
    precision = config.precision
  } = {}) {
    value = round(value);
    return "".concat(parseFloat(value.toPrecision(precision)));
  }
  function isArray(value) {
    return Array.isArray(value) || ArrayBuffer.isView(value) && !(value instanceof DataView);
  }
  function equals(a2, b, epsilon) {
    const oldEpsilon = config.EPSILON;
    if (epsilon) {
      config.EPSILON = epsilon;
    }
    try {
      if (a2 === b) {
        return true;
      }
      if (isArray(a2) && isArray(b)) {
        if (a2.length !== b.length) {
          return false;
        }
        for (let i2 = 0; i2 < a2.length; ++i2) {
          if (!equals(a2[i2], b[i2])) {
            return false;
          }
        }
        return true;
      }
      if (a2 && a2.equals) {
        return a2.equals(b);
      }
      if (b && b.equals) {
        return b.equals(a2);
      }
      if (typeof a2 === "number" && typeof b === "number") {
        return Math.abs(a2 - b) <= config.EPSILON * Math.max(1, Math.abs(a2), Math.abs(b));
      }
      return false;
    } finally {
      config.EPSILON = oldEpsilon;
    }
  }
  function round(value) {
    return Math.round(value / config.EPSILON) * config.EPSILON;
  }
  var RADIANS_TO_DEGREES, DEGREES_TO_RADIANS, config;
  var init_common = __esm({
    "../../node_modules/@math.gl/core/dist/esm/lib/common.js"() {
      RADIANS_TO_DEGREES = 1 / Math.PI * 180;
      DEGREES_TO_RADIANS = 1 / 180 * Math.PI;
      config = {
        EPSILON: 1e-12,
        debug: false,
        precision: 4,
        printTypes: false,
        printDegrees: false,
        printRowMajor: true
      };
    }
  });

  // ../../node_modules/@math.gl/core/dist/esm/classes/base/math-array.js
  function _extendableBuiltin(cls) {
    function ExtendableBuiltin() {
      var instance = Reflect.construct(cls, Array.from(arguments));
      Object.setPrototypeOf(instance, Object.getPrototypeOf(this));
      return instance;
    }
    ExtendableBuiltin.prototype = Object.create(cls.prototype, {
      constructor: {
        value: cls,
        enumerable: false,
        writable: true,
        configurable: true
      }
    });
    if (Object.setPrototypeOf) {
      Object.setPrototypeOf(ExtendableBuiltin, cls);
    } else {
      ExtendableBuiltin.__proto__ = cls;
    }
    return ExtendableBuiltin;
  }
  var MathArray;
  var init_math_array = __esm({
    "../../node_modules/@math.gl/core/dist/esm/classes/base/math-array.js"() {
      init_common();
      MathArray = class extends _extendableBuiltin(Array) {
        clone() {
          return new this.constructor().copy(this);
        }
        fromArray(array, offset = 0) {
          for (let i2 = 0; i2 < this.ELEMENTS; ++i2) {
            this[i2] = array[i2 + offset];
          }
          return this.check();
        }
        toArray(targetArray = [], offset = 0) {
          for (let i2 = 0; i2 < this.ELEMENTS; ++i2) {
            targetArray[offset + i2] = this[i2];
          }
          return targetArray;
        }
        from(arrayOrObject) {
          return Array.isArray(arrayOrObject) ? this.copy(arrayOrObject) : this.fromObject(arrayOrObject);
        }
        to(arrayOrObject) {
          if (arrayOrObject === this) {
            return this;
          }
          return isArray(arrayOrObject) ? this.toArray(arrayOrObject) : this.toObject(arrayOrObject);
        }
        toTarget(target) {
          return target ? this.to(target) : this;
        }
        toFloat32Array() {
          return new Float32Array(this);
        }
        toString() {
          return this.formatString(config);
        }
        formatString(opts) {
          let string = "";
          for (let i2 = 0; i2 < this.ELEMENTS; ++i2) {
            string += (i2 > 0 ? ", " : "") + formatValue(this[i2], opts);
          }
          return "".concat(opts.printTypes ? this.constructor.name : "", "[").concat(string, "]");
        }
        equals(array) {
          if (!array || this.length !== array.length) {
            return false;
          }
          for (let i2 = 0; i2 < this.ELEMENTS; ++i2) {
            if (!equals(this[i2], array[i2])) {
              return false;
            }
          }
          return true;
        }
        exactEquals(array) {
          if (!array || this.length !== array.length) {
            return false;
          }
          for (let i2 = 0; i2 < this.ELEMENTS; ++i2) {
            if (this[i2] !== array[i2]) {
              return false;
            }
          }
          return true;
        }
        negate() {
          for (let i2 = 0; i2 < this.ELEMENTS; ++i2) {
            this[i2] = -this[i2];
          }
          return this.check();
        }
        lerp(a2, b, t2) {
          if (t2 === void 0) {
            return this.lerp(this, a2, b);
          }
          for (let i2 = 0; i2 < this.ELEMENTS; ++i2) {
            const ai = a2[i2];
            this[i2] = ai + t2 * (b[i2] - ai);
          }
          return this.check();
        }
        min(vector) {
          for (let i2 = 0; i2 < this.ELEMENTS; ++i2) {
            this[i2] = Math.min(vector[i2], this[i2]);
          }
          return this.check();
        }
        max(vector) {
          for (let i2 = 0; i2 < this.ELEMENTS; ++i2) {
            this[i2] = Math.max(vector[i2], this[i2]);
          }
          return this.check();
        }
        clamp(minVector, maxVector) {
          for (let i2 = 0; i2 < this.ELEMENTS; ++i2) {
            this[i2] = Math.min(Math.max(this[i2], minVector[i2]), maxVector[i2]);
          }
          return this.check();
        }
        add(...vectors) {
          for (const vector of vectors) {
            for (let i2 = 0; i2 < this.ELEMENTS; ++i2) {
              this[i2] += vector[i2];
            }
          }
          return this.check();
        }
        subtract(...vectors) {
          for (const vector of vectors) {
            for (let i2 = 0; i2 < this.ELEMENTS; ++i2) {
              this[i2] -= vector[i2];
            }
          }
          return this.check();
        }
        scale(scale2) {
          if (typeof scale2 === "number") {
            for (let i2 = 0; i2 < this.ELEMENTS; ++i2) {
              this[i2] *= scale2;
            }
          } else {
            for (let i2 = 0; i2 < this.ELEMENTS && i2 < scale2.length; ++i2) {
              this[i2] *= scale2[i2];
            }
          }
          return this.check();
        }
        multiplyByScalar(scalar) {
          for (let i2 = 0; i2 < this.ELEMENTS; ++i2) {
            this[i2] *= scalar;
          }
          return this.check();
        }
        check() {
          if (config.debug && !this.validate()) {
            throw new Error("math.gl: ".concat(this.constructor.name, " some fields set to invalid numbers'"));
          }
          return this;
        }
        validate() {
          let valid = this.length === this.ELEMENTS;
          for (let i2 = 0; i2 < this.ELEMENTS; ++i2) {
            valid = valid && Number.isFinite(this[i2]);
          }
          return valid;
        }
        sub(a2) {
          return this.subtract(a2);
        }
        setScalar(a2) {
          for (let i2 = 0; i2 < this.ELEMENTS; ++i2) {
            this[i2] = a2;
          }
          return this.check();
        }
        addScalar(a2) {
          for (let i2 = 0; i2 < this.ELEMENTS; ++i2) {
            this[i2] += a2;
          }
          return this.check();
        }
        subScalar(a2) {
          return this.addScalar(-a2);
        }
        multiplyScalar(scalar) {
          for (let i2 = 0; i2 < this.ELEMENTS; ++i2) {
            this[i2] *= scalar;
          }
          return this.check();
        }
        divideScalar(a2) {
          return this.multiplyByScalar(1 / a2);
        }
        clampScalar(min, max) {
          for (let i2 = 0; i2 < this.ELEMENTS; ++i2) {
            this[i2] = Math.min(Math.max(this[i2], min), max);
          }
          return this.check();
        }
        get elements() {
          return this;
        }
      };
    }
  });

  // ../../node_modules/@math.gl/core/dist/esm/lib/validators.js
  function validateVector(v, length) {
    if (v.length !== length) {
      return false;
    }
    for (let i2 = 0; i2 < v.length; ++i2) {
      if (!Number.isFinite(v[i2])) {
        return false;
      }
    }
    return true;
  }
  function checkNumber(value) {
    if (!Number.isFinite(value)) {
      throw new Error("Invalid number ".concat(value));
    }
    return value;
  }
  function checkVector(v, length, callerName = "") {
    if (config.debug && !validateVector(v, length)) {
      throw new Error("math.gl: ".concat(callerName, " some fields set to invalid numbers'"));
    }
    return v;
  }
  var init_validators = __esm({
    "../../node_modules/@math.gl/core/dist/esm/lib/validators.js"() {
      init_common();
    }
  });

  // ../../node_modules/@math.gl/core/dist/esm/classes/base/vector.js
  var Vector;
  var init_vector = __esm({
    "../../node_modules/@math.gl/core/dist/esm/classes/base/vector.js"() {
      init_math_array();
      init_validators();
      init_assert5();
      Vector = class extends MathArray {
        get x() {
          return this[0];
        }
        set x(value) {
          this[0] = checkNumber(value);
        }
        get y() {
          return this[1];
        }
        set y(value) {
          this[1] = checkNumber(value);
        }
        len() {
          return Math.sqrt(this.lengthSquared());
        }
        magnitude() {
          return this.len();
        }
        lengthSquared() {
          let length = 0;
          for (let i2 = 0; i2 < this.ELEMENTS; ++i2) {
            length += this[i2] * this[i2];
          }
          return length;
        }
        magnitudeSquared() {
          return this.lengthSquared();
        }
        distance(mathArray) {
          return Math.sqrt(this.distanceSquared(mathArray));
        }
        distanceSquared(mathArray) {
          let length = 0;
          for (let i2 = 0; i2 < this.ELEMENTS; ++i2) {
            const dist = this[i2] - mathArray[i2];
            length += dist * dist;
          }
          return checkNumber(length);
        }
        dot(mathArray) {
          let product = 0;
          for (let i2 = 0; i2 < this.ELEMENTS; ++i2) {
            product += this[i2] * mathArray[i2];
          }
          return checkNumber(product);
        }
        normalize() {
          const length = this.magnitude();
          if (length !== 0) {
            for (let i2 = 0; i2 < this.ELEMENTS; ++i2) {
              this[i2] /= length;
            }
          }
          return this.check();
        }
        multiply(...vectors) {
          for (const vector of vectors) {
            for (let i2 = 0; i2 < this.ELEMENTS; ++i2) {
              this[i2] *= vector[i2];
            }
          }
          return this.check();
        }
        divide(...vectors) {
          for (const vector of vectors) {
            for (let i2 = 0; i2 < this.ELEMENTS; ++i2) {
              this[i2] /= vector[i2];
            }
          }
          return this.check();
        }
        lengthSq() {
          return this.lengthSquared();
        }
        distanceTo(vector) {
          return this.distance(vector);
        }
        distanceToSquared(vector) {
          return this.distanceSquared(vector);
        }
        getComponent(i2) {
          assert5(i2 >= 0 && i2 < this.ELEMENTS, "index is out of range");
          return checkNumber(this[i2]);
        }
        setComponent(i2, value) {
          assert5(i2 >= 0 && i2 < this.ELEMENTS, "index is out of range");
          this[i2] = value;
          return this.check();
        }
        addVectors(a2, b) {
          return this.copy(a2).add(b);
        }
        subVectors(a2, b) {
          return this.copy(a2).subtract(b);
        }
        multiplyVectors(a2, b) {
          return this.copy(a2).multiply(b);
        }
        addScaledVector(a2, b) {
          return this.add(new this.constructor(a2).multiplyScalar(b));
        }
      };
    }
  });

  // ../../node_modules/gl-matrix/esm/common.js
  var ARRAY_TYPE, degree;
  var init_common2 = __esm({
    "../../node_modules/gl-matrix/esm/common.js"() {
      ARRAY_TYPE = typeof Float32Array !== "undefined" ? Float32Array : Array;
      degree = Math.PI / 180;
      if (!Math.hypot)
        Math.hypot = function() {
          var y = 0, i2 = arguments.length;
          while (i2--) {
            y += arguments[i2] * arguments[i2];
          }
          return Math.sqrt(y);
        };
    }
  });

  // ../../node_modules/gl-matrix/esm/vec2.js
  function create() {
    var out = new ARRAY_TYPE(2);
    if (ARRAY_TYPE != Float32Array) {
      out[0] = 0;
      out[1] = 0;
    }
    return out;
  }
  function transformMat3(out, a2, m) {
    var x = a2[0], y = a2[1];
    out[0] = m[0] * x + m[3] * y + m[6];
    out[1] = m[1] * x + m[4] * y + m[7];
    return out;
  }
  var forEach;
  var init_vec2 = __esm({
    "../../node_modules/gl-matrix/esm/vec2.js"() {
      init_common2();
      forEach = function() {
        var vec = create();
        return function(a2, stride, offset, count, fn, arg) {
          var i2, l2;
          if (!stride) {
            stride = 2;
          }
          if (!offset) {
            offset = 0;
          }
          if (count) {
            l2 = Math.min(count * stride + offset, a2.length);
          } else {
            l2 = a2.length;
          }
          for (i2 = offset; i2 < l2; i2 += stride) {
            vec[0] = a2[i2];
            vec[1] = a2[i2 + 1];
            fn(vec, vec, arg);
            a2[i2] = vec[0];
            a2[i2 + 1] = vec[1];
          }
          return a2;
        };
      }();
    }
  });

  // ../../node_modules/@math.gl/core/dist/esm/lib/gl-matrix-extras.js
  function vec3_transformMat4AsVector(out, a2, m) {
    const x = a2[0];
    const y = a2[1];
    const z = a2[2];
    const w = m[3] * x + m[7] * y + m[11] * z || 1;
    out[0] = (m[0] * x + m[4] * y + m[8] * z) / w;
    out[1] = (m[1] * x + m[5] * y + m[9] * z) / w;
    out[2] = (m[2] * x + m[6] * y + m[10] * z) / w;
    return out;
  }
  function vec3_transformMat2(out, a2, m) {
    const x = a2[0];
    const y = a2[1];
    out[0] = m[0] * x + m[2] * y;
    out[1] = m[1] * x + m[3] * y;
    out[2] = a2[2];
    return out;
  }
  function vec4_transformMat3(out, a2, m) {
    const x = a2[0];
    const y = a2[1];
    const z = a2[2];
    out[0] = m[0] * x + m[3] * y + m[6] * z;
    out[1] = m[1] * x + m[4] * y + m[7] * z;
    out[2] = m[2] * x + m[5] * y + m[8] * z;
    out[3] = a2[3];
    return out;
  }
  var init_gl_matrix_extras = __esm({
    "../../node_modules/@math.gl/core/dist/esm/lib/gl-matrix-extras.js"() {
    }
  });

  // ../../node_modules/gl-matrix/esm/vec3.js
  function create2() {
    var out = new ARRAY_TYPE(3);
    if (ARRAY_TYPE != Float32Array) {
      out[0] = 0;
      out[1] = 0;
      out[2] = 0;
    }
    return out;
  }
  function dot(a2, b) {
    return a2[0] * b[0] + a2[1] * b[1] + a2[2] * b[2];
  }
  function cross(out, a2, b) {
    var ax = a2[0], ay = a2[1], az = a2[2];
    var bx = b[0], by = b[1], bz = b[2];
    out[0] = ay * bz - az * by;
    out[1] = az * bx - ax * bz;
    out[2] = ax * by - ay * bx;
    return out;
  }
  function transformMat4(out, a2, m) {
    var x = a2[0], y = a2[1], z = a2[2];
    var w = m[3] * x + m[7] * y + m[11] * z + m[15];
    w = w || 1;
    out[0] = (m[0] * x + m[4] * y + m[8] * z + m[12]) / w;
    out[1] = (m[1] * x + m[5] * y + m[9] * z + m[13]) / w;
    out[2] = (m[2] * x + m[6] * y + m[10] * z + m[14]) / w;
    return out;
  }
  function transformMat32(out, a2, m) {
    var x = a2[0], y = a2[1], z = a2[2];
    out[0] = x * m[0] + y * m[3] + z * m[6];
    out[1] = x * m[1] + y * m[4] + z * m[7];
    out[2] = x * m[2] + y * m[5] + z * m[8];
    return out;
  }
  function transformQuat(out, a2, q) {
    var qx = q[0], qy = q[1], qz = q[2], qw = q[3];
    var x = a2[0], y = a2[1], z = a2[2];
    var uvx = qy * z - qz * y, uvy = qz * x - qx * z, uvz = qx * y - qy * x;
    var uuvx = qy * uvz - qz * uvy, uuvy = qz * uvx - qx * uvz, uuvz = qx * uvy - qy * uvx;
    var w2 = qw * 2;
    uvx *= w2;
    uvy *= w2;
    uvz *= w2;
    uuvx *= 2;
    uuvy *= 2;
    uuvz *= 2;
    out[0] = x + uvx + uuvx;
    out[1] = y + uvy + uuvy;
    out[2] = z + uvz + uuvz;
    return out;
  }
  function rotateX(out, a2, b, rad) {
    var p2 = [], r2 = [];
    p2[0] = a2[0] - b[0];
    p2[1] = a2[1] - b[1];
    p2[2] = a2[2] - b[2];
    r2[0] = p2[0];
    r2[1] = p2[1] * Math.cos(rad) - p2[2] * Math.sin(rad);
    r2[2] = p2[1] * Math.sin(rad) + p2[2] * Math.cos(rad);
    out[0] = r2[0] + b[0];
    out[1] = r2[1] + b[1];
    out[2] = r2[2] + b[2];
    return out;
  }
  function rotateY(out, a2, b, rad) {
    var p2 = [], r2 = [];
    p2[0] = a2[0] - b[0];
    p2[1] = a2[1] - b[1];
    p2[2] = a2[2] - b[2];
    r2[0] = p2[2] * Math.sin(rad) + p2[0] * Math.cos(rad);
    r2[1] = p2[1];
    r2[2] = p2[2] * Math.cos(rad) - p2[0] * Math.sin(rad);
    out[0] = r2[0] + b[0];
    out[1] = r2[1] + b[1];
    out[2] = r2[2] + b[2];
    return out;
  }
  function rotateZ(out, a2, b, rad) {
    var p2 = [], r2 = [];
    p2[0] = a2[0] - b[0];
    p2[1] = a2[1] - b[1];
    p2[2] = a2[2] - b[2];
    r2[0] = p2[0] * Math.cos(rad) - p2[1] * Math.sin(rad);
    r2[1] = p2[0] * Math.sin(rad) + p2[1] * Math.cos(rad);
    r2[2] = p2[2];
    out[0] = r2[0] + b[0];
    out[1] = r2[1] + b[1];
    out[2] = r2[2] + b[2];
    return out;
  }
  function angle(a2, b) {
    var ax = a2[0], ay = a2[1], az = a2[2], bx = b[0], by = b[1], bz = b[2], mag1 = Math.sqrt(ax * ax + ay * ay + az * az), mag2 = Math.sqrt(bx * bx + by * by + bz * bz), mag = mag1 * mag2, cosine = mag && dot(a2, b) / mag;
    return Math.acos(Math.min(Math.max(cosine, -1), 1));
  }
  var forEach2;
  var init_vec3 = __esm({
    "../../node_modules/gl-matrix/esm/vec3.js"() {
      init_common2();
      forEach2 = function() {
        var vec = create2();
        return function(a2, stride, offset, count, fn, arg) {
          var i2, l2;
          if (!stride) {
            stride = 3;
          }
          if (!offset) {
            offset = 0;
          }
          if (count) {
            l2 = Math.min(count * stride + offset, a2.length);
          } else {
            l2 = a2.length;
          }
          for (i2 = offset; i2 < l2; i2 += stride) {
            vec[0] = a2[i2];
            vec[1] = a2[i2 + 1];
            vec[2] = a2[i2 + 2];
            fn(vec, vec, arg);
            a2[i2] = vec[0];
            a2[i2 + 1] = vec[1];
            a2[i2 + 2] = vec[2];
          }
          return a2;
        };
      }();
    }
  });

  // ../../node_modules/@math.gl/core/dist/esm/classes/vector3.js
  var ORIGIN, ZERO, Vector3;
  var init_vector3 = __esm({
    "../../node_modules/@math.gl/core/dist/esm/classes/vector3.js"() {
      init_vector();
      init_common();
      init_validators();
      init_vec3();
      init_gl_matrix_extras();
      ORIGIN = [0, 0, 0];
      Vector3 = class extends Vector {
        static get ZERO() {
          if (!ZERO) {
            ZERO = new Vector3(0, 0, 0);
            Object.freeze(ZERO);
          }
          return ZERO;
        }
        constructor(x = 0, y = 0, z = 0) {
          super(-0, -0, -0);
          if (arguments.length === 1 && isArray(x)) {
            this.copy(x);
          } else {
            if (config.debug) {
              checkNumber(x);
              checkNumber(y);
              checkNumber(z);
            }
            this[0] = x;
            this[1] = y;
            this[2] = z;
          }
        }
        set(x, y, z) {
          this[0] = x;
          this[1] = y;
          this[2] = z;
          return this.check();
        }
        copy(array) {
          this[0] = array[0];
          this[1] = array[1];
          this[2] = array[2];
          return this.check();
        }
        fromObject(object) {
          if (config.debug) {
            checkNumber(object.x);
            checkNumber(object.y);
            checkNumber(object.z);
          }
          this[0] = object.x;
          this[1] = object.y;
          this[2] = object.z;
          return this.check();
        }
        toObject(object) {
          object.x = this[0];
          object.y = this[1];
          object.z = this[2];
          return object;
        }
        get ELEMENTS() {
          return 3;
        }
        get z() {
          return this[2];
        }
        set z(value) {
          this[2] = checkNumber(value);
        }
        angle(vector) {
          return angle(this, vector);
        }
        cross(vector) {
          cross(this, this, vector);
          return this.check();
        }
        rotateX({
          radians,
          origin = ORIGIN
        }) {
          rotateX(this, this, origin, radians);
          return this.check();
        }
        rotateY({
          radians,
          origin = ORIGIN
        }) {
          rotateY(this, this, origin, radians);
          return this.check();
        }
        rotateZ({
          radians,
          origin = ORIGIN
        }) {
          rotateZ(this, this, origin, radians);
          return this.check();
        }
        transform(matrix4) {
          return this.transformAsPoint(matrix4);
        }
        transformAsPoint(matrix4) {
          transformMat4(this, this, matrix4);
          return this.check();
        }
        transformAsVector(matrix4) {
          vec3_transformMat4AsVector(this, this, matrix4);
          return this.check();
        }
        transformByMatrix3(matrix3) {
          transformMat32(this, this, matrix3);
          return this.check();
        }
        transformByMatrix2(matrix2) {
          vec3_transformMat2(this, this, matrix2);
          return this.check();
        }
        transformByQuaternion(quaternion) {
          transformQuat(this, this, quaternion);
          return this.check();
        }
      };
    }
  });

  // ../../node_modules/@math.gl/core/dist/esm/classes/base/matrix.js
  var Matrix;
  var init_matrix = __esm({
    "../../node_modules/@math.gl/core/dist/esm/classes/base/matrix.js"() {
      init_math_array();
      init_validators();
      init_common();
      Matrix = class extends MathArray {
        toString() {
          let string = "[";
          if (config.printRowMajor) {
            string += "row-major:";
            for (let row = 0; row < this.RANK; ++row) {
              for (let col = 0; col < this.RANK; ++col) {
                string += " ".concat(this[col * this.RANK + row]);
              }
            }
          } else {
            string += "column-major:";
            for (let i2 = 0; i2 < this.ELEMENTS; ++i2) {
              string += " ".concat(this[i2]);
            }
          }
          string += "]";
          return string;
        }
        getElementIndex(row, col) {
          return col * this.RANK + row;
        }
        getElement(row, col) {
          return this[col * this.RANK + row];
        }
        setElement(row, col, value) {
          this[col * this.RANK + row] = checkNumber(value);
          return this;
        }
        getColumn(columnIndex, result = new Array(this.RANK).fill(-0)) {
          const firstIndex = columnIndex * this.RANK;
          for (let i2 = 0; i2 < this.RANK; ++i2) {
            result[i2] = this[firstIndex + i2];
          }
          return result;
        }
        setColumn(columnIndex, columnVector) {
          const firstIndex = columnIndex * this.RANK;
          for (let i2 = 0; i2 < this.RANK; ++i2) {
            this[firstIndex + i2] = columnVector[i2];
          }
          return this;
        }
      };
    }
  });

  // ../../node_modules/gl-matrix/esm/mat3.js
  function transpose(out, a2) {
    if (out === a2) {
      var a01 = a2[1], a02 = a2[2], a12 = a2[5];
      out[1] = a2[3];
      out[2] = a2[6];
      out[3] = a01;
      out[5] = a2[7];
      out[6] = a02;
      out[7] = a12;
    } else {
      out[0] = a2[0];
      out[1] = a2[3];
      out[2] = a2[6];
      out[3] = a2[1];
      out[4] = a2[4];
      out[5] = a2[7];
      out[6] = a2[2];
      out[7] = a2[5];
      out[8] = a2[8];
    }
    return out;
  }
  function invert(out, a2) {
    var a00 = a2[0], a01 = a2[1], a02 = a2[2];
    var a10 = a2[3], a11 = a2[4], a12 = a2[5];
    var a20 = a2[6], a21 = a2[7], a22 = a2[8];
    var b01 = a22 * a11 - a12 * a21;
    var b11 = -a22 * a10 + a12 * a20;
    var b21 = a21 * a10 - a11 * a20;
    var det = a00 * b01 + a01 * b11 + a02 * b21;
    if (!det) {
      return null;
    }
    det = 1 / det;
    out[0] = b01 * det;
    out[1] = (-a22 * a01 + a02 * a21) * det;
    out[2] = (a12 * a01 - a02 * a11) * det;
    out[3] = b11 * det;
    out[4] = (a22 * a00 - a02 * a20) * det;
    out[5] = (-a12 * a00 + a02 * a10) * det;
    out[6] = b21 * det;
    out[7] = (-a21 * a00 + a01 * a20) * det;
    out[8] = (a11 * a00 - a01 * a10) * det;
    return out;
  }
  function determinant(a2) {
    var a00 = a2[0], a01 = a2[1], a02 = a2[2];
    var a10 = a2[3], a11 = a2[4], a12 = a2[5];
    var a20 = a2[6], a21 = a2[7], a22 = a2[8];
    return a00 * (a22 * a11 - a12 * a21) + a01 * (-a22 * a10 + a12 * a20) + a02 * (a21 * a10 - a11 * a20);
  }
  function multiply(out, a2, b) {
    var a00 = a2[0], a01 = a2[1], a02 = a2[2];
    var a10 = a2[3], a11 = a2[4], a12 = a2[5];
    var a20 = a2[6], a21 = a2[7], a22 = a2[8];
    var b00 = b[0], b01 = b[1], b02 = b[2];
    var b10 = b[3], b11 = b[4], b12 = b[5];
    var b20 = b[6], b21 = b[7], b22 = b[8];
    out[0] = b00 * a00 + b01 * a10 + b02 * a20;
    out[1] = b00 * a01 + b01 * a11 + b02 * a21;
    out[2] = b00 * a02 + b01 * a12 + b02 * a22;
    out[3] = b10 * a00 + b11 * a10 + b12 * a20;
    out[4] = b10 * a01 + b11 * a11 + b12 * a21;
    out[5] = b10 * a02 + b11 * a12 + b12 * a22;
    out[6] = b20 * a00 + b21 * a10 + b22 * a20;
    out[7] = b20 * a01 + b21 * a11 + b22 * a21;
    out[8] = b20 * a02 + b21 * a12 + b22 * a22;
    return out;
  }
  function translate(out, a2, v) {
    var a00 = a2[0], a01 = a2[1], a02 = a2[2], a10 = a2[3], a11 = a2[4], a12 = a2[5], a20 = a2[6], a21 = a2[7], a22 = a2[8], x = v[0], y = v[1];
    out[0] = a00;
    out[1] = a01;
    out[2] = a02;
    out[3] = a10;
    out[4] = a11;
    out[5] = a12;
    out[6] = x * a00 + y * a10 + a20;
    out[7] = x * a01 + y * a11 + a21;
    out[8] = x * a02 + y * a12 + a22;
    return out;
  }
  function rotate(out, a2, rad) {
    var a00 = a2[0], a01 = a2[1], a02 = a2[2], a10 = a2[3], a11 = a2[4], a12 = a2[5], a20 = a2[6], a21 = a2[7], a22 = a2[8], s2 = Math.sin(rad), c = Math.cos(rad);
    out[0] = c * a00 + s2 * a10;
    out[1] = c * a01 + s2 * a11;
    out[2] = c * a02 + s2 * a12;
    out[3] = c * a10 - s2 * a00;
    out[4] = c * a11 - s2 * a01;
    out[5] = c * a12 - s2 * a02;
    out[6] = a20;
    out[7] = a21;
    out[8] = a22;
    return out;
  }
  function scale(out, a2, v) {
    var x = v[0], y = v[1];
    out[0] = x * a2[0];
    out[1] = x * a2[1];
    out[2] = x * a2[2];
    out[3] = y * a2[3];
    out[4] = y * a2[4];
    out[5] = y * a2[5];
    out[6] = a2[6];
    out[7] = a2[7];
    out[8] = a2[8];
    return out;
  }
  function fromQuat(out, q) {
    var x = q[0], y = q[1], z = q[2], w = q[3];
    var x2 = x + x;
    var y2 = y + y;
    var z2 = z + z;
    var xx = x * x2;
    var yx = y * x2;
    var yy = y * y2;
    var zx = z * x2;
    var zy = z * y2;
    var zz = z * z2;
    var wx = w * x2;
    var wy = w * y2;
    var wz = w * z2;
    out[0] = 1 - yy - zz;
    out[3] = yx - wz;
    out[6] = zx + wy;
    out[1] = yx + wz;
    out[4] = 1 - xx - zz;
    out[7] = zy - wx;
    out[2] = zx - wy;
    out[5] = zy + wx;
    out[8] = 1 - xx - yy;
    return out;
  }
  var init_mat3 = __esm({
    "../../node_modules/gl-matrix/esm/mat3.js"() {
    }
  });

  // ../../node_modules/@math.gl/core/dist/esm/classes/matrix3.js
  function getZeroMatrix() {
    if (!ZERO_MATRIX3) {
      ZERO_MATRIX3 = new Matrix3([0, 0, 0, 0, 0, 0, 0, 0, 0]);
      Object.freeze(ZERO_MATRIX3);
    }
    return ZERO_MATRIX3;
  }
  function getIdentityMatrix() {
    if (!IDENTITY_MATRIX3) {
      IDENTITY_MATRIX3 = new Matrix3();
      Object.freeze(IDENTITY_MATRIX3);
    }
    return IDENTITY_MATRIX3;
  }
  var INDICES, IDENTITY_MATRIX, Matrix3, ZERO_MATRIX3, IDENTITY_MATRIX3;
  var init_matrix3 = __esm({
    "../../node_modules/@math.gl/core/dist/esm/classes/matrix3.js"() {
      init_matrix();
      init_validators();
      init_gl_matrix_extras();
      init_mat3();
      init_vec2();
      init_vec3();
      (function(INDICES2) {
        INDICES2[INDICES2["COL0ROW0"] = 0] = "COL0ROW0";
        INDICES2[INDICES2["COL0ROW1"] = 1] = "COL0ROW1";
        INDICES2[INDICES2["COL0ROW2"] = 2] = "COL0ROW2";
        INDICES2[INDICES2["COL1ROW0"] = 3] = "COL1ROW0";
        INDICES2[INDICES2["COL1ROW1"] = 4] = "COL1ROW1";
        INDICES2[INDICES2["COL1ROW2"] = 5] = "COL1ROW2";
        INDICES2[INDICES2["COL2ROW0"] = 6] = "COL2ROW0";
        INDICES2[INDICES2["COL2ROW1"] = 7] = "COL2ROW1";
        INDICES2[INDICES2["COL2ROW2"] = 8] = "COL2ROW2";
      })(INDICES || (INDICES = {}));
      IDENTITY_MATRIX = Object.freeze([1, 0, 0, 0, 1, 0, 0, 0, 1]);
      Matrix3 = class extends Matrix {
        static get IDENTITY() {
          return getIdentityMatrix();
        }
        static get ZERO() {
          return getZeroMatrix();
        }
        get ELEMENTS() {
          return 9;
        }
        get RANK() {
          return 3;
        }
        get INDICES() {
          return INDICES;
        }
        constructor(array, ...args) {
          super(-0, -0, -0, -0, -0, -0, -0, -0, -0);
          if (arguments.length === 1 && Array.isArray(array)) {
            this.copy(array);
          } else if (args.length > 0) {
            this.copy([array, ...args]);
          } else {
            this.identity();
          }
        }
        copy(array) {
          this[0] = array[0];
          this[1] = array[1];
          this[2] = array[2];
          this[3] = array[3];
          this[4] = array[4];
          this[5] = array[5];
          this[6] = array[6];
          this[7] = array[7];
          this[8] = array[8];
          return this.check();
        }
        identity() {
          return this.copy(IDENTITY_MATRIX);
        }
        fromObject(object) {
          return this.check();
        }
        fromQuaternion(q) {
          fromQuat(this, q);
          return this.check();
        }
        set(m00, m10, m20, m01, m11, m21, m02, m12, m22) {
          this[0] = m00;
          this[1] = m10;
          this[2] = m20;
          this[3] = m01;
          this[4] = m11;
          this[5] = m21;
          this[6] = m02;
          this[7] = m12;
          this[8] = m22;
          return this.check();
        }
        setRowMajor(m00, m01, m02, m10, m11, m12, m20, m21, m22) {
          this[0] = m00;
          this[1] = m10;
          this[2] = m20;
          this[3] = m01;
          this[4] = m11;
          this[5] = m21;
          this[6] = m02;
          this[7] = m12;
          this[8] = m22;
          return this.check();
        }
        determinant() {
          return determinant(this);
        }
        transpose() {
          transpose(this, this);
          return this.check();
        }
        invert() {
          invert(this, this);
          return this.check();
        }
        multiplyLeft(a2) {
          multiply(this, a2, this);
          return this.check();
        }
        multiplyRight(a2) {
          multiply(this, this, a2);
          return this.check();
        }
        rotate(radians) {
          rotate(this, this, radians);
          return this.check();
        }
        scale(factor) {
          if (Array.isArray(factor)) {
            scale(this, this, factor);
          } else {
            scale(this, this, [factor, factor]);
          }
          return this.check();
        }
        translate(vec) {
          translate(this, this, vec);
          return this.check();
        }
        transform(vector, result) {
          let out;
          switch (vector.length) {
            case 2:
              out = transformMat3(result || [-0, -0], vector, this);
              break;
            case 3:
              out = transformMat32(result || [-0, -0, -0], vector, this);
              break;
            case 4:
              out = vec4_transformMat3(result || [-0, -0, -0, -0], vector, this);
              break;
            default:
              throw new Error("Illegal vector");
          }
          checkVector(out, vector.length);
          return out;
        }
        transformVector(vector, result) {
          return this.transform(vector, result);
        }
        transformVector2(vector, result) {
          return this.transform(vector, result);
        }
        transformVector3(vector, result) {
          return this.transform(vector, result);
        }
      };
    }
  });

  // ../../node_modules/@math.gl/core/dist/esm/index.js
  var init_esm = __esm({
    "../../node_modules/@math.gl/core/dist/esm/index.js"() {
      init_vector3();
      init_matrix3();
    }
  });

  // src/lib/gltf-utils/gltf-constants.ts
  var COMPONENTS, BYTES;
  var init_gltf_constants = __esm({
    "src/lib/gltf-utils/gltf-constants.ts"() {
      COMPONENTS = {
        SCALAR: 1,
        VEC2: 2,
        VEC3: 3,
        VEC4: 4,
        MAT2: 4,
        MAT3: 9,
        MAT4: 16
      };
      BYTES = {
        5120: 1,
        5121: 1,
        5122: 2,
        5123: 2,
        5125: 4,
        5126: 4
      };
    }
  });

  // src/lib/extensions/KHR_texture_transform.ts
  var KHR_texture_transform_exports = {};
  __export(KHR_texture_transform_exports, {
    decode: () => decode4,
    name: () => name5
  });
  async function decode4(gltfData, options) {
    const gltfScenegraph = new GLTFScenegraph(gltfData);
    const extension = gltfScenegraph.getExtension(EXT_MESHOPT_TRANSFORM);
    if (!extension) {
      return;
    }
    const materials = gltfData.json.materials || [];
    for (let i2 = 0; i2 < materials.length; i2++) {
      transformTexCoords(i2, gltfData);
    }
  }
  function transformTexCoords(materialIndex, gltfData) {
    const processedTexCoords = [];
    const material = gltfData.json.materials?.[materialIndex];
    const baseColorTexture = material?.pbrMetallicRoughness?.baseColorTexture;
    if (baseColorTexture) {
      transformPrimitives(gltfData, materialIndex, baseColorTexture, processedTexCoords);
    }
    const emisiveTexture = material?.emissiveTexture;
    if (emisiveTexture) {
      transformPrimitives(gltfData, materialIndex, emisiveTexture, processedTexCoords);
    }
    const normalTexture = material?.normalTexture;
    if (normalTexture) {
      transformPrimitives(gltfData, materialIndex, normalTexture, processedTexCoords);
    }
    const occlusionTexture = material?.occlusionTexture;
    if (occlusionTexture) {
      transformPrimitives(gltfData, materialIndex, occlusionTexture, processedTexCoords);
    }
    const metallicRoughnessTexture = material?.pbrMetallicRoughness?.metallicRoughnessTexture;
    if (metallicRoughnessTexture) {
      transformPrimitives(gltfData, materialIndex, metallicRoughnessTexture, processedTexCoords);
    }
  }
  function transformPrimitives(gltfData, materialIndex, texture, processedTexCoords) {
    const transformParameters = getTransformParameters(texture, processedTexCoords);
    if (!transformParameters) {
      return;
    }
    const meshes = gltfData.json.meshes || [];
    for (const mesh of meshes) {
      for (const primitive of mesh.primitives) {
        const material = primitive.material;
        if (Number.isFinite(material) && materialIndex === material) {
          transformPrimitive(gltfData, primitive, transformParameters);
        }
      }
    }
  }
  function getTransformParameters(texture, processedTexCoords) {
    const textureInfo = texture.extensions?.[EXT_MESHOPT_TRANSFORM];
    const { texCoord: originalTexCoord = 0 } = texture;
    const { texCoord = originalTexCoord } = textureInfo;
    const isProcessed = processedTexCoords.findIndex(([original, newTexCoord]) => original === originalTexCoord && newTexCoord === texCoord) !== -1;
    if (!isProcessed) {
      const matrix = makeTransformationMatrix(textureInfo);
      if (originalTexCoord !== texCoord) {
        texture.texCoord = texCoord;
      }
      processedTexCoords.push([originalTexCoord, texCoord]);
      return { originalTexCoord, texCoord, matrix };
    }
    return null;
  }
  function transformPrimitive(gltfData, primitive, transformParameters) {
    const { originalTexCoord, texCoord, matrix } = transformParameters;
    const texCoordAccessor = primitive.attributes[`TEXCOORD_${originalTexCoord}`];
    if (Number.isFinite(texCoordAccessor)) {
      const accessor = gltfData.json.accessors?.[texCoordAccessor];
      if (accessor && accessor.bufferView) {
        const bufferView = gltfData.json.bufferViews?.[accessor.bufferView];
        if (bufferView) {
          const { arrayBuffer, byteOffset: bufferByteOffset } = gltfData.buffers[bufferView.buffer];
          const byteOffset = (bufferByteOffset || 0) + (accessor.byteOffset || 0) + (bufferView.byteOffset || 0);
          const { ArrayType, length } = getAccessorArrayTypeAndLength(accessor, bufferView);
          const bytes = BYTES[accessor.componentType];
          const components = COMPONENTS[accessor.type];
          const elementAddressScale = bufferView.byteStride || bytes * components;
          const result = new Float32Array(length);
          for (let i2 = 0; i2 < accessor.count; i2++) {
            const uv = new ArrayType(arrayBuffer, byteOffset + i2 * elementAddressScale, 2);
            scratchVector.set(uv[0], uv[1], 1);
            scratchVector.transformByMatrix3(matrix);
            result.set([scratchVector[0], scratchVector[1]], i2 * components);
          }
          if (originalTexCoord === texCoord) {
            updateGltf(accessor, bufferView, gltfData.buffers, result);
          } else {
            createAttribute(texCoord, accessor, primitive, gltfData, result);
          }
        }
      }
    }
  }
  function updateGltf(accessor, bufferView, buffers, newTexCoordArray) {
    accessor.componentType = 5126;
    buffers.push({
      arrayBuffer: newTexCoordArray.buffer,
      byteOffset: 0,
      byteLength: newTexCoordArray.buffer.byteLength
    });
    bufferView.buffer = buffers.length - 1;
    bufferView.byteLength = newTexCoordArray.buffer.byteLength;
    bufferView.byteOffset = 0;
    delete bufferView.byteStride;
  }
  function createAttribute(newTexCoord, originalAccessor, primitive, gltfData, newTexCoordArray) {
    gltfData.buffers.push({
      arrayBuffer: newTexCoordArray.buffer,
      byteOffset: 0,
      byteLength: newTexCoordArray.buffer.byteLength
    });
    const bufferViews = gltfData.json.bufferViews;
    if (!bufferViews) {
      return;
    }
    bufferViews.push({
      buffer: gltfData.buffers.length - 1,
      byteLength: newTexCoordArray.buffer.byteLength,
      byteOffset: 0
    });
    const accessors = gltfData.json.accessors;
    if (!accessors) {
      return;
    }
    accessors.push({
      bufferView: bufferViews?.length - 1,
      byteOffset: 0,
      componentType: 5126,
      count: originalAccessor.count,
      type: "VEC2"
    });
    primitive.attributes[`TEXCOORD_${newTexCoord}`] = accessors.length - 1;
  }
  function makeTransformationMatrix(extensionData) {
    const { offset = [0, 0], rotation = 0, scale: scale2 = [1, 1] } = extensionData;
    const translationMatirx = new Matrix3().set(1, 0, 0, 0, 1, 0, offset[0], offset[1], 1);
    const rotationMatirx = scratchRotationMatrix.set(Math.cos(rotation), Math.sin(rotation), 0, -Math.sin(rotation), Math.cos(rotation), 0, 0, 0, 1);
    const scaleMatrix = scratchScaleMatrix.set(scale2[0], 0, 0, 0, scale2[1], 0, 0, 0, 1);
    return translationMatirx.multiplyRight(rotationMatirx).multiplyRight(scaleMatrix);
  }
  var EXT_MESHOPT_TRANSFORM, name5, scratchVector, scratchRotationMatrix, scratchScaleMatrix;
  var init_KHR_texture_transform = __esm({
    "src/lib/extensions/KHR_texture_transform.ts"() {
      init_esm();
      init_gltf_utils();
      init_gltf_constants();
      init_gltf_scenegraph();
      EXT_MESHOPT_TRANSFORM = "KHR_texture_transform";
      name5 = EXT_MESHOPT_TRANSFORM;
      scratchVector = new Vector3();
      scratchRotationMatrix = new Matrix3();
      scratchScaleMatrix = new Matrix3();
    }
  });

  // src/lib/extensions/deprecated/KHR_lights_punctual.ts
  var KHR_lights_punctual_exports = {};
  __export(KHR_lights_punctual_exports, {
    decode: () => decode5,
    encode: () => encode2,
    name: () => name6
  });
  async function decode5(gltfData) {
    const gltfScenegraph = new GLTFScenegraph(gltfData);
    const { json } = gltfScenegraph;
    const extension = gltfScenegraph.getExtension(KHR_LIGHTS_PUNCTUAL);
    if (extension) {
      gltfScenegraph.json.lights = extension.lights;
      gltfScenegraph.removeExtension(KHR_LIGHTS_PUNCTUAL);
    }
    for (const node of json.nodes || []) {
      const nodeExtension = gltfScenegraph.getObjectExtension(node, KHR_LIGHTS_PUNCTUAL);
      if (nodeExtension) {
        node.light = nodeExtension.light;
      }
      gltfScenegraph.removeObjectExtension(node, KHR_LIGHTS_PUNCTUAL);
    }
  }
  async function encode2(gltfData) {
    const gltfScenegraph = new GLTFScenegraph(gltfData);
    const { json } = gltfScenegraph;
    if (json.lights) {
      const extension = gltfScenegraph.addExtension(KHR_LIGHTS_PUNCTUAL);
      assert3(!extension.lights);
      extension.lights = json.lights;
      delete json.lights;
    }
    if (gltfScenegraph.json.lights) {
      for (const light of gltfScenegraph.json.lights) {
        const node = light.node;
        gltfScenegraph.addObjectExtension(node, KHR_LIGHTS_PUNCTUAL, light);
      }
      delete gltfScenegraph.json.lights;
    }
  }
  var KHR_LIGHTS_PUNCTUAL, name6;
  var init_KHR_lights_punctual = __esm({
    "src/lib/extensions/deprecated/KHR_lights_punctual.ts"() {
      init_assert3();
      init_gltf_scenegraph();
      KHR_LIGHTS_PUNCTUAL = "KHR_lights_punctual";
      name6 = KHR_LIGHTS_PUNCTUAL;
    }
  });

  // src/lib/extensions/deprecated/KHR_materials_unlit.ts
  var KHR_materials_unlit_exports = {};
  __export(KHR_materials_unlit_exports, {
    decode: () => decode6,
    encode: () => encode3,
    name: () => name7
  });
  async function decode6(gltfData) {
    const gltfScenegraph = new GLTFScenegraph(gltfData);
    const { json } = gltfScenegraph;
    for (const material of json.materials || []) {
      const extension = material.extensions && material.extensions.KHR_materials_unlit;
      if (extension) {
        material.unlit = true;
      }
      gltfScenegraph.removeObjectExtension(material, KHR_MATERIALS_UNLIT);
    }
    gltfScenegraph.removeExtension(KHR_MATERIALS_UNLIT);
  }
  function encode3(gltfData) {
    const gltfScenegraph = new GLTFScenegraph(gltfData);
    const { json } = gltfScenegraph;
    if (gltfScenegraph.materials) {
      for (const material of json.materials || []) {
        if (material.unlit) {
          delete material.unlit;
          gltfScenegraph.addObjectExtension(material, KHR_MATERIALS_UNLIT, {});
          gltfScenegraph.addExtension(KHR_MATERIALS_UNLIT);
        }
      }
    }
  }
  var KHR_MATERIALS_UNLIT, name7;
  var init_KHR_materials_unlit = __esm({
    "src/lib/extensions/deprecated/KHR_materials_unlit.ts"() {
      init_gltf_scenegraph();
      KHR_MATERIALS_UNLIT = "KHR_materials_unlit";
      name7 = KHR_MATERIALS_UNLIT;
    }
  });

  // src/lib/extensions/deprecated/KHR_techniques_webgl.ts
  var KHR_techniques_webgl_exports = {};
  __export(KHR_techniques_webgl_exports, {
    decode: () => decode7,
    encode: () => encode4,
    name: () => name8
  });
  async function decode7(gltfData) {
    const gltfScenegraph = new GLTFScenegraph(gltfData);
    const { json } = gltfScenegraph;
    const extension = gltfScenegraph.getExtension(KHR_TECHNIQUES_WEBGL);
    if (extension) {
      const techniques = resolveTechniques(extension, gltfScenegraph);
      for (const material of json.materials || []) {
        const materialExtension = gltfScenegraph.getObjectExtension(material, KHR_TECHNIQUES_WEBGL);
        if (materialExtension) {
          material.technique = Object.assign({}, materialExtension, techniques[materialExtension.technique]);
          material.technique.values = resolveValues(material.technique, gltfScenegraph);
        }
        gltfScenegraph.removeObjectExtension(material, KHR_TECHNIQUES_WEBGL);
      }
      gltfScenegraph.removeExtension(KHR_TECHNIQUES_WEBGL);
    }
  }
  async function encode4(gltfData, options) {
  }
  function resolveTechniques(techniquesExtension, gltfScenegraph) {
    const { programs = [], shaders = [], techniques = [] } = techniquesExtension;
    const textDecoder = new TextDecoder();
    shaders.forEach((shader) => {
      if (Number.isFinite(shader.bufferView)) {
        shader.code = textDecoder.decode(gltfScenegraph.getTypedArrayForBufferView(shader.bufferView));
      } else {
        throw new Error("KHR_techniques_webgl: no shader code");
      }
    });
    programs.forEach((program) => {
      program.fragmentShader = shaders[program.fragmentShader];
      program.vertexShader = shaders[program.vertexShader];
    });
    techniques.forEach((technique) => {
      technique.program = programs[technique.program];
    });
    return techniques;
  }
  function resolveValues(technique, gltfScenegraph) {
    const values = Object.assign({}, technique.values);
    Object.keys(technique.uniforms || {}).forEach((uniform) => {
      if (technique.uniforms[uniform].value && !(uniform in values)) {
        values[uniform] = technique.uniforms[uniform].value;
      }
    });
    Object.keys(values).forEach((uniform) => {
      if (typeof values[uniform] === "object" && values[uniform].index !== void 0) {
        values[uniform].texture = gltfScenegraph.getTexture(values[uniform].index);
      }
    });
    return values;
  }
  var KHR_TECHNIQUES_WEBGL, name8;
  var init_KHR_techniques_webgl = __esm({
    "src/lib/extensions/deprecated/KHR_techniques_webgl.ts"() {
      init_gltf_scenegraph();
      KHR_TECHNIQUES_WEBGL = "KHR_techniques_webgl";
      name8 = KHR_TECHNIQUES_WEBGL;
    }
  });

  // src/lib/extensions/deprecated/EXT_feature_metadata.ts
  var EXT_feature_metadata_exports = {};
  __export(EXT_feature_metadata_exports, {
    decode: () => decode8,
    name: () => name9
  });
  async function decode8(gltfData) {
    const scenegraph = new GLTFScenegraph(gltfData);
    decodeExtFeatureMetadata(scenegraph);
  }
  function decodeExtFeatureMetadata(scenegraph) {
    const extension = scenegraph.getExtension(EXT_FEATURE_METADATA);
    const schemaClasses = extension?.schema?.classes;
    const featureTables = extension?.featureTables;
    const featureTextures = extension?.featureTextures;
    if (featureTextures) {
      console.warn('featureTextures is not yet supported in the "EXT_feature_metadata" extension.');
    }
    if (schemaClasses && featureTables) {
      for (const schemaName in schemaClasses) {
        const schemaClass = schemaClasses[schemaName];
        const featureTable = findFeatureTableByName(featureTables, schemaName);
        if (featureTable) {
          handleFeatureTableProperties(scenegraph, featureTable, schemaClass);
        }
      }
    }
  }
  function handleFeatureTableProperties(scenegraph, featureTable, schemaClass) {
    for (const propertyName in schemaClass.properties) {
      const schemaProperty = schemaClass.properties[propertyName];
      const featureTableProperty = featureTable?.properties?.[propertyName];
      const numberOfFeatures = featureTable.count;
      if (featureTableProperty) {
        const data = getPropertyDataFromBinarySource(scenegraph, schemaProperty, numberOfFeatures, featureTableProperty);
        featureTableProperty.data = data;
      }
    }
  }
  function getPropertyDataFromBinarySource(scenegraph, schemaProperty, numberOfFeatures, featureTableProperty) {
    const bufferView = featureTableProperty.bufferView;
    let data = scenegraph.getTypedArrayForBufferView(bufferView);
    switch (schemaProperty.type) {
      case "STRING": {
        const stringOffsetBufferView = featureTableProperty.stringOffsetBufferView;
        const offsetsData = scenegraph.getTypedArrayForBufferView(stringOffsetBufferView);
        data = getStringAttributes(data, offsetsData, numberOfFeatures);
        break;
      }
      default:
    }
    return data;
  }
  function findFeatureTableByName(featureTables, schemaClassName) {
    for (const featureTableName in featureTables) {
      const featureTable = featureTables[featureTableName];
      if (featureTable.class === schemaClassName) {
        return featureTable;
      }
    }
    return null;
  }
  function getStringAttributes(data, offsetsData, stringsCount) {
    const stringsArray = [];
    const textDecoder = new TextDecoder("utf8");
    let stringOffset = 0;
    const bytesPerStringSize = 4;
    for (let index = 0; index < stringsCount; index++) {
      const stringByteSize = offsetsData[(index + 1) * bytesPerStringSize] - offsetsData[index * bytesPerStringSize];
      const stringData = data.subarray(stringOffset, stringByteSize + stringOffset);
      const stringAttribute = textDecoder.decode(stringData);
      stringsArray.push(stringAttribute);
      stringOffset += stringByteSize;
    }
    return stringsArray;
  }
  var EXT_FEATURE_METADATA, name9;
  var init_EXT_feature_metadata = __esm({
    "src/lib/extensions/deprecated/EXT_feature_metadata.ts"() {
      init_gltf_scenegraph();
      EXT_FEATURE_METADATA = "EXT_feature_metadata";
      name9 = EXT_FEATURE_METADATA;
    }
  });

  // src/lib/api/gltf-extensions.ts
  function preprocessExtensions(gltf, options = {}, context) {
    const extensions = EXTENSIONS2.filter((extension) => useExtension(extension.name, options));
    for (const extension of extensions) {
      extension.preprocess?.(gltf, options, context);
    }
  }
  async function decodeExtensions(gltf, options = {}, context) {
    const extensions = EXTENSIONS2.filter((extension) => useExtension(extension.name, options));
    for (const extension of extensions) {
      await extension.decode?.(gltf, options, context);
    }
  }
  function useExtension(extensionName, options) {
    const excludes = options?.gltf?.excludeExtensions || {};
    const exclude = extensionName in excludes && !excludes[extensionName];
    return !exclude;
  }
  var EXTENSIONS2;
  var init_gltf_extensions = __esm({
    "src/lib/api/gltf-extensions.ts"() {
      init_EXT_meshopt_compression();
      init_EXT_texture_webp();
      init_KHR_texture_basisu();
      init_KHR_draco_mesh_compression();
      init_KHR_texture_transform();
      init_KHR_lights_punctual();
      init_KHR_materials_unlit();
      init_KHR_techniques_webgl();
      init_EXT_feature_metadata();
      EXTENSIONS2 = [
        EXT_meshopt_compression_exports,
        EXT_texture_webp_exports,
        KHR_texture_basisu_exports,
        KHR_draco_mesh_compression_exports,
        KHR_lights_punctual_exports,
        KHR_materials_unlit_exports,
        KHR_techniques_webgl_exports,
        KHR_texture_transform_exports,
        EXT_feature_metadata_exports
      ];
    }
  });

  // src/lib/extensions/KHR_binary_gltf.ts
  function preprocess4(gltfData) {
    const gltfScenegraph = new GLTFScenegraph(gltfData);
    const { json } = gltfScenegraph;
    for (const image of json.images || []) {
      const extension = gltfScenegraph.getObjectExtension(image, KHR_BINARY_GLTF);
      if (extension) {
        Object.assign(image, extension);
      }
      gltfScenegraph.removeObjectExtension(image, KHR_BINARY_GLTF);
    }
    if (json.buffers && json.buffers[0]) {
      delete json.buffers[0].uri;
    }
    gltfScenegraph.removeExtension(KHR_BINARY_GLTF);
  }
  var KHR_BINARY_GLTF;
  var init_KHR_binary_gltf = __esm({
    "src/lib/extensions/KHR_binary_gltf.ts"() {
      init_gltf_scenegraph();
      KHR_BINARY_GLTF = "KHR_binary_glTF";
    }
  });

  // src/lib/api/normalize-gltf-v1.ts
  function normalizeGLTFV1(gltf, options = {}) {
    return new GLTFV1Normalizer().normalize(gltf, options);
  }
  var GLTF_ARRAYS, GLTF_KEYS, GLTFV1Normalizer;
  var init_normalize_gltf_v1 = __esm({
    "src/lib/api/normalize-gltf-v1.ts"() {
      init_KHR_binary_gltf();
      GLTF_ARRAYS = {
        accessors: "accessor",
        animations: "animation",
        buffers: "buffer",
        bufferViews: "bufferView",
        images: "image",
        materials: "material",
        meshes: "mesh",
        nodes: "node",
        samplers: "sampler",
        scenes: "scene",
        skins: "skin",
        textures: "texture"
      };
      GLTF_KEYS = {
        accessor: "accessors",
        animations: "animation",
        buffer: "buffers",
        bufferView: "bufferViews",
        image: "images",
        material: "materials",
        mesh: "meshes",
        node: "nodes",
        sampler: "samplers",
        scene: "scenes",
        skin: "skins",
        texture: "textures"
      };
      GLTFV1Normalizer = class {
        constructor() {
          this.idToIndexMap = {
            animations: {},
            accessors: {},
            buffers: {},
            bufferViews: {},
            images: {},
            materials: {},
            meshes: {},
            nodes: {},
            samplers: {},
            scenes: {},
            skins: {},
            textures: {}
          };
        }
        normalize(gltf, options) {
          this.json = gltf.json;
          const json = gltf.json;
          switch (json.asset && json.asset.version) {
            case "2.0":
              return;
            case void 0:
            case "1.0":
              break;
            default:
              console.warn(`glTF: Unknown version ${json.asset.version}`);
              return;
          }
          if (!options.normalize) {
            throw new Error("glTF v1 is not supported.");
          }
          console.warn("Converting glTF v1 to glTF v2 format. This is experimental and may fail.");
          this._addAsset(json);
          this._convertTopLevelObjectsToArrays(json);
          preprocess4(gltf);
          this._convertObjectIdsToArrayIndices(json);
          this._updateObjects(json);
          this._updateMaterial(json);
        }
        _addAsset(json) {
          json.asset = json.asset || {};
          json.asset.version = "2.0";
          json.asset.generator = json.asset.generator || "Normalized to glTF 2.0 by loaders.gl";
        }
        _convertTopLevelObjectsToArrays(json) {
          for (const arrayName in GLTF_ARRAYS) {
            this._convertTopLevelObjectToArray(json, arrayName);
          }
        }
        _convertTopLevelObjectToArray(json, mapName) {
          const objectMap = json[mapName];
          if (!objectMap || Array.isArray(objectMap)) {
            return;
          }
          json[mapName] = [];
          for (const id in objectMap) {
            const object = objectMap[id];
            object.id = object.id || id;
            const index = json[mapName].length;
            json[mapName].push(object);
            this.idToIndexMap[mapName][id] = index;
          }
        }
        _convertObjectIdsToArrayIndices(json) {
          for (const arrayName in GLTF_ARRAYS) {
            this._convertIdsToIndices(json, arrayName);
          }
          if ("scene" in json) {
            json.scene = this._convertIdToIndex(json.scene, "scene");
          }
          for (const texture of json.textures) {
            this._convertTextureIds(texture);
          }
          for (const mesh of json.meshes) {
            this._convertMeshIds(mesh);
          }
          for (const node of json.nodes) {
            this._convertNodeIds(node);
          }
          for (const node of json.scenes) {
            this._convertSceneIds(node);
          }
        }
        _convertTextureIds(texture) {
          if (texture.source) {
            texture.source = this._convertIdToIndex(texture.source, "image");
          }
        }
        _convertMeshIds(mesh) {
          for (const primitive of mesh.primitives) {
            const { attributes, indices, material } = primitive;
            for (const attributeName in attributes) {
              attributes[attributeName] = this._convertIdToIndex(attributes[attributeName], "accessor");
            }
            if (indices) {
              primitive.indices = this._convertIdToIndex(indices, "accessor");
            }
            if (material) {
              primitive.material = this._convertIdToIndex(material, "material");
            }
          }
        }
        _convertNodeIds(node) {
          if (node.children) {
            node.children = node.children.map((child) => this._convertIdToIndex(child, "node"));
          }
          if (node.meshes) {
            node.meshes = node.meshes.map((mesh) => this._convertIdToIndex(mesh, "mesh"));
          }
        }
        _convertSceneIds(scene) {
          if (scene.nodes) {
            scene.nodes = scene.nodes.map((node) => this._convertIdToIndex(node, "node"));
          }
        }
        _convertIdsToIndices(json, topLevelArrayName) {
          if (!json[topLevelArrayName]) {
            console.warn(`gltf v1: json doesn't contain attribute ${topLevelArrayName}`);
            json[topLevelArrayName] = [];
          }
          for (const object of json[topLevelArrayName]) {
            for (const key in object) {
              const id = object[key];
              const index = this._convertIdToIndex(id, key);
              object[key] = index;
            }
          }
        }
        _convertIdToIndex(id, key) {
          const arrayName = GLTF_KEYS[key];
          if (arrayName in this.idToIndexMap) {
            const index = this.idToIndexMap[arrayName][id];
            if (!Number.isFinite(index)) {
              throw new Error(`gltf v1: failed to resolve ${key} with id ${id}`);
            }
            return index;
          }
          return id;
        }
        _updateObjects(json) {
          for (const buffer of this.json.buffers) {
            delete buffer.type;
          }
        }
        _updateMaterial(json) {
          for (const material of json.materials) {
            material.pbrMetallicRoughness = {
              baseColorFactor: [1, 1, 1, 1],
              metallicFactor: 1,
              roughnessFactor: 1
            };
            const textureId = material.values?.tex || material.values?.texture2d_0 || material.values?.diffuseTex;
            const textureIndex = json.textures.findIndex((texture) => texture.id === textureId);
            if (textureIndex !== -1) {
              material.pbrMetallicRoughness.baseColorTexture = { index: textureIndex };
            }
          }
        }
      };
    }
  });

  // src/lib/api/post-process-gltf.ts
  function getBytesFromComponentType(componentType) {
    return BYTES2[componentType];
  }
  function getSizeFromAccessorType(type) {
    return COMPONENTS2[type];
  }
  function postProcessGLTF(gltf, options) {
    return new GLTFPostProcessor().postProcess(gltf, options);
  }
  var COMPONENTS2, BYTES2, GL_SAMPLER, SAMPLER_PARAMETER_GLTF_TO_GL, DEFAULT_SAMPLER, GLTFPostProcessor;
  var init_post_process_gltf = __esm({
    "src/lib/api/post-process-gltf.ts"() {
      init_assert3();
      init_gltf_utils();
      COMPONENTS2 = {
        SCALAR: 1,
        VEC2: 2,
        VEC3: 3,
        VEC4: 4,
        MAT2: 4,
        MAT3: 9,
        MAT4: 16
      };
      BYTES2 = {
        5120: 1,
        5121: 1,
        5122: 2,
        5123: 2,
        5125: 4,
        5126: 4
      };
      GL_SAMPLER = {
        TEXTURE_MAG_FILTER: 10240,
        TEXTURE_MIN_FILTER: 10241,
        TEXTURE_WRAP_S: 10242,
        TEXTURE_WRAP_T: 10243,
        REPEAT: 10497,
        LINEAR: 9729,
        NEAREST_MIPMAP_LINEAR: 9986
      };
      SAMPLER_PARAMETER_GLTF_TO_GL = {
        magFilter: GL_SAMPLER.TEXTURE_MAG_FILTER,
        minFilter: GL_SAMPLER.TEXTURE_MIN_FILTER,
        wrapS: GL_SAMPLER.TEXTURE_WRAP_S,
        wrapT: GL_SAMPLER.TEXTURE_WRAP_T
      };
      DEFAULT_SAMPLER = {
        [GL_SAMPLER.TEXTURE_MAG_FILTER]: GL_SAMPLER.LINEAR,
        [GL_SAMPLER.TEXTURE_MIN_FILTER]: GL_SAMPLER.NEAREST_MIPMAP_LINEAR,
        [GL_SAMPLER.TEXTURE_WRAP_S]: GL_SAMPLER.REPEAT,
        [GL_SAMPLER.TEXTURE_WRAP_T]: GL_SAMPLER.REPEAT
      };
      GLTFPostProcessor = class {
        constructor() {
          this.baseUri = "";
          this.json = {};
          this.buffers = [];
          this.images = [];
        }
        postProcess(gltf, options = {}) {
          const { json, buffers = [], images = [], baseUri = "" } = gltf;
          assert3(json);
          this.baseUri = baseUri;
          this.json = json;
          this.buffers = buffers;
          this.images = images;
          this._resolveTree(this.json, options);
          return this.json;
        }
        _resolveTree(json, options = {}) {
          if (json.bufferViews) {
            json.bufferViews = json.bufferViews.map((bufView, i2) => this._resolveBufferView(bufView, i2));
          }
          if (json.images) {
            json.images = json.images.map((image, i2) => this._resolveImage(image, i2));
          }
          if (json.samplers) {
            json.samplers = json.samplers.map((sampler, i2) => this._resolveSampler(sampler, i2));
          }
          if (json.textures) {
            json.textures = json.textures.map((texture, i2) => this._resolveTexture(texture, i2));
          }
          if (json.accessors) {
            json.accessors = json.accessors.map((accessor, i2) => this._resolveAccessor(accessor, i2));
          }
          if (json.materials) {
            json.materials = json.materials.map((material, i2) => this._resolveMaterial(material, i2));
          }
          if (json.meshes) {
            json.meshes = json.meshes.map((mesh, i2) => this._resolveMesh(mesh, i2));
          }
          if (json.nodes) {
            json.nodes = json.nodes.map((node, i2) => this._resolveNode(node, i2));
          }
          if (json.skins) {
            json.skins = json.skins.map((skin, i2) => this._resolveSkin(skin, i2));
          }
          if (json.scenes) {
            json.scenes = json.scenes.map((scene, i2) => this._resolveScene(scene, i2));
          }
          if (json.scene !== void 0) {
            json.scene = json.scenes[this.json.scene];
          }
        }
        getScene(index) {
          return this._get("scenes", index);
        }
        getNode(index) {
          return this._get("nodes", index);
        }
        getSkin(index) {
          return this._get("skins", index);
        }
        getMesh(index) {
          return this._get("meshes", index);
        }
        getMaterial(index) {
          return this._get("materials", index);
        }
        getAccessor(index) {
          return this._get("accessors", index);
        }
        getCamera(index) {
          return null;
        }
        getTexture(index) {
          return this._get("textures", index);
        }
        getSampler(index) {
          return this._get("samplers", index);
        }
        getImage(index) {
          return this._get("images", index);
        }
        getBufferView(index) {
          return this._get("bufferViews", index);
        }
        getBuffer(index) {
          return this._get("buffers", index);
        }
        _get(array, index) {
          if (typeof index === "object") {
            return index;
          }
          const object = this.json[array] && this.json[array][index];
          if (!object) {
            console.warn(`glTF file error: Could not find ${array}[${index}]`);
          }
          return object;
        }
        _resolveScene(scene, index) {
          scene.id = scene.id || `scene-${index}`;
          scene.nodes = (scene.nodes || []).map((node) => this.getNode(node));
          return scene;
        }
        _resolveNode(node, index) {
          node.id = node.id || `node-${index}`;
          if (node.children) {
            node.children = node.children.map((child) => this.getNode(child));
          }
          if (node.mesh !== void 0) {
            node.mesh = this.getMesh(node.mesh);
          } else if (node.meshes !== void 0 && node.meshes.length) {
            node.mesh = node.meshes.reduce((accum, meshIndex) => {
              const mesh = this.getMesh(meshIndex);
              accum.id = mesh.id;
              accum.primitives = accum.primitives.concat(mesh.primitives);
              return accum;
            }, { primitives: [] });
          }
          if (node.camera !== void 0) {
            node.camera = this.getCamera(node.camera);
          }
          if (node.skin !== void 0) {
            node.skin = this.getSkin(node.skin);
          }
          return node;
        }
        _resolveSkin(skin, index) {
          skin.id = skin.id || `skin-${index}`;
          skin.inverseBindMatrices = this.getAccessor(skin.inverseBindMatrices);
          return skin;
        }
        _resolveMesh(mesh, index) {
          mesh.id = mesh.id || `mesh-${index}`;
          if (mesh.primitives) {
            mesh.primitives = mesh.primitives.map((primitive) => {
              primitive = { ...primitive };
              const attributes = primitive.attributes;
              primitive.attributes = {};
              for (const attribute in attributes) {
                primitive.attributes[attribute] = this.getAccessor(attributes[attribute]);
              }
              if (primitive.indices !== void 0) {
                primitive.indices = this.getAccessor(primitive.indices);
              }
              if (primitive.material !== void 0) {
                primitive.material = this.getMaterial(primitive.material);
              }
              return primitive;
            });
          }
          return mesh;
        }
        _resolveMaterial(material, index) {
          material.id = material.id || `material-${index}`;
          if (material.normalTexture) {
            material.normalTexture = { ...material.normalTexture };
            material.normalTexture.texture = this.getTexture(material.normalTexture.index);
          }
          if (material.occlusionTexture) {
            material.occlustionTexture = { ...material.occlustionTexture };
            material.occlusionTexture.texture = this.getTexture(material.occlusionTexture.index);
          }
          if (material.emissiveTexture) {
            material.emmisiveTexture = { ...material.emmisiveTexture };
            material.emissiveTexture.texture = this.getTexture(material.emissiveTexture.index);
          }
          if (!material.emissiveFactor) {
            material.emissiveFactor = material.emmisiveTexture ? [1, 1, 1] : [0, 0, 0];
          }
          if (material.pbrMetallicRoughness) {
            material.pbrMetallicRoughness = { ...material.pbrMetallicRoughness };
            const mr = material.pbrMetallicRoughness;
            if (mr.baseColorTexture) {
              mr.baseColorTexture = { ...mr.baseColorTexture };
              mr.baseColorTexture.texture = this.getTexture(mr.baseColorTexture.index);
            }
            if (mr.metallicRoughnessTexture) {
              mr.metallicRoughnessTexture = { ...mr.metallicRoughnessTexture };
              mr.metallicRoughnessTexture.texture = this.getTexture(mr.metallicRoughnessTexture.index);
            }
          }
          return material;
        }
        _resolveAccessor(accessor, index) {
          accessor.id = accessor.id || `accessor-${index}`;
          if (accessor.bufferView !== void 0) {
            accessor.bufferView = this.getBufferView(accessor.bufferView);
          }
          accessor.bytesPerComponent = getBytesFromComponentType(accessor.componentType);
          accessor.components = getSizeFromAccessorType(accessor.type);
          accessor.bytesPerElement = accessor.bytesPerComponent * accessor.components;
          if (accessor.bufferView) {
            const buffer = accessor.bufferView.buffer;
            const { ArrayType, byteLength } = getAccessorArrayTypeAndLength(accessor, accessor.bufferView);
            const byteOffset = (accessor.bufferView.byteOffset || 0) + (accessor.byteOffset || 0) + buffer.byteOffset;
            let cutBuffer = buffer.arrayBuffer.slice(byteOffset, byteOffset + byteLength);
            if (accessor.bufferView.byteStride) {
              cutBuffer = this._getValueFromInterleavedBuffer(buffer, byteOffset, accessor.bufferView.byteStride, accessor.bytesPerElement, accessor.count);
            }
            accessor.value = new ArrayType(cutBuffer);
          }
          return accessor;
        }
        _getValueFromInterleavedBuffer(buffer, byteOffset, byteStride, bytesPerElement, count) {
          const result = new Uint8Array(count * bytesPerElement);
          for (let i2 = 0; i2 < count; i2++) {
            const elementOffset = byteOffset + i2 * byteStride;
            result.set(new Uint8Array(buffer.arrayBuffer.slice(elementOffset, elementOffset + bytesPerElement)), i2 * bytesPerElement);
          }
          return result.buffer;
        }
        _resolveTexture(texture, index) {
          texture.id = texture.id || `texture-${index}`;
          texture.sampler = "sampler" in texture ? this.getSampler(texture.sampler) : DEFAULT_SAMPLER;
          texture.source = this.getImage(texture.source);
          return texture;
        }
        _resolveSampler(sampler, index) {
          sampler.id = sampler.id || `sampler-${index}`;
          sampler.parameters = {};
          for (const key in sampler) {
            const glEnum = this._enumSamplerParameter(key);
            if (glEnum !== void 0) {
              sampler.parameters[glEnum] = sampler[key];
            }
          }
          return sampler;
        }
        _enumSamplerParameter(key) {
          return SAMPLER_PARAMETER_GLTF_TO_GL[key];
        }
        _resolveImage(image, index) {
          image.id = image.id || `image-${index}`;
          if (image.bufferView !== void 0) {
            image.bufferView = this.getBufferView(image.bufferView);
          }
          const preloadedImage = this.images[index];
          if (preloadedImage) {
            image.image = preloadedImage;
          }
          return image;
        }
        _resolveBufferView(bufferView, index) {
          const bufferIndex = bufferView.buffer;
          const result = {
            id: `bufferView-${index}`,
            ...bufferView,
            buffer: this.buffers[bufferIndex]
          };
          const arrayBuffer = this.buffers[bufferIndex].arrayBuffer;
          let byteOffset = this.buffers[bufferIndex].byteOffset || 0;
          if ("byteOffset" in bufferView) {
            byteOffset += bufferView.byteOffset;
          }
          result.data = new Uint8Array(arrayBuffer, byteOffset, bufferView.byteLength);
          return result;
        }
        _resolveCamera(camera, index) {
          camera.id = camera.id || `camera-${index}`;
          if (camera.perspective) {
          }
          if (camera.orthographic) {
          }
          return camera;
        }
      };
    }
  });

  // src/lib/parsers/parse-glb.ts
  function getMagicString2(dataView, byteOffset = 0) {
    return `${String.fromCharCode(dataView.getUint8(byteOffset + 0))}${String.fromCharCode(dataView.getUint8(byteOffset + 1))}${String.fromCharCode(dataView.getUint8(byteOffset + 2))}${String.fromCharCode(dataView.getUint8(byteOffset + 3))}`;
  }
  function isGLB(arrayBuffer, byteOffset = 0, options = {}) {
    const dataView = new DataView(arrayBuffer);
    const { magic = MAGIC_glTF } = options;
    const magic1 = dataView.getUint32(byteOffset, false);
    return magic1 === magic || magic1 === MAGIC_glTF;
  }
  function parseGLBSync(glb, arrayBuffer, byteOffset = 0, options = {}) {
    const dataView = new DataView(arrayBuffer);
    const type = getMagicString2(dataView, byteOffset + 0);
    const version = dataView.getUint32(byteOffset + 4, LE);
    const byteLength = dataView.getUint32(byteOffset + 8, LE);
    Object.assign(glb, {
      header: {
        byteOffset,
        byteLength,
        hasBinChunk: false
      },
      type,
      version,
      json: {},
      binChunks: []
    });
    byteOffset += GLB_FILE_HEADER_SIZE;
    switch (glb.version) {
      case 1:
        return parseGLBV1(glb, dataView, byteOffset);
      case 2:
        return parseGLBV2(glb, dataView, byteOffset, options = {});
      default:
        throw new Error(`Invalid GLB version ${glb.version}. Only supports v1 and v2.`);
    }
  }
  function parseGLBV1(glb, dataView, byteOffset) {
    assert(glb.header.byteLength > GLB_FILE_HEADER_SIZE + GLB_CHUNK_HEADER_SIZE);
    const contentLength = dataView.getUint32(byteOffset + 0, LE);
    const contentFormat = dataView.getUint32(byteOffset + 4, LE);
    byteOffset += GLB_CHUNK_HEADER_SIZE;
    assert(contentFormat === GLB_V1_CONTENT_FORMAT_JSON);
    parseJSONChunk(glb, dataView, byteOffset, contentLength);
    byteOffset += contentLength;
    byteOffset += parseBINChunk(glb, dataView, byteOffset, glb.header.byteLength);
    return byteOffset;
  }
  function parseGLBV2(glb, dataView, byteOffset, options) {
    assert(glb.header.byteLength > GLB_FILE_HEADER_SIZE + GLB_CHUNK_HEADER_SIZE);
    parseGLBChunksSync(glb, dataView, byteOffset, options);
    return byteOffset + glb.header.byteLength;
  }
  function parseGLBChunksSync(glb, dataView, byteOffset, options) {
    while (byteOffset + 8 <= glb.header.byteLength) {
      const chunkLength = dataView.getUint32(byteOffset + 0, LE);
      const chunkFormat = dataView.getUint32(byteOffset + 4, LE);
      byteOffset += GLB_CHUNK_HEADER_SIZE;
      switch (chunkFormat) {
        case GLB_CHUNK_TYPE_JSON:
          parseJSONChunk(glb, dataView, byteOffset, chunkLength);
          break;
        case GLB_CHUNK_TYPE_BIN:
          parseBINChunk(glb, dataView, byteOffset, chunkLength);
          break;
        case GLB_CHUNK_TYPE_JSON_XVIZ_DEPRECATED:
          if (!options.strict) {
            parseJSONChunk(glb, dataView, byteOffset, chunkLength);
          }
          break;
        case GLB_CHUNK_TYPE_BIX_XVIZ_DEPRECATED:
          if (!options.strict) {
            parseBINChunk(glb, dataView, byteOffset, chunkLength);
          }
          break;
        default:
          break;
      }
      byteOffset += padToNBytes(chunkLength, 4);
    }
    return byteOffset;
  }
  function parseJSONChunk(glb, dataView, byteOffset, chunkLength) {
    const jsonChunk = new Uint8Array(dataView.buffer, byteOffset, chunkLength);
    const textDecoder = new TextDecoder("utf8");
    const jsonText = textDecoder.decode(jsonChunk);
    glb.json = JSON.parse(jsonText);
    return padToNBytes(chunkLength, 4);
  }
  function parseBINChunk(glb, dataView, byteOffset, chunkLength) {
    glb.header.hasBinChunk = true;
    glb.binChunks.push({
      byteOffset,
      byteLength: chunkLength,
      arrayBuffer: dataView.buffer
    });
    return padToNBytes(chunkLength, 4);
  }
  var MAGIC_glTF, GLB_FILE_HEADER_SIZE, GLB_CHUNK_HEADER_SIZE, GLB_CHUNK_TYPE_JSON, GLB_CHUNK_TYPE_BIN, GLB_CHUNK_TYPE_JSON_XVIZ_DEPRECATED, GLB_CHUNK_TYPE_BIX_XVIZ_DEPRECATED, GLB_V1_CONTENT_FORMAT_JSON, LE;
  var init_parse_glb = __esm({
    "src/lib/parsers/parse-glb.ts"() {
      init_src2();
      MAGIC_glTF = 1735152710;
      GLB_FILE_HEADER_SIZE = 12;
      GLB_CHUNK_HEADER_SIZE = 8;
      GLB_CHUNK_TYPE_JSON = 1313821514;
      GLB_CHUNK_TYPE_BIN = 5130562;
      GLB_CHUNK_TYPE_JSON_XVIZ_DEPRECATED = 0;
      GLB_CHUNK_TYPE_BIX_XVIZ_DEPRECATED = 1;
      GLB_V1_CONTENT_FORMAT_JSON = 0;
      LE = true;
    }
  });

  // src/lib/parsers/parse-gltf.ts
  async function parseGLTF(gltf, arrayBufferOrString, byteOffset = 0, options, context) {
    parseGLTFContainerSync(gltf, arrayBufferOrString, byteOffset, options);
    normalizeGLTFV1(gltf, { normalize: options?.gltf?.normalize });
    preprocessExtensions(gltf, options, context);
    const promises = [];
    if (options?.gltf?.loadBuffers && gltf.json.buffers) {
      await loadBuffers(gltf, options, context);
    }
    if (options?.gltf?.loadImages) {
      const promise2 = loadImages(gltf, options, context);
      promises.push(promise2);
    }
    const promise = decodeExtensions(gltf, options, context);
    promises.push(promise);
    await Promise.all(promises);
    return options?.gltf?.postProcess ? postProcessGLTF(gltf, options) : gltf;
  }
  function parseGLTFContainerSync(gltf, data, byteOffset, options) {
    if (options.uri) {
      gltf.baseUri = options.uri;
    }
    if (data instanceof ArrayBuffer && !isGLB(data, byteOffset, options)) {
      const textDecoder = new TextDecoder();
      data = textDecoder.decode(data);
    }
    if (typeof data === "string") {
      gltf.json = parseJSON(data);
    } else if (data instanceof ArrayBuffer) {
      const glb = {};
      byteOffset = parseGLBSync(glb, data, byteOffset, options.glb);
      assert3(glb.type === "glTF", `Invalid GLB magic string ${glb.type}`);
      gltf._glb = glb;
      gltf.json = glb.json;
    } else {
      assert3(false, "GLTF: must be ArrayBuffer or string");
    }
    const buffers = gltf.json.buffers || [];
    gltf.buffers = new Array(buffers.length).fill(null);
    if (gltf._glb && gltf._glb.header.hasBinChunk) {
      const { binChunks } = gltf._glb;
      gltf.buffers[0] = {
        arrayBuffer: binChunks[0].arrayBuffer,
        byteOffset: binChunks[0].byteOffset,
        byteLength: binChunks[0].byteLength
      };
    }
    const images = gltf.json.images || [];
    gltf.images = new Array(images.length).fill({});
  }
  async function loadBuffers(gltf, options, context) {
    const buffers = gltf.json.buffers || [];
    for (let i2 = 0; i2 < buffers.length; ++i2) {
      const buffer = buffers[i2];
      if (buffer.uri) {
        const { fetch: fetch2 } = context;
        assert3(fetch2);
        const uri = resolveUrl(buffer.uri, options);
        const response = await context?.fetch?.(uri);
        const arrayBuffer = await response?.arrayBuffer?.();
        gltf.buffers[i2] = {
          arrayBuffer,
          byteOffset: 0,
          byteLength: arrayBuffer.byteLength
        };
        delete buffer.uri;
      } else if (gltf.buffers[i2] === null) {
        gltf.buffers[i2] = {
          arrayBuffer: new ArrayBuffer(buffer.byteLength),
          byteOffset: 0,
          byteLength: buffer.byteLength
        };
      }
    }
  }
  async function loadImages(gltf, options, context) {
    const imageIndices = getReferencesImageIndices(gltf);
    const images = gltf.json.images || [];
    const promises = [];
    for (const imageIndex of imageIndices) {
      promises.push(loadImage(gltf, images[imageIndex], imageIndex, options, context));
    }
    return await Promise.all(promises);
  }
  function getReferencesImageIndices(gltf) {
    const imageIndices = new Set();
    const textures = gltf.json.textures || [];
    for (const texture of textures) {
      if (texture.source !== void 0) {
        imageIndices.add(texture.source);
      }
    }
    return Array.from(imageIndices).sort();
  }
  async function loadImage(gltf, image, index, options, context) {
    const { fetch: fetch2, parse: parse4 } = context;
    let arrayBuffer;
    if (image.uri && !image.hasOwnProperty("bufferView")) {
      const uri = resolveUrl(image.uri, options);
      const response = await fetch2(uri);
      arrayBuffer = await response.arrayBuffer();
      image.bufferView = {
        data: arrayBuffer
      };
    }
    if (Number.isFinite(image.bufferView)) {
      const array = getTypedArrayForBufferView(gltf.json, gltf.buffers, image.bufferView);
      arrayBuffer = sliceArrayBuffer(array.buffer, array.byteOffset, array.byteLength);
    }
    assert3(arrayBuffer, "glTF image has no data");
    let parsedImage = await parse4(arrayBuffer, [ImageLoader, BasisLoader], { mimeType: image.mimeType, basis: options.basis || { format: selectSupportedBasisFormat() } }, context);
    if (parsedImage && parsedImage[0]) {
      parsedImage = {
        compressed: true,
        mipmaps: false,
        width: parsedImage[0].width,
        height: parsedImage[0].height,
        data: parsedImage[0]
      };
    }
    gltf.images = gltf.images || [];
    gltf.images[index] = parsedImage;
  }
  var init_parse_gltf = __esm({
    "src/lib/parsers/parse-gltf.ts"() {
      init_src4();
      init_src3();
      init_src2();
      init_assert3();
      init_resolve_url();
      init_get_typed_array();
      init_gltf_extensions();
      init_normalize_gltf_v1();
      init_post_process_gltf();
      init_parse_glb();
    }
  });

  // src/gltf-loader.ts
  async function parse2(arrayBuffer, options = {}, context) {
    options = { ...GLTFLoader.options, ...options };
    options.gltf = { ...GLTFLoader.options.gltf, ...options.gltf };
    const { byteOffset = 0 } = options;
    const gltf = {};
    return await parseGLTF(gltf, arrayBuffer, byteOffset, options, context);
  }
  var GLTFLoader;
  var init_gltf_loader = __esm({
    "src/gltf-loader.ts"() {
      init_version();
      init_parse_gltf();
      GLTFLoader = {
        name: "glTF",
        id: "gltf",
        module: "gltf",
        version: VERSION,
        extensions: ["gltf", "glb"],
        mimeTypes: ["model/gltf+json", "model/gltf-binary"],
        text: true,
        binary: true,
        tests: ["glTF"],
        parse: parse2,
        options: {
          gltf: {
            normalize: true,
            loadBuffers: true,
            loadImages: true,
            decompressMeshes: true,
            postProcess: true
          },
          log: console
        },
        deprecatedOptions: {
          fetchImages: "gltf.loadImages",
          createImages: "gltf.loadImages",
          decompress: "gltf.decompressMeshes",
          postProcess: "gltf.postProcess",
          gltf: {
            decompress: "gltf.decompressMeshes"
          }
        }
      };
    }
  });

  // src/lib/encoders/encode-glb.ts
  function encodeGLBSync(glb, dataView, byteOffset = 0, options = {}) {
    const { magic = MAGIC_glTF2, version = 2, json = {}, binary } = glb;
    const byteOffsetStart = byteOffset;
    if (dataView) {
      dataView.setUint32(byteOffset + 0, magic, LE2);
      dataView.setUint32(byteOffset + 4, version, LE2);
      dataView.setUint32(byteOffset + 8, 0, LE2);
    }
    const byteOffsetFileLength = byteOffset + 8;
    byteOffset += 12;
    const byteOffsetJsonHeader = byteOffset;
    if (dataView) {
      dataView.setUint32(byteOffset + 0, 0, LE2);
      dataView.setUint32(byteOffset + 4, MAGIC_JSON, LE2);
    }
    byteOffset += 8;
    const jsonString = JSON.stringify(json);
    byteOffset = copyPaddedStringToDataView(dataView, byteOffset, jsonString, 4);
    if (dataView) {
      const jsonByteLength = byteOffset - byteOffsetJsonHeader - 8;
      dataView.setUint32(byteOffsetJsonHeader + 0, jsonByteLength, LE2);
    }
    if (binary) {
      const byteOffsetBinHeader = byteOffset;
      if (dataView) {
        dataView.setUint32(byteOffset + 0, 0, LE2);
        dataView.setUint32(byteOffset + 4, MAGIC_BIN, LE2);
      }
      byteOffset += 8;
      byteOffset = copyPaddedArrayBufferToDataView(dataView, byteOffset, binary, 4);
      if (dataView) {
        const binByteLength = byteOffset - byteOffsetBinHeader - 8;
        dataView.setUint32(byteOffsetBinHeader + 0, binByteLength, LE2);
      }
    }
    if (dataView) {
      const fileByteLength = byteOffset - byteOffsetStart;
      dataView.setUint32(byteOffsetFileLength, fileByteLength, LE2);
    }
    return byteOffset;
  }
  var MAGIC_glTF2, MAGIC_JSON, MAGIC_BIN, LE2;
  var init_encode_glb = __esm({
    "src/lib/encoders/encode-glb.ts"() {
      init_src2();
      MAGIC_glTF2 = 1179937895;
      MAGIC_JSON = 1313821514;
      MAGIC_BIN = 5130562;
      LE2 = true;
    }
  });

  // src/lib/encoders/encode-gltf.ts
  function encodeGLTFSync(gltf, arrayBuffer, byteOffset, options) {
    convertBuffersToBase64(gltf);
    return encodeGLBSync(gltf, arrayBuffer, byteOffset, options);
  }
  function convertBuffersToBase64(gltf, { firstBuffer = 0 } = {}) {
    if (gltf.buffers && gltf.buffers.length > firstBuffer) {
      throw new Error("encodeGLTF: multiple buffers not yet implemented");
    }
  }
  var init_encode_gltf = __esm({
    "src/lib/encoders/encode-gltf.ts"() {
      init_encode_glb();
    }
  });

  // src/gltf-writer.ts
  function encodeSync(gltf, options = {}) {
    const { byteOffset = 0 } = options;
    const byteLength = encodeGLTFSync(gltf, null, byteOffset, options);
    const arrayBuffer = new ArrayBuffer(byteLength);
    const dataView = new DataView(arrayBuffer);
    encodeGLTFSync(gltf, dataView, byteOffset, options);
    return arrayBuffer;
  }
  var GLTFWriter;
  var init_gltf_writer = __esm({
    "src/gltf-writer.ts"() {
      init_version();
      init_encode_gltf();
      GLTFWriter = {
        name: "glTF",
        id: "gltf",
        module: "gltf",
        version: VERSION,
        extensions: ["glb"],
        mimeTypes: ["model/gltf-binary"],
        binary: true,
        encodeSync,
        options: {
          gltf: {}
        }
      };
    }
  });

  // src/glb-loader.ts
  async function parse3(arrayBuffer, options) {
    return parseSync(arrayBuffer, options);
  }
  function parseSync(arrayBuffer, options) {
    const { byteOffset = 0 } = options || {};
    const glb = {};
    parseGLBSync(glb, arrayBuffer, byteOffset, options?.glb);
    return glb;
  }
  var GLBLoader;
  var init_glb_loader = __esm({
    "src/glb-loader.ts"() {
      init_version();
      init_parse_glb();
      GLBLoader = {
        name: "GLB",
        id: "glb",
        module: "gltf",
        version: VERSION,
        extensions: ["glb"],
        mimeTypes: ["model/gltf-binary"],
        binary: true,
        parse: parse3,
        parseSync,
        options: {
          glb: {
            strict: false
          }
        }
      };
    }
  });

  // src/glb-writer.ts
  function encodeSync2(glb, options) {
    const { byteOffset = 0 } = options;
    const byteLength = encodeGLBSync(glb, null, byteOffset, options);
    const arrayBuffer = new ArrayBuffer(byteLength);
    const dataView = new DataView(arrayBuffer);
    encodeGLBSync(glb, dataView, byteOffset, options);
    return arrayBuffer;
  }
  var GLBWriter;
  var init_glb_writer = __esm({
    "src/glb-writer.ts"() {
      init_version();
      init_encode_glb();
      GLBWriter = {
        name: "GLB",
        id: "glb",
        module: "gltf",
        version: VERSION,
        extensions: ["glb"],
        mimeTypes: ["model/gltf-binary"],
        binary: true,
        encodeSync: encodeSync2,
        options: {
          glb: {}
        }
      };
    }
  });

  // src/index.ts
  var src_exports = {};
  __export(src_exports, {
    GLBLoader: () => GLBLoader,
    GLBWriter: () => GLBWriter,
    GLTFLoader: () => GLTFLoader,
    GLTFScenegraph: () => GLTFScenegraph,
    GLTFWriter: () => GLTFWriter,
    _getMemoryUsageGLTF: () => getMemoryUsageGLTF,
    postProcessGLTF: () => postProcessGLTF
  });
  var init_src7 = __esm({
    "src/index.ts"() {
      init_gltf_loader();
      init_gltf_writer();
      init_glb_loader();
      init_glb_writer();
      init_gltf_scenegraph();
      init_post_process_gltf();
      init_gltf_utils();
    }
  });

  // src/bundle.ts
  var require_bundle = __commonJS({
    "src/bundle.ts"(exports, module) {
      var moduleExports = (init_src7(), src_exports);
      globalThis.loaders = globalThis.loaders || {};
      module.exports = Object.assign(globalThis.loaders, moduleExports);
    }
  });
  require_bundle();
})();
