import type { LoaderWithParser, LoaderOptions } from '@loaders.gl/loader-utils';
import type { GLBParseOptions } from './lib/parsers/parse-glb';
export type GLBLoaderOptions = LoaderOptions & {
    glb?: GLBParseOptions;
    byteOffset?: number;
};
/**
 * GLB Loader -
 * GLB is the binary container format for GLTF
 */
export declare const GLBLoader: LoaderWithParser;
export declare const _TypecheckGLBLoader: LoaderWithParser;
//# sourceMappingURL=glb-loader.d.ts.map