import type { LoaderWithParser, LoaderOptions } from '@loaders.gl/loader-utils';
import type { DracoLoaderOptions } from '@loaders.gl/draco';
import type { ImageLoaderOptions } from '@loaders.gl/images';
import type { TextureLoaderOptions } from '@loaders.gl/textures';
import type { GLTFParseOptions } from './lib/parsers/parse-gltf';
import type { GLTFWithBuffers } from './lib/types/gltf-types';
import { GLBLoaderOptions } from './glb-loader';
/**
 * GLTF loader options
 */
export type GLTFLoaderOptions = LoaderOptions & ImageLoaderOptions & TextureLoaderOptions & GLBLoaderOptions & DracoLoaderOptions & {
    gltf?: GLTFParseOptions;
};
/**
 * GLTF loader
 */
export declare const GLTFLoader: LoaderWithParser;
export declare function parse(arrayBuffer: any, options: GLTFLoaderOptions | undefined, context: any): Promise<GLTFWithBuffers | Record<string, any>>;
//# sourceMappingURL=gltf-loader.d.ts.map