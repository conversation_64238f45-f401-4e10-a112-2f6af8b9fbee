import type { GLTF, GLTFScene, GLTFNode, GLTFMesh, GLTFSkin, GLTFMaterial, GLTFAccessor, GLTFSampler, GLTFTexture, GLTFImage, GLTFBuffer, GLTFBufferView, GLTFWithBuffers } from '../types/gltf-types';
type Extension = {
    [key: string]: any;
};
/**
 * Class for structured access to GLTF data
 */
export default class GLTFScenegraph {
    gltf: GLTFWithBuffers;
    sourceBuffers: any[];
    byteLength: number;
    constructor(gltf?: {
        json: GLTF;
        buffers?: any[];
    });
    get json(): GLTF;
    getApplicationData(key: string): unknown;
    getExtraData(key: string): {
        [key: string]: unknown;
    };
    getExtension<T = Extension>(extensionName: string): T | null;
    getRequiredExtension<T = Extension>(extensionName: string): T | null;
    getRequiredExtensions(): string[];
    getUsedExtensions(): string[];
    getRemovedExtensions(): string[];
    getObjectExtension<T = Extension>(object: {
        [key: string]: any;
    }, extensionName: string): T | null;
    getScene(index: number): GLTFScene;
    getNode(index: number): GLTFNode;
    getSkin(index: number): GLTFSkin;
    getMesh(index: number): GLTFMesh;
    getMaterial(index: number): GLTFMaterial;
    getAccessor(index: number): GLTFAccessor;
    getTexture(index: number): GLTFTexture;
    getSampler(index: number): GLTFSampler;
    getImage(index: number): GLTFImage;
    getBufferView(index: number | object): GLTFBufferView;
    getBuffer(index: number): GLTFBuffer;
    getObject(array: string, index: number | object): object;
    /**
     * Accepts buffer view index or buffer view object
     * @returns a `Uint8Array`
     */
    getTypedArrayForBufferView(bufferView: number | object): Uint8Array;
    /** Accepts accessor index or accessor object
     * @returns a typed array with type that matches the types
     */
    getTypedArrayForAccessor(accessor: number | object): any;
    /** accepts accessor index or accessor object
     * returns a `Uint8Array`
     */
    getTypedArrayForImageData(image: number | object): Uint8Array;
    /**
     * Add an extra application-defined key to the top-level data structure
     */
    addApplicationData(key: string, data: object): GLTFScenegraph;
    /**
     * `extras` - Standard GLTF field for storing application specific data
     */
    addExtraData(key: string, data: object): GLTFScenegraph;
    addObjectExtension(object: object, extensionName: string, data: object): GLTFScenegraph;
    setObjectExtension(object: object, extensionName: string, data: object): void;
    removeObjectExtension(object: object, extensionName: string): object;
    /**
     * Add to standard GLTF top level extension object, mark as used
     */
    addExtension(extensionName: string, extensionData?: object): object;
    /**
     * Standard GLTF top level extension object, mark as used and required
     */
    addRequiredExtension(extensionName: any, extensionData?: object): object;
    /**
     * Add extensionName to list of used extensions
     */
    registerUsedExtension(extensionName: string): void;
    /**
     * Add extensionName to list of required extensions
     */
    registerRequiredExtension(extensionName: string): void;
    /**
     * Removes an extension from the top-level list
     */
    removeExtension(extensionName: string): void;
    /**
     *  Set default scene which is to be displayed at load time
     */
    setDefaultScene(sceneIndex: number): void;
    /**
     * @todo: add more properties for scene initialization:
     *   name`, `extensions`, `extras`
     *   https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#reference-scene
     */
    addScene(scene: {
        nodeIndices: number[];
    }): number;
    /**
     * @todo: add more properties for node initialization:
     *   `name`, `extensions`, `extras`, `camera`, `children`, `skin`, `rotation`, `scale`, `translation`, `weights`
     *   https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#node
     */
    addNode(node: {
        meshIndex: number;
        matrix?: number[];
    }): number;
    /** Adds a mesh to the json part */
    addMesh(mesh: {
        attributes: object;
        indices?: object;
        material?: number;
        mode?: number;
    }): number;
    addPointCloud(attributes: object): number;
    /**
     * Adds a binary image. Builds glTF "JSON metadata" and saves buffer reference
     * Buffer will be copied into BIN chunk during "pack"
     * Currently encodes as glTF image
     * @param imageData
     * @param mimeType
     */
    addImage(imageData: any, mimeTypeOpt?: string): number;
    /**
     * Add one untyped source buffer, create a matching glTF `bufferView`, and return its index
     * @param buffer
     */
    addBufferView(buffer: any): number;
    /**
     * Adds an accessor to a bufferView
     * @param bufferViewIndex
     * @param accessor
     */
    addAccessor(bufferViewIndex: number, accessor: object): number;
    /**
     * Add a binary buffer. Builds glTF "JSON metadata" and saves buffer reference
     * Buffer will be copied into BIN chunk during "pack"
     * Currently encodes buffers as glTF accessors, but this could be optimized
     * @param sourceBuffer
     * @param accessor
     */
    addBinaryBuffer(sourceBuffer: any, accessor?: object): number;
    /**
     * Adds a texture to the json part
     * @todo: add more properties for texture initialization
     * `sampler`, `name`, `extensions`, `extras`
     * https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#texture
     */
    addTexture(texture: {
        imageIndex: number;
    }): number;
    /** Adds a material to the json part */
    addMaterial(pbrMaterialInfo: Object): number;
    /** Pack the binary chunk */
    createBinaryChunk(): void;
    _removeStringFromArray(array: any, string: any): void;
    /**
     * Add attributes to buffers and create `attributes` object which is part of `mesh`
     */
    _addAttributes(attributes?: {}): {};
    /**
     * Add indices to buffers
     */
    _addIndices(indices: any): number;
    /**
     * Deduce gltf specific attribue name from input attribute name
     */
    _getGltfAttributeName(attributeName: any): any;
    /**
     * Calculate `min` and `max` arrays of accessor according to spec:
     * https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#reference-accessor
     */
    _getAccessorMinMax(buffer: any, size: any): {
        min: null;
        max: null;
    };
}
export {};
//# sourceMappingURL=gltf-scenegraph.d.ts.map