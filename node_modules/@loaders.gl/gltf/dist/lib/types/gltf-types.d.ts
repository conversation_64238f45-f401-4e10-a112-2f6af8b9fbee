import type { <PERSON>LT<PERSON>, Accessor as GLT<PERSON>ccessor, <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as G<PERSON><PERSON>uffer<PERSON>iew, MeshPrimitive as GLTFMeshPrimitive, Mesh as GLTFMesh, Node as GLTFNode, Material as GLTFMaterial, <PERSON><PERSON> as G<PERSON><PERSON>ampler, Scene as GLTFScene, Skin as GLT<PERSON>kin, Texture as GLTFTexture, Image as GLTFImage, GLTF_KHR_binary_glTF, GLTF_KHR_draco_mesh_compression, GLTF_KHR_texture_basisu, GLTF_EXT_meshopt_compression, GLTF_EXT_texture_webp, GLTF_EXT_feature_metadata, GLTF_EXT_feature_metadata_primitive, GLTF_EXT_feature_metadata_attribute } from './gltf-json-schema';
import type { GLTF as GLTFPostprocessed, Accessor as GLTFAccessorPostprocessed, Image as GLTFImagePostprocessed, Mesh as GLTFMeshPostprocessed, MeshPrimitive as GLTFMeshPrimitivePostprocessed, Material as GLTFMater<PERSON>Postprocessed, Node as GLT<PERSON>NodePostprocessed, Texture as GLTFTexturePostprocessed } from './gltf-postprocessed-schema';
export type { GLTF, GLTFAccessor, GLTFBuffer, GLTFBufferView, GLTFMeshPrimitive, GLTFMesh, GLTFNode, GLTFMaterial, GLTFSampler, GLTFScene, GLTFSkin, GLTFTexture, GLTFImage, GLTF_KHR_binary_glTF, GLTF_KHR_draco_mesh_compression, GLTF_KHR_texture_basisu, GLTF_EXT_meshopt_compression, GLTF_EXT_texture_webp, GLTF_EXT_feature_metadata, GLTF_EXT_feature_metadata_primitive, GLTF_EXT_feature_metadata_attribute };
export type { GLTFPostprocessed, GLTFAccessorPostprocessed, GLTFImagePostprocessed, GLTFNodePostprocessed, GLTFMeshPostprocessed, GLTFMeshPrimitivePostprocessed, GLTFMaterialPostprocessed, GLTFTexturePostprocessed };
export type GLTFObject = GLTFAccessor | GLTFBuffer | GLTFBufferView | GLTFMeshPrimitive | GLTFMesh | GLTFNode | GLTFMaterial | GLTFSampler | GLTFScene | GLTFSkin | GLTFTexture | GLTFImage;
/** GLTFLoader removes processed extensions from `extensionsUsed` and `extensionsUsed`
 * `processedExtensions` is used to track those extensions
 */
export type GLTFWithBuffers = {
    json: GLTF;
    buffers: any[];
    binary?: ArrayBuffer;
    images?: any[];
};
//# sourceMappingURL=gltf-types.d.ts.map