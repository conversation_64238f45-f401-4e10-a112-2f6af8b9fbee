import type { GLB } from '../types/glb-types';
export type GLBParseOptions = {
    magic?: number;
    strict?: boolean;
};
export declare function isGLB(arrayBuffer: ArrayBuffer, byteOffset?: number, options?: GLBParseOptions): boolean;
export default function parseGLBSync(glb: GLB, arrayBuffer: ArrayBuffer, byteOffset?: number, options?: GLBParseOptions): number;
//# sourceMappingURL=parse-glb.d.ts.map