import type { LoaderContext } from '@loaders.gl/loader-utils';
import type { GLTFLoaderOptions } from '../../gltf-loader';
import type { GLTFWithBuffers } from '../types/gltf-types';
export type GLTFParseOptions = {
    normalize?: boolean;
    loadImages?: boolean;
    loadBuffers?: boolean;
    decompressMeshes?: boolean;
    postProcess?: boolean;
    excludeExtensions?: string[];
};
export declare function isGLTF(arrayBuffer: any, options?: any): boolean;
export declare function parseGLTF(gltf: GLTFWithBuffers, arrayBufferOrString: any, byteOffset: number | undefined, options: GLTFLoaderOptions, context: LoaderContext): Promise<GLTFWithBuffers | Record<string, any>>;
//# sourceMappingURL=parse-gltf.d.ts.map