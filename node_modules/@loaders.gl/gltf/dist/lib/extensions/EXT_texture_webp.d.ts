import type { GLTF } from '../types/gltf-types';
import type { GLTFLoaderOptions } from '../../gltf-loader';
/** Extension name */
export declare const name = "EXT_texture_webp";
/**
 * Replaces a texture source reference with the extension texture
 * Done in preprocess() to prevent load of default image
 */
export declare function preprocess(gltfData: {
    json: GLTF;
}, options: GLTFLoaderOptions): void;
//# sourceMappingURL=EXT_texture_webp.d.ts.map