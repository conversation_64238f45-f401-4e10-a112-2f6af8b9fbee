import { GLTF } from '../types/gltf-types';
export declare function getAccessorTypeFromSize(size: any): string;
export declare function getComponentTypeFromArray(typedArray: any): number;
export declare function getAccessorArrayTypeAndLength(accessor: any, bufferView: any): {
    ArrayType: any;
    length: number;
    byteLength: number;
};
/**
 * Calculate the GPU memory used by a GLTF tile, for both buffer and texture memory
 * @param gltf - the gltf content of a GLTF tile
 * @returns - total memory usage in bytes
 */
export declare function getMemoryUsageGLTF(gltf: GLTF): number;
//# sourceMappingURL=gltf-utils.d.ts.map