export declare const COMPONENTS: {
    SCALAR: number;
    VEC2: number;
    VEC3: number;
    VEC4: number;
    MAT2: number;
    MAT3: number;
    MAT4: number;
};
export declare const BYTES: {
    5120: number;
    5121: number;
    5122: number;
    5123: number;
    5125: number;
    5126: number;
};
export declare function getBytesFromComponentType(componentType: any): any;
export declare function getSizeFromAccessorType(type: any): any;
export declare function getGLEnumFromSamplerParameter(parameter: any): any;
//# sourceMappingURL=gltf-constants.d.ts.map