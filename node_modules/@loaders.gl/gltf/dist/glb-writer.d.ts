import type { Writer } from '@loaders.gl/loader-utils';
/**
 * GLB exporter
 * GLB is the binary container format for GLTF
 */
export declare const GLBWriter: {
    name: string;
    id: string;
    module: string;
    version: any;
    extensions: string[];
    mimeTypes: string[];
    binary: boolean;
    encodeSync: typeof encodeSync;
    options: {
        glb: {};
    };
};
declare function encodeSync(glb: any, options: any): ArrayBuffer;
export declare const _TypecheckGLBLoader: Writer;
export {};
//# sourceMappingURL=glb-writer.d.ts.map