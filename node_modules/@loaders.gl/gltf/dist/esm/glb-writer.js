import { VERSION } from './lib/utils/version';
import encodeG<PERSON>BSync from './lib/encoders/encode-glb';
export const GLBWriter = {
  name: 'GL<PERSON>',
  id: 'glb',
  module: 'gltf',
  version: VERSION,
  extensions: ['glb'],
  mimeTypes: ['model/gltf-binary'],
  binary: true,
  encodeSync,
  options: {
    glb: {}
  }
};
function encodeSync(glb, options) {
  const {
    byteOffset = 0
  } = options;
  const byteLength = encodeGLBSync(glb, null, byteOffset, options);
  const arrayBuffer = new ArrayBuffer(byteLength);
  const dataView = new DataView(arrayBuffer);
  encodeGLBSync(glb, dataView, byteOffset, options);
  return arrayBuffer;
}
export const _TypecheckGLBLoader = GLBWriter;
//# sourceMappingURL=glb-writer.js.map