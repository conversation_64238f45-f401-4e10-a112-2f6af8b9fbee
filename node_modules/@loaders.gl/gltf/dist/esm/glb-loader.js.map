{"version": 3, "file": "glb-loader.js", "names": ["VERSION", "parseGLBSync", "GLBL<PERSON>der", "name", "id", "module", "version", "extensions", "mimeTypes", "binary", "parse", "parseSync", "options", "glb", "strict", "arrayBuffer", "byteOffset", "_TypecheckGLBLoader"], "sources": ["../../src/glb-loader.ts"], "sourcesContent": ["import type {LoaderWithParser, LoaderOptions} from '@loaders.gl/loader-utils';\nimport type {GLB} from './lib/types/glb-types';\nimport type {GLBParseOptions} from './lib/parsers/parse-glb';\nimport {VERSION} from './lib/utils/version';\nimport parseGLBSync from './lib/parsers/parse-glb';\n\nexport type GLBLoaderOptions = LoaderOptions & {\n  glb?: GLBParseOptions;\n  byteOffset?: number;\n};\n\n/**\n * GLB Loader -\n * GLB is the binary container format for GLTF\n */\nexport const GLBLoader: LoaderWithParser = {\n  name: 'GLB',\n  id: 'glb',\n  module: 'gltf',\n  version: VERSION,\n  extensions: ['glb'],\n  mimeTypes: ['model/gltf-binary'],\n  binary: true,\n  parse,\n  parseSync,\n  options: {\n    glb: {\n      strict: false // Enables deprecated XVIZ support (illegal CHUNK formats)\n    }\n  }\n};\n\nasync function parse(arrayBuffer: ArrayBuffer, options?: GLBLoaderOptions): Promise<GLB> {\n  return parseSync(arrayBuffer, options);\n}\n\nfunction parseSync(arrayBuffer: ArrayBuffer, options?: GLBLoaderOptions): GLB {\n  const {byteOffset = 0} = options || {};\n  const glb: GLB = {} as GLB;\n  parseGLBSync(glb, arrayBuffer, byteOffset, options?.glb);\n  return glb;\n}\n\n// TYPE TESTS - TODO find a better way than exporting junk\nexport const _TypecheckGLBLoader: LoaderWithParser = GLBLoader;\n"], "mappings": "AAGA,SAAQA,OAAO,QAAO,qBAAqB;AAC3C,OAAOC,YAAY,MAAM,yBAAyB;AAWlD,OAAO,MAAMC,SAA2B,GAAG;EACzCC,IAAI,EAAE,KAAK;EACXC,EAAE,EAAE,KAAK;EACTC,MAAM,EAAE,MAAM;EACdC,OAAO,EAAEN,OAAO;EAChBO,UAAU,EAAE,CAAC,KAAK,CAAC;EACnBC,SAAS,EAAE,CAAC,mBAAmB,CAAC;EAChCC,MAAM,EAAE,IAAI;EACZC,KAAK;EACLC,SAAS;EACTC,OAAO,EAAE;IACPC,GAAG,EAAE;MACHC,MAAM,EAAE;IACV;EACF;AACF,CAAC;AAED,eAAeJ,KAAKA,CAACK,WAAwB,EAAEH,OAA0B,EAAgB;EACvF,OAAOD,SAAS,CAACI,WAAW,EAAEH,OAAO,CAAC;AACxC;AAEA,SAASD,SAASA,CAACI,WAAwB,EAAEH,OAA0B,EAAO;EAC5E,MAAM;IAACI,UAAU,GAAG;EAAC,CAAC,GAAGJ,OAAO,IAAI,CAAC,CAAC;EACtC,MAAMC,GAAQ,GAAG,CAAC,CAAQ;EAC1BZ,YAAY,CAACY,GAAG,EAAEE,WAAW,EAAEC,UAAU,EAAEJ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEC,GAAG,CAAC;EACxD,OAAOA,GAAG;AACZ;AAGA,OAAO,MAAMI,mBAAqC,GAAGf,SAAS"}