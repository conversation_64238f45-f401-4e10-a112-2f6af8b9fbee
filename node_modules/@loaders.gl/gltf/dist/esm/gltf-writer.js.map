{"version": 3, "file": "gltf-writer.js", "names": ["VERSION", "encodeGLTFSync", "GLTFWriter", "name", "id", "module", "version", "extensions", "mimeTypes", "binary", "encodeSync", "options", "gltf", "arguments", "length", "undefined", "byteOffset", "byteLength", "arrayBuffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dataView", "DataView", "_TypecheckGLBLoader"], "sources": ["../../src/gltf-writer.ts"], "sourcesContent": ["import type {Writer, WriterOptions} from '@loaders.gl/loader-utils';\nimport {VERSION} from './lib/utils/version';\nimport {encodeGLTFSync} from './lib/encoders/encode-gltf';\n\nexport type GLTFWriterOptions = WriterOptions & {\n  gltf?: {};\n  byteOffset?: number;\n};\n\n/**\n * GLTF exporter\n */\nexport const GLTFWriter = {\n  name: 'glTF',\n  id: 'gltf',\n  module: 'gltf',\n  version: VERSION,\n\n  extensions: ['glb'], // We only support encoding to binary GLB, not to JSON GLTF\n  mimeTypes: ['model/gltf-binary'], // 'model/gltf+json',\n  binary: true,\n\n  encodeSync,\n\n  options: {\n    gltf: {}\n  }\n};\n\nfunction encodeSync(gltf, options: GLTFWriterOptions = {}) {\n  const {byteOffset = 0} = options;\n\n  // Calculate length, then create arraybuffer and encode\n  const byteLength = encodeGLTFSync(gltf, null, byteOffset, options);\n  const arrayBuffer = new ArrayBuffer(byteLength);\n  const dataView = new DataView(arrayBuffer);\n  encodeGLTFSync(gltf, dataView, byteOffset, options);\n\n  return arrayBuffer;\n}\n\n// TYPE TESTS - TODO find a better way than exporting junk\nexport const _TypecheckGLBLoader: Writer = GLTFWriter;\n"], "mappings": "AACA,SAAQA,OAAO,QAAO,qBAAqB;AAC3C,SAAQC,cAAc,QAAO,4BAA4B;AAUzD,OAAO,MAAMC,UAAU,GAAG;EACxBC,IAAI,EAAE,MAAM;EACZC,EAAE,EAAE,MAAM;EACVC,MAAM,EAAE,MAAM;EACdC,OAAO,EAAEN,OAAO;EAEhBO,UAAU,EAAE,CAAC,KAAK,CAAC;EACnBC,SAAS,EAAE,CAAC,mBAAmB,CAAC;EAChCC,MAAM,EAAE,IAAI;EAEZC,UAAU;EAEVC,OAAO,EAAE;IACPC,IAAI,EAAE,CAAC;EACT;AACF,CAAC;AAED,SAASF,UAAUA,CAACE,IAAI,EAAmC;EAAA,IAAjCD,OAA0B,GAAAE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACvD,MAAM;IAACG,UAAU,GAAG;EAAC,CAAC,GAAGL,OAAO;EAGhC,MAAMM,UAAU,GAAGhB,cAAc,CAACW,IAAI,EAAE,IAAI,EAAEI,UAAU,EAAEL,OAAO,CAAC;EAClE,MAAMO,WAAW,GAAG,IAAIC,WAAW,CAACF,UAAU,CAAC;EAC/C,MAAMG,QAAQ,GAAG,IAAIC,QAAQ,CAACH,WAAW,CAAC;EAC1CjB,cAAc,CAACW,IAAI,EAAEQ,QAAQ,EAAEJ,UAAU,EAAEL,OAAO,CAAC;EAEnD,OAAOO,WAAW;AACpB;AAGA,OAAO,MAAMI,mBAA2B,GAAGpB,UAAU"}