{"version": 3, "file": "index.js", "names": ["GLTFLoader", "GLTFWriter", "GLBL<PERSON>der", "GLBWriter", "default", "GLTFScenegraph", "postProcessGLTF", "getMemoryUsageGLTF", "_getMemoryUsageGLTF"], "sources": ["../../src/index.ts"], "sourcesContent": ["/* eslint-disable camelcase, indent */\nexport type {GLB} from './lib/types/glb-types';\nexport type {\n  GLTF,\n  GLTFAccessor,\n  GLTFBuffer,\n  GLTFBufferView,\n  GLTFMeshPrimitive,\n  GLTFMesh,\n  GLTFNode,\n  GLTFMaterial,\n  GLTFSampler,\n  GLTFScene,\n  GLTFSkin,\n  GLTFTexture,\n  GLTFImage,\n  GLTFPostprocessed,\n  GLTFAccessorPostprocessed,\n  GLTFNodePostprocessed,\n  GLTFMaterialPostprocessed,\n  GLTFMeshPostprocessed,\n  GLTFMeshPrimitivePostprocessed,\n  GLTFImagePostprocessed,\n  GLTFTexturePostprocessed,\n  // The following extensions are handled by the GLTFLoader and removed from the parsed glTF (disable via options.gltf.excludeExtensions)\n  GLTF_KHR_binary_glTF,\n  GLTF_KHR_draco_mesh_compression,\n  GLTF_KHR_texture_basisu,\n  GLTF_EXT_meshopt_compression,\n  GLTF_EXT_texture_webp,\n  GLTF_EXT_feature_metadata,\n  GLTF_EXT_feature_metadata_primitive,\n  GLTF_EXT_feature_metadata_attribute\n} from './lib/types/gltf-types';\n\n// glTF loader/writer definition objects\nexport {GLTFLoader} from './gltf-loader';\nexport {GLTFWriter} from './gltf-writer';\n\n// GLB Loader & Writer (for custom formats that want to leverage the GLB binary \"envelope\")\nexport {GLBLoader} from './glb-loader';\nexport {GLBWriter} from './glb-writer';\n\n// glTF Data Access Helper Class\nexport {default as GLTFScenegraph} from './lib/api/gltf-scenegraph';\nexport {postProcessGLTF} from './lib/api/post-process-gltf';\nexport {getMemoryUsageGLTF as _getMemoryUsageGLTF} from './lib/gltf-utils/gltf-utils';\nexport type {Mesh} from './lib/types/gltf-json-schema';\nexport type {GLTFObject} from './lib/types/gltf-types';\nexport type {Node, Accessor, Image} from './lib/types/gltf-postprocessed-schema';\n"], "mappings": "AAoCA,SAAQA,UAAU,QAAO,eAAe;AACxC,SAAQC,UAAU,QAAO,eAAe;AAGxC,SAAQC,SAAS,QAAO,cAAc;AACtC,SAAQC,SAAS,QAAO,cAAc;AAGtC,SAAQC,OAAO,IAAIC,cAAc,QAAO,2BAA2B;AACnE,SAAQC,eAAe,QAAO,6BAA6B;AAC3D,SAAQC,kBAAkB,IAAIC,mBAAmB,QAAO,6BAA6B"}