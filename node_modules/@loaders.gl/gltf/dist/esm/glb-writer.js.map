{"version": 3, "file": "glb-writer.js", "names": ["VERSION", "encodeGLBSync", "GLBWriter", "name", "id", "module", "version", "extensions", "mimeTypes", "binary", "encodeSync", "options", "glb", "byteOffset", "byteLength", "arrayBuffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dataView", "DataView", "_TypecheckGLBLoader"], "sources": ["../../src/glb-writer.ts"], "sourcesContent": ["import type {Writer} from '@loaders.gl/loader-utils';\nimport {VERSION} from './lib/utils/version';\nimport encodeGLBSync from './lib/encoders/encode-glb';\n\n/**\n * GLB exporter\n * GLB is the binary container format for GLTF\n */\nexport const GLBWriter = {\n  name: 'GL<PERSON>',\n  id: 'glb',\n  module: 'gltf',\n  version: VERSION,\n\n  extensions: ['glb'],\n  mimeTypes: ['model/gltf-binary'],\n  binary: true,\n\n  encodeSync,\n\n  options: {\n    glb: {}\n  }\n};\n\nfunction encodeSync(glb, options) {\n  const {byteOffset = 0} = options;\n\n  // Calculate length and allocate buffer\n  const byteLength = encodeGLBSync(glb, null, byteOffset, options);\n  const arrayBuffer = new ArrayBuffer(byteLength);\n\n  // Encode into buffer\n  const dataView = new DataView(arrayBuffer);\n  encodeGLBSync(glb, dataView, byteOffset, options);\n\n  return arrayBuffer;\n}\n\n// TYPE TESTS - TODO find a better way than exporting junk\nexport const _TypecheckGLBLoader: Writer = GLBWriter;\n"], "mappings": "AACA,SAAQA,OAAO,QAAO,qBAAqB;AAC3C,OAAOC,aAAa,MAAM,2BAA2B;AAMrD,OAAO,MAAMC,SAAS,GAAG;EACvBC,IAAI,EAAE,KAAK;EACXC,EAAE,EAAE,KAAK;EACTC,MAAM,EAAE,MAAM;EACdC,OAAO,EAAEN,OAAO;EAEhBO,UAAU,EAAE,CAAC,KAAK,CAAC;EACnBC,SAAS,EAAE,CAAC,mBAAmB,CAAC;EAChCC,MAAM,EAAE,IAAI;EAEZC,UAAU;EAEVC,OAAO,EAAE;IACPC,GAAG,EAAE,CAAC;EACR;AACF,CAAC;AAED,SAASF,UAAUA,CAACE,GAAG,EAAED,OAAO,EAAE;EAChC,MAAM;IAACE,UAAU,GAAG;EAAC,CAAC,GAAGF,OAAO;EAGhC,MAAMG,UAAU,GAAGb,aAAa,CAACW,GAAG,EAAE,IAAI,EAAEC,UAAU,EAAEF,OAAO,CAAC;EAChE,MAAMI,WAAW,GAAG,IAAIC,WAAW,CAACF,UAAU,CAAC;EAG/C,MAAMG,QAAQ,GAAG,IAAIC,QAAQ,CAACH,WAAW,CAAC;EAC1Cd,aAAa,CAACW,GAAG,EAAEK,QAAQ,EAAEJ,UAAU,EAAEF,OAAO,CAAC;EAEjD,OAAOI,WAAW;AACpB;AAGA,OAAO,MAAMI,mBAA2B,GAAGjB,SAAS"}