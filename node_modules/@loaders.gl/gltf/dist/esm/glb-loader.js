import { VERSION } from './lib/utils/version';
import parseGLBSync from './lib/parsers/parse-glb';
export const GLBLoader = {
  name: 'GL<PERSON>',
  id: 'glb',
  module: 'gltf',
  version: VERSION,
  extensions: ['glb'],
  mimeTypes: ['model/gltf-binary'],
  binary: true,
  parse,
  parseSync,
  options: {
    glb: {
      strict: false
    }
  }
};
async function parse(arrayBuffer, options) {
  return parseSync(arrayBuffer, options);
}
function parseSync(arrayBuffer, options) {
  const {
    byteOffset = 0
  } = options || {};
  const glb = {};
  parseGLBSync(glb, arrayBuffer, byteOffset, options === null || options === void 0 ? void 0 : options.glb);
  return glb;
}
export const _TypecheckGLBLoader = GLBLoader;
//# sourceMappingURL=glb-loader.js.map