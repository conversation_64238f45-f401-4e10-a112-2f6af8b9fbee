{"version": 3, "file": "gltf-loader.js", "names": ["VERSION", "parseGLTF", "GLTFLoader", "name", "id", "module", "version", "extensions", "mimeTypes", "text", "binary", "tests", "parse", "options", "gltf", "normalize", "loadBuffers", "loadImages", "decompressMeshes", "postProcess", "log", "console", "deprecatedOptions", "fetchImages", "createImages", "decompress", "arrayBuffer", "arguments", "length", "undefined", "context", "byteOffset"], "sources": ["../../src/gltf-loader.ts"], "sourcesContent": ["import type {LoaderWithParser, LoaderOptions} from '@loaders.gl/loader-utils';\nimport type {DracoLoaderOptions} from '@loaders.gl/draco';\nimport {VERSION} from './lib/utils/version';\nimport type {ImageLoaderOptions} from '@loaders.gl/images';\nimport type {TextureLoaderOptions} from '@loaders.gl/textures';\nimport type {GLTFParseOptions} from './lib/parsers/parse-gltf';\nimport type {GLTFWithBuffers} from './lib/types/gltf-types';\nimport {parseGLTF} from './lib/parsers/parse-gltf';\nimport {GLBLoaderOptions} from './glb-loader';\n\n/**\n * GLTF loader options\n */\nexport type GLTFLoaderOptions = LoaderOptions &\n  ImageLoaderOptions &\n  TextureLoaderOptions &\n  GLBLoaderOptions &\n  DracoLoaderOptions & {\n    gltf?: GLTFParseOptions;\n  };\n\n/**\n * GLTF loader\n */\nexport const GLTFLoader: LoaderWithParser = {\n  name: 'glTF',\n  id: 'gltf',\n  module: 'gltf',\n  version: VERSION,\n  extensions: ['gltf', 'glb'],\n  mimeTypes: ['model/gltf+json', 'model/gltf-binary'],\n\n  text: true,\n  binary: true,\n  tests: ['glTF'],\n  parse,\n\n  options: {\n    gltf: {\n      normalize: true, // Normalize glTF v1 to glTF v2 format (not yet stable)\n      loadBuffers: true, // Fetch any linked .BIN buffers, decode base64\n      loadImages: true, // Create image objects\n      decompressMeshes: true, // Decompress Draco encoded meshes\n      postProcess: true // Postprocess glTF and return json structure directly\n    },\n\n    // common?\n    log: console // eslint-disable-line\n  },\n  deprecatedOptions: {\n    fetchImages: 'gltf.loadImages',\n    createImages: 'gltf.loadImages',\n    decompress: 'gltf.decompressMeshes',\n    postProcess: 'gltf.postProcess',\n    gltf: {\n      decompress: 'gltf.decompressMeshes'\n    }\n  }\n};\n\nexport async function parse(arrayBuffer, options: GLTFLoaderOptions = {}, context) {\n  // Apps can call the parse method directly, we so apply default options here\n  options = {...GLTFLoader.options, ...options};\n  // @ts-ignore\n  options.gltf = {...GLTFLoader.options.gltf, ...options.gltf};\n\n  const {byteOffset = 0} = options;\n  const gltf = {};\n  return await parseGLTF(gltf as GLTFWithBuffers, arrayBuffer, byteOffset, options, context);\n}\n"], "mappings": "AAEA,SAAQA,OAAO,QAAO,qBAAqB;AAK3C,SAAQC,SAAS,QAAO,0BAA0B;AAiBlD,OAAO,MAAMC,UAA4B,GAAG;EAC1CC,IAAI,EAAE,MAAM;EACZC,EAAE,EAAE,MAAM;EACVC,MAAM,EAAE,MAAM;EACdC,OAAO,EAAEN,OAAO;EAChBO,UAAU,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;EAC3BC,SAAS,EAAE,CAAC,iBAAiB,EAAE,mBAAmB,CAAC;EAEnDC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE,IAAI;EACZC,KAAK,EAAE,CAAC,MAAM,CAAC;EACfC,KAAK;EAELC,OAAO,EAAE;IACPC,IAAI,EAAE;MACJC,SAAS,EAAE,IAAI;MACfC,WAAW,EAAE,IAAI;MACjBC,UAAU,EAAE,IAAI;MAChBC,gBAAgB,EAAE,IAAI;MACtBC,WAAW,EAAE;IACf,CAAC;IAGDC,GAAG,EAAEC;EACP,CAAC;EACDC,iBAAiB,EAAE;IACjBC,WAAW,EAAE,iBAAiB;IAC9BC,YAAY,EAAE,iBAAiB;IAC/BC,UAAU,EAAE,uBAAuB;IACnCN,WAAW,EAAE,kBAAkB;IAC/BL,IAAI,EAAE;MACJW,UAAU,EAAE;IACd;EACF;AACF,CAAC;AAED,OAAO,eAAeb,KAAKA,CAACc,WAAW,EAA4C;EAAA,IAA1Cb,OAA0B,GAAAc,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAAA,IAAEG,OAAO,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAE/EhB,OAAO,GAAG;IAAC,GAAGX,UAAU,CAACW,OAAO;IAAE,GAAGA;EAAO,CAAC;EAE7CA,OAAO,CAACC,IAAI,GAAG;IAAC,GAAGZ,UAAU,CAACW,OAAO,CAACC,IAAI;IAAE,GAAGD,OAAO,CAACC;EAAI,CAAC;EAE5D,MAAM;IAACiB,UAAU,GAAG;EAAC,CAAC,GAAGlB,OAAO;EAChC,MAAMC,IAAI,GAAG,CAAC,CAAC;EACf,OAAO,MAAMb,SAAS,CAACa,IAAI,EAAqBY,WAAW,EAAEK,UAAU,EAAElB,OAAO,EAAEiB,OAAO,CAAC;AAC5F"}