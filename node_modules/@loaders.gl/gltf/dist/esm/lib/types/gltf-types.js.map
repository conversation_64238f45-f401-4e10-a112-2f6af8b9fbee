{"version": 3, "file": "gltf-types.js", "names": [], "sources": ["../../../../src/lib/types/gltf-types.ts"], "sourcesContent": ["/* eslint-disable camelcase */\n\n// Export renamed auto generated types\nimport type {\n  GLTF,\n  Accessor as GLTFAccessor,\n  <PERSON><PERSON><PERSON> as G<PERSON>FBuffer,\n  <PERSON><PERSON><PERSON><PERSON>ie<PERSON> as GLTFBufferView,\n  MeshPrimitive as GLTFMeshPrimitive,\n  Mesh as GLT<PERSON>esh,\n  Node as GLTFN<PERSON>,\n  Material as GLTFMaterial,\n  <PERSON><PERSON> as GLT<PERSON>ampler,\n  Scene as GLTFScene,\n  Skin as GLTFSkin,\n  Texture as GLTFTexture,\n  Image as GLTFImage,\n  GLTF_KHR_binary_glTF,\n  GLTF_KHR_draco_mesh_compression,\n  GLTF_KHR_texture_basisu,\n  GLTF_EXT_meshopt_compression,\n  GLTF_EXT_texture_webp,\n  GLTF_EXT_feature_metadata,\n  GLTF_EXT_feature_metadata_primitive,\n  GLTF_EXT_feature_metadata_attribute\n} from './gltf-json-schema';\n\nimport type {\n  GLTF as GLTFPostprocessed,\n  Accessor as GLTFAccessorPostprocessed,\n  Image as GLTFImagePostprocessed,\n  Mesh as GLTFMeshPostprocessed,\n  MeshPrimitive as GLTFMeshPrimitivePostprocessed,\n  Material as GLT<PERSON>aterialPostprocessed,\n  Node as GLTFNodePostprocessed,\n  Texture as GLTFTexturePostprocessed\n} from './gltf-postprocessed-schema';\n\n// Source glTF types\nexport type {\n  GLTF,\n  GLTFAccessor,\n  GLTFBuffer,\n  GLTFBufferView,\n  GLTFMeshPrimitive,\n  GLTFMesh,\n  GLTFNode,\n  GLTFMaterial,\n  GLTFSampler,\n  GLTFScene,\n  GLTFSkin,\n  GLTFTexture,\n  GLTFImage,\n  GLTF_KHR_binary_glTF,\n  GLTF_KHR_draco_mesh_compression,\n  GLTF_KHR_texture_basisu,\n  GLTF_EXT_meshopt_compression,\n  GLTF_EXT_texture_webp,\n  GLTF_EXT_feature_metadata,\n  GLTF_EXT_feature_metadata_primitive,\n  GLTF_EXT_feature_metadata_attribute\n};\n\n// Post processed glTF types\nexport type {\n  GLTFPostprocessed,\n  GLTFAccessorPostprocessed,\n  GLTFImagePostprocessed,\n  GLTFNodePostprocessed,\n  GLTFMeshPostprocessed,\n  GLTFMeshPrimitivePostprocessed,\n  GLTFMaterialPostprocessed,\n  GLTFTexturePostprocessed\n};\n\nexport type GLTFObject =\n  | GLTFAccessor\n  | GLTFBuffer\n  | GLTFBufferView\n  | GLTFMeshPrimitive\n  | GLTFMesh\n  | GLTFNode\n  | GLTFMaterial\n  | GLTFSampler\n  | GLTFScene\n  | GLTFSkin\n  | GLTFTexture\n  | GLTFImage;\n\n/** GLTFLoader removes processed extensions from `extensionsUsed` and `extensionsUsed`\n * `processedExtensions` is used to track those extensions\n */\nexport type GLTFWithBuffers = {\n  json: GLTF;\n  buffers: any[];\n  binary?: ArrayBuffer;\n  images?: any[];\n};\n"], "mappings": ""}