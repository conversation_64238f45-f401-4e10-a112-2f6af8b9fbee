{"version": 3, "file": "glb-types.js", "names": [], "sources": ["../../../../src/lib/types/glb-types.ts"], "sourcesContent": ["export type GLBBinChunk = {\n  byteOffset: number;\n  byteLength: number;\n  arrayBuffer: ArrayBuffer;\n};\n\nexport type GLB = {\n  type: string;\n  version: number; // Version 2 of binary glTF container format\n\n  // Put less important stuff in a header, to avoid clutter\n  header: {\n    byteOffset: number; // Byte offset into the initial arrayBuffer\n    byteLength: number;\n    hasBinChunk: boolean;\n  };\n\n  // Per spec we must iterate over chunks, ignoring all except JSON and BIN\n  json: {};\n  binChunks: GLBBinChunk[];\n};\n"], "mappings": ""}