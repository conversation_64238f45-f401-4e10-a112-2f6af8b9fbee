{"version": 3, "file": "gltf-postprocessed-schema.js", "names": [], "sources": ["../../../../src/lib/types/gltf-postprocessed-schema.ts"], "sourcesContent": ["// Types forked from https://github.com/bwasty/gltf-loader-ts under MIT license\n// Generated from official JSON schema using `npm run generate-interface` on 2018-02-24\n\n// tslint:disable:quotemark\n// tslint:disable:max-line-length\n\nexport type GlTfId = number;\n/**\n * Indices of those attributes that deviate from their initialization value.\n */\nexport interface AccessorSparseIndices {\n  /**\n   * The index of the bufferView with sparse indices. Referenced bufferView can't have ARRAY_BUFFER or ELEMENT_ARRAY_BUFFER target.\n   */\n  bufferView: GlTfId;\n  /**\n   * The offset relative to the start of the bufferView in bytes. Must be aligned.\n   */\n  byteOffset?: number;\n  /**\n   * The indices data type.\n   */\n  componentType: 5121 | 5123 | 5125 | number;\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\n/**\n * Array of size `accessor.sparse.count` times number of components storing the displaced accessor attributes pointed by `accessor.sparse.indices`.\n */\nexport interface AccessorSparseValues {\n  /**\n   * The index of the bufferView with sparse values. Referenced bufferView can't have ARRAY_BUFFER or ELEMENT_ARRAY_BUFFER target.\n   */\n  bufferView: GlTfId;\n  /**\n   * The offset relative to the start of the bufferView in bytes. Must be aligned.\n   */\n  byteOffset?: number;\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\n/**\n * Sparse storage of attributes that deviate from their initialization value.\n */\nexport interface AccessorSparse {\n  /**\n   * Number of entries stored in the sparse array.\n   */\n  count: number;\n  /**\n   * Index array of size `count` that points to those accessor attributes that deviate from their initialization value. Indices must strictly increase.\n   */\n  indices: AccessorSparseIndices;\n  /**\n   * Array of size `count` times number of components, storing the displaced accessor attributes pointed by `indices`. Substituted values must have the same `componentType` and number of components as the base accessor.\n   */\n  values: AccessorSparseValues;\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\n/**\n * A typed view into a bufferView.  A bufferView contains raw binary data.  An accessor provides a typed view into a bufferView or a subset of a bufferView similar to how WebGL's `vertexAttribPointer()` defines an attribute in a buffer.\n */\nexport interface Accessor {\n  /**\n   * The index of the bufferView.\n   */\n  bufferView?: GlTfId;\n  /**\n   * The offset relative to the start of the bufferView in bytes.\n   */\n  byteOffset?: number;\n  /**\n   * The datatype of components in the attribute.\n   */\n  componentType: 5120 | 5121 | 5122 | 5123 | 5125 | 5126 | number;\n  /**\n   * Specifies whether integer data values should be normalized.\n   */\n  normalized?: boolean;\n  /**\n   * The number of attributes referenced by this accessor.\n   */\n  count: number;\n  /**\n   * Specifies if the attribute is a scalar, vector, or matrix.\n   */\n  type: 'SCALAR' | 'VEC2' | 'VEC3' | 'VEC4' | 'MAT2' | 'MAT3' | 'MAT4' | string;\n  /**\n   * Maximum value of each component in this attribute.\n   */\n  max?: number[];\n  /**\n   * Minimum value of each component in this attribute.\n   */\n  min?: number[];\n  /**\n   * Sparse storage of attributes that deviate from their initialization value.\n   */\n  sparse?: AccessorSparse;\n  name?: any;\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\n/**\n * The index of the node and TRS property that an animation channel targets.\n */\nexport interface AnimationChannelTarget {\n  /**\n   * The index of the node to target.\n   */\n  node?: GlTfId;\n  /**\n   * The name of the node's TRS property to modify, or the \"weights\" of the Morph Targets it instantiates. For the \"translation\" property, the values that are provided by the sampler are the translation along the x, y, and z axes. For the \"rotation\" property, the values are a quaternion in the order (x, y, z, w), where w is the scalar. For the \"scale\" property, the values are the scaling factors along the x, y, and z axes.\n   */\n  path: 'translation' | 'rotation' | 'scale' | 'weights' | string;\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\n/**\n * Targets an animation's sampler at a node's property.\n */\nexport interface AnimationChannel {\n  /**\n   * The index of a sampler in this animation used to compute the value for the target.\n   */\n  sampler: GlTfId;\n  /**\n   * The index of the node and TRS property to target.\n   */\n  target: AnimationChannelTarget;\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\n/**\n * Combines input and output accessors with an interpolation algorithm to define a keyframe graph (but not its target).\n */\nexport interface AnimationSampler {\n  /**\n   * The index of an accessor containing keyframe input values, e.g., time.\n   */\n  input: GlTfId;\n  /**\n   * Interpolation algorithm.\n   */\n  interpolation?: 'LINEAR' | 'STEP' | 'CUBICSPLINE' | string;\n  /**\n   * The index of an accessor, containing keyframe output values.\n   */\n  output: GlTfId;\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\n/**\n * A keyframe animation.\n */\nexport interface Animation {\n  /**\n   * An array of channels, each of which targets an animation's sampler at a node's property. Different channels of the same animation can't have equal targets.\n   */\n  channels: AnimationChannel[];\n  /**\n   * An array of samplers that combines input and output accessors with an interpolation algorithm to define a keyframe graph (but not its target).\n   */\n  samplers: AnimationSampler[];\n  name?: any;\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\n/**\n * Metadata about the glTF asset.\n */\nexport interface Asset {\n  /**\n   * A copyright message suitable for display to credit the content creator.\n   */\n  copyright?: string;\n  /**\n   * Tool that generated this glTF model.  Useful for debugging.\n   */\n  generator?: string;\n  /**\n   * The glTF version that this asset targets.\n   */\n  version: string;\n  /**\n   * The minimum glTF version that this asset targets.\n   */\n  minVersion?: string;\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\n/**\n * A buffer points to binary geometry, animation, or skins.\n */\nexport interface Buffer {\n  /**\n   * The uri of the buffer.\n   */\n  uri?: string;\n  /**\n   * The length of the buffer in bytes.\n   */\n  byteLength: number;\n  name?: any;\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\n/**\n * A view into a buffer generally representing a subset of the buffer.\n */\nexport interface BufferView {\n  id: string;\n  /**\n   * The index of the buffer.\n   */\n  buffer: ArrayBuffer;\n  /**\n   * The offset into the buffer in bytes.\n   */\n  byteOffset?: number;\n  /**\n   * The length of the bufferView in bytes.\n   */\n  byteLength: number;\n  /**\n   * The stride, in bytes.\n   */\n  byteStride?: number;\n  /**\n   * The target that the GPU buffer should be bound to.\n   */\n  target?: 34962 | 34963 | number;\n  name?: any;\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\n/**\n * An orthographic camera containing properties to create an orthographic projection matrix.\n */\nexport interface CameraOrthographic {\n  /**\n   * The floating-point horizontal magnification of the view. Must not be zero.\n   */\n  xmag: number;\n  /**\n   * The floating-point vertical magnification of the view. Must not be zero.\n   */\n  ymag: number;\n  /**\n   * The floating-point distance to the far clipping plane. `zfar` must be greater than `znear`.\n   */\n  zfar: number;\n  /**\n   * The floating-point distance to the near clipping plane.\n   */\n  znear: number;\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\n/**\n * A perspective camera containing properties to create a perspective projection matrix.\n */\nexport interface CameraPerspective {\n  /**\n   * The floating-point aspect ratio of the field of view.\n   */\n  aspectRatio?: number;\n  /**\n   * The floating-point vertical field of view in radians.\n   */\n  yfov: number;\n  /**\n   * The floating-point distance to the far clipping plane.\n   */\n  zfar?: number;\n  /**\n   * The floating-point distance to the near clipping plane.\n   */\n  znear: number;\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\n/**\n * A camera's projection.  A node can reference a camera to apply a transform to place the camera in the scene.\n */\nexport interface Camera {\n  /**\n   * An orthographic camera containing properties to create an orthographic projection matrix.\n   */\n  orthographic?: CameraOrthographic;\n  /**\n   * A perspective camera containing properties to create a perspective projection matrix.\n   */\n  perspective?: CameraPerspective;\n  /**\n   * Specifies if the camera uses a perspective or orthographic projection.\n   */\n  type: 'perspective' | 'orthographic' | string;\n  name?: any;\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\n/**\n * Image data used to create a texture. Image can be referenced by URI or `bufferView` index. `mimeType` is required in the latter case.\n */\nexport interface Image {\n  /**\n   * The uri of the image.\n   */\n  uri?: string;\n  /**\n   * The image's MIME type.\n   */\n  mimeType?: 'image/jpeg' | 'image/png' | string;\n  /**\n   * The index of the bufferView that contains the image. Use this instead of the image's uri property.\n   */\n  bufferView?: BufferView;\n  name?: any;\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\n/**\n * Reference to a texture.\n */\nexport interface TextureInfo {\n  /**\n   * The index of the texture.\n   */\n  index: GlTfId;\n  /**\n   * The set index of texture's TEXCOORD attribute used for texture coordinate mapping.\n   */\n  texCoord?: number;\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\n/**\n * A set of parameter values that are used to define the metallic-roughness material model from Physically-Based Rendering (PBR) methodology.\n */\nexport interface MaterialPbrMetallicRoughness {\n  /**\n   * The material's base color factor.\n   */\n  baseColorFactor?: number[];\n  /**\n   * The base color texture.\n   */\n  baseColorTexture?: TextureInfo;\n  /**\n   * The metalness of the material.\n   */\n  metallicFactor?: number;\n  /**\n   * The roughness of the material.\n   */\n  roughnessFactor?: number;\n  /**\n   * The metallic-roughness texture.\n   */\n  metallicRoughnessTexture?: TextureInfo;\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\nexport interface MaterialNormalTextureInfo {\n  index?: any;\n  texCoord?: any;\n  /**\n   * The scalar multiplier applied to each normal vector of the normal texture.\n   */\n  scale?: number;\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\nexport interface MaterialOcclusionTextureInfo {\n  index?: any;\n  texCoord?: any;\n  /**\n   * A scalar multiplier controlling the amount of occlusion applied.\n   */\n  strength?: number;\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\n/**\n * The material appearance of a primitive.\n */\nexport interface Material {\n  name?: any;\n  extensions?: any;\n  extras?: any;\n  /**\n   * A set of parameter values that are used to define the metallic-roughness material model from Physically-Based Rendering (PBR) methodology. When not specified, all the default values of `pbrMetallicRoughness` apply.\n   */\n  pbrMetallicRoughness?: MaterialPbrMetallicRoughness;\n  /**\n   * The normal map texture.\n   */\n  normalTexture?: MaterialNormalTextureInfo;\n  /**\n   * The occlusion map texture.\n   */\n  occlusionTexture?: MaterialOcclusionTextureInfo;\n  /**\n   * The emissive map texture.\n   */\n  emissiveTexture?: TextureInfo;\n  /**\n   * The emissive color of the material.\n   */\n  emissiveFactor?: number[];\n  /**\n   * The alpha rendering mode of the material.\n   */\n  alphaMode?: 'OPAQUE' | 'MASK' | 'BLEND' | string;\n  /**\n   * The alpha cutoff value of the material.\n   */\n  alphaCutoff?: number;\n  /**\n   * Specifies whether the material is double sided.\n   */\n  doubleSided?: boolean;\n  [k: string]: any;\n}\n/**\n * Geometry to be rendered with the given material.\n */\nexport interface MeshPrimitive {\n  /**\n   * A dictionary object, where each key corresponds to mesh attribute semantic and each value is the index of the accessor containing attribute's data.\n   */\n  attributes: {\n    [k: string]: Accessor;\n  };\n  /**\n   * The index of the accessor that contains the indices.\n   */\n  indices?: Accessor;\n  /**\n   * The index of the material to apply to this primitive when rendering.\n   */\n  material?: Material;\n  /**\n   * The type of primitives to render.\n   */\n  mode?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | number;\n  /**\n   * An array of Morph Targets, each  Morph Target is a dictionary mapping attributes (only `POSITION`, `NORMAL`, and `TANGENT` supported) to their deviations in the Morph Target.\n   */\n  targets?: {\n    [k: string]: GlTfId;\n  }[];\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\n/**\n * A set of primitives to be rendered.  A node can contain one mesh.  A node's transform places the mesh in the scene.\n */\nexport interface Mesh {\n  /**\n   * An array of primitives, each defining geometry to be rendered with a material.\n   */\n  primitives: MeshPrimitive[];\n  /**\n   * Array of weights to be applied to the Morph Targets.\n   */\n  weights?: number[];\n  name?: any;\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\n/**\n * A node in the node hierarchy.  When the node contains `skin`, all `mesh.primitives` must contain `JOINTS_0` and `WEIGHTS_0` attributes.  A node can have either a `matrix` or any combination of `translation`/`rotation`/`scale` (TRS) properties. TRS properties are converted to matrices and postmultiplied in the `T * R * S` order to compose the transformation matrix; first the scale is applied to the vertices, then the rotation, and then the translation. If none are provided, the transform is the identity. When a node is targeted for animation (referenced by an animation.channel.target), only TRS properties may be present; `matrix` will not be present.\n */\nexport interface Node {\n  /**\n   * The index of the camera referenced by this node.\n   */\n  camera?: GlTfId;\n  /**\n   * The indices of this node's children.\n   */\n  children?: Node[];\n  /**\n   * The index of the skin referenced by this node.\n   */\n  skin?: GlTfId;\n  /**\n   * A floating-point 4x4 transformation matrix stored in column-major order.\n   */\n  matrix?: number[];\n  /**\n   * The index of the mesh in this node.\n   */\n  mesh?: Mesh;\n  /**\n   * The node's unit quaternion rotation in the order (x, y, z, w), where w is the scalar.\n   */\n  rotation?: number[];\n  /**\n   * The node's non-uniform scale, given as the scaling factors along the x, y, and z axes.\n   */\n  scale?: number[];\n  /**\n   * The node's translation along the x, y, and z axes.\n   */\n  translation?: number[];\n  /**\n   * The weights of the instantiated Morph Target. Number of elements must match number of Morph Targets of used mesh.\n   */\n  weights?: number[];\n  name?: any;\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\n/**\n * Texture sampler properties for filtering and wrapping modes.\n */\nexport interface Sampler {\n  /**\n   * Magnification filter.\n   */\n  magFilter?: 9728 | 9729 | number;\n  /**\n   * Minification filter.\n   */\n  minFilter?: 9728 | 9729 | 9984 | 9985 | 9986 | 9987 | number;\n  /**\n   * s wrapping mode.\n   */\n  wrapS?: 33071 | 33648 | 10497 | number;\n  /**\n   * t wrapping mode.\n   */\n  wrapT?: 33071 | 33648 | 10497 | number;\n  name?: any;\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\n/**\n * The root nodes of a scene.\n */\nexport interface Scene {\n  /**\n   * The indices of each root node.\n   */\n  nodes?: Node[];\n  name?: any;\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\n/**\n * Joints and matrices defining a skin.\n */\nexport interface Skin {\n  /**\n   * The index of the accessor containing the floating-point 4x4 inverse-bind matrices.  The default is that each matrix is a 4x4 identity matrix, which implies that inverse-bind matrices were pre-applied.\n   */\n  inverseBindMatrices?: GlTfId;\n  /**\n   * The index of the node used as a skeleton root. When undefined, joints transforms resolve to scene root.\n   */\n  skeleton?: GlTfId;\n  /**\n   * Indices of skeleton nodes, used as joints in this skin.\n   */\n  joints: GlTfId[];\n  name?: any;\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\n/**\n * A texture and its sampler.\n */\nexport interface Texture {\n  /**\n   * The index of the sampler used by this texture. When undefined, a sampler with repeat wrapping and auto filtering should be used.\n   */\n  sampler?: GlTfId;\n  /**\n   * The index of the image used by this texture.\n   */\n  source?: Image;\n  name?: any;\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\n/**\n * The root object for a glTF asset.\n */\nexport interface GLTF {\n  /**\n   * Names of glTF extensions used somewhere in this asset.\n   */\n  extensionsUsed?: string[];\n  /**\n   * Names of glTF extensions required to properly load this asset.\n   */\n  extensionsRequired?: string[];\n  /**\n   * An array of accessors.\n   */\n  accessors?: Accessor[];\n  /**\n   * An array of keyframe animations.\n   */\n  animations?: Animation[];\n  /**\n   * Metadata about the glTF asset.\n   */\n  asset: Asset;\n  /**\n   * An array of buffers.\n   */\n  buffers?: Buffer[];\n  /**\n   * An array of bufferViews.\n   */\n  bufferViews?: BufferView[];\n  /**\n   * An array of cameras.\n   */\n  cameras?: Camera[];\n  /**\n   * An array of images.\n   */\n  images?: Image[];\n  /**\n   * An array of materials.\n   */\n  materials?: Material[];\n  /**\n   * An array of meshes.\n   */\n  meshes?: Mesh[];\n  /**\n   * An array of nodes.\n   */\n  nodes?: Node[];\n  /**\n   * An array of samplers.\n   */\n  samplers?: Sampler[];\n  /**\n   * The index of the default scene.\n   */\n  scene?: Scene;\n  /**\n   * An array of scenes.\n   */\n  scenes?: Scene[];\n  /**\n   * An array of skins.\n   */\n  skins?: Skin[];\n  /**\n   * An array of textures.\n   */\n  textures?: Texture[];\n  extensions?: any;\n  extras?: any;\n  [k: string]: any;\n}\n"], "mappings": ""}