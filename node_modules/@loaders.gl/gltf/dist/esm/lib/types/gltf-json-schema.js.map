{"version": 3, "file": "gltf-json-schema.js", "names": [], "sources": ["../../../../src/lib/types/gltf-json-schema.ts"], "sourcesContent": ["// Types forked from https://github.com/bwasty/gltf-loader-ts under MIT license\n// Generated from official JSON schema using `npm run generate-interface` on 2018-02-24\n\nexport type GLTFId = number;\n\n/**\n * Indices of those attributes that deviate from their initialization value.\n */\nexport interface AccessorSparseIndices {\n  /**\n   * The index of the bufferView with sparse indices. Referenced bufferView can't have ARRAY_BUFFER or ELEMENT_ARRAY_BUFFER target.\n   */\n  bufferView: GLTFId;\n  /**\n   * The offset relative to the start of the bufferView in bytes. Must be aligned.\n   */\n  byteOffset?: number;\n  /**\n   * The indices data type.\n   */\n  componentType: 5121 | 5123 | 5125 | number;\n  extensions?: any;\n  extras?: any;\n  // [k: string]: any;\n}\n\n/**\n * Array of size `accessor.sparse.count` times number of components storing the displaced accessor attributes pointed by `accessor.sparse.indices`.\n */\nexport interface AccessorSparseValues {\n  /**\n   * The index of the bufferView with sparse values. Referenced bufferView can't have <PERSON><PERSON><PERSON>_BUFFER or ELEMENT_ARRAY_BUFFER target.\n   */\n  bufferView: GLTFId;\n  /**\n   * The offset relative to the start of the bufferView in bytes. Must be aligned.\n   */\n  byteOffset?: number;\n  extensions?: any;\n  extras?: any;\n  // [k: string]: any;\n}\n\n/**\n * Sparse storage of attributes that deviate from their initialization value.\n */\nexport interface AccessorSparse {\n  /**\n   * Number of entries stored in the sparse array.\n   */\n  count: number;\n  /**\n   * Index array of size `count` that points to those accessor attributes that deviate from their initialization value. Indices must strictly increase.\n   */\n  indices: AccessorSparseIndices;\n  /**\n   * Array of size `count` times number of components, storing the displaced accessor attributes pointed by `indices`. Substituted values must have the same `componentType` and number of components as the base accessor.\n   */\n  values: AccessorSparseValues;\n  extensions?: any;\n  extras?: any;\n  // [k: string]: any;\n}\n\n/**\n * A typed view into a bufferView.  A bufferView contains raw binary data.  An accessor provides a typed view into a bufferView or a subset of a bufferView similar to how WebGL's `vertexAttribPointer()` defines an attribute in a buffer.\n */\nexport interface Accessor {\n  /**\n   * The index of the bufferView.\n   */\n  bufferView?: GLTFId;\n  /**\n   * The offset relative to the start of the bufferView in bytes.\n   */\n  byteOffset?: number;\n  /**\n   * The datatype of components in the attribute.\n   */\n  componentType: 5120 | 5121 | 5122 | 5123 | 5125 | 5126 | number;\n  /**\n   * Specifies whether integer data values should be normalized.\n   */\n  normalized?: boolean;\n  /**\n   * The number of attributes referenced by this accessor.\n   */\n  count: number;\n  /**\n   * Specifies if the attribute is a scalar, vector, or matrix.\n   */\n  type: 'SCALAR' | 'VEC2' | 'VEC3' | 'VEC4' | 'MAT2' | 'MAT3' | 'MAT4' | string;\n  /**\n   * Maximum value of each component in this attribute.\n   */\n  max?: number[];\n  /**\n   * Minimum value of each component in this attribute.\n   */\n  min?: number[];\n  /**\n   * Sparse storage of attributes that deviate from their initialization value.\n   */\n  sparse?: AccessorSparse;\n  name?: any;\n  extensions?: any;\n  extras?: any;\n  // [k: string]: any;\n}\n\n/**\n * The index of the node and TRS property that an animation channel targets.\n */\nexport interface AnimationChannelTarget {\n  /**\n   * The index of the node to target.\n   */\n  node?: GLTFId;\n  /**\n   * The name of the node's TRS property to modify, or the \"weights\" of the Morph Targets it instantiates. For the \"translation\" property, the values that are provided by the sampler are the translation along the x, y, and z axes. For the \"rotation\" property, the values are a quaternion in the order (x, y, z, w), where w is the scalar. For the \"scale\" property, the values are the scaling factors along the x, y, and z axes.\n   */\n  path: 'translation' | 'rotation' | 'scale' | 'weights' | string;\n  extensions?: any;\n  extras?: any;\n  // [k: string]: any;\n}\n\n/**\n * Targets an animation's sampler at a node's property.\n */\nexport interface AnimationChannel {\n  /**\n   * The index of a sampler in this animation used to compute the value for the target.\n   */\n  sampler: GLTFId;\n  /**\n   * The index of the node and TRS property to target.\n   */\n  target: AnimationChannelTarget;\n  extensions?: any;\n  extras?: any;\n  // [k: string]: any;\n}\n\n/**\n * Combines input and output accessors with an interpolation algorithm to define a keyframe graph (but not its target).\n */\nexport interface AnimationSampler {\n  /**\n   * The index of an accessor containing keyframe input values, e.g., time.\n   */\n  input: GLTFId;\n  /**\n   * Interpolation algorithm.\n   */\n  interpolation?: 'LINEAR' | 'STEP' | 'CUBICSPLINE' | string;\n  /**\n   * The index of an accessor, containing keyframe output values.\n   */\n  output: GLTFId;\n  extensions?: any;\n  extras?: any;\n  // [k: string]: any;\n}\n\n/**\n * A keyframe animation.\n */\nexport interface Animation {\n  /**\n   * An array of channels, each of which targets an animation's sampler at a node's property. Different channels of the same animation can't have equal targets.\n   */\n  channels: AnimationChannel[];\n  /**\n   * An array of samplers that combines input and output accessors with an interpolation algorithm to define a keyframe graph (but not its target).\n   */\n  samplers: AnimationSampler[];\n  name?: any;\n  extensions?: any;\n  extras?: any;\n  // [k: string]: any;\n}\n\n/**\n * Metadata about the glTF asset.\n */\nexport interface Asset {\n  /**\n   * A copyright message suitable for display to credit the content creator.\n   */\n  copyright?: string;\n  /**\n   * Tool that generated this glTF model.  Useful for debugging.\n   */\n  generator?: string;\n  /**\n   * The glTF version that this asset targets.\n   */\n  version: string;\n  /**\n   * The minimum glTF version that this asset targets.\n   */\n  minVersion?: string;\n  extensions?: any;\n  extras?: any;\n  // [k: string]: any;\n}\n\n/**\n * A buffer points to binary geometry, animation, or skins.\n */\nexport interface Buffer {\n  /**\n   * The uri of the buffer.\n   */\n  uri?: string;\n  /**\n   * The length of the buffer in bytes.\n   */\n  byteLength: number;\n  name?: any;\n  extensions?: any;\n  extras?: any;\n  // [k: string]: any;\n}\n\n/**\n * A view into a buffer generally representing a subset of the buffer.\n */\nexport interface BufferView {\n  /**\n   * The index of the buffer.\n   */\n  buffer: GLTFId;\n  /**\n   * The offset into the buffer in bytes.\n   */\n  byteOffset?: number;\n  /**\n   * The length of the bufferView in bytes.\n   */\n  byteLength: number;\n  /**\n   * The stride, in bytes.\n   */\n  byteStride?: number;\n  /**\n   * The target that the GPU buffer should be bound to.\n   */\n  target?: 34962 | 34963 | number;\n  name?: any;\n  extensions?: any;\n  extras?: any;\n  // [k: string]: any;\n}\n\n/**\n * An orthographic camera containing properties to create an orthographic projection matrix.\n */\nexport interface CameraOrthographic {\n  /**\n   * The floating-point horizontal magnification of the view. Must not be zero.\n   */\n  xmag: number;\n  /**\n   * The floating-point vertical magnification of the view. Must not be zero.\n   */\n  ymag: number;\n  /**\n   * The floating-point distance to the far clipping plane. `zfar` must be greater than `znear`.\n   */\n  zfar: number;\n  /**\n   * The floating-point distance to the near clipping plane.\n   */\n  znear: number;\n  extensions?: any;\n  extras?: any;\n  // [k: string]: any;\n}\n\n/**\n * A perspective camera containing properties to create a perspective projection matrix.\n */\nexport interface CameraPerspective {\n  /**\n   * The floating-point aspect ratio of the field of view.\n   */\n  aspectRatio?: number;\n  /**\n   * The floating-point vertical field of view in radians.\n   */\n  yfov: number;\n  /**\n   * The floating-point distance to the far clipping plane.\n   */\n  zfar?: number;\n  /**\n   * The floating-point distance to the near clipping plane.\n   */\n  znear: number;\n  extensions?: any;\n  extras?: any;\n  // [k: string]: any;\n}\n\n/**\n * A camera's projection.  A node can reference a camera to apply a transform to place the camera in the scene.\n */\nexport interface Camera {\n  /**\n   * An orthographic camera containing properties to create an orthographic projection matrix.\n   */\n  orthographic?: CameraOrthographic;\n  /**\n   * A perspective camera containing properties to create a perspective projection matrix.\n   */\n  perspective?: CameraPerspective;\n  /**\n   * Specifies if the camera uses a perspective or orthographic projection.\n   */\n  type: 'perspective' | 'orthographic' | string;\n  name?: any;\n  extensions?: any;\n  extras?: any;\n  // [k: string]: any;\n}\n\n/**\n * Image data used to create a texture. Image can be referenced by URI or `bufferView` index. `mimeType` is required in the latter case.\n */\nexport interface Image {\n  /**\n   * The uri of the image.\n   */\n  uri?: string;\n  /**\n   * The image's MIME type.\n   */\n  mimeType?: 'image/jpeg' | 'image/png' | string;\n  /**\n   * The index of the bufferView that contains the image. Use this instead of the image's uri property.\n   */\n  bufferView?: GLTFId;\n  name?: any;\n  extensions?: any;\n  extras?: any;\n  // [k: string]: any;\n}\n\n/**\n * Reference to a texture.\n */\nexport interface TextureInfo {\n  /**\n   * The index of the texture.\n   */\n  index: GLTFId;\n  /**\n   * The set index of texture's TEXCOORD attribute used for texture coordinate mapping.\n   */\n  texCoord?: number;\n  extensions?: any;\n  extras?: any;\n  // [k: string]: any;\n}\n\n/**\n * A set of parameter values that are used to define the metallic-roughness material model from Physically-Based Rendering (PBR) methodology.\n */\nexport interface MaterialPbrMetallicRoughness {\n  /**\n   * The material's base color factor.\n   */\n  baseColorFactor?: number[];\n  /**\n   * The base color texture.\n   */\n  baseColorTexture?: TextureInfo;\n  /**\n   * The metalness of the material.\n   */\n  metallicFactor?: number;\n  /**\n   * The roughness of the material.\n   */\n  roughnessFactor?: number;\n  /**\n   * The metallic-roughness texture.\n   */\n  metallicRoughnessTexture?: TextureInfo;\n  extensions?: any;\n  extras?: any;\n  // [k: string]: any;\n}\nexport interface MaterialNormalTextureInfo {\n  index: any;\n  texCoord?: any;\n  /**\n   * The scalar multiplier applied to each normal vector of the normal texture.\n   */\n  scale?: number;\n  extensions?: any;\n  extras?: any;\n  // [k: string]: any;\n}\nexport interface MaterialOcclusionTextureInfo {\n  index: any;\n  texCoord?: any;\n  /**\n   * A scalar multiplier controlling the amount of occlusion applied.\n   */\n  strength?: number;\n  extensions?: any;\n  extras?: any;\n  // [k: string]: any;\n}\n\n/**\n * The material appearance of a primitive.\n */\nexport interface Material {\n  name?: any;\n  extensions?: any;\n  extras?: any;\n  /**\n   * A set of parameter values that are used to define the metallic-roughness material model from Physically-Based Rendering (PBR) methodology. When not specified, all the default values of `pbrMetallicRoughness` apply.\n   */\n  pbrMetallicRoughness?: MaterialPbrMetallicRoughness;\n  /**\n   * The normal map texture.\n   */\n  normalTexture?: MaterialNormalTextureInfo;\n  /**\n   * The occlusion map texture.\n   */\n  occlusionTexture?: MaterialOcclusionTextureInfo;\n  /**\n   * The emissive map texture.\n   */\n  emissiveTexture?: TextureInfo;\n  /**\n   * The emissive color of the material.\n   */\n  emissiveFactor?: number[];\n  /**\n   * The alpha rendering mode of the material.\n   */\n  alphaMode?: 'OPAQUE' | 'MASK' | 'BLEND' | string;\n  /**\n   * The alpha cutoff value of the material.\n   */\n  alphaCutoff?: number;\n  /**\n   * Specifies whether the material is double sided.\n   */\n  doubleSided?: boolean;\n  // [k: string]: any;\n}\n\n/**\n * Geometry to be rendered with the given material.\n */\nexport interface MeshPrimitive {\n  /**\n   * A dictionary object, where each key corresponds to mesh attribute semantic and each value is the index of the accessor containing attribute's data.\n   */\n  attributes: {\n    [k: string]: GLTFId;\n  };\n  /**\n   * The index of the accessor that contains the indices.\n   */\n  indices?: GLTFId;\n  /**\n   * The index of the material to apply to this primitive when rendering.\n   */\n  material?: GLTFId;\n  /**\n   * The type of primitives to render.\n   */\n  mode?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | number;\n  /**\n   * An array of Morph Targets, each  Morph Target is a dictionary mapping attributes (only `POSITION`, `NORMAL`, and `TANGENT` supported) to their deviations in the Morph Target.\n   */\n  targets?: {\n    [k: string]: GLTFId;\n  }[];\n  extensions?: any;\n  extras?: any;\n  // [k: string]: any;\n}\n\n/**\n * A set of primitives to be rendered.  A node can contain one mesh.  A node's transform places the mesh in the scene.\n */\n\nexport interface Mesh {\n  id?: string;\n  /**\n   * An array of primitives, each defining geometry to be rendered with a material.\n   */\n  primitives: MeshPrimitive[];\n  /**\n   * Array of weights to be applied to the Morph Targets.\n   */\n  weights?: number[];\n  name?: any;\n  extensions?: any;\n  extras?: any;\n  // [k: string]: any;\n}\n\n/**\n * A node in the node hierarchy.  When the node contains `skin`, all `mesh.primitives` must contain `JOINTS_0` and `WEIGHTS_0` attributes.  A node can have either a `matrix` or any combination of `translation`/`rotation`/`scale` (TRS) properties. TRS properties are converted to matrices and postmultiplied in the `T * R * S` order to compose the transformation matrix; first the scale is applied to the vertices, then the rotation, and then the translation. If none are provided, the transform is the identity. When a node is targeted for animation (referenced by an animation.channel.target), only TRS properties may be present; `matrix` will not be present.\n */\nexport interface Node {\n  /**\n   * The index of the camera referenced by this node.\n   */\n  camera?: GLTFId;\n  /**\n   * The indices of this node's children.\n   */\n  children?: GLTFId[];\n  /**\n   * The index of the skin referenced by this node.\n   */\n  skin?: GLTFId;\n  /**\n   * A floating-point 4x4 transformation matrix stored in column-major order.\n   */\n  matrix?: number[];\n  /**\n   * The index of the mesh in this node.\n   */\n  mesh?: GLTFId;\n  /**\n   * The node's unit quaternion rotation in the order (x, y, z, w), where w is the scalar.\n   */\n  rotation?: number[];\n  /**\n   * The node's non-uniform scale, given as the scaling factors along the x, y, and z axes.\n   */\n  scale?: number[];\n  /**\n   * The node's translation along the x, y, and z axes.\n   */\n  translation?: number[];\n  /**\n   * The weights of the instantiated Morph Target. Number of elements must match number of Morph Targets of used mesh.\n   */\n  weights?: number[];\n  name?: any;\n  extensions?: any;\n  extras?: any;\n  // [k: string]: any;\n}\n\n/**\n * Texture sampler properties for filtering and wrapping modes.\n */\nexport interface Sampler {\n  /**\n   * Magnification filter.\n   */\n  magFilter?: 9728 | 9729 | number;\n  /**\n   * Minification filter.\n   */\n  minFilter?: 9728 | 9729 | 9984 | 9985 | 9986 | 9987 | number;\n  /**\n   * s wrapping mode.\n   */\n  wrapS?: 33071 | 33648 | 10497 | number;\n  /**\n   * t wrapping mode.\n   */\n  wrapT?: 33071 | 33648 | 10497 | number;\n  name?: any;\n  extensions?: any;\n  extras?: any;\n  // [k: string]: any;\n}\n\n/**\n * The root nodes of a scene.\n */\nexport interface Scene {\n  /**\n   * The indices of each root node.\n   */\n  nodes?: GLTFId[];\n  name?: any;\n  extensions?: any;\n  extras?: any;\n  // [k: string]: any;\n}\n\n/**\n * Joints and matrices defining a skin.\n */\nexport interface Skin {\n  /**\n   * The index of the accessor containing the floating-point 4x4 inverse-bind matrices.  The default is that each matrix is a 4x4 identity matrix, which implies that inverse-bind matrices were pre-applied.\n   */\n  inverseBindMatrices?: GLTFId;\n  /**\n   * The index of the node used as a skeleton root. When undefined, joints transforms resolve to scene root.\n   */\n  skeleton?: GLTFId;\n  /**\n   * Indices of skeleton nodes, used as joints in this skin.\n   */\n  joints: GLTFId[];\n  name?: any;\n  extensions?: any;\n  extras?: any;\n  // [k: string]: any;\n}\n\n/**\n * A texture and its sampler.\n */\nexport interface Texture {\n  /**\n   * The index of the sampler used by this texture. When undefined, a sampler with repeat wrapping and auto filtering should be used.\n   */\n  sampler?: GLTFId;\n  /**\n   * The index of the image used by this texture.\n   */\n  source?: GLTFId;\n  name?: any;\n  extensions?: any;\n  extras?: any;\n  // [k: string]: any;\n}\n\n/**\n * The root object for a glTF asset.\n */\nexport interface GLTF {\n  /**\n   * Names of glTF extensions used somewhere in this asset.\n   */\n  extensionsUsed?: string[];\n  /**\n   * Names of glTF extensions required to properly load this asset.\n   */\n  extensionsRequired?: string[];\n  /**\n   * An array of accessors.\n   */\n  accessors?: Accessor[];\n  /**\n   * An array of keyframe animations.\n   */\n  animations?: Animation[];\n  /**\n   * Metadata about the glTF asset.\n   */\n  asset: Asset;\n  /**\n   * An array of buffers.\n   */\n  buffers?: Buffer[];\n  /**\n   * An array of bufferViews.\n   */\n  bufferViews?: BufferView[];\n  /**\n   * An array of cameras.\n   */\n  cameras?: Camera[];\n  /**\n   * An array of images.\n   */\n  images?: Image[];\n  /**\n   * An array of materials.\n   */\n  materials?: Material[];\n  /**\n   * An array of meshes.\n   */\n  meshes?: Mesh[];\n  /**\n   * An array of nodes.\n   */\n  nodes?: Node[];\n  /**\n   * An array of samplers.\n   */\n  samplers?: Sampler[];\n  /**\n   * The index of the default scene.\n   */\n  scene?: GLTFId;\n  /**\n   * An array of scenes.\n   */\n  scenes?: Scene[];\n  /**\n   * An array of skins.\n   */\n  skins?: Skin[];\n  /**\n   * An array of textures.\n   */\n  textures?: Texture[];\n  extensions?: unknown;\n  extras?: unknown;\n  [k: string]: unknown;\n}\n\n// GLTF Extensions\n/* eslint-disable camelcase */\n\n/**\n * @see https://github.com/KhronosGroup/glTF/tree/master/extensions/1.0/Khronos/KHR_binary_glTF\n * TODO - this can be used on both images and shaders\n */\nexport type GLTF_KHR_binary_glTF = {\n  bufferView: number;\n  // required for images but not shaders\n  mimeType?: string;\n  height?: number;\n  width?: number;\n  extras?: any;\n};\n\n/**\n * @see https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_draco_mesh_compression\n */\nexport type GLTF_KHR_draco_mesh_compression = {\n  bufferView: GLTFId;\n  attributes: {[name: string]: number};\n  extras?: any;\n};\n\n/**\n * @see https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_texture_basisu\n */\nexport type GLTF_KHR_texture_basisu = {\n  source: GLTFId;\n  extras?: any;\n};\n\n/**\n * @see https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_meshopt_compression\n * buffer: number; //\tThe index of the buffer with compressed data.\t✅ Required\n * byteOffset\tinteger\tThe offset into the buffer in bytes.\tDefault: 0\n * byteLength\tinteger\tThe length of the compressed data in bytes.\t✅ Required\n * byteStride\tinteger\tThe stride, in bytes.\t✅ Required\n * count\tinteger\tThe number of elements.\t✅ Required\n * mode\tstring\tThe compression mode.\t✅ Required\n * filter\tstring\tThe compression filter.\tDefault: \"NONE\"\n */\nexport type GLTF_EXT_meshopt_compression = {\n  buffer: number;\n  byteOffset?: number;\n  byteLength: number;\n  byteStride: number;\n  count: number;\n  mode: 'ATTRIBUTES' | 'TRIANGLES' | 'INDICES';\n  filter?: 'NONE' | 'OCTAHEDRAL' | 'QUATERNION' | 'EXPONENTIAL';\n  extras?: any;\n};\n\n/**\n * @see https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_texture_webp\n */\nexport type GLTF_EXT_texture_webp = {\n  source: GLTFId;\n  extras?: any;\n};\n\n/**\n * @see https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/MSFT_texture_dds\n */\nexport type GLTF_MSFT_texture_dds = {\n  source: GLTFId;\n  extras?: any;\n};\n\n/**\n * Spec - https://github.com/CesiumGS/glTF/tree/3d-tiles-next/extensions/2.0/Vendor/EXT_feature_metadata#gltf-extension-1\n */\nexport type GLTF_EXT_feature_metadata = {\n  /** An object defining classes and enums. */\n  schema?: ExtFeatureMetadataSchema;\n  /** A uri to an external schema file. */\n  schemaUri?: string;\n  /** An object containing statistics about features. */\n  statistics?: Statistics;\n  /** A dictionary, where each key is a feature table ID and each value is an object defining the feature table. */\n  featureTables?: {\n    [key: string]: EXT_feature_metadata_feature_table;\n  };\n  /** A dictionary, where each key is a feature texture ID and each value is an object defining the feature texture. */\n  featureTextures?: {\n    [key: string]: FeatureTexture;\n  };\n  extensions?: any;\n  extras?: any;\n  [key: string]: any;\n};\n\n/**\n * Spec - https://github.com/CesiumGS/glTF/tree/3d-tiles-next/extensions/2.0/Vendor/EXT_feature_metadata#schema\n */\ntype ExtFeatureMetadataSchema = {\n  /** The name of the schema. */\n  name?: string;\n  /** The description of the schema. */\n  description?: string;\n  /** Application-specific version of the schema. */\n  version?: string;\n  /** A dictionary, where each key is a class ID and each value is an object defining the class. */\n  classes?: {\n    [key: string]: EXT_feature_metadata_class_object;\n  };\n  /** A dictionary, where each key is an enum ID and each value is an object defining the values for the enum. */\n  enums?: {\n    [key: string]: ExtFeatureMetadataEnum;\n  };\n  extensions?: any;\n  extras?: any;\n  [key: string]: any;\n};\n\n/**\n * Spec - https://github.com/CesiumGS/glTF/tree/3d-tiles-next/extensions/2.0/Vendor/EXT_feature_metadata#class\n */\nexport type EXT_feature_metadata_class_object = {\n  /** The name of the class, e.g. for display purposes. */\n  name?: string;\n  /** The description of the class. */\n  description?: string;\n  /** A dictionary, where each key is a property ID and each value is an object defining the property. */\n  properties: {\n    [key: string]: ClassProperty;\n  };\n  extensions?: any;\n  extras?: any;\n  [key: string]: any;\n};\n\n/**\n * https://github.com/CesiumGS/glTF/tree/3d-tiles-next/extensions/2.0/Vendor/EXT_feature_metadata#class-property\n */\nexport type ClassProperty = {\n  /** The name of the property, e.g. for display purposes. */\n  name?: string;\n  /** The description of the property. */\n  description?: string;\n  /**\n   * The property type. If ENUM is used, then enumType must also be specified.\n   * If ARRAY is used, then componentType must also be specified.\n   * ARRAY is a fixed-length array when componentCount is defined, and variable-length otherwise.\n   */\n  type: ClassPropertyType;\n  /**\n   * An enum ID as declared in the enums dictionary.\n   * This value must be specified when type or componentType is ENUM.\n   */\n  enumType?: string;\n  /**\n   * When type is ARRAY this indicates the type of each component of the array.\n   * If ENUM is used, then enumType must also be specified.\n   */\n  componentType?:\n    | 'INT8'\n    | 'UINT8'\n    | 'INT16'\n    | 'UINT16'\n    | 'INT32'\n    | 'UINT32'\n    | 'INT64'\n    | 'UINT64'\n    | 'FLOAT32'\n    | 'FLOAT64'\n    | 'BOOLEAN'\n    | 'STRING'\n    | 'ENUM';\n  /** The number of components per element for ARRAY elements. */\n  componentCount?: number;\n  /**\n   * Specifies whether integer values are normalized.\n   * This applies both when type is an integer type, or when type is ARRAY with a componentType that is an integer type.\n   * For unsigned integer types, values are normalized between [0.0, 1.0].\n   * For signed integer types, values are normalized between [-1.0, 1.0].\n   * For all other types, this property is ignored.\n   */\n  normalized: boolean;\n  /**\n   * Maximum allowed values for property values.\n   * Only applicable for numeric types and fixed-length arrays of numeric types.\n   * For numeric types this is a single number.\n   * For fixed-length arrays this is an array with componentCount number of elements.\n   * The normalized property has no effect on these values: they always correspond to the integer values.\n   */\n  max?: number | number[];\n  /**\n   * Minimum allowed values for property values.\n   * Only applicable for numeric types and fixed-length arrays of numeric types.\n   * For numeric types this is a single number.\n   * For fixed-length arrays this is an array with componentCount number of elements.\n   * The normalized property has no effect on these values: they always correspond to the integer values.\n   */\n  min?: number | number[];\n\n  /**\n   * A default value to use when the property value is not defined.\n   * If used, optional must be set to true.\n   * The type of the default value must match the property definition: For BOOLEAN use true or false.\n   * For STRING use a JSON string. For a numeric type use a JSON number.\n   * For ENUM use the enum name, not the integer value.\n   * For ARRAY use a JSON array containing values matching the componentType.\n   */\n  default?: boolean | number | string | number[];\n  /** If true, this property is optional. */\n  optional?: boolean; // default false;\n  /**\n   * An identifier that describes how this property should be interpreted.\n   * The semantic cannot be used by other properties in the class.\n   */\n  semantic?: string;\n  extensions?: any;\n  extras?: any;\n  [key: string]: any;\n};\n\n/**\n * Spec - https://github.com/CesiumGS/glTF/tree/3d-tiles-next/extensions/2.0/Vendor/EXT_feature_metadata#classpropertytype\n */\ntype ClassPropertyType =\n  | 'INT8'\n  | 'UINT8'\n  | 'INT16'\n  | 'UINT16'\n  | 'INT32'\n  | 'UINT32'\n  | 'INT64'\n  | 'UINT64'\n  | 'FLOAT32'\n  | 'FLOAT64'\n  | 'BOOLEAN'\n  | 'STRING'\n  | 'ENUM'\n  | 'ARRAY';\n\n/**\n * Spec - https://github.com/CesiumGS/glTF/tree/3d-tiles-next/extensions/2.0/Vendor/EXT_feature_metadata#enum\n */\ntype ExtFeatureMetadataEnum = {\n  /** The name of the enum, e.g. for display purposes. */\n  name?: string;\n  /** The description of the enum. */\n  description?: string;\n  /** The type of the integer enum value. */\n  valueType?: 'INT8' | 'UINT8' | 'INT16' | 'UINT16' | 'INT32' | 'UINT32' | 'INT64' | 'UINT64'; // default: \"UINT16\"\n  /** An array of enum values. Duplicate names or duplicate integer values are not allowed. */\n  values: EnumValue[];\n  extensions?: any;\n  extras?: any;\n  [key: string]: any;\n};\n\n/**\n * Spec - https://github.com/CesiumGS/glTF/tree/3d-tiles-next/extensions/2.0/Vendor/EXT_feature_metadata#enum-value\n */\ntype EnumValue = {\n  /** The name of the enum value. */\n  name: string;\n  /** The description of the enum value. */\n  description?: string;\n  /** The integer enum value. */\n  value: number; // default: \"UINT16\"\n  extensions?: any;\n  extras?: any;\n  [key: string]: any;\n};\n\n/**\n * Spec - https://github.com/CesiumGS/glTF/tree/3d-tiles-next/extensions/2.0/Vendor/EXT_feature_metadata#feature-table\n */\nexport type EXT_feature_metadata_feature_table = {\n  featureTable: any;\n  /** The class that property values conform to. The value must be a class ID declared in the classes dictionary. */\n  class?: string;\n  /** The number of features, as well as the number of elements in each property array. */\n  count: number;\n  /**\n   * A dictionary, where each key corresponds to a property ID in the class properties dictionary\n   * and each value is an object describing where property values are stored.\n   * Optional properties may be excluded from this dictionary.\n   */\n  properties?: {\n    [key: string]: FeatureTableProperty;\n  };\n  extensions?: any;\n  extras?: any;\n  [key: string]: any;\n};\n\n/**\n * Spec - https://github.com/CesiumGS/glTF/tree/3d-tiles-next/extensions/2.0/Vendor/EXT_feature_metadata#feature-table-property\n */\nexport type FeatureTableProperty = {\n  /**\n   * The index of the buffer view containing property values.\n   * The data type of property values is determined by the property definition:\n   * When type is BOOLEAN values are packed into a bitfield.\n   * When type is STRING values are stored as byte sequences and decoded as UTF-8 strings.\n   * When type is a numeric type values are stored as the provided type.\n   * When type is ENUM values are stored as the enum's valueType.\n   * Each enum value in the buffer must match one of the allowed values in the enum definition.\n   * When type is ARRAY elements are packed tightly together and the data type is based on the componentType following the same rules as above.\n   * arrayOffsetBufferView is required for variable-size arrays\n   * and stringOffsetBufferView is required for strings (for variable-length arrays of strings, both are required)\n   * The buffer view byteOffset must be aligned to a multiple of 8 bytes.\n   * If the buffer view's buffer is the GLB-stored BIN chunk the byte offset is measured relative to the beginning of the GLB.\n   * Otherwise it is measured relative to the beginning of the buffer.\n   */\n  bufferView: number;\n  /** The type of values in arrayOffsetBufferView and stringOffsetBufferView. */\n  offsetType?: string; // default: \"UINT32\"\n  /**\n   * The index of the buffer view containing offsets for variable-length arrays.\n   * The number of offsets is equal to the feature table count plus one.\n   * The offsets represent the start positions of each array, with the last offset representing the position after the last array.\n   * The array length is computed using the difference between the current offset and the subsequent offset.\n   * If componentType is STRING the offsets index into the string offsets array (stored in stringOffsetBufferView),\n   * otherwise they index into the property array (stored in bufferView).\n   * The data type of these offsets is determined by offsetType.\n   * The buffer view byteOffset must be aligned to a multiple of 8 bytes in the same manner as the main bufferView\n   */\n  arrayOffsetBufferView?: number;\n  /**\n   * The index of the buffer view containing offsets for strings.\n   * The number of offsets is equal to the number of string components plus one.\n   * The offsets represent the byte offsets of each string in the main bufferView,\n   * with the last offset representing the byte offset after the last string.\n   * The string byte length is computed using the difference between the current offset and the subsequent offset.\n   * The data type of these offsets is determined by offsetType.\n   * The buffer view byteOffset must be aligned to a multiple of 8 bytes in the same manner as the main bufferView.\n   */\n  stringOffsetBufferView?: number;\n  extensions?: any;\n  extras?: any;\n  [key: string]: any;\n};\n\n/**\n * Spec - https://github.com/CesiumGS/glTF/tree/3d-tiles-next/extensions/2.0/Vendor/EXT_feature_metadata#feature-texture\n */\ntype FeatureTexture = {\n  /** The class this feature texture conforms to. The value must be a class ID declared in the classes dictionary. */\n  class: string;\n  /**\n   * A dictionary, where each key corresponds to a property ID in the class properties dictionary\n   * and each value describes the texture channels containing property values.\n   */\n  properties: {\n    [key: string]: TextureAccessor;\n  };\n  extensions?: any;\n  extras?: any;\n  [key: string]: any;\n};\n\n/**\n * Spec - https://github.com/CesiumGS/glTF/tree/3d-tiles-next/extensions/2.0/Vendor/EXT_feature_metadata#texture-accessor\n */\ntype TextureAccessor = {\n  /** Texture channels containing property values. Channels are labeled by rgba and are swizzled with a string of 1-4 characters. */\n  channels: string;\n  /** The glTF texture and texture coordinates to use. */\n  texture: TextureInfo;\n  extensions?: any;\n  extras?: any;\n  [key: string]: any;\n};\n\n/**\n * Spec - https://github.com/CesiumGS/glTF/tree/3d-tiles-next/extensions/2.0/Vendor/EXT_feature_metadata#statistics-1\n */\ntype Statistics = {\n  /**\n   * A dictionary, where each key is a class ID declared in the classes dictionary\n   * and each value is an object containing statistics about features that conform to the class.\n   */\n  classes?: {\n    [key: string]: ClassStatistics;\n  };\n  extensions?: any;\n  extras?: any;\n  [key: string]: any;\n};\n\n/**\n * Spec - https://github.com/CesiumGS/glTF/tree/3d-tiles-next/extensions/2.0/Vendor/EXT_feature_metadata#class-statistics\n */\ntype ClassStatistics = {\n  /** The number of features that conform to the class. */\n  count?: number;\n  /**\n   * A dictionary, where each key is a class ID declared in the classes dictionary\n   * and each value is an object containing statistics about property values.\n   */\n  properties?: {\n    [key: string]: StatisticsClassProperty;\n  };\n  extensions?: any;\n  extras?: any;\n  [key: string]: any;\n};\n\n/**\n * Spec - https://github.com/CesiumGS/glTF/tree/3d-tiles-next/extensions/2.0/Vendor/EXT_feature_metadata#property-statistics\n * min, max, mean, median, standardDeviation, variance, sum are\n * only applicable for numeric types and fixed-length arrays of numeric types.\n * For numeric types this is a single number.\n * For fixed-length arrays this is an array with componentCount number of elements.\n * The normalized property has no effect on these values.\n */\ntype StatisticsClassProperty = {\n  /** The minimum property value. */\n  min?: number | number[];\n  /** The maximum property value. */\n  max?: number | number[];\n  /** The arithmetic mean of the property values. */\n  mean?: number | number[];\n  /** The median of the property values. */\n  median?: number | number[];\n  /** The standard deviation of the property values. */\n  standardDeviation?: number | number[];\n  /** The variance of the property values. */\n  variance?: number | number[];\n  /** The sum of the property values. */\n  sum?: number | number[];\n  /**\n   * A dictionary, where each key corresponds to an enum name and each value is the number of occurrences of that enum.\n   * Only applicable when type or componentType is ENUM.\n   * For fixed-length arrays, this is an array with componentCount number of elements.\n   */\n  occurrences: {\n    [key: string]: number | number[];\n  };\n  extensions?: any;\n  extras?: any;\n  [key: string]: any;\n};\n\n/**\n * 3DTilesNext EXT_feature_metadata primitive extension\n * Spec - https://github.com/CesiumGS/glTF/tree/3d-tiles-next/extensions/2.0/Vendor/EXT_feature_metadata#primitive-extension\n */\nexport type GLTF_EXT_feature_metadata_primitive = {\n  /** Feature ids definition in attributes */\n  featureIdAttributes?: GLTF_EXT_feature_metadata_attribute[];\n  /** Feature ids definition in textures */\n  featureIdTextures?: GLTF_EXT_feature_metadata_attribute[];\n  /** An array of IDs of feature textures from the root EXT_feature_metadata object. */\n  featureTextures?: string[];\n  extensions?: any;\n  extras?: any;\n  [key: string]: any;\n};\n\n/**\n * Attribute which described featureIds definition.\n */\nexport type GLTF_EXT_feature_metadata_attribute = {\n  /** Name of feature table */\n  featureTable: string;\n  /** Described how feature ids are defined */\n  featureIds: ExtFeatureMetadataFeatureIds;\n  extensions?: any;\n  extras?: any;\n  [key: string]: any;\n};\n\n/**\n * Defining featureIds by attributes or implicitly.\n */\ntype ExtFeatureMetadataFeatureIds = {\n  /** Name of attribute where featureIds are defined */\n  attribute?: string;\n  /** Sets a constant feature ID for each vertex. The default is 0. */\n  constant?: number;\n  /** Sets the rate at which feature IDs increment.\n   * If divisor is zero then constant is used.\n   * If divisor is greater than zero the feature ID increments once per divisor sets of vertices, starting at constant.\n   * The default is 0\n   */\n  divisor?: number;\n  /** gLTF textureInfo object - https://github.com/CesiumGS/glTF/blob/3d-tiles-next/specification/2.0/schema/textureInfo.schema.json */\n  texture?: ExtFeatureMetadataTexture;\n  /** Must be a single channel (\"r\", \"g\", \"b\", or \"a\") */\n  channels?: 'r' | 'g' | 'b' | 'a';\n};\n\n/**\n * Reference to a texture.\n */\ntype ExtFeatureMetadataTexture = {\n  /** The set index of texture's TEXCOORD attribute used for texture coordinate mapping.*/\n  texCoord: number;\n  /** The index of the texture. */\n  index: number;\n};\n"], "mappings": ""}