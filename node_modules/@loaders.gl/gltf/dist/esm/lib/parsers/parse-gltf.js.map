{"version": 3, "file": "parse-gltf.js", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectSupportedBasisFormat", "ImageLoader", "parseJSON", "sliceArrayBuffer", "assert", "resolveUrl", "getTypedArrayForBufferView", "preprocessExtensions", "decodeExtensions", "normalizeGLTFV1", "postProcessGLTF", "parseGLBSync", "isGLB", "isGLTF", "arrayBuffer", "options", "byteOffset", "parseGLTF", "gltf", "arrayBufferOrString", "_options$gltf", "_options$gltf2", "_options$gltf3", "_options$gltf4", "arguments", "length", "undefined", "context", "parseGLTFContainerSync", "normalize", "promises", "loadBuffers", "json", "buffers", "loadImages", "promise", "push", "Promise", "all", "postProcess", "data", "uri", "baseUri", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "textDecoder", "TextDecoder", "decode", "glb", "type", "concat", "_glb", "Array", "fill", "header", "hasBinChunk", "binChunks", "byteLength", "images", "i", "buffer", "_context$fetch", "_response$arrayBuffer", "fetch", "response", "call", "imageIndices", "getReferencesImageIndices", "imageIndex", "loadImage", "Set", "textures", "texture", "source", "add", "from", "sort", "image", "index", "parse", "hasOwnProperty", "bufferView", "Number", "isFinite", "array", "parsedImage", "mimeType", "basis", "format", "compressed", "mipmaps", "width", "height"], "sources": ["../../../../src/lib/parsers/parse-gltf.ts"], "sourcesContent": ["/* eslint-disable camelcase, max-statements, no-restricted-globals */\nimport type {LoaderContext} from '@loaders.gl/loader-utils';\nimport {BasisLoader, selectSupportedBasisFormat} from '@loaders.gl/textures';\nimport type {GLTFLoaderOptions} from '../../gltf-loader';\nimport type {GLB} from '../types/glb-types';\nimport type {GLTFWithBuffers} from '../types/gltf-types';\n\nimport {ImageLoader} from '@loaders.gl/images';\nimport {parseJSON, sliceArrayBuffer} from '@loaders.gl/loader-utils';\nimport {assert} from '../utils/assert';\nimport {resolveUrl} from '../gltf-utils/resolve-url';\nimport {getTypedArrayForBufferView} from '../gltf-utils/get-typed-array';\nimport {preprocessExtensions, decodeExtensions} from '../api/gltf-extensions';\nimport {normalizeGLTFV1} from '../api/normalize-gltf-v1';\nimport {postProcessGLTF} from '../api/post-process-gltf';\nimport parseGLBSync, {isGLB} from './parse-glb';\n\nexport type GLTFParseOptions = {\n  normalize?: boolean;\n  loadImages?: boolean;\n  loadBuffers?: boolean;\n  decompressMeshes?: boolean;\n  postProcess?: boolean;\n  excludeExtensions?: string[];\n};\n\n// export type GLTFOptions = {\n//   gltf?: GLTFParseOptions;\n// };\n\nexport function isGLTF(arrayBuffer, options?): boolean {\n  const byteOffset = 0;\n  return isGLB(arrayBuffer, byteOffset, options);\n}\n\nexport async function parseGLTF(\n  gltf: GLTFWithBuffers,\n  arrayBufferOrString,\n  byteOffset = 0,\n  options: GLTFLoaderOptions,\n  context: LoaderContext\n) {\n  parseGLTFContainerSync(gltf, arrayBufferOrString, byteOffset, options);\n\n  normalizeGLTFV1(gltf, {normalize: options?.gltf?.normalize});\n\n  preprocessExtensions(gltf, options, context);\n\n  const promises: Promise<any>[] = [];\n\n  // Load linked buffers asynchronously and decodes base64 buffers in parallel\n  if (options?.gltf?.loadBuffers && gltf.json.buffers) {\n    await loadBuffers(gltf, options, context);\n  }\n\n  if (options?.gltf?.loadImages) {\n    const promise = loadImages(gltf, options, context);\n    promises.push(promise);\n  }\n\n  const promise = decodeExtensions(gltf, options, context);\n  promises.push(promise);\n\n  // Parallelize image loading and buffer loading/extension decoding\n  await Promise.all(promises);\n\n  // Post processing resolves indices to objects, buffers\n  return options?.gltf?.postProcess ? postProcessGLTF(gltf, options) : gltf;\n}\n\n// `data` - can be ArrayBuffer (GLB), ArrayBuffer (Binary JSON), String (JSON), or Object (parsed JSON)\nfunction parseGLTFContainerSync(gltf, data, byteOffset, options) {\n  // Initialize gltf container\n  if (options.uri) {\n    gltf.baseUri = options.uri;\n  }\n\n  // If data is binary and starting with magic bytes, assume binary JSON text, convert to string\n  if (data instanceof ArrayBuffer && !isGLB(data, byteOffset, options)) {\n    const textDecoder = new TextDecoder();\n    data = textDecoder.decode(data);\n  }\n\n  if (typeof data === 'string') {\n    // If string, try to parse as JSON\n    gltf.json = parseJSON(data);\n  } else if (data instanceof ArrayBuffer) {\n    // If still ArrayBuffer, parse as GLB container\n    const glb: GLB = {} as GLB;\n    byteOffset = parseGLBSync(glb, data, byteOffset, options.glb);\n\n    assert(glb.type === 'glTF', `Invalid GLB magic string ${glb.type}`);\n\n    gltf._glb = glb;\n    gltf.json = glb.json;\n  } else {\n    assert(false, 'GLTF: must be ArrayBuffer or string');\n  }\n\n  // Populate buffers\n  // Create an external buffers array to hold binary data\n  const buffers = gltf.json.buffers || [];\n  gltf.buffers = new Array(buffers.length).fill(null);\n\n  // Populates JSON and some bin chunk info\n  if (gltf._glb && gltf._glb.header.hasBinChunk) {\n    const {binChunks} = gltf._glb;\n    gltf.buffers[0] = {\n      arrayBuffer: binChunks[0].arrayBuffer,\n      byteOffset: binChunks[0].byteOffset,\n      byteLength: binChunks[0].byteLength\n    };\n\n    // TODO - this modifies JSON and is a post processing thing\n    // gltf.json.buffers[0].data = gltf.buffers[0].arrayBuffer;\n    // gltf.json.buffers[0].byteOffset = gltf.buffers[0].byteOffset;\n  }\n\n  // Populate images\n  const images = gltf.json.images || [];\n  gltf.images = new Array(images.length).fill({});\n}\n\n/** Asynchronously fetch and parse buffers, store in buffers array outside of json\n * TODO - traverse gltf and determine which buffers are actually needed\n */\nasync function loadBuffers(gltf: GLTFWithBuffers, options, context: LoaderContext) {\n  // TODO\n  const buffers = gltf.json.buffers || [];\n  for (let i = 0; i < buffers.length; ++i) {\n    const buffer = buffers[i];\n    if (buffer.uri) {\n      const {fetch} = context;\n      assert(fetch);\n\n      const uri = resolveUrl(buffer.uri, options);\n      const response = await context?.fetch?.(uri);\n      const arrayBuffer = await response?.arrayBuffer?.();\n\n      gltf.buffers[i] = {\n        arrayBuffer,\n        byteOffset: 0,\n        byteLength: arrayBuffer.byteLength\n      };\n\n      delete buffer.uri;\n    } else if (gltf.buffers[i] === null) {\n      gltf.buffers[i] = {\n        arrayBuffer: new ArrayBuffer(buffer.byteLength),\n        byteOffset: 0,\n        byteLength: buffer.byteLength\n      };\n    }\n  }\n}\n\n/**\n * Loads all images\n * TODO - traverse gltf and determine which images are actually needed\n * @param gltf\n * @param options\n * @param context\n * @returns\n */\nasync function loadImages(gltf: GLTFWithBuffers, options, context: LoaderContext) {\n  const imageIndices = getReferencesImageIndices(gltf);\n\n  const images = gltf.json.images || [];\n\n  const promises: Promise<any>[] = [];\n  for (const imageIndex of imageIndices) {\n    promises.push(loadImage(gltf, images[imageIndex], imageIndex, options, context));\n  }\n\n  return await Promise.all(promises);\n}\n\n/** Make sure we only load images that are actually referenced by textures */\nfunction getReferencesImageIndices(gltf: GLTFWithBuffers): number[] {\n  const imageIndices = new Set<number>();\n\n  const textures = gltf.json.textures || [];\n  for (const texture of textures) {\n    if (texture.source !== undefined) {\n      imageIndices.add(texture.source);\n    }\n  }\n\n  return Array.from(imageIndices).sort();\n}\n\n/** Asynchronously fetches and parses one image, store in images array outside of json */\nasync function loadImage(\n  gltf: GLTFWithBuffers,\n  image,\n  index: number,\n  options,\n  context: LoaderContext\n) {\n  const {fetch, parse} = context;\n\n  let arrayBuffer;\n\n  if (image.uri && !image.hasOwnProperty('bufferView')) {\n    const uri = resolveUrl(image.uri, options);\n    const response = await fetch(uri);\n    arrayBuffer = await response.arrayBuffer();\n    image.bufferView = {\n      data: arrayBuffer\n    };\n  }\n\n  if (Number.isFinite(image.bufferView)) {\n    const array = getTypedArrayForBufferView(gltf.json, gltf.buffers, image.bufferView);\n    arrayBuffer = sliceArrayBuffer(array.buffer, array.byteOffset, array.byteLength);\n  }\n\n  assert(arrayBuffer, 'glTF image has no data');\n\n  // Call `parse`\n  let parsedImage = await parse(\n    arrayBuffer,\n    [ImageLoader, BasisLoader],\n    {mimeType: image.mimeType, basis: options.basis || {format: selectSupportedBasisFormat()}},\n    context\n  );\n\n  if (parsedImage && parsedImage[0]) {\n    parsedImage = {\n      compressed: true,\n      mipmaps: false,\n      width: parsedImage[0].width,\n      height: parsedImage[0].height,\n      data: parsedImage[0]\n    };\n  }\n  // TODO making sure ImageLoader is overridable by using array of loaders\n  // const parsedImage = await parse(arrayBuffer, [ImageLoader]);\n\n  // Store the loaded image\n  gltf.images = gltf.images || [];\n  gltf.images[index] = parsedImage;\n}\n"], "mappings": "AAEA,SAAQA,WAAW,EAAEC,0BAA0B,QAAO,sBAAsB;AAK5E,SAAQC,WAAW,QAAO,oBAAoB;AAC9C,SAAQC,SAAS,EAAEC,gBAAgB,QAAO,0BAA0B;AACpE,SAAQC,MAAM,QAAO,iBAAiB;AACtC,SAAQC,UAAU,QAAO,2BAA2B;AACpD,SAAQC,0BAA0B,QAAO,+BAA+B;AACxE,SAAQC,oBAAoB,EAAEC,gBAAgB,QAAO,wBAAwB;AAC7E,SAAQC,eAAe,QAAO,0BAA0B;AACxD,SAAQC,eAAe,QAAO,0BAA0B;AACxD,OAAOC,YAAY,IAAGC,KAAK,QAAO,aAAa;AAe/C,OAAO,SAASC,MAAMA,CAACC,WAAW,EAAEC,OAAQ,EAAW;EACrD,MAAMC,UAAU,GAAG,CAAC;EACpB,OAAOJ,KAAK,CAACE,WAAW,EAAEE,UAAU,EAAED,OAAO,CAAC;AAChD;AAEA,OAAO,eAAeE,SAASA,CAC7BC,IAAqB,EACrBC,mBAAmB,EAInB;EAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA;EAAA,IAHAP,UAAU,GAAAQ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IACdT,OAA0B,GAAAS,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,IAC1BC,OAAsB,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAEtBE,sBAAsB,CAACV,IAAI,EAAEC,mBAAmB,EAAEH,UAAU,EAAED,OAAO,CAAC;EAEtEN,eAAe,CAACS,IAAI,EAAE;IAACW,SAAS,EAAEd,OAAO,aAAPA,OAAO,wBAAAK,aAAA,GAAPL,OAAO,CAAEG,IAAI,cAAAE,aAAA,uBAAbA,aAAA,CAAeS;EAAS,CAAC,CAAC;EAE5DtB,oBAAoB,CAACW,IAAI,EAAEH,OAAO,EAAEY,OAAO,CAAC;EAE5C,MAAMG,QAAwB,GAAG,EAAE;EAGnC,IAAIf,OAAO,aAAPA,OAAO,gBAAAM,cAAA,GAAPN,OAAO,CAAEG,IAAI,cAAAG,cAAA,eAAbA,cAAA,CAAeU,WAAW,IAAIb,IAAI,CAACc,IAAI,CAACC,OAAO,EAAE;IACnD,MAAMF,WAAW,CAACb,IAAI,EAAEH,OAAO,EAAEY,OAAO,CAAC;EAC3C;EAEA,IAAIZ,OAAO,aAAPA,OAAO,gBAAAO,cAAA,GAAPP,OAAO,CAAEG,IAAI,cAAAI,cAAA,eAAbA,cAAA,CAAeY,UAAU,EAAE;IAC7B,MAAMC,OAAO,GAAGD,UAAU,CAAChB,IAAI,EAAEH,OAAO,EAAEY,OAAO,CAAC;IAClDG,QAAQ,CAACM,IAAI,CAACD,OAAO,CAAC;EACxB;EAEA,MAAMA,OAAO,GAAG3B,gBAAgB,CAACU,IAAI,EAAEH,OAAO,EAAEY,OAAO,CAAC;EACxDG,QAAQ,CAACM,IAAI,CAACD,OAAO,CAAC;EAGtB,MAAME,OAAO,CAACC,GAAG,CAACR,QAAQ,CAAC;EAG3B,OAAOf,OAAO,aAAPA,OAAO,gBAAAQ,cAAA,GAAPR,OAAO,CAAEG,IAAI,cAAAK,cAAA,eAAbA,cAAA,CAAegB,WAAW,GAAG7B,eAAe,CAACQ,IAAI,EAAEH,OAAO,CAAC,GAAGG,IAAI;AAC3E;AAGA,SAASU,sBAAsBA,CAACV,IAAI,EAAEsB,IAAI,EAAExB,UAAU,EAAED,OAAO,EAAE;EAE/D,IAAIA,OAAO,CAAC0B,GAAG,EAAE;IACfvB,IAAI,CAACwB,OAAO,GAAG3B,OAAO,CAAC0B,GAAG;EAC5B;EAGA,IAAID,IAAI,YAAYG,WAAW,IAAI,CAAC/B,KAAK,CAAC4B,IAAI,EAAExB,UAAU,EAAED,OAAO,CAAC,EAAE;IACpE,MAAM6B,WAAW,GAAG,IAAIC,WAAW,CAAC,CAAC;IACrCL,IAAI,GAAGI,WAAW,CAACE,MAAM,CAACN,IAAI,CAAC;EACjC;EAEA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAE5BtB,IAAI,CAACc,IAAI,GAAG9B,SAAS,CAACsC,IAAI,CAAC;EAC7B,CAAC,MAAM,IAAIA,IAAI,YAAYG,WAAW,EAAE;IAEtC,MAAMI,GAAQ,GAAG,CAAC,CAAQ;IAC1B/B,UAAU,GAAGL,YAAY,CAACoC,GAAG,EAAEP,IAAI,EAAExB,UAAU,EAAED,OAAO,CAACgC,GAAG,CAAC;IAE7D3C,MAAM,CAAC2C,GAAG,CAACC,IAAI,KAAK,MAAM,8BAAAC,MAAA,CAA8BF,GAAG,CAACC,IAAI,CAAE,CAAC;IAEnE9B,IAAI,CAACgC,IAAI,GAAGH,GAAG;IACf7B,IAAI,CAACc,IAAI,GAAGe,GAAG,CAACf,IAAI;EACtB,CAAC,MAAM;IACL5B,MAAM,CAAC,KAAK,EAAE,qCAAqC,CAAC;EACtD;EAIA,MAAM6B,OAAO,GAAGf,IAAI,CAACc,IAAI,CAACC,OAAO,IAAI,EAAE;EACvCf,IAAI,CAACe,OAAO,GAAG,IAAIkB,KAAK,CAAClB,OAAO,CAACR,MAAM,CAAC,CAAC2B,IAAI,CAAC,IAAI,CAAC;EAGnD,IAAIlC,IAAI,CAACgC,IAAI,IAAIhC,IAAI,CAACgC,IAAI,CAACG,MAAM,CAACC,WAAW,EAAE;IAC7C,MAAM;MAACC;IAAS,CAAC,GAAGrC,IAAI,CAACgC,IAAI;IAC7BhC,IAAI,CAACe,OAAO,CAAC,CAAC,CAAC,GAAG;MAChBnB,WAAW,EAAEyC,SAAS,CAAC,CAAC,CAAC,CAACzC,WAAW;MACrCE,UAAU,EAAEuC,SAAS,CAAC,CAAC,CAAC,CAACvC,UAAU;MACnCwC,UAAU,EAAED,SAAS,CAAC,CAAC,CAAC,CAACC;IAC3B,CAAC;EAKH;EAGA,MAAMC,MAAM,GAAGvC,IAAI,CAACc,IAAI,CAACyB,MAAM,IAAI,EAAE;EACrCvC,IAAI,CAACuC,MAAM,GAAG,IAAIN,KAAK,CAACM,MAAM,CAAChC,MAAM,CAAC,CAAC2B,IAAI,CAAC,CAAC,CAAC,CAAC;AACjD;AAKA,eAAerB,WAAWA,CAACb,IAAqB,EAAEH,OAAO,EAAEY,OAAsB,EAAE;EAEjF,MAAMM,OAAO,GAAGf,IAAI,CAACc,IAAI,CAACC,OAAO,IAAI,EAAE;EACvC,KAAK,IAAIyB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzB,OAAO,CAACR,MAAM,EAAE,EAAEiC,CAAC,EAAE;IACvC,MAAMC,MAAM,GAAG1B,OAAO,CAACyB,CAAC,CAAC;IACzB,IAAIC,MAAM,CAAClB,GAAG,EAAE;MAAA,IAAAmB,cAAA,EAAAC,qBAAA;MACd,MAAM;QAACC;MAAK,CAAC,GAAGnC,OAAO;MACvBvB,MAAM,CAAC0D,KAAK,CAAC;MAEb,MAAMrB,GAAG,GAAGpC,UAAU,CAACsD,MAAM,CAAClB,GAAG,EAAE1B,OAAO,CAAC;MAC3C,MAAMgD,QAAQ,GAAG,OAAMpC,OAAO,aAAPA,OAAO,wBAAAiC,cAAA,GAAPjC,OAAO,CAAEmC,KAAK,cAAAF,cAAA,uBAAdA,cAAA,CAAAI,IAAA,CAAArC,OAAO,EAAUc,GAAG,CAAC;MAC5C,MAAM3B,WAAW,GAAG,OAAMiD,QAAQ,aAARA,QAAQ,wBAAAF,qBAAA,GAARE,QAAQ,CAAEjD,WAAW,cAAA+C,qBAAA,uBAArBA,qBAAA,CAAAG,IAAA,CAAAD,QAAwB,CAAC;MAEnD7C,IAAI,CAACe,OAAO,CAACyB,CAAC,CAAC,GAAG;QAChB5C,WAAW;QACXE,UAAU,EAAE,CAAC;QACbwC,UAAU,EAAE1C,WAAW,CAAC0C;MAC1B,CAAC;MAED,OAAOG,MAAM,CAAClB,GAAG;IACnB,CAAC,MAAM,IAAIvB,IAAI,CAACe,OAAO,CAACyB,CAAC,CAAC,KAAK,IAAI,EAAE;MACnCxC,IAAI,CAACe,OAAO,CAACyB,CAAC,CAAC,GAAG;QAChB5C,WAAW,EAAE,IAAI6B,WAAW,CAACgB,MAAM,CAACH,UAAU,CAAC;QAC/CxC,UAAU,EAAE,CAAC;QACbwC,UAAU,EAAEG,MAAM,CAACH;MACrB,CAAC;IACH;EACF;AACF;AAUA,eAAetB,UAAUA,CAAChB,IAAqB,EAAEH,OAAO,EAAEY,OAAsB,EAAE;EAChF,MAAMsC,YAAY,GAAGC,yBAAyB,CAAChD,IAAI,CAAC;EAEpD,MAAMuC,MAAM,GAAGvC,IAAI,CAACc,IAAI,CAACyB,MAAM,IAAI,EAAE;EAErC,MAAM3B,QAAwB,GAAG,EAAE;EACnC,KAAK,MAAMqC,UAAU,IAAIF,YAAY,EAAE;IACrCnC,QAAQ,CAACM,IAAI,CAACgC,SAAS,CAAClD,IAAI,EAAEuC,MAAM,CAACU,UAAU,CAAC,EAAEA,UAAU,EAAEpD,OAAO,EAAEY,OAAO,CAAC,CAAC;EAClF;EAEA,OAAO,MAAMU,OAAO,CAACC,GAAG,CAACR,QAAQ,CAAC;AACpC;AAGA,SAASoC,yBAAyBA,CAAChD,IAAqB,EAAY;EAClE,MAAM+C,YAAY,GAAG,IAAII,GAAG,CAAS,CAAC;EAEtC,MAAMC,QAAQ,GAAGpD,IAAI,CAACc,IAAI,CAACsC,QAAQ,IAAI,EAAE;EACzC,KAAK,MAAMC,OAAO,IAAID,QAAQ,EAAE;IAC9B,IAAIC,OAAO,CAACC,MAAM,KAAK9C,SAAS,EAAE;MAChCuC,YAAY,CAACQ,GAAG,CAACF,OAAO,CAACC,MAAM,CAAC;IAClC;EACF;EAEA,OAAOrB,KAAK,CAACuB,IAAI,CAACT,YAAY,CAAC,CAACU,IAAI,CAAC,CAAC;AACxC;AAGA,eAAeP,SAASA,CACtBlD,IAAqB,EACrB0D,KAAK,EACLC,KAAa,EACb9D,OAAO,EACPY,OAAsB,EACtB;EACA,MAAM;IAACmC,KAAK;IAAEgB;EAAK,CAAC,GAAGnD,OAAO;EAE9B,IAAIb,WAAW;EAEf,IAAI8D,KAAK,CAACnC,GAAG,IAAI,CAACmC,KAAK,CAACG,cAAc,CAAC,YAAY,CAAC,EAAE;IACpD,MAAMtC,GAAG,GAAGpC,UAAU,CAACuE,KAAK,CAACnC,GAAG,EAAE1B,OAAO,CAAC;IAC1C,MAAMgD,QAAQ,GAAG,MAAMD,KAAK,CAACrB,GAAG,CAAC;IACjC3B,WAAW,GAAG,MAAMiD,QAAQ,CAACjD,WAAW,CAAC,CAAC;IAC1C8D,KAAK,CAACI,UAAU,GAAG;MACjBxC,IAAI,EAAE1B;IACR,CAAC;EACH;EAEA,IAAImE,MAAM,CAACC,QAAQ,CAACN,KAAK,CAACI,UAAU,CAAC,EAAE;IACrC,MAAMG,KAAK,GAAG7E,0BAA0B,CAACY,IAAI,CAACc,IAAI,EAAEd,IAAI,CAACe,OAAO,EAAE2C,KAAK,CAACI,UAAU,CAAC;IACnFlE,WAAW,GAAGX,gBAAgB,CAACgF,KAAK,CAACxB,MAAM,EAAEwB,KAAK,CAACnE,UAAU,EAAEmE,KAAK,CAAC3B,UAAU,CAAC;EAClF;EAEApD,MAAM,CAACU,WAAW,EAAE,wBAAwB,CAAC;EAG7C,IAAIsE,WAAW,GAAG,MAAMN,KAAK,CAC3BhE,WAAW,EACX,CAACb,WAAW,EAAEF,WAAW,CAAC,EAC1B;IAACsF,QAAQ,EAAET,KAAK,CAACS,QAAQ;IAAEC,KAAK,EAAEvE,OAAO,CAACuE,KAAK,IAAI;MAACC,MAAM,EAAEvF,0BAA0B,CAAC;IAAC;EAAC,CAAC,EAC1F2B,OACF,CAAC;EAED,IAAIyD,WAAW,IAAIA,WAAW,CAAC,CAAC,CAAC,EAAE;IACjCA,WAAW,GAAG;MACZI,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,KAAK;MACdC,KAAK,EAAEN,WAAW,CAAC,CAAC,CAAC,CAACM,KAAK;MAC3BC,MAAM,EAAEP,WAAW,CAAC,CAAC,CAAC,CAACO,MAAM;MAC7BnD,IAAI,EAAE4C,WAAW,CAAC,CAAC;IACrB,CAAC;EACH;EAKAlE,IAAI,CAACuC,MAAM,GAAGvC,IAAI,CAACuC,MAAM,IAAI,EAAE;EAC/BvC,IAAI,CAACuC,MAAM,CAACoB,KAAK,CAAC,GAAGO,WAAW;AAClC"}