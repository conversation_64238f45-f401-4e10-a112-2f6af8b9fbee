{"version": 3, "file": "get-typed-array.js", "names": ["assert", "getTypedArrayForBufferView", "json", "buffers", "bufferViewIndex", "bufferView", "bufferViews", "bufferIndex", "buffer", "binChunk", "byteOffset", "Uint8Array", "arrayBuffer", "byteLength", "getTypedArrayForImageData", "imageIndex", "image", "images"], "sources": ["../../../../src/lib/gltf-utils/get-typed-array.ts"], "sourcesContent": ["// TODO - GLTFScenegraph should use these\nimport {assert} from '../utils/assert';\n\n// accepts buffer view index or buffer view object\n// returns a `Uint8Array`\nexport function getTypedArrayForBufferView(json, buffers, bufferViewIndex) {\n  const bufferView = json.bufferViews[bufferViewIndex];\n  assert(bufferView);\n\n  // Get hold of the arrayBuffer\n  const bufferIndex = bufferView.buffer;\n  const binChunk = buffers[bufferIndex];\n  assert(binChunk);\n\n  const byteOffset = (bufferView.byteOffset || 0) + binChunk.byteOffset;\n  return new Uint8Array(binChunk.arrayBuffer, byteOffset, bufferView.byteLength);\n}\n\n// accepts accessor index or accessor object\n// returns a `Uint8Array`\nexport function getTypedArrayForImageData(json, buffers, imageIndex) {\n  const image = json.images[imageIndex];\n  const bufferViewIndex = json.bufferViews[image.bufferView];\n  return getTypedArrayForBufferView(json, buffers, bufferViewIndex);\n}\n\n/*\n// accepts accessor index or accessor object\n// returns a typed array with type that matches the types\nexport function getTypedArrayForAccessor(accessor) {\n  accessor = this.getAccessor(accessor);\n  const bufferView = this.getBufferView(accessor.bufferView);\n  const buffer = this.getBuffer(bufferView.buffer);\n  const arrayBuffer = buffer.data;\n\n  // Create a new typed array as a view into the combined buffer\n  const {ArrayType, length} = getAccessorArrayTypeAndLength(accessor, bufferView);\n  const byteOffset = bufferView.byteOffset + accessor.byteOffset;\n  return new ArrayType(arrayBuffer, byteOffset, length);\n}\n*/\n"], "mappings": "AACA,SAAQA,MAAM,QAAO,iBAAiB;AAItC,OAAO,SAASC,0BAA0BA,CAACC,IAAI,EAAEC,OAAO,EAAEC,eAAe,EAAE;EACzE,MAAMC,UAAU,GAAGH,IAAI,CAACI,WAAW,CAACF,eAAe,CAAC;EACpDJ,MAAM,CAACK,UAAU,CAAC;EAGlB,MAAME,WAAW,GAAGF,UAAU,CAACG,MAAM;EACrC,MAAMC,QAAQ,GAAGN,OAAO,CAACI,WAAW,CAAC;EACrCP,MAAM,CAACS,QAAQ,CAAC;EAEhB,MAAMC,UAAU,GAAG,CAACL,UAAU,CAACK,UAAU,IAAI,CAAC,IAAID,QAAQ,CAACC,UAAU;EACrE,OAAO,IAAIC,UAAU,CAACF,QAAQ,CAACG,WAAW,EAAEF,UAAU,EAAEL,UAAU,CAACQ,UAAU,CAAC;AAChF;AAIA,OAAO,SAASC,yBAAyBA,CAACZ,IAAI,EAAEC,OAAO,EAAEY,UAAU,EAAE;EACnE,MAAMC,KAAK,GAAGd,IAAI,CAACe,MAAM,CAACF,UAAU,CAAC;EACrC,MAAMX,eAAe,GAAGF,IAAI,CAACI,WAAW,CAACU,KAAK,CAACX,UAAU,CAAC;EAC1D,OAAOJ,0BAA0B,CAACC,IAAI,EAAEC,OAAO,EAAEC,eAAe,CAAC;AACnE"}