{"version": 3, "file": "gltf-utils.js", "names": ["assert", "TYPES", "ARRAY_CONSTRUCTOR_TO_WEBGL_CONSTANT", "Int8Array", "Uint8Array", "Int16Array", "Uint16Array", "Uint32Array", "Float32Array", "Float64Array", "ARRAY_TO_COMPONENT_TYPE", "Map", "ATTRIBUTE_TYPE_TO_COMPONENTS", "SCALAR", "VEC2", "VEC3", "VEC4", "MAT2", "MAT3", "MAT4", "ATTRIBUTE_COMPONENT_TYPE_TO_BYTE_SIZE", "ATTRIBUTE_COMPONENT_TYPE_TO_ARRAY", "getAccessorTypeFromSize", "size", "type", "getComponentTypeFromArray", "typedArray", "componentType", "get", "constructor", "Error", "getAccessorArrayTypeAndLength", "accessor", "bufferView", "ArrayType", "components", "bytesPerComponent", "length", "count", "byteLength", "getMemoryUsageGLTF", "gltf", "images", "bufferViews", "imageBufferViews", "map", "i", "filter", "view", "includes", "bufferMemory", "reduce", "acc", "pixelCount", "image", "width", "height", "Math", "ceil"], "sources": ["../../../../src/lib/gltf-utils/gltf-utils.ts"], "sourcesContent": ["import {assert} from '../utils/assert';\nimport {GLTF} from '../types/gltf-types';\n\nconst TYPES = ['SCALAR', 'VEC2', 'VEC3', 'VEC4'];\n\ntype TypedArrayConstructor =\n  | Int8ArrayConstructor\n  | Uint8ArrayConstructor\n  | Int16ArrayConstructor\n  | Uint16ArrayConstructor\n  | Int32ArrayConstructor\n  | Uint32ArrayConstructor\n  | Int32ArrayConstructor\n  | Uint32ArrayConstructor\n  | Float32ArrayConstructor\n  | Float64ArrayConstructor;\n\nconst ARRAY_CONSTRUCTOR_TO_WEBGL_CONSTANT: [TypedArrayConstructor, number][] = [\n  [Int8Array, 5120],\n  [Uint8Array, 5121],\n  [Int16Array, 5122],\n  [Uint16Array, 5123],\n  [Uint32Array, 5125],\n  [Float32Array, 5126],\n  [Float64Array, 5130]\n];\nconst ARRAY_TO_COMPONENT_TYPE = new Map<TypedArrayConstructor, number>(\n  ARRAY_CONSTRUCTOR_TO_WEBGL_CONSTANT\n);\n\nconst ATTRIBUTE_TYPE_TO_COMPONENTS = {\n  SCALAR: 1,\n  VEC2: 2,\n  VEC3: 3,\n  VEC4: 4,\n  MAT2: 4,\n  MAT3: 9,\n  MAT4: 16\n};\n\nconst ATTRIBUTE_COMPONENT_TYPE_TO_BYTE_SIZE = {\n  5120: 1,\n  5121: 1,\n  5122: 2,\n  5123: 2,\n  5125: 4,\n  5126: 4\n};\n\nconst ATTRIBUTE_COMPONENT_TYPE_TO_ARRAY = {\n  5120: Int8Array,\n  5121: Uint8Array,\n  5122: Int16Array,\n  5123: Uint16Array,\n  5125: Uint32Array,\n  5126: Float32Array\n};\n\nexport function getAccessorTypeFromSize(size) {\n  const type = TYPES[size - 1];\n  return type || TYPES[0];\n}\n\nexport function getComponentTypeFromArray(typedArray) {\n  const componentType = ARRAY_TO_COMPONENT_TYPE.get(typedArray.constructor);\n  if (!componentType) {\n    throw new Error('Illegal typed array');\n  }\n  return componentType;\n}\n\nexport function getAccessorArrayTypeAndLength(accessor, bufferView) {\n  const ArrayType = ATTRIBUTE_COMPONENT_TYPE_TO_ARRAY[accessor.componentType];\n  const components = ATTRIBUTE_TYPE_TO_COMPONENTS[accessor.type];\n  const bytesPerComponent = ATTRIBUTE_COMPONENT_TYPE_TO_BYTE_SIZE[accessor.componentType];\n  const length = accessor.count * components;\n  const byteLength = accessor.count * components * bytesPerComponent;\n  assert(byteLength >= 0 && byteLength <= bufferView.byteLength);\n  return {ArrayType, length, byteLength};\n}\n\n/**\n * Calculate the GPU memory used by a GLTF tile, for both buffer and texture memory\n * @param gltf - the gltf content of a GLTF tile\n * @returns - total memory usage in bytes\n */\nexport function getMemoryUsageGLTF(gltf: GLTF): number {\n  let {images, bufferViews} = gltf;\n  images = images || [];\n  bufferViews = bufferViews || [];\n  const imageBufferViews = images.map((i) => i.bufferView);\n  bufferViews = bufferViews.filter((view) => !imageBufferViews.includes(view as any));\n\n  const bufferMemory = bufferViews.reduce((acc, view) => acc + view.byteLength, 0);\n\n  // Assume each pixel of the texture is 4 channel with mimmaps (which add 33%)\n  // TODO correctly handle compressed textures\n  const pixelCount = images.reduce((acc, image) => {\n    // @ts-ignore\n    const {width, height} = (image as any).image;\n    return acc + width * height;\n  }, 0);\n  return bufferMemory + Math.ceil(4 * pixelCount * 1.33);\n}\n"], "mappings": "AAAA,SAAQA,MAAM,QAAO,iBAAiB;AAGtC,MAAMC,KAAK,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;AAchD,MAAMC,mCAAsE,GAAG,CAC7E,CAACC,SAAS,EAAE,IAAI,CAAC,EACjB,CAACC,UAAU,EAAE,IAAI,CAAC,EAClB,CAACC,UAAU,EAAE,IAAI,CAAC,EAClB,CAACC,WAAW,EAAE,IAAI,CAAC,EACnB,CAACC,WAAW,EAAE,IAAI,CAAC,EACnB,CAACC,YAAY,EAAE,IAAI,CAAC,EACpB,CAACC,YAAY,EAAE,IAAI,CAAC,CACrB;AACD,MAAMC,uBAAuB,GAAG,IAAIC,GAAG,CACrCT,mCACF,CAAC;AAED,MAAMU,4BAA4B,GAAG;EACnCC,MAAM,EAAE,CAAC;EACTC,IAAI,EAAE,CAAC;EACPC,IAAI,EAAE,CAAC;EACPC,IAAI,EAAE,CAAC;EACPC,IAAI,EAAE,CAAC;EACPC,IAAI,EAAE,CAAC;EACPC,IAAI,EAAE;AACR,CAAC;AAED,MAAMC,qCAAqC,GAAG;EAC5C,IAAI,EAAE,CAAC;EACP,IAAI,EAAE,CAAC;EACP,IAAI,EAAE,CAAC;EACP,IAAI,EAAE,CAAC;EACP,IAAI,EAAE,CAAC;EACP,IAAI,EAAE;AACR,CAAC;AAED,MAAMC,iCAAiC,GAAG;EACxC,IAAI,EAAElB,SAAS;EACf,IAAI,EAAEC,UAAU;EAChB,IAAI,EAAEC,UAAU;EAChB,IAAI,EAAEC,WAAW;EACjB,IAAI,EAAEC,WAAW;EACjB,IAAI,EAAEC;AACR,CAAC;AAED,OAAO,SAASc,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,MAAMC,IAAI,GAAGvB,KAAK,CAACsB,IAAI,GAAG,CAAC,CAAC;EAC5B,OAAOC,IAAI,IAAIvB,KAAK,CAAC,CAAC,CAAC;AACzB;AAEA,OAAO,SAASwB,yBAAyBA,CAACC,UAAU,EAAE;EACpD,MAAMC,aAAa,GAAGjB,uBAAuB,CAACkB,GAAG,CAACF,UAAU,CAACG,WAAW,CAAC;EACzE,IAAI,CAACF,aAAa,EAAE;IAClB,MAAM,IAAIG,KAAK,CAAC,qBAAqB,CAAC;EACxC;EACA,OAAOH,aAAa;AACtB;AAEA,OAAO,SAASI,6BAA6BA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAClE,MAAMC,SAAS,GAAGb,iCAAiC,CAACW,QAAQ,CAACL,aAAa,CAAC;EAC3E,MAAMQ,UAAU,GAAGvB,4BAA4B,CAACoB,QAAQ,CAACR,IAAI,CAAC;EAC9D,MAAMY,iBAAiB,GAAGhB,qCAAqC,CAACY,QAAQ,CAACL,aAAa,CAAC;EACvF,MAAMU,MAAM,GAAGL,QAAQ,CAACM,KAAK,GAAGH,UAAU;EAC1C,MAAMI,UAAU,GAAGP,QAAQ,CAACM,KAAK,GAAGH,UAAU,GAAGC,iBAAiB;EAClEpC,MAAM,CAACuC,UAAU,IAAI,CAAC,IAAIA,UAAU,IAAIN,UAAU,CAACM,UAAU,CAAC;EAC9D,OAAO;IAACL,SAAS;IAAEG,MAAM;IAAEE;EAAU,CAAC;AACxC;AAOA,OAAO,SAASC,kBAAkBA,CAACC,IAAU,EAAU;EACrD,IAAI;IAACC,MAAM;IAAEC;EAAW,CAAC,GAAGF,IAAI;EAChCC,MAAM,GAAGA,MAAM,IAAI,EAAE;EACrBC,WAAW,GAAGA,WAAW,IAAI,EAAE;EAC/B,MAAMC,gBAAgB,GAAGF,MAAM,CAACG,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACb,UAAU,CAAC;EACxDU,WAAW,GAAGA,WAAW,CAACI,MAAM,CAAEC,IAAI,IAAK,CAACJ,gBAAgB,CAACK,QAAQ,CAACD,IAAW,CAAC,CAAC;EAEnF,MAAME,YAAY,GAAGP,WAAW,CAACQ,MAAM,CAAC,CAACC,GAAG,EAAEJ,IAAI,KAAKI,GAAG,GAAGJ,IAAI,CAACT,UAAU,EAAE,CAAC,CAAC;EAIhF,MAAMc,UAAU,GAAGX,MAAM,CAACS,MAAM,CAAC,CAACC,GAAG,EAAEE,KAAK,KAAK;IAE/C,MAAM;MAACC,KAAK;MAAEC;IAAM,CAAC,GAAIF,KAAK,CAASA,KAAK;IAC5C,OAAOF,GAAG,GAAGG,KAAK,GAAGC,MAAM;EAC7B,CAAC,EAAE,CAAC,CAAC;EACL,OAAON,YAAY,GAAGO,IAAI,CAACC,IAAI,CAAC,CAAC,GAAGL,UAAU,GAAG,IAAI,CAAC;AACxD"}