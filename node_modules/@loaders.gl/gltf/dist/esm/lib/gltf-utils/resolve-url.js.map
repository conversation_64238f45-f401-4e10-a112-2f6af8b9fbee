{"version": 3, "file": "resolve-url.js", "names": ["resolveUrl", "url", "options", "absolute", "startsWith", "baseUrl", "baseUri", "uri", "Error", "concat", "substr", "lastIndexOf"], "sources": ["../../../../src/lib/gltf-utils/resolve-url.ts"], "sourcesContent": ["// Resolves a relative url against a baseUrl\n// If url is absolute, return it unchanged\nexport function resolveUrl(url, options) {\n  // TODO: Use better logic to handle all protocols plus not delay on data\n  const absolute = url.startsWith('data:') || url.startsWith('http:') || url.startsWith('https:');\n  if (absolute) {\n    return url;\n  }\n  const baseUrl = options.baseUri || options.uri;\n  if (!baseUrl) {\n    throw new Error(`'baseUri' must be provided to resolve relative url ${url}`);\n  }\n  return baseUrl.substr(0, baseUrl.lastIndexOf('/') + 1) + url;\n}\n"], "mappings": "AAEA,OAAO,SAASA,UAAUA,CAACC,GAAG,EAAEC,OAAO,EAAE;EAEvC,MAAMC,QAAQ,GAAGF,GAAG,CAACG,UAAU,CAAC,OAAO,CAAC,IAAIH,GAAG,CAACG,UAAU,CAAC,OAAO,CAAC,IAAIH,GAAG,CAACG,UAAU,CAAC,QAAQ,CAAC;EAC/F,IAAID,QAAQ,EAAE;IACZ,OAAOF,GAAG;EACZ;EACA,MAAMI,OAAO,GAAGH,OAAO,CAACI,OAAO,IAAIJ,OAAO,CAACK,GAAG;EAC9C,IAAI,CAACF,OAAO,EAAE;IACZ,MAAM,IAAIG,KAAK,uDAAAC,MAAA,CAAuDR,GAAG,CAAE,CAAC;EAC9E;EACA,OAAOI,OAAO,CAACK,MAAM,CAAC,CAAC,EAAEL,OAAO,CAACM,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGV,GAAG;AAC9D"}