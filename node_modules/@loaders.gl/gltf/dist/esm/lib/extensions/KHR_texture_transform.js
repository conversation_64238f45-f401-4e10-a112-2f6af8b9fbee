import { Vector3, Matrix3 } from '@math.gl/core';
import { getAccessorArrayTypeAndLength } from '../gltf-utils/gltf-utils';
import { BYTES, COMPONENTS } from '../gltf-utils/gltf-constants';
import GLTFScenegraph from '../api/gltf-scenegraph';
const EXT_MESHOPT_TRANSFORM = 'KHR_texture_transform';
export const name = EXT_MESHOPT_TRANSFORM;
const scratchVector = new Vector3();
const scratchRotationMatrix = new Matrix3();
const scratchScaleMatrix = new Matrix3();
export async function decode(gltfData, options) {
  const gltfScenegraph = new GLTFScenegraph(gltfData);
  const extension = gltfScenegraph.getExtension(EXT_MESHOPT_TRANSFORM);
  if (!extension) {
    return;
  }
  const materials = gltfData.json.materials || [];
  for (let i = 0; i < materials.length; i++) {
    transformTexCoords(i, gltfData);
  }
}
function transformTexCoords(materialIndex, gltfData) {
  var _gltfData$json$materi, _material$pbrMetallic, _material$pbrMetallic2;
  const processedTexCoords = [];
  const material = (_gltfData$json$materi = gltfData.json.materials) === null || _gltfData$json$materi === void 0 ? void 0 : _gltfData$json$materi[materialIndex];
  const baseColorTexture = material === null || material === void 0 ? void 0 : (_material$pbrMetallic = material.pbrMetallicRoughness) === null || _material$pbrMetallic === void 0 ? void 0 : _material$pbrMetallic.baseColorTexture;
  if (baseColorTexture) {
    transformPrimitives(gltfData, materialIndex, baseColorTexture, processedTexCoords);
  }
  const emisiveTexture = material === null || material === void 0 ? void 0 : material.emissiveTexture;
  if (emisiveTexture) {
    transformPrimitives(gltfData, materialIndex, emisiveTexture, processedTexCoords);
  }
  const normalTexture = material === null || material === void 0 ? void 0 : material.normalTexture;
  if (normalTexture) {
    transformPrimitives(gltfData, materialIndex, normalTexture, processedTexCoords);
  }
  const occlusionTexture = material === null || material === void 0 ? void 0 : material.occlusionTexture;
  if (occlusionTexture) {
    transformPrimitives(gltfData, materialIndex, occlusionTexture, processedTexCoords);
  }
  const metallicRoughnessTexture = material === null || material === void 0 ? void 0 : (_material$pbrMetallic2 = material.pbrMetallicRoughness) === null || _material$pbrMetallic2 === void 0 ? void 0 : _material$pbrMetallic2.metallicRoughnessTexture;
  if (metallicRoughnessTexture) {
    transformPrimitives(gltfData, materialIndex, metallicRoughnessTexture, processedTexCoords);
  }
}
function transformPrimitives(gltfData, materialIndex, texture, processedTexCoords) {
  const transformParameters = getTransformParameters(texture, processedTexCoords);
  if (!transformParameters) {
    return;
  }
  const meshes = gltfData.json.meshes || [];
  for (const mesh of meshes) {
    for (const primitive of mesh.primitives) {
      const material = primitive.material;
      if (Number.isFinite(material) && materialIndex === material) {
        transformPrimitive(gltfData, primitive, transformParameters);
      }
    }
  }
}
function getTransformParameters(texture, processedTexCoords) {
  var _texture$extensions;
  const textureInfo = (_texture$extensions = texture.extensions) === null || _texture$extensions === void 0 ? void 0 : _texture$extensions[EXT_MESHOPT_TRANSFORM];
  const {
    texCoord: originalTexCoord = 0
  } = texture;
  const {
    texCoord = originalTexCoord
  } = textureInfo;
  const isProcessed = processedTexCoords.findIndex(_ref => {
    let [original, newTexCoord] = _ref;
    return original === originalTexCoord && newTexCoord === texCoord;
  }) !== -1;
  if (!isProcessed) {
    const matrix = makeTransformationMatrix(textureInfo);
    if (originalTexCoord !== texCoord) {
      texture.texCoord = texCoord;
    }
    processedTexCoords.push([originalTexCoord, texCoord]);
    return {
      originalTexCoord,
      texCoord,
      matrix
    };
  }
  return null;
}
function transformPrimitive(gltfData, primitive, transformParameters) {
  const {
    originalTexCoord,
    texCoord,
    matrix
  } = transformParameters;
  const texCoordAccessor = primitive.attributes["TEXCOORD_".concat(originalTexCoord)];
  if (Number.isFinite(texCoordAccessor)) {
    var _gltfData$json$access;
    const accessor = (_gltfData$json$access = gltfData.json.accessors) === null || _gltfData$json$access === void 0 ? void 0 : _gltfData$json$access[texCoordAccessor];
    if (accessor && accessor.bufferView) {
      var _gltfData$json$buffer;
      const bufferView = (_gltfData$json$buffer = gltfData.json.bufferViews) === null || _gltfData$json$buffer === void 0 ? void 0 : _gltfData$json$buffer[accessor.bufferView];
      if (bufferView) {
        const {
          arrayBuffer,
          byteOffset: bufferByteOffset
        } = gltfData.buffers[bufferView.buffer];
        const byteOffset = (bufferByteOffset || 0) + (accessor.byteOffset || 0) + (bufferView.byteOffset || 0);
        const {
          ArrayType,
          length
        } = getAccessorArrayTypeAndLength(accessor, bufferView);
        const bytes = BYTES[accessor.componentType];
        const components = COMPONENTS[accessor.type];
        const elementAddressScale = bufferView.byteStride || bytes * components;
        const result = new Float32Array(length);
        for (let i = 0; i < accessor.count; i++) {
          const uv = new ArrayType(arrayBuffer, byteOffset + i * elementAddressScale, 2);
          scratchVector.set(uv[0], uv[1], 1);
          scratchVector.transformByMatrix3(matrix);
          result.set([scratchVector[0], scratchVector[1]], i * components);
        }
        if (originalTexCoord === texCoord) {
          updateGltf(accessor, bufferView, gltfData.buffers, result);
        } else {
          createAttribute(texCoord, accessor, primitive, gltfData, result);
        }
      }
    }
  }
}
function updateGltf(accessor, bufferView, buffers, newTexCoordArray) {
  accessor.componentType = 5126;
  buffers.push({
    arrayBuffer: newTexCoordArray.buffer,
    byteOffset: 0,
    byteLength: newTexCoordArray.buffer.byteLength
  });
  bufferView.buffer = buffers.length - 1;
  bufferView.byteLength = newTexCoordArray.buffer.byteLength;
  bufferView.byteOffset = 0;
  delete bufferView.byteStride;
}
function createAttribute(newTexCoord, originalAccessor, primitive, gltfData, newTexCoordArray) {
  gltfData.buffers.push({
    arrayBuffer: newTexCoordArray.buffer,
    byteOffset: 0,
    byteLength: newTexCoordArray.buffer.byteLength
  });
  const bufferViews = gltfData.json.bufferViews;
  if (!bufferViews) {
    return;
  }
  bufferViews.push({
    buffer: gltfData.buffers.length - 1,
    byteLength: newTexCoordArray.buffer.byteLength,
    byteOffset: 0
  });
  const accessors = gltfData.json.accessors;
  if (!accessors) {
    return;
  }
  accessors.push({
    bufferView: (bufferViews === null || bufferViews === void 0 ? void 0 : bufferViews.length) - 1,
    byteOffset: 0,
    componentType: 5126,
    count: originalAccessor.count,
    type: 'VEC2'
  });
  primitive.attributes["TEXCOORD_".concat(newTexCoord)] = accessors.length - 1;
}
function makeTransformationMatrix(extensionData) {
  const {
    offset = [0, 0],
    rotation = 0,
    scale = [1, 1]
  } = extensionData;
  const translationMatirx = new Matrix3().set(1, 0, 0, 0, 1, 0, offset[0], offset[1], 1);
  const rotationMatirx = scratchRotationMatrix.set(Math.cos(rotation), Math.sin(rotation), 0, -Math.sin(rotation), Math.cos(rotation), 0, 0, 0, 1);
  const scaleMatrix = scratchScaleMatrix.set(scale[0], 0, 0, 0, scale[1], 0, 0, 0, 1);
  return translationMatirx.multiplyRight(rotationMatirx).multiplyRight(scaleMatrix);
}
//# sourceMappingURL=KHR_texture_transform.js.map