{"version": 3, "file": "KHR_texture_basisu.js", "names": ["GLTFScenegraph", "KHR_TEXTURE_BASISU", "name", "preprocess", "gltfData", "options", "scene", "json", "texture", "textures", "extension", "getObjectExtension", "source", "removeObjectExtension", "removeExtension"], "sources": ["../../../../src/lib/extensions/KHR_texture_basisu.ts"], "sourcesContent": ["// GLTF EXTENSION: KHR_texture_basisu\n// https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_texture_basisu\n/* eslint-disable camelcase */\n\nimport type {GLTF, GLTF_KHR_texture_basisu} from '../types/gltf-types';\nimport type {GLTFLoaderOptions} from '../../gltf-loader';\n\nimport GLTFScenegraph from '../api/gltf-scenegraph';\n\nconst KHR_TEXTURE_BASISU = 'KHR_texture_basisu';\n\n/** Extension name */\nexport const name = KHR_TEXTURE_BASISU;\n\n/**\n * Replaces a texture source reference with the extension texture\n * Done in preprocess() to prevent load of default image\n */\nexport function preprocess(gltfData: {json: GLTF}, options: GLTFLoaderOptions): void {\n  const scene = new GLTFScenegraph(gltfData);\n  const {json} = scene;\n\n  for (const texture of json.textures || []) {\n    const extension = scene.getObjectExtension<GLTF_KHR_texture_basisu>(\n      texture,\n      KHR_TEXTURE_BASISU\n    );\n    if (extension) {\n      // TODO - if multiple texture extensions are present which one wins?\n      texture.source = extension.source;\n    }\n    scene.removeObjectExtension(texture, KHR_TEXTURE_BASISU);\n  }\n\n  // Remove the top-level extension\n  scene.removeExtension(KHR_TEXTURE_BASISU);\n}\n"], "mappings": "AAOA,OAAOA,cAAc,MAAM,wBAAwB;AAEnD,MAAMC,kBAAkB,GAAG,oBAAoB;AAG/C,OAAO,MAAMC,IAAI,GAAGD,kBAAkB;AAMtC,OAAO,SAASE,UAAUA,CAACC,QAAsB,EAAEC,OAA0B,EAAQ;EACnF,MAAMC,KAAK,GAAG,IAAIN,cAAc,CAACI,QAAQ,CAAC;EAC1C,MAAM;IAACG;EAAI,CAAC,GAAGD,KAAK;EAEpB,KAAK,MAAME,OAAO,IAAID,IAAI,CAACE,QAAQ,IAAI,EAAE,EAAE;IACzC,MAAMC,SAAS,GAAGJ,KAAK,CAACK,kBAAkB,CACxCH,OAAO,EACPP,kBACF,CAAC;IACD,IAAIS,SAAS,EAAE;MAEbF,OAAO,CAACI,MAAM,GAAGF,SAAS,CAACE,MAAM;IACnC;IACAN,KAAK,CAACO,qBAAqB,CAACL,OAAO,EAAEP,kBAAkB,CAAC;EAC1D;EAGAK,KAAK,CAACQ,eAAe,CAACb,kBAAkB,CAAC;AAC3C"}