import { isImageFormatSupported } from '@loaders.gl/images';
import GLTFScenegraph from '../api/gltf-scenegraph';
const EXT_TEXTURE_WEBP = 'EXT_texture_webp';
export const name = EXT_TEXTURE_WEBP;
export function preprocess(gltfData, options) {
  const scenegraph = new GLTFScenegraph(gltfData);
  if (!isImageFormatSupported('image/webp')) {
    if (scenegraph.getRequiredExtensions().includes(EXT_TEXTURE_WEBP)) {
      throw new Error("gltf: Required extension ".concat(EXT_TEXTURE_WEBP, " not supported by browser"));
    }
    return;
  }
  const {
    json
  } = scenegraph;
  for (const texture of json.textures || []) {
    const extension = scenegraph.getObjectExtension(texture, EXT_TEXTURE_WEBP);
    if (extension) {
      texture.source = extension.source;
    }
    scenegraph.removeObjectExtension(texture, EXT_TEXTURE_WEBP);
  }
  scenegraph.removeExtension(EXT_TEXTURE_WEBP);
}
//# sourceMappingURL=EXT_texture_webp.js.map