import GLTFScenegraph from '../api/gltf-scenegraph';
import { meshoptDecodeGltfBuffer } from '../../meshopt/meshopt-decoder';
const DEFAULT_MESHOPT_OPTIONS = {
  byteOffset: 0,
  filter: 'NONE'
};
const EXT_MESHOPT_COMPRESSION = 'EXT_meshopt_compression';
export const name = EXT_MESHOPT_COMPRESSION;
export async function decode(gltfData, options) {
  var _options$gltf;
  const scenegraph = new GLTFScenegraph(gltfData);
  if (!(options !== null && options !== void 0 && (_options$gltf = options.gltf) !== null && _options$gltf !== void 0 && _options$gltf.decompressMeshes)) {
    return;
  }
  const promises = [];
  for (const bufferViewIndex of gltfData.json.bufferViews || []) {
    promises.push(decodeMeshoptBufferView(scenegraph, bufferViewIndex));
  }
  await Promise.all(promises);
  scenegraph.removeExtension(EXT_MESHOPT_COMPRESSION);
}
async function decodeMeshoptBufferView(scenegraph, bufferView) {
  const meshoptExtension = scenegraph.getObjectExtension(bufferView, EXT_MESHOPT_COMPRESSION);
  if (meshoptExtension) {
    const {
      byteOffset = 0,
      byteLength = 0,
      byteStride,
      count,
      mode,
      filter = 'NONE',
      buffer: bufferIndex
    } = meshoptExtension;
    const buffer = scenegraph.gltf.buffers[bufferIndex];
    const source = new Uint8Array(buffer.arrayBuffer, buffer.byteOffset + byteOffset, byteLength);
    const result = new Uint8Array(scenegraph.gltf.buffers[bufferView.buffer].arrayBuffer, bufferView.byteOffset, bufferView.byteLength);
    await meshoptDecodeGltfBuffer(result, count, byteStride, source, mode, filter);
    return result;
  }
  return null;
}
//# sourceMappingURL=EXT_meshopt_compression.js.map