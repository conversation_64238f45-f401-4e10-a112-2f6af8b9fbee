{"version": 3, "file": "KHR_draco_mesh_compression.js", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "sliceArrayBuffer", "default", "Scenegraph", "getGLTFAccessors", "getGLTFAccessor", "KHR_DRACO_MESH_COMPRESSION", "name", "preprocess", "gltfData", "options", "context", "scenegraph", "primitive", "makeMeshPrimitiveIterator", "getObjectExtension", "decode", "_options$gltf", "gltf", "decompressMeshes", "promises", "push", "decompressPrimitive", "Promise", "all", "removeExtension", "encode", "arguments", "length", "undefined", "mesh", "json", "meshes", "compressMesh", "addRequiredExtension", "dracoExtension", "buffer", "getTypedArrayForBufferView", "bufferView", "bufferCopy", "byteOffset", "parse", "dracoOptions", "decodedData", "decodedAttributes", "attributes", "attributeName", "decodedAttribute", "Object", "entries", "accessorIndex", "accessor", "getAccessor", "min", "max", "indices", "checkPrimitive", "_context$parseSync", "mode", "DracoWriter", "Error", "compressedData", "encodeSync", "parseSync", "call", "fauxAccessors", "_addFauxAttributes", "bufferViewIndex", "addBufferView", "glTFMesh", "primitives", "extensions", "keys"], "sources": ["../../../../src/lib/extensions/KHR_draco_mesh_compression.ts"], "sourcesContent": ["// https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_draco_mesh_compression\n// Only TRIANGLES: 0x0004 and TRIANGLE_STRIP: 0x0005 are supported\n/* eslint-disable camelcase */\n\n/* eslint-disable camelcase */\nimport type {\n  GLTF,\n  GLTFAccessor,\n  GLTFMeshPrimitive,\n  GLTF_KHR_draco_mesh_compression\n} from '../types/gltf-types';\nimport type {GLTFLoaderOptions} from '../../gltf-loader';\n\nimport type {LoaderContext} from '@loaders.gl/loader-utils';\nimport {DracoLoader} from '@loaders.gl/draco';\nimport {DracoLoaderOptions, DracoMesh} from '@loaders.gl/draco';\nimport {sliceArrayBuffer} from '@loaders.gl/loader-utils';\nimport {default as Scenegraph} from '../api/gltf-scenegraph';\nimport {getGLTFAccessors, getGLTFAccessor} from '../gltf-utils/gltf-attribute-utils';\n\nconst KHR_DRACO_MESH_COMPRESSION = 'KHR_draco_mesh_compression';\n\n/** Extension name */\nexport const name = KHR_DRACO_MESH_COMPRESSION;\n\nexport function preprocess(\n  gltfData: {json: GLTF},\n  options: GLTFLoaderOptions,\n  context: LoaderContext\n): void {\n  const scenegraph = new Scenegraph(gltfData);\n  for (const primitive of makeMeshPrimitiveIterator(scenegraph)) {\n    if (scenegraph.getObjectExtension(primitive, KHR_DRACO_MESH_COMPRESSION)) {\n      // TODO - Remove fallback accessors to make sure we don't load unnecessary buffers\n    }\n  }\n}\n\nexport async function decode(\n  gltfData: {json: GLTF},\n  options: GLTFLoaderOptions,\n  context: LoaderContext\n): Promise<void> {\n  if (!options?.gltf?.decompressMeshes) {\n    return;\n  }\n\n  const scenegraph = new Scenegraph(gltfData);\n  const promises: Promise<void>[] = [];\n  for (const primitive of makeMeshPrimitiveIterator(scenegraph)) {\n    if (scenegraph.getObjectExtension(primitive, KHR_DRACO_MESH_COMPRESSION)) {\n      promises.push(decompressPrimitive(scenegraph, primitive, options, context));\n    }\n  }\n\n  // Decompress meshes in parallel\n  await Promise.all(promises);\n\n  // We have now decompressed all primitives, so remove the top-level extension\n  scenegraph.removeExtension(KHR_DRACO_MESH_COMPRESSION);\n}\n\nexport function encode(gltfData, options: GLTFLoaderOptions = {}): void {\n  const scenegraph = new Scenegraph(gltfData);\n\n  for (const mesh of scenegraph.json.meshes || []) {\n    // eslint-disable-next-line camelcase\n    // @ts-ignore\n    compressMesh(mesh, options);\n    // NOTE: Only add the extension if something was actually compressed\n    scenegraph.addRequiredExtension(KHR_DRACO_MESH_COMPRESSION);\n  }\n}\n\n// DECODE\n\n// Unpacks one mesh primitive and removes the extension from the primitive\n// DracoDecoder needs to be imported and registered by app\n// Returns: Promise that resolves when all pending draco decoder jobs for this mesh complete\n\n// TODO - Implement fallback behavior per KHR_DRACO_MESH_COMPRESSION spec\n\nasync function decompressPrimitive(\n  scenegraph: Scenegraph,\n  primitive: GLTFMeshPrimitive,\n  options: GLTFLoaderOptions,\n  context: LoaderContext\n): Promise<void> {\n  const dracoExtension = scenegraph.getObjectExtension<GLTF_KHR_draco_mesh_compression>(\n    primitive,\n    KHR_DRACO_MESH_COMPRESSION\n  );\n  if (!dracoExtension) {\n    return;\n  }\n\n  const buffer = scenegraph.getTypedArrayForBufferView(dracoExtension.bufferView);\n  // TODO - parse does not yet deal well with byte offsets embedded in typed arrays. Copy buffer\n  // TODO - remove when `parse` is fixed to handle `byteOffset`s\n  const bufferCopy = sliceArrayBuffer(buffer.buffer, buffer.byteOffset); // , buffer.byteLength);\n\n  const {parse} = context;\n  const dracoOptions: DracoLoaderOptions = {...options};\n\n  // TODO - remove hack: The entire tileset might be included, too expensive to serialize\n  delete dracoOptions['3d-tiles'];\n  const decodedData = (await parse(bufferCopy, DracoLoader, dracoOptions, context)) as DracoMesh;\n\n  const decodedAttributes: {[key: string]: GLTFAccessor} = getGLTFAccessors(decodedData.attributes);\n\n  // Restore min/max values\n  for (const [attributeName, decodedAttribute] of Object.entries(decodedAttributes)) {\n    if (attributeName in primitive.attributes) {\n      const accessorIndex: number = primitive.attributes[attributeName];\n      const accessor = scenegraph.getAccessor(accessorIndex);\n      if (accessor?.min && accessor?.max) {\n        decodedAttribute.min = accessor.min;\n        decodedAttribute.max = accessor.max;\n      }\n    }\n  }\n\n  // @ts-ignore\n  primitive.attributes = decodedAttributes;\n  if (decodedData.indices) {\n    // @ts-ignore\n    primitive.indices = getGLTFAccessor(decodedData.indices);\n  }\n\n  // Extension has been processed, delete it\n  // delete primitive.extensions[KHR_DRACO_MESH_COMPRESSION];\n\n  checkPrimitive(primitive);\n}\n\n// ENCODE\n\n// eslint-disable-next-line max-len\n// Only TRIANGLES: 0x0004 and TRIANGLE_STRIP: 0x0005 are supported\nfunction compressMesh(attributes, indices, mode: number = 4, options, context: LoaderContext) {\n  if (!options.DracoWriter) {\n    throw new Error('options.gltf.DracoWriter not provided');\n  }\n\n  // TODO - use DracoWriter using encode w/ registered DracoWriter...\n  const compressedData = options.DracoWriter.encodeSync({attributes});\n\n  // Draco compression may change the order and number of vertices in a mesh.\n  // To satisfy the requirement that accessors properties be correct for both\n  // compressed and uncompressed data, generators should create uncompressed\n  // attributes and indices using data that has been decompressed from the Draco buffer,\n  // rather than the original source data.\n  // @ts-ignore TODO this needs to be fixed\n  const decodedData = context?.parseSync?.({attributes});\n  const fauxAccessors = options._addFauxAttributes(decodedData.attributes);\n\n  const bufferViewIndex = options.addBufferView(compressedData);\n\n  const glTFMesh = {\n    primitives: [\n      {\n        attributes: fauxAccessors, // TODO - verify with spec\n        mode, // GL.POINTS\n        extensions: {\n          [KHR_DRACO_MESH_COMPRESSION]: {\n            bufferView: bufferViewIndex,\n            attributes: fauxAccessors // TODO - verify with spec\n          }\n        }\n      }\n    ]\n  };\n\n  return glTFMesh;\n}\n\n// UTILS\n\nfunction checkPrimitive(primitive: GLTFMeshPrimitive) {\n  if (!primitive.attributes && Object.keys(primitive.attributes).length > 0) {\n    throw new Error('glTF: Empty primitive detected: Draco decompression failure?');\n  }\n}\n\nfunction* makeMeshPrimitiveIterator(scenegraph) {\n  for (const mesh of scenegraph.json.meshes || []) {\n    for (const primitive of mesh.primitives) {\n      yield primitive;\n    }\n  }\n}\n"], "mappings": "AAcA,SAAQA,WAAW,QAAO,mBAAmB;AAE7C,SAAQC,gBAAgB,QAAO,0BAA0B;AACzD,SAAQC,OAAO,IAAIC,UAAU,QAAO,wBAAwB;AAC5D,SAAQC,gBAAgB,EAAEC,eAAe,QAAO,oCAAoC;AAEpF,MAAMC,0BAA0B,GAAG,4BAA4B;AAG/D,OAAO,MAAMC,IAAI,GAAGD,0BAA0B;AAE9C,OAAO,SAASE,UAAUA,CACxBC,QAAsB,EACtBC,OAA0B,EAC1BC,OAAsB,EAChB;EACN,MAAMC,UAAU,GAAG,IAAIT,UAAU,CAACM,QAAQ,CAAC;EAC3C,KAAK,MAAMI,SAAS,IAAIC,yBAAyB,CAACF,UAAU,CAAC,EAAE;IAC7D,IAAIA,UAAU,CAACG,kBAAkB,CAACF,SAAS,EAAEP,0BAA0B,CAAC,EAAE,CAE1E;EACF;AACF;AAEA,OAAO,eAAeU,MAAMA,CAC1BP,QAAsB,EACtBC,OAA0B,EAC1BC,OAAsB,EACP;EAAA,IAAAM,aAAA;EACf,IAAI,EAACP,OAAO,aAAPA,OAAO,gBAAAO,aAAA,GAAPP,OAAO,CAAEQ,IAAI,cAAAD,aAAA,eAAbA,aAAA,CAAeE,gBAAgB,GAAE;IACpC;EACF;EAEA,MAAMP,UAAU,GAAG,IAAIT,UAAU,CAACM,QAAQ,CAAC;EAC3C,MAAMW,QAAyB,GAAG,EAAE;EACpC,KAAK,MAAMP,SAAS,IAAIC,yBAAyB,CAACF,UAAU,CAAC,EAAE;IAC7D,IAAIA,UAAU,CAACG,kBAAkB,CAACF,SAAS,EAAEP,0BAA0B,CAAC,EAAE;MACxEc,QAAQ,CAACC,IAAI,CAACC,mBAAmB,CAACV,UAAU,EAAEC,SAAS,EAAEH,OAAO,EAAEC,OAAO,CAAC,CAAC;IAC7E;EACF;EAGA,MAAMY,OAAO,CAACC,GAAG,CAACJ,QAAQ,CAAC;EAG3BR,UAAU,CAACa,eAAe,CAACnB,0BAA0B,CAAC;AACxD;AAEA,OAAO,SAASoB,MAAMA,CAACjB,QAAQ,EAAyC;EAAA,IAAvCC,OAA0B,GAAAiB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC9D,MAAMf,UAAU,GAAG,IAAIT,UAAU,CAACM,QAAQ,CAAC;EAE3C,KAAK,MAAMqB,IAAI,IAAIlB,UAAU,CAACmB,IAAI,CAACC,MAAM,IAAI,EAAE,EAAE;IAG/CC,YAAY,CAACH,IAAI,EAAEpB,OAAO,CAAC;IAE3BE,UAAU,CAACsB,oBAAoB,CAAC5B,0BAA0B,CAAC;EAC7D;AACF;AAUA,eAAegB,mBAAmBA,CAChCV,UAAsB,EACtBC,SAA4B,EAC5BH,OAA0B,EAC1BC,OAAsB,EACP;EACf,MAAMwB,cAAc,GAAGvB,UAAU,CAACG,kBAAkB,CAClDF,SAAS,EACTP,0BACF,CAAC;EACD,IAAI,CAAC6B,cAAc,EAAE;IACnB;EACF;EAEA,MAAMC,MAAM,GAAGxB,UAAU,CAACyB,0BAA0B,CAACF,cAAc,CAACG,UAAU,CAAC;EAG/E,MAAMC,UAAU,GAAGtC,gBAAgB,CAACmC,MAAM,CAACA,MAAM,EAAEA,MAAM,CAACI,UAAU,CAAC;EAErE,MAAM;IAACC;EAAK,CAAC,GAAG9B,OAAO;EACvB,MAAM+B,YAAgC,GAAG;IAAC,GAAGhC;EAAO,CAAC;EAGrD,OAAOgC,YAAY,CAAC,UAAU,CAAC;EAC/B,MAAMC,WAAW,GAAI,MAAMF,KAAK,CAACF,UAAU,EAAEvC,WAAW,EAAE0C,YAAY,EAAE/B,OAAO,CAAe;EAE9F,MAAMiC,iBAAgD,GAAGxC,gBAAgB,CAACuC,WAAW,CAACE,UAAU,CAAC;EAGjG,KAAK,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACL,iBAAiB,CAAC,EAAE;IACjF,IAAIE,aAAa,IAAIjC,SAAS,CAACgC,UAAU,EAAE;MACzC,MAAMK,aAAqB,GAAGrC,SAAS,CAACgC,UAAU,CAACC,aAAa,CAAC;MACjE,MAAMK,QAAQ,GAAGvC,UAAU,CAACwC,WAAW,CAACF,aAAa,CAAC;MACtD,IAAIC,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEE,GAAG,IAAIF,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEG,GAAG,EAAE;QAClCP,gBAAgB,CAACM,GAAG,GAAGF,QAAQ,CAACE,GAAG;QACnCN,gBAAgB,CAACO,GAAG,GAAGH,QAAQ,CAACG,GAAG;MACrC;IACF;EACF;EAGAzC,SAAS,CAACgC,UAAU,GAAGD,iBAAiB;EACxC,IAAID,WAAW,CAACY,OAAO,EAAE;IAEvB1C,SAAS,CAAC0C,OAAO,GAAGlD,eAAe,CAACsC,WAAW,CAACY,OAAO,CAAC;EAC1D;EAKAC,cAAc,CAAC3C,SAAS,CAAC;AAC3B;AAMA,SAASoB,YAAYA,CAACY,UAAU,EAAEU,OAAO,EAAqD;EAAA,IAAAE,kBAAA;EAAA,IAAnDC,IAAY,GAAA/B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEjB,OAAO,GAAAiB,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,IAAElB,OAAsB,GAAAgB,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAC1F,IAAI,CAACnB,OAAO,CAACiD,WAAW,EAAE;IACxB,MAAM,IAAIC,KAAK,CAAC,uCAAuC,CAAC;EAC1D;EAGA,MAAMC,cAAc,GAAGnD,OAAO,CAACiD,WAAW,CAACG,UAAU,CAAC;IAACjB;EAAU,CAAC,CAAC;EAQnE,MAAMF,WAAW,GAAGhC,OAAO,aAAPA,OAAO,wBAAA8C,kBAAA,GAAP9C,OAAO,CAAEoD,SAAS,cAAAN,kBAAA,uBAAlBA,kBAAA,CAAAO,IAAA,CAAArD,OAAO,EAAc;IAACkC;EAAU,CAAC,CAAC;EACtD,MAAMoB,aAAa,GAAGvD,OAAO,CAACwD,kBAAkB,CAACvB,WAAW,CAACE,UAAU,CAAC;EAExE,MAAMsB,eAAe,GAAGzD,OAAO,CAAC0D,aAAa,CAACP,cAAc,CAAC;EAE7D,MAAMQ,QAAQ,GAAG;IACfC,UAAU,EAAE,CACV;MACEzB,UAAU,EAAEoB,aAAa;MACzBP,IAAI;MACJa,UAAU,EAAE;QACV,CAACjE,0BAA0B,GAAG;UAC5BgC,UAAU,EAAE6B,eAAe;UAC3BtB,UAAU,EAAEoB;QACd;MACF;IACF,CAAC;EAEL,CAAC;EAED,OAAOI,QAAQ;AACjB;AAIA,SAASb,cAAcA,CAAC3C,SAA4B,EAAE;EACpD,IAAI,CAACA,SAAS,CAACgC,UAAU,IAAIG,MAAM,CAACwB,IAAI,CAAC3D,SAAS,CAACgC,UAAU,CAAC,CAACjB,MAAM,GAAG,CAAC,EAAE;IACzE,MAAM,IAAIgC,KAAK,CAAC,8DAA8D,CAAC;EACjF;AACF;AAEA,UAAU9C,yBAAyBA,CAACF,UAAU,EAAE;EAC9C,KAAK,MAAMkB,IAAI,IAAIlB,UAAU,CAACmB,IAAI,CAACC,MAAM,IAAI,EAAE,EAAE;IAC/C,KAAK,MAAMnB,SAAS,IAAIiB,IAAI,CAACwC,UAAU,EAAE;MACvC,MAAMzD,SAAS;IACjB;EACF;AACF"}