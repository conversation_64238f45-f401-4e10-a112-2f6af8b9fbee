{"version": 3, "file": "EXT_texture_webp.js", "names": ["isImageFormatSupported", "GLTFScenegraph", "EXT_TEXTURE_WEBP", "name", "preprocess", "gltfData", "options", "scenegraph", "getRequiredExtensions", "includes", "Error", "concat", "json", "texture", "textures", "extension", "getObjectExtension", "source", "removeObjectExtension", "removeExtension"], "sources": ["../../../../src/lib/extensions/EXT_texture_webp.ts"], "sourcesContent": ["// GLTF EXTENSION: EXT_TEXTURE_WEBP\n// https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/EXT_TEXTURE_WEBP\n/* eslint-disable camelcase */\n\nimport type {GLTF, GLTF_EXT_texture_webp} from '../types/gltf-types';\nimport type {GLTFLoaderOptions} from '../../gltf-loader';\n\nimport {isImageFormatSupported} from '@loaders.gl/images';\nimport GLTFScenegraph from '../api/gltf-scenegraph';\n\nconst EXT_TEXTURE_WEBP = 'EXT_texture_webp';\n\n/** Extension name */\nexport const name = EXT_TEXTURE_WEBP;\n\n/**\n * Replaces a texture source reference with the extension texture\n * Done in preprocess() to prevent load of default image\n */\nexport function preprocess(gltfData: {json: GLTF}, options: GLTFLoaderOptions): void {\n  const scenegraph = new GLTFScenegraph(gltfData);\n\n  if (!isImageFormatSupported('image/webp')) {\n    if (scenegraph.getRequiredExtensions().includes(EXT_TEXTURE_WEBP)) {\n      throw new Error(`gltf: Required extension ${EXT_TEXTURE_WEBP} not supported by browser`);\n    }\n    return;\n  }\n\n  const {json} = scenegraph;\n\n  for (const texture of json.textures || []) {\n    const extension = scenegraph.getObjectExtension<GLTF_EXT_texture_webp>(\n      texture,\n      EXT_TEXTURE_WEBP\n    );\n    if (extension) {\n      // TODO - if multiple texture extensions are present which one wins?\n      texture.source = extension.source;\n    }\n    scenegraph.removeObjectExtension(texture, EXT_TEXTURE_WEBP);\n  }\n\n  // Remove the top-level extension\n  scenegraph.removeExtension(EXT_TEXTURE_WEBP);\n}\n"], "mappings": "AAOA,SAAQA,sBAAsB,QAAO,oBAAoB;AACzD,OAAOC,cAAc,MAAM,wBAAwB;AAEnD,MAAMC,gBAAgB,GAAG,kBAAkB;AAG3C,OAAO,MAAMC,IAAI,GAAGD,gBAAgB;AAMpC,OAAO,SAASE,UAAUA,CAACC,QAAsB,EAAEC,OAA0B,EAAQ;EACnF,MAAMC,UAAU,GAAG,IAAIN,cAAc,CAACI,QAAQ,CAAC;EAE/C,IAAI,CAACL,sBAAsB,CAAC,YAAY,CAAC,EAAE;IACzC,IAAIO,UAAU,CAACC,qBAAqB,CAAC,CAAC,CAACC,QAAQ,CAACP,gBAAgB,CAAC,EAAE;MACjE,MAAM,IAAIQ,KAAK,6BAAAC,MAAA,CAA6BT,gBAAgB,8BAA2B,CAAC;IAC1F;IACA;EACF;EAEA,MAAM;IAACU;EAAI,CAAC,GAAGL,UAAU;EAEzB,KAAK,MAAMM,OAAO,IAAID,IAAI,CAACE,QAAQ,IAAI,EAAE,EAAE;IACzC,MAAMC,SAAS,GAAGR,UAAU,CAACS,kBAAkB,CAC7CH,OAAO,EACPX,gBACF,CAAC;IACD,IAAIa,SAAS,EAAE;MAEbF,OAAO,CAACI,MAAM,GAAGF,SAAS,CAACE,MAAM;IACnC;IACAV,UAAU,CAACW,qBAAqB,CAACL,OAAO,EAAEX,gBAAgB,CAAC;EAC7D;EAGAK,UAAU,CAACY,eAAe,CAACjB,gBAAgB,CAAC;AAC9C"}