{"version": 3, "file": "KHR_texture_transform.js", "names": ["Vector3", "Matrix3", "getAccessorArrayTypeAndLength", "BYTES", "COMPONENTS", "GLTFScenegraph", "EXT_MESHOPT_TRANSFORM", "name", "scratchVector", "scratchRotationMatrix", "scratchScaleMatrix", "decode", "gltfData", "options", "gltfScenegraph", "extension", "getExtension", "materials", "json", "i", "length", "transformTexCoords", "materialIndex", "_gltfData$json$materi", "_material$pbrMetallic", "_material$pbrMetallic2", "processedTexCoords", "material", "baseColorTexture", "pbrMetallicRoughness", "transformPrimitives", "emisiveTexture", "emissiveTexture", "normalTexture", "occlusionTexture", "metallicRoughnessTexture", "texture", "transformParameters", "getTransformParameters", "meshes", "mesh", "primitive", "primitives", "Number", "isFinite", "transformPrimitive", "_texture$extensions", "textureInfo", "extensions", "texCoord", "originalTexCoord", "isProcessed", "findIndex", "_ref", "original", "newTexCoord", "matrix", "makeTransformationMatrix", "push", "texCoordAccessor", "attributes", "concat", "_gltfData$json$access", "accessor", "accessors", "bufferView", "_gltfData$json$buffer", "bufferViews", "arrayBuffer", "byteOffset", "bufferByteOffset", "buffers", "buffer", "ArrayType", "bytes", "componentType", "components", "type", "elementAddressScale", "byteStride", "result", "Float32Array", "count", "uv", "set", "transformByMatrix3", "updateGltf", "createAttribute", "newTexCoordArray", "byteLength", "originalAccessor", "extensionData", "offset", "rotation", "scale", "translationMatirx", "rotationMatirx", "Math", "cos", "sin", "scaleMatrix", "multiplyRight"], "sources": ["../../../../src/lib/extensions/KHR_texture_transform.ts"], "sourcesContent": ["/**\n * https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_texture_transform/README.md\n */\n\nimport {Vector3, Matrix3} from '@math.gl/core';\nimport type {GLTFMeshPrimitive, GLTFWithBuffers} from '../types/gltf-types';\nimport type {GLTFLoaderOptions} from '../../gltf-loader';\nimport {getAccessorArrayTypeAndLength} from '../gltf-utils/gltf-utils';\nimport {BYTES, COMPONENTS} from '../gltf-utils/gltf-constants';\nimport {\n  Accessor,\n  BufferView,\n  MaterialNormalTextureInfo,\n  MaterialOcclusionTextureInfo,\n  TextureInfo as GLTFTextureInfo\n} from '../types/gltf-json-schema';\nimport GLTFScenegraph from '../api/gltf-scenegraph';\n\n/** Extension name */\nconst EXT_MESHOPT_TRANSFORM = 'KHR_texture_transform';\n\nexport const name = EXT_MESHOPT_TRANSFORM;\n\nconst scratchVector = new Vector3();\nconst scratchRotationMatrix = new Matrix3();\nconst scratchScaleMatrix = new Matrix3();\n\n/** Extension textureInfo https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_texture_transform#gltf-schema-updates */\ntype TextureInfo = {\n  /** The offset of the UV coordinate origin as a factor of the texture dimensions. */\n  offset?: [number, number];\n  /** Rotate the UVs by this many radians counter-clockwise around the origin. This is equivalent to a similar rotation of the image clockwise. */\n  rotation?: number;\n  /** The scale factor applied to the components of the UV coordinates. */\n  scale?: [number, number];\n  /** Overrides the textureInfo texCoord value if supplied, and if this extension is supported. */\n  texCoord?: number;\n};\n/** Intersection of all GLTF textures */\ntype CompoundGLTFTextureInfo = GLTFTextureInfo &\n  MaterialNormalTextureInfo &\n  MaterialOcclusionTextureInfo;\n/** Parameters for TEXCOORD transformation */\ntype TransformParameters = {\n  /** Original texCoord value https://www.khronos.org/registry/glTF/specs/2.0/glTF-2.0.html#_textureinfo_texcoord */\n  originalTexCoord: number;\n  /** New texCoord value from extension https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_texture_transform#gltf-schema-updates */\n  texCoord: number;\n  /** Transformation matrix */\n  matrix: Matrix3;\n};\n\n/**\n * The extension entry to process the transformation\n * @param gltfData gltf buffers and json\n * @param options GLTFLoader options\n */\nexport async function decode(gltfData: GLTFWithBuffers, options: GLTFLoaderOptions) {\n  const gltfScenegraph = new GLTFScenegraph(gltfData);\n  const extension = gltfScenegraph.getExtension(EXT_MESHOPT_TRANSFORM);\n  if (!extension) {\n    return;\n  }\n  const materials = gltfData.json.materials || [];\n  for (let i = 0; i < materials.length; i++) {\n    transformTexCoords(i, gltfData);\n  }\n}\n\n/**\n * Transform TEXCOORD by material\n * @param materialIndex processing material index\n * @param gltfData gltf buffers and json\n */\nfunction transformTexCoords(materialIndex: number, gltfData: GLTFWithBuffers): void {\n  // Save processed texCoords in order no to process the same twice\n  const processedTexCoords: [number, number][] = [];\n  const material = gltfData.json.materials?.[materialIndex];\n  const baseColorTexture = material?.pbrMetallicRoughness?.baseColorTexture;\n  if (baseColorTexture) {\n    transformPrimitives(gltfData, materialIndex, baseColorTexture, processedTexCoords);\n  }\n  const emisiveTexture = material?.emissiveTexture;\n  if (emisiveTexture) {\n    transformPrimitives(gltfData, materialIndex, emisiveTexture, processedTexCoords);\n  }\n  const normalTexture = material?.normalTexture;\n  if (normalTexture) {\n    transformPrimitives(gltfData, materialIndex, normalTexture, processedTexCoords);\n  }\n  const occlusionTexture = material?.occlusionTexture;\n  if (occlusionTexture) {\n    transformPrimitives(gltfData, materialIndex, occlusionTexture, processedTexCoords);\n  }\n  const metallicRoughnessTexture = material?.pbrMetallicRoughness?.metallicRoughnessTexture;\n  if (metallicRoughnessTexture) {\n    transformPrimitives(gltfData, materialIndex, metallicRoughnessTexture, processedTexCoords);\n  }\n}\n\n/**\n * Transform primitives of the particular material\n * @param gltfData gltf data\n * @param materialIndex primitives with this material will be transformed\n * @param texture texture object\n * @param processedTexCoords storage to save already processed texCoords\n */\nfunction transformPrimitives(\n  gltfData: GLTFWithBuffers,\n  materialIndex: number,\n  texture: CompoundGLTFTextureInfo,\n  processedTexCoords: [number, number][]\n) {\n  const transformParameters = getTransformParameters(texture, processedTexCoords);\n  if (!transformParameters) {\n    return;\n  }\n  const meshes = gltfData.json.meshes || [];\n  for (const mesh of meshes) {\n    for (const primitive of mesh.primitives) {\n      const material = primitive.material;\n      if (Number.isFinite(material) && materialIndex === material) {\n        transformPrimitive(gltfData, primitive, transformParameters);\n      }\n    }\n  }\n}\n\n/**\n * Get parameters for TEXCOORD transformation\n * @param texture texture object\n * @param processedTexCoords storage to save already processed texCoords\n * @returns texCoord couple and transformation matrix\n */\nfunction getTransformParameters(\n  texture: CompoundGLTFTextureInfo,\n  processedTexCoords: [number, number][]\n): TransformParameters | null {\n  const textureInfo = texture.extensions?.[EXT_MESHOPT_TRANSFORM];\n  const {texCoord: originalTexCoord = 0} = texture;\n  // If texCoord is not set in the extension, original attribute data will be replaced\n  const {texCoord = originalTexCoord} = textureInfo;\n  // Make sure that couple [originalTexCoord, extensionTexCoord] is not processed twice\n  const isProcessed =\n    processedTexCoords.findIndex(\n      ([original, newTexCoord]) => original === originalTexCoord && newTexCoord === texCoord\n    ) !== -1;\n  if (!isProcessed) {\n    const matrix = makeTransformationMatrix(textureInfo);\n    if (originalTexCoord !== texCoord) {\n      texture.texCoord = texCoord;\n    }\n    processedTexCoords.push([originalTexCoord, texCoord]);\n    return {originalTexCoord, texCoord, matrix};\n  }\n  return null;\n}\n\n/**\n * Transform `TEXCOORD_0` attribute in the primitive\n * @param gltfData gltf data\n * @param primitive primitive object\n * @param transformParameters texCoord couple and transformation matrix\n */\nfunction transformPrimitive(\n  gltfData: GLTFWithBuffers,\n  primitive: GLTFMeshPrimitive,\n  transformParameters: TransformParameters\n) {\n  const {originalTexCoord, texCoord, matrix} = transformParameters;\n  const texCoordAccessor = primitive.attributes[`TEXCOORD_${originalTexCoord}`];\n  if (Number.isFinite(texCoordAccessor)) {\n    // Get accessor of the `TEXCOORD_0` attribute\n    const accessor = gltfData.json.accessors?.[texCoordAccessor];\n    if (accessor && accessor.bufferView) {\n      // Get `bufferView` of the `accessor`\n      const bufferView = gltfData.json.bufferViews?.[accessor.bufferView];\n      if (bufferView) {\n        // Get `arrayBuffer` the `bufferView` look at\n        const {arrayBuffer, byteOffset: bufferByteOffset} = gltfData.buffers[bufferView.buffer];\n        // Resulting byteOffset is sum of the buffer, accessor and bufferView byte offsets\n        const byteOffset =\n          (bufferByteOffset || 0) + (accessor.byteOffset || 0) + (bufferView.byteOffset || 0);\n        // Deduce TypedArray type and its length from `accessor` and `bufferView` data\n        const {ArrayType, length} = getAccessorArrayTypeAndLength(accessor, bufferView);\n        // Number of bytes each component occupies\n        const bytes = BYTES[accessor.componentType];\n        // Number of components. For the `TEXCOORD_0` with `VEC2` type, it must return 2\n        const components = COMPONENTS[accessor.type];\n        // Multiplier to calculate the address of the `TEXCOORD_0` element in the arrayBuffer\n        const elementAddressScale = bufferView.byteStride || bytes * components;\n        // Data transform to Float32Array\n        const result = new Float32Array(length);\n        for (let i = 0; i < accessor.count; i++) {\n          // Take [u, v] couple from the arrayBuffer\n          const uv = new ArrayType(arrayBuffer, byteOffset + i * elementAddressScale, 2);\n          // Set and transform Vector3 per https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_texture_transform#overview\n          scratchVector.set(uv[0], uv[1], 1);\n          scratchVector.transformByMatrix3(matrix);\n          // Save result in Float32Array\n          result.set([scratchVector[0], scratchVector[1]], i * components);\n        }\n        // If texCoord the same, replace gltf structural data\n        if (originalTexCoord === texCoord) {\n          updateGltf(accessor, bufferView, gltfData.buffers, result);\n        } else {\n          // If texCoord change, create new attribute\n          createAttribute(texCoord, accessor, primitive, gltfData, result);\n        }\n      }\n    }\n  }\n}\n\n/**\n * Update GLTF structural objects with new data as we create new `Float32Array` for `TEXCOORD_0`.\n * @param accessor accessor to change\n * @param bufferView bufferView to change\n * @param buffers binary buffers\n * @param newTexcoordArray typed array with data after transformation\n */\nfunction updateGltf(\n  accessor: Accessor,\n  bufferView: BufferView,\n  buffers: {arrayBuffer: ArrayBuffer; byteOffset: number; byteLength: number}[],\n  newTexCoordArray: Float32Array\n): void {\n  accessor.componentType = 5126;\n  buffers.push({\n    arrayBuffer: newTexCoordArray.buffer,\n    byteOffset: 0,\n    byteLength: newTexCoordArray.buffer.byteLength\n  });\n  bufferView.buffer = buffers.length - 1;\n  bufferView.byteLength = newTexCoordArray.buffer.byteLength;\n  bufferView.byteOffset = 0;\n  delete bufferView.byteStride;\n}\n\n/**\n *\n * @param newTexCoord new `texCoord` value\n * @param originalAccessor original accessor object, that store data before transformation\n * @param primitive primitive object\n * @param gltfData gltf data\n * @param newTexCoordArray typed array with data after transformation\n * @returns\n */\nfunction createAttribute(\n  newTexCoord: number,\n  originalAccessor: Accessor,\n  primitive: GLTFMeshPrimitive,\n  gltfData: GLTFWithBuffers,\n  newTexCoordArray: Float32Array\n) {\n  gltfData.buffers.push({\n    arrayBuffer: newTexCoordArray.buffer,\n    byteOffset: 0,\n    byteLength: newTexCoordArray.buffer.byteLength\n  });\n  const bufferViews = gltfData.json.bufferViews;\n  if (!bufferViews) {\n    return;\n  }\n  bufferViews.push({\n    buffer: gltfData.buffers.length - 1,\n    byteLength: newTexCoordArray.buffer.byteLength,\n    byteOffset: 0\n  });\n  const accessors = gltfData.json.accessors;\n  if (!accessors) {\n    return;\n  }\n  accessors.push({\n    bufferView: bufferViews?.length - 1,\n    byteOffset: 0,\n    componentType: 5126,\n    count: originalAccessor.count,\n    type: 'VEC2'\n  });\n  primitive.attributes[`TEXCOORD_${newTexCoord}`] = accessors.length - 1;\n}\n\n/**\n * Construct transformation matrix from the extension data (transition, rotation, scale)\n * @param extensionData extension data\n * @returns transformation matrix\n */\nfunction makeTransformationMatrix(extensionData: TextureInfo): Matrix3 {\n  const {offset = [0, 0], rotation = 0, scale = [1, 1]} = extensionData;\n  const translationMatirx = new Matrix3().set(1, 0, 0, 0, 1, 0, offset[0], offset[1], 1);\n  const rotationMatirx = scratchRotationMatrix.set(\n    Math.cos(rotation),\n    Math.sin(rotation),\n    0,\n    -Math.sin(rotation),\n    Math.cos(rotation),\n    0,\n    0,\n    0,\n    1\n  );\n  const scaleMatrix = scratchScaleMatrix.set(scale[0], 0, 0, 0, scale[1], 0, 0, 0, 1);\n  return translationMatirx.multiplyRight(rotationMatirx).multiplyRight(scaleMatrix);\n}\n"], "mappings": "AAIA,SAAQA,OAAO,EAAEC,OAAO,QAAO,eAAe;AAG9C,SAAQC,6BAA6B,QAAO,0BAA0B;AACtE,SAAQC,KAAK,EAAEC,UAAU,QAAO,8BAA8B;AAQ9D,OAAOC,cAAc,MAAM,wBAAwB;AAGnD,MAAMC,qBAAqB,GAAG,uBAAuB;AAErD,OAAO,MAAMC,IAAI,GAAGD,qBAAqB;AAEzC,MAAME,aAAa,GAAG,IAAIR,OAAO,CAAC,CAAC;AACnC,MAAMS,qBAAqB,GAAG,IAAIR,OAAO,CAAC,CAAC;AAC3C,MAAMS,kBAAkB,GAAG,IAAIT,OAAO,CAAC,CAAC;AAgCxC,OAAO,eAAeU,MAAMA,CAACC,QAAyB,EAAEC,OAA0B,EAAE;EAClF,MAAMC,cAAc,GAAG,IAAIT,cAAc,CAACO,QAAQ,CAAC;EACnD,MAAMG,SAAS,GAAGD,cAAc,CAACE,YAAY,CAACV,qBAAqB,CAAC;EACpE,IAAI,CAACS,SAAS,EAAE;IACd;EACF;EACA,MAAME,SAAS,GAAGL,QAAQ,CAACM,IAAI,CAACD,SAAS,IAAI,EAAE;EAC/C,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACzCE,kBAAkB,CAACF,CAAC,EAAEP,QAAQ,CAAC;EACjC;AACF;AAOA,SAASS,kBAAkBA,CAACC,aAAqB,EAAEV,QAAyB,EAAQ;EAAA,IAAAW,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EAElF,MAAMC,kBAAsC,GAAG,EAAE;EACjD,MAAMC,QAAQ,IAAAJ,qBAAA,GAAGX,QAAQ,CAACM,IAAI,CAACD,SAAS,cAAAM,qBAAA,uBAAvBA,qBAAA,CAA0BD,aAAa,CAAC;EACzD,MAAMM,gBAAgB,GAAGD,QAAQ,aAARA,QAAQ,wBAAAH,qBAAA,GAARG,QAAQ,CAAEE,oBAAoB,cAAAL,qBAAA,uBAA9BA,qBAAA,CAAgCI,gBAAgB;EACzE,IAAIA,gBAAgB,EAAE;IACpBE,mBAAmB,CAAClB,QAAQ,EAAEU,aAAa,EAAEM,gBAAgB,EAAEF,kBAAkB,CAAC;EACpF;EACA,MAAMK,cAAc,GAAGJ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEK,eAAe;EAChD,IAAID,cAAc,EAAE;IAClBD,mBAAmB,CAAClB,QAAQ,EAAEU,aAAa,EAAES,cAAc,EAAEL,kBAAkB,CAAC;EAClF;EACA,MAAMO,aAAa,GAAGN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEM,aAAa;EAC7C,IAAIA,aAAa,EAAE;IACjBH,mBAAmB,CAAClB,QAAQ,EAAEU,aAAa,EAAEW,aAAa,EAAEP,kBAAkB,CAAC;EACjF;EACA,MAAMQ,gBAAgB,GAAGP,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEO,gBAAgB;EACnD,IAAIA,gBAAgB,EAAE;IACpBJ,mBAAmB,CAAClB,QAAQ,EAAEU,aAAa,EAAEY,gBAAgB,EAAER,kBAAkB,CAAC;EACpF;EACA,MAAMS,wBAAwB,GAAGR,QAAQ,aAARA,QAAQ,wBAAAF,sBAAA,GAARE,QAAQ,CAAEE,oBAAoB,cAAAJ,sBAAA,uBAA9BA,sBAAA,CAAgCU,wBAAwB;EACzF,IAAIA,wBAAwB,EAAE;IAC5BL,mBAAmB,CAAClB,QAAQ,EAAEU,aAAa,EAAEa,wBAAwB,EAAET,kBAAkB,CAAC;EAC5F;AACF;AASA,SAASI,mBAAmBA,CAC1BlB,QAAyB,EACzBU,aAAqB,EACrBc,OAAgC,EAChCV,kBAAsC,EACtC;EACA,MAAMW,mBAAmB,GAAGC,sBAAsB,CAACF,OAAO,EAAEV,kBAAkB,CAAC;EAC/E,IAAI,CAACW,mBAAmB,EAAE;IACxB;EACF;EACA,MAAME,MAAM,GAAG3B,QAAQ,CAACM,IAAI,CAACqB,MAAM,IAAI,EAAE;EACzC,KAAK,MAAMC,IAAI,IAAID,MAAM,EAAE;IACzB,KAAK,MAAME,SAAS,IAAID,IAAI,CAACE,UAAU,EAAE;MACvC,MAAMf,QAAQ,GAAGc,SAAS,CAACd,QAAQ;MACnC,IAAIgB,MAAM,CAACC,QAAQ,CAACjB,QAAQ,CAAC,IAAIL,aAAa,KAAKK,QAAQ,EAAE;QAC3DkB,kBAAkB,CAACjC,QAAQ,EAAE6B,SAAS,EAAEJ,mBAAmB,CAAC;MAC9D;IACF;EACF;AACF;AAQA,SAASC,sBAAsBA,CAC7BF,OAAgC,EAChCV,kBAAsC,EACV;EAAA,IAAAoB,mBAAA;EAC5B,MAAMC,WAAW,IAAAD,mBAAA,GAAGV,OAAO,CAACY,UAAU,cAAAF,mBAAA,uBAAlBA,mBAAA,CAAqBxC,qBAAqB,CAAC;EAC/D,MAAM;IAAC2C,QAAQ,EAAEC,gBAAgB,GAAG;EAAC,CAAC,GAAGd,OAAO;EAEhD,MAAM;IAACa,QAAQ,GAAGC;EAAgB,CAAC,GAAGH,WAAW;EAEjD,MAAMI,WAAW,GACfzB,kBAAkB,CAAC0B,SAAS,CAC1BC,IAAA;IAAA,IAAC,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAAF,IAAA;IAAA,OAAKC,QAAQ,KAAKJ,gBAAgB,IAAIK,WAAW,KAAKN,QAAQ;EAAA,CACxF,CAAC,KAAK,CAAC,CAAC;EACV,IAAI,CAACE,WAAW,EAAE;IAChB,MAAMK,MAAM,GAAGC,wBAAwB,CAACV,WAAW,CAAC;IACpD,IAAIG,gBAAgB,KAAKD,QAAQ,EAAE;MACjCb,OAAO,CAACa,QAAQ,GAAGA,QAAQ;IAC7B;IACAvB,kBAAkB,CAACgC,IAAI,CAAC,CAACR,gBAAgB,EAAED,QAAQ,CAAC,CAAC;IACrD,OAAO;MAACC,gBAAgB;MAAED,QAAQ;MAAEO;IAAM,CAAC;EAC7C;EACA,OAAO,IAAI;AACb;AAQA,SAASX,kBAAkBA,CACzBjC,QAAyB,EACzB6B,SAA4B,EAC5BJ,mBAAwC,EACxC;EACA,MAAM;IAACa,gBAAgB;IAAED,QAAQ;IAAEO;EAAM,CAAC,GAAGnB,mBAAmB;EAChE,MAAMsB,gBAAgB,GAAGlB,SAAS,CAACmB,UAAU,aAAAC,MAAA,CAAaX,gBAAgB,EAAG;EAC7E,IAAIP,MAAM,CAACC,QAAQ,CAACe,gBAAgB,CAAC,EAAE;IAAA,IAAAG,qBAAA;IAErC,MAAMC,QAAQ,IAAAD,qBAAA,GAAGlD,QAAQ,CAACM,IAAI,CAAC8C,SAAS,cAAAF,qBAAA,uBAAvBA,qBAAA,CAA0BH,gBAAgB,CAAC;IAC5D,IAAII,QAAQ,IAAIA,QAAQ,CAACE,UAAU,EAAE;MAAA,IAAAC,qBAAA;MAEnC,MAAMD,UAAU,IAAAC,qBAAA,GAAGtD,QAAQ,CAACM,IAAI,CAACiD,WAAW,cAAAD,qBAAA,uBAAzBA,qBAAA,CAA4BH,QAAQ,CAACE,UAAU,CAAC;MACnE,IAAIA,UAAU,EAAE;QAEd,MAAM;UAACG,WAAW;UAAEC,UAAU,EAAEC;QAAgB,CAAC,GAAG1D,QAAQ,CAAC2D,OAAO,CAACN,UAAU,CAACO,MAAM,CAAC;QAEvF,MAAMH,UAAU,GACd,CAACC,gBAAgB,IAAI,CAAC,KAAKP,QAAQ,CAACM,UAAU,IAAI,CAAC,CAAC,IAAIJ,UAAU,CAACI,UAAU,IAAI,CAAC,CAAC;QAErF,MAAM;UAACI,SAAS;UAAErD;QAAM,CAAC,GAAGlB,6BAA6B,CAAC6D,QAAQ,EAAEE,UAAU,CAAC;QAE/E,MAAMS,KAAK,GAAGvE,KAAK,CAAC4D,QAAQ,CAACY,aAAa,CAAC;QAE3C,MAAMC,UAAU,GAAGxE,UAAU,CAAC2D,QAAQ,CAACc,IAAI,CAAC;QAE5C,MAAMC,mBAAmB,GAAGb,UAAU,CAACc,UAAU,IAAIL,KAAK,GAAGE,UAAU;QAEvE,MAAMI,MAAM,GAAG,IAAIC,YAAY,CAAC7D,MAAM,CAAC;QACvC,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4C,QAAQ,CAACmB,KAAK,EAAE/D,CAAC,EAAE,EAAE;UAEvC,MAAMgE,EAAE,GAAG,IAAIV,SAAS,CAACL,WAAW,EAAEC,UAAU,GAAGlD,CAAC,GAAG2D,mBAAmB,EAAE,CAAC,CAAC;UAE9EtE,aAAa,CAAC4E,GAAG,CAACD,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAClC3E,aAAa,CAAC6E,kBAAkB,CAAC7B,MAAM,CAAC;UAExCwB,MAAM,CAACI,GAAG,CAAC,CAAC5E,aAAa,CAAC,CAAC,CAAC,EAAEA,aAAa,CAAC,CAAC,CAAC,CAAC,EAAEW,CAAC,GAAGyD,UAAU,CAAC;QAClE;QAEA,IAAI1B,gBAAgB,KAAKD,QAAQ,EAAE;UACjCqC,UAAU,CAACvB,QAAQ,EAAEE,UAAU,EAAErD,QAAQ,CAAC2D,OAAO,EAAES,MAAM,CAAC;QAC5D,CAAC,MAAM;UAELO,eAAe,CAACtC,QAAQ,EAAEc,QAAQ,EAAEtB,SAAS,EAAE7B,QAAQ,EAAEoE,MAAM,CAAC;QAClE;MACF;IACF;EACF;AACF;AASA,SAASM,UAAUA,CACjBvB,QAAkB,EAClBE,UAAsB,EACtBM,OAA6E,EAC7EiB,gBAA8B,EACxB;EACNzB,QAAQ,CAACY,aAAa,GAAG,IAAI;EAC7BJ,OAAO,CAACb,IAAI,CAAC;IACXU,WAAW,EAAEoB,gBAAgB,CAAChB,MAAM;IACpCH,UAAU,EAAE,CAAC;IACboB,UAAU,EAAED,gBAAgB,CAAChB,MAAM,CAACiB;EACtC,CAAC,CAAC;EACFxB,UAAU,CAACO,MAAM,GAAGD,OAAO,CAACnD,MAAM,GAAG,CAAC;EACtC6C,UAAU,CAACwB,UAAU,GAAGD,gBAAgB,CAAChB,MAAM,CAACiB,UAAU;EAC1DxB,UAAU,CAACI,UAAU,GAAG,CAAC;EACzB,OAAOJ,UAAU,CAACc,UAAU;AAC9B;AAWA,SAASQ,eAAeA,CACtBhC,WAAmB,EACnBmC,gBAA0B,EAC1BjD,SAA4B,EAC5B7B,QAAyB,EACzB4E,gBAA8B,EAC9B;EACA5E,QAAQ,CAAC2D,OAAO,CAACb,IAAI,CAAC;IACpBU,WAAW,EAAEoB,gBAAgB,CAAChB,MAAM;IACpCH,UAAU,EAAE,CAAC;IACboB,UAAU,EAAED,gBAAgB,CAAChB,MAAM,CAACiB;EACtC,CAAC,CAAC;EACF,MAAMtB,WAAW,GAAGvD,QAAQ,CAACM,IAAI,CAACiD,WAAW;EAC7C,IAAI,CAACA,WAAW,EAAE;IAChB;EACF;EACAA,WAAW,CAACT,IAAI,CAAC;IACfc,MAAM,EAAE5D,QAAQ,CAAC2D,OAAO,CAACnD,MAAM,GAAG,CAAC;IACnCqE,UAAU,EAAED,gBAAgB,CAAChB,MAAM,CAACiB,UAAU;IAC9CpB,UAAU,EAAE;EACd,CAAC,CAAC;EACF,MAAML,SAAS,GAAGpD,QAAQ,CAACM,IAAI,CAAC8C,SAAS;EACzC,IAAI,CAACA,SAAS,EAAE;IACd;EACF;EACAA,SAAS,CAACN,IAAI,CAAC;IACbO,UAAU,EAAE,CAAAE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE/C,MAAM,IAAG,CAAC;IACnCiD,UAAU,EAAE,CAAC;IACbM,aAAa,EAAE,IAAI;IACnBO,KAAK,EAAEQ,gBAAgB,CAACR,KAAK;IAC7BL,IAAI,EAAE;EACR,CAAC,CAAC;EACFpC,SAAS,CAACmB,UAAU,aAAAC,MAAA,CAAaN,WAAW,EAAG,GAAGS,SAAS,CAAC5C,MAAM,GAAG,CAAC;AACxE;AAOA,SAASqC,wBAAwBA,CAACkC,aAA0B,EAAW;EACrE,MAAM;IAACC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAAEC,QAAQ,GAAG,CAAC;IAAEC,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;EAAC,CAAC,GAAGH,aAAa;EACrE,MAAMI,iBAAiB,GAAG,IAAI9F,OAAO,CAAC,CAAC,CAACmF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEQ,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACtF,MAAMI,cAAc,GAAGvF,qBAAqB,CAAC2E,GAAG,CAC9Ca,IAAI,CAACC,GAAG,CAACL,QAAQ,CAAC,EAClBI,IAAI,CAACE,GAAG,CAACN,QAAQ,CAAC,EAClB,CAAC,EACD,CAACI,IAAI,CAACE,GAAG,CAACN,QAAQ,CAAC,EACnBI,IAAI,CAACC,GAAG,CAACL,QAAQ,CAAC,EAClB,CAAC,EACD,CAAC,EACD,CAAC,EACD,CACF,CAAC;EACD,MAAMO,WAAW,GAAG1F,kBAAkB,CAAC0E,GAAG,CAACU,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACnF,OAAOC,iBAAiB,CAACM,aAAa,CAACL,cAAc,CAAC,CAACK,aAAa,CAACD,WAAW,CAAC;AACnF"}