{"version": 3, "file": "EXT_meshopt_compression.js", "names": ["GLTFScenegraph", "meshoptDecodeGltfBuffer", "DEFAULT_MESHOPT_OPTIONS", "byteOffset", "filter", "EXT_MESHOPT_COMPRESSION", "name", "decode", "gltfData", "options", "_options$gltf", "scenegraph", "gltf", "decompressMeshes", "promises", "bufferViewIndex", "json", "bufferViews", "push", "decodeMeshoptBufferView", "Promise", "all", "removeExtension", "bufferView", "meshoptExtension", "getObjectExtension", "byteLength", "byteStride", "count", "mode", "buffer", "bufferIndex", "buffers", "source", "Uint8Array", "arrayBuffer", "result"], "sources": ["../../../../src/lib/extensions/EXT_meshopt_compression.ts"], "sourcesContent": ["/* eslint-disable camelcase */\nimport type {GLTF, GLTFBufferView, GLTF_EXT_meshopt_compression} from '../types/gltf-types';\nimport type {GLTFLoaderOptions} from '../../gltf-loader';\nimport GLTFScenegraph from '../api/gltf-scenegraph';\nimport {meshoptDecodeGltfBuffer} from '../../meshopt/meshopt-decoder';\n\n// @ts-ignore\n// eslint-disable-next-line\nconst DEFAULT_MESHOPT_OPTIONS = {\n  byteOffset: 0,\n  filter: 'NONE'\n};\n\n/** Extension name */\nconst EXT_MESHOPT_COMPRESSION = 'EXT_meshopt_compression';\n\nexport const name = EXT_MESHOPT_COMPRESSION;\n\nexport async function decode(gltfData: {json: GLTF}, options: GLTFLoaderOptions) {\n  const scenegraph = new GLTFScenegraph(gltfData);\n\n  if (!options?.gltf?.decompressMeshes) {\n    return;\n  }\n\n  const promises: Promise<any>[] = [];\n  for (const bufferViewIndex of gltfData.json.bufferViews || []) {\n    promises.push(decodeMeshoptBufferView(scenegraph, bufferViewIndex));\n  }\n\n  // Decompress meshes in parallel\n  await Promise.all(promises);\n\n  // We have now decompressed all primitives, so remove the top-level extension\n  scenegraph.removeExtension(EXT_MESHOPT_COMPRESSION);\n}\n\n/** Decode one meshopt buffer view */\nasync function decodeMeshoptBufferView(\n  scenegraph: GLTFScenegraph,\n  bufferView: GLTFBufferView\n): Promise<ArrayBuffer | null> {\n  const meshoptExtension = scenegraph.getObjectExtension<GLTF_EXT_meshopt_compression>(\n    bufferView,\n    EXT_MESHOPT_COMPRESSION\n  );\n  if (meshoptExtension) {\n    const {\n      byteOffset = 0,\n      byteLength = 0,\n      byteStride,\n      count,\n      mode,\n      filter = 'NONE',\n      buffer: bufferIndex\n    } = meshoptExtension;\n    const buffer = scenegraph.gltf.buffers[bufferIndex];\n\n    const source = new Uint8Array(buffer.arrayBuffer, buffer.byteOffset + byteOffset, byteLength);\n    const result = new Uint8Array(\n      scenegraph.gltf.buffers[bufferView.buffer].arrayBuffer,\n      bufferView.byteOffset,\n      bufferView.byteLength\n    );\n    await meshoptDecodeGltfBuffer(result, count, byteStride, source, mode, filter);\n    return result;\n  }\n\n  return null;\n}\n"], "mappings": "AAGA,OAAOA,cAAc,MAAM,wBAAwB;AACnD,SAAQC,uBAAuB,QAAO,+BAA+B;AAIrE,MAAMC,uBAAuB,GAAG;EAC9BC,UAAU,EAAE,CAAC;EACbC,MAAM,EAAE;AACV,CAAC;AAGD,MAAMC,uBAAuB,GAAG,yBAAyB;AAEzD,OAAO,MAAMC,IAAI,GAAGD,uBAAuB;AAE3C,OAAO,eAAeE,MAAMA,CAACC,QAAsB,EAAEC,OAA0B,EAAE;EAAA,IAAAC,aAAA;EAC/E,MAAMC,UAAU,GAAG,IAAIX,cAAc,CAACQ,QAAQ,CAAC;EAE/C,IAAI,EAACC,OAAO,aAAPA,OAAO,gBAAAC,aAAA,GAAPD,OAAO,CAAEG,IAAI,cAAAF,aAAA,eAAbA,aAAA,CAAeG,gBAAgB,GAAE;IACpC;EACF;EAEA,MAAMC,QAAwB,GAAG,EAAE;EACnC,KAAK,MAAMC,eAAe,IAAIP,QAAQ,CAACQ,IAAI,CAACC,WAAW,IAAI,EAAE,EAAE;IAC7DH,QAAQ,CAACI,IAAI,CAACC,uBAAuB,CAACR,UAAU,EAAEI,eAAe,CAAC,CAAC;EACrE;EAGA,MAAMK,OAAO,CAACC,GAAG,CAACP,QAAQ,CAAC;EAG3BH,UAAU,CAACW,eAAe,CAACjB,uBAAuB,CAAC;AACrD;AAGA,eAAec,uBAAuBA,CACpCR,UAA0B,EAC1BY,UAA0B,EACG;EAC7B,MAAMC,gBAAgB,GAAGb,UAAU,CAACc,kBAAkB,CACpDF,UAAU,EACVlB,uBACF,CAAC;EACD,IAAImB,gBAAgB,EAAE;IACpB,MAAM;MACJrB,UAAU,GAAG,CAAC;MACduB,UAAU,GAAG,CAAC;MACdC,UAAU;MACVC,KAAK;MACLC,IAAI;MACJzB,MAAM,GAAG,MAAM;MACf0B,MAAM,EAAEC;IACV,CAAC,GAAGP,gBAAgB;IACpB,MAAMM,MAAM,GAAGnB,UAAU,CAACC,IAAI,CAACoB,OAAO,CAACD,WAAW,CAAC;IAEnD,MAAME,MAAM,GAAG,IAAIC,UAAU,CAACJ,MAAM,CAACK,WAAW,EAAEL,MAAM,CAAC3B,UAAU,GAAGA,UAAU,EAAEuB,UAAU,CAAC;IAC7F,MAAMU,MAAM,GAAG,IAAIF,UAAU,CAC3BvB,UAAU,CAACC,IAAI,CAACoB,OAAO,CAACT,UAAU,CAACO,MAAM,CAAC,CAACK,WAAW,EACtDZ,UAAU,CAACpB,UAAU,EACrBoB,UAAU,CAACG,UACb,CAAC;IACD,MAAMzB,uBAAuB,CAACmC,MAAM,EAAER,KAAK,EAAED,UAAU,EAAEM,MAAM,EAAEJ,IAAI,EAAEzB,MAAM,CAAC;IAC9E,OAAOgC,MAAM;EACf;EAEA,OAAO,IAAI;AACb"}