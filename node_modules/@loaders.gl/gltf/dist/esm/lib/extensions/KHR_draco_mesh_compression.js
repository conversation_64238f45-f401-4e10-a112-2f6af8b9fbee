import { <PERSON><PERSON><PERSON>oa<PERSON> } from '@loaders.gl/draco';
import { sliceArrayBuffer } from '@loaders.gl/loader-utils';
import { default as Scenegraph } from '../api/gltf-scenegraph';
import { getGLTFAccessors, getGLTFAccessor } from '../gltf-utils/gltf-attribute-utils';
const KHR_DRACO_MESH_COMPRESSION = 'KHR_draco_mesh_compression';
export const name = KHR_DRACO_MESH_COMPRESSION;
export function preprocess(gltfData, options, context) {
  const scenegraph = new Scenegraph(gltfData);
  for (const primitive of makeMeshPrimitiveIterator(scenegraph)) {
    if (scenegraph.getObjectExtension(primitive, KHR_DRACO_MESH_COMPRESSION)) {}
  }
}
export async function decode(gltfData, options, context) {
  var _options$gltf;
  if (!(options !== null && options !== void 0 && (_options$gltf = options.gltf) !== null && _options$gltf !== void 0 && _options$gltf.decompressMeshes)) {
    return;
  }
  const scenegraph = new Scenegraph(gltfData);
  const promises = [];
  for (const primitive of makeMeshPrimitiveIterator(scenegraph)) {
    if (scenegraph.getObjectExtension(primitive, KHR_DRACO_MESH_COMPRESSION)) {
      promises.push(decompressPrimitive(scenegraph, primitive, options, context));
    }
  }
  await Promise.all(promises);
  scenegraph.removeExtension(KHR_DRACO_MESH_COMPRESSION);
}
export function encode(gltfData) {
  let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  const scenegraph = new Scenegraph(gltfData);
  for (const mesh of scenegraph.json.meshes || []) {
    compressMesh(mesh, options);
    scenegraph.addRequiredExtension(KHR_DRACO_MESH_COMPRESSION);
  }
}
async function decompressPrimitive(scenegraph, primitive, options, context) {
  const dracoExtension = scenegraph.getObjectExtension(primitive, KHR_DRACO_MESH_COMPRESSION);
  if (!dracoExtension) {
    return;
  }
  const buffer = scenegraph.getTypedArrayForBufferView(dracoExtension.bufferView);
  const bufferCopy = sliceArrayBuffer(buffer.buffer, buffer.byteOffset);
  const {
    parse
  } = context;
  const dracoOptions = {
    ...options
  };
  delete dracoOptions['3d-tiles'];
  const decodedData = await parse(bufferCopy, DracoLoader, dracoOptions, context);
  const decodedAttributes = getGLTFAccessors(decodedData.attributes);
  for (const [attributeName, decodedAttribute] of Object.entries(decodedAttributes)) {
    if (attributeName in primitive.attributes) {
      const accessorIndex = primitive.attributes[attributeName];
      const accessor = scenegraph.getAccessor(accessorIndex);
      if (accessor !== null && accessor !== void 0 && accessor.min && accessor !== null && accessor !== void 0 && accessor.max) {
        decodedAttribute.min = accessor.min;
        decodedAttribute.max = accessor.max;
      }
    }
  }
  primitive.attributes = decodedAttributes;
  if (decodedData.indices) {
    primitive.indices = getGLTFAccessor(decodedData.indices);
  }
  checkPrimitive(primitive);
}
function compressMesh(attributes, indices) {
  var _context$parseSync;
  let mode = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 4;
  let options = arguments.length > 3 ? arguments[3] : undefined;
  let context = arguments.length > 4 ? arguments[4] : undefined;
  if (!options.DracoWriter) {
    throw new Error('options.gltf.DracoWriter not provided');
  }
  const compressedData = options.DracoWriter.encodeSync({
    attributes
  });
  const decodedData = context === null || context === void 0 ? void 0 : (_context$parseSync = context.parseSync) === null || _context$parseSync === void 0 ? void 0 : _context$parseSync.call(context, {
    attributes
  });
  const fauxAccessors = options._addFauxAttributes(decodedData.attributes);
  const bufferViewIndex = options.addBufferView(compressedData);
  const glTFMesh = {
    primitives: [{
      attributes: fauxAccessors,
      mode,
      extensions: {
        [KHR_DRACO_MESH_COMPRESSION]: {
          bufferView: bufferViewIndex,
          attributes: fauxAccessors
        }
      }
    }]
  };
  return glTFMesh;
}
function checkPrimitive(primitive) {
  if (!primitive.attributes && Object.keys(primitive.attributes).length > 0) {
    throw new Error('glTF: Empty primitive detected: Draco decompression failure?');
  }
}
function* makeMeshPrimitiveIterator(scenegraph) {
  for (const mesh of scenegraph.json.meshes || []) {
    for (const primitive of mesh.primitives) {
      yield primitive;
    }
  }
}
//# sourceMappingURL=KHR_draco_mesh_compression.js.map