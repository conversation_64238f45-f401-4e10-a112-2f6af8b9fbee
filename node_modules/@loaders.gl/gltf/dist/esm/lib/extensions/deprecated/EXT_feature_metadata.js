import GLTFScenegraph from '../../api/gltf-scenegraph';
const EXT_FEATURE_METADATA = 'EXT_feature_metadata';
export const name = EXT_FEATURE_METADATA;
export async function decode(gltfData) {
  const scenegraph = new GLTFScenegraph(gltfData);
  decodeExtFeatureMetadata(scenegraph);
}
function decodeExtFeatureMetadata(scenegraph) {
  var _extension$schema;
  const extension = scenegraph.getExtension(EXT_FEATURE_METADATA);
  const schemaClasses = extension === null || extension === void 0 ? void 0 : (_extension$schema = extension.schema) === null || _extension$schema === void 0 ? void 0 : _extension$schema.classes;
  const featureTables = extension === null || extension === void 0 ? void 0 : extension.featureTables;
  const featureTextures = extension === null || extension === void 0 ? void 0 : extension.featureTextures;
  if (featureTextures) {
    console.warn('featureTextures is not yet supported in the "EXT_feature_metadata" extension.');
  }
  if (schemaClasses && featureTables) {
    for (const schemaName in schemaClasses) {
      const schemaClass = schemaClasses[schemaName];
      const featureTable = findFeatureTableByName(featureTables, schemaName);
      if (featureTable) {
        handleFeatureTableProperties(scenegraph, featureTable, schemaClass);
      }
    }
  }
}
function handleFeatureTableProperties(scenegraph, featureTable, schemaClass) {
  for (const propertyName in schemaClass.properties) {
    var _featureTable$propert;
    const schemaProperty = schemaClass.properties[propertyName];
    const featureTableProperty = featureTable === null || featureTable === void 0 ? void 0 : (_featureTable$propert = featureTable.properties) === null || _featureTable$propert === void 0 ? void 0 : _featureTable$propert[propertyName];
    const numberOfFeatures = featureTable.count;
    if (featureTableProperty) {
      const data = getPropertyDataFromBinarySource(scenegraph, schemaProperty, numberOfFeatures, featureTableProperty);
      featureTableProperty.data = data;
    }
  }
}
function getPropertyDataFromBinarySource(scenegraph, schemaProperty, numberOfFeatures, featureTableProperty) {
  const bufferView = featureTableProperty.bufferView;
  let data = scenegraph.getTypedArrayForBufferView(bufferView);
  switch (schemaProperty.type) {
    case 'STRING':
      {
        const stringOffsetBufferView = featureTableProperty.stringOffsetBufferView;
        const offsetsData = scenegraph.getTypedArrayForBufferView(stringOffsetBufferView);
        data = getStringAttributes(data, offsetsData, numberOfFeatures);
        break;
      }
    default:
  }
  return data;
}
function findFeatureTableByName(featureTables, schemaClassName) {
  for (const featureTableName in featureTables) {
    const featureTable = featureTables[featureTableName];
    if (featureTable.class === schemaClassName) {
      return featureTable;
    }
  }
  return null;
}
function getStringAttributes(data, offsetsData, stringsCount) {
  const stringsArray = [];
  const textDecoder = new TextDecoder('utf8');
  let stringOffset = 0;
  const bytesPerStringSize = 4;
  for (let index = 0; index < stringsCount; index++) {
    const stringByteSize = offsetsData[(index + 1) * bytesPerStringSize] - offsetsData[index * bytesPerStringSize];
    const stringData = data.subarray(stringOffset, stringByteSize + stringOffset);
    const stringAttribute = textDecoder.decode(stringData);
    stringsArray.push(stringAttribute);
    stringOffset += stringByteSize;
  }
  return stringsArray;
}
//# sourceMappingURL=EXT_feature_metadata.js.map