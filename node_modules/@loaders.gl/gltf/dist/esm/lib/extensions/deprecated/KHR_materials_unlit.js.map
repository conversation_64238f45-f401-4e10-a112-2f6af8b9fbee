{"version": 3, "file": "KHR_materials_unlit.js", "names": ["GLTFScenegraph", "KHR_MATERIALS_UNLIT", "name", "decode", "gltfData", "gltfScenegraph", "json", "material", "materials", "extension", "extensions", "KHR_materials_unlit", "unlit", "removeObjectExtension", "removeExtension", "encode", "addObjectExtension", "addExtension"], "sources": ["../../../../../src/lib/extensions/deprecated/KHR_materials_unlit.ts"], "sourcesContent": ["// GLTF EXTENSION: KHR_materials_unlit\n// https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_unlit\n\nimport type {GLTF} from '../../types/gltf-types';\n\nimport GLTFScenegraph from '../../api/gltf-scenegraph';\n\nconst KHR_MATERIALS_UNLIT = 'KHR_materials_unlit';\n\nexport const name = KHR_MATERIALS_UNLIT;\n\nexport async function decode(gltfData: {json: GLTF}): Promise<void> {\n  const gltfScenegraph = new GLTFScenegraph(gltfData);\n  const {json} = gltfScenegraph;\n\n  // Any nodes that have the extension, add lights field pointing to light object\n  // and remove the extension\n  for (const material of json.materials || []) {\n    const extension = material.extensions && material.extensions.KHR_materials_unlit;\n    if (extension) {\n      // @ts-ignore TODO\n      material.unlit = true;\n    }\n    gltfScenegraph.removeObjectExtension(material, KHR_MATERIALS_UNLIT);\n  }\n\n  // Remove the top-level extension\n  gltfScenegraph.removeExtension(KHR_MATERIALS_UNLIT);\n}\n\nexport function encode(gltfData) {\n  const gltfScenegraph = new GLTFScenegraph(gltfData);\n  const {json} = gltfScenegraph;\n\n  // Any nodes that have lights field pointing to light object\n  // add the extension\n  // @ts-ignore\n  if (gltfScenegraph.materials) {\n    for (const material of json.materials || []) {\n      // @ts-ignore\n      if (material.unlit) {\n        // @ts-ignore\n        delete material.unlit;\n        gltfScenegraph.addObjectExtension(material, KHR_MATERIALS_UNLIT, {});\n        gltfScenegraph.addExtension(KHR_MATERIALS_UNLIT);\n      }\n    }\n  }\n}\n"], "mappings": "AAKA,OAAOA,cAAc,MAAM,2BAA2B;AAEtD,MAAMC,mBAAmB,GAAG,qBAAqB;AAEjD,OAAO,MAAMC,IAAI,GAAGD,mBAAmB;AAEvC,OAAO,eAAeE,MAAMA,CAACC,QAAsB,EAAiB;EAClE,MAAMC,cAAc,GAAG,IAAIL,cAAc,CAACI,QAAQ,CAAC;EACnD,MAAM;IAACE;EAAI,CAAC,GAAGD,cAAc;EAI7B,KAAK,MAAME,QAAQ,IAAID,IAAI,CAACE,SAAS,IAAI,EAAE,EAAE;IAC3C,MAAMC,SAAS,GAAGF,QAAQ,CAACG,UAAU,IAAIH,QAAQ,CAACG,UAAU,CAACC,mBAAmB;IAChF,IAAIF,SAAS,EAAE;MAEbF,QAAQ,CAACK,KAAK,GAAG,IAAI;IACvB;IACAP,cAAc,CAACQ,qBAAqB,CAACN,QAAQ,EAAEN,mBAAmB,CAAC;EACrE;EAGAI,cAAc,CAACS,eAAe,CAACb,mBAAmB,CAAC;AACrD;AAEA,OAAO,SAASc,MAAMA,CAACX,QAAQ,EAAE;EAC/B,MAAMC,cAAc,GAAG,IAAIL,cAAc,CAACI,QAAQ,CAAC;EACnD,MAAM;IAACE;EAAI,CAAC,GAAGD,cAAc;EAK7B,IAAIA,cAAc,CAACG,SAAS,EAAE;IAC5B,KAAK,MAAMD,QAAQ,IAAID,IAAI,CAACE,SAAS,IAAI,EAAE,EAAE;MAE3C,IAAID,QAAQ,CAACK,KAAK,EAAE;QAElB,OAAOL,QAAQ,CAACK,KAAK;QACrBP,cAAc,CAACW,kBAAkB,CAACT,QAAQ,EAAEN,mBAAmB,EAAE,CAAC,CAAC,CAAC;QACpEI,cAAc,CAACY,YAAY,CAAChB,mBAAmB,CAAC;MAClD;IACF;EACF;AACF"}