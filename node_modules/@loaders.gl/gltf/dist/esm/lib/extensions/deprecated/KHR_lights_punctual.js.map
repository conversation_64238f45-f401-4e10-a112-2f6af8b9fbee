{"version": 3, "file": "KHR_lights_punctual.js", "names": ["assert", "GLTFScenegraph", "KHR_LIGHTS_PUNCTUAL", "name", "decode", "gltfData", "gltfScenegraph", "json", "extension", "getExtension", "lights", "removeExtension", "node", "nodes", "nodeExtension", "getObjectExtension", "light", "removeObjectExtension", "encode", "addExtension", "addObjectExtension"], "sources": ["../../../../../src/lib/extensions/deprecated/KHR_lights_punctual.ts"], "sourcesContent": ["// GLTF EXTENSION: KHR_lights_punctual\n// https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_lights_punctual\n\nimport type {GLTF} from '../../types/gltf-types';\n\nimport {assert} from '../../utils/assert';\nimport GLTFScenegraph from '../../api/gltf-scenegraph';\n\nconst KHR_LIGHTS_PUNCTUAL = 'KHR_lights_punctual';\n\nexport const name = KHR_LIGHTS_PUNCTUAL;\n\nexport async function decode(gltfData: {json: GLTF}): Promise<void> {\n  const gltfScenegraph = new GLTFScenegraph(gltfData);\n  const {json} = gltfScenegraph;\n\n  // Move the light array out of the extension and remove the extension\n  const extension = gltfScenegraph.getExtension(KHR_LIGHTS_PUNCTUAL);\n  if (extension) {\n    // @ts-ignore\n    gltfScenegraph.json.lights = extension.lights;\n    gltfScenegraph.removeExtension(KHR_LIGHTS_PUNCTUAL);\n  }\n\n  // Any nodes that have the extension, add lights field pointing to light object\n  // and remove the extension\n  for (const node of json.nodes || []) {\n    const nodeExtension = gltfScenegraph.getObjectExtension(node, KHR_LIGHTS_PUNCTUAL);\n    if (nodeExtension) {\n      // @ts-ignore\n      node.light = nodeExtension.light;\n    }\n    gltfScenegraph.removeObjectExtension(node, KHR_LIGHTS_PUNCTUAL);\n  }\n}\n\n// Move the light ar ray out of the extension and remove the extension\nexport async function encode(gltfData): Promise<void> {\n  const gltfScenegraph = new GLTFScenegraph(gltfData);\n  const {json} = gltfScenegraph;\n\n  // @ts-ignore\n  if (json.lights) {\n    const extension = gltfScenegraph.addExtension(KHR_LIGHTS_PUNCTUAL);\n    // @ts-ignore\n    assert(!extension.lights);\n    // @ts-ignore\n    extension.lights = json.lights;\n    // @ts-ignore\n    delete json.lights;\n  }\n\n  // Any nodes that have lights field pointing to light object\n  // add the extension\n  // @ts-ignore\n  if (gltfScenegraph.json.lights) {\n    // @ts-ignore\n    for (const light of gltfScenegraph.json.lights) {\n      const node = light.node;\n      gltfScenegraph.addObjectExtension(node, KHR_LIGHTS_PUNCTUAL, light);\n    }\n    // @ts-ignore\n    delete gltfScenegraph.json.lights;\n  }\n}\n"], "mappings": "AAKA,SAAQA,MAAM,QAAO,oBAAoB;AACzC,OAAOC,cAAc,MAAM,2BAA2B;AAEtD,MAAMC,mBAAmB,GAAG,qBAAqB;AAEjD,OAAO,MAAMC,IAAI,GAAGD,mBAAmB;AAEvC,OAAO,eAAeE,MAAMA,CAACC,QAAsB,EAAiB;EAClE,MAAMC,cAAc,GAAG,IAAIL,cAAc,CAACI,QAAQ,CAAC;EACnD,MAAM;IAACE;EAAI,CAAC,GAAGD,cAAc;EAG7B,MAAME,SAAS,GAAGF,cAAc,CAACG,YAAY,CAACP,mBAAmB,CAAC;EAClE,IAAIM,SAAS,EAAE;IAEbF,cAAc,CAACC,IAAI,CAACG,MAAM,GAAGF,SAAS,CAACE,MAAM;IAC7CJ,cAAc,CAACK,eAAe,CAACT,mBAAmB,CAAC;EACrD;EAIA,KAAK,MAAMU,IAAI,IAAIL,IAAI,CAACM,KAAK,IAAI,EAAE,EAAE;IACnC,MAAMC,aAAa,GAAGR,cAAc,CAACS,kBAAkB,CAACH,IAAI,EAAEV,mBAAmB,CAAC;IAClF,IAAIY,aAAa,EAAE;MAEjBF,IAAI,CAACI,KAAK,GAAGF,aAAa,CAACE,KAAK;IAClC;IACAV,cAAc,CAACW,qBAAqB,CAACL,IAAI,EAAEV,mBAAmB,CAAC;EACjE;AACF;AAGA,OAAO,eAAegB,MAAMA,CAACb,QAAQ,EAAiB;EACpD,MAAMC,cAAc,GAAG,IAAIL,cAAc,CAACI,QAAQ,CAAC;EACnD,MAAM;IAACE;EAAI,CAAC,GAAGD,cAAc;EAG7B,IAAIC,IAAI,CAACG,MAAM,EAAE;IACf,MAAMF,SAAS,GAAGF,cAAc,CAACa,YAAY,CAACjB,mBAAmB,CAAC;IAElEF,MAAM,CAAC,CAACQ,SAAS,CAACE,MAAM,CAAC;IAEzBF,SAAS,CAACE,MAAM,GAAGH,IAAI,CAACG,MAAM;IAE9B,OAAOH,IAAI,CAACG,MAAM;EACpB;EAKA,IAAIJ,cAAc,CAACC,IAAI,CAACG,MAAM,EAAE;IAE9B,KAAK,MAAMM,KAAK,IAAIV,cAAc,CAACC,IAAI,CAACG,MAAM,EAAE;MAC9C,MAAME,IAAI,GAAGI,KAAK,CAACJ,IAAI;MACvBN,cAAc,CAACc,kBAAkB,CAACR,IAAI,EAAEV,mBAAmB,EAAEc,KAAK,CAAC;IACrE;IAEA,OAAOV,cAAc,CAACC,IAAI,CAACG,MAAM;EACnC;AACF"}