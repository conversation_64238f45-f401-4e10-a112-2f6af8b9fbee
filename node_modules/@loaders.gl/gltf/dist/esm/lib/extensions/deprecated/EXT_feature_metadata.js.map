{"version": 3, "file": "EXT_feature_metadata.js", "names": ["GLTFScenegraph", "EXT_FEATURE_METADATA", "name", "decode", "gltfData", "scenegraph", "decodeExtFeatureMetadata", "_extension$schema", "extension", "getExtension", "schemaClasses", "schema", "classes", "featureTables", "featureTextures", "console", "warn", "schemaName", "schemaClass", "featureTable", "findFeatureTableByName", "handleFeatureTableProperties", "propertyName", "properties", "_featureTable$propert", "schemaProperty", "featureTableProperty", "numberOfFeatures", "count", "data", "getPropertyDataFromBinarySource", "bufferView", "getTypedArrayForBufferView", "type", "stringOffsetBufferView", "offsetsData", "getStringAttributes", "schemaClassName", "featureTableName", "class", "stringsCount", "stringsArray", "textDecoder", "TextDecoder", "stringOffset", "bytesPerStringSize", "index", "stringByteSize", "stringData", "subarray", "stringAttribute", "push"], "sources": ["../../../../../src/lib/extensions/deprecated/EXT_feature_metadata.ts"], "sourcesContent": ["/* eslint-disable camelcase */\nimport type {GLTF} from '../../types/gltf-types';\n\nimport GLTFScenegraph from '../../api/gltf-scenegraph';\nimport {\n  ClassProperty,\n  EXT_feature_metadata_class_object,\n  EXT_feature_metadata_feature_table,\n  FeatureTableProperty,\n  GLTF_EXT_feature_metadata\n} from '../../types/gltf-json-schema';\n\n/** Extension name */\nconst EXT_FEATURE_METADATA = 'EXT_feature_metadata';\n\nexport const name = EXT_FEATURE_METADATA;\n\nexport async function decode(gltfData: {json: GLTF}): Promise<void> {\n  const scenegraph = new GLTFScenegraph(gltfData);\n  decodeExtFeatureMetadata(scenegraph);\n}\n\n/**\n * Decodes feature metadata from extension\n * @param scenegraph\n */\nfunction decodeExtFeatureMetadata(scenegraph: GLTFScenegraph): void {\n  const extension: GLTF_EXT_feature_metadata | null = scenegraph.getExtension(EXT_FEATURE_METADATA);\n  const schemaClasses = extension?.schema?.classes;\n  const featureTables = extension?.featureTables;\n  const featureTextures = extension?.featureTextures;\n\n  if (featureTextures) {\n    /*\n     * TODO add support for featureTextures\n     * Spec - https://github.com/CesiumGS/glTF/tree/3d-tiles-next/extensions/2.0/Vendor/EXT_feature_metadata#feature-textures\n     */\n    // eslint-disable-next-line no-console\n    console.warn('featureTextures is not yet supported in the \"EXT_feature_metadata\" extension.');\n  }\n\n  if (schemaClasses && featureTables) {\n    for (const schemaName in schemaClasses) {\n      const schemaClass = schemaClasses[schemaName];\n      const featureTable = findFeatureTableByName(featureTables, schemaName);\n\n      if (featureTable) {\n        handleFeatureTableProperties(scenegraph, featureTable, schemaClass);\n      }\n    }\n  }\n}\n\n/**\n * Navigate throw all properies in feature table and gets properties data.\n * @param scenegraph\n * @param featureTable\n * @param schemaClass\n */\nfunction handleFeatureTableProperties(\n  scenegraph: GLTFScenegraph,\n  featureTable: EXT_feature_metadata_feature_table,\n  schemaClass: EXT_feature_metadata_class_object\n): void {\n  for (const propertyName in schemaClass.properties) {\n    const schemaProperty = schemaClass.properties[propertyName];\n    const featureTableProperty = featureTable?.properties?.[propertyName];\n    const numberOfFeatures = featureTable.count;\n\n    if (featureTableProperty) {\n      const data = getPropertyDataFromBinarySource(\n        scenegraph,\n        schemaProperty,\n        numberOfFeatures,\n        featureTableProperty\n      );\n      featureTableProperty.data = data;\n    }\n  }\n}\n\n/**\n * Decode properties from binary sourse based on property type.\n * @param scenegraph\n * @param schemaProperty\n * @param numberOfFeatures\n * @param featureTableProperty\n */\nfunction getPropertyDataFromBinarySource(\n  scenegraph: GLTFScenegraph,\n  schemaProperty: ClassProperty,\n  numberOfFeatures: number,\n  featureTableProperty: FeatureTableProperty\n): Uint8Array | string[] {\n  const bufferView = featureTableProperty.bufferView;\n  // TODO think maybe we shouldn't get data only in Uint8Array format.\n  let data: Uint8Array | string[] = scenegraph.getTypedArrayForBufferView(bufferView);\n\n  switch (schemaProperty.type) {\n    case 'STRING': {\n      // stringOffsetBufferView should be available for string type.\n      const stringOffsetBufferView = featureTableProperty.stringOffsetBufferView!;\n      const offsetsData = scenegraph.getTypedArrayForBufferView(stringOffsetBufferView);\n      data = getStringAttributes(data, offsetsData, numberOfFeatures);\n      break;\n    }\n    default:\n  }\n\n  return data;\n}\n\n/**\n * Find the feature table by class name.\n * @param featureTables\n * @param schemaClassName\n */\nfunction findFeatureTableByName(\n  featureTables: {[key: string]: EXT_feature_metadata_feature_table},\n  schemaClassName: string\n): EXT_feature_metadata_feature_table | null {\n  for (const featureTableName in featureTables) {\n    const featureTable = featureTables[featureTableName];\n\n    if (featureTable.class === schemaClassName) {\n      return featureTable;\n    }\n  }\n\n  return null;\n}\n\n/**\n * Getting string attributes from binary data.\n * Spec - https://github.com/CesiumGS/3d-tiles/tree/main/specification/Metadata#strings\n * @param data\n * @param offsetsData\n * @param stringsCount\n */\nfunction getStringAttributes(\n  data: Uint8Array,\n  offsetsData: Uint8Array,\n  stringsCount: number\n): string[] {\n  const stringsArray: string[] = [];\n  const textDecoder = new TextDecoder('utf8');\n\n  let stringOffset = 0;\n  const bytesPerStringSize = 4;\n\n  for (let index = 0; index < stringsCount; index++) {\n    // TODO check if it is multiplication on bytesPerStringSize is valid operation?\n    const stringByteSize =\n      offsetsData[(index + 1) * bytesPerStringSize] - offsetsData[index * bytesPerStringSize];\n    const stringData = data.subarray(stringOffset, stringByteSize + stringOffset);\n    const stringAttribute = textDecoder.decode(stringData);\n\n    stringsArray.push(stringAttribute);\n    stringOffset += stringByteSize;\n  }\n\n  return stringsArray;\n}\n"], "mappings": "AAGA,OAAOA,cAAc,MAAM,2BAA2B;AAUtD,MAAMC,oBAAoB,GAAG,sBAAsB;AAEnD,OAAO,MAAMC,IAAI,GAAGD,oBAAoB;AAExC,OAAO,eAAeE,MAAMA,CAACC,QAAsB,EAAiB;EAClE,MAAMC,UAAU,GAAG,IAAIL,cAAc,CAACI,QAAQ,CAAC;EAC/CE,wBAAwB,CAACD,UAAU,CAAC;AACtC;AAMA,SAASC,wBAAwBA,CAACD,UAA0B,EAAQ;EAAA,IAAAE,iBAAA;EAClE,MAAMC,SAA2C,GAAGH,UAAU,CAACI,YAAY,CAACR,oBAAoB,CAAC;EACjG,MAAMS,aAAa,GAAGF,SAAS,aAATA,SAAS,wBAAAD,iBAAA,GAATC,SAAS,CAAEG,MAAM,cAAAJ,iBAAA,uBAAjBA,iBAAA,CAAmBK,OAAO;EAChD,MAAMC,aAAa,GAAGL,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEK,aAAa;EAC9C,MAAMC,eAAe,GAAGN,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEM,eAAe;EAElD,IAAIA,eAAe,EAAE;IAMnBC,OAAO,CAACC,IAAI,CAAC,+EAA+E,CAAC;EAC/F;EAEA,IAAIN,aAAa,IAAIG,aAAa,EAAE;IAClC,KAAK,MAAMI,UAAU,IAAIP,aAAa,EAAE;MACtC,MAAMQ,WAAW,GAAGR,aAAa,CAACO,UAAU,CAAC;MAC7C,MAAME,YAAY,GAAGC,sBAAsB,CAACP,aAAa,EAAEI,UAAU,CAAC;MAEtE,IAAIE,YAAY,EAAE;QAChBE,4BAA4B,CAAChB,UAAU,EAAEc,YAAY,EAAED,WAAW,CAAC;MACrE;IACF;EACF;AACF;AAQA,SAASG,4BAA4BA,CACnChB,UAA0B,EAC1Bc,YAAgD,EAChDD,WAA8C,EACxC;EACN,KAAK,MAAMI,YAAY,IAAIJ,WAAW,CAACK,UAAU,EAAE;IAAA,IAAAC,qBAAA;IACjD,MAAMC,cAAc,GAAGP,WAAW,CAACK,UAAU,CAACD,YAAY,CAAC;IAC3D,MAAMI,oBAAoB,GAAGP,YAAY,aAAZA,YAAY,wBAAAK,qBAAA,GAAZL,YAAY,CAAEI,UAAU,cAAAC,qBAAA,uBAAxBA,qBAAA,CAA2BF,YAAY,CAAC;IACrE,MAAMK,gBAAgB,GAAGR,YAAY,CAACS,KAAK;IAE3C,IAAIF,oBAAoB,EAAE;MACxB,MAAMG,IAAI,GAAGC,+BAA+B,CAC1CzB,UAAU,EACVoB,cAAc,EACdE,gBAAgB,EAChBD,oBACF,CAAC;MACDA,oBAAoB,CAACG,IAAI,GAAGA,IAAI;IAClC;EACF;AACF;AASA,SAASC,+BAA+BA,CACtCzB,UAA0B,EAC1BoB,cAA6B,EAC7BE,gBAAwB,EACxBD,oBAA0C,EACnB;EACvB,MAAMK,UAAU,GAAGL,oBAAoB,CAACK,UAAU;EAElD,IAAIF,IAA2B,GAAGxB,UAAU,CAAC2B,0BAA0B,CAACD,UAAU,CAAC;EAEnF,QAAQN,cAAc,CAACQ,IAAI;IACzB,KAAK,QAAQ;MAAE;QAEb,MAAMC,sBAAsB,GAAGR,oBAAoB,CAACQ,sBAAuB;QAC3E,MAAMC,WAAW,GAAG9B,UAAU,CAAC2B,0BAA0B,CAACE,sBAAsB,CAAC;QACjFL,IAAI,GAAGO,mBAAmB,CAACP,IAAI,EAAEM,WAAW,EAAER,gBAAgB,CAAC;QAC/D;MACF;IACA;EACF;EAEA,OAAOE,IAAI;AACb;AAOA,SAAST,sBAAsBA,CAC7BP,aAAkE,EAClEwB,eAAuB,EACoB;EAC3C,KAAK,MAAMC,gBAAgB,IAAIzB,aAAa,EAAE;IAC5C,MAAMM,YAAY,GAAGN,aAAa,CAACyB,gBAAgB,CAAC;IAEpD,IAAInB,YAAY,CAACoB,KAAK,KAAKF,eAAe,EAAE;MAC1C,OAAOlB,YAAY;IACrB;EACF;EAEA,OAAO,IAAI;AACb;AASA,SAASiB,mBAAmBA,CAC1BP,IAAgB,EAChBM,WAAuB,EACvBK,YAAoB,EACV;EACV,MAAMC,YAAsB,GAAG,EAAE;EACjC,MAAMC,WAAW,GAAG,IAAIC,WAAW,CAAC,MAAM,CAAC;EAE3C,IAAIC,YAAY,GAAG,CAAC;EACpB,MAAMC,kBAAkB,GAAG,CAAC;EAE5B,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGN,YAAY,EAAEM,KAAK,EAAE,EAAE;IAEjD,MAAMC,cAAc,GAClBZ,WAAW,CAAC,CAACW,KAAK,GAAG,CAAC,IAAID,kBAAkB,CAAC,GAAGV,WAAW,CAACW,KAAK,GAAGD,kBAAkB,CAAC;IACzF,MAAMG,UAAU,GAAGnB,IAAI,CAACoB,QAAQ,CAACL,YAAY,EAAEG,cAAc,GAAGH,YAAY,CAAC;IAC7E,MAAMM,eAAe,GAAGR,WAAW,CAACvC,MAAM,CAAC6C,UAAU,CAAC;IAEtDP,YAAY,CAACU,IAAI,CAACD,eAAe,CAAC;IAClCN,YAAY,IAAIG,cAAc;EAChC;EAEA,OAAON,YAAY;AACrB"}