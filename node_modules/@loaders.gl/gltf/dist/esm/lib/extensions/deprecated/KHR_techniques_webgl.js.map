{"version": 3, "file": "KHR_techniques_webgl.js", "names": ["GLTFScenegraph", "KHR_TECHNIQUES_WEBGL", "name", "decode", "gltfData", "gltfScenegraph", "json", "extension", "getExtension", "techniques", "resolveTechniques", "material", "materials", "materialExtension", "getObjectExtension", "technique", "Object", "assign", "values", "resolveValues", "removeObjectExtension", "removeExtension", "encode", "options", "techniquesExtension", "programs", "shaders", "textDecoder", "TextDecoder", "for<PERSON>ach", "shader", "Number", "isFinite", "bufferView", "code", "getTypedArrayForBufferView", "Error", "program", "fragmentShader", "vertexShader", "keys", "uniforms", "uniform", "value", "index", "undefined", "texture", "getTexture"], "sources": ["../../../../../src/lib/extensions/deprecated/KHR_techniques_webgl.ts"], "sourcesContent": ["// GLTF EXTENSION: KHR_techniques_webgl\n// https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_techniques_webgl\n\nimport type {GLTF} from '../../types/gltf-types';\n\nimport GLTFScenegraph from '../../api/gltf-scenegraph';\n\nconst KHR_TECHNIQUES_WEBGL = 'KHR_techniques_webgl';\n\nexport const name = KHR_TECHNIQUES_WEBGL;\n\nexport async function decode(gltfData: {json: GLTF}): Promise<void> {\n  const gltfScenegraph = new GLTFScenegraph(gltfData);\n  const {json} = gltfScenegraph;\n\n  const extension = gltfScenegraph.getExtension(KHR_TECHNIQUES_WEBGL);\n  if (extension) {\n    const techniques = resolveTechniques(extension, gltfScenegraph);\n\n    for (const material of json.materials || []) {\n      const materialExtension = gltfScenegraph.getObjectExtension(material, KHR_TECHNIQUES_WEBGL);\n      if (materialExtension) {\n        // @ts-ignore TODO\n        material.technique = Object.assign(\n          {},\n          materialExtension,\n          // @ts-ignore\n          techniques[materialExtension.technique]\n        );\n        // @ts-ignore TODO\n        material.technique.values = resolveValues(material.technique, gltfScenegraph);\n      }\n      gltfScenegraph.removeObjectExtension(material, KHR_TECHNIQUES_WEBGL);\n    }\n\n    // Remove the top-level extension\n    gltfScenegraph.removeExtension(KHR_TECHNIQUES_WEBGL);\n  }\n}\n// eslint-disable-next-line\nexport async function encode(gltfData, options): Promise<void> {\n  // TODO\n}\n\nfunction resolveTechniques(\n  techniquesExtension: {[key: string]: any},\n  // programs: {[key: string]: any}[],\n  // shaders: {[key: string]: any}[],\n  // techniques: {[key: string]: any}[]\n  gltfScenegraph\n) {\n  const {programs = [], shaders = [], techniques = []} = techniquesExtension;\n  const textDecoder = new TextDecoder();\n\n  shaders.forEach((shader) => {\n    if (Number.isFinite(shader.bufferView)) {\n      shader.code = textDecoder.decode(\n        gltfScenegraph.getTypedArrayForBufferView(shader.bufferView)\n      );\n    } else {\n      // TODO: handle URI shader\n      throw new Error('KHR_techniques_webgl: no shader code');\n    }\n  });\n\n  programs.forEach((program) => {\n    program.fragmentShader = shaders[program.fragmentShader];\n    program.vertexShader = shaders[program.vertexShader];\n  });\n\n  techniques.forEach((technique) => {\n    technique.program = programs[technique.program];\n  });\n\n  return techniques;\n}\n\nfunction resolveValues(technique, gltfScenegraph) {\n  const values = Object.assign({}, technique.values);\n\n  // merge values from uniforms\n  Object.keys(technique.uniforms || {}).forEach((uniform) => {\n    if (technique.uniforms[uniform].value && !(uniform in values)) {\n      values[uniform] = technique.uniforms[uniform].value;\n    }\n  });\n\n  // resolve textures\n  Object.keys(values).forEach((uniform) => {\n    if (typeof values[uniform] === 'object' && values[uniform].index !== undefined) {\n      // Assume this is a texture\n      // TODO: find if there are any other types that can be referenced\n      values[uniform].texture = gltfScenegraph.getTexture(values[uniform].index);\n    }\n  });\n\n  return values;\n}\n"], "mappings": "AAKA,OAAOA,cAAc,MAAM,2BAA2B;AAEtD,MAAMC,oBAAoB,GAAG,sBAAsB;AAEnD,OAAO,MAAMC,IAAI,GAAGD,oBAAoB;AAExC,OAAO,eAAeE,MAAMA,CAACC,QAAsB,EAAiB;EAClE,MAAMC,cAAc,GAAG,IAAIL,cAAc,CAACI,QAAQ,CAAC;EACnD,MAAM;IAACE;EAAI,CAAC,GAAGD,cAAc;EAE7B,MAAME,SAAS,GAAGF,cAAc,CAACG,YAAY,CAACP,oBAAoB,CAAC;EACnE,IAAIM,SAAS,EAAE;IACb,MAAME,UAAU,GAAGC,iBAAiB,CAACH,SAAS,EAAEF,cAAc,CAAC;IAE/D,KAAK,MAAMM,QAAQ,IAAIL,IAAI,CAACM,SAAS,IAAI,EAAE,EAAE;MAC3C,MAAMC,iBAAiB,GAAGR,cAAc,CAACS,kBAAkB,CAACH,QAAQ,EAAEV,oBAAoB,CAAC;MAC3F,IAAIY,iBAAiB,EAAE;QAErBF,QAAQ,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAChC,CAAC,CAAC,EACFJ,iBAAiB,EAEjBJ,UAAU,CAACI,iBAAiB,CAACE,SAAS,CACxC,CAAC;QAEDJ,QAAQ,CAACI,SAAS,CAACG,MAAM,GAAGC,aAAa,CAACR,QAAQ,CAACI,SAAS,EAAEV,cAAc,CAAC;MAC/E;MACAA,cAAc,CAACe,qBAAqB,CAACT,QAAQ,EAAEV,oBAAoB,CAAC;IACtE;IAGAI,cAAc,CAACgB,eAAe,CAACpB,oBAAoB,CAAC;EACtD;AACF;AAEA,OAAO,eAAeqB,MAAMA,CAAClB,QAAQ,EAAEmB,OAAO,EAAiB,CAE/D;AAEA,SAASb,iBAAiBA,CACxBc,mBAAyC,EAIzCnB,cAAc,EACd;EACA,MAAM;IAACoB,QAAQ,GAAG,EAAE;IAAEC,OAAO,GAAG,EAAE;IAAEjB,UAAU,GAAG;EAAE,CAAC,GAAGe,mBAAmB;EAC1E,MAAMG,WAAW,GAAG,IAAIC,WAAW,CAAC,CAAC;EAErCF,OAAO,CAACG,OAAO,CAAEC,MAAM,IAAK;IAC1B,IAAIC,MAAM,CAACC,QAAQ,CAACF,MAAM,CAACG,UAAU,CAAC,EAAE;MACtCH,MAAM,CAACI,IAAI,GAAGP,WAAW,CAACxB,MAAM,CAC9BE,cAAc,CAAC8B,0BAA0B,CAACL,MAAM,CAACG,UAAU,CAC7D,CAAC;IACH,CAAC,MAAM;MAEL,MAAM,IAAIG,KAAK,CAAC,sCAAsC,CAAC;IACzD;EACF,CAAC,CAAC;EAEFX,QAAQ,CAACI,OAAO,CAAEQ,OAAO,IAAK;IAC5BA,OAAO,CAACC,cAAc,GAAGZ,OAAO,CAACW,OAAO,CAACC,cAAc,CAAC;IACxDD,OAAO,CAACE,YAAY,GAAGb,OAAO,CAACW,OAAO,CAACE,YAAY,CAAC;EACtD,CAAC,CAAC;EAEF9B,UAAU,CAACoB,OAAO,CAAEd,SAAS,IAAK;IAChCA,SAAS,CAACsB,OAAO,GAAGZ,QAAQ,CAACV,SAAS,CAACsB,OAAO,CAAC;EACjD,CAAC,CAAC;EAEF,OAAO5B,UAAU;AACnB;AAEA,SAASU,aAAaA,CAACJ,SAAS,EAAEV,cAAc,EAAE;EAChD,MAAMa,MAAM,GAAGF,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,SAAS,CAACG,MAAM,CAAC;EAGlDF,MAAM,CAACwB,IAAI,CAACzB,SAAS,CAAC0B,QAAQ,IAAI,CAAC,CAAC,CAAC,CAACZ,OAAO,CAAEa,OAAO,IAAK;IACzD,IAAI3B,SAAS,CAAC0B,QAAQ,CAACC,OAAO,CAAC,CAACC,KAAK,IAAI,EAAED,OAAO,IAAIxB,MAAM,CAAC,EAAE;MAC7DA,MAAM,CAACwB,OAAO,CAAC,GAAG3B,SAAS,CAAC0B,QAAQ,CAACC,OAAO,CAAC,CAACC,KAAK;IACrD;EACF,CAAC,CAAC;EAGF3B,MAAM,CAACwB,IAAI,CAACtB,MAAM,CAAC,CAACW,OAAO,CAAEa,OAAO,IAAK;IACvC,IAAI,OAAOxB,MAAM,CAACwB,OAAO,CAAC,KAAK,QAAQ,IAAIxB,MAAM,CAACwB,OAAO,CAAC,CAACE,KAAK,KAAKC,SAAS,EAAE;MAG9E3B,MAAM,CAACwB,OAAO,CAAC,CAACI,OAAO,GAAGzC,cAAc,CAAC0C,UAAU,CAAC7B,MAAM,CAACwB,OAAO,CAAC,CAACE,KAAK,CAAC;IAC5E;EACF,CAAC,CAAC;EAEF,OAAO1B,MAAM;AACf"}