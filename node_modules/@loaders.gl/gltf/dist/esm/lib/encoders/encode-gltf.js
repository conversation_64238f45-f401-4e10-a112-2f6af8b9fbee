import encodeGLBSync from './encode-glb';
export function encodeGLT<PERSON>ync(gltf, arrayBuffer, byteOffset, options) {
  convertBuffersToBase64(gltf);
  return encodeGLBSync(gltf, arrayBuffer, byteOffset, options);
}
function convertBuffersToBase64(gltf) {
  let {
    firstBuffer = 0
  } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  if (gltf.buffers && gltf.buffers.length > firstBuffer) {
    throw new Error('encodeGLTF: multiple buffers not yet implemented');
  }
}
//# sourceMappingURL=encode-gltf.js.map