{"version": 3, "file": "normalize-gltf-v1.js", "names": ["KHR_binary_glTF", "GLTF_ARRAYS", "accessors", "animations", "buffers", "bufferViews", "images", "materials", "meshes", "nodes", "samplers", "scenes", "skins", "textures", "GLTF_KEYS", "accessor", "buffer", "bufferView", "image", "material", "mesh", "node", "sampler", "scene", "skin", "texture", "GLTFV1Normalizer", "constructor", "_defineProperty", "normalize", "gltf", "options", "json", "asset", "version", "undefined", "console", "warn", "concat", "Error", "_addAsset", "_convertTopLevelObjectsToArrays", "preprocess", "_convertObjectIdsToArrayIndices", "_updateObjects", "_updateMaterial", "generator", "arrayName", "_convertTopLevelObjectToArray", "mapName", "objectMap", "Array", "isArray", "id", "object", "index", "length", "push", "idToIndexMap", "_convertIdsToIndices", "_convertIdToIndex", "_convertTextureIds", "_convertMeshIds", "_convertNodeIds", "_convertSceneIds", "source", "primitive", "primitives", "attributes", "indices", "attributeName", "children", "map", "child", "topLevelArrayName", "key", "Number", "isFinite", "type", "_material$values", "_material$values2", "_material$values3", "pbrMetallicRoughness", "baseColorFactor", "metallicFactor", "roughnessFactor", "textureId", "values", "tex", "texture2d_0", "diffuseTex", "textureIndex", "findIndex", "baseColorTexture", "normalizeGLTFV1", "arguments"], "sources": ["../../../../src/lib/api/normalize-gltf-v1.ts"], "sourcesContent": ["/* eslint-disable camelcase */\nimport * as KHR_binary_glTF from '../extensions/KHR_binary_gltf';\n\n// Binary format changes (mainly implemented by GLBLoader)\n// https://github.com/KhronosGroup/glTF/tree/master/extensions/1.0/Khronos/KHR_binary_glTF\n\n// JSON format changes:\n// https://github.com/khronosgroup/gltf/issues/605\n\n// - [x] Top-level JSON objects are arrays now\n// - [ ] Removed indirection from animation: sampler now refers directly to accessors, #712\n// - [ ] material.parameter.value and technique.parameter.value must be an array, #690\n// - [ ] Node can have only one mesh #821\n// - [ ] Added reqs on JSON encoding\n// - [ ] Added reqs on binary data alignment #802 (comment)\n\n// Additions:\n// - [ ] Added accessor.normalized, #691, #706\n// - [ ] Added glExtensionsUsed property and 5125 (UNSIGNED_INT) accessor.componentType value, #619\n// - [ ] Added extensionsRequired property, #720, #721\n// - [ ] Added \"STEP\" as valid animation.sampler.interpolation value, #712\n\n// Removals:\n// - [x] Removed buffer.type, #786, #629\n// - [ ] Removed revision number from profile.version, #709\n// - [ ] Removed technique.functions.scissor and removed 3089 (SCISSOR_TEST) as a valid value for technique.states.enable, #681\n// - [ ] Techniques, programs, and shaders were moved out to KHR_technique_webgl extension.\n\n// Other edits:\n// - [x] asset is now required, #642\n// - [ ] buffer.byteLength and bufferView.byteLength are now required, #560.\n// - [ ] accessor.min and accessor.max are now required, #593, and clarified that the JSON value and binary data must be the same, #628.\n// - [ ] Clarified animation.sampler and animation.channel restrictions, #712\n// - [ ] skin.inverseBindMatrices is now optional, #461.\n// - [ ] Attribute parameters can't have a value defined in the technique or parameter, #563 (comment).\n// - [ ] Only TEXCOORD and COLOR attribute semantics can be written in the form [semantic]_[set_index], #563 (comment).\n// - [ ] TEXCOORD and COLOR attribute semantics must be written in the form [semantic]_[set_index], e.g., just TEXCOORD should be TEXCOORD_0, and just COLOR should be COLOR_0, #649\n// - [ ] camera.perspective.aspectRatio and camera.perspective.yfov must now be > 0, not >= 0, #563 (comment).\n// - [ ] Application-specific parameter semantics must start with an underscore, e.g., _TEMPERATURE and _SIMULATION_TIME, #563 (comment).\n// - [ ] Properties in technique.parameters must be defined in technique.uniforms or technique.attributes,\n\n// #563 (comment).\n// - [ ] technique.parameter.count can only be defined when the semantic is JOINTMATRIX or an application-specific semantic is used. It can never be defined for attribute parameters; only uniforms, d2f6945\n// - [ ] technique.parameter.semantic is required when the parameter is an attribute, 28e113d\n// - [ ] Mesh-only models are allowed, e.g., without materials, #642\n// - [ ] Skeleton hierarchies (nodes containing jointName) must be separated from non-skeleton hierarchies., #647\n// - [ ] technique.states.functions.blendColor and technique.states.functions.depthRange parameters now must match WebGL function min/max, #707\n\nconst GLTF_ARRAYS = {\n  accessors: 'accessor',\n  animations: 'animation',\n  buffers: 'buffer',\n  bufferViews: 'bufferView',\n  images: 'image',\n  materials: 'material',\n  meshes: 'mesh',\n  nodes: 'node',\n  samplers: 'sampler',\n  scenes: 'scene',\n  skins: 'skin',\n  textures: 'texture'\n};\n\nconst GLTF_KEYS = {\n  accessor: 'accessors',\n  animations: 'animation',\n  buffer: 'buffers',\n  bufferView: 'bufferViews',\n  image: 'images',\n  material: 'materials',\n  mesh: 'meshes',\n  node: 'nodes',\n  sampler: 'samplers',\n  scene: 'scenes',\n  skin: 'skins',\n  texture: 'textures'\n};\n\n/**\n * Converts (normalizes) glTF v1 to v2\n */\nclass GLTFV1Normalizer {\n  idToIndexMap = {\n    animations: {},\n    accessors: {},\n    buffers: {},\n    bufferViews: {},\n    images: {},\n    materials: {},\n    meshes: {},\n    nodes: {},\n    samplers: {},\n    scenes: {},\n    skins: {},\n    textures: {}\n  };\n\n  json;\n\n  // constructor() {}\n\n  /**\n   * Convert (normalize) glTF < 2.0 to glTF 2.0\n   * @param gltf - object with json and binChunks\n   * @param options\n   * @param options normalize Whether to actually normalize\n   */\n  normalize(gltf, options) {\n    this.json = gltf.json;\n    const json = gltf.json;\n\n    // Check version\n    switch (json.asset && json.asset.version) {\n      // We are converting to v2 format. Return if there is nothing to do\n      case '2.0':\n        return;\n\n      // This class is written to convert 1.0\n      case undefined:\n      case '1.0':\n        break;\n\n      default:\n        // eslint-disable-next-line no-undef, no-console\n        console.warn(`glTF: Unknown version ${json.asset.version}`);\n        return;\n    }\n\n    if (!options.normalize) {\n      // We are still missing a few conversion tricks, remove once addressed\n      throw new Error('glTF v1 is not supported.');\n    }\n\n    // eslint-disable-next-line no-undef, no-console\n    console.warn('Converting glTF v1 to glTF v2 format. This is experimental and may fail.');\n\n    this._addAsset(json);\n\n    // In glTF2 top-level fields are Arrays not Object maps\n    this._convertTopLevelObjectsToArrays(json);\n\n    // Extract bufferView indices for images\n    // (this extension needs to be invoked early in the normalization process)\n    // TODO can this be handled by standard extension processing instead of called explicitly?\n    KHR_binary_glTF.preprocess(gltf);\n\n    // Convert object references from ids to indices\n    this._convertObjectIdsToArrayIndices(json);\n\n    this._updateObjects(json);\n\n    this._updateMaterial(json);\n  }\n\n  // asset is now required, #642 https://github.com/KhronosGroup/glTF/issues/639\n  _addAsset(json) {\n    json.asset = json.asset || {};\n    // We are normalizing to glTF v2, so change version to \"2.0\"\n    json.asset.version = '2.0';\n    json.asset.generator = json.asset.generator || 'Normalized to glTF 2.0 by loaders.gl';\n  }\n\n  _convertTopLevelObjectsToArrays(json) {\n    // TODO check that all arrays are covered\n    for (const arrayName in GLTF_ARRAYS) {\n      this._convertTopLevelObjectToArray(json, arrayName);\n    }\n  }\n\n  /** Convert one top level object to array */\n  _convertTopLevelObjectToArray(json, mapName) {\n    const objectMap = json[mapName];\n    if (!objectMap || Array.isArray(objectMap)) {\n      return;\n    }\n\n    // Rewrite the top-level field as an array\n    json[mapName] = [];\n    // Copy the map key into object.id\n    for (const id in objectMap) {\n      const object = objectMap[id];\n      object.id = object.id || id; // Mutates the loaded object\n      const index = json[mapName].length;\n      json[mapName].push(object);\n      this.idToIndexMap[mapName][id] = index;\n    }\n  }\n\n  /** Go through all objects in all top-level arrays and replace ids with indices */\n  _convertObjectIdsToArrayIndices(json) {\n    for (const arrayName in GLTF_ARRAYS) {\n      this._convertIdsToIndices(json, arrayName);\n    }\n    if ('scene' in json) {\n      json.scene = this._convertIdToIndex(json.scene, 'scene');\n    }\n\n    // Convert any index references that are not using array names\n\n    // texture.source (image)\n    for (const texture of json.textures) {\n      this._convertTextureIds(texture);\n    }\n    for (const mesh of json.meshes) {\n      this._convertMeshIds(mesh);\n    }\n    for (const node of json.nodes) {\n      this._convertNodeIds(node);\n    }\n    for (const node of json.scenes) {\n      this._convertSceneIds(node);\n    }\n  }\n\n  _convertTextureIds(texture) {\n    if (texture.source) {\n      texture.source = this._convertIdToIndex(texture.source, 'image');\n    }\n  }\n\n  _convertMeshIds(mesh) {\n    for (const primitive of mesh.primitives) {\n      const {attributes, indices, material} = primitive;\n      for (const attributeName in attributes) {\n        attributes[attributeName] = this._convertIdToIndex(attributes[attributeName], 'accessor');\n      }\n      if (indices) {\n        primitive.indices = this._convertIdToIndex(indices, 'accessor');\n      }\n      if (material) {\n        primitive.material = this._convertIdToIndex(material, 'material');\n      }\n    }\n  }\n\n  _convertNodeIds(node) {\n    if (node.children) {\n      node.children = node.children.map((child) => this._convertIdToIndex(child, 'node'));\n    }\n    if (node.meshes) {\n      node.meshes = node.meshes.map((mesh) => this._convertIdToIndex(mesh, 'mesh'));\n    }\n  }\n\n  _convertSceneIds(scene) {\n    if (scene.nodes) {\n      scene.nodes = scene.nodes.map((node) => this._convertIdToIndex(node, 'node'));\n    }\n  }\n\n  /** Go through all objects in a top-level array and replace ids with indices */\n  _convertIdsToIndices(json, topLevelArrayName) {\n    if (!json[topLevelArrayName]) {\n      console.warn(`gltf v1: json doesn't contain attribute ${topLevelArrayName}`); // eslint-disable-line no-console, no-undef\n      json[topLevelArrayName] = [];\n    }\n    for (const object of json[topLevelArrayName]) {\n      for (const key in object) {\n        const id = object[key];\n        const index = this._convertIdToIndex(id, key);\n        object[key] = index;\n      }\n    }\n  }\n\n  _convertIdToIndex(id, key) {\n    const arrayName = GLTF_KEYS[key];\n    if (arrayName in this.idToIndexMap) {\n      const index = this.idToIndexMap[arrayName][id];\n      if (!Number.isFinite(index)) {\n        throw new Error(`gltf v1: failed to resolve ${key} with id ${id}`);\n      }\n      return index;\n    }\n    return id;\n  }\n\n  /**\n   *\n   * @param {*} json\n   */\n  _updateObjects(json) {\n    for (const buffer of this.json.buffers) {\n      // - [x] Removed buffer.type, #786, #629\n      delete buffer.type;\n    }\n  }\n\n  /**\n   * Update material (set pbrMetallicRoughness)\n   * @param {*} json\n   */\n  _updateMaterial(json) {\n    for (const material of json.materials) {\n      material.pbrMetallicRoughness = {\n        baseColorFactor: [1, 1, 1, 1],\n        metallicFactor: 1,\n        roughnessFactor: 1\n      };\n\n      const textureId =\n        material.values?.tex || material.values?.texture2d_0 || material.values?.diffuseTex;\n      const textureIndex = json.textures.findIndex((texture) => texture.id === textureId);\n      if (textureIndex !== -1) {\n        material.pbrMetallicRoughness.baseColorTexture = {index: textureIndex};\n      }\n    }\n  }\n}\n\nexport function normalizeGLTFV1(gltf, options = {}) {\n  return new GLTFV1Normalizer().normalize(gltf, options);\n}\n"], "mappings": ";AACA,OAAO,KAAKA,eAAe,MAAM,+BAA+B;AA+ChE,MAAMC,WAAW,GAAG;EAClBC,SAAS,EAAE,UAAU;EACrBC,UAAU,EAAE,WAAW;EACvBC,OAAO,EAAE,QAAQ;EACjBC,WAAW,EAAE,YAAY;EACzBC,MAAM,EAAE,OAAO;EACfC,SAAS,EAAE,UAAU;EACrBC,MAAM,EAAE,MAAM;EACdC,KAAK,EAAE,MAAM;EACbC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,OAAO;EACfC,KAAK,EAAE,MAAM;EACbC,QAAQ,EAAE;AACZ,CAAC;AAED,MAAMC,SAAS,GAAG;EAChBC,QAAQ,EAAE,WAAW;EACrBZ,UAAU,EAAE,WAAW;EACvBa,MAAM,EAAE,SAAS;EACjBC,UAAU,EAAE,aAAa;EACzBC,KAAK,EAAE,QAAQ;EACfC,QAAQ,EAAE,WAAW;EACrBC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,OAAO;EACbC,OAAO,EAAE,UAAU;EACnBC,KAAK,EAAE,QAAQ;EACfC,IAAI,EAAE,OAAO;EACbC,OAAO,EAAE;AACX,CAAC;AAKD,MAAMC,gBAAgB,CAAC;EAAAC,YAAA;IAAAC,eAAA,uBACN;MACbzB,UAAU,EAAE,CAAC,CAAC;MACdD,SAAS,EAAE,CAAC,CAAC;MACbE,OAAO,EAAE,CAAC,CAAC;MACXC,WAAW,EAAE,CAAC,CAAC;MACfC,MAAM,EAAE,CAAC,CAAC;MACVC,SAAS,EAAE,CAAC,CAAC;MACbC,MAAM,EAAE,CAAC,CAAC;MACVC,KAAK,EAAE,CAAC,CAAC;MACTC,QAAQ,EAAE,CAAC,CAAC;MACZC,MAAM,EAAE,CAAC,CAAC;MACVC,KAAK,EAAE,CAAC,CAAC;MACTC,QAAQ,EAAE,CAAC;IACb,CAAC;IAAAe,eAAA;EAAA;EAYDC,SAASA,CAACC,IAAI,EAAEC,OAAO,EAAE;IACvB,IAAI,CAACC,IAAI,GAAGF,IAAI,CAACE,IAAI;IACrB,MAAMA,IAAI,GAAGF,IAAI,CAACE,IAAI;IAGtB,QAAQA,IAAI,CAACC,KAAK,IAAID,IAAI,CAACC,KAAK,CAACC,OAAO;MAEtC,KAAK,KAAK;QACR;MAGF,KAAKC,SAAS;MACd,KAAK,KAAK;QACR;MAEF;QAEEC,OAAO,CAACC,IAAI,0BAAAC,MAAA,CAA0BN,IAAI,CAACC,KAAK,CAACC,OAAO,CAAE,CAAC;QAC3D;IACJ;IAEA,IAAI,CAACH,OAAO,CAACF,SAAS,EAAE;MAEtB,MAAM,IAAIU,KAAK,CAAC,2BAA2B,CAAC;IAC9C;IAGAH,OAAO,CAACC,IAAI,CAAC,0EAA0E,CAAC;IAExF,IAAI,CAACG,SAAS,CAACR,IAAI,CAAC;IAGpB,IAAI,CAACS,+BAA+B,CAACT,IAAI,CAAC;IAK1ChC,eAAe,CAAC0C,UAAU,CAACZ,IAAI,CAAC;IAGhC,IAAI,CAACa,+BAA+B,CAACX,IAAI,CAAC;IAE1C,IAAI,CAACY,cAAc,CAACZ,IAAI,CAAC;IAEzB,IAAI,CAACa,eAAe,CAACb,IAAI,CAAC;EAC5B;EAGAQ,SAASA,CAACR,IAAI,EAAE;IACdA,IAAI,CAACC,KAAK,GAAGD,IAAI,CAACC,KAAK,IAAI,CAAC,CAAC;IAE7BD,IAAI,CAACC,KAAK,CAACC,OAAO,GAAG,KAAK;IAC1BF,IAAI,CAACC,KAAK,CAACa,SAAS,GAAGd,IAAI,CAACC,KAAK,CAACa,SAAS,IAAI,sCAAsC;EACvF;EAEAL,+BAA+BA,CAACT,IAAI,EAAE;IAEpC,KAAK,MAAMe,SAAS,IAAI9C,WAAW,EAAE;MACnC,IAAI,CAAC+C,6BAA6B,CAAChB,IAAI,EAAEe,SAAS,CAAC;IACrD;EACF;EAGAC,6BAA6BA,CAAChB,IAAI,EAAEiB,OAAO,EAAE;IAC3C,MAAMC,SAAS,GAAGlB,IAAI,CAACiB,OAAO,CAAC;IAC/B,IAAI,CAACC,SAAS,IAAIC,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,EAAE;MAC1C;IACF;IAGAlB,IAAI,CAACiB,OAAO,CAAC,GAAG,EAAE;IAElB,KAAK,MAAMI,EAAE,IAAIH,SAAS,EAAE;MAC1B,MAAMI,MAAM,GAAGJ,SAAS,CAACG,EAAE,CAAC;MAC5BC,MAAM,CAACD,EAAE,GAAGC,MAAM,CAACD,EAAE,IAAIA,EAAE;MAC3B,MAAME,KAAK,GAAGvB,IAAI,CAACiB,OAAO,CAAC,CAACO,MAAM;MAClCxB,IAAI,CAACiB,OAAO,CAAC,CAACQ,IAAI,CAACH,MAAM,CAAC;MAC1B,IAAI,CAACI,YAAY,CAACT,OAAO,CAAC,CAACI,EAAE,CAAC,GAAGE,KAAK;IACxC;EACF;EAGAZ,+BAA+BA,CAACX,IAAI,EAAE;IACpC,KAAK,MAAMe,SAAS,IAAI9C,WAAW,EAAE;MACnC,IAAI,CAAC0D,oBAAoB,CAAC3B,IAAI,EAAEe,SAAS,CAAC;IAC5C;IACA,IAAI,OAAO,IAAIf,IAAI,EAAE;MACnBA,IAAI,CAACT,KAAK,GAAG,IAAI,CAACqC,iBAAiB,CAAC5B,IAAI,CAACT,KAAK,EAAE,OAAO,CAAC;IAC1D;IAKA,KAAK,MAAME,OAAO,IAAIO,IAAI,CAACnB,QAAQ,EAAE;MACnC,IAAI,CAACgD,kBAAkB,CAACpC,OAAO,CAAC;IAClC;IACA,KAAK,MAAML,IAAI,IAAIY,IAAI,CAACxB,MAAM,EAAE;MAC9B,IAAI,CAACsD,eAAe,CAAC1C,IAAI,CAAC;IAC5B;IACA,KAAK,MAAMC,IAAI,IAAIW,IAAI,CAACvB,KAAK,EAAE;MAC7B,IAAI,CAACsD,eAAe,CAAC1C,IAAI,CAAC;IAC5B;IACA,KAAK,MAAMA,IAAI,IAAIW,IAAI,CAACrB,MAAM,EAAE;MAC9B,IAAI,CAACqD,gBAAgB,CAAC3C,IAAI,CAAC;IAC7B;EACF;EAEAwC,kBAAkBA,CAACpC,OAAO,EAAE;IAC1B,IAAIA,OAAO,CAACwC,MAAM,EAAE;MAClBxC,OAAO,CAACwC,MAAM,GAAG,IAAI,CAACL,iBAAiB,CAACnC,OAAO,CAACwC,MAAM,EAAE,OAAO,CAAC;IAClE;EACF;EAEAH,eAAeA,CAAC1C,IAAI,EAAE;IACpB,KAAK,MAAM8C,SAAS,IAAI9C,IAAI,CAAC+C,UAAU,EAAE;MACvC,MAAM;QAACC,UAAU;QAAEC,OAAO;QAAElD;MAAQ,CAAC,GAAG+C,SAAS;MACjD,KAAK,MAAMI,aAAa,IAAIF,UAAU,EAAE;QACtCA,UAAU,CAACE,aAAa,CAAC,GAAG,IAAI,CAACV,iBAAiB,CAACQ,UAAU,CAACE,aAAa,CAAC,EAAE,UAAU,CAAC;MAC3F;MACA,IAAID,OAAO,EAAE;QACXH,SAAS,CAACG,OAAO,GAAG,IAAI,CAACT,iBAAiB,CAACS,OAAO,EAAE,UAAU,CAAC;MACjE;MACA,IAAIlD,QAAQ,EAAE;QACZ+C,SAAS,CAAC/C,QAAQ,GAAG,IAAI,CAACyC,iBAAiB,CAACzC,QAAQ,EAAE,UAAU,CAAC;MACnE;IACF;EACF;EAEA4C,eAAeA,CAAC1C,IAAI,EAAE;IACpB,IAAIA,IAAI,CAACkD,QAAQ,EAAE;MACjBlD,IAAI,CAACkD,QAAQ,GAAGlD,IAAI,CAACkD,QAAQ,CAACC,GAAG,CAAEC,KAAK,IAAK,IAAI,CAACb,iBAAiB,CAACa,KAAK,EAAE,MAAM,CAAC,CAAC;IACrF;IACA,IAAIpD,IAAI,CAACb,MAAM,EAAE;MACfa,IAAI,CAACb,MAAM,GAAGa,IAAI,CAACb,MAAM,CAACgE,GAAG,CAAEpD,IAAI,IAAK,IAAI,CAACwC,iBAAiB,CAACxC,IAAI,EAAE,MAAM,CAAC,CAAC;IAC/E;EACF;EAEA4C,gBAAgBA,CAACzC,KAAK,EAAE;IACtB,IAAIA,KAAK,CAACd,KAAK,EAAE;MACfc,KAAK,CAACd,KAAK,GAAGc,KAAK,CAACd,KAAK,CAAC+D,GAAG,CAAEnD,IAAI,IAAK,IAAI,CAACuC,iBAAiB,CAACvC,IAAI,EAAE,MAAM,CAAC,CAAC;IAC/E;EACF;EAGAsC,oBAAoBA,CAAC3B,IAAI,EAAE0C,iBAAiB,EAAE;IAC5C,IAAI,CAAC1C,IAAI,CAAC0C,iBAAiB,CAAC,EAAE;MAC5BtC,OAAO,CAACC,IAAI,4CAAAC,MAAA,CAA4CoC,iBAAiB,CAAE,CAAC;MAC5E1C,IAAI,CAAC0C,iBAAiB,CAAC,GAAG,EAAE;IAC9B;IACA,KAAK,MAAMpB,MAAM,IAAItB,IAAI,CAAC0C,iBAAiB,CAAC,EAAE;MAC5C,KAAK,MAAMC,GAAG,IAAIrB,MAAM,EAAE;QACxB,MAAMD,EAAE,GAAGC,MAAM,CAACqB,GAAG,CAAC;QACtB,MAAMpB,KAAK,GAAG,IAAI,CAACK,iBAAiB,CAACP,EAAE,EAAEsB,GAAG,CAAC;QAC7CrB,MAAM,CAACqB,GAAG,CAAC,GAAGpB,KAAK;MACrB;IACF;EACF;EAEAK,iBAAiBA,CAACP,EAAE,EAAEsB,GAAG,EAAE;IACzB,MAAM5B,SAAS,GAAGjC,SAAS,CAAC6D,GAAG,CAAC;IAChC,IAAI5B,SAAS,IAAI,IAAI,CAACW,YAAY,EAAE;MAClC,MAAMH,KAAK,GAAG,IAAI,CAACG,YAAY,CAACX,SAAS,CAAC,CAACM,EAAE,CAAC;MAC9C,IAAI,CAACuB,MAAM,CAACC,QAAQ,CAACtB,KAAK,CAAC,EAAE;QAC3B,MAAM,IAAIhB,KAAK,+BAAAD,MAAA,CAA+BqC,GAAG,eAAArC,MAAA,CAAYe,EAAE,CAAE,CAAC;MACpE;MACA,OAAOE,KAAK;IACd;IACA,OAAOF,EAAE;EACX;EAMAT,cAAcA,CAACZ,IAAI,EAAE;IACnB,KAAK,MAAMhB,MAAM,IAAI,IAAI,CAACgB,IAAI,CAAC5B,OAAO,EAAE;MAEtC,OAAOY,MAAM,CAAC8D,IAAI;IACpB;EACF;EAMAjC,eAAeA,CAACb,IAAI,EAAE;IACpB,KAAK,MAAMb,QAAQ,IAAIa,IAAI,CAACzB,SAAS,EAAE;MAAA,IAAAwE,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA;MACrC9D,QAAQ,CAAC+D,oBAAoB,GAAG;QAC9BC,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC7BC,cAAc,EAAE,CAAC;QACjBC,eAAe,EAAE;MACnB,CAAC;MAED,MAAMC,SAAS,GACb,EAAAP,gBAAA,GAAA5D,QAAQ,CAACoE,MAAM,cAAAR,gBAAA,uBAAfA,gBAAA,CAAiBS,GAAG,OAAAR,iBAAA,GAAI7D,QAAQ,CAACoE,MAAM,cAAAP,iBAAA,uBAAfA,iBAAA,CAAiBS,WAAW,OAAAR,iBAAA,GAAI9D,QAAQ,CAACoE,MAAM,cAAAN,iBAAA,uBAAfA,iBAAA,CAAiBS,UAAU;MACrF,MAAMC,YAAY,GAAG3D,IAAI,CAACnB,QAAQ,CAAC+E,SAAS,CAAEnE,OAAO,IAAKA,OAAO,CAAC4B,EAAE,KAAKiC,SAAS,CAAC;MACnF,IAAIK,YAAY,KAAK,CAAC,CAAC,EAAE;QACvBxE,QAAQ,CAAC+D,oBAAoB,CAACW,gBAAgB,GAAG;UAACtC,KAAK,EAAEoC;QAAY,CAAC;MACxE;IACF;EACF;AACF;AAEA,OAAO,SAASG,eAAeA,CAAChE,IAAI,EAAgB;EAAA,IAAdC,OAAO,GAAAgE,SAAA,CAAAvC,MAAA,QAAAuC,SAAA,QAAA5D,SAAA,GAAA4D,SAAA,MAAG,CAAC,CAAC;EAChD,OAAO,IAAIrE,gBAAgB,CAAC,CAAC,CAACG,SAAS,CAACC,IAAI,EAAEC,OAAO,CAAC;AACxD"}