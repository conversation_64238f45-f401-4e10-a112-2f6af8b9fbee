{"version": 3, "file": "gltf-scenegraph.js", "names": ["getBinaryImageMetadata", "padToNBytes", "copyToArray", "assert", "getAccessorArrayTypeAndLength", "getAccessorTypeFromSize", "getComponentTypeFromArray", "DEFAULT_GLTF_JSON", "asset", "version", "generator", "buffers", "GLTFScenegraph", "constructor", "gltf", "_defineProperty", "json", "sourceBuffers", "byteLength", "getApplicationData", "key", "data", "getExtraData", "extras", "getExtension", "extensionName", "isExtension", "getUsedExtensions", "find", "name", "extensions", "getRequiredExtension", "isRequired", "getRequiredExtensions", "extensionsRequired", "extensionsUsed", "getRemovedExtensions", "extensionsRemoved", "getObjectExtension", "object", "getScene", "index", "getObject", "getNode", "getSkin", "<PERSON><PERSON><PERSON>", "getMaterial", "getAccessor", "getTexture", "getSampler", "getImage", "getBufferView", "<PERSON><PERSON><PERSON><PERSON>", "array", "Error", "concat", "getTypedArrayForBufferView", "bufferView", "bufferIndex", "buffer", "binChunk", "byteOffset", "Uint8Array", "arrayBuffer", "getTypedArrayForAccessor", "accessor", "ArrayType", "length", "getTypedArrayForImageData", "image", "addApplicationData", "addExtraData", "addObjectExtension", "registerUsedExtension", "setObjectExtension", "removeObjectExtension", "extension", "addExtension", "extensionData", "arguments", "undefined", "addRequiredExtension", "registerRequiredExtension", "ext", "push", "removeExtension", "_removeStringFromArray", "Array", "isArray", "includes", "setDefaultScene", "sceneIndex", "scene", "addScene", "nodeIndices", "scenes", "nodes", "addNode", "node", "meshIndex", "matrix", "nodeData", "mesh", "<PERSON><PERSON><PERSON>", "attributes", "indices", "material", "mode", "accessors", "_addAttributes", "glTFMesh", "primitives", "indicesAccessor", "_addIndices", "Number", "isFinite", "meshes", "addPointCloud", "accessorIndices", "addImage", "imageData", "mimeTypeOpt", "metadata", "mimeType", "bufferViewIndex", "addBufferView", "glTFImage", "images", "glTFBufferView", "bufferViews", "addAccessor", "glTFAccessor", "type", "size", "componentType", "count", "max", "min", "addBinaryBuffer", "sourceBuffer", "minMax", "_getAccessorMinMax", "accessorDefaults", "Math", "round", "Object", "assign", "addTexture", "texture", "imageIndex", "glTFTexture", "source", "textures", "addMaterial", "pbrMaterialInfo", "materials", "createBinaryChunk", "_this$json", "_this$json$buffers", "totalByteLength", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "targetArray", "dstByteOffset", "binary", "string", "found", "indexOf", "splice", "result", "<PERSON><PERSON><PERSON>", "attributeData", "attrName", "_getGltfAttributeName", "value", "attributeName", "toLowerCase", "initValues", "subarray", "componentIndex"], "sources": ["../../../../src/lib/api/gltf-scenegraph.ts"], "sourcesContent": ["import type {\n  <PERSON>LTF,\n  GLT<PERSON><PERSON>,\n  GLT<PERSON>Node,\n  GLT<PERSON>esh,\n  GLT<PERSON><PERSON>,\n  GLTFMaterial,\n  GLTFAccessor,\n  GLTFSampler,\n  GLTFTexture,\n  GLTFImage,\n  GLTFBuffer,\n  GLTFBufferView,\n  GLTFWithBuffers\n} from '../types/gltf-types';\n\nimport {getBinaryImageMetadata} from '@loaders.gl/images';\nimport {padToNBytes, copyToArray} from '@loaders.gl/loader-utils';\nimport {assert} from '../utils/assert';\nimport {\n  getAccessorArrayTypeAndLength,\n  getAccessorTypeFromSize,\n  getComponentTypeFromArray\n} from '../gltf-utils/gltf-utils';\n\nconst DEFAULT_GLTF_JSON: GLTF = {\n  asset: {\n    version: '2.0',\n    generator: 'loaders.gl'\n  },\n  buffers: []\n};\n\ntype Extension = {[key: string]: any};\n/**\n * Class for structured access to GLTF data\n */\nexport default class GLTFScenegraph {\n  // internal\n  gltf: GLTFWithBuffers;\n  sourceBuffers: any[];\n  byteLength: number;\n\n  constructor(gltf?: {json: GLTF; buffers?: any[]}) {\n    // @ts-ignore\n    this.gltf = gltf || {\n      json: {...DEFAULT_GLTF_JSON},\n      buffers: []\n    };\n    this.sourceBuffers = [];\n    this.byteLength = 0;\n\n    // Initialize buffers\n    if (this.gltf.buffers && this.gltf.buffers[0]) {\n      this.byteLength = this.gltf.buffers[0].byteLength;\n      this.sourceBuffers = [this.gltf.buffers[0]];\n    }\n  }\n\n  // Accessors\n\n  get json(): GLTF {\n    return this.gltf.json;\n  }\n\n  getApplicationData(key: string): unknown {\n    // TODO - Data is already unpacked by GLBParser\n    const data = this.json[key];\n    return data;\n  }\n\n  getExtraData(key: string): {[key: string]: unknown} {\n    // TODO - Data is already unpacked by GLBParser\n    const extras = this.json.extras || {};\n    return extras[key];\n  }\n\n  getExtension<T = Extension>(extensionName: string): T | null {\n    const isExtension = this.getUsedExtensions().find((name) => name === extensionName);\n    const extensions = this.json.extensions || {};\n    return isExtension ? extensions[extensionName] || true : null;\n  }\n\n  getRequiredExtension<T = Extension>(extensionName: string): T | null {\n    const isRequired = this.getRequiredExtensions().find((name) => name === extensionName);\n    return isRequired ? this.getExtension(extensionName) : null;\n  }\n\n  getRequiredExtensions(): string[] {\n    return this.json.extensionsRequired || [];\n  }\n\n  getUsedExtensions(): string[] {\n    return this.json.extensionsUsed || [];\n  }\n\n  getRemovedExtensions(): string[] {\n    return (this.json.extensionsRemoved || []) as string[];\n  }\n\n  getObjectExtension<T = Extension>(object: {[key: string]: any}, extensionName: string): T | null {\n    const extensions = object.extensions || {};\n    return extensions[extensionName];\n  }\n\n  getScene(index: number): GLTFScene {\n    return this.getObject('scenes', index) as GLTFScene;\n  }\n\n  getNode(index: number): GLTFNode {\n    return this.getObject('nodes', index) as GLTFNode;\n  }\n\n  getSkin(index: number): GLTFSkin {\n    return this.getObject('skins', index) as GLTFSkin;\n  }\n\n  getMesh(index: number): GLTFMesh {\n    return this.getObject('meshes', index) as GLTFMesh;\n  }\n\n  getMaterial(index: number): GLTFMaterial {\n    return this.getObject('materials', index) as GLTFMaterial;\n  }\n\n  getAccessor(index: number): GLTFAccessor {\n    return this.getObject('accessors', index) as GLTFAccessor;\n  }\n\n  // getCamera(index: number): object | null {\n  //   return null; // TODO: fix thi: object  as null;\n  // }\n\n  getTexture(index: number): GLTFTexture {\n    return this.getObject('textures', index) as GLTFTexture;\n  }\n\n  getSampler(index: number): GLTFSampler {\n    return this.getObject('samplers', index) as GLTFSampler;\n  }\n\n  getImage(index: number): GLTFImage {\n    return this.getObject('images', index) as GLTFImage;\n  }\n\n  getBufferView(index: number | object): GLTFBufferView {\n    return this.getObject('bufferViews', index) as GLTFBufferView;\n  }\n\n  getBuffer(index: number): GLTFBuffer {\n    return this.getObject('buffers', index) as GLTFBuffer;\n  }\n\n  getObject(array: string, index: number | object): object {\n    // check if already resolved\n    if (typeof index === 'object') {\n      return index;\n    }\n    const object = this.json[array] && (this.json[array] as {}[])[index];\n    if (!object) {\n      throw new Error(`glTF file error: Could not find ${array}[${index}]`); // eslint-disable-line\n    }\n    return object;\n  }\n\n  /**\n   * Accepts buffer view index or buffer view object\n   * @returns a `Uint8Array`\n   */\n  getTypedArrayForBufferView(bufferView: number | object): Uint8Array {\n    bufferView = this.getBufferView(bufferView);\n    // @ts-ignore\n    const bufferIndex = bufferView.buffer;\n\n    // Get hold of the arrayBuffer\n    // const buffer = this.getBuffer(bufferIndex);\n    const binChunk = this.gltf.buffers[bufferIndex];\n    assert(binChunk);\n\n    // @ts-ignore\n    const byteOffset = (bufferView.byteOffset || 0) + binChunk.byteOffset;\n    // @ts-ignore\n    return new Uint8Array(binChunk.arrayBuffer, byteOffset, bufferView.byteLength);\n  }\n\n  /** Accepts accessor index or accessor object\n   * @returns a typed array with type that matches the types\n   */\n  getTypedArrayForAccessor(accessor: number | object): any {\n    // @ts-ignore\n    accessor = this.getAccessor(accessor);\n    // @ts-ignore\n    const bufferView = this.getBufferView(accessor.bufferView);\n    const buffer = this.getBuffer(bufferView.buffer);\n    // @ts-ignore\n    const arrayBuffer = buffer.data;\n\n    // Create a new typed array as a view into the combined buffer\n    const {ArrayType, length} = getAccessorArrayTypeAndLength(accessor, bufferView);\n    // @ts-ignore\n    const byteOffset = bufferView.byteOffset + accessor.byteOffset;\n    return new ArrayType(arrayBuffer, byteOffset, length);\n  }\n\n  /** accepts accessor index or accessor object\n   * returns a `Uint8Array`\n   */\n  getTypedArrayForImageData(image: number | object): Uint8Array {\n    // @ts-ignore\n    image = this.getAccessor(image);\n    // @ts-ignore\n    const bufferView = this.getBufferView(image.bufferView);\n    const buffer = this.getBuffer(bufferView.buffer);\n    // @ts-ignore\n    const arrayBuffer = buffer.data;\n\n    const byteOffset = bufferView.byteOffset || 0;\n    return new Uint8Array(arrayBuffer, byteOffset, bufferView.byteLength);\n  }\n\n  // MODIFERS\n\n  /**\n   * Add an extra application-defined key to the top-level data structure\n   */\n  addApplicationData(key: string, data: object): GLTFScenegraph {\n    this.json[key] = data;\n    return this;\n  }\n\n  /**\n   * `extras` - Standard GLTF field for storing application specific data\n   */\n  addExtraData(key: string, data: object): GLTFScenegraph {\n    this.json.extras = this.json.extras || {};\n    (this.json.extras as Record<string, unknown>)[key] = data;\n    return this;\n  }\n\n  addObjectExtension(object: object, extensionName: string, data: object): GLTFScenegraph {\n    // @ts-ignore\n    object.extensions = object.extensions || {};\n    // TODO - clobber or merge?\n    // @ts-ignore\n    object.extensions[extensionName] = data;\n    this.registerUsedExtension(extensionName);\n    return this;\n  }\n\n  setObjectExtension(object: object, extensionName: string, data: object): void {\n    // @ts-ignore\n    const extensions = object.extensions || {};\n    extensions[extensionName] = data;\n    // TODO - add to usedExtensions...\n  }\n\n  removeObjectExtension(object: object, extensionName: string): object {\n    // @ts-ignore\n    const extensions = object.extensions || {};\n    const extension = extensions[extensionName];\n    delete extensions[extensionName];\n    return extension;\n  }\n\n  /**\n   * Add to standard GLTF top level extension object, mark as used\n   */\n  addExtension(extensionName: string, extensionData: object = {}): object {\n    assert(extensionData);\n    this.json.extensions = this.json.extensions || {};\n    (this.json.extensions as Record<string, unknown>)[extensionName] = extensionData;\n    this.registerUsedExtension(extensionName);\n    return extensionData;\n  }\n\n  /**\n   * Standard GLTF top level extension object, mark as used and required\n   */\n  addRequiredExtension(extensionName, extensionData: object = {}): object {\n    assert(extensionData);\n    this.addExtension(extensionName, extensionData);\n    this.registerRequiredExtension(extensionName);\n    return extensionData;\n  }\n\n  /**\n   * Add extensionName to list of used extensions\n   */\n  registerUsedExtension(extensionName: string): void {\n    this.json.extensionsUsed = this.json.extensionsUsed || [];\n    if (!this.json.extensionsUsed.find((ext) => ext === extensionName)) {\n      this.json.extensionsUsed.push(extensionName);\n    }\n  }\n\n  /**\n   * Add extensionName to list of required extensions\n   */\n  registerRequiredExtension(extensionName: string): void {\n    this.registerUsedExtension(extensionName);\n    this.json.extensionsRequired = this.json.extensionsRequired || [];\n    if (!this.json.extensionsRequired.find((ext) => ext === extensionName)) {\n      this.json.extensionsRequired.push(extensionName);\n    }\n  }\n\n  /**\n   * Removes an extension from the top-level list\n   */\n  removeExtension(extensionName: string): void {\n    if (!this.getExtension(extensionName)) {\n      return;\n    }\n    if (this.json.extensionsRequired) {\n      this._removeStringFromArray(this.json.extensionsRequired, extensionName);\n    }\n    if (this.json.extensionsUsed) {\n      this._removeStringFromArray(this.json.extensionsUsed, extensionName);\n    }\n    if (this.json.extensions) {\n      delete this.json.extensions[extensionName];\n    }\n    if (!Array.isArray(this.json.extensionsRemoved)) {\n      this.json.extensionsRemoved = [];\n    }\n    const extensionsRemoved = this.json.extensionsRemoved as string[];\n    if (!extensionsRemoved.includes(extensionName)) {\n      extensionsRemoved.push(extensionName);\n    }\n  }\n\n  /**\n   *  Set default scene which is to be displayed at load time\n   */\n  setDefaultScene(sceneIndex: number): void {\n    this.json.scene = sceneIndex;\n  }\n\n  /**\n   * @todo: add more properties for scene initialization:\n   *   name`, `extensions`, `extras`\n   *   https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#reference-scene\n   */\n  addScene(scene: {nodeIndices: number[]}): number {\n    const {nodeIndices} = scene;\n    this.json.scenes = this.json.scenes || [];\n    this.json.scenes.push({nodes: nodeIndices});\n    return this.json.scenes.length - 1;\n  }\n\n  /**\n   * @todo: add more properties for node initialization:\n   *   `name`, `extensions`, `extras`, `camera`, `children`, `skin`, `rotation`, `scale`, `translation`, `weights`\n   *   https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#node\n   */\n  addNode(node: {meshIndex: number; matrix?: number[]}): number {\n    const {meshIndex, matrix} = node;\n    this.json.nodes = this.json.nodes || [];\n    const nodeData = {mesh: meshIndex};\n    if (matrix) {\n      // @ts-ignore\n      nodeData.matrix = matrix;\n    }\n    this.json.nodes.push(nodeData);\n    return this.json.nodes.length - 1;\n  }\n\n  /** Adds a mesh to the json part */\n  addMesh(mesh: {attributes: object; indices?: object; material?: number; mode?: number}): number {\n    const {attributes, indices, material, mode = 4} = mesh;\n    const accessors = this._addAttributes(attributes);\n\n    const glTFMesh = {\n      primitives: [\n        {\n          attributes: accessors,\n          mode\n        }\n      ]\n    };\n\n    if (indices) {\n      const indicesAccessor = this._addIndices(indices);\n      // @ts-ignore\n      glTFMesh.primitives[0].indices = indicesAccessor;\n    }\n\n    if (Number.isFinite(material)) {\n      // @ts-ignore\n      glTFMesh.primitives[0].material = material;\n    }\n\n    this.json.meshes = this.json.meshes || [];\n    this.json.meshes.push(glTFMesh);\n    return this.json.meshes.length - 1;\n  }\n\n  addPointCloud(attributes: object): number {\n    // @ts-ignore\n    const accessorIndices = this._addAttributes(attributes);\n\n    const glTFMesh = {\n      primitives: [\n        {\n          attributes: accessorIndices,\n          mode: 0 // GL.POINTS\n        }\n      ]\n    };\n\n    this.json.meshes = this.json.meshes || [];\n    this.json.meshes.push(glTFMesh);\n    return this.json.meshes.length - 1;\n  }\n\n  /**\n   * Adds a binary image. Builds glTF \"JSON metadata\" and saves buffer reference\n   * Buffer will be copied into BIN chunk during \"pack\"\n   * Currently encodes as glTF image\n   * @param imageData\n   * @param mimeType\n   */\n  addImage(imageData: any, mimeTypeOpt?: string): number {\n    // If image is referencing a bufferView instead of URI, mimeType must be defined:\n    //   https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#images\n    //   \"a reference to a bufferView; in that case mimeType must be defined.\"\n    const metadata = getBinaryImageMetadata(imageData);\n    const mimeType = mimeTypeOpt || metadata?.mimeType;\n\n    const bufferViewIndex = this.addBufferView(imageData);\n\n    const glTFImage = {\n      bufferView: bufferViewIndex,\n      mimeType\n    };\n\n    this.json.images = this.json.images || [];\n    this.json.images.push(glTFImage);\n    return this.json.images.length - 1;\n  }\n\n  /**\n   * Add one untyped source buffer, create a matching glTF `bufferView`, and return its index\n   * @param buffer\n   */\n  addBufferView(buffer: any): number {\n    const byteLength = buffer.byteLength;\n    assert(Number.isFinite(byteLength));\n\n    // Add this buffer to the list of buffers to be written to the body.\n    this.sourceBuffers = this.sourceBuffers || [];\n    this.sourceBuffers.push(buffer);\n\n    const glTFBufferView = {\n      buffer: 0,\n      // Write offset from the start of the binary body\n      byteOffset: this.byteLength,\n      byteLength\n    };\n\n    // We've now added the contents to the body, so update the total length\n    // Every sub-chunk needs to be 4-byte align ed\n    this.byteLength += padToNBytes(byteLength, 4);\n\n    // Add a bufferView indicating start and length of this binary sub-chunk\n    this.json.bufferViews = this.json.bufferViews || [];\n    this.json.bufferViews.push(glTFBufferView);\n    return this.json.bufferViews.length - 1;\n  }\n\n  /**\n   * Adds an accessor to a bufferView\n   * @param bufferViewIndex\n   * @param accessor\n   */\n  addAccessor(bufferViewIndex: number, accessor: object): number {\n    const glTFAccessor = {\n      bufferView: bufferViewIndex,\n      // @ts-ignore\n      type: getAccessorTypeFromSize(accessor.size),\n      // @ts-ignore\n      componentType: accessor.componentType,\n      // @ts-ignore\n      count: accessor.count,\n      // @ts-ignore\n      max: accessor.max,\n      // @ts-ignore\n      min: accessor.min\n    };\n\n    this.json.accessors = this.json.accessors || [];\n    this.json.accessors.push(glTFAccessor);\n    return this.json.accessors.length - 1;\n  }\n\n  /**\n   * Add a binary buffer. Builds glTF \"JSON metadata\" and saves buffer reference\n   * Buffer will be copied into BIN chunk during \"pack\"\n   * Currently encodes buffers as glTF accessors, but this could be optimized\n   * @param sourceBuffer\n   * @param accessor\n   */\n  addBinaryBuffer(sourceBuffer: any, accessor: object = {size: 3}): number {\n    const bufferViewIndex = this.addBufferView(sourceBuffer);\n    // @ts-ignore\n    let minMax = {min: accessor.min, max: accessor.max};\n    if (!minMax.min || !minMax.max) {\n      // @ts-ignore\n      minMax = this._getAccessorMinMax(sourceBuffer, accessor.size);\n    }\n\n    const accessorDefaults = {\n      // @ts-ignore\n      size: accessor.size,\n      componentType: getComponentTypeFromArray(sourceBuffer),\n      // @ts-ignore\n      count: Math.round(sourceBuffer.length / accessor.size),\n      min: minMax.min,\n      max: minMax.max\n    };\n\n    return this.addAccessor(bufferViewIndex, Object.assign(accessorDefaults, accessor));\n  }\n\n  /**\n   * Adds a texture to the json part\n   * @todo: add more properties for texture initialization\n   * `sampler`, `name`, `extensions`, `extras`\n   * https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#texture\n   */\n  addTexture(texture: {imageIndex: number}): number {\n    const {imageIndex} = texture;\n    const glTFTexture = {\n      source: imageIndex\n    };\n\n    this.json.textures = this.json.textures || [];\n    this.json.textures.push(glTFTexture);\n    return this.json.textures.length - 1;\n  }\n\n  /** Adds a material to the json part */\n  addMaterial(pbrMaterialInfo: Object): number {\n    this.json.materials = this.json.materials || [];\n    this.json.materials.push(pbrMaterialInfo);\n    return this.json.materials.length - 1;\n  }\n\n  /** Pack the binary chunk */\n  createBinaryChunk(): void {\n    // Encoder expects this array undefined or empty\n    this.gltf.buffers = [];\n\n    // Allocate total array\n    const totalByteLength = this.byteLength;\n    const arrayBuffer = new ArrayBuffer(totalByteLength);\n    const targetArray = new Uint8Array(arrayBuffer);\n\n    // Copy each array into\n    let dstByteOffset = 0;\n    for (const sourceBuffer of this.sourceBuffers || []) {\n      dstByteOffset = copyToArray(sourceBuffer, targetArray, dstByteOffset);\n    }\n\n    // Update the glTF BIN CHUNK byte length\n    if (this.json?.buffers?.[0]) {\n      this.json.buffers[0].byteLength = totalByteLength;\n    } else {\n      this.json.buffers = [{byteLength: totalByteLength}];\n    }\n\n    // Save generated arrayBuffer\n    this.gltf.binary = arrayBuffer;\n\n    // Put arrayBuffer to sourceBuffers for possible additional writing data in the chunk\n    this.sourceBuffers = [arrayBuffer];\n  }\n\n  // PRIVATE\n\n  _removeStringFromArray(array, string) {\n    let found = true;\n    while (found) {\n      const index = array.indexOf(string);\n      if (index > -1) {\n        array.splice(index, 1);\n      } else {\n        found = false;\n      }\n    }\n  }\n\n  /**\n   * Add attributes to buffers and create `attributes` object which is part of `mesh`\n   */\n  _addAttributes(attributes = {}) {\n    const result = {};\n    for (const attributeKey in attributes) {\n      const attributeData = attributes[attributeKey];\n      const attrName = this._getGltfAttributeName(attributeKey);\n      const accessor = this.addBinaryBuffer(attributeData.value, attributeData);\n      result[attrName] = accessor;\n    }\n    return result;\n  }\n\n  /**\n   * Add indices to buffers\n   */\n  _addIndices(indices) {\n    return this.addBinaryBuffer(indices, {size: 1});\n  }\n\n  /**\n   * Deduce gltf specific attribue name from input attribute name\n   */\n  _getGltfAttributeName(attributeName) {\n    switch (attributeName.toLowerCase()) {\n      case 'position':\n      case 'positions':\n      case 'vertices':\n        return 'POSITION';\n      case 'normal':\n      case 'normals':\n        return 'NORMAL';\n      case 'color':\n      case 'colors':\n        return 'COLOR_0';\n      case 'texcoord':\n      case 'texcoords':\n        return 'TEXCOORD_0';\n      default:\n        return attributeName;\n    }\n  }\n\n  /**\n   * Calculate `min` and `max` arrays of accessor according to spec:\n   * https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#reference-accessor\n   */\n  _getAccessorMinMax(buffer, size) {\n    const result = {min: null, max: null};\n    if (buffer.length < size) {\n      return result;\n    }\n    // @ts-ignore\n    result.min = [];\n    // @ts-ignore\n    result.max = [];\n    const initValues = buffer.subarray(0, size);\n    for (const value of initValues) {\n      // @ts-ignore\n      result.min.push(value);\n      // @ts-ignore\n      result.max.push(value);\n    }\n\n    for (let index = size; index < buffer.length; index += size) {\n      for (let componentIndex = 0; componentIndex < size; componentIndex++) {\n        // @ts-ignore\n        result.min[0 + componentIndex] = Math.min(\n          // @ts-ignore\n          result.min[0 + componentIndex],\n          buffer[index + componentIndex]\n        );\n        // @ts-ignore\n        result.max[0 + componentIndex] = Math.max(\n          // @ts-ignore\n          result.max[0 + componentIndex],\n          buffer[index + componentIndex]\n        );\n      }\n    }\n    return result;\n  }\n}\n"], "mappings": ";AAgBA,SAAQA,sBAAsB,QAAO,oBAAoB;AACzD,SAAQC,WAAW,EAAEC,WAAW,QAAO,0BAA0B;AACjE,SAAQC,MAAM,QAAO,iBAAiB;AACtC,SACEC,6BAA6B,EAC7BC,uBAAuB,EACvBC,yBAAyB,QACpB,0BAA0B;AAEjC,MAAMC,iBAAuB,GAAG;EAC9BC,KAAK,EAAE;IACLC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE;EACb,CAAC;EACDC,OAAO,EAAE;AACX,CAAC;AAMD,eAAe,MAAMC,cAAc,CAAC;EAMlCC,WAAWA,CAACC,IAAoC,EAAE;IAAAC,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAEhD,IAAI,CAACD,IAAI,GAAGA,IAAI,IAAI;MAClBE,IAAI,EAAE;QAAC,GAAGT;MAAiB,CAAC;MAC5BI,OAAO,EAAE;IACX,CAAC;IACD,IAAI,CAACM,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,UAAU,GAAG,CAAC;IAGnB,IAAI,IAAI,CAACJ,IAAI,CAACH,OAAO,IAAI,IAAI,CAACG,IAAI,CAACH,OAAO,CAAC,CAAC,CAAC,EAAE;MAC7C,IAAI,CAACO,UAAU,GAAG,IAAI,CAACJ,IAAI,CAACH,OAAO,CAAC,CAAC,CAAC,CAACO,UAAU;MACjD,IAAI,CAACD,aAAa,GAAG,CAAC,IAAI,CAACH,IAAI,CAACH,OAAO,CAAC,CAAC,CAAC,CAAC;IAC7C;EACF;EAIA,IAAIK,IAAIA,CAAA,EAAS;IACf,OAAO,IAAI,CAACF,IAAI,CAACE,IAAI;EACvB;EAEAG,kBAAkBA,CAACC,GAAW,EAAW;IAEvC,MAAMC,IAAI,GAAG,IAAI,CAACL,IAAI,CAACI,GAAG,CAAC;IAC3B,OAAOC,IAAI;EACb;EAEAC,YAAYA,CAACF,GAAW,EAA4B;IAElD,MAAMG,MAAM,GAAG,IAAI,CAACP,IAAI,CAACO,MAAM,IAAI,CAAC,CAAC;IACrC,OAAOA,MAAM,CAACH,GAAG,CAAC;EACpB;EAEAI,YAAYA,CAAgBC,aAAqB,EAAY;IAC3D,MAAMC,WAAW,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC,CAACC,IAAI,CAAEC,IAAI,IAAKA,IAAI,KAAKJ,aAAa,CAAC;IACnF,MAAMK,UAAU,GAAG,IAAI,CAACd,IAAI,CAACc,UAAU,IAAI,CAAC,CAAC;IAC7C,OAAOJ,WAAW,GAAGI,UAAU,CAACL,aAAa,CAAC,IAAI,IAAI,GAAG,IAAI;EAC/D;EAEAM,oBAAoBA,CAAgBN,aAAqB,EAAY;IACnE,MAAMO,UAAU,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC,CAACL,IAAI,CAAEC,IAAI,IAAKA,IAAI,KAAKJ,aAAa,CAAC;IACtF,OAAOO,UAAU,GAAG,IAAI,CAACR,YAAY,CAACC,aAAa,CAAC,GAAG,IAAI;EAC7D;EAEAQ,qBAAqBA,CAAA,EAAa;IAChC,OAAO,IAAI,CAACjB,IAAI,CAACkB,kBAAkB,IAAI,EAAE;EAC3C;EAEAP,iBAAiBA,CAAA,EAAa;IAC5B,OAAO,IAAI,CAACX,IAAI,CAACmB,cAAc,IAAI,EAAE;EACvC;EAEAC,oBAAoBA,CAAA,EAAa;IAC/B,OAAQ,IAAI,CAACpB,IAAI,CAACqB,iBAAiB,IAAI,EAAE;EAC3C;EAEAC,kBAAkBA,CAAgBC,MAA4B,EAAEd,aAAqB,EAAY;IAC/F,MAAMK,UAAU,GAAGS,MAAM,CAACT,UAAU,IAAI,CAAC,CAAC;IAC1C,OAAOA,UAAU,CAACL,aAAa,CAAC;EAClC;EAEAe,QAAQA,CAACC,KAAa,EAAa;IACjC,OAAO,IAAI,CAACC,SAAS,CAAC,QAAQ,EAAED,KAAK,CAAC;EACxC;EAEAE,OAAOA,CAACF,KAAa,EAAY;IAC/B,OAAO,IAAI,CAACC,SAAS,CAAC,OAAO,EAAED,KAAK,CAAC;EACvC;EAEAG,OAAOA,CAACH,KAAa,EAAY;IAC/B,OAAO,IAAI,CAACC,SAAS,CAAC,OAAO,EAAED,KAAK,CAAC;EACvC;EAEAI,OAAOA,CAACJ,KAAa,EAAY;IAC/B,OAAO,IAAI,CAACC,SAAS,CAAC,QAAQ,EAAED,KAAK,CAAC;EACxC;EAEAK,WAAWA,CAACL,KAAa,EAAgB;IACvC,OAAO,IAAI,CAACC,SAAS,CAAC,WAAW,EAAED,KAAK,CAAC;EAC3C;EAEAM,WAAWA,CAACN,KAAa,EAAgB;IACvC,OAAO,IAAI,CAACC,SAAS,CAAC,WAAW,EAAED,KAAK,CAAC;EAC3C;EAMAO,UAAUA,CAACP,KAAa,EAAe;IACrC,OAAO,IAAI,CAACC,SAAS,CAAC,UAAU,EAAED,KAAK,CAAC;EAC1C;EAEAQ,UAAUA,CAACR,KAAa,EAAe;IACrC,OAAO,IAAI,CAACC,SAAS,CAAC,UAAU,EAAED,KAAK,CAAC;EAC1C;EAEAS,QAAQA,CAACT,KAAa,EAAa;IACjC,OAAO,IAAI,CAACC,SAAS,CAAC,QAAQ,EAAED,KAAK,CAAC;EACxC;EAEAU,aAAaA,CAACV,KAAsB,EAAkB;IACpD,OAAO,IAAI,CAACC,SAAS,CAAC,aAAa,EAAED,KAAK,CAAC;EAC7C;EAEAW,SAASA,CAACX,KAAa,EAAc;IACnC,OAAO,IAAI,CAACC,SAAS,CAAC,SAAS,EAAED,KAAK,CAAC;EACzC;EAEAC,SAASA,CAACW,KAAa,EAAEZ,KAAsB,EAAU;IAEvD,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAOA,KAAK;IACd;IACA,MAAMF,MAAM,GAAG,IAAI,CAACvB,IAAI,CAACqC,KAAK,CAAC,IAAK,IAAI,CAACrC,IAAI,CAACqC,KAAK,CAAC,CAAUZ,KAAK,CAAC;IACpE,IAAI,CAACF,MAAM,EAAE;MACX,MAAM,IAAIe,KAAK,oCAAAC,MAAA,CAAoCF,KAAK,OAAAE,MAAA,CAAId,KAAK,MAAG,CAAC;IACvE;IACA,OAAOF,MAAM;EACf;EAMAiB,0BAA0BA,CAACC,UAA2B,EAAc;IAClEA,UAAU,GAAG,IAAI,CAACN,aAAa,CAACM,UAAU,CAAC;IAE3C,MAAMC,WAAW,GAAGD,UAAU,CAACE,MAAM;IAIrC,MAAMC,QAAQ,GAAG,IAAI,CAAC9C,IAAI,CAACH,OAAO,CAAC+C,WAAW,CAAC;IAC/CvD,MAAM,CAACyD,QAAQ,CAAC;IAGhB,MAAMC,UAAU,GAAG,CAACJ,UAAU,CAACI,UAAU,IAAI,CAAC,IAAID,QAAQ,CAACC,UAAU;IAErE,OAAO,IAAIC,UAAU,CAACF,QAAQ,CAACG,WAAW,EAAEF,UAAU,EAAEJ,UAAU,CAACvC,UAAU,CAAC;EAChF;EAKA8C,wBAAwBA,CAACC,QAAyB,EAAO;IAEvDA,QAAQ,GAAG,IAAI,CAAClB,WAAW,CAACkB,QAAQ,CAAC;IAErC,MAAMR,UAAU,GAAG,IAAI,CAACN,aAAa,CAACc,QAAQ,CAACR,UAAU,CAAC;IAC1D,MAAME,MAAM,GAAG,IAAI,CAACP,SAAS,CAACK,UAAU,CAACE,MAAM,CAAC;IAEhD,MAAMI,WAAW,GAAGJ,MAAM,CAACtC,IAAI;IAG/B,MAAM;MAAC6C,SAAS;MAAEC;IAAM,CAAC,GAAG/D,6BAA6B,CAAC6D,QAAQ,EAAER,UAAU,CAAC;IAE/E,MAAMI,UAAU,GAAGJ,UAAU,CAACI,UAAU,GAAGI,QAAQ,CAACJ,UAAU;IAC9D,OAAO,IAAIK,SAAS,CAACH,WAAW,EAAEF,UAAU,EAAEM,MAAM,CAAC;EACvD;EAKAC,yBAAyBA,CAACC,KAAsB,EAAc;IAE5DA,KAAK,GAAG,IAAI,CAACtB,WAAW,CAACsB,KAAK,CAAC;IAE/B,MAAMZ,UAAU,GAAG,IAAI,CAACN,aAAa,CAACkB,KAAK,CAACZ,UAAU,CAAC;IACvD,MAAME,MAAM,GAAG,IAAI,CAACP,SAAS,CAACK,UAAU,CAACE,MAAM,CAAC;IAEhD,MAAMI,WAAW,GAAGJ,MAAM,CAACtC,IAAI;IAE/B,MAAMwC,UAAU,GAAGJ,UAAU,CAACI,UAAU,IAAI,CAAC;IAC7C,OAAO,IAAIC,UAAU,CAACC,WAAW,EAAEF,UAAU,EAAEJ,UAAU,CAACvC,UAAU,CAAC;EACvE;EAOAoD,kBAAkBA,CAAClD,GAAW,EAAEC,IAAY,EAAkB;IAC5D,IAAI,CAACL,IAAI,CAACI,GAAG,CAAC,GAAGC,IAAI;IACrB,OAAO,IAAI;EACb;EAKAkD,YAAYA,CAACnD,GAAW,EAAEC,IAAY,EAAkB;IACtD,IAAI,CAACL,IAAI,CAACO,MAAM,GAAG,IAAI,CAACP,IAAI,CAACO,MAAM,IAAI,CAAC,CAAC;IACxC,IAAI,CAACP,IAAI,CAACO,MAAM,CAA6BH,GAAG,CAAC,GAAGC,IAAI;IACzD,OAAO,IAAI;EACb;EAEAmD,kBAAkBA,CAACjC,MAAc,EAAEd,aAAqB,EAAEJ,IAAY,EAAkB;IAEtFkB,MAAM,CAACT,UAAU,GAAGS,MAAM,CAACT,UAAU,IAAI,CAAC,CAAC;IAG3CS,MAAM,CAACT,UAAU,CAACL,aAAa,CAAC,GAAGJ,IAAI;IACvC,IAAI,CAACoD,qBAAqB,CAAChD,aAAa,CAAC;IACzC,OAAO,IAAI;EACb;EAEAiD,kBAAkBA,CAACnC,MAAc,EAAEd,aAAqB,EAAEJ,IAAY,EAAQ;IAE5E,MAAMS,UAAU,GAAGS,MAAM,CAACT,UAAU,IAAI,CAAC,CAAC;IAC1CA,UAAU,CAACL,aAAa,CAAC,GAAGJ,IAAI;EAElC;EAEAsD,qBAAqBA,CAACpC,MAAc,EAAEd,aAAqB,EAAU;IAEnE,MAAMK,UAAU,GAAGS,MAAM,CAACT,UAAU,IAAI,CAAC,CAAC;IAC1C,MAAM8C,SAAS,GAAG9C,UAAU,CAACL,aAAa,CAAC;IAC3C,OAAOK,UAAU,CAACL,aAAa,CAAC;IAChC,OAAOmD,SAAS;EAClB;EAKAC,YAAYA,CAACpD,aAAqB,EAAsC;IAAA,IAApCqD,aAAqB,GAAAC,SAAA,CAAAZ,MAAA,QAAAY,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;IAC5D5E,MAAM,CAAC2E,aAAa,CAAC;IACrB,IAAI,CAAC9D,IAAI,CAACc,UAAU,GAAG,IAAI,CAACd,IAAI,CAACc,UAAU,IAAI,CAAC,CAAC;IAChD,IAAI,CAACd,IAAI,CAACc,UAAU,CAA6BL,aAAa,CAAC,GAAGqD,aAAa;IAChF,IAAI,CAACL,qBAAqB,CAAChD,aAAa,CAAC;IACzC,OAAOqD,aAAa;EACtB;EAKAG,oBAAoBA,CAACxD,aAAa,EAAsC;IAAA,IAApCqD,aAAqB,GAAAC,SAAA,CAAAZ,MAAA,QAAAY,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;IAC5D5E,MAAM,CAAC2E,aAAa,CAAC;IACrB,IAAI,CAACD,YAAY,CAACpD,aAAa,EAAEqD,aAAa,CAAC;IAC/C,IAAI,CAACI,yBAAyB,CAACzD,aAAa,CAAC;IAC7C,OAAOqD,aAAa;EACtB;EAKAL,qBAAqBA,CAAChD,aAAqB,EAAQ;IACjD,IAAI,CAACT,IAAI,CAACmB,cAAc,GAAG,IAAI,CAACnB,IAAI,CAACmB,cAAc,IAAI,EAAE;IACzD,IAAI,CAAC,IAAI,CAACnB,IAAI,CAACmB,cAAc,CAACP,IAAI,CAAEuD,GAAG,IAAKA,GAAG,KAAK1D,aAAa,CAAC,EAAE;MAClE,IAAI,CAACT,IAAI,CAACmB,cAAc,CAACiD,IAAI,CAAC3D,aAAa,CAAC;IAC9C;EACF;EAKAyD,yBAAyBA,CAACzD,aAAqB,EAAQ;IACrD,IAAI,CAACgD,qBAAqB,CAAChD,aAAa,CAAC;IACzC,IAAI,CAACT,IAAI,CAACkB,kBAAkB,GAAG,IAAI,CAAClB,IAAI,CAACkB,kBAAkB,IAAI,EAAE;IACjE,IAAI,CAAC,IAAI,CAAClB,IAAI,CAACkB,kBAAkB,CAACN,IAAI,CAAEuD,GAAG,IAAKA,GAAG,KAAK1D,aAAa,CAAC,EAAE;MACtE,IAAI,CAACT,IAAI,CAACkB,kBAAkB,CAACkD,IAAI,CAAC3D,aAAa,CAAC;IAClD;EACF;EAKA4D,eAAeA,CAAC5D,aAAqB,EAAQ;IAC3C,IAAI,CAAC,IAAI,CAACD,YAAY,CAACC,aAAa,CAAC,EAAE;MACrC;IACF;IACA,IAAI,IAAI,CAACT,IAAI,CAACkB,kBAAkB,EAAE;MAChC,IAAI,CAACoD,sBAAsB,CAAC,IAAI,CAACtE,IAAI,CAACkB,kBAAkB,EAAET,aAAa,CAAC;IAC1E;IACA,IAAI,IAAI,CAACT,IAAI,CAACmB,cAAc,EAAE;MAC5B,IAAI,CAACmD,sBAAsB,CAAC,IAAI,CAACtE,IAAI,CAACmB,cAAc,EAAEV,aAAa,CAAC;IACtE;IACA,IAAI,IAAI,CAACT,IAAI,CAACc,UAAU,EAAE;MACxB,OAAO,IAAI,CAACd,IAAI,CAACc,UAAU,CAACL,aAAa,CAAC;IAC5C;IACA,IAAI,CAAC8D,KAAK,CAACC,OAAO,CAAC,IAAI,CAACxE,IAAI,CAACqB,iBAAiB,CAAC,EAAE;MAC/C,IAAI,CAACrB,IAAI,CAACqB,iBAAiB,GAAG,EAAE;IAClC;IACA,MAAMA,iBAAiB,GAAG,IAAI,CAACrB,IAAI,CAACqB,iBAA6B;IACjE,IAAI,CAACA,iBAAiB,CAACoD,QAAQ,CAAChE,aAAa,CAAC,EAAE;MAC9CY,iBAAiB,CAAC+C,IAAI,CAAC3D,aAAa,CAAC;IACvC;EACF;EAKAiE,eAAeA,CAACC,UAAkB,EAAQ;IACxC,IAAI,CAAC3E,IAAI,CAAC4E,KAAK,GAAGD,UAAU;EAC9B;EAOAE,QAAQA,CAACD,KAA8B,EAAU;IAC/C,MAAM;MAACE;IAAW,CAAC,GAAGF,KAAK;IAC3B,IAAI,CAAC5E,IAAI,CAAC+E,MAAM,GAAG,IAAI,CAAC/E,IAAI,CAAC+E,MAAM,IAAI,EAAE;IACzC,IAAI,CAAC/E,IAAI,CAAC+E,MAAM,CAACX,IAAI,CAAC;MAACY,KAAK,EAAEF;IAAW,CAAC,CAAC;IAC3C,OAAO,IAAI,CAAC9E,IAAI,CAAC+E,MAAM,CAAC5B,MAAM,GAAG,CAAC;EACpC;EAOA8B,OAAOA,CAACC,IAA4C,EAAU;IAC5D,MAAM;MAACC,SAAS;MAAEC;IAAM,CAAC,GAAGF,IAAI;IAChC,IAAI,CAAClF,IAAI,CAACgF,KAAK,GAAG,IAAI,CAAChF,IAAI,CAACgF,KAAK,IAAI,EAAE;IACvC,MAAMK,QAAQ,GAAG;MAACC,IAAI,EAAEH;IAAS,CAAC;IAClC,IAAIC,MAAM,EAAE;MAEVC,QAAQ,CAACD,MAAM,GAAGA,MAAM;IAC1B;IACA,IAAI,CAACpF,IAAI,CAACgF,KAAK,CAACZ,IAAI,CAACiB,QAAQ,CAAC;IAC9B,OAAO,IAAI,CAACrF,IAAI,CAACgF,KAAK,CAAC7B,MAAM,GAAG,CAAC;EACnC;EAGAoC,OAAOA,CAACD,IAA8E,EAAU;IAC9F,MAAM;MAACE,UAAU;MAAEC,OAAO;MAAEC,QAAQ;MAAEC,IAAI,GAAG;IAAC,CAAC,GAAGL,IAAI;IACtD,MAAMM,SAAS,GAAG,IAAI,CAACC,cAAc,CAACL,UAAU,CAAC;IAEjD,MAAMM,QAAQ,GAAG;MACfC,UAAU,EAAE,CACV;QACEP,UAAU,EAAEI,SAAS;QACrBD;MACF,CAAC;IAEL,CAAC;IAED,IAAIF,OAAO,EAAE;MACX,MAAMO,eAAe,GAAG,IAAI,CAACC,WAAW,CAACR,OAAO,CAAC;MAEjDK,QAAQ,CAACC,UAAU,CAAC,CAAC,CAAC,CAACN,OAAO,GAAGO,eAAe;IAClD;IAEA,IAAIE,MAAM,CAACC,QAAQ,CAACT,QAAQ,CAAC,EAAE;MAE7BI,QAAQ,CAACC,UAAU,CAAC,CAAC,CAAC,CAACL,QAAQ,GAAGA,QAAQ;IAC5C;IAEA,IAAI,CAAC1F,IAAI,CAACoG,MAAM,GAAG,IAAI,CAACpG,IAAI,CAACoG,MAAM,IAAI,EAAE;IACzC,IAAI,CAACpG,IAAI,CAACoG,MAAM,CAAChC,IAAI,CAAC0B,QAAQ,CAAC;IAC/B,OAAO,IAAI,CAAC9F,IAAI,CAACoG,MAAM,CAACjD,MAAM,GAAG,CAAC;EACpC;EAEAkD,aAAaA,CAACb,UAAkB,EAAU;IAExC,MAAMc,eAAe,GAAG,IAAI,CAACT,cAAc,CAACL,UAAU,CAAC;IAEvD,MAAMM,QAAQ,GAAG;MACfC,UAAU,EAAE,CACV;QACEP,UAAU,EAAEc,eAAe;QAC3BX,IAAI,EAAE;MACR,CAAC;IAEL,CAAC;IAED,IAAI,CAAC3F,IAAI,CAACoG,MAAM,GAAG,IAAI,CAACpG,IAAI,CAACoG,MAAM,IAAI,EAAE;IACzC,IAAI,CAACpG,IAAI,CAACoG,MAAM,CAAChC,IAAI,CAAC0B,QAAQ,CAAC;IAC/B,OAAO,IAAI,CAAC9F,IAAI,CAACoG,MAAM,CAACjD,MAAM,GAAG,CAAC;EACpC;EASAoD,QAAQA,CAACC,SAAc,EAAEC,WAAoB,EAAU;IAIrD,MAAMC,QAAQ,GAAG1H,sBAAsB,CAACwH,SAAS,CAAC;IAClD,MAAMG,QAAQ,GAAGF,WAAW,KAAIC,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEC,QAAQ;IAElD,MAAMC,eAAe,GAAG,IAAI,CAACC,aAAa,CAACL,SAAS,CAAC;IAErD,MAAMM,SAAS,GAAG;MAChBrE,UAAU,EAAEmE,eAAe;MAC3BD;IACF,CAAC;IAED,IAAI,CAAC3G,IAAI,CAAC+G,MAAM,GAAG,IAAI,CAAC/G,IAAI,CAAC+G,MAAM,IAAI,EAAE;IACzC,IAAI,CAAC/G,IAAI,CAAC+G,MAAM,CAAC3C,IAAI,CAAC0C,SAAS,CAAC;IAChC,OAAO,IAAI,CAAC9G,IAAI,CAAC+G,MAAM,CAAC5D,MAAM,GAAG,CAAC;EACpC;EAMA0D,aAAaA,CAAClE,MAAW,EAAU;IACjC,MAAMzC,UAAU,GAAGyC,MAAM,CAACzC,UAAU;IACpCf,MAAM,CAAC+G,MAAM,CAACC,QAAQ,CAACjG,UAAU,CAAC,CAAC;IAGnC,IAAI,CAACD,aAAa,GAAG,IAAI,CAACA,aAAa,IAAI,EAAE;IAC7C,IAAI,CAACA,aAAa,CAACmE,IAAI,CAACzB,MAAM,CAAC;IAE/B,MAAMqE,cAAc,GAAG;MACrBrE,MAAM,EAAE,CAAC;MAETE,UAAU,EAAE,IAAI,CAAC3C,UAAU;MAC3BA;IACF,CAAC;IAID,IAAI,CAACA,UAAU,IAAIjB,WAAW,CAACiB,UAAU,EAAE,CAAC,CAAC;IAG7C,IAAI,CAACF,IAAI,CAACiH,WAAW,GAAG,IAAI,CAACjH,IAAI,CAACiH,WAAW,IAAI,EAAE;IACnD,IAAI,CAACjH,IAAI,CAACiH,WAAW,CAAC7C,IAAI,CAAC4C,cAAc,CAAC;IAC1C,OAAO,IAAI,CAAChH,IAAI,CAACiH,WAAW,CAAC9D,MAAM,GAAG,CAAC;EACzC;EAOA+D,WAAWA,CAACN,eAAuB,EAAE3D,QAAgB,EAAU;IAC7D,MAAMkE,YAAY,GAAG;MACnB1E,UAAU,EAAEmE,eAAe;MAE3BQ,IAAI,EAAE/H,uBAAuB,CAAC4D,QAAQ,CAACoE,IAAI,CAAC;MAE5CC,aAAa,EAAErE,QAAQ,CAACqE,aAAa;MAErCC,KAAK,EAAEtE,QAAQ,CAACsE,KAAK;MAErBC,GAAG,EAAEvE,QAAQ,CAACuE,GAAG;MAEjBC,GAAG,EAAExE,QAAQ,CAACwE;IAChB,CAAC;IAED,IAAI,CAACzH,IAAI,CAAC4F,SAAS,GAAG,IAAI,CAAC5F,IAAI,CAAC4F,SAAS,IAAI,EAAE;IAC/C,IAAI,CAAC5F,IAAI,CAAC4F,SAAS,CAACxB,IAAI,CAAC+C,YAAY,CAAC;IACtC,OAAO,IAAI,CAACnH,IAAI,CAAC4F,SAAS,CAACzC,MAAM,GAAG,CAAC;EACvC;EASAuE,eAAeA,CAACC,YAAiB,EAAwC;IAAA,IAAtC1E,QAAgB,GAAAc,SAAA,CAAAZ,MAAA,QAAAY,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG;MAACsD,IAAI,EAAE;IAAC,CAAC;IAC7D,MAAMT,eAAe,GAAG,IAAI,CAACC,aAAa,CAACc,YAAY,CAAC;IAExD,IAAIC,MAAM,GAAG;MAACH,GAAG,EAAExE,QAAQ,CAACwE,GAAG;MAAED,GAAG,EAAEvE,QAAQ,CAACuE;IAAG,CAAC;IACnD,IAAI,CAACI,MAAM,CAACH,GAAG,IAAI,CAACG,MAAM,CAACJ,GAAG,EAAE;MAE9BI,MAAM,GAAG,IAAI,CAACC,kBAAkB,CAACF,YAAY,EAAE1E,QAAQ,CAACoE,IAAI,CAAC;IAC/D;IAEA,MAAMS,gBAAgB,GAAG;MAEvBT,IAAI,EAAEpE,QAAQ,CAACoE,IAAI;MACnBC,aAAa,EAAEhI,yBAAyB,CAACqI,YAAY,CAAC;MAEtDJ,KAAK,EAAEQ,IAAI,CAACC,KAAK,CAACL,YAAY,CAACxE,MAAM,GAAGF,QAAQ,CAACoE,IAAI,CAAC;MACtDI,GAAG,EAAEG,MAAM,CAACH,GAAG;MACfD,GAAG,EAAEI,MAAM,CAACJ;IACd,CAAC;IAED,OAAO,IAAI,CAACN,WAAW,CAACN,eAAe,EAAEqB,MAAM,CAACC,MAAM,CAACJ,gBAAgB,EAAE7E,QAAQ,CAAC,CAAC;EACrF;EAQAkF,UAAUA,CAACC,OAA6B,EAAU;IAChD,MAAM;MAACC;IAAU,CAAC,GAAGD,OAAO;IAC5B,MAAME,WAAW,GAAG;MAClBC,MAAM,EAAEF;IACV,CAAC;IAED,IAAI,CAACrI,IAAI,CAACwI,QAAQ,GAAG,IAAI,CAACxI,IAAI,CAACwI,QAAQ,IAAI,EAAE;IAC7C,IAAI,CAACxI,IAAI,CAACwI,QAAQ,CAACpE,IAAI,CAACkE,WAAW,CAAC;IACpC,OAAO,IAAI,CAACtI,IAAI,CAACwI,QAAQ,CAACrF,MAAM,GAAG,CAAC;EACtC;EAGAsF,WAAWA,CAACC,eAAuB,EAAU;IAC3C,IAAI,CAAC1I,IAAI,CAAC2I,SAAS,GAAG,IAAI,CAAC3I,IAAI,CAAC2I,SAAS,IAAI,EAAE;IAC/C,IAAI,CAAC3I,IAAI,CAAC2I,SAAS,CAACvE,IAAI,CAACsE,eAAe,CAAC;IACzC,OAAO,IAAI,CAAC1I,IAAI,CAAC2I,SAAS,CAACxF,MAAM,GAAG,CAAC;EACvC;EAGAyF,iBAAiBA,CAAA,EAAS;IAAA,IAAAC,UAAA,EAAAC,kBAAA;IAExB,IAAI,CAAChJ,IAAI,CAACH,OAAO,GAAG,EAAE;IAGtB,MAAMoJ,eAAe,GAAG,IAAI,CAAC7I,UAAU;IACvC,MAAM6C,WAAW,GAAG,IAAIiG,WAAW,CAACD,eAAe,CAAC;IACpD,MAAME,WAAW,GAAG,IAAInG,UAAU,CAACC,WAAW,CAAC;IAG/C,IAAImG,aAAa,GAAG,CAAC;IACrB,KAAK,MAAMvB,YAAY,IAAI,IAAI,CAAC1H,aAAa,IAAI,EAAE,EAAE;MACnDiJ,aAAa,GAAGhK,WAAW,CAACyI,YAAY,EAAEsB,WAAW,EAAEC,aAAa,CAAC;IACvE;IAGA,KAAAL,UAAA,GAAI,IAAI,CAAC7I,IAAI,cAAA6I,UAAA,gBAAAC,kBAAA,GAATD,UAAA,CAAWlJ,OAAO,cAAAmJ,kBAAA,eAAlBA,kBAAA,CAAqB,CAAC,CAAC,EAAE;MAC3B,IAAI,CAAC9I,IAAI,CAACL,OAAO,CAAC,CAAC,CAAC,CAACO,UAAU,GAAG6I,eAAe;IACnD,CAAC,MAAM;MACL,IAAI,CAAC/I,IAAI,CAACL,OAAO,GAAG,CAAC;QAACO,UAAU,EAAE6I;MAAe,CAAC,CAAC;IACrD;IAGA,IAAI,CAACjJ,IAAI,CAACqJ,MAAM,GAAGpG,WAAW;IAG9B,IAAI,CAAC9C,aAAa,GAAG,CAAC8C,WAAW,CAAC;EACpC;EAIAuB,sBAAsBA,CAACjC,KAAK,EAAE+G,MAAM,EAAE;IACpC,IAAIC,KAAK,GAAG,IAAI;IAChB,OAAOA,KAAK,EAAE;MACZ,MAAM5H,KAAK,GAAGY,KAAK,CAACiH,OAAO,CAACF,MAAM,CAAC;MACnC,IAAI3H,KAAK,GAAG,CAAC,CAAC,EAAE;QACdY,KAAK,CAACkH,MAAM,CAAC9H,KAAK,EAAE,CAAC,CAAC;MACxB,CAAC,MAAM;QACL4H,KAAK,GAAG,KAAK;MACf;IACF;EACF;EAKAxD,cAAcA,CAAA,EAAkB;IAAA,IAAjBL,UAAU,GAAAzB,SAAA,CAAAZ,MAAA,QAAAY,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;IAC5B,MAAMyF,MAAM,GAAG,CAAC,CAAC;IACjB,KAAK,MAAMC,YAAY,IAAIjE,UAAU,EAAE;MACrC,MAAMkE,aAAa,GAAGlE,UAAU,CAACiE,YAAY,CAAC;MAC9C,MAAME,QAAQ,GAAG,IAAI,CAACC,qBAAqB,CAACH,YAAY,CAAC;MACzD,MAAMxG,QAAQ,GAAG,IAAI,CAACyE,eAAe,CAACgC,aAAa,CAACG,KAAK,EAAEH,aAAa,CAAC;MACzEF,MAAM,CAACG,QAAQ,CAAC,GAAG1G,QAAQ;IAC7B;IACA,OAAOuG,MAAM;EACf;EAKAvD,WAAWA,CAACR,OAAO,EAAE;IACnB,OAAO,IAAI,CAACiC,eAAe,CAACjC,OAAO,EAAE;MAAC4B,IAAI,EAAE;IAAC,CAAC,CAAC;EACjD;EAKAuC,qBAAqBA,CAACE,aAAa,EAAE;IACnC,QAAQA,aAAa,CAACC,WAAW,CAAC,CAAC;MACjC,KAAK,UAAU;MACf,KAAK,WAAW;MAChB,KAAK,UAAU;QACb,OAAO,UAAU;MACnB,KAAK,QAAQ;MACb,KAAK,SAAS;QACZ,OAAO,QAAQ;MACjB,KAAK,OAAO;MACZ,KAAK,QAAQ;QACX,OAAO,SAAS;MAClB,KAAK,UAAU;MACf,KAAK,WAAW;QACd,OAAO,YAAY;MACrB;QACE,OAAOD,aAAa;IACxB;EACF;EAMAjC,kBAAkBA,CAAClF,MAAM,EAAE0E,IAAI,EAAE;IAC/B,MAAMmC,MAAM,GAAG;MAAC/B,GAAG,EAAE,IAAI;MAAED,GAAG,EAAE;IAAI,CAAC;IACrC,IAAI7E,MAAM,CAACQ,MAAM,GAAGkE,IAAI,EAAE;MACxB,OAAOmC,MAAM;IACf;IAEAA,MAAM,CAAC/B,GAAG,GAAG,EAAE;IAEf+B,MAAM,CAAChC,GAAG,GAAG,EAAE;IACf,MAAMwC,UAAU,GAAGrH,MAAM,CAACsH,QAAQ,CAAC,CAAC,EAAE5C,IAAI,CAAC;IAC3C,KAAK,MAAMwC,KAAK,IAAIG,UAAU,EAAE;MAE9BR,MAAM,CAAC/B,GAAG,CAACrD,IAAI,CAACyF,KAAK,CAAC;MAEtBL,MAAM,CAAChC,GAAG,CAACpD,IAAI,CAACyF,KAAK,CAAC;IACxB;IAEA,KAAK,IAAIpI,KAAK,GAAG4F,IAAI,EAAE5F,KAAK,GAAGkB,MAAM,CAACQ,MAAM,EAAE1B,KAAK,IAAI4F,IAAI,EAAE;MAC3D,KAAK,IAAI6C,cAAc,GAAG,CAAC,EAAEA,cAAc,GAAG7C,IAAI,EAAE6C,cAAc,EAAE,EAAE;QAEpEV,MAAM,CAAC/B,GAAG,CAAC,CAAC,GAAGyC,cAAc,CAAC,GAAGnC,IAAI,CAACN,GAAG,CAEvC+B,MAAM,CAAC/B,GAAG,CAAC,CAAC,GAAGyC,cAAc,CAAC,EAC9BvH,MAAM,CAAClB,KAAK,GAAGyI,cAAc,CAC/B,CAAC;QAEDV,MAAM,CAAChC,GAAG,CAAC,CAAC,GAAG0C,cAAc,CAAC,GAAGnC,IAAI,CAACP,GAAG,CAEvCgC,MAAM,CAAChC,GAAG,CAAC,CAAC,GAAG0C,cAAc,CAAC,EAC9BvH,MAAM,CAAClB,KAAK,GAAGyI,cAAc,CAC/B,CAAC;MACH;IACF;IACA,OAAOV,MAAM;EACf;AACF"}