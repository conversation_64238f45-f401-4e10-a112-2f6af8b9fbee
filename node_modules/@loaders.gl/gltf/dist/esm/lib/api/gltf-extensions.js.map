{"version": 3, "file": "gltf-extensions.js", "names": ["EXT_meshopt_compression", "EXT_texture_webp", "KHR_texture_basisu", "KHR_draco_mesh_compression", "KHR_texture_transform", "KHR_lights_punctual", "KHR_materials_unlit", "KHR_techniques_webgl", "EXT_feature_metadata", "EXTENSIONS", "preprocessExtensions", "gltf", "options", "arguments", "length", "undefined", "context", "extensions", "filter", "extension", "useExtension", "name", "_extension$preprocess", "preprocess", "call", "decodeExtensions", "_extension$decode", "decode", "extensionName", "_options$gltf", "excludes", "excludeExtensions", "exclude"], "sources": ["../../../../src/lib/api/gltf-extensions.ts"], "sourcesContent": ["/* eslint-disable camelcase */\nimport {GLTF} from '../types/gltf-types';\nimport type {GLTFLoaderOptions} from '../../gltf-loader';\n\n// GLTF 1.0 extensions (decode only)\n// import * as KHR_binary_gltf from './KHR_draco_mesh_compression';\n\n// GLTF 2.0 Khronos extensions (decode/encode)\nimport * as EXT_meshopt_compression from '../extensions/EXT_meshopt_compression';\nimport * as EXT_texture_webp from '../extensions/EXT_texture_webp';\nimport * as KHR_texture_basisu from '../extensions/KHR_texture_basisu';\nimport * as KHR_draco_mesh_compression from '../extensions/KHR_draco_mesh_compression';\nimport * as KHR_texture_transform from '../extensions/KHR_texture_transform';\n\n// Deprecated. These should be handled by rendering library (e.g. luma.gl), not the loader.\nimport * as KHR_lights_punctual from '../extensions/deprecated/KHR_lights_punctual';\nimport * as KHR_materials_unlit from '../extensions/deprecated/KHR_materials_unlit';\nimport * as KHR_techniques_webgl from '../extensions/deprecated/KHR_techniques_webgl';\nimport * as EXT_feature_metadata from '../extensions/deprecated/EXT_feature_metadata';\n\n// Vendor extensions\n\ntype GLTFExtensionPlugin = {\n  name: string;\n  preprocess?: (gltfData: {json: GLTF}, options: GLTFLoaderOptions, context) => void;\n  decode?: (\n    gltfData: {\n      json: GLTF;\n      buffers: {arrayBuffer: ArrayBuffer; byteOffset: number; byteLength: number}[];\n    },\n    options: GLTFLoaderOptions,\n    context\n  ) => Promise<void>;\n  encode?: (gltfData: {json: GLTF}, options: GLTFLoaderOptions) => void;\n};\n\n/**\n * List of extensions processed by the GLTFLoader\n * Note that may extensions can only be handled on the rendering stage and are left out here\n * These are just extensions that can be handled fully or partially during loading.\n */\nexport const EXTENSIONS: GLTFExtensionPlugin[] = [\n  // 1.0\n  // KHR_binary_gltf is handled separately - must be processed before other parsing starts\n  // KHR_binary_gltf,\n\n  // 2.0\n  EXT_meshopt_compression,\n  EXT_texture_webp,\n  // Basisu should come after webp, we want basisu to be preferred if both are provided\n  KHR_texture_basisu,\n  KHR_draco_mesh_compression,\n  KHR_lights_punctual,\n  KHR_materials_unlit,\n  KHR_techniques_webgl,\n  KHR_texture_transform,\n  EXT_feature_metadata\n];\n\n/** Call before any resource loading starts */\nexport function preprocessExtensions(gltf, options: GLTFLoaderOptions = {}, context?) {\n  const extensions = EXTENSIONS.filter((extension) => useExtension(extension.name, options));\n  for (const extension of extensions) {\n    extension.preprocess?.(gltf, options, context);\n  }\n}\n\n/** Call after resource loading */\nexport async function decodeExtensions(gltf, options: GLTFLoaderOptions = {}, context?) {\n  const extensions = EXTENSIONS.filter((extension) => useExtension(extension.name, options));\n  for (const extension of extensions) {\n    // Note: We decode async extensions sequentially, this might not be necessary\n    // Currently we only have Draco, but when we add Basis we may revisit\n    await extension.decode?.(gltf, options, context);\n  }\n}\n\nfunction useExtension(extensionName: string, options: GLTFLoaderOptions) {\n  const excludes = options?.gltf?.excludeExtensions || {};\n  const exclude = extensionName in excludes && !excludes[extensionName];\n  return !exclude;\n}\n"], "mappings": "AAQA,OAAO,KAAKA,uBAAuB,MAAM,uCAAuC;AAChF,OAAO,KAAKC,gBAAgB,MAAM,gCAAgC;AAClE,OAAO,KAAKC,kBAAkB,MAAM,kCAAkC;AACtE,OAAO,KAAKC,0BAA0B,MAAM,0CAA0C;AACtF,OAAO,KAAKC,qBAAqB,MAAM,qCAAqC;AAG5E,OAAO,KAAKC,mBAAmB,MAAM,8CAA8C;AACnF,OAAO,KAAKC,mBAAmB,MAAM,8CAA8C;AACnF,OAAO,KAAKC,oBAAoB,MAAM,+CAA+C;AACrF,OAAO,KAAKC,oBAAoB,MAAM,+CAA+C;AAuBrF,OAAO,MAAMC,UAAiC,GAAG,CAM/CT,uBAAuB,EACvBC,gBAAgB,EAEhBC,kBAAkB,EAClBC,0BAA0B,EAC1BE,mBAAmB,EACnBC,mBAAmB,EACnBC,oBAAoB,EACpBH,qBAAqB,EACrBI,oBAAoB,CACrB;AAGD,OAAO,SAASE,oBAAoBA,CAACC,IAAI,EAA6C;EAAA,IAA3CC,OAA0B,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAAA,IAAEG,OAAQ,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAClF,MAAME,UAAU,GAAGR,UAAU,CAACS,MAAM,CAAEC,SAAS,IAAKC,YAAY,CAACD,SAAS,CAACE,IAAI,EAAET,OAAO,CAAC,CAAC;EAC1F,KAAK,MAAMO,SAAS,IAAIF,UAAU,EAAE;IAAA,IAAAK,qBAAA;IAClC,CAAAA,qBAAA,GAAAH,SAAS,CAACI,UAAU,cAAAD,qBAAA,uBAApBA,qBAAA,CAAAE,IAAA,CAAAL,SAAS,EAAcR,IAAI,EAAEC,OAAO,EAAEI,OAAO,CAAC;EAChD;AACF;AAGA,OAAO,eAAeS,gBAAgBA,CAACd,IAAI,EAA6C;EAAA,IAA3CC,OAA0B,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAAA,IAAEG,OAAQ,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EACpF,MAAME,UAAU,GAAGR,UAAU,CAACS,MAAM,CAAEC,SAAS,IAAKC,YAAY,CAACD,SAAS,CAACE,IAAI,EAAET,OAAO,CAAC,CAAC;EAC1F,KAAK,MAAMO,SAAS,IAAIF,UAAU,EAAE;IAAA,IAAAS,iBAAA;IAGlC,QAAAA,iBAAA,GAAMP,SAAS,CAACQ,MAAM,cAAAD,iBAAA,uBAAhBA,iBAAA,CAAAF,IAAA,CAAAL,SAAS,EAAUR,IAAI,EAAEC,OAAO,EAAEI,OAAO,CAAC;EAClD;AACF;AAEA,SAASI,YAAYA,CAACQ,aAAqB,EAAEhB,OAA0B,EAAE;EAAA,IAAAiB,aAAA;EACvE,MAAMC,QAAQ,GAAG,CAAAlB,OAAO,aAAPA,OAAO,wBAAAiB,aAAA,GAAPjB,OAAO,CAAED,IAAI,cAAAkB,aAAA,uBAAbA,aAAA,CAAeE,iBAAiB,KAAI,CAAC,CAAC;EACvD,MAAMC,OAAO,GAAGJ,aAAa,IAAIE,QAAQ,IAAI,CAACA,QAAQ,CAACF,aAAa,CAAC;EACrE,OAAO,CAACI,OAAO;AACjB"}