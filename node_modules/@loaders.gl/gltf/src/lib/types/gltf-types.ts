/* eslint-disable camelcase */

// Export renamed auto generated types
import type {
  GLTF,
  Accessor as GLTFAccessor,
  <PERSON><PERSON><PERSON> as G<PERSON>FBuffer,
  <PERSON><PERSON><PERSON><PERSON>ie<PERSON> as GLTFBufferView,
  MeshPrimitive as GLTFMeshPrimitive,
  Mesh as GLT<PERSON>esh,
  Node as GLTFN<PERSON>,
  Material as GLTFMaterial,
  <PERSON><PERSON> as GLT<PERSON>ampler,
  Scene as GLTFScene,
  Skin as GLTFSkin,
  Texture as GLTFTexture,
  Image as GLTFImage,
  GLTF_KHR_binary_glTF,
  GLTF_KHR_draco_mesh_compression,
  GLTF_KHR_texture_basisu,
  GLTF_EXT_meshopt_compression,
  GLTF_EXT_texture_webp,
  GLTF_EXT_feature_metadata,
  GLTF_EXT_feature_metadata_primitive,
  GLTF_EXT_feature_metadata_attribute
} from './gltf-json-schema';

import type {
  GLTF as GLTFPostprocessed,
  Accessor as GLTFAccessorPostprocessed,
  Image as GLTFImagePostprocessed,
  Mesh as GLTFMeshPostprocessed,
  MeshPrimitive as GLTFMeshPrimitivePostprocessed,
  Material as GLT<PERSON>aterialPostprocessed,
  Node as GLTFNodePostprocessed,
  Texture as GLTFTexturePostprocessed
} from './gltf-postprocessed-schema';

// Source glTF types
export type {
  GLTF,
  GLTFAccessor,
  GLTFBuffer,
  GLTFBufferView,
  GLTFMeshPrimitive,
  GLTFMesh,
  GLTFNode,
  GLTFMaterial,
  GLTFSampler,
  GLTFScene,
  GLTFSkin,
  GLTFTexture,
  GLTFImage,
  GLTF_KHR_binary_glTF,
  GLTF_KHR_draco_mesh_compression,
  GLTF_KHR_texture_basisu,
  GLTF_EXT_meshopt_compression,
  GLTF_EXT_texture_webp,
  GLTF_EXT_feature_metadata,
  GLTF_EXT_feature_metadata_primitive,
  GLTF_EXT_feature_metadata_attribute
};

// Post processed glTF types
export type {
  GLTFPostprocessed,
  GLTFAccessorPostprocessed,
  GLTFImagePostprocessed,
  GLTFNodePostprocessed,
  GLTFMeshPostprocessed,
  GLTFMeshPrimitivePostprocessed,
  GLTFMaterialPostprocessed,
  GLTFTexturePostprocessed
};

export type GLTFObject =
  | GLTFAccessor
  | GLTFBuffer
  | GLTFBufferView
  | GLTFMeshPrimitive
  | GLTFMesh
  | GLTFNode
  | GLTFMaterial
  | GLTFSampler
  | GLTFScene
  | GLTFSkin
  | GLTFTexture
  | GLTFImage;

/** GLTFLoader removes processed extensions from `extensionsUsed` and `extensionsUsed`
 * `processedExtensions` is used to track those extensions
 */
export type GLTFWithBuffers = {
  json: GLTF;
  buffers: any[];
  binary?: ArrayBuffer;
  images?: any[];
};
