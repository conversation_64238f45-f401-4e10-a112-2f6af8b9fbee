{"name": "@loaders.gl/loader-utils", "version": "3.4.15", "description": "Framework-independent loaders for 3D graphics formats", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/visgl/loaders.gl"}, "keywords": ["webgl", "loader", "3d", "mesh", "point cloud"], "types": "dist/index.d.ts", "main": "dist/es5/index.js", "module": "dist/esm/index.js", "sideEffects": false, "files": ["src", "dist", "README.md"], "browser": {"./src/lib/node/buffer.ts": "./src/lib/node/buffer.browser.ts", "./dist/es5/lib/node/buffer.js": "./dist/es5/lib/node/buffer.browser.js", "./dist/esm/lib/node/buffer.js": "./dist/esm/lib/node/buffer.browser.js", "./src/lib/node/fs.ts": "./src/lib/node/fs.browser.ts", "./dist/es5/lib/node/fs.js": "./dist/es5/lib/node/fs.browser.js", "./dist/esm/lib/node/fs.js": "./dist/esm/lib/node/fs.browser.js", "./src/lib/node/stream.ts": "./src/lib/node/stream.browser.ts", "./dist/es5/lib/node/stream.js": "./dist/es5/lib/node/stream.browser.js", "./dist/esm/lib/node/stream.js": "./dist/esm/lib/node/stream.browser.js", "fs": false, "stream": false}, "scripts": {}, "dependencies": {"@babel/runtime": "^7.3.1", "@loaders.gl/worker-utils": "3.4.15", "@probe.gl/stats": "^3.5.0"}, "gitHead": "19e941d5805568e449ef9092490d6568a4853298"}