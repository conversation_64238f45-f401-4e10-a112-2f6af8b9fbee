{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AACA,YAAY,EACV,MAAM,EACN,gBAAgB,EAChB,aAAa,EACb,aAAa,EACb,MAAM,EACN,aAAa,EACb,QAAQ,EACR,YAAY,EACZ,iBAAiB,EACjB,WAAW,EACX,2BAA2B,EAC5B,MAAM,SAAS,CAAC;AAGjB,OAAO,EAAC,MAAM,EAAC,MAAM,wBAAwB,CAAC;AAC9C,OAAO,EACL,SAAS,EACT,QAAQ,EACR,WAAW,EACX,IAAI,EACJ,MAAM,EACN,MAAM,EACN,QAAQ,EACT,MAAM,yBAAyB,CAAC;AAEjC,OAAO,EAAC,kBAAkB,EAAC,MAAM,yCAAyC,CAAC;AAG3E,OAAO,EAAC,kBAAkB,EAAC,MAAM,gDAAgD,CAAC;AAClF,OAAO,EAAC,eAAe,EAAE,kBAAkB,EAAC,MAAM,6CAA6C,CAAC;AAChG,OAAO,EAAC,mBAAmB,EAAC,MAAM,8CAA8C,CAAC;AAGjF,OAAO,EAAC,SAAS,EAAC,MAAM,+BAA+B,CAAC;AAGxD,OAAO,EACL,gBAAgB,EAChB,uBAAuB,EACvB,sBAAsB,EACtB,mBAAmB,EACpB,MAAM,uCAAuC,CAAC;AAC/C,OAAO,EAAC,WAAW,EAAE,WAAW,EAAE,eAAe,EAAC,MAAM,sCAAsC,CAAC;AAC/F,OAAO,EACL,wBAAwB,EACxB,oBAAoB,EACpB,oBAAoB,EACpB,+BAA+B,EAC/B,0BAA0B,EAC3B,MAAM,wCAAwC,CAAC;AAChD,OAAO,EAAC,kBAAkB,EAAE,cAAc,EAAC,MAAM,yCAAyC,CAAC;AAG3F,OAAO,EACL,uBAAuB,EACvB,uBAAuB,EACvB,gBAAgB,EAChB,wBAAwB,EACzB,MAAM,gCAAgC,CAAC;AACxC,OAAO,EAAC,OAAO,EAAE,4BAA4B,EAAC,MAAM,iCAAiC,CAAC;AAGtF,OAAO,EAAC,OAAO,IAAI,gBAAgB,EAAC,MAAM,uCAAuC,CAAC;AAGlF,OAAO,EAAC,aAAa,EAAE,aAAa,EAAE,WAAW,EAAC,MAAM,+BAA+B,CAAC;AACxF,OAAO,EAAC,UAAU,IAAI,WAAW,EAAC,MAAM,+BAA+B,CAAC;AAGxE,OAAO,EAAC,UAAU,EAAC,MAAM,eAAe,CAAC;AAOzC,OAAO,EAAC,QAAQ,EAAE,QAAQ,EAAE,aAAa,EAAC,MAAM,4CAA4C,CAAC;AAK7F,OAAO,EAAC,UAAU,EAAE,UAAU,EAAC,MAAM,sBAAsB,CAAC;AAG5D,OAAO,KAAK,IAAI,MAAM,uBAAuB,CAAC;AAC9C,OAAO,EAAC,IAAI,EAAC,CAAC;AAGd,OAAO,KAAK,EAAE,MAAM,eAAe,CAAC;AACpC,OAAO,EAAC,EAAE,EAAC,CAAC;AAGZ,OAAO,KAAK,MAAM,MAAM,mBAAmB,CAAC;AAC5C,OAAO,EAAC,MAAM,EAAC,CAAC;AAGhB,YAAY,EAAC,YAAY,EAAC,MAAM,iCAAiC,CAAC;AAClE,OAAO,EAAC,gBAAgB,EAAC,MAAM,iCAAiC,CAAC;AAEjE,YAAY,EAAC,YAAY,EAAC,MAAM,iCAAiC,CAAC;AAClE,OAAO,EAAC,gBAAgB,EAAC,MAAM,iCAAiC,CAAC;AAEjE,OAAO,EAAC,OAAO,IAAI,eAAe,EAAC,MAAM,mCAAmC,CAAC"}