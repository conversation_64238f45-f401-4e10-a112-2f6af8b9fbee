export type { Loader, LoaderWithParser, LoaderContext, LoaderOptions, Writer, WriterOptions, DataType, SyncDataType, BatchableDataType, IFileSystem, IRandomAccessReadFileSystem } from './types';
export { assert } from './lib/env-utils/assert';
export { isBrowser, isWorker, nodeVersion, self, window, global, document } from './lib/env-utils/globals';
export { mergeLoaderOptions } from './lib/option-utils/merge-loader-options';
export { createLoaderWorker } from './lib/worker-loader-utils/create-loader-worker';
export { parseWithWorker, canParseWithWorker } from './lib/worker-loader-utils/parse-with-worker';
export { canEncodeWithWorker } from './lib/worker-loader-utils/encode-with-worker';
export { parseJSON } from './lib/parser-utils/parse-json';
export { sliceArrayBuffer, concatenateArrayBuffers, concatenateTypedArrays, compareArrayBuffers } from './lib/binary-utils/array-buffer-utils';
export { padToNBytes, copyToArray, copyArrayBuffer } from './lib/binary-utils/memory-copy-utils';
export { padStringToByteAlignment, copyStringToDataView, copyBinaryToDataView, copyPaddedArrayBufferToDataView, copyPaddedStringToDataView } from './lib/binary-utils/dataview-copy-utils';
export { getFirstCharacters, getMagicString } from './lib/binary-utils/get-first-characters';
export { makeTextEncoderIterator, makeTextDecoderIterator, makeLineIterator, makeNumberedLineIterator } from './lib/iterators/text-iterators';
export { forEach, concatenateArrayBuffersAsync } from './lib/iterators/async-iteration';
export { default as RequestScheduler } from './lib/request-utils/request-scheduler';
export { setPathPrefix, getPathPrefix, resolvePath } from './lib/path-utils/file-aliases';
export { addAliases as _addAliases } from './lib/path-utils/file-aliases';
export { JSONLoader } from './json-loader';
export { isBuffer, toBuffer, toArrayBuffer } from './lib/binary-utils/memory-conversion-utils';
export { promisify1, promisify2 } from './lib/node/promisify';
import * as path from './lib/path-utils/path';
export { path };
import * as fs from './lib/node/fs';
export { fs };
import * as stream from './lib/node/stream';
export { stream };
export type { ReadableFile } from './lib/filesystems/readable-file';
export { makeReadableFile } from './lib/filesystems/readable-file';
export type { WritableFile } from './lib/filesystems/writable-file';
export { makeWritableFile } from './lib/filesystems/writable-file';
export { default as _NodeFileSystem } from './lib/filesystems/node-filesystem';
//# sourceMappingURL=index.d.ts.map