/// <reference types="node" />
import fs from 'fs';
export type { Stats, WriteStream } from 'fs';
/** Wrapper for Node.js fs method */
export declare const readdir: any;
/** Wrapper for Node.js fs method */
export declare const stat: any;
/** Wrapper for Node.js fs method */
export declare const readFile: any;
/** Wrapper for Node.js fs method */
export declare const readFileSync: typeof fs.readFileSync;
/** Wrapper for Node.js fs method */
export declare const writeFile: any;
/** Wrapper for Node.js fs method */
export declare const writeFileSync: typeof fs.writeFileSync;
/** Wrapper for Node.js fs method */
export declare const open: any;
/** Wrapper for Node.js fs method */
export declare const close: (fd: number) => Promise<void>;
/** Wrapper for Node.js fs method */
export declare const read: any;
/** Wrapper for Node.js fs method */
export declare const fstat: any;
export declare const createWriteStream: typeof fs.createWriteStream;
export declare const isSupported: boolean;
export declare function _readToArrayBuffer(fd: number, start: number, length: number): Promise<any>;
//# sourceMappingURL=fs.d.ts.map