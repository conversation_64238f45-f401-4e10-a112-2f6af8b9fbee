{"version": 3, "file": "async-iteration.d.ts", "sourceRoot": "", "sources": ["../../../src/lib/iterators/async-iteration.ts"], "names": [], "mappings": "AAIA;;;;;;;;;;;GAWG;AACH,wBAAsB,OAAO,CAAC,QAAQ,KAAA,EAAE,OAAO,KAAA,iBAa9C;AAID;;;GAGG;AAEH,wBAAsB,4BAA4B,CAChD,aAAa,EAAE,aAAa,CAAC,WAAW,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,GAChE,OAAO,CAAC,WAAW,CAAC,CAMtB;AAED,wBAAsB,uBAAuB,CAC3C,aAAa,EAAE,aAAa,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,GACtD,OAAO,CAAC,MAAM,CAAC,CAMjB"}