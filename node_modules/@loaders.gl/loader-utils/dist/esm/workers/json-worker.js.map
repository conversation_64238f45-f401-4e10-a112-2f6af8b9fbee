{"version": 3, "file": "json-worker.js", "names": ["createLoaderWorker", "JSONLoader"], "sources": ["../../../src/workers/json-worker.ts"], "sourcesContent": ["import {createLoaderWorker} from '../lib/worker-loader-utils/create-loader-worker';\nimport {JSONLoader} from '../json-loader';\n\ncreateLoaderWorker(JSONLoader);\n"], "mappings": "AAAA,SAAQA,kBAAkB,QAAO,iDAAiD;AAClF,SAAQC,UAAU,QAAO,gBAAgB;AAEzCD,kBAAkB,CAACC,UAAU,CAAC"}