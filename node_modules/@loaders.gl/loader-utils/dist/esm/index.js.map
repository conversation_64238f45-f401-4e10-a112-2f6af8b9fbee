{"version": 3, "file": "index.js", "names": ["assert", "<PERSON><PERSON><PERSON><PERSON>", "isWorker", "nodeVersion", "self", "window", "global", "document", "mergeLoaderOptions", "createLoaderWorker", "parseWithWorker", "canParseWithWorker", "canEncodeWithWorker", "parseJSON", "sliceArrayBuffer", "concatenateArrayBuffers", "concatenateTypedArrays", "compareArrayBuffers", "padToNBytes", "copyToArray", "copyArrayBuffer", "padStringToByteAlignment", "copyStringToDataView", "copyBinaryToDataView", "copyPaddedArrayBufferToDataView", "copyPaddedStringToDataView", "getFirstCharacters", "getMagicString", "makeTextEncoderIterator", "makeTextDecoderIterator", "makeLineIterator", "makeNumberedLineIterator", "for<PERSON>ach", "concatenateArrayBuffersAsync", "default", "RequestScheduler", "setPathPrefix", "getPathPrefix", "<PERSON><PERSON><PERSON>", "addAliases", "_addAliases", "JSONLoader", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "promisify1", "promisify2", "path", "fs", "stream", "makeReadableFile", "makeWritableFile", "_NodeFileSystem"], "sources": ["../../src/index.ts"], "sourcesContent": ["// TYPES\nexport type {\n  Lo<PERSON>,\n  LoaderWithParser,\n  LoaderContext,\n  LoaderOptions,\n  Writer,\n  WriterOptions,\n  DataType,\n  SyncDataType,\n  BatchableDataType,\n  IFileSystem,\n  IRandomAccessReadFileSystem\n} from './types';\n\n// GENERAL UTILS\nexport {assert} from './lib/env-utils/assert';\nexport {\n  isBrowser,\n  isWorker,\n  nodeVersion,\n  self,\n  window,\n  global,\n  document\n} from './lib/env-utils/globals';\n\nexport {mergeLoaderOptions} from './lib/option-utils/merge-loader-options';\n\n// LOADERS.GL-SPECIFIC WORKER UTILS\nexport {createLoaderWorker} from './lib/worker-loader-utils/create-loader-worker';\nexport {parseWithWorker, canParseWithWorker} from './lib/worker-loader-utils/parse-with-worker';\nexport {canEncodeWithWorker} from './lib/worker-loader-utils/encode-with-worker';\n\n// PARSER UTILS\nexport {parseJSON} from './lib/parser-utils/parse-json';\n\n// MEMORY COPY UTILS\nexport {\n  sliceArrayBuffer,\n  concatenateArrayBuffers,\n  concatenateTypedArrays,\n  compareArrayBuffers\n} from './lib/binary-utils/array-buffer-utils';\nexport {padToNBytes, copyToArray, copyArrayBuffer} from './lib/binary-utils/memory-copy-utils';\nexport {\n  padStringToByteAlignment,\n  copyStringToDataView,\n  copyBinaryToDataView,\n  copyPaddedArrayBufferToDataView,\n  copyPaddedStringToDataView\n} from './lib/binary-utils/dataview-copy-utils';\nexport {getFirstCharacters, getMagicString} from './lib/binary-utils/get-first-characters';\n\n// ITERATOR UTILS\nexport {\n  makeTextEncoderIterator,\n  makeTextDecoderIterator,\n  makeLineIterator,\n  makeNumberedLineIterator\n} from './lib/iterators/text-iterators';\nexport {forEach, concatenateArrayBuffersAsync} from './lib/iterators/async-iteration';\n\n// REQUEST UTILS\nexport {default as RequestScheduler} from './lib/request-utils/request-scheduler';\n\n// PATH HELPERS\nexport {setPathPrefix, getPathPrefix, resolvePath} from './lib/path-utils/file-aliases';\nexport {addAliases as _addAliases} from './lib/path-utils/file-aliases';\n\n// MICRO LOADERS\nexport {JSONLoader} from './json-loader';\n\n// NODE support\n\n// Node.js emulation (can be used in browser)\n\n// Avoid direct use of `Buffer` which pulls in 50KB polyfill\nexport {isBuffer, toBuffer, toArrayBuffer} from './lib/binary-utils/memory-conversion-utils';\n\n// Note.js wrappers (can be safely imported, but not used in browser)\n\n// Use instead of importing 'util' to avoid node dependencies\nexport {promisify1, promisify2} from './lib/node/promisify';\n\n// `path` replacement (avoids bundling big path polyfill)\nimport * as path from './lib/path-utils/path';\nexport {path};\n\n// Use instead of importing 'fs' to avoid node dependencies`\nimport * as fs from './lib/node/fs';\nexport {fs};\n\n// Use instead of importing 'stream' to avoid node dependencies`\nimport * as stream from './lib/node/stream';\nexport {stream};\n\n// EXPERIMENTAL\nexport type {ReadableFile} from './lib/filesystems/readable-file';\nexport {makeReadableFile} from './lib/filesystems/readable-file';\n\nexport type {WritableFile} from './lib/filesystems/writable-file';\nexport {makeWritableFile} from './lib/filesystems/writable-file';\n\nexport {default as _NodeFileSystem} from './lib/filesystems/node-filesystem';\n"], "mappings": "AAgBA,SAAQA,MAAM,QAAO,wBAAwB;AAC7C,SACEC,SAAS,EACTC,QAAQ,EACRC,WAAW,EACXC,IAAI,EACJC,MAAM,EACNC,MAAM,EACNC,QAAQ,QACH,yBAAyB;AAEhC,SAAQC,kBAAkB,QAAO,yCAAyC;AAG1E,SAAQC,kBAAkB,QAAO,gDAAgD;AACjF,SAAQC,eAAe,EAAEC,kBAAkB,QAAO,6CAA6C;AAC/F,SAAQC,mBAAmB,QAAO,8CAA8C;AAGhF,SAAQC,SAAS,QAAO,+BAA+B;AAGvD,SACEC,gBAAgB,EAChBC,uBAAuB,EACvBC,sBAAsB,EACtBC,mBAAmB,QACd,uCAAuC;AAC9C,SAAQC,WAAW,EAAEC,WAAW,EAAEC,eAAe,QAAO,sCAAsC;AAC9F,SACEC,wBAAwB,EACxBC,oBAAoB,EACpBC,oBAAoB,EACpBC,+BAA+B,EAC/BC,0BAA0B,QACrB,wCAAwC;AAC/C,SAAQC,kBAAkB,EAAEC,cAAc,QAAO,yCAAyC;AAG1F,SACEC,uBAAuB,EACvBC,uBAAuB,EACvBC,gBAAgB,EAChBC,wBAAwB,QACnB,gCAAgC;AACvC,SAAQC,OAAO,EAAEC,4BAA4B,QAAO,iCAAiC;AAGrF,SAAQC,OAAO,IAAIC,gBAAgB,QAAO,uCAAuC;AAGjF,SAAQC,aAAa,EAAEC,aAAa,EAAEC,WAAW,QAAO,+BAA+B;AACvF,SAAQC,UAAU,IAAIC,WAAW,QAAO,+BAA+B;AAGvE,SAAQC,UAAU,QAAO,eAAe;AAOxC,SAAQC,QAAQ,EAAEC,QAAQ,EAAEC,aAAa,QAAO,4CAA4C;AAK5F,SAAQC,UAAU,EAAEC,UAAU,QAAO,sBAAsB;AAG3D,OAAO,KAAKC,IAAI,MAAM,uBAAuB;AAC7C,SAAQA,IAAI;AAGZ,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAAQA,EAAE;AAGV,OAAO,KAAKC,MAAM,MAAM,mBAAmB;AAC3C,SAAQA,MAAM;AAId,SAAQC,gBAAgB,QAAO,iCAAiC;AAGhE,SAAQC,gBAAgB,QAAO,iCAAiC;AAEhE,SAAQjB,OAAO,IAAIkB,eAAe,QAAO,mCAAmC"}