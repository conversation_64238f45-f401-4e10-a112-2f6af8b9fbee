const VERSION = typeof "3.4.15" !== 'undefined' ? "3.4.15" : 'latest';
export const JSONLoader = {
  name: '<PERSON><PERSON><PERSON>',
  id: 'json',
  module: 'json',
  version: VERSION,
  extensions: ['json', 'geojson'],
  mimeTypes: ['application/json'],
  category: 'json',
  text: true,
  parseTextSync,
  parse: async arrayBuffer => parseTextSync(new TextDecoder().decode(arrayBuffer)),
  options: {}
};
function parseTextSync(text) {
  return JSON.parse(text);
}
export const _typecheckJSONLoader = JSONLoader;
//# sourceMappingURL=json-loader.js.map