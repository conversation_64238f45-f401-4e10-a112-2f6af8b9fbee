export function mergeLoaderOptions(baseOptions, newOptions) {
  const options = {
    ...baseOptions
  };
  for (const [key, newValue] of Object.entries(newOptions)) {
    if (newValue && typeof newValue === 'object') {
      options[key] = options[key] || {};
      Object.assign(options[key], newOptions[key]);
    } else {
      options[key] = newOptions[key];
    }
  }
  return options;
}
//# sourceMappingURL=merge-loader-options.js.map