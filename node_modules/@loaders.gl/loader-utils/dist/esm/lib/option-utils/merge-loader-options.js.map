{"version": 3, "file": "merge-loader-options.js", "names": ["mergeLoaderOptions", "baseOptions", "newOptions", "options", "key", "newValue", "Object", "entries", "assign"], "sources": ["../../../../src/lib/option-utils/merge-loader-options.ts"], "sourcesContent": ["// loaders.gl, MIT license\n\nimport {LoaderOptions} from '../../types';\n\n/**\n *\n * @param baseOptions Can be undefined, in which case a fresh options object will be minted\n * @param newOptions\n * @returns\n */\nexport function mergeLoaderOptions<Options extends LoaderOptions>(\n  baseOptions: Options | undefined,\n  newOptions: Options\n): Options {\n  const options = {...baseOptions};\n  for (const [key, newValue] of Object.entries(newOptions)) {\n    if (newValue && typeof newValue === 'object') {\n      options[key] = options[key] || {};\n      Object.assign(options[key] as object, newOptions[key]);\n    } else {\n      options[key] = newOptions[key];\n    }\n  }\n  return options as Options;\n}\n"], "mappings": "AAUA,OAAO,SAASA,kBAAkBA,CAChCC,WAAgC,EAChCC,UAAmB,EACV;EACT,MAAMC,OAAO,GAAG;IAAC,GAAGF;EAAW,CAAC;EAChC,KAAK,MAAM,CAACG,GAAG,EAAEC,QAAQ,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACL,UAAU,CAAC,EAAE;IACxD,IAAIG,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAC5CF,OAAO,CAACC,GAAG,CAAC,GAAGD,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC,CAAC;MACjCE,MAAM,CAACE,MAAM,CAACL,OAAO,CAACC,GAAG,CAAC,EAAYF,UAAU,CAACE,GAAG,CAAC,CAAC;IACxD,CAAC,MAAM;MACLD,OAAO,CAACC,GAAG,CAAC,GAAGF,UAAU,CAACE,GAAG,CAAC;IAChC;EACF;EACA,OAAOD,OAAO;AAChB"}