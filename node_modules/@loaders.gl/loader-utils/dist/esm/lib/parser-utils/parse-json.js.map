{"version": 3, "file": "parse-json.js", "names": ["getFirstCharacters", "parseJSON", "string", "JSON", "parse", "_", "Error", "concat"], "sources": ["../../../../src/lib/parser-utils/parse-json.ts"], "sourcesContent": ["import {getFirstCharacters} from '../binary-utils/get-first-characters';\n\n/**\n * Minimal JSON parser that throws more meaningful error messages\n */\nexport function parseJSON(string: string): any {\n  try {\n    return JSON.parse(string);\n  } catch (_) {\n    throw new Error(`Failed to parse JSON from data starting with \"${getFirstCharacters(string)}\"`);\n  }\n}\n"], "mappings": "AAAA,SAAQA,kBAAkB,QAAO,sCAAsC;AAKvE,OAAO,SAASC,SAASA,CAACC,MAAc,EAAO;EAC7C,IAAI;IACF,OAAOC,IAAI,CAACC,KAAK,CAACF,MAAM,CAAC;EAC3B,CAAC,CAAC,OAAOG,CAAC,EAAE;IACV,MAAM,IAAIC,KAAK,mDAAAC,MAAA,CAAkDP,kBAAkB,CAACE,MAAM,CAAC,OAAG,CAAC;EACjG;AACF"}