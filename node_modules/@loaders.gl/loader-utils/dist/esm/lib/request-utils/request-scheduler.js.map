{"version": 3, "file": "request-scheduler.js", "names": ["Stats", "STAT_QUEUED_REQUESTS", "STAT_ACTIVE_REQUESTS", "STAT_CANCELLED_REQUESTS", "STAT_QUEUED_REQUESTS_EVER", "STAT_ACTIVE_REQUESTS_EVER", "DEFAULT_PROPS", "id", "throttleRequests", "maxRequests", "RequestScheduler", "constructor", "props", "arguments", "length", "undefined", "_defineProperty", "Map", "stats", "get", "scheduleRequest", "handle", "getPriority", "Promise", "resolve", "done", "requestMap", "has", "request", "priority", "promise", "requestQueue", "push", "set", "_issueNewRequests", "_issueRequest", "isDone", "delete", "activeRequestCount", "deferredUpdate", "setTimeout", "_issueNewRequestsAsync", "freeSlots", "Math", "max", "_updateAllRequests", "i", "shift", "_updateRequest", "splice", "sort", "a", "b"], "sources": ["../../../../src/lib/request-utils/request-scheduler.ts"], "sourcesContent": ["import {Stats} from '@probe.gl/stats';\n\ntype Handle = any;\ntype DoneFunction = () => any;\ntype GetPriorityFunction = () => number;\ntype RequestResult = {\n  done: DoneFunction;\n} | null;\n\n/** RequestScheduler Options */\nexport type RequestSchedulerProps = {\n  id?: string;\n  throttleRequests?: boolean;\n  maxRequests?: number;\n};\n\nconst STAT_QUEUED_REQUESTS = 'Queued Requests';\nconst STAT_ACTIVE_REQUESTS = 'Active Requests';\nconst STAT_CANCELLED_REQUESTS = 'Cancelled Requests';\nconst STAT_QUEUED_REQUESTS_EVER = 'Queued Requests Ever';\nconst STAT_ACTIVE_REQUESTS_EVER = 'Active Requests Ever';\n\nconst DEFAULT_PROPS: Required<RequestSchedulerProps> = {\n  id: 'request-scheduler',\n  // Specifies if the request scheduler should throttle incoming requests, mainly for comparative testing\n  throttleRequests: true,\n  // The maximum number of simultaneous active requests. Un-throttled requests do not observe this limit.\n  maxRequests: 6\n};\n\n/** Tracks one request */\ntype Request = {\n  handle: Handle;\n  priority: number;\n  getPriority: GetPriorityFunction;\n  resolve?: (value: any) => any;\n};\n\n/**\n * Used to issue a request, without having them \"deeply queued\" by the browser.\n * @todo - Track requests globally, across multiple servers\n */\nexport default class RequestScheduler {\n  readonly props: Required<RequestSchedulerProps>;\n  readonly stats: Stats;\n  activeRequestCount: number = 0;\n\n  /** Tracks the number of active requests and prioritizes/cancels queued requests. */\n  private requestQueue: Request[] = [];\n  private requestMap: Map<Handle, Promise<RequestResult>> = new Map();\n  private deferredUpdate: any = null;\n\n  constructor(props: RequestSchedulerProps = {}) {\n    this.props = {...DEFAULT_PROPS, ...props};\n\n    // Returns the statistics used by the request scheduler.\n    this.stats = new Stats({id: this.props.id});\n    this.stats.get(STAT_QUEUED_REQUESTS);\n    this.stats.get(STAT_ACTIVE_REQUESTS);\n    this.stats.get(STAT_CANCELLED_REQUESTS);\n    this.stats.get(STAT_QUEUED_REQUESTS_EVER);\n    this.stats.get(STAT_ACTIVE_REQUESTS_EVER);\n  }\n\n  /**\n   * Called by an application that wants to issue a request, without having it deeply queued by the browser\n   *\n   * When the returned promise resolved, it is OK for the application to issue a request.\n   * The promise resolves to an object that contains a `done` method.\n   * When the application's request has completed (or failed), the application must call the `done` function\n   *\n   * @param handle\n   * @param getPriority will be called when request \"slots\" open up,\n   *    allowing the caller to update priority or cancel the request\n   *    Highest priority executes first, priority < 0 cancels the request\n   * @returns a promise\n   *   - resolves to a object (with a `done` field) when the request can be issued without queueing,\n   *   - resolves to `null` if the request has been cancelled (by the callback return < 0).\n   *     In this case the application should not issue the request\n   */\n  scheduleRequest(\n    handle: Handle,\n    getPriority: GetPriorityFunction = () => 0\n  ): Promise<RequestResult> {\n    // Allows throttling to be disabled\n    if (!this.props.throttleRequests) {\n      return Promise.resolve({done: () => {}});\n    }\n\n    // dedupe\n    if (this.requestMap.has(handle)) {\n      return this.requestMap.get(handle) as Promise<any>;\n    }\n\n    const request: Request = {handle, priority: 0, getPriority};\n    const promise = new Promise<RequestResult>((resolve) => {\n      // @ts-ignore\n      request.resolve = resolve;\n      return request;\n    });\n\n    this.requestQueue.push(request);\n    this.requestMap.set(handle, promise);\n    this._issueNewRequests();\n    return promise;\n  }\n\n  // PRIVATE\n\n  _issueRequest(request: Request): Promise<any> {\n    const {handle, resolve} = request;\n    let isDone = false;\n\n    const done = () => {\n      // can only be called once\n      if (!isDone) {\n        isDone = true;\n\n        // Stop tracking a request - it has completed, failed, cancelled etc\n        this.requestMap.delete(handle);\n        this.activeRequestCount--;\n        // A slot just freed up, see if any queued requests are waiting\n        this._issueNewRequests();\n      }\n    };\n\n    // Track this request\n    this.activeRequestCount++;\n\n    return resolve ? resolve({done}) : Promise.resolve({done});\n  }\n\n  /** We check requests asynchronously, to prevent multiple updates */\n  _issueNewRequests(): void {\n    if (!this.deferredUpdate) {\n      this.deferredUpdate = setTimeout(() => this._issueNewRequestsAsync(), 0);\n    }\n  }\n\n  /** Refresh all requests  */\n  _issueNewRequestsAsync() {\n    // TODO - shouldn't we clear the timeout?\n    this.deferredUpdate = null;\n\n    const freeSlots = Math.max(this.props.maxRequests - this.activeRequestCount, 0);\n\n    if (freeSlots === 0) {\n      return;\n    }\n\n    this._updateAllRequests();\n\n    // Resolve pending promises for the top-priority requests\n    for (let i = 0; i < freeSlots; ++i) {\n      const request = this.requestQueue.shift();\n      if (request) {\n        this._issueRequest(request); // eslint-disable-line @typescript-eslint/no-floating-promises\n      }\n    }\n\n    // Uncomment to debug\n    // console.log(`${freeSlots} free slots, ${this.requestQueue.length} queued requests`);\n  }\n\n  /** Ensure all requests have updated priorities, and that no longer valid requests are cancelled */\n  _updateAllRequests() {\n    const requestQueue = this.requestQueue;\n    for (let i = 0; i < requestQueue.length; ++i) {\n      const request = requestQueue[i];\n      if (!this._updateRequest(request)) {\n        // Remove the element and make sure to adjust the counter to account for shortened array\n        requestQueue.splice(i, 1);\n        this.requestMap.delete(request.handle);\n        i--;\n      }\n    }\n\n    // Sort the remaining requests based on priority\n    requestQueue.sort((a, b) => a.priority - b.priority);\n  }\n\n  /** Update a single request by calling the callback */\n  _updateRequest(request) {\n    request.priority = request.getPriority(request.handle); // eslint-disable-line callback-return\n\n    // by returning a negative priority, the callback cancels the request\n    if (request.priority < 0) {\n      request.resolve(null);\n      return false;\n    }\n    return true;\n  }\n}\n"], "mappings": ";AAAA,SAAQA,KAAK,QAAO,iBAAiB;AAgBrC,MAAMC,oBAAoB,GAAG,iBAAiB;AAC9C,MAAMC,oBAAoB,GAAG,iBAAiB;AAC9C,MAAMC,uBAAuB,GAAG,oBAAoB;AACpD,MAAMC,yBAAyB,GAAG,sBAAsB;AACxD,MAAMC,yBAAyB,GAAG,sBAAsB;AAExD,MAAMC,aAA8C,GAAG;EACrDC,EAAE,EAAE,mBAAmB;EAEvBC,gBAAgB,EAAE,IAAI;EAEtBC,WAAW,EAAE;AACf,CAAC;AAcD,eAAe,MAAMC,gBAAgB,CAAC;EAUpCC,WAAWA,CAAA,EAAoC;IAAA,IAAnCC,KAA4B,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAAAG,eAAA;IAAAA,eAAA;IAAAA,eAAA,6BAPhB,CAAC;IAAAA,eAAA,uBAGI,EAAE;IAAAA,eAAA,qBACsB,IAAIC,GAAG,CAAC,CAAC;IAAAD,eAAA,yBACrC,IAAI;IAGhC,IAAI,CAACJ,KAAK,GAAG;MAAC,GAAGN,aAAa;MAAE,GAAGM;IAAK,CAAC;IAGzC,IAAI,CAACM,KAAK,GAAG,IAAIlB,KAAK,CAAC;MAACO,EAAE,EAAE,IAAI,CAACK,KAAK,CAACL;IAAE,CAAC,CAAC;IAC3C,IAAI,CAACW,KAAK,CAACC,GAAG,CAAClB,oBAAoB,CAAC;IACpC,IAAI,CAACiB,KAAK,CAACC,GAAG,CAACjB,oBAAoB,CAAC;IACpC,IAAI,CAACgB,KAAK,CAACC,GAAG,CAAChB,uBAAuB,CAAC;IACvC,IAAI,CAACe,KAAK,CAACC,GAAG,CAACf,yBAAyB,CAAC;IACzC,IAAI,CAACc,KAAK,CAACC,GAAG,CAACd,yBAAyB,CAAC;EAC3C;EAkBAe,eAAeA,CACbC,MAAc,EAEU;IAAA,IADxBC,WAAgC,GAAAT,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,MAAM,CAAC;IAG1C,IAAI,CAAC,IAAI,CAACD,KAAK,CAACJ,gBAAgB,EAAE;MAChC,OAAOe,OAAO,CAACC,OAAO,CAAC;QAACC,IAAI,EAAEA,CAAA,KAAM,CAAC;MAAC,CAAC,CAAC;IAC1C;IAGA,IAAI,IAAI,CAACC,UAAU,CAACC,GAAG,CAACN,MAAM,CAAC,EAAE;MAC/B,OAAO,IAAI,CAACK,UAAU,CAACP,GAAG,CAACE,MAAM,CAAC;IACpC;IAEA,MAAMO,OAAgB,GAAG;MAACP,MAAM;MAAEQ,QAAQ,EAAE,CAAC;MAAEP;IAAW,CAAC;IAC3D,MAAMQ,OAAO,GAAG,IAAIP,OAAO,CAAiBC,OAAO,IAAK;MAEtDI,OAAO,CAACJ,OAAO,GAAGA,OAAO;MACzB,OAAOI,OAAO;IAChB,CAAC,CAAC;IAEF,IAAI,CAACG,YAAY,CAACC,IAAI,CAACJ,OAAO,CAAC;IAC/B,IAAI,CAACF,UAAU,CAACO,GAAG,CAACZ,MAAM,EAAES,OAAO,CAAC;IACpC,IAAI,CAACI,iBAAiB,CAAC,CAAC;IACxB,OAAOJ,OAAO;EAChB;EAIAK,aAAaA,CAACP,OAAgB,EAAgB;IAC5C,MAAM;MAACP,MAAM;MAAEG;IAAO,CAAC,GAAGI,OAAO;IACjC,IAAIQ,MAAM,GAAG,KAAK;IAElB,MAAMX,IAAI,GAAGA,CAAA,KAAM;MAEjB,IAAI,CAACW,MAAM,EAAE;QACXA,MAAM,GAAG,IAAI;QAGb,IAAI,CAACV,UAAU,CAACW,MAAM,CAAChB,MAAM,CAAC;QAC9B,IAAI,CAACiB,kBAAkB,EAAE;QAEzB,IAAI,CAACJ,iBAAiB,CAAC,CAAC;MAC1B;IACF,CAAC;IAGD,IAAI,CAACI,kBAAkB,EAAE;IAEzB,OAAOd,OAAO,GAAGA,OAAO,CAAC;MAACC;IAAI,CAAC,CAAC,GAAGF,OAAO,CAACC,OAAO,CAAC;MAACC;IAAI,CAAC,CAAC;EAC5D;EAGAS,iBAAiBA,CAAA,EAAS;IACxB,IAAI,CAAC,IAAI,CAACK,cAAc,EAAE;MACxB,IAAI,CAACA,cAAc,GAAGC,UAAU,CAAC,MAAM,IAAI,CAACC,sBAAsB,CAAC,CAAC,EAAE,CAAC,CAAC;IAC1E;EACF;EAGAA,sBAAsBA,CAAA,EAAG;IAEvB,IAAI,CAACF,cAAc,GAAG,IAAI;IAE1B,MAAMG,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAAChC,KAAK,CAACH,WAAW,GAAG,IAAI,CAAC6B,kBAAkB,EAAE,CAAC,CAAC;IAE/E,IAAII,SAAS,KAAK,CAAC,EAAE;MACnB;IACF;IAEA,IAAI,CAACG,kBAAkB,CAAC,CAAC;IAGzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,SAAS,EAAE,EAAEI,CAAC,EAAE;MAClC,MAAMlB,OAAO,GAAG,IAAI,CAACG,YAAY,CAACgB,KAAK,CAAC,CAAC;MACzC,IAAInB,OAAO,EAAE;QACX,IAAI,CAACO,aAAa,CAACP,OAAO,CAAC;MAC7B;IACF;EAIF;EAGAiB,kBAAkBA,CAAA,EAAG;IACnB,MAAMd,YAAY,GAAG,IAAI,CAACA,YAAY;IACtC,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,YAAY,CAACjB,MAAM,EAAE,EAAEgC,CAAC,EAAE;MAC5C,MAAMlB,OAAO,GAAGG,YAAY,CAACe,CAAC,CAAC;MAC/B,IAAI,CAAC,IAAI,CAACE,cAAc,CAACpB,OAAO,CAAC,EAAE;QAEjCG,YAAY,CAACkB,MAAM,CAACH,CAAC,EAAE,CAAC,CAAC;QACzB,IAAI,CAACpB,UAAU,CAACW,MAAM,CAACT,OAAO,CAACP,MAAM,CAAC;QACtCyB,CAAC,EAAE;MACL;IACF;IAGAf,YAAY,CAACmB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACtB,QAAQ,GAAGuB,CAAC,CAACvB,QAAQ,CAAC;EACtD;EAGAmB,cAAcA,CAACpB,OAAO,EAAE;IACtBA,OAAO,CAACC,QAAQ,GAAGD,OAAO,CAACN,WAAW,CAACM,OAAO,CAACP,MAAM,CAAC;IAGtD,IAAIO,OAAO,CAACC,QAAQ,GAAG,CAAC,EAAE;MACxBD,OAAO,CAACJ,OAAO,CAAC,IAAI,CAAC;MACrB,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb;AACF"}