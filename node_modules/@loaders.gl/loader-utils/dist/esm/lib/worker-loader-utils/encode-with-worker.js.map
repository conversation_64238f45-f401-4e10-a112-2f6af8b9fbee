{"version": 3, "file": "encode-with-worker.js", "names": ["WorkerFarm", "<PERSON><PERSON><PERSON><PERSON>", "canEncodeWithWorker", "writer", "options", "isSupported", "_nodeWorkers", "worker"], "sources": ["../../../../src/lib/worker-loader-utils/encode-with-worker.ts"], "sourcesContent": ["import {WorkerFarm} from '@loaders.gl/worker-utils';\nimport {Writer, WriterOptions} from '../../types';\nimport {isBrowser} from '../env-utils/globals';\n\n/**\n * Determines if a loader can parse with worker\n * @param loader\n * @param options\n */\nexport function canEncodeWithWorker(writer: Writer, options?: WriterOptions) {\n  if (!WorkerFarm.isSupported()) {\n    return false;\n  }\n\n  // Node workers are still experimental\n  if (!isBrowser && !options?._nodeWorkers) {\n    return false;\n  }\n\n  return writer.worker && options?.worker;\n}\n"], "mappings": "AAAA,SAAQA,UAAU,QAAO,0BAA0B;AAEnD,SAAQC,SAAS,QAAO,sBAAsB;AAO9C,OAAO,SAASC,mBAAmBA,CAACC,MAAc,EAAEC,OAAuB,EAAE;EAC3E,IAAI,CAACJ,UAAU,CAACK,WAAW,CAAC,CAAC,EAAE;IAC7B,OAAO,KAAK;EACd;EAGA,IAAI,CAACJ,SAAS,IAAI,EAACG,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEE,YAAY,GAAE;IACxC,OAAO,KAAK;EACd;EAEA,OAAOH,MAAM,CAACI,MAAM,KAAIH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,MAAM;AACzC"}