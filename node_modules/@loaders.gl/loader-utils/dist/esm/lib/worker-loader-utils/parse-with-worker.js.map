{"version": 3, "file": "parse-with-worker.js", "names": ["<PERSON><PERSON><PERSON><PERSON>", "WorkerFarm", "getWorkerURL", "canParseWithWorker", "loader", "options", "isSupported", "_nodeWorkers", "worker", "parseWithWorker", "data", "context", "parseOnMainThread", "name", "id", "url", "workerFarm", "getWorkerFarm", "workerPool", "getWorkerPool", "JSON", "parse", "stringify", "job", "startJob", "onMessage", "bind", "postMessage", "input", "result", "type", "payload", "done", "error", "Error", "message", "console", "warn", "concat"], "sources": ["../../../../src/lib/worker-loader-utils/parse-with-worker.ts"], "sourcesContent": ["import {\n  WorkerJob,\n  WorkerMessageType,\n  WorkerMessagePayload,\n  isBrowser\n} from '@loaders.gl/worker-utils';\nimport type {Loader, LoaderOptions, LoaderContext} from '../../types';\nimport {WorkerFarm, getWorkerURL} from '@loaders.gl/worker-utils';\n\n/**\n * Determines if a loader can parse with worker\n * @param loader\n * @param options\n */\nexport function canParseWithWorker(loader: Loader, options?: LoaderOptions) {\n  if (!WorkerFarm.isSupported()) {\n    return false;\n  }\n\n  // Node workers are still experimental\n  if (!isBrowser && !options?._nodeWorkers) {\n    return false;\n  }\n\n  return loader.worker && options?.worker;\n}\n\n/**\n * this function expects that the worker function sends certain messages,\n * this can be automated if the worker is wrapper by a call to createLoaderWorker in @loaders.gl/loader-utils.\n */\nexport async function parseWithWorker(\n  loader: Loader,\n  data: any,\n  options?: LoaderOptions,\n  context?: LoaderContext,\n  parseOnMainThread?: (arrayBuffer: <PERSON><PERSON><PERSON><PERSON><PERSON>er, options: {[key: string]: any}) => Promise<void>\n) {\n  const name = loader.id; // TODO\n  const url = getWorkerURL(loader, options);\n\n  const workerFarm = WorkerFarm.getWorkerFarm(options);\n  const workerPool = workerFarm.getWorkerPool({name, url});\n\n  // options.log object contains functions which cannot be transferred\n  // context.fetch & context.parse functions cannot be transferred\n  // TODO - decide how to handle logging on workers\n  options = JSON.parse(JSON.stringify(options));\n  context = JSON.parse(JSON.stringify(context || {}));\n\n  const job = await workerPool.startJob(\n    'process-on-worker',\n    // @ts-expect-error\n    onMessage.bind(null, parseOnMainThread) // eslint-disable-line @typescript-eslint/no-misused-promises\n  );\n\n  job.postMessage('process', {\n    // @ts-ignore\n    input: data,\n    options,\n    context\n  });\n\n  const result = await job.result;\n  // TODO - what is going on here?\n  return await result.result;\n}\n\n/**\n * Handle worker's responses to the main thread\n * @param job\n * @param type\n * @param payload\n */\nasync function onMessage(\n  parseOnMainThread: (arrayBuffer: ArrayBuffer, options?: {[key: string]: any}) => Promise<void>,\n  job: WorkerJob,\n  type: WorkerMessageType,\n  payload: WorkerMessagePayload\n) {\n  switch (type) {\n    case 'done':\n      job.done(payload);\n      break;\n\n    case 'error':\n      job.error(new Error(payload.error));\n      break;\n\n    case 'process':\n      // Worker is asking for main thread to parseO\n      const {id, input, options} = payload;\n      try {\n        const result = await parseOnMainThread(input, options);\n        job.postMessage('done', {id, result});\n      } catch (error) {\n        const message = error instanceof Error ? error.message : 'unknown error';\n        job.postMessage('error', {id, error: message});\n      }\n      break;\n\n    default:\n      // eslint-disable-next-line\n      console.warn(`parse-with-worker unknown message ${type}`);\n  }\n}\n"], "mappings": "AAAA,SAIEA,SAAS,QACJ,0BAA0B;AAEjC,SAAQC,UAAU,EAAEC,YAAY,QAAO,0BAA0B;AAOjE,OAAO,SAASC,kBAAkBA,CAACC,MAAc,EAAEC,OAAuB,EAAE;EAC1E,IAAI,CAACJ,UAAU,CAACK,WAAW,CAAC,CAAC,EAAE;IAC7B,OAAO,KAAK;EACd;EAGA,IAAI,CAACN,SAAS,IAAI,EAACK,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEE,YAAY,GAAE;IACxC,OAAO,KAAK;EACd;EAEA,OAAOH,MAAM,CAACI,MAAM,KAAIH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,MAAM;AACzC;AAMA,OAAO,eAAeC,eAAeA,CACnCL,MAAc,EACdM,IAAS,EACTL,OAAuB,EACvBM,OAAuB,EACvBC,iBAA8F,EAC9F;EACA,MAAMC,IAAI,GAAGT,MAAM,CAACU,EAAE;EACtB,MAAMC,GAAG,GAAGb,YAAY,CAACE,MAAM,EAAEC,OAAO,CAAC;EAEzC,MAAMW,UAAU,GAAGf,UAAU,CAACgB,aAAa,CAACZ,OAAO,CAAC;EACpD,MAAMa,UAAU,GAAGF,UAAU,CAACG,aAAa,CAAC;IAACN,IAAI;IAAEE;EAAG,CAAC,CAAC;EAKxDV,OAAO,GAAGe,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACjB,OAAO,CAAC,CAAC;EAC7CM,OAAO,GAAGS,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACX,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;EAEnD,MAAMY,GAAG,GAAG,MAAML,UAAU,CAACM,QAAQ,CACnC,mBAAmB,EAEnBC,SAAS,CAACC,IAAI,CAAC,IAAI,EAAEd,iBAAiB,CACxC,CAAC;EAEDW,GAAG,CAACI,WAAW,CAAC,SAAS,EAAE;IAEzBC,KAAK,EAAElB,IAAI;IACXL,OAAO;IACPM;EACF,CAAC,CAAC;EAEF,MAAMkB,MAAM,GAAG,MAAMN,GAAG,CAACM,MAAM;EAE/B,OAAO,MAAMA,MAAM,CAACA,MAAM;AAC5B;AAQA,eAAeJ,SAASA,CACtBb,iBAA8F,EAC9FW,GAAc,EACdO,IAAuB,EACvBC,OAA6B,EAC7B;EACA,QAAQD,IAAI;IACV,KAAK,MAAM;MACTP,GAAG,CAACS,IAAI,CAACD,OAAO,CAAC;MACjB;IAEF,KAAK,OAAO;MACVR,GAAG,CAACU,KAAK,CAAC,IAAIC,KAAK,CAACH,OAAO,CAACE,KAAK,CAAC,CAAC;MACnC;IAEF,KAAK,SAAS;MAEZ,MAAM;QAACnB,EAAE;QAAEc,KAAK;QAAEvB;MAAO,CAAC,GAAG0B,OAAO;MACpC,IAAI;QACF,MAAMF,MAAM,GAAG,MAAMjB,iBAAiB,CAACgB,KAAK,EAAEvB,OAAO,CAAC;QACtDkB,GAAG,CAACI,WAAW,CAAC,MAAM,EAAE;UAACb,EAAE;UAAEe;QAAM,CAAC,CAAC;MACvC,CAAC,CAAC,OAAOI,KAAK,EAAE;QACd,MAAME,OAAO,GAAGF,KAAK,YAAYC,KAAK,GAAGD,KAAK,CAACE,OAAO,GAAG,eAAe;QACxEZ,GAAG,CAACI,WAAW,CAAC,OAAO,EAAE;UAACb,EAAE;UAAEmB,KAAK,EAAEE;QAAO,CAAC,CAAC;MAChD;MACA;IAEF;MAEEC,OAAO,CAACC,IAAI,sCAAAC,MAAA,CAAsCR,IAAI,CAAE,CAAC;EAC7D;AACF"}