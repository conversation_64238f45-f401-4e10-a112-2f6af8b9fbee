{"version": 3, "file": "async-iteration.js", "names": ["concatenateArrayBuffers", "for<PERSON>ach", "iterator", "visitor", "done", "value", "next", "return", "cancel", "concatenateArrayBuffersAsync", "asyncIterator", "arrayBuffers", "chunk", "push", "concatenateStringsAsync", "strings", "join"], "sources": ["../../../../src/lib/iterators/async-iteration.ts"], "sourcesContent": ["import {concatenateArrayBuffers} from '../binary-utils/array-buffer-utils';\n\n// GENERAL UTILITIES\n\n/**\n * Iterate over async iterator, without resetting iterator if end is not reached\n * - for<PERSON><PERSON> intentionally does not reset iterator if exiting loop prematurely\n *   so that iteration can continue in a second loop\n * - It is recommended to use a standard for-await as last loop to ensure\n *   iterator gets properly reset\n *\n * TODO - optimize using sync iteration if argument is an Iterable?\n *\n * @param iterator\n * @param visitor\n */\nexport async function forEach(iterator, visitor) {\n  // eslint-disable-next-line\n  while (true) {\n    const {done, value} = await iterator.next();\n    if (done) {\n      iterator.return();\n      return;\n    }\n    const cancel = visitor(value);\n    if (cancel) {\n      return;\n    }\n  }\n}\n\n// Breaking big data into iterable chunks, concatenating iterable chunks into big data objects\n\n/**\n * Concatenates all data chunks yielded by an (async) iterator\n * This function can e.g. be used to enable atomic parsers to work on (async) iterator inputs\n */\n\nexport async function concatenateArrayBuffersAsync(\n  asyncIterator: AsyncIterable<ArrayBuffer> | Iterable<ArrayBuffer>\n): Promise<ArrayBuffer> {\n  const arrayBuffers: ArrayBuffer[] = [];\n  for await (const chunk of asyncIterator) {\n    arrayBuffers.push(chunk);\n  }\n  return concatenateArrayBuffers(...arrayBuffers);\n}\n\nexport async function concatenateStringsAsync(\n  asyncIterator: AsyncIterable<string> | Iterable<string>\n): Promise<string> {\n  const strings: string[] = [];\n  for await (const chunk of asyncIterator) {\n    strings.push(chunk);\n  }\n  return strings.join('');\n}\n"], "mappings": "AAAA,SAAQA,uBAAuB,QAAO,oCAAoC;AAgB1E,OAAO,eAAeC,OAAOA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EAE/C,OAAO,IAAI,EAAE;IACX,MAAM;MAACC,IAAI;MAAEC;IAAK,CAAC,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;IAC3C,IAAIF,IAAI,EAAE;MACRF,QAAQ,CAACK,MAAM,CAAC,CAAC;MACjB;IACF;IACA,MAAMC,MAAM,GAAGL,OAAO,CAACE,KAAK,CAAC;IAC7B,IAAIG,MAAM,EAAE;MACV;IACF;EACF;AACF;AASA,OAAO,eAAeC,4BAA4BA,CAChDC,aAAiE,EAC3C;EACtB,MAAMC,YAA2B,GAAG,EAAE;EACtC,WAAW,MAAMC,KAAK,IAAIF,aAAa,EAAE;IACvCC,YAAY,CAACE,IAAI,CAACD,KAAK,CAAC;EAC1B;EACA,OAAOZ,uBAAuB,CAAC,GAAGW,YAAY,CAAC;AACjD;AAEA,OAAO,eAAeG,uBAAuBA,CAC3CJ,aAAuD,EACtC;EACjB,MAAMK,OAAiB,GAAG,EAAE;EAC5B,WAAW,MAAMH,KAAK,IAAIF,aAAa,EAAE;IACvCK,OAAO,CAACF,IAAI,CAACD,KAAK,CAAC;EACrB;EACA,OAAOG,OAAO,CAACC,IAAI,CAAC,EAAE,CAAC;AACzB"}