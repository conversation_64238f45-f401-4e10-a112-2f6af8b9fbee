{"version": 3, "file": "fs.js", "names": ["fs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "promisify2", "promisify3", "readdir", "stat", "readFile", "readFileSync", "writeFile", "writeFileSync", "open", "close", "fd", "Promise", "resolve", "reject", "err", "read", "fstat", "createWriteStream", "isSupported", "Boolean", "_readToArrayBuffer", "start", "length", "buffer", "<PERSON><PERSON><PERSON>", "alloc", "bytesRead", "Error"], "sources": ["../../../../src/lib/node/fs.ts"], "sourcesContent": ["// fs wrapper (promisified fs + avoids bundling fs in browsers)\nimport fs from 'fs';\nimport {to<PERSON><PERSON>yBuffer} from './buffer';\nimport {promisify2, promisify3} from './promisify';\n\nexport type {Stats, WriteStream} from 'fs';\n\n/** Wrapper for Node.js fs method */\nexport const readdir: any = promisify2(fs.readdir);\n/** Wrapper for Node.js fs method */\nexport const stat: any = promisify2(fs.stat);\n\n/** Wrapper for Node.js fs method */\nexport const readFile: any = fs.readFile;\n/** Wrapper for Node.js fs method */\nexport const readFileSync = fs.readFileSync;\n/** Wrapper for Node.js fs method */\nexport const writeFile: any = promisify3(fs.writeFile);\n/** Wrapper for Node.js fs method */\nexport const writeFileSync = fs.writeFileSync;\n\n// file descriptors\n\n/** Wrapper for Node.js fs method */\nexport const open: any = fs.open;\n/** Wrapper for Node.js fs method */\nexport const close = (fd: number) =>\n  new Promise<void>((resolve, reject) => fs.close(fd, (err) => (err ? reject(err) : resolve())));\n/** Wrapper for Node.js fs method */\nexport const read: any = fs.read;\n/** Wrapper for Node.js fs method */\nexport const fstat: any = fs.fstat;\n\nexport const createWriteStream = fs.createWriteStream;\n\nexport const isSupported = Boolean(fs);\n\nexport async function _readToArrayBuffer(fd: number, start: number, length: number) {\n  const buffer = Buffer.alloc(length);\n  const {bytesRead} = await read(fd, buffer, 0, length, start);\n  if (bytesRead !== length) {\n    throw new Error('fs.read failed');\n  }\n  return toArrayBuffer(buffer);\n}\n"], "mappings": "AACA,OAAOA,EAAE,MAAM,IAAI;AACnB,SAAQC,aAAa,QAAO,UAAU;AACtC,SAAQC,UAAU,EAAEC,UAAU,QAAO,aAAa;AAKlD,OAAO,MAAMC,OAAY,GAAGF,UAAU,CAACF,EAAE,CAACI,OAAO,CAAC;AAElD,OAAO,MAAMC,IAAS,GAAGH,UAAU,CAACF,EAAE,CAACK,IAAI,CAAC;AAG5C,OAAO,MAAMC,QAAa,GAAGN,EAAE,CAACM,QAAQ;AAExC,OAAO,MAAMC,YAAY,GAAGP,EAAE,CAACO,YAAY;AAE3C,OAAO,MAAMC,SAAc,GAAGL,UAAU,CAACH,EAAE,CAACQ,SAAS,CAAC;AAEtD,OAAO,MAAMC,aAAa,GAAGT,EAAE,CAACS,aAAa;AAK7C,OAAO,MAAMC,IAAS,GAAGV,EAAE,CAACU,IAAI;AAEhC,OAAO,MAAMC,KAAK,GAAIC,EAAU,IAC9B,IAAIC,OAAO,CAAO,CAACC,OAAO,EAAEC,MAAM,KAAKf,EAAE,CAACW,KAAK,CAACC,EAAE,EAAGI,GAAG,IAAMA,GAAG,GAAGD,MAAM,CAACC,GAAG,CAAC,GAAGF,OAAO,CAAC,CAAE,CAAC,CAAC;AAEhG,OAAO,MAAMG,IAAS,GAAGjB,EAAE,CAACiB,IAAI;AAEhC,OAAO,MAAMC,KAAU,GAAGlB,EAAE,CAACkB,KAAK;AAElC,OAAO,MAAMC,iBAAiB,GAAGnB,EAAE,CAACmB,iBAAiB;AAErD,OAAO,MAAMC,WAAW,GAAGC,OAAO,CAACrB,EAAE,CAAC;AAEtC,OAAO,eAAesB,kBAAkBA,CAACV,EAAU,EAAEW,KAAa,EAAEC,MAAc,EAAE;EAClF,MAAMC,MAAM,GAAGC,MAAM,CAACC,KAAK,CAACH,MAAM,CAAC;EACnC,MAAM;IAACI;EAAS,CAAC,GAAG,MAAMX,IAAI,CAACL,EAAE,EAAEa,MAAM,EAAE,CAAC,EAAED,MAAM,EAAED,KAAK,CAAC;EAC5D,IAAIK,SAAS,KAAKJ,MAAM,EAAE;IACxB,MAAM,IAAIK,KAAK,CAAC,gBAAgB,CAAC;EACnC;EACA,OAAO5B,aAAa,CAACwB,MAAM,CAAC;AAC9B"}