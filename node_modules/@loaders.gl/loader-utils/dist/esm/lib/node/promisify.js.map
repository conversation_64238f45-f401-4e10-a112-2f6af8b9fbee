{"version": 3, "file": "promisify.js", "names": ["promisify1", "fn", "args", "Promise", "resolve", "reject", "error", "callback<PERSON><PERSON><PERSON>", "promisify2", "arg1", "arg2", "promisify3", "arg3"], "sources": ["../../../../src/lib/node/promisify.ts"], "sourcesContent": ["// @loaders.gl, MIT license\n\n// type Parameter1<T extends (arg1: any, ...args: unknown[]) => unknown> = T extends (\n//   arg1: infer P,\n//   ...args: unknown[]\n// ) => unknown\n//   ? P\n//   : never;\n\n//   type Parameter2<T extends (arg1: unknown, arg2: any, ...args: unknown[]) => unknown> = T extends (\n//   arg1: unknown,\n//   arg2: infer P,\n//   ...args: unknown[]\n// ) => unknown\n//   ? P\n//   : never;\n\n// type CallbackParameter2<\n//   T extends (arg: unknown, cb: (error: any, value: any) => void) => unknown\n// > = T extends (arg: unknown, cb: (error: any, value: infer P) => void) => unknown ? P : never;\n\n// type CallbackParameter3<\n//   T extends (arg: unknown, arg2: unknown, cb: (error: any, value: any) => void) => unknown\n// > = T extends (arg: unknown, arg2: unknown, cb: (error: any, value: infer P) => void) => unknown\n//   ? P\n//   : never;\n\n// /** Extract the parameters of a function type in a tuple */\n// export type Promisified<F extends (arg1, cb: (error: unknown, value: any) => void) => any> = (\n//   arg1: Parameter1<F>\n// ) => Promise<CallbackParameter2<F>>;\n// /** Extract the parameters of a function type in a tuple */\n// export type Promisified2<F extends (arg1, arg2, cb: (error: unknown, value: any) => void) => any> = (\n//   arg1: Parameter1<F>,\n//   arg2: Parameter2<F>\n// ) => Promise<CallbackParameter3<F>>;\n\n/** Wrapper for Node.js promisify */\ntype Callback<A> = (error: unknown, args: A) => void;\n\n/**\n * Typesafe promisify implementation\n * @link https://dev.to/_gdelgado/implement-a-type-safe-version-of-node-s-promisify-in-7-lines-of-code-in-typescript-2j34\n * @param fn\n * @returns\n */\nexport function promisify1<T, A>(fn: (args: T, cb: Callback<A>) => void): (args: T) => Promise<A> {\n  return (args: T) =>\n    new Promise((resolve, reject) =>\n      fn(args, (error, callbackArgs) => (error ? reject(error) : resolve(callbackArgs)))\n    );\n}\n\nexport function promisify2<T1, T2, A>(\n  fn: (arg1: T1, arg2: T2, cb: Callback<A>) => void\n): (arg1: T1, arg2: T2) => Promise<A> {\n  return (arg1: T1, arg2: T2) =>\n    new Promise((resolve, reject) =>\n      fn(arg1, arg2, (error, callbackArgs) => (error ? reject(error) : resolve(callbackArgs)))\n    );\n}\n\nexport function promisify3<T1, T2, T3, A>(\n  fn: (arg1: T1, arg2: T2, arg3: T3, cb: Callback<A>) => void\n): (arg1: T1, arg2: T2, arg3: T3) => Promise<A> {\n  return (arg1: T1, arg2: T2, arg3: T3) =>\n    new Promise((resolve, reject) =>\n      fn(arg1, arg2, arg3, (error, callbackArgs) => (error ? reject(error) : resolve(callbackArgs)))\n    );\n}\n"], "mappings": "AA8CA,OAAO,SAASA,UAAUA,CAAOC,EAAsC,EAA2B;EAChG,OAAQC,IAAO,IACb,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAC1BJ,EAAE,CAACC,IAAI,EAAE,CAACI,KAAK,EAAEC,YAAY,KAAMD,KAAK,GAAGD,MAAM,CAACC,KAAK,CAAC,GAAGF,OAAO,CAACG,YAAY,CAAE,CACnF,CAAC;AACL;AAEA,OAAO,SAASC,UAAUA,CACxBP,EAAiD,EACb;EACpC,OAAO,CAACQ,IAAQ,EAAEC,IAAQ,KACxB,IAAIP,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAC1BJ,EAAE,CAACQ,IAAI,EAAEC,IAAI,EAAE,CAACJ,KAAK,EAAEC,YAAY,KAAMD,KAAK,GAAGD,MAAM,CAACC,KAAK,CAAC,GAAGF,OAAO,CAACG,YAAY,CAAE,CACzF,CAAC;AACL;AAEA,OAAO,SAASI,UAAUA,CACxBV,EAA2D,EACb;EAC9C,OAAO,CAACQ,IAAQ,EAAEC,IAAQ,EAAEE,IAAQ,KAClC,IAAIT,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAC1BJ,EAAE,CAACQ,IAAI,EAAEC,IAAI,EAAEE,IAAI,EAAE,CAACN,KAAK,EAAEC,YAAY,KAAMD,KAAK,GAAGD,MAAM,CAACC,KAAK,CAAC,GAAGF,OAAO,CAACG,YAAY,CAAE,CAC/F,CAAC;AACL"}