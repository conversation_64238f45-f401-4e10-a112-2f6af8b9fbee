{"version": 3, "file": "stream.js", "names": ["stream", "Transform", "isSupported", "Boolean"], "sources": ["../../../../src/lib/node/stream.ts"], "sourcesContent": ["// loaders.gl, MIT license\n\nimport stream from 'stream';\n\nexport type {Writable} from 'stream';\n\n/** Wrapper for Node.js stream method */\nexport const Transform = stream.Transform;\n\nexport const isSupported = Boolean(stream);\n"], "mappings": "AAEA,OAAOA,MAAM,MAAM,QAAQ;AAK3B,OAAO,MAAMC,SAAS,GAAGD,MAAM,CAACC,SAAS;AAEzC,OAAO,MAAMC,WAAW,GAAGC,OAAO,CAACH,MAAM,CAAC"}