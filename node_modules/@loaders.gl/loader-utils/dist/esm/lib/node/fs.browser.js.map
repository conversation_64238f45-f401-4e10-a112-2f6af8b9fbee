{"version": 3, "file": "fs.browser.js", "names": ["readdir", "stat", "readFile", "readFileSync", "writeFile", "writeFileSync", "open", "close", "read", "fstat", "createWriteStream", "_readToArrayBuffer", "isSupported"], "sources": ["../../../../src/lib/node/fs.browser.ts"], "sourcesContent": ["export const readdir = null;\nexport const stat = null;\nexport const readFile = null;\nexport const readFileSync = null;\nexport const writeFile = null;\nexport const writeFileSync = null;\nexport const open = null;\nexport const close = null;\nexport const read = null;\nexport const fstat = null;\nexport const createWriteStream = null;\nexport const _readToArrayBuffer = null;\n\nexport const isSupported = false;\n"], "mappings": "AAAA,OAAO,MAAMA,OAAO,GAAG,IAAI;AAC3B,OAAO,MAAMC,IAAI,GAAG,IAAI;AACxB,OAAO,MAAMC,QAAQ,GAAG,IAAI;AAC5B,OAAO,MAAMC,YAAY,GAAG,IAAI;AAChC,OAAO,MAAMC,SAAS,GAAG,IAAI;AAC7B,OAAO,MAAMC,aAAa,GAAG,IAAI;AACjC,OAAO,MAAMC,IAAI,GAAG,IAAI;AACxB,OAAO,MAAMC,KAAK,GAAG,IAAI;AACzB,OAAO,MAAMC,IAAI,GAAG,IAAI;AACxB,OAAO,MAAMC,KAAK,GAAG,IAAI;AACzB,OAAO,MAAMC,iBAAiB,GAAG,IAAI;AACrC,OAAO,MAAMC,kBAAkB,GAAG,IAAI;AAEtC,OAAO,MAAMC,WAAW,GAAG,KAAK"}