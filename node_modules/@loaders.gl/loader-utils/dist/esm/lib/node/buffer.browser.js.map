{"version": 3, "file": "buffer.browser.js", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "buffer", "<PERSON><PERSON><PERSON><PERSON>", "binaryData", "Error"], "sources": ["../../../../src/lib/node/buffer.browser.ts"], "sourcesContent": ["// loaders.gl, MIT license\n\n// Isolates Buffer references to ensure they are only bundled under Node.js (avoids big webpack polyfill)\n// this file is selected by the package.json \"browser\" field).\n\n/**\n * Convert Buffer to ArrayBuffer\n * Converts Node.js `Buffer` to `<PERSON>rrayBuffer` (without triggering bundler to include <PERSON><PERSON><PERSON> polyfill on browser)\n * @todo better data type\n */\nexport function toArrayBuffer(buffer) {\n  return buffer;\n}\n\n/**\n * Convert (copy) ArrayBuffer to Buffer\n */\nexport function toBuffer(binaryData: ArrayBuffer | ArrayBuffer | Buffer): Buffer {\n  throw new Error('Buffer not supported in browser');\n}\n"], "mappings": "AAUA,OAAO,SAASA,aAAaA,CAACC,MAAM,EAAE;EACpC,OAAOA,MAAM;AACf;AAKA,OAAO,SAASC,QAAQA,CAACC,UAA8C,EAAU;EAC/E,MAAM,IAAIC,KAAK,CAAC,iCAAiC,CAAC;AACpD"}