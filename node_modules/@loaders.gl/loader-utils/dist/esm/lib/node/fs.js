import fs from 'fs';
import { toArrayBuffer } from './buffer';
import { promisify2, promisify3 } from './promisify';
export const readdir = promisify2(fs.readdir);
export const stat = promisify2(fs.stat);
export const readFile = fs.readFile;
export const readFileSync = fs.readFileSync;
export const writeFile = promisify3(fs.writeFile);
export const writeFileSync = fs.writeFileSync;
export const open = fs.open;
export const close = fd => new Promise((resolve, reject) => fs.close(fd, err => err ? reject(err) : resolve()));
export const read = fs.read;
export const fstat = fs.fstat;
export const createWriteStream = fs.createWriteStream;
export const isSupported = Boolean(fs);
export async function _readToArrayBuffer(fd, start, length) {
  const buffer = Buffer.alloc(length);
  const {
    bytesRead
  } = await read(fd, buffer, 0, length, start);
  if (bytesRead !== length) {
    throw new Error('fs.read failed');
  }
  return toArrayBuffer(buffer);
}
//# sourceMappingURL=fs.js.map