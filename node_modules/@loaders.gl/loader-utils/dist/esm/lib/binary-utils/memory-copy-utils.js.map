{"version": 3, "file": "memory-copy-utils.js", "names": ["assert", "padToNBytes", "byteLength", "padding", "copyArrayBuffer", "targetBuffer", "sourceBuffer", "byteOffset", "arguments", "length", "undefined", "targetArray", "Uint8Array", "sourceArray", "set", "copyToArray", "source", "target", "targetOffset", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "srcByteOffset", "srcByteLength", "buffer", "arrayBuffer"], "sources": ["../../../../src/lib/binary-utils/memory-copy-utils.ts"], "sourcesContent": ["import {assert} from '../env-utils/assert';\n\n/**\n * Calculate new size of an arrayBuffer to be aligned to an n-byte boundary\n * This function increases `byteLength` by the minimum delta,\n * allowing the total length to be divided by `padding`\n * @param byteLength\n * @param padding\n */\nexport function padToNBytes(byteLength: number, padding: number): number {\n  assert(byteLength >= 0); // `Incorrect 'byteLength' value: ${byteLength}`\n  assert(padding > 0); // `Incorrect 'padding' value: ${padding}`\n  return (byteLength + (padding - 1)) & ~(padding - 1);\n}\n\n/**\n * Creates a new Uint8Array based on two different ArrayBuffers\n * @param targetBuffer The first buffer.\n * @param sourceBuffer The second buffer.\n * @return The new ArrayBuffer created out of the two.\n */\nexport function copyArrayBuffer(\n  targetBuffer: ArrayBuffer,\n  sourceBuffer: ArrayBuffer,\n  byteOffset: number,\n  byteLength: number = sourceBuffer.byteLength\n): ArrayBuffer {\n  const targetArray = new Uint8Array(targetBuffer, byteOffset, byteLength);\n  const sourceArray = new Uint8Array(sourceBuffer);\n  targetArray.set(sourceArray);\n  return targetBuffer;\n}\n\n/**\n * Copy from source to target at the targetOffset\n *\n * @param source - The data to copy\n * @param target - The destination to copy data into\n * @param targetOffset - The start offset into target to place the copied data\n * @returns the new offset taking into account proper padding\n */\nexport function copyToArray(source: ArrayBuffer | any, target: any, targetOffset: number): number {\n  let sourceArray;\n\n  if (source instanceof ArrayBuffer) {\n    sourceArray = new Uint8Array(source);\n  } else {\n    // Pack buffer onto the big target array\n    //\n    // 'source.data.buffer' could be a view onto a larger buffer.\n    // We MUST use this constructor to ensure the byteOffset and byteLength is\n    // set to correct values from 'source.data' and not the underlying\n    // buffer for target.set() to work properly.\n    const srcByteOffset = source.byteOffset;\n    const srcByteLength = source.byteLength;\n    // In gltf parser it is set as \"arrayBuffer\" instead of \"buffer\"\n    // https://github.com/visgl/loaders.gl/blob/1e3a82a0a65d7b6a67b1e60633453e5edda2960a/modules/gltf/src/lib/parse-gltf.js#L85\n    sourceArray = new Uint8Array(source.buffer || source.arrayBuffer, srcByteOffset, srcByteLength);\n  }\n\n  // Pack buffer onto the big target array\n  target.set(sourceArray, targetOffset);\n\n  return targetOffset + padToNBytes(sourceArray.byteLength, 4);\n}\n"], "mappings": "AAAA,SAAQA,MAAM,QAAO,qBAAqB;AAS1C,OAAO,SAASC,WAAWA,CAACC,UAAkB,EAAEC,OAAe,EAAU;EACvEH,MAAM,CAACE,UAAU,IAAI,CAAC,CAAC;EACvBF,MAAM,CAACG,OAAO,GAAG,CAAC,CAAC;EACnB,OAAQD,UAAU,IAAIC,OAAO,GAAG,CAAC,CAAC,GAAI,EAAEA,OAAO,GAAG,CAAC,CAAC;AACtD;AAQA,OAAO,SAASC,eAAeA,CAC7BC,YAAyB,EACzBC,YAAyB,EACzBC,UAAkB,EAEL;EAAA,IADbL,UAAkB,GAAAM,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGF,YAAY,CAACJ,UAAU;EAE5C,MAAMS,WAAW,GAAG,IAAIC,UAAU,CAACP,YAAY,EAAEE,UAAU,EAAEL,UAAU,CAAC;EACxE,MAAMW,WAAW,GAAG,IAAID,UAAU,CAACN,YAAY,CAAC;EAChDK,WAAW,CAACG,GAAG,CAACD,WAAW,CAAC;EAC5B,OAAOR,YAAY;AACrB;AAUA,OAAO,SAASU,WAAWA,CAACC,MAAyB,EAAEC,MAAW,EAAEC,YAAoB,EAAU;EAChG,IAAIL,WAAW;EAEf,IAAIG,MAAM,YAAYG,WAAW,EAAE;IACjCN,WAAW,GAAG,IAAID,UAAU,CAACI,MAAM,CAAC;EACtC,CAAC,MAAM;IAOL,MAAMI,aAAa,GAAGJ,MAAM,CAACT,UAAU;IACvC,MAAMc,aAAa,GAAGL,MAAM,CAACd,UAAU;IAGvCW,WAAW,GAAG,IAAID,UAAU,CAACI,MAAM,CAACM,MAAM,IAAIN,MAAM,CAACO,WAAW,EAAEH,aAAa,EAAEC,aAAa,CAAC;EACjG;EAGAJ,MAAM,CAACH,GAAG,CAACD,WAAW,EAAEK,YAAY,CAAC;EAErC,OAAOA,YAAY,GAAGjB,WAAW,CAACY,WAAW,CAACX,UAAU,EAAE,CAAC,CAAC;AAC9D"}