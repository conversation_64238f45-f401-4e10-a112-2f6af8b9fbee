{"version": 3, "file": "array-buffer-utils.js", "names": ["compareArrayBuffers", "arrayBuffer1", "arrayBuffer2", "byteLength", "array1", "Uint8Array", "array2", "i", "length", "concatenateArrayBuffers", "_len", "arguments", "sources", "Array", "_key", "sourceArrays", "map", "source2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reduce", "typedArray", "result", "offset", "sourceArray", "set", "buffer", "concatenateTypedArrays", "_len2", "typedArrays", "_key2", "arrays", "TypedArrayConstructor", "constructor", "Error", "sumLength", "acc", "value", "array", "sliceArrayBuffer", "arrayBuffer", "byteOffset", "subArray", "undefined", "subarray", "arrayCopy"], "sources": ["../../../../src/lib/binary-utils/array-buffer-utils.ts"], "sourcesContent": ["import {TypedArray} from '../../types';\n\n/**\n * compare two binary arrays for equality\n * @param a\n * @param b\n * @param byteLength\n */\nexport function compareArrayBuffers(\n  arrayBuffer1: ArrayBuffer,\n  arrayBuffer2: ArrayBuffer,\n  byteLength?: number\n): boolean {\n  byteLength = byteLength || arrayBuffer1.byteLength;\n  if (arrayBuffer1.byteLength < byteLength || arrayBuffer2.byteLength < byteLength) {\n    return false;\n  }\n  const array1 = new Uint8Array(arrayBuffer1);\n  const array2 = new Uint8Array(arrayBuffer2);\n  for (let i = 0; i < array1.length; ++i) {\n    if (array1[i] !== array2[i]) {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * Concatenate a sequence of ArrayBuffers\n * @return A concatenated ArrayBuffer\n */\nexport function concatenateArrayBuffers(...sources: (ArrayBuffer | Uint8Array)[]): ArrayBuffer {\n  // Make sure all inputs are wrapped in typed arrays\n  const sourceArrays = sources.map((source2) =>\n    source2 instanceof ArrayBuffer ? new Uint8Array(source2) : source2\n  );\n\n  // Get length of all inputs\n  const byteLength = sourceArrays.reduce((length, typedArray) => length + typedArray.byteLength, 0);\n\n  // Allocate array with space for all inputs\n  const result = new Uint8Array(byteLength);\n\n  // Copy the subarrays\n  let offset = 0;\n  for (const sourceArray of sourceArrays) {\n    result.set(sourceArray, offset);\n    offset += sourceArray.byteLength;\n  }\n\n  // We work with ArrayBuffers, discard the typed array wrapper\n  return result.buffer;\n}\n\n/**\n * Concatenate arbitrary count of typed arrays\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Typed_arrays\n * @param - list of arrays. All arrays should be the same type\n * @return A concatenated TypedArray\n */\nexport function concatenateTypedArrays<T>(...typedArrays: T[]): T {\n  // @ts-ignore\n  const arrays = typedArrays as TypedArray[];\n  // @ts-ignore\n  const TypedArrayConstructor = (arrays && arrays.length > 1 && arrays[0].constructor) || null;\n  if (!TypedArrayConstructor) {\n    throw new Error(\n      '\"concatenateTypedArrays\" - incorrect quantity of arguments or arguments have incompatible data types'\n    );\n  }\n\n  const sumLength = arrays.reduce((acc, value) => acc + value.length, 0);\n  // @ts-ignore typescript does not like dynamic constructors\n  const result = new TypedArrayConstructor(sumLength);\n  let offset = 0;\n  for (const array of arrays) {\n    result.set(array, offset);\n    offset += array.length;\n  }\n  return result;\n}\n\n/**\n * Copy a view of an ArrayBuffer into new ArrayBuffer with byteOffset = 0\n * @param arrayBuffer\n * @param byteOffset\n * @param byteLength\n */\nexport function sliceArrayBuffer(\n  arrayBuffer: ArrayBuffer,\n  byteOffset: number,\n  byteLength?: number\n): ArrayBuffer {\n  const subArray =\n    byteLength !== undefined\n      ? new Uint8Array(arrayBuffer).subarray(byteOffset, byteOffset + byteLength)\n      : new Uint8Array(arrayBuffer).subarray(byteOffset);\n  const arrayCopy = new Uint8Array(subArray);\n  return arrayCopy.buffer;\n}\n"], "mappings": "AAQA,OAAO,SAASA,mBAAmBA,CACjCC,YAAyB,EACzBC,YAAyB,EACzBC,UAAmB,EACV;EACTA,UAAU,GAAGA,UAAU,IAAIF,YAAY,CAACE,UAAU;EAClD,IAAIF,YAAY,CAACE,UAAU,GAAGA,UAAU,IAAID,YAAY,CAACC,UAAU,GAAGA,UAAU,EAAE;IAChF,OAAO,KAAK;EACd;EACA,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAACJ,YAAY,CAAC;EAC3C,MAAMK,MAAM,GAAG,IAAID,UAAU,CAACH,YAAY,CAAC;EAC3C,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,MAAM,EAAE,EAAED,CAAC,EAAE;IACtC,IAAIH,MAAM,CAACG,CAAC,CAAC,KAAKD,MAAM,CAACC,CAAC,CAAC,EAAE;MAC3B,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb;AAMA,OAAO,SAASE,uBAAuBA,CAAA,EAAwD;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAH,MAAA,EAApDI,OAAO,OAAAC,KAAA,CAAAH,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;IAAPF,OAAO,CAAAE,IAAA,IAAAH,SAAA,CAAAG,IAAA;EAAA;EAEhD,MAAMC,YAAY,GAAGH,OAAO,CAACI,GAAG,CAAEC,OAAO,IACvCA,OAAO,YAAYC,WAAW,GAAG,IAAIb,UAAU,CAACY,OAAO,CAAC,GAAGA,OAC7D,CAAC;EAGD,MAAMd,UAAU,GAAGY,YAAY,CAACI,MAAM,CAAC,CAACX,MAAM,EAAEY,UAAU,KAAKZ,MAAM,GAAGY,UAAU,CAACjB,UAAU,EAAE,CAAC,CAAC;EAGjG,MAAMkB,MAAM,GAAG,IAAIhB,UAAU,CAACF,UAAU,CAAC;EAGzC,IAAImB,MAAM,GAAG,CAAC;EACd,KAAK,MAAMC,WAAW,IAAIR,YAAY,EAAE;IACtCM,MAAM,CAACG,GAAG,CAACD,WAAW,EAAED,MAAM,CAAC;IAC/BA,MAAM,IAAIC,WAAW,CAACpB,UAAU;EAClC;EAGA,OAAOkB,MAAM,CAACI,MAAM;AACtB;AAQA,OAAO,SAASC,sBAAsBA,CAAA,EAA4B;EAAA,SAAAC,KAAA,GAAAhB,SAAA,CAAAH,MAAA,EAArBoB,WAAW,OAAAf,KAAA,CAAAc,KAAA,GAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;IAAXD,WAAW,CAAAC,KAAA,IAAAlB,SAAA,CAAAkB,KAAA;EAAA;EAEtD,MAAMC,MAAM,GAAGF,WAA2B;EAE1C,MAAMG,qBAAqB,GAAID,MAAM,IAAIA,MAAM,CAACtB,MAAM,GAAG,CAAC,IAAIsB,MAAM,CAAC,CAAC,CAAC,CAACE,WAAW,IAAK,IAAI;EAC5F,IAAI,CAACD,qBAAqB,EAAE;IAC1B,MAAM,IAAIE,KAAK,CACb,sGACF,CAAC;EACH;EAEA,MAAMC,SAAS,GAAGJ,MAAM,CAACX,MAAM,CAAC,CAACgB,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,CAAC5B,MAAM,EAAE,CAAC,CAAC;EAEtE,MAAMa,MAAM,GAAG,IAAIU,qBAAqB,CAACG,SAAS,CAAC;EACnD,IAAIZ,MAAM,GAAG,CAAC;EACd,KAAK,MAAMe,KAAK,IAAIP,MAAM,EAAE;IAC1BT,MAAM,CAACG,GAAG,CAACa,KAAK,EAAEf,MAAM,CAAC;IACzBA,MAAM,IAAIe,KAAK,CAAC7B,MAAM;EACxB;EACA,OAAOa,MAAM;AACf;AAQA,OAAO,SAASiB,gBAAgBA,CAC9BC,WAAwB,EACxBC,UAAkB,EAClBrC,UAAmB,EACN;EACb,MAAMsC,QAAQ,GACZtC,UAAU,KAAKuC,SAAS,GACpB,IAAIrC,UAAU,CAACkC,WAAW,CAAC,CAACI,QAAQ,CAACH,UAAU,EAAEA,UAAU,GAAGrC,UAAU,CAAC,GACzE,IAAIE,UAAU,CAACkC,WAAW,CAAC,CAACI,QAAQ,CAACH,UAAU,CAAC;EACtD,MAAMI,SAAS,GAAG,IAAIvC,UAAU,CAACoC,QAAQ,CAAC;EAC1C,OAAOG,SAAS,CAACnB,MAAM;AACzB"}