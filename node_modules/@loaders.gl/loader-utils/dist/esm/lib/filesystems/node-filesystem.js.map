{"version": 3, "file": "node-filesystem.js", "names": ["fs", "NodeFileSystem", "constructor", "options", "fetch", "_fetch", "readdir", "dirname", "arguments", "length", "undefined", "stat", "path", "info", "size", "Number", "isDirectory", "fallback<PERSON><PERSON><PERSON>", "open", "flags", "mode", "close", "fd", "fstat", "read", "_ref", "buffer", "offset", "byteLength", "position", "totalBytesRead", "bytesRead"], "sources": ["../../../../src/lib/filesystems/node-filesystem.ts"], "sourcesContent": ["import * as fs from '../node/fs';\nimport {IFileSystem, IRandomAccessReadFileSystem} from '../../types';\n// import {fetchFile} from \"../fetch/fetch-file\"\n// import {selectLoader} from \"../api/select-loader\";\n\ntype Stat = {\n  size: number;\n  isDirectory: () => boolean;\n  info?: fs.Stats;\n};\n\ntype ReadOptions = {\n  buffer?: Buffer;\n  offset?: number;\n  length?: number;\n  position?: number;\n};\n\n/**\n * FileSystem pass-through for Node.js\n * Compatible with BrowserFileSystem.\n * @param options\n */\nexport default class NodeFileSystem implements IFileSystem, IRandomAccessReadFileSystem {\n  // implements IFileSystem\n  constructor(options: {[key: string]: any}) {\n    this.fetch = options._fetch;\n  }\n\n  async readdir(dirname = '.', options?: {}): Promise<any[]> {\n    return await fs.readdir(dirname, options);\n  }\n\n  async stat(path: string, options?: {}): Promise<Stat> {\n    const info = await fs.stat(path, options);\n    return {size: Number(info.size), isDirectory: () => false, info};\n  }\n\n  async fetch(path: string, options: {[key: string]: any}) {\n    // Falls back to handle https:/http:/data: etc fetches\n    // eslint-disable-next-line\n    const fallbackFetch = options.fetch || this.fetch;\n    return fallbackFetch(path, options);\n  }\n\n  // implements IRandomAccessFileSystem\n  async open(path: string, flags: string | number, mode?: any): Promise<number> {\n    return await fs.open(path, flags);\n  }\n\n  async close(fd: number): Promise<void> {\n    return await fs.close(fd);\n  }\n\n  async fstat(fd: number): Promise<Stat> {\n    const info = await fs.fstat(fd);\n    return info;\n  }\n\n  async read(\n    fd: number,\n    // @ts-ignore Possibly null\n    {buffer = null, offset = 0, length = buffer.byteLength, position = null}: ReadOptions\n  ): Promise<{bytesRead: number; buffer: Buffer}> {\n    let totalBytesRead = 0;\n    // Read in loop until we get required number of bytes\n    while (totalBytesRead < length) {\n      const {bytesRead} = await fs.read(\n        fd,\n        buffer,\n        offset + totalBytesRead,\n        length - totalBytesRead,\n        position + totalBytesRead\n      );\n      totalBytesRead += bytesRead;\n    }\n    return {bytesRead: totalBytesRead, buffer};\n  }\n}\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,YAAY;AAuBhC,eAAe,MAAMC,cAAc,CAAqD;EAEtFC,WAAWA,CAACC,OAA6B,EAAE;IACzC,IAAI,CAACC,KAAK,GAAGD,OAAO,CAACE,MAAM;EAC7B;EAEA,MAAMC,OAAOA,CAAA,EAA8C;IAAA,IAA7CC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,GAAG;IAAA,IAAEL,OAAY,GAAAK,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;IACvC,OAAO,MAAMV,EAAE,CAACM,OAAO,CAACC,OAAO,EAAEJ,OAAO,CAAC;EAC3C;EAEA,MAAMQ,IAAIA,CAACC,IAAY,EAAET,OAAY,EAAiB;IACpD,MAAMU,IAAI,GAAG,MAAMb,EAAE,CAACW,IAAI,CAACC,IAAI,EAAET,OAAO,CAAC;IACzC,OAAO;MAACW,IAAI,EAAEC,MAAM,CAACF,IAAI,CAACC,IAAI,CAAC;MAAEE,WAAW,EAAEA,CAAA,KAAM,KAAK;MAAEH;IAAI,CAAC;EAClE;EAEA,MAAMT,KAAKA,CAACQ,IAAY,EAAET,OAA6B,EAAE;IAGvD,MAAMc,aAAa,GAAGd,OAAO,CAACC,KAAK,IAAI,IAAI,CAACA,KAAK;IACjD,OAAOa,aAAa,CAACL,IAAI,EAAET,OAAO,CAAC;EACrC;EAGA,MAAMe,IAAIA,CAACN,IAAY,EAAEO,KAAsB,EAAEC,IAAU,EAAmB;IAC5E,OAAO,MAAMpB,EAAE,CAACkB,IAAI,CAACN,IAAI,EAAEO,KAAK,CAAC;EACnC;EAEA,MAAME,KAAKA,CAACC,EAAU,EAAiB;IACrC,OAAO,MAAMtB,EAAE,CAACqB,KAAK,CAACC,EAAE,CAAC;EAC3B;EAEA,MAAMC,KAAKA,CAACD,EAAU,EAAiB;IACrC,MAAMT,IAAI,GAAG,MAAMb,EAAE,CAACuB,KAAK,CAACD,EAAE,CAAC;IAC/B,OAAOT,IAAI;EACb;EAEA,MAAMW,IAAIA,CACRF,EAAU,EAAAG,IAAA,EAGoC;IAAA,IAD9C;MAACC,MAAM,GAAG,IAAI;MAAEC,MAAM,GAAG,CAAC;MAAElB,MAAM,GAAGiB,MAAM,CAACE,UAAU;MAAEC,QAAQ,GAAG;IAAiB,CAAC,GAAAJ,IAAA;IAErF,IAAIK,cAAc,GAAG,CAAC;IAEtB,OAAOA,cAAc,GAAGrB,MAAM,EAAE;MAC9B,MAAM;QAACsB;MAAS,CAAC,GAAG,MAAM/B,EAAE,CAACwB,IAAI,CAC/BF,EAAE,EACFI,MAAM,EACNC,MAAM,GAAGG,cAAc,EACvBrB,MAAM,GAAGqB,cAAc,EACvBD,QAAQ,GAAGC,cACb,CAAC;MACDA,cAAc,IAAIC,SAAS;IAC7B;IACA,OAAO;MAACA,SAAS,EAAED,cAAc;MAAEJ;IAAM,CAAC;EAC5C;AACF"}