{"version": 3, "file": "writable-file.js", "names": ["<PERSON><PERSON><PERSON><PERSON>", "fs", "makeWritableFile", "pathOrStream", "options", "write", "close", "outputStream", "createWriteStream", "buffer", "Promise", "resolve", "reject", "err"], "sources": ["../../../../src/lib/filesystems/writable-file.ts"], "sourcesContent": ["// Forked from https://github.com/kbajalc/parquets under MIT license (Copyright (c) 2017 ironSource Ltd.)\nimport {isBrowser} from '../env-utils/globals';\nimport * as fs from '../node/fs';\nimport type {Writable} from 'stream';\n\nexport type WritableFile = {\n  write: (buf: Buffer) => Promise<void>;\n  close: () => Promise<void>;\n};\n\nexport interface WriteStreamOptions {\n  flags?: string;\n  encoding?: 'utf8';\n  fd?: number;\n  mode?: number;\n  autoClose?: boolean;\n  start?: number;\n}\n\n/** Helper function to create an envelope reader for a binary memory input */\nexport function makeWritableFile(\n  pathOrStream: string | Writable,\n  options?: WriteStreamOptions\n): WritableFile {\n  if (isBrowser) {\n    return {\n      write: async () => {},\n      close: async () => {}\n    };\n  }\n\n  const outputStream: Writable =\n    typeof pathOrStream === 'string' ? fs.createWriteStream(pathOrStream, options) : pathOrStream;\n  return {\n    write: async (buffer: Buffer) =>\n      new Promise((resolve, reject) => {\n        outputStream.write(buffer, (err) => (err ? reject(err) : resolve()));\n      }),\n    close: () =>\n      new Promise((resolve, reject) => {\n        (outputStream as any).close((err) => (err ? reject(err) : resolve()));\n      })\n  };\n}\n"], "mappings": "AACA,SAAQA,SAAS,QAAO,sBAAsB;AAC9C,OAAO,KAAKC,EAAE,MAAM,YAAY;AAkBhC,OAAO,SAASC,gBAAgBA,CAC9BC,YAA+B,EAC/BC,OAA4B,EACd;EACd,IAAIJ,SAAS,EAAE;IACb,OAAO;MACLK,KAAK,EAAE,MAAAA,CAAA,KAAY,CAAC,CAAC;MACrBC,KAAK,EAAE,MAAAA,CAAA,KAAY,CAAC;IACtB,CAAC;EACH;EAEA,MAAMC,YAAsB,GAC1B,OAAOJ,YAAY,KAAK,QAAQ,GAAGF,EAAE,CAACO,iBAAiB,CAACL,YAAY,EAAEC,OAAO,CAAC,GAAGD,YAAY;EAC/F,OAAO;IACLE,KAAK,EAAE,MAAOI,MAAc,IAC1B,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MAC/BL,YAAY,CAACF,KAAK,CAACI,MAAM,EAAGI,GAAG,IAAMA,GAAG,GAAGD,MAAM,CAACC,GAAG,CAAC,GAAGF,OAAO,CAAC,CAAE,CAAC;IACtE,CAAC,CAAC;IACJL,KAAK,EAAEA,CAAA,KACL,IAAII,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MAC9BL,YAAY,CAASD,KAAK,CAAEO,GAAG,IAAMA,GAAG,GAAGD,MAAM,CAACC,GAAG,CAAC,GAAGF,OAAO,CAAC,CAAE,CAAC;IACvE,CAAC;EACL,CAAC;AACH"}