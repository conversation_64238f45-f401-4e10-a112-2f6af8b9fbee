{"version": 3, "file": "path.js", "names": ["getCWD", "filename", "url", "slashIndex", "lastIndexOf", "substr", "dirname", "join", "_len", "arguments", "length", "parts", "Array", "_key", "separator", "map", "part", "index", "replace", "RegExp", "concat", "resolve", "paths", "_i", "undefined", "<PERSON><PERSON><PERSON>", "resolvedAbsolute", "cwd", "i", "path", "charCodeAt", "SLASH", "normalizeStringPosix", "DOT", "allowAboveRoot", "res", "lastSlash", "dots", "code", "isAboveRoot", "start", "j", "slice"], "sources": ["../../../../src/lib/path-utils/path.ts"], "sourcesContent": ["// Beginning of a minimal implementation of the Node.js path API, that doesn't pull in big polyfills.\n\nimport {getCWD} from './get-cwd';\n\n/**\n * Replacement for Node.js path.filename\n * @param url\n */\nexport function filename(url: string): string {\n  const slashIndex = url ? url.lastIndexOf('/') : -1;\n  return slashIndex >= 0 ? url.substr(slashIndex + 1) : '';\n}\n\n/**\n * Replacement for Node.js path.dirname\n * @param url\n */\nexport function dirname(url: string): string {\n  const slashIndex = url ? url.lastIndexOf('/') : -1;\n  return slashIndex >= 0 ? url.substr(0, slashIndex) : '';\n}\n\n/**\n * Replacement for Node.js path.join\n * @param parts\n */\nexport function join(...parts: string[]): string {\n  const separator = '/';\n  parts = parts.map((part, index) => {\n    if (index) {\n      part = part.replace(new RegExp(`^${separator}`), '');\n    }\n    if (index !== parts.length - 1) {\n      part = part.replace(new RegExp(`${separator}$`), '');\n    }\n    return part;\n  });\n  return parts.join(separator);\n}\n\n/* eslint-disable no-continue */\n\n/**\n * https://nodejs.org/api/path.html#path_path_resolve_paths\n * @param paths A sequence of paths or path segments.\n * @return resolved path\n * Forked from BTOdell/path-resolve under MIT license\n * @see https://github.com/BTOdell/path-resolve/blob/master/LICENSE\n */\nexport function resolve(...components: string[]): string {\n  const paths: string[] = [];\n  for (let _i = 0; _i < components.length; _i++) {\n    paths[_i] = components[_i];\n  }\n  let resolvedPath = '';\n  let resolvedAbsolute = false;\n  let cwd: string | undefined;\n  for (let i = paths.length - 1; i >= -1 && !resolvedAbsolute; i--) {\n    let path: string | undefined;\n    if (i >= 0) {\n      path = paths[i];\n    } else {\n      if (cwd === undefined) {\n        cwd = getCWD();\n      }\n      path = cwd;\n    }\n    // Skip empty entries\n    if (path.length === 0) {\n      continue;\n    }\n    resolvedPath = `${path}/${resolvedPath}`;\n    resolvedAbsolute = path.charCodeAt(0) === SLASH;\n  }\n  // At this point the path should be resolved to a full absolute path, but\n  // handle relative paths to be safe (might happen when process.cwd() fails)\n  // Normalize the path (removes leading slash)\n  resolvedPath = normalizeStringPosix(resolvedPath, !resolvedAbsolute);\n  if (resolvedAbsolute) {\n    return `/${resolvedPath}`;\n  } else if (resolvedPath.length > 0) {\n    return resolvedPath;\n  }\n  return '.';\n}\n\nconst SLASH = 47;\nconst DOT = 46;\n\n/**\n * Resolves . and .. elements in a path with directory names\n * Forked from BTOdell/path-resolve under MIT license\n * @see https://github.com/BTOdell/path-resolve/blob/master/LICENSE\n */\n/* eslint-disable max-depth */\n// eslint-disable-next-line complexity, max-statements\nfunction normalizeStringPosix(path: string, allowAboveRoot: boolean): string {\n  let res = '';\n  let lastSlash = -1;\n  let dots = 0;\n  let code: number | undefined;\n  let isAboveRoot = false;\n\n  for (let i = 0; i <= path.length; ++i) {\n    if (i < path.length) {\n      code = path.charCodeAt(i);\n    } else if (code === SLASH) {\n      break;\n    } else {\n      code = SLASH;\n    }\n    if (code === SLASH) {\n      if (lastSlash === i - 1 || dots === 1) {\n        // NOOP\n      } else if (lastSlash !== i - 1 && dots === 2) {\n        if (\n          res.length < 2 ||\n          !isAboveRoot ||\n          res.charCodeAt(res.length - 1) !== DOT ||\n          res.charCodeAt(res.length - 2) !== DOT\n        ) {\n          if (res.length > 2) {\n            const start = res.length - 1;\n            let j = start;\n            for (; j >= 0; --j) {\n              if (res.charCodeAt(j) === SLASH) {\n                break;\n              }\n            }\n            if (j !== start) {\n              res = j === -1 ? '' : res.slice(0, j);\n              lastSlash = i;\n              dots = 0;\n              isAboveRoot = false;\n              continue;\n            }\n          } else if (res.length === 2 || res.length === 1) {\n            res = '';\n            lastSlash = i;\n            dots = 0;\n            isAboveRoot = false;\n            continue;\n          }\n        }\n        if (allowAboveRoot) {\n          if (res.length > 0) {\n            res += '/..';\n          } else {\n            res = '..';\n          }\n          isAboveRoot = true;\n        }\n      } else {\n        const slice = path.slice(lastSlash + 1, i);\n        if (res.length > 0) {\n          res += `/${slice}`;\n        } else {\n          res = slice;\n        }\n        isAboveRoot = false;\n      }\n      lastSlash = i;\n      dots = 0;\n    } else if (code === DOT && dots !== -1) {\n      ++dots;\n    } else {\n      dots = -1;\n    }\n  }\n  return res;\n}\n"], "mappings": "AAEA,SAAQA,MAAM,QAAO,WAAW;AAMhC,OAAO,SAASC,QAAQA,CAACC,GAAW,EAAU;EAC5C,MAAMC,UAAU,GAAGD,GAAG,GAAGA,GAAG,CAACE,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAClD,OAAOD,UAAU,IAAI,CAAC,GAAGD,GAAG,CAACG,MAAM,CAACF,UAAU,GAAG,CAAC,CAAC,GAAG,EAAE;AAC1D;AAMA,OAAO,SAASG,OAAOA,CAACJ,GAAW,EAAU;EAC3C,MAAMC,UAAU,GAAGD,GAAG,GAAGA,GAAG,CAACE,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAClD,OAAOD,UAAU,IAAI,CAAC,GAAGD,GAAG,CAACG,MAAM,CAAC,CAAC,EAAEF,UAAU,CAAC,GAAG,EAAE;AACzD;AAMA,OAAO,SAASI,IAAIA,CAAA,EAA6B;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAzBC,KAAK,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAALF,KAAK,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EAC3B,MAAMC,SAAS,GAAG,GAAG;EACrBH,KAAK,GAAGA,KAAK,CAACI,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;IACjC,IAAIA,KAAK,EAAE;MACTD,IAAI,GAAGA,IAAI,CAACE,OAAO,CAAC,IAAIC,MAAM,KAAAC,MAAA,CAAKN,SAAS,CAAE,CAAC,EAAE,EAAE,CAAC;IACtD;IACA,IAAIG,KAAK,KAAKN,KAAK,CAACD,MAAM,GAAG,CAAC,EAAE;MAC9BM,IAAI,GAAGA,IAAI,CAACE,OAAO,CAAC,IAAIC,MAAM,IAAAC,MAAA,CAAIN,SAAS,MAAG,CAAC,EAAE,EAAE,CAAC;IACtD;IACA,OAAOE,IAAI;EACb,CAAC,CAAC;EACF,OAAOL,KAAK,CAACJ,IAAI,CAACO,SAAS,CAAC;AAC9B;AAWA,OAAO,SAASO,OAAOA,CAAA,EAAkC;EACvD,MAAMC,KAAe,GAAG,EAAE;EAC1B,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGd,SAAA,CAAWC,MAAM,EAAEa,EAAE,EAAE,EAAE;IAC7CD,KAAK,CAACC,EAAE,CAAC,GAAcA,EAAE,QAAAd,SAAA,CAAAC,MAAA,IAAFa,EAAE,GAAAC,SAAA,GAAAf,SAAA,CAAFc,EAAE,CAAC;EAC5B;EACA,IAAIE,YAAY,GAAG,EAAE;EACrB,IAAIC,gBAAgB,GAAG,KAAK;EAC5B,IAAIC,GAAuB;EAC3B,KAAK,IAAIC,CAAC,GAAGN,KAAK,CAACZ,MAAM,GAAG,CAAC,EAAEkB,CAAC,IAAI,CAAC,CAAC,IAAI,CAACF,gBAAgB,EAAEE,CAAC,EAAE,EAAE;IAChE,IAAIC,IAAwB;IAC5B,IAAID,CAAC,IAAI,CAAC,EAAE;MACVC,IAAI,GAAGP,KAAK,CAACM,CAAC,CAAC;IACjB,CAAC,MAAM;MACL,IAAID,GAAG,KAAKH,SAAS,EAAE;QACrBG,GAAG,GAAG3B,MAAM,CAAC,CAAC;MAChB;MACA6B,IAAI,GAAGF,GAAG;IACZ;IAEA,IAAIE,IAAI,CAACnB,MAAM,KAAK,CAAC,EAAE;MACrB;IACF;IACAe,YAAY,MAAAL,MAAA,CAAMS,IAAI,OAAAT,MAAA,CAAIK,YAAY,CAAE;IACxCC,gBAAgB,GAAGG,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC,KAAKC,KAAK;EACjD;EAIAN,YAAY,GAAGO,oBAAoB,CAACP,YAAY,EAAE,CAACC,gBAAgB,CAAC;EACpE,IAAIA,gBAAgB,EAAE;IACpB,WAAAN,MAAA,CAAWK,YAAY;EACzB,CAAC,MAAM,IAAIA,YAAY,CAACf,MAAM,GAAG,CAAC,EAAE;IAClC,OAAOe,YAAY;EACrB;EACA,OAAO,GAAG;AACZ;AAEA,MAAMM,KAAK,GAAG,EAAE;AAChB,MAAME,GAAG,GAAG,EAAE;AASd,SAASD,oBAAoBA,CAACH,IAAY,EAAEK,cAAuB,EAAU;EAC3E,IAAIC,GAAG,GAAG,EAAE;EACZ,IAAIC,SAAS,GAAG,CAAC,CAAC;EAClB,IAAIC,IAAI,GAAG,CAAC;EACZ,IAAIC,IAAwB;EAC5B,IAAIC,WAAW,GAAG,KAAK;EAEvB,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIC,IAAI,CAACnB,MAAM,EAAE,EAAEkB,CAAC,EAAE;IACrC,IAAIA,CAAC,GAAGC,IAAI,CAACnB,MAAM,EAAE;MACnB4B,IAAI,GAAGT,IAAI,CAACC,UAAU,CAACF,CAAC,CAAC;IAC3B,CAAC,MAAM,IAAIU,IAAI,KAAKP,KAAK,EAAE;MACzB;IACF,CAAC,MAAM;MACLO,IAAI,GAAGP,KAAK;IACd;IACA,IAAIO,IAAI,KAAKP,KAAK,EAAE;MAClB,IAAIK,SAAS,KAAKR,CAAC,GAAG,CAAC,IAAIS,IAAI,KAAK,CAAC,EAAE,CAEvC,CAAC,MAAM,IAAID,SAAS,KAAKR,CAAC,GAAG,CAAC,IAAIS,IAAI,KAAK,CAAC,EAAE;QAC5C,IACEF,GAAG,CAACzB,MAAM,GAAG,CAAC,IACd,CAAC6B,WAAW,IACZJ,GAAG,CAACL,UAAU,CAACK,GAAG,CAACzB,MAAM,GAAG,CAAC,CAAC,KAAKuB,GAAG,IACtCE,GAAG,CAACL,UAAU,CAACK,GAAG,CAACzB,MAAM,GAAG,CAAC,CAAC,KAAKuB,GAAG,EACtC;UACA,IAAIE,GAAG,CAACzB,MAAM,GAAG,CAAC,EAAE;YAClB,MAAM8B,KAAK,GAAGL,GAAG,CAACzB,MAAM,GAAG,CAAC;YAC5B,IAAI+B,CAAC,GAAGD,KAAK;YACb,OAAOC,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;cAClB,IAAIN,GAAG,CAACL,UAAU,CAACW,CAAC,CAAC,KAAKV,KAAK,EAAE;gBAC/B;cACF;YACF;YACA,IAAIU,CAAC,KAAKD,KAAK,EAAE;cACfL,GAAG,GAAGM,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,GAAGN,GAAG,CAACO,KAAK,CAAC,CAAC,EAAED,CAAC,CAAC;cACrCL,SAAS,GAAGR,CAAC;cACbS,IAAI,GAAG,CAAC;cACRE,WAAW,GAAG,KAAK;cACnB;YACF;UACF,CAAC,MAAM,IAAIJ,GAAG,CAACzB,MAAM,KAAK,CAAC,IAAIyB,GAAG,CAACzB,MAAM,KAAK,CAAC,EAAE;YAC/CyB,GAAG,GAAG,EAAE;YACRC,SAAS,GAAGR,CAAC;YACbS,IAAI,GAAG,CAAC;YACRE,WAAW,GAAG,KAAK;YACnB;UACF;QACF;QACA,IAAIL,cAAc,EAAE;UAClB,IAAIC,GAAG,CAACzB,MAAM,GAAG,CAAC,EAAE;YAClByB,GAAG,IAAI,KAAK;UACd,CAAC,MAAM;YACLA,GAAG,GAAG,IAAI;UACZ;UACAI,WAAW,GAAG,IAAI;QACpB;MACF,CAAC,MAAM;QACL,MAAMG,KAAK,GAAGb,IAAI,CAACa,KAAK,CAACN,SAAS,GAAG,CAAC,EAAER,CAAC,CAAC;QAC1C,IAAIO,GAAG,CAACzB,MAAM,GAAG,CAAC,EAAE;UAClByB,GAAG,QAAAf,MAAA,CAAQsB,KAAK,CAAE;QACpB,CAAC,MAAM;UACLP,GAAG,GAAGO,KAAK;QACb;QACAH,WAAW,GAAG,KAAK;MACrB;MACAH,SAAS,GAAGR,CAAC;MACbS,IAAI,GAAG,CAAC;IACV,CAAC,MAAM,IAAIC,IAAI,KAAKL,GAAG,IAAII,IAAI,KAAK,CAAC,CAAC,EAAE;MACtC,EAAEA,IAAI;IACR,CAAC,MAAM;MACLA,IAAI,GAAG,CAAC,CAAC;IACX;EACF;EACA,OAAOF,GAAG;AACZ"}