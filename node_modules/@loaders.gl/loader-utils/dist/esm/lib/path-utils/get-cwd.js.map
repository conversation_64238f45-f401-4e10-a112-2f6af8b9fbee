{"version": 3, "file": "get-cwd.js", "names": ["getCWD", "_window$location", "process", "cwd", "pathname", "window", "location", "slice", "lastIndexOf"], "sources": ["../../../../src/lib/path-utils/get-cwd.ts"], "sourcesContent": ["// loaders.gl MIT license\n\nexport function getCWD() {\n  if (typeof process !== 'undefined' && typeof process.cwd !== 'undefined') {\n    return process.cwd();\n  }\n  const pathname = window.location?.pathname;\n  return pathname?.slice(0, pathname.lastIndexOf('/') + 1) || '';\n}\n"], "mappings": "AAEA,OAAO,SAASA,MAAMA,CAAA,EAAG;EAAA,IAAAC,gBAAA;EACvB,IAAI,OAAOC,OAAO,KAAK,WAAW,IAAI,OAAOA,OAAO,CAACC,GAAG,KAAK,WAAW,EAAE;IACxE,OAAOD,OAAO,CAACC,GAAG,CAAC,CAAC;EACtB;EACA,MAAMC,QAAQ,IAAAH,gBAAA,GAAGI,MAAM,CAACC,QAAQ,cAAAL,gBAAA,uBAAfA,gBAAA,CAAiBG,QAAQ;EAC1C,OAAO,CAAAA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEG,KAAK,CAAC,CAAC,EAAEH,QAAQ,CAACI,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAI,EAAE;AAChE"}