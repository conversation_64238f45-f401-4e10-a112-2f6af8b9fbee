import type { LoaderWithParser } from './types';
/**
 * A JSON Micro loader (minimal bundle size)
 * Alternative to `@loaders.gl/json`
 */
export declare const JSONLoader: {
    name: string;
    id: string;
    module: string;
    version: any;
    extensions: string[];
    mimeTypes: string[];
    category: string;
    text: boolean;
    parseTextSync: typeof parseTextSync;
    parse: (arrayBuffer: any) => Promise<any>;
    options: {};
};
declare function parseTextSync(text: any): any;
export declare const _typecheckJSONLoader: LoaderWithParser;
export {};
//# sourceMappingURL=json-loader.d.ts.map