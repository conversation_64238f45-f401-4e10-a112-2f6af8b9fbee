{"version": 3, "file": "index.js", "names": ["_assert", "require", "_globals", "_mergeLoaderOptions", "_createLoaderWorker", "_parseWithWorker", "_encodeWithWorker", "_parseJson", "_arrayBufferUtils", "_memoryCopyUtils", "_dataviewCopyUtils", "_getFirstCharacters", "_textIterators", "_asyncIteration", "_requestScheduler", "_interopRequireDefault", "_fileAliases", "_j<PERSON><PERSON><PERSON><PERSON>", "_memoryConversionUtils", "_promisify", "path", "_interopRequireWildcard", "exports", "fs", "stream", "_readableFile", "_writableFile", "_nodeFilesystem", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set"], "sources": ["../../src/index.ts"], "sourcesContent": ["// TYPES\nexport type {\n  Lo<PERSON>,\n  LoaderWithParser,\n  LoaderContext,\n  LoaderOptions,\n  Writer,\n  WriterOptions,\n  DataType,\n  SyncDataType,\n  BatchableDataType,\n  IFileSystem,\n  IRandomAccessReadFileSystem\n} from './types';\n\n// GENERAL UTILS\nexport {assert} from './lib/env-utils/assert';\nexport {\n  isBrowser,\n  isWorker,\n  nodeVersion,\n  self,\n  window,\n  global,\n  document\n} from './lib/env-utils/globals';\n\nexport {mergeLoaderOptions} from './lib/option-utils/merge-loader-options';\n\n// LOADERS.GL-SPECIFIC WORKER UTILS\nexport {createLoaderWorker} from './lib/worker-loader-utils/create-loader-worker';\nexport {parseWithWorker, canParseWithWorker} from './lib/worker-loader-utils/parse-with-worker';\nexport {canEncodeWithWorker} from './lib/worker-loader-utils/encode-with-worker';\n\n// PARSER UTILS\nexport {parseJSON} from './lib/parser-utils/parse-json';\n\n// MEMORY COPY UTILS\nexport {\n  sliceArrayBuffer,\n  concatenateArrayBuffers,\n  concatenateTypedArrays,\n  compareArrayBuffers\n} from './lib/binary-utils/array-buffer-utils';\nexport {padToNBytes, copyToArray, copyArrayBuffer} from './lib/binary-utils/memory-copy-utils';\nexport {\n  padStringToByteAlignment,\n  copyStringToDataView,\n  copyBinaryToDataView,\n  copyPaddedArrayBufferToDataView,\n  copyPaddedStringToDataView\n} from './lib/binary-utils/dataview-copy-utils';\nexport {getFirstCharacters, getMagicString} from './lib/binary-utils/get-first-characters';\n\n// ITERATOR UTILS\nexport {\n  makeTextEncoderIterator,\n  makeTextDecoderIterator,\n  makeLineIterator,\n  makeNumberedLineIterator\n} from './lib/iterators/text-iterators';\nexport {forEach, concatenateArrayBuffersAsync} from './lib/iterators/async-iteration';\n\n// REQUEST UTILS\nexport {default as RequestScheduler} from './lib/request-utils/request-scheduler';\n\n// PATH HELPERS\nexport {setPathPrefix, getPathPrefix, resolvePath} from './lib/path-utils/file-aliases';\nexport {addAliases as _addAliases} from './lib/path-utils/file-aliases';\n\n// MICRO LOADERS\nexport {JSONLoader} from './json-loader';\n\n// NODE support\n\n// Node.js emulation (can be used in browser)\n\n// Avoid direct use of `Buffer` which pulls in 50KB polyfill\nexport {isBuffer, toBuffer, toArrayBuffer} from './lib/binary-utils/memory-conversion-utils';\n\n// Note.js wrappers (can be safely imported, but not used in browser)\n\n// Use instead of importing 'util' to avoid node dependencies\nexport {promisify1, promisify2} from './lib/node/promisify';\n\n// `path` replacement (avoids bundling big path polyfill)\nimport * as path from './lib/path-utils/path';\nexport {path};\n\n// Use instead of importing 'fs' to avoid node dependencies`\nimport * as fs from './lib/node/fs';\nexport {fs};\n\n// Use instead of importing 'stream' to avoid node dependencies`\nimport * as stream from './lib/node/stream';\nexport {stream};\n\n// EXPERIMENTAL\nexport type {ReadableFile} from './lib/filesystems/readable-file';\nexport {makeReadableFile} from './lib/filesystems/readable-file';\n\nexport type {WritableFile} from './lib/filesystems/writable-file';\nexport {makeWritableFile} from './lib/filesystems/writable-file';\n\nexport {default as _NodeFileSystem} from './lib/filesystems/node-filesystem';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AAUA,IAAAE,mBAAA,GAAAF,OAAA;AAGA,IAAAG,mBAAA,GAAAH,OAAA;AACA,IAAAI,gBAAA,GAAAJ,OAAA;AACA,IAAAK,iBAAA,GAAAL,OAAA;AAGA,IAAAM,UAAA,GAAAN,OAAA;AAGA,IAAAO,iBAAA,GAAAP,OAAA;AAMA,IAAAQ,gBAAA,GAAAR,OAAA;AACA,IAAAS,kBAAA,GAAAT,OAAA;AAOA,IAAAU,mBAAA,GAAAV,OAAA;AAGA,IAAAW,cAAA,GAAAX,OAAA;AAMA,IAAAY,eAAA,GAAAZ,OAAA;AAGA,IAAAa,iBAAA,GAAAC,sBAAA,CAAAd,OAAA;AAGA,IAAAe,YAAA,GAAAf,OAAA;AAIA,IAAAgB,WAAA,GAAAhB,OAAA;AAOA,IAAAiB,sBAAA,GAAAjB,OAAA;AAKA,IAAAkB,UAAA,GAAAlB,OAAA;AAGA,IAAAmB,IAAA,GAAAC,uBAAA,CAAApB,OAAA;AAA8CqB,OAAA,CAAAF,IAAA,GAAAA,IAAA;AAI9C,IAAAG,EAAA,GAAAF,uBAAA,CAAApB,OAAA;AAAoCqB,OAAA,CAAAC,EAAA,GAAAA,EAAA;AAIpC,IAAAC,MAAA,GAAAH,uBAAA,CAAApB,OAAA;AAA4CqB,OAAA,CAAAE,MAAA,GAAAA,MAAA;AAK5C,IAAAC,aAAA,GAAAxB,OAAA;AAGA,IAAAyB,aAAA,GAAAzB,OAAA;AAEA,IAAA0B,eAAA,GAAAZ,sBAAA,CAAAd,OAAA;AAA6E,SAAA2B,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAR,wBAAAY,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA"}