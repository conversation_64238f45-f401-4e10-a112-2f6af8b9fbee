"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports._typecheckJSONLoader = exports.JSONLoader = void 0;
const VERSION = typeof "3.4.15" !== 'undefined' ? "3.4.15" : 'latest';
const J<PERSON>NLoader = {
  name: '<PERSON><PERSON><PERSON>',
  id: 'json',
  module: 'json',
  version: VERSION,
  extensions: ['json', 'geojson'],
  mimeTypes: ['application/json'],
  category: 'json',
  text: true,
  parseTextSync,
  parse: async arrayBuffer => parseTextSync(new TextDecoder().decode(arrayBuffer)),
  options: {}
};
exports.JSONLoader = JSONLoader;
function parseTextSync(text) {
  return JSON.parse(text);
}
const _typecheckJSONLoader = JSONLoader;
exports._typecheckJSONLoader = _typecheckJSONLoader;
//# sourceMappingURL=json-loader.js.map