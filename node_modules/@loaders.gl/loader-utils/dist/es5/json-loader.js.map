{"version": 3, "file": "json-loader.js", "names": ["VERSION", "JSONLoader", "name", "id", "module", "version", "extensions", "mimeTypes", "category", "text", "parseTextSync", "parse", "arrayBuffer", "TextDecoder", "decode", "options", "exports", "JSON", "_typecheckJSONLoader"], "sources": ["../../src/json-loader.ts"], "sourcesContent": ["import type {LoaderWithParser} from './types';\n\n// __VERSION__ is injected by babel-plugin-version-inline\n// @ts-ignore TS2304: Cannot find name '__VERSION__'.\nconst VERSION = typeof __VERSION__ !== 'undefined' ? __VERSION__ : 'latest';\n\n/**\n * A JSON Micro loader (minimal bundle size)\n * Alternative to `@loaders.gl/json`\n */\nexport const JSONLoader = {\n  name: 'J<PERSON><PERSON>',\n  id: 'json',\n  module: 'json',\n  version: VERSION,\n  extensions: ['json', 'geojson'],\n  mimeTypes: ['application/json'],\n  category: 'json',\n  text: true,\n  parseTextSync,\n  parse: async (arrayBuffer) => parseTextSync(new TextDecoder().decode(arrayBuffer)),\n  options: {}\n};\n\n// TODO - deprecated\nfunction parseTextSync(text) {\n  return JSON.parse(text);\n}\n\nexport const _typecheckJSONLoader: LoaderWithParser = JSONLoader;\n"], "mappings": ";;;;;;AAIA,MAAMA,OAAO,GAAG,eAAkB,KAAK,WAAW,cAAiB,QAAQ;AAMpE,MAAMC,UAAU,GAAG;EACxBC,IAAI,EAAE,MAAM;EACZC,EAAE,EAAE,MAAM;EACVC,MAAM,EAAE,MAAM;EACdC,OAAO,EAAEL,OAAO;EAChBM,UAAU,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;EAC/BC,SAAS,EAAE,CAAC,kBAAkB,CAAC;EAC/BC,QAAQ,EAAE,MAAM;EAChBC,IAAI,EAAE,IAAI;EACVC,aAAa;EACbC,KAAK,EAAE,MAAOC,WAAW,IAAKF,aAAa,CAAC,IAAIG,WAAW,CAAC,CAAC,CAACC,MAAM,CAACF,WAAW,CAAC,CAAC;EAClFG,OAAO,EAAE,CAAC;AACZ,CAAC;AAACC,OAAA,CAAAf,UAAA,GAAAA,UAAA;AAGF,SAASS,aAAaA,CAACD,IAAI,EAAE;EAC3B,OAAOQ,IAAI,CAACN,KAAK,CAACF,IAAI,CAAC;AACzB;AAEO,MAAMS,oBAAsC,GAAGjB,UAAU;AAACe,OAAA,CAAAE,oBAAA,GAAAA,oBAAA"}