{"version": 3, "file": "json-worker.js", "names": ["_createLoaderWorker", "require", "_j<PERSON><PERSON><PERSON><PERSON>", "createLoaderWorker", "JSONLoader"], "sources": ["../../../src/workers/json-worker.ts"], "sourcesContent": ["import {createLoaderWorker} from '../lib/worker-loader-utils/create-loader-worker';\nimport {JSONLoader} from '../json-loader';\n\ncreateLoaderWorker(JSONLoader);\n"], "mappings": ";;AAAA,IAAAA,mBAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AAEA,IAAAE,sCAAkB,EAACC,sBAAU,CAAC"}