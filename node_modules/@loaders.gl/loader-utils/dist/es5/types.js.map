{"version": 3, "file": "types.js", "names": [], "sources": ["../../src/types.ts"], "sourcesContent": ["// Typed arrays\n\nexport type TypedIntArray =\n  | Int8Array\n  | Uint8Array\n  | Uint8ClampedArray\n  | Int16Array\n  | Uint16Array\n  | Int32Array\n  | Uint32Array\n  | Int32Array\n  | Uint32Array;\n\nexport type TypedFloatArray = Uint16Array | Float32Array | Float64Array;\n\nexport type TypedArray = TypedIntArray | TypedFloatArray;\n\nexport type NumericArray = Array<number> | TypedIntArray | TypedFloatArray;\n\ntype FetchLike = (url: string, options?: RequestInit) => Promise<Response>;\n\n/**\n * Core Loader Options\n */\nexport type LoaderOptions = {\n  /** fetch options or a custom fetch function */\n  fetch?: typeof fetch | FetchLike | RequestInit | null;\n  /** Do not throw on errors */\n  nothrow?: boolean;\n\n  /** loader selection, search first for supplied mimeType */\n  mimeType?: string;\n  /** loader selection, provide fallback mimeType is server does not provide */\n  fallbackMimeType?: string;\n  /** loader selection, avoid searching registered loaders */\n  ignoreRegisteredLoaders?: boolean;\n\n  // general\n  /** Experimental: Supply a logger to the parser */\n  log?: any;\n\n  // batched parsing\n\n  /** Size of each batch. `auto` matches batches to size of incoming chunks */\n  batchSize?: number | 'auto';\n  /** Minimal amount of time between batches */\n  batchDebounceMs?: number;\n  /** Stop loading after a given number of rows (compare SQL limit clause) */\n  limit?: 0;\n  /** Experimental: Stop loading after reaching */\n  _limitMB?: 0;\n  /** Generate metadata batches */\n  metadata?: boolean;\n  /** Transforms to run on incoming batches */\n  transforms?: TransformBatches[];\n\n  // workers\n\n  /** CDN load workers from */\n  CDN?: string | null;\n  /** Set to `false` to disable workers */\n  worker?: boolean;\n  /** Number of concurrent workers (per loader) on desktop browser */\n  maxConcurrency?: number;\n  /** Number of concurrent workers (per loader) on mobile browsers */\n  maxMobileConcurrency?: number;\n  /** Set to `false` to prevent reuse workers */\n  reuseWorkers?: boolean;\n  /** Whether to use workers under Node.js (experimental) */\n  _nodeWorkers?: boolean;\n  /** set to 'test' to run local worker */\n  _workerType?: string;\n\n  /** @deprecated `options.batchType` removed, Use `options.<loader>.type` instead */\n  batchType?: 'row' | 'columnar' | 'arrow';\n  /** @deprecated `options.throw removed`, Use `options.nothrow` instead */\n  throws?: boolean;\n  /** @deprecated `options.dataType` no longer used */\n  dataType?: never;\n  /** @deprecated `options.uri` no longer used */\n  uri?: never;\n  /** @deprecated `options.method` removed. Use `options.fetch.method` */\n  method?: never;\n  /** @deprecated `options.headers` removed. Use `options.fetch.headers` */\n  headers?: never;\n  /** @deprecated `options.body` removed. Use `options.fetch.body` */\n  body?: never;\n  /** @deprecated `options.mode` removed. Use `options.fetch.mode` */\n  mode?: never;\n  /** @deprecated `options.credentials` removed. Use `options.fetch.credentials` */\n  credentials?: never;\n  /** @deprecated `options.cache` removed. Use `options.fetch.cache` */\n  cache?: never;\n  /** @deprecated `options.redirect` removed. Use `options.fetch.redirect` */\n  redirect?: never;\n  /** @deprecated `options.referrer` removed. Use `options.fetch.referrer` */\n  referrer?: never;\n  /** @deprecated `options.referrerPolicy` removed. Use `options.fetch.referrerPolicy` */\n  referrerPolicy?: never;\n  /** @deprecated `options.integrity` removed. Use `options.fetch.integrity` */\n  integrity?: never;\n  /** @deprecated `options.keepalive` removed. Use `options.fetch.keepalive` */\n  keepalive?: never;\n  /** @deprecated `options.signal` removed. Use `options.fetch.signal` */\n  signal?: never;\n\n  // Accept other keys (loader options objects, e.g. `options.csv`, `options.json` ...)\n  [loaderId: string]: unknown;\n};\n\ntype PreloadOptions = {\n  [key: string]: unknown;\n};\n\n/**\n * A worker loader definition that can be used with `@loaders.gl/core` functions\n */\nexport type Loader = {\n  // Worker\n  name: string;\n  /** id should be the same as the field used in LoaderOptions */\n  id: string;\n  /** module is used to generate worker threads, need to be the module directory name */\n  module: string;\n  /** Version should be injected by build tools */\n  version: string;\n  /** A boolean, or a URL */\n  worker?: string | boolean;\n  /** Default Options */\n  options: LoaderOptions;\n  /** Deprecated Options */\n  deprecatedOptions?: object;\n\n  /** Which category does this loader belong to */\n  category?: string;\n  /** What extensions does this loader generate */\n  extensions: string[];\n  mimeTypes: string[];\n\n  binary?: boolean;\n  text?: boolean;\n\n  tests?: (((ArrayBuffer: ArrayBuffer) => boolean) | ArrayBuffer | string)[];\n\n  // TODO - deprecated\n  supported?: boolean;\n  testText?: (string: string) => boolean;\n};\n\n/**\n * A \"bundled\" loader definition that can be used with `@loaders.gl/core` functions\n * If a worker loader is supported it will also be supported.\n */\nexport type LoaderWithParser = Loader & {\n  // TODO - deprecated\n  testText?: (string: string) => boolean;\n\n  parse: Parse;\n  preload?: Preload;\n  parseSync?: ParseSync;\n  parseText?: ParseText;\n  parseTextSync?: ParseTextSync;\n  parseInBatches?: ParseInBatches;\n  parseFileInBatches?: ParseFileInBatches;\n};\n\n/** Options for writers */\nexport type WriterOptions = {\n  /** worker source. If is set will be used instead of loading worker from the Internet */\n  souce?: string | null;\n  /** writer-specific options */\n  [writerId: string]: any;\n};\n\n/**\n * A writer definition that can be used with `@loaders.gl/core` functions\n */\nexport type Writer = {\n  name: string;\n\n  id: string;\n  module: string;\n  version: string;\n  worker?: string | boolean;\n\n  options: WriterOptions;\n  deprecatedOptions?: object;\n\n  // TODO - are these are needed?\n  binary?: boolean;\n  extensions?: string[];\n  mimeTypes?: string[];\n  text?: boolean;\n\n  encode?: Encode;\n  encodeSync?: EncodeSync;\n  encodeInBatches?: EncodeInBatches;\n  encodeURLtoURL?: EncodeURLtoURL;\n  encodeText?: EncodeText;\n};\n\n/**\n * A Loader context is provided as a third parameters to a loader object's\n * parse functions when that loader is called by other loaders rather then\n * directly by the application.\n *\n * - The context object allows the subloaders to be aware of the parameters and\n *   options that the application provided in the top level call.\n * - The context also providedsaccess to parse functions so that the subloader\n *   does not need to include the core module.\n * - In addition, the context's parse functions may also redirect loads from worker\n *   threads back to main thread.\n */\nexport type LoaderContext = {\n  loaders?: Loader[] | null;\n  /** If URL is available.  */\n  url?: string;\n  /** the file name component of the URL (leading path and query string removed) */\n  filename?: string;\n  /** the directory name component of the URL (leading path excluding file name and query string) */\n  baseUrl?: string;\n  /** Query string (characters after `?`) */\n  queryString?: string;\n\n  /** Provides access to any application overrides of fetch() */\n  fetch: typeof fetch | FetchLike;\n  /** TBD */\n  response?: Response;\n  /** Parse function. Use instead of importing `core`. In workers, may redirect to main thread */\n  parse: (\n    arrayBuffer: ArrayBuffer,\n    loaders?: Loader | Loader[] | LoaderOptions,\n    options?: LoaderOptions,\n    context?: LoaderContext\n  ) => Promise<any>;\n  /** ParseSync function. Use instead of importing `core`. In workers, may redirect to main thread */\n  parseSync?: (\n    arrayBuffer: ArrayBuffer,\n    loaders?: Loader | Loader[] | LoaderOptions,\n    options?: LoaderOptions,\n    context?: LoaderContext\n  ) => any;\n  /** ParseInBatches function. Use instead of importing `core`.  */\n  parseInBatches?: (\n    iterator: AsyncIterable<ArrayBuffer> | Iterable<ArrayBuffer>,\n    loaders?: Loader | Loader[] | LoaderOptions,\n    options?: LoaderOptions,\n    context?: LoaderContext\n  ) => AsyncIterable<any> | Promise<AsyncIterable<any>>;\n};\n\ntype Parse = (\n  arrayBuffer: ArrayBuffer,\n  options?: LoaderOptions,\n  context?: LoaderContext\n) => Promise<any>;\ntype ParseSync = (\n  arrayBuffer: ArrayBuffer,\n  options?: LoaderOptions,\n  context?: LoaderContext\n) => any;\ntype ParseText = (text: string, options?: LoaderOptions) => Promise<any>;\ntype ParseTextSync = (text: string, options?: LoaderOptions) => any;\ntype ParseInBatches = (\n  iterator: AsyncIterable<ArrayBuffer> | Iterable<ArrayBuffer>,\n  options?: LoaderOptions,\n  context?: LoaderContext\n) => AsyncIterable<any>;\ntype ParseFileInBatches = (\n  file: Blob,\n  options?: LoaderOptions,\n  context?: LoaderContext\n) => AsyncIterable<any>;\n\ntype Encode = (data: any, options?: WriterOptions) => Promise<ArrayBuffer>;\ntype EncodeSync = (data: any, options?: WriterOptions) => ArrayBuffer;\n// TODO\ntype EncodeText = Function;\ntype EncodeInBatches = Function;\ntype EncodeURLtoURL = (\n  inputUrl: string,\n  outputUrl: string,\n  options?: WriterOptions\n) => Promise<string>;\ntype Preload = (url: string, options?: PreloadOptions) => any;\n\nexport type TransformBatches = (\n  asyncIterator: AsyncIterable<ArrayBuffer> | Iterable<ArrayBuffer>\n) => AsyncIterable<ArrayBuffer>;\n\n/** Types that can be synchronously parsed */\nexport type SyncDataType = string | ArrayBuffer; // TODO File | Blob can be read synchronously...\n\n/** Types that can be parsed async */\nexport type DataType =\n  | string\n  | ArrayBuffer\n  | File\n  | Blob\n  | Response\n  | ReadableStream\n  | Iterable<ArrayBuffer>\n  | AsyncIterable<ArrayBuffer>;\n\n/** Types that can be parsed in batches */\nexport type BatchableDataType =\n  | DataType\n  | Iterable<ArrayBuffer>\n  | AsyncIterable<ArrayBuffer>\n  | Promise<AsyncIterable<ArrayBuffer>>;\n\n/**\n * A FileSystem interface can encapsulate a FileList, a ZipFile, a GoogleDrive etc.\n */\nexport interface IFileSystem {\n  /**\n   * Return a list of file names\n   * @param dirname directory name. file system root directory if omitted\n   */\n  readdir(dirname?: string, options?: {recursive?: boolean}): Promise<string[]>;\n\n  /**\n   * Gets information from a local file from the filesystem\n   * @param filename file name to stat\n   * @param options currently unused\n   * @throws if filename is not in local filesystem\n   */\n  stat(filename: string, options?: object): Promise<{size: number}>;\n\n  /**\n   * Fetches a local file from the filesystem (or a URL)\n   * @param filename\n   * @param options\n   */\n  fetch(filename: string, options?: object): Promise<Response>;\n}\n\ntype ReadOptions = {buffer?: ArrayBuffer; offset?: number; length?: number; position?: number};\nexport interface IRandomAccessReadFileSystem extends IFileSystem {\n  open(path: string, flags: string | number, mode?: any): Promise<any>;\n  close(fd: any): Promise<void>;\n  fstat(fd: any): Promise<object>;\n  read(fd: any, options?: ReadOptions): Promise<{bytesRead: number; buffer: Buffer}>;\n}\n"], "mappings": ""}