{"version": 3, "file": "globals.js", "names": ["globals", "self", "window", "global", "document", "self_", "exports", "window_", "global_", "document_", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "process", "String", "browser", "isWorker", "importScripts", "matches", "version", "exec", "nodeVersion", "parseFloat"], "sources": ["../../../../src/lib/env-utils/globals.ts"], "sourcesContent": ["// Purpose: include this in your module to avoid\n// dependencies on micro modules like 'global' and 'is-browser';\n\n/* eslint-disable no-restricted-globals */\nconst globals = {\n  self: typeof self !== 'undefined' && self,\n  window: typeof window !== 'undefined' && window,\n  global: typeof global !== 'undefined' && global,\n  document: typeof document !== 'undefined' && document\n};\n\ntype obj = {[key: string]: any};\nconst self_: obj = globals.self || globals.window || globals.global || {};\nconst window_: obj = globals.window || globals.self || globals.global || {};\nconst global_: obj = globals.global || globals.self || globals.window || {};\nconst document_: obj = globals.document || {};\n\nexport {self_ as self, window_ as window, global_ as global, document_ as document};\n\n/** true if running in a browser */\nexport const isBrowser: boolean =\n  // @ts-ignore process does not exist on browser\n  Boolean(typeof process !== 'object' || String(process) !== '[object process]' || process.browser);\n\n/** true if running in a worker thread */\nexport const isWorker: boolean = typeof importScripts === 'function';\n\n// Extract node major version\nconst matches =\n  typeof process !== 'undefined' && process.version && /v([0-9]*)/.exec(process.version);\n/** Major Node version (as a number) */\nexport const nodeVersion: number = (matches && parseFloat(matches[1])) || 0;\n"], "mappings": ";;;;;;AAIA,MAAMA,OAAO,GAAG;EACdC,IAAI,EAAE,OAAOA,IAAI,KAAK,WAAW,IAAIA,IAAI;EACzCC,MAAM,EAAE,OAAOA,MAAM,KAAK,WAAW,IAAIA,MAAM;EAC/CC,MAAM,EAAE,OAAOA,MAAM,KAAK,WAAW,IAAIA,MAAM;EAC/CC,QAAQ,EAAE,OAAOA,QAAQ,KAAK,WAAW,IAAIA;AAC/C,CAAC;AAGD,MAAMC,KAAU,GAAGL,OAAO,CAACC,IAAI,IAAID,OAAO,CAACE,MAAM,IAAIF,OAAO,CAACG,MAAM,IAAI,CAAC,CAAC;AAACG,OAAA,CAAAL,IAAA,GAAAI,KAAA;AAC1E,MAAME,OAAY,GAAGP,OAAO,CAACE,MAAM,IAAIF,OAAO,CAACC,IAAI,IAAID,OAAO,CAACG,MAAM,IAAI,CAAC,CAAC;AAACG,OAAA,CAAAJ,MAAA,GAAAK,OAAA;AAC5E,MAAMC,OAAY,GAAGR,OAAO,CAACG,MAAM,IAAIH,OAAO,CAACC,IAAI,IAAID,OAAO,CAACE,MAAM,IAAI,CAAC,CAAC;AAACI,OAAA,CAAAH,MAAA,GAAAK,OAAA;AAC5E,MAAMC,SAAc,GAAGT,OAAO,CAACI,QAAQ,IAAI,CAAC,CAAC;AAACE,OAAA,CAAAF,QAAA,GAAAK,SAAA;AAKvC,MAAMC,SAAkB,GAE7BC,OAAO,CAAC,OAAOC,OAAO,KAAK,QAAQ,IAAIC,MAAM,CAACD,OAAO,CAAC,KAAK,kBAAkB,IAAIA,OAAO,CAACE,OAAO,CAAC;AAACR,OAAA,CAAAI,SAAA,GAAAA,SAAA;AAG7F,MAAMK,QAAiB,GAAG,OAAOC,aAAa,KAAK,UAAU;AAACV,OAAA,CAAAS,QAAA,GAAAA,QAAA;AAGrE,MAAME,OAAO,GACX,OAAOL,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACM,OAAO,IAAI,WAAW,CAACC,IAAI,CAACP,OAAO,CAACM,OAAO,CAAC;AAEjF,MAAME,WAAmB,GAAIH,OAAO,IAAII,UAAU,CAACJ,OAAO,CAAC,CAAC,CAAC,CAAC,IAAK,CAAC;AAACX,OAAA,CAAAc,WAAA,GAAAA,WAAA"}