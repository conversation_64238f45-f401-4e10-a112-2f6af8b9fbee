"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.window = exports.self = exports.nodeVersion = exports.isWorker = exports.isBrowser = exports.global = exports.document = void 0;
const globals = {
  self: typeof self !== 'undefined' && self,
  window: typeof window !== 'undefined' && window,
  global: typeof global !== 'undefined' && global,
  document: typeof document !== 'undefined' && document
};
const self_ = globals.self || globals.window || globals.global || {};
exports.self = self_;
const window_ = globals.window || globals.self || globals.global || {};
exports.window = window_;
const global_ = globals.global || globals.self || globals.window || {};
exports.global = global_;
const document_ = globals.document || {};
exports.document = document_;
const isBrowser = Boolean(typeof process !== 'object' || String(process) !== '[object process]' || process.browser);
exports.isBrowser = isBrowser;
const isWorker = typeof importScripts === 'function';
exports.isWorker = isWorker;
const matches = typeof process !== 'undefined' && process.version && /v([0-9]*)/.exec(process.version);
const nodeVersion = matches && parseFloat(matches[1]) || 0;
exports.nodeVersion = nodeVersion;
//# sourceMappingURL=globals.js.map