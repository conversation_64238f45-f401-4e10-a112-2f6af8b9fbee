{"version": 3, "file": "assert.js", "names": ["assert", "condition", "message", "Error"], "sources": ["../../../../src/lib/env-utils/assert.ts"], "sourcesContent": ["/**\n * Throws an `Error` with the optional `message` if `condition` is falsy\n * @note Replacement for the external assert method to reduce bundle size\n */\nexport function assert(condition: any, message?: string): void {\n  if (!condition) {\n    throw new Error(message || 'loader assertion failed.');\n  }\n}\n"], "mappings": ";;;;;;AAIO,SAASA,MAAMA,CAACC,SAAc,EAAEC,OAAgB,EAAQ;EAC7D,IAAI,CAACD,SAAS,EAAE;IACd,MAAM,IAAIE,KAAK,CAACD,OAAO,IAAI,0BAA0B,CAAC;EACxD;AACF"}