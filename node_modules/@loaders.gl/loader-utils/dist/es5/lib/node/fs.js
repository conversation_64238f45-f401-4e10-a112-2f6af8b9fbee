"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports._readToArrayBuffer = _readToArrayBuffer;
exports.writeFileSync = exports.writeFile = exports.stat = exports.readdir = exports.readFileSync = exports.readFile = exports.read = exports.open = exports.isSupported = exports.fstat = exports.createWriteStream = exports.close = void 0;
var _fs = _interopRequireDefault(require("fs"));
var _buffer = require("./buffer");
var _promisify = require("./promisify");
const readdir = (0, _promisify.promisify2)(_fs.default.readdir);
exports.readdir = readdir;
const stat = (0, _promisify.promisify2)(_fs.default.stat);
exports.stat = stat;
const readFile = _fs.default.readFile;
exports.readFile = readFile;
const readFileSync = _fs.default.readFileSync;
exports.readFileSync = readFileSync;
const writeFile = (0, _promisify.promisify3)(_fs.default.writeFile);
exports.writeFile = writeFile;
const writeFileSync = _fs.default.writeFileSync;
exports.writeFileSync = writeFileSync;
const open = _fs.default.open;
exports.open = open;
const close = fd => new Promise((resolve, reject) => _fs.default.close(fd, err => err ? reject(err) : resolve()));
exports.close = close;
const read = _fs.default.read;
exports.read = read;
const fstat = _fs.default.fstat;
exports.fstat = fstat;
const createWriteStream = _fs.default.createWriteStream;
exports.createWriteStream = createWriteStream;
const isSupported = Boolean(_fs.default);
exports.isSupported = isSupported;
async function _readToArrayBuffer(fd, start, length) {
  const buffer = Buffer.alloc(length);
  const {
    bytesRead
  } = await read(fd, buffer, 0, length, start);
  if (bytesRead !== length) {
    throw new Error('fs.read failed');
  }
  return (0, _buffer.toArrayBuffer)(buffer);
}
//# sourceMappingURL=fs.js.map