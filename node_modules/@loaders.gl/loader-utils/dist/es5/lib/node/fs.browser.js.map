{"version": 3, "file": "fs.browser.js", "names": ["readdir", "exports", "stat", "readFile", "readFileSync", "writeFile", "writeFileSync", "open", "close", "read", "fstat", "createWriteStream", "_readToArrayBuffer", "isSupported"], "sources": ["../../../../src/lib/node/fs.browser.ts"], "sourcesContent": ["export const readdir = null;\nexport const stat = null;\nexport const readFile = null;\nexport const readFileSync = null;\nexport const writeFile = null;\nexport const writeFileSync = null;\nexport const open = null;\nexport const close = null;\nexport const read = null;\nexport const fstat = null;\nexport const createWriteStream = null;\nexport const _readToArrayBuffer = null;\n\nexport const isSupported = false;\n"], "mappings": ";;;;;;AAAO,MAAMA,OAAO,GAAG,IAAI;AAACC,OAAA,CAAAD,OAAA,GAAAA,OAAA;AACrB,MAAME,IAAI,GAAG,IAAI;AAACD,OAAA,CAAAC,IAAA,GAAAA,IAAA;AAClB,MAAMC,QAAQ,GAAG,IAAI;AAACF,OAAA,CAAAE,QAAA,GAAAA,QAAA;AACtB,MAAMC,YAAY,GAAG,IAAI;AAACH,OAAA,CAAAG,YAAA,GAAAA,YAAA;AAC1B,MAAMC,SAAS,GAAG,IAAI;AAACJ,OAAA,CAAAI,SAAA,GAAAA,SAAA;AACvB,MAAMC,aAAa,GAAG,IAAI;AAACL,OAAA,CAAAK,aAAA,GAAAA,aAAA;AAC3B,MAAMC,IAAI,GAAG,IAAI;AAACN,OAAA,CAAAM,IAAA,GAAAA,IAAA;AAClB,MAAMC,KAAK,GAAG,IAAI;AAACP,OAAA,CAAAO,KAAA,GAAAA,KAAA;AACnB,MAAMC,IAAI,GAAG,IAAI;AAACR,OAAA,CAAAQ,IAAA,GAAAA,IAAA;AAClB,MAAMC,KAAK,GAAG,IAAI;AAACT,OAAA,CAAAS,KAAA,GAAAA,KAAA;AACnB,MAAMC,iBAAiB,GAAG,IAAI;AAACV,OAAA,CAAAU,iBAAA,GAAAA,iBAAA;AAC/B,MAAMC,kBAAkB,GAAG,IAAI;AAACX,OAAA,CAAAW,kBAAA,GAAAA,kBAAA;AAEhC,MAAMC,WAAW,GAAG,KAAK;AAACZ,OAAA,CAAAY,WAAA,GAAAA,WAAA"}