"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.writeFileSync = exports.writeFile = exports.stat = exports.readdir = exports.readFileSync = exports.readFile = exports.read = exports.open = exports.isSupported = exports.fstat = exports.createWriteStream = exports.close = exports._readToArrayBuffer = void 0;
const readdir = null;
exports.readdir = readdir;
const stat = null;
exports.stat = stat;
const readFile = null;
exports.readFile = readFile;
const readFileSync = null;
exports.readFileSync = readFileSync;
const writeFile = null;
exports.writeFile = writeFile;
const writeFileSync = null;
exports.writeFileSync = writeFileSync;
const open = null;
exports.open = open;
const close = null;
exports.close = close;
const read = null;
exports.read = read;
const fstat = null;
exports.fstat = fstat;
const createWriteStream = null;
exports.createWriteStream = createWriteStream;
const _readToArrayBuffer = null;
exports._readToArrayBuffer = _readToArrayBuffer;
const isSupported = false;
exports.isSupported = isSupported;
//# sourceMappingURL=fs.browser.js.map