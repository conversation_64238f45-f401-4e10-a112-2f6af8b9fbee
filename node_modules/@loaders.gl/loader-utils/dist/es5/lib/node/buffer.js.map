{"version": 3, "file": "buffer.js", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "buffer", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "typedArray", "Uint8Array", "byteOffset", "length", "slice", "<PERSON><PERSON><PERSON><PERSON>", "binaryData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "from", "Error"], "sources": ["../../../../src/lib/node/buffer.ts"], "sourcesContent": ["// loaders.gl, MIT license\n\n// Isolates Buffer references to ensure they are only bundled under Node.js (avoids big webpack polyfill)\n// this file is selected by the package.json \"browser\" field).\n\n/**\n * Convert Buffer to ArrayBuffer\n * Converts Node.js `Buffer` to `<PERSON>rrayBuffer` (without triggering bundler to include <PERSON><PERSON><PERSON> polyfill on browser)\n * @todo better data type\n */\nexport function toArrayBuffer(buffer) {\n  // TODO - per docs we should just be able to call buffer.buffer, but there are issues\n  if (Buffer.isBuffer(buffer)) {\n    const typedArray = new Uint8Array(buffer.buffer, buffer.byteOffset, buffer.length);\n    return typedArray.slice().buffer;\n  }\n  return buffer;\n}\n\n/**\n * Convert (copy) ArrayBuffer to Buffer\n */\nexport function toBuffer(binaryData: ArrayBuffer | ArrayBuffer | Buffer): Buffer {\n  if (Buffer.isBuffer(binaryData)) {\n    return binaryData;\n  }\n\n  if (ArrayBuffer.isView(binaryData)) {\n    binaryData = binaryData.buffer;\n  }\n\n  if (typeof Buffer !== 'undefined' && binaryData instanceof ArrayBuffer) {\n    return Buffer.from(binaryData);\n  }\n\n  throw new Error('toBuffer');\n}\n"], "mappings": ";;;;;;;AAUO,SAASA,aAAaA,CAACC,MAAM,EAAE;EAEpC,IAAIC,MAAM,CAACC,QAAQ,CAACF,MAAM,CAAC,EAAE;IAC3B,MAAMG,UAAU,GAAG,IAAIC,UAAU,CAACJ,MAAM,CAACA,MAAM,EAAEA,MAAM,CAACK,UAAU,EAAEL,MAAM,CAACM,MAAM,CAAC;IAClF,OAAOH,UAAU,CAACI,KAAK,CAAC,CAAC,CAACP,MAAM;EAClC;EACA,OAAOA,MAAM;AACf;AAKO,SAASQ,QAAQA,CAACC,UAA8C,EAAU;EAC/E,IAAIR,MAAM,CAACC,QAAQ,CAACO,UAAU,CAAC,EAAE;IAC/B,OAAOA,UAAU;EACnB;EAEA,IAAIC,WAAW,CAACC,MAAM,CAACF,UAAU,CAAC,EAAE;IAClCA,UAAU,GAAGA,UAAU,CAACT,MAAM;EAChC;EAEA,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAIQ,UAAU,YAAYC,WAAW,EAAE;IACtE,OAAOT,MAAM,CAACW,IAAI,CAACH,UAAU,CAAC;EAChC;EAEA,MAAM,IAAII,KAAK,CAAC,UAAU,CAAC;AAC7B"}