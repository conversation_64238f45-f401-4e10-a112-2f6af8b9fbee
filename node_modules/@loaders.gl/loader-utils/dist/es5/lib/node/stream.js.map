{"version": 3, "file": "stream.js", "names": ["_stream", "_interopRequireDefault", "require", "Transform", "stream", "exports", "isSupported", "Boolean"], "sources": ["../../../../src/lib/node/stream.ts"], "sourcesContent": ["// loaders.gl, MIT license\n\nimport stream from 'stream';\n\nexport type {Writable} from 'stream';\n\n/** Wrapper for Node.js stream method */\nexport const Transform = stream.Transform;\n\nexport const isSupported = Boolean(stream);\n"], "mappings": ";;;;;;;AAEA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKO,MAAMC,SAAS,GAAGC,eAAM,CAACD,SAAS;AAACE,OAAA,CAAAF,SAAA,GAAAA,SAAA;AAEnC,MAAMG,WAAW,GAAGC,OAAO,CAACH,eAAM,CAAC;AAACC,OAAA,CAAAC,WAAA,GAAAA,WAAA"}