{"version": 3, "file": "fs.js", "names": ["_fs", "_interopRequireDefault", "require", "_buffer", "_promisify", "readdir", "promisify2", "fs", "exports", "stat", "readFile", "readFileSync", "writeFile", "promisify3", "writeFileSync", "open", "close", "fd", "Promise", "resolve", "reject", "err", "read", "fstat", "createWriteStream", "isSupported", "Boolean", "_readToArrayBuffer", "start", "length", "buffer", "<PERSON><PERSON><PERSON>", "alloc", "bytesRead", "Error", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["../../../../src/lib/node/fs.ts"], "sourcesContent": ["// fs wrapper (promisified fs + avoids bundling fs in browsers)\nimport fs from 'fs';\nimport {to<PERSON><PERSON>yBuffer} from './buffer';\nimport {promisify2, promisify3} from './promisify';\n\nexport type {Stats, WriteStream} from 'fs';\n\n/** Wrapper for Node.js fs method */\nexport const readdir: any = promisify2(fs.readdir);\n/** Wrapper for Node.js fs method */\nexport const stat: any = promisify2(fs.stat);\n\n/** Wrapper for Node.js fs method */\nexport const readFile: any = fs.readFile;\n/** Wrapper for Node.js fs method */\nexport const readFileSync = fs.readFileSync;\n/** Wrapper for Node.js fs method */\nexport const writeFile: any = promisify3(fs.writeFile);\n/** Wrapper for Node.js fs method */\nexport const writeFileSync = fs.writeFileSync;\n\n// file descriptors\n\n/** Wrapper for Node.js fs method */\nexport const open: any = fs.open;\n/** Wrapper for Node.js fs method */\nexport const close = (fd: number) =>\n  new Promise<void>((resolve, reject) => fs.close(fd, (err) => (err ? reject(err) : resolve())));\n/** Wrapper for Node.js fs method */\nexport const read: any = fs.read;\n/** Wrapper for Node.js fs method */\nexport const fstat: any = fs.fstat;\n\nexport const createWriteStream = fs.createWriteStream;\n\nexport const isSupported = Boolean(fs);\n\nexport async function _readToArrayBuffer(fd: number, start: number, length: number) {\n  const buffer = Buffer.alloc(length);\n  const {bytesRead} = await read(fd, buffer, 0, length, start);\n  if (bytesRead !== length) {\n    throw new Error('fs.read failed');\n  }\n  return toArrayBuffer(buffer);\n}\n"], "mappings": ";;;;;;;;AACA,IAAAA,GAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;AAKO,MAAMG,OAAY,GAAG,IAAAC,qBAAU,EAACC,WAAE,CAACF,OAAO,CAAC;AAACG,OAAA,CAAAH,OAAA,GAAAA,OAAA;AAE5C,MAAMI,IAAS,GAAG,IAAAH,qBAAU,EAACC,WAAE,CAACE,IAAI,CAAC;AAACD,OAAA,CAAAC,IAAA,GAAAA,IAAA;AAGtC,MAAMC,QAAa,GAAGH,WAAE,CAACG,QAAQ;AAACF,OAAA,CAAAE,QAAA,GAAAA,QAAA;AAElC,MAAMC,YAAY,GAAGJ,WAAE,CAACI,YAAY;AAACH,OAAA,CAAAG,YAAA,GAAAA,YAAA;AAErC,MAAMC,SAAc,GAAG,IAAAC,qBAAU,EAACN,WAAE,CAACK,SAAS,CAAC;AAACJ,OAAA,CAAAI,SAAA,GAAAA,SAAA;AAEhD,MAAME,aAAa,GAAGP,WAAE,CAACO,aAAa;AAACN,OAAA,CAAAM,aAAA,GAAAA,aAAA;AAKvC,MAAMC,IAAS,GAAGR,WAAE,CAACQ,IAAI;AAACP,OAAA,CAAAO,IAAA,GAAAA,IAAA;AAE1B,MAAMC,KAAK,GAAIC,EAAU,IAC9B,IAAIC,OAAO,CAAO,CAACC,OAAO,EAAEC,MAAM,KAAKb,WAAE,CAACS,KAAK,CAACC,EAAE,EAAGI,GAAG,IAAMA,GAAG,GAAGD,MAAM,CAACC,GAAG,CAAC,GAAGF,OAAO,CAAC,CAAE,CAAC,CAAC;AAACX,OAAA,CAAAQ,KAAA,GAAAA,KAAA;AAE1F,MAAMM,IAAS,GAAGf,WAAE,CAACe,IAAI;AAACd,OAAA,CAAAc,IAAA,GAAAA,IAAA;AAE1B,MAAMC,KAAU,GAAGhB,WAAE,CAACgB,KAAK;AAACf,OAAA,CAAAe,KAAA,GAAAA,KAAA;AAE5B,MAAMC,iBAAiB,GAAGjB,WAAE,CAACiB,iBAAiB;AAAChB,OAAA,CAAAgB,iBAAA,GAAAA,iBAAA;AAE/C,MAAMC,WAAW,GAAGC,OAAO,CAACnB,WAAE,CAAC;AAACC,OAAA,CAAAiB,WAAA,GAAAA,WAAA;AAEhC,eAAeE,kBAAkBA,CAACV,EAAU,EAAEW,KAAa,EAAEC,MAAc,EAAE;EAClF,MAAMC,MAAM,GAAGC,MAAM,CAACC,KAAK,CAACH,MAAM,CAAC;EACnC,MAAM;IAACI;EAAS,CAAC,GAAG,MAAMX,IAAI,CAACL,EAAE,EAAEa,MAAM,EAAE,CAAC,EAAED,MAAM,EAAED,KAAK,CAAC;EAC5D,IAAIK,SAAS,KAAKJ,MAAM,EAAE;IACxB,MAAM,IAAIK,KAAK,CAAC,gBAAgB,CAAC;EACnC;EACA,OAAO,IAAAC,qBAAa,EAACL,MAAM,CAAC;AAC9B"}