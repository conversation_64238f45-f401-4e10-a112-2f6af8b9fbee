"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.isSupported = exports.Transform = void 0;
var _stream = _interopRequireDefault(require("stream"));
const Transform = _stream.default.Transform;
exports.Transform = Transform;
const isSupported = Boolean(_stream.default);
exports.isSupported = isSupported;
//# sourceMappingURL=stream.js.map