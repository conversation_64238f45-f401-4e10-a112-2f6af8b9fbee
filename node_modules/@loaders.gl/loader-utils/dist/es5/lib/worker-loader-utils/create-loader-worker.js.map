{"version": 3, "file": "create-loader-worker.js", "names": ["_workerUtils", "require", "requestId", "createLoaderWorker", "loader", "WorkerBody", "inWorkerThread", "onmessage", "type", "payload", "input", "options", "context", "result", "parseData", "arrayBuffer", "parse", "parseOnMainThread", "postMessage", "error", "message", "Error", "Promise", "resolve", "reject", "id", "onMessage", "removeEventListener", "addEventListener", "_ref", "data", "parser", "parseSync", "parseTextSync", "textDecoder", "TextDecoder", "decode", "concat", "name", "modules", "worker"], "sources": ["../../../../src/lib/worker-loader-utils/create-loader-worker.ts"], "sourcesContent": ["/* eslint-disable no-restricted-globals */\nimport type {LoaderWithParser} from '../../types';\nimport {WorkerBody} from '@loaders.gl/worker-utils';\n// import {validateLoaderVersion} from './validate-loader-version';\n\nlet requestId = 0;\n\n/**\n * Set up a WebWorkerGlobalScope to talk with the main thread\n * @param loader\n */\nexport function createLoaderWorker(loader: LoaderWithParser) {\n  // Check that we are actually in a worker thread\n  if (!WorkerBody.inWorkerThread()) {\n    return;\n  }\n\n  WorkerBody.onmessage = async (type, payload) => {\n    switch (type) {\n      case 'process':\n        try {\n          // validateLoaderVersion(loader, data.source.split('@')[1]);\n\n          const {input, options = {}, context = {}} = payload;\n\n          const result = await parseData({\n            loader,\n            arrayBuffer: input,\n            options,\n            context: {\n              ...context,\n              parse: parseOnMainThread\n            }\n          });\n          WorkerBody.postMessage('done', {result});\n        } catch (error) {\n          const message = error instanceof Error ? error.message : '';\n          WorkerBody.postMessage('error', {error: message});\n        }\n        break;\n      default:\n    }\n  };\n}\n\nfunction parseOnMainThread(arrayBuffer: ArrayBuffer, options: {[key: string]: any}): Promise<void> {\n  return new Promise((resolve, reject) => {\n    const id = requestId++;\n\n    /**\n     */\n    const onMessage = (type, payload) => {\n      if (payload.id !== id) {\n        // not ours\n        return;\n      }\n\n      switch (type) {\n        case 'done':\n          WorkerBody.removeEventListener(onMessage);\n          resolve(payload.result);\n          break;\n\n        case 'error':\n          WorkerBody.removeEventListener(onMessage);\n          reject(payload.error);\n          break;\n\n        default:\n        // ignore\n      }\n    };\n\n    WorkerBody.addEventListener(onMessage);\n\n    // Ask the main thread to decode data\n    const payload = {id, input: arrayBuffer, options};\n    WorkerBody.postMessage('process', payload);\n  });\n}\n\n// TODO - Support byteOffset and byteLength (enabling parsing of embedded binaries without copies)\n// TODO - Why not support async loader.parse* funcs here?\n// TODO - Why not reuse a common function instead of reimplementing loader.parse* selection logic? Keeping loader small?\n// TODO - Lack of appropriate parser functions can be detected when we create worker, no need to wait until parse\nasync function parseData({loader, arrayBuffer, options, context}) {\n  let data;\n  let parser;\n  if (loader.parseSync || loader.parse) {\n    data = arrayBuffer;\n    parser = loader.parseSync || loader.parse;\n  } else if (loader.parseTextSync) {\n    const textDecoder = new TextDecoder();\n    data = textDecoder.decode(arrayBuffer);\n    parser = loader.parseTextSync;\n  } else {\n    throw new Error(`Could not load data with ${loader.name} loader`);\n  }\n\n  // TODO - proper merge in of loader options...\n  options = {\n    ...options,\n    modules: (loader && loader.options && loader.options.modules) || {},\n    worker: false\n  };\n\n  return await parser(data, {...options}, context, loader);\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,YAAA,GAAAC,OAAA;AAGA,IAAIC,SAAS,GAAG,CAAC;AAMV,SAASC,kBAAkBA,CAACC,MAAwB,EAAE;EAE3D,IAAI,CAACC,uBAAU,CAACC,cAAc,CAAC,CAAC,EAAE;IAChC;EACF;EAEAD,uBAAU,CAACE,SAAS,GAAG,OAAOC,IAAI,EAAEC,OAAO,KAAK;IAC9C,QAAQD,IAAI;MACV,KAAK,SAAS;QACZ,IAAI;UAGF,MAAM;YAACE,KAAK;YAAEC,OAAO,GAAG,CAAC,CAAC;YAAEC,OAAO,GAAG,CAAC;UAAC,CAAC,GAAGH,OAAO;UAEnD,MAAMI,MAAM,GAAG,MAAMC,SAAS,CAAC;YAC7BV,MAAM;YACNW,WAAW,EAAEL,KAAK;YAClBC,OAAO;YACPC,OAAO,EAAE;cACP,GAAGA,OAAO;cACVI,KAAK,EAAEC;YACT;UACF,CAAC,CAAC;UACFZ,uBAAU,CAACa,WAAW,CAAC,MAAM,EAAE;YAACL;UAAM,CAAC,CAAC;QAC1C,CAAC,CAAC,OAAOM,KAAK,EAAE;UACd,MAAMC,OAAO,GAAGD,KAAK,YAAYE,KAAK,GAAGF,KAAK,CAACC,OAAO,GAAG,EAAE;UAC3Df,uBAAU,CAACa,WAAW,CAAC,OAAO,EAAE;YAACC,KAAK,EAAEC;UAAO,CAAC,CAAC;QACnD;QACA;MACF;IACF;EACF,CAAC;AACH;AAEA,SAASH,iBAAiBA,CAACF,WAAwB,EAAEJ,OAA6B,EAAiB;EACjG,OAAO,IAAIW,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,MAAMC,EAAE,GAAGvB,SAAS,EAAE;IAItB,MAAMwB,SAAS,GAAGA,CAAClB,IAAI,EAAEC,OAAO,KAAK;MACnC,IAAIA,OAAO,CAACgB,EAAE,KAAKA,EAAE,EAAE;QAErB;MACF;MAEA,QAAQjB,IAAI;QACV,KAAK,MAAM;UACTH,uBAAU,CAACsB,mBAAmB,CAACD,SAAS,CAAC;UACzCH,OAAO,CAACd,OAAO,CAACI,MAAM,CAAC;UACvB;QAEF,KAAK,OAAO;UACVR,uBAAU,CAACsB,mBAAmB,CAACD,SAAS,CAAC;UACzCF,MAAM,CAACf,OAAO,CAACU,KAAK,CAAC;UACrB;QAEF;MAEF;IACF,CAAC;IAEDd,uBAAU,CAACuB,gBAAgB,CAACF,SAAS,CAAC;IAGtC,MAAMjB,OAAO,GAAG;MAACgB,EAAE;MAAEf,KAAK,EAAEK,WAAW;MAAEJ;IAAO,CAAC;IACjDN,uBAAU,CAACa,WAAW,CAAC,SAAS,EAAET,OAAO,CAAC;EAC5C,CAAC,CAAC;AACJ;AAMA,eAAeK,SAASA,CAAAe,IAAA,EAA0C;EAAA,IAAzC;IAACzB,MAAM;IAAEW,WAAW;IAAEJ,OAAO;IAAEC;EAAO,CAAC,GAAAiB,IAAA;EAC9D,IAAIC,IAAI;EACR,IAAIC,MAAM;EACV,IAAI3B,MAAM,CAAC4B,SAAS,IAAI5B,MAAM,CAACY,KAAK,EAAE;IACpCc,IAAI,GAAGf,WAAW;IAClBgB,MAAM,GAAG3B,MAAM,CAAC4B,SAAS,IAAI5B,MAAM,CAACY,KAAK;EAC3C,CAAC,MAAM,IAAIZ,MAAM,CAAC6B,aAAa,EAAE;IAC/B,MAAMC,WAAW,GAAG,IAAIC,WAAW,CAAC,CAAC;IACrCL,IAAI,GAAGI,WAAW,CAACE,MAAM,CAACrB,WAAW,CAAC;IACtCgB,MAAM,GAAG3B,MAAM,CAAC6B,aAAa;EAC/B,CAAC,MAAM;IACL,MAAM,IAAIZ,KAAK,6BAAAgB,MAAA,CAA6BjC,MAAM,CAACkC,IAAI,YAAS,CAAC;EACnE;EAGA3B,OAAO,GAAG;IACR,GAAGA,OAAO;IACV4B,OAAO,EAAGnC,MAAM,IAAIA,MAAM,CAACO,OAAO,IAAIP,MAAM,CAACO,OAAO,CAAC4B,OAAO,IAAK,CAAC,CAAC;IACnEC,MAAM,EAAE;EACV,CAAC;EAED,OAAO,MAAMT,MAAM,CAACD,IAAI,EAAE;IAAC,GAAGnB;EAAO,CAAC,EAAEC,OAAO,EAAER,MAAM,CAAC;AAC1D"}