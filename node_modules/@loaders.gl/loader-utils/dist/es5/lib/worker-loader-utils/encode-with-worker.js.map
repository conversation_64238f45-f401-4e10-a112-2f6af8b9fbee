{"version": 3, "file": "encode-with-worker.js", "names": ["_workerUtils", "require", "_globals", "canEncodeWithWorker", "writer", "options", "WorkerFarm", "isSupported", "<PERSON><PERSON><PERSON><PERSON>", "_nodeWorkers", "worker"], "sources": ["../../../../src/lib/worker-loader-utils/encode-with-worker.ts"], "sourcesContent": ["import {WorkerFarm} from '@loaders.gl/worker-utils';\nimport {Writer, WriterOptions} from '../../types';\nimport {isBrowser} from '../env-utils/globals';\n\n/**\n * Determines if a loader can parse with worker\n * @param loader\n * @param options\n */\nexport function canEncodeWithWorker(writer: Writer, options?: WriterOptions) {\n  if (!WorkerFarm.isSupported()) {\n    return false;\n  }\n\n  // Node workers are still experimental\n  if (!isBrowser && !options?._nodeWorkers) {\n    return false;\n  }\n\n  return writer.worker && options?.worker;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAEA,IAAAC,QAAA,GAAAD,OAAA;AAOO,SAASE,mBAAmBA,CAACC,MAAc,EAAEC,OAAuB,EAAE;EAC3E,IAAI,CAACC,uBAAU,CAACC,WAAW,CAAC,CAAC,EAAE;IAC7B,OAAO,KAAK;EACd;EAGA,IAAI,CAACC,kBAAS,IAAI,EAACH,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEI,YAAY,GAAE;IACxC,OAAO,KAAK;EACd;EAEA,OAAOL,MAAM,CAACM,MAAM,KAAIL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEK,MAAM;AACzC"}