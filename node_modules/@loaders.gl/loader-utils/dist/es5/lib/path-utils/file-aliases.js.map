{"version": 3, "file": "file-aliases.js", "names": ["pathPrefix", "fileAliases", "setPathPrefix", "prefix", "getPathPrefix", "addAliases", "aliases", "Object", "assign", "<PERSON><PERSON><PERSON>", "filename", "alias", "startsWith", "replacement", "replace", "concat"], "sources": ["../../../../src/lib/path-utils/file-aliases.ts"], "sourcesContent": ["// Simple file alias mechanisms for tests.\n\nlet pathPrefix = '';\nconst fileAliases: {[aliasPath: string]: string} = {};\n\n/*\n * Set a relative path prefix\n */\nexport function setPathPrefix(prefix: string): void {\n  pathPrefix = prefix;\n}\n\n/*\n * Get the relative path prefix\n */\nexport function getPathPrefix(): string {\n  return pathPrefix;\n}\n\n/**\n *\n * @param aliases\n *\n * Note: addAliases are an experimental export, they are only for testing of loaders.gl loaders\n * not intended as a generic aliasing mechanism\n */\nexport function addAliases(aliases: {[aliasPath: string]: string}): void {\n  Object.assign(fileAliases, aliases);\n}\n\n/**\n * Resolves aliases and adds path-prefix to paths\n */\nexport function resolvePath(filename: string): string {\n  for (const alias in fileAliases) {\n    if (filename.startsWith(alias)) {\n      const replacement = fileAliases[alias];\n      filename = filename.replace(alias, replacement);\n    }\n  }\n  if (!filename.startsWith('http://') && !filename.startsWith('https://')) {\n    filename = `${pathPrefix}${filename}`;\n  }\n  return filename;\n}\n"], "mappings": ";;;;;;;;;AAEA,IAAIA,UAAU,GAAG,EAAE;AACnB,MAAMC,WAA0C,GAAG,CAAC,CAAC;AAK9C,SAASC,aAAaA,CAACC,MAAc,EAAQ;EAClDH,UAAU,GAAGG,MAAM;AACrB;AAKO,SAASC,aAAaA,CAAA,EAAW;EACtC,OAAOJ,UAAU;AACnB;AASO,SAASK,UAAUA,CAACC,OAAsC,EAAQ;EACvEC,MAAM,CAACC,MAAM,CAACP,WAAW,EAAEK,OAAO,CAAC;AACrC;AAKO,SAASG,WAAWA,CAACC,QAAgB,EAAU;EACpD,KAAK,MAAMC,KAAK,IAAIV,WAAW,EAAE;IAC/B,IAAIS,QAAQ,CAACE,UAAU,CAACD,KAAK,CAAC,EAAE;MAC9B,MAAME,WAAW,GAAGZ,WAAW,CAACU,KAAK,CAAC;MACtCD,QAAQ,GAAGA,QAAQ,CAACI,OAAO,CAACH,KAAK,EAAEE,WAAW,CAAC;IACjD;EACF;EACA,IAAI,CAACH,QAAQ,CAACE,UAAU,CAAC,SAAS,CAAC,IAAI,CAACF,QAAQ,CAACE,UAAU,CAAC,UAAU,CAAC,EAAE;IACvEF,QAAQ,MAAAK,MAAA,CAAMf,UAAU,EAAAe,MAAA,CAAGL,QAAQ,CAAE;EACvC;EACA,OAAOA,QAAQ;AACjB"}