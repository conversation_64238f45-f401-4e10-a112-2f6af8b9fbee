{"version": 3, "file": "memory-conversion-utils.js", "names": ["node", "_interopRequireWildcard", "require", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "<PERSON><PERSON><PERSON><PERSON>", "value", "<PERSON><PERSON><PERSON><PERSON>", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "byteOffset", "byteLength", "buffer", "slice", "text", "uint8Array", "TextEncoder", "encode", "_to<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Error"], "sources": ["../../../../src/lib/binary-utils/memory-conversion-utils.ts"], "sourcesContent": ["// loaders.gl, MIT license\n\nimport * as node from '../node/buffer';\n\n/**\n * Check for Node.js `<PERSON><PERSON><PERSON>` (without triggering bundler to include <PERSON><PERSON><PERSON> polyfill on browser)\n */\nexport function isBuffer(value: any): boolean {\n  return value && typeof value === 'object' && value.isBuffer;\n}\n\n/**\n * Converts to Node.js `Buffer` (without triggering bundler to include <PERSON><PERSON><PERSON> polyfill on browser)\n * @todo better data type\n */\nexport function toBuffer(data: any): Buffer {\n  return node.toBuffer ? node.toBuffer(data) : data;\n}\n\n/**\n * Convert an object to an array buffer\n */\nexport function toArrayBuffer(data: unknown): ArrayBuffer {\n  // Note: Should be called first, <PERSON><PERSON><PERSON> can trigger other detections below\n  if (isBuffer(data)) {\n    return node.toArrayBuffer(data);\n  }\n\n  if (data instanceof ArrayBuffer) {\n    return data;\n  }\n\n  // Careful - Node Buffers look like Uint8Arrays (keep after isBuffer)\n  if (ArrayBuffer.isView(data)) {\n    if (data.byteOffset === 0 && data.byteLength === data.buffer.byteLength) {\n      return data.buffer;\n    }\n    return data.buffer.slice(data.byteOffset, data.byteOffset + data.byteLength);\n  }\n\n  if (typeof data === 'string') {\n    const text = data;\n    const uint8Array = new TextEncoder().encode(text);\n    return uint8Array.buffer;\n  }\n\n  // HACK to support Blob polyfill\n  if (data && typeof data === 'object' && (data as any)._toArrayBuffer) {\n    return (data as any)._toArrayBuffer();\n  }\n\n  throw new Error('toArrayBuffer');\n}\n"], "mappings": ";;;;;;;;AAEA,IAAAA,IAAA,GAAAC,uBAAA,CAAAC,OAAA;AAAuC,SAAAC,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAH,wBAAAO,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAKhC,SAASW,QAAQA,CAACC,KAAU,EAAW;EAC5C,OAAOA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACD,QAAQ;AAC7D;AAMO,SAASE,QAAQA,CAACC,IAAS,EAAU;EAC1C,OAAO5B,IAAI,CAAC2B,QAAQ,GAAG3B,IAAI,CAAC2B,QAAQ,CAACC,IAAI,CAAC,GAAGA,IAAI;AACnD;AAKO,SAASC,aAAaA,CAACD,IAAa,EAAe;EAExD,IAAIH,QAAQ,CAACG,IAAI,CAAC,EAAE;IAClB,OAAO5B,IAAI,CAAC6B,aAAa,CAACD,IAAI,CAAC;EACjC;EAEA,IAAIA,IAAI,YAAYE,WAAW,EAAE;IAC/B,OAAOF,IAAI;EACb;EAGA,IAAIE,WAAW,CAACC,MAAM,CAACH,IAAI,CAAC,EAAE;IAC5B,IAAIA,IAAI,CAACI,UAAU,KAAK,CAAC,IAAIJ,IAAI,CAACK,UAAU,KAAKL,IAAI,CAACM,MAAM,CAACD,UAAU,EAAE;MACvE,OAAOL,IAAI,CAACM,MAAM;IACpB;IACA,OAAON,IAAI,CAACM,MAAM,CAACC,KAAK,CAACP,IAAI,CAACI,UAAU,EAAEJ,IAAI,CAACI,UAAU,GAAGJ,IAAI,CAACK,UAAU,CAAC;EAC9E;EAEA,IAAI,OAAOL,IAAI,KAAK,QAAQ,EAAE;IAC5B,MAAMQ,IAAI,GAAGR,IAAI;IACjB,MAAMS,UAAU,GAAG,IAAIC,WAAW,CAAC,CAAC,CAACC,MAAM,CAACH,IAAI,CAAC;IACjD,OAAOC,UAAU,CAACH,MAAM;EAC1B;EAGA,IAAIN,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAKA,IAAI,CAASY,cAAc,EAAE;IACpE,OAAQZ,IAAI,CAASY,cAAc,CAAC,CAAC;EACvC;EAEA,MAAM,IAAIC,KAAK,CAAC,eAAe,CAAC;AAClC"}