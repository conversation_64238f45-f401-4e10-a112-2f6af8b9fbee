{"version": 3, "file": "dataview-copy-utils.js", "names": ["_memoryCopyUtils", "require", "padStringToByteAlignment", "string", "byteAlignment", "length", "<PERSON><PERSON><PERSON><PERSON>", "Math", "ceil", "padding", "whitespace", "i", "copyStringToDataView", "dataView", "byteOffset", "byteLength", "setUint8", "charCodeAt", "copyBinaryToDataView", "binary", "copyPaddedArrayBufferToDataView", "sourceBuffer", "padToNBytes", "<PERSON><PERSON><PERSON><PERSON>", "targetArray", "Uint8Array", "buffer", "sourceArray", "set", "copyPaddedStringToDataView", "textEncoder", "TextEncoder", "stringBuffer", "encode"], "sources": ["../../../../src/lib/binary-utils/dataview-copy-utils.ts"], "sourcesContent": ["// loaders./gl, MIT license\n\nimport {TypedArray} from '../../types';\nimport {padToNBytes} from './memory-copy-utils';\n\n/**\n * Helper function that pads a string with spaces to fit a certain byte alignment\n * @param string\n * @param byteAlignment\n * @returns\n *\n * @todo PERFORMANCE IDEA: No need to copy string twice...\n */\nexport function padStringToByteAlignment(string: string, byteAlignment: number): string {\n  const length = string.length;\n  const paddedLength = Math.ceil(length / byteAlignment) * byteAlignment; // Round up to the required alignment\n  const padding = paddedLength - length;\n  let whitespace = '';\n  for (let i = 0; i < padding; ++i) {\n    whitespace += ' ';\n  }\n  return string + whitespace;\n}\n\n/**\n *\n * @param dataView\n * @param byteOffset\n * @param string\n * @param byteLength\n * @returns\n */\nexport function copyStringToDataView(\n  dataView: DataView,\n  byteOffset: number,\n  string: string,\n  byteLength: number\n): number {\n  if (dataView) {\n    for (let i = 0; i < byteLength; i++) {\n      dataView.setUint8(byteOffset + i, string.charCodeAt(i));\n    }\n  }\n  return byteOffset + byteLength;\n}\n\nexport function copyBinaryToDataView(dataView, byteOffset, binary, byteLength) {\n  if (dataView) {\n    for (let i = 0; i < byteLength; i++) {\n      dataView.setUint8(byteOffset + i, binary[i]);\n    }\n  }\n  return byteOffset + byteLength;\n}\n\n/**\n * Copy sourceBuffer to dataView with some padding\n *\n * @param dataView - destination data container. If null - only new offset is calculated\n * @param byteOffset - destination byte offset to copy to\n * @param sourceBuffer - source data buffer\n * @param padding - pad the resulting array to multiple of \"padding\" bytes. Additional bytes are filled with 0x20 (ASCII space)\n *\n * @return new byteOffset of resulting dataView\n */\nexport function copyPaddedArrayBufferToDataView(\n  dataView: DataView | null,\n  byteOffset: number,\n  sourceBuffer: TypedArray,\n  padding: number\n): number {\n  const paddedLength = padToNBytes(sourceBuffer.byteLength, padding);\n  const padLength = paddedLength - sourceBuffer.byteLength;\n\n  if (dataView) {\n    // Copy array\n    const targetArray = new Uint8Array(\n      dataView.buffer,\n      dataView.byteOffset + byteOffset,\n      sourceBuffer.byteLength\n    );\n    const sourceArray = new Uint8Array(sourceBuffer);\n    targetArray.set(sourceArray);\n\n    // Add PADDING\n    for (let i = 0; i < padLength; ++i) {\n      // json chunk is padded with spaces (ASCII 0x20)\n      dataView.setUint8(byteOffset + sourceBuffer.byteLength + i, 0x20);\n    }\n  }\n  byteOffset += paddedLength;\n  return byteOffset;\n}\n\n/**\n * Copy string to dataView with some padding\n *\n * @param {DataView | null} dataView - destination data container. If null - only new offset is calculated\n * @param {number} byteOffset - destination byte offset to copy to\n * @param {string} string - source string\n * @param {number} padding - pad the resulting array to multiple of \"padding\" bytes. Additional bytes are filled with 0x20 (ASCII space)\n *\n * @return new byteOffset of resulting dataView\n */\nexport function copyPaddedStringToDataView(\n  dataView: DataView | null,\n  byteOffset: number,\n  string: string,\n  padding: number\n): number {\n  const textEncoder = new TextEncoder();\n  // PERFORMANCE IDEA: We encode twice, once to get size and once to store\n  // PERFORMANCE IDEA: Use TextEncoder.encodeInto() to avoid temporary copy\n  const stringBuffer = textEncoder.encode(string);\n\n  byteOffset = copyPaddedArrayBufferToDataView(dataView, byteOffset, stringBuffer, padding);\n\n  return byteOffset;\n}\n"], "mappings": ";;;;;;;;;;AAGA,IAAAA,gBAAA,GAAAC,OAAA;AAUO,SAASC,wBAAwBA,CAACC,MAAc,EAAEC,aAAqB,EAAU;EACtF,MAAMC,MAAM,GAAGF,MAAM,CAACE,MAAM;EAC5B,MAAMC,YAAY,GAAGC,IAAI,CAACC,IAAI,CAACH,MAAM,GAAGD,aAAa,CAAC,GAAGA,aAAa;EACtE,MAAMK,OAAO,GAAGH,YAAY,GAAGD,MAAM;EACrC,IAAIK,UAAU,GAAG,EAAE;EACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,EAAE,EAAEE,CAAC,EAAE;IAChCD,UAAU,IAAI,GAAG;EACnB;EACA,OAAOP,MAAM,GAAGO,UAAU;AAC5B;AAUO,SAASE,oBAAoBA,CAClCC,QAAkB,EAClBC,UAAkB,EAClBX,MAAc,EACdY,UAAkB,EACV;EACR,IAAIF,QAAQ,EAAE;IACZ,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,UAAU,EAAEJ,CAAC,EAAE,EAAE;MACnCE,QAAQ,CAACG,QAAQ,CAACF,UAAU,GAAGH,CAAC,EAAER,MAAM,CAACc,UAAU,CAACN,CAAC,CAAC,CAAC;IACzD;EACF;EACA,OAAOG,UAAU,GAAGC,UAAU;AAChC;AAEO,SAASG,oBAAoBA,CAACL,QAAQ,EAAEC,UAAU,EAAEK,MAAM,EAAEJ,UAAU,EAAE;EAC7E,IAAIF,QAAQ,EAAE;IACZ,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,UAAU,EAAEJ,CAAC,EAAE,EAAE;MACnCE,QAAQ,CAACG,QAAQ,CAACF,UAAU,GAAGH,CAAC,EAAEQ,MAAM,CAACR,CAAC,CAAC,CAAC;IAC9C;EACF;EACA,OAAOG,UAAU,GAAGC,UAAU;AAChC;AAYO,SAASK,+BAA+BA,CAC7CP,QAAyB,EACzBC,UAAkB,EAClBO,YAAwB,EACxBZ,OAAe,EACP;EACR,MAAMH,YAAY,GAAG,IAAAgB,4BAAW,EAACD,YAAY,CAACN,UAAU,EAAEN,OAAO,CAAC;EAClE,MAAMc,SAAS,GAAGjB,YAAY,GAAGe,YAAY,CAACN,UAAU;EAExD,IAAIF,QAAQ,EAAE;IAEZ,MAAMW,WAAW,GAAG,IAAIC,UAAU,CAChCZ,QAAQ,CAACa,MAAM,EACfb,QAAQ,CAACC,UAAU,GAAGA,UAAU,EAChCO,YAAY,CAACN,UACf,CAAC;IACD,MAAMY,WAAW,GAAG,IAAIF,UAAU,CAACJ,YAAY,CAAC;IAChDG,WAAW,CAACI,GAAG,CAACD,WAAW,CAAC;IAG5B,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,EAAE,EAAEZ,CAAC,EAAE;MAElCE,QAAQ,CAACG,QAAQ,CAACF,UAAU,GAAGO,YAAY,CAACN,UAAU,GAAGJ,CAAC,EAAE,IAAI,CAAC;IACnE;EACF;EACAG,UAAU,IAAIR,YAAY;EAC1B,OAAOQ,UAAU;AACnB;AAYO,SAASe,0BAA0BA,CACxChB,QAAyB,EACzBC,UAAkB,EAClBX,MAAc,EACdM,OAAe,EACP;EACR,MAAMqB,WAAW,GAAG,IAAIC,WAAW,CAAC,CAAC;EAGrC,MAAMC,YAAY,GAAGF,WAAW,CAACG,MAAM,CAAC9B,MAAM,CAAC;EAE/CW,UAAU,GAAGM,+BAA+B,CAACP,QAAQ,EAAEC,UAAU,EAAEkB,YAAY,EAAEvB,OAAO,CAAC;EAEzF,OAAOK,UAAU;AACnB"}