{"version": 3, "file": "get-first-characters.js", "names": ["getFirstCharacters", "data", "length", "arguments", "undefined", "slice", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "getMagicString", "buffer", "byteOffset", "arrayBuffer", "byteLength", "dataView", "DataView", "magic", "i", "String", "fromCharCode", "getUint8"], "sources": ["../../../../src/lib/binary-utils/get-first-characters.ts"], "sourcesContent": ["// loaders.gl, MIT license\n\n/**\n * Get the first characters from a binary file (interpret the first bytes as an ASCII string)\n * @param data\n * @param length\n * @returns\n */\nexport function getFirstCharacters(data: string | ArrayBuffer, length: number = 5): string {\n  if (typeof data === 'string') {\n    return data.slice(0, length);\n  } else if (ArrayBuffer.isView(data)) {\n    // Typed Arrays can have offsets into underlying buffer\n    return getMagicString(data.buffer, data.byteOffset, length);\n  } else if (data instanceof ArrayBuffer) {\n    const byteOffset = 0;\n    return getMagicString(data, byteOffset, length);\n  }\n  return '';\n}\n\n/**\n * Gets a magic string from a \"file\"\n * Typically used to check or detect file format\n * @param arrayBuffer\n * @param byteOffset\n * @param length\n * @returns\n */\nexport function getMagicString(\n  arrayBuffer: ArrayBuffer,\n  byteOffset: number,\n  length: number\n): string {\n  if (arrayBuffer.byteLength <= byteOffset + length) {\n    return '';\n  }\n  const dataView = new DataView(arrayBuffer);\n  let magic = '';\n  for (let i = 0; i < length; i++) {\n    magic += String.fromCharCode(dataView.getUint8(byteOffset + i));\n  }\n  return magic;\n}\n"], "mappings": ";;;;;;;AAQO,SAASA,kBAAkBA,CAACC,IAA0B,EAA8B;EAAA,IAA5BC,MAAc,GAAAC,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC;EAC/E,IAAI,OAAOF,IAAI,KAAK,QAAQ,EAAE;IAC5B,OAAOA,IAAI,CAACI,KAAK,CAAC,CAAC,EAAEH,MAAM,CAAC;EAC9B,CAAC,MAAM,IAAII,WAAW,CAACC,MAAM,CAACN,IAAI,CAAC,EAAE;IAEnC,OAAOO,cAAc,CAACP,IAAI,CAACQ,MAAM,EAAER,IAAI,CAACS,UAAU,EAAER,MAAM,CAAC;EAC7D,CAAC,MAAM,IAAID,IAAI,YAAYK,WAAW,EAAE;IACtC,MAAMI,UAAU,GAAG,CAAC;IACpB,OAAOF,cAAc,CAACP,IAAI,EAAES,UAAU,EAAER,MAAM,CAAC;EACjD;EACA,OAAO,EAAE;AACX;AAUO,SAASM,cAAcA,CAC5BG,WAAwB,EACxBD,UAAkB,EAClBR,MAAc,EACN;EACR,IAAIS,WAAW,CAACC,UAAU,IAAIF,UAAU,GAAGR,MAAM,EAAE;IACjD,OAAO,EAAE;EACX;EACA,MAAMW,QAAQ,GAAG,IAAIC,QAAQ,CAACH,WAAW,CAAC;EAC1C,IAAII,KAAK,GAAG,EAAE;EACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,MAAM,EAAEc,CAAC,EAAE,EAAE;IAC/BD,KAAK,IAAIE,MAAM,CAACC,YAAY,CAACL,QAAQ,CAACM,QAAQ,CAACT,UAAU,GAAGM,CAAC,CAAC,CAAC;EACjE;EACA,OAAOD,KAAK;AACd"}