{"version": 3, "file": "writable-file.js", "names": ["_globals", "require", "fs", "_interopRequireWildcard", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "makeWritableFile", "pathOrStream", "options", "<PERSON><PERSON><PERSON><PERSON>", "write", "close", "outputStream", "createWriteStream", "buffer", "Promise", "resolve", "reject", "err"], "sources": ["../../../../src/lib/filesystems/writable-file.ts"], "sourcesContent": ["// Forked from https://github.com/kbajalc/parquets under MIT license (Copyright (c) 2017 ironSource Ltd.)\nimport {isBrowser} from '../env-utils/globals';\nimport * as fs from '../node/fs';\nimport type {Writable} from 'stream';\n\nexport type WritableFile = {\n  write: (buf: Buffer) => Promise<void>;\n  close: () => Promise<void>;\n};\n\nexport interface WriteStreamOptions {\n  flags?: string;\n  encoding?: 'utf8';\n  fd?: number;\n  mode?: number;\n  autoClose?: boolean;\n  start?: number;\n}\n\n/** Helper function to create an envelope reader for a binary memory input */\nexport function makeWritableFile(\n  pathOrStream: string | Writable,\n  options?: WriteStreamOptions\n): WritableFile {\n  if (isBrowser) {\n    return {\n      write: async () => {},\n      close: async () => {}\n    };\n  }\n\n  const outputStream: Writable =\n    typeof pathOrStream === 'string' ? fs.createWriteStream(pathOrStream, options) : pathOrStream;\n  return {\n    write: async (buffer: Buffer) =>\n      new Promise((resolve, reject) => {\n        outputStream.write(buffer, (err) => (err ? reject(err) : resolve()));\n      }),\n    close: () =>\n      new Promise((resolve, reject) => {\n        (outputStream as any).close((err) => (err ? reject(err) : resolve()));\n      })\n  };\n}\n"], "mappings": ";;;;;;AACA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,EAAA,GAAAC,uBAAA,CAAAF,OAAA;AAAiC,SAAAG,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAF,wBAAAM,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAkB1B,SAASW,gBAAgBA,CAC9BC,YAA+B,EAC/BC,OAA4B,EACd;EACd,IAAIC,kBAAS,EAAE;IACb,OAAO;MACLC,KAAK,EAAE,MAAAA,CAAA,KAAY,CAAC,CAAC;MACrBC,KAAK,EAAE,MAAAA,CAAA,KAAY,CAAC;IACtB,CAAC;EACH;EAEA,MAAMC,YAAsB,GAC1B,OAAOL,YAAY,KAAK,QAAQ,GAAGzB,EAAE,CAAC+B,iBAAiB,CAACN,YAAY,EAAEC,OAAO,CAAC,GAAGD,YAAY;EAC/F,OAAO;IACLG,KAAK,EAAE,MAAOI,MAAc,IAC1B,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MAC/BL,YAAY,CAACF,KAAK,CAACI,MAAM,EAAGI,GAAG,IAAMA,GAAG,GAAGD,MAAM,CAACC,GAAG,CAAC,GAAGF,OAAO,CAAC,CAAE,CAAC;IACtE,CAAC,CAAC;IACJL,KAAK,EAAEA,CAAA,KACL,IAAII,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MAC9BL,YAAY,CAASD,KAAK,CAAEO,GAAG,IAAMA,GAAG,GAAGD,MAAM,CAACC,GAAG,CAAC,GAAGF,OAAO,CAAC,CAAE,CAAC;IACvE,CAAC;EACL,CAAC;AACH"}