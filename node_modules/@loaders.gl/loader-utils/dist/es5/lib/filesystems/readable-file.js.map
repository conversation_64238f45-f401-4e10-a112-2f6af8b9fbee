{"version": 3, "file": "readable-file.js", "names": ["makeReadableFile", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arrayBuffer", "read", "start", "length", "<PERSON><PERSON><PERSON>", "from", "close", "size", "byteLength", "blob", "slice"], "sources": ["../../../../src/lib/filesystems/readable-file.ts"], "sourcesContent": ["// loaders.gl, MIT license\n\nexport type ReadableFile = {\n  read: (position: number, length: number) => Promise<Buffer>;\n  close: () => Promise<void>;\n  /** Length of file in bytes */\n  size: number;\n};\n\n/** Helper function to create an envelope reader for a binary memory input */\nexport function makeReadableFile(data: Blob | ArrayBuffer): ReadableFile {\n  if (data instanceof ArrayBuffer) {\n    const arrayBuffer: ArrayBuffer = data;\n    return {\n      read: async (start: number, length: number) => Buffer.from(data, start, length),\n      close: async () => {},\n      size: arrayBuffer.byteLength\n    };\n  }\n\n  const blob: Blob = data;\n  return {\n    read: async (start: number, length: number) => {\n      const arrayBuffer = await blob.slice(start, start + length).arrayBuffer();\n      return Buffer.from(arrayBuffer);\n    },\n    close: async () => {},\n    size: blob.size\n  };\n}\n"], "mappings": ";;;;;;AAUO,SAASA,gBAAgBA,CAACC,IAAwB,EAAgB;EACvE,IAAIA,IAAI,YAAYC,WAAW,EAAE;IAC/B,MAAMC,WAAwB,GAAGF,IAAI;IACrC,OAAO;MACLG,IAAI,EAAE,MAAAA,CAAOC,KAAa,EAAEC,MAAc,KAAKC,MAAM,CAACC,IAAI,CAACP,IAAI,EAAEI,KAAK,EAAEC,MAAM,CAAC;MAC/EG,KAAK,EAAE,MAAAA,CAAA,KAAY,CAAC,CAAC;MACrBC,IAAI,EAAEP,WAAW,CAACQ;IACpB,CAAC;EACH;EAEA,MAAMC,IAAU,GAAGX,IAAI;EACvB,OAAO;IACLG,IAAI,EAAE,MAAAA,CAAOC,KAAa,EAAEC,MAAc,KAAK;MAC7C,MAAMH,WAAW,GAAG,MAAMS,IAAI,CAACC,KAAK,CAACR,KAAK,EAAEA,KAAK,GAAGC,MAAM,CAAC,CAACH,WAAW,CAAC,CAAC;MACzE,OAAOI,MAAM,CAACC,IAAI,CAACL,WAAW,CAAC;IACjC,CAAC;IACDM,KAAK,EAAE,MAAAA,CAAA,KAAY,CAAC,CAAC;IACrBC,IAAI,EAAEE,IAAI,CAACF;EACb,CAAC;AACH"}