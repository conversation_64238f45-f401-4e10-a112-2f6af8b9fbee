"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.makeWritableFile = makeWritableFile;
var _globals = require("../env-utils/globals");
var fs = _interopRequireWildcard(require("../node/fs"));
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
function makeWritableFile(pathOrStream, options) {
  if (_globals.isBrowser) {
    return {
      write: async () => {},
      close: async () => {}
    };
  }
  const outputStream = typeof pathOrStream === 'string' ? fs.createWriteStream(pathOrStream, options) : pathOrStream;
  return {
    write: async buffer => new Promise((resolve, reject) => {
      outputStream.write(buffer, err => err ? reject(err) : resolve());
    }),
    close: () => new Promise((resolve, reject) => {
      outputStream.close(err => err ? reject(err) : resolve());
    })
  };
}
//# sourceMappingURL=writable-file.js.map