"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var fs = _interopRequireWildcard(require("../node/fs"));
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
class NodeFileSystem {
  constructor(options) {
    this.fetch = options._fetch;
  }
  async readdir() {
    let dirname = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '.';
    let options = arguments.length > 1 ? arguments[1] : undefined;
    return await fs.readdir(dirname, options);
  }
  async stat(path, options) {
    const info = await fs.stat(path, options);
    return {
      size: Number(info.size),
      isDirectory: () => false,
      info
    };
  }
  async fetch(path, options) {
    const fallbackFetch = options.fetch || this.fetch;
    return fallbackFetch(path, options);
  }
  async open(path, flags, mode) {
    return await fs.open(path, flags);
  }
  async close(fd) {
    return await fs.close(fd);
  }
  async fstat(fd) {
    const info = await fs.fstat(fd);
    return info;
  }
  async read(fd, _ref) {
    let {
      buffer = null,
      offset = 0,
      length = buffer.byteLength,
      position = null
    } = _ref;
    let totalBytesRead = 0;
    while (totalBytesRead < length) {
      const {
        bytesRead
      } = await fs.read(fd, buffer, offset + totalBytesRead, length - totalBytesRead, position + totalBytesRead);
      totalBytesRead += bytesRead;
    }
    return {
      bytesRead: totalBytesRead,
      buffer
    };
  }
}
exports.default = NodeFileSystem;
//# sourceMappingURL=node-filesystem.js.map