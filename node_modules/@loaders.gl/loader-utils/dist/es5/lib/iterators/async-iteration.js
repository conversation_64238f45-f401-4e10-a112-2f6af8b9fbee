"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.concatenateArrayBuffersAsync = concatenateArrayBuffersAsync;
exports.concatenateStringsAsync = concatenateStringsAsync;
exports.forEach = forEach;
var _arrayBufferUtils = require("../binary-utils/array-buffer-utils");
async function forEach(iterator, visitor) {
  while (true) {
    const {
      done,
      value
    } = await iterator.next();
    if (done) {
      iterator.return();
      return;
    }
    const cancel = visitor(value);
    if (cancel) {
      return;
    }
  }
}
async function concatenateArrayBuffersAsync(asyncIterator) {
  const arrayBuffers = [];
  for await (const chunk of asyncIterator) {
    arrayBuffers.push(chunk);
  }
  return (0, _arrayBufferUtils.concatenateArrayBuffers)(...arrayBuffers);
}
async function concatenateStringsAsync(asyncIterator) {
  const strings = [];
  for await (const chunk of asyncIterator) {
    strings.push(chunk);
  }
  return strings.join('');
}
//# sourceMappingURL=async-iteration.js.map