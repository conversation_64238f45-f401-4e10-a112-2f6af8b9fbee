{"version": 3, "file": "text-iterators.js", "names": ["makeTextDecoderIterator", "arrayBufferIterator", "options", "arguments", "length", "undefined", "textDecoder", "TextDecoder", "arrayBuffer", "decode", "stream", "e", "Promise", "reject", "makeTextEncoderIterator", "textIterator", "textEncoder", "TextEncoder", "text", "encode", "makeLineIterator", "previous", "textChunk", "eolIndex", "indexOf", "line", "slice", "makeNumberedLineIterator", "lineIterator", "counter"], "sources": ["../../../../src/lib/iterators/text-iterators.ts"], "sourcesContent": ["// TextDecoder iterators\n// TextDecoder will keep any partial undecoded bytes between calls to `decode`\n\nexport async function* makeTextDecoderIterator(\n  arrayBufferIterator: AsyncIterable<ArrayBuffer> | Iterable<ArrayBuffer>,\n  options: TextDecoderOptions = {}\n): AsyncIterable<string> {\n  const textDecoder = new TextDecoder(undefined, options);\n  for await (const arrayBuffer of arrayBufferIterator) {\n    yield typeof arrayBuffer === 'string'\n      ? arrayBuffer\n      : textDecoder.decode(arrayBuffer, {stream: true});\n  }\n}\n\n// TextEncoder iterator\n// TODO - this is not useful unless min chunk size is given\n// TextEncoder will keep any partial undecoded bytes between calls to `encode`\n// If iterator does not yield strings, assume arrayBuffer and return unencoded\n\nexport async function* makeTextEncoderIterator(\n  textIterator: AsyncIterable<string> | Iterable<ArrayBuffer>\n): AsyncIterable<ArrayBuffer> {\n  const textEncoder = new TextEncoder();\n  for await (const text of textIterator) {\n    yield typeof text === 'string' ? textEncoder.encode(text) : text;\n  }\n}\n\n/**\n * @param textIterator async iterable yielding strings\n * @returns an async iterable over lines\n * See http://2ality.com/2018/04/async-iter-nodejs.html\n */\n\nexport async function* makeLineIterator(\n  textIterator: AsyncIterable<string>\n): AsyncIterable<string> {\n  let previous = '';\n  for await (const textChunk of textIterator) {\n    previous += textChunk;\n    let eolIndex;\n    while ((eolIndex = previous.indexOf('\\n')) >= 0) {\n      // line includes the EOL\n      const line = previous.slice(0, eolIndex + 1);\n      previous = previous.slice(eolIndex + 1);\n      yield line;\n    }\n  }\n\n  if (previous.length > 0) {\n    yield previous;\n  }\n}\n\n/**\n * @param lineIterator async iterable yielding lines\n * @returns async iterable yielding numbered lines\n *\n * See http://2ality.com/2018/04/async-iter-nodejs.html\n */\nexport async function* makeNumberedLineIterator(\n  lineIterator: AsyncIterable<string>\n): AsyncIterable<{counter: number; line: string}> {\n  let counter = 1;\n  for await (const line of lineIterator) {\n    yield {counter, line};\n    counter++;\n  }\n}\n"], "mappings": ";;;;;;;;;AAGO,SAAgBA,uBAAuBA,CAC5CC,mBAAuE;EAAA;IAAA,IACvEC,OAA2B,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAAA,0BACT;MACvB,MAAMG,WAAW,GAAG,IAAIC,WAAW,CAACF,SAAS,EAAEH,OAAO,CAAC;MACvD,WAAW,MAAMM,WAAW,IAAIP,mBAAmB,EAAE;QACnD,MAAM,OAAOO,WAAW,KAAK,QAAQ,GACjCA,WAAW,GACXF,WAAW,CAACG,MAAM,CAACD,WAAW,EAAE;UAACE,MAAM,EAAE;QAAI,CAAC,CAAC;MACrD;IACF,CAAC;EAAA,SAAAC,CAAA;IAAA,OAAAC,OAAA,CAAAC,MAAA,CAAAF,CAAA;EAAA;AAAA;AAOM,gBAAgBG,uBAAuBA,CAC5CC,YAA2D,EAC/B;EAC5B,MAAMC,WAAW,GAAG,IAAIC,WAAW,CAAC,CAAC;EACrC,WAAW,MAAMC,IAAI,IAAIH,YAAY,EAAE;IACrC,MAAM,OAAOG,IAAI,KAAK,QAAQ,GAAGF,WAAW,CAACG,MAAM,CAACD,IAAI,CAAC,GAAGA,IAAI;EAClE;AACF;AAQO,gBAAgBE,gBAAgBA,CACrCL,YAAmC,EACZ;EACvB,IAAIM,QAAQ,GAAG,EAAE;EACjB,WAAW,MAAMC,SAAS,IAAIP,YAAY,EAAE;IAC1CM,QAAQ,IAAIC,SAAS;IACrB,IAAIC,QAAQ;IACZ,OAAO,CAACA,QAAQ,GAAGF,QAAQ,CAACG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;MAE/C,MAAMC,IAAI,GAAGJ,QAAQ,CAACK,KAAK,CAAC,CAAC,EAAEH,QAAQ,GAAG,CAAC,CAAC;MAC5CF,QAAQ,GAAGA,QAAQ,CAACK,KAAK,CAACH,QAAQ,GAAG,CAAC,CAAC;MACvC,MAAME,IAAI;IACZ;EACF;EAEA,IAAIJ,QAAQ,CAACjB,MAAM,GAAG,CAAC,EAAE;IACvB,MAAMiB,QAAQ;EAChB;AACF;AAQO,gBAAgBM,wBAAwBA,CAC7CC,YAAmC,EACa;EAChD,IAAIC,OAAO,GAAG,CAAC;EACf,WAAW,MAAMJ,IAAI,IAAIG,YAAY,EAAE;IACrC,MAAM;MAACC,OAAO;MAAEJ;IAAI,CAAC;IACrBI,OAAO,EAAE;EACX;AACF"}