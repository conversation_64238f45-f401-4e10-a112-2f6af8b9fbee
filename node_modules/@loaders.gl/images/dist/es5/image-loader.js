"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports._typecheckImageLoader = exports.ImageLoader = void 0;
var _version = require("./lib/utils/version");
var _parseImage = require("./lib/parsers/parse-image");
var _binaryImageApi = require("./lib/category-api/binary-image-api");
const EXTENSIONS = ['png', 'jpg', 'jpeg', 'gif', 'webp', 'bmp', 'ico', 'svg', 'avif'];
const MIME_TYPES = ['image/png', 'image/jpeg', 'image/gif', 'image/webp', 'image/avif', 'image/bmp', 'image/vnd.microsoft.icon', 'image/svg+xml'];
const DEFAULT_IMAGE_LOADER_OPTIONS = {
  image: {
    type: 'auto',
    decode: true
  }
};
const ImageLoader = {
  id: 'image',
  module: 'images',
  name: 'Images',
  version: _version.VERSION,
  mimeTypes: MIME_TYPES,
  extensions: EXTENSIONS,
  parse: _parseImage.parseImage,
  tests: [arrayBuffer => Boolean((0, _binaryImageApi.getBinaryImageMetadata)(new DataView(arrayBuffer)))],
  options: DEFAULT_IMAGE_LOADER_OPTIONS
};
exports.ImageLoader = ImageLoader;
const _typecheckImageLoader = ImageLoader;
exports._typecheckImageLoader = _typecheckImageLoader;
//# sourceMappingURL=image-loader.js.map