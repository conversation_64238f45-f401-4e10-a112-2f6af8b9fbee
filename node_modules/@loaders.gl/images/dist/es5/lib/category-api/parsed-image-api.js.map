{"version": 3, "file": "parsed-image-api.js", "names": ["isImage", "image", "Boolean", "getImageTypeOrNull", "deleteImage", "getImageType", "close", "format", "Error", "getImageSize", "getImageData", "canvas", "document", "createElement", "context", "getContext", "width", "height", "drawImage", "ImageBitmap", "Image", "data"], "sources": ["../../../../src/lib/category-api/parsed-image-api.ts"], "sourcesContent": ["import type {ImageType, ImageTypeEnum, ImageDataType} from '../../types';\n\nexport function isImage(image: ImageType): boolean {\n  return Boolean(getImageTypeOrNull(image));\n}\n\nexport function deleteImage(image: ImageType): void {\n  switch (getImageType(image)) {\n    case 'imagebitmap':\n      (image as ImageBitmap).close();\n      break;\n    default:\n    // Nothing to do for images and image data objects\n  }\n}\n\nexport function getImageType(image: ImageType): ImageTypeEnum {\n  const format = getImageTypeOrNull(image);\n  if (!format) {\n    throw new Error('Not an image');\n  }\n  return format;\n}\n\nexport function getImageSize(image: ImageType): {width: number; height: number} {\n  return getImageData(image);\n}\n\nexport function getImageData(image: ImageType): ImageDataType | ImageData {\n  switch (getImageType(image)) {\n    case 'data':\n      return image as unknown as ImageData;\n\n    case 'image':\n    case 'imagebitmap':\n      // Extract the image data from the image via a canvas\n      const canvas = document.createElement('canvas');\n      // TODO - reuse the canvas?\n      const context = canvas.getContext('2d');\n      if (!context) {\n        throw new Error('getImageData');\n      }\n      // @ts-ignore\n      canvas.width = image.width;\n      // @ts-ignore\n      canvas.height = image.height;\n      // @ts-ignore\n      context.drawImage(image, 0, 0);\n      // @ts-ignore\n      return context.getImageData(0, 0, image.width, image.height);\n\n    default:\n      throw new Error('getImageData');\n  }\n}\n\n// PRIVATE\n\n// eslint-disable-next-line complexity\nfunction getImageTypeOrNull(image) {\n  if (typeof ImageBitmap !== 'undefined' && image instanceof ImageBitmap) {\n    return 'imagebitmap';\n  }\n  if (typeof Image !== 'undefined' && image instanceof Image) {\n    return 'image';\n  }\n  if (image && typeof image === 'object' && image.data && image.width && image.height) {\n    return 'data';\n  }\n  return null;\n}\n"], "mappings": ";;;;;;;;;;AAEO,SAASA,OAAOA,CAACC,KAAgB,EAAW;EACjD,OAAOC,OAAO,CAACC,kBAAkB,CAACF,KAAK,CAAC,CAAC;AAC3C;AAEO,SAASG,WAAWA,CAACH,KAAgB,EAAQ;EAClD,QAAQI,YAAY,CAACJ,KAAK,CAAC;IACzB,KAAK,aAAa;MACfA,KAAK,CAAiBK,KAAK,CAAC,CAAC;MAC9B;IACF;EAEF;AACF;AAEO,SAASD,YAAYA,CAACJ,KAAgB,EAAiB;EAC5D,MAAMM,MAAM,GAAGJ,kBAAkB,CAACF,KAAK,CAAC;EACxC,IAAI,CAACM,MAAM,EAAE;IACX,MAAM,IAAIC,KAAK,CAAC,cAAc,CAAC;EACjC;EACA,OAAOD,MAAM;AACf;AAEO,SAASE,YAAYA,CAACR,KAAgB,EAAmC;EAC9E,OAAOS,YAAY,CAACT,KAAK,CAAC;AAC5B;AAEO,SAASS,YAAYA,CAACT,KAAgB,EAA6B;EACxE,QAAQI,YAAY,CAACJ,KAAK,CAAC;IACzB,KAAK,MAAM;MACT,OAAOA,KAAK;IAEd,KAAK,OAAO;IACZ,KAAK,aAAa;MAEhB,MAAMU,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAE/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;MACvC,IAAI,CAACD,OAAO,EAAE;QACZ,MAAM,IAAIN,KAAK,CAAC,cAAc,CAAC;MACjC;MAEAG,MAAM,CAACK,KAAK,GAAGf,KAAK,CAACe,KAAK;MAE1BL,MAAM,CAACM,MAAM,GAAGhB,KAAK,CAACgB,MAAM;MAE5BH,OAAO,CAACI,SAAS,CAACjB,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;MAE9B,OAAOa,OAAO,CAACJ,YAAY,CAAC,CAAC,EAAE,CAAC,EAAET,KAAK,CAACe,KAAK,EAAEf,KAAK,CAACgB,MAAM,CAAC;IAE9D;MACE,MAAM,IAAIT,KAAK,CAAC,cAAc,CAAC;EACnC;AACF;AAKA,SAASL,kBAAkBA,CAACF,KAAK,EAAE;EACjC,IAAI,OAAOkB,WAAW,KAAK,WAAW,IAAIlB,KAAK,YAAYkB,WAAW,EAAE;IACtE,OAAO,aAAa;EACtB;EACA,IAAI,OAAOC,KAAK,KAAK,WAAW,IAAInB,KAAK,YAAYmB,KAAK,EAAE;IAC1D,OAAO,OAAO;EAChB;EACA,IAAInB,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACoB,IAAI,IAAIpB,KAAK,CAACe,KAAK,IAAIf,KAAK,CAACgB,MAAM,EAAE;IACnF,OAAO,MAAM;EACf;EACA,OAAO,IAAI;AACb"}