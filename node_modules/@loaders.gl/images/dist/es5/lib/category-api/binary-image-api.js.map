{"version": 3, "file": "binary-image-api.js", "names": ["_parseIsobmffBinary", "require", "BIG_ENDIAN", "LITTLE_ENDIAN", "getBinaryImageMetadata", "binaryData", "dataView", "toDataView", "getPngMetadata", "getJpegMetadata", "getGifMetadata", "getBmpMetadata", "getISOBMFFMetadata", "buffer", "Uint8Array", "DataView", "mediaType", "getISOBMFFMediaType", "mimeType", "width", "height", "isPng", "byteLength", "getUint32", "isGif", "getUint16", "isBmp", "isJpeg", "getUint8", "tableMarkers", "sofMarkers", "getJpegMarkers", "i", "marker", "has", "Set", "add", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Error"], "sources": ["../../../../src/lib/category-api/binary-image-api.ts"], "sourcesContent": ["// Attributions\n// * Based on binary-gltf-utils under MIT license: Copyright (c) 2016-17 <PERSON>\n\n// TODO: make these functions work for Node.js buffers?\n// Quarantine references to <PERSON>uff<PERSON> to prevent bundler from adding big polyfills\n// import {bufferToArrayBuffer} from '../node/buffer-to-array-buffer';\n// TODO - this should be handled in @loaders.gl/polyfills\n\nimport {getISOBMFFMediaType} from './parse-isobmff-binary';\n\n/** MIME type, width and height extracted from binary compressed image data */\nexport type BinaryImageMetadata = {\n  mimeType: string;\n  width: number;\n  height: number;\n};\n\nconst BIG_ENDIAN = false;\nconst LITTLE_ENDIAN = true;\n\n/**\n * Extracts `{mimeType, width and height}` from a memory buffer containing a known image format\n * Currently supports `image/png`, `image/jpeg`, `image/bmp` and `image/gif`.\n * @param binaryData: DataView | ArrayBuffer image file memory to parse\n * @returns metadata or null if memory is not a valid image file format layout.\n */\nexport function getBinaryImageMetadata(\n  binaryData: DataView | ArrayBuffer\n): BinaryImageMetadata | null {\n  const dataView = toDataView(binaryData);\n  return (\n    getPngMetadata(dataView) ||\n    getJpegMetadata(dataView) ||\n    getGifMetadata(dataView) ||\n    getBmpMetadata(dataView) ||\n    getISOBMFFMetadata(dataView)\n  );\n}\n\n// ISOBMFF\n\nfunction getISOBMFFMetadata(binaryData: DataView | ArrayBuffer): BinaryImageMetadata | null {\n  const buffer = new Uint8Array(binaryData instanceof DataView ? binaryData.buffer : binaryData);\n  const mediaType = getISOBMFFMediaType(buffer);\n  if (!mediaType) {\n    return null;\n  }\n  return {\n    mimeType: mediaType.mimeType,\n    // TODO - decode width and height\n    width: 0,\n    height: 0\n  };\n}\n\n// PNG\n\nfunction getPngMetadata(binaryData: DataView | ArrayBuffer): BinaryImageMetadata | null {\n  const dataView = toDataView(binaryData);\n  // Check file contains the first 4 bytes of the PNG signature.\n  const isPng = dataView.byteLength >= 24 && dataView.getUint32(0, BIG_ENDIAN) === 0x89504e47;\n  if (!isPng) {\n    return null;\n  }\n\n  // Extract size from a binary PNG file\n  return {\n    mimeType: 'image/png',\n    width: dataView.getUint32(16, BIG_ENDIAN),\n    height: dataView.getUint32(20, BIG_ENDIAN)\n  };\n}\n\n// GIF\n\n// Extract size from a binary GIF file\n// TODO: GIF is not this simple\nfunction getGifMetadata(binaryData: DataView | ArrayBuffer): BinaryImageMetadata | null {\n  const dataView = toDataView(binaryData);\n  // Check first 4 bytes of the GIF signature (\"GIF8\").\n  const isGif = dataView.byteLength >= 10 && dataView.getUint32(0, BIG_ENDIAN) === 0x47494638;\n  if (!isGif) {\n    return null;\n  }\n\n  // GIF is little endian.\n  return {\n    mimeType: 'image/gif',\n    width: dataView.getUint16(6, LITTLE_ENDIAN),\n    height: dataView.getUint16(8, LITTLE_ENDIAN)\n  };\n}\n\n// BMP\n\n// TODO: BMP is not this simple\nexport function getBmpMetadata(binaryData: DataView | ArrayBuffer): BinaryImageMetadata | null {\n  const dataView = toDataView(binaryData);\n  // Check magic number is valid (first 2 characters should be \"BM\").\n  // The mandatory bitmap file header is 14 bytes long.\n  const isBmp =\n    dataView.byteLength >= 14 &&\n    dataView.getUint16(0, BIG_ENDIAN) === 0x424d &&\n    dataView.getUint32(2, LITTLE_ENDIAN) === dataView.byteLength;\n\n  if (!isBmp) {\n    return null;\n  }\n\n  // BMP is little endian.\n  return {\n    mimeType: 'image/bmp',\n    width: dataView.getUint32(18, LITTLE_ENDIAN),\n    height: dataView.getUint32(22, LITTLE_ENDIAN)\n  };\n}\n\n// JPEG\n\n// Extract width and height from a binary JPEG file\nfunction getJpegMetadata(binaryData: DataView | ArrayBuffer): BinaryImageMetadata | null {\n  const dataView = toDataView(binaryData);\n  // Check file contains the JPEG \"start of image\" (SOI) marker\n  // followed by another marker.\n  const isJpeg =\n    dataView.byteLength >= 3 &&\n    dataView.getUint16(0, BIG_ENDIAN) === 0xffd8 &&\n    dataView.getUint8(2) === 0xff;\n\n  if (!isJpeg) {\n    return null;\n  }\n\n  const {tableMarkers, sofMarkers} = getJpegMarkers();\n\n  // Exclude the two byte SOI marker.\n  let i = 2;\n  while (i + 9 < dataView.byteLength) {\n    const marker = dataView.getUint16(i, BIG_ENDIAN);\n\n    // The frame that contains the width and height of the JPEG image.\n    if (sofMarkers.has(marker)) {\n      return {\n        mimeType: 'image/jpeg',\n        height: dataView.getUint16(i + 5, BIG_ENDIAN), // Number of lines\n        width: dataView.getUint16(i + 7, BIG_ENDIAN) // Number of pixels per line\n      };\n    }\n\n    // Miscellaneous tables/data preceding the frame header.\n    if (!tableMarkers.has(marker)) {\n      return null;\n    }\n\n    // Length includes size of length parameter but not the two byte header.\n    i += 2;\n    i += dataView.getUint16(i, BIG_ENDIAN);\n  }\n\n  return null;\n}\n\nfunction getJpegMarkers() {\n  // Tables/misc header markers.\n  // DQT, DHT, DAC, DRI, COM, APP_n\n  const tableMarkers = new Set([0xffdb, 0xffc4, 0xffcc, 0xffdd, 0xfffe]);\n  for (let i = 0xffe0; i < 0xfff0; ++i) {\n    tableMarkers.add(i);\n  }\n\n  // SOF markers and DHP marker.\n  // These markers are after tables/misc data.\n  const sofMarkers = new Set([\n    0xffc0, 0xffc1, 0xffc2, 0xffc3, 0xffc5, 0xffc6, 0xffc7, 0xffc9, 0xffca, 0xffcb, 0xffcd, 0xffce,\n    0xffcf, 0xffde\n  ]);\n\n  return {tableMarkers, sofMarkers};\n}\n\n// TODO - move into image module?\nfunction toDataView(data) {\n  if (data instanceof DataView) {\n    return data;\n  }\n  if (ArrayBuffer.isView(data)) {\n    return new DataView(data.buffer);\n  }\n\n  // TODO: make these functions work for Node.js buffers?\n  // if (bufferToArrayBuffer) {\n  //   data = bufferToArrayBuffer(data);\n  // }\n\n  // Careful - Node Buffers will look like ArrayBuffers (keep after isBuffer)\n  if (data instanceof ArrayBuffer) {\n    return new DataView(data);\n  }\n  throw new Error('toDataView');\n}\n"], "mappings": ";;;;;;;AAQA,IAAAA,mBAAA,GAAAC,OAAA;AASA,MAAMC,UAAU,GAAG,KAAK;AACxB,MAAMC,aAAa,GAAG,IAAI;AAQnB,SAASC,sBAAsBA,CACpCC,UAAkC,EACN;EAC5B,MAAMC,QAAQ,GAAGC,UAAU,CAACF,UAAU,CAAC;EACvC,OACEG,cAAc,CAACF,QAAQ,CAAC,IACxBG,eAAe,CAACH,QAAQ,CAAC,IACzBI,cAAc,CAACJ,QAAQ,CAAC,IACxBK,cAAc,CAACL,QAAQ,CAAC,IACxBM,kBAAkB,CAACN,QAAQ,CAAC;AAEhC;AAIA,SAASM,kBAAkBA,CAACP,UAAkC,EAA8B;EAC1F,MAAMQ,MAAM,GAAG,IAAIC,UAAU,CAACT,UAAU,YAAYU,QAAQ,GAAGV,UAAU,CAACQ,MAAM,GAAGR,UAAU,CAAC;EAC9F,MAAMW,SAAS,GAAG,IAAAC,uCAAmB,EAACJ,MAAM,CAAC;EAC7C,IAAI,CAACG,SAAS,EAAE;IACd,OAAO,IAAI;EACb;EACA,OAAO;IACLE,QAAQ,EAAEF,SAAS,CAACE,QAAQ;IAE5BC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACV,CAAC;AACH;AAIA,SAASZ,cAAcA,CAACH,UAAkC,EAA8B;EACtF,MAAMC,QAAQ,GAAGC,UAAU,CAACF,UAAU,CAAC;EAEvC,MAAMgB,KAAK,GAAGf,QAAQ,CAACgB,UAAU,IAAI,EAAE,IAAIhB,QAAQ,CAACiB,SAAS,CAAC,CAAC,EAAErB,UAAU,CAAC,KAAK,UAAU;EAC3F,IAAI,CAACmB,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EAGA,OAAO;IACLH,QAAQ,EAAE,WAAW;IACrBC,KAAK,EAAEb,QAAQ,CAACiB,SAAS,CAAC,EAAE,EAAErB,UAAU,CAAC;IACzCkB,MAAM,EAAEd,QAAQ,CAACiB,SAAS,CAAC,EAAE,EAAErB,UAAU;EAC3C,CAAC;AACH;AAMA,SAASQ,cAAcA,CAACL,UAAkC,EAA8B;EACtF,MAAMC,QAAQ,GAAGC,UAAU,CAACF,UAAU,CAAC;EAEvC,MAAMmB,KAAK,GAAGlB,QAAQ,CAACgB,UAAU,IAAI,EAAE,IAAIhB,QAAQ,CAACiB,SAAS,CAAC,CAAC,EAAErB,UAAU,CAAC,KAAK,UAAU;EAC3F,IAAI,CAACsB,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EAGA,OAAO;IACLN,QAAQ,EAAE,WAAW;IACrBC,KAAK,EAAEb,QAAQ,CAACmB,SAAS,CAAC,CAAC,EAAEtB,aAAa,CAAC;IAC3CiB,MAAM,EAAEd,QAAQ,CAACmB,SAAS,CAAC,CAAC,EAAEtB,aAAa;EAC7C,CAAC;AACH;AAKO,SAASQ,cAAcA,CAACN,UAAkC,EAA8B;EAC7F,MAAMC,QAAQ,GAAGC,UAAU,CAACF,UAAU,CAAC;EAGvC,MAAMqB,KAAK,GACTpB,QAAQ,CAACgB,UAAU,IAAI,EAAE,IACzBhB,QAAQ,CAACmB,SAAS,CAAC,CAAC,EAAEvB,UAAU,CAAC,KAAK,MAAM,IAC5CI,QAAQ,CAACiB,SAAS,CAAC,CAAC,EAAEpB,aAAa,CAAC,KAAKG,QAAQ,CAACgB,UAAU;EAE9D,IAAI,CAACI,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EAGA,OAAO;IACLR,QAAQ,EAAE,WAAW;IACrBC,KAAK,EAAEb,QAAQ,CAACiB,SAAS,CAAC,EAAE,EAAEpB,aAAa,CAAC;IAC5CiB,MAAM,EAAEd,QAAQ,CAACiB,SAAS,CAAC,EAAE,EAAEpB,aAAa;EAC9C,CAAC;AACH;AAKA,SAASM,eAAeA,CAACJ,UAAkC,EAA8B;EACvF,MAAMC,QAAQ,GAAGC,UAAU,CAACF,UAAU,CAAC;EAGvC,MAAMsB,MAAM,GACVrB,QAAQ,CAACgB,UAAU,IAAI,CAAC,IACxBhB,QAAQ,CAACmB,SAAS,CAAC,CAAC,EAAEvB,UAAU,CAAC,KAAK,MAAM,IAC5CI,QAAQ,CAACsB,QAAQ,CAAC,CAAC,CAAC,KAAK,IAAI;EAE/B,IAAI,CAACD,MAAM,EAAE;IACX,OAAO,IAAI;EACb;EAEA,MAAM;IAACE,YAAY;IAAEC;EAAU,CAAC,GAAGC,cAAc,CAAC,CAAC;EAGnD,IAAIC,CAAC,GAAG,CAAC;EACT,OAAOA,CAAC,GAAG,CAAC,GAAG1B,QAAQ,CAACgB,UAAU,EAAE;IAClC,MAAMW,MAAM,GAAG3B,QAAQ,CAACmB,SAAS,CAACO,CAAC,EAAE9B,UAAU,CAAC;IAGhD,IAAI4B,UAAU,CAACI,GAAG,CAACD,MAAM,CAAC,EAAE;MAC1B,OAAO;QACLf,QAAQ,EAAE,YAAY;QACtBE,MAAM,EAAEd,QAAQ,CAACmB,SAAS,CAACO,CAAC,GAAG,CAAC,EAAE9B,UAAU,CAAC;QAC7CiB,KAAK,EAAEb,QAAQ,CAACmB,SAAS,CAACO,CAAC,GAAG,CAAC,EAAE9B,UAAU;MAC7C,CAAC;IACH;IAGA,IAAI,CAAC2B,YAAY,CAACK,GAAG,CAACD,MAAM,CAAC,EAAE;MAC7B,OAAO,IAAI;IACb;IAGAD,CAAC,IAAI,CAAC;IACNA,CAAC,IAAI1B,QAAQ,CAACmB,SAAS,CAACO,CAAC,EAAE9B,UAAU,CAAC;EACxC;EAEA,OAAO,IAAI;AACb;AAEA,SAAS6B,cAAcA,CAAA,EAAG;EAGxB,MAAMF,YAAY,GAAG,IAAIM,GAAG,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;EACtE,KAAK,IAAIH,CAAC,GAAG,MAAM,EAAEA,CAAC,GAAG,MAAM,EAAE,EAAEA,CAAC,EAAE;IACpCH,YAAY,CAACO,GAAG,CAACJ,CAAC,CAAC;EACrB;EAIA,MAAMF,UAAU,GAAG,IAAIK,GAAG,CAAC,CACzB,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAC9F,MAAM,EAAE,MAAM,CACf,CAAC;EAEF,OAAO;IAACN,YAAY;IAAEC;EAAU,CAAC;AACnC;AAGA,SAASvB,UAAUA,CAAC8B,IAAI,EAAE;EACxB,IAAIA,IAAI,YAAYtB,QAAQ,EAAE;IAC5B,OAAOsB,IAAI;EACb;EACA,IAAIC,WAAW,CAACC,MAAM,CAACF,IAAI,CAAC,EAAE;IAC5B,OAAO,IAAItB,QAAQ,CAACsB,IAAI,CAACxB,MAAM,CAAC;EAClC;EAQA,IAAIwB,IAAI,YAAYC,WAAW,EAAE;IAC/B,OAAO,IAAIvB,QAAQ,CAACsB,IAAI,CAAC;EAC3B;EACA,MAAM,IAAIG,KAAK,CAAC,YAAY,CAAC;AAC/B"}