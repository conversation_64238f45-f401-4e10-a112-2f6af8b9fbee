"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.parseImage = parseImage;
var _loaderUtils = require("@loaders.gl/loader-utils");
var _imageType = require("../category-api/image-type");
var _parsedImageApi = require("../category-api/parsed-image-api");
var _parseToImage = require("./parse-to-image");
var _parseToImageBitmap = require("./parse-to-image-bitmap");
var _parseToNodeImage = require("./parse-to-node-image");
async function parseImage(arrayBuffer, options, context) {
  options = options || {};
  const imageOptions = options.image || {};
  const imageType = imageOptions.type || 'auto';
  const {
    url
  } = context || {};
  const loadType = getLoadableImageType(imageType);
  let image;
  switch (loadType) {
    case 'imagebitmap':
      image = await (0, _parseToImageBitmap.parseToImageBitmap)(arrayBuffer, options, url);
      break;
    case 'image':
      image = await (0, _parseToImage.parseToImage)(arrayBuffer, options, url);
      break;
    case 'data':
      image = await (0, _parseToNodeImage.parseToNodeImage)(arrayBuffer, options);
      break;
    default:
      (0, _loaderUtils.assert)(false);
  }
  if (imageType === 'data') {
    image = (0, _parsedImageApi.getImageData)(image);
  }
  return image;
}
function getLoadableImageType(type) {
  switch (type) {
    case 'auto':
    case 'data':
      return (0, _imageType.getDefaultImageType)();
    default:
      (0, _imageType.isImageTypeSupported)(type);
      return type;
  }
}
//# sourceMappingURL=parse-image.js.map