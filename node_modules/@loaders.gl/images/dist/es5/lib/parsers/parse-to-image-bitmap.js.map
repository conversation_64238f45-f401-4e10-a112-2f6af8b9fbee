{"version": 3, "file": "parse-to-image-bitmap.js", "names": ["_svgUtils", "require", "_parseToImage", "EMPTY_OBJECT", "imagebitmapOptionsSupported", "parseToImageBitmap", "arrayBuffer", "options", "url", "blob", "isSVG", "image", "parseToImage", "getBlob", "imagebitmapOptions", "imagebitmap", "safeCreateImageBitmap", "arguments", "length", "undefined", "isEmptyObject", "createImageBitmap", "error", "console", "warn", "object", "key"], "sources": ["../../../../src/lib/parsers/parse-to-image-bitmap.ts"], "sourcesContent": ["import type {ImageLoaderOptions} from '../../image-loader';\nimport {isSVG, getBlob} from './svg-utils';\nimport {parseToImage} from './parse-to-image';\n\nconst EMPTY_OBJECT = {};\n\nlet imagebitmapOptionsSupported = true;\n\n/**\n * Asynchronously parses an array buffer into an ImageBitmap - this contains the decoded data\n * ImageBitmaps are supported on worker threads, but not supported on Edge, IE11 and Safari\n * https://developer.mozilla.org/en-US/docs/Web/API/ImageBitmap#Browser_compatibility\n *\n * TODO - createImageBitmap supports source rect (5 param overload), pass through?\n */\nexport async function parseToImageBitmap(\n  arrayBuffer: ArrayBuffer,\n  options: ImageLoaderOptions,\n  url?: string\n): Promise<ImageBitmap> {\n  let blob;\n\n  // Cannot parse SVG directly to ImageBitmap, parse to Image first\n  if (isSVG(url)) {\n    // Note: this only works on main thread\n    const image = await parseToImage(arrayBuffer, options, url);\n    blob = image;\n  } else {\n    // Create blob from the array buffer\n    blob = getBlob(arrayBuffer, url);\n  }\n\n  const imagebitmapOptions = options && options.imagebitmap;\n\n  return await safeCreateImageBitmap(blob, imagebitmapOptions);\n}\n\n/**\n * Safely creates an imageBitmap with options\n * *\n * Firefox crashes if imagebitmapOptions is supplied\n * Avoid supplying if not provided or supported, remember if not supported\n */\nasync function safeCreateImageBitmap(\n  blob: Blob,\n  imagebitmapOptions: ImageBitmapOptions | null = null\n): Promise<ImageBitmap> {\n  if (isEmptyObject(imagebitmapOptions) || !imagebitmapOptionsSupported) {\n    imagebitmapOptions = null;\n  }\n\n  if (imagebitmapOptions) {\n    try {\n      // @ts-ignore Options\n      return await createImageBitmap(blob, imagebitmapOptions);\n    } catch (error) {\n      console.warn(error); // eslint-disable-line\n      imagebitmapOptionsSupported = false;\n    }\n  }\n\n  return await createImageBitmap(blob);\n}\n\nfunction isEmptyObject(object) {\n  // @ts-ignore\n  for (const key in object || EMPTY_OBJECT) {\n    return false;\n  }\n  return true;\n}\n"], "mappings": ";;;;;;AACA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,aAAA,GAAAD,OAAA;AAEA,MAAME,YAAY,GAAG,CAAC,CAAC;AAEvB,IAAIC,2BAA2B,GAAG,IAAI;AAS/B,eAAeC,kBAAkBA,CACtCC,WAAwB,EACxBC,OAA2B,EAC3BC,GAAY,EACU;EACtB,IAAIC,IAAI;EAGR,IAAI,IAAAC,eAAK,EAACF,GAAG,CAAC,EAAE;IAEd,MAAMG,KAAK,GAAG,MAAM,IAAAC,0BAAY,EAACN,WAAW,EAAEC,OAAO,EAAEC,GAAG,CAAC;IAC3DC,IAAI,GAAGE,KAAK;EACd,CAAC,MAAM;IAELF,IAAI,GAAG,IAAAI,iBAAO,EAACP,WAAW,EAAEE,GAAG,CAAC;EAClC;EAEA,MAAMM,kBAAkB,GAAGP,OAAO,IAAIA,OAAO,CAACQ,WAAW;EAEzD,OAAO,MAAMC,qBAAqB,CAACP,IAAI,EAAEK,kBAAkB,CAAC;AAC9D;AAQA,eAAeE,qBAAqBA,CAClCP,IAAU,EAEY;EAAA,IADtBK,kBAA6C,GAAAG,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAEpD,IAAIG,aAAa,CAACN,kBAAkB,CAAC,IAAI,CAACV,2BAA2B,EAAE;IACrEU,kBAAkB,GAAG,IAAI;EAC3B;EAEA,IAAIA,kBAAkB,EAAE;IACtB,IAAI;MAEF,OAAO,MAAMO,iBAAiB,CAACZ,IAAI,EAAEK,kBAAkB,CAAC;IAC1D,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAACF,KAAK,CAAC;MACnBlB,2BAA2B,GAAG,KAAK;IACrC;EACF;EAEA,OAAO,MAAMiB,iBAAiB,CAACZ,IAAI,CAAC;AACtC;AAEA,SAASW,aAAaA,CAACK,MAAM,EAAE;EAE7B,KAAK,MAAMC,GAAG,IAAID,MAAM,IAAItB,YAAY,EAAE;IACxC,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb"}