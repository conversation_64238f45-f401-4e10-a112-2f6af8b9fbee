{"version": 3, "file": "svg-utils.js", "names": ["SVG_DATA_URL_PATTERN", "SVG_URL_PATTERN", "isSVG", "url", "test", "getBlobOrSVGDataUrl", "arrayBuffer", "textDecoder", "TextDecoder", "xmlText", "decode", "unescape", "encodeURIComponent", "error", "Error", "message", "src", "concat", "btoa", "getBlob", "Blob", "Uint8Array"], "sources": ["../../../../src/lib/parsers/svg-utils.ts"], "sourcesContent": ["// SVG parsing has limitations, e.g:\n// https://bugs.chromium.org/p/chromium/issues/detail?id=606319\n\nconst SVG_DATA_URL_PATTERN = /^data:image\\/svg\\+xml/;\nconst SVG_URL_PATTERN = /\\.svg((\\?|#).*)?$/;\n\nexport function isSVG(url) {\n  return url && (SVG_DATA_URL_PATTERN.test(url) || SVG_URL_PATTERN.test(url));\n}\n\nexport function getBlobOrSVGDataUrl(arrayBuffer: ArrayBuffer, url?: string): Blob | string {\n  if (isSVG(url)) {\n    // Prepare a properly tagged data URL, and load using normal mechanism\n    const textDecoder = new TextDecoder();\n    let xmlText = textDecoder.decode(arrayBuffer);\n    // TODO Escape in browser to support e.g. Chinese characters\n    try {\n      if (typeof unescape === 'function' && typeof encodeURIComponent === 'function') {\n        xmlText = unescape(encodeURIComponent(xmlText));\n      }\n    } catch (error) {\n      throw new Error((error as Error).message);\n    }\n    // base64 encoding is safer. utf-8 fails in some browsers\n    const src = `data:image/svg+xml;base64,${btoa(xmlText)}`;\n    return src;\n  }\n  return getBlob(arrayBuffer, url);\n}\n\nexport function getBlob(arrayBuffer: ArrayBuffer, url?: string): Blob {\n  if (isSVG(url)) {\n    // https://bugs.chromium.org/p/chromium/issues/detail?id=606319\n    // return new Blob([new Uint8Array(arrayBuffer)], {type: 'image/svg+xml'});\n    throw new Error('SVG cannot be parsed directly to imagebitmap');\n  }\n  // TODO - how to determine mime type? Param? Sniff here?\n  return new Blob([new Uint8Array(arrayBuffer)]); // MIME type not needed?\n}\n"], "mappings": ";;;;;;;;AAGA,MAAMA,oBAAoB,GAAG,uBAAuB;AACpD,MAAMC,eAAe,GAAG,mBAAmB;AAEpC,SAASC,KAAKA,CAACC,GAAG,EAAE;EACzB,OAAOA,GAAG,KAAKH,oBAAoB,CAACI,IAAI,CAACD,GAAG,CAAC,IAAIF,eAAe,CAACG,IAAI,CAACD,GAAG,CAAC,CAAC;AAC7E;AAEO,SAASE,mBAAmBA,CAACC,WAAwB,EAAEH,GAAY,EAAiB;EACzF,IAAID,KAAK,CAACC,GAAG,CAAC,EAAE;IAEd,MAAMI,WAAW,GAAG,IAAIC,WAAW,CAAC,CAAC;IACrC,IAAIC,OAAO,GAAGF,WAAW,CAACG,MAAM,CAACJ,WAAW,CAAC;IAE7C,IAAI;MACF,IAAI,OAAOK,QAAQ,KAAK,UAAU,IAAI,OAAOC,kBAAkB,KAAK,UAAU,EAAE;QAC9EH,OAAO,GAAGE,QAAQ,CAACC,kBAAkB,CAACH,OAAO,CAAC,CAAC;MACjD;IACF,CAAC,CAAC,OAAOI,KAAK,EAAE;MACd,MAAM,IAAIC,KAAK,CAAED,KAAK,CAAWE,OAAO,CAAC;IAC3C;IAEA,MAAMC,GAAG,gCAAAC,MAAA,CAAgCC,IAAI,CAACT,OAAO,CAAC,CAAE;IACxD,OAAOO,GAAG;EACZ;EACA,OAAOG,OAAO,CAACb,WAAW,EAAEH,GAAG,CAAC;AAClC;AAEO,SAASgB,OAAOA,CAACb,WAAwB,EAAEH,GAAY,EAAQ;EACpE,IAAID,KAAK,CAACC,GAAG,CAAC,EAAE;IAGd,MAAM,IAAIW,KAAK,CAAC,8CAA8C,CAAC;EACjE;EAEA,OAAO,IAAIM,IAAI,CAAC,CAAC,IAAIC,UAAU,CAACf,WAAW,CAAC,CAAC,CAAC;AAChD"}