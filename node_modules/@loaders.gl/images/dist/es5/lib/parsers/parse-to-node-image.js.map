{"version": 3, "file": "parse-to-node-image.js", "names": ["_loaderUtils", "require", "_binaryImageApi", "parseToNodeImage", "arrayBuffer", "options", "mimeType", "getBinaryImageMetadata", "_parseImageNode", "globalThis", "assert"], "sources": ["../../../../src/lib/parsers/parse-to-node-image.ts"], "sourcesContent": ["import type {ImageLoaderOptions} from '../../image-loader';\nimport type {ImageDataType} from '../../types';\nimport {assert} from '@loaders.gl/loader-utils';\nimport {getBinaryImageMetadata} from '../category-api/binary-image-api';\n\n// Note: These types should be consistent with loaders.gl/polyfills\n\ntype NDArray = {\n  shape: number[];\n  data: Uint8Array;\n  width: number;\n  height: number;\n  components: number;\n  layers: number[];\n};\n\ntype ParseImageNode = (arrayBuffer: ArrayBuffer, mimeType: string) => Promise<NDArray>;\n\n// Use polyfills if installed to parsed image using get-pixels\nexport async function parseToNodeImage(\n  arrayBuffer: ArrayBuffer,\n  options: ImageLoaderOptions\n): Promise<ImageDataType> {\n  const {mimeType} = getBinaryImageMetadata(arrayBuffer) || {};\n\n  // @ts-ignore\n  const _parseImageNode: ParseImageNode = globalThis._parseImageNode;\n  assert(_parseImageNode); // '@loaders.gl/polyfills not installed'\n\n  // @ts-expect-error TODO should we throw error in this case?\n  return await _parseImageNode(arrayBuffer, mimeType);\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,eAAA,GAAAD,OAAA;AAgBO,eAAeE,gBAAgBA,CACpCC,WAAwB,EACxBC,OAA2B,EACH;EACxB,MAAM;IAACC;EAAQ,CAAC,GAAG,IAAAC,sCAAsB,EAACH,WAAW,CAAC,IAAI,CAAC,CAAC;EAG5D,MAAMI,eAA+B,GAAGC,UAAU,CAACD,eAAe;EAClE,IAAAE,mBAAM,EAACF,eAAe,CAAC;EAGvB,OAAO,MAAMA,eAAe,CAACJ,WAAW,EAAEE,QAAQ,CAAC;AACrD"}