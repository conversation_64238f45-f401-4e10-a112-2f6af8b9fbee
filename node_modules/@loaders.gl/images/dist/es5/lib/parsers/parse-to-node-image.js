"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.parseToNodeImage = parseToNodeImage;
var _loaderUtils = require("@loaders.gl/loader-utils");
var _binaryImageApi = require("../category-api/binary-image-api");
async function parseToNodeImage(arrayBuffer, options) {
  const {
    mimeType
  } = (0, _binaryImageApi.getBinaryImageMetadata)(arrayBuffer) || {};
  const _parseImageNode = globalThis._parseImageNode;
  (0, _loaderUtils.assert)(_parseImageNode);
  return await _parseImageNode(arrayBuffer, mimeType);
}
//# sourceMappingURL=parse-to-node-image.js.map