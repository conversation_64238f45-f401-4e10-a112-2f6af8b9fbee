"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.parseToImageBitmap = parseToImageBitmap;
var _svgUtils = require("./svg-utils");
var _parseToImage = require("./parse-to-image");
const EMPTY_OBJECT = {};
let imagebitmapOptionsSupported = true;
async function parseToImageBitmap(arrayBuffer, options, url) {
  let blob;
  if ((0, _svgUtils.isSVG)(url)) {
    const image = await (0, _parseToImage.parseToImage)(arrayBuffer, options, url);
    blob = image;
  } else {
    blob = (0, _svgUtils.getBlob)(arrayBuffer, url);
  }
  const imagebitmapOptions = options && options.imagebitmap;
  return await safeCreateImageBitmap(blob, imagebitmapOptions);
}
async function safeCreateImageBitmap(blob) {
  let imagebitmapOptions = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
  if (isEmptyObject(imagebitmapOptions) || !imagebitmapOptionsSupported) {
    imagebitmapOptions = null;
  }
  if (imagebitmapOptions) {
    try {
      return await createImageBitmap(blob, imagebitmapOptions);
    } catch (error) {
      console.warn(error);
      imagebitmapOptionsSupported = false;
    }
  }
  return await createImageBitmap(blob);
}
function isEmptyObject(object) {
  for (const key in object || EMPTY_OBJECT) {
    return false;
  }
  return true;
}
//# sourceMappingURL=parse-to-image-bitmap.js.map