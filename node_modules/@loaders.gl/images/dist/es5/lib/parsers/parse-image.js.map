{"version": 3, "file": "parse-image.js", "names": ["_loaderUtils", "require", "_imageType", "_parsedImageApi", "_parseToImage", "_parseToImageBitmap", "_parseToNodeImage", "parseImage", "arrayBuffer", "options", "context", "imageOptions", "image", "imageType", "type", "url", "loadType", "getLoadableImageType", "parseToImageBitmap", "parseToImage", "parseToNodeImage", "assert", "getImageData", "getDefaultImageType", "isImageTypeSupported"], "sources": ["../../../../src/lib/parsers/parse-image.ts"], "sourcesContent": ["import type {LoaderContext} from '@loaders.gl/loader-utils';\nimport {assert} from '@loaders.gl/loader-utils';\nimport type {ImageType} from '../../types';\nimport type {ImageLoaderOptions} from '../../image-loader';\nimport {isImageTypeSupported, getDefaultImageType} from '../category-api/image-type';\nimport {getImageData} from '../category-api/parsed-image-api';\nimport {parseToImage} from './parse-to-image';\nimport {parseToImageBitmap} from './parse-to-image-bitmap';\nimport {parseToNodeImage} from './parse-to-node-image';\n\n// Parse to platform defined image type (data on node, ImageBitmap or HTMLImage on browser)\n// eslint-disable-next-line complexity\nexport async function parseImage(\n  arrayBuffer: ArrayBuffer,\n  options?: ImageLoaderOptions,\n  context?: LoaderContext\n): Promise<ImageType> {\n  options = options || {};\n  const imageOptions = options.image || {};\n\n  // The user can request a specific output format via `options.image.type`\n  const imageType = imageOptions.type || 'auto';\n\n  const {url} = context || {};\n\n  // Note: For options.image.type === `data`, we may still need to load as `image` or `imagebitmap`\n  const loadType = getLoadableImageType(imageType);\n\n  let image;\n  switch (loadType) {\n    case 'imagebitmap':\n      image = await parseToImageBitmap(arrayBuffer, options, url);\n      break;\n    case 'image':\n      image = await parseToImage(arrayBuffer, options, url);\n      break;\n    case 'data':\n      // Node.js loads imagedata directly\n      image = await parseToNodeImage(arrayBuffer, options);\n      break;\n    default:\n      assert(false);\n  }\n\n  // Browser: if options.image.type === 'data', we can now extract data from the loaded image\n  if (imageType === 'data') {\n    image = getImageData(image);\n  }\n\n  return image;\n}\n\n// Get a loadable image type from image type\nfunction getLoadableImageType(type) {\n  switch (type) {\n    case 'auto':\n    case 'data':\n      // Browser: For image data we need still need to load using an image format\n      // Node: the default image type is `data`.\n      return getDefaultImageType();\n    default:\n      // Throw an error if not supported\n      isImageTypeSupported(type);\n      return type;\n  }\n}\n"], "mappings": ";;;;;;AACA,IAAAA,YAAA,GAAAC,OAAA;AAGA,IAAAC,UAAA,GAAAD,OAAA;AACA,IAAAE,eAAA,GAAAF,OAAA;AACA,IAAAG,aAAA,GAAAH,OAAA;AACA,IAAAI,mBAAA,GAAAJ,OAAA;AACA,IAAAK,iBAAA,GAAAL,OAAA;AAIO,eAAeM,UAAUA,CAC9BC,WAAwB,EACxBC,OAA4B,EAC5BC,OAAuB,EACH;EACpBD,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,MAAME,YAAY,GAAGF,OAAO,CAACG,KAAK,IAAI,CAAC,CAAC;EAGxC,MAAMC,SAAS,GAAGF,YAAY,CAACG,IAAI,IAAI,MAAM;EAE7C,MAAM;IAACC;EAAG,CAAC,GAAGL,OAAO,IAAI,CAAC,CAAC;EAG3B,MAAMM,QAAQ,GAAGC,oBAAoB,CAACJ,SAAS,CAAC;EAEhD,IAAID,KAAK;EACT,QAAQI,QAAQ;IACd,KAAK,aAAa;MAChBJ,KAAK,GAAG,MAAM,IAAAM,sCAAkB,EAACV,WAAW,EAAEC,OAAO,EAAEM,GAAG,CAAC;MAC3D;IACF,KAAK,OAAO;MACVH,KAAK,GAAG,MAAM,IAAAO,0BAAY,EAACX,WAAW,EAAEC,OAAO,EAAEM,GAAG,CAAC;MACrD;IACF,KAAK,MAAM;MAETH,KAAK,GAAG,MAAM,IAAAQ,kCAAgB,EAACZ,WAAW,EAAEC,OAAO,CAAC;MACpD;IACF;MACE,IAAAY,mBAAM,EAAC,KAAK,CAAC;EACjB;EAGA,IAAIR,SAAS,KAAK,MAAM,EAAE;IACxBD,KAAK,GAAG,IAAAU,4BAAY,EAACV,KAAK,CAAC;EAC7B;EAEA,OAAOA,KAAK;AACd;AAGA,SAASK,oBAAoBA,CAACH,IAAI,EAAE;EAClC,QAAQA,IAAI;IACV,KAAK,MAAM;IACX,KAAK,MAAM;MAGT,OAAO,IAAAS,8BAAmB,EAAC,CAAC;IAC9B;MAEE,IAAAC,+BAAoB,EAACV,IAAI,CAAC;MAC1B,OAAOA,IAAI;EACf;AACF"}