{"version": 3, "file": "load-image.js", "names": ["_loaderUtils", "require", "_parseImage", "_parsedImageApi", "_generateUrl", "_deepLoad", "loadImage", "getUrl", "options", "arguments", "length", "undefined", "imageUrls", "getImageUrls", "deepLoad", "parseImage", "urlOptions", "mipLevels", "image", "getMipmappedImageUrls", "generateUrl", "urls", "url", "lod", "shallowLoad", "width", "height", "getImageSize", "getMipLevels", "push", "assert", "mipLevel", "_ref", "Math", "floor", "log2", "max"], "sources": ["../../../../src/lib/texture-api/load-image.ts"], "sourcesContent": ["import {assert} from '@loaders.gl/loader-utils';\nimport {parseImage} from '../parsers/parse-image';\nimport {getImageSize} from '../category-api/parsed-image-api';\nimport {generateUrl} from './generate-url';\nimport {deepLoad, shallowLoad} from './deep-load';\n\nexport async function loadImage(getUrl, options = {}) {\n  const imageUrls = await getImageUrls(getUrl, options);\n  return await deepLoad(imageUrls, parseImage, options);\n}\n\nexport async function getImageUrls(getUrl, options, urlOptions = {}) {\n  const mipLevels = (options && options.image && options.image.mipLevels) || 0;\n  return mipLevels !== 0\n    ? await getMipmappedImageUrls(getUrl, mipLevels, options, urlOptions)\n    : generateUrl(getUrl, options, urlOptions);\n}\n\nasync function getMipmappedImageUrls(getUrl, mipLevels, options, urlOptions) {\n  const urls: string[] = [];\n\n  // If no mip levels supplied, we need to load the level 0 image and calculate based on size\n  if (mipLevels === 'auto') {\n    const url = generateUrl(getUrl, options, {...urlOptions, lod: 0});\n    const image = await shallowLoad(url, parseImage, options);\n\n    const {width, height} = getImageSize(image);\n    mipLevels = getMipLevels({width, height});\n\n    // TODO - push image and make `deepLoad` pass through non-url values, avoid loading twice?\n    urls.push(url);\n  }\n\n  // We now know how many mipLevels we need, remaining image urls can now be constructed\n  assert(mipLevels > 0);\n\n  for (let mipLevel = urls.length; mipLevel < mipLevels; ++mipLevel) {\n    const url = generateUrl(getUrl, options, {...urlOptions, lod: mipLevel});\n    urls.push(url);\n  }\n\n  return urls;\n}\n\n// Calculates number of mipmaps based on texture size (log2)\nexport function getMipLevels({width, height}) {\n  return 1 + Math.floor(Math.log2(Math.max(width, height)));\n}\n"], "mappings": ";;;;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AACA,IAAAE,eAAA,GAAAF,OAAA;AACA,IAAAG,YAAA,GAAAH,OAAA;AACA,IAAAI,SAAA,GAAAJ,OAAA;AAEO,eAAeK,SAASA,CAACC,MAAM,EAAgB;EAAA,IAAdC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAClD,MAAMG,SAAS,GAAG,MAAMC,YAAY,CAACN,MAAM,EAAEC,OAAO,CAAC;EACrD,OAAO,MAAM,IAAAM,kBAAQ,EAACF,SAAS,EAAEG,sBAAU,EAAEP,OAAO,CAAC;AACvD;AAEO,eAAeK,YAAYA,CAACN,MAAM,EAAEC,OAAO,EAAmB;EAAA,IAAjBQ,UAAU,GAAAP,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACjE,MAAMQ,SAAS,GAAIT,OAAO,IAAIA,OAAO,CAACU,KAAK,IAAIV,OAAO,CAACU,KAAK,CAACD,SAAS,IAAK,CAAC;EAC5E,OAAOA,SAAS,KAAK,CAAC,GAClB,MAAME,qBAAqB,CAACZ,MAAM,EAAEU,SAAS,EAAET,OAAO,EAAEQ,UAAU,CAAC,GACnE,IAAAI,wBAAW,EAACb,MAAM,EAAEC,OAAO,EAAEQ,UAAU,CAAC;AAC9C;AAEA,eAAeG,qBAAqBA,CAACZ,MAAM,EAAEU,SAAS,EAAET,OAAO,EAAEQ,UAAU,EAAE;EAC3E,MAAMK,IAAc,GAAG,EAAE;EAGzB,IAAIJ,SAAS,KAAK,MAAM,EAAE;IACxB,MAAMK,GAAG,GAAG,IAAAF,wBAAW,EAACb,MAAM,EAAEC,OAAO,EAAE;MAAC,GAAGQ,UAAU;MAAEO,GAAG,EAAE;IAAC,CAAC,CAAC;IACjE,MAAML,KAAK,GAAG,MAAM,IAAAM,qBAAW,EAACF,GAAG,EAAEP,sBAAU,EAAEP,OAAO,CAAC;IAEzD,MAAM;MAACiB,KAAK;MAAEC;IAAM,CAAC,GAAG,IAAAC,4BAAY,EAACT,KAAK,CAAC;IAC3CD,SAAS,GAAGW,YAAY,CAAC;MAACH,KAAK;MAAEC;IAAM,CAAC,CAAC;IAGzCL,IAAI,CAACQ,IAAI,CAACP,GAAG,CAAC;EAChB;EAGA,IAAAQ,mBAAM,EAACb,SAAS,GAAG,CAAC,CAAC;EAErB,KAAK,IAAIc,QAAQ,GAAGV,IAAI,CAACX,MAAM,EAAEqB,QAAQ,GAAGd,SAAS,EAAE,EAAEc,QAAQ,EAAE;IACjE,MAAMT,GAAG,GAAG,IAAAF,wBAAW,EAACb,MAAM,EAAEC,OAAO,EAAE;MAAC,GAAGQ,UAAU;MAAEO,GAAG,EAAEQ;IAAQ,CAAC,CAAC;IACxEV,IAAI,CAACQ,IAAI,CAACP,GAAG,CAAC;EAChB;EAEA,OAAOD,IAAI;AACb;AAGO,SAASO,YAAYA,CAAAI,IAAA,EAAkB;EAAA,IAAjB;IAACP,KAAK;IAAEC;EAAM,CAAC,GAAAM,IAAA;EAC1C,OAAO,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,IAAI,CAACF,IAAI,CAACG,GAAG,CAACX,KAAK,EAAEC,MAAM,CAAC,CAAC,CAAC;AAC3D"}