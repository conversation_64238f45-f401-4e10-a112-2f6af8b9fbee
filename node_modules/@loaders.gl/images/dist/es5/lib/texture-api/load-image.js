"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getImageUrls = getImageUrls;
exports.getMipLevels = getMipLevels;
exports.loadImage = loadImage;
var _loaderUtils = require("@loaders.gl/loader-utils");
var _parseImage = require("../parsers/parse-image");
var _parsedImageApi = require("../category-api/parsed-image-api");
var _generateUrl = require("./generate-url");
var _deepLoad = require("./deep-load");
async function loadImage(getUrl) {
  let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  const imageUrls = await getImageUrls(getUrl, options);
  return await (0, _deepLoad.deepLoad)(imageUrls, _parseImage.parseImage, options);
}
async function getImageUrls(getUrl, options) {
  let urlOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
  const mipLevels = options && options.image && options.image.mipLevels || 0;
  return mipLevels !== 0 ? await getMipmappedImageUrls(getUrl, mipLevels, options, urlOptions) : (0, _generateUrl.generateUrl)(getUrl, options, urlOptions);
}
async function getMipmappedImageUrls(getUrl, mipLevels, options, urlOptions) {
  const urls = [];
  if (mipLevels === 'auto') {
    const url = (0, _generateUrl.generateUrl)(getUrl, options, {
      ...urlOptions,
      lod: 0
    });
    const image = await (0, _deepLoad.shallowLoad)(url, _parseImage.parseImage, options);
    const {
      width,
      height
    } = (0, _parsedImageApi.getImageSize)(image);
    mipLevels = getMipLevels({
      width,
      height
    });
    urls.push(url);
  }
  (0, _loaderUtils.assert)(mipLevels > 0);
  for (let mipLevel = urls.length; mipLevel < mipLevels; ++mipLevel) {
    const url = (0, _generateUrl.generateUrl)(getUrl, options, {
      ...urlOptions,
      lod: mipLevel
    });
    urls.push(url);
  }
  return urls;
}
function getMipLevels(_ref) {
  let {
    width,
    height
  } = _ref;
  return 1 + Math.floor(Math.log2(Math.max(width, height)));
}
//# sourceMappingURL=load-image.js.map