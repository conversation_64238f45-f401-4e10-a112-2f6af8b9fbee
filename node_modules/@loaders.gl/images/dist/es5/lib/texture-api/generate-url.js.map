{"version": 3, "file": "generate-url.js", "names": ["_loaderUtils", "require", "generateUrl", "getUrl", "options", "urlOptions", "url", "assert", "baseUrl", "length", "concat", "<PERSON><PERSON><PERSON>"], "sources": ["../../../../src/lib/texture-api/generate-url.ts"], "sourcesContent": ["import {resolvePath, assert} from '@loaders.gl/loader-utils';\n\n// Generate a url by calling getUrl with mix of options, applying options.baseUrl\nexport function generateUrl(getUrl, options, urlOptions) {\n  // Get url\n  let url = getUrl;\n  if (typeof getUrl === 'function') {\n    url = getUrl({...options, ...urlOptions});\n  }\n  assert(typeof url === 'string');\n\n  // Apply options.baseUrl\n  const {baseUrl} = options;\n  if (baseUrl) {\n    url = baseUrl[baseUrl.length - 1] === '/' ? `${baseUrl}${url}` : `${baseUrl}/${url}`;\n  }\n\n  return resolvePath(url);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAGO,SAASC,WAAWA,CAACC,MAAM,EAAEC,OAAO,EAAEC,UAAU,EAAE;EAEvD,IAAIC,GAAG,GAAGH,MAAM;EAChB,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;IAChCG,GAAG,GAAGH,MAAM,CAAC;MAAC,GAAGC,OAAO;MAAE,GAAGC;IAAU,CAAC,CAAC;EAC3C;EACA,IAAAE,mBAAM,EAAC,OAAOD,GAAG,KAAK,QAAQ,CAAC;EAG/B,MAAM;IAACE;EAAO,CAAC,GAAGJ,OAAO;EACzB,IAAII,OAAO,EAAE;IACXF,GAAG,GAAGE,OAAO,CAACA,OAAO,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,MAAAC,MAAA,CAAMF,OAAO,EAAAE,MAAA,CAAGJ,GAAG,OAAAI,MAAA,CAAQF,OAAO,OAAAE,MAAA,CAAIJ,GAAG,CAAE;EACtF;EAEA,OAAO,IAAAK,wBAAW,EAACL,GAAG,CAAC;AACzB"}