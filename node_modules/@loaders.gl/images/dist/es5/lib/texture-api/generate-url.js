"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.generateUrl = generateUrl;
var _loaderUtils = require("@loaders.gl/loader-utils");
function generateUrl(getUrl, options, urlOptions) {
  let url = getUrl;
  if (typeof getUrl === 'function') {
    url = getUrl({
      ...options,
      ...urlOptions
    });
  }
  (0, _loaderUtils.assert)(typeof url === 'string');
  const {
    baseUrl
  } = options;
  if (baseUrl) {
    url = baseUrl[baseUrl.length - 1] === '/' ? "".concat(baseUrl).concat(url) : "".concat(baseUrl, "/").concat(url);
  }
  return (0, _loaderUtils.resolvePath)(url);
}
//# sourceMappingURL=generate-url.js.map