{"version": 3, "file": "deep-load.js", "names": ["_asyncDeepMap", "require", "deepLoad", "urlTree", "load", "options", "asyncDeepMap", "url", "shallowLoad", "response", "fetch", "arrayBuffer"], "sources": ["../../../../src/lib/texture-api/deep-load.ts"], "sourcesContent": ["import {asyncDeepMap} from './async-deep-map';\n\nexport async function deepLoad(urlTree, load, options) {\n  return await asyncDeepMap(urlTree, (url) => shallowLoad(url, load, options));\n}\n\nexport async function shallowLoad(url, load, options) {\n  // console.error('loading', url);\n  const response = await fetch(url, options.fetch);\n  const arrayBuffer = await response.arrayBuffer();\n  return await load(arrayBuffer, options);\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,aAAA,GAAAC,OAAA;AAEO,eAAeC,QAAQA,CAACC,OAAO,EAAEC,IAAI,EAAEC,OAAO,EAAE;EACrD,OAAO,MAAM,IAAAC,0BAAY,EAACH,OAAO,EAAGI,GAAG,IAAKC,WAAW,CAACD,GAAG,EAAEH,IAAI,EAAEC,OAAO,CAAC,CAAC;AAC9E;AAEO,eAAeG,WAAWA,CAACD,GAAG,EAAEH,IAAI,EAAEC,OAAO,EAAE;EAEpD,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAACH,GAAG,EAAEF,OAAO,CAACK,KAAK,CAAC;EAChD,MAAMC,WAAW,GAAG,MAAMF,QAAQ,CAACE,WAAW,CAAC,CAAC;EAChD,OAAO,MAAMP,IAAI,CAACO,WAAW,EAAEN,OAAO,CAAC;AACzC"}