{"version": 3, "file": "async-deep-map.js", "names": ["isObject", "value", "asyncDeepMap", "tree", "func", "options", "arguments", "length", "undefined", "mapSubtree", "object", "Array", "isArray", "mapArray", "mapObject", "url", "promises", "values", "key", "promise", "then", "push", "Promise", "all", "urlArray", "map"], "sources": ["../../../../src/lib/texture-api/async-deep-map.ts"], "sourcesContent": ["/*\nAsynchronously maps a deep structure of values (e.g. objects and arrays of urls).\n\nE.g. a mipmapped cubemap\n{\n  [CUBE_FACE_FRONT]: [\n    \"image-front-0.jpg\",\n    \"image-front-1.jpg\",\n    \"image-front-2.jpg\",\n  ],\n  [CUBE_MAP_BACK]: [\n    ...\n  ]\n}\n*/\n\nconst isObject = (value) => value && typeof value === 'object';\n\n// Loads a deep structure of urls (objects and arrays of urls)\n// Returns an object with six key-value pairs containing the images (or image mip arrays)\n// for each cube face\nexport async function asyncDeepMap(tree, func, options = {}) {\n  return await mapSubtree(tree, func, options);\n}\n\nexport async function mapSubtree(object, func, options) {\n  if (Array.isArray(object)) {\n    return await mapArray(object, func, options);\n  }\n\n  if (isObject(object)) {\n    return await mapObject(object, func, options);\n  }\n\n  // TODO - ignore non-urls, non-arraybuffers?\n  const url = object;\n  return await func(url, options);\n}\n\n// HELPERS\n\nasync function mapObject(object, func, options) {\n  const promises: Promise<any>[] = [];\n  const values = {};\n\n  for (const key in object) {\n    const url = object[key];\n    const promise = mapSubtree(url, func, options).then((value) => {\n      values[key] = value;\n    });\n    promises.push(promise);\n  }\n\n  await Promise.all(promises);\n\n  return values;\n}\n\nasync function mapArray(urlArray, func, options = {}) {\n  const promises = urlArray.map((url) => mapSubtree(url, func, options));\n  return await Promise.all(promises);\n}\n"], "mappings": ";;;;;;;AAgBA,MAAMA,QAAQ,GAAIC,KAAK,IAAKA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ;AAKvD,eAAeC,YAAYA,CAACC,IAAI,EAAEC,IAAI,EAAgB;EAAA,IAAdC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACzD,OAAO,MAAMG,UAAU,CAACN,IAAI,EAAEC,IAAI,EAAEC,OAAO,CAAC;AAC9C;AAEO,eAAeI,UAAUA,CAACC,MAAM,EAAEN,IAAI,EAAEC,OAAO,EAAE;EACtD,IAAIM,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;IACzB,OAAO,MAAMG,QAAQ,CAACH,MAAM,EAAEN,IAAI,EAAEC,OAAO,CAAC;EAC9C;EAEA,IAAIL,QAAQ,CAACU,MAAM,CAAC,EAAE;IACpB,OAAO,MAAMI,SAAS,CAACJ,MAAM,EAAEN,IAAI,EAAEC,OAAO,CAAC;EAC/C;EAGA,MAAMU,GAAG,GAAGL,MAAM;EAClB,OAAO,MAAMN,IAAI,CAACW,GAAG,EAAEV,OAAO,CAAC;AACjC;AAIA,eAAeS,SAASA,CAACJ,MAAM,EAAEN,IAAI,EAAEC,OAAO,EAAE;EAC9C,MAAMW,QAAwB,GAAG,EAAE;EACnC,MAAMC,MAAM,GAAG,CAAC,CAAC;EAEjB,KAAK,MAAMC,GAAG,IAAIR,MAAM,EAAE;IACxB,MAAMK,GAAG,GAAGL,MAAM,CAACQ,GAAG,CAAC;IACvB,MAAMC,OAAO,GAAGV,UAAU,CAACM,GAAG,EAAEX,IAAI,EAAEC,OAAO,CAAC,CAACe,IAAI,CAAEnB,KAAK,IAAK;MAC7DgB,MAAM,CAACC,GAAG,CAAC,GAAGjB,KAAK;IACrB,CAAC,CAAC;IACFe,QAAQ,CAACK,IAAI,CAACF,OAAO,CAAC;EACxB;EAEA,MAAMG,OAAO,CAACC,GAAG,CAACP,QAAQ,CAAC;EAE3B,OAAOC,MAAM;AACf;AAEA,eAAeJ,QAAQA,CAACW,QAAQ,EAAEpB,IAAI,EAAgB;EAAA,IAAdC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAClD,MAAMU,QAAQ,GAAGQ,QAAQ,CAACC,GAAG,CAAEV,GAAG,IAAKN,UAAU,CAACM,GAAG,EAAEX,IAAI,EAAEC,OAAO,CAAC,CAAC;EACtE,OAAO,MAAMiB,OAAO,CAACC,GAAG,CAACP,QAAQ,CAAC;AACpC"}