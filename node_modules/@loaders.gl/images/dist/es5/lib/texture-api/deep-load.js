"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.deepLoad = deepLoad;
exports.shallowLoad = shallowLoad;
var _asyncDeepMap = require("./async-deep-map");
async function deepLoad(urlTree, load, options) {
  return await (0, _asyncDeepMap.asyncDeepMap)(urlTree, url => shallowLoad(url, load, options));
}
async function shallowLoad(url, load, options) {
  const response = await fetch(url, options.fetch);
  const arrayBuffer = await response.arrayBuffer();
  return await load(arrayBuffer, options);
}
//# sourceMappingURL=deep-load.js.map