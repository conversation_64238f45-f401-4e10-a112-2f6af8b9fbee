{"version": 3, "file": "encode-image.js", "names": ["_parsedImageApi", "require", "_encodeImageNode", "globalThis", "encodeImage", "image", "options", "type", "mimeType", "encodeImageInBrowser", "qualityParamSupported", "jpegQuality", "width", "height", "getImageSize", "canvas", "document", "createElement", "drawImageToCanvas", "blob", "Promise", "resolve", "toBlob", "error", "Error", "arrayBuffer", "x", "arguments", "length", "undefined", "y", "ImageBitmap", "context", "getContext", "transferFromImageBitmap", "data", "clampedArray", "Uint8ClampedArray", "imageData", "ImageData", "putImageData", "drawImage"], "sources": ["../../../../src/lib/encoders/encode-image.ts"], "sourcesContent": ["// Image loading/saving for browser and Node.js\nimport {getImageSize} from '../category-api/parsed-image-api';\n\n// @ts-ignore TS2339: Property does not exist on type\nconst {_encodeImageNode} = globalThis;\n\n/**\n * Returns data bytes representing a compressed image in PNG or JPG format,\n * This data can be saved using file system (f) methods or used in a request.\n * @param image - ImageBitmap Image or Canvas\n * @param options\n * param opt.type='png' - png, jpg or image/png, image/jpg are valid\n * param mimeType= - Whether to include a data URI header\n */\nexport async function encodeImage(\n  image: any,\n  options?: {[key: string]: any}\n): Promise<ArrayBuffer> {\n  options = options || {};\n  options.image = options.image || ({} as {[key: string]: any});\n\n  return _encodeImageNode\n    ? _encodeImageNode(image, {type: options.image.mimeType})\n    : encodeImageInBrowser(image, options);\n}\n\n// In case we get exceptions from canvas.toBlob(resolve, type, quality)\nlet qualityParamSupported = true;\n\n/**\n *\n * @param image\n * @param options\n * @note Based on canvas.toBlob\n * @see https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toBlob\n */\nasync function encodeImageInBrowser(image, options) {\n  const {mimeType, jpegQuality} = options.image;\n\n  const {width, height} = getImageSize(image);\n\n  // create a canvas and resize it to the size of our image\n  const canvas = document.createElement('canvas');\n  canvas.width = width;\n  canvas.height = height;\n\n  drawImageToCanvas(image, canvas);\n\n  // The actual encoding is done asynchronously with `canvas.toBlob()`\n  const blob = await new Promise<Blob | null>((resolve) => {\n    // get it back as a Blob\n    if (jpegQuality && qualityParamSupported) {\n      try {\n        canvas.toBlob(resolve, mimeType, jpegQuality);\n        return;\n      } catch (error) {\n        qualityParamSupported = false;\n      }\n    }\n    canvas.toBlob(resolve, mimeType);\n  });\n\n  if (!blob) {\n    throw new Error('image encoding failed');\n  }\n\n  return await blob.arrayBuffer();\n}\n\nfunction drawImageToCanvas(image, canvas, x = 0, y = 0) {\n  // Try optimized path for ImageBitmaps via bitmaprenderer context\n  if (x === 0 && y === 0 && typeof ImageBitmap !== 'undefined' && image instanceof ImageBitmap) {\n    const context = canvas.getContext('bitmaprenderer');\n    if (context) {\n      // transfer the ImageBitmap to it\n      context.transferFromImageBitmap(image);\n      return canvas;\n    }\n  }\n\n  // Available on most platforms, except IE11 and Andriod WebViews...\n  const context = canvas.getContext('2d');\n  if (image.data) {\n    // ImageData constructor expects clamped array even though getImageData does not return a clamped array...\n    const clampedArray = new Uint8ClampedArray(image.data);\n    const imageData = new ImageData(clampedArray, image.width, image.height);\n    context.putImageData(imageData, 0, 0);\n    return canvas;\n  }\n\n  // Fall back to generic image/image bitmap rendering path\n  context.drawImage(image, 0, 0);\n  return canvas;\n}\n"], "mappings": ";;;;;;AACA,IAAAA,eAAA,GAAAC,OAAA;AAGA,MAAM;EAACC;AAAgB,CAAC,GAAGC,UAAU;AAU9B,eAAeC,WAAWA,CAC/BC,KAAU,EACVC,OAA8B,EACR;EACtBA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvBA,OAAO,CAACD,KAAK,GAAGC,OAAO,CAACD,KAAK,IAAK,CAAC,CAA0B;EAE7D,OAAOH,gBAAgB,GACnBA,gBAAgB,CAACG,KAAK,EAAE;IAACE,IAAI,EAAED,OAAO,CAACD,KAAK,CAACG;EAAQ,CAAC,CAAC,GACvDC,oBAAoB,CAACJ,KAAK,EAAEC,OAAO,CAAC;AAC1C;AAGA,IAAII,qBAAqB,GAAG,IAAI;AAShC,eAAeD,oBAAoBA,CAACJ,KAAK,EAAEC,OAAO,EAAE;EAClD,MAAM;IAACE,QAAQ;IAAEG;EAAW,CAAC,GAAGL,OAAO,CAACD,KAAK;EAE7C,MAAM;IAACO,KAAK;IAAEC;EAAM,CAAC,GAAG,IAAAC,4BAAY,EAACT,KAAK,CAAC;EAG3C,MAAMU,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/CF,MAAM,CAACH,KAAK,GAAGA,KAAK;EACpBG,MAAM,CAACF,MAAM,GAAGA,MAAM;EAEtBK,iBAAiB,CAACb,KAAK,EAAEU,MAAM,CAAC;EAGhC,MAAMI,IAAI,GAAG,MAAM,IAAIC,OAAO,CAAeC,OAAO,IAAK;IAEvD,IAAIV,WAAW,IAAID,qBAAqB,EAAE;MACxC,IAAI;QACFK,MAAM,CAACO,MAAM,CAACD,OAAO,EAAEb,QAAQ,EAAEG,WAAW,CAAC;QAC7C;MACF,CAAC,CAAC,OAAOY,KAAK,EAAE;QACdb,qBAAqB,GAAG,KAAK;MAC/B;IACF;IACAK,MAAM,CAACO,MAAM,CAACD,OAAO,EAAEb,QAAQ,CAAC;EAClC,CAAC,CAAC;EAEF,IAAI,CAACW,IAAI,EAAE;IACT,MAAM,IAAIK,KAAK,CAAC,uBAAuB,CAAC;EAC1C;EAEA,OAAO,MAAML,IAAI,CAACM,WAAW,CAAC,CAAC;AACjC;AAEA,SAASP,iBAAiBA,CAACb,KAAK,EAAEU,MAAM,EAAgB;EAAA,IAAdW,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEG,CAAC,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAEpD,IAAID,CAAC,KAAK,CAAC,IAAII,CAAC,KAAK,CAAC,IAAI,OAAOC,WAAW,KAAK,WAAW,IAAI1B,KAAK,YAAY0B,WAAW,EAAE;IAC5F,MAAMC,OAAO,GAAGjB,MAAM,CAACkB,UAAU,CAAC,gBAAgB,CAAC;IACnD,IAAID,OAAO,EAAE;MAEXA,OAAO,CAACE,uBAAuB,CAAC7B,KAAK,CAAC;MACtC,OAAOU,MAAM;IACf;EACF;EAGA,MAAMiB,OAAO,GAAGjB,MAAM,CAACkB,UAAU,CAAC,IAAI,CAAC;EACvC,IAAI5B,KAAK,CAAC8B,IAAI,EAAE;IAEd,MAAMC,YAAY,GAAG,IAAIC,iBAAiB,CAAChC,KAAK,CAAC8B,IAAI,CAAC;IACtD,MAAMG,SAAS,GAAG,IAAIC,SAAS,CAACH,YAAY,EAAE/B,KAAK,CAACO,KAAK,EAAEP,KAAK,CAACQ,MAAM,CAAC;IACxEmB,OAAO,CAACQ,YAAY,CAACF,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;IACrC,OAAOvB,MAAM;EACf;EAGAiB,OAAO,CAACS,SAAS,CAACpC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9B,OAAOU,MAAM;AACf"}