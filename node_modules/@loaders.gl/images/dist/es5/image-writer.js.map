{"version": 3, "file": "image-writer.js", "names": ["_version", "require", "_encodeImage", "ImageWriter", "name", "id", "module", "version", "VERSION", "extensions", "options", "image", "mimeType", "jpegQuality", "encode", "encodeImage", "exports"], "sources": ["../../src/image-writer.ts"], "sourcesContent": ["// import type {Writer} from '@loaders.gl/loader-utils';\nimport {VERSION} from './lib/utils/version';\nimport {encodeImage} from './lib/encoders/encode-image';\n\nexport const ImageWriter = {\n  name: 'Images',\n  id: 'image',\n  module: 'images',\n  version: VERSION,\n  extensions: ['jpeg'],\n  options: {\n    image: {\n      mimeType: 'image/png',\n      jpegQuality: null\n    }\n  },\n  encode: encodeImage\n};\n"], "mappings": ";;;;;;AACA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEO,MAAME,WAAW,GAAG;EACzBC,IAAI,EAAE,QAAQ;EACdC,EAAE,EAAE,OAAO;EACXC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAEC,gBAAO;EAChBC,UAAU,EAAE,CAAC,MAAM,CAAC;EACpBC,OAAO,EAAE;IACPC,KAAK,EAAE;MACLC,QAAQ,EAAE,WAAW;MACrBC,WAAW,EAAE;IACf;EACF,CAAC;EACDC,MAAM,EAAEC;AACV,CAAC;AAACC,OAAA,CAAAb,WAAA,GAAAA,WAAA"}