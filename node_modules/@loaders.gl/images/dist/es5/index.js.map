{"version": 3, "file": "index.js", "names": ["_imageLoader", "require", "_imageWriter", "_binaryImageApi", "_imageType", "_parsedImageApi", "_imageFormat", "_loadImage"], "sources": ["../../src/index.ts"], "sourcesContent": ["// TYPES\nexport type {ImageDataType, ImageType, ImageTypeEnum} from './types';\nexport type {ImageLoaderOptions} from './image-loader';\n\n// LOADERS AND WRITERS\nexport {ImageLoader} from './image-loader';\nexport {ImageWriter} from './image-writer';\n\n// IMAGE CATEGORY API\n\n// Binary Image API\nexport {getBinaryImageMetadata} from './lib/category-api/binary-image-api';\n\n// Parsed Image API\nexport {isImageTypeSupported, getDefaultImageType} from './lib/category-api/image-type';\n\nexport {\n  isImage,\n  getImageType,\n  getImageSize,\n  getImageData\n} from './lib/category-api/parsed-image-api';\n\n// EXPERIMENTAL\nexport {getSupportedImageFormats} from './lib/category-api/image-format';\nexport {isImageFormatSupported} from './lib/category-api/image-format';\n\n// DEPRECATED - Remove in V4 (fix dependency in luma.gl)\nexport {loadImage} from './lib/texture-api/load-image';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAKA,IAAAE,eAAA,GAAAF,OAAA;AAGA,IAAAG,UAAA,GAAAH,OAAA;AAEA,IAAAI,eAAA,GAAAJ,OAAA;AAQA,IAAAK,YAAA,GAAAL,OAAA;AAIA,IAAAM,UAAA,GAAAN,OAAA"}