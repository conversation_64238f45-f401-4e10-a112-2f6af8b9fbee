{"version": 3, "file": "image-loader.js", "names": ["_version", "require", "_parseImage", "_binaryImageApi", "EXTENSIONS", "MIME_TYPES", "DEFAULT_IMAGE_LOADER_OPTIONS", "image", "type", "decode", "ImageLoader", "id", "module", "name", "version", "VERSION", "mimeTypes", "extensions", "parse", "parseImage", "tests", "arrayBuffer", "Boolean", "getBinaryImageMetadata", "DataView", "options", "exports", "_typecheckImageLoader"], "sources": ["../../src/image-loader.ts"], "sourcesContent": ["import type {LoaderOptions, LoaderWithParser} from '@loaders.gl/loader-utils';\nimport {VERSION} from './lib/utils/version';\nimport {parseImage} from './lib/parsers/parse-image';\nimport {getBinaryImageMetadata} from './lib/category-api/binary-image-api';\n\nconst EXTENSIONS = ['png', 'jpg', 'jpeg', 'gif', 'webp', 'bmp', 'ico', 'svg', 'avif'];\nconst MIME_TYPES = [\n  'image/png',\n  'image/jpeg',\n  'image/gif',\n  'image/webp',\n  'image/avif',\n  'image/bmp',\n  'image/vnd.microsoft.icon',\n  'image/svg+xml'\n];\n\nexport type ImageLoaderOptions = LoaderOptions & {\n  image?: {\n    type?: 'auto' | 'data' | 'imagebitmap' | 'image';\n    decode?: boolean;\n  };\n  imagebitmap?: ImageBitmapOptions;\n};\n\nconst DEFAULT_IMAGE_LOADER_OPTIONS: ImageLoaderOptions = {\n  image: {\n    type: 'auto',\n    decode: true // if format is HTML\n  }\n  // imagebitmap: {} - passes (platform dependent) parameters to ImageBitmap constructor\n};\n\n/**\n * Loads a platform-specific image type\n * Note: This type can be used as input data to WebGL texture creation\n */\nexport const ImageLoader = {\n  id: 'image',\n  module: 'images',\n  name: 'Images',\n  version: VERSION,\n  mimeTypes: MIME_TYPES,\n  extensions: EXTENSIONS,\n  parse: parseImage,\n  // TODO: byteOffset, byteLength;\n  tests: [(arrayBuffer) => Boolean(getBinaryImageMetadata(new DataView(arrayBuffer)))],\n  options: DEFAULT_IMAGE_LOADER_OPTIONS\n};\n\nexport const _typecheckImageLoader: LoaderWithParser = ImageLoader;\n"], "mappings": ";;;;;;AACA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AACA,IAAAE,eAAA,GAAAF,OAAA;AAEA,MAAMG,UAAU,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;AACrF,MAAMC,UAAU,GAAG,CACjB,WAAW,EACX,YAAY,EACZ,WAAW,EACX,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,0BAA0B,EAC1B,eAAe,CAChB;AAUD,MAAMC,4BAAgD,GAAG;EACvDC,KAAK,EAAE;IACLC,IAAI,EAAE,MAAM;IACZC,MAAM,EAAE;EACV;AAEF,CAAC;AAMM,MAAMC,WAAW,GAAG;EACzBC,EAAE,EAAE,OAAO;EACXC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,QAAQ;EACdC,OAAO,EAAEC,gBAAO;EAChBC,SAAS,EAAEX,UAAU;EACrBY,UAAU,EAAEb,UAAU;EACtBc,KAAK,EAAEC,sBAAU;EAEjBC,KAAK,EAAE,CAAEC,WAAW,IAAKC,OAAO,CAAC,IAAAC,sCAAsB,EAAC,IAAIC,QAAQ,CAACH,WAAW,CAAC,CAAC,CAAC,CAAC;EACpFI,OAAO,EAAEnB;AACX,CAAC;AAACoB,OAAA,CAAAhB,WAAA,GAAAA,WAAA;AAEK,MAAMiB,qBAAuC,GAAGjB,WAAW;AAACgB,OAAA,CAAAC,qBAAA,GAAAA,qBAAA"}