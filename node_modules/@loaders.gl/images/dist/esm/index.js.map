{"version": 3, "file": "index.js", "names": ["ImageLoader", "ImageWriter", "getBinaryImageMetadata", "isImageTypeSupported", "getDefaultImageType", "isImage", "getImageType", "getImageSize", "getImageData", "getSupportedImageFormats", "isImageFormatSupported", "loadImage"], "sources": ["../../src/index.ts"], "sourcesContent": ["// TYPES\nexport type {ImageDataType, ImageType, ImageTypeEnum} from './types';\nexport type {ImageLoaderOptions} from './image-loader';\n\n// LOADERS AND WRITERS\nexport {ImageLoader} from './image-loader';\nexport {ImageWriter} from './image-writer';\n\n// IMAGE CATEGORY API\n\n// Binary Image API\nexport {getBinaryImageMetadata} from './lib/category-api/binary-image-api';\n\n// Parsed Image API\nexport {isImageTypeSupported, getDefaultImageType} from './lib/category-api/image-type';\n\nexport {\n  isImage,\n  getImageType,\n  getImageSize,\n  getImageData\n} from './lib/category-api/parsed-image-api';\n\n// EXPERIMENTAL\nexport {getSupportedImageFormats} from './lib/category-api/image-format';\nexport {isImageFormatSupported} from './lib/category-api/image-format';\n\n// DEPRECATED - Remove in V4 (fix dependency in luma.gl)\nexport {loadImage} from './lib/texture-api/load-image';\n"], "mappings": "AAKA,SAAQA,WAAW,QAAO,gBAAgB;AAC1C,SAAQC,WAAW,QAAO,gBAAgB;AAK1C,SAAQC,sBAAsB,QAAO,qCAAqC;AAG1E,SAAQC,oBAAoB,EAAEC,mBAAmB,QAAO,+BAA+B;AAEvF,SACEC,OAAO,EACPC,YAAY,EACZC,YAAY,EACZC,YAAY,QACP,qCAAqC;AAG5C,SAAQC,wBAAwB,QAAO,iCAAiC;AACxE,SAAQC,sBAAsB,QAAO,iCAAiC;AAGtE,SAAQC,SAAS,QAAO,8BAA8B"}