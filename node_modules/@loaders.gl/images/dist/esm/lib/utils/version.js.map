{"version": 3, "file": "version.js", "names": ["VERSION"], "sources": ["../../../../src/lib/utils/version.ts"], "sourcesContent": ["// Version constant cannot be imported, it needs to correspond to the build version of **this** module.\n// __VERSION__ is injected by babel-plugin-version-inline\n// @ts-ignore TS2304: Cannot find name '__VERSION__'.\nexport const VERSION = typeof __VERSION__ !== 'undefined' ? __VERSION__ : 'latest';\n"], "mappings": "AAGA,OAAO,MAAMA,OAAO,GAAG,eAAkB,KAAK,WAAW,cAAiB,QAAQ"}