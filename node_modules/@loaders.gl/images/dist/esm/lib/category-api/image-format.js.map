{"version": 3, "file": "image-format.js", "names": ["<PERSON><PERSON><PERSON><PERSON>", "MIME_TYPES", "mimeTypeSupportedPromise", "getSupportedImageFormats", "supportedMimeTypes", "Set", "mimeType", "supported", "checkBrowserImageFormatSupportAsync", "checkNodeImageFormatSupport", "add", "mimeTypeSupportedSync", "isImageFormatSupported", "undefined", "checkBrowserImageFormatSupport", "NODE_FORMAT_SUPPORT", "_parseImageNode", "_imageFormatsNode", "globalThis", "Boolean", "includes", "testBrowserImageFormatSupport", "TEST_IMAGE", "dataURL", "testBrowserImageFormatSupportAsync", "element", "document", "createElement", "toDataURL", "indexOf", "concat", "testImageDataURL", "Promise", "resolve", "image", "Image", "src", "onload", "height", "onerror"], "sources": ["../../../../src/lib/category-api/image-format.ts"], "sourcesContent": ["// loaders.gl, MIT license\n\nimport {isBrowser} from '@loaders.gl/loader-utils';\n\nconst MIME_TYPES = [\n  'image/png',\n  'image/jpeg',\n  'image/gif',\n  'image/webp',\n  'image/avif',\n  'image/tiff',\n  // TODO - what is the correct type for SVG\n  'image/svg',\n  'image/svg+xml',\n  'image/bmp',\n  'image/vnd.microsoft.icon'\n];\n\n/** Only one round of tests is performed */\nconst mimeTypeSupportedPromise: Promise<Set<string>> | null = null;\n\n/** Run-time browser detection of file formats requires async tests for most precise results */\nexport async function getSupportedImageFormats(): Promise<Set<string>> {\n  if (mimeTypeSupportedPromise) {\n    return await mimeTypeSupportedPromise;\n  }\n\n  const supportedMimeTypes = new Set<string>();\n  for (const mimeType of MIME_TYPES) {\n    const supported = isBrowser\n      ? await checkBrowserImageFormatSupportAsync(mimeType)\n      : checkNodeImageFormatSupport(mimeType);\n    if (supported) {\n      supportedMimeTypes.add(mimeType);\n    }\n  }\n\n  return supportedMimeTypes;\n}\n\n/** Cache sync values for speed */\nconst mimeTypeSupportedSync: {[mimeType: string]: boolean} = {};\n\n/**\n * Check if image MIME type is supported. Result is cached to avoid repeated tests.\n */\nexport function isImageFormatSupported(mimeType: string): boolean {\n  if (mimeTypeSupportedSync[mimeType] === undefined) {\n    const supported = isBrowser\n      ? checkBrowserImageFormatSupport(mimeType)\n      : checkNodeImageFormatSupport(mimeType);\n    mimeTypeSupportedSync[mimeType] = supported;\n  }\n  return mimeTypeSupportedSync[mimeType];\n}\n\n/**\n * Checks that polyfills are installed and that mimeType is supported by polyfills\n * @todo Ideally polyfills should declare what formats they support, instead of storing that data here.\n */\nfunction checkNodeImageFormatSupport(mimeType: string): boolean {\n  /** @deprecated Remove these in 4.0 and rely on polyfills to inject them */\n  const NODE_FORMAT_SUPPORT = ['image/png', 'image/jpeg', 'image/gif'];\n  // @ts-ignore\n  const {_parseImageNode, _imageFormatsNode = NODE_FORMAT_SUPPORT} = globalThis;\n  return Boolean(_parseImageNode) && _imageFormatsNode.includes(mimeType);\n}\n\n/** Checks image format support synchronously.\n * @note Unreliable, fails on AVIF\n */\nfunction checkBrowserImageFormatSupport(mimeType: string): boolean {\n  switch (mimeType) {\n    case 'image/avif': // Will fail\n    case 'image/webp':\n      return testBrowserImageFormatSupport(mimeType);\n    default:\n      return true;\n  }\n}\n\nconst TEST_IMAGE = {\n  'image/avif':\n    'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgANogQEAwgMg8f8D///8WfhwB8+ErK42A=',\n  // Lossy test image. Support for lossy images doesn't guarantee support for all WebP images.\n  'image/webp': 'data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA'\n};\n\n/** Checks WebP and AVIF support asynchronously */\nasync function checkBrowserImageFormatSupportAsync(mimeType: string): Promise<boolean> {\n  const dataURL = TEST_IMAGE[mimeType];\n  return dataURL ? await testBrowserImageFormatSupportAsync(dataURL) : true;\n}\n\n/**\n * Checks browser synchronously\n * Checks if toDataURL supports the mimeType.\n * @note Imperfect testOn Chrome this is true for WebP but not for AVIF\n */\nfunction testBrowserImageFormatSupport(mimeType: string): boolean {\n  try {\n    const element = document.createElement('canvas');\n    const dataURL = element.toDataURL(mimeType);\n    return dataURL.indexOf(`data:${mimeType}`) === 0;\n  } catch {\n    // Probably Safari...\n    return false;\n  }\n}\n\n// Check WebPSupport asynchronously\nasync function testBrowserImageFormatSupportAsync(testImageDataURL: string): Promise<boolean> {\n  return new Promise((resolve) => {\n    const image = new Image();\n    image.src = testImageDataURL;\n    image.onload = () => resolve(image.height > 0);\n    image.onerror = () => resolve(false);\n  });\n}\n"], "mappings": "AAEA,SAAQA,SAAS,QAAO,0BAA0B;AAElD,MAAMC,UAAU,GAAG,CACjB,WAAW,EACX,YAAY,EACZ,WAAW,EACX,YAAY,EACZ,YAAY,EACZ,YAAY,EAEZ,WAAW,EACX,eAAe,EACf,WAAW,EACX,0BAA0B,CAC3B;AAGD,MAAMC,wBAAqD,GAAG,IAAI;AAGlE,OAAO,eAAeC,wBAAwBA,CAAA,EAAyB;EACrE,IAAID,wBAAwB,EAAE;IAC5B,OAAO,MAAMA,wBAAwB;EACvC;EAEA,MAAME,kBAAkB,GAAG,IAAIC,GAAG,CAAS,CAAC;EAC5C,KAAK,MAAMC,QAAQ,IAAIL,UAAU,EAAE;IACjC,MAAMM,SAAS,GAAGP,SAAS,GACvB,MAAMQ,mCAAmC,CAACF,QAAQ,CAAC,GACnDG,2BAA2B,CAACH,QAAQ,CAAC;IACzC,IAAIC,SAAS,EAAE;MACbH,kBAAkB,CAACM,GAAG,CAACJ,QAAQ,CAAC;IAClC;EACF;EAEA,OAAOF,kBAAkB;AAC3B;AAGA,MAAMO,qBAAoD,GAAG,CAAC,CAAC;AAK/D,OAAO,SAASC,sBAAsBA,CAACN,QAAgB,EAAW;EAChE,IAAIK,qBAAqB,CAACL,QAAQ,CAAC,KAAKO,SAAS,EAAE;IACjD,MAAMN,SAAS,GAAGP,SAAS,GACvBc,8BAA8B,CAACR,QAAQ,CAAC,GACxCG,2BAA2B,CAACH,QAAQ,CAAC;IACzCK,qBAAqB,CAACL,QAAQ,CAAC,GAAGC,SAAS;EAC7C;EACA,OAAOI,qBAAqB,CAACL,QAAQ,CAAC;AACxC;AAMA,SAASG,2BAA2BA,CAACH,QAAgB,EAAW;EAE9D,MAAMS,mBAAmB,GAAG,CAAC,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC;EAEpE,MAAM;IAACC,eAAe;IAAEC,iBAAiB,GAAGF;EAAmB,CAAC,GAAGG,UAAU;EAC7E,OAAOC,OAAO,CAACH,eAAe,CAAC,IAAIC,iBAAiB,CAACG,QAAQ,CAACd,QAAQ,CAAC;AACzE;AAKA,SAASQ,8BAA8BA,CAACR,QAAgB,EAAW;EACjE,QAAQA,QAAQ;IACd,KAAK,YAAY;IACjB,KAAK,YAAY;MACf,OAAOe,6BAA6B,CAACf,QAAQ,CAAC;IAChD;MACE,OAAO,IAAI;EACf;AACF;AAEA,MAAMgB,UAAU,GAAG;EACjB,YAAY,EACV,ybAAyb;EAE3b,YAAY,EAAE;AAChB,CAAC;AAGD,eAAed,mCAAmCA,CAACF,QAAgB,EAAoB;EACrF,MAAMiB,OAAO,GAAGD,UAAU,CAAChB,QAAQ,CAAC;EACpC,OAAOiB,OAAO,GAAG,MAAMC,kCAAkC,CAACD,OAAO,CAAC,GAAG,IAAI;AAC3E;AAOA,SAASF,6BAA6BA,CAACf,QAAgB,EAAW;EAChE,IAAI;IACF,MAAMmB,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAChD,MAAMJ,OAAO,GAAGE,OAAO,CAACG,SAAS,CAACtB,QAAQ,CAAC;IAC3C,OAAOiB,OAAO,CAACM,OAAO,SAAAC,MAAA,CAASxB,QAAQ,CAAE,CAAC,KAAK,CAAC;EAClD,CAAC,CAAC,MAAM;IAEN,OAAO,KAAK;EACd;AACF;AAGA,eAAekB,kCAAkCA,CAACO,gBAAwB,EAAoB;EAC5F,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;IAC9B,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAAC,CAAC;IACzBD,KAAK,CAACE,GAAG,GAAGL,gBAAgB;IAC5BG,KAAK,CAACG,MAAM,GAAG,MAAMJ,OAAO,CAACC,KAAK,CAACI,MAAM,GAAG,CAAC,CAAC;IAC9CJ,KAAK,CAACK,OAAO,GAAG,MAAMN,OAAO,CAAC,KAAK,CAAC;EACtC,CAAC,CAAC;AACJ"}