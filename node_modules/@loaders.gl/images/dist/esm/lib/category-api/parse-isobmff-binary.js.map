{"version": 3, "file": "parse-isobmff-binary.js", "names": ["getISOBMFFMediaType", "buffer", "checkString", "decodeMajorBrand", "brandMajor", "getUTF8String", "replace", "trim", "extension", "mimeType", "array", "start", "end", "String", "fromCharCode", "slice", "stringToBytes", "string", "map", "character", "charCodeAt", "header", "offset", "arguments", "length", "undefined", "headerBytes", "i"], "sources": ["../../../../src/lib/category-api/parse-isobmff-binary.ts"], "sourcesContent": ["// loaders.gl, MIT license\n// code adapted from https://github.com/sindresorhus/file-type under MIT license\n\n/**\n * Box is a container format that can contain a variety of media related files,\n * so we want to return information about which type of file is actually contained inside\n */\nexport type BoxFileType = {extension: string; mimeType: string};\n\n/**\n * Tests if a buffer is in ISO base media file format (ISOBMFF) @see https://en.wikipedia.org/wiki/ISO_base_media_file_format\n * (ISOBMFF is a media container standard based on the Apple QuickTime container format)\n */\nexport function getISOBMFFMediaType(buffer: Uint8Array): BoxFileType | null {\n  // Almost all ISO base media files start with `ftyp` box. (It's not required to be first, but it's recommended to be.)\n  if (!checkString(buffer, 'ftyp', 4)) {\n    return null;\n  }\n\n  // Extra check: test for 8859-1 printable characters (for simplicity, it's a mask which also catches one non-printable character).\n  if ((buffer[8] & 0x60) === 0x00) {\n    return null;\n  }\n\n  // `ftyp` box must contain a brand major identifier, which must consist of ISO 8859-1 printable characters.\n  return decodeMajorBrand(buffer);\n}\n\n/**\n * brands explained @see https://github.com/strukturag/libheif/issues/83\n * code adapted from @see https://github.com/sindresorhus/file-type/blob/main/core.js#L489-L492\n */\nexport function decodeMajorBrand(buffer: Uint8Array): BoxFileType | null {\n  const brandMajor = getUTF8String(buffer, 8, 12).replace('\\0', ' ').trim();\n\n  switch (brandMajor) {\n    case 'avif':\n    case 'avis':\n      return {extension: 'avif', mimeType: 'image/avif'};\n    default:\n      return null;\n  }\n  // We don't need these now, but they are easy to add\n  // case 'mif1':\n  //   return {extension: 'heic', mimeType: 'image/heif'};\n  // case 'msf1':\n  //   return {extension: 'heic', mimeType: 'image/heif-sequence'};\n  // case 'heic':\n  // case 'heix':\n  //   return {extension: 'heic', mimeType: 'image/heic'};\n  // case 'hevc':\n  // case 'hevx':\n  //   return {extension: 'heic', mimeType: 'image/heic-sequence'};\n  // case 'qt':\n  //   return {ext: 'mov', mime: 'video/quicktime'};\n  // case 'M4V':\n  // case 'M4VH':\n  // case 'M4VP':\n  //   return {ext: 'm4v', mime: 'video/x-m4v'};\n  // case 'M4P':\n  //   return {ext: 'm4p', mime: 'video/mp4'};\n  // case 'M4B':\n  //   return {ext: 'm4b', mime: 'audio/mp4'};\n  // case 'M4A':\n  //   return {ext: 'm4a', mime: 'audio/x-m4a'};\n  // case 'F4V':\n  //   return {ext: 'f4v', mime: 'video/mp4'};\n  // case 'F4P':\n  //   return {ext: 'f4p', mime: 'video/mp4'};\n  // case 'F4A':\n  //   return {ext: 'f4a', mime: 'audio/mp4'};\n  // case 'F4B':\n  //   return {ext: 'f4b', mime: 'audio/mp4'};\n  // case 'crx':\n  //   return {ext: 'cr3', mime: 'image/x-canon-cr3'};\n  // default:\n  // if (brandMajor.startsWith('3g')) {\n  //   if (brandMajor.startsWith('3g2')) {\n  //     return {ext: '3g2', mime: 'video/3gpp2'};\n  //   }\n  //   return {ext: '3gp', mime: 'video/3gpp'};\n  // }\n  // return {ext: 'mp4', mime: 'video/mp4'};\n}\n\n/** Interpret a chunk of bytes as a UTF8 string */\nfunction getUTF8String(array: Uint8Array, start: number, end: number): string {\n  return String.fromCharCode(...array.slice(start, end));\n}\n\nfunction stringToBytes(string: string): number[] {\n  return [...string].map((character) => character.charCodeAt(0));\n}\n\nfunction checkString(buffer: ArrayLike<number>, header: string, offset: number = 0): boolean {\n  const headerBytes = stringToBytes(header);\n\n  for (let i = 0; i < headerBytes.length; ++i) {\n    if (headerBytes[i] !== buffer[i + offset]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n"], "mappings": "AAaA,OAAO,SAASA,mBAAmBA,CAACC,MAAkB,EAAsB;EAE1E,IAAI,CAACC,WAAW,CAACD,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE;IACnC,OAAO,IAAI;EACb;EAGA,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,MAAM,IAAI,EAAE;IAC/B,OAAO,IAAI;EACb;EAGA,OAAOE,gBAAgB,CAACF,MAAM,CAAC;AACjC;AAMA,OAAO,SAASE,gBAAgBA,CAACF,MAAkB,EAAsB;EACvE,MAAMG,UAAU,GAAGC,aAAa,CAACJ,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAACK,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACC,IAAI,CAAC,CAAC;EAEzE,QAAQH,UAAU;IAChB,KAAK,MAAM;IACX,KAAK,MAAM;MACT,OAAO;QAACI,SAAS,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAY,CAAC;IACpD;MACE,OAAO,IAAI;EACf;AA0CF;AAGA,SAASJ,aAAaA,CAACK,KAAiB,EAAEC,KAAa,EAAEC,GAAW,EAAU;EAC5E,OAAOC,MAAM,CAACC,YAAY,CAAC,GAAGJ,KAAK,CAACK,KAAK,CAACJ,KAAK,EAAEC,GAAG,CAAC,CAAC;AACxD;AAEA,SAASI,aAAaA,CAACC,MAAc,EAAY;EAC/C,OAAO,CAAC,GAAGA,MAAM,CAAC,CAACC,GAAG,CAAEC,SAAS,IAAKA,SAAS,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC;AAChE;AAEA,SAASlB,WAAWA,CAACD,MAAyB,EAAEoB,MAAc,EAA+B;EAAA,IAA7BC,MAAc,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAChF,MAAMG,WAAW,GAAGV,aAAa,CAACK,MAAM,CAAC;EAEzC,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,WAAW,CAACF,MAAM,EAAE,EAAEG,CAAC,EAAE;IAC3C,IAAID,WAAW,CAACC,CAAC,CAAC,KAAK1B,MAAM,CAAC0B,CAAC,GAAGL,MAAM,CAAC,EAAE;MACzC,OAAO,KAAK;IACd;EACF;EAEA,OAAO,IAAI;AACb"}