{"version": 3, "file": "image-type.js", "names": ["<PERSON><PERSON><PERSON><PERSON>", "_parseImageNode", "globalThis", "IMAGE_SUPPORTED", "Image", "IMAGE_BITMAP_SUPPORTED", "ImageBitmap", "NODE_IMAGE_SUPPORTED", "Boolean", "DATA_SUPPORTED", "isImageTypeSupported", "type", "Error", "concat", "getDefaultImageType"], "sources": ["../../../../src/lib/category-api/image-type.ts"], "sourcesContent": ["import {isBrowser} from '@loaders.gl/loader-utils';\nimport type {ImageTypeEnum} from '../../types';\n\n// @ts-ignore TS2339: Property does not exist on type\nconst {_parseImageNode} = globalThis;\n\nconst IMAGE_SUPPORTED = typeof Image !== 'undefined'; // NOTE: \"false\" positives if jsdom is installed\nconst IMAGE_BITMAP_SUPPORTED = typeof ImageBitmap !== 'undefined';\nconst NODE_IMAGE_SUPPORTED = Boolean(_parseImageNode);\nconst DATA_SUPPORTED = isBrowser ? true : NODE_IMAGE_SUPPORTED;\n\n/**\n * Checks if a loaders.gl image type is supported\n * @param type image type string\n */\nexport function isImageTypeSupported(type: string): boolean {\n  switch (type) {\n    case 'auto':\n      // Should only ever be false in Node.js, if polyfills have not been installed...\n      return IMAGE_BITMAP_SUPPORTED || IMAGE_SUPPORTED || DATA_SUPPORTED;\n\n    case 'imagebitmap':\n      return IMAGE_BITMAP_SUPPORTED;\n    case 'image':\n      return IMAGE_SUPPORTED;\n    case 'data':\n      return DATA_SUPPORTED;\n\n    default:\n      throw new Error(`@loaders.gl/images: image ${type} not supported in this environment`);\n  }\n}\n\n/**\n * Returns the \"most performant\" supported image type on this platform\n * @returns image type string\n */\nexport function getDefaultImageType(): ImageTypeEnum {\n  if (IMAGE_BITMAP_SUPPORTED) {\n    return 'imagebitmap';\n  }\n  if (IMAGE_SUPPORTED) {\n    return 'image';\n  }\n  if (DATA_SUPPORTED) {\n    return 'data';\n  }\n\n  // This should only happen in Node.js\n  throw new Error('Install \\'@loaders.gl/polyfills\\' to parse images under Node.js');\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,0BAA0B;AAIlD,MAAM;EAACC;AAAe,CAAC,GAAGC,UAAU;AAEpC,MAAMC,eAAe,GAAG,OAAOC,KAAK,KAAK,WAAW;AACpD,MAAMC,sBAAsB,GAAG,OAAOC,WAAW,KAAK,WAAW;AACjE,MAAMC,oBAAoB,GAAGC,OAAO,CAACP,eAAe,CAAC;AACrD,MAAMQ,cAAc,GAAGT,SAAS,GAAG,IAAI,GAAGO,oBAAoB;AAM9D,OAAO,SAASG,oBAAoBA,CAACC,IAAY,EAAW;EAC1D,QAAQA,IAAI;IACV,KAAK,MAAM;MAET,OAAON,sBAAsB,IAAIF,eAAe,IAAIM,cAAc;IAEpE,KAAK,aAAa;MAChB,OAAOJ,sBAAsB;IAC/B,KAAK,OAAO;MACV,OAAOF,eAAe;IACxB,KAAK,MAAM;MACT,OAAOM,cAAc;IAEvB;MACE,MAAM,IAAIG,KAAK,8BAAAC,MAAA,CAA8BF,IAAI,uCAAoC,CAAC;EAC1F;AACF;AAMA,OAAO,SAASG,mBAAmBA,CAAA,EAAkB;EACnD,IAAIT,sBAAsB,EAAE;IAC1B,OAAO,aAAa;EACtB;EACA,IAAIF,eAAe,EAAE;IACnB,OAAO,OAAO;EAChB;EACA,IAAIM,cAAc,EAAE;IAClB,OAAO,MAAM;EACf;EAGA,MAAM,IAAIG,KAAK,CAAC,iEAAiE,CAAC;AACpF"}