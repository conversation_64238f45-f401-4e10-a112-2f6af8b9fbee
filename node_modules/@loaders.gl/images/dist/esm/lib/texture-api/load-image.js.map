{"version": 3, "file": "load-image.js", "names": ["assert", "parseImage", "getImageSize", "generateUrl", "deepLoad", "shallowLoad", "loadImage", "getUrl", "options", "arguments", "length", "undefined", "imageUrls", "getImageUrls", "urlOptions", "mipLevels", "image", "getMipmappedImageUrls", "urls", "url", "lod", "width", "height", "getMipLevels", "push", "mipLevel", "_ref", "Math", "floor", "log2", "max"], "sources": ["../../../../src/lib/texture-api/load-image.ts"], "sourcesContent": ["import {assert} from '@loaders.gl/loader-utils';\nimport {parseImage} from '../parsers/parse-image';\nimport {getImageSize} from '../category-api/parsed-image-api';\nimport {generateUrl} from './generate-url';\nimport {deepLoad, shallowLoad} from './deep-load';\n\nexport async function loadImage(getUrl, options = {}) {\n  const imageUrls = await getImageUrls(getUrl, options);\n  return await deepLoad(imageUrls, parseImage, options);\n}\n\nexport async function getImageUrls(getUrl, options, urlOptions = {}) {\n  const mipLevels = (options && options.image && options.image.mipLevels) || 0;\n  return mipLevels !== 0\n    ? await getMipmappedImageUrls(getUrl, mipLevels, options, urlOptions)\n    : generateUrl(getUrl, options, urlOptions);\n}\n\nasync function getMipmappedImageUrls(getUrl, mipLevels, options, urlOptions) {\n  const urls: string[] = [];\n\n  // If no mip levels supplied, we need to load the level 0 image and calculate based on size\n  if (mipLevels === 'auto') {\n    const url = generateUrl(getUrl, options, {...urlOptions, lod: 0});\n    const image = await shallowLoad(url, parseImage, options);\n\n    const {width, height} = getImageSize(image);\n    mipLevels = getMipLevels({width, height});\n\n    // TODO - push image and make `deepLoad` pass through non-url values, avoid loading twice?\n    urls.push(url);\n  }\n\n  // We now know how many mipLevels we need, remaining image urls can now be constructed\n  assert(mipLevels > 0);\n\n  for (let mipLevel = urls.length; mipLevel < mipLevels; ++mipLevel) {\n    const url = generateUrl(getUrl, options, {...urlOptions, lod: mipLevel});\n    urls.push(url);\n  }\n\n  return urls;\n}\n\n// Calculates number of mipmaps based on texture size (log2)\nexport function getMipLevels({width, height}) {\n  return 1 + Math.floor(Math.log2(Math.max(width, height)));\n}\n"], "mappings": "AAAA,SAAQA,MAAM,QAAO,0BAA0B;AAC/C,SAAQC,UAAU,QAAO,wBAAwB;AACjD,SAAQC,YAAY,QAAO,kCAAkC;AAC7D,SAAQC,WAAW,QAAO,gBAAgB;AAC1C,SAAQC,QAAQ,EAAEC,WAAW,QAAO,aAAa;AAEjD,OAAO,eAAeC,SAASA,CAACC,MAAM,EAAgB;EAAA,IAAdC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAClD,MAAMG,SAAS,GAAG,MAAMC,YAAY,CAACN,MAAM,EAAEC,OAAO,CAAC;EACrD,OAAO,MAAMJ,QAAQ,CAACQ,SAAS,EAAEX,UAAU,EAAEO,OAAO,CAAC;AACvD;AAEA,OAAO,eAAeK,YAAYA,CAACN,MAAM,EAAEC,OAAO,EAAmB;EAAA,IAAjBM,UAAU,GAAAL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACjE,MAAMM,SAAS,GAAIP,OAAO,IAAIA,OAAO,CAACQ,KAAK,IAAIR,OAAO,CAACQ,KAAK,CAACD,SAAS,IAAK,CAAC;EAC5E,OAAOA,SAAS,KAAK,CAAC,GAClB,MAAME,qBAAqB,CAACV,MAAM,EAAEQ,SAAS,EAAEP,OAAO,EAAEM,UAAU,CAAC,GACnEX,WAAW,CAACI,MAAM,EAAEC,OAAO,EAAEM,UAAU,CAAC;AAC9C;AAEA,eAAeG,qBAAqBA,CAACV,MAAM,EAAEQ,SAAS,EAAEP,OAAO,EAAEM,UAAU,EAAE;EAC3E,MAAMI,IAAc,GAAG,EAAE;EAGzB,IAAIH,SAAS,KAAK,MAAM,EAAE;IACxB,MAAMI,GAAG,GAAGhB,WAAW,CAACI,MAAM,EAAEC,OAAO,EAAE;MAAC,GAAGM,UAAU;MAAEM,GAAG,EAAE;IAAC,CAAC,CAAC;IACjE,MAAMJ,KAAK,GAAG,MAAMX,WAAW,CAACc,GAAG,EAAElB,UAAU,EAAEO,OAAO,CAAC;IAEzD,MAAM;MAACa,KAAK;MAAEC;IAAM,CAAC,GAAGpB,YAAY,CAACc,KAAK,CAAC;IAC3CD,SAAS,GAAGQ,YAAY,CAAC;MAACF,KAAK;MAAEC;IAAM,CAAC,CAAC;IAGzCJ,IAAI,CAACM,IAAI,CAACL,GAAG,CAAC;EAChB;EAGAnB,MAAM,CAACe,SAAS,GAAG,CAAC,CAAC;EAErB,KAAK,IAAIU,QAAQ,GAAGP,IAAI,CAACR,MAAM,EAAEe,QAAQ,GAAGV,SAAS,EAAE,EAAEU,QAAQ,EAAE;IACjE,MAAMN,GAAG,GAAGhB,WAAW,CAACI,MAAM,EAAEC,OAAO,EAAE;MAAC,GAAGM,UAAU;MAAEM,GAAG,EAAEK;IAAQ,CAAC,CAAC;IACxEP,IAAI,CAACM,IAAI,CAACL,GAAG,CAAC;EAChB;EAEA,OAAOD,IAAI;AACb;AAGA,OAAO,SAASK,YAAYA,CAAAG,IAAA,EAAkB;EAAA,IAAjB;IAACL,KAAK;IAAEC;EAAM,CAAC,GAAAI,IAAA;EAC1C,OAAO,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,IAAI,CAACF,IAAI,CAACG,GAAG,CAACT,KAAK,EAAEC,MAAM,CAAC,CAAC,CAAC;AAC3D"}