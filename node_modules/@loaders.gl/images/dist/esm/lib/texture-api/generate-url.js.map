{"version": 3, "file": "generate-url.js", "names": ["<PERSON><PERSON><PERSON>", "assert", "generateUrl", "getUrl", "options", "urlOptions", "url", "baseUrl", "length", "concat"], "sources": ["../../../../src/lib/texture-api/generate-url.ts"], "sourcesContent": ["import {resolvePath, assert} from '@loaders.gl/loader-utils';\n\n// Generate a url by calling getUrl with mix of options, applying options.baseUrl\nexport function generateUrl(getUrl, options, urlOptions) {\n  // Get url\n  let url = getUrl;\n  if (typeof getUrl === 'function') {\n    url = getUrl({...options, ...urlOptions});\n  }\n  assert(typeof url === 'string');\n\n  // Apply options.baseUrl\n  const {baseUrl} = options;\n  if (baseUrl) {\n    url = baseUrl[baseUrl.length - 1] === '/' ? `${baseUrl}${url}` : `${baseUrl}/${url}`;\n  }\n\n  return resolvePath(url);\n}\n"], "mappings": "AAAA,SAAQA,WAAW,EAAEC,MAAM,QAAO,0BAA0B;AAG5D,OAAO,SAASC,WAAWA,CAACC,MAAM,EAAEC,OAAO,EAAEC,UAAU,EAAE;EAEvD,IAAIC,GAAG,GAAGH,MAAM;EAChB,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;IAChCG,GAAG,GAAGH,MAAM,CAAC;MAAC,GAAGC,OAAO;MAAE,GAAGC;IAAU,CAAC,CAAC;EAC3C;EACAJ,MAAM,CAAC,OAAOK,GAAG,KAAK,QAAQ,CAAC;EAG/B,MAAM;IAACC;EAAO,CAAC,GAAGH,OAAO;EACzB,IAAIG,OAAO,EAAE;IACXD,GAAG,GAAGC,OAAO,CAACA,OAAO,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,MAAAC,MAAA,CAAMF,OAAO,EAAAE,MAAA,CAAGH,GAAG,OAAAG,MAAA,CAAQF,OAAO,OAAAE,MAAA,CAAIH,GAAG,CAAE;EACtF;EAEA,OAAON,WAAW,CAACM,GAAG,CAAC;AACzB"}