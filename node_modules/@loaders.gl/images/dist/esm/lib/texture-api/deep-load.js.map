{"version": 3, "file": "deep-load.js", "names": ["asyncDeepMap", "deepLoad", "urlTree", "load", "options", "url", "shallowLoad", "response", "fetch", "arrayBuffer"], "sources": ["../../../../src/lib/texture-api/deep-load.ts"], "sourcesContent": ["import {asyncDeepMap} from './async-deep-map';\n\nexport async function deepLoad(urlTree, load, options) {\n  return await asyncDeepMap(urlTree, (url) => shallowLoad(url, load, options));\n}\n\nexport async function shallowLoad(url, load, options) {\n  // console.error('loading', url);\n  const response = await fetch(url, options.fetch);\n  const arrayBuffer = await response.arrayBuffer();\n  return await load(arrayBuffer, options);\n}\n"], "mappings": "AAAA,SAAQA,YAAY,QAAO,kBAAkB;AAE7C,OAAO,eAAeC,QAAQA,CAACC,OAAO,EAAEC,IAAI,EAAEC,OAAO,EAAE;EACrD,OAAO,MAAMJ,YAAY,CAACE,OAAO,EAAGG,GAAG,IAAKC,WAAW,CAACD,GAAG,EAAEF,IAAI,EAAEC,OAAO,CAAC,CAAC;AAC9E;AAEA,OAAO,eAAeE,WAAWA,CAACD,GAAG,EAAEF,IAAI,EAAEC,OAAO,EAAE;EAEpD,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAACH,GAAG,EAAED,OAAO,CAACI,KAAK,CAAC;EAChD,MAAMC,WAAW,GAAG,MAAMF,QAAQ,CAACE,WAAW,CAAC,CAAC;EAChD,OAAO,MAAMN,IAAI,CAACM,WAAW,EAAEL,OAAO,CAAC;AACzC"}