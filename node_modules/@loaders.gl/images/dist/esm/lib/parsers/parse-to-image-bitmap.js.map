{"version": 3, "file": "parse-to-image-bitmap.js", "names": ["isSVG", "getBlob", "parseToImage", "EMPTY_OBJECT", "imagebitmapOptionsSupported", "parseToImageBitmap", "arrayBuffer", "options", "url", "blob", "image", "imagebitmapOptions", "imagebitmap", "safeCreateImageBitmap", "arguments", "length", "undefined", "isEmptyObject", "createImageBitmap", "error", "console", "warn", "object", "key"], "sources": ["../../../../src/lib/parsers/parse-to-image-bitmap.ts"], "sourcesContent": ["import type {ImageLoaderOptions} from '../../image-loader';\nimport {isSVG, getBlob} from './svg-utils';\nimport {parseToImage} from './parse-to-image';\n\nconst EMPTY_OBJECT = {};\n\nlet imagebitmapOptionsSupported = true;\n\n/**\n * Asynchronously parses an array buffer into an ImageBitmap - this contains the decoded data\n * ImageBitmaps are supported on worker threads, but not supported on Edge, IE11 and Safari\n * https://developer.mozilla.org/en-US/docs/Web/API/ImageBitmap#Browser_compatibility\n *\n * TODO - createImageBitmap supports source rect (5 param overload), pass through?\n */\nexport async function parseToImageBitmap(\n  arrayBuffer: ArrayBuffer,\n  options: ImageLoaderOptions,\n  url?: string\n): Promise<ImageBitmap> {\n  let blob;\n\n  // Cannot parse SVG directly to ImageBitmap, parse to Image first\n  if (isSVG(url)) {\n    // Note: this only works on main thread\n    const image = await parseToImage(arrayBuffer, options, url);\n    blob = image;\n  } else {\n    // Create blob from the array buffer\n    blob = getBlob(arrayBuffer, url);\n  }\n\n  const imagebitmapOptions = options && options.imagebitmap;\n\n  return await safeCreateImageBitmap(blob, imagebitmapOptions);\n}\n\n/**\n * Safely creates an imageBitmap with options\n * *\n * Firefox crashes if imagebitmapOptions is supplied\n * Avoid supplying if not provided or supported, remember if not supported\n */\nasync function safeCreateImageBitmap(\n  blob: Blob,\n  imagebitmapOptions: ImageBitmapOptions | null = null\n): Promise<ImageBitmap> {\n  if (isEmptyObject(imagebitmapOptions) || !imagebitmapOptionsSupported) {\n    imagebitmapOptions = null;\n  }\n\n  if (imagebitmapOptions) {\n    try {\n      // @ts-ignore Options\n      return await createImageBitmap(blob, imagebitmapOptions);\n    } catch (error) {\n      console.warn(error); // eslint-disable-line\n      imagebitmapOptionsSupported = false;\n    }\n  }\n\n  return await createImageBitmap(blob);\n}\n\nfunction isEmptyObject(object) {\n  // @ts-ignore\n  for (const key in object || EMPTY_OBJECT) {\n    return false;\n  }\n  return true;\n}\n"], "mappings": "AACA,SAAQA,KAAK,EAAEC,OAAO,QAAO,aAAa;AAC1C,SAAQC,YAAY,QAAO,kBAAkB;AAE7C,MAAMC,YAAY,GAAG,CAAC,CAAC;AAEvB,IAAIC,2BAA2B,GAAG,IAAI;AAStC,OAAO,eAAeC,kBAAkBA,CACtCC,WAAwB,EACxBC,OAA2B,EAC3BC,GAAY,EACU;EACtB,IAAIC,IAAI;EAGR,IAAIT,KAAK,CAACQ,GAAG,CAAC,EAAE;IAEd,MAAME,KAAK,GAAG,MAAMR,YAAY,CAACI,WAAW,EAAEC,OAAO,EAAEC,GAAG,CAAC;IAC3DC,IAAI,GAAGC,KAAK;EACd,CAAC,MAAM;IAELD,IAAI,GAAGR,OAAO,CAACK,WAAW,EAAEE,GAAG,CAAC;EAClC;EAEA,MAAMG,kBAAkB,GAAGJ,OAAO,IAAIA,OAAO,CAACK,WAAW;EAEzD,OAAO,MAAMC,qBAAqB,CAACJ,IAAI,EAAEE,kBAAkB,CAAC;AAC9D;AAQA,eAAeE,qBAAqBA,CAClCJ,IAAU,EAEY;EAAA,IADtBE,kBAA6C,GAAAG,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAEpD,IAAIG,aAAa,CAACN,kBAAkB,CAAC,IAAI,CAACP,2BAA2B,EAAE;IACrEO,kBAAkB,GAAG,IAAI;EAC3B;EAEA,IAAIA,kBAAkB,EAAE;IACtB,IAAI;MAEF,OAAO,MAAMO,iBAAiB,CAACT,IAAI,EAAEE,kBAAkB,CAAC;IAC1D,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAACF,KAAK,CAAC;MACnBf,2BAA2B,GAAG,KAAK;IACrC;EACF;EAEA,OAAO,MAAMc,iBAAiB,CAACT,IAAI,CAAC;AACtC;AAEA,SAASQ,aAAaA,CAACK,MAAM,EAAE;EAE7B,KAAK,MAAMC,GAAG,IAAID,MAAM,IAAInB,YAAY,EAAE;IACxC,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb"}