{"version": 3, "file": "parse-to-image.js", "names": ["getBlobOrSVGDataUrl", "parseToImage", "arrayBuffer", "options", "url", "blobOrDataUrl", "URL", "self", "webkitURL", "objectUrl", "createObjectURL", "loadToImage", "revokeObjectURL", "image", "Image", "src", "decode", "Promise", "resolve", "reject", "onload", "onerror", "err", "Error", "concat", "error"], "sources": ["../../../../src/lib/parsers/parse-to-image.ts"], "sourcesContent": ["import type {ImageLoaderOptions} from '../../image-loader';\nimport {getBlobOrSVGDataUrl} from './svg-utils';\n\n// Parses html image from array buffer\nexport async function parseToImage(\n  arrayBuffer: ArrayBuffer,\n  options: ImageLoaderOptions,\n  url?: string\n): Promise<HTMLImageElement> {\n  // Note: image parsing requires conversion to Blob (for createObjectURL).\n  // Potentially inefficient for not using `response.blob()` (and for File / Blob inputs)...\n  // But presumably not worth adding 'blob' flag to loader objects?\n\n  const blobOrDataUrl = getBlobOrSVGDataUrl(arrayBuffer, url);\n  const URL = self.URL || self.webkitURL;\n  const objectUrl = typeof blobOrDataUrl !== 'string' && URL.createObjectURL(blobOrDataUrl);\n  try {\n    return await loadToImage(objectUrl || blobOrDataUrl, options);\n  } finally {\n    if (objectUrl) {\n      URL.revokeObjectURL(objectUrl);\n    }\n  }\n}\n\nexport async function loadToImage(url, options): Promise<HTMLImageElement> {\n  const image = new Image();\n  image.src = url;\n\n  // The `image.onload()` callback does not guarantee that the image has been decoded\n  // so a main thread \"freeze\" can be incurred when using the image for the first time.\n  // `Image.decode()` returns a promise that completes when image is decoded.\n\n  // https://developer.mozilla.org/en-US/docs/Web/API/HTMLImageElement/decode\n  // Note: When calling `img.decode()`, we do not need to wait for `img.onload()`\n  // Note: `HTMLImageElement.decode()` is not available in Edge and IE11\n  if (options.image && options.image.decode && image.decode) {\n    await image.decode();\n    return image;\n  }\n\n  // Create a promise that tracks onload/onerror callbacks\n  return await new Promise((resolve, reject) => {\n    try {\n      image.onload = () => resolve(image);\n      image.onerror = (err) => reject(new Error(`Could not load image ${url}: ${err}`));\n    } catch (error) {\n      reject(error);\n    }\n  });\n}\n"], "mappings": "AACA,SAAQA,mBAAmB,QAAO,aAAa;AAG/C,OAAO,eAAeC,YAAYA,CAChCC,WAAwB,EACxBC,OAA2B,EAC3BC,GAAY,EACe;EAK3B,MAAMC,aAAa,GAAGL,mBAAmB,CAACE,WAAW,EAAEE,GAAG,CAAC;EAC3D,MAAME,GAAG,GAAGC,IAAI,CAACD,GAAG,IAAIC,IAAI,CAACC,SAAS;EACtC,MAAMC,SAAS,GAAG,OAAOJ,aAAa,KAAK,QAAQ,IAAIC,GAAG,CAACI,eAAe,CAACL,aAAa,CAAC;EACzF,IAAI;IACF,OAAO,MAAMM,WAAW,CAACF,SAAS,IAAIJ,aAAa,EAAEF,OAAO,CAAC;EAC/D,CAAC,SAAS;IACR,IAAIM,SAAS,EAAE;MACbH,GAAG,CAACM,eAAe,CAACH,SAAS,CAAC;IAChC;EACF;AACF;AAEA,OAAO,eAAeE,WAAWA,CAACP,GAAG,EAAED,OAAO,EAA6B;EACzE,MAAMU,KAAK,GAAG,IAAIC,KAAK,CAAC,CAAC;EACzBD,KAAK,CAACE,GAAG,GAAGX,GAAG;EASf,IAAID,OAAO,CAACU,KAAK,IAAIV,OAAO,CAACU,KAAK,CAACG,MAAM,IAAIH,KAAK,CAACG,MAAM,EAAE;IACzD,MAAMH,KAAK,CAACG,MAAM,CAAC,CAAC;IACpB,OAAOH,KAAK;EACd;EAGA,OAAO,MAAM,IAAII,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IAC5C,IAAI;MACFN,KAAK,CAACO,MAAM,GAAG,MAAMF,OAAO,CAACL,KAAK,CAAC;MACnCA,KAAK,CAACQ,OAAO,GAAIC,GAAG,IAAKH,MAAM,CAAC,IAAII,KAAK,yBAAAC,MAAA,CAAyBpB,GAAG,QAAAoB,MAAA,CAAKF,GAAG,CAAE,CAAC,CAAC;IACnF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdN,MAAM,CAACM,KAAK,CAAC;IACf;EACF,CAAC,CAAC;AACJ"}