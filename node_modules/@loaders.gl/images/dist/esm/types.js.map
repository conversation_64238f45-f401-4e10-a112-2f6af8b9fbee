{"version": 3, "file": "types.js", "names": [], "sources": ["../../src/types.ts"], "sourcesContent": ["/**\n * data images\n */\nexport type ImageDataType = {\n  data: Uint8Array;\n  width: number;\n  height: number;\n  compressed?: boolean;\n};\n\n/**\n * Supported Image Types\n */\nexport type ImageType = ImageBitmap | typeof Image | ImageDataType;\n\n/**\n * Image type string used to control or determine the type of images returned from ImageLoader\n */\nexport type ImageTypeEnum = 'imagebitmap' | 'image' | 'data';\n"], "mappings": ""}