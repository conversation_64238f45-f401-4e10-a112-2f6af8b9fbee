{"version": 3, "file": "image-writer.js", "names": ["VERSION", "encodeImage", "ImageWriter", "name", "id", "module", "version", "extensions", "options", "image", "mimeType", "jpegQuality", "encode"], "sources": ["../../src/image-writer.ts"], "sourcesContent": ["// import type {Writer} from '@loaders.gl/loader-utils';\nimport {VERSION} from './lib/utils/version';\nimport {encodeImage} from './lib/encoders/encode-image';\n\nexport const ImageWriter = {\n  name: 'Images',\n  id: 'image',\n  module: 'images',\n  version: VERSION,\n  extensions: ['jpeg'],\n  options: {\n    image: {\n      mimeType: 'image/png',\n      jpegQuality: null\n    }\n  },\n  encode: encodeImage\n};\n"], "mappings": "AACA,SAAQA,OAAO,QAAO,qBAAqB;AAC3C,SAAQC,WAAW,QAAO,6BAA6B;AAEvD,OAAO,MAAMC,WAAW,GAAG;EACzBC,IAAI,EAAE,QAAQ;EACdC,EAAE,EAAE,OAAO;EACXC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAEN,OAAO;EAChBO,UAAU,EAAE,CAAC,MAAM,CAAC;EACpBC,OAAO,EAAE;IACPC,KAAK,EAAE;MACLC,QAAQ,EAAE,WAAW;MACrBC,WAAW,EAAE;IACf;EACF,CAAC;EACDC,MAAM,EAAEX;AACV,CAAC"}