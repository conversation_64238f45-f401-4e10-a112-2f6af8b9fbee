import type { LoaderOptions, LoaderWithParser } from '@loaders.gl/loader-utils';
import { parseImage } from './lib/parsers/parse-image';
export type ImageLoaderOptions = LoaderOptions & {
    image?: {
        type?: 'auto' | 'data' | 'imagebitmap' | 'image';
        decode?: boolean;
    };
    imagebitmap?: ImageBitmapOptions;
};
/**
 * Loads a platform-specific image type
 * Note: This type can be used as input data to WebGL texture creation
 */
export declare const ImageLoader: {
    id: string;
    module: string;
    name: string;
    version: any;
    mimeTypes: string[];
    extensions: string[];
    parse: typeof parseImage;
    tests: ((arrayBuffer: any) => boolean)[];
    options: ImageLoaderOptions;
};
export declare const _typecheckImageLoader: LoaderWithParser;
//# sourceMappingURL=image-loader.d.ts.map