(()=>{var Q=Object.defineProperty;var Pe=e=>Q(e,"__esModule",{value:!0});var n=(e,t)=>()=>(e&&(t=e(e=0)),t);var Le=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Me=(e,t)=>{Pe(e);for(var r in t)Q(e,r,{get:t[r],enumerable:!0})};var h,L=n(()=>{h=typeof __VERSION__!="undefined"?__VERSION__:"latest"});function f(e,t){if(!e)throw new Error(t||"loader assertion failed.")}var $=n(()=>{});var p,Oe,Ue,ke,ve,u,Y,_e,J=n(()=>{p={self:typeof self!="undefined"&&self,window:typeof window!="undefined"&&window,global:typeof global!="undefined"&&global,document:typeof document!="undefined"&&document},Oe=p.self||p.window||p.global||{},Ue=p.window||p.self||p.global||{},ke=p.global||p.self||p.window||{},ve=p.document||{},u=Boolean(typeof process!="object"||String(process)!=="[object process]"||process.browser),Y=typeof process!="undefined"&&process.version&&/v([0-9]*)/.exec(process.version),_e=Y&&parseFloat(Y[1])||0});function M(e){for(let t in X)if(e.startsWith(t)){let r=X[t];e=e.replace(t,r)}return!e.startsWith("http://")&&!e.startsWith("https://")&&(e=`${Ne}${e}`),e}var Ne,X,Z=n(()=>{Ne="",X={}});var l=n(()=>{$();J();Z()});function B(e){switch(e){case"auto":return U||O||k;case"imagebitmap":return U;case"image":return O;case"data":return k;default:throw new Error(`@loaders.gl/images: image ${e} not supported in this environment`)}}function T(){if(U)return"imagebitmap";if(O)return"image";if(k)return"data";throw new Error("Install '@loaders.gl/polyfills' to parse images under Node.js")}var Ve,O,U,Fe,k,v=n(()=>{l();({_parseImageNode:Ve}=globalThis),O=typeof Image!="undefined",U=typeof ImageBitmap!="undefined",Fe=Boolean(Ve),k=u?!0:Fe});function q(e){return Boolean(z(e))}function _(e){let t=z(e);if(!t)throw new Error("Not an image");return t}function d(e){return y(e)}function y(e){switch(_(e)){case"data":return e;case"image":case"imagebitmap":let t=document.createElement("canvas"),r=t.getContext("2d");if(!r)throw new Error("getImageData");return t.width=e.width,t.height=e.height,r.drawImage(e,0,0),r.getImageData(0,0,e.width,e.height);default:throw new Error("getImageData")}}function z(e){return typeof ImageBitmap!="undefined"&&e instanceof ImageBitmap?"imagebitmap":typeof Image!="undefined"&&e instanceof Image?"image":e&&typeof e=="object"&&e.data&&e.width&&e.height?"data":null}var b=n(()=>{});function S(e){return e&&(Re.test(e)||je.test(e))}function H(e,t){if(S(t)){let a=new TextDecoder().decode(e);try{typeof unescape=="function"&&typeof encodeURIComponent=="function"&&(a=unescape(encodeURIComponent(a)))}catch(i){throw new Error(i.message)}return`data:image/svg+xml;base64,${btoa(a)}`}return N(e,t)}function N(e,t){if(S(t))throw new Error("SVG cannot be parsed directly to imagebitmap");return new Blob([new Uint8Array(e)])}var Re,je,V=n(()=>{Re=/^data:image\/svg\+xml/,je=/\.svg((\?|#).*)?$/});async function E(e,t,r){let a=H(e,r),o=self.URL||self.webkitURL,i=typeof a!="string"&&o.createObjectURL(a);try{return await We(i||a,t)}finally{i&&o.revokeObjectURL(i)}}async function We(e,t){let r=new Image;return r.src=e,t.image&&t.image.decode&&r.decode?(await r.decode(),r):await new Promise((a,o)=>{try{r.onload=()=>a(r),r.onerror=i=>o(new Error(`Could not load image ${e}: ${i}`))}catch(i){o(i)}})}var F=n(()=>{V()});async function ee(e,t,r){let a;S(r)?a=await E(e,t,r):a=N(e,r);let o=t&&t.imagebitmap;return await Ce(a,o)}async function Ce(e,t=null){if((Qe(t)||!K)&&(t=null),t)try{return await createImageBitmap(e,t)}catch(r){console.warn(r),K=!1}return await createImageBitmap(e)}function Qe(e){for(let t in e||Ge)return!1;return!0}var Ge,K,te=n(()=>{V();F();Ge={},K=!0});function re(e){return!Xe(e,"ftyp",4)||(e[8]&96)==0?null:$e(e)}function $e(e){switch(Ye(e,8,12).replace("\0"," ").trim()){case"avif":case"avis":return{extension:"avif",mimeType:"image/avif"};default:return null}}function Ye(e,t,r){return String.fromCharCode(...e.slice(t,r))}function Je(e){return[...e].map(t=>t.charCodeAt(0))}function Xe(e,t,r=0){let a=Je(t);for(let o=0;o<a.length;++o)if(a[o]!==e[o+r])return!1;return!0}var ae=n(()=>{});function A(e){let t=I(e);return qe(t)||Ke(t)||ze(t)||He(t)||Ze(t)}function Ze(e){let t=new Uint8Array(e instanceof DataView?e.buffer:e),r=re(t);return r?{mimeType:r.mimeType,width:0,height:0}:null}function qe(e){let t=I(e);return t.byteLength>=24&&t.getUint32(0,g)===2303741511?{mimeType:"image/png",width:t.getUint32(16,g),height:t.getUint32(20,g)}:null}function ze(e){let t=I(e);return t.byteLength>=10&&t.getUint32(0,g)===1195984440?{mimeType:"image/gif",width:t.getUint16(6,w),height:t.getUint16(8,w)}:null}function He(e){let t=I(e);return t.byteLength>=14&&t.getUint16(0,g)===16973&&t.getUint32(2,w)===t.byteLength?{mimeType:"image/bmp",width:t.getUint32(18,w),height:t.getUint32(22,w)}:null}function Ke(e){let t=I(e);if(!(t.byteLength>=3&&t.getUint16(0,g)===65496&&t.getUint8(2)===255))return null;let{tableMarkers:a,sofMarkers:o}=et(),i=2;for(;i+9<t.byteLength;){let s=t.getUint16(i,g);if(o.has(s))return{mimeType:"image/jpeg",height:t.getUint16(i+5,g),width:t.getUint16(i+7,g)};if(!a.has(s))return null;i+=2,i+=t.getUint16(i,g)}return null}function et(){let e=new Set([65499,65476,65484,65501,65534]);for(let r=65504;r<65520;++r)e.add(r);let t=new Set([65472,65473,65474,65475,65477,65478,65479,65481,65482,65483,65485,65486,65487,65502]);return{tableMarkers:e,sofMarkers:t}}function I(e){if(e instanceof DataView)return e;if(ArrayBuffer.isView(e))return new DataView(e.buffer);if(e instanceof ArrayBuffer)return new DataView(e);throw new Error("toDataView")}var g,w,D=n(()=>{ae();g=!1,w=!0});async function oe(e,t){let{mimeType:r}=A(e)||{},a=globalThis._parseImageNode;return f(a),await a(e,r)}var ie=n(()=>{l();D()});async function x(e,t,r){t=t||{};let o=(t.image||{}).type||"auto",{url:i}=r||{},s=tt(o),m;switch(s){case"imagebitmap":m=await ee(e,t,i);break;case"image":m=await E(e,t,i);break;case"data":m=await oe(e,t);break;default:f(!1)}return o==="data"&&(m=y(m)),m}function tt(e){switch(e){case"auto":case"data":return T();default:return B(e),e}}var R=n(()=>{l();v();b();F();te();ie()});var rt,at,ot,ne,se=n(()=>{L();R();D();rt=["png","jpg","jpeg","gif","webp","bmp","ico","svg","avif"],at=["image/png","image/jpeg","image/gif","image/webp","image/avif","image/bmp","image/vnd.microsoft.icon","image/svg+xml"],ot={image:{type:"auto",decode:!0}},ne={id:"image",module:"images",name:"Images",version:h,mimeTypes:at,extensions:rt,parse:x,tests:[e=>Boolean(A(new DataView(e)))],options:ot}});async function pe(e,t){return t=t||{},t.image=t.image||{},me?me(e,{type:t.image.mimeType}):it(e,t)}async function it(e,t){let{mimeType:r,jpegQuality:a}=t.image,{width:o,height:i}=d(e),s=document.createElement("canvas");s.width=o,s.height=i,nt(e,s);let m=await new Promise(c=>{if(a&&ge)try{s.toBlob(c,r,a);return}catch{ge=!1}s.toBlob(c,r)});if(!m)throw new Error("image encoding failed");return await m.arrayBuffer()}function nt(e,t,r=0,a=0){if(r===0&&a===0&&typeof ImageBitmap!="undefined"&&e instanceof ImageBitmap){let i=t.getContext("bitmaprenderer");if(i)return i.transferFromImageBitmap(e),t}let o=t.getContext("2d");if(e.data){let i=new Uint8ClampedArray(e.data),s=new ImageData(i,e.width,e.height);return o.putImageData(s,0,0),t}return o.drawImage(e,0,0),t}var me,ge,fe=n(()=>{b();({_encodeImageNode:me}=globalThis);ge=!0});var le,ce=n(()=>{L();fe();le={name:"Images",id:"image",module:"images",version:h,extensions:["jpeg"],options:{image:{mimeType:"image/png",jpegQuality:null}},encode:pe}});async function de(){if(ue)return await ue;let e=new Set;for(let t of st)(u?await gt(t):ye(t))&&e.add(t);return e}function Ae(e){if(j[e]===void 0){let t=u?mt(e):ye(e);j[e]=t}return j[e]}function ye(e){let t=["image/png","image/jpeg","image/gif"],{_parseImageNode:r,_imageFormatsNode:a=t}=globalThis;return Boolean(r)&&a.includes(e)}function mt(e){switch(e){case"image/avif":case"image/webp":return ft(e);default:return!0}}async function gt(e){let t=pt[e];return t?await lt(t):!0}function ft(e){try{return document.createElement("canvas").toDataURL(e).indexOf(`data:${e}`)===0}catch{return!1}}async function lt(e){return new Promise(t=>{let r=new Image;r.src=e,r.onload=()=>t(r.height>0),r.onerror=()=>t(!1)})}var st,ue,j,pt,W=n(()=>{l();st=["image/png","image/jpeg","image/gif","image/webp","image/avif","image/tiff","image/svg","image/svg+xml","image/bmp","image/vnd.microsoft.icon"],ue=null;j={};pt={"image/avif":"data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgANogQEAwgMg8f8D///8WfhwB8+ErK42A=","image/webp":"data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA"}});function P(e,t,r){let a=e;typeof e=="function"&&(a=e({...t,...r})),f(typeof a=="string");let{baseUrl:o}=t;return o&&(a=o[o.length-1]==="/"?`${o}${a}`:`${o}/${a}`),M(a)}var be=n(()=>{l()});async function we(e,t,r={}){return await G(e,t,r)}async function G(e,t,r){return Array.isArray(e)?await dt(e,t,r):ct(e)?await ut(e,t,r):await t(e,r)}async function ut(e,t,r){let a=[],o={};for(let i in e){let s=e[i],m=G(s,t,r).then(c=>{o[i]=c});a.push(m)}return await Promise.all(a),o}async function dt(e,t,r={}){let a=e.map(o=>G(o,t,r));return await Promise.all(a)}var ct,Ie=n(()=>{ct=e=>e&&typeof e=="object"});async function xe(e,t,r){return await we(e,a=>C(a,t,r))}async function C(e,t,r){let o=await(await fetch(e,r.fetch)).arrayBuffer();return await t(o,r)}var he=n(()=>{Ie()});async function Be(e,t={}){let r=await At(e,t);return await xe(r,x,t)}async function At(e,t,r={}){let a=t&&t.image&&t.image.mipLevels||0;return a!==0?await yt(e,a,t,r):P(e,t,r)}async function yt(e,t,r,a){let o=[];if(t==="auto"){let i=P(e,r,{...a,lod:0}),s=await C(i,x,r),{width:m,height:c}=d(s);t=bt({width:m,height:c}),o.push(i)}f(t>0);for(let i=o.length;i<t;++i){let s=P(e,r,{...a,lod:i});o.push(s)}return o}function bt({width:e,height:t}){return 1+Math.floor(Math.log2(Math.max(e,t)))}var Te=n(()=>{l();R();b();be();he()});var Se={};Me(Se,{ImageLoader:()=>ne,ImageWriter:()=>le,getBinaryImageMetadata:()=>A,getDefaultImageType:()=>T,getImageData:()=>y,getImageSize:()=>d,getImageType:()=>_,getSupportedImageFormats:()=>de,isImage:()=>q,isImageFormatSupported:()=>Ae,isImageTypeSupported:()=>B,loadImage:()=>Be});var Ee=n(()=>{se();ce();D();v();b();W();W();Te()});var It=Le((Ur,De)=>{var wt=(Ee(),Se);globalThis.loaders=globalThis.loaders||{};De.exports=Object.assign(globalThis.loaders,wt)});It();})();
//# sourceMappingURL=dist.min.js.map
