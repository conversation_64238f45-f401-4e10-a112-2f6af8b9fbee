{"version": 3, "sources": ["../src/lib/utils/version.ts", "../../loader-utils/src/lib/env-utils/assert.ts", "../../loader-utils/src/lib/env-utils/globals.ts", "../../loader-utils/src/lib/path-utils/file-aliases.ts", "../../loader-utils/src/index.ts", "../src/lib/category-api/image-type.ts", "../src/lib/category-api/parsed-image-api.ts", "../src/lib/parsers/svg-utils.ts", "../src/lib/parsers/parse-to-image.ts", "../src/lib/parsers/parse-to-image-bitmap.ts", "../src/lib/category-api/parse-isobmff-binary.ts", "../src/lib/category-api/binary-image-api.ts", "../src/lib/parsers/parse-to-node-image.ts", "../src/lib/parsers/parse-image.ts", "../src/image-loader.ts", "../src/lib/encoders/encode-image.ts", "../src/image-writer.ts", "../src/lib/category-api/image-format.ts", "../src/lib/texture-api/generate-url.ts", "../src/lib/texture-api/async-deep-map.ts", "../src/lib/texture-api/deep-load.ts", "../src/lib/texture-api/load-image.ts", "../src/index.ts", "../src/bundle.ts"], "sourcesContent": ["// Version constant cannot be imported, it needs to correspond to the build version of **this** module.\n// __VERSION__ is injected by babel-plugin-version-inline\n// @ts-ignore TS2304: Cannot find name '__VERSION__'.\nexport const VERSION = typeof __VERSION__ !== 'undefined' ? __VERSION__ : 'latest';\n", "/**\n * Throws an `Error` with the optional `message` if `condition` is falsy\n * @note Replacement for the external assert method to reduce bundle size\n */\nexport function assert(condition: any, message?: string): void {\n  if (!condition) {\n    throw new Error(message || 'loader assertion failed.');\n  }\n}\n", "// Purpose: include this in your module to avoid\n// dependencies on micro modules like 'global' and 'is-browser';\n\n/* eslint-disable no-restricted-globals */\nconst globals = {\n  self: typeof self !== 'undefined' && self,\n  window: typeof window !== 'undefined' && window,\n  global: typeof global !== 'undefined' && global,\n  document: typeof document !== 'undefined' && document\n};\n\ntype obj = {[key: string]: any};\nconst self_: obj = globals.self || globals.window || globals.global || {};\nconst window_: obj = globals.window || globals.self || globals.global || {};\nconst global_: obj = globals.global || globals.self || globals.window || {};\nconst document_: obj = globals.document || {};\n\nexport {self_ as self, window_ as window, global_ as global, document_ as document};\n\n/** true if running in a browser */\nexport const isBrowser: boolean =\n  // @ts-ignore process does not exist on browser\n  Boolean(typeof process !== 'object' || String(process) !== '[object process]' || process.browser);\n\n/** true if running in a worker thread */\nexport const isWorker: boolean = typeof importScripts === 'function';\n\n// Extract node major version\nconst matches =\n  typeof process !== 'undefined' && process.version && /v([0-9]*)/.exec(process.version);\n/** Major Node version (as a number) */\nexport const nodeVersion: number = (matches && parseFloat(matches[1])) || 0;\n", "// Simple file alias mechanisms for tests.\n\nlet pathPrefix = '';\nconst fileAliases: {[aliasPath: string]: string} = {};\n\n/*\n * Set a relative path prefix\n */\nexport function setPathPrefix(prefix: string): void {\n  pathPrefix = prefix;\n}\n\n/*\n * Get the relative path prefix\n */\nexport function getPathPrefix(): string {\n  return pathPrefix;\n}\n\n/**\n *\n * @param aliases\n *\n * Note: addAliases are an experimental export, they are only for testing of loaders.gl loaders\n * not intended as a generic aliasing mechanism\n */\nexport function addAliases(aliases: {[aliasPath: string]: string}): void {\n  Object.assign(fileAliases, aliases);\n}\n\n/**\n * Resolves aliases and adds path-prefix to paths\n */\nexport function resolvePath(filename: string): string {\n  for (const alias in fileAliases) {\n    if (filename.startsWith(alias)) {\n      const replacement = fileAliases[alias];\n      filename = filename.replace(alias, replacement);\n    }\n  }\n  if (!filename.startsWith('http://') && !filename.startsWith('https://')) {\n    filename = `${pathPrefix}${filename}`;\n  }\n  return filename;\n}\n", "// TYPES\nexport type {\n  Lo<PERSON>,\n  LoaderWithParser,\n  LoaderContext,\n  LoaderOptions,\n  Writer,\n  WriterOptions,\n  DataType,\n  SyncDataType,\n  BatchableDataType,\n  IFileSystem,\n  IRandomAccessReadFileSystem\n} from './types';\n\n// GENERAL UTILS\nexport {assert} from './lib/env-utils/assert';\nexport {\n  isBrowser,\n  isWorker,\n  nodeVersion,\n  self,\n  window,\n  global,\n  document\n} from './lib/env-utils/globals';\n\nexport {mergeLoaderOptions} from './lib/option-utils/merge-loader-options';\n\n// LOADERS.GL-SPECIFIC WORKER UTILS\nexport {createLoaderWorker} from './lib/worker-loader-utils/create-loader-worker';\nexport {parseWithWorker, canParseWithWorker} from './lib/worker-loader-utils/parse-with-worker';\nexport {canEncodeWithWorker} from './lib/worker-loader-utils/encode-with-worker';\n\n// PARSER UTILS\nexport {parseJSON} from './lib/parser-utils/parse-json';\n\n// MEMORY COPY UTILS\nexport {\n  sliceArrayBuffer,\n  concatenateArrayBuffers,\n  concatenateTypedArrays,\n  compareArrayBuffers\n} from './lib/binary-utils/array-buffer-utils';\nexport {padToNBytes, copyToArray, copyArrayBuffer} from './lib/binary-utils/memory-copy-utils';\nexport {\n  padStringToByteAlignment,\n  copyStringToDataView,\n  copyBinaryToDataView,\n  copyPaddedArrayBufferToDataView,\n  copyPaddedStringToDataView\n} from './lib/binary-utils/dataview-copy-utils';\nexport {getFirstCharacters, getMagicString} from './lib/binary-utils/get-first-characters';\n\n// ITERATOR UTILS\nexport {\n  makeTextEncoderIterator,\n  makeTextDecoderIterator,\n  makeLineIterator,\n  makeNumberedLineIterator\n} from './lib/iterators/text-iterators';\nexport {forEach, concatenateArrayBuffersAsync} from './lib/iterators/async-iteration';\n\n// REQUEST UTILS\nexport {default as RequestScheduler} from './lib/request-utils/request-scheduler';\n\n// PATH HELPERS\nexport {setPathPrefix, getPathPrefix, resolvePath} from './lib/path-utils/file-aliases';\nexport {addAliases as _addAliases} from './lib/path-utils/file-aliases';\n\n// MICRO LOADERS\nexport {JSONLoader} from './json-loader';\n\n// NODE support\n\n// Node.js emulation (can be used in browser)\n\n// Avoid direct use of `Buffer` which pulls in 50KB polyfill\nexport {isBuffer, toBuffer, toArrayBuffer} from './lib/binary-utils/memory-conversion-utils';\n\n// Note.js wrappers (can be safely imported, but not used in browser)\n\n// Use instead of importing 'util' to avoid node dependencies\nexport {promisify1, promisify2} from './lib/node/promisify';\n\n// `path` replacement (avoids bundling big path polyfill)\nimport * as path from './lib/path-utils/path';\nexport {path};\n\n// Use instead of importing 'fs' to avoid node dependencies`\nimport * as fs from './lib/node/fs';\nexport {fs};\n\n// Use instead of importing 'stream' to avoid node dependencies`\nimport * as stream from './lib/node/stream';\nexport {stream};\n\n// EXPERIMENTAL\nexport type {ReadableFile} from './lib/filesystems/readable-file';\nexport {makeReadableFile} from './lib/filesystems/readable-file';\n\nexport type {WritableFile} from './lib/filesystems/writable-file';\nexport {makeWritableFile} from './lib/filesystems/writable-file';\n\nexport {default as _NodeFileSystem} from './lib/filesystems/node-filesystem';\n", "import {isBrowser} from '@loaders.gl/loader-utils';\nimport type {ImageTypeEnum} from '../../types';\n\n// @ts-ignore TS2339: Property does not exist on type\nconst {_parseImageNode} = globalThis;\n\nconst IMAGE_SUPPORTED = typeof Image !== 'undefined'; // NOTE: \"false\" positives if jsdom is installed\nconst IMAGE_BITMAP_SUPPORTED = typeof ImageBitmap !== 'undefined';\nconst NODE_IMAGE_SUPPORTED = Boolean(_parseImageNode);\nconst DATA_SUPPORTED = isBrowser ? true : NODE_IMAGE_SUPPORTED;\n\n/**\n * Checks if a loaders.gl image type is supported\n * @param type image type string\n */\nexport function isImageTypeSupported(type: string): boolean {\n  switch (type) {\n    case 'auto':\n      // Should only ever be false in Node.js, if polyfills have not been installed...\n      return IMAGE_BITMAP_SUPPORTED || IMAGE_SUPPORTED || DATA_SUPPORTED;\n\n    case 'imagebitmap':\n      return IMAGE_BITMAP_SUPPORTED;\n    case 'image':\n      return IMAGE_SUPPORTED;\n    case 'data':\n      return DATA_SUPPORTED;\n\n    default:\n      throw new Error(`@loaders.gl/images: image ${type} not supported in this environment`);\n  }\n}\n\n/**\n * Returns the \"most performant\" supported image type on this platform\n * @returns image type string\n */\nexport function getDefaultImageType(): ImageTypeEnum {\n  if (IMAGE_BITMAP_SUPPORTED) {\n    return 'imagebitmap';\n  }\n  if (IMAGE_SUPPORTED) {\n    return 'image';\n  }\n  if (DATA_SUPPORTED) {\n    return 'data';\n  }\n\n  // This should only happen in Node.js\n  throw new Error('Install \\'@loaders.gl/polyfills\\' to parse images under Node.js');\n}\n", "import type {ImageType, ImageTypeEnum, ImageDataType} from '../../types';\n\nexport function isImage(image: ImageType): boolean {\n  return Boolean(getImageTypeOrNull(image));\n}\n\nexport function deleteImage(image: ImageType): void {\n  switch (getImageType(image)) {\n    case 'imagebitmap':\n      (image as ImageBitmap).close();\n      break;\n    default:\n    // Nothing to do for images and image data objects\n  }\n}\n\nexport function getImageType(image: ImageType): ImageTypeEnum {\n  const format = getImageTypeOrNull(image);\n  if (!format) {\n    throw new Error('Not an image');\n  }\n  return format;\n}\n\nexport function getImageSize(image: ImageType): {width: number; height: number} {\n  return getImageData(image);\n}\n\nexport function getImageData(image: ImageType): ImageDataType | ImageData {\n  switch (getImageType(image)) {\n    case 'data':\n      return image as unknown as ImageData;\n\n    case 'image':\n    case 'imagebitmap':\n      // Extract the image data from the image via a canvas\n      const canvas = document.createElement('canvas');\n      // TODO - reuse the canvas?\n      const context = canvas.getContext('2d');\n      if (!context) {\n        throw new Error('getImageData');\n      }\n      // @ts-ignore\n      canvas.width = image.width;\n      // @ts-ignore\n      canvas.height = image.height;\n      // @ts-ignore\n      context.drawImage(image, 0, 0);\n      // @ts-ignore\n      return context.getImageData(0, 0, image.width, image.height);\n\n    default:\n      throw new Error('getImageData');\n  }\n}\n\n// PRIVATE\n\n// eslint-disable-next-line complexity\nfunction getImageTypeOrNull(image) {\n  if (typeof ImageBitmap !== 'undefined' && image instanceof ImageBitmap) {\n    return 'imagebitmap';\n  }\n  if (typeof Image !== 'undefined' && image instanceof Image) {\n    return 'image';\n  }\n  if (image && typeof image === 'object' && image.data && image.width && image.height) {\n    return 'data';\n  }\n  return null;\n}\n", "// SVG parsing has limitations, e.g:\n// https://bugs.chromium.org/p/chromium/issues/detail?id=606319\n\nconst SVG_DATA_URL_PATTERN = /^data:image\\/svg\\+xml/;\nconst SVG_URL_PATTERN = /\\.svg((\\?|#).*)?$/;\n\nexport function isSVG(url) {\n  return url && (SVG_DATA_URL_PATTERN.test(url) || SVG_URL_PATTERN.test(url));\n}\n\nexport function getBlobOrSVGDataUrl(arrayBuffer: ArrayBuffer, url?: string): Blob | string {\n  if (isSVG(url)) {\n    // Prepare a properly tagged data URL, and load using normal mechanism\n    const textDecoder = new TextDecoder();\n    let xmlText = textDecoder.decode(arrayBuffer);\n    // TODO Escape in browser to support e.g. Chinese characters\n    try {\n      if (typeof unescape === 'function' && typeof encodeURIComponent === 'function') {\n        xmlText = unescape(encodeURIComponent(xmlText));\n      }\n    } catch (error) {\n      throw new Error((error as Error).message);\n    }\n    // base64 encoding is safer. utf-8 fails in some browsers\n    const src = `data:image/svg+xml;base64,${btoa(xmlText)}`;\n    return src;\n  }\n  return getBlob(arrayBuffer, url);\n}\n\nexport function getBlob(arrayBuffer: ArrayBuffer, url?: string): Blob {\n  if (isSVG(url)) {\n    // https://bugs.chromium.org/p/chromium/issues/detail?id=606319\n    // return new Blob([new Uint8Array(arrayBuffer)], {type: 'image/svg+xml'});\n    throw new Error('SVG cannot be parsed directly to imagebitmap');\n  }\n  // TODO - how to determine mime type? Param? Sniff here?\n  return new Blob([new Uint8Array(arrayBuffer)]); // MIME type not needed?\n}\n", "import type {ImageLoaderOptions} from '../../image-loader';\nimport {getBlobOrSVGDataUrl} from './svg-utils';\n\n// Parses html image from array buffer\nexport async function parseToImage(\n  arrayBuffer: ArrayBuffer,\n  options: ImageLoaderOptions,\n  url?: string\n): Promise<HTMLImageElement> {\n  // Note: image parsing requires conversion to Blob (for createObjectURL).\n  // Potentially inefficient for not using `response.blob()` (and for File / Blob inputs)...\n  // But presumably not worth adding 'blob' flag to loader objects?\n\n  const blobOrDataUrl = getBlobOrSVGDataUrl(arrayBuffer, url);\n  const URL = self.URL || self.webkitURL;\n  const objectUrl = typeof blobOrDataUrl !== 'string' && URL.createObjectURL(blobOrDataUrl);\n  try {\n    return await loadToImage(objectUrl || blobOrDataUrl, options);\n  } finally {\n    if (objectUrl) {\n      URL.revokeObjectURL(objectUrl);\n    }\n  }\n}\n\nexport async function loadToImage(url, options): Promise<HTMLImageElement> {\n  const image = new Image();\n  image.src = url;\n\n  // The `image.onload()` callback does not guarantee that the image has been decoded\n  // so a main thread \"freeze\" can be incurred when using the image for the first time.\n  // `Image.decode()` returns a promise that completes when image is decoded.\n\n  // https://developer.mozilla.org/en-US/docs/Web/API/HTMLImageElement/decode\n  // Note: When calling `img.decode()`, we do not need to wait for `img.onload()`\n  // Note: `HTMLImageElement.decode()` is not available in Edge and IE11\n  if (options.image && options.image.decode && image.decode) {\n    await image.decode();\n    return image;\n  }\n\n  // Create a promise that tracks onload/onerror callbacks\n  return await new Promise((resolve, reject) => {\n    try {\n      image.onload = () => resolve(image);\n      image.onerror = (err) => reject(new Error(`Could not load image ${url}: ${err}`));\n    } catch (error) {\n      reject(error);\n    }\n  });\n}\n", "import type {ImageLoaderOptions} from '../../image-loader';\nimport {isSVG, getBlob} from './svg-utils';\nimport {parseToImage} from './parse-to-image';\n\nconst EMPTY_OBJECT = {};\n\nlet imagebitmapOptionsSupported = true;\n\n/**\n * Asynchronously parses an array buffer into an ImageBitmap - this contains the decoded data\n * ImageBitmaps are supported on worker threads, but not supported on Edge, IE11 and Safari\n * https://developer.mozilla.org/en-US/docs/Web/API/ImageBitmap#Browser_compatibility\n *\n * TODO - createImageBitmap supports source rect (5 param overload), pass through?\n */\nexport async function parseToImageBitmap(\n  arrayBuffer: ArrayBuffer,\n  options: ImageLoaderOptions,\n  url?: string\n): Promise<ImageBitmap> {\n  let blob;\n\n  // Cannot parse SVG directly to ImageBitmap, parse to Image first\n  if (isSVG(url)) {\n    // Note: this only works on main thread\n    const image = await parseToImage(arrayBuffer, options, url);\n    blob = image;\n  } else {\n    // Create blob from the array buffer\n    blob = getBlob(arrayBuffer, url);\n  }\n\n  const imagebitmapOptions = options && options.imagebitmap;\n\n  return await safeCreateImageBitmap(blob, imagebitmapOptions);\n}\n\n/**\n * Safely creates an imageBitmap with options\n * *\n * Firefox crashes if imagebitmapOptions is supplied\n * Avoid supplying if not provided or supported, remember if not supported\n */\nasync function safeCreateImageBitmap(\n  blob: Blob,\n  imagebitmapOptions: ImageBitmapOptions | null = null\n): Promise<ImageBitmap> {\n  if (isEmptyObject(imagebitmapOptions) || !imagebitmapOptionsSupported) {\n    imagebitmapOptions = null;\n  }\n\n  if (imagebitmapOptions) {\n    try {\n      // @ts-ignore Options\n      return await createImageBitmap(blob, imagebitmapOptions);\n    } catch (error) {\n      console.warn(error); // eslint-disable-line\n      imagebitmapOptionsSupported = false;\n    }\n  }\n\n  return await createImageBitmap(blob);\n}\n\nfunction isEmptyObject(object) {\n  // @ts-ignore\n  for (const key in object || EMPTY_OBJECT) {\n    return false;\n  }\n  return true;\n}\n", "// loaders.gl, MIT license\n// code adapted from https://github.com/sindresorhus/file-type under MIT license\n\n/**\n * Box is a container format that can contain a variety of media related files,\n * so we want to return information about which type of file is actually contained inside\n */\nexport type BoxFileType = {extension: string; mimeType: string};\n\n/**\n * Tests if a buffer is in ISO base media file format (ISOBMFF) @see https://en.wikipedia.org/wiki/ISO_base_media_file_format\n * (ISOBMFF is a media container standard based on the Apple QuickTime container format)\n */\nexport function getISOBMFFMediaType(buffer: Uint8Array): BoxFileType | null {\n  // Almost all ISO base media files start with `ftyp` box. (It's not required to be first, but it's recommended to be.)\n  if (!checkString(buffer, 'ftyp', 4)) {\n    return null;\n  }\n\n  // Extra check: test for 8859-1 printable characters (for simplicity, it's a mask which also catches one non-printable character).\n  if ((buffer[8] & 0x60) === 0x00) {\n    return null;\n  }\n\n  // `ftyp` box must contain a brand major identifier, which must consist of ISO 8859-1 printable characters.\n  return decodeMajorBrand(buffer);\n}\n\n/**\n * brands explained @see https://github.com/strukturag/libheif/issues/83\n * code adapted from @see https://github.com/sindresorhus/file-type/blob/main/core.js#L489-L492\n */\nexport function decodeMajorBrand(buffer: Uint8Array): BoxFileType | null {\n  const brandMajor = getUTF8String(buffer, 8, 12).replace('\\0', ' ').trim();\n\n  switch (brandMajor) {\n    case 'avif':\n    case 'avis':\n      return {extension: 'avif', mimeType: 'image/avif'};\n    default:\n      return null;\n  }\n  // We don't need these now, but they are easy to add\n  // case 'mif1':\n  //   return {extension: 'heic', mimeType: 'image/heif'};\n  // case 'msf1':\n  //   return {extension: 'heic', mimeType: 'image/heif-sequence'};\n  // case 'heic':\n  // case 'heix':\n  //   return {extension: 'heic', mimeType: 'image/heic'};\n  // case 'hevc':\n  // case 'hevx':\n  //   return {extension: 'heic', mimeType: 'image/heic-sequence'};\n  // case 'qt':\n  //   return {ext: 'mov', mime: 'video/quicktime'};\n  // case 'M4V':\n  // case 'M4VH':\n  // case 'M4VP':\n  //   return {ext: 'm4v', mime: 'video/x-m4v'};\n  // case 'M4P':\n  //   return {ext: 'm4p', mime: 'video/mp4'};\n  // case 'M4B':\n  //   return {ext: 'm4b', mime: 'audio/mp4'};\n  // case 'M4A':\n  //   return {ext: 'm4a', mime: 'audio/x-m4a'};\n  // case 'F4V':\n  //   return {ext: 'f4v', mime: 'video/mp4'};\n  // case 'F4P':\n  //   return {ext: 'f4p', mime: 'video/mp4'};\n  // case 'F4A':\n  //   return {ext: 'f4a', mime: 'audio/mp4'};\n  // case 'F4B':\n  //   return {ext: 'f4b', mime: 'audio/mp4'};\n  // case 'crx':\n  //   return {ext: 'cr3', mime: 'image/x-canon-cr3'};\n  // default:\n  // if (brandMajor.startsWith('3g')) {\n  //   if (brandMajor.startsWith('3g2')) {\n  //     return {ext: '3g2', mime: 'video/3gpp2'};\n  //   }\n  //   return {ext: '3gp', mime: 'video/3gpp'};\n  // }\n  // return {ext: 'mp4', mime: 'video/mp4'};\n}\n\n/** Interpret a chunk of bytes as a UTF8 string */\nfunction getUTF8String(array: Uint8Array, start: number, end: number): string {\n  return String.fromCharCode(...array.slice(start, end));\n}\n\nfunction stringToBytes(string: string): number[] {\n  return [...string].map((character) => character.charCodeAt(0));\n}\n\nfunction checkString(buffer: ArrayLike<number>, header: string, offset: number = 0): boolean {\n  const headerBytes = stringToBytes(header);\n\n  for (let i = 0; i < headerBytes.length; ++i) {\n    if (headerBytes[i] !== buffer[i + offset]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n", "// Attributions\n// * Based on binary-gltf-utils under MIT license: Copyright (c) 2016-17 <PERSON>\n\n// TODO: make these functions work for Node.js buffers?\n// Quarantine references to <PERSON>uff<PERSON> to prevent bundler from adding big polyfills\n// import {bufferToArrayBuffer} from '../node/buffer-to-array-buffer';\n// TODO - this should be handled in @loaders.gl/polyfills\n\nimport {getISOBMFFMediaType} from './parse-isobmff-binary';\n\n/** MIME type, width and height extracted from binary compressed image data */\nexport type BinaryImageMetadata = {\n  mimeType: string;\n  width: number;\n  height: number;\n};\n\nconst BIG_ENDIAN = false;\nconst LITTLE_ENDIAN = true;\n\n/**\n * Extracts `{mimeType, width and height}` from a memory buffer containing a known image format\n * Currently supports `image/png`, `image/jpeg`, `image/bmp` and `image/gif`.\n * @param binaryData: DataView | ArrayBuffer image file memory to parse\n * @returns metadata or null if memory is not a valid image file format layout.\n */\nexport function getBinaryImageMetadata(\n  binaryData: DataView | ArrayBuffer\n): BinaryImageMetadata | null {\n  const dataView = toDataView(binaryData);\n  return (\n    getPngMetadata(dataView) ||\n    getJpegMetadata(dataView) ||\n    getGifMetadata(dataView) ||\n    getBmpMetadata(dataView) ||\n    getISOBMFFMetadata(dataView)\n  );\n}\n\n// ISOBMFF\n\nfunction getISOBMFFMetadata(binaryData: DataView | ArrayBuffer): BinaryImageMetadata | null {\n  const buffer = new Uint8Array(binaryData instanceof DataView ? binaryData.buffer : binaryData);\n  const mediaType = getISOBMFFMediaType(buffer);\n  if (!mediaType) {\n    return null;\n  }\n  return {\n    mimeType: mediaType.mimeType,\n    // TODO - decode width and height\n    width: 0,\n    height: 0\n  };\n}\n\n// PNG\n\nfunction getPngMetadata(binaryData: DataView | ArrayBuffer): BinaryImageMetadata | null {\n  const dataView = toDataView(binaryData);\n  // Check file contains the first 4 bytes of the PNG signature.\n  const isPng = dataView.byteLength >= 24 && dataView.getUint32(0, BIG_ENDIAN) === 0x89504e47;\n  if (!isPng) {\n    return null;\n  }\n\n  // Extract size from a binary PNG file\n  return {\n    mimeType: 'image/png',\n    width: dataView.getUint32(16, BIG_ENDIAN),\n    height: dataView.getUint32(20, BIG_ENDIAN)\n  };\n}\n\n// GIF\n\n// Extract size from a binary GIF file\n// TODO: GIF is not this simple\nfunction getGifMetadata(binaryData: DataView | ArrayBuffer): BinaryImageMetadata | null {\n  const dataView = toDataView(binaryData);\n  // Check first 4 bytes of the GIF signature (\"GIF8\").\n  const isGif = dataView.byteLength >= 10 && dataView.getUint32(0, BIG_ENDIAN) === 0x47494638;\n  if (!isGif) {\n    return null;\n  }\n\n  // GIF is little endian.\n  return {\n    mimeType: 'image/gif',\n    width: dataView.getUint16(6, LITTLE_ENDIAN),\n    height: dataView.getUint16(8, LITTLE_ENDIAN)\n  };\n}\n\n// BMP\n\n// TODO: BMP is not this simple\nexport function getBmpMetadata(binaryData: DataView | ArrayBuffer): BinaryImageMetadata | null {\n  const dataView = toDataView(binaryData);\n  // Check magic number is valid (first 2 characters should be \"BM\").\n  // The mandatory bitmap file header is 14 bytes long.\n  const isBmp =\n    dataView.byteLength >= 14 &&\n    dataView.getUint16(0, BIG_ENDIAN) === 0x424d &&\n    dataView.getUint32(2, LITTLE_ENDIAN) === dataView.byteLength;\n\n  if (!isBmp) {\n    return null;\n  }\n\n  // BMP is little endian.\n  return {\n    mimeType: 'image/bmp',\n    width: dataView.getUint32(18, LITTLE_ENDIAN),\n    height: dataView.getUint32(22, LITTLE_ENDIAN)\n  };\n}\n\n// JPEG\n\n// Extract width and height from a binary JPEG file\nfunction getJpegMetadata(binaryData: DataView | ArrayBuffer): BinaryImageMetadata | null {\n  const dataView = toDataView(binaryData);\n  // Check file contains the JPEG \"start of image\" (SOI) marker\n  // followed by another marker.\n  const isJpeg =\n    dataView.byteLength >= 3 &&\n    dataView.getUint16(0, BIG_ENDIAN) === 0xffd8 &&\n    dataView.getUint8(2) === 0xff;\n\n  if (!isJpeg) {\n    return null;\n  }\n\n  const {tableMarkers, sofMarkers} = getJpegMarkers();\n\n  // Exclude the two byte SOI marker.\n  let i = 2;\n  while (i + 9 < dataView.byteLength) {\n    const marker = dataView.getUint16(i, BIG_ENDIAN);\n\n    // The frame that contains the width and height of the JPEG image.\n    if (sofMarkers.has(marker)) {\n      return {\n        mimeType: 'image/jpeg',\n        height: dataView.getUint16(i + 5, BIG_ENDIAN), // Number of lines\n        width: dataView.getUint16(i + 7, BIG_ENDIAN) // Number of pixels per line\n      };\n    }\n\n    // Miscellaneous tables/data preceding the frame header.\n    if (!tableMarkers.has(marker)) {\n      return null;\n    }\n\n    // Length includes size of length parameter but not the two byte header.\n    i += 2;\n    i += dataView.getUint16(i, BIG_ENDIAN);\n  }\n\n  return null;\n}\n\nfunction getJpegMarkers() {\n  // Tables/misc header markers.\n  // DQT, DHT, DAC, DRI, COM, APP_n\n  const tableMarkers = new Set([0xffdb, 0xffc4, 0xffcc, 0xffdd, 0xfffe]);\n  for (let i = 0xffe0; i < 0xfff0; ++i) {\n    tableMarkers.add(i);\n  }\n\n  // SOF markers and DHP marker.\n  // These markers are after tables/misc data.\n  const sofMarkers = new Set([\n    0xffc0, 0xffc1, 0xffc2, 0xffc3, 0xffc5, 0xffc6, 0xffc7, 0xffc9, 0xffca, 0xffcb, 0xffcd, 0xffce,\n    0xffcf, 0xffde\n  ]);\n\n  return {tableMarkers, sofMarkers};\n}\n\n// TODO - move into image module?\nfunction toDataView(data) {\n  if (data instanceof DataView) {\n    return data;\n  }\n  if (ArrayBuffer.isView(data)) {\n    return new DataView(data.buffer);\n  }\n\n  // TODO: make these functions work for Node.js buffers?\n  // if (bufferToArrayBuffer) {\n  //   data = bufferToArrayBuffer(data);\n  // }\n\n  // Careful - Node Buffers will look like ArrayBuffers (keep after isBuffer)\n  if (data instanceof ArrayBuffer) {\n    return new DataView(data);\n  }\n  throw new Error('toDataView');\n}\n", "import type {ImageLoaderOptions} from '../../image-loader';\nimport type {ImageDataType} from '../../types';\nimport {assert} from '@loaders.gl/loader-utils';\nimport {getBinaryImageMetadata} from '../category-api/binary-image-api';\n\n// Note: These types should be consistent with loaders.gl/polyfills\n\ntype NDArray = {\n  shape: number[];\n  data: Uint8Array;\n  width: number;\n  height: number;\n  components: number;\n  layers: number[];\n};\n\ntype ParseImageNode = (arrayBuffer: ArrayBuffer, mimeType: string) => Promise<NDArray>;\n\n// Use polyfills if installed to parsed image using get-pixels\nexport async function parseToNodeImage(\n  arrayBuffer: ArrayBuffer,\n  options: ImageLoaderOptions\n): Promise<ImageDataType> {\n  const {mimeType} = getBinaryImageMetadata(arrayBuffer) || {};\n\n  // @ts-ignore\n  const _parseImageNode: ParseImageNode = globalThis._parseImageNode;\n  assert(_parseImageNode); // '@loaders.gl/polyfills not installed'\n\n  // @ts-expect-error TODO should we throw error in this case?\n  return await _parseImageNode(arrayBuffer, mimeType);\n}\n", "import type {LoaderContext} from '@loaders.gl/loader-utils';\nimport {assert} from '@loaders.gl/loader-utils';\nimport type {ImageType} from '../../types';\nimport type {ImageLoaderOptions} from '../../image-loader';\nimport {isImageTypeSupported, getDefaultImageType} from '../category-api/image-type';\nimport {getImageData} from '../category-api/parsed-image-api';\nimport {parseToImage} from './parse-to-image';\nimport {parseToImageBitmap} from './parse-to-image-bitmap';\nimport {parseToNodeImage} from './parse-to-node-image';\n\n// Parse to platform defined image type (data on node, ImageBitmap or HTMLImage on browser)\n// eslint-disable-next-line complexity\nexport async function parseImage(\n  arrayBuffer: ArrayBuffer,\n  options?: ImageLoaderOptions,\n  context?: LoaderContext\n): Promise<ImageType> {\n  options = options || {};\n  const imageOptions = options.image || {};\n\n  // The user can request a specific output format via `options.image.type`\n  const imageType = imageOptions.type || 'auto';\n\n  const {url} = context || {};\n\n  // Note: For options.image.type === `data`, we may still need to load as `image` or `imagebitmap`\n  const loadType = getLoadableImageType(imageType);\n\n  let image;\n  switch (loadType) {\n    case 'imagebitmap':\n      image = await parseToImageBitmap(arrayBuffer, options, url);\n      break;\n    case 'image':\n      image = await parseToImage(arrayBuffer, options, url);\n      break;\n    case 'data':\n      // Node.js loads imagedata directly\n      image = await parseToNodeImage(arrayBuffer, options);\n      break;\n    default:\n      assert(false);\n  }\n\n  // Browser: if options.image.type === 'data', we can now extract data from the loaded image\n  if (imageType === 'data') {\n    image = getImageData(image);\n  }\n\n  return image;\n}\n\n// Get a loadable image type from image type\nfunction getLoadableImageType(type) {\n  switch (type) {\n    case 'auto':\n    case 'data':\n      // Browser: For image data we need still need to load using an image format\n      // Node: the default image type is `data`.\n      return getDefaultImageType();\n    default:\n      // Throw an error if not supported\n      isImageTypeSupported(type);\n      return type;\n  }\n}\n", "import type {LoaderOptions, LoaderWithParser} from '@loaders.gl/loader-utils';\nimport {VERSION} from './lib/utils/version';\nimport {parseImage} from './lib/parsers/parse-image';\nimport {getBinaryImageMetadata} from './lib/category-api/binary-image-api';\n\nconst EXTENSIONS = ['png', 'jpg', 'jpeg', 'gif', 'webp', 'bmp', 'ico', 'svg', 'avif'];\nconst MIME_TYPES = [\n  'image/png',\n  'image/jpeg',\n  'image/gif',\n  'image/webp',\n  'image/avif',\n  'image/bmp',\n  'image/vnd.microsoft.icon',\n  'image/svg+xml'\n];\n\nexport type ImageLoaderOptions = LoaderOptions & {\n  image?: {\n    type?: 'auto' | 'data' | 'imagebitmap' | 'image';\n    decode?: boolean;\n  };\n  imagebitmap?: ImageBitmapOptions;\n};\n\nconst DEFAULT_IMAGE_LOADER_OPTIONS: ImageLoaderOptions = {\n  image: {\n    type: 'auto',\n    decode: true // if format is HTML\n  }\n  // imagebitmap: {} - passes (platform dependent) parameters to ImageBitmap constructor\n};\n\n/**\n * Loads a platform-specific image type\n * Note: This type can be used as input data to WebGL texture creation\n */\nexport const ImageLoader = {\n  id: 'image',\n  module: 'images',\n  name: 'Images',\n  version: VERSION,\n  mimeTypes: MIME_TYPES,\n  extensions: EXTENSIONS,\n  parse: parseImage,\n  // TODO: byteOffset, byteLength;\n  tests: [(arrayBuffer) => Boolean(getBinaryImageMetadata(new DataView(arrayBuffer)))],\n  options: DEFAULT_IMAGE_LOADER_OPTIONS\n};\n\nexport const _typecheckImageLoader: LoaderWithParser = ImageLoader;\n", "// Image loading/saving for browser and Node.js\nimport {getImageSize} from '../category-api/parsed-image-api';\n\n// @ts-ignore TS2339: Property does not exist on type\nconst {_encodeImageNode} = globalThis;\n\n/**\n * Returns data bytes representing a compressed image in PNG or JPG format,\n * This data can be saved using file system (f) methods or used in a request.\n * @param image - ImageBitmap Image or Canvas\n * @param options\n * param opt.type='png' - png, jpg or image/png, image/jpg are valid\n * param mimeType= - Whether to include a data URI header\n */\nexport async function encodeImage(\n  image: any,\n  options?: {[key: string]: any}\n): Promise<ArrayBuffer> {\n  options = options || {};\n  options.image = options.image || ({} as {[key: string]: any});\n\n  return _encodeImageNode\n    ? _encodeImageNode(image, {type: options.image.mimeType})\n    : encodeImageInBrowser(image, options);\n}\n\n// In case we get exceptions from canvas.toBlob(resolve, type, quality)\nlet qualityParamSupported = true;\n\n/**\n *\n * @param image\n * @param options\n * @note Based on canvas.toBlob\n * @see https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toBlob\n */\nasync function encodeImageInBrowser(image, options) {\n  const {mimeType, jpegQuality} = options.image;\n\n  const {width, height} = getImageSize(image);\n\n  // create a canvas and resize it to the size of our image\n  const canvas = document.createElement('canvas');\n  canvas.width = width;\n  canvas.height = height;\n\n  drawImageToCanvas(image, canvas);\n\n  // The actual encoding is done asynchronously with `canvas.toBlob()`\n  const blob = await new Promise<Blob | null>((resolve) => {\n    // get it back as a Blob\n    if (jpegQuality && qualityParamSupported) {\n      try {\n        canvas.toBlob(resolve, mimeType, jpegQuality);\n        return;\n      } catch (error) {\n        qualityParamSupported = false;\n      }\n    }\n    canvas.toBlob(resolve, mimeType);\n  });\n\n  if (!blob) {\n    throw new Error('image encoding failed');\n  }\n\n  return await blob.arrayBuffer();\n}\n\nfunction drawImageToCanvas(image, canvas, x = 0, y = 0) {\n  // Try optimized path for ImageBitmaps via bitmaprenderer context\n  if (x === 0 && y === 0 && typeof ImageBitmap !== 'undefined' && image instanceof ImageBitmap) {\n    const context = canvas.getContext('bitmaprenderer');\n    if (context) {\n      // transfer the ImageBitmap to it\n      context.transferFromImageBitmap(image);\n      return canvas;\n    }\n  }\n\n  // Available on most platforms, except IE11 and Andriod WebViews...\n  const context = canvas.getContext('2d');\n  if (image.data) {\n    // ImageData constructor expects clamped array even though getImageData does not return a clamped array...\n    const clampedArray = new Uint8ClampedArray(image.data);\n    const imageData = new ImageData(clampedArray, image.width, image.height);\n    context.putImageData(imageData, 0, 0);\n    return canvas;\n  }\n\n  // Fall back to generic image/image bitmap rendering path\n  context.drawImage(image, 0, 0);\n  return canvas;\n}\n", "// import type {Writer} from '@loaders.gl/loader-utils';\nimport {VERSION} from './lib/utils/version';\nimport {encodeImage} from './lib/encoders/encode-image';\n\nexport const ImageWriter = {\n  name: 'Images',\n  id: 'image',\n  module: 'images',\n  version: VERSION,\n  extensions: ['jpeg'],\n  options: {\n    image: {\n      mimeType: 'image/png',\n      jpegQuality: null\n    }\n  },\n  encode: encodeImage\n};\n", "// loaders.gl, MIT license\n\nimport {isBrowser} from '@loaders.gl/loader-utils';\n\nconst MIME_TYPES = [\n  'image/png',\n  'image/jpeg',\n  'image/gif',\n  'image/webp',\n  'image/avif',\n  'image/tiff',\n  // TODO - what is the correct type for SVG\n  'image/svg',\n  'image/svg+xml',\n  'image/bmp',\n  'image/vnd.microsoft.icon'\n];\n\n/** Only one round of tests is performed */\nconst mimeTypeSupportedPromise: Promise<Set<string>> | null = null;\n\n/** Run-time browser detection of file formats requires async tests for most precise results */\nexport async function getSupportedImageFormats(): Promise<Set<string>> {\n  if (mimeTypeSupportedPromise) {\n    return await mimeTypeSupportedPromise;\n  }\n\n  const supportedMimeTypes = new Set<string>();\n  for (const mimeType of MIME_TYPES) {\n    const supported = isBrowser\n      ? await checkBrowserImageFormatSupportAsync(mimeType)\n      : checkNodeImageFormatSupport(mimeType);\n    if (supported) {\n      supportedMimeTypes.add(mimeType);\n    }\n  }\n\n  return supportedMimeTypes;\n}\n\n/** Cache sync values for speed */\nconst mimeTypeSupportedSync: {[mimeType: string]: boolean} = {};\n\n/**\n * Check if image MIME type is supported. Result is cached to avoid repeated tests.\n */\nexport function isImageFormatSupported(mimeType: string): boolean {\n  if (mimeTypeSupportedSync[mimeType] === undefined) {\n    const supported = isBrowser\n      ? checkBrowserImageFormatSupport(mimeType)\n      : checkNodeImageFormatSupport(mimeType);\n    mimeTypeSupportedSync[mimeType] = supported;\n  }\n  return mimeTypeSupportedSync[mimeType];\n}\n\n/**\n * Checks that polyfills are installed and that mimeType is supported by polyfills\n * @todo Ideally polyfills should declare what formats they support, instead of storing that data here.\n */\nfunction checkNodeImageFormatSupport(mimeType: string): boolean {\n  /** @deprecated Remove these in 4.0 and rely on polyfills to inject them */\n  const NODE_FORMAT_SUPPORT = ['image/png', 'image/jpeg', 'image/gif'];\n  // @ts-ignore\n  const {_parseImageNode, _imageFormatsNode = NODE_FORMAT_SUPPORT} = globalThis;\n  return Boolean(_parseImageNode) && _imageFormatsNode.includes(mimeType);\n}\n\n/** Checks image format support synchronously.\n * @note Unreliable, fails on AVIF\n */\nfunction checkBrowserImageFormatSupport(mimeType: string): boolean {\n  switch (mimeType) {\n    case 'image/avif': // Will fail\n    case 'image/webp':\n      return testBrowserImageFormatSupport(mimeType);\n    default:\n      return true;\n  }\n}\n\nconst TEST_IMAGE = {\n  'image/avif':\n    'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgANogQEAwgMg8f8D///8WfhwB8+ErK42A=',\n  // Lossy test image. Support for lossy images doesn't guarantee support for all WebP images.\n  'image/webp': 'data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA'\n};\n\n/** Checks WebP and AVIF support asynchronously */\nasync function checkBrowserImageFormatSupportAsync(mimeType: string): Promise<boolean> {\n  const dataURL = TEST_IMAGE[mimeType];\n  return dataURL ? await testBrowserImageFormatSupportAsync(dataURL) : true;\n}\n\n/**\n * Checks browser synchronously\n * Checks if toDataURL supports the mimeType.\n * @note Imperfect testOn Chrome this is true for WebP but not for AVIF\n */\nfunction testBrowserImageFormatSupport(mimeType: string): boolean {\n  try {\n    const element = document.createElement('canvas');\n    const dataURL = element.toDataURL(mimeType);\n    return dataURL.indexOf(`data:${mimeType}`) === 0;\n  } catch {\n    // Probably Safari...\n    return false;\n  }\n}\n\n// Check WebPSupport asynchronously\nasync function testBrowserImageFormatSupportAsync(testImageDataURL: string): Promise<boolean> {\n  return new Promise((resolve) => {\n    const image = new Image();\n    image.src = testImageDataURL;\n    image.onload = () => resolve(image.height > 0);\n    image.onerror = () => resolve(false);\n  });\n}\n", "import {resolvePath, assert} from '@loaders.gl/loader-utils';\n\n// Generate a url by calling getUrl with mix of options, applying options.baseUrl\nexport function generateUrl(getUrl, options, urlOptions) {\n  // Get url\n  let url = getUrl;\n  if (typeof getUrl === 'function') {\n    url = getUrl({...options, ...urlOptions});\n  }\n  assert(typeof url === 'string');\n\n  // Apply options.baseUrl\n  const {baseUrl} = options;\n  if (baseUrl) {\n    url = baseUrl[baseUrl.length - 1] === '/' ? `${baseUrl}${url}` : `${baseUrl}/${url}`;\n  }\n\n  return resolvePath(url);\n}\n", "/*\nAsynchronously maps a deep structure of values (e.g. objects and arrays of urls).\n\nE.g. a mipmapped cubemap\n{\n  [CUBE_FACE_FRONT]: [\n    \"image-front-0.jpg\",\n    \"image-front-1.jpg\",\n    \"image-front-2.jpg\",\n  ],\n  [CUBE_MAP_BACK]: [\n    ...\n  ]\n}\n*/\n\nconst isObject = (value) => value && typeof value === 'object';\n\n// Loads a deep structure of urls (objects and arrays of urls)\n// Returns an object with six key-value pairs containing the images (or image mip arrays)\n// for each cube face\nexport async function asyncDeepMap(tree, func, options = {}) {\n  return await mapSubtree(tree, func, options);\n}\n\nexport async function mapSubtree(object, func, options) {\n  if (Array.isArray(object)) {\n    return await mapArray(object, func, options);\n  }\n\n  if (isObject(object)) {\n    return await mapObject(object, func, options);\n  }\n\n  // TODO - ignore non-urls, non-arraybuffers?\n  const url = object;\n  return await func(url, options);\n}\n\n// HELPERS\n\nasync function mapObject(object, func, options) {\n  const promises: Promise<any>[] = [];\n  const values = {};\n\n  for (const key in object) {\n    const url = object[key];\n    const promise = mapSubtree(url, func, options).then((value) => {\n      values[key] = value;\n    });\n    promises.push(promise);\n  }\n\n  await Promise.all(promises);\n\n  return values;\n}\n\nasync function mapArray(urlArray, func, options = {}) {\n  const promises = urlArray.map((url) => mapSubtree(url, func, options));\n  return await Promise.all(promises);\n}\n", "import {asyncDeepMap} from './async-deep-map';\n\nexport async function deepLoad(urlTree, load, options) {\n  return await asyncDeepMap(urlTree, (url) => shallowLoad(url, load, options));\n}\n\nexport async function shallowLoad(url, load, options) {\n  // console.error('loading', url);\n  const response = await fetch(url, options.fetch);\n  const arrayBuffer = await response.arrayBuffer();\n  return await load(arrayBuffer, options);\n}\n", "import {assert} from '@loaders.gl/loader-utils';\nimport {parseImage} from '../parsers/parse-image';\nimport {getImageSize} from '../category-api/parsed-image-api';\nimport {generateUrl} from './generate-url';\nimport {deepLoad, shallowLoad} from './deep-load';\n\nexport async function loadImage(getUrl, options = {}) {\n  const imageUrls = await getImageUrls(getUrl, options);\n  return await deepLoad(imageUrls, parseImage, options);\n}\n\nexport async function getImageUrls(getUrl, options, urlOptions = {}) {\n  const mipLevels = (options && options.image && options.image.mipLevels) || 0;\n  return mipLevels !== 0\n    ? await getMipmappedImageUrls(getUrl, mipLevels, options, urlOptions)\n    : generateUrl(getUrl, options, urlOptions);\n}\n\nasync function getMipmappedImageUrls(getUrl, mipLevels, options, urlOptions) {\n  const urls: string[] = [];\n\n  // If no mip levels supplied, we need to load the level 0 image and calculate based on size\n  if (mipLevels === 'auto') {\n    const url = generateUrl(getUrl, options, {...urlOptions, lod: 0});\n    const image = await shallowLoad(url, parseImage, options);\n\n    const {width, height} = getImageSize(image);\n    mipLevels = getMipLevels({width, height});\n\n    // TODO - push image and make `deepLoad` pass through non-url values, avoid loading twice?\n    urls.push(url);\n  }\n\n  // We now know how many mipLevels we need, remaining image urls can now be constructed\n  assert(mipLevels > 0);\n\n  for (let mipLevel = urls.length; mipLevel < mipLevels; ++mipLevel) {\n    const url = generateUrl(getUrl, options, {...urlOptions, lod: mipLevel});\n    urls.push(url);\n  }\n\n  return urls;\n}\n\n// Calculates number of mipmaps based on texture size (log2)\nexport function getMipLevels({width, height}) {\n  return 1 + Math.floor(Math.log2(Math.max(width, height)));\n}\n", "// TYPES\nexport type {ImageDataType, ImageType, ImageTypeEnum} from './types';\nexport type {ImageLoaderOptions} from './image-loader';\n\n// LOADERS AND WRITERS\nexport {ImageLoader} from './image-loader';\nexport {ImageWriter} from './image-writer';\n\n// IMAGE CATEGORY API\n\n// Binary Image API\nexport {getBinaryImageMetadata} from './lib/category-api/binary-image-api';\n\n// Parsed Image API\nexport {isImageTypeSupported, getDefaultImageType} from './lib/category-api/image-type';\n\nexport {\n  isImage,\n  getImageType,\n  getImageSize,\n  getImageData\n} from './lib/category-api/parsed-image-api';\n\n// EXPERIMENTAL\nexport {getSupportedImageFormats} from './lib/category-api/image-format';\nexport {isImageFormatSupported} from './lib/category-api/image-format';\n\n// DEPRECATED - Remove in V4 (fix dependency in luma.gl)\nexport {loadImage} from './lib/texture-api/load-image';\n", "// @ts-nocheck\nconst moduleExports = require('./index');\nglobalThis.loaders = globalThis.loaders || {};\nmodule.exports = Object.assign(globalThis.loaders, moduleExports);\n"], "mappings": "4OAAA,GAGa,GAHb,SAGO,AAAM,EAAU,MAAO,cAAgB,YAAc,YAAc,WCCnE,WAAgB,EAAgB,EAAwB,CAC7D,GAAI,CAAC,EACH,KAAM,IAAI,OAAM,GAAW,4BAN/B,gBCAA,GAIM,GAQA,GACA,GACA,GACA,GAKO,EAQP,EAGO,GA/Bb,SAIA,AAAM,EAAU,CACd,KAAM,MAAO,OAAS,aAAe,KACrC,OAAQ,MAAO,SAAW,aAAe,OACzC,OAAQ,MAAO,SAAW,aAAe,OACzC,SAAU,MAAO,WAAa,aAAe,UAIzC,GAAa,EAAQ,MAAQ,EAAQ,QAAU,EAAQ,QAAU,GACjE,GAAe,EAAQ,QAAU,EAAQ,MAAQ,EAAQ,QAAU,GACnE,GAAe,EAAQ,QAAU,EAAQ,MAAQ,EAAQ,QAAU,GACnE,GAAiB,EAAQ,UAAY,GAK9B,EAEX,QAAQ,MAAO,UAAY,UAAY,OAAO,WAAa,oBAAsB,QAAQ,SAMrF,EACJ,MAAO,UAAY,aAAe,QAAQ,SAAW,YAAY,KAAK,QAAQ,SAEnE,GAAuB,GAAW,WAAW,EAAQ,KAAQ,ICEnE,WAAqB,EAA0B,CACpD,OAAW,KAAS,GAClB,GAAI,EAAS,WAAW,GAAQ,CAC9B,GAAM,GAAc,EAAY,GAChC,EAAW,EAAS,QAAQ,EAAO,GAGvC,MAAI,CAAC,EAAS,WAAW,YAAc,CAAC,EAAS,WAAW,aAC1D,GAAW,GAAG,KAAa,KAEtB,EA3CT,GAEI,IACE,EAHN,SAEA,AAAI,GAAa,GACX,EAA6C,KCHnD,aAgBA,IACA,IAkDA,MCpDO,WAA8B,EAAuB,CAC1D,OAAQ,OACD,OAEH,MAAO,IAA0B,GAAmB,MAEjD,cACH,MAAO,OACJ,QACH,MAAO,OACJ,OACH,MAAO,WAGP,KAAM,IAAI,OAAM,6BAA6B,wCAQ5C,YAA8C,CACnD,GAAI,EACF,MAAO,cAET,GAAI,EACF,MAAO,QAET,GAAI,EACF,MAAO,OAIT,KAAM,IAAI,OAAM,iEAjDlB,GAIO,IAED,EACA,EACA,GACA,EATN,aAIA,AAAM,EAAC,oBAAmB,YAEpB,EAAkB,MAAO,QAAU,YACnC,EAAyB,MAAO,cAAgB,YAChD,GAAuB,QAAQ,IAC/B,EAAiB,EAAY,GAAO,KCPnC,WAAiB,EAA2B,CACjD,MAAO,SAAQ,EAAmB,IAa7B,WAAsB,EAAiC,CAC5D,GAAM,GAAS,EAAmB,GAClC,GAAI,CAAC,EACH,KAAM,IAAI,OAAM,gBAElB,MAAO,GAGF,WAAsB,EAAmD,CAC9E,MAAO,GAAa,GAGf,WAAsB,EAA6C,CACxE,OAAQ,EAAa,QACd,OACH,MAAO,OAEJ,YACA,cAEH,GAAM,GAAS,SAAS,cAAc,UAEhC,EAAU,EAAO,WAAW,MAClC,GAAI,CAAC,EACH,KAAM,IAAI,OAAM,gBAGlB,SAAO,MAAQ,EAAM,MAErB,EAAO,OAAS,EAAM,OAEtB,EAAQ,UAAU,EAAO,EAAG,GAErB,EAAQ,aAAa,EAAG,EAAG,EAAM,MAAO,EAAM,gBAGrD,KAAM,IAAI,OAAM,iBAOtB,WAA4B,EAAO,CACjC,MAAI,OAAO,cAAgB,aAAe,YAAiB,aAClD,cAEL,MAAO,QAAU,aAAe,YAAiB,OAC5C,QAEL,GAAS,MAAO,IAAU,UAAY,EAAM,MAAQ,EAAM,OAAS,EAAM,OACpE,OAEF,KArET,gBCMO,WAAe,EAAK,CACzB,MAAO,IAAQ,IAAqB,KAAK,IAAQ,GAAgB,KAAK,IAGjE,WAA6B,EAA0B,EAA6B,CACzF,GAAI,EAAM,GAAM,CAGd,GAAI,GAAU,AADM,GAAI,eACE,OAAO,GAEjC,GAAI,CACF,AAAI,MAAO,WAAa,YAAc,MAAO,qBAAuB,YAClE,GAAU,SAAS,mBAAmB,WAEjC,EAAP,CACA,KAAM,IAAI,OAAO,EAAgB,SAInC,MADY,6BAA6B,KAAK,KAGhD,MAAO,GAAQ,EAAa,GAGvB,WAAiB,EAA0B,EAAoB,CACpE,GAAI,EAAM,GAGR,KAAM,IAAI,OAAM,gDAGlB,MAAO,IAAI,MAAK,CAAC,GAAI,YAAW,KArClC,GAGM,IACA,GAJN,SAGA,AAAM,GAAuB,wBACvB,GAAkB,sBCAxB,iBACE,EACA,EACA,EAC2B,CAK3B,GAAM,GAAgB,EAAoB,EAAa,GACjD,EAAM,KAAK,KAAO,KAAK,UACvB,EAAY,MAAO,IAAkB,UAAY,EAAI,gBAAgB,GAC3E,GAAI,CACF,MAAO,MAAM,IAAY,GAAa,EAAe,UACrD,CACA,AAAI,GACF,EAAI,gBAAgB,IAK1B,kBAAkC,EAAK,EAAoC,CACzE,GAAM,GAAQ,GAAI,OAUlB,MATA,GAAM,IAAM,EASR,EAAQ,OAAS,EAAQ,MAAM,QAAU,EAAM,OACjD,MAAM,GAAM,SACL,GAIF,KAAM,IAAI,SAAQ,CAAC,EAAS,IAAW,CAC5C,GAAI,CACF,EAAM,OAAS,IAAM,EAAQ,GAC7B,EAAM,QAAU,AAAC,GAAQ,EAAO,GAAI,OAAM,wBAAwB,MAAQ,YACnE,EAAP,CACA,EAAO,MA/Cb,aACA,MCcA,kBACE,EACA,EACA,EACsB,CACtB,GAAI,GAGJ,AAAI,EAAM,GAGR,EADc,KAAM,GAAa,EAAa,EAAS,GAIvD,EAAO,EAAQ,EAAa,GAG9B,GAAM,GAAqB,GAAW,EAAQ,YAE9C,MAAO,MAAM,IAAsB,EAAM,GAS3C,kBACE,EACA,EAAgD,KAC1B,CAKtB,GAJI,IAAc,IAAuB,CAAC,IACxC,GAAqB,MAGnB,EACF,GAAI,CAEF,MAAO,MAAM,mBAAkB,EAAM,SAC9B,EAAP,CACA,QAAQ,KAAK,GACb,EAA8B,GAIlC,MAAO,MAAM,mBAAkB,GAGjC,YAAuB,EAAQ,CAE7B,OAAW,KAAO,IAAU,GAC1B,MAAO,GAET,MAAO,GArET,GAIM,IAEF,EANJ,UACA,IACA,IAEA,AAAM,GAAe,GAEjB,EAA8B,KCO3B,YAA6B,EAAwC,CAO1E,MALI,CAAC,GAAY,EAAQ,OAAQ,IAK5B,GAAO,GAAK,KAAU,EAClB,KAIF,GAAiB,GAOnB,YAA0B,EAAwC,CAGvE,OAFmB,GAAc,EAAQ,EAAG,IAAI,QAAQ,KAAM,KAAK,YAG5D,WACA,OACH,MAAO,CAAC,UAAW,OAAQ,SAAU,sBAErC,MAAO,OA8Cb,YAAuB,EAAmB,EAAe,EAAqB,CAC5E,MAAO,QAAO,aAAa,GAAG,EAAM,MAAM,EAAO,IAGnD,YAAuB,EAA0B,CAC/C,MAAO,CAAC,GAAG,GAAQ,IAAI,AAAC,GAAc,EAAU,WAAW,IAG7D,YAAqB,EAA2B,EAAgB,EAAiB,EAAY,CAC3F,GAAM,GAAc,GAAc,GAElC,OAAS,GAAI,EAAG,EAAI,EAAY,OAAQ,EAAE,EACxC,GAAI,EAAY,KAAO,EAAO,EAAI,GAChC,MAAO,GAIX,MAAO,GAvGT,iBC0BO,WACL,EAC4B,CAC5B,GAAM,GAAW,EAAW,GAC5B,MACE,IAAe,IACf,GAAgB,IAChB,GAAe,IACf,GAAe,IACf,GAAmB,GAMvB,YAA4B,EAAgE,CAC1F,GAAM,GAAS,GAAI,YAAW,YAAsB,UAAW,EAAW,OAAS,GAC7E,EAAY,GAAoB,GACtC,MAAK,GAGE,CACL,SAAU,EAAU,SAEpB,MAAO,EACP,OAAQ,GAND,KAYX,YAAwB,EAAgE,CACtF,GAAM,GAAW,EAAW,GAG5B,MADc,GAAS,YAAc,IAAM,EAAS,UAAU,EAAG,KAAgB,WAM1E,CACL,SAAU,YACV,MAAO,EAAS,UAAU,GAAI,GAC9B,OAAQ,EAAS,UAAU,GAAI,IAPxB,KAeX,YAAwB,EAAgE,CACtF,GAAM,GAAW,EAAW,GAG5B,MADc,GAAS,YAAc,IAAM,EAAS,UAAU,EAAG,KAAgB,WAM1E,CACL,SAAU,YACV,MAAO,EAAS,UAAU,EAAG,GAC7B,OAAQ,EAAS,UAAU,EAAG,IAPvB,KAcJ,YAAwB,EAAgE,CAC7F,GAAM,GAAW,EAAW,GAQ5B,MAJE,GAAS,YAAc,IACvB,EAAS,UAAU,EAAG,KAAgB,OACtC,EAAS,UAAU,EAAG,KAAmB,EAAS,WAO7C,CACL,SAAU,YACV,MAAO,EAAS,UAAU,GAAI,GAC9B,OAAQ,EAAS,UAAU,GAAI,IAPxB,KAcX,YAAyB,EAAgE,CACvF,GAAM,GAAW,EAAW,GAQ5B,GAAI,CAJF,GAAS,YAAc,GACvB,EAAS,UAAU,EAAG,KAAgB,OACtC,EAAS,SAAS,KAAO,KAGzB,MAAO,MAGT,GAAM,CAAC,eAAc,cAAc,KAG/B,EAAI,EACR,KAAO,EAAI,EAAI,EAAS,YAAY,CAClC,GAAM,GAAS,EAAS,UAAU,EAAG,GAGrC,GAAI,EAAW,IAAI,GACjB,MAAO,CACL,SAAU,aACV,OAAQ,EAAS,UAAU,EAAI,EAAG,GAClC,MAAO,EAAS,UAAU,EAAI,EAAG,IAKrC,GAAI,CAAC,EAAa,IAAI,GACpB,MAAO,MAIT,GAAK,EACL,GAAK,EAAS,UAAU,EAAG,GAG7B,MAAO,MAGT,aAA0B,CAGxB,GAAM,GAAe,GAAI,KAAI,CAAC,MAAQ,MAAQ,MAAQ,MAAQ,QAC9D,OAAS,GAAI,MAAQ,EAAI,MAAQ,EAAE,EACjC,EAAa,IAAI,GAKnB,GAAM,GAAa,GAAI,KAAI,CACzB,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MACxF,MAAQ,QAGV,MAAO,CAAC,eAAc,cAIxB,WAAoB,EAAM,CACxB,GAAI,YAAgB,UAClB,MAAO,GAET,GAAI,YAAY,OAAO,GACrB,MAAO,IAAI,UAAS,EAAK,QAS3B,GAAI,YAAgB,aAClB,MAAO,IAAI,UAAS,GAEtB,KAAM,IAAI,OAAM,cAtMlB,GAiBM,GACA,EAlBN,SAQA,KASA,AAAM,EAAa,GACb,EAAgB,KCCtB,kBACE,EACA,EACwB,CACxB,GAAM,CAAC,YAAY,EAAuB,IAAgB,GAGpD,EAAkC,WAAW,gBACnD,SAAO,GAGA,KAAM,GAAgB,EAAa,GA9B5C,cAEA,IACA,MCSA,iBACE,EACA,EACA,EACoB,CACpB,EAAU,GAAW,GAIrB,GAAM,GAAY,AAHG,GAAQ,OAAS,IAGP,MAAQ,OAEjC,CAAC,OAAO,GAAW,GAGnB,EAAW,GAAqB,GAElC,EACJ,OAAQ,OACD,cACH,EAAQ,KAAM,IAAmB,EAAa,EAAS,GACvD,UACG,QACH,EAAQ,KAAM,GAAa,EAAa,EAAS,GACjD,UACG,OAEH,EAAQ,KAAM,IAAiB,EAAa,GAC5C,cAEA,EAAO,IAIX,MAAI,KAAc,QAChB,GAAQ,EAAa,IAGhB,EAIT,YAA8B,EAAM,CAClC,OAAQ,OACD,WACA,OAGH,MAAO,aAGP,SAAqB,GACd,GA/Db,aACA,IAGA,IACA,IACA,IACA,KACA,OCRA,GAKM,IACA,GAmBA,GAYO,GArCb,UACA,IACA,IACA,IAEA,AAAM,GAAa,CAAC,MAAO,MAAO,OAAQ,MAAO,OAAQ,MAAO,MAAO,MAAO,QACxE,GAAa,CACjB,YACA,aACA,YACA,aACA,aACA,YACA,2BACA,iBAWI,GAAmD,CACvD,MAAO,CACL,KAAM,OACN,OAAQ,KASC,GAAc,CACzB,GAAI,QACJ,OAAQ,SACR,KAAM,SACN,QAAS,EACT,UAAW,GACX,WAAY,GACZ,MAAO,EAEP,MAAO,CAAC,AAAC,GAAgB,QAAQ,EAAuB,GAAI,UAAS,MACrE,QAAS,MCjCX,kBACE,EACA,EACsB,CACtB,SAAU,GAAW,GACrB,EAAQ,MAAQ,EAAQ,OAAU,GAE3B,GACH,GAAiB,EAAO,CAAC,KAAM,EAAQ,MAAM,WAC7C,GAAqB,EAAO,GAalC,kBAAoC,EAAO,EAAS,CAClD,GAAM,CAAC,WAAU,eAAe,EAAQ,MAElC,CAAC,QAAO,UAAU,EAAa,GAG/B,EAAS,SAAS,cAAc,UACtC,EAAO,MAAQ,EACf,EAAO,OAAS,EAEhB,GAAkB,EAAO,GAGzB,GAAM,GAAO,KAAM,IAAI,SAAqB,AAAC,GAAY,CAEvD,GAAI,GAAe,GACjB,GAAI,CACF,EAAO,OAAO,EAAS,EAAU,GACjC,YACA,CACA,GAAwB,GAG5B,EAAO,OAAO,EAAS,KAGzB,GAAI,CAAC,EACH,KAAM,IAAI,OAAM,yBAGlB,MAAO,MAAM,GAAK,cAGpB,YAA2B,EAAO,EAAQ,EAAI,EAAG,EAAI,EAAG,CAEtD,GAAI,IAAM,GAAK,IAAM,GAAK,MAAO,cAAgB,aAAe,YAAiB,aAAa,CAC5F,GAAM,GAAU,EAAO,WAAW,kBAClC,GAAI,EAEF,SAAQ,wBAAwB,GACzB,EAKX,GAAM,GAAU,EAAO,WAAW,MAClC,GAAI,EAAM,KAAM,CAEd,GAAM,GAAe,GAAI,mBAAkB,EAAM,MAC3C,EAAY,GAAI,WAAU,EAAc,EAAM,MAAO,EAAM,QACjE,SAAQ,aAAa,EAAW,EAAG,GAC5B,EAIT,SAAQ,UAAU,EAAO,EAAG,GACrB,EA5FT,GAIO,IAuBH,GA3BJ,UACA,IAGA,AAAM,EAAC,qBAAoB,YAuB3B,AAAI,GAAwB,KC3B5B,GAIa,IAJb,UACA,IACA,KAEO,AAAM,GAAc,CACzB,KAAM,SACN,GAAI,QACJ,OAAQ,SACR,QAAS,EACT,WAAY,CAAC,QACb,QAAS,CACP,MAAO,CACL,SAAU,YACV,YAAa,OAGjB,OAAQ,MCMV,mBAAuE,CACrE,GAAI,GACF,MAAO,MAAM,IAGf,GAAM,GAAqB,GAAI,KAC/B,OAAW,KAAY,IAIrB,AAAI,AAHc,GACd,KAAM,IAAoC,GAC1C,GAA4B,KAE9B,EAAmB,IAAI,GAI3B,MAAO,GASF,YAAgC,EAA2B,CAChE,GAAI,EAAsB,KAAc,OAAW,CACjD,GAAM,GAAY,EACd,GAA+B,GAC/B,GAA4B,GAChC,EAAsB,GAAY,EAEpC,MAAO,GAAsB,GAO/B,YAAqC,EAA2B,CAE9D,GAAM,GAAsB,CAAC,YAAa,aAAc,aAElD,CAAC,kBAAiB,oBAAoB,GAAuB,WACnE,MAAO,SAAQ,IAAoB,EAAkB,SAAS,GAMhE,YAAwC,EAA2B,CACjE,OAAQ,OACD,iBACA,aACH,MAAO,IAA8B,WAErC,MAAO,IAYb,kBAAmD,EAAoC,CACrF,GAAM,GAAU,GAAW,GAC3B,MAAO,GAAU,KAAM,IAAmC,GAAW,GAQvE,YAAuC,EAA2B,CAChE,GAAI,CAGF,MAAO,AADS,AADA,UAAS,cAAc,UACf,UAAU,GACnB,QAAQ,QAAQ,OAAgB,OAC/C,CAEA,MAAO,IAKX,kBAAkD,EAA4C,CAC5F,MAAO,IAAI,SAAQ,AAAC,GAAY,CAC9B,GAAM,GAAQ,GAAI,OAClB,EAAM,IAAM,EACZ,EAAM,OAAS,IAAM,EAAQ,EAAM,OAAS,GAC5C,EAAM,QAAU,IAAM,EAAQ,MApHlC,GAIM,IAeA,GAsBA,EAwCA,GAjFN,SAEA,IAEA,AAAM,GAAa,CACjB,YACA,aACA,YACA,aACA,aACA,aAEA,YACA,gBACA,YACA,4BAII,GAAwD,KAsB9D,AAAM,EAAuD,GAwC7D,AAAM,GAAa,CACjB,aACE,0bAEF,aAAc,qFClFT,WAAqB,EAAQ,EAAS,EAAY,CAEvD,GAAI,GAAM,EACV,AAAI,MAAO,IAAW,YACpB,GAAM,EAAO,IAAI,KAAY,KAE/B,EAAO,MAAO,IAAQ,UAGtB,GAAM,CAAC,WAAW,EAClB,MAAI,IACF,GAAM,EAAQ,EAAQ,OAAS,KAAO,IAAM,GAAG,IAAU,IAAQ,GAAG,KAAW,KAG1E,EAAY,GAjBrB,oBCqBA,kBAAmC,EAAM,EAAM,EAAU,GAAI,CAC3D,MAAO,MAAM,GAAW,EAAM,EAAM,GAGtC,iBAAiC,EAAQ,EAAM,EAAS,CACtD,MAAI,OAAM,QAAQ,GACT,KAAM,IAAS,EAAQ,EAAM,GAGlC,GAAS,GACJ,KAAM,IAAU,EAAQ,EAAM,GAKhC,KAAM,GADD,EACW,GAKzB,kBAAyB,EAAQ,EAAM,EAAS,CAC9C,GAAM,GAA2B,GAC3B,EAAS,GAEf,OAAW,KAAO,GAAQ,CACxB,GAAM,GAAM,EAAO,GACb,EAAU,EAAW,EAAK,EAAM,GAAS,KAAK,AAAC,GAAU,CAC7D,EAAO,GAAO,IAEhB,EAAS,KAAK,GAGhB,YAAM,SAAQ,IAAI,GAEX,EAGT,kBAAwB,EAAU,EAAM,EAAU,GAAI,CACpD,GAAM,GAAW,EAAS,IAAI,AAAC,GAAQ,EAAW,EAAK,EAAM,IAC7D,MAAO,MAAM,SAAQ,IAAI,GA5D3B,GAgBM,IAhBN,UAgBA,AAAM,GAAW,AAAC,GAAU,GAAS,MAAO,IAAU,WCdtD,kBAA+B,EAAS,EAAM,EAAS,CACrD,MAAO,MAAM,IAAa,EAAS,AAAC,GAAQ,EAAY,EAAK,EAAM,IAGrE,iBAAkC,EAAK,EAAM,EAAS,CAGpD,GAAM,GAAc,KAAM,AADT,MAAM,OAAM,EAAK,EAAQ,QACP,cACnC,MAAO,MAAM,GAAK,EAAa,GAVjC,qBCMA,kBAAgC,EAAQ,EAAU,GAAI,CACpD,GAAM,GAAY,KAAM,IAAa,EAAQ,GAC7C,MAAO,MAAM,IAAS,EAAW,EAAY,GAG/C,kBAAmC,EAAQ,EAAS,EAAa,GAAI,CACnE,GAAM,GAAa,GAAW,EAAQ,OAAS,EAAQ,MAAM,WAAc,EAC3E,MAAO,KAAc,EACjB,KAAM,IAAsB,EAAQ,EAAW,EAAS,GACxD,EAAY,EAAQ,EAAS,GAGnC,kBAAqC,EAAQ,EAAW,EAAS,EAAY,CAC3E,GAAM,GAAiB,GAGvB,GAAI,IAAc,OAAQ,CACxB,GAAM,GAAM,EAAY,EAAQ,EAAS,IAAI,EAAY,IAAK,IACxD,EAAQ,KAAM,GAAY,EAAK,EAAY,GAE3C,CAAC,QAAO,UAAU,EAAa,GACrC,EAAY,GAAa,CAAC,QAAO,WAGjC,EAAK,KAAK,GAIZ,EAAO,EAAY,GAEnB,OAAS,GAAW,EAAK,OAAQ,EAAW,EAAW,EAAE,EAAU,CACjE,GAAM,GAAM,EAAY,EAAQ,EAAS,IAAI,EAAY,IAAK,IAC9D,EAAK,KAAK,GAGZ,MAAO,GAIF,YAAsB,CAAC,QAAO,UAAS,CAC5C,MAAO,GAAI,KAAK,MAAM,KAAK,KAAK,KAAK,IAAI,EAAO,KA9ClD,kBACA,IACA,IACA,KACA,OCJA,+SAKA,KACA,KAKA,IAGA,IAEA,IAQA,IACA,IAGA,OC5BA,oBACA,GAAM,IAAgB,UACtB,WAAW,QAAU,WAAW,SAAW,GAC3C,GAAO,QAAU,OAAO,OAAO,WAAW,QAAS", "names": []}