{"name": "@loaders.gl/images", "version": "3.4.15", "description": "Framework-independent loaders and writers for images (PNG, JPG, ...)", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/visgl/loaders.gl"}, "keywords": ["webgl", "loader", "3d", "mesh", "point cloud", "PLY"], "types": "dist/index.d.ts", "main": "dist/es5/index.js", "module": "dist/esm/index.js", "sideEffects": false, "files": ["src", "dist", "README.md"], "scripts": {"pre-build": "npm run build-bundle", "build-bundle": "esbuild src/bundle.ts --outfile=dist/dist.min.js --bundle --minify --sourcemap"}, "dependencies": {"@loaders.gl/loader-utils": "3.4.15"}, "gitHead": "19e941d5805568e449ef9092490d6568a4853298", "devDependencies": {"@types/get-pixels": "^3.3.2"}}