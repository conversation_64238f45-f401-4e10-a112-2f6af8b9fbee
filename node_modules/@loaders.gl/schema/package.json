{"name": "@loaders.gl/schema", "version": "3.4.15", "description": "Table format APIs for JSON, CSV, etc...", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/visgl/loaders.gl"}, "keywords": ["webgl", "loader", "3d", "mesh", "point cloud", "PLY"], "types": "dist/index.d.ts", "main": "dist/es5/index.js", "module": "dist/esm/index.js", "sideEffects": false, "files": ["src", "dist", "README.md"], "scripts": {"pre-build": "npm run build-bundle", "build-bundle": "esbuild src/bundle.ts --bundle --outfile=dist/dist.min.js"}, "dependencies": {"@types/geojson": "^7946.0.7"}, "gitHead": "19e941d5805568e449ef9092490d6568a4853298"}