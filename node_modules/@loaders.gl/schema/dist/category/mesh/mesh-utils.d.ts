import { TypedArray } from '../../types';
import { MeshAttributes } from './mesh-types';
type TypedArrays = {
    [key: string]: TypedArray;
};
/**
 * Holds an axis aligned bounding box
 * TODO - make sure AxisAlignedBoundingBox in math.gl/culling understands this format (or change this format)
 */
type BoundingBox = [[number, number, number], [number, number, number]];
/**
 * Get number of vertices in mesh
 * @param attributes
 */
export declare function getMeshSize(attributes: TypedArrays): number;
/**
 * Get the (axis aligned) bounding box of a mesh
 * @param attributes
 * @returns array of two vectors representing the axis aligned bounding box
 */
export declare function getMeshBoundingBox(attributes: MeshAttributes): BoundingBox;
export {};
//# sourceMappingURL=mesh-utils.d.ts.map