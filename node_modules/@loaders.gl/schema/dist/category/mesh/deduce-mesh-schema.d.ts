import { MeshAttribute, MeshAttributes } from './mesh-types';
import { Schema, Field } from '../../lib/schema/schema';
/**
 * Create a schema for mesh attributes data
 * @param attributes
 * @param metadata
 * @returns
 */
export declare function deduceMeshSchema(attributes: MeshAttributes, metadata?: Map<string, string>): Schema;
/**
 * Create arrow-like schema field for mesh attribute
 * @param attributeName
 * @param attribute
 * @param optionalMetadata
 * @returns
 */
export declare function deduceMeshField(attributeName: string, attribute: MeshAttribute, optionalMetadata?: Map<string, string>): Field;
/**
 * Make metadata by mesh attribute properties
 * @param attribute
 * @returns
 */
export declare function makeMeshAttributeMetadata(attribute: MeshAttribute): Map<string, string>;
//# sourceMappingURL=deduce-mesh-schema.d.ts.map