{"version": 3, "file": "index.js", "names": ["default", "TableBatchBuilder", "RowTableBatchAggregator", "ColumnarTableBatchAggregator", "convertToObjectRow", "convertToArrayRow", "getMeshSize", "getMeshBoundingBox", "deduceMeshSchema", "deduce<PERSON><PERSON><PERSON><PERSON>", "makeMeshAttributeMetadata", "<PERSON><PERSON><PERSON>", "Field", "DataType", "<PERSON><PERSON>", "Binary", "Bool", "Int", "Int8", "Int16", "Int32", "Int64", "Uint8", "Uint16", "Uint32", "Uint64", "Float", "Float16", "Float32", "Float64", "Utf8", "Date", "DateDay", "DateMillisecond", "Time", "TimeMillisecond", "TimeSecond", "Timestamp", "TimestampSecond", "TimestampMillisecond", "TimestampMicrosecond", "TimestampNanosecond", "Interval", "IntervalDayTime", "IntervalYearMonth", "FixedSizeList", "Struct", "deduceTypeFromColumn", "deduceTypeFromValue", "getTypeInfo", "getArrowTypeFromTypedArray", "AsyncQueue"], "sources": ["../../src/index.ts"], "sourcesContent": ["// COMMON CATEGORY\nexport type {TypedArray, NumberArray, AnyArray} from './types';\n\nexport type {Batch} from './category/common';\n\n// TABLE CATEGORY TYPES\nexport type {\n  Table,\n  ArrayRowTable,\n  ObjectRowTable,\n  GeoJSONRowTable,\n  ColumnarTable,\n  ArrowTable,\n  Tables\n} from './category/table/table-types';\nexport type {\n  TableBatch,\n  RowArrayTableBatch,\n  RowObjectTableBatch,\n  GeoJSONRowTableBatch,\n  ColumnarTableBatch,\n  ArrowTableBatch\n} from './category/table/table-types';\n\n// TABLE CATEGORY UTILS\nexport {default as TableBatchBuilder} from './lib/batches/table-batch-builder';\nexport type {TableBatchAggregator} from './lib/batches/table-batch-aggregator';\nexport {default as RowTableBatchAggregator} from './lib/batches/row-table-batch-aggregator';\nexport {default as ColumnarTableBatchAggregator} from './lib/batches/columnar-table-batch-aggregator';\n\nexport {convertToObjectRow, convertToArrayRow} from './lib/utils/row-utils';\n\n// MESH CATEGORY\nexport type {\n  MeshTable,\n  MeshArrowTable,\n  Mesh,\n  MeshGeometry,\n  MeshAttribute,\n  MeshAttributes\n} from './category/mesh/mesh-types';\n\nexport {getMeshSize, getMeshBoundingBox} from './category/mesh/mesh-utils';\n// Commented out due to https://github.com/visgl/deck.gl/issues/6906 and https://github.com/visgl/loaders.gl/issues/2177\n// export {convertMesh} from './category/mesh/convert-mesh';\nexport {\n  deduceMeshSchema,\n  deduceMeshField,\n  makeMeshAttributeMetadata\n} from './category/mesh/deduce-mesh-schema';\n\n// TEXTURES\nexport type {TextureLevel, GPUTextureFormat} from './category/texture/texture';\n\n// IMAGES\nexport type {ImageDataType, ImageType, ImageTypeEnum} from './category/image/image';\n\n// TYPES\n// GIS CATEGORY - GEOJSON\nexport type {\n  GeoJSON,\n  Feature,\n  FeatureCollection,\n  Geometry,\n  Position,\n  GeoJsonProperties,\n  Point,\n  MultiPoint,\n  LineString,\n  MultiLineString,\n  Polygon,\n  MultiPolygon,\n  GeometryCollection\n} from './category/gis';\n\nexport type {GeojsonGeometryInfo} from './category/gis';\n\n// GIS CATEGORY - FLAT GEOJSON\nexport type {\n  FlatFeature,\n  FlatIndexedGeometry,\n  FlatGeometry,\n  FlatGeometryType,\n  FlatPoint,\n  FlatLineString,\n  FlatPolygon\n} from './category/gis';\n\n// GIS CATEGORY - BINARY\nexport type {\n  BinaryGeometryType,\n  BinaryGeometry,\n  BinaryPointGeometry,\n  BinaryLineGeometry,\n  BinaryPolygonGeometry,\n  BinaryAttribute\n} from './category/gis';\nexport type {\n  BinaryFeatures,\n  BinaryPointFeatures,\n  BinaryLineFeatures,\n  BinaryPolygonFeatures\n} from './category/gis';\n\n// SCHEMA\nexport {\n  Schema,\n  Field,\n  DataType,\n  Null,\n  Binary,\n  Bool,\n  Int,\n  Int8,\n  Int16,\n  Int32,\n  Int64,\n  Uint8,\n  Uint16,\n  Uint32,\n  Uint64,\n  Float,\n  Float16,\n  Float32,\n  Float64,\n  Utf8,\n  Date,\n  DateDay,\n  DateMillisecond,\n  Time,\n  TimeMillisecond,\n  TimeSecond,\n  Timestamp,\n  TimestampSecond,\n  TimestampMillisecond,\n  TimestampMicrosecond,\n  TimestampNanosecond,\n  Interval,\n  IntervalDayTime,\n  IntervalYearMonth,\n  FixedSizeList,\n  Struct\n} from './lib/schema/schema';\n\n// EXPERIMENTAL APIs\n\n// SCHEMA UTILS\nexport {deduceTypeFromColumn, deduceTypeFromValue} from './lib/schema-utils/deduce-column-type';\nexport {getTypeInfo} from './lib/arrow/get-type-info';\nexport {getArrowTypeFromTypedArray} from './lib/arrow/arrow-like-type-utils';\n\nexport {default as AsyncQueue} from './lib/utils/async-queue';\n"], "mappings": "AAyBA,SAAQA,OAAO,IAAIC,iBAAiB,QAAO,mCAAmC;AAE9E,SAAQD,OAAO,IAAIE,uBAAuB,QAAO,0CAA0C;AAC3F,SAAQF,OAAO,IAAIG,4BAA4B,QAAO,+CAA+C;AAErG,SAAQC,kBAAkB,EAAEC,iBAAiB,QAAO,uBAAuB;AAY3E,SAAQC,WAAW,EAAEC,kBAAkB,QAAO,4BAA4B;AAG1E,SACEC,gBAAgB,EAChBC,eAAe,EACfC,yBAAyB,QACpB,oCAAoC;AAwD3C,SACEC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,IAAI,EACJC,MAAM,EACNC,IAAI,EACJC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,KAAK,EACLC,OAAO,EACPC,OAAO,EACPC,OAAO,EACPC,IAAI,EACJC,IAAI,EACJC,OAAO,EACPC,eAAe,EACfC,IAAI,EACJC,eAAe,EACfC,UAAU,EACVC,SAAS,EACTC,eAAe,EACfC,oBAAoB,EACpBC,oBAAoB,EACpBC,mBAAmB,EACnBC,QAAQ,EACRC,eAAe,EACfC,iBAAiB,EACjBC,aAAa,EACbC,MAAM,QACD,qBAAqB;AAK5B,SAAQC,oBAAoB,EAAEC,mBAAmB,QAAO,uCAAuC;AAC/F,SAAQC,WAAW,QAAO,2BAA2B;AACrD,SAAQC,0BAA0B,QAAO,mCAAmC;AAE5E,SAAQlD,OAAO,IAAImD,UAAU,QAAO,yBAAyB"}