export function deduceTableSchema(table, schema) {
  const deducedSchema = Array.isArray(table) ? deduceSchemaForRowTable(table) : deduceSchemaForColumnarTable(table);
  return Object.assign(deducedSchema, schema);
}
function deduceSchemaForColumnarTable(columnarTable) {
  const schema = {};
  for (const field in columnarTable) {
    const column = columnarTable[field];
    if (ArrayBuffer.isView(column)) {
      schema[field] = column.constructor;
    } else if (column.length) {
      const value = column[0];
      schema[field] = deduceTypeFromValue(value);
    }
    schema[field] = schema[field] || null;
  }
  return schema;
}
function deduceSchemaForRowTable(rowTable) {
  const schema = {};
  if (rowTable.length) {
    const row = rowTable[0];
    for (const field in row) {
      const value = row[field];
      schema[field] = deduceTypeFromValue(value);
    }
  }
  return schema;
}
function deduceTypeFromValue(value) {
  if (value instanceof Date) {
    return Date;
  } else if (value instanceof Number) {
    return Float32Array;
  } else if (typeof value === 'string') {
    return String;
  }
  return null;
}
//# sourceMappingURL=deduce-table-schema.js.map