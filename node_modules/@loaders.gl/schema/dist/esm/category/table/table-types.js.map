{"version": 3, "file": "table-types.js", "names": [], "sources": ["../../../../src/category/table/table-types.ts"], "sourcesContent": ["import type {Schema} from '../../lib/schema/schema';\nimport type {Table as ApacheArrowTable, RecordBatch} from 'apache-arrow/Arrow.dom';\nimport type {AnyArray} from '../../types';\nimport type {Batch} from '../common';\nimport type {Feature} from '../gis';\n\n/** A general table */\nexport interface Table {\n  shape:\n    | 'row-table'\n    | 'array-row-table'\n    | 'object-row-table'\n    | 'geojson-row-table'\n    | 'columnar-table'\n    | 'arrow-table';\n  schema?: Schema;\n  schemaType?: 'explicit' | 'deduced';\n}\n\n/** A table organized as an array of rows */\nexport interface RowTable extends Table {\n  shape: 'row-table' | 'array-row-table' | 'object-row-table' | 'geojson-row-table';\n  data: any[];\n}\n\n/** A table organized as an array of rows, each row is an array of values */\nexport interface ArrayRowTable extends RowTable {\n  shape: 'array-row-table';\n  data: any[][];\n}\n\n/** A table organized as an array of rows, each row is an object mapping columns to values */\nexport interface ObjectRowTable extends RowTable {\n  shape: 'object-row-table';\n  data: {[columnName: string]: any}[];\n}\n\n/** A table organized as an array of rows, each row is a GeoJSON Feature */\nexport interface GeoJSONRowTable extends RowTable {\n  shape: 'geojson-row-table';\n  data: Feature[];\n}\n\n/** A table organized as a map of columns, each column is an array of value */\nexport interface ColumnarTable extends Table {\n  shape: 'columnar-table';\n  data: {[columnName: string]: AnyArray};\n}\n\n/** A table organized as an Apache Arrow table */\nexport interface ArrowTable extends Table {\n  shape: 'arrow-table';\n  data: ApacheArrowTable;\n}\n\n/** A collection of tables */\nexport type Tables<TableType extends Table = Table> = {\n  shape: 'tables';\n  tables: {name: string; table: TableType}[];\n};\n\n// Batches\n\n/** Batch for a general table */\nexport type TableBatch = Batch & {\n  data: any;\n  length: number;\n  schema?: Schema;\n  schemaType?: 'explicit' | 'deduced';\n};\n\n/** Batch for a table organized as an array of rows */\nexport type RowTableBatch = TableBatch & {\n  shape: 'row-table';\n  data: any[];\n};\n\n/** Batch for a table organized as an array of rows, each row is an array of values */\nexport type RowArrayTableBatch = RowTableBatch & {\n  shape: 'array-row-table';\n  data: any[][];\n};\n\n/** Batch for a table organized as an array of rows, each row is an object mapping columns to values */\nexport type RowObjectTableBatch = RowTableBatch & {\n  shape: 'object-row-table';\n  data: {[columnName: string]: any}[];\n};\n\n/** Batch for a table organized as an array of rows, each row is an array of values */\nexport type GeoJSONRowTableBatch = RowTableBatch & {\n  shape: 'geojson-row-table';\n  data: Feature[];\n};\n\n/** Batch for a table organized as a map of columns, each column is an array of value */\nexport type ColumnarTableBatch = TableBatch & {\n  shape: 'columnar-table';\n  data: {[columnName: string]: AnyArray};\n};\n\n/** Batch for a table organized as an Apache Arrow table */\nexport type ArrowTableBatch = TableBatch & {\n  shape: 'arrow-table';\n  data: RecordBatch;\n};\n"], "mappings": ""}