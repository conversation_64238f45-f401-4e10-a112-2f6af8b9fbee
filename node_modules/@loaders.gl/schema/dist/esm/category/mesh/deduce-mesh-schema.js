import { Schema, Field, FixedSizeList } from '../../lib/schema/schema';
import { getArrowTypeFromTypedArray } from '../../lib/arrow/arrow-like-type-utils';
export function deduceMeshSchema(attributes, metadata) {
  const fields = deduceMeshFields(attributes);
  return new Schema(fields, metadata);
}
export function deduceMeshField(attributeName, attribute, optionalMetadata) {
  const type = getArrowTypeFromTypedArray(attribute.value);
  const metadata = optionalMetadata ? optionalMetadata : makeMeshAttributeMetadata(attribute);
  const field = new Field(attributeName, new FixedSizeList(attribute.size, new Field('value', type)), false, metadata);
  return field;
}
function deduceMeshFields(attributes) {
  const fields = [];
  for (const attributeName in attributes) {
    const attribute = attributes[attributeName];
    fields.push(deduceMeshField(attributeName, attribute));
  }
  return fields;
}
export function makeMeshAttributeMetadata(attribute) {
  const result = new Map();
  if ('byteOffset' in attribute) {
    result.set('byteOffset', attribute.byteOffset.toString(10));
  }
  if ('byteStride' in attribute) {
    result.set('byteStride', attribute.byteStride.toString(10));
  }
  if ('normalized' in attribute) {
    result.set('normalized', attribute.normalized.toString());
  }
  return result;
}
//# sourceMappingURL=deduce-mesh-schema.js.map