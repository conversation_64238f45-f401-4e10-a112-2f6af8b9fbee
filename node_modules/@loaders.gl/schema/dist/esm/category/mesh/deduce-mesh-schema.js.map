{"version": 3, "file": "deduce-mesh-schema.js", "names": ["<PERSON><PERSON><PERSON>", "Field", "FixedSizeList", "getArrowTypeFromTypedArray", "deduceMeshSchema", "attributes", "metadata", "fields", "deduce<PERSON><PERSON><PERSON><PERSON>s", "deduce<PERSON><PERSON><PERSON><PERSON>", "attributeName", "attribute", "optionalMetadata", "type", "value", "makeMeshAttributeMetadata", "field", "size", "push", "result", "Map", "set", "byteOffset", "toString", "byteStride", "normalized"], "sources": ["../../../../src/category/mesh/deduce-mesh-schema.ts"], "sourcesContent": ["import {MeshAttribute, MeshAttributes} from './mesh-types';\nimport {Schem<PERSON>, Field, FixedSizeList} from '../../lib/schema/schema';\nimport {getArrowTypeFromTypedArray} from '../../lib/arrow/arrow-like-type-utils';\n\n/**\n * Create a schema for mesh attributes data\n * @param attributes\n * @param metadata\n * @returns\n */\nexport function deduceMeshSchema(\n  attributes: MeshAttributes,\n  metadata?: Map<string, string>\n): Schema {\n  const fields = deduceMeshFields(attributes);\n  return new Schema(fields, metadata);\n}\n\n/**\n * Create arrow-like schema field for mesh attribute\n * @param attributeName\n * @param attribute\n * @param optionalMetadata\n * @returns\n */\nexport function deduceMeshField(\n  attributeName: string,\n  attribute: MeshAttribute,\n  optionalMetadata?: Map<string, string>\n): Field {\n  const type = getArrowTypeFromTypedArray(attribute.value);\n  const metadata = optionalMetadata ? optionalMetadata : makeMeshAttributeMetadata(attribute);\n  const field = new Field(\n    attributeName,\n    new FixedSizeList(attribute.size, new Field('value', type)),\n    false,\n    metadata\n  );\n  return field;\n}\n\n/**\n * Create fields array for mesh attributes\n * @param attributes\n * @returns\n */\nfunction deduceMeshFields(attributes: MeshAttributes): Field[] {\n  const fields: Field[] = [];\n  for (const attributeName in attributes) {\n    const attribute: MeshAttribute = attributes[attributeName];\n    fields.push(deduceMeshField(attributeName, attribute));\n  }\n  return fields;\n}\n\n/**\n * Make metadata by mesh attribute properties\n * @param attribute\n * @returns\n */\nexport function makeMeshAttributeMetadata(attribute: MeshAttribute): Map<string, string> {\n  const result = new Map();\n  if ('byteOffset' in attribute) {\n    result.set('byteOffset', attribute.byteOffset!.toString(10));\n  }\n  if ('byteStride' in attribute) {\n    result.set('byteStride', attribute.byteStride!.toString(10));\n  }\n  if ('normalized' in attribute) {\n    result.set('normalized', attribute.normalized!.toString());\n  }\n  return result;\n}\n"], "mappings": "AACA,SAAQA,MAAM,EAAEC,KAAK,EAAEC,aAAa,QAAO,yBAAyB;AACpE,SAAQC,0BAA0B,QAAO,uCAAuC;AAQhF,OAAO,SAASC,gBAAgBA,CAC9BC,UAA0B,EAC1BC,QAA8B,EACtB;EACR,MAAMC,MAAM,GAAGC,gBAAgB,CAACH,UAAU,CAAC;EAC3C,OAAO,IAAIL,MAAM,CAACO,MAAM,EAAED,QAAQ,CAAC;AACrC;AASA,OAAO,SAASG,eAAeA,CAC7BC,aAAqB,EACrBC,SAAwB,EACxBC,gBAAsC,EAC/B;EACP,MAAMC,IAAI,GAAGV,0BAA0B,CAACQ,SAAS,CAACG,KAAK,CAAC;EACxD,MAAMR,QAAQ,GAAGM,gBAAgB,GAAGA,gBAAgB,GAAGG,yBAAyB,CAACJ,SAAS,CAAC;EAC3F,MAAMK,KAAK,GAAG,IAAIf,KAAK,CACrBS,aAAa,EACb,IAAIR,aAAa,CAACS,SAAS,CAACM,IAAI,EAAE,IAAIhB,KAAK,CAAC,OAAO,EAAEY,IAAI,CAAC,CAAC,EAC3D,KAAK,EACLP,QACF,CAAC;EACD,OAAOU,KAAK;AACd;AAOA,SAASR,gBAAgBA,CAACH,UAA0B,EAAW;EAC7D,MAAME,MAAe,GAAG,EAAE;EAC1B,KAAK,MAAMG,aAAa,IAAIL,UAAU,EAAE;IACtC,MAAMM,SAAwB,GAAGN,UAAU,CAACK,aAAa,CAAC;IAC1DH,MAAM,CAACW,IAAI,CAACT,eAAe,CAACC,aAAa,EAAEC,SAAS,CAAC,CAAC;EACxD;EACA,OAAOJ,MAAM;AACf;AAOA,OAAO,SAASQ,yBAAyBA,CAACJ,SAAwB,EAAuB;EACvF,MAAMQ,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;EACxB,IAAI,YAAY,IAAIT,SAAS,EAAE;IAC7BQ,MAAM,CAACE,GAAG,CAAC,YAAY,EAAEV,SAAS,CAACW,UAAU,CAAEC,QAAQ,CAAC,EAAE,CAAC,CAAC;EAC9D;EACA,IAAI,YAAY,IAAIZ,SAAS,EAAE;IAC7BQ,MAAM,CAACE,GAAG,CAAC,YAAY,EAAEV,SAAS,CAACa,UAAU,CAAED,QAAQ,CAAC,EAAE,CAAC,CAAC;EAC9D;EACA,IAAI,YAAY,IAAIZ,SAAS,EAAE;IAC7BQ,MAAM,CAACE,GAAG,CAAC,YAAY,EAAEV,SAAS,CAACc,UAAU,CAAEF,QAAQ,CAAC,CAAC,CAAC;EAC5D;EACA,OAAOJ,MAAM;AACf"}