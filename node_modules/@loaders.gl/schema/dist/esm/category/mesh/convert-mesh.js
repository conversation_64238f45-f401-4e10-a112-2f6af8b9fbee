import { convertMeshToArrowTable } from './mesh-to-arrow-table';
export function convertMesh(mesh, shape, options) {
  switch (shape || 'mesh') {
    case 'mesh':
      return mesh;
    case 'columnar-table':
      return convertMeshToColumnarTable(mesh);
    case 'arrow-table':
      return {
        shape: 'arrow-table',
        data: convertMeshToArrowTable(mesh)
      };
    default:
      throw new Error("Unsupported shape ".concat(options === null || options === void 0 ? void 0 : options.shape));
  }
}
export function convertMeshToColumnarTable(mesh) {
  const columns = {};
  for (const [columnName, attribute] of Object.entries(mesh.attributes)) {
    columns[columnName] = attribute.value;
  }
  return {
    shape: 'columnar-table',
    schema: mesh.schema,
    data: columns
  };
}
//# sourceMappingURL=convert-mesh.js.map