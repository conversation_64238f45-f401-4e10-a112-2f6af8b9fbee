{"version": 3, "file": "convert-mesh.js", "names": ["convertMeshToArrowTable", "<PERSON><PERSON><PERSON>", "mesh", "shape", "options", "convertMeshToColumnarTable", "data", "Error", "concat", "columns", "columnName", "attribute", "Object", "entries", "attributes", "value", "schema"], "sources": ["../../../../src/category/mesh/convert-mesh.ts"], "sourcesContent": ["import type {Mesh} from './mesh-types';\nimport type {ColumnarTable, ArrowTable} from '../table/table-types';\nimport {convertMeshToArrowTable} from './mesh-to-arrow-table';\n\ntype TargetShape = 'mesh' | 'columnar-table' | 'arrow-table';\n\n/**\n * Convert a mesh to a specific shape\n */\nexport function convertMesh(\n  mesh: Mesh,\n  shape: TargetShape,\n  options?: any\n): Mesh | ColumnarTable | ArrowTable {\n  switch (shape || 'mesh') {\n    case 'mesh':\n      return mesh;\n    case 'columnar-table':\n      return convertMeshToColumnarTable(mesh);\n    case 'arrow-table':\n      return {\n        shape: 'arrow-table',\n        data: convertMeshToArrowTable(mesh)\n      };\n    default:\n      throw new Error(`Unsupported shape ${options?.shape}`);\n  }\n}\n\n/**\n * Convert a loaders.gl Mesh to a Columnar Table\n * @param mesh\n * @returns\n */\nexport function convertMeshToColumnarTable(mesh: Mesh): ColumnarTable {\n  const columns = {};\n\n  for (const [columnName, attribute] of Object.entries(mesh.attributes)) {\n    columns[columnName] = attribute.value;\n  }\n\n  return {\n    shape: 'columnar-table',\n    schema: mesh.schema,\n    data: columns\n  };\n}\n"], "mappings": "AAEA,SAAQA,uBAAuB,QAAO,uBAAuB;AAO7D,OAAO,SAASC,WAAWA,CACzBC,IAAU,EACVC,KAAkB,EAClBC,OAAa,EACsB;EACnC,QAAQD,KAAK,IAAI,MAAM;IACrB,KAAK,MAAM;MACT,OAAOD,IAAI;IACb,KAAK,gBAAgB;MACnB,OAAOG,0BAA0B,CAACH,IAAI,CAAC;IACzC,KAAK,aAAa;MAChB,OAAO;QACLC,KAAK,EAAE,aAAa;QACpBG,IAAI,EAAEN,uBAAuB,CAACE,IAAI;MACpC,CAAC;IACH;MACE,MAAM,IAAIK,KAAK,sBAAAC,MAAA,CAAsBJ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAED,KAAK,CAAE,CAAC;EAC1D;AACF;AAOA,OAAO,SAASE,0BAA0BA,CAACH,IAAU,EAAiB;EACpE,MAAMO,OAAO,GAAG,CAAC,CAAC;EAElB,KAAK,MAAM,CAACC,UAAU,EAAEC,SAAS,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACX,IAAI,CAACY,UAAU,CAAC,EAAE;IACrEL,OAAO,CAACC,UAAU,CAAC,GAAGC,SAAS,CAACI,KAAK;EACvC;EAEA,OAAO;IACLZ,KAAK,EAAE,gBAAgB;IACvBa,MAAM,EAAEd,IAAI,CAACc,MAAM;IACnBV,IAAI,EAAEG;EACR,CAAC;AACH"}