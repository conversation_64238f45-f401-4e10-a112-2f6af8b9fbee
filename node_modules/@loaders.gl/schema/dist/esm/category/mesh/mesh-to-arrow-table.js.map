{"version": 3, "file": "mesh-to-arrow-table.js", "names": ["Table", "<PERSON><PERSON><PERSON>", "RecordBatch", "FixedSizeList", "Field", "Data", "FixedSizeListVector", "getArrowType", "getArrowVector", "makeMeshAttributeMetadata", "convertMeshToArrowTable", "mesh", "batchSize", "_mesh$schema", "vectors", "fields", "<PERSON><PERSON><PERSON>", "attributes", "attribute", "value", "size", "type", "vector", "listType", "field", "data", "length", "undefined", "listVector", "push", "schema", "metadata", "Map", "recordBatch", "table"], "sources": ["../../../../src/category/mesh/mesh-to-arrow-table.ts"], "sourcesContent": ["import {\n  Table,\n  Schema,\n  RecordBatch,\n  FixedSizeList,\n  Field,\n  Data,\n  FixedSizeListVector\n} from 'apache-arrow/Arrow.dom';\nimport {AbstractVector} from 'apache-arrow/vector';\nimport {getArrowType, getArrowVector} from '../../lib/arrow/arrow-type-utils';\nimport type {Mesh} from './mesh-types';\nimport {makeMeshAttributeMetadata} from './deduce-mesh-schema';\n\n/**\n * * Convert a loaders.gl Mesh to an Apache Arrow Table\n * @param mesh\n * @param metadata\n * @param batchSize\n * @returns\n */\nexport function convertMeshToArrowTable(mesh: Mesh, batchSize?: number): Table {\n  const vectors: AbstractVector[] = [];\n  const fields: Field[] = [];\n  for (const attributeKey in mesh.attributes) {\n    const attribute = mesh.attributes[attributeKey];\n    const {value, size = 1} = attribute;\n    const type = getArrowType(value);\n    const vector = getArrowVector(value);\n    const listType = new FixedSizeList(size, new Field('value', type));\n    const field = new Field(attributeKey, listType, false, makeMeshAttributeMetadata(attribute));\n    const data = new Data(listType, 0, value.length / size, 0, undefined, [vector]);\n    const listVector = new FixedSizeListVector(data);\n    vectors.push(listVector);\n    fields.push(field);\n  }\n  const schema = new Schema(fields, mesh?.schema?.metadata || new Map<string, string>());\n  const recordBatch = new RecordBatch(schema, vectors[0].length, vectors);\n  const table = new Table(schema, recordBatch);\n  return table;\n}\n"], "mappings": "AAAA,SACEA,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,KAAK,EACLC,IAAI,EACJC,mBAAmB,QACd,wBAAwB;AAE/B,SAAQC,YAAY,EAAEC,cAAc,QAAO,kCAAkC;AAE7E,SAAQC,yBAAyB,QAAO,sBAAsB;AAS9D,OAAO,SAASC,uBAAuBA,CAACC,IAAU,EAAEC,SAAkB,EAAS;EAAA,IAAAC,YAAA;EAC7E,MAAMC,OAAyB,GAAG,EAAE;EACpC,MAAMC,MAAe,GAAG,EAAE;EAC1B,KAAK,MAAMC,YAAY,IAAIL,IAAI,CAACM,UAAU,EAAE;IAC1C,MAAMC,SAAS,GAAGP,IAAI,CAACM,UAAU,CAACD,YAAY,CAAC;IAC/C,MAAM;MAACG,KAAK;MAAEC,IAAI,GAAG;IAAC,CAAC,GAAGF,SAAS;IACnC,MAAMG,IAAI,GAAGd,YAAY,CAACY,KAAK,CAAC;IAChC,MAAMG,MAAM,GAAGd,cAAc,CAACW,KAAK,CAAC;IACpC,MAAMI,QAAQ,GAAG,IAAIpB,aAAa,CAACiB,IAAI,EAAE,IAAIhB,KAAK,CAAC,OAAO,EAAEiB,IAAI,CAAC,CAAC;IAClE,MAAMG,KAAK,GAAG,IAAIpB,KAAK,CAACY,YAAY,EAAEO,QAAQ,EAAE,KAAK,EAAEd,yBAAyB,CAACS,SAAS,CAAC,CAAC;IAC5F,MAAMO,IAAI,GAAG,IAAIpB,IAAI,CAACkB,QAAQ,EAAE,CAAC,EAAEJ,KAAK,CAACO,MAAM,GAAGN,IAAI,EAAE,CAAC,EAAEO,SAAS,EAAE,CAACL,MAAM,CAAC,CAAC;IAC/E,MAAMM,UAAU,GAAG,IAAItB,mBAAmB,CAACmB,IAAI,CAAC;IAChDX,OAAO,CAACe,IAAI,CAACD,UAAU,CAAC;IACxBb,MAAM,CAACc,IAAI,CAACL,KAAK,CAAC;EACpB;EACA,MAAMM,MAAM,GAAG,IAAI7B,MAAM,CAACc,MAAM,EAAE,CAAAJ,IAAI,aAAJA,IAAI,wBAAAE,YAAA,GAAJF,IAAI,CAAEmB,MAAM,cAAAjB,YAAA,uBAAZA,YAAA,CAAckB,QAAQ,KAAI,IAAIC,GAAG,CAAiB,CAAC,CAAC;EACtF,MAAMC,WAAW,GAAG,IAAI/B,WAAW,CAAC4B,MAAM,EAAEhB,OAAO,CAAC,CAAC,CAAC,CAACY,MAAM,EAAEZ,OAAO,CAAC;EACvE,MAAMoB,KAAK,GAAG,IAAIlC,KAAK,CAAC8B,MAAM,EAAEG,WAAW,CAAC;EAC5C,OAAOC,KAAK;AACd"}