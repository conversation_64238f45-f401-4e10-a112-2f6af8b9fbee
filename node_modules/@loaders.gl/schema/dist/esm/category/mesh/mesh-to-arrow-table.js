import { Table, Schema, RecordBatch, FixedSizeList, Field, Data, FixedSizeListVector } from 'apache-arrow/Arrow.dom';
import { getArrowType, getArrowVector } from '../../lib/arrow/arrow-type-utils';
import { makeMeshAttributeMetadata } from './deduce-mesh-schema';
export function convertMeshToArrowTable(mesh, batchSize) {
  var _mesh$schema;
  const vectors = [];
  const fields = [];
  for (const attributeKey in mesh.attributes) {
    const attribute = mesh.attributes[attributeKey];
    const {
      value,
      size = 1
    } = attribute;
    const type = getArrowType(value);
    const vector = getArrowVector(value);
    const listType = new FixedSizeList(size, new Field('value', type));
    const field = new Field(attributeKey, listType, false, makeMeshAttributeMetadata(attribute));
    const data = new Data(listType, 0, value.length / size, 0, undefined, [vector]);
    const listVector = new FixedSizeListVector(data);
    vectors.push(listVector);
    fields.push(field);
  }
  const schema = new Schema(fields, (mesh === null || mesh === void 0 ? void 0 : (_mesh$schema = mesh.schema) === null || _mesh$schema === void 0 ? void 0 : _mesh$schema.metadata) || new Map());
  const recordBatch = new RecordBatch(schema, vectors[0].length, vectors);
  const table = new Table(schema, recordBatch);
  return table;
}
//# sourceMappingURL=mesh-to-arrow-table.js.map