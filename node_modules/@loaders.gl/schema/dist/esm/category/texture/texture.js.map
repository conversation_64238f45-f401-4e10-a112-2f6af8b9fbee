{"version": 3, "file": "texture.js", "names": [], "sources": ["../../../../src/category/texture/texture.ts"], "sourcesContent": ["import type {ImageType} from '../image/image';\n\n/**\n * These represent the main compressed texture formats\n * Each format typically has a number of more specific subformats\n */\nexport type GPUTextureFormat =\n  | 'dxt'\n  | 'dxt-srgb'\n  | 'etc1'\n  | 'etc2'\n  | 'pvrtc'\n  | 'atc'\n  | 'astc'\n  | 'rgtc';\n\n/** One mip level */\nexport type TextureLevel = {\n  compressed: boolean;\n  format?: number;\n  data: Uint8Array;\n  width: number;\n  height: number;\n  levelSize?: number;\n  hasAlpha?: boolean;\n};\n\nexport type TextureOrImage = ImageType | (TextureLevel | ImageType);\n"], "mappings": ""}