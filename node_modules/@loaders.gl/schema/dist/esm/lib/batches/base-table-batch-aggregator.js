import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
const DEFAULT_ROW_COUNT = 100;
export default class RowTableBatchAggregator {
  constructor(schema, options) {
    _defineProperty(this, "schema", void 0);
    _defineProperty(this, "options", void 0);
    _defineProperty(this, "length", 0);
    _defineProperty(this, "rows", null);
    _defineProperty(this, "cursor", 0);
    _defineProperty(this, "_headers", []);
    this.options = options;
    this.schema = schema;
    if (!Array.isArray(schema)) {
      this._headers = [];
      for (const key in schema) {
        this._headers[schema[key].index] = schema[key].name;
      }
    }
  }
  rowCount() {
    return this.length;
  }
  addArrayRow(row, cursor) {
    if (Number.isFinite(cursor)) {
      this.cursor = cursor;
    }
    this.rows = this.rows || new Array(DEFAULT_ROW_COUNT);
    this.rows[this.length] = row;
    this.length++;
  }
  addObjectRow(row, cursor) {
    if (Number.isFinite(cursor)) {
      this.cursor = cursor;
    }
    this.rows = this.rows || new Array(DEFAULT_ROW_COUNT);
    this.rows[this.length] = row;
    this.length++;
  }
  getBatch() {
    let rows = this.rows;
    if (!rows) {
      return null;
    }
    rows = rows.slice(0, this.length);
    this.rows = null;
    const batch = {
      shape: this.options.shape,
      batchType: 'data',
      data: rows,
      length: this.length,
      schema: this.schema,
      cursor: this.cursor
    };
    return batch;
  }
}
//# sourceMappingURL=base-table-batch-aggregator.js.map