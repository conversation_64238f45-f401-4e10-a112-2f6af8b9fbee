import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
import { convertToArrayRow, convertToObjectRow } from '../utils/row-utils';
const DEFAULT_ROW_COUNT = 100;
export default class RowTableBatchAggregator {
  constructor(schema, options) {
    _defineProperty(this, "schema", void 0);
    _defineProperty(this, "options", void 0);
    _defineProperty(this, "length", 0);
    _defineProperty(this, "objectRows", null);
    _defineProperty(this, "arrayRows", null);
    _defineProperty(this, "cursor", 0);
    _defineProperty(this, "_headers", []);
    this.options = options;
    this.schema = schema;
    if (!Array.isArray(schema)) {
      this._headers = [];
      for (const key in schema) {
        this._headers[schema[key].index] = schema[key].name;
      }
    }
  }
  rowCount() {
    return this.length;
  }
  addArrayRow(row, cursor) {
    if (Number.isFinite(cursor)) {
      this.cursor = cursor;
    }
    switch (this.options.shape) {
      case 'object-row-table':
        const rowObject = convertToObjectRow(row, this._headers);
        this.addObjectRow(rowObject, cursor);
        break;
      case 'array-row-table':
        this.arrayRows = this.arrayRows || new Array(DEFAULT_ROW_COUNT);
        this.arrayRows[this.length] = row;
        this.length++;
        break;
    }
  }
  addObjectRow(row, cursor) {
    if (Number.isFinite(cursor)) {
      this.cursor = cursor;
    }
    switch (this.options.shape) {
      case 'array-row-table':
        const rowArray = convertToArrayRow(row, this._headers);
        this.addArrayRow(rowArray, cursor);
        break;
      case 'object-row-table':
        this.objectRows = this.objectRows || new Array(DEFAULT_ROW_COUNT);
        this.objectRows[this.length] = row;
        this.length++;
        break;
    }
  }
  getBatch() {
    let rows = this.arrayRows || this.objectRows;
    if (!rows) {
      return null;
    }
    rows = rows.slice(0, this.length);
    this.arrayRows = null;
    this.objectRows = null;
    return {
      shape: this.options.shape,
      batchType: 'data',
      data: rows,
      length: this.length,
      schema: this.schema,
      cursor: this.cursor
    };
  }
}
//# sourceMappingURL=row-table-batch-aggregator.js.map