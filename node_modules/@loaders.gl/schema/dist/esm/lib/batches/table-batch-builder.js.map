{"version": 3, "file": "table-batch-builder.js", "names": ["BaseTableBatchAggregator", "RowTableBatchAggregator", "ColumnarTableBatchAggregator", "DEFAULT_OPTIONS", "shape", "batchSize", "batchDebounceMs", "limit", "_limitMB", "ERR_MESSAGE", "TableBatchBuilder", "constructor", "schema", "options", "_defineProperty", "Date", "now", "limitReached", "_this$options", "_this$options2", "Boolean", "totalLength", "totalBytes", "addRow", "row", "rowBytes", "_estimateRowMB", "Array", "isArray", "addArrayRow", "addObjectRow", "aggregator", "TableBatchType", "_getTableBatchType", "chunkComplete", "chunk", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bytesUsed", "byteLength", "length", "isChunkComplete", "getFullBatch", "_isFull", "_getBatch", "getFinalBatch", "Object", "keys", "rowCount", "lastBatchEmittedMs", "normalizedBatch", "getBatch", "count", "batchCount", "assign", "ArrowBatch", "Error"], "sources": ["../../../../src/lib/batches/table-batch-builder.ts"], "sourcesContent": ["import type {Schema} from '../schema/schema';\nimport type {TableBatch} from '../../category/table/table-types';\nimport type {TableBatchAggregator, TableBatchConstructor} from './table-batch-aggregator';\nimport BaseTableBatchAggregator from './base-table-batch-aggregator';\nimport RowTableBatchAggregator from './row-table-batch-aggregator';\nimport ColumnarTableBatchAggregator from './columnar-table-batch-aggregator';\n\n// TODO define interface instead\ntype TableBatchBuilderOptions = {\n  shape: 'row-table' | 'array-row-table' | 'object-row-table' | 'columnar-table' | 'arrow-table';\n  batchSize?: number | 'auto';\n  batchDebounceMs?: number;\n  limit: number;\n  _limitMB: number;\n};\n\ntype GetBatchOptions = {\n  bytesUsed?: number;\n  [key: string]: any;\n};\n\nconst DEFAULT_OPTIONS: Required<TableBatchBuilderOptions> = {\n  shape: 'array-row-table',\n  batchSize: 'auto',\n  batchDebounceMs: 0,\n  limit: 0,\n  _limitMB: 0\n};\n\nconst ERR_MESSAGE = 'TableBatchBuilder';\n\n/** Incrementally builds batches from a stream of rows */\nexport default class TableBatchBuilder {\n  schema: Schema;\n  options: Required<TableBatchBuilderOptions>;\n\n  private aggregator: TableBatchAggregator | null = null;\n  private batchCount: number = 0;\n  private bytesUsed: number = 0;\n  private isChunkComplete: boolean = false;\n  private lastBatchEmittedMs: number = Date.now();\n  private totalLength: number = 0;\n  private totalBytes: number = 0;\n  private rowBytes: number = 0;\n\n  static ArrowBatch?: TableBatchConstructor;\n\n  constructor(schema: Schema, options?: TableBatchBuilderOptions) {\n    this.schema = schema;\n    this.options = {...DEFAULT_OPTIONS, ...options};\n  }\n\n  limitReached(): boolean {\n    if (Boolean(this.options?.limit) && this.totalLength >= this.options.limit) {\n      return true;\n    }\n    if (Boolean(this.options?._limitMB) && this.totalBytes / 1e6 >= this.options._limitMB) {\n      return true;\n    }\n    return false;\n  }\n\n  /** @deprecated Use addArrayRow or addObjectRow */\n  addRow(row: any[] | {[columnName: string]: any}): void {\n    if (this.limitReached()) {\n      return;\n    }\n    this.totalLength++;\n    this.rowBytes = this.rowBytes || this._estimateRowMB(row);\n    this.totalBytes += this.rowBytes;\n    if (Array.isArray(row)) {\n      this.addArrayRow(row);\n    } else {\n      this.addObjectRow(row);\n    }\n  }\n\n  /** Add one row to the batch */\n  protected addArrayRow(row: any[]) {\n    if (!this.aggregator) {\n      const TableBatchType = this._getTableBatchType();\n      this.aggregator = new TableBatchType(this.schema, this.options);\n    }\n    this.aggregator.addArrayRow(row);\n  }\n\n  /** Add one row to the batch */\n  protected addObjectRow(row: {[columnName: string]: any}): void {\n    if (!this.aggregator) {\n      const TableBatchType = this._getTableBatchType();\n      this.aggregator = new TableBatchType(this.schema, this.options);\n    }\n    this.aggregator.addObjectRow(row);\n  }\n\n  /** Mark an incoming raw memory chunk has completed */\n  chunkComplete(chunk: ArrayBuffer | string): void {\n    if (chunk instanceof ArrayBuffer) {\n      this.bytesUsed += chunk.byteLength;\n    }\n    if (typeof chunk === 'string') {\n      this.bytesUsed += chunk.length;\n    }\n    this.isChunkComplete = true;\n  }\n\n  getFullBatch(options?: GetBatchOptions): TableBatch | null {\n    return this._isFull() ? this._getBatch(options) : null;\n  }\n\n  getFinalBatch(options?: GetBatchOptions): TableBatch | null {\n    return this._getBatch(options);\n  }\n\n  // INTERNAL\n\n  _estimateRowMB(row: any[] | object): number {\n    return Array.isArray(row) ? row.length * 8 : Object.keys(row).length * 8;\n  }\n\n  private _isFull(): boolean {\n    // No batch, not ready\n    if (!this.aggregator || this.aggregator.rowCount() === 0) {\n      return false;\n    }\n\n    // if batchSize === 'auto' we wait for chunk to complete\n    // if batchSize === number, ensure we have enough rows\n    if (this.options.batchSize === 'auto') {\n      if (!this.isChunkComplete) {\n        return false;\n      }\n    } else if (this.options.batchSize > this.aggregator.rowCount()) {\n      return false;\n    }\n\n    // Debounce batches\n    if (this.options.batchDebounceMs > Date.now() - this.lastBatchEmittedMs) {\n      return false;\n    }\n\n    // Emit batch\n    this.isChunkComplete = false;\n    this.lastBatchEmittedMs = Date.now();\n    return true;\n  }\n\n  /**\n   * bytesUsed can be set via chunkComplete or via getBatch*\n   */\n  private _getBatch(options?: GetBatchOptions): TableBatch | null {\n    if (!this.aggregator) {\n      return null;\n    }\n\n    // TODO - this can overly increment bytes used?\n    if (options?.bytesUsed) {\n      this.bytesUsed = options.bytesUsed;\n    }\n    const normalizedBatch = this.aggregator.getBatch() as TableBatch;\n    normalizedBatch.count = this.batchCount;\n    normalizedBatch.bytesUsed = this.bytesUsed;\n    Object.assign(normalizedBatch, options);\n\n    this.batchCount++;\n    this.aggregator = null;\n    return normalizedBatch;\n  }\n\n  private _getTableBatchType(): TableBatchConstructor {\n    switch (this.options.shape) {\n      case 'row-table':\n        return BaseTableBatchAggregator;\n      case 'array-row-table':\n      case 'object-row-table':\n        return RowTableBatchAggregator;\n      case 'columnar-table':\n        return ColumnarTableBatchAggregator;\n      case 'arrow-table':\n        if (!TableBatchBuilder.ArrowBatch) {\n          throw new Error(ERR_MESSAGE);\n        }\n        return TableBatchBuilder.ArrowBatch;\n      default:\n        throw new Error(ERR_MESSAGE);\n    }\n  }\n}\n"], "mappings": ";AAGA,OAAOA,wBAAwB,MAAM,+BAA+B;AACpE,OAAOC,uBAAuB,MAAM,8BAA8B;AAClE,OAAOC,4BAA4B,MAAM,mCAAmC;AAgB5E,MAAMC,eAAmD,GAAG;EAC1DC,KAAK,EAAE,iBAAiB;EACxBC,SAAS,EAAE,MAAM;EACjBC,eAAe,EAAE,CAAC;EAClBC,KAAK,EAAE,CAAC;EACRC,QAAQ,EAAE;AACZ,CAAC;AAED,MAAMC,WAAW,GAAG,mBAAmB;AAGvC,eAAe,MAAMC,iBAAiB,CAAC;EAerCC,WAAWA,CAACC,MAAc,EAAEC,OAAkC,EAAE;IAAAC,eAAA;IAAAA,eAAA;IAAAA,eAAA,qBAXd,IAAI;IAAAA,eAAA,qBACzB,CAAC;IAAAA,eAAA,oBACF,CAAC;IAAAA,eAAA,0BACM,KAAK;IAAAA,eAAA,6BACHC,IAAI,CAACC,GAAG,CAAC,CAAC;IAAAF,eAAA,sBACjB,CAAC;IAAAA,eAAA,qBACF,CAAC;IAAAA,eAAA,mBACH,CAAC;IAK1B,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,OAAO,GAAG;MAAC,GAAGV,eAAe;MAAE,GAAGU;IAAO,CAAC;EACjD;EAEAI,YAAYA,CAAA,EAAY;IAAA,IAAAC,aAAA,EAAAC,cAAA;IACtB,IAAIC,OAAO,EAAAF,aAAA,GAAC,IAAI,CAACL,OAAO,cAAAK,aAAA,uBAAZA,aAAA,CAAcX,KAAK,CAAC,IAAI,IAAI,CAACc,WAAW,IAAI,IAAI,CAACR,OAAO,CAACN,KAAK,EAAE;MAC1E,OAAO,IAAI;IACb;IACA,IAAIa,OAAO,EAAAD,cAAA,GAAC,IAAI,CAACN,OAAO,cAAAM,cAAA,uBAAZA,cAAA,CAAcX,QAAQ,CAAC,IAAI,IAAI,CAACc,UAAU,GAAG,GAAG,IAAI,IAAI,CAACT,OAAO,CAACL,QAAQ,EAAE;MACrF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAGAe,MAAMA,CAACC,GAAwC,EAAQ;IACrD,IAAI,IAAI,CAACP,YAAY,CAAC,CAAC,EAAE;MACvB;IACF;IACA,IAAI,CAACI,WAAW,EAAE;IAClB,IAAI,CAACI,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAI,IAAI,CAACC,cAAc,CAACF,GAAG,CAAC;IACzD,IAAI,CAACF,UAAU,IAAI,IAAI,CAACG,QAAQ;IAChC,IAAIE,KAAK,CAACC,OAAO,CAACJ,GAAG,CAAC,EAAE;MACtB,IAAI,CAACK,WAAW,CAACL,GAAG,CAAC;IACvB,CAAC,MAAM;MACL,IAAI,CAACM,YAAY,CAACN,GAAG,CAAC;IACxB;EACF;EAGUK,WAAWA,CAACL,GAAU,EAAE;IAChC,IAAI,CAAC,IAAI,CAACO,UAAU,EAAE;MACpB,MAAMC,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;MAChD,IAAI,CAACF,UAAU,GAAG,IAAIC,cAAc,CAAC,IAAI,CAACpB,MAAM,EAAE,IAAI,CAACC,OAAO,CAAC;IACjE;IACA,IAAI,CAACkB,UAAU,CAACF,WAAW,CAACL,GAAG,CAAC;EAClC;EAGUM,YAAYA,CAACN,GAAgC,EAAQ;IAC7D,IAAI,CAAC,IAAI,CAACO,UAAU,EAAE;MACpB,MAAMC,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;MAChD,IAAI,CAACF,UAAU,GAAG,IAAIC,cAAc,CAAC,IAAI,CAACpB,MAAM,EAAE,IAAI,CAACC,OAAO,CAAC;IACjE;IACA,IAAI,CAACkB,UAAU,CAACD,YAAY,CAACN,GAAG,CAAC;EACnC;EAGAU,aAAaA,CAACC,KAA2B,EAAQ;IAC/C,IAAIA,KAAK,YAAYC,WAAW,EAAE;MAChC,IAAI,CAACC,SAAS,IAAIF,KAAK,CAACG,UAAU;IACpC;IACA,IAAI,OAAOH,KAAK,KAAK,QAAQ,EAAE;MAC7B,IAAI,CAACE,SAAS,IAAIF,KAAK,CAACI,MAAM;IAChC;IACA,IAAI,CAACC,eAAe,GAAG,IAAI;EAC7B;EAEAC,YAAYA,CAAC5B,OAAyB,EAAqB;IACzD,OAAO,IAAI,CAAC6B,OAAO,CAAC,CAAC,GAAG,IAAI,CAACC,SAAS,CAAC9B,OAAO,CAAC,GAAG,IAAI;EACxD;EAEA+B,aAAaA,CAAC/B,OAAyB,EAAqB;IAC1D,OAAO,IAAI,CAAC8B,SAAS,CAAC9B,OAAO,CAAC;EAChC;EAIAa,cAAcA,CAACF,GAAmB,EAAU;IAC1C,OAAOG,KAAK,CAACC,OAAO,CAACJ,GAAG,CAAC,GAAGA,GAAG,CAACe,MAAM,GAAG,CAAC,GAAGM,MAAM,CAACC,IAAI,CAACtB,GAAG,CAAC,CAACe,MAAM,GAAG,CAAC;EAC1E;EAEQG,OAAOA,CAAA,EAAY;IAEzB,IAAI,CAAC,IAAI,CAACX,UAAU,IAAI,IAAI,CAACA,UAAU,CAACgB,QAAQ,CAAC,CAAC,KAAK,CAAC,EAAE;MACxD,OAAO,KAAK;IACd;IAIA,IAAI,IAAI,CAAClC,OAAO,CAACR,SAAS,KAAK,MAAM,EAAE;MACrC,IAAI,CAAC,IAAI,CAACmC,eAAe,EAAE;QACzB,OAAO,KAAK;MACd;IACF,CAAC,MAAM,IAAI,IAAI,CAAC3B,OAAO,CAACR,SAAS,GAAG,IAAI,CAAC0B,UAAU,CAACgB,QAAQ,CAAC,CAAC,EAAE;MAC9D,OAAO,KAAK;IACd;IAGA,IAAI,IAAI,CAAClC,OAAO,CAACP,eAAe,GAAGS,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACgC,kBAAkB,EAAE;MACvE,OAAO,KAAK;IACd;IAGA,IAAI,CAACR,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACQ,kBAAkB,GAAGjC,IAAI,CAACC,GAAG,CAAC,CAAC;IACpC,OAAO,IAAI;EACb;EAKQ2B,SAASA,CAAC9B,OAAyB,EAAqB;IAC9D,IAAI,CAAC,IAAI,CAACkB,UAAU,EAAE;MACpB,OAAO,IAAI;IACb;IAGA,IAAIlB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEwB,SAAS,EAAE;MACtB,IAAI,CAACA,SAAS,GAAGxB,OAAO,CAACwB,SAAS;IACpC;IACA,MAAMY,eAAe,GAAG,IAAI,CAAClB,UAAU,CAACmB,QAAQ,CAAC,CAAe;IAChED,eAAe,CAACE,KAAK,GAAG,IAAI,CAACC,UAAU;IACvCH,eAAe,CAACZ,SAAS,GAAG,IAAI,CAACA,SAAS;IAC1CQ,MAAM,CAACQ,MAAM,CAACJ,eAAe,EAAEpC,OAAO,CAAC;IAEvC,IAAI,CAACuC,UAAU,EAAE;IACjB,IAAI,CAACrB,UAAU,GAAG,IAAI;IACtB,OAAOkB,eAAe;EACxB;EAEQhB,kBAAkBA,CAAA,EAA0B;IAClD,QAAQ,IAAI,CAACpB,OAAO,CAACT,KAAK;MACxB,KAAK,WAAW;QACd,OAAOJ,wBAAwB;MACjC,KAAK,iBAAiB;MACtB,KAAK,kBAAkB;QACrB,OAAOC,uBAAuB;MAChC,KAAK,gBAAgB;QACnB,OAAOC,4BAA4B;MACrC,KAAK,aAAa;QAChB,IAAI,CAACQ,iBAAiB,CAAC4C,UAAU,EAAE;UACjC,MAAM,IAAIC,KAAK,CAAC9C,WAAW,CAAC;QAC9B;QACA,OAAOC,iBAAiB,CAAC4C,UAAU;MACrC;QACE,MAAM,IAAIC,KAAK,CAAC9C,WAAW,CAAC;IAChC;EACF;AACF;AAACK,eAAA,CA3JoBJ,iBAAiB"}