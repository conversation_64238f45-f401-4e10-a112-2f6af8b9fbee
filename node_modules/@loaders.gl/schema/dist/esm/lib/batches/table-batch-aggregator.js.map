{"version": 3, "file": "table-batch-aggregator.js", "names": [], "sources": ["../../../../src/lib/batches/table-batch-aggregator.ts"], "sourcesContent": ["import type {Schema} from '../schema/schema';\nimport type {TableBatch} from '../../category/table/table-types';\n\nexport interface TableBatchOptions {\n  batchSize: number | string;\n  [key: string]: any;\n}\n\nexport interface TableBatchConstructor {\n  new (schema: Schema, options: TableBatchOptions): TableBatchAggregator;\n}\n\n/**\n * TableBatchBuilder delegates batch building to this interface\n */\nexport interface TableBatchAggregator {\n  /** Number of rows */\n  rowCount(): number;\n  /** Add one row */\n  addArrayRow(row: any[]): void;\n  /** Add one row */\n  addObjectRow(row: {[columnName: string]: any}): void;\n  /** return a batch object */\n  getBatch(): TableBatch | null;\n}\n"], "mappings": ""}