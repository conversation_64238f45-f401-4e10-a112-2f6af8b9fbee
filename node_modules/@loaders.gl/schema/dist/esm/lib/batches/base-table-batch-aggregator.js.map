{"version": 3, "file": "base-table-batch-aggregator.js", "names": ["DEFAULT_ROW_COUNT", "RowTableBatchAggregator", "constructor", "schema", "options", "_defineProperty", "Array", "isArray", "_headers", "key", "index", "name", "rowCount", "length", "addArrayRow", "row", "cursor", "Number", "isFinite", "rows", "addObjectRow", "getBatch", "slice", "batch", "shape", "batchType", "data"], "sources": ["../../../../src/lib/batches/base-table-batch-aggregator.ts"], "sourcesContent": ["import type {Schema} from '../schema/schema';\nimport type {TableBatch} from '../../category/table/table-types';\nimport {TableBatchAggregator, TableBatchOptions} from './table-batch-aggregator';\n\nconst DEFAULT_ROW_COUNT = 100;\n\nexport default class RowTableBatchAggregator implements TableBatchAggregator {\n  schema: Schema;\n  options: TableBatchOptions;\n\n  length: number = 0;\n  rows: any[] | null = null;\n  cursor: number = 0;\n  private _headers: string[] = [];\n\n  constructor(schema: Schema, options: TableBatchOptions) {\n    this.options = options;\n    this.schema = schema;\n\n    // schema is an array if there're no headers\n    // object if there are headers\n    if (!Array.isArray(schema)) {\n      this._headers = [];\n      for (const key in schema) {\n        this._headers[schema[key].index] = schema[key].name;\n      }\n    }\n  }\n\n  rowCount(): number {\n    return this.length;\n  }\n\n  addArrayRow(row: any[], cursor?: number): void {\n    if (Number.isFinite(cursor)) {\n      this.cursor = cursor as number;\n    }\n\n    this.rows = this.rows || new Array(DEFAULT_ROW_COUNT);\n    this.rows[this.length] = row;\n    this.length++;\n  }\n\n  addObjectRow(row: {[columnName: string]: any}, cursor?: number): void {\n    if (Number.isFinite(cursor)) {\n      this.cursor = cursor as number;\n    }\n\n    this.rows = this.rows || new Array(DEFAULT_ROW_COUNT);\n    this.rows[this.length] = row;\n    this.length++;\n  }\n\n  getBatch(): TableBatch | null {\n    let rows = this.rows;\n    if (!rows) {\n      return null;\n    }\n\n    rows = rows.slice(0, this.length);\n    this.rows = null;\n\n    const batch: TableBatch = {\n      shape: this.options.shape,\n      batchType: 'data',\n      data: rows,\n      length: this.length,\n      schema: this.schema,\n      cursor: this.cursor\n    };\n\n    return batch;\n  }\n}\n"], "mappings": ";AAIA,MAAMA,iBAAiB,GAAG,GAAG;AAE7B,eAAe,MAAMC,uBAAuB,CAAiC;EAS3EC,WAAWA,CAACC,MAAc,EAAEC,OAA0B,EAAE;IAAAC,eAAA;IAAAA,eAAA;IAAAA,eAAA,iBALvC,CAAC;IAAAA,eAAA,eACG,IAAI;IAAAA,eAAA,iBACR,CAAC;IAAAA,eAAA,mBACW,EAAE;IAG7B,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,MAAM,GAAGA,MAAM;IAIpB,IAAI,CAACG,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,EAAE;MAC1B,IAAI,CAACK,QAAQ,GAAG,EAAE;MAClB,KAAK,MAAMC,GAAG,IAAIN,MAAM,EAAE;QACxB,IAAI,CAACK,QAAQ,CAACL,MAAM,CAACM,GAAG,CAAC,CAACC,KAAK,CAAC,GAAGP,MAAM,CAACM,GAAG,CAAC,CAACE,IAAI;MACrD;IACF;EACF;EAEAC,QAAQA,CAAA,EAAW;IACjB,OAAO,IAAI,CAACC,MAAM;EACpB;EAEAC,WAAWA,CAACC,GAAU,EAAEC,MAAe,EAAQ;IAC7C,IAAIC,MAAM,CAACC,QAAQ,CAACF,MAAM,CAAC,EAAE;MAC3B,IAAI,CAACA,MAAM,GAAGA,MAAgB;IAChC;IAEA,IAAI,CAACG,IAAI,GAAG,IAAI,CAACA,IAAI,IAAI,IAAIb,KAAK,CAACN,iBAAiB,CAAC;IACrD,IAAI,CAACmB,IAAI,CAAC,IAAI,CAACN,MAAM,CAAC,GAAGE,GAAG;IAC5B,IAAI,CAACF,MAAM,EAAE;EACf;EAEAO,YAAYA,CAACL,GAAgC,EAAEC,MAAe,EAAQ;IACpE,IAAIC,MAAM,CAACC,QAAQ,CAACF,MAAM,CAAC,EAAE;MAC3B,IAAI,CAACA,MAAM,GAAGA,MAAgB;IAChC;IAEA,IAAI,CAACG,IAAI,GAAG,IAAI,CAACA,IAAI,IAAI,IAAIb,KAAK,CAACN,iBAAiB,CAAC;IACrD,IAAI,CAACmB,IAAI,CAAC,IAAI,CAACN,MAAM,CAAC,GAAGE,GAAG;IAC5B,IAAI,CAACF,MAAM,EAAE;EACf;EAEAQ,QAAQA,CAAA,EAAsB;IAC5B,IAAIF,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAI,CAACA,IAAI,EAAE;MACT,OAAO,IAAI;IACb;IAEAA,IAAI,GAAGA,IAAI,CAACG,KAAK,CAAC,CAAC,EAAE,IAAI,CAACT,MAAM,CAAC;IACjC,IAAI,CAACM,IAAI,GAAG,IAAI;IAEhB,MAAMI,KAAiB,GAAG;MACxBC,KAAK,EAAE,IAAI,CAACpB,OAAO,CAACoB,KAAK;MACzBC,SAAS,EAAE,MAAM;MACjBC,IAAI,EAAEP,IAAI;MACVN,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBV,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBa,MAAM,EAAE,IAAI,CAACA;IACf,CAAC;IAED,OAAOO,KAAK;EACd;AACF"}