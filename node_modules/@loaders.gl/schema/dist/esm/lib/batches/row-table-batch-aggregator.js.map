{"version": 3, "file": "row-table-batch-aggregator.js", "names": ["convertToArrayRow", "convertToObjectRow", "DEFAULT_ROW_COUNT", "RowTableBatchAggregator", "constructor", "schema", "options", "_defineProperty", "Array", "isArray", "_headers", "key", "index", "name", "rowCount", "length", "addArrayRow", "row", "cursor", "Number", "isFinite", "shape", "rowObject", "addObjectRow", "arrayRows", "rowArray", "objectRows", "getBatch", "rows", "slice", "batchType", "data"], "sources": ["../../../../src/lib/batches/row-table-batch-aggregator.ts"], "sourcesContent": ["import type {Schema} from '../schema/schema';\nimport type {TableBatch} from '../../category/table/table-types';\n// import type {ArrayRowTableBatch, ObjectRowTableBatch} from '../../category/table';\nimport {convertToArrayRow, convertToObjectRow} from '../utils/row-utils';\nimport {TableBatchAggregator, TableBatchOptions} from './table-batch-aggregator';\n\nconst DEFAULT_ROW_COUNT = 100;\n\nexport default class RowTableBatchAggregator implements TableBatchAggregator {\n  schema: Schema;\n  options: TableBatchOptions;\n\n  length: number = 0;\n  objectRows: {[columnName: string]: any} | null = null;\n  arrayRows: any[] | null = null;\n  cursor: number = 0;\n  private _headers: string[] = [];\n\n  constructor(schema: Schema, options: TableBatchOptions) {\n    this.options = options;\n    this.schema = schema;\n\n    // schema is an array if there're no headers\n    // object if there are headers\n    if (!Array.isArray(schema)) {\n      this._headers = [];\n      for (const key in schema) {\n        this._headers[schema[key].index] = schema[key].name;\n      }\n    }\n  }\n\n  rowCount(): number {\n    return this.length;\n  }\n\n  addArrayRow(row: any[], cursor?: number): void {\n    if (Number.isFinite(cursor)) {\n      this.cursor = cursor as number;\n    }\n\n    // eslint-disable-next-line default-case\n    switch (this.options.shape) {\n      case 'object-row-table':\n        const rowObject = convertToObjectRow(row, this._headers);\n        this.addObjectRow(rowObject, cursor);\n        break;\n      case 'array-row-table':\n        this.arrayRows = this.arrayRows || new Array(DEFAULT_ROW_COUNT);\n        this.arrayRows[this.length] = row;\n        this.length++;\n        break;\n    }\n  }\n\n  addObjectRow(row: {[columnName: string]: any}, cursor?: number): void {\n    if (Number.isFinite(cursor)) {\n      this.cursor = cursor as number;\n    }\n\n    // eslint-disable-next-line default-case\n    switch (this.options.shape) {\n      case 'array-row-table':\n        const rowArray = convertToArrayRow(row, this._headers);\n        this.addArrayRow(rowArray, cursor);\n        break;\n      case 'object-row-table':\n        this.objectRows = this.objectRows || new Array(DEFAULT_ROW_COUNT);\n        this.objectRows[this.length] = row;\n        this.length++;\n        break;\n    }\n  }\n\n  getBatch(): TableBatch | null {\n    let rows = this.arrayRows || this.objectRows;\n    if (!rows) {\n      return null;\n    }\n\n    rows = rows.slice(0, this.length);\n    this.arrayRows = null;\n    this.objectRows = null;\n\n    return {\n      shape: this.options.shape,\n      batchType: 'data',\n      data: rows,\n      length: this.length,\n      schema: this.schema,\n      cursor: this.cursor\n    };\n  }\n}\n"], "mappings": ";AAGA,SAAQA,iBAAiB,EAAEC,kBAAkB,QAAO,oBAAoB;AAGxE,MAAMC,iBAAiB,GAAG,GAAG;AAE7B,eAAe,MAAMC,uBAAuB,CAAiC;EAU3EC,WAAWA,CAACC,MAAc,EAAEC,OAA0B,EAAE;IAAAC,eAAA;IAAAA,eAAA;IAAAA,eAAA,iBANvC,CAAC;IAAAA,eAAA,qBAC+B,IAAI;IAAAA,eAAA,oBAC3B,IAAI;IAAAA,eAAA,iBACb,CAAC;IAAAA,eAAA,mBACW,EAAE;IAG7B,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,MAAM,GAAGA,MAAM;IAIpB,IAAI,CAACG,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,EAAE;MAC1B,IAAI,CAACK,QAAQ,GAAG,EAAE;MAClB,KAAK,MAAMC,GAAG,IAAIN,MAAM,EAAE;QACxB,IAAI,CAACK,QAAQ,CAACL,MAAM,CAACM,GAAG,CAAC,CAACC,KAAK,CAAC,GAAGP,MAAM,CAACM,GAAG,CAAC,CAACE,IAAI;MACrD;IACF;EACF;EAEAC,QAAQA,CAAA,EAAW;IACjB,OAAO,IAAI,CAACC,MAAM;EACpB;EAEAC,WAAWA,CAACC,GAAU,EAAEC,MAAe,EAAQ;IAC7C,IAAIC,MAAM,CAACC,QAAQ,CAACF,MAAM,CAAC,EAAE;MAC3B,IAAI,CAACA,MAAM,GAAGA,MAAgB;IAChC;IAGA,QAAQ,IAAI,CAACZ,OAAO,CAACe,KAAK;MACxB,KAAK,kBAAkB;QACrB,MAAMC,SAAS,GAAGrB,kBAAkB,CAACgB,GAAG,EAAE,IAAI,CAACP,QAAQ,CAAC;QACxD,IAAI,CAACa,YAAY,CAACD,SAAS,EAAEJ,MAAM,CAAC;QACpC;MACF,KAAK,iBAAiB;QACpB,IAAI,CAACM,SAAS,GAAG,IAAI,CAACA,SAAS,IAAI,IAAIhB,KAAK,CAACN,iBAAiB,CAAC;QAC/D,IAAI,CAACsB,SAAS,CAAC,IAAI,CAACT,MAAM,CAAC,GAAGE,GAAG;QACjC,IAAI,CAACF,MAAM,EAAE;QACb;IACJ;EACF;EAEAQ,YAAYA,CAACN,GAAgC,EAAEC,MAAe,EAAQ;IACpE,IAAIC,MAAM,CAACC,QAAQ,CAACF,MAAM,CAAC,EAAE;MAC3B,IAAI,CAACA,MAAM,GAAGA,MAAgB;IAChC;IAGA,QAAQ,IAAI,CAACZ,OAAO,CAACe,KAAK;MACxB,KAAK,iBAAiB;QACpB,MAAMI,QAAQ,GAAGzB,iBAAiB,CAACiB,GAAG,EAAE,IAAI,CAACP,QAAQ,CAAC;QACtD,IAAI,CAACM,WAAW,CAACS,QAAQ,EAAEP,MAAM,CAAC;QAClC;MACF,KAAK,kBAAkB;QACrB,IAAI,CAACQ,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI,IAAIlB,KAAK,CAACN,iBAAiB,CAAC;QACjE,IAAI,CAACwB,UAAU,CAAC,IAAI,CAACX,MAAM,CAAC,GAAGE,GAAG;QAClC,IAAI,CAACF,MAAM,EAAE;QACb;IACJ;EACF;EAEAY,QAAQA,CAAA,EAAsB;IAC5B,IAAIC,IAAI,GAAG,IAAI,CAACJ,SAAS,IAAI,IAAI,CAACE,UAAU;IAC5C,IAAI,CAACE,IAAI,EAAE;MACT,OAAO,IAAI;IACb;IAEAA,IAAI,GAAGA,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,IAAI,CAACd,MAAM,CAAC;IACjC,IAAI,CAACS,SAAS,GAAG,IAAI;IACrB,IAAI,CAACE,UAAU,GAAG,IAAI;IAEtB,OAAO;MACLL,KAAK,EAAE,IAAI,CAACf,OAAO,CAACe,KAAK;MACzBS,SAAS,EAAE,MAAM;MACjBC,IAAI,EAAEH,IAAI;MACVb,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBV,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBa,MAAM,EAAE,IAAI,CAACA;IACf,CAAC;EACH;AACF"}