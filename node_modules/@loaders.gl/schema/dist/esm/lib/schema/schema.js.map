{"version": 3, "file": "schema.js", "names": ["default", "<PERSON><PERSON><PERSON>", "Field", "Type", "DataType", "<PERSON><PERSON>", "Bool", "Int", "Int8", "Int16", "Int32", "Int64", "Uint8", "Uint16", "Uint32", "Uint64", "Float", "Float16", "Float32", "Float64", "Binary", "Utf8", "Date", "DateDay", "DateMillisecond", "Time", "TimeSecond", "TimeMillisecond", "Timestamp", "TimestampSecond", "TimestampMillisecond", "TimestampMicrosecond", "TimestampNanosecond", "Interval", "IntervalDayTime", "IntervalYearMonth", "FixedSizeList", "Struct"], "sources": ["../../../../src/lib/schema/schema.ts"], "sourcesContent": ["/*\nexport {\n  <PERSON>hema,\n  <PERSON>,\n  DataType,\n  Null,\n  Bool,\n  Int,\n  Int8,\n  Int16,\n  Int32,\n  Int64,\n  Uint8,\n  Uint16,\n  Uint32,\n  <PERSON>int64,\n  Float,\n  Float16,\n  <PERSON>loat32,\n  Float64,\n  Binary,\n  Utf8,\n  Date,\n  DateDay,\n  DateMillisecond,\n  Time,\n  TimeSecond,\n  TimeMillisecond,\n  TimeMicrosecond,\n  TimeNanosecond,\n  Timestamp,\n  TimestampSecond,\n  TimestampMillisecond,\n  TimestampMicrosecond,\n  TimestampNanosecond,\n  Interval,\n  IntervalDayTime,\n  IntervalYearMonth,\n  FixedSizeList\n} from 'apache-arrow';\n*/\nexport {default as Schema} from './impl/schema';\nexport {default as Field} from './impl/field';\nexport {Type} from './impl/type';\nexport {\n  DataType,\n  Null,\n  Bool,\n  Int,\n  Int8,\n  Int16,\n  Int32,\n  Int64,\n  Uint8,\n  Uint16,\n  Uint32,\n  Uint64,\n  Float,\n  Float16,\n  Float32,\n  Float64,\n  Binary,\n  Utf8,\n  Date,\n  DateDay,\n  DateMillisecond,\n  Time,\n  TimeSecond,\n  TimeMillisecond,\n  // TimeMicrosecond,\n  // TimeNanosecond,\n  Timestamp,\n  TimestampSecond,\n  TimestampMillisecond,\n  TimestampMicrosecond,\n  TimestampNanosecond,\n  Interval,\n  IntervalDayTime,\n  IntervalYearMonth,\n  FixedSizeList,\n  Struct\n} from './impl/type';\n"], "mappings": "AAyCA,SAAQA,OAAO,IAAIC,MAAM,QAAO,eAAe;AAC/C,SAAQD,OAAO,IAAIE,KAAK,QAAO,cAAc;AAC7C,SAAQC,IAAI,QAAO,aAAa;AAChC,SACEC,QAAQ,EACRC,IAAI,EACJC,IAAI,EACJC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,KAAK,EACLC,OAAO,EACPC,OAAO,EACPC,OAAO,EACPC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,OAAO,EACPC,eAAe,EACfC,IAAI,EACJC,UAAU,EACVC,eAAe,EAGfC,SAAS,EACTC,eAAe,EACfC,oBAAoB,EACpBC,oBAAoB,EACpBC,mBAAmB,EACnBC,QAAQ,EACRC,eAAe,EACfC,iBAAiB,EACjBC,aAAa,EACbC,MAAM,QACD,aAAa"}