{"version": 3, "file": "field.js", "names": ["Field", "constructor", "name", "type", "nullable", "arguments", "length", "undefined", "metadata", "Map", "_defineProperty", "typeId", "clone", "compareTo", "other", "toString", "concat"], "sources": ["../../../../../src/lib/schema/impl/field.ts"], "sourcesContent": ["import {DataType} from './type';\n\n/**\n * ArrowJS `Field` API-compatible class for row-based tables\n * https://loaders.gl/arrowjs/docs/api-reference/field\n * A field holds name, nullable, and metadata information about a table \"column\"\n * A Schema is essentially a list of fields\n */\nexport default class Field {\n  name: string;\n  type: DataType;\n  nullable: boolean;\n  metadata: Map<string, string>;\n\n  constructor(\n    name: string,\n    type: DataType,\n    nullable = false,\n    metadata: Map<string, string> = new Map()\n  ) {\n    this.name = name;\n    this.type = type;\n    this.nullable = nullable;\n    this.metadata = metadata;\n  }\n\n  get typeId(): number {\n    return this.type && this.type.typeId;\n  }\n\n  clone(): Field {\n    return new Field(this.name, this.type, this.nullable, this.metadata);\n  }\n\n  compareTo(other: this): boolean {\n    return (\n      this.name === other.name &&\n      this.type === other.type &&\n      this.nullable === other.nullable &&\n      this.metadata === other.metadata\n    );\n  }\n\n  toString(): string {\n    return `${this.type}${this.nullable ? ', nullable' : ''}${\n      this.metadata ? `, metadata: ${this.metadata}` : ''\n    }`;\n  }\n}\n"], "mappings": ";AAQA,eAAe,MAAMA,KAAK,CAAC;EAMzBC,WAAWA,CACTC,IAAY,EACZC,IAAc,EAGd;IAAA,IAFAC,QAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IAAA,IAChBG,QAA6B,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAII,GAAG,CAAC,CAAC;IAAAC,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAEzC,IAAI,CAACR,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACI,QAAQ,GAAGA,QAAQ;EAC1B;EAEA,IAAIG,MAAMA,CAAA,EAAW;IACnB,OAAO,IAAI,CAACR,IAAI,IAAI,IAAI,CAACA,IAAI,CAACQ,MAAM;EACtC;EAEAC,KAAKA,CAAA,EAAU;IACb,OAAO,IAAIZ,KAAK,CAAC,IAAI,CAACE,IAAI,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACI,QAAQ,CAAC;EACtE;EAEAK,SAASA,CAACC,KAAW,EAAW;IAC9B,OACE,IAAI,CAACZ,IAAI,KAAKY,KAAK,CAACZ,IAAI,IACxB,IAAI,CAACC,IAAI,KAAKW,KAAK,CAACX,IAAI,IACxB,IAAI,CAACC,QAAQ,KAAKU,KAAK,CAACV,QAAQ,IAChC,IAAI,CAACI,QAAQ,KAAKM,KAAK,CAACN,QAAQ;EAEpC;EAEAO,QAAQA,CAAA,EAAW;IACjB,UAAAC,MAAA,CAAU,IAAI,CAACb,IAAI,EAAAa,MAAA,CAAG,IAAI,CAACZ,QAAQ,GAAG,YAAY,GAAG,EAAE,EAAAY,MAAA,CACrD,IAAI,CAACR,QAAQ,kBAAAQ,MAAA,CAAkB,IAAI,CAACR,QAAQ,IAAK,EAAE;EAEvD;AACF"}