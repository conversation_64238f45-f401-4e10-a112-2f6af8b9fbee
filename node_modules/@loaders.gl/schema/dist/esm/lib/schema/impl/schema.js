import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
import { assert } from '../../utils/assert';
export default class Schema {
  constructor(fields, metadata) {
    _defineProperty(this, "fields", void 0);
    _defineProperty(this, "metadata", void 0);
    assert(Array.isArray(fields));
    checkNames(fields);
    this.fields = fields;
    this.metadata = metadata || new Map();
  }
  compareTo(other) {
    if (this.metadata !== other.metadata) {
      return false;
    }
    if (this.fields.length !== other.fields.length) {
      return false;
    }
    for (let i = 0; i < this.fields.length; ++i) {
      if (!this.fields[i].compareTo(other.fields[i])) {
        return false;
      }
    }
    return true;
  }
  select() {
    const nameMap = Object.create(null);
    for (var _len = arguments.length, columnNames = new Array(_len), _key = 0; _key < _len; _key++) {
      columnNames[_key] = arguments[_key];
    }
    for (const name of columnNames) {
      nameMap[name] = true;
    }
    const selectedFields = this.fields.filter(field => nameMap[field.name]);
    return new Schema(selectedFields, this.metadata);
  }
  selectAt() {
    for (var _len2 = arguments.length, columnIndices = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
      columnIndices[_key2] = arguments[_key2];
    }
    const selectedFields = columnIndices.map(index => this.fields[index]).filter(Boolean);
    return new Schema(selectedFields, this.metadata);
  }
  assign(schemaOrFields) {
    let fields;
    let metadata = this.metadata;
    if (schemaOrFields instanceof Schema) {
      const otherSchema = schemaOrFields;
      fields = otherSchema.fields;
      metadata = mergeMaps(mergeMaps(new Map(), this.metadata), otherSchema.metadata);
    } else {
      fields = schemaOrFields;
    }
    const fieldMap = Object.create(null);
    for (const field of this.fields) {
      fieldMap[field.name] = field;
    }
    for (const field of fields) {
      fieldMap[field.name] = field;
    }
    const mergedFields = Object.values(fieldMap);
    return new Schema(mergedFields, metadata);
  }
}
function checkNames(fields) {
  const usedNames = {};
  for (const field of fields) {
    if (usedNames[field.name]) {
      console.warn('Schema: duplicated field name', field.name, field);
    }
    usedNames[field.name] = true;
  }
}
function mergeMaps(m1, m2) {
  return new Map([...(m1 || new Map()), ...(m2 || new Map())]);
}
//# sourceMappingURL=schema.js.map