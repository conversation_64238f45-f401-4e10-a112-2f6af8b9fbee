import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
export default class Field {
  constructor(name, type) {
    let nullable = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
    let metadata = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : new Map();
    _defineProperty(this, "name", void 0);
    _defineProperty(this, "type", void 0);
    _defineProperty(this, "nullable", void 0);
    _defineProperty(this, "metadata", void 0);
    this.name = name;
    this.type = type;
    this.nullable = nullable;
    this.metadata = metadata;
  }
  get typeId() {
    return this.type && this.type.typeId;
  }
  clone() {
    return new Field(this.name, this.type, this.nullable, this.metadata);
  }
  compareTo(other) {
    return this.name === other.name && this.type === other.type && this.nullable === other.nullable && this.metadata === other.metadata;
  }
  toString() {
    return "".concat(this.type).concat(this.nullable ? ', nullable' : '').concat(this.metadata ? ", metadata: ".concat(this.metadata) : '');
  }
}
//# sourceMappingURL=field.js.map