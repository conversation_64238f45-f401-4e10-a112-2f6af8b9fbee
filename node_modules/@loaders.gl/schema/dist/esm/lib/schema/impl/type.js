import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
let _Symbol$toStringTag, _Symbol$toStringTag2, _Symbol$toStringTag3, _Symbol$toStringTag4, _Symbol$toStringTag5, _Symbol$toStringTag6, _Symbol$toStringTag7, _Symbol$toStringTag8;
import { Type } from './enum';
export { Type } from './enum';
export class DataType {
  static isNull(x) {
    return x && x.typeId === Type.Null;
  }
  static isInt(x) {
    return x && x.typeId === Type.Int;
  }
  static isFloat(x) {
    return x && x.typeId === Type.Float;
  }
  static isBinary(x) {
    return x && x.typeId === Type.Binary;
  }
  static isUtf8(x) {
    return x && x.typeId === Type.Utf8;
  }
  static isBool(x) {
    return x && x.typeId === Type.Bool;
  }
  static isDecimal(x) {
    return x && x.typeId === Type.Decimal;
  }
  static isDate(x) {
    return x && x.typeId === Type.Date;
  }
  static isTime(x) {
    return x && x.typeId === Type.Time;
  }
  static isTimestamp(x) {
    return x && x.typeId === Type.Timestamp;
  }
  static isInterval(x) {
    return x && x.typeId === Type.Interval;
  }
  static isList(x) {
    return x && x.typeId === Type.List;
  }
  static isStruct(x) {
    return x && x.typeId === Type.Struct;
  }
  static isUnion(x) {
    return x && x.typeId === Type.Union;
  }
  static isFixedSizeBinary(x) {
    return x && x.typeId === Type.FixedSizeBinary;
  }
  static isFixedSizeList(x) {
    return x && x.typeId === Type.FixedSizeList;
  }
  static isMap(x) {
    return x && x.typeId === Type.Map;
  }
  static isDictionary(x) {
    return x && x.typeId === Type.Dictionary;
  }
  get typeId() {
    return Type.NONE;
  }
  compareTo(other) {
    return this === other;
  }
}
export class Null extends DataType {
  get typeId() {
    return Type.Null;
  }
  get [Symbol.toStringTag]() {
    return 'Null';
  }
  toString() {
    return 'Null';
  }
}
export class Bool extends DataType {
  get typeId() {
    return Type.Bool;
  }
  get [Symbol.toStringTag]() {
    return 'Bool';
  }
  toString() {
    return 'Bool';
  }
}
_Symbol$toStringTag = Symbol.toStringTag;
export class Int extends DataType {
  constructor(isSigned, bitWidth) {
    super();
    _defineProperty(this, "isSigned", void 0);
    _defineProperty(this, "bitWidth", void 0);
    this.isSigned = isSigned;
    this.bitWidth = bitWidth;
  }
  get typeId() {
    return Type.Int;
  }
  get [_Symbol$toStringTag]() {
    return 'Int';
  }
  toString() {
    return "".concat(this.isSigned ? 'I' : 'Ui', "nt").concat(this.bitWidth);
  }
}
export class Int8 extends Int {
  constructor() {
    super(true, 8);
  }
}
export class Int16 extends Int {
  constructor() {
    super(true, 16);
  }
}
export class Int32 extends Int {
  constructor() {
    super(true, 32);
  }
}
export class Int64 extends Int {
  constructor() {
    super(true, 64);
  }
}
export class Uint8 extends Int {
  constructor() {
    super(false, 8);
  }
}
export class Uint16 extends Int {
  constructor() {
    super(false, 16);
  }
}
export class Uint32 extends Int {
  constructor() {
    super(false, 32);
  }
}
export class Uint64 extends Int {
  constructor() {
    super(false, 64);
  }
}
const Precision = {
  HALF: 16,
  SINGLE: 32,
  DOUBLE: 64
};
_Symbol$toStringTag2 = Symbol.toStringTag;
export class Float extends DataType {
  constructor(precision) {
    super();
    _defineProperty(this, "precision", void 0);
    this.precision = precision;
  }
  get typeId() {
    return Type.Float;
  }
  get [_Symbol$toStringTag2]() {
    return 'Float';
  }
  toString() {
    return "Float".concat(this.precision);
  }
}
export class Float16 extends Float {
  constructor() {
    super(Precision.HALF);
  }
}
export class Float32 extends Float {
  constructor() {
    super(Precision.SINGLE);
  }
}
export class Float64 extends Float {
  constructor() {
    super(Precision.DOUBLE);
  }
}
export class Binary extends DataType {
  constructor() {
    super();
  }
  get typeId() {
    return Type.Binary;
  }
  toString() {
    return 'Binary';
  }
  get [Symbol.toStringTag]() {
    return 'Binary';
  }
}
export class Utf8 extends DataType {
  get typeId() {
    return Type.Utf8;
  }
  get [Symbol.toStringTag]() {
    return 'Utf8';
  }
  toString() {
    return 'Utf8';
  }
}
const DateUnit = {
  DAY: 0,
  MILLISECOND: 1
};
_Symbol$toStringTag3 = Symbol.toStringTag;
export class Date extends DataType {
  constructor(unit) {
    super();
    _defineProperty(this, "unit", void 0);
    this.unit = unit;
  }
  get typeId() {
    return Type.Date;
  }
  get [_Symbol$toStringTag3]() {
    return 'Date';
  }
  toString() {
    return "Date".concat((this.unit + 1) * 32, "<").concat(DateUnit[this.unit], ">");
  }
}
export class DateDay extends Date {
  constructor() {
    super(DateUnit.DAY);
  }
}
export class DateMillisecond extends Date {
  constructor() {
    super(DateUnit.MILLISECOND);
  }
}
const TimeUnit = {
  SECOND: 1,
  MILLISECOND: 1e3,
  MICROSECOND: 1e6,
  NANOSECOND: 1e9
};
_Symbol$toStringTag4 = Symbol.toStringTag;
export class Time extends DataType {
  constructor(unit, bitWidth) {
    super();
    _defineProperty(this, "unit", void 0);
    _defineProperty(this, "bitWidth", void 0);
    this.unit = unit;
    this.bitWidth = bitWidth;
  }
  get typeId() {
    return Type.Time;
  }
  toString() {
    return "Time".concat(this.bitWidth, "<").concat(TimeUnit[this.unit], ">");
  }
  get [_Symbol$toStringTag4]() {
    return 'Time';
  }
}
export class TimeSecond extends Time {
  constructor() {
    super(TimeUnit.SECOND, 32);
  }
}
export class TimeMillisecond extends Time {
  constructor() {
    super(TimeUnit.MILLISECOND, 32);
  }
}
_Symbol$toStringTag5 = Symbol.toStringTag;
export class Timestamp extends DataType {
  constructor(unit) {
    let timezone = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
    super();
    _defineProperty(this, "unit", void 0);
    _defineProperty(this, "timezone", void 0);
    this.unit = unit;
    this.timezone = timezone;
  }
  get typeId() {
    return Type.Timestamp;
  }
  get [_Symbol$toStringTag5]() {
    return 'Timestamp';
  }
  toString() {
    return "Timestamp<".concat(TimeUnit[this.unit]).concat(this.timezone ? ", ".concat(this.timezone) : '', ">");
  }
}
export class TimestampSecond extends Timestamp {
  constructor() {
    let timezone = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;
    super(TimeUnit.SECOND, timezone);
  }
}
export class TimestampMillisecond extends Timestamp {
  constructor() {
    let timezone = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;
    super(TimeUnit.MILLISECOND, timezone);
  }
}
export class TimestampMicrosecond extends Timestamp {
  constructor() {
    let timezone = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;
    super(TimeUnit.MICROSECOND, timezone);
  }
}
export class TimestampNanosecond extends Timestamp {
  constructor() {
    let timezone = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;
    super(TimeUnit.NANOSECOND, timezone);
  }
}
const IntervalUnit = {
  DAY_TIME: 0,
  YEAR_MONTH: 1
};
_Symbol$toStringTag6 = Symbol.toStringTag;
export class Interval extends DataType {
  constructor(unit) {
    super();
    _defineProperty(this, "unit", void 0);
    this.unit = unit;
  }
  get typeId() {
    return Type.Interval;
  }
  get [_Symbol$toStringTag6]() {
    return 'Interval';
  }
  toString() {
    return "Interval<".concat(IntervalUnit[this.unit], ">");
  }
}
export class IntervalDayTime extends Interval {
  constructor() {
    super(IntervalUnit.DAY_TIME);
  }
}
export class IntervalYearMonth extends Interval {
  constructor() {
    super(IntervalUnit.YEAR_MONTH);
  }
}
_Symbol$toStringTag7 = Symbol.toStringTag;
export class FixedSizeList extends DataType {
  constructor(listSize, child) {
    super();
    _defineProperty(this, "listSize", void 0);
    _defineProperty(this, "children", void 0);
    this.listSize = listSize;
    this.children = [child];
  }
  get typeId() {
    return Type.FixedSizeList;
  }
  get valueType() {
    return this.children[0].type;
  }
  get valueField() {
    return this.children[0];
  }
  get [_Symbol$toStringTag7]() {
    return 'FixedSizeList';
  }
  toString() {
    return "FixedSizeList[".concat(this.listSize, "]<").concat(this.valueType, ">");
  }
}
_Symbol$toStringTag8 = Symbol.toStringTag;
export class Struct extends DataType {
  constructor(children) {
    super();
    _defineProperty(this, "children", void 0);
    this.children = children;
  }
  get typeId() {
    return Type.Struct;
  }
  toString() {
    return "Struct<{".concat(this.children.map(f => "".concat(f.name, ":").concat(f.type)).join(', '), "}>");
  }
  get [_Symbol$toStringTag8]() {
    return 'Struct';
  }
}
//# sourceMappingURL=type.js.map