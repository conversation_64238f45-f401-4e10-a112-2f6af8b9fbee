{"version": 3, "file": "schema.js", "names": ["assert", "<PERSON><PERSON><PERSON>", "constructor", "fields", "metadata", "_defineProperty", "Array", "isArray", "checkNames", "Map", "compareTo", "other", "length", "i", "select", "nameMap", "Object", "create", "_len", "arguments", "columnNames", "_key", "name", "<PERSON><PERSON><PERSON>s", "filter", "field", "selectAt", "_len2", "columnIndices", "_key2", "map", "index", "Boolean", "assign", "schema<PERSON>r<PERSON>ields", "otherSchema", "mergeMaps", "fieldMap", "mergedFields", "values", "usedNames", "console", "warn", "m1", "m2"], "sources": ["../../../../../src/lib/schema/impl/schema.ts"], "sourcesContent": ["import {assert} from '../../utils/assert';\nimport Field from './field';\n\nexport type SchemaMetadata = Map<string, any>;\n\n/**\n * ArrowJS `Schema` API-compatible class for row-based tables (returned from `DataTable`)\n * https://loaders.gl/arrowjs/docs/api-reference/schema\n */\nexport default class Schema {\n  fields: Field[];\n  // TODO - Arrow just allows Map<string, string>\n  metadata: SchemaMetadata;\n\n  constructor(fields: Field[], metadata?: SchemaMetadata) {\n    assert(Array.isArray(fields));\n    checkNames(fields);\n    // For kepler fields, create arrow compatible `Fields` that have kepler fields as `metadata`\n    this.fields = fields;\n    this.metadata = metadata || new Map();\n  }\n\n  // TODO - arrow only seems to compare fields, not metadata\n  compareTo(other: Schema): boolean {\n    if (this.metadata !== other.metadata) {\n      return false;\n    }\n    if (this.fields.length !== other.fields.length) {\n      return false;\n    }\n    for (let i = 0; i < this.fields.length; ++i) {\n      if (!this.fields[i].compareTo(other.fields[i])) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  select(...columnNames: string[]): Schema {\n    // Ensure column names reference valid fields\n    const nameMap = Object.create(null);\n    for (const name of columnNames) {\n      nameMap[name] = true;\n    }\n    const selectedFields = this.fields.filter((field) => nameMap[field.name]);\n    return new Schema(selectedFields, this.metadata);\n  }\n\n  selectAt(...columnIndices: number[]): Schema {\n    // Ensure column indices reference valid fields\n    const selectedFields = columnIndices.map((index) => this.fields[index]).filter(Boolean);\n    return new Schema(selectedFields, this.metadata);\n  }\n\n  assign(schemaOrFields: Schema | Field[]): Schema {\n    let fields: Field[];\n    let metadata: SchemaMetadata = this.metadata;\n\n    if (schemaOrFields instanceof Schema) {\n      const otherSchema = schemaOrFields;\n      fields = otherSchema.fields;\n      metadata = mergeMaps(mergeMaps(new Map(), this.metadata), otherSchema.metadata);\n    } else {\n      fields = schemaOrFields;\n    }\n\n    // Create a merged list of fields, overwrite fields in place, new fields at end\n    const fieldMap: {[key: string]: Field} = Object.create(null);\n\n    for (const field of this.fields) {\n      fieldMap[field.name] = field;\n    }\n\n    for (const field of fields) {\n      fieldMap[field.name] = field;\n    }\n\n    const mergedFields = Object.values(fieldMap);\n\n    return new Schema(mergedFields, metadata);\n  }\n}\n\n// Warn if any duplicated field names\nfunction checkNames(fields: Field[]): void {\n  const usedNames: Record<string, boolean> = {};\n  for (const field of fields) {\n    if (usedNames[field.name]) {\n      // eslint-disable-next-line\n      console.warn('Schema: duplicated field name', field.name, field);\n    }\n    usedNames[field.name] = true;\n  }\n}\n\nfunction mergeMaps<T>(m1: T, m2: T): T {\n  // @ts-ignore\n  return new Map([...(m1 || new Map()), ...(m2 || new Map())]);\n}\n"], "mappings": ";AAAA,SAAQA,MAAM,QAAO,oBAAoB;AASzC,eAAe,MAAMC,MAAM,CAAC;EAK1BC,WAAWA,CAACC,MAAe,EAAEC,QAAyB,EAAE;IAAAC,eAAA;IAAAA,eAAA;IACtDL,MAAM,CAACM,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,CAAC;IAC7BK,UAAU,CAACL,MAAM,CAAC;IAElB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ,IAAI,IAAIK,GAAG,CAAC,CAAC;EACvC;EAGAC,SAASA,CAACC,KAAa,EAAW;IAChC,IAAI,IAAI,CAACP,QAAQ,KAAKO,KAAK,CAACP,QAAQ,EAAE;MACpC,OAAO,KAAK;IACd;IACA,IAAI,IAAI,CAACD,MAAM,CAACS,MAAM,KAAKD,KAAK,CAACR,MAAM,CAACS,MAAM,EAAE;MAC9C,OAAO,KAAK;IACd;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACV,MAAM,CAACS,MAAM,EAAE,EAAEC,CAAC,EAAE;MAC3C,IAAI,CAAC,IAAI,CAACV,MAAM,CAACU,CAAC,CAAC,CAACH,SAAS,CAACC,KAAK,CAACR,MAAM,CAACU,CAAC,CAAC,CAAC,EAAE;QAC9C,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb;EAEAC,MAAMA,CAAA,EAAmC;IAEvC,MAAMC,OAAO,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAAC,SAAAC,IAAA,GAAAC,SAAA,CAAAP,MAAA,EAF5BQ,WAAW,OAAAd,KAAA,CAAAY,IAAA,GAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;MAAXD,WAAW,CAAAC,IAAA,IAAAF,SAAA,CAAAE,IAAA;IAAA;IAGnB,KAAK,MAAMC,IAAI,IAAIF,WAAW,EAAE;MAC9BL,OAAO,CAACO,IAAI,CAAC,GAAG,IAAI;IACtB;IACA,MAAMC,cAAc,GAAG,IAAI,CAACpB,MAAM,CAACqB,MAAM,CAAEC,KAAK,IAAKV,OAAO,CAACU,KAAK,CAACH,IAAI,CAAC,CAAC;IACzE,OAAO,IAAIrB,MAAM,CAACsB,cAAc,EAAE,IAAI,CAACnB,QAAQ,CAAC;EAClD;EAEAsB,QAAQA,CAAA,EAAqC;IAAA,SAAAC,KAAA,GAAAR,SAAA,CAAAP,MAAA,EAAjCgB,aAAa,OAAAtB,KAAA,CAAAqB,KAAA,GAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;MAAbD,aAAa,CAAAC,KAAA,IAAAV,SAAA,CAAAU,KAAA;IAAA;IAEvB,MAAMN,cAAc,GAAGK,aAAa,CAACE,GAAG,CAAEC,KAAK,IAAK,IAAI,CAAC5B,MAAM,CAAC4B,KAAK,CAAC,CAAC,CAACP,MAAM,CAACQ,OAAO,CAAC;IACvF,OAAO,IAAI/B,MAAM,CAACsB,cAAc,EAAE,IAAI,CAACnB,QAAQ,CAAC;EAClD;EAEA6B,MAAMA,CAACC,cAAgC,EAAU;IAC/C,IAAI/B,MAAe;IACnB,IAAIC,QAAwB,GAAG,IAAI,CAACA,QAAQ;IAE5C,IAAI8B,cAAc,YAAYjC,MAAM,EAAE;MACpC,MAAMkC,WAAW,GAAGD,cAAc;MAClC/B,MAAM,GAAGgC,WAAW,CAAChC,MAAM;MAC3BC,QAAQ,GAAGgC,SAAS,CAACA,SAAS,CAAC,IAAI3B,GAAG,CAAC,CAAC,EAAE,IAAI,CAACL,QAAQ,CAAC,EAAE+B,WAAW,CAAC/B,QAAQ,CAAC;IACjF,CAAC,MAAM;MACLD,MAAM,GAAG+B,cAAc;IACzB;IAGA,MAAMG,QAAgC,GAAGrB,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAE5D,KAAK,MAAMQ,KAAK,IAAI,IAAI,CAACtB,MAAM,EAAE;MAC/BkC,QAAQ,CAACZ,KAAK,CAACH,IAAI,CAAC,GAAGG,KAAK;IAC9B;IAEA,KAAK,MAAMA,KAAK,IAAItB,MAAM,EAAE;MAC1BkC,QAAQ,CAACZ,KAAK,CAACH,IAAI,CAAC,GAAGG,KAAK;IAC9B;IAEA,MAAMa,YAAY,GAAGtB,MAAM,CAACuB,MAAM,CAACF,QAAQ,CAAC;IAE5C,OAAO,IAAIpC,MAAM,CAACqC,YAAY,EAAElC,QAAQ,CAAC;EAC3C;AACF;AAGA,SAASI,UAAUA,CAACL,MAAe,EAAQ;EACzC,MAAMqC,SAAkC,GAAG,CAAC,CAAC;EAC7C,KAAK,MAAMf,KAAK,IAAItB,MAAM,EAAE;IAC1B,IAAIqC,SAAS,CAACf,KAAK,CAACH,IAAI,CAAC,EAAE;MAEzBmB,OAAO,CAACC,IAAI,CAAC,+BAA+B,EAAEjB,KAAK,CAACH,IAAI,EAAEG,KAAK,CAAC;IAClE;IACAe,SAAS,CAACf,KAAK,CAACH,IAAI,CAAC,GAAG,IAAI;EAC9B;AACF;AAEA,SAASc,SAASA,CAAIO,EAAK,EAAEC,EAAK,EAAK;EAErC,OAAO,IAAInC,GAAG,CAAC,CAAC,IAAIkC,EAAE,IAAI,IAAIlC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAImC,EAAE,IAAI,IAAInC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9D"}