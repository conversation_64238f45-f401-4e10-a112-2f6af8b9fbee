{"version": 3, "file": "type.js", "names": ["Type", "DataType", "isNull", "x", "typeId", "<PERSON><PERSON>", "isInt", "Int", "isFloat", "Float", "isBinary", "Binary", "isUtf8", "Utf8", "isBool", "Bool", "isDecimal", "Decimal", "isDate", "Date", "isTime", "Time", "isTimestamp", "Timestamp", "isInterval", "Interval", "isList", "List", "isStruct", "Struct", "isUnion", "Union", "isFixedSizeBinary", "FixedSizeBinary", "isFixedSizeList", "FixedSizeList", "isMap", "Map", "isDictionary", "Dictionary", "NONE", "compareTo", "other", "Symbol", "toStringTag", "toString", "_Symbol$toStringTag", "constructor", "isSigned", "bitWidth", "_defineProperty", "concat", "Int8", "Int16", "Int32", "Int64", "Uint8", "Uint16", "Uint32", "Uint64", "Precision", "HALF", "SINGLE", "DOUBLE", "_Symbol$toStringTag2", "precision", "Float16", "Float32", "Float64", "DateUnit", "DAY", "MILLISECOND", "_Symbol$toStringTag3", "unit", "DateDay", "DateMillisecond", "TimeUnit", "SECOND", "MICROSECOND", "NANOSECOND", "_Symbol$toStringTag4", "TimeSecond", "TimeMillisecond", "_Symbol$toStringTag5", "timezone", "arguments", "length", "undefined", "TimestampSecond", "TimestampMillisecond", "TimestampMicrosecond", "TimestampNanosecond", "IntervalUnit", "DAY_TIME", "YEAR_MONTH", "_Symbol$toStringTag6", "IntervalDayTime", "IntervalYearMonth", "_Symbol$toStringTag7", "listSize", "child", "children", "valueType", "type", "valueField", "_Symbol$toStringTag8", "map", "f", "name", "join"], "sources": ["../../../../../src/lib/schema/impl/type.ts"], "sourcesContent": ["// This code is adapted from ArrowJS https://github.com/apache/arrow\n// under Apache license http://www.apache.org/licenses/LICENSE-2.0\n\nimport {Type} from './enum';\n\nimport Field from './field';\n\nexport {Type} from './enum';\n\nexport type TypedIntArray =\n  | Int8Array\n  | Uint8Array\n  | Int16Array\n  | Uint16Array\n  | Int32Array\n  | Uint32Array\n  | Int32Array\n  | Uint32Array\n  | Uint8ClampedArray;\n\nexport type TypedFloatArray = Float32Array | Float64Array;\n\nexport type TypedArray = TypedIntArray | TypedFloatArray;\n\nexport type AnyArrayType = Array<any> | TypedIntArray | TypedFloatArray;\n\nexport class DataType {\n  static isNull(x: any): boolean {\n    return x && x.typeId === Type.Null;\n  }\n  static isInt(x: any): boolean {\n    return x && x.typeId === Type.Int;\n  }\n  static isFloat(x: any): boolean {\n    return x && x.typeId === Type.Float;\n  }\n  static isBinary(x: any): boolean {\n    return x && x.typeId === Type.Binary;\n  }\n  static isUtf8(x: any): boolean {\n    return x && x.typeId === Type.Utf8;\n  }\n  static isBool(x: any): boolean {\n    return x && x.typeId === Type.Bool;\n  }\n  static isDecimal(x: any): boolean {\n    return x && x.typeId === Type.Decimal;\n  }\n  static isDate(x: any): boolean {\n    return x && x.typeId === Type.Date;\n  }\n  static isTime(x: any): boolean {\n    return x && x.typeId === Type.Time;\n  }\n  static isTimestamp(x: any): boolean {\n    return x && x.typeId === Type.Timestamp;\n  }\n  static isInterval(x: any): boolean {\n    return x && x.typeId === Type.Interval;\n  }\n  static isList(x: any): boolean {\n    return x && x.typeId === Type.List;\n  }\n  static isStruct(x: any): boolean {\n    return x && x.typeId === Type.Struct;\n  }\n  static isUnion(x: any): boolean {\n    return x && x.typeId === Type.Union;\n  }\n  static isFixedSizeBinary(x: any): boolean {\n    return x && x.typeId === Type.FixedSizeBinary;\n  }\n  static isFixedSizeList(x: any): boolean {\n    return x && x.typeId === Type.FixedSizeList;\n  }\n  static isMap(x: any): boolean {\n    return x && x.typeId === Type.Map;\n  }\n  static isDictionary(x: any): boolean {\n    return x && x.typeId === Type.Dictionary;\n  }\n\n  get typeId(): Type {\n    return Type.NONE;\n  }\n\n  // get ArrayType(): AnyArrayType {\n  //   return Int8Array;\n  // }\n\n  // get ArrayType() { return Array; }\n  compareTo(other: DataType): boolean {\n    // TODO\n    return this === other; // comparer.visit(this, other);\n  }\n}\n\n// NULL\n\nexport class Null extends DataType {\n  get typeId(): Type {\n    return Type.Null;\n  }\n  get [Symbol.toStringTag](): string {\n    return 'Null';\n  }\n  toString(): string {\n    return 'Null';\n  }\n}\n\n// BOOLEANS\n\nexport class Bool extends DataType {\n  get typeId(): Type {\n    return Type.Bool;\n  }\n  // get ArrayType() {\n  //   return Uint8Array;\n  // }\n  get [Symbol.toStringTag](): string {\n    return 'Bool';\n  }\n  toString(): string {\n    return 'Bool';\n  }\n}\n\n// INTS\n\nexport class Int extends DataType {\n  readonly isSigned: boolean;\n  readonly bitWidth: number;\n  constructor(isSigned, bitWidth) {\n    super();\n    this.isSigned = isSigned;\n    this.bitWidth = bitWidth;\n  }\n  get typeId(): Type {\n    return Type.Int;\n  }\n  // get ArrayType() {\n  //   switch (this.bitWidth) {\n  //     case 8:\n  //       return this.isSigned ? Int8Array : Uint8Array;\n  //     case 16:\n  //       return this.isSigned ? Int16Array : Uint16Array;\n  //     case 32:\n  //       return this.isSigned ? Int32Array : Uint32Array;\n  //     case 64:\n  //       return this.isSigned ? Int32Array : Uint32Array;\n  //     default:\n  //       throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`);\n  //   }\n  // }\n  get [Symbol.toStringTag](): string {\n    return 'Int';\n  }\n  toString(): string {\n    return `${this.isSigned ? 'I' : 'Ui'}nt${this.bitWidth}`;\n  }\n}\n\nexport class Int8 extends Int {\n  constructor() {\n    super(true, 8);\n  }\n}\nexport class Int16 extends Int {\n  constructor() {\n    super(true, 16);\n  }\n}\nexport class Int32 extends Int {\n  constructor() {\n    super(true, 32);\n  }\n}\nexport class Int64 extends Int {\n  constructor() {\n    super(true, 64);\n  }\n}\nexport class Uint8 extends Int {\n  constructor() {\n    super(false, 8);\n  }\n}\nexport class Uint16 extends Int {\n  constructor() {\n    super(false, 16);\n  }\n}\nexport class Uint32 extends Int {\n  constructor() {\n    super(false, 32);\n  }\n}\nexport class Uint64 extends Int {\n  constructor() {\n    super(false, 64);\n  }\n}\n\n// FLOATS\n\nconst Precision = {\n  HALF: 16,\n  SINGLE: 32,\n  DOUBLE: 64\n};\n\nexport class Float extends DataType {\n  readonly precision: number;\n  constructor(precision) {\n    super();\n    this.precision = precision;\n  }\n  get typeId(): Type {\n    return Type.Float;\n  }\n  // get ArrayType() {\n  //   switch (this.precision) {\n  //     case Precision.HALF:\n  //       return Uint16Array;\n  //     case Precision.SINGLE:\n  //       return Float32Array;\n  //     case Precision.DOUBLE:\n  //       return Float64Array;\n  //     default:\n  //       throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`);\n  //   }\n  // }\n  get [Symbol.toStringTag](): string {\n    return 'Float';\n  }\n  toString(): string {\n    return `Float${this.precision}`;\n  }\n}\n\nexport class Float16 extends Float {\n  constructor() {\n    super(Precision.HALF);\n  }\n}\nexport class Float32 extends Float {\n  constructor() {\n    super(Precision.SINGLE);\n  }\n}\nexport class Float64 extends Float {\n  constructor() {\n    super(Precision.DOUBLE);\n  }\n}\n\nexport class Binary extends DataType {\n  constructor() {\n    super();\n  }\n  get typeId() {\n    return Type.Binary;\n  }\n  toString() {\n    return 'Binary';\n  }\n  get [Symbol.toStringTag]() {\n    return 'Binary';\n  }\n}\n\n// STRINGS\n\nexport class Utf8 extends DataType {\n  get typeId(): Type {\n    return Type.Utf8;\n  }\n  // get ArrayType() {\n  //   return Uint8Array;\n  // }\n  get [Symbol.toStringTag](): string {\n    return 'Utf8';\n  }\n  toString(): string {\n    return 'Utf8';\n  }\n}\n\n// DATES, TIMES AND INTERVALS\n\nconst DateUnit = {\n  DAY: 0,\n  MILLISECOND: 1\n};\n\nexport class Date extends DataType {\n  readonly unit: number;\n  constructor(unit) {\n    super();\n    this.unit = unit;\n  }\n  get typeId(): Type {\n    return Type.Date;\n  }\n  // get ArrayType() {\n  //   return Int32Array;\n  // }\n  get [Symbol.toStringTag](): string {\n    return 'Date';\n  }\n  toString(): string {\n    return `Date${(this.unit + 1) * 32}<${DateUnit[this.unit]}>`;\n  }\n}\n\nexport class DateDay extends Date {\n  constructor() {\n    super(DateUnit.DAY);\n  }\n}\nexport class DateMillisecond extends Date {\n  constructor() {\n    super(DateUnit.MILLISECOND);\n  }\n}\n\nconst TimeUnit = {\n  SECOND: 1,\n  MILLISECOND: 1e3,\n  MICROSECOND: 1e6,\n  NANOSECOND: 1e9\n};\n\nexport class Time extends DataType {\n  readonly unit: any;\n  readonly bitWidth: number;\n\n  constructor(unit: any, bitWidth: number) {\n    super();\n    this.unit = unit;\n    this.bitWidth = bitWidth;\n  }\n  get typeId(): Type {\n    return Type.Time;\n  }\n  toString(): string {\n    return `Time${this.bitWidth}<${TimeUnit[this.unit]}>`;\n  }\n  get [Symbol.toStringTag](): string {\n    return 'Time';\n  }\n  // get ArrayType() {\n  //   return Int32Array;\n  // }\n}\n\nexport class TimeSecond extends Time {\n  constructor() {\n    super(TimeUnit.SECOND, 32);\n  }\n}\nexport class TimeMillisecond extends Time {\n  constructor() {\n    super(TimeUnit.MILLISECOND, 32);\n  }\n}\n// export class TimeMicrosecond extends Time { constructor() { super(TimeUnit.MICROSECOND, 64); } }\n// export class TimeNanosecond extends Time { constructor() { super(TimeUnit.NANOSECOND, 64); } }\n\nexport class Timestamp extends DataType {\n  readonly unit: any;\n  readonly timezone: any;\n\n  constructor(unit: any, timezone = null) {\n    super();\n    this.unit = unit;\n    this.timezone = timezone;\n  }\n  get typeId(): Type {\n    return Type.Timestamp;\n  }\n  // get ArrayType() {\n  //   return Int32Array;\n  // }\n  get [Symbol.toStringTag](): string {\n    return 'Timestamp';\n  }\n  toString(): string {\n    return `Timestamp<${TimeUnit[this.unit]}${this.timezone ? `, ${this.timezone}` : ''}>`;\n  }\n}\n\nexport class TimestampSecond extends Timestamp {\n  constructor(timezone = null) {\n    super(TimeUnit.SECOND, timezone);\n  }\n}\nexport class TimestampMillisecond extends Timestamp {\n  constructor(timezone = null) {\n    super(TimeUnit.MILLISECOND, timezone);\n  }\n}\nexport class TimestampMicrosecond extends Timestamp {\n  constructor(timezone = null) {\n    super(TimeUnit.MICROSECOND, timezone);\n  }\n}\nexport class TimestampNanosecond extends Timestamp {\n  constructor(timezone = null) {\n    super(TimeUnit.NANOSECOND, timezone);\n  }\n}\n\nconst IntervalUnit = {\n  DAY_TIME: 0,\n  YEAR_MONTH: 1\n};\n\nexport class Interval extends DataType {\n  readonly unit: number;\n  constructor(unit: number) {\n    super();\n    this.unit = unit;\n  }\n  get typeId(): Type {\n    return Type.Interval;\n  }\n  // get ArrayType() {\n  //   return Int32Array;\n  // }\n  get [Symbol.toStringTag](): string {\n    return 'Interval';\n  }\n  toString(): string {\n    return `Interval<${IntervalUnit[this.unit]}>`;\n  }\n}\n\nexport class IntervalDayTime extends Interval {\n  constructor() {\n    super(IntervalUnit.DAY_TIME);\n  }\n}\nexport class IntervalYearMonth extends Interval {\n  constructor() {\n    super(IntervalUnit.YEAR_MONTH);\n  }\n}\n\nexport class FixedSizeList extends DataType {\n  readonly listSize: number;\n  readonly children: Field[];\n\n  constructor(listSize: number, child: Field) {\n    super();\n    this.listSize = listSize;\n    this.children = [child];\n  }\n  get typeId(): Type {\n    return Type.FixedSizeList;\n  }\n  get valueType() {\n    return this.children[0].type;\n  }\n  get valueField() {\n    return this.children[0];\n  }\n  // get ArrayType() {\n  //   return this.valueType.ArrayType;\n  // }\n  get [Symbol.toStringTag](): string {\n    return 'FixedSizeList';\n  }\n  toString(): string {\n    return `FixedSizeList[${this.listSize}]<${this.valueType}>`;\n  }\n}\n\nexport class Struct extends DataType {\n  public readonly children: Field[];\n\n  constructor(children: Field[]) {\n    super();\n    this.children = children;\n  }\n\n  public get typeId() {\n    return Type.Struct;\n  }\n  public toString() {\n    return `Struct<{${this.children.map((f) => `${f.name}:${f.type}`).join(', ')}}>`;\n  }\n  get [Symbol.toStringTag](): string {\n    return 'Struct';\n  }\n}\n"], "mappings": ";;AAGA,SAAQA,IAAI,QAAO,QAAQ;AAI3B,SAAQA,IAAI,QAAO,QAAQ;AAmB3B,OAAO,MAAMC,QAAQ,CAAC;EACpB,OAAOC,MAAMA,CAACC,CAAM,EAAW;IAC7B,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKJ,IAAI,CAACK,IAAI;EACpC;EACA,OAAOC,KAAKA,CAACH,CAAM,EAAW;IAC5B,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKJ,IAAI,CAACO,GAAG;EACnC;EACA,OAAOC,OAAOA,CAACL,CAAM,EAAW;IAC9B,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKJ,IAAI,CAACS,KAAK;EACrC;EACA,OAAOC,QAAQA,CAACP,CAAM,EAAW;IAC/B,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKJ,IAAI,CAACW,MAAM;EACtC;EACA,OAAOC,MAAMA,CAACT,CAAM,EAAW;IAC7B,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKJ,IAAI,CAACa,IAAI;EACpC;EACA,OAAOC,MAAMA,CAACX,CAAM,EAAW;IAC7B,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKJ,IAAI,CAACe,IAAI;EACpC;EACA,OAAOC,SAASA,CAACb,CAAM,EAAW;IAChC,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKJ,IAAI,CAACiB,OAAO;EACvC;EACA,OAAOC,MAAMA,CAACf,CAAM,EAAW;IAC7B,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKJ,IAAI,CAACmB,IAAI;EACpC;EACA,OAAOC,MAAMA,CAACjB,CAAM,EAAW;IAC7B,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKJ,IAAI,CAACqB,IAAI;EACpC;EACA,OAAOC,WAAWA,CAACnB,CAAM,EAAW;IAClC,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKJ,IAAI,CAACuB,SAAS;EACzC;EACA,OAAOC,UAAUA,CAACrB,CAAM,EAAW;IACjC,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKJ,IAAI,CAACyB,QAAQ;EACxC;EACA,OAAOC,MAAMA,CAACvB,CAAM,EAAW;IAC7B,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKJ,IAAI,CAAC2B,IAAI;EACpC;EACA,OAAOC,QAAQA,CAACzB,CAAM,EAAW;IAC/B,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKJ,IAAI,CAAC6B,MAAM;EACtC;EACA,OAAOC,OAAOA,CAAC3B,CAAM,EAAW;IAC9B,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKJ,IAAI,CAAC+B,KAAK;EACrC;EACA,OAAOC,iBAAiBA,CAAC7B,CAAM,EAAW;IACxC,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKJ,IAAI,CAACiC,eAAe;EAC/C;EACA,OAAOC,eAAeA,CAAC/B,CAAM,EAAW;IACtC,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKJ,IAAI,CAACmC,aAAa;EAC7C;EACA,OAAOC,KAAKA,CAACjC,CAAM,EAAW;IAC5B,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKJ,IAAI,CAACqC,GAAG;EACnC;EACA,OAAOC,YAAYA,CAACnC,CAAM,EAAW;IACnC,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKJ,IAAI,CAACuC,UAAU;EAC1C;EAEA,IAAInC,MAAMA,CAAA,EAAS;IACjB,OAAOJ,IAAI,CAACwC,IAAI;EAClB;EAOAC,SAASA,CAACC,KAAe,EAAW;IAElC,OAAO,IAAI,KAAKA,KAAK;EACvB;AACF;AAIA,OAAO,MAAMrC,IAAI,SAASJ,QAAQ,CAAC;EACjC,IAAIG,MAAMA,CAAA,EAAS;IACjB,OAAOJ,IAAI,CAACK,IAAI;EAClB;EACA,KAAKsC,MAAM,CAACC,WAAW,IAAY;IACjC,OAAO,MAAM;EACf;EACAC,QAAQA,CAAA,EAAW;IACjB,OAAO,MAAM;EACf;AACF;AAIA,OAAO,MAAM9B,IAAI,SAASd,QAAQ,CAAC;EACjC,IAAIG,MAAMA,CAAA,EAAS;IACjB,OAAOJ,IAAI,CAACe,IAAI;EAClB;EAIA,KAAK4B,MAAM,CAACC,WAAW,IAAY;IACjC,OAAO,MAAM;EACf;EACAC,QAAQA,CAAA,EAAW;IACjB,OAAO,MAAM;EACf;AACF;AAACC,mBAAA,GA6BMH,MAAM,CAACC,WAAW;AAzBzB,OAAO,MAAMrC,GAAG,SAASN,QAAQ,CAAC;EAGhC8C,WAAWA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IAC9B,KAAK,CAAC,CAAC;IAACC,eAAA;IAAAA,eAAA;IACR,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC1B;EACA,IAAI7C,MAAMA,CAAA,EAAS;IACjB,OAAOJ,IAAI,CAACO,GAAG;EACjB;EAeA,KAAAuC,mBAAA,IAAmC;IACjC,OAAO,KAAK;EACd;EACAD,QAAQA,CAAA,EAAW;IACjB,UAAAM,MAAA,CAAU,IAAI,CAACH,QAAQ,GAAG,GAAG,GAAG,IAAI,QAAAG,MAAA,CAAK,IAAI,CAACF,QAAQ;EACxD;AACF;AAEA,OAAO,MAAMG,IAAI,SAAS7C,GAAG,CAAC;EAC5BwC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;EAChB;AACF;AACA,OAAO,MAAMM,KAAK,SAAS9C,GAAG,CAAC;EAC7BwC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;EACjB;AACF;AACA,OAAO,MAAMO,KAAK,SAAS/C,GAAG,CAAC;EAC7BwC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;EACjB;AACF;AACA,OAAO,MAAMQ,KAAK,SAAShD,GAAG,CAAC;EAC7BwC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;EACjB;AACF;AACA,OAAO,MAAMS,KAAK,SAASjD,GAAG,CAAC;EAC7BwC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;EACjB;AACF;AACA,OAAO,MAAMU,MAAM,SAASlD,GAAG,CAAC;EAC9BwC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC;EAClB;AACF;AACA,OAAO,MAAMW,MAAM,SAASnD,GAAG,CAAC;EAC9BwC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC;EAClB;AACF;AACA,OAAO,MAAMY,MAAM,SAASpD,GAAG,CAAC;EAC9BwC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC;EAClB;AACF;AAIA,MAAMa,SAAS,GAAG;EAChBC,IAAI,EAAE,EAAE;EACRC,MAAM,EAAE,EAAE;EACVC,MAAM,EAAE;AACV,CAAC;AAACC,oBAAA,GAuBKrB,MAAM,CAACC,WAAW;AArBzB,OAAO,MAAMnC,KAAK,SAASR,QAAQ,CAAC;EAElC8C,WAAWA,CAACkB,SAAS,EAAE;IACrB,KAAK,CAAC,CAAC;IAACf,eAAA;IACR,IAAI,CAACe,SAAS,GAAGA,SAAS;EAC5B;EACA,IAAI7D,MAAMA,CAAA,EAAS;IACjB,OAAOJ,IAAI,CAACS,KAAK;EACnB;EAaA,KAAAuD,oBAAA,IAAmC;IACjC,OAAO,OAAO;EAChB;EACAnB,QAAQA,CAAA,EAAW;IACjB,eAAAM,MAAA,CAAe,IAAI,CAACc,SAAS;EAC/B;AACF;AAEA,OAAO,MAAMC,OAAO,SAASzD,KAAK,CAAC;EACjCsC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAACa,SAAS,CAACC,IAAI,CAAC;EACvB;AACF;AACA,OAAO,MAAMM,OAAO,SAAS1D,KAAK,CAAC;EACjCsC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAACa,SAAS,CAACE,MAAM,CAAC;EACzB;AACF;AACA,OAAO,MAAMM,OAAO,SAAS3D,KAAK,CAAC;EACjCsC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAACa,SAAS,CAACG,MAAM,CAAC;EACzB;AACF;AAEA,OAAO,MAAMpD,MAAM,SAASV,QAAQ,CAAC;EACnC8C,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;EACT;EACA,IAAI3C,MAAMA,CAAA,EAAG;IACX,OAAOJ,IAAI,CAACW,MAAM;EACpB;EACAkC,QAAQA,CAAA,EAAG;IACT,OAAO,QAAQ;EACjB;EACA,KAAKF,MAAM,CAACC,WAAW,IAAI;IACzB,OAAO,QAAQ;EACjB;AACF;AAIA,OAAO,MAAM/B,IAAI,SAASZ,QAAQ,CAAC;EACjC,IAAIG,MAAMA,CAAA,EAAS;IACjB,OAAOJ,IAAI,CAACa,IAAI;EAClB;EAIA,KAAK8B,MAAM,CAACC,WAAW,IAAY;IACjC,OAAO,MAAM;EACf;EACAC,QAAQA,CAAA,EAAW;IACjB,OAAO,MAAM;EACf;AACF;AAIA,MAAMwB,QAAQ,GAAG;EACfC,GAAG,EAAE,CAAC;EACNC,WAAW,EAAE;AACf,CAAC;AAACC,oBAAA,GAcK7B,MAAM,CAACC,WAAW;AAZzB,OAAO,MAAMzB,IAAI,SAASlB,QAAQ,CAAC;EAEjC8C,WAAWA,CAAC0B,IAAI,EAAE;IAChB,KAAK,CAAC,CAAC;IAACvB,eAAA;IACR,IAAI,CAACuB,IAAI,GAAGA,IAAI;EAClB;EACA,IAAIrE,MAAMA,CAAA,EAAS;IACjB,OAAOJ,IAAI,CAACmB,IAAI;EAClB;EAIA,KAAAqD,oBAAA,IAAmC;IACjC,OAAO,MAAM;EACf;EACA3B,QAAQA,CAAA,EAAW;IACjB,cAAAM,MAAA,CAAc,CAAC,IAAI,CAACsB,IAAI,GAAG,CAAC,IAAI,EAAE,OAAAtB,MAAA,CAAIkB,QAAQ,CAAC,IAAI,CAACI,IAAI,CAAC;EAC3D;AACF;AAEA,OAAO,MAAMC,OAAO,SAASvD,IAAI,CAAC;EAChC4B,WAAWA,CAAA,EAAG;IACZ,KAAK,CAACsB,QAAQ,CAACC,GAAG,CAAC;EACrB;AACF;AACA,OAAO,MAAMK,eAAe,SAASxD,IAAI,CAAC;EACxC4B,WAAWA,CAAA,EAAG;IACZ,KAAK,CAACsB,QAAQ,CAACE,WAAW,CAAC;EAC7B;AACF;AAEA,MAAMK,QAAQ,GAAG;EACfC,MAAM,EAAE,CAAC;EACTN,WAAW,EAAE,GAAG;EAChBO,WAAW,EAAE,GAAG;EAChBC,UAAU,EAAE;AACd,CAAC;AAACC,oBAAA,GAiBKrC,MAAM,CAACC,WAAW;AAfzB,OAAO,MAAMvB,IAAI,SAASpB,QAAQ,CAAC;EAIjC8C,WAAWA,CAAC0B,IAAS,EAAExB,QAAgB,EAAE;IACvC,KAAK,CAAC,CAAC;IAACC,eAAA;IAAAA,eAAA;IACR,IAAI,CAACuB,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACxB,QAAQ,GAAGA,QAAQ;EAC1B;EACA,IAAI7C,MAAMA,CAAA,EAAS;IACjB,OAAOJ,IAAI,CAACqB,IAAI;EAClB;EACAwB,QAAQA,CAAA,EAAW;IACjB,cAAAM,MAAA,CAAc,IAAI,CAACF,QAAQ,OAAAE,MAAA,CAAIyB,QAAQ,CAAC,IAAI,CAACH,IAAI,CAAC;EACpD;EACA,KAAAO,oBAAA,IAAmC;IACjC,OAAO,MAAM;EACf;AAIF;AAEA,OAAO,MAAMC,UAAU,SAAS5D,IAAI,CAAC;EACnC0B,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC6B,QAAQ,CAACC,MAAM,EAAE,EAAE,CAAC;EAC5B;AACF;AACA,OAAO,MAAMK,eAAe,SAAS7D,IAAI,CAAC;EACxC0B,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC6B,QAAQ,CAACL,WAAW,EAAE,EAAE,CAAC;EACjC;AACF;AAACY,oBAAA,GAmBMxC,MAAM,CAACC,WAAW;AAfzB,OAAO,MAAMrB,SAAS,SAAStB,QAAQ,CAAC;EAItC8C,WAAWA,CAAC0B,IAAS,EAAmB;IAAA,IAAjBW,QAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IACpC,KAAK,CAAC,CAAC;IAACnC,eAAA;IAAAA,eAAA;IACR,IAAI,CAACuB,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACW,QAAQ,GAAGA,QAAQ;EAC1B;EACA,IAAIhF,MAAMA,CAAA,EAAS;IACjB,OAAOJ,IAAI,CAACuB,SAAS;EACvB;EAIA,KAAA4D,oBAAA,IAAmC;IACjC,OAAO,WAAW;EACpB;EACAtC,QAAQA,CAAA,EAAW;IACjB,oBAAAM,MAAA,CAAoByB,QAAQ,CAAC,IAAI,CAACH,IAAI,CAAC,EAAAtB,MAAA,CAAG,IAAI,CAACiC,QAAQ,QAAAjC,MAAA,CAAQ,IAAI,CAACiC,QAAQ,IAAK,EAAE;EACrF;AACF;AAEA,OAAO,MAAMI,eAAe,SAASjE,SAAS,CAAC;EAC7CwB,WAAWA,CAAA,EAAkB;IAAA,IAAjBqC,QAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IACzB,KAAK,CAACT,QAAQ,CAACC,MAAM,EAAEO,QAAQ,CAAC;EAClC;AACF;AACA,OAAO,MAAMK,oBAAoB,SAASlE,SAAS,CAAC;EAClDwB,WAAWA,CAAA,EAAkB;IAAA,IAAjBqC,QAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IACzB,KAAK,CAACT,QAAQ,CAACL,WAAW,EAAEa,QAAQ,CAAC;EACvC;AACF;AACA,OAAO,MAAMM,oBAAoB,SAASnE,SAAS,CAAC;EAClDwB,WAAWA,CAAA,EAAkB;IAAA,IAAjBqC,QAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IACzB,KAAK,CAACT,QAAQ,CAACE,WAAW,EAAEM,QAAQ,CAAC;EACvC;AACF;AACA,OAAO,MAAMO,mBAAmB,SAASpE,SAAS,CAAC;EACjDwB,WAAWA,CAAA,EAAkB;IAAA,IAAjBqC,QAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IACzB,KAAK,CAACT,QAAQ,CAACG,UAAU,EAAEK,QAAQ,CAAC;EACtC;AACF;AAEA,MAAMQ,YAAY,GAAG;EACnBC,QAAQ,EAAE,CAAC;EACXC,UAAU,EAAE;AACd,CAAC;AAACC,oBAAA,GAcKpD,MAAM,CAACC,WAAW;AAZzB,OAAO,MAAMnB,QAAQ,SAASxB,QAAQ,CAAC;EAErC8C,WAAWA,CAAC0B,IAAY,EAAE;IACxB,KAAK,CAAC,CAAC;IAACvB,eAAA;IACR,IAAI,CAACuB,IAAI,GAAGA,IAAI;EAClB;EACA,IAAIrE,MAAMA,CAAA,EAAS;IACjB,OAAOJ,IAAI,CAACyB,QAAQ;EACtB;EAIA,KAAAsE,oBAAA,IAAmC;IACjC,OAAO,UAAU;EACnB;EACAlD,QAAQA,CAAA,EAAW;IACjB,mBAAAM,MAAA,CAAmByC,YAAY,CAAC,IAAI,CAACnB,IAAI,CAAC;EAC5C;AACF;AAEA,OAAO,MAAMuB,eAAe,SAASvE,QAAQ,CAAC;EAC5CsB,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC6C,YAAY,CAACC,QAAQ,CAAC;EAC9B;AACF;AACA,OAAO,MAAMI,iBAAiB,SAASxE,QAAQ,CAAC;EAC9CsB,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC6C,YAAY,CAACE,UAAU,CAAC;EAChC;AACF;AAACI,oBAAA,GAuBMvD,MAAM,CAACC,WAAW;AArBzB,OAAO,MAAMT,aAAa,SAASlC,QAAQ,CAAC;EAI1C8C,WAAWA,CAACoD,QAAgB,EAAEC,KAAY,EAAE;IAC1C,KAAK,CAAC,CAAC;IAAClD,eAAA;IAAAA,eAAA;IACR,IAAI,CAACiD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,QAAQ,GAAG,CAACD,KAAK,CAAC;EACzB;EACA,IAAIhG,MAAMA,CAAA,EAAS;IACjB,OAAOJ,IAAI,CAACmC,aAAa;EAC3B;EACA,IAAImE,SAASA,CAAA,EAAG;IACd,OAAO,IAAI,CAACD,QAAQ,CAAC,CAAC,CAAC,CAACE,IAAI;EAC9B;EACA,IAAIC,UAAUA,CAAA,EAAG;IACf,OAAO,IAAI,CAACH,QAAQ,CAAC,CAAC,CAAC;EACzB;EAIA,KAAAH,oBAAA,IAAmC;IACjC,OAAO,eAAe;EACxB;EACArD,QAAQA,CAAA,EAAW;IACjB,wBAAAM,MAAA,CAAwB,IAAI,CAACgD,QAAQ,QAAAhD,MAAA,CAAK,IAAI,CAACmD,SAAS;EAC1D;AACF;AAACG,oBAAA,GAgBM9D,MAAM,CAACC,WAAW;AAdzB,OAAO,MAAMf,MAAM,SAAS5B,QAAQ,CAAC;EAGnC8C,WAAWA,CAACsD,QAAiB,EAAE;IAC7B,KAAK,CAAC,CAAC;IAACnD,eAAA;IACR,IAAI,CAACmD,QAAQ,GAAGA,QAAQ;EAC1B;EAEA,IAAWjG,MAAMA,CAAA,EAAG;IAClB,OAAOJ,IAAI,CAAC6B,MAAM;EACpB;EACOgB,QAAQA,CAAA,EAAG;IAChB,kBAAAM,MAAA,CAAkB,IAAI,CAACkD,QAAQ,CAACK,GAAG,CAAEC,CAAC,OAAAxD,MAAA,CAAQwD,CAAC,CAACC,IAAI,OAAAzD,MAAA,CAAIwD,CAAC,CAACJ,IAAI,CAAE,CAAC,CAACM,IAAI,CAAC,IAAI,CAAC;EAC9E;EACA,KAAAJ,oBAAA,IAAmC;IACjC,OAAO,QAAQ;EACjB;AACF"}