{"version": 3, "file": "assert.js", "names": ["assert", "condition", "message", "Error"], "sources": ["../../../../src/lib/utils/assert.ts"], "sourcesContent": ["// Replacement for the external assert method to reduce bundle size\n// Note: We don't use the second \"message\" argument in calling code,\n// so no need to support it here\nexport function assert(condition: boolean, message?: string) {\n  if (!condition) {\n    throw new Error(message || 'loader assertion failed.');\n  }\n}\n"], "mappings": "AAGA,OAAO,SAASA,MAAMA,CAACC,SAAkB,EAAEC,OAAgB,EAAE;EAC3D,IAAI,CAACD,SAAS,EAAE;IACd,MAAM,IAAIE,KAAK,CAACD,OAAO,IAAI,0BAA0B,CAAC;EACxD;AACF"}