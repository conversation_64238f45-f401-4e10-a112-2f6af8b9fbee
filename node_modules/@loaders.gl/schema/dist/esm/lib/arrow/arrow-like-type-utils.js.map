{"version": 3, "file": "arrow-like-type-utils.js", "names": ["Float32", "Float64", "Int16", "Int32", "Int8", "Uint16", "Uint32", "Uint8", "getArrowTypeFromTypedArray", "array", "constructor", "Int8Array", "Uint8Array", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "Error"], "sources": ["../../../../src/lib/arrow/arrow-like-type-utils.ts"], "sourcesContent": ["import type {TypedArray} from '../../types';\nimport {\n  DataType,\n  Float32,\n  Float64,\n  Int16,\n  Int32,\n  Int8,\n  Uint16,\n  Uint32,\n  Uint8\n} from '../schema/schema';\n\nexport function getArrowTypeFromTypedArray(array: TypedArray): DataType {\n  switch (array.constructor) {\n    case Int8Array:\n      return new Int8();\n    case Uint8Array:\n      return new Uint8();\n    case Int16Array:\n      return new Int16();\n    case Uint16Array:\n      return new Uint16();\n    case Int32Array:\n      return new Int32();\n    case Uint32Array:\n      return new Uint32();\n    case Float32Array:\n      return new Float32();\n    case Float64Array:\n      return new Float64();\n    default:\n      throw new Error('array type not supported');\n  }\n}\n"], "mappings": "AACA,SAEEA,OAAO,EACPC,OAAO,EACPC,KAAK,EACLC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,MAAM,EACNC,KAAK,QACA,kBAAkB;AAEzB,OAAO,SAASC,0BAA0BA,CAACC,KAAiB,EAAY;EACtE,QAAQA,KAAK,CAACC,WAAW;IACvB,KAAKC,SAAS;MACZ,OAAO,IAAIP,IAAI,CAAC,CAAC;IACnB,KAAKQ,UAAU;MACb,OAAO,IAAIL,KAAK,CAAC,CAAC;IACpB,KAAKM,UAAU;MACb,OAAO,IAAIX,KAAK,CAAC,CAAC;IACpB,KAAKY,WAAW;MACd,OAAO,IAAIT,MAAM,CAAC,CAAC;IACrB,KAAKU,UAAU;MACb,OAAO,IAAIZ,KAAK,CAAC,CAAC;IACpB,KAAKa,WAAW;MACd,OAAO,IAAIV,MAAM,CAAC,CAAC;IACrB,KAAKW,YAAY;MACf,OAAO,IAAIjB,OAAO,CAAC,CAAC;IACtB,KAAKkB,YAAY;MACf,OAAO,IAAIjB,OAAO,CAAC,CAAC;IACtB;MACE,MAAM,IAAIkB,KAAK,CAAC,0BAA0B,CAAC;EAC/C;AACF"}