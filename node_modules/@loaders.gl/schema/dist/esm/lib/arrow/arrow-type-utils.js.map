{"version": 3, "file": "arrow-type-utils.js", "names": ["Float32", "Float64", "Int16", "Int32", "Int8", "Uint16", "Uint32", "Uint8", "Int8Vector", "Uint8Vector", "Int16Vector", "Uint16Vector", "Int32Vector", "Uint32Vector", "Float32Vector", "Float64Vector", "getArrowType", "array", "constructor", "Int8Array", "Uint8Array", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "Error", "getArrowVector", "from"], "sources": ["../../../../src/lib/arrow/arrow-type-utils.ts"], "sourcesContent": ["import type {TypedArray} from '../../types';\nimport {\n  DataType,\n  Float32,\n  Float64,\n  Int16,\n  Int32,\n  Int8,\n  Uint16,\n  Uint32,\n  Uint8,\n  Int8Vector,\n  Uint8Vector,\n  Int16Vector,\n  Uint16Vector,\n  Int32Vector,\n  Uint32Vector,\n  Float32Vector,\n  Float64Vector\n} from 'apache-arrow/Arrow.dom';\nimport {AbstractVector} from 'apache-arrow/vector';\n\nexport function getArrowType(array: TypedArray): DataType {\n  switch (array.constructor) {\n    case Int8Array:\n      return new Int8();\n    case Uint8Array:\n      return new Uint8();\n    case Int16Array:\n      return new Int16();\n    case Uint16Array:\n      return new Uint16();\n    case Int32Array:\n      return new Int32();\n    case Uint32Array:\n      return new Uint32();\n    case Float32Array:\n      return new Float32();\n    case Float64Array:\n      return new Float64();\n    default:\n      throw new Error('array type not supported');\n  }\n}\n\nexport function getArrowVector(array: TypedArray): AbstractVector {\n  switch (array.constructor) {\n    case Int8Array:\n      return Int8Vector.from(array);\n    case Uint8Array:\n      return Uint8Vector.from(array);\n    case Int16Array:\n      return Int16Vector.from(array);\n    case Uint16Array:\n      return Uint16Vector.from(array);\n    case Int32Array:\n      return Int32Vector.from(array);\n    case Uint32Array:\n      return Uint32Vector.from(array);\n    case Float32Array:\n      return Float32Vector.from(array);\n    case Float64Array:\n      return Float64Vector.from(array);\n    default:\n      throw new Error('array type not supported');\n  }\n}\n"], "mappings": "AACA,SAEEA,OAAO,EACPC,OAAO,EACPC,KAAK,EACLC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,WAAW,EACXC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,YAAY,EACZC,aAAa,EACbC,aAAa,QACR,wBAAwB;AAG/B,OAAO,SAASC,YAAYA,CAACC,KAAiB,EAAY;EACxD,QAAQA,KAAK,CAACC,WAAW;IACvB,KAAKC,SAAS;MACZ,OAAO,IAAIf,IAAI,CAAC,CAAC;IACnB,KAAKgB,UAAU;MACb,OAAO,IAAIb,KAAK,CAAC,CAAC;IACpB,KAAKc,UAAU;MACb,OAAO,IAAInB,KAAK,CAAC,CAAC;IACpB,KAAKoB,WAAW;MACd,OAAO,IAAIjB,MAAM,CAAC,CAAC;IACrB,KAAKkB,UAAU;MACb,OAAO,IAAIpB,KAAK,CAAC,CAAC;IACpB,KAAKqB,WAAW;MACd,OAAO,IAAIlB,MAAM,CAAC,CAAC;IACrB,KAAKmB,YAAY;MACf,OAAO,IAAIzB,OAAO,CAAC,CAAC;IACtB,KAAK0B,YAAY;MACf,OAAO,IAAIzB,OAAO,CAAC,CAAC;IACtB;MACE,MAAM,IAAI0B,KAAK,CAAC,0BAA0B,CAAC;EAC/C;AACF;AAEA,OAAO,SAASC,cAAcA,CAACX,KAAiB,EAAkB;EAChE,QAAQA,KAAK,CAACC,WAAW;IACvB,KAAKC,SAAS;MACZ,OAAOX,UAAU,CAACqB,IAAI,CAACZ,KAAK,CAAC;IAC/B,KAAKG,UAAU;MACb,OAAOX,WAAW,CAACoB,IAAI,CAACZ,KAAK,CAAC;IAChC,KAAKI,UAAU;MACb,OAAOX,WAAW,CAACmB,IAAI,CAACZ,KAAK,CAAC;IAChC,KAAKK,WAAW;MACd,OAAOX,YAAY,CAACkB,IAAI,CAACZ,KAAK,CAAC;IACjC,KAAKM,UAAU;MACb,OAAOX,WAAW,CAACiB,IAAI,CAACZ,KAAK,CAAC;IAChC,KAAKO,WAAW;MACd,OAAOX,YAAY,CAACgB,IAAI,CAACZ,KAAK,CAAC;IACjC,KAAKQ,YAAY;MACf,OAAOX,aAAa,CAACe,IAAI,CAACZ,KAAK,CAAC;IAClC,KAAKS,YAAY;MACf,OAAOX,aAAa,CAACc,IAAI,CAACZ,KAAK,CAAC;IAClC;MACE,MAAM,IAAIU,KAAK,CAAC,0BAA0B,CAAC;EAC/C;AACF"}