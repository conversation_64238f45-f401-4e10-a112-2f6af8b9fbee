{"version": 3, "file": "get-type-info.js", "names": ["Type", "getTypeInfo", "arrowTypeLike", "typeId", "ArrayType", "typeName", "toString", "typeEnumName", "getTypeKey", "precision", "ReverseType", "typeKey", "key"], "sources": ["../../../../src/lib/arrow/get-type-info.ts"], "sourcesContent": ["import {Type} from '../schema/schema';\nimport {AnyArray} from '../../types';\n\n/**\n * Gets type information from an Arrow type object or \"mock\" Arrow type object\n * @param arrowTypeLike Arrow Type or type object of similar shape\n */\nexport function getTypeInfo(arrowTypeLike: any): {\n  typeId: Type;\n  ArrayType: AnyArray;\n  typeName: string;\n  typeEnumName?: string;\n  precision?: number;\n} {\n  return {\n    typeId: arrowTypeLike.typeId,\n    ArrayType: arrowTypeLike.ArrayType,\n    typeName: arrowTypeLike.toString(),\n    typeEnumName: getTypeKey(arrowTypeLike.typeId),\n    precision: arrowTypeLike.precision\n  };\n}\n\nlet ReverseType: {[key: string]: string} | null = null;\n\nfunction getTypeKey(typeKey) {\n  if (!ReverseType) {\n    ReverseType = {};\n    for (const key in Type) {\n      ReverseType[Type[key]] = key;\n    }\n  }\n\n  return ReverseType[typeKey];\n}\n"], "mappings": "AAAA,SAAQA,IAAI,QAAO,kBAAkB;AAOrC,OAAO,SAASC,WAAWA,CAACC,aAAkB,EAM5C;EACA,OAAO;IACLC,MAAM,EAAED,aAAa,CAACC,MAAM;IAC5BC,SAAS,EAAEF,aAAa,CAACE,SAAS;IAClCC,QAAQ,EAAEH,aAAa,CAACI,QAAQ,CAAC,CAAC;IAClCC,YAAY,EAAEC,UAAU,CAACN,aAAa,CAACC,MAAM,CAAC;IAC9CM,SAAS,EAAEP,aAAa,CAACO;EAC3B,CAAC;AACH;AAEA,IAAIC,WAA2C,GAAG,IAAI;AAEtD,SAASF,UAAUA,CAACG,OAAO,EAAE;EAC3B,IAAI,CAACD,WAAW,EAAE;IAChBA,WAAW,GAAG,CAAC,CAAC;IAChB,KAAK,MAAME,GAAG,IAAIZ,IAAI,EAAE;MACtBU,WAAW,CAACV,IAAI,CAACY,GAAG,CAAC,CAAC,GAAGA,GAAG;IAC9B;EACF;EAEA,OAAOF,WAAW,CAACC,OAAO,CAAC;AAC7B"}