{"version": 3, "file": "deduce-column-type.js", "names": ["deduceTypeFromColumn", "value", "Date", "Number", "Float32Array", "String", "deduceTypeFromValue"], "sources": ["../../../../src/lib/schema-utils/deduce-column-type.ts"], "sourcesContent": ["// Type deduction\n// import {\n//   Schema\n//   Int,\n//   Int8,\n//   Int16,\n//   Int32,\n//   Uint8,\n//   Uint16,\n//   Uint32,\n//   Float32,\n//   Float64\n//   Bool,\n//   Utf8,\n//   TimestampMillisecond,\n//   Null\n// } from '../schema';\n\n// const TYPED_ARRAY_TO_TYPE = {\n//   Int8Array: new Int8(),\n//   Int16Array: new Int16(),\n//   Int32Array: new Int32(),\n//   Uint8Array: new Uint8(),\n//   Uint8ClampedArray: new Uint8(),\n//   Uint16Array: new Uint16(),\n//   Uint32Array: new Uint32(),\n//   Float32Array: new Float32(),\n//   Float64Array: new Float64()\n// };\n\n// if (typeof BigInt64Array !== 'undefined') {\n//   TYPED_ARRAY_TO_TYPE.BigInt64Array = new Int64();\n//   TYPED_ARRAY_TO_TYPE.BigUint64Array = new Uint64();\n// }\n\nexport function deduceTypeFromColumn(\n  value: unknown\n): StringConstructor | DateConstructor | Float32ArrayConstructor | null {\n  if (value instanceof Date) {\n    return Date;\n  } else if (value instanceof Number) {\n    return Float32Array;\n  } else if (typeof value === 'string') {\n    return String;\n  }\n  return null;\n}\n\nexport function deduceTypeFromValue(\n  value: unknown\n): StringConstructor | DateConstructor | Float32ArrayConstructor | null {\n  if (value instanceof Date) {\n    return Date;\n  } else if (value instanceof Number) {\n    return Float32Array;\n  } else if (typeof value === 'string') {\n    return String;\n  }\n  return null;\n}\n\n/*\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction deduceSchema(rows) {\n  const row = rows[0];\n\n  const schema = {};\n  let i = 0;\n  for (const columnName in row) {\n    const value = row[columnName];\n    switch (typeof value) {\n      case 'number':\n      case 'boolean':\n        // TODO - booleans could be handled differently...\n        schema[columnName] = {name: String(columnName), index: i, type: Float32Array};\n        break;\n\n      case 'object':\n        schema[columnName] = {name: String(columnName), index: i, type: Array};\n        break;\n\n      case 'string':\n      default:\n        schema[columnName] = {name: String(columnName), index: i, type: Array};\n      // We currently only handle numeric rows\n      // TODO we could offer a function to map strings to numbers?\n    }\n    i++;\n  }\n  return schema;\n}\n*/\n"], "mappings": "AAmCA,OAAO,SAASA,oBAAoBA,CAClCC,KAAc,EACwD;EACtE,IAAIA,KAAK,YAAYC,IAAI,EAAE;IACzB,OAAOA,IAAI;EACb,CAAC,MAAM,IAAID,KAAK,YAAYE,MAAM,EAAE;IAClC,OAAOC,YAAY;EACrB,CAAC,MAAM,IAAI,OAAOH,KAAK,KAAK,QAAQ,EAAE;IACpC,OAAOI,MAAM;EACf;EACA,OAAO,IAAI;AACb;AAEA,OAAO,SAASC,mBAAmBA,CACjCL,KAAc,EACwD;EACtE,IAAIA,KAAK,YAAYC,IAAI,EAAE;IACzB,OAAOA,IAAI;EACb,CAAC,MAAM,IAAID,KAAK,YAAYE,MAAM,EAAE;IAClC,OAAOC,YAAY;EACrB,CAAC,MAAM,IAAI,OAAOH,KAAK,KAAK,QAAQ,EAAE;IACpC,OAAOI,MAAM;EACf;EACA,OAAO,IAAI;AACb"}