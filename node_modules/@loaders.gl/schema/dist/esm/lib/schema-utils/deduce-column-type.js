export function deduceTypeFromColumn(value) {
  if (value instanceof Date) {
    return Date;
  } else if (value instanceof Number) {
    return Float32Array;
  } else if (typeof value === 'string') {
    return String;
  }
  return null;
}
export function deduceTypeFromValue(value) {
  if (value instanceof Date) {
    return Date;
  } else if (value instanceof Number) {
    return Float32Array;
  } else if (typeof value === 'string') {
    return String;
  }
  return null;
}
//# sourceMappingURL=deduce-column-type.js.map