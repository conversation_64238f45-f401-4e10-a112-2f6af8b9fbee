export type { TypedArray, <PERSON>Array, AnyArray } from './types';
export type { Batch } from './category/common';
export type { Table, ArrayRowTable, ObjectRowTable, GeoJSONRowTable, ColumnarTable, ArrowTable, Tables } from './category/table/table-types';
export type { TableBatch, RowArrayTableBatch, RowObjectTableBatch, GeoJSONRowTableBatch, ColumnarTableBatch, ArrowTableBatch } from './category/table/table-types';
export { default as TableBatchBuilder } from './lib/batches/table-batch-builder';
export type { TableBatchAggregator } from './lib/batches/table-batch-aggregator';
export { default as RowTableBatchAggregator } from './lib/batches/row-table-batch-aggregator';
export { default as ColumnarTableBatchAggregator } from './lib/batches/columnar-table-batch-aggregator';
export { convertToObjectRow, convertToArrayRow } from './lib/utils/row-utils';
export type { MeshTable, MeshArrowTable, Mesh, MeshGeometry, MeshAttribute, MeshAttributes } from './category/mesh/mesh-types';
export { getMeshSize, getMeshBoundingBox } from './category/mesh/mesh-utils';
export { deduceMeshSchema, deduceMeshField, makeMeshAttributeMetadata } from './category/mesh/deduce-mesh-schema';
export type { TextureLevel, GPUTextureFormat } from './category/texture/texture';
export type { ImageDataType, ImageType, ImageTypeEnum } from './category/image/image';
export type { GeoJSON, Feature, FeatureCollection, Geometry, Position, GeoJsonProperties, Point, MultiPoint, LineString, MultiLineString, Polygon, MultiPolygon, GeometryCollection } from './category/gis';
export type { GeojsonGeometryInfo } from './category/gis';
export type { FlatFeature, FlatIndexedGeometry, FlatGeometry, FlatGeometryType, FlatPoint, FlatLineString, FlatPolygon } from './category/gis';
export type { BinaryGeometryType, BinaryGeometry, BinaryPointGeometry, BinaryLineGeometry, BinaryPolygonGeometry, BinaryAttribute } from './category/gis';
export type { BinaryFeatures, BinaryPointFeatures, BinaryLineFeatures, BinaryPolygonFeatures } from './category/gis';
export { Schema, Field, DataType, Null, Binary, Bool, Int, Int8, Int16, Int32, Int64, Uint8, Uint16, Uint32, Uint64, Float, Float16, Float32, Float64, Utf8, Date, DateDay, DateMillisecond, Time, TimeMillisecond, TimeSecond, Timestamp, TimestampSecond, TimestampMillisecond, TimestampMicrosecond, TimestampNanosecond, Interval, IntervalDayTime, IntervalYearMonth, FixedSizeList, Struct } from './lib/schema/schema';
export { deduceTypeFromColumn, deduceTypeFromValue } from './lib/schema-utils/deduce-column-type';
export { getTypeInfo } from './lib/arrow/get-type-info';
export { getArrowTypeFromTypedArray } from './lib/arrow/arrow-like-type-utils';
export { default as AsyncQueue } from './lib/utils/async-queue';
//# sourceMappingURL=index.d.ts.map