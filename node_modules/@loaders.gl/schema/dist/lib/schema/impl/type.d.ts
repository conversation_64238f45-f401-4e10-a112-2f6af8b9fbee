import { Type } from './enum';
import Field from './field';
export { Type } from './enum';
export type TypedIntArray = Int8Array | Uint8Array | Int16Array | Uint16Array | Int32Array | Uint32Array | Int32Array | Uint32Array | Uint8ClampedArray;
export type TypedFloatArray = Float32Array | Float64Array;
export type TypedArray = TypedIntArray | TypedFloatArray;
export type AnyArrayType = Array<any> | TypedIntArray | TypedFloatArray;
export declare class DataType {
    static isNull(x: any): boolean;
    static isInt(x: any): boolean;
    static isFloat(x: any): boolean;
    static isBinary(x: any): boolean;
    static isUtf8(x: any): boolean;
    static isBool(x: any): boolean;
    static isDecimal(x: any): boolean;
    static isDate(x: any): boolean;
    static isTime(x: any): boolean;
    static isTimestamp(x: any): boolean;
    static isInterval(x: any): boolean;
    static isList(x: any): boolean;
    static isStruct(x: any): boolean;
    static isUnion(x: any): boolean;
    static isFixedSizeBinary(x: any): boolean;
    static isFixedSizeList(x: any): boolean;
    static isMap(x: any): boolean;
    static isDictionary(x: any): boolean;
    get typeId(): Type;
    compareTo(other: DataType): boolean;
}
export declare class Null extends DataType {
    get typeId(): Type;
    get [Symbol.toStringTag](): string;
    toString(): string;
}
export declare class Bool extends DataType {
    get typeId(): Type;
    get [Symbol.toStringTag](): string;
    toString(): string;
}
export declare class Int extends DataType {
    readonly isSigned: boolean;
    readonly bitWidth: number;
    constructor(isSigned: any, bitWidth: any);
    get typeId(): Type;
    get [Symbol.toStringTag](): string;
    toString(): string;
}
export declare class Int8 extends Int {
    constructor();
}
export declare class Int16 extends Int {
    constructor();
}
export declare class Int32 extends Int {
    constructor();
}
export declare class Int64 extends Int {
    constructor();
}
export declare class Uint8 extends Int {
    constructor();
}
export declare class Uint16 extends Int {
    constructor();
}
export declare class Uint32 extends Int {
    constructor();
}
export declare class Uint64 extends Int {
    constructor();
}
export declare class Float extends DataType {
    readonly precision: number;
    constructor(precision: any);
    get typeId(): Type;
    get [Symbol.toStringTag](): string;
    toString(): string;
}
export declare class Float16 extends Float {
    constructor();
}
export declare class Float32 extends Float {
    constructor();
}
export declare class Float64 extends Float {
    constructor();
}
export declare class Binary extends DataType {
    constructor();
    get typeId(): Type;
    toString(): string;
    get [Symbol.toStringTag](): string;
}
export declare class Utf8 extends DataType {
    get typeId(): Type;
    get [Symbol.toStringTag](): string;
    toString(): string;
}
export declare class Date extends DataType {
    readonly unit: number;
    constructor(unit: any);
    get typeId(): Type;
    get [Symbol.toStringTag](): string;
    toString(): string;
}
export declare class DateDay extends Date {
    constructor();
}
export declare class DateMillisecond extends Date {
    constructor();
}
export declare class Time extends DataType {
    readonly unit: any;
    readonly bitWidth: number;
    constructor(unit: any, bitWidth: number);
    get typeId(): Type;
    toString(): string;
    get [Symbol.toStringTag](): string;
}
export declare class TimeSecond extends Time {
    constructor();
}
export declare class TimeMillisecond extends Time {
    constructor();
}
export declare class Timestamp extends DataType {
    readonly unit: any;
    readonly timezone: any;
    constructor(unit: any, timezone?: null);
    get typeId(): Type;
    get [Symbol.toStringTag](): string;
    toString(): string;
}
export declare class TimestampSecond extends Timestamp {
    constructor(timezone?: null);
}
export declare class TimestampMillisecond extends Timestamp {
    constructor(timezone?: null);
}
export declare class TimestampMicrosecond extends Timestamp {
    constructor(timezone?: null);
}
export declare class TimestampNanosecond extends Timestamp {
    constructor(timezone?: null);
}
export declare class Interval extends DataType {
    readonly unit: number;
    constructor(unit: number);
    get typeId(): Type;
    get [Symbol.toStringTag](): string;
    toString(): string;
}
export declare class IntervalDayTime extends Interval {
    constructor();
}
export declare class IntervalYearMonth extends Interval {
    constructor();
}
export declare class FixedSizeList extends DataType {
    readonly listSize: number;
    readonly children: Field[];
    constructor(listSize: number, child: Field);
    get typeId(): Type;
    get valueType(): DataType;
    get valueField(): Field;
    get [Symbol.toStringTag](): string;
    toString(): string;
}
export declare class Struct extends DataType {
    readonly children: Field[];
    constructor(children: Field[]);
    get typeId(): Type;
    toString(): string;
    get [Symbol.toStringTag](): string;
}
//# sourceMappingURL=type.d.ts.map