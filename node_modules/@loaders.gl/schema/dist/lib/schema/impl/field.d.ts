import { DataType } from './type';
/**
 * ArrowJS `Field` API-compatible class for row-based tables
 * https://loaders.gl/arrowjs/docs/api-reference/field
 * A field holds name, nullable, and metadata information about a table "column"
 * A Schema is essentially a list of fields
 */
export default class Field {
    name: string;
    type: DataType;
    nullable: boolean;
    metadata: Map<string, string>;
    constructor(name: string, type: DataType, nullable?: boolean, metadata?: Map<string, string>);
    get typeId(): number;
    clone(): Field;
    compareTo(other: this): boolean;
    toString(): string;
}
//# sourceMappingURL=field.d.ts.map