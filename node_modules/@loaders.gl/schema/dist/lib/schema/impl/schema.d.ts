import Field from './field';
export type SchemaMetadata = Map<string, any>;
/**
 * ArrowJS `Schema` API-compatible class for row-based tables (returned from `DataTable`)
 * https://loaders.gl/arrowjs/docs/api-reference/schema
 */
export default class Schema {
    fields: Field[];
    metadata: SchemaMetadata;
    constructor(fields: Field[], metadata?: SchemaMetadata);
    compareTo(other: Schema): boolean;
    select(...columnNames: string[]): Schema;
    selectAt(...columnIndices: number[]): Schema;
    assign(schemaOrFields: Schema | Field[]): Schema;
}
//# sourceMappingURL=schema.d.ts.map