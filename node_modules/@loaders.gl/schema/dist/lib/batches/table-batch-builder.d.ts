import type { Schema } from '../schema/schema';
import type { TableBatch } from '../../category/table/table-types';
import type { TableBatchConstructor } from './table-batch-aggregator';
type TableBatchBuilderOptions = {
    shape: 'row-table' | 'array-row-table' | 'object-row-table' | 'columnar-table' | 'arrow-table';
    batchSize?: number | 'auto';
    batchDebounceMs?: number;
    limit: number;
    _limitMB: number;
};
type GetBatchOptions = {
    bytesUsed?: number;
    [key: string]: any;
};
/** Incrementally builds batches from a stream of rows */
export default class TableBatchBuilder {
    schema: Schema;
    options: Required<TableBatchBuilderOptions>;
    private aggregator;
    private batchCount;
    private bytesUsed;
    private isChunkComplete;
    private lastBatchEmittedMs;
    private totalLength;
    private totalBytes;
    private rowBytes;
    static ArrowBatch?: TableBatchConstructor;
    constructor(schema: Schema, options?: TableBatchBuilderOptions);
    limitReached(): boolean;
    /** @deprecated Use addArrayRow or addObjectRow */
    addRow(row: any[] | {
        [columnName: string]: any;
    }): void;
    /** Add one row to the batch */
    protected addArrayRow(row: any[]): void;
    /** Add one row to the batch */
    protected addObjectRow(row: {
        [columnName: string]: any;
    }): void;
    /** Mark an incoming raw memory chunk has completed */
    chunkComplete(chunk: ArrayBuffer | string): void;
    getFullBatch(options?: GetBatchOptions): TableBatch | null;
    getFinalBatch(options?: GetBatchOptions): TableBatch | null;
    _estimateRowMB(row: any[] | object): number;
    private _isFull;
    /**
     * bytesUsed can be set via chunkComplete or via getBatch*
     */
    private _getBatch;
    private _getTableBatchType;
}
export {};
//# sourceMappingURL=table-batch-builder.d.ts.map