import type { Schema } from '../schema/schema';
import type { TableBatch } from '../../category/table/table-types';
import { TableBatchAggregator, TableBatchOptions } from './table-batch-aggregator';
export default class RowTableBatchAggregator implements TableBatchAggregator {
    schema: Schema;
    options: TableBatchOptions;
    length: number;
    objectRows: {
        [columnName: string]: any;
    } | null;
    arrayRows: any[] | null;
    cursor: number;
    private _headers;
    constructor(schema: Schema, options: TableBatchOptions);
    rowCount(): number;
    addArrayRow(row: any[], cursor?: number): void;
    addObjectRow(row: {
        [columnName: string]: any;
    }, cursor?: number): void;
    getBatch(): TableBatch | null;
}
//# sourceMappingURL=row-table-batch-aggregator.d.ts.map