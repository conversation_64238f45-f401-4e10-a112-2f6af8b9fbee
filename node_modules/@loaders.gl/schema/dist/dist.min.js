(() => {
  var __defProp = Object.defineProperty;
  var __markAsModule = (target) => __defProp(target, "__esModule", { value: true });
  var __esm = (fn, res) => function __init() {
    return fn && (res = (0, fn[Object.keys(fn)[0]])(fn = 0)), res;
  };
  var __commonJS = (cb, mod) => function __require() {
    return mod || (0, cb[Object.keys(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
  };
  var __export = (target, all) => {
    __markAsModule(target);
    for (var name in all)
      __defProp(target, name, { get: all[name], enumerable: true });
  };

  // src/lib/batches/base-table-batch-aggregator.ts
  var DEFAULT_ROW_COUNT, RowTableBatchAggregator;
  var init_base_table_batch_aggregator = __esm({
    "src/lib/batches/base-table-batch-aggregator.ts"() {
      DEFAULT_ROW_COUNT = 100;
      RowTableBatchAggregator = class {
        constructor(schema, options) {
          this.length = 0;
          this.rows = null;
          this.cursor = 0;
          this._headers = [];
          this.options = options;
          this.schema = schema;
          if (!Array.isArray(schema)) {
            this._headers = [];
            for (const key in schema) {
              this._headers[schema[key].index] = schema[key].name;
            }
          }
        }
        rowCount() {
          return this.length;
        }
        addArrayRow(row, cursor) {
          if (Number.isFinite(cursor)) {
            this.cursor = cursor;
          }
          this.rows = this.rows || new Array(DEFAULT_ROW_COUNT);
          this.rows[this.length] = row;
          this.length++;
        }
        addObjectRow(row, cursor) {
          if (Number.isFinite(cursor)) {
            this.cursor = cursor;
          }
          this.rows = this.rows || new Array(DEFAULT_ROW_COUNT);
          this.rows[this.length] = row;
          this.length++;
        }
        getBatch() {
          let rows = this.rows;
          if (!rows) {
            return null;
          }
          rows = rows.slice(0, this.length);
          this.rows = null;
          const batch = {
            shape: this.options.shape,
            batchType: "data",
            data: rows,
            length: this.length,
            schema: this.schema,
            cursor: this.cursor
          };
          return batch;
        }
      };
    }
  });

  // src/lib/utils/row-utils.ts
  function convertToObjectRow(arrayRow, headers) {
    if (!arrayRow) {
      throw new Error("null row");
    }
    if (!headers) {
      throw new Error("no headers");
    }
    const objectRow = {};
    for (let i = 0; i < headers.length; i++) {
      objectRow[headers[i]] = arrayRow[i];
    }
    return objectRow;
  }
  function convertToArrayRow(objectRow, headers) {
    if (!objectRow) {
      throw new Error("null row");
    }
    if (!headers) {
      throw new Error("no headers");
    }
    const arrayRow = new Array(headers.length);
    for (let i = 0; i < headers.length; i++) {
      arrayRow[i] = objectRow[headers[i]];
    }
    return arrayRow;
  }
  var init_row_utils = __esm({
    "src/lib/utils/row-utils.ts"() {
    }
  });

  // src/lib/batches/row-table-batch-aggregator.ts
  var DEFAULT_ROW_COUNT2, RowTableBatchAggregator2;
  var init_row_table_batch_aggregator = __esm({
    "src/lib/batches/row-table-batch-aggregator.ts"() {
      init_row_utils();
      DEFAULT_ROW_COUNT2 = 100;
      RowTableBatchAggregator2 = class {
        constructor(schema, options) {
          this.length = 0;
          this.objectRows = null;
          this.arrayRows = null;
          this.cursor = 0;
          this._headers = [];
          this.options = options;
          this.schema = schema;
          if (!Array.isArray(schema)) {
            this._headers = [];
            for (const key in schema) {
              this._headers[schema[key].index] = schema[key].name;
            }
          }
        }
        rowCount() {
          return this.length;
        }
        addArrayRow(row, cursor) {
          if (Number.isFinite(cursor)) {
            this.cursor = cursor;
          }
          switch (this.options.shape) {
            case "object-row-table":
              const rowObject = convertToObjectRow(row, this._headers);
              this.addObjectRow(rowObject, cursor);
              break;
            case "array-row-table":
              this.arrayRows = this.arrayRows || new Array(DEFAULT_ROW_COUNT2);
              this.arrayRows[this.length] = row;
              this.length++;
              break;
          }
        }
        addObjectRow(row, cursor) {
          if (Number.isFinite(cursor)) {
            this.cursor = cursor;
          }
          switch (this.options.shape) {
            case "array-row-table":
              const rowArray = convertToArrayRow(row, this._headers);
              this.addArrayRow(rowArray, cursor);
              break;
            case "object-row-table":
              this.objectRows = this.objectRows || new Array(DEFAULT_ROW_COUNT2);
              this.objectRows[this.length] = row;
              this.length++;
              break;
          }
        }
        getBatch() {
          let rows = this.arrayRows || this.objectRows;
          if (!rows) {
            return null;
          }
          rows = rows.slice(0, this.length);
          this.arrayRows = null;
          this.objectRows = null;
          return {
            shape: this.options.shape,
            batchType: "data",
            data: rows,
            length: this.length,
            schema: this.schema,
            cursor: this.cursor
          };
        }
      };
    }
  });

  // src/lib/batches/columnar-table-batch-aggregator.ts
  var DEFAULT_ROW_COUNT3, ColumnarTableBatchAggregator;
  var init_columnar_table_batch_aggregator = __esm({
    "src/lib/batches/columnar-table-batch-aggregator.ts"() {
      DEFAULT_ROW_COUNT3 = 100;
      ColumnarTableBatchAggregator = class {
        constructor(schema, options) {
          this.length = 0;
          this.allocated = 0;
          this.columns = {};
          this.schema = schema;
          this._reallocateColumns();
        }
        rowCount() {
          return this.length;
        }
        addArrayRow(row) {
          this._reallocateColumns();
          let i = 0;
          for (const fieldName in this.columns) {
            this.columns[fieldName][this.length] = row[i++];
          }
          this.length++;
        }
        addObjectRow(row) {
          this._reallocateColumns();
          for (const fieldName in row) {
            this.columns[fieldName][this.length] = row[fieldName];
          }
          this.length++;
        }
        getBatch() {
          this._pruneColumns();
          const columns = Array.isArray(this.schema) ? this.columns : {};
          if (!Array.isArray(this.schema)) {
            for (const fieldName in this.schema) {
              const field = this.schema[fieldName];
              columns[field.name] = this.columns[field.index];
            }
          }
          this.columns = {};
          const batch = {
            shape: "columnar-table",
            batchType: "data",
            data: columns,
            schema: this.schema,
            length: this.length
          };
          return batch;
        }
        _reallocateColumns() {
          if (this.length < this.allocated) {
            return;
          }
          this.allocated = this.allocated > 0 ? this.allocated *= 2 : DEFAULT_ROW_COUNT3;
          this.columns = {};
          for (const fieldName in this.schema) {
            const field = this.schema[fieldName];
            const ArrayType = field.type || Float32Array;
            const oldColumn = this.columns[field.index];
            if (oldColumn && ArrayBuffer.isView(oldColumn)) {
              const typedArray = new ArrayType(this.allocated);
              typedArray.set(oldColumn);
              this.columns[field.index] = typedArray;
            } else if (oldColumn) {
              oldColumn.length = this.allocated;
              this.columns[field.index] = oldColumn;
            } else {
              this.columns[field.index] = new ArrayType(this.allocated);
            }
          }
        }
        _pruneColumns() {
          for (const [columnName, column] of Object.entries(this.columns)) {
            this.columns[columnName] = column.slice(0, this.length);
          }
        }
      };
    }
  });

  // src/lib/batches/table-batch-builder.ts
  var DEFAULT_OPTIONS, ERR_MESSAGE, TableBatchBuilder;
  var init_table_batch_builder = __esm({
    "src/lib/batches/table-batch-builder.ts"() {
      init_base_table_batch_aggregator();
      init_row_table_batch_aggregator();
      init_columnar_table_batch_aggregator();
      DEFAULT_OPTIONS = {
        shape: "array-row-table",
        batchSize: "auto",
        batchDebounceMs: 0,
        limit: 0,
        _limitMB: 0
      };
      ERR_MESSAGE = "TableBatchBuilder";
      TableBatchBuilder = class {
        constructor(schema, options) {
          this.aggregator = null;
          this.batchCount = 0;
          this.bytesUsed = 0;
          this.isChunkComplete = false;
          this.lastBatchEmittedMs = Date.now();
          this.totalLength = 0;
          this.totalBytes = 0;
          this.rowBytes = 0;
          this.schema = schema;
          this.options = { ...DEFAULT_OPTIONS, ...options };
        }
        limitReached() {
          if (Boolean(this.options?.limit) && this.totalLength >= this.options.limit) {
            return true;
          }
          if (Boolean(this.options?._limitMB) && this.totalBytes / 1e6 >= this.options._limitMB) {
            return true;
          }
          return false;
        }
        addRow(row) {
          if (this.limitReached()) {
            return;
          }
          this.totalLength++;
          this.rowBytes = this.rowBytes || this._estimateRowMB(row);
          this.totalBytes += this.rowBytes;
          if (Array.isArray(row)) {
            this.addArrayRow(row);
          } else {
            this.addObjectRow(row);
          }
        }
        addArrayRow(row) {
          if (!this.aggregator) {
            const TableBatchType = this._getTableBatchType();
            this.aggregator = new TableBatchType(this.schema, this.options);
          }
          this.aggregator.addArrayRow(row);
        }
        addObjectRow(row) {
          if (!this.aggregator) {
            const TableBatchType = this._getTableBatchType();
            this.aggregator = new TableBatchType(this.schema, this.options);
          }
          this.aggregator.addObjectRow(row);
        }
        chunkComplete(chunk) {
          if (chunk instanceof ArrayBuffer) {
            this.bytesUsed += chunk.byteLength;
          }
          if (typeof chunk === "string") {
            this.bytesUsed += chunk.length;
          }
          this.isChunkComplete = true;
        }
        getFullBatch(options) {
          return this._isFull() ? this._getBatch(options) : null;
        }
        getFinalBatch(options) {
          return this._getBatch(options);
        }
        _estimateRowMB(row) {
          return Array.isArray(row) ? row.length * 8 : Object.keys(row).length * 8;
        }
        _isFull() {
          if (!this.aggregator || this.aggregator.rowCount() === 0) {
            return false;
          }
          if (this.options.batchSize === "auto") {
            if (!this.isChunkComplete) {
              return false;
            }
          } else if (this.options.batchSize > this.aggregator.rowCount()) {
            return false;
          }
          if (this.options.batchDebounceMs > Date.now() - this.lastBatchEmittedMs) {
            return false;
          }
          this.isChunkComplete = false;
          this.lastBatchEmittedMs = Date.now();
          return true;
        }
        _getBatch(options) {
          if (!this.aggregator) {
            return null;
          }
          if (options?.bytesUsed) {
            this.bytesUsed = options.bytesUsed;
          }
          const normalizedBatch = this.aggregator.getBatch();
          normalizedBatch.count = this.batchCount;
          normalizedBatch.bytesUsed = this.bytesUsed;
          Object.assign(normalizedBatch, options);
          this.batchCount++;
          this.aggregator = null;
          return normalizedBatch;
        }
        _getTableBatchType() {
          switch (this.options.shape) {
            case "row-table":
              return RowTableBatchAggregator;
            case "array-row-table":
            case "object-row-table":
              return RowTableBatchAggregator2;
            case "columnar-table":
              return ColumnarTableBatchAggregator;
            case "arrow-table":
              if (!TableBatchBuilder.ArrowBatch) {
                throw new Error(ERR_MESSAGE);
              }
              return TableBatchBuilder.ArrowBatch;
            default:
              throw new Error(ERR_MESSAGE);
          }
        }
      };
    }
  });

  // src/category/mesh/mesh-utils.ts
  function getMeshSize(attributes) {
    let size = 0;
    for (const attributeName in attributes) {
      const attribute = attributes[attributeName];
      if (ArrayBuffer.isView(attribute)) {
        size += attribute.byteLength * attribute.BYTES_PER_ELEMENT;
      }
    }
    return size;
  }
  function getMeshBoundingBox(attributes) {
    let minX = Infinity;
    let minY = Infinity;
    let minZ = Infinity;
    let maxX = -Infinity;
    let maxY = -Infinity;
    let maxZ = -Infinity;
    const positions = attributes.POSITION ? attributes.POSITION.value : [];
    const len = positions && positions.length;
    for (let i = 0; i < len; i += 3) {
      const x = positions[i];
      const y = positions[i + 1];
      const z = positions[i + 2];
      minX = x < minX ? x : minX;
      minY = y < minY ? y : minY;
      minZ = z < minZ ? z : minZ;
      maxX = x > maxX ? x : maxX;
      maxY = y > maxY ? y : maxY;
      maxZ = z > maxZ ? z : maxZ;
    }
    return [
      [minX, minY, minZ],
      [maxX, maxY, maxZ]
    ];
  }
  var init_mesh_utils = __esm({
    "src/category/mesh/mesh-utils.ts"() {
    }
  });

  // src/lib/utils/assert.ts
  function assert(condition, message) {
    if (!condition) {
      throw new Error(message || "loader assertion failed.");
    }
  }
  var init_assert = __esm({
    "src/lib/utils/assert.ts"() {
    }
  });

  // src/lib/schema/impl/schema.ts
  function checkNames(fields) {
    const usedNames = {};
    for (const field of fields) {
      if (usedNames[field.name]) {
        console.warn("Schema: duplicated field name", field.name, field);
      }
      usedNames[field.name] = true;
    }
  }
  function mergeMaps(m1, m2) {
    return new Map([...m1 || new Map(), ...m2 || new Map()]);
  }
  var Schema;
  var init_schema = __esm({
    "src/lib/schema/impl/schema.ts"() {
      init_assert();
      Schema = class {
        constructor(fields, metadata) {
          assert(Array.isArray(fields));
          checkNames(fields);
          this.fields = fields;
          this.metadata = metadata || new Map();
        }
        compareTo(other) {
          if (this.metadata !== other.metadata) {
            return false;
          }
          if (this.fields.length !== other.fields.length) {
            return false;
          }
          for (let i = 0; i < this.fields.length; ++i) {
            if (!this.fields[i].compareTo(other.fields[i])) {
              return false;
            }
          }
          return true;
        }
        select(...columnNames) {
          const nameMap = Object.create(null);
          for (const name of columnNames) {
            nameMap[name] = true;
          }
          const selectedFields = this.fields.filter((field) => nameMap[field.name]);
          return new Schema(selectedFields, this.metadata);
        }
        selectAt(...columnIndices) {
          const selectedFields = columnIndices.map((index) => this.fields[index]).filter(Boolean);
          return new Schema(selectedFields, this.metadata);
        }
        assign(schemaOrFields) {
          let fields;
          let metadata = this.metadata;
          if (schemaOrFields instanceof Schema) {
            const otherSchema = schemaOrFields;
            fields = otherSchema.fields;
            metadata = mergeMaps(mergeMaps(new Map(), this.metadata), otherSchema.metadata);
          } else {
            fields = schemaOrFields;
          }
          const fieldMap = Object.create(null);
          for (const field of this.fields) {
            fieldMap[field.name] = field;
          }
          for (const field of fields) {
            fieldMap[field.name] = field;
          }
          const mergedFields = Object.values(fieldMap);
          return new Schema(mergedFields, metadata);
        }
      };
    }
  });

  // src/lib/schema/impl/field.ts
  var Field;
  var init_field = __esm({
    "src/lib/schema/impl/field.ts"() {
      Field = class {
        constructor(name, type, nullable = false, metadata = new Map()) {
          this.name = name;
          this.type = type;
          this.nullable = nullable;
          this.metadata = metadata;
        }
        get typeId() {
          return this.type && this.type.typeId;
        }
        clone() {
          return new Field(this.name, this.type, this.nullable, this.metadata);
        }
        compareTo(other) {
          return this.name === other.name && this.type === other.type && this.nullable === other.nullable && this.metadata === other.metadata;
        }
        toString() {
          return `${this.type}${this.nullable ? ", nullable" : ""}${this.metadata ? `, metadata: ${this.metadata}` : ""}`;
        }
      };
    }
  });

  // src/lib/schema/impl/enum.ts
  var Type;
  var init_enum = __esm({
    "src/lib/schema/impl/enum.ts"() {
      (function(Type2) {
        Type2[Type2["NONE"] = 0] = "NONE";
        Type2[Type2["Null"] = 1] = "Null";
        Type2[Type2["Int"] = 2] = "Int";
        Type2[Type2["Float"] = 3] = "Float";
        Type2[Type2["Binary"] = 4] = "Binary";
        Type2[Type2["Utf8"] = 5] = "Utf8";
        Type2[Type2["Bool"] = 6] = "Bool";
        Type2[Type2["Decimal"] = 7] = "Decimal";
        Type2[Type2["Date"] = 8] = "Date";
        Type2[Type2["Time"] = 9] = "Time";
        Type2[Type2["Timestamp"] = 10] = "Timestamp";
        Type2[Type2["Interval"] = 11] = "Interval";
        Type2[Type2["List"] = 12] = "List";
        Type2[Type2["Struct"] = 13] = "Struct";
        Type2[Type2["Union"] = 14] = "Union";
        Type2[Type2["FixedSizeBinary"] = 15] = "FixedSizeBinary";
        Type2[Type2["FixedSizeList"] = 16] = "FixedSizeList";
        Type2[Type2["Map"] = 17] = "Map";
        Type2[Type2["Dictionary"] = -1] = "Dictionary";
        Type2[Type2["Int8"] = -2] = "Int8";
        Type2[Type2["Int16"] = -3] = "Int16";
        Type2[Type2["Int32"] = -4] = "Int32";
        Type2[Type2["Int64"] = -5] = "Int64";
        Type2[Type2["Uint8"] = -6] = "Uint8";
        Type2[Type2["Uint16"] = -7] = "Uint16";
        Type2[Type2["Uint32"] = -8] = "Uint32";
        Type2[Type2["Uint64"] = -9] = "Uint64";
        Type2[Type2["Float16"] = -10] = "Float16";
        Type2[Type2["Float32"] = -11] = "Float32";
        Type2[Type2["Float64"] = -12] = "Float64";
        Type2[Type2["DateDay"] = -13] = "DateDay";
        Type2[Type2["DateMillisecond"] = -14] = "DateMillisecond";
        Type2[Type2["TimestampSecond"] = -15] = "TimestampSecond";
        Type2[Type2["TimestampMillisecond"] = -16] = "TimestampMillisecond";
        Type2[Type2["TimestampMicrosecond"] = -17] = "TimestampMicrosecond";
        Type2[Type2["TimestampNanosecond"] = -18] = "TimestampNanosecond";
        Type2[Type2["TimeSecond"] = -19] = "TimeSecond";
        Type2[Type2["TimeMillisecond"] = -20] = "TimeMillisecond";
        Type2[Type2["TimeMicrosecond"] = -21] = "TimeMicrosecond";
        Type2[Type2["TimeNanosecond"] = -22] = "TimeNanosecond";
        Type2[Type2["DenseUnion"] = -23] = "DenseUnion";
        Type2[Type2["SparseUnion"] = -24] = "SparseUnion";
        Type2[Type2["IntervalDayTime"] = -25] = "IntervalDayTime";
        Type2[Type2["IntervalYearMonth"] = -26] = "IntervalYearMonth";
      })(Type || (Type = {}));
    }
  });

  // src/lib/schema/impl/type.ts
  var DataType, Null, Bool, Int, Int8, Int16, Int32, Int64, Uint8, Uint16, Uint32, Uint64, Precision, Float, Float16, Float32, Float64, Binary, Utf8, DateUnit, Date2, DateDay, DateMillisecond, TimeUnit, Time, TimeSecond, TimeMillisecond, Timestamp, TimestampSecond, TimestampMillisecond, TimestampMicrosecond, TimestampNanosecond, IntervalUnit, Interval, IntervalDayTime, IntervalYearMonth, FixedSizeList, Struct;
  var init_type = __esm({
    "src/lib/schema/impl/type.ts"() {
      init_enum();
      init_enum();
      DataType = class {
        static isNull(x) {
          return x && x.typeId === Type.Null;
        }
        static isInt(x) {
          return x && x.typeId === Type.Int;
        }
        static isFloat(x) {
          return x && x.typeId === Type.Float;
        }
        static isBinary(x) {
          return x && x.typeId === Type.Binary;
        }
        static isUtf8(x) {
          return x && x.typeId === Type.Utf8;
        }
        static isBool(x) {
          return x && x.typeId === Type.Bool;
        }
        static isDecimal(x) {
          return x && x.typeId === Type.Decimal;
        }
        static isDate(x) {
          return x && x.typeId === Type.Date;
        }
        static isTime(x) {
          return x && x.typeId === Type.Time;
        }
        static isTimestamp(x) {
          return x && x.typeId === Type.Timestamp;
        }
        static isInterval(x) {
          return x && x.typeId === Type.Interval;
        }
        static isList(x) {
          return x && x.typeId === Type.List;
        }
        static isStruct(x) {
          return x && x.typeId === Type.Struct;
        }
        static isUnion(x) {
          return x && x.typeId === Type.Union;
        }
        static isFixedSizeBinary(x) {
          return x && x.typeId === Type.FixedSizeBinary;
        }
        static isFixedSizeList(x) {
          return x && x.typeId === Type.FixedSizeList;
        }
        static isMap(x) {
          return x && x.typeId === Type.Map;
        }
        static isDictionary(x) {
          return x && x.typeId === Type.Dictionary;
        }
        get typeId() {
          return Type.NONE;
        }
        compareTo(other) {
          return this === other;
        }
      };
      Null = class extends DataType {
        get typeId() {
          return Type.Null;
        }
        get [Symbol.toStringTag]() {
          return "Null";
        }
        toString() {
          return "Null";
        }
      };
      Bool = class extends DataType {
        get typeId() {
          return Type.Bool;
        }
        get [Symbol.toStringTag]() {
          return "Bool";
        }
        toString() {
          return "Bool";
        }
      };
      Int = class extends DataType {
        constructor(isSigned, bitWidth) {
          super();
          this.isSigned = isSigned;
          this.bitWidth = bitWidth;
        }
        get typeId() {
          return Type.Int;
        }
        get [Symbol.toStringTag]() {
          return "Int";
        }
        toString() {
          return `${this.isSigned ? "I" : "Ui"}nt${this.bitWidth}`;
        }
      };
      Int8 = class extends Int {
        constructor() {
          super(true, 8);
        }
      };
      Int16 = class extends Int {
        constructor() {
          super(true, 16);
        }
      };
      Int32 = class extends Int {
        constructor() {
          super(true, 32);
        }
      };
      Int64 = class extends Int {
        constructor() {
          super(true, 64);
        }
      };
      Uint8 = class extends Int {
        constructor() {
          super(false, 8);
        }
      };
      Uint16 = class extends Int {
        constructor() {
          super(false, 16);
        }
      };
      Uint32 = class extends Int {
        constructor() {
          super(false, 32);
        }
      };
      Uint64 = class extends Int {
        constructor() {
          super(false, 64);
        }
      };
      Precision = {
        HALF: 16,
        SINGLE: 32,
        DOUBLE: 64
      };
      Float = class extends DataType {
        constructor(precision) {
          super();
          this.precision = precision;
        }
        get typeId() {
          return Type.Float;
        }
        get [Symbol.toStringTag]() {
          return "Float";
        }
        toString() {
          return `Float${this.precision}`;
        }
      };
      Float16 = class extends Float {
        constructor() {
          super(Precision.HALF);
        }
      };
      Float32 = class extends Float {
        constructor() {
          super(Precision.SINGLE);
        }
      };
      Float64 = class extends Float {
        constructor() {
          super(Precision.DOUBLE);
        }
      };
      Binary = class extends DataType {
        constructor() {
          super();
        }
        get typeId() {
          return Type.Binary;
        }
        toString() {
          return "Binary";
        }
        get [Symbol.toStringTag]() {
          return "Binary";
        }
      };
      Utf8 = class extends DataType {
        get typeId() {
          return Type.Utf8;
        }
        get [Symbol.toStringTag]() {
          return "Utf8";
        }
        toString() {
          return "Utf8";
        }
      };
      DateUnit = {
        DAY: 0,
        MILLISECOND: 1
      };
      Date2 = class extends DataType {
        constructor(unit) {
          super();
          this.unit = unit;
        }
        get typeId() {
          return Type.Date;
        }
        get [Symbol.toStringTag]() {
          return "Date";
        }
        toString() {
          return `Date${(this.unit + 1) * 32}<${DateUnit[this.unit]}>`;
        }
      };
      DateDay = class extends Date2 {
        constructor() {
          super(DateUnit.DAY);
        }
      };
      DateMillisecond = class extends Date2 {
        constructor() {
          super(DateUnit.MILLISECOND);
        }
      };
      TimeUnit = {
        SECOND: 1,
        MILLISECOND: 1e3,
        MICROSECOND: 1e6,
        NANOSECOND: 1e9
      };
      Time = class extends DataType {
        constructor(unit, bitWidth) {
          super();
          this.unit = unit;
          this.bitWidth = bitWidth;
        }
        get typeId() {
          return Type.Time;
        }
        toString() {
          return `Time${this.bitWidth}<${TimeUnit[this.unit]}>`;
        }
        get [Symbol.toStringTag]() {
          return "Time";
        }
      };
      TimeSecond = class extends Time {
        constructor() {
          super(TimeUnit.SECOND, 32);
        }
      };
      TimeMillisecond = class extends Time {
        constructor() {
          super(TimeUnit.MILLISECOND, 32);
        }
      };
      Timestamp = class extends DataType {
        constructor(unit, timezone = null) {
          super();
          this.unit = unit;
          this.timezone = timezone;
        }
        get typeId() {
          return Type.Timestamp;
        }
        get [Symbol.toStringTag]() {
          return "Timestamp";
        }
        toString() {
          return `Timestamp<${TimeUnit[this.unit]}${this.timezone ? `, ${this.timezone}` : ""}>`;
        }
      };
      TimestampSecond = class extends Timestamp {
        constructor(timezone = null) {
          super(TimeUnit.SECOND, timezone);
        }
      };
      TimestampMillisecond = class extends Timestamp {
        constructor(timezone = null) {
          super(TimeUnit.MILLISECOND, timezone);
        }
      };
      TimestampMicrosecond = class extends Timestamp {
        constructor(timezone = null) {
          super(TimeUnit.MICROSECOND, timezone);
        }
      };
      TimestampNanosecond = class extends Timestamp {
        constructor(timezone = null) {
          super(TimeUnit.NANOSECOND, timezone);
        }
      };
      IntervalUnit = {
        DAY_TIME: 0,
        YEAR_MONTH: 1
      };
      Interval = class extends DataType {
        constructor(unit) {
          super();
          this.unit = unit;
        }
        get typeId() {
          return Type.Interval;
        }
        get [Symbol.toStringTag]() {
          return "Interval";
        }
        toString() {
          return `Interval<${IntervalUnit[this.unit]}>`;
        }
      };
      IntervalDayTime = class extends Interval {
        constructor() {
          super(IntervalUnit.DAY_TIME);
        }
      };
      IntervalYearMonth = class extends Interval {
        constructor() {
          super(IntervalUnit.YEAR_MONTH);
        }
      };
      FixedSizeList = class extends DataType {
        constructor(listSize, child) {
          super();
          this.listSize = listSize;
          this.children = [child];
        }
        get typeId() {
          return Type.FixedSizeList;
        }
        get valueType() {
          return this.children[0].type;
        }
        get valueField() {
          return this.children[0];
        }
        get [Symbol.toStringTag]() {
          return "FixedSizeList";
        }
        toString() {
          return `FixedSizeList[${this.listSize}]<${this.valueType}>`;
        }
      };
      Struct = class extends DataType {
        constructor(children) {
          super();
          this.children = children;
        }
        get typeId() {
          return Type.Struct;
        }
        toString() {
          return `Struct<{${this.children.map((f) => `${f.name}:${f.type}`).join(", ")}}>`;
        }
        get [Symbol.toStringTag]() {
          return "Struct";
        }
      };
    }
  });

  // src/lib/schema/schema.ts
  var init_schema2 = __esm({
    "src/lib/schema/schema.ts"() {
      init_schema();
      init_field();
      init_type();
      init_type();
    }
  });

  // src/lib/arrow/arrow-like-type-utils.ts
  function getArrowTypeFromTypedArray(array) {
    switch (array.constructor) {
      case Int8Array:
        return new Int8();
      case Uint8Array:
        return new Uint8();
      case Int16Array:
        return new Int16();
      case Uint16Array:
        return new Uint16();
      case Int32Array:
        return new Int32();
      case Uint32Array:
        return new Uint32();
      case Float32Array:
        return new Float32();
      case Float64Array:
        return new Float64();
      default:
        throw new Error("array type not supported");
    }
  }
  var init_arrow_like_type_utils = __esm({
    "src/lib/arrow/arrow-like-type-utils.ts"() {
      init_schema2();
    }
  });

  // src/category/mesh/deduce-mesh-schema.ts
  function deduceMeshSchema(attributes, metadata) {
    const fields = deduceMeshFields(attributes);
    return new Schema(fields, metadata);
  }
  function deduceMeshField(attributeName, attribute, optionalMetadata) {
    const type = getArrowTypeFromTypedArray(attribute.value);
    const metadata = optionalMetadata ? optionalMetadata : makeMeshAttributeMetadata(attribute);
    const field = new Field(attributeName, new FixedSizeList(attribute.size, new Field("value", type)), false, metadata);
    return field;
  }
  function deduceMeshFields(attributes) {
    const fields = [];
    for (const attributeName in attributes) {
      const attribute = attributes[attributeName];
      fields.push(deduceMeshField(attributeName, attribute));
    }
    return fields;
  }
  function makeMeshAttributeMetadata(attribute) {
    const result = new Map();
    if ("byteOffset" in attribute) {
      result.set("byteOffset", attribute.byteOffset.toString(10));
    }
    if ("byteStride" in attribute) {
      result.set("byteStride", attribute.byteStride.toString(10));
    }
    if ("normalized" in attribute) {
      result.set("normalized", attribute.normalized.toString());
    }
    return result;
  }
  var init_deduce_mesh_schema = __esm({
    "src/category/mesh/deduce-mesh-schema.ts"() {
      init_schema2();
      init_arrow_like_type_utils();
    }
  });

  // src/lib/schema-utils/deduce-column-type.ts
  function deduceTypeFromColumn(value) {
    if (value instanceof Date) {
      return Date;
    } else if (value instanceof Number) {
      return Float32Array;
    } else if (typeof value === "string") {
      return String;
    }
    return null;
  }
  function deduceTypeFromValue(value) {
    if (value instanceof Date) {
      return Date;
    } else if (value instanceof Number) {
      return Float32Array;
    } else if (typeof value === "string") {
      return String;
    }
    return null;
  }
  var init_deduce_column_type = __esm({
    "src/lib/schema-utils/deduce-column-type.ts"() {
    }
  });

  // src/lib/arrow/get-type-info.ts
  function getTypeInfo(arrowTypeLike) {
    return {
      typeId: arrowTypeLike.typeId,
      ArrayType: arrowTypeLike.ArrayType,
      typeName: arrowTypeLike.toString(),
      typeEnumName: getTypeKey(arrowTypeLike.typeId),
      precision: arrowTypeLike.precision
    };
  }
  function getTypeKey(typeKey) {
    if (!ReverseType) {
      ReverseType = {};
      for (const key in Type) {
        ReverseType[Type[key]] = key;
      }
    }
    return ReverseType[typeKey];
  }
  var ReverseType;
  var init_get_type_info = __esm({
    "src/lib/arrow/get-type-info.ts"() {
      init_schema2();
      ReverseType = null;
    }
  });

  // src/lib/utils/async-queue.ts
  var ArrayQueue, AsyncQueue;
  var init_async_queue = __esm({
    "src/lib/utils/async-queue.ts"() {
      ArrayQueue = class extends Array {
        enqueue(value) {
          return this.push(value);
        }
        dequeue() {
          return this.shift();
        }
      };
      AsyncQueue = class {
        constructor() {
          this._values = new ArrayQueue();
          this._settlers = new ArrayQueue();
          this._closed = false;
        }
        close() {
          while (this._settlers.length > 0) {
            this._settlers.dequeue().resolve({ done: true });
          }
          this._closed = true;
        }
        [Symbol.asyncIterator]() {
          return this;
        }
        enqueue(value) {
          if (this._closed) {
            throw new Error("Closed");
          }
          if (this._settlers.length > 0) {
            if (this._values.length > 0) {
              throw new Error("Illegal internal state");
            }
            const settler = this._settlers.dequeue();
            if (value instanceof Error) {
              settler.reject(value);
            } else {
              settler.resolve({ value });
            }
          } else {
            this._values.enqueue(value);
          }
        }
        next() {
          if (this._values.length > 0) {
            const value = this._values.dequeue();
            if (value instanceof Error) {
              return Promise.reject(value);
            }
            return Promise.resolve({ value });
          }
          if (this._closed) {
            if (this._settlers.length > 0) {
              throw new Error("Illegal internal state");
            }
            return Promise.resolve({ done: true });
          }
          return new Promise((resolve, reject) => {
            this._settlers.enqueue({ resolve, reject });
          });
        }
      };
    }
  });

  // src/index.ts
  var src_exports = {};
  __export(src_exports, {
    AsyncQueue: () => AsyncQueue,
    Binary: () => Binary,
    Bool: () => Bool,
    ColumnarTableBatchAggregator: () => ColumnarTableBatchAggregator,
    DataType: () => DataType,
    Date: () => Date2,
    DateDay: () => DateDay,
    DateMillisecond: () => DateMillisecond,
    Field: () => Field,
    FixedSizeList: () => FixedSizeList,
    Float: () => Float,
    Float16: () => Float16,
    Float32: () => Float32,
    Float64: () => Float64,
    Int: () => Int,
    Int16: () => Int16,
    Int32: () => Int32,
    Int64: () => Int64,
    Int8: () => Int8,
    Interval: () => Interval,
    IntervalDayTime: () => IntervalDayTime,
    IntervalYearMonth: () => IntervalYearMonth,
    Null: () => Null,
    RowTableBatchAggregator: () => RowTableBatchAggregator2,
    Schema: () => Schema,
    Struct: () => Struct,
    TableBatchBuilder: () => TableBatchBuilder,
    Time: () => Time,
    TimeMillisecond: () => TimeMillisecond,
    TimeSecond: () => TimeSecond,
    Timestamp: () => Timestamp,
    TimestampMicrosecond: () => TimestampMicrosecond,
    TimestampMillisecond: () => TimestampMillisecond,
    TimestampNanosecond: () => TimestampNanosecond,
    TimestampSecond: () => TimestampSecond,
    Uint16: () => Uint16,
    Uint32: () => Uint32,
    Uint64: () => Uint64,
    Uint8: () => Uint8,
    Utf8: () => Utf8,
    convertToArrayRow: () => convertToArrayRow,
    convertToObjectRow: () => convertToObjectRow,
    deduceMeshField: () => deduceMeshField,
    deduceMeshSchema: () => deduceMeshSchema,
    deduceTypeFromColumn: () => deduceTypeFromColumn,
    deduceTypeFromValue: () => deduceTypeFromValue,
    getArrowTypeFromTypedArray: () => getArrowTypeFromTypedArray,
    getMeshBoundingBox: () => getMeshBoundingBox,
    getMeshSize: () => getMeshSize,
    getTypeInfo: () => getTypeInfo,
    makeMeshAttributeMetadata: () => makeMeshAttributeMetadata
  });
  var init_src = __esm({
    "src/index.ts"() {
      init_table_batch_builder();
      init_row_table_batch_aggregator();
      init_columnar_table_batch_aggregator();
      init_row_utils();
      init_mesh_utils();
      init_deduce_mesh_schema();
      init_schema2();
      init_deduce_column_type();
      init_get_type_info();
      init_arrow_like_type_utils();
      init_async_queue();
    }
  });

  // src/bundle.ts
  var require_bundle = __commonJS({
    "src/bundle.ts"(exports, module) {
      var moduleExports = (init_src(), src_exports);
      globalThis.loaders = globalThis.loaders || {};
      module.exports = Object.assign(globalThis.loaders, moduleExports);
    }
  });
  require_bundle();
})();
