"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "AsyncQueue", {
  enumerable: true,
  get: function () {
    return _asyncQueue.default;
  }
});
Object.defineProperty(exports, "Binary", {
  enumerable: true,
  get: function () {
    return _schema.Binary;
  }
});
Object.defineProperty(exports, "Bool", {
  enumerable: true,
  get: function () {
    return _schema.Bool;
  }
});
Object.defineProperty(exports, "ColumnarTableBatchAggregator", {
  enumerable: true,
  get: function () {
    return _columnarTableBatchAggregator.default;
  }
});
Object.defineProperty(exports, "DataType", {
  enumerable: true,
  get: function () {
    return _schema.DataType;
  }
});
Object.defineProperty(exports, "Date", {
  enumerable: true,
  get: function () {
    return _schema.Date;
  }
});
Object.defineProperty(exports, "DateDay", {
  enumerable: true,
  get: function () {
    return _schema.DateDay;
  }
});
Object.defineProperty(exports, "DateMillisecond", {
  enumerable: true,
  get: function () {
    return _schema.DateMillisecond;
  }
});
Object.defineProperty(exports, "Field", {
  enumerable: true,
  get: function () {
    return _schema.Field;
  }
});
Object.defineProperty(exports, "FixedSizeList", {
  enumerable: true,
  get: function () {
    return _schema.FixedSizeList;
  }
});
Object.defineProperty(exports, "Float", {
  enumerable: true,
  get: function () {
    return _schema.Float;
  }
});
Object.defineProperty(exports, "Float16", {
  enumerable: true,
  get: function () {
    return _schema.Float16;
  }
});
Object.defineProperty(exports, "Float32", {
  enumerable: true,
  get: function () {
    return _schema.Float32;
  }
});
Object.defineProperty(exports, "Float64", {
  enumerable: true,
  get: function () {
    return _schema.Float64;
  }
});
Object.defineProperty(exports, "Int", {
  enumerable: true,
  get: function () {
    return _schema.Int;
  }
});
Object.defineProperty(exports, "Int16", {
  enumerable: true,
  get: function () {
    return _schema.Int16;
  }
});
Object.defineProperty(exports, "Int32", {
  enumerable: true,
  get: function () {
    return _schema.Int32;
  }
});
Object.defineProperty(exports, "Int64", {
  enumerable: true,
  get: function () {
    return _schema.Int64;
  }
});
Object.defineProperty(exports, "Int8", {
  enumerable: true,
  get: function () {
    return _schema.Int8;
  }
});
Object.defineProperty(exports, "Interval", {
  enumerable: true,
  get: function () {
    return _schema.Interval;
  }
});
Object.defineProperty(exports, "IntervalDayTime", {
  enumerable: true,
  get: function () {
    return _schema.IntervalDayTime;
  }
});
Object.defineProperty(exports, "IntervalYearMonth", {
  enumerable: true,
  get: function () {
    return _schema.IntervalYearMonth;
  }
});
Object.defineProperty(exports, "Null", {
  enumerable: true,
  get: function () {
    return _schema.Null;
  }
});
Object.defineProperty(exports, "RowTableBatchAggregator", {
  enumerable: true,
  get: function () {
    return _rowTableBatchAggregator.default;
  }
});
Object.defineProperty(exports, "Schema", {
  enumerable: true,
  get: function () {
    return _schema.Schema;
  }
});
Object.defineProperty(exports, "Struct", {
  enumerable: true,
  get: function () {
    return _schema.Struct;
  }
});
Object.defineProperty(exports, "TableBatchBuilder", {
  enumerable: true,
  get: function () {
    return _tableBatchBuilder.default;
  }
});
Object.defineProperty(exports, "Time", {
  enumerable: true,
  get: function () {
    return _schema.Time;
  }
});
Object.defineProperty(exports, "TimeMillisecond", {
  enumerable: true,
  get: function () {
    return _schema.TimeMillisecond;
  }
});
Object.defineProperty(exports, "TimeSecond", {
  enumerable: true,
  get: function () {
    return _schema.TimeSecond;
  }
});
Object.defineProperty(exports, "Timestamp", {
  enumerable: true,
  get: function () {
    return _schema.Timestamp;
  }
});
Object.defineProperty(exports, "TimestampMicrosecond", {
  enumerable: true,
  get: function () {
    return _schema.TimestampMicrosecond;
  }
});
Object.defineProperty(exports, "TimestampMillisecond", {
  enumerable: true,
  get: function () {
    return _schema.TimestampMillisecond;
  }
});
Object.defineProperty(exports, "TimestampNanosecond", {
  enumerable: true,
  get: function () {
    return _schema.TimestampNanosecond;
  }
});
Object.defineProperty(exports, "TimestampSecond", {
  enumerable: true,
  get: function () {
    return _schema.TimestampSecond;
  }
});
Object.defineProperty(exports, "Uint16", {
  enumerable: true,
  get: function () {
    return _schema.Uint16;
  }
});
Object.defineProperty(exports, "Uint32", {
  enumerable: true,
  get: function () {
    return _schema.Uint32;
  }
});
Object.defineProperty(exports, "Uint64", {
  enumerable: true,
  get: function () {
    return _schema.Uint64;
  }
});
Object.defineProperty(exports, "Uint8", {
  enumerable: true,
  get: function () {
    return _schema.Uint8;
  }
});
Object.defineProperty(exports, "Utf8", {
  enumerable: true,
  get: function () {
    return _schema.Utf8;
  }
});
Object.defineProperty(exports, "convertToArrayRow", {
  enumerable: true,
  get: function () {
    return _rowUtils.convertToArrayRow;
  }
});
Object.defineProperty(exports, "convertToObjectRow", {
  enumerable: true,
  get: function () {
    return _rowUtils.convertToObjectRow;
  }
});
Object.defineProperty(exports, "deduceMeshField", {
  enumerable: true,
  get: function () {
    return _deduceMeshSchema.deduceMeshField;
  }
});
Object.defineProperty(exports, "deduceMeshSchema", {
  enumerable: true,
  get: function () {
    return _deduceMeshSchema.deduceMeshSchema;
  }
});
Object.defineProperty(exports, "deduceTypeFromColumn", {
  enumerable: true,
  get: function () {
    return _deduceColumnType.deduceTypeFromColumn;
  }
});
Object.defineProperty(exports, "deduceTypeFromValue", {
  enumerable: true,
  get: function () {
    return _deduceColumnType.deduceTypeFromValue;
  }
});
Object.defineProperty(exports, "getArrowTypeFromTypedArray", {
  enumerable: true,
  get: function () {
    return _arrowLikeTypeUtils.getArrowTypeFromTypedArray;
  }
});
Object.defineProperty(exports, "getMeshBoundingBox", {
  enumerable: true,
  get: function () {
    return _meshUtils.getMeshBoundingBox;
  }
});
Object.defineProperty(exports, "getMeshSize", {
  enumerable: true,
  get: function () {
    return _meshUtils.getMeshSize;
  }
});
Object.defineProperty(exports, "getTypeInfo", {
  enumerable: true,
  get: function () {
    return _getTypeInfo.getTypeInfo;
  }
});
Object.defineProperty(exports, "makeMeshAttributeMetadata", {
  enumerable: true,
  get: function () {
    return _deduceMeshSchema.makeMeshAttributeMetadata;
  }
});
var _tableBatchBuilder = _interopRequireDefault(require("./lib/batches/table-batch-builder"));
var _rowTableBatchAggregator = _interopRequireDefault(require("./lib/batches/row-table-batch-aggregator"));
var _columnarTableBatchAggregator = _interopRequireDefault(require("./lib/batches/columnar-table-batch-aggregator"));
var _rowUtils = require("./lib/utils/row-utils");
var _meshUtils = require("./category/mesh/mesh-utils");
var _deduceMeshSchema = require("./category/mesh/deduce-mesh-schema");
var _schema = require("./lib/schema/schema");
var _deduceColumnType = require("./lib/schema-utils/deduce-column-type");
var _getTypeInfo = require("./lib/arrow/get-type-info");
var _arrowLikeTypeUtils = require("./lib/arrow/arrow-like-type-utils");
var _asyncQueue = _interopRequireDefault(require("./lib/utils/async-queue"));
//# sourceMappingURL=index.js.map