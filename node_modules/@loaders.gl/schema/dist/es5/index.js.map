{"version": 3, "file": "index.js", "names": ["_tableBatchBuilder", "_interopRequireDefault", "require", "_rowTableBatchAggregator", "_columnarTableBatchAggregator", "_rowUtils", "_meshUtils", "_deduceMeshSchema", "_schema", "_deduceColumnType", "_getTypeInfo", "_arrowLikeTypeUtils", "_asyncQueue"], "sources": ["../../src/index.ts"], "sourcesContent": ["// COMMON CATEGORY\nexport type {TypedArray, NumberArray, AnyArray} from './types';\n\nexport type {Batch} from './category/common';\n\n// TABLE CATEGORY TYPES\nexport type {\n  Table,\n  ArrayRowTable,\n  ObjectRowTable,\n  GeoJSONRowTable,\n  ColumnarTable,\n  ArrowTable,\n  Tables\n} from './category/table/table-types';\nexport type {\n  TableBatch,\n  RowArrayTableBatch,\n  RowObjectTableBatch,\n  GeoJSONRowTableBatch,\n  ColumnarTableBatch,\n  ArrowTableBatch\n} from './category/table/table-types';\n\n// TABLE CATEGORY UTILS\nexport {default as TableBatchBuilder} from './lib/batches/table-batch-builder';\nexport type {TableBatchAggregator} from './lib/batches/table-batch-aggregator';\nexport {default as RowTableBatchAggregator} from './lib/batches/row-table-batch-aggregator';\nexport {default as ColumnarTableBatchAggregator} from './lib/batches/columnar-table-batch-aggregator';\n\nexport {convertToObjectRow, convertToArrayRow} from './lib/utils/row-utils';\n\n// MESH CATEGORY\nexport type {\n  MeshTable,\n  MeshArrowTable,\n  Mesh,\n  MeshGeometry,\n  MeshAttribute,\n  MeshAttributes\n} from './category/mesh/mesh-types';\n\nexport {getMeshSize, getMeshBoundingBox} from './category/mesh/mesh-utils';\n// Commented out due to https://github.com/visgl/deck.gl/issues/6906 and https://github.com/visgl/loaders.gl/issues/2177\n// export {convertMesh} from './category/mesh/convert-mesh';\nexport {\n  deduceMeshSchema,\n  deduceMeshField,\n  makeMeshAttributeMetadata\n} from './category/mesh/deduce-mesh-schema';\n\n// TEXTURES\nexport type {TextureLevel, GPUTextureFormat} from './category/texture/texture';\n\n// IMAGES\nexport type {ImageDataType, ImageType, ImageTypeEnum} from './category/image/image';\n\n// TYPES\n// GIS CATEGORY - GEOJSON\nexport type {\n  GeoJSON,\n  Feature,\n  FeatureCollection,\n  Geometry,\n  Position,\n  GeoJsonProperties,\n  Point,\n  MultiPoint,\n  LineString,\n  MultiLineString,\n  Polygon,\n  MultiPolygon,\n  GeometryCollection\n} from './category/gis';\n\nexport type {GeojsonGeometryInfo} from './category/gis';\n\n// GIS CATEGORY - FLAT GEOJSON\nexport type {\n  FlatFeature,\n  FlatIndexedGeometry,\n  FlatGeometry,\n  FlatGeometryType,\n  FlatPoint,\n  FlatLineString,\n  FlatPolygon\n} from './category/gis';\n\n// GIS CATEGORY - BINARY\nexport type {\n  BinaryGeometryType,\n  BinaryGeometry,\n  BinaryPointGeometry,\n  BinaryLineGeometry,\n  BinaryPolygonGeometry,\n  BinaryAttribute\n} from './category/gis';\nexport type {\n  BinaryFeatures,\n  BinaryPointFeatures,\n  BinaryLineFeatures,\n  BinaryPolygonFeatures\n} from './category/gis';\n\n// SCHEMA\nexport {\n  Schema,\n  Field,\n  DataType,\n  Null,\n  Binary,\n  Bool,\n  Int,\n  Int8,\n  Int16,\n  Int32,\n  Int64,\n  Uint8,\n  Uint16,\n  Uint32,\n  Uint64,\n  Float,\n  Float16,\n  Float32,\n  Float64,\n  Utf8,\n  Date,\n  DateDay,\n  DateMillisecond,\n  Time,\n  TimeMillisecond,\n  TimeSecond,\n  Timestamp,\n  TimestampSecond,\n  TimestampMillisecond,\n  TimestampMicrosecond,\n  TimestampNanosecond,\n  Interval,\n  IntervalDayTime,\n  IntervalYearMonth,\n  FixedSizeList,\n  Struct\n} from './lib/schema/schema';\n\n// EXPERIMENTAL APIs\n\n// SCHEMA UTILS\nexport {deduceTypeFromColumn, deduceTypeFromValue} from './lib/schema-utils/deduce-column-type';\nexport {getTypeInfo} from './lib/arrow/get-type-info';\nexport {getArrowTypeFromTypedArray} from './lib/arrow/arrow-like-type-utils';\n\nexport {default as AsyncQueue} from './lib/utils/async-queue';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,IAAAA,kBAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,wBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,6BAAA,GAAAH,sBAAA,CAAAC,OAAA;AAEA,IAAAG,SAAA,GAAAH,OAAA;AAYA,IAAAI,UAAA,GAAAJ,OAAA;AAGA,IAAAK,iBAAA,GAAAL,OAAA;AA4DA,IAAAM,OAAA,GAAAN,OAAA;AA0CA,IAAAO,iBAAA,GAAAP,OAAA;AACA,IAAAQ,YAAA,GAAAR,OAAA;AACA,IAAAS,mBAAA,GAAAT,OAAA;AAEA,IAAAU,WAAA,GAAAX,sBAAA,CAAAC,OAAA"}