{"version": 3, "file": "types.js", "names": [], "sources": ["../../src/types.ts"], "sourcesContent": ["/** Any typed array */\nexport type TypedArray =\n  | Int8Array\n  | Uint8Array\n  | Int16Array\n  | Uint16Array\n  | Int32Array\n  | Uint32Array\n  | Uint8ClampedArray\n  | Float32Array\n  | Float64Array;\n\nexport type BigTypedArray = TypedArray | BigInt64Array | BigUint64Array;\n\n/** Any numeric array: typed array or `number[]` */\nexport type NumberArray = number[] | TypedArray;\n\n/** Any array: typed array or js array (`any[]`) */\nexport type AnyArray = any[] | TypedArray;\n"], "mappings": ""}