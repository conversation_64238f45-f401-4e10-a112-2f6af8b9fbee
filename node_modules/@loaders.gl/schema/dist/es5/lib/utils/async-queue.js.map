{"version": 3, "file": "async-queue.js", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Array", "enqueue", "value", "push", "dequeue", "shift", "_Symbol$asyncIterator", "Symbol", "asyncIterator", "AsyncQueue", "constructor", "_defineProperty2", "default", "_values", "_settlers", "_closed", "close", "length", "resolve", "done", "Error", "settler", "reject", "next", "Promise", "exports", "take<PERSON><PERSON>", "asyncIterable", "count", "arguments", "undefined", "Infinity", "result", "iterator"], "sources": ["../../../../src/lib/utils/async-queue.ts"], "sourcesContent": ["// From https://github.com/rauschma/async-iter-demo/tree/master/src under MIT license\n// http://2ality.com/2016/10/asynchronous-iteration.html\n\nclass ArrayQueue<T> extends Array<T> {\n  enqueue(value: T) {\n    // Add at the end\n    return this.push(value);\n  }\n  dequeue(): T {\n    // Remove first element\n    return this.shift() as T;\n  }\n}\n\nexport default class AsyncQueue<T> {\n  private _values: ArrayQueue<T | Error>;\n  private _settlers: ArrayQueue<{resolve: (value: any) => void; reject: (reason?: any) => void}>;\n  private _closed: boolean;\n\n  constructor() {\n    // enqueues > dequeues\n    this._values = new ArrayQueue<T>();\n    // dequeues > enqueues\n    this._settlers = new ArrayQueue<{\n      resolve: (value: any) => void;\n      reject: (reason?: any) => void;\n    }>();\n    this._closed = false;\n  }\n\n  close(): void {\n    while (this._settlers.length > 0) {\n      this._settlers.dequeue().resolve({done: true});\n    }\n    this._closed = true;\n  }\n\n  [Symbol.asyncIterator](): AsyncIterator<T> {\n    return this;\n  }\n\n  enqueue(value: T | Error): void {\n    if (this._closed) {\n      throw new Error('Closed');\n    }\n\n    if (this._settlers.length > 0) {\n      if (this._values.length > 0) {\n        throw new Error('Illegal internal state');\n      }\n      const settler = this._settlers.dequeue();\n      if (value instanceof Error) {\n        settler.reject(value);\n      } else {\n        settler.resolve({value});\n      }\n    } else {\n      this._values.enqueue(value);\n    }\n  }\n\n  /**\n   * @returns a Promise for an IteratorResult\n   */\n  next(): Promise<any> {\n    if (this._values.length > 0) {\n      const value = this._values.dequeue();\n      if (value instanceof Error) {\n        return Promise.reject(value);\n      }\n      return Promise.resolve({value});\n    }\n\n    if (this._closed) {\n      if (this._settlers.length > 0) {\n        throw new Error('Illegal internal state');\n      }\n      return Promise.resolve({done: true});\n    }\n    // Wait for new values to be enqueued\n    return new Promise((resolve, reject) => {\n      this._settlers.enqueue({resolve, reject});\n    });\n  }\n}\n\n/**\n * @returns a Promise for an Array with the elements in `asyncIterable`\n */\nexport async function takeAsync(\n  asyncIterable: AsyncIterable<any>,\n  count = Infinity\n): Promise<any[]> {\n  const result: Array<any> = [];\n  const iterator = asyncIterable[Symbol.asyncIterator]();\n  while (result.length < count) {\n    const {value, done} = await iterator.next();\n    if (done) {\n      break;\n    }\n    result.push(value);\n  }\n  return result;\n}\n"], "mappings": ";;;;;;;;;;AAGA,MAAMA,UAAU,SAAYC,KAAK,CAAI;EACnCC,OAAOA,CAACC,KAAQ,EAAE;IAEhB,OAAO,IAAI,CAACC,IAAI,CAACD,KAAK,CAAC;EACzB;EACAE,OAAOA,CAAA,EAAM;IAEX,OAAO,IAAI,CAACC,KAAK,CAAC,CAAC;EACrB;AACF;AAACC,qBAAA,GAyBEC,MAAM,CAACC,aAAa;AAvBR,MAAMC,UAAU,CAAI;EAKjCC,WAAWA,CAAA,EAAG;IAAA,IAAAC,gBAAA,CAAAC,OAAA;IAAA,IAAAD,gBAAA,CAAAC,OAAA;IAAA,IAAAD,gBAAA,CAAAC,OAAA;IAEZ,IAAI,CAACC,OAAO,GAAG,IAAId,UAAU,CAAI,CAAC;IAElC,IAAI,CAACe,SAAS,GAAG,IAAIf,UAAU,CAG5B,CAAC;IACJ,IAAI,CAACgB,OAAO,GAAG,KAAK;EACtB;EAEAC,KAAKA,CAAA,EAAS;IACZ,OAAO,IAAI,CAACF,SAAS,CAACG,MAAM,GAAG,CAAC,EAAE;MAChC,IAAI,CAACH,SAAS,CAACV,OAAO,CAAC,CAAC,CAACc,OAAO,CAAC;QAACC,IAAI,EAAE;MAAI,CAAC,CAAC;IAChD;IACA,IAAI,CAACJ,OAAO,GAAG,IAAI;EACrB;EAEA,CAAAT,qBAAA,IAA2C;IACzC,OAAO,IAAI;EACb;EAEAL,OAAOA,CAACC,KAAgB,EAAQ;IAC9B,IAAI,IAAI,CAACa,OAAO,EAAE;MAChB,MAAM,IAAIK,KAAK,CAAC,QAAQ,CAAC;IAC3B;IAEA,IAAI,IAAI,CAACN,SAAS,CAACG,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,IAAI,CAACJ,OAAO,CAACI,MAAM,GAAG,CAAC,EAAE;QAC3B,MAAM,IAAIG,KAAK,CAAC,wBAAwB,CAAC;MAC3C;MACA,MAAMC,OAAO,GAAG,IAAI,CAACP,SAAS,CAACV,OAAO,CAAC,CAAC;MACxC,IAAIF,KAAK,YAAYkB,KAAK,EAAE;QAC1BC,OAAO,CAACC,MAAM,CAACpB,KAAK,CAAC;MACvB,CAAC,MAAM;QACLmB,OAAO,CAACH,OAAO,CAAC;UAAChB;QAAK,CAAC,CAAC;MAC1B;IACF,CAAC,MAAM;MACL,IAAI,CAACW,OAAO,CAACZ,OAAO,CAACC,KAAK,CAAC;IAC7B;EACF;EAKAqB,IAAIA,CAAA,EAAiB;IACnB,IAAI,IAAI,CAACV,OAAO,CAACI,MAAM,GAAG,CAAC,EAAE;MAC3B,MAAMf,KAAK,GAAG,IAAI,CAACW,OAAO,CAACT,OAAO,CAAC,CAAC;MACpC,IAAIF,KAAK,YAAYkB,KAAK,EAAE;QAC1B,OAAOI,OAAO,CAACF,MAAM,CAACpB,KAAK,CAAC;MAC9B;MACA,OAAOsB,OAAO,CAACN,OAAO,CAAC;QAAChB;MAAK,CAAC,CAAC;IACjC;IAEA,IAAI,IAAI,CAACa,OAAO,EAAE;MAChB,IAAI,IAAI,CAACD,SAAS,CAACG,MAAM,GAAG,CAAC,EAAE;QAC7B,MAAM,IAAIG,KAAK,CAAC,wBAAwB,CAAC;MAC3C;MACA,OAAOI,OAAO,CAACN,OAAO,CAAC;QAACC,IAAI,EAAE;MAAI,CAAC,CAAC;IACtC;IAEA,OAAO,IAAIK,OAAO,CAAC,CAACN,OAAO,EAAEI,MAAM,KAAK;MACtC,IAAI,CAACR,SAAS,CAACb,OAAO,CAAC;QAACiB,OAAO;QAAEI;MAAM,CAAC,CAAC;IAC3C,CAAC,CAAC;EACJ;AACF;AAACG,OAAA,CAAAb,OAAA,GAAAH,UAAA;AAKM,eAAeiB,SAASA,CAC7BC,aAAiC,EAEjB;EAAA,IADhBC,KAAK,GAAAC,SAAA,CAAAZ,MAAA,QAAAY,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGE,QAAQ;EAEhB,MAAMC,MAAkB,GAAG,EAAE;EAC7B,MAAMC,QAAQ,GAAGN,aAAa,CAACpB,MAAM,CAACC,aAAa,CAAC,CAAC,CAAC;EACtD,OAAOwB,MAAM,CAACf,MAAM,GAAGW,KAAK,EAAE;IAC5B,MAAM;MAAC1B,KAAK;MAAEiB;IAAI,CAAC,GAAG,MAAMc,QAAQ,CAACV,IAAI,CAAC,CAAC;IAC3C,IAAIJ,IAAI,EAAE;MACR;IACF;IACAa,MAAM,CAAC7B,IAAI,CAACD,KAAK,CAAC;EACpB;EACA,OAAO8B,MAAM;AACf"}