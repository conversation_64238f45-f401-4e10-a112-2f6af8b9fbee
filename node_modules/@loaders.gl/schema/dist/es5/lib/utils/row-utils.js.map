{"version": 3, "file": "row-utils.js", "names": ["convertToObjectRow", "arrayRow", "headers", "Error", "objectRow", "i", "length", "convertToArrayRow", "Array"], "sources": ["../../../../src/lib/utils/row-utils.ts"], "sourcesContent": ["/** Convert an object row to an array row */\nexport function convertToObjectRow(\n  arrayRow: any[],\n  headers: string[]\n): {[columnName: string]: any} {\n  if (!arrayRow) {\n    throw new Error('null row');\n  }\n  if (!headers) {\n    throw new Error('no headers');\n  }\n  const objectRow = {};\n  for (let i = 0; i < headers.length; i++) {\n    objectRow[headers[i]] = arrayRow[i];\n  }\n  return objectRow;\n}\n\n/** Convert an object row to an array row */\nexport function convertToArrayRow(\n  objectRow: {[columnName: string]: any},\n  headers: string[]\n): any[] {\n  if (!objectRow) {\n    throw new Error('null row');\n  }\n  if (!headers) {\n    throw new Error('no headers');\n  }\n  const arrayRow = new Array(headers.length);\n  for (let i = 0; i < headers.length; i++) {\n    arrayRow[i] = objectRow[headers[i]];\n  }\n  return arrayRow;\n}\n"], "mappings": ";;;;;;;AACO,SAASA,kBAAkBA,CAChCC,QAAe,EACfC,OAAiB,EACY;EAC7B,IAAI,CAACD,QAAQ,EAAE;IACb,MAAM,IAAIE,KAAK,CAAC,UAAU,CAAC;EAC7B;EACA,IAAI,CAACD,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,YAAY,CAAC;EAC/B;EACA,MAAMC,SAAS,GAAG,CAAC,CAAC;EACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACvCD,SAAS,CAACF,OAAO,CAACG,CAAC,CAAC,CAAC,GAAGJ,QAAQ,CAACI,CAAC,CAAC;EACrC;EACA,OAAOD,SAAS;AAClB;AAGO,SAASG,iBAAiBA,CAC/BH,SAAsC,EACtCF,OAAiB,EACV;EACP,IAAI,CAACE,SAAS,EAAE;IACd,MAAM,IAAID,KAAK,CAAC,UAAU,CAAC;EAC7B;EACA,IAAI,CAACD,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,YAAY,CAAC;EAC/B;EACA,MAAMF,QAAQ,GAAG,IAAIO,KAAK,CAACN,OAAO,CAACI,MAAM,CAAC;EAC1C,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACvCJ,QAAQ,CAACI,CAAC,CAAC,GAAGD,SAAS,CAACF,OAAO,CAACG,CAAC,CAAC,CAAC;EACrC;EACA,OAAOJ,QAAQ;AACjB"}