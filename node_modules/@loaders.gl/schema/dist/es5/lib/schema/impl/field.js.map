{"version": 3, "file": "field.js", "names": ["Field", "constructor", "name", "type", "nullable", "arguments", "length", "undefined", "metadata", "Map", "_defineProperty2", "default", "typeId", "clone", "compareTo", "other", "toString", "concat", "exports"], "sources": ["../../../../../src/lib/schema/impl/field.ts"], "sourcesContent": ["import {DataType} from './type';\n\n/**\n * ArrowJS `Field` API-compatible class for row-based tables\n * https://loaders.gl/arrowjs/docs/api-reference/field\n * A field holds name, nullable, and metadata information about a table \"column\"\n * A Schema is essentially a list of fields\n */\nexport default class Field {\n  name: string;\n  type: DataType;\n  nullable: boolean;\n  metadata: Map<string, string>;\n\n  constructor(\n    name: string,\n    type: DataType,\n    nullable = false,\n    metadata: Map<string, string> = new Map()\n  ) {\n    this.name = name;\n    this.type = type;\n    this.nullable = nullable;\n    this.metadata = metadata;\n  }\n\n  get typeId(): number {\n    return this.type && this.type.typeId;\n  }\n\n  clone(): Field {\n    return new Field(this.name, this.type, this.nullable, this.metadata);\n  }\n\n  compareTo(other: this): boolean {\n    return (\n      this.name === other.name &&\n      this.type === other.type &&\n      this.nullable === other.nullable &&\n      this.metadata === other.metadata\n    );\n  }\n\n  toString(): string {\n    return `${this.type}${this.nullable ? ', nullable' : ''}${\n      this.metadata ? `, metadata: ${this.metadata}` : ''\n    }`;\n  }\n}\n"], "mappings": ";;;;;;;;AAQe,MAAMA,KAAK,CAAC;EAMzBC,WAAWA,CACTC,IAAY,EACZC,IAAc,EAGd;IAAA,IAFAC,QAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IAAA,IAChBG,QAA6B,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAII,GAAG,CAAC,CAAC;IAAA,IAAAC,gBAAA,CAAAC,OAAA;IAAA,IAAAD,gBAAA,CAAAC,OAAA;IAAA,IAAAD,gBAAA,CAAAC,OAAA;IAAA,IAAAD,gBAAA,CAAAC,OAAA;IAEzC,IAAI,CAACT,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACI,QAAQ,GAAGA,QAAQ;EAC1B;EAEA,IAAII,MAAMA,CAAA,EAAW;IACnB,OAAO,IAAI,CAACT,IAAI,IAAI,IAAI,CAACA,IAAI,CAACS,MAAM;EACtC;EAEAC,KAAKA,CAAA,EAAU;IACb,OAAO,IAAIb,KAAK,CAAC,IAAI,CAACE,IAAI,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACI,QAAQ,CAAC;EACtE;EAEAM,SAASA,CAACC,KAAW,EAAW;IAC9B,OACE,IAAI,CAACb,IAAI,KAAKa,KAAK,CAACb,IAAI,IACxB,IAAI,CAACC,IAAI,KAAKY,KAAK,CAACZ,IAAI,IACxB,IAAI,CAACC,QAAQ,KAAKW,KAAK,CAACX,QAAQ,IAChC,IAAI,CAACI,QAAQ,KAAKO,KAAK,CAACP,QAAQ;EAEpC;EAEAQ,QAAQA,CAAA,EAAW;IACjB,UAAAC,MAAA,CAAU,IAAI,CAACd,IAAI,EAAAc,MAAA,CAAG,IAAI,CAACb,QAAQ,GAAG,YAAY,GAAG,EAAE,EAAAa,MAAA,CACrD,IAAI,CAACT,QAAQ,kBAAAS,MAAA,CAAkB,IAAI,CAACT,QAAQ,IAAK,EAAE;EAEvD;AACF;AAACU,OAAA,CAAAP,OAAA,GAAAX,KAAA"}