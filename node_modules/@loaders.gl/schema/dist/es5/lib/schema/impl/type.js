"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.TimestampSecond = exports.TimestampNanosecond = exports.TimestampMillisecond = exports.TimestampMicrosecond = exports.Timestamp = exports.TimeSecond = exports.TimeMillisecond = exports.Time = exports.Struct = exports.Null = exports.IntervalYearMonth = exports.IntervalDayTime = exports.Interval = exports.Int8 = exports.Int64 = exports.Int32 = exports.Int16 = exports.Int = exports.Float64 = exports.Float32 = exports.Float16 = exports.Float = exports.FixedSizeList = exports.DateMillisecond = exports.DateDay = exports.Date = exports.DataType = exports.Bool = exports.Binary = void 0;
Object.defineProperty(exports, "Type", {
  enumerable: true,
  get: function () {
    return _enum.Type;
  }
});
exports.Utf8 = exports.Uint8 = exports.Uint64 = exports.Uint32 = exports.Uint16 = void 0;
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _enum = require("./enum");
let _Symbol$toStringTag, _Symbol$toStringTag2, _Symbol$toStringTag3, _Symbol$toStringTag4, _Symbol$toStringTag5, _Symbol$toStringTag6, _Symbol$toStringTag7, _Symbol$toStringTag8;
class DataType {
  static isNull(x) {
    return x && x.typeId === _enum.Type.Null;
  }
  static isInt(x) {
    return x && x.typeId === _enum.Type.Int;
  }
  static isFloat(x) {
    return x && x.typeId === _enum.Type.Float;
  }
  static isBinary(x) {
    return x && x.typeId === _enum.Type.Binary;
  }
  static isUtf8(x) {
    return x && x.typeId === _enum.Type.Utf8;
  }
  static isBool(x) {
    return x && x.typeId === _enum.Type.Bool;
  }
  static isDecimal(x) {
    return x && x.typeId === _enum.Type.Decimal;
  }
  static isDate(x) {
    return x && x.typeId === _enum.Type.Date;
  }
  static isTime(x) {
    return x && x.typeId === _enum.Type.Time;
  }
  static isTimestamp(x) {
    return x && x.typeId === _enum.Type.Timestamp;
  }
  static isInterval(x) {
    return x && x.typeId === _enum.Type.Interval;
  }
  static isList(x) {
    return x && x.typeId === _enum.Type.List;
  }
  static isStruct(x) {
    return x && x.typeId === _enum.Type.Struct;
  }
  static isUnion(x) {
    return x && x.typeId === _enum.Type.Union;
  }
  static isFixedSizeBinary(x) {
    return x && x.typeId === _enum.Type.FixedSizeBinary;
  }
  static isFixedSizeList(x) {
    return x && x.typeId === _enum.Type.FixedSizeList;
  }
  static isMap(x) {
    return x && x.typeId === _enum.Type.Map;
  }
  static isDictionary(x) {
    return x && x.typeId === _enum.Type.Dictionary;
  }
  get typeId() {
    return _enum.Type.NONE;
  }
  compareTo(other) {
    return this === other;
  }
}
exports.DataType = DataType;
class Null extends DataType {
  get typeId() {
    return _enum.Type.Null;
  }
  get [Symbol.toStringTag]() {
    return 'Null';
  }
  toString() {
    return 'Null';
  }
}
exports.Null = Null;
class Bool extends DataType {
  get typeId() {
    return _enum.Type.Bool;
  }
  get [Symbol.toStringTag]() {
    return 'Bool';
  }
  toString() {
    return 'Bool';
  }
}
exports.Bool = Bool;
_Symbol$toStringTag = Symbol.toStringTag;
class Int extends DataType {
  constructor(isSigned, bitWidth) {
    super();
    (0, _defineProperty2.default)(this, "isSigned", void 0);
    (0, _defineProperty2.default)(this, "bitWidth", void 0);
    this.isSigned = isSigned;
    this.bitWidth = bitWidth;
  }
  get typeId() {
    return _enum.Type.Int;
  }
  get [_Symbol$toStringTag]() {
    return 'Int';
  }
  toString() {
    return "".concat(this.isSigned ? 'I' : 'Ui', "nt").concat(this.bitWidth);
  }
}
exports.Int = Int;
class Int8 extends Int {
  constructor() {
    super(true, 8);
  }
}
exports.Int8 = Int8;
class Int16 extends Int {
  constructor() {
    super(true, 16);
  }
}
exports.Int16 = Int16;
class Int32 extends Int {
  constructor() {
    super(true, 32);
  }
}
exports.Int32 = Int32;
class Int64 extends Int {
  constructor() {
    super(true, 64);
  }
}
exports.Int64 = Int64;
class Uint8 extends Int {
  constructor() {
    super(false, 8);
  }
}
exports.Uint8 = Uint8;
class Uint16 extends Int {
  constructor() {
    super(false, 16);
  }
}
exports.Uint16 = Uint16;
class Uint32 extends Int {
  constructor() {
    super(false, 32);
  }
}
exports.Uint32 = Uint32;
class Uint64 extends Int {
  constructor() {
    super(false, 64);
  }
}
exports.Uint64 = Uint64;
const Precision = {
  HALF: 16,
  SINGLE: 32,
  DOUBLE: 64
};
_Symbol$toStringTag2 = Symbol.toStringTag;
class Float extends DataType {
  constructor(precision) {
    super();
    (0, _defineProperty2.default)(this, "precision", void 0);
    this.precision = precision;
  }
  get typeId() {
    return _enum.Type.Float;
  }
  get [_Symbol$toStringTag2]() {
    return 'Float';
  }
  toString() {
    return "Float".concat(this.precision);
  }
}
exports.Float = Float;
class Float16 extends Float {
  constructor() {
    super(Precision.HALF);
  }
}
exports.Float16 = Float16;
class Float32 extends Float {
  constructor() {
    super(Precision.SINGLE);
  }
}
exports.Float32 = Float32;
class Float64 extends Float {
  constructor() {
    super(Precision.DOUBLE);
  }
}
exports.Float64 = Float64;
class Binary extends DataType {
  constructor() {
    super();
  }
  get typeId() {
    return _enum.Type.Binary;
  }
  toString() {
    return 'Binary';
  }
  get [Symbol.toStringTag]() {
    return 'Binary';
  }
}
exports.Binary = Binary;
class Utf8 extends DataType {
  get typeId() {
    return _enum.Type.Utf8;
  }
  get [Symbol.toStringTag]() {
    return 'Utf8';
  }
  toString() {
    return 'Utf8';
  }
}
exports.Utf8 = Utf8;
const DateUnit = {
  DAY: 0,
  MILLISECOND: 1
};
_Symbol$toStringTag3 = Symbol.toStringTag;
class Date extends DataType {
  constructor(unit) {
    super();
    (0, _defineProperty2.default)(this, "unit", void 0);
    this.unit = unit;
  }
  get typeId() {
    return _enum.Type.Date;
  }
  get [_Symbol$toStringTag3]() {
    return 'Date';
  }
  toString() {
    return "Date".concat((this.unit + 1) * 32, "<").concat(DateUnit[this.unit], ">");
  }
}
exports.Date = Date;
class DateDay extends Date {
  constructor() {
    super(DateUnit.DAY);
  }
}
exports.DateDay = DateDay;
class DateMillisecond extends Date {
  constructor() {
    super(DateUnit.MILLISECOND);
  }
}
exports.DateMillisecond = DateMillisecond;
const TimeUnit = {
  SECOND: 1,
  MILLISECOND: 1e3,
  MICROSECOND: 1e6,
  NANOSECOND: 1e9
};
_Symbol$toStringTag4 = Symbol.toStringTag;
class Time extends DataType {
  constructor(unit, bitWidth) {
    super();
    (0, _defineProperty2.default)(this, "unit", void 0);
    (0, _defineProperty2.default)(this, "bitWidth", void 0);
    this.unit = unit;
    this.bitWidth = bitWidth;
  }
  get typeId() {
    return _enum.Type.Time;
  }
  toString() {
    return "Time".concat(this.bitWidth, "<").concat(TimeUnit[this.unit], ">");
  }
  get [_Symbol$toStringTag4]() {
    return 'Time';
  }
}
exports.Time = Time;
class TimeSecond extends Time {
  constructor() {
    super(TimeUnit.SECOND, 32);
  }
}
exports.TimeSecond = TimeSecond;
class TimeMillisecond extends Time {
  constructor() {
    super(TimeUnit.MILLISECOND, 32);
  }
}
exports.TimeMillisecond = TimeMillisecond;
_Symbol$toStringTag5 = Symbol.toStringTag;
class Timestamp extends DataType {
  constructor(unit) {
    let timezone = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
    super();
    (0, _defineProperty2.default)(this, "unit", void 0);
    (0, _defineProperty2.default)(this, "timezone", void 0);
    this.unit = unit;
    this.timezone = timezone;
  }
  get typeId() {
    return _enum.Type.Timestamp;
  }
  get [_Symbol$toStringTag5]() {
    return 'Timestamp';
  }
  toString() {
    return "Timestamp<".concat(TimeUnit[this.unit]).concat(this.timezone ? ", ".concat(this.timezone) : '', ">");
  }
}
exports.Timestamp = Timestamp;
class TimestampSecond extends Timestamp {
  constructor() {
    let timezone = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;
    super(TimeUnit.SECOND, timezone);
  }
}
exports.TimestampSecond = TimestampSecond;
class TimestampMillisecond extends Timestamp {
  constructor() {
    let timezone = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;
    super(TimeUnit.MILLISECOND, timezone);
  }
}
exports.TimestampMillisecond = TimestampMillisecond;
class TimestampMicrosecond extends Timestamp {
  constructor() {
    let timezone = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;
    super(TimeUnit.MICROSECOND, timezone);
  }
}
exports.TimestampMicrosecond = TimestampMicrosecond;
class TimestampNanosecond extends Timestamp {
  constructor() {
    let timezone = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;
    super(TimeUnit.NANOSECOND, timezone);
  }
}
exports.TimestampNanosecond = TimestampNanosecond;
const IntervalUnit = {
  DAY_TIME: 0,
  YEAR_MONTH: 1
};
_Symbol$toStringTag6 = Symbol.toStringTag;
class Interval extends DataType {
  constructor(unit) {
    super();
    (0, _defineProperty2.default)(this, "unit", void 0);
    this.unit = unit;
  }
  get typeId() {
    return _enum.Type.Interval;
  }
  get [_Symbol$toStringTag6]() {
    return 'Interval';
  }
  toString() {
    return "Interval<".concat(IntervalUnit[this.unit], ">");
  }
}
exports.Interval = Interval;
class IntervalDayTime extends Interval {
  constructor() {
    super(IntervalUnit.DAY_TIME);
  }
}
exports.IntervalDayTime = IntervalDayTime;
class IntervalYearMonth extends Interval {
  constructor() {
    super(IntervalUnit.YEAR_MONTH);
  }
}
exports.IntervalYearMonth = IntervalYearMonth;
_Symbol$toStringTag7 = Symbol.toStringTag;
class FixedSizeList extends DataType {
  constructor(listSize, child) {
    super();
    (0, _defineProperty2.default)(this, "listSize", void 0);
    (0, _defineProperty2.default)(this, "children", void 0);
    this.listSize = listSize;
    this.children = [child];
  }
  get typeId() {
    return _enum.Type.FixedSizeList;
  }
  get valueType() {
    return this.children[0].type;
  }
  get valueField() {
    return this.children[0];
  }
  get [_Symbol$toStringTag7]() {
    return 'FixedSizeList';
  }
  toString() {
    return "FixedSizeList[".concat(this.listSize, "]<").concat(this.valueType, ">");
  }
}
exports.FixedSizeList = FixedSizeList;
_Symbol$toStringTag8 = Symbol.toStringTag;
class Struct extends DataType {
  constructor(children) {
    super();
    (0, _defineProperty2.default)(this, "children", void 0);
    this.children = children;
  }
  get typeId() {
    return _enum.Type.Struct;
  }
  toString() {
    return "Struct<{".concat(this.children.map(f => "".concat(f.name, ":").concat(f.type)).join(', '), "}>");
  }
  get [_Symbol$toStringTag8]() {
    return 'Struct';
  }
}
exports.Struct = Struct;
//# sourceMappingURL=type.js.map