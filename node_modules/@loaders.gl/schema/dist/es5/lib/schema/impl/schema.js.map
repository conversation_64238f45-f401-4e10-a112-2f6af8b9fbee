{"version": 3, "file": "schema.js", "names": ["_assert", "require", "<PERSON><PERSON><PERSON>", "constructor", "fields", "metadata", "_defineProperty2", "default", "assert", "Array", "isArray", "checkNames", "Map", "compareTo", "other", "length", "i", "select", "nameMap", "Object", "create", "_len", "arguments", "columnNames", "_key", "name", "<PERSON><PERSON><PERSON>s", "filter", "field", "selectAt", "_len2", "columnIndices", "_key2", "map", "index", "Boolean", "assign", "schema<PERSON>r<PERSON>ields", "otherSchema", "mergeMaps", "fieldMap", "mergedFields", "values", "exports", "usedNames", "console", "warn", "m1", "m2"], "sources": ["../../../../../src/lib/schema/impl/schema.ts"], "sourcesContent": ["import {assert} from '../../utils/assert';\nimport Field from './field';\n\nexport type SchemaMetadata = Map<string, any>;\n\n/**\n * ArrowJS `Schema` API-compatible class for row-based tables (returned from `DataTable`)\n * https://loaders.gl/arrowjs/docs/api-reference/schema\n */\nexport default class Schema {\n  fields: Field[];\n  // TODO - Arrow just allows Map<string, string>\n  metadata: SchemaMetadata;\n\n  constructor(fields: Field[], metadata?: SchemaMetadata) {\n    assert(Array.isArray(fields));\n    checkNames(fields);\n    // For kepler fields, create arrow compatible `Fields` that have kepler fields as `metadata`\n    this.fields = fields;\n    this.metadata = metadata || new Map();\n  }\n\n  // TODO - arrow only seems to compare fields, not metadata\n  compareTo(other: Schema): boolean {\n    if (this.metadata !== other.metadata) {\n      return false;\n    }\n    if (this.fields.length !== other.fields.length) {\n      return false;\n    }\n    for (let i = 0; i < this.fields.length; ++i) {\n      if (!this.fields[i].compareTo(other.fields[i])) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  select(...columnNames: string[]): Schema {\n    // Ensure column names reference valid fields\n    const nameMap = Object.create(null);\n    for (const name of columnNames) {\n      nameMap[name] = true;\n    }\n    const selectedFields = this.fields.filter((field) => nameMap[field.name]);\n    return new Schema(selectedFields, this.metadata);\n  }\n\n  selectAt(...columnIndices: number[]): Schema {\n    // Ensure column indices reference valid fields\n    const selectedFields = columnIndices.map((index) => this.fields[index]).filter(Boolean);\n    return new Schema(selectedFields, this.metadata);\n  }\n\n  assign(schemaOrFields: Schema | Field[]): Schema {\n    let fields: Field[];\n    let metadata: SchemaMetadata = this.metadata;\n\n    if (schemaOrFields instanceof Schema) {\n      const otherSchema = schemaOrFields;\n      fields = otherSchema.fields;\n      metadata = mergeMaps(mergeMaps(new Map(), this.metadata), otherSchema.metadata);\n    } else {\n      fields = schemaOrFields;\n    }\n\n    // Create a merged list of fields, overwrite fields in place, new fields at end\n    const fieldMap: {[key: string]: Field} = Object.create(null);\n\n    for (const field of this.fields) {\n      fieldMap[field.name] = field;\n    }\n\n    for (const field of fields) {\n      fieldMap[field.name] = field;\n    }\n\n    const mergedFields = Object.values(fieldMap);\n\n    return new Schema(mergedFields, metadata);\n  }\n}\n\n// Warn if any duplicated field names\nfunction checkNames(fields: Field[]): void {\n  const usedNames: Record<string, boolean> = {};\n  for (const field of fields) {\n    if (usedNames[field.name]) {\n      // eslint-disable-next-line\n      console.warn('Schema: duplicated field name', field.name, field);\n    }\n    usedNames[field.name] = true;\n  }\n}\n\nfunction mergeMaps<T>(m1: T, m2: T): T {\n  // @ts-ignore\n  return new Map([...(m1 || new Map()), ...(m2 || new Map())]);\n}\n"], "mappings": ";;;;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AASe,MAAMC,MAAM,CAAC;EAK1BC,WAAWA,CAACC,MAAe,EAAEC,QAAyB,EAAE;IAAA,IAAAC,gBAAA,CAAAC,OAAA;IAAA,IAAAD,gBAAA,CAAAC,OAAA;IACtD,IAAAC,cAAM,EAACC,KAAK,CAACC,OAAO,CAACN,MAAM,CAAC,CAAC;IAC7BO,UAAU,CAACP,MAAM,CAAC;IAElB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ,IAAI,IAAIO,GAAG,CAAC,CAAC;EACvC;EAGAC,SAASA,CAACC,KAAa,EAAW;IAChC,IAAI,IAAI,CAACT,QAAQ,KAAKS,KAAK,CAACT,QAAQ,EAAE;MACpC,OAAO,KAAK;IACd;IACA,IAAI,IAAI,CAACD,MAAM,CAACW,MAAM,KAAKD,KAAK,CAACV,MAAM,CAACW,MAAM,EAAE;MAC9C,OAAO,KAAK;IACd;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACZ,MAAM,CAACW,MAAM,EAAE,EAAEC,CAAC,EAAE;MAC3C,IAAI,CAAC,IAAI,CAACZ,MAAM,CAACY,CAAC,CAAC,CAACH,SAAS,CAACC,KAAK,CAACV,MAAM,CAACY,CAAC,CAAC,CAAC,EAAE;QAC9C,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb;EAEAC,MAAMA,CAAA,EAAmC;IAEvC,MAAMC,OAAO,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAAC,SAAAC,IAAA,GAAAC,SAAA,CAAAP,MAAA,EAF5BQ,WAAW,OAAAd,KAAA,CAAAY,IAAA,GAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;MAAXD,WAAW,CAAAC,IAAA,IAAAF,SAAA,CAAAE,IAAA;IAAA;IAGnB,KAAK,MAAMC,IAAI,IAAIF,WAAW,EAAE;MAC9BL,OAAO,CAACO,IAAI,CAAC,GAAG,IAAI;IACtB;IACA,MAAMC,cAAc,GAAG,IAAI,CAACtB,MAAM,CAACuB,MAAM,CAAEC,KAAK,IAAKV,OAAO,CAACU,KAAK,CAACH,IAAI,CAAC,CAAC;IACzE,OAAO,IAAIvB,MAAM,CAACwB,cAAc,EAAE,IAAI,CAACrB,QAAQ,CAAC;EAClD;EAEAwB,QAAQA,CAAA,EAAqC;IAAA,SAAAC,KAAA,GAAAR,SAAA,CAAAP,MAAA,EAAjCgB,aAAa,OAAAtB,KAAA,CAAAqB,KAAA,GAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;MAAbD,aAAa,CAAAC,KAAA,IAAAV,SAAA,CAAAU,KAAA;IAAA;IAEvB,MAAMN,cAAc,GAAGK,aAAa,CAACE,GAAG,CAAEC,KAAK,IAAK,IAAI,CAAC9B,MAAM,CAAC8B,KAAK,CAAC,CAAC,CAACP,MAAM,CAACQ,OAAO,CAAC;IACvF,OAAO,IAAIjC,MAAM,CAACwB,cAAc,EAAE,IAAI,CAACrB,QAAQ,CAAC;EAClD;EAEA+B,MAAMA,CAACC,cAAgC,EAAU;IAC/C,IAAIjC,MAAe;IACnB,IAAIC,QAAwB,GAAG,IAAI,CAACA,QAAQ;IAE5C,IAAIgC,cAAc,YAAYnC,MAAM,EAAE;MACpC,MAAMoC,WAAW,GAAGD,cAAc;MAClCjC,MAAM,GAAGkC,WAAW,CAAClC,MAAM;MAC3BC,QAAQ,GAAGkC,SAAS,CAACA,SAAS,CAAC,IAAI3B,GAAG,CAAC,CAAC,EAAE,IAAI,CAACP,QAAQ,CAAC,EAAEiC,WAAW,CAACjC,QAAQ,CAAC;IACjF,CAAC,MAAM;MACLD,MAAM,GAAGiC,cAAc;IACzB;IAGA,MAAMG,QAAgC,GAAGrB,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAE5D,KAAK,MAAMQ,KAAK,IAAI,IAAI,CAACxB,MAAM,EAAE;MAC/BoC,QAAQ,CAACZ,KAAK,CAACH,IAAI,CAAC,GAAGG,KAAK;IAC9B;IAEA,KAAK,MAAMA,KAAK,IAAIxB,MAAM,EAAE;MAC1BoC,QAAQ,CAACZ,KAAK,CAACH,IAAI,CAAC,GAAGG,KAAK;IAC9B;IAEA,MAAMa,YAAY,GAAGtB,MAAM,CAACuB,MAAM,CAACF,QAAQ,CAAC;IAE5C,OAAO,IAAItC,MAAM,CAACuC,YAAY,EAAEpC,QAAQ,CAAC;EAC3C;AACF;AAACsC,OAAA,CAAApC,OAAA,GAAAL,MAAA;AAGD,SAASS,UAAUA,CAACP,MAAe,EAAQ;EACzC,MAAMwC,SAAkC,GAAG,CAAC,CAAC;EAC7C,KAAK,MAAMhB,KAAK,IAAIxB,MAAM,EAAE;IAC1B,IAAIwC,SAAS,CAAChB,KAAK,CAACH,IAAI,CAAC,EAAE;MAEzBoB,OAAO,CAACC,IAAI,CAAC,+BAA+B,EAAElB,KAAK,CAACH,IAAI,EAAEG,KAAK,CAAC;IAClE;IACAgB,SAAS,CAAChB,KAAK,CAACH,IAAI,CAAC,GAAG,IAAI;EAC9B;AACF;AAEA,SAASc,SAASA,CAAIQ,EAAK,EAAEC,EAAK,EAAK;EAErC,OAAO,IAAIpC,GAAG,CAAC,CAAC,IAAImC,EAAE,IAAI,IAAInC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAIoC,EAAE,IAAI,IAAIpC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9D"}