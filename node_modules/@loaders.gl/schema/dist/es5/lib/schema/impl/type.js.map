{"version": 3, "file": "type.js", "names": ["_enum", "require", "_Symbol$toStringTag", "_Symbol$toStringTag2", "_Symbol$toStringTag3", "_Symbol$toStringTag4", "_Symbol$toStringTag5", "_Symbol$toStringTag6", "_Symbol$toStringTag7", "_Symbol$toStringTag8", "DataType", "isNull", "x", "typeId", "Type", "<PERSON><PERSON>", "isInt", "Int", "isFloat", "Float", "isBinary", "Binary", "isUtf8", "Utf8", "isBool", "Bool", "isDecimal", "Decimal", "isDate", "Date", "isTime", "Time", "isTimestamp", "Timestamp", "isInterval", "Interval", "isList", "List", "isStruct", "Struct", "isUnion", "Union", "isFixedSizeBinary", "FixedSizeBinary", "isFixedSizeList", "FixedSizeList", "isMap", "Map", "isDictionary", "Dictionary", "NONE", "compareTo", "other", "exports", "Symbol", "toStringTag", "toString", "constructor", "isSigned", "bitWidth", "_defineProperty2", "default", "concat", "Int8", "Int16", "Int32", "Int64", "Uint8", "Uint16", "Uint32", "Uint64", "Precision", "HALF", "SINGLE", "DOUBLE", "precision", "Float16", "Float32", "Float64", "DateUnit", "DAY", "MILLISECOND", "unit", "DateDay", "DateMillisecond", "TimeUnit", "SECOND", "MICROSECOND", "NANOSECOND", "TimeSecond", "TimeMillisecond", "timezone", "arguments", "length", "undefined", "TimestampSecond", "TimestampMillisecond", "TimestampMicrosecond", "TimestampNanosecond", "IntervalUnit", "DAY_TIME", "YEAR_MONTH", "IntervalDayTime", "IntervalYearMonth", "listSize", "child", "children", "valueType", "type", "valueField", "map", "f", "name", "join"], "sources": ["../../../../../src/lib/schema/impl/type.ts"], "sourcesContent": ["// This code is adapted from ArrowJS https://github.com/apache/arrow\n// under Apache license http://www.apache.org/licenses/LICENSE-2.0\n\nimport {Type} from './enum';\n\nimport Field from './field';\n\nexport {Type} from './enum';\n\nexport type TypedIntArray =\n  | Int8Array\n  | Uint8Array\n  | Int16Array\n  | Uint16Array\n  | Int32Array\n  | Uint32Array\n  | Int32Array\n  | Uint32Array\n  | Uint8ClampedArray;\n\nexport type TypedFloatArray = Float32Array | Float64Array;\n\nexport type TypedArray = TypedIntArray | TypedFloatArray;\n\nexport type AnyArrayType = Array<any> | TypedIntArray | TypedFloatArray;\n\nexport class DataType {\n  static isNull(x: any): boolean {\n    return x && x.typeId === Type.Null;\n  }\n  static isInt(x: any): boolean {\n    return x && x.typeId === Type.Int;\n  }\n  static isFloat(x: any): boolean {\n    return x && x.typeId === Type.Float;\n  }\n  static isBinary(x: any): boolean {\n    return x && x.typeId === Type.Binary;\n  }\n  static isUtf8(x: any): boolean {\n    return x && x.typeId === Type.Utf8;\n  }\n  static isBool(x: any): boolean {\n    return x && x.typeId === Type.Bool;\n  }\n  static isDecimal(x: any): boolean {\n    return x && x.typeId === Type.Decimal;\n  }\n  static isDate(x: any): boolean {\n    return x && x.typeId === Type.Date;\n  }\n  static isTime(x: any): boolean {\n    return x && x.typeId === Type.Time;\n  }\n  static isTimestamp(x: any): boolean {\n    return x && x.typeId === Type.Timestamp;\n  }\n  static isInterval(x: any): boolean {\n    return x && x.typeId === Type.Interval;\n  }\n  static isList(x: any): boolean {\n    return x && x.typeId === Type.List;\n  }\n  static isStruct(x: any): boolean {\n    return x && x.typeId === Type.Struct;\n  }\n  static isUnion(x: any): boolean {\n    return x && x.typeId === Type.Union;\n  }\n  static isFixedSizeBinary(x: any): boolean {\n    return x && x.typeId === Type.FixedSizeBinary;\n  }\n  static isFixedSizeList(x: any): boolean {\n    return x && x.typeId === Type.FixedSizeList;\n  }\n  static isMap(x: any): boolean {\n    return x && x.typeId === Type.Map;\n  }\n  static isDictionary(x: any): boolean {\n    return x && x.typeId === Type.Dictionary;\n  }\n\n  get typeId(): Type {\n    return Type.NONE;\n  }\n\n  // get ArrayType(): AnyArrayType {\n  //   return Int8Array;\n  // }\n\n  // get ArrayType() { return Array; }\n  compareTo(other: DataType): boolean {\n    // TODO\n    return this === other; // comparer.visit(this, other);\n  }\n}\n\n// NULL\n\nexport class Null extends DataType {\n  get typeId(): Type {\n    return Type.Null;\n  }\n  get [Symbol.toStringTag](): string {\n    return 'Null';\n  }\n  toString(): string {\n    return 'Null';\n  }\n}\n\n// BOOLEANS\n\nexport class Bool extends DataType {\n  get typeId(): Type {\n    return Type.Bool;\n  }\n  // get ArrayType() {\n  //   return Uint8Array;\n  // }\n  get [Symbol.toStringTag](): string {\n    return 'Bool';\n  }\n  toString(): string {\n    return 'Bool';\n  }\n}\n\n// INTS\n\nexport class Int extends DataType {\n  readonly isSigned: boolean;\n  readonly bitWidth: number;\n  constructor(isSigned, bitWidth) {\n    super();\n    this.isSigned = isSigned;\n    this.bitWidth = bitWidth;\n  }\n  get typeId(): Type {\n    return Type.Int;\n  }\n  // get ArrayType() {\n  //   switch (this.bitWidth) {\n  //     case 8:\n  //       return this.isSigned ? Int8Array : Uint8Array;\n  //     case 16:\n  //       return this.isSigned ? Int16Array : Uint16Array;\n  //     case 32:\n  //       return this.isSigned ? Int32Array : Uint32Array;\n  //     case 64:\n  //       return this.isSigned ? Int32Array : Uint32Array;\n  //     default:\n  //       throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`);\n  //   }\n  // }\n  get [Symbol.toStringTag](): string {\n    return 'Int';\n  }\n  toString(): string {\n    return `${this.isSigned ? 'I' : 'Ui'}nt${this.bitWidth}`;\n  }\n}\n\nexport class Int8 extends Int {\n  constructor() {\n    super(true, 8);\n  }\n}\nexport class Int16 extends Int {\n  constructor() {\n    super(true, 16);\n  }\n}\nexport class Int32 extends Int {\n  constructor() {\n    super(true, 32);\n  }\n}\nexport class Int64 extends Int {\n  constructor() {\n    super(true, 64);\n  }\n}\nexport class Uint8 extends Int {\n  constructor() {\n    super(false, 8);\n  }\n}\nexport class Uint16 extends Int {\n  constructor() {\n    super(false, 16);\n  }\n}\nexport class Uint32 extends Int {\n  constructor() {\n    super(false, 32);\n  }\n}\nexport class Uint64 extends Int {\n  constructor() {\n    super(false, 64);\n  }\n}\n\n// FLOATS\n\nconst Precision = {\n  HALF: 16,\n  SINGLE: 32,\n  DOUBLE: 64\n};\n\nexport class Float extends DataType {\n  readonly precision: number;\n  constructor(precision) {\n    super();\n    this.precision = precision;\n  }\n  get typeId(): Type {\n    return Type.Float;\n  }\n  // get ArrayType() {\n  //   switch (this.precision) {\n  //     case Precision.HALF:\n  //       return Uint16Array;\n  //     case Precision.SINGLE:\n  //       return Float32Array;\n  //     case Precision.DOUBLE:\n  //       return Float64Array;\n  //     default:\n  //       throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`);\n  //   }\n  // }\n  get [Symbol.toStringTag](): string {\n    return 'Float';\n  }\n  toString(): string {\n    return `Float${this.precision}`;\n  }\n}\n\nexport class Float16 extends Float {\n  constructor() {\n    super(Precision.HALF);\n  }\n}\nexport class Float32 extends Float {\n  constructor() {\n    super(Precision.SINGLE);\n  }\n}\nexport class Float64 extends Float {\n  constructor() {\n    super(Precision.DOUBLE);\n  }\n}\n\nexport class Binary extends DataType {\n  constructor() {\n    super();\n  }\n  get typeId() {\n    return Type.Binary;\n  }\n  toString() {\n    return 'Binary';\n  }\n  get [Symbol.toStringTag]() {\n    return 'Binary';\n  }\n}\n\n// STRINGS\n\nexport class Utf8 extends DataType {\n  get typeId(): Type {\n    return Type.Utf8;\n  }\n  // get ArrayType() {\n  //   return Uint8Array;\n  // }\n  get [Symbol.toStringTag](): string {\n    return 'Utf8';\n  }\n  toString(): string {\n    return 'Utf8';\n  }\n}\n\n// DATES, TIMES AND INTERVALS\n\nconst DateUnit = {\n  DAY: 0,\n  MILLISECOND: 1\n};\n\nexport class Date extends DataType {\n  readonly unit: number;\n  constructor(unit) {\n    super();\n    this.unit = unit;\n  }\n  get typeId(): Type {\n    return Type.Date;\n  }\n  // get ArrayType() {\n  //   return Int32Array;\n  // }\n  get [Symbol.toStringTag](): string {\n    return 'Date';\n  }\n  toString(): string {\n    return `Date${(this.unit + 1) * 32}<${DateUnit[this.unit]}>`;\n  }\n}\n\nexport class DateDay extends Date {\n  constructor() {\n    super(DateUnit.DAY);\n  }\n}\nexport class DateMillisecond extends Date {\n  constructor() {\n    super(DateUnit.MILLISECOND);\n  }\n}\n\nconst TimeUnit = {\n  SECOND: 1,\n  MILLISECOND: 1e3,\n  MICROSECOND: 1e6,\n  NANOSECOND: 1e9\n};\n\nexport class Time extends DataType {\n  readonly unit: any;\n  readonly bitWidth: number;\n\n  constructor(unit: any, bitWidth: number) {\n    super();\n    this.unit = unit;\n    this.bitWidth = bitWidth;\n  }\n  get typeId(): Type {\n    return Type.Time;\n  }\n  toString(): string {\n    return `Time${this.bitWidth}<${TimeUnit[this.unit]}>`;\n  }\n  get [Symbol.toStringTag](): string {\n    return 'Time';\n  }\n  // get ArrayType() {\n  //   return Int32Array;\n  // }\n}\n\nexport class TimeSecond extends Time {\n  constructor() {\n    super(TimeUnit.SECOND, 32);\n  }\n}\nexport class TimeMillisecond extends Time {\n  constructor() {\n    super(TimeUnit.MILLISECOND, 32);\n  }\n}\n// export class TimeMicrosecond extends Time { constructor() { super(TimeUnit.MICROSECOND, 64); } }\n// export class TimeNanosecond extends Time { constructor() { super(TimeUnit.NANOSECOND, 64); } }\n\nexport class Timestamp extends DataType {\n  readonly unit: any;\n  readonly timezone: any;\n\n  constructor(unit: any, timezone = null) {\n    super();\n    this.unit = unit;\n    this.timezone = timezone;\n  }\n  get typeId(): Type {\n    return Type.Timestamp;\n  }\n  // get ArrayType() {\n  //   return Int32Array;\n  // }\n  get [Symbol.toStringTag](): string {\n    return 'Timestamp';\n  }\n  toString(): string {\n    return `Timestamp<${TimeUnit[this.unit]}${this.timezone ? `, ${this.timezone}` : ''}>`;\n  }\n}\n\nexport class TimestampSecond extends Timestamp {\n  constructor(timezone = null) {\n    super(TimeUnit.SECOND, timezone);\n  }\n}\nexport class TimestampMillisecond extends Timestamp {\n  constructor(timezone = null) {\n    super(TimeUnit.MILLISECOND, timezone);\n  }\n}\nexport class TimestampMicrosecond extends Timestamp {\n  constructor(timezone = null) {\n    super(TimeUnit.MICROSECOND, timezone);\n  }\n}\nexport class TimestampNanosecond extends Timestamp {\n  constructor(timezone = null) {\n    super(TimeUnit.NANOSECOND, timezone);\n  }\n}\n\nconst IntervalUnit = {\n  DAY_TIME: 0,\n  YEAR_MONTH: 1\n};\n\nexport class Interval extends DataType {\n  readonly unit: number;\n  constructor(unit: number) {\n    super();\n    this.unit = unit;\n  }\n  get typeId(): Type {\n    return Type.Interval;\n  }\n  // get ArrayType() {\n  //   return Int32Array;\n  // }\n  get [Symbol.toStringTag](): string {\n    return 'Interval';\n  }\n  toString(): string {\n    return `Interval<${IntervalUnit[this.unit]}>`;\n  }\n}\n\nexport class IntervalDayTime extends Interval {\n  constructor() {\n    super(IntervalUnit.DAY_TIME);\n  }\n}\nexport class IntervalYearMonth extends Interval {\n  constructor() {\n    super(IntervalUnit.YEAR_MONTH);\n  }\n}\n\nexport class FixedSizeList extends DataType {\n  readonly listSize: number;\n  readonly children: Field[];\n\n  constructor(listSize: number, child: Field) {\n    super();\n    this.listSize = listSize;\n    this.children = [child];\n  }\n  get typeId(): Type {\n    return Type.FixedSizeList;\n  }\n  get valueType() {\n    return this.children[0].type;\n  }\n  get valueField() {\n    return this.children[0];\n  }\n  // get ArrayType() {\n  //   return this.valueType.ArrayType;\n  // }\n  get [Symbol.toStringTag](): string {\n    return 'FixedSizeList';\n  }\n  toString(): string {\n    return `FixedSizeList[${this.listSize}]<${this.valueType}>`;\n  }\n}\n\nexport class Struct extends DataType {\n  public readonly children: Field[];\n\n  constructor(children: Field[]) {\n    super();\n    this.children = children;\n  }\n\n  public get typeId() {\n    return Type.Struct;\n  }\n  public toString() {\n    return `Struct<{${this.children.map((f) => `${f.name}:${f.type}`).join(', ')}}>`;\n  }\n  get [Symbol.toStringTag](): string {\n    return 'Struct';\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAGA,IAAAA,KAAA,GAAAC,OAAA;AAA4B,IAAAC,mBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA;AAuBrB,MAAMC,QAAQ,CAAC;EACpB,OAAOC,MAAMA,CAACC,CAAM,EAAW;IAC7B,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKC,UAAI,CAACC,IAAI;EACpC;EACA,OAAOC,KAAKA,CAACJ,CAAM,EAAW;IAC5B,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKC,UAAI,CAACG,GAAG;EACnC;EACA,OAAOC,OAAOA,CAACN,CAAM,EAAW;IAC9B,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKC,UAAI,CAACK,KAAK;EACrC;EACA,OAAOC,QAAQA,CAACR,CAAM,EAAW;IAC/B,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKC,UAAI,CAACO,MAAM;EACtC;EACA,OAAOC,MAAMA,CAACV,CAAM,EAAW;IAC7B,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKC,UAAI,CAACS,IAAI;EACpC;EACA,OAAOC,MAAMA,CAACZ,CAAM,EAAW;IAC7B,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKC,UAAI,CAACW,IAAI;EACpC;EACA,OAAOC,SAASA,CAACd,CAAM,EAAW;IAChC,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKC,UAAI,CAACa,OAAO;EACvC;EACA,OAAOC,MAAMA,CAAChB,CAAM,EAAW;IAC7B,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKC,UAAI,CAACe,IAAI;EACpC;EACA,OAAOC,MAAMA,CAAClB,CAAM,EAAW;IAC7B,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKC,UAAI,CAACiB,IAAI;EACpC;EACA,OAAOC,WAAWA,CAACpB,CAAM,EAAW;IAClC,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKC,UAAI,CAACmB,SAAS;EACzC;EACA,OAAOC,UAAUA,CAACtB,CAAM,EAAW;IACjC,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKC,UAAI,CAACqB,QAAQ;EACxC;EACA,OAAOC,MAAMA,CAACxB,CAAM,EAAW;IAC7B,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKC,UAAI,CAACuB,IAAI;EACpC;EACA,OAAOC,QAAQA,CAAC1B,CAAM,EAAW;IAC/B,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKC,UAAI,CAACyB,MAAM;EACtC;EACA,OAAOC,OAAOA,CAAC5B,CAAM,EAAW;IAC9B,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKC,UAAI,CAAC2B,KAAK;EACrC;EACA,OAAOC,iBAAiBA,CAAC9B,CAAM,EAAW;IACxC,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKC,UAAI,CAAC6B,eAAe;EAC/C;EACA,OAAOC,eAAeA,CAAChC,CAAM,EAAW;IACtC,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKC,UAAI,CAAC+B,aAAa;EAC7C;EACA,OAAOC,KAAKA,CAAClC,CAAM,EAAW;IAC5B,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKC,UAAI,CAACiC,GAAG;EACnC;EACA,OAAOC,YAAYA,CAACpC,CAAM,EAAW;IACnC,OAAOA,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKC,UAAI,CAACmC,UAAU;EAC1C;EAEA,IAAIpC,MAAMA,CAAA,EAAS;IACjB,OAAOC,UAAI,CAACoC,IAAI;EAClB;EAOAC,SAASA,CAACC,KAAe,EAAW;IAElC,OAAO,IAAI,KAAKA,KAAK;EACvB;AACF;AAACC,OAAA,CAAA3C,QAAA,GAAAA,QAAA;AAIM,MAAMK,IAAI,SAASL,QAAQ,CAAC;EACjC,IAAIG,MAAMA,CAAA,EAAS;IACjB,OAAOC,UAAI,CAACC,IAAI;EAClB;EACA,KAAKuC,MAAM,CAACC,WAAW,IAAY;IACjC,OAAO,MAAM;EACf;EACAC,QAAQA,CAAA,EAAW;IACjB,OAAO,MAAM;EACf;AACF;AAACH,OAAA,CAAAtC,IAAA,GAAAA,IAAA;AAIM,MAAMU,IAAI,SAASf,QAAQ,CAAC;EACjC,IAAIG,MAAMA,CAAA,EAAS;IACjB,OAAOC,UAAI,CAACW,IAAI;EAClB;EAIA,KAAK6B,MAAM,CAACC,WAAW,IAAY;IACjC,OAAO,MAAM;EACf;EACAC,QAAQA,CAAA,EAAW;IACjB,OAAO,MAAM;EACf;AACF;AAACH,OAAA,CAAA5B,IAAA,GAAAA,IAAA;AAAAvB,mBAAA,GA6BMoD,MAAM,CAACC,WAAW;AAzBlB,MAAMtC,GAAG,SAASP,QAAQ,CAAC;EAGhC+C,WAAWA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IAC9B,KAAK,CAAC,CAAC;IAAC,IAAAC,gBAAA,CAAAC,OAAA;IAAA,IAAAD,gBAAA,CAAAC,OAAA;IACR,IAAI,CAACH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC1B;EACA,IAAI9C,MAAMA,CAAA,EAAS;IACjB,OAAOC,UAAI,CAACG,GAAG;EACjB;EAeA,KAAAf,mBAAA,IAAmC;IACjC,OAAO,KAAK;EACd;EACAsD,QAAQA,CAAA,EAAW;IACjB,UAAAM,MAAA,CAAU,IAAI,CAACJ,QAAQ,GAAG,GAAG,GAAG,IAAI,QAAAI,MAAA,CAAK,IAAI,CAACH,QAAQ;EACxD;AACF;AAACN,OAAA,CAAApC,GAAA,GAAAA,GAAA;AAEM,MAAM8C,IAAI,SAAS9C,GAAG,CAAC;EAC5BwC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;EAChB;AACF;AAACJ,OAAA,CAAAU,IAAA,GAAAA,IAAA;AACM,MAAMC,KAAK,SAAS/C,GAAG,CAAC;EAC7BwC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;EACjB;AACF;AAACJ,OAAA,CAAAW,KAAA,GAAAA,KAAA;AACM,MAAMC,KAAK,SAAShD,GAAG,CAAC;EAC7BwC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;EACjB;AACF;AAACJ,OAAA,CAAAY,KAAA,GAAAA,KAAA;AACM,MAAMC,KAAK,SAASjD,GAAG,CAAC;EAC7BwC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;EACjB;AACF;AAACJ,OAAA,CAAAa,KAAA,GAAAA,KAAA;AACM,MAAMC,KAAK,SAASlD,GAAG,CAAC;EAC7BwC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;EACjB;AACF;AAACJ,OAAA,CAAAc,KAAA,GAAAA,KAAA;AACM,MAAMC,MAAM,SAASnD,GAAG,CAAC;EAC9BwC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC;EAClB;AACF;AAACJ,OAAA,CAAAe,MAAA,GAAAA,MAAA;AACM,MAAMC,MAAM,SAASpD,GAAG,CAAC;EAC9BwC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC;EAClB;AACF;AAACJ,OAAA,CAAAgB,MAAA,GAAAA,MAAA;AACM,MAAMC,MAAM,SAASrD,GAAG,CAAC;EAC9BwC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC;EAClB;AACF;AAACJ,OAAA,CAAAiB,MAAA,GAAAA,MAAA;AAID,MAAMC,SAAS,GAAG;EAChBC,IAAI,EAAE,EAAE;EACRC,MAAM,EAAE,EAAE;EACVC,MAAM,EAAE;AACV,CAAC;AAACvE,oBAAA,GAuBKmD,MAAM,CAACC,WAAW;AArBlB,MAAMpC,KAAK,SAAST,QAAQ,CAAC;EAElC+C,WAAWA,CAACkB,SAAS,EAAE;IACrB,KAAK,CAAC,CAAC;IAAC,IAAAf,gBAAA,CAAAC,OAAA;IACR,IAAI,CAACc,SAAS,GAAGA,SAAS;EAC5B;EACA,IAAI9D,MAAMA,CAAA,EAAS;IACjB,OAAOC,UAAI,CAACK,KAAK;EACnB;EAaA,KAAAhB,oBAAA,IAAmC;IACjC,OAAO,OAAO;EAChB;EACAqD,QAAQA,CAAA,EAAW;IACjB,eAAAM,MAAA,CAAe,IAAI,CAACa,SAAS;EAC/B;AACF;AAACtB,OAAA,CAAAlC,KAAA,GAAAA,KAAA;AAEM,MAAMyD,OAAO,SAASzD,KAAK,CAAC;EACjCsC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAACc,SAAS,CAACC,IAAI,CAAC;EACvB;AACF;AAACnB,OAAA,CAAAuB,OAAA,GAAAA,OAAA;AACM,MAAMC,OAAO,SAAS1D,KAAK,CAAC;EACjCsC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAACc,SAAS,CAACE,MAAM,CAAC;EACzB;AACF;AAACpB,OAAA,CAAAwB,OAAA,GAAAA,OAAA;AACM,MAAMC,OAAO,SAAS3D,KAAK,CAAC;EACjCsC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAACc,SAAS,CAACG,MAAM,CAAC;EACzB;AACF;AAACrB,OAAA,CAAAyB,OAAA,GAAAA,OAAA;AAEM,MAAMzD,MAAM,SAASX,QAAQ,CAAC;EACnC+C,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;EACT;EACA,IAAI5C,MAAMA,CAAA,EAAG;IACX,OAAOC,UAAI,CAACO,MAAM;EACpB;EACAmC,QAAQA,CAAA,EAAG;IACT,OAAO,QAAQ;EACjB;EACA,KAAKF,MAAM,CAACC,WAAW,IAAI;IACzB,OAAO,QAAQ;EACjB;AACF;AAACF,OAAA,CAAAhC,MAAA,GAAAA,MAAA;AAIM,MAAME,IAAI,SAASb,QAAQ,CAAC;EACjC,IAAIG,MAAMA,CAAA,EAAS;IACjB,OAAOC,UAAI,CAACS,IAAI;EAClB;EAIA,KAAK+B,MAAM,CAACC,WAAW,IAAY;IACjC,OAAO,MAAM;EACf;EACAC,QAAQA,CAAA,EAAW;IACjB,OAAO,MAAM;EACf;AACF;AAACH,OAAA,CAAA9B,IAAA,GAAAA,IAAA;AAID,MAAMwD,QAAQ,GAAG;EACfC,GAAG,EAAE,CAAC;EACNC,WAAW,EAAE;AACf,CAAC;AAAC7E,oBAAA,GAcKkD,MAAM,CAACC,WAAW;AAZlB,MAAM1B,IAAI,SAASnB,QAAQ,CAAC;EAEjC+C,WAAWA,CAACyB,IAAI,EAAE;IAChB,KAAK,CAAC,CAAC;IAAC,IAAAtB,gBAAA,CAAAC,OAAA;IACR,IAAI,CAACqB,IAAI,GAAGA,IAAI;EAClB;EACA,IAAIrE,MAAMA,CAAA,EAAS;IACjB,OAAOC,UAAI,CAACe,IAAI;EAClB;EAIA,KAAAzB,oBAAA,IAAmC;IACjC,OAAO,MAAM;EACf;EACAoD,QAAQA,CAAA,EAAW;IACjB,cAAAM,MAAA,CAAc,CAAC,IAAI,CAACoB,IAAI,GAAG,CAAC,IAAI,EAAE,OAAApB,MAAA,CAAIiB,QAAQ,CAAC,IAAI,CAACG,IAAI,CAAC;EAC3D;AACF;AAAC7B,OAAA,CAAAxB,IAAA,GAAAA,IAAA;AAEM,MAAMsD,OAAO,SAAStD,IAAI,CAAC;EAChC4B,WAAWA,CAAA,EAAG;IACZ,KAAK,CAACsB,QAAQ,CAACC,GAAG,CAAC;EACrB;AACF;AAAC3B,OAAA,CAAA8B,OAAA,GAAAA,OAAA;AACM,MAAMC,eAAe,SAASvD,IAAI,CAAC;EACxC4B,WAAWA,CAAA,EAAG;IACZ,KAAK,CAACsB,QAAQ,CAACE,WAAW,CAAC;EAC7B;AACF;AAAC5B,OAAA,CAAA+B,eAAA,GAAAA,eAAA;AAED,MAAMC,QAAQ,GAAG;EACfC,MAAM,EAAE,CAAC;EACTL,WAAW,EAAE,GAAG;EAChBM,WAAW,EAAE,GAAG;EAChBC,UAAU,EAAE;AACd,CAAC;AAACnF,oBAAA,GAiBKiD,MAAM,CAACC,WAAW;AAflB,MAAMxB,IAAI,SAASrB,QAAQ,CAAC;EAIjC+C,WAAWA,CAACyB,IAAS,EAAEvB,QAAgB,EAAE;IACvC,KAAK,CAAC,CAAC;IAAC,IAAAC,gBAAA,CAAAC,OAAA;IAAA,IAAAD,gBAAA,CAAAC,OAAA;IACR,IAAI,CAACqB,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACvB,QAAQ,GAAGA,QAAQ;EAC1B;EACA,IAAI9C,MAAMA,CAAA,EAAS;IACjB,OAAOC,UAAI,CAACiB,IAAI;EAClB;EACAyB,QAAQA,CAAA,EAAW;IACjB,cAAAM,MAAA,CAAc,IAAI,CAACH,QAAQ,OAAAG,MAAA,CAAIuB,QAAQ,CAAC,IAAI,CAACH,IAAI,CAAC;EACpD;EACA,KAAA7E,oBAAA,IAAmC;IACjC,OAAO,MAAM;EACf;AAIF;AAACgD,OAAA,CAAAtB,IAAA,GAAAA,IAAA;AAEM,MAAM0D,UAAU,SAAS1D,IAAI,CAAC;EACnC0B,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC4B,QAAQ,CAACC,MAAM,EAAE,EAAE,CAAC;EAC5B;AACF;AAACjC,OAAA,CAAAoC,UAAA,GAAAA,UAAA;AACM,MAAMC,eAAe,SAAS3D,IAAI,CAAC;EACxC0B,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC4B,QAAQ,CAACJ,WAAW,EAAE,EAAE,CAAC;EACjC;AACF;AAAC5B,OAAA,CAAAqC,eAAA,GAAAA,eAAA;AAAApF,oBAAA,GAmBMgD,MAAM,CAACC,WAAW;AAflB,MAAMtB,SAAS,SAASvB,QAAQ,CAAC;EAItC+C,WAAWA,CAACyB,IAAS,EAAmB;IAAA,IAAjBS,QAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IACpC,KAAK,CAAC,CAAC;IAAC,IAAAhC,gBAAA,CAAAC,OAAA;IAAA,IAAAD,gBAAA,CAAAC,OAAA;IACR,IAAI,CAACqB,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACS,QAAQ,GAAGA,QAAQ;EAC1B;EACA,IAAI9E,MAAMA,CAAA,EAAS;IACjB,OAAOC,UAAI,CAACmB,SAAS;EACvB;EAIA,KAAA3B,oBAAA,IAAmC;IACjC,OAAO,WAAW;EACpB;EACAkD,QAAQA,CAAA,EAAW;IACjB,oBAAAM,MAAA,CAAoBuB,QAAQ,CAAC,IAAI,CAACH,IAAI,CAAC,EAAApB,MAAA,CAAG,IAAI,CAAC6B,QAAQ,QAAA7B,MAAA,CAAQ,IAAI,CAAC6B,QAAQ,IAAK,EAAE;EACrF;AACF;AAACtC,OAAA,CAAApB,SAAA,GAAAA,SAAA;AAEM,MAAM8D,eAAe,SAAS9D,SAAS,CAAC;EAC7CwB,WAAWA,CAAA,EAAkB;IAAA,IAAjBkC,QAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IACzB,KAAK,CAACP,QAAQ,CAACC,MAAM,EAAEK,QAAQ,CAAC;EAClC;AACF;AAACtC,OAAA,CAAA0C,eAAA,GAAAA,eAAA;AACM,MAAMC,oBAAoB,SAAS/D,SAAS,CAAC;EAClDwB,WAAWA,CAAA,EAAkB;IAAA,IAAjBkC,QAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IACzB,KAAK,CAACP,QAAQ,CAACJ,WAAW,EAAEU,QAAQ,CAAC;EACvC;AACF;AAACtC,OAAA,CAAA2C,oBAAA,GAAAA,oBAAA;AACM,MAAMC,oBAAoB,SAAShE,SAAS,CAAC;EAClDwB,WAAWA,CAAA,EAAkB;IAAA,IAAjBkC,QAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IACzB,KAAK,CAACP,QAAQ,CAACE,WAAW,EAAEI,QAAQ,CAAC;EACvC;AACF;AAACtC,OAAA,CAAA4C,oBAAA,GAAAA,oBAAA;AACM,MAAMC,mBAAmB,SAASjE,SAAS,CAAC;EACjDwB,WAAWA,CAAA,EAAkB;IAAA,IAAjBkC,QAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IACzB,KAAK,CAACP,QAAQ,CAACG,UAAU,EAAEG,QAAQ,CAAC;EACtC;AACF;AAACtC,OAAA,CAAA6C,mBAAA,GAAAA,mBAAA;AAED,MAAMC,YAAY,GAAG;EACnBC,QAAQ,EAAE,CAAC;EACXC,UAAU,EAAE;AACd,CAAC;AAAC9F,oBAAA,GAcK+C,MAAM,CAACC,WAAW;AAZlB,MAAMpB,QAAQ,SAASzB,QAAQ,CAAC;EAErC+C,WAAWA,CAACyB,IAAY,EAAE;IACxB,KAAK,CAAC,CAAC;IAAC,IAAAtB,gBAAA,CAAAC,OAAA;IACR,IAAI,CAACqB,IAAI,GAAGA,IAAI;EAClB;EACA,IAAIrE,MAAMA,CAAA,EAAS;IACjB,OAAOC,UAAI,CAACqB,QAAQ;EACtB;EAIA,KAAA5B,oBAAA,IAAmC;IACjC,OAAO,UAAU;EACnB;EACAiD,QAAQA,CAAA,EAAW;IACjB,mBAAAM,MAAA,CAAmBqC,YAAY,CAAC,IAAI,CAACjB,IAAI,CAAC;EAC5C;AACF;AAAC7B,OAAA,CAAAlB,QAAA,GAAAA,QAAA;AAEM,MAAMmE,eAAe,SAASnE,QAAQ,CAAC;EAC5CsB,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC0C,YAAY,CAACC,QAAQ,CAAC;EAC9B;AACF;AAAC/C,OAAA,CAAAiD,eAAA,GAAAA,eAAA;AACM,MAAMC,iBAAiB,SAASpE,QAAQ,CAAC;EAC9CsB,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC0C,YAAY,CAACE,UAAU,CAAC;EAChC;AACF;AAAChD,OAAA,CAAAkD,iBAAA,GAAAA,iBAAA;AAAA/F,oBAAA,GAuBM8C,MAAM,CAACC,WAAW;AArBlB,MAAMV,aAAa,SAASnC,QAAQ,CAAC;EAI1C+C,WAAWA,CAAC+C,QAAgB,EAAEC,KAAY,EAAE;IAC1C,KAAK,CAAC,CAAC;IAAC,IAAA7C,gBAAA,CAAAC,OAAA;IAAA,IAAAD,gBAAA,CAAAC,OAAA;IACR,IAAI,CAAC2C,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,QAAQ,GAAG,CAACD,KAAK,CAAC;EACzB;EACA,IAAI5F,MAAMA,CAAA,EAAS;IACjB,OAAOC,UAAI,CAAC+B,aAAa;EAC3B;EACA,IAAI8D,SAASA,CAAA,EAAG;IACd,OAAO,IAAI,CAACD,QAAQ,CAAC,CAAC,CAAC,CAACE,IAAI;EAC9B;EACA,IAAIC,UAAUA,CAAA,EAAG;IACf,OAAO,IAAI,CAACH,QAAQ,CAAC,CAAC,CAAC;EACzB;EAIA,KAAAlG,oBAAA,IAAmC;IACjC,OAAO,eAAe;EACxB;EACAgD,QAAQA,CAAA,EAAW;IACjB,wBAAAM,MAAA,CAAwB,IAAI,CAAC0C,QAAQ,QAAA1C,MAAA,CAAK,IAAI,CAAC6C,SAAS;EAC1D;AACF;AAACtD,OAAA,CAAAR,aAAA,GAAAA,aAAA;AAAApC,oBAAA,GAgBM6C,MAAM,CAACC,WAAW;AAdlB,MAAMhB,MAAM,SAAS7B,QAAQ,CAAC;EAGnC+C,WAAWA,CAACiD,QAAiB,EAAE;IAC7B,KAAK,CAAC,CAAC;IAAC,IAAA9C,gBAAA,CAAAC,OAAA;IACR,IAAI,CAAC6C,QAAQ,GAAGA,QAAQ;EAC1B;EAEA,IAAW7F,MAAMA,CAAA,EAAG;IAClB,OAAOC,UAAI,CAACyB,MAAM;EACpB;EACOiB,QAAQA,CAAA,EAAG;IAChB,kBAAAM,MAAA,CAAkB,IAAI,CAAC4C,QAAQ,CAACI,GAAG,CAAEC,CAAC,OAAAjD,MAAA,CAAQiD,CAAC,CAACC,IAAI,OAAAlD,MAAA,CAAIiD,CAAC,CAACH,IAAI,CAAE,CAAC,CAACK,IAAI,CAAC,IAAI,CAAC;EAC9E;EACA,KAAAxG,oBAAA,IAAmC;IACjC,OAAO,QAAQ;EACjB;AACF;AAAC4C,OAAA,CAAAd,MAAA,GAAAA,MAAA"}