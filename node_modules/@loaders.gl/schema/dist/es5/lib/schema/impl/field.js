"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
class Field {
  constructor(name, type) {
    let nullable = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
    let metadata = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : new Map();
    (0, _defineProperty2.default)(this, "name", void 0);
    (0, _defineProperty2.default)(this, "type", void 0);
    (0, _defineProperty2.default)(this, "nullable", void 0);
    (0, _defineProperty2.default)(this, "metadata", void 0);
    this.name = name;
    this.type = type;
    this.nullable = nullable;
    this.metadata = metadata;
  }
  get typeId() {
    return this.type && this.type.typeId;
  }
  clone() {
    return new Field(this.name, this.type, this.nullable, this.metadata);
  }
  compareTo(other) {
    return this.name === other.name && this.type === other.type && this.nullable === other.nullable && this.metadata === other.metadata;
  }
  toString() {
    return "".concat(this.type).concat(this.nullable ? ', nullable' : '').concat(this.metadata ? ", metadata: ".concat(this.metadata) : '');
  }
}
exports.default = Field;
//# sourceMappingURL=field.js.map