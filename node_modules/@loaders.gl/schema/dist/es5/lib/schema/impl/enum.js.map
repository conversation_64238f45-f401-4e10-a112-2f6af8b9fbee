{"version": 3, "file": "enum.js", "names": ["Type", "exports"], "sources": ["../../../../../src/lib/schema/impl/enum.ts"], "sourcesContent": ["// This code is adapted from ArrowJS https://github.com/apache/arrow\n// under Apache license http://www.apache.org/licenses/LICENSE-2.0\n\n/**\n * Main data type enumeration.\n *\n * Data types in this library are all *logical*. They can be expressed as\n * either a primitive physical type (bytes or bits of some fixed size), a\n * nested type consisting of other data types, or another data type (e.g. a\n * timestamp encoded as an int64).\n *\n * **Note**: Only enum values 0-17 (NONE through Map) are written to an Arrow\n * IPC payload.\n *\n * The rest of the values are specified here so TypeScript can narrow the type\n * signatures further beyond the base Arrow Types. The Arrow DataTypes include\n * metadata like `bitWidth` that impact the type signatures of the values we\n * accept and return.\n *\n * For example, the `Int8Vector` reads 1-byte numbers from an `Int8Array`, an\n * `Int32Vector` reads a 4-byte number from an `Int32Array`, and an `Int64Vector`\n * reads a pair of 4-byte lo, hi 32-bit integers as a zero-copy slice from the\n * underlying `Int32Array`.\n *\n * Library consumers benefit by knowing the narrowest type, since we can ensure\n * the types across all public methods are propagated, and never bail to `any`.\n * These values are _never_ used at runtime, and they will _never_ be written\n * to the flatbuffers metadata of serialized Arrow IPC payloads.\n */\nexport enum Type {\n  /** The default placeholder type */\n  NONE = 0,\n  /** A NULL type having no physical storage */\n  Null = 1,\n  /** Signed or unsigned 8, 16, 32, or 64-bit little-endian integer */\n  Int = 2,\n  /** 2, 4, or 8-byte floating point value */\n  Float = 3,\n  /** Variable-length bytes (no guarantee of UTF8-ness) */\n  Binary = 4,\n  /** UTF8 variable-length string as List<Char> */\n  Utf8 = 5,\n  /** Boolean as 1 bit, LSB bit-packed ordering */\n  Bool = 6,\n  /** Precision-and-scale-based decimal type. Storage type depends on the parameters. */\n  Decimal = 7,\n  /** int32_t days or int64_t milliseconds since the UNIX epoch */\n  Date = 8,\n  /** Time as signed 32 or 64-bit integer, representing either seconds, milliseconds, microseconds, or nanoseconds since midnight since midnight */\n  Time = 9,\n  /** Exact timestamp encoded with int64 since UNIX epoch (Default unit millisecond) */\n  Timestamp = 10,\n  /** YEAR_MONTH or DAY_TIME interval in SQL style */\n  Interval = 11,\n  /** A list of some logical data type */\n  List = 12,\n  /** Struct of logical types */\n  Struct = 13,\n  /** Union of logical types */\n  Union = 14,\n  /** Fixed-size binary. Each value occupies the same number of bytes */\n  FixedSizeBinary = 15,\n  /** Fixed-size list. Each value occupies the same number of bytes */\n  FixedSizeList = 16,\n  /** Map of named logical types */\n  Map = 17,\n\n  /** Dictionary aka Category type */\n  Dictionary = -1,\n  Int8 = -2,\n  Int16 = -3,\n  Int32 = -4,\n  Int64 = -5,\n  Uint8 = -6,\n  Uint16 = -7,\n  Uint32 = -8,\n  Uint64 = -9,\n  Float16 = -10,\n  Float32 = -11,\n  Float64 = -12,\n  DateDay = -13,\n  DateMillisecond = -14,\n  TimestampSecond = -15,\n  TimestampMillisecond = -16,\n  TimestampMicrosecond = -17,\n  TimestampNanosecond = -18,\n  TimeSecond = -19,\n  TimeMillisecond = -20,\n  TimeMicrosecond = -21,\n  TimeNanosecond = -22,\n  DenseUnion = -23,\n  SparseUnion = -24,\n  IntervalDayTime = -25,\n  IntervalYearMonth = -26\n}\n"], "mappings": ";;;;;;IA6BYA,IAAI,aAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAA,OAAJA,IAAI;AAAA;AAAAC,OAAA,CAAAD,IAAA,GAAAA,IAAA"}