{"version": 3, "file": "schema.js", "names": ["_schema", "_interopRequireDefault", "require", "_field", "_type"], "sources": ["../../../../src/lib/schema/schema.ts"], "sourcesContent": ["/*\nexport {\n  <PERSON>hema,\n  <PERSON>,\n  DataType,\n  Null,\n  Bool,\n  Int,\n  Int8,\n  Int16,\n  Int32,\n  Int64,\n  Uint8,\n  Uint16,\n  Uint32,\n  <PERSON>int64,\n  Float,\n  Float16,\n  <PERSON>loat32,\n  Float64,\n  Binary,\n  Utf8,\n  Date,\n  DateDay,\n  DateMillisecond,\n  Time,\n  TimeSecond,\n  TimeMillisecond,\n  TimeMicrosecond,\n  TimeNanosecond,\n  Timestamp,\n  TimestampSecond,\n  TimestampMillisecond,\n  TimestampMicrosecond,\n  TimestampNanosecond,\n  Interval,\n  IntervalDayTime,\n  IntervalYearMonth,\n  FixedSizeList\n} from 'apache-arrow';\n*/\nexport {default as Schema} from './impl/schema';\nexport {default as Field} from './impl/field';\nexport {Type} from './impl/type';\nexport {\n  DataType,\n  Null,\n  Bool,\n  Int,\n  Int8,\n  Int16,\n  Int32,\n  Int64,\n  Uint8,\n  Uint16,\n  Uint32,\n  Uint64,\n  Float,\n  Float16,\n  Float32,\n  Float64,\n  Binary,\n  Utf8,\n  Date,\n  DateDay,\n  DateMillisecond,\n  Time,\n  TimeSecond,\n  TimeMillisecond,\n  // TimeMicrosecond,\n  // TimeNanosecond,\n  Timestamp,\n  TimestampSecond,\n  TimestampMillisecond,\n  TimestampMicrosecond,\n  TimestampNanosecond,\n  Interval,\n  IntervalDayTime,\n  IntervalYearMonth,\n  FixedSizeList,\n  Struct\n} from './impl/type';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA"}