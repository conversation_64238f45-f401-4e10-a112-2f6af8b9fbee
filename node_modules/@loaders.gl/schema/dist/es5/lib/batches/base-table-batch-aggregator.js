"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
const DEFAULT_ROW_COUNT = 100;
class RowTableBatchAggregator {
  constructor(schema, options) {
    (0, _defineProperty2.default)(this, "schema", void 0);
    (0, _defineProperty2.default)(this, "options", void 0);
    (0, _defineProperty2.default)(this, "length", 0);
    (0, _defineProperty2.default)(this, "rows", null);
    (0, _defineProperty2.default)(this, "cursor", 0);
    (0, _defineProperty2.default)(this, "_headers", []);
    this.options = options;
    this.schema = schema;
    if (!Array.isArray(schema)) {
      this._headers = [];
      for (const key in schema) {
        this._headers[schema[key].index] = schema[key].name;
      }
    }
  }
  rowCount() {
    return this.length;
  }
  addArrayRow(row, cursor) {
    if (Number.isFinite(cursor)) {
      this.cursor = cursor;
    }
    this.rows = this.rows || new Array(DEFAULT_ROW_COUNT);
    this.rows[this.length] = row;
    this.length++;
  }
  addObjectRow(row, cursor) {
    if (Number.isFinite(cursor)) {
      this.cursor = cursor;
    }
    this.rows = this.rows || new Array(DEFAULT_ROW_COUNT);
    this.rows[this.length] = row;
    this.length++;
  }
  getBatch() {
    let rows = this.rows;
    if (!rows) {
      return null;
    }
    rows = rows.slice(0, this.length);
    this.rows = null;
    const batch = {
      shape: this.options.shape,
      batchType: 'data',
      data: rows,
      length: this.length,
      schema: this.schema,
      cursor: this.cursor
    };
    return batch;
  }
}
exports.default = RowTableBatchAggregator;
//# sourceMappingURL=base-table-batch-aggregator.js.map