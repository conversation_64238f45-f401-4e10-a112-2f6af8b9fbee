{"version": 3, "file": "row-table-batch-aggregator.js", "names": ["_rowUtils", "require", "DEFAULT_ROW_COUNT", "RowTableBatchAggregator", "constructor", "schema", "options", "_defineProperty2", "default", "Array", "isArray", "_headers", "key", "index", "name", "rowCount", "length", "addArrayRow", "row", "cursor", "Number", "isFinite", "shape", "rowObject", "convertToObjectRow", "addObjectRow", "arrayRows", "rowArray", "convertToArrayRow", "objectRows", "getBatch", "rows", "slice", "batchType", "data", "exports"], "sources": ["../../../../src/lib/batches/row-table-batch-aggregator.ts"], "sourcesContent": ["import type {Schema} from '../schema/schema';\nimport type {TableBatch} from '../../category/table/table-types';\n// import type {ArrayRowTableBatch, ObjectRowTableBatch} from '../../category/table';\nimport {convertToArrayRow, convertToObjectRow} from '../utils/row-utils';\nimport {TableBatchAggregator, TableBatchOptions} from './table-batch-aggregator';\n\nconst DEFAULT_ROW_COUNT = 100;\n\nexport default class RowTableBatchAggregator implements TableBatchAggregator {\n  schema: Schema;\n  options: TableBatchOptions;\n\n  length: number = 0;\n  objectRows: {[columnName: string]: any} | null = null;\n  arrayRows: any[] | null = null;\n  cursor: number = 0;\n  private _headers: string[] = [];\n\n  constructor(schema: Schema, options: TableBatchOptions) {\n    this.options = options;\n    this.schema = schema;\n\n    // schema is an array if there're no headers\n    // object if there are headers\n    if (!Array.isArray(schema)) {\n      this._headers = [];\n      for (const key in schema) {\n        this._headers[schema[key].index] = schema[key].name;\n      }\n    }\n  }\n\n  rowCount(): number {\n    return this.length;\n  }\n\n  addArrayRow(row: any[], cursor?: number): void {\n    if (Number.isFinite(cursor)) {\n      this.cursor = cursor as number;\n    }\n\n    // eslint-disable-next-line default-case\n    switch (this.options.shape) {\n      case 'object-row-table':\n        const rowObject = convertToObjectRow(row, this._headers);\n        this.addObjectRow(rowObject, cursor);\n        break;\n      case 'array-row-table':\n        this.arrayRows = this.arrayRows || new Array(DEFAULT_ROW_COUNT);\n        this.arrayRows[this.length] = row;\n        this.length++;\n        break;\n    }\n  }\n\n  addObjectRow(row: {[columnName: string]: any}, cursor?: number): void {\n    if (Number.isFinite(cursor)) {\n      this.cursor = cursor as number;\n    }\n\n    // eslint-disable-next-line default-case\n    switch (this.options.shape) {\n      case 'array-row-table':\n        const rowArray = convertToArrayRow(row, this._headers);\n        this.addArrayRow(rowArray, cursor);\n        break;\n      case 'object-row-table':\n        this.objectRows = this.objectRows || new Array(DEFAULT_ROW_COUNT);\n        this.objectRows[this.length] = row;\n        this.length++;\n        break;\n    }\n  }\n\n  getBatch(): TableBatch | null {\n    let rows = this.arrayRows || this.objectRows;\n    if (!rows) {\n      return null;\n    }\n\n    rows = rows.slice(0, this.length);\n    this.arrayRows = null;\n    this.objectRows = null;\n\n    return {\n      shape: this.options.shape,\n      batchType: 'data',\n      data: rows,\n      length: this.length,\n      schema: this.schema,\n      cursor: this.cursor\n    };\n  }\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,SAAA,GAAAC,OAAA;AAGA,MAAMC,iBAAiB,GAAG,GAAG;AAEd,MAAMC,uBAAuB,CAAiC;EAU3EC,WAAWA,CAACC,MAAc,EAAEC,OAA0B,EAAE;IAAA,IAAAC,gBAAA,CAAAC,OAAA;IAAA,IAAAD,gBAAA,CAAAC,OAAA;IAAA,IAAAD,gBAAA,CAAAC,OAAA,kBANvC,CAAC;IAAA,IAAAD,gBAAA,CAAAC,OAAA,sBAC+B,IAAI;IAAA,IAAAD,gBAAA,CAAAC,OAAA,qBAC3B,IAAI;IAAA,IAAAD,gBAAA,CAAAC,OAAA,kBACb,CAAC;IAAA,IAAAD,gBAAA,CAAAC,OAAA,oBACW,EAAE;IAG7B,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,MAAM,GAAGA,MAAM;IAIpB,IAAI,CAACI,KAAK,CAACC,OAAO,CAACL,MAAM,CAAC,EAAE;MAC1B,IAAI,CAACM,QAAQ,GAAG,EAAE;MAClB,KAAK,MAAMC,GAAG,IAAIP,MAAM,EAAE;QACxB,IAAI,CAACM,QAAQ,CAACN,MAAM,CAACO,GAAG,CAAC,CAACC,KAAK,CAAC,GAAGR,MAAM,CAACO,GAAG,CAAC,CAACE,IAAI;MACrD;IACF;EACF;EAEAC,QAAQA,CAAA,EAAW;IACjB,OAAO,IAAI,CAACC,MAAM;EACpB;EAEAC,WAAWA,CAACC,GAAU,EAAEC,MAAe,EAAQ;IAC7C,IAAIC,MAAM,CAACC,QAAQ,CAACF,MAAM,CAAC,EAAE;MAC3B,IAAI,CAACA,MAAM,GAAGA,MAAgB;IAChC;IAGA,QAAQ,IAAI,CAACb,OAAO,CAACgB,KAAK;MACxB,KAAK,kBAAkB;QACrB,MAAMC,SAAS,GAAG,IAAAC,4BAAkB,EAACN,GAAG,EAAE,IAAI,CAACP,QAAQ,CAAC;QACxD,IAAI,CAACc,YAAY,CAACF,SAAS,EAAEJ,MAAM,CAAC;QACpC;MACF,KAAK,iBAAiB;QACpB,IAAI,CAACO,SAAS,GAAG,IAAI,CAACA,SAAS,IAAI,IAAIjB,KAAK,CAACP,iBAAiB,CAAC;QAC/D,IAAI,CAACwB,SAAS,CAAC,IAAI,CAACV,MAAM,CAAC,GAAGE,GAAG;QACjC,IAAI,CAACF,MAAM,EAAE;QACb;IACJ;EACF;EAEAS,YAAYA,CAACP,GAAgC,EAAEC,MAAe,EAAQ;IACpE,IAAIC,MAAM,CAACC,QAAQ,CAACF,MAAM,CAAC,EAAE;MAC3B,IAAI,CAACA,MAAM,GAAGA,MAAgB;IAChC;IAGA,QAAQ,IAAI,CAACb,OAAO,CAACgB,KAAK;MACxB,KAAK,iBAAiB;QACpB,MAAMK,QAAQ,GAAG,IAAAC,2BAAiB,EAACV,GAAG,EAAE,IAAI,CAACP,QAAQ,CAAC;QACtD,IAAI,CAACM,WAAW,CAACU,QAAQ,EAAER,MAAM,CAAC;QAClC;MACF,KAAK,kBAAkB;QACrB,IAAI,CAACU,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI,IAAIpB,KAAK,CAACP,iBAAiB,CAAC;QACjE,IAAI,CAAC2B,UAAU,CAAC,IAAI,CAACb,MAAM,CAAC,GAAGE,GAAG;QAClC,IAAI,CAACF,MAAM,EAAE;QACb;IACJ;EACF;EAEAc,QAAQA,CAAA,EAAsB;IAC5B,IAAIC,IAAI,GAAG,IAAI,CAACL,SAAS,IAAI,IAAI,CAACG,UAAU;IAC5C,IAAI,CAACE,IAAI,EAAE;MACT,OAAO,IAAI;IACb;IAEAA,IAAI,GAAGA,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAChB,MAAM,CAAC;IACjC,IAAI,CAACU,SAAS,GAAG,IAAI;IACrB,IAAI,CAACG,UAAU,GAAG,IAAI;IAEtB,OAAO;MACLP,KAAK,EAAE,IAAI,CAAChB,OAAO,CAACgB,KAAK;MACzBW,SAAS,EAAE,MAAM;MACjBC,IAAI,EAAEH,IAAI;MACVf,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBX,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBc,MAAM,EAAE,IAAI,CAACA;IACf,CAAC;EACH;AACF;AAACgB,OAAA,CAAA3B,OAAA,GAAAL,uBAAA"}