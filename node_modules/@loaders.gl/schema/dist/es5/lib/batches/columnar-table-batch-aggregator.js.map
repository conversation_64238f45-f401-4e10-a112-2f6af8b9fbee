{"version": 3, "file": "columnar-table-batch-aggregator.js", "names": ["DEFAULT_ROW_COUNT", "ColumnarTableBatchAggregator", "constructor", "schema", "options", "_defineProperty2", "default", "_reallocateColumns", "rowCount", "length", "addArrayRow", "row", "i", "fieldName", "columns", "addObjectRow", "getBatch", "_pruneColumns", "Array", "isArray", "field", "name", "index", "batch", "shape", "batchType", "data", "allocated", "ArrayType", "type", "Float32Array", "oldColumn", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "typedArray", "set", "columnName", "column", "Object", "entries", "slice", "exports"], "sources": ["../../../../src/lib/batches/columnar-table-batch-aggregator.ts"], "sourcesContent": ["import type {Schema} from '../schema/schema';\nimport type {ColumnarTableBatch, ArrowTableBatch} from '../../category/table/table-types';\nimport {TableBatchAggregator} from './table-batch-aggregator';\n\ntype ColumnarTableBatchOptions = {};\n\nconst DEFAULT_ROW_COUNT = 100;\n\nexport default class ColumnarTableBatchAggregator implements TableBatchAggregator {\n  schema: Schema;\n  length: number = 0;\n  allocated: number = 0;\n  columns: {[columnName: string]: any[]} = {};\n\n  constructor(schema: Schema, options: ColumnarTableBatchOptions) {\n    this.schema = schema;\n    this._reallocateColumns();\n  }\n\n  rowCount(): number {\n    return this.length;\n  }\n\n  addArrayRow(row: any[]) {\n    // If user keeps pushing rows beyond batch size, reallocate\n    this._reallocateColumns();\n    let i = 0;\n    // TODO what if no csv header, columns not populated?\n    for (const fieldName in this.columns) {\n      this.columns[fieldName][this.length] = row[i++];\n    }\n    this.length++;\n  }\n\n  addObjectRow(row: {[columnName: string]: any}): void {\n    // If user keeps pushing rows beyond batch size, reallocate\n    this._reallocateColumns();\n    for (const fieldName in row) {\n      this.columns[fieldName][this.length] = row[fieldName];\n    }\n    this.length++;\n  }\n\n  getBatch(): ColumnarTableBatch | ArrowTableBatch | null {\n    this._pruneColumns();\n    const columns = Array.isArray(this.schema) ? this.columns : {};\n\n    // schema is an array if there're no headers\n    // object if there are headers\n    // columns should match schema format\n    if (!Array.isArray(this.schema)) {\n      for (const fieldName in this.schema) {\n        const field = this.schema[fieldName];\n        columns[field.name] = this.columns[field.index];\n      }\n    }\n\n    this.columns = {};\n\n    const batch: ColumnarTableBatch = {\n      shape: 'columnar-table',\n      batchType: 'data',\n      data: columns,\n      schema: this.schema,\n      length: this.length\n    };\n\n    return batch;\n  }\n\n  // HELPERS\n\n  _reallocateColumns() {\n    if (this.length < this.allocated) {\n      return;\n    }\n\n    // @ts-ignore TODO\n    this.allocated = this.allocated > 0 ? (this.allocated *= 2) : DEFAULT_ROW_COUNT;\n    this.columns = {};\n\n    for (const fieldName in this.schema) {\n      const field = this.schema[fieldName];\n      const ArrayType = field.type || Float32Array;\n      const oldColumn = this.columns[field.index];\n\n      if (oldColumn && ArrayBuffer.isView(oldColumn)) {\n        // Copy the old data to the new array\n        const typedArray = new ArrayType(this.allocated);\n        typedArray.set(oldColumn);\n        this.columns[field.index] = typedArray;\n      } else if (oldColumn) {\n        // Plain array\n        oldColumn.length = this.allocated;\n        this.columns[field.index] = oldColumn;\n      } else {\n        // Create new\n        this.columns[field.index] = new ArrayType(this.allocated);\n      }\n    }\n  }\n\n  _pruneColumns() {\n    for (const [columnName, column] of Object.entries(this.columns)) {\n      this.columns[columnName] = column.slice(0, this.length);\n    }\n  }\n}\n"], "mappings": ";;;;;;;;AAMA,MAAMA,iBAAiB,GAAG,GAAG;AAEd,MAAMC,4BAA4B,CAAiC;EAMhFC,WAAWA,CAACC,MAAc,EAAEC,OAAkC,EAAE;IAAA,IAAAC,gBAAA,CAAAC,OAAA;IAAA,IAAAD,gBAAA,CAAAC,OAAA,kBAJ/C,CAAC;IAAA,IAAAD,gBAAA,CAAAC,OAAA,qBACE,CAAC;IAAA,IAAAD,gBAAA,CAAAC,OAAA,mBACoB,CAAC,CAAC;IAGzC,IAAI,CAACH,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACI,kBAAkB,CAAC,CAAC;EAC3B;EAEAC,QAAQA,CAAA,EAAW;IACjB,OAAO,IAAI,CAACC,MAAM;EACpB;EAEAC,WAAWA,CAACC,GAAU,EAAE;IAEtB,IAAI,CAACJ,kBAAkB,CAAC,CAAC;IACzB,IAAIK,CAAC,GAAG,CAAC;IAET,KAAK,MAAMC,SAAS,IAAI,IAAI,CAACC,OAAO,EAAE;MACpC,IAAI,CAACA,OAAO,CAACD,SAAS,CAAC,CAAC,IAAI,CAACJ,MAAM,CAAC,GAAGE,GAAG,CAACC,CAAC,EAAE,CAAC;IACjD;IACA,IAAI,CAACH,MAAM,EAAE;EACf;EAEAM,YAAYA,CAACJ,GAAgC,EAAQ;IAEnD,IAAI,CAACJ,kBAAkB,CAAC,CAAC;IACzB,KAAK,MAAMM,SAAS,IAAIF,GAAG,EAAE;MAC3B,IAAI,CAACG,OAAO,CAACD,SAAS,CAAC,CAAC,IAAI,CAACJ,MAAM,CAAC,GAAGE,GAAG,CAACE,SAAS,CAAC;IACvD;IACA,IAAI,CAACJ,MAAM,EAAE;EACf;EAEAO,QAAQA,CAAA,EAAgD;IACtD,IAAI,CAACC,aAAa,CAAC,CAAC;IACpB,MAAMH,OAAO,GAAGI,KAAK,CAACC,OAAO,CAAC,IAAI,CAAChB,MAAM,CAAC,GAAG,IAAI,CAACW,OAAO,GAAG,CAAC,CAAC;IAK9D,IAAI,CAACI,KAAK,CAACC,OAAO,CAAC,IAAI,CAAChB,MAAM,CAAC,EAAE;MAC/B,KAAK,MAAMU,SAAS,IAAI,IAAI,CAACV,MAAM,EAAE;QACnC,MAAMiB,KAAK,GAAG,IAAI,CAACjB,MAAM,CAACU,SAAS,CAAC;QACpCC,OAAO,CAACM,KAAK,CAACC,IAAI,CAAC,GAAG,IAAI,CAACP,OAAO,CAACM,KAAK,CAACE,KAAK,CAAC;MACjD;IACF;IAEA,IAAI,CAACR,OAAO,GAAG,CAAC,CAAC;IAEjB,MAAMS,KAAyB,GAAG;MAChCC,KAAK,EAAE,gBAAgB;MACvBC,SAAS,EAAE,MAAM;MACjBC,IAAI,EAAEZ,OAAO;MACbX,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBM,MAAM,EAAE,IAAI,CAACA;IACf,CAAC;IAED,OAAOc,KAAK;EACd;EAIAhB,kBAAkBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACE,MAAM,GAAG,IAAI,CAACkB,SAAS,EAAE;MAChC;IACF;IAGA,IAAI,CAACA,SAAS,GAAG,IAAI,CAACA,SAAS,GAAG,CAAC,GAAI,IAAI,CAACA,SAAS,IAAI,CAAC,GAAI3B,iBAAiB;IAC/E,IAAI,CAACc,OAAO,GAAG,CAAC,CAAC;IAEjB,KAAK,MAAMD,SAAS,IAAI,IAAI,CAACV,MAAM,EAAE;MACnC,MAAMiB,KAAK,GAAG,IAAI,CAACjB,MAAM,CAACU,SAAS,CAAC;MACpC,MAAMe,SAAS,GAAGR,KAAK,CAACS,IAAI,IAAIC,YAAY;MAC5C,MAAMC,SAAS,GAAG,IAAI,CAACjB,OAAO,CAACM,KAAK,CAACE,KAAK,CAAC;MAE3C,IAAIS,SAAS,IAAIC,WAAW,CAACC,MAAM,CAACF,SAAS,CAAC,EAAE;QAE9C,MAAMG,UAAU,GAAG,IAAIN,SAAS,CAAC,IAAI,CAACD,SAAS,CAAC;QAChDO,UAAU,CAACC,GAAG,CAACJ,SAAS,CAAC;QACzB,IAAI,CAACjB,OAAO,CAACM,KAAK,CAACE,KAAK,CAAC,GAAGY,UAAU;MACxC,CAAC,MAAM,IAAIH,SAAS,EAAE;QAEpBA,SAAS,CAACtB,MAAM,GAAG,IAAI,CAACkB,SAAS;QACjC,IAAI,CAACb,OAAO,CAACM,KAAK,CAACE,KAAK,CAAC,GAAGS,SAAS;MACvC,CAAC,MAAM;QAEL,IAAI,CAACjB,OAAO,CAACM,KAAK,CAACE,KAAK,CAAC,GAAG,IAAIM,SAAS,CAAC,IAAI,CAACD,SAAS,CAAC;MAC3D;IACF;EACF;EAEAV,aAAaA,CAAA,EAAG;IACd,KAAK,MAAM,CAACmB,UAAU,EAAEC,MAAM,CAAC,IAAIC,MAAM,CAACC,OAAO,CAAC,IAAI,CAACzB,OAAO,CAAC,EAAE;MAC/D,IAAI,CAACA,OAAO,CAACsB,UAAU,CAAC,GAAGC,MAAM,CAACG,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC/B,MAAM,CAAC;IACzD;EACF;AACF;AAACgC,OAAA,CAAAnC,OAAA,GAAAL,4BAAA"}