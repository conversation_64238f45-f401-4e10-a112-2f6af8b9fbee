"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _rowUtils = require("../utils/row-utils");
const DEFAULT_ROW_COUNT = 100;
class RowTableBatchAggregator {
  constructor(schema, options) {
    (0, _defineProperty2.default)(this, "schema", void 0);
    (0, _defineProperty2.default)(this, "options", void 0);
    (0, _defineProperty2.default)(this, "length", 0);
    (0, _defineProperty2.default)(this, "objectRows", null);
    (0, _defineProperty2.default)(this, "arrayRows", null);
    (0, _defineProperty2.default)(this, "cursor", 0);
    (0, _defineProperty2.default)(this, "_headers", []);
    this.options = options;
    this.schema = schema;
    if (!Array.isArray(schema)) {
      this._headers = [];
      for (const key in schema) {
        this._headers[schema[key].index] = schema[key].name;
      }
    }
  }
  rowCount() {
    return this.length;
  }
  addArrayRow(row, cursor) {
    if (Number.isFinite(cursor)) {
      this.cursor = cursor;
    }
    switch (this.options.shape) {
      case 'object-row-table':
        const rowObject = (0, _rowUtils.convertToObjectRow)(row, this._headers);
        this.addObjectRow(rowObject, cursor);
        break;
      case 'array-row-table':
        this.arrayRows = this.arrayRows || new Array(DEFAULT_ROW_COUNT);
        this.arrayRows[this.length] = row;
        this.length++;
        break;
    }
  }
  addObjectRow(row, cursor) {
    if (Number.isFinite(cursor)) {
      this.cursor = cursor;
    }
    switch (this.options.shape) {
      case 'array-row-table':
        const rowArray = (0, _rowUtils.convertToArrayRow)(row, this._headers);
        this.addArrayRow(rowArray, cursor);
        break;
      case 'object-row-table':
        this.objectRows = this.objectRows || new Array(DEFAULT_ROW_COUNT);
        this.objectRows[this.length] = row;
        this.length++;
        break;
    }
  }
  getBatch() {
    let rows = this.arrayRows || this.objectRows;
    if (!rows) {
      return null;
    }
    rows = rows.slice(0, this.length);
    this.arrayRows = null;
    this.objectRows = null;
    return {
      shape: this.options.shape,
      batchType: 'data',
      data: rows,
      length: this.length,
      schema: this.schema,
      cursor: this.cursor
    };
  }
}
exports.default = RowTableBatchAggregator;
//# sourceMappingURL=row-table-batch-aggregator.js.map