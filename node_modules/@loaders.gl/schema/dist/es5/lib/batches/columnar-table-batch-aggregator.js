"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
const DEFAULT_ROW_COUNT = 100;
class ColumnarTableBatchAggregator {
  constructor(schema, options) {
    (0, _defineProperty2.default)(this, "schema", void 0);
    (0, _defineProperty2.default)(this, "length", 0);
    (0, _defineProperty2.default)(this, "allocated", 0);
    (0, _defineProperty2.default)(this, "columns", {});
    this.schema = schema;
    this._reallocateColumns();
  }
  rowCount() {
    return this.length;
  }
  addArrayRow(row) {
    this._reallocateColumns();
    let i = 0;
    for (const fieldName in this.columns) {
      this.columns[fieldName][this.length] = row[i++];
    }
    this.length++;
  }
  addObjectRow(row) {
    this._reallocateColumns();
    for (const fieldName in row) {
      this.columns[fieldName][this.length] = row[fieldName];
    }
    this.length++;
  }
  getBatch() {
    this._pruneColumns();
    const columns = Array.isArray(this.schema) ? this.columns : {};
    if (!Array.isArray(this.schema)) {
      for (const fieldName in this.schema) {
        const field = this.schema[fieldName];
        columns[field.name] = this.columns[field.index];
      }
    }
    this.columns = {};
    const batch = {
      shape: 'columnar-table',
      batchType: 'data',
      data: columns,
      schema: this.schema,
      length: this.length
    };
    return batch;
  }
  _reallocateColumns() {
    if (this.length < this.allocated) {
      return;
    }
    this.allocated = this.allocated > 0 ? this.allocated *= 2 : DEFAULT_ROW_COUNT;
    this.columns = {};
    for (const fieldName in this.schema) {
      const field = this.schema[fieldName];
      const ArrayType = field.type || Float32Array;
      const oldColumn = this.columns[field.index];
      if (oldColumn && ArrayBuffer.isView(oldColumn)) {
        const typedArray = new ArrayType(this.allocated);
        typedArray.set(oldColumn);
        this.columns[field.index] = typedArray;
      } else if (oldColumn) {
        oldColumn.length = this.allocated;
        this.columns[field.index] = oldColumn;
      } else {
        this.columns[field.index] = new ArrayType(this.allocated);
      }
    }
  }
  _pruneColumns() {
    for (const [columnName, column] of Object.entries(this.columns)) {
      this.columns[columnName] = column.slice(0, this.length);
    }
  }
}
exports.default = ColumnarTableBatchAggregator;
//# sourceMappingURL=columnar-table-batch-aggregator.js.map