"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _baseTableBatchAggregator = _interopRequireDefault(require("./base-table-batch-aggregator"));
var _rowTableBatchAggregator = _interopRequireDefault(require("./row-table-batch-aggregator"));
var _columnarTableBatchAggregator = _interopRequireDefault(require("./columnar-table-batch-aggregator"));
const DEFAULT_OPTIONS = {
  shape: 'array-row-table',
  batchSize: 'auto',
  batchDebounceMs: 0,
  limit: 0,
  _limitMB: 0
};
const ERR_MESSAGE = 'TableBatchBuilder';
class TableBatchBuilder {
  constructor(schema, options) {
    (0, _defineProperty2.default)(this, "schema", void 0);
    (0, _defineProperty2.default)(this, "options", void 0);
    (0, _defineProperty2.default)(this, "aggregator", null);
    (0, _defineProperty2.default)(this, "batchCount", 0);
    (0, _defineProperty2.default)(this, "bytesUsed", 0);
    (0, _defineProperty2.default)(this, "isChunkComplete", false);
    (0, _defineProperty2.default)(this, "lastBatchEmittedMs", Date.now());
    (0, _defineProperty2.default)(this, "totalLength", 0);
    (0, _defineProperty2.default)(this, "totalBytes", 0);
    (0, _defineProperty2.default)(this, "rowBytes", 0);
    this.schema = schema;
    this.options = {
      ...DEFAULT_OPTIONS,
      ...options
    };
  }
  limitReached() {
    var _this$options, _this$options2;
    if (Boolean((_this$options = this.options) === null || _this$options === void 0 ? void 0 : _this$options.limit) && this.totalLength >= this.options.limit) {
      return true;
    }
    if (Boolean((_this$options2 = this.options) === null || _this$options2 === void 0 ? void 0 : _this$options2._limitMB) && this.totalBytes / 1e6 >= this.options._limitMB) {
      return true;
    }
    return false;
  }
  addRow(row) {
    if (this.limitReached()) {
      return;
    }
    this.totalLength++;
    this.rowBytes = this.rowBytes || this._estimateRowMB(row);
    this.totalBytes += this.rowBytes;
    if (Array.isArray(row)) {
      this.addArrayRow(row);
    } else {
      this.addObjectRow(row);
    }
  }
  addArrayRow(row) {
    if (!this.aggregator) {
      const TableBatchType = this._getTableBatchType();
      this.aggregator = new TableBatchType(this.schema, this.options);
    }
    this.aggregator.addArrayRow(row);
  }
  addObjectRow(row) {
    if (!this.aggregator) {
      const TableBatchType = this._getTableBatchType();
      this.aggregator = new TableBatchType(this.schema, this.options);
    }
    this.aggregator.addObjectRow(row);
  }
  chunkComplete(chunk) {
    if (chunk instanceof ArrayBuffer) {
      this.bytesUsed += chunk.byteLength;
    }
    if (typeof chunk === 'string') {
      this.bytesUsed += chunk.length;
    }
    this.isChunkComplete = true;
  }
  getFullBatch(options) {
    return this._isFull() ? this._getBatch(options) : null;
  }
  getFinalBatch(options) {
    return this._getBatch(options);
  }
  _estimateRowMB(row) {
    return Array.isArray(row) ? row.length * 8 : Object.keys(row).length * 8;
  }
  _isFull() {
    if (!this.aggregator || this.aggregator.rowCount() === 0) {
      return false;
    }
    if (this.options.batchSize === 'auto') {
      if (!this.isChunkComplete) {
        return false;
      }
    } else if (this.options.batchSize > this.aggregator.rowCount()) {
      return false;
    }
    if (this.options.batchDebounceMs > Date.now() - this.lastBatchEmittedMs) {
      return false;
    }
    this.isChunkComplete = false;
    this.lastBatchEmittedMs = Date.now();
    return true;
  }
  _getBatch(options) {
    if (!this.aggregator) {
      return null;
    }
    if (options !== null && options !== void 0 && options.bytesUsed) {
      this.bytesUsed = options.bytesUsed;
    }
    const normalizedBatch = this.aggregator.getBatch();
    normalizedBatch.count = this.batchCount;
    normalizedBatch.bytesUsed = this.bytesUsed;
    Object.assign(normalizedBatch, options);
    this.batchCount++;
    this.aggregator = null;
    return normalizedBatch;
  }
  _getTableBatchType() {
    switch (this.options.shape) {
      case 'row-table':
        return _baseTableBatchAggregator.default;
      case 'array-row-table':
      case 'object-row-table':
        return _rowTableBatchAggregator.default;
      case 'columnar-table':
        return _columnarTableBatchAggregator.default;
      case 'arrow-table':
        if (!TableBatchBuilder.ArrowBatch) {
          throw new Error(ERR_MESSAGE);
        }
        return TableBatchBuilder.ArrowBatch;
      default:
        throw new Error(ERR_MESSAGE);
    }
  }
}
exports.default = TableBatchBuilder;
(0, _defineProperty2.default)(TableBatchBuilder, "ArrowBatch", void 0);
//# sourceMappingURL=table-batch-builder.js.map