"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getArrowType = getArrowType;
exports.getArrowVector = getArrowVector;
var _Arrow = require("apache-arrow/Arrow.dom");
function getArrowType(array) {
  switch (array.constructor) {
    case Int8Array:
      return new _Arrow.Int8();
    case Uint8Array:
      return new _Arrow.Uint8();
    case Int16Array:
      return new _Arrow.Int16();
    case Uint16Array:
      return new _Arrow.Uint16();
    case Int32Array:
      return new _Arrow.Int32();
    case Uint32Array:
      return new _Arrow.Uint32();
    case Float32Array:
      return new _Arrow.Float32();
    case Float64Array:
      return new _Arrow.Float64();
    default:
      throw new Error('array type not supported');
  }
}
function getArrowVector(array) {
  switch (array.constructor) {
    case Int8Array:
      return _Arrow.Int8Vector.from(array);
    case Uint8Array:
      return _Arrow.Uint8Vector.from(array);
    case Int16Array:
      return _Arrow.Int16Vector.from(array);
    case Uint16Array:
      return _Arrow.Uint16Vector.from(array);
    case Int32Array:
      return _Arrow.Int32Vector.from(array);
    case Uint32Array:
      return _Arrow.Uint32Vector.from(array);
    case Float32Array:
      return _Arrow.Float32Vector.from(array);
    case Float64Array:
      return _Arrow.Float64Vector.from(array);
    default:
      throw new Error('array type not supported');
  }
}
//# sourceMappingURL=arrow-type-utils.js.map