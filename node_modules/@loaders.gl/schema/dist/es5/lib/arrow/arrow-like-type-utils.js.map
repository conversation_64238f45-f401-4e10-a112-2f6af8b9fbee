{"version": 3, "file": "arrow-like-type-utils.js", "names": ["_schema", "require", "getArrowTypeFromTypedArray", "array", "constructor", "Int8Array", "Int8", "Uint8Array", "Uint8", "Int16Array", "Int16", "Uint16Array", "Uint16", "Int32Array", "Int32", "Uint32Array", "Uint32", "Float32Array", "Float32", "Float64Array", "Float64", "Error"], "sources": ["../../../../src/lib/arrow/arrow-like-type-utils.ts"], "sourcesContent": ["import type {TypedArray} from '../../types';\nimport {\n  DataType,\n  Float32,\n  Float64,\n  Int16,\n  Int32,\n  Int8,\n  Uint16,\n  Uint32,\n  Uint8\n} from '../schema/schema';\n\nexport function getArrowTypeFromTypedArray(array: TypedArray): DataType {\n  switch (array.constructor) {\n    case Int8Array:\n      return new Int8();\n    case Uint8Array:\n      return new Uint8();\n    case Int16Array:\n      return new Int16();\n    case Uint16Array:\n      return new Uint16();\n    case Int32Array:\n      return new Int32();\n    case Uint32Array:\n      return new Uint32();\n    case Float32Array:\n      return new Float32();\n    case Float64Array:\n      return new Float64();\n    default:\n      throw new Error('array type not supported');\n  }\n}\n"], "mappings": ";;;;;;AACA,IAAAA,OAAA,GAAAC,OAAA;AAYO,SAASC,0BAA0BA,CAACC,KAAiB,EAAY;EACtE,QAAQA,KAAK,CAACC,WAAW;IACvB,KAAKC,SAAS;MACZ,OAAO,IAAIC,YAAI,CAAC,CAAC;IACnB,KAAKC,UAAU;MACb,OAAO,IAAIC,aAAK,CAAC,CAAC;IACpB,KAAKC,UAAU;MACb,OAAO,IAAIC,aAAK,CAAC,CAAC;IACpB,KAAKC,WAAW;MACd,OAAO,IAAIC,cAAM,CAAC,CAAC;IACrB,KAAKC,UAAU;MACb,OAAO,IAAIC,aAAK,CAAC,CAAC;IACpB,KAAKC,WAAW;MACd,OAAO,IAAIC,cAAM,CAAC,CAAC;IACrB,KAAKC,YAAY;MACf,OAAO,IAAIC,eAAO,CAAC,CAAC;IACtB,KAAKC,YAAY;MACf,OAAO,IAAIC,eAAO,CAAC,CAAC;IACtB;MACE,MAAM,IAAIC,KAAK,CAAC,0BAA0B,CAAC;EAC/C;AACF"}