{"version": 3, "file": "arrow-type-utils.js", "names": ["_Arrow", "require", "getArrowType", "array", "constructor", "Int8Array", "Int8", "Uint8Array", "Uint8", "Int16Array", "Int16", "Uint16Array", "Uint16", "Int32Array", "Int32", "Uint32Array", "Uint32", "Float32Array", "Float32", "Float64Array", "Float64", "Error", "getArrowVector", "Int8Vector", "from", "Uint8Vector", "Int16Vector", "Uint16Vector", "Int32Vector", "Uint32Vector", "Float32Vector", "Float64Vector"], "sources": ["../../../../src/lib/arrow/arrow-type-utils.ts"], "sourcesContent": ["import type {TypedArray} from '../../types';\nimport {\n  DataType,\n  Float32,\n  Float64,\n  Int16,\n  Int32,\n  Int8,\n  Uint16,\n  Uint32,\n  Uint8,\n  Int8Vector,\n  Uint8Vector,\n  Int16Vector,\n  Uint16Vector,\n  Int32Vector,\n  Uint32Vector,\n  Float32Vector,\n  Float64Vector\n} from 'apache-arrow/Arrow.dom';\nimport {AbstractVector} from 'apache-arrow/vector';\n\nexport function getArrowType(array: TypedArray): DataType {\n  switch (array.constructor) {\n    case Int8Array:\n      return new Int8();\n    case Uint8Array:\n      return new Uint8();\n    case Int16Array:\n      return new Int16();\n    case Uint16Array:\n      return new Uint16();\n    case Int32Array:\n      return new Int32();\n    case Uint32Array:\n      return new Uint32();\n    case Float32Array:\n      return new Float32();\n    case Float64Array:\n      return new Float64();\n    default:\n      throw new Error('array type not supported');\n  }\n}\n\nexport function getArrowVector(array: TypedArray): AbstractVector {\n  switch (array.constructor) {\n    case Int8Array:\n      return Int8Vector.from(array);\n    case Uint8Array:\n      return Uint8Vector.from(array);\n    case Int16Array:\n      return Int16Vector.from(array);\n    case Uint16Array:\n      return Uint16Vector.from(array);\n    case Int32Array:\n      return Int32Vector.from(array);\n    case Uint32Array:\n      return Uint32Vector.from(array);\n    case Float32Array:\n      return Float32Vector.from(array);\n    case Float64Array:\n      return Float64Vector.from(array);\n    default:\n      throw new Error('array type not supported');\n  }\n}\n"], "mappings": ";;;;;;;AACA,IAAAA,MAAA,GAAAC,OAAA;AAqBO,SAASC,YAAYA,CAACC,KAAiB,EAAY;EACxD,QAAQA,KAAK,CAACC,WAAW;IACvB,KAAKC,SAAS;MACZ,OAAO,IAAIC,WAAI,CAAC,CAAC;IACnB,KAAKC,UAAU;MACb,OAAO,IAAIC,YAAK,CAAC,CAAC;IACpB,KAAKC,UAAU;MACb,OAAO,IAAIC,YAAK,CAAC,CAAC;IACpB,KAAKC,WAAW;MACd,OAAO,IAAIC,aAAM,CAAC,CAAC;IACrB,KAAKC,UAAU;MACb,OAAO,IAAIC,YAAK,CAAC,CAAC;IACpB,KAAKC,WAAW;MACd,OAAO,IAAIC,aAAM,CAAC,CAAC;IACrB,KAAKC,YAAY;MACf,OAAO,IAAIC,cAAO,CAAC,CAAC;IACtB,KAAKC,YAAY;MACf,OAAO,IAAIC,cAAO,CAAC,CAAC;IACtB;MACE,MAAM,IAAIC,KAAK,CAAC,0BAA0B,CAAC;EAC/C;AACF;AAEO,SAASC,cAAcA,CAACnB,KAAiB,EAAkB;EAChE,QAAQA,KAAK,CAACC,WAAW;IACvB,KAAKC,SAAS;MACZ,OAAOkB,iBAAU,CAACC,IAAI,CAACrB,KAAK,CAAC;IAC/B,KAAKI,UAAU;MACb,OAAOkB,kBAAW,CAACD,IAAI,CAACrB,KAAK,CAAC;IAChC,KAAKM,UAAU;MACb,OAAOiB,kBAAW,CAACF,IAAI,CAACrB,KAAK,CAAC;IAChC,KAAKQ,WAAW;MACd,OAAOgB,mBAAY,CAACH,IAAI,CAACrB,KAAK,CAAC;IACjC,KAAKU,UAAU;MACb,OAAOe,kBAAW,CAACJ,IAAI,CAACrB,KAAK,CAAC;IAChC,KAAKY,WAAW;MACd,OAAOc,mBAAY,CAACL,IAAI,CAACrB,KAAK,CAAC;IACjC,KAAKc,YAAY;MACf,OAAOa,oBAAa,CAACN,IAAI,CAACrB,KAAK,CAAC;IAClC,KAAKgB,YAAY;MACf,OAAOY,oBAAa,CAACP,IAAI,CAACrB,KAAK,CAAC;IAClC;MACE,MAAM,IAAIkB,KAAK,CAAC,0BAA0B,CAAC;EAC/C;AACF"}