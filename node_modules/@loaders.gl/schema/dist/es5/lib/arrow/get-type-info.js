"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getTypeInfo = getTypeInfo;
var _schema = require("../schema/schema");
function getTypeInfo(arrowTypeLike) {
  return {
    typeId: arrowTypeLike.typeId,
    ArrayType: arrowTypeLike.ArrayType,
    typeName: arrowTypeLike.toString(),
    typeEnumName: getTypeKey(arrowTypeLike.typeId),
    precision: arrowTypeLike.precision
  };
}
let ReverseType = null;
function getTypeKey(typeKey) {
  if (!ReverseType) {
    ReverseType = {};
    for (const key in _schema.Type) {
      ReverseType[_schema.Type[key]] = key;
    }
  }
  return ReverseType[typeKey];
}
//# sourceMappingURL=get-type-info.js.map