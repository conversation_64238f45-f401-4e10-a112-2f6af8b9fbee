"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getArrowTypeFromTypedArray = getArrowTypeFromTypedArray;
var _schema = require("../schema/schema");
function getArrowTypeFromTypedArray(array) {
  switch (array.constructor) {
    case Int8Array:
      return new _schema.Int8();
    case Uint8Array:
      return new _schema.Uint8();
    case Int16Array:
      return new _schema.Int16();
    case Uint16Array:
      return new _schema.Uint16();
    case Int32Array:
      return new _schema.Int32();
    case Uint32Array:
      return new _schema.Uint32();
    case Float32Array:
      return new _schema.Float32();
    case Float64Array:
      return new _schema.Float64();
    default:
      throw new Error('array type not supported');
  }
}
//# sourceMappingURL=arrow-like-type-utils.js.map