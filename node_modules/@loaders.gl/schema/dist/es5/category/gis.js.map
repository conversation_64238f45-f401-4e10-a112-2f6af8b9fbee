{"version": 3, "file": "gis.js", "names": [], "sources": ["../../../src/category/gis.ts"], "sourcesContent": ["// GIS\nimport type {TypedArray} from '../types';\nimport type {Feature, Geometry, Point, LineString, Polygon} from 'geojson';\n\n// GEOJSON FORMAT GEOMETRY\n\n// eslint-disable-next-line import/no-unresolved\nexport type {\n  GeoJSON,\n  Feature,\n  FeatureCollection,\n  Geometry,\n  Position,\n  GeoJsonProperties\n} from 'geojson';\n// eslint-disable-next-line import/no-unresolved\nexport type {\n  Point,\n  MultiPoint,\n  LineString,\n  MultiLineString,\n  Polygon,\n  MultiPolygon,\n  GeometryCollection\n} from 'geojson';\n\n// Aggregate information for converting GeoJSON into other formats\nexport type GeojsonGeometryInfo = {\n  coordLength: number;\n  pointPositionsCount: number;\n  pointFeaturesCount: number;\n  linePositionsCount: number;\n  linePathsCount: number;\n  lineFeaturesCount: number;\n  polygonPositionsCount: number;\n  polygonObjectsCount: number;\n  polygonRingsCount: number;\n  polygonFeaturesCount: number;\n};\n\n// FLAT GEOJSON FORMAT GEOMETRY\nexport type FlatGeometryType = 'Point' | 'LineString' | 'Polygon';\ntype RemoveCoordinatesField<Type> = {\n  [Property in keyof Type as Exclude<Property, 'coordinates'>]: Type[Property];\n};\n\n/**\n * Generic flat geometry data storage type\n */\nexport type FlatIndexedGeometry = {\n  data: number[];\n  indices: number[];\n};\n\n/**\n * GeoJSON (Multi)Point geometry with coordinate data flattened into `data` array and indexed by `indices`\n */\nexport type FlatPoint = RemoveCoordinatesField<Point> & FlatIndexedGeometry;\n\n/**\n * GeoJSON (Multi)LineString geometry with coordinate data flattened into `data` array and indexed by `indices`\n */\nexport type FlatLineString = RemoveCoordinatesField<LineString> & FlatIndexedGeometry;\n\n/**\n * GeoJSON (Multi)Polygon geometry with coordinate data flattened into `data` array and indexed by 2D `indices`\n */\nexport type FlatPolygon = RemoveCoordinatesField<Polygon> & {\n  data: number[];\n  indices: number[][];\n  areas: number[][];\n};\n\nexport type FlatGeometry = FlatPoint | FlatLineString | FlatPolygon;\n\ntype FlattenGeometry<Type> = {\n  [Property in keyof Type]: Type[Property] extends Geometry ? FlatGeometry : Type[Property];\n};\n\n/**\n * GeoJSON Feature with Geometry replaced by FlatGeometry\n */\nexport type FlatFeature = FlattenGeometry<Feature>;\n\n// BINARY FORMAT GEOMETRY\n\nexport type BinaryAttribute = {value: TypedArray; size: number};\nexport type BinaryGeometryType = 'Point' | 'LineString' | 'Polygon';\n\ntype NumericProps = {[key: string]: BinaryAttribute};\ntype Properties = object[];\n\n/**\n * Represent a single Geometry, similar to a GeoJSON Geometry\n */\nexport type BinaryGeometry = BinaryPointGeometry | BinaryLineGeometry | BinaryPolygonGeometry;\n\nexport type BinaryPointGeometry = {\n  type: 'Point';\n  positions: BinaryAttribute;\n};\n\nexport type BinaryLineGeometry = {\n  type: 'LineString';\n  positions: BinaryAttribute;\n  pathIndices: BinaryAttribute;\n};\n\nexport type BinaryPolygonGeometry = {\n  type: 'Polygon';\n  positions: BinaryAttribute;\n  polygonIndices: BinaryAttribute;\n  primitivePolygonIndices: BinaryAttribute;\n  triangles?: BinaryAttribute;\n};\n\nexport type BinaryProperties = {\n  featureIds: BinaryAttribute;\n  globalFeatureIds: BinaryAttribute;\n  numericProps: NumericProps;\n  properties: Properties;\n  fields?: Properties;\n};\n\nexport type BinaryPointFeatures = BinaryPointGeometry & BinaryProperties;\nexport type BinaryLineFeatures = BinaryLineGeometry & BinaryProperties;\nexport type BinaryPolygonFeatures = BinaryPolygonGeometry & BinaryProperties;\n\n/**\n * Represent a collection of Features, similar to a GeoJSON FeatureCollection\n */\nexport type BinaryFeatures = {\n  points?: BinaryPointFeatures;\n  lines?: BinaryLineFeatures;\n  polygons?: BinaryPolygonFeatures;\n};\n"], "mappings": ""}