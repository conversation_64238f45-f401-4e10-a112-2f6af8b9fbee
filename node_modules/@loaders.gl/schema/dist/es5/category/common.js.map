{"version": 3, "file": "common.js", "names": [], "sources": ["../../../src/category/common.ts"], "sourcesContent": ["import type {RecordBatch} from 'apache-arrow';\n\nexport type Field = any;\n\nexport type Schema = {\n  [key: string]: Field;\n};\n\nexport type Batch = {\n  batchType: 'data' | 'metadata' | 'partial-result' | 'final-result';\n  mimeType?: string;\n  shape: string;\n  data: any;\n  recordBatch?: RecordBatch;\n  length: number;\n  schema?: Schema;\n  bytesUsed?: number;\n  count?: number;\n  cursor?: number;\n  [key: string]: any;\n};\n\n/*\nexport type Batch = {\n  bytesUsed?: number;\n  count?: number;\n  cursor?: number;\n  [key: string]: any;\n}\n*/\n"], "mappings": ""}