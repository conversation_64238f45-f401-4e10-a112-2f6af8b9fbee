"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.convertMesh = convertMesh;
exports.convertMeshToColumnarTable = convertMeshToColumnarTable;
var _meshToArrowTable = require("./mesh-to-arrow-table");
function convertMesh(mesh, shape, options) {
  switch (shape || 'mesh') {
    case 'mesh':
      return mesh;
    case 'columnar-table':
      return convertMeshToColumnarTable(mesh);
    case 'arrow-table':
      return {
        shape: 'arrow-table',
        data: (0, _meshToArrowTable.convertMeshToArrowTable)(mesh)
      };
    default:
      throw new Error("Unsupported shape ".concat(options === null || options === void 0 ? void 0 : options.shape));
  }
}
function convertMeshToColumnarTable(mesh) {
  const columns = {};
  for (const [columnName, attribute] of Object.entries(mesh.attributes)) {
    columns[columnName] = attribute.value;
  }
  return {
    shape: 'columnar-table',
    schema: mesh.schema,
    data: columns
  };
}
//# sourceMappingURL=convert-mesh.js.map