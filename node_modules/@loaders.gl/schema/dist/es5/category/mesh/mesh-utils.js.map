{"version": 3, "file": "mesh-utils.js", "names": ["getMeshSize", "attributes", "size", "attributeName", "attribute", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "byteLength", "BYTES_PER_ELEMENT", "getMeshBoundingBox", "minX", "Infinity", "minY", "minZ", "maxX", "maxY", "maxZ", "positions", "POSITION", "value", "len", "length", "i", "x", "y", "z"], "sources": ["../../../../src/category/mesh/mesh-utils.ts"], "sourcesContent": ["// Mesh category utilities\n// TODO - move to mesh category module, or to math.gl/geometry module\nimport {TypedArray} from '../../types';\nimport {MeshAttributes} from './mesh-types';\n\ntype TypedArrays = {[key: string]: TypedArray};\n\n/**\n * Holds an axis aligned bounding box\n * TODO - make sure AxisAlignedBoundingBox in math.gl/culling understands this format (or change this format)\n */\ntype BoundingBox = [[number, number, number], [number, number, number]];\n\n/**\n * Get number of vertices in mesh\n * @param attributes\n */\nexport function getMeshSize(attributes: TypedArrays): number {\n  let size = 0;\n  for (const attributeName in attributes) {\n    const attribute = attributes[attributeName];\n    if (ArrayBuffer.isView(attribute)) {\n      // @ts-ignore DataView doesn't have BYTES_PER_ELEMENT\n      size += attribute.byteLength * attribute.BYTES_PER_ELEMENT;\n    }\n  }\n  return size;\n}\n\n/**\n * Get the (axis aligned) bounding box of a mesh\n * @param attributes\n * @returns array of two vectors representing the axis aligned bounding box\n */\n// eslint-disable-next-line complexity\nexport function getMeshBoundingBox(attributes: MeshAttributes): BoundingBox {\n  let minX = Infinity;\n  let minY = Infinity;\n  let minZ = Infinity;\n  let maxX = -Infinity;\n  let maxY = -Infinity;\n  let maxZ = -Infinity;\n\n  const positions = attributes.POSITION ? attributes.POSITION.value : [];\n  const len = positions && positions.length;\n\n  for (let i = 0; i < len; i += 3) {\n    const x = positions[i];\n    const y = positions[i + 1];\n    const z = positions[i + 2];\n\n    minX = x < minX ? x : minX;\n    minY = y < minY ? y : minY;\n    minZ = z < minZ ? z : minZ;\n\n    maxX = x > maxX ? x : maxX;\n    maxY = y > maxY ? y : maxY;\n    maxZ = z > maxZ ? z : maxZ;\n  }\n  return [\n    [minX, minY, minZ],\n    [maxX, maxY, maxZ]\n  ];\n}\n"], "mappings": ";;;;;;;AAiBO,SAASA,WAAWA,CAACC,UAAuB,EAAU;EAC3D,IAAIC,IAAI,GAAG,CAAC;EACZ,KAAK,MAAMC,aAAa,IAAIF,UAAU,EAAE;IACtC,MAAMG,SAAS,GAAGH,UAAU,CAACE,aAAa,CAAC;IAC3C,IAAIE,WAAW,CAACC,MAAM,CAACF,SAAS,CAAC,EAAE;MAEjCF,IAAI,IAAIE,SAAS,CAACG,UAAU,GAAGH,SAAS,CAACI,iBAAiB;IAC5D;EACF;EACA,OAAON,IAAI;AACb;AAQO,SAASO,kBAAkBA,CAACR,UAA0B,EAAe;EAC1E,IAAIS,IAAI,GAAGC,QAAQ;EACnB,IAAIC,IAAI,GAAGD,QAAQ;EACnB,IAAIE,IAAI,GAAGF,QAAQ;EACnB,IAAIG,IAAI,GAAG,CAACH,QAAQ;EACpB,IAAII,IAAI,GAAG,CAACJ,QAAQ;EACpB,IAAIK,IAAI,GAAG,CAACL,QAAQ;EAEpB,MAAMM,SAAS,GAAGhB,UAAU,CAACiB,QAAQ,GAAGjB,UAAU,CAACiB,QAAQ,CAACC,KAAK,GAAG,EAAE;EACtE,MAAMC,GAAG,GAAGH,SAAS,IAAIA,SAAS,CAACI,MAAM;EAEzC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,IAAI,CAAC,EAAE;IAC/B,MAAMC,CAAC,GAAGN,SAAS,CAACK,CAAC,CAAC;IACtB,MAAME,CAAC,GAAGP,SAAS,CAACK,CAAC,GAAG,CAAC,CAAC;IAC1B,MAAMG,CAAC,GAAGR,SAAS,CAACK,CAAC,GAAG,CAAC,CAAC;IAE1BZ,IAAI,GAAGa,CAAC,GAAGb,IAAI,GAAGa,CAAC,GAAGb,IAAI;IAC1BE,IAAI,GAAGY,CAAC,GAAGZ,IAAI,GAAGY,CAAC,GAAGZ,IAAI;IAC1BC,IAAI,GAAGY,CAAC,GAAGZ,IAAI,GAAGY,CAAC,GAAGZ,IAAI;IAE1BC,IAAI,GAAGS,CAAC,GAAGT,IAAI,GAAGS,CAAC,GAAGT,IAAI;IAC1BC,IAAI,GAAGS,CAAC,GAAGT,IAAI,GAAGS,CAAC,GAAGT,IAAI;IAC1BC,IAAI,GAAGS,CAAC,GAAGT,IAAI,GAAGS,CAAC,GAAGT,IAAI;EAC5B;EACA,OAAO,CACL,CAACN,IAAI,EAAEE,IAAI,EAAEC,IAAI,CAAC,EAClB,CAACC,IAAI,EAAEC,IAAI,EAAEC,IAAI,CAAC,CACnB;AACH"}