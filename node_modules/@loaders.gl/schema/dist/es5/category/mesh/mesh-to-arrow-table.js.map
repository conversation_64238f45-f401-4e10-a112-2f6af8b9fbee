{"version": 3, "file": "mesh-to-arrow-table.js", "names": ["_Arrow", "require", "_arrowTypeUtils", "_deduceMeshSchema", "convertMeshToArrowTable", "mesh", "batchSize", "_mesh$schema", "vectors", "fields", "<PERSON><PERSON><PERSON>", "attributes", "attribute", "value", "size", "type", "getArrowType", "vector", "getArrowVector", "listType", "FixedSizeList", "Field", "field", "makeMeshAttributeMetadata", "data", "Data", "length", "undefined", "listVector", "FixedSizeListVector", "push", "schema", "<PERSON><PERSON><PERSON>", "metadata", "Map", "recordBatch", "RecordBatch", "table", "Table"], "sources": ["../../../../src/category/mesh/mesh-to-arrow-table.ts"], "sourcesContent": ["import {\n  Table,\n  Schema,\n  RecordBatch,\n  FixedSizeList,\n  Field,\n  Data,\n  FixedSizeListVector\n} from 'apache-arrow/Arrow.dom';\nimport {AbstractVector} from 'apache-arrow/vector';\nimport {getArrowType, getArrowVector} from '../../lib/arrow/arrow-type-utils';\nimport type {Mesh} from './mesh-types';\nimport {makeMeshAttributeMetadata} from './deduce-mesh-schema';\n\n/**\n * * Convert a loaders.gl Mesh to an Apache Arrow Table\n * @param mesh\n * @param metadata\n * @param batchSize\n * @returns\n */\nexport function convertMeshToArrowTable(mesh: Mesh, batchSize?: number): Table {\n  const vectors: AbstractVector[] = [];\n  const fields: Field[] = [];\n  for (const attributeKey in mesh.attributes) {\n    const attribute = mesh.attributes[attributeKey];\n    const {value, size = 1} = attribute;\n    const type = getArrowType(value);\n    const vector = getArrowVector(value);\n    const listType = new FixedSizeList(size, new Field('value', type));\n    const field = new Field(attributeKey, listType, false, makeMeshAttributeMetadata(attribute));\n    const data = new Data(listType, 0, value.length / size, 0, undefined, [vector]);\n    const listVector = new FixedSizeListVector(data);\n    vectors.push(listVector);\n    fields.push(field);\n  }\n  const schema = new Schema(fields, mesh?.schema?.metadata || new Map<string, string>());\n  const recordBatch = new RecordBatch(schema, vectors[0].length, vectors);\n  const table = new Table(schema, recordBatch);\n  return table;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAUA,IAAAC,eAAA,GAAAD,OAAA;AAEA,IAAAE,iBAAA,GAAAF,OAAA;AASO,SAASG,uBAAuBA,CAACC,IAAU,EAAEC,SAAkB,EAAS;EAAA,IAAAC,YAAA;EAC7E,MAAMC,OAAyB,GAAG,EAAE;EACpC,MAAMC,MAAe,GAAG,EAAE;EAC1B,KAAK,MAAMC,YAAY,IAAIL,IAAI,CAACM,UAAU,EAAE;IAC1C,MAAMC,SAAS,GAAGP,IAAI,CAACM,UAAU,CAACD,YAAY,CAAC;IAC/C,MAAM;MAACG,KAAK;MAAEC,IAAI,GAAG;IAAC,CAAC,GAAGF,SAAS;IACnC,MAAMG,IAAI,GAAG,IAAAC,4BAAY,EAACH,KAAK,CAAC;IAChC,MAAMI,MAAM,GAAG,IAAAC,8BAAc,EAACL,KAAK,CAAC;IACpC,MAAMM,QAAQ,GAAG,IAAIC,oBAAa,CAACN,IAAI,EAAE,IAAIO,YAAK,CAAC,OAAO,EAAEN,IAAI,CAAC,CAAC;IAClE,MAAMO,KAAK,GAAG,IAAID,YAAK,CAACX,YAAY,EAAES,QAAQ,EAAE,KAAK,EAAE,IAAAI,2CAAyB,EAACX,SAAS,CAAC,CAAC;IAC5F,MAAMY,IAAI,GAAG,IAAIC,WAAI,CAACN,QAAQ,EAAE,CAAC,EAAEN,KAAK,CAACa,MAAM,GAAGZ,IAAI,EAAE,CAAC,EAAEa,SAAS,EAAE,CAACV,MAAM,CAAC,CAAC;IAC/E,MAAMW,UAAU,GAAG,IAAIC,0BAAmB,CAACL,IAAI,CAAC;IAChDhB,OAAO,CAACsB,IAAI,CAACF,UAAU,CAAC;IACxBnB,MAAM,CAACqB,IAAI,CAACR,KAAK,CAAC;EACpB;EACA,MAAMS,MAAM,GAAG,IAAIC,aAAM,CAACvB,MAAM,EAAE,CAAAJ,IAAI,aAAJA,IAAI,wBAAAE,YAAA,GAAJF,IAAI,CAAE0B,MAAM,cAAAxB,YAAA,uBAAZA,YAAA,CAAc0B,QAAQ,KAAI,IAAIC,GAAG,CAAiB,CAAC,CAAC;EACtF,MAAMC,WAAW,GAAG,IAAIC,kBAAW,CAACL,MAAM,EAAEvB,OAAO,CAAC,CAAC,CAAC,CAACkB,MAAM,EAAElB,OAAO,CAAC;EACvE,MAAM6B,KAAK,GAAG,IAAIC,YAAK,CAACP,MAAM,EAAEI,WAAW,CAAC;EAC5C,OAAOE,KAAK;AACd"}