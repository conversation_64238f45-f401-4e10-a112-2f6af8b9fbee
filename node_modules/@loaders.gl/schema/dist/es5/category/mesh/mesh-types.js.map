{"version": 3, "file": "mesh-types.js", "names": [], "sources": ["../../../../src/category/mesh/mesh-types.ts"], "sourcesContent": ["import type {Schema} from '../../lib/schema/schema';\nimport type {TypedArray} from '../../types';\nimport type {ColumnarTable, ArrowTable} from '../table/table-types';\n\n/** Mesh as columnar table */\nexport interface MeshTable extends ColumnarTable {\n  // shape: 'mesh-table';\n  topology: 'point-list' | 'triangle-list' | 'triangle-strip';\n  indices?: MeshAttribute;\n}\n\n/** Mesh as arrow table */\nexport interface MeshArrowTable extends ArrowTable {\n  // shape: 'mesh-arrow-table';\n  topology: 'point-list' | 'triangle-list' | 'triangle-strip';\n  indices?: MeshAttribute;\n}\n\n/** Geometry part of a Mesh (compatible with a standard luma.gl \"mesh\") */\nexport type MeshGeometry = {\n  attributes: {[attributeName: string]: MeshAttribute};\n  indices?: MeshAttribute;\n  topology: 'point-list' | 'triangle-list' | 'triangle-strip';\n  mode: number;\n};\n\n/** Geometry and metadata for a Mesh (compatible with a standard luma.gl \"mesh\") */\nexport type Mesh = MeshGeometry & {\n  loader?: string;\n  loaderData?: {[key: string]: any};\n  header?: {\n    vertexCount: number;\n    boundingBox?: [number[], number[]];\n  };\n  schema: Schema;\n};\n\n/**\n * luma.gl compatible attribute descriptors\n * Can be mapped to any WebGL framework\n */\nexport type MeshAttribute = {\n  value: TypedArray;\n  size: number;\n  byteOffset?: number;\n  byteStride?: number;\n  normalized?: boolean;\n};\n\n/** A map of mesh attributes keyed by attribute names */\nexport type MeshAttributes = Record<string, MeshAttribute>;\n"], "mappings": ""}