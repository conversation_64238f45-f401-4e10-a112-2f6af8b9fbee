"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.convertMeshToArrowTable = convertMeshToArrowTable;
var _Arrow = require("apache-arrow/Arrow.dom");
var _arrowTypeUtils = require("../../lib/arrow/arrow-type-utils");
var _deduceMeshSchema = require("./deduce-mesh-schema");
function convertMeshToArrowTable(mesh, batchSize) {
  var _mesh$schema;
  const vectors = [];
  const fields = [];
  for (const attributeKey in mesh.attributes) {
    const attribute = mesh.attributes[attributeKey];
    const {
      value,
      size = 1
    } = attribute;
    const type = (0, _arrowTypeUtils.getArrowType)(value);
    const vector = (0, _arrowTypeUtils.getArrowVector)(value);
    const listType = new _Arrow.FixedSizeList(size, new _Arrow.Field('value', type));
    const field = new _Arrow.Field(attributeKey, listType, false, (0, _deduceMeshSchema.makeMeshAttributeMetadata)(attribute));
    const data = new _Arrow.Data(listType, 0, value.length / size, 0, undefined, [vector]);
    const listVector = new _Arrow.FixedSizeListVector(data);
    vectors.push(listVector);
    fields.push(field);
  }
  const schema = new _Arrow.Schema(fields, (mesh === null || mesh === void 0 ? void 0 : (_mesh$schema = mesh.schema) === null || _mesh$schema === void 0 ? void 0 : _mesh$schema.metadata) || new Map());
  const recordBatch = new _Arrow.RecordBatch(schema, vectors[0].length, vectors);
  const table = new _Arrow.Table(schema, recordBatch);
  return table;
}
//# sourceMappingURL=mesh-to-arrow-table.js.map