{"version": 3, "file": "deduce-mesh-schema.js", "names": ["_schema", "require", "_arrowLikeTypeUtils", "deduceMeshSchema", "attributes", "metadata", "fields", "deduce<PERSON><PERSON><PERSON><PERSON>s", "<PERSON><PERSON><PERSON>", "deduce<PERSON><PERSON><PERSON><PERSON>", "attributeName", "attribute", "optionalMetadata", "type", "getArrowTypeFromTypedArray", "value", "makeMeshAttributeMetadata", "field", "Field", "FixedSizeList", "size", "push", "result", "Map", "set", "byteOffset", "toString", "byteStride", "normalized"], "sources": ["../../../../src/category/mesh/deduce-mesh-schema.ts"], "sourcesContent": ["import {MeshAttribute, MeshAttributes} from './mesh-types';\nimport {Schem<PERSON>, Field, FixedSizeList} from '../../lib/schema/schema';\nimport {getArrowTypeFromTypedArray} from '../../lib/arrow/arrow-like-type-utils';\n\n/**\n * Create a schema for mesh attributes data\n * @param attributes\n * @param metadata\n * @returns\n */\nexport function deduceMeshSchema(\n  attributes: MeshAttributes,\n  metadata?: Map<string, string>\n): Schema {\n  const fields = deduceMeshFields(attributes);\n  return new Schema(fields, metadata);\n}\n\n/**\n * Create arrow-like schema field for mesh attribute\n * @param attributeName\n * @param attribute\n * @param optionalMetadata\n * @returns\n */\nexport function deduceMeshField(\n  attributeName: string,\n  attribute: MeshAttribute,\n  optionalMetadata?: Map<string, string>\n): Field {\n  const type = getArrowTypeFromTypedArray(attribute.value);\n  const metadata = optionalMetadata ? optionalMetadata : makeMeshAttributeMetadata(attribute);\n  const field = new Field(\n    attributeName,\n    new FixedSizeList(attribute.size, new Field('value', type)),\n    false,\n    metadata\n  );\n  return field;\n}\n\n/**\n * Create fields array for mesh attributes\n * @param attributes\n * @returns\n */\nfunction deduceMeshFields(attributes: MeshAttributes): Field[] {\n  const fields: Field[] = [];\n  for (const attributeName in attributes) {\n    const attribute: MeshAttribute = attributes[attributeName];\n    fields.push(deduceMeshField(attributeName, attribute));\n  }\n  return fields;\n}\n\n/**\n * Make metadata by mesh attribute properties\n * @param attribute\n * @returns\n */\nexport function makeMeshAttributeMetadata(attribute: MeshAttribute): Map<string, string> {\n  const result = new Map();\n  if ('byteOffset' in attribute) {\n    result.set('byteOffset', attribute.byteOffset!.toString(10));\n  }\n  if ('byteStride' in attribute) {\n    result.set('byteStride', attribute.byteStride!.toString(10));\n  }\n  if ('normalized' in attribute) {\n    result.set('normalized', attribute.normalized!.toString());\n  }\n  return result;\n}\n"], "mappings": ";;;;;;;;AACA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,mBAAA,GAAAD,OAAA;AAQO,SAASE,gBAAgBA,CAC9BC,UAA0B,EAC1BC,QAA8B,EACtB;EACR,MAAMC,MAAM,GAAGC,gBAAgB,CAACH,UAAU,CAAC;EAC3C,OAAO,IAAII,cAAM,CAACF,MAAM,EAAED,QAAQ,CAAC;AACrC;AASO,SAASI,eAAeA,CAC7BC,aAAqB,EACrBC,SAAwB,EACxBC,gBAAsC,EAC/B;EACP,MAAMC,IAAI,GAAG,IAAAC,8CAA0B,EAACH,SAAS,CAACI,KAAK,CAAC;EACxD,MAAMV,QAAQ,GAAGO,gBAAgB,GAAGA,gBAAgB,GAAGI,yBAAyB,CAACL,SAAS,CAAC;EAC3F,MAAMM,KAAK,GAAG,IAAIC,aAAK,CACrBR,aAAa,EACb,IAAIS,qBAAa,CAACR,SAAS,CAACS,IAAI,EAAE,IAAIF,aAAK,CAAC,OAAO,EAAEL,IAAI,CAAC,CAAC,EAC3D,KAAK,EACLR,QACF,CAAC;EACD,OAAOY,KAAK;AACd;AAOA,SAASV,gBAAgBA,CAACH,UAA0B,EAAW;EAC7D,MAAME,MAAe,GAAG,EAAE;EAC1B,KAAK,MAAMI,aAAa,IAAIN,UAAU,EAAE;IACtC,MAAMO,SAAwB,GAAGP,UAAU,CAACM,aAAa,CAAC;IAC1DJ,MAAM,CAACe,IAAI,CAACZ,eAAe,CAACC,aAAa,EAAEC,SAAS,CAAC,CAAC;EACxD;EACA,OAAOL,MAAM;AACf;AAOO,SAASU,yBAAyBA,CAACL,SAAwB,EAAuB;EACvF,MAAMW,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;EACxB,IAAI,YAAY,IAAIZ,SAAS,EAAE;IAC7BW,MAAM,CAACE,GAAG,CAAC,YAAY,EAAEb,SAAS,CAACc,UAAU,CAAEC,QAAQ,CAAC,EAAE,CAAC,CAAC;EAC9D;EACA,IAAI,YAAY,IAAIf,SAAS,EAAE;IAC7BW,MAAM,CAACE,GAAG,CAAC,YAAY,EAAEb,SAAS,CAACgB,UAAU,CAAED,QAAQ,CAAC,EAAE,CAAC,CAAC;EAC9D;EACA,IAAI,YAAY,IAAIf,SAAS,EAAE;IAC7BW,MAAM,CAACE,GAAG,CAAC,YAAY,EAAEb,SAAS,CAACiB,UAAU,CAAEF,QAAQ,CAAC,CAAC,CAAC;EAC5D;EACA,OAAOJ,MAAM;AACf"}