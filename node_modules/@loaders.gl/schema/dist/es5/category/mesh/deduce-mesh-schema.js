"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.deduceMeshField = deduceMeshField;
exports.deduceMeshSchema = deduceMeshSchema;
exports.makeMeshAttributeMetadata = makeMeshAttributeMetadata;
var _schema = require("../../lib/schema/schema");
var _arrowLikeTypeUtils = require("../../lib/arrow/arrow-like-type-utils");
function deduceMeshSchema(attributes, metadata) {
  const fields = deduceMeshFields(attributes);
  return new _schema.Schema(fields, metadata);
}
function deduceMeshField(attributeName, attribute, optionalMetadata) {
  const type = (0, _arrowLikeTypeUtils.getArrowTypeFromTypedArray)(attribute.value);
  const metadata = optionalMetadata ? optionalMetadata : makeMeshAttributeMetadata(attribute);
  const field = new _schema.Field(attributeName, new _schema.FixedSizeList(attribute.size, new _schema.Field('value', type)), false, metadata);
  return field;
}
function deduceMeshFields(attributes) {
  const fields = [];
  for (const attributeName in attributes) {
    const attribute = attributes[attributeName];
    fields.push(deduceMeshField(attributeName, attribute));
  }
  return fields;
}
function makeMeshAttributeMetadata(attribute) {
  const result = new Map();
  if ('byteOffset' in attribute) {
    result.set('byteOffset', attribute.byteOffset.toString(10));
  }
  if ('byteStride' in attribute) {
    result.set('byteStride', attribute.byteStride.toString(10));
  }
  if ('normalized' in attribute) {
    result.set('normalized', attribute.normalized.toString());
  }
  return result;
}
//# sourceMappingURL=deduce-mesh-schema.js.map