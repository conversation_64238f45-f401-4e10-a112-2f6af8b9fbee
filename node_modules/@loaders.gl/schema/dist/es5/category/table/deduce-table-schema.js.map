{"version": 3, "file": "deduce-table-schema.js", "names": ["deduceTableSchema", "table", "schema", "deducedSchema", "Array", "isArray", "deduceSchemaForRowTable", "deduceSchemaForColumnarTable", "Object", "assign", "columnarTable", "field", "column", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "length", "value", "deduceTypeFromValue", "rowTable", "row", "Date", "Number", "Float32Array", "String"], "sources": ["../../../../src/category/table/deduce-table-schema.ts"], "sourcesContent": ["// Type deduction\nimport {\n  Schema\n  // Int,\n  // Int8,\n  // Int16,\n  // Int32,\n  // Uint8,\n  // Uint16,\n  // Uint32,\n  // Float32,\n  // Float64\n  // Bool,\n  // Utf8,\n  // TimestampMillisecond,\n  // Null\n} from '../../lib/schema/schema';\n\n// const TYPED_ARRAY_TO_TYPE = {\n//   Int8Array: new Int8(),\n//   Int16Array: new Int16(),\n//   Int32Array: new Int32(),\n//   Uint8Array: new Uint8(),\n//   Uint8ClampedArray: new Uint8(),\n//   Uint16Array: new Uint16(),\n//   Uint32Array: new Uint32(),\n//   Float32Array: new Float32(),\n//   Float64Array: new Float64()\n// };\n\n// if (typeof BigInt64Array !== 'undefined') {\n//   TYPED_ARRAY_TO_TYPE.BigInt64Array = new Int64();\n//   TYPED_ARRAY_TO_TYPE.BigUint64Array = new Uint64();\n// }\n\n/**\n * SCHEMA SUPPORT - AUTODEDUCTION\n * @param {*} table\n * @param {*} schema\n * @returns\n */\nexport function deduceTableSchema(table, schema?: Schema) {\n  const deducedSchema = Array.isArray(table)\n    ? deduceSchemaForRowTable(table)\n    : deduceSchemaForColumnarTable(table);\n  // Deduced schema will fill in missing info from partial options.schema, if provided\n  return Object.assign(deducedSchema, schema);\n}\n\nfunction deduceSchemaForColumnarTable(columnarTable) {\n  const schema = {};\n  for (const field in columnarTable) {\n    const column = columnarTable[field];\n    // Check if column is typed, if so we are done\n    if (ArrayBuffer.isView(column)) {\n      schema[field] = column.constructor;\n      // else we need data\n    } else if (column.length) {\n      const value = column[0];\n      schema[field] = deduceTypeFromValue(value);\n      // TODO - support nested schemas?\n    }\n    // else we mark as present but unknow\n    schema[field] = schema[field] || null;\n  }\n  return schema;\n}\n\nfunction deduceSchemaForRowTable(rowTable) {\n  const schema = {};\n  if (rowTable.length) {\n    const row = rowTable[0];\n    // TODO - Could look at additional rows if nulls in first row\n    for (const field in row) {\n      const value = row[field];\n      schema[field] = deduceTypeFromValue(value);\n    }\n  }\n  return schema;\n}\n\nfunction deduceTypeFromValue(value) {\n  if (value instanceof Date) {\n    return Date;\n  } else if (value instanceof Number) {\n    return Float32Array;\n  } else if (typeof value === 'string') {\n    return String;\n  }\n  return null;\n}\n\n/*\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction deduceSchema(rows) {\n  const row = rows[0];\n\n  const schema = {};\n  let i = 0;\n  for (const columnName in row) {\n    const value = row[columnName];\n    switch (typeof value) {\n      case 'number':\n      case 'boolean':\n        // TODO - booleans could be handled differently...\n        schema[columnName] = {name: String(columnName), index: i, type: Float32Array};\n        break;\n\n      case 'object':\n        schema[columnName] = {name: String(columnName), index: i, type: Array};\n        break;\n\n      case 'string':\n      default:\n        schema[columnName] = {name: String(columnName), index: i, type: Array};\n      // We currently only handle numeric rows\n      // TODO we could offer a function to map strings to numbers?\n    }\n    i++;\n  }\n  return schema;\n}\n*/\n"], "mappings": ";;;;;;AAyCO,SAASA,iBAAiBA,CAACC,KAAK,EAAEC,MAAe,EAAE;EACxD,MAAMC,aAAa,GAAGC,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC,GACtCK,uBAAuB,CAACL,KAAK,CAAC,GAC9BM,4BAA4B,CAACN,KAAK,CAAC;EAEvC,OAAOO,MAAM,CAACC,MAAM,CAACN,aAAa,EAAED,MAAM,CAAC;AAC7C;AAEA,SAASK,4BAA4BA,CAACG,aAAa,EAAE;EACnD,MAAMR,MAAM,GAAG,CAAC,CAAC;EACjB,KAAK,MAAMS,KAAK,IAAID,aAAa,EAAE;IACjC,MAAME,MAAM,GAAGF,aAAa,CAACC,KAAK,CAAC;IAEnC,IAAIE,WAAW,CAACC,MAAM,CAACF,MAAM,CAAC,EAAE;MAC9BV,MAAM,CAACS,KAAK,CAAC,GAAGC,MAAM,CAACG,WAAW;IAEpC,CAAC,MAAM,IAAIH,MAAM,CAACI,MAAM,EAAE;MACxB,MAAMC,KAAK,GAAGL,MAAM,CAAC,CAAC,CAAC;MACvBV,MAAM,CAACS,KAAK,CAAC,GAAGO,mBAAmB,CAACD,KAAK,CAAC;IAE5C;IAEAf,MAAM,CAACS,KAAK,CAAC,GAAGT,MAAM,CAACS,KAAK,CAAC,IAAI,IAAI;EACvC;EACA,OAAOT,MAAM;AACf;AAEA,SAASI,uBAAuBA,CAACa,QAAQ,EAAE;EACzC,MAAMjB,MAAM,GAAG,CAAC,CAAC;EACjB,IAAIiB,QAAQ,CAACH,MAAM,EAAE;IACnB,MAAMI,GAAG,GAAGD,QAAQ,CAAC,CAAC,CAAC;IAEvB,KAAK,MAAMR,KAAK,IAAIS,GAAG,EAAE;MACvB,MAAMH,KAAK,GAAGG,GAAG,CAACT,KAAK,CAAC;MACxBT,MAAM,CAACS,KAAK,CAAC,GAAGO,mBAAmB,CAACD,KAAK,CAAC;IAC5C;EACF;EACA,OAAOf,MAAM;AACf;AAEA,SAASgB,mBAAmBA,CAACD,KAAK,EAAE;EAClC,IAAIA,KAAK,YAAYI,IAAI,EAAE;IACzB,OAAOA,IAAI;EACb,CAAC,MAAM,IAAIJ,KAAK,YAAYK,MAAM,EAAE;IAClC,OAAOC,YAAY;EACrB,CAAC,MAAM,IAAI,OAAON,KAAK,KAAK,QAAQ,EAAE;IACpC,OAAOO,MAAM;EACf;EACA,OAAO,IAAI;AACb"}