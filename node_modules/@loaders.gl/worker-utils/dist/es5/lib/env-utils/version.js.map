{"version": 3, "file": "version.js", "names": ["DEFAULT_VERSION", "VERSION", "exports", "console", "error"], "sources": ["../../../../src/lib/env-utils/version.ts"], "sourcesContent": ["// Version constant cannot be imported, it needs to correspond to the build version of **this** module.\n// __VERSION__ is injected by babel-plugin-version-inline\n\n// Change to `latest` on production branches\nconst DEFAULT_VERSION = 'latest';\ndeclare let __VERSION__: string;\nexport const VERSION = typeof __VERSION__ !== 'undefined' ? __VERSION__ : DEFAULT_VERSION;\nif (typeof __VERSION__ === 'undefined') {\n  // eslint-disable-next-line\n  console.error(\n    'loaders.gl: The __VERSION__ variable is not injected using babel plugin. Latest unstable workers would be fetched from the CDN.'\n  );\n}\n"], "mappings": ";;;;;;AAIA,MAAMA,eAAe,GAAG,QAAQ;AAEzB,MAAMC,OAAO,GAAG,eAAkB,KAAK,WAAW,cAAiBD,eAAe;AAACE,OAAA,CAAAD,OAAA,GAAAA,OAAA;AAC1F,IAAI,eAAkB,KAAK,WAAW,EAAE;EAEtCE,OAAO,CAACC,KAAK,CACX,iIACF,CAAC;AACH"}