"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.VERSION = void 0;
const DEFAULT_VERSION = 'latest';
const VERSION = typeof "3.4.15" !== 'undefined' ? "3.4.15" : DEFAULT_VERSION;
exports.VERSION = VERSION;
if (typeof "3.4.15" === 'undefined') {
  console.error('loaders.gl: The __VERSION__ variable is not injected using babel plugin. Latest unstable workers would be fetched from the CDN.');
}
//# sourceMappingURL=version.js.map