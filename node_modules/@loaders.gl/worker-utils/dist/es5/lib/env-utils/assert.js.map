{"version": 3, "file": "assert.js", "names": ["assert", "condition", "message", "Error"], "sources": ["../../../../src/lib/env-utils/assert.ts"], "sourcesContent": ["// Replacement for the external assert method to reduce bundle size\n// Note: We don't use the second \"message\" argument in calling code,\n// so no need to support it here\n\n/** Throws an `Error` with the optional `message` if `condition` is falsy */\nexport function assert(condition: any, message?: string): void {\n  if (!condition) {\n    throw new Error(message || 'loaders.gl assertion failed.');\n  }\n}\n"], "mappings": ";;;;;;AAKO,SAASA,MAAMA,CAACC,SAAc,EAAEC,OAAgB,EAAQ;EAC7D,IAAI,CAACD,SAAS,EAAE;IACd,MAAM,IAAIE,KAAK,CAACD,OAAO,IAAI,8BAA8B,CAAC;EAC5D;AACF"}