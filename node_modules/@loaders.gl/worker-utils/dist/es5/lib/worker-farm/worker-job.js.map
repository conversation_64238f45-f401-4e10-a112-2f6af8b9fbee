{"version": 3, "file": "worker-job.js", "names": ["_assert", "require", "<PERSON><PERSON><PERSON>", "constructor", "job<PERSON>ame", "workerThread", "_defineProperty2", "default", "name", "result", "Promise", "resolve", "reject", "_resolve", "_reject", "postMessage", "type", "payload", "source", "done", "value", "assert", "isRunning", "error", "exports"], "sources": ["../../../../src/lib/worker-farm/worker-job.ts"], "sourcesContent": ["import type {WorkerMessageType, WorkerMessagePayload} from '../../types';\nimport WorkerThread from './worker-thread';\nimport {assert} from '../env-utils/assert';\n\n/**\n * Represents one Job handled by a WorkerPool or WorkerFarm\n */\nexport default class WorkerJob {\n  readonly name: string;\n  readonly workerThread: WorkerThread;\n  isRunning: boolean = true;\n  /** Promise that resolves when Job is done */\n  readonly result: Promise<any>;\n\n  private _resolve: (value: any) => void = () => {};\n  private _reject: (reason?: any) => void = () => {};\n\n  constructor(jobName: string, workerThread: WorkerThread) {\n    this.name = jobName;\n    this.workerThread = workerThread;\n    this.result = new Promise((resolve, reject) => {\n      this._resolve = resolve;\n      this._reject = reject;\n    });\n  }\n\n  /**\n   * Send a message to the job's worker thread\n   * @param data any data structure, ideally consisting mostly of transferrable objects\n   */\n  postMessage(type: WorkerMessageType, payload: WorkerMessagePayload): void {\n    this.workerThread.postMessage({\n      source: 'loaders.gl', // Lets worker ignore unrelated messages\n      type,\n      payload\n    });\n  }\n\n  /**\n   * Call to resolve the `result` Promise with the supplied value\n   */\n  done(value: any): void {\n    assert(this.isRunning);\n    this.isRunning = false;\n    this._resolve(value);\n  }\n\n  /**\n   * Call to reject the `result` Promise with the supplied error\n   */\n  error(error: Error): void {\n    assert(this.isRunning);\n    this.isRunning = false;\n    this._reject(error);\n  }\n}\n"], "mappings": ";;;;;;;;AAEA,IAAAA,OAAA,GAAAC,OAAA;AAKe,MAAMC,SAAS,CAAC;EAU7BC,WAAWA,CAACC,OAAe,EAAEC,YAA0B,EAAE;IAAA,IAAAC,gBAAA,CAAAC,OAAA;IAAA,IAAAD,gBAAA,CAAAC,OAAA;IAAA,IAAAD,gBAAA,CAAAC,OAAA,qBAPpC,IAAI;IAAA,IAAAD,gBAAA,CAAAC,OAAA;IAAA,IAAAD,gBAAA,CAAAC,OAAA,oBAIgB,MAAM,CAAC,CAAC;IAAA,IAAAD,gBAAA,CAAAC,OAAA,mBACP,MAAM,CAAC,CAAC;IAGhD,IAAI,CAACC,IAAI,GAAGJ,OAAO;IACnB,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACI,MAAM,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MAC7C,IAAI,CAACC,QAAQ,GAAGF,OAAO;MACvB,IAAI,CAACG,OAAO,GAAGF,MAAM;IACvB,CAAC,CAAC;EACJ;EAMAG,WAAWA,CAACC,IAAuB,EAAEC,OAA6B,EAAQ;IACxE,IAAI,CAACZ,YAAY,CAACU,WAAW,CAAC;MAC5BG,MAAM,EAAE,YAAY;MACpBF,IAAI;MACJC;IACF,CAAC,CAAC;EACJ;EAKAE,IAAIA,CAACC,KAAU,EAAQ;IACrB,IAAAC,cAAM,EAAC,IAAI,CAACC,SAAS,CAAC;IACtB,IAAI,CAACA,SAAS,GAAG,KAAK;IACtB,IAAI,CAACT,QAAQ,CAACO,KAAK,CAAC;EACtB;EAKAG,KAAKA,CAACA,KAAY,EAAQ;IACxB,IAAAF,cAAM,EAAC,IAAI,CAACC,SAAS,CAAC;IACtB,IAAI,CAACA,SAAS,GAAG,KAAK;IACtB,IAAI,CAACR,OAAO,CAACS,KAAK,CAAC;EACrB;AACF;AAACC,OAAA,CAAAjB,OAAA,GAAAL,SAAA"}