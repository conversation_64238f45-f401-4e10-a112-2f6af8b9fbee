"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _worker_threads = require("../node/worker_threads");
var _globals = require("../env-utils/globals");
var _assert = require("../env-utils/assert");
var _getLoadableWorkerUrl = require("../worker-utils/get-loadable-worker-url");
var _getTransferList = require("../worker-utils/get-transfer-list");
const NOOP = () => {};
class WorkerThread {
  static isSupported() {
    return typeof Worker !== 'undefined' && _globals.isBrowser || typeof _worker_threads.NodeWorker !== 'undefined' && !_globals.isBrowser;
  }
  constructor(props) {
    (0, _defineProperty2.default)(this, "name", void 0);
    (0, _defineProperty2.default)(this, "source", void 0);
    (0, _defineProperty2.default)(this, "url", void 0);
    (0, _defineProperty2.default)(this, "terminated", false);
    (0, _defineProperty2.default)(this, "worker", void 0);
    (0, _defineProperty2.default)(this, "onMessage", void 0);
    (0, _defineProperty2.default)(this, "onError", void 0);
    (0, _defineProperty2.default)(this, "_loadableURL", '');
    const {
      name,
      source,
      url
    } = props;
    (0, _assert.assert)(source || url);
    this.name = name;
    this.source = source;
    this.url = url;
    this.onMessage = NOOP;
    this.onError = error => console.log(error);
    this.worker = _globals.isBrowser ? this._createBrowserWorker() : this._createNodeWorker();
  }
  destroy() {
    this.onMessage = NOOP;
    this.onError = NOOP;
    this.worker.terminate();
    this.terminated = true;
  }
  get isRunning() {
    return Boolean(this.onMessage);
  }
  postMessage(data, transferList) {
    transferList = transferList || (0, _getTransferList.getTransferList)(data);
    this.worker.postMessage(data, transferList);
  }
  _getErrorFromErrorEvent(event) {
    let message = 'Failed to load ';
    message += "worker ".concat(this.name, " from ").concat(this.url, ". ");
    if (event.message) {
      message += "".concat(event.message, " in ");
    }
    if (event.lineno) {
      message += ":".concat(event.lineno, ":").concat(event.colno);
    }
    return new Error(message);
  }
  _createBrowserWorker() {
    this._loadableURL = (0, _getLoadableWorkerUrl.getLoadableWorkerURL)({
      source: this.source,
      url: this.url
    });
    const worker = new Worker(this._loadableURL, {
      name: this.name
    });
    worker.onmessage = event => {
      if (!event.data) {
        this.onError(new Error('No data received'));
      } else {
        this.onMessage(event.data);
      }
    };
    worker.onerror = error => {
      this.onError(this._getErrorFromErrorEvent(error));
      this.terminated = true;
    };
    worker.onmessageerror = event => console.error(event);
    return worker;
  }
  _createNodeWorker() {
    let worker;
    if (this.url) {
      const absolute = this.url.includes(':/') || this.url.startsWith('/');
      const url = absolute ? this.url : "./".concat(this.url);
      worker = new _worker_threads.NodeWorker(url, {
        eval: false
      });
    } else if (this.source) {
      worker = new _worker_threads.NodeWorker(this.source, {
        eval: true
      });
    } else {
      throw new Error('no worker');
    }
    worker.on('message', data => {
      this.onMessage(data);
    });
    worker.on('error', error => {
      this.onError(error);
    });
    worker.on('exit', code => {});
    return worker;
  }
}
exports.default = WorkerThread;
//# sourceMappingURL=worker-thread.js.map