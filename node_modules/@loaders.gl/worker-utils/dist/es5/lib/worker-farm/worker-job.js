"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _assert = require("../env-utils/assert");
class WorkerJob {
  constructor(jobName, workerThread) {
    (0, _defineProperty2.default)(this, "name", void 0);
    (0, _defineProperty2.default)(this, "workerThread", void 0);
    (0, _defineProperty2.default)(this, "isRunning", true);
    (0, _defineProperty2.default)(this, "result", void 0);
    (0, _defineProperty2.default)(this, "_resolve", () => {});
    (0, _defineProperty2.default)(this, "_reject", () => {});
    this.name = jobName;
    this.workerThread = workerThread;
    this.result = new Promise((resolve, reject) => {
      this._resolve = resolve;
      this._reject = reject;
    });
  }
  postMessage(type, payload) {
    this.workerThread.postMessage({
      source: 'loaders.gl',
      type,
      payload
    });
  }
  done(value) {
    (0, _assert.assert)(this.isRunning);
    this.isRunning = false;
    this._resolve(value);
  }
  error(error) {
    (0, _assert.assert)(this.isRunning);
    this.isRunning = false;
    this._reject(error);
  }
}
exports.default = WorkerJob;
//# sourceMappingURL=worker-job.js.map