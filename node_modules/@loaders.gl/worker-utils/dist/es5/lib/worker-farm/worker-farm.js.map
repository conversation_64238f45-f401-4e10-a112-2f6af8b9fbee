{"version": 3, "file": "worker-farm.js", "names": ["_workerPool", "_interopRequireDefault", "require", "_workerThread", "DEFAULT_PROPS", "maxConcurrency", "maxMobileConcurrency", "reuseWorkers", "onDebug", "WorkerFarm", "isSupported", "WorkerThread", "getWorkerFarm", "props", "arguments", "length", "undefined", "_workerFarm", "setProps", "constructor", "_defineProperty2", "default", "Map", "workerPools", "destroy", "workerPool", "values", "_getWorkerPoolProps", "getWorkerPool", "options", "name", "source", "url", "get", "WorkerPool", "set", "exports"], "sources": ["../../../../src/lib/worker-farm/worker-farm.ts"], "sourcesContent": ["import WorkerPool from './worker-pool';\nimport WorkerThread from './worker-thread';\n\n/**\n * @param maxConcurrency - max count of workers\n * @param maxMobileConcurrency - max count of workers on mobile\n * @param maxConcurrency - max count of workers\n * @param reuseWorkers - if false, destroys workers when task is completed\n * @param onDebug - callback intended to allow application to log worker pool activity\n */\nexport type WorkerFarmProps = {\n  maxConcurrency?: number;\n  maxMobileConcurrency?: number;\n  reuseWorkers?: boolean;\n  onDebug?: () => void;\n};\n\nconst DEFAULT_PROPS: Required<WorkerFarmProps> = {\n  maxConcurrency: 3,\n  maxMobileConcurrency: 1,\n  reuseWorkers: true,\n  onDebug: () => {}\n};\n\n/**\n * Process multiple jobs with a \"farm\" of different workers in worker pools.\n */\nexport default class WorkerFarm {\n  private props: WorkerFarmProps;\n  private workerPools = new Map<string, WorkerPool>();\n  // singleton\n  private static _workerFarm?: WorkerFarm;\n\n  /** Checks if workers are supported on this platform */\n  static isSupported(): boolean {\n    return WorkerThread.isSupported();\n  }\n\n  /** Get the singleton instance of the global worker farm */\n  static getWorkerFarm(props: WorkerFarmProps = {}): WorkerFarm {\n    WorkerFarm._workerFarm = WorkerFarm._workerFarm || new WorkerFarm({});\n    WorkerFarm._workerFarm.setProps(props);\n    return WorkerFarm._workerFarm;\n  }\n\n  /** get global instance with WorkerFarm.getWorkerFarm() */\n  private constructor(props: WorkerFarmProps) {\n    this.props = {...DEFAULT_PROPS};\n    this.setProps(props);\n    /** @type Map<string, WorkerPool>} */\n    this.workerPools = new Map();\n  }\n\n  /**\n   * Terminate all workers in the farm\n   * @note Can free up significant memory\n   */\n  destroy(): void {\n    for (const workerPool of this.workerPools.values()) {\n      workerPool.destroy();\n    }\n    this.workerPools = new Map<string, WorkerPool>();\n  }\n\n  /**\n   * Set props used when initializing worker pools\n   * @param props\n   */\n  setProps(props: WorkerFarmProps): void {\n    this.props = {...this.props, ...props};\n    // Update worker pool props\n    for (const workerPool of this.workerPools.values()) {\n      workerPool.setProps(this._getWorkerPoolProps());\n    }\n  }\n\n  /**\n   * Returns a worker pool for the specified worker\n   * @param options - only used first time for a specific worker name\n   * @param options.name - the name of the worker - used to identify worker pool\n   * @param options.url -\n   * @param options.source -\n   * @example\n   *   const job = WorkerFarm.getWorkerFarm().getWorkerPool({name, url}).startJob(...);\n   */\n  getWorkerPool(options: {name: string; source?: string; url?: string}): WorkerPool {\n    const {name, source, url} = options;\n    let workerPool = this.workerPools.get(name);\n    if (!workerPool) {\n      workerPool = new WorkerPool({\n        name,\n        source,\n        url\n      });\n      workerPool.setProps(this._getWorkerPoolProps());\n      this.workerPools.set(name, workerPool);\n    }\n    return workerPool;\n  }\n\n  _getWorkerPoolProps() {\n    return {\n      maxConcurrency: this.props.maxConcurrency,\n      maxMobileConcurrency: this.props.maxMobileConcurrency,\n      reuseWorkers: this.props.reuseWorkers,\n      onDebug: this.props.onDebug\n    };\n  }\n}\n"], "mappings": ";;;;;;;;AAAA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAF,sBAAA,CAAAC,OAAA;AAgBA,MAAME,aAAwC,GAAG;EAC/CC,cAAc,EAAE,CAAC;EACjBC,oBAAoB,EAAE,CAAC;EACvBC,YAAY,EAAE,IAAI;EAClBC,OAAO,EAAEA,CAAA,KAAM,CAAC;AAClB,CAAC;AAKc,MAAMC,UAAU,CAAC;EAO9B,OAAOC,WAAWA,CAAA,EAAY;IAC5B,OAAOC,qBAAY,CAACD,WAAW,CAAC,CAAC;EACnC;EAGA,OAAOE,aAAaA,CAAA,EAA0C;IAAA,IAAzCC,KAAsB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAC9CL,UAAU,CAACQ,WAAW,GAAGR,UAAU,CAACQ,WAAW,IAAI,IAAIR,UAAU,CAAC,CAAC,CAAC,CAAC;IACrEA,UAAU,CAACQ,WAAW,CAACC,QAAQ,CAACL,KAAK,CAAC;IACtC,OAAOJ,UAAU,CAACQ,WAAW;EAC/B;EAGQE,WAAWA,CAACN,KAAsB,EAAE;IAAA,IAAAO,gBAAA,CAAAC,OAAA;IAAA,IAAAD,gBAAA,CAAAC,OAAA,uBAjBtB,IAAIC,GAAG,CAAqB,CAAC;IAkBjD,IAAI,CAACT,KAAK,GAAG;MAAC,GAAGT;IAAa,CAAC;IAC/B,IAAI,CAACc,QAAQ,CAACL,KAAK,CAAC;IAEpB,IAAI,CAACU,WAAW,GAAG,IAAID,GAAG,CAAC,CAAC;EAC9B;EAMAE,OAAOA,CAAA,EAAS;IACd,KAAK,MAAMC,UAAU,IAAI,IAAI,CAACF,WAAW,CAACG,MAAM,CAAC,CAAC,EAAE;MAClDD,UAAU,CAACD,OAAO,CAAC,CAAC;IACtB;IACA,IAAI,CAACD,WAAW,GAAG,IAAID,GAAG,CAAqB,CAAC;EAClD;EAMAJ,QAAQA,CAACL,KAAsB,EAAQ;IACrC,IAAI,CAACA,KAAK,GAAG;MAAC,GAAG,IAAI,CAACA,KAAK;MAAE,GAAGA;IAAK,CAAC;IAEtC,KAAK,MAAMY,UAAU,IAAI,IAAI,CAACF,WAAW,CAACG,MAAM,CAAC,CAAC,EAAE;MAClDD,UAAU,CAACP,QAAQ,CAAC,IAAI,CAACS,mBAAmB,CAAC,CAAC,CAAC;IACjD;EACF;EAWAC,aAAaA,CAACC,OAAsD,EAAc;IAChF,MAAM;MAACC,IAAI;MAAEC,MAAM;MAAEC;IAAG,CAAC,GAAGH,OAAO;IACnC,IAAIJ,UAAU,GAAG,IAAI,CAACF,WAAW,CAACU,GAAG,CAACH,IAAI,CAAC;IAC3C,IAAI,CAACL,UAAU,EAAE;MACfA,UAAU,GAAG,IAAIS,mBAAU,CAAC;QAC1BJ,IAAI;QACJC,MAAM;QACNC;MACF,CAAC,CAAC;MACFP,UAAU,CAACP,QAAQ,CAAC,IAAI,CAACS,mBAAmB,CAAC,CAAC,CAAC;MAC/C,IAAI,CAACJ,WAAW,CAACY,GAAG,CAACL,IAAI,EAAEL,UAAU,CAAC;IACxC;IACA,OAAOA,UAAU;EACnB;EAEAE,mBAAmBA,CAAA,EAAG;IACpB,OAAO;MACLtB,cAAc,EAAE,IAAI,CAACQ,KAAK,CAACR,cAAc;MACzCC,oBAAoB,EAAE,IAAI,CAACO,KAAK,CAACP,oBAAoB;MACrDC,YAAY,EAAE,IAAI,CAACM,KAAK,CAACN,YAAY;MACrCC,OAAO,EAAE,IAAI,CAACK,KAAK,CAACL;IACtB,CAAC;EACH;AACF;AAAC4B,OAAA,CAAAf,OAAA,GAAAZ,UAAA;AAAA,IAAAW,gBAAA,CAAAC,OAAA,EAjFoBZ,UAAU"}