"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getLibraryUrl = getLibraryUrl;
exports.loadLibrary = loadLibrary;
var _globals = require("../env-utils/globals");
var node = _interopRequireWildcard(require("../node/require-utils.node"));
var _assert = require("../env-utils/assert");
var _version = require("../env-utils/version");
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
const LATEST = 'latest';
const VERSION = typeof "3.4.15" !== 'undefined' ? "3.4.15" : LATEST;
const loadLibraryPromises = {};
async function loadLibrary(libraryUrl) {
  let moduleName = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
  if (moduleName) {
    libraryUrl = getLibraryUrl(libraryUrl, moduleName, options);
  }
  loadLibraryPromises[libraryUrl] = loadLibraryPromises[libraryUrl] || loadLibraryFromFile(libraryUrl);
  return await loadLibraryPromises[libraryUrl];
}
function getLibraryUrl(library, moduleName, options) {
  if (library.startsWith('http')) {
    return library;
  }
  const modules = options.modules || {};
  if (modules[library]) {
    return modules[library];
  }
  if (!_globals.isBrowser) {
    return "modules/".concat(moduleName, "/dist/libs/").concat(library);
  }
  if (options.CDN) {
    (0, _assert.assert)(options.CDN.startsWith('http'));
    return "".concat(options.CDN, "/").concat(moduleName, "@").concat(VERSION, "/dist/libs/").concat(library);
  }
  if (_globals.isWorker) {
    return "../src/libs/".concat(library);
  }
  return "modules/".concat(moduleName, "/src/libs/").concat(library);
}
async function loadLibraryFromFile(libraryUrl) {
  if (libraryUrl.endsWith('wasm')) {
    const response = await fetch(libraryUrl);
    return await response.arrayBuffer();
  }
  if (!_globals.isBrowser) {
    try {
      return node && node.requireFromFile && (await node.requireFromFile(libraryUrl));
    } catch {
      return null;
    }
  }
  if (_globals.isWorker) {
    return importScripts(libraryUrl);
  }
  const response = await fetch(libraryUrl);
  const scriptSource = await response.text();
  return loadLibraryFromString(scriptSource, libraryUrl);
}
function loadLibraryFromString(scriptSource, id) {
  if (!_globals.isBrowser) {
    return node.requireFromString && node.requireFromString(scriptSource, id);
  }
  if (_globals.isWorker) {
    eval.call(_globals.global, scriptSource);
    return null;
  }
  const script = document.createElement('script');
  script.id = id;
  try {
    script.appendChild(document.createTextNode(scriptSource));
  } catch (e) {
    script.text = scriptSource;
  }
  document.body.appendChild(script);
  return null;
}
//# sourceMappingURL=library-utils.js.map