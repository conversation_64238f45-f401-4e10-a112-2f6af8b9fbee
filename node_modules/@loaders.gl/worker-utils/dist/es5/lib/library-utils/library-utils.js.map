{"version": 3, "file": "library-utils.js", "names": ["_globals", "require", "node", "_interopRequireWildcard", "_assert", "_version", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "LATEST", "VERSION", "loadLibraryPromises", "loadLibrary", "libraryUrl", "moduleName", "arguments", "length", "undefined", "options", "getLibraryUrl", "loadLibraryFromFile", "library", "startsWith", "modules", "<PERSON><PERSON><PERSON><PERSON>", "concat", "CDN", "assert", "isWorker", "endsWith", "response", "fetch", "arrayBuffer", "requireFromFile", "importScripts", "scriptSource", "text", "loadLibraryFromString", "id", "requireFromString", "eval", "global", "script", "document", "createElement", "append<PERSON><PERSON><PERSON>", "createTextNode", "e", "body"], "sources": ["../../../../src/lib/library-utils/library-utils.ts"], "sourcesContent": ["/* global importScripts */\nimport {global, isBrowser, isWorker} from '../env-utils/globals';\nimport * as node from '../node/require-utils.node';\nimport {assert} from '../env-utils/assert';\nimport {VERSION as __VERSION__} from '../env-utils/version';\n\n/**\n * TODO - unpkg.com doesn't seem to have a `latest` specifier for alpha releases...\n * 'beta' on beta branch, 'latest' on prod branch\n */\nconst LATEST = 'latest';\nconst VERSION = typeof __VERSION__ !== 'undefined' ? __VERSION__ : LATEST;\n\nconst loadLibraryPromises: Record<string, Promise<any>> = {}; // promises\n\n/**\n * Dynamically loads a library (\"module\")\n *\n * - wasm library: Array buffer is returned\n * - js library: Parse JS is returned\n *\n * Method depends on environment\n * - browser - script element is created and installed on document\n * - worker - eval is called on global context\n * - node - file is required\n *\n * @param libraryUrl\n * @param moduleName\n * @param options\n */\nexport async function loadLibrary(\n  libraryUrl: string,\n  moduleName: string | null = null,\n  options: object = {}\n): Promise<any> {\n  if (moduleName) {\n    libraryUrl = getLibraryUrl(libraryUrl, moduleName, options);\n  }\n\n  // Ensure libraries are only loaded once\n\n  loadLibraryPromises[libraryUrl] =\n    // eslint-disable-next-line @typescript-eslint/no-misused-promises\n    loadLibraryPromises[libraryUrl] || loadLibraryFromFile(libraryUrl);\n  return await loadLibraryPromises[libraryUrl];\n}\n\n// TODO - sort out how to resolve paths for main/worker and dev/prod\nexport function getLibraryUrl(library: string, moduleName?: string, options?: any): string {\n  // Check if already a URL\n  if (library.startsWith('http')) {\n    return library;\n  }\n\n  // Allow application to import and supply libraries through `options.modules`\n  const modules = options.modules || {};\n  if (modules[library]) {\n    return modules[library];\n  }\n\n  // Load from local files, not from CDN scripts in Node.js\n  // TODO - needs to locate the modules directory when installed!\n  if (!isBrowser) {\n    return `modules/${moduleName}/dist/libs/${library}`;\n  }\n\n  // In browser, load from external scripts\n  if (options.CDN) {\n    assert(options.CDN.startsWith('http'));\n    return `${options.CDN}/${moduleName}@${VERSION}/dist/libs/${library}`;\n  }\n\n  // TODO - loading inside workers requires paths relative to worker script location...\n  if (isWorker) {\n    return `../src/libs/${library}`;\n  }\n\n  return `modules/${moduleName}/src/libs/${library}`;\n}\n\nasync function loadLibraryFromFile(libraryUrl: string): Promise<any> {\n  if (libraryUrl.endsWith('wasm')) {\n    const response = await fetch(libraryUrl);\n    return await response.arrayBuffer();\n  }\n\n  if (!isBrowser) {\n    try {\n      return node && node.requireFromFile && (await node.requireFromFile(libraryUrl));\n    } catch {\n      return null;\n    }\n  }\n  if (isWorker) {\n    return importScripts(libraryUrl);\n  }\n  // TODO - fix - should be more secure than string parsing since observes CORS\n  // if (isBrowser) {\n  //   return await loadScriptFromFile(libraryUrl);\n  // }\n\n  const response = await fetch(libraryUrl);\n  const scriptSource = await response.text();\n  return loadLibraryFromString(scriptSource, libraryUrl);\n}\n\n/*\nasync function loadScriptFromFile(libraryUrl) {\n  const script = document.createElement('script');\n  script.src = libraryUrl;\n  return await new Promise((resolve, reject) => {\n    script.onload = data => {\n      resolve(data);\n    };\n    script.onerror = reject;\n  });\n}\n*/\n\n// TODO - Needs security audit...\n//  - Raw eval call\n//  - Potentially bypasses CORS\n// Upside is that this separates fetching and parsing\n// we could create a`LibraryLoader` or`ModuleLoader`\nfunction loadLibraryFromString(scriptSource: string, id: string): null | any {\n  if (!isBrowser) {\n    return node.requireFromString && node.requireFromString(scriptSource, id);\n  }\n\n  if (isWorker) {\n    // Use lvalue trick to make eval run in global scope\n    eval.call(global, scriptSource); // eslint-disable-line no-eval\n    // https://stackoverflow.com/questions/9107240/1-evalthis-vs-evalthis-in-javascript\n    // http://perfectionkills.com/global-eval-what-are-the-options/\n    return null;\n  }\n\n  const script = document.createElement('script');\n  script.id = id;\n  // most browsers like a separate text node but some throw an error. The second method covers those.\n  try {\n    script.appendChild(document.createTextNode(scriptSource));\n  } catch (e) {\n    script.text = scriptSource;\n  }\n  document.body.appendChild(script);\n  return null;\n}\n\n// TODO - technique for module injection into worker, from THREE.DracoLoader...\n/*\nfunction combineWorkerWithLibrary(worker, jsContent) {\n  var fn = wWorker.toString();\n  var body = [\n    '// injected',\n    jsContent,\n    '',\n    '// worker',\n    fn.substring(fn.indexOf('{') + 1, fn.lastIndexOf('}'))\n  ].join('\\n');\n  this.workerSourceURL = URL.createObjectURL(new Blob([body]));\n}\n*/\n"], "mappings": ";;;;;;;AACA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,IAAA,GAAAC,uBAAA,CAAAF,OAAA;AACA,IAAAG,OAAA,GAAAH,OAAA;AACA,IAAAI,QAAA,GAAAJ,OAAA;AAA4D,SAAAK,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAJ,wBAAAQ,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAM5D,MAAMW,MAAM,GAAG,QAAQ;AACvB,MAAMC,OAAO,GAAG,eAAkB,KAAK,WAAW,cAAiBD,MAAM;AAEzE,MAAME,mBAAiD,GAAG,CAAC,CAAC;AAiBrD,eAAeC,WAAWA,CAC/BC,UAAkB,EAGJ;EAAA,IAFdC,UAAyB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAAA,IAChCG,OAAe,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAEpB,IAAID,UAAU,EAAE;IACdD,UAAU,GAAGM,aAAa,CAACN,UAAU,EAAEC,UAAU,EAAEI,OAAO,CAAC;EAC7D;EAIAP,mBAAmB,CAACE,UAAU,CAAC,GAE7BF,mBAAmB,CAACE,UAAU,CAAC,IAAIO,mBAAmB,CAACP,UAAU,CAAC;EACpE,OAAO,MAAMF,mBAAmB,CAACE,UAAU,CAAC;AAC9C;AAGO,SAASM,aAAaA,CAACE,OAAe,EAAEP,UAAmB,EAAEI,OAAa,EAAU;EAEzF,IAAIG,OAAO,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;IAC9B,OAAOD,OAAO;EAChB;EAGA,MAAME,OAAO,GAAGL,OAAO,CAACK,OAAO,IAAI,CAAC,CAAC;EACrC,IAAIA,OAAO,CAACF,OAAO,CAAC,EAAE;IACpB,OAAOE,OAAO,CAACF,OAAO,CAAC;EACzB;EAIA,IAAI,CAACG,kBAAS,EAAE;IACd,kBAAAC,MAAA,CAAkBX,UAAU,iBAAAW,MAAA,CAAcJ,OAAO;EACnD;EAGA,IAAIH,OAAO,CAACQ,GAAG,EAAE;IACf,IAAAC,cAAM,EAACT,OAAO,CAACQ,GAAG,CAACJ,UAAU,CAAC,MAAM,CAAC,CAAC;IACtC,UAAAG,MAAA,CAAUP,OAAO,CAACQ,GAAG,OAAAD,MAAA,CAAIX,UAAU,OAAAW,MAAA,CAAIf,OAAO,iBAAAe,MAAA,CAAcJ,OAAO;EACrE;EAGA,IAAIO,iBAAQ,EAAE;IACZ,sBAAAH,MAAA,CAAsBJ,OAAO;EAC/B;EAEA,kBAAAI,MAAA,CAAkBX,UAAU,gBAAAW,MAAA,CAAaJ,OAAO;AAClD;AAEA,eAAeD,mBAAmBA,CAACP,UAAkB,EAAgB;EACnE,IAAIA,UAAU,CAACgB,QAAQ,CAAC,MAAM,CAAC,EAAE;IAC/B,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAClB,UAAU,CAAC;IACxC,OAAO,MAAMiB,QAAQ,CAACE,WAAW,CAAC,CAAC;EACrC;EAEA,IAAI,CAACR,kBAAS,EAAE;IACd,IAAI;MACF,OAAOzC,IAAI,IAAIA,IAAI,CAACkD,eAAe,KAAK,MAAMlD,IAAI,CAACkD,eAAe,CAACpB,UAAU,CAAC,CAAC;IACjF,CAAC,CAAC,MAAM;MACN,OAAO,IAAI;IACb;EACF;EACA,IAAIe,iBAAQ,EAAE;IACZ,OAAOM,aAAa,CAACrB,UAAU,CAAC;EAClC;EAMA,MAAMiB,QAAQ,GAAG,MAAMC,KAAK,CAAClB,UAAU,CAAC;EACxC,MAAMsB,YAAY,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;EAC1C,OAAOC,qBAAqB,CAACF,YAAY,EAAEtB,UAAU,CAAC;AACxD;AAoBA,SAASwB,qBAAqBA,CAACF,YAAoB,EAAEG,EAAU,EAAc;EAC3E,IAAI,CAACd,kBAAS,EAAE;IACd,OAAOzC,IAAI,CAACwD,iBAAiB,IAAIxD,IAAI,CAACwD,iBAAiB,CAACJ,YAAY,EAAEG,EAAE,CAAC;EAC3E;EAEA,IAAIV,iBAAQ,EAAE;IAEZY,IAAI,CAAClC,IAAI,CAACmC,eAAM,EAAEN,YAAY,CAAC;IAG/B,OAAO,IAAI;EACb;EAEA,MAAMO,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/CF,MAAM,CAACJ,EAAE,GAAGA,EAAE;EAEd,IAAI;IACFI,MAAM,CAACG,WAAW,CAACF,QAAQ,CAACG,cAAc,CAACX,YAAY,CAAC,CAAC;EAC3D,CAAC,CAAC,OAAOY,CAAC,EAAE;IACVL,MAAM,CAACN,IAAI,GAAGD,YAAY;EAC5B;EACAQ,QAAQ,CAACK,IAAI,CAACH,WAAW,CAACH,MAAM,CAAC;EACjC,OAAO,IAAI;AACb"}