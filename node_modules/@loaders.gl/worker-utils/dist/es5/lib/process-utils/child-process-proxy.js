"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var ChildProcess = _interopRequireWildcard(require("child_process"));
var _processUtils = require("./process-utils");
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
const DEFAULT_PROPS = {
  command: '',
  arguments: [],
  port: 5000,
  autoPort: true,
  wait: 2000,
  onSuccess: processProxy => {
    console.log("Started ".concat(processProxy.props.command));
  }
};
class ChildProcessProxy {
  constructor() {
    let {
      id = 'browser-driver'
    } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    (0, _defineProperty2.default)(this, "id", void 0);
    (0, _defineProperty2.default)(this, "props", {
      ...DEFAULT_PROPS
    });
    (0, _defineProperty2.default)(this, "childProcess", null);
    (0, _defineProperty2.default)(this, "port", 0);
    (0, _defineProperty2.default)(this, "successTimer", void 0);
    this.id = id;
  }
  async start(props) {
    props = {
      ...DEFAULT_PROPS,
      ...props
    };
    this.props = props;
    const args = [...props.arguments];
    this.port = Number(props.port);
    if (props.portArg) {
      if (props.autoPort) {
        this.port = await (0, _processUtils.getAvailablePort)(props.port);
      }
      args.push(props.portArg, String(this.port));
    }
    return await new Promise((resolve, reject) => {
      try {
        this._setTimeout(() => {
          if (props.onSuccess) {
            props.onSuccess(this);
          }
          resolve({});
        });
        console.log("Spawning ".concat(props.command, " ").concat(props.arguments.join(' ')));
        const childProcess = ChildProcess.spawn(props.command, args, props.spawn);
        this.childProcess = childProcess;
        childProcess.stdout.on('data', data => {
          console.log(data.toString());
        });
        childProcess.stderr.on('data', data => {
          console.log("Child process wrote to stderr: \"".concat(data, "\"."));
          if (!props.ignoreStderr) {
            this._clearTimeout();
            reject(new Error(data));
          }
        });
        childProcess.on('error', error => {
          console.log("Child process errored with ".concat(error));
          this._clearTimeout();
          reject(error);
        });
        childProcess.on('close', code => {
          console.log("Child process exited with ".concat(code));
          this.childProcess = null;
          this._clearTimeout();
          resolve({});
        });
      } catch (error) {
        reject(error);
      }
    });
  }
  async stop() {
    if (this.childProcess) {
      this.childProcess.kill();
      this.childProcess = null;
    }
  }
  async exit() {
    let statusCode = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;
    try {
      await this.stop();
      process.exit(statusCode);
    } catch (error) {
      console.error(error.message || error);
      process.exit(1);
    }
  }
  _setTimeout(callback) {
    if (Number(this.props.wait) > 0) {
      this.successTimer = setTimeout(callback, this.props.wait);
    }
  }
  _clearTimeout() {
    if (this.successTimer) {
      clearTimeout(this.successTimer);
    }
  }
}
exports.default = ChildProcessProxy;
//# sourceMappingURL=child-process-proxy.js.map