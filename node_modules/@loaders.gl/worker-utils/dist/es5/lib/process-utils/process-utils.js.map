{"version": 3, "file": "process-utils.js", "names": ["_child_process", "_interopRequireDefault", "require", "getAvailablePort", "defaultPort", "arguments", "length", "undefined", "Promise", "resolve", "ChildProcess", "exec", "error", "stdout", "portsInUse", "regex", "split", "for<PERSON>ach", "line", "match", "push", "Number", "port", "includes"], "sources": ["../../../../src/lib/process-utils/process-utils.ts"], "sourcesContent": ["import ChildProcess from 'child_process';\n\n// Get an available port\n// Works on Unix systems\nexport function getAvailablePort(defaultPort: number = 3000): Promise<number> {\n  return new Promise((resolve) => {\n    // Get a list of all ports in use\n    ChildProcess.exec('lsof -i -P -n | grep LISTEN', (error, stdout) => {\n      if (error) {\n        // likely no permission, e.g. CI\n        resolve(defaultPort);\n        return;\n      }\n\n      const portsInUse: number[] = [];\n      const regex = /:(\\d+) \\(LISTEN\\)/;\n      stdout.split('\\n').forEach((line) => {\n        const match = regex.exec(line);\n        if (match) {\n          portsInUse.push(Number(match[1]));\n        }\n      });\n      let port = defaultPort;\n      while (portsInUse.includes(port)) {\n        port++;\n      }\n      resolve(port);\n    });\n  });\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,cAAA,GAAAC,sBAAA,CAAAC,OAAA;AAIO,SAASC,gBAAgBA,CAAA,EAA8C;EAAA,IAA7CC,WAAmB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EACzD,OAAO,IAAIG,OAAO,CAAEC,OAAO,IAAK;IAE9BC,sBAAY,CAACC,IAAI,CAAC,6BAA6B,EAAE,CAACC,KAAK,EAAEC,MAAM,KAAK;MAClE,IAAID,KAAK,EAAE;QAETH,OAAO,CAACL,WAAW,CAAC;QACpB;MACF;MAEA,MAAMU,UAAoB,GAAG,EAAE;MAC/B,MAAMC,KAAK,GAAG,mBAAmB;MACjCF,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC,CAACC,OAAO,CAAEC,IAAI,IAAK;QACnC,MAAMC,KAAK,GAAGJ,KAAK,CAACJ,IAAI,CAACO,IAAI,CAAC;QAC9B,IAAIC,KAAK,EAAE;UACTL,UAAU,CAACM,IAAI,CAACC,MAAM,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC;MACF,CAAC,CAAC;MACF,IAAIG,IAAI,GAAGlB,WAAW;MACtB,OAAOU,UAAU,CAACS,QAAQ,CAACD,IAAI,CAAC,EAAE;QAChCA,IAAI,EAAE;MACR;MACAb,OAAO,CAACa,IAAI,CAAC;IACf,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ"}