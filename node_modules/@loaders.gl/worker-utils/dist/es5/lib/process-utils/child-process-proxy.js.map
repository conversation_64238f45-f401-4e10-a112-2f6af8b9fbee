{"version": 3, "file": "child-process-proxy.js", "names": ["ChildProcess", "_interopRequireWildcard", "require", "_processUtils", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "DEFAULT_PROPS", "command", "arguments", "port", "autoPort", "wait", "onSuccess", "processProxy", "console", "log", "concat", "props", "ChildProcessProxy", "constructor", "id", "length", "undefined", "_defineProperty2", "start", "args", "Number", "portArg", "getAvailablePort", "push", "String", "Promise", "resolve", "reject", "_setTimeout", "join", "childProcess", "spawn", "stdout", "on", "data", "toString", "stderr", "ignoreStderr", "_clearTimeout", "Error", "error", "code", "stop", "kill", "exit", "statusCode", "process", "message", "callback", "successTimer", "setTimeout", "clearTimeout", "exports"], "sources": ["../../../../src/lib/process-utils/child-process-proxy.ts"], "sourcesContent": ["/* eslint-disable no-console */\n// Avoid using named imports for Node builtins to help with \"empty\" resolution\n// for bundlers targeting browser environments. Access imports & types\n// through the `ChildProcess` object (e.g. `ChildProcess.spawn`, `ChildProcess.ChildProcess`).\nimport * as ChildProcess from 'child_process';\nimport {getAvailablePort} from './process-utils';\n\nexport type ChildProcessProxyProps = {\n  command: string;\n  arguments: string[];\n  /** Whether to add a port specified arg */\n  portArg?: string;\n  /** Base port number */\n  port?: number;\n  /** Whether to search for an available port if the base port is occupied */\n  autoPort?: boolean;\n  /** Number of milliseconds to wait until concluding success */\n  /** wait: 0 - infinity */\n  wait?: number;\n  /** Options passed on to Node'.js `spawn` */\n  spawn?: ChildProcess.SpawnOptionsWithoutStdio;\n  /** Should proceed if stderr stream recieved data */\n  ignoreStderr?: boolean;\n  /** Callback when the  */\n  onStart?: (proxy: ChildProcessProxy) => void;\n  onSuccess?: (proxy: ChildProcessProxy) => void;\n};\n\nconst DEFAULT_PROPS: ChildProcessProxyProps = {\n  command: '',\n  arguments: [],\n  port: 5000,\n  autoPort: true,\n  wait: 2000,\n  onSuccess: (processProxy) => {\n    console.log(`Started ${processProxy.props.command}`);\n  }\n};\n\n/**\n * Manager for a Node.js child process\n * Prepares arguments, starts, stops and tracks output\n */\nexport default class ChildProcessProxy {\n  id: string;\n  props: ChildProcessProxyProps = {...DEFAULT_PROPS};\n  private childProcess: ChildProcess.ChildProcess | null = null;\n  private port: number = 0;\n  private successTimer?: any; // NodeJS.Timeout;\n\n  // constructor(props?: {id?: string});\n  constructor({id = 'browser-driver'} = {}) {\n    this.id = id;\n  }\n\n  /** Starts a child process with the provided props */\n  async start(props: ChildProcessProxyProps): Promise<object> {\n    props = {...DEFAULT_PROPS, ...props};\n    this.props = props;\n\n    const args = [...props.arguments];\n\n    // If portArg is set, we can look up an available port\n    this.port = Number(props.port);\n    if (props.portArg) {\n      if (props.autoPort) {\n        this.port = await getAvailablePort(props.port);\n      }\n      args.push(props.portArg, String(this.port));\n    }\n\n    return await new Promise((resolve, reject) => {\n      try {\n        this._setTimeout(() => {\n          if (props.onSuccess) {\n            props.onSuccess(this);\n          }\n          resolve({});\n        });\n\n        console.log(`Spawning ${props.command} ${props.arguments.join(' ')}`);\n        const childProcess = ChildProcess.spawn(props.command, args, props.spawn);\n        this.childProcess = childProcess;\n\n        childProcess.stdout.on('data', (data) => {\n          console.log(data.toString());\n        });\n        childProcess.stderr.on('data', (data) => {\n          console.log(`Child process wrote to stderr: \"${data}\".`);\n          if (!props.ignoreStderr) {\n            this._clearTimeout();\n            reject(new Error(data));\n          }\n        });\n        childProcess.on('error', (error) => {\n          console.log(`Child process errored with ${error}`);\n          this._clearTimeout();\n          reject(error);\n        });\n        childProcess.on('close', (code) => {\n          console.log(`Child process exited with ${code}`);\n          this.childProcess = null;\n          this._clearTimeout();\n          resolve({});\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  /** Stops a running child process */\n  async stop(): Promise<void> {\n    if (this.childProcess) {\n      this.childProcess.kill();\n      this.childProcess = null;\n    }\n  }\n\n  /** Exits this process */\n  async exit(statusCode: number = 0): Promise<void> {\n    try {\n      await this.stop();\n      // eslint-disable-next-line no-process-exit\n      process.exit(statusCode);\n    } catch (error) {\n      console.error((error as Error).message || error);\n      // eslint-disable-next-line no-process-exit\n      process.exit(1);\n    }\n  }\n\n  _setTimeout(callback: (...args: any[]) => void) {\n    if (Number(this.props.wait) > 0) {\n      this.successTimer = setTimeout(callback, this.props.wait);\n    }\n  }\n\n  _clearTimeout() {\n    if (this.successTimer) {\n      clearTimeout(this.successTimer);\n    }\n  }\n}\n"], "mappings": ";;;;;;;;AAIA,IAAAA,YAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAD,OAAA;AAAiD,SAAAE,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAJ,wBAAAQ,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAuBjD,MAAMW,aAAqC,GAAG;EAC5CC,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE,EAAE;EACbC,IAAI,EAAE,IAAI;EACVC,QAAQ,EAAE,IAAI;EACdC,IAAI,EAAE,IAAI;EACVC,SAAS,EAAGC,YAAY,IAAK;IAC3BC,OAAO,CAACC,GAAG,YAAAC,MAAA,CAAYH,YAAY,CAACI,KAAK,CAACV,OAAO,CAAE,CAAC;EACtD;AACF,CAAC;AAMc,MAAMW,iBAAiB,CAAC;EAQrCC,WAAWA,CAAA,EAA+B;IAAA,IAA9B;MAACC,EAAE,GAAG;IAAgB,CAAC,GAAAZ,SAAA,CAAAa,MAAA,QAAAb,SAAA,QAAAc,SAAA,GAAAd,SAAA,MAAG,CAAC,CAAC;IAAA,IAAAe,gBAAA,CAAAhC,OAAA;IAAA,IAAAgC,gBAAA,CAAAhC,OAAA,iBANR;MAAC,GAAGe;IAAa,CAAC;IAAA,IAAAiB,gBAAA,CAAAhC,OAAA,wBACO,IAAI;IAAA,IAAAgC,gBAAA,CAAAhC,OAAA,gBACtC,CAAC;IAAA,IAAAgC,gBAAA,CAAAhC,OAAA;IAKtB,IAAI,CAAC6B,EAAE,GAAGA,EAAE;EACd;EAGA,MAAMI,KAAKA,CAACP,KAA6B,EAAmB;IAC1DA,KAAK,GAAG;MAAC,GAAGX,aAAa;MAAE,GAAGW;IAAK,CAAC;IACpC,IAAI,CAACA,KAAK,GAAGA,KAAK;IAElB,MAAMQ,IAAI,GAAG,CAAC,GAAGR,KAAK,CAACT,SAAS,CAAC;IAGjC,IAAI,CAACC,IAAI,GAAGiB,MAAM,CAACT,KAAK,CAACR,IAAI,CAAC;IAC9B,IAAIQ,KAAK,CAACU,OAAO,EAAE;MACjB,IAAIV,KAAK,CAACP,QAAQ,EAAE;QAClB,IAAI,CAACD,IAAI,GAAG,MAAM,IAAAmB,8BAAgB,EAACX,KAAK,CAACR,IAAI,CAAC;MAChD;MACAgB,IAAI,CAACI,IAAI,CAACZ,KAAK,CAACU,OAAO,EAAEG,MAAM,CAAC,IAAI,CAACrB,IAAI,CAAC,CAAC;IAC7C;IAEA,OAAO,MAAM,IAAIsB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MAC5C,IAAI;QACF,IAAI,CAACC,WAAW,CAAC,MAAM;UACrB,IAAIjB,KAAK,CAACL,SAAS,EAAE;YACnBK,KAAK,CAACL,SAAS,CAAC,IAAI,CAAC;UACvB;UACAoB,OAAO,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC;QAEFlB,OAAO,CAACC,GAAG,aAAAC,MAAA,CAAaC,KAAK,CAACV,OAAO,OAAAS,MAAA,CAAIC,KAAK,CAACT,SAAS,CAAC2B,IAAI,CAAC,GAAG,CAAC,CAAE,CAAC;QACrE,MAAMC,YAAY,GAAGxD,YAAY,CAACyD,KAAK,CAACpB,KAAK,CAACV,OAAO,EAAEkB,IAAI,EAAER,KAAK,CAACoB,KAAK,CAAC;QACzE,IAAI,CAACD,YAAY,GAAGA,YAAY;QAEhCA,YAAY,CAACE,MAAM,CAACC,EAAE,CAAC,MAAM,EAAGC,IAAI,IAAK;UACvC1B,OAAO,CAACC,GAAG,CAACyB,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC;QACFL,YAAY,CAACM,MAAM,CAACH,EAAE,CAAC,MAAM,EAAGC,IAAI,IAAK;UACvC1B,OAAO,CAACC,GAAG,qCAAAC,MAAA,CAAoCwB,IAAI,QAAI,CAAC;UACxD,IAAI,CAACvB,KAAK,CAAC0B,YAAY,EAAE;YACvB,IAAI,CAACC,aAAa,CAAC,CAAC;YACpBX,MAAM,CAAC,IAAIY,KAAK,CAACL,IAAI,CAAC,CAAC;UACzB;QACF,CAAC,CAAC;QACFJ,YAAY,CAACG,EAAE,CAAC,OAAO,EAAGO,KAAK,IAAK;UAClChC,OAAO,CAACC,GAAG,+BAAAC,MAAA,CAA+B8B,KAAK,CAAE,CAAC;UAClD,IAAI,CAACF,aAAa,CAAC,CAAC;UACpBX,MAAM,CAACa,KAAK,CAAC;QACf,CAAC,CAAC;QACFV,YAAY,CAACG,EAAE,CAAC,OAAO,EAAGQ,IAAI,IAAK;UACjCjC,OAAO,CAACC,GAAG,8BAAAC,MAAA,CAA8B+B,IAAI,CAAE,CAAC;UAChD,IAAI,CAACX,YAAY,GAAG,IAAI;UACxB,IAAI,CAACQ,aAAa,CAAC,CAAC;UACpBZ,OAAO,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOc,KAAK,EAAE;QACdb,MAAM,CAACa,KAAK,CAAC;MACf;IACF,CAAC,CAAC;EACJ;EAGA,MAAME,IAAIA,CAAA,EAAkB;IAC1B,IAAI,IAAI,CAACZ,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACa,IAAI,CAAC,CAAC;MACxB,IAAI,CAACb,YAAY,GAAG,IAAI;IAC1B;EACF;EAGA,MAAMc,IAAIA,CAAA,EAAwC;IAAA,IAAvCC,UAAkB,GAAA3C,SAAA,CAAAa,MAAA,QAAAb,SAAA,QAAAc,SAAA,GAAAd,SAAA,MAAG,CAAC;IAC/B,IAAI;MACF,MAAM,IAAI,CAACwC,IAAI,CAAC,CAAC;MAEjBI,OAAO,CAACF,IAAI,CAACC,UAAU,CAAC;IAC1B,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdhC,OAAO,CAACgC,KAAK,CAAEA,KAAK,CAAWO,OAAO,IAAIP,KAAK,CAAC;MAEhDM,OAAO,CAACF,IAAI,CAAC,CAAC,CAAC;IACjB;EACF;EAEAhB,WAAWA,CAACoB,QAAkC,EAAE;IAC9C,IAAI5B,MAAM,CAAC,IAAI,CAACT,KAAK,CAACN,IAAI,CAAC,GAAG,CAAC,EAAE;MAC/B,IAAI,CAAC4C,YAAY,GAAGC,UAAU,CAACF,QAAQ,EAAE,IAAI,CAACrC,KAAK,CAACN,IAAI,CAAC;IAC3D;EACF;EAEAiC,aAAaA,CAAA,EAAG;IACd,IAAI,IAAI,CAACW,YAAY,EAAE;MACrBE,YAAY,CAAC,IAAI,CAACF,YAAY,CAAC;IACjC;EACF;AACF;AAACG,OAAA,CAAAnE,OAAA,GAAA2B,iBAAA"}