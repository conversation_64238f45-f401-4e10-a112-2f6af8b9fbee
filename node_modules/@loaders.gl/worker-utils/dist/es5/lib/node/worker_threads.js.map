{"version": 3, "file": "worker_threads.js", "names": ["WorkerThreads", "_interopRequireWildcard", "require", "Object", "keys", "for<PERSON>ach", "key", "prototype", "hasOwnProperty", "call", "_exportNames", "exports", "defineProperty", "enumerable", "get", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "newObj", "hasPropertyDescriptor", "getOwnPropertyDescriptor", "desc", "set", "NodeWorker", "Worker"], "sources": ["../../../../src/lib/node/worker_threads.ts"], "sourcesContent": ["// loaders.gl, MIT license\n\nimport * as WorkerThreads from 'worker_threads';\nexport * from 'worker_threads';\nexport const NodeWorker = WorkerThreads.Worker;\nexport type NodeWorkerType = WorkerThreads.Worker;\n"], "mappings": ";;;;;;;;;AAEA,IAAAA,aAAA,GAAAC,uBAAA,CAAAC,OAAA;AACAC,MAAA,CAAAC,IAAA,CAAAJ,aAAA,EAAAK,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAN,aAAA,CAAAM,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAd,aAAA,CAAAM,GAAA;IAAA;EAAA;AAAA;AAA+B,SAAAS,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAf,wBAAAmB,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAT,GAAA,CAAAM,GAAA,SAAAK,MAAA,WAAAC,qBAAA,GAAAvB,MAAA,CAAAS,cAAA,IAAAT,MAAA,CAAAwB,wBAAA,WAAArB,GAAA,IAAAc,GAAA,QAAAd,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAW,GAAA,EAAAd,GAAA,SAAAsB,IAAA,GAAAF,qBAAA,GAAAvB,MAAA,CAAAwB,wBAAA,CAAAP,GAAA,EAAAd,GAAA,cAAAsB,IAAA,KAAAA,IAAA,CAAAd,GAAA,IAAAc,IAAA,CAAAC,GAAA,KAAA1B,MAAA,CAAAS,cAAA,CAAAa,MAAA,EAAAnB,GAAA,EAAAsB,IAAA,YAAAH,MAAA,CAAAnB,GAAA,IAAAc,GAAA,CAAAd,GAAA,SAAAmB,MAAA,CAAAH,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAM,GAAA,CAAAT,GAAA,EAAAK,MAAA,YAAAA,MAAA;AACxB,MAAMK,UAAU,GAAG9B,aAAa,CAAC+B,MAAM;AAACpB,OAAA,CAAAmB,UAAA,GAAAA,UAAA"}