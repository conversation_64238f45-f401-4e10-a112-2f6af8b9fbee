{"version": 3, "file": "require-utils.node.js", "names": ["_module", "_interopRequireDefault", "require", "_path", "requireFromFile", "filename", "startsWith", "response", "fetch", "code", "text", "requireFromString", "concat", "process", "cwd", "_options", "_options2", "arguments", "length", "undefined", "options", "Error", "paths", "<PERSON><PERSON><PERSON>", "_nodeModulePaths", "path", "dirname", "parent", "module", "newModule", "prependPaths", "appendPaths", "_compile", "children", "splice", "indexOf", "exports"], "sources": ["../../../../src/lib/node/require-utils.node.ts"], "sourcesContent": ["// Fork of https://github.com/floatdrop/require-from-string/blob/master/index.js\n// Copyright (c) Vse<PERSON><PERSON><PERSON> <<EMAIL>> (github.com/floatdrop)\n// MIT license\n\n// this file is not visible to webpack (it is excluded in the package.json \"browser\" field).\n\nimport Module from 'module';\nimport path from 'path';\n\n// Node.js Dynamically require from file\n// Relative names are resolved relative to cwd\n// This indirect function is provided because webpack will try to bundle `module.require`.\n// this file is not visible to webpack (it is excluded in the package.json \"browser\" field).\nexport async function requireFromFile(filename: string): Promise<any> {\n  if (filename.startsWith('http')) {\n    const response = await fetch(filename);\n    const code = await response.text();\n    return requireFromString(code);\n  }\n\n  if (!filename.startsWith('/')) {\n    filename = `${process.cwd()}/${filename}`;\n  }\n  return require(filename);\n}\n\n// Dynamically require from string\n// - `code` - Required - Type: string - Module code.\n// - `filename` - Type: string - Default: '' - Optional filename.\n// - `options.appendPaths` Type: Array List of paths, that will be appended to module paths.\n// Useful, when you want to be able require modules from these paths.\n// - `options.prependPaths` Type: Array Same as appendPaths, but paths will be prepended.\nexport function requireFromString(\n  code: string,\n  filename = '',\n  options?: {\n    prependPaths?: string[];\n    appendPaths?: string[];\n  }\n): any {\n  if (typeof filename === 'object') {\n    options = filename;\n    filename = '';\n  }\n\n  if (typeof code !== 'string') {\n    throw new Error(`code must be a string, not ${typeof code}`);\n  }\n\n  // @ts-ignore\n  const paths = Module._nodeModulePaths(path.dirname(filename));\n\n  const parent = module.parent;\n  // @ts-ignore\n  const newModule = new Module(filename, parent);\n  newModule.filename = filename;\n  newModule.paths = ([] as string[])\n    .concat(options?.prependPaths || [])\n    .concat(paths)\n    .concat(options?.appendPaths || []);\n  // @ts-ignore\n  newModule._compile(code, filename);\n\n  if (parent && parent.children) {\n    parent.children.splice(parent.children.indexOf(newModule), 1);\n  }\n\n  return newModule.exports;\n}\n"], "mappings": ";;;;;;;;AAMA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAF,sBAAA,CAAAC,OAAA;AAMO,eAAeE,eAAeA,CAACC,QAAgB,EAAgB;EACpE,IAAIA,QAAQ,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;IAC/B,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACH,QAAQ,CAAC;IACtC,MAAMI,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;IAClC,OAAOC,iBAAiB,CAACF,IAAI,CAAC;EAChC;EAEA,IAAI,CAACJ,QAAQ,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;IAC7BD,QAAQ,MAAAO,MAAA,CAAMC,OAAO,CAACC,GAAG,CAAC,CAAC,OAAAF,MAAA,CAAIP,QAAQ,CAAE;EAC3C;EACA,OAAOH,OAAO,CAACG,QAAQ,CAAC;AAC1B;AAQO,SAASM,iBAAiBA,CAC/BF,IAAY,EAMP;EAAA,IAAAM,QAAA,EAAAC,SAAA;EAAA,IALLX,QAAQ,GAAAY,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAAA,IACbG,OAGC,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAED,IAAI,OAAOd,QAAQ,KAAK,QAAQ,EAAE;IAChCe,OAAO,GAAGf,QAAQ;IAClBA,QAAQ,GAAG,EAAE;EACf;EAEA,IAAI,OAAOI,IAAI,KAAK,QAAQ,EAAE;IAC5B,MAAM,IAAIY,KAAK,+BAAAT,MAAA,CAA+B,OAAOH,IAAI,CAAE,CAAC;EAC9D;EAGA,MAAMa,KAAK,GAAGC,eAAM,CAACC,gBAAgB,CAACC,aAAI,CAACC,OAAO,CAACrB,QAAQ,CAAC,CAAC;EAE7D,MAAMsB,MAAM,GAAGC,MAAM,CAACD,MAAM;EAE5B,MAAME,SAAS,GAAG,IAAIN,eAAM,CAAClB,QAAQ,EAAEsB,MAAM,CAAC;EAC9CE,SAAS,CAACxB,QAAQ,GAAGA,QAAQ;EAC7BwB,SAAS,CAACP,KAAK,GAAI,EAAE,CAClBV,MAAM,CAAC,EAAAG,QAAA,GAAAK,OAAO,cAAAL,QAAA,uBAAPA,QAAA,CAASe,YAAY,KAAI,EAAE,CAAC,CACnClB,MAAM,CAACU,KAAK,CAAC,CACbV,MAAM,CAAC,EAAAI,SAAA,GAAAI,OAAO,cAAAJ,SAAA,uBAAPA,SAAA,CAASe,WAAW,KAAI,EAAE,CAAC;EAErCF,SAAS,CAACG,QAAQ,CAACvB,IAAI,EAAEJ,QAAQ,CAAC;EAElC,IAAIsB,MAAM,IAAIA,MAAM,CAACM,QAAQ,EAAE;IAC7BN,MAAM,CAACM,QAAQ,CAACC,MAAM,CAACP,MAAM,CAACM,QAAQ,CAACE,OAAO,CAACN,SAAS,CAAC,EAAE,CAAC,CAAC;EAC/D;EAEA,OAAOA,SAAS,CAACO,OAAO;AAC1B"}