{"version": 3, "file": "require-utils.browser.js", "names": ["readFileAsArrayBuffer", "exports", "readFileAsText", "requireFromFile", "requireFromString"], "sources": ["../../../../src/lib/node/require-utils.browser.ts"], "sourcesContent": ["export const readFileAsArrayBuffer = null;\nexport const readFileAsText = null;\nexport const requireFromFile = null;\nexport const requireFromString = null;\n"], "mappings": ";;;;;;AAAO,MAAMA,qBAAqB,GAAG,IAAI;AAACC,OAAA,CAAAD,qBAAA,GAAAA,qBAAA;AACnC,MAAME,cAAc,GAAG,IAAI;AAACD,OAAA,CAAAC,cAAA,GAAAA,cAAA;AAC5B,MAAMC,eAAe,GAAG,IAAI;AAACF,OAAA,CAAAE,eAAA,GAAAA,eAAA;AAC7B,MAAMC,iBAAiB,GAAG,IAAI;AAACH,OAAA,CAAAG,iBAAA,GAAAA,iBAAA"}