{"version": 3, "file": "async-queue.js", "names": ["Symbol", "asyncIterator", "AsyncQueue", "constructor", "_defineProperty2", "default", "_values", "_settlers", "_closed", "_Symbol$asyncIterator", "push", "value", "enqueue", "Error", "length", "settler", "shift", "reject", "resolve", "close", "done", "next", "Promise", "undefined", "exports"], "sources": ["../../../../src/lib/async-queue/async-queue.ts"], "sourcesContent": ["// From https://github.com/rauschma/async-iter-demo/tree/master/src under MIT license\n// http://2ality.com/2016/10/asynchronous-iteration.html\n\n/**\n * Async Queue\n * - AsyncIterable: An async iterator can be\n * - Values can be pushed onto the queue\n * @example\n *   const asyncQueue = new AsyncQueue();\n *   setTimeout(() => asyncQueue.enqueue('tick'), 1000);\n *   setTimeout(() => asyncQueue.enqueue(new Error('done')), 10000);\n *   for await (const value of asyncQueue) {\n *     console.log(value); // tick\n *   }\n */\nexport default class AsyncQueue<T> {\n  private _values: any[];\n  private _settlers: any[];\n  private _closed: boolean;\n\n  constructor() {\n    this._values = []; // enqueues > dequeues\n    this._settlers = []; // dequeues > enqueues\n    this._closed = false;\n  }\n\n  /** Return an async iterator for this queue */\n  [Symbol.asyncIterator](): AsyncIterator<T> {\n    return this;\n  }\n\n  /** Push a new value - the async iterator will yield a promise resolved to this value */\n  push(value: T): void {\n    return this.enqueue(value);\n  }\n\n  /**\n   * Push a new value - the async iterator will yield a promise resolved to this value\n   * Add an error - the async iterator will yield a promise rejected with this value\n   */\n  enqueue(value: T | Error): void {\n    if (this._closed) {\n      throw new Error('Closed');\n    }\n\n    if (this._settlers.length > 0) {\n      if (this._values.length > 0) {\n        throw new Error('Illegal internal state');\n      }\n      const settler = this._settlers.shift();\n      if (value instanceof Error) {\n        settler.reject(value);\n      } else {\n        settler.resolve({value});\n      }\n    } else {\n      this._values.push(value);\n    }\n  }\n\n  /** Indicate that we not waiting for more values - The async iterator will be done */\n  close(): void {\n    while (this._settlers.length > 0) {\n      const settler = this._settlers.shift();\n      settler.resolve({done: true});\n    }\n    this._closed = true;\n  }\n\n  // ITERATOR IMPLEMENTATION\n\n  /** @returns a Promise for an IteratorResult */\n  next(): Promise<IteratorResult<T, any>> {\n    // If values in queue, yield the first value\n    if (this._values.length > 0) {\n      const value = this._values.shift();\n      if (value instanceof Error) {\n        return Promise.reject(value);\n      }\n      return Promise.resolve({done: false, value});\n    }\n\n    // If queue is closed, the iterator is done\n    if (this._closed) {\n      if (this._settlers.length > 0) {\n        throw new Error('Illegal internal state');\n      }\n      return Promise.resolve({done: true, value: undefined});\n    }\n\n    // Yield a promise that waits for new values to be enqueued\n    return new Promise((resolve, reject) => {\n      this._settlers.push({resolve, reject});\n    });\n  }\n}\n"], "mappings": ";;;;;;;;;wBA2BGA,MAAM,CAACC,aAAa;AAZR,MAAMC,UAAU,CAAI;EAKjCC,WAAWA,CAAA,EAAG;IAAA,IAAAC,gBAAA,CAAAC,OAAA;IAAA,IAAAD,gBAAA,CAAAC,OAAA;IAAA,IAAAD,gBAAA,CAAAC,OAAA;IACZ,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,KAAK;EACtB;EAGA,CAAAC,qBAAA,IAA2C;IACzC,OAAO,IAAI;EACb;EAGAC,IAAIA,CAACC,KAAQ,EAAQ;IACnB,OAAO,IAAI,CAACC,OAAO,CAACD,KAAK,CAAC;EAC5B;EAMAC,OAAOA,CAACD,KAAgB,EAAQ;IAC9B,IAAI,IAAI,CAACH,OAAO,EAAE;MAChB,MAAM,IAAIK,KAAK,CAAC,QAAQ,CAAC;IAC3B;IAEA,IAAI,IAAI,CAACN,SAAS,CAACO,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,IAAI,CAACR,OAAO,CAACQ,MAAM,GAAG,CAAC,EAAE;QAC3B,MAAM,IAAID,KAAK,CAAC,wBAAwB,CAAC;MAC3C;MACA,MAAME,OAAO,GAAG,IAAI,CAACR,SAAS,CAACS,KAAK,CAAC,CAAC;MACtC,IAAIL,KAAK,YAAYE,KAAK,EAAE;QAC1BE,OAAO,CAACE,MAAM,CAACN,KAAK,CAAC;MACvB,CAAC,MAAM;QACLI,OAAO,CAACG,OAAO,CAAC;UAACP;QAAK,CAAC,CAAC;MAC1B;IACF,CAAC,MAAM;MACL,IAAI,CAACL,OAAO,CAACI,IAAI,CAACC,KAAK,CAAC;IAC1B;EACF;EAGAQ,KAAKA,CAAA,EAAS;IACZ,OAAO,IAAI,CAACZ,SAAS,CAACO,MAAM,GAAG,CAAC,EAAE;MAChC,MAAMC,OAAO,GAAG,IAAI,CAACR,SAAS,CAACS,KAAK,CAAC,CAAC;MACtCD,OAAO,CAACG,OAAO,CAAC;QAACE,IAAI,EAAE;MAAI,CAAC,CAAC;IAC/B;IACA,IAAI,CAACZ,OAAO,GAAG,IAAI;EACrB;EAKAa,IAAIA,CAAA,EAAoC;IAEtC,IAAI,IAAI,CAACf,OAAO,CAACQ,MAAM,GAAG,CAAC,EAAE;MAC3B,MAAMH,KAAK,GAAG,IAAI,CAACL,OAAO,CAACU,KAAK,CAAC,CAAC;MAClC,IAAIL,KAAK,YAAYE,KAAK,EAAE;QAC1B,OAAOS,OAAO,CAACL,MAAM,CAACN,KAAK,CAAC;MAC9B;MACA,OAAOW,OAAO,CAACJ,OAAO,CAAC;QAACE,IAAI,EAAE,KAAK;QAAET;MAAK,CAAC,CAAC;IAC9C;IAGA,IAAI,IAAI,CAACH,OAAO,EAAE;MAChB,IAAI,IAAI,CAACD,SAAS,CAACO,MAAM,GAAG,CAAC,EAAE;QAC7B,MAAM,IAAID,KAAK,CAAC,wBAAwB,CAAC;MAC3C;MACA,OAAOS,OAAO,CAACJ,OAAO,CAAC;QAACE,IAAI,EAAE,IAAI;QAAET,KAAK,EAAEY;MAAS,CAAC,CAAC;IACxD;IAGA,OAAO,IAAID,OAAO,CAAC,CAACJ,OAAO,EAAED,MAAM,KAAK;MACtC,IAAI,CAACV,SAAS,CAACG,IAAI,CAAC;QAACQ,OAAO;QAAED;MAAM,CAAC,CAAC;IACxC,CAAC,CAAC;EACJ;AACF;AAACO,OAAA,CAAAnB,OAAA,GAAAH,UAAA"}