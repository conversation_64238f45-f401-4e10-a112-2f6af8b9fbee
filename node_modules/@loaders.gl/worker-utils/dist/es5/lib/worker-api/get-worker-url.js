"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getWorkerName = getWorkerName;
exports.getWorkerURL = getWorkerURL;
var _assert = require("../env-utils/assert");
var _version = require("../env-utils/version");
const NPM_TAG = 'latest';
const VERSION = typeof "3.4.15" !== 'undefined' ? "3.4.15" : NPM_TAG;
function getWorkerName(worker) {
  const warning = worker.version !== VERSION ? " (worker-utils@".concat(VERSION, ")") : '';
  return "".concat(worker.name, "@").concat(worker.version).concat(warning);
}
function getWorkerURL(worker) {
  let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  const workerOptions = options[worker.id] || {};
  const workerFile = "".concat(worker.id, "-worker.js");
  let url = workerOptions.workerUrl;
  if (!url && worker.id === 'compression') {
    url = options.workerUrl;
  }
  if (options._workerType === 'test') {
    url = "modules/".concat(worker.module, "/dist/").concat(workerFile);
  }
  if (!url) {
    let version = worker.version;
    if (version === 'latest') {
      version = NPM_TAG;
    }
    const versionTag = version ? "@".concat(version) : '';
    url = "https://unpkg.com/@loaders.gl/".concat(worker.module).concat(versionTag, "/dist/").concat(workerFile);
  }
  (0, _assert.assert)(url);
  return url;
}
//# sourceMappingURL=get-worker-url.js.map