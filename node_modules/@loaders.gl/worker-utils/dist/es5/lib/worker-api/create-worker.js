"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.createWorker = createWorker;
var _asyncQueue = _interopRequireDefault(require("../async-queue/async-queue"));
var _workerBody = _interopRequireDefault(require("../worker-farm/worker-body"));
let requestId = 0;
let inputBatches;
let options;
function createWorker(process, processInBatches) {
  if (!_workerBody.default.inWorkerThread()) {
    return;
  }
  const context = {
    process: processOnMainThread
  };
  _workerBody.default.onmessage = async (type, payload) => {
    try {
      switch (type) {
        case 'process':
          if (!process) {
            throw new Error('Worker does not support atomic processing');
          }
          const result = await process(payload.input, payload.options || {}, context);
          _workerBody.default.postMessage('done', {
            result
          });
          break;
        case 'process-in-batches':
          if (!processInBatches) {
            throw new Error('Worker does not support batched processing');
          }
          inputBatches = new _asyncQueue.default();
          options = payload.options || {};
          const resultIterator = processInBatches(inputBatches, options, context);
          for await (const batch of resultIterator) {
            _workerBody.default.postMessage('output-batch', {
              result: batch
            });
          }
          _workerBody.default.postMessage('done', {});
          break;
        case 'input-batch':
          inputBatches.push(payload.input);
          break;
        case 'input-done':
          inputBatches.close();
          break;
        default:
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '';
      _workerBody.default.postMessage('error', {
        error: message
      });
    }
  };
}
function processOnMainThread(arrayBuffer) {
  let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return new Promise((resolve, reject) => {
    const id = requestId++;
    const onMessage = (type, payload) => {
      if (payload.id !== id) {
        return;
      }
      switch (type) {
        case 'done':
          _workerBody.default.removeEventListener(onMessage);
          resolve(payload.result);
          break;
        case 'error':
          _workerBody.default.removeEventListener(onMessage);
          reject(payload.error);
          break;
        default:
      }
    };
    _workerBody.default.addEventListener(onMessage);
    const payload = {
      id,
      input: arrayBuffer,
      options
    };
    _workerBody.default.postMessage('process', payload);
  });
}
//# sourceMappingURL=create-worker.js.map