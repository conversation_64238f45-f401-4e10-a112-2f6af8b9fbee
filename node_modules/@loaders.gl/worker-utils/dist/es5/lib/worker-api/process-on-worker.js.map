{"version": 3, "file": "process-on-worker.js", "names": ["_workerFarm", "_interopRequireDefault", "require", "_getWorkerUrl", "_getTransferList", "canProcessOnWorker", "worker", "options", "WorkerFarm", "isSupported", "processOnWorker", "data", "arguments", "length", "undefined", "context", "name", "getWorkerName", "workerFarm", "getWorkerFarm", "source", "workerPoolProps", "url", "getWorkerURL", "workerPool", "getWorkerPool", "job<PERSON>ame", "job", "startJob", "onMessage", "bind", "transferableOptions", "getTransferListForWriter", "postMessage", "input", "result", "type", "payload", "done", "error", "Error", "id", "process", "message", "console", "warn", "concat"], "sources": ["../../../../src/lib/worker-api/process-on-worker.ts"], "sourcesContent": ["import type {\n  WorkerObject,\n  WorkerOptions,\n  WorkerContext,\n  WorkerMessageType,\n  WorkerMessagePayload\n} from '../../types';\nimport type WorkerJob from '../worker-farm/worker-job';\nimport WorkerFarm from '../worker-farm/worker-farm';\nimport {getWorkerURL, getWorkerName} from './get-worker-url';\nimport {getTransferListForWriter} from '../worker-utils/get-transfer-list';\n\ntype ProcessOnWorkerOptions = WorkerOptions & {\n  jobName?: string;\n  [key: string]: any;\n};\n\n/**\n * Determines if we can parse with worker\n * @param loader\n * @param data\n * @param options\n */\nexport function canProcessOnWorker(worker: WorkerObject, options?: WorkerOptions) {\n  if (!WorkerFarm.isSupported()) {\n    return false;\n  }\n\n  return worker.worker && options?.worker;\n}\n\n/**\n * This function expects that the worker thread sends certain messages,\n * Creating such a worker can be automated if the worker is wrapper by a call to\n * createWorker in @loaders.gl/worker-utils.\n */\nexport async function processOnWorker(\n  worker: WorkerObject,\n  data: any,\n  options: ProcessOnWorkerOptions = {},\n  context: WorkerContext = {}\n): Promise<any> {\n  const name = getWorkerName(worker);\n\n  const workerFarm = WorkerFarm.getWorkerFarm(options);\n  const {source} = options;\n  const workerPoolProps: {name: string; source?: string; url?: string} = {name, source};\n  if (!source) {\n    workerPoolProps.url = getWorkerURL(worker, options);\n  }\n  const workerPool = workerFarm.getWorkerPool(workerPoolProps);\n\n  const jobName = options.jobName || worker.name;\n  const job = await workerPool.startJob(\n    jobName,\n    // eslint-disable-next-line\n    onMessage.bind(null, context)\n  );\n\n  // Kick off the processing in the worker\n  const transferableOptions = getTransferListForWriter(options);\n  job.postMessage('process', {input: data, options: transferableOptions});\n\n  const result = await job.result;\n  return result.result;\n}\n\n/**\n * Job completes when we receive the result\n * @param job\n * @param message\n */\nasync function onMessage(\n  context: WorkerContext,\n  job: WorkerJob,\n  type: WorkerMessageType,\n  payload: WorkerMessagePayload\n) {\n  switch (type) {\n    case 'done':\n      // Worker is done\n      job.done(payload);\n      break;\n\n    case 'error':\n      // Worker encountered an error\n      job.error(new Error(payload.error));\n      break;\n\n    case 'process':\n      // Worker is asking for us (main thread) to process something\n      const {id, input, options} = payload;\n      try {\n        if (!context.process) {\n          job.postMessage('error', {id, error: 'Worker not set up to process on main thread'});\n          return;\n        }\n        const result = await context.process(input, options);\n        job.postMessage('done', {id, result});\n      } catch (error) {\n        const message = error instanceof Error ? error.message : 'unknown error';\n        job.postMessage('error', {id, error: message});\n      }\n      break;\n\n    default:\n      // eslint-disable-next-line\n      console.warn(`process-on-worker: unknown message ${type}`);\n  }\n}\n"], "mappings": ";;;;;;;;AAQA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAD,OAAA;AACA,IAAAE,gBAAA,GAAAF,OAAA;AAaO,SAASG,kBAAkBA,CAACC,MAAoB,EAAEC,OAAuB,EAAE;EAChF,IAAI,CAACC,mBAAU,CAACC,WAAW,CAAC,CAAC,EAAE;IAC7B,OAAO,KAAK;EACd;EAEA,OAAOH,MAAM,CAACA,MAAM,KAAIC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAED,MAAM;AACzC;AAOO,eAAeI,eAAeA,CACnCJ,MAAoB,EACpBK,IAAS,EAGK;EAAA,IAFdJ,OAA+B,GAAAK,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAAA,IACpCG,OAAsB,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAE3B,MAAMI,IAAI,GAAG,IAAAC,2BAAa,EAACX,MAAM,CAAC;EAElC,MAAMY,UAAU,GAAGV,mBAAU,CAACW,aAAa,CAACZ,OAAO,CAAC;EACpD,MAAM;IAACa;EAAM,CAAC,GAAGb,OAAO;EACxB,MAAMc,eAA8D,GAAG;IAACL,IAAI;IAAEI;EAAM,CAAC;EACrF,IAAI,CAACA,MAAM,EAAE;IACXC,eAAe,CAACC,GAAG,GAAG,IAAAC,0BAAY,EAACjB,MAAM,EAAEC,OAAO,CAAC;EACrD;EACA,MAAMiB,UAAU,GAAGN,UAAU,CAACO,aAAa,CAACJ,eAAe,CAAC;EAE5D,MAAMK,OAAO,GAAGnB,OAAO,CAACmB,OAAO,IAAIpB,MAAM,CAACU,IAAI;EAC9C,MAAMW,GAAG,GAAG,MAAMH,UAAU,CAACI,QAAQ,CACnCF,OAAO,EAEPG,SAAS,CAACC,IAAI,CAAC,IAAI,EAAEf,OAAO,CAC9B,CAAC;EAGD,MAAMgB,mBAAmB,GAAG,IAAAC,yCAAwB,EAACzB,OAAO,CAAC;EAC7DoB,GAAG,CAACM,WAAW,CAAC,SAAS,EAAE;IAACC,KAAK,EAAEvB,IAAI;IAAEJ,OAAO,EAAEwB;EAAmB,CAAC,CAAC;EAEvE,MAAMI,MAAM,GAAG,MAAMR,GAAG,CAACQ,MAAM;EAC/B,OAAOA,MAAM,CAACA,MAAM;AACtB;AAOA,eAAeN,SAASA,CACtBd,OAAsB,EACtBY,GAAc,EACdS,IAAuB,EACvBC,OAA6B,EAC7B;EACA,QAAQD,IAAI;IACV,KAAK,MAAM;MAETT,GAAG,CAACW,IAAI,CAACD,OAAO,CAAC;MACjB;IAEF,KAAK,OAAO;MAEVV,GAAG,CAACY,KAAK,CAAC,IAAIC,KAAK,CAACH,OAAO,CAACE,KAAK,CAAC,CAAC;MACnC;IAEF,KAAK,SAAS;MAEZ,MAAM;QAACE,EAAE;QAAEP,KAAK;QAAE3B;MAAO,CAAC,GAAG8B,OAAO;MACpC,IAAI;QACF,IAAI,CAACtB,OAAO,CAAC2B,OAAO,EAAE;UACpBf,GAAG,CAACM,WAAW,CAAC,OAAO,EAAE;YAACQ,EAAE;YAAEF,KAAK,EAAE;UAA6C,CAAC,CAAC;UACpF;QACF;QACA,MAAMJ,MAAM,GAAG,MAAMpB,OAAO,CAAC2B,OAAO,CAACR,KAAK,EAAE3B,OAAO,CAAC;QACpDoB,GAAG,CAACM,WAAW,CAAC,MAAM,EAAE;UAACQ,EAAE;UAAEN;QAAM,CAAC,CAAC;MACvC,CAAC,CAAC,OAAOI,KAAK,EAAE;QACd,MAAMI,OAAO,GAAGJ,KAAK,YAAYC,KAAK,GAAGD,KAAK,CAACI,OAAO,GAAG,eAAe;QACxEhB,GAAG,CAACM,WAAW,CAAC,OAAO,EAAE;UAACQ,EAAE;UAAEF,KAAK,EAAEI;QAAO,CAAC,CAAC;MAChD;MACA;IAEF;MAEEC,OAAO,CAACC,IAAI,uCAAAC,MAAA,CAAuCV,IAAI,CAAE,CAAC;EAC9D;AACF"}