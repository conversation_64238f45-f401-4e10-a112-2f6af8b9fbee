{"version": 3, "file": "create-worker.js", "names": ["_asyncQueue", "_interopRequireDefault", "require", "_workerBody", "requestId", "inputBatches", "options", "createWorker", "process", "processInBatches", "WorkerBody", "inWorkerThread", "context", "processOnMainThread", "onmessage", "type", "payload", "Error", "result", "input", "postMessage", "AsyncQueue", "resultIterator", "batch", "push", "close", "error", "message", "arrayBuffer", "arguments", "length", "undefined", "Promise", "resolve", "reject", "id", "onMessage", "removeEventListener", "addEventListener"], "sources": ["../../../../src/lib/worker-api/create-worker.ts"], "sourcesContent": ["import type {\n  WorkerMessageType,\n  WorkerMessagePayload,\n  WorkerContext,\n  Process,\n  ProcessInBatches\n} from '../../types';\nimport AsyncQueue from '../async-queue/async-queue';\nimport WorkerBody from '../worker-farm/worker-body';\n// import {validateWorkerVersion} from './validate-worker-version';\n\n/** Counter for jobs */\nlet requestId = 0;\nlet inputBatches: AsyncQueue<any>;\nlet options: {[key: string]: any};\n\nexport type ProcessOnMainThread = (\n  data: any,\n  options?: {[key: string]: any},\n  context?: WorkerContext\n) => any;\n\n/**\n * Set up a WebWorkerGlobalScope to talk with the main thread\n */\nexport function createWorker(process: Process, processInBatches?: ProcessInBatches): void {\n  if (!WorkerBody.inWorkerThread()) {\n    return;\n  }\n\n  const context: WorkerContext = {\n    process: processOnMainThread\n  };\n\n  // eslint-disable-next-line complexity\n  WorkerBody.onmessage = async (type: WorkerMessageType, payload: WorkerMessagePayload) => {\n    try {\n      switch (type) {\n        case 'process':\n          if (!process) {\n            throw new Error('Worker does not support atomic processing');\n          }\n          const result = await process(payload.input, payload.options || {}, context);\n          WorkerBody.postMessage('done', {result});\n          break;\n\n        case 'process-in-batches':\n          if (!processInBatches) {\n            throw new Error('Worker does not support batched processing');\n          }\n          inputBatches = new AsyncQueue<any>();\n          options = payload.options || {};\n          const resultIterator = processInBatches(inputBatches, options, context);\n          for await (const batch of resultIterator) {\n            WorkerBody.postMessage('output-batch', {result: batch});\n          }\n          WorkerBody.postMessage('done', {});\n          break;\n\n        case 'input-batch':\n          inputBatches.push(payload.input);\n          break;\n\n        case 'input-done':\n          inputBatches.close();\n          break;\n\n        default:\n      }\n    } catch (error) {\n      const message = error instanceof Error ? error.message : '';\n      WorkerBody.postMessage('error', {error: message});\n    }\n  };\n}\n\nfunction processOnMainThread(arrayBuffer: ArrayBuffer, options = {}) {\n  return new Promise((resolve, reject) => {\n    const id = requestId++;\n\n    /**\n     */\n    const onMessage = (type: string, payload: WorkerMessagePayload) => {\n      if (payload.id !== id) {\n        // not ours\n        return;\n      }\n\n      switch (type) {\n        case 'done':\n          WorkerBody.removeEventListener(onMessage);\n          resolve(payload.result);\n          break;\n\n        case 'error':\n          WorkerBody.removeEventListener(onMessage);\n          reject(payload.error);\n          break;\n\n        default:\n        // ignore\n      }\n    };\n\n    WorkerBody.addEventListener(onMessage);\n\n    // Ask the main thread to decode data\n    const payload = {id, input: arrayBuffer, options};\n    WorkerBody.postMessage('process', payload);\n  });\n}\n"], "mappings": ";;;;;;;AAOA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,WAAA,GAAAF,sBAAA,CAAAC,OAAA;AAIA,IAAIE,SAAS,GAAG,CAAC;AACjB,IAAIC,YAA6B;AACjC,IAAIC,OAA6B;AAW1B,SAASC,YAAYA,CAACC,OAAgB,EAAEC,gBAAmC,EAAQ;EACxF,IAAI,CAACC,mBAAU,CAACC,cAAc,CAAC,CAAC,EAAE;IAChC;EACF;EAEA,MAAMC,OAAsB,GAAG;IAC7BJ,OAAO,EAAEK;EACX,CAAC;EAGDH,mBAAU,CAACI,SAAS,GAAG,OAAOC,IAAuB,EAAEC,OAA6B,KAAK;IACvF,IAAI;MACF,QAAQD,IAAI;QACV,KAAK,SAAS;UACZ,IAAI,CAACP,OAAO,EAAE;YACZ,MAAM,IAAIS,KAAK,CAAC,2CAA2C,CAAC;UAC9D;UACA,MAAMC,MAAM,GAAG,MAAMV,OAAO,CAACQ,OAAO,CAACG,KAAK,EAAEH,OAAO,CAACV,OAAO,IAAI,CAAC,CAAC,EAAEM,OAAO,CAAC;UAC3EF,mBAAU,CAACU,WAAW,CAAC,MAAM,EAAE;YAACF;UAAM,CAAC,CAAC;UACxC;QAEF,KAAK,oBAAoB;UACvB,IAAI,CAACT,gBAAgB,EAAE;YACrB,MAAM,IAAIQ,KAAK,CAAC,4CAA4C,CAAC;UAC/D;UACAZ,YAAY,GAAG,IAAIgB,mBAAU,CAAM,CAAC;UACpCf,OAAO,GAAGU,OAAO,CAACV,OAAO,IAAI,CAAC,CAAC;UAC/B,MAAMgB,cAAc,GAAGb,gBAAgB,CAACJ,YAAY,EAAEC,OAAO,EAAEM,OAAO,CAAC;UACvE,WAAW,MAAMW,KAAK,IAAID,cAAc,EAAE;YACxCZ,mBAAU,CAACU,WAAW,CAAC,cAAc,EAAE;cAACF,MAAM,EAAEK;YAAK,CAAC,CAAC;UACzD;UACAb,mBAAU,CAACU,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;UAClC;QAEF,KAAK,aAAa;UAChBf,YAAY,CAACmB,IAAI,CAACR,OAAO,CAACG,KAAK,CAAC;UAChC;QAEF,KAAK,YAAY;UACfd,YAAY,CAACoB,KAAK,CAAC,CAAC;UACpB;QAEF;MACF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd,MAAMC,OAAO,GAAGD,KAAK,YAAYT,KAAK,GAAGS,KAAK,CAACC,OAAO,GAAG,EAAE;MAC3DjB,mBAAU,CAACU,WAAW,CAAC,OAAO,EAAE;QAACM,KAAK,EAAEC;MAAO,CAAC,CAAC;IACnD;EACF,CAAC;AACH;AAEA,SAASd,mBAAmBA,CAACe,WAAwB,EAAgB;EAAA,IAAdtB,OAAO,GAAAuB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACjE,OAAO,IAAIG,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,MAAMC,EAAE,GAAG/B,SAAS,EAAE;IAItB,MAAMgC,SAAS,GAAGA,CAACrB,IAAY,EAAEC,OAA6B,KAAK;MACjE,IAAIA,OAAO,CAACmB,EAAE,KAAKA,EAAE,EAAE;QAErB;MACF;MAEA,QAAQpB,IAAI;QACV,KAAK,MAAM;UACTL,mBAAU,CAAC2B,mBAAmB,CAACD,SAAS,CAAC;UACzCH,OAAO,CAACjB,OAAO,CAACE,MAAM,CAAC;UACvB;QAEF,KAAK,OAAO;UACVR,mBAAU,CAAC2B,mBAAmB,CAACD,SAAS,CAAC;UACzCF,MAAM,CAAClB,OAAO,CAACU,KAAK,CAAC;UACrB;QAEF;MAEF;IACF,CAAC;IAEDhB,mBAAU,CAAC4B,gBAAgB,CAACF,SAAS,CAAC;IAGtC,MAAMpB,OAAO,GAAG;MAACmB,EAAE;MAAEhB,KAAK,EAAES,WAAW;MAAEtB;IAAO,CAAC;IACjDI,mBAAU,CAACU,WAAW,CAAC,SAAS,EAAEJ,OAAO,CAAC;EAC5C,CAAC,CAAC;AACJ"}