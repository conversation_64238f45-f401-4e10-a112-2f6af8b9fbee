{"version": 3, "file": "index.js", "names": ["_version", "require", "_assert", "_globals", "_worker<PERSON>ob", "_interopRequireDefault", "_workerThread", "_workerFarm", "_workerPool", "_workerBody", "_processOnWorker", "_createWorker", "_getWorkerUrl", "_validateWorkerVersion", "_getTransferList", "_libraryUtils", "_asyncQueue", "_childProcessProxy", "NullWorker", "id", "name", "module", "version", "VERSION", "options", "null", "exports"], "sources": ["../../src/index.ts"], "sourcesContent": ["import type {WorkerObject} from './types';\nimport {VERSION} from './lib/env-utils/version';\n\n// TYPES\nexport type {\n  WorkerObject,\n  WorkerOptions,\n  // Protocol\n  WorkerMessage,\n  WorkerMessageType,\n  WorkerMessageData,\n  WorkerMessagePayload\n} from './types';\n\n// GENERAL UTILS\nexport {assert} from './lib/env-utils/assert';\nexport {isBrowser, isWorker} from './lib/env-utils/globals';\n\n// WORKER UTILS - TYPES\nexport {default as WorkerJob} from './lib/worker-farm/worker-job';\nexport {default as WorkerThread} from './lib/worker-farm/worker-thread';\n\n// WORKER FARMS\nexport {default as WorkerFarm} from './lib/worker-farm/worker-farm';\nexport {default as WorkerPool} from './lib/worker-farm/worker-pool';\nexport {default as WorkerBody} from './lib/worker-farm/worker-body';\n\nexport {processOnWorker, canProcessOnWorker} from './lib/worker-api/process-on-worker';\nexport {createWorker} from './lib/worker-api/create-worker';\n\n// WORKER UTILS - EXPORTS\nexport {getWorkerURL} from './lib/worker-api/get-worker-url';\nexport {validateWorkerVersion} from './lib/worker-api/validate-worker-version';\nexport {getTransferList, getTransferListForWriter} from './lib/worker-utils/get-transfer-list';\n\n// LIBRARY UTILS\nexport {getLibraryUrl, loadLibrary} from './lib/library-utils/library-utils';\n\n// PARSER UTILS\nexport {default as AsyncQueue} from './lib/async-queue/async-queue';\n\n// PROCESS UTILS\nexport {default as ChildProcessProxy} from './lib/process-utils/child-process-proxy';\n\n// WORKER OBJECTS\n\n/** A null worker to test that worker processing is functional */\nexport const NullWorker: WorkerObject = {\n  id: 'null',\n  name: 'null',\n  module: 'worker-utils',\n  version: VERSION,\n  options: {\n    null: {}\n  }\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,QAAA,GAAAC,OAAA;AAcA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AAGA,IAAAG,UAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,aAAA,GAAAD,sBAAA,CAAAJ,OAAA;AAGA,IAAAM,WAAA,GAAAF,sBAAA,CAAAJ,OAAA;AACA,IAAAO,WAAA,GAAAH,sBAAA,CAAAJ,OAAA;AACA,IAAAQ,WAAA,GAAAJ,sBAAA,CAAAJ,OAAA;AAEA,IAAAS,gBAAA,GAAAT,OAAA;AACA,IAAAU,aAAA,GAAAV,OAAA;AAGA,IAAAW,aAAA,GAAAX,OAAA;AACA,IAAAY,sBAAA,GAAAZ,OAAA;AACA,IAAAa,gBAAA,GAAAb,OAAA;AAGA,IAAAc,aAAA,GAAAd,OAAA;AAGA,IAAAe,WAAA,GAAAX,sBAAA,CAAAJ,OAAA;AAGA,IAAAgB,kBAAA,GAAAZ,sBAAA,CAAAJ,OAAA;AAKO,MAAMiB,UAAwB,GAAG;EACtCC,EAAE,EAAE,MAAM;EACVC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,cAAc;EACtBC,OAAO,EAAEC,gBAAO;EAChBC,OAAO,EAAE;IACPC,IAAI,EAAE,CAAC;EACT;AACF,CAAC;AAACC,OAAA,CAAAR,UAAA,GAAAA,UAAA"}