{"version": 3, "file": "null-worker.js", "names": ["_createWorker", "require", "createWorker", "data"], "sources": ["../../../src/workers/null-worker.ts"], "sourcesContent": ["import {createWorker} from '../lib/worker-api/create-worker';\n\ncreateWorker(async (data) => {\n  // @ts-ignore\n  return data;\n});\n"], "mappings": ";;AAAA,IAAAA,aAAA,GAAAC,OAAA;AAEA,IAAAC,0BAAY,EAAC,MAAOC,IAAI,IAAK;EAE3B,OAAOA,IAAI;AACb,CAAC,CAAC"}