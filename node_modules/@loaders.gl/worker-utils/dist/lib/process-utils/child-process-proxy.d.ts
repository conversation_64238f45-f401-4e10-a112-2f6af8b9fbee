/// <reference types="node" />
import * as ChildProcess from 'child_process';
export type ChildProcessProxyProps = {
    command: string;
    arguments: string[];
    /** Whether to add a port specified arg */
    portArg?: string;
    /** Base port number */
    port?: number;
    /** Whether to search for an available port if the base port is occupied */
    autoPort?: boolean;
    /** Number of milliseconds to wait until concluding success */
    /** wait: 0 - infinity */
    wait?: number;
    /** Options passed on to Node'.js `spawn` */
    spawn?: ChildProcess.SpawnOptionsWithoutStdio;
    /** Should proceed if stderr stream recieved data */
    ignoreStderr?: boolean;
    /** Callback when the  */
    onStart?: (proxy: ChildProcessProxy) => void;
    onSuccess?: (proxy: ChildProcessProxy) => void;
};
/**
 * Manager for a Node.js child process
 * Prepares arguments, starts, stops and tracks output
 */
export default class ChildProcessProxy {
    id: string;
    props: ChildProcessProxyProps;
    private childProcess;
    private port;
    private successTimer?;
    constructor({ id }?: {
        id?: string | undefined;
    });
    /** Starts a child process with the provided props */
    start(props: ChildProcessProxyProps): Promise<object>;
    /** Stops a running child process */
    stop(): Promise<void>;
    /** Exits this process */
    exit(statusCode?: number): Promise<void>;
    _setTimeout(callback: (...args: any[]) => void): void;
    _clearTimeout(): void;
}
//# sourceMappingURL=child-process-proxy.d.ts.map