{"version": 3, "file": "types.js", "names": [], "sources": ["../../src/types.ts"], "sourcesContent": ["/**\n * Worker Options\n */\nexport type WorkerOptions = {\n  // Worker farm options\n  CDN?: string | null;\n  worker?: boolean;\n  maxConcurrency?: number;\n  maxMobileConcurrency?: number;\n  reuseWorkers?: boolean;\n  _workerType?: string;\n  workerUrl?: string;\n  [key: string]: any; // TODO\n};\n\nexport type WorkerContext = {\n  process?: Process;\n  processInBatches?: ProcessInBatches;\n};\n\nexport type Process = (data: any, options?: {[key: string]: any}, context?: WorkerContext) => any;\n\nexport type ProcessInBatches = (\n  iterator: AsyncIterable<any> | Iterable<any>,\n  options?: {[key: string]: any},\n  context?: WorkerContext\n) => AsyncIterable<any>;\n\n/**\n * A worker description object\n */\nexport type WorkerObject = {\n  id: string;\n  name: string;\n  module: string;\n  version: string;\n  worker?: string | boolean;\n  options: {[key: string]: any};\n  deprecatedOptions?: object;\n\n  process?: Process;\n  processInBatches?: ProcessInBatches;\n};\n\n/*\n  PROTOCOL\n\n  Main thread                                     worker\n               => process-batches-start\n\n               => process-batches-input-batch\n               <= process-batches-output-batch\n                  ... // repeat\n\n              => process-batches-input-done\n              <= process-batches-result\n\n                 // or\n\n              <= process-batches-error\n */\nexport type WorkerMessageType =\n  | 'process'\n  | 'done'\n  | 'error'\n  | 'process-in-batches'\n  | 'input-batch'\n  | 'input-done'\n  | 'output-batch';\n\nexport type WorkerMessagePayload = {\n  id?: number;\n  options?: {[key: string]: any};\n  context?: {[key: string]: any};\n  input?: any; // Transferable;\n  result?: any; // Transferable\n  error?: string;\n};\n\nexport type WorkerMessageData = {\n  source: 'loaders.gl';\n  type: WorkerMessageType;\n  payload: WorkerMessagePayload;\n};\n\nexport type WorkerMessage = {\n  type: string;\n  data: WorkerMessageData;\n};\n"], "mappings": ""}