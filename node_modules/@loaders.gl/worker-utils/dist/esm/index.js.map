{"version": 3, "file": "index.js", "names": ["VERSION", "assert", "<PERSON><PERSON><PERSON><PERSON>", "isWorker", "default", "<PERSON><PERSON><PERSON>", "WorkerThread", "WorkerFarm", "WorkerPool", "WorkerBody", "processOnWorker", "canProcessOnWorker", "createWorker", "getWorkerURL", "validateWorkerVersion", "getTransferList", "getTransferListForWriter", "getLibraryUrl", "loadLibrary", "AsyncQueue", "ChildProcessProxy", "NullWorker", "id", "name", "module", "version", "options", "null"], "sources": ["../../src/index.ts"], "sourcesContent": ["import type {WorkerObject} from './types';\nimport {VERSION} from './lib/env-utils/version';\n\n// TYPES\nexport type {\n  WorkerObject,\n  WorkerOptions,\n  // Protocol\n  WorkerMessage,\n  WorkerMessageType,\n  WorkerMessageData,\n  WorkerMessagePayload\n} from './types';\n\n// GENERAL UTILS\nexport {assert} from './lib/env-utils/assert';\nexport {isBrowser, isWorker} from './lib/env-utils/globals';\n\n// WORKER UTILS - TYPES\nexport {default as WorkerJob} from './lib/worker-farm/worker-job';\nexport {default as WorkerThread} from './lib/worker-farm/worker-thread';\n\n// WORKER FARMS\nexport {default as WorkerFarm} from './lib/worker-farm/worker-farm';\nexport {default as WorkerPool} from './lib/worker-farm/worker-pool';\nexport {default as WorkerBody} from './lib/worker-farm/worker-body';\n\nexport {processOnWorker, canProcessOnWorker} from './lib/worker-api/process-on-worker';\nexport {createWorker} from './lib/worker-api/create-worker';\n\n// WORKER UTILS - EXPORTS\nexport {getWorkerURL} from './lib/worker-api/get-worker-url';\nexport {validateWorkerVersion} from './lib/worker-api/validate-worker-version';\nexport {getTransferList, getTransferListForWriter} from './lib/worker-utils/get-transfer-list';\n\n// LIBRARY UTILS\nexport {getLibraryUrl, loadLibrary} from './lib/library-utils/library-utils';\n\n// PARSER UTILS\nexport {default as AsyncQueue} from './lib/async-queue/async-queue';\n\n// PROCESS UTILS\nexport {default as ChildProcessProxy} from './lib/process-utils/child-process-proxy';\n\n// WORKER OBJECTS\n\n/** A null worker to test that worker processing is functional */\nexport const NullWorker: WorkerObject = {\n  id: 'null',\n  name: 'null',\n  module: 'worker-utils',\n  version: VERSION,\n  options: {\n    null: {}\n  }\n};\n"], "mappings": "AACA,SAAQA,OAAO,QAAO,yBAAyB;AAc/C,SAAQC,MAAM,QAAO,wBAAwB;AAC7C,SAAQC,SAAS,EAAEC,QAAQ,QAAO,yBAAyB;AAG3D,SAAQC,OAAO,IAAIC,SAAS,QAAO,8BAA8B;AACjE,SAAQD,OAAO,IAAIE,YAAY,QAAO,iCAAiC;AAGvE,SAAQF,OAAO,IAAIG,UAAU,QAAO,+BAA+B;AACnE,SAAQH,OAAO,IAAII,UAAU,QAAO,+BAA+B;AACnE,SAAQJ,OAAO,IAAIK,UAAU,QAAO,+BAA+B;AAEnE,SAAQC,eAAe,EAAEC,kBAAkB,QAAO,oCAAoC;AACtF,SAAQC,YAAY,QAAO,gCAAgC;AAG3D,SAAQC,YAAY,QAAO,iCAAiC;AAC5D,SAAQC,qBAAqB,QAAO,0CAA0C;AAC9E,SAAQC,eAAe,EAAEC,wBAAwB,QAAO,sCAAsC;AAG9F,SAAQC,aAAa,EAAEC,WAAW,QAAO,mCAAmC;AAG5E,SAAQd,OAAO,IAAIe,UAAU,QAAO,+BAA+B;AAGnE,SAAQf,OAAO,IAAIgB,iBAAiB,QAAO,yCAAyC;AAKpF,OAAO,MAAMC,UAAwB,GAAG;EACtCC,EAAE,EAAE,MAAM;EACVC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,cAAc;EACtBC,OAAO,EAAEzB,OAAO;EAChB0B,OAAO,EAAE;IACPC,IAAI,EAAE,CAAC;EACT;AACF,CAAC"}