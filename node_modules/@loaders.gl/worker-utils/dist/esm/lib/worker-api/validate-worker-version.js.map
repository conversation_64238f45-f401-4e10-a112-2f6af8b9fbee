{"version": 3, "file": "validate-worker-version.js", "names": ["assert", "VERSION", "validateWorkerVersion", "worker", "coreVersion", "arguments", "length", "undefined", "workerVersion", "version", "parseVersion", "parts", "split", "map", "Number", "major", "minor"], "sources": ["../../../../src/lib/worker-api/validate-worker-version.ts"], "sourcesContent": ["import type {WorkerObject} from '../../types';\nimport {assert} from '../env-utils/assert';\nimport {VERSION} from '../env-utils/version';\n\n/**\n * Check if worker is compatible with this library version\n * @param worker\n * @param libVersion\n * @returns `true` if the two versions are compatible\n */\nexport function validateWorkerVersion(\n  worker: WorkerObject,\n  coreVersion: string = VERSION\n): boolean {\n  assert(worker, 'no worker provided');\n\n  const workerVersion = worker.version;\n  if (!coreVersion || !workerVersion) {\n    return false;\n  }\n\n  // TODO enable when fix the __version__ injection\n  // const coreVersions = parseVersion(coreVersion);\n  // const workerVersions = parseVersion(workerVersion);\n  // assert(\n  //   coreVersion.major === workerVersion.major && coreVersion.minor <= workerVersion.minor,\n  //   `worker: ${worker.name} is not compatible. ${coreVersion.major}.${\n  //     coreVersion.minor\n  //   }+ is required.`\n  // );\n\n  return true;\n}\n\n// @ts-ignore\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction parseVersion(version) {\n  const parts = version.split('.').map(Number);\n  return {major: parts[0], minor: parts[1]};\n}\n"], "mappings": "AACA,SAAQA,MAAM,QAAO,qBAAqB;AAC1C,SAAQC,OAAO,QAAO,sBAAsB;AAQ5C,OAAO,SAASC,qBAAqBA,CACnCC,MAAoB,EAEX;EAAA,IADTC,WAAmB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGJ,OAAO;EAE7BD,MAAM,CAACG,MAAM,EAAE,oBAAoB,CAAC;EAEpC,MAAMK,aAAa,GAAGL,MAAM,CAACM,OAAO;EACpC,IAAI,CAACL,WAAW,IAAI,CAACI,aAAa,EAAE;IAClC,OAAO,KAAK;EACd;EAYA,OAAO,IAAI;AACb;AAIA,SAASE,YAAYA,CAACD,OAAO,EAAE;EAC7B,MAAME,KAAK,GAAGF,OAAO,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;EAC5C,OAAO;IAACC,KAAK,EAAEJ,KAAK,CAAC,CAAC,CAAC;IAAEK,KAAK,EAAEL,KAAK,CAAC,CAAC;EAAC,CAAC;AAC3C"}