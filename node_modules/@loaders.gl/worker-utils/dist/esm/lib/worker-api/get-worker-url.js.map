{"version": 3, "file": "get-worker-url.js", "names": ["assert", "VERSION", "__VERSION__", "NPM_TAG", "getWorkerName", "worker", "warning", "version", "concat", "name", "getWorkerURL", "options", "arguments", "length", "undefined", "workerOptions", "id", "workerFile", "url", "workerUrl", "_workerType", "module", "versionTag"], "sources": ["../../../../src/lib/worker-api/get-worker-url.ts"], "sourcesContent": ["// loaders.gl, MIT license\n\nimport type {WorkerObject, WorkerOptions} from '../../types';\nimport {assert} from '../env-utils/assert';\nimport {VERSION as __VERSION__} from '../env-utils/version';\n\nconst NPM_TAG = 'latest'; // 'beta', or 'latest' on release-branch\nconst VERSION = typeof __VERSION__ !== 'undefined' ? __VERSION__ : NPM_TAG;\n\n/**\n * Gets worker object's name (for debugging in Chrome thread inspector window)\n */\nexport function getWorkerName(worker: WorkerObject): string {\n  const warning = worker.version !== VERSION ? ` (worker-utils@${VERSION})` : '';\n  return `${worker.name}@${worker.version}${warning}`;\n}\n\n/**\n * Generate a worker URL based on worker object and options\n * @returns A URL to one of the following:\n * - a published worker on unpkg CDN\n * - a local test worker\n * - a URL provided by the user in options\n */\nexport function getWorkerURL(worker: WorkerObject, options: WorkerOptions = {}): string {\n  const workerOptions = options[worker.id] || {};\n\n  const workerFile = `${worker.id}-worker.js`;\n\n  let url = workerOptions.workerUrl;\n\n  // HACK: Allow for non-nested workerUrl for the CompressionWorker.\n  // For the compression worker, workerOptions is currently not nested correctly. For most loaders,\n  // you'd have options within an object, i.e. `{mvt: {coordinates: ...}}` but the CompressionWorker\n  // puts options at the top level, not within a `compression` key (its `id`). For this reason, the\n  // above `workerOptions` will always be a string (i.e. `'gzip'`) for the CompressionWorker. To not\n  // break backwards compatibility, we allow the CompressionWorker to have options at the top level.\n  if (!url && worker.id === 'compression') {\n    url = options.workerUrl;\n  }\n\n  // If URL is test, generate local loaders.gl url\n  // @ts-ignore _workerType\n  if (options._workerType === 'test') {\n    url = `modules/${worker.module}/dist/${workerFile}`;\n  }\n\n  // If url override is not provided, generate a URL to published version on npm CDN unpkg.com\n  if (!url) {\n    // GENERATE\n    let version = worker.version;\n    // On master we need to load npm alpha releases published with the `beta` tag\n    if (version === 'latest') {\n      // throw new Error('latest worker version specified');\n      version = NPM_TAG;\n    }\n    const versionTag = version ? `@${version}` : '';\n    url = `https://unpkg.com/@loaders.gl/${worker.module}${versionTag}/dist/${workerFile}`;\n  }\n\n  assert(url);\n\n  // Allow user to override location\n  return url;\n}\n"], "mappings": "AAGA,SAAQA,MAAM,QAAO,qBAAqB;AAC1C,SAAQC,OAAO,IAAIC,WAAW,QAAO,sBAAsB;AAE3D,MAAMC,OAAO,GAAG,QAAQ;AACxB,MAAMF,OAAO,GAAG,eAAkB,KAAK,WAAW,cAAiBE,OAAO;AAK1E,OAAO,SAASC,aAAaA,CAACC,MAAoB,EAAU;EAC1D,MAAMC,OAAO,GAAGD,MAAM,CAACE,OAAO,KAAKN,OAAO,qBAAAO,MAAA,CAAqBP,OAAO,SAAM,EAAE;EAC9E,UAAAO,MAAA,CAAUH,MAAM,CAACI,IAAI,OAAAD,MAAA,CAAIH,MAAM,CAACE,OAAO,EAAAC,MAAA,CAAGF,OAAO;AACnD;AASA,OAAO,SAASI,YAAYA,CAACL,MAAoB,EAAuC;EAAA,IAArCM,OAAsB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC5E,MAAMG,aAAa,GAAGJ,OAAO,CAACN,MAAM,CAACW,EAAE,CAAC,IAAI,CAAC,CAAC;EAE9C,MAAMC,UAAU,MAAAT,MAAA,CAAMH,MAAM,CAACW,EAAE,eAAY;EAE3C,IAAIE,GAAG,GAAGH,aAAa,CAACI,SAAS;EAQjC,IAAI,CAACD,GAAG,IAAIb,MAAM,CAACW,EAAE,KAAK,aAAa,EAAE;IACvCE,GAAG,GAAGP,OAAO,CAACQ,SAAS;EACzB;EAIA,IAAIR,OAAO,CAACS,WAAW,KAAK,MAAM,EAAE;IAClCF,GAAG,cAAAV,MAAA,CAAcH,MAAM,CAACgB,MAAM,YAAAb,MAAA,CAASS,UAAU,CAAE;EACrD;EAGA,IAAI,CAACC,GAAG,EAAE;IAER,IAAIX,OAAO,GAAGF,MAAM,CAACE,OAAO;IAE5B,IAAIA,OAAO,KAAK,QAAQ,EAAE;MAExBA,OAAO,GAAGJ,OAAO;IACnB;IACA,MAAMmB,UAAU,GAAGf,OAAO,OAAAC,MAAA,CAAOD,OAAO,IAAK,EAAE;IAC/CW,GAAG,oCAAAV,MAAA,CAAoCH,MAAM,CAACgB,MAAM,EAAAb,MAAA,CAAGc,UAAU,YAAAd,MAAA,CAASS,UAAU,CAAE;EACxF;EAEAjB,MAAM,CAACkB,GAAG,CAAC;EAGX,OAAOA,GAAG;AACZ"}