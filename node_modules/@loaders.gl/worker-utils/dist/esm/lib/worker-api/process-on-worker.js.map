{"version": 3, "file": "process-on-worker.js", "names": ["WorkerFarm", "getWorkerURL", "getWorkerName", "getTransferListForWriter", "canProcessOnWorker", "worker", "options", "isSupported", "processOnWorker", "data", "arguments", "length", "undefined", "context", "name", "workerFarm", "getWorkerFarm", "source", "workerPoolProps", "url", "workerPool", "getWorkerPool", "job<PERSON>ame", "job", "startJob", "onMessage", "bind", "transferableOptions", "postMessage", "input", "result", "type", "payload", "done", "error", "Error", "id", "process", "message", "console", "warn", "concat"], "sources": ["../../../../src/lib/worker-api/process-on-worker.ts"], "sourcesContent": ["import type {\n  WorkerObject,\n  WorkerOptions,\n  WorkerContext,\n  WorkerMessageType,\n  WorkerMessagePayload\n} from '../../types';\nimport type WorkerJob from '../worker-farm/worker-job';\nimport WorkerFarm from '../worker-farm/worker-farm';\nimport {getWorkerURL, getWorkerName} from './get-worker-url';\nimport {getTransferListForWriter} from '../worker-utils/get-transfer-list';\n\ntype ProcessOnWorkerOptions = WorkerOptions & {\n  jobName?: string;\n  [key: string]: any;\n};\n\n/**\n * Determines if we can parse with worker\n * @param loader\n * @param data\n * @param options\n */\nexport function canProcessOnWorker(worker: WorkerObject, options?: WorkerOptions) {\n  if (!WorkerFarm.isSupported()) {\n    return false;\n  }\n\n  return worker.worker && options?.worker;\n}\n\n/**\n * This function expects that the worker thread sends certain messages,\n * Creating such a worker can be automated if the worker is wrapper by a call to\n * createWorker in @loaders.gl/worker-utils.\n */\nexport async function processOnWorker(\n  worker: WorkerObject,\n  data: any,\n  options: ProcessOnWorkerOptions = {},\n  context: WorkerContext = {}\n): Promise<any> {\n  const name = getWorkerName(worker);\n\n  const workerFarm = WorkerFarm.getWorkerFarm(options);\n  const {source} = options;\n  const workerPoolProps: {name: string; source?: string; url?: string} = {name, source};\n  if (!source) {\n    workerPoolProps.url = getWorkerURL(worker, options);\n  }\n  const workerPool = workerFarm.getWorkerPool(workerPoolProps);\n\n  const jobName = options.jobName || worker.name;\n  const job = await workerPool.startJob(\n    jobName,\n    // eslint-disable-next-line\n    onMessage.bind(null, context)\n  );\n\n  // Kick off the processing in the worker\n  const transferableOptions = getTransferListForWriter(options);\n  job.postMessage('process', {input: data, options: transferableOptions});\n\n  const result = await job.result;\n  return result.result;\n}\n\n/**\n * Job completes when we receive the result\n * @param job\n * @param message\n */\nasync function onMessage(\n  context: WorkerContext,\n  job: WorkerJob,\n  type: WorkerMessageType,\n  payload: WorkerMessagePayload\n) {\n  switch (type) {\n    case 'done':\n      // Worker is done\n      job.done(payload);\n      break;\n\n    case 'error':\n      // Worker encountered an error\n      job.error(new Error(payload.error));\n      break;\n\n    case 'process':\n      // Worker is asking for us (main thread) to process something\n      const {id, input, options} = payload;\n      try {\n        if (!context.process) {\n          job.postMessage('error', {id, error: 'Worker not set up to process on main thread'});\n          return;\n        }\n        const result = await context.process(input, options);\n        job.postMessage('done', {id, result});\n      } catch (error) {\n        const message = error instanceof Error ? error.message : 'unknown error';\n        job.postMessage('error', {id, error: message});\n      }\n      break;\n\n    default:\n      // eslint-disable-next-line\n      console.warn(`process-on-worker: unknown message ${type}`);\n  }\n}\n"], "mappings": "AAQA,OAAOA,UAAU,MAAM,4BAA4B;AACnD,SAAQC,YAAY,EAAEC,aAAa,QAAO,kBAAkB;AAC5D,SAAQC,wBAAwB,QAAO,mCAAmC;AAa1E,OAAO,SAASC,kBAAkBA,CAACC,MAAoB,EAAEC,OAAuB,EAAE;EAChF,IAAI,CAACN,UAAU,CAACO,WAAW,CAAC,CAAC,EAAE;IAC7B,OAAO,KAAK;EACd;EAEA,OAAOF,MAAM,CAACA,MAAM,KAAIC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAED,MAAM;AACzC;AAOA,OAAO,eAAeG,eAAeA,CACnCH,MAAoB,EACpBI,IAAS,EAGK;EAAA,IAFdH,OAA+B,GAAAI,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAAA,IACpCG,OAAsB,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAE3B,MAAMI,IAAI,GAAGZ,aAAa,CAACG,MAAM,CAAC;EAElC,MAAMU,UAAU,GAAGf,UAAU,CAACgB,aAAa,CAACV,OAAO,CAAC;EACpD,MAAM;IAACW;EAAM,CAAC,GAAGX,OAAO;EACxB,MAAMY,eAA8D,GAAG;IAACJ,IAAI;IAAEG;EAAM,CAAC;EACrF,IAAI,CAACA,MAAM,EAAE;IACXC,eAAe,CAACC,GAAG,GAAGlB,YAAY,CAACI,MAAM,EAAEC,OAAO,CAAC;EACrD;EACA,MAAMc,UAAU,GAAGL,UAAU,CAACM,aAAa,CAACH,eAAe,CAAC;EAE5D,MAAMI,OAAO,GAAGhB,OAAO,CAACgB,OAAO,IAAIjB,MAAM,CAACS,IAAI;EAC9C,MAAMS,GAAG,GAAG,MAAMH,UAAU,CAACI,QAAQ,CACnCF,OAAO,EAEPG,SAAS,CAACC,IAAI,CAAC,IAAI,EAAEb,OAAO,CAC9B,CAAC;EAGD,MAAMc,mBAAmB,GAAGxB,wBAAwB,CAACG,OAAO,CAAC;EAC7DiB,GAAG,CAACK,WAAW,CAAC,SAAS,EAAE;IAACC,KAAK,EAAEpB,IAAI;IAAEH,OAAO,EAAEqB;EAAmB,CAAC,CAAC;EAEvE,MAAMG,MAAM,GAAG,MAAMP,GAAG,CAACO,MAAM;EAC/B,OAAOA,MAAM,CAACA,MAAM;AACtB;AAOA,eAAeL,SAASA,CACtBZ,OAAsB,EACtBU,GAAc,EACdQ,IAAuB,EACvBC,OAA6B,EAC7B;EACA,QAAQD,IAAI;IACV,KAAK,MAAM;MAETR,GAAG,CAACU,IAAI,CAACD,OAAO,CAAC;MACjB;IAEF,KAAK,OAAO;MAEVT,GAAG,CAACW,KAAK,CAAC,IAAIC,KAAK,CAACH,OAAO,CAACE,KAAK,CAAC,CAAC;MACnC;IAEF,KAAK,SAAS;MAEZ,MAAM;QAACE,EAAE;QAAEP,KAAK;QAAEvB;MAAO,CAAC,GAAG0B,OAAO;MACpC,IAAI;QACF,IAAI,CAACnB,OAAO,CAACwB,OAAO,EAAE;UACpBd,GAAG,CAACK,WAAW,CAAC,OAAO,EAAE;YAACQ,EAAE;YAAEF,KAAK,EAAE;UAA6C,CAAC,CAAC;UACpF;QACF;QACA,MAAMJ,MAAM,GAAG,MAAMjB,OAAO,CAACwB,OAAO,CAACR,KAAK,EAAEvB,OAAO,CAAC;QACpDiB,GAAG,CAACK,WAAW,CAAC,MAAM,EAAE;UAACQ,EAAE;UAAEN;QAAM,CAAC,CAAC;MACvC,CAAC,CAAC,OAAOI,KAAK,EAAE;QACd,MAAMI,OAAO,GAAGJ,KAAK,YAAYC,KAAK,GAAGD,KAAK,CAACI,OAAO,GAAG,eAAe;QACxEf,GAAG,CAACK,WAAW,CAAC,OAAO,EAAE;UAACQ,EAAE;UAAEF,KAAK,EAAEI;QAAO,CAAC,CAAC;MAChD;MACA;IAEF;MAEEC,OAAO,CAACC,IAAI,uCAAAC,MAAA,CAAuCV,IAAI,CAAE,CAAC;EAC9D;AACF"}