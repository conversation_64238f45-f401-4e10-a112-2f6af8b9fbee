{"version": 3, "file": "create-worker.js", "names": ["AsyncQueue", "WorkerBody", "requestId", "inputBatches", "options", "createWorker", "process", "processInBatches", "inWorkerThread", "context", "processOnMainThread", "onmessage", "type", "payload", "Error", "result", "input", "postMessage", "resultIterator", "batch", "push", "close", "error", "message", "arrayBuffer", "arguments", "length", "undefined", "Promise", "resolve", "reject", "id", "onMessage", "removeEventListener", "addEventListener"], "sources": ["../../../../src/lib/worker-api/create-worker.ts"], "sourcesContent": ["import type {\n  WorkerMessageType,\n  WorkerMessagePayload,\n  WorkerContext,\n  Process,\n  ProcessInBatches\n} from '../../types';\nimport AsyncQueue from '../async-queue/async-queue';\nimport WorkerBody from '../worker-farm/worker-body';\n// import {validateWorkerVersion} from './validate-worker-version';\n\n/** Counter for jobs */\nlet requestId = 0;\nlet inputBatches: AsyncQueue<any>;\nlet options: {[key: string]: any};\n\nexport type ProcessOnMainThread = (\n  data: any,\n  options?: {[key: string]: any},\n  context?: WorkerContext\n) => any;\n\n/**\n * Set up a WebWorkerGlobalScope to talk with the main thread\n */\nexport function createWorker(process: Process, processInBatches?: ProcessInBatches): void {\n  if (!WorkerBody.inWorkerThread()) {\n    return;\n  }\n\n  const context: WorkerContext = {\n    process: processOnMainThread\n  };\n\n  // eslint-disable-next-line complexity\n  WorkerBody.onmessage = async (type: WorkerMessageType, payload: WorkerMessagePayload) => {\n    try {\n      switch (type) {\n        case 'process':\n          if (!process) {\n            throw new Error('Worker does not support atomic processing');\n          }\n          const result = await process(payload.input, payload.options || {}, context);\n          WorkerBody.postMessage('done', {result});\n          break;\n\n        case 'process-in-batches':\n          if (!processInBatches) {\n            throw new Error('Worker does not support batched processing');\n          }\n          inputBatches = new AsyncQueue<any>();\n          options = payload.options || {};\n          const resultIterator = processInBatches(inputBatches, options, context);\n          for await (const batch of resultIterator) {\n            WorkerBody.postMessage('output-batch', {result: batch});\n          }\n          WorkerBody.postMessage('done', {});\n          break;\n\n        case 'input-batch':\n          inputBatches.push(payload.input);\n          break;\n\n        case 'input-done':\n          inputBatches.close();\n          break;\n\n        default:\n      }\n    } catch (error) {\n      const message = error instanceof Error ? error.message : '';\n      WorkerBody.postMessage('error', {error: message});\n    }\n  };\n}\n\nfunction processOnMainThread(arrayBuffer: ArrayBuffer, options = {}) {\n  return new Promise((resolve, reject) => {\n    const id = requestId++;\n\n    /**\n     */\n    const onMessage = (type: string, payload: WorkerMessagePayload) => {\n      if (payload.id !== id) {\n        // not ours\n        return;\n      }\n\n      switch (type) {\n        case 'done':\n          WorkerBody.removeEventListener(onMessage);\n          resolve(payload.result);\n          break;\n\n        case 'error':\n          WorkerBody.removeEventListener(onMessage);\n          reject(payload.error);\n          break;\n\n        default:\n        // ignore\n      }\n    };\n\n    WorkerBody.addEventListener(onMessage);\n\n    // Ask the main thread to decode data\n    const payload = {id, input: arrayBuffer, options};\n    WorkerBody.postMessage('process', payload);\n  });\n}\n"], "mappings": "AAOA,OAAOA,UAAU,MAAM,4BAA4B;AACnD,OAAOC,UAAU,MAAM,4BAA4B;AAInD,IAAIC,SAAS,GAAG,CAAC;AACjB,IAAIC,YAA6B;AACjC,IAAIC,OAA6B;AAWjC,OAAO,SAASC,YAAYA,CAACC,OAAgB,EAAEC,gBAAmC,EAAQ;EACxF,IAAI,CAACN,UAAU,CAACO,cAAc,CAAC,CAAC,EAAE;IAChC;EACF;EAEA,MAAMC,OAAsB,GAAG;IAC7BH,OAAO,EAAEI;EACX,CAAC;EAGDT,UAAU,CAACU,SAAS,GAAG,OAAOC,IAAuB,EAAEC,OAA6B,KAAK;IACvF,IAAI;MACF,QAAQD,IAAI;QACV,KAAK,SAAS;UACZ,IAAI,CAACN,OAAO,EAAE;YACZ,MAAM,IAAIQ,KAAK,CAAC,2CAA2C,CAAC;UAC9D;UACA,MAAMC,MAAM,GAAG,MAAMT,OAAO,CAACO,OAAO,CAACG,KAAK,EAAEH,OAAO,CAACT,OAAO,IAAI,CAAC,CAAC,EAAEK,OAAO,CAAC;UAC3ER,UAAU,CAACgB,WAAW,CAAC,MAAM,EAAE;YAACF;UAAM,CAAC,CAAC;UACxC;QAEF,KAAK,oBAAoB;UACvB,IAAI,CAACR,gBAAgB,EAAE;YACrB,MAAM,IAAIO,KAAK,CAAC,4CAA4C,CAAC;UAC/D;UACAX,YAAY,GAAG,IAAIH,UAAU,CAAM,CAAC;UACpCI,OAAO,GAAGS,OAAO,CAACT,OAAO,IAAI,CAAC,CAAC;UAC/B,MAAMc,cAAc,GAAGX,gBAAgB,CAACJ,YAAY,EAAEC,OAAO,EAAEK,OAAO,CAAC;UACvE,WAAW,MAAMU,KAAK,IAAID,cAAc,EAAE;YACxCjB,UAAU,CAACgB,WAAW,CAAC,cAAc,EAAE;cAACF,MAAM,EAAEI;YAAK,CAAC,CAAC;UACzD;UACAlB,UAAU,CAACgB,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;UAClC;QAEF,KAAK,aAAa;UAChBd,YAAY,CAACiB,IAAI,CAACP,OAAO,CAACG,KAAK,CAAC;UAChC;QAEF,KAAK,YAAY;UACfb,YAAY,CAACkB,KAAK,CAAC,CAAC;UACpB;QAEF;MACF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd,MAAMC,OAAO,GAAGD,KAAK,YAAYR,KAAK,GAAGQ,KAAK,CAACC,OAAO,GAAG,EAAE;MAC3DtB,UAAU,CAACgB,WAAW,CAAC,OAAO,EAAE;QAACK,KAAK,EAAEC;MAAO,CAAC,CAAC;IACnD;EACF,CAAC;AACH;AAEA,SAASb,mBAAmBA,CAACc,WAAwB,EAAgB;EAAA,IAAdpB,OAAO,GAAAqB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACjE,OAAO,IAAIG,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,MAAMC,EAAE,GAAG7B,SAAS,EAAE;IAItB,MAAM8B,SAAS,GAAGA,CAACpB,IAAY,EAAEC,OAA6B,KAAK;MACjE,IAAIA,OAAO,CAACkB,EAAE,KAAKA,EAAE,EAAE;QAErB;MACF;MAEA,QAAQnB,IAAI;QACV,KAAK,MAAM;UACTX,UAAU,CAACgC,mBAAmB,CAACD,SAAS,CAAC;UACzCH,OAAO,CAAChB,OAAO,CAACE,MAAM,CAAC;UACvB;QAEF,KAAK,OAAO;UACVd,UAAU,CAACgC,mBAAmB,CAACD,SAAS,CAAC;UACzCF,MAAM,CAACjB,OAAO,CAACS,KAAK,CAAC;UACrB;QAEF;MAEF;IACF,CAAC;IAEDrB,UAAU,CAACiC,gBAAgB,CAACF,SAAS,CAAC;IAGtC,MAAMnB,OAAO,GAAG;MAACkB,EAAE;MAAEf,KAAK,EAAEQ,WAAW;MAAEpB;IAAO,CAAC;IACjDH,UAAU,CAACgB,WAAW,CAAC,SAAS,EAAEJ,OAAO,CAAC;EAC5C,CAAC,CAAC;AACJ"}