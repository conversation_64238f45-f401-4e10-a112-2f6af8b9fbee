{"version": 3, "file": "get-loadable-worker-url.js", "names": ["assert", "workerURLCache", "Map", "getLoadableWorkerURL", "props", "source", "url", "workerURL", "get", "getLoadableWorkerURLFromURL", "set", "getLoadableWorkerURLFromSource", "startsWith", "workerSource", "buildScriptSource", "blob", "Blob", "type", "URL", "createObjectURL", "workerUrl", "concat"], "sources": ["../../../../src/lib/worker-utils/get-loadable-worker-url.ts"], "sourcesContent": ["import {assert} from '../env-utils/assert';\n\nconst workerURLCache = new Map();\n\n/**\n * Creates a loadable URL from worker source or URL\n * that can be used to create `Worker` instances.\n * Due to CORS issues it may be necessary to wrap a URL in a small importScripts\n * @param props\n * @param props.source Worker source\n * @param props.url Worker URL\n * @returns loadable url\n */\nexport function getLoadableWorkerURL(props: {source?: string; url?: string}) {\n  assert((props.source && !props.url) || (!props.source && props.url)); // Either source or url must be defined\n\n  let workerURL = workerURLCache.get(props.source || props.url);\n  if (!workerURL) {\n    // Differentiate worker urls from worker source code\n    if (props.url) {\n      workerURL = getLoadableWorkerURLFromURL(props.url);\n      workerURLCache.set(props.url, workerURL);\n    }\n\n    if (props.source) {\n      workerURL = getLoadableWorkerURLFromSource(props.source);\n      workerURLCache.set(props.source, workerURL);\n    }\n  }\n\n  assert(workerURL);\n  return workerURL;\n}\n\n/**\n * Build a loadable worker URL from worker URL\n * @param url\n * @returns loadable URL\n */\nfunction getLoadableWorkerURLFromURL(url: string): string {\n  // A local script url, we can use it to initialize a Worker directly\n  if (!url.startsWith('http')) {\n    return url;\n  }\n\n  // A remote script, we need to use `importScripts` to load from different origin\n  const workerSource = buildScriptSource(url);\n  return getLoadableWorkerURLFromSource(workerSource);\n}\n\n/**\n * Build a loadable worker URL from worker source\n * @param workerSource\n * @returns loadable url\n */\nfunction getLoadableWorkerURLFromSource(workerSource: string): string {\n  const blob = new Blob([workerSource], {type: 'application/javascript'});\n  return URL.createObjectURL(blob);\n}\n\n/**\n * Per spec, worker cannot be initialized with a script from a different origin\n * However a local worker script can still import scripts from other origins,\n * so we simply build a wrapper script.\n *\n * @param workerUrl\n * @returns source\n */\nfunction buildScriptSource(workerUrl: string): string {\n  return `\\\ntry {\n  importScripts('${workerUrl}');\n} catch (error) {\n  console.error(error);\n  throw error;\n}`;\n}\n"], "mappings": "AAAA,SAAQA,MAAM,QAAO,qBAAqB;AAE1C,MAAMC,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;AAWhC,OAAO,SAASC,oBAAoBA,CAACC,KAAsC,EAAE;EAC3EJ,MAAM,CAAEI,KAAK,CAACC,MAAM,IAAI,CAACD,KAAK,CAACE,GAAG,IAAM,CAACF,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,GAAI,CAAC;EAEpE,IAAIC,SAAS,GAAGN,cAAc,CAACO,GAAG,CAACJ,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,GAAG,CAAC;EAC7D,IAAI,CAACC,SAAS,EAAE;IAEd,IAAIH,KAAK,CAACE,GAAG,EAAE;MACbC,SAAS,GAAGE,2BAA2B,CAACL,KAAK,CAACE,GAAG,CAAC;MAClDL,cAAc,CAACS,GAAG,CAACN,KAAK,CAACE,GAAG,EAAEC,SAAS,CAAC;IAC1C;IAEA,IAAIH,KAAK,CAACC,MAAM,EAAE;MAChBE,SAAS,GAAGI,8BAA8B,CAACP,KAAK,CAACC,MAAM,CAAC;MACxDJ,cAAc,CAACS,GAAG,CAACN,KAAK,CAACC,MAAM,EAAEE,SAAS,CAAC;IAC7C;EACF;EAEAP,MAAM,CAACO,SAAS,CAAC;EACjB,OAAOA,SAAS;AAClB;AAOA,SAASE,2BAA2BA,CAACH,GAAW,EAAU;EAExD,IAAI,CAACA,GAAG,CAACM,UAAU,CAAC,MAAM,CAAC,EAAE;IAC3B,OAAON,GAAG;EACZ;EAGA,MAAMO,YAAY,GAAGC,iBAAiB,CAACR,GAAG,CAAC;EAC3C,OAAOK,8BAA8B,CAACE,YAAY,CAAC;AACrD;AAOA,SAASF,8BAA8BA,CAACE,YAAoB,EAAU;EACpE,MAAME,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,YAAY,CAAC,EAAE;IAACI,IAAI,EAAE;EAAwB,CAAC,CAAC;EACvE,OAAOC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;AAClC;AAUA,SAASD,iBAAiBA,CAACM,SAAiB,EAAU;EACpD,kCAAAC,MAAA,CAEiBD,SAAS;AAK5B"}