{"version": 3, "file": "get-transfer-list.js", "names": ["getTransferList", "object", "recursive", "arguments", "length", "undefined", "transfers", "transfersSet", "Set", "isTransferable", "add", "buffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "key", "Array", "from", "MessagePort", "ImageBitmap", "OffscreenCanvas", "getTransferListForWriter", "clone", "Object", "assign", "keys", "for<PERSON>ach", "RegExp"], "sources": ["../../../../src/lib/worker-utils/get-transfer-list.ts"], "sourcesContent": ["// NOTE - there is a copy of this function is both in core and loader-utils\n// core does not need all the utils in loader-utils, just this one.\n\n/**\n * Returns an array of Transferrable objects that can be used with postMessage\n * https://developer.mozilla.org/en-US/docs/Web/API/Worker/postMessage\n * @param object data to be sent via postMessage\n * @param recursive - not for application use\n * @param transfers - not for application use\n * @returns a transfer list that can be passed to postMessage\n */\nexport function getTransferList(\n  object: any,\n  recursive: boolean = true,\n  transfers?: Set<any>\n): Transferable[] {\n  // Make sure that items in the transfer list is unique\n  const transfersSet = transfers || new Set();\n\n  if (!object) {\n    // ignore\n  } else if (isTransferable(object)) {\n    transfersSet.add(object);\n  } else if (isTransferable(object.buffer)) {\n    // Typed array\n    transfersSet.add(object.buffer);\n  } else if (ArrayBuffer.isView(object)) {\n    // object is a TypeArray viewing into a SharedArrayBuffer (not transferable)\n    // Do not iterate through the content in this case\n  } else if (recursive && typeof object === 'object') {\n    for (const key in object) {\n      // Avoid perf hit - only go one level deep\n      getTransferList(object[key], recursive, transfersSet);\n    }\n  }\n\n  // If transfers is defined, is internal recursive call\n  // Otherwise it's called by the user\n  return transfers === undefined ? Array.from(transfersSet) : [];\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/Transferable\nfunction isTransferable(object: unknown) {\n  if (!object) {\n    return false;\n  }\n  if (object instanceof ArrayBuffer) {\n    return true;\n  }\n  if (typeof MessagePort !== 'undefined' && object instanceof MessagePort) {\n    return true;\n  }\n  if (typeof ImageBitmap !== 'undefined' && object instanceof ImageBitmap) {\n    return true;\n  }\n  // @ts-ignore\n  if (typeof OffscreenCanvas !== 'undefined' && object instanceof OffscreenCanvas) {\n    return true;\n  }\n  return false;\n}\n\n/**\n * Recursively drop non serializable values like functions and regexps.\n * @param object\n */\nexport function getTransferListForWriter(object: object | null): object {\n  if (object === null) {\n    return {};\n  }\n  const clone = Object.assign({}, object);\n\n  Object.keys(clone).forEach((key) => {\n    // Typed Arrays and Arrays are passed with no change\n    if (\n      typeof object[key] === 'object' &&\n      !ArrayBuffer.isView(object[key]) &&\n      !(object[key] instanceof Array)\n    ) {\n      clone[key] = getTransferListForWriter(object[key]);\n    } else if (typeof clone[key] === 'function' || clone[key] instanceof RegExp) {\n      clone[key] = {};\n    } else {\n      clone[key] = object[key];\n    }\n  });\n\n  return clone;\n}\n"], "mappings": "AAWA,OAAO,SAASA,eAAeA,CAC7BC,MAAW,EAGK;EAAA,IAFhBC,SAAkB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAAA,IACzBG,SAAoB,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAGpB,MAAME,YAAY,GAAGD,SAAS,IAAI,IAAIE,GAAG,CAAC,CAAC;EAE3C,IAAI,CAACP,MAAM,EAAE,CAEb,CAAC,MAAM,IAAIQ,cAAc,CAACR,MAAM,CAAC,EAAE;IACjCM,YAAY,CAACG,GAAG,CAACT,MAAM,CAAC;EAC1B,CAAC,MAAM,IAAIQ,cAAc,CAACR,MAAM,CAACU,MAAM,CAAC,EAAE;IAExCJ,YAAY,CAACG,GAAG,CAACT,MAAM,CAACU,MAAM,CAAC;EACjC,CAAC,MAAM,IAAIC,WAAW,CAACC,MAAM,CAACZ,MAAM,CAAC,EAAE,CAGvC,CAAC,MAAM,IAAIC,SAAS,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;IAClD,KAAK,MAAMa,GAAG,IAAIb,MAAM,EAAE;MAExBD,eAAe,CAACC,MAAM,CAACa,GAAG,CAAC,EAAEZ,SAAS,EAAEK,YAAY,CAAC;IACvD;EACF;EAIA,OAAOD,SAAS,KAAKD,SAAS,GAAGU,KAAK,CAACC,IAAI,CAACT,YAAY,CAAC,GAAG,EAAE;AAChE;AAGA,SAASE,cAAcA,CAACR,MAAe,EAAE;EACvC,IAAI,CAACA,MAAM,EAAE;IACX,OAAO,KAAK;EACd;EACA,IAAIA,MAAM,YAAYW,WAAW,EAAE;IACjC,OAAO,IAAI;EACb;EACA,IAAI,OAAOK,WAAW,KAAK,WAAW,IAAIhB,MAAM,YAAYgB,WAAW,EAAE;IACvE,OAAO,IAAI;EACb;EACA,IAAI,OAAOC,WAAW,KAAK,WAAW,IAAIjB,MAAM,YAAYiB,WAAW,EAAE;IACvE,OAAO,IAAI;EACb;EAEA,IAAI,OAAOC,eAAe,KAAK,WAAW,IAAIlB,MAAM,YAAYkB,eAAe,EAAE;IAC/E,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd;AAMA,OAAO,SAASC,wBAAwBA,CAACnB,MAAqB,EAAU;EACtE,IAAIA,MAAM,KAAK,IAAI,EAAE;IACnB,OAAO,CAAC,CAAC;EACX;EACA,MAAMoB,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEtB,MAAM,CAAC;EAEvCqB,MAAM,CAACE,IAAI,CAACH,KAAK,CAAC,CAACI,OAAO,CAAEX,GAAG,IAAK;IAElC,IACE,OAAOb,MAAM,CAACa,GAAG,CAAC,KAAK,QAAQ,IAC/B,CAACF,WAAW,CAACC,MAAM,CAACZ,MAAM,CAACa,GAAG,CAAC,CAAC,IAChC,EAAEb,MAAM,CAACa,GAAG,CAAC,YAAYC,KAAK,CAAC,EAC/B;MACAM,KAAK,CAACP,GAAG,CAAC,GAAGM,wBAAwB,CAACnB,MAAM,CAACa,GAAG,CAAC,CAAC;IACpD,CAAC,MAAM,IAAI,OAAOO,KAAK,CAACP,GAAG,CAAC,KAAK,UAAU,IAAIO,KAAK,CAACP,GAAG,CAAC,YAAYY,MAAM,EAAE;MAC3EL,KAAK,CAACP,GAAG,CAAC,GAAG,CAAC,CAAC;IACjB,CAAC,MAAM;MACLO,KAAK,CAACP,GAAG,CAAC,GAAGb,MAAM,CAACa,GAAG,CAAC;IAC1B;EACF,CAAC,CAAC;EAEF,OAAOO,KAAK;AACd"}