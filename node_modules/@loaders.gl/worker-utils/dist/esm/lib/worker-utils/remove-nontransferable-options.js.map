{"version": 3, "file": "remove-nontransferable-options.js", "names": ["removeNontransferableOptions", "object", "clone", "Object", "assign", "keys", "for<PERSON>ach", "key", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "RegExp"], "sources": ["../../../../src/lib/worker-utils/remove-nontransferable-options.ts"], "sourcesContent": ["/**\n * Recursively drop non serializable values like functions and regexps.\n * @param object\n */\nexport function removeNontransferableOptions(object: object | null): object {\n  if (object === null) {\n    return {};\n  }\n  const clone = Object.assign({}, object);\n\n  Object.keys(clone).forEach((key) => {\n    // Checking if it is an object and not a typed array.\n    if (typeof object[key] === 'object' && !ArrayBuffer.isView(object[key])) {\n      clone[key] = removeNontransferableOptions(object[key]);\n    } else if (typeof clone[key] === 'function' || clone[key] instanceof RegExp) {\n      clone[key] = {};\n    } else {\n      clone[key] = object[key];\n    }\n  });\n\n  return clone;\n}\n"], "mappings": "AAIA,OAAO,SAASA,4BAA4BA,CAACC,MAAqB,EAAU;EAC1E,IAAIA,MAAM,KAAK,IAAI,EAAE;IACnB,OAAO,CAAC,CAAC;EACX;EACA,MAAMC,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,MAAM,CAAC;EAEvCE,MAAM,CAACE,IAAI,CAACH,KAAK,CAAC,CAACI,OAAO,CAAEC,GAAG,IAAK;IAElC,IAAI,OAAON,MAAM,CAACM,GAAG,CAAC,KAAK,QAAQ,IAAI,CAACC,WAAW,CAACC,MAAM,CAACR,MAAM,CAACM,GAAG,CAAC,CAAC,EAAE;MACvEL,KAAK,CAACK,GAAG,CAAC,GAAGP,4BAA4B,CAACC,MAAM,CAACM,GAAG,CAAC,CAAC;IACxD,CAAC,MAAM,IAAI,OAAOL,KAAK,CAACK,GAAG,CAAC,KAAK,UAAU,IAAIL,KAAK,CAACK,GAAG,CAAC,YAAYG,MAAM,EAAE;MAC3ER,KAAK,CAACK,GAAG,CAAC,GAAG,CAAC,CAAC;IACjB,CAAC,MAAM;MACLL,KAAK,CAACK,GAAG,CAAC,GAAGN,MAAM,CAACM,GAAG,CAAC;IAC1B;EACF,CAAC,CAAC;EAEF,OAAOL,KAAK;AACd"}