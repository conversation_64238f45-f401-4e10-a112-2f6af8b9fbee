import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
import { isMobile } from '../env-utils/globals';
import WorkerThread from './worker-thread';
import WorkerJob from './worker-job';
export default class WorkerPool {
  static isSupported() {
    return WorkerThread.isSupported();
  }
  constructor(props) {
    _defineProperty(this, "name", 'unnamed');
    _defineProperty(this, "source", void 0);
    _defineProperty(this, "url", void 0);
    _defineProperty(this, "maxConcurrency", 1);
    _defineProperty(this, "maxMobileConcurrency", 1);
    _defineProperty(this, "onDebug", () => {});
    _defineProperty(this, "reuseWorkers", true);
    _defineProperty(this, "props", {});
    _defineProperty(this, "jobQueue", []);
    _defineProperty(this, "idleQueue", []);
    _defineProperty(this, "count", 0);
    _defineProperty(this, "isDestroyed", false);
    this.source = props.source;
    this.url = props.url;
    this.setProps(props);
  }
  destroy() {
    this.idleQueue.forEach(worker => worker.destroy());
    this.isDestroyed = true;
  }
  setProps(props) {
    this.props = {
      ...this.props,
      ...props
    };
    if (props.name !== undefined) {
      this.name = props.name;
    }
    if (props.maxConcurrency !== undefined) {
      this.maxConcurrency = props.maxConcurrency;
    }
    if (props.maxMobileConcurrency !== undefined) {
      this.maxMobileConcurrency = props.maxMobileConcurrency;
    }
    if (props.reuseWorkers !== undefined) {
      this.reuseWorkers = props.reuseWorkers;
    }
    if (props.onDebug !== undefined) {
      this.onDebug = props.onDebug;
    }
  }
  async startJob(name) {
    let onMessage = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (job, type, data) => job.done(data);
    let onError = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (job, error) => job.error(error);
    const startPromise = new Promise(onStart => {
      this.jobQueue.push({
        name,
        onMessage,
        onError,
        onStart
      });
      return this;
    });
    this._startQueuedJob();
    return await startPromise;
  }
  async _startQueuedJob() {
    if (!this.jobQueue.length) {
      return;
    }
    const workerThread = this._getAvailableWorker();
    if (!workerThread) {
      return;
    }
    const queuedJob = this.jobQueue.shift();
    if (queuedJob) {
      this.onDebug({
        message: 'Starting job',
        name: queuedJob.name,
        workerThread,
        backlog: this.jobQueue.length
      });
      const job = new WorkerJob(queuedJob.name, workerThread);
      workerThread.onMessage = data => queuedJob.onMessage(job, data.type, data.payload);
      workerThread.onError = error => queuedJob.onError(job, error);
      queuedJob.onStart(job);
      try {
        await job.result;
      } finally {
        this.returnWorkerToQueue(workerThread);
      }
    }
  }
  returnWorkerToQueue(worker) {
    const shouldDestroyWorker = this.isDestroyed || !this.reuseWorkers || this.count > this._getMaxConcurrency();
    if (shouldDestroyWorker) {
      worker.destroy();
      this.count--;
    } else {
      this.idleQueue.push(worker);
    }
    if (!this.isDestroyed) {
      this._startQueuedJob();
    }
  }
  _getAvailableWorker() {
    if (this.idleQueue.length > 0) {
      return this.idleQueue.shift() || null;
    }
    if (this.count < this._getMaxConcurrency()) {
      this.count++;
      const name = "".concat(this.name.toLowerCase(), " (#").concat(this.count, " of ").concat(this.maxConcurrency, ")");
      return new WorkerThread({
        name,
        source: this.source,
        url: this.url
      });
    }
    return null;
  }
  _getMaxConcurrency() {
    return isMobile ? this.maxMobileConcurrency : this.maxConcurrency;
  }
}
//# sourceMappingURL=worker-pool.js.map