{"version": 3, "file": "worker-thread.js", "names": ["NodeWorker", "<PERSON><PERSON><PERSON><PERSON>", "assert", "getLoadableWorkerURL", "getTransferList", "NOOP", "WorkerThread", "isSupported", "Worker", "constructor", "props", "_defineProperty", "name", "source", "url", "onMessage", "onError", "error", "console", "log", "worker", "_createBrowserWorker", "_createNodeWorker", "destroy", "terminate", "terminated", "isRunning", "Boolean", "postMessage", "data", "transferList", "_getErrorFromErrorEvent", "event", "message", "concat", "lineno", "colno", "Error", "_loadableURL", "onmessage", "onerror", "onmessageerror", "absolute", "includes", "startsWith", "eval", "on", "code"], "sources": ["../../../../src/lib/worker-farm/worker-thread.ts"], "sourcesContent": ["// loaders.gl, MIT license\n\nimport {Node<PERSON>orker, NodeWorkerType} from '../node/worker_threads';\nimport {isBrowser} from '../env-utils/globals';\nimport {assert} from '../env-utils/assert';\nimport {getLoadableWorkerURL} from '../worker-utils/get-loadable-worker-url';\nimport {getTransferList} from '../worker-utils/get-transfer-list';\n\nconst NOOP = () => {};\n\nexport type WorkerThreadProps = {\n  name: string;\n  source?: string;\n  url?: string;\n};\n\n/**\n * Represents one worker thread\n */\nexport default class WorkerThread {\n  readonly name: string;\n  readonly source: string | undefined;\n  readonly url: string | undefined;\n  terminated: boolean = false;\n  worker: Worker | NodeWorkerType;\n  onMessage: (message: any) => void;\n  onError: (error: Error) => void;\n\n  private _loadableURL: string = '';\n\n  /** Checks if workers are supported on this platform */\n  static isSupported(): boolean {\n    return (\n      (typeof Worker !== 'undefined' && isBrowser) ||\n      (typeof NodeWorker !== 'undefined' && !isBrowser)\n    );\n  }\n\n  constructor(props: WorkerThreadProps) {\n    const {name, source, url} = props;\n    assert(source || url); // Either source or url must be defined\n    this.name = name;\n    this.source = source;\n    this.url = url;\n    this.onMessage = NOOP;\n    this.onError = (error) => console.log(error); // eslint-disable-line\n\n    this.worker = isBrowser ? this._createBrowserWorker() : this._createNodeWorker();\n  }\n\n  /**\n   * Terminate this worker thread\n   * @note Can free up significant memory\n   */\n  destroy(): void {\n    this.onMessage = NOOP;\n    this.onError = NOOP;\n    this.worker.terminate(); // eslint-disable-line @typescript-eslint/no-floating-promises\n    this.terminated = true;\n  }\n\n  get isRunning() {\n    return Boolean(this.onMessage);\n  }\n\n  /**\n   * Send a message to this worker thread\n   * @param data any data structure, ideally consisting mostly of transferrable objects\n   * @param transferList If not supplied, calculated automatically by traversing data\n   */\n  postMessage(data: any, transferList?: any[]): void {\n    transferList = transferList || getTransferList(data);\n    // @ts-ignore\n    this.worker.postMessage(data, transferList);\n  }\n\n  // PRIVATE\n\n  /**\n   * Generate a standard Error from an ErrorEvent\n   * @param event\n   */\n  _getErrorFromErrorEvent(event: ErrorEvent): Error {\n    // Note Error object does not have the expected fields if loading failed completely\n    // https://developer.mozilla.org/en-US/docs/Web/API/Worker#Event_handlers\n    // https://developer.mozilla.org/en-US/docs/Web/API/ErrorEvent\n    let message = 'Failed to load ';\n    message += `worker ${this.name} from ${this.url}. `;\n    if (event.message) {\n      message += `${event.message} in `;\n    }\n    // const hasFilename = event.filename && !event.filename.startsWith('blob:');\n    // message += hasFilename ? event.filename : this.source.slice(0, 100);\n    if (event.lineno) {\n      message += `:${event.lineno}:${event.colno}`;\n    }\n    return new Error(message);\n  }\n\n  /**\n   * Creates a worker thread on the browser\n   */\n  _createBrowserWorker(): Worker {\n    this._loadableURL = getLoadableWorkerURL({source: this.source, url: this.url});\n    const worker = new Worker(this._loadableURL, {name: this.name});\n\n    worker.onmessage = (event) => {\n      if (!event.data) {\n        this.onError(new Error('No data received'));\n      } else {\n        this.onMessage(event.data);\n      }\n    };\n    // This callback represents an uncaught exception in the worker thread\n    worker.onerror = (error: ErrorEvent): void => {\n      this.onError(this._getErrorFromErrorEvent(error));\n      this.terminated = true;\n    };\n    // TODO - not clear when this would be called, for now just log in case it happens\n    worker.onmessageerror = (event) => console.error(event); // eslint-disable-line\n\n    return worker;\n  }\n\n  /**\n   * Creates a worker thread in node.js\n   * @todo https://nodejs.org/api/async_hooks.html#async-resource-worker-pool\n   */\n  _createNodeWorker(): NodeWorkerType {\n    let worker: NodeWorkerType;\n    if (this.url) {\n      // Make sure relative URLs start with './'\n      const absolute = this.url.includes(':/') || this.url.startsWith('/');\n      const url = absolute ? this.url : `./${this.url}`;\n      // console.log('Starting work from', url);\n      worker = new NodeWorker(url, {eval: false});\n    } else if (this.source) {\n      worker = new NodeWorker(this.source, {eval: true});\n    } else {\n      throw new Error('no worker');\n    }\n    worker.on('message', (data) => {\n      // console.error('message', data);\n      this.onMessage(data);\n    });\n    worker.on('error', (error) => {\n      // console.error('error', error);\n      this.onError(error);\n    });\n    worker.on('exit', (code) => {\n      // console.error('exit', code);\n    });\n    return worker;\n  }\n}\n"], "mappings": ";AAEA,SAAQA,UAAU,QAAuB,wBAAwB;AACjE,SAAQC,SAAS,QAAO,sBAAsB;AAC9C,SAAQC,MAAM,QAAO,qBAAqB;AAC1C,SAAQC,oBAAoB,QAAO,yCAAyC;AAC5E,SAAQC,eAAe,QAAO,mCAAmC;AAEjE,MAAMC,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AAWrB,eAAe,MAAMC,YAAY,CAAC;EAYhC,OAAOC,WAAWA,CAAA,EAAY;IAC5B,OACG,OAAOC,MAAM,KAAK,WAAW,IAAIP,SAAS,IAC1C,OAAOD,UAAU,KAAK,WAAW,IAAI,CAACC,SAAU;EAErD;EAEAQ,WAAWA,CAACC,KAAwB,EAAE;IAAAC,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,qBAfhB,KAAK;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,uBAKI,EAAE;IAW/B,MAAM;MAACC,IAAI;MAAEC,MAAM;MAAEC;IAAG,CAAC,GAAGJ,KAAK;IACjCR,MAAM,CAACW,MAAM,IAAIC,GAAG,CAAC;IACrB,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,SAAS,GAAGV,IAAI;IACrB,IAAI,CAACW,OAAO,GAAIC,KAAK,IAAKC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAE5C,IAAI,CAACG,MAAM,GAAGnB,SAAS,GAAG,IAAI,CAACoB,oBAAoB,CAAC,CAAC,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAClF;EAMAC,OAAOA,CAAA,EAAS;IACd,IAAI,CAACR,SAAS,GAAGV,IAAI;IACrB,IAAI,CAACW,OAAO,GAAGX,IAAI;IACnB,IAAI,CAACe,MAAM,CAACI,SAAS,CAAC,CAAC;IACvB,IAAI,CAACC,UAAU,GAAG,IAAI;EACxB;EAEA,IAAIC,SAASA,CAAA,EAAG;IACd,OAAOC,OAAO,CAAC,IAAI,CAACZ,SAAS,CAAC;EAChC;EAOAa,WAAWA,CAACC,IAAS,EAAEC,YAAoB,EAAQ;IACjDA,YAAY,GAAGA,YAAY,IAAI1B,eAAe,CAACyB,IAAI,CAAC;IAEpD,IAAI,CAACT,MAAM,CAACQ,WAAW,CAACC,IAAI,EAAEC,YAAY,CAAC;EAC7C;EAQAC,uBAAuBA,CAACC,KAAiB,EAAS;IAIhD,IAAIC,OAAO,GAAG,iBAAiB;IAC/BA,OAAO,cAAAC,MAAA,CAAc,IAAI,CAACtB,IAAI,YAAAsB,MAAA,CAAS,IAAI,CAACpB,GAAG,OAAI;IACnD,IAAIkB,KAAK,CAACC,OAAO,EAAE;MACjBA,OAAO,OAAAC,MAAA,CAAOF,KAAK,CAACC,OAAO,SAAM;IACnC;IAGA,IAAID,KAAK,CAACG,MAAM,EAAE;MAChBF,OAAO,QAAAC,MAAA,CAAQF,KAAK,CAACG,MAAM,OAAAD,MAAA,CAAIF,KAAK,CAACI,KAAK,CAAE;IAC9C;IACA,OAAO,IAAIC,KAAK,CAACJ,OAAO,CAAC;EAC3B;EAKAZ,oBAAoBA,CAAA,EAAW;IAC7B,IAAI,CAACiB,YAAY,GAAGnC,oBAAoB,CAAC;MAACU,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEC,GAAG,EAAE,IAAI,CAACA;IAAG,CAAC,CAAC;IAC9E,MAAMM,MAAM,GAAG,IAAIZ,MAAM,CAAC,IAAI,CAAC8B,YAAY,EAAE;MAAC1B,IAAI,EAAE,IAAI,CAACA;IAAI,CAAC,CAAC;IAE/DQ,MAAM,CAACmB,SAAS,GAAIP,KAAK,IAAK;MAC5B,IAAI,CAACA,KAAK,CAACH,IAAI,EAAE;QACf,IAAI,CAACb,OAAO,CAAC,IAAIqB,KAAK,CAAC,kBAAkB,CAAC,CAAC;MAC7C,CAAC,MAAM;QACL,IAAI,CAACtB,SAAS,CAACiB,KAAK,CAACH,IAAI,CAAC;MAC5B;IACF,CAAC;IAEDT,MAAM,CAACoB,OAAO,GAAIvB,KAAiB,IAAW;MAC5C,IAAI,CAACD,OAAO,CAAC,IAAI,CAACe,uBAAuB,CAACd,KAAK,CAAC,CAAC;MACjD,IAAI,CAACQ,UAAU,GAAG,IAAI;IACxB,CAAC;IAEDL,MAAM,CAACqB,cAAc,GAAIT,KAAK,IAAKd,OAAO,CAACD,KAAK,CAACe,KAAK,CAAC;IAEvD,OAAOZ,MAAM;EACf;EAMAE,iBAAiBA,CAAA,EAAmB;IAClC,IAAIF,MAAsB;IAC1B,IAAI,IAAI,CAACN,GAAG,EAAE;MAEZ,MAAM4B,QAAQ,GAAG,IAAI,CAAC5B,GAAG,CAAC6B,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC7B,GAAG,CAAC8B,UAAU,CAAC,GAAG,CAAC;MACpE,MAAM9B,GAAG,GAAG4B,QAAQ,GAAG,IAAI,CAAC5B,GAAG,QAAAoB,MAAA,CAAQ,IAAI,CAACpB,GAAG,CAAE;MAEjDM,MAAM,GAAG,IAAIpB,UAAU,CAACc,GAAG,EAAE;QAAC+B,IAAI,EAAE;MAAK,CAAC,CAAC;IAC7C,CAAC,MAAM,IAAI,IAAI,CAAChC,MAAM,EAAE;MACtBO,MAAM,GAAG,IAAIpB,UAAU,CAAC,IAAI,CAACa,MAAM,EAAE;QAACgC,IAAI,EAAE;MAAI,CAAC,CAAC;IACpD,CAAC,MAAM;MACL,MAAM,IAAIR,KAAK,CAAC,WAAW,CAAC;IAC9B;IACAjB,MAAM,CAAC0B,EAAE,CAAC,SAAS,EAAGjB,IAAI,IAAK;MAE7B,IAAI,CAACd,SAAS,CAACc,IAAI,CAAC;IACtB,CAAC,CAAC;IACFT,MAAM,CAAC0B,EAAE,CAAC,OAAO,EAAG7B,KAAK,IAAK;MAE5B,IAAI,CAACD,OAAO,CAACC,KAAK,CAAC;IACrB,CAAC,CAAC;IACFG,MAAM,CAAC0B,EAAE,CAAC,MAAM,EAAGC,IAAI,IAAK,CAE5B,CAAC,CAAC;IACF,OAAO3B,MAAM;EACf;AACF"}