{"version": 3, "file": "worker-body.js", "names": ["getTransferList", "getParentPort", "parentPort", "eval", "globalThis", "onMessageWrapperMap", "Map", "WorkerBody", "inWorkerThread", "self", "Boolean", "onmessage", "onMessage", "handleMessage", "message", "type", "payload", "data", "on", "console", "debug", "addEventListener", "onMessageWrapper", "get", "isKnownMessage", "error", "removeEventListener", "delete", "postMessage", "source", "transferList", "startsWith"], "sources": ["../../../../src/lib/worker-farm/worker-body.ts"], "sourcesContent": ["import type {WorkerMessageData, WorkerMessageType, WorkerMessagePayload} from '../../types';\nimport {getTransferList} from '../worker-utils/get-transfer-list';\n\n/** Vile hack to defeat over-zealous bundlers from stripping out the require */\nfunction getParentPort() {\n  // const isNode = globalThis.process;\n  let parentPort;\n  try {\n    // prettier-ignore\n    eval('globalThis.parentPort = require(\\'worker_threads\\').parentPort'); // eslint-disable-line no-eval\n    parentPort = globalThis.parentPort;\n    // eslint-disable-next-line no-empty\n  } catch {}\n  return parentPort;\n}\n\nconst onMessageWrapperMap = new Map();\n\n/**\n * Type safe wrapper for worker code\n */\nexport default class WorkerBody {\n  /** Check that we are actually in a worker thread */\n  static inWorkerThread(): boolean {\n    return typeof self !== 'undefined' || Boolean(getParentPort());\n  }\n\n  /*\n   * (type: WorkerMessageType, payload: WorkerMessagePayload) => any\n   */\n  static set onmessage(onMessage: (type: WorkerMessageType, payload: WorkerMessagePayload) => any) {\n    function handleMessage(message) {\n      // Confusingly the message itself also has a 'type' field which is always set to 'message'\n      const parentPort = getParentPort();\n      const {type, payload} = parentPort ? message : message.data;\n      // if (!isKnownMessage(message)) {\n      //   return;\n      // }\n      onMessage(type, payload);\n    }\n\n    const parentPort = getParentPort();\n    if (parentPort) {\n      parentPort.on('message', handleMessage);\n      // if (message == 'exit') { parentPort.unref(); }\n      // eslint-disable-next-line\n      parentPort.on('exit', () => console.debug('Node worker closing'));\n    } else {\n      // eslint-disable-next-line no-restricted-globals\n      globalThis.onmessage = handleMessage;\n    }\n  }\n\n  static addEventListener(\n    onMessage: (type: WorkerMessageType, payload: WorkerMessagePayload) => any\n  ) {\n    let onMessageWrapper = onMessageWrapperMap.get(onMessage);\n\n    if (!onMessageWrapper) {\n      onMessageWrapper = (message: MessageEvent<any>) => {\n        if (!isKnownMessage(message)) {\n          return;\n        }\n\n        // Confusingly in the browser, the message itself also has a 'type' field which is always set to 'message'\n        const parentPort = getParentPort();\n        const {type, payload} = parentPort ? message : message.data;\n        onMessage(type, payload);\n      };\n    }\n\n    const parentPort = getParentPort();\n    if (parentPort) {\n      console.error('not implemented'); // eslint-disable-line\n    } else {\n      globalThis.addEventListener('message', onMessageWrapper);\n    }\n  }\n\n  static removeEventListener(\n    onMessage: (type: WorkerMessageType, payload: WorkerMessagePayload) => any\n  ) {\n    const onMessageWrapper = onMessageWrapperMap.get(onMessage);\n    onMessageWrapperMap.delete(onMessage);\n    const parentPort = getParentPort();\n    if (parentPort) {\n      console.error('not implemented'); // eslint-disable-line\n    } else {\n      globalThis.removeEventListener('message', onMessageWrapper);\n    }\n  }\n\n  /**\n   * Send a message from a worker to creating thread (main thread)\n   * @param type\n   * @param payload\n   */\n  static postMessage(type: WorkerMessageType, payload: WorkerMessagePayload): void {\n    const data: WorkerMessageData = {source: 'loaders.gl', type, payload};\n    // console.log('posting message', data);\n    const transferList = getTransferList(payload);\n\n    const parentPort = getParentPort();\n    if (parentPort) {\n      parentPort.postMessage(data, transferList);\n      // console.log('posted message', data);\n    } else {\n      // @ts-ignore\n      globalThis.postMessage(data, transferList);\n    }\n  }\n}\n\n// Filter out noise messages sent to workers\nfunction isKnownMessage(message: MessageEvent<any>) {\n  const {type, data} = message;\n  return (\n    type === 'message' &&\n    data &&\n    typeof data.source === 'string' &&\n    data.source.startsWith('loaders.gl')\n  );\n}\n"], "mappings": "AACA,SAAQA,eAAe,QAAO,mCAAmC;AAGjE,SAASC,aAAaA,CAAA,EAAG;EAEvB,IAAIC,UAAU;EACd,IAAI;IAEFC,IAAI,CAAC,gEAAgE,CAAC;IACtED,UAAU,GAAGE,UAAU,CAACF,UAAU;EAEpC,CAAC,CAAC,MAAM,CAAC;EACT,OAAOA,UAAU;AACnB;AAEA,MAAMG,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC;AAKrC,eAAe,MAAMC,UAAU,CAAC;EAE9B,OAAOC,cAAcA,CAAA,EAAY;IAC/B,OAAO,OAAOC,IAAI,KAAK,WAAW,IAAIC,OAAO,CAACT,aAAa,CAAC,CAAC,CAAC;EAChE;EAKA,WAAWU,SAASA,CAACC,SAA0E,EAAE;IAC/F,SAASC,aAAaA,CAACC,OAAO,EAAE;MAE9B,MAAMZ,UAAU,GAAGD,aAAa,CAAC,CAAC;MAClC,MAAM;QAACc,IAAI;QAAEC;MAAO,CAAC,GAAGd,UAAU,GAAGY,OAAO,GAAGA,OAAO,CAACG,IAAI;MAI3DL,SAAS,CAACG,IAAI,EAAEC,OAAO,CAAC;IAC1B;IAEA,MAAMd,UAAU,GAAGD,aAAa,CAAC,CAAC;IAClC,IAAIC,UAAU,EAAE;MACdA,UAAU,CAACgB,EAAE,CAAC,SAAS,EAAEL,aAAa,CAAC;MAGvCX,UAAU,CAACgB,EAAE,CAAC,MAAM,EAAE,MAAMC,OAAO,CAACC,KAAK,CAAC,qBAAqB,CAAC,CAAC;IACnE,CAAC,MAAM;MAELhB,UAAU,CAACO,SAAS,GAAGE,aAAa;IACtC;EACF;EAEA,OAAOQ,gBAAgBA,CACrBT,SAA0E,EAC1E;IACA,IAAIU,gBAAgB,GAAGjB,mBAAmB,CAACkB,GAAG,CAACX,SAAS,CAAC;IAEzD,IAAI,CAACU,gBAAgB,EAAE;MACrBA,gBAAgB,GAAIR,OAA0B,IAAK;QACjD,IAAI,CAACU,cAAc,CAACV,OAAO,CAAC,EAAE;UAC5B;QACF;QAGA,MAAMZ,UAAU,GAAGD,aAAa,CAAC,CAAC;QAClC,MAAM;UAACc,IAAI;UAAEC;QAAO,CAAC,GAAGd,UAAU,GAAGY,OAAO,GAAGA,OAAO,CAACG,IAAI;QAC3DL,SAAS,CAACG,IAAI,EAAEC,OAAO,CAAC;MAC1B,CAAC;IACH;IAEA,MAAMd,UAAU,GAAGD,aAAa,CAAC,CAAC;IAClC,IAAIC,UAAU,EAAE;MACdiB,OAAO,CAACM,KAAK,CAAC,iBAAiB,CAAC;IAClC,CAAC,MAAM;MACLrB,UAAU,CAACiB,gBAAgB,CAAC,SAAS,EAAEC,gBAAgB,CAAC;IAC1D;EACF;EAEA,OAAOI,mBAAmBA,CACxBd,SAA0E,EAC1E;IACA,MAAMU,gBAAgB,GAAGjB,mBAAmB,CAACkB,GAAG,CAACX,SAAS,CAAC;IAC3DP,mBAAmB,CAACsB,MAAM,CAACf,SAAS,CAAC;IACrC,MAAMV,UAAU,GAAGD,aAAa,CAAC,CAAC;IAClC,IAAIC,UAAU,EAAE;MACdiB,OAAO,CAACM,KAAK,CAAC,iBAAiB,CAAC;IAClC,CAAC,MAAM;MACLrB,UAAU,CAACsB,mBAAmB,CAAC,SAAS,EAAEJ,gBAAgB,CAAC;IAC7D;EACF;EAOA,OAAOM,WAAWA,CAACb,IAAuB,EAAEC,OAA6B,EAAQ;IAC/E,MAAMC,IAAuB,GAAG;MAACY,MAAM,EAAE,YAAY;MAAEd,IAAI;MAAEC;IAAO,CAAC;IAErE,MAAMc,YAAY,GAAG9B,eAAe,CAACgB,OAAO,CAAC;IAE7C,MAAMd,UAAU,GAAGD,aAAa,CAAC,CAAC;IAClC,IAAIC,UAAU,EAAE;MACdA,UAAU,CAAC0B,WAAW,CAACX,IAAI,EAAEa,YAAY,CAAC;IAE5C,CAAC,MAAM;MAEL1B,UAAU,CAACwB,WAAW,CAACX,IAAI,EAAEa,YAAY,CAAC;IAC5C;EACF;AACF;AAGA,SAASN,cAAcA,CAACV,OAA0B,EAAE;EAClD,MAAM;IAACC,IAAI;IAAEE;EAAI,CAAC,GAAGH,OAAO;EAC5B,OACEC,IAAI,KAAK,SAAS,IAClBE,IAAI,IACJ,OAAOA,IAAI,CAACY,MAAM,KAAK,QAAQ,IAC/BZ,IAAI,CAACY,MAAM,CAACE,UAAU,CAAC,YAAY,CAAC;AAExC"}