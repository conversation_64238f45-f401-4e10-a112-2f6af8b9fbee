{"version": 3, "file": "worker-pool.js", "names": ["isMobile", "WorkerThread", "<PERSON><PERSON><PERSON>", "WorkerPool", "isSupported", "constructor", "props", "_defineProperty", "source", "url", "setProps", "destroy", "idleQueue", "for<PERSON>ach", "worker", "isDestroyed", "name", "undefined", "maxConcurrency", "maxMobileConcurrency", "reuseWorkers", "onDebug", "startJob", "onMessage", "arguments", "length", "job", "type", "data", "done", "onError", "error", "startPromise", "Promise", "onStart", "jobQueue", "push", "_startQ<PERSON><PERSON><PERSON><PERSON>", "workerThread", "_getAvailableWorker", "queuedJob", "shift", "message", "backlog", "payload", "result", "returnWorkerToQueue", "shouldDestroyWorker", "count", "_getMaxConcurrency", "concat", "toLowerCase"], "sources": ["../../../../src/lib/worker-farm/worker-pool.ts"], "sourcesContent": ["import type {WorkerMessageType, WorkerMessagePayload} from '../../types';\nimport {isMobile} from '../env-utils/globals';\nimport WorkerThread from './worker-thread';\nimport WorkerJob from './worker-job';\n\n/** WorkerPool onDebug Callback Parameters */\ntype OnDebugParameters = {\n  message: string;\n  worker: string;\n  name: string;\n  job: string;\n  backlog: number;\n  workerThread: WorkerThread;\n};\n\n/** WorkerPool Properties */\nexport type WorkerPoolProps = {\n  name?: string;\n  source?: string; // | Function;\n  url?: string;\n  maxConcurrency?: number;\n  maxMobileConcurrency?: number;\n  onDebug?: (options: OnDebugParameters) => any;\n  reuseWorkers?: boolean;\n};\n\n/** Private helper types */\ntype OnMessage = (job: WorkerJob, type: WorkerMessageType, payload: WorkerMessagePayload) => void;\ntype OnError = (job: WorkerJob, error: Error) => void;\n\ntype QueuedJob = {\n  name: string;\n  onMessage: OnMessage;\n  onError: OnError;\n  onStart: (value: any) => void; // Resolve job start promise\n};\n\n/**\n * Process multiple data messages with small pool of identical workers\n */\nexport default class WorkerPool {\n  name: string = 'unnamed';\n  source?: string; // | Function;\n  url?: string;\n  maxConcurrency: number = 1;\n  maxMobileConcurrency: number = 1;\n  onDebug: (options: OnDebugParameters) => any = () => {};\n  reuseWorkers: boolean = true;\n\n  private props: WorkerPoolProps = {};\n  private jobQueue: QueuedJob[] = [];\n  private idleQueue: WorkerThread[] = [];\n  private count = 0;\n  private isDestroyed = false;\n\n  /** Checks if workers are supported on this platform */\n  static isSupported(): boolean {\n    return WorkerThread.isSupported();\n  }\n\n  /**\n   * @param processor - worker function\n   * @param maxConcurrency - max count of workers\n   */\n  constructor(props: WorkerPoolProps) {\n    this.source = props.source;\n    this.url = props.url;\n    this.setProps(props);\n  }\n\n  /**\n   * Terminates all workers in the pool\n   * @note Can free up significant memory\n   */\n  destroy(): void {\n    // Destroy idle workers, active Workers will be destroyed on completion\n    this.idleQueue.forEach((worker) => worker.destroy());\n    this.isDestroyed = true;\n  }\n\n  setProps(props: WorkerPoolProps) {\n    this.props = {...this.props, ...props};\n\n    if (props.name !== undefined) {\n      this.name = props.name;\n    }\n    if (props.maxConcurrency !== undefined) {\n      this.maxConcurrency = props.maxConcurrency;\n    }\n    if (props.maxMobileConcurrency !== undefined) {\n      this.maxMobileConcurrency = props.maxMobileConcurrency;\n    }\n    if (props.reuseWorkers !== undefined) {\n      this.reuseWorkers = props.reuseWorkers;\n    }\n    if (props.onDebug !== undefined) {\n      this.onDebug = props.onDebug;\n    }\n  }\n\n  async startJob(\n    name: string,\n    onMessage: OnMessage = (job, type, data) => job.done(data),\n    onError: OnError = (job, error) => job.error(error)\n  ): Promise<WorkerJob> {\n    // Promise resolves when thread starts working on this job\n    const startPromise = new Promise<WorkerJob>((onStart) => {\n      // Promise resolves when thread completes or fails working on this job\n      this.jobQueue.push({name, onMessage, onError, onStart});\n      return this;\n    });\n    this._startQueuedJob(); // eslint-disable-line @typescript-eslint/no-floating-promises\n    return await startPromise;\n  }\n\n  // PRIVATE\n\n  /**\n   * Starts first queued job if worker is available or can be created\n   * Called when job is started and whenever a worker returns to the idleQueue\n   */\n  async _startQueuedJob(): Promise<void> {\n    if (!this.jobQueue.length) {\n      return;\n    }\n\n    const workerThread = this._getAvailableWorker();\n    if (!workerThread) {\n      return;\n    }\n\n    // We have a worker, dequeue and start the job\n    const queuedJob = this.jobQueue.shift();\n    if (queuedJob) {\n      // Emit a debug event\n      // @ts-ignore\n      this.onDebug({\n        message: 'Starting job',\n        name: queuedJob.name,\n        workerThread,\n        backlog: this.jobQueue.length\n      });\n\n      // Create a worker job to let the app access thread and manage job completion\n      const job = new WorkerJob(queuedJob.name, workerThread);\n\n      // Set the worker thread's message handlers\n      workerThread.onMessage = (data) => queuedJob.onMessage(job, data.type, data.payload);\n      workerThread.onError = (error) => queuedJob.onError(job, error);\n\n      // Resolve the start promise so that the app can start sending messages to worker\n      queuedJob.onStart(job);\n\n      // Wait for the app to signal that the job is complete, then return worker to queue\n      try {\n        await job.result;\n      } finally {\n        this.returnWorkerToQueue(workerThread);\n      }\n    }\n  }\n\n  /**\n   * Returns a worker to the idle queue\n   * Destroys the worker if\n   *  - pool is destroyed\n   *  - if this pool doesn't reuse workers\n   *  - if maxConcurrency has been lowered\n   * @param worker\n   */\n  returnWorkerToQueue(worker: WorkerThread) {\n    const shouldDestroyWorker =\n      this.isDestroyed || !this.reuseWorkers || this.count > this._getMaxConcurrency();\n\n    if (shouldDestroyWorker) {\n      worker.destroy();\n      this.count--;\n    } else {\n      this.idleQueue.push(worker);\n    }\n\n    if (!this.isDestroyed) {\n      this._startQueuedJob(); // eslint-disable-line @typescript-eslint/no-floating-promises\n    }\n  }\n\n  /**\n   * Returns idle worker or creates new worker if maxConcurrency has not been reached\n   */\n  _getAvailableWorker(): WorkerThread | null {\n    // If a worker has completed and returned to the queue, it can be used\n    if (this.idleQueue.length > 0) {\n      return this.idleQueue.shift() || null;\n    }\n\n    // Create fresh worker if we haven't yet created the max amount of worker threads for this worker source\n    if (this.count < this._getMaxConcurrency()) {\n      this.count++;\n      const name = `${this.name.toLowerCase()} (#${this.count} of ${this.maxConcurrency})`;\n      return new WorkerThread({name, source: this.source, url: this.url});\n    }\n\n    // No worker available, have to wait\n    return null;\n  }\n\n  _getMaxConcurrency() {\n    return isMobile ? this.maxMobileConcurrency : this.maxConcurrency;\n  }\n}\n"], "mappings": ";AACA,SAAQA,QAAQ,QAAO,sBAAsB;AAC7C,OAAOC,YAAY,MAAM,iBAAiB;AAC1C,OAAOC,SAAS,MAAM,cAAc;AAqCpC,eAAe,MAAMC,UAAU,CAAC;EAgB9B,OAAOC,WAAWA,CAAA,EAAY;IAC5B,OAAOH,YAAY,CAACG,WAAW,CAAC,CAAC;EACnC;EAMAC,WAAWA,CAACC,KAAsB,EAAE;IAAAC,eAAA,eAvBrB,SAAS;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,yBAGC,CAAC;IAAAA,eAAA,+BACK,CAAC;IAAAA,eAAA,kBACe,MAAM,CAAC,CAAC;IAAAA,eAAA,uBAC/B,IAAI;IAAAA,eAAA,gBAEK,CAAC,CAAC;IAAAA,eAAA,mBACH,EAAE;IAAAA,eAAA,oBACE,EAAE;IAAAA,eAAA,gBACtB,CAAC;IAAAA,eAAA,sBACK,KAAK;IAYzB,IAAI,CAACC,MAAM,GAAGF,KAAK,CAACE,MAAM;IAC1B,IAAI,CAACC,GAAG,GAAGH,KAAK,CAACG,GAAG;IACpB,IAAI,CAACC,QAAQ,CAACJ,KAAK,CAAC;EACtB;EAMAK,OAAOA,CAAA,EAAS;IAEd,IAAI,CAACC,SAAS,CAACC,OAAO,CAAEC,MAAM,IAAKA,MAAM,CAACH,OAAO,CAAC,CAAC,CAAC;IACpD,IAAI,CAACI,WAAW,GAAG,IAAI;EACzB;EAEAL,QAAQA,CAACJ,KAAsB,EAAE;IAC/B,IAAI,CAACA,KAAK,GAAG;MAAC,GAAG,IAAI,CAACA,KAAK;MAAE,GAAGA;IAAK,CAAC;IAEtC,IAAIA,KAAK,CAACU,IAAI,KAAKC,SAAS,EAAE;MAC5B,IAAI,CAACD,IAAI,GAAGV,KAAK,CAACU,IAAI;IACxB;IACA,IAAIV,KAAK,CAACY,cAAc,KAAKD,SAAS,EAAE;MACtC,IAAI,CAACC,cAAc,GAAGZ,KAAK,CAACY,cAAc;IAC5C;IACA,IAAIZ,KAAK,CAACa,oBAAoB,KAAKF,SAAS,EAAE;MAC5C,IAAI,CAACE,oBAAoB,GAAGb,KAAK,CAACa,oBAAoB;IACxD;IACA,IAAIb,KAAK,CAACc,YAAY,KAAKH,SAAS,EAAE;MACpC,IAAI,CAACG,YAAY,GAAGd,KAAK,CAACc,YAAY;IACxC;IACA,IAAId,KAAK,CAACe,OAAO,KAAKJ,SAAS,EAAE;MAC/B,IAAI,CAACI,OAAO,GAAGf,KAAK,CAACe,OAAO;IAC9B;EACF;EAEA,MAAMC,QAAQA,CACZN,IAAY,EAGQ;IAAA,IAFpBO,SAAoB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAP,SAAA,GAAAO,SAAA,MAAG,CAACE,GAAG,EAAEC,IAAI,EAAEC,IAAI,KAAKF,GAAG,CAACG,IAAI,CAACD,IAAI,CAAC;IAAA,IAC1DE,OAAgB,GAAAN,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAP,SAAA,GAAAO,SAAA,MAAG,CAACE,GAAG,EAAEK,KAAK,KAAKL,GAAG,CAACK,KAAK,CAACA,KAAK,CAAC;IAGnD,MAAMC,YAAY,GAAG,IAAIC,OAAO,CAAaC,OAAO,IAAK;MAEvD,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC;QAACpB,IAAI;QAAEO,SAAS;QAAEO,OAAO;QAAEI;MAAO,CAAC,CAAC;MACvD,OAAO,IAAI;IACb,CAAC,CAAC;IACF,IAAI,CAACG,eAAe,CAAC,CAAC;IACtB,OAAO,MAAML,YAAY;EAC3B;EAQA,MAAMK,eAAeA,CAAA,EAAkB;IACrC,IAAI,CAAC,IAAI,CAACF,QAAQ,CAACV,MAAM,EAAE;MACzB;IACF;IAEA,MAAMa,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC/C,IAAI,CAACD,YAAY,EAAE;MACjB;IACF;IAGA,MAAME,SAAS,GAAG,IAAI,CAACL,QAAQ,CAACM,KAAK,CAAC,CAAC;IACvC,IAAID,SAAS,EAAE;MAGb,IAAI,CAACnB,OAAO,CAAC;QACXqB,OAAO,EAAE,cAAc;QACvB1B,IAAI,EAAEwB,SAAS,CAACxB,IAAI;QACpBsB,YAAY;QACZK,OAAO,EAAE,IAAI,CAACR,QAAQ,CAACV;MACzB,CAAC,CAAC;MAGF,MAAMC,GAAG,GAAG,IAAIxB,SAAS,CAACsC,SAAS,CAACxB,IAAI,EAAEsB,YAAY,CAAC;MAGvDA,YAAY,CAACf,SAAS,GAAIK,IAAI,IAAKY,SAAS,CAACjB,SAAS,CAACG,GAAG,EAAEE,IAAI,CAACD,IAAI,EAAEC,IAAI,CAACgB,OAAO,CAAC;MACpFN,YAAY,CAACR,OAAO,GAAIC,KAAK,IAAKS,SAAS,CAACV,OAAO,CAACJ,GAAG,EAAEK,KAAK,CAAC;MAG/DS,SAAS,CAACN,OAAO,CAACR,GAAG,CAAC;MAGtB,IAAI;QACF,MAAMA,GAAG,CAACmB,MAAM;MAClB,CAAC,SAAS;QACR,IAAI,CAACC,mBAAmB,CAACR,YAAY,CAAC;MACxC;IACF;EACF;EAUAQ,mBAAmBA,CAAChC,MAAoB,EAAE;IACxC,MAAMiC,mBAAmB,GACvB,IAAI,CAAChC,WAAW,IAAI,CAAC,IAAI,CAACK,YAAY,IAAI,IAAI,CAAC4B,KAAK,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAElF,IAAIF,mBAAmB,EAAE;MACvBjC,MAAM,CAACH,OAAO,CAAC,CAAC;MAChB,IAAI,CAACqC,KAAK,EAAE;IACd,CAAC,MAAM;MACL,IAAI,CAACpC,SAAS,CAACwB,IAAI,CAACtB,MAAM,CAAC;IAC7B;IAEA,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE;MACrB,IAAI,CAACsB,eAAe,CAAC,CAAC;IACxB;EACF;EAKAE,mBAAmBA,CAAA,EAAwB;IAEzC,IAAI,IAAI,CAAC3B,SAAS,CAACa,MAAM,GAAG,CAAC,EAAE;MAC7B,OAAO,IAAI,CAACb,SAAS,CAAC6B,KAAK,CAAC,CAAC,IAAI,IAAI;IACvC;IAGA,IAAI,IAAI,CAACO,KAAK,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC,EAAE;MAC1C,IAAI,CAACD,KAAK,EAAE;MACZ,MAAMhC,IAAI,MAAAkC,MAAA,CAAM,IAAI,CAAClC,IAAI,CAACmC,WAAW,CAAC,CAAC,SAAAD,MAAA,CAAM,IAAI,CAACF,KAAK,UAAAE,MAAA,CAAO,IAAI,CAAChC,cAAc,MAAG;MACpF,OAAO,IAAIjB,YAAY,CAAC;QAACe,IAAI;QAAER,MAAM,EAAE,IAAI,CAACA,MAAM;QAAEC,GAAG,EAAE,IAAI,CAACA;MAAG,CAAC,CAAC;IACrE;IAGA,OAAO,IAAI;EACb;EAEAwC,kBAAkBA,CAAA,EAAG;IACnB,OAAOjD,QAAQ,GAAG,IAAI,CAACmB,oBAAoB,GAAG,IAAI,CAACD,cAAc;EACnE;AACF"}