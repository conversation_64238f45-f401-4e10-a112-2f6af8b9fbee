{"version": 3, "file": "globals.js", "names": ["globals", "self", "window", "global", "document", "self_", "window_", "global_", "document_", "<PERSON><PERSON><PERSON><PERSON>", "process", "String", "browser", "isWorker", "importScripts", "isMobile", "orientation", "matches", "version", "exec", "nodeVersion", "parseFloat"], "sources": ["../../../../src/lib/env-utils/globals.ts"], "sourcesContent": ["// Purpose: include this in your module to avoids adding dependencies on\n// micro modules like 'global' and 'is-browser';\n\n/* eslint-disable no-restricted-globals */\nconst globals = {\n  self: typeof self !== 'undefined' && self,\n  window: typeof window !== 'undefined' && window,\n  global: typeof global !== 'undefined' && global,\n  document: typeof document !== 'undefined' && document\n};\n\nconst self_: {[key: string]: any} = globals.self || globals.window || globals.global || {};\nconst window_: {[key: string]: any} = globals.window || globals.self || globals.global || {};\nconst global_: {[key: string]: any} = globals.global || globals.self || globals.window || {};\nconst document_: {[key: string]: any} = globals.document || {};\n\nexport {self_ as self, window_ as window, global_ as global, document_ as document};\n\n/** true if running in the browser, false if running in Node.js */\nexport const isBrowser: boolean =\n  // @ts-ignore process.browser\n  typeof process !== 'object' || String(process) !== '[object process]' || process.browser;\n\n/** true if running on a worker thread */\nexport const isWorker: boolean = typeof importScripts === 'function';\n\n/** true if running on a mobile device */\nexport const isMobile: boolean =\n  typeof window !== 'undefined' && typeof window.orientation !== 'undefined';\n\n// Extract node major version\nconst matches =\n  typeof process !== 'undefined' && process.version && /v([0-9]*)/.exec(process.version);\n\n/** Version of Node.js if running under Node, otherwise 0 */\nexport const nodeVersion: number = (matches && parseFloat(matches[1])) || 0;\n"], "mappings": "AAIA,MAAMA,OAAO,GAAG;EACdC,IAAI,EAAE,OAAOA,IAAI,KAAK,WAAW,IAAIA,IAAI;EACzCC,MAAM,EAAE,OAAOA,MAAM,KAAK,WAAW,IAAIA,MAAM;EAC/CC,MAAM,EAAE,OAAOA,MAAM,KAAK,WAAW,IAAIA,MAAM;EAC/CC,QAAQ,EAAE,OAAOA,QAAQ,KAAK,WAAW,IAAIA;AAC/C,CAAC;AAED,MAAMC,KAA2B,GAAGL,OAAO,CAACC,IAAI,IAAID,OAAO,CAACE,MAAM,IAAIF,OAAO,CAACG,MAAM,IAAI,CAAC,CAAC;AAC1F,MAAMG,OAA6B,GAAGN,OAAO,CAACE,MAAM,IAAIF,OAAO,CAACC,IAAI,IAAID,OAAO,CAACG,MAAM,IAAI,CAAC,CAAC;AAC5F,MAAMI,OAA6B,GAAGP,OAAO,CAACG,MAAM,IAAIH,OAAO,CAACC,IAAI,IAAID,OAAO,CAACE,MAAM,IAAI,CAAC,CAAC;AAC5F,MAAMM,SAA+B,GAAGR,OAAO,CAACI,QAAQ,IAAI,CAAC,CAAC;AAE9D,SAAQC,KAAK,IAAIJ,IAAI,EAAEK,OAAO,IAAIJ,MAAM,EAAEK,OAAO,IAAIJ,MAAM,EAAEK,SAAS,IAAIJ,QAAQ;AAGlF,OAAO,MAAMK,SAAkB,GAE7B,OAAOC,OAAO,KAAK,QAAQ,IAAIC,MAAM,CAACD,OAAO,CAAC,KAAK,kBAAkB,IAAIA,OAAO,CAACE,OAAO;AAG1F,OAAO,MAAMC,QAAiB,GAAG,OAAOC,aAAa,KAAK,UAAU;AAGpE,OAAO,MAAMC,QAAiB,GAC5B,OAAOb,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACc,WAAW,KAAK,WAAW;AAG5E,MAAMC,OAAO,GACX,OAAOP,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACQ,OAAO,IAAI,WAAW,CAACC,IAAI,CAACT,OAAO,CAACQ,OAAO,CAAC;AAGxF,OAAO,MAAME,WAAmB,GAAIH,OAAO,IAAII,UAAU,CAACJ,OAAO,CAAC,CAAC,CAAC,CAAC,IAAK,CAAC"}