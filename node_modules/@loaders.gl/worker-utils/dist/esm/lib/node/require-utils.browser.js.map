{"version": 3, "file": "require-utils.browser.js", "names": ["readFileAsArrayBuffer", "readFileAsText", "requireFromFile", "requireFromString"], "sources": ["../../../../src/lib/node/require-utils.browser.ts"], "sourcesContent": ["export const readFileAsArrayBuffer = null;\nexport const readFileAsText = null;\nexport const requireFromFile = null;\nexport const requireFromString = null;\n"], "mappings": "AAAA,OAAO,MAAMA,qBAAqB,GAAG,IAAI;AACzC,OAAO,MAAMC,cAAc,GAAG,IAAI;AAClC,OAAO,MAAMC,eAAe,GAAG,IAAI;AACnC,OAAO,MAAMC,iBAAiB,GAAG,IAAI"}