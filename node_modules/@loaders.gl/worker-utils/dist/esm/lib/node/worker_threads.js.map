{"version": 3, "file": "worker_threads.js", "names": ["WorkerThreads", "NodeWorker", "Worker"], "sources": ["../../../../src/lib/node/worker_threads.ts"], "sourcesContent": ["// loaders.gl, MIT license\n\nimport * as WorkerThreads from 'worker_threads';\nexport * from 'worker_threads';\nexport const NodeWorker = WorkerThreads.Worker;\nexport type NodeWorkerType = WorkerThreads.Worker;\n"], "mappings": "AAEA,OAAO,KAAKA,aAAa,MAAM,gBAAgB;AAC/C,cAAc,gBAAgB;AAC9B,OAAO,MAAMC,UAAU,GAAGD,aAAa,CAACE,MAAM"}