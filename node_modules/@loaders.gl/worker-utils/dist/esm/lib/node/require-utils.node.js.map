{"version": 3, "file": "require-utils.node.js", "names": ["<PERSON><PERSON><PERSON>", "path", "requireFromFile", "filename", "startsWith", "response", "fetch", "code", "text", "requireFromString", "concat", "process", "cwd", "require", "_options", "_options2", "arguments", "length", "undefined", "options", "Error", "paths", "_nodeModulePaths", "dirname", "parent", "module", "newModule", "prependPaths", "appendPaths", "_compile", "children", "splice", "indexOf", "exports"], "sources": ["../../../../src/lib/node/require-utils.node.ts"], "sourcesContent": ["// Fork of https://github.com/floatdrop/require-from-string/blob/master/index.js\n// Copyright (c) Vse<PERSON><PERSON><PERSON> <<EMAIL>> (github.com/floatdrop)\n// MIT license\n\n// this file is not visible to webpack (it is excluded in the package.json \"browser\" field).\n\nimport Module from 'module';\nimport path from 'path';\n\n// Node.js Dynamically require from file\n// Relative names are resolved relative to cwd\n// This indirect function is provided because webpack will try to bundle `module.require`.\n// this file is not visible to webpack (it is excluded in the package.json \"browser\" field).\nexport async function requireFromFile(filename: string): Promise<any> {\n  if (filename.startsWith('http')) {\n    const response = await fetch(filename);\n    const code = await response.text();\n    return requireFromString(code);\n  }\n\n  if (!filename.startsWith('/')) {\n    filename = `${process.cwd()}/${filename}`;\n  }\n  return require(filename);\n}\n\n// Dynamically require from string\n// - `code` - Required - Type: string - Module code.\n// - `filename` - Type: string - Default: '' - Optional filename.\n// - `options.appendPaths` Type: Array List of paths, that will be appended to module paths.\n// Useful, when you want to be able require modules from these paths.\n// - `options.prependPaths` Type: Array Same as appendPaths, but paths will be prepended.\nexport function requireFromString(\n  code: string,\n  filename = '',\n  options?: {\n    prependPaths?: string[];\n    appendPaths?: string[];\n  }\n): any {\n  if (typeof filename === 'object') {\n    options = filename;\n    filename = '';\n  }\n\n  if (typeof code !== 'string') {\n    throw new Error(`code must be a string, not ${typeof code}`);\n  }\n\n  // @ts-ignore\n  const paths = Module._nodeModulePaths(path.dirname(filename));\n\n  const parent = module.parent;\n  // @ts-ignore\n  const newModule = new Module(filename, parent);\n  newModule.filename = filename;\n  newModule.paths = ([] as string[])\n    .concat(options?.prependPaths || [])\n    .concat(paths)\n    .concat(options?.appendPaths || []);\n  // @ts-ignore\n  newModule._compile(code, filename);\n\n  if (parent && parent.children) {\n    parent.children.splice(parent.children.indexOf(newModule), 1);\n  }\n\n  return newModule.exports;\n}\n"], "mappings": "AAMA,OAAOA,MAAM,MAAM,QAAQ;AAC3B,OAAOC,IAAI,MAAM,MAAM;AAMvB,OAAO,eAAeC,eAAeA,CAACC,QAAgB,EAAgB;EACpE,IAAIA,QAAQ,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;IAC/B,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACH,QAAQ,CAAC;IACtC,MAAMI,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;IAClC,OAAOC,iBAAiB,CAACF,IAAI,CAAC;EAChC;EAEA,IAAI,CAACJ,QAAQ,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;IAC7BD,QAAQ,MAAAO,MAAA,CAAMC,OAAO,CAACC,GAAG,CAAC,CAAC,OAAAF,MAAA,CAAIP,QAAQ,CAAE;EAC3C;EACA,OAAOU,OAAO,CAACV,QAAQ,CAAC;AAC1B;AAQA,OAAO,SAASM,iBAAiBA,CAC/BF,IAAY,EAMP;EAAA,IAAAO,QAAA,EAAAC,SAAA;EAAA,IALLZ,QAAQ,GAAAa,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAAA,IACbG,OAGC,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAED,IAAI,OAAOf,QAAQ,KAAK,QAAQ,EAAE;IAChCgB,OAAO,GAAGhB,QAAQ;IAClBA,QAAQ,GAAG,EAAE;EACf;EAEA,IAAI,OAAOI,IAAI,KAAK,QAAQ,EAAE;IAC5B,MAAM,IAAIa,KAAK,+BAAAV,MAAA,CAA+B,OAAOH,IAAI,CAAE,CAAC;EAC9D;EAGA,MAAMc,KAAK,GAAGrB,MAAM,CAACsB,gBAAgB,CAACrB,IAAI,CAACsB,OAAO,CAACpB,QAAQ,CAAC,CAAC;EAE7D,MAAMqB,MAAM,GAAGC,MAAM,CAACD,MAAM;EAE5B,MAAME,SAAS,GAAG,IAAI1B,MAAM,CAACG,QAAQ,EAAEqB,MAAM,CAAC;EAC9CE,SAAS,CAACvB,QAAQ,GAAGA,QAAQ;EAC7BuB,SAAS,CAACL,KAAK,GAAI,EAAE,CAClBX,MAAM,CAAC,EAAAI,QAAA,GAAAK,OAAO,cAAAL,QAAA,uBAAPA,QAAA,CAASa,YAAY,KAAI,EAAE,CAAC,CACnCjB,MAAM,CAACW,KAAK,CAAC,CACbX,MAAM,CAAC,EAAAK,SAAA,GAAAI,OAAO,cAAAJ,SAAA,uBAAPA,SAAA,CAASa,WAAW,KAAI,EAAE,CAAC;EAErCF,SAAS,CAACG,QAAQ,CAACtB,IAAI,EAAEJ,QAAQ,CAAC;EAElC,IAAIqB,MAAM,IAAIA,MAAM,CAACM,QAAQ,EAAE;IAC7BN,MAAM,CAACM,QAAQ,CAACC,MAAM,CAACP,MAAM,CAACM,QAAQ,CAACE,OAAO,CAACN,SAAS,CAAC,EAAE,CAAC,CAAC;EAC/D;EAEA,OAAOA,SAAS,CAACO,OAAO;AAC1B"}