{"version": 3, "file": "worker_threads-browser.js", "names": ["Worker", "terminate", "NodeWorker", "NodeWorkerType", "parentPort"], "sources": ["../../../../src/lib/node/worker_threads-browser.ts"], "sourcesContent": ["// <PERSON><PERSON>er fills for Node.js built-in `worker_threads` module.\n// These fills are non-functional, and just intended to ensure that\n// `import 'worker_threads` doesn't break browser builds.\n// The replacement is done in package.json browser field\nexport class Worker {\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  terminate() {}\n}\n\nexport {Worker as NodeWorker};\nexport {Worker as NodeWorkerType};\n\nexport const parentPort = null;\n"], "mappings": "AAIA,OAAO,MAAMA,MAAM,CAAC;EAElBC,SAASA,CAAA,EAAG,CAAC;AACf;AAEA,SAAQD,MAAM,IAAIE,UAAU;AAC5B,SAAQF,MAAM,IAAIG,cAAc;AAEhC,OAAO,MAAMC,UAAU,GAAG,IAAI"}