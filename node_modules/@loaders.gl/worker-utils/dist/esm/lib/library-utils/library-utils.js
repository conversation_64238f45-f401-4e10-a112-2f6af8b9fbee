import { global, isBrowser, isWorker } from '../env-utils/globals';
import * as node from '../node/require-utils.node';
import { assert } from '../env-utils/assert';
import { VERSION as __VERSION__ } from '../env-utils/version';
const LATEST = 'latest';
const VERSION = typeof "3.4.15" !== 'undefined' ? "3.4.15" : LATEST;
const loadLibraryPromises = {};
export async function loadLibrary(libraryUrl) {
  let moduleName = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
  if (moduleName) {
    libraryUrl = getLibraryUrl(libraryUrl, moduleName, options);
  }
  loadLibraryPromises[libraryUrl] = loadLibraryPromises[libraryUrl] || loadLibraryFromFile(libraryUrl);
  return await loadLibraryPromises[libraryUrl];
}
export function getLibraryUrl(library, moduleName, options) {
  if (library.startsWith('http')) {
    return library;
  }
  const modules = options.modules || {};
  if (modules[library]) {
    return modules[library];
  }
  if (!isBrowser) {
    return "modules/".concat(moduleName, "/dist/libs/").concat(library);
  }
  if (options.CDN) {
    assert(options.CDN.startsWith('http'));
    return "".concat(options.CDN, "/").concat(moduleName, "@").concat(VERSION, "/dist/libs/").concat(library);
  }
  if (isWorker) {
    return "../src/libs/".concat(library);
  }
  return "modules/".concat(moduleName, "/src/libs/").concat(library);
}
async function loadLibraryFromFile(libraryUrl) {
  if (libraryUrl.endsWith('wasm')) {
    const response = await fetch(libraryUrl);
    return await response.arrayBuffer();
  }
  if (!isBrowser) {
    try {
      return node && node.requireFromFile && (await node.requireFromFile(libraryUrl));
    } catch {
      return null;
    }
  }
  if (isWorker) {
    return importScripts(libraryUrl);
  }
  const response = await fetch(libraryUrl);
  const scriptSource = await response.text();
  return loadLibraryFromString(scriptSource, libraryUrl);
}
function loadLibraryFromString(scriptSource, id) {
  if (!isBrowser) {
    return node.requireFromString && node.requireFromString(scriptSource, id);
  }
  if (isWorker) {
    eval.call(global, scriptSource);
    return null;
  }
  const script = document.createElement('script');
  script.id = id;
  try {
    script.appendChild(document.createTextNode(scriptSource));
  } catch (e) {
    script.text = scriptSource;
  }
  document.body.appendChild(script);
  return null;
}
//# sourceMappingURL=library-utils.js.map