{"version": 3, "sources": ["../src/lib/async-queue/async-queue.ts", "../src/lib/worker-utils/get-transfer-list.ts", "../src/lib/worker-farm/worker-body.ts", "../src/lib/worker-api/create-worker.ts", "../src/workers/null-worker.ts"], "sourcesContent": ["// From https://github.com/rauschma/async-iter-demo/tree/master/src under MIT license\n// http://2ality.com/2016/10/asynchronous-iteration.html\n\n/**\n * Async Queue\n * - AsyncIterable: An async iterator can be\n * - Values can be pushed onto the queue\n * @example\n *   const asyncQueue = new AsyncQueue();\n *   setTimeout(() => asyncQueue.enqueue('tick'), 1000);\n *   setTimeout(() => asyncQueue.enqueue(new Error('done')), 10000);\n *   for await (const value of asyncQueue) {\n *     console.log(value); // tick\n *   }\n */\nexport default class AsyncQueue<T> {\n  private _values: any[];\n  private _settlers: any[];\n  private _closed: boolean;\n\n  constructor() {\n    this._values = []; // enqueues > dequeues\n    this._settlers = []; // dequeues > enqueues\n    this._closed = false;\n  }\n\n  /** Return an async iterator for this queue */\n  [Symbol.asyncIterator](): AsyncIterator<T> {\n    return this;\n  }\n\n  /** Push a new value - the async iterator will yield a promise resolved to this value */\n  push(value: T): void {\n    return this.enqueue(value);\n  }\n\n  /**\n   * Push a new value - the async iterator will yield a promise resolved to this value\n   * Add an error - the async iterator will yield a promise rejected with this value\n   */\n  enqueue(value: T | Error): void {\n    if (this._closed) {\n      throw new Error('Closed');\n    }\n\n    if (this._settlers.length > 0) {\n      if (this._values.length > 0) {\n        throw new Error('Illegal internal state');\n      }\n      const settler = this._settlers.shift();\n      if (value instanceof Error) {\n        settler.reject(value);\n      } else {\n        settler.resolve({value});\n      }\n    } else {\n      this._values.push(value);\n    }\n  }\n\n  /** Indicate that we not waiting for more values - The async iterator will be done */\n  close(): void {\n    while (this._settlers.length > 0) {\n      const settler = this._settlers.shift();\n      settler.resolve({done: true});\n    }\n    this._closed = true;\n  }\n\n  // ITERATOR IMPLEMENTATION\n\n  /** @returns a Promise for an IteratorResult */\n  next(): Promise<IteratorResult<T, any>> {\n    // If values in queue, yield the first value\n    if (this._values.length > 0) {\n      const value = this._values.shift();\n      if (value instanceof Error) {\n        return Promise.reject(value);\n      }\n      return Promise.resolve({done: false, value});\n    }\n\n    // If queue is closed, the iterator is done\n    if (this._closed) {\n      if (this._settlers.length > 0) {\n        throw new Error('Illegal internal state');\n      }\n      return Promise.resolve({done: true, value: undefined});\n    }\n\n    // Yield a promise that waits for new values to be enqueued\n    return new Promise((resolve, reject) => {\n      this._settlers.push({resolve, reject});\n    });\n  }\n}\n", "// NOTE - there is a copy of this function is both in core and loader-utils\n// core does not need all the utils in loader-utils, just this one.\n\n/**\n * Returns an array of Transferrable objects that can be used with postMessage\n * https://developer.mozilla.org/en-US/docs/Web/API/Worker/postMessage\n * @param object data to be sent via postMessage\n * @param recursive - not for application use\n * @param transfers - not for application use\n * @returns a transfer list that can be passed to postMessage\n */\nexport function getTransferList(\n  object: any,\n  recursive: boolean = true,\n  transfers?: Set<any>\n): Transferable[] {\n  // Make sure that items in the transfer list is unique\n  const transfersSet = transfers || new Set();\n\n  if (!object) {\n    // ignore\n  } else if (isTransferable(object)) {\n    transfersSet.add(object);\n  } else if (isTransferable(object.buffer)) {\n    // Typed array\n    transfersSet.add(object.buffer);\n  } else if (ArrayBuffer.isView(object)) {\n    // object is a TypeArray viewing into a SharedArrayBuffer (not transferable)\n    // Do not iterate through the content in this case\n  } else if (recursive && typeof object === 'object') {\n    for (const key in object) {\n      // Avoid perf hit - only go one level deep\n      getTransferList(object[key], recursive, transfersSet);\n    }\n  }\n\n  // If transfers is defined, is internal recursive call\n  // Otherwise it's called by the user\n  return transfers === undefined ? Array.from(transfersSet) : [];\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/Transferable\nfunction isTransferable(object: unknown) {\n  if (!object) {\n    return false;\n  }\n  if (object instanceof ArrayBuffer) {\n    return true;\n  }\n  if (typeof MessagePort !== 'undefined' && object instanceof MessagePort) {\n    return true;\n  }\n  if (typeof ImageBitmap !== 'undefined' && object instanceof ImageBitmap) {\n    return true;\n  }\n  // @ts-ignore\n  if (typeof OffscreenCanvas !== 'undefined' && object instanceof OffscreenCanvas) {\n    return true;\n  }\n  return false;\n}\n\n/**\n * Recursively drop non serializable values like functions and regexps.\n * @param object\n */\nexport function getTransferListForWriter(object: object | null): object {\n  if (object === null) {\n    return {};\n  }\n  const clone = Object.assign({}, object);\n\n  Object.keys(clone).forEach((key) => {\n    // Typed Arrays and Arrays are passed with no change\n    if (\n      typeof object[key] === 'object' &&\n      !ArrayBuffer.isView(object[key]) &&\n      !(object[key] instanceof Array)\n    ) {\n      clone[key] = getTransferListForWriter(object[key]);\n    } else if (typeof clone[key] === 'function' || clone[key] instanceof RegExp) {\n      clone[key] = {};\n    } else {\n      clone[key] = object[key];\n    }\n  });\n\n  return clone;\n}\n", "import type {WorkerMessageData, WorkerMessageType, WorkerMessagePayload} from '../../types';\nimport {getTransferList} from '../worker-utils/get-transfer-list';\n\n/** Vile hack to defeat over-zealous bundlers from stripping out the require */\nfunction getParentPort() {\n  // const isNode = globalThis.process;\n  let parentPort;\n  try {\n    // prettier-ignore\n    eval('globalThis.parentPort = require(\\'worker_threads\\').parentPort'); // eslint-disable-line no-eval\n    parentPort = globalThis.parentPort;\n    // eslint-disable-next-line no-empty\n  } catch {}\n  return parentPort;\n}\n\nconst onMessageWrapperMap = new Map();\n\n/**\n * Type safe wrapper for worker code\n */\nexport default class WorkerBody {\n  /** Check that we are actually in a worker thread */\n  static inWorkerThread(): boolean {\n    return typeof self !== 'undefined' || Boolean(getParentPort());\n  }\n\n  /*\n   * (type: WorkerMessageType, payload: WorkerMessagePayload) => any\n   */\n  static set onmessage(onMessage: (type: WorkerMessageType, payload: WorkerMessagePayload) => any) {\n    function handleMessage(message) {\n      // Confusingly the message itself also has a 'type' field which is always set to 'message'\n      const parentPort = getParentPort();\n      const {type, payload} = parentPort ? message : message.data;\n      // if (!isKnownMessage(message)) {\n      //   return;\n      // }\n      onMessage(type, payload);\n    }\n\n    const parentPort = getParentPort();\n    if (parentPort) {\n      parentPort.on('message', handleMessage);\n      // if (message == 'exit') { parentPort.unref(); }\n      // eslint-disable-next-line\n      parentPort.on('exit', () => console.debug('Node worker closing'));\n    } else {\n      // eslint-disable-next-line no-restricted-globals\n      globalThis.onmessage = handleMessage;\n    }\n  }\n\n  static addEventListener(\n    onMessage: (type: WorkerMessageType, payload: WorkerMessagePayload) => any\n  ) {\n    let onMessageWrapper = onMessageWrapperMap.get(onMessage);\n\n    if (!onMessageWrapper) {\n      onMessageWrapper = (message: MessageEvent<any>) => {\n        if (!isKnownMessage(message)) {\n          return;\n        }\n\n        // Confusingly in the browser, the message itself also has a 'type' field which is always set to 'message'\n        const parentPort = getParentPort();\n        const {type, payload} = parentPort ? message : message.data;\n        onMessage(type, payload);\n      };\n    }\n\n    const parentPort = getParentPort();\n    if (parentPort) {\n      console.error('not implemented'); // eslint-disable-line\n    } else {\n      globalThis.addEventListener('message', onMessageWrapper);\n    }\n  }\n\n  static removeEventListener(\n    onMessage: (type: WorkerMessageType, payload: WorkerMessagePayload) => any\n  ) {\n    const onMessageWrapper = onMessageWrapperMap.get(onMessage);\n    onMessageWrapperMap.delete(onMessage);\n    const parentPort = getParentPort();\n    if (parentPort) {\n      console.error('not implemented'); // eslint-disable-line\n    } else {\n      globalThis.removeEventListener('message', onMessageWrapper);\n    }\n  }\n\n  /**\n   * Send a message from a worker to creating thread (main thread)\n   * @param type\n   * @param payload\n   */\n  static postMessage(type: WorkerMessageType, payload: WorkerMessagePayload): void {\n    const data: WorkerMessageData = {source: 'loaders.gl', type, payload};\n    // console.log('posting message', data);\n    const transferList = getTransferList(payload);\n\n    const parentPort = getParentPort();\n    if (parentPort) {\n      parentPort.postMessage(data, transferList);\n      // console.log('posted message', data);\n    } else {\n      // @ts-ignore\n      globalThis.postMessage(data, transferList);\n    }\n  }\n}\n\n// Filter out noise messages sent to workers\nfunction isKnownMessage(message: MessageEvent<any>) {\n  const {type, data} = message;\n  return (\n    type === 'message' &&\n    data &&\n    typeof data.source === 'string' &&\n    data.source.startsWith('loaders.gl')\n  );\n}\n", "import type {\n  WorkerMessageType,\n  WorkerMessagePayload,\n  WorkerContext,\n  Process,\n  ProcessInBatches\n} from '../../types';\nimport AsyncQueue from '../async-queue/async-queue';\nimport WorkerBody from '../worker-farm/worker-body';\n// import {validateWorkerVersion} from './validate-worker-version';\n\n/** Counter for jobs */\nlet requestId = 0;\nlet inputBatches: AsyncQueue<any>;\nlet options: {[key: string]: any};\n\nexport type ProcessOnMainThread = (\n  data: any,\n  options?: {[key: string]: any},\n  context?: WorkerContext\n) => any;\n\n/**\n * Set up a WebWorkerGlobalScope to talk with the main thread\n */\nexport function createWorker(process: Process, processInBatches?: ProcessInBatches): void {\n  if (!WorkerBody.inWorkerThread()) {\n    return;\n  }\n\n  const context: WorkerContext = {\n    process: processOnMainThread\n  };\n\n  // eslint-disable-next-line complexity\n  WorkerBody.onmessage = async (type: WorkerMessageType, payload: WorkerMessagePayload) => {\n    try {\n      switch (type) {\n        case 'process':\n          if (!process) {\n            throw new Error('Worker does not support atomic processing');\n          }\n          const result = await process(payload.input, payload.options || {}, context);\n          WorkerBody.postMessage('done', {result});\n          break;\n\n        case 'process-in-batches':\n          if (!processInBatches) {\n            throw new Error('Worker does not support batched processing');\n          }\n          inputBatches = new AsyncQueue<any>();\n          options = payload.options || {};\n          const resultIterator = processInBatches(inputBatches, options, context);\n          for await (const batch of resultIterator) {\n            WorkerBody.postMessage('output-batch', {result: batch});\n          }\n          WorkerBody.postMessage('done', {});\n          break;\n\n        case 'input-batch':\n          inputBatches.push(payload.input);\n          break;\n\n        case 'input-done':\n          inputBatches.close();\n          break;\n\n        default:\n      }\n    } catch (error) {\n      const message = error instanceof Error ? error.message : '';\n      WorkerBody.postMessage('error', {error: message});\n    }\n  };\n}\n\nfunction processOnMainThread(arrayBuffer: ArrayBuffer, options = {}) {\n  return new Promise((resolve, reject) => {\n    const id = requestId++;\n\n    /**\n     */\n    const onMessage = (type: string, payload: WorkerMessagePayload) => {\n      if (payload.id !== id) {\n        // not ours\n        return;\n      }\n\n      switch (type) {\n        case 'done':\n          WorkerBody.removeEventListener(onMessage);\n          resolve(payload.result);\n          break;\n\n        case 'error':\n          WorkerBody.removeEventListener(onMessage);\n          reject(payload.error);\n          break;\n\n        default:\n        // ignore\n      }\n    };\n\n    WorkerBody.addEventListener(onMessage);\n\n    // Ask the main thread to decode data\n    const payload = {id, input: arrayBuffer, options};\n    WorkerBody.postMessage('process', payload);\n  });\n}\n", "import {createWorker} from '../lib/worker-api/create-worker';\n\ncreateWorker(async (data) => {\n  // @ts-ignore\n  return data;\n});\n"], "mappings": ";;AAeA,yBAAmC;AAAA,IAKjC,cAAc;AACZ,WAAK,UAAU;AACf,WAAK,YAAY;AACjB,WAAK,UAAU;AAAA;AAAA,KAIhB,OAAO,iBAAmC;AACzC,aAAO;AAAA;AAAA,IAIT,KAAK,OAAgB;AACnB,aAAO,KAAK,QAAQ;AAAA;AAAA,IAOtB,QAAQ,OAAwB;AAC9B,UAAI,KAAK,SAAS;AAChB,cAAM,IAAI,MAAM;AAAA;AAGlB,UAAI,KAAK,UAAU,SAAS,GAAG;AAC7B,YAAI,KAAK,QAAQ,SAAS,GAAG;AAC3B,gBAAM,IAAI,MAAM;AAAA;AAElB,cAAM,UAAU,KAAK,UAAU;AAC/B,YAAI,iBAAiB,OAAO;AAC1B,kBAAQ,OAAO;AAAA,eACV;AACL,kBAAQ,QAAQ,EAAC;AAAA;AAAA,aAEd;AACL,aAAK,QAAQ,KAAK;AAAA;AAAA;AAAA,IAKtB,QAAc;AACZ,aAAO,KAAK,UAAU,SAAS,GAAG;AAChC,cAAM,UAAU,KAAK,UAAU;AAC/B,gBAAQ,QAAQ,EAAC,MAAM;AAAA;AAEzB,WAAK,UAAU;AAAA;AAAA,IAMjB,OAAwC;AAEtC,UAAI,KAAK,QAAQ,SAAS,GAAG;AAC3B,cAAM,QAAQ,KAAK,QAAQ;AAC3B,YAAI,iBAAiB,OAAO;AAC1B,iBAAO,QAAQ,OAAO;AAAA;AAExB,eAAO,QAAQ,QAAQ,EAAC,MAAM,OAAO;AAAA;AAIvC,UAAI,KAAK,SAAS;AAChB,YAAI,KAAK,UAAU,SAAS,GAAG;AAC7B,gBAAM,IAAI,MAAM;AAAA;AAElB,eAAO,QAAQ,QAAQ,EAAC,MAAM,MAAM,OAAO;AAAA;AAI7C,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,aAAK,UAAU,KAAK,EAAC,SAAS;AAAA;AAAA;AAAA;;;ACjF7B,2BACL,QACA,YAAqB,MACrB,WACgB;AAEhB,UAAM,eAAe,aAAa,IAAI;AAEtC,QAAI,CAAC,QAAQ;AAAA,eAEF,eAAe,SAAS;AACjC,mBAAa,IAAI;AAAA,eACR,eAAe,OAAO,SAAS;AAExC,mBAAa,IAAI,OAAO;AAAA,eACf,YAAY,OAAO,SAAS;AAAA,eAG5B,aAAa,OAAO,WAAW,UAAU;AAClD,iBAAW,OAAO,QAAQ;AAExB,wBAAgB,OAAO,MAAM,WAAW;AAAA;AAAA;AAM5C,WAAO,cAAc,SAAY,MAAM,KAAK,gBAAgB;AAAA;AAI9D,0BAAwB,QAAiB;AACvC,QAAI,CAAC,QAAQ;AACX,aAAO;AAAA;AAET,QAAI,kBAAkB,aAAa;AACjC,aAAO;AAAA;AAET,QAAI,OAAO,gBAAgB,eAAe,kBAAkB,aAAa;AACvE,aAAO;AAAA;AAET,QAAI,OAAO,gBAAgB,eAAe,kBAAkB,aAAa;AACvE,aAAO;AAAA;AAGT,QAAI,OAAO,oBAAoB,eAAe,kBAAkB,iBAAiB;AAC/E,aAAO;AAAA;AAET,WAAO;AAAA;;;ACvDT,2BAAyB;AAEvB,QAAI;AACJ,QAAI;AAEF,WAAK;AACL,mBAAa,WAAW;AAAA,YAExB;AAAA;AACF,WAAO;AAAA;AAGT,MAAM,sBAAsB,IAAI;AAKhC,yBAAgC;AAAA,WAEvB,iBAA0B;AAC/B,aAAO,OAAO,SAAS,eAAe,QAAQ;AAAA;AAAA,eAMrC,UAAU,WAA4E;AAC/F,6BAAuB,SAAS;AAE9B,cAAM,cAAa;AACnB,cAAM,EAAC,MAAM,YAAW,cAAa,UAAU,QAAQ;AAIvD,kBAAU,MAAM;AAAA;AAGlB,YAAM,cAAa;AACnB,UAAI,aAAY;AACd,oBAAW,GAAG,WAAW;AAGzB,oBAAW,GAAG,QAAQ,MAAM,QAAQ,MAAM;AAAA,aACrC;AAEL,mBAAW,YAAY;AAAA;AAAA;AAAA,WAIpB,iBACL,WACA;AACA,UAAI,mBAAmB,oBAAoB,IAAI;AAE/C,UAAI,CAAC,kBAAkB;AACrB,2BAAmB,CAAC,YAA+B;AACjD,cAAI,CAAC,eAAe,UAAU;AAC5B;AAAA;AAIF,gBAAM,cAAa;AACnB,gBAAM,EAAC,MAAM,YAAW,cAAa,UAAU,QAAQ;AACvD,oBAAU,MAAM;AAAA;AAAA;AAIpB,YAAM,cAAa;AACnB,UAAI,aAAY;AACd,gBAAQ,MAAM;AAAA,aACT;AACL,mBAAW,iBAAiB,WAAW;AAAA;AAAA;AAAA,WAIpC,oBACL,WACA;AACA,YAAM,mBAAmB,oBAAoB,IAAI;AACjD,0BAAoB,OAAO;AAC3B,YAAM,cAAa;AACnB,UAAI,aAAY;AACd,gBAAQ,MAAM;AAAA,aACT;AACL,mBAAW,oBAAoB,WAAW;AAAA;AAAA;AAAA,WASvC,YAAY,MAAyB,SAAqC;AAC/E,YAAM,OAA0B,EAAC,QAAQ,cAAc,MAAM;AAE7D,YAAM,eAAe,gBAAgB;AAErC,YAAM,cAAa;AACnB,UAAI,aAAY;AACd,oBAAW,YAAY,MAAM;AAAA,aAExB;AAEL,mBAAW,YAAY,MAAM;AAAA;AAAA;AAAA;AAMnC,0BAAwB,SAA4B;AAClD,UAAM,EAAC,MAAM,SAAQ;AACrB,WACE,SAAS,aACT,QACA,OAAO,KAAK,WAAW,YACvB,KAAK,OAAO,WAAW;AAAA;;;AC5G3B,MAAI,YAAY;AAChB,MAAI;AACJ,MAAI;AAWG,wBAAsB,SAAkB,kBAA2C;AACxF,QAAI,CAAC,WAAW,kBAAkB;AAChC;AAAA;AAGF,UAAM,UAAyB;AAAA,MAC7B,SAAS;AAAA;AAIX,eAAW,YAAY,OAAO,MAAyB,YAAkC;AACvF,UAAI;AACF,gBAAQ;AAAA,eACD;AACH,gBAAI,CAAC,SAAS;AACZ,oBAAM,IAAI,MAAM;AAAA;AAElB,kBAAM,SAAS,MAAM,QAAQ,QAAQ,OAAO,QAAQ,WAAW,IAAI;AACnE,uBAAW,YAAY,QAAQ,EAAC;AAChC;AAAA,eAEG;AACH,gBAAI,CAAC,kBAAkB;AACrB,oBAAM,IAAI,MAAM;AAAA;AAElB,2BAAe,IAAI;AACnB,sBAAU,QAAQ,WAAW;AAC7B,kBAAM,iBAAiB,iBAAiB,cAAc,SAAS;AAC/D,6BAAiB,SAAS,gBAAgB;AACxC,yBAAW,YAAY,gBAAgB,EAAC,QAAQ;AAAA;AAElD,uBAAW,YAAY,QAAQ;AAC/B;AAAA,eAEG;AACH,yBAAa,KAAK,QAAQ;AAC1B;AAAA,eAEG;AACH,yBAAa;AACb;AAAA;AAAA;AAAA,eAIG,OAAP;AACA,cAAM,UAAU,iBAAiB,QAAQ,MAAM,UAAU;AACzD,mBAAW,YAAY,SAAS,EAAC,OAAO;AAAA;AAAA;AAAA;AAK9C,+BAA6B,aAA0B,WAAU,IAAI;AACnE,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,YAAM,KAAK;AAIX,YAAM,YAAY,CAAC,MAAc,aAAkC;AACjE,YAAI,SAAQ,OAAO,IAAI;AAErB;AAAA;AAGF,gBAAQ;AAAA,eACD;AACH,uBAAW,oBAAoB;AAC/B,oBAAQ,SAAQ;AAChB;AAAA,eAEG;AACH,uBAAW,oBAAoB;AAC/B,mBAAO,SAAQ;AACf;AAAA;AAAA;AAAA;AAON,iBAAW,iBAAiB;AAG5B,YAAM,UAAU,EAAC,IAAI,OAAO,aAAa;AACzC,iBAAW,YAAY,WAAW;AAAA;AAAA;;;AC1GtC,eAAa,OAAO,SAAS;AAE3B,WAAO;AAAA;", "names": []}