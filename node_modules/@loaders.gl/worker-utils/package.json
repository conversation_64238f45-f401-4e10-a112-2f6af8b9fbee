{"name": "@loaders.gl/worker-utils", "version": "3.4.15", "description": "Utilities for running tasks on worker threads", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/visgl/loaders.gl"}, "keywords": ["worker", "process", "thread"], "types": "dist/index.d.ts", "main": "dist/es5/index.js", "module": "dist/esm/index.js", "sideEffects": false, "files": ["src", "dist", "README.md"], "browser": {"child_process": false, "module": false, "path": false, "fs": false, "./src/lib/node/worker_threads.ts": "./src/lib/node/worker_threads-browser.ts", "./dist/es5/lib/node/worker_threads.js": "./dist/es5/lib/node/worker_threads-browser.js", "./dist/esm/lib/node/worker_threads.js": "./dist/esm/lib/node/worker_threads-browser.js", "./src/lib/node/require-utils.node.ts": "./src/lib/node/require-utils.browser.ts", "./dist/es5/lib/node/require-utils.node.js": "./dist/es5/lib/node/require-utils.browser.js", "./dist/esm/lib/node/require-utils.node.js": "./dist/esm/lib/node/require-utils.browser.js", "./src/lib/process-utils/child-process-proxy.ts": false, "./dist/es5/lib/process-utils/child-process-proxy.js": false, "./dist/esm/lib/process-utils/child-process-proxy.js": false}, "scripts": {"pre-build": "npm run build-workers", "pre-build-disabled": "npm run build-bundle && npm run build-workers", "build-bundle": "esbuild src/bundle.ts --outfile=dist/dist.min.js", "build-workers": "esbuild src/workers/null-worker.ts --outfile=dist/null-worker.js --bundle --sourcemap --define:__VERSION__=\\\"$npm_package_version\\\""}, "dependencies": {"@babel/runtime": "^7.3.1"}, "gitHead": "19e941d5805568e449ef9092490d6568a4853298"}