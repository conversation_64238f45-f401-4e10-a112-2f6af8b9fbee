import type { LoaderWithParser } from '@loaders.gl/loader-utils';
import type { <PERSON><PERSON><PERSON>esh, DracoLoaderData } from './lib/draco-types';
import type { DracoLoaderOptions } from './draco-loader';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON>aco<PERSON>orkerLoader } from './draco-loader';
export type { DracoMesh, DracoLoaderData };
export type { DracoWriterOptions } from './draco-writer';
export { DracoWriter } from './draco-writer';
/**
 * Browser worker doesn't work because of issue during "draco_encoder.js" loading.
 * Refused to execute script from 'https://raw.githubusercontent.com/google/draco/1.4.1/javascript/draco_encoder.js' because its MIME type ('') is not executable.
 */
export declare const DracoWriterWorker: {
    id: string;
    name: string;
    module: string;
    version: any;
    worker: boolean;
    options: {
        draco: {};
        source: null;
    };
};
export type { DracoLoaderOptions };
export { Draco<PERSON>orkerLoader };
/**
 * Loader for Draco3D compressed geometries
 */
export declare const DracoLoader: {
    parse: typeof parse;
    name: string;
    id: string;
    module: string;
    shapes: string[];
    version: any;
    worker: boolean;
    extensions: string[];
    mimeTypes: string[];
    binary: boolean;
    tests: string[];
    options: DracoLoaderOptions;
};
declare function parse(arrayBuffer: ArrayBuffer, options?: DracoLoaderOptions): Promise<DracoMesh>;
export declare const _TypecheckDracoLoader: LoaderWithParser;
//# sourceMappingURL=index.d.ts.map