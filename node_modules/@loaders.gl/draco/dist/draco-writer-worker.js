(()=>{var k=Object.defineProperty;var N=t=>k(t,"__esModule",{value:!0});var v=(t,e)=>{N(t);for(var r in e)k(t,r,{get:e[r],enumerable:!0})};var m="3.4.15";function T(t,e){if(!t)throw new Error(e||"loaders.gl assertion failed.")}var c={self:typeof self!="undefined"&&self,window:typeof window!="undefined"&&window,global:typeof global!="undefined"&&global,document:typeof document!="undefined"&&document},oe=c.self||c.window||c.global||{},ae=c.window||c.self||c.global||{},P=c.global||c.self||c.window||{},se=c.document||{};var y=typeof process!="object"||String(process)!=="[object process]"||process.browser,h=typeof importScripts=="function",ne=typeof window!="undefined"&&typeof window.orientation!="undefined",W=typeof process!="undefined"&&process.version&&/v([0-9]*)/.exec(process.version),ie=W&&parseFloat(W[1])||0;function w(t,e=!0,r){let o=r||new Set;if(t){if(B(t))o.add(t);else if(B(t.buffer))o.add(t.buffer);else if(!ArrayBuffer.isView(t)){if(e&&typeof t=="object")for(let a in t)w(t[a],e,o)}}return r===void 0?Array.from(o):[]}function B(t){return t?t instanceof ArrayBuffer||typeof MessagePort!="undefined"&&t instanceof MessagePort||typeof ImageBitmap!="undefined"&&t instanceof ImageBitmap||typeof OffscreenCanvas!="undefined"&&t instanceof OffscreenCanvas:!1}function f(){let parentPort;try{eval("globalThis.parentPort = require('worker_threads').parentPort"),parentPort=globalThis.parentPort}catch{}return parentPort}var b=new Map,u=class{static inWorkerThread(){return typeof self!="undefined"||Boolean(f())}static set onmessage(e){function r(a){let s=f(),{type:i,payload:n}=s?a:a.data;e(i,n)}let o=f();o?(o.on("message",r),o.on("exit",()=>console.debug("Node worker closing"))):globalThis.onmessage=r}static addEventListener(e){let r=b.get(e);r||(r=a=>{if(!F(a))return;let s=f(),{type:i,payload:n}=s?a:a.data;e(i,n)}),f()?console.error("not implemented"):globalThis.addEventListener("message",r)}static removeEventListener(e){let r=b.get(e);b.delete(e),f()?console.error("not implemented"):globalThis.removeEventListener("message",r)}static postMessage(e,r){let o={source:"loaders.gl",type:e,payload:r},a=w(r),s=f();s?s.postMessage(o,a):globalThis.postMessage(o,a)}};function F(t){let{type:e,data:r}=t;return e==="message"&&r&&typeof r.source=="string"&&r.source.startsWith("loaders.gl")}var A={};v(A,{readFileAsArrayBuffer:()=>V,readFileAsText:()=>$,requireFromFile:()=>M,requireFromString:()=>_});var V=null,$=null,M=null,_=null;var q="latest",j=typeof m!="undefined"?m:q,E={};async function O(t,e=null,r={}){return e&&(t=I(t,e,r)),E[t]=E[t]||U(t),await E[t]}function I(t,e,r){if(t.startsWith("http"))return t;let o=r.modules||{};return o[t]?o[t]:y?r.CDN?(T(r.CDN.startsWith("http")),`${r.CDN}/${e}@${j}/dist/libs/${t}`):h?`../src/libs/${t}`:`modules/${e}/src/libs/${t}`:`modules/${e}/dist/libs/${t}`}async function U(t){if(t.endsWith("wasm"))return await(await fetch(t)).arrayBuffer();if(!y)try{return A&&M&&await M(t)}catch{return null}if(h)return importScripts(t);let r=await(await fetch(t)).text();return z(r,t)}function z(t,e){if(!y)return _&&_(t,e);if(h)return eval.call(P,t),null;let r=document.createElement("script");r.id=e;try{r.appendChild(document.createTextNode(t))}catch{r.text=t}return document.body.appendChild(r),null}var R={POSITION:"POSITION",NORMAL:"NORMAL",COLOR_0:"COLOR",TEXCOORD_0:"TEX_COORD"},G=()=>{},g=class{constructor(e){this.draco=e,this.dracoEncoder=new this.draco.Encoder,this.dracoMeshBuilder=new this.draco.MeshBuilder,this.dracoMetadataBuilder=new this.draco.MetadataBuilder}destroy(){this.destroyEncodedObject(this.dracoMeshBuilder),this.destroyEncodedObject(this.dracoEncoder),this.destroyEncodedObject(this.dracoMetadataBuilder),this.dracoMeshBuilder=null,this.dracoEncoder=null,this.draco=null}destroyEncodedObject(e){e&&this.draco.destroy(e)}encodeSync(e,r={}){return this.log=G,this._setOptions(r),r.pointcloud?this._encodePointCloud(e,r):this._encodeMesh(e,r)}_getAttributesFromMesh(e){let r={...e,...e.attributes};return e.indices&&(r.indices=e.indices),r}_encodePointCloud(e,r){let o=new this.draco.PointCloud;r.metadata&&this._addGeometryMetadata(o,r.metadata);let a=this._getAttributesFromMesh(e);this._createDracoPointCloud(o,a,r);let s=new this.draco.DracoInt8Array;try{let i=this.dracoEncoder.EncodePointCloudToDracoBuffer(o,!1,s);if(!(i>0))throw new Error("Draco encoding failed.");return this.log(`DRACO encoded ${o.num_points()} points
        with ${o.num_attributes()} attributes into ${i} bytes`),x(s)}finally{this.destroyEncodedObject(s),this.destroyEncodedObject(o)}}_encodeMesh(e,r){let o=new this.draco.Mesh;r.metadata&&this._addGeometryMetadata(o,r.metadata);let a=this._getAttributesFromMesh(e);this._createDracoMesh(o,a,r);let s=new this.draco.DracoInt8Array;try{let i=this.dracoEncoder.EncodeMeshToDracoBuffer(o,s);if(i<=0)throw new Error("Draco encoding failed.");return this.log(`DRACO encoded ${o.num_points()} points
        with ${o.num_attributes()} attributes into ${i} bytes`),x(s)}finally{this.destroyEncodedObject(s),this.destroyEncodedObject(o)}}_setOptions(e){if("speed"in e&&this.dracoEncoder.SetSpeedOptions(...e.speed),"method"in e){let r=this.draco[e.method||"MESH_SEQUENTIAL_ENCODING"];this.dracoEncoder.SetEncodingMethod(r)}if("quantization"in e)for(let r in e.quantization){let o=e.quantization[r],a=this.draco[r];this.dracoEncoder.SetAttributeQuantization(a,o)}}_createDracoMesh(e,r,o){let a=o.attributesMetadata||{};try{let s=this._getPositionAttribute(r);if(!s)throw new Error("positions");let i=s.length/3;for(let n in r){let d=r[n];n=R[n]||n;let l=this._addAttributeToMesh(e,n,d,i);l!==-1&&this._addAttributeMetadata(e,l,{name:n,...a[n]||{}})}}catch(s){throw this.destroyEncodedObject(e),s}return e}_createDracoPointCloud(e,r,o){let a=o.attributesMetadata||{};try{let s=this._getPositionAttribute(r);if(!s)throw new Error("positions");let i=s.length/3;for(let n in r){let d=r[n];n=R[n]||n;let l=this._addAttributeToMesh(e,n,d,i);l!==-1&&this._addAttributeMetadata(e,l,{name:n,...a[n]||{}})}}catch(s){throw this.destroyEncodedObject(e),s}return e}_addAttributeToMesh(e,r,o,a){if(!ArrayBuffer.isView(o))return-1;let s=this._getDracoAttributeType(r),i=o.length/a;if(s==="indices"){let l=o.length/3;return this.log(`Adding attribute ${r}, size ${l}`),this.dracoMeshBuilder.AddFacesToMesh(e,l,o),-1}this.log(`Adding attribute ${r}, size ${i}`);let n=this.dracoMeshBuilder,{buffer:d}=o;switch(o.constructor){case Int8Array:return n.AddInt8Attribute(e,s,a,i,new Int8Array(d));case Int16Array:return n.AddInt16Attribute(e,s,a,i,new Int16Array(d));case Int32Array:return n.AddInt32Attribute(e,s,a,i,new Int32Array(d));case Uint8Array:case Uint8ClampedArray:return n.AddUInt8Attribute(e,s,a,i,new Uint8Array(d));case Uint16Array:return n.AddUInt16Attribute(e,s,a,i,new Uint16Array(d));case Uint32Array:return n.AddUInt32Attribute(e,s,a,i,new Uint32Array(d));case Float32Array:default:return n.AddFloatAttribute(e,s,a,i,new Float32Array(d))}}_getDracoAttributeType(e){switch(e.toLowerCase()){case"indices":return"indices";case"position":case"positions":case"vertices":return this.draco.POSITION;case"normal":case"normals":return this.draco.NORMAL;case"color":case"colors":return this.draco.COLOR;case"texcoord":case"texcoords":return this.draco.TEX_COORD;default:return this.draco.GENERIC}}_getPositionAttribute(e){for(let r in e){let o=e[r];if(this._getDracoAttributeType(r)===this.draco.POSITION)return o}return null}_addGeometryMetadata(e,r){let o=new this.draco.Metadata;this._populateDracoMetadata(o,r),this.dracoMeshBuilder.AddMetadata(e,o)}_addAttributeMetadata(e,r,o){let a=new this.draco.Metadata;this._populateDracoMetadata(a,o),this.dracoMeshBuilder.SetMetadataForAttribute(e,r,a)}_populateDracoMetadata(e,r){for(let[o,a]of Q(r))switch(typeof a){case"number":Math.trunc(a)===a?this.dracoMetadataBuilder.AddIntEntry(e,o,a):this.dracoMetadataBuilder.AddDoubleEntry(e,o,a);break;case"object":a instanceof Int32Array&&this.dracoMetadataBuilder.AddIntEntryArray(e,o,a,a.length);break;case"string":default:this.dracoMetadataBuilder.AddStringEntry(e,o,a)}}};function x(t){let e=t.size(),r=new ArrayBuffer(e),o=new Int8Array(r);for(let a=0;a<e;++a)o[a]=t.GetValue(a);return r}function Q(t){return t.entries&&!t.hasOwnProperty("entries")?t.entries():Object.entries(t)}var X="1.5.5",J="1.4.1",D=`https://www.gstatic.com/draco/versioned/decoders/${X}`,_e=`${D}/draco_decoder.js`,Ae=`${D}/draco_wasm_wrapper.js`,Ee=`${D}/draco_decoder.wasm`,H=`https://raw.githubusercontent.com/google/draco/${J}/javascript/draco_encoder.js`;var p;async function S(t){let e=t.modules||{};return e.draco3d?p=p||e.draco3d.createEncoderModule({}).then(r=>({draco:r})):p=p||K(t),await p}async function K(t){let e=await O(H,"draco",t);return e=e||globalThis.DracoEncoderModule,new Promise(r=>{e({onModuleLoaded:o=>r({draco:o})})})}var C="3.4.15";var Y={pointcloud:!1,attributeNameEntry:"name"},L={name:"DRACO",id:"draco",module:"draco",version:C,extensions:["drc"],encode:Z,options:{draco:Y}};async function Z(t,e={}){let{draco:r}=await S(e),o=new g(r);try{return o.encodeSync(t,e.draco)}finally{o.destroy()}}(()=>{!u.inWorkerThread()||(u.onmessage=async(t,e)=>{switch(t){case"process":try{let{input:r,options:o}=e,a=await L.encode(r,o);u.postMessage("done",{result:a})}catch(r){let o=r instanceof Error?r.message:"";u.postMessage("error",{error:o})}break;default:}})})();})();
//# sourceMappingURL=draco-writer-worker.js.map
