{"version": 3, "file": "index.js", "names": ["_d<PERSON>o<PERSON><PERSON><PERSON>", "require", "_d<PERSON><PERSON><PERSON><PERSON><PERSON>", "_interopRequireDefault", "_dracoModuleLoader", "_version", "_workerUtils", "_dracoWriter", "DracoWriterWorker", "id", "<PERSON><PERSON><PERSON><PERSON>", "name", "module", "version", "VERSION", "worker", "options", "draco", "source", "exports", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DracoWorkerLoader", "parse", "arrayBuffer", "loadDracoDecoderModule", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseSync", "destroy", "_TypecheckDracoLoader"], "sources": ["../../src/index.ts"], "sourcesContent": ["import type {LoaderWithParser} from '@loaders.gl/loader-utils';\nimport type {<PERSON><PERSON>Mesh, DracoLoaderData} from './lib/draco-types';\nimport type {DracoLoaderOptions} from './draco-loader';\nimport {<PERSON><PERSON><PERSON>oa<PERSON> as DracoWorkerLoader} from './draco-loader';\nimport DracoParser from './lib/draco-parser';\nimport {loadDracoDecoderModule} from './lib/draco-module-loader';\nimport {VERSION} from './lib/utils/version';\nimport {isBrowser} from '@loaders.gl/worker-utils';\n\n// Draco data types\n\nexport type {DracoMesh, DracoLoaderData};\n\n// Draco Writer\n\nexport type {DracoWriterOptions} from './draco-writer';\nexport {DracoWriter} from './draco-writer';\n\n/**\n * Browser worker doesn't work because of issue during \"draco_encoder.js\" loading.\n * Refused to execute script from 'https://raw.githubusercontent.com/google/draco/1.4.1/javascript/draco_encoder.js' because its MIME type ('') is not executable.\n */\nexport const DracoWriterWorker = {\n  id: isBrowser ? 'draco-writer' : 'draco-writer-nodejs',\n  name: 'Draco compressed geometry writer',\n  module: 'draco',\n  version: VERSION,\n  worker: true,\n  options: {\n    draco: {},\n    source: null\n  }\n};\n\n// Draco Loader\n\nexport type {DracoLoaderOptions};\nexport {DracoWorkerLoader};\n\n/**\n * Loader for Draco3D compressed geometries\n */\nexport const DracoLoader = {\n  ...DracoWorkerLoader,\n  parse\n};\n\nasync function parse(arrayBuffer: ArrayBuffer, options?: DracoLoaderOptions): Promise<DracoMesh> {\n  const {draco} = await loadDracoDecoderModule(options);\n  const dracoParser = new DracoParser(draco);\n  try {\n    return dracoParser.parseSync(arrayBuffer, options?.draco);\n  } finally {\n    dracoParser.destroy();\n  }\n}\n\n// TYPE TESTS - TODO find a better way than exporting junk\nexport const _TypecheckDracoLoader: LoaderWithParser = DracoLoader;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,kBAAA,GAAAH,OAAA;AACA,IAAAI,QAAA,GAAAJ,OAAA;AACA,IAAAK,YAAA,GAAAL,OAAA;AASA,IAAAM,YAAA,GAAAN,OAAA;AAMO,MAAMO,iBAAiB,GAAG;EAC/BC,EAAE,EAAEC,sBAAS,GAAG,cAAc,GAAG,qBAAqB;EACtDC,IAAI,EAAE,kCAAkC;EACxCC,MAAM,EAAE,OAAO;EACfC,OAAO,EAAEC,gBAAO;EAChBC,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE;IACPC,KAAK,EAAE,CAAC,CAAC;IACTC,MAAM,EAAE;EACV;AACF,CAAC;AAACC,OAAA,CAAAX,iBAAA,GAAAA,iBAAA;AAUK,MAAMY,WAAW,GAAG;EACzB,GAAGC,wBAAiB;EACpBC;AACF,CAAC;AAACH,OAAA,CAAAC,WAAA,GAAAA,WAAA;AAEF,eAAeE,KAAKA,CAACC,WAAwB,EAAEP,OAA4B,EAAsB;EAC/F,MAAM;IAACC;EAAK,CAAC,GAAG,MAAM,IAAAO,yCAAsB,EAACR,OAAO,CAAC;EACrD,MAAMS,WAAW,GAAG,IAAIC,oBAAW,CAACT,KAAK,CAAC;EAC1C,IAAI;IACF,OAAOQ,WAAW,CAACE,SAAS,CAACJ,WAAW,EAAEP,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEC,KAAK,CAAC;EAC3D,CAAC,SAAS;IACRQ,WAAW,CAACG,OAAO,CAAC,CAAC;EACvB;AACF;AAGO,MAAMC,qBAAuC,GAAGT,WAAW;AAACD,OAAA,CAAAU,qBAAA,GAAAA,qBAAA"}