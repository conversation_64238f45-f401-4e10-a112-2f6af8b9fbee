{"version": 3, "file": "draco-writer.js", "names": ["_dracoBuilder", "_interopRequireDefault", "require", "_dracoModuleLoader", "_version", "DEFAULT_DRACO_OPTIONS", "pointcloud", "attributeNameEntry", "DracoWriter", "name", "id", "module", "version", "VERSION", "extensions", "encode", "options", "draco", "exports", "data", "arguments", "length", "undefined", "loadDracoEncoderModule", "dracoBuilder", "DRACOBuilder", "encodeSync", "destroy"], "sources": ["../../src/draco-writer.ts"], "sourcesContent": ["import type {Writer, WriterOptions} from '@loaders.gl/loader-utils';\nimport type {DracoMesh} from './lib/draco-types';\nimport type {DracoBuildOptions} from './lib/draco-builder';\nimport DRACOBuilder from './lib/draco-builder';\nimport {loadDracoEncoderModule} from './lib/draco-module-loader';\nimport {VERSION} from './lib/utils/version';\n\nexport type DracoWriterOptions = WriterOptions & {\n  draco?: DracoBuildOptions & {\n    attributeNameEntry: string;\n  };\n};\n\nconst DEFAULT_DRACO_OPTIONS = {\n  pointcloud: false, // Set to true if pointcloud (mode: 0, no indices)\n  attributeNameEntry: 'name'\n  // Draco Compression Parameters\n  // method: 'MESH_EDGEBREAKER_ENCODING',\n  // speed: [5, 5],\n  // quantization: {\n  //   POSITION: 10\n  // }\n};\n\n/**\n * Exporter for Draco3D compressed geometries\n */\nexport const DracoWriter: Writer = {\n  name: 'DR<PERSON><PERSON>',\n  id: 'draco',\n  module: 'draco',\n  version: VERSION,\n  extensions: ['drc'],\n  encode,\n  options: {\n    draco: DEFAULT_DRACO_OPTIONS\n  }\n};\n\nasync function encode(data: DracoMesh, options: DracoWriterOptions = {}): Promise<ArrayBuffer> {\n  // Dynamically load draco\n  const {draco} = await loadDracoEncoderModule(options);\n  const dracoBuilder = new DRACOBuilder(draco);\n\n  try {\n    return dracoBuilder.encodeSync(data, options.draco);\n  } finally {\n    dracoBuilder.destroy();\n  }\n}\n"], "mappings": ";;;;;;;AAGA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AAQA,MAAMG,qBAAqB,GAAG;EAC5BC,UAAU,EAAE,KAAK;EACjBC,kBAAkB,EAAE;AAOtB,CAAC;AAKM,MAAMC,WAAmB,GAAG;EACjCC,IAAI,EAAE,OAAO;EACbC,EAAE,EAAE,OAAO;EACXC,MAAM,EAAE,OAAO;EACfC,OAAO,EAAEC,gBAAO;EAChBC,UAAU,EAAE,CAAC,KAAK,CAAC;EACnBC,MAAM;EACNC,OAAO,EAAE;IACPC,KAAK,EAAEZ;EACT;AACF,CAAC;AAACa,OAAA,CAAAV,WAAA,GAAAA,WAAA;AAEF,eAAeO,MAAMA,CAACI,IAAe,EAA0D;EAAA,IAAxDH,OAA2B,GAAAI,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAErE,MAAM;IAACH;EAAK,CAAC,GAAG,MAAM,IAAAM,yCAAsB,EAACP,OAAO,CAAC;EACrD,MAAMQ,YAAY,GAAG,IAAIC,qBAAY,CAACR,KAAK,CAAC;EAE5C,IAAI;IACF,OAAOO,YAAY,CAACE,UAAU,CAACP,IAAI,EAAEH,OAAO,CAACC,KAAK,CAAC;EACrD,CAAC,SAAS;IACRO,YAAY,CAACG,OAAO,CAAC,CAAC;EACxB;AACF"}