"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.DracoWriter = void 0;
var _dracoBuilder = _interopRequireDefault(require("./lib/draco-builder"));
var _dracoModuleLoader = require("./lib/draco-module-loader");
var _version = require("./lib/utils/version");
const DEFAULT_DRACO_OPTIONS = {
  pointcloud: false,
  attributeNameEntry: 'name'
};
const DracoWriter = {
  name: 'DRACO',
  id: 'draco',
  module: 'draco',
  version: _version.VERSION,
  extensions: ['drc'],
  encode,
  options: {
    draco: DEFAULT_DRACO_OPTIONS
  }
};
exports.DracoWriter = DracoWriter;
async function encode(data) {
  let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  const {
    draco
  } = await (0, _dracoModuleLoader.loadDracoEncoderModule)(options);
  const dracoBuilder = new _dracoBuilder.default(draco);
  try {
    return dracoBuilder.encodeSync(data, options.draco);
  } finally {
    dracoBuilder.destroy();
  }
}
//# sourceMappingURL=draco-writer.js.map