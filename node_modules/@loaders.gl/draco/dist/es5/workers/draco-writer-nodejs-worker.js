"use strict";

var _workerUtils = require("@loaders.gl/worker-utils");
var _dracoWriterNodejs = require("../draco-writer-nodejs");
(() => {
  if (!_workerUtils.WorkerBody.inWorkerThread()) {
    return;
  }
  _workerUtils.WorkerBody.onmessage = async (type, payload) => {
    switch (type) {
      case 'process':
        try {
          const {
            input,
            options
          } = payload;
          const result = await _dracoWriterNodejs.DracoWriterNodeJS.encode(input, options);
          _workerUtils.WorkerBody.postMessage('done', {
            result
          });
        } catch (error) {
          const message = error instanceof Error ? error.message : '';
          _workerUtils.WorkerBody.postMessage('error', {
            error: message
          });
        }
        break;
      default:
    }
  };
})();
//# sourceMappingURL=draco-writer-nodejs-worker.js.map