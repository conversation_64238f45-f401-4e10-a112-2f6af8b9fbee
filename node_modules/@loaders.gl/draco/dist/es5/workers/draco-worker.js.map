{"version": 3, "file": "draco-worker.js", "names": ["_loaderUtils", "require", "_index", "createLoaderWorker", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["../../../src/workers/draco-worker.ts"], "sourcesContent": ["import {createLoaderWorker} from '@loaders.gl/loader-utils';\nimport {DracoLoader} from '../index';\n\ncreateLoaderWorker(DracoLoader);\n"], "mappings": ";;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAEA,IAAAE,+BAAkB,EAACC,kBAAW,CAAC"}