{"version": 3, "file": "draco-nodejs-worker.js", "names": ["_loaderUtils", "require", "_index", "createLoaderWorker", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["../../../src/workers/draco-nodejs-worker.ts"], "sourcesContent": ["import {createLoaderWorker} from '@loaders.gl/loader-utils';\nimport '@loaders.gl/polyfills';\nimport {DracoLoader} from '../index';\n\ncreateLoaderWorker(DracoLoader);\n"], "mappings": ";;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACAA,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAEA,IAAAE,+BAAkB,EAACC,kBAAW,CAAC"}