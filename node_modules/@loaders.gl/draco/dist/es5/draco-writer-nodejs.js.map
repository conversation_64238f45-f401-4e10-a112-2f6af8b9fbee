{"version": 3, "file": "draco-writer-nodejs.js", "names": ["require", "_dracoWriter"], "sources": ["../../src/draco-writer-nodejs.ts"], "sourcesContent": ["// Polyfills increases the bundle size significantly. Use it for NodeJS worker only\nimport '@loaders.gl/polyfills';\n\nexport {DracoWriter as DracoWriterNodeJS} from './draco-writer';\n"], "mappings": ";;;;;;;;;;;AACAA,OAAA;AAEA,IAAAC,YAAA,GAAAD,OAAA"}