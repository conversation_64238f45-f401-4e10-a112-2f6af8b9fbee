"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.DracoLoader = void 0;
Object.defineProperty(exports, "DracoWorkerLoader", {
  enumerable: true,
  get: function () {
    return _dracoLoader.DracoLoader;
  }
});
Object.defineProperty(exports, "DracoWriter", {
  enumerable: true,
  get: function () {
    return _dracoWriter.DracoWriter;
  }
});
exports._TypecheckDracoLoader = exports.DracoWriterWorker = void 0;
var _dracoLoader = require("./draco-loader");
var _dracoParser = _interopRequireDefault(require("./lib/draco-parser"));
var _dracoModuleLoader = require("./lib/draco-module-loader");
var _version = require("./lib/utils/version");
var _workerUtils = require("@loaders.gl/worker-utils");
var _dracoWriter = require("./draco-writer");
const DracoWriterWorker = {
  id: _workerUtils.isBrowser ? 'draco-writer' : 'draco-writer-nodejs',
  name: 'Draco compressed geometry writer',
  module: 'draco',
  version: _version.VERSION,
  worker: true,
  options: {
    draco: {},
    source: null
  }
};
exports.DracoWriterWorker = DracoWriterWorker;
const DracoLoader = {
  ..._dracoLoader.DracoLoader,
  parse
};
exports.DracoLoader = DracoLoader;
async function parse(arrayBuffer, options) {
  const {
    draco
  } = await (0, _dracoModuleLoader.loadDracoDecoderModule)(options);
  const dracoParser = new _dracoParser.default(draco);
  try {
    return dracoParser.parseSync(arrayBuffer, options === null || options === void 0 ? void 0 : options.draco);
  } finally {
    dracoParser.destroy();
  }
}
const _TypecheckDracoLoader = DracoLoader;
exports._TypecheckDracoLoader = _TypecheckDracoLoader;
//# sourceMappingURL=index.js.map