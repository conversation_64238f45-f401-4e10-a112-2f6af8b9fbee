"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports._TypecheckDracoLoader = exports.DracoLoader = void 0;
var _workerUtils = require("@loaders.gl/worker-utils");
var _version = require("./lib/utils/version");
const DEFAULT_DRACO_OPTIONS = {
  draco: {
    decoderType: typeof WebAssembly === 'object' ? 'wasm' : 'js',
    libraryPath: 'libs/',
    extraAttributes: {},
    attributeNameEntry: undefined
  }
};
const DracoLoader = {
  name: 'Draco',
  id: _workerUtils.isBrowser ? 'draco' : 'draco-nodejs',
  module: 'draco',
  shapes: ['mesh'],
  version: _version.VERSION,
  worker: true,
  extensions: ['drc'],
  mimeTypes: ['application/octet-stream'],
  binary: true,
  tests: ['DRACO'],
  options: DEFAULT_DRACO_OPTIONS
};
exports.DracoLoader = DracoLoader;
const _TypecheckDracoLoader = DracoLoader;
exports._TypecheckDracoLoader = _TypecheckDracoLoader;
//# sourceMappingURL=draco-loader.js.map