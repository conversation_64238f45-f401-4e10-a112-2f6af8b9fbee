"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getDracoSchema = getDracoSchema;
var _schema = require("@loaders.gl/schema");
function getDracoSchema(attributes, loaderData, indices) {
  const metadataMap = makeMetadata(loaderData.metadata);
  const fields = [];
  const namedLoaderDataAttributes = transformAttributesLoaderData(loaderData.attributes);
  for (const attributeName in attributes) {
    const attribute = attributes[attributeName];
    const field = getArrowFieldFromAttribute(attributeName, attribute, namedLoaderDataAttributes[attributeName]);
    fields.push(field);
  }
  if (indices) {
    const indicesField = getArrowFieldFromAttribute('indices', indices);
    fields.push(indicesField);
  }
  return new _schema.Schema(fields, metadataMap);
}
function transformAttributesLoaderData(loaderData) {
  const result = {};
  for (const key in loaderData) {
    const dracoAttribute = loaderData[key];
    result[dracoAttribute.name || 'undefined'] = dracoAttribute;
  }
  return result;
}
function getArrowFieldFromAttribute(attributeName, attribute, loaderData) {
  const metadataMap = loaderData ? makeMetadata(loaderData.metadata) : undefined;
  const field = (0, _schema.deduceMeshField)(attributeName, attribute, metadataMap);
  return field;
}
function makeMetadata(metadata) {
  const metadataMap = new Map();
  for (const key in metadata) {
    metadataMap.set("".concat(key, ".string"), JSON.stringify(metadata[key]));
  }
  return metadataMap;
}
//# sourceMappingURL=get-draco-schema.js.map