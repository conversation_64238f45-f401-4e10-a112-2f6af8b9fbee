{"version": 3, "file": "draco-module-loader.js", "names": ["_workerUtils", "require", "DRACO_DECODER_VERSION", "DRACO_ENCODER_VERSION", "STATIC_DECODER_URL", "concat", "DRACO_JS_DECODER_URL", "DRACO_WASM_WRAPPER_URL", "DRACO_WASM_DECODER_URL", "DRACO_ENCODER_URL", "loadDecoderPromise", "loadEncoderPromise", "loadDracoDecoderModule", "options", "modules", "draco3d", "createDecoderModule", "then", "draco", "loadDracoDecoder", "loadDracoEncoderModule", "createEncoderModule", "loadDracoEncoder", "DracoDecoderModule", "wasmBinary", "decoderType", "loadLibrary", "Promise", "all", "globalThis", "initializeDracoDecoder", "resolve", "onModuleLoaded", "DracoEncoderModule"], "sources": ["../../../src/lib/draco-module-loader.ts"], "sourcesContent": ["// Dynamic DRACO module loading inspired by THREE.DRACOLoader\n// https://github.com/mrdoob/three.js/blob/398c4f39ebdb8b23eefd4a7a5ec49ec0c96c7462/examples/jsm/loaders/DRACOLoader.js\n// by <PERSON> / https://www.donmccurdy.com / MIT license\n\nimport {loadLibrary} from '@loaders.gl/worker-utils';\n\nconst DRACO_DECODER_VERSION = '1.5.5';\nconst DRACO_ENCODER_VERSION = '1.4.1';\n\nconst STATIC_DECODER_URL = `https://www.gstatic.com/draco/versioned/decoders/${DRACO_DECODER_VERSION}`;\n\nconst DRACO_JS_DECODER_URL = `${STATIC_DECODER_URL}/draco_decoder.js`;\nconst DRACO_WASM_WRAPPER_URL = `${STATIC_DECODER_URL}/draco_wasm_wrapper.js`;\nconst DRACO_WASM_DECODER_URL = `${STATIC_DECODER_URL}/draco_decoder.wasm`;\n\nconst DRACO_ENCODER_URL = `https://raw.githubusercontent.com/google/draco/${DRACO_ENCODER_VERSION}/javascript/draco_encoder.js`;\n\nlet loadDecoderPromise;\nlet loadEncoderPromise;\n\nexport async function loadDracoDecoderModule(options) {\n  const modules = options.modules || {};\n\n  // Check if a bundled draco3d library has been supplied by application\n  if (modules.draco3d) {\n    loadDecoderPromise =\n      loadDecoderPromise ||\n      modules.draco3d.createDecoderModule({}).then((draco) => {\n        return {draco};\n      });\n  } else {\n    // If not, dynamically load the WASM script from our CDN\n    loadDecoderPromise = loadDecoderPromise || loadDracoDecoder(options);\n  }\n  return await loadDecoderPromise;\n}\n\nexport async function loadDracoEncoderModule(options) {\n  const modules = options.modules || {};\n\n  // Check if a bundled draco3d library has been supplied by application\n  if (modules.draco3d) {\n    loadEncoderPromise =\n      loadEncoderPromise ||\n      modules.draco3d.createEncoderModule({}).then((draco) => {\n        return {draco};\n      });\n  } else {\n    // If not, dynamically load the WASM script from our CDN\n    loadEncoderPromise = loadEncoderPromise || loadDracoEncoder(options);\n  }\n  return await loadEncoderPromise;\n}\n\n// DRACO DECODER LOADING\n\nasync function loadDracoDecoder(options) {\n  let DracoDecoderModule;\n  let wasmBinary;\n  switch (options.draco && options.draco.decoderType) {\n    case 'js':\n      DracoDecoderModule = await loadLibrary(DRACO_JS_DECODER_URL, 'draco', options);\n      break;\n\n    case 'wasm':\n    default:\n      [DracoDecoderModule, wasmBinary] = await Promise.all([\n        await loadLibrary(DRACO_WASM_WRAPPER_URL, 'draco', options),\n        await loadLibrary(DRACO_WASM_DECODER_URL, 'draco', options)\n      ]);\n  }\n  // Depends on how import happened...\n  // @ts-ignore\n  DracoDecoderModule = DracoDecoderModule || globalThis.DracoDecoderModule;\n  return await initializeDracoDecoder(DracoDecoderModule, wasmBinary);\n}\n\nfunction initializeDracoDecoder(DracoDecoderModule, wasmBinary) {\n  const options: {wasmBinary?: any} = {};\n  if (wasmBinary) {\n    options.wasmBinary = wasmBinary;\n  }\n\n  return new Promise((resolve) => {\n    DracoDecoderModule({\n      ...options,\n      onModuleLoaded: (draco) => resolve({draco}) // Module is Promise-like. Wrap in object to avoid loop.\n    });\n  });\n}\n\n// ENCODER\n\nasync function loadDracoEncoder(options) {\n  let DracoEncoderModule = await loadLibrary(DRACO_ENCODER_URL, 'draco', options);\n  // @ts-ignore\n  DracoEncoderModule = DracoEncoderModule || globalThis.DracoEncoderModule;\n\n  return new Promise((resolve) => {\n    DracoEncoderModule({\n      onModuleLoaded: (draco) => resolve({draco}) // Module is Promise-like. Wrap in object to avoid loop.\n    });\n  });\n}\n"], "mappings": ";;;;;;;AAIA,IAAAA,YAAA,GAAAC,OAAA;AAEA,MAAMC,qBAAqB,GAAG,OAAO;AACrC,MAAMC,qBAAqB,GAAG,OAAO;AAErC,MAAMC,kBAAkB,uDAAAC,MAAA,CAAuDH,qBAAqB,CAAE;AAEtG,MAAMI,oBAAoB,MAAAD,MAAA,CAAMD,kBAAkB,sBAAmB;AACrE,MAAMG,sBAAsB,MAAAF,MAAA,CAAMD,kBAAkB,2BAAwB;AAC5E,MAAMI,sBAAsB,MAAAH,MAAA,CAAMD,kBAAkB,wBAAqB;AAEzE,MAAMK,iBAAiB,qDAAAJ,MAAA,CAAqDF,qBAAqB,iCAA8B;AAE/H,IAAIO,kBAAkB;AACtB,IAAIC,kBAAkB;AAEf,eAAeC,sBAAsBA,CAACC,OAAO,EAAE;EACpD,MAAMC,OAAO,GAAGD,OAAO,CAACC,OAAO,IAAI,CAAC,CAAC;EAGrC,IAAIA,OAAO,CAACC,OAAO,EAAE;IACnBL,kBAAkB,GAChBA,kBAAkB,IAClBI,OAAO,CAACC,OAAO,CAACC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAEC,KAAK,IAAK;MACtD,OAAO;QAACA;MAAK,CAAC;IAChB,CAAC,CAAC;EACN,CAAC,MAAM;IAELR,kBAAkB,GAAGA,kBAAkB,IAAIS,gBAAgB,CAACN,OAAO,CAAC;EACtE;EACA,OAAO,MAAMH,kBAAkB;AACjC;AAEO,eAAeU,sBAAsBA,CAACP,OAAO,EAAE;EACpD,MAAMC,OAAO,GAAGD,OAAO,CAACC,OAAO,IAAI,CAAC,CAAC;EAGrC,IAAIA,OAAO,CAACC,OAAO,EAAE;IACnBJ,kBAAkB,GAChBA,kBAAkB,IAClBG,OAAO,CAACC,OAAO,CAACM,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAACJ,IAAI,CAAEC,KAAK,IAAK;MACtD,OAAO;QAACA;MAAK,CAAC;IAChB,CAAC,CAAC;EACN,CAAC,MAAM;IAELP,kBAAkB,GAAGA,kBAAkB,IAAIW,gBAAgB,CAACT,OAAO,CAAC;EACtE;EACA,OAAO,MAAMF,kBAAkB;AACjC;AAIA,eAAeQ,gBAAgBA,CAACN,OAAO,EAAE;EACvC,IAAIU,kBAAkB;EACtB,IAAIC,UAAU;EACd,QAAQX,OAAO,CAACK,KAAK,IAAIL,OAAO,CAACK,KAAK,CAACO,WAAW;IAChD,KAAK,IAAI;MACPF,kBAAkB,GAAG,MAAM,IAAAG,wBAAW,EAACpB,oBAAoB,EAAE,OAAO,EAAEO,OAAO,CAAC;MAC9E;IAEF,KAAK,MAAM;IACX;MACE,CAACU,kBAAkB,EAAEC,UAAU,CAAC,GAAG,MAAMG,OAAO,CAACC,GAAG,CAAC,CACnD,MAAM,IAAAF,wBAAW,EAACnB,sBAAsB,EAAE,OAAO,EAAEM,OAAO,CAAC,EAC3D,MAAM,IAAAa,wBAAW,EAAClB,sBAAsB,EAAE,OAAO,EAAEK,OAAO,CAAC,CAC5D,CAAC;EACN;EAGAU,kBAAkB,GAAGA,kBAAkB,IAAIM,UAAU,CAACN,kBAAkB;EACxE,OAAO,MAAMO,sBAAsB,CAACP,kBAAkB,EAAEC,UAAU,CAAC;AACrE;AAEA,SAASM,sBAAsBA,CAACP,kBAAkB,EAAEC,UAAU,EAAE;EAC9D,MAAMX,OAA2B,GAAG,CAAC,CAAC;EACtC,IAAIW,UAAU,EAAE;IACdX,OAAO,CAACW,UAAU,GAAGA,UAAU;EACjC;EAEA,OAAO,IAAIG,OAAO,CAAEI,OAAO,IAAK;IAC9BR,kBAAkB,CAAC;MACjB,GAAGV,OAAO;MACVmB,cAAc,EAAGd,KAAK,IAAKa,OAAO,CAAC;QAACb;MAAK,CAAC;IAC5C,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAIA,eAAeI,gBAAgBA,CAACT,OAAO,EAAE;EACvC,IAAIoB,kBAAkB,GAAG,MAAM,IAAAP,wBAAW,EAACjB,iBAAiB,EAAE,OAAO,EAAEI,OAAO,CAAC;EAE/EoB,kBAAkB,GAAGA,kBAAkB,IAAIJ,UAAU,CAACI,kBAAkB;EAExE,OAAO,IAAIN,OAAO,CAAEI,OAAO,IAAK;IAC9BE,kBAAkB,CAAC;MACjBD,cAAc,EAAGd,KAAK,IAAKa,OAAO,CAAC;QAACb;MAAK,CAAC;IAC5C,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ"}