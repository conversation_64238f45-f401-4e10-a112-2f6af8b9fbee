{"version": 3, "file": "draco-parser.js", "names": ["_schema", "require", "_getDracoSchema", "GEOMETRY_TYPE", "TRIANGULAR_MESH", "POINT_CLOUD", "DRACO_TO_GLTF_ATTRIBUTE_NAME_MAP", "POSITION", "NORMAL", "COLOR", "TEX_COORD", "DRACO_DATA_TYPE_TO_TYPED_ARRAY_MAP", "Int8Array", "Uint8Array", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "INDEX_ITEM_SIZE", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "draco", "_defineProperty2", "default", "decoder", "Decoder", "metadataQuerier", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "destroy", "parseSync", "arrayBuffer", "options", "arguments", "length", "undefined", "buffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Init", "byteLength", "_disableAttributeTransforms", "geometry_type", "GetEncodedGeometryType", "dracoGeometry", "<PERSON><PERSON>", "PointCloud", "drac<PERSON><PERSON><PERSON><PERSON>", "DecodeBufferToMesh", "DecodeBufferToPointCloud", "Error", "ok", "ptr", "message", "concat", "error_msg", "loaderData", "_getDracoLoaderData", "geometry", "_getMeshData", "boundingBox", "getMeshBoundingBox", "attributes", "schema", "getDracoSchema", "indices", "data", "loader", "header", "vertexCount", "num_points", "metadata", "_getTopLevelMetadata", "_getDracoAttributes", "num_attributes", "num_faces", "dracoAttributes", "attributeId", "dracoAttribute", "GetAttribute", "_getAttributeMetadata", "unique_id", "attribute_type", "data_type", "num_components", "byte_offset", "byte_stride", "normalized", "attribute_index", "quantization", "_getQuantizationTransform", "quantization_transform", "octahedron", "_getOctahedronTransform", "octahedron_transform", "_getMeshAttributes", "positionAttribute", "topology", "mode", "value", "_getTriangleStripIndices", "size", "_getTriangleListIndices", "loaderAttribute", "Object", "values", "attributeName", "_deduceAttributeName", "name", "_getAttributeValues", "byteOffset", "byteStride", "numFaces", "numIndices", "_malloc", "GetTrianglesUInt32Array", "HEAPF32", "slice", "_free", "dracoA<PERSON>y", "DracoInt32Array", "GetTriangleStripsFromMesh", "getUint32Array", "attribute", "TypedArrayCtor", "numComponents", "numPoints", "numValues", "BYTES_PER_ELEMENT", "dataType", "getDracoDataType", "GetAttributeDataArrayForAllPoints", "uniqueId", "attributeUniqueId", "entries", "extraAttributes", "thisAttributeType", "dracoAttributeConstant", "attributeType", "entryName", "attributeNameEntry", "string", "dracoMetadata", "GetMetadata", "_getDracoMetadata", "GetAttributeMetadata", "result", "numEntries", "NumEntries", "entryIndex", "GetEntryName", "_getDracoMetadataField", "GetIntEntryArray", "intArray", "getInt32Array", "int", "GetIntEntry", "GetStringEntry", "double", "GetDoubleEntry", "quantizedAttributes", "octahedronAttributes", "skipAttributes", "dracoAttributeName", "SkipAttributeTransform", "skip", "map", "type", "includes", "transform", "AttributeQuantizationTransform", "InitFromAttribute", "quantization_bits", "range", "min_values", "i", "min_value", "exports", "DT_FLOAT32", "DT_INT8", "DT_INT16", "DT_INT32", "DT_UINT8", "DT_UINT16", "DT_UINT32", "DT_INVALID", "GetValue"], "sources": ["../../../src/lib/draco-parser.ts"], "sourcesContent": ["/* eslint-disable camelcase */\n\nimport type {TypedArray, MeshAttribute, MeshGeometry} from '@loaders.gl/schema';\n\n// Draco types (input)\nimport type {\n  Draco3D,\n  Decoder,\n  Mesh,\n  PointCloud,\n  PointAttribute,\n  Metadata,\n  MetadataQuerier,\n  DracoInt32Array,\n  draco_DataType\n} from '../draco3d/draco3d-types';\n\n// Parsed data types (output)\nimport type {\n  DracoMesh,\n  DracoLoaderData,\n  DracoAttribute,\n  DracoMetadataEntry,\n  DracoQuantizationTransform,\n  DracoOctahedronTransform\n} from './draco-types';\n\nimport {getMeshBoundingBox} from '@loaders.gl/schema';\nimport {getDracoSchema} from './utils/get-draco-schema';\n\n/**\n * @param topology - How triangle indices should be generated (mesh only)\n * @param attributeNameEntry\n * @param extraAttributes\n * @param quantizedAttributes\n * @param octahedronAttributes\n */\nexport type DracoParseOptions = {\n  topology?: 'triangle-list' | 'triangle-strip';\n  attributeNameEntry?: string;\n  extraAttributes?: {[uniqueId: string]: number};\n  quantizedAttributes?: ('POSITION' | 'NORMAL' | 'COLOR' | 'TEX_COORD' | 'GENERIC')[];\n  octahedronAttributes?: ('POSITION' | 'NORMAL' | 'COLOR' | 'TEX_COORD' | 'GENERIC')[];\n};\n\n// @ts-ignore\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst GEOMETRY_TYPE = {\n  TRIANGULAR_MESH: 0,\n  POINT_CLOUD: 1\n};\n\n// Native Draco attribute names to GLTF attribute names.\nconst DRACO_TO_GLTF_ATTRIBUTE_NAME_MAP = {\n  POSITION: 'POSITION',\n  NORMAL: 'NORMAL',\n  COLOR: 'COLOR_0',\n  TEX_COORD: 'TEXCOORD_0'\n};\n\nconst DRACO_DATA_TYPE_TO_TYPED_ARRAY_MAP = {\n  1: Int8Array,\n  2: Uint8Array,\n  3: Int16Array,\n  4: Uint16Array,\n  5: Int32Array,\n  6: Uint32Array,\n  9: Float32Array\n};\n\nconst INDEX_ITEM_SIZE = 4;\n\nexport default class DracoParser {\n  draco: Draco3D;\n  decoder: Decoder;\n  metadataQuerier: MetadataQuerier;\n\n  // draco - the draco decoder, either import `draco3d` or load dynamically\n  constructor(draco: Draco3D) {\n    this.draco = draco;\n    this.decoder = new this.draco.Decoder();\n    this.metadataQuerier = new this.draco.MetadataQuerier();\n  }\n\n  /**\n   * Destroy draco resources\n   */\n  destroy(): void {\n    this.draco.destroy(this.decoder);\n    this.draco.destroy(this.metadataQuerier);\n  }\n\n  /**\n   * NOTE: caller must call `destroyGeometry` on the return value after using it\n   * @param arrayBuffer\n   * @param options\n   */\n  parseSync(arrayBuffer: ArrayBuffer, options: DracoParseOptions = {}): DracoMesh {\n    const buffer = new this.draco.DecoderBuffer();\n    buffer.Init(new Int8Array(arrayBuffer), arrayBuffer.byteLength);\n\n    this._disableAttributeTransforms(options);\n\n    const geometry_type = this.decoder.GetEncodedGeometryType(buffer);\n    const dracoGeometry =\n      geometry_type === this.draco.TRIANGULAR_MESH\n        ? new this.draco.Mesh()\n        : new this.draco.PointCloud();\n\n    try {\n      let dracoStatus;\n      switch (geometry_type) {\n        case this.draco.TRIANGULAR_MESH:\n          dracoStatus = this.decoder.DecodeBufferToMesh(buffer, dracoGeometry as Mesh);\n          break;\n\n        case this.draco.POINT_CLOUD:\n          dracoStatus = this.decoder.DecodeBufferToPointCloud(buffer, dracoGeometry);\n          break;\n\n        default:\n          throw new Error('DRACO: Unknown geometry type.');\n      }\n\n      if (!dracoStatus.ok() || !dracoGeometry.ptr) {\n        const message = `DRACO decompression failed: ${dracoStatus.error_msg()}`;\n        // console.error(message);\n        throw new Error(message);\n      }\n\n      const loaderData = this._getDracoLoaderData(dracoGeometry, geometry_type, options);\n\n      const geometry = this._getMeshData(dracoGeometry, loaderData, options);\n\n      const boundingBox = getMeshBoundingBox(geometry.attributes);\n\n      const schema = getDracoSchema(geometry.attributes, loaderData, geometry.indices);\n\n      const data: DracoMesh = {\n        loader: 'draco',\n        loaderData,\n        header: {\n          vertexCount: dracoGeometry.num_points(),\n          boundingBox\n        },\n        ...geometry,\n        schema\n      };\n      return data;\n    } finally {\n      this.draco.destroy(buffer);\n      if (dracoGeometry) {\n        this.draco.destroy(dracoGeometry);\n      }\n    }\n  }\n\n  // Draco specific \"loader data\"\n\n  /**\n   * Extract\n   * @param dracoGeometry\n   * @param geometry_type\n   * @param options\n   * @returns\n   */\n  _getDracoLoaderData(\n    dracoGeometry: Mesh | PointCloud,\n    geometry_type,\n    options: DracoParseOptions\n  ): DracoLoaderData {\n    const metadata = this._getTopLevelMetadata(dracoGeometry);\n    const attributes = this._getDracoAttributes(dracoGeometry, options);\n\n    return {\n      geometry_type,\n      num_attributes: dracoGeometry.num_attributes(),\n      num_points: dracoGeometry.num_points(),\n      num_faces: dracoGeometry instanceof this.draco.Mesh ? dracoGeometry.num_faces() : 0,\n      metadata,\n      attributes\n    };\n  }\n\n  /**\n   * Extract all draco provided information and metadata for each attribute\n   * @param dracoGeometry\n   * @param options\n   * @returns\n   */\n  _getDracoAttributes(\n    dracoGeometry: Mesh | PointCloud,\n    options: DracoParseOptions\n  ): {[unique_id: number]: DracoAttribute} {\n    const dracoAttributes: {[unique_id: number]: DracoAttribute} = {};\n\n    for (let attributeId = 0; attributeId < dracoGeometry.num_attributes(); attributeId++) {\n      // Note: Draco docs do not seem clear on `GetAttribute` ids just being a zero-based index,\n      // but it does seems to work this way\n      const dracoAttribute = this.decoder.GetAttribute(dracoGeometry, attributeId);\n\n      const metadata = this._getAttributeMetadata(dracoGeometry, attributeId);\n\n      dracoAttributes[dracoAttribute.unique_id()] = {\n        unique_id: dracoAttribute.unique_id(),\n        attribute_type: dracoAttribute.attribute_type(),\n        data_type: dracoAttribute.data_type(),\n        num_components: dracoAttribute.num_components(),\n\n        byte_offset: dracoAttribute.byte_offset(),\n        byte_stride: dracoAttribute.byte_stride(),\n        normalized: dracoAttribute.normalized(),\n        attribute_index: attributeId,\n\n        metadata\n      };\n\n      // Add transformation parameters for any attributes app wants untransformed\n      const quantization = this._getQuantizationTransform(dracoAttribute, options);\n      if (quantization) {\n        dracoAttributes[dracoAttribute.unique_id()].quantization_transform = quantization;\n      }\n\n      const octahedron = this._getOctahedronTransform(dracoAttribute, options);\n      if (octahedron) {\n        dracoAttributes[dracoAttribute.unique_id()].octahedron_transform = octahedron;\n      }\n    }\n\n    return dracoAttributes;\n  }\n\n  /**\n   * Get standard loaders.gl mesh category data\n   * Extracts the geometry from draco\n   * @param dracoGeometry\n   * @param options\n   */\n  _getMeshData(\n    dracoGeometry: Mesh | PointCloud,\n    loaderData: DracoLoaderData,\n    options: DracoParseOptions\n  ): MeshGeometry {\n    const attributes = this._getMeshAttributes(loaderData, dracoGeometry, options);\n\n    const positionAttribute = attributes.POSITION;\n    if (!positionAttribute) {\n      throw new Error('DRACO: No position attribute found.');\n    }\n\n    // For meshes, we need indices to define the faces.\n    if (dracoGeometry instanceof this.draco.Mesh) {\n      switch (options.topology) {\n        case 'triangle-strip':\n          return {\n            topology: 'triangle-strip',\n            mode: 4, // GL.TRIANGLES\n            attributes,\n            indices: {\n              value: this._getTriangleStripIndices(dracoGeometry),\n              size: 1\n            }\n          };\n        case 'triangle-list':\n        default:\n          return {\n            topology: 'triangle-list',\n            mode: 5, // GL.TRIANGLE_STRIP\n            attributes,\n            indices: {\n              value: this._getTriangleListIndices(dracoGeometry),\n              size: 1\n            }\n          };\n      }\n    }\n\n    // PointCloud - must come last as Mesh inherits from PointCloud\n    return {\n      topology: 'point-list',\n      mode: 0, // GL.POINTS\n      attributes\n    };\n  }\n\n  _getMeshAttributes(\n    loaderData: DracoLoaderData,\n    dracoGeometry: Mesh | PointCloud,\n    options: DracoParseOptions\n  ): {[attributeName: string]: MeshAttribute} {\n    const attributes: {[key: string]: MeshAttribute} = {};\n\n    for (const loaderAttribute of Object.values(loaderData.attributes)) {\n      const attributeName = this._deduceAttributeName(loaderAttribute, options);\n      loaderAttribute.name = attributeName;\n      const {value, size} = this._getAttributeValues(dracoGeometry, loaderAttribute);\n      attributes[attributeName] = {\n        value,\n        size,\n        byteOffset: loaderAttribute.byte_offset,\n        byteStride: loaderAttribute.byte_stride,\n        normalized: loaderAttribute.normalized\n      };\n    }\n\n    return attributes;\n  }\n\n  // MESH INDICES EXTRACTION\n\n  /**\n   * For meshes, we need indices to define the faces.\n   * @param dracoGeometry\n   */\n  _getTriangleListIndices(dracoGeometry: Mesh) {\n    // Example on how to retrieve mesh and attributes.\n    const numFaces = dracoGeometry.num_faces();\n    const numIndices = numFaces * 3;\n    const byteLength = numIndices * INDEX_ITEM_SIZE;\n\n    const ptr = this.draco._malloc(byteLength);\n    try {\n      this.decoder.GetTrianglesUInt32Array(dracoGeometry, byteLength, ptr);\n      return new Uint32Array(this.draco.HEAPF32.buffer, ptr, numIndices).slice();\n    } finally {\n      this.draco._free(ptr);\n    }\n  }\n\n  /**\n   * For meshes, we need indices to define the faces.\n   * @param dracoGeometry\n   */\n  _getTriangleStripIndices(dracoGeometry: Mesh) {\n    const dracoArray = new this.draco.DracoInt32Array();\n    try {\n      /* const numStrips = */ this.decoder.GetTriangleStripsFromMesh(dracoGeometry, dracoArray);\n      return getUint32Array(dracoArray);\n    } finally {\n      this.draco.destroy(dracoArray);\n    }\n  }\n\n  /**\n   *\n   * @param dracoGeometry\n   * @param dracoAttribute\n   * @param attributeName\n   */\n  _getAttributeValues(\n    dracoGeometry: Mesh | PointCloud,\n    attribute: DracoAttribute\n  ): {value: TypedArray; size: number} {\n    const TypedArrayCtor = DRACO_DATA_TYPE_TO_TYPED_ARRAY_MAP[attribute.data_type];\n    const numComponents = attribute.num_components;\n    const numPoints = dracoGeometry.num_points();\n    const numValues = numPoints * numComponents;\n\n    const byteLength = numValues * TypedArrayCtor.BYTES_PER_ELEMENT;\n    const dataType = getDracoDataType(this.draco, TypedArrayCtor);\n\n    let value: TypedArray;\n\n    const ptr = this.draco._malloc(byteLength);\n    try {\n      const dracoAttribute = this.decoder.GetAttribute(dracoGeometry, attribute.attribute_index);\n      this.decoder.GetAttributeDataArrayForAllPoints(\n        dracoGeometry,\n        dracoAttribute,\n        dataType,\n        byteLength,\n        ptr\n      );\n      value = new TypedArrayCtor(this.draco.HEAPF32.buffer, ptr, numValues).slice();\n    } finally {\n      this.draco._free(ptr);\n    }\n\n    return {value, size: numComponents};\n  }\n\n  // Attribute names\n\n  /** \n   * DRACO does not store attribute names - We need to deduce an attribute name\n   * for each attribute\n  _getAttributeNames(\n    dracoGeometry: Mesh | PointCloud,\n    options: DracoParseOptions\n  ): {[unique_id: number]: string} {\n    const attributeNames: {[unique_id: number]: string} = {};\n    for (let attributeId = 0; attributeId < dracoGeometry.num_attributes(); attributeId++) {\n      const dracoAttribute = this.decoder.GetAttribute(dracoGeometry, attributeId);\n      const attributeName = this._deduceAttributeName(dracoAttribute, options);\n      attributeNames[attributeName] = attributeName;\n    }\n    return attributeNames;\n  }\n   */\n\n  /**\n   * Deduce an attribute name.\n   * @note DRACO does not save attribute names, just general type (POSITION, COLOR)\n   * to help optimize compression. We generate GLTF compatible names for the Draco-recognized\n   * types\n   * @param attributeData\n   */\n  _deduceAttributeName(attribute: DracoAttribute, options: DracoParseOptions): string {\n    // Deduce name based on application provided map\n    const uniqueId = attribute.unique_id;\n    for (const [attributeName, attributeUniqueId] of Object.entries(\n      options.extraAttributes || {}\n    )) {\n      if (attributeUniqueId === uniqueId) {\n        return attributeName;\n      }\n    }\n\n    // Deduce name based on attribute type\n    const thisAttributeType = attribute.attribute_type;\n    for (const dracoAttributeConstant in DRACO_TO_GLTF_ATTRIBUTE_NAME_MAP) {\n      const attributeType = this.draco[dracoAttributeConstant];\n      if (attributeType === thisAttributeType) {\n        // TODO - Return unique names if there multiple attributes per type\n        // (e.g. multiple TEX_COORDS or COLORS)\n        return DRACO_TO_GLTF_ATTRIBUTE_NAME_MAP[dracoAttributeConstant];\n      }\n    }\n\n    // Look up in metadata\n    // TODO - shouldn't this have priority?\n    const entryName = options.attributeNameEntry || 'name';\n    if (attribute.metadata[entryName]) {\n      return attribute.metadata[entryName].string;\n    }\n\n    // Attribute of \"GENERIC\" type, we need to assign some name\n    return `CUSTOM_ATTRIBUTE_${uniqueId}`;\n  }\n\n  // METADATA EXTRACTION\n\n  /** Get top level metadata */\n  _getTopLevelMetadata(dracoGeometry: Mesh | PointCloud) {\n    const dracoMetadata = this.decoder.GetMetadata(dracoGeometry);\n    return this._getDracoMetadata(dracoMetadata);\n  }\n\n  /** Get per attribute metadata */\n  _getAttributeMetadata(dracoGeometry: Mesh | PointCloud, attributeId: number) {\n    const dracoMetadata = this.decoder.GetAttributeMetadata(dracoGeometry, attributeId);\n    return this._getDracoMetadata(dracoMetadata);\n  }\n\n  /**\n   * Extract metadata field values\n   * @param dracoMetadata\n   * @returns\n   */\n  _getDracoMetadata(dracoMetadata: Metadata): {[entry: string]: DracoMetadataEntry} {\n    // The not so wonderful world of undocumented Draco APIs :(\n    if (!dracoMetadata || !dracoMetadata.ptr) {\n      return {};\n    }\n    const result = {};\n    const numEntries = this.metadataQuerier.NumEntries(dracoMetadata);\n    for (let entryIndex = 0; entryIndex < numEntries; entryIndex++) {\n      const entryName = this.metadataQuerier.GetEntryName(dracoMetadata, entryIndex);\n      result[entryName] = this._getDracoMetadataField(dracoMetadata, entryName);\n    }\n    return result;\n  }\n\n  /**\n   * Extracts possible values for one metadata entry by name\n   * @param dracoMetadata\n   * @param entryName\n   */\n  _getDracoMetadataField(dracoMetadata: Metadata, entryName: string): DracoMetadataEntry {\n    const dracoArray = new this.draco.DracoInt32Array();\n    try {\n      // Draco metadata fields can hold int32 arrays\n      this.metadataQuerier.GetIntEntryArray(dracoMetadata, entryName, dracoArray);\n      const intArray = getInt32Array(dracoArray);\n      return {\n        int: this.metadataQuerier.GetIntEntry(dracoMetadata, entryName),\n        string: this.metadataQuerier.GetStringEntry(dracoMetadata, entryName),\n        double: this.metadataQuerier.GetDoubleEntry(dracoMetadata, entryName),\n        intArray\n      };\n    } finally {\n      this.draco.destroy(dracoArray);\n    }\n  }\n\n  // QUANTIZED ATTRIBUTE SUPPORT (NO DECOMPRESSION)\n\n  /** Skip transforms for specific attribute types */\n  _disableAttributeTransforms(options: DracoParseOptions) {\n    const {quantizedAttributes = [], octahedronAttributes = []} = options;\n    const skipAttributes = [...quantizedAttributes, ...octahedronAttributes];\n    for (const dracoAttributeName of skipAttributes) {\n      this.decoder.SkipAttributeTransform(this.draco[dracoAttributeName]);\n    }\n  }\n\n  /**\n   * Extract (and apply?) Position Transform\n   * @todo not used\n   */\n  _getQuantizationTransform(\n    dracoAttribute: PointAttribute,\n    options: DracoParseOptions\n  ): DracoQuantizationTransform | null {\n    const {quantizedAttributes = []} = options;\n    const attribute_type = dracoAttribute.attribute_type();\n    const skip = quantizedAttributes.map((type) => this.decoder[type]).includes(attribute_type);\n    if (skip) {\n      const transform = new this.draco.AttributeQuantizationTransform();\n      try {\n        if (transform.InitFromAttribute(dracoAttribute)) {\n          return {\n            quantization_bits: transform.quantization_bits(),\n            range: transform.range(),\n            min_values: new Float32Array([1, 2, 3]).map((i) => transform.min_value(i))\n          };\n        }\n      } finally {\n        this.draco.destroy(transform);\n      }\n    }\n    return null;\n  }\n\n  _getOctahedronTransform(\n    dracoAttribute: PointAttribute,\n    options: DracoParseOptions\n  ): DracoOctahedronTransform | null {\n    const {octahedronAttributes = []} = options;\n    const attribute_type = dracoAttribute.attribute_type();\n    const octahedron = octahedronAttributes\n      .map((type) => this.decoder[type])\n      .includes(attribute_type);\n    if (octahedron) {\n      const transform = new this.draco.AttributeQuantizationTransform();\n      try {\n        if (transform.InitFromAttribute(dracoAttribute)) {\n          return {\n            quantization_bits: transform.quantization_bits()\n          };\n        }\n      } finally {\n        this.draco.destroy(transform);\n      }\n    }\n    return null;\n  }\n\n  // HELPERS\n}\n\n/**\n * Get draco specific data type by TypedArray constructor type\n * @param attributeType\n * @returns draco specific data type\n */\nfunction getDracoDataType(draco: Draco3D, attributeType: any): draco_DataType {\n  switch (attributeType) {\n    case Float32Array:\n      return draco.DT_FLOAT32;\n    case Int8Array:\n      return draco.DT_INT8;\n    case Int16Array:\n      return draco.DT_INT16;\n    case Int32Array:\n      return draco.DT_INT32;\n    case Uint8Array:\n      return draco.DT_UINT8;\n    case Uint16Array:\n      return draco.DT_UINT16;\n    case Uint32Array:\n      return draco.DT_UINT32;\n    default:\n      return draco.DT_INVALID;\n  }\n}\n\n/**\n * Copy a Draco int32 array into a JS typed array\n */\nfunction getInt32Array(dracoArray: DracoInt32Array): Int32Array {\n  const numValues = dracoArray.size();\n  const intArray = new Int32Array(numValues);\n  for (let i = 0; i < numValues; i++) {\n    intArray[i] = dracoArray.GetValue(i);\n  }\n  return intArray;\n}\n\n/**\n * Copy a Draco int32 array into a JS typed array\n */\nfunction getUint32Array(dracoArray: DracoInt32Array): Int32Array {\n  const numValues = dracoArray.size();\n  const intArray = new Int32Array(numValues);\n  for (let i = 0; i < numValues; i++) {\n    intArray[i] = dracoArray.GetValue(i);\n  }\n  return intArray;\n}\n"], "mappings": ";;;;;;;;AA2BA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,eAAA,GAAAD,OAAA;AAmBA,MAAME,aAAa,GAAG;EACpBC,eAAe,EAAE,CAAC;EAClBC,WAAW,EAAE;AACf,CAAC;AAGD,MAAMC,gCAAgC,GAAG;EACvCC,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,SAAS;EAChBC,SAAS,EAAE;AACb,CAAC;AAED,MAAMC,kCAAkC,GAAG;EACzC,CAAC,EAAEC,SAAS;EACZ,CAAC,EAAEC,UAAU;EACb,CAAC,EAAEC,UAAU;EACb,CAAC,EAAEC,WAAW;EACd,CAAC,EAAEC,UAAU;EACb,CAAC,EAAEC,WAAW;EACd,CAAC,EAAEC;AACL,CAAC;AAED,MAAMC,eAAe,GAAG,CAAC;AAEV,MAAMC,WAAW,CAAC;EAM/BC,WAAWA,CAACC,KAAc,EAAE;IAAA,IAAAC,gBAAA,CAAAC,OAAA;IAAA,IAAAD,gBAAA,CAAAC,OAAA;IAAA,IAAAD,gBAAA,CAAAC,OAAA;IAC1B,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACG,OAAO,GAAG,IAAI,IAAI,CAACH,KAAK,CAACI,OAAO,CAAC,CAAC;IACvC,IAAI,CAACC,eAAe,GAAG,IAAI,IAAI,CAACL,KAAK,CAACM,eAAe,CAAC,CAAC;EACzD;EAKAC,OAAOA,CAAA,EAAS;IACd,IAAI,CAACP,KAAK,CAACO,OAAO,CAAC,IAAI,CAACJ,OAAO,CAAC;IAChC,IAAI,CAACH,KAAK,CAACO,OAAO,CAAC,IAAI,CAACF,eAAe,CAAC;EAC1C;EAOAG,SAASA,CAACC,WAAwB,EAA8C;IAAA,IAA5CC,OAA0B,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IACjE,MAAMG,MAAM,GAAG,IAAI,IAAI,CAACd,KAAK,CAACe,aAAa,CAAC,CAAC;IAC7CD,MAAM,CAACE,IAAI,CAAC,IAAI1B,SAAS,CAACmB,WAAW,CAAC,EAAEA,WAAW,CAACQ,UAAU,CAAC;IAE/D,IAAI,CAACC,2BAA2B,CAACR,OAAO,CAAC;IAEzC,MAAMS,aAAa,GAAG,IAAI,CAAChB,OAAO,CAACiB,sBAAsB,CAACN,MAAM,CAAC;IACjE,MAAMO,aAAa,GACjBF,aAAa,KAAK,IAAI,CAACnB,KAAK,CAAClB,eAAe,GACxC,IAAI,IAAI,CAACkB,KAAK,CAACsB,IAAI,CAAC,CAAC,GACrB,IAAI,IAAI,CAACtB,KAAK,CAACuB,UAAU,CAAC,CAAC;IAEjC,IAAI;MACF,IAAIC,WAAW;MACf,QAAQL,aAAa;QACnB,KAAK,IAAI,CAACnB,KAAK,CAAClB,eAAe;UAC7B0C,WAAW,GAAG,IAAI,CAACrB,OAAO,CAACsB,kBAAkB,CAACX,MAAM,EAAEO,aAAqB,CAAC;UAC5E;QAEF,KAAK,IAAI,CAACrB,KAAK,CAACjB,WAAW;UACzByC,WAAW,GAAG,IAAI,CAACrB,OAAO,CAACuB,wBAAwB,CAACZ,MAAM,EAAEO,aAAa,CAAC;UAC1E;QAEF;UACE,MAAM,IAAIM,KAAK,CAAC,+BAA+B,CAAC;MACpD;MAEA,IAAI,CAACH,WAAW,CAACI,EAAE,CAAC,CAAC,IAAI,CAACP,aAAa,CAACQ,GAAG,EAAE;QAC3C,MAAMC,OAAO,kCAAAC,MAAA,CAAkCP,WAAW,CAACQ,SAAS,CAAC,CAAC,CAAE;QAExE,MAAM,IAAIL,KAAK,CAACG,OAAO,CAAC;MAC1B;MAEA,MAAMG,UAAU,GAAG,IAAI,CAACC,mBAAmB,CAACb,aAAa,EAAEF,aAAa,EAAET,OAAO,CAAC;MAElF,MAAMyB,QAAQ,GAAG,IAAI,CAACC,YAAY,CAACf,aAAa,EAAEY,UAAU,EAAEvB,OAAO,CAAC;MAEtE,MAAM2B,WAAW,GAAG,IAAAC,0BAAkB,EAACH,QAAQ,CAACI,UAAU,CAAC;MAE3D,MAAMC,MAAM,GAAG,IAAAC,8BAAc,EAACN,QAAQ,CAACI,UAAU,EAAEN,UAAU,EAAEE,QAAQ,CAACO,OAAO,CAAC;MAEhF,MAAMC,IAAe,GAAG;QACtBC,MAAM,EAAE,OAAO;QACfX,UAAU;QACVY,MAAM,EAAE;UACNC,WAAW,EAAEzB,aAAa,CAAC0B,UAAU,CAAC,CAAC;UACvCV;QACF,CAAC;QACD,GAAGF,QAAQ;QACXK;MACF,CAAC;MACD,OAAOG,IAAI;IACb,CAAC,SAAS;MACR,IAAI,CAAC3C,KAAK,CAACO,OAAO,CAACO,MAAM,CAAC;MAC1B,IAAIO,aAAa,EAAE;QACjB,IAAI,CAACrB,KAAK,CAACO,OAAO,CAACc,aAAa,CAAC;MACnC;IACF;EACF;EAWAa,mBAAmBA,CACjBb,aAAgC,EAChCF,aAAa,EACbT,OAA0B,EACT;IACjB,MAAMsC,QAAQ,GAAG,IAAI,CAACC,oBAAoB,CAAC5B,aAAa,CAAC;IACzD,MAAMkB,UAAU,GAAG,IAAI,CAACW,mBAAmB,CAAC7B,aAAa,EAAEX,OAAO,CAAC;IAEnE,OAAO;MACLS,aAAa;MACbgC,cAAc,EAAE9B,aAAa,CAAC8B,cAAc,CAAC,CAAC;MAC9CJ,UAAU,EAAE1B,aAAa,CAAC0B,UAAU,CAAC,CAAC;MACtCK,SAAS,EAAE/B,aAAa,YAAY,IAAI,CAACrB,KAAK,CAACsB,IAAI,GAAGD,aAAa,CAAC+B,SAAS,CAAC,CAAC,GAAG,CAAC;MACnFJ,QAAQ;MACRT;IACF,CAAC;EACH;EAQAW,mBAAmBA,CACjB7B,aAAgC,EAChCX,OAA0B,EACa;IACvC,MAAM2C,eAAsD,GAAG,CAAC,CAAC;IAEjE,KAAK,IAAIC,WAAW,GAAG,CAAC,EAAEA,WAAW,GAAGjC,aAAa,CAAC8B,cAAc,CAAC,CAAC,EAAEG,WAAW,EAAE,EAAE;MAGrF,MAAMC,cAAc,GAAG,IAAI,CAACpD,OAAO,CAACqD,YAAY,CAACnC,aAAa,EAAEiC,WAAW,CAAC;MAE5E,MAAMN,QAAQ,GAAG,IAAI,CAACS,qBAAqB,CAACpC,aAAa,EAAEiC,WAAW,CAAC;MAEvED,eAAe,CAACE,cAAc,CAACG,SAAS,CAAC,CAAC,CAAC,GAAG;QAC5CA,SAAS,EAAEH,cAAc,CAACG,SAAS,CAAC,CAAC;QACrCC,cAAc,EAAEJ,cAAc,CAACI,cAAc,CAAC,CAAC;QAC/CC,SAAS,EAAEL,cAAc,CAACK,SAAS,CAAC,CAAC;QACrCC,cAAc,EAAEN,cAAc,CAACM,cAAc,CAAC,CAAC;QAE/CC,WAAW,EAAEP,cAAc,CAACO,WAAW,CAAC,CAAC;QACzCC,WAAW,EAAER,cAAc,CAACQ,WAAW,CAAC,CAAC;QACzCC,UAAU,EAAET,cAAc,CAACS,UAAU,CAAC,CAAC;QACvCC,eAAe,EAAEX,WAAW;QAE5BN;MACF,CAAC;MAGD,MAAMkB,YAAY,GAAG,IAAI,CAACC,yBAAyB,CAACZ,cAAc,EAAE7C,OAAO,CAAC;MAC5E,IAAIwD,YAAY,EAAE;QAChBb,eAAe,CAACE,cAAc,CAACG,SAAS,CAAC,CAAC,CAAC,CAACU,sBAAsB,GAAGF,YAAY;MACnF;MAEA,MAAMG,UAAU,GAAG,IAAI,CAACC,uBAAuB,CAACf,cAAc,EAAE7C,OAAO,CAAC;MACxE,IAAI2D,UAAU,EAAE;QACdhB,eAAe,CAACE,cAAc,CAACG,SAAS,CAAC,CAAC,CAAC,CAACa,oBAAoB,GAAGF,UAAU;MAC/E;IACF;IAEA,OAAOhB,eAAe;EACxB;EAQAjB,YAAYA,CACVf,aAAgC,EAChCY,UAA2B,EAC3BvB,OAA0B,EACZ;IACd,MAAM6B,UAAU,GAAG,IAAI,CAACiC,kBAAkB,CAACvC,UAAU,EAAEZ,aAAa,EAAEX,OAAO,CAAC;IAE9E,MAAM+D,iBAAiB,GAAGlC,UAAU,CAACtD,QAAQ;IAC7C,IAAI,CAACwF,iBAAiB,EAAE;MACtB,MAAM,IAAI9C,KAAK,CAAC,qCAAqC,CAAC;IACxD;IAGA,IAAIN,aAAa,YAAY,IAAI,CAACrB,KAAK,CAACsB,IAAI,EAAE;MAC5C,QAAQZ,OAAO,CAACgE,QAAQ;QACtB,KAAK,gBAAgB;UACnB,OAAO;YACLA,QAAQ,EAAE,gBAAgB;YAC1BC,IAAI,EAAE,CAAC;YACPpC,UAAU;YACVG,OAAO,EAAE;cACPkC,KAAK,EAAE,IAAI,CAACC,wBAAwB,CAACxD,aAAa,CAAC;cACnDyD,IAAI,EAAE;YACR;UACF,CAAC;QACH,KAAK,eAAe;QACpB;UACE,OAAO;YACLJ,QAAQ,EAAE,eAAe;YACzBC,IAAI,EAAE,CAAC;YACPpC,UAAU;YACVG,OAAO,EAAE;cACPkC,KAAK,EAAE,IAAI,CAACG,uBAAuB,CAAC1D,aAAa,CAAC;cAClDyD,IAAI,EAAE;YACR;UACF,CAAC;MACL;IACF;IAGA,OAAO;MACLJ,QAAQ,EAAE,YAAY;MACtBC,IAAI,EAAE,CAAC;MACPpC;IACF,CAAC;EACH;EAEAiC,kBAAkBA,CAChBvC,UAA2B,EAC3BZ,aAAgC,EAChCX,OAA0B,EACgB;IAC1C,MAAM6B,UAA0C,GAAG,CAAC,CAAC;IAErD,KAAK,MAAMyC,eAAe,IAAIC,MAAM,CAACC,MAAM,CAACjD,UAAU,CAACM,UAAU,CAAC,EAAE;MAClE,MAAM4C,aAAa,GAAG,IAAI,CAACC,oBAAoB,CAACJ,eAAe,EAAEtE,OAAO,CAAC;MACzEsE,eAAe,CAACK,IAAI,GAAGF,aAAa;MACpC,MAAM;QAACP,KAAK;QAAEE;MAAI,CAAC,GAAG,IAAI,CAACQ,mBAAmB,CAACjE,aAAa,EAAE2D,eAAe,CAAC;MAC9EzC,UAAU,CAAC4C,aAAa,CAAC,GAAG;QAC1BP,KAAK;QACLE,IAAI;QACJS,UAAU,EAAEP,eAAe,CAAClB,WAAW;QACvC0B,UAAU,EAAER,eAAe,CAACjB,WAAW;QACvCC,UAAU,EAAEgB,eAAe,CAAChB;MAC9B,CAAC;IACH;IAEA,OAAOzB,UAAU;EACnB;EAQAwC,uBAAuBA,CAAC1D,aAAmB,EAAE;IAE3C,MAAMoE,QAAQ,GAAGpE,aAAa,CAAC+B,SAAS,CAAC,CAAC;IAC1C,MAAMsC,UAAU,GAAGD,QAAQ,GAAG,CAAC;IAC/B,MAAMxE,UAAU,GAAGyE,UAAU,GAAG7F,eAAe;IAE/C,MAAMgC,GAAG,GAAG,IAAI,CAAC7B,KAAK,CAAC2F,OAAO,CAAC1E,UAAU,CAAC;IAC1C,IAAI;MACF,IAAI,CAACd,OAAO,CAACyF,uBAAuB,CAACvE,aAAa,EAAEJ,UAAU,EAAEY,GAAG,CAAC;MACpE,OAAO,IAAIlC,WAAW,CAAC,IAAI,CAACK,KAAK,CAAC6F,OAAO,CAAC/E,MAAM,EAAEe,GAAG,EAAE6D,UAAU,CAAC,CAACI,KAAK,CAAC,CAAC;IAC5E,CAAC,SAAS;MACR,IAAI,CAAC9F,KAAK,CAAC+F,KAAK,CAAClE,GAAG,CAAC;IACvB;EACF;EAMAgD,wBAAwBA,CAACxD,aAAmB,EAAE;IAC5C,MAAM2E,UAAU,GAAG,IAAI,IAAI,CAAChG,KAAK,CAACiG,eAAe,CAAC,CAAC;IACnD,IAAI;MACsB,IAAI,CAAC9F,OAAO,CAAC+F,yBAAyB,CAAC7E,aAAa,EAAE2E,UAAU,CAAC;MACzF,OAAOG,cAAc,CAACH,UAAU,CAAC;IACnC,CAAC,SAAS;MACR,IAAI,CAAChG,KAAK,CAACO,OAAO,CAACyF,UAAU,CAAC;IAChC;EACF;EAQAV,mBAAmBA,CACjBjE,aAAgC,EAChC+E,SAAyB,EACU;IACnC,MAAMC,cAAc,GAAGhH,kCAAkC,CAAC+G,SAAS,CAACxC,SAAS,CAAC;IAC9E,MAAM0C,aAAa,GAAGF,SAAS,CAACvC,cAAc;IAC9C,MAAM0C,SAAS,GAAGlF,aAAa,CAAC0B,UAAU,CAAC,CAAC;IAC5C,MAAMyD,SAAS,GAAGD,SAAS,GAAGD,aAAa;IAE3C,MAAMrF,UAAU,GAAGuF,SAAS,GAAGH,cAAc,CAACI,iBAAiB;IAC/D,MAAMC,QAAQ,GAAGC,gBAAgB,CAAC,IAAI,CAAC3G,KAAK,EAAEqG,cAAc,CAAC;IAE7D,IAAIzB,KAAiB;IAErB,MAAM/C,GAAG,GAAG,IAAI,CAAC7B,KAAK,CAAC2F,OAAO,CAAC1E,UAAU,CAAC;IAC1C,IAAI;MACF,MAAMsC,cAAc,GAAG,IAAI,CAACpD,OAAO,CAACqD,YAAY,CAACnC,aAAa,EAAE+E,SAAS,CAACnC,eAAe,CAAC;MAC1F,IAAI,CAAC9D,OAAO,CAACyG,iCAAiC,CAC5CvF,aAAa,EACbkC,cAAc,EACdmD,QAAQ,EACRzF,UAAU,EACVY,GACF,CAAC;MACD+C,KAAK,GAAG,IAAIyB,cAAc,CAAC,IAAI,CAACrG,KAAK,CAAC6F,OAAO,CAAC/E,MAAM,EAAEe,GAAG,EAAE2E,SAAS,CAAC,CAACV,KAAK,CAAC,CAAC;IAC/E,CAAC,SAAS;MACR,IAAI,CAAC9F,KAAK,CAAC+F,KAAK,CAAClE,GAAG,CAAC;IACvB;IAEA,OAAO;MAAC+C,KAAK;MAAEE,IAAI,EAAEwB;IAAa,CAAC;EACrC;EA4BAlB,oBAAoBA,CAACgB,SAAyB,EAAE1F,OAA0B,EAAU;IAElF,MAAMmG,QAAQ,GAAGT,SAAS,CAAC1C,SAAS;IACpC,KAAK,MAAM,CAACyB,aAAa,EAAE2B,iBAAiB,CAAC,IAAI7B,MAAM,CAAC8B,OAAO,CAC7DrG,OAAO,CAACsG,eAAe,IAAI,CAAC,CAC9B,CAAC,EAAE;MACD,IAAIF,iBAAiB,KAAKD,QAAQ,EAAE;QAClC,OAAO1B,aAAa;MACtB;IACF;IAGA,MAAM8B,iBAAiB,GAAGb,SAAS,CAACzC,cAAc;IAClD,KAAK,MAAMuD,sBAAsB,IAAIlI,gCAAgC,EAAE;MACrE,MAAMmI,aAAa,GAAG,IAAI,CAACnH,KAAK,CAACkH,sBAAsB,CAAC;MACxD,IAAIC,aAAa,KAAKF,iBAAiB,EAAE;QAGvC,OAAOjI,gCAAgC,CAACkI,sBAAsB,CAAC;MACjE;IACF;IAIA,MAAME,SAAS,GAAG1G,OAAO,CAAC2G,kBAAkB,IAAI,MAAM;IACtD,IAAIjB,SAAS,CAACpD,QAAQ,CAACoE,SAAS,CAAC,EAAE;MACjC,OAAOhB,SAAS,CAACpD,QAAQ,CAACoE,SAAS,CAAC,CAACE,MAAM;IAC7C;IAGA,2BAAAvF,MAAA,CAA2B8E,QAAQ;EACrC;EAKA5D,oBAAoBA,CAAC5B,aAAgC,EAAE;IACrD,MAAMkG,aAAa,GAAG,IAAI,CAACpH,OAAO,CAACqH,WAAW,CAACnG,aAAa,CAAC;IAC7D,OAAO,IAAI,CAACoG,iBAAiB,CAACF,aAAa,CAAC;EAC9C;EAGA9D,qBAAqBA,CAACpC,aAAgC,EAAEiC,WAAmB,EAAE;IAC3E,MAAMiE,aAAa,GAAG,IAAI,CAACpH,OAAO,CAACuH,oBAAoB,CAACrG,aAAa,EAAEiC,WAAW,CAAC;IACnF,OAAO,IAAI,CAACmE,iBAAiB,CAACF,aAAa,CAAC;EAC9C;EAOAE,iBAAiBA,CAACF,aAAuB,EAAyC;IAEhF,IAAI,CAACA,aAAa,IAAI,CAACA,aAAa,CAAC1F,GAAG,EAAE;MACxC,OAAO,CAAC,CAAC;IACX;IACA,MAAM8F,MAAM,GAAG,CAAC,CAAC;IACjB,MAAMC,UAAU,GAAG,IAAI,CAACvH,eAAe,CAACwH,UAAU,CAACN,aAAa,CAAC;IACjE,KAAK,IAAIO,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAGF,UAAU,EAAEE,UAAU,EAAE,EAAE;MAC9D,MAAMV,SAAS,GAAG,IAAI,CAAC/G,eAAe,CAAC0H,YAAY,CAACR,aAAa,EAAEO,UAAU,CAAC;MAC9EH,MAAM,CAACP,SAAS,CAAC,GAAG,IAAI,CAACY,sBAAsB,CAACT,aAAa,EAAEH,SAAS,CAAC;IAC3E;IACA,OAAOO,MAAM;EACf;EAOAK,sBAAsBA,CAACT,aAAuB,EAAEH,SAAiB,EAAsB;IACrF,MAAMpB,UAAU,GAAG,IAAI,IAAI,CAAChG,KAAK,CAACiG,eAAe,CAAC,CAAC;IACnD,IAAI;MAEF,IAAI,CAAC5F,eAAe,CAAC4H,gBAAgB,CAACV,aAAa,EAAEH,SAAS,EAAEpB,UAAU,CAAC;MAC3E,MAAMkC,QAAQ,GAAGC,aAAa,CAACnC,UAAU,CAAC;MAC1C,OAAO;QACLoC,GAAG,EAAE,IAAI,CAAC/H,eAAe,CAACgI,WAAW,CAACd,aAAa,EAAEH,SAAS,CAAC;QAC/DE,MAAM,EAAE,IAAI,CAACjH,eAAe,CAACiI,cAAc,CAACf,aAAa,EAAEH,SAAS,CAAC;QACrEmB,MAAM,EAAE,IAAI,CAAClI,eAAe,CAACmI,cAAc,CAACjB,aAAa,EAAEH,SAAS,CAAC;QACrEc;MACF,CAAC;IACH,CAAC,SAAS;MACR,IAAI,CAAClI,KAAK,CAACO,OAAO,CAACyF,UAAU,CAAC;IAChC;EACF;EAKA9E,2BAA2BA,CAACR,OAA0B,EAAE;IACtD,MAAM;MAAC+H,mBAAmB,GAAG,EAAE;MAAEC,oBAAoB,GAAG;IAAE,CAAC,GAAGhI,OAAO;IACrE,MAAMiI,cAAc,GAAG,CAAC,GAAGF,mBAAmB,EAAE,GAAGC,oBAAoB,CAAC;IACxE,KAAK,MAAME,kBAAkB,IAAID,cAAc,EAAE;MAC/C,IAAI,CAACxI,OAAO,CAAC0I,sBAAsB,CAAC,IAAI,CAAC7I,KAAK,CAAC4I,kBAAkB,CAAC,CAAC;IACrE;EACF;EAMAzE,yBAAyBA,CACvBZ,cAA8B,EAC9B7C,OAA0B,EACS;IACnC,MAAM;MAAC+H,mBAAmB,GAAG;IAAE,CAAC,GAAG/H,OAAO;IAC1C,MAAMiD,cAAc,GAAGJ,cAAc,CAACI,cAAc,CAAC,CAAC;IACtD,MAAMmF,IAAI,GAAGL,mBAAmB,CAACM,GAAG,CAAEC,IAAI,IAAK,IAAI,CAAC7I,OAAO,CAAC6I,IAAI,CAAC,CAAC,CAACC,QAAQ,CAACtF,cAAc,CAAC;IAC3F,IAAImF,IAAI,EAAE;MACR,MAAMI,SAAS,GAAG,IAAI,IAAI,CAAClJ,KAAK,CAACmJ,8BAA8B,CAAC,CAAC;MACjE,IAAI;QACF,IAAID,SAAS,CAACE,iBAAiB,CAAC7F,cAAc,CAAC,EAAE;UAC/C,OAAO;YACL8F,iBAAiB,EAAEH,SAAS,CAACG,iBAAiB,CAAC,CAAC;YAChDC,KAAK,EAAEJ,SAAS,CAACI,KAAK,CAAC,CAAC;YACxBC,UAAU,EAAE,IAAI3J,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAACmJ,GAAG,CAAES,CAAC,IAAKN,SAAS,CAACO,SAAS,CAACD,CAAC,CAAC;UAC3E,CAAC;QACH;MACF,CAAC,SAAS;QACR,IAAI,CAACxJ,KAAK,CAACO,OAAO,CAAC2I,SAAS,CAAC;MAC/B;IACF;IACA,OAAO,IAAI;EACb;EAEA5E,uBAAuBA,CACrBf,cAA8B,EAC9B7C,OAA0B,EACO;IACjC,MAAM;MAACgI,oBAAoB,GAAG;IAAE,CAAC,GAAGhI,OAAO;IAC3C,MAAMiD,cAAc,GAAGJ,cAAc,CAACI,cAAc,CAAC,CAAC;IACtD,MAAMU,UAAU,GAAGqE,oBAAoB,CACpCK,GAAG,CAAEC,IAAI,IAAK,IAAI,CAAC7I,OAAO,CAAC6I,IAAI,CAAC,CAAC,CACjCC,QAAQ,CAACtF,cAAc,CAAC;IAC3B,IAAIU,UAAU,EAAE;MACd,MAAM6E,SAAS,GAAG,IAAI,IAAI,CAAClJ,KAAK,CAACmJ,8BAA8B,CAAC,CAAC;MACjE,IAAI;QACF,IAAID,SAAS,CAACE,iBAAiB,CAAC7F,cAAc,CAAC,EAAE;UAC/C,OAAO;YACL8F,iBAAiB,EAAEH,SAAS,CAACG,iBAAiB,CAAC;UACjD,CAAC;QACH;MACF,CAAC,SAAS;QACR,IAAI,CAACrJ,KAAK,CAACO,OAAO,CAAC2I,SAAS,CAAC;MAC/B;IACF;IACA,OAAO,IAAI;EACb;AAGF;AAACQ,OAAA,CAAAxJ,OAAA,GAAAJ,WAAA;AAOD,SAAS6G,gBAAgBA,CAAC3G,KAAc,EAAEmH,aAAkB,EAAkB;EAC5E,QAAQA,aAAa;IACnB,KAAKvH,YAAY;MACf,OAAOI,KAAK,CAAC2J,UAAU;IACzB,KAAKrK,SAAS;MACZ,OAAOU,KAAK,CAAC4J,OAAO;IACtB,KAAKpK,UAAU;MACb,OAAOQ,KAAK,CAAC6J,QAAQ;IACvB,KAAKnK,UAAU;MACb,OAAOM,KAAK,CAAC8J,QAAQ;IACvB,KAAKvK,UAAU;MACb,OAAOS,KAAK,CAAC+J,QAAQ;IACvB,KAAKtK,WAAW;MACd,OAAOO,KAAK,CAACgK,SAAS;IACxB,KAAKrK,WAAW;MACd,OAAOK,KAAK,CAACiK,SAAS;IACxB;MACE,OAAOjK,KAAK,CAACkK,UAAU;EAC3B;AACF;AAKA,SAAS/B,aAAaA,CAACnC,UAA2B,EAAc;EAC9D,MAAMQ,SAAS,GAAGR,UAAU,CAAClB,IAAI,CAAC,CAAC;EACnC,MAAMoD,QAAQ,GAAG,IAAIxI,UAAU,CAAC8G,SAAS,CAAC;EAC1C,KAAK,IAAIgD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhD,SAAS,EAAEgD,CAAC,EAAE,EAAE;IAClCtB,QAAQ,CAACsB,CAAC,CAAC,GAAGxD,UAAU,CAACmE,QAAQ,CAACX,CAAC,CAAC;EACtC;EACA,OAAOtB,QAAQ;AACjB;AAKA,SAAS/B,cAAcA,CAACH,UAA2B,EAAc;EAC/D,MAAMQ,SAAS,GAAGR,UAAU,CAAClB,IAAI,CAAC,CAAC;EACnC,MAAMoD,QAAQ,GAAG,IAAIxI,UAAU,CAAC8G,SAAS,CAAC;EAC1C,KAAK,IAAIgD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhD,SAAS,EAAEgD,CAAC,EAAE,EAAE;IAClCtB,QAAQ,CAACsB,CAAC,CAAC,GAAGxD,UAAU,CAACmE,QAAQ,CAACX,CAAC,CAAC;EACtC;EACA,OAAOtB,QAAQ;AACjB"}