{"version": 3, "file": "draco3d-types.js", "names": ["draco_GeometryAttribute_Type", "exports", "draco_EncodedGeometryType", "draco_DataType", "draco_StatusCode"], "sources": ["../../../src/draco3d/draco3d-types.ts"], "sourcesContent": ["// A set of typescript types manually adapted from the Draco web IDL\n// Draco JS is a bit tricky to work with due to the C++ emscripten code base\n// sparse documentation, so these types provide an extra safety net.\n\n// Typescript defs adapted from draco3d emscripten IDL\n// https://raw.githubusercontent.com/google/draco/master/src/draco/javascript/emscripten/draco_web_decoder.idl\n// Interface exposed to emscripten's WebIDL Binder.\n// http://kripken.github.io/emscripten-site/docs/porting/connecting_cpp_and_javascript/WebIDL-Binder.html\n\n/* eslint-disable camelcase */\n\n/** Draco3D untyped memory pointer */\ntype VoidPtr = any;\n\n// DRACO WEB DECODER IDL\n\n/** Draco3D geometry attribute type */\nexport enum draco_GeometryAttribute_Type {\n  'draco_GeometryAttribute::INVALID',\n  'draco_GeometryAttribute::POSITION',\n  'draco_GeometryAttribute::NORMAL',\n  'draco_GeometryAttribute::COLOR',\n  'draco_GeometryAttribute::TEX_COORD',\n  'draco_GeometryAttribute::GENERIC'\n}\n\n/** Draco3D encoded geometry type */\nexport enum draco_EncodedGeometryType {\n  'draco::INVALID_GEOMETRY_TYPE',\n  'draco::POINT_CLOUD',\n  'draco::TRIANGULAR_MESH'\n}\n\n/** Draco3D data type */\nexport enum draco_DataType {\n  'draco::DT_INVALID',\n  'draco::DT_INT8',\n  'draco::DT_UINT8',\n  'draco::DT_INT16',\n  'draco::DT_UINT16',\n  'draco::DT_INT32',\n  'draco::DT_UINT32',\n  'draco::DT_INT64',\n  'draco::DT_UINT64',\n  'draco::DT_FLOAT32',\n  'draco::DT_FLOAT64',\n  'draco::DT_BOOL',\n  'draco::DT_TYPES_COUNT'\n}\n\n/** Draco3D status code */\nexport enum draco_StatusCode {\n  'draco_Status::OK',\n  'draco_Status::DRACO_ERROR',\n  'draco_Status::IO_ERROR',\n  'draco_Status::INVALID_PARAMETER',\n  'draco_Status::UNSUPPORTED_VERSION',\n  'draco_Status::UNKNOWN_VERSION'\n}\n\n/** Draco3D decoder buffer allocated on emscripten heap */\nexport declare class DecoderBuffer {\n  constructor();\n  Init(data: Int8Array, data_size: number): void;\n}\n\n/** Draco3D attribute transform data */\nexport declare class AttributeTransformData {\n  constructor();\n  transform_type(): number;\n}\n\n/** Draco3D geometry attribute */\nexport declare class GeometryAttribute {\n  constructor();\n}\n\n/** Draco3D point attribute */\nexport declare class PointAttribute {\n  ptr: VoidPtr;\n\n  constructor();\n  size(): number;\n  GetAttributeTransformData(): AttributeTransformData;\n\n  // From GeometryAttribute\n  attribute_type(): number;\n  data_type(): number;\n  num_components(): number;\n  normalized(): boolean;\n  byte_stride(): number;\n  byte_offset(): number;\n  unique_id(): number;\n}\n\n/** Draco3D attribute transform */\nexport declare class AttributeQuantizationTransform {\n  constructor();\n  InitFromAttribute(att: PointAttribute): boolean;\n  quantization_bits(): number;\n  min_value(axis: number): number;\n  range(): number;\n}\n\n/** Draco3D attribute transform */\nexport declare class AttributeOctahedronTransform {\n  constructor();\n  InitFromAttribute(att: PointAttribute): boolean;\n  quantization_bits(): number;\n}\n\n/** Draco3D point cloud */\nexport declare class PointCloud {\n  ptr: VoidPtr;\n\n  constructor();\n  num_attributes(): number;\n  num_points(): number;\n}\n\n/** Draco3D mesh */\nexport declare class Mesh extends PointCloud {\n  constructor();\n  num_faces(): number;\n}\n\n/** Draco3D metadata */\nexport declare class Metadata {\n  ptr: VoidPtr;\n\n  constructor();\n}\n\n/** Draco3D status */\nexport declare class Status {\n  constructor();\n  code(): draco_StatusCode;\n  ok(): boolean;\n  error_msg(): string;\n}\n\n/** Draco3D Float32Array allocated on the emscripten heap */\nexport declare class DracoFloat32Array {\n  constructor();\n  GetValue(index: number): number;\n  size(): number;\n}\n\n/** Draco3D Int8Array allocated on the emscripten heap */\nexport declare class DracoInt8Array {\n  constructor();\n  GetValue(index: number): number;\n  size(): number;\n}\n\n/** Draco3D Uint8Array allocated on the emscripten heap */\nexport declare class DracoUInt8Array {\n  GetValue(index: number): number;\n  size(): number;\n}\n\n/** Draco3D Int16Array allocated on the emscripten heap */\nexport declare class DracoInt16Array {\n  constructor();\n  GetValue(index: number): number;\n  size(): number;\n}\n\n/** Draco3D Uint16Array allocated on the emscripten heap */\nexport declare class DracoUInt16Array {\n  constructor();\n  GetValue(index: number): number;\n  size(): number;\n}\n\n/** Draco3D Int32Array allocated on the emscripten heap */\nexport declare class DracoInt32Array {\n  constructor();\n  GetValue(index: number): number;\n  size(): number;\n}\n\n/** Draco3D Uint32Array allocated on the emscripten heap */\nexport declare class DracoUInt32Array {\n  constructor();\n  GetValue(index: number): number;\n  size(): number;\n}\n\n/** Draco3D metadata querier */\nexport declare class MetadataQuerier {\n  constructor();\n\n  HasEntry(metadata: Metadata, entry_name: string): string;\n  GetIntEntry(metadata: Metadata, entry_name: string);\n  GetIntEntryArray(metadata: Metadata, entry_name: string, out_values: DracoInt32Array);\n  GetDoubleEntry(metadata: Metadata, entry_name: string): number;\n  GetStringEntry(metadata: Metadata, entry_name: string): string;\n\n  NumEntries(metadata: Metadata): number;\n  GetEntryName(metadata: Metadata, entry_id: number): string;\n}\n\n/**\n * Draco3D Decoder class\n */\nexport declare class Decoder {\n  constructor();\n\n  GetEncodedGeometryType(in_buffer: DecoderBuffer): draco_EncodedGeometryType;\n\n  DecodeBufferToPointCloud(in_buffer: DecoderBuffer, out_point_cloud: PointCloud): Status;\n  DecodeBufferToMesh(in_buffer: DecoderBuffer, out_mesh: Mesh): Status;\n\n  GetAttributeId(pc: PointCloud, type: draco_GeometryAttribute_Type): number;\n  GetAttributeIdByName(pc: PointCloud, name: string): number;\n  GetAttributeIdByMetadataEntry(pc: PointCloud, name: string, value: string): number;\n\n  GetAttribute(pc: PointCloud, att_id: number): PointAttribute;\n  GetAttributeByUniqueId(pc: PointCloud, unique_id: number): PointAttribute;\n\n  GetMetadata(pc: PointCloud): Metadata;\n  GetAttributeMetadata(pc: PointCloud, att_id: number): Metadata;\n\n  GetFaceFromMesh(m: Mesh, face_id: number, out_values: DracoInt32Array): boolean;\n  GetTriangleStripsFromMesh(m: Mesh, strip_values: DracoInt32Array);\n\n  GetTrianglesUInt16Array(m: Mesh, out_size: number, out_values: VoidPtr): boolean;\n  GetTrianglesUInt32Array(m: Mesh, out_size: number, out_values: VoidPtr): boolean;\n\n  GetAttributeFloat(pa: PointAttribute, att_index: number, out_values: DracoFloat32Array): boolean;\n\n  GetAttributeFloatForAllPoints(\n    pc: PointCloud,\n    pa: PointAttribute,\n    out_values: DracoFloat32Array\n  ): boolean;\n\n  // Deprecated, use GetAttributeInt32ForAllPoints instead.\n  GetAttributeIntForAllPoints(\n    pc: PointCloud,\n    pa: PointAttribute,\n    out_values: DracoInt32Array\n  ): boolean;\n\n  GetAttributeInt8ForAllPoints(\n    pc: PointCloud,\n    pa: PointAttribute,\n    out_values: DracoInt8Array\n  ): boolean;\n  GetAttributeUInt8ForAllPoints(\n    pc: PointCloud,\n    pa: PointAttribute,\n    out_values: DracoUInt8Array\n  ): boolean;\n  GetAttributeInt16ForAllPoints(\n    pc: PointCloud,\n    pa: PointAttribute,\n    out_values: DracoInt16Array\n  ): boolean;\n  GetAttributeUInt16ForAllPoints(\n    pc: PointCloud,\n    pa: PointAttribute,\n    out_values: DracoUInt16Array\n  ): boolean;\n  GetAttributeInt32ForAllPoints(\n    pc: PointCloud,\n    pa: PointAttribute,\n    out_values: DracoInt32Array\n  ): boolean;\n  GetAttributeUInt32ForAllPoints(\n    pc: PointCloud,\n    pa: PointAttribute,\n    out_values: DracoUInt32Array\n  ): boolean;\n\n  GetAttributeDataArrayForAllPoints(\n    pc: PointCloud,\n    pa: PointAttribute,\n    data_type: draco_DataType,\n    out_size: number,\n    out_values: VoidPtr\n  ): boolean;\n\n  SkipAttributeTransform(att_type: draco_GeometryAttribute_Type): void;\n}\n\n// DRACO WEB ENCODER IDL\n\n/** Draco3D metadata builder */\nexport declare class MetadataBuilder {\n  constructor();\n  AddStringEntry(metadata: Metadata, entry_name: string, entry_value: string);\n  AddIntEntry(metadata: Metadata, entry_name: string, entry_value: number);\n  AddDoubleEntry(metadata: Metadata, entry_name: string, entry_value: number);\n  AddIntEntryArray(\n    metadata: Metadata,\n    entry_name: string,\n    entry_value: Int32Array,\n    num_values: number\n  );\n}\n\n/** Draco3D point cloud builder */\n\nexport declare class PointCloudBuilder {\n  constructor();\n  PointCloudBuilder(): void;\n  AddFloatAttribute(\n    pc: PointCloud,\n    type: draco_GeometryAttribute_Type,\n    num_vertices: number,\n    num_components: number,\n    att_values: Float32Array\n  );\n  AddInt8Attribute(\n    pc: PointCloud,\n    type: draco_GeometryAttribute_Type,\n    num_vertices: number,\n    num_components: number,\n    att_values: Int8Array\n  );\n  AddUInt8Attribute(\n    pc: PointCloud,\n    type: draco_GeometryAttribute_Type,\n    num_vertices: number,\n    num_components: number,\n    att_values: Uint8Array\n  );\n  AddInt16Attribute(\n    pc: PointCloud,\n    type: draco_GeometryAttribute_Type,\n    num_vertices: number,\n    num_components: number,\n    att_values: Int16Array\n  );\n  AddUInt16Attribute(\n    pc: PointCloud,\n    type: draco_GeometryAttribute_Type,\n    num_vertices: number,\n    num_components: number,\n    att_values: Uint16Array\n  );\n  AddInt32Attribute(\n    pc: PointCloud,\n    type: draco_GeometryAttribute_Type,\n    num_vertices: number,\n    num_components: number,\n    att_values: Int32Array\n  );\n  AddUInt32Attribute(\n    pc: PointCloud,\n    type: draco_GeometryAttribute_Type,\n    num_vertices: number,\n    num_components: number,\n    att_values: Uint32Array\n  );\n\n  AddMetadata(pc: PointCloud, metadata: Metadata): boolean;\n  SetMetadataForAttribute(pc: PointCloud, attribute_id: number, metadata: Metadata);\n}\n\n/** Draco3D mesh builder */\nexport declare class MeshBuilder extends PointCloudBuilder {\n  constructor();\n  AddFacesToMesh(mesh: Mesh, num_faces: number, faces: number[]): boolean;\n}\n\n/** Draco3D encoder */\nexport declare class Encoder {\n  constructor();\n  Encoder(): void;\n  SetEncodingMethod(method: number): void;\n  SetAttributeQuantization(type: draco_GeometryAttribute_Type, quantization_bits: number);\n  SetAttributeExplicitQuantization(\n    type: draco_GeometryAttribute_Type,\n    quantization_bits: number,\n    num_components: number,\n    origin: number[],\n    range: number\n  );\n  SetSpeedOptions(encoding_speed: number, decoding_speed: number): void;\n  SetTrackEncodedProperties(flag: boolean): void;\n\n  EncodeMeshToDracoBuffer(mesh: Mesh, encoded_data: DracoInt8Array);\n  EncodePointCloudToDracoBuffer(\n    pc: PointCloud,\n    deduplicate_values: boolean,\n    encoded_data: DracoInt8Array\n  );\n\n  // Returns the number of encoded points or faces from the last Encode\n  // operation. Returns 0 if SetTrackEncodedProperties was not set to true.\n  GetNumberOfEncodedPoints(): number;\n  GetNumberOfEncodedFaces(): number;\n}\n\n/** Draco3D expert encoder */\nexport declare class ExpertEncoder {\n  constructor();\n  ExpertEncoder(pc: PointCloud): void;\n  SetEncodingMethod(method: number): void;\n  SetAttributeQuantization(att_id: number, quantization_bits: number);\n  SetAttributeExplicitQuantization(\n    att_id: number,\n    quantization_bits: number,\n    num_components: number,\n    origin: number[],\n    range: number\n  );\n  SetSpeedOptions(encoding_speed: number, decoding_speed: number): void;\n  SetTrackEncodedProperties(flag: boolean): void;\n\n  EncodeToDracoBuffer(deduplicate_values: boolean, encoded_data: DracoInt8Array);\n\n  // Returns the number of encoded points or faces from the last Encode\n  // operation. Returns 0 if SetTrackEncodedProperties was not set to true.\n  GetNumberOfEncodedPoints(): number;\n  GetNumberOfEncodedFaces(): number;\n}\n\n/** Draco3D module interface */\nexport interface Draco3D {\n  // ENUMS\n\n  // draco_EncodedGeometryType\n  readonly INVALID_GEOMETRY_TYPE: draco_EncodedGeometryType;\n  readonly POINT_CLOUD: draco_EncodedGeometryType;\n  readonly TRIANGULAR_MESH: draco_EncodedGeometryType;\n\n  // enum draco_GeometryAttribute_Type\n  readonly INVALID: draco_GeometryAttribute_Type;\n  readonly POSITION: draco_GeometryAttribute_Type;\n  readonly NORMAL: draco_GeometryAttribute_Type;\n  readonly COLOR: draco_GeometryAttribute_Type;\n  readonly TEX_COORD: draco_GeometryAttribute_Type;\n  readonly GENERIC: draco_GeometryAttribute_Type;\n\n  // enum draco_DataType\n  readonly DT_INVALID: draco_DataType;\n  readonly DT_INT8: draco_DataType;\n  readonly DT_UINT8: draco_DataType;\n  readonly DT_INT16: draco_DataType;\n  readonly DT_UINT16: draco_DataType;\n  readonly DT_INT32: draco_DataType;\n  readonly DT_UINT32: draco_DataType;\n  readonly DT_INT64: draco_DataType;\n  readonly DT_UINT64: draco_DataType;\n  readonly DT_FLOAT32: draco_DataType;\n  readonly DT_FLOAT64: draco_DataType;\n  readonly DT_BOOL: draco_DataType;\n  readonly DT_TYPES_COUNT: draco_DataType;\n\n  readonly Mesh: typeof Mesh;\n  readonly PointCloud: typeof PointCloud;\n  readonly Metadata: typeof Metadata;\n\n  readonly Encoder: typeof Encoder;\n  readonly MeshBuilder: typeof MeshBuilder;\n  readonly MetadataBuilder: typeof MetadataBuilder;\n\n  readonly MetadataQuerier: typeof MetadataQuerier;\n  readonly Decoder: typeof Decoder;\n  readonly DecoderBuffer: typeof DecoderBuffer;\n\n  readonly DracoFloat32Array: typeof DracoFloat32Array;\n  readonly DracoInt8Array: typeof DracoInt8Array;\n  readonly DracoUInt8Array: typeof DracoUInt8Array;\n  readonly DracoInt16Array: typeof DracoInt16Array;\n  readonly DracoUInt16Array: typeof DracoUInt16Array;\n  readonly DracoInt32Array: typeof DracoInt32Array;\n  readonly DracoUInt32Array: typeof DracoUInt32Array;\n\n  readonly AttributeQuantizationTransform: typeof AttributeQuantizationTransform;\n\n  // createEncoderModule(): Encoder;\n  // createDecoderModule(): Decoder;\n  destroy(resource: any): void;\n  _malloc(byteLength: number): number;\n  _free(ptr: number): void;\n\n  HEAPF32: {\n    buffer: ArrayBuffer;\n  };\n}\n"], "mappings": ";;;;;;IAiBYA,4BAA4B,aAA5BA,4BAA4B;EAA5BA,4BAA4B,CAA5BA,4BAA4B;EAA5BA,4BAA4B,CAA5BA,4BAA4B;EAA5BA,4BAA4B,CAA5BA,4BAA4B;EAA5BA,4BAA4B,CAA5BA,4BAA4B;EAA5BA,4BAA4B,CAA5BA,4BAA4B;EAA5BA,4BAA4B,CAA5BA,4BAA4B;EAAA,OAA5BA,4BAA4B;AAAA;AAAAC,OAAA,CAAAD,4BAAA,GAAAA,4BAAA;AAAA,IAU5BE,yBAAyB,aAAzBA,yBAAyB;EAAzBA,yBAAyB,CAAzBA,yBAAyB;EAAzBA,yBAAyB,CAAzBA,yBAAyB;EAAzBA,yBAAyB,CAAzBA,yBAAyB;EAAA,OAAzBA,yBAAyB;AAAA;AAAAD,OAAA,CAAAC,yBAAA,GAAAA,yBAAA;AAAA,IAOzBC,cAAc,aAAdA,cAAc;EAAdA,cAAc,CAAdA,cAAc;EAAdA,cAAc,CAAdA,cAAc;EAAdA,cAAc,CAAdA,cAAc;EAAdA,cAAc,CAAdA,cAAc;EAAdA,cAAc,CAAdA,cAAc;EAAdA,cAAc,CAAdA,cAAc;EAAdA,cAAc,CAAdA,cAAc;EAAdA,cAAc,CAAdA,cAAc;EAAdA,cAAc,CAAdA,cAAc;EAAdA,cAAc,CAAdA,cAAc;EAAdA,cAAc,CAAdA,cAAc;EAAdA,cAAc,CAAdA,cAAc;EAAdA,cAAc,CAAdA,cAAc;EAAA,OAAdA,cAAc;AAAA;AAAAF,OAAA,CAAAE,cAAA,GAAAA,cAAA;AAAA,IAiBdC,gBAAgB,aAAhBA,gBAAgB;EAAhBA,gBAAgB,CAAhBA,gBAAgB;EAAhBA,gBAAgB,CAAhBA,gBAAgB;EAAhBA,gBAAgB,CAAhBA,gBAAgB;EAAhBA,gBAAgB,CAAhBA,gBAAgB;EAAhBA,gBAAgB,CAAhBA,gBAAgB;EAAhBA,gBAAgB,CAAhBA,gBAAgB;EAAA,OAAhBA,gBAAgB;AAAA;AAAAH,OAAA,CAAAG,gBAAA,GAAAA,gBAAA"}