{"version": 3, "sources": ["../../worker-utils/src/lib/env-utils/version.ts", "../../worker-utils/src/lib/env-utils/assert.ts", "../../worker-utils/src/lib/env-utils/globals.ts", "../../worker-utils/src/lib/worker-utils/get-transfer-list.ts", "../../worker-utils/src/lib/worker-farm/worker-body.ts", "../../worker-utils/src/lib/node/require-utils.browser.ts", "../../worker-utils/src/lib/library-utils/library-utils.ts", "../src/lib/draco-builder.ts", "../src/lib/draco-module-loader.ts", "../src/lib/utils/version.ts", "../src/draco-writer.ts", "../src/workers/draco-writer-worker.ts"], "sourcesContent": ["// Version constant cannot be imported, it needs to correspond to the build version of **this** module.\n// __VERSION__ is injected by babel-plugin-version-inline\n\n// Change to `latest` on production branches\nconst DEFAULT_VERSION = 'latest';\ndeclare let __VERSION__: string;\nexport const VERSION = typeof __VERSION__ !== 'undefined' ? __VERSION__ : DEFAULT_VERSION;\nif (typeof __VERSION__ === 'undefined') {\n  // eslint-disable-next-line\n  console.error(\n    'loaders.gl: The __VERSION__ variable is not injected using babel plugin. Latest unstable workers would be fetched from the CDN.'\n  );\n}\n", "// Replacement for the external assert method to reduce bundle size\n// Note: We don't use the second \"message\" argument in calling code,\n// so no need to support it here\n\n/** Throws an `Error` with the optional `message` if `condition` is falsy */\nexport function assert(condition: any, message?: string): void {\n  if (!condition) {\n    throw new Error(message || 'loaders.gl assertion failed.');\n  }\n}\n", "// Purpose: include this in your module to avoids adding dependencies on\n// micro modules like 'global' and 'is-browser';\n\n/* eslint-disable no-restricted-globals */\nconst globals = {\n  self: typeof self !== 'undefined' && self,\n  window: typeof window !== 'undefined' && window,\n  global: typeof global !== 'undefined' && global,\n  document: typeof document !== 'undefined' && document\n};\n\nconst self_: {[key: string]: any} = globals.self || globals.window || globals.global || {};\nconst window_: {[key: string]: any} = globals.window || globals.self || globals.global || {};\nconst global_: {[key: string]: any} = globals.global || globals.self || globals.window || {};\nconst document_: {[key: string]: any} = globals.document || {};\n\nexport {self_ as self, window_ as window, global_ as global, document_ as document};\n\n/** true if running in the browser, false if running in Node.js */\nexport const isBrowser: boolean =\n  // @ts-ignore process.browser\n  typeof process !== 'object' || String(process) !== '[object process]' || process.browser;\n\n/** true if running on a worker thread */\nexport const isWorker: boolean = typeof importScripts === 'function';\n\n/** true if running on a mobile device */\nexport const isMobile: boolean =\n  typeof window !== 'undefined' && typeof window.orientation !== 'undefined';\n\n// Extract node major version\nconst matches =\n  typeof process !== 'undefined' && process.version && /v([0-9]*)/.exec(process.version);\n\n/** Version of Node.js if running under Node, otherwise 0 */\nexport const nodeVersion: number = (matches && parseFloat(matches[1])) || 0;\n", "// NOTE - there is a copy of this function is both in core and loader-utils\n// core does not need all the utils in loader-utils, just this one.\n\n/**\n * Returns an array of Transferrable objects that can be used with postMessage\n * https://developer.mozilla.org/en-US/docs/Web/API/Worker/postMessage\n * @param object data to be sent via postMessage\n * @param recursive - not for application use\n * @param transfers - not for application use\n * @returns a transfer list that can be passed to postMessage\n */\nexport function getTransferList(\n  object: any,\n  recursive: boolean = true,\n  transfers?: Set<any>\n): Transferable[] {\n  // Make sure that items in the transfer list is unique\n  const transfersSet = transfers || new Set();\n\n  if (!object) {\n    // ignore\n  } else if (isTransferable(object)) {\n    transfersSet.add(object);\n  } else if (isTransferable(object.buffer)) {\n    // Typed array\n    transfersSet.add(object.buffer);\n  } else if (ArrayBuffer.isView(object)) {\n    // object is a TypeArray viewing into a SharedArrayBuffer (not transferable)\n    // Do not iterate through the content in this case\n  } else if (recursive && typeof object === 'object') {\n    for (const key in object) {\n      // Avoid perf hit - only go one level deep\n      getTransferList(object[key], recursive, transfersSet);\n    }\n  }\n\n  // If transfers is defined, is internal recursive call\n  // Otherwise it's called by the user\n  return transfers === undefined ? Array.from(transfersSet) : [];\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/Transferable\nfunction isTransferable(object: unknown) {\n  if (!object) {\n    return false;\n  }\n  if (object instanceof ArrayBuffer) {\n    return true;\n  }\n  if (typeof MessagePort !== 'undefined' && object instanceof MessagePort) {\n    return true;\n  }\n  if (typeof ImageBitmap !== 'undefined' && object instanceof ImageBitmap) {\n    return true;\n  }\n  // @ts-ignore\n  if (typeof OffscreenCanvas !== 'undefined' && object instanceof OffscreenCanvas) {\n    return true;\n  }\n  return false;\n}\n\n/**\n * Recursively drop non serializable values like functions and regexps.\n * @param object\n */\nexport function getTransferListForWriter(object: object | null): object {\n  if (object === null) {\n    return {};\n  }\n  const clone = Object.assign({}, object);\n\n  Object.keys(clone).forEach((key) => {\n    // Typed Arrays and Arrays are passed with no change\n    if (\n      typeof object[key] === 'object' &&\n      !ArrayBuffer.isView(object[key]) &&\n      !(object[key] instanceof Array)\n    ) {\n      clone[key] = getTransferListForWriter(object[key]);\n    } else if (typeof clone[key] === 'function' || clone[key] instanceof RegExp) {\n      clone[key] = {};\n    } else {\n      clone[key] = object[key];\n    }\n  });\n\n  return clone;\n}\n", "import type {WorkerMessageData, WorkerMessageType, WorkerMessagePayload} from '../../types';\nimport {getTransferList} from '../worker-utils/get-transfer-list';\n\n/** Vile hack to defeat over-zealous bundlers from stripping out the require */\nfunction getParentPort() {\n  // const isNode = globalThis.process;\n  let parentPort;\n  try {\n    // prettier-ignore\n    eval('globalThis.parentPort = require(\\'worker_threads\\').parentPort'); // eslint-disable-line no-eval\n    parentPort = globalThis.parentPort;\n    // eslint-disable-next-line no-empty\n  } catch {}\n  return parentPort;\n}\n\nconst onMessageWrapperMap = new Map();\n\n/**\n * Type safe wrapper for worker code\n */\nexport default class WorkerBody {\n  /** Check that we are actually in a worker thread */\n  static inWorkerThread(): boolean {\n    return typeof self !== 'undefined' || Boolean(getParentPort());\n  }\n\n  /*\n   * (type: WorkerMessageType, payload: WorkerMessagePayload) => any\n   */\n  static set onmessage(onMessage: (type: WorkerMessageType, payload: WorkerMessagePayload) => any) {\n    function handleMessage(message) {\n      // Confusingly the message itself also has a 'type' field which is always set to 'message'\n      const parentPort = getParentPort();\n      const {type, payload} = parentPort ? message : message.data;\n      // if (!isKnownMessage(message)) {\n      //   return;\n      // }\n      onMessage(type, payload);\n    }\n\n    const parentPort = getParentPort();\n    if (parentPort) {\n      parentPort.on('message', handleMessage);\n      // if (message == 'exit') { parentPort.unref(); }\n      // eslint-disable-next-line\n      parentPort.on('exit', () => console.debug('Node worker closing'));\n    } else {\n      // eslint-disable-next-line no-restricted-globals\n      globalThis.onmessage = handleMessage;\n    }\n  }\n\n  static addEventListener(\n    onMessage: (type: WorkerMessageType, payload: WorkerMessagePayload) => any\n  ) {\n    let onMessageWrapper = onMessageWrapperMap.get(onMessage);\n\n    if (!onMessageWrapper) {\n      onMessageWrapper = (message: MessageEvent<any>) => {\n        if (!isKnownMessage(message)) {\n          return;\n        }\n\n        // Confusingly in the browser, the message itself also has a 'type' field which is always set to 'message'\n        const parentPort = getParentPort();\n        const {type, payload} = parentPort ? message : message.data;\n        onMessage(type, payload);\n      };\n    }\n\n    const parentPort = getParentPort();\n    if (parentPort) {\n      console.error('not implemented'); // eslint-disable-line\n    } else {\n      globalThis.addEventListener('message', onMessageWrapper);\n    }\n  }\n\n  static removeEventListener(\n    onMessage: (type: WorkerMessageType, payload: WorkerMessagePayload) => any\n  ) {\n    const onMessageWrapper = onMessageWrapperMap.get(onMessage);\n    onMessageWrapperMap.delete(onMessage);\n    const parentPort = getParentPort();\n    if (parentPort) {\n      console.error('not implemented'); // eslint-disable-line\n    } else {\n      globalThis.removeEventListener('message', onMessageWrapper);\n    }\n  }\n\n  /**\n   * Send a message from a worker to creating thread (main thread)\n   * @param type\n   * @param payload\n   */\n  static postMessage(type: WorkerMessageType, payload: WorkerMessagePayload): void {\n    const data: WorkerMessageData = {source: 'loaders.gl', type, payload};\n    // console.log('posting message', data);\n    const transferList = getTransferList(payload);\n\n    const parentPort = getParentPort();\n    if (parentPort) {\n      parentPort.postMessage(data, transferList);\n      // console.log('posted message', data);\n    } else {\n      // @ts-ignore\n      globalThis.postMessage(data, transferList);\n    }\n  }\n}\n\n// Filter out noise messages sent to workers\nfunction isKnownMessage(message: MessageEvent<any>) {\n  const {type, data} = message;\n  return (\n    type === 'message' &&\n    data &&\n    typeof data.source === 'string' &&\n    data.source.startsWith('loaders.gl')\n  );\n}\n", "export const readFileAsArrayBuffer = null;\nexport const readFileAsText = null;\nexport const requireFromFile = null;\nexport const requireFromString = null;\n", "/* global importScripts */\nimport {global, isBrowser, isWorker} from '../env-utils/globals';\nimport * as node from '../node/require-utils.node';\nimport {assert} from '../env-utils/assert';\nimport {VERSION as __VERSION__} from '../env-utils/version';\n\n/**\n * TODO - unpkg.com doesn't seem to have a `latest` specifier for alpha releases...\n * 'beta' on beta branch, 'latest' on prod branch\n */\nconst LATEST = 'latest';\nconst VERSION = typeof __VERSION__ !== 'undefined' ? __VERSION__ : LATEST;\n\nconst loadLibraryPromises: Record<string, Promise<any>> = {}; // promises\n\n/**\n * Dynamically loads a library (\"module\")\n *\n * - wasm library: Array buffer is returned\n * - js library: Parse JS is returned\n *\n * Method depends on environment\n * - browser - script element is created and installed on document\n * - worker - eval is called on global context\n * - node - file is required\n *\n * @param libraryUrl\n * @param moduleName\n * @param options\n */\nexport async function loadLibrary(\n  libraryUrl: string,\n  moduleName: string | null = null,\n  options: object = {}\n): Promise<any> {\n  if (moduleName) {\n    libraryUrl = getLibraryUrl(libraryUrl, moduleName, options);\n  }\n\n  // Ensure libraries are only loaded once\n\n  loadLibraryPromises[libraryUrl] =\n    // eslint-disable-next-line @typescript-eslint/no-misused-promises\n    loadLibraryPromises[libraryUrl] || loadLibraryFromFile(libraryUrl);\n  return await loadLibraryPromises[libraryUrl];\n}\n\n// TODO - sort out how to resolve paths for main/worker and dev/prod\nexport function getLibraryUrl(library: string, moduleName?: string, options?: any): string {\n  // Check if already a URL\n  if (library.startsWith('http')) {\n    return library;\n  }\n\n  // Allow application to import and supply libraries through `options.modules`\n  const modules = options.modules || {};\n  if (modules[library]) {\n    return modules[library];\n  }\n\n  // Load from local files, not from CDN scripts in Node.js\n  // TODO - needs to locate the modules directory when installed!\n  if (!isBrowser) {\n    return `modules/${moduleName}/dist/libs/${library}`;\n  }\n\n  // In browser, load from external scripts\n  if (options.CDN) {\n    assert(options.CDN.startsWith('http'));\n    return `${options.CDN}/${moduleName}@${VERSION}/dist/libs/${library}`;\n  }\n\n  // TODO - loading inside workers requires paths relative to worker script location...\n  if (isWorker) {\n    return `../src/libs/${library}`;\n  }\n\n  return `modules/${moduleName}/src/libs/${library}`;\n}\n\nasync function loadLibraryFromFile(libraryUrl: string): Promise<any> {\n  if (libraryUrl.endsWith('wasm')) {\n    const response = await fetch(libraryUrl);\n    return await response.arrayBuffer();\n  }\n\n  if (!isBrowser) {\n    try {\n      return node && node.requireFromFile && (await node.requireFromFile(libraryUrl));\n    } catch {\n      return null;\n    }\n  }\n  if (isWorker) {\n    return importScripts(libraryUrl);\n  }\n  // TODO - fix - should be more secure than string parsing since observes CORS\n  // if (isBrowser) {\n  //   return await loadScriptFromFile(libraryUrl);\n  // }\n\n  const response = await fetch(libraryUrl);\n  const scriptSource = await response.text();\n  return loadLibraryFromString(scriptSource, libraryUrl);\n}\n\n/*\nasync function loadScriptFromFile(libraryUrl) {\n  const script = document.createElement('script');\n  script.src = libraryUrl;\n  return await new Promise((resolve, reject) => {\n    script.onload = data => {\n      resolve(data);\n    };\n    script.onerror = reject;\n  });\n}\n*/\n\n// TODO - Needs security audit...\n//  - Raw eval call\n//  - Potentially bypasses CORS\n// Upside is that this separates fetching and parsing\n// we could create a`LibraryLoader` or`ModuleLoader`\nfunction loadLibraryFromString(scriptSource: string, id: string): null | any {\n  if (!isBrowser) {\n    return node.requireFromString && node.requireFromString(scriptSource, id);\n  }\n\n  if (isWorker) {\n    // Use lvalue trick to make eval run in global scope\n    eval.call(global, scriptSource); // eslint-disable-line no-eval\n    // https://stackoverflow.com/questions/9107240/1-evalthis-vs-evalthis-in-javascript\n    // http://perfectionkills.com/global-eval-what-are-the-options/\n    return null;\n  }\n\n  const script = document.createElement('script');\n  script.id = id;\n  // most browsers like a separate text node but some throw an error. The second method covers those.\n  try {\n    script.appendChild(document.createTextNode(scriptSource));\n  } catch (e) {\n    script.text = scriptSource;\n  }\n  document.body.appendChild(script);\n  return null;\n}\n\n// TODO - technique for module injection into worker, from THREE.DracoLoader...\n/*\nfunction combineWorkerWithLibrary(worker, jsContent) {\n  var fn = wWorker.toString();\n  var body = [\n    '// injected',\n    jsContent,\n    '',\n    '// worker',\n    fn.substring(fn.indexOf('{') + 1, fn.lastIndexOf('}'))\n  ].join('\\n');\n  this.workerSourceURL = URL.createObjectURL(new Blob([body]));\n}\n*/\n", "/* eslint-disable camelcase */\n// This code is inspired by example code in the DRACO repository\nimport type {\n  Draco3D,\n  DracoInt8Array,\n  Encoder,\n  Mesh,\n  MeshBuilder,\n  PointCloud,\n  Metadata,\n  MetadataBuilder,\n  draco_GeometryAttribute_Type\n} from '../draco3d/draco3d-types';\n\nimport type {TypedArray} from '@loaders.gl/schema';\nimport type {DracoMesh} from './draco-types';\n\nexport type DracoBuildOptions = {\n  pointcloud?: boolean;\n  metadata?: {[key: string]: string};\n  attributesMetadata?: {};\n  log?: any;\n\n  // draco encoding options\n  speed?: [number, number];\n  method?: string;\n  quantization?: {[attributeName: string]: number};\n};\n\n// Native Draco attribute names to GLTF attribute names.\nconst GLTF_TO_DRACO_ATTRIBUTE_NAME_MAP = {\n  POSITION: 'POSITION',\n  NORMAL: 'NORMAL',\n  COLOR_0: 'COLOR',\n  TEXCOORD_0: 'TEX_COORD'\n};\n\nconst noop = () => {};\n\nexport default class DracoBuilder {\n  draco: Draco3D;\n  dracoEncoder: Encoder;\n  dracoMeshBuilder: MeshBuilder;\n  dracoMetadataBuilder: MetadataBuilder;\n  log: any;\n\n  // draco - the draco decoder, either import `draco3d` or load dynamically\n  constructor(draco: Draco3D) {\n    this.draco = draco;\n    this.dracoEncoder = new this.draco.Encoder();\n    this.dracoMeshBuilder = new this.draco.MeshBuilder();\n    this.dracoMetadataBuilder = new this.draco.MetadataBuilder();\n  }\n\n  destroy(): void {\n    this.destroyEncodedObject(this.dracoMeshBuilder);\n    this.destroyEncodedObject(this.dracoEncoder);\n    this.destroyEncodedObject(this.dracoMetadataBuilder);\n    // @ts-ignore\n    this.dracoMeshBuilder = null;\n    // @ts-ignore\n    this.dracoEncoder = null;\n    // @ts-ignore\n    this.draco = null;\n  }\n\n  // TBD - when does this need to be called?\n  destroyEncodedObject(object): void {\n    if (object) {\n      this.draco.destroy(object);\n    }\n  }\n\n  /**\n   * Encode mesh or point cloud\n   * @param mesh =({})\n   * @param options\n   */\n  encodeSync(mesh: DracoMesh, options: DracoBuildOptions = {}): ArrayBuffer {\n    this.log = noop; // TODO\n    this._setOptions(options);\n\n    return options.pointcloud\n      ? this._encodePointCloud(mesh, options)\n      : this._encodeMesh(mesh, options);\n  }\n\n  // PRIVATE\n\n  _getAttributesFromMesh(mesh: DracoMesh) {\n    // TODO - Change the encodePointCloud interface instead?\n    const attributes = {...mesh, ...mesh.attributes};\n    // Fold indices into the attributes\n    if (mesh.indices) {\n      attributes.indices = mesh.indices;\n    }\n    return attributes;\n  }\n\n  _encodePointCloud(pointcloud: DracoMesh, options: DracoBuildOptions): ArrayBuffer {\n    const dracoPointCloud = new this.draco.PointCloud();\n\n    if (options.metadata) {\n      this._addGeometryMetadata(dracoPointCloud, options.metadata);\n    }\n\n    const attributes = this._getAttributesFromMesh(pointcloud);\n\n    // Build a `DracoPointCloud` from the input data\n    this._createDracoPointCloud(dracoPointCloud, attributes, options);\n\n    const dracoData = new this.draco.DracoInt8Array();\n\n    try {\n      const encodedLen = this.dracoEncoder.EncodePointCloudToDracoBuffer(\n        dracoPointCloud,\n        false,\n        dracoData\n      );\n\n      if (!(encodedLen > 0)) {\n        throw new Error('Draco encoding failed.');\n      }\n\n      this.log(`DRACO encoded ${dracoPointCloud.num_points()} points\n        with ${dracoPointCloud.num_attributes()} attributes into ${encodedLen} bytes`);\n\n      return dracoInt8ArrayToArrayBuffer(dracoData);\n    } finally {\n      this.destroyEncodedObject(dracoData);\n      this.destroyEncodedObject(dracoPointCloud);\n    }\n  }\n\n  _encodeMesh(mesh: DracoMesh, options: DracoBuildOptions): ArrayBuffer {\n    const dracoMesh = new this.draco.Mesh();\n\n    if (options.metadata) {\n      this._addGeometryMetadata(dracoMesh, options.metadata);\n    }\n\n    const attributes = this._getAttributesFromMesh(mesh);\n\n    // Build a `DracoMesh` from the input data\n    this._createDracoMesh(dracoMesh, attributes, options);\n\n    const dracoData = new this.draco.DracoInt8Array();\n\n    try {\n      const encodedLen = this.dracoEncoder.EncodeMeshToDracoBuffer(dracoMesh, dracoData);\n      if (encodedLen <= 0) {\n        throw new Error('Draco encoding failed.');\n      }\n\n      this.log(`DRACO encoded ${dracoMesh.num_points()} points\n        with ${dracoMesh.num_attributes()} attributes into ${encodedLen} bytes`);\n\n      return dracoInt8ArrayToArrayBuffer(dracoData);\n    } finally {\n      this.destroyEncodedObject(dracoData);\n      this.destroyEncodedObject(dracoMesh);\n    }\n  }\n\n  /**\n   * Set encoding options.\n   * @param {{speed?: any; method?: any; quantization?: any;}} options\n   */\n  _setOptions(options: DracoBuildOptions): void {\n    if ('speed' in options) {\n      // @ts-ignore\n      this.dracoEncoder.SetSpeedOptions(...options.speed);\n    }\n    if ('method' in options) {\n      const dracoMethod = this.draco[options.method || 'MESH_SEQUENTIAL_ENCODING'];\n      // assert(dracoMethod)\n      this.dracoEncoder.SetEncodingMethod(dracoMethod);\n    }\n    if ('quantization' in options) {\n      for (const attribute in options.quantization) {\n        const bits = options.quantization[attribute];\n        const dracoPosition = this.draco[attribute];\n        this.dracoEncoder.SetAttributeQuantization(dracoPosition, bits);\n      }\n    }\n  }\n\n  /**\n   * @param {Mesh} dracoMesh\n   * @param {object} attributes\n   * @returns {Mesh}\n   */\n  _createDracoMesh(dracoMesh: Mesh, attributes, options: DracoBuildOptions): Mesh {\n    const optionalMetadata = options.attributesMetadata || {};\n\n    try {\n      const positions = this._getPositionAttribute(attributes);\n      if (!positions) {\n        throw new Error('positions');\n      }\n      const vertexCount = positions.length / 3;\n\n      for (let attributeName in attributes) {\n        const attribute = attributes[attributeName];\n        attributeName = GLTF_TO_DRACO_ATTRIBUTE_NAME_MAP[attributeName] || attributeName;\n        const uniqueId = this._addAttributeToMesh(dracoMesh, attributeName, attribute, vertexCount);\n\n        if (uniqueId !== -1) {\n          this._addAttributeMetadata(dracoMesh, uniqueId, {\n            name: attributeName,\n            ...(optionalMetadata[attributeName] || {})\n          });\n        }\n      }\n    } catch (error) {\n      this.destroyEncodedObject(dracoMesh);\n      throw error;\n    }\n\n    return dracoMesh;\n  }\n\n  /**\n   * @param {} dracoPointCloud\n   * @param {object} attributes\n   */\n  _createDracoPointCloud(\n    dracoPointCloud: PointCloud,\n    attributes: object,\n    options: DracoBuildOptions\n  ): PointCloud {\n    const optionalMetadata = options.attributesMetadata || {};\n\n    try {\n      const positions = this._getPositionAttribute(attributes);\n      if (!positions) {\n        throw new Error('positions');\n      }\n      const vertexCount = positions.length / 3;\n\n      for (let attributeName in attributes) {\n        const attribute = attributes[attributeName];\n        attributeName = GLTF_TO_DRACO_ATTRIBUTE_NAME_MAP[attributeName] || attributeName;\n        const uniqueId = this._addAttributeToMesh(\n          dracoPointCloud,\n          attributeName,\n          attribute,\n          vertexCount\n        );\n        if (uniqueId !== -1) {\n          this._addAttributeMetadata(dracoPointCloud, uniqueId, {\n            name: attributeName,\n            ...(optionalMetadata[attributeName] || {})\n          });\n        }\n      }\n    } catch (error) {\n      this.destroyEncodedObject(dracoPointCloud);\n      throw error;\n    }\n\n    return dracoPointCloud;\n  }\n\n  /**\n   * @param mesh\n   * @param attributeName\n   * @param attribute\n   * @param vertexCount\n   */\n  _addAttributeToMesh(\n    mesh: PointCloud,\n    attributeName: string,\n    attribute: TypedArray,\n    vertexCount: number\n  ) {\n    if (!ArrayBuffer.isView(attribute)) {\n      return -1;\n    }\n\n    const type = this._getDracoAttributeType(attributeName);\n    // @ts-ignore TODO/fix types\n    const size = attribute.length / vertexCount;\n\n    if (type === 'indices') {\n      // @ts-ignore TODO/fix types\n      const numFaces = attribute.length / 3;\n      this.log(`Adding attribute ${attributeName}, size ${numFaces}`);\n\n      // @ts-ignore assumes mesh is a Mesh, not a point cloud\n      this.dracoMeshBuilder.AddFacesToMesh(mesh, numFaces, attribute);\n      return -1;\n    }\n\n    this.log(`Adding attribute ${attributeName}, size ${size}`);\n\n    const builder = this.dracoMeshBuilder;\n    const {buffer} = attribute;\n\n    switch (attribute.constructor) {\n      case Int8Array:\n        return builder.AddInt8Attribute(mesh, type, vertexCount, size, new Int8Array(buffer));\n\n      case Int16Array:\n        return builder.AddInt16Attribute(mesh, type, vertexCount, size, new Int16Array(buffer));\n\n      case Int32Array:\n        return builder.AddInt32Attribute(mesh, type, vertexCount, size, new Int32Array(buffer));\n      case Uint8Array:\n      case Uint8ClampedArray:\n        return builder.AddUInt8Attribute(mesh, type, vertexCount, size, new Uint8Array(buffer));\n\n      case Uint16Array:\n        return builder.AddUInt16Attribute(mesh, type, vertexCount, size, new Uint16Array(buffer));\n\n      case Uint32Array:\n        return builder.AddUInt32Attribute(mesh, type, vertexCount, size, new Uint32Array(buffer));\n\n      case Float32Array:\n      default:\n        return builder.AddFloatAttribute(mesh, type, vertexCount, size, new Float32Array(buffer));\n    }\n  }\n\n  /**\n   * DRACO can compress attributes of know type better\n   * TODO - expose an attribute type map?\n   * @param attributeName\n   */\n  _getDracoAttributeType(attributeName: string): draco_GeometryAttribute_Type | 'indices' {\n    switch (attributeName.toLowerCase()) {\n      case 'indices':\n        return 'indices';\n      case 'position':\n      case 'positions':\n      case 'vertices':\n        return this.draco.POSITION;\n      case 'normal':\n      case 'normals':\n        return this.draco.NORMAL;\n      case 'color':\n      case 'colors':\n        return this.draco.COLOR;\n      case 'texcoord':\n      case 'texcoords':\n        return this.draco.TEX_COORD;\n      default:\n        return this.draco.GENERIC;\n    }\n  }\n\n  _getPositionAttribute(attributes) {\n    for (const attributeName in attributes) {\n      const attribute = attributes[attributeName];\n      const dracoType = this._getDracoAttributeType(attributeName);\n      if (dracoType === this.draco.POSITION) {\n        return attribute;\n      }\n    }\n    return null;\n  }\n\n  /**\n   * Add metadata for the geometry.\n   * @param dracoGeometry - WASM Draco Object\n   * @param metadata\n   */\n  _addGeometryMetadata(dracoGeometry: PointCloud, metadata: {[key: string]: string}) {\n    const dracoMetadata = new this.draco.Metadata();\n    this._populateDracoMetadata(dracoMetadata, metadata);\n    this.dracoMeshBuilder.AddMetadata(dracoGeometry, dracoMetadata);\n  }\n\n  /**\n   * Add metadata for an attribute to geometry.\n   * @param dracoGeometry - WASM Draco Object\n   * @param uniqueAttributeId\n   * @param metadata\n   */\n  _addAttributeMetadata(\n    dracoGeometry: PointCloud,\n    uniqueAttributeId: number,\n    metadata: Map<string, string> | {[key: string]: string}\n  ) {\n    // Note: Draco JS IDL doesn't seem to expose draco.AttributeMetadata, however it seems to\n    // create such objects automatically from draco.Metadata object.\n    const dracoAttributeMetadata = new this.draco.Metadata();\n    this._populateDracoMetadata(dracoAttributeMetadata, metadata);\n    // Draco3d doc note: Directly add attribute metadata to geometry.\n    // You can do this without explicitly adding |GeometryMetadata| to mesh.\n    this.dracoMeshBuilder.SetMetadataForAttribute(\n      dracoGeometry,\n      uniqueAttributeId,\n      dracoAttributeMetadata\n    );\n  }\n\n  /**\n   * Add contents of object or map to a WASM Draco Metadata Object\n   * @param dracoMetadata - WASM Draco Object\n   * @param metadata\n   */\n  _populateDracoMetadata(\n    dracoMetadata: Metadata,\n    metadata: Map<string, string> | {[key: string]: string}\n  ) {\n    for (const [key, value] of getEntries(metadata)) {\n      switch (typeof value) {\n        case 'number':\n          if (Math.trunc(value) === value) {\n            this.dracoMetadataBuilder.AddIntEntry(dracoMetadata, key, value);\n          } else {\n            this.dracoMetadataBuilder.AddDoubleEntry(dracoMetadata, key, value);\n          }\n          break;\n        case 'object':\n          if (value instanceof Int32Array) {\n            this.dracoMetadataBuilder.AddIntEntryArray(dracoMetadata, key, value, value.length);\n          }\n          break;\n        case 'string':\n        default:\n          this.dracoMetadataBuilder.AddStringEntry(dracoMetadata, key, value);\n      }\n    }\n  }\n}\n\n// HELPER FUNCTIONS\n\n/**\n * Copy encoded data to buffer\n * @param dracoData\n */\nfunction dracoInt8ArrayToArrayBuffer(dracoData: DracoInt8Array) {\n  const byteLength = dracoData.size();\n  const outputBuffer = new ArrayBuffer(byteLength);\n  const outputData = new Int8Array(outputBuffer);\n  for (let i = 0; i < byteLength; ++i) {\n    outputData[i] = dracoData.GetValue(i);\n  }\n  return outputBuffer;\n}\n\n/** Enable iteration over either an object or a map */\nfunction getEntries(container) {\n  const hasEntriesFunc = container.entries && !container.hasOwnProperty('entries');\n  return hasEntriesFunc ? container.entries() : Object.entries(container);\n}\n", "// Dynamic DRACO module loading inspired by THREE.DRACOLoader\n// https://github.com/mrdoob/three.js/blob/398c4f39ebdb8b23eefd4a7a5ec49ec0c96c7462/examples/jsm/loaders/DRACOLoader.js\n// by <PERSON> / https://www.donmccurdy.com / MIT license\n\nimport {loadLibrary} from '@loaders.gl/worker-utils';\n\nconst DRACO_DECODER_VERSION = '1.5.5';\nconst DRACO_ENCODER_VERSION = '1.4.1';\n\nconst STATIC_DECODER_URL = `https://www.gstatic.com/draco/versioned/decoders/${DRACO_DECODER_VERSION}`;\n\nconst DRACO_JS_DECODER_URL = `${STATIC_DECODER_URL}/draco_decoder.js`;\nconst DRACO_WASM_WRAPPER_URL = `${STATIC_DECODER_URL}/draco_wasm_wrapper.js`;\nconst DRACO_WASM_DECODER_URL = `${STATIC_DECODER_URL}/draco_decoder.wasm`;\n\nconst DRACO_ENCODER_URL = `https://raw.githubusercontent.com/google/draco/${DRACO_ENCODER_VERSION}/javascript/draco_encoder.js`;\n\nlet loadDecoderPromise;\nlet loadEncoderPromise;\n\nexport async function loadDracoDecoderModule(options) {\n  const modules = options.modules || {};\n\n  // Check if a bundled draco3d library has been supplied by application\n  if (modules.draco3d) {\n    loadDecoderPromise =\n      loadDecoderPromise ||\n      modules.draco3d.createDecoderModule({}).then((draco) => {\n        return {draco};\n      });\n  } else {\n    // If not, dynamically load the WASM script from our CDN\n    loadDecoderPromise = loadDecoderPromise || loadDracoDecoder(options);\n  }\n  return await loadDecoderPromise;\n}\n\nexport async function loadDracoEncoderModule(options) {\n  const modules = options.modules || {};\n\n  // Check if a bundled draco3d library has been supplied by application\n  if (modules.draco3d) {\n    loadEncoderPromise =\n      loadEncoderPromise ||\n      modules.draco3d.createEncoderModule({}).then((draco) => {\n        return {draco};\n      });\n  } else {\n    // If not, dynamically load the WASM script from our CDN\n    loadEncoderPromise = loadEncoderPromise || loadDracoEncoder(options);\n  }\n  return await loadEncoderPromise;\n}\n\n// DRACO DECODER LOADING\n\nasync function loadDracoDecoder(options) {\n  let DracoDecoderModule;\n  let wasmBinary;\n  switch (options.draco && options.draco.decoderType) {\n    case 'js':\n      DracoDecoderModule = await loadLibrary(DRACO_JS_DECODER_URL, 'draco', options);\n      break;\n\n    case 'wasm':\n    default:\n      [DracoDecoderModule, wasmBinary] = await Promise.all([\n        await loadLibrary(DRACO_WASM_WRAPPER_URL, 'draco', options),\n        await loadLibrary(DRACO_WASM_DECODER_URL, 'draco', options)\n      ]);\n  }\n  // Depends on how import happened...\n  // @ts-ignore\n  DracoDecoderModule = DracoDecoderModule || globalThis.DracoDecoderModule;\n  return await initializeDracoDecoder(DracoDecoderModule, wasmBinary);\n}\n\nfunction initializeDracoDecoder(DracoDecoderModule, wasmBinary) {\n  const options: {wasmBinary?: any} = {};\n  if (wasmBinary) {\n    options.wasmBinary = wasmBinary;\n  }\n\n  return new Promise((resolve) => {\n    DracoDecoderModule({\n      ...options,\n      onModuleLoaded: (draco) => resolve({draco}) // Module is Promise-like. Wrap in object to avoid loop.\n    });\n  });\n}\n\n// ENCODER\n\nasync function loadDracoEncoder(options) {\n  let DracoEncoderModule = await loadLibrary(DRACO_ENCODER_URL, 'draco', options);\n  // @ts-ignore\n  DracoEncoderModule = DracoEncoderModule || globalThis.DracoEncoderModule;\n\n  return new Promise((resolve) => {\n    DracoEncoderModule({\n      onModuleLoaded: (draco) => resolve({draco}) // Module is Promise-like. Wrap in object to avoid loop.\n    });\n  });\n}\n", "// Version constant cannot be imported, it needs to correspond to the build version of **this** module.\n// __VERSION__ is injected by babel-plugin-version-inline\n// @ts-ignore TS2304: Cannot find name '__VERSION__'.\nexport const VERSION = typeof __VERSION__ !== 'undefined' ? __VERSION__ : 'latest';\n", "import type {Writer, WriterOptions} from '@loaders.gl/loader-utils';\nimport type {DracoMesh} from './lib/draco-types';\nimport type {DracoBuildOptions} from './lib/draco-builder';\nimport DRACOBuilder from './lib/draco-builder';\nimport {loadDracoEncoderModule} from './lib/draco-module-loader';\nimport {VERSION} from './lib/utils/version';\n\nexport type DracoWriterOptions = WriterOptions & {\n  draco?: DracoBuildOptions & {\n    attributeNameEntry: string;\n  };\n};\n\nconst DEFAULT_DRACO_OPTIONS = {\n  pointcloud: false, // Set to true if pointcloud (mode: 0, no indices)\n  attributeNameEntry: 'name'\n  // Draco Compression Parameters\n  // method: 'MESH_EDGEBREAKER_ENCODING',\n  // speed: [5, 5],\n  // quantization: {\n  //   POSITION: 10\n  // }\n};\n\n/**\n * Exporter for Draco3D compressed geometries\n */\nexport const DracoWriter: Writer = {\n  name: 'DR<PERSON><PERSON>',\n  id: 'draco',\n  module: 'draco',\n  version: VERSION,\n  extensions: ['drc'],\n  encode,\n  options: {\n    draco: DEFAULT_DRACO_OPTIONS\n  }\n};\n\nasync function encode(data: DracoMesh, options: DracoWriterOptions = {}): Promise<ArrayBuffer> {\n  // Dynamically load draco\n  const {draco} = await loadDracoEncoderModule(options);\n  const dracoBuilder = new DRACOBuilder(draco);\n\n  try {\n    return dracoBuilder.encodeSync(data, options.draco);\n  } finally {\n    dracoBuilder.destroy();\n  }\n}\n", "import {WorkerBody, WorkerMessagePayload} from '@loaders.gl/worker-utils';\nimport {DracoWriter} from '../draco-writer';\n\n(() => {\n  // Check that we are actually in a worker thread\n  if (!WorkerBody.inWorkerThread()) {\n    return;\n  }\n\n  WorkerBody.onmessage = async (type, payload: WorkerMessagePayload) => {\n    switch (type) {\n      case 'process':\n        try {\n          const {input, options} = payload;\n          const result = await DracoWriter.encode!(input, options);\n          WorkerBody.postMessage('done', {result});\n        } catch (error) {\n          const message = error instanceof Error ? error.message : '';\n          WorkerBody.postMessage('error', {error: message});\n        }\n        break;\n      default:\n    }\n  };\n})();\n"], "mappings": "2IAMO,GAAM,GAA+C,SCDrD,WAAgB,EAAgB,EAAwB,CAC7D,GAAI,CAAC,EACH,KAAM,IAAI,OAAM,GAAW,gCCH/B,GAAM,GAAU,CACd,KAAM,MAAO,OAAS,aAAe,KACrC,OAAQ,MAAO,SAAW,aAAe,OACzC,OAAQ,MAAO,SAAW,aAAe,OACzC,SAAU,MAAO,WAAa,aAAe,UAGzC,GAA8B,EAAQ,MAAQ,EAAQ,QAAU,EAAQ,QAAU,GAClF,GAAgC,EAAQ,QAAU,EAAQ,MAAQ,EAAQ,QAAU,GACpF,EAAgC,EAAQ,QAAU,EAAQ,MAAQ,EAAQ,QAAU,GACpF,GAAkC,EAAQ,UAAY,GAKrD,GAAM,GAEX,MAAO,UAAY,UAAY,OAAO,WAAa,oBAAsB,QAAQ,QAGtE,EAAoB,MAAO,gBAAkB,WAG7C,GACX,MAAO,SAAW,aAAe,MAAO,QAAO,aAAgB,YAG3D,EACJ,MAAO,UAAY,aAAe,QAAQ,SAAW,YAAY,KAAK,QAAQ,SAGnE,GAAuB,GAAW,WAAW,EAAQ,KAAQ,ECxBnE,WACL,EACA,EAAqB,GACrB,EACgB,CAEhB,GAAM,GAAe,GAAa,GAAI,KAEtC,GAAK,GAEE,GAAI,EAAe,GACxB,EAAa,IAAI,WACR,EAAe,EAAO,QAE/B,EAAa,IAAI,EAAO,gBACf,aAAY,OAAO,IAGvB,GAAI,GAAa,MAAO,IAAW,SACxC,OAAW,KAAO,GAEhB,EAAgB,EAAO,GAAM,EAAW,IAM5C,MAAO,KAAc,OAAY,MAAM,KAAK,GAAgB,GAI9D,WAAwB,EAAiB,CACvC,MAAK,GAGD,YAAkB,cAGlB,MAAO,cAAgB,aAAe,YAAkB,cAGxD,MAAO,cAAgB,aAAe,YAAkB,cAIxD,MAAO,kBAAoB,aAAe,YAAkB,iBAZvD,GCxCX,YAAyB,CAEvB,GAAI,YACJ,GAAI,CAEF,KAAK,gEACL,WAAa,WAAW,gBAExB,EACF,MAAO,YAGT,GAAM,GAAsB,GAAI,KAKhC,OAAgC,OAEvB,iBAA0B,CAC/B,MAAO,OAAO,OAAS,aAAe,QAAQ,eAMrC,WAAU,EAA4E,CAC/F,WAAuB,EAAS,CAE9B,GAAM,GAAa,IACb,CAAC,OAAM,WAAW,EAAa,EAAU,EAAQ,KAIvD,EAAU,EAAM,GAGlB,GAAM,GAAa,IACnB,AAAI,EACF,GAAW,GAAG,UAAW,GAGzB,EAAW,GAAG,OAAQ,IAAM,QAAQ,MAAM,yBAG1C,WAAW,UAAY,QAIpB,kBACL,EACA,CACA,GAAI,GAAmB,EAAoB,IAAI,GAE/C,AAAK,GACH,GAAmB,AAAC,GAA+B,CACjD,GAAI,CAAC,EAAe,GAClB,OAIF,GAAM,GAAa,IACb,CAAC,OAAM,WAAW,EAAa,EAAU,EAAQ,KACvD,EAAU,EAAM,KAKpB,AADmB,IAEjB,QAAQ,MAAM,mBAEd,WAAW,iBAAiB,UAAW,SAIpC,qBACL,EACA,CACA,GAAM,GAAmB,EAAoB,IAAI,GACjD,EAAoB,OAAO,GAE3B,AADmB,IAEjB,QAAQ,MAAM,mBAEd,WAAW,oBAAoB,UAAW,SASvC,aAAY,EAAyB,EAAqC,CAC/E,GAAM,GAA0B,CAAC,OAAQ,aAAc,OAAM,WAEvD,EAAe,EAAgB,GAE/B,EAAa,IACnB,AAAI,EACF,EAAW,YAAY,EAAM,GAI7B,WAAW,YAAY,EAAM,KAMnC,WAAwB,EAA4B,CAClD,GAAM,CAAC,OAAM,QAAQ,EACrB,MACE,KAAS,WACT,GACA,MAAO,GAAK,QAAW,UACvB,EAAK,OAAO,WAAW,cCxH3B,+GAAO,GAAM,GAAwB,KACxB,EAAiB,KACjB,EAAkB,KAClB,EAAoB,KCOjC,GAAM,GAAS,SACT,EAAU,MAAO,IAAgB,YAAc,EAAc,EAE7D,EAAoD,GAiB1D,iBACE,EACA,EAA4B,KAC5B,EAAkB,GACJ,CACd,MAAI,IACF,GAAa,EAAc,EAAY,EAAY,IAKrD,EAAoB,GAElB,EAAoB,IAAe,EAAoB,GAClD,KAAM,GAAoB,GAI5B,WAAuB,EAAiB,EAAqB,EAAuB,CAEzF,GAAI,EAAQ,WAAW,QACrB,MAAO,GAIT,GAAM,GAAU,EAAQ,SAAW,GACnC,MAAI,GAAQ,GACH,EAAQ,GAKZ,EAKD,EAAQ,IACV,GAAO,EAAQ,IAAI,WAAW,SACvB,GAAG,EAAQ,OAAO,KAAc,eAAqB,KAI1D,EACK,eAAe,IAGjB,WAAW,cAAuB,IAdhC,WAAW,eAAwB,IAiB9C,iBAAmC,EAAkC,CACnE,GAAI,EAAW,SAAS,QAEtB,MAAO,MAAM,AADI,MAAM,OAAM,IACP,cAGxB,GAAI,CAAC,EACH,GAAI,CACF,MAAO,IAAa,GAAoB,KAAM,AAAK,GAAgB,QACnE,CACA,MAAO,MAGX,GAAI,EACF,MAAO,eAAc,GAQvB,GAAM,GAAe,KAAM,AADV,MAAM,OAAM,IACO,OACpC,MAAO,GAAsB,EAAc,GAqB7C,WAA+B,EAAsB,EAAwB,CAC3E,GAAI,CAAC,EACH,MAAO,AAAK,IAAqB,AAAK,EAAkB,EAAc,GAGxE,GAAI,EAEF,YAAK,KAAK,EAAQ,GAGX,KAGT,GAAM,GAAS,SAAS,cAAc,UACtC,EAAO,GAAK,EAEZ,GAAI,CACF,EAAO,YAAY,SAAS,eAAe,SAC3C,CACA,EAAO,KAAO,EAEhB,gBAAS,KAAK,YAAY,GACnB,KCpHT,GAAM,GAAmC,CACvC,SAAU,WACV,OAAQ,SACR,QAAS,QACT,WAAY,aAGR,EAAO,IAAM,GAEnB,OAAkC,CAQhC,YAAY,EAAgB,CAC1B,KAAK,MAAQ,EACb,KAAK,aAAe,GAAI,MAAK,MAAM,QACnC,KAAK,iBAAmB,GAAI,MAAK,MAAM,YACvC,KAAK,qBAAuB,GAAI,MAAK,MAAM,gBAG7C,SAAgB,CACd,KAAK,qBAAqB,KAAK,kBAC/B,KAAK,qBAAqB,KAAK,cAC/B,KAAK,qBAAqB,KAAK,sBAE/B,KAAK,iBAAmB,KAExB,KAAK,aAAe,KAEpB,KAAK,MAAQ,KAIf,qBAAqB,EAAc,CACjC,AAAI,GACF,KAAK,MAAM,QAAQ,GASvB,WAAW,EAAiB,EAA6B,GAAiB,CACxE,YAAK,IAAM,EACX,KAAK,YAAY,GAEV,EAAQ,WACX,KAAK,kBAAkB,EAAM,GAC7B,KAAK,YAAY,EAAM,GAK7B,uBAAuB,EAAiB,CAEtC,GAAM,GAAa,IAAI,KAAS,EAAK,YAErC,MAAI,GAAK,SACP,GAAW,QAAU,EAAK,SAErB,EAGT,kBAAkB,EAAuB,EAAyC,CAChF,GAAM,GAAkB,GAAI,MAAK,MAAM,WAEvC,AAAI,EAAQ,UACV,KAAK,qBAAqB,EAAiB,EAAQ,UAGrD,GAAM,GAAa,KAAK,uBAAuB,GAG/C,KAAK,uBAAuB,EAAiB,EAAY,GAEzD,GAAM,GAAY,GAAI,MAAK,MAAM,eAEjC,GAAI,CACF,GAAM,GAAa,KAAK,aAAa,8BACnC,EACA,GACA,GAGF,GAAI,CAAE,GAAa,GACjB,KAAM,IAAI,OAAM,0BAGlB,YAAK,IAAI,iBAAiB,EAAgB;AAAA,eACjC,EAAgB,oCAAoC,WAEtD,EAA4B,UACnC,CACA,KAAK,qBAAqB,GAC1B,KAAK,qBAAqB,IAI9B,YAAY,EAAiB,EAAyC,CACpE,GAAM,GAAY,GAAI,MAAK,MAAM,KAEjC,AAAI,EAAQ,UACV,KAAK,qBAAqB,EAAW,EAAQ,UAG/C,GAAM,GAAa,KAAK,uBAAuB,GAG/C,KAAK,iBAAiB,EAAW,EAAY,GAE7C,GAAM,GAAY,GAAI,MAAK,MAAM,eAEjC,GAAI,CACF,GAAM,GAAa,KAAK,aAAa,wBAAwB,EAAW,GACxE,GAAI,GAAc,EAChB,KAAM,IAAI,OAAM,0BAGlB,YAAK,IAAI,iBAAiB,EAAU;AAAA,eAC3B,EAAU,oCAAoC,WAEhD,EAA4B,UACnC,CACA,KAAK,qBAAqB,GAC1B,KAAK,qBAAqB,IAQ9B,YAAY,EAAkC,CAK5C,GAJI,SAAW,IAEb,KAAK,aAAa,gBAAgB,GAAG,EAAQ,OAE3C,UAAY,GAAS,CACvB,GAAM,GAAc,KAAK,MAAM,EAAQ,QAAU,4BAEjD,KAAK,aAAa,kBAAkB,GAEtC,GAAI,gBAAkB,GACpB,OAAW,KAAa,GAAQ,aAAc,CAC5C,GAAM,GAAO,EAAQ,aAAa,GAC5B,EAAgB,KAAK,MAAM,GACjC,KAAK,aAAa,yBAAyB,EAAe,IAUhE,iBAAiB,EAAiB,EAAY,EAAkC,CAC9E,GAAM,GAAmB,EAAQ,oBAAsB,GAEvD,GAAI,CACF,GAAM,GAAY,KAAK,sBAAsB,GAC7C,GAAI,CAAC,EACH,KAAM,IAAI,OAAM,aAElB,GAAM,GAAc,EAAU,OAAS,EAEvC,OAAS,KAAiB,GAAY,CACpC,GAAM,GAAY,EAAW,GAC7B,EAAgB,EAAiC,IAAkB,EACnE,GAAM,GAAW,KAAK,oBAAoB,EAAW,EAAe,EAAW,GAE/E,AAAI,IAAa,IACf,KAAK,sBAAsB,EAAW,EAAU,CAC9C,KAAM,KACF,EAAiB,IAAkB,YAItC,EAAP,CACA,WAAK,qBAAqB,GACpB,EAGR,MAAO,GAOT,uBACE,EACA,EACA,EACY,CACZ,GAAM,GAAmB,EAAQ,oBAAsB,GAEvD,GAAI,CACF,GAAM,GAAY,KAAK,sBAAsB,GAC7C,GAAI,CAAC,EACH,KAAM,IAAI,OAAM,aAElB,GAAM,GAAc,EAAU,OAAS,EAEvC,OAAS,KAAiB,GAAY,CACpC,GAAM,GAAY,EAAW,GAC7B,EAAgB,EAAiC,IAAkB,EACnE,GAAM,GAAW,KAAK,oBACpB,EACA,EACA,EACA,GAEF,AAAI,IAAa,IACf,KAAK,sBAAsB,EAAiB,EAAU,CACpD,KAAM,KACF,EAAiB,IAAkB,YAItC,EAAP,CACA,WAAK,qBAAqB,GACpB,EAGR,MAAO,GAST,oBACE,EACA,EACA,EACA,EACA,CACA,GAAI,CAAC,YAAY,OAAO,GACtB,MAAO,GAGT,GAAM,GAAO,KAAK,uBAAuB,GAEnC,EAAO,EAAU,OAAS,EAEhC,GAAI,IAAS,UAAW,CAEtB,GAAM,GAAW,EAAU,OAAS,EACpC,YAAK,IAAI,oBAAoB,WAAuB,KAGpD,KAAK,iBAAiB,eAAe,EAAM,EAAU,GAC9C,GAGT,KAAK,IAAI,oBAAoB,WAAuB,KAEpD,GAAM,GAAU,KAAK,iBACf,CAAC,UAAU,EAEjB,OAAQ,EAAU,iBACX,WACH,MAAO,GAAQ,iBAAiB,EAAM,EAAM,EAAa,EAAM,GAAI,WAAU,QAE1E,YACH,MAAO,GAAQ,kBAAkB,EAAM,EAAM,EAAa,EAAM,GAAI,YAAW,QAE5E,YACH,MAAO,GAAQ,kBAAkB,EAAM,EAAM,EAAa,EAAM,GAAI,YAAW,QAC5E,gBACA,mBACH,MAAO,GAAQ,kBAAkB,EAAM,EAAM,EAAa,EAAM,GAAI,YAAW,QAE5E,aACH,MAAO,GAAQ,mBAAmB,EAAM,EAAM,EAAa,EAAM,GAAI,aAAY,QAE9E,aACH,MAAO,GAAQ,mBAAmB,EAAM,EAAM,EAAa,EAAM,GAAI,aAAY,QAE9E,sBAEH,MAAO,GAAQ,kBAAkB,EAAM,EAAM,EAAa,EAAM,GAAI,cAAa,KASvF,uBAAuB,EAAiE,CACtF,OAAQ,EAAc,mBACf,UACH,MAAO,cACJ,eACA,gBACA,WACH,MAAO,MAAK,MAAM,aACf,aACA,UACH,MAAO,MAAK,MAAM,WACf,YACA,SACH,MAAO,MAAK,MAAM,UACf,eACA,YACH,MAAO,MAAK,MAAM,kBAElB,MAAO,MAAK,MAAM,SAIxB,sBAAsB,EAAY,CAChC,OAAW,KAAiB,GAAY,CACtC,GAAM,GAAY,EAAW,GAE7B,GAAI,AADc,KAAK,uBAAuB,KAC5B,KAAK,MAAM,SAC3B,MAAO,GAGX,MAAO,MAQT,qBAAqB,EAA2B,EAAmC,CACjF,GAAM,GAAgB,GAAI,MAAK,MAAM,SACrC,KAAK,uBAAuB,EAAe,GAC3C,KAAK,iBAAiB,YAAY,EAAe,GASnD,sBACE,EACA,EACA,EACA,CAGA,GAAM,GAAyB,GAAI,MAAK,MAAM,SAC9C,KAAK,uBAAuB,EAAwB,GAGpD,KAAK,iBAAiB,wBACpB,EACA,EACA,GASJ,uBACE,EACA,EACA,CACA,OAAW,CAAC,EAAK,IAAU,GAAW,GACpC,OAAQ,MAAO,QACR,SACH,AAAI,KAAK,MAAM,KAAW,EACxB,KAAK,qBAAqB,YAAY,EAAe,EAAK,GAE1D,KAAK,qBAAqB,eAAe,EAAe,EAAK,GAE/D,UACG,SACH,AAAI,YAAiB,aACnB,KAAK,qBAAqB,iBAAiB,EAAe,EAAK,EAAO,EAAM,QAE9E,UACG,iBAEH,KAAK,qBAAqB,eAAe,EAAe,EAAK,MAYvE,WAAqC,EAA2B,CAC9D,GAAM,GAAa,EAAU,OACvB,EAAe,GAAI,aAAY,GAC/B,EAAa,GAAI,WAAU,GACjC,OAAS,GAAI,EAAG,EAAI,EAAY,EAAE,EAChC,EAAW,GAAK,EAAU,SAAS,GAErC,MAAO,GAIT,WAAoB,EAAW,CAE7B,MAAO,AADgB,GAAU,SAAW,CAAC,EAAU,eAAe,WAC9C,EAAU,UAAY,OAAO,QAAQ,GCzb/D,GAAM,GAAwB,QACxB,EAAwB,QAExB,EAAqB,oDAAoD,IAEzE,GAAuB,GAAG,qBAC1B,GAAyB,GAAG,0BAC5B,GAAyB,GAAG,uBAE5B,EAAoB,kDAAkD,gCAG5E,GAAI,GAmBJ,iBAA6C,EAAS,CACpD,GAAM,GAAU,EAAQ,SAAW,GAGnC,MAAI,GAAQ,QACV,EACE,GACA,EAAQ,QAAQ,oBAAoB,IAAI,KAAK,AAAC,GACrC,EAAC,WAIZ,EAAqB,GAAsB,EAAiB,GAEvD,KAAM,GA0Cf,iBAAgC,EAAS,CACvC,GAAI,GAAqB,KAAM,GAAY,EAAmB,QAAS,GAEvE,SAAqB,GAAsB,WAAW,mBAE/C,GAAI,SAAQ,AAAC,GAAY,CAC9B,EAAmB,CACjB,eAAgB,AAAC,GAAU,EAAQ,CAAC,cCjGnC,GAAM,GAA+C,SCU5D,GAAM,GAAwB,CAC5B,WAAY,GACZ,mBAAoB,QAYT,EAAsB,CACjC,KAAM,QACN,GAAI,QACJ,OAAQ,QACR,QAAS,EACT,WAAY,CAAC,OACb,SACA,QAAS,CACP,MAAO,IAIX,iBAAsB,EAAiB,EAA8B,GAA0B,CAE7F,GAAM,CAAC,SAAS,KAAM,GAAuB,GACvC,EAAe,GAAI,GAAa,GAEtC,GAAI,CACF,MAAO,GAAa,WAAW,EAAM,EAAQ,cAC7C,CACA,EAAa,WC5CjB,AAAC,KAAM,CAEL,AAAI,CAAC,EAAW,kBAIhB,GAAW,UAAY,MAAO,EAAM,IAAkC,CACpE,OAAQ,OACD,UACH,GAAI,CACF,GAAM,CAAC,QAAO,WAAW,EACnB,EAAS,KAAM,GAAY,OAAQ,EAAO,GAChD,EAAW,YAAY,OAAQ,CAAC,iBACzB,EAAP,CACA,GAAM,GAAU,YAAiB,OAAQ,EAAM,QAAU,GACzD,EAAW,YAAY,QAAS,CAAC,MAAO,IAE1C", "names": []}