(()=>{var oe=Object.defineProperty;var Ie=r=>oe(r,"__esModule",{value:!0});var Me=(r,e)=>{Ie(r);for(var t in e)oe(r,t,{get:e[t],enumerable:!0})};var v="3.4.15";function ae(r,e){if(!r)throw new Error(e||"loaders.gl assertion failed.")}var g={self:typeof self!="undefined"&&self,window:typeof window!="undefined"&&window,global:typeof global!="undefined"&&global,document:typeof document!="undefined"&&document},yt=g.self||g.window||g.global||{},gt=g.window||g.self||g.global||{},ne=g.global||g.self||g.window||{},bt=g.document||{};var D=typeof process!="object"||String(process)!=="[object process]"||process.browser,I=typeof importScripts=="function",ht=typeof window!="undefined"&&typeof window.orientation!="undefined",se=typeof process!="undefined"&&process.version&&/v([0-9]*)/.exec(process.version),At=se&&parseFloat(se[1])||0;function C(r,e=!0,t){let o=t||new Set;if(r){if(ie(r))o.add(r);else if(ie(r.buffer))o.add(r.buffer);else if(!ArrayBuffer.isView(r)){if(e&&typeof r=="object")for(let n in r)C(r[n],e,o)}}return t===void 0?Array.from(o):[]}function ie(r){return r?r instanceof ArrayBuffer||typeof MessagePort!="undefined"&&r instanceof MessagePort||typeof ImageBitmap!="undefined"&&r instanceof ImageBitmap||typeof OffscreenCanvas!="undefined"&&r instanceof OffscreenCanvas:!1}function x(){let parentPort;try{eval("globalThis.parentPort = require('worker_threads').parentPort"),parentPort=globalThis.parentPort}catch{}return parentPort}var W=new Map,m=class{static inWorkerThread(){return typeof self!="undefined"||Boolean(x())}static set onmessage(e){function t(n){let s=x(),{type:i,payload:l}=s?n:n.data;e(i,l)}let o=x();o?(o.on("message",t),o.on("exit",()=>console.debug("Node worker closing"))):globalThis.onmessage=t}static addEventListener(e){let t=W.get(e);t||(t=n=>{if(!Te(n))return;let s=x(),{type:i,payload:l}=s?n:n.data;e(i,l)}),x()?console.error("not implemented"):globalThis.addEventListener("message",t)}static removeEventListener(e){let t=W.get(e);W.delete(e),x()?console.error("not implemented"):globalThis.removeEventListener("message",t)}static postMessage(e,t){let o={source:"loaders.gl",type:e,payload:t},n=C(t),s=x();s?s.postMessage(o,n):globalThis.postMessage(o,n)}};function Te(r){let{type:e,data:t}=r;return e==="message"&&t&&typeof t.source=="string"&&t.source.startsWith("loaders.gl")}var V={};Me(V,{readFileAsArrayBuffer:()=>_e,readFileAsText:()=>Se,requireFromFile:()=>U,requireFromString:()=>z});var _e=null,Se=null,U=null,z=null;var Oe="latest",Ee=typeof v!="undefined"?v:Oe,$={};async function M(r,e=null,t={}){return e&&(r=ce(r,e,t)),$[r]=$[r]||Le(r),await $[r]}function ce(r,e,t){if(r.startsWith("http"))return r;let o=t.modules||{};return o[r]?o[r]:D?t.CDN?(ae(t.CDN.startsWith("http")),`${t.CDN}/${e}@${Ee}/dist/libs/${r}`):I?`../src/libs/${r}`:`modules/${e}/src/libs/${r}`:`modules/${e}/dist/libs/${r}`}async function Le(r){if(r.endsWith("wasm"))return await(await fetch(r)).arrayBuffer();if(!D)try{return V&&U&&await U(r)}catch{return null}if(I)return importScripts(r);let t=await(await fetch(r)).text();return Fe(t,r)}function Fe(r,e){if(!D)return z&&z(r,e);if(I)return eval.call(ne,r),null;let t=document.createElement("script");t.id=e;try{t.appendChild(document.createTextNode(r))}catch{t.text=r}return document.body.appendChild(t),null}var Ne=0;function q(r){!m.inWorkerThread()||(m.onmessage=async(e,t)=>{switch(e){case"process":try{let{input:o,options:n={},context:s={}}=t,i=await ke({loader:r,arrayBuffer:o,options:n,context:{...s,parse:Pe}});m.postMessage("done",{result:i})}catch(o){let n=o instanceof Error?o.message:"";m.postMessage("error",{error:n})}break;default:}})}function Pe(r,e){return new Promise((t,o)=>{let n=Ne++,s=(l,d)=>{if(d.id===n)switch(l){case"done":m.removeEventListener(s),t(d.result);break;case"error":m.removeEventListener(s),o(d.error);break;default:}};m.addEventListener(s);let i={id:n,input:r,options:e};m.postMessage("process",i)})}async function ke({loader:r,arrayBuffer:e,options:t,context:o}){let n,s;if(r.parseSync||r.parse)n=e,s=r.parseSync||r.parse;else if(r.parseTextSync)n=new TextDecoder().decode(e),s=r.parseTextSync;else throw new Error(`Could not load data with ${r.name} loader`);return t={...t,modules:r&&r.options&&r.options.modules||{},worker:!1},await s(n,{...t},o,r)}var le="3.4.15";var Be={draco:{decoderType:typeof WebAssembly=="object"?"wasm":"js",libraryPath:"libs/",extraAttributes:{},attributeNameEntry:void 0}},de={name:"Draco",id:D?"draco":"draco-nodejs",module:"draco",shapes:["mesh"],version:le,worker:!0,extensions:["drc"],mimeTypes:["application/octet-stream"],binary:!0,tests:["DRACO"],options:Be};function G(r){let e=1/0,t=1/0,o=1/0,n=-1/0,s=-1/0,i=-1/0,l=r.POSITION?r.POSITION.value:[],d=l&&l.length;for(let f=0;f<d;f+=3){let y=l[f],A=l[f+1],w=l[f+2];e=y<e?y:e,t=A<t?A:t,o=w<o?w:o,n=y>n?y:n,s=A>s?A:s,i=w>i?w:i}return[[e,t,o],[n,s,i]]}function ue(r,e){if(!r)throw new Error(e||"loader assertion failed.")}var p=class{constructor(e,t){ue(Array.isArray(e)),Re(e),this.fields=e,this.metadata=t||new Map}compareTo(e){if(this.metadata!==e.metadata||this.fields.length!==e.fields.length)return!1;for(let t=0;t<this.fields.length;++t)if(!this.fields[t].compareTo(e.fields[t]))return!1;return!0}select(...e){let t=Object.create(null);for(let n of e)t[n]=!0;let o=this.fields.filter(n=>t[n.name]);return new p(o,this.metadata)}selectAt(...e){let t=e.map(o=>this.fields[o]).filter(Boolean);return new p(t,this.metadata)}assign(e){let t,o=this.metadata;if(e instanceof p){let i=e;t=i.fields,o=me(me(new Map,this.metadata),i.metadata)}else t=e;let n=Object.create(null);for(let i of this.fields)n[i.name]=i;for(let i of t)n[i.name]=i;let s=Object.values(n);return new p(s,o)}};function Re(r){let e={};for(let t of r)e[t.name]&&console.warn("Schema: duplicated field name",t.name,t),e[t.name]=!0}function me(r,e){return new Map([...r||new Map,...e||new Map])}var b=class{constructor(e,t,o=!1,n=new Map){this.name=e,this.type=t,this.nullable=o,this.metadata=n}get typeId(){return this.type&&this.type.typeId}clone(){return new b(this.name,this.type,this.nullable,this.metadata)}compareTo(e){return this.name===e.name&&this.type===e.type&&this.nullable===e.nullable&&this.metadata===e.metadata}toString(){return`${this.type}${this.nullable?", nullable":""}${this.metadata?`, metadata: ${this.metadata}`:""}`}};var c;(function(a){a[a.NONE=0]="NONE",a[a.Null=1]="Null",a[a.Int=2]="Int",a[a.Float=3]="Float",a[a.Binary=4]="Binary",a[a.Utf8=5]="Utf8",a[a.Bool=6]="Bool",a[a.Decimal=7]="Decimal",a[a.Date=8]="Date",a[a.Time=9]="Time",a[a.Timestamp=10]="Timestamp",a[a.Interval=11]="Interval",a[a.List=12]="List",a[a.Struct=13]="Struct",a[a.Union=14]="Union",a[a.FixedSizeBinary=15]="FixedSizeBinary",a[a.FixedSizeList=16]="FixedSizeList",a[a.Map=17]="Map",a[a.Dictionary=-1]="Dictionary",a[a.Int8=-2]="Int8",a[a.Int16=-3]="Int16",a[a.Int32=-4]="Int32",a[a.Int64=-5]="Int64",a[a.Uint8=-6]="Uint8",a[a.Uint16=-7]="Uint16",a[a.Uint32=-8]="Uint32",a[a.Uint64=-9]="Uint64",a[a.Float16=-10]="Float16",a[a.Float32=-11]="Float32",a[a.Float64=-12]="Float64",a[a.DateDay=-13]="DateDay",a[a.DateMillisecond=-14]="DateMillisecond",a[a.TimestampSecond=-15]="TimestampSecond",a[a.TimestampMillisecond=-16]="TimestampMillisecond",a[a.TimestampMicrosecond=-17]="TimestampMicrosecond",a[a.TimestampNanosecond=-18]="TimestampNanosecond",a[a.TimeSecond=-19]="TimeSecond",a[a.TimeMillisecond=-20]="TimeMillisecond",a[a.TimeMicrosecond=-21]="TimeMicrosecond",a[a.TimeNanosecond=-22]="TimeNanosecond",a[a.DenseUnion=-23]="DenseUnion",a[a.SparseUnion=-24]="SparseUnion",a[a.IntervalDayTime=-25]="IntervalDayTime",a[a.IntervalYearMonth=-26]="IntervalYearMonth"})(c||(c={}));var u=class{static isNull(e){return e&&e.typeId===c.Null}static isInt(e){return e&&e.typeId===c.Int}static isFloat(e){return e&&e.typeId===c.Float}static isBinary(e){return e&&e.typeId===c.Binary}static isUtf8(e){return e&&e.typeId===c.Utf8}static isBool(e){return e&&e.typeId===c.Bool}static isDecimal(e){return e&&e.typeId===c.Decimal}static isDate(e){return e&&e.typeId===c.Date}static isTime(e){return e&&e.typeId===c.Time}static isTimestamp(e){return e&&e.typeId===c.Timestamp}static isInterval(e){return e&&e.typeId===c.Interval}static isList(e){return e&&e.typeId===c.List}static isStruct(e){return e&&e.typeId===c.Struct}static isUnion(e){return e&&e.typeId===c.Union}static isFixedSizeBinary(e){return e&&e.typeId===c.FixedSizeBinary}static isFixedSizeList(e){return e&&e.typeId===c.FixedSizeList}static isMap(e){return e&&e.typeId===c.Map}static isDictionary(e){return e&&e.typeId===c.Dictionary}get typeId(){return c.NONE}compareTo(e){return this===e}},Q=class extends u{get typeId(){return c.Null}get[Symbol.toStringTag](){return"Null"}toString(){return"Null"}},j=class extends u{get typeId(){return c.Bool}get[Symbol.toStringTag](){return"Bool"}toString(){return"Bool"}},h=class extends u{constructor(e,t){super();this.isSigned=e,this.bitWidth=t}get typeId(){return c.Int}get[Symbol.toStringTag](){return"Int"}toString(){return`${this.isSigned?"I":"Ui"}nt${this.bitWidth}`}},T=class extends h{constructor(){super(!0,8)}},_=class extends h{constructor(){super(!0,16)}},S=class extends h{constructor(){super(!0,32)}};var O=class extends h{constructor(){super(!1,8)}},E=class extends h{constructor(){super(!1,16)}},L=class extends h{constructor(){super(!1,32)}};var pe={HALF:16,SINGLE:32,DOUBLE:64},F=class extends u{constructor(e){super();this.precision=e}get typeId(){return c.Float}get[Symbol.toStringTag](){return"Float"}toString(){return`Float${this.precision}`}};var N=class extends F{constructor(){super(pe.SINGLE)}},P=class extends F{constructor(){super(pe.DOUBLE)}},Y=class extends u{constructor(){super()}get typeId(){return c.Binary}toString(){return"Binary"}get[Symbol.toStringTag](){return"Binary"}},H=class extends u{get typeId(){return c.Utf8}get[Symbol.toStringTag](){return"Utf8"}toString(){return"Utf8"}},ve={DAY:0,MILLISECOND:1},J=class extends u{constructor(e){super();this.unit=e}get typeId(){return c.Date}get[Symbol.toStringTag](){return"Date"}toString(){return`Date${(this.unit+1)*32}<${ve[this.unit]}>`}};var fe={SECOND:1,MILLISECOND:1e3,MICROSECOND:1e6,NANOSECOND:1e9},X=class extends u{constructor(e,t){super();this.unit=e,this.bitWidth=t}get typeId(){return c.Time}toString(){return`Time${this.bitWidth}<${fe[this.unit]}>`}get[Symbol.toStringTag](){return"Time"}};var Z=class extends u{constructor(e,t=null){super();this.unit=e,this.timezone=t}get typeId(){return c.Timestamp}get[Symbol.toStringTag](){return"Timestamp"}toString(){return`Timestamp<${fe[this.unit]}${this.timezone?`, ${this.timezone}`:""}>`}};var Ce={DAY_TIME:0,YEAR_MONTH:1},K=class extends u{constructor(e){super();this.unit=e}get typeId(){return c.Interval}get[Symbol.toStringTag](){return"Interval"}toString(){return`Interval<${Ce[this.unit]}>`}};var k=class extends u{constructor(e,t){super();this.listSize=e,this.children=[t]}get typeId(){return c.FixedSizeList}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get[Symbol.toStringTag](){return"FixedSizeList"}toString(){return`FixedSizeList[${this.listSize}]<${this.valueType}>`}},ee=class extends u{constructor(e){super();this.children=e}get typeId(){return c.Struct}toString(){return`Struct<{${this.children.map(e=>`${e.name}:${e.type}`).join(", ")}}>`}get[Symbol.toStringTag](){return"Struct"}};function ye(r){switch(r.constructor){case Int8Array:return new T;case Uint8Array:return new O;case Int16Array:return new _;case Uint16Array:return new E;case Int32Array:return new S;case Uint32Array:return new L;case Float32Array:return new N;case Float64Array:return new P;default:throw new Error("array type not supported")}}function te(r,e,t){let o=ye(e.value),n=t||ge(e);return new b(r,new k(e.size,new b("value",o)),!1,n)}function ge(r){let e=new Map;return"byteOffset"in r&&e.set("byteOffset",r.byteOffset.toString(10)),"byteStride"in r&&e.set("byteStride",r.byteStride.toString(10)),"normalized"in r&&e.set("normalized",r.normalized.toString()),e}function be(r,e,t){let o=Ae(e.metadata),n=[],s=Ze(e.attributes);for(let i in r){let l=r[i],d=he(i,l,s[i]);n.push(d)}if(t){let i=he("indices",t);n.push(i)}return new p(n,o)}function Ze(r){let e={};for(let t in r){let o=r[t];e[o.name||"undefined"]=o}return e}function he(r,e,t){let o=t?Ae(t.metadata):void 0;return te(r,e,o)}function Ae(r){let e=new Map;for(let t in r)e.set(`${t}.string`,JSON.stringify(r[t]));return e}var De={POSITION:"POSITION",NORMAL:"NORMAL",COLOR:"COLOR_0",TEX_COORD:"TEXCOORD_0"},Ke={1:Int8Array,2:Uint8Array,3:Int16Array,4:Uint16Array,5:Int32Array,6:Uint32Array,9:Float32Array},et=4,R=class{constructor(e){this.draco=e,this.decoder=new this.draco.Decoder,this.metadataQuerier=new this.draco.MetadataQuerier}destroy(){this.draco.destroy(this.decoder),this.draco.destroy(this.metadataQuerier)}parseSync(e,t={}){let o=new this.draco.DecoderBuffer;o.Init(new Int8Array(e),e.byteLength),this._disableAttributeTransforms(t);let n=this.decoder.GetEncodedGeometryType(o),s=n===this.draco.TRIANGULAR_MESH?new this.draco.Mesh:new this.draco.PointCloud;try{let i;switch(n){case this.draco.TRIANGULAR_MESH:i=this.decoder.DecodeBufferToMesh(o,s);break;case this.draco.POINT_CLOUD:i=this.decoder.DecodeBufferToPointCloud(o,s);break;default:throw new Error("DRACO: Unknown geometry type.")}if(!i.ok()||!s.ptr){let w=`DRACO decompression failed: ${i.error_msg()}`;throw new Error(w)}let l=this._getDracoLoaderData(s,n,t),d=this._getMeshData(s,l,t),f=G(d.attributes),y=be(d.attributes,l,d.indices);return{loader:"draco",loaderData:l,header:{vertexCount:s.num_points(),boundingBox:f},...d,schema:y}}finally{this.draco.destroy(o),s&&this.draco.destroy(s)}}_getDracoLoaderData(e,t,o){let n=this._getTopLevelMetadata(e),s=this._getDracoAttributes(e,o);return{geometry_type:t,num_attributes:e.num_attributes(),num_points:e.num_points(),num_faces:e instanceof this.draco.Mesh?e.num_faces():0,metadata:n,attributes:s}}_getDracoAttributes(e,t){let o={};for(let n=0;n<e.num_attributes();n++){let s=this.decoder.GetAttribute(e,n),i=this._getAttributeMetadata(e,n);o[s.unique_id()]={unique_id:s.unique_id(),attribute_type:s.attribute_type(),data_type:s.data_type(),num_components:s.num_components(),byte_offset:s.byte_offset(),byte_stride:s.byte_stride(),normalized:s.normalized(),attribute_index:n,metadata:i};let l=this._getQuantizationTransform(s,t);l&&(o[s.unique_id()].quantization_transform=l);let d=this._getOctahedronTransform(s,t);d&&(o[s.unique_id()].octahedron_transform=d)}return o}_getMeshData(e,t,o){let n=this._getMeshAttributes(t,e,o);if(!n.POSITION)throw new Error("DRACO: No position attribute found.");if(e instanceof this.draco.Mesh)switch(o.topology){case"triangle-strip":return{topology:"triangle-strip",mode:4,attributes:n,indices:{value:this._getTriangleStripIndices(e),size:1}};case"triangle-list":default:return{topology:"triangle-list",mode:5,attributes:n,indices:{value:this._getTriangleListIndices(e),size:1}}}return{topology:"point-list",mode:0,attributes:n}}_getMeshAttributes(e,t,o){let n={};for(let s of Object.values(e.attributes)){let i=this._deduceAttributeName(s,o);s.name=i;let{value:l,size:d}=this._getAttributeValues(t,s);n[i]={value:l,size:d,byteOffset:s.byte_offset,byteStride:s.byte_stride,normalized:s.normalized}}return n}_getTriangleListIndices(e){let o=e.num_faces()*3,n=o*et,s=this.draco._malloc(n);try{return this.decoder.GetTrianglesUInt32Array(e,n,s),new Uint32Array(this.draco.HEAPF32.buffer,s,o).slice()}finally{this.draco._free(s)}}_getTriangleStripIndices(e){let t=new this.draco.DracoInt32Array;try{return this.decoder.GetTriangleStripsFromMesh(e,t),ot(t)}finally{this.draco.destroy(t)}}_getAttributeValues(e,t){let o=Ke[t.data_type],n=t.num_components,i=e.num_points()*n,l=i*o.BYTES_PER_ELEMENT,d=tt(this.draco,o),f,y=this.draco._malloc(l);try{let A=this.decoder.GetAttribute(e,t.attribute_index);this.decoder.GetAttributeDataArrayForAllPoints(e,A,d,l,y),f=new o(this.draco.HEAPF32.buffer,y,i).slice()}finally{this.draco._free(y)}return{value:f,size:n}}_deduceAttributeName(e,t){let o=e.unique_id;for(let[i,l]of Object.entries(t.extraAttributes||{}))if(l===o)return i;let n=e.attribute_type;for(let i in De)if(this.draco[i]===n)return De[i];let s=t.attributeNameEntry||"name";return e.metadata[s]?e.metadata[s].string:`CUSTOM_ATTRIBUTE_${o}`}_getTopLevelMetadata(e){let t=this.decoder.GetMetadata(e);return this._getDracoMetadata(t)}_getAttributeMetadata(e,t){let o=this.decoder.GetAttributeMetadata(e,t);return this._getDracoMetadata(o)}_getDracoMetadata(e){if(!e||!e.ptr)return{};let t={},o=this.metadataQuerier.NumEntries(e);for(let n=0;n<o;n++){let s=this.metadataQuerier.GetEntryName(e,n);t[s]=this._getDracoMetadataField(e,s)}return t}_getDracoMetadataField(e,t){let o=new this.draco.DracoInt32Array;try{this.metadataQuerier.GetIntEntryArray(e,t,o);let n=rt(o);return{int:this.metadataQuerier.GetIntEntry(e,t),string:this.metadataQuerier.GetStringEntry(e,t),double:this.metadataQuerier.GetDoubleEntry(e,t),intArray:n}}finally{this.draco.destroy(o)}}_disableAttributeTransforms(e){let{quantizedAttributes:t=[],octahedronAttributes:o=[]}=e,n=[...t,...o];for(let s of n)this.decoder.SkipAttributeTransform(this.draco[s])}_getQuantizationTransform(e,t){let{quantizedAttributes:o=[]}=t,n=e.attribute_type();if(o.map(i=>this.decoder[i]).includes(n)){let i=new this.draco.AttributeQuantizationTransform;try{if(i.InitFromAttribute(e))return{quantization_bits:i.quantization_bits(),range:i.range(),min_values:new Float32Array([1,2,3]).map(l=>i.min_value(l))}}finally{this.draco.destroy(i)}}return null}_getOctahedronTransform(e,t){let{octahedronAttributes:o=[]}=t,n=e.attribute_type();if(o.map(i=>this.decoder[i]).includes(n)){let i=new this.draco.AttributeQuantizationTransform;try{if(i.InitFromAttribute(e))return{quantization_bits:i.quantization_bits()}}finally{this.draco.destroy(i)}}return null}};function tt(r,e){switch(e){case Float32Array:return r.DT_FLOAT32;case Int8Array:return r.DT_INT8;case Int16Array:return r.DT_INT16;case Int32Array:return r.DT_INT32;case Uint8Array:return r.DT_UINT8;case Uint16Array:return r.DT_UINT16;case Uint32Array:return r.DT_UINT32;default:return r.DT_INVALID}}function rt(r){let e=r.size(),t=new Int32Array(e);for(let o=0;o<e;o++)t[o]=r.GetValue(o);return t}function ot(r){let e=r.size(),t=new Int32Array(e);for(let o=0;o<e;o++)t[o]=r.GetValue(o);return t}var at="1.5.5",nt="1.4.1",re=`https://www.gstatic.com/draco/versioned/decoders/${at}`,st=`${re}/draco_decoder.js`,it=`${re}/draco_wasm_wrapper.js`,ct=`${re}/draco_decoder.wasm`,Ar=`https://raw.githubusercontent.com/google/draco/${nt}/javascript/draco_encoder.js`,B;async function xe(r){let e=r.modules||{};return e.draco3d?B=B||e.draco3d.createDecoderModule({}).then(t=>({draco:t})):B=B||lt(r),await B}async function lt(r){let e,t;switch(r.draco&&r.draco.decoderType){case"js":e=await M(st,"draco",r);break;case"wasm":default:[e,t]=await Promise.all([await M(it,"draco",r),await M(ct,"draco",r)])}return e=e||globalThis.DracoDecoderModule,await dt(e,t)}function dt(r,e){let t={};return e&&(t.wasmBinary=e),new Promise(o=>{r({...t,onModuleLoaded:n=>o({draco:n})})})}var we={...de,parse:ut};async function ut(r,e){let{draco:t}=await xe(e),o=new R(t);try{return o.parseSync(r,e?.draco)}finally{o.destroy()}}q(we);})();
//# sourceMappingURL=draco-worker.js.map
