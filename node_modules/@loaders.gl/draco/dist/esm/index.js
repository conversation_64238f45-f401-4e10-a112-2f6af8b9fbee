import { <PERSON><PERSON><PERSON>oa<PERSON> as Dr<PERSON><PERSON>orkerLoader } from './draco-loader';
import DracoParser from './lib/draco-parser';
import { loadDracoDecoderModule } from './lib/draco-module-loader';
import { VERSION } from './lib/utils/version';
import { isBrowser } from '@loaders.gl/worker-utils';
export { DracoWriter } from './draco-writer';
export const DracoWriterWorker = {
  id: isBrowser ? 'draco-writer' : 'draco-writer-nodejs',
  name: 'Draco compressed geometry writer',
  module: 'draco',
  version: VERSION,
  worker: true,
  options: {
    draco: {},
    source: null
  }
};
export { DracoWorkerLoader };
export const DracoLoader = {
  ...DracoWorkerLoader,
  parse
};
async function parse(arrayBuffer, options) {
  const {
    draco
  } = await loadDracoDecoderModule(options);
  const dracoParser = new DracoParser(draco);
  try {
    return dracoParser.parseSync(arrayBuffer, options === null || options === void 0 ? void 0 : options.draco);
  } finally {
    dracoParser.destroy();
  }
}
export const _TypecheckDracoLoader = DracoLoader;
//# sourceMappingURL=index.js.map