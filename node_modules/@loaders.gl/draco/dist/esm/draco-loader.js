import { isBrowser } from '@loaders.gl/worker-utils';
import { VERSION } from './lib/utils/version';
const DEFAULT_DRACO_OPTIONS = {
  draco: {
    decoderType: typeof WebAssembly === 'object' ? 'wasm' : 'js',
    libraryPath: 'libs/',
    extraAttributes: {},
    attributeNameEntry: undefined
  }
};
export const DracoLoader = {
  name: 'Draco',
  id: isBrowser ? 'draco' : 'draco-nodejs',
  module: 'draco',
  shapes: ['mesh'],
  version: VERSION,
  worker: true,
  extensions: ['drc'],
  mimeTypes: ['application/octet-stream'],
  binary: true,
  tests: ['DRACO'],
  options: DEFAULT_DRACO_OPTIONS
};
export const _TypecheckDracoLoader = DracoLoader;
//# sourceMappingURL=draco-loader.js.map