{"version": 3, "file": "draco-loader.js", "names": ["<PERSON><PERSON><PERSON><PERSON>", "VERSION", "DEFAULT_DRACO_OPTIONS", "draco", "decoderType", "WebAssembly", "libraryPath", "extraAttributes", "attributeNameEntry", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "id", "module", "shapes", "version", "worker", "extensions", "mimeTypes", "binary", "tests", "options", "_TypecheckDracoLoader"], "sources": ["../../src/draco-loader.ts"], "sourcesContent": ["import type {Loader, LoaderOptions} from '@loaders.gl/loader-utils';\nimport {isBrowser} from '@loaders.gl/worker-utils';\nimport type {DracoParseOptions} from './lib/draco-parser';\n// import type {DracoMeshData} from './types';\nimport {VERSION} from './lib/utils/version';\n\nexport type DracoLoaderOptions = LoaderOptions & {\n  draco?: DracoParseOptions & {\n    decoderType?: 'wasm' | 'js';\n    libraryPath?: string;\n    extraAttributes?;\n    attributeNameEntry?: string;\n    workerUrl?: string;\n  };\n};\n\nconst DEFAULT_DRACO_OPTIONS: DracoLoaderOptions = {\n  draco: {\n    decoderType: typeof WebAssembly === 'object' ? 'wasm' : 'js', // 'js' for IE11\n    libraryPath: 'libs/',\n    extraAttributes: {},\n    attributeNameEntry: undefined\n  }\n};\n\n/**\n * Worker loader for Draco3D compressed geometries\n */\nexport const DracoLoader = {\n  name: 'Draco',\n  id: isBrowser ? 'draco' : 'draco-nodejs',\n  module: 'draco',\n  shapes: ['mesh'],\n  version: VERSION,\n  worker: true,\n  extensions: ['drc'],\n  mimeTypes: ['application/octet-stream'],\n  binary: true,\n  tests: ['DRACO'],\n  options: DEFAULT_DRACO_OPTIONS\n};\n\nexport const _TypecheckDracoLoader: Loader = DracoLoader;\n"], "mappings": "AACA,SAAQA,SAAS,QAAO,0BAA0B;AAGlD,SAAQC,OAAO,QAAO,qBAAqB;AAY3C,MAAMC,qBAAyC,GAAG;EAChDC,KAAK,EAAE;IACLC,WAAW,EAAE,OAAOC,WAAW,KAAK,QAAQ,GAAG,MAAM,GAAG,IAAI;IAC5DC,WAAW,EAAE,OAAO;IACpBC,eAAe,EAAE,CAAC,CAAC;IACnBC,kBAAkB,EAAEC;EACtB;AACF,CAAC;AAKD,OAAO,MAAMC,WAAW,GAAG;EACzBC,IAAI,EAAE,OAAO;EACbC,EAAE,EAAEZ,SAAS,GAAG,OAAO,GAAG,cAAc;EACxCa,MAAM,EAAE,OAAO;EACfC,MAAM,EAAE,CAAC,MAAM,CAAC;EAChBC,OAAO,EAAEd,OAAO;EAChBe,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,CAAC,KAAK,CAAC;EACnBC,SAAS,EAAE,CAAC,0BAA0B,CAAC;EACvCC,MAAM,EAAE,IAAI;EACZC,KAAK,EAAE,CAAC,OAAO,CAAC;EAChBC,OAAO,EAAEnB;AACX,CAAC;AAED,OAAO,MAAMoB,qBAA6B,GAAGZ,WAAW"}