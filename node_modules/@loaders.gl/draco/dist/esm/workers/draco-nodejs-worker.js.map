{"version": 3, "file": "draco-nodejs-worker.js", "names": ["createLoaderWorker", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["../../../src/workers/draco-nodejs-worker.ts"], "sourcesContent": ["import {createLoaderWorker} from '@loaders.gl/loader-utils';\nimport '@loaders.gl/polyfills';\nimport {DracoLoader} from '../index';\n\ncreateLoaderWorker(DracoLoader);\n"], "mappings": "AAAA,SAAQA,kBAAkB,QAAO,0BAA0B;AAC3D,OAAO,uBAAuB;AAC9B,SAAQC,WAAW,QAAO,UAAU;AAEpCD,kBAAkB,CAACC,WAAW,CAAC"}