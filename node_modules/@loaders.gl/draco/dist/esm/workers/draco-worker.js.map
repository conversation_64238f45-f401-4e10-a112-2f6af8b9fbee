{"version": 3, "file": "draco-worker.js", "names": ["createLoaderWorker", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["../../../src/workers/draco-worker.ts"], "sourcesContent": ["import {createLoaderWorker} from '@loaders.gl/loader-utils';\nimport {DracoLoader} from '../index';\n\ncreateLoaderWorker(DracoLoader);\n"], "mappings": "AAAA,SAAQA,kBAAkB,QAAO,0BAA0B;AAC3D,SAAQC,WAAW,QAAO,UAAU;AAEpCD,kBAAkB,CAACC,WAAW,CAAC"}