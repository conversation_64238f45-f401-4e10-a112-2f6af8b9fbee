import { WorkerBody } from '@loaders.gl/worker-utils';
import { DracoWriterNodeJS } from '../draco-writer-nodejs';
(() => {
  if (!WorkerBody.inWorkerThread()) {
    return;
  }
  WorkerBody.onmessage = async (type, payload) => {
    switch (type) {
      case 'process':
        try {
          const {
            input,
            options
          } = payload;
          const result = await DracoWriterNodeJS.encode(input, options);
          WorkerBody.postMessage('done', {
            result
          });
        } catch (error) {
          const message = error instanceof Error ? error.message : '';
          WorkerBody.postMessage('error', {
            error: message
          });
        }
        break;
      default:
    }
  };
})();
//# sourceMappingURL=draco-writer-nodejs-worker.js.map