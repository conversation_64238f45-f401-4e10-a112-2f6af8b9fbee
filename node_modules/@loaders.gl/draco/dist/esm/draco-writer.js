import DRACOBuilder from './lib/draco-builder';
import { loadDracoEncoderModule } from './lib/draco-module-loader';
import { VERSION } from './lib/utils/version';
const DEFAULT_DRACO_OPTIONS = {
  pointcloud: false,
  attributeNameEntry: 'name'
};
export const DracoWriter = {
  name: 'DRAC<PERSON>',
  id: 'draco',
  module: 'draco',
  version: VERSION,
  extensions: ['drc'],
  encode,
  options: {
    draco: DEFAULT_DRACO_OPTIONS
  }
};
async function encode(data) {
  let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  const {
    draco
  } = await loadDracoEncoderModule(options);
  const dracoBuilder = new DRACOBuilder(draco);
  try {
    return dracoBuilder.encodeSync(data, options.draco);
  } finally {
    dracoBuilder.destroy();
  }
}
//# sourceMappingURL=draco-writer.js.map