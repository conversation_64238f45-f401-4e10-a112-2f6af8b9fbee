{"version": 3, "file": "get-draco-schema.js", "names": ["deduce<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getDracoSchema", "attributes", "loaderData", "indices", "metadataMap", "makeMetadata", "metadata", "fields", "namedLoaderDataAttributes", "transformAttributesLoaderData", "attributeName", "attribute", "field", "getArrowFieldFromAttribute", "push", "indicesField", "result", "key", "dracoAttribute", "name", "undefined", "Map", "set", "concat", "JSON", "stringify"], "sources": ["../../../../src/lib/utils/get-draco-schema.ts"], "sourcesContent": ["import {deduce<PERSON><PERSON><PERSON><PERSON>, MeshAttribute} from '@loaders.gl/schema';\nimport {Schema, Field} from '@loaders.gl/schema';\nimport type {DracoAttribute, DracoLoaderData, DracoMetadataEntry} from '../draco-types';\n\n/** Extract an arrow-like schema from a Draco mesh */\nexport function getDracoSchema(\n  attributes: {[attributeName: string]: MeshAttribute},\n  loaderData: DracoLoaderData,\n  indices?: MeshAttribute\n): Schema {\n  const metadataMap = makeMetadata(loaderData.metadata);\n  const fields: Field[] = [];\n  const namedLoaderDataAttributes = transformAttributesLoaderData(loaderData.attributes);\n  for (const attributeName in attributes) {\n    const attribute = attributes[attributeName];\n    const field = getArrowFieldFromAttribute(\n      attributeName,\n      attribute,\n      namedLoaderDataAttributes[attributeName]\n    );\n    fields.push(field);\n  }\n  if (indices) {\n    const indicesField = getArrowFieldFromAttribute('indices', indices);\n    fields.push(indicesField);\n  }\n  return new Schema(fields, metadataMap);\n}\n\nfunction transformAttributesLoaderData(loaderData: {[key: number]: DracoAttribute}): {\n  [attributeName: string]: DracoAttribute;\n} {\n  const result: {[attributeName: string]: DracoAttribute} = {};\n  for (const key in loaderData) {\n    const dracoAttribute = loaderData[key];\n    result[dracoAttribute.name || 'undefined'] = dracoAttribute;\n  }\n  return result;\n}\n\nfunction getArrowFieldFromAttribute(\n  attributeName: string,\n  attribute: MeshAttribute,\n  loaderData?: DracoAttribute\n): Field {\n  const metadataMap = loaderData ? makeMetadata(loaderData.metadata) : undefined;\n  const field = deduceMeshField(attributeName, attribute, metadataMap);\n  return field;\n}\n\nfunction makeMetadata(metadata: {[key: string]: DracoMetadataEntry}): Map<string, string> {\n  const metadataMap = new Map();\n  for (const key in metadata) {\n    metadataMap.set(`${key}.string`, JSON.stringify(metadata[key]));\n  }\n  return metadataMap;\n}\n"], "mappings": "AAAA,SAAQA,eAAe,QAAsB,oBAAoB;AACjE,SAAQC,MAAM,QAAc,oBAAoB;AAIhD,OAAO,SAASC,cAAcA,CAC5BC,UAAoD,EACpDC,UAA2B,EAC3BC,OAAuB,EACf;EACR,MAAMC,WAAW,GAAGC,YAAY,CAACH,UAAU,CAACI,QAAQ,CAAC;EACrD,MAAMC,MAAe,GAAG,EAAE;EAC1B,MAAMC,yBAAyB,GAAGC,6BAA6B,CAACP,UAAU,CAACD,UAAU,CAAC;EACtF,KAAK,MAAMS,aAAa,IAAIT,UAAU,EAAE;IACtC,MAAMU,SAAS,GAAGV,UAAU,CAACS,aAAa,CAAC;IAC3C,MAAME,KAAK,GAAGC,0BAA0B,CACtCH,aAAa,EACbC,SAAS,EACTH,yBAAyB,CAACE,aAAa,CACzC,CAAC;IACDH,MAAM,CAACO,IAAI,CAACF,KAAK,CAAC;EACpB;EACA,IAAIT,OAAO,EAAE;IACX,MAAMY,YAAY,GAAGF,0BAA0B,CAAC,SAAS,EAAEV,OAAO,CAAC;IACnEI,MAAM,CAACO,IAAI,CAACC,YAAY,CAAC;EAC3B;EACA,OAAO,IAAIhB,MAAM,CAACQ,MAAM,EAAEH,WAAW,CAAC;AACxC;AAEA,SAASK,6BAA6BA,CAACP,UAA2C,EAEhF;EACA,MAAMc,MAAiD,GAAG,CAAC,CAAC;EAC5D,KAAK,MAAMC,GAAG,IAAIf,UAAU,EAAE;IAC5B,MAAMgB,cAAc,GAAGhB,UAAU,CAACe,GAAG,CAAC;IACtCD,MAAM,CAACE,cAAc,CAACC,IAAI,IAAI,WAAW,CAAC,GAAGD,cAAc;EAC7D;EACA,OAAOF,MAAM;AACf;AAEA,SAASH,0BAA0BA,CACjCH,aAAqB,EACrBC,SAAwB,EACxBT,UAA2B,EACpB;EACP,MAAME,WAAW,GAAGF,UAAU,GAAGG,YAAY,CAACH,UAAU,CAACI,QAAQ,CAAC,GAAGc,SAAS;EAC9E,MAAMR,KAAK,GAAGd,eAAe,CAACY,aAAa,EAAEC,SAAS,EAAEP,WAAW,CAAC;EACpE,OAAOQ,KAAK;AACd;AAEA,SAASP,YAAYA,CAACC,QAA6C,EAAuB;EACxF,MAAMF,WAAW,GAAG,IAAIiB,GAAG,CAAC,CAAC;EAC7B,KAAK,MAAMJ,GAAG,IAAIX,QAAQ,EAAE;IAC1BF,WAAW,CAACkB,GAAG,IAAAC,MAAA,CAAIN,GAAG,cAAWO,IAAI,CAACC,SAAS,CAACnB,QAAQ,CAACW,GAAG,CAAC,CAAC,CAAC;EACjE;EACA,OAAOb,WAAW;AACpB"}