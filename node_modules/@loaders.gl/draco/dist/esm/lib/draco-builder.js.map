{"version": 3, "file": "draco-builder.js", "names": ["GLTF_TO_DRACO_ATTRIBUTE_NAME_MAP", "POSITION", "NORMAL", "COLOR_0", "TEXCOORD_0", "noop", "DracoBuilder", "constructor", "draco", "_defineProperty", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Encoder", "dracoMeshBuilder", "MeshBuilder", "dracoMetadataBuilder", "MetadataBuilder", "destroy", "destroyEncodedObject", "object", "encodeSync", "mesh", "options", "arguments", "length", "undefined", "log", "_setOptions", "pointcloud", "_encodePointCloud", "_encodeMesh", "_getAttributesFromMesh", "attributes", "indices", "dracoPointCloud", "PointCloud", "metadata", "_addGeometryMetadata", "_createDracoPointCloud", "dracoData", "DracoInt8Array", "encodedLen", "EncodePointCloudToDracoBuffer", "Error", "concat", "num_points", "num_attributes", "dracoInt8ArrayToArrayBuffer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "_create<PERSON><PERSON><PERSON><PERSON><PERSON>", "EncodeMeshToDracoBuffer", "SetSpeedOptions", "speed", "dracoMethod", "method", "SetEncodingMethod", "attribute", "quantization", "bits", "dracoPosition", "SetAttributeQuantization", "optionalMetadata", "attributesMetadata", "positions", "_getPositionAttribute", "vertexCount", "attributeName", "uniqueId", "_addAttributeToMesh", "_addAttributeMetadata", "name", "error", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "type", "_getDracoAttributeType", "size", "numFaces", "AddFacesToMesh", "builder", "buffer", "Int8Array", "AddInt8Attribute", "Int16Array", "AddInt16Attribute", "Int32Array", "AddInt32Attribute", "Uint8Array", "Uint8ClampedArray", "AddUInt8Attribute", "Uint16Array", "AddUInt16Attribute", "Uint32Array", "AddUInt32Attribute", "Float32Array", "AddFloatAttribute", "toLowerCase", "COLOR", "TEX_COORD", "GENERIC", "dracoType", "dracoGeometry", "dracoMetadata", "<PERSON><PERSON><PERSON>", "_populateDracoMetadata", "AddMetadata", "uniqueAttributeId", "dracoAttributeMetadata", "SetMetadataForAttribute", "key", "value", "getEntries", "Math", "trunc", "AddIntEntry", "AddDoubleEntry", "AddIntEntryArray", "AddStringEntry", "byteLength", "outputBuffer", "outputData", "i", "GetValue", "container", "hasEntriesFunc", "entries", "hasOwnProperty", "Object"], "sources": ["../../../src/lib/draco-builder.ts"], "sourcesContent": ["/* eslint-disable camelcase */\n// This code is inspired by example code in the DRACO repository\nimport type {\n  Draco3D,\n  DracoInt8Array,\n  Encoder,\n  Mesh,\n  MeshBuilder,\n  PointCloud,\n  Metadata,\n  MetadataBuilder,\n  draco_GeometryAttribute_Type\n} from '../draco3d/draco3d-types';\n\nimport type {TypedArray} from '@loaders.gl/schema';\nimport type {DracoMesh} from './draco-types';\n\nexport type DracoBuildOptions = {\n  pointcloud?: boolean;\n  metadata?: {[key: string]: string};\n  attributesMetadata?: {};\n  log?: any;\n\n  // draco encoding options\n  speed?: [number, number];\n  method?: string;\n  quantization?: {[attributeName: string]: number};\n};\n\n// Native Draco attribute names to GLTF attribute names.\nconst GLTF_TO_DRACO_ATTRIBUTE_NAME_MAP = {\n  POSITION: 'POSITION',\n  NORMAL: 'NORMAL',\n  COLOR_0: 'COLOR',\n  TEXCOORD_0: 'TEX_COORD'\n};\n\nconst noop = () => {};\n\nexport default class DracoBuilder {\n  draco: Draco3D;\n  dracoEncoder: Encoder;\n  dracoMeshBuilder: MeshBuilder;\n  dracoMetadataBuilder: MetadataBuilder;\n  log: any;\n\n  // draco - the draco decoder, either import `draco3d` or load dynamically\n  constructor(draco: Draco3D) {\n    this.draco = draco;\n    this.dracoEncoder = new this.draco.Encoder();\n    this.dracoMeshBuilder = new this.draco.MeshBuilder();\n    this.dracoMetadataBuilder = new this.draco.MetadataBuilder();\n  }\n\n  destroy(): void {\n    this.destroyEncodedObject(this.dracoMeshBuilder);\n    this.destroyEncodedObject(this.dracoEncoder);\n    this.destroyEncodedObject(this.dracoMetadataBuilder);\n    // @ts-ignore\n    this.dracoMeshBuilder = null;\n    // @ts-ignore\n    this.dracoEncoder = null;\n    // @ts-ignore\n    this.draco = null;\n  }\n\n  // TBD - when does this need to be called?\n  destroyEncodedObject(object): void {\n    if (object) {\n      this.draco.destroy(object);\n    }\n  }\n\n  /**\n   * Encode mesh or point cloud\n   * @param mesh =({})\n   * @param options\n   */\n  encodeSync(mesh: DracoMesh, options: DracoBuildOptions = {}): ArrayBuffer {\n    this.log = noop; // TODO\n    this._setOptions(options);\n\n    return options.pointcloud\n      ? this._encodePointCloud(mesh, options)\n      : this._encodeMesh(mesh, options);\n  }\n\n  // PRIVATE\n\n  _getAttributesFromMesh(mesh: DracoMesh) {\n    // TODO - Change the encodePointCloud interface instead?\n    const attributes = {...mesh, ...mesh.attributes};\n    // Fold indices into the attributes\n    if (mesh.indices) {\n      attributes.indices = mesh.indices;\n    }\n    return attributes;\n  }\n\n  _encodePointCloud(pointcloud: DracoMesh, options: DracoBuildOptions): ArrayBuffer {\n    const dracoPointCloud = new this.draco.PointCloud();\n\n    if (options.metadata) {\n      this._addGeometryMetadata(dracoPointCloud, options.metadata);\n    }\n\n    const attributes = this._getAttributesFromMesh(pointcloud);\n\n    // Build a `DracoPointCloud` from the input data\n    this._createDracoPointCloud(dracoPointCloud, attributes, options);\n\n    const dracoData = new this.draco.DracoInt8Array();\n\n    try {\n      const encodedLen = this.dracoEncoder.EncodePointCloudToDracoBuffer(\n        dracoPointCloud,\n        false,\n        dracoData\n      );\n\n      if (!(encodedLen > 0)) {\n        throw new Error('Draco encoding failed.');\n      }\n\n      this.log(`DRACO encoded ${dracoPointCloud.num_points()} points\n        with ${dracoPointCloud.num_attributes()} attributes into ${encodedLen} bytes`);\n\n      return dracoInt8ArrayToArrayBuffer(dracoData);\n    } finally {\n      this.destroyEncodedObject(dracoData);\n      this.destroyEncodedObject(dracoPointCloud);\n    }\n  }\n\n  _encodeMesh(mesh: DracoMesh, options: DracoBuildOptions): ArrayBuffer {\n    const dracoMesh = new this.draco.Mesh();\n\n    if (options.metadata) {\n      this._addGeometryMetadata(dracoMesh, options.metadata);\n    }\n\n    const attributes = this._getAttributesFromMesh(mesh);\n\n    // Build a `DracoMesh` from the input data\n    this._createDracoMesh(dracoMesh, attributes, options);\n\n    const dracoData = new this.draco.DracoInt8Array();\n\n    try {\n      const encodedLen = this.dracoEncoder.EncodeMeshToDracoBuffer(dracoMesh, dracoData);\n      if (encodedLen <= 0) {\n        throw new Error('Draco encoding failed.');\n      }\n\n      this.log(`DRACO encoded ${dracoMesh.num_points()} points\n        with ${dracoMesh.num_attributes()} attributes into ${encodedLen} bytes`);\n\n      return dracoInt8ArrayToArrayBuffer(dracoData);\n    } finally {\n      this.destroyEncodedObject(dracoData);\n      this.destroyEncodedObject(dracoMesh);\n    }\n  }\n\n  /**\n   * Set encoding options.\n   * @param {{speed?: any; method?: any; quantization?: any;}} options\n   */\n  _setOptions(options: DracoBuildOptions): void {\n    if ('speed' in options) {\n      // @ts-ignore\n      this.dracoEncoder.SetSpeedOptions(...options.speed);\n    }\n    if ('method' in options) {\n      const dracoMethod = this.draco[options.method || 'MESH_SEQUENTIAL_ENCODING'];\n      // assert(dracoMethod)\n      this.dracoEncoder.SetEncodingMethod(dracoMethod);\n    }\n    if ('quantization' in options) {\n      for (const attribute in options.quantization) {\n        const bits = options.quantization[attribute];\n        const dracoPosition = this.draco[attribute];\n        this.dracoEncoder.SetAttributeQuantization(dracoPosition, bits);\n      }\n    }\n  }\n\n  /**\n   * @param {Mesh} dracoMesh\n   * @param {object} attributes\n   * @returns {Mesh}\n   */\n  _createDracoMesh(dracoMesh: Mesh, attributes, options: DracoBuildOptions): Mesh {\n    const optionalMetadata = options.attributesMetadata || {};\n\n    try {\n      const positions = this._getPositionAttribute(attributes);\n      if (!positions) {\n        throw new Error('positions');\n      }\n      const vertexCount = positions.length / 3;\n\n      for (let attributeName in attributes) {\n        const attribute = attributes[attributeName];\n        attributeName = GLTF_TO_DRACO_ATTRIBUTE_NAME_MAP[attributeName] || attributeName;\n        const uniqueId = this._addAttributeToMesh(dracoMesh, attributeName, attribute, vertexCount);\n\n        if (uniqueId !== -1) {\n          this._addAttributeMetadata(dracoMesh, uniqueId, {\n            name: attributeName,\n            ...(optionalMetadata[attributeName] || {})\n          });\n        }\n      }\n    } catch (error) {\n      this.destroyEncodedObject(dracoMesh);\n      throw error;\n    }\n\n    return dracoMesh;\n  }\n\n  /**\n   * @param {} dracoPointCloud\n   * @param {object} attributes\n   */\n  _createDracoPointCloud(\n    dracoPointCloud: PointCloud,\n    attributes: object,\n    options: DracoBuildOptions\n  ): PointCloud {\n    const optionalMetadata = options.attributesMetadata || {};\n\n    try {\n      const positions = this._getPositionAttribute(attributes);\n      if (!positions) {\n        throw new Error('positions');\n      }\n      const vertexCount = positions.length / 3;\n\n      for (let attributeName in attributes) {\n        const attribute = attributes[attributeName];\n        attributeName = GLTF_TO_DRACO_ATTRIBUTE_NAME_MAP[attributeName] || attributeName;\n        const uniqueId = this._addAttributeToMesh(\n          dracoPointCloud,\n          attributeName,\n          attribute,\n          vertexCount\n        );\n        if (uniqueId !== -1) {\n          this._addAttributeMetadata(dracoPointCloud, uniqueId, {\n            name: attributeName,\n            ...(optionalMetadata[attributeName] || {})\n          });\n        }\n      }\n    } catch (error) {\n      this.destroyEncodedObject(dracoPointCloud);\n      throw error;\n    }\n\n    return dracoPointCloud;\n  }\n\n  /**\n   * @param mesh\n   * @param attributeName\n   * @param attribute\n   * @param vertexCount\n   */\n  _addAttributeToMesh(\n    mesh: PointCloud,\n    attributeName: string,\n    attribute: TypedArray,\n    vertexCount: number\n  ) {\n    if (!ArrayBuffer.isView(attribute)) {\n      return -1;\n    }\n\n    const type = this._getDracoAttributeType(attributeName);\n    // @ts-ignore TODO/fix types\n    const size = attribute.length / vertexCount;\n\n    if (type === 'indices') {\n      // @ts-ignore TODO/fix types\n      const numFaces = attribute.length / 3;\n      this.log(`Adding attribute ${attributeName}, size ${numFaces}`);\n\n      // @ts-ignore assumes mesh is a Mesh, not a point cloud\n      this.dracoMeshBuilder.AddFacesToMesh(mesh, numFaces, attribute);\n      return -1;\n    }\n\n    this.log(`Adding attribute ${attributeName}, size ${size}`);\n\n    const builder = this.dracoMeshBuilder;\n    const {buffer} = attribute;\n\n    switch (attribute.constructor) {\n      case Int8Array:\n        return builder.AddInt8Attribute(mesh, type, vertexCount, size, new Int8Array(buffer));\n\n      case Int16Array:\n        return builder.AddInt16Attribute(mesh, type, vertexCount, size, new Int16Array(buffer));\n\n      case Int32Array:\n        return builder.AddInt32Attribute(mesh, type, vertexCount, size, new Int32Array(buffer));\n      case Uint8Array:\n      case Uint8ClampedArray:\n        return builder.AddUInt8Attribute(mesh, type, vertexCount, size, new Uint8Array(buffer));\n\n      case Uint16Array:\n        return builder.AddUInt16Attribute(mesh, type, vertexCount, size, new Uint16Array(buffer));\n\n      case Uint32Array:\n        return builder.AddUInt32Attribute(mesh, type, vertexCount, size, new Uint32Array(buffer));\n\n      case Float32Array:\n      default:\n        return builder.AddFloatAttribute(mesh, type, vertexCount, size, new Float32Array(buffer));\n    }\n  }\n\n  /**\n   * DRACO can compress attributes of know type better\n   * TODO - expose an attribute type map?\n   * @param attributeName\n   */\n  _getDracoAttributeType(attributeName: string): draco_GeometryAttribute_Type | 'indices' {\n    switch (attributeName.toLowerCase()) {\n      case 'indices':\n        return 'indices';\n      case 'position':\n      case 'positions':\n      case 'vertices':\n        return this.draco.POSITION;\n      case 'normal':\n      case 'normals':\n        return this.draco.NORMAL;\n      case 'color':\n      case 'colors':\n        return this.draco.COLOR;\n      case 'texcoord':\n      case 'texcoords':\n        return this.draco.TEX_COORD;\n      default:\n        return this.draco.GENERIC;\n    }\n  }\n\n  _getPositionAttribute(attributes) {\n    for (const attributeName in attributes) {\n      const attribute = attributes[attributeName];\n      const dracoType = this._getDracoAttributeType(attributeName);\n      if (dracoType === this.draco.POSITION) {\n        return attribute;\n      }\n    }\n    return null;\n  }\n\n  /**\n   * Add metadata for the geometry.\n   * @param dracoGeometry - WASM Draco Object\n   * @param metadata\n   */\n  _addGeometryMetadata(dracoGeometry: PointCloud, metadata: {[key: string]: string}) {\n    const dracoMetadata = new this.draco.Metadata();\n    this._populateDracoMetadata(dracoMetadata, metadata);\n    this.dracoMeshBuilder.AddMetadata(dracoGeometry, dracoMetadata);\n  }\n\n  /**\n   * Add metadata for an attribute to geometry.\n   * @param dracoGeometry - WASM Draco Object\n   * @param uniqueAttributeId\n   * @param metadata\n   */\n  _addAttributeMetadata(\n    dracoGeometry: PointCloud,\n    uniqueAttributeId: number,\n    metadata: Map<string, string> | {[key: string]: string}\n  ) {\n    // Note: Draco JS IDL doesn't seem to expose draco.AttributeMetadata, however it seems to\n    // create such objects automatically from draco.Metadata object.\n    const dracoAttributeMetadata = new this.draco.Metadata();\n    this._populateDracoMetadata(dracoAttributeMetadata, metadata);\n    // Draco3d doc note: Directly add attribute metadata to geometry.\n    // You can do this without explicitly adding |GeometryMetadata| to mesh.\n    this.dracoMeshBuilder.SetMetadataForAttribute(\n      dracoGeometry,\n      uniqueAttributeId,\n      dracoAttributeMetadata\n    );\n  }\n\n  /**\n   * Add contents of object or map to a WASM Draco Metadata Object\n   * @param dracoMetadata - WASM Draco Object\n   * @param metadata\n   */\n  _populateDracoMetadata(\n    dracoMetadata: Metadata,\n    metadata: Map<string, string> | {[key: string]: string}\n  ) {\n    for (const [key, value] of getEntries(metadata)) {\n      switch (typeof value) {\n        case 'number':\n          if (Math.trunc(value) === value) {\n            this.dracoMetadataBuilder.AddIntEntry(dracoMetadata, key, value);\n          } else {\n            this.dracoMetadataBuilder.AddDoubleEntry(dracoMetadata, key, value);\n          }\n          break;\n        case 'object':\n          if (value instanceof Int32Array) {\n            this.dracoMetadataBuilder.AddIntEntryArray(dracoMetadata, key, value, value.length);\n          }\n          break;\n        case 'string':\n        default:\n          this.dracoMetadataBuilder.AddStringEntry(dracoMetadata, key, value);\n      }\n    }\n  }\n}\n\n// HELPER FUNCTIONS\n\n/**\n * Copy encoded data to buffer\n * @param dracoData\n */\nfunction dracoInt8ArrayToArrayBuffer(dracoData: DracoInt8Array) {\n  const byteLength = dracoData.size();\n  const outputBuffer = new ArrayBuffer(byteLength);\n  const outputData = new Int8Array(outputBuffer);\n  for (let i = 0; i < byteLength; ++i) {\n    outputData[i] = dracoData.GetValue(i);\n  }\n  return outputBuffer;\n}\n\n/** Enable iteration over either an object or a map */\nfunction getEntries(container) {\n  const hasEntriesFunc = container.entries && !container.hasOwnProperty('entries');\n  return hasEntriesFunc ? container.entries() : Object.entries(container);\n}\n"], "mappings": ";AA8BA,MAAMA,gCAAgC,GAAG;EACvCC,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,OAAO;EAChBC,UAAU,EAAE;AACd,CAAC;AAED,MAAMC,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AAErB,eAAe,MAAMC,YAAY,CAAC;EAQhCC,WAAWA,CAACC,KAAc,EAAE;IAAAC,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAC1B,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACE,YAAY,GAAG,IAAI,IAAI,CAACF,KAAK,CAACG,OAAO,CAAC,CAAC;IAC5C,IAAI,CAACC,gBAAgB,GAAG,IAAI,IAAI,CAACJ,KAAK,CAACK,WAAW,CAAC,CAAC;IACpD,IAAI,CAACC,oBAAoB,GAAG,IAAI,IAAI,CAACN,KAAK,CAACO,eAAe,CAAC,CAAC;EAC9D;EAEAC,OAAOA,CAAA,EAAS;IACd,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACL,gBAAgB,CAAC;IAChD,IAAI,CAACK,oBAAoB,CAAC,IAAI,CAACP,YAAY,CAAC;IAC5C,IAAI,CAACO,oBAAoB,CAAC,IAAI,CAACH,oBAAoB,CAAC;IAEpD,IAAI,CAACF,gBAAgB,GAAG,IAAI;IAE5B,IAAI,CAACF,YAAY,GAAG,IAAI;IAExB,IAAI,CAACF,KAAK,GAAG,IAAI;EACnB;EAGAS,oBAAoBA,CAACC,MAAM,EAAQ;IACjC,IAAIA,MAAM,EAAE;MACV,IAAI,CAACV,KAAK,CAACQ,OAAO,CAACE,MAAM,CAAC;IAC5B;EACF;EAOAC,UAAUA,CAACC,IAAe,EAAgD;IAAA,IAA9CC,OAA0B,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IACzD,IAAI,CAACG,GAAG,GAAGpB,IAAI;IACf,IAAI,CAACqB,WAAW,CAACL,OAAO,CAAC;IAEzB,OAAOA,OAAO,CAACM,UAAU,GACrB,IAAI,CAACC,iBAAiB,CAACR,IAAI,EAAEC,OAAO,CAAC,GACrC,IAAI,CAACQ,WAAW,CAACT,IAAI,EAAEC,OAAO,CAAC;EACrC;EAIAS,sBAAsBA,CAACV,IAAe,EAAE;IAEtC,MAAMW,UAAU,GAAG;MAAC,GAAGX,IAAI;MAAE,GAAGA,IAAI,CAACW;IAAU,CAAC;IAEhD,IAAIX,IAAI,CAACY,OAAO,EAAE;MAChBD,UAAU,CAACC,OAAO,GAAGZ,IAAI,CAACY,OAAO;IACnC;IACA,OAAOD,UAAU;EACnB;EAEAH,iBAAiBA,CAACD,UAAqB,EAAEN,OAA0B,EAAe;IAChF,MAAMY,eAAe,GAAG,IAAI,IAAI,CAACzB,KAAK,CAAC0B,UAAU,CAAC,CAAC;IAEnD,IAAIb,OAAO,CAACc,QAAQ,EAAE;MACpB,IAAI,CAACC,oBAAoB,CAACH,eAAe,EAAEZ,OAAO,CAACc,QAAQ,CAAC;IAC9D;IAEA,MAAMJ,UAAU,GAAG,IAAI,CAACD,sBAAsB,CAACH,UAAU,CAAC;IAG1D,IAAI,CAACU,sBAAsB,CAACJ,eAAe,EAAEF,UAAU,EAAEV,OAAO,CAAC;IAEjE,MAAMiB,SAAS,GAAG,IAAI,IAAI,CAAC9B,KAAK,CAAC+B,cAAc,CAAC,CAAC;IAEjD,IAAI;MACF,MAAMC,UAAU,GAAG,IAAI,CAAC9B,YAAY,CAAC+B,6BAA6B,CAChER,eAAe,EACf,KAAK,EACLK,SACF,CAAC;MAED,IAAI,EAAEE,UAAU,GAAG,CAAC,CAAC,EAAE;QACrB,MAAM,IAAIE,KAAK,CAAC,wBAAwB,CAAC;MAC3C;MAEA,IAAI,CAACjB,GAAG,kBAAAkB,MAAA,CAAkBV,eAAe,CAACW,UAAU,CAAC,CAAC,4BAAAD,MAAA,CAC7CV,eAAe,CAACY,cAAc,CAAC,CAAC,uBAAAF,MAAA,CAAoBH,UAAU,WAAQ,CAAC;MAEhF,OAAOM,2BAA2B,CAACR,SAAS,CAAC;IAC/C,CAAC,SAAS;MACR,IAAI,CAACrB,oBAAoB,CAACqB,SAAS,CAAC;MACpC,IAAI,CAACrB,oBAAoB,CAACgB,eAAe,CAAC;IAC5C;EACF;EAEAJ,WAAWA,CAACT,IAAe,EAAEC,OAA0B,EAAe;IACpE,MAAM0B,SAAS,GAAG,IAAI,IAAI,CAACvC,KAAK,CAACwC,IAAI,CAAC,CAAC;IAEvC,IAAI3B,OAAO,CAACc,QAAQ,EAAE;MACpB,IAAI,CAACC,oBAAoB,CAACW,SAAS,EAAE1B,OAAO,CAACc,QAAQ,CAAC;IACxD;IAEA,MAAMJ,UAAU,GAAG,IAAI,CAACD,sBAAsB,CAACV,IAAI,CAAC;IAGpD,IAAI,CAAC6B,gBAAgB,CAACF,SAAS,EAAEhB,UAAU,EAAEV,OAAO,CAAC;IAErD,MAAMiB,SAAS,GAAG,IAAI,IAAI,CAAC9B,KAAK,CAAC+B,cAAc,CAAC,CAAC;IAEjD,IAAI;MACF,MAAMC,UAAU,GAAG,IAAI,CAAC9B,YAAY,CAACwC,uBAAuB,CAACH,SAAS,EAAET,SAAS,CAAC;MAClF,IAAIE,UAAU,IAAI,CAAC,EAAE;QACnB,MAAM,IAAIE,KAAK,CAAC,wBAAwB,CAAC;MAC3C;MAEA,IAAI,CAACjB,GAAG,kBAAAkB,MAAA,CAAkBI,SAAS,CAACH,UAAU,CAAC,CAAC,4BAAAD,MAAA,CACvCI,SAAS,CAACF,cAAc,CAAC,CAAC,uBAAAF,MAAA,CAAoBH,UAAU,WAAQ,CAAC;MAE1E,OAAOM,2BAA2B,CAACR,SAAS,CAAC;IAC/C,CAAC,SAAS;MACR,IAAI,CAACrB,oBAAoB,CAACqB,SAAS,CAAC;MACpC,IAAI,CAACrB,oBAAoB,CAAC8B,SAAS,CAAC;IACtC;EACF;EAMArB,WAAWA,CAACL,OAA0B,EAAQ;IAC5C,IAAI,OAAO,IAAIA,OAAO,EAAE;MAEtB,IAAI,CAACX,YAAY,CAACyC,eAAe,CAAC,GAAG9B,OAAO,CAAC+B,KAAK,CAAC;IACrD;IACA,IAAI,QAAQ,IAAI/B,OAAO,EAAE;MACvB,MAAMgC,WAAW,GAAG,IAAI,CAAC7C,KAAK,CAACa,OAAO,CAACiC,MAAM,IAAI,0BAA0B,CAAC;MAE5E,IAAI,CAAC5C,YAAY,CAAC6C,iBAAiB,CAACF,WAAW,CAAC;IAClD;IACA,IAAI,cAAc,IAAIhC,OAAO,EAAE;MAC7B,KAAK,MAAMmC,SAAS,IAAInC,OAAO,CAACoC,YAAY,EAAE;QAC5C,MAAMC,IAAI,GAAGrC,OAAO,CAACoC,YAAY,CAACD,SAAS,CAAC;QAC5C,MAAMG,aAAa,GAAG,IAAI,CAACnD,KAAK,CAACgD,SAAS,CAAC;QAC3C,IAAI,CAAC9C,YAAY,CAACkD,wBAAwB,CAACD,aAAa,EAAED,IAAI,CAAC;MACjE;IACF;EACF;EAOAT,gBAAgBA,CAACF,SAAe,EAAEhB,UAAU,EAAEV,OAA0B,EAAQ;IAC9E,MAAMwC,gBAAgB,GAAGxC,OAAO,CAACyC,kBAAkB,IAAI,CAAC,CAAC;IAEzD,IAAI;MACF,MAAMC,SAAS,GAAG,IAAI,CAACC,qBAAqB,CAACjC,UAAU,CAAC;MACxD,IAAI,CAACgC,SAAS,EAAE;QACd,MAAM,IAAIrB,KAAK,CAAC,WAAW,CAAC;MAC9B;MACA,MAAMuB,WAAW,GAAGF,SAAS,CAACxC,MAAM,GAAG,CAAC;MAExC,KAAK,IAAI2C,aAAa,IAAInC,UAAU,EAAE;QACpC,MAAMyB,SAAS,GAAGzB,UAAU,CAACmC,aAAa,CAAC;QAC3CA,aAAa,GAAGlE,gCAAgC,CAACkE,aAAa,CAAC,IAAIA,aAAa;QAChF,MAAMC,QAAQ,GAAG,IAAI,CAACC,mBAAmB,CAACrB,SAAS,EAAEmB,aAAa,EAAEV,SAAS,EAAES,WAAW,CAAC;QAE3F,IAAIE,QAAQ,KAAK,CAAC,CAAC,EAAE;UACnB,IAAI,CAACE,qBAAqB,CAACtB,SAAS,EAAEoB,QAAQ,EAAE;YAC9CG,IAAI,EAAEJ,aAAa;YACnB,IAAIL,gBAAgB,CAACK,aAAa,CAAC,IAAI,CAAC,CAAC;UAC3C,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC,OAAOK,KAAK,EAAE;MACd,IAAI,CAACtD,oBAAoB,CAAC8B,SAAS,CAAC;MACpC,MAAMwB,KAAK;IACb;IAEA,OAAOxB,SAAS;EAClB;EAMAV,sBAAsBA,CACpBJ,eAA2B,EAC3BF,UAAkB,EAClBV,OAA0B,EACd;IACZ,MAAMwC,gBAAgB,GAAGxC,OAAO,CAACyC,kBAAkB,IAAI,CAAC,CAAC;IAEzD,IAAI;MACF,MAAMC,SAAS,GAAG,IAAI,CAACC,qBAAqB,CAACjC,UAAU,CAAC;MACxD,IAAI,CAACgC,SAAS,EAAE;QACd,MAAM,IAAIrB,KAAK,CAAC,WAAW,CAAC;MAC9B;MACA,MAAMuB,WAAW,GAAGF,SAAS,CAACxC,MAAM,GAAG,CAAC;MAExC,KAAK,IAAI2C,aAAa,IAAInC,UAAU,EAAE;QACpC,MAAMyB,SAAS,GAAGzB,UAAU,CAACmC,aAAa,CAAC;QAC3CA,aAAa,GAAGlE,gCAAgC,CAACkE,aAAa,CAAC,IAAIA,aAAa;QAChF,MAAMC,QAAQ,GAAG,IAAI,CAACC,mBAAmB,CACvCnC,eAAe,EACfiC,aAAa,EACbV,SAAS,EACTS,WACF,CAAC;QACD,IAAIE,QAAQ,KAAK,CAAC,CAAC,EAAE;UACnB,IAAI,CAACE,qBAAqB,CAACpC,eAAe,EAAEkC,QAAQ,EAAE;YACpDG,IAAI,EAAEJ,aAAa;YACnB,IAAIL,gBAAgB,CAACK,aAAa,CAAC,IAAI,CAAC,CAAC;UAC3C,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC,OAAOK,KAAK,EAAE;MACd,IAAI,CAACtD,oBAAoB,CAACgB,eAAe,CAAC;MAC1C,MAAMsC,KAAK;IACb;IAEA,OAAOtC,eAAe;EACxB;EAQAmC,mBAAmBA,CACjBhD,IAAgB,EAChB8C,aAAqB,EACrBV,SAAqB,EACrBS,WAAmB,EACnB;IACA,IAAI,CAACO,WAAW,CAACC,MAAM,CAACjB,SAAS,CAAC,EAAE;MAClC,OAAO,CAAC,CAAC;IACX;IAEA,MAAMkB,IAAI,GAAG,IAAI,CAACC,sBAAsB,CAACT,aAAa,CAAC;IAEvD,MAAMU,IAAI,GAAGpB,SAAS,CAACjC,MAAM,GAAG0C,WAAW;IAE3C,IAAIS,IAAI,KAAK,SAAS,EAAE;MAEtB,MAAMG,QAAQ,GAAGrB,SAAS,CAACjC,MAAM,GAAG,CAAC;MACrC,IAAI,CAACE,GAAG,qBAAAkB,MAAA,CAAqBuB,aAAa,aAAAvB,MAAA,CAAUkC,QAAQ,CAAE,CAAC;MAG/D,IAAI,CAACjE,gBAAgB,CAACkE,cAAc,CAAC1D,IAAI,EAAEyD,QAAQ,EAAErB,SAAS,CAAC;MAC/D,OAAO,CAAC,CAAC;IACX;IAEA,IAAI,CAAC/B,GAAG,qBAAAkB,MAAA,CAAqBuB,aAAa,aAAAvB,MAAA,CAAUiC,IAAI,CAAE,CAAC;IAE3D,MAAMG,OAAO,GAAG,IAAI,CAACnE,gBAAgB;IACrC,MAAM;MAACoE;IAAM,CAAC,GAAGxB,SAAS;IAE1B,QAAQA,SAAS,CAACjD,WAAW;MAC3B,KAAK0E,SAAS;QACZ,OAAOF,OAAO,CAACG,gBAAgB,CAAC9D,IAAI,EAAEsD,IAAI,EAAET,WAAW,EAAEW,IAAI,EAAE,IAAIK,SAAS,CAACD,MAAM,CAAC,CAAC;MAEvF,KAAKG,UAAU;QACb,OAAOJ,OAAO,CAACK,iBAAiB,CAAChE,IAAI,EAAEsD,IAAI,EAAET,WAAW,EAAEW,IAAI,EAAE,IAAIO,UAAU,CAACH,MAAM,CAAC,CAAC;MAEzF,KAAKK,UAAU;QACb,OAAON,OAAO,CAACO,iBAAiB,CAAClE,IAAI,EAAEsD,IAAI,EAAET,WAAW,EAAEW,IAAI,EAAE,IAAIS,UAAU,CAACL,MAAM,CAAC,CAAC;MACzF,KAAKO,UAAU;MACf,KAAKC,iBAAiB;QACpB,OAAOT,OAAO,CAACU,iBAAiB,CAACrE,IAAI,EAAEsD,IAAI,EAAET,WAAW,EAAEW,IAAI,EAAE,IAAIW,UAAU,CAACP,MAAM,CAAC,CAAC;MAEzF,KAAKU,WAAW;QACd,OAAOX,OAAO,CAACY,kBAAkB,CAACvE,IAAI,EAAEsD,IAAI,EAAET,WAAW,EAAEW,IAAI,EAAE,IAAIc,WAAW,CAACV,MAAM,CAAC,CAAC;MAE3F,KAAKY,WAAW;QACd,OAAOb,OAAO,CAACc,kBAAkB,CAACzE,IAAI,EAAEsD,IAAI,EAAET,WAAW,EAAEW,IAAI,EAAE,IAAIgB,WAAW,CAACZ,MAAM,CAAC,CAAC;MAE3F,KAAKc,YAAY;MACjB;QACE,OAAOf,OAAO,CAACgB,iBAAiB,CAAC3E,IAAI,EAAEsD,IAAI,EAAET,WAAW,EAAEW,IAAI,EAAE,IAAIkB,YAAY,CAACd,MAAM,CAAC,CAAC;IAC7F;EACF;EAOAL,sBAAsBA,CAACT,aAAqB,EAA4C;IACtF,QAAQA,aAAa,CAAC8B,WAAW,CAAC,CAAC;MACjC,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB,KAAK,UAAU;MACf,KAAK,WAAW;MAChB,KAAK,UAAU;QACb,OAAO,IAAI,CAACxF,KAAK,CAACP,QAAQ;MAC5B,KAAK,QAAQ;MACb,KAAK,SAAS;QACZ,OAAO,IAAI,CAACO,KAAK,CAACN,MAAM;MAC1B,KAAK,OAAO;MACZ,KAAK,QAAQ;QACX,OAAO,IAAI,CAACM,KAAK,CAACyF,KAAK;MACzB,KAAK,UAAU;MACf,KAAK,WAAW;QACd,OAAO,IAAI,CAACzF,KAAK,CAAC0F,SAAS;MAC7B;QACE,OAAO,IAAI,CAAC1F,KAAK,CAAC2F,OAAO;IAC7B;EACF;EAEAnC,qBAAqBA,CAACjC,UAAU,EAAE;IAChC,KAAK,MAAMmC,aAAa,IAAInC,UAAU,EAAE;MACtC,MAAMyB,SAAS,GAAGzB,UAAU,CAACmC,aAAa,CAAC;MAC3C,MAAMkC,SAAS,GAAG,IAAI,CAACzB,sBAAsB,CAACT,aAAa,CAAC;MAC5D,IAAIkC,SAAS,KAAK,IAAI,CAAC5F,KAAK,CAACP,QAAQ,EAAE;QACrC,OAAOuD,SAAS;MAClB;IACF;IACA,OAAO,IAAI;EACb;EAOApB,oBAAoBA,CAACiE,aAAyB,EAAElE,QAAiC,EAAE;IACjF,MAAMmE,aAAa,GAAG,IAAI,IAAI,CAAC9F,KAAK,CAAC+F,QAAQ,CAAC,CAAC;IAC/C,IAAI,CAACC,sBAAsB,CAACF,aAAa,EAAEnE,QAAQ,CAAC;IACpD,IAAI,CAACvB,gBAAgB,CAAC6F,WAAW,CAACJ,aAAa,EAAEC,aAAa,CAAC;EACjE;EAQAjC,qBAAqBA,CACnBgC,aAAyB,EACzBK,iBAAyB,EACzBvE,QAAuD,EACvD;IAGA,MAAMwE,sBAAsB,GAAG,IAAI,IAAI,CAACnG,KAAK,CAAC+F,QAAQ,CAAC,CAAC;IACxD,IAAI,CAACC,sBAAsB,CAACG,sBAAsB,EAAExE,QAAQ,CAAC;IAG7D,IAAI,CAACvB,gBAAgB,CAACgG,uBAAuB,CAC3CP,aAAa,EACbK,iBAAiB,EACjBC,sBACF,CAAC;EACH;EAOAH,sBAAsBA,CACpBF,aAAuB,EACvBnE,QAAuD,EACvD;IACA,KAAK,MAAM,CAAC0E,GAAG,EAAEC,KAAK,CAAC,IAAIC,UAAU,CAAC5E,QAAQ,CAAC,EAAE;MAC/C,QAAQ,OAAO2E,KAAK;QAClB,KAAK,QAAQ;UACX,IAAIE,IAAI,CAACC,KAAK,CAACH,KAAK,CAAC,KAAKA,KAAK,EAAE;YAC/B,IAAI,CAAChG,oBAAoB,CAACoG,WAAW,CAACZ,aAAa,EAAEO,GAAG,EAAEC,KAAK,CAAC;UAClE,CAAC,MAAM;YACL,IAAI,CAAChG,oBAAoB,CAACqG,cAAc,CAACb,aAAa,EAAEO,GAAG,EAAEC,KAAK,CAAC;UACrE;UACA;QACF,KAAK,QAAQ;UACX,IAAIA,KAAK,YAAYzB,UAAU,EAAE;YAC/B,IAAI,CAACvE,oBAAoB,CAACsG,gBAAgB,CAACd,aAAa,EAAEO,GAAG,EAAEC,KAAK,EAAEA,KAAK,CAACvF,MAAM,CAAC;UACrF;UACA;QACF,KAAK,QAAQ;QACb;UACE,IAAI,CAACT,oBAAoB,CAACuG,cAAc,CAACf,aAAa,EAAEO,GAAG,EAAEC,KAAK,CAAC;MACvE;IACF;EACF;AACF;AAQA,SAAShE,2BAA2BA,CAACR,SAAyB,EAAE;EAC9D,MAAMgF,UAAU,GAAGhF,SAAS,CAACsC,IAAI,CAAC,CAAC;EACnC,MAAM2C,YAAY,GAAG,IAAI/C,WAAW,CAAC8C,UAAU,CAAC;EAChD,MAAME,UAAU,GAAG,IAAIvC,SAAS,CAACsC,YAAY,CAAC;EAC9C,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,UAAU,EAAE,EAAEG,CAAC,EAAE;IACnCD,UAAU,CAACC,CAAC,CAAC,GAAGnF,SAAS,CAACoF,QAAQ,CAACD,CAAC,CAAC;EACvC;EACA,OAAOF,YAAY;AACrB;AAGA,SAASR,UAAUA,CAACY,SAAS,EAAE;EAC7B,MAAMC,cAAc,GAAGD,SAAS,CAACE,OAAO,IAAI,CAACF,SAAS,CAACG,cAAc,CAAC,SAAS,CAAC;EAChF,OAAOF,cAAc,GAAGD,SAAS,CAACE,OAAO,CAAC,CAAC,GAAGE,MAAM,CAACF,OAAO,CAACF,SAAS,CAAC;AACzE"}