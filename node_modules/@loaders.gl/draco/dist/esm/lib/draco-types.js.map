{"version": 3, "file": "draco-types.js", "names": [], "sources": ["../../../src/lib/draco-types.ts"], "sourcesContent": ["/* eslint-disable camelcase */\n\nimport {Mesh} from '@loaders.gl/schema';\n\n// DRACO FORMAT SPECIFIC DATA\n\nexport type DracoMetadataEntry = {\n  int: number;\n  string: string;\n  double: number;\n  intArray: Int32Array;\n};\n\n/** For attributes that have not been fully decompressed */\nexport type DracoQuantizationTransform = {\n  quantization_bits?: number;\n  range?: number;\n  min_values?: Float32Array;\n};\n\n/** For attributes that have not been fully decompressed */\nexport type DracoOctahedronTransform = {\n  quantization_bits?: number;\n};\n\n/** Draco attribute fields */\nexport type DracoAttribute = {\n  unique_id: number;\n\n  num_components: number; // Duplicate of size\n  attribute_type: number;\n  data_type: number;\n\n  byte_offset: number;\n  byte_stride: number;\n  normalized: boolean;\n  name?: string;\n\n  quantization_transform?: DracoQuantizationTransform;\n  octahedron_transform?: DracoOctahedronTransform;\n\n  metadata: {[key: string]: DracoMetadataEntry};\n  attribute_index: number;\n};\n\n/**\n * Draco format specific data\n * The `data.loaderData` field will have this layout when `data.loader === 'draco'`.\n * @todo Metadata should also be available in normalized form in a standard `Schema`.\n */\nexport type DracoLoaderData = {\n  geometry_type: number;\n  num_attributes: number;\n  num_points: number;\n  num_faces: number;\n  metadata: {[entry: string]: DracoMetadataEntry};\n  attributes: {[unique_id: number]: DracoAttribute};\n  // attribute_names?: {[unique_id: number]: string};\n  // unique_ids?: {[attributeName: string]: number};\n};\n\n/**\n * loaders.gl Mesh with Draco specific data\n */\nexport type DracoMesh = Mesh & {\n  loader: 'draco';\n  loaderData: DracoLoaderData; // Draco specific data\n};\n"], "mappings": ""}