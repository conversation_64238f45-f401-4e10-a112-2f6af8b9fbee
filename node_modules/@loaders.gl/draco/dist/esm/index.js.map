{"version": 3, "file": "index.js", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "DracoWorkerLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loadDracoDecoderModule", "VERSION", "<PERSON><PERSON><PERSON><PERSON>", "DracoWriter", "DracoWriterWorker", "id", "name", "module", "version", "worker", "options", "draco", "source", "parse", "arrayBuffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parseSync", "destroy", "_TypecheckDracoLoader"], "sources": ["../../src/index.ts"], "sourcesContent": ["import type {LoaderWithParser} from '@loaders.gl/loader-utils';\nimport type {<PERSON><PERSON>Mesh, DracoLoaderData} from './lib/draco-types';\nimport type {DracoLoaderOptions} from './draco-loader';\nimport {<PERSON><PERSON><PERSON>oa<PERSON> as DracoWorkerLoader} from './draco-loader';\nimport DracoParser from './lib/draco-parser';\nimport {loadDracoDecoderModule} from './lib/draco-module-loader';\nimport {VERSION} from './lib/utils/version';\nimport {isBrowser} from '@loaders.gl/worker-utils';\n\n// Draco data types\n\nexport type {DracoMesh, DracoLoaderData};\n\n// Draco Writer\n\nexport type {DracoWriterOptions} from './draco-writer';\nexport {DracoWriter} from './draco-writer';\n\n/**\n * Browser worker doesn't work because of issue during \"draco_encoder.js\" loading.\n * Refused to execute script from 'https://raw.githubusercontent.com/google/draco/1.4.1/javascript/draco_encoder.js' because its MIME type ('') is not executable.\n */\nexport const DracoWriterWorker = {\n  id: isBrowser ? 'draco-writer' : 'draco-writer-nodejs',\n  name: 'Draco compressed geometry writer',\n  module: 'draco',\n  version: VERSION,\n  worker: true,\n  options: {\n    draco: {},\n    source: null\n  }\n};\n\n// Draco Loader\n\nexport type {DracoLoaderOptions};\nexport {DracoWorkerLoader};\n\n/**\n * Loader for Draco3D compressed geometries\n */\nexport const DracoLoader = {\n  ...DracoWorkerLoader,\n  parse\n};\n\nasync function parse(arrayBuffer: ArrayBuffer, options?: DracoLoaderOptions): Promise<DracoMesh> {\n  const {draco} = await loadDracoDecoderModule(options);\n  const dracoParser = new DracoParser(draco);\n  try {\n    return dracoParser.parseSync(arrayBuffer, options?.draco);\n  } finally {\n    dracoParser.destroy();\n  }\n}\n\n// TYPE TESTS - TODO find a better way than exporting junk\nexport const _TypecheckDracoLoader: LoaderWithParser = DracoLoader;\n"], "mappings": "AAGA,SAAQA,WAAW,IAAIC,iBAAiB,QAAO,gBAAgB;AAC/D,OAAOC,WAAW,MAAM,oBAAoB;AAC5C,SAAQC,sBAAsB,QAAO,2BAA2B;AAChE,SAAQC,OAAO,QAAO,qBAAqB;AAC3C,SAAQC,SAAS,QAAO,0BAA0B;AASlD,SAAQC,WAAW,QAAO,gBAAgB;AAM1C,OAAO,MAAMC,iBAAiB,GAAG;EAC/BC,EAAE,EAAEH,SAAS,GAAG,cAAc,GAAG,qBAAqB;EACtDI,IAAI,EAAE,kCAAkC;EACxCC,MAAM,EAAE,OAAO;EACfC,OAAO,EAAEP,OAAO;EAChBQ,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE;IACPC,KAAK,EAAE,CAAC,CAAC;IACTC,MAAM,EAAE;EACV;AACF,CAAC;AAKD,SAAQd,iBAAiB;AAKzB,OAAO,MAAMD,WAAW,GAAG;EACzB,GAAGC,iBAAiB;EACpBe;AACF,CAAC;AAED,eAAeA,KAAKA,CAACC,WAAwB,EAAEJ,OAA4B,EAAsB;EAC/F,MAAM;IAACC;EAAK,CAAC,GAAG,MAAMX,sBAAsB,CAACU,OAAO,CAAC;EACrD,MAAMK,WAAW,GAAG,IAAIhB,WAAW,CAACY,KAAK,CAAC;EAC1C,IAAI;IACF,OAAOI,WAAW,CAACC,SAAS,CAACF,WAAW,EAAEJ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEC,KAAK,CAAC;EAC3D,CAAC,SAAS;IACRI,WAAW,CAACE,OAAO,CAAC,CAAC;EACvB;AACF;AAGA,OAAO,MAAMC,qBAAuC,GAAGrB,WAAW"}