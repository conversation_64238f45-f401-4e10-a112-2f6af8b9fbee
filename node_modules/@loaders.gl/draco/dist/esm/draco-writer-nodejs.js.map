{"version": 3, "file": "draco-writer-nodejs.js", "names": ["DracoWriter", "DracoWriterNodeJS"], "sources": ["../../src/draco-writer-nodejs.ts"], "sourcesContent": ["// Polyfills increases the bundle size significantly. Use it for NodeJS worker only\nimport '@loaders.gl/polyfills';\n\nexport {DracoWriter as DracoWriterNodeJS} from './draco-writer';\n"], "mappings": "AACA,OAAO,uBAAuB;AAE9B,SAAQA,WAAW,IAAIC,iBAAiB,QAAO,gBAAgB"}