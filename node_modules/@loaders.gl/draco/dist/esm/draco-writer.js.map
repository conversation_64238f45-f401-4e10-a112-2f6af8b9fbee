{"version": 3, "file": "draco-writer.js", "names": ["DRACOBuilder", "loadDracoEncoderModule", "VERSION", "DEFAULT_DRACO_OPTIONS", "pointcloud", "attributeNameEntry", "DracoWriter", "name", "id", "module", "version", "extensions", "encode", "options", "draco", "data", "arguments", "length", "undefined", "dracoBuilder", "encodeSync", "destroy"], "sources": ["../../src/draco-writer.ts"], "sourcesContent": ["import type {Writer, WriterOptions} from '@loaders.gl/loader-utils';\nimport type {DracoMesh} from './lib/draco-types';\nimport type {DracoBuildOptions} from './lib/draco-builder';\nimport DRACOBuilder from './lib/draco-builder';\nimport {loadDracoEncoderModule} from './lib/draco-module-loader';\nimport {VERSION} from './lib/utils/version';\n\nexport type DracoWriterOptions = WriterOptions & {\n  draco?: DracoBuildOptions & {\n    attributeNameEntry: string;\n  };\n};\n\nconst DEFAULT_DRACO_OPTIONS = {\n  pointcloud: false, // Set to true if pointcloud (mode: 0, no indices)\n  attributeNameEntry: 'name'\n  // Draco Compression Parameters\n  // method: 'MESH_EDGEBREAKER_ENCODING',\n  // speed: [5, 5],\n  // quantization: {\n  //   POSITION: 10\n  // }\n};\n\n/**\n * Exporter for Draco3D compressed geometries\n */\nexport const DracoWriter: Writer = {\n  name: 'DR<PERSON><PERSON>',\n  id: 'draco',\n  module: 'draco',\n  version: VERSION,\n  extensions: ['drc'],\n  encode,\n  options: {\n    draco: DEFAULT_DRACO_OPTIONS\n  }\n};\n\nasync function encode(data: DracoMesh, options: DracoWriterOptions = {}): Promise<ArrayBuffer> {\n  // Dynamically load draco\n  const {draco} = await loadDracoEncoderModule(options);\n  const dracoBuilder = new DRACOBuilder(draco);\n\n  try {\n    return dracoBuilder.encodeSync(data, options.draco);\n  } finally {\n    dracoBuilder.destroy();\n  }\n}\n"], "mappings": "AAGA,OAAOA,YAAY,MAAM,qBAAqB;AAC9C,SAAQC,sBAAsB,QAAO,2BAA2B;AAChE,SAAQC,OAAO,QAAO,qBAAqB;AAQ3C,MAAMC,qBAAqB,GAAG;EAC5BC,UAAU,EAAE,KAAK;EACjBC,kBAAkB,EAAE;AAOtB,CAAC;AAKD,OAAO,MAAMC,WAAmB,GAAG;EACjCC,IAAI,EAAE,OAAO;EACbC,EAAE,EAAE,OAAO;EACXC,MAAM,EAAE,OAAO;EACfC,OAAO,EAAER,OAAO;EAChBS,UAAU,EAAE,CAAC,KAAK,CAAC;EACnBC,MAAM;EACNC,OAAO,EAAE;IACPC,KAAK,EAAEX;EACT;AACF,CAAC;AAED,eAAeS,MAAMA,CAACG,IAAe,EAA0D;EAAA,IAAxDF,OAA2B,GAAAG,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAErE,MAAM;IAACF;EAAK,CAAC,GAAG,MAAMb,sBAAsB,CAACY,OAAO,CAAC;EACrD,MAAMM,YAAY,GAAG,IAAInB,YAAY,CAACc,KAAK,CAAC;EAE5C,IAAI;IACF,OAAOK,YAAY,CAACC,UAAU,CAACL,IAAI,EAAEF,OAAO,CAACC,KAAK,CAAC;EACrD,CAAC,SAAS;IACRK,YAAY,CAACE,OAAO,CAAC,CAAC;EACxB;AACF"}