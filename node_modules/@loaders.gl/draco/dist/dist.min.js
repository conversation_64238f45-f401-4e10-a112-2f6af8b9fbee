(()=>{var dt=Object.defineProperty;var Jt=r=>dt(r,"__esModule",{value:!0});var l=(r,t)=>()=>(r&&(t=r(r=0)),t);var Zt=(r,t)=>()=>(t||r((t={exports:{}}).exports,t),t.exports),ut=(r,t)=>{Jt(r);for(var e in t)dt(r,e,{get:t[e],enumerable:!0})};var Kt,G,lt=l(()=>{Kt="latest",G=typeof __VERSION__!="undefined"?__VERSION__:Kt;typeof __VERSION__=="undefined"&&console.error("loaders.gl: The __VERSION__ variable is not injected using babel plugin. Latest unstable workers would be fetched from the CDN.")});function mt(r,t){if(!r)throw new Error(t||"loaders.gl assertion failed.")}var pt=l(()=>{});var f,Xe,Je,yt,Ze,b,w,Ke,ht,tr,j=l(()=>{f={self:typeof self!="undefined"&&self,window:typeof window!="undefined"&&window,global:typeof global!="undefined"&&global,document:typeof document!="undefined"&&document},Xe=f.self||f.window||f.global||{},Je=f.window||f.self||f.global||{},yt=f.global||f.self||f.window||{},Ze=f.document||{},b=typeof process!="object"||String(process)!=="[object process]"||process.browser,w=typeof importScripts=="function",Ke=typeof window!="undefined"&&typeof window.orientation!="undefined",ht=typeof process!="undefined"&&process.version&&/v([0-9]*)/.exec(process.version),tr=ht&&parseFloat(ht[1])||0});var Q={};ut(Q,{readFileAsArrayBuffer:()=>te,readFileAsText:()=>ee,requireFromFile:()=>q,requireFromString:()=>V});var te,ee,q,V,ft=l(()=>{te=null,ee=null,q=null,V=null});async function _(r,t=null,e={}){return t&&(r=bt(r,t,e)),Y[r]=Y[r]||ae(r),await Y[r]}function bt(r,t,e){if(r.startsWith("http"))return r;let o=e.modules||{};return o[r]?o[r]:b?e.CDN?(mt(e.CDN.startsWith("http")),`${e.CDN}/${t}@${oe}/dist/libs/${r}`):w?`../src/libs/${r}`:`modules/${t}/src/libs/${r}`:`modules/${t}/dist/libs/${r}`}async function ae(r){if(r.endsWith("wasm"))return await(await fetch(r)).arrayBuffer();if(!b)try{return Q&&q&&await q(r)}catch{return null}if(w)return importScripts(r);let e=await(await fetch(r)).text();return ne(e,r)}function ne(r,t){if(!b)return V&&V(r,t);if(w)return eval.call(yt,r),null;let e=document.createElement("script");e.id=t;try{e.appendChild(document.createTextNode(r))}catch{e.text=r}return document.body.appendChild(e),null}var re,oe,Y,gt=l(()=>{j();ft();pt();lt();re="latest",oe=typeof G!="undefined"?G:re,Y={}});var k=l(()=>{j();gt()});var I,U=l(()=>{I=typeof __VERSION__!="undefined"?__VERSION__:"latest"});var ie,H,At=l(()=>{k();U();ie={draco:{decoderType:typeof WebAssembly=="object"?"wasm":"js",libraryPath:"libs/",extraAttributes:{},attributeNameEntry:void 0}},H={name:"Draco",id:b?"draco":"draco-nodejs",module:"draco",shapes:["mesh"],version:I,worker:!0,extensions:["drc"],mimeTypes:["application/octet-stream"],binary:!0,tests:["DRACO"],options:ie}});function X(r){let t=1/0,e=1/0,o=1/0,a=-1/0,i=-1/0,s=-1/0,c=r.POSITION?r.POSITION.value:[],u=c&&c.length;for(let m=0;m<u;m+=3){let h=c[m],D=c[m+1],M=c[m+2];t=h<t?h:t,e=D<e?D:e,o=M<o?M:o,a=h>a?h:a,i=D>i?D:i,s=M>s?M:s}return[[t,e,o],[a,i,s]]}var Dt=l(()=>{});function Mt(r,t){if(!r)throw new Error(t||"loader assertion failed.")}var _t=l(()=>{});function se(r){let t={};for(let e of r)t[e.name]&&console.warn("Schema: duplicated field name",e.name,e),t[e.name]=!0}function It(r,t){return new Map([...r||new Map,...t||new Map])}var y,wt=l(()=>{_t();y=class{constructor(t,e){Mt(Array.isArray(t)),se(t),this.fields=t,this.metadata=e||new Map}compareTo(t){if(this.metadata!==t.metadata||this.fields.length!==t.fields.length)return!1;for(let e=0;e<this.fields.length;++e)if(!this.fields[e].compareTo(t.fields[e]))return!1;return!0}select(...t){let e=Object.create(null);for(let a of t)e[a]=!0;let o=this.fields.filter(a=>e[a.name]);return new y(o,this.metadata)}selectAt(...t){let e=t.map(o=>this.fields[o]).filter(Boolean);return new y(e,this.metadata)}assign(t){let e,o=this.metadata;if(t instanceof y){let s=t;e=s.fields,o=It(It(new Map,this.metadata),s.metadata)}else e=t;let a=Object.create(null);for(let s of this.fields)a[s.name]=s;for(let s of e)a[s.name]=s;let i=Object.values(a);return new y(i,o)}}});var g,Ot=l(()=>{g=class{constructor(t,e,o=!1,a=new Map){this.name=t,this.type=e,this.nullable=o,this.metadata=a}get typeId(){return this.type&&this.type.typeId}clone(){return new g(this.name,this.type,this.nullable,this.metadata)}compareTo(t){return this.name===t.name&&this.type===t.type&&this.nullable===t.nullable&&this.metadata===t.metadata}toString(){return`${this.type}${this.nullable?", nullable":""}${this.metadata?`, metadata: ${this.metadata}`:""}`}}});var d,Tt=l(()=>{(function(n){n[n.NONE=0]="NONE",n[n.Null=1]="Null",n[n.Int=2]="Int",n[n.Float=3]="Float",n[n.Binary=4]="Binary",n[n.Utf8=5]="Utf8",n[n.Bool=6]="Bool",n[n.Decimal=7]="Decimal",n[n.Date=8]="Date",n[n.Time=9]="Time",n[n.Timestamp=10]="Timestamp",n[n.Interval=11]="Interval",n[n.List=12]="List",n[n.Struct=13]="Struct",n[n.Union=14]="Union",n[n.FixedSizeBinary=15]="FixedSizeBinary",n[n.FixedSizeList=16]="FixedSizeList",n[n.Map=17]="Map",n[n.Dictionary=-1]="Dictionary",n[n.Int8=-2]="Int8",n[n.Int16=-3]="Int16",n[n.Int32=-4]="Int32",n[n.Int64=-5]="Int64",n[n.Uint8=-6]="Uint8",n[n.Uint16=-7]="Uint16",n[n.Uint32=-8]="Uint32",n[n.Uint64=-9]="Uint64",n[n.Float16=-10]="Float16",n[n.Float32=-11]="Float32",n[n.Float64=-12]="Float64",n[n.DateDay=-13]="DateDay",n[n.DateMillisecond=-14]="DateMillisecond",n[n.TimestampSecond=-15]="TimestampSecond",n[n.TimestampMillisecond=-16]="TimestampMillisecond",n[n.TimestampMicrosecond=-17]="TimestampMicrosecond",n[n.TimestampNanosecond=-18]="TimestampNanosecond",n[n.TimeSecond=-19]="TimeSecond",n[n.TimeMillisecond=-20]="TimeMillisecond",n[n.TimeMicrosecond=-21]="TimeMicrosecond",n[n.TimeNanosecond=-22]="TimeNanosecond",n[n.DenseUnion=-23]="DenseUnion",n[n.SparseUnion=-24]="SparseUnion",n[n.IntervalDayTime=-25]="IntervalDayTime",n[n.IntervalYearMonth=-26]="IntervalYearMonth"})(d||(d={}))});var p,J,Z,A,O,T,x,S,E,F,xt,N,L,B,K,tt,ce,et,St,rt,ot,de,at,P,nt,Et=l(()=>{Tt();p=class{static isNull(t){return t&&t.typeId===d.Null}static isInt(t){return t&&t.typeId===d.Int}static isFloat(t){return t&&t.typeId===d.Float}static isBinary(t){return t&&t.typeId===d.Binary}static isUtf8(t){return t&&t.typeId===d.Utf8}static isBool(t){return t&&t.typeId===d.Bool}static isDecimal(t){return t&&t.typeId===d.Decimal}static isDate(t){return t&&t.typeId===d.Date}static isTime(t){return t&&t.typeId===d.Time}static isTimestamp(t){return t&&t.typeId===d.Timestamp}static isInterval(t){return t&&t.typeId===d.Interval}static isList(t){return t&&t.typeId===d.List}static isStruct(t){return t&&t.typeId===d.Struct}static isUnion(t){return t&&t.typeId===d.Union}static isFixedSizeBinary(t){return t&&t.typeId===d.FixedSizeBinary}static isFixedSizeList(t){return t&&t.typeId===d.FixedSizeList}static isMap(t){return t&&t.typeId===d.Map}static isDictionary(t){return t&&t.typeId===d.Dictionary}get typeId(){return d.NONE}compareTo(t){return this===t}},J=class extends p{get typeId(){return d.Null}get[Symbol.toStringTag](){return"Null"}toString(){return"Null"}},Z=class extends p{get typeId(){return d.Bool}get[Symbol.toStringTag](){return"Bool"}toString(){return"Bool"}},A=class extends p{constructor(t,e){super();this.isSigned=t,this.bitWidth=e}get typeId(){return d.Int}get[Symbol.toStringTag](){return"Int"}toString(){return`${this.isSigned?"I":"Ui"}nt${this.bitWidth}`}},O=class extends A{constructor(){super(!0,8)}},T=class extends A{constructor(){super(!0,16)}},x=class extends A{constructor(){super(!0,32)}},S=class extends A{constructor(){super(!1,8)}},E=class extends A{constructor(){super(!1,16)}},F=class extends A{constructor(){super(!1,32)}},xt={HALF:16,SINGLE:32,DOUBLE:64},N=class extends p{constructor(t){super();this.precision=t}get typeId(){return d.Float}get[Symbol.toStringTag](){return"Float"}toString(){return`Float${this.precision}`}},L=class extends N{constructor(){super(xt.SINGLE)}},B=class extends N{constructor(){super(xt.DOUBLE)}},K=class extends p{constructor(){super()}get typeId(){return d.Binary}toString(){return"Binary"}get[Symbol.toStringTag](){return"Binary"}},tt=class extends p{get typeId(){return d.Utf8}get[Symbol.toStringTag](){return"Utf8"}toString(){return"Utf8"}},ce={DAY:0,MILLISECOND:1},et=class extends p{constructor(t){super();this.unit=t}get typeId(){return d.Date}get[Symbol.toStringTag](){return"Date"}toString(){return`Date${(this.unit+1)*32}<${ce[this.unit]}>`}},St={SECOND:1,MILLISECOND:1e3,MICROSECOND:1e6,NANOSECOND:1e9},rt=class extends p{constructor(t,e){super();this.unit=t,this.bitWidth=e}get typeId(){return d.Time}toString(){return`Time${this.bitWidth}<${St[this.unit]}>`}get[Symbol.toStringTag](){return"Time"}},ot=class extends p{constructor(t,e=null){super();this.unit=t,this.timezone=e}get typeId(){return d.Timestamp}get[Symbol.toStringTag](){return"Timestamp"}toString(){return`Timestamp<${St[this.unit]}${this.timezone?`, ${this.timezone}`:""}>`}},de={DAY_TIME:0,YEAR_MONTH:1},at=class extends p{constructor(t){super();this.unit=t}get typeId(){return d.Interval}get[Symbol.toStringTag](){return"Interval"}toString(){return`Interval<${de[this.unit]}>`}},P=class extends p{constructor(t,e){super();this.listSize=t,this.children=[e]}get typeId(){return d.FixedSizeList}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get[Symbol.toStringTag](){return"FixedSizeList"}toString(){return`FixedSizeList[${this.listSize}]<${this.valueType}>`}},nt=class extends p{constructor(t){super();this.children=t}get typeId(){return d.Struct}toString(){return`Struct<{${this.children.map(t=>`${t.name}:${t.type}`).join(", ")}}>`}get[Symbol.toStringTag](){return"Struct"}}});var v=l(()=>{wt();Ot();Et()});function Ft(r){switch(r.constructor){case Int8Array:return new O;case Uint8Array:return new S;case Int16Array:return new T;case Uint16Array:return new E;case Int32Array:return new x;case Uint32Array:return new F;case Float32Array:return new L;case Float64Array:return new B;default:throw new Error("array type not supported")}}var Nt=l(()=>{v()});function it(r,t,e){let o=Ft(t.value),a=e||Lt(t);return new g(r,new P(t.size,new g("value",o)),!1,a)}function Lt(r){let t=new Map;return"byteOffset"in r&&t.set("byteOffset",r.byteOffset.toString(10)),"byteStride"in r&&t.set("byteStride",r.byteStride.toString(10)),"normalized"in r&&t.set("normalized",r.normalized.toString()),t}var Bt=l(()=>{v();Nt()});var z=l(()=>{Dt();Bt();v()});function Pt(r,t,e){let o=Ct(t.metadata),a=[],i=Ie(t.attributes);for(let s in r){let c=r[s],u=Rt(s,c,i[s]);a.push(u)}if(e){let s=Rt("indices",e);a.push(s)}return new y(a,o)}function Ie(r){let t={};for(let e in r){let o=r[e];t[o.name||"undefined"]=o}return t}function Rt(r,t,e){let o=e?Ct(e.metadata):void 0;return it(r,t,o)}function Ct(r){let t=new Map;for(let e in r)t.set(`${e}.string`,JSON.stringify(r[e]));return t}var kt=l(()=>{z();z()});function Te(r,t){switch(t){case Float32Array:return r.DT_FLOAT32;case Int8Array:return r.DT_INT8;case Int16Array:return r.DT_INT16;case Int32Array:return r.DT_INT32;case Uint8Array:return r.DT_UINT8;case Uint16Array:return r.DT_UINT16;case Uint32Array:return r.DT_UINT32;default:return r.DT_INVALID}}function xe(r){let t=r.size(),e=new Int32Array(t);for(let o=0;o<t;o++)e[o]=r.GetValue(o);return e}function Se(r){let t=r.size(),e=new Int32Array(t);for(let o=0;o<t;o++)e[o]=r.GetValue(o);return e}var Ut,we,Oe,W,vt=l(()=>{z();kt();Ut={POSITION:"POSITION",NORMAL:"NORMAL",COLOR:"COLOR_0",TEX_COORD:"TEXCOORD_0"},we={1:Int8Array,2:Uint8Array,3:Int16Array,4:Uint16Array,5:Int32Array,6:Uint32Array,9:Float32Array},Oe=4,W=class{constructor(t){this.draco=t,this.decoder=new this.draco.Decoder,this.metadataQuerier=new this.draco.MetadataQuerier}destroy(){this.draco.destroy(this.decoder),this.draco.destroy(this.metadataQuerier)}parseSync(t,e={}){let o=new this.draco.DecoderBuffer;o.Init(new Int8Array(t),t.byteLength),this._disableAttributeTransforms(e);let a=this.decoder.GetEncodedGeometryType(o),i=a===this.draco.TRIANGULAR_MESH?new this.draco.Mesh:new this.draco.PointCloud;try{let s;switch(a){case this.draco.TRIANGULAR_MESH:s=this.decoder.DecodeBufferToMesh(o,i);break;case this.draco.POINT_CLOUD:s=this.decoder.DecodeBufferToPointCloud(o,i);break;default:throw new Error("DRACO: Unknown geometry type.")}if(!s.ok()||!i.ptr){let M=`DRACO decompression failed: ${s.error_msg()}`;throw new Error(M)}let c=this._getDracoLoaderData(i,a,e),u=this._getMeshData(i,c,e),m=X(u.attributes),h=Pt(u.attributes,c,u.indices);return{loader:"draco",loaderData:c,header:{vertexCount:i.num_points(),boundingBox:m},...u,schema:h}}finally{this.draco.destroy(o),i&&this.draco.destroy(i)}}_getDracoLoaderData(t,e,o){let a=this._getTopLevelMetadata(t),i=this._getDracoAttributes(t,o);return{geometry_type:e,num_attributes:t.num_attributes(),num_points:t.num_points(),num_faces:t instanceof this.draco.Mesh?t.num_faces():0,metadata:a,attributes:i}}_getDracoAttributes(t,e){let o={};for(let a=0;a<t.num_attributes();a++){let i=this.decoder.GetAttribute(t,a),s=this._getAttributeMetadata(t,a);o[i.unique_id()]={unique_id:i.unique_id(),attribute_type:i.attribute_type(),data_type:i.data_type(),num_components:i.num_components(),byte_offset:i.byte_offset(),byte_stride:i.byte_stride(),normalized:i.normalized(),attribute_index:a,metadata:s};let c=this._getQuantizationTransform(i,e);c&&(o[i.unique_id()].quantization_transform=c);let u=this._getOctahedronTransform(i,e);u&&(o[i.unique_id()].octahedron_transform=u)}return o}_getMeshData(t,e,o){let a=this._getMeshAttributes(e,t,o);if(!a.POSITION)throw new Error("DRACO: No position attribute found.");if(t instanceof this.draco.Mesh)switch(o.topology){case"triangle-strip":return{topology:"triangle-strip",mode:4,attributes:a,indices:{value:this._getTriangleStripIndices(t),size:1}};case"triangle-list":default:return{topology:"triangle-list",mode:5,attributes:a,indices:{value:this._getTriangleListIndices(t),size:1}}}return{topology:"point-list",mode:0,attributes:a}}_getMeshAttributes(t,e,o){let a={};for(let i of Object.values(t.attributes)){let s=this._deduceAttributeName(i,o);i.name=s;let{value:c,size:u}=this._getAttributeValues(e,i);a[s]={value:c,size:u,byteOffset:i.byte_offset,byteStride:i.byte_stride,normalized:i.normalized}}return a}_getTriangleListIndices(t){let o=t.num_faces()*3,a=o*Oe,i=this.draco._malloc(a);try{return this.decoder.GetTrianglesUInt32Array(t,a,i),new Uint32Array(this.draco.HEAPF32.buffer,i,o).slice()}finally{this.draco._free(i)}}_getTriangleStripIndices(t){let e=new this.draco.DracoInt32Array;try{return this.decoder.GetTriangleStripsFromMesh(t,e),Se(e)}finally{this.draco.destroy(e)}}_getAttributeValues(t,e){let o=we[e.data_type],a=e.num_components,s=t.num_points()*a,c=s*o.BYTES_PER_ELEMENT,u=Te(this.draco,o),m,h=this.draco._malloc(c);try{let D=this.decoder.GetAttribute(t,e.attribute_index);this.decoder.GetAttributeDataArrayForAllPoints(t,D,u,c,h),m=new o(this.draco.HEAPF32.buffer,h,s).slice()}finally{this.draco._free(h)}return{value:m,size:a}}_deduceAttributeName(t,e){let o=t.unique_id;for(let[s,c]of Object.entries(e.extraAttributes||{}))if(c===o)return s;let a=t.attribute_type;for(let s in Ut)if(this.draco[s]===a)return Ut[s];let i=e.attributeNameEntry||"name";return t.metadata[i]?t.metadata[i].string:`CUSTOM_ATTRIBUTE_${o}`}_getTopLevelMetadata(t){let e=this.decoder.GetMetadata(t);return this._getDracoMetadata(e)}_getAttributeMetadata(t,e){let o=this.decoder.GetAttributeMetadata(t,e);return this._getDracoMetadata(o)}_getDracoMetadata(t){if(!t||!t.ptr)return{};let e={},o=this.metadataQuerier.NumEntries(t);for(let a=0;a<o;a++){let i=this.metadataQuerier.GetEntryName(t,a);e[i]=this._getDracoMetadataField(t,i)}return e}_getDracoMetadataField(t,e){let o=new this.draco.DracoInt32Array;try{this.metadataQuerier.GetIntEntryArray(t,e,o);let a=xe(o);return{int:this.metadataQuerier.GetIntEntry(t,e),string:this.metadataQuerier.GetStringEntry(t,e),double:this.metadataQuerier.GetDoubleEntry(t,e),intArray:a}}finally{this.draco.destroy(o)}}_disableAttributeTransforms(t){let{quantizedAttributes:e=[],octahedronAttributes:o=[]}=t,a=[...e,...o];for(let i of a)this.decoder.SkipAttributeTransform(this.draco[i])}_getQuantizationTransform(t,e){let{quantizedAttributes:o=[]}=e,a=t.attribute_type();if(o.map(s=>this.decoder[s]).includes(a)){let s=new this.draco.AttributeQuantizationTransform;try{if(s.InitFromAttribute(t))return{quantization_bits:s.quantization_bits(),range:s.range(),min_values:new Float32Array([1,2,3]).map(c=>s.min_value(c))}}finally{this.draco.destroy(s)}}return null}_getOctahedronTransform(t,e){let{octahedronAttributes:o=[]}=e,a=t.attribute_type();if(o.map(s=>this.decoder[s]).includes(a)){let s=new this.draco.AttributeQuantizationTransform;try{if(s.InitFromAttribute(t))return{quantization_bits:s.quantization_bits()}}finally{this.draco.destroy(s)}}return null}}});async function zt(r){let t=r.modules||{};return t.draco3d?R=R||t.draco3d.createDecoderModule({}).then(e=>({draco:e})):R=R||Re(r),await R}async function Wt(r){let t=r.modules||{};return t.draco3d?C=C||t.draco3d.createEncoderModule({}).then(e=>({draco:e})):C=C||ke(r),await C}async function Re(r){let t,e;switch(r.draco&&r.draco.decoderType){case"js":t=await _(Ne,"draco",r);break;case"wasm":default:[t,e]=await Promise.all([await _(Le,"draco",r),await _(Be,"draco",r)])}return t=t||globalThis.DracoDecoderModule,await Ce(t,e)}function Ce(r,t){let e={};return t&&(e.wasmBinary=t),new Promise(o=>{r({...e,onModuleLoaded:a=>o({draco:a})})})}async function ke(r){let t=await _(Pe,"draco",r);return t=t||globalThis.DracoEncoderModule,new Promise(e=>{t({onModuleLoaded:o=>e({draco:o})})})}var Ee,Fe,st,Ne,Le,Be,Pe,R,C,ct=l(()=>{k();Ee="1.5.5",Fe="1.4.1",st=`https://www.gstatic.com/draco/versioned/decoders/${Ee}`,Ne=`${st}/draco_decoder.js`,Le=`${st}/draco_wasm_wrapper.js`,Be=`${st}/draco_decoder.wasm`,Pe=`https://raw.githubusercontent.com/google/draco/${Fe}/javascript/draco_encoder.js`});function Gt(r){let t=r.size(),e=new ArrayBuffer(t),o=new Int8Array(e);for(let a=0;a<t;++a)o[a]=r.GetValue(a);return e}function ve(r){return r.entries&&!r.hasOwnProperty("entries")?r.entries():Object.entries(r)}var $t,Ue,$,jt=l(()=>{$t={POSITION:"POSITION",NORMAL:"NORMAL",COLOR_0:"COLOR",TEXCOORD_0:"TEX_COORD"},Ue=()=>{},$=class{constructor(t){this.draco=t,this.dracoEncoder=new this.draco.Encoder,this.dracoMeshBuilder=new this.draco.MeshBuilder,this.dracoMetadataBuilder=new this.draco.MetadataBuilder}destroy(){this.destroyEncodedObject(this.dracoMeshBuilder),this.destroyEncodedObject(this.dracoEncoder),this.destroyEncodedObject(this.dracoMetadataBuilder),this.dracoMeshBuilder=null,this.dracoEncoder=null,this.draco=null}destroyEncodedObject(t){t&&this.draco.destroy(t)}encodeSync(t,e={}){return this.log=Ue,this._setOptions(e),e.pointcloud?this._encodePointCloud(t,e):this._encodeMesh(t,e)}_getAttributesFromMesh(t){let e={...t,...t.attributes};return t.indices&&(e.indices=t.indices),e}_encodePointCloud(t,e){let o=new this.draco.PointCloud;e.metadata&&this._addGeometryMetadata(o,e.metadata);let a=this._getAttributesFromMesh(t);this._createDracoPointCloud(o,a,e);let i=new this.draco.DracoInt8Array;try{let s=this.dracoEncoder.EncodePointCloudToDracoBuffer(o,!1,i);if(!(s>0))throw new Error("Draco encoding failed.");return this.log(`DRACO encoded ${o.num_points()} points
        with ${o.num_attributes()} attributes into ${s} bytes`),Gt(i)}finally{this.destroyEncodedObject(i),this.destroyEncodedObject(o)}}_encodeMesh(t,e){let o=new this.draco.Mesh;e.metadata&&this._addGeometryMetadata(o,e.metadata);let a=this._getAttributesFromMesh(t);this._createDracoMesh(o,a,e);let i=new this.draco.DracoInt8Array;try{let s=this.dracoEncoder.EncodeMeshToDracoBuffer(o,i);if(s<=0)throw new Error("Draco encoding failed.");return this.log(`DRACO encoded ${o.num_points()} points
        with ${o.num_attributes()} attributes into ${s} bytes`),Gt(i)}finally{this.destroyEncodedObject(i),this.destroyEncodedObject(o)}}_setOptions(t){if("speed"in t&&this.dracoEncoder.SetSpeedOptions(...t.speed),"method"in t){let e=this.draco[t.method||"MESH_SEQUENTIAL_ENCODING"];this.dracoEncoder.SetEncodingMethod(e)}if("quantization"in t)for(let e in t.quantization){let o=t.quantization[e],a=this.draco[e];this.dracoEncoder.SetAttributeQuantization(a,o)}}_createDracoMesh(t,e,o){let a=o.attributesMetadata||{};try{let i=this._getPositionAttribute(e);if(!i)throw new Error("positions");let s=i.length/3;for(let c in e){let u=e[c];c=$t[c]||c;let m=this._addAttributeToMesh(t,c,u,s);m!==-1&&this._addAttributeMetadata(t,m,{name:c,...a[c]||{}})}}catch(i){throw this.destroyEncodedObject(t),i}return t}_createDracoPointCloud(t,e,o){let a=o.attributesMetadata||{};try{let i=this._getPositionAttribute(e);if(!i)throw new Error("positions");let s=i.length/3;for(let c in e){let u=e[c];c=$t[c]||c;let m=this._addAttributeToMesh(t,c,u,s);m!==-1&&this._addAttributeMetadata(t,m,{name:c,...a[c]||{}})}}catch(i){throw this.destroyEncodedObject(t),i}return t}_addAttributeToMesh(t,e,o,a){if(!ArrayBuffer.isView(o))return-1;let i=this._getDracoAttributeType(e),s=o.length/a;if(i==="indices"){let m=o.length/3;return this.log(`Adding attribute ${e}, size ${m}`),this.dracoMeshBuilder.AddFacesToMesh(t,m,o),-1}this.log(`Adding attribute ${e}, size ${s}`);let c=this.dracoMeshBuilder,{buffer:u}=o;switch(o.constructor){case Int8Array:return c.AddInt8Attribute(t,i,a,s,new Int8Array(u));case Int16Array:return c.AddInt16Attribute(t,i,a,s,new Int16Array(u));case Int32Array:return c.AddInt32Attribute(t,i,a,s,new Int32Array(u));case Uint8Array:case Uint8ClampedArray:return c.AddUInt8Attribute(t,i,a,s,new Uint8Array(u));case Uint16Array:return c.AddUInt16Attribute(t,i,a,s,new Uint16Array(u));case Uint32Array:return c.AddUInt32Attribute(t,i,a,s,new Uint32Array(u));case Float32Array:default:return c.AddFloatAttribute(t,i,a,s,new Float32Array(u))}}_getDracoAttributeType(t){switch(t.toLowerCase()){case"indices":return"indices";case"position":case"positions":case"vertices":return this.draco.POSITION;case"normal":case"normals":return this.draco.NORMAL;case"color":case"colors":return this.draco.COLOR;case"texcoord":case"texcoords":return this.draco.TEX_COORD;default:return this.draco.GENERIC}}_getPositionAttribute(t){for(let e in t){let o=t[e];if(this._getDracoAttributeType(e)===this.draco.POSITION)return o}return null}_addGeometryMetadata(t,e){let o=new this.draco.Metadata;this._populateDracoMetadata(o,e),this.dracoMeshBuilder.AddMetadata(t,o)}_addAttributeMetadata(t,e,o){let a=new this.draco.Metadata;this._populateDracoMetadata(a,o),this.dracoMeshBuilder.SetMetadataForAttribute(t,e,a)}_populateDracoMetadata(t,e){for(let[o,a]of ve(e))switch(typeof a){case"number":Math.trunc(a)===a?this.dracoMetadataBuilder.AddIntEntry(t,o,a):this.dracoMetadataBuilder.AddDoubleEntry(t,o,a);break;case"object":a instanceof Int32Array&&this.dracoMetadataBuilder.AddIntEntryArray(t,o,a,a.length);break;case"string":default:this.dracoMetadataBuilder.AddStringEntry(t,o,a)}}}});async function We(r,t={}){let{draco:e}=await Wt(t),o=new $(e);try{return o.encodeSync(r,t.draco)}finally{o.destroy()}}var ze,qt,Vt=l(()=>{jt();ct();U();ze={pointcloud:!1,attributeNameEntry:"name"},qt={name:"DRACO",id:"draco",module:"draco",version:I,extensions:["drc"],encode:We,options:{draco:ze}}});var Yt={};ut(Yt,{DracoLoader:()=>Qt,DracoWorkerLoader:()=>H,DracoWriter:()=>qt,DracoWriterWorker:()=>$e,_TypecheckDracoLoader:()=>je});async function Ge(r,t){let{draco:e}=await zt(t),o=new W(e);try{return o.parseSync(r,t?.draco)}finally{o.destroy()}}var $e,Qt,je,Ht=l(()=>{At();vt();ct();U();k();Vt();$e={id:b?"draco-writer":"draco-writer-nodejs",name:"Draco compressed geometry writer",module:"draco",version:I,worker:!0,options:{draco:{},source:null}},Qt={...H,parse:Ge};je=Qt});var Ve=Zt((ao,Xt)=>{var qe=(Ht(),Yt);globalThis.loaders=globalThis.loaders||{};Xt.exports=Object.assign(globalThis.loaders,qe)});Ve();})();
//# sourceMappingURL=dist.min.js.map
