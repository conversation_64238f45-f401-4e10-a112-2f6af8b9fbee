{"version": 3, "file": "draco-types.d.ts", "sourceRoot": "", "sources": ["../../src/lib/draco-types.ts"], "names": [], "mappings": "AAEA,OAAO,EAAC,IAAI,EAAC,MAAM,oBAAoB,CAAC;AAIxC,MAAM,MAAM,kBAAkB,GAAG;IAC/B,GAAG,EAAE,MAAM,CAAC;IACZ,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,UAAU,CAAC;CACtB,CAAC;AAEF,2DAA2D;AAC3D,MAAM,MAAM,0BAA0B,GAAG;IACvC,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,UAAU,CAAC,EAAE,YAAY,CAAC;CAC3B,CAAC;AAEF,2DAA2D;AAC3D,MAAM,MAAM,wBAAwB,GAAG;IACrC,iBAAiB,CAAC,EAAE,MAAM,CAAC;CAC5B,CAAC;AAEF,6BAA6B;AAC7B,MAAM,MAAM,cAAc,GAAG;IAC3B,SAAS,EAAE,MAAM,CAAC;IAElB,cAAc,EAAE,MAAM,CAAC;IACvB,cAAc,EAAE,MAAM,CAAC;IACvB,SAAS,EAAE,MAAM,CAAC;IAElB,WAAW,EAAE,MAAM,CAAC;IACpB,WAAW,EAAE,MAAM,CAAC;IACpB,UAAU,EAAE,OAAO,CAAC;IACpB,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd,sBAAsB,CAAC,EAAE,0BAA0B,CAAC;IACpD,oBAAoB,CAAC,EAAE,wBAAwB,CAAC;IAEhD,QAAQ,EAAE;QAAC,CAAC,GAAG,EAAE,MAAM,GAAG,kBAAkB,CAAA;KAAC,CAAC;IAC9C,eAAe,EAAE,MAAM,CAAC;CACzB,CAAC;AAEF;;;;GAIG;AACH,MAAM,MAAM,eAAe,GAAG;IAC5B,aAAa,EAAE,MAAM,CAAC;IACtB,cAAc,EAAE,MAAM,CAAC;IACvB,UAAU,EAAE,MAAM,CAAC;IACnB,SAAS,EAAE,MAAM,CAAC;IAClB,QAAQ,EAAE;QAAC,CAAC,KAAK,EAAE,MAAM,GAAG,kBAAkB,CAAA;KAAC,CAAC;IAChD,UAAU,EAAE;QAAC,CAAC,SAAS,EAAE,MAAM,GAAG,cAAc,CAAA;KAAC,CAAC;CAGnD,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,SAAS,GAAG,IAAI,GAAG;IAC7B,MAAM,EAAE,OAAO,CAAC;IAChB,UAAU,EAAE,eAAe,CAAC;CAC7B,CAAC"}