{"name": "@loaders.gl/draco", "version": "3.4.15", "description": "Framework-independent loader and writer for Draco compressed meshes and point clouds", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/visgl/loaders.gl"}, "keywords": ["webgl", "loader", "3d", "mesh", "point cloud", "draco3d", "draco"], "types": "dist/index.d.ts", "main": "dist/es5/index.js", "module": "dist/esm/index.js", "sideEffects": false, "browser": {"fs": false}, "files": ["src", "dist", "README.md"], "scripts": {"pre-build": "npm run build-bundle && npm run build-worker", "build-bundle": "esbuild src/bundle.ts --outfile=dist/dist.min.js --bundle --minify --sourcemap", "build-loader-worker": "esbuild src/workers/draco-worker.ts --outfile=dist/draco-worker.js --bundle --minify --sourcemap --define:__VERSION__=\\\"$npm_package_version\\\"", "build-loader-worker-nodejs": "esbuild src/workers/draco-nodejs-worker.ts --outfile=dist/draco-nodejs-worker.js --platform=node --target=esnext,node12 --minify --bundle --sourcemap --define:__VERSION__=\\\"$npm_package_version\\\"", "build-writer-worker": "esbuild src/workers/draco-writer-worker.ts --outfile=dist/draco-writer-worker.js --minify --bundle --sourcemap --define:__VERSION__=\\\"$npm_package_version\\\"", "build-writer-worker-nodejs": "esbuild src/workers/draco-writer-nodejs-worker.ts --outfile=dist/draco-writer-nodejs-worker.js --platform=node --target=esnext,node12 --minify --bundle --sourcemap --define:__VERSION__=\\\"$npm_package_version\\\"", "build-worker": "yarn build-loader-worker && yarn build-loader-worker-nodejs && yarn build-writer-worker && yarn build-writer-worker-nodejs"}, "dependencies": {"@babel/runtime": "^7.3.1", "@loaders.gl/loader-utils": "3.4.15", "@loaders.gl/schema": "3.4.15", "@loaders.gl/worker-utils": "3.4.15", "draco3d": "1.5.5"}, "devDependencies": {"@loaders.gl/polyfills": "3.4.15"}, "gitHead": "19e941d5805568e449ef9092490d6568a4853298"}