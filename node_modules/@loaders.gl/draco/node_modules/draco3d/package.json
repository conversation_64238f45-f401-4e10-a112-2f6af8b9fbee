{"name": "draco3d", "version": "1.5.5", "description": "Draco is a library for compressing and decompressing 3D geometric meshes and point clouds. It is intended to improve the storage and transmission of 3D graphics.", "main": "draco3d.js", "scripts": {"test": "nodejs draco_nodejs_example.js"}, "keywords": ["geometry", "compression", "mesh", "point cloud"], "author": "Google Draco Team", "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/google/draco.git"}}