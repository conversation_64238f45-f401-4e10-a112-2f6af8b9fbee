{"name": "d3-tricontour", "version": "1.0.2", "description": "Create contours from non-gridded data with meandering triangles.", "homepage": "https://github.com/Fil/d3-tricontour", "repository": {"type": "git", "url": "https://github.com/Fil/d3-tricontour.git"}, "keywords": ["d3", "d3-module", "contours", "triangulation"], "license": "ISC", "author": {"name": "<PERSON>", "url": "https://github.com/Fil"}, "type": "module", "files": ["dist/**/*.js", "src/**/*.js"], "module": "src/index.js", "main": "src/index.js", "jsdelivr": "dist/d3-tricontour.min.js", "unpkg": "dist/d3-tricontour.min.js", "exports": {"umd": "./dist/d3-tricontour.min.js", "default": "./src/index.js"}, "sideEffects": false, "dependencies": {"d3-delaunay": "6", "d3-scale": "4"}, "devDependencies": {"eslint": "7", "mocha": "8", "package-preamble": "0.1", "rollup": "2", "rollup-plugin-terser": "7"}, "scripts": {"test": "mocha 'test/**/*-test.js' && eslint src test", "prepublishOnly": "rm -rf dist && yarn test && rollup -c"}, "engines": {"node": ">=12"}}