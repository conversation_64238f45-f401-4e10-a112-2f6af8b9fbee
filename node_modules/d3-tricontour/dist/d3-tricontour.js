// https://github.com/Fil/d3-tricontour v1.0.2 Copyright 2021 <PERSON>
(function (global, factory) {
typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, require('d3-delaunay'), require('d3-scale')) :
typeof define === 'function' && define.amd ? define(['exports', 'd3-delaunay', 'd3-scale'], factory) :
(global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.d3 = global.d3 || {}, global.d3, global.d3));
}(this, (function (exports, d3Delaunay, d3Scale) { 'use strict';

// https://github.com/d3/d3-array/blob/master/src/extent.js
function extent(values) {
  let min, max;
  for (const value of values) {
    if (value != null) {
      if (min === undefined) {
        if (value >= value) min = max = value;
      } else {
        if (min > value) min = value;
        if (max < value) max = value;
      }
    }
  }
  return [min, max];
}

// https://github.com/d3/d3-array/blob/master/src/merge.js
function* flatten(arrays) {
  for (const array of arrays) {
    yield* array;
  }
}

function merge(arrays) {
  return Array.from(flatten(arrays));
}

// https://github.com/d3/d3-contour/blob/master/src/contains.js
function contains(ring, hole) {
  const n = hole.length;
  let i = -1;
  while (++i < n) {
    const c = ringContains(ring, hole[i]);
    if (c) return c;
  }
  return 0;
}

function ringContains(ring, point) {
  let x = point[0], y = point[1], contains = -1;
  for (let i = 0, n = ring.length, j = n - 1; i < n; j = i++) {
    const pi = ring[i], xi = pi[0], yi = pi[1], pj = ring[j], xj = pj[0], yj = pj[1];
    if (segmentContains(pi, pj, point)) return 0;
    if (((yi > y) !== (yj > y)) && ((x < (xj - xi) * (y - yi) / (yj - yi) + xi))) contains = -contains;
  }
  return contains;
}

function segmentContains(a, b, c) {
  let i; return collinear(a, b, c) && within(a[i = +(a[0] === b[0])], c[i], b[i]);
}

function collinear(a, b, c) {
  return (b[0] - a[0]) * (c[1] - a[1]) === (c[0] - a[0]) * (b[1] - a[1]);
}

function within(p, q, r) {
  return p <= q && q <= r || r <= q && q <= p;
}

// https://github.com/d3/d3-contour/blob/master/src/area.js
function area(ring) {
  let i = 0, n = ring.length, area = ring[n - 1][1] * ring[0][0] - ring[n - 1][0] * ring[0][1];
  while (++i < n) area += ring[i - 1][1] * ring[i][0] - ring[i - 1][0] * ring[i][1];
  return area;
}

// sorts the polygons so that the holes are grouped with their parent polygon

function planarRingsort(rings) {
  const polygons = [];
  const holes = [];

  for (const ring of rings) {
    if (area(ring) > 0) polygons.push([ring]);
    else holes.push(ring);
  }

  holes.forEach(function(hole) {
    for (let i = 0, n = polygons.length, polygon; i < n; ++i) {
      if (contains((polygon = polygons[i])[0], hole) !== -1) {
        polygon.push(hole);
        return;
      }
    }
  });

  return polygons;
}

function tricontour() {
  // accessors
  let x = d => d[0],
    y = d => d[1],
    value = d => (isFinite(+d[2]) ? +d[2] : 0),
    triangulate = d3Delaunay.Delaunay.from,
    pointInterpolate = (i, j, a) => {
      const { points } = triangulation;
      const A = [points[2 * i], points[2 * i + 1]],
        B = [points[2 * j], points[2 * j + 1]];
      return [a * B[0] + (1 - a) * A[0], a * B[1] + (1 - a) * A[1]];
    },
    ringsort = planarRingsort;

  let thresholds, values, triangulation;

  function init(points) {
    triangulation = triangulate(points, x, y);
    values = Array.from(points, value);
    if (typeof thresholds !== "object") {
      thresholds = d3Scale.scaleLinear()
        .domain(extent(values))
        .nice()
        .ticks(thresholds);
    }
  }

  function* tricontours(points) {
    init(points);

    for (const threshold of thresholds) {
      const polygon = tricontour(triangulation, values, threshold);
      yield {
        type: "MultiPolygon",
        coordinates: polygon,
        value: threshold
      };
    }
  }

  function contour(points, threshold) {
    init(points);

    return {
      type: "MultiPolygon",
      coordinates: tricontour(triangulation, values, threshold),
      value: threshold
    };
  }

  function* isobands(points) {
    init(points);

    let p0, p1, th0;
    for (const th of thresholds) {
      if (p1) p0 = p1;
      p1 = merge(tricontour(triangulation, values, th));
      if (p0) {
        yield {
          type: "MultiPolygon",
          coordinates: ringsort(
            p0.concat(p1.map(ring => ring.slice().reverse()))
          ),
          value: th0,
          valueMax: th
        };
      }
      th0 = th;
    }
  }

  const contours = function(data) {
    return [...tricontours(data)];
  };

  // API
  contours.x = _ => (_ ? ((x = _), contours) : x);
  contours.y = _ => (_ ? ((y = _), contours) : y);
  contours.value = _ => (_ ? ((value = _), contours) : value);
  contours.thresholds = _ => (_ ? ((thresholds = _), contours) : thresholds);
  contours.triangulate = _ => (_ ? ((triangulate = _), contours) : triangulate);
  contours.pointInterpolate = _ =>
    _ ? ((pointInterpolate = _), contours) : pointInterpolate;
  contours.ringsort = _ =>
    _ ? ((ringsort = _), contours) : ringsort;
  contours.contours = tricontours;
  contours.contour = contour;
  contours.isobands = isobands;

  // expose the internals (useful for debugging, not part of the API)
  contours._values = () => values;
  contours._triangulation = () => triangulation;

  return contours;

  // navigate a triangle
  function next(i) {
    return i % 3 === 2 ? i - 2 : i + 1;
  }
  function prev(i) {
    return i % 3 === 0 ? i + 2 : i - 1;
  }

  function tricontour(triangulation, values, v0 = 0) {
    // sanity check
    for (const d of values) if (!isFinite(d)) throw ["Invalid value", d];

    const { halfedges, hull, inedges, triangles } = triangulation,
      n = values.length;

    function edgealpha(i) {
      return alpha(triangles[i], triangles[next(i)]);
    }
    function alpha(i, j) {
      const u = values[i],
        v = values[j];
      if (u <= v0 && v >= v0 && u < v) {
        return (v0 - u) / (v - u);
      }
    }

    // create the path from the first exit; cancel visited halfedges
    const rings = [],
      visited = new Uint8Array(halfedges.length).fill(0);
    let path, i, j, k, a;
    for (k = 0; k < halfedges.length; k++) {
      if (visited[k]) continue;

      i = k;
      path = [];

      while ((a = edgealpha(i)) > 0) {
        const [ti, tj] = [triangles[i], triangles[(j = next(i))]];

        // is our tour done?
        if (
          (path.length && (ti === path[0].ti && tj === path[0].tj)) ||
          path.length > 2 * n
        )
          break;

        visited[i] = 1;
        path.push({ ti, tj, a });

        // jump into the adjacent triangle
        if ((j = halfedges[i]) > -1) {
          if (edgealpha((j = next(j))) > 0) {
            i = j;
            continue;
          }
          if (edgealpha((j = next(j))) > 0) {
            i = j;
            continue;
          }
          // debugger;
        }

        // or follow the hull
        else {
          let h = (hull.indexOf(triangles[i]) + 1) % hull.length;

          while (values[hull[h]] < v0) {
            // debugger;
            h = (h + 1) % hull.length;
          }

          while (values[hull[h]] >= v0) {
            path.push({ ti: hull[h], tj: hull[h], a: 0 });
            h = (h + 1) % hull.length;
          }

          // take that entry
          j = inedges[hull[h]];
          path.push({
            ti: hull[h],
            tj: triangles[j],
            a: alpha(hull[h], triangles[j])
          });

          if (edgealpha((i = next(j))) > 0) continue;
          if (edgealpha((i = prev(j))) > 0) continue;
        }
      }

      if (path.length) {
        path.push(path[0]);
        rings.push(path.map(({ ti, tj, a }) => pointInterpolate(ti, tj, a)));
      }
    }

    // special case all values on the hull are >=v0, add the hull
    if (hull.every(d => values[d] >= v0)) {
      rings.unshift(
        Array.from(hull)
          .concat([hull[0]])
          .map(i => pointInterpolate(i, i, 0))
      );
    }

    return ringsort(rings); // return [rings] if we don't need to sort
  }
}

exports.tricontour = tricontour;

Object.defineProperty(exports, '__esModule', { value: true });

})));
