{"version": 3, "file": "index.cjs", "sources": ["../../src/utils.ts", "../../src/equals.ts", "../../src/comparator.ts", "../../src/index.ts"], "sourcesContent": ["import type {\n  AnyEqualityComparator,\n  Cache,\n  CircularState,\n  Dictionary,\n  State,\n  TypeEqualityComparator,\n} from './internalTypes';\n\nconst { getOwnPropertyNames, getOwnPropertySymbols } = Object;\nconst { hasOwnProperty } = Object.prototype;\n\n/**\n * Combine two comparators into a single comparators.\n */\nexport function combineComparators<Meta>(\n  comparatorA: AnyEqualityComparator<Meta>,\n  comparatorB: AnyEqualityComparator<Meta>,\n) {\n  return function isEqual<A, B>(a: A, b: B, state: State<Meta>) {\n    return comparatorA(a, b, state) && comparatorB(a, b, state);\n  };\n}\n\n/**\n * Wrap the provided `areItemsEqual` method to manage the circular state, allowing\n * for circular references to be safely included in the comparison without creating\n * stack overflows.\n */\nexport function createIsCircular<\n  AreItemsEqual extends TypeEqualityComparator<any, any>,\n>(areItemsEqual: AreItemsEqual): AreItemsEqual {\n  return function isCircular(\n    a: any,\n    b: any,\n    state: CircularState<Cache<any, any>>,\n  ) {\n    if (!a || !b || typeof a !== 'object' || typeof b !== 'object') {\n      return areItemsEqual(a, b, state);\n    }\n\n    const { cache } = state;\n\n    const cachedA = cache.get(a);\n    const cachedB = cache.get(b);\n\n    if (cachedA && cachedB) {\n      return cachedA === b && cachedB === a;\n    }\n\n    cache.set(a, b);\n    cache.set(b, a);\n\n    const result = areItemsEqual(a, b, state);\n\n    cache.delete(a);\n    cache.delete(b);\n\n    return result;\n  } as AreItemsEqual;\n}\n\n/**\n * Get the properties to strictly examine, which include both own properties that are\n * not enumerable and symbol properties.\n */\nexport function getStrictProperties(\n  object: Dictionary,\n): Array<string | symbol> {\n  return (getOwnPropertyNames(object) as Array<string | symbol>).concat(\n    getOwnPropertySymbols(object),\n  );\n}\n\n/**\n * Whether the object contains the property passed as an own property.\n */\nexport const hasOwn =\n  Object.hasOwn ||\n  ((object: Dictionary, property: number | string | symbol) =>\n    hasOwnProperty.call(object, property));\n\n/**\n * Whether the values passed are strictly equal or both NaN.\n */\nexport function sameValueZeroEqual(a: any, b: any): boolean {\n  return a === b || (!a && !b && a !== a && b !== b);\n}\n", "import { getStrictProperties, hasOwn, sameValueZeroEqual } from './utils';\nimport type {\n  Dictionary,\n  PrimitiveWrapper,\n  State,\n  TypedArray,\n} from './internalTypes';\n\nconst PREACT_VNODE = '__v';\nconst PREACT_OWNER = '__o';\nconst REACT_OWNER = '_owner';\n\nconst { getOwnPropertyDescriptor, keys } = Object;\n\n/**\n * Whether the arrays are equal in value.\n */\nexport function areArraysEqual(a: any[], b: any[], state: State<any>) {\n  let index = a.length;\n\n  if (b.length !== index) {\n    return false;\n  }\n\n  while (index-- > 0) {\n    if (!state.equals(a[index], b[index], index, index, a, b, state)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Whether the dates passed are equal in value.\n */\nexport function areDatesEqual(a: Date, b: Date): boolean {\n  return sameValueZeroEqual(a.getTime(), b.getTime());\n}\n\n/**\n * Whether the errors passed are equal in value.\n */\nexport function areErrorsEqual(a: Error, b: Error): boolean {\n  return (\n    a.name === b.name &&\n    a.message === b.message &&\n    a.cause === b.cause &&\n    a.stack === b.stack\n  );\n}\n\n/**\n * Whether the functions passed are equal in value.\n */\nexport function areFunctionsEqual(\n  a: (...args: any[]) => any,\n  b: (...args: any[]) => any,\n): boolean {\n  return a === b;\n}\n\n/**\n * Whether the `Map`s are equal in value.\n */\nexport function areMapsEqual(\n  a: Map<any, any>,\n  b: Map<any, any>,\n  state: State<any>,\n): boolean {\n  const size = a.size;\n\n  if (size !== b.size) {\n    return false;\n  }\n\n  if (!size) {\n    return true;\n  }\n\n  const matchedIndices: Array<true | undefined> = new Array(size);\n  const aIterable = a.entries();\n\n  let aResult: IteratorResult<[any, any]>;\n  let bResult: IteratorResult<[any, any]>;\n  let index = 0;\n\n  while ((aResult = aIterable.next())) {\n    if (aResult.done) {\n      break;\n    }\n\n    const bIterable = b.entries();\n\n    let hasMatch = false;\n    let matchIndex = 0;\n\n    while ((bResult = bIterable.next())) {\n      if (bResult.done) {\n        break;\n      }\n\n      if (matchedIndices[matchIndex]) {\n        matchIndex++;\n        continue;\n      }\n\n      const aEntry = aResult.value;\n      const bEntry = bResult.value;\n\n      if (\n        state.equals(aEntry[0], bEntry[0], index, matchIndex, a, b, state) &&\n        state.equals(aEntry[1], bEntry[1], aEntry[0], bEntry[0], a, b, state)\n      ) {\n        hasMatch = matchedIndices[matchIndex] = true;\n        break;\n      }\n\n      matchIndex++;\n    }\n\n    if (!hasMatch) {\n      return false;\n    }\n\n    index++;\n  }\n\n  return true;\n}\n\n/**\n * Whether the numbers are equal in value.\n */\nexport const areNumbersEqual = sameValueZeroEqual;\n\n/**\n * Whether the objects are equal in value.\n */\nexport function areObjectsEqual(\n  a: Dictionary,\n  b: Dictionary,\n  state: State<any>,\n): boolean {\n  const properties = keys(a);\n\n  let index = properties.length;\n\n  if (keys(b).length !== index) {\n    return false;\n  }\n\n  // Decrementing `while` showed faster results than either incrementing or\n  // decrementing `for` loop and than an incrementing `while` loop. Declarative\n  // methods like `some` / `every` were not used to avoid incurring the garbage\n  // cost of anonymous callbacks.\n  while (index-- > 0) {\n    if (!isPropertyEqual(a, b, state, properties[index]!)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Whether the objects are equal in value with strict property checking.\n */\nexport function areObjectsEqualStrict(\n  a: Dictionary,\n  b: Dictionary,\n  state: State<any>,\n): boolean {\n  const properties = getStrictProperties(a);\n\n  let index = properties.length;\n\n  if (getStrictProperties(b).length !== index) {\n    return false;\n  }\n\n  let property: string | symbol;\n  let descriptorA: ReturnType<typeof getOwnPropertyDescriptor>;\n  let descriptorB: ReturnType<typeof getOwnPropertyDescriptor>;\n\n  // Decrementing `while` showed faster results than either incrementing or\n  // decrementing `for` loop and than an incrementing `while` loop. Declarative\n  // methods like `some` / `every` were not used to avoid incurring the garbage\n  // cost of anonymous callbacks.\n  while (index-- > 0) {\n    property = properties[index]!;\n\n    if (!isPropertyEqual(a, b, state, property)) {\n      return false;\n    }\n\n    descriptorA = getOwnPropertyDescriptor(a, property);\n    descriptorB = getOwnPropertyDescriptor(b, property);\n\n    if (\n      (descriptorA || descriptorB) &&\n      (!descriptorA ||\n        !descriptorB ||\n        descriptorA.configurable !== descriptorB.configurable ||\n        descriptorA.enumerable !== descriptorB.enumerable ||\n        descriptorA.writable !== descriptorB.writable)\n    ) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Whether the primitive wrappers passed are equal in value.\n */\nexport function arePrimitiveWrappersEqual(\n  a: PrimitiveWrapper,\n  b: PrimitiveWrapper,\n): boolean {\n  return sameValueZeroEqual(a.valueOf(), b.valueOf());\n}\n\n/**\n * Whether the regexps passed are equal in value.\n */\nexport function areRegExpsEqual(a: RegExp, b: RegExp): boolean {\n  return a.source === b.source && a.flags === b.flags;\n}\n\n/**\n * Whether the `Set`s are equal in value.\n */\nexport function areSetsEqual(\n  a: Set<any>,\n  b: Set<any>,\n  state: State<any>,\n): boolean {\n  const size = a.size;\n\n  if (size !== b.size) {\n    return false;\n  }\n\n  if (!size) {\n    return true;\n  }\n\n  const matchedIndices: Array<true | undefined> = new Array(size);\n  const aIterable = a.values();\n\n  let aResult: IteratorResult<any>;\n  let bResult: IteratorResult<any>;\n\n  while ((aResult = aIterable.next())) {\n    if (aResult.done) {\n      break;\n    }\n\n    const bIterable = b.values();\n\n    let hasMatch = false;\n    let matchIndex = 0;\n\n    while ((bResult = bIterable.next())) {\n      if (bResult.done) {\n        break;\n      }\n\n      if (\n        !matchedIndices[matchIndex] &&\n        state.equals(\n          aResult.value,\n          bResult.value,\n          aResult.value,\n          bResult.value,\n          a,\n          b,\n          state,\n        )\n      ) {\n        hasMatch = matchedIndices[matchIndex] = true;\n        break;\n      }\n\n      matchIndex++;\n    }\n\n    if (!hasMatch) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Whether the TypedArray instances are equal in value.\n */\nexport function areTypedArraysEqual(a: TypedArray, b: TypedArray) {\n  let index = a.length;\n\n  if (b.length !== index) {\n    return false;\n  }\n\n  while (index-- > 0) {\n    if (a[index] !== b[index]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Whether the URL instances are equal in value.\n */\nexport function areUrlsEqual(a: URL, b: URL): boolean {\n  return (\n    a.hostname === b.hostname &&\n    a.pathname === b.pathname &&\n    a.protocol === b.protocol &&\n    a.port === b.port &&\n    a.hash === b.hash &&\n    a.username === b.username &&\n    a.password === b.password\n  );\n}\n\nfunction isPropertyEqual(\n  a: Dictionary,\n  b: Dictionary,\n  state: State<any>,\n  property: string | symbol,\n) {\n  if (\n    (property === REACT_OWNER ||\n      property === PREACT_OWNER ||\n      property === PREACT_VNODE) &&\n    (a.$$typeof || b.$$typeof)\n  ) {\n    return true;\n  }\n\n  return (\n    hasOwn(b, property) &&\n    state.equals(a[property], b[property], property, property, a, b, state)\n  );\n}\n", "import {\n  areArraysEqual as areArraysEqualDefault,\n  areDatesEqual as areDatesEqualDefault,\n  areErrorsEqual as areErrorsEqualDefault,\n  areFunctionsEqual as areFunctionsEqualDefault,\n  areMapsEqual as areMapsEqualDefault,\n  areNumbersEqual as areNumbersEqualDefault,\n  areObjectsEqual as areObjectsEqualDefault,\n  areObjectsEqualStrict as areObjectsEqualStrictDefault,\n  arePrimitiveWrappersEqual as arePrimitiveWrappersEqualDefault,\n  areRegExpsEqual as areRegExpsEqualDefault,\n  areSetsEqual as areSetsEqualDefault,\n  areTypedArraysEqual as areTypedArraysEqualDefault,\n  areUrlsEqual as areUrlsEqualDefault,\n} from './equals';\nimport { combineComparators, createIsCircular } from './utils';\nimport type {\n  ComparatorConfig,\n  CreateState,\n  CustomEqualCreatorOptions,\n  EqualityComparator,\n  InternalEqualityComparator,\n  State,\n} from './internalTypes';\n\nconst ARGUMENTS_TAG = '[object Arguments]';\nconst BOOLEAN_TAG = '[object Boolean]';\nconst DATE_TAG = '[object Date]';\nconst ERROR_TAG = '[object Error]';\nconst MAP_TAG = '[object Map]';\nconst NUMBER_TAG = '[object Number]';\nconst OBJECT_TAG = '[object Object]';\nconst REG_EXP_TAG = '[object RegExp]';\nconst SET_TAG = '[object Set]';\nconst STRING_TAG = '[object String]';\nconst URL_TAG = '[object URL]';\n\nconst { isArray } = Array;\nconst isTypedArray =\n  typeof ArrayBuffer === 'function' && ArrayBuffer.isView\n    ? ArrayBuffer.isView\n    : null;\nconst { assign } = Object;\nconst getTag = Object.prototype.toString.call.bind(\n  Object.prototype.toString,\n) as (a: object) => string;\n\ninterface CreateIsEqualOptions<Meta> {\n  circular: boolean;\n  comparator: EqualityComparator<Meta>;\n  createState: CreateState<Meta> | undefined;\n  equals: InternalEqualityComparator<Meta>;\n  strict: boolean;\n}\n\n/**\n * Create a comparator method based on the type-specific equality comparators passed.\n */\nexport function createEqualityComparator<Meta>({\n  areArraysEqual,\n  areDatesEqual,\n  areErrorsEqual,\n  areFunctionsEqual,\n  areMapsEqual,\n  areNumbersEqual,\n  areObjectsEqual,\n  arePrimitiveWrappersEqual,\n  areRegExpsEqual,\n  areSetsEqual,\n  areTypedArraysEqual,\n  areUrlsEqual,\n}: ComparatorConfig<Meta>): EqualityComparator<Meta> {\n  /**\n   * compare the value of the two objects and return true if they are equivalent in values\n   */\n  return function comparator(a: any, b: any, state: State<Meta>): boolean {\n    // If the items are strictly equal, no need to do a value comparison.\n    if (a === b) {\n      return true;\n    }\n\n    // If either of the items are nullish and fail the strictly equal check\n    // above, then they must be unequal.\n    if (a == null || b == null) {\n      return false;\n    }\n\n    const type = typeof a;\n\n    if (type !== typeof b) {\n      return false;\n    }\n\n    if (type !== 'object') {\n      if (type === 'number') {\n        return areNumbersEqual(a, b, state);\n      }\n\n      if (type === 'function') {\n        return areFunctionsEqual(a, b, state);\n      }\n\n      // If a primitive value that is not strictly equal, it must be unequal.\n      return false;\n    }\n\n    const constructor = a.constructor;\n\n    // Checks are listed in order of commonality of use-case:\n    //   1. Common complex object types (plain object, array)\n    //   2. Common data values (date, regexp)\n    //   3. Less-common complex object types (map, set)\n    //   4. Less-common data values (promise, primitive wrappers)\n    // Inherently this is both subjective and assumptive, however\n    // when reviewing comparable libraries in the wild this order\n    // appears to be generally consistent.\n\n    // Constructors should match, otherwise there is potential for false positives\n    // between class and subclass or custom object and POJO.\n    if (constructor !== b.constructor) {\n      return false;\n    }\n\n    // `isPlainObject` only checks against the object's own realm. Cross-realm\n    // comparisons are rare, and will be handled in the ultimate fallback, so\n    // we can avoid capturing the string tag.\n    if (constructor === Object) {\n      return areObjectsEqual(a, b, state);\n    }\n\n    // `isArray()` works on subclasses and is cross-realm, so we can avoid capturing\n    // the string tag or doing an `instanceof` check.\n    if (isArray(a)) {\n      return areArraysEqual(a, b, state);\n    }\n\n    // `isTypedArray()` works on all possible TypedArray classes, so we can avoid\n    // capturing the string tag or comparing against all possible constructors.\n    if (isTypedArray != null && isTypedArray(a)) {\n      return areTypedArraysEqual(a, b, state);\n    }\n\n    // Try to fast-path equality checks for other complex object types in the\n    // same realm to avoid capturing the string tag. Strict equality is used\n    // instead of `instanceof` because it is more performant for the common\n    // use-case. If someone is subclassing a native class, it will be handled\n    // with the string tag comparison.\n\n    if (constructor === Date) {\n      return areDatesEqual(a, b, state);\n    }\n\n    if (constructor === RegExp) {\n      return areRegExpsEqual(a, b, state);\n    }\n\n    if (constructor === Map) {\n      return areMapsEqual(a, b, state);\n    }\n\n    if (constructor === Set) {\n      return areSetsEqual(a, b, state);\n    }\n\n    // Since this is a custom object, capture the string tag to determing its type.\n    // This is reasonably performant in modern environments like v8 and SpiderMonkey.\n    const tag = getTag(a);\n\n    if (tag === DATE_TAG) {\n      return areDatesEqual(a, b, state);\n    }\n\n    // For RegExp, the properties are not enumerable, and therefore will give false positives if\n    // tested like a standard object.\n    if (tag === REG_EXP_TAG) {\n      return areRegExpsEqual(a, b, state);\n    }\n\n    if (tag === MAP_TAG) {\n      return areMapsEqual(a, b, state);\n    }\n\n    if (tag === SET_TAG) {\n      return areSetsEqual(a, b, state);\n    }\n\n    if (tag === OBJECT_TAG) {\n      // The exception for value comparison is custom `Promise`-like class instances. These should\n      // be treated the same as standard `Promise` objects, which means strict equality, and if\n      // it reaches this point then that strict equality comparison has already failed.\n      return (\n        typeof a.then !== 'function' &&\n        typeof b.then !== 'function' &&\n        areObjectsEqual(a, b, state)\n      );\n    }\n\n    // If a URL tag, it should be tested explicitly. Like RegExp, the properties are not\n    // enumerable, and therefore will give false positives if tested like a standard object.\n    if (tag === URL_TAG) {\n      return areUrlsEqual(a, b, state);\n    }\n\n    // If an error tag, it should be tested explicitly. Like RegExp, the properties are not\n    // enumerable, and therefore will give false positives if tested like a standard object.\n    if (tag === ERROR_TAG) {\n      return areErrorsEqual(a, b, state);\n    }\n\n    // If an arguments tag, it should be treated as a standard object.\n    if (tag === ARGUMENTS_TAG) {\n      return areObjectsEqual(a, b, state);\n    }\n\n    // As the penultimate fallback, check if the values passed are primitive wrappers. This\n    // is very rare in modern JS, which is why it is deprioritized compared to all other object\n    // types.\n    if (tag === BOOLEAN_TAG || tag === NUMBER_TAG || tag === STRING_TAG) {\n      return arePrimitiveWrappersEqual(a, b, state);\n    }\n\n    // If not matching any tags that require a specific type of comparison, then we hard-code false because\n    // the only thing remaining is strict equality, which has already been compared. This is for a few reasons:\n    //   - Certain types that cannot be introspected (e.g., `WeakMap`). For these types, this is the only\n    //     comparison that can be made.\n    //   - For types that can be introspected, but rarely have requirements to be compared\n    //     (`ArrayBuffer`, `DataView`, etc.), the cost is avoided to prioritize the common\n    //     use-cases (may be included in a future release, if requested enough).\n    //   - For types that can be introspected but do not have an objective definition of what\n    //     equality is (`Error`, etc.), the subjective decision is to be conservative and strictly compare.\n    // In all cases, these decisions should be reevaluated based on changes to the language and\n    // common development practices.\n    return false;\n  };\n}\n\n/**\n * Create the configuration object used for building comparators.\n */\nexport function createEqualityComparatorConfig<Meta>({\n  circular,\n  createCustomConfig,\n  strict,\n}: CustomEqualCreatorOptions<Meta>): ComparatorConfig<Meta> {\n  let config = {\n    areArraysEqual: strict\n      ? areObjectsEqualStrictDefault\n      : areArraysEqualDefault,\n    areDatesEqual: areDatesEqualDefault,\n    areErrorsEqual: areErrorsEqualDefault,\n    areFunctionsEqual: areFunctionsEqualDefault,\n    areMapsEqual: strict\n      ? combineComparators(areMapsEqualDefault, areObjectsEqualStrictDefault)\n      : areMapsEqualDefault,\n    areNumbersEqual: areNumbersEqualDefault,\n    areObjectsEqual: strict\n      ? areObjectsEqualStrictDefault\n      : areObjectsEqualDefault,\n    arePrimitiveWrappersEqual: arePrimitiveWrappersEqualDefault,\n    areRegExpsEqual: areRegExpsEqualDefault,\n    areSetsEqual: strict\n      ? combineComparators(areSetsEqualDefault, areObjectsEqualStrictDefault)\n      : areSetsEqualDefault,\n    areTypedArraysEqual: strict\n      ? areObjectsEqualStrictDefault\n      : areTypedArraysEqualDefault,\n    areUrlsEqual: areUrlsEqualDefault,\n  };\n\n  if (createCustomConfig) {\n    config = assign({}, config, createCustomConfig(config));\n  }\n\n  if (circular) {\n    const areArraysEqual = createIsCircular(config.areArraysEqual);\n    const areMapsEqual = createIsCircular(config.areMapsEqual);\n    const areObjectsEqual = createIsCircular(config.areObjectsEqual);\n    const areSetsEqual = createIsCircular(config.areSetsEqual);\n\n    config = assign({}, config, {\n      areArraysEqual,\n      areMapsEqual,\n      areObjectsEqual,\n      areSetsEqual,\n    });\n  }\n\n  return config;\n}\n\n/**\n * Default equality comparator pass-through, used as the standard `isEqual` creator for\n * use inside the built comparator.\n */\nexport function createInternalEqualityComparator<Meta>(\n  compare: EqualityComparator<Meta>,\n): InternalEqualityComparator<Meta> {\n  return function (\n    a: any,\n    b: any,\n    _indexOrKeyA: any,\n    _indexOrKeyB: any,\n    _parentA: any,\n    _parentB: any,\n    state: State<Meta>,\n  ) {\n    return compare(a, b, state);\n  };\n}\n\n/**\n * Create the `isEqual` function used by the consuming application.\n */\nexport function createIsEqual<Meta>({\n  circular,\n  comparator,\n  createState,\n  equals,\n  strict,\n}: CreateIsEqualOptions<Meta>) {\n  if (createState) {\n    return function isEqual<A, B>(a: A, b: B): boolean {\n      const { cache = circular ? new WeakMap() : undefined, meta } =\n        createState!();\n\n      return comparator(a, b, {\n        cache,\n        equals,\n        meta,\n        strict,\n      } as State<Meta>);\n    };\n  }\n\n  if (circular) {\n    return function isEqual<A, B>(a: A, b: B): boolean {\n      return comparator(a, b, {\n        cache: new WeakMap(),\n        equals,\n        meta: undefined as Meta,\n        strict,\n      } as State<Meta>);\n    };\n  }\n\n  const state = {\n    cache: undefined,\n    equals,\n    meta: undefined,\n    strict,\n  } as State<Meta>;\n\n  return function isEqual<A, B>(a: A, b: B): boolean {\n    return comparator(a, b, state);\n  };\n}\n", "import {\n  createEqualityComparatorConfig,\n  createEqualityComparator,\n  createInternalEqualityComparator,\n  createIsEqual,\n} from './comparator';\nimport type { CustomEqualCreatorOptions } from './internalTypes';\nimport { sameValueZeroEqual } from './utils';\n\nexport { sameValueZeroEqual };\nexport type {\n  AnyEqualityComparator,\n  Cache,\n  CircularState,\n  ComparatorConfig,\n  CreateCustomComparatorConfig,\n  CreateState,\n  CustomEqualCreatorOptions,\n  DefaultState,\n  Dictionary,\n  EqualityComparator,\n  EqualityComparatorCreator,\n  InternalEqualityComparator,\n  PrimitiveWrapper,\n  State,\n  TypeEqualityComparator,\n  TypedArray,\n} from './internalTypes';\n\n/**\n * Whether the items passed are deeply-equal in value.\n */\nexport const deepEqual = createCustomEqual();\n\n/**\n * Whether the items passed are deeply-equal in value based on strict comparison.\n */\nexport const strictDeepEqual = createCustomEqual({ strict: true });\n\n/**\n * Whether the items passed are deeply-equal in value, including circular references.\n */\nexport const circularDeepEqual = createCustomEqual({ circular: true });\n\n/**\n * Whether the items passed are deeply-equal in value, including circular references,\n * based on strict comparison.\n */\nexport const strictCircularDeepEqual = createCustomEqual({\n  circular: true,\n  strict: true,\n});\n\n/**\n * Whether the items passed are shallowly-equal in value.\n */\nexport const shallowEqual = createCustomEqual({\n  createInternalComparator: () => sameValueZeroEqual,\n});\n\n/**\n * Whether the items passed are shallowly-equal in value based on strict comparison\n */\nexport const strictShallowEqual = createCustomEqual({\n  strict: true,\n  createInternalComparator: () => sameValueZeroEqual,\n});\n\n/**\n * Whether the items passed are shallowly-equal in value, including circular references.\n */\nexport const circularShallowEqual = createCustomEqual({\n  circular: true,\n  createInternalComparator: () => sameValueZeroEqual,\n});\n\n/**\n * Whether the items passed are shallowly-equal in value, including circular references,\n * based on strict comparison.\n */\nexport const strictCircularShallowEqual = createCustomEqual({\n  circular: true,\n  createInternalComparator: () => sameValueZeroEqual,\n  strict: true,\n});\n\n/**\n * Create a custom equality comparison method.\n *\n * This can be done to create very targeted comparisons in extreme hot-path scenarios\n * where the standard methods are not performant enough, but can also be used to provide\n * support for legacy environments that do not support expected features like\n * `RegExp.prototype.flags` out of the box.\n */\nexport function createCustomEqual<Meta = undefined>(\n  options: CustomEqualCreatorOptions<Meta> = {},\n) {\n  const {\n    circular = false,\n    createInternalComparator: createCustomInternalComparator,\n    createState,\n    strict = false,\n  } = options;\n\n  const config = createEqualityComparatorConfig<Meta>(options);\n  const comparator = createEqualityComparator(config);\n  const equals = createCustomInternalComparator\n    ? createCustomInternalComparator(comparator)\n    : createInternalEqualityComparator(comparator);\n\n  return createIsEqual({ circular, comparator, createState, equals, strict });\n}\n"], "names": ["areObjectsEqualStrictDefault", "areArraysEqualDefault", "areDatesEqualDefault", "areErrorsEqualDefault", "areFunctionsEqualDefault", "areMapsEqualDefault", "areNumbersEqualDefault", "areObjectsEqualDefault", "arePrimitiveWrappersEqualDefault", "areRegExpsEqualDefault", "areSetsEqualDefault", "areTypedArraysEqualDefault", "areUrlsEqualDefault", "areArraysEqual", "areMapsEqual", "areObjectsEqual", "areSetsEqual"], "mappings": ";;AASQ,IAAA,mBAAmB,GAA4B,MAAM,CAAA,mBAAlC,EAAE,qBAAqB,GAAK,MAAM,CAAA,qBAAX,CAAY;AACtD,IAAA,cAAc,GAAK,MAAM,CAAC,SAAS,eAArB,CAAsB;AAE5C;;AAEG;AACa,SAAA,kBAAkB,CAChC,WAAwC,EACxC,WAAwC,EAAA;AAExC,IAAA,OAAO,SAAS,OAAO,CAAO,CAAI,EAAE,CAAI,EAAE,KAAkB,EAAA;AAC1D,QAAA,OAAO,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,IAAI,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AAC9D,KAAC,CAAC;AACJ,CAAC;AAED;;;;AAIG;AACG,SAAU,gBAAgB,CAE9B,aAA4B,EAAA;AAC5B,IAAA,OAAO,SAAS,UAAU,CACxB,CAAM,EACN,CAAM,EACN,KAAqC,EAAA;AAErC,QAAA,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;YAC9D,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AACnC,SAAA;AAEO,QAAA,IAAA,KAAK,GAAK,KAAK,CAAA,KAAV,CAAW;QAExB,IAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC7B,IAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAE7B,IAAI,OAAO,IAAI,OAAO,EAAE;AACtB,YAAA,OAAO,OAAO,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,CAAC;AACvC,SAAA;AAED,QAAA,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAChB,QAAA,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEhB,IAAM,MAAM,GAAG,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AAE1C,QAAA,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAChB,QAAA,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAEhB,QAAA,OAAO,MAAM,CAAC;AAChB,KAAkB,CAAC;AACrB,CAAC;AAED;;;AAGG;AACG,SAAU,mBAAmB,CACjC,MAAkB,EAAA;AAElB,IAAA,OAAQ,mBAAmB,CAAC,MAAM,CAA4B,CAAC,MAAM,CACnE,qBAAqB,CAAC,MAAM,CAAC,CAC9B,CAAC;AACJ,CAAC;AAED;;AAEG;AACI,IAAM,MAAM,GACjB,MAAM,CAAC,MAAM;KACZ,UAAC,MAAkB,EAAE,QAAkC,EAAA;AACtD,QAAA,OAAA,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;AAArC,KAAqC,CAAC,CAAC;AAE3C;;AAEG;AACa,SAAA,kBAAkB,CAAC,CAAM,EAAE,CAAM,EAAA;AAC/C,IAAA,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACrD;;AC/EA,IAAM,YAAY,GAAG,KAAK,CAAC;AAC3B,IAAM,YAAY,GAAG,KAAK,CAAC;AAC3B,IAAM,WAAW,GAAG,QAAQ,CAAC;AAErB,IAAA,wBAAwB,GAAW,MAAM,CAAA,wBAAjB,EAAE,IAAI,GAAK,MAAM,CAAA,IAAX,CAAY;AAElD;;AAEG;SACa,cAAc,CAAC,CAAQ,EAAE,CAAQ,EAAE,KAAiB,EAAA;AAClE,IAAA,IAAI,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC;AAErB,IAAA,IAAI,CAAC,CAAC,MAAM,KAAK,KAAK,EAAE;AACtB,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AAED,IAAA,OAAO,KAAK,EAAE,GAAG,CAAC,EAAE;QAClB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE;AAChE,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AACF,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;AAEG;AACa,SAAA,aAAa,CAAC,CAAO,EAAE,CAAO,EAAA;AAC5C,IAAA,OAAO,kBAAkB,CAAC,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;AACtD,CAAC;AAED;;AAEG;AACa,SAAA,cAAc,CAAC,CAAQ,EAAE,CAAQ,EAAA;AAC/C,IAAA,QACE,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI;AACjB,QAAA,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,OAAO;AACvB,QAAA,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK;AACnB,QAAA,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK,EACnB;AACJ,CAAC;AAED;;AAEG;AACa,SAAA,iBAAiB,CAC/B,CAA0B,EAC1B,CAA0B,EAAA;IAE1B,OAAO,CAAC,KAAK,CAAC,CAAC;AACjB,CAAC;AAED;;AAEG;SACa,YAAY,CAC1B,CAAgB,EAChB,CAAgB,EAChB,KAAiB,EAAA;AAEjB,IAAA,IAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;AAEpB,IAAA,IAAI,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE;AACnB,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;IAED,IAAI,CAAC,IAAI,EAAE;AACT,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;AAED,IAAA,IAAM,cAAc,GAA4B,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC;AAChE,IAAA,IAAM,SAAS,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;AAE9B,IAAA,IAAI,OAAmC,CAAC;AACxC,IAAA,IAAI,OAAmC,CAAC;IACxC,IAAI,KAAK,GAAG,CAAC,CAAC;IAEd,QAAQ,OAAO,GAAG,SAAS,CAAC,IAAI,EAAE,GAAG;QACnC,IAAI,OAAO,CAAC,IAAI,EAAE;YAChB,MAAM;AACP,SAAA;AAED,QAAA,IAAM,SAAS,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;QAE9B,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,QAAQ,OAAO,GAAG,SAAS,CAAC,IAAI,EAAE,GAAG;YACnC,IAAI,OAAO,CAAC,IAAI,EAAE;gBAChB,MAAM;AACP,aAAA;AAED,YAAA,IAAI,cAAc,CAAC,UAAU,CAAC,EAAE;AAC9B,gBAAA,UAAU,EAAE,CAAC;gBACb,SAAS;AACV,aAAA;AAED,YAAA,IAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC;AAC7B,YAAA,IAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC;YAE7B,IACE,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;AAClE,gBAAA,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,EACrE;AACA,gBAAA,QAAQ,GAAG,cAAc,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;gBAC7C,MAAM;AACP,aAAA;AAED,YAAA,UAAU,EAAE,CAAC;AACd,SAAA;QAED,IAAI,CAAC,QAAQ,EAAE;AACb,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AAED,QAAA,KAAK,EAAE,CAAC;AACT,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;AAEG;AACI,IAAM,eAAe,GAAG,kBAAkB,CAAC;AAElD;;AAEG;SACa,eAAe,CAC7B,CAAa,EACb,CAAa,EACb,KAAiB,EAAA;AAEjB,IAAA,IAAM,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAE3B,IAAA,IAAI,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC;IAE9B,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,KAAK,EAAE;AAC5B,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;;;;;AAMD,IAAA,OAAO,KAAK,EAAE,GAAG,CAAC,EAAE;AAClB,QAAA,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,UAAU,CAAC,KAAK,CAAE,CAAC,EAAE;AACrD,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AACF,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;AAEG;SACa,qBAAqB,CACnC,CAAa,EACb,CAAa,EACb,KAAiB,EAAA;AAEjB,IAAA,IAAM,UAAU,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC;AAE1C,IAAA,IAAI,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC;IAE9B,IAAI,mBAAmB,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,KAAK,EAAE;AAC3C,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AAED,IAAA,IAAI,QAAyB,CAAC;AAC9B,IAAA,IAAI,WAAwD,CAAC;AAC7D,IAAA,IAAI,WAAwD,CAAC;;;;;AAM7D,IAAA,OAAO,KAAK,EAAE,GAAG,CAAC,EAAE;AAClB,QAAA,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAE,CAAC;QAE9B,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,EAAE;AAC3C,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AAED,QAAA,WAAW,GAAG,wBAAwB,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AACpD,QAAA,WAAW,GAAG,wBAAwB,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AAEpD,QAAA,IACE,CAAC,WAAW,IAAI,WAAW;AAC3B,aAAC,CAAC,WAAW;AACX,gBAAA,CAAC,WAAW;AACZ,gBAAA,WAAW,CAAC,YAAY,KAAK,WAAW,CAAC,YAAY;AACrD,gBAAA,WAAW,CAAC,UAAU,KAAK,WAAW,CAAC,UAAU;AACjD,gBAAA,WAAW,CAAC,QAAQ,KAAK,WAAW,CAAC,QAAQ,CAAC,EAChD;AACA,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AACF,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;AAEG;AACa,SAAA,yBAAyB,CACvC,CAAmB,EACnB,CAAmB,EAAA;AAEnB,IAAA,OAAO,kBAAkB,CAAC,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;AACtD,CAAC;AAED;;AAEG;AACa,SAAA,eAAe,CAAC,CAAS,EAAE,CAAS,EAAA;AAClD,IAAA,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK,CAAC;AACtD,CAAC;AAED;;AAEG;SACa,YAAY,CAC1B,CAAW,EACX,CAAW,EACX,KAAiB,EAAA;AAEjB,IAAA,IAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;AAEpB,IAAA,IAAI,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE;AACnB,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;IAED,IAAI,CAAC,IAAI,EAAE;AACT,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;AAED,IAAA,IAAM,cAAc,GAA4B,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC;AAChE,IAAA,IAAM,SAAS,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC;AAE7B,IAAA,IAAI,OAA4B,CAAC;AACjC,IAAA,IAAI,OAA4B,CAAC;IAEjC,QAAQ,OAAO,GAAG,SAAS,CAAC,IAAI,EAAE,GAAG;QACnC,IAAI,OAAO,CAAC,IAAI,EAAE;YAChB,MAAM;AACP,SAAA;AAED,QAAA,IAAM,SAAS,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC;QAE7B,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,QAAQ,OAAO,GAAG,SAAS,CAAC,IAAI,EAAE,GAAG;YACnC,IAAI,OAAO,CAAC,IAAI,EAAE;gBAChB,MAAM;AACP,aAAA;AAED,YAAA,IACE,CAAC,cAAc,CAAC,UAAU,CAAC;gBAC3B,KAAK,CAAC,MAAM,CACV,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,KAAK,EACb,CAAC,EACD,CAAC,EACD,KAAK,CACN,EACD;AACA,gBAAA,QAAQ,GAAG,cAAc,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;gBAC7C,MAAM;AACP,aAAA;AAED,YAAA,UAAU,EAAE,CAAC;AACd,SAAA;QAED,IAAI,CAAC,QAAQ,EAAE;AACb,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AACF,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;AAEG;AACa,SAAA,mBAAmB,CAAC,CAAa,EAAE,CAAa,EAAA;AAC9D,IAAA,IAAI,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC;AAErB,IAAA,IAAI,CAAC,CAAC,MAAM,KAAK,KAAK,EAAE;AACtB,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AAED,IAAA,OAAO,KAAK,EAAE,GAAG,CAAC,EAAE;QAClB,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE;AACzB,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AACF,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;AAEG;AACa,SAAA,YAAY,CAAC,CAAM,EAAE,CAAM,EAAA;AACzC,IAAA,QACE,CAAC,CAAC,QAAQ,KAAK,CAAC,CAAC,QAAQ;AACzB,QAAA,CAAC,CAAC,QAAQ,KAAK,CAAC,CAAC,QAAQ;AACzB,QAAA,CAAC,CAAC,QAAQ,KAAK,CAAC,CAAC,QAAQ;AACzB,QAAA,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI;AACjB,QAAA,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI;AACjB,QAAA,CAAC,CAAC,QAAQ,KAAK,CAAC,CAAC,QAAQ;AACzB,QAAA,CAAC,CAAC,QAAQ,KAAK,CAAC,CAAC,QAAQ,EACzB;AACJ,CAAC;AAED,SAAS,eAAe,CACtB,CAAa,EACb,CAAa,EACb,KAAiB,EACjB,QAAyB,EAAA;IAEzB,IACE,CAAC,QAAQ,KAAK,WAAW;AACvB,QAAA,QAAQ,KAAK,YAAY;QACzB,QAAQ,KAAK,YAAY;SAC1B,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,QAAQ,CAAC,EAC1B;AACA,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;AAED,IAAA,QACE,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC;QACnB,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,EACvE;AACJ;;ACrUA,IAAM,aAAa,GAAG,oBAAoB,CAAC;AAC3C,IAAM,WAAW,GAAG,kBAAkB,CAAC;AACvC,IAAM,QAAQ,GAAG,eAAe,CAAC;AACjC,IAAM,SAAS,GAAG,gBAAgB,CAAC;AACnC,IAAM,OAAO,GAAG,cAAc,CAAC;AAC/B,IAAM,UAAU,GAAG,iBAAiB,CAAC;AACrC,IAAM,UAAU,GAAG,iBAAiB,CAAC;AACrC,IAAM,WAAW,GAAG,iBAAiB,CAAC;AACtC,IAAM,OAAO,GAAG,cAAc,CAAC;AAC/B,IAAM,UAAU,GAAG,iBAAiB,CAAC;AACrC,IAAM,OAAO,GAAG,cAAc,CAAC;AAEvB,IAAA,OAAO,GAAK,KAAK,CAAA,OAAV,CAAW;AAC1B,IAAM,YAAY,GAChB,OAAO,WAAW,KAAK,UAAU,IAAI,WAAW,CAAC,MAAM;MACnD,WAAW,CAAC,MAAM;MAClB,IAAI,CAAC;AACH,IAAA,MAAM,GAAK,MAAM,CAAA,MAAX,CAAY;AAC1B,IAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAChD,MAAM,CAAC,SAAS,CAAC,QAAQ,CACD,CAAC;AAU3B;;AAEG;AACG,SAAU,wBAAwB,CAAO,EAatB,EAAA;AAZvB,IAAA,IAAA,cAAc,GAAA,EAAA,CAAA,cAAA,EACd,aAAa,GAAA,EAAA,CAAA,aAAA,EACb,cAAc,GAAA,EAAA,CAAA,cAAA,EACd,iBAAiB,GAAA,EAAA,CAAA,iBAAA,EACjB,YAAY,GAAA,EAAA,CAAA,YAAA,EACZ,eAAe,GAAA,EAAA,CAAA,eAAA,EACf,eAAe,GAAA,EAAA,CAAA,eAAA,EACf,yBAAyB,GAAA,EAAA,CAAA,yBAAA,EACzB,eAAe,GAAA,EAAA,CAAA,eAAA,EACf,YAAY,kBAAA,EACZ,mBAAmB,GAAA,EAAA,CAAA,mBAAA,EACnB,YAAY,GAAA,EAAA,CAAA,YAAA,CAAA;AAEZ;;AAEG;AACH,IAAA,OAAO,SAAS,UAAU,CAAC,CAAM,EAAE,CAAM,EAAE,KAAkB,EAAA;;QAE3D,IAAI,CAAC,KAAK,CAAC,EAAE;AACX,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;;;AAID,QAAA,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE;AAC1B,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AAED,QAAA,IAAM,IAAI,GAAG,OAAO,CAAC,CAAC;AAEtB,QAAA,IAAI,IAAI,KAAK,OAAO,CAAC,EAAE;AACrB,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;QAED,IAAI,IAAI,KAAK,QAAQ,EAAE;YACrB,IAAI,IAAI,KAAK,QAAQ,EAAE;gBACrB,OAAO,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AACrC,aAAA;YAED,IAAI,IAAI,KAAK,UAAU,EAAE;gBACvB,OAAO,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AACvC,aAAA;;AAGD,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AAED,QAAA,IAAM,WAAW,GAAG,CAAC,CAAC,WAAW,CAAC;;;;;;;;;;;AAalC,QAAA,IAAI,WAAW,KAAK,CAAC,CAAC,WAAW,EAAE;AACjC,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;;;;QAKD,IAAI,WAAW,KAAK,MAAM,EAAE;YAC1B,OAAO,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AACrC,SAAA;;;AAID,QAAA,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE;YACd,OAAO,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AACpC,SAAA;;;QAID,IAAI,YAAY,IAAI,IAAI,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE;YAC3C,OAAO,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AACzC,SAAA;;;;;;QAQD,IAAI,WAAW,KAAK,IAAI,EAAE;YACxB,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AACnC,SAAA;QAED,IAAI,WAAW,KAAK,MAAM,EAAE;YAC1B,OAAO,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AACrC,SAAA;QAED,IAAI,WAAW,KAAK,GAAG,EAAE;YACvB,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AAClC,SAAA;QAED,IAAI,WAAW,KAAK,GAAG,EAAE;YACvB,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AAClC,SAAA;;;AAID,QAAA,IAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAEtB,IAAI,GAAG,KAAK,QAAQ,EAAE;YACpB,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AACnC,SAAA;;;QAID,IAAI,GAAG,KAAK,WAAW,EAAE;YACvB,OAAO,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AACrC,SAAA;QAED,IAAI,GAAG,KAAK,OAAO,EAAE;YACnB,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AAClC,SAAA;QAED,IAAI,GAAG,KAAK,OAAO,EAAE;YACnB,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AAClC,SAAA;QAED,IAAI,GAAG,KAAK,UAAU,EAAE;;;;AAItB,YAAA,QACE,OAAO,CAAC,CAAC,IAAI,KAAK,UAAU;AAC5B,gBAAA,OAAO,CAAC,CAAC,IAAI,KAAK,UAAU;gBAC5B,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,EAC5B;AACH,SAAA;;;QAID,IAAI,GAAG,KAAK,OAAO,EAAE;YACnB,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AAClC,SAAA;;;QAID,IAAI,GAAG,KAAK,SAAS,EAAE;YACrB,OAAO,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AACpC,SAAA;;QAGD,IAAI,GAAG,KAAK,aAAa,EAAE;YACzB,OAAO,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AACrC,SAAA;;;;QAKD,IAAI,GAAG,KAAK,WAAW,IAAI,GAAG,KAAK,UAAU,IAAI,GAAG,KAAK,UAAU,EAAE;YACnE,OAAO,yBAAyB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AAC/C,SAAA;;;;;;;;;;;;AAaD,QAAA,OAAO,KAAK,CAAC;AACf,KAAC,CAAC;AACJ,CAAC;AAED;;AAEG;AACG,SAAU,8BAA8B,CAAO,EAInB,EAAA;AAHhC,IAAA,IAAA,QAAQ,cAAA,EACR,kBAAkB,GAAA,EAAA,CAAA,kBAAA,EAClB,MAAM,GAAA,EAAA,CAAA,MAAA,CAAA;AAEN,IAAA,IAAI,MAAM,GAAG;AACX,QAAA,cAAc,EAAE,MAAM;AACpB,cAAEA,qBAA4B;AAC9B,cAAEC,cAAqB;AACzB,QAAA,aAAa,EAAEC,aAAoB;AACnC,QAAA,cAAc,EAAEC,cAAqB;AACrC,QAAA,iBAAiB,EAAEC,iBAAwB;AAC3C,QAAA,YAAY,EAAE,MAAM;AAClB,cAAE,kBAAkB,CAACC,YAAmB,EAAEL,qBAA4B,CAAC;AACvE,cAAEK,YAAmB;AACvB,QAAA,eAAe,EAAEC,eAAsB;AACvC,QAAA,eAAe,EAAE,MAAM;AACrB,cAAEN,qBAA4B;AAC9B,cAAEO,eAAsB;AAC1B,QAAA,yBAAyB,EAAEC,yBAAgC;AAC3D,QAAA,eAAe,EAAEC,eAAsB;AACvC,QAAA,YAAY,EAAE,MAAM;AAClB,cAAE,kBAAkB,CAACC,YAAmB,EAAEV,qBAA4B,CAAC;AACvE,cAAEU,YAAmB;AACvB,QAAA,mBAAmB,EAAE,MAAM;AACzB,cAAEV,qBAA4B;AAC9B,cAAEW,mBAA0B;AAC9B,QAAA,YAAY,EAAEC,YAAmB;KAClC,CAAC;AAEF,IAAA,IAAI,kBAAkB,EAAE;AACtB,QAAA,MAAM,GAAG,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC;AACzD,KAAA;AAED,IAAA,IAAI,QAAQ,EAAE;QACZ,IAAMC,gBAAc,GAAG,gBAAgB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QAC/D,IAAMC,cAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QAC3D,IAAMC,iBAAe,GAAG,gBAAgB,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;QACjE,IAAMC,cAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AAE3D,QAAA,MAAM,GAAG,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE;AAC1B,YAAA,cAAc,EAAAH,gBAAA;AACd,YAAA,YAAY,EAAAC,cAAA;AACZ,YAAA,eAAe,EAAAC,iBAAA;AACf,YAAA,YAAY,EAAAC,cAAA;AACb,SAAA,CAAC,CAAC;AACJ,KAAA;AAED,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;AAGG;AACG,SAAU,gCAAgC,CAC9C,OAAiC,EAAA;AAEjC,IAAA,OAAO,UACL,CAAM,EACN,CAAM,EACN,YAAiB,EACjB,YAAiB,EACjB,QAAa,EACb,QAAa,EACb,KAAkB,EAAA;QAElB,OAAO,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AAC9B,KAAC,CAAC;AACJ,CAAC;AAED;;AAEG;AACG,SAAU,aAAa,CAAO,EAMP,EAAA;AAL3B,IAAA,IAAA,QAAQ,GAAA,EAAA,CAAA,QAAA,EACR,UAAU,GAAA,EAAA,CAAA,UAAA,EACV,WAAW,GAAA,EAAA,CAAA,WAAA,EACX,MAAM,GAAA,EAAA,CAAA,MAAA,EACN,MAAM,GAAA,EAAA,CAAA,MAAA,CAAA;AAEN,IAAA,IAAI,WAAW,EAAE;AACf,QAAA,OAAO,SAAS,OAAO,CAAO,CAAI,EAAE,CAAI,EAAA;YAChC,IAAA,EAAA,GACJ,WAAY,EAAE,EADR,aAA4C,EAA5C,KAAK,GAAG,EAAA,KAAA,KAAA,CAAA,GAAA,QAAQ,GAAG,IAAI,OAAO,EAAE,GAAG,SAAS,GAAA,EAAA,EAAE,IAAI,GAAA,EAAA,CAAA,IAC1C,CAAC;AAEjB,YAAA,OAAO,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE;AACtB,gBAAA,KAAK,EAAA,KAAA;AACL,gBAAA,MAAM,EAAA,MAAA;AACN,gBAAA,IAAI,EAAA,IAAA;AACJ,gBAAA,MAAM,EAAA,MAAA;AACQ,aAAA,CAAC,CAAC;AACpB,SAAC,CAAC;AACH,KAAA;AAED,IAAA,IAAI,QAAQ,EAAE;AACZ,QAAA,OAAO,SAAS,OAAO,CAAO,CAAI,EAAE,CAAI,EAAA;AACtC,YAAA,OAAO,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE;gBACtB,KAAK,EAAE,IAAI,OAAO,EAAE;AACpB,gBAAA,MAAM,EAAA,MAAA;AACN,gBAAA,IAAI,EAAE,SAAiB;AACvB,gBAAA,MAAM,EAAA,MAAA;AACQ,aAAA,CAAC,CAAC;AACpB,SAAC,CAAC;AACH,KAAA;AAED,IAAA,IAAM,KAAK,GAAG;AACZ,QAAA,KAAK,EAAE,SAAS;AAChB,QAAA,MAAM,EAAA,MAAA;AACN,QAAA,IAAI,EAAE,SAAS;AACf,QAAA,MAAM,EAAA,MAAA;KACQ,CAAC;AAEjB,IAAA,OAAO,SAAS,OAAO,CAAO,CAAI,EAAE,CAAI,EAAA;QACtC,OAAO,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AACjC,KAAC,CAAC;AACJ;;ACtUA;;AAEG;AACU,IAAA,SAAS,GAAG,iBAAiB,GAAG;AAE7C;;AAEG;AACI,IAAM,eAAe,GAAG,iBAAiB,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE;AAEnE;;AAEG;AACI,IAAM,iBAAiB,GAAG,iBAAiB,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE;AAEvE;;;AAGG;AACI,IAAM,uBAAuB,GAAG,iBAAiB,CAAC;AACvD,IAAA,QAAQ,EAAE,IAAI;AACd,IAAA,MAAM,EAAE,IAAI;AACb,CAAA,EAAE;AAEH;;AAEG;AACI,IAAM,YAAY,GAAG,iBAAiB,CAAC;AAC5C,IAAA,wBAAwB,EAAE,YAAA,EAAM,OAAA,kBAAkB,GAAA;AACnD,CAAA,EAAE;AAEH;;AAEG;AACI,IAAM,kBAAkB,GAAG,iBAAiB,CAAC;AAClD,IAAA,MAAM,EAAE,IAAI;AACZ,IAAA,wBAAwB,EAAE,YAAA,EAAM,OAAA,kBAAkB,GAAA;AACnD,CAAA,EAAE;AAEH;;AAEG;AACI,IAAM,oBAAoB,GAAG,iBAAiB,CAAC;AACpD,IAAA,QAAQ,EAAE,IAAI;AACd,IAAA,wBAAwB,EAAE,YAAA,EAAM,OAAA,kBAAkB,GAAA;AACnD,CAAA,EAAE;AAEH;;;AAGG;AACI,IAAM,0BAA0B,GAAG,iBAAiB,CAAC;AAC1D,IAAA,QAAQ,EAAE,IAAI;AACd,IAAA,wBAAwB,EAAE,YAAA,EAAM,OAAA,kBAAkB,GAAA;AAClD,IAAA,MAAM,EAAE,IAAI;AACb,CAAA,EAAE;AAEH;;;;;;;AAOG;AACG,SAAU,iBAAiB,CAC/B,OAA6C,EAAA;AAA7C,IAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAA6C,GAAA,EAAA,CAAA,EAAA;IAG3C,IAAA,EAAA,GAIE,OAAO,CAAA,QAJO,EAAhB,QAAQ,GAAG,EAAA,KAAA,KAAA,CAAA,GAAA,KAAK,GAAA,EAAA,EACU,8BAA8B,GAGtD,OAAO,CAAA,wBAH+C,EACxD,WAAW,GAET,OAAO,CAFE,WAAA,EACX,EACE,GAAA,OAAO,CADK,MAAA,EAAd,MAAM,GAAA,EAAA,KAAA,KAAA,CAAA,GAAG,KAAK,GAAA,EAAA,CACJ;AAEZ,IAAA,IAAM,MAAM,GAAG,8BAA8B,CAAO,OAAO,CAAC,CAAC;AAC7D,IAAA,IAAM,UAAU,GAAG,wBAAwB,CAAC,MAAM,CAAC,CAAC;IACpD,IAAM,MAAM,GAAG,8BAA8B;AAC3C,UAAE,8BAA8B,CAAC,UAAU,CAAC;AAC5C,UAAE,gCAAgC,CAAC,UAAU,CAAC,CAAC;AAEjD,IAAA,OAAO,aAAa,CAAC,EAAE,QAAQ,EAAA,QAAA,EAAE,UAAU,EAAA,UAAA,EAAE,WAAW,EAAA,WAAA,EAAE,MAAM,EAAA,MAAA,EAAE,MAAM,EAAA,MAAA,EAAE,CAAC,CAAC;AAC9E;;;;;;;;;;;;;"}