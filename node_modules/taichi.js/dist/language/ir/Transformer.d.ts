import { Guard } from './Builder';
import { AllocaStmt, ArgLoadStmt, AtomicLoadStmt, AtomicOpStmt, AtomicStoreStmt, BinaryOpStmt, Block, BuiltInInputStmt, BuiltInOutputStmt, CompositeExtractStmt, ConstStmt, ContinueStmt, DiscardStmt, FragmentDerivativeStmt, FragmentForStmt, FragmentInputStmt, GlobalLoadStmt, GlobalPtrStmt, GlobalStoreStmt, GlobalTemporaryLoadStmt, GlobalTemporaryStmt, GlobalTemporaryStoreStmt, IfStmt, IRModule, LocalLoadStmt, LocalStoreStmt, LoopIndexStmt, RandStmt, RangeForStmt, ReturnStmt, Stmt, TextureFunctionStmt, UnaryOpStmt, VertexForStmt, VertexInputStmt, VertexOutputStmt, WhileControlStmt, WhileStmt } from './Stmt';
import { IRVisitor } from './Visitor';
export declare class IRTransformer extends IRVisitor {
    guards: Guard[];
    module: IRModule;
    transform(module: IRModule): void;
    pushNewStmt(stmt: Stmt): Stmt;
    addGuard(block: Block): Guard;
    visitBlock(block: Block): void;
    visitConstStmt(stmt: ConstStmt): void;
    visitRangeForStmt(stmt: RangeForStmt): void;
    visitLoopIndexStmt(stmt: LoopIndexStmt): void;
    visitAllocaStmt(stmt: AllocaStmt): void;
    visitLocalLoadStmt(stmt: LocalLoadStmt): void;
    visitLocalStoreStmt(stmt: LocalStoreStmt): void;
    visitGlobalPtrStmt(stmt: GlobalPtrStmt): void;
    visitGlobalLoadStmt(stmt: GlobalLoadStmt): void;
    visitGlobalStoreStmt(stmt: GlobalStoreStmt): void;
    visitGlobalTemporaryStmt(stmt: GlobalTemporaryStmt): void;
    visitGlobalTemporaryLoadStmt(stmt: GlobalTemporaryLoadStmt): void;
    visitGlobalTemporaryStoreStmt(stmt: GlobalTemporaryStoreStmt): void;
    visitBinaryOpStmt(stmt: BinaryOpStmt): void;
    visitUnaryOpStmt(stmt: UnaryOpStmt): void;
    visitWhileStmt(stmt: WhileStmt): void;
    visitIfStmt(stmt: IfStmt): void;
    visitWhileControlStmt(stmt: WhileControlStmt): void;
    visitContinueStmt(stmt: ContinueStmt): void;
    visitArgLoadStmt(stmt: ArgLoadStmt): void;
    visitRandStmt(stmt: RandStmt): void;
    visitReturnStmt(stmt: ReturnStmt): void;
    visitAtomicOpStmt(stmt: AtomicOpStmt): void;
    visitAtomicLoadStmt(stmt: AtomicLoadStmt): void;
    visitAtomicStoreStmt(stmt: AtomicStoreStmt): void;
    visitVertexForStmt(stmt: VertexForStmt): void;
    visitFragmentForStmt(stmt: FragmentForStmt): void;
    visitVertexInputStmt(stmt: VertexInputStmt): void;
    visitVertexOutputStmt(stmt: VertexOutputStmt): void;
    visitFragmentInputStmt(stmt: FragmentInputStmt): void;
    visitBuiltInOutputStmt(stmt: BuiltInOutputStmt): void;
    visitBuiltInInputStmt(stmt: BuiltInInputStmt): void;
    visitFragmentDerivativeStmt(stmt: FragmentDerivativeStmt): void;
    visitDiscardStmt(stmt: DiscardStmt): void;
    visitTextureFunctionStmt(stmt: TextureFunctionStmt): void;
    visitCompositeExtractStmt(stmt: CompositeExtractStmt): void;
}
