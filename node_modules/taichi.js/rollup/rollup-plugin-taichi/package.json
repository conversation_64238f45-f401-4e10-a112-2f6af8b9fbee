{"name": "rollup-plugin-taichi", "version": "0.0.3", "description": "A rollup plugin that makes using taichi.js a bit easier", "homepage": "https://taichi-js.com", "repository": {"type": "git", "url": "https://github.com/AmesingFlank/taichi.js"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "./dist/rollup-plugin-taichi.umd.js", "module": "./dist/rollup-plugin-taichi.js", "scripts": {"build": "rollup --config rollup/rollup.config.mjs -w"}, "devDependencies": {"@rollup/plugin-commonjs": "^21.0.3", "@rollup/plugin-node-resolve": "^13.1.3", "@rollup/plugin-replace": "^4.0.0", "@rollup/plugin-typescript": "^8.3.1", "http-server": "^14.0.0", "rollup": "^2.70.1", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.31.2", "ts-loader": "^9.2.6", "typescript": "^4.7.3"}, "dependencies": {"tslib": "^2.4.0"}}