{"name": "taichi.js", "version": "0.0.36", "description": "Modern GPU Compute Framework in Javascript", "homepage": "https://taichi-js.com", "repository": {"type": "git", "url": "https://github.com/AmesingFlank/taichi.js"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "./dist/taichi.umd.js", "module": "./dist/taichi.js", "scripts": {"build_dev": "rollup --config rollup/rollup.config.dev.js -w", "build_prod": "rollup --config rollup/rollup.config.prod.js", "build": "npm run build_prod", "start": "http-server -a 127.0.0.1 -p 8080", "format": "prettier --config .prettierrc.json src/**/*.ts examples/**/*.js --write"}, "devDependencies": {"@rollup/plugin-commonjs": "^21.0.3", "@rollup/plugin-node-resolve": "^13.1.3", "@rollup/plugin-replace": "^4.0.0", "@rollup/plugin-typescript": "^8.3.1", "http-server": "^14.0.0", "prettier": "^2.8.8", "rollup": "^2.70.1", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.31.2", "ts-loader": "^9.2.6", "typescript": "^4.7.3"}, "dependencies": {"@loaders.gl/core": "^3.1.8", "@loaders.gl/gltf": "^3.1.8", "@webgpu/types": "^0.1.20", "gl-matrix": "^3.4.3", "tslib": "^2.4.0"}, "browser": {"fs": false, "child_process": false}, "keywords": ["taichi", "webgpu", "gpu", "gpgpu", "shader", "graphics", "simulation", "render"]}