{"version": 3, "file": "jerrypick.js", "sources": ["../src/index.js"], "sourcesContent": ["const pluck = (obj, keys) =>\n  Object.assign({}, ...keys.map(key => ({ [key]: obj[key] })));\n\nconst omit = (obj, keys) => {\n  const keySet = new Set(keys);\n\n  return Object.assign(\n    {},\n    ...Object.entries(obj)\n      .filter(([key]) => !keySet.has(key))\n      .map(([key, val]) => ({ [key]: val }))\n  );\n};\n\nexport { pluck, omit };\n"], "names": ["pluck", "obj", "keys", "Object", "assign", "map", "key", "omit", "keySet", "Set", "entries", "filter", "has", "val"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,MAAMA,KAAK,GAAG,SAARA,KAAK,CAAIC,GAAG,EAAEC,IAAI,EAAA;EAAA,EAAA,OACtBC,MAAM,CAACC,MAAM,CAAA,KAAA,CAAbD,MAAM,EAAQ,CAAA,EAAE,CAAA,CAAA,MAAA,CAAA,kBAAA,CAAKD,IAAI,CAACG,GAAG,CAAC,UAAAC,GAAG,EAAA;EAAA,IAAA,OAAA,eAAA,CAAA,EAAA,EAAQA,GAAG,EAAGL,GAAG,CAACK,GAAG,CAAC,CAAA,CAAA;EAAA,GAAG,CAAC,CAAC,CAAA,CAAA,CAAA;EAAA,EAAA;AAExDC,MAAAA,IAAI,GAAG,SAAPA,IAAI,CAAIN,GAAG,EAAEC,IAAI,EAAK;EAC1B,EAAA,IAAMM,MAAM,GAAG,IAAIC,GAAG,CAACP,IAAI,CAAC,CAAA;EAE5B,EAAA,OAAOC,MAAM,CAACC,MAAM,CAAbD,KAAAA,CAAAA,MAAM,GACX,EAAE,CACCA,CAAAA,MAAAA,CAAAA,kBAAAA,CAAAA,MAAM,CAACO,OAAO,CAACT,GAAG,CAAC,CACnBU,MAAM,CAAC,UAAA,KAAA,EAAA;EAAA,IAAA,IAAA,KAAA,GAAA,cAAA,CAAA,KAAA,EAAA,CAAA,CAAA;QAAEL,GAAG,GAAA,KAAA,CAAA,CAAA,CAAA,CAAA;EAAA,IAAA,OAAM,CAACE,MAAM,CAACI,GAAG,CAACN,GAAG,CAAC,CAAA;KAAC,CAAA,CACnCD,GAAG,CAAC,UAAA,KAAA,EAAA;EAAA,IAAA,IAAA,KAAA,GAAA,cAAA,CAAA,KAAA,EAAA,CAAA,CAAA;QAAEC,GAAG,GAAA,KAAA,CAAA,CAAA,CAAA;QAAEO,GAAG,GAAA,KAAA,CAAA,CAAA,CAAA,CAAA;MAAA,OAAUP,eAAAA,CAAAA,EAAAA,EAAAA,GAAG,EAAGO,GAAG,CAAA,CAAA;EAAA,GAAG,CAAC,CACzC,CAAA,CAAA,CAAA;EACH;;;;;;;;;"}