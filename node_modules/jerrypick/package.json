{"name": "jerrypick", "version": "1.1.2", "description": "Pluck and omit properties from a JS object", "type": "module", "jsdelivr": "dist/jerrypick.min.js", "unpkg": "dist/jerrypick.min.js", "main": "dist/jerrypick.mjs", "module": "dist/jerrypick.mjs", "types": "dist/jerrypick.d.ts", "exports": {"umd": "./dist/jerrypick.min.js", "default": "./dist/jerrypick.mjs"}, "repository": {"type": "git", "url": "git+https://github.com/vasturiano/jerrypick.git"}, "keywords": ["object", "properties", "pluck", "omit"], "author": {"name": "Vasco <PERSON>turiano", "url": "https://github.com/vasturiano"}, "license": "MIT", "bugs": {"url": "https://github.com/vasturiano/jerrypick/issues"}, "homepage": "https://github.com/vasturiano/jerrypick", "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -w -c", "prepare": "npm run build"}, "files": ["dist/**/*"], "dependencies": {}, "devDependencies": {"@babel/core": "^7.20.12", "@babel/preset-env": "^7.20.2", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-terser": "^0.4.0", "rimraf": "^4.1.2", "rollup": "^3.14.0", "rollup-plugin-dts": "^5.1.1", "typescript": "^4.9.5"}, "engines": {"node": ">=12"}}