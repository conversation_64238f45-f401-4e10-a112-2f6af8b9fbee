// Version 1.0.3 three-slippy-map-globe - https://github.com/vasturiano/three-slippy-map-globe
!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n(require("three")):"function"==typeof define&&define.amd?define(["three"],n):(t="undefined"!=typeof globalThis?globalThis:t||self).SlippyMapGlobe=n(t.THREE)}(this,(function(t){"use strict";function n(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=Array(n);e<n;e++)r[e]=t[e];return r}function e(t,n,e){if("function"==typeof t?t===n:t.has(n))return arguments.length<3?n:e;throw new TypeError("Private element is not present on this object")}function r(t,n,e){return n=h(n),function(t,n){if(n&&("object"==typeof n||"function"==typeof n))return n;if(void 0!==n)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,c()?Reflect.construct(n,[],h(t).constructor):n.apply(t,e))}function i(t,n){if(n.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function o(t,n){return t.get(e(t,n))}function a(t,n,e){i(t,n),n.set(t,e)}function s(t,n,r){return t.set(e(t,n),r),r}function u(t,n,e){return function(t,n){for(var e=0;e<n.length;e++){var r=n[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,g(r.key),r)}}(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function l(t,n,e){return(n=g(n))in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}function h(t){return h=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},h(t)}function c(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(c=function(){return!!t})()}function f(t,n){return f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,n){return t.__proto__=n,t},f(t,n)}function p(t,n){return function(t){if(Array.isArray(t))return t}(t)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,i,o,a,s=[],u=!0,l=!1;try{if(o=(e=e.call(t)).next,0===n);else for(;!(u=(r=o.call(e)).done)&&(s.push(r.value),s.length!==n);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(l)throw i}}return s}}(t,n)||y(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(t){return function(t){if(Array.isArray(t))return n(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||y(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(t){var n=function(t,n){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,n);if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==typeof n?n:n+""}function y(t,e){if(t){if("string"==typeof t)return n(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(t,e):void 0}}function v(t,n,e,r,i){if(isNaN(n)||isNaN(e)||isNaN(r))return t;var o,a,s,u,l,h,c,f,p,d,g,y,v=t._root,m={data:i},w=t._x0,b=t._y0,M=t._z0,x=t._x1,_=t._y1,N=t._z1;if(!v)return t._root=m,t;for(;v.length;)if((f=n>=(a=(w+x)/2))?w=a:x=a,(p=e>=(s=(b+_)/2))?b=s:_=s,(d=r>=(u=(M+N)/2))?M=u:N=u,o=v,!(v=v[g=d<<2|p<<1|f]))return o[g]=m,t;if(l=+t._x.call(null,v.data),h=+t._y.call(null,v.data),c=+t._z.call(null,v.data),n===l&&e===h&&r===c)return m.next=v,o?o[g]=m:t._root=m,t;do{o=o?o[g]=new Array(8):t._root=new Array(8),(f=n>=(a=(w+x)/2))?w=a:x=a,(p=e>=(s=(b+_)/2))?b=s:_=s,(d=r>=(u=(M+N)/2))?M=u:N=u}while((g=d<<2|p<<1|f)==(y=(c>=u)<<2|(h>=s)<<1|l>=a));return o[y]=v,o[g]=m,t}function m(t,n,e,r,i,o,a){this.node=t,this.x0=n,this.y0=e,this.z0=r,this.x1=i,this.y1=o,this.z1=a}const w=(t,n,e,r,i,o)=>Math.sqrt((t-r)**2+(n-i)**2+(e-o)**2);function b(t){return t[0]}function M(t){return t[1]}function x(t){return t[2]}function _(t,n,e,r){var i=new N(null==n?b:n,null==e?M:e,null==r?x:r,NaN,NaN,NaN,NaN,NaN,NaN);return null==t?i:i.addAll(t)}function N(t,n,e,r,i,o,a,s,u){this._x=t,this._y=n,this._z=e,this._x0=r,this._y0=i,this._z0=o,this._x1=a,this._y1=s,this._z1=u,this._root=void 0}function k(t){for(var n={data:t.data},e=n;t=t.next;)e=e.next={data:t.data};return n}var z=_.prototype=N.prototype;z.copy=function(){var t,n,e=new N(this._x,this._y,this._z,this._x0,this._y0,this._z0,this._x1,this._y1,this._z1),r=this._root;if(!r)return e;if(!r.length)return e._root=k(r),e;for(t=[{source:r,target:e._root=new Array(8)}];r=t.pop();)for(var i=0;i<8;++i)(n=r.source[i])&&(n.length?t.push({source:n,target:r.target[i]=new Array(8)}):r.target[i]=k(n));return e},z.add=function(t){const n=+this._x.call(null,t),e=+this._y.call(null,t),r=+this._z.call(null,t);return v(this.cover(n,e,r),n,e,r,t)},z.addAll=function(t){Array.isArray(t)||(t=Array.from(t));const n=t.length,e=new Float64Array(n),r=new Float64Array(n),i=new Float64Array(n);let o=1/0,a=1/0,s=1/0,u=-1/0,l=-1/0,h=-1/0;for(let c,f,p,d,g=0;g<n;++g)isNaN(f=+this._x.call(null,c=t[g]))||isNaN(p=+this._y.call(null,c))||isNaN(d=+this._z.call(null,c))||(e[g]=f,r[g]=p,i[g]=d,f<o&&(o=f),f>u&&(u=f),p<a&&(a=p),p>l&&(l=p),d<s&&(s=d),d>h&&(h=d));if(o>u||a>l||s>h)return this;this.cover(o,a,s).cover(u,l,h);for(let o=0;o<n;++o)v(this,e[o],r[o],i[o],t[o]);return this},z.cover=function(t,n,e){if(isNaN(t=+t)||isNaN(n=+n)||isNaN(e=+e))return this;var r=this._x0,i=this._y0,o=this._z0,a=this._x1,s=this._y1,u=this._z1;if(isNaN(r))a=(r=Math.floor(t))+1,s=(i=Math.floor(n))+1,u=(o=Math.floor(e))+1;else{for(var l,h,c=a-r||1,f=this._root;r>t||t>=a||i>n||n>=s||o>e||e>=u;)switch(h=(e<o)<<2|(n<i)<<1|t<r,(l=new Array(8))[h]=f,f=l,c*=2,h){case 0:a=r+c,s=i+c,u=o+c;break;case 1:r=a-c,s=i+c,u=o+c;break;case 2:a=r+c,i=s-c,u=o+c;break;case 3:r=a-c,i=s-c,u=o+c;break;case 4:a=r+c,s=i+c,o=u-c;break;case 5:r=a-c,s=i+c,o=u-c;break;case 6:a=r+c,i=s-c,o=u-c;break;case 7:r=a-c,i=s-c,o=u-c}this._root&&this._root.length&&(this._root=f)}return this._x0=r,this._y0=i,this._z0=o,this._x1=a,this._y1=s,this._z1=u,this},z.data=function(){var t=[];return this.visit((function(n){if(!n.length)do{t.push(n.data)}while(n=n.next)})),t},z.extent=function(t){return arguments.length?this.cover(+t[0][0],+t[0][1],+t[0][2]).cover(+t[1][0],+t[1][1],+t[1][2]):isNaN(this._x0)?void 0:[[this._x0,this._y0,this._z0],[this._x1,this._y1,this._z1]]},z.find=function(t,n,e,r){var i,o,a,s,u,l,h,c,f,p=this._x0,d=this._y0,g=this._z0,y=this._x1,v=this._y1,w=this._z1,b=[],M=this._root;for(M&&b.push(new m(M,p,d,g,y,v,w)),null==r?r=1/0:(p=t-r,d=n-r,g=e-r,y=t+r,v=n+r,w=e+r,r*=r);c=b.pop();)if(!(!(M=c.node)||(o=c.x0)>y||(a=c.y0)>v||(s=c.z0)>w||(u=c.x1)<p||(l=c.y1)<d||(h=c.z1)<g))if(M.length){var x=(o+u)/2,_=(a+l)/2,N=(s+h)/2;b.push(new m(M[7],x,_,N,u,l,h),new m(M[6],o,_,N,x,l,h),new m(M[5],x,a,N,u,_,h),new m(M[4],o,a,N,x,_,h),new m(M[3],x,_,s,u,l,N),new m(M[2],o,_,s,x,l,N),new m(M[1],x,a,s,u,_,N),new m(M[0],o,a,s,x,_,N)),(f=(e>=N)<<2|(n>=_)<<1|t>=x)&&(c=b[b.length-1],b[b.length-1]=b[b.length-1-f],b[b.length-1-f]=c)}else{var k=t-+this._x.call(null,M.data),z=n-+this._y.call(null,M.data),j=e-+this._z.call(null,M.data),$=k*k+z*z+j*j;if($<r){var A=Math.sqrt(r=$);p=t-A,d=n-A,g=e-A,y=t+A,v=n+A,w=e+A,i=M.data}}return i},z.findAllWithinRadius=function(t,n,e,r){const i=[],o=t-r,a=n-r,s=e-r,u=t+r,l=n+r,h=e+r;return this.visit(((c,f,p,d,g,y,v)=>{if(!c.length)do{const o=c.data;w(t,n,e,this._x(o),this._y(o),this._z(o))<=r&&i.push(o)}while(c=c.next);return f>u||p>l||d>h||g<o||y<a||v<s})),i},z.remove=function(t){if(isNaN(o=+this._x.call(null,t))||isNaN(a=+this._y.call(null,t))||isNaN(s=+this._z.call(null,t)))return this;var n,e,r,i,o,a,s,u,l,h,c,f,p,d,g,y=this._root,v=this._x0,m=this._y0,w=this._z0,b=this._x1,M=this._y1,x=this._z1;if(!y)return this;if(y.length)for(;;){if((c=o>=(u=(v+b)/2))?v=u:b=u,(f=a>=(l=(m+M)/2))?m=l:M=l,(p=s>=(h=(w+x)/2))?w=h:x=h,n=y,!(y=y[d=p<<2|f<<1|c]))return this;if(!y.length)break;(n[d+1&7]||n[d+2&7]||n[d+3&7]||n[d+4&7]||n[d+5&7]||n[d+6&7]||n[d+7&7])&&(e=n,g=d)}for(;y.data!==t;)if(r=y,!(y=y.next))return this;return(i=y.next)&&delete y.next,r?(i?r.next=i:delete r.next,this):n?(i?n[d]=i:delete n[d],(y=n[0]||n[1]||n[2]||n[3]||n[4]||n[5]||n[6]||n[7])&&y===(n[7]||n[6]||n[5]||n[4]||n[3]||n[2]||n[1]||n[0])&&!y.length&&(e?e[g]=y:this._root=y),this):(this._root=i,this)},z.removeAll=function(t){for(var n=0,e=t.length;n<e;++n)this.remove(t[n]);return this},z.root=function(){return this._root},z.size=function(){var t=0;return this.visit((function(n){if(!n.length)do{++t}while(n=n.next)})),t},z.visit=function(t){var n,e,r,i,o,a,s,u,l=[],h=this._root;for(h&&l.push(new m(h,this._x0,this._y0,this._z0,this._x1,this._y1,this._z1));n=l.pop();)if(!t(h=n.node,r=n.x0,i=n.y0,o=n.z0,a=n.x1,s=n.y1,u=n.z1)&&h.length){var c=(r+a)/2,f=(i+s)/2,p=(o+u)/2;(e=h[7])&&l.push(new m(e,c,f,p,a,s,u)),(e=h[6])&&l.push(new m(e,r,f,p,c,s,u)),(e=h[5])&&l.push(new m(e,c,i,p,a,f,u)),(e=h[4])&&l.push(new m(e,r,i,p,c,f,u)),(e=h[3])&&l.push(new m(e,c,f,o,a,s,p)),(e=h[2])&&l.push(new m(e,r,f,o,c,s,p)),(e=h[1])&&l.push(new m(e,c,i,o,a,f,p)),(e=h[0])&&l.push(new m(e,r,i,o,c,f,p))}return this},z.visitAfter=function(t){var n,e=[],r=[];for(this._root&&e.push(new m(this._root,this._x0,this._y0,this._z0,this._x1,this._y1,this._z1));n=e.pop();){var i=n.node;if(i.length){var o,a=n.x0,s=n.y0,u=n.z0,l=n.x1,h=n.y1,c=n.z1,f=(a+l)/2,p=(s+h)/2,d=(u+c)/2;(o=i[0])&&e.push(new m(o,a,s,u,f,p,d)),(o=i[1])&&e.push(new m(o,f,s,u,l,p,d)),(o=i[2])&&e.push(new m(o,a,p,u,f,h,d)),(o=i[3])&&e.push(new m(o,f,p,u,l,h,d)),(o=i[4])&&e.push(new m(o,a,s,d,f,p,c)),(o=i[5])&&e.push(new m(o,f,s,d,l,p,c)),(o=i[6])&&e.push(new m(o,a,p,d,f,h,c)),(o=i[7])&&e.push(new m(o,f,p,d,l,h,c))}r.push(n)}for(;n=r.pop();)t(n.node,n.x0,n.y0,n.z0,n.x1,n.y1,n.z1);return this},z.x=function(t){return arguments.length?(this._x=t,this):this._x},z.y=function(t){return arguments.length?(this._y=t,this):this._y},z.z=function(t){return arguments.length?(this._z=t,this):this._z};var j=function(t){t instanceof Array?t.forEach(j):(t.map&&t.map.dispose(),t.dispose())},$=function(t){t.geometry&&t.geometry.dispose(),t.material&&j(t.material),t.texture&&t.texture.dispose(),t.children&&t.children.forEach($)},A=function(t){if(t&&t.children)for(;t.children.length;){var n=t.children[0];t.remove(n),$(n)}};function P(t,n,e){var r=(90-t)*Math.PI/180,i=(90-n)*Math.PI/180;return{x:e*Math.sin(r)*Math.cos(i),y:e*Math.cos(r),z:e*Math.sin(r)*Math.sin(i)}}function E(t){return t*Math.PI/180}function O(t,n){return null==t||null==n?NaN:t<n?-1:t>n?1:t>=n?0:NaN}function S(t,n){return null==t||null==n?NaN:n<t?-1:n>t?1:n>=t?0:NaN}function I(t){let n,e,r;function i(t,r,i=0,o=t.length){if(i<o){if(0!==n(r,r))return o;do{const n=i+o>>>1;e(t[n],r)<0?i=n+1:o=n}while(i<o)}return i}return 2!==t.length?(n=O,e=(n,e)=>O(t(n),e),r=(n,e)=>t(n)-e):(n=t===O||t===S?t:R,e=t,r=t),{left:i,center:function(t,n,e=0,o=t.length){const a=i(t,n,e,o-1);return a>e&&r(t[a-1],n)>-r(t[a],n)?a-1:a},right:function(t,r,i=0,o=t.length){if(i<o){if(0!==n(r,r))return o;do{const n=i+o>>>1;e(t[n],r)<=0?i=n+1:o=n}while(i<o)}return i}}}function R(){return 0}const L=I(O).right;I((function(t){return null===t?NaN:+t})).center;const T=Math.sqrt(50),W=Math.sqrt(10),q=Math.sqrt(2);function U(t,n,e){const r=(n-t)/Math.max(0,e),i=Math.floor(Math.log10(r)),o=r/Math.pow(10,i),a=o>=T?10:o>=W?5:o>=q?2:1;let s,u,l;return i<0?(l=Math.pow(10,-i)/a,s=Math.round(t*l),u=Math.round(n*l),s/l<t&&++s,u/l>n&&--u,l=-l):(l=Math.pow(10,i)*a,s=Math.round(t/l),u=Math.round(n/l),s*l<t&&++s,u*l>n&&--u),u<s&&.5<=e&&e<2?U(t,n,2*e):[s,u,l]}function H(t,n,e){return U(t=+t,n=+n,e=+e)[2]}function C(t,n){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(n).domain(t)}return this}function F(t,n,e){t.prototype=n.prototype=e,e.constructor=t}function G(t,n){var e=Object.create(t.prototype);for(var r in n)e[r]=n[r];return e}function B(){}var D=.7,V=1/D,X="\\s*([+-]?\\d+)\\s*",Y="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Z="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",J=/^#([0-9a-f]{3,8})$/,K=new RegExp(`^rgb\\(${X},${X},${X}\\)$`),Q=new RegExp(`^rgb\\(${Z},${Z},${Z}\\)$`),tt=new RegExp(`^rgba\\(${X},${X},${X},${Y}\\)$`),nt=new RegExp(`^rgba\\(${Z},${Z},${Z},${Y}\\)$`),et=new RegExp(`^hsl\\(${Y},${Z},${Z}\\)$`),rt=new RegExp(`^hsla\\(${Y},${Z},${Z},${Y}\\)$`),it={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function ot(){return this.rgb().formatHex()}function at(){return this.rgb().formatRgb()}function st(t){var n,e;return t=(t+"").trim().toLowerCase(),(n=J.exec(t))?(e=n[1].length,n=parseInt(n[1],16),6===e?ut(n):3===e?new ct(n>>8&15|n>>4&240,n>>4&15|240&n,(15&n)<<4|15&n,1):8===e?lt(n>>24&255,n>>16&255,n>>8&255,(255&n)/255):4===e?lt(n>>12&15|n>>8&240,n>>8&15|n>>4&240,n>>4&15|240&n,((15&n)<<4|15&n)/255):null):(n=K.exec(t))?new ct(n[1],n[2],n[3],1):(n=Q.exec(t))?new ct(255*n[1]/100,255*n[2]/100,255*n[3]/100,1):(n=tt.exec(t))?lt(n[1],n[2],n[3],n[4]):(n=nt.exec(t))?lt(255*n[1]/100,255*n[2]/100,255*n[3]/100,n[4]):(n=et.exec(t))?vt(n[1],n[2]/100,n[3]/100,1):(n=rt.exec(t))?vt(n[1],n[2]/100,n[3]/100,n[4]):it.hasOwnProperty(t)?ut(it[t]):"transparent"===t?new ct(NaN,NaN,NaN,0):null}function ut(t){return new ct(t>>16&255,t>>8&255,255&t,1)}function lt(t,n,e,r){return r<=0&&(t=n=e=NaN),new ct(t,n,e,r)}function ht(t,n,e,r){return 1===arguments.length?((i=t)instanceof B||(i=st(i)),i?new ct((i=i.rgb()).r,i.g,i.b,i.opacity):new ct):new ct(t,n,e,null==r?1:r);var i}function ct(t,n,e,r){this.r=+t,this.g=+n,this.b=+e,this.opacity=+r}function ft(){return`#${yt(this.r)}${yt(this.g)}${yt(this.b)}`}function pt(){const t=dt(this.opacity);return`${1===t?"rgb(":"rgba("}${gt(this.r)}, ${gt(this.g)}, ${gt(this.b)}${1===t?")":`, ${t})`}`}function dt(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function gt(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function yt(t){return((t=gt(t))<16?"0":"")+t.toString(16)}function vt(t,n,e,r){return r<=0?t=n=e=NaN:e<=0||e>=1?t=n=NaN:n<=0&&(t=NaN),new wt(t,n,e,r)}function mt(t){if(t instanceof wt)return new wt(t.h,t.s,t.l,t.opacity);if(t instanceof B||(t=st(t)),!t)return new wt;if(t instanceof wt)return t;var n=(t=t.rgb()).r/255,e=t.g/255,r=t.b/255,i=Math.min(n,e,r),o=Math.max(n,e,r),a=NaN,s=o-i,u=(o+i)/2;return s?(a=n===o?(e-r)/s+6*(e<r):e===o?(r-n)/s+2:(n-e)/s+4,s/=u<.5?o+i:2-o-i,a*=60):s=u>0&&u<1?0:a,new wt(a,s,u,t.opacity)}function wt(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}function bt(t){return(t=(t||0)%360)<0?t+360:t}function Mt(t){return Math.max(0,Math.min(1,t||0))}function xt(t,n,e){return 255*(t<60?n+(e-n)*t/60:t<180?e:t<240?n+(e-n)*(240-t)/60:n)}F(B,st,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:ot,formatHex:ot,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return mt(this).formatHsl()},formatRgb:at,toString:at}),F(ct,ht,G(B,{brighter(t){return t=null==t?V:Math.pow(V,t),new ct(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?D:Math.pow(D,t),new ct(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new ct(gt(this.r),gt(this.g),gt(this.b),dt(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:ft,formatHex:ft,formatHex8:function(){return`#${yt(this.r)}${yt(this.g)}${yt(this.b)}${yt(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:pt,toString:pt})),F(wt,(function(t,n,e,r){return 1===arguments.length?mt(t):new wt(t,n,e,null==r?1:r)}),G(B,{brighter(t){return t=null==t?V:Math.pow(V,t),new wt(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?D:Math.pow(D,t),new wt(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+360*(this.h<0),n=isNaN(t)||isNaN(this.s)?0:this.s,e=this.l,r=e+(e<.5?e:1-e)*n,i=2*e-r;return new ct(xt(t>=240?t-240:t+120,i,r),xt(t,i,r),xt(t<120?t+240:t-120,i,r),this.opacity)},clamp(){return new wt(bt(this.h),Mt(this.s),Mt(this.l),dt(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=dt(this.opacity);return`${1===t?"hsl(":"hsla("}${bt(this.h)}, ${100*Mt(this.s)}%, ${100*Mt(this.l)}%${1===t?")":`, ${t})`}`}}));var _t=t=>()=>t;function Nt(t){return 1==(t=+t)?kt:function(n,e){return e-n?function(t,n,e){return t=Math.pow(t,e),n=Math.pow(n,e)-t,e=1/e,function(r){return Math.pow(t+r*n,e)}}(n,e,t):_t(isNaN(n)?e:n)}}function kt(t,n){var e=n-t;return e?function(t,n){return function(e){return t+e*n}}(t,e):_t(isNaN(t)?n:t)}var zt=function t(n){var e=Nt(n);function r(t,n){var r=e((t=ht(t)).r,(n=ht(n)).r),i=e(t.g,n.g),o=e(t.b,n.b),a=kt(t.opacity,n.opacity);return function(n){return t.r=r(n),t.g=i(n),t.b=o(n),t.opacity=a(n),t+""}}return r.gamma=t,r}(1);function jt(t,n){n||(n=[]);var e,r=t?Math.min(n.length,t.length):0,i=n.slice();return function(o){for(e=0;e<r;++e)i[e]=t[e]*(1-o)+n[e]*o;return i}}function $t(t,n){var e,r=n?n.length:0,i=t?Math.min(r,t.length):0,o=new Array(i),a=new Array(r);for(e=0;e<i;++e)o[e]=Rt(t[e],n[e]);for(;e<r;++e)a[e]=n[e];return function(t){for(e=0;e<i;++e)a[e]=o[e](t);return a}}function At(t,n){var e=new Date;return t=+t,n=+n,function(r){return e.setTime(t*(1-r)+n*r),e}}function Pt(t,n){return t=+t,n=+n,function(e){return t*(1-e)+n*e}}function Et(t,n){var e,r={},i={};for(e in null!==t&&"object"==typeof t||(t={}),null!==n&&"object"==typeof n||(n={}),n)e in t?r[e]=Rt(t[e],n[e]):i[e]=n[e];return function(t){for(e in r)i[e]=r[e](t);return i}}var Ot=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,St=new RegExp(Ot.source,"g");function It(t,n){var e,r,i,o=Ot.lastIndex=St.lastIndex=0,a=-1,s=[],u=[];for(t+="",n+="";(e=Ot.exec(t))&&(r=St.exec(n));)(i=r.index)>o&&(i=n.slice(o,i),s[a]?s[a]+=i:s[++a]=i),(e=e[0])===(r=r[0])?s[a]?s[a]+=r:s[++a]=r:(s[++a]=null,u.push({i:a,x:Pt(e,r)})),o=St.lastIndex;return o<n.length&&(i=n.slice(o),s[a]?s[a]+=i:s[++a]=i),s.length<2?u[0]?function(t){return function(n){return t(n)+""}}(u[0].x):function(t){return function(){return t}}(n):(n=u.length,function(t){for(var e,r=0;r<n;++r)s[(e=u[r]).i]=e.x(t);return s.join("")})}function Rt(t,n){var e,r,i=typeof n;return null==n||"boolean"===i?_t(n):("number"===i?Pt:"string"===i?(e=st(n))?(n=e,zt):It:n instanceof st?zt:n instanceof Date?At:(r=n,!ArrayBuffer.isView(r)||r instanceof DataView?Array.isArray(n)?$t:"function"!=typeof n.valueOf&&"function"!=typeof n.toString||isNaN(n)?Et:Pt:jt))(t,n)}function Lt(t,n){return t=+t,n=+n,function(e){return Math.round(t*(1-e)+n*e)}}function Tt(t){return+t}var Wt=[0,1];function qt(t){return t}function Ut(t,n){return(n-=t=+t)?function(e){return(e-t)/n}:(e=isNaN(n)?NaN:.5,function(){return e});var e}function Ht(t,n,e){var r=t[0],i=t[1],o=n[0],a=n[1];return i<r?(r=Ut(i,r),o=e(a,o)):(r=Ut(r,i),o=e(o,a)),function(t){return o(r(t))}}function Ct(t,n,e){var r=Math.min(t.length,n.length)-1,i=new Array(r),o=new Array(r),a=-1;for(t[r]<t[0]&&(t=t.slice().reverse(),n=n.slice().reverse());++a<r;)i[a]=Ut(t[a],t[a+1]),o[a]=e(n[a],n[a+1]);return function(n){var e=L(t,n,1,r)-1;return o[e](i[e](n))}}function Ft(){var t,n,e,r,i,o,a=Wt,s=Wt,u=Rt,l=qt;function h(){var t,n,e,u=Math.min(a.length,s.length);return l!==qt&&(t=a[0],n=a[u-1],t>n&&(e=t,t=n,n=e),l=function(e){return Math.max(t,Math.min(n,e))}),r=u>2?Ct:Ht,i=o=null,c}function c(n){return null==n||isNaN(n=+n)?e:(i||(i=r(a.map(t),s,u)))(t(l(n)))}return c.invert=function(e){return l(n((o||(o=r(s,a.map(t),Pt)))(e)))},c.domain=function(t){return arguments.length?(a=Array.from(t,Tt),h()):a.slice()},c.range=function(t){return arguments.length?(s=Array.from(t),h()):s.slice()},c.rangeRound=function(t){return s=Array.from(t),u=Lt,h()},c.clamp=function(t){return arguments.length?(l=!!t||qt,h()):l!==qt},c.interpolate=function(t){return arguments.length?(u=t,h()):u},c.unknown=function(t){return arguments.length?(e=t,c):e},function(e,r){return t=e,n=r,h()}}function Gt(t,n){if((e=(t=n?t.toExponential(n-1):t.toExponential()).indexOf("e"))<0)return null;var e,r=t.slice(0,e);return[r.length>1?r[0]+r.slice(2):r,+t.slice(e+1)]}function Bt(t){return(t=Gt(Math.abs(t)))?t[1]:NaN}var Dt,Vt=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Xt(t){if(!(n=Vt.exec(t)))throw new Error("invalid format: "+t);var n;return new Yt({fill:n[1],align:n[2],sign:n[3],symbol:n[4],zero:n[5],width:n[6],comma:n[7],precision:n[8]&&n[8].slice(1),trim:n[9],type:n[10]})}function Yt(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function Zt(t,n){var e=Gt(t,n);if(!e)return t+"";var r=e[0],i=e[1];return i<0?"0."+new Array(-i).join("0")+r:r.length>i+1?r.slice(0,i+1)+"."+r.slice(i+1):r+new Array(i-r.length+2).join("0")}Xt.prototype=Yt.prototype,Yt.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};var Jt={"%":(t,n)=>(100*t).toFixed(n),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,n)=>t.toExponential(n),f:(t,n)=>t.toFixed(n),g:(t,n)=>t.toPrecision(n),o:t=>Math.round(t).toString(8),p:(t,n)=>Zt(100*t,n),r:Zt,s:function(t,n){var e=Gt(t,n);if(!e)return t+"";var r=e[0],i=e[1],o=i-(Dt=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,a=r.length;return o===a?r:o>a?r+new Array(o-a+1).join("0"):o>0?r.slice(0,o)+"."+r.slice(o):"0."+new Array(1-o).join("0")+Gt(t,Math.max(0,n+o-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function Kt(t){return t}var Qt,tn,nn,en=Array.prototype.map,rn=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function on(t){var n,e,r=void 0===t.grouping||void 0===t.thousands?Kt:(n=en.call(t.grouping,Number),e=t.thousands+"",function(t,r){for(var i=t.length,o=[],a=0,s=n[0],u=0;i>0&&s>0&&(u+s+1>r&&(s=Math.max(1,r-u)),o.push(t.substring(i-=s,i+s)),!((u+=s+1)>r));)s=n[a=(a+1)%n.length];return o.reverse().join(e)}),i=void 0===t.currency?"":t.currency[0]+"",o=void 0===t.currency?"":t.currency[1]+"",a=void 0===t.decimal?".":t.decimal+"",s=void 0===t.numerals?Kt:function(t){return function(n){return n.replace(/[0-9]/g,(function(n){return t[+n]}))}}(en.call(t.numerals,String)),u=void 0===t.percent?"%":t.percent+"",l=void 0===t.minus?"−":t.minus+"",h=void 0===t.nan?"NaN":t.nan+"";function c(t){var n=(t=Xt(t)).fill,e=t.align,c=t.sign,f=t.symbol,p=t.zero,d=t.width,g=t.comma,y=t.precision,v=t.trim,m=t.type;"n"===m?(g=!0,m="g"):Jt[m]||(void 0===y&&(y=12),v=!0,m="g"),(p||"0"===n&&"="===e)&&(p=!0,n="0",e="=");var w="$"===f?i:"#"===f&&/[boxX]/.test(m)?"0"+m.toLowerCase():"",b="$"===f?o:/[%p]/.test(m)?u:"",M=Jt[m],x=/[defgprs%]/.test(m);function _(t){var i,o,u,f=w,_=b;if("c"===m)_=M(t)+_,t="";else{var N=(t=+t)<0||1/t<0;if(t=isNaN(t)?h:M(Math.abs(t),y),v&&(t=function(t){t:for(var n,e=t.length,r=1,i=-1;r<e;++r)switch(t[r]){case".":i=n=r;break;case"0":0===i&&(i=r),n=r;break;default:if(!+t[r])break t;i>0&&(i=0)}return i>0?t.slice(0,i)+t.slice(n+1):t}(t)),N&&0==+t&&"+"!==c&&(N=!1),f=(N?"("===c?c:l:"-"===c||"("===c?"":c)+f,_=("s"===m?rn[8+Dt/3]:"")+_+(N&&"("===c?")":""),x)for(i=-1,o=t.length;++i<o;)if(48>(u=t.charCodeAt(i))||u>57){_=(46===u?a+t.slice(i+1):t.slice(i))+_,t=t.slice(0,i);break}}g&&!p&&(t=r(t,1/0));var k=f.length+t.length+_.length,z=k<d?new Array(d-k+1).join(n):"";switch(g&&p&&(t=r(z+t,z.length?d-_.length:1/0),z=""),e){case"<":t=f+t+_+z;break;case"=":t=f+z+t+_;break;case"^":t=z.slice(0,k=z.length>>1)+f+t+_+z.slice(k);break;default:t=z+f+t+_}return s(t)}return y=void 0===y?6:/[gprs]/.test(m)?Math.max(1,Math.min(21,y)):Math.max(0,Math.min(20,y)),_.toString=function(){return t+""},_}return{format:c,formatPrefix:function(t,n){var e=c(((t=Xt(t)).type="f",t)),r=3*Math.max(-8,Math.min(8,Math.floor(Bt(n)/3))),i=Math.pow(10,-r),o=rn[8+r/3];return function(t){return e(i*t)+o}}}}function an(t,n,e,r){var i,o=function(t,n,e){e=+e;const r=(n=+n)<(t=+t),i=r?H(n,t,e):H(t,n,e);return(r?-1:1)*(i<0?1/-i:i)}(t,n,e);switch((r=Xt(null==r?",f":r)).type){case"s":var a=Math.max(Math.abs(t),Math.abs(n));return null!=r.precision||isNaN(i=function(t,n){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(Bt(n)/3)))-Bt(Math.abs(t)))}(o,a))||(r.precision=i),nn(r,a);case"":case"e":case"g":case"p":case"r":null!=r.precision||isNaN(i=function(t,n){return t=Math.abs(t),n=Math.abs(n)-t,Math.max(0,Bt(n)-Bt(t))+1}(o,Math.max(Math.abs(t),Math.abs(n))))||(r.precision=i-("e"===r.type));break;case"f":case"%":null!=r.precision||isNaN(i=function(t){return Math.max(0,-Bt(Math.abs(t)))}(o))||(r.precision=i-2*("%"===r.type))}return tn(r)}function sn(t){var n=t.domain;return t.ticks=function(t){var e=n();return function(t,n,e){if(!((e=+e)>0))return[];if((t=+t)==(n=+n))return[t];const r=n<t,[i,o,a]=r?U(n,t,e):U(t,n,e);if(!(o>=i))return[];const s=o-i+1,u=new Array(s);if(r)if(a<0)for(let t=0;t<s;++t)u[t]=(o-t)/-a;else for(let t=0;t<s;++t)u[t]=(o-t)*a;else if(a<0)for(let t=0;t<s;++t)u[t]=(i+t)/-a;else for(let t=0;t<s;++t)u[t]=(i+t)*a;return u}(e[0],e[e.length-1],null==t?10:t)},t.tickFormat=function(t,e){var r=n();return an(r[0],r[r.length-1],null==t?10:t,e)},t.nice=function(e){null==e&&(e=10);var r,i,o=n(),a=0,s=o.length-1,u=o[a],l=o[s],h=10;for(l<u&&(i=u,u=l,l=i,i=a,a=s,s=i);h-- >0;){if((i=H(u,l,e))===r)return o[a]=u,o[s]=l,n(o);if(i>0)u=Math.floor(u/i)*i,l=Math.ceil(l/i)*i;else{if(!(i<0))break;u=Math.ceil(u*i)/i,l=Math.floor(l*i)/i}r=i}return t},t}function un(){var t=Ft()(qt,qt);return t.copy=function(){return n=t,un().domain(n.domain()).range(n.range()).interpolate(n.interpolate()).clamp(n.clamp()).unknown(n.unknown());var n},C.apply(t,arguments),sn(t)}Qt=on({thousands:",",grouping:[3],currency:["$",""]}),tn=Qt.format,nn=Qt.formatPrefix;var ln=Math.PI/2,hn=Math.atan,cn=Math.exp,fn=Math.log,pn=Math.tan;function dn(t,n){return[t,fn(pn((ln+n)/2))]}dn.invert=function(t,n){return[t,2*hn(cn(n))-ln]};var gn=function(t){return 1-(dn(0,(.5-t)*Math.PI)[1]/Math.PI+1)/2},yn=function(t){return Math.max(0,Math.min(1,gn(t)))},vn=function(t){return.5-dn.invert(0,(2*(1-t)-1)*Math.PI)[1]/Math.PI},mn=function(t,n,e,r){var i=Math.pow(2,t),o=Math.max(0,Math.min(i-1,Math.floor((e+180)*i/360))),a=(90-r)/180;return n&&(a=Math.max(0,Math.min(1,gn(a)))),[o,Math.floor(a*i)]},wn=function(t,n){for(var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4?arguments[4]:void 0,o=arguments.length>5?arguments[5]:void 0,a=[],s=Math.pow(2,t),u=360/s,l=180/s,h=void 0===i?s-1:i,c=void 0===o?s-1:o,f=e,p=Math.min(s-1,h);f<=p;f++)for(var d=r,g=Math.min(s-1,c);d<=g;d++){var y=d,v=l;if(n)y=0===d?d:vn(d/s)*s,v=180*((d+1===s?d+1:vn((d+1)/s)*s)-y)/s;var m=(f+.5)*u-180,w=90-(180*y/s+v/2),b=v;a.push({x:f,y:d,lng:m,lat:w,latLen:b})}return a},bn=new WeakMap,Mn=new WeakMap,xn=new WeakMap,_n=new WeakMap,Nn=new WeakMap,kn=new WeakMap,zn=new WeakMap,jn=new WeakMap,$n=new WeakSet,An=function(n){function h(n){var e,u,c,f=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},p=f.tileUrl,g=f.minLevel,y=void 0===g?0:g,v=f.maxLevel,m=void 0===v?17:v,w=f.mercatorProjection,b=void 0===w||w;return function(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}(this,h),e=r(this,h),i(u=e,c=$n),c.add(u),a(e,bn,void 0),a(e,Mn,void 0),a(e,xn,void 0),a(e,_n,void 0),a(e,Nn,{}),a(e,kn,void 0),a(e,zn,void 0),a(e,jn,void 0),l(e,"minLevel",void 0),l(e,"maxLevel",void 0),l(e,"thresholds",d(new Array(30)).map((function(t,n){return 8/Math.pow(2,n)}))),l(e,"curvatureResolution",5),l(e,"tileMargin",0),l(e,"clearTiles",(function(){Object.values(o(Nn,e)).forEach((function(t){t.forEach((function(t){t.obj&&(e.remove(t.obj),A(t.obj),delete t.obj)}))})),s(Nn,e,{})})),s(bn,e,n),e.tileUrl=p,s(Mn,e,b),e.minLevel=y,e.maxLevel=m,e.level=0,e.add(s(jn,e,new t.Mesh(new t.SphereGeometry(.99*o(bn,e),180,90),new t.MeshBasicMaterial({color:0})))),o(jn,e).visible=!1,o(jn,e).material.polygonOffset=!0,o(jn,e).material.polygonOffsetUnits=3,o(jn,e).material.polygonOffsetFactor=1,e}return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),n&&f(t,n)}(h,n),u(h,[{key:"tileUrl",get:function(){return o(xn,this)},set:function(t){s(xn,this,t),this.updatePov(o(zn,this))}},{key:"level",get:function(){return o(_n,this)},set:function(t){var n=this;o(Nn,this)[t]||e($n,this,Pn).call(this,t);var r=o(_n,this);if(s(_n,this,t),t!==r&&void 0!==r){if(o(jn,this).visible=t>0,o(Nn,this)[t].forEach((function(t){return t.obj&&(t.obj.material.depthWrite=!0)})),r<t&&o(Nn,this)[r].forEach((function(t){return t.obj&&(t.obj.material.depthWrite=!1)})),r>t)for(var i=t+1;i<=r;i++)o(Nn,this)[i]&&o(Nn,this)[i].forEach((function(t){t.obj&&(n.remove(t.obj),A(t.obj),delete t.obj)}));e($n,this,En).call(this)}}},{key:"updatePov",value:function(n){var r,i=this;if(n&&n instanceof t.Camera&&(s(zn,this,n),s(kn,this,(function(e){if(!e.hullPnts){var a=360/Math.pow(2,i.level),s=e.lng,u=e.lat,l=e.latLen,h=s-a/2,c=s+a/2,f=u-l/2,d=u+l/2;e.hullPnts=[[u,s],[f,h],[d,h],[f,c],[d,c]].map((function(t){var n=p(t,2);return P(n[0],n[1],o(bn,i))})).map((function(n){var e=n.x,r=n.y,i=n.z;return new t.Vector3(e,r,i)}))}return r||(r=new t.Frustum,n.updateMatrix(),n.updateMatrixWorld(),r.setFromProjectionMatrix((new t.Matrix4).multiplyMatrices(n.projectionMatrix,n.matrixWorldInverse))),e.hullPnts.some((function(t){return r.containsPoint(t.clone().applyMatrix4(i.matrixWorld))}))})),this.tileUrl)){var a=(n.position.clone().distanceTo(this.getWorldPosition(new t.Vector3))-o(bn,this))/o(bn,this),u=this.thresholds.findIndex((function(t){return t&&t<=a}));this.level=Math.min(this.maxLevel,Math.max(this.minLevel,u<0?this.thresholds.length:u)),e($n,this,En).call(this)}}}])}(t.Group);function Pn(t){var n=this;if(t>7)o(Nn,this)[t]=[];else{var e=o(Nn,this)[t]=wn(t,o(Mn,this));e.forEach((function(t){return t.centroid=P(t.lat,t.lng,o(bn,n))})),e.octree=_().x((function(t){return t.centroid.x})).y((function(t){return t.centroid.y})).z((function(t){return t.centroid.z})).addAll(e)}}function En(){var n=this;if(this.tileUrl&&void 0!==this.level&&o(Nn,this).hasOwnProperty(this.level)&&(o(kn,this)||!(this.level>6))){var e=o(Nn,this)[this.level];if(o(zn,this)){var r=this.worldToLocal(o(zn,this).position.clone());if(e.octree){var i,a=this.worldToLocal(o(zn,this).position.clone()),s=3*(a.length()-o(bn,this));e=(i=e.octree).findAllWithinRadius.apply(i,d(a).concat([s]))}else{var u=function(t){var n=t.x,e=t.y,r=t.z,i=Math.sqrt(n*n+e*e+r*r),o=Math.acos(e/i),a=Math.atan2(r,n);return{lat:90-180*o/Math.PI,lng:90-180*a/Math.PI-(a<-Math.PI/2?360:0),r:i}}(r),l=90*(u.r/o(bn,this)-1),h=l/Math.cos(E(u.lat)),c=[u.lng-h,u.lng+h],f=[u.lat+l,u.lat-l],g=p(mn(this.level,o(Mn,this),c[0],f[0]),2),y=g[0],v=g[1],m=p(mn(this.level,o(Mn,this),c[1],f[1]),2),w=m[0],b=m[1];!e.record&&(e.record={});var M=e.record;if(M.hasOwnProperty("".concat(Math.round((y+w)/2),"_").concat(Math.round((v+b)/2)))){for(var x=[],_=y;_<=w;_++)for(var N=v;N<=b;N++){var k="".concat(_,"_").concat(N);M.hasOwnProperty(k)||(M[k]=wn(this.level,o(Mn,this),_,N,_,N)[0],e.push(M[k])),x.push(M[k])}e=x}else e=wn(this.level,o(Mn,this),y,v,w,b).map((function(t){var n="".concat(t.x,"_").concat(t.y);return M.hasOwnProperty(n)?M[n]:(M[n]=t,e.push(t),t)}))}}e.filter((function(t){return!t.obj})).filter(o(kn,this)||function(){return!0}).forEach((function(e){var r=e.x,i=e.y,a=e.lng,s=e.lat,u=e.latLen,l=360/Math.pow(2,n.level);if(!e.obj){var h=l*(1-n.tileMargin),c=u*(1-n.tileMargin),f=E(a),d=E(-s),g=new t.Mesh(new t.SphereGeometry(o(bn,n),Math.ceil(h/n.curvatureResolution),Math.ceil(c/n.curvatureResolution),E(90-h/2)+f,E(h),E(90-c/2)+d,E(c)),new t.MeshLambertMaterial);if(o(Mn,n)){var y=p([s+u/2,s-u/2].map((function(t){return.5-t/180})),2),v=y[0],m=y[1];!function(t){for(var n,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,i=un().domain([1,0]).range([e,r]).clamp(!0),o=un().domain([yn(e),yn(r)]).range([1,0]).clamp(!0),a=t.array,s=0,u=a.length;s<u;s+=2)a[s+1]=(n=a[s+1],o(yn(i(n))));t.needsUpdate=!0}(g.geometry.attributes.uv,v,m)}e.obj=g}e.loading||(e.loading=!0,(new t.TextureLoader).load(n.tileUrl(r,i,n.level),(function(r){var i=e.obj;i&&(r.colorSpace=t.SRGBColorSpace,i.material.map=r,i.material.color=null,i.material.needsUpdate=!0,n.add(i)),e.loading=!1})))}))}}return An}));
