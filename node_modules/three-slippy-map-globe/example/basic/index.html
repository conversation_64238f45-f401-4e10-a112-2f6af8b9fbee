<head>
  <style> body { margin: 0; } </style>

  <script type="importmap">{ "imports": {
    "three": "https://esm.sh/three",
    "three/": "https://esm.sh/three/"
  }}</script>

<!--  <script type="module"> import * as THREE from 'three'; window.THREE = THREE;</script>-->
<!--  <script src="../../dist/three-slippy-map-globe.js" defer></script>-->
</head>

<body>
  <div id="globe"></div>

  <script type="module">
    import SlippyMapGlobe from 'https://esm.sh/three-slippy-map-globe?external=three';
    import * as THREE from 'https://esm.sh/three';
    import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js?external=three';

    const R = 100;

    const Globe = new SlippyMapGlobe(R, {
      tileUrl: (x, y, l) => `https://tile.openstreetmap.org/${l}/${x}/${y}.png`
    });

    // Setup renderer
    const renderer = new THREE.WebGLRenderer();
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(window.devicePixelRatio);
    document.getElementById('globe').appendChild(renderer.domElement);

    // Setup scene
    const scene = new THREE.Scene();
    scene.add(Globe);
    scene.add(new THREE.AmbientLight(0xcccccc, Math.PI));
    scene.add(new THREE.DirectionalLight(0xffffff, 0.6 * Math.PI));

    // Setup camera
    const camera = new THREE.PerspectiveCamera();
    camera.aspect = window.innerWidth/window.innerHeight;
    camera.near = 1e-3;
    camera.far = R * 100;
    camera.updateProjectionMatrix();
    camera.position.z = R * 6;

    // Add camera controls
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.minDistance = R * (1 + 5 / 2**Globe.maxLevel); // Just above surface, adjusted resolution to max level
    controls.maxDistance = camera.far - R;
    controls.enableDamping = true;
    controls.dampingFactor = 0.1;
    controls.rotateSpeed = 0.3;
    controls.zoomSpeed = 0.3;

    // Update pov when camera moves
    Globe.updatePov(camera);
    controls.addEventListener('change', () => {
      Globe.updatePov(camera);
      const distToSurface = camera.position.distanceTo(Globe.position) - R;
      controls.rotateSpeed = distToSurface / R * 0.4;
      controls.zoomSpeed = Math.sqrt(distToSurface / R) * 0.6;
    });

    // Kick-off renderer
    (function animate() { // IIFE
      // Frame cycle
      controls.update();
      renderer.render(scene, camera);
      requestAnimationFrame(animate);
    })();
  </script>
</body>