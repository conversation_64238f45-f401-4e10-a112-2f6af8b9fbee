{"name": "three-slippy-map-globe", "version": "1.0.3", "description": "Tiled maps on a globe as a ThreeJS reusable 3D object", "type": "module", "unpkg": "dist/three-slippy-map-globe.min.js", "main": "dist/three-slippy-map-globe.mjs", "module": "dist/three-slippy-map-globe.mjs", "types": "dist/three-slippy-map-globe.d.ts", "exports": {"types": "./dist/three-slippy-map-globe.d.ts", "umd": "./dist/three-slippy-map-globe.min.js", "default": "./dist/three-slippy-map-globe.mjs"}, "sideEffects": false, "repository": {"type": "git", "url": "git+https://github.com/vasturiano/three-slippy-map-globe.git"}, "homepage": "https://github.com/vasturiano/three-slippy-map-globe", "keywords": ["3d", "tile", "slippy maps", "tiling engine", "gis", "globe", "webgl", "visualization", "three"], "author": {"name": "Vasco <PERSON>turiano", "url": "https://github.com/vasturiano"}, "license": "MIT", "bugs": {"url": "https://github.com/vasturiano/three-slippy-map-globe/issues"}, "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -w -c rollup.config.dev.js", "prepare": "npm run build"}, "files": ["dist/**/*", "example/**/*"], "dependencies": {"d3-geo": "1 - 3", "d3-octree": "^1.1", "d3-scale": "1 - 4"}, "peerDependencies": {"three": ">=0.154"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.0", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.2", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-terser": "^0.4.4", "@types/three": ">=0.154.0", "rimraf": "^6.0.1", "rollup": "^4.30.1", "rollup-plugin-dts": "^6.1.1", "three": "^0.172.0", "typescript": "^5.7.3"}, "engines": {"node": ">=12"}}