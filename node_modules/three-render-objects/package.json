{"name": "three-render-objects", "version": "1.40.2", "description": "Easy way to render ThreeJS objects with built-in interaction defaults", "license": "MIT", "type": "module", "unpkg": "dist/three-render-objects.min.js", "jsdelivr": "dist/three-render-objects.min.js", "main": "dist/three-render-objects.mjs", "module": "dist/three-render-objects.mjs", "types": "dist/three-render-objects.d.ts", "exports": {"types": "./dist/three-render-objects.d.ts", "umd": "./dist/three-render-objects.min.js", "default": "./dist/three-render-objects.mjs"}, "sideEffects": ["./src/*.css"], "repository": {"type": "git", "url": "git+https://github.com/vasturiano/three-render-objects.git"}, "homepage": "https://github.com/vasturiano/three-render-objects", "keywords": ["3d", "three", "objects", "scene", "tick", "webgl"], "author": {"name": "Vasco <PERSON>turiano", "url": "https://github.com/vasturiano"}, "bugs": {"url": "https://github.com/vasturiano/three-render-objects/issues"}, "files": ["dist/**/*"], "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -w -c rollup.config.dev.js", "prepare": "npm run build"}, "dependencies": {"@tweenjs/tween.js": "18 - 25", "accessor-fn": "1", "float-tooltip": "^1.7", "kapsule": "^1.16", "polished": "4"}, "peerDependencies": {"three": ">=0.168"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.2", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-terser": "^0.4.4", "@types/react": "^19.1.4", "postcss": "^8.5.3", "rimraf": "^6.0.1", "rollup": "^4.40.2", "rollup-plugin-dts": "^6.2.1", "rollup-plugin-postcss": "^4.0.2", "three": "^0.176.0", "typescript": "^5.8.3"}, "engines": {"node": ">=12"}}