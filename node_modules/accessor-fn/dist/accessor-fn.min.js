// Version 1.5.3 accessor-fn - https://github.com/vasturiano/accessor-fn
!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):(e="undefined"!=typeof globalThis?globalThis:e||self).accessorFn=n()}(this,(function(){"use strict";return function(e){return"function"==typeof e?e:"string"==typeof e?function(n){return n[e]}:function(n){return e}}}));
