// Version 1.5.3 accessor-fn - https://github.com/vasturiano/accessor-fn
(function (global, factory) {
    typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
    typeof define === 'function' && define.amd ? define(factory) :
    (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.accessorFn = factory());
})(this, (function () { 'use strict';

    var index = (function (p) {
      return typeof p === 'function' ? p // fn
      : typeof p === 'string' ? function (obj) {
        return obj[p];
      } // property name
      : function (obj) {
        return p;
      };
    }); // constant

    return index;

}));
//# sourceMappingURL=accessor-fn.js.map
