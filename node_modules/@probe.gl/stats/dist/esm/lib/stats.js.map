{"version": 3, "sources": ["../../../src/lib/stats.ts"], "names": ["Stat", "Stats", "constructor", "options", "id", "stats", "_initializeStats", "Object", "seal", "get", "name", "type", "_getOrCreate", "size", "keys", "length", "reset", "key", "for<PERSON>ach", "fn", "getTable", "table", "stat", "time", "count", "average", "getAverageTime", "hz", "getHz"], "mappings": ";AAAA,OAAOA,IAAP,MAAiB,QAAjB;AAGA,eAAe,MAAMC,KAAN,CAAY;AAIzBC,EAAAA,WAAW,CAACC,OAAD,EAAkF;AAAA;;AAAA,mCAFtD,EAEsD;;AAC3F,SAAKC,EAAL,GAAUD,OAAO,CAACC,EAAlB;AACA,SAAKC,KAAL,GAAa,EAAb;;AAEA,SAAKC,gBAAL,CAAsBH,OAAO,CAACE,KAA9B;;AAEAE,IAAAA,MAAM,CAACC,IAAP,CAAY,IAAZ;AACD;;AAGDC,EAAAA,GAAG,CAACC,IAAD,EAA6C;AAAA,QAA9BC,IAA8B,uEAAf,OAAe;AAC9C,WAAO,KAAKC,YAAL,CAAkB;AAACF,MAAAA,IAAD;AAAOC,MAAAA;AAAP,KAAlB,CAAP;AACD;;AAEO,MAAJE,IAAI,GAAW;AACjB,WAAON,MAAM,CAACO,IAAP,CAAY,KAAKT,KAAjB,EAAwBU,MAA/B;AACD;;AAGDC,EAAAA,KAAK,GAAS;AACZ,SAAK,MAAMC,GAAX,IAAkB,KAAKZ,KAAvB,EAA8B;AAC5B,WAAKA,KAAL,CAAWY,GAAX,EAAgBD,KAAhB;AACD;;AAED,WAAO,IAAP;AACD;;AAEDE,EAAAA,OAAO,CAACC,EAAD,EAAiC;AACtC,SAAK,MAAMF,GAAX,IAAkB,KAAKZ,KAAvB,EAA8B;AAC5Bc,MAAAA,EAAE,CAAC,KAAKd,KAAL,CAAWY,GAAX,CAAD,CAAF;AACD;AACF;;AAEDG,EAAAA,QAAQ,GAQJ;AACF,UAAMC,KAAK,GAAG,EAAd;AACA,SAAKH,OAAL,CAAaI,IAAI,IAAI;AACnBD,MAAAA,KAAK,CAACC,IAAI,CAACZ,IAAN,CAAL,GAAmB;AACjBa,QAAAA,IAAI,EAAED,IAAI,CAACC,IAAL,IAAa,CADF;AAEjBC,QAAAA,KAAK,EAAEF,IAAI,CAACE,KAAL,IAAc,CAFJ;AAGjBC,QAAAA,OAAO,EAAEH,IAAI,CAACI,cAAL,MAAyB,CAHjB;AAIjBC,QAAAA,EAAE,EAAEL,IAAI,CAACM,KAAL,MAAgB;AAJH,OAAnB;AAMD,KAPD;AASA,WAAOP,KAAP;AACD;;AAEDf,EAAAA,gBAAgB,GAAqE;AAAA,QAApED,KAAoE,uEAAV,EAAU;AACnFA,IAAAA,KAAK,CAACa,OAAN,CAAcI,IAAI,IAAI,KAAKV,YAAL,CAAkBU,IAAlB,CAAtB;AACD;;AAEDV,EAAAA,YAAY,CAACU,IAAD,EAAa;AACvB,QAAI,CAACA,IAAD,IAAS,CAACA,IAAI,CAACZ,IAAnB,EAAyB;AACvB,aAAO,IAAP;AACD;;AAED,UAAM;AAACA,MAAAA,IAAD;AAAOC,MAAAA;AAAP,QAAeW,IAArB;;AACA,QAAI,CAAC,KAAKjB,KAAL,CAAWK,IAAX,CAAL,EAAuB;AACrB,UAAIY,IAAI,YAAYtB,IAApB,EAA0B;AACxB,aAAKK,KAAL,CAAWK,IAAX,IAAmBY,IAAnB;AACD,OAFD,MAEO;AACL,aAAKjB,KAAL,CAAWK,IAAX,IAAmB,IAAIV,IAAJ,CAASU,IAAT,EAAeC,IAAf,CAAnB;AACD;AACF;;AACD,WAAO,KAAKN,KAAL,CAAWK,IAAX,CAAP;AACD;;AA7EwB", "sourcesContent": ["import Stat from './stat';\n\n/** A \"bag\" of `Stat` objects, can be visualized using `StatsWidget` */\nexport default class Stats {\n  readonly id: string;\n  readonly stats: Record<string, Stat> = {};\n\n  constructor(options: {id: string; stats?: Stats | Stat[] | {name: string; type?: string}[]}) {\n    this.id = options.id;\n    this.stats = {};\n\n    this._initializeStats(options.stats);\n\n    Object.seal(this);\n  }\n\n  /** Acquire a stat. Create if it doesn't exist. */\n  get(name: string, type: string = 'count'): Stat {\n    return this._getOrCreate({name, type});\n  }\n\n  get size(): number {\n    return Object.keys(this.stats).length;\n  }\n\n  /** Reset all stats */\n  reset(): this {\n    for (const key in this.stats) {\n      this.stats[key].reset();\n    }\n\n    return this;\n  }\n\n  forEach(fn: (stat: Stat) => void): void {\n    for (const key in this.stats) {\n      fn(this.stats[key]);\n    }\n  }\n\n  getTable(): Record<\n    string,\n    {\n      time: number;\n      count: number;\n      average: number;\n      hz: number;\n    }\n    > {\n    const table = {};\n    this.forEach(stat => {\n      table[stat.name] = {\n        time: stat.time || 0,\n        count: stat.count || 0,\n        average: stat.getAverageTime() || 0,\n        hz: stat.getHz() || 0\n      };\n    });\n\n    return table;\n  }\n\n  _initializeStats(stats: Stats | Stat[] | {name: string; type?: string}[] = []): void {\n    stats.forEach(stat => this._getOrCreate(stat));\n  }\n\n  _getOrCreate(stat): Stat {\n    if (!stat || !stat.name) {\n      return null;\n    }\n\n    const {name, type} = stat;\n    if (!this.stats[name]) {\n      if (stat instanceof Stat) {\n        this.stats[name] = stat;\n      } else {\n        this.stats[name] = new Stat(name, type);\n      }\n    }\n    return this.stats[name];\n  }\n}\n"], "file": "stats.js"}