{"version": 3, "sources": ["../../../src/lib/stat.ts"], "names": ["getHiResTimestamp", "Stat", "constructor", "name", "type", "reset", "setSampleSize", "samples", "sampleSize", "incrementCount", "addCount", "decrementCount", "subtractCount", "value", "_count", "_samples", "_checkSampling", "addTime", "time", "_time", "lastTiming", "timeStart", "_startTime", "_timerPending", "timeEnd", "getSampleAverageCount", "lastSampleCount", "getSampleAverageTime", "lastSampleTime", "getSampleHz", "getAverageCount", "count", "getAverageTime", "getHz"], "mappings": ";AAAA,OAAOA,iBAAP,MAA8B,2BAA9B;AAEA,eAAe,MAAMC,IAAN,CAAW;AAiBxBC,EAAAA,WAAW,CAACC,IAAD,EAAeC,IAAf,EAA8B;AAAA;;AAAA;;AAAA,wCAdpB,CAcoB;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,oCANxB,CAMwB;;AAAA,mCALzB,CAKyB;;AAAA,sCAJtB,CAIsB;;AAAA,wCAHpB,CAGoB;;AAAA,2CAFhB,KAEgB;;AACvC,SAAKD,IAAL,GAAYA,IAAZ;AACA,SAAKC,IAAL,GAAYA,IAAZ;AACA,SAAKC,KAAL;AACD;;AAEDC,EAAAA,aAAa,CAACC,OAAD,EAAwB;AACnC,SAAKC,UAAL,GAAkBD,OAAlB;AACA,WAAO,IAAP;AACD;;AAGDE,EAAAA,cAAc,GAAS;AACrB,SAAKC,QAAL,CAAc,CAAd;AAEA,WAAO,IAAP;AACD;;AAGDC,EAAAA,cAAc,GAAS;AACrB,SAAKC,aAAL,CAAmB,CAAnB;AAEA,WAAO,IAAP;AACD;;AAGDF,EAAAA,QAAQ,CAACG,KAAD,EAAsB;AAC5B,SAAKC,MAAL,IAAeD,KAAf;AACA,SAAKE,QAAL;;AACA,SAAKC,cAAL;;AAEA,WAAO,IAAP;AACD;;AAGDJ,EAAAA,aAAa,CAACC,KAAD,EAAsB;AACjC,SAAKC,MAAL,IAAeD,KAAf;AACA,SAAKE,QAAL;;AACA,SAAKC,cAAL;;AAEA,WAAO,IAAP;AACD;;AAGDC,EAAAA,OAAO,CAACC,IAAD,EAAqB;AAC1B,SAAKC,KAAL,IAAcD,IAAd;AACA,SAAKE,UAAL,GAAkBF,IAAlB;AACA,SAAKH,QAAL;;AACA,SAAKC,cAAL;;AAEA,WAAO,IAAP;AACD;;AAGDK,EAAAA,SAAS,GAAS;AAChB,SAAKC,UAAL,GAAkBtB,iBAAiB,EAAnC;AACA,SAAKuB,aAAL,GAAqB,IAArB;AAEA,WAAO,IAAP;AACD;;AAGDC,EAAAA,OAAO,GAAS;AACd,QAAI,CAAC,KAAKD,aAAV,EAAyB;AACvB,aAAO,IAAP;AACD;;AACD,SAAKN,OAAL,CAAajB,iBAAiB,KAAK,KAAKsB,UAAxC;AACA,SAAKC,aAAL,GAAqB,KAArB;;AACA,SAAKP,cAAL;;AAEA,WAAO,IAAP;AACD;;AAEDS,EAAAA,qBAAqB,GAAW;AAC9B,WAAO,KAAKjB,UAAL,GAAkB,CAAlB,GAAsB,KAAKkB,eAAL,GAAuB,KAAKlB,UAAlD,GAA+D,CAAtE;AACD;;AAGDmB,EAAAA,oBAAoB,GAAW;AAC7B,WAAO,KAAKnB,UAAL,GAAkB,CAAlB,GAAsB,KAAKoB,cAAL,GAAsB,KAAKpB,UAAjD,GAA8D,CAArE;AACD;;AAGDqB,EAAAA,WAAW,GAAW;AACpB,WAAO,KAAKD,cAAL,GAAsB,CAAtB,GAA0B,KAAKpB,UAAL,IAAmB,KAAKoB,cAAL,GAAsB,IAAzC,CAA1B,GAA2E,CAAlF;AACD;;AAEDE,EAAAA,eAAe,GAAW;AACxB,WAAO,KAAKvB,OAAL,GAAe,CAAf,GAAmB,KAAKwB,KAAL,GAAa,KAAKxB,OAArC,GAA+C,CAAtD;AACD;;AAGDyB,EAAAA,cAAc,GAAW;AACvB,WAAO,KAAKzB,OAAL,GAAe,CAAf,GAAmB,KAAKW,IAAL,GAAY,KAAKX,OAApC,GAA8C,CAArD;AACD;;AAGD0B,EAAAA,KAAK,GAAW;AACd,WAAO,KAAKf,IAAL,GAAY,CAAZ,GAAgB,KAAKX,OAAL,IAAgB,KAAKW,IAAL,GAAY,IAA5B,CAAhB,GAAoD,CAA3D;AACD;;AAEDb,EAAAA,KAAK,GAAS;AACZ,SAAKa,IAAL,GAAY,CAAZ;AACA,SAAKa,KAAL,GAAa,CAAb;AACA,SAAKxB,OAAL,GAAe,CAAf;AACA,SAAKa,UAAL,GAAkB,CAAlB;AACA,SAAKQ,cAAL,GAAsB,CAAtB;AACA,SAAKF,eAAL,GAAuB,CAAvB;AACA,SAAKZ,MAAL,GAAc,CAAd;AACA,SAAKK,KAAL,GAAa,CAAb;AACA,SAAKJ,QAAL,GAAgB,CAAhB;AACA,SAAKO,UAAL,GAAkB,CAAlB;AACA,SAAKC,aAAL,GAAqB,KAArB;AAEA,WAAO,IAAP;AACD;;AAEDP,EAAAA,cAAc,GAAS;AACrB,QAAI,KAAKD,QAAL,KAAkB,KAAKP,UAA3B,EAAuC;AACrC,WAAKoB,cAAL,GAAsB,KAAKT,KAA3B;AACA,WAAKO,eAAL,GAAuB,KAAKZ,MAA5B;AACA,WAAKiB,KAAL,IAAc,KAAKjB,MAAnB;AACA,WAAKI,IAAL,IAAa,KAAKC,KAAlB;AACA,WAAKZ,OAAL,IAAgB,KAAKQ,QAArB;AACA,WAAKI,KAAL,GAAa,CAAb;AACA,WAAKL,MAAL,GAAc,CAAd;AACA,WAAKC,QAAL,GAAgB,CAAhB;AACD;AACF;;AAjJuB", "sourcesContent": ["import getHiResTimestamp from '../utils/hi-res-timestamp';\n\nexport default class Stat {\n  readonly name: string;\n  readonly type: string;\n  sampleSize: number = 1;\n  time: number;\n  count: number;\n  samples: number;\n  lastTiming: number;\n  lastSampleTime: number;\n  lastSampleCount: number;\n\n  _count: number = 0;\n  _time: number = 0;\n  _samples: number = 0;\n  _startTime: number = 0;\n  _timerPending: boolean = false;\n\n  constructor(name: string, type?: string) {\n    this.name = name;\n    this.type = type;\n    this.reset();\n  }\n\n  setSampleSize(samples: number): this {\n    this.sampleSize = samples;\n    return this;\n  }\n\n  /** Call to increment count (+1) */\n  incrementCount(): this {\n    this.addCount(1);\n\n    return this;\n  }\n\n  /** Call to decrement count (-1) */\n  decrementCount(): this {\n    this.subtractCount(1);\n\n    return this;\n  }\n\n  /** Increase count */\n  addCount(value: number): this {\n    this._count += value;\n    this._samples++;\n    this._checkSampling();\n\n    return this;\n  }\n\n  /** Decrease count */\n  subtractCount(value: number): this {\n    this._count -= value;\n    this._samples++;\n    this._checkSampling();\n\n    return this;\n  }\n\n  /** Add an arbitrary timing and bump the count */\n  addTime(time: number): this {\n    this._time += time;\n    this.lastTiming = time;\n    this._samples++;\n    this._checkSampling();\n\n    return this;\n  }\n\n  /** Start a timer */\n  timeStart(): this {\n    this._startTime = getHiResTimestamp();\n    this._timerPending = true;\n\n    return this;\n  }\n\n  /** End a timer. Adds to time and bumps the timing count. */\n  timeEnd(): this {\n    if (!this._timerPending) {\n      return this;\n    }\n    this.addTime(getHiResTimestamp() - this._startTime);\n    this._timerPending = false;\n    this._checkSampling();\n\n    return this;\n  }\n\n  getSampleAverageCount(): number {\n    return this.sampleSize > 0 ? this.lastSampleCount / this.sampleSize : 0;\n  }\n\n  /** Calculate average time / count for the previous window */\n  getSampleAverageTime(): number {\n    return this.sampleSize > 0 ? this.lastSampleTime / this.sampleSize : 0;\n  }\n\n  /** Calculate counts per second for the previous window */\n  getSampleHz(): number {\n    return this.lastSampleTime > 0 ? this.sampleSize / (this.lastSampleTime / 1000) : 0;\n  }\n\n  getAverageCount(): number {\n    return this.samples > 0 ? this.count / this.samples : 0;\n  }\n\n  /** Calculate average time / count */\n  getAverageTime(): number {\n    return this.samples > 0 ? this.time / this.samples : 0;\n  }\n\n  /** Calculate counts per second */\n  getHz(): number {\n    return this.time > 0 ? this.samples / (this.time / 1000) : 0;\n  }\n\n  reset(): this {\n    this.time = 0;\n    this.count = 0;\n    this.samples = 0;\n    this.lastTiming = 0;\n    this.lastSampleTime = 0;\n    this.lastSampleCount = 0;\n    this._count = 0;\n    this._time = 0;\n    this._samples = 0;\n    this._startTime = 0;\n    this._timerPending = false;\n\n    return this;\n  }\n\n  _checkSampling(): void {\n    if (this._samples === this.sampleSize) {\n      this.lastSampleTime = this._time;\n      this.lastSampleCount = this._count;\n      this.count += this._count;\n      this.time += this._time;\n      this.samples += this._samples;\n      this._time = 0;\n      this._count = 0;\n      this._samples = 0;\n    }\n  }\n}\n"], "file": "stat.js"}