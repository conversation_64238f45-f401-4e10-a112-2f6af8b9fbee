{"version": 3, "sources": ["../../../src/utils/hi-res-timestamp.ts"], "names": ["getHiResTimestamp", "timestamp", "window", "performance", "now", "process", "hrtime", "timeParts", "Date"], "mappings": "AAoBA,eAAe,SAASA,iBAAT,GAAqC;AAClD,MAAIC,SAAJ;;AAEA,MAAI,OAAOC,MAAP,KAAkB,WAAlB,IAAiCA,MAAM,CAACC,WAA5C,EAAyD;AACvDF,IAAAA,SAAS,GAAGC,MAAM,CAACC,WAAP,CAAmBC,GAAnB,EAAZ;AACD,GAFD,MAEO,IAAI,OAAOC,OAAP,KAAmB,WAAnB,IAAkCA,OAAO,CAACC,MAA9C,EAAsD;AAC3D,UAAMC,SAAS,GAAGF,OAAO,CAACC,MAAR,EAAlB;AACAL,IAAAA,SAAS,GAAGM,SAAS,CAAC,CAAD,CAAT,GAAe,IAAf,GAAsBA,SAAS,CAAC,CAAD,CAAT,GAAe,GAAjD;AACD,GAHM,MAGA;AACLN,IAAAA,SAAS,GAAGO,IAAI,CAACJ,GAAL,EAAZ;AACD;;AAED,SAAOH,SAAP;AACD", "sourcesContent": ["// Copyright (c) 2017 Uber Technologies, Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\nexport default function getHiResTimestamp(): number {\n  let timestamp;\n  // Get best timer available.\n  if (typeof window !== 'undefined' && window.performance) {\n    timestamp = window.performance.now();\n  } else if (typeof process !== 'undefined' && process.hrtime) {\n    const timeParts = process.hrtime();\n    timestamp = timeParts[0] * 1000 + timeParts[1] / 1e6;\n  } else {\n    timestamp = Date.now();\n  }\n\n  return timestamp;\n}\n"], "file": "hi-res-timestamp.js"}