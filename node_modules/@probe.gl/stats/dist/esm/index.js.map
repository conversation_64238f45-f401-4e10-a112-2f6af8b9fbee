{"version": 3, "sources": ["../../src/index.ts"], "names": ["default", "Stats", "Stat", "_getHiResTimestamp"], "mappings": "AAAA,SAAQA,OAAO,IAAIC,KAAnB,QAA+B,aAA/B;AACA,SAAQD,OAAO,IAAIE,IAAnB,QAA8B,YAA9B;AAGA,SAAQF,OAAO,IAAIG,kBAAnB,QAA4C,0BAA5C", "sourcesContent": ["export {default as Stats} from './lib/stats';\nexport {default as Stat} from './lib/stat';\n\n// UTILITIES\nexport {default as _getHiResTimestamp} from './utils/hi-res-timestamp';\n"], "file": "index.js"}