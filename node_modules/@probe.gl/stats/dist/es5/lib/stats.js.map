{"version": 3, "sources": ["../../../src/lib/stats.ts"], "names": ["Stats", "options", "id", "stats", "_initializeStats", "Object", "seal", "name", "type", "_getOrCreate", "keys", "length", "key", "reset", "fn", "table", "for<PERSON>ach", "stat", "time", "count", "average", "getAverageTime", "hz", "getHz", "Stat"], "mappings": ";;;;;;;;;;;;;;;AAAA;;IAGqBA,K;AAInB,iBAAYC,OAAZ,EAA6F;AAAA;AAAA;AAAA,iDAFtD,EAEsD;AAC3F,SAAKC,EAAL,GAAUD,OAAO,CAACC,EAAlB;AACA,SAAKC,KAAL,GAAa,EAAb;;AAEA,SAAKC,gBAAL,CAAsBH,OAAO,CAACE,KAA9B;;AAEAE,IAAAA,MAAM,CAACC,IAAP,CAAY,IAAZ;AACD;;;;WAGD,aAAIC,IAAJ,EAAgD;AAAA,UAA9BC,IAA8B,uEAAf,OAAe;AAC9C,aAAO,KAAKC,YAAL,CAAkB;AAACF,QAAAA,IAAI,EAAJA,IAAD;AAAOC,QAAAA,IAAI,EAAJA;AAAP,OAAlB,CAAP;AACD;;;SAED,eAAmB;AACjB,aAAOH,MAAM,CAACK,IAAP,CAAY,KAAKP,KAAjB,EAAwBQ,MAA/B;AACD;;;WAGD,iBAAc;AACZ,WAAK,IAAMC,GAAX,IAAkB,KAAKT,KAAvB,EAA8B;AAC5B,aAAKA,KAAL,CAAWS,GAAX,EAAgBC,KAAhB;AACD;;AAED,aAAO,IAAP;AACD;;;WAED,iBAAQC,EAAR,EAAwC;AACtC,WAAK,IAAMF,GAAX,IAAkB,KAAKT,KAAvB,EAA8B;AAC5BW,QAAAA,EAAE,CAAC,KAAKX,KAAL,CAAWS,GAAX,CAAD,CAAF;AACD;AACF;;;WAED,oBAQI;AACF,UAAMG,KAAK,GAAG,EAAd;AACA,WAAKC,OAAL,CAAa,UAAAC,IAAI,EAAI;AACnBF,QAAAA,KAAK,CAACE,IAAI,CAACV,IAAN,CAAL,GAAmB;AACjBW,UAAAA,IAAI,EAAED,IAAI,CAACC,IAAL,IAAa,CADF;AAEjBC,UAAAA,KAAK,EAAEF,IAAI,CAACE,KAAL,IAAc,CAFJ;AAGjBC,UAAAA,OAAO,EAAEH,IAAI,CAACI,cAAL,MAAyB,CAHjB;AAIjBC,UAAAA,EAAE,EAAEL,IAAI,CAACM,KAAL,MAAgB;AAJH,SAAnB;AAMD,OAPD;AASA,aAAOR,KAAP;AACD;;;WAED,4BAAqF;AAAA;;AAAA,UAApEZ,KAAoE,uEAAV,EAAU;AACnFA,MAAAA,KAAK,CAACa,OAAN,CAAc,UAAAC,IAAI;AAAA,eAAI,KAAI,CAACR,YAAL,CAAkBQ,IAAlB,CAAJ;AAAA,OAAlB;AACD;;;WAED,sBAAaA,IAAb,EAAyB;AACvB,UAAI,CAACA,IAAD,IAAS,CAACA,IAAI,CAACV,IAAnB,EAAyB;AACvB,eAAO,IAAP;AACD;;AAED,UAAOA,IAAP,GAAqBU,IAArB,CAAOV,IAAP;AAAA,UAAaC,IAAb,GAAqBS,IAArB,CAAaT,IAAb;;AACA,UAAI,CAAC,KAAKL,KAAL,CAAWI,IAAX,CAAL,EAAuB;AACrB,YAAIU,IAAI,YAAYO,aAApB,EAA0B;AACxB,eAAKrB,KAAL,CAAWI,IAAX,IAAmBU,IAAnB;AACD,SAFD,MAEO;AACL,eAAKd,KAAL,CAAWI,IAAX,IAAmB,IAAIiB,aAAJ,CAASjB,IAAT,EAAeC,IAAf,CAAnB;AACD;AACF;;AACD,aAAO,KAAKL,KAAL,CAAWI,IAAX,CAAP;AACD", "sourcesContent": ["import Stat from './stat';\n\n/** A \"bag\" of `Stat` objects, can be visualized using `StatsWidget` */\nexport default class Stats {\n  readonly id: string;\n  readonly stats: Record<string, Stat> = {};\n\n  constructor(options: {id: string; stats?: Stats | Stat[] | {name: string; type?: string}[]}) {\n    this.id = options.id;\n    this.stats = {};\n\n    this._initializeStats(options.stats);\n\n    Object.seal(this);\n  }\n\n  /** Acquire a stat. Create if it doesn't exist. */\n  get(name: string, type: string = 'count'): Stat {\n    return this._getOrCreate({name, type});\n  }\n\n  get size(): number {\n    return Object.keys(this.stats).length;\n  }\n\n  /** Reset all stats */\n  reset(): this {\n    for (const key in this.stats) {\n      this.stats[key].reset();\n    }\n\n    return this;\n  }\n\n  forEach(fn: (stat: Stat) => void): void {\n    for (const key in this.stats) {\n      fn(this.stats[key]);\n    }\n  }\n\n  getTable(): Record<\n    string,\n    {\n      time: number;\n      count: number;\n      average: number;\n      hz: number;\n    }\n    > {\n    const table = {};\n    this.forEach(stat => {\n      table[stat.name] = {\n        time: stat.time || 0,\n        count: stat.count || 0,\n        average: stat.getAverageTime() || 0,\n        hz: stat.getHz() || 0\n      };\n    });\n\n    return table;\n  }\n\n  _initializeStats(stats: Stats | Stat[] | {name: string; type?: string}[] = []): void {\n    stats.forEach(stat => this._getOrCreate(stat));\n  }\n\n  _getOrCreate(stat): Stat {\n    if (!stat || !stat.name) {\n      return null;\n    }\n\n    const {name, type} = stat;\n    if (!this.stats[name]) {\n      if (stat instanceof Stat) {\n        this.stats[name] = stat;\n      } else {\n        this.stats[name] = new Stat(name, type);\n      }\n    }\n    return this.stats[name];\n  }\n}\n"], "file": "stats.js"}