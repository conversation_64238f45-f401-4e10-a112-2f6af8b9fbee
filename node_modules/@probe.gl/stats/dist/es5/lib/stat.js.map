{"version": 3, "sources": ["../../../src/lib/stat.ts"], "names": ["Stat", "name", "type", "reset", "samples", "sampleSize", "addCount", "subtractCount", "value", "_count", "_samples", "_checkSampling", "time", "_time", "lastTiming", "_startTime", "_timerPending", "addTime", "lastSampleCount", "lastSampleTime", "count"], "mappings": ";;;;;;;;;;;;;;;AAAA;;IAEqBA,I;AAiBnB,gBAAYC,IAAZ,EAA0BC,IAA1B,EAAyC;AAAA;AAAA;AAAA;AAAA,sDAdpB,CAcoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kDANxB,CAMwB;AAAA,iDALzB,CAKyB;AAAA,oDAJtB,CAIsB;AAAA,sDAHpB,CAGoB;AAAA,yDAFhB,KAEgB;AACvC,SAAKD,IAAL,GAAYA,IAAZ;AACA,SAAKC,IAAL,GAAYA,IAAZ;AACA,SAAKC,KAAL;AACD;;;;WAED,uBAAcC,OAAd,EAAqC;AACnC,WAAKC,UAAL,GAAkBD,OAAlB;AACA,aAAO,IAAP;AACD;;;WAGD,0BAAuB;AACrB,WAAKE,QAAL,CAAc,CAAd;AAEA,aAAO,IAAP;AACD;;;WAGD,0BAAuB;AACrB,WAAKC,aAAL,CAAmB,CAAnB;AAEA,aAAO,IAAP;AACD;;;WAGD,kBAASC,KAAT,EAA8B;AAC5B,WAAKC,MAAL,IAAeD,KAAf;AACA,WAAKE,QAAL;;AACA,WAAKC,cAAL;;AAEA,aAAO,IAAP;AACD;;;WAGD,uBAAcH,KAAd,EAAmC;AACjC,WAAKC,MAAL,IAAeD,KAAf;AACA,WAAKE,QAAL;;AACA,WAAKC,cAAL;;AAEA,aAAO,IAAP;AACD;;;WAGD,iBAAQC,IAAR,EAA4B;AAC1B,WAAKC,KAAL,IAAcD,IAAd;AACA,WAAKE,UAAL,GAAkBF,IAAlB;AACA,WAAKF,QAAL;;AACA,WAAKC,cAAL;;AAEA,aAAO,IAAP;AACD;;;WAGD,qBAAkB;AAChB,WAAKI,UAAL,GAAkB,8BAAlB;AACA,WAAKC,aAAL,GAAqB,IAArB;AAEA,aAAO,IAAP;AACD;;;WAGD,mBAAgB;AACd,UAAI,CAAC,KAAKA,aAAV,EAAyB;AACvB,eAAO,IAAP;AACD;;AACD,WAAKC,OAAL,CAAa,iCAAsB,KAAKF,UAAxC;AACA,WAAKC,aAAL,GAAqB,KAArB;;AACA,WAAKL,cAAL;;AAEA,aAAO,IAAP;AACD;;;WAED,iCAAgC;AAC9B,aAAO,KAAKN,UAAL,GAAkB,CAAlB,GAAsB,KAAKa,eAAL,GAAuB,KAAKb,UAAlD,GAA+D,CAAtE;AACD;;;WAGD,gCAA+B;AAC7B,aAAO,KAAKA,UAAL,GAAkB,CAAlB,GAAsB,KAAKc,cAAL,GAAsB,KAAKd,UAAjD,GAA8D,CAArE;AACD;;;WAGD,uBAAsB;AACpB,aAAO,KAAKc,cAAL,GAAsB,CAAtB,GAA0B,KAAKd,UAAL,IAAmB,KAAKc,cAAL,GAAsB,IAAzC,CAA1B,GAA2E,CAAlF;AACD;;;WAED,2BAA0B;AACxB,aAAO,KAAKf,OAAL,GAAe,CAAf,GAAmB,KAAKgB,KAAL,GAAa,KAAKhB,OAArC,GAA+C,CAAtD;AACD;;;WAGD,0BAAyB;AACvB,aAAO,KAAKA,OAAL,GAAe,CAAf,GAAmB,KAAKQ,IAAL,GAAY,KAAKR,OAApC,GAA8C,CAArD;AACD;;;WAGD,iBAAgB;AACd,aAAO,KAAKQ,IAAL,GAAY,CAAZ,GAAgB,KAAKR,OAAL,IAAgB,KAAKQ,IAAL,GAAY,IAA5B,CAAhB,GAAoD,CAA3D;AACD;;;WAED,iBAAc;AACZ,WAAKA,IAAL,GAAY,CAAZ;AACA,WAAKQ,KAAL,GAAa,CAAb;AACA,WAAKhB,OAAL,GAAe,CAAf;AACA,WAAKU,UAAL,GAAkB,CAAlB;AACA,WAAKK,cAAL,GAAsB,CAAtB;AACA,WAAKD,eAAL,GAAuB,CAAvB;AACA,WAAKT,MAAL,GAAc,CAAd;AACA,WAAKI,KAAL,GAAa,CAAb;AACA,WAAKH,QAAL,GAAgB,CAAhB;AACA,WAAKK,UAAL,GAAkB,CAAlB;AACA,WAAKC,aAAL,GAAqB,KAArB;AAEA,aAAO,IAAP;AACD;;;WAED,0BAAuB;AACrB,UAAI,KAAKN,QAAL,KAAkB,KAAKL,UAA3B,EAAuC;AACrC,aAAKc,cAAL,GAAsB,KAAKN,KAA3B;AACA,aAAKK,eAAL,GAAuB,KAAKT,MAA5B;AACA,aAAKW,KAAL,IAAc,KAAKX,MAAnB;AACA,aAAKG,IAAL,IAAa,KAAKC,KAAlB;AACA,aAAKT,OAAL,IAAgB,KAAKM,QAArB;AACA,aAAKG,KAAL,GAAa,CAAb;AACA,aAAKJ,MAAL,GAAc,CAAd;AACA,aAAKC,QAAL,GAAgB,CAAhB;AACD;AACF", "sourcesContent": ["import getHiResTimestamp from '../utils/hi-res-timestamp';\n\nexport default class Stat {\n  readonly name: string;\n  readonly type: string;\n  sampleSize: number = 1;\n  time: number;\n  count: number;\n  samples: number;\n  lastTiming: number;\n  lastSampleTime: number;\n  lastSampleCount: number;\n\n  _count: number = 0;\n  _time: number = 0;\n  _samples: number = 0;\n  _startTime: number = 0;\n  _timerPending: boolean = false;\n\n  constructor(name: string, type?: string) {\n    this.name = name;\n    this.type = type;\n    this.reset();\n  }\n\n  setSampleSize(samples: number): this {\n    this.sampleSize = samples;\n    return this;\n  }\n\n  /** Call to increment count (+1) */\n  incrementCount(): this {\n    this.addCount(1);\n\n    return this;\n  }\n\n  /** Call to decrement count (-1) */\n  decrementCount(): this {\n    this.subtractCount(1);\n\n    return this;\n  }\n\n  /** Increase count */\n  addCount(value: number): this {\n    this._count += value;\n    this._samples++;\n    this._checkSampling();\n\n    return this;\n  }\n\n  /** Decrease count */\n  subtractCount(value: number): this {\n    this._count -= value;\n    this._samples++;\n    this._checkSampling();\n\n    return this;\n  }\n\n  /** Add an arbitrary timing and bump the count */\n  addTime(time: number): this {\n    this._time += time;\n    this.lastTiming = time;\n    this._samples++;\n    this._checkSampling();\n\n    return this;\n  }\n\n  /** Start a timer */\n  timeStart(): this {\n    this._startTime = getHiResTimestamp();\n    this._timerPending = true;\n\n    return this;\n  }\n\n  /** End a timer. Adds to time and bumps the timing count. */\n  timeEnd(): this {\n    if (!this._timerPending) {\n      return this;\n    }\n    this.addTime(getHiResTimestamp() - this._startTime);\n    this._timerPending = false;\n    this._checkSampling();\n\n    return this;\n  }\n\n  getSampleAverageCount(): number {\n    return this.sampleSize > 0 ? this.lastSampleCount / this.sampleSize : 0;\n  }\n\n  /** Calculate average time / count for the previous window */\n  getSampleAverageTime(): number {\n    return this.sampleSize > 0 ? this.lastSampleTime / this.sampleSize : 0;\n  }\n\n  /** Calculate counts per second for the previous window */\n  getSampleHz(): number {\n    return this.lastSampleTime > 0 ? this.sampleSize / (this.lastSampleTime / 1000) : 0;\n  }\n\n  getAverageCount(): number {\n    return this.samples > 0 ? this.count / this.samples : 0;\n  }\n\n  /** Calculate average time / count */\n  getAverageTime(): number {\n    return this.samples > 0 ? this.time / this.samples : 0;\n  }\n\n  /** Calculate counts per second */\n  getHz(): number {\n    return this.time > 0 ? this.samples / (this.time / 1000) : 0;\n  }\n\n  reset(): this {\n    this.time = 0;\n    this.count = 0;\n    this.samples = 0;\n    this.lastTiming = 0;\n    this.lastSampleTime = 0;\n    this.lastSampleCount = 0;\n    this._count = 0;\n    this._time = 0;\n    this._samples = 0;\n    this._startTime = 0;\n    this._timerPending = false;\n\n    return this;\n  }\n\n  _checkSampling(): void {\n    if (this._samples === this.sampleSize) {\n      this.lastSampleTime = this._time;\n      this.lastSampleCount = this._count;\n      this.count += this._count;\n      this.time += this._time;\n      this.samples += this._samples;\n      this._time = 0;\n      this._count = 0;\n      this._samples = 0;\n    }\n  }\n}\n"], "file": "stat.js"}