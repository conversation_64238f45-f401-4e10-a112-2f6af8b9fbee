export { VERSION } from './utils/globals';
export { self, window, global, document, process, console } from './lib/globals';
export { default as isBrowser, isBrowserMainThread } from './lib/is-browser';
export { default as getBrowser, isMobile } from './lib/get-browser';
export { default as isElectron } from './lib/is-electron';
export { default as assert } from './utils/assert';
//# sourceMappingURL=index.d.ts.map