{"version": 3, "sources": ["../../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAGA;;AACA;;AACA;;AACA;;AAGA", "sourcesContent": ["export {VERSION} from './utils/globals';\n\n// ENVIRONMENT\nexport {self, window, global, document, process, console} from './lib/globals';\nexport {default as isBrowser, isBrowserMainThread} from './lib/is-browser';\nexport {default as getBrowser, isMobile} from './lib/get-browser';\nexport {default as isElectron} from './lib/is-electron';\n\n// ENVIRONMENT'S ASSERT IS 5-15KB, SO WE PROVIDE OUR OWN\nexport {default as assert} from './utils/assert';\n"], "file": "index.js"}