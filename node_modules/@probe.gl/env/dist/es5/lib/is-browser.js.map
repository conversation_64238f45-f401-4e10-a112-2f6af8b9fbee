{"version": 3, "sources": ["../../../src/lib/is-browser.ts"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "isNode", "process", "String", "browser", "isBrowserMainThread", "document"], "mappings": ";;;;;;;;;;;;AAGA;;AAEe,SAASA,SAAT,GAA8B;AAE3C,MAAMC,MAAM,GAEV,QAAOC,OAAP,uDAAOA,OAAP,OAAmB,QAAnB,IAA+BC,MAAM,CAACD,OAAD,CAAN,KAAoB,kBAAnD,IAAyE,CAACA,OAAO,CAACE,OAFpF;AAIA,SAAO,CAACH,MAAD,IAAW,0BAAlB;AACD;;AAGM,SAASI,mBAAT,GAAwC;AAC7C,SAAOL,SAAS,MAAM,OAAOM,QAAP,KAAoB,WAA1C;AACD", "sourcesContent": ["// This function is needed in initialization stages,\n// make sure it can be imported in isolation\n\nimport isElectron from './is-electron';\n\nexport default function isBrowser(): boolean {\n  // Check if in browser by duck-typing Node context\n  const isNode =\n    // @ts-expect-error\n    typeof process === 'object' && String(process) === '[object process]' && !process.browser;\n\n  return !isNode || isElectron();\n}\n\n// document does not exist on worker thread\nexport function isBrowserMainThread(): boolean {\n  return isBrowser() && typeof document !== 'undefined';\n}\n"], "file": "is-browser.js"}