{"version": 3, "sources": ["../../../src/utils/assert.ts"], "names": ["assert", "condition", "message", "Error"], "mappings": ";;;;;;;AAAe,SAASA,MAAT,CAAgBC,SAAhB,EAAoCC,OAApC,EAAsD;AACnE,MAAI,CAACD,SAAL,EAAgB;AACd,UAAM,IAAIE,KAAJ,CAAUD,OAAO,IAAI,kBAArB,CAAN;AACD;AACF", "sourcesContent": ["export default function assert(condition: unknown, message?: string) {\n  if (!condition) {\n    throw new Error(message || 'Asser<PERSON> failed');\n  }\n}\n"], "file": "assert.js"}