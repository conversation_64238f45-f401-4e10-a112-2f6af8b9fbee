{"version": 3, "sources": ["../../../src/utils/globals.ts"], "names": ["VERSION", "__VERSION__", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAEA;;AAIO,IAAMA,OAAO,GAAG,OAAOC,WAAP,KAAuB,WAAvB,GAAqCA,WAArC,GAAmD,qBAAnE;;AAEA,IAAMC,SAAS,GAAG,yBAAlB", "sourcesContent": ["import checkIfBrowser from '../lib/is-browser';\n\nexport {self, window, global, document, process, console} from '../lib/globals';\n\n// Extract injected version from package.json (injected by babel plugin)\n// @ts-expect-error\nexport const VERSION = typeof __VERSION__ !== 'undefined' ? __VERSION__ : 'untranspiled source';\n\nexport const isBrowser = checkIfBrowser();\n"], "file": "globals.js"}