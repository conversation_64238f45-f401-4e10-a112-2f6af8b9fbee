{"version": 3, "sources": ["../../src/index.ts"], "names": ["VERSION", "self", "window", "global", "document", "process", "console", "default", "<PERSON><PERSON><PERSON><PERSON>", "isBrowserMainThread", "<PERSON><PERSON><PERSON><PERSON>", "isMobile", "isElectron", "assert"], "mappings": "AAAA,SAAQA,OAAR,QAAsB,iBAAtB;AAGA,SAAQC,IAAR,EAAcC,MAAd,EAAsBC,MAAtB,EAA8BC,QAA9B,EAAwCC,OAAxC,EAAiDC,OAAjD,QAA+D,eAA/D;AACA,SAAQC,OAAO,IAAIC,SAAnB,EAA8BC,mBAA9B,QAAwD,kBAAxD;AACA,SAAQF,OAAO,IAAIG,UAAnB,EAA+BC,QAA/B,QAA8C,mBAA9C;AACA,SAAQJ,OAAO,IAAIK,UAAnB,QAAoC,mBAApC;AAGA,SAAQL,OAAO,IAAIM,MAAnB,QAAgC,gBAAhC", "sourcesContent": ["export {VERSION} from './utils/globals';\n\n// ENVIRONMENT\nexport {self, window, global, document, process, console} from './lib/globals';\nexport {default as isBrowser, isBrowserMainThread} from './lib/is-browser';\nexport {default as getBrowser, isMobile} from './lib/get-browser';\nexport {default as isElectron} from './lib/is-electron';\n\n// ENVIRONMENT'S ASSERT IS 5-15KB, SO WE PROVIDE OUR OWN\nexport {default as assert} from './utils/assert';\n"], "file": "index.js"}