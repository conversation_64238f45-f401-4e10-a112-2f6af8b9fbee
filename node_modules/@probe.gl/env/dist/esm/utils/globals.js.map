{"version": 3, "sources": ["../../../src/utils/globals.ts"], "names": ["checkIfBrowser", "self", "window", "global", "document", "process", "console", "VERSION", "__VERSION__", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAAA,OAAOA,cAAP,MAA2B,mBAA3B;AAEA,SAAQC,IAAR,EAAcC,MAAd,EAAsBC,MAAtB,EAA8BC,QAA9B,EAAwCC,OAAxC,EAAiDC,OAAjD,QAA+D,gBAA/D;AAIA,OAAO,MAAMC,OAAO,GAAG,OAAOC,WAAP,KAAuB,WAAvB,GAAqCA,WAArC,GAAmD,qBAAnE;AAEP,OAAO,MAAMC,SAAS,GAAGT,cAAc,EAAhC", "sourcesContent": ["import checkIfBrowser from '../lib/is-browser';\n\nexport {self, window, global, document, process, console} from '../lib/globals';\n\n// Extract injected version from package.json (injected by babel plugin)\n// @ts-expect-error\nexport const VERSION = typeof __VERSION__ !== 'undefined' ? __VERSION__ : 'untranspiled source';\n\nexport const isBrowser = checkIfBrowser();\n"], "file": "globals.js"}