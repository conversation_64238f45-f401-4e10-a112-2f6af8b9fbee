{"version": 3, "sources": ["../../../src/lib/get-browser.ts"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "isElectron", "window", "globalThis", "isMobile", "orientation", "<PERSON><PERSON><PERSON><PERSON>", "mockUserAgent", "navigator_", "navigator", "userAgent", "indexOf", "isMSIE", "isTrident", "chrome", "safari", "mozInnerScreenX"], "mappings": "AAuBA,OAAOA,SAAP,MAAsB,cAAtB;AACA,OAAOC,UAAP,MAAuB,eAAvB;AAEA,MAAMC,MAAM,GAAGC,UAAf;AAEA,OAAO,SAASC,QAAT,GAA6B;AAClC,SAAO,OAAOF,MAAM,CAACG,WAAd,KAA8B,WAArC;AACD;AAKD,eAAe,SAASC,UAAT,CACbC,aADa,EAEsE;AACnF,MAAI,CAACA,aAAD,IAAkB,CAACP,SAAS,EAAhC,EAAoC;AAClC,WAAO,MAAP;AACD;;AAED,MAAIC,UAAU,CAACM,aAAD,CAAd,EAA+B;AAC7B,WAAO,UAAP;AACD;;AAED,QAAMC,UAAU,GAAG,OAAOC,SAAP,KAAqB,WAArB,GAAmCA,SAAnC,GAA+C,EAAlE;AAEA,QAAMC,SAAS,GAAGH,aAAa,IAAIC,UAAU,CAACE,SAA5B,IAAyC,EAA3D;;AAIA,MAAIA,SAAS,CAACC,OAAV,CAAkB,MAAlB,IAA4B,CAAC,CAAjC,EAAoC;AAClC,WAAO,MAAP;AACD;;AACD,QAAMC,MAAM,GAAGF,SAAS,CAACC,OAAV,CAAkB,OAAlB,MAA+B,CAAC,CAA/C;AACA,QAAME,SAAS,GAAGH,SAAS,CAACC,OAAV,CAAkB,UAAlB,MAAkC,CAAC,CAArD;;AACA,MAAIC,MAAM,IAAIC,SAAd,EAAyB;AACvB,WAAO,IAAP;AACD;;AACD,MAAIX,MAAM,CAACY,MAAX,EAAmB;AACjB,WAAO,QAAP;AACD;;AACD,MAAIZ,MAAM,CAACa,MAAX,EAAmB;AACjB,WAAO,QAAP;AACD;;AACD,MAAIb,MAAM,CAACc,eAAX,EAA4B;AAC1B,WAAO,SAAP;AACD;;AACD,SAAO,SAAP;AACD", "sourcesContent": ["// Copyright (c) 2017 Uber Technologies, Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n// This function is needed in initialization stages,\n// make sure it can be imported in isolation\n\nimport isBrowser from './is-browser';\nimport isElectron from './is-electron';\n\nconst window = globalThis;\n\nexport function isMobile(): boolean {\n  return typeof window.orientation !== 'undefined';\n}\n\n// Simple browser detection\n// `mockUserAgent` parameter allows user agent to be overridden for testing\n/* eslint-disable complexity */\nexport default function getBrowser(\n  mockUserAgent?: string\n): 'Node' | 'Electron' | 'Chrome' | 'Firefox' | 'Safari' | 'Edge' | 'IE' | 'Unknown' {\n  if (!mockUserAgent && !isBrowser()) {\n    return 'Node';\n  }\n\n  if (isElectron(mockUserAgent)) {\n    return 'Electron';\n  }\n\n  const navigator_ = typeof navigator !== 'undefined' ? navigator : {};\n  // @ts-expect-error\n  const userAgent = mockUserAgent || navigator_.userAgent || '';\n  // const appVersion = navigator_.appVersion || '';\n\n  // NOTE: Order of tests matter, as many agents list Chrome etc.\n  if (userAgent.indexOf('Edge') > -1) {\n    return 'Edge';\n  }\n  const isMSIE = userAgent.indexOf('MSIE ') !== -1;\n  const isTrident = userAgent.indexOf('Trident/') !== -1;\n  if (isMSIE || isTrident) {\n    return 'IE';\n  }\n  if (window.chrome) {\n    return 'Chrome';\n  }\n  if (window.safari) {\n    return 'Safari';\n  }\n  if (window.mozInnerScreenX) {\n    return 'Firefox';\n  }\n  return 'Unknown';\n}\n"], "file": "get-browser.js"}