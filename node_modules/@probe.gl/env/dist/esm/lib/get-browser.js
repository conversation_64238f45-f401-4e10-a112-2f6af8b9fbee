import isBrowser from './is-browser';
import isElectron from './is-electron';
const window = globalThis;
export function isMobile() {
  return typeof window.orientation !== 'undefined';
}
export default function getBrowser(mockUserAgent) {
  if (!mockUserAgent && !isBrowser()) {
    return 'Node';
  }

  if (isElectron(mockUserAgent)) {
    return 'Electron';
  }

  const navigator_ = typeof navigator !== 'undefined' ? navigator : {};
  const userAgent = mockUserAgent || navigator_.userAgent || '';

  if (userAgent.indexOf('Edge') > -1) {
    return 'Edge';
  }

  const isMSIE = userAgent.indexOf('MSIE ') !== -1;
  const isTrident = userAgent.indexOf('Trident/') !== -1;

  if (isMSIE || isTrident) {
    return 'IE';
  }

  if (window.chrome) {
    return 'Chrome';
  }

  if (window.safari) {
    return 'Safari';
  }

  if (window.mozInnerScreenX) {
    return 'Firefox';
  }

  return 'Unknown';
}
//# sourceMappingURL=get-browser.js.map