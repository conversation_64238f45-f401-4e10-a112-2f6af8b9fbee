{"version": 3, "sources": ["../../../src/lib/globals.ts"], "names": ["globals", "self", "window", "global", "document", "process", "global_", "globalThis", "self_", "window_", "document_", "process_", "console_", "console"], "mappings": "AACA,MAAMA,OAAO,GAAG;AACdC,EAAAA,IAAI,EAAE,OAAOA,IAAP,KAAgB,WAAhB,IAA+BA,IADvB;AAEdC,EAAAA,MAAM,EAAE,OAAOA,MAAP,KAAkB,WAAlB,IAAiCA,MAF3B;AAGdC,EAAAA,MAAM,EAAE,OAAOA,MAAP,KAAkB,WAAlB,IAAiCA,MAH3B;AAIdC,EAAAA,QAAQ,EAAE,OAAOA,QAAP,KAAoB,WAApB,IAAmCA,QAJ/B;AAKdC,EAAAA,OAAO,EAAE,OAAOA,OAAP,KAAmB,QAAnB,IAA+BA;AAL1B,CAAhB;AAQA,MAAMC,OAAO,GAAGC,UAAhB;AACA,MAAMC,KAAK,GAAGR,OAAO,CAACC,IAAR,IAAgBD,OAAO,CAACE,MAAxB,IAAkCF,OAAO,CAACG,MAAxD;AACA,MAAMM,OAAO,GAAGT,OAAO,CAACE,MAAR,IAAkBF,OAAO,CAACC,IAA1B,IAAkCD,OAAO,CAACG,MAA1D;AACA,MAAMO,SAAS,GAAGV,OAAO,CAACI,QAAR,IAAoB,EAAtC;AACA,MAAMO,QAAQ,GAAGX,OAAO,CAACK,OAAR,IAAmB,EAApC;AACA,MAAMO,QAAQ,GAAGC,OAAjB;AAEA,SACEL,KAAK,IAAIP,IADX,EAEEQ,OAAO,IAAIP,MAFb,EAGEI,OAAO,IAAIH,MAHb,EAIEO,SAAS,IAAIN,QAJf,EAKEO,QAAQ,IAAIN,OALd,EAMEO,QAAQ,IAAIC,OANd", "sourcesContent": ["/* eslint-disable no-restricted-globals */\nconst globals = {\n  self: typeof self !== 'undefined' && self,\n  window: typeof window !== 'undefined' && window,\n  global: typeof global !== 'undefined' && global,\n  document: typeof document !== 'undefined' && document,\n  process: typeof process === 'object' && process\n};\n\nconst global_ = globalThis;\nconst self_ = globals.self || globals.window || globals.global;\nconst window_ = globals.window || globals.self || globals.global;\nconst document_ = globals.document || {};\nconst process_ = globals.process || {};\nconst console_ = console;\n\nexport {\n  self_ as self,\n  window_ as window,\n  global_ as global,\n  document_ as document,\n  process_ as process,\n  console_ as console\n};\n"], "file": "globals.js"}