{"version": 3, "sources": ["../../../src/lib/is-electron.ts"], "names": ["isElectron", "mockUserAgent", "window", "process", "type", "versions", "Boolean", "realUserAgent", "navigator", "userAgent", "indexOf"], "mappings": "AAGA,eAAe,SAASA,UAAT,CAAoBC,aAApB,EAAqD;AAElE,MACE,OAAOC,MAAP,KAAkB,WAAlB,IACA,OAAOA,MAAM,CAACC,OAAd,KAA0B,QAD1B,IAGAD,MAAM,CAACC,OAAP,CAAeC,IAAf,KAAwB,UAJ1B,EAKE;AACA,WAAO,IAAP;AACD;;AAED,MACE,OAAOD,OAAP,KAAmB,WAAnB,IACA,OAAOA,OAAO,CAACE,QAAf,KAA4B,QAD5B,IAGAC,OAAO,CAACH,OAAO,CAACE,QAAR,CAAiB,UAAjB,CAAD,CAJT,EAKE;AACA,WAAO,IAAP;AACD;;AAED,QAAME,aAAa,GACjB,OAAOC,SAAP,KAAqB,QAArB,IAAiC,OAAOA,SAAS,CAACC,SAAjB,KAA+B,QAAhE,IAA4ED,SAAS,CAACC,SADxF;AAEA,QAAMA,SAAS,GAAGR,aAAa,IAAIM,aAAnC;;AACA,MAAIE,SAAS,IAAIA,SAAS,CAACC,OAAV,CAAkB,UAAlB,KAAiC,CAAlD,EAAqD;AACnD,WAAO,IAAP;AACD;;AACD,SAAO,KAAP;AACD", "sourcesContent": ["// based on https://github.com/cheton/is-electron\n// https://github.com/electron/electron/issues/2288\n/* eslint-disable complexity */\nexport default function isElectron(mockUserAgent?: string): boolean {\n  // Renderer process\n  if (\n    typeof window !== 'undefined' &&\n    typeof window.process === 'object' &&\n    // @ts-expect-error\n    window.process.type === 'renderer'\n  ) {\n    return true;\n  }\n  // Main process\n  if (\n    typeof process !== 'undefined' &&\n    typeof process.versions === 'object' &&\n    // eslint-disable-next-line\n    Boolean(process.versions['electron'])\n  ) {\n    return true;\n  }\n  // Detect the user agent when the `nodeIntegration` option is set to true\n  const realUserAgent =\n    typeof navigator === 'object' && typeof navigator.userAgent === 'string' && navigator.userAgent;\n  const userAgent = mockUserAgent || realUserAgent;\n  if (userAgent && userAgent.indexOf('Electron') >= 0) {\n    return true;\n  }\n  return false;\n}\n"], "file": "is-electron.js"}