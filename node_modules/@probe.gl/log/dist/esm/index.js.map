{"version": 3, "sources": ["../../src/index.ts"], "names": ["Log", "id", "COLOR", "addColor", "leftPad", "rightPad", "autobind", "LocalStorage", "getHiResTimestamp"], "mappings": "AAAA,SAAQA,GAAR,QAAkB,OAAlB;AAGA,eAAe,IAAIA,GAAJ,CAAQ;AAACC,EAAAA,EAAE,EAAE;AAAL,CAAR,CAAf;AAGA,SAAQD,GAAR,QAAkB,OAAlB;AACA,SAAQE,KAAR,QAAoB,eAApB;AAGA,SAAQC,QAAR,QAAuB,eAAvB;AACA,SAAQC,OAAR,EAAiBC,QAAjB,QAAgC,oBAAhC;AACA,SAAQC,QAAR,QAAuB,kBAAvB;AACA,SAAQC,YAAR,QAA2B,uBAA3B;AACA,SAAQC,iBAAR,QAAgC,0BAAhC;AAEA,OAAO,QAAP", "sourcesContent": ["import {Log} from './log';\n\n// DEFAULT EXPORT IS A LOG INSTANCE\nexport default new Log({id: '@probe.gl/log'});\n\n// LOGGING\nexport {Log} from './log';\nexport {COLOR} from './utils/color';\n\n// UTILITIES\nexport {addColor} from './utils/color';\nexport {leftPad, rightPad} from './utils/formatters';\nexport {autobind} from './utils/autobind';\nexport {LocalStorage} from './utils/local-storage';\nexport {getHiResTimestamp} from './utils/hi-res-timestamp';\n\nimport './init';\n"], "file": "index.js"}