{"version": 3, "sources": ["../../src/log.ts"], "names": ["VERSION", "<PERSON><PERSON><PERSON><PERSON>", "LocalStorage", "formatImage", "formatTime", "leftPad", "addColor", "autobind", "assert", "getHiResTimestamp", "originalConsole", "debug", "console", "log", "info", "warn", "error", "DEFAULT_SETTINGS", "enabled", "level", "noop", "cache", "ONCE", "once", "Log", "constructor", "id", "userData", "_storage", "timeStamp", "Object", "seal", "newLevel", "setLevel", "getLevel", "isEnabled", "config", "getTotal", "Number", "_startTs", "toPrecision", "<PERSON><PERSON><PERSON><PERSON>", "_deltaTs", "priority", "newPriority", "getPriority", "enable", "setConfiguration", "get", "setting", "set", "value", "settings", "table", "condition", "message", "_getLogFunction", "arguments", "deprecated", "oldUsage", "newUsage", "removed", "probe", "logLevel", "time", "args", "columns", "tag", "getTableHeader", "image", "scale", "_shouldLog", "logImageInBrowser", "logImageInNode", "timeEnd", "group", "opts", "collapsed", "options", "normalizeArguments", "method", "groupCollapsed", "assign", "groupEnd", "withGroup", "func", "trace", "normalizeLogLevel", "total", "delta", "decorateMessage", "bind", "resolvedLevel", "isFinite", "Array", "from", "length", "shift", "undefined", "unshift", "messageType", "color", "background", "img", "Image", "onload", "src", "element", "nodeName", "toLowerCase", "toDataURL", "key", "title"], "mappings": ";AAGA,SAAQA,OAAR,EAAiBC,SAAjB,QAAiC,eAAjC;AACA,SAAQC,YAAR,QAA2B,uBAA3B;AACA,SAAQC,WAAR,EAAqBC,UAArB,EAAiCC,OAAjC,QAA+C,oBAA/C;AACA,SAAQC,QAAR,QAAuB,eAAvB;AACA,SAAQC,QAAR,QAAuB,kBAAvB;AACA,OAAOC,MAAP,MAAmB,gBAAnB;AACA,SAAQC,iBAAR,QAAgC,0BAAhC;AAGA,MAAMC,eAAe,GAAG;AACtBC,EAAAA,KAAK,EAAEV,SAAS,GAAGW,OAAO,CAACD,KAAR,IAAiBC,OAAO,CAACC,GAA5B,GAAkCD,OAAO,CAACC,GADpC;AAEtBA,EAAAA,GAAG,EAAED,OAAO,CAACC,GAFS;AAGtBC,EAAAA,IAAI,EAAEF,OAAO,CAACE,IAHQ;AAItBC,EAAAA,IAAI,EAAEH,OAAO,CAACG,IAJQ;AAKtBC,EAAAA,KAAK,EAAEJ,OAAO,CAACI;AALO,CAAxB;AA8BA,MAAMC,gBAAuC,GAAG;AAC9CC,EAAAA,OAAO,EAAE,IADqC;AAE9CC,EAAAA,KAAK,EAAE;AAFuC,CAAhD;;AAKA,SAASC,IAAT,GAAgB,CAAE;;AAElB,MAAMC,KAAK,GAAG,EAAd;AACA,MAAMC,IAAI,GAAG;AAACC,EAAAA,IAAI,EAAE;AAAP,CAAb;AASA,OAAO,MAAMC,GAAN,CAAU;AAafC,EAAAA,WAAW,GAAkB;AAAA,QAAjB;AAACC,MAAAA;AAAD,KAAiB,uEAAV;AAACA,MAAAA,EAAE,EAAE;AAAL,KAAU;;AAAA;;AAAA,qCATX1B,OASW;;AAAA,sCARVS,iBAAiB,EAQP;;AAAA,sCAPVA,iBAAiB,EAOP;;AAAA;;AAAA,sCALlB,EAKkB;;AAAA,kDAFE,CAEF;;AAC3B,SAAKiB,EAAL,GAAUA,EAAV;AACA,SAAKC,QAAL,GAAgB,EAAhB;AACA,SAAKC,QAAL,GAAgB,IAAI1B,YAAJ,mBAA8C,KAAKwB,EAAnD,SAA2DT,gBAA3D,CAAhB;AAEA,SAAKY,SAAL,WAAkB,KAAKH,EAAvB;AAEAnB,IAAAA,QAAQ,CAAC,IAAD,CAAR;AACAuB,IAAAA,MAAM,CAACC,IAAP,CAAY,IAAZ;AACD;;AAEQ,MAALZ,KAAK,CAACa,QAAD,EAAmB;AAC1B,SAAKC,QAAL,CAAcD,QAAd;AACD;;AAEQ,MAALb,KAAK,GAAW;AAClB,WAAO,KAAKe,QAAL,EAAP;AACD;;AAEDC,EAAAA,SAAS,GAAY;AACnB,WAAO,KAAKP,QAAL,CAAcQ,MAAd,CAAqBlB,OAA5B;AACD;;AAEDgB,EAAAA,QAAQ,GAAW;AACjB,WAAO,KAAKN,QAAL,CAAcQ,MAAd,CAAqBjB,KAA5B;AACD;;AAGDkB,EAAAA,QAAQ,GAAW;AACjB,WAAOC,MAAM,CAAC,CAAC7B,iBAAiB,KAAK,KAAK8B,QAA5B,EAAsCC,WAAtC,CAAkD,EAAlD,CAAD,CAAb;AACD;;AAGDC,EAAAA,QAAQ,GAAW;AACjB,WAAOH,MAAM,CAAC,CAAC7B,iBAAiB,KAAK,KAAKiC,QAA5B,EAAsCF,WAAtC,CAAkD,EAAlD,CAAD,CAAb;AACD;;AAGW,MAARG,QAAQ,CAACC,WAAD,EAAsB;AAChC,SAAKzB,KAAL,GAAayB,WAAb;AACD;;AAGW,MAARD,QAAQ,GAAW;AACrB,WAAO,KAAKxB,KAAZ;AACD;;AAGD0B,EAAAA,WAAW,GAAW;AACpB,WAAO,KAAK1B,KAAZ;AACD;;AAID2B,EAAAA,MAAM,GAAgC;AAAA,QAA/B5B,OAA+B,uEAAZ,IAAY;;AACpC,SAAKU,QAAL,CAAcmB,gBAAd,CAA+B;AAAC7B,MAAAA;AAAD,KAA/B;;AACA,WAAO,IAAP;AACD;;AAEDe,EAAAA,QAAQ,CAACd,KAAD,EAAsB;AAC5B,SAAKS,QAAL,CAAcmB,gBAAd,CAA+B;AAAC5B,MAAAA;AAAD,KAA/B;;AACA,WAAO,IAAP;AACD;;AAGD6B,EAAAA,GAAG,CAACC,OAAD,EAAuB;AACxB,WAAO,KAAKrB,QAAL,CAAcQ,MAAd,CAAqBa,OAArB,CAAP;AACD;;AAGDC,EAAAA,GAAG,CAACD,OAAD,EAAkBE,KAAlB,EAAoC;AACrC,SAAKvB,QAAL,CAAcmB,gBAAd,CAA+B;AAAC,OAACE,OAAD,GAAWE;AAAZ,KAA/B;AACD;;AAGDC,EAAAA,QAAQ,GAAS;AACf,QAAIxC,OAAO,CAACyC,KAAZ,EAAmB;AACjBzC,MAAAA,OAAO,CAACyC,KAAR,CAAc,KAAKzB,QAAL,CAAcQ,MAA5B;AACD,KAFD,MAEO;AACLxB,MAAAA,OAAO,CAACC,GAAR,CAAY,KAAKe,QAAL,CAAcQ,MAA1B;AACD;AACF;;AAID5B,EAAAA,MAAM,CAAC8C,SAAD,EAAqBC,OAArB,EAA0D;AAC9D/C,IAAAA,MAAM,CAAC8C,SAAD,EAAYC,OAAZ,CAAN;AACD;;AAIDxC,EAAAA,IAAI,CAACwC,OAAD,EAA+B;AACjC,WAAO,KAAKC,eAAL,CAAqB,CAArB,EAAwBD,OAAxB,EAAiC7C,eAAe,CAACK,IAAjD,EAAuD0C,SAAvD,EAAkEnC,IAAlE,CAAP;AACD;;AAIDN,EAAAA,KAAK,CAACuC,OAAD,EAA+B;AAClC,WAAO,KAAKC,eAAL,CAAqB,CAArB,EAAwBD,OAAxB,EAAiC7C,eAAe,CAACM,KAAjD,EAAwDyC,SAAxD,CAAP;AACD;;AAGDC,EAAAA,UAAU,CAACC,QAAD,EAAmBC,QAAnB,EAAkD;AAC1D,WAAO,KAAK7C,IAAL,YAAe4C,QAAf,0EACiBC,QADjB,eAAP;AAED;;AAGDC,EAAAA,OAAO,CAACF,QAAD,EAAmBC,QAAnB,EAAkD;AACvD,WAAO,KAAK5C,KAAL,YAAgB2C,QAAhB,sCAAsDC,QAAtD,eAAP;AACD;;AAMDE,EAAAA,KAAK,CAACC,QAAD,EAAWR,OAAX,EAAkC;AACrC,WAAO,KAAKC,eAAL,CAAqBO,QAArB,EAA+BR,OAA/B,EAAwC7C,eAAe,CAACG,GAAxD,EAA6D4C,SAA7D,EAAwE;AAC7EO,MAAAA,IAAI,EAAE,IADuE;AAE7EzC,MAAAA,IAAI,EAAE;AAFuE,KAAxE,CAAP;AAID;;AAIDV,EAAAA,GAAG,CAACkD,QAAD,EAAWR,OAAX,EAAkC;AACnC,WAAO,KAAKC,eAAL,CAAqBO,QAArB,EAA+BR,OAA/B,EAAwC7C,eAAe,CAACC,KAAxD,EAA+D8C,SAA/D,CAAP;AACD;;AAID3C,EAAAA,IAAI,CAACiD,QAAD,EAAWR,OAAX,EAAkC;AACpC,WAAO,KAAKC,eAAL,CAAqBO,QAArB,EAA+BR,OAA/B,EAAwC3C,OAAO,CAACE,IAAhD,EAAsD2C,SAAtD,CAAP;AACD;;AAIDlC,EAAAA,IAAI,CAACwC,QAAD,EAAWR,OAAX,EAA8B;AAAA,sCAANU,IAAM;AAANA,MAAAA,IAAM;AAAA;;AAChC,WAAO,KAAKT,eAAL,CACLO,QADK,EAELR,OAFK,EAGL7C,eAAe,CAACC,KAAhB,IAAyBD,eAAe,CAACI,IAHpC,EAIL2C,SAJK,EAKLnC,IALK,CAAP;AAOD;;AAGD+B,EAAAA,KAAK,CAACU,QAAD,EAAWV,KAAX,EAAmBa,OAAnB,EAA0C;AAC7C,QAAIb,KAAJ,EAAW;AAET,aAAO,KAAKG,eAAL,CAAqBO,QAArB,EAA+BV,KAA/B,EAAsCzC,OAAO,CAACyC,KAAR,IAAiBjC,IAAvD,EAA6D8C,OAAO,IAAI,CAACA,OAAD,CAAxE,EAAmF;AACxFC,QAAAA,GAAG,EAAEC,cAAc,CAACf,KAAD;AADqE,OAAnF,CAAP;AAGD;;AACD,WAAOjC,IAAP;AACD;;AAGDiD,EAAAA,KAAK,OAAoE;AAAA,QAAnE;AAACN,MAAAA,QAAD;AAAWpB,MAAAA,QAAX;AAAqB0B,MAAAA,KAArB;AAA4Bd,MAAAA,OAAO,GAAG,EAAtC;AAA0Ce,MAAAA,KAAK,GAAG;AAAlD,KAAmE;;AACvE,QAAI,CAAC,KAAKC,UAAL,CAAgBR,QAAQ,IAAIpB,QAA5B,CAAL,EAA4C;AAC1C,aAAOvB,IAAP;AACD;;AACD,WAAOnB,SAAS,GACZuE,iBAAiB,CAAC;AAACH,MAAAA,KAAD;AAAQd,MAAAA,OAAR;AAAiBe,MAAAA;AAAjB,KAAD,CADL,GAEZG,cAAc,CAAC;AAACJ,MAAAA,KAAD;AAAQd,MAAAA,OAAR;AAAiBe,MAAAA;AAAjB,KAAD,CAFlB;AAGD;;AAEDN,EAAAA,IAAI,CAACD,QAAD,EAAWR,OAAX,EAAoB;AACtB,WAAO,KAAKC,eAAL,CAAqBO,QAArB,EAA+BR,OAA/B,EAAwC3C,OAAO,CAACoD,IAAR,GAAepD,OAAO,CAACoD,IAAvB,GAA8BpD,OAAO,CAACE,IAA9E,CAAP;AACD;;AAED4D,EAAAA,OAAO,CAACX,QAAD,EAAWR,OAAX,EAAoB;AACzB,WAAO,KAAKC,eAAL,CACLO,QADK,EAELR,OAFK,EAGL3C,OAAO,CAAC8D,OAAR,GAAkB9D,OAAO,CAAC8D,OAA1B,GAAoC9D,OAAO,CAACE,IAHvC,CAAP;AAKD;;AAEDe,EAAAA,SAAS,CAACkC,QAAD,EAAWR,OAAX,EAAqB;AAC5B,WAAO,KAAKC,eAAL,CAAqBO,QAArB,EAA+BR,OAA/B,EAAwC3C,OAAO,CAACiB,SAAR,IAAqBT,IAA7D,CAAP;AACD;;AAEDuD,EAAAA,KAAK,CAACZ,QAAD,EAAWR,OAAX,EAA+C;AAAA,QAA3BqB,IAA2B,uEAApB;AAACC,MAAAA,SAAS,EAAE;AAAZ,KAAoB;AAClD,UAAMC,OAAO,GAAGC,kBAAkB,CAAC;AAAChB,MAAAA,QAAD;AAAWR,MAAAA,OAAX;AAAoBqB,MAAAA;AAApB,KAAD,CAAlC;AACA,UAAM;AAACC,MAAAA;AAAD,QAAcD,IAApB;AAEAE,IAAAA,OAAO,CAACE,MAAR,GAAiB,CAACH,SAAS,GAAGjE,OAAO,CAACqE,cAAX,GAA4BrE,OAAO,CAAC+D,KAA9C,KAAwD/D,OAAO,CAACE,IAAjF;AAEA,WAAO,KAAK0C,eAAL,CAAqBsB,OAArB,CAAP;AACD;;AAEDG,EAAAA,cAAc,CAAClB,QAAD,EAAWR,OAAX,EAA+B;AAAA,QAAXqB,IAAW,uEAAJ,EAAI;AAC3C,WAAO,KAAKD,KAAL,CAAWZ,QAAX,EAAqBR,OAArB,EAA8BzB,MAAM,CAACoD,MAAP,CAAc,EAAd,EAAkBN,IAAlB,EAAwB;AAACC,MAAAA,SAAS,EAAE;AAAZ,KAAxB,CAA9B,CAAP;AACD;;AAEDM,EAAAA,QAAQ,CAACpB,QAAD,EAAW;AACjB,WAAO,KAAKP,eAAL,CAAqBO,QAArB,EAA+B,EAA/B,EAAmCnD,OAAO,CAACuE,QAAR,IAAoB/D,IAAvD,CAAP;AACD;;AAIDgE,EAAAA,SAAS,CAACrB,QAAD,EAAmBR,OAAnB,EAAoC8B,IAApC,EAA0D;AACjE,SAAKV,KAAL,CAAWZ,QAAX,EAAqBR,OAArB;;AAEA,QAAI;AACF8B,MAAAA,IAAI;AACL,KAFD,SAEU;AACR,WAAKF,QAAL,CAAcpB,QAAd;AACD;AACF;;AAEDuB,EAAAA,KAAK,GAAS;AACZ,QAAI1E,OAAO,CAAC0E,KAAZ,EAAmB;AACjB1E,MAAAA,OAAO,CAAC0E,KAAR;AACD;AACF;;AAKDf,EAAAA,UAAU,CAACR,QAAD,EAA6B;AACrC,WAAO,KAAK5B,SAAL,MAAoB,KAAKD,QAAL,MAAmBqD,iBAAiB,CAACxB,QAAD,CAA/D;AACD;;AAEDP,EAAAA,eAAe,CACbO,QADa,EAEbR,OAFa,EAGbyB,MAHa,EAIbf,IAJa,EAKbW,IALa,EAMA;AACb,QAAI,KAAKL,UAAL,CAAgBR,QAAhB,CAAJ,EAA+B;AAE7Ba,MAAAA,IAAI,GAAGG,kBAAkB,CAAC;AAAChB,QAAAA,QAAD;AAAWR,QAAAA,OAAX;AAAoBU,QAAAA,IAApB;AAA0BW,QAAAA;AAA1B,OAAD,CAAzB;AACAI,MAAAA,MAAM,GAAGA,MAAM,IAAIJ,IAAI,CAACI,MAAxB;AACAxE,MAAAA,MAAM,CAACwE,MAAD,CAAN;AAEAJ,MAAAA,IAAI,CAACY,KAAL,GAAa,KAAKnD,QAAL,EAAb;AACAuC,MAAAA,IAAI,CAACa,KAAL,GAAa,KAAKhD,QAAL,EAAb;AAEA,WAAKC,QAAL,GAAgBjC,iBAAiB,EAAjC;AAEA,YAAM0D,GAAG,GAAGS,IAAI,CAACT,GAAL,IAAYS,IAAI,CAACrB,OAA7B;;AAEA,UAAIqB,IAAI,CAACrD,IAAT,EAAe;AACb,YAAI,CAACF,KAAK,CAAC8C,GAAD,CAAV,EAAiB;AACf9C,UAAAA,KAAK,CAAC8C,GAAD,CAAL,GAAa1D,iBAAiB,EAA9B;AACD,SAFD,MAEO;AACL,iBAAOW,IAAP;AACD;AACF;;AAODmC,MAAAA,OAAO,GAAGmC,eAAe,CAAC,KAAKhE,EAAN,EAAUkD,IAAI,CAACrB,OAAf,EAAwBqB,IAAxB,CAAzB;AAGA,aAAOI,MAAM,CAACW,IAAP,CAAY/E,OAAZ,EAAqB2C,OAArB,EAA8B,GAAGqB,IAAI,CAACX,IAAtC,CAAP;AACD;;AACD,WAAO7C,IAAP;AACD;;AAtRc;;gBAAJI,G,aACMxB,O;;AA+RnB,SAASuF,iBAAT,CAA2BxB,QAA3B,EAAsD;AACpD,MAAI,CAACA,QAAL,EAAe;AACb,WAAO,CAAP;AACD;;AACD,MAAI6B,aAAJ;;AAEA,UAAQ,OAAO7B,QAAf;AACE,SAAK,QAAL;AACE6B,MAAAA,aAAa,GAAG7B,QAAhB;AACA;;AAEF,SAAK,QAAL;AAIE6B,MAAAA,aAAa,GAAG7B,QAAQ,CAACA,QAAT,IAAqBA,QAAQ,CAACpB,QAA9B,IAA0C,CAA1D;AACA;;AAEF;AACE,aAAO,CAAP;AAbJ;;AAgBAnC,EAAAA,MAAM,CAAC8B,MAAM,CAACuD,QAAP,CAAgBD,aAAhB,KAAkCA,aAAa,IAAI,CAApD,CAAN;AAEA,SAAOA,aAAP;AACD;;AASD,OAAO,SAASb,kBAAT,CAA4BH,IAA5B,EAUL;AACA,QAAM;AAACb,IAAAA,QAAD;AAAWR,IAAAA;AAAX,MAAsBqB,IAA5B;AACAA,EAAAA,IAAI,CAACb,QAAL,GAAgBwB,iBAAiB,CAACxB,QAAD,CAAjC;AAOA,QAAME,IAAW,GAAGW,IAAI,CAACX,IAAL,GAAY6B,KAAK,CAACC,IAAN,CAAWnB,IAAI,CAACX,IAAhB,CAAZ,GAAoC,EAAxD;;AAGA,SAAOA,IAAI,CAAC+B,MAAL,IAAe/B,IAAI,CAACgC,KAAL,OAAiB1C,OAAvC,EAAgD,CAAE;;AAElD,UAAQ,OAAOQ,QAAf;AACE,SAAK,QAAL;AACA,SAAK,UAAL;AACE,UAAIR,OAAO,KAAK2C,SAAhB,EAA2B;AACzBjC,QAAAA,IAAI,CAACkC,OAAL,CAAa5C,OAAb;AACD;;AACDqB,MAAAA,IAAI,CAACrB,OAAL,GAAeQ,QAAf;AACA;;AAEF,SAAK,QAAL;AACEjC,MAAAA,MAAM,CAACoD,MAAP,CAAcN,IAAd,EAAoBb,QAApB;AACA;;AAEF;AAbF;;AAiBA,MAAI,OAAOa,IAAI,CAACrB,OAAZ,KAAwB,UAA5B,EAAwC;AACtCqB,IAAAA,IAAI,CAACrB,OAAL,GAAeqB,IAAI,CAACrB,OAAL,EAAf;AACD;;AACD,QAAM6C,WAAW,GAAG,OAAOxB,IAAI,CAACrB,OAAhC;AAEA/C,EAAAA,MAAM,CAAC4F,WAAW,KAAK,QAAhB,IAA4BA,WAAW,KAAK,QAA7C,CAAN;AAGA,SAAOtE,MAAM,CAACoD,MAAP,CAAcN,IAAd,EAAoB;AAACX,IAAAA;AAAD,GAApB,EAA4BW,IAAI,CAACA,IAAjC,CAAP;AACD;;AAED,SAASc,eAAT,CAAyBhE,EAAzB,EAA6B6B,OAA7B,EAAsCqB,IAAtC,EAA4C;AAC1C,MAAI,OAAOrB,OAAP,KAAmB,QAAvB,EAAiC;AAC/B,UAAMS,IAAI,GAAGY,IAAI,CAACZ,IAAL,GAAY3D,OAAO,CAACD,UAAU,CAACwE,IAAI,CAACY,KAAN,CAAX,CAAnB,GAA8C,EAA3D;AACAjC,IAAAA,OAAO,GAAGqB,IAAI,CAACZ,IAAL,aAAetC,EAAf,eAAsBsC,IAAtB,eAA+BT,OAA/B,cAA8C7B,EAA9C,eAAqD6B,OAArD,CAAV;AACAA,IAAAA,OAAO,GAAGjD,QAAQ,CAACiD,OAAD,EAAUqB,IAAI,CAACyB,KAAf,EAAsBzB,IAAI,CAAC0B,UAA3B,CAAlB;AACD;;AACD,SAAO/C,OAAP;AACD;;AAGD,SAASkB,cAAT,QAA0D;AAAA,MAAlC;AAACJ,IAAAA,KAAD;AAAQd,IAAAA,OAAO,GAAG,EAAlB;AAAsBe,IAAAA,KAAK,GAAG;AAA9B,GAAkC;AACxD1D,EAAAA,OAAO,CAACG,IAAR,CAAa,SAAb;AACA,SAAOK,IAAP;AACD;;AAED,SAASoD,iBAAT,QAA6D;AAAA,MAAlC;AAACH,IAAAA,KAAD;AAAQd,IAAAA,OAAO,GAAG,EAAlB;AAAsBe,IAAAA,KAAK,GAAG;AAA9B,GAAkC;;AAC3D,MAAI,OAAOD,KAAP,KAAiB,QAArB,EAA+B;AAC7B,UAAMkC,GAAG,GAAG,IAAIC,KAAJ,EAAZ;;AACAD,IAAAA,GAAG,CAACE,MAAJ,GAAa,MAAM;AACjB,YAAMxC,IAAI,GAAG9D,WAAW,CAACoG,GAAD,EAAMhD,OAAN,EAAee,KAAf,CAAxB;AACA1D,MAAAA,OAAO,CAACC,GAAR,CAAY,GAAGoD,IAAf;AACD,KAHD;;AAIAsC,IAAAA,GAAG,CAACG,GAAJ,GAAUrC,KAAV;AACA,WAAOjD,IAAP;AACD;;AACD,QAAMuF,OAAO,GAAGtC,KAAK,CAACuC,QAAN,IAAkB,EAAlC;;AACA,MAAID,OAAO,CAACE,WAAR,OAA0B,KAA9B,EAAqC;AACnCjG,IAAAA,OAAO,CAACC,GAAR,CAAY,GAAGV,WAAW,CAACkE,KAAD,EAAQd,OAAR,EAAiBe,KAAjB,CAA1B;AACA,WAAOlD,IAAP;AACD;;AACD,MAAIuF,OAAO,CAACE,WAAR,OAA0B,QAA9B,EAAwC;AACtC,UAAMN,GAAG,GAAG,IAAIC,KAAJ,EAAZ;;AACAD,IAAAA,GAAG,CAACE,MAAJ,GAAa,MAAM7F,OAAO,CAACC,GAAR,CAAY,GAAGV,WAAW,CAACoG,GAAD,EAAMhD,OAAN,EAAee,KAAf,CAA1B,CAAnB;;AACAiC,IAAAA,GAAG,CAACG,GAAJ,GAAUrC,KAAK,CAACyC,SAAN,EAAV;AACA,WAAO1F,IAAP;AACD;;AACD,SAAOA,IAAP;AACD;;AAED,SAASgD,cAAT,CAAwBf,KAAxB,EAA8C;AAC5C,OAAK,MAAM0D,GAAX,IAAkB1D,KAAlB,EAAyB;AACvB,SAAK,MAAM2D,KAAX,IAAoB3D,KAAK,CAAC0D,GAAD,CAAzB,EAAgC;AAC9B,aAAOC,KAAK,IAAI,UAAhB;AACD;AACF;;AACD,SAAO,OAAP;AACD", "sourcesContent": ["// probe.gl, MIT license\n\n/* eslint-disable no-console */\nimport {VERSION, isBrowser} from '@probe.gl/env';\nimport {LocalStorage} from './utils/local-storage';\nimport {formatImage, formatTime, leftPad} from './utils/formatters';\nimport {addColor} from './utils/color';\nimport {autobind} from './utils/autobind';\nimport assert from './utils/assert';\nimport {getHiResTimestamp} from './utils/hi-res-timestamp';\n\n// Instrumentation in other packages may override console methods, so preserve them here\nconst originalConsole = {\n  debug: isBrowser ? console.debug || console.log : console.log,\n  log: console.log,\n  info: console.info,\n  warn: console.warn,\n  error: console.error\n};\n\ntype Table = Record<string, any>;\n\ntype LogFunction = () => void;\n\ntype LogOptions = {\n  method?;\n  time?;\n  total?: number;\n  delta?: number;\n  tag?: string;\n  message?: string;\n  once?: boolean;\n  nothrottle?: boolean;\n  args?: any;\n};\n\ntype LogSettings = {\n  enabled?: boolean;\n  level?: number;\n  [key: string]: any;\n};\n\nconst DEFAULT_SETTINGS: Required<LogSettings> = {\n  enabled: true,\n  level: 0\n};\n\nfunction noop() {} // eslint-disable-line @typescript-eslint/no-empty-function\n\nconst cache = {};\nconst ONCE = {once: true};\n\ntype LogConfiguration = {\n  enabled?: boolean;\n  level?: number;\n};\n\n/** A console wrapper */\n\nexport class Log {\n  static VERSION = VERSION;\n\n  id: string;\n  VERSION: string = VERSION;\n  _startTs: number = getHiResTimestamp();\n  _deltaTs: number = getHiResTimestamp();\n  _storage: LocalStorage<LogConfiguration>;\n  userData = {};\n\n  // TODO - fix support from throttling groups\n  LOG_THROTTLE_TIMEOUT: number = 0; // Time before throttled messages are logged again\n\n  constructor({id} = {id: ''}) {\n    this.id = id;\n    this.userData = {};\n    this._storage = new LocalStorage<LogConfiguration>(`__probe-${this.id}__`, DEFAULT_SETTINGS);\n\n    this.timeStamp(`${this.id} started`);\n\n    autobind(this);\n    Object.seal(this);\n  }\n\n  set level(newLevel: number) {\n    this.setLevel(newLevel);\n  }\n\n  get level(): number {\n    return this.getLevel();\n  }\n\n  isEnabled(): boolean {\n    return this._storage.config.enabled;\n  }\n\n  getLevel(): number {\n    return this._storage.config.level;\n  }\n\n  /** @return milliseconds, with fractions */\n  getTotal(): number {\n    return Number((getHiResTimestamp() - this._startTs).toPrecision(10));\n  }\n\n  /** @return milliseconds, with fractions */\n  getDelta(): number {\n    return Number((getHiResTimestamp() - this._deltaTs).toPrecision(10));\n  }\n\n  /** @deprecated use logLevel */\n  set priority(newPriority: number) {\n    this.level = newPriority;\n  }\n\n  /** @deprecated use logLevel */\n  get priority(): number {\n    return this.level;\n  }\n\n  /** @deprecated use logLevel */\n  getPriority(): number {\n    return this.level;\n  }\n\n  // Configure\n\n  enable(enabled: boolean = true): this {\n    this._storage.setConfiguration({enabled});\n    return this;\n  }\n\n  setLevel(level: number): this {\n    this._storage.setConfiguration({level});\n    return this;\n  }\n\n  /** return the current status of the setting */\n  get(setting: string): any {\n    return this._storage.config[setting];\n  }\n\n  // update the status of the setting\n  set(setting: string, value: any): void {\n    this._storage.setConfiguration({[setting]: value});\n  }\n\n  /** Logs the current settings as a table */\n  settings(): void {\n    if (console.table) {\n      console.table(this._storage.config);\n    } else {\n      console.log(this._storage.config);\n    }\n  }\n\n  // Unconditional logging\n\n  assert(condition: unknown, message?: string): asserts condition {\n    assert(condition, message);\n  }\n\n  /** Warn, but only once, no console flooding */\n  warn(message: string, ...args): LogFunction;\n  warn(message: string): LogFunction {\n    return this._getLogFunction(0, message, originalConsole.warn, arguments, ONCE);\n  }\n\n  /** Print an error */\n  error(message: string, ...args): LogFunction;\n  error(message: string): LogFunction {\n    return this._getLogFunction(0, message, originalConsole.error, arguments);\n  }\n\n  /** Print a deprecation warning */\n  deprecated(oldUsage: string, newUsage: string): LogFunction {\n    return this.warn(`\\`${oldUsage}\\` is deprecated and will be removed \\\nin a later version. Use \\`${newUsage}\\` instead`);\n  }\n\n  /** Print a removal warning */\n  removed(oldUsage: string, newUsage: string): LogFunction {\n    return this.error(`\\`${oldUsage}\\` has been removed. Use \\`${newUsage}\\` instead`);\n  }\n\n  // Conditional logging\n\n  /** Log to a group */\n  probe(logLevel, message?, ...args): LogFunction;\n  probe(logLevel, message?): LogFunction {\n    return this._getLogFunction(logLevel, message, originalConsole.log, arguments, {\n      time: true,\n      once: true\n    });\n  }\n\n  /** Log a debug message */\n  log(logLevel, message?, ...args): LogFunction;\n  log(logLevel, message?): LogFunction {\n    return this._getLogFunction(logLevel, message, originalConsole.debug, arguments);\n  }\n\n  /** Log a normal message */\n  info(logLevel, message?, ...args): LogFunction;\n  info(logLevel, message?): LogFunction {\n    return this._getLogFunction(logLevel, message, console.info, arguments);\n  }\n\n  /** Log a normal message, but only once, no console flooding */\n  once(logLevel, message?, ...args): LogFunction;\n  once(logLevel, message?, ...args) {\n    return this._getLogFunction(\n      logLevel,\n      message,\n      originalConsole.debug || originalConsole.info,\n      arguments,\n      ONCE\n    );\n  }\n\n  /** Logs an object as a table */\n  table(logLevel, table?, columns?): LogFunction {\n    if (table) {\n      // @ts-expect-error Not clear how this works, columns being passed as arguments\n      return this._getLogFunction(logLevel, table, console.table || noop, columns && [columns], {\n        tag: getTableHeader(table)\n      });\n    }\n    return noop;\n  }\n\n  /** logs an image under Chrome */\n  image({logLevel, priority, image, message = '', scale = 1}): LogFunction {\n    if (!this._shouldLog(logLevel || priority)) {\n      return noop;\n    }\n    return isBrowser\n      ? logImageInBrowser({image, message, scale})\n      : logImageInNode({image, message, scale});\n  }\n\n  time(logLevel, message) {\n    return this._getLogFunction(logLevel, message, console.time ? console.time : console.info);\n  }\n\n  timeEnd(logLevel, message) {\n    return this._getLogFunction(\n      logLevel,\n      message,\n      console.timeEnd ? console.timeEnd : console.info\n    );\n  }\n\n  timeStamp(logLevel, message?) {\n    return this._getLogFunction(logLevel, message, console.timeStamp || noop);\n  }\n\n  group(logLevel, message, opts = {collapsed: false}) {\n    const options = normalizeArguments({logLevel, message, opts});\n    const {collapsed} = opts;\n    // @ts-expect-error\n    options.method = (collapsed ? console.groupCollapsed : console.group) || console.info;\n\n    return this._getLogFunction(options);\n  }\n\n  groupCollapsed(logLevel, message, opts = {}) {\n    return this.group(logLevel, message, Object.assign({}, opts, {collapsed: true}));\n  }\n\n  groupEnd(logLevel) {\n    return this._getLogFunction(logLevel, '', console.groupEnd || noop);\n  }\n\n  // EXPERIMENTAL\n\n  withGroup(logLevel: number, message: string, func: Function): void {\n    this.group(logLevel, message)();\n\n    try {\n      func();\n    } finally {\n      this.groupEnd(logLevel)();\n    }\n  }\n\n  trace(): void {\n    if (console.trace) {\n      console.trace();\n    }\n  }\n\n  // PRIVATE METHODS\n\n  /** Deduces log level from a variety of arguments */\n  _shouldLog(logLevel: unknown): boolean {\n    return this.isEnabled() && this.getLevel() >= normalizeLogLevel(logLevel);\n  }\n\n  _getLogFunction(\n    logLevel: unknown,\n    message?: unknown,\n    method?: Function,\n    args?: IArguments,\n    opts?: LogOptions\n  ): LogFunction {\n    if (this._shouldLog(logLevel)) {\n      // normalized opts + timings\n      opts = normalizeArguments({logLevel, message, args, opts});\n      method = method || opts.method;\n      assert(method);\n\n      opts.total = this.getTotal();\n      opts.delta = this.getDelta();\n      // reset delta timer\n      this._deltaTs = getHiResTimestamp();\n\n      const tag = opts.tag || opts.message;\n\n      if (opts.once) {\n        if (!cache[tag]) {\n          cache[tag] = getHiResTimestamp();\n        } else {\n          return noop;\n        }\n      }\n\n      // TODO - Make throttling work with groups\n      // if (opts.nothrottle || !throttle(tag, this.LOG_THROTTLE_TIMEOUT)) {\n      //   return noop;\n      // }\n\n      message = decorateMessage(this.id, opts.message, opts);\n\n      // Bind console function so that it can be called after being returned\n      return method.bind(console, message, ...opts.args);\n    }\n    return noop;\n  }\n}\n\n/**\n * Get logLevel from first argument:\n * - log(logLevel, message, args) => logLevel\n * - log(message, args) => 0\n * - log({logLevel, ...}, message, args) => logLevel\n * - log({logLevel, message, args}) => logLevel\n */\nfunction normalizeLogLevel(logLevel: unknown): number {\n  if (!logLevel) {\n    return 0;\n  }\n  let resolvedLevel;\n\n  switch (typeof logLevel) {\n    case 'number':\n      resolvedLevel = logLevel;\n      break;\n\n    case 'object':\n      // Backward compatibility\n      // TODO - deprecate `priority`\n      // @ts-expect-error\n      resolvedLevel = logLevel.logLevel || logLevel.priority || 0;\n      break;\n\n    default:\n      return 0;\n  }\n  // 'log level must be a number'\n  assert(Number.isFinite(resolvedLevel) && resolvedLevel >= 0);\n\n  return resolvedLevel;\n}\n\n/**\n * \"Normalizes\" the various argument patterns into an object with known types\n * - log(logLevel, message, args) => {logLevel, message, args}\n * - log(message, args) => {logLevel: 0, message, args}\n * - log({logLevel, ...}, message, args) => {logLevel, message, args}\n * - log({logLevel, message, args}) => {logLevel, message, args}\n */\nexport function normalizeArguments(opts: {\n  logLevel;\n  message;\n  collapsed?: boolean;\n  args?: IArguments;\n  opts?;\n}): {\n  logLevel: number;\n  message: string;\n  args: any[];\n} {\n  const {logLevel, message} = opts;\n  opts.logLevel = normalizeLogLevel(logLevel);\n\n  // We use `arguments` instead of rest parameters (...args) because IE\n  // does not support the syntax. Rest parameters is transpiled to code with\n  // perf impact. Doing it here instead avoids constructing args when logging is\n  // disabled.\n  // TODO - remove when/if IE support is dropped\n  const args: any[] = opts.args ? Array.from(opts.args) : [];\n  // args should only contain arguments that appear after `message`\n  // eslint-disable-next-line no-empty\n  while (args.length && args.shift() !== message) {}\n\n  switch (typeof logLevel) {\n    case 'string':\n    case 'function':\n      if (message !== undefined) {\n        args.unshift(message);\n      }\n      opts.message = logLevel;\n      break;\n\n    case 'object':\n      Object.assign(opts, logLevel);\n      break;\n\n    default:\n  }\n\n  // Resolve functions into strings by calling them\n  if (typeof opts.message === 'function') {\n    opts.message = opts.message();\n  }\n  const messageType = typeof opts.message;\n  // 'log message must be a string' or object\n  assert(messageType === 'string' || messageType === 'object');\n\n  // original opts + normalized opts + opts arg + fixed up message\n  return Object.assign(opts, {args}, opts.opts);\n}\n\nfunction decorateMessage(id, message, opts) {\n  if (typeof message === 'string') {\n    const time = opts.time ? leftPad(formatTime(opts.total)) : '';\n    message = opts.time ? `${id}: ${time}  ${message}` : `${id}: ${message}`;\n    message = addColor(message, opts.color, opts.background);\n  }\n  return message;\n}\n\n/** @deprecated Function removed */\nfunction logImageInNode({image, message = '', scale = 1}) {\n  console.warn('removed');\n  return noop;\n}\n\nfunction logImageInBrowser({image, message = '', scale = 1}) {\n  if (typeof image === 'string') {\n    const img = new Image();\n    img.onload = () => {\n      const args = formatImage(img, message, scale);\n      console.log(...args);\n    };\n    img.src = image;\n    return noop;\n  }\n  const element = image.nodeName || '';\n  if (element.toLowerCase() === 'img') {\n    console.log(...formatImage(image, message, scale));\n    return noop;\n  }\n  if (element.toLowerCase() === 'canvas') {\n    const img = new Image();\n    img.onload = () => console.log(...formatImage(img, message, scale));\n    img.src = image.toDataURL();\n    return noop;\n  }\n  return noop;\n}\n\nfunction getTableHeader(table: Table): string {\n  for (const key in table) {\n    for (const title in table[key]) {\n      return title || 'untitled';\n    }\n  }\n  return 'empty';\n}\n"], "file": "log.js"}