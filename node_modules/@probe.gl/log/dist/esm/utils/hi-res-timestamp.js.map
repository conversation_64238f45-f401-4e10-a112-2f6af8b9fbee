{"version": 3, "sources": ["../../../src/utils/hi-res-timestamp.ts"], "names": ["window", "process", "<PERSON><PERSON><PERSON><PERSON>", "getHiResTimestamp", "timestamp", "performance", "now", "timeParts", "hrtime", "Date"], "mappings": "AAEA,SAAQA,MAAR,EAAgBC,OAAhB,EAAyBC,SAAzB,QAAyC,eAAzC;AAGA,OAAO,SAASC,iBAAT,GAA6B;AAClC,MAAIC,SAAJ;;AACA,MAAIF,SAAS,IAAI,iBAAiBF,MAAlC,EAA0C;AAAA;;AACxCI,IAAAA,SAAS,GAAGJ,MAAH,aAAGA,MAAH,8CAAGA,MAAM,CAAEK,WAAX,iFAAG,oBAAqBC,GAAxB,0DAAG,+CAAZ;AACD,GAFD,MAEO,IAAI,YAAYL,OAAhB,EAAyB;AAAA;;AAE9B,UAAMM,SAAS,GAAGN,OAAH,aAAGA,OAAH,0CAAGA,OAAO,CAAEO,MAAZ,oDAAG,qBAAAP,OAAO,CAAzB;AACAG,IAAAA,SAAS,GAAGG,SAAS,CAAC,CAAD,CAAT,GAAe,IAAf,GAAsBA,SAAS,CAAC,CAAD,CAAT,GAAe,GAAjD;AACD,GAJM,MAIA;AACLH,IAAAA,SAAS,GAAGK,IAAI,CAACH,GAAL,EAAZ;AACD;;AAED,SAAOF,SAAP;AACD", "sourcesContent": ["// probe.gl, MIT license\n\nimport {window, process, isBrowser} from '@probe.gl/env';\n\n/** Get best timer available. */\nexport function getHiResTimestamp() {\n  let timestamp;\n  if (isBrowser && 'performance' in window) {\n    timestamp = window?.performance?.now?.();\n  } else if ('hrtime' in process) {\n    // @ts-ignore\n    const timeParts = process?.hrtime?.();\n    timestamp = timeParts[0] * 1000 + timeParts[1] / 1e6;\n  } else {\n    timestamp = Date.now();\n  }\n\n  return timestamp;\n}\n"], "file": "hi-res-timestamp.js"}