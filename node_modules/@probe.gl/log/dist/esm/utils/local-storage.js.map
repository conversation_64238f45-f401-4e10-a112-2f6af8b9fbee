{"version": 3, "sources": ["../../../src/utils/local-storage.ts"], "names": ["getStorage", "type", "storage", "window", "x", "setItem", "removeItem", "e", "LocalStorage", "constructor", "id", "defaultConfig", "config", "_loadConfiguration", "getConfiguration", "setConfiguration", "configuration", "Object", "assign", "serialized", "JSON", "stringify", "serializedConfiguration", "getItem", "parse"], "mappings": ";;AAEA,SAASA,UAAT,CAAoBC,IAApB,EAAmC;AACjC,MAAI;AAEF,UAAMC,OAAgB,GAAGC,MAAM,CAACF,IAAD,CAA/B;AACA,UAAMG,CAAC,GAAG,kBAAV;AACAF,IAAAA,OAAO,CAACG,OAAR,CAAgBD,CAAhB,EAAmBA,CAAnB;AACAF,IAAAA,OAAO,CAACI,UAAR,CAAmBF,CAAnB;AACA,WAAOF,OAAP;AACD,GAPD,CAOE,OAAOK,CAAP,EAAU;AACV,WAAO,IAAP;AACD;AACF;;AAGD,OAAO,MAAMC,YAAN,CAA6C;AAKlDC,EAAAA,WAAW,CAACC,EAAD,EAAaC,aAAb,EAA8E;AAAA,QAAzBV,IAAyB,uEAAlB,gBAAkB;;AAAA;;AAAA;;AAAA;;AACvF,SAAKC,OAAL,GAAeF,UAAU,CAACC,IAAD,CAAzB;AACA,SAAKS,EAAL,GAAUA,EAAV;AACA,SAAKE,MAAL,GAAcD,aAAd;;AACA,SAAKE,kBAAL;AACD;;AAEDC,EAAAA,gBAAgB,GAA4B;AAC1C,WAAO,KAAKF,MAAZ;AACD;;AAEDG,EAAAA,gBAAgB,CAACC,aAAD,EAAqC;AACnDC,IAAAA,MAAM,CAACC,MAAP,CAAc,KAAKN,MAAnB,EAA2BI,aAA3B;;AACA,QAAI,KAAKd,OAAT,EAAkB;AAChB,YAAMiB,UAAU,GAAGC,IAAI,CAACC,SAAL,CAAe,KAAKT,MAApB,CAAnB;AACA,WAAKV,OAAL,CAAaG,OAAb,CAAqB,KAAKK,EAA1B,EAA8BS,UAA9B;AACD;AACF;;AAGDN,EAAAA,kBAAkB,GAAG;AACnB,QAAIG,aAAa,GAAG,EAApB;;AACA,QAAI,KAAKd,OAAT,EAAkB;AAChB,YAAMoB,uBAAuB,GAAG,KAAKpB,OAAL,CAAaqB,OAAb,CAAqB,KAAKb,EAA1B,CAAhC;AACAM,MAAAA,aAAa,GAAGM,uBAAuB,GAAGF,IAAI,CAACI,KAAL,CAAWF,uBAAX,CAAH,GAAyC,EAAhF;AACD;;AACDL,IAAAA,MAAM,CAACC,MAAP,CAAc,KAAKN,MAAnB,EAA2BI,aAA3B;AACA,WAAO,IAAP;AACD;;AAjCiD", "sourcesContent": ["// probe.gl, MIT license\n\nfunction getStorage(type): Storage {\n  try {\n    // @ts-expect-error\n    const storage: Storage = window[type];\n    const x = '__storage_test__';\n    storage.setItem(x, x);\n    storage.removeItem(x);\n    return storage;\n  } catch (e) {\n    return null;\n  }\n}\n\n// Store keys in local storage via simple interface\nexport class LocalStorage<Configuration extends {}> {\n  storage: Storage;\n  id: string;\n  config: Required<Configuration>;\n\n  constructor(id: string, defaultConfig: Required<Configuration>, type = 'sessionStorage') {\n    this.storage = getStorage(type);\n    this.id = id;\n    this.config = defaultConfig;\n    this._loadConfiguration();\n  }\n\n  getConfiguration(): Required<Configuration> {\n    return this.config;\n  }\n\n  setConfiguration(configuration: Configuration): void {\n    Object.assign(this.config, configuration);\n    if (this.storage) {\n      const serialized = JSON.stringify(this.config);\n      this.storage.setItem(this.id, serialized);\n    }\n  }\n\n  // Get config from persistent store, if available\n  _loadConfiguration() {\n    let configuration = {};\n    if (this.storage) {\n      const serializedConfiguration = this.storage.getItem(this.id);\n      configuration = serializedConfiguration ? JSON.parse(serializedConfiguration) : {};\n    }\n    Object.assign(this.config, configuration);\n    return this;\n  }\n}\n"], "file": "local-storage.js"}