{"version": 3, "sources": ["../../../src/utils/color.ts"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "COLOR", "getColor", "color", "toUpperCase", "WHITE", "addColor", "string", "background"], "mappings": "AAAA,SAAQA,SAAR,QAAwB,eAAxB;AAEA,WAAYC,KAAZ;;WAAYA,K;AAAAA,EAAAA,K,CAAAA,K;AAAAA,EAAAA,K,CAAAA,K;AAAAA,EAAAA,K,CAAAA,K;AAAAA,EAAAA,K,CAAAA,K;AAAAA,EAAAA,K,CAAAA,K;AAAAA,EAAAA,K,CAAAA,K;AAAAA,EAAAA,K,CAAAA,K;AAAAA,EAAAA,K,CAAAA,K;AAAAA,EAAAA,K,CAAAA,K;AAAAA,EAAAA,K,CAAAA,K;AAAAA,EAAAA,K,CAAAA,K;AAAAA,EAAAA,K,CAAAA,K;AAAAA,EAAAA,K,CAAAA,K;AAAAA,EAAAA,K,CAAAA,K;AAAAA,EAAAA,K,CAAAA,K;AAAAA,EAAAA,K,CAAAA,K;GAAAA,K,KAAAA,K;;AAoBZ,SAASC,QAAT,CAAkBC,KAAlB,EAAiC;AAC/B,SAAO,OAAOA,KAAP,KAAiB,QAAjB,GAA4BF,KAAK,CAACE,KAAK,CAACC,WAAN,EAAD,CAAL,IAA8BH,KAAK,CAACI,KAAhE,GAAwEF,KAA/E;AACD;;AAED,OAAO,SAASG,QAAT,CAAkBC,MAAlB,EAA0BJ,KAA1B,EAAiCK,UAAjC,EAAsD;AAC3D,MAAI,CAACR,SAAD,IAAc,OAAOO,MAAP,KAAkB,QAApC,EAA8C;AAC5C,QAAIJ,KAAJ,EAAW;AACTA,MAAAA,KAAK,GAAGD,QAAQ,CAACC,KAAD,CAAhB;AACAI,MAAAA,MAAM,kBAAaJ,KAAb,cAAsBI,MAAtB,aAAN;AACD;;AACD,QAAIC,UAAJ,EAAgB;AAEdL,MAAAA,KAAK,GAAGD,QAAQ,CAACM,UAAD,CAAhB;AACAD,MAAAA,MAAM,kBAAaC,UAAU,GAAG,EAA1B,cAAgCD,MAAhC,aAAN;AACD;AACF;;AACD,SAAOA,MAAP;AACD", "sourcesContent": ["import {isBrowser} from '@probe.gl/env';\n\nexport enum COLOR {\n  BLACK = 30,\n  RED = 31,\n  G<PERSON><PERSON> = 32,\n  YELLOW = 33,\n  BLUE = 34,\n  MAGENTA = 35,\n  CYAN = 36,\n  WHITE = 37,\n\n  BRIGHT_BLACK = 90,\n  <PERSON>IGHT_RED = 91,\n  BRIGHT_GREEN = 92,\n  BR<PERSON>HT_YELLOW = 93,\n  BRIGHT_BLUE = 94,\n  BRIGHT_MAGENTA = 95,\n  BRIGHT_CYAN = 96,\n  BRIGHT_WHITE = 97\n}\n\nfunction getColor(color): number {\n  return typeof color === 'string' ? COLOR[color.toUpperCase()] || COLOR.WHITE : color;\n}\n\nexport function addColor(string, color, background?): string {\n  if (!isBrowser && typeof string === 'string') {\n    if (color) {\n      color = getColor(color);\n      string = `\\u001b[${color}m${string}\\u001b[39m`;\n    }\n    if (background) {\n      // background colors values are +10\n      color = getColor(background);\n      string = `\\u001b[${background + 10}m${string}\\u001b[49m`;\n    }\n  }\n  return string;\n}\n"], "file": "color.js"}