{"version": 3, "sources": ["../../../src/utils/assert.ts"], "names": ["assert", "condition", "message", "Error"], "mappings": "AAAA,eAAe,SAASA,MAAT,CAAgBC,SAAhB,EAAoCC,OAApC,EAAyE;AACtF,MAAI,CAACD,SAAL,EAAgB;AACd,UAAM,IAAIE,KAAJ,CAAUD,OAAO,IAAI,kBAArB,CAAN;AACD;AACF", "sourcesContent": ["export default function assert(condition: unknown, message?: string): asserts condition {\n  if (!condition) {\n    throw new Error(message || '<PERSON><PERSON><PERSON> failed');\n  }\n}\n"], "file": "assert.js"}