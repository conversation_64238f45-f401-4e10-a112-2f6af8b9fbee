export declare enum COLOR {
    BLACK = 30,
    RED = 31,
    <PERSON><PERSON><PERSON> = 32,
    YELLOW = 33,
    BLUE = 34,
    MAGENTA = 35,
    CYAN = 36,
    WHITE = 37,
    BRIGHT_BLACK = 90,
    BRIGHT_RED = 91,
    BRIGHT_GREEN = 92,
    BRIGHT_YELLOW = 93,
    BRIGHT_BLUE = 94,
    BRIGHT_MAGENTA = 95,
    BRIGHT_CYAN = 96,
    BRIGHT_WHITE = 97
}
export declare function addColor(string: any, color: any, background?: any): string;
//# sourceMappingURL=color.d.ts.map