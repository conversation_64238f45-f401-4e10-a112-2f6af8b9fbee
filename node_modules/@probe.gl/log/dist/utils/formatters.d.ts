export declare function formatTime(ms: number): string;
export declare function leftPad(string: string, length?: number): string;
export declare function rightPad(string: string, length?: number): string;
export declare function formatValue(v: unknown, opts?: {
    isInteger?: boolean;
}): string;
/** Inspired by https://github.com/hughsk/console-image (MIT license) */
export declare function formatImage(image: any, message: any, scale: any, maxWidth?: number): string[];
//# sourceMappingURL=formatters.d.ts.map