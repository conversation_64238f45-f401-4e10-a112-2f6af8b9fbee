{"version": 3, "sources": ["../../../src/utils/formatters.ts"], "names": ["formatTime", "ms", "formatted", "toFixed", "leftPad", "string", "length", "<PERSON><PERSON><PERSON><PERSON>", "Math", "max", "repeat", "rightPad", "formatValue", "v", "opts", "EPSILON", "isInteger", "Array", "isArray", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "formatArrayValue", "Number", "isFinite", "String", "abs", "toPrecision", "decimal", "indexOf", "slice", "maxElts", "size", "i", "terminator", "formatImage", "image", "message", "scale", "max<PERSON><PERSON><PERSON>", "imageUrl", "src", "replace", "width", "min", "height", "style", "floor", "join"], "mappings": ";;;;;;;;;;;AACO,SAASA,UAAT,CAAoBC,EAApB,EAAwC;AAC7C,MAAIC,SAAJ;;AACA,MAAID,EAAE,GAAG,EAAT,EAAa;AACXC,IAAAA,SAAS,aAAMD,EAAE,CAACE,OAAH,CAAW,CAAX,CAAN,OAAT;AACD,GAFD,MAEO,IAAIF,EAAE,GAAG,GAAT,EAAc;AACnBC,IAAAA,SAAS,aAAMD,EAAE,CAACE,OAAH,CAAW,CAAX,CAAN,OAAT;AACD,GAFM,MAEA,IAAIF,EAAE,GAAG,IAAT,EAAe;AACpBC,IAAAA,SAAS,aAAMD,EAAE,CAACE,OAAH,CAAW,CAAX,CAAN,OAAT;AACD,GAFM,MAEA;AACLD,IAAAA,SAAS,aAAM,CAACD,EAAE,GAAG,IAAN,EAAYE,OAAZ,CAAoB,CAApB,CAAN,MAAT;AACD;;AACD,SAAOD,SAAP;AACD;;AAEM,SAASE,OAAT,CAAiBC,MAAjB,EAA6D;AAAA,MAA5BC,MAA4B,uEAAX,CAAW;AAClE,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAL,CAASH,MAAM,GAAGD,MAAM,CAACC,MAAzB,EAAiC,CAAjC,CAAlB;AACA,mBAAU,IAAII,MAAJ,CAAWH,SAAX,CAAV,SAAkCF,MAAlC;AACD;;AAEM,SAASM,QAAT,CAAkBN,MAAlB,EAA8D;AAAA,MAA5BC,MAA4B,uEAAX,CAAW;AACnE,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAL,CAASH,MAAM,GAAGD,MAAM,CAACC,MAAzB,EAAiC,CAAjC,CAAlB;AACA,mBAAUD,MAAV,SAAmB,IAAIK,MAAJ,CAAWH,SAAX,CAAnB;AACD;;AAEM,SAASK,WAAT,CAAqBC,CAArB,EAA2E;AAAA,MAA1CC,IAA0C,uEAAZ,EAAY;AAChF,MAAMC,OAAO,GAAG,KAAhB;AACA,wBAA4BD,IAA5B,CAAOE,SAAP;AAAA,MAAOA,SAAP,gCAAmB,KAAnB;;AACA,MAAIC,KAAK,CAACC,OAAN,CAAcL,CAAd,KAAoBM,WAAW,CAACC,MAAZ,CAAmBP,CAAnB,CAAxB,EAA+C;AAC7C,WAAOQ,gBAAgB,CAACR,CAAD,EAAIC,IAAJ,CAAvB;AACD;;AACD,MAAI,CAACQ,MAAM,CAACC,QAAP,CAAgBV,CAAhB,CAAL,EAAyB;AACvB,WAAOW,MAAM,CAACX,CAAD,CAAb;AACD;;AAED,MAAIL,IAAI,CAACiB,GAAL,CAASZ,CAAT,IAAcE,OAAlB,EAA2B;AACzB,WAAOC,SAAS,GAAG,GAAH,GAAS,IAAzB;AACD;;AACD,MAAIA,SAAJ,EAAe;AAEb,WAAOH,CAAC,CAACV,OAAF,CAAU,CAAV,CAAP;AACD;;AAED,MAAIK,IAAI,CAACiB,GAAL,CAASZ,CAAT,IAAc,GAAd,IAAqBL,IAAI,CAACiB,GAAL,CAASZ,CAAT,IAAc,KAAvC,EAA8C;AAE5C,WAAOA,CAAC,CAACV,OAAF,CAAU,CAAV,CAAP;AACD;;AAED,MAAME,MAAM,GAAGQ,CAAC,CAACa,WAAF,CAAc,CAAd,CAAf;AACA,MAAMC,OAAO,GAAGtB,MAAM,CAACuB,OAAP,CAAe,IAAf,CAAhB;AACA,SAAOD,OAAO,KAAKtB,MAAM,CAACC,MAAP,GAAgB,CAA5B,GAAgCD,MAAM,CAACwB,KAAP,CAAa,CAAb,EAAgB,CAAC,CAAjB,CAAhC,GAAsDxB,MAA7D;AACD;;AAGD,SAASgB,gBAAT,CAA0BR,CAA1B,EAA6BC,IAA7B,EAAmC;AACjC,sBAAiCA,IAAjC,CAAOgB,OAAP;AAAA,MAAOA,OAAP,8BAAiB,EAAjB;AAAA,mBAAiChB,IAAjC,CAAqBiB,IAArB;AAAA,MAAqBA,IAArB,2BAA4B,CAA5B;AACA,MAAI1B,MAAM,GAAG,GAAb;;AACA,OAAK,IAAI2B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGnB,CAAC,CAACP,MAAN,IAAgB0B,CAAC,GAAGF,OAApC,EAA6C,EAAEE,CAA/C,EAAkD;AAChD,QAAIA,CAAC,GAAG,CAAR,EAAW;AACT3B,MAAAA,MAAM,eAAQ2B,CAAC,GAAGD,IAAJ,KAAa,CAAb,GAAiB,GAAjB,GAAuB,EAA/B,CAAN;AACD;;AACD1B,IAAAA,MAAM,IAAIO,WAAW,CAACC,CAAC,CAACmB,CAAD,CAAF,EAAOlB,IAAP,CAArB;AACD;;AACD,MAAMmB,UAAU,GAAGpB,CAAC,CAACP,MAAF,GAAWwB,OAAX,GAAqB,KAArB,GAA6B,GAAhD;AACA,mBAAUzB,MAAV,SAAmB4B,UAAnB;AACD;;AAGM,SAASC,WAAT,CAAqBC,KAArB,EAA4BC,OAA5B,EAAqCC,KAArC,EAA4D;AAAA,MAAhBC,QAAgB,uEAAL,GAAK;AACjE,MAAMC,QAAQ,GAAGJ,KAAK,CAACK,GAAN,CAAUC,OAAV,CAAkB,KAAlB,EAAyB,KAAzB,EAAgCA,OAAhC,CAAwC,KAAxC,EAA+C,KAA/C,CAAjB;;AAEA,MAAIN,KAAK,CAACO,KAAN,GAAcJ,QAAlB,EAA4B;AAC1BD,IAAAA,KAAK,GAAG7B,IAAI,CAACmC,GAAL,CAASN,KAAT,EAAgBC,QAAQ,GAAGH,KAAK,CAACO,KAAjC,CAAR;AACD;;AAED,MAAMA,KAAK,GAAGP,KAAK,CAACO,KAAN,GAAcL,KAA5B;AACA,MAAMO,MAAM,GAAGT,KAAK,CAACS,MAAN,GAAeP,KAA9B;AAEA,MAAMQ,KAAK,GAAG,CACZ,gBADY,oBAEDrC,IAAI,CAACsC,KAAL,CAAWF,MAAM,GAAG,CAApB,CAFC,gBAE2BpC,IAAI,CAACsC,KAAL,CAAWJ,KAAK,GAAG,CAAnB,CAF3B,gCAGGE,MAHH,mCAIML,QAJN,mCAKOG,KALP,gBAKkBE,MALlB,UAMZ,oBANY,EAOZG,IAPY,CAOP,EAPO,CAAd;AASA,SAAO,WAAIX,OAAJ,WAAmBS,KAAnB,CAAP;AACD", "sourcesContent": ["// TODO: Currently unused, keeping in case we want it later for log formatting\nexport function formatTime(ms: number): string {\n  let formatted;\n  if (ms < 10) {\n    formatted = `${ms.toFixed(2)}ms`;\n  } else if (ms < 100) {\n    formatted = `${ms.toFixed(1)}ms`;\n  } else if (ms < 1000) {\n    formatted = `${ms.toFixed(0)}ms`;\n  } else {\n    formatted = `${(ms / 1000).toFixed(2)}s`;\n  }\n  return formatted;\n}\n\nexport function leftPad(string: string, length: number = 8): string {\n  const padLength = Math.max(length - string.length, 0);\n  return `${' '.repeat(padLength)}${string}`;\n}\n\nexport function rightPad(string: string, length: number = 8): string {\n  const padLength = Math.max(length - string.length, 0);\n  return `${string}${' '.repeat(padLength)}`;\n}\n\nexport function formatValue(v: unknown, opts: {isInteger?: boolean} = {}): string {\n  const EPSILON = 1e-16;\n  const {isInteger = false} = opts;\n  if (Array.isArray(v) || ArrayBuffer.isView(v)) {\n    return formatArrayValue(v, opts);\n  }\n  if (!Number.isFinite(v)) {\n    return String(v);\n  }\n  // @ts-expect-error\n  if (Math.abs(v) < EPSILON) {\n    return isInteger ? '0' : '0.';\n  }\n  if (isInteger) {\n    // @ts-expect-error\n    return v.toFixed(0);\n  }\n  // @ts-expect-error\n  if (Math.abs(v) > 100 && Math.abs(v) < 10000) {\n    // @ts-expect-error\n    return v.toFixed(0);\n  }\n  // @ts-expect-error\n  const string = v.toPrecision(2);\n  const decimal = string.indexOf('.0');\n  return decimal === string.length - 2 ? string.slice(0, -1) : string;\n}\n\n/** Helper to formatValue */\nfunction formatArrayValue(v, opts) {\n  const {maxElts = 16, size = 1} = opts;\n  let string = '[';\n  for (let i = 0; i < v.length && i < maxElts; ++i) {\n    if (i > 0) {\n      string += `,${i % size === 0 ? ' ' : ''}`;\n    }\n    string += formatValue(v[i], opts);\n  }\n  const terminator = v.length > maxElts ? '...' : ']';\n  return `${string}${terminator}`;\n}\n\n/** Inspired by https://github.com/hughsk/console-image (MIT license) */\nexport function formatImage(image, message, scale, maxWidth = 600) {\n  const imageUrl = image.src.replace(/\\(/g, '%28').replace(/\\)/g, '%29');\n\n  if (image.width > maxWidth) {\n    scale = Math.min(scale, maxWidth / image.width);\n  }\n\n  const width = image.width * scale;\n  const height = image.height * scale;\n\n  const style = [\n    'font-size:1px;',\n    `padding:${Math.floor(height / 2)}px ${Math.floor(width / 2)}px;`,\n    `line-height:${height}px;`,\n    `background:url(${imageUrl});`,\n    `background-size:${width}px ${height}px;`,\n    'color:transparent;'\n  ].join('');\n\n  return [`${message} %c+`, style];\n}\n"], "file": "formatters.js"}