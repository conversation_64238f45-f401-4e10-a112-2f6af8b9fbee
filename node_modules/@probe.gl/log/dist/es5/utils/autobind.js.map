{"version": 3, "sources": ["../../../src/utils/autobind.ts"], "names": ["autobind", "obj", "predefined", "proto", "Object", "getPrototypeOf", "propNames", "getOwnPropertyNames", "key", "find", "name", "bind"], "mappings": ";;;;;;;;;;;;;AAwBO,SAASA,QAAT,CAAkBC,GAAlB,EAAmE;AAAA,MAApCC,UAAoC,uEAAvB,CAAC,aAAD,CAAuB;AACxE,MAAMC,KAAK,GAAGC,MAAM,CAACC,cAAP,CAAsBJ,GAAtB,CAAd;AACA,MAAMK,SAAS,GAAGF,MAAM,CAACG,mBAAP,CAA2BJ,KAA3B,CAAlB;;AAFwE,6CAGtDG,SAHsD;AAAA;;AAAA;AAAA;AAAA,UAG7DE,GAH6D;;AAItE,UAAI,OAAOP,GAAG,CAACO,GAAD,CAAV,KAAoB,UAAxB,EAAoC;AAClC,YAAI,CAACN,UAAU,CAACO,IAAX,CAAgB,UAAAC,IAAI;AAAA,iBAAIF,GAAG,KAAKE,IAAZ;AAAA,SAApB,CAAL,EAA4C;AAC1CT,UAAAA,GAAG,CAACO,GAAD,CAAH,GAAWP,GAAG,CAACO,GAAD,CAAH,CAASG,IAAT,CAAcV,GAAd,CAAX;AACD;AACF;AARqE;;AAGxE,wDAA6B;AAAA;AAM5B;AATuE;AAAA;AAAA;AAAA;AAAA;AAUzE", "sourcesContent": ["// Copyright (c) 2015 - 2017 Uber Technologies, Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n/**\n * Binds the \"this\" argument of all functions on a class instance to the instance\n * @param obj - class instance (typically a react component)\n */\nexport function autobind(obj: object, predefined = ['constructor']): void {\n  const proto = Object.getPrototypeOf(obj);\n  const propNames = Object.getOwnPropertyNames(proto);\n  for (const key of propNames) {\n    if (typeof obj[key] === 'function') {\n      if (!predefined.find(name => key === name)) {\n        obj[key] = obj[key].bind(obj);\n      }\n    }\n  }\n}\n"], "file": "autobind.js"}