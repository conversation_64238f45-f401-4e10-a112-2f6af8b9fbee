{"version": 3, "sources": ["../../../src/utils/local-storage.ts"], "names": ["getStorage", "type", "storage", "window", "x", "setItem", "removeItem", "e", "LocalStorage", "id", "defaultConfig", "config", "_loadConfiguration", "configuration", "Object", "assign", "serialized", "JSON", "stringify", "serializedConfiguration", "getItem", "parse"], "mappings": ";;;;;;;;;;;;;;;AAEA,SAASA,UAAT,CAAoBC,IAApB,EAAmC;AACjC,MAAI;AAEF,QAAMC,OAAgB,GAAGC,MAAM,CAACF,IAAD,CAA/B;AACA,QAAMG,CAAC,GAAG,kBAAV;AACAF,IAAAA,OAAO,CAACG,OAAR,CAAgBD,CAAhB,EAAmBA,CAAnB;AACAF,IAAAA,OAAO,CAACI,UAAR,CAAmBF,CAAnB;AACA,WAAOF,OAAP;AACD,GAPD,CAOE,OAAOK,CAAP,EAAU;AACV,WAAO,IAAP;AACD;AACF;;IAGYC,Y;AAKX,wBAAYC,EAAZ,EAAwBC,aAAxB,EAAyF;AAAA,QAAzBT,IAAyB,uEAAlB,gBAAkB;AAAA;AAAA;AAAA;AAAA;AACvF,SAAKC,OAAL,GAAeF,UAAU,CAACC,IAAD,CAAzB;AACA,SAAKQ,EAAL,GAAUA,EAAV;AACA,SAAKE,MAAL,GAAcD,aAAd;;AACA,SAAKE,kBAAL;AACD;;;;WAED,4BAA4C;AAC1C,aAAO,KAAKD,MAAZ;AACD;;;WAED,0BAAiBE,aAAjB,EAAqD;AACnDC,MAAAA,MAAM,CAACC,MAAP,CAAc,KAAKJ,MAAnB,EAA2BE,aAA3B;;AACA,UAAI,KAAKX,OAAT,EAAkB;AAChB,YAAMc,UAAU,GAAGC,IAAI,CAACC,SAAL,CAAe,KAAKP,MAApB,CAAnB;AACA,aAAKT,OAAL,CAAaG,OAAb,CAAqB,KAAKI,EAA1B,EAA8BO,UAA9B;AACD;AACF;;;WAGD,8BAAqB;AACnB,UAAIH,aAAa,GAAG,EAApB;;AACA,UAAI,KAAKX,OAAT,EAAkB;AAChB,YAAMiB,uBAAuB,GAAG,KAAKjB,OAAL,CAAakB,OAAb,CAAqB,KAAKX,EAA1B,CAAhC;AACAI,QAAAA,aAAa,GAAGM,uBAAuB,GAAGF,IAAI,CAACI,KAAL,CAAWF,uBAAX,CAAH,GAAyC,EAAhF;AACD;;AACDL,MAAAA,MAAM,CAACC,MAAP,CAAc,KAAKJ,MAAnB,EAA2BE,aAA3B;AACA,aAAO,IAAP;AACD", "sourcesContent": ["// probe.gl, MIT license\n\nfunction getStorage(type): Storage {\n  try {\n    // @ts-expect-error\n    const storage: Storage = window[type];\n    const x = '__storage_test__';\n    storage.setItem(x, x);\n    storage.removeItem(x);\n    return storage;\n  } catch (e) {\n    return null;\n  }\n}\n\n// Store keys in local storage via simple interface\nexport class LocalStorage<Configuration extends {}> {\n  storage: Storage;\n  id: string;\n  config: Required<Configuration>;\n\n  constructor(id: string, defaultConfig: Required<Configuration>, type = 'sessionStorage') {\n    this.storage = getStorage(type);\n    this.id = id;\n    this.config = defaultConfig;\n    this._loadConfiguration();\n  }\n\n  getConfiguration(): Required<Configuration> {\n    return this.config;\n  }\n\n  setConfiguration(configuration: Configuration): void {\n    Object.assign(this.config, configuration);\n    if (this.storage) {\n      const serialized = JSON.stringify(this.config);\n      this.storage.setItem(this.id, serialized);\n    }\n  }\n\n  // Get config from persistent store, if available\n  _loadConfiguration() {\n    let configuration = {};\n    if (this.storage) {\n      const serializedConfiguration = this.storage.getItem(this.id);\n      configuration = serializedConfiguration ? JSON.parse(serializedConfiguration) : {};\n    }\n    Object.assign(this.config, configuration);\n    return this;\n  }\n}\n"], "file": "local-storage.js"}