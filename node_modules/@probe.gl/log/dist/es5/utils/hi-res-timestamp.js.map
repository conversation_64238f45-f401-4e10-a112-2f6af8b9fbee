{"version": 3, "sources": ["../../../src/utils/hi-res-timestamp.ts"], "names": ["getHiResTimestamp", "timestamp", "<PERSON><PERSON><PERSON><PERSON>", "window", "performance", "now", "process", "timeParts", "hrtime", "Date"], "mappings": ";;;;;;;AAEA;;AAGO,SAASA,iBAAT,GAA6B;AAClC,MAAIC,SAAJ;;AACA,MAAIC,kBAAa,iBAAiBC,WAAlC,EAA0C;AAAA;;AACxCF,IAAAA,SAAS,GAAGE,WAAH,aAAGA,WAAH,8CAAGA,YAAQC,WAAX,iFAAG,oBAAqBC,GAAxB,0DAAG,+CAAZ;AACD,GAFD,MAEO,IAAI,YAAYC,YAAhB,EAAyB;AAAA;;AAE9B,QAAMC,SAAS,GAAGD,YAAH,aAAGA,YAAH,0CAAGA,aAASE,MAAZ,oDAAG,kCAAlB;AACAP,IAAAA,SAAS,GAAGM,SAAS,CAAC,CAAD,CAAT,GAAe,IAAf,GAAsBA,SAAS,CAAC,CAAD,CAAT,GAAe,GAAjD;AACD,GAJM,MAIA;AACLN,IAAAA,SAAS,GAAGQ,IAAI,CAACJ,GAAL,EAAZ;AACD;;AAED,SAAOJ,SAAP;AACD", "sourcesContent": ["// probe.gl, MIT license\n\nimport {window, process, isBrowser} from '@probe.gl/env';\n\n/** Get best timer available. */\nexport function getHiResTimestamp() {\n  let timestamp;\n  if (isBrowser && 'performance' in window) {\n    timestamp = window?.performance?.now?.();\n  } else if ('hrtime' in process) {\n    // @ts-ignore\n    const timeParts = process?.hrtime?.();\n    timestamp = timeParts[0] * 1000 + timeParts[1] / 1e6;\n  } else {\n    timestamp = Date.now();\n  }\n\n  return timestamp;\n}\n"], "file": "hi-res-timestamp.js"}