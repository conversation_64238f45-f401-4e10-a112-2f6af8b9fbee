{"version": 3, "sources": ["../../src/log.ts"], "names": ["originalConsole", "debug", "<PERSON><PERSON><PERSON><PERSON>", "console", "log", "info", "warn", "error", "DEFAULT_SETTINGS", "enabled", "level", "noop", "cache", "ONCE", "once", "Log", "id", "VERSION", "userData", "_storage", "LocalStorage", "timeStamp", "Object", "seal", "getLevel", "newLevel", "setLevel", "config", "Number", "_startTs", "toPrecision", "_deltaTs", "newPriority", "setConfiguration", "setting", "value", "table", "condition", "message", "_getLogFunction", "arguments", "oldUsage", "newUsage", "logLevel", "time", "args", "columns", "tag", "getTableHeader", "priority", "image", "scale", "_shouldLog", "logImageInBrowser", "logImageInNode", "timeEnd", "opts", "collapsed", "options", "normalizeArguments", "method", "groupCollapsed", "group", "assign", "groupEnd", "func", "trace", "isEnabled", "normalizeLogLevel", "total", "getTotal", "delta", "<PERSON><PERSON><PERSON><PERSON>", "decorateMessage", "bind", "resolvedLevel", "isFinite", "Array", "from", "length", "shift", "undefined", "unshift", "messageType", "color", "background", "img", "Image", "onload", "src", "element", "nodeName", "toLowerCase", "toDataURL", "key", "title"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAGA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AAGA,IAAMA,eAAe,GAAG;AACtBC,EAAAA,KAAK,EAAEC,iBAAYC,OAAO,CAACF,KAAR,IAAiBE,OAAO,CAACC,GAArC,GAA2CD,OAAO,CAACC,GADpC;AAEtBA,EAAAA,GAAG,EAAED,OAAO,CAACC,GAFS;AAGtBC,EAAAA,IAAI,EAAEF,OAAO,CAACE,IAHQ;AAItBC,EAAAA,IAAI,EAAEH,OAAO,CAACG,IAJQ;AAKtBC,EAAAA,KAAK,EAAEJ,OAAO,CAACI;AALO,CAAxB;AA8BA,IAAMC,gBAAuC,GAAG;AAC9CC,EAAAA,OAAO,EAAE,IADqC;AAE9CC,EAAAA,KAAK,EAAE;AAFuC,CAAhD;;AAKA,SAASC,IAAT,GAAgB,CAAE;;AAElB,IAAMC,KAAK,GAAG,EAAd;AACA,IAAMC,IAAI,GAAG;AAACC,EAAAA,IAAI,EAAE;AAAP,CAAb;;IASaC,G;AAaX,iBAA6B;AAAA,mFAAV;AAACC,MAAAA,EAAE,EAAE;AAAL,KAAU;AAAA,QAAhBA,EAAgB,QAAhBA,EAAgB;;AAAA;AAAA;AAAA,mDATXC,YASW;AAAA,oDARV,wCAQU;AAAA,oDAPV,wCAOU;AAAA;AAAA,oDALlB,EAKkB;AAAA,gEAFE,CAEF;AAC3B,SAAKD,EAAL,GAAUA,EAAV;AACA,SAAKE,QAAL,GAAgB,EAAhB;AACA,SAAKC,QAAL,GAAgB,IAAIC,0BAAJ,mBAA8C,KAAKJ,EAAnD,SAA2DR,gBAA3D,CAAhB;AAEA,SAAKa,SAAL,WAAkB,KAAKL,EAAvB;AAEA,4BAAS,IAAT;AACAM,IAAAA,MAAM,CAACC,IAAP,CAAY,IAAZ;AACD;;;;SAMD,eAAoB;AAClB,aAAO,KAAKC,QAAL,EAAP;AACD,K;SAND,aAAUC,QAAV,EAA4B;AAC1B,WAAKC,QAAL,CAAcD,QAAd;AACD;;;WAMD,qBAAqB;AACnB,aAAO,KAAKN,QAAL,CAAcQ,MAAd,CAAqBlB,OAA5B;AACD;;;WAED,oBAAmB;AACjB,aAAO,KAAKU,QAAL,CAAcQ,MAAd,CAAqBjB,KAA5B;AACD;;;WAGD,oBAAmB;AACjB,aAAOkB,MAAM,CAAC,CAAC,2CAAsB,KAAKC,QAA5B,EAAsCC,WAAtC,CAAkD,EAAlD,CAAD,CAAb;AACD;;;WAGD,oBAAmB;AACjB,aAAOF,MAAM,CAAC,CAAC,2CAAsB,KAAKG,QAA5B,EAAsCD,WAAtC,CAAkD,EAAlD,CAAD,CAAb;AACD;;;SAQD,eAAuB;AACrB,aAAO,KAAKpB,KAAZ;AACD,K;SAPD,aAAasB,WAAb,EAAkC;AAChC,WAAKtB,KAAL,GAAasB,WAAb;AACD;;;WAQD,uBAAsB;AACpB,aAAO,KAAKtB,KAAZ;AACD;;;WAID,kBAAsC;AAAA,UAA/BD,OAA+B,uEAAZ,IAAY;;AACpC,WAAKU,QAAL,CAAcc,gBAAd,CAA+B;AAACxB,QAAAA,OAAO,EAAPA;AAAD,OAA/B;;AACA,aAAO,IAAP;AACD;;;WAED,kBAASC,KAAT,EAA8B;AAC5B,WAAKS,QAAL,CAAcc,gBAAd,CAA+B;AAACvB,QAAAA,KAAK,EAALA;AAAD,OAA/B;;AACA,aAAO,IAAP;AACD;;;WAGD,aAAIwB,OAAJ,EAA0B;AACxB,aAAO,KAAKf,QAAL,CAAcQ,MAAd,CAAqBO,OAArB,CAAP;AACD;;;WAGD,aAAIA,OAAJ,EAAqBC,KAArB,EAAuC;AACrC,WAAKhB,QAAL,CAAcc,gBAAd,mCAAiCC,OAAjC,EAA2CC,KAA3C;AACD;;;WAGD,oBAAiB;AACf,UAAIhC,OAAO,CAACiC,KAAZ,EAAmB;AACjBjC,QAAAA,OAAO,CAACiC,KAAR,CAAc,KAAKjB,QAAL,CAAcQ,MAA5B;AACD,OAFD,MAEO;AACLxB,QAAAA,OAAO,CAACC,GAAR,CAAY,KAAKe,QAAL,CAAcQ,MAA1B;AACD;AACF;;;WAID,gBAAOU,SAAP,EAA2BC,OAA3B,EAAgE;AAC9D,4BAAOD,SAAP,EAAkBC,OAAlB;AACD;;;WAID,cAAKA,OAAL,EAAmC;AACjC,aAAO,KAAKC,eAAL,CAAqB,CAArB,EAAwBD,OAAxB,EAAiCtC,eAAe,CAACM,IAAjD,EAAuDkC,SAAvD,EAAkE3B,IAAlE,CAAP;AACD;;;WAID,eAAMyB,OAAN,EAAoC;AAClC,aAAO,KAAKC,eAAL,CAAqB,CAArB,EAAwBD,OAAxB,EAAiCtC,eAAe,CAACO,KAAjD,EAAwDiC,SAAxD,CAAP;AACD;;;WAGD,oBAAWC,QAAX,EAA6BC,QAA7B,EAA4D;AAC1D,aAAO,KAAKpC,IAAL,YAAemC,QAAf,0EACiBC,QADjB,eAAP;AAED;;;WAGD,iBAAQD,QAAR,EAA0BC,QAA1B,EAAyD;AACvD,aAAO,KAAKnC,KAAL,YAAgBkC,QAAhB,sCAAsDC,QAAtD,eAAP;AACD;;;WAMD,eAAMC,QAAN,EAAgBL,OAAhB,EAAuC;AACrC,aAAO,KAAKC,eAAL,CAAqBI,QAArB,EAA+BL,OAA/B,EAAwCtC,eAAe,CAACI,GAAxD,EAA6DoC,SAA7D,EAAwE;AAC7EI,QAAAA,IAAI,EAAE,IADuE;AAE7E9B,QAAAA,IAAI,EAAE;AAFuE,OAAxE,CAAP;AAID;;;WAID,aAAI6B,QAAJ,EAAcL,OAAd,EAAqC;AACnC,aAAO,KAAKC,eAAL,CAAqBI,QAArB,EAA+BL,OAA/B,EAAwCtC,eAAe,CAACC,KAAxD,EAA+DuC,SAA/D,CAAP;AACD;;;WAID,cAAKG,QAAL,EAAeL,OAAf,EAAsC;AACpC,aAAO,KAAKC,eAAL,CAAqBI,QAArB,EAA+BL,OAA/B,EAAwCnC,OAAO,CAACE,IAAhD,EAAsDmC,SAAtD,CAAP;AACD;;;WAID,cAAKG,QAAL,EAAeL,OAAf,EAAkC;AAAA,wCAANO,IAAM;AAANA,QAAAA,IAAM;AAAA;;AAChC,aAAO,KAAKN,eAAL,CACLI,QADK,EAELL,OAFK,EAGLtC,eAAe,CAACC,KAAhB,IAAyBD,eAAe,CAACK,IAHpC,EAILmC,SAJK,EAKL3B,IALK,CAAP;AAOD;;;WAGD,eAAM8B,QAAN,EAAgBP,MAAhB,EAAwBU,OAAxB,EAA+C;AAC7C,UAAIV,MAAJ,EAAW;AAET,eAAO,KAAKG,eAAL,CAAqBI,QAArB,EAA+BP,MAA/B,EAAsCjC,OAAO,CAACiC,KAAR,IAAiBzB,IAAvD,EAA6DmC,OAAO,IAAI,CAACA,OAAD,CAAxE,EAAmF;AACxFC,UAAAA,GAAG,EAAEC,cAAc,CAACZ,MAAD;AADqE,SAAnF,CAAP;AAGD;;AACD,aAAOzB,IAAP;AACD;;;WAGD,sBAAyE;AAAA,UAAlEgC,QAAkE,SAAlEA,QAAkE;AAAA,UAAxDM,QAAwD,SAAxDA,QAAwD;AAAA,UAA9CC,MAA8C,SAA9CA,KAA8C;AAAA,gCAAvCZ,OAAuC;AAAA,UAAvCA,OAAuC,8BAA7B,EAA6B;AAAA,8BAAzBa,KAAyB;AAAA,UAAzBA,KAAyB,4BAAjB,CAAiB;;AACvE,UAAI,CAAC,KAAKC,UAAL,CAAgBT,QAAQ,IAAIM,QAA5B,CAAL,EAA4C;AAC1C,eAAOtC,IAAP;AACD;;AACD,aAAOT,iBACHmD,iBAAiB,CAAC;AAACH,QAAAA,KAAK,EAALA,MAAD;AAAQZ,QAAAA,OAAO,EAAPA,OAAR;AAAiBa,QAAAA,KAAK,EAALA;AAAjB,OAAD,CADd,GAEHG,cAAc,CAAC;AAACJ,QAAAA,KAAK,EAALA,MAAD;AAAQZ,QAAAA,OAAO,EAAPA,OAAR;AAAiBa,QAAAA,KAAK,EAALA;AAAjB,OAAD,CAFlB;AAGD;;;WAED,cAAKR,QAAL,EAAeL,OAAf,EAAwB;AACtB,aAAO,KAAKC,eAAL,CAAqBI,QAArB,EAA+BL,OAA/B,EAAwCnC,OAAO,CAACyC,IAAR,GAAezC,OAAO,CAACyC,IAAvB,GAA8BzC,OAAO,CAACE,IAA9E,CAAP;AACD;;;WAED,iBAAQsC,QAAR,EAAkBL,OAAlB,EAA2B;AACzB,aAAO,KAAKC,eAAL,CACLI,QADK,EAELL,OAFK,EAGLnC,OAAO,CAACoD,OAAR,GAAkBpD,OAAO,CAACoD,OAA1B,GAAoCpD,OAAO,CAACE,IAHvC,CAAP;AAKD;;;WAED,mBAAUsC,QAAV,EAAoBL,OAApB,EAA8B;AAC5B,aAAO,KAAKC,eAAL,CAAqBI,QAArB,EAA+BL,OAA/B,EAAwCnC,OAAO,CAACkB,SAAR,IAAqBV,IAA7D,CAAP;AACD;;;WAED,eAAMgC,QAAN,EAAgBL,OAAhB,EAAoD;AAAA,UAA3BkB,IAA2B,uEAApB;AAACC,QAAAA,SAAS,EAAE;AAAZ,OAAoB;AAClD,UAAMC,OAAO,GAAGC,kBAAkB,CAAC;AAAChB,QAAAA,QAAQ,EAARA,QAAD;AAAWL,QAAAA,OAAO,EAAPA,OAAX;AAAoBkB,QAAAA,IAAI,EAAJA;AAApB,OAAD,CAAlC;AACA,UAAOC,SAAP,GAAoBD,IAApB,CAAOC,SAAP;AAEAC,MAAAA,OAAO,CAACE,MAAR,GAAiB,CAACH,SAAS,GAAGtD,OAAO,CAAC0D,cAAX,GAA4B1D,OAAO,CAAC2D,KAA9C,KAAwD3D,OAAO,CAACE,IAAjF;AAEA,aAAO,KAAKkC,eAAL,CAAqBmB,OAArB,CAAP;AACD;;;WAED,wBAAef,QAAf,EAAyBL,OAAzB,EAA6C;AAAA,UAAXkB,IAAW,uEAAJ,EAAI;AAC3C,aAAO,KAAKM,KAAL,CAAWnB,QAAX,EAAqBL,OAArB,EAA8BhB,MAAM,CAACyC,MAAP,CAAc,EAAd,EAAkBP,IAAlB,EAAwB;AAACC,QAAAA,SAAS,EAAE;AAAZ,OAAxB,CAA9B,CAAP;AACD;;;WAED,kBAASd,QAAT,EAAmB;AACjB,aAAO,KAAKJ,eAAL,CAAqBI,QAArB,EAA+B,EAA/B,EAAmCxC,OAAO,CAAC6D,QAAR,IAAoBrD,IAAvD,CAAP;AACD;;;WAID,mBAAUgC,QAAV,EAA4BL,OAA5B,EAA6C2B,IAA7C,EAAmE;AACjE,WAAKH,KAAL,CAAWnB,QAAX,EAAqBL,OAArB;;AAEA,UAAI;AACF2B,QAAAA,IAAI;AACL,OAFD,SAEU;AACR,aAAKD,QAAL,CAAcrB,QAAd;AACD;AACF;;;WAED,iBAAc;AACZ,UAAIxC,OAAO,CAAC+D,KAAZ,EAAmB;AACjB/D,QAAAA,OAAO,CAAC+D,KAAR;AACD;AACF;;;WAKD,oBAAWvB,QAAX,EAAuC;AACrC,aAAO,KAAKwB,SAAL,MAAoB,KAAK3C,QAAL,MAAmB4C,iBAAiB,CAACzB,QAAD,CAA/D;AACD;;;WAED,yBACEA,QADF,EAEEL,OAFF,EAGEsB,MAHF,EAIEf,IAJF,EAKEW,IALF,EAMe;AACb,UAAI,KAAKJ,UAAL,CAAgBT,QAAhB,CAAJ,EAA+B;AAAA;;AAE7Ba,QAAAA,IAAI,GAAGG,kBAAkB,CAAC;AAAChB,UAAAA,QAAQ,EAARA,QAAD;AAAWL,UAAAA,OAAO,EAAPA,OAAX;AAAoBO,UAAAA,IAAI,EAAJA,IAApB;AAA0BW,UAAAA,IAAI,EAAJA;AAA1B,SAAD,CAAzB;AACAI,QAAAA,MAAM,GAAGA,MAAM,IAAIJ,IAAI,CAACI,MAAxB;AACA,8BAAOA,MAAP;AAEAJ,QAAAA,IAAI,CAACa,KAAL,GAAa,KAAKC,QAAL,EAAb;AACAd,QAAAA,IAAI,CAACe,KAAL,GAAa,KAAKC,QAAL,EAAb;AAEA,aAAKzC,QAAL,GAAgB,wCAAhB;AAEA,YAAMgB,GAAG,GAAGS,IAAI,CAACT,GAAL,IAAYS,IAAI,CAAClB,OAA7B;;AAEA,YAAIkB,IAAI,CAAC1C,IAAT,EAAe;AACb,cAAI,CAACF,KAAK,CAACmC,GAAD,CAAV,EAAiB;AACfnC,YAAAA,KAAK,CAACmC,GAAD,CAAL,GAAa,wCAAb;AACD,WAFD,MAEO;AACL,mBAAOpC,IAAP;AACD;AACF;;AAOD2B,QAAAA,OAAO,GAAGmC,eAAe,CAAC,KAAKzD,EAAN,EAAUwC,IAAI,CAAClB,OAAf,EAAwBkB,IAAxB,CAAzB;AAGA,eAAO,WAAAI,MAAM,EAACc,IAAP,iBAAYvE,OAAZ,EAAqBmC,OAArB,0CAAiCkB,IAAI,CAACX,IAAtC,GAAP;AACD;;AACD,aAAOlC,IAAP;AACD;;;;;;8BAtRUI,G,aACME,Y;;AA+RnB,SAASmD,iBAAT,CAA2BzB,QAA3B,EAAsD;AACpD,MAAI,CAACA,QAAL,EAAe;AACb,WAAO,CAAP;AACD;;AACD,MAAIgC,aAAJ;;AAEA,gCAAehC,QAAf;AACE,SAAK,QAAL;AACEgC,MAAAA,aAAa,GAAGhC,QAAhB;AACA;;AAEF,SAAK,QAAL;AAIEgC,MAAAA,aAAa,GAAGhC,QAAQ,CAACA,QAAT,IAAqBA,QAAQ,CAACM,QAA9B,IAA0C,CAA1D;AACA;;AAEF;AACE,aAAO,CAAP;AAbJ;;AAgBA,wBAAOrB,MAAM,CAACgD,QAAP,CAAgBD,aAAhB,KAAkCA,aAAa,IAAI,CAA1D;AAEA,SAAOA,aAAP;AACD;;AASM,SAAShB,kBAAT,CAA4BH,IAA5B,EAUL;AACA,MAAOb,QAAP,GAA4Ba,IAA5B,CAAOb,QAAP;AAAA,MAAiBL,OAAjB,GAA4BkB,IAA5B,CAAiBlB,OAAjB;AACAkB,EAAAA,IAAI,CAACb,QAAL,GAAgByB,iBAAiB,CAACzB,QAAD,CAAjC;AAOA,MAAME,IAAW,GAAGW,IAAI,CAACX,IAAL,GAAYgC,KAAK,CAACC,IAAN,CAAWtB,IAAI,CAACX,IAAhB,CAAZ,GAAoC,EAAxD;;AAGA,SAAOA,IAAI,CAACkC,MAAL,IAAelC,IAAI,CAACmC,KAAL,OAAiB1C,OAAvC,EAAgD,CAAE;;AAElD,gCAAeK,QAAf;AACE,SAAK,QAAL;AACA,SAAK,UAAL;AACE,UAAIL,OAAO,KAAK2C,SAAhB,EAA2B;AACzBpC,QAAAA,IAAI,CAACqC,OAAL,CAAa5C,OAAb;AACD;;AACDkB,MAAAA,IAAI,CAAClB,OAAL,GAAeK,QAAf;AACA;;AAEF,SAAK,QAAL;AACErB,MAAAA,MAAM,CAACyC,MAAP,CAAcP,IAAd,EAAoBb,QAApB;AACA;;AAEF;AAbF;;AAiBA,MAAI,OAAOa,IAAI,CAAClB,OAAZ,KAAwB,UAA5B,EAAwC;AACtCkB,IAAAA,IAAI,CAAClB,OAAL,GAAekB,IAAI,CAAClB,OAAL,EAAf;AACD;;AACD,MAAM6C,WAAW,yBAAU3B,IAAI,CAAClB,OAAf,CAAjB;AAEA,wBAAO6C,WAAW,KAAK,QAAhB,IAA4BA,WAAW,KAAK,QAAnD;AAGA,SAAO7D,MAAM,CAACyC,MAAP,CAAcP,IAAd,EAAoB;AAACX,IAAAA,IAAI,EAAJA;AAAD,GAApB,EAA4BW,IAAI,CAACA,IAAjC,CAAP;AACD;;AAED,SAASiB,eAAT,CAAyBzD,EAAzB,EAA6BsB,OAA7B,EAAsCkB,IAAtC,EAA4C;AAC1C,MAAI,OAAOlB,OAAP,KAAmB,QAAvB,EAAiC;AAC/B,QAAMM,IAAI,GAAGY,IAAI,CAACZ,IAAL,GAAY,yBAAQ,4BAAWY,IAAI,CAACa,KAAhB,CAAR,CAAZ,GAA8C,EAA3D;AACA/B,IAAAA,OAAO,GAAGkB,IAAI,CAACZ,IAAL,aAAe5B,EAAf,eAAsB4B,IAAtB,eAA+BN,OAA/B,cAA8CtB,EAA9C,eAAqDsB,OAArD,CAAV;AACAA,IAAAA,OAAO,GAAG,qBAASA,OAAT,EAAkBkB,IAAI,CAAC4B,KAAvB,EAA8B5B,IAAI,CAAC6B,UAAnC,CAAV;AACD;;AACD,SAAO/C,OAAP;AACD;;AAGD,SAASgB,cAAT,QAA0D;AAAA,MAAjCJ,KAAiC,SAAjCA,KAAiC;AAAA,4BAA1BZ,OAA0B;AAAA,MAA1BA,OAA0B,8BAAhB,EAAgB;AAAA,0BAAZa,KAAY;AAAA,MAAZA,KAAY,4BAAJ,CAAI;AACxDhD,EAAAA,OAAO,CAACG,IAAR,CAAa,SAAb;AACA,SAAOK,IAAP;AACD;;AAED,SAAS0C,iBAAT,QAA6D;AAAA,MAAjCH,KAAiC,SAAjCA,KAAiC;AAAA,4BAA1BZ,OAA0B;AAAA,MAA1BA,OAA0B,8BAAhB,EAAgB;AAAA,0BAAZa,KAAY;AAAA,MAAZA,KAAY,4BAAJ,CAAI;;AAC3D,MAAI,OAAOD,KAAP,KAAiB,QAArB,EAA+B;AAC7B,QAAMoC,GAAG,GAAG,IAAIC,KAAJ,EAAZ;;AACAD,IAAAA,GAAG,CAACE,MAAJ,GAAa,YAAM;AAAA;;AACjB,UAAM3C,IAAI,GAAG,6BAAYyC,GAAZ,EAAiBhD,OAAjB,EAA0Ba,KAA1B,CAAb;;AACA,kBAAAhD,OAAO,EAACC,GAAR,kDAAeyC,IAAf;AACD,KAHD;;AAIAyC,IAAAA,GAAG,CAACG,GAAJ,GAAUvC,KAAV;AACA,WAAOvC,IAAP;AACD;;AACD,MAAM+E,OAAO,GAAGxC,KAAK,CAACyC,QAAN,IAAkB,EAAlC;;AACA,MAAID,OAAO,CAACE,WAAR,OAA0B,KAA9B,EAAqC;AAAA;;AACnC,iBAAAzF,OAAO,EAACC,GAAR,mDAAe,6BAAY8C,KAAZ,EAAmBZ,OAAnB,EAA4Ba,KAA5B,CAAf;;AACA,WAAOxC,IAAP;AACD;;AACD,MAAI+E,OAAO,CAACE,WAAR,OAA0B,QAA9B,EAAwC;AACtC,QAAMN,IAAG,GAAG,IAAIC,KAAJ,EAAZ;;AACAD,IAAAA,IAAG,CAACE,MAAJ,GAAa;AAAA;;AAAA,aAAM,aAAArF,OAAO,EAACC,GAAR,mDAAe,6BAAYkF,IAAZ,EAAiBhD,OAAjB,EAA0Ba,KAA1B,CAAf,EAAN;AAAA,KAAb;;AACAmC,IAAAA,IAAG,CAACG,GAAJ,GAAUvC,KAAK,CAAC2C,SAAN,EAAV;AACA,WAAOlF,IAAP;AACD;;AACD,SAAOA,IAAP;AACD;;AAED,SAASqC,cAAT,CAAwBZ,KAAxB,EAA8C;AAC5C,OAAK,IAAM0D,KAAX,IAAkB1D,KAAlB,EAAyB;AACvB,SAAK,IAAM2D,KAAX,IAAoB3D,KAAK,CAAC0D,KAAD,CAAzB,EAAgC;AAC9B,aAAOC,KAAK,IAAI,UAAhB;AACD;AACF;;AACD,SAAO,OAAP;AACD", "sourcesContent": ["// probe.gl, MIT license\n\n/* eslint-disable no-console */\nimport {VERSION, isBrowser} from '@probe.gl/env';\nimport {LocalStorage} from './utils/local-storage';\nimport {formatImage, formatTime, leftPad} from './utils/formatters';\nimport {addColor} from './utils/color';\nimport {autobind} from './utils/autobind';\nimport assert from './utils/assert';\nimport {getHiResTimestamp} from './utils/hi-res-timestamp';\n\n// Instrumentation in other packages may override console methods, so preserve them here\nconst originalConsole = {\n  debug: isBrowser ? console.debug || console.log : console.log,\n  log: console.log,\n  info: console.info,\n  warn: console.warn,\n  error: console.error\n};\n\ntype Table = Record<string, any>;\n\ntype LogFunction = () => void;\n\ntype LogOptions = {\n  method?;\n  time?;\n  total?: number;\n  delta?: number;\n  tag?: string;\n  message?: string;\n  once?: boolean;\n  nothrottle?: boolean;\n  args?: any;\n};\n\ntype LogSettings = {\n  enabled?: boolean;\n  level?: number;\n  [key: string]: any;\n};\n\nconst DEFAULT_SETTINGS: Required<LogSettings> = {\n  enabled: true,\n  level: 0\n};\n\nfunction noop() {} // eslint-disable-line @typescript-eslint/no-empty-function\n\nconst cache = {};\nconst ONCE = {once: true};\n\ntype LogConfiguration = {\n  enabled?: boolean;\n  level?: number;\n};\n\n/** A console wrapper */\n\nexport class Log {\n  static VERSION = VERSION;\n\n  id: string;\n  VERSION: string = VERSION;\n  _startTs: number = getHiResTimestamp();\n  _deltaTs: number = getHiResTimestamp();\n  _storage: LocalStorage<LogConfiguration>;\n  userData = {};\n\n  // TODO - fix support from throttling groups\n  LOG_THROTTLE_TIMEOUT: number = 0; // Time before throttled messages are logged again\n\n  constructor({id} = {id: ''}) {\n    this.id = id;\n    this.userData = {};\n    this._storage = new LocalStorage<LogConfiguration>(`__probe-${this.id}__`, DEFAULT_SETTINGS);\n\n    this.timeStamp(`${this.id} started`);\n\n    autobind(this);\n    Object.seal(this);\n  }\n\n  set level(newLevel: number) {\n    this.setLevel(newLevel);\n  }\n\n  get level(): number {\n    return this.getLevel();\n  }\n\n  isEnabled(): boolean {\n    return this._storage.config.enabled;\n  }\n\n  getLevel(): number {\n    return this._storage.config.level;\n  }\n\n  /** @return milliseconds, with fractions */\n  getTotal(): number {\n    return Number((getHiResTimestamp() - this._startTs).toPrecision(10));\n  }\n\n  /** @return milliseconds, with fractions */\n  getDelta(): number {\n    return Number((getHiResTimestamp() - this._deltaTs).toPrecision(10));\n  }\n\n  /** @deprecated use logLevel */\n  set priority(newPriority: number) {\n    this.level = newPriority;\n  }\n\n  /** @deprecated use logLevel */\n  get priority(): number {\n    return this.level;\n  }\n\n  /** @deprecated use logLevel */\n  getPriority(): number {\n    return this.level;\n  }\n\n  // Configure\n\n  enable(enabled: boolean = true): this {\n    this._storage.setConfiguration({enabled});\n    return this;\n  }\n\n  setLevel(level: number): this {\n    this._storage.setConfiguration({level});\n    return this;\n  }\n\n  /** return the current status of the setting */\n  get(setting: string): any {\n    return this._storage.config[setting];\n  }\n\n  // update the status of the setting\n  set(setting: string, value: any): void {\n    this._storage.setConfiguration({[setting]: value});\n  }\n\n  /** Logs the current settings as a table */\n  settings(): void {\n    if (console.table) {\n      console.table(this._storage.config);\n    } else {\n      console.log(this._storage.config);\n    }\n  }\n\n  // Unconditional logging\n\n  assert(condition: unknown, message?: string): asserts condition {\n    assert(condition, message);\n  }\n\n  /** Warn, but only once, no console flooding */\n  warn(message: string, ...args): LogFunction;\n  warn(message: string): LogFunction {\n    return this._getLogFunction(0, message, originalConsole.warn, arguments, ONCE);\n  }\n\n  /** Print an error */\n  error(message: string, ...args): LogFunction;\n  error(message: string): LogFunction {\n    return this._getLogFunction(0, message, originalConsole.error, arguments);\n  }\n\n  /** Print a deprecation warning */\n  deprecated(oldUsage: string, newUsage: string): LogFunction {\n    return this.warn(`\\`${oldUsage}\\` is deprecated and will be removed \\\nin a later version. Use \\`${newUsage}\\` instead`);\n  }\n\n  /** Print a removal warning */\n  removed(oldUsage: string, newUsage: string): LogFunction {\n    return this.error(`\\`${oldUsage}\\` has been removed. Use \\`${newUsage}\\` instead`);\n  }\n\n  // Conditional logging\n\n  /** Log to a group */\n  probe(logLevel, message?, ...args): LogFunction;\n  probe(logLevel, message?): LogFunction {\n    return this._getLogFunction(logLevel, message, originalConsole.log, arguments, {\n      time: true,\n      once: true\n    });\n  }\n\n  /** Log a debug message */\n  log(logLevel, message?, ...args): LogFunction;\n  log(logLevel, message?): LogFunction {\n    return this._getLogFunction(logLevel, message, originalConsole.debug, arguments);\n  }\n\n  /** Log a normal message */\n  info(logLevel, message?, ...args): LogFunction;\n  info(logLevel, message?): LogFunction {\n    return this._getLogFunction(logLevel, message, console.info, arguments);\n  }\n\n  /** Log a normal message, but only once, no console flooding */\n  once(logLevel, message?, ...args): LogFunction;\n  once(logLevel, message?, ...args) {\n    return this._getLogFunction(\n      logLevel,\n      message,\n      originalConsole.debug || originalConsole.info,\n      arguments,\n      ONCE\n    );\n  }\n\n  /** Logs an object as a table */\n  table(logLevel, table?, columns?): LogFunction {\n    if (table) {\n      // @ts-expect-error Not clear how this works, columns being passed as arguments\n      return this._getLogFunction(logLevel, table, console.table || noop, columns && [columns], {\n        tag: getTableHeader(table)\n      });\n    }\n    return noop;\n  }\n\n  /** logs an image under Chrome */\n  image({logLevel, priority, image, message = '', scale = 1}): LogFunction {\n    if (!this._shouldLog(logLevel || priority)) {\n      return noop;\n    }\n    return isBrowser\n      ? logImageInBrowser({image, message, scale})\n      : logImageInNode({image, message, scale});\n  }\n\n  time(logLevel, message) {\n    return this._getLogFunction(logLevel, message, console.time ? console.time : console.info);\n  }\n\n  timeEnd(logLevel, message) {\n    return this._getLogFunction(\n      logLevel,\n      message,\n      console.timeEnd ? console.timeEnd : console.info\n    );\n  }\n\n  timeStamp(logLevel, message?) {\n    return this._getLogFunction(logLevel, message, console.timeStamp || noop);\n  }\n\n  group(logLevel, message, opts = {collapsed: false}) {\n    const options = normalizeArguments({logLevel, message, opts});\n    const {collapsed} = opts;\n    // @ts-expect-error\n    options.method = (collapsed ? console.groupCollapsed : console.group) || console.info;\n\n    return this._getLogFunction(options);\n  }\n\n  groupCollapsed(logLevel, message, opts = {}) {\n    return this.group(logLevel, message, Object.assign({}, opts, {collapsed: true}));\n  }\n\n  groupEnd(logLevel) {\n    return this._getLogFunction(logLevel, '', console.groupEnd || noop);\n  }\n\n  // EXPERIMENTAL\n\n  withGroup(logLevel: number, message: string, func: Function): void {\n    this.group(logLevel, message)();\n\n    try {\n      func();\n    } finally {\n      this.groupEnd(logLevel)();\n    }\n  }\n\n  trace(): void {\n    if (console.trace) {\n      console.trace();\n    }\n  }\n\n  // PRIVATE METHODS\n\n  /** Deduces log level from a variety of arguments */\n  _shouldLog(logLevel: unknown): boolean {\n    return this.isEnabled() && this.getLevel() >= normalizeLogLevel(logLevel);\n  }\n\n  _getLogFunction(\n    logLevel: unknown,\n    message?: unknown,\n    method?: Function,\n    args?: IArguments,\n    opts?: LogOptions\n  ): LogFunction {\n    if (this._shouldLog(logLevel)) {\n      // normalized opts + timings\n      opts = normalizeArguments({logLevel, message, args, opts});\n      method = method || opts.method;\n      assert(method);\n\n      opts.total = this.getTotal();\n      opts.delta = this.getDelta();\n      // reset delta timer\n      this._deltaTs = getHiResTimestamp();\n\n      const tag = opts.tag || opts.message;\n\n      if (opts.once) {\n        if (!cache[tag]) {\n          cache[tag] = getHiResTimestamp();\n        } else {\n          return noop;\n        }\n      }\n\n      // TODO - Make throttling work with groups\n      // if (opts.nothrottle || !throttle(tag, this.LOG_THROTTLE_TIMEOUT)) {\n      //   return noop;\n      // }\n\n      message = decorateMessage(this.id, opts.message, opts);\n\n      // Bind console function so that it can be called after being returned\n      return method.bind(console, message, ...opts.args);\n    }\n    return noop;\n  }\n}\n\n/**\n * Get logLevel from first argument:\n * - log(logLevel, message, args) => logLevel\n * - log(message, args) => 0\n * - log({logLevel, ...}, message, args) => logLevel\n * - log({logLevel, message, args}) => logLevel\n */\nfunction normalizeLogLevel(logLevel: unknown): number {\n  if (!logLevel) {\n    return 0;\n  }\n  let resolvedLevel;\n\n  switch (typeof logLevel) {\n    case 'number':\n      resolvedLevel = logLevel;\n      break;\n\n    case 'object':\n      // Backward compatibility\n      // TODO - deprecate `priority`\n      // @ts-expect-error\n      resolvedLevel = logLevel.logLevel || logLevel.priority || 0;\n      break;\n\n    default:\n      return 0;\n  }\n  // 'log level must be a number'\n  assert(Number.isFinite(resolvedLevel) && resolvedLevel >= 0);\n\n  return resolvedLevel;\n}\n\n/**\n * \"Normalizes\" the various argument patterns into an object with known types\n * - log(logLevel, message, args) => {logLevel, message, args}\n * - log(message, args) => {logLevel: 0, message, args}\n * - log({logLevel, ...}, message, args) => {logLevel, message, args}\n * - log({logLevel, message, args}) => {logLevel, message, args}\n */\nexport function normalizeArguments(opts: {\n  logLevel;\n  message;\n  collapsed?: boolean;\n  args?: IArguments;\n  opts?;\n}): {\n  logLevel: number;\n  message: string;\n  args: any[];\n} {\n  const {logLevel, message} = opts;\n  opts.logLevel = normalizeLogLevel(logLevel);\n\n  // We use `arguments` instead of rest parameters (...args) because IE\n  // does not support the syntax. Rest parameters is transpiled to code with\n  // perf impact. Doing it here instead avoids constructing args when logging is\n  // disabled.\n  // TODO - remove when/if IE support is dropped\n  const args: any[] = opts.args ? Array.from(opts.args) : [];\n  // args should only contain arguments that appear after `message`\n  // eslint-disable-next-line no-empty\n  while (args.length && args.shift() !== message) {}\n\n  switch (typeof logLevel) {\n    case 'string':\n    case 'function':\n      if (message !== undefined) {\n        args.unshift(message);\n      }\n      opts.message = logLevel;\n      break;\n\n    case 'object':\n      Object.assign(opts, logLevel);\n      break;\n\n    default:\n  }\n\n  // Resolve functions into strings by calling them\n  if (typeof opts.message === 'function') {\n    opts.message = opts.message();\n  }\n  const messageType = typeof opts.message;\n  // 'log message must be a string' or object\n  assert(messageType === 'string' || messageType === 'object');\n\n  // original opts + normalized opts + opts arg + fixed up message\n  return Object.assign(opts, {args}, opts.opts);\n}\n\nfunction decorateMessage(id, message, opts) {\n  if (typeof message === 'string') {\n    const time = opts.time ? leftPad(formatTime(opts.total)) : '';\n    message = opts.time ? `${id}: ${time}  ${message}` : `${id}: ${message}`;\n    message = addColor(message, opts.color, opts.background);\n  }\n  return message;\n}\n\n/** @deprecated Function removed */\nfunction logImageInNode({image, message = '', scale = 1}) {\n  console.warn('removed');\n  return noop;\n}\n\nfunction logImageInBrowser({image, message = '', scale = 1}) {\n  if (typeof image === 'string') {\n    const img = new Image();\n    img.onload = () => {\n      const args = formatImage(img, message, scale);\n      console.log(...args);\n    };\n    img.src = image;\n    return noop;\n  }\n  const element = image.nodeName || '';\n  if (element.toLowerCase() === 'img') {\n    console.log(...formatImage(image, message, scale));\n    return noop;\n  }\n  if (element.toLowerCase() === 'canvas') {\n    const img = new Image();\n    img.onload = () => console.log(...formatImage(img, message, scale));\n    img.src = image.toDataURL();\n    return noop;\n  }\n  return noop;\n}\n\nfunction getTableHeader(table: Table): string {\n  for (const key in table) {\n    for (const title in table[key]) {\n      return title || 'untitled';\n    }\n  }\n  return 'empty';\n}\n"], "file": "log.js"}