{"version": 3, "sources": ["../../src/index.ts"], "names": ["Log", "id"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAOA;;AAIA;;AACA;;AACA;;AACA;;AAEA;;eAbe,IAAIA,QAAJ,CAAQ;AAACC,EAAAA,EAAE,EAAE;AAAL,CAAR,C", "sourcesContent": ["import {Log} from './log';\n\n// DEFAULT EXPORT IS A LOG INSTANCE\nexport default new Log({id: '@probe.gl/log'});\n\n// LOGGING\nexport {Log} from './log';\nexport {COLOR} from './utils/color';\n\n// UTILITIES\nexport {addColor} from './utils/color';\nexport {leftPad, rightPad} from './utils/formatters';\nexport {autobind} from './utils/autobind';\nexport {LocalStorage} from './utils/local-storage';\nexport {getHiResTimestamp} from './utils/hi-res-timestamp';\n\nimport './init';\n"], "file": "index.js"}