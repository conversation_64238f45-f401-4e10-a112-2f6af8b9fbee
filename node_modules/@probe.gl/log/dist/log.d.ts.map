{"version": 3, "file": "log.d.ts", "sourceRoot": "", "sources": ["../src/log.ts"], "names": [], "mappings": "AAIA,OAAO,EAAC,YAAY,EAAC,MAAM,uBAAuB,CAAC;AAkBnD,aAAK,WAAW,GAAG,MAAM,IAAI,CAAC;AAE9B,aAAK,UAAU,GAAG;IAChB,MAAM,CAAC,MAAC;IACR,IAAI,CAAC,MAAC;IACN,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,IAAI,CAAC,EAAE,OAAO,CAAC;IACf,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,IAAI,CAAC,EAAE,GAAG,CAAC;CACZ,CAAC;AAkBF,aAAK,gBAAgB,GAAG;IACtB,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,KAAK,CAAC,EAAE,MAAM,CAAC;CAChB,CAAC;AAEF,wBAAwB;AAExB,qBAAa,GAAG;IACd,MAAM,CAAC,OAAO,MAAW;IAEzB,EAAE,EAAE,MAAM,CAAC;IACX,OAAO,EAAE,MAAM,CAAW;IAC1B,QAAQ,EAAE,MAAM,CAAuB;IACvC,QAAQ,EAAE,MAAM,CAAuB;IACvC,QAAQ,EAAE,YAAY,CAAC,gBAAgB,CAAC,CAAC;IACzC,QAAQ,KAAM;IAGd,oBAAoB,EAAE,MAAM,CAAK;gBAErB,EAAC,EAAE,EAAC;;KAAW;IAW3B,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,EAEzB;IAED,IAAI,KAAK,IAAI,MAAM,CAElB;IAED,SAAS,IAAI,OAAO;IAIpB,QAAQ,IAAI,MAAM;IAIlB,2CAA2C;IAC3C,QAAQ,IAAI,MAAM;IAIlB,2CAA2C;IAC3C,QAAQ,IAAI,MAAM;IAIlB,+BAA+B;IAC/B,IAAI,QAAQ,CAAC,WAAW,EAAE,MAAM,EAE/B;IAED,+BAA+B;IAC/B,IAAI,QAAQ,IAAI,MAAM,CAErB;IAED,+BAA+B;IAC/B,WAAW,IAAI,MAAM;IAMrB,MAAM,CAAC,OAAO,GAAE,OAAc,GAAG,IAAI;IAKrC,QAAQ,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;IAK7B,+CAA+C;IAC/C,GAAG,CAAC,OAAO,EAAE,MAAM,GAAG,GAAG;IAKzB,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,IAAI;IAItC,2CAA2C;IAC3C,QAAQ,IAAI,IAAI;IAUhB,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,SAAS;IAI/D,+CAA+C;IAC/C,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,GAAG,IAAI,OAAA,GAAG,WAAW;IAK3C,qBAAqB;IACrB,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,GAAG,IAAI,OAAA,GAAG,WAAW;IAK5C,kCAAkC;IAClC,UAAU,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,WAAW;IAK3D,8BAA8B;IAC9B,OAAO,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,WAAW;IAMxD,qBAAqB;IACrB,KAAK,CAAC,QAAQ,KAAA,EAAE,OAAO,CAAC,KAAA,EAAE,GAAG,IAAI,OAAA,GAAG,WAAW;IAQ/C,0BAA0B;IAC1B,GAAG,CAAC,QAAQ,KAAA,EAAE,OAAO,CAAC,KAAA,EAAE,GAAG,IAAI,OAAA,GAAG,WAAW;IAK7C,2BAA2B;IAC3B,IAAI,CAAC,QAAQ,KAAA,EAAE,OAAO,CAAC,KAAA,EAAE,GAAG,IAAI,OAAA,GAAG,WAAW;IAK9C,+DAA+D;IAC/D,IAAI,CAAC,QAAQ,KAAA,EAAE,OAAO,CAAC,KAAA,EAAE,GAAG,IAAI,OAAA,GAAG,WAAW;IAW9C,gCAAgC;IAChC,KAAK,CAAC,QAAQ,KAAA,EAAE,KAAK,CAAC,KAAA,EAAE,OAAO,CAAC,KAAA,GAAG,WAAW;IAU9C,iCAAiC;IACjC,KAAK,CAAC,EAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAY,EAAE,KAAS,EAAC;;;;;;KAAA,GAAG,WAAW;IASxE,IAAI,CAAC,QAAQ,KAAA,EAAE,OAAO,KAAA;IAItB,OAAO,CAAC,QAAQ,KAAA,EAAE,OAAO,KAAA;IAQzB,SAAS,CAAC,QAAQ,KAAA,EAAE,OAAO,CAAC,KAAA;IAI5B,KAAK,CAAC,QAAQ,KAAA,EAAE,OAAO,KAAA,EAAE,IAAI;;KAAqB;IASlD,cAAc,CAAC,QAAQ,KAAA,EAAE,OAAO,KAAA,EAAE,IAAI,KAAK;IAI3C,QAAQ,CAAC,QAAQ,KAAA;IAMjB,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,GAAG,IAAI;IAUlE,KAAK,IAAI,IAAI;IAQb,oDAAoD;IACpD,UAAU,CAAC,QAAQ,EAAE,OAAO,GAAG,OAAO;IAItC,eAAe,CACb,QAAQ,EAAE,OAAO,EACjB,OAAO,CAAC,EAAE,OAAO,EACjB,MAAM,CAAC,EAAE,QAAQ,EACjB,IAAI,CAAC,EAAE,UAAU,EACjB,IAAI,CAAC,EAAE,UAAU,GAChB,WAAW;CAkCf;AAoCD;;;;;;GAMG;AACH,wBAAgB,kBAAkB,CAAC,IAAI,EAAE;IACvC,QAAQ,MAAC;IACT,OAAO,MAAC;IACR,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,IAAI,CAAC,EAAE,UAAU,CAAC;IAClB,IAAI,CAAC,MAAC;CACP,GAAG;IACF,QAAQ,EAAE,MAAM,CAAC;IACjB,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,EAAE,GAAG,EAAE,CAAC;CACb,CAwCA"}