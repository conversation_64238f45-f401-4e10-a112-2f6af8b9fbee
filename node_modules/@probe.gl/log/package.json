{"name": "@probe.gl/log", "description": "JavaScript debug logging for browser and Node", "license": "MIT", "version": "3.6.0", "keywords": ["javascript", "profiling", "instrumentation", "logging"], "repository": {"type": "git", "url": "https://github.com/uber-web/probe.gl.git"}, "types": "dist/index.d.ts", "main": "dist/es5/index.js", "module": "dist/esm/index.js", "files": ["dist", "src"], "sideEffects": ["./src/init.js"], "browser": {}, "dependencies": {"@babel/runtime": "^7.0.0", "@probe.gl/env": "3.6.0"}, "gitHead": "4317dfd211d771250d91c10784461106fd8cb45d"}