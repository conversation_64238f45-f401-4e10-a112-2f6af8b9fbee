export {install as Bar3<PERSON><PERSON>} from '../chart/bar3D/install';
export {install as Line3<PERSON><PERSON>} from '../chart/line3D/install';
export {install as Scatter3DC<PERSON>} from '../chart/scatter3D/install';
export {install as Lines3DChart} from '../chart/lines3D/install';
export {install as Polygons3DChart} from '../chart/polygons3D/install';
export {install as Surface<PERSON><PERSON>} from '../chart/surface/install';
export {install as Map3DChart} from '../chart/map3D/install';

export {install as ScatterGLC<PERSON>} from '../chart/scatterGL/install';
export {install as GraphG<PERSON><PERSON>} from '../chart/graphGL/install';
export {install as FlowG<PERSON><PERSON>} from '../chart/flowGL/install';
export {install as LinesG<PERSON><PERSON>} from '../chart/linesGL/install';