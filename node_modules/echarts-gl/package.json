{"name": "echarts-gl", "description": "Extension pack of ECharts providing 3D plots and globe visualization", "version": "2.0.9", "maintainers": [{"name": "<PERSON>"}], "scripts": {"build": "node build/glsl2js.js && node node_modules/webpack/bin/webpack.js --mode development --devtool source-map && node node_modules/webpack/bin/webpack.js --mode production", "dev": "node node_modules/webpack/bin/webpack.js --mode development -w --devtool eval-cheap-module-source-map", "prepublish": "node build/glsl2js.js && ./node_modules/.bin/babel src -d lib", "release": "npm run prepublish && npm run build"}, "dependencies": {"claygl": "^1.2.1", "zrender": "^5.1.1"}, "peerDependencies": {"echarts": "^5.1.2"}, "main": "dist/echarts-gl.js", "module": "index.js", "repository": "https://github.com/ecomfe/echarts-gl", "sideEffects": ["lib/chart/*.js", "lib/component/*.js", "lib/echarts-gl.js", "lib/export/all.js", "src/chart/*.js", "src/component/*.js", "src/echarts-gl.js", "src/export/all.js", "index.js"], "devDependencies": {"@babel/cli": "^7.12.10", "@babel/core": "^7.12.10", "@babel/plugin-transform-modules-commonjs": "^7.12.1", "babel-plugin-module-resolver": "^4.1.0", "benchmark": "^2.1.3", "case-sensitive-paths-webpack-plugin": "^2.1.1", "echarts": "^5.1.2", "fs-extra": "^4.0.2", "glob": "^7.1.1", "http-server": "^0.10.0", "maptalks": "^0.38.0", "webpack": "^5.4.0", "webpack-cli": "^4.2.0"}}