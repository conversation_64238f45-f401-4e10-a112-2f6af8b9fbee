{"version": 3, "file": "OutlineEffect.cjs", "sources": ["../../src/effects/OutlineEffect.js"], "sourcesContent": ["import { BackSide, Color, ShaderMaterial, UniformsLib, UniformsUtils } from 'three'\nimport { version } from '../_polyfill/constants'\n\n/**\n * Reference: https://en.wikipedia.org/wiki/Cel_shading\n *\n * API\n *\n * 1. Traditional\n *\n * const effect = new OutlineEffect( renderer );\n *\n * function render() {\n *\n * \teffect.render( scene, camera );\n *\n * }\n *\n * 2. VR compatible\n *\n * const effect = new OutlineEffect( renderer );\n * let renderingOutline = false;\n *\n * scene.onAfterRender = function () {\n *\n * \tif ( renderingOutline ) return;\n *\n * \trenderingOutline = true;\n *\n * \teffect.renderOutline( scene, camera );\n *\n * \trenderingOutline = false;\n *\n * };\n *\n * function render() {\n *\n * \trenderer.render( scene, camera );\n *\n * }\n *\n * // How to set default outline parameters\n * new OutlineEffect( renderer, {\n * \tdefaultThickness: 0.01,\n * \tdefaultColor: [ 0, 0, 0 ],\n * \tdefaultAlpha: 0.8,\n * \tdefaultKeepAlive: true // keeps outline material in cache even if material is removed from scene\n * } );\n *\n * // How to set outline parameters for each material\n * material.userData.outlineParameters = {\n * \tthickness: 0.01,\n * \tcolor: [ 0, 0, 0 ],\n * \talpha: 0.8,\n * \tvisible: true,\n * \tkeepAlive: true\n * };\n */\n\nclass OutlineEffect {\n  constructor(renderer, parameters = {}) {\n    this.enabled = true\n\n    const defaultThickness = parameters.defaultThickness !== undefined ? parameters.defaultThickness : 0.003\n    const defaultColor = new Color().fromArray(\n      parameters.defaultColor !== undefined ? parameters.defaultColor : [0, 0, 0],\n    )\n    const defaultAlpha = parameters.defaultAlpha !== undefined ? parameters.defaultAlpha : 1.0\n    const defaultKeepAlive = parameters.defaultKeepAlive !== undefined ? parameters.defaultKeepAlive : false\n\n    // object.material.uuid -> outlineMaterial or\n    // object.material[ n ].uuid -> outlineMaterial\n    // save at the outline material creation and release\n    // if it's unused removeThresholdCount frames\n    // unless keepAlive is true.\n    const cache = {}\n\n    const removeThresholdCount = 60\n\n    // outlineMaterial.uuid -> object.material or\n    // outlineMaterial.uuid -> object.material[ n ]\n    // save before render and release after render.\n    const originalMaterials = {}\n\n    // object.uuid -> originalOnBeforeRender\n    // save before render and release after render.\n    const originalOnBeforeRenders = {}\n\n    //this.cache = cache;  // for debug\n\n    const uniformsOutline = {\n      outlineThickness: { value: defaultThickness },\n      outlineColor: { value: defaultColor },\n      outlineAlpha: { value: defaultAlpha },\n    }\n\n    const vertexShader = [\n      '#include <common>',\n      '#include <uv_pars_vertex>',\n      '#include <displacementmap_pars_vertex>',\n      '#include <fog_pars_vertex>',\n      '#include <morphtarget_pars_vertex>',\n      '#include <skinning_pars_vertex>',\n      '#include <logdepthbuf_pars_vertex>',\n      '#include <clipping_planes_pars_vertex>',\n\n      'uniform float outlineThickness;',\n\n      'vec4 calculateOutline( vec4 pos, vec3 normal, vec4 skinned ) {',\n      '\tfloat thickness = outlineThickness;',\n      '\tconst float ratio = 1.0;', // TODO: support outline thickness ratio for each vertex\n      '\tvec4 pos2 = projectionMatrix * modelViewMatrix * vec4( skinned.xyz + normal, 1.0 );',\n      // NOTE: subtract pos2 from pos because BackSide objectNormal is negative\n      '\tvec4 norm = normalize( pos - pos2 );',\n      '\treturn pos + norm * thickness * pos.w * ratio;',\n      '}',\n\n      'void main() {',\n\n      '\t#include <uv_vertex>',\n\n      '\t#include <beginnormal_vertex>',\n      '\t#include <morphnormal_vertex>',\n      '\t#include <skinbase_vertex>',\n      '\t#include <skinnormal_vertex>',\n\n      '\t#include <begin_vertex>',\n      '\t#include <morphtarget_vertex>',\n      '\t#include <skinning_vertex>',\n      '\t#include <displacementmap_vertex>',\n      '\t#include <project_vertex>',\n\n      '\tvec3 outlineNormal = - objectNormal;', // the outline material is always rendered with BackSide\n\n      '\tgl_Position = calculateOutline( gl_Position, outlineNormal, vec4( transformed, 1.0 ) );',\n\n      '\t#include <logdepthbuf_vertex>',\n      '\t#include <clipping_planes_vertex>',\n      '\t#include <fog_vertex>',\n\n      '}',\n    ].join('\\n')\n\n    const fragmentShader = [\n      '#include <common>',\n      '#include <fog_pars_fragment>',\n      '#include <logdepthbuf_pars_fragment>',\n      '#include <clipping_planes_pars_fragment>',\n\n      'uniform vec3 outlineColor;',\n      'uniform float outlineAlpha;',\n\n      'void main() {',\n\n      '\t#include <clipping_planes_fragment>',\n      '\t#include <logdepthbuf_fragment>',\n\n      '\tgl_FragColor = vec4( outlineColor, outlineAlpha );',\n\n      '\t#include <tonemapping_fragment>',\n      `\t#include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>`,\n      '\t#include <fog_fragment>',\n      '\t#include <premultiplied_alpha_fragment>',\n\n      '}',\n    ].join('\\n')\n\n    function createMaterial() {\n      return new ShaderMaterial({\n        type: 'OutlineEffect',\n        uniforms: UniformsUtils.merge([UniformsLib['fog'], UniformsLib['displacementmap'], uniformsOutline]),\n        vertexShader: vertexShader,\n        fragmentShader: fragmentShader,\n        side: BackSide,\n      })\n    }\n\n    function getOutlineMaterialFromCache(originalMaterial) {\n      let data = cache[originalMaterial.uuid]\n\n      if (data === undefined) {\n        data = {\n          material: createMaterial(),\n          used: true,\n          keepAlive: defaultKeepAlive,\n          count: 0,\n        }\n\n        cache[originalMaterial.uuid] = data\n      }\n\n      data.used = true\n\n      return data.material\n    }\n\n    function getOutlineMaterial(originalMaterial) {\n      const outlineMaterial = getOutlineMaterialFromCache(originalMaterial)\n\n      originalMaterials[outlineMaterial.uuid] = originalMaterial\n\n      updateOutlineMaterial(outlineMaterial, originalMaterial)\n\n      return outlineMaterial\n    }\n\n    function isCompatible(object) {\n      const geometry = object.geometry\n      const hasNormals = geometry !== undefined && geometry.attributes.normal !== undefined\n\n      return object.isMesh === true && object.material !== undefined && hasNormals === true\n    }\n\n    function setOutlineMaterial(object) {\n      if (isCompatible(object) === false) return\n\n      if (Array.isArray(object.material)) {\n        for (let i = 0, il = object.material.length; i < il; i++) {\n          object.material[i] = getOutlineMaterial(object.material[i])\n        }\n      } else {\n        object.material = getOutlineMaterial(object.material)\n      }\n\n      originalOnBeforeRenders[object.uuid] = object.onBeforeRender\n      object.onBeforeRender = onBeforeRender\n    }\n\n    function restoreOriginalMaterial(object) {\n      if (isCompatible(object) === false) return\n\n      if (Array.isArray(object.material)) {\n        for (let i = 0, il = object.material.length; i < il; i++) {\n          object.material[i] = originalMaterials[object.material[i].uuid]\n        }\n      } else {\n        object.material = originalMaterials[object.material.uuid]\n      }\n\n      object.onBeforeRender = originalOnBeforeRenders[object.uuid]\n    }\n\n    function onBeforeRender(renderer, scene, camera, geometry, material) {\n      const originalMaterial = originalMaterials[material.uuid]\n\n      // just in case\n      if (originalMaterial === undefined) return\n\n      updateUniforms(material, originalMaterial)\n    }\n\n    function updateUniforms(material, originalMaterial) {\n      const outlineParameters = originalMaterial.userData.outlineParameters\n\n      material.uniforms.outlineAlpha.value = originalMaterial.opacity\n\n      if (outlineParameters !== undefined) {\n        if (outlineParameters.thickness !== undefined)\n          material.uniforms.outlineThickness.value = outlineParameters.thickness\n        if (outlineParameters.color !== undefined)\n          material.uniforms.outlineColor.value.fromArray(outlineParameters.color)\n        if (outlineParameters.alpha !== undefined) material.uniforms.outlineAlpha.value = outlineParameters.alpha\n      }\n\n      if (originalMaterial.displacementMap) {\n        material.uniforms.displacementMap.value = originalMaterial.displacementMap\n        material.uniforms.displacementScale.value = originalMaterial.displacementScale\n        material.uniforms.displacementBias.value = originalMaterial.displacementBias\n      }\n    }\n\n    function updateOutlineMaterial(material, originalMaterial) {\n      if (material.name === 'invisible') return\n\n      const outlineParameters = originalMaterial.userData.outlineParameters\n\n      material.fog = originalMaterial.fog\n      material.toneMapped = originalMaterial.toneMapped\n      material.premultipliedAlpha = originalMaterial.premultipliedAlpha\n      material.displacementMap = originalMaterial.displacementMap\n\n      if (outlineParameters !== undefined) {\n        if (originalMaterial.visible === false) {\n          material.visible = false\n        } else {\n          material.visible = outlineParameters.visible !== undefined ? outlineParameters.visible : true\n        }\n\n        material.transparent =\n          outlineParameters.alpha !== undefined && outlineParameters.alpha < 1.0 ? true : originalMaterial.transparent\n\n        if (outlineParameters.keepAlive !== undefined)\n          cache[originalMaterial.uuid].keepAlive = outlineParameters.keepAlive\n      } else {\n        material.transparent = originalMaterial.transparent\n        material.visible = originalMaterial.visible\n      }\n\n      if (originalMaterial.wireframe === true || originalMaterial.depthTest === false) material.visible = false\n\n      if (originalMaterial.clippingPlanes) {\n        material.clipping = true\n\n        material.clippingPlanes = originalMaterial.clippingPlanes\n        material.clipIntersection = originalMaterial.clipIntersection\n        material.clipShadows = originalMaterial.clipShadows\n      }\n\n      material.version = originalMaterial.version // update outline material if necessary\n    }\n\n    function cleanupCache() {\n      let keys\n\n      // clear originialMaterials\n      keys = Object.keys(originalMaterials)\n\n      for (let i = 0, il = keys.length; i < il; i++) {\n        originalMaterials[keys[i]] = undefined\n      }\n\n      // clear originalOnBeforeRenders\n      keys = Object.keys(originalOnBeforeRenders)\n\n      for (let i = 0, il = keys.length; i < il; i++) {\n        originalOnBeforeRenders[keys[i]] = undefined\n      }\n\n      // remove unused outlineMaterial from cache\n      keys = Object.keys(cache)\n\n      for (let i = 0, il = keys.length; i < il; i++) {\n        const key = keys[i]\n\n        if (cache[key].used === false) {\n          cache[key].count++\n\n          if (cache[key].keepAlive === false && cache[key].count > removeThresholdCount) {\n            delete cache[key]\n          }\n        } else {\n          cache[key].used = false\n          cache[key].count = 0\n        }\n      }\n    }\n\n    this.render = function (scene, camera) {\n      if (this.enabled === false) {\n        renderer.render(scene, camera)\n        return\n      }\n\n      const currentAutoClear = renderer.autoClear\n      renderer.autoClear = this.autoClear\n\n      renderer.render(scene, camera)\n\n      renderer.autoClear = currentAutoClear\n\n      this.renderOutline(scene, camera)\n    }\n\n    this.renderOutline = function (scene, camera) {\n      const currentAutoClear = renderer.autoClear\n      const currentSceneAutoUpdate = scene.matrixWorldAutoUpdate\n      const currentSceneBackground = scene.background\n      const currentShadowMapEnabled = renderer.shadowMap.enabled\n\n      scene.matrixWorldAutoUpdate = false\n      scene.background = null\n      renderer.autoClear = false\n      renderer.shadowMap.enabled = false\n\n      scene.traverse(setOutlineMaterial)\n\n      renderer.render(scene, camera)\n\n      scene.traverse(restoreOriginalMaterial)\n\n      cleanupCache()\n\n      scene.matrixWorldAutoUpdate = currentSceneAutoUpdate\n      scene.background = currentSceneBackground\n      renderer.autoClear = currentAutoClear\n      renderer.shadowMap.enabled = currentShadowMapEnabled\n    }\n\n    /*\n     * See #9918\n     *\n     * The following property copies and wrapper methods enable\n     * OutlineEffect to be called from other *Effect, like\n     *\n     * effect = new StereoEffect( new OutlineEffect( renderer ) );\n     *\n     * function render () {\n     *\n     * \teffect.render( scene, camera );\n     *\n     * }\n     */\n    this.autoClear = renderer.autoClear\n    this.domElement = renderer.domElement\n    this.shadowMap = renderer.shadowMap\n\n    this.clear = function (color, depth, stencil) {\n      renderer.clear(color, depth, stencil)\n    }\n\n    this.getPixelRatio = function () {\n      return renderer.getPixelRatio()\n    }\n\n    this.setPixelRatio = function (value) {\n      renderer.setPixelRatio(value)\n    }\n\n    this.getSize = function (target) {\n      return renderer.getSize(target)\n    }\n\n    this.setSize = function (width, height, updateStyle) {\n      renderer.setSize(width, height, updateStyle)\n    }\n\n    this.setViewport = function (x, y, width, height) {\n      renderer.setViewport(x, y, width, height)\n    }\n\n    this.setScissor = function (x, y, width, height) {\n      renderer.setScissor(x, y, width, height)\n    }\n\n    this.setScissorTest = function (boolean) {\n      renderer.setScissorTest(boolean)\n    }\n\n    this.setRenderTarget = function (renderTarget) {\n      renderer.setRenderTarget(renderTarget)\n    }\n  }\n}\n\nexport { OutlineEffect }\n"], "names": ["Color", "version", "ShaderMaterial", "UniformsUtils", "UniformsLib", "BackSide", "renderer"], "mappings": ";;;;AA2DA,MAAM,cAAc;AAAA,EAClB,YAAY,UAAU,aAAa,IAAI;AACrC,SAAK,UAAU;AAEf,UAAM,mBAAmB,WAAW,qBAAqB,SAAY,WAAW,mBAAmB;AACnG,UAAM,eAAe,IAAIA,MAAK,MAAA,EAAG;AAAA,MAC/B,WAAW,iBAAiB,SAAY,WAAW,eAAe,CAAC,GAAG,GAAG,CAAC;AAAA,IAC3E;AACD,UAAM,eAAe,WAAW,iBAAiB,SAAY,WAAW,eAAe;AACvF,UAAM,mBAAmB,WAAW,qBAAqB,SAAY,WAAW,mBAAmB;AAOnG,UAAM,QAAQ,CAAE;AAEhB,UAAM,uBAAuB;AAK7B,UAAM,oBAAoB,CAAE;AAI5B,UAAM,0BAA0B,CAAE;AAIlC,UAAM,kBAAkB;AAAA,MACtB,kBAAkB,EAAE,OAAO,iBAAkB;AAAA,MAC7C,cAAc,EAAE,OAAO,aAAc;AAAA,MACrC,cAAc,EAAE,OAAO,aAAc;AAAA,IACtC;AAED,UAAM,eAAe;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MAEA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MAEA;AAAA,MAEA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MAEA;AAAA;AAAA,MAEA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MAEA;AAAA,IACN,EAAM,KAAK,IAAI;AAEX,UAAM,iBAAiB;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MAEA;AAAA,MACA;AAAA,MAEA;AAAA,MAEA;AAAA,MACA;AAAA,MAEA;AAAA,MAEA;AAAA,MACA,cAAcC,UAAAA,WAAW,MAAM,wBAAwB;AAAA,MACvD;AAAA,MACA;AAAA,MAEA;AAAA,IACN,EAAM,KAAK,IAAI;AAEX,aAAS,iBAAiB;AACxB,aAAO,IAAIC,MAAAA,eAAe;AAAA,QACxB,MAAM;AAAA,QACN,UAAUC,MAAAA,cAAc,MAAM,CAACC,MAAW,YAAC,KAAK,GAAGA,kBAAY,iBAAiB,GAAG,eAAe,CAAC;AAAA,QACnG;AAAA,QACA;AAAA,QACA,MAAMC,MAAQ;AAAA,MACtB,CAAO;AAAA,IACF;AAED,aAAS,4BAA4B,kBAAkB;AACrD,UAAI,OAAO,MAAM,iBAAiB,IAAI;AAEtC,UAAI,SAAS,QAAW;AACtB,eAAO;AAAA,UACL,UAAU,eAAgB;AAAA,UAC1B,MAAM;AAAA,UACN,WAAW;AAAA,UACX,OAAO;AAAA,QACR;AAED,cAAM,iBAAiB,IAAI,IAAI;AAAA,MAChC;AAED,WAAK,OAAO;AAEZ,aAAO,KAAK;AAAA,IACb;AAED,aAAS,mBAAmB,kBAAkB;AAC5C,YAAM,kBAAkB,4BAA4B,gBAAgB;AAEpE,wBAAkB,gBAAgB,IAAI,IAAI;AAE1C,4BAAsB,iBAAiB,gBAAgB;AAEvD,aAAO;AAAA,IACR;AAED,aAAS,aAAa,QAAQ;AAC5B,YAAM,WAAW,OAAO;AACxB,YAAM,aAAa,aAAa,UAAa,SAAS,WAAW,WAAW;AAE5E,aAAO,OAAO,WAAW,QAAQ,OAAO,aAAa,UAAa,eAAe;AAAA,IAClF;AAED,aAAS,mBAAmB,QAAQ;AAClC,UAAI,aAAa,MAAM,MAAM;AAAO;AAEpC,UAAI,MAAM,QAAQ,OAAO,QAAQ,GAAG;AAClC,iBAAS,IAAI,GAAG,KAAK,OAAO,SAAS,QAAQ,IAAI,IAAI,KAAK;AACxD,iBAAO,SAAS,CAAC,IAAI,mBAAmB,OAAO,SAAS,CAAC,CAAC;AAAA,QAC3D;AAAA,MACT,OAAa;AACL,eAAO,WAAW,mBAAmB,OAAO,QAAQ;AAAA,MACrD;AAED,8BAAwB,OAAO,IAAI,IAAI,OAAO;AAC9C,aAAO,iBAAiB;AAAA,IACzB;AAED,aAAS,wBAAwB,QAAQ;AACvC,UAAI,aAAa,MAAM,MAAM;AAAO;AAEpC,UAAI,MAAM,QAAQ,OAAO,QAAQ,GAAG;AAClC,iBAAS,IAAI,GAAG,KAAK,OAAO,SAAS,QAAQ,IAAI,IAAI,KAAK;AACxD,iBAAO,SAAS,CAAC,IAAI,kBAAkB,OAAO,SAAS,CAAC,EAAE,IAAI;AAAA,QAC/D;AAAA,MACT,OAAa;AACL,eAAO,WAAW,kBAAkB,OAAO,SAAS,IAAI;AAAA,MACzD;AAED,aAAO,iBAAiB,wBAAwB,OAAO,IAAI;AAAA,IAC5D;AAED,aAAS,eAAeC,WAAU,OAAO,QAAQ,UAAU,UAAU;AACnE,YAAM,mBAAmB,kBAAkB,SAAS,IAAI;AAGxD,UAAI,qBAAqB;AAAW;AAEpC,qBAAe,UAAU,gBAAgB;AAAA,IAC1C;AAED,aAAS,eAAe,UAAU,kBAAkB;AAClD,YAAM,oBAAoB,iBAAiB,SAAS;AAEpD,eAAS,SAAS,aAAa,QAAQ,iBAAiB;AAExD,UAAI,sBAAsB,QAAW;AACnC,YAAI,kBAAkB,cAAc;AAClC,mBAAS,SAAS,iBAAiB,QAAQ,kBAAkB;AAC/D,YAAI,kBAAkB,UAAU;AAC9B,mBAAS,SAAS,aAAa,MAAM,UAAU,kBAAkB,KAAK;AACxE,YAAI,kBAAkB,UAAU;AAAW,mBAAS,SAAS,aAAa,QAAQ,kBAAkB;AAAA,MACrG;AAED,UAAI,iBAAiB,iBAAiB;AACpC,iBAAS,SAAS,gBAAgB,QAAQ,iBAAiB;AAC3D,iBAAS,SAAS,kBAAkB,QAAQ,iBAAiB;AAC7D,iBAAS,SAAS,iBAAiB,QAAQ,iBAAiB;AAAA,MAC7D;AAAA,IACF;AAED,aAAS,sBAAsB,UAAU,kBAAkB;AACzD,UAAI,SAAS,SAAS;AAAa;AAEnC,YAAM,oBAAoB,iBAAiB,SAAS;AAEpD,eAAS,MAAM,iBAAiB;AAChC,eAAS,aAAa,iBAAiB;AACvC,eAAS,qBAAqB,iBAAiB;AAC/C,eAAS,kBAAkB,iBAAiB;AAE5C,UAAI,sBAAsB,QAAW;AACnC,YAAI,iBAAiB,YAAY,OAAO;AACtC,mBAAS,UAAU;AAAA,QAC7B,OAAe;AACL,mBAAS,UAAU,kBAAkB,YAAY,SAAY,kBAAkB,UAAU;AAAA,QAC1F;AAED,iBAAS,cACP,kBAAkB,UAAU,UAAa,kBAAkB,QAAQ,IAAM,OAAO,iBAAiB;AAEnG,YAAI,kBAAkB,cAAc;AAClC,gBAAM,iBAAiB,IAAI,EAAE,YAAY,kBAAkB;AAAA,MACrE,OAAa;AACL,iBAAS,cAAc,iBAAiB;AACxC,iBAAS,UAAU,iBAAiB;AAAA,MACrC;AAED,UAAI,iBAAiB,cAAc,QAAQ,iBAAiB,cAAc;AAAO,iBAAS,UAAU;AAEpG,UAAI,iBAAiB,gBAAgB;AACnC,iBAAS,WAAW;AAEpB,iBAAS,iBAAiB,iBAAiB;AAC3C,iBAAS,mBAAmB,iBAAiB;AAC7C,iBAAS,cAAc,iBAAiB;AAAA,MACzC;AAED,eAAS,UAAU,iBAAiB;AAAA,IACrC;AAED,aAAS,eAAe;AACtB,UAAI;AAGJ,aAAO,OAAO,KAAK,iBAAiB;AAEpC,eAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,KAAK;AAC7C,0BAAkB,KAAK,CAAC,CAAC,IAAI;AAAA,MAC9B;AAGD,aAAO,OAAO,KAAK,uBAAuB;AAE1C,eAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,KAAK;AAC7C,gCAAwB,KAAK,CAAC,CAAC,IAAI;AAAA,MACpC;AAGD,aAAO,OAAO,KAAK,KAAK;AAExB,eAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,KAAK;AAC7C,cAAM,MAAM,KAAK,CAAC;AAElB,YAAI,MAAM,GAAG,EAAE,SAAS,OAAO;AAC7B,gBAAM,GAAG,EAAE;AAEX,cAAI,MAAM,GAAG,EAAE,cAAc,SAAS,MAAM,GAAG,EAAE,QAAQ,sBAAsB;AAC7E,mBAAO,MAAM,GAAG;AAAA,UACjB;AAAA,QACX,OAAe;AACL,gBAAM,GAAG,EAAE,OAAO;AAClB,gBAAM,GAAG,EAAE,QAAQ;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAED,SAAK,SAAS,SAAU,OAAO,QAAQ;AACrC,UAAI,KAAK,YAAY,OAAO;AAC1B,iBAAS,OAAO,OAAO,MAAM;AAC7B;AAAA,MACD;AAED,YAAM,mBAAmB,SAAS;AAClC,eAAS,YAAY,KAAK;AAE1B,eAAS,OAAO,OAAO,MAAM;AAE7B,eAAS,YAAY;AAErB,WAAK,cAAc,OAAO,MAAM;AAAA,IACjC;AAED,SAAK,gBAAgB,SAAU,OAAO,QAAQ;AAC5C,YAAM,mBAAmB,SAAS;AAClC,YAAM,yBAAyB,MAAM;AACrC,YAAM,yBAAyB,MAAM;AACrC,YAAM,0BAA0B,SAAS,UAAU;AAEnD,YAAM,wBAAwB;AAC9B,YAAM,aAAa;AACnB,eAAS,YAAY;AACrB,eAAS,UAAU,UAAU;AAE7B,YAAM,SAAS,kBAAkB;AAEjC,eAAS,OAAO,OAAO,MAAM;AAE7B,YAAM,SAAS,uBAAuB;AAEtC,mBAAc;AAEd,YAAM,wBAAwB;AAC9B,YAAM,aAAa;AACnB,eAAS,YAAY;AACrB,eAAS,UAAU,UAAU;AAAA,IAC9B;AAgBD,SAAK,YAAY,SAAS;AAC1B,SAAK,aAAa,SAAS;AAC3B,SAAK,YAAY,SAAS;AAE1B,SAAK,QAAQ,SAAU,OAAO,OAAO,SAAS;AAC5C,eAAS,MAAM,OAAO,OAAO,OAAO;AAAA,IACrC;AAED,SAAK,gBAAgB,WAAY;AAC/B,aAAO,SAAS,cAAe;AAAA,IAChC;AAED,SAAK,gBAAgB,SAAU,OAAO;AACpC,eAAS,cAAc,KAAK;AAAA,IAC7B;AAED,SAAK,UAAU,SAAU,QAAQ;AAC/B,aAAO,SAAS,QAAQ,MAAM;AAAA,IAC/B;AAED,SAAK,UAAU,SAAU,OAAO,QAAQ,aAAa;AACnD,eAAS,QAAQ,OAAO,QAAQ,WAAW;AAAA,IAC5C;AAED,SAAK,cAAc,SAAU,GAAG,GAAG,OAAO,QAAQ;AAChD,eAAS,YAAY,GAAG,GAAG,OAAO,MAAM;AAAA,IACzC;AAED,SAAK,aAAa,SAAU,GAAG,GAAG,OAAO,QAAQ;AAC/C,eAAS,WAAW,GAAG,GAAG,OAAO,MAAM;AAAA,IACxC;AAED,SAAK,iBAAiB,SAAU,SAAS;AACvC,eAAS,eAAe,OAAO;AAAA,IAChC;AAED,SAAK,kBAAkB,SAAU,cAAc;AAC7C,eAAS,gBAAgB,YAAY;AAAA,IACtC;AAAA,EACF;AACH;;"}