{"version": 3, "file": "StereoEffect.js", "sources": ["../../src/effects/StereoEffect.js"], "sourcesContent": ["import { StereoCamera, Vector2 } from 'three'\n\nclass StereoEffect {\n  constructor(renderer) {\n    const _stereo = new StereoCamera()\n    _stereo.aspect = 0.5\n    const size = new Vector2()\n\n    this.setEyeSeparation = function (eyeSep) {\n      _stereo.eyeSep = eyeSep\n    }\n\n    this.setSize = function (width, height) {\n      renderer.setSize(width, height)\n    }\n\n    this.render = function (scene, camera) {\n      if (scene.matrixWorldAutoUpdate === true) scene.updateMatrixWorld()\n\n      if (camera.parent === null && camera.matrixWorldAutoUpdate === true) camera.updateMatrixWorld()\n\n      _stereo.update(camera)\n\n      renderer.getSize(size)\n\n      if (renderer.autoClear) renderer.clear()\n      renderer.setScissorTest(true)\n\n      renderer.setScissor(0, 0, size.width / 2, size.height)\n      renderer.setViewport(0, 0, size.width / 2, size.height)\n      renderer.render(scene, _stereo.cameraL)\n\n      renderer.setScissor(size.width / 2, 0, size.width / 2, size.height)\n      renderer.setViewport(size.width / 2, 0, size.width / 2, size.height)\n      renderer.render(scene, _stereo.cameraR)\n\n      renderer.setScissorTest(false)\n    }\n  }\n}\n\nexport { StereoEffect }\n"], "names": [], "mappings": ";AAEA,MAAM,aAAa;AAAA,EACjB,YAAY,UAAU;AACpB,UAAM,UAAU,IAAI,aAAc;AAClC,YAAQ,SAAS;AACjB,UAAM,OAAO,IAAI,QAAS;AAE1B,SAAK,mBAAmB,SAAU,QAAQ;AACxC,cAAQ,SAAS;AAAA,IAClB;AAED,SAAK,UAAU,SAAU,OAAO,QAAQ;AACtC,eAAS,QAAQ,OAAO,MAAM;AAAA,IAC/B;AAED,SAAK,SAAS,SAAU,OAAO,QAAQ;AACrC,UAAI,MAAM,0BAA0B;AAAM,cAAM,kBAAmB;AAEnE,UAAI,OAAO,WAAW,QAAQ,OAAO,0BAA0B;AAAM,eAAO,kBAAmB;AAE/F,cAAQ,OAAO,MAAM;AAErB,eAAS,QAAQ,IAAI;AAErB,UAAI,SAAS;AAAW,iBAAS,MAAO;AACxC,eAAS,eAAe,IAAI;AAE5B,eAAS,WAAW,GAAG,GAAG,KAAK,QAAQ,GAAG,KAAK,MAAM;AACrD,eAAS,YAAY,GAAG,GAAG,KAAK,QAAQ,GAAG,KAAK,MAAM;AACtD,eAAS,OAAO,OAAO,QAAQ,OAAO;AAEtC,eAAS,WAAW,KAAK,QAAQ,GAAG,GAAG,KAAK,QAAQ,GAAG,KAAK,MAAM;AAClE,eAAS,YAAY,KAAK,QAAQ,GAAG,GAAG,KAAK,QAAQ,GAAG,KAAK,MAAM;AACnE,eAAS,OAAO,OAAO,QAAQ,OAAO;AAEtC,eAAS,eAAe,KAAK;AAAA,IAC9B;AAAA,EACF;AACH;"}