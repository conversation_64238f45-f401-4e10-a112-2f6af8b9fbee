{"version": 3, "file": "AsciiEffect.js", "sources": ["../../src/effects/AsciiEffect.js"], "sourcesContent": ["/**\n * Ascii generation is based on https://github.com/hassadee/jsascii/blob/master/jsascii.js\n *\n * 16 April 2012 - @blurspline\n */\n\nclass AsciiEffect {\n  constructor(renderer, charSet = ' .:-=+*#%@', options = {}) {\n    // ' .,:;=|iI+hHOE#`$';\n    // darker bolder character set from https://github.com/saw/Canvas-ASCII-Art/\n    // ' .\\'`^\",:;Il!i~+_-?][}{1)(|/tfjrxnuvczXYUJCLQ0OZmwqpdbkhao*#MW&8%B@$'.split('');\n\n    // Some ASCII settings\n\n    const fResolution = options['resolution'] || 0.15 // Higher for more details\n    const iScale = options['scale'] || 1\n    const bColor = options['color'] || false // nice but slows down rendering!\n    const bAlpha = options['alpha'] || false // Transparency\n    const bBlock = options['block'] || false // blocked characters. like good O dos\n    const bInvert = options['invert'] || false // black is white, white is black\n    const strResolution = options['strResolution'] || 'low'\n\n    let width, height\n\n    const domElement = document.createElement('div')\n    domElement.style.cursor = 'default'\n\n    const oAscii = document.createElement('table')\n    domElement.appendChild(oAscii)\n\n    let iWidth, iHeight\n    let oImg\n\n    this.setSize = function (w, h) {\n      width = w\n      height = h\n\n      renderer.setSize(w, h)\n\n      initAsciiSize()\n    }\n\n    this.render = function (scene, camera) {\n      renderer.render(scene, camera)\n      asciifyImage(oAscii)\n    }\n\n    this.domElement = domElement\n\n    // Throw in ascii library from https://github.com/hassadee/jsascii/blob/master/jsascii.js (MIT License)\n\n    function initAsciiSize() {\n      iWidth = Math.floor(width * fResolution)\n      iHeight = Math.floor(height * fResolution)\n\n      oCanvas.width = iWidth\n      oCanvas.height = iHeight\n      // oCanvas.style.display = \"none\";\n      // oCanvas.style.width = iWidth;\n      // oCanvas.style.height = iHeight;\n\n      oImg = renderer.domElement\n\n      if (oImg.style.backgroundColor) {\n        oAscii.rows[0].cells[0].style.backgroundColor = oImg.style.backgroundColor\n        oAscii.rows[0].cells[0].style.color = oImg.style.color\n      }\n\n      oAscii.cellSpacing = 0\n      oAscii.cellPadding = 0\n\n      const oStyle = oAscii.style\n      oStyle.whiteSpace = 'pre'\n      oStyle.margin = '0px'\n      oStyle.padding = '0px'\n      oStyle.letterSpacing = fLetterSpacing + 'px'\n      oStyle.fontFamily = strFont\n      oStyle.fontSize = fFontSize + 'px'\n      oStyle.lineHeight = fLineHeight + 'px'\n      oStyle.textAlign = 'left'\n      oStyle.textDecoration = 'none'\n    }\n\n    const aDefaultCharList = ' .,:;i1tfLCG08@'.split('')\n    const aDefaultColorCharList = ' CGO08@'.split('')\n    const strFont = 'courier new, monospace'\n\n    const oCanvasImg = renderer.domElement\n\n    const oCanvas = document.createElement('canvas')\n    if (!oCanvas.getContext) {\n      return\n    }\n\n    const oCtx = oCanvas.getContext('2d')\n    if (!oCtx.getImageData) {\n      return\n    }\n\n    let aCharList = bColor ? aDefaultColorCharList : aDefaultCharList\n\n    if (charSet) aCharList = charSet\n\n    // Setup dom\n\n    const fFontSize = (2 / fResolution) * iScale\n    const fLineHeight = (2 / fResolution) * iScale\n\n    // adjust letter-spacing for all combinations of scale and resolution to get it to fit the image width.\n\n    let fLetterSpacing = 0\n\n    if (strResolution == 'low') {\n      switch (iScale) {\n        case 1:\n          fLetterSpacing = -1\n          break\n        case 2:\n        case 3:\n          fLetterSpacing = -2.1\n          break\n        case 4:\n          fLetterSpacing = -3.1\n          break\n        case 5:\n          fLetterSpacing = -4.15\n          break\n      }\n    }\n\n    if (strResolution == 'medium') {\n      switch (iScale) {\n        case 1:\n          fLetterSpacing = 0\n          break\n        case 2:\n          fLetterSpacing = -1\n          break\n        case 3:\n          fLetterSpacing = -1.04\n          break\n        case 4:\n        case 5:\n          fLetterSpacing = -2.1\n          break\n      }\n    }\n\n    if (strResolution == 'high') {\n      switch (iScale) {\n        case 1:\n        case 2:\n          fLetterSpacing = 0\n          break\n        case 3:\n        case 4:\n        case 5:\n          fLetterSpacing = -1\n          break\n      }\n    }\n\n    // can't get a span or div to flow like an img element, but a table works?\n\n    // convert img element to ascii\n\n    function asciifyImage(oAscii) {\n      oCtx.clearRect(0, 0, iWidth, iHeight)\n      oCtx.drawImage(oCanvasImg, 0, 0, iWidth, iHeight)\n      const oImgData = oCtx.getImageData(0, 0, iWidth, iHeight).data\n\n      // Coloring loop starts now\n      let strChars = ''\n\n      // console.time('rendering');\n\n      for (let y = 0; y < iHeight; y += 2) {\n        for (let x = 0; x < iWidth; x++) {\n          const iOffset = (y * iWidth + x) * 4\n\n          const iRed = oImgData[iOffset]\n          const iGreen = oImgData[iOffset + 1]\n          const iBlue = oImgData[iOffset + 2]\n          const iAlpha = oImgData[iOffset + 3]\n          let iCharIdx\n\n          let fBrightness\n\n          fBrightness = (0.3 * iRed + 0.59 * iGreen + 0.11 * iBlue) / 255\n          // fBrightness = (0.3*iRed + 0.5*iGreen + 0.3*iBlue) / 255;\n\n          if (iAlpha == 0) {\n            // should calculate alpha instead, but quick hack :)\n            //fBrightness *= (iAlpha / 255);\n            fBrightness = 1\n          }\n\n          iCharIdx = Math.floor((1 - fBrightness) * (aCharList.length - 1))\n\n          if (bInvert) {\n            iCharIdx = aCharList.length - iCharIdx - 1\n          }\n\n          // good for debugging\n          //fBrightness = Math.floor(fBrightness * 10);\n          //strThisChar = fBrightness;\n\n          let strThisChar = aCharList[iCharIdx]\n\n          if (strThisChar === undefined || strThisChar == ' ') strThisChar = '&nbsp;'\n\n          if (bColor) {\n            strChars +=\n              \"<span style='\" +\n              'color:rgb(' +\n              iRed +\n              ',' +\n              iGreen +\n              ',' +\n              iBlue +\n              ');' +\n              (bBlock ? 'background-color:rgb(' + iRed + ',' + iGreen + ',' + iBlue + ');' : '') +\n              (bAlpha ? 'opacity:' + iAlpha / 255 + ';' : '') +\n              \"'>\" +\n              strThisChar +\n              '</span>'\n          } else {\n            strChars += strThisChar\n          }\n        }\n\n        strChars += '<br/>'\n      }\n\n      oAscii.innerHTML = `<tr><td style=\"display:block;width:${width}px;height:${height}px;overflow:hidden\">${strChars}</td></tr>`\n\n      // console.timeEnd('rendering');\n\n      // return oAscii;\n    }\n  }\n}\n\nexport { AsciiEffect }\n"], "names": ["oAscii"], "mappings": "AAMA,MAAM,YAAY;AAAA,EAChB,YAAY,UAAU,UAAU,cAAc,UAAU,CAAA,GAAI;AAO1D,UAAM,cAAc,QAAQ,YAAY,KAAK;AAC7C,UAAM,SAAS,QAAQ,OAAO,KAAK;AACnC,UAAM,SAAS,QAAQ,OAAO,KAAK;AACnC,UAAM,SAAS,QAAQ,OAAO,KAAK;AACnC,UAAM,SAAS,QAAQ,OAAO,KAAK;AACnC,UAAM,UAAU,QAAQ,QAAQ,KAAK;AACrC,UAAM,gBAAgB,QAAQ,eAAe,KAAK;AAElD,QAAI,OAAO;AAEX,UAAM,aAAa,SAAS,cAAc,KAAK;AAC/C,eAAW,MAAM,SAAS;AAE1B,UAAM,SAAS,SAAS,cAAc,OAAO;AAC7C,eAAW,YAAY,MAAM;AAE7B,QAAI,QAAQ;AACZ,QAAI;AAEJ,SAAK,UAAU,SAAU,GAAG,GAAG;AAC7B,cAAQ;AACR,eAAS;AAET,eAAS,QAAQ,GAAG,CAAC;AAErB,oBAAe;AAAA,IAChB;AAED,SAAK,SAAS,SAAU,OAAO,QAAQ;AACrC,eAAS,OAAO,OAAO,MAAM;AAC7B,mBAAa,MAAM;AAAA,IACpB;AAED,SAAK,aAAa;AAIlB,aAAS,gBAAgB;AACvB,eAAS,KAAK,MAAM,QAAQ,WAAW;AACvC,gBAAU,KAAK,MAAM,SAAS,WAAW;AAEzC,cAAQ,QAAQ;AAChB,cAAQ,SAAS;AAKjB,aAAO,SAAS;AAEhB,UAAI,KAAK,MAAM,iBAAiB;AAC9B,eAAO,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM,kBAAkB,KAAK,MAAM;AAC3D,eAAO,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM,QAAQ,KAAK,MAAM;AAAA,MAClD;AAED,aAAO,cAAc;AACrB,aAAO,cAAc;AAErB,YAAM,SAAS,OAAO;AACtB,aAAO,aAAa;AACpB,aAAO,SAAS;AAChB,aAAO,UAAU;AACjB,aAAO,gBAAgB,iBAAiB;AACxC,aAAO,aAAa;AACpB,aAAO,WAAW,YAAY;AAC9B,aAAO,aAAa,cAAc;AAClC,aAAO,YAAY;AACnB,aAAO,iBAAiB;AAAA,IACzB;AAED,UAAM,mBAAmB,kBAAkB,MAAM,EAAE;AACnD,UAAM,wBAAwB,UAAU,MAAM,EAAE;AAChD,UAAM,UAAU;AAEhB,UAAM,aAAa,SAAS;AAE5B,UAAM,UAAU,SAAS,cAAc,QAAQ;AAC/C,QAAI,CAAC,QAAQ,YAAY;AACvB;AAAA,IACD;AAED,UAAM,OAAO,QAAQ,WAAW,IAAI;AACpC,QAAI,CAAC,KAAK,cAAc;AACtB;AAAA,IACD;AAED,QAAI,YAAY,SAAS,wBAAwB;AAEjD,QAAI;AAAS,kBAAY;AAIzB,UAAM,YAAa,IAAI,cAAe;AACtC,UAAM,cAAe,IAAI,cAAe;AAIxC,QAAI,iBAAiB;AAErB,QAAI,iBAAiB,OAAO;AAC1B,cAAQ,QAAM;AAAA,QACZ,KAAK;AACH,2BAAiB;AACjB;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,2BAAiB;AACjB;AAAA,QACF,KAAK;AACH,2BAAiB;AACjB;AAAA,QACF,KAAK;AACH,2BAAiB;AACjB;AAAA,MACH;AAAA,IACF;AAED,QAAI,iBAAiB,UAAU;AAC7B,cAAQ,QAAM;AAAA,QACZ,KAAK;AACH,2BAAiB;AACjB;AAAA,QACF,KAAK;AACH,2BAAiB;AACjB;AAAA,QACF,KAAK;AACH,2BAAiB;AACjB;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,2BAAiB;AACjB;AAAA,MACH;AAAA,IACF;AAED,QAAI,iBAAiB,QAAQ;AAC3B,cAAQ,QAAM;AAAA,QACZ,KAAK;AAAA,QACL,KAAK;AACH,2BAAiB;AACjB;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,2BAAiB;AACjB;AAAA,MACH;AAAA,IACF;AAMD,aAAS,aAAaA,SAAQ;AAC5B,WAAK,UAAU,GAAG,GAAG,QAAQ,OAAO;AACpC,WAAK,UAAU,YAAY,GAAG,GAAG,QAAQ,OAAO;AAChD,YAAM,WAAW,KAAK,aAAa,GAAG,GAAG,QAAQ,OAAO,EAAE;AAG1D,UAAI,WAAW;AAIf,eAAS,IAAI,GAAG,IAAI,SAAS,KAAK,GAAG;AACnC,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,gBAAM,WAAW,IAAI,SAAS,KAAK;AAEnC,gBAAM,OAAO,SAAS,OAAO;AAC7B,gBAAM,SAAS,SAAS,UAAU,CAAC;AACnC,gBAAM,QAAQ,SAAS,UAAU,CAAC;AAClC,gBAAM,SAAS,SAAS,UAAU,CAAC;AACnC,cAAI;AAEJ,cAAI;AAEJ,yBAAe,MAAM,OAAO,OAAO,SAAS,OAAO,SAAS;AAG5D,cAAI,UAAU,GAAG;AAGf,0BAAc;AAAA,UACf;AAED,qBAAW,KAAK,OAAO,IAAI,gBAAgB,UAAU,SAAS,EAAE;AAEhE,cAAI,SAAS;AACX,uBAAW,UAAU,SAAS,WAAW;AAAA,UAC1C;AAMD,cAAI,cAAc,UAAU,QAAQ;AAEpC,cAAI,gBAAgB,UAAa,eAAe;AAAK,0BAAc;AAEnE,cAAI,QAAQ;AACV,wBACE,4BAEA,OACA,MACA,SACA,MACA,QACA,QACC,SAAS,0BAA0B,OAAO,MAAM,SAAS,MAAM,QAAQ,OAAO,OAC9E,SAAS,aAAa,SAAS,MAAM,MAAM,MAC5C,OACA,cACA;AAAA,UACd,OAAiB;AACL,wBAAY;AAAA,UACb;AAAA,QACF;AAED,oBAAY;AAAA,MACb;AAED,MAAAA,QAAO,YAAY,sCAAsC,kBAAkB,6BAA6B;AAAA,IAKzG;AAAA,EACF;AACH;"}