{"version": 3, "file": "SimplexNoise.js", "sources": ["../../src/math/SimplexNoise.ts"], "sourcesContent": ["// Ported from <PERSON>'s java implementation\n// http://staffwww.itn.liu.se/~stegu/simplexnoise/simplexnoise.pdf\n// Read <PERSON>'s excellent paper for details on how this code works.\n//\n// <PERSON> <EMAIL>\n//\n\nexport interface NumberGenerator {\n  random: () => number\n}\n\n// Added 4D noise\nexport class SimplexNoise {\n  private grad3 = [\n    [1, 1, 0],\n    [-1, 1, 0],\n    [1, -1, 0],\n    [-1, -1, 0],\n    [1, 0, 1],\n    [-1, 0, 1],\n    [1, 0, -1],\n    [-1, 0, -1],\n    [0, 1, 1],\n    [0, -1, 1],\n    [0, 1, -1],\n    [0, -1, -1],\n  ]\n\n  private grad4 = [\n    [0, 1, 1, 1],\n    [0, 1, 1, -1],\n    [0, 1, -1, 1],\n    [0, 1, -1, -1],\n    [0, -1, 1, 1],\n    [0, -1, 1, -1],\n    [0, -1, -1, 1],\n    [0, -1, -1, -1],\n    [1, 0, 1, 1],\n    [1, 0, 1, -1],\n    [1, 0, -1, 1],\n    [1, 0, -1, -1],\n    [-1, 0, 1, 1],\n    [-1, 0, 1, -1],\n    [-1, 0, -1, 1],\n    [-1, 0, -1, -1],\n    [1, 1, 0, 1],\n    [1, 1, 0, -1],\n    [1, -1, 0, 1],\n    [1, -1, 0, -1],\n    [-1, 1, 0, 1],\n    [-1, 1, 0, -1],\n    [-1, -1, 0, 1],\n    [-1, -1, 0, -1],\n    [1, 1, 1, 0],\n    [1, 1, -1, 0],\n    [1, -1, 1, 0],\n    [1, -1, -1, 0],\n    [-1, 1, 1, 0],\n    [-1, 1, -1, 0],\n    [-1, -1, 1, 0],\n    [-1, -1, -1, 0],\n  ]\n\n  private p: number[] = []\n\n  // To remove the need for index wrapping, double the permutation table length\n  private perm: number[] = []\n\n  // A lookup table to traverse the simplex around a given point in 4D.\n  // Details can be found where this table is used, in the 4D noise method.\n  private simplex = [\n    [0, 1, 2, 3],\n    [0, 1, 3, 2],\n    [0, 0, 0, 0],\n    [0, 2, 3, 1],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [1, 2, 3, 0],\n    [0, 2, 1, 3],\n    [0, 0, 0, 0],\n    [0, 3, 1, 2],\n    [0, 3, 2, 1],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [1, 3, 2, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [1, 2, 0, 3],\n    [0, 0, 0, 0],\n    [1, 3, 0, 2],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [2, 3, 0, 1],\n    [2, 3, 1, 0],\n    [1, 0, 2, 3],\n    [1, 0, 3, 2],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [2, 0, 3, 1],\n    [0, 0, 0, 0],\n    [2, 1, 3, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [2, 0, 1, 3],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [3, 0, 1, 2],\n    [3, 0, 2, 1],\n    [0, 0, 0, 0],\n    [3, 1, 2, 0],\n    [2, 1, 0, 3],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [3, 1, 0, 2],\n    [0, 0, 0, 0],\n    [3, 2, 0, 1],\n    [3, 2, 1, 0],\n  ]\n\n  /**\n   * You can pass in a random number generator object if you like.\n   * It is assumed to have a random() method.\n   */\n  constructor(r: NumberGenerator = Math) {\n    for (let i = 0; i < 256; i++) {\n      this.p[i] = Math.floor(r.random() * 256)\n    }\n\n    for (let i = 0; i < 512; i++) {\n      this.perm[i] = this.p[i & 255]\n    }\n  }\n\n  public dot = (g: number[], x: number, y: number): number => {\n    return g[0] * x + g[1] * y\n  }\n\n  public dot3 = (g: number[], x: number, y: number, z: number): number => {\n    return g[0] * x + g[1] * y + g[2] * z\n  }\n\n  public dot4 = (g: number[], x: number, y: number, z: number, w: number): number => {\n    return g[0] * x + g[1] * y + g[2] * z + g[3] * w\n  }\n\n  public noise = (xin: number, yin: number): number => {\n    let n0\n    let n1\n    let n2 // Noise contributions from the three corners\n    // Skew the input space to determine which simplex cell we're in\n    const F2 = 0.5 * (Math.sqrt(3.0) - 1.0)\n    const s = (xin + yin) * F2 // Hairy factor for 2D\n    const i = Math.floor(xin + s)\n    const j = Math.floor(yin + s)\n    const G2 = (3.0 - Math.sqrt(3.0)) / 6.0\n    const t = (i + j) * G2\n    const X0 = i - t // Unskew the cell origin back to (x,y) space\n    const Y0 = j - t\n    const x0 = xin - X0 // The x,y distances from the cell origin\n    const y0 = yin - Y0\n    // For the 2D case, the simplex shape is an equilateral triangle.\n    // Determine which simplex we are in.\n    // upper triangle, YX order: (0,0)->(0,1)->(1,1)\n    let i1 = 0\n    // Offsets for second (middle) corner of simplex in (i,j) coords\n    let j1 = 1\n    if (x0 > y0) {\n      i1 = 1\n      j1 = 0\n    }\n\n    // A step of (1,0) in (i,j) means a step of (1-c,-c) in (x,y), and\n    // a step of (0,1) in (i,j) means a step of (-c,1-c) in (x,y), where\n    // c = (3-sqrt(3))/6\n    const x1 = x0 - i1 + G2 // Offsets for middle corner in (x,y) unskewed coords\n    const y1 = y0 - j1 + G2\n    const x2 = x0 - 1.0 + 2.0 * G2 // Offsets for last corner in (x,y) unskewed coords\n    const y2 = y0 - 1.0 + 2.0 * G2\n    // Work out the hashed gradient indices of the three simplex corners\n    const ii = i & 255\n    const jj = j & 255\n    const gi0 = this.perm[ii + this.perm[jj]] % 12\n    const gi1 = this.perm[ii + i1 + this.perm[jj + j1]] % 12\n    const gi2 = this.perm[ii + 1 + this.perm[jj + 1]] % 12\n    // Calculate the contribution from the three corners\n    let t0 = 0.5 - x0 * x0 - y0 * y0\n    if (t0 < 0) {\n      n0 = 0.0\n    } else {\n      t0 *= t0\n      n0 = t0 * t0 * this.dot(this.grad3[gi0], x0, y0) // (x,y) of grad3 used for 2D gradient\n    }\n\n    let t1 = 0.5 - x1 * x1 - y1 * y1\n    if (t1 < 0) {\n      n1 = 0.0\n    } else {\n      t1 *= t1\n      n1 = t1 * t1 * this.dot(this.grad3[gi1], x1, y1)\n    }\n\n    let t2 = 0.5 - x2 * x2 - y2 * y2\n    if (t2 < 0) {\n      n2 = 0.0\n    } else {\n      t2 *= t2\n      n2 = t2 * t2 * this.dot(this.grad3[gi2], x2, y2)\n    }\n\n    // Add contributions from each corner to get the final noise value.\n    // The result is scaled to return values in the interval [-1,1].\n    return 70.0 * (n0 + n1 + n2)\n  }\n\n  // 3D simplex noise\n  private noise3d = (xin: number, yin: number, zin: number): number => {\n    // Noise contributions from the four corners\n    let n0\n    let n1\n    let n2\n    let n3\n    // Skew the input space to determine which simplex cell we're in\n    const F3 = 1.0 / 3.0\n    const s = (xin + yin + zin) * F3 // Very nice and simple skew factor for 3D\n    const i = Math.floor(xin + s)\n    const j = Math.floor(yin + s)\n    const k = Math.floor(zin + s)\n    const G3 = 1.0 / 6.0 // Very nice and simple unskew factor, too\n    const t = (i + j + k) * G3\n    const X0 = i - t // Unskew the cell origin back to (x,y,z) space\n    const Y0 = j - t\n    const Z0 = k - t\n    const x0 = xin - X0 // The x,y,z distances from the cell origin\n    const y0 = yin - Y0\n    const z0 = zin - Z0\n    // For the 3D case, the simplex shape is a slightly irregular tetrahedron.\n    // Determine which simplex we are in.\n    let i1\n    let j1\n    let k1 // Offsets for second corner of simplex in (i,j,k) coords\n    let i2\n    let j2\n    let k2 // Offsets for third corner of simplex in (i,j,k) coords\n    if (x0 >= y0) {\n      if (y0 >= z0) {\n        i1 = 1\n        j1 = 0\n        k1 = 0\n        i2 = 1\n        j2 = 1\n        k2 = 0\n\n        // X Y Z order\n      } else if (x0 >= z0) {\n        i1 = 1\n        j1 = 0\n        k1 = 0\n        i2 = 1\n        j2 = 0\n        k2 = 1\n\n        // X Z Y order\n      } else {\n        i1 = 0\n        j1 = 0\n        k1 = 1\n        i2 = 1\n        j2 = 0\n        k2 = 1\n      } // Z X Y order\n    } else {\n      // x0<y0\n\n      if (y0 < z0) {\n        i1 = 0\n        j1 = 0\n        k1 = 1\n        i2 = 0\n        j2 = 1\n        k2 = 1\n\n        // Z Y X order\n      } else if (x0 < z0) {\n        i1 = 0\n        j1 = 1\n        k1 = 0\n        i2 = 0\n        j2 = 1\n        k2 = 1\n\n        // Y Z X order\n      } else {\n        i1 = 0\n        j1 = 1\n        k1 = 0\n        i2 = 1\n        j2 = 1\n        k2 = 0\n      } // Y X Z order\n    }\n\n    // A step of (1,0,0) in (i,j,k) means a step of (1-c,-c,-c) in (x,y,z),\n    // a step of (0,1,0) in (i,j,k) means a step of (-c,1-c,-c) in (x,y,z), and\n    // a step of (0,0,1) in (i,j,k) means a step of (-c,-c,1-c) in (x,y,z), where\n    // c = 1/6.\n    const x1 = x0 - i1 + G3 // Offsets for second corner in (x,y,z) coords\n    const y1 = y0 - j1 + G3\n    const z1 = z0 - k1 + G3\n    const x2 = x0 - i2 + 2.0 * G3 // Offsets for third corner in (x,y,z) coords\n    const y2 = y0 - j2 + 2.0 * G3\n    const z2 = z0 - k2 + 2.0 * G3\n    const x3 = x0 - 1.0 + 3.0 * G3 // Offsets for last corner in (x,y,z) coords\n    const y3 = y0 - 1.0 + 3.0 * G3\n    const z3 = z0 - 1.0 + 3.0 * G3\n    // Work out the hashed gradient indices of the four simplex corners\n    const ii = i & 255\n    const jj = j & 255\n    const kk = k & 255\n    const gi0 = this.perm[ii + this.perm[jj + this.perm[kk]]] % 12\n    const gi1 = this.perm[ii + i1 + this.perm[jj + j1 + this.perm[kk + k1]]] % 12\n    const gi2 = this.perm[ii + i2 + this.perm[jj + j2 + this.perm[kk + k2]]] % 12\n    const gi3 = this.perm[ii + 1 + this.perm[jj + 1 + this.perm[kk + 1]]] % 12\n    // Calculate the contribution from the four corners\n    let t0 = 0.6 - x0 * x0 - y0 * y0 - z0 * z0\n    if (t0 < 0) {\n      n0 = 0.0\n    } else {\n      t0 *= t0\n      n0 = t0 * t0 * this.dot3(this.grad3[gi0], x0, y0, z0)\n    }\n\n    let t1 = 0.6 - x1 * x1 - y1 * y1 - z1 * z1\n    if (t1 < 0) {\n      n1 = 0.0\n    } else {\n      t1 *= t1\n      n1 = t1 * t1 * this.dot3(this.grad3[gi1], x1, y1, z1)\n    }\n\n    let t2 = 0.6 - x2 * x2 - y2 * y2 - z2 * z2\n    if (t2 < 0) {\n      n2 = 0.0\n    } else {\n      t2 *= t2\n      n2 = t2 * t2 * this.dot3(this.grad3[gi2], x2, y2, z2)\n    }\n\n    let t3 = 0.6 - x3 * x3 - y3 * y3 - z3 * z3\n    if (t3 < 0) {\n      n3 = 0.0\n    } else {\n      t3 *= t3\n      n3 = t3 * t3 * this.dot3(this.grad3[gi3], x3, y3, z3)\n    }\n\n    // Add contributions from each corner to get the final noise value.\n    // The result is scaled to stay just inside [-1,1]\n    return 32.0 * (n0 + n1 + n2 + n3)\n  }\n\n  // 4D simplex noise\n  public noise4d = (x: number, y: number, z: number, w: number): number => {\n    // For faster and easier lookups\n    const grad4 = this.grad4\n    const simplex = this.simplex\n    const perm = this.perm\n\n    // The skewing and unskewing factors are hairy again for the 4D case\n    const F4 = (Math.sqrt(5.0) - 1.0) / 4.0\n    const G4 = (5.0 - Math.sqrt(5.0)) / 20.0\n    let n0\n    let n1\n    let n2\n    let n3\n    let n4 // Noise contributions from the five corners\n    // Skew the (x,y,z,w) space to determine which cell of 24 simplices we're in\n    const s = (x + y + z + w) * F4 // Factor for 4D skewing\n    const i = Math.floor(x + s)\n    const j = Math.floor(y + s)\n    const k = Math.floor(z + s)\n    const l = Math.floor(w + s)\n    const t = (i + j + k + l) * G4 // Factor for 4D unskewing\n    const X0 = i - t // Unskew the cell origin back to (x,y,z,w) space\n    const Y0 = j - t\n    const Z0 = k - t\n    const W0 = l - t\n    const x0 = x - X0 // The x,y,z,w distances from the cell origin\n    const y0 = y - Y0\n    const z0 = z - Z0\n    const w0 = w - W0\n\n    // For the 4D case, the simplex is a 4D shape I won't even try to describe.\n    // To find out which of the 24 possible simplices we're in, we need to\n    // determine the magnitude ordering of x0, y0, z0 and w0.\n    // The method below is a good way of finding the ordering of x,y,z,w and\n    // then find the correct traversal order for the simplex we’re in.\n    // First, six pair-wise comparisons are performed between each possible pair\n    // of the four coordinates, and the results are used to add up binary bits\n    // for an integer index.\n    const c1 = x0 > y0 ? 32 : 0\n    const c2 = x0 > z0 ? 16 : 0\n    const c3 = y0 > z0 ? 8 : 0\n    const c4 = x0 > w0 ? 4 : 0\n    const c5 = y0 > w0 ? 2 : 0\n    const c6 = z0 > w0 ? 1 : 0\n    const c = c1 + c2 + c3 + c4 + c5 + c6\n    // The integer offsets for the second simplex corner\n    let i1\n    let j1\n    let k1\n    let l1\n\n    // The integer offsets for the third simplex corner\n    let i2\n    let j2\n    let k2\n    let l2\n\n    // The integer offsets for the fourth simplex corner\n    let i3\n    let j3\n    let k3\n    let l3\n    // simplex[c] is a 4-vector with the numbers 0, 1, 2 and 3 in some order.\n    // Many values of c will never occur, since e.g. x>y>z>w makes x<z, y<w and x<w\n    // impossible. Only the 24 indices which have non-zero entries make any sense.\n    // We use a thresholding to set the coordinates in turn from the largest magnitude.\n    // The number 3 in the \"simplex\" array is at the position of the largest coordinate.\n    i1 = simplex[c][0] >= 3 ? 1 : 0\n    j1 = simplex[c][1] >= 3 ? 1 : 0\n    k1 = simplex[c][2] >= 3 ? 1 : 0\n    l1 = simplex[c][3] >= 3 ? 1 : 0\n    // The number 2 in the \"simplex\" array is at the second largest coordinate.\n    i2 = simplex[c][0] >= 2 ? 1 : 0\n    j2 = simplex[c][1] >= 2 ? 1 : 0\n    k2 = simplex[c][2] >= 2 ? 1 : 0\n    l2 = simplex[c][3] >= 2 ? 1 : 0\n    // The number 1 in the \"simplex\" array is at the second smallest coordinate.\n    i3 = simplex[c][0] >= 1 ? 1 : 0\n    j3 = simplex[c][1] >= 1 ? 1 : 0\n    k3 = simplex[c][2] >= 1 ? 1 : 0\n    l3 = simplex[c][3] >= 1 ? 1 : 0\n    // The fifth corner has all coordinate offsets = 1, so no need to look that up.\n    const x1 = x0 - i1 + G4 // Offsets for second corner in (x,y,z,w) coords\n    const y1 = y0 - j1 + G4\n    const z1 = z0 - k1 + G4\n    const w1 = w0 - l1 + G4\n    const x2 = x0 - i2 + 2.0 * G4 // Offsets for third corner in (x,y,z,w) coords\n    const y2 = y0 - j2 + 2.0 * G4\n    const z2 = z0 - k2 + 2.0 * G4\n    const w2 = w0 - l2 + 2.0 * G4\n    const x3 = x0 - i3 + 3.0 * G4 // Offsets for fourth corner in (x,y,z,w) coords\n    const y3 = y0 - j3 + 3.0 * G4\n    const z3 = z0 - k3 + 3.0 * G4\n    const w3 = w0 - l3 + 3.0 * G4\n    const x4 = x0 - 1.0 + 4.0 * G4 // Offsets for last corner in (x,y,z,w) coords\n    const y4 = y0 - 1.0 + 4.0 * G4\n    const z4 = z0 - 1.0 + 4.0 * G4\n    const w4 = w0 - 1.0 + 4.0 * G4\n    // Work out the hashed gradient indices of the five simplex corners\n    const ii = i & 255\n    const jj = j & 255\n    const kk = k & 255\n    const ll = l & 255\n    const gi0 = perm[ii + perm[jj + perm[kk + perm[ll]]]] % 32\n    const gi1 = perm[ii + i1 + perm[jj + j1 + perm[kk + k1 + perm[ll + l1]]]] % 32\n    const gi2 = perm[ii + i2 + perm[jj + j2 + perm[kk + k2 + perm[ll + l2]]]] % 32\n    const gi3 = perm[ii + i3 + perm[jj + j3 + perm[kk + k3 + perm[ll + l3]]]] % 32\n    const gi4 = perm[ii + 1 + perm[jj + 1 + perm[kk + 1 + perm[ll + 1]]]] % 32\n    // Calculate the contribution from the five corners\n    let t0 = 0.6 - x0 * x0 - y0 * y0 - z0 * z0 - w0 * w0\n    if (t0 < 0) {\n      n0 = 0.0\n    } else {\n      t0 *= t0\n      n0 = t0 * t0 * this.dot4(grad4[gi0], x0, y0, z0, w0)\n    }\n\n    let t1 = 0.6 - x1 * x1 - y1 * y1 - z1 * z1 - w1 * w1\n    if (t1 < 0) {\n      n1 = 0.0\n    } else {\n      t1 *= t1\n      n1 = t1 * t1 * this.dot4(grad4[gi1], x1, y1, z1, w1)\n    }\n\n    let t2 = 0.6 - x2 * x2 - y2 * y2 - z2 * z2 - w2 * w2\n    if (t2 < 0) {\n      n2 = 0.0\n    } else {\n      t2 *= t2\n      n2 = t2 * t2 * this.dot4(grad4[gi2], x2, y2, z2, w2)\n    }\n\n    let t3 = 0.6 - x3 * x3 - y3 * y3 - z3 * z3 - w3 * w3\n    if (t3 < 0) {\n      n3 = 0.0\n    } else {\n      t3 *= t3\n      n3 = t3 * t3 * this.dot4(grad4[gi3], x3, y3, z3, w3)\n    }\n\n    let t4 = 0.6 - x4 * x4 - y4 * y4 - z4 * z4 - w4 * w4\n    if (t4 < 0) {\n      n4 = 0.0\n    } else {\n      t4 *= t4\n      n4 = t4 * t4 * this.dot4(grad4[gi4], x4, y4, z4, w4)\n    }\n\n    // Sum up and scale the result to cover the range [-1,1]\n    return 27.0 * (n0 + n1 + n2 + n3 + n4)\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAYO,MAAM,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAiIxB,YAAY,IAAqB,MAAM;AAhI/B,iCAAQ;AAAA,MACd,CAAC,GAAG,GAAG,CAAC;AAAA,MACR,CAAC,IAAI,GAAG,CAAC;AAAA,MACT,CAAC,GAAG,IAAI,CAAC;AAAA,MACT,CAAC,IAAI,IAAI,CAAC;AAAA,MACV,CAAC,GAAG,GAAG,CAAC;AAAA,MACR,CAAC,IAAI,GAAG,CAAC;AAAA,MACT,CAAC,GAAG,GAAG,EAAE;AAAA,MACT,CAAC,IAAI,GAAG,EAAE;AAAA,MACV,CAAC,GAAG,GAAG,CAAC;AAAA,MACR,CAAC,GAAG,IAAI,CAAC;AAAA,MACT,CAAC,GAAG,GAAG,EAAE;AAAA,MACT,CAAC,GAAG,IAAI,EAAE;AAAA,IAAA;AAGJ,iCAAQ;AAAA,MACd,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,EAAE;AAAA,MACZ,CAAC,GAAG,GAAG,IAAI,CAAC;AAAA,MACZ,CAAC,GAAG,GAAG,IAAI,EAAE;AAAA,MACb,CAAC,GAAG,IAAI,GAAG,CAAC;AAAA,MACZ,CAAC,GAAG,IAAI,GAAG,EAAE;AAAA,MACb,CAAC,GAAG,IAAI,IAAI,CAAC;AAAA,MACb,CAAC,GAAG,IAAI,IAAI,EAAE;AAAA,MACd,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,EAAE;AAAA,MACZ,CAAC,GAAG,GAAG,IAAI,CAAC;AAAA,MACZ,CAAC,GAAG,GAAG,IAAI,EAAE;AAAA,MACb,CAAC,IAAI,GAAG,GAAG,CAAC;AAAA,MACZ,CAAC,IAAI,GAAG,GAAG,EAAE;AAAA,MACb,CAAC,IAAI,GAAG,IAAI,CAAC;AAAA,MACb,CAAC,IAAI,GAAG,IAAI,EAAE;AAAA,MACd,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,EAAE;AAAA,MACZ,CAAC,GAAG,IAAI,GAAG,CAAC;AAAA,MACZ,CAAC,GAAG,IAAI,GAAG,EAAE;AAAA,MACb,CAAC,IAAI,GAAG,GAAG,CAAC;AAAA,MACZ,CAAC,IAAI,GAAG,GAAG,EAAE;AAAA,MACb,CAAC,IAAI,IAAI,GAAG,CAAC;AAAA,MACb,CAAC,IAAI,IAAI,GAAG,EAAE;AAAA,MACd,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,IAAI,CAAC;AAAA,MACZ,CAAC,GAAG,IAAI,GAAG,CAAC;AAAA,MACZ,CAAC,GAAG,IAAI,IAAI,CAAC;AAAA,MACb,CAAC,IAAI,GAAG,GAAG,CAAC;AAAA,MACZ,CAAC,IAAI,GAAG,IAAI,CAAC;AAAA,MACb,CAAC,IAAI,IAAI,GAAG,CAAC;AAAA,MACb,CAAC,IAAI,IAAI,IAAI,CAAC;AAAA,IAAA;AAGR,6BAAc,CAAA;AAGd;AAAA,gCAAiB,CAAA;AAIjB;AAAA;AAAA,mCAAU;AAAA,MAChB,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACX,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAiBN,+BAAM,CAAC,GAAa,GAAW,MAAsB;AAC1D,aAAO,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI;AAAA,IAAA;AAGpB,gCAAO,CAAC,GAAa,GAAW,GAAW,MAAsB;AAC/D,aAAA,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI;AAAA,IAAA;AAG/B,gCAAO,CAAC,GAAa,GAAW,GAAW,GAAW,MAAsB;AACjF,aAAO,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI;AAAA,IAAA;AAG1C,iCAAQ,CAAC,KAAa,QAAwB;AAC/C,UAAA;AACA,UAAA;AACA,UAAA;AAEJ,YAAM,KAAK,OAAO,KAAK,KAAK,CAAG,IAAI;AAC7B,YAAA,KAAK,MAAM,OAAO;AACxB,YAAM,IAAI,KAAK,MAAM,MAAM,CAAC;AAC5B,YAAM,IAAI,KAAK,MAAM,MAAM,CAAC;AAC5B,YAAM,MAAM,IAAM,KAAK,KAAK,CAAG,KAAK;AAC9B,YAAA,KAAK,IAAI,KAAK;AACpB,YAAM,KAAK,IAAI;AACf,YAAM,KAAK,IAAI;AACf,YAAM,KAAK,MAAM;AACjB,YAAM,KAAK,MAAM;AAIjB,UAAI,KAAK;AAET,UAAI,KAAK;AACT,UAAI,KAAK,IAAI;AACN,aAAA;AACA,aAAA;AAAA,MACP;AAKM,YAAA,KAAK,KAAK,KAAK;AACf,YAAA,KAAK,KAAK,KAAK;AACf,YAAA,KAAK,KAAK,IAAM,IAAM;AACtB,YAAA,KAAK,KAAK,IAAM,IAAM;AAE5B,YAAM,KAAK,IAAI;AACf,YAAM,KAAK,IAAI;AACT,YAAA,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,CAAC,IAAI;AACtC,YAAA,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,CAAC,IAAI;AAChD,YAAA,MAAM,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC,CAAC,IAAI;AAEpD,UAAI,KAAK,MAAM,KAAK,KAAK,KAAK;AAC9B,UAAI,KAAK,GAAG;AACL,aAAA;AAAA,MAAA,OACA;AACC,cAAA;AACD,aAAA,KAAK,KAAK,KAAK,IAAI,KAAK,MAAM,GAAG,GAAG,IAAI,EAAE;AAAA,MACjD;AAEA,UAAI,KAAK,MAAM,KAAK,KAAK,KAAK;AAC9B,UAAI,KAAK,GAAG;AACL,aAAA;AAAA,MAAA,OACA;AACC,cAAA;AACD,aAAA,KAAK,KAAK,KAAK,IAAI,KAAK,MAAM,GAAG,GAAG,IAAI,EAAE;AAAA,MACjD;AAEA,UAAI,KAAK,MAAM,KAAK,KAAK,KAAK;AAC9B,UAAI,KAAK,GAAG;AACL,aAAA;AAAA,MAAA,OACA;AACC,cAAA;AACD,aAAA,KAAK,KAAK,KAAK,IAAI,KAAK,MAAM,GAAG,GAAG,IAAI,EAAE;AAAA,MACjD;AAIO,aAAA,MAAQ,KAAK,KAAK;AAAA,IAAA;AAInB;AAAA,mCAAU,CAAC,KAAa,KAAa,QAAwB;AAE/D,UAAA;AACA,UAAA;AACA,UAAA;AACA,UAAA;AAEJ,YAAM,KAAK,IAAM;AACX,YAAA,KAAK,MAAM,MAAM,OAAO;AAC9B,YAAM,IAAI,KAAK,MAAM,MAAM,CAAC;AAC5B,YAAM,IAAI,KAAK,MAAM,MAAM,CAAC;AAC5B,YAAM,IAAI,KAAK,MAAM,MAAM,CAAC;AAC5B,YAAM,KAAK,IAAM;AACX,YAAA,KAAK,IAAI,IAAI,KAAK;AACxB,YAAM,KAAK,IAAI;AACf,YAAM,KAAK,IAAI;AACf,YAAM,KAAK,IAAI;AACf,YAAM,KAAK,MAAM;AACjB,YAAM,KAAK,MAAM;AACjB,YAAM,KAAK,MAAM;AAGb,UAAA;AACA,UAAA;AACA,UAAA;AACA,UAAA;AACA,UAAA;AACA,UAAA;AACJ,UAAI,MAAM,IAAI;AACZ,YAAI,MAAM,IAAI;AACP,eAAA;AACA,eAAA;AACA,eAAA;AACA,eAAA;AACA,eAAA;AACA,eAAA;AAAA,QAAA,WAGI,MAAM,IAAI;AACd,eAAA;AACA,eAAA;AACA,eAAA;AACA,eAAA;AACA,eAAA;AACA,eAAA;AAAA,QAAA,OAGA;AACA,eAAA;AACA,eAAA;AACA,eAAA;AACA,eAAA;AACA,eAAA;AACA,eAAA;AAAA,QACP;AAAA,MAAA,OACK;AAGL,YAAI,KAAK,IAAI;AACN,eAAA;AACA,eAAA;AACA,eAAA;AACA,eAAA;AACA,eAAA;AACA,eAAA;AAAA,QAAA,WAGI,KAAK,IAAI;AACb,eAAA;AACA,eAAA;AACA,eAAA;AACA,eAAA;AACA,eAAA;AACA,eAAA;AAAA,QAAA,OAGA;AACA,eAAA;AACA,eAAA;AACA,eAAA;AACA,eAAA;AACA,eAAA;AACA,eAAA;AAAA,QACP;AAAA,MACF;AAMM,YAAA,KAAK,KAAK,KAAK;AACf,YAAA,KAAK,KAAK,KAAK;AACf,YAAA,KAAK,KAAK,KAAK;AACf,YAAA,KAAK,KAAK,KAAK,IAAM;AACrB,YAAA,KAAK,KAAK,KAAK,IAAM;AACrB,YAAA,KAAK,KAAK,KAAK,IAAM;AACrB,YAAA,KAAK,KAAK,IAAM,IAAM;AACtB,YAAA,KAAK,KAAK,IAAM,IAAM;AACtB,YAAA,KAAK,KAAK,IAAM,IAAM;AAE5B,YAAM,KAAK,IAAI;AACf,YAAM,KAAK,IAAI;AACf,YAAM,KAAK,IAAI;AACf,YAAM,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,CAAC,CAAC,IAAI;AAC5D,YAAM,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,CAAC,CAAC,IAAI;AAC3E,YAAM,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,CAAC,CAAC,IAAI;AAC3E,YAAM,MAAM,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI;AAExE,UAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK;AACxC,UAAI,KAAK,GAAG;AACL,aAAA;AAAA,MAAA,OACA;AACC,cAAA;AACD,aAAA,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE;AAAA,MACtD;AAEA,UAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK;AACxC,UAAI,KAAK,GAAG;AACL,aAAA;AAAA,MAAA,OACA;AACC,cAAA;AACD,aAAA,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE;AAAA,MACtD;AAEA,UAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK;AACxC,UAAI,KAAK,GAAG;AACL,aAAA;AAAA,MAAA,OACA;AACC,cAAA;AACD,aAAA,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE;AAAA,MACtD;AAEA,UAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK;AACxC,UAAI,KAAK,GAAG;AACL,aAAA;AAAA,MAAA,OACA;AACC,cAAA;AACD,aAAA,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE;AAAA,MACtD;AAIO,aAAA,MAAQ,KAAK,KAAK,KAAK;AAAA,IAAA;AAIzB;AAAA,mCAAU,CAAC,GAAW,GAAW,GAAW,MAAsB;AAEvE,YAAM,QAAQ,KAAK;AACnB,YAAM,UAAU,KAAK;AACrB,YAAM,OAAO,KAAK;AAGlB,YAAM,MAAM,KAAK,KAAK,CAAG,IAAI,KAAO;AACpC,YAAM,MAAM,IAAM,KAAK,KAAK,CAAG,KAAK;AAChC,UAAA;AACA,UAAA;AACA,UAAA;AACA,UAAA;AACA,UAAA;AAEJ,YAAM,KAAK,IAAI,IAAI,IAAI,KAAK;AAC5B,YAAM,IAAI,KAAK,MAAM,IAAI,CAAC;AAC1B,YAAM,IAAI,KAAK,MAAM,IAAI,CAAC;AAC1B,YAAM,IAAI,KAAK,MAAM,IAAI,CAAC;AAC1B,YAAM,IAAI,KAAK,MAAM,IAAI,CAAC;AAC1B,YAAM,KAAK,IAAI,IAAI,IAAI,KAAK;AAC5B,YAAM,KAAK,IAAI;AACf,YAAM,KAAK,IAAI;AACf,YAAM,KAAK,IAAI;AACf,YAAM,KAAK,IAAI;AACf,YAAM,KAAK,IAAI;AACf,YAAM,KAAK,IAAI;AACf,YAAM,KAAK,IAAI;AACf,YAAM,KAAK,IAAI;AAUT,YAAA,KAAK,KAAK,KAAK,KAAK;AACpB,YAAA,KAAK,KAAK,KAAK,KAAK;AACpB,YAAA,KAAK,KAAK,KAAK,IAAI;AACnB,YAAA,KAAK,KAAK,KAAK,IAAI;AACnB,YAAA,KAAK,KAAK,KAAK,IAAI;AACnB,YAAA,KAAK,KAAK,KAAK,IAAI;AACzB,YAAM,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK;AAE/B,UAAA;AACA,UAAA;AACA,UAAA;AACA,UAAA;AAGA,UAAA;AACA,UAAA;AACA,UAAA;AACA,UAAA;AAGA,UAAA;AACA,UAAA;AACA,UAAA;AACA,UAAA;AAMJ,WAAK,QAAQ,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI;AAC9B,WAAK,QAAQ,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI;AAC9B,WAAK,QAAQ,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI;AAC9B,WAAK,QAAQ,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI;AAE9B,WAAK,QAAQ,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI;AAC9B,WAAK,QAAQ,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI;AAC9B,WAAK,QAAQ,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI;AAC9B,WAAK,QAAQ,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI;AAE9B,WAAK,QAAQ,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI;AAC9B,WAAK,QAAQ,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI;AAC9B,WAAK,QAAQ,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI;AAC9B,WAAK,QAAQ,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI;AAExB,YAAA,KAAK,KAAK,KAAK;AACf,YAAA,KAAK,KAAK,KAAK;AACf,YAAA,KAAK,KAAK,KAAK;AACf,YAAA,KAAK,KAAK,KAAK;AACf,YAAA,KAAK,KAAK,KAAK,IAAM;AACrB,YAAA,KAAK,KAAK,KAAK,IAAM;AACrB,YAAA,KAAK,KAAK,KAAK,IAAM;AACrB,YAAA,KAAK,KAAK,KAAK,IAAM;AACrB,YAAA,KAAK,KAAK,KAAK,IAAM;AACrB,YAAA,KAAK,KAAK,KAAK,IAAM;AACrB,YAAA,KAAK,KAAK,KAAK,IAAM;AACrB,YAAA,KAAK,KAAK,KAAK,IAAM;AACrB,YAAA,KAAK,KAAK,IAAM,IAAM;AACtB,YAAA,KAAK,KAAK,IAAM,IAAM;AACtB,YAAA,KAAK,KAAK,IAAM,IAAM;AACtB,YAAA,KAAK,KAAK,IAAM,IAAM;AAE5B,YAAM,KAAK,IAAI;AACf,YAAM,KAAK,IAAI;AACf,YAAM,KAAK,IAAI;AACf,YAAM,KAAK,IAAI;AACf,YAAM,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI;AACxD,YAAM,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI;AAC5E,YAAM,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI;AAC5E,YAAM,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI;AAC5E,YAAM,MAAM,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;AAEpE,UAAA,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAClD,UAAI,KAAK,GAAG;AACL,aAAA;AAAA,MAAA,OACA;AACC,cAAA;AACD,aAAA,KAAK,KAAK,KAAK,KAAK,MAAM,GAAG,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,MACrD;AAEI,UAAA,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAClD,UAAI,KAAK,GAAG;AACL,aAAA;AAAA,MAAA,OACA;AACC,cAAA;AACD,aAAA,KAAK,KAAK,KAAK,KAAK,MAAM,GAAG,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,MACrD;AAEI,UAAA,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAClD,UAAI,KAAK,GAAG;AACL,aAAA;AAAA,MAAA,OACA;AACC,cAAA;AACD,aAAA,KAAK,KAAK,KAAK,KAAK,MAAM,GAAG,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,MACrD;AAEI,UAAA,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAClD,UAAI,KAAK,GAAG;AACL,aAAA;AAAA,MAAA,OACA;AACC,cAAA;AACD,aAAA,KAAK,KAAK,KAAK,KAAK,MAAM,GAAG,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,MACrD;AAEI,UAAA,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAClD,UAAI,KAAK,GAAG;AACL,aAAA;AAAA,MAAA,OACA;AACC,cAAA;AACD,aAAA,KAAK,KAAK,KAAK,KAAK,MAAM,GAAG,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,MACrD;AAGA,aAAO,MAAQ,KAAK,KAAK,KAAK,KAAK;AAAA,IAAA;AAnYnC,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AACvB,WAAA,EAAE,CAAC,IAAI,KAAK,MAAM,EAAE,WAAW,GAAG;AAAA,IACzC;AAEA,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,WAAK,KAAK,CAAC,IAAI,KAAK,EAAE,IAAI,GAAG;AAAA,IAC/B;AAAA,EACF;AA8XF;"}