{"version": 3, "file": "Capsule.cjs", "sources": ["../../src/math/Capsule.js"], "sourcesContent": ["import { Vector3 } from 'three'\n\nconst _v1 = /* @__PURE__ */ new Vector3()\nconst _v2 = /* @__PURE__ */ new Vector3()\nconst _v3 = /* @__PURE__ */ new Vector3()\n\nconst EPS = 1e-10\n\nclass Capsule {\n  constructor(start = new Vector3(0, 0, 0), end = new Vector3(0, 1, 0), radius = 1) {\n    this.start = start\n    this.end = end\n    this.radius = radius\n  }\n\n  clone() {\n    return new Capsule(this.start.clone(), this.end.clone(), this.radius)\n  }\n\n  set(start, end, radius) {\n    this.start.copy(start)\n    this.end.copy(end)\n    this.radius = radius\n  }\n\n  copy(capsule) {\n    this.start.copy(capsule.start)\n    this.end.copy(capsule.end)\n    this.radius = capsule.radius\n  }\n\n  getCenter(target) {\n    return target.copy(this.end).add(this.start).multiplyScalar(0.5)\n  }\n\n  translate(v) {\n    this.start.add(v)\n    this.end.add(v)\n  }\n\n  checkAABBAxis(p1x, p1y, p2x, p2y, minx, maxx, miny, maxy, radius) {\n    return (\n      (minx - p1x < radius || minx - p2x < radius) &&\n      (p1x - maxx < radius || p2x - maxx < radius) &&\n      (miny - p1y < radius || miny - p2y < radius) &&\n      (p1y - maxy < radius || p2y - maxy < radius)\n    )\n  }\n\n  intersectsBox(box) {\n    return (\n      this.checkAABBAxis(\n        this.start.x,\n        this.start.y,\n        this.end.x,\n        this.end.y,\n        box.min.x,\n        box.max.x,\n        box.min.y,\n        box.max.y,\n        this.radius,\n      ) &&\n      this.checkAABBAxis(\n        this.start.x,\n        this.start.z,\n        this.end.x,\n        this.end.z,\n        box.min.x,\n        box.max.x,\n        box.min.z,\n        box.max.z,\n        this.radius,\n      ) &&\n      this.checkAABBAxis(\n        this.start.y,\n        this.start.z,\n        this.end.y,\n        this.end.z,\n        box.min.y,\n        box.max.y,\n        box.min.z,\n        box.max.z,\n        this.radius,\n      )\n    )\n  }\n\n  lineLineMinimumPoints(line1, line2) {\n    const r = _v1.copy(line1.end).sub(line1.start)\n    const s = _v2.copy(line2.end).sub(line2.start)\n    const w = _v3.copy(line2.start).sub(line1.start)\n\n    const a = r.dot(s),\n      b = r.dot(r),\n      c = s.dot(s),\n      d = s.dot(w),\n      e = r.dot(w)\n\n    let t1, t2\n    const divisor = b * c - a * a\n\n    if (Math.abs(divisor) < EPS) {\n      const d1 = -d / c\n      const d2 = (a - d) / c\n\n      if (Math.abs(d1 - 0.5) < Math.abs(d2 - 0.5)) {\n        t1 = 0\n        t2 = d1\n      } else {\n        t1 = 1\n        t2 = d2\n      }\n    } else {\n      t1 = (d * a + e * c) / divisor\n      t2 = (t1 * a - d) / c\n    }\n\n    t2 = Math.max(0, Math.min(1, t2))\n    t1 = Math.max(0, Math.min(1, t1))\n\n    const point1 = r.multiplyScalar(t1).add(line1.start)\n    const point2 = s.multiplyScalar(t2).add(line2.start)\n\n    return [point1, point2]\n  }\n}\n\nexport { Capsule }\n"], "names": ["Vector3"], "mappings": ";;;AAEA,MAAM,MAAsB,oBAAIA,MAAAA,QAAS;AACzC,MAAM,MAAsB,oBAAIA,MAAAA,QAAS;AACzC,MAAM,MAAsB,oBAAIA,MAAAA,QAAS;AAEzC,MAAM,MAAM;AAEZ,MAAM,QAAQ;AAAA,EACZ,YAAY,QAAQ,IAAIA,MAAAA,QAAQ,GAAG,GAAG,CAAC,GAAG,MAAM,IAAIA,MAAAA,QAAQ,GAAG,GAAG,CAAC,GAAG,SAAS,GAAG;AAChF,SAAK,QAAQ;AACb,SAAK,MAAM;AACX,SAAK,SAAS;AAAA,EACf;AAAA,EAED,QAAQ;AACN,WAAO,IAAI,QAAQ,KAAK,MAAM,MAAK,GAAI,KAAK,IAAI,SAAS,KAAK,MAAM;AAAA,EACrE;AAAA,EAED,IAAI,OAAO,KAAK,QAAQ;AACtB,SAAK,MAAM,KAAK,KAAK;AACrB,SAAK,IAAI,KAAK,GAAG;AACjB,SAAK,SAAS;AAAA,EACf;AAAA,EAED,KAAK,SAAS;AACZ,SAAK,MAAM,KAAK,QAAQ,KAAK;AAC7B,SAAK,IAAI,KAAK,QAAQ,GAAG;AACzB,SAAK,SAAS,QAAQ;AAAA,EACvB;AAAA,EAED,UAAU,QAAQ;AAChB,WAAO,OAAO,KAAK,KAAK,GAAG,EAAE,IAAI,KAAK,KAAK,EAAE,eAAe,GAAG;AAAA,EAChE;AAAA,EAED,UAAU,GAAG;AACX,SAAK,MAAM,IAAI,CAAC;AAChB,SAAK,IAAI,IAAI,CAAC;AAAA,EACf;AAAA,EAED,cAAc,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,MAAM,MAAM,QAAQ;AAChE,YACG,OAAO,MAAM,UAAU,OAAO,MAAM,YACpC,MAAM,OAAO,UAAU,MAAM,OAAO,YACpC,OAAO,MAAM,UAAU,OAAO,MAAM,YACpC,MAAM,OAAO,UAAU,MAAM,OAAO;AAAA,EAExC;AAAA,EAED,cAAc,KAAK;AACjB,WACE,KAAK;AAAA,MACH,KAAK,MAAM;AAAA,MACX,KAAK,MAAM;AAAA,MACX,KAAK,IAAI;AAAA,MACT,KAAK,IAAI;AAAA,MACT,IAAI,IAAI;AAAA,MACR,IAAI,IAAI;AAAA,MACR,IAAI,IAAI;AAAA,MACR,IAAI,IAAI;AAAA,MACR,KAAK;AAAA,IACN,KACD,KAAK;AAAA,MACH,KAAK,MAAM;AAAA,MACX,KAAK,MAAM;AAAA,MACX,KAAK,IAAI;AAAA,MACT,KAAK,IAAI;AAAA,MACT,IAAI,IAAI;AAAA,MACR,IAAI,IAAI;AAAA,MACR,IAAI,IAAI;AAAA,MACR,IAAI,IAAI;AAAA,MACR,KAAK;AAAA,IACN,KACD,KAAK;AAAA,MACH,KAAK,MAAM;AAAA,MACX,KAAK,MAAM;AAAA,MACX,KAAK,IAAI;AAAA,MACT,KAAK,IAAI;AAAA,MACT,IAAI,IAAI;AAAA,MACR,IAAI,IAAI;AAAA,MACR,IAAI,IAAI;AAAA,MACR,IAAI,IAAI;AAAA,MACR,KAAK;AAAA,IACN;AAAA,EAEJ;AAAA,EAED,sBAAsB,OAAO,OAAO;AAClC,UAAM,IAAI,IAAI,KAAK,MAAM,GAAG,EAAE,IAAI,MAAM,KAAK;AAC7C,UAAM,IAAI,IAAI,KAAK,MAAM,GAAG,EAAE,IAAI,MAAM,KAAK;AAC7C,UAAM,IAAI,IAAI,KAAK,MAAM,KAAK,EAAE,IAAI,MAAM,KAAK;AAE/C,UAAM,IAAI,EAAE,IAAI,CAAC,GACf,IAAI,EAAE,IAAI,CAAC,GACX,IAAI,EAAE,IAAI,CAAC,GACX,IAAI,EAAE,IAAI,CAAC,GACX,IAAI,EAAE,IAAI,CAAC;AAEb,QAAI,IAAI;AACR,UAAM,UAAU,IAAI,IAAI,IAAI;AAE5B,QAAI,KAAK,IAAI,OAAO,IAAI,KAAK;AAC3B,YAAM,KAAK,CAAC,IAAI;AAChB,YAAM,MAAM,IAAI,KAAK;AAErB,UAAI,KAAK,IAAI,KAAK,GAAG,IAAI,KAAK,IAAI,KAAK,GAAG,GAAG;AAC3C,aAAK;AACL,aAAK;AAAA,MACb,OAAa;AACL,aAAK;AACL,aAAK;AAAA,MACN;AAAA,IACP,OAAW;AACL,YAAM,IAAI,IAAI,IAAI,KAAK;AACvB,YAAM,KAAK,IAAI,KAAK;AAAA,IACrB;AAED,SAAK,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,EAAE,CAAC;AAChC,SAAK,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,EAAE,CAAC;AAEhC,UAAM,SAAS,EAAE,eAAe,EAAE,EAAE,IAAI,MAAM,KAAK;AACnD,UAAM,SAAS,EAAE,eAAe,EAAE,EAAE,IAAI,MAAM,KAAK;AAEnD,WAAO,CAAC,QAAQ,MAAM;AAAA,EACvB;AACH;;"}