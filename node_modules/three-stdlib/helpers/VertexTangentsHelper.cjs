"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const THREE = require("three");
const _v1 = /* @__PURE__ */ new THREE.Vector3();
const _v2 = /* @__PURE__ */ new THREE.Vector3();
class VertexTangentsHelper extends THREE.LineSegments {
  constructor(object, size = 1, color = 65535) {
    const geometry = new THREE.BufferGeometry();
    const nTangents = object.geometry.attributes.tangent.count;
    const positions = new THREE.Float32BufferAttribute(nTangents * 2 * 3, 3);
    geometry.setAttribute("position", positions);
    super(geometry, new THREE.LineBasicMaterial({ color, toneMapped: false }));
    this.object = object;
    this.size = size;
    this.type = "VertexTangentsHelper";
    this.matrixAutoUpdate = false;
    this.update();
  }
  update() {
    this.object.updateMatrixWorld(true);
    const matrixWorld = this.object.matrixWorld;
    const position = this.geometry.attributes.position;
    const objGeometry = this.object.geometry;
    const objPos = objGeometry.attributes.position;
    const objTan = objGeometry.attributes.tangent;
    let idx = 0;
    for (let j = 0, jl = objPos.count; j < jl; j++) {
      _v1.fromBufferAttribute(objPos, j).applyMatrix4(matrixWorld);
      _v2.fromBufferAttribute(objTan, j);
      _v2.transformDirection(matrixWorld).multiplyScalar(this.size).add(_v1);
      position.setXYZ(idx, _v1.x, _v1.y, _v1.z);
      idx = idx + 1;
      position.setXYZ(idx, _v2.x, _v2.y, _v2.z);
      idx = idx + 1;
    }
    position.needsUpdate = true;
  }
  dispose() {
    this.geometry.dispose();
    this.material.dispose();
  }
}
exports.VertexTangentsHelper = VertexTangentsHelper;
//# sourceMappingURL=VertexTangentsHelper.cjs.map
