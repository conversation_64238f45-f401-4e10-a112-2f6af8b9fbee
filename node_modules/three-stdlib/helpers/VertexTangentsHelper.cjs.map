{"version": 3, "file": "VertexTangentsHelper.cjs", "sources": ["../../src/helpers/VertexTangentsHelper.js"], "sourcesContent": ["import { BufferGeometry, Float32BufferAttribute, LineSegments, LineBasicMaterial, Vector3 } from 'three'\n\nconst _v1 = /* @__PURE__ */ new Vector3()\nconst _v2 = /* @__PURE__ */ new Vector3()\n\nclass VertexTangentsHelper extends LineSegments {\n  constructor(object, size = 1, color = 0x00ffff) {\n    const geometry = new BufferGeometry()\n\n    const nTangents = object.geometry.attributes.tangent.count\n    const positions = new Float32BufferAttribute(nTangents * 2 * 3, 3)\n\n    geometry.setAttribute('position', positions)\n\n    super(geometry, new LineBasicMaterial({ color, toneMapped: false }))\n\n    this.object = object\n    this.size = size\n    this.type = 'VertexTangentsHelper'\n\n    //\n\n    this.matrixAutoUpdate = false\n\n    this.update()\n  }\n\n  update() {\n    this.object.updateMatrixWorld(true)\n\n    const matrixWorld = this.object.matrixWorld\n\n    const position = this.geometry.attributes.position\n\n    //\n\n    const objGeometry = this.object.geometry\n\n    const objPos = objGeometry.attributes.position\n\n    const objTan = objGeometry.attributes.tangent\n\n    let idx = 0\n\n    // for simplicity, ignore index and drawcalls, and render every tangent\n\n    for (let j = 0, jl = objPos.count; j < jl; j++) {\n      _v1.fromBufferAttribute(objPos, j).applyMatrix4(matrixWorld)\n\n      _v2.fromBufferAttribute(objTan, j)\n\n      _v2.transformDirection(matrixWorld).multiplyScalar(this.size).add(_v1)\n\n      position.setXYZ(idx, _v1.x, _v1.y, _v1.z)\n\n      idx = idx + 1\n\n      position.setXYZ(idx, _v2.x, _v2.y, _v2.z)\n\n      idx = idx + 1\n    }\n\n    position.needsUpdate = true\n  }\n\n  dispose() {\n    this.geometry.dispose()\n    this.material.dispose()\n  }\n}\n\nexport { VertexTangentsHelper }\n"], "names": ["Vector3", "LineSegments", "BufferGeometry", "Float32BufferAttribute", "LineBasicMaterial"], "mappings": ";;;AAEA,MAAM,MAAsB,oBAAIA,MAAAA,QAAS;AACzC,MAAM,MAAsB,oBAAIA,MAAAA,QAAS;AAEzC,MAAM,6BAA6BC,MAAAA,aAAa;AAAA,EAC9C,YAAY,QAAQ,OAAO,GAAG,QAAQ,OAAU;AAC9C,UAAM,WAAW,IAAIC,qBAAgB;AAErC,UAAM,YAAY,OAAO,SAAS,WAAW,QAAQ;AACrD,UAAM,YAAY,IAAIC,MAAsB,uBAAC,YAAY,IAAI,GAAG,CAAC;AAEjE,aAAS,aAAa,YAAY,SAAS;AAE3C,UAAM,UAAU,IAAIC,MAAiB,kBAAC,EAAE,OAAO,YAAY,MAAK,CAAE,CAAC;AAEnE,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,OAAO;AAIZ,SAAK,mBAAmB;AAExB,SAAK,OAAQ;AAAA,EACd;AAAA,EAED,SAAS;AACP,SAAK,OAAO,kBAAkB,IAAI;AAElC,UAAM,cAAc,KAAK,OAAO;AAEhC,UAAM,WAAW,KAAK,SAAS,WAAW;AAI1C,UAAM,cAAc,KAAK,OAAO;AAEhC,UAAM,SAAS,YAAY,WAAW;AAEtC,UAAM,SAAS,YAAY,WAAW;AAEtC,QAAI,MAAM;AAIV,aAAS,IAAI,GAAG,KAAK,OAAO,OAAO,IAAI,IAAI,KAAK;AAC9C,UAAI,oBAAoB,QAAQ,CAAC,EAAE,aAAa,WAAW;AAE3D,UAAI,oBAAoB,QAAQ,CAAC;AAEjC,UAAI,mBAAmB,WAAW,EAAE,eAAe,KAAK,IAAI,EAAE,IAAI,GAAG;AAErE,eAAS,OAAO,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAExC,YAAM,MAAM;AAEZ,eAAS,OAAO,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAExC,YAAM,MAAM;AAAA,IACb;AAED,aAAS,cAAc;AAAA,EACxB;AAAA,EAED,UAAU;AACR,SAAK,SAAS,QAAS;AACvB,SAAK,SAAS,QAAS;AAAA,EACxB;AACH;;"}