{"version": 3, "file": "SSRShader.cjs", "sources": ["../../src/shaders/SSRShader.ts"], "sourcesContent": ["import { Matrix4, Vector2 } from 'three'\n/**\n * References:\n * https://lettier.github.io/3d-game-shaders-for-beginners/screen-space-reflection.html\n */\n\nexport const SSRShader = {\n  defines: {\n    MAX_STEP: 0,\n    isPerspectiveCamera: true,\n    isDistanceAttenuation: true,\n    isFresnel: true,\n    isInfiniteThick: false,\n    isSelective: false,\n  },\n\n  uniforms: {\n    tDiffuse: { value: null },\n    tNormal: { value: null },\n    tMetalness: { value: null },\n    tDepth: { value: null },\n    cameraNear: { value: null },\n    cameraFar: { value: null },\n    resolution: { value: /* @__PURE__ */ new Vector2() },\n    cameraProjectionMatrix: { value: /* @__PURE__ */ new Matrix4() },\n    cameraInverseProjectionMatrix: { value: /* @__PURE__ */ new Matrix4() },\n    opacity: { value: 0.5 },\n    maxDistance: { value: 180 },\n    cameraRange: { value: 0 },\n    surfDist: { value: 0.007 },\n    thickTolerance: { value: 0.03 },\n  },\n\n  vertexShader: /* glsl */ `\n\n    varying vec2 vUv;\n\n    void main() {\n\n\t\t\tvUv = uv;\n\n\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n\n  `,\n\n  fragmentShader: /* glsl */ `\n\t\t// precision highp float;\n\t\tprecision highp sampler2D;\n\t\tvarying vec2 vUv;\n\t\tuniform sampler2D tDepth;\n\t\tuniform sampler2D tNormal;\n\t\tuniform sampler2D tMetalness;\n\t\tuniform sampler2D tDiffuse;\n\t\tuniform float cameraRange;\n\t\tuniform vec2 resolution;\n\t\tuniform float opacity;\n\t\tuniform float cameraNear;\n\t\tuniform float cameraFar;\n\t\tuniform float maxDistance;\n\t\tuniform float surfDist;\n\t\tuniform mat4 cameraProjectionMatrix;\n\t\tuniform mat4 cameraInverseProjectionMatrix;\n\t\tuniform float thickTolerance;\n\t\t#include <packing>\n\t\tfloat pointToLineDistance(vec3 x0, vec3 x1, vec3 x2) {\n\t\t\t//x0: point, x1: linePointA, x2: linePointB\n\t\t\t//https://mathworld.wolfram.com/Point-LineDistance3-Dimensional.html\n\t\t\treturn length(cross(x0-x1,x0-x2))/length(x2-x1);\n\t\t}\n\t\tfloat pointPlaneDistance(vec3 point,vec3 planePoint,vec3 planeNormal){\n\t\t\t// https://mathworld.wolfram.com/Point-PlaneDistance.html\n\t\t\t//// https://en.wikipedia.org/wiki/Plane_(geometry)\n\t\t\t//// http://paulbourke.net/geometry/pointlineplane/\n\t\t\tfloat a=planeNormal.x,b=planeNormal.y,c=planeNormal.z;\n\t\t\tfloat x0=point.x,y0=point.y,z0=point.z;\n\t\t\tfloat x=planePoint.x,y=planePoint.y,z=planePoint.z;\n\t\t\tfloat d=-(a*x+b*y+c*z);\n\t\t\tfloat distance=(a*x0+b*y0+c*z0+d)/sqrt(a*a+b*b+c*c);\n\t\t\treturn distance;\n\t\t}\n\t\tfloat getDepth( const in vec2 uv ) {\n\t\t\treturn texture2D( tDepth, uv ).x;\n\t\t}\n\t\tfloat getViewZ( const in float depth ) {\n\t\t\t#ifdef isPerspectiveCamera\n\t\t\t\treturn perspectiveDepthToViewZ( depth, cameraNear, cameraFar );\n\t\t\t#else\n\t\t\t\treturn orthographicDepthToViewZ( depth, cameraNear, cameraFar );\n\t\t\t#endif\n\t\t}\n\t\tvec3 getViewPosition( const in vec2 uv, const in float depth/*clip space*/, const in float clipW ) {\n\t\t\tvec4 clipPosition = vec4( ( vec3( uv, depth ) - 0.5 ) * 2.0, 1.0 );//ndc\n\t\t\tclipPosition *= clipW; //clip\n\t\t\treturn ( cameraInverseProjectionMatrix * clipPosition ).xyz;//view\n\t\t}\n\t\tvec3 getViewNormal( const in vec2 uv ) {\n\t\t\treturn unpackRGBToNormal( texture2D( tNormal, uv ).xyz );\n\t\t}\n\t\tvec2 viewPositionToXY(vec3 viewPosition){\n\t\t\tvec2 xy;\n\t\t\tvec4 clip=cameraProjectionMatrix*vec4(viewPosition,1);\n\t\t\txy=clip.xy;//clip\n\t\t\tfloat clipW=clip.w;\n\t\t\txy/=clipW;//NDC\n\t\t\txy=(xy+1.)/2.;//uv\n\t\t\txy*=resolution;//screen\n\t\t\treturn xy;\n\t\t}\n\t\tvoid main(){\n\t\t\t#ifdef isSelective\n\t\t\t\tfloat metalness=texture2D(tMetalness,vUv).r;\n\t\t\t\tif(metalness==0.) return;\n\t\t\t#endif\n\n\t\t\tfloat depth = getDepth( vUv );\n\t\t\tfloat viewZ = getViewZ( depth );\n\t\t\tif(-viewZ>=cameraFar) return;\n\n\t\t\tfloat clipW = cameraProjectionMatrix[2][3] * viewZ+cameraProjectionMatrix[3][3];\n\t\t\tvec3 viewPosition=getViewPosition( vUv, depth, clipW );\n\n\t\t\tvec2 d0=gl_FragCoord.xy;\n\t\t\tvec2 d1;\n\n\t\t\tvec3 viewNormal=getViewNormal( vUv );\n\n\t\t\t#ifdef isPerspectiveCamera\n\t\t\t\tvec3 viewIncidenceDir=normalize(viewPosition);\n\t\t\t\tvec3 viewReflectDir=reflect(viewIncidenceDir,viewNormal);\n\t\t\t#else\n\t\t\t\tvec3 viewIncidenceDir=vec3(0,0,-1);\n\t\t\t\tvec3 viewReflectDir=reflect(viewIncidenceDir,viewNormal);\n\t\t\t#endif\n\n\t\t\tfloat maxReflectRayLen=maxDistance/dot(-viewIncidenceDir,viewNormal);\n\t\t\t// dot(a,b)==length(a)*length(b)*cos(theta) // https://www.mathsisfun.com/algebra/vectors-dot-product.html\n\t\t\t// if(a.isNormalized&&b.isNormalized) dot(a,b)==cos(theta)\n\t\t\t// maxDistance/maxReflectRayLen=cos(theta)\n\t\t\t// maxDistance/maxReflectRayLen==dot(a,b)\n\t\t\t// maxReflectRayLen==maxDistance/dot(a,b)\n\n\t\t\tvec3 d1viewPosition=viewPosition+viewReflectDir*maxReflectRayLen;\n\t\t\t#ifdef isPerspectiveCamera\n\t\t\t\tif(d1viewPosition.z>-cameraNear){\n\t\t\t\t\t//https://tutorial.math.lamar.edu/Classes/CalcIII/EqnsOfLines.aspx\n\t\t\t\t\tfloat t=(-cameraNear-viewPosition.z)/viewReflectDir.z;\n\t\t\t\t\td1viewPosition=viewPosition+viewReflectDir*t;\n\t\t\t\t}\n\t\t\t#endif\n\t\t\td1=viewPositionToXY(d1viewPosition);\n\n\t\t\tfloat totalLen=length(d1-d0);\n\t\t\tfloat xLen=d1.x-d0.x;\n\t\t\tfloat yLen=d1.y-d0.y;\n\t\t\tfloat totalStep=max(abs(xLen),abs(yLen));\n\t\t\tfloat xSpan=xLen/totalStep;\n\t\t\tfloat ySpan=yLen/totalStep;\n\t\t\tfor(float i=0.;i<MAX_STEP;i++){\n\t\t\t\tif(i>=totalStep) break;\n\t\t\t\tvec2 xy=vec2(d0.x+i*xSpan,d0.y+i*ySpan);\n\t\t\t\tif(xy.x<0.||xy.x>resolution.x||xy.y<0.||xy.y>resolution.y) break;\n\t\t\t\tfloat s=length(xy-d0)/totalLen;\n\t\t\t\tvec2 uv=xy/resolution;\n\n\t\t\t\tfloat d = getDepth(uv);\n\t\t\t\tfloat vZ = getViewZ( d );\n\t\t\t\tif(-vZ>=cameraFar) continue;\n\t\t\t\tfloat cW = cameraProjectionMatrix[2][3] * vZ+cameraProjectionMatrix[3][3];\n\t\t\t\tvec3 vP=getViewPosition( uv, d, cW );\n\n\t\t\t\t#ifdef isPerspectiveCamera\n\t\t\t\t\t// https://www.comp.nus.edu.sg/~lowkl/publications/lowk_persp_interp_techrep.pdf\n\t\t\t\t\tfloat recipVPZ=1./viewPosition.z;\n\t\t\t\t\tfloat viewReflectRayZ=1./(recipVPZ+s*(1./d1viewPosition.z-recipVPZ));\n\t\t\t\t\tfloat sD=surfDist*cW;\n\t\t\t\t#else\n\t\t\t\t\tfloat viewReflectRayZ=viewPosition.z+s*(d1viewPosition.z-viewPosition.z);\n\t\t\t\t\tfloat sD=surfDist;\n\t\t\t\t#endif\n\t\t\t\tif(viewReflectRayZ-sD>vZ) continue;\n\n\t\t\t\t#ifdef isInfiniteThick\n\t\t\t\t\tif(viewReflectRayZ+thickTolerance*clipW<vP.z) break;\n\t\t\t\t#endif\n\t\t\t\tfloat away=pointToLineDistance(vP,viewPosition,d1viewPosition);\n\n\t\t\t\tfloat op=opacity;\n\n\t\t\t\tif(away<sD){\n\t\t\t\t\tvec3 vN=getViewNormal( uv );\n\t\t\t\t\tif(dot(viewReflectDir,vN)>=0.) continue;\n\t\t\t\t\tfloat distance=pointPlaneDistance(vP,viewPosition,viewNormal);\n\t\t\t\t\tif(distance>maxDistance) break;\n\t\t\t\t\t#ifdef isDistanceAttenuation\n\t\t\t\t\t\tfloat ratio=1.-(distance/maxDistance);\n\t\t\t\t\t\tfloat attenuation=ratio*ratio;\n\t\t\t\t\t\top=opacity*attenuation;\n\t\t\t\t\t#endif\n\t\t\t\t\t#ifdef isFresnel\n\t\t\t\t\t\tfloat fresnel=(dot(viewIncidenceDir,viewReflectDir)+1.)/2.;\n\t\t\t\t\t\top*=fresnel;\n\t\t\t\t\t#endif\n\t\t\t\t\tvec4 reflectColor=texture2D(tDiffuse,uv);\n\t\t\t\t\tgl_FragColor.xyz=reflectColor.xyz;\n\t\t\t\t\tgl_FragColor.a=op;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t`,\n}\n\nexport const SSRDepthShader = {\n  defines: {\n    PERSPECTIVE_CAMERA: 1,\n  },\n\n  uniforms: {\n    tDepth: { value: null },\n    cameraNear: { value: null },\n    cameraFar: { value: null },\n  },\n\n  vertexShader: /* glsl */ `\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n\n  `,\n\n  fragmentShader: /* glsl */ `\n\n    uniform sampler2D tDepth;\n\n    uniform float cameraNear;\n    uniform float cameraFar;\n\n    varying vec2 vUv;\n\n    #include <packing>\n\n\t\tfloat getLinearDepth( const in vec2 uv ) {\n\n\t\t\t#if PERSPECTIVE_CAMERA == 1\n\n\t\t\t\tfloat fragCoordZ = texture2D( tDepth, uv ).x;\n\t\t\t\tfloat viewZ = perspectiveDepthToViewZ( fragCoordZ, cameraNear, cameraFar );\n\t\t\t\treturn viewZToOrthographicDepth( viewZ, cameraNear, cameraFar );\n\n\t\t\t#else\n\n\t\t\t\treturn texture2D( tDepth, uv ).x;\n\n\t\t\t#endif\n\n\t\t}\n\n    void main() {\n\n    \tfloat depth = getLinearDepth( vUv );\n\t\t\tfloat d = 1.0 - depth;\n\t\t\t// d=(d-.999)*1000.;\n    \tgl_FragColor = vec4( vec3( d ), 1.0 );\n\n    }\n\n  `,\n}\n\nexport const SSRBlurShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    resolution: { value: /* @__PURE__ */ new Vector2() },\n    opacity: { value: 0.5 },\n  },\n\n  vertexShader: /* glsl */ `\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n\n  `,\n\n  fragmentShader: /* glsl */ `\n\n    uniform sampler2D tDiffuse;\n    uniform vec2 resolution;\n    varying vec2 vUv;\n    void main() {\n\t\t\t//reverse engineering from PhotoShop blur filter, then change coefficient\n\n    \tvec2 texelSize = ( 1.0 / resolution );\n\n\t\t\tvec4 c=texture2D(tDiffuse,vUv);\n\n\t\t\tvec2 offset;\n\n\t\t\toffset=(vec2(-1,0))*texelSize;\n\t\t\tvec4 cl=texture2D(tDiffuse,vUv+offset);\n\n\t\t\toffset=(vec2(1,0))*texelSize;\n\t\t\tvec4 cr=texture2D(tDiffuse,vUv+offset);\n\n\t\t\toffset=(vec2(0,-1))*texelSize;\n\t\t\tvec4 cb=texture2D(tDiffuse,vUv+offset);\n\n\t\t\toffset=(vec2(0,1))*texelSize;\n\t\t\tvec4 ct=texture2D(tDiffuse,vUv+offset);\n\n\t\t\t// float coeCenter=.5;\n\t\t\t// float coeSide=.125;\n\t\t\tfloat coeCenter=.2;\n\t\t\tfloat coeSide=.2;\n\t\t\tfloat a=c.a*coeCenter+cl.a*coeSide+cr.a*coeSide+cb.a*coeSide+ct.a*coeSide;\n\t\t\tvec3 rgb=(c.rgb*c.a*coeCenter+cl.rgb*cl.a*coeSide+cr.rgb*cr.a*coeSide+cb.rgb*cb.a*coeSide+ct.rgb*ct.a*coeSide)/a;\n\t\t\tgl_FragColor=vec4(rgb,a);\n\n\t\t}\n\t`,\n}\n"], "names": ["Vector2", "Matrix4"], "mappings": ";;;AAMO,MAAM,YAAY;AAAA,EACvB,SAAS;AAAA,IACP,UAAU;AAAA,IACV,qBAAqB;AAAA,IACrB,uBAAuB;AAAA,IACvB,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,aAAa;AAAA,EACf;AAAA,EAEA,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,SAAS,EAAE,OAAO,KAAK;AAAA,IACvB,YAAY,EAAE,OAAO,KAAK;AAAA,IAC1B,QAAQ,EAAE,OAAO,KAAK;AAAA,IACtB,YAAY,EAAE,OAAO,KAAK;AAAA,IAC1B,WAAW,EAAE,OAAO,KAAK;AAAA,IACzB,YAAY,EAAE,OAAuB,oBAAIA,MAAAA,UAAU;AAAA,IACnD,wBAAwB,EAAE,OAAuB,oBAAIC,MAAAA,UAAU;AAAA,IAC/D,+BAA+B,EAAE,OAAuB,oBAAIA,MAAAA,UAAU;AAAA,IACtE,SAAS,EAAE,OAAO,IAAI;AAAA,IACtB,aAAa,EAAE,OAAO,IAAI;AAAA,IAC1B,aAAa,EAAE,OAAO,EAAE;AAAA,IACxB,UAAU,EAAE,OAAO,KAAM;AAAA,IACzB,gBAAgB,EAAE,OAAO,KAAK;AAAA,EAChC;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAczB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqK7B;AAEO,MAAM,iBAAiB;AAAA,EAC5B,SAAS;AAAA,IACP,oBAAoB;AAAA,EACtB;AAAA,EAEA,UAAU;AAAA,IACR,QAAQ,EAAE,OAAO,KAAK;AAAA,IACtB,YAAY,EAAE,OAAO,KAAK;AAAA,IAC1B,WAAW,EAAE,OAAO,KAAK;AAAA,EAC3B;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAazB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqC7B;AAEO,MAAM,gBAAgB;AAAA,EAC3B,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,YAAY,EAAE,OAAuB,oBAAID,MAAAA,UAAU;AAAA,IACnD,SAAS,EAAE,OAAO,IAAI;AAAA,EACxB;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAazB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoC7B;;;;"}