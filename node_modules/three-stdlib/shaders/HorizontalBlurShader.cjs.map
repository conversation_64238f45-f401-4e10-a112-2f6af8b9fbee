{"version": 3, "file": "HorizontalBlurShader.cjs", "sources": ["../../src/shaders/HorizontalBlurShader.ts"], "sourcesContent": ["/**\n * Two pass Gaussian blur filter (horizontal and vertical blur shaders)\n * - described in http://www.gamerendering.com/2008/10/11/gaussian-blur-filter-shader/\n *   and used in http://www.cake23.de/traveling-wavefronts-lit-up.html\n *\n * - 9 samples per pass\n * - standard deviation 2.7\n * - \"h\" and \"v\" parameters should be set to \"1 / width\" and \"1 / height\"\n */\n\nimport type { IUniform, Texture } from 'three'\nimport type { IShader } from './types'\n\nexport type HorizontalBlurShaderUniforms = {\n  tDiffuse: IUniform<Texture | null>\n  h: IUniform<number>\n}\n\nexport interface IHorizontalBlurShader extends IShader<HorizontalBlurShaderUniforms> {}\n\nexport const HorizontalBlurShader: IHorizontalBlurShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    h: { value: 1.0 / 512.0 },\n  },\n  vertexShader: /* glsl */ `\n      varying vec2 vUv;\n\n      void main() {\n\n        vUv = uv;\n        gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n      }\n  `,\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDiffuse;\n    uniform float h;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 sum = vec4( 0.0 );\n\n    \tsum += texture2D( tDiffuse, vec2( vUv.x - 4.0 * h, vUv.y ) ) * 0.051;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x - 3.0 * h, vUv.y ) ) * 0.0918;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x - 2.0 * h, vUv.y ) ) * 0.12245;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x - 1.0 * h, vUv.y ) ) * 0.1531;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x, vUv.y ) ) * 0.1633;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x + 1.0 * h, vUv.y ) ) * 0.1531;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x + 2.0 * h, vUv.y ) ) * 0.12245;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x + 3.0 * h, vUv.y ) ) * 0.0918;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x + 4.0 * h, vUv.y ) ) * 0.051;\n\n    \tgl_FragColor = sum;\n\n    }\n  `,\n}\n"], "names": [], "mappings": ";;AAoBO,MAAM,uBAA8C;AAAA,EACzD,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,GAAG,EAAE,OAAO,IAAM,IAAM;AAAA,EAC1B;AAAA,EACA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAwB7B;;"}