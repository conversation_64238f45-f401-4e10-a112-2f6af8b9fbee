{"version": 3, "file": "HueSaturationShader.cjs", "sources": ["../../src/shaders/HueSaturationShader.ts"], "sourcesContent": ["/**\n * Hue and saturation adjustment\n * https://github.com/evanw/glfx.js\n * hue: -1 to 1 (-1 is 180 degrees in the negative direction, 0 is no change, etc.\n * saturation: -1 to 1 (-1 is solid gray, 0 is no change, and 1 is maximum contrast)\n */\n\nexport const HueSaturationShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    hue: { value: 0 },\n    saturation: { value: 0 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDiffuse;\n    uniform float hue;\n    uniform float saturation;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tgl_FragColor = texture2D( tDiffuse, vUv );\n\n    // hue\n    \tfloat angle = hue * 3.14159265;\n    \tfloat s = sin(angle), c = cos(angle);\n    \tvec3 weights = (vec3(2.0 * c, -sqrt(3.0) * s - c, sqrt(3.0) * s - c) + 1.0) / 3.0;\n    \tfloat len = length(gl_FragColor.rgb);\n    \tgl_FragColor.rgb = vec3(\n    \t\tdot(gl_FragColor.rgb, weights.xyz),\n    \t\tdot(gl_FragColor.rgb, weights.zxy),\n    \t\tdot(gl_FragColor.rgb, weights.yzx)\n    \t);\n\n    // saturation\n    \tfloat average = (gl_FragColor.r + gl_FragColor.g + gl_FragColor.b) / 3.0;\n    \tif (saturation > 0.0) {\n    \t\tgl_FragColor.rgb += (average - gl_FragColor.rgb) * (1.0 - 1.0 / (1.001 - saturation));\n    \t} else {\n    \t\tgl_FragColor.rgb += (average - gl_FragColor.rgb) * (-saturation);\n    \t}\n\n    }\n  `,\n}\n"], "names": [], "mappings": ";;AAOO,MAAM,sBAAsB;AAAA,EACjC,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,KAAK,EAAE,OAAO,EAAE;AAAA,IAChB,YAAY,EAAE,OAAO,EAAE;AAAA,EACzB;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgC7B;;"}