{"version": 3, "file": "BleachBypassShader.js", "sources": ["../../src/shaders/BleachBypassShader.ts"], "sourcesContent": ["/**\n * Bleach bypass shader [http://en.wikipedia.org/wiki/Bleach_bypass]\n * - based on Nvidia example\n * http://developer.download.nvidia.com/shaderlibrary/webpages/shader_library.html#post_bleach_bypass\n */\n\nimport type { IUniform, Texture } from 'three'\nimport type { IShader } from './types'\n\nexport type BleachBypassShaderUniforms = {\n  opacity: IUniform<number>\n  tDiffuse: IUniform<Texture | null>\n}\n\nexport interface IBleachBypassShader extends IShader<BleachBypassShaderUniforms> {}\n\nexport const BleachBypassShader: IBleachBypassShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    opacity: { value: 1.0 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform float opacity;\n\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 base = texture2D( tDiffuse, vUv );\n\n    \tvec3 lumCoeff = vec3( 0.25, 0.65, 0.1 );\n    \tfloat lum = dot( lumCoeff, base.rgb );\n    \tvec3 blend = vec3( lum );\n\n    \tfloat L = min( 1.0, max( 0.0, 10.0 * ( lum - 0.45 ) ) );\n\n    \tvec3 result1 = 2.0 * base.rgb * blend;\n    \tvec3 result2 = 1.0 - 2.0 * ( 1.0 - blend ) * ( 1.0 - base.rgb );\n\n    \tvec3 newColor = mix( result1, result2, L );\n\n    \tfloat A2 = opacity * base.a;\n    \tvec3 mixRGB = A2 * newColor.rgb;\n    \tmixRGB += ( ( 1.0 - A2 ) * base.rgb );\n\n    \tgl_FragColor = vec4( mixRGB, base.a );\n\n    }\n  `,\n}\n"], "names": [], "mappings": "AAgBO,MAAM,qBAA0C;AAAA,EACrD,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,SAAS,EAAE,OAAO,EAAI;AAAA,EACxB;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA8B7B;"}