{"version": 3, "file": "FocusShader.js", "sources": ["../../src/shaders/FocusShader.ts"], "sourcesContent": ["/**\n * Focus shader\n * based on PaintEffect postprocess from ro.me\n * http://code.google.com/p/3-dreams-of-black/source/browse/deploy/js/effects/PaintEffect.js\n */\n\nexport const FocusShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    screenWidth: { value: 1024 },\n    screenHeight: { value: 1024 },\n    sampleDistance: { value: 0.94 },\n    waveFactor: { value: 0.00125 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform float screenWidth;\n    uniform float screenHeight;\n    uniform float sampleDistance;\n    uniform float waveFactor;\n\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 color, org, tmp, add;\n    \tfloat sample_dist, f;\n    \tvec2 vin;\n    \tvec2 uv = vUv;\n\n    \tadd = color = org = texture2D( tDiffuse, uv );\n\n    \tvin = ( uv - vec2( 0.5 ) ) * vec2( 1.4 );\n    \tsample_dist = dot( vin, vin ) * 2.0;\n\n    \tf = ( waveFactor * 100.0 + sample_dist ) * sampleDistance * 4.0;\n\n    \tvec2 sampleSize = vec2(  1.0 / screenWidth, 1.0 / screenHeight ) * vec2( f );\n\n    \tadd += tmp = texture2D( tDiffuse, uv + vec2( 0.111964, 0.993712 ) * sampleSize );\n    \tif( tmp.b < color.b ) color = tmp;\n\n    \tadd += tmp = texture2D( tDiffuse, uv + vec2( 0.846724, 0.532032 ) * sampleSize );\n    \tif( tmp.b < color.b ) color = tmp;\n\n    \tadd += tmp = texture2D( tDiffuse, uv + vec2( 0.943883, -0.330279 ) * sampleSize );\n    \tif( tmp.b < color.b ) color = tmp;\n\n    \tadd += tmp = texture2D( tDiffuse, uv + vec2( 0.330279, -0.943883 ) * sampleSize );\n    \tif( tmp.b < color.b ) color = tmp;\n\n    \tadd += tmp = texture2D( tDiffuse, uv + vec2( -0.532032, -0.846724 ) * sampleSize );\n    \tif( tmp.b < color.b ) color = tmp;\n\n    \tadd += tmp = texture2D( tDiffuse, uv + vec2( -0.993712, -0.111964 ) * sampleSize );\n    \tif( tmp.b < color.b ) color = tmp;\n\n    \tadd += tmp = texture2D( tDiffuse, uv + vec2( -0.707107, 0.707107 ) * sampleSize );\n    \tif( tmp.b < color.b ) color = tmp;\n\n    \tcolor = color * vec4( 2.0 ) - ( add / vec4( 8.0 ) );\n    \tcolor = color + ( add / vec4( 8.0 ) - color ) * ( vec4( 1.0 ) - vec4( sample_dist * 0.5 ) );\n\n    \tgl_FragColor = vec4( color.rgb * color.rgb * vec3( 0.95 ) + color.rgb, 1.0 );\n\n    }\n  `,\n}\n"], "names": [], "mappings": "AAMO,MAAM,cAAc;AAAA,EACzB,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,aAAa,EAAE,OAAO,KAAK;AAAA,IAC3B,cAAc,EAAE,OAAO,KAAK;AAAA,IAC5B,gBAAgB,EAAE,OAAO,KAAK;AAAA,IAC9B,YAAY,EAAE,OAAO,OAAQ;AAAA,EAC/B;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsD7B;"}