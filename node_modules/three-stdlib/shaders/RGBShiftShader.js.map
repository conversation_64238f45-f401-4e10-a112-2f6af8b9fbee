{"version": 3, "file": "RGBShiftShader.js", "sources": ["../../src/shaders/RGBShiftShader.ts"], "sourcesContent": ["/**\n * RGB Shift Shader\n * Shifts red and blue channels from center in opposite directions\n * Ported from http://kriss.cx/tom/2009/05/rgb-shift/\n * by <PERSON> / http://kriss.cx/tom/\n *\n * amount: shift distance (1 is width of input)\n * angle: shift angle in radians\n */\n\nexport const RGBShiftShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    amount: { value: 0.005 },\n    angle: { value: 0.0 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDiffuse;\n    uniform float amount;\n    uniform float angle;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec2 offset = amount * vec2( cos(angle), sin(angle));\n    \tvec4 cr = texture2D(tDiffuse, vUv + offset);\n    \tvec4 cga = texture2D(tDiffuse, vUv);\n    \tvec4 cb = texture2D(tDiffuse, vUv - offset);\n    \tgl_FragColor = vec4(cr.r, cga.g, cb.b, cga.a);\n\n    }\n  `,\n}\n"], "names": [], "mappings": "AAUO,MAAM,iBAAiB;AAAA,EAC5B,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,QAAQ,EAAE,OAAO,KAAM;AAAA,IACvB,OAAO,EAAE,OAAO,EAAI;AAAA,EACtB;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiB7B;"}