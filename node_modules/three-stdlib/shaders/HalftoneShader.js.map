{"version": 3, "file": "HalftoneShader.js", "sources": ["../../src/shaders/HalftoneShader.ts"], "sourcesContent": ["/**\n * RGB Halftone shader for three.js.\n *\tNOTE:\n * \t\tShape (1 = Dot, 2 = Ellipse, 3 = Line, 4 = Square)\n *\t\tBlending Mode (1 = Linear, 2 = Multiply, 3 = Add, 4 = Lighter, 5 = Darker)\n */\n\nexport const HalftoneShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    shape: { value: 1 },\n    radius: { value: 4 },\n    rotateR: { value: (Math.PI / 12) * 1 },\n    rotateG: { value: (Math.PI / 12) * 2 },\n    rotateB: { value: (Math.PI / 12) * 3 },\n    scatter: { value: 0 },\n    width: { value: 1 },\n    height: { value: 1 },\n    blending: { value: 1 },\n    blendingMode: { value: 1 },\n    greyscale: { value: false },\n    disable: { value: false },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUV;\n\n    void main() {\n\n    \tvUV = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    #define SQRT2_MINUS_ONE 0.41421356\n    #define SQRT2_HALF_MINUS_ONE 0.20710678\n    #define PI2 6.28318531\n    #define SHAPE_DOT 1\n    #define SHAPE_ELLIPSE 2\n    #define SHAPE_LINE 3\n    #define SHAPE_SQUARE 4\n    #define BLENDING_LINEAR 1\n    #define BLENDING_MULTIPLY 2\n    #define BLENDING_ADD 3\n    #define BLENDING_LIGHTER 4\n    #define BLENDING_DARKER 5\n    uniform sampler2D tDiffuse;\n    uniform float radius;\n    uniform float rotateR;\n    uniform float rotateG;\n    uniform float rotateB;\n    uniform float scatter;\n    uniform float width;\n    uniform float height;\n    uniform int shape;\n    uniform bool disable;\n    uniform float blending;\n    uniform int blendingMode;\n    varying vec2 vUV;\n    uniform bool greyscale;\n    const int samples = 8;\n\n    float blend( float a, float b, float t ) {\n\n    // linear blend\n    \treturn a * ( 1.0 - t ) + b * t;\n\n    }\n\n    float hypot( float x, float y ) {\n\n    // vector magnitude\n    \treturn sqrt( x * x + y * y );\n\n    }\n\n    float rand( vec2 seed ){\n\n    // get pseudo-random number\n    return fract( sin( dot( seed.xy, vec2( 12.9898, 78.233 ) ) ) * 43758.5453 );\n\n    }\n\n    float distanceToDotRadius( float channel, vec2 coord, vec2 normal, vec2 p, float angle, float rad_max ) {\n\n    // apply shape-specific transforms\n    \tfloat dist = hypot( coord.x - p.x, coord.y - p.y );\n    \tfloat rad = channel;\n\n    \tif ( shape == SHAPE_DOT ) {\n\n    \t\trad = pow( abs( rad ), 1.125 ) * rad_max;\n\n    \t} else if ( shape == SHAPE_ELLIPSE ) {\n\n    \t\trad = pow( abs( rad ), 1.125 ) * rad_max;\n\n    \t\tif ( dist != 0.0 ) {\n    \t\t\tfloat dot_p = abs( ( p.x - coord.x ) / dist * normal.x + ( p.y - coord.y ) / dist * normal.y );\n    \t\t\tdist = ( dist * ( 1.0 - SQRT2_HALF_MINUS_ONE ) ) + dot_p * dist * SQRT2_MINUS_ONE;\n    \t\t}\n\n    \t} else if ( shape == SHAPE_LINE ) {\n\n    \t\trad = pow( abs( rad ), 1.5) * rad_max;\n    \t\tfloat dot_p = ( p.x - coord.x ) * normal.x + ( p.y - coord.y ) * normal.y;\n    \t\tdist = hypot( normal.x * dot_p, normal.y * dot_p );\n\n    \t} else if ( shape == SHAPE_SQUARE ) {\n\n    \t\tfloat theta = atan( p.y - coord.y, p.x - coord.x ) - angle;\n    \t\tfloat sin_t = abs( sin( theta ) );\n    \t\tfloat cos_t = abs( cos( theta ) );\n    \t\trad = pow( abs( rad ), 1.4 );\n    \t\trad = rad_max * ( rad + ( ( sin_t > cos_t ) ? rad - sin_t * rad : rad - cos_t * rad ) );\n\n    \t}\n\n    \treturn rad - dist;\n\n    }\n\n    struct Cell {\n\n    // grid sample positions\n    \tvec2 normal;\n    \tvec2 p1;\n    \tvec2 p2;\n    \tvec2 p3;\n    \tvec2 p4;\n    \tfloat samp2;\n    \tfloat samp1;\n    \tfloat samp3;\n    \tfloat samp4;\n\n    };\n\n    vec4 getSample( vec2 point ) {\n\n    // multi-sampled point\n    \tvec4 tex = texture2D( tDiffuse, vec2( point.x / width, point.y / height ) );\n    \tfloat base = rand( vec2( floor( point.x ), floor( point.y ) ) ) * PI2;\n    \tfloat step = PI2 / float( samples );\n    \tfloat dist = radius * 0.66;\n\n    \tfor ( int i = 0; i < samples; ++i ) {\n\n    \t\tfloat r = base + step * float( i );\n    \t\tvec2 coord = point + vec2( cos( r ) * dist, sin( r ) * dist );\n    \t\ttex += texture2D( tDiffuse, vec2( coord.x / width, coord.y / height ) );\n\n    \t}\n\n    \ttex /= float( samples ) + 1.0;\n    \treturn tex;\n\n    }\n\n    float getDotColour( Cell c, vec2 p, int channel, float angle, float aa ) {\n\n    // get colour for given point\n    \tfloat dist_c_1, dist_c_2, dist_c_3, dist_c_4, res;\n\n    \tif ( channel == 0 ) {\n\n    \t\tc.samp1 = getSample( c.p1 ).r;\n    \t\tc.samp2 = getSample( c.p2 ).r;\n    \t\tc.samp3 = getSample( c.p3 ).r;\n    \t\tc.samp4 = getSample( c.p4 ).r;\n\n    \t} else if (channel == 1) {\n\n    \t\tc.samp1 = getSample( c.p1 ).g;\n    \t\tc.samp2 = getSample( c.p2 ).g;\n    \t\tc.samp3 = getSample( c.p3 ).g;\n    \t\tc.samp4 = getSample( c.p4 ).g;\n\n    \t} else {\n\n    \t\tc.samp1 = getSample( c.p1 ).b;\n    \t\tc.samp3 = getSample( c.p3 ).b;\n    \t\tc.samp2 = getSample( c.p2 ).b;\n    \t\tc.samp4 = getSample( c.p4 ).b;\n\n    \t}\n\n    \tdist_c_1 = distanceToDotRadius( c.samp1, c.p1, c.normal, p, angle, radius );\n    \tdist_c_2 = distanceToDotRadius( c.samp2, c.p2, c.normal, p, angle, radius );\n    \tdist_c_3 = distanceToDotRadius( c.samp3, c.p3, c.normal, p, angle, radius );\n    \tdist_c_4 = distanceToDotRadius( c.samp4, c.p4, c.normal, p, angle, radius );\n    \tres = ( dist_c_1 > 0.0 ) ? clamp( dist_c_1 / aa, 0.0, 1.0 ) : 0.0;\n    \tres += ( dist_c_2 > 0.0 ) ? clamp( dist_c_2 / aa, 0.0, 1.0 ) : 0.0;\n    \tres += ( dist_c_3 > 0.0 ) ? clamp( dist_c_3 / aa, 0.0, 1.0 ) : 0.0;\n    \tres += ( dist_c_4 > 0.0 ) ? clamp( dist_c_4 / aa, 0.0, 1.0 ) : 0.0;\n    \tres = clamp( res, 0.0, 1.0 );\n\n    \treturn res;\n\n    }\n\n    Cell getReferenceCell( vec2 p, vec2 origin, float grid_angle, float step ) {\n\n    // get containing cell\n    \tCell c;\n\n    // calc grid\n    \tvec2 n = vec2( cos( grid_angle ), sin( grid_angle ) );\n    \tfloat threshold = step * 0.5;\n    \tfloat dot_normal = n.x * ( p.x - origin.x ) + n.y * ( p.y - origin.y );\n    \tfloat dot_line = -n.y * ( p.x - origin.x ) + n.x * ( p.y - origin.y );\n    \tvec2 offset = vec2( n.x * dot_normal, n.y * dot_normal );\n    \tfloat offset_normal = mod( hypot( offset.x, offset.y ), step );\n    \tfloat normal_dir = ( dot_normal < 0.0 ) ? 1.0 : -1.0;\n    \tfloat normal_scale = ( ( offset_normal < threshold ) ? -offset_normal : step - offset_normal ) * normal_dir;\n    \tfloat offset_line = mod( hypot( ( p.x - offset.x ) - origin.x, ( p.y - offset.y ) - origin.y ), step );\n    \tfloat line_dir = ( dot_line < 0.0 ) ? 1.0 : -1.0;\n    \tfloat line_scale = ( ( offset_line < threshold ) ? -offset_line : step - offset_line ) * line_dir;\n\n    // get closest corner\n    \tc.normal = n;\n    \tc.p1.x = p.x - n.x * normal_scale + n.y * line_scale;\n    \tc.p1.y = p.y - n.y * normal_scale - n.x * line_scale;\n\n    // scatter\n    \tif ( scatter != 0.0 ) {\n\n    \t\tfloat off_mag = scatter * threshold * 0.5;\n    \t\tfloat off_angle = rand( vec2( floor( c.p1.x ), floor( c.p1.y ) ) ) * PI2;\n    \t\tc.p1.x += cos( off_angle ) * off_mag;\n    \t\tc.p1.y += sin( off_angle ) * off_mag;\n\n    \t}\n\n    // find corners\n    \tfloat normal_step = normal_dir * ( ( offset_normal < threshold ) ? step : -step );\n    \tfloat line_step = line_dir * ( ( offset_line < threshold ) ? step : -step );\n    \tc.p2.x = c.p1.x - n.x * normal_step;\n    \tc.p2.y = c.p1.y - n.y * normal_step;\n    \tc.p3.x = c.p1.x + n.y * line_step;\n    \tc.p3.y = c.p1.y - n.x * line_step;\n    \tc.p4.x = c.p1.x - n.x * normal_step + n.y * line_step;\n    \tc.p4.y = c.p1.y - n.y * normal_step - n.x * line_step;\n\n    \treturn c;\n\n    }\n\n    float blendColour( float a, float b, float t ) {\n\n    // blend colours\n    \tif ( blendingMode == BLENDING_LINEAR ) {\n    \t\treturn blend( a, b, 1.0 - t );\n    \t} else if ( blendingMode == BLENDING_ADD ) {\n    \t\treturn blend( a, min( 1.0, a + b ), t );\n    \t} else if ( blendingMode == BLENDING_MULTIPLY ) {\n    \t\treturn blend( a, max( 0.0, a * b ), t );\n    \t} else if ( blendingMode == BLENDING_LIGHTER ) {\n    \t\treturn blend( a, max( a, b ), t );\n    \t} else if ( blendingMode == BLENDING_DARKER ) {\n    \t\treturn blend( a, min( a, b ), t );\n    \t} else {\n    \t\treturn blend( a, b, 1.0 - t );\n    \t}\n\n    }\n\n    void main() {\n\n    \tif ( ! disable ) {\n\n    // setup\n    \t\tvec2 p = vec2( vUV.x * width, vUV.y * height );\n    \t\tvec2 origin = vec2( 0, 0 );\n    \t\tfloat aa = ( radius < 2.5 ) ? radius * 0.5 : 1.25;\n\n    // get channel samples\n    \t\tCell cell_r = getReferenceCell( p, origin, rotateR, radius );\n    \t\tCell cell_g = getReferenceCell( p, origin, rotateG, radius );\n    \t\tCell cell_b = getReferenceCell( p, origin, rotateB, radius );\n    \t\tfloat r = getDotColour( cell_r, p, 0, rotateR, aa );\n    \t\tfloat g = getDotColour( cell_g, p, 1, rotateG, aa );\n    \t\tfloat b = getDotColour( cell_b, p, 2, rotateB, aa );\n\n    // blend with original\n    \t\tvec4 colour = texture2D( tDiffuse, vUV );\n    \t\tr = blendColour( r, colour.r, blending );\n    \t\tg = blendColour( g, colour.g, blending );\n    \t\tb = blendColour( b, colour.b, blending );\n\n    \t\tif ( greyscale ) {\n    \t\t\tr = g = b = (r + b + g) / 3.0;\n    \t\t}\n\n    \t\tgl_FragColor = vec4( r, g, b, 1.0 );\n\n    \t} else {\n\n    \t\tgl_FragColor = texture2D( tDiffuse, vUV );\n\n    \t}\n\n    }\n  `,\n}\n"], "names": [], "mappings": "AAOO,MAAM,iBAAiB;AAAA,EAC5B,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,OAAO,EAAE,OAAO,EAAE;AAAA,IAClB,QAAQ,EAAE,OAAO,EAAE;AAAA,IACnB,SAAS,EAAE,OAAQ,KAAK,KAAK,KAAM,EAAE;AAAA,IACrC,SAAS,EAAE,OAAQ,KAAK,KAAK,KAAM,EAAE;AAAA,IACrC,SAAS,EAAE,OAAQ,KAAK,KAAK,KAAM,EAAE;AAAA,IACrC,SAAS,EAAE,OAAO,EAAE;AAAA,IACpB,OAAO,EAAE,OAAO,EAAE;AAAA,IAClB,QAAQ,EAAE,OAAO,EAAE;AAAA,IACnB,UAAU,EAAE,OAAO,EAAE;AAAA,IACrB,cAAc,EAAE,OAAO,EAAE;AAAA,IACzB,WAAW,EAAE,OAAO,MAAM;AAAA,IAC1B,SAAS,EAAE,OAAO,MAAM;AAAA,EAC1B;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA8Q7B;"}