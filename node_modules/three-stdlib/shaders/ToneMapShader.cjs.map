{"version": 3, "file": "ToneMapShader.cjs", "sources": ["../../src/shaders/ToneMapShader.ts"], "sourcesContent": ["/**\n * Full-screen tone-mapping shader based on http://www.cis.rit.edu/people/faculty/ferwerda/publications/sig02_paper.pdf\n */\n\nexport const ToneMapShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    averageLuminance: { value: 1.0 },\n    luminanceMap: { value: null },\n    maxLuminance: { value: 16.0 },\n    minLuminance: { value: 0.01 },\n    middleGrey: { value: 0.6 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    #include <common>\n\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    uniform float middleGrey;\n    uniform float minLuminance;\n    uniform float maxLuminance;\n    #ifdef ADAPTED_LUMINANCE\n    \tuniform sampler2D luminanceMap;\n    #else\n    \tuniform float averageLuminance;\n    #endif\n\n    vec3 ToneMap( vec3 vColor ) {\n    \t#ifdef ADAPTED_LUMINANCE\n    // Get the calculated average luminance\n    \t\tfloat fLumAvg = texture2D(luminanceMap, vec2(0.5, 0.5)).r;\n    \t#else\n    \t\tfloat fLumAvg = averageLuminance;\n    \t#endif\n\n    // Calculate the luminance of the current pixel\n    \tfloat fLumPixel = linearToRelativeLuminance( vColor );\n\n    // Apply the modified operator (Eq. 4)\n    \tfloat fLumScaled = (fLumPixel * middleGrey) / max( minLuminance, fLumAvg );\n\n    \tfloat fLumCompressed = (fLumScaled * (1.0 + (fLumScaled / (maxLuminance * maxLuminance)))) / (1.0 + fLumScaled);\n    \treturn fLumCompressed * vColor;\n    }\n\n    void main() {\n\n    \tvec4 texel = texture2D( tDiffuse, vUv );\n\n    \tgl_FragColor = vec4( ToneMap( texel.xyz ), texel.w );\n\n    }\n  `,\n}\n"], "names": [], "mappings": ";;AAIO,MAAM,gBAAgB;AAAA,EAC3B,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,kBAAkB,EAAE,OAAO,EAAI;AAAA,IAC/B,cAAc,EAAE,OAAO,KAAK;AAAA,IAC5B,cAAc,EAAE,OAAO,GAAK;AAAA,IAC5B,cAAc,EAAE,OAAO,KAAK;AAAA,IAC5B,YAAY,EAAE,OAAO,IAAI;AAAA,EAC3B;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA0C7B;;"}