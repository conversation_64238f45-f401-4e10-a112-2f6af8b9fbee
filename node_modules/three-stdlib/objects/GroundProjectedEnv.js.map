{"version": 3, "file": "GroundProjectedEnv.js", "sources": ["../../src/objects/GroundProjectedEnv.ts"], "sourcesContent": ["import { Mesh, IcosahedronGeometry, ShaderMaterial, DoubleSide, Texture, CubeTexture, BufferGeometry } from 'three'\nimport { version } from '../_polyfill/constants'\n\nexport interface GroundProjectedEnvParameters {\n  height?: number\n  radius?: number\n}\n\nconst isCubeTexture = (def: CubeTexture | Texture): def is CubeTexture => def && (def as CubeTexture).isCubeTexture\n\nexport class GroundProjectedEnv extends Mesh<BufferGeometry, ShaderMaterial> {\n  constructor(texture: CubeTexture | Texture, options?: GroundProjectedEnvParameters) {\n    const isCubeMap = isCubeTexture(texture)\n    const w = (isCubeMap ? texture.image[0]?.width : texture.image.width) ?? 1024\n    const cubeSize = w / 4\n    const _lodMax = Math.floor(Math.log2(cubeSize))\n    const _cubeSize = Math.pow(2, _lodMax)\n    const width = 3 * Math.max(_cubeSize, 16 * 7)\n    const height = 4 * _cubeSize\n\n    const defines = [\n      isCubeMap ? '#define ENVMAP_TYPE_CUBE' : '',\n      `#define CUBEUV_TEXEL_WIDTH ${1.0 / width}`,\n      `#define CUBEUV_TEXEL_HEIGHT ${1.0 / height}`,\n      `#define CUBEUV_MAX_MIP ${_lodMax}.0`,\n    ]\n\n    const vertexShader = /* glsl */ `\n        varying vec3 vWorldPosition;\n        void main() \n        {\n            vec4 worldPosition = ( modelMatrix * vec4( position, 1.0 ) );\n            vWorldPosition = worldPosition.xyz;\n            \n            gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        }\n        `\n    const fragmentShader =\n      defines.join('\\n') +\n      /* glsl */ `\n        #define ENVMAP_TYPE_CUBE_UV\n        varying vec3 vWorldPosition;\n        uniform float radius;\n        uniform float height;\n        uniform float angle;\n        #ifdef ENVMAP_TYPE_CUBE\n            uniform samplerCube map;\n        #else\n            uniform sampler2D map;\n        #endif\n        // From: https://www.shadertoy.com/view/4tsBD7\n        float diskIntersectWithBackFaceCulling( vec3 ro, vec3 rd, vec3 c, vec3 n, float r ) \n        {\n            float d = dot ( rd, n );\n            \n            if( d > 0.0 ) { return 1e6; }\n            \n            vec3  o = ro - c;\n            float t = - dot( n, o ) / d;\n            vec3  q = o + rd * t;\n            \n            return ( dot( q, q ) < r * r ) ? t : 1e6;\n        }\n        // From: https://www.iquilezles.org/www/articles/intersectors/intersectors.htm\n        float sphereIntersect( vec3 ro, vec3 rd, vec3 ce, float ra ) \n        {\n            vec3 oc = ro - ce;\n            float b = dot( oc, rd );\n            float c = dot( oc, oc ) - ra * ra;\n            float h = b * b - c;\n            \n            if( h < 0.0 ) { return -1.0; }\n            \n            h = sqrt( h );\n            \n            return - b + h;\n        }\n        vec3 project() \n        {\n            vec3 p = normalize( vWorldPosition );\n            vec3 camPos = cameraPosition;\n            camPos.y -= height;\n            float intersection = sphereIntersect( camPos, p, vec3( 0.0 ), radius );\n            if( intersection > 0.0 ) {\n                \n                vec3 h = vec3( 0.0, - height, 0.0 );\n                float intersection2 = diskIntersectWithBackFaceCulling( camPos, p, h, vec3( 0.0, 1.0, 0.0 ), radius );\n                p = ( camPos + min( intersection, intersection2 ) * p ) / radius;\n            } else {\n                p = vec3( 0.0, 1.0, 0.0 );\n            }\n            return p;\n        }\n        #include <common>\n        #include <cube_uv_reflection_fragment>\n        void main() \n        {\n            vec3 projectedWorldPosition = project();\n            \n            #ifdef ENVMAP_TYPE_CUBE\n                vec3 outcolor = textureCube( map, projectedWorldPosition ).rgb;\n            #else\n                vec3 direction = normalize( projectedWorldPosition );\n                vec2 uv = equirectUv( direction );\n                vec3 outcolor = texture2D( map, uv ).rgb;\n            #endif\n            gl_FragColor = vec4( outcolor, 1.0 );\n            #include <tonemapping_fragment>\n            #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n        }\n        `\n\n    const uniforms = {\n      map: { value: texture },\n      height: { value: options?.height || 15 },\n      radius: { value: options?.radius || 100 },\n    }\n\n    const geometry = new IcosahedronGeometry(1, 16)\n    const material = new ShaderMaterial({\n      uniforms,\n      fragmentShader,\n      vertexShader,\n      side: DoubleSide,\n    })\n\n    super(geometry, material)\n  }\n\n  set radius(radius: number) {\n    this.material.uniforms.radius.value = radius\n  }\n\n  get radius(): number {\n    return this.material.uniforms.radius.value\n  }\n\n  set height(height: number) {\n    this.material.uniforms.height.value = height\n  }\n\n  get height(): number {\n    return this.material.uniforms.height.value\n  }\n}\n"], "names": [], "mappings": ";;AAQA,MAAM,gBAAgB,CAAC,QAAmD,OAAQ,IAAoB;AAE/F,MAAM,2BAA2B,KAAqC;AAAA,EAC3E,YAAY,SAAgC,SAAwC;;AAC5E,UAAA,YAAY,cAAc,OAAO;AACjC,UAAA,KAAK,kBAAY,aAAQ,MAAM,CAAC,MAAf,mBAAkB,QAAQ,QAAQ,MAAM,UAApD,YAA8D;AACzE,UAAM,WAAW,IAAI;AACrB,UAAM,UAAU,KAAK,MAAM,KAAK,KAAK,QAAQ,CAAC;AAC9C,UAAM,YAAY,KAAK,IAAI,GAAG,OAAO;AACrC,UAAM,QAAQ,IAAI,KAAK,IAAI,WAAW,KAAK,CAAC;AAC5C,UAAM,SAAS,IAAI;AAEnB,UAAM,UAAU;AAAA,MACd,YAAY,6BAA6B;AAAA,MACzC,8BAA8B,IAAM;AAAA,MACpC,+BAA+B,IAAM;AAAA,MACrC,0BAA0B;AAAA,IAAA;AAGtB,UAAA;AAAA;AAAA,MAA0B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAU1B,UAAA,iBACJ,QAAQ,KAAK,IAAI;AAAA,IACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBAqEO,WAAW,MAAM,wBAAwB;AAAA;AAAA;AAI7D,UAAM,WAAW;AAAA,MACf,KAAK,EAAE,OAAO,QAAQ;AAAA,MACtB,QAAQ,EAAE,QAAO,mCAAS,WAAU,GAAG;AAAA,MACvC,QAAQ,EAAE,QAAO,mCAAS,WAAU,IAAI;AAAA,IAAA;AAG1C,UAAM,WAAW,IAAI,oBAAoB,GAAG,EAAE;AACxC,UAAA,WAAW,IAAI,eAAe;AAAA,MAClC;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM;AAAA,IAAA,CACP;AAED,UAAM,UAAU,QAAQ;AAAA,EAC1B;AAAA,EAEA,IAAI,OAAO,QAAgB;AACpB,SAAA,SAAS,SAAS,OAAO,QAAQ;AAAA,EACxC;AAAA,EAEA,IAAI,SAAiB;AACZ,WAAA,KAAK,SAAS,SAAS,OAAO;AAAA,EACvC;AAAA,EAEA,IAAI,OAAO,QAAgB;AACpB,SAAA,SAAS,SAAS,OAAO,QAAQ;AAAA,EACxC;AAAA,EAEA,IAAI,SAAiB;AACZ,WAAA,KAAK,SAAS,SAAS,OAAO;AAAA,EACvC;AACF;"}