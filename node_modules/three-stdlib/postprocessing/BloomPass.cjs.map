{"version": 3, "file": "BloomPass.cjs", "sources": ["../../src/postprocessing/BloomPass.ts"], "sourcesContent": ["import { Pass, FullScreenQuad } from './Pass'\nimport {\n  AdditiveBlending,\n  IUniform,\n  ShaderMaterial,\n  UniformsUtils,\n  Vector2,\n  WebGLRenderer,\n  WebGLRenderTarget,\n} from 'three'\nimport { ConvolutionShader } from '../shaders/ConvolutionShader'\n\nclass BloomPass extends Pass {\n  public renderTargetX: WebGLRenderTarget\n  public renderTargetY: WebGLRenderTarget\n  public materialCombine: ShaderMaterial\n  public materialConvolution: ShaderMaterial\n  public fsQuad: FullScreenQuad\n  public combineUniforms: Record<keyof typeof CombineShader['uniforms'], IUniform<any>>\n  public convolutionUniforms: Record<keyof typeof ConvolutionShader['uniforms'], IUniform<any>>\n\n  public blurX = new Vector2(0.001953125, 0.0)\n  public blurY = new Vector2(0.0, 0.001953125)\n\n  constructor(strength = 1, kernelSize = 25, sigma = 4, resolution = 256) {\n    super() // render targets\n\n    this.renderTargetX = new WebGLRenderTarget(resolution, resolution)\n    this.renderTargetX.texture.name = 'BloomPass.x'\n    this.renderTargetY = new WebGLRenderTarget(resolution, resolution)\n    this.renderTargetY.texture.name = 'BloomPass.y' // combine material\n\n    this.combineUniforms = UniformsUtils.clone(CombineShader.uniforms)\n    this.combineUniforms['strength'].value = strength\n    this.materialCombine = new ShaderMaterial({\n      uniforms: this.combineUniforms,\n      vertexShader: CombineShader.vertexShader,\n      fragmentShader: CombineShader.fragmentShader,\n      blending: AdditiveBlending,\n      transparent: true,\n    }) // convolution material\n\n    if (ConvolutionShader === undefined) console.error('BloomPass relies on ConvolutionShader')\n    const convolutionShader = ConvolutionShader\n    this.convolutionUniforms = UniformsUtils.clone(convolutionShader.uniforms)\n    this.convolutionUniforms['uImageIncrement'].value = this.blurX\n    this.convolutionUniforms['cKernel'].value = ConvolutionShader.buildKernel(sigma)\n    this.materialConvolution = new ShaderMaterial({\n      uniforms: this.convolutionUniforms,\n      vertexShader: convolutionShader.vertexShader,\n      fragmentShader: convolutionShader.fragmentShader,\n      defines: {\n        KERNEL_SIZE_FLOAT: kernelSize.toFixed(1),\n        KERNEL_SIZE_INT: kernelSize.toFixed(0),\n      },\n    })\n    this.needsSwap = false\n    this.fsQuad = new FullScreenQuad(this.materialConvolution)\n  }\n\n  public render(\n    renderer: WebGLRenderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget,\n    deltaTime: number,\n    maskActive: boolean,\n  ): void {\n    if (maskActive) renderer.state.buffers.stencil.setTest(false) // Render quad with blured scene into texture (convolution pass 1)\n\n    this.fsQuad.material = this.materialConvolution\n    this.convolutionUniforms['tDiffuse'].value = readBuffer.texture\n    this.convolutionUniforms['uImageIncrement'].value = this.blurX\n    renderer.setRenderTarget(this.renderTargetX)\n    renderer.clear()\n    this.fsQuad.render(renderer) // Render quad with blured scene into texture (convolution pass 2)\n\n    this.convolutionUniforms['tDiffuse'].value = this.renderTargetX.texture\n    this.convolutionUniforms['uImageIncrement'].value = this.blurY\n    renderer.setRenderTarget(this.renderTargetY)\n    renderer.clear()\n    this.fsQuad.render(renderer) // Render original scene with superimposed blur to texture\n\n    this.fsQuad.material = this.materialCombine\n    this.combineUniforms['tDiffuse'].value = this.renderTargetY.texture\n    if (maskActive) renderer.state.buffers.stencil.setTest(true)\n    renderer.setRenderTarget(readBuffer)\n    if (this.clear) renderer.clear()\n    this.fsQuad.render(renderer)\n  }\n}\n\nconst CombineShader = {\n  uniforms: {\n    tDiffuse: {\n      value: null,\n    },\n    strength: {\n      value: 1.0,\n    },\n  },\n  vertexShader:\n    /* glsl */\n    `\n  varying vec2 vUv;\n  void main() {\n    vUv = uv;\n    gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n  }`,\n  fragmentShader:\n    /* glsl */\n    `\n  uniform float strength;\n  uniform sampler2D tDiffuse;\n  varying vec2 vUv;\n  void main() {\n    vec4 texel = texture2D( tDiffuse, vUv );\n    gl_FragColor = strength * texel;\n  }`,\n}\n\nexport { BloomPass }\n"], "names": ["Pass", "Vector2", "WebGLRenderTarget", "UniformsUtils", "ShaderMaterial", "AdditiveBlending", "ConvolutionShader", "FullScreenQuad"], "mappings": ";;;;;;;;;;;AAYA,MAAM,kBAAkBA,KAAAA,KAAK;AAAA,EAY3B,YAAY,WAAW,GAAG,aAAa,IAAI,QAAQ,GAAG,aAAa,KAAK;AAChE;AAZD;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,iCAAQ,IAAIC,MAAAA,QAAQ,YAAa,CAAG;AACpC,iCAAQ,IAAIA,MAAAA,QAAQ,GAAK,UAAW;AAKzC,SAAK,gBAAgB,IAAIC,MAAkB,kBAAA,YAAY,UAAU;AAC5D,SAAA,cAAc,QAAQ,OAAO;AAClC,SAAK,gBAAgB,IAAIA,MAAkB,kBAAA,YAAY,UAAU;AAC5D,SAAA,cAAc,QAAQ,OAAO;AAElC,SAAK,kBAAkBC,MAAA,cAAc,MAAM,cAAc,QAAQ;AAC5D,SAAA,gBAAgB,UAAU,EAAE,QAAQ;AACpC,SAAA,kBAAkB,IAAIC,qBAAe;AAAA,MACxC,UAAU,KAAK;AAAA,MACf,cAAc,cAAc;AAAA,MAC5B,gBAAgB,cAAc;AAAA,MAC9B,UAAUC,MAAA;AAAA,MACV,aAAa;AAAA,IAAA,CACd;AAED,QAAIC,kBAAAA,sBAAsB;AAAW,cAAQ,MAAM,uCAAuC;AAC1F,UAAM,oBAAoBA,kBAAAA;AAC1B,SAAK,sBAAsBH,MAAA,cAAc,MAAM,kBAAkB,QAAQ;AACzE,SAAK,oBAAoB,iBAAiB,EAAE,QAAQ,KAAK;AACzD,SAAK,oBAAoB,SAAS,EAAE,QAAQG,oCAAkB,YAAY,KAAK;AAC1E,SAAA,sBAAsB,IAAIF,qBAAe;AAAA,MAC5C,UAAU,KAAK;AAAA,MACf,cAAc,kBAAkB;AAAA,MAChC,gBAAgB,kBAAkB;AAAA,MAClC,SAAS;AAAA,QACP,mBAAmB,WAAW,QAAQ,CAAC;AAAA,QACvC,iBAAiB,WAAW,QAAQ,CAAC;AAAA,MACvC;AAAA,IAAA,CACD;AACD,SAAK,YAAY;AACjB,SAAK,SAAS,IAAIG,KAAe,eAAA,KAAK,mBAAmB;AAAA,EAC3D;AAAA,EAEO,OACL,UACA,aACA,YACA,WACA,YACM;AACF,QAAA;AAAY,eAAS,MAAM,QAAQ,QAAQ,QAAQ,KAAK;AAEvD,SAAA,OAAO,WAAW,KAAK;AAC5B,SAAK,oBAAoB,UAAU,EAAE,QAAQ,WAAW;AACxD,SAAK,oBAAoB,iBAAiB,EAAE,QAAQ,KAAK;AAChD,aAAA,gBAAgB,KAAK,aAAa;AAC3C,aAAS,MAAM;AACV,SAAA,OAAO,OAAO,QAAQ;AAE3B,SAAK,oBAAoB,UAAU,EAAE,QAAQ,KAAK,cAAc;AAChE,SAAK,oBAAoB,iBAAiB,EAAE,QAAQ,KAAK;AAChD,aAAA,gBAAgB,KAAK,aAAa;AAC3C,aAAS,MAAM;AACV,SAAA,OAAO,OAAO,QAAQ;AAEtB,SAAA,OAAO,WAAW,KAAK;AAC5B,SAAK,gBAAgB,UAAU,EAAE,QAAQ,KAAK,cAAc;AACxD,QAAA;AAAY,eAAS,MAAM,QAAQ,QAAQ,QAAQ,IAAI;AAC3D,aAAS,gBAAgB,UAAU;AACnC,QAAI,KAAK;AAAO,eAAS,MAAM;AAC1B,SAAA,OAAO,OAAO,QAAQ;AAAA,EAC7B;AACF;AAEA,MAAM,gBAAgB;AAAA,EACpB,UAAU;AAAA,IACR,UAAU;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA;AAAA;AAAA,IAEE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMF;AAAA;AAAA,IAEE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQJ;;"}