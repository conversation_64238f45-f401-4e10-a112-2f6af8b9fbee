{"version": 3, "file": "DecalGeometry.cjs", "sources": ["../../src/geometries/DecalGeometry.js"], "sourcesContent": ["import { BufferGeometry, Float32BufferAttribute, Matrix4, Vector3 } from 'three'\n\n/**\n * You can use this geometry to create a decal mesh, that serves different kinds of purposes.\n * e.g. adding unique details to models, performing dynamic visual environmental changes or covering seams.\n *\n * Constructor parameter:\n *\n * mesh — Any mesh object\n * position — Position of the decal projector\n * orientation — Orientation of the decal projector\n * size — Size of the decal projector\n *\n * reference: http://blog.wolfire.com/2009/06/how-to-project-decals/\n *\n */\n\nclass DecalGeometry extends BufferGeometry {\n  constructor(mesh, position, orientation, size) {\n    super()\n\n    // buffers\n\n    const vertices = []\n    const normals = []\n    const uvs = []\n\n    // helpers\n\n    const plane = new Vector3()\n\n    // this matrix represents the transformation of the decal projector\n\n    const projectorMatrix = new Matrix4()\n    projectorMatrix.makeRotationFromEuler(orientation)\n    projectorMatrix.setPosition(position)\n\n    const projectorMatrixInverse = new Matrix4()\n    projectorMatrixInverse.copy(projectorMatrix).invert()\n\n    // generate buffers\n\n    generate()\n\n    // build geometry\n\n    this.setAttribute('position', new Float32BufferAttribute(vertices, 3))\n    this.setAttribute('normal', new Float32BufferAttribute(normals, 3))\n    this.setAttribute('uv', new Float32BufferAttribute(uvs, 2))\n\n    function generate() {\n      let i\n\n      let decalVertices = []\n\n      const vertex = new Vector3()\n      const normal = new Vector3()\n\n      // handle different geometry types\n\n      if (mesh.geometry.isGeometry === true) {\n        console.error('THREE.DecalGeometry no longer supports THREE.Geometry. Use BufferGeometry instead.')\n        return\n      }\n\n      const geometry = mesh.geometry\n\n      const positionAttribute = geometry.attributes.position\n      const normalAttribute = geometry.attributes.normal\n\n      // first, create an array of 'DecalVertex' objects\n      // three consecutive 'DecalVertex' objects represent a single face\n      //\n      // this data structure will be later used to perform the clipping\n\n      if (geometry.index !== null) {\n        // indexed BufferGeometry\n\n        const index = geometry.index\n\n        for (i = 0; i < index.count; i++) {\n          vertex.fromBufferAttribute(positionAttribute, index.getX(i))\n          normal.fromBufferAttribute(normalAttribute, index.getX(i))\n\n          pushDecalVertex(decalVertices, vertex, normal)\n        }\n      } else {\n        // non-indexed BufferGeometry\n\n        for (i = 0; i < positionAttribute.count; i++) {\n          vertex.fromBufferAttribute(positionAttribute, i)\n          normal.fromBufferAttribute(normalAttribute, i)\n\n          pushDecalVertex(decalVertices, vertex, normal)\n        }\n      }\n\n      // second, clip the geometry so that it doesn't extend out from the projector\n\n      decalVertices = clipGeometry(decalVertices, plane.set(1, 0, 0))\n      decalVertices = clipGeometry(decalVertices, plane.set(-1, 0, 0))\n      decalVertices = clipGeometry(decalVertices, plane.set(0, 1, 0))\n      decalVertices = clipGeometry(decalVertices, plane.set(0, -1, 0))\n      decalVertices = clipGeometry(decalVertices, plane.set(0, 0, 1))\n      decalVertices = clipGeometry(decalVertices, plane.set(0, 0, -1))\n\n      // third, generate final vertices, normals and uvs\n\n      for (i = 0; i < decalVertices.length; i++) {\n        const decalVertex = decalVertices[i]\n\n        // create texture coordinates (we are still in projector space)\n\n        uvs.push(0.5 + decalVertex.position.x / size.x, 0.5 + decalVertex.position.y / size.y)\n\n        // transform the vertex back to world space\n\n        decalVertex.position.applyMatrix4(projectorMatrix)\n\n        // now create vertex and normal buffer data\n\n        vertices.push(decalVertex.position.x, decalVertex.position.y, decalVertex.position.z)\n        normals.push(decalVertex.normal.x, decalVertex.normal.y, decalVertex.normal.z)\n      }\n    }\n\n    function pushDecalVertex(decalVertices, vertex, normal) {\n      // transform the vertex to world space, then to projector space\n\n      vertex.applyMatrix4(mesh.matrixWorld)\n      vertex.applyMatrix4(projectorMatrixInverse)\n\n      normal.transformDirection(mesh.matrixWorld)\n\n      decalVertices.push(new DecalVertex(vertex.clone(), normal.clone()))\n    }\n\n    function clipGeometry(inVertices, plane) {\n      const outVertices = []\n\n      const s = 0.5 * Math.abs(size.dot(plane))\n\n      // a single iteration clips one face,\n      // which consists of three consecutive 'DecalVertex' objects\n\n      for (let i = 0; i < inVertices.length; i += 3) {\n        let v1Out,\n          v2Out,\n          v3Out,\n          total = 0\n        let nV1, nV2, nV3, nV4\n\n        const d1 = inVertices[i + 0].position.dot(plane) - s\n        const d2 = inVertices[i + 1].position.dot(plane) - s\n        const d3 = inVertices[i + 2].position.dot(plane) - s\n\n        v1Out = d1 > 0\n        v2Out = d2 > 0\n        v3Out = d3 > 0\n\n        // calculate, how many vertices of the face lie outside of the clipping plane\n\n        total = (v1Out ? 1 : 0) + (v2Out ? 1 : 0) + (v3Out ? 1 : 0)\n\n        switch (total) {\n          case 0: {\n            // the entire face lies inside of the plane, no clipping needed\n\n            outVertices.push(inVertices[i])\n            outVertices.push(inVertices[i + 1])\n            outVertices.push(inVertices[i + 2])\n            break\n          }\n\n          case 1: {\n            // one vertex lies outside of the plane, perform clipping\n\n            if (v1Out) {\n              nV1 = inVertices[i + 1]\n              nV2 = inVertices[i + 2]\n              nV3 = clip(inVertices[i], nV1, plane, s)\n              nV4 = clip(inVertices[i], nV2, plane, s)\n            }\n\n            if (v2Out) {\n              nV1 = inVertices[i]\n              nV2 = inVertices[i + 2]\n              nV3 = clip(inVertices[i + 1], nV1, plane, s)\n              nV4 = clip(inVertices[i + 1], nV2, plane, s)\n\n              outVertices.push(nV3)\n              outVertices.push(nV2.clone())\n              outVertices.push(nV1.clone())\n\n              outVertices.push(nV2.clone())\n              outVertices.push(nV3.clone())\n              outVertices.push(nV4)\n              break\n            }\n\n            if (v3Out) {\n              nV1 = inVertices[i]\n              nV2 = inVertices[i + 1]\n              nV3 = clip(inVertices[i + 2], nV1, plane, s)\n              nV4 = clip(inVertices[i + 2], nV2, plane, s)\n            }\n\n            outVertices.push(nV1.clone())\n            outVertices.push(nV2.clone())\n            outVertices.push(nV3)\n\n            outVertices.push(nV4)\n            outVertices.push(nV3.clone())\n            outVertices.push(nV2.clone())\n\n            break\n          }\n\n          case 2: {\n            // two vertices lies outside of the plane, perform clipping\n\n            if (!v1Out) {\n              nV1 = inVertices[i].clone()\n              nV2 = clip(nV1, inVertices[i + 1], plane, s)\n              nV3 = clip(nV1, inVertices[i + 2], plane, s)\n              outVertices.push(nV1)\n              outVertices.push(nV2)\n              outVertices.push(nV3)\n            }\n\n            if (!v2Out) {\n              nV1 = inVertices[i + 1].clone()\n              nV2 = clip(nV1, inVertices[i + 2], plane, s)\n              nV3 = clip(nV1, inVertices[i], plane, s)\n              outVertices.push(nV1)\n              outVertices.push(nV2)\n              outVertices.push(nV3)\n            }\n\n            if (!v3Out) {\n              nV1 = inVertices[i + 2].clone()\n              nV2 = clip(nV1, inVertices[i], plane, s)\n              nV3 = clip(nV1, inVertices[i + 1], plane, s)\n              outVertices.push(nV1)\n              outVertices.push(nV2)\n              outVertices.push(nV3)\n            }\n\n            break\n          }\n\n          case 3: {\n            // the entire face lies outside of the plane, so let's discard the corresponding vertices\n\n            break\n          }\n        }\n      }\n\n      return outVertices\n    }\n\n    function clip(v0, v1, p, s) {\n      const d0 = v0.position.dot(p) - s\n      const d1 = v1.position.dot(p) - s\n\n      const s0 = d0 / (d0 - d1)\n\n      const v = new DecalVertex(\n        new Vector3(\n          v0.position.x + s0 * (v1.position.x - v0.position.x),\n          v0.position.y + s0 * (v1.position.y - v0.position.y),\n          v0.position.z + s0 * (v1.position.z - v0.position.z),\n        ),\n        new Vector3(\n          v0.normal.x + s0 * (v1.normal.x - v0.normal.x),\n          v0.normal.y + s0 * (v1.normal.y - v0.normal.y),\n          v0.normal.z + s0 * (v1.normal.z - v0.normal.z),\n        ),\n      )\n\n      // need to clip more values (texture coordinates)? do it this way:\n      // intersectpoint.value = a.value + s * ( b.value - a.value );\n\n      return v\n    }\n  }\n}\n\n// helper\n\nclass DecalVertex {\n  constructor(position, normal) {\n    this.position = position\n    this.normal = normal\n  }\n\n  clone() {\n    return new this.constructor(this.position.clone(), this.normal.clone())\n  }\n}\n\nexport { DecalGeometry, DecalVertex }\n"], "names": ["BufferGeometry", "Vector3", "Matrix4", "Float32BufferAttribute", "plane"], "mappings": ";;;AAiBA,MAAM,sBAAsBA,MAAAA,eAAe;AAAA,EACzC,YAAY,MAAM,UAAU,aAAa,MAAM;AAC7C,UAAO;AAIP,UAAM,WAAW,CAAE;AACnB,UAAM,UAAU,CAAE;AAClB,UAAM,MAAM,CAAE;AAId,UAAM,QAAQ,IAAIC,cAAS;AAI3B,UAAM,kBAAkB,IAAIC,cAAS;AACrC,oBAAgB,sBAAsB,WAAW;AACjD,oBAAgB,YAAY,QAAQ;AAEpC,UAAM,yBAAyB,IAAIA,cAAS;AAC5C,2BAAuB,KAAK,eAAe,EAAE,OAAQ;AAIrD,aAAU;AAIV,SAAK,aAAa,YAAY,IAAIC,MAAAA,uBAAuB,UAAU,CAAC,CAAC;AACrE,SAAK,aAAa,UAAU,IAAIA,MAAAA,uBAAuB,SAAS,CAAC,CAAC;AAClE,SAAK,aAAa,MAAM,IAAIA,MAAAA,uBAAuB,KAAK,CAAC,CAAC;AAE1D,aAAS,WAAW;AAClB,UAAI;AAEJ,UAAI,gBAAgB,CAAE;AAEtB,YAAM,SAAS,IAAIF,cAAS;AAC5B,YAAM,SAAS,IAAIA,cAAS;AAI5B,UAAI,KAAK,SAAS,eAAe,MAAM;AACrC,gBAAQ,MAAM,oFAAoF;AAClG;AAAA,MACD;AAED,YAAM,WAAW,KAAK;AAEtB,YAAM,oBAAoB,SAAS,WAAW;AAC9C,YAAM,kBAAkB,SAAS,WAAW;AAO5C,UAAI,SAAS,UAAU,MAAM;AAG3B,cAAM,QAAQ,SAAS;AAEvB,aAAK,IAAI,GAAG,IAAI,MAAM,OAAO,KAAK;AAChC,iBAAO,oBAAoB,mBAAmB,MAAM,KAAK,CAAC,CAAC;AAC3D,iBAAO,oBAAoB,iBAAiB,MAAM,KAAK,CAAC,CAAC;AAEzD,0BAAgB,eAAe,QAAQ,MAAM;AAAA,QAC9C;AAAA,MACT,OAAa;AAGL,aAAK,IAAI,GAAG,IAAI,kBAAkB,OAAO,KAAK;AAC5C,iBAAO,oBAAoB,mBAAmB,CAAC;AAC/C,iBAAO,oBAAoB,iBAAiB,CAAC;AAE7C,0BAAgB,eAAe,QAAQ,MAAM;AAAA,QAC9C;AAAA,MACF;AAID,sBAAgB,aAAa,eAAe,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC;AAC9D,sBAAgB,aAAa,eAAe,MAAM,IAAI,IAAI,GAAG,CAAC,CAAC;AAC/D,sBAAgB,aAAa,eAAe,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC;AAC9D,sBAAgB,aAAa,eAAe,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC;AAC/D,sBAAgB,aAAa,eAAe,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC;AAC9D,sBAAgB,aAAa,eAAe,MAAM,IAAI,GAAG,GAAG,EAAE,CAAC;AAI/D,WAAK,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AACzC,cAAM,cAAc,cAAc,CAAC;AAInC,YAAI,KAAK,MAAM,YAAY,SAAS,IAAI,KAAK,GAAG,MAAM,YAAY,SAAS,IAAI,KAAK,CAAC;AAIrF,oBAAY,SAAS,aAAa,eAAe;AAIjD,iBAAS,KAAK,YAAY,SAAS,GAAG,YAAY,SAAS,GAAG,YAAY,SAAS,CAAC;AACpF,gBAAQ,KAAK,YAAY,OAAO,GAAG,YAAY,OAAO,GAAG,YAAY,OAAO,CAAC;AAAA,MAC9E;AAAA,IACF;AAED,aAAS,gBAAgB,eAAe,QAAQ,QAAQ;AAGtD,aAAO,aAAa,KAAK,WAAW;AACpC,aAAO,aAAa,sBAAsB;AAE1C,aAAO,mBAAmB,KAAK,WAAW;AAE1C,oBAAc,KAAK,IAAI,YAAY,OAAO,SAAS,OAAO,MAAK,CAAE,CAAC;AAAA,IACnE;AAED,aAAS,aAAa,YAAYG,QAAO;AACvC,YAAM,cAAc,CAAE;AAEtB,YAAM,IAAI,MAAM,KAAK,IAAI,KAAK,IAAIA,MAAK,CAAC;AAKxC,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK,GAAG;AAC7C,YAAI,OACF,OACA,OACA,QAAQ;AACV,YAAI,KAAK,KAAK,KAAK;AAEnB,cAAM,KAAK,WAAW,IAAI,CAAC,EAAE,SAAS,IAAIA,MAAK,IAAI;AACnD,cAAM,KAAK,WAAW,IAAI,CAAC,EAAE,SAAS,IAAIA,MAAK,IAAI;AACnD,cAAM,KAAK,WAAW,IAAI,CAAC,EAAE,SAAS,IAAIA,MAAK,IAAI;AAEnD,gBAAQ,KAAK;AACb,gBAAQ,KAAK;AACb,gBAAQ,KAAK;AAIb,iBAAS,QAAQ,IAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,IAAI;AAEzD,gBAAQ,OAAK;AAAA,UACX,KAAK,GAAG;AAGN,wBAAY,KAAK,WAAW,CAAC,CAAC;AAC9B,wBAAY,KAAK,WAAW,IAAI,CAAC,CAAC;AAClC,wBAAY,KAAK,WAAW,IAAI,CAAC,CAAC;AAClC;AAAA,UACD;AAAA,UAED,KAAK,GAAG;AAGN,gBAAI,OAAO;AACT,oBAAM,WAAW,IAAI,CAAC;AACtB,oBAAM,WAAW,IAAI,CAAC;AACtB,oBAAM,KAAK,WAAW,CAAC,GAAG,KAAKA,QAAO,CAAC;AACvC,oBAAM,KAAK,WAAW,CAAC,GAAG,KAAKA,QAAO,CAAC;AAAA,YACxC;AAED,gBAAI,OAAO;AACT,oBAAM,WAAW,CAAC;AAClB,oBAAM,WAAW,IAAI,CAAC;AACtB,oBAAM,KAAK,WAAW,IAAI,CAAC,GAAG,KAAKA,QAAO,CAAC;AAC3C,oBAAM,KAAK,WAAW,IAAI,CAAC,GAAG,KAAKA,QAAO,CAAC;AAE3C,0BAAY,KAAK,GAAG;AACpB,0BAAY,KAAK,IAAI,OAAO;AAC5B,0BAAY,KAAK,IAAI,OAAO;AAE5B,0BAAY,KAAK,IAAI,OAAO;AAC5B,0BAAY,KAAK,IAAI,OAAO;AAC5B,0BAAY,KAAK,GAAG;AACpB;AAAA,YACD;AAED,gBAAI,OAAO;AACT,oBAAM,WAAW,CAAC;AAClB,oBAAM,WAAW,IAAI,CAAC;AACtB,oBAAM,KAAK,WAAW,IAAI,CAAC,GAAG,KAAKA,QAAO,CAAC;AAC3C,oBAAM,KAAK,WAAW,IAAI,CAAC,GAAG,KAAKA,QAAO,CAAC;AAAA,YAC5C;AAED,wBAAY,KAAK,IAAI,OAAO;AAC5B,wBAAY,KAAK,IAAI,OAAO;AAC5B,wBAAY,KAAK,GAAG;AAEpB,wBAAY,KAAK,GAAG;AACpB,wBAAY,KAAK,IAAI,OAAO;AAC5B,wBAAY,KAAK,IAAI,OAAO;AAE5B;AAAA,UACD;AAAA,UAED,KAAK,GAAG;AAGN,gBAAI,CAAC,OAAO;AACV,oBAAM,WAAW,CAAC,EAAE,MAAO;AAC3B,oBAAM,KAAK,KAAK,WAAW,IAAI,CAAC,GAAGA,QAAO,CAAC;AAC3C,oBAAM,KAAK,KAAK,WAAW,IAAI,CAAC,GAAGA,QAAO,CAAC;AAC3C,0BAAY,KAAK,GAAG;AACpB,0BAAY,KAAK,GAAG;AACpB,0BAAY,KAAK,GAAG;AAAA,YACrB;AAED,gBAAI,CAAC,OAAO;AACV,oBAAM,WAAW,IAAI,CAAC,EAAE,MAAO;AAC/B,oBAAM,KAAK,KAAK,WAAW,IAAI,CAAC,GAAGA,QAAO,CAAC;AAC3C,oBAAM,KAAK,KAAK,WAAW,CAAC,GAAGA,QAAO,CAAC;AACvC,0BAAY,KAAK,GAAG;AACpB,0BAAY,KAAK,GAAG;AACpB,0BAAY,KAAK,GAAG;AAAA,YACrB;AAED,gBAAI,CAAC,OAAO;AACV,oBAAM,WAAW,IAAI,CAAC,EAAE,MAAO;AAC/B,oBAAM,KAAK,KAAK,WAAW,CAAC,GAAGA,QAAO,CAAC;AACvC,oBAAM,KAAK,KAAK,WAAW,IAAI,CAAC,GAAGA,QAAO,CAAC;AAC3C,0BAAY,KAAK,GAAG;AACpB,0BAAY,KAAK,GAAG;AACpB,0BAAY,KAAK,GAAG;AAAA,YACrB;AAED;AAAA,UACD;AAAA,QAOF;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,KAAK,IAAI,IAAI,GAAG,GAAG;AAC1B,YAAM,KAAK,GAAG,SAAS,IAAI,CAAC,IAAI;AAChC,YAAM,KAAK,GAAG,SAAS,IAAI,CAAC,IAAI;AAEhC,YAAM,KAAK,MAAM,KAAK;AAEtB,YAAM,IAAI,IAAI;AAAA,QACZ,IAAIH,MAAO;AAAA,UACT,GAAG,SAAS,IAAI,MAAM,GAAG,SAAS,IAAI,GAAG,SAAS;AAAA,UAClD,GAAG,SAAS,IAAI,MAAM,GAAG,SAAS,IAAI,GAAG,SAAS;AAAA,UAClD,GAAG,SAAS,IAAI,MAAM,GAAG,SAAS,IAAI,GAAG,SAAS;AAAA,QACnD;AAAA,QACD,IAAIA,MAAO;AAAA,UACT,GAAG,OAAO,IAAI,MAAM,GAAG,OAAO,IAAI,GAAG,OAAO;AAAA,UAC5C,GAAG,OAAO,IAAI,MAAM,GAAG,OAAO,IAAI,GAAG,OAAO;AAAA,UAC5C,GAAG,OAAO,IAAI,MAAM,GAAG,OAAO,IAAI,GAAG,OAAO;AAAA,QAC7C;AAAA,MACF;AAKD,aAAO;AAAA,IACR;AAAA,EACF;AACH;AAIA,MAAM,YAAY;AAAA,EAChB,YAAY,UAAU,QAAQ;AAC5B,SAAK,WAAW;AAChB,SAAK,SAAS;AAAA,EACf;AAAA,EAED,QAAQ;AACN,WAAO,IAAI,KAAK,YAAY,KAAK,SAAS,MAAK,GAAI,KAAK,OAAO,OAAO;AAAA,EACvE;AACH;;;"}