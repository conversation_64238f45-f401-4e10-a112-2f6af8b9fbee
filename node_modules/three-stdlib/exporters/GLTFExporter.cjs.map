{"version": 3, "file": "GLTFExporter.cjs", "sources": ["../../src/exporters/GLTFExporter.js"], "sourcesContent": ["import {\n  RE<PERSON><PERSON><PERSON>,\n  <PERSON>ufferAttri<PERSON>e,\n  ClampToEdgeWrapping,\n  Color,\n  DoubleSide,\n  InterpolateDiscrete,\n  InterpolateLinear,\n  LinearFilter,\n  LinearMipmapLinearFilter,\n  LinearMipmapNearestFilter,\n  MathUtils,\n  Matrix4,\n  MirroredRepeatWrapping,\n  NearestFilter,\n  NearestMipmapLinearFilter,\n  NearestMipmapNearestFilter,\n  PropertyBinding,\n  RGBAFormat,\n  RepeatWrapping,\n  Scene,\n  Texture,\n  CompressedTexture,\n  Vector3,\n  PlaneGeometry,\n  ShaderMaterial,\n  Uniform,\n  Mesh,\n  PerspectiveCamera,\n  WebGLRenderer,\n} from 'three'\nimport { version } from '../_polyfill/constants'\n\nasync function readAsDataURL(blob) {\n  const buffer = await blob.arrayBuffer()\n  const data = btoa(String.fromCharCode(...new Uint8Array(buffer)))\n  return `data:${blob.type || ''};base64,${data}`\n}\n\nlet _renderer\nlet fullscreenQuadGeometry\nlet fullscreenQuadMaterial\nlet fullscreenQuad\n\nfunction decompress(texture, maxTextureSize = Infinity, renderer = null) {\n  if (!fullscreenQuadGeometry) fullscreenQuadGeometry = new PlaneGeometry(2, 2, 1, 1)\n  if (!fullscreenQuadMaterial)\n    fullscreenQuadMaterial = new ShaderMaterial({\n      uniforms: { blitTexture: new Uniform(texture) },\n      vertexShader: /* glsl */ `\n        varying vec2 vUv;\n        void main(){\n            vUv = uv;\n            gl_Position = vec4(position.xy * 1.0,0.,.999999);\n        }\n      `,\n      fragmentShader: /* glsl */ `\n          uniform sampler2D blitTexture; \n          varying vec2 vUv;\n\n          void main(){ \n              gl_FragColor = vec4(vUv.xy, 0, 1);\n              \n              #ifdef IS_SRGB\n              gl_FragColor = LinearTosRGB( texture2D( blitTexture, vUv) );\n              #else\n              gl_FragColor = texture2D( blitTexture, vUv);\n              #endif\n          }\n      `,\n    })\n\n  fullscreenQuadMaterial.uniforms.blitTexture.value = texture\n  fullscreenQuadMaterial.defines.IS_SRGB =\n    'colorSpace' in texture ? texture.colorSpace === 'srgb' : texture.encoding === 3001\n  fullscreenQuadMaterial.needsUpdate = true\n\n  if (!fullscreenQuad) {\n    fullscreenQuad = new Mesh(fullscreenQuadGeometry, fullscreenQuadMaterial)\n    fullscreenQuad.frustrumCulled = false\n  }\n\n  const _camera = new PerspectiveCamera()\n  const _scene = new Scene()\n  _scene.add(fullscreenQuad)\n\n  if (!renderer) {\n    renderer = _renderer = new WebGLRenderer({ antialias: false })\n  }\n\n  renderer.setSize(Math.min(texture.image.width, maxTextureSize), Math.min(texture.image.height, maxTextureSize))\n  renderer.clear()\n  renderer.render(_scene, _camera)\n\n  const readableTexture = new Texture(renderer.domElement)\n\n  readableTexture.minFilter = texture.minFilter\n  readableTexture.magFilter = texture.magFilter\n  readableTexture.wrapS = texture.wrapS\n  readableTexture.wrapT = texture.wrapT\n  readableTexture.name = texture.name\n\n  if (_renderer) {\n    _renderer.dispose()\n    _renderer = null\n  }\n\n  return readableTexture\n}\n\n/**\n * The KHR_mesh_quantization extension allows these extra attribute component types\n *\n * @see https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_mesh_quantization/README.md#extending-mesh-attributes\n */\nconst KHR_mesh_quantization_ExtraAttrTypes = {\n  POSITION: [\n    'byte',\n    'byte normalized',\n    'unsigned byte',\n    'unsigned byte normalized',\n    'short',\n    'short normalized',\n    'unsigned short',\n    'unsigned short normalized',\n  ],\n  NORMAL: ['byte normalized', 'short normalized'],\n  TANGENT: ['byte normalized', 'short normalized'],\n  TEXCOORD: ['byte', 'byte normalized', 'unsigned byte', 'short', 'short normalized', 'unsigned short'],\n}\n\nconst GLTFExporter = /* @__PURE__ */ (() => {\n  class GLTFExporter {\n    /**\n     * Static utility functions\n     */\n    static Utils = {\n      insertKeyframe: function (track, time) {\n        const tolerance = 0.001 // 1ms\n        const valueSize = track.getValueSize()\n\n        const times = new track.TimeBufferType(track.times.length + 1)\n        const values = new track.ValueBufferType(track.values.length + valueSize)\n        const interpolant = track.createInterpolant(new track.ValueBufferType(valueSize))\n\n        let index\n\n        if (track.times.length === 0) {\n          times[0] = time\n\n          for (let i = 0; i < valueSize; i++) {\n            values[i] = 0\n          }\n\n          index = 0\n        } else if (time < track.times[0]) {\n          if (Math.abs(track.times[0] - time) < tolerance) return 0\n\n          times[0] = time\n          times.set(track.times, 1)\n\n          values.set(interpolant.evaluate(time), 0)\n          values.set(track.values, valueSize)\n\n          index = 0\n        } else if (time > track.times[track.times.length - 1]) {\n          if (Math.abs(track.times[track.times.length - 1] - time) < tolerance) {\n            return track.times.length - 1\n          }\n\n          times[times.length - 1] = time\n          times.set(track.times, 0)\n\n          values.set(track.values, 0)\n          values.set(interpolant.evaluate(time), track.values.length)\n\n          index = times.length - 1\n        } else {\n          for (let i = 0; i < track.times.length; i++) {\n            if (Math.abs(track.times[i] - time) < tolerance) return i\n\n            if (track.times[i] < time && track.times[i + 1] > time) {\n              times.set(track.times.slice(0, i + 1), 0)\n              times[i + 1] = time\n              times.set(track.times.slice(i + 1), i + 2)\n\n              values.set(track.values.slice(0, (i + 1) * valueSize), 0)\n              values.set(interpolant.evaluate(time), (i + 1) * valueSize)\n              values.set(track.values.slice((i + 1) * valueSize), (i + 2) * valueSize)\n\n              index = i + 1\n\n              break\n            }\n          }\n        }\n\n        track.times = times\n        track.values = values\n\n        return index\n      },\n\n      mergeMorphTargetTracks: function (clip, root) {\n        const tracks = []\n        const mergedTracks = {}\n        const sourceTracks = clip.tracks\n\n        for (let i = 0; i < sourceTracks.length; ++i) {\n          let sourceTrack = sourceTracks[i]\n          const sourceTrackBinding = PropertyBinding.parseTrackName(sourceTrack.name)\n          const sourceTrackNode = PropertyBinding.findNode(root, sourceTrackBinding.nodeName)\n\n          if (\n            sourceTrackBinding.propertyName !== 'morphTargetInfluences' ||\n            sourceTrackBinding.propertyIndex === undefined\n          ) {\n            // Tracks that don't affect morph targets, or that affect all morph targets together, can be left as-is.\n            tracks.push(sourceTrack)\n            continue\n          }\n\n          if (\n            sourceTrack.createInterpolant !== sourceTrack.InterpolantFactoryMethodDiscrete &&\n            sourceTrack.createInterpolant !== sourceTrack.InterpolantFactoryMethodLinear\n          ) {\n            if (sourceTrack.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline) {\n              // This should never happen, because glTF morph target animations\n              // affect all targets already.\n              throw new Error('THREE.GLTFExporter: Cannot merge tracks with glTF CUBICSPLINE interpolation.')\n            }\n\n            console.warn('THREE.GLTFExporter: Morph target interpolation mode not yet supported. Using LINEAR instead.')\n\n            sourceTrack = sourceTrack.clone()\n            sourceTrack.setInterpolation(InterpolateLinear)\n          }\n\n          const targetCount = sourceTrackNode.morphTargetInfluences.length\n          const targetIndex = sourceTrackNode.morphTargetDictionary[sourceTrackBinding.propertyIndex]\n\n          if (targetIndex === undefined) {\n            throw new Error('THREE.GLTFExporter: Morph target name not found: ' + sourceTrackBinding.propertyIndex)\n          }\n\n          let mergedTrack\n\n          // If this is the first time we've seen this object, create a new\n          // track to store merged keyframe data for each morph target.\n          if (mergedTracks[sourceTrackNode.uuid] === undefined) {\n            mergedTrack = sourceTrack.clone()\n\n            const values = new mergedTrack.ValueBufferType(targetCount * mergedTrack.times.length)\n\n            for (let j = 0; j < mergedTrack.times.length; j++) {\n              values[j * targetCount + targetIndex] = mergedTrack.values[j]\n            }\n\n            // We need to take into consideration the intended target node\n            // of our original un-merged morphTarget animation.\n            mergedTrack.name = (sourceTrackBinding.nodeName || '') + '.morphTargetInfluences'\n            mergedTrack.values = values\n\n            mergedTracks[sourceTrackNode.uuid] = mergedTrack\n            tracks.push(mergedTrack)\n\n            continue\n          }\n\n          const sourceInterpolant = sourceTrack.createInterpolant(new sourceTrack.ValueBufferType(1))\n\n          mergedTrack = mergedTracks[sourceTrackNode.uuid]\n\n          // For every existing keyframe of the merged track, write a (possibly\n          // interpolated) value from the source track.\n          for (let j = 0; j < mergedTrack.times.length; j++) {\n            mergedTrack.values[j * targetCount + targetIndex] = sourceInterpolant.evaluate(mergedTrack.times[j])\n          }\n\n          // For every existing keyframe of the source track, write a (possibly\n          // new) keyframe to the merged track. Values from the previous loop may\n          // be written again, but keyframes are de-duplicated.\n          for (let j = 0; j < sourceTrack.times.length; j++) {\n            const keyframeIndex = this.insertKeyframe(mergedTrack, sourceTrack.times[j])\n            mergedTrack.values[keyframeIndex * targetCount + targetIndex] = sourceTrack.values[j]\n          }\n        }\n\n        clip.tracks = tracks\n\n        return clip\n      },\n    }\n\n    constructor() {\n      this.pluginCallbacks = []\n\n      this.register(function (writer) {\n        return new GLTFLightExtension(writer)\n      })\n\n      this.register(function (writer) {\n        return new GLTFMaterialsUnlitExtension(writer)\n      })\n\n      this.register(function (writer) {\n        return new GLTFMaterialsTransmissionExtension(writer)\n      })\n\n      this.register(function (writer) {\n        return new GLTFMaterialsVolumeExtension(writer)\n      })\n\n      this.register(function (writer) {\n        return new GLTFMaterialsIorExtension(writer)\n      })\n\n      this.register(function (writer) {\n        return new GLTFMaterialsSpecularExtension(writer)\n      })\n\n      this.register(function (writer) {\n        return new GLTFMaterialsClearcoatExtension(writer)\n      })\n\n      this.register(function (writer) {\n        return new GLTFMaterialsIridescenceExtension(writer)\n      })\n\n      this.register(function (writer) {\n        return new GLTFMaterialsSheenExtension(writer)\n      })\n\n      this.register(function (writer) {\n        return new GLTFMaterialsAnisotropyExtension(writer)\n      })\n\n      this.register(function (writer) {\n        return new GLTFMaterialsEmissiveStrengthExtension(writer)\n      })\n    }\n\n    register(callback) {\n      if (this.pluginCallbacks.indexOf(callback) === -1) {\n        this.pluginCallbacks.push(callback)\n      }\n\n      return this\n    }\n\n    unregister(callback) {\n      if (this.pluginCallbacks.indexOf(callback) !== -1) {\n        this.pluginCallbacks.splice(this.pluginCallbacks.indexOf(callback), 1)\n      }\n\n      return this\n    }\n\n    /**\n     * Parse scenes and generate GLTF output\n     * @param  {Scene or [THREE.Scenes]} input   Scene or Array of THREE.Scenes\n     * @param  {Function} onDone  Callback on completed\n     * @param  {Function} onError  Callback on errors\n     * @param  {Object} options options\n     */\n    parse(input, onDone, onError, options) {\n      const writer = new GLTFWriter()\n      const plugins = []\n\n      for (let i = 0, il = this.pluginCallbacks.length; i < il; i++) {\n        plugins.push(this.pluginCallbacks[i](writer))\n      }\n\n      writer.setPlugins(plugins)\n      writer.write(input, onDone, options).catch(onError)\n    }\n\n    parseAsync(input, options) {\n      const scope = this\n\n      return new Promise(function (resolve, reject) {\n        scope.parse(input, resolve, reject, options)\n      })\n    }\n  }\n\n  return GLTFExporter\n})()\n\n//------------------------------------------------------------------------------\n// Constants\n//------------------------------------------------------------------------------\n\nconst WEBGL_CONSTANTS = {\n  POINTS: 0x0000,\n  LINES: 0x0001,\n  LINE_LOOP: 0x0002,\n  LINE_STRIP: 0x0003,\n  TRIANGLES: 0x0004,\n  TRIANGLE_STRIP: 0x0005,\n  TRIANGLE_FAN: 0x0006,\n\n  BYTE: 0x1400,\n  UNSIGNED_BYTE: 0x1401,\n  SHORT: 0x1402,\n  UNSIGNED_SHORT: 0x1403,\n  INT: 0x1404,\n  UNSIGNED_INT: 0x1405,\n  FLOAT: 0x1406,\n\n  ARRAY_BUFFER: 0x8892,\n  ELEMENT_ARRAY_BUFFER: 0x8893,\n\n  NEAREST: 0x2600,\n  LINEAR: 0x2601,\n  NEAREST_MIPMAP_NEAREST: 0x2700,\n  LINEAR_MIPMAP_NEAREST: 0x2701,\n  NEAREST_MIPMAP_LINEAR: 0x2702,\n  LINEAR_MIPMAP_LINEAR: 0x2703,\n\n  CLAMP_TO_EDGE: 33071,\n  MIRRORED_REPEAT: 33648,\n  REPEAT: 10497,\n}\n\nconst KHR_MESH_QUANTIZATION = 'KHR_mesh_quantization'\n\nconst THREE_TO_WEBGL = {}\n\nTHREE_TO_WEBGL[NearestFilter] = WEBGL_CONSTANTS.NEAREST\nTHREE_TO_WEBGL[NearestMipmapNearestFilter] = WEBGL_CONSTANTS.NEAREST_MIPMAP_NEAREST\nTHREE_TO_WEBGL[NearestMipmapLinearFilter] = WEBGL_CONSTANTS.NEAREST_MIPMAP_LINEAR\nTHREE_TO_WEBGL[LinearFilter] = WEBGL_CONSTANTS.LINEAR\nTHREE_TO_WEBGL[LinearMipmapNearestFilter] = WEBGL_CONSTANTS.LINEAR_MIPMAP_NEAREST\nTHREE_TO_WEBGL[LinearMipmapLinearFilter] = WEBGL_CONSTANTS.LINEAR_MIPMAP_LINEAR\n\nTHREE_TO_WEBGL[ClampToEdgeWrapping] = WEBGL_CONSTANTS.CLAMP_TO_EDGE\nTHREE_TO_WEBGL[RepeatWrapping] = WEBGL_CONSTANTS.REPEAT\nTHREE_TO_WEBGL[MirroredRepeatWrapping] = WEBGL_CONSTANTS.MIRRORED_REPEAT\n\nconst PATH_PROPERTIES = {\n  scale: 'scale',\n  position: 'translation',\n  quaternion: 'rotation',\n  morphTargetInfluences: 'weights',\n}\n\nconst DEFAULT_SPECULAR_COLOR = /* @__PURE__ */ new Color()\n\n// GLB constants\n// https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#glb-file-format-specification\n\nconst GLB_HEADER_BYTES = 12\nconst GLB_HEADER_MAGIC = 0x46546c67\nconst GLB_VERSION = 2\n\nconst GLB_CHUNK_PREFIX_BYTES = 8\nconst GLB_CHUNK_TYPE_JSON = 0x4e4f534a\nconst GLB_CHUNK_TYPE_BIN = 0x004e4942\n\n//------------------------------------------------------------------------------\n// Utility functions\n//------------------------------------------------------------------------------\n\n/**\n * Compare two arrays\n * @param  {Array} array1 Array 1 to compare\n * @param  {Array} array2 Array 2 to compare\n * @return {Boolean}        Returns true if both arrays are equal\n */\nfunction equalArray(array1, array2) {\n  return (\n    array1.length === array2.length &&\n    array1.every(function (element, index) {\n      return element === array2[index]\n    })\n  )\n}\n\n/**\n * Converts a string to an ArrayBuffer.\n * @param  {string} text\n * @return {ArrayBuffer}\n */\nfunction stringToArrayBuffer(text) {\n  return new TextEncoder().encode(text).buffer\n}\n\n/**\n * Is identity matrix\n *\n * @param {Matrix4} matrix\n * @returns {Boolean} Returns true, if parameter is identity matrix\n */\nfunction isIdentityMatrix(matrix) {\n  return equalArray(matrix.elements, [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1])\n}\n\n/**\n * Get the min and max vectors from the given attribute\n * @param  {BufferAttribute} attribute Attribute to find the min/max in range from start to start + count\n * @param  {Integer} start\n * @param  {Integer} count\n * @return {Object} Object containing the `min` and `max` values (As an array of attribute.itemSize components)\n */\nfunction getMinMax(attribute, start, count) {\n  const output = {\n    min: new Array(attribute.itemSize).fill(Number.POSITIVE_INFINITY),\n    max: new Array(attribute.itemSize).fill(Number.NEGATIVE_INFINITY),\n  }\n\n  for (let i = start; i < start + count; i++) {\n    for (let a = 0; a < attribute.itemSize; a++) {\n      let value\n\n      if (attribute.itemSize > 4) {\n        // no support for interleaved data for itemSize > 4\n\n        value = attribute.array[i * attribute.itemSize + a]\n      } else {\n        if (a === 0) value = attribute.getX(i)\n        else if (a === 1) value = attribute.getY(i)\n        else if (a === 2) value = attribute.getZ(i)\n        else if (a === 3) value = attribute.getW(i)\n\n        if (attribute.normalized === true) {\n          value = MathUtils.normalize(value, attribute.array)\n        }\n      }\n\n      output.min[a] = Math.min(output.min[a], value)\n      output.max[a] = Math.max(output.max[a], value)\n    }\n  }\n\n  return output\n}\n\n/**\n * Get the required size + padding for a buffer, rounded to the next 4-byte boundary.\n * https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#data-alignment\n *\n * @param {Integer} bufferSize The size the original buffer.\n * @returns {Integer} new buffer size with required padding.\n *\n */\nfunction getPaddedBufferSize(bufferSize) {\n  return Math.ceil(bufferSize / 4) * 4\n}\n\n/**\n * Returns a buffer aligned to 4-byte boundary.\n *\n * @param {ArrayBuffer} arrayBuffer Buffer to pad\n * @param {Integer} paddingByte (Optional)\n * @returns {ArrayBuffer} The same buffer if it's already aligned to 4-byte boundary or a new buffer\n */\nfunction getPaddedArrayBuffer(arrayBuffer, paddingByte = 0) {\n  const paddedLength = getPaddedBufferSize(arrayBuffer.byteLength)\n\n  if (paddedLength !== arrayBuffer.byteLength) {\n    const array = new Uint8Array(paddedLength)\n    array.set(new Uint8Array(arrayBuffer))\n\n    if (paddingByte !== 0) {\n      for (let i = arrayBuffer.byteLength; i < paddedLength; i++) {\n        array[i] = paddingByte\n      }\n    }\n\n    return array.buffer\n  }\n\n  return arrayBuffer\n}\n\nfunction getCanvas() {\n  if (typeof document === 'undefined' && typeof OffscreenCanvas !== 'undefined') {\n    return new OffscreenCanvas(1, 1)\n  }\n\n  return document.createElement('canvas')\n}\n\nfunction getToBlobPromise(canvas, mimeType) {\n  if (canvas.toBlob !== undefined) {\n    return new Promise((resolve) => canvas.toBlob(resolve, mimeType))\n  }\n\n  let quality\n\n  // Blink's implementation of convertToBlob seems to default to a quality level of 100%\n  // Use the Blink default quality levels of toBlob instead so that file sizes are comparable.\n  if (mimeType === 'image/jpeg') {\n    quality = 0.92\n  } else if (mimeType === 'image/webp') {\n    quality = 0.8\n  }\n\n  return canvas.convertToBlob({\n    type: mimeType,\n    quality: quality,\n  })\n}\n\n/**\n * Writer\n */\nclass GLTFWriter {\n  constructor() {\n    this.plugins = []\n\n    this.options = {}\n    this.pending = []\n    this.buffers = []\n\n    this.byteOffset = 0\n    this.buffers = []\n    this.nodeMap = new Map()\n    this.skins = []\n\n    this.extensionsUsed = {}\n    this.extensionsRequired = {}\n\n    this.uids = new Map()\n    this.uid = 0\n\n    this.json = {\n      asset: {\n        version: '2.0',\n        generator: 'THREE.GLTFExporter',\n      },\n    }\n\n    this.cache = {\n      meshes: new Map(),\n      attributes: new Map(),\n      attributesNormalized: new Map(),\n      materials: new Map(),\n      textures: new Map(),\n      images: new Map(),\n    }\n  }\n\n  setPlugins(plugins) {\n    this.plugins = plugins\n  }\n\n  /**\n   * Parse scenes and generate GLTF output\n   * @param  {Scene or [THREE.Scenes]} input   Scene or Array of THREE.Scenes\n   * @param  {Function} onDone  Callback on completed\n   * @param  {Object} options options\n   */\n  async write(input, onDone, options = {}) {\n    this.options = Object.assign(\n      {\n        // default options\n        binary: false,\n        trs: false,\n        onlyVisible: true,\n        maxTextureSize: Infinity,\n        animations: [],\n        includeCustomExtensions: false,\n      },\n      options,\n    )\n\n    if (this.options.animations.length > 0) {\n      // Only TRS properties, and not matrices, may be targeted by animation.\n      this.options.trs = true\n    }\n\n    this.processInput(input)\n\n    await Promise.all(this.pending)\n\n    const writer = this\n    const buffers = writer.buffers\n    const json = writer.json\n    options = writer.options\n\n    const extensionsUsed = writer.extensionsUsed\n    const extensionsRequired = writer.extensionsRequired\n\n    // Merge buffers.\n    const blob = new Blob(buffers, { type: 'application/octet-stream' })\n\n    // Declare extensions.\n    const extensionsUsedList = Object.keys(extensionsUsed)\n    const extensionsRequiredList = Object.keys(extensionsRequired)\n\n    if (extensionsUsedList.length > 0) json.extensionsUsed = extensionsUsedList\n    if (extensionsRequiredList.length > 0) json.extensionsRequired = extensionsRequiredList\n\n    // Update bytelength of the single buffer.\n    if (json.buffers && json.buffers.length > 0) json.buffers[0].byteLength = blob.size\n\n    if (options.binary === true) {\n      // https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#glb-file-format-specification\n\n      blob.arrayBuffer().then((result) => {\n        // Binary chunk.\n        const binaryChunk = getPaddedArrayBuffer(result)\n        const binaryChunkPrefix = new DataView(new ArrayBuffer(GLB_CHUNK_PREFIX_BYTES))\n        binaryChunkPrefix.setUint32(0, binaryChunk.byteLength, true)\n        binaryChunkPrefix.setUint32(4, GLB_CHUNK_TYPE_BIN, true)\n\n        // JSON chunk.\n        const jsonChunk = getPaddedArrayBuffer(stringToArrayBuffer(JSON.stringify(json)), 0x20)\n        const jsonChunkPrefix = new DataView(new ArrayBuffer(GLB_CHUNK_PREFIX_BYTES))\n        jsonChunkPrefix.setUint32(0, jsonChunk.byteLength, true)\n        jsonChunkPrefix.setUint32(4, GLB_CHUNK_TYPE_JSON, true)\n\n        // GLB header.\n        const header = new ArrayBuffer(GLB_HEADER_BYTES)\n        const headerView = new DataView(header)\n        headerView.setUint32(0, GLB_HEADER_MAGIC, true)\n        headerView.setUint32(4, GLB_VERSION, true)\n        const totalByteLength =\n          GLB_HEADER_BYTES +\n          jsonChunkPrefix.byteLength +\n          jsonChunk.byteLength +\n          binaryChunkPrefix.byteLength +\n          binaryChunk.byteLength\n        headerView.setUint32(8, totalByteLength, true)\n\n        const glbBlob = new Blob([header, jsonChunkPrefix, jsonChunk, binaryChunkPrefix, binaryChunk], {\n          type: 'application/octet-stream',\n        })\n\n        glbBlob.arrayBuffer().then(onDone)\n      })\n    } else {\n      if (json.buffers && json.buffers.length > 0) {\n        readAsDataURL(blob).then((uri) => {\n          json.buffers[0].uri = uri\n          onDone(json)\n        })\n      } else {\n        onDone(json)\n      }\n    }\n  }\n\n  /**\n   * Serializes a userData.\n   *\n   * @param {THREE.Object3D|THREE.Material} object\n   * @param {Object} objectDef\n   */\n  serializeUserData(object, objectDef) {\n    if (Object.keys(object.userData).length === 0) return\n\n    const options = this.options\n    const extensionsUsed = this.extensionsUsed\n\n    try {\n      const json = JSON.parse(JSON.stringify(object.userData))\n\n      if (options.includeCustomExtensions && json.gltfExtensions) {\n        if (objectDef.extensions === undefined) objectDef.extensions = {}\n\n        for (const extensionName in json.gltfExtensions) {\n          objectDef.extensions[extensionName] = json.gltfExtensions[extensionName]\n          extensionsUsed[extensionName] = true\n        }\n\n        delete json.gltfExtensions\n      }\n\n      if (Object.keys(json).length > 0) objectDef.extras = json\n    } catch (error) {\n      console.warn(\n        \"THREE.GLTFExporter: userData of '\" +\n          object.name +\n          \"' \" +\n          \"won't be serialized because of JSON.stringify error - \" +\n          error.message,\n      )\n    }\n  }\n\n  /**\n   * Returns ids for buffer attributes.\n   * @param  {Object} object\n   * @return {Integer}\n   */\n  getUID(attribute, isRelativeCopy = false) {\n    if (this.uids.has(attribute) === false) {\n      const uids = new Map()\n\n      uids.set(true, this.uid++)\n      uids.set(false, this.uid++)\n\n      this.uids.set(attribute, uids)\n    }\n\n    const uids = this.uids.get(attribute)\n\n    return uids.get(isRelativeCopy)\n  }\n\n  /**\n   * Checks if normal attribute values are normalized.\n   *\n   * @param {BufferAttribute} normal\n   * @returns {Boolean}\n   */\n  isNormalizedNormalAttribute(normal) {\n    const cache = this.cache\n\n    if (cache.attributesNormalized.has(normal)) return false\n\n    const v = new Vector3()\n\n    for (let i = 0, il = normal.count; i < il; i++) {\n      // 0.0005 is from glTF-validator\n      if (Math.abs(v.fromBufferAttribute(normal, i).length() - 1.0) > 0.0005) return false\n    }\n\n    return true\n  }\n\n  /**\n   * Creates normalized normal buffer attribute.\n   *\n   * @param {BufferAttribute} normal\n   * @returns {BufferAttribute}\n   *\n   */\n  createNormalizedNormalAttribute(normal) {\n    const cache = this.cache\n\n    if (cache.attributesNormalized.has(normal)) return cache.attributesNormalized.get(normal)\n\n    const attribute = normal.clone()\n    const v = new Vector3()\n\n    for (let i = 0, il = attribute.count; i < il; i++) {\n      v.fromBufferAttribute(attribute, i)\n\n      if (v.x === 0 && v.y === 0 && v.z === 0) {\n        // if values can't be normalized set (1, 0, 0)\n        v.setX(1.0)\n      } else {\n        v.normalize()\n      }\n\n      attribute.setXYZ(i, v.x, v.y, v.z)\n    }\n\n    cache.attributesNormalized.set(normal, attribute)\n\n    return attribute\n  }\n\n  /**\n   * Applies a texture transform, if present, to the map definition. Requires\n   * the KHR_texture_transform extension.\n   *\n   * @param {Object} mapDef\n   * @param {THREE.Texture} texture\n   */\n  applyTextureTransform(mapDef, texture) {\n    let didTransform = false\n    const transformDef = {}\n\n    if (texture.offset.x !== 0 || texture.offset.y !== 0) {\n      transformDef.offset = texture.offset.toArray()\n      didTransform = true\n    }\n\n    if (texture.rotation !== 0) {\n      transformDef.rotation = texture.rotation\n      didTransform = true\n    }\n\n    if (texture.repeat.x !== 1 || texture.repeat.y !== 1) {\n      transformDef.scale = texture.repeat.toArray()\n      didTransform = true\n    }\n\n    if (didTransform) {\n      mapDef.extensions = mapDef.extensions || {}\n      mapDef.extensions['KHR_texture_transform'] = transformDef\n      this.extensionsUsed['KHR_texture_transform'] = true\n    }\n  }\n\n  buildMetalRoughTexture(metalnessMap, roughnessMap) {\n    if (metalnessMap === roughnessMap) return metalnessMap\n\n    function getEncodingConversion(map) {\n      if ('colorSpace' in map ? map.colorSpace === 'srgb' : map.encoding === 3001) {\n        return function SRGBToLinear(c) {\n          return c < 0.04045 ? c * 0.0773993808 : Math.pow(c * 0.9478672986 + 0.0521327014, 2.4)\n        }\n      }\n\n      return function LinearToLinear(c) {\n        return c\n      }\n    }\n\n    console.warn('THREE.GLTFExporter: Merged metalnessMap and roughnessMap textures.')\n\n    if (metalnessMap instanceof CompressedTexture) {\n      metalnessMap = decompress(metalnessMap)\n    }\n\n    if (roughnessMap instanceof CompressedTexture) {\n      roughnessMap = decompress(roughnessMap)\n    }\n\n    const metalness = metalnessMap ? metalnessMap.image : null\n    const roughness = roughnessMap ? roughnessMap.image : null\n\n    const width = Math.max(metalness ? metalness.width : 0, roughness ? roughness.width : 0)\n    const height = Math.max(metalness ? metalness.height : 0, roughness ? roughness.height : 0)\n\n    const canvas = getCanvas()\n    canvas.width = width\n    canvas.height = height\n\n    const context = canvas.getContext('2d')\n    context.fillStyle = '#00ffff'\n    context.fillRect(0, 0, width, height)\n\n    const composite = context.getImageData(0, 0, width, height)\n\n    if (metalness) {\n      context.drawImage(metalness, 0, 0, width, height)\n\n      const convert = getEncodingConversion(metalnessMap)\n      const data = context.getImageData(0, 0, width, height).data\n\n      for (let i = 2; i < data.length; i += 4) {\n        composite.data[i] = convert(data[i] / 256) * 256\n      }\n    }\n\n    if (roughness) {\n      context.drawImage(roughness, 0, 0, width, height)\n\n      const convert = getEncodingConversion(roughnessMap)\n      const data = context.getImageData(0, 0, width, height).data\n\n      for (let i = 1; i < data.length; i += 4) {\n        composite.data[i] = convert(data[i] / 256) * 256\n      }\n    }\n\n    context.putImageData(composite, 0, 0)\n\n    //\n\n    const reference = metalnessMap || roughnessMap\n\n    const texture = reference.clone()\n\n    // TODO Use new Source() instead?\n    texture.source = new Texture(canvas).source\n    if ('colorSpace' in texture) texture.colorSpace = ''\n    else texture.encoding = 3000\n    texture.channel = (metalnessMap || roughnessMap).channel\n\n    if (metalnessMap && roughnessMap && metalnessMap.channel !== roughnessMap.channel) {\n      console.warn('THREE.GLTFExporter: UV channels for metalnessMap and roughnessMap textures must match.')\n    }\n\n    return texture\n  }\n\n  /**\n   * Process a buffer to append to the default one.\n   * @param  {ArrayBuffer} buffer\n   * @return {Integer}\n   */\n  processBuffer(buffer) {\n    const json = this.json\n    const buffers = this.buffers\n\n    if (!json.buffers) json.buffers = [{ byteLength: 0 }]\n\n    // All buffers are merged before export.\n    buffers.push(buffer)\n\n    return 0\n  }\n\n  /**\n   * Process and generate a BufferView\n   * @param  {BufferAttribute} attribute\n   * @param  {number} componentType\n   * @param  {number} start\n   * @param  {number} count\n   * @param  {number} target (Optional) Target usage of the BufferView\n   * @return {Object}\n   */\n  processBufferView(attribute, componentType, start, count, target) {\n    const json = this.json\n\n    if (!json.bufferViews) json.bufferViews = []\n\n    // Create a new dataview and dump the attribute's array into it\n\n    let componentSize\n\n    switch (componentType) {\n      case WEBGL_CONSTANTS.BYTE:\n      case WEBGL_CONSTANTS.UNSIGNED_BYTE:\n        componentSize = 1\n\n        break\n\n      case WEBGL_CONSTANTS.SHORT:\n      case WEBGL_CONSTANTS.UNSIGNED_SHORT:\n        componentSize = 2\n\n        break\n\n      default:\n        componentSize = 4\n    }\n\n    let byteStride = attribute.itemSize * componentSize\n    if (target === WEBGL_CONSTANTS.ARRAY_BUFFER) {\n      // Each element of a vertex attribute MUST be aligned to 4-byte boundaries\n      // inside a bufferView\n      byteStride = Math.ceil(byteStride / 4) * 4\n    }\n    const byteLength = getPaddedBufferSize(count * byteStride)\n    const dataView = new DataView(new ArrayBuffer(byteLength))\n    let offset = 0\n\n    for (let i = start; i < start + count; i++) {\n      for (let a = 0; a < attribute.itemSize; a++) {\n        let value\n\n        if (attribute.itemSize > 4) {\n          // no support for interleaved data for itemSize > 4\n\n          value = attribute.array[i * attribute.itemSize + a]\n        } else {\n          if (a === 0) value = attribute.getX(i)\n          else if (a === 1) value = attribute.getY(i)\n          else if (a === 2) value = attribute.getZ(i)\n          else if (a === 3) value = attribute.getW(i)\n\n          if (attribute.normalized === true) {\n            value = MathUtils.normalize(value, attribute.array)\n          }\n        }\n\n        if (componentType === WEBGL_CONSTANTS.FLOAT) {\n          dataView.setFloat32(offset, value, true)\n        } else if (componentType === WEBGL_CONSTANTS.INT) {\n          dataView.setInt32(offset, value, true)\n        } else if (componentType === WEBGL_CONSTANTS.UNSIGNED_INT) {\n          dataView.setUint32(offset, value, true)\n        } else if (componentType === WEBGL_CONSTANTS.SHORT) {\n          dataView.setInt16(offset, value, true)\n        } else if (componentType === WEBGL_CONSTANTS.UNSIGNED_SHORT) {\n          dataView.setUint16(offset, value, true)\n        } else if (componentType === WEBGL_CONSTANTS.BYTE) {\n          dataView.setInt8(offset, value)\n        } else if (componentType === WEBGL_CONSTANTS.UNSIGNED_BYTE) {\n          dataView.setUint8(offset, value)\n        }\n\n        offset += componentSize\n      }\n      if (offset % byteStride !== 0) {\n        offset += byteStride - (offset % byteStride)\n      }\n    }\n\n    const bufferViewDef = {\n      buffer: this.processBuffer(dataView.buffer),\n      byteOffset: this.byteOffset,\n      byteLength: byteLength,\n    }\n\n    if (target !== undefined) bufferViewDef.target = target\n\n    if (target === WEBGL_CONSTANTS.ARRAY_BUFFER) {\n      // Only define byteStride for vertex attributes.\n      bufferViewDef.byteStride = byteStride\n    }\n\n    this.byteOffset += byteLength\n\n    json.bufferViews.push(bufferViewDef)\n\n    // @TODO Merge bufferViews where possible.\n    const output = {\n      id: json.bufferViews.length - 1,\n      byteLength: 0,\n    }\n\n    return output\n  }\n\n  /**\n   * Process and generate a BufferView from an image Blob.\n   * @param {Blob} blob\n   * @return {Promise<Integer>}\n   */\n  processBufferViewImage(blob) {\n    const writer = this\n    const json = writer.json\n\n    if (!json.bufferViews) json.bufferViews = []\n\n    return blob.arrayBuffer().then((result) => {\n      const buffer = getPaddedArrayBuffer(result)\n\n      const bufferViewDef = {\n        buffer: writer.processBuffer(buffer),\n        byteOffset: writer.byteOffset,\n        byteLength: buffer.byteLength,\n      }\n\n      writer.byteOffset += buffer.byteLength\n      return json.bufferViews.push(bufferViewDef) - 1\n    })\n  }\n\n  /**\n   * Process attribute to generate an accessor\n   * @param  {BufferAttribute} attribute Attribute to process\n   * @param  {THREE.BufferGeometry} geometry (Optional) Geometry used for truncated draw range\n   * @param  {Integer} start (Optional)\n   * @param  {Integer} count (Optional)\n   * @return {Integer|null} Index of the processed accessor on the \"accessors\" array\n   */\n  processAccessor(attribute, geometry, start, count) {\n    const json = this.json\n\n    const types = {\n      1: 'SCALAR',\n      2: 'VEC2',\n      3: 'VEC3',\n      4: 'VEC4',\n      9: 'MAT3',\n      16: 'MAT4',\n    }\n\n    let componentType\n\n    // Detect the component type of the attribute array\n    if (attribute.array.constructor === Float32Array) {\n      componentType = WEBGL_CONSTANTS.FLOAT\n    } else if (attribute.array.constructor === Int32Array) {\n      componentType = WEBGL_CONSTANTS.INT\n    } else if (attribute.array.constructor === Uint32Array) {\n      componentType = WEBGL_CONSTANTS.UNSIGNED_INT\n    } else if (attribute.array.constructor === Int16Array) {\n      componentType = WEBGL_CONSTANTS.SHORT\n    } else if (attribute.array.constructor === Uint16Array) {\n      componentType = WEBGL_CONSTANTS.UNSIGNED_SHORT\n    } else if (attribute.array.constructor === Int8Array) {\n      componentType = WEBGL_CONSTANTS.BYTE\n    } else if (attribute.array.constructor === Uint8Array) {\n      componentType = WEBGL_CONSTANTS.UNSIGNED_BYTE\n    } else {\n      throw new Error(\n        'THREE.GLTFExporter: Unsupported bufferAttribute component type: ' + attribute.array.constructor.name,\n      )\n    }\n\n    if (start === undefined) start = 0\n    if (count === undefined) count = attribute.count\n\n    // Skip creating an accessor if the attribute doesn't have data to export\n    if (count === 0) return null\n\n    const minMax = getMinMax(attribute, start, count)\n    let bufferViewTarget\n\n    // If geometry isn't provided, don't infer the target usage of the bufferView. For\n    // animation samplers, target must not be set.\n    if (geometry !== undefined) {\n      bufferViewTarget =\n        attribute === geometry.index ? WEBGL_CONSTANTS.ELEMENT_ARRAY_BUFFER : WEBGL_CONSTANTS.ARRAY_BUFFER\n    }\n\n    const bufferView = this.processBufferView(attribute, componentType, start, count, bufferViewTarget)\n\n    const accessorDef = {\n      bufferView: bufferView.id,\n      byteOffset: bufferView.byteOffset,\n      componentType: componentType,\n      count: count,\n      max: minMax.max,\n      min: minMax.min,\n      type: types[attribute.itemSize],\n    }\n\n    if (attribute.normalized === true) accessorDef.normalized = true\n    if (!json.accessors) json.accessors = []\n\n    return json.accessors.push(accessorDef) - 1\n  }\n\n  /**\n   * Process image\n   * @param  {Image} image to process\n   * @param  {Integer} format of the image (RGBAFormat)\n   * @param  {Boolean} flipY before writing out the image\n   * @param  {String} mimeType export format\n   * @return {Integer}     Index of the processed texture in the \"images\" array\n   */\n  processImage(image, format, flipY, mimeType = 'image/png') {\n    if (image !== null) {\n      const writer = this\n      const cache = writer.cache\n      const json = writer.json\n      const options = writer.options\n      const pending = writer.pending\n\n      if (!cache.images.has(image)) cache.images.set(image, {})\n\n      const cachedImages = cache.images.get(image)\n\n      const key = mimeType + ':flipY/' + flipY.toString()\n\n      if (cachedImages[key] !== undefined) return cachedImages[key]\n\n      if (!json.images) json.images = []\n\n      const imageDef = { mimeType: mimeType }\n\n      const canvas = getCanvas()\n\n      canvas.width = Math.min(image.width, options.maxTextureSize)\n      canvas.height = Math.min(image.height, options.maxTextureSize)\n\n      const ctx = canvas.getContext('2d')\n\n      if (flipY === true) {\n        ctx.translate(0, canvas.height)\n        ctx.scale(1, -1)\n      }\n\n      if (image.data !== undefined) {\n        // THREE.DataTexture\n\n        if (format !== RGBAFormat) {\n          console.error('GLTFExporter: Only RGBAFormat is supported.', format)\n        }\n\n        if (image.width > options.maxTextureSize || image.height > options.maxTextureSize) {\n          console.warn('GLTFExporter: Image size is bigger than maxTextureSize', image)\n        }\n\n        const data = new Uint8ClampedArray(image.height * image.width * 4)\n\n        for (let i = 0; i < data.length; i += 4) {\n          data[i + 0] = image.data[i + 0]\n          data[i + 1] = image.data[i + 1]\n          data[i + 2] = image.data[i + 2]\n          data[i + 3] = image.data[i + 3]\n        }\n\n        ctx.putImageData(new ImageData(data, image.width, image.height), 0, 0)\n      } else {\n        ctx.drawImage(image, 0, 0, canvas.width, canvas.height)\n      }\n\n      if (options.binary === true) {\n        pending.push(\n          getToBlobPromise(canvas, mimeType)\n            .then((blob) => writer.processBufferViewImage(blob))\n            .then((bufferViewIndex) => {\n              imageDef.bufferView = bufferViewIndex\n            }),\n        )\n      } else {\n        if (canvas.toDataURL !== undefined) {\n          imageDef.uri = canvas.toDataURL(mimeType)\n        } else {\n          pending.push(\n            getToBlobPromise(canvas, mimeType)\n              .then(readAsDataURL)\n              .then((uri) => {\n                imageDef.uri = uri\n              }),\n          )\n        }\n      }\n\n      const index = json.images.push(imageDef) - 1\n      cachedImages[key] = index\n      return index\n    } else {\n      throw new Error('THREE.GLTFExporter: No valid image data found. Unable to process texture.')\n    }\n  }\n\n  /**\n   * Process sampler\n   * @param  {Texture} map Texture to process\n   * @return {Integer}     Index of the processed texture in the \"samplers\" array\n   */\n  processSampler(map) {\n    const json = this.json\n\n    if (!json.samplers) json.samplers = []\n\n    const samplerDef = {\n      magFilter: THREE_TO_WEBGL[map.magFilter],\n      minFilter: THREE_TO_WEBGL[map.minFilter],\n      wrapS: THREE_TO_WEBGL[map.wrapS],\n      wrapT: THREE_TO_WEBGL[map.wrapT],\n    }\n\n    return json.samplers.push(samplerDef) - 1\n  }\n\n  /**\n   * Process texture\n   * @param  {Texture} map Map to process\n   * @return {Integer} Index of the processed texture in the \"textures\" array\n   */\n  processTexture(map) {\n    const writer = this\n    const options = writer.options\n    const cache = this.cache\n    const json = this.json\n\n    if (cache.textures.has(map)) return cache.textures.get(map)\n\n    if (!json.textures) json.textures = []\n\n    // make non-readable textures (e.g. CompressedTexture) readable by blitting them into a new texture\n    if (map instanceof CompressedTexture) {\n      map = decompress(map, options.maxTextureSize)\n    }\n\n    let mimeType = map.userData.mimeType\n\n    if (mimeType === 'image/webp') mimeType = 'image/png'\n\n    const textureDef = {\n      sampler: this.processSampler(map),\n      source: this.processImage(map.image, map.format, map.flipY, mimeType),\n    }\n\n    if (map.name) textureDef.name = map.name\n\n    this._invokeAll(function (ext) {\n      ext.writeTexture && ext.writeTexture(map, textureDef)\n    })\n\n    const index = json.textures.push(textureDef) - 1\n    cache.textures.set(map, index)\n    return index\n  }\n\n  /**\n   * Process material\n   * @param  {THREE.Material} material Material to process\n   * @return {Integer|null} Index of the processed material in the \"materials\" array\n   */\n  processMaterial(material) {\n    const cache = this.cache\n    const json = this.json\n\n    if (cache.materials.has(material)) return cache.materials.get(material)\n\n    if (material.isShaderMaterial) {\n      console.warn('GLTFExporter: THREE.ShaderMaterial not supported.')\n      return null\n    }\n\n    if (!json.materials) json.materials = []\n\n    // @QUESTION Should we avoid including any attribute that has the default value?\n    const materialDef = { pbrMetallicRoughness: {} }\n\n    if (material.isMeshStandardMaterial !== true && material.isMeshBasicMaterial !== true) {\n      console.warn('GLTFExporter: Use MeshStandardMaterial or MeshBasicMaterial for best results.')\n    }\n\n    // pbrMetallicRoughness.baseColorFactor\n    const color = material.color.toArray().concat([material.opacity])\n\n    if (!equalArray(color, [1, 1, 1, 1])) {\n      materialDef.pbrMetallicRoughness.baseColorFactor = color\n    }\n\n    if (material.isMeshStandardMaterial) {\n      materialDef.pbrMetallicRoughness.metallicFactor = material.metalness\n      materialDef.pbrMetallicRoughness.roughnessFactor = material.roughness\n    } else {\n      materialDef.pbrMetallicRoughness.metallicFactor = 0.5\n      materialDef.pbrMetallicRoughness.roughnessFactor = 0.5\n    }\n\n    // pbrMetallicRoughness.metallicRoughnessTexture\n    if (material.metalnessMap || material.roughnessMap) {\n      const metalRoughTexture = this.buildMetalRoughTexture(material.metalnessMap, material.roughnessMap)\n\n      const metalRoughMapDef = {\n        index: this.processTexture(metalRoughTexture),\n        channel: metalRoughTexture.channel,\n      }\n      this.applyTextureTransform(metalRoughMapDef, metalRoughTexture)\n      materialDef.pbrMetallicRoughness.metallicRoughnessTexture = metalRoughMapDef\n    }\n\n    // pbrMetallicRoughness.baseColorTexture\n    if (material.map) {\n      const baseColorMapDef = {\n        index: this.processTexture(material.map),\n        texCoord: material.map.channel,\n      }\n      this.applyTextureTransform(baseColorMapDef, material.map)\n      materialDef.pbrMetallicRoughness.baseColorTexture = baseColorMapDef\n    }\n\n    if (material.emissive) {\n      const emissive = material.emissive\n      const maxEmissiveComponent = Math.max(emissive.r, emissive.g, emissive.b)\n\n      if (maxEmissiveComponent > 0) {\n        materialDef.emissiveFactor = material.emissive.toArray()\n      }\n\n      // emissiveTexture\n      if (material.emissiveMap) {\n        const emissiveMapDef = {\n          index: this.processTexture(material.emissiveMap),\n          texCoord: material.emissiveMap.channel,\n        }\n        this.applyTextureTransform(emissiveMapDef, material.emissiveMap)\n        materialDef.emissiveTexture = emissiveMapDef\n      }\n    }\n\n    // normalTexture\n    if (material.normalMap) {\n      const normalMapDef = {\n        index: this.processTexture(material.normalMap),\n        texCoord: material.normalMap.channel,\n      }\n\n      if (material.normalScale && material.normalScale.x !== 1) {\n        // glTF normal scale is univariate. Ignore `y`, which may be flipped.\n        // Context: https://github.com/mrdoob/three.js/issues/11438#issuecomment-507003995\n        normalMapDef.scale = material.normalScale.x\n      }\n\n      this.applyTextureTransform(normalMapDef, material.normalMap)\n      materialDef.normalTexture = normalMapDef\n    }\n\n    // occlusionTexture\n    if (material.aoMap) {\n      const occlusionMapDef = {\n        index: this.processTexture(material.aoMap),\n        texCoord: material.aoMap.channel,\n      }\n\n      if (material.aoMapIntensity !== 1.0) {\n        occlusionMapDef.strength = material.aoMapIntensity\n      }\n\n      this.applyTextureTransform(occlusionMapDef, material.aoMap)\n      materialDef.occlusionTexture = occlusionMapDef\n    }\n\n    // alphaMode\n    if (material.transparent) {\n      materialDef.alphaMode = 'BLEND'\n    } else {\n      if (material.alphaTest > 0.0) {\n        materialDef.alphaMode = 'MASK'\n        materialDef.alphaCutoff = material.alphaTest\n      }\n    }\n\n    // doubleSided\n    if (material.side === DoubleSide) materialDef.doubleSided = true\n    if (material.name !== '') materialDef.name = material.name\n\n    this.serializeUserData(material, materialDef)\n\n    this._invokeAll(function (ext) {\n      ext.writeMaterial && ext.writeMaterial(material, materialDef)\n    })\n\n    const index = json.materials.push(materialDef) - 1\n    cache.materials.set(material, index)\n    return index\n  }\n\n  /**\n   * Process mesh\n   * @param  {THREE.Mesh} mesh Mesh to process\n   * @return {Integer|null} Index of the processed mesh in the \"meshes\" array\n   */\n  processMesh(mesh) {\n    const cache = this.cache\n    const json = this.json\n\n    const meshCacheKeyParts = [mesh.geometry.uuid]\n\n    if (Array.isArray(mesh.material)) {\n      for (let i = 0, l = mesh.material.length; i < l; i++) {\n        meshCacheKeyParts.push(mesh.material[i].uuid)\n      }\n    } else {\n      meshCacheKeyParts.push(mesh.material.uuid)\n    }\n\n    const meshCacheKey = meshCacheKeyParts.join(':')\n\n    if (cache.meshes.has(meshCacheKey)) return cache.meshes.get(meshCacheKey)\n\n    const geometry = mesh.geometry\n\n    let mode\n\n    // Use the correct mode\n    if (mesh.isLineSegments) {\n      mode = WEBGL_CONSTANTS.LINES\n    } else if (mesh.isLineLoop) {\n      mode = WEBGL_CONSTANTS.LINE_LOOP\n    } else if (mesh.isLine) {\n      mode = WEBGL_CONSTANTS.LINE_STRIP\n    } else if (mesh.isPoints) {\n      mode = WEBGL_CONSTANTS.POINTS\n    } else {\n      mode = mesh.material.wireframe ? WEBGL_CONSTANTS.LINES : WEBGL_CONSTANTS.TRIANGLES\n    }\n\n    const meshDef = {}\n    const attributes = {}\n    const primitives = []\n    const targets = []\n\n    // Conversion between attributes names in threejs and gltf spec\n    const nameConversion = {\n      ...(version >= 152\n        ? {\n            uv: 'TEXCOORD_0',\n            uv1: 'TEXCOORD_1',\n            uv2: 'TEXCOORD_2',\n            uv3: 'TEXCOORD_3',\n          }\n        : {\n            uv: 'TEXCOORD_0',\n            uv2: 'TEXCOORD_1',\n          }),\n      color: 'COLOR_0',\n      skinWeight: 'WEIGHTS_0',\n      skinIndex: 'JOINTS_0',\n    }\n\n    const originalNormal = geometry.getAttribute('normal')\n\n    if (originalNormal !== undefined && !this.isNormalizedNormalAttribute(originalNormal)) {\n      console.warn('THREE.GLTFExporter: Creating normalized normal attribute from the non-normalized one.')\n\n      geometry.setAttribute('normal', this.createNormalizedNormalAttribute(originalNormal))\n    }\n\n    // @QUESTION Detect if .vertexColors = true?\n    // For every attribute create an accessor\n    let modifiedAttribute = null\n\n    for (let attributeName in geometry.attributes) {\n      // Ignore morph target attributes, which are exported later.\n      if (attributeName.slice(0, 5) === 'morph') continue\n\n      const attribute = geometry.attributes[attributeName]\n      attributeName = nameConversion[attributeName] || attributeName.toUpperCase()\n\n      // Prefix all geometry attributes except the ones specifically\n      // listed in the spec; non-spec attributes are considered custom.\n      const validVertexAttributes = /^(POSITION|NORMAL|TANGENT|TEXCOORD_\\d+|COLOR_\\d+|JOINTS_\\d+|WEIGHTS_\\d+)$/\n\n      if (!validVertexAttributes.test(attributeName)) attributeName = '_' + attributeName\n\n      if (cache.attributes.has(this.getUID(attribute))) {\n        attributes[attributeName] = cache.attributes.get(this.getUID(attribute))\n        continue\n      }\n\n      // JOINTS_0 must be UNSIGNED_BYTE or UNSIGNED_SHORT.\n      modifiedAttribute = null\n      const array = attribute.array\n\n      if (attributeName === 'JOINTS_0' && !(array instanceof Uint16Array) && !(array instanceof Uint8Array)) {\n        console.warn('GLTFExporter: Attribute \"skinIndex\" converted to type UNSIGNED_SHORT.')\n        modifiedAttribute = new BufferAttribute(new Uint16Array(array), attribute.itemSize, attribute.normalized)\n      }\n\n      const accessor = this.processAccessor(modifiedAttribute || attribute, geometry)\n\n      if (accessor !== null) {\n        if (!attributeName.startsWith('_')) {\n          this.detectMeshQuantization(attributeName, attribute)\n        }\n\n        attributes[attributeName] = accessor\n        cache.attributes.set(this.getUID(attribute), accessor)\n      }\n    }\n\n    if (originalNormal !== undefined) geometry.setAttribute('normal', originalNormal)\n\n    // Skip if no exportable attributes found\n    if (Object.keys(attributes).length === 0) return null\n\n    // Morph targets\n    if (mesh.morphTargetInfluences !== undefined && mesh.morphTargetInfluences.length > 0) {\n      const weights = []\n      const targetNames = []\n      const reverseDictionary = {}\n\n      if (mesh.morphTargetDictionary !== undefined) {\n        for (const key in mesh.morphTargetDictionary) {\n          reverseDictionary[mesh.morphTargetDictionary[key]] = key\n        }\n      }\n\n      for (let i = 0; i < mesh.morphTargetInfluences.length; ++i) {\n        const target = {}\n        let warned = false\n\n        for (const attributeName in geometry.morphAttributes) {\n          // glTF 2.0 morph supports only POSITION/NORMAL/TANGENT.\n          // Three.js doesn't support TANGENT yet.\n\n          if (attributeName !== 'position' && attributeName !== 'normal') {\n            if (!warned) {\n              console.warn('GLTFExporter: Only POSITION and NORMAL morph are supported.')\n              warned = true\n            }\n\n            continue\n          }\n\n          const attribute = geometry.morphAttributes[attributeName][i]\n          const gltfAttributeName = attributeName.toUpperCase()\n\n          // Three.js morph attribute has absolute values while the one of glTF has relative values.\n          //\n          // glTF 2.0 Specification:\n          // https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#morph-targets\n\n          const baseAttribute = geometry.attributes[attributeName]\n\n          if (cache.attributes.has(this.getUID(attribute, true))) {\n            target[gltfAttributeName] = cache.attributes.get(this.getUID(attribute, true))\n            continue\n          }\n\n          // Clones attribute not to override\n          const relativeAttribute = attribute.clone()\n\n          if (!geometry.morphTargetsRelative) {\n            for (let j = 0, jl = attribute.count; j < jl; j++) {\n              for (let a = 0; a < attribute.itemSize; a++) {\n                if (a === 0) relativeAttribute.setX(j, attribute.getX(j) - baseAttribute.getX(j))\n                if (a === 1) relativeAttribute.setY(j, attribute.getY(j) - baseAttribute.getY(j))\n                if (a === 2) relativeAttribute.setZ(j, attribute.getZ(j) - baseAttribute.getZ(j))\n                if (a === 3) relativeAttribute.setW(j, attribute.getW(j) - baseAttribute.getW(j))\n              }\n            }\n          }\n\n          target[gltfAttributeName] = this.processAccessor(relativeAttribute, geometry)\n          cache.attributes.set(this.getUID(baseAttribute, true), target[gltfAttributeName])\n        }\n\n        targets.push(target)\n\n        weights.push(mesh.morphTargetInfluences[i])\n\n        if (mesh.morphTargetDictionary !== undefined) targetNames.push(reverseDictionary[i])\n      }\n\n      meshDef.weights = weights\n\n      if (targetNames.length > 0) {\n        meshDef.extras = {}\n        meshDef.extras.targetNames = targetNames\n      }\n    }\n\n    const isMultiMaterial = Array.isArray(mesh.material)\n\n    if (isMultiMaterial && geometry.groups.length === 0) return null\n\n    const materials = isMultiMaterial ? mesh.material : [mesh.material]\n    const groups = isMultiMaterial ? geometry.groups : [{ materialIndex: 0, start: undefined, count: undefined }]\n\n    for (let i = 0, il = groups.length; i < il; i++) {\n      const primitive = {\n        mode: mode,\n        attributes: attributes,\n      }\n\n      this.serializeUserData(geometry, primitive)\n\n      if (targets.length > 0) primitive.targets = targets\n\n      if (geometry.index !== null) {\n        let cacheKey = this.getUID(geometry.index)\n\n        if (groups[i].start !== undefined || groups[i].count !== undefined) {\n          cacheKey += ':' + groups[i].start + ':' + groups[i].count\n        }\n\n        if (cache.attributes.has(cacheKey)) {\n          primitive.indices = cache.attributes.get(cacheKey)\n        } else {\n          primitive.indices = this.processAccessor(geometry.index, geometry, groups[i].start, groups[i].count)\n          cache.attributes.set(cacheKey, primitive.indices)\n        }\n\n        if (primitive.indices === null) delete primitive.indices\n      }\n\n      const material = this.processMaterial(materials[groups[i].materialIndex])\n\n      if (material !== null) primitive.material = material\n\n      primitives.push(primitive)\n    }\n\n    meshDef.primitives = primitives\n\n    if (!json.meshes) json.meshes = []\n\n    this._invokeAll(function (ext) {\n      ext.writeMesh && ext.writeMesh(mesh, meshDef)\n    })\n\n    const index = json.meshes.push(meshDef) - 1\n    cache.meshes.set(meshCacheKey, index)\n    return index\n  }\n\n  /**\n   * If a vertex attribute with a\n   * [non-standard data type](https://registry.khronos.org/glTF/specs/2.0/glTF-2.0.html#meshes-overview)\n   * is used, it is checked whether it is a valid data type according to the\n   * [KHR_mesh_quantization](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_mesh_quantization/README.md)\n   * extension.\n   * In this case the extension is automatically added to the list of used extensions.\n   *\n   * @param {string} attributeName\n   * @param {THREE.BufferAttribute} attribute\n   */\n  detectMeshQuantization(attributeName, attribute) {\n    if (this.extensionsUsed[KHR_MESH_QUANTIZATION]) return\n\n    let attrType = undefined\n\n    switch (attribute.array.constructor) {\n      case Int8Array:\n        attrType = 'byte'\n\n        break\n\n      case Uint8Array:\n        attrType = 'unsigned byte'\n\n        break\n\n      case Int16Array:\n        attrType = 'short'\n\n        break\n\n      case Uint16Array:\n        attrType = 'unsigned short'\n\n        break\n\n      default:\n        return\n    }\n\n    if (attribute.normalized) attrType += ' normalized'\n\n    const attrNamePrefix = attributeName.split('_', 1)[0]\n\n    if (\n      KHR_mesh_quantization_ExtraAttrTypes[attrNamePrefix] &&\n      KHR_mesh_quantization_ExtraAttrTypes[attrNamePrefix].includes(attrType)\n    ) {\n      this.extensionsUsed[KHR_MESH_QUANTIZATION] = true\n      this.extensionsRequired[KHR_MESH_QUANTIZATION] = true\n    }\n  }\n\n  /**\n   * Process camera\n   * @param  {THREE.Camera} camera Camera to process\n   * @return {Integer}      Index of the processed mesh in the \"camera\" array\n   */\n  processCamera(camera) {\n    const json = this.json\n\n    if (!json.cameras) json.cameras = []\n\n    const isOrtho = camera.isOrthographicCamera\n\n    const cameraDef = {\n      type: isOrtho ? 'orthographic' : 'perspective',\n    }\n\n    if (isOrtho) {\n      cameraDef.orthographic = {\n        xmag: camera.right * 2,\n        ymag: camera.top * 2,\n        zfar: camera.far <= 0 ? 0.001 : camera.far,\n        znear: camera.near < 0 ? 0 : camera.near,\n      }\n    } else {\n      cameraDef.perspective = {\n        aspectRatio: camera.aspect,\n        yfov: MathUtils.degToRad(camera.fov),\n        zfar: camera.far <= 0 ? 0.001 : camera.far,\n        znear: camera.near < 0 ? 0 : camera.near,\n      }\n    }\n\n    // Question: Is saving \"type\" as name intentional?\n    if (camera.name !== '') cameraDef.name = camera.type\n\n    return json.cameras.push(cameraDef) - 1\n  }\n\n  /**\n   * Creates glTF animation entry from AnimationClip object.\n   *\n   * Status:\n   * - Only properties listed in PATH_PROPERTIES may be animated.\n   *\n   * @param {THREE.AnimationClip} clip\n   * @param {THREE.Object3D} root\n   * @return {number|null}\n   */\n  processAnimation(clip, root) {\n    const json = this.json\n    const nodeMap = this.nodeMap\n\n    if (!json.animations) json.animations = []\n\n    clip = GLTFExporter.Utils.mergeMorphTargetTracks(clip.clone(), root)\n\n    const tracks = clip.tracks\n    const channels = []\n    const samplers = []\n\n    for (let i = 0; i < tracks.length; ++i) {\n      const track = tracks[i]\n      const trackBinding = PropertyBinding.parseTrackName(track.name)\n      let trackNode = PropertyBinding.findNode(root, trackBinding.nodeName)\n      const trackProperty = PATH_PROPERTIES[trackBinding.propertyName]\n\n      if (trackBinding.objectName === 'bones') {\n        if (trackNode.isSkinnedMesh === true) {\n          trackNode = trackNode.skeleton.getBoneByName(trackBinding.objectIndex)\n        } else {\n          trackNode = undefined\n        }\n      }\n\n      if (!trackNode || !trackProperty) {\n        console.warn('THREE.GLTFExporter: Could not export animation track \"%s\".', track.name)\n        return null\n      }\n\n      const inputItemSize = 1\n      let outputItemSize = track.values.length / track.times.length\n\n      if (trackProperty === PATH_PROPERTIES.morphTargetInfluences) {\n        outputItemSize /= trackNode.morphTargetInfluences.length\n      }\n\n      let interpolation\n\n      // @TODO export CubicInterpolant(InterpolateSmooth) as CUBICSPLINE\n\n      // Detecting glTF cubic spline interpolant by checking factory method's special property\n      // GLTFCubicSplineInterpolant is a custom interpolant and track doesn't return\n      // valid value from .getInterpolation().\n      if (track.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline === true) {\n        interpolation = 'CUBICSPLINE'\n\n        // itemSize of CUBICSPLINE keyframe is 9\n        // (VEC3 * 3: inTangent, splineVertex, and outTangent)\n        // but needs to be stored as VEC3 so dividing by 3 here.\n        outputItemSize /= 3\n      } else if (track.getInterpolation() === InterpolateDiscrete) {\n        interpolation = 'STEP'\n      } else {\n        interpolation = 'LINEAR'\n      }\n\n      samplers.push({\n        input: this.processAccessor(new BufferAttribute(track.times, inputItemSize)),\n        output: this.processAccessor(new BufferAttribute(track.values, outputItemSize)),\n        interpolation: interpolation,\n      })\n\n      channels.push({\n        sampler: samplers.length - 1,\n        target: {\n          node: nodeMap.get(trackNode),\n          path: trackProperty,\n        },\n      })\n    }\n\n    json.animations.push({\n      name: clip.name || 'clip_' + json.animations.length,\n      samplers: samplers,\n      channels: channels,\n    })\n\n    return json.animations.length - 1\n  }\n\n  /**\n   * @param {THREE.Object3D} object\n   * @return {number|null}\n   */\n  processSkin(object) {\n    const json = this.json\n    const nodeMap = this.nodeMap\n\n    const node = json.nodes[nodeMap.get(object)]\n\n    const skeleton = object.skeleton\n\n    if (skeleton === undefined) return null\n\n    const rootJoint = object.skeleton.bones[0]\n\n    if (rootJoint === undefined) return null\n\n    const joints = []\n    const inverseBindMatrices = new Float32Array(skeleton.bones.length * 16)\n    const temporaryBoneInverse = new Matrix4()\n\n    for (let i = 0; i < skeleton.bones.length; ++i) {\n      joints.push(nodeMap.get(skeleton.bones[i]))\n      temporaryBoneInverse.copy(skeleton.boneInverses[i])\n      temporaryBoneInverse.multiply(object.bindMatrix).toArray(inverseBindMatrices, i * 16)\n    }\n\n    if (json.skins === undefined) json.skins = []\n\n    json.skins.push({\n      inverseBindMatrices: this.processAccessor(new BufferAttribute(inverseBindMatrices, 16)),\n      joints: joints,\n      skeleton: nodeMap.get(rootJoint),\n    })\n\n    const skinIndex = (node.skin = json.skins.length - 1)\n\n    return skinIndex\n  }\n\n  /**\n   * Process Object3D node\n   * @param  {THREE.Object3D} node Object3D to processNode\n   * @return {Integer} Index of the node in the nodes list\n   */\n  processNode(object) {\n    const json = this.json\n    const options = this.options\n    const nodeMap = this.nodeMap\n\n    if (!json.nodes) json.nodes = []\n\n    const nodeDef = {}\n\n    if (options.trs) {\n      const rotation = object.quaternion.toArray()\n      const position = object.position.toArray()\n      const scale = object.scale.toArray()\n\n      if (!equalArray(rotation, [0, 0, 0, 1])) {\n        nodeDef.rotation = rotation\n      }\n\n      if (!equalArray(position, [0, 0, 0])) {\n        nodeDef.translation = position\n      }\n\n      if (!equalArray(scale, [1, 1, 1])) {\n        nodeDef.scale = scale\n      }\n    } else {\n      if (object.matrixAutoUpdate) {\n        object.updateMatrix()\n      }\n\n      if (isIdentityMatrix(object.matrix) === false) {\n        nodeDef.matrix = object.matrix.elements\n      }\n    }\n\n    // We don't export empty strings name because it represents no-name in Three.js.\n    if (object.name !== '') nodeDef.name = String(object.name)\n\n    this.serializeUserData(object, nodeDef)\n\n    if (object.isMesh || object.isLine || object.isPoints) {\n      const meshIndex = this.processMesh(object)\n\n      if (meshIndex !== null) nodeDef.mesh = meshIndex\n    } else if (object.isCamera) {\n      nodeDef.camera = this.processCamera(object)\n    }\n\n    if (object.isSkinnedMesh) this.skins.push(object)\n\n    if (object.children.length > 0) {\n      const children = []\n\n      for (let i = 0, l = object.children.length; i < l; i++) {\n        const child = object.children[i]\n\n        if (child.visible || options.onlyVisible === false) {\n          const nodeIndex = this.processNode(child)\n\n          if (nodeIndex !== null) children.push(nodeIndex)\n        }\n      }\n\n      if (children.length > 0) nodeDef.children = children\n    }\n\n    this._invokeAll(function (ext) {\n      ext.writeNode && ext.writeNode(object, nodeDef)\n    })\n\n    const nodeIndex = json.nodes.push(nodeDef) - 1\n    nodeMap.set(object, nodeIndex)\n    return nodeIndex\n  }\n\n  /**\n   * Process Scene\n   * @param  {Scene} node Scene to process\n   */\n  processScene(scene) {\n    const json = this.json\n    const options = this.options\n\n    if (!json.scenes) {\n      json.scenes = []\n      json.scene = 0\n    }\n\n    const sceneDef = {}\n\n    if (scene.name !== '') sceneDef.name = scene.name\n\n    json.scenes.push(sceneDef)\n\n    const nodes = []\n\n    for (let i = 0, l = scene.children.length; i < l; i++) {\n      const child = scene.children[i]\n\n      if (child.visible || options.onlyVisible === false) {\n        const nodeIndex = this.processNode(child)\n\n        if (nodeIndex !== null) nodes.push(nodeIndex)\n      }\n    }\n\n    if (nodes.length > 0) sceneDef.nodes = nodes\n\n    this.serializeUserData(scene, sceneDef)\n  }\n\n  /**\n   * Creates a Scene to hold a list of objects and parse it\n   * @param  {Array} objects List of objects to process\n   */\n  processObjects(objects) {\n    const scene = new Scene()\n    scene.name = 'AuxScene'\n\n    for (let i = 0; i < objects.length; i++) {\n      // We push directly to children instead of calling `add` to prevent\n      // modify the .parent and break its original scene and hierarchy\n      scene.children.push(objects[i])\n    }\n\n    this.processScene(scene)\n  }\n\n  /**\n   * @param {THREE.Object3D|Array<THREE.Object3D>} input\n   */\n  processInput(input) {\n    const options = this.options\n\n    input = input instanceof Array ? input : [input]\n\n    this._invokeAll(function (ext) {\n      ext.beforeParse && ext.beforeParse(input)\n    })\n\n    const objectsWithoutScene = []\n\n    for (let i = 0; i < input.length; i++) {\n      if (input[i] instanceof Scene) {\n        this.processScene(input[i])\n      } else {\n        objectsWithoutScene.push(input[i])\n      }\n    }\n\n    if (objectsWithoutScene.length > 0) this.processObjects(objectsWithoutScene)\n\n    for (let i = 0; i < this.skins.length; ++i) {\n      this.processSkin(this.skins[i])\n    }\n\n    for (let i = 0; i < options.animations.length; ++i) {\n      this.processAnimation(options.animations[i], input[0])\n    }\n\n    this._invokeAll(function (ext) {\n      ext.afterParse && ext.afterParse(input)\n    })\n  }\n\n  _invokeAll(func) {\n    for (let i = 0, il = this.plugins.length; i < il; i++) {\n      func(this.plugins[i])\n    }\n  }\n}\n\n/**\n * Punctual Lights Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_lights_punctual\n */\nclass GLTFLightExtension {\n  constructor(writer) {\n    this.writer = writer\n    this.name = 'KHR_lights_punctual'\n  }\n\n  writeNode(light, nodeDef) {\n    if (!light.isLight) return\n\n    if (!light.isDirectionalLight && !light.isPointLight && !light.isSpotLight) {\n      console.warn('THREE.GLTFExporter: Only directional, point, and spot lights are supported.', light)\n      return\n    }\n\n    const writer = this.writer\n    const json = writer.json\n    const extensionsUsed = writer.extensionsUsed\n\n    const lightDef = {}\n\n    if (light.name) lightDef.name = light.name\n\n    lightDef.color = light.color.toArray()\n\n    lightDef.intensity = light.intensity\n\n    if (light.isDirectionalLight) {\n      lightDef.type = 'directional'\n    } else if (light.isPointLight) {\n      lightDef.type = 'point'\n\n      if (light.distance > 0) lightDef.range = light.distance\n    } else if (light.isSpotLight) {\n      lightDef.type = 'spot'\n\n      if (light.distance > 0) lightDef.range = light.distance\n\n      lightDef.spot = {}\n      lightDef.spot.innerConeAngle = (light.penumbra - 1.0) * light.angle * -1.0\n      lightDef.spot.outerConeAngle = light.angle\n    }\n\n    if (light.decay !== undefined && light.decay !== 2) {\n      console.warn(\n        'THREE.GLTFExporter: Light decay may be lost. glTF is physically-based, ' + 'and expects light.decay=2.',\n      )\n    }\n\n    if (\n      light.target &&\n      (light.target.parent !== light ||\n        light.target.position.x !== 0 ||\n        light.target.position.y !== 0 ||\n        light.target.position.z !== -1)\n    ) {\n      console.warn(\n        'THREE.GLTFExporter: Light direction may be lost. For best results, ' +\n          'make light.target a child of the light with position 0,0,-1.',\n      )\n    }\n\n    if (!extensionsUsed[this.name]) {\n      json.extensions = json.extensions || {}\n      json.extensions[this.name] = { lights: [] }\n      extensionsUsed[this.name] = true\n    }\n\n    const lights = json.extensions[this.name].lights\n    lights.push(lightDef)\n\n    nodeDef.extensions = nodeDef.extensions || {}\n    nodeDef.extensions[this.name] = { light: lights.length - 1 }\n  }\n}\n\n/**\n * Unlit Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_unlit\n */\nclass GLTFMaterialsUnlitExtension {\n  constructor(writer) {\n    this.writer = writer\n    this.name = 'KHR_materials_unlit'\n  }\n\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshBasicMaterial) return\n\n    const writer = this.writer\n    const extensionsUsed = writer.extensionsUsed\n\n    materialDef.extensions = materialDef.extensions || {}\n    materialDef.extensions[this.name] = {}\n\n    extensionsUsed[this.name] = true\n\n    materialDef.pbrMetallicRoughness.metallicFactor = 0.0\n    materialDef.pbrMetallicRoughness.roughnessFactor = 0.9\n  }\n}\n\n/**\n * Clearcoat Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_clearcoat\n */\nclass GLTFMaterialsClearcoatExtension {\n  constructor(writer) {\n    this.writer = writer\n    this.name = 'KHR_materials_clearcoat'\n  }\n\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshPhysicalMaterial || material.clearcoat === 0) return\n\n    const writer = this.writer\n    const extensionsUsed = writer.extensionsUsed\n\n    const extensionDef = {}\n\n    extensionDef.clearcoatFactor = material.clearcoat\n\n    if (material.clearcoatMap) {\n      const clearcoatMapDef = {\n        index: writer.processTexture(material.clearcoatMap),\n        texCoord: material.clearcoatMap.channel,\n      }\n      writer.applyTextureTransform(clearcoatMapDef, material.clearcoatMap)\n      extensionDef.clearcoatTexture = clearcoatMapDef\n    }\n\n    extensionDef.clearcoatRoughnessFactor = material.clearcoatRoughness\n\n    if (material.clearcoatRoughnessMap) {\n      const clearcoatRoughnessMapDef = {\n        index: writer.processTexture(material.clearcoatRoughnessMap),\n        texCoord: material.clearcoatRoughnessMap.channel,\n      }\n      writer.applyTextureTransform(clearcoatRoughnessMapDef, material.clearcoatRoughnessMap)\n      extensionDef.clearcoatRoughnessTexture = clearcoatRoughnessMapDef\n    }\n\n    if (material.clearcoatNormalMap) {\n      const clearcoatNormalMapDef = {\n        index: writer.processTexture(material.clearcoatNormalMap),\n        texCoord: material.clearcoatNormalMap.channel,\n      }\n      writer.applyTextureTransform(clearcoatNormalMapDef, material.clearcoatNormalMap)\n      extensionDef.clearcoatNormalTexture = clearcoatNormalMapDef\n    }\n\n    materialDef.extensions = materialDef.extensions || {}\n    materialDef.extensions[this.name] = extensionDef\n\n    extensionsUsed[this.name] = true\n  }\n}\n\n/**\n * Iridescence Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_iridescence\n */\nclass GLTFMaterialsIridescenceExtension {\n  constructor(writer) {\n    this.writer = writer\n    this.name = 'KHR_materials_iridescence'\n  }\n\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshPhysicalMaterial || material.iridescence === 0) return\n\n    const writer = this.writer\n    const extensionsUsed = writer.extensionsUsed\n\n    const extensionDef = {}\n\n    extensionDef.iridescenceFactor = material.iridescence\n\n    if (material.iridescenceMap) {\n      const iridescenceMapDef = {\n        index: writer.processTexture(material.iridescenceMap),\n        texCoord: material.iridescenceMap.channel,\n      }\n      writer.applyTextureTransform(iridescenceMapDef, material.iridescenceMap)\n      extensionDef.iridescenceTexture = iridescenceMapDef\n    }\n\n    extensionDef.iridescenceIor = material.iridescenceIOR\n    extensionDef.iridescenceThicknessMinimum = material.iridescenceThicknessRange[0]\n    extensionDef.iridescenceThicknessMaximum = material.iridescenceThicknessRange[1]\n\n    if (material.iridescenceThicknessMap) {\n      const iridescenceThicknessMapDef = {\n        index: writer.processTexture(material.iridescenceThicknessMap),\n        texCoord: material.iridescenceThicknessMap.channel,\n      }\n      writer.applyTextureTransform(iridescenceThicknessMapDef, material.iridescenceThicknessMap)\n      extensionDef.iridescenceThicknessTexture = iridescenceThicknessMapDef\n    }\n\n    materialDef.extensions = materialDef.extensions || {}\n    materialDef.extensions[this.name] = extensionDef\n\n    extensionsUsed[this.name] = true\n  }\n}\n\n/**\n * Transmission Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_transmission\n */\nclass GLTFMaterialsTransmissionExtension {\n  constructor(writer) {\n    this.writer = writer\n    this.name = 'KHR_materials_transmission'\n  }\n\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshPhysicalMaterial || material.transmission === 0) return\n\n    const writer = this.writer\n    const extensionsUsed = writer.extensionsUsed\n\n    const extensionDef = {}\n\n    extensionDef.transmissionFactor = material.transmission\n\n    if (material.transmissionMap) {\n      const transmissionMapDef = {\n        index: writer.processTexture(material.transmissionMap),\n        texCoord: material.transmissionMap.channel,\n      }\n      writer.applyTextureTransform(transmissionMapDef, material.transmissionMap)\n      extensionDef.transmissionTexture = transmissionMapDef\n    }\n\n    materialDef.extensions = materialDef.extensions || {}\n    materialDef.extensions[this.name] = extensionDef\n\n    extensionsUsed[this.name] = true\n  }\n}\n\n/**\n * Materials Volume Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_volume\n */\nclass GLTFMaterialsVolumeExtension {\n  constructor(writer) {\n    this.writer = writer\n    this.name = 'KHR_materials_volume'\n  }\n\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshPhysicalMaterial || material.transmission === 0) return\n\n    const writer = this.writer\n    const extensionsUsed = writer.extensionsUsed\n\n    const extensionDef = {}\n\n    extensionDef.thicknessFactor = material.thickness\n\n    if (material.thicknessMap) {\n      const thicknessMapDef = {\n        index: writer.processTexture(material.thicknessMap),\n        texCoord: material.thicknessMap.channel,\n      }\n      writer.applyTextureTransform(thicknessMapDef, material.thicknessMap)\n      extensionDef.thicknessTexture = thicknessMapDef\n    }\n\n    extensionDef.attenuationDistance = material.attenuationDistance\n    extensionDef.attenuationColor = material.attenuationColor.toArray()\n\n    materialDef.extensions = materialDef.extensions || {}\n    materialDef.extensions[this.name] = extensionDef\n\n    extensionsUsed[this.name] = true\n  }\n}\n\n/**\n * Materials ior Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_ior\n */\nclass GLTFMaterialsIorExtension {\n  constructor(writer) {\n    this.writer = writer\n    this.name = 'KHR_materials_ior'\n  }\n\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshPhysicalMaterial || material.ior === 1.5) return\n\n    const writer = this.writer\n    const extensionsUsed = writer.extensionsUsed\n\n    const extensionDef = {}\n\n    extensionDef.ior = material.ior\n\n    materialDef.extensions = materialDef.extensions || {}\n    materialDef.extensions[this.name] = extensionDef\n\n    extensionsUsed[this.name] = true\n  }\n}\n\n/**\n * Materials specular Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_specular\n */\nclass GLTFMaterialsSpecularExtension {\n  constructor(writer) {\n    this.writer = writer\n    this.name = 'KHR_materials_specular'\n  }\n\n  writeMaterial(material, materialDef) {\n    if (\n      !material.isMeshPhysicalMaterial ||\n      (material.specularIntensity === 1.0 &&\n        material.specularColor.equals(DEFAULT_SPECULAR_COLOR) &&\n        !material.specularIntensityMap &&\n        !material.specularColorTexture)\n    )\n      return\n\n    const writer = this.writer\n    const extensionsUsed = writer.extensionsUsed\n\n    const extensionDef = {}\n\n    if (material.specularIntensityMap) {\n      const specularIntensityMapDef = {\n        index: writer.processTexture(material.specularIntensityMap),\n        texCoord: material.specularIntensityMap.channel,\n      }\n      writer.applyTextureTransform(specularIntensityMapDef, material.specularIntensityMap)\n      extensionDef.specularTexture = specularIntensityMapDef\n    }\n\n    if (material.specularColorMap) {\n      const specularColorMapDef = {\n        index: writer.processTexture(material.specularColorMap),\n        texCoord: material.specularColorMap.channel,\n      }\n      writer.applyTextureTransform(specularColorMapDef, material.specularColorMap)\n      extensionDef.specularColorTexture = specularColorMapDef\n    }\n\n    extensionDef.specularFactor = material.specularIntensity\n    extensionDef.specularColorFactor = material.specularColor.toArray()\n\n    materialDef.extensions = materialDef.extensions || {}\n    materialDef.extensions[this.name] = extensionDef\n\n    extensionsUsed[this.name] = true\n  }\n}\n\n/**\n * Sheen Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_materials_sheen\n */\nclass GLTFMaterialsSheenExtension {\n  constructor(writer) {\n    this.writer = writer\n    this.name = 'KHR_materials_sheen'\n  }\n\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshPhysicalMaterial || material.sheen == 0.0) return\n\n    const writer = this.writer\n    const extensionsUsed = writer.extensionsUsed\n\n    const extensionDef = {}\n\n    if (material.sheenRoughnessMap) {\n      const sheenRoughnessMapDef = {\n        index: writer.processTexture(material.sheenRoughnessMap),\n        texCoord: material.sheenRoughnessMap.channel,\n      }\n      writer.applyTextureTransform(sheenRoughnessMapDef, material.sheenRoughnessMap)\n      extensionDef.sheenRoughnessTexture = sheenRoughnessMapDef\n    }\n\n    if (material.sheenColorMap) {\n      const sheenColorMapDef = {\n        index: writer.processTexture(material.sheenColorMap),\n        texCoord: material.sheenColorMap.channel,\n      }\n      writer.applyTextureTransform(sheenColorMapDef, material.sheenColorMap)\n      extensionDef.sheenColorTexture = sheenColorMapDef\n    }\n\n    extensionDef.sheenRoughnessFactor = material.sheenRoughness\n    extensionDef.sheenColorFactor = material.sheenColor.toArray()\n\n    materialDef.extensions = materialDef.extensions || {}\n    materialDef.extensions[this.name] = extensionDef\n\n    extensionsUsed[this.name] = true\n  }\n}\n\n/**\n * Anisotropy Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_materials_anisotropy\n */\nclass GLTFMaterialsAnisotropyExtension {\n  constructor(writer) {\n    this.writer = writer\n    this.name = 'KHR_materials_anisotropy'\n  }\n\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshPhysicalMaterial || material.anisotropy == 0.0) return\n\n    const writer = this.writer\n    const extensionsUsed = writer.extensionsUsed\n\n    const extensionDef = {}\n\n    if (material.anisotropyMap) {\n      const anisotropyMapDef = { index: writer.processTexture(material.anisotropyMap) }\n      writer.applyTextureTransform(anisotropyMapDef, material.anisotropyMap)\n      extensionDef.anisotropyTexture = anisotropyMapDef\n    }\n\n    extensionDef.anisotropyStrength = material.anisotropy\n    extensionDef.anisotropyRotation = material.anisotropyRotation\n\n    materialDef.extensions = materialDef.extensions || {}\n    materialDef.extensions[this.name] = extensionDef\n\n    extensionsUsed[this.name] = true\n  }\n}\n\n/**\n * Materials Emissive Strength Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/blob/5768b3ce0ef32bc39cdf1bef10b948586635ead3/extensions/2.0/Khronos/KHR_materials_emissive_strength/README.md\n */\nclass GLTFMaterialsEmissiveStrengthExtension {\n  constructor(writer) {\n    this.writer = writer\n    this.name = 'KHR_materials_emissive_strength'\n  }\n\n  writeMaterial(material, materialDef) {\n    if (!material.isMeshStandardMaterial || material.emissiveIntensity === 1.0) return\n\n    const writer = this.writer\n    const extensionsUsed = writer.extensionsUsed\n\n    const extensionDef = {}\n\n    extensionDef.emissiveStrength = material.emissiveIntensity\n\n    materialDef.extensions = materialDef.extensions || {}\n    materialDef.extensions[this.name] = extensionDef\n\n    extensionsUsed[this.name] = true\n  }\n}\n\nexport { GLTFExporter }\n"], "names": ["PlaneGeometry", "ShaderMaterial", "Uniform", "<PERSON><PERSON>", "PerspectiveCamera", "Scene", "WebGLRenderer", "Texture", "GLTFExporter", "PropertyBinding", "InterpolateLinear", "NearestFilter", "NearestMipmapNearestFilter", "NearestMipmapLinearFilter", "LinearFilter", "LinearMipmapNearestFilter", "LinearMipmapLinearFilter", "ClampToEdgeWrapping", "RepeatWrapping", "MirroredRepeatWrapping", "Color", "MathUtils", "uids", "Vector3", "CompressedTexture", "RGBAFormat", "DoubleSide", "version", "BufferAttribute", "InterpolateDiscrete", "Matrix4", "nodeIndex"], "mappings": ";;;;;;;;;;AAiCA,eAAe,cAAc,MAAM;AACjC,QAAM,SAAS,MAAM,KAAK,YAAa;AACvC,QAAM,OAAO,KAAK,OAAO,aAAa,GAAG,IAAI,WAAW,MAAM,CAAC,CAAC;AAChE,SAAO,QAAQ,KAAK,QAAQ,aAAa;AAC3C;AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,SAAS,WAAW,SAAS,iBAAiB,UAAU,WAAW,MAAM;AACvE,MAAI,CAAC;AAAwB,6BAAyB,IAAIA,MAAAA,cAAc,GAAG,GAAG,GAAG,CAAC;AAClF,MAAI,CAAC;AACH,6BAAyB,IAAIC,MAAAA,eAAe;AAAA,MAC1C,UAAU,EAAE,aAAa,IAAIC,MAAO,QAAC,OAAO,EAAG;AAAA,MAC/C;AAAA;AAAA,QAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOzB;AAAA;AAAA,QAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcjC,CAAK;AAEH,yBAAuB,SAAS,YAAY,QAAQ;AACpD,yBAAuB,QAAQ,UAC7B,gBAAgB,UAAU,QAAQ,eAAe,SAAS,QAAQ,aAAa;AACjF,yBAAuB,cAAc;AAErC,MAAI,CAAC,gBAAgB;AACnB,qBAAiB,IAAIC,MAAAA,KAAK,wBAAwB,sBAAsB;AACxE,mBAAe,iBAAiB;AAAA,EACjC;AAED,QAAM,UAAU,IAAIC,wBAAmB;AACvC,QAAM,SAAS,IAAIC,YAAO;AAC1B,SAAO,IAAI,cAAc;AAEzB,MAAI,CAAC,UAAU;AACb,eAAW,YAAY,IAAIC,MAAAA,cAAc,EAAE,WAAW,MAAK,CAAE;AAAA,EAC9D;AAED,WAAS,QAAQ,KAAK,IAAI,QAAQ,MAAM,OAAO,cAAc,GAAG,KAAK,IAAI,QAAQ,MAAM,QAAQ,cAAc,CAAC;AAC9G,WAAS,MAAO;AAChB,WAAS,OAAO,QAAQ,OAAO;AAE/B,QAAM,kBAAkB,IAAIC,cAAQ,SAAS,UAAU;AAEvD,kBAAgB,YAAY,QAAQ;AACpC,kBAAgB,YAAY,QAAQ;AACpC,kBAAgB,QAAQ,QAAQ;AAChC,kBAAgB,QAAQ,QAAQ;AAChC,kBAAgB,OAAO,QAAQ;AAE/B,MAAI,WAAW;AACb,cAAU,QAAS;AACnB,gBAAY;AAAA,EACb;AAED,SAAO;AACT;AAOA,MAAM,uCAAuC;AAAA,EAC3C,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA,EACD,QAAQ,CAAC,mBAAmB,kBAAkB;AAAA,EAC9C,SAAS,CAAC,mBAAmB,kBAAkB;AAAA,EAC/C,UAAU,CAAC,QAAQ,mBAAmB,iBAAiB,SAAS,oBAAoB,gBAAgB;AACtG;AAEK,MAAC,eAAgC,uBAAM;AAC1C,QAAMC,cAAa;AAAA,IAkKjB,cAAc;AACZ,WAAK,kBAAkB,CAAE;AAEzB,WAAK,SAAS,SAAU,QAAQ;AAC9B,eAAO,IAAI,mBAAmB,MAAM;AAAA,MAC5C,CAAO;AAED,WAAK,SAAS,SAAU,QAAQ;AAC9B,eAAO,IAAI,4BAA4B,MAAM;AAAA,MACrD,CAAO;AAED,WAAK,SAAS,SAAU,QAAQ;AAC9B,eAAO,IAAI,mCAAmC,MAAM;AAAA,MAC5D,CAAO;AAED,WAAK,SAAS,SAAU,QAAQ;AAC9B,eAAO,IAAI,6BAA6B,MAAM;AAAA,MACtD,CAAO;AAED,WAAK,SAAS,SAAU,QAAQ;AAC9B,eAAO,IAAI,0BAA0B,MAAM;AAAA,MACnD,CAAO;AAED,WAAK,SAAS,SAAU,QAAQ;AAC9B,eAAO,IAAI,+BAA+B,MAAM;AAAA,MACxD,CAAO;AAED,WAAK,SAAS,SAAU,QAAQ;AAC9B,eAAO,IAAI,gCAAgC,MAAM;AAAA,MACzD,CAAO;AAED,WAAK,SAAS,SAAU,QAAQ;AAC9B,eAAO,IAAI,kCAAkC,MAAM;AAAA,MAC3D,CAAO;AAED,WAAK,SAAS,SAAU,QAAQ;AAC9B,eAAO,IAAI,4BAA4B,MAAM;AAAA,MACrD,CAAO;AAED,WAAK,SAAS,SAAU,QAAQ;AAC9B,eAAO,IAAI,iCAAiC,MAAM;AAAA,MAC1D,CAAO;AAED,WAAK,SAAS,SAAU,QAAQ;AAC9B,eAAO,IAAI,uCAAuC,MAAM;AAAA,MAChE,CAAO;AAAA,IACF;AAAA,IAED,SAAS,UAAU;AACjB,UAAI,KAAK,gBAAgB,QAAQ,QAAQ,MAAM,IAAI;AACjD,aAAK,gBAAgB,KAAK,QAAQ;AAAA,MACnC;AAED,aAAO;AAAA,IACR;AAAA,IAED,WAAW,UAAU;AACnB,UAAI,KAAK,gBAAgB,QAAQ,QAAQ,MAAM,IAAI;AACjD,aAAK,gBAAgB,OAAO,KAAK,gBAAgB,QAAQ,QAAQ,GAAG,CAAC;AAAA,MACtE;AAED,aAAO;AAAA,IACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASD,MAAM,OAAO,QAAQ,SAAS,SAAS;AACrC,YAAM,SAAS,IAAI,WAAY;AAC/B,YAAM,UAAU,CAAE;AAElB,eAAS,IAAI,GAAG,KAAK,KAAK,gBAAgB,QAAQ,IAAI,IAAI,KAAK;AAC7D,gBAAQ,KAAK,KAAK,gBAAgB,CAAC,EAAE,MAAM,CAAC;AAAA,MAC7C;AAED,aAAO,WAAW,OAAO;AACzB,aAAO,MAAM,OAAO,QAAQ,OAAO,EAAE,MAAM,OAAO;AAAA,IACnD;AAAA,IAED,WAAW,OAAO,SAAS;AACzB,YAAM,QAAQ;AAEd,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,cAAM,MAAM,OAAO,SAAS,QAAQ,OAAO;AAAA,MACnD,CAAO;AAAA,IACF;AAAA,EACF;AAxPC;AAAA;AAAA;AAAA,gBAJIA,eAIG,SAAQ;AAAA,IACb,gBAAgB,SAAU,OAAO,MAAM;AACrC,YAAM,YAAY;AAClB,YAAM,YAAY,MAAM,aAAc;AAEtC,YAAM,QAAQ,IAAI,MAAM,eAAe,MAAM,MAAM,SAAS,CAAC;AAC7D,YAAM,SAAS,IAAI,MAAM,gBAAgB,MAAM,OAAO,SAAS,SAAS;AACxE,YAAM,cAAc,MAAM,kBAAkB,IAAI,MAAM,gBAAgB,SAAS,CAAC;AAEhF,UAAI;AAEJ,UAAI,MAAM,MAAM,WAAW,GAAG;AAC5B,cAAM,CAAC,IAAI;AAEX,iBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,iBAAO,CAAC,IAAI;AAAA,QACb;AAED,gBAAQ;AAAA,MACT,WAAU,OAAO,MAAM,MAAM,CAAC,GAAG;AAChC,YAAI,KAAK,IAAI,MAAM,MAAM,CAAC,IAAI,IAAI,IAAI;AAAW,iBAAO;AAExD,cAAM,CAAC,IAAI;AACX,cAAM,IAAI,MAAM,OAAO,CAAC;AAExB,eAAO,IAAI,YAAY,SAAS,IAAI,GAAG,CAAC;AACxC,eAAO,IAAI,MAAM,QAAQ,SAAS;AAElC,gBAAQ;AAAA,MAClB,WAAmB,OAAO,MAAM,MAAM,MAAM,MAAM,SAAS,CAAC,GAAG;AACrD,YAAI,KAAK,IAAI,MAAM,MAAM,MAAM,MAAM,SAAS,CAAC,IAAI,IAAI,IAAI,WAAW;AACpE,iBAAO,MAAM,MAAM,SAAS;AAAA,QAC7B;AAED,cAAM,MAAM,SAAS,CAAC,IAAI;AAC1B,cAAM,IAAI,MAAM,OAAO,CAAC;AAExB,eAAO,IAAI,MAAM,QAAQ,CAAC;AAC1B,eAAO,IAAI,YAAY,SAAS,IAAI,GAAG,MAAM,OAAO,MAAM;AAE1D,gBAAQ,MAAM,SAAS;AAAA,MACjC,OAAe;AACL,iBAAS,IAAI,GAAG,IAAI,MAAM,MAAM,QAAQ,KAAK;AAC3C,cAAI,KAAK,IAAI,MAAM,MAAM,CAAC,IAAI,IAAI,IAAI;AAAW,mBAAO;AAExD,cAAI,MAAM,MAAM,CAAC,IAAI,QAAQ,MAAM,MAAM,IAAI,CAAC,IAAI,MAAM;AACtD,kBAAM,IAAI,MAAM,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC;AACxC,kBAAM,IAAI,CAAC,IAAI;AACf,kBAAM,IAAI,MAAM,MAAM,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC;AAEzC,mBAAO,IAAI,MAAM,OAAO,MAAM,IAAI,IAAI,KAAK,SAAS,GAAG,CAAC;AACxD,mBAAO,IAAI,YAAY,SAAS,IAAI,IAAI,IAAI,KAAK,SAAS;AAC1D,mBAAO,IAAI,MAAM,OAAO,OAAO,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,SAAS;AAEvE,oBAAQ,IAAI;AAEZ;AAAA,UACD;AAAA,QACF;AAAA,MACF;AAED,YAAM,QAAQ;AACd,YAAM,SAAS;AAEf,aAAO;AAAA,IACR;AAAA,IAED,wBAAwB,SAAU,MAAM,MAAM;AAC5C,YAAM,SAAS,CAAE;AACjB,YAAM,eAAe,CAAE;AACvB,YAAM,eAAe,KAAK;AAE1B,eAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,EAAE,GAAG;AAC5C,YAAI,cAAc,aAAa,CAAC;AAChC,cAAM,qBAAqBC,MAAe,gBAAC,eAAe,YAAY,IAAI;AAC1E,cAAM,kBAAkBA,MAAAA,gBAAgB,SAAS,MAAM,mBAAmB,QAAQ;AAElF,YACE,mBAAmB,iBAAiB,2BACpC,mBAAmB,kBAAkB,QACrC;AAEA,iBAAO,KAAK,WAAW;AACvB;AAAA,QACD;AAED,YACE,YAAY,sBAAsB,YAAY,oCAC9C,YAAY,sBAAsB,YAAY,gCAC9C;AACA,cAAI,YAAY,kBAAkB,2CAA2C;AAG3E,kBAAM,IAAI,MAAM,8EAA8E;AAAA,UAC/F;AAED,kBAAQ,KAAK,8FAA8F;AAE3G,wBAAc,YAAY,MAAO;AACjC,sBAAY,iBAAiBC,uBAAiB;AAAA,QAC/C;AAED,cAAM,cAAc,gBAAgB,sBAAsB;AAC1D,cAAM,cAAc,gBAAgB,sBAAsB,mBAAmB,aAAa;AAE1F,YAAI,gBAAgB,QAAW;AAC7B,gBAAM,IAAI,MAAM,sDAAsD,mBAAmB,aAAa;AAAA,QACvG;AAED,YAAI;AAIJ,YAAI,aAAa,gBAAgB,IAAI,MAAM,QAAW;AACpD,wBAAc,YAAY,MAAO;AAEjC,gBAAM,SAAS,IAAI,YAAY,gBAAgB,cAAc,YAAY,MAAM,MAAM;AAErF,mBAAS,IAAI,GAAG,IAAI,YAAY,MAAM,QAAQ,KAAK;AACjD,mBAAO,IAAI,cAAc,WAAW,IAAI,YAAY,OAAO,CAAC;AAAA,UAC7D;AAID,sBAAY,QAAQ,mBAAmB,YAAY,MAAM;AACzD,sBAAY,SAAS;AAErB,uBAAa,gBAAgB,IAAI,IAAI;AACrC,iBAAO,KAAK,WAAW;AAEvB;AAAA,QACD;AAED,cAAM,oBAAoB,YAAY,kBAAkB,IAAI,YAAY,gBAAgB,CAAC,CAAC;AAE1F,sBAAc,aAAa,gBAAgB,IAAI;AAI/C,iBAAS,IAAI,GAAG,IAAI,YAAY,MAAM,QAAQ,KAAK;AACjD,sBAAY,OAAO,IAAI,cAAc,WAAW,IAAI,kBAAkB,SAAS,YAAY,MAAM,CAAC,CAAC;AAAA,QACpG;AAKD,iBAAS,IAAI,GAAG,IAAI,YAAY,MAAM,QAAQ,KAAK;AACjD,gBAAM,gBAAgB,KAAK,eAAe,aAAa,YAAY,MAAM,CAAC,CAAC;AAC3E,sBAAY,OAAO,gBAAgB,cAAc,WAAW,IAAI,YAAY,OAAO,CAAC;AAAA,QACrF;AAAA,MACF;AAED,WAAK,SAAS;AAEd,aAAO;AAAA,IACR;AAAA,EACF;AA8FH,SAAOF;AACT,GAAI;AAMJ,MAAM,kBAAkB;AAAA,EACtB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,cAAc;AAAA,EAEd,MAAM;AAAA,EACN,eAAe;AAAA,EACf,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,KAAK;AAAA,EACL,cAAc;AAAA,EACd,OAAO;AAAA,EAEP,cAAc;AAAA,EACd,sBAAsB;AAAA,EAEtB,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EAEtB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,QAAQ;AACV;AAEA,MAAM,wBAAwB;AAE9B,MAAM,iBAAiB,CAAE;AAEzB,eAAeG,MAAa,aAAA,IAAI,gBAAgB;AAChD,eAAeC,MAA0B,0BAAA,IAAI,gBAAgB;AAC7D,eAAeC,MAAyB,yBAAA,IAAI,gBAAgB;AAC5D,eAAeC,MAAY,YAAA,IAAI,gBAAgB;AAC/C,eAAeC,MAAyB,yBAAA,IAAI,gBAAgB;AAC5D,eAAeC,MAAwB,wBAAA,IAAI,gBAAgB;AAE3D,eAAeC,MAAmB,mBAAA,IAAI,gBAAgB;AACtD,eAAeC,MAAc,cAAA,IAAI,gBAAgB;AACjD,eAAeC,MAAsB,sBAAA,IAAI,gBAAgB;AAEzD,MAAM,kBAAkB;AAAA,EACtB,OAAO;AAAA,EACP,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,uBAAuB;AACzB;AAEA,MAAM,yBAAyC,oBAAIC,MAAAA,MAAO;AAK1D,MAAM,mBAAmB;AACzB,MAAM,mBAAmB;AACzB,MAAM,cAAc;AAEpB,MAAM,yBAAyB;AAC/B,MAAM,sBAAsB;AAC5B,MAAM,qBAAqB;AAY3B,SAAS,WAAW,QAAQ,QAAQ;AAClC,SACE,OAAO,WAAW,OAAO,UACzB,OAAO,MAAM,SAAU,SAAS,OAAO;AACrC,WAAO,YAAY,OAAO,KAAK;AAAA,EACrC,CAAK;AAEL;AAOA,SAAS,oBAAoB,MAAM;AACjC,SAAO,IAAI,YAAa,EAAC,OAAO,IAAI,EAAE;AACxC;AAQA,SAAS,iBAAiB,QAAQ;AAChC,SAAO,WAAW,OAAO,UAAU,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AACrF;AASA,SAAS,UAAU,WAAW,OAAO,OAAO;AAC1C,QAAM,SAAS;AAAA,IACb,KAAK,IAAI,MAAM,UAAU,QAAQ,EAAE,KAAK,OAAO,iBAAiB;AAAA,IAChE,KAAK,IAAI,MAAM,UAAU,QAAQ,EAAE,KAAK,OAAO,iBAAiB;AAAA,EACjE;AAED,WAAS,IAAI,OAAO,IAAI,QAAQ,OAAO,KAAK;AAC1C,aAAS,IAAI,GAAG,IAAI,UAAU,UAAU,KAAK;AAC3C,UAAI;AAEJ,UAAI,UAAU,WAAW,GAAG;AAG1B,gBAAQ,UAAU,MAAM,IAAI,UAAU,WAAW,CAAC;AAAA,MAC1D,OAAa;AACL,YAAI,MAAM;AAAG,kBAAQ,UAAU,KAAK,CAAC;AAAA,iBAC5B,MAAM;AAAG,kBAAQ,UAAU,KAAK,CAAC;AAAA,iBACjC,MAAM;AAAG,kBAAQ,UAAU,KAAK,CAAC;AAAA,iBACjC,MAAM;AAAG,kBAAQ,UAAU,KAAK,CAAC;AAE1C,YAAI,UAAU,eAAe,MAAM;AACjC,kBAAQC,MAAAA,UAAU,UAAU,OAAO,UAAU,KAAK;AAAA,QACnD;AAAA,MACF;AAED,aAAO,IAAI,CAAC,IAAI,KAAK,IAAI,OAAO,IAAI,CAAC,GAAG,KAAK;AAC7C,aAAO,IAAI,CAAC,IAAI,KAAK,IAAI,OAAO,IAAI,CAAC,GAAG,KAAK;AAAA,IAC9C;AAAA,EACF;AAED,SAAO;AACT;AAUA,SAAS,oBAAoB,YAAY;AACvC,SAAO,KAAK,KAAK,aAAa,CAAC,IAAI;AACrC;AASA,SAAS,qBAAqB,aAAa,cAAc,GAAG;AAC1D,QAAM,eAAe,oBAAoB,YAAY,UAAU;AAE/D,MAAI,iBAAiB,YAAY,YAAY;AAC3C,UAAM,QAAQ,IAAI,WAAW,YAAY;AACzC,UAAM,IAAI,IAAI,WAAW,WAAW,CAAC;AAErC,QAAI,gBAAgB,GAAG;AACrB,eAAS,IAAI,YAAY,YAAY,IAAI,cAAc,KAAK;AAC1D,cAAM,CAAC,IAAI;AAAA,MACZ;AAAA,IACF;AAED,WAAO,MAAM;AAAA,EACd;AAED,SAAO;AACT;AAEA,SAAS,YAAY;AACnB,MAAI,OAAO,aAAa,eAAe,OAAO,oBAAoB,aAAa;AAC7E,WAAO,IAAI,gBAAgB,GAAG,CAAC;AAAA,EAChC;AAED,SAAO,SAAS,cAAc,QAAQ;AACxC;AAEA,SAAS,iBAAiB,QAAQ,UAAU;AAC1C,MAAI,OAAO,WAAW,QAAW;AAC/B,WAAO,IAAI,QAAQ,CAAC,YAAY,OAAO,OAAO,SAAS,QAAQ,CAAC;AAAA,EACjE;AAED,MAAI;AAIJ,MAAI,aAAa,cAAc;AAC7B,cAAU;AAAA,EACd,WAAa,aAAa,cAAc;AACpC,cAAU;AAAA,EACX;AAED,SAAO,OAAO,cAAc;AAAA,IAC1B,MAAM;AAAA,IACN;AAAA,EACJ,CAAG;AACH;AAKA,MAAM,WAAW;AAAA,EACf,cAAc;AACZ,SAAK,UAAU,CAAE;AAEjB,SAAK,UAAU,CAAE;AACjB,SAAK,UAAU,CAAE;AACjB,SAAK,UAAU,CAAE;AAEjB,SAAK,aAAa;AAClB,SAAK,UAAU,CAAE;AACjB,SAAK,UAAU,oBAAI,IAAK;AACxB,SAAK,QAAQ,CAAE;AAEf,SAAK,iBAAiB,CAAE;AACxB,SAAK,qBAAqB,CAAE;AAE5B,SAAK,OAAO,oBAAI,IAAK;AACrB,SAAK,MAAM;AAEX,SAAK,OAAO;AAAA,MACV,OAAO;AAAA,QACL,SAAS;AAAA,QACT,WAAW;AAAA,MACZ;AAAA,IACF;AAED,SAAK,QAAQ;AAAA,MACX,QAAQ,oBAAI,IAAK;AAAA,MACjB,YAAY,oBAAI,IAAK;AAAA,MACrB,sBAAsB,oBAAI,IAAK;AAAA,MAC/B,WAAW,oBAAI,IAAK;AAAA,MACpB,UAAU,oBAAI,IAAK;AAAA,MACnB,QAAQ,oBAAI,IAAK;AAAA,IAClB;AAAA,EACF;AAAA,EAED,WAAW,SAAS;AAClB,SAAK,UAAU;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,MAAM,MAAM,OAAO,QAAQ,UAAU,CAAA,GAAI;AACvC,SAAK,UAAU,OAAO;AAAA,MACpB;AAAA;AAAA,QAEE,QAAQ;AAAA,QACR,KAAK;AAAA,QACL,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,YAAY,CAAE;AAAA,QACd,yBAAyB;AAAA,MAC1B;AAAA,MACD;AAAA,IACD;AAED,QAAI,KAAK,QAAQ,WAAW,SAAS,GAAG;AAEtC,WAAK,QAAQ,MAAM;AAAA,IACpB;AAED,SAAK,aAAa,KAAK;AAEvB,UAAM,QAAQ,IAAI,KAAK,OAAO;AAE9B,UAAM,SAAS;AACf,UAAM,UAAU,OAAO;AACvB,UAAM,OAAO,OAAO;AACpB,cAAU,OAAO;AAEjB,UAAM,iBAAiB,OAAO;AAC9B,UAAM,qBAAqB,OAAO;AAGlC,UAAM,OAAO,IAAI,KAAK,SAAS,EAAE,MAAM,4BAA4B;AAGnE,UAAM,qBAAqB,OAAO,KAAK,cAAc;AACrD,UAAM,yBAAyB,OAAO,KAAK,kBAAkB;AAE7D,QAAI,mBAAmB,SAAS;AAAG,WAAK,iBAAiB;AACzD,QAAI,uBAAuB,SAAS;AAAG,WAAK,qBAAqB;AAGjE,QAAI,KAAK,WAAW,KAAK,QAAQ,SAAS;AAAG,WAAK,QAAQ,CAAC,EAAE,aAAa,KAAK;AAE/E,QAAI,QAAQ,WAAW,MAAM;AAG3B,WAAK,YAAW,EAAG,KAAK,CAAC,WAAW;AAElC,cAAM,cAAc,qBAAqB,MAAM;AAC/C,cAAM,oBAAoB,IAAI,SAAS,IAAI,YAAY,sBAAsB,CAAC;AAC9E,0BAAkB,UAAU,GAAG,YAAY,YAAY,IAAI;AAC3D,0BAAkB,UAAU,GAAG,oBAAoB,IAAI;AAGvD,cAAM,YAAY,qBAAqB,oBAAoB,KAAK,UAAU,IAAI,CAAC,GAAG,EAAI;AACtF,cAAM,kBAAkB,IAAI,SAAS,IAAI,YAAY,sBAAsB,CAAC;AAC5E,wBAAgB,UAAU,GAAG,UAAU,YAAY,IAAI;AACvD,wBAAgB,UAAU,GAAG,qBAAqB,IAAI;AAGtD,cAAM,SAAS,IAAI,YAAY,gBAAgB;AAC/C,cAAM,aAAa,IAAI,SAAS,MAAM;AACtC,mBAAW,UAAU,GAAG,kBAAkB,IAAI;AAC9C,mBAAW,UAAU,GAAG,aAAa,IAAI;AACzC,cAAM,kBACJ,mBACA,gBAAgB,aAChB,UAAU,aACV,kBAAkB,aAClB,YAAY;AACd,mBAAW,UAAU,GAAG,iBAAiB,IAAI;AAE7C,cAAM,UAAU,IAAI,KAAK,CAAC,QAAQ,iBAAiB,WAAW,mBAAmB,WAAW,GAAG;AAAA,UAC7F,MAAM;AAAA,QAChB,CAAS;AAED,gBAAQ,YAAW,EAAG,KAAK,MAAM;AAAA,MACzC,CAAO;AAAA,IACP,OAAW;AACL,UAAI,KAAK,WAAW,KAAK,QAAQ,SAAS,GAAG;AAC3C,sBAAc,IAAI,EAAE,KAAK,CAAC,QAAQ;AAChC,eAAK,QAAQ,CAAC,EAAE,MAAM;AACtB,iBAAO,IAAI;AAAA,QACrB,CAAS;AAAA,MACT,OAAa;AACL,eAAO,IAAI;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,kBAAkB,QAAQ,WAAW;AACnC,QAAI,OAAO,KAAK,OAAO,QAAQ,EAAE,WAAW;AAAG;AAE/C,UAAM,UAAU,KAAK;AACrB,UAAM,iBAAiB,KAAK;AAE5B,QAAI;AACF,YAAM,OAAO,KAAK,MAAM,KAAK,UAAU,OAAO,QAAQ,CAAC;AAEvD,UAAI,QAAQ,2BAA2B,KAAK,gBAAgB;AAC1D,YAAI,UAAU,eAAe;AAAW,oBAAU,aAAa,CAAE;AAEjE,mBAAW,iBAAiB,KAAK,gBAAgB;AAC/C,oBAAU,WAAW,aAAa,IAAI,KAAK,eAAe,aAAa;AACvE,yBAAe,aAAa,IAAI;AAAA,QACjC;AAED,eAAO,KAAK;AAAA,MACb;AAED,UAAI,OAAO,KAAK,IAAI,EAAE,SAAS;AAAG,kBAAU,SAAS;AAAA,IACtD,SAAQ,OAAP;AACA,cAAQ;AAAA,QACN,sCACE,OAAO,OACP,6DAEA,MAAM;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,OAAO,WAAW,iBAAiB,OAAO;AACxC,QAAI,KAAK,KAAK,IAAI,SAAS,MAAM,OAAO;AACtC,YAAMC,QAAO,oBAAI,IAAK;AAEtB,MAAAA,MAAK,IAAI,MAAM,KAAK,KAAK;AACzB,MAAAA,MAAK,IAAI,OAAO,KAAK,KAAK;AAE1B,WAAK,KAAK,IAAI,WAAWA,KAAI;AAAA,IAC9B;AAED,UAAM,OAAO,KAAK,KAAK,IAAI,SAAS;AAEpC,WAAO,KAAK,IAAI,cAAc;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,4BAA4B,QAAQ;AAClC,UAAM,QAAQ,KAAK;AAEnB,QAAI,MAAM,qBAAqB,IAAI,MAAM;AAAG,aAAO;AAEnD,UAAM,IAAI,IAAIC,cAAS;AAEvB,aAAS,IAAI,GAAG,KAAK,OAAO,OAAO,IAAI,IAAI,KAAK;AAE9C,UAAI,KAAK,IAAI,EAAE,oBAAoB,QAAQ,CAAC,EAAE,OAAM,IAAK,CAAG,IAAI;AAAQ,eAAO;AAAA,IAChF;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,gCAAgC,QAAQ;AACtC,UAAM,QAAQ,KAAK;AAEnB,QAAI,MAAM,qBAAqB,IAAI,MAAM;AAAG,aAAO,MAAM,qBAAqB,IAAI,MAAM;AAExF,UAAM,YAAY,OAAO,MAAO;AAChC,UAAM,IAAI,IAAIA,cAAS;AAEvB,aAAS,IAAI,GAAG,KAAK,UAAU,OAAO,IAAI,IAAI,KAAK;AACjD,QAAE,oBAAoB,WAAW,CAAC;AAElC,UAAI,EAAE,MAAM,KAAK,EAAE,MAAM,KAAK,EAAE,MAAM,GAAG;AAEvC,UAAE,KAAK,CAAG;AAAA,MAClB,OAAa;AACL,UAAE,UAAW;AAAA,MACd;AAED,gBAAU,OAAO,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAAA,IAClC;AAED,UAAM,qBAAqB,IAAI,QAAQ,SAAS;AAEhD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,sBAAsB,QAAQ,SAAS;AACrC,QAAI,eAAe;AACnB,UAAM,eAAe,CAAE;AAEvB,QAAI,QAAQ,OAAO,MAAM,KAAK,QAAQ,OAAO,MAAM,GAAG;AACpD,mBAAa,SAAS,QAAQ,OAAO,QAAS;AAC9C,qBAAe;AAAA,IAChB;AAED,QAAI,QAAQ,aAAa,GAAG;AAC1B,mBAAa,WAAW,QAAQ;AAChC,qBAAe;AAAA,IAChB;AAED,QAAI,QAAQ,OAAO,MAAM,KAAK,QAAQ,OAAO,MAAM,GAAG;AACpD,mBAAa,QAAQ,QAAQ,OAAO,QAAS;AAC7C,qBAAe;AAAA,IAChB;AAED,QAAI,cAAc;AAChB,aAAO,aAAa,OAAO,cAAc,CAAE;AAC3C,aAAO,WAAW,uBAAuB,IAAI;AAC7C,WAAK,eAAe,uBAAuB,IAAI;AAAA,IAChD;AAAA,EACF;AAAA,EAED,uBAAuB,cAAc,cAAc;AACjD,QAAI,iBAAiB;AAAc,aAAO;AAE1C,aAAS,sBAAsB,KAAK;AAClC,UAAI,gBAAgB,MAAM,IAAI,eAAe,SAAS,IAAI,aAAa,MAAM;AAC3E,eAAO,SAAS,aAAa,GAAG;AAC9B,iBAAO,IAAI,UAAU,IAAI,eAAe,KAAK,IAAI,IAAI,eAAe,cAAc,GAAG;AAAA,QACtF;AAAA,MACF;AAED,aAAO,SAAS,eAAe,GAAG;AAChC,eAAO;AAAA,MACR;AAAA,IACF;AAED,YAAQ,KAAK,oEAAoE;AAEjF,QAAI,wBAAwBC,MAAAA,mBAAmB;AAC7C,qBAAe,WAAW,YAAY;AAAA,IACvC;AAED,QAAI,wBAAwBA,MAAAA,mBAAmB;AAC7C,qBAAe,WAAW,YAAY;AAAA,IACvC;AAED,UAAM,YAAY,eAAe,aAAa,QAAQ;AACtD,UAAM,YAAY,eAAe,aAAa,QAAQ;AAEtD,UAAM,QAAQ,KAAK,IAAI,YAAY,UAAU,QAAQ,GAAG,YAAY,UAAU,QAAQ,CAAC;AACvF,UAAM,SAAS,KAAK,IAAI,YAAY,UAAU,SAAS,GAAG,YAAY,UAAU,SAAS,CAAC;AAE1F,UAAM,SAAS,UAAW;AAC1B,WAAO,QAAQ;AACf,WAAO,SAAS;AAEhB,UAAM,UAAU,OAAO,WAAW,IAAI;AACtC,YAAQ,YAAY;AACpB,YAAQ,SAAS,GAAG,GAAG,OAAO,MAAM;AAEpC,UAAM,YAAY,QAAQ,aAAa,GAAG,GAAG,OAAO,MAAM;AAE1D,QAAI,WAAW;AACb,cAAQ,UAAU,WAAW,GAAG,GAAG,OAAO,MAAM;AAEhD,YAAM,UAAU,sBAAsB,YAAY;AAClD,YAAM,OAAO,QAAQ,aAAa,GAAG,GAAG,OAAO,MAAM,EAAE;AAEvD,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACvC,kBAAU,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,IAAI,GAAG,IAAI;AAAA,MAC9C;AAAA,IACF;AAED,QAAI,WAAW;AACb,cAAQ,UAAU,WAAW,GAAG,GAAG,OAAO,MAAM;AAEhD,YAAM,UAAU,sBAAsB,YAAY;AAClD,YAAM,OAAO,QAAQ,aAAa,GAAG,GAAG,OAAO,MAAM,EAAE;AAEvD,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACvC,kBAAU,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,IAAI,GAAG,IAAI;AAAA,MAC9C;AAAA,IACF;AAED,YAAQ,aAAa,WAAW,GAAG,CAAC;AAIpC,UAAM,YAAY,gBAAgB;AAElC,UAAM,UAAU,UAAU,MAAO;AAGjC,YAAQ,SAAS,IAAIjB,cAAQ,MAAM,EAAE;AACrC,QAAI,gBAAgB;AAAS,cAAQ,aAAa;AAAA;AAC7C,cAAQ,WAAW;AACxB,YAAQ,WAAW,gBAAgB,cAAc;AAEjD,QAAI,gBAAgB,gBAAgB,aAAa,YAAY,aAAa,SAAS;AACjF,cAAQ,KAAK,wFAAwF;AAAA,IACtG;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,cAAc,QAAQ;AACpB,UAAM,OAAO,KAAK;AAClB,UAAM,UAAU,KAAK;AAErB,QAAI,CAAC,KAAK;AAAS,WAAK,UAAU,CAAC,EAAE,YAAY,GAAG;AAGpD,YAAQ,KAAK,MAAM;AAEnB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWD,kBAAkB,WAAW,eAAe,OAAO,OAAO,QAAQ;AAChE,UAAM,OAAO,KAAK;AAElB,QAAI,CAAC,KAAK;AAAa,WAAK,cAAc,CAAE;AAI5C,QAAI;AAEJ,YAAQ,eAAa;AAAA,MACnB,KAAK,gBAAgB;AAAA,MACrB,KAAK,gBAAgB;AACnB,wBAAgB;AAEhB;AAAA,MAEF,KAAK,gBAAgB;AAAA,MACrB,KAAK,gBAAgB;AACnB,wBAAgB;AAEhB;AAAA,MAEF;AACE,wBAAgB;AAAA,IACnB;AAED,QAAI,aAAa,UAAU,WAAW;AACtC,QAAI,WAAW,gBAAgB,cAAc;AAG3C,mBAAa,KAAK,KAAK,aAAa,CAAC,IAAI;AAAA,IAC1C;AACD,UAAM,aAAa,oBAAoB,QAAQ,UAAU;AACzD,UAAM,WAAW,IAAI,SAAS,IAAI,YAAY,UAAU,CAAC;AACzD,QAAI,SAAS;AAEb,aAAS,IAAI,OAAO,IAAI,QAAQ,OAAO,KAAK;AAC1C,eAAS,IAAI,GAAG,IAAI,UAAU,UAAU,KAAK;AAC3C,YAAI;AAEJ,YAAI,UAAU,WAAW,GAAG;AAG1B,kBAAQ,UAAU,MAAM,IAAI,UAAU,WAAW,CAAC;AAAA,QAC5D,OAAe;AACL,cAAI,MAAM;AAAG,oBAAQ,UAAU,KAAK,CAAC;AAAA,mBAC5B,MAAM;AAAG,oBAAQ,UAAU,KAAK,CAAC;AAAA,mBACjC,MAAM;AAAG,oBAAQ,UAAU,KAAK,CAAC;AAAA,mBACjC,MAAM;AAAG,oBAAQ,UAAU,KAAK,CAAC;AAE1C,cAAI,UAAU,eAAe,MAAM;AACjC,oBAAQc,MAAAA,UAAU,UAAU,OAAO,UAAU,KAAK;AAAA,UACnD;AAAA,QACF;AAED,YAAI,kBAAkB,gBAAgB,OAAO;AAC3C,mBAAS,WAAW,QAAQ,OAAO,IAAI;AAAA,QACjD,WAAmB,kBAAkB,gBAAgB,KAAK;AAChD,mBAAS,SAAS,QAAQ,OAAO,IAAI;AAAA,QAC/C,WAAmB,kBAAkB,gBAAgB,cAAc;AACzD,mBAAS,UAAU,QAAQ,OAAO,IAAI;AAAA,QAChD,WAAmB,kBAAkB,gBAAgB,OAAO;AAClD,mBAAS,SAAS,QAAQ,OAAO,IAAI;AAAA,QAC/C,WAAmB,kBAAkB,gBAAgB,gBAAgB;AAC3D,mBAAS,UAAU,QAAQ,OAAO,IAAI;AAAA,QAChD,WAAmB,kBAAkB,gBAAgB,MAAM;AACjD,mBAAS,QAAQ,QAAQ,KAAK;AAAA,QACxC,WAAmB,kBAAkB,gBAAgB,eAAe;AAC1D,mBAAS,SAAS,QAAQ,KAAK;AAAA,QAChC;AAED,kBAAU;AAAA,MACX;AACD,UAAI,SAAS,eAAe,GAAG;AAC7B,kBAAU,aAAc,SAAS;AAAA,MAClC;AAAA,IACF;AAED,UAAM,gBAAgB;AAAA,MACpB,QAAQ,KAAK,cAAc,SAAS,MAAM;AAAA,MAC1C,YAAY,KAAK;AAAA,MACjB;AAAA,IACD;AAED,QAAI,WAAW;AAAW,oBAAc,SAAS;AAEjD,QAAI,WAAW,gBAAgB,cAAc;AAE3C,oBAAc,aAAa;AAAA,IAC5B;AAED,SAAK,cAAc;AAEnB,SAAK,YAAY,KAAK,aAAa;AAGnC,UAAM,SAAS;AAAA,MACb,IAAI,KAAK,YAAY,SAAS;AAAA,MAC9B,YAAY;AAAA,IACb;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,uBAAuB,MAAM;AAC3B,UAAM,SAAS;AACf,UAAM,OAAO,OAAO;AAEpB,QAAI,CAAC,KAAK;AAAa,WAAK,cAAc,CAAE;AAE5C,WAAO,KAAK,YAAW,EAAG,KAAK,CAAC,WAAW;AACzC,YAAM,SAAS,qBAAqB,MAAM;AAE1C,YAAM,gBAAgB;AAAA,QACpB,QAAQ,OAAO,cAAc,MAAM;AAAA,QACnC,YAAY,OAAO;AAAA,QACnB,YAAY,OAAO;AAAA,MACpB;AAED,aAAO,cAAc,OAAO;AAC5B,aAAO,KAAK,YAAY,KAAK,aAAa,IAAI;AAAA,IACpD,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUD,gBAAgB,WAAW,UAAU,OAAO,OAAO;AACjD,UAAM,OAAO,KAAK;AAElB,UAAM,QAAQ;AAAA,MACZ,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,IAAI;AAAA,IACL;AAED,QAAI;AAGJ,QAAI,UAAU,MAAM,gBAAgB,cAAc;AAChD,sBAAgB,gBAAgB;AAAA,IACjC,WAAU,UAAU,MAAM,gBAAgB,YAAY;AACrD,sBAAgB,gBAAgB;AAAA,IACjC,WAAU,UAAU,MAAM,gBAAgB,aAAa;AACtD,sBAAgB,gBAAgB;AAAA,IACjC,WAAU,UAAU,MAAM,gBAAgB,YAAY;AACrD,sBAAgB,gBAAgB;AAAA,IACjC,WAAU,UAAU,MAAM,gBAAgB,aAAa;AACtD,sBAAgB,gBAAgB;AAAA,IACjC,WAAU,UAAU,MAAM,gBAAgB,WAAW;AACpD,sBAAgB,gBAAgB;AAAA,IACjC,WAAU,UAAU,MAAM,gBAAgB,YAAY;AACrD,sBAAgB,gBAAgB;AAAA,IACtC,OAAW;AACL,YAAM,IAAI;AAAA,QACR,qEAAqE,UAAU,MAAM,YAAY;AAAA,MAClG;AAAA,IACF;AAED,QAAI,UAAU;AAAW,cAAQ;AACjC,QAAI,UAAU;AAAW,cAAQ,UAAU;AAG3C,QAAI,UAAU;AAAG,aAAO;AAExB,UAAM,SAAS,UAAU,WAAW,OAAO,KAAK;AAChD,QAAI;AAIJ,QAAI,aAAa,QAAW;AAC1B,yBACE,cAAc,SAAS,QAAQ,gBAAgB,uBAAuB,gBAAgB;AAAA,IACzF;AAED,UAAM,aAAa,KAAK,kBAAkB,WAAW,eAAe,OAAO,OAAO,gBAAgB;AAElG,UAAM,cAAc;AAAA,MAClB,YAAY,WAAW;AAAA,MACvB,YAAY,WAAW;AAAA,MACvB;AAAA,MACA;AAAA,MACA,KAAK,OAAO;AAAA,MACZ,KAAK,OAAO;AAAA,MACZ,MAAM,MAAM,UAAU,QAAQ;AAAA,IAC/B;AAED,QAAI,UAAU,eAAe;AAAM,kBAAY,aAAa;AAC5D,QAAI,CAAC,KAAK;AAAW,WAAK,YAAY,CAAE;AAExC,WAAO,KAAK,UAAU,KAAK,WAAW,IAAI;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUD,aAAa,OAAO,QAAQ,OAAO,WAAW,aAAa;AACzD,QAAI,UAAU,MAAM;AAClB,YAAM,SAAS;AACf,YAAM,QAAQ,OAAO;AACrB,YAAM,OAAO,OAAO;AACpB,YAAM,UAAU,OAAO;AACvB,YAAM,UAAU,OAAO;AAEvB,UAAI,CAAC,MAAM,OAAO,IAAI,KAAK;AAAG,cAAM,OAAO,IAAI,OAAO,EAAE;AAExD,YAAM,eAAe,MAAM,OAAO,IAAI,KAAK;AAE3C,YAAM,MAAM,WAAW,YAAY,MAAM,SAAU;AAEnD,UAAI,aAAa,GAAG,MAAM;AAAW,eAAO,aAAa,GAAG;AAE5D,UAAI,CAAC,KAAK;AAAQ,aAAK,SAAS,CAAE;AAElC,YAAM,WAAW,EAAE,SAAoB;AAEvC,YAAM,SAAS,UAAW;AAE1B,aAAO,QAAQ,KAAK,IAAI,MAAM,OAAO,QAAQ,cAAc;AAC3D,aAAO,SAAS,KAAK,IAAI,MAAM,QAAQ,QAAQ,cAAc;AAE7D,YAAM,MAAM,OAAO,WAAW,IAAI;AAElC,UAAI,UAAU,MAAM;AAClB,YAAI,UAAU,GAAG,OAAO,MAAM;AAC9B,YAAI,MAAM,GAAG,EAAE;AAAA,MAChB;AAED,UAAI,MAAM,SAAS,QAAW;AAG5B,YAAI,WAAWI,MAAAA,YAAY;AACzB,kBAAQ,MAAM,+CAA+C,MAAM;AAAA,QACpE;AAED,YAAI,MAAM,QAAQ,QAAQ,kBAAkB,MAAM,SAAS,QAAQ,gBAAgB;AACjF,kBAAQ,KAAK,0DAA0D,KAAK;AAAA,QAC7E;AAED,cAAM,OAAO,IAAI,kBAAkB,MAAM,SAAS,MAAM,QAAQ,CAAC;AAEjE,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACvC,eAAK,IAAI,CAAC,IAAI,MAAM,KAAK,IAAI,CAAC;AAC9B,eAAK,IAAI,CAAC,IAAI,MAAM,KAAK,IAAI,CAAC;AAC9B,eAAK,IAAI,CAAC,IAAI,MAAM,KAAK,IAAI,CAAC;AAC9B,eAAK,IAAI,CAAC,IAAI,MAAM,KAAK,IAAI,CAAC;AAAA,QAC/B;AAED,YAAI,aAAa,IAAI,UAAU,MAAM,MAAM,OAAO,MAAM,MAAM,GAAG,GAAG,CAAC;AAAA,MAC7E,OAAa;AACL,YAAI,UAAU,OAAO,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAAA,MACvD;AAED,UAAI,QAAQ,WAAW,MAAM;AAC3B,gBAAQ;AAAA,UACN,iBAAiB,QAAQ,QAAQ,EAC9B,KAAK,CAAC,SAAS,OAAO,uBAAuB,IAAI,CAAC,EAClD,KAAK,CAAC,oBAAoB;AACzB,qBAAS,aAAa;AAAA,UACpC,CAAa;AAAA,QACJ;AAAA,MACT,OAAa;AACL,YAAI,OAAO,cAAc,QAAW;AAClC,mBAAS,MAAM,OAAO,UAAU,QAAQ;AAAA,QAClD,OAAe;AACL,kBAAQ;AAAA,YACN,iBAAiB,QAAQ,QAAQ,EAC9B,KAAK,aAAa,EAClB,KAAK,CAAC,QAAQ;AACb,uBAAS,MAAM;AAAA,YAC/B,CAAe;AAAA,UACJ;AAAA,QACF;AAAA,MACF;AAED,YAAM,QAAQ,KAAK,OAAO,KAAK,QAAQ,IAAI;AAC3C,mBAAa,GAAG,IAAI;AACpB,aAAO;AAAA,IACb,OAAW;AACL,YAAM,IAAI,MAAM,2EAA2E;AAAA,IAC5F;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,eAAe,KAAK;AAClB,UAAM,OAAO,KAAK;AAElB,QAAI,CAAC,KAAK;AAAU,WAAK,WAAW,CAAE;AAEtC,UAAM,aAAa;AAAA,MACjB,WAAW,eAAe,IAAI,SAAS;AAAA,MACvC,WAAW,eAAe,IAAI,SAAS;AAAA,MACvC,OAAO,eAAe,IAAI,KAAK;AAAA,MAC/B,OAAO,eAAe,IAAI,KAAK;AAAA,IAChC;AAED,WAAO,KAAK,SAAS,KAAK,UAAU,IAAI;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,eAAe,KAAK;AAClB,UAAM,SAAS;AACf,UAAM,UAAU,OAAO;AACvB,UAAM,QAAQ,KAAK;AACnB,UAAM,OAAO,KAAK;AAElB,QAAI,MAAM,SAAS,IAAI,GAAG;AAAG,aAAO,MAAM,SAAS,IAAI,GAAG;AAE1D,QAAI,CAAC,KAAK;AAAU,WAAK,WAAW,CAAE;AAGtC,QAAI,eAAeD,MAAAA,mBAAmB;AACpC,YAAM,WAAW,KAAK,QAAQ,cAAc;AAAA,IAC7C;AAED,QAAI,WAAW,IAAI,SAAS;AAE5B,QAAI,aAAa;AAAc,iBAAW;AAE1C,UAAM,aAAa;AAAA,MACjB,SAAS,KAAK,eAAe,GAAG;AAAA,MAChC,QAAQ,KAAK,aAAa,IAAI,OAAO,IAAI,QAAQ,IAAI,OAAO,QAAQ;AAAA,IACrE;AAED,QAAI,IAAI;AAAM,iBAAW,OAAO,IAAI;AAEpC,SAAK,WAAW,SAAU,KAAK;AAC7B,UAAI,gBAAgB,IAAI,aAAa,KAAK,UAAU;AAAA,IAC1D,CAAK;AAED,UAAM,QAAQ,KAAK,SAAS,KAAK,UAAU,IAAI;AAC/C,UAAM,SAAS,IAAI,KAAK,KAAK;AAC7B,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,gBAAgB,UAAU;AACxB,UAAM,QAAQ,KAAK;AACnB,UAAM,OAAO,KAAK;AAElB,QAAI,MAAM,UAAU,IAAI,QAAQ;AAAG,aAAO,MAAM,UAAU,IAAI,QAAQ;AAEtE,QAAI,SAAS,kBAAkB;AAC7B,cAAQ,KAAK,mDAAmD;AAChE,aAAO;AAAA,IACR;AAED,QAAI,CAAC,KAAK;AAAW,WAAK,YAAY,CAAE;AAGxC,UAAM,cAAc,EAAE,sBAAsB,GAAI;AAEhD,QAAI,SAAS,2BAA2B,QAAQ,SAAS,wBAAwB,MAAM;AACrF,cAAQ,KAAK,+EAA+E;AAAA,IAC7F;AAGD,UAAM,QAAQ,SAAS,MAAM,QAAS,EAAC,OAAO,CAAC,SAAS,OAAO,CAAC;AAEhE,QAAI,CAAC,WAAW,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG;AACpC,kBAAY,qBAAqB,kBAAkB;AAAA,IACpD;AAED,QAAI,SAAS,wBAAwB;AACnC,kBAAY,qBAAqB,iBAAiB,SAAS;AAC3D,kBAAY,qBAAqB,kBAAkB,SAAS;AAAA,IAClE,OAAW;AACL,kBAAY,qBAAqB,iBAAiB;AAClD,kBAAY,qBAAqB,kBAAkB;AAAA,IACpD;AAGD,QAAI,SAAS,gBAAgB,SAAS,cAAc;AAClD,YAAM,oBAAoB,KAAK,uBAAuB,SAAS,cAAc,SAAS,YAAY;AAElG,YAAM,mBAAmB;AAAA,QACvB,OAAO,KAAK,eAAe,iBAAiB;AAAA,QAC5C,SAAS,kBAAkB;AAAA,MAC5B;AACD,WAAK,sBAAsB,kBAAkB,iBAAiB;AAC9D,kBAAY,qBAAqB,2BAA2B;AAAA,IAC7D;AAGD,QAAI,SAAS,KAAK;AAChB,YAAM,kBAAkB;AAAA,QACtB,OAAO,KAAK,eAAe,SAAS,GAAG;AAAA,QACvC,UAAU,SAAS,IAAI;AAAA,MACxB;AACD,WAAK,sBAAsB,iBAAiB,SAAS,GAAG;AACxD,kBAAY,qBAAqB,mBAAmB;AAAA,IACrD;AAED,QAAI,SAAS,UAAU;AACrB,YAAM,WAAW,SAAS;AAC1B,YAAM,uBAAuB,KAAK,IAAI,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC;AAExE,UAAI,uBAAuB,GAAG;AAC5B,oBAAY,iBAAiB,SAAS,SAAS,QAAS;AAAA,MACzD;AAGD,UAAI,SAAS,aAAa;AACxB,cAAM,iBAAiB;AAAA,UACrB,OAAO,KAAK,eAAe,SAAS,WAAW;AAAA,UAC/C,UAAU,SAAS,YAAY;AAAA,QAChC;AACD,aAAK,sBAAsB,gBAAgB,SAAS,WAAW;AAC/D,oBAAY,kBAAkB;AAAA,MAC/B;AAAA,IACF;AAGD,QAAI,SAAS,WAAW;AACtB,YAAM,eAAe;AAAA,QACnB,OAAO,KAAK,eAAe,SAAS,SAAS;AAAA,QAC7C,UAAU,SAAS,UAAU;AAAA,MAC9B;AAED,UAAI,SAAS,eAAe,SAAS,YAAY,MAAM,GAAG;AAGxD,qBAAa,QAAQ,SAAS,YAAY;AAAA,MAC3C;AAED,WAAK,sBAAsB,cAAc,SAAS,SAAS;AAC3D,kBAAY,gBAAgB;AAAA,IAC7B;AAGD,QAAI,SAAS,OAAO;AAClB,YAAM,kBAAkB;AAAA,QACtB,OAAO,KAAK,eAAe,SAAS,KAAK;AAAA,QACzC,UAAU,SAAS,MAAM;AAAA,MAC1B;AAED,UAAI,SAAS,mBAAmB,GAAK;AACnC,wBAAgB,WAAW,SAAS;AAAA,MACrC;AAED,WAAK,sBAAsB,iBAAiB,SAAS,KAAK;AAC1D,kBAAY,mBAAmB;AAAA,IAChC;AAGD,QAAI,SAAS,aAAa;AACxB,kBAAY,YAAY;AAAA,IAC9B,OAAW;AACL,UAAI,SAAS,YAAY,GAAK;AAC5B,oBAAY,YAAY;AACxB,oBAAY,cAAc,SAAS;AAAA,MACpC;AAAA,IACF;AAGD,QAAI,SAAS,SAASE,MAAU;AAAE,kBAAY,cAAc;AAC5D,QAAI,SAAS,SAAS;AAAI,kBAAY,OAAO,SAAS;AAEtD,SAAK,kBAAkB,UAAU,WAAW;AAE5C,SAAK,WAAW,SAAU,KAAK;AAC7B,UAAI,iBAAiB,IAAI,cAAc,UAAU,WAAW;AAAA,IAClE,CAAK;AAED,UAAM,QAAQ,KAAK,UAAU,KAAK,WAAW,IAAI;AACjD,UAAM,UAAU,IAAI,UAAU,KAAK;AACnC,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,YAAY,MAAM;AAChB,UAAM,QAAQ,KAAK;AACnB,UAAM,OAAO,KAAK;AAElB,UAAM,oBAAoB,CAAC,KAAK,SAAS,IAAI;AAE7C,QAAI,MAAM,QAAQ,KAAK,QAAQ,GAAG;AAChC,eAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,IAAI,GAAG,KAAK;AACpD,0BAAkB,KAAK,KAAK,SAAS,CAAC,EAAE,IAAI;AAAA,MAC7C;AAAA,IACP,OAAW;AACL,wBAAkB,KAAK,KAAK,SAAS,IAAI;AAAA,IAC1C;AAED,UAAM,eAAe,kBAAkB,KAAK,GAAG;AAE/C,QAAI,MAAM,OAAO,IAAI,YAAY;AAAG,aAAO,MAAM,OAAO,IAAI,YAAY;AAExE,UAAM,WAAW,KAAK;AAEtB,QAAI;AAGJ,QAAI,KAAK,gBAAgB;AACvB,aAAO,gBAAgB;AAAA,IAC7B,WAAe,KAAK,YAAY;AAC1B,aAAO,gBAAgB;AAAA,IAC7B,WAAe,KAAK,QAAQ;AACtB,aAAO,gBAAgB;AAAA,IAC7B,WAAe,KAAK,UAAU;AACxB,aAAO,gBAAgB;AAAA,IAC7B,OAAW;AACL,aAAO,KAAK,SAAS,YAAY,gBAAgB,QAAQ,gBAAgB;AAAA,IAC1E;AAED,UAAM,UAAU,CAAE;AAClB,UAAM,aAAa,CAAE;AACrB,UAAM,aAAa,CAAE;AACrB,UAAM,UAAU,CAAE;AAGlB,UAAM,iBAAiB;AAAA,MACrB,GAAIC,UAAO,WAAI,MACX;AAAA,QACE,IAAI;AAAA,QACJ,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACN,IACD;AAAA,QACE,IAAI;AAAA,QACJ,KAAK;AAAA,MACjB;AAAA,MACM,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,WAAW;AAAA,IACZ;AAED,UAAM,iBAAiB,SAAS,aAAa,QAAQ;AAErD,QAAI,mBAAmB,UAAa,CAAC,KAAK,4BAA4B,cAAc,GAAG;AACrF,cAAQ,KAAK,uFAAuF;AAEpG,eAAS,aAAa,UAAU,KAAK,gCAAgC,cAAc,CAAC;AAAA,IACrF;AAID,QAAI,oBAAoB;AAExB,aAAS,iBAAiB,SAAS,YAAY;AAE7C,UAAI,cAAc,MAAM,GAAG,CAAC,MAAM;AAAS;AAE3C,YAAM,YAAY,SAAS,WAAW,aAAa;AACnD,sBAAgB,eAAe,aAAa,KAAK,cAAc,YAAa;AAI5E,YAAM,wBAAwB;AAE9B,UAAI,CAAC,sBAAsB,KAAK,aAAa;AAAG,wBAAgB,MAAM;AAEtE,UAAI,MAAM,WAAW,IAAI,KAAK,OAAO,SAAS,CAAC,GAAG;AAChD,mBAAW,aAAa,IAAI,MAAM,WAAW,IAAI,KAAK,OAAO,SAAS,CAAC;AACvE;AAAA,MACD;AAGD,0BAAoB;AACpB,YAAM,QAAQ,UAAU;AAExB,UAAI,kBAAkB,cAAc,EAAE,iBAAiB,gBAAgB,EAAE,iBAAiB,aAAa;AACrG,gBAAQ,KAAK,uEAAuE;AACpF,4BAAoB,IAAIC,sBAAgB,IAAI,YAAY,KAAK,GAAG,UAAU,UAAU,UAAU,UAAU;AAAA,MACzG;AAED,YAAM,WAAW,KAAK,gBAAgB,qBAAqB,WAAW,QAAQ;AAE9E,UAAI,aAAa,MAAM;AACrB,YAAI,CAAC,cAAc,WAAW,GAAG,GAAG;AAClC,eAAK,uBAAuB,eAAe,SAAS;AAAA,QACrD;AAED,mBAAW,aAAa,IAAI;AAC5B,cAAM,WAAW,IAAI,KAAK,OAAO,SAAS,GAAG,QAAQ;AAAA,MACtD;AAAA,IACF;AAED,QAAI,mBAAmB;AAAW,eAAS,aAAa,UAAU,cAAc;AAGhF,QAAI,OAAO,KAAK,UAAU,EAAE,WAAW;AAAG,aAAO;AAGjD,QAAI,KAAK,0BAA0B,UAAa,KAAK,sBAAsB,SAAS,GAAG;AACrF,YAAM,UAAU,CAAE;AAClB,YAAM,cAAc,CAAE;AACtB,YAAM,oBAAoB,CAAE;AAE5B,UAAI,KAAK,0BAA0B,QAAW;AAC5C,mBAAW,OAAO,KAAK,uBAAuB;AAC5C,4BAAkB,KAAK,sBAAsB,GAAG,CAAC,IAAI;AAAA,QACtD;AAAA,MACF;AAED,eAAS,IAAI,GAAG,IAAI,KAAK,sBAAsB,QAAQ,EAAE,GAAG;AAC1D,cAAM,SAAS,CAAE;AACjB,YAAI,SAAS;AAEb,mBAAW,iBAAiB,SAAS,iBAAiB;AAIpD,cAAI,kBAAkB,cAAc,kBAAkB,UAAU;AAC9D,gBAAI,CAAC,QAAQ;AACX,sBAAQ,KAAK,6DAA6D;AAC1E,uBAAS;AAAA,YACV;AAED;AAAA,UACD;AAED,gBAAM,YAAY,SAAS,gBAAgB,aAAa,EAAE,CAAC;AAC3D,gBAAM,oBAAoB,cAAc,YAAa;AAOrD,gBAAM,gBAAgB,SAAS,WAAW,aAAa;AAEvD,cAAI,MAAM,WAAW,IAAI,KAAK,OAAO,WAAW,IAAI,CAAC,GAAG;AACtD,mBAAO,iBAAiB,IAAI,MAAM,WAAW,IAAI,KAAK,OAAO,WAAW,IAAI,CAAC;AAC7E;AAAA,UACD;AAGD,gBAAM,oBAAoB,UAAU,MAAO;AAE3C,cAAI,CAAC,SAAS,sBAAsB;AAClC,qBAAS,IAAI,GAAG,KAAK,UAAU,OAAO,IAAI,IAAI,KAAK;AACjD,uBAAS,IAAI,GAAG,IAAI,UAAU,UAAU,KAAK;AAC3C,oBAAI,MAAM;AAAG,oCAAkB,KAAK,GAAG,UAAU,KAAK,CAAC,IAAI,cAAc,KAAK,CAAC,CAAC;AAChF,oBAAI,MAAM;AAAG,oCAAkB,KAAK,GAAG,UAAU,KAAK,CAAC,IAAI,cAAc,KAAK,CAAC,CAAC;AAChF,oBAAI,MAAM;AAAG,oCAAkB,KAAK,GAAG,UAAU,KAAK,CAAC,IAAI,cAAc,KAAK,CAAC,CAAC;AAChF,oBAAI,MAAM;AAAG,oCAAkB,KAAK,GAAG,UAAU,KAAK,CAAC,IAAI,cAAc,KAAK,CAAC,CAAC;AAAA,cACjF;AAAA,YACF;AAAA,UACF;AAED,iBAAO,iBAAiB,IAAI,KAAK,gBAAgB,mBAAmB,QAAQ;AAC5E,gBAAM,WAAW,IAAI,KAAK,OAAO,eAAe,IAAI,GAAG,OAAO,iBAAiB,CAAC;AAAA,QACjF;AAED,gBAAQ,KAAK,MAAM;AAEnB,gBAAQ,KAAK,KAAK,sBAAsB,CAAC,CAAC;AAE1C,YAAI,KAAK,0BAA0B;AAAW,sBAAY,KAAK,kBAAkB,CAAC,CAAC;AAAA,MACpF;AAED,cAAQ,UAAU;AAElB,UAAI,YAAY,SAAS,GAAG;AAC1B,gBAAQ,SAAS,CAAE;AACnB,gBAAQ,OAAO,cAAc;AAAA,MAC9B;AAAA,IACF;AAED,UAAM,kBAAkB,MAAM,QAAQ,KAAK,QAAQ;AAEnD,QAAI,mBAAmB,SAAS,OAAO,WAAW;AAAG,aAAO;AAE5D,UAAM,YAAY,kBAAkB,KAAK,WAAW,CAAC,KAAK,QAAQ;AAClE,UAAM,SAAS,kBAAkB,SAAS,SAAS,CAAC,EAAE,eAAe,GAAG,OAAO,QAAW,OAAO,OAAS,CAAE;AAE5G,aAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAK;AAC/C,YAAM,YAAY;AAAA,QAChB;AAAA,QACA;AAAA,MACD;AAED,WAAK,kBAAkB,UAAU,SAAS;AAE1C,UAAI,QAAQ,SAAS;AAAG,kBAAU,UAAU;AAE5C,UAAI,SAAS,UAAU,MAAM;AAC3B,YAAI,WAAW,KAAK,OAAO,SAAS,KAAK;AAEzC,YAAI,OAAO,CAAC,EAAE,UAAU,UAAa,OAAO,CAAC,EAAE,UAAU,QAAW;AAClE,sBAAY,MAAM,OAAO,CAAC,EAAE,QAAQ,MAAM,OAAO,CAAC,EAAE;AAAA,QACrD;AAED,YAAI,MAAM,WAAW,IAAI,QAAQ,GAAG;AAClC,oBAAU,UAAU,MAAM,WAAW,IAAI,QAAQ;AAAA,QAC3D,OAAe;AACL,oBAAU,UAAU,KAAK,gBAAgB,SAAS,OAAO,UAAU,OAAO,CAAC,EAAE,OAAO,OAAO,CAAC,EAAE,KAAK;AACnG,gBAAM,WAAW,IAAI,UAAU,UAAU,OAAO;AAAA,QACjD;AAED,YAAI,UAAU,YAAY;AAAM,iBAAO,UAAU;AAAA,MAClD;AAED,YAAM,WAAW,KAAK,gBAAgB,UAAU,OAAO,CAAC,EAAE,aAAa,CAAC;AAExE,UAAI,aAAa;AAAM,kBAAU,WAAW;AAE5C,iBAAW,KAAK,SAAS;AAAA,IAC1B;AAED,YAAQ,aAAa;AAErB,QAAI,CAAC,KAAK;AAAQ,WAAK,SAAS,CAAE;AAElC,SAAK,WAAW,SAAU,KAAK;AAC7B,UAAI,aAAa,IAAI,UAAU,MAAM,OAAO;AAAA,IAClD,CAAK;AAED,UAAM,QAAQ,KAAK,OAAO,KAAK,OAAO,IAAI;AAC1C,UAAM,OAAO,IAAI,cAAc,KAAK;AACpC,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaD,uBAAuB,eAAe,WAAW;AAC/C,QAAI,KAAK,eAAe,qBAAqB;AAAG;AAEhD,QAAI,WAAW;AAEf,YAAQ,UAAU,MAAM,aAAW;AAAA,MACjC,KAAK;AACH,mBAAW;AAEX;AAAA,MAEF,KAAK;AACH,mBAAW;AAEX;AAAA,MAEF,KAAK;AACH,mBAAW;AAEX;AAAA,MAEF,KAAK;AACH,mBAAW;AAEX;AAAA,MAEF;AACE;AAAA,IACH;AAED,QAAI,UAAU;AAAY,kBAAY;AAEtC,UAAM,iBAAiB,cAAc,MAAM,KAAK,CAAC,EAAE,CAAC;AAEpD,QACE,qCAAqC,cAAc,KACnD,qCAAqC,cAAc,EAAE,SAAS,QAAQ,GACtE;AACA,WAAK,eAAe,qBAAqB,IAAI;AAC7C,WAAK,mBAAmB,qBAAqB,IAAI;AAAA,IAClD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,cAAc,QAAQ;AACpB,UAAM,OAAO,KAAK;AAElB,QAAI,CAAC,KAAK;AAAS,WAAK,UAAU,CAAE;AAEpC,UAAM,UAAU,OAAO;AAEvB,UAAM,YAAY;AAAA,MAChB,MAAM,UAAU,iBAAiB;AAAA,IAClC;AAED,QAAI,SAAS;AACX,gBAAU,eAAe;AAAA,QACvB,MAAM,OAAO,QAAQ;AAAA,QACrB,MAAM,OAAO,MAAM;AAAA,QACnB,MAAM,OAAO,OAAO,IAAI,OAAQ,OAAO;AAAA,QACvC,OAAO,OAAO,OAAO,IAAI,IAAI,OAAO;AAAA,MACrC;AAAA,IACP,OAAW;AACL,gBAAU,cAAc;AAAA,QACtB,aAAa,OAAO;AAAA,QACpB,MAAMP,MAAS,UAAC,SAAS,OAAO,GAAG;AAAA,QACnC,MAAM,OAAO,OAAO,IAAI,OAAQ,OAAO;AAAA,QACvC,OAAO,OAAO,OAAO,IAAI,IAAI,OAAO;AAAA,MACrC;AAAA,IACF;AAGD,QAAI,OAAO,SAAS;AAAI,gBAAU,OAAO,OAAO;AAEhD,WAAO,KAAK,QAAQ,KAAK,SAAS,IAAI;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYD,iBAAiB,MAAM,MAAM;AAC3B,UAAM,OAAO,KAAK;AAClB,UAAM,UAAU,KAAK;AAErB,QAAI,CAAC,KAAK;AAAY,WAAK,aAAa,CAAE;AAE1C,WAAO,aAAa,MAAM,uBAAuB,KAAK,MAAO,GAAE,IAAI;AAEnE,UAAM,SAAS,KAAK;AACpB,UAAM,WAAW,CAAE;AACnB,UAAM,WAAW,CAAE;AAEnB,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACtC,YAAM,QAAQ,OAAO,CAAC;AACtB,YAAM,eAAeZ,MAAe,gBAAC,eAAe,MAAM,IAAI;AAC9D,UAAI,YAAYA,MAAAA,gBAAgB,SAAS,MAAM,aAAa,QAAQ;AACpE,YAAM,gBAAgB,gBAAgB,aAAa,YAAY;AAE/D,UAAI,aAAa,eAAe,SAAS;AACvC,YAAI,UAAU,kBAAkB,MAAM;AACpC,sBAAY,UAAU,SAAS,cAAc,aAAa,WAAW;AAAA,QAC/E,OAAe;AACL,sBAAY;AAAA,QACb;AAAA,MACF;AAED,UAAI,CAAC,aAAa,CAAC,eAAe;AAChC,gBAAQ,KAAK,8DAA8D,MAAM,IAAI;AACrF,eAAO;AAAA,MACR;AAED,YAAM,gBAAgB;AACtB,UAAI,iBAAiB,MAAM,OAAO,SAAS,MAAM,MAAM;AAEvD,UAAI,kBAAkB,gBAAgB,uBAAuB;AAC3D,0BAAkB,UAAU,sBAAsB;AAAA,MACnD;AAED,UAAI;AAOJ,UAAI,MAAM,kBAAkB,8CAA8C,MAAM;AAC9E,wBAAgB;AAKhB,0BAAkB;AAAA,MACnB,WAAU,MAAM,iBAAkB,MAAKoB,2BAAqB;AAC3D,wBAAgB;AAAA,MACxB,OAAa;AACL,wBAAgB;AAAA,MACjB;AAED,eAAS,KAAK;AAAA,QACZ,OAAO,KAAK,gBAAgB,IAAID,MAAe,gBAAC,MAAM,OAAO,aAAa,CAAC;AAAA,QAC3E,QAAQ,KAAK,gBAAgB,IAAIA,MAAe,gBAAC,MAAM,QAAQ,cAAc,CAAC;AAAA,QAC9E;AAAA,MACR,CAAO;AAED,eAAS,KAAK;AAAA,QACZ,SAAS,SAAS,SAAS;AAAA,QAC3B,QAAQ;AAAA,UACN,MAAM,QAAQ,IAAI,SAAS;AAAA,UAC3B,MAAM;AAAA,QACP;AAAA,MACT,CAAO;AAAA,IACF;AAED,SAAK,WAAW,KAAK;AAAA,MACnB,MAAM,KAAK,QAAQ,UAAU,KAAK,WAAW;AAAA,MAC7C;AAAA,MACA;AAAA,IACN,CAAK;AAED,WAAO,KAAK,WAAW,SAAS;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,YAAY,QAAQ;AAClB,UAAM,OAAO,KAAK;AAClB,UAAM,UAAU,KAAK;AAErB,UAAM,OAAO,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC;AAE3C,UAAM,WAAW,OAAO;AAExB,QAAI,aAAa;AAAW,aAAO;AAEnC,UAAM,YAAY,OAAO,SAAS,MAAM,CAAC;AAEzC,QAAI,cAAc;AAAW,aAAO;AAEpC,UAAM,SAAS,CAAE;AACjB,UAAM,sBAAsB,IAAI,aAAa,SAAS,MAAM,SAAS,EAAE;AACvE,UAAM,uBAAuB,IAAIE,cAAS;AAE1C,aAAS,IAAI,GAAG,IAAI,SAAS,MAAM,QAAQ,EAAE,GAAG;AAC9C,aAAO,KAAK,QAAQ,IAAI,SAAS,MAAM,CAAC,CAAC,CAAC;AAC1C,2BAAqB,KAAK,SAAS,aAAa,CAAC,CAAC;AAClD,2BAAqB,SAAS,OAAO,UAAU,EAAE,QAAQ,qBAAqB,IAAI,EAAE;AAAA,IACrF;AAED,QAAI,KAAK,UAAU;AAAW,WAAK,QAAQ,CAAE;AAE7C,SAAK,MAAM,KAAK;AAAA,MACd,qBAAqB,KAAK,gBAAgB,IAAIF,MAAAA,gBAAgB,qBAAqB,EAAE,CAAC;AAAA,MACtF;AAAA,MACA,UAAU,QAAQ,IAAI,SAAS;AAAA,IACrC,CAAK;AAED,UAAM,YAAa,KAAK,OAAO,KAAK,MAAM,SAAS;AAEnD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,YAAY,QAAQ;AAClB,UAAM,OAAO,KAAK;AAClB,UAAM,UAAU,KAAK;AACrB,UAAM,UAAU,KAAK;AAErB,QAAI,CAAC,KAAK;AAAO,WAAK,QAAQ,CAAE;AAEhC,UAAM,UAAU,CAAE;AAElB,QAAI,QAAQ,KAAK;AACf,YAAM,WAAW,OAAO,WAAW,QAAS;AAC5C,YAAM,WAAW,OAAO,SAAS,QAAS;AAC1C,YAAM,QAAQ,OAAO,MAAM,QAAS;AAEpC,UAAI,CAAC,WAAW,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG;AACvC,gBAAQ,WAAW;AAAA,MACpB;AAED,UAAI,CAAC,WAAW,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG;AACpC,gBAAQ,cAAc;AAAA,MACvB;AAED,UAAI,CAAC,WAAW,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG;AACjC,gBAAQ,QAAQ;AAAA,MACjB;AAAA,IACP,OAAW;AACL,UAAI,OAAO,kBAAkB;AAC3B,eAAO,aAAc;AAAA,MACtB;AAED,UAAI,iBAAiB,OAAO,MAAM,MAAM,OAAO;AAC7C,gBAAQ,SAAS,OAAO,OAAO;AAAA,MAChC;AAAA,IACF;AAGD,QAAI,OAAO,SAAS;AAAI,cAAQ,OAAO,OAAO,OAAO,IAAI;AAEzD,SAAK,kBAAkB,QAAQ,OAAO;AAEtC,QAAI,OAAO,UAAU,OAAO,UAAU,OAAO,UAAU;AACrD,YAAM,YAAY,KAAK,YAAY,MAAM;AAEzC,UAAI,cAAc;AAAM,gBAAQ,OAAO;AAAA,IAC7C,WAAe,OAAO,UAAU;AAC1B,cAAQ,SAAS,KAAK,cAAc,MAAM;AAAA,IAC3C;AAED,QAAI,OAAO;AAAe,WAAK,MAAM,KAAK,MAAM;AAEhD,QAAI,OAAO,SAAS,SAAS,GAAG;AAC9B,YAAM,WAAW,CAAE;AAEnB,eAAS,IAAI,GAAG,IAAI,OAAO,SAAS,QAAQ,IAAI,GAAG,KAAK;AACtD,cAAM,QAAQ,OAAO,SAAS,CAAC;AAE/B,YAAI,MAAM,WAAW,QAAQ,gBAAgB,OAAO;AAClD,gBAAMG,aAAY,KAAK,YAAY,KAAK;AAExC,cAAIA,eAAc;AAAM,qBAAS,KAAKA,UAAS;AAAA,QAChD;AAAA,MACF;AAED,UAAI,SAAS,SAAS;AAAG,gBAAQ,WAAW;AAAA,IAC7C;AAED,SAAK,WAAW,SAAU,KAAK;AAC7B,UAAI,aAAa,IAAI,UAAU,QAAQ,OAAO;AAAA,IACpD,CAAK;AAED,UAAM,YAAY,KAAK,MAAM,KAAK,OAAO,IAAI;AAC7C,YAAQ,IAAI,QAAQ,SAAS;AAC7B,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,aAAa,OAAO;AAClB,UAAM,OAAO,KAAK;AAClB,UAAM,UAAU,KAAK;AAErB,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,SAAS,CAAE;AAChB,WAAK,QAAQ;AAAA,IACd;AAED,UAAM,WAAW,CAAE;AAEnB,QAAI,MAAM,SAAS;AAAI,eAAS,OAAO,MAAM;AAE7C,SAAK,OAAO,KAAK,QAAQ;AAEzB,UAAM,QAAQ,CAAE;AAEhB,aAAS,IAAI,GAAG,IAAI,MAAM,SAAS,QAAQ,IAAI,GAAG,KAAK;AACrD,YAAM,QAAQ,MAAM,SAAS,CAAC;AAE9B,UAAI,MAAM,WAAW,QAAQ,gBAAgB,OAAO;AAClD,cAAM,YAAY,KAAK,YAAY,KAAK;AAExC,YAAI,cAAc;AAAM,gBAAM,KAAK,SAAS;AAAA,MAC7C;AAAA,IACF;AAED,QAAI,MAAM,SAAS;AAAG,eAAS,QAAQ;AAEvC,SAAK,kBAAkB,OAAO,QAAQ;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,eAAe,SAAS;AACtB,UAAM,QAAQ,IAAI1B,YAAO;AACzB,UAAM,OAAO;AAEb,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AAGvC,YAAM,SAAS,KAAK,QAAQ,CAAC,CAAC;AAAA,IAC/B;AAED,SAAK,aAAa,KAAK;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAKD,aAAa,OAAO;AAClB,UAAM,UAAU,KAAK;AAErB,YAAQ,iBAAiB,QAAQ,QAAQ,CAAC,KAAK;AAE/C,SAAK,WAAW,SAAU,KAAK;AAC7B,UAAI,eAAe,IAAI,YAAY,KAAK;AAAA,IAC9C,CAAK;AAED,UAAM,sBAAsB,CAAE;AAE9B,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAI,MAAM,CAAC,aAAaA,aAAO;AAC7B,aAAK,aAAa,MAAM,CAAC,CAAC;AAAA,MAClC,OAAa;AACL,4BAAoB,KAAK,MAAM,CAAC,CAAC;AAAA,MAClC;AAAA,IACF;AAED,QAAI,oBAAoB,SAAS;AAAG,WAAK,eAAe,mBAAmB;AAE3E,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,EAAE,GAAG;AAC1C,WAAK,YAAY,KAAK,MAAM,CAAC,CAAC;AAAA,IAC/B;AAED,aAAS,IAAI,GAAG,IAAI,QAAQ,WAAW,QAAQ,EAAE,GAAG;AAClD,WAAK,iBAAiB,QAAQ,WAAW,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,IACtD;AAED,SAAK,WAAW,SAAU,KAAK;AAC7B,UAAI,cAAc,IAAI,WAAW,KAAK;AAAA,IAC5C,CAAK;AAAA,EACF;AAAA,EAED,WAAW,MAAM;AACf,aAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK;AACrD,WAAK,KAAK,QAAQ,CAAC,CAAC;AAAA,IACrB;AAAA,EACF;AACH;AAOA,MAAM,mBAAmB;AAAA,EACvB,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EACb;AAAA,EAED,UAAU,OAAO,SAAS;AACxB,QAAI,CAAC,MAAM;AAAS;AAEpB,QAAI,CAAC,MAAM,sBAAsB,CAAC,MAAM,gBAAgB,CAAC,MAAM,aAAa;AAC1E,cAAQ,KAAK,+EAA+E,KAAK;AACjG;AAAA,IACD;AAED,UAAM,SAAS,KAAK;AACpB,UAAM,OAAO,OAAO;AACpB,UAAM,iBAAiB,OAAO;AAE9B,UAAM,WAAW,CAAE;AAEnB,QAAI,MAAM;AAAM,eAAS,OAAO,MAAM;AAEtC,aAAS,QAAQ,MAAM,MAAM,QAAS;AAEtC,aAAS,YAAY,MAAM;AAE3B,QAAI,MAAM,oBAAoB;AAC5B,eAAS,OAAO;AAAA,IACtB,WAAe,MAAM,cAAc;AAC7B,eAAS,OAAO;AAEhB,UAAI,MAAM,WAAW;AAAG,iBAAS,QAAQ,MAAM;AAAA,IACrD,WAAe,MAAM,aAAa;AAC5B,eAAS,OAAO;AAEhB,UAAI,MAAM,WAAW;AAAG,iBAAS,QAAQ,MAAM;AAE/C,eAAS,OAAO,CAAE;AAClB,eAAS,KAAK,kBAAkB,MAAM,WAAW,KAAO,MAAM,QAAQ;AACtE,eAAS,KAAK,iBAAiB,MAAM;AAAA,IACtC;AAED,QAAI,MAAM,UAAU,UAAa,MAAM,UAAU,GAAG;AAClD,cAAQ;AAAA,QACN;AAAA,MACD;AAAA,IACF;AAED,QACE,MAAM,WACL,MAAM,OAAO,WAAW,SACvB,MAAM,OAAO,SAAS,MAAM,KAC5B,MAAM,OAAO,SAAS,MAAM,KAC5B,MAAM,OAAO,SAAS,MAAM,KAC9B;AACA,cAAQ;AAAA,QACN;AAAA,MAED;AAAA,IACF;AAED,QAAI,CAAC,eAAe,KAAK,IAAI,GAAG;AAC9B,WAAK,aAAa,KAAK,cAAc,CAAE;AACvC,WAAK,WAAW,KAAK,IAAI,IAAI,EAAE,QAAQ,GAAI;AAC3C,qBAAe,KAAK,IAAI,IAAI;AAAA,IAC7B;AAED,UAAM,SAAS,KAAK,WAAW,KAAK,IAAI,EAAE;AAC1C,WAAO,KAAK,QAAQ;AAEpB,YAAQ,aAAa,QAAQ,cAAc,CAAE;AAC7C,YAAQ,WAAW,KAAK,IAAI,IAAI,EAAE,OAAO,OAAO,SAAS,EAAG;AAAA,EAC7D;AACH;AAOA,MAAM,4BAA4B;AAAA,EAChC,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EACb;AAAA,EAED,cAAc,UAAU,aAAa;AACnC,QAAI,CAAC,SAAS;AAAqB;AAEnC,UAAM,SAAS,KAAK;AACpB,UAAM,iBAAiB,OAAO;AAE9B,gBAAY,aAAa,YAAY,cAAc,CAAE;AACrD,gBAAY,WAAW,KAAK,IAAI,IAAI,CAAE;AAEtC,mBAAe,KAAK,IAAI,IAAI;AAE5B,gBAAY,qBAAqB,iBAAiB;AAClD,gBAAY,qBAAqB,kBAAkB;AAAA,EACpD;AACH;AAOA,MAAM,gCAAgC;AAAA,EACpC,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EACb;AAAA,EAED,cAAc,UAAU,aAAa;AACnC,QAAI,CAAC,SAAS,0BAA0B,SAAS,cAAc;AAAG;AAElE,UAAM,SAAS,KAAK;AACpB,UAAM,iBAAiB,OAAO;AAE9B,UAAM,eAAe,CAAE;AAEvB,iBAAa,kBAAkB,SAAS;AAExC,QAAI,SAAS,cAAc;AACzB,YAAM,kBAAkB;AAAA,QACtB,OAAO,OAAO,eAAe,SAAS,YAAY;AAAA,QAClD,UAAU,SAAS,aAAa;AAAA,MACjC;AACD,aAAO,sBAAsB,iBAAiB,SAAS,YAAY;AACnE,mBAAa,mBAAmB;AAAA,IACjC;AAED,iBAAa,2BAA2B,SAAS;AAEjD,QAAI,SAAS,uBAAuB;AAClC,YAAM,2BAA2B;AAAA,QAC/B,OAAO,OAAO,eAAe,SAAS,qBAAqB;AAAA,QAC3D,UAAU,SAAS,sBAAsB;AAAA,MAC1C;AACD,aAAO,sBAAsB,0BAA0B,SAAS,qBAAqB;AACrF,mBAAa,4BAA4B;AAAA,IAC1C;AAED,QAAI,SAAS,oBAAoB;AAC/B,YAAM,wBAAwB;AAAA,QAC5B,OAAO,OAAO,eAAe,SAAS,kBAAkB;AAAA,QACxD,UAAU,SAAS,mBAAmB;AAAA,MACvC;AACD,aAAO,sBAAsB,uBAAuB,SAAS,kBAAkB;AAC/E,mBAAa,yBAAyB;AAAA,IACvC;AAED,gBAAY,aAAa,YAAY,cAAc,CAAE;AACrD,gBAAY,WAAW,KAAK,IAAI,IAAI;AAEpC,mBAAe,KAAK,IAAI,IAAI;AAAA,EAC7B;AACH;AAOA,MAAM,kCAAkC;AAAA,EACtC,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EACb;AAAA,EAED,cAAc,UAAU,aAAa;AACnC,QAAI,CAAC,SAAS,0BAA0B,SAAS,gBAAgB;AAAG;AAEpE,UAAM,SAAS,KAAK;AACpB,UAAM,iBAAiB,OAAO;AAE9B,UAAM,eAAe,CAAE;AAEvB,iBAAa,oBAAoB,SAAS;AAE1C,QAAI,SAAS,gBAAgB;AAC3B,YAAM,oBAAoB;AAAA,QACxB,OAAO,OAAO,eAAe,SAAS,cAAc;AAAA,QACpD,UAAU,SAAS,eAAe;AAAA,MACnC;AACD,aAAO,sBAAsB,mBAAmB,SAAS,cAAc;AACvE,mBAAa,qBAAqB;AAAA,IACnC;AAED,iBAAa,iBAAiB,SAAS;AACvC,iBAAa,8BAA8B,SAAS,0BAA0B,CAAC;AAC/E,iBAAa,8BAA8B,SAAS,0BAA0B,CAAC;AAE/E,QAAI,SAAS,yBAAyB;AACpC,YAAM,6BAA6B;AAAA,QACjC,OAAO,OAAO,eAAe,SAAS,uBAAuB;AAAA,QAC7D,UAAU,SAAS,wBAAwB;AAAA,MAC5C;AACD,aAAO,sBAAsB,4BAA4B,SAAS,uBAAuB;AACzF,mBAAa,8BAA8B;AAAA,IAC5C;AAED,gBAAY,aAAa,YAAY,cAAc,CAAE;AACrD,gBAAY,WAAW,KAAK,IAAI,IAAI;AAEpC,mBAAe,KAAK,IAAI,IAAI;AAAA,EAC7B;AACH;AAOA,MAAM,mCAAmC;AAAA,EACvC,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EACb;AAAA,EAED,cAAc,UAAU,aAAa;AACnC,QAAI,CAAC,SAAS,0BAA0B,SAAS,iBAAiB;AAAG;AAErE,UAAM,SAAS,KAAK;AACpB,UAAM,iBAAiB,OAAO;AAE9B,UAAM,eAAe,CAAE;AAEvB,iBAAa,qBAAqB,SAAS;AAE3C,QAAI,SAAS,iBAAiB;AAC5B,YAAM,qBAAqB;AAAA,QACzB,OAAO,OAAO,eAAe,SAAS,eAAe;AAAA,QACrD,UAAU,SAAS,gBAAgB;AAAA,MACpC;AACD,aAAO,sBAAsB,oBAAoB,SAAS,eAAe;AACzE,mBAAa,sBAAsB;AAAA,IACpC;AAED,gBAAY,aAAa,YAAY,cAAc,CAAE;AACrD,gBAAY,WAAW,KAAK,IAAI,IAAI;AAEpC,mBAAe,KAAK,IAAI,IAAI;AAAA,EAC7B;AACH;AAOA,MAAM,6BAA6B;AAAA,EACjC,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EACb;AAAA,EAED,cAAc,UAAU,aAAa;AACnC,QAAI,CAAC,SAAS,0BAA0B,SAAS,iBAAiB;AAAG;AAErE,UAAM,SAAS,KAAK;AACpB,UAAM,iBAAiB,OAAO;AAE9B,UAAM,eAAe,CAAE;AAEvB,iBAAa,kBAAkB,SAAS;AAExC,QAAI,SAAS,cAAc;AACzB,YAAM,kBAAkB;AAAA,QACtB,OAAO,OAAO,eAAe,SAAS,YAAY;AAAA,QAClD,UAAU,SAAS,aAAa;AAAA,MACjC;AACD,aAAO,sBAAsB,iBAAiB,SAAS,YAAY;AACnE,mBAAa,mBAAmB;AAAA,IACjC;AAED,iBAAa,sBAAsB,SAAS;AAC5C,iBAAa,mBAAmB,SAAS,iBAAiB,QAAS;AAEnE,gBAAY,aAAa,YAAY,cAAc,CAAE;AACrD,gBAAY,WAAW,KAAK,IAAI,IAAI;AAEpC,mBAAe,KAAK,IAAI,IAAI;AAAA,EAC7B;AACH;AAOA,MAAM,0BAA0B;AAAA,EAC9B,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EACb;AAAA,EAED,cAAc,UAAU,aAAa;AACnC,QAAI,CAAC,SAAS,0BAA0B,SAAS,QAAQ;AAAK;AAE9D,UAAM,SAAS,KAAK;AACpB,UAAM,iBAAiB,OAAO;AAE9B,UAAM,eAAe,CAAE;AAEvB,iBAAa,MAAM,SAAS;AAE5B,gBAAY,aAAa,YAAY,cAAc,CAAE;AACrD,gBAAY,WAAW,KAAK,IAAI,IAAI;AAEpC,mBAAe,KAAK,IAAI,IAAI;AAAA,EAC7B;AACH;AAOA,MAAM,+BAA+B;AAAA,EACnC,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EACb;AAAA,EAED,cAAc,UAAU,aAAa;AACnC,QACE,CAAC,SAAS,0BACT,SAAS,sBAAsB,KAC9B,SAAS,cAAc,OAAO,sBAAsB,KACpD,CAAC,SAAS,wBACV,CAAC,SAAS;AAEZ;AAEF,UAAM,SAAS,KAAK;AACpB,UAAM,iBAAiB,OAAO;AAE9B,UAAM,eAAe,CAAE;AAEvB,QAAI,SAAS,sBAAsB;AACjC,YAAM,0BAA0B;AAAA,QAC9B,OAAO,OAAO,eAAe,SAAS,oBAAoB;AAAA,QAC1D,UAAU,SAAS,qBAAqB;AAAA,MACzC;AACD,aAAO,sBAAsB,yBAAyB,SAAS,oBAAoB;AACnF,mBAAa,kBAAkB;AAAA,IAChC;AAED,QAAI,SAAS,kBAAkB;AAC7B,YAAM,sBAAsB;AAAA,QAC1B,OAAO,OAAO,eAAe,SAAS,gBAAgB;AAAA,QACtD,UAAU,SAAS,iBAAiB;AAAA,MACrC;AACD,aAAO,sBAAsB,qBAAqB,SAAS,gBAAgB;AAC3E,mBAAa,uBAAuB;AAAA,IACrC;AAED,iBAAa,iBAAiB,SAAS;AACvC,iBAAa,sBAAsB,SAAS,cAAc,QAAS;AAEnE,gBAAY,aAAa,YAAY,cAAc,CAAE;AACrD,gBAAY,WAAW,KAAK,IAAI,IAAI;AAEpC,mBAAe,KAAK,IAAI,IAAI;AAAA,EAC7B;AACH;AAOA,MAAM,4BAA4B;AAAA,EAChC,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EACb;AAAA,EAED,cAAc,UAAU,aAAa;AACnC,QAAI,CAAC,SAAS,0BAA0B,SAAS,SAAS;AAAK;AAE/D,UAAM,SAAS,KAAK;AACpB,UAAM,iBAAiB,OAAO;AAE9B,UAAM,eAAe,CAAE;AAEvB,QAAI,SAAS,mBAAmB;AAC9B,YAAM,uBAAuB;AAAA,QAC3B,OAAO,OAAO,eAAe,SAAS,iBAAiB;AAAA,QACvD,UAAU,SAAS,kBAAkB;AAAA,MACtC;AACD,aAAO,sBAAsB,sBAAsB,SAAS,iBAAiB;AAC7E,mBAAa,wBAAwB;AAAA,IACtC;AAED,QAAI,SAAS,eAAe;AAC1B,YAAM,mBAAmB;AAAA,QACvB,OAAO,OAAO,eAAe,SAAS,aAAa;AAAA,QACnD,UAAU,SAAS,cAAc;AAAA,MAClC;AACD,aAAO,sBAAsB,kBAAkB,SAAS,aAAa;AACrE,mBAAa,oBAAoB;AAAA,IAClC;AAED,iBAAa,uBAAuB,SAAS;AAC7C,iBAAa,mBAAmB,SAAS,WAAW,QAAS;AAE7D,gBAAY,aAAa,YAAY,cAAc,CAAE;AACrD,gBAAY,WAAW,KAAK,IAAI,IAAI;AAEpC,mBAAe,KAAK,IAAI,IAAI;AAAA,EAC7B;AACH;AAOA,MAAM,iCAAiC;AAAA,EACrC,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EACb;AAAA,EAED,cAAc,UAAU,aAAa;AACnC,QAAI,CAAC,SAAS,0BAA0B,SAAS,cAAc;AAAK;AAEpE,UAAM,SAAS,KAAK;AACpB,UAAM,iBAAiB,OAAO;AAE9B,UAAM,eAAe,CAAE;AAEvB,QAAI,SAAS,eAAe;AAC1B,YAAM,mBAAmB,EAAE,OAAO,OAAO,eAAe,SAAS,aAAa,EAAG;AACjF,aAAO,sBAAsB,kBAAkB,SAAS,aAAa;AACrE,mBAAa,oBAAoB;AAAA,IAClC;AAED,iBAAa,qBAAqB,SAAS;AAC3C,iBAAa,qBAAqB,SAAS;AAE3C,gBAAY,aAAa,YAAY,cAAc,CAAE;AACrD,gBAAY,WAAW,KAAK,IAAI,IAAI;AAEpC,mBAAe,KAAK,IAAI,IAAI;AAAA,EAC7B;AACH;AAOA,MAAM,uCAAuC;AAAA,EAC3C,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EACb;AAAA,EAED,cAAc,UAAU,aAAa;AACnC,QAAI,CAAC,SAAS,0BAA0B,SAAS,sBAAsB;AAAK;AAE5E,UAAM,SAAS,KAAK;AACpB,UAAM,iBAAiB,OAAO;AAE9B,UAAM,eAAe,CAAE;AAEvB,iBAAa,mBAAmB,SAAS;AAEzC,gBAAY,aAAa,YAAY,cAAc,CAAE;AACrD,gBAAY,WAAW,KAAK,IAAI,IAAI;AAEpC,mBAAe,KAAK,IAAI,IAAI;AAAA,EAC7B;AACH;;"}