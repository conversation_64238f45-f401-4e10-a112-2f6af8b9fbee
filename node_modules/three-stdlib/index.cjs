"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const MD2CharacterComplex = require("./misc/MD2CharacterComplex.cjs");
const ConvexObjectBreaker = require("./misc/ConvexObjectBreaker.cjs");
const MorphBlendMesh = require("./misc/MorphBlendMesh.cjs");
const GPUComputationRenderer = require("./misc/GPUComputationRenderer.cjs");
const Gyroscope = require("./misc/Gyroscope.cjs");
const MorphAnimMesh = require("./misc/MorphAnimMesh.cjs");
const RollerCoaster = require("./misc/RollerCoaster.cjs");
const Timer = require("./misc/Timer.cjs");
const WebGL = require("./misc/WebGL.cjs");
const MD2Character = require("./misc/MD2Character.cjs");
const VolumeSlice = require("./misc/VolumeSlice.cjs");
const TubePainter = require("./misc/TubePainter.cjs");
const Volume = require("./misc/Volume.cjs");
const ProgressiveLightmap = require("./misc/ProgressiveLightmap.cjs");
const CSS2DRenderer = require("./renderers/CSS2DRenderer.cjs");
const CSS3DRenderer = require("./renderers/CSS3DRenderer.cjs");
const Projector = require("./renderers/Projector.cjs");
const SVGRenderer = require("./renderers/SVGRenderer.cjs");
const FlakesTexture = require("./textures/FlakesTexture.cjs");
const CurveModifier = require("./modifiers/CurveModifier.cjs");
const SimplifyModifier = require("./modifiers/SimplifyModifier.cjs");
const EdgeSplitModifier = require("./modifiers/EdgeSplitModifier.cjs");
const TessellateModifier = require("./modifiers/TessellateModifier.cjs");
const GLTFExporter = require("./exporters/GLTFExporter.cjs");
const USDZExporter = require("./exporters/USDZExporter.cjs");
const PLYExporter = require("./exporters/PLYExporter.cjs");
const DRACOExporter = require("./exporters/DRACOExporter.cjs");
const ColladaExporter = require("./exporters/ColladaExporter.cjs");
const MMDExporter = require("./exporters/MMDExporter.cjs");
const STLExporter = require("./exporters/STLExporter.cjs");
const OBJExporter = require("./exporters/OBJExporter.cjs");
const RoomEnvironment = require("./environments/RoomEnvironment.cjs");
const AnimationClipCreator = require("./animation/AnimationClipCreator.cjs");
const CCDIKSolver = require("./animation/CCDIKSolver.cjs");
const MMDPhysics = require("./animation/MMDPhysics.cjs");
const MMDAnimationHelper = require("./animation/MMDAnimationHelper.cjs");
const BatchedMesh = require("./objects/BatchedMesh.cjs");
const Reflector = require("./objects/Reflector.cjs");
const Refractor = require("./objects/Refractor.cjs");
const ShadowMesh = require("./objects/ShadowMesh.cjs");
const Lensflare = require("./objects/Lensflare.cjs");
const Water = require("./objects/Water.cjs");
const MarchingCubes = require("./objects/MarchingCubes.cjs");
const LightningStorm = require("./objects/LightningStorm.cjs");
const ReflectorRTT = require("./objects/ReflectorRTT.cjs");
const ReflectorForSSRPass = require("./objects/ReflectorForSSRPass.cjs");
const Sky = require("./objects/Sky.cjs");
const Water2 = require("./objects/Water2.cjs");
const GroundProjectedEnv = require("./objects/GroundProjectedEnv.cjs");
const SceneUtils = require("./utils/SceneUtils.cjs");
const UVsDebug = require("./utils/UVsDebug.cjs");
const GeometryUtils = require("./utils/GeometryUtils.cjs");
const RoughnessMipmapper = require("./utils/RoughnessMipmapper.cjs");
const SkeletonUtils = require("./utils/SkeletonUtils.cjs");
const ShadowMapViewer = require("./utils/ShadowMapViewer.cjs");
const BufferGeometryUtils = require("./utils/BufferGeometryUtils.cjs");
const GeometryCompressionUtils = require("./utils/GeometryCompressionUtils.cjs");
const CinematicCamera = require("./cameras/CinematicCamera.cjs");
const ConvexHull = require("./math/ConvexHull.cjs");
const MeshSurfaceSampler = require("./math/MeshSurfaceSampler.cjs");
const SimplexNoise = require("./math/SimplexNoise.cjs");
const OBB = require("./math/OBB.cjs");
const Capsule = require("./math/Capsule.cjs");
const ColorConverter = require("./math/ColorConverter.cjs");
const ImprovedNoise = require("./math/ImprovedNoise.cjs");
const Octree = require("./math/Octree.cjs");
const Lut = require("./math/Lut.cjs");
const CameraControls = require("./controls/experimental/CameraControls.cjs");
const FirstPersonControls = require("./controls/FirstPersonControls.cjs");
const TransformControls = require("./controls/TransformControls.cjs");
const DragControls = require("./controls/DragControls.cjs");
const PointerLockControls = require("./controls/PointerLockControls.cjs");
const DeviceOrientationControls = require("./controls/DeviceOrientationControls.cjs");
const TrackballControls = require("./controls/TrackballControls.cjs");
const OrbitControls = require("./controls/OrbitControls.cjs");
const ArcballControls = require("./controls/ArcballControls.cjs");
const FlyControls = require("./controls/FlyControls.cjs");
const LUTPass = require("./postprocessing/LUTPass.cjs");
const ClearPass = require("./postprocessing/ClearPass.cjs");
const GlitchPass = require("./postprocessing/GlitchPass.cjs");
const HalftonePass = require("./postprocessing/HalftonePass.cjs");
const SMAAPass = require("./postprocessing/SMAAPass.cjs");
const FilmPass = require("./postprocessing/FilmPass.cjs");
const OutlinePass = require("./postprocessing/OutlinePass.cjs");
const SSAOPass = require("./postprocessing/SSAOPass.cjs");
const SavePass = require("./postprocessing/SavePass.cjs");
const BokehPass = require("./postprocessing/BokehPass.cjs");
const Pass = require("./postprocessing/Pass.cjs");
const TexturePass = require("./postprocessing/TexturePass.cjs");
const AdaptiveToneMappingPass = require("./postprocessing/AdaptiveToneMappingPass.cjs");
const UnrealBloomPass = require("./postprocessing/UnrealBloomPass.cjs");
const CubeTexturePass = require("./postprocessing/CubeTexturePass.cjs");
const SAOPass = require("./postprocessing/SAOPass.cjs");
const AfterimagePass = require("./postprocessing/AfterimagePass.cjs");
const MaskPass = require("./postprocessing/MaskPass.cjs");
const EffectComposer = require("./postprocessing/EffectComposer.cjs");
const DotScreenPass = require("./postprocessing/DotScreenPass.cjs");
const SSRPass = require("./postprocessing/SSRPass.cjs");
const TAARenderPass = require("./postprocessing/TAARenderPass.cjs");
const ShaderPass = require("./postprocessing/ShaderPass.cjs");
const SSAARenderPass = require("./postprocessing/SSAARenderPass.cjs");
const RenderPass = require("./postprocessing/RenderPass.cjs");
const RenderPixelatedPass = require("./postprocessing/RenderPixelatedPass.cjs");
const BloomPass = require("./postprocessing/BloomPass.cjs");
const WaterPass = require("./postprocessing/WaterPass.cjs");
const ARButton = require("./webxr/ARButton.cjs");
const OculusHandModel = require("./webxr/OculusHandModel.cjs");
const OculusHandPointerModel = require("./webxr/OculusHandPointerModel.cjs");
const Text2D = require("./webxr/Text2D.cjs");
const VRButton = require("./webxr/VRButton.cjs");
const XRControllerModelFactory = require("./webxr/XRControllerModelFactory.cjs");
const XREstimatedLight = require("./webxr/XREstimatedLight.cjs");
const XRHandMeshModel = require("./webxr/XRHandMeshModel.cjs");
const XRHandModelFactory = require("./webxr/XRHandModelFactory.cjs");
const XRHandPrimitiveModel = require("./webxr/XRHandPrimitiveModel.cjs");
const ParametricGeometries = require("./geometries/ParametricGeometries.cjs");
const ParametricGeometry = require("./geometries/ParametricGeometry.cjs");
const ConvexGeometry = require("./geometries/ConvexGeometry.cjs");
const LightningStrike = require("./geometries/LightningStrike.cjs");
const RoundedBoxGeometry = require("./geometries/RoundedBoxGeometry.cjs");
const BoxLineGeometry = require("./geometries/BoxLineGeometry.cjs");
const DecalGeometry = require("./geometries/DecalGeometry.cjs");
const TeapotGeometry = require("./geometries/TeapotGeometry.cjs");
const TextGeometry = require("./geometries/TextGeometry.cjs");
const CSM = require("./csm/CSM.cjs");
const CSMFrustum = require("./csm/CSMFrustum.cjs");
const CSMHelper = require("./csm/CSMHelper.cjs");
const CSMShader = require("./csm/CSMShader.cjs");
const ACESFilmicToneMappingShader = require("./shaders/ACESFilmicToneMappingShader.cjs");
const AfterimageShader = require("./shaders/AfterimageShader.cjs");
const BasicShader = require("./shaders/BasicShader.cjs");
const BleachBypassShader = require("./shaders/BleachBypassShader.cjs");
const BlendShader = require("./shaders/BlendShader.cjs");
const BokehShader = require("./shaders/BokehShader.cjs");
const BokehShader2 = require("./shaders/BokehShader2.cjs");
const BrightnessContrastShader = require("./shaders/BrightnessContrastShader.cjs");
const ColorCorrectionShader = require("./shaders/ColorCorrectionShader.cjs");
const ColorifyShader = require("./shaders/ColorifyShader.cjs");
const ConvolutionShader = require("./shaders/ConvolutionShader.cjs");
const CopyShader = require("./shaders/CopyShader.cjs");
const DOFMipMapShader = require("./shaders/DOFMipMapShader.cjs");
const DepthLimitedBlurShader = require("./shaders/DepthLimitedBlurShader.cjs");
const DigitalGlitch = require("./shaders/DigitalGlitch.cjs");
const DotScreenShader = require("./shaders/DotScreenShader.cjs");
const FXAAShader = require("./shaders/FXAAShader.cjs");
const FilmShader = require("./shaders/FilmShader.cjs");
const FocusShader = require("./shaders/FocusShader.cjs");
const FreiChenShader = require("./shaders/FreiChenShader.cjs");
const FresnelShader = require("./shaders/FresnelShader.cjs");
const GammaCorrectionShader = require("./shaders/GammaCorrectionShader.cjs");
const GodRaysShader = require("./shaders/GodRaysShader.cjs");
const HalftoneShader = require("./shaders/HalftoneShader.cjs");
const HorizontalBlurShader = require("./shaders/HorizontalBlurShader.cjs");
const HorizontalTiltShiftShader = require("./shaders/HorizontalTiltShiftShader.cjs");
const HueSaturationShader = require("./shaders/HueSaturationShader.cjs");
const KaleidoShader = require("./shaders/KaleidoShader.cjs");
const LuminosityHighPassShader = require("./shaders/LuminosityHighPassShader.cjs");
const LuminosityShader = require("./shaders/LuminosityShader.cjs");
const MirrorShader = require("./shaders/MirrorShader.cjs");
const NormalMapShader = require("./shaders/NormalMapShader.cjs");
const ParallaxShader = require("./shaders/ParallaxShader.cjs");
const PixelShader = require("./shaders/PixelShader.cjs");
const RGBShiftShader = require("./shaders/RGBShiftShader.cjs");
const SAOShader = require("./shaders/SAOShader.cjs");
const SMAAShader = require("./shaders/SMAAShader.cjs");
const SSAOShader = require("./shaders/SSAOShader.cjs");
const SSRShader = require("./shaders/SSRShader.cjs");
const SepiaShader = require("./shaders/SepiaShader.cjs");
const SobelOperatorShader = require("./shaders/SobelOperatorShader.cjs");
const SubsurfaceScatteringShader = require("./shaders/SubsurfaceScatteringShader.cjs");
const TechnicolorShader = require("./shaders/TechnicolorShader.cjs");
const ToneMapShader = require("./shaders/ToneMapShader.cjs");
const ToonShader = require("./shaders/ToonShader.cjs");
const TriangleBlurShader = require("./shaders/TriangleBlurShader.cjs");
const UnpackDepthRGBAShader = require("./shaders/UnpackDepthRGBAShader.cjs");
const VerticalBlurShader = require("./shaders/VerticalBlurShader.cjs");
const VerticalTiltShiftShader = require("./shaders/VerticalTiltShiftShader.cjs");
const VignetteShader = require("./shaders/VignetteShader.cjs");
const VolumeShader = require("./shaders/VolumeShader.cjs");
const WaterRefractionShader = require("./shaders/WaterRefractionShader.cjs");
const HTMLMesh = require("./interactive/HTMLMesh.cjs");
const InteractiveGroup = require("./interactive/InteractiveGroup.cjs");
const SelectionHelper = require("./interactive/SelectionHelper.cjs");
const SelectionBox = require("./interactive/SelectionBox.cjs");
const AmmoPhysics = require("./physics/AmmoPhysics.cjs");
const ParallaxBarrierEffect = require("./effects/ParallaxBarrierEffect.cjs");
const PeppersGhostEffect = require("./effects/PeppersGhostEffect.cjs");
const OutlineEffect = require("./effects/OutlineEffect.cjs");
const AnaglyphEffect = require("./effects/AnaglyphEffect.cjs");
const AsciiEffect = require("./effects/AsciiEffect.cjs");
const StereoEffect = require("./effects/StereoEffect.cjs");
const FBXLoader = require("./loaders/FBXLoader.cjs");
const FontLoader = require("./loaders/FontLoader.cjs");
const TGALoader = require("./loaders/TGALoader.cjs");
const LUTCubeLoader = require("./loaders/LUTCubeLoader.cjs");
const NRRDLoader = require("./loaders/NRRDLoader.cjs");
const STLLoader = require("./loaders/STLLoader.cjs");
const MTLLoader = require("./loaders/MTLLoader.cjs");
const XLoader = require("./loaders/XLoader.cjs");
const BVHLoader = require("./loaders/BVHLoader.cjs");
const KMZLoader = require("./loaders/KMZLoader.cjs");
const VRMLoader = require("./loaders/VRMLoader.cjs");
const VRMLLoader = require("./loaders/VRMLLoader.cjs");
const KTX2Loader = require("./loaders/KTX2Loader.cjs");
const LottieLoader = require("./loaders/LottieLoader.cjs");
const TTFLoader = require("./loaders/TTFLoader.cjs");
const RGBELoader = require("./loaders/RGBELoader.cjs");
const AssimpLoader = require("./loaders/AssimpLoader.cjs");
const ColladaLoader = require("./loaders/ColladaLoader.cjs");
const MDDLoader = require("./loaders/MDDLoader.cjs");
const EXRLoader = require("./loaders/EXRLoader.cjs");
const _3MFLoader = require("./loaders/3MFLoader.cjs");
const XYZLoader = require("./loaders/XYZLoader.cjs");
const VTKLoader = require("./loaders/VTKLoader.cjs");
const LUT3dlLoader = require("./loaders/LUT3dlLoader.cjs");
const DDSLoader = require("./loaders/DDSLoader.cjs");
const PVRLoader = require("./loaders/PVRLoader.cjs");
const GCodeLoader = require("./loaders/GCodeLoader.cjs");
const BasisTextureLoader = require("./loaders/BasisTextureLoader.cjs");
const TDSLoader = require("./loaders/TDSLoader.cjs");
const LDrawLoader = require("./loaders/LDrawLoader.cjs");
const GLTFLoader = require("./loaders/GLTFLoader.cjs");
const SVGLoader = require("./loaders/SVGLoader.cjs");
const _3DMLoader = require("./loaders/3DMLoader.cjs");
const OBJLoader = require("./loaders/OBJLoader.cjs");
const AMFLoader = require("./loaders/AMFLoader.cjs");
const MMDLoader = require("./loaders/MMDLoader.cjs");
const MD2Loader = require("./loaders/MD2Loader.cjs");
const KTXLoader = require("./loaders/KTXLoader.cjs");
const TiltLoader = require("./loaders/TiltLoader.cjs");
const DRACOLoader = require("./loaders/DRACOLoader.cjs");
const HDRCubeTextureLoader = require("./loaders/HDRCubeTextureLoader.cjs");
const PDBLoader = require("./loaders/PDBLoader.cjs");
const PRWMLoader = require("./loaders/PRWMLoader.cjs");
const RGBMLoader = require("./loaders/RGBMLoader.cjs");
const VOXLoader = require("./loaders/VOXLoader.cjs");
const PCDLoader = require("./loaders/PCDLoader.cjs");
const LWOLoader = require("./loaders/LWOLoader.cjs");
const PLYLoader = require("./loaders/PLYLoader.cjs");
const LineSegmentsGeometry = require("./lines/LineSegmentsGeometry.cjs");
const LineGeometry = require("./lines/LineGeometry.cjs");
const Wireframe = require("./lines/Wireframe.cjs");
const WireframeGeometry2 = require("./lines/WireframeGeometry2.cjs");
const Line2 = require("./lines/Line2.cjs");
const LineMaterial = require("./lines/LineMaterial.cjs");
const LineSegments2 = require("./lines/LineSegments2.cjs");
const LightProbeHelper = require("./helpers/LightProbeHelper.cjs");
const RaycasterHelper = require("./helpers/RaycasterHelper.cjs");
const VertexTangentsHelper = require("./helpers/VertexTangentsHelper.cjs");
const PositionalAudioHelper = require("./helpers/PositionalAudioHelper.cjs");
const VertexNormalsHelper = require("./helpers/VertexNormalsHelper.cjs");
const RectAreaLightHelper = require("./helpers/RectAreaLightHelper.cjs");
const RectAreaLightUniformsLib = require("./lights/RectAreaLightUniformsLib.cjs");
const LightProbeGenerator = require("./lights/LightProbeGenerator.cjs");
const NURBSUtils = require("./curves/NURBSUtils.cjs");
const NURBSCurve = require("./curves/NURBSCurve.cjs");
const NURBSSurface = require("./curves/NURBSSurface.cjs");
const CurveExtras = require("./curves/CurveExtras.cjs");
const Geometry = require("./deprecated/Geometry.cjs");
const MeshoptDecoder = require("./libs/MeshoptDecoder.cjs");
const MotionControllers = require("./libs/MotionControllers.cjs");
exports.MD2CharacterComplex = MD2CharacterComplex.MD2CharacterComplex;
exports.ConvexObjectBreaker = ConvexObjectBreaker.ConvexObjectBreaker;
exports.MorphBlendMesh = MorphBlendMesh.MorphBlendMesh;
exports.GPUComputationRenderer = GPUComputationRenderer.GPUComputationRenderer;
exports.Gyroscope = Gyroscope.Gyroscope;
exports.MorphAnimMesh = MorphAnimMesh.MorphAnimMesh;
exports.RollerCoasterGeometry = RollerCoaster.RollerCoasterGeometry;
exports.RollerCoasterLiftersGeometry = RollerCoaster.RollerCoasterLiftersGeometry;
exports.RollerCoasterShadowGeometry = RollerCoaster.RollerCoasterShadowGeometry;
exports.SkyGeometry = RollerCoaster.SkyGeometry;
exports.TreesGeometry = RollerCoaster.TreesGeometry;
exports.Timer = Timer.Timer;
exports.getErrorMessage = WebGL.getErrorMessage;
exports.getWebGL2ErrorMessage = WebGL.getWebGL2ErrorMessage;
exports.getWebGLErrorMessage = WebGL.getWebGLErrorMessage;
exports.isWebGL2Available = WebGL.isWebGL2Available;
exports.isWebGLAvailable = WebGL.isWebGLAvailable;
exports.MD2Character = MD2Character.MD2Character;
exports.VolumeSlice = VolumeSlice.VolumeSlice;
exports.TubePainter = TubePainter.TubePainter;
exports.Volume = Volume.Volume;
exports.ProgressiveLightMap = ProgressiveLightmap.ProgressiveLightMap;
exports.CSS2DObject = CSS2DRenderer.CSS2DObject;
exports.CSS2DRenderer = CSS2DRenderer.CSS2DRenderer;
exports.CSS3DObject = CSS3DRenderer.CSS3DObject;
exports.CSS3DRenderer = CSS3DRenderer.CSS3DRenderer;
exports.CSS3DSprite = CSS3DRenderer.CSS3DSprite;
exports.Projector = Projector.Projector;
exports.RenderableFace = Projector.RenderableFace;
exports.RenderableLine = Projector.RenderableLine;
exports.RenderableObject = Projector.RenderableObject;
exports.RenderableSprite = Projector.RenderableSprite;
exports.RenderableVertex = Projector.RenderableVertex;
exports.SVGObject = SVGRenderer.SVGObject;
exports.SVGRenderer = SVGRenderer.SVGRenderer;
exports.FlakesTexture = FlakesTexture.FlakesTexture;
exports.Flow = CurveModifier.Flow;
exports.InstancedFlow = CurveModifier.InstancedFlow;
exports.getUniforms = CurveModifier.getUniforms;
exports.initSplineTexture = CurveModifier.initSplineTexture;
exports.modifyShader = CurveModifier.modifyShader;
exports.updateSplineTexture = CurveModifier.updateSplineTexture;
exports.SimplifyModifier = SimplifyModifier.SimplifyModifier;
exports.EdgeSplitModifier = EdgeSplitModifier.EdgeSplitModifier;
exports.TessellateModifier = TessellateModifier.TessellateModifier;
exports.GLTFExporter = GLTFExporter.GLTFExporter;
exports.USDZExporter = USDZExporter.USDZExporter;
exports.PLYExporter = PLYExporter.PLYExporter;
exports.DRACOExporter = DRACOExporter.DRACOExporter;
exports.ColladaExporter = ColladaExporter.ColladaExporter;
exports.MMDExporter = MMDExporter.MMDExporter;
exports.STLExporter = STLExporter.STLExporter;
exports.OBJExporter = OBJExporter.OBJExporter;
exports.RoomEnvironment = RoomEnvironment.RoomEnvironment;
exports.AnimationClipCreator = AnimationClipCreator.AnimationClipCreator;
exports.CCDIKHelper = CCDIKSolver.CCDIKHelper;
exports.CCDIKSolver = CCDIKSolver.CCDIKSolver;
exports.MMDPhysics = MMDPhysics.MMDPhysics;
exports.MMDAnimationHelper = MMDAnimationHelper.MMDAnimationHelper;
exports.BatchedMesh = BatchedMesh.BatchedMesh;
exports.Reflector = Reflector.Reflector;
exports.Refractor = Refractor.Refractor;
exports.ShadowMesh = ShadowMesh.ShadowMesh;
exports.Lensflare = Lensflare.Lensflare;
exports.LensflareElement = Lensflare.LensflareElement;
exports.Water = Water.Water;
exports.MarchingCubes = MarchingCubes.MarchingCubes;
exports.edgeTable = MarchingCubes.edgeTable;
exports.triTable = MarchingCubes.triTable;
exports.LightningStorm = LightningStorm.LightningStorm;
exports.ReflectorRTT = ReflectorRTT.ReflectorRTT;
exports.ReflectorForSSRPass = ReflectorForSSRPass.ReflectorForSSRPass;
exports.Sky = Sky.Sky;
exports.Water2 = Water2.Water2;
exports.GroundProjectedEnv = GroundProjectedEnv.GroundProjectedEnv;
exports.SceneUtils = SceneUtils.SceneUtils;
exports.UVsDebug = UVsDebug.UVsDebug;
exports.GeometryUtils = GeometryUtils.GeometryUtils;
exports.RoughnessMipmapper = RoughnessMipmapper.RoughnessMipmapper;
exports.SkeletonUtils = SkeletonUtils.SkeletonUtils;
exports.ShadowMapViewer = ShadowMapViewer.ShadowMapViewer;
exports.computeMorphedAttributes = BufferGeometryUtils.computeMorphedAttributes;
exports.estimateBytesUsed = BufferGeometryUtils.estimateBytesUsed;
exports.interleaveAttributes = BufferGeometryUtils.interleaveAttributes;
exports.mergeBufferAttributes = BufferGeometryUtils.mergeBufferAttributes;
exports.mergeBufferGeometries = BufferGeometryUtils.mergeBufferGeometries;
exports.mergeVertices = BufferGeometryUtils.mergeVertices;
exports.toCreasedNormals = BufferGeometryUtils.toCreasedNormals;
exports.toTrianglesDrawMode = BufferGeometryUtils.toTrianglesDrawMode;
exports.GeometryCompressionUtils = GeometryCompressionUtils.GeometryCompressionUtils;
exports.PackedPhongMaterial = GeometryCompressionUtils.PackedPhongMaterial;
exports.CinematicCamera = CinematicCamera.CinematicCamera;
exports.ConvexHull = ConvexHull.ConvexHull;
exports.Face = ConvexHull.Face;
exports.HalfEdge = ConvexHull.HalfEdge;
exports.VertexList = ConvexHull.VertexList;
exports.VertexNode = ConvexHull.VertexNode;
exports.MeshSurfaceSampler = MeshSurfaceSampler.MeshSurfaceSampler;
exports.SimplexNoise = SimplexNoise.SimplexNoise;
exports.OBB = OBB.OBB;
exports.Capsule = Capsule.Capsule;
exports.ColorConverter = ColorConverter.ColorConverter;
exports.ImprovedNoise = ImprovedNoise.ImprovedNoise;
exports.Octree = Octree.Octree;
exports.ColorMapKeywords = Lut.ColorMapKeywords;
exports.Lut = Lut.Lut;
exports.CameraControls = CameraControls.CameraControls;
exports.MapControlsExp = CameraControls.MapControlsExp;
exports.OrbitControlsExp = CameraControls.OrbitControlsExp;
exports.STATE = CameraControls.STATE;
exports.TrackballControlsExp = CameraControls.TrackballControlsExp;
exports.FirstPersonControls = FirstPersonControls.FirstPersonControls;
exports.TransformControls = TransformControls.TransformControls;
exports.TransformControlsGizmo = TransformControls.TransformControlsGizmo;
exports.TransformControlsPlane = TransformControls.TransformControlsPlane;
exports.DragControls = DragControls.DragControls;
exports.PointerLockControls = PointerLockControls.PointerLockControls;
exports.DeviceOrientationControls = DeviceOrientationControls.DeviceOrientationControls;
exports.TrackballControls = TrackballControls.TrackballControls;
exports.MapControls = OrbitControls.MapControls;
exports.OrbitControls = OrbitControls.OrbitControls;
exports.ArcballControls = ArcballControls.ArcballControls;
exports.FlyControls = FlyControls.FlyControls;
exports.LUTPass = LUTPass.LUTPass;
exports.ClearPass = ClearPass.ClearPass;
exports.GlitchPass = GlitchPass.GlitchPass;
exports.HalftonePass = HalftonePass.HalftonePass;
exports.SMAAPass = SMAAPass.SMAAPass;
exports.FilmPass = FilmPass.FilmPass;
exports.OutlinePass = OutlinePass.OutlinePass;
exports.SSAOPass = SSAOPass.SSAOPass;
exports.SavePass = SavePass.SavePass;
exports.BokehPass = BokehPass.BokehPass;
exports.FullScreenQuad = Pass.FullScreenQuad;
exports.Pass = Pass.Pass;
exports.TexturePass = TexturePass.TexturePass;
exports.AdaptiveToneMappingPass = AdaptiveToneMappingPass.AdaptiveToneMappingPass;
exports.UnrealBloomPass = UnrealBloomPass.UnrealBloomPass;
exports.CubeTexturePass = CubeTexturePass.CubeTexturePass;
exports.SAOPass = SAOPass.SAOPass;
exports.AfterimagePass = AfterimagePass.AfterimagePass;
exports.ClearMaskPass = MaskPass.ClearMaskPass;
exports.MaskPass = MaskPass.MaskPass;
exports.EffectComposer = EffectComposer.EffectComposer;
exports.DotScreenPass = DotScreenPass.DotScreenPass;
exports.SSRPass = SSRPass.SSRPass;
exports.TAARenderPass = TAARenderPass.TAARenderPass;
exports.ShaderPass = ShaderPass.ShaderPass;
exports.SSAARenderPass = SSAARenderPass.SSAARenderPass;
exports.RenderPass = RenderPass.RenderPass;
exports.RenderPixelatedPass = RenderPixelatedPass.RenderPixelatedPass;
exports.BloomPass = BloomPass.BloomPass;
exports.WaterPass = WaterPass.WaterPass;
exports.ARButton = ARButton.ARButton;
exports.OculusHandModel = OculusHandModel.OculusHandModel;
exports.OculusHandPointerModel = OculusHandPointerModel.OculusHandPointerModel;
exports.createText = Text2D.createText;
exports.VRButton = VRButton.VRButton;
exports.XRControllerModelFactory = XRControllerModelFactory.XRControllerModelFactory;
exports.XREstimatedLight = XREstimatedLight.XREstimatedLight;
exports.XRHandMeshModel = XRHandMeshModel.XRHandMeshModel;
exports.XRHandModelFactory = XRHandModelFactory.XRHandModelFactory;
exports.XRHandPrimitiveModel = XRHandPrimitiveModel.XRHandPrimitiveModel;
exports.ParametricGeometries = ParametricGeometries.ParametricGeometries;
exports.ParametricGeometry = ParametricGeometry.ParametricGeometry;
exports.ConvexGeometry = ConvexGeometry.ConvexGeometry;
exports.LightningStrike = LightningStrike.LightningStrike;
exports.RoundedBoxGeometry = RoundedBoxGeometry.RoundedBoxGeometry;
exports.BoxLineGeometry = BoxLineGeometry.BoxLineGeometry;
exports.DecalGeometry = DecalGeometry.DecalGeometry;
exports.DecalVertex = DecalGeometry.DecalVertex;
exports.TeapotGeometry = TeapotGeometry.TeapotGeometry;
exports.TextBufferGeometry = TextGeometry.TextGeometry;
exports.TextGeometry = TextGeometry.TextGeometry;
exports.CSM = CSM.CSM;
exports.CSMFrustum = CSMFrustum.CSMFrustum;
exports.CSMHelper = CSMHelper.CSMHelper;
exports.CSMShader = CSMShader.CSMShader;
exports.ACESFilmicToneMappingShader = ACESFilmicToneMappingShader.ACESFilmicToneMappingShader;
exports.AfterimageShader = AfterimageShader.AfterimageShader;
exports.BasicShader = BasicShader.BasicShader;
exports.BleachBypassShader = BleachBypassShader.BleachBypassShader;
exports.BlendShader = BlendShader.BlendShader;
exports.BokehShader = BokehShader.BokehShader;
exports.BokehDepthShader = BokehShader2.BokehDepthShader;
exports.BokehShader2 = BokehShader2.BokehShader2;
exports.BrightnessContrastShader = BrightnessContrastShader.BrightnessContrastShader;
exports.ColorCorrectionShader = ColorCorrectionShader.ColorCorrectionShader;
exports.ColorifyShader = ColorifyShader.ColorifyShader;
exports.ConvolutionShader = ConvolutionShader.ConvolutionShader;
exports.CopyShader = CopyShader.CopyShader;
exports.DOFMipMapShader = DOFMipMapShader.DOFMipMapShader;
exports.BlurShaderUtils = DepthLimitedBlurShader.BlurShaderUtils;
exports.DepthLimitedBlurShader = DepthLimitedBlurShader.DepthLimitedBlurShader;
exports.DigitalGlitch = DigitalGlitch.DigitalGlitch;
exports.DotScreenShader = DotScreenShader.DotScreenShader;
exports.FXAAShader = FXAAShader.FXAAShader;
exports.FilmShader = FilmShader.FilmShader;
exports.FocusShader = FocusShader.FocusShader;
exports.FreiChenShader = FreiChenShader.FreiChenShader;
exports.FresnelShader = FresnelShader.FresnelShader;
exports.GammaCorrectionShader = GammaCorrectionShader.GammaCorrectionShader;
exports.GodRaysCombineShader = GodRaysShader.GodRaysCombineShader;
exports.GodRaysDepthMaskShader = GodRaysShader.GodRaysDepthMaskShader;
exports.GodRaysFakeSunShader = GodRaysShader.GodRaysFakeSunShader;
exports.GodRaysGenerateShader = GodRaysShader.GodRaysGenerateShader;
exports.HalftoneShader = HalftoneShader.HalftoneShader;
exports.HorizontalBlurShader = HorizontalBlurShader.HorizontalBlurShader;
exports.HorizontalTiltShiftShader = HorizontalTiltShiftShader.HorizontalTiltShiftShader;
exports.HueSaturationShader = HueSaturationShader.HueSaturationShader;
exports.KaleidoShader = KaleidoShader.KaleidoShader;
exports.LuminosityHighPassShader = LuminosityHighPassShader.LuminosityHighPassShader;
exports.LuminosityShader = LuminosityShader.LuminosityShader;
exports.MirrorShader = MirrorShader.MirrorShader;
exports.NormalMapShader = NormalMapShader.NormalMapShader;
exports.ParallaxShader = ParallaxShader.ParallaxShader;
exports.PixelShader = PixelShader.PixelShader;
exports.RGBShiftShader = RGBShiftShader.RGBShiftShader;
exports.SAOShader = SAOShader.SAOShader;
exports.SMAABlendShader = SMAAShader.SMAABlendShader;
exports.SMAAEdgesShader = SMAAShader.SMAAEdgesShader;
exports.SMAAWeightsShader = SMAAShader.SMAAWeightsShader;
exports.SSAOBlurShader = SSAOShader.SSAOBlurShader;
exports.SSAODepthShader = SSAOShader.SSAODepthShader;
exports.SSAOShader = SSAOShader.SSAOShader;
exports.SSRBlurShader = SSRShader.SSRBlurShader;
exports.SSRDepthShader = SSRShader.SSRDepthShader;
exports.SSRShader = SSRShader.SSRShader;
exports.SepiaShader = SepiaShader.SepiaShader;
exports.SobelOperatorShader = SobelOperatorShader.SobelOperatorShader;
exports.SubsurfaceScatteringShader = SubsurfaceScatteringShader.SubsurfaceScatteringShader;
exports.TechnicolorShader = TechnicolorShader.TechnicolorShader;
exports.ToneMapShader = ToneMapShader.ToneMapShader;
exports.ToonShader1 = ToonShader.ToonShader1;
exports.ToonShader2 = ToonShader.ToonShader2;
exports.ToonShaderDotted = ToonShader.ToonShaderDotted;
exports.ToonShaderHatching = ToonShader.ToonShaderHatching;
exports.TriangleBlurShader = TriangleBlurShader.TriangleBlurShader;
exports.UnpackDepthRGBAShader = UnpackDepthRGBAShader.UnpackDepthRGBAShader;
exports.VerticalBlurShader = VerticalBlurShader.VerticalBlurShader;
exports.VerticalTiltShiftShader = VerticalTiltShiftShader.VerticalTiltShiftShader;
exports.VignetteShader = VignetteShader.VignetteShader;
exports.VolumeRenderShader1 = VolumeShader.VolumeRenderShader1;
exports.WaterRefractionShader = WaterRefractionShader.WaterRefractionShader;
exports.HTMLMesh = HTMLMesh.HTMLMesh;
exports.InteractiveGroup = InteractiveGroup.InteractiveGroup;
exports.SelectionHelper = SelectionHelper.SelectionHelper;
exports.SelectionBox = SelectionBox.SelectionBox;
exports.AmmoPhysics = AmmoPhysics.AmmoPhysics;
exports.ParallaxBarrierEffect = ParallaxBarrierEffect.ParallaxBarrierEffect;
exports.PeppersGhostEffect = PeppersGhostEffect.PeppersGhostEffect;
exports.OutlineEffect = OutlineEffect.OutlineEffect;
exports.AnaglyphEffect = AnaglyphEffect.AnaglyphEffect;
exports.AsciiEffect = AsciiEffect.AsciiEffect;
exports.StereoEffect = StereoEffect.StereoEffect;
exports.FBXLoader = FBXLoader.FBXLoader;
exports.Font = FontLoader.Font;
exports.FontLoader = FontLoader.FontLoader;
exports.TGALoader = TGALoader.TGALoader;
exports.LUTCubeLoader = LUTCubeLoader.LUTCubeLoader;
exports.NRRDLoader = NRRDLoader.NRRDLoader;
exports.STLLoader = STLLoader.STLLoader;
exports.MTLLoader = MTLLoader.MTLLoader;
exports.XLoader = XLoader.XLoader;
exports.BVHLoader = BVHLoader.BVHLoader;
exports.KMZLoader = KMZLoader.KMZLoader;
exports.VRMLoader = VRMLoader.VRMLoader;
exports.VRMLLoader = VRMLLoader.VRMLLoader;
exports.KTX2Loader = KTX2Loader.KTX2Loader;
exports.LottieLoader = LottieLoader.LottieLoader;
exports.TTFLoader = TTFLoader.TTFLoader;
exports.RGBELoader = RGBELoader.RGBELoader;
exports.AssimpLoader = AssimpLoader.AssimpLoader;
exports.ColladaLoader = ColladaLoader.ColladaLoader;
exports.MDDLoader = MDDLoader.MDDLoader;
exports.EXRLoader = EXRLoader.EXRLoader;
exports.ThreeMFLoader = _3MFLoader.ThreeMFLoader;
exports.XYZLoader = XYZLoader.XYZLoader;
exports.VTKLoader = VTKLoader.VTKLoader;
exports.LUT3dlLoader = LUT3dlLoader.LUT3dlLoader;
exports.DDSLoader = DDSLoader.DDSLoader;
exports.PVRLoader = PVRLoader.PVRLoader;
exports.GCodeLoader = GCodeLoader.GCodeLoader;
exports.BasisTextureLoader = BasisTextureLoader.BasisTextureLoader;
exports.TDSLoader = TDSLoader.TDSLoader;
exports.LDrawLoader = LDrawLoader.LDrawLoader;
exports.GLTFLoader = GLTFLoader.GLTFLoader;
exports.SVGLoader = SVGLoader.SVGLoader;
exports.Rhino3dmLoader = _3DMLoader.Rhino3dmLoader;
exports.OBJLoader = OBJLoader.OBJLoader;
exports.AMFLoader = AMFLoader.AMFLoader;
exports.MMDLoader = MMDLoader.MMDLoader;
exports.MD2Loader = MD2Loader.MD2Loader;
exports.KTXLoader = KTXLoader.KTXLoader;
exports.TiltLoader = TiltLoader.TiltLoader;
exports.DRACOLoader = DRACOLoader.DRACOLoader;
exports.HDRCubeTextureLoader = HDRCubeTextureLoader.HDRCubeTextureLoader;
exports.PDBLoader = PDBLoader.PDBLoader;
exports.PRWMLoader = PRWMLoader.PRWMLoader;
exports.RGBMLoader = RGBMLoader.RGBMLoader;
exports.VOXData3DTexture = VOXLoader.VOXData3DTexture;
exports.VOXLoader = VOXLoader.VOXLoader;
exports.VOXMesh = VOXLoader.VOXMesh;
exports.PCDLoader = PCDLoader.PCDLoader;
exports.LWOLoader = LWOLoader.LWOLoader;
exports.PLYLoader = PLYLoader.PLYLoader;
exports.LineSegmentsGeometry = LineSegmentsGeometry.LineSegmentsGeometry;
exports.LineGeometry = LineGeometry.LineGeometry;
exports.Wireframe = Wireframe.Wireframe;
exports.WireframeGeometry2 = WireframeGeometry2.WireframeGeometry2;
exports.Line2 = Line2.Line2;
exports.LineMaterial = LineMaterial.LineMaterial;
exports.LineSegments2 = LineSegments2.LineSegments2;
exports.LightProbeHelper = LightProbeHelper.LightProbeHelper;
exports.RaycasterHelper = RaycasterHelper.RaycasterHelper;
exports.VertexTangentsHelper = VertexTangentsHelper.VertexTangentsHelper;
exports.PositionalAudioHelper = PositionalAudioHelper.PositionalAudioHelper;
exports.VertexNormalsHelper = VertexNormalsHelper.VertexNormalsHelper;
exports.RectAreaLightHelper = RectAreaLightHelper.RectAreaLightHelper;
exports.RectAreaLightUniformsLib = RectAreaLightUniformsLib.RectAreaLightUniformsLib;
exports.LightProbeGenerator = LightProbeGenerator.LightProbeGenerator;
exports.calcBSplineDerivatives = NURBSUtils.calcBSplineDerivatives;
exports.calcBSplinePoint = NURBSUtils.calcBSplinePoint;
exports.calcBasisFunctionDerivatives = NURBSUtils.calcBasisFunctionDerivatives;
exports.calcBasisFunctions = NURBSUtils.calcBasisFunctions;
exports.calcKoverI = NURBSUtils.calcKoverI;
exports.calcNURBSDerivatives = NURBSUtils.calcNURBSDerivatives;
exports.calcRationalCurveDerivatives = NURBSUtils.calcRationalCurveDerivatives;
exports.calcSurfacePoint = NURBSUtils.calcSurfacePoint;
exports.findSpan = NURBSUtils.findSpan;
exports.NURBSCurve = NURBSCurve.NURBSCurve;
exports.NURBSSurface = NURBSSurface.NURBSSurface;
exports.CinquefoilKnot = CurveExtras.CinquefoilKnot;
exports.DecoratedTorusKnot4a = CurveExtras.DecoratedTorusKnot4a;
exports.DecoratedTorusKnot4b = CurveExtras.DecoratedTorusKnot4b;
exports.DecoratedTorusKnot5a = CurveExtras.DecoratedTorusKnot5a;
exports.DecoratedTorusKnot5c = CurveExtras.DecoratedTorusKnot5c;
exports.FigureEightPolynomialKnot = CurveExtras.FigureEightPolynomialKnot;
exports.GrannyKnot = CurveExtras.GrannyKnot;
exports.HeartCurve = CurveExtras.HeartCurve;
exports.HelixCurve = CurveExtras.HelixCurve;
exports.KnotCurve = CurveExtras.KnotCurve;
exports.TorusKnot = CurveExtras.TorusKnot;
exports.TrefoilKnot = CurveExtras.TrefoilKnot;
exports.TrefoilPolynomialKnot = CurveExtras.TrefoilPolynomialKnot;
exports.VivianiCurve = CurveExtras.VivianiCurve;
exports.Face3 = Geometry.Face3;
exports.Geometry = Geometry.Geometry;
exports.MeshoptDecoder = MeshoptDecoder.MeshoptDecoder;
exports.MotionController = MotionControllers.MotionController;
exports.MotionControllerConstants = MotionControllers.MotionControllerConstants;
exports.fetchProfile = MotionControllers.fetchProfile;
exports.fetchProfilesList = MotionControllers.fetchProfilesList;
//# sourceMappingURL=index.cjs.map
