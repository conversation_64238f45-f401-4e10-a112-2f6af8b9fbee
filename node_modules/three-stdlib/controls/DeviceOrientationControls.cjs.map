{"version": 3, "file": "DeviceOrientationControls.cjs", "sources": ["../../src/controls/DeviceOrientationControls.ts"], "sourcesContent": ["import { <PERSON>, <PERSON>uler, <PERSON><PERSON><PERSON><PERSON>, Quaternion, Vector3 } from 'three'\nimport { EventDispatcher } from './EventDispatcher'\nimport { StandardControlsEventMap } from './StandardControlsEventMap'\n\n/**\n * W3C Device Orientation control (http://w3c.github.io/deviceorientation/spec-source-orientation.html)\n */\n\nclass DeviceOrientationControls extends EventDispatcher<StandardControlsEventMap> {\n  public object: Camera\n\n  private changeEvent = { type: 'change' }\n  private EPS = 0.000001\n\n  public enabled = true\n  public deviceOrientation: Partial<DeviceOrientationEvent> = { alpha: 0, beta: 0, gamma: 0 }\n  public screenOrientation: string | number = 0\n  public alphaOffset = 0 // radians\n\n  constructor(object: Camera) {\n    super()\n\n    this.object = object\n    this.object.rotation.reorder('YXZ')\n\n    this.connect()\n  }\n\n  private onDeviceOrientationChangeEvent = (event: DeviceOrientationEvent): void => {\n    this.deviceOrientation = event\n  }\n\n  private onScreenOrientationChangeEvent = (): void => {\n    this.screenOrientation = window.orientation || 0\n  }\n\n  // The angles alpha, beta and gamma form a set of intrinsic Tait-Bryan angles of type Z-X'-Y''\n\n  private zee = new Vector3(0, 0, 1)\n  private euler = new Euler()\n  private q0 = new Quaternion()\n  private q1 = new Quaternion(-Math.sqrt(0.5), 0, 0, Math.sqrt(0.5)) // - PI/2 around the x-axis\n  private setObjectQuaternion = (\n    quaternion: Quaternion,\n    alpha: number,\n    beta: number,\n    gamma: number,\n    orient: number,\n  ): void => {\n    this.euler.set(beta, alpha, -gamma, 'YXZ') // 'ZXY' for the device, but 'YXZ' for us\n    quaternion.setFromEuler(this.euler) // orient the device\n    quaternion.multiply(this.q1) // camera looks out the back of the device, not the top\n    quaternion.multiply(this.q0.setFromAxisAngle(this.zee, -orient)) // adjust for screen orientation\n  }\n\n  public connect = (): void => {\n    this.onScreenOrientationChangeEvent() // run once on load\n\n    // iOS 13+\n\n    if (\n      window.DeviceOrientationEvent !== undefined &&\n      // @ts-ignore\n      typeof window.DeviceOrientationEvent.requestPermission === 'function'\n    ) {\n      // @ts-ignore\n      window.DeviceOrientationEvent.requestPermission()\n        .then((response: any) => {\n          if (response == 'granted') {\n            window.addEventListener('orientationchange', this.onScreenOrientationChangeEvent)\n            window.addEventListener('deviceorientation', this.onDeviceOrientationChangeEvent)\n          }\n        })\n        .catch((error: any) => {\n          console.error('THREE.DeviceOrientationControls: Unable to use DeviceOrientation API:', error)\n        })\n    } else {\n      window.addEventListener('orientationchange', this.onScreenOrientationChangeEvent)\n      window.addEventListener('deviceorientation', this.onDeviceOrientationChangeEvent)\n    }\n\n    this.enabled = true\n  }\n\n  public disconnect = (): void => {\n    window.removeEventListener('orientationchange', this.onScreenOrientationChangeEvent)\n    window.removeEventListener('deviceorientation', this.onDeviceOrientationChangeEvent)\n\n    this.enabled = false\n  }\n\n  private lastQuaternion = new Quaternion()\n  public update = (): void => {\n    if (this.enabled === false) return\n\n    const device = this.deviceOrientation\n\n    if (device) {\n      const alpha = device.alpha ? MathUtils.degToRad(device.alpha) + this.alphaOffset : 0 // Z\n      const beta = device.beta ? MathUtils.degToRad(device.beta) : 0 // X'\n      const gamma = device.gamma ? MathUtils.degToRad(device.gamma) : 0 // Y''\n      const orient = this.screenOrientation ? MathUtils.degToRad(this.screenOrientation as number) : 0 // O\n\n      this.setObjectQuaternion(this.object.quaternion, alpha, beta, gamma, orient)\n\n      if (8 * (1 - this.lastQuaternion.dot(this.object.quaternion)) > this.EPS) {\n        this.lastQuaternion.copy(this.object.quaternion)\n        // @ts-ignore\n        this.dispatchEvent(this.changeEvent)\n      }\n    }\n  }\n\n  public dispose = (): void => this.disconnect()\n}\n\nexport { DeviceOrientationControls }\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Vector3", "<PERSON>uler", "Quaternion", "MathUtils"], "mappings": ";;;;;;;;;;AAQA,MAAM,kCAAkCA,gBAAAA,gBAA0C;AAAA;AAAA,EAWhF,YAAY,QAAgB;AACpB;AAXD;AAEC,uCAAc,EAAE,MAAM;AACtB,+BAAM;AAEP,mCAAU;AACV,6CAAqD,EAAE,OAAO,GAAG,MAAM,GAAG,OAAO;AACjF,6CAAqC;AACrC,uCAAc;AAWb,0DAAiC,CAAC,UAAwC;AAChF,WAAK,oBAAoB;AAAA,IAAA;AAGnB,0DAAiC,MAAY;AAC9C,WAAA,oBAAoB,OAAO,eAAe;AAAA,IAAA;AAKzC;AAAA,+BAAM,IAAIC,MAAQ,QAAA,GAAG,GAAG,CAAC;AACzB,iCAAQ,IAAIC,MAAAA;AACZ,8BAAK,IAAIC,MAAAA;AACT,8BAAK,IAAIA,MAAW,WAAA,CAAC,KAAK,KAAK,GAAG,GAAG,GAAG,GAAG,KAAK,KAAK,GAAG,CAAC;AACzD;AAAA,+CAAsB,CAC5B,YACA,OACA,MACA,OACA,WACS;AACT,WAAK,MAAM,IAAI,MAAM,OAAO,CAAC,OAAO,KAAK;AAC9B,iBAAA,aAAa,KAAK,KAAK;AACvB,iBAAA,SAAS,KAAK,EAAE;AAChB,iBAAA,SAAS,KAAK,GAAG,iBAAiB,KAAK,KAAK,CAAC,MAAM,CAAC;AAAA,IAAA;AAG1D,mCAAU,MAAY;AAC3B,WAAK,+BAA+B;AAIpC,UACE,OAAO,2BAA2B;AAAA,MAElC,OAAO,OAAO,uBAAuB,sBAAsB,YAC3D;AAEA,eAAO,uBAAuB,kBAC3B,EAAA,KAAK,CAAC,aAAkB;AACvB,cAAI,YAAY,WAAW;AAClB,mBAAA,iBAAiB,qBAAqB,KAAK,8BAA8B;AACzE,mBAAA,iBAAiB,qBAAqB,KAAK,8BAA8B;AAAA,UAClF;AAAA,QAAA,CACD,EACA,MAAM,CAAC,UAAe;AACb,kBAAA,MAAM,yEAAyE,KAAK;AAAA,QAAA,CAC7F;AAAA,MAAA,OACE;AACE,eAAA,iBAAiB,qBAAqB,KAAK,8BAA8B;AACzE,eAAA,iBAAiB,qBAAqB,KAAK,8BAA8B;AAAA,MAClF;AAEA,WAAK,UAAU;AAAA,IAAA;AAGV,sCAAa,MAAY;AACvB,aAAA,oBAAoB,qBAAqB,KAAK,8BAA8B;AAC5E,aAAA,oBAAoB,qBAAqB,KAAK,8BAA8B;AAEnF,WAAK,UAAU;AAAA,IAAA;AAGT,0CAAiB,IAAIA,MAAAA;AACtB,kCAAS,MAAY;AAC1B,UAAI,KAAK,YAAY;AAAO;AAE5B,YAAM,SAAS,KAAK;AAEpB,UAAI,QAAQ;AACJ,cAAA,QAAQ,OAAO,QAAQC,gBAAU,SAAS,OAAO,KAAK,IAAI,KAAK,cAAc;AACnF,cAAM,OAAO,OAAO,OAAOA,MAAAA,UAAU,SAAS,OAAO,IAAI,IAAI;AAC7D,cAAM,QAAQ,OAAO,QAAQA,MAAAA,UAAU,SAAS,OAAO,KAAK,IAAI;AAChE,cAAM,SAAS,KAAK,oBAAoBA,MAAAA,UAAU,SAAS,KAAK,iBAA2B,IAAI;AAE/F,aAAK,oBAAoB,KAAK,OAAO,YAAY,OAAO,MAAM,OAAO,MAAM;AAEvE,YAAA,KAAK,IAAI,KAAK,eAAe,IAAI,KAAK,OAAO,UAAU,KAAK,KAAK,KAAK;AACxE,eAAK,eAAe,KAAK,KAAK,OAAO,UAAU;AAE1C,eAAA,cAAc,KAAK,WAAW;AAAA,QACrC;AAAA,MACF;AAAA,IAAA;AAGK,mCAAU,MAAY,KAAK;AA3FhC,SAAK,SAAS;AACT,SAAA,OAAO,SAAS,QAAQ,KAAK;AAElC,SAAK,QAAQ;AAAA,EACf;AAwFF;;"}