{"version": 3, "file": "EventDispatcher.cjs", "sources": ["../../src/controls/EventDispatcher.ts"], "sourcesContent": ["/*\nDue to @types/three r168 breaking change\nwe have to manually copy the EventDispatcher class from three.js.\nSo this files merges the declarations from https://github.com/DefinitelyTyped/DefinitelyTyped/blob/master/types/three/src/core/EventDispatcher.d.ts\nwith the implementation from https://github.com/mrdoob/three.js/blob/dev/src/core/EventDispatcher.js\nMore info in https://github.com/pmndrs/three-stdlib/issues/387\n*/\n\n/**\n * The minimal basic Event that can be dispatched by a {@link EventDispatcher<>}.\n */\nexport interface BaseEvent<TEventType extends string = string> {\n    readonly type: TEventType;\n    // not defined in @types/three\n    target: any;\n}\n\n/**\n * The minimal expected contract of a fired Event that was dispatched by a {@link EventDispatcher<>}.\n */\nexport interface Event<TEventType extends string = string, TTarget = unknown> {\n    readonly type: TEventType;\n    readonly target: TTarget;\n}\n\nexport type EventListener<TEventData, TEventType extends string, TTarget> = (\n    event: TEventData & Event<TEventType, TTarget>,\n) => void;\n\nexport class EventDispatcher<TEventMap extends {} = {}> {\n    // not defined in @types/three\n    private _listeners: any;\n\n    /**\n     * Adds a listener to an event type.\n     * @param type The type of event to listen to.\n     * @param listener The function that gets called when the event is fired.\n     */\n\taddEventListener<T extends Extract<keyof TEventMap, string>>(\n        type: T,\n        listener: EventListener<TEventMap[T], T, this>,\n    ): void {\n\n\t\tif ( this._listeners === undefined ) this._listeners = {};\n\n\t\tconst listeners = this._listeners;\n\n\t\tif ( listeners[ type ] === undefined ) {\n\n\t\t\tlisteners[ type ] = [];\n\n\t\t}\n\n\t\tif ( listeners[ type ].indexOf( listener ) === - 1 ) {\n\n\t\t\tlisteners[ type ].push( listener );\n\n\t\t}\n\n\t}\n\n\t/**\n     * Checks if listener is added to an event type.\n     * @param type The type of event to listen to.\n     * @param listener The function that gets called when the event is fired.\n     */\n    hasEventListener<T extends Extract<keyof TEventMap, string>>(\n        type: T,\n        listener: EventListener<TEventMap[T], T, this>,\n    ): boolean {\n\n\t\tif ( this._listeners === undefined ) return false;\n\n\t\tconst listeners = this._listeners;\n\n\t\treturn listeners[ type ] !== undefined && listeners[ type ].indexOf( listener ) !== - 1;\n\n\t}\n\n\t/**\n     * Removes a listener from an event type.\n     * @param type The type of the listener that gets removed.\n     * @param listener The listener function that gets removed.\n     */\n    removeEventListener<T extends Extract<keyof TEventMap, string>>(\n        type: T,\n        listener: EventListener<TEventMap[T], T, this>,\n    ): void {\n\n\t\tif ( this._listeners === undefined ) return;\n\n\t\tconst listeners = this._listeners;\n\t\tconst listenerArray = listeners[ type ];\n\n\t\tif ( listenerArray !== undefined ) {\n\n\t\t\tconst index = listenerArray.indexOf( listener );\n\n\t\t\tif ( index !== - 1 ) {\n\n\t\t\t\tlistenerArray.splice( index, 1 );\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/**\n     * Fire an event type.\n     * @param event The event that gets fired.\n     */\n    dispatchEvent<T extends Extract<keyof TEventMap, string>>(event: BaseEvent<T> & TEventMap[T]): void {\n\n\t\tif ( this._listeners === undefined ) return;\n\n\t\tconst listeners = this._listeners;\n\t\tconst listenerArray = listeners[ event.type ];\n\n\t\tif ( listenerArray !== undefined ) {\n\n\t\t\tevent.target = this;\n\n\t\t\t// Make a copy, in case listeners are removed while iterating.\n\t\t\tconst array = listenerArray.slice( 0 );\n\n\t\t\tfor ( let i = 0, l = array.length; i < l; i ++ ) {\n\n\t\t\t\tarray[ i ].call( this, event );\n\n\t\t\t}\n\n\t\t\tevent.target = null;\n\n\t\t}\n\n\t}\n\n}"], "names": [], "mappings": ";;;;;;;;AA6BO,MAAM,gBAA2C;AAAA,EAAjD;AAEK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOX,iBACO,MACA,UACI;AAEV,QAAK,KAAK,eAAe;AAAY,WAAK,aAAa;AAEvD,UAAM,YAAY,KAAK;AAElB,QAAA,UAAW,IAAK,MAAM,QAAY;AAE3B,gBAAA,IAAK,IAAI;IAErB;AAEA,QAAK,UAAW,IAAK,EAAE,QAAS,QAAS,MAAM,IAAM;AAEzC,gBAAA,IAAK,EAAE,KAAM,QAAS;AAAA,IAElC;AAAA,EAED;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOG,iBACI,MACA,UACO;AAEb,QAAK,KAAK,eAAe;AAAmB,aAAA;AAE5C,UAAM,YAAY,KAAK;AAEhB,WAAA,UAAW,IAAK,MAAM,UAAa,UAAW,IAAK,EAAE,QAAS,QAAS,MAAM;AAAA,EAErF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOG,oBACI,MACA,UACI;AAEV,QAAK,KAAK,eAAe;AAAY;AAErC,UAAM,YAAY,KAAK;AACjB,UAAA,gBAAgB,UAAW,IAAK;AAEtC,QAAK,kBAAkB,QAAY;AAE5B,YAAA,QAAQ,cAAc,QAAS,QAAS;AAE9C,UAAK,UAAU,IAAM;AAEN,sBAAA,OAAQ,OAAO,CAAE;AAAA,MAEhC;AAAA,IAED;AAAA,EAED;AAAA;AAAA;AAAA;AAAA;AAAA,EAMG,cAA0D,OAA0C;AAEtG,QAAK,KAAK,eAAe;AAAY;AAErC,UAAM,YAAY,KAAK;AACjB,UAAA,gBAAgB,UAAW,MAAM,IAAK;AAE5C,QAAK,kBAAkB,QAAY;AAElC,YAAM,SAAS;AAGT,YAAA,QAAQ,cAAc,MAAO,CAAE;AAErC,eAAU,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAO;AAEhD,cAAO,CAAE,EAAE,KAAM,MAAM,KAAM;AAAA,MAE9B;AAEA,YAAM,SAAS;AAAA,IAEhB;AAAA,EAED;AAED;;"}