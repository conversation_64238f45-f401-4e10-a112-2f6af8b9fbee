{"version": 3, "file": "OrbitControls.js", "sources": ["../../src/controls/OrbitControls.ts"], "sourcesContent": ["import {\n  Matrix4,\n  MOUSE,\n  OrthographicCamera,\n  PerspectiveCamera,\n  Quaternion,\n  Spherical,\n  TOUCH,\n  Vector2,\n  Vector3,\n  Ray,\n  Plane,\n} from 'three'\nimport { EventDispatcher } from './EventDispatcher'\nimport { StandardControlsEventMap } from './StandardControlsEventMap'\n\nconst _ray = /* @__PURE__ */ new Ray()\nconst _plane = /* @__PURE__ */ new Plane()\nconst TILT_LIMIT = Math.cos(70 * (Math.PI / 180))\n\n// This set of controls performs orbiting, dollying (zooming), and panning.\n// Unlike TrackballControls, it maintains the \"up\" direction object.up (+Y by default).\n//\n//    Orbit - left mouse / touch: one-finger move\n//    Zoom - middle mouse, or mousewheel / touch: two-finger spread or squish\n//    Pan - right mouse, or left mouse + ctrl/meta/shiftKey, or arrow keys / touch: two-finger move\n\nconst moduloWrapAround = (offset: number, capacity: number) => ((offset % capacity) + capacity) % capacity\n\nclass OrbitControls extends EventDispatcher<StandardControlsEventMap> {\n  object: PerspectiveCamera | OrthographicCamera\n  domElement: HTMLElement | undefined\n  // Set to false to disable this control\n  enabled = true\n  // \"target\" sets the location of focus, where the object orbits around\n  target = new Vector3()\n  // How far you can dolly in and out ( PerspectiveCamera only )\n  minDistance = 0\n  maxDistance = Infinity\n  // How far you can zoom in and out ( OrthographicCamera only )\n  minZoom = 0\n  maxZoom = Infinity\n  // How far you can orbit vertically, upper and lower limits.\n  // Range is 0 to Math.PI radians.\n  minPolarAngle = 0 // radians\n  maxPolarAngle = Math.PI // radians\n  // How far you can orbit horizontally, upper and lower limits.\n  // If set, the interval [ min, max ] must be a sub-interval of [ - 2 PI, 2 PI ], with ( max - min < 2 PI )\n  minAzimuthAngle = -Infinity // radians\n  maxAzimuthAngle = Infinity // radians\n  // Set to true to enable damping (inertia)\n  // If damping is enabled, you must call controls.update() in your animation loop\n  enableDamping = false\n  dampingFactor = 0.05\n  // This option actually enables dollying in and out; left as \"zoom\" for backwards compatibility.\n  // Set to false to disable zooming\n  enableZoom = true\n  zoomSpeed = 1.0\n  // Set to false to disable rotating\n  enableRotate = true\n  rotateSpeed = 1.0\n  // Set to false to disable panning\n  enablePan = true\n  panSpeed = 1.0\n  screenSpacePanning = true // if false, pan orthogonal to world-space direction camera.up\n  keyPanSpeed = 7.0 // pixels moved per arrow key push\n  zoomToCursor = false\n  // Set to true to automatically rotate around the target\n  // If auto-rotate is enabled, you must call controls.update() in your animation loop\n  autoRotate = false\n  autoRotateSpeed = 2.0 // 30 seconds per orbit when fps is 60\n  reverseOrbit = false // true if you want to reverse the orbit to mouse drag from left to right = orbits left\n  reverseHorizontalOrbit = false // true if you want to reverse the horizontal orbit direction\n  reverseVerticalOrbit = false // true if you want to reverse the vertical orbit direction\n  // The four arrow keys\n  keys = { LEFT: 'ArrowLeft', UP: 'ArrowUp', RIGHT: 'ArrowRight', BOTTOM: 'ArrowDown' }\n  // Mouse buttons\n  mouseButtons: Partial<{\n    LEFT: MOUSE\n    MIDDLE: MOUSE\n    RIGHT: MOUSE\n  }> = {\n    LEFT: MOUSE.ROTATE,\n    MIDDLE: MOUSE.DOLLY,\n    RIGHT: MOUSE.PAN,\n  }\n  // Touch fingers\n  touches: Partial<{\n    ONE: TOUCH\n    TWO: TOUCH\n  }> = { ONE: TOUCH.ROTATE, TWO: TOUCH.DOLLY_PAN }\n  target0: Vector3\n  position0: Vector3\n  zoom0: number\n  // the target DOM element for key events\n  _domElementKeyEvents: any = null\n\n  getPolarAngle: () => number\n  getAzimuthalAngle: () => number\n  setPolarAngle: (x: number) => void\n  setAzimuthalAngle: (x: number) => void\n  getDistance: () => number\n  // Not used in most scenarios, however they can be useful for specific use cases\n  getZoomScale: () => number\n\n  listenToKeyEvents: (domElement: HTMLElement) => void\n  stopListenToKeyEvents: () => void\n  saveState: () => void\n  reset: () => void\n  update: () => void\n  connect: (domElement: HTMLElement) => void\n  dispose: () => void\n\n  // Dolly in programmatically\n  dollyIn: (dollyScale?: number) => void\n  // Dolly out programmatically\n  dollyOut: (dollyScale?: number) => void\n  // Get the current scale\n  getScale: () => number\n  // Set the current scale (these are not used in most scenarios, however they can be useful for specific use cases)\n  setScale: (newScale: number) => void\n\n  constructor(object: PerspectiveCamera | OrthographicCamera, domElement?: HTMLElement) {\n    super()\n\n    this.object = object\n    this.domElement = domElement\n\n    // for reset\n    this.target0 = this.target.clone()\n    this.position0 = this.object.position.clone()\n    this.zoom0 = this.object.zoom\n\n    //\n    // public methods\n    //\n\n    this.getPolarAngle = (): number => spherical.phi\n\n    this.getAzimuthalAngle = (): number => spherical.theta\n\n    this.setPolarAngle = (value: number): void => {\n      // use modulo wrapping to safeguard value\n      let phi = moduloWrapAround(value, 2 * Math.PI)\n      let currentPhi = spherical.phi\n\n      // convert to the equivalent shortest angle\n      if (currentPhi < 0) currentPhi += 2 * Math.PI\n      if (phi < 0) phi += 2 * Math.PI\n      let phiDist = Math.abs(phi - currentPhi)\n      if (2 * Math.PI - phiDist < phiDist) {\n        if (phi < currentPhi) {\n          phi += 2 * Math.PI\n        } else {\n          currentPhi += 2 * Math.PI\n        }\n      }\n      sphericalDelta.phi = phi - currentPhi\n      scope.update()\n    }\n\n    this.setAzimuthalAngle = (value: number): void => {\n      // use modulo wrapping to safeguard value\n      let theta = moduloWrapAround(value, 2 * Math.PI)\n      let currentTheta = spherical.theta\n\n      // convert to the equivalent shortest angle\n      if (currentTheta < 0) currentTheta += 2 * Math.PI\n      if (theta < 0) theta += 2 * Math.PI\n      let thetaDist = Math.abs(theta - currentTheta)\n      if (2 * Math.PI - thetaDist < thetaDist) {\n        if (theta < currentTheta) {\n          theta += 2 * Math.PI\n        } else {\n          currentTheta += 2 * Math.PI\n        }\n      }\n      sphericalDelta.theta = theta - currentTheta\n      scope.update()\n    }\n\n    this.getDistance = (): number => scope.object.position.distanceTo(scope.target)\n\n    this.listenToKeyEvents = (domElement: HTMLElement): void => {\n      domElement.addEventListener('keydown', onKeyDown)\n      this._domElementKeyEvents = domElement\n    }\n\n    this.stopListenToKeyEvents = (): void => {\n      this._domElementKeyEvents.removeEventListener('keydown', onKeyDown)\n      this._domElementKeyEvents = null\n    }\n\n    this.saveState = (): void => {\n      scope.target0.copy(scope.target)\n      scope.position0.copy(scope.object.position)\n      scope.zoom0 = scope.object.zoom\n    }\n\n    this.reset = (): void => {\n      scope.target.copy(scope.target0)\n      scope.object.position.copy(scope.position0)\n      scope.object.zoom = scope.zoom0\n      scope.object.updateProjectionMatrix()\n\n      // @ts-ignore\n      scope.dispatchEvent(changeEvent)\n\n      scope.update()\n\n      state = STATE.NONE\n    }\n\n    // this method is exposed, but perhaps it would be better if we can make it private...\n    this.update = ((): (() => void) => {\n      const offset = new Vector3()\n      const up = new Vector3(0, 1, 0)\n\n      // so camera.up is the orbit axis\n      const quat = new Quaternion().setFromUnitVectors(object.up, up)\n      const quatInverse = quat.clone().invert()\n\n      const lastPosition = new Vector3()\n      const lastQuaternion = new Quaternion()\n\n      const twoPI = 2 * Math.PI\n\n      return function update(): boolean {\n        const position = scope.object.position\n\n        // update new up direction\n        quat.setFromUnitVectors(object.up, up)\n        quatInverse.copy(quat).invert()\n\n        offset.copy(position).sub(scope.target)\n\n        // rotate offset to \"y-axis-is-up\" space\n        offset.applyQuaternion(quat)\n\n        // angle from z-axis around y-axis\n        spherical.setFromVector3(offset)\n\n        if (scope.autoRotate && state === STATE.NONE) {\n          rotateLeft(getAutoRotationAngle())\n        }\n\n        if (scope.enableDamping) {\n          spherical.theta += sphericalDelta.theta * scope.dampingFactor\n          spherical.phi += sphericalDelta.phi * scope.dampingFactor\n        } else {\n          spherical.theta += sphericalDelta.theta\n          spherical.phi += sphericalDelta.phi\n        }\n\n        // restrict theta to be between desired limits\n\n        let min = scope.minAzimuthAngle\n        let max = scope.maxAzimuthAngle\n\n        if (isFinite(min) && isFinite(max)) {\n          if (min < -Math.PI) min += twoPI\n          else if (min > Math.PI) min -= twoPI\n\n          if (max < -Math.PI) max += twoPI\n          else if (max > Math.PI) max -= twoPI\n\n          if (min <= max) {\n            spherical.theta = Math.max(min, Math.min(max, spherical.theta))\n          } else {\n            spherical.theta =\n              spherical.theta > (min + max) / 2 ? Math.max(min, spherical.theta) : Math.min(max, spherical.theta)\n          }\n        }\n\n        // restrict phi to be between desired limits\n        spherical.phi = Math.max(scope.minPolarAngle, Math.min(scope.maxPolarAngle, spherical.phi))\n        spherical.makeSafe()\n\n        // move target to panned location\n\n        if (scope.enableDamping === true) {\n          scope.target.addScaledVector(panOffset, scope.dampingFactor)\n        } else {\n          scope.target.add(panOffset)\n        }\n\n        // adjust the camera position based on zoom only if we're not zooming to the cursor or if it's an ortho camera\n        // we adjust zoom later in these cases\n        if ((scope.zoomToCursor && performCursorZoom) || (scope.object as OrthographicCamera).isOrthographicCamera) {\n          spherical.radius = clampDistance(spherical.radius)\n        } else {\n          spherical.radius = clampDistance(spherical.radius * scale)\n        }\n\n        offset.setFromSpherical(spherical)\n\n        // rotate offset back to \"camera-up-vector-is-up\" space\n        offset.applyQuaternion(quatInverse)\n\n        position.copy(scope.target).add(offset)\n\n        if (!scope.object.matrixAutoUpdate) scope.object.updateMatrix()\n        scope.object.lookAt(scope.target)\n\n        if (scope.enableDamping === true) {\n          sphericalDelta.theta *= 1 - scope.dampingFactor\n          sphericalDelta.phi *= 1 - scope.dampingFactor\n\n          panOffset.multiplyScalar(1 - scope.dampingFactor)\n        } else {\n          sphericalDelta.set(0, 0, 0)\n\n          panOffset.set(0, 0, 0)\n        }\n\n        // adjust camera position\n        let zoomChanged = false\n        if (scope.zoomToCursor && performCursorZoom) {\n          let newRadius = null\n          if (scope.object instanceof PerspectiveCamera && scope.object.isPerspectiveCamera) {\n            // move the camera down the pointer ray\n            // this method avoids floating point error\n            const prevRadius = offset.length()\n            newRadius = clampDistance(prevRadius * scale)\n\n            const radiusDelta = prevRadius - newRadius\n            scope.object.position.addScaledVector(dollyDirection, radiusDelta)\n            scope.object.updateMatrixWorld()\n          } else if ((scope.object as OrthographicCamera).isOrthographicCamera) {\n            // adjust the ortho camera position based on zoom changes\n            const mouseBefore = new Vector3(mouse.x, mouse.y, 0)\n            mouseBefore.unproject(scope.object)\n\n            scope.object.zoom = Math.max(scope.minZoom, Math.min(scope.maxZoom, scope.object.zoom / scale))\n            scope.object.updateProjectionMatrix()\n            zoomChanged = true\n\n            const mouseAfter = new Vector3(mouse.x, mouse.y, 0)\n            mouseAfter.unproject(scope.object)\n\n            scope.object.position.sub(mouseAfter).add(mouseBefore)\n            scope.object.updateMatrixWorld()\n\n            newRadius = offset.length()\n          } else {\n            console.warn('WARNING: OrbitControls.js encountered an unknown camera type - zoom to cursor disabled.')\n            scope.zoomToCursor = false\n          }\n\n          // handle the placement of the target\n          if (newRadius !== null) {\n            if (scope.screenSpacePanning) {\n              // position the orbit target in front of the new camera position\n              scope.target\n                .set(0, 0, -1)\n                .transformDirection(scope.object.matrix)\n                .multiplyScalar(newRadius)\n                .add(scope.object.position)\n            } else {\n              // get the ray and translation plane to compute target\n              _ray.origin.copy(scope.object.position)\n              _ray.direction.set(0, 0, -1).transformDirection(scope.object.matrix)\n\n              // if the camera is 20 degrees above the horizon then don't adjust the focus target to avoid\n              // extremely large values\n              if (Math.abs(scope.object.up.dot(_ray.direction)) < TILT_LIMIT) {\n                object.lookAt(scope.target)\n              } else {\n                _plane.setFromNormalAndCoplanarPoint(scope.object.up, scope.target)\n                _ray.intersectPlane(_plane, scope.target)\n              }\n            }\n          }\n        } else if (scope.object instanceof OrthographicCamera && scope.object.isOrthographicCamera) {\n          zoomChanged = scale !== 1\n\n          if (zoomChanged) {\n            scope.object.zoom = Math.max(scope.minZoom, Math.min(scope.maxZoom, scope.object.zoom / scale))\n            scope.object.updateProjectionMatrix()\n          }\n        }\n\n        scale = 1\n        performCursorZoom = false\n\n        // update condition is:\n        // min(camera displacement, camera rotation in radians)^2 > EPS\n        // using small-angle approximation cos(x/2) = 1 - x^2 / 8\n\n        if (\n          zoomChanged ||\n          lastPosition.distanceToSquared(scope.object.position) > EPS ||\n          8 * (1 - lastQuaternion.dot(scope.object.quaternion)) > EPS\n        ) {\n          // @ts-ignore\n          scope.dispatchEvent(changeEvent)\n\n          lastPosition.copy(scope.object.position)\n          lastQuaternion.copy(scope.object.quaternion)\n          zoomChanged = false\n\n          return true\n        }\n\n        return false\n      }\n    })()\n\n    // https://github.com/mrdoob/three.js/issues/20575\n    this.connect = (domElement: HTMLElement): void => {\n      scope.domElement = domElement\n      // disables touch scroll\n      // touch-action needs to be defined for pointer events to work on mobile\n      // https://stackoverflow.com/a/48254578\n      scope.domElement.style.touchAction = 'none'\n      scope.domElement.addEventListener('contextmenu', onContextMenu)\n      scope.domElement.addEventListener('pointerdown', onPointerDown)\n      scope.domElement.addEventListener('pointercancel', onPointerUp)\n      scope.domElement.addEventListener('wheel', onMouseWheel)\n    }\n\n    this.dispose = (): void => {\n      // Enabling touch scroll\n      if (scope.domElement) {\n        scope.domElement.style.touchAction = 'auto'\n      }\n      scope.domElement?.removeEventListener('contextmenu', onContextMenu)\n      scope.domElement?.removeEventListener('pointerdown', onPointerDown)\n      scope.domElement?.removeEventListener('pointercancel', onPointerUp)\n      scope.domElement?.removeEventListener('wheel', onMouseWheel)\n      scope.domElement?.ownerDocument.removeEventListener('pointermove', onPointerMove)\n      scope.domElement?.ownerDocument.removeEventListener('pointerup', onPointerUp)\n      if (scope._domElementKeyEvents !== null) {\n        scope._domElementKeyEvents.removeEventListener('keydown', onKeyDown)\n      }\n      //scope.dispatchEvent( { type: 'dispose' } ); // should this be added here?\n    }\n\n    //\n    // internals\n    //\n\n    const scope = this\n\n    const changeEvent = { type: 'change' }\n    const startEvent = { type: 'start' }\n    const endEvent = { type: 'end' }\n\n    const STATE = {\n      NONE: -1,\n      ROTATE: 0,\n      DOLLY: 1,\n      PAN: 2,\n      TOUCH_ROTATE: 3,\n      TOUCH_PAN: 4,\n      TOUCH_DOLLY_PAN: 5,\n      TOUCH_DOLLY_ROTATE: 6,\n    }\n\n    let state = STATE.NONE\n\n    const EPS = 0.000001\n\n    // current position in spherical coordinates\n    const spherical = new Spherical()\n    const sphericalDelta = new Spherical()\n\n    let scale = 1\n    const panOffset = new Vector3()\n\n    const rotateStart = new Vector2()\n    const rotateEnd = new Vector2()\n    const rotateDelta = new Vector2()\n\n    const panStart = new Vector2()\n    const panEnd = new Vector2()\n    const panDelta = new Vector2()\n\n    const dollyStart = new Vector2()\n    const dollyEnd = new Vector2()\n    const dollyDelta = new Vector2()\n\n    const dollyDirection = new Vector3()\n    const mouse = new Vector2()\n    let performCursorZoom = false\n\n    const pointers: PointerEvent[] = []\n    const pointerPositions: { [key: string]: Vector2 } = {}\n\n    function getAutoRotationAngle(): number {\n      return ((2 * Math.PI) / 60 / 60) * scope.autoRotateSpeed\n    }\n\n    function getZoomScale(): number {\n      return Math.pow(0.95, scope.zoomSpeed)\n    }\n\n    function rotateLeft(angle: number): void {\n      if (scope.reverseOrbit || scope.reverseHorizontalOrbit) {\n        sphericalDelta.theta += angle\n      } else {\n        sphericalDelta.theta -= angle\n      }\n    }\n\n    function rotateUp(angle: number): void {\n      if (scope.reverseOrbit || scope.reverseVerticalOrbit) {\n        sphericalDelta.phi += angle\n      } else {\n        sphericalDelta.phi -= angle\n      }\n    }\n\n    const panLeft = (() => {\n      const v = new Vector3()\n\n      return function panLeft(distance: number, objectMatrix: Matrix4) {\n        v.setFromMatrixColumn(objectMatrix, 0) // get X column of objectMatrix\n        v.multiplyScalar(-distance)\n\n        panOffset.add(v)\n      }\n    })()\n\n    const panUp = (() => {\n      const v = new Vector3()\n\n      return function panUp(distance: number, objectMatrix: Matrix4) {\n        if (scope.screenSpacePanning === true) {\n          v.setFromMatrixColumn(objectMatrix, 1)\n        } else {\n          v.setFromMatrixColumn(objectMatrix, 0)\n          v.crossVectors(scope.object.up, v)\n        }\n\n        v.multiplyScalar(distance)\n\n        panOffset.add(v)\n      }\n    })()\n\n    // deltaX and deltaY are in pixels; right and down are positive\n    const pan = (() => {\n      const offset = new Vector3()\n\n      return function pan(deltaX: number, deltaY: number) {\n        const element = scope.domElement\n\n        if (element && scope.object instanceof PerspectiveCamera && scope.object.isPerspectiveCamera) {\n          // perspective\n          const position = scope.object.position\n          offset.copy(position).sub(scope.target)\n          let targetDistance = offset.length()\n\n          // half of the fov is center to top of screen\n          targetDistance *= Math.tan(((scope.object.fov / 2) * Math.PI) / 180.0)\n\n          // we use only clientHeight here so aspect ratio does not distort speed\n          panLeft((2 * deltaX * targetDistance) / element.clientHeight, scope.object.matrix)\n          panUp((2 * deltaY * targetDistance) / element.clientHeight, scope.object.matrix)\n        } else if (element && scope.object instanceof OrthographicCamera && scope.object.isOrthographicCamera) {\n          // orthographic\n          panLeft(\n            (deltaX * (scope.object.right - scope.object.left)) / scope.object.zoom / element.clientWidth,\n            scope.object.matrix,\n          )\n          panUp(\n            (deltaY * (scope.object.top - scope.object.bottom)) / scope.object.zoom / element.clientHeight,\n            scope.object.matrix,\n          )\n        } else {\n          // camera neither orthographic nor perspective\n          console.warn('WARNING: OrbitControls.js encountered an unknown camera type - pan disabled.')\n          scope.enablePan = false\n        }\n      }\n    })()\n\n    function setScale(newScale: number) {\n      if (\n        (scope.object instanceof PerspectiveCamera && scope.object.isPerspectiveCamera) ||\n        (scope.object instanceof OrthographicCamera && scope.object.isOrthographicCamera)\n      ) {\n        scale = newScale\n      } else {\n        console.warn('WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled.')\n        scope.enableZoom = false\n      }\n    }\n\n    function dollyOut(dollyScale: number) {\n      setScale(scale / dollyScale)\n    }\n\n    function dollyIn(dollyScale: number) {\n      setScale(scale * dollyScale)\n    }\n\n    function updateMouseParameters(event: MouseEvent): void {\n      if (!scope.zoomToCursor || !scope.domElement) {\n        return\n      }\n\n      performCursorZoom = true\n\n      const rect = scope.domElement.getBoundingClientRect()\n      const x = event.clientX - rect.left\n      const y = event.clientY - rect.top\n      const w = rect.width\n      const h = rect.height\n\n      mouse.x = (x / w) * 2 - 1\n      mouse.y = -(y / h) * 2 + 1\n\n      dollyDirection.set(mouse.x, mouse.y, 1).unproject(scope.object).sub(scope.object.position).normalize()\n    }\n\n    function clampDistance(dist: number): number {\n      return Math.max(scope.minDistance, Math.min(scope.maxDistance, dist))\n    }\n\n    //\n    // event callbacks - update the object state\n    //\n\n    function handleMouseDownRotate(event: MouseEvent) {\n      rotateStart.set(event.clientX, event.clientY)\n    }\n\n    function handleMouseDownDolly(event: MouseEvent) {\n      updateMouseParameters(event)\n      dollyStart.set(event.clientX, event.clientY)\n    }\n\n    function handleMouseDownPan(event: MouseEvent) {\n      panStart.set(event.clientX, event.clientY)\n    }\n\n    function handleMouseMoveRotate(event: MouseEvent) {\n      rotateEnd.set(event.clientX, event.clientY)\n      rotateDelta.subVectors(rotateEnd, rotateStart).multiplyScalar(scope.rotateSpeed)\n\n      const element = scope.domElement\n\n      if (element) {\n        rotateLeft((2 * Math.PI * rotateDelta.x) / element.clientHeight) // yes, height\n        rotateUp((2 * Math.PI * rotateDelta.y) / element.clientHeight)\n      }\n      rotateStart.copy(rotateEnd)\n      scope.update()\n    }\n\n    function handleMouseMoveDolly(event: MouseEvent) {\n      dollyEnd.set(event.clientX, event.clientY)\n      dollyDelta.subVectors(dollyEnd, dollyStart)\n\n      if (dollyDelta.y > 0) {\n        dollyOut(getZoomScale())\n      } else if (dollyDelta.y < 0) {\n        dollyIn(getZoomScale())\n      }\n\n      dollyStart.copy(dollyEnd)\n      scope.update()\n    }\n\n    function handleMouseMovePan(event: MouseEvent) {\n      panEnd.set(event.clientX, event.clientY)\n      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed)\n      pan(panDelta.x, panDelta.y)\n      panStart.copy(panEnd)\n      scope.update()\n    }\n\n    function handleMouseWheel(event: WheelEvent) {\n      updateMouseParameters(event)\n\n      if (event.deltaY < 0) {\n        dollyIn(getZoomScale())\n      } else if (event.deltaY > 0) {\n        dollyOut(getZoomScale())\n      }\n\n      scope.update()\n    }\n\n    function handleKeyDown(event: KeyboardEvent) {\n      let needsUpdate = false\n\n      switch (event.code) {\n        case scope.keys.UP:\n          pan(0, scope.keyPanSpeed)\n          needsUpdate = true\n          break\n\n        case scope.keys.BOTTOM:\n          pan(0, -scope.keyPanSpeed)\n          needsUpdate = true\n          break\n\n        case scope.keys.LEFT:\n          pan(scope.keyPanSpeed, 0)\n          needsUpdate = true\n          break\n\n        case scope.keys.RIGHT:\n          pan(-scope.keyPanSpeed, 0)\n          needsUpdate = true\n          break\n      }\n\n      if (needsUpdate) {\n        // prevent the browser from scrolling on cursor keys\n        event.preventDefault()\n        scope.update()\n      }\n    }\n\n    function handleTouchStartRotate() {\n      if (pointers.length == 1) {\n        rotateStart.set(pointers[0].pageX, pointers[0].pageY)\n      } else {\n        const x = 0.5 * (pointers[0].pageX + pointers[1].pageX)\n        const y = 0.5 * (pointers[0].pageY + pointers[1].pageY)\n\n        rotateStart.set(x, y)\n      }\n    }\n\n    function handleTouchStartPan() {\n      if (pointers.length == 1) {\n        panStart.set(pointers[0].pageX, pointers[0].pageY)\n      } else {\n        const x = 0.5 * (pointers[0].pageX + pointers[1].pageX)\n        const y = 0.5 * (pointers[0].pageY + pointers[1].pageY)\n\n        panStart.set(x, y)\n      }\n    }\n\n    function handleTouchStartDolly() {\n      const dx = pointers[0].pageX - pointers[1].pageX\n      const dy = pointers[0].pageY - pointers[1].pageY\n      const distance = Math.sqrt(dx * dx + dy * dy)\n\n      dollyStart.set(0, distance)\n    }\n\n    function handleTouchStartDollyPan() {\n      if (scope.enableZoom) handleTouchStartDolly()\n      if (scope.enablePan) handleTouchStartPan()\n    }\n\n    function handleTouchStartDollyRotate() {\n      if (scope.enableZoom) handleTouchStartDolly()\n      if (scope.enableRotate) handleTouchStartRotate()\n    }\n\n    function handleTouchMoveRotate(event: PointerEvent) {\n      if (pointers.length == 1) {\n        rotateEnd.set(event.pageX, event.pageY)\n      } else {\n        const position = getSecondPointerPosition(event)\n        const x = 0.5 * (event.pageX + position.x)\n        const y = 0.5 * (event.pageY + position.y)\n        rotateEnd.set(x, y)\n      }\n\n      rotateDelta.subVectors(rotateEnd, rotateStart).multiplyScalar(scope.rotateSpeed)\n\n      const element = scope.domElement\n\n      if (element) {\n        rotateLeft((2 * Math.PI * rotateDelta.x) / element.clientHeight) // yes, height\n        rotateUp((2 * Math.PI * rotateDelta.y) / element.clientHeight)\n      }\n      rotateStart.copy(rotateEnd)\n    }\n\n    function handleTouchMovePan(event: PointerEvent) {\n      if (pointers.length == 1) {\n        panEnd.set(event.pageX, event.pageY)\n      } else {\n        const position = getSecondPointerPosition(event)\n        const x = 0.5 * (event.pageX + position.x)\n        const y = 0.5 * (event.pageY + position.y)\n        panEnd.set(x, y)\n      }\n\n      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed)\n      pan(panDelta.x, panDelta.y)\n      panStart.copy(panEnd)\n    }\n\n    function handleTouchMoveDolly(event: PointerEvent) {\n      const position = getSecondPointerPosition(event)\n      const dx = event.pageX - position.x\n      const dy = event.pageY - position.y\n      const distance = Math.sqrt(dx * dx + dy * dy)\n\n      dollyEnd.set(0, distance)\n      dollyDelta.set(0, Math.pow(dollyEnd.y / dollyStart.y, scope.zoomSpeed))\n      dollyOut(dollyDelta.y)\n      dollyStart.copy(dollyEnd)\n    }\n\n    function handleTouchMoveDollyPan(event: PointerEvent) {\n      if (scope.enableZoom) handleTouchMoveDolly(event)\n      if (scope.enablePan) handleTouchMovePan(event)\n    }\n\n    function handleTouchMoveDollyRotate(event: PointerEvent) {\n      if (scope.enableZoom) handleTouchMoveDolly(event)\n      if (scope.enableRotate) handleTouchMoveRotate(event)\n    }\n\n    //\n    // event handlers - FSM: listen for events and reset state\n    //\n\n    function onPointerDown(event: PointerEvent) {\n      if (scope.enabled === false) return\n\n      if (pointers.length === 0) {\n        scope.domElement?.ownerDocument.addEventListener('pointermove', onPointerMove)\n        scope.domElement?.ownerDocument.addEventListener('pointerup', onPointerUp)\n      }\n\n      addPointer(event)\n\n      if (event.pointerType === 'touch') {\n        onTouchStart(event)\n      } else {\n        onMouseDown(event)\n      }\n    }\n\n    function onPointerMove(event: PointerEvent) {\n      if (scope.enabled === false) return\n\n      if (event.pointerType === 'touch') {\n        onTouchMove(event)\n      } else {\n        onMouseMove(event)\n      }\n    }\n\n    function onPointerUp(event: PointerEvent) {\n      removePointer(event)\n\n      if (pointers.length === 0) {\n        scope.domElement?.releasePointerCapture(event.pointerId)\n\n        scope.domElement?.ownerDocument.removeEventListener('pointermove', onPointerMove)\n        scope.domElement?.ownerDocument.removeEventListener('pointerup', onPointerUp)\n      }\n\n      // @ts-ignore\n      scope.dispatchEvent(endEvent)\n\n      state = STATE.NONE\n    }\n\n    function onMouseDown(event: MouseEvent) {\n      let mouseAction\n\n      switch (event.button) {\n        case 0:\n          mouseAction = scope.mouseButtons.LEFT\n          break\n\n        case 1:\n          mouseAction = scope.mouseButtons.MIDDLE\n          break\n\n        case 2:\n          mouseAction = scope.mouseButtons.RIGHT\n          break\n\n        default:\n          mouseAction = -1\n      }\n\n      switch (mouseAction) {\n        case MOUSE.DOLLY:\n          if (scope.enableZoom === false) return\n          handleMouseDownDolly(event)\n          state = STATE.DOLLY\n          break\n\n        case MOUSE.ROTATE:\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            if (scope.enablePan === false) return\n            handleMouseDownPan(event)\n            state = STATE.PAN\n          } else {\n            if (scope.enableRotate === false) return\n            handleMouseDownRotate(event)\n            state = STATE.ROTATE\n          }\n          break\n\n        case MOUSE.PAN:\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            if (scope.enableRotate === false) return\n            handleMouseDownRotate(event)\n            state = STATE.ROTATE\n          } else {\n            if (scope.enablePan === false) return\n            handleMouseDownPan(event)\n            state = STATE.PAN\n          }\n          break\n\n        default:\n          state = STATE.NONE\n      }\n\n      if (state !== STATE.NONE) {\n        // @ts-ignore\n        scope.dispatchEvent(startEvent)\n      }\n    }\n\n    function onMouseMove(event: MouseEvent) {\n      if (scope.enabled === false) return\n\n      switch (state) {\n        case STATE.ROTATE:\n          if (scope.enableRotate === false) return\n          handleMouseMoveRotate(event)\n          break\n\n        case STATE.DOLLY:\n          if (scope.enableZoom === false) return\n          handleMouseMoveDolly(event)\n          break\n\n        case STATE.PAN:\n          if (scope.enablePan === false) return\n          handleMouseMovePan(event)\n          break\n      }\n    }\n\n    function onMouseWheel(event: WheelEvent) {\n      if (scope.enabled === false || scope.enableZoom === false || (state !== STATE.NONE && state !== STATE.ROTATE)) {\n        return\n      }\n\n      event.preventDefault()\n\n      // @ts-ignore\n      scope.dispatchEvent(startEvent)\n\n      handleMouseWheel(event)\n\n      // @ts-ignore\n      scope.dispatchEvent(endEvent)\n    }\n\n    function onKeyDown(event: KeyboardEvent) {\n      if (scope.enabled === false || scope.enablePan === false) return\n      handleKeyDown(event)\n    }\n\n    function onTouchStart(event: PointerEvent) {\n      trackPointer(event)\n\n      switch (pointers.length) {\n        case 1:\n          switch (scope.touches.ONE) {\n            case TOUCH.ROTATE:\n              if (scope.enableRotate === false) return\n              handleTouchStartRotate()\n              state = STATE.TOUCH_ROTATE\n              break\n\n            case TOUCH.PAN:\n              if (scope.enablePan === false) return\n              handleTouchStartPan()\n              state = STATE.TOUCH_PAN\n              break\n\n            default:\n              state = STATE.NONE\n          }\n\n          break\n\n        case 2:\n          switch (scope.touches.TWO) {\n            case TOUCH.DOLLY_PAN:\n              if (scope.enableZoom === false && scope.enablePan === false) return\n              handleTouchStartDollyPan()\n              state = STATE.TOUCH_DOLLY_PAN\n              break\n\n            case TOUCH.DOLLY_ROTATE:\n              if (scope.enableZoom === false && scope.enableRotate === false) return\n              handleTouchStartDollyRotate()\n              state = STATE.TOUCH_DOLLY_ROTATE\n              break\n\n            default:\n              state = STATE.NONE\n          }\n\n          break\n\n        default:\n          state = STATE.NONE\n      }\n\n      if (state !== STATE.NONE) {\n        // @ts-ignore\n        scope.dispatchEvent(startEvent)\n      }\n    }\n\n    function onTouchMove(event: PointerEvent) {\n      trackPointer(event)\n\n      switch (state) {\n        case STATE.TOUCH_ROTATE:\n          if (scope.enableRotate === false) return\n          handleTouchMoveRotate(event)\n          scope.update()\n          break\n\n        case STATE.TOUCH_PAN:\n          if (scope.enablePan === false) return\n          handleTouchMovePan(event)\n          scope.update()\n          break\n\n        case STATE.TOUCH_DOLLY_PAN:\n          if (scope.enableZoom === false && scope.enablePan === false) return\n          handleTouchMoveDollyPan(event)\n          scope.update()\n          break\n\n        case STATE.TOUCH_DOLLY_ROTATE:\n          if (scope.enableZoom === false && scope.enableRotate === false) return\n          handleTouchMoveDollyRotate(event)\n          scope.update()\n          break\n\n        default:\n          state = STATE.NONE\n      }\n    }\n\n    function onContextMenu(event: Event) {\n      if (scope.enabled === false) return\n      event.preventDefault()\n    }\n\n    function addPointer(event: PointerEvent) {\n      pointers.push(event)\n    }\n\n    function removePointer(event: PointerEvent) {\n      delete pointerPositions[event.pointerId]\n\n      for (let i = 0; i < pointers.length; i++) {\n        if (pointers[i].pointerId == event.pointerId) {\n          pointers.splice(i, 1)\n          return\n        }\n      }\n    }\n\n    function trackPointer(event: PointerEvent) {\n      let position = pointerPositions[event.pointerId]\n\n      if (position === undefined) {\n        position = new Vector2()\n        pointerPositions[event.pointerId] = position\n      }\n\n      position.set(event.pageX, event.pageY)\n    }\n\n    function getSecondPointerPosition(event: PointerEvent) {\n      const pointer = event.pointerId === pointers[0].pointerId ? pointers[1] : pointers[0]\n      return pointerPositions[pointer.pointerId]\n    }\n\n    // Add dolly in/out methods for public API\n\n    this.dollyIn = (dollyScale = getZoomScale()) => {\n      dollyIn(dollyScale)\n      scope.update()\n    }\n\n    this.dollyOut = (dollyScale = getZoomScale()) => {\n      dollyOut(dollyScale)\n      scope.update()\n    }\n\n    this.getScale = () => {\n      return scale\n    }\n\n    this.setScale = (newScale) => {\n      setScale(newScale)\n      scope.update()\n    }\n\n    this.getZoomScale = () => {\n      return getZoomScale()\n    }\n\n    // connect events\n    if (domElement !== undefined) this.connect(domElement)\n    // force an update at start\n    this.update()\n  }\n}\n\n// This set of controls performs orbiting, dollying (zooming), and panning.\n// Unlike TrackballControls, it maintains the \"up\" direction object.up (+Y by default).\n// This is very similar to OrbitControls, another set of touch behavior\n//\n//    Orbit - right mouse, or left mouse + ctrl/meta/shiftKey / touch: two-finger rotate\n//    Zoom - middle mouse, or mousewheel / touch: two-finger spread or squish\n//    Pan - left mouse, or arrow keys / touch: one-finger move\n\nclass MapControls extends OrbitControls {\n  constructor(object: PerspectiveCamera | OrthographicCamera, domElement?: HTMLElement) {\n    super(object, domElement)\n\n    this.screenSpacePanning = false // pan orthogonal to world-space direction camera.up\n\n    this.mouseButtons.LEFT = MOUSE.PAN\n    this.mouseButtons.RIGHT = MOUSE.ROTATE\n\n    this.touches.ONE = TOUCH.PAN\n    this.touches.TWO = TOUCH.DOLLY_ROTATE\n  }\n}\n\nexport { OrbitControls, MapControls }\n"], "names": ["dom<PERSON>lement", "panLeft", "panUp", "pan"], "mappings": ";;;;;;;;AAgBA,MAAM,2BAA2B;AACjC,MAAM,6BAA6B;AACnC,MAAM,aAAa,KAAK,IAAI,MAAM,KAAK,KAAK,IAAI;AAShD,MAAM,mBAAmB,CAAC,QAAgB,cAAuB,SAAS,WAAY,YAAY;AAElG,MAAM,sBAAsB,gBAA0C;AAAA,EA6FpE,YAAY,QAAgD,YAA0B;AAC9E;AA7FR;AACA;AAEA;AAAA,mCAAU;AAEV;AAAA,kCAAS,IAAI;AAEb;AAAA,uCAAc;AACd,uCAAc;AAEd;AAAA,mCAAU;AACV,mCAAU;AAGV;AAAA;AAAA,yCAAgB;AAChB;AAAA,yCAAgB,KAAK;AAGrB;AAAA;AAAA;AAAA,2CAAkB;AAClB;AAAA,2CAAkB;AAGlB;AAAA;AAAA;AAAA,yCAAgB;AAChB,yCAAgB;AAGhB;AAAA;AAAA,sCAAa;AACb,qCAAY;AAEZ;AAAA,wCAAe;AACf,uCAAc;AAEd;AAAA,qCAAY;AACZ,oCAAW;AACX,8CAAqB;AACrB;AAAA,uCAAc;AACd;AAAA,wCAAe;AAGf;AAAA;AAAA,sCAAa;AACb,2CAAkB;AAClB;AAAA,wCAAe;AACf;AAAA,kDAAyB;AACzB;AAAA,gDAAuB;AAEvB;AAAA;AAAA,gCAAO,EAAE,MAAM,aAAa,IAAI,WAAW,OAAO,cAAc,QAAQ;AAExE;AAAA,wCAIK;AAAA,MACH,MAAM,MAAM;AAAA,MACZ,QAAQ,MAAM;AAAA,MACd,OAAO,MAAM;AAAA,IAAA;AAGf;AAAA,mCAGK,EAAE,KAAK,MAAM,QAAQ,KAAK,MAAM;AACrC;AACA;AACA;AAEA;AAAA,gDAA4B;AAE5B;AACA;AACA;AACA;AACA;AAEA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAAA;AAEA;AAAA;AAEA;AAAA;AAEA;AAAA;AAKE,SAAK,SAAS;AACd,SAAK,aAAa;AAGb,SAAA,UAAU,KAAK,OAAO,MAAM;AACjC,SAAK,YAAY,KAAK,OAAO,SAAS,MAAM;AACvC,SAAA,QAAQ,KAAK,OAAO;AAMpB,SAAA,gBAAgB,MAAc,UAAU;AAExC,SAAA,oBAAoB,MAAc,UAAU;AAE5C,SAAA,gBAAgB,CAAC,UAAwB;AAE5C,UAAI,MAAM,iBAAiB,OAAO,IAAI,KAAK,EAAE;AAC7C,UAAI,aAAa,UAAU;AAG3B,UAAI,aAAa;AAAG,sBAAc,IAAI,KAAK;AAC3C,UAAI,MAAM;AAAG,eAAO,IAAI,KAAK;AAC7B,UAAI,UAAU,KAAK,IAAI,MAAM,UAAU;AACvC,UAAI,IAAI,KAAK,KAAK,UAAU,SAAS;AACnC,YAAI,MAAM,YAAY;AACpB,iBAAO,IAAI,KAAK;AAAA,QAAA,OACX;AACL,wBAAc,IAAI,KAAK;AAAA,QACzB;AAAA,MACF;AACA,qBAAe,MAAM,MAAM;AAC3B,YAAM,OAAO;AAAA,IAAA;AAGV,SAAA,oBAAoB,CAAC,UAAwB;AAEhD,UAAI,QAAQ,iBAAiB,OAAO,IAAI,KAAK,EAAE;AAC/C,UAAI,eAAe,UAAU;AAG7B,UAAI,eAAe;AAAG,wBAAgB,IAAI,KAAK;AAC/C,UAAI,QAAQ;AAAG,iBAAS,IAAI,KAAK;AACjC,UAAI,YAAY,KAAK,IAAI,QAAQ,YAAY;AAC7C,UAAI,IAAI,KAAK,KAAK,YAAY,WAAW;AACvC,YAAI,QAAQ,cAAc;AACxB,mBAAS,IAAI,KAAK;AAAA,QAAA,OACb;AACL,0BAAgB,IAAI,KAAK;AAAA,QAC3B;AAAA,MACF;AACA,qBAAe,QAAQ,QAAQ;AAC/B,YAAM,OAAO;AAAA,IAAA;AAGf,SAAK,cAAc,MAAc,MAAM,OAAO,SAAS,WAAW,MAAM,MAAM;AAEzE,SAAA,oBAAoB,CAACA,gBAAkC;AAC1DA,kBAAW,iBAAiB,WAAW,SAAS;AAChD,WAAK,uBAAuBA;AAAAA,IAAA;AAG9B,SAAK,wBAAwB,MAAY;AAClC,WAAA,qBAAqB,oBAAoB,WAAW,SAAS;AAClE,WAAK,uBAAuB;AAAA,IAAA;AAG9B,SAAK,YAAY,MAAY;AACrB,YAAA,QAAQ,KAAK,MAAM,MAAM;AAC/B,YAAM,UAAU,KAAK,MAAM,OAAO,QAAQ;AACpC,YAAA,QAAQ,MAAM,OAAO;AAAA,IAAA;AAG7B,SAAK,QAAQ,MAAY;AACjB,YAAA,OAAO,KAAK,MAAM,OAAO;AAC/B,YAAM,OAAO,SAAS,KAAK,MAAM,SAAS;AACpC,YAAA,OAAO,OAAO,MAAM;AAC1B,YAAM,OAAO;AAGb,YAAM,cAAc,WAAW;AAE/B,YAAM,OAAO;AAEb,cAAQ,MAAM;AAAA,IAAA;AAIhB,SAAK,UAAU,MAAoB;AAC3B,YAAA,SAAS,IAAI;AACnB,YAAM,KAAK,IAAI,QAAQ,GAAG,GAAG,CAAC;AAG9B,YAAM,OAAO,IAAI,aAAa,mBAAmB,OAAO,IAAI,EAAE;AAC9D,YAAM,cAAc,KAAK,MAAM,EAAE,OAAO;AAElC,YAAA,eAAe,IAAI;AACnB,YAAA,iBAAiB,IAAI;AAErB,YAAA,QAAQ,IAAI,KAAK;AAEvB,aAAO,SAAS,SAAkB;AAC1B,cAAA,WAAW,MAAM,OAAO;AAGzB,aAAA,mBAAmB,OAAO,IAAI,EAAE;AACzB,oBAAA,KAAK,IAAI,EAAE,OAAO;AAE9B,eAAO,KAAK,QAAQ,EAAE,IAAI,MAAM,MAAM;AAGtC,eAAO,gBAAgB,IAAI;AAG3B,kBAAU,eAAe,MAAM;AAE/B,YAAI,MAAM,cAAc,UAAU,MAAM,MAAM;AAC5C,qBAAW,sBAAsB;AAAA,QACnC;AAEA,YAAI,MAAM,eAAe;AACb,oBAAA,SAAS,eAAe,QAAQ,MAAM;AACtC,oBAAA,OAAO,eAAe,MAAM,MAAM;AAAA,QAAA,OACvC;AACL,oBAAU,SAAS,eAAe;AAClC,oBAAU,OAAO,eAAe;AAAA,QAClC;AAIA,YAAI,MAAM,MAAM;AAChB,YAAI,MAAM,MAAM;AAEhB,YAAI,SAAS,GAAG,KAAK,SAAS,GAAG,GAAG;AAC9B,cAAA,MAAM,CAAC,KAAK;AAAW,mBAAA;AAAA,mBAClB,MAAM,KAAK;AAAW,mBAAA;AAE3B,cAAA,MAAM,CAAC,KAAK;AAAW,mBAAA;AAAA,mBAClB,MAAM,KAAK;AAAW,mBAAA;AAE/B,cAAI,OAAO,KAAK;AACJ,sBAAA,QAAQ,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,UAAU,KAAK,CAAC;AAAA,UAAA,OACzD;AACL,sBAAU,QACR,UAAU,SAAS,MAAM,OAAO,IAAI,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI,KAAK,IAAI,KAAK,UAAU,KAAK;AAAA,UACtG;AAAA,QACF;AAGU,kBAAA,MAAM,KAAK,IAAI,MAAM,eAAe,KAAK,IAAI,MAAM,eAAe,UAAU,GAAG,CAAC;AAC1F,kBAAU,SAAS;AAIf,YAAA,MAAM,kBAAkB,MAAM;AAChC,gBAAM,OAAO,gBAAgB,WAAW,MAAM,aAAa;AAAA,QAAA,OACtD;AACC,gBAAA,OAAO,IAAI,SAAS;AAAA,QAC5B;AAIA,YAAK,MAAM,gBAAgB,qBAAuB,MAAM,OAA8B,sBAAsB;AAChG,oBAAA,SAAS,cAAc,UAAU,MAAM;AAAA,QAAA,OAC5C;AACL,oBAAU,SAAS,cAAc,UAAU,SAAS,KAAK;AAAA,QAC3D;AAEA,eAAO,iBAAiB,SAAS;AAGjC,eAAO,gBAAgB,WAAW;AAElC,iBAAS,KAAK,MAAM,MAAM,EAAE,IAAI,MAAM;AAElC,YAAA,CAAC,MAAM,OAAO;AAAkB,gBAAM,OAAO;AAC3C,cAAA,OAAO,OAAO,MAAM,MAAM;AAE5B,YAAA,MAAM,kBAAkB,MAAM;AACjB,yBAAA,SAAS,IAAI,MAAM;AACnB,yBAAA,OAAO,IAAI,MAAM;AAEtB,oBAAA,eAAe,IAAI,MAAM,aAAa;AAAA,QAAA,OAC3C;AACU,yBAAA,IAAI,GAAG,GAAG,CAAC;AAEhB,oBAAA,IAAI,GAAG,GAAG,CAAC;AAAA,QACvB;AAGA,YAAI,cAAc;AACd,YAAA,MAAM,gBAAgB,mBAAmB;AAC3C,cAAI,YAAY;AAChB,cAAI,MAAM,kBAAkB,qBAAqB,MAAM,OAAO,qBAAqB;AAG3E,kBAAA,aAAa,OAAO;AACd,wBAAA,cAAc,aAAa,KAAK;AAE5C,kBAAM,cAAc,aAAa;AACjC,kBAAM,OAAO,SAAS,gBAAgB,gBAAgB,WAAW;AACjE,kBAAM,OAAO;UAAkB,WACrB,MAAM,OAA8B,sBAAsB;AAEpE,kBAAM,cAAc,IAAI,QAAQ,MAAM,GAAG,MAAM,GAAG,CAAC;AACvC,wBAAA,UAAU,MAAM,MAAM;AAElC,kBAAM,OAAO,OAAO,KAAK,IAAI,MAAM,SAAS,KAAK,IAAI,MAAM,SAAS,MAAM,OAAO,OAAO,KAAK,CAAC;AAC9F,kBAAM,OAAO;AACC,0BAAA;AAEd,kBAAM,aAAa,IAAI,QAAQ,MAAM,GAAG,MAAM,GAAG,CAAC;AACvC,uBAAA,UAAU,MAAM,MAAM;AAEjC,kBAAM,OAAO,SAAS,IAAI,UAAU,EAAE,IAAI,WAAW;AACrD,kBAAM,OAAO;AAEb,wBAAY,OAAO;UAAO,OACrB;AACL,oBAAQ,KAAK,yFAAyF;AACtG,kBAAM,eAAe;AAAA,UACvB;AAGA,cAAI,cAAc,MAAM;AACtB,gBAAI,MAAM,oBAAoB;AAE5B,oBAAM,OACH,IAAI,GAAG,GAAG,EAAE,EACZ,mBAAmB,MAAM,OAAO,MAAM,EACtC,eAAe,SAAS,EACxB,IAAI,MAAM,OAAO,QAAQ;AAAA,YAAA,OACvB;AAEL,mBAAK,OAAO,KAAK,MAAM,OAAO,QAAQ;AACjC,mBAAA,UAAU,IAAI,GAAG,GAAG,EAAE,EAAE,mBAAmB,MAAM,OAAO,MAAM;AAI/D,kBAAA,KAAK,IAAI,MAAM,OAAO,GAAG,IAAI,KAAK,SAAS,CAAC,IAAI,YAAY;AACvD,uBAAA,OAAO,MAAM,MAAM;AAAA,cAAA,OACrB;AACL,uBAAO,8BAA8B,MAAM,OAAO,IAAI,MAAM,MAAM;AAC7D,qBAAA,eAAe,QAAQ,MAAM,MAAM;AAAA,cAC1C;AAAA,YACF;AAAA,UACF;AAAA,QAAA,WACS,MAAM,kBAAkB,sBAAsB,MAAM,OAAO,sBAAsB;AAC1F,wBAAc,UAAU;AAExB,cAAI,aAAa;AACf,kBAAM,OAAO,OAAO,KAAK,IAAI,MAAM,SAAS,KAAK,IAAI,MAAM,SAAS,MAAM,OAAO,OAAO,KAAK,CAAC;AAC9F,kBAAM,OAAO;UACf;AAAA,QACF;AAEQ,gBAAA;AACY,4BAAA;AAMpB,YACE,eACA,aAAa,kBAAkB,MAAM,OAAO,QAAQ,IAAI,OACxD,KAAK,IAAI,eAAe,IAAI,MAAM,OAAO,UAAU,KAAK,KACxD;AAEA,gBAAM,cAAc,WAAW;AAElB,uBAAA,KAAK,MAAM,OAAO,QAAQ;AACxB,yBAAA,KAAK,MAAM,OAAO,UAAU;AAC7B,wBAAA;AAEP,iBAAA;AAAA,QACT;AAEO,eAAA;AAAA,MAAA;AAAA,IACT;AAIG,SAAA,UAAU,CAACA,gBAAkC;AAChD,YAAM,aAAaA;AAIb,YAAA,WAAW,MAAM,cAAc;AAC/B,YAAA,WAAW,iBAAiB,eAAe,aAAa;AACxD,YAAA,WAAW,iBAAiB,eAAe,aAAa;AACxD,YAAA,WAAW,iBAAiB,iBAAiB,WAAW;AACxD,YAAA,WAAW,iBAAiB,SAAS,YAAY;AAAA,IAAA;AAGzD,SAAK,UAAU,MAAY;;AAEzB,UAAI,MAAM,YAAY;AACd,cAAA,WAAW,MAAM,cAAc;AAAA,MACvC;AACM,kBAAA,eAAA,mBAAY,oBAAoB,eAAe;AAC/C,kBAAA,eAAA,mBAAY,oBAAoB,eAAe;AAC/C,kBAAA,eAAA,mBAAY,oBAAoB,iBAAiB;AACjD,kBAAA,eAAA,mBAAY,oBAAoB,SAAS;AAC/C,kBAAM,eAAN,mBAAkB,cAAc,oBAAoB,eAAe;AACnE,kBAAM,eAAN,mBAAkB,cAAc,oBAAoB,aAAa;AAC7D,UAAA,MAAM,yBAAyB,MAAM;AACjC,cAAA,qBAAqB,oBAAoB,WAAW,SAAS;AAAA,MACrE;AAAA,IAAA;AAQF,UAAM,QAAQ;AAER,UAAA,cAAc,EAAE,MAAM;AACtB,UAAA,aAAa,EAAE,MAAM;AACrB,UAAA,WAAW,EAAE,MAAM;AAEzB,UAAM,QAAQ;AAAA,MACZ,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,cAAc;AAAA,MACd,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,IAAA;AAGtB,QAAI,QAAQ,MAAM;AAElB,UAAM,MAAM;AAGN,UAAA,YAAY,IAAI;AAChB,UAAA,iBAAiB,IAAI;AAE3B,QAAI,QAAQ;AACN,UAAA,YAAY,IAAI;AAEhB,UAAA,cAAc,IAAI;AAClB,UAAA,YAAY,IAAI;AAChB,UAAA,cAAc,IAAI;AAElB,UAAA,WAAW,IAAI;AACf,UAAA,SAAS,IAAI;AACb,UAAA,WAAW,IAAI;AAEf,UAAA,aAAa,IAAI;AACjB,UAAA,WAAW,IAAI;AACf,UAAA,aAAa,IAAI;AAEjB,UAAA,iBAAiB,IAAI;AACrB,UAAA,QAAQ,IAAI;AAClB,QAAI,oBAAoB;AAExB,UAAM,WAA2B,CAAA;AACjC,UAAM,mBAA+C,CAAA;AAErD,aAAS,uBAA+B;AACtC,aAAS,IAAI,KAAK,KAAM,KAAK,KAAM,MAAM;AAAA,IAC3C;AAEA,aAAS,eAAuB;AAC9B,aAAO,KAAK,IAAI,MAAM,MAAM,SAAS;AAAA,IACvC;AAEA,aAAS,WAAW,OAAqB;AACnC,UAAA,MAAM,gBAAgB,MAAM,wBAAwB;AACtD,uBAAe,SAAS;AAAA,MAAA,OACnB;AACL,uBAAe,SAAS;AAAA,MAC1B;AAAA,IACF;AAEA,aAAS,SAAS,OAAqB;AACjC,UAAA,MAAM,gBAAgB,MAAM,sBAAsB;AACpD,uBAAe,OAAO;AAAA,MAAA,OACjB;AACL,uBAAe,OAAO;AAAA,MACxB;AAAA,IACF;AAEA,UAAM,WAAW,MAAM;AACf,YAAA,IAAI,IAAI;AAEP,aAAA,SAASC,SAAQ,UAAkB,cAAuB;AAC7D,UAAA,oBAAoB,cAAc,CAAC;AACnC,UAAA,eAAe,CAAC,QAAQ;AAE1B,kBAAU,IAAI,CAAC;AAAA,MAAA;AAAA,IACjB;AAGF,UAAM,SAAS,MAAM;AACb,YAAA,IAAI,IAAI;AAEP,aAAA,SAASC,OAAM,UAAkB,cAAuB;AACzD,YAAA,MAAM,uBAAuB,MAAM;AACnC,YAAA,oBAAoB,cAAc,CAAC;AAAA,QAAA,OAChC;AACH,YAAA,oBAAoB,cAAc,CAAC;AACrC,YAAE,aAAa,MAAM,OAAO,IAAI,CAAC;AAAA,QACnC;AAEA,UAAE,eAAe,QAAQ;AAEzB,kBAAU,IAAI,CAAC;AAAA,MAAA;AAAA,IACjB;AAIF,UAAM,OAAO,MAAM;AACX,YAAA,SAAS,IAAI;AAEZ,aAAA,SAASC,KAAI,QAAgB,QAAgB;AAClD,cAAM,UAAU,MAAM;AAEtB,YAAI,WAAW,MAAM,kBAAkB,qBAAqB,MAAM,OAAO,qBAAqB;AAEtF,gBAAA,WAAW,MAAM,OAAO;AAC9B,iBAAO,KAAK,QAAQ,EAAE,IAAI,MAAM,MAAM;AAClC,cAAA,iBAAiB,OAAO;AAGV,4BAAA,KAAK,IAAM,MAAM,OAAO,MAAM,IAAK,KAAK,KAAM,GAAK;AAGrE,kBAAS,IAAI,SAAS,iBAAkB,QAAQ,cAAc,MAAM,OAAO,MAAM;AACjF,gBAAO,IAAI,SAAS,iBAAkB,QAAQ,cAAc,MAAM,OAAO,MAAM;AAAA,QAAA,WACtE,WAAW,MAAM,kBAAkB,sBAAsB,MAAM,OAAO,sBAAsB;AAErG;AAAA,YACG,UAAU,MAAM,OAAO,QAAQ,MAAM,OAAO,QAAS,MAAM,OAAO,OAAO,QAAQ;AAAA,YAClF,MAAM,OAAO;AAAA,UAAA;AAEf;AAAA,YACG,UAAU,MAAM,OAAO,MAAM,MAAM,OAAO,UAAW,MAAM,OAAO,OAAO,QAAQ;AAAA,YAClF,MAAM,OAAO;AAAA,UAAA;AAAA,QACf,OACK;AAEL,kBAAQ,KAAK,8EAA8E;AAC3F,gBAAM,YAAY;AAAA,QACpB;AAAA,MAAA;AAAA,IACF;AAGF,aAAS,SAAS,UAAkB;AAE/B,UAAA,MAAM,kBAAkB,qBAAqB,MAAM,OAAO,uBAC1D,MAAM,kBAAkB,sBAAsB,MAAM,OAAO,sBAC5D;AACQ,gBAAA;AAAA,MAAA,OACH;AACL,gBAAQ,KAAK,qFAAqF;AAClG,cAAM,aAAa;AAAA,MACrB;AAAA,IACF;AAEA,aAAS,SAAS,YAAoB;AACpC,eAAS,QAAQ,UAAU;AAAA,IAC7B;AAEA,aAAS,QAAQ,YAAoB;AACnC,eAAS,QAAQ,UAAU;AAAA,IAC7B;AAEA,aAAS,sBAAsB,OAAyB;AACtD,UAAI,CAAC,MAAM,gBAAgB,CAAC,MAAM,YAAY;AAC5C;AAAA,MACF;AAEoB,0BAAA;AAEd,YAAA,OAAO,MAAM,WAAW,sBAAsB;AAC9C,YAAA,IAAI,MAAM,UAAU,KAAK;AACzB,YAAA,IAAI,MAAM,UAAU,KAAK;AAC/B,YAAM,IAAI,KAAK;AACf,YAAM,IAAI,KAAK;AAET,YAAA,IAAK,IAAI,IAAK,IAAI;AACxB,YAAM,IAAI,EAAE,IAAI,KAAK,IAAI;AAEzB,qBAAe,IAAI,MAAM,GAAG,MAAM,GAAG,CAAC,EAAE,UAAU,MAAM,MAAM,EAAE,IAAI,MAAM,OAAO,QAAQ,EAAE;IAC7F;AAEA,aAAS,cAAc,MAAsB;AACpC,aAAA,KAAK,IAAI,MAAM,aAAa,KAAK,IAAI,MAAM,aAAa,IAAI,CAAC;AAAA,IACtE;AAMA,aAAS,sBAAsB,OAAmB;AAChD,kBAAY,IAAI,MAAM,SAAS,MAAM,OAAO;AAAA,IAC9C;AAEA,aAAS,qBAAqB,OAAmB;AAC/C,4BAAsB,KAAK;AAC3B,iBAAW,IAAI,MAAM,SAAS,MAAM,OAAO;AAAA,IAC7C;AAEA,aAAS,mBAAmB,OAAmB;AAC7C,eAAS,IAAI,MAAM,SAAS,MAAM,OAAO;AAAA,IAC3C;AAEA,aAAS,sBAAsB,OAAmB;AAChD,gBAAU,IAAI,MAAM,SAAS,MAAM,OAAO;AAC1C,kBAAY,WAAW,WAAW,WAAW,EAAE,eAAe,MAAM,WAAW;AAE/E,YAAM,UAAU,MAAM;AAEtB,UAAI,SAAS;AACX,mBAAY,IAAI,KAAK,KAAK,YAAY,IAAK,QAAQ,YAAY;AAC/D,iBAAU,IAAI,KAAK,KAAK,YAAY,IAAK,QAAQ,YAAY;AAAA,MAC/D;AACA,kBAAY,KAAK,SAAS;AAC1B,YAAM,OAAO;AAAA,IACf;AAEA,aAAS,qBAAqB,OAAmB;AAC/C,eAAS,IAAI,MAAM,SAAS,MAAM,OAAO;AAC9B,iBAAA,WAAW,UAAU,UAAU;AAEtC,UAAA,WAAW,IAAI,GAAG;AACpB,iBAAS,cAAc;AAAA,MAAA,WACd,WAAW,IAAI,GAAG;AAC3B,gBAAQ,cAAc;AAAA,MACxB;AAEA,iBAAW,KAAK,QAAQ;AACxB,YAAM,OAAO;AAAA,IACf;AAEA,aAAS,mBAAmB,OAAmB;AAC7C,aAAO,IAAI,MAAM,SAAS,MAAM,OAAO;AACvC,eAAS,WAAW,QAAQ,QAAQ,EAAE,eAAe,MAAM,QAAQ;AAC/D,UAAA,SAAS,GAAG,SAAS,CAAC;AAC1B,eAAS,KAAK,MAAM;AACpB,YAAM,OAAO;AAAA,IACf;AAEA,aAAS,iBAAiB,OAAmB;AAC3C,4BAAsB,KAAK;AAEvB,UAAA,MAAM,SAAS,GAAG;AACpB,gBAAQ,cAAc;AAAA,MAAA,WACb,MAAM,SAAS,GAAG;AAC3B,iBAAS,cAAc;AAAA,MACzB;AAEA,YAAM,OAAO;AAAA,IACf;AAEA,aAAS,cAAc,OAAsB;AAC3C,UAAI,cAAc;AAElB,cAAQ,MAAM,MAAM;AAAA,QAClB,KAAK,MAAM,KAAK;AACV,cAAA,GAAG,MAAM,WAAW;AACV,wBAAA;AACd;AAAA,QAEF,KAAK,MAAM,KAAK;AACV,cAAA,GAAG,CAAC,MAAM,WAAW;AACX,wBAAA;AACd;AAAA,QAEF,KAAK,MAAM,KAAK;AACV,cAAA,MAAM,aAAa,CAAC;AACV,wBAAA;AACd;AAAA,QAEF,KAAK,MAAM,KAAK;AACV,cAAA,CAAC,MAAM,aAAa,CAAC;AACX,wBAAA;AACd;AAAA,MACJ;AAEA,UAAI,aAAa;AAEf,cAAM,eAAe;AACrB,cAAM,OAAO;AAAA,MACf;AAAA,IACF;AAEA,aAAS,yBAAyB;AAC5B,UAAA,SAAS,UAAU,GAAG;AACZ,oBAAA,IAAI,SAAS,CAAC,EAAE,OAAO,SAAS,CAAC,EAAE,KAAK;AAAA,MAAA,OAC/C;AACC,cAAA,IAAI,OAAO,SAAS,CAAC,EAAE,QAAQ,SAAS,CAAC,EAAE;AAC3C,cAAA,IAAI,OAAO,SAAS,CAAC,EAAE,QAAQ,SAAS,CAAC,EAAE;AAErC,oBAAA,IAAI,GAAG,CAAC;AAAA,MACtB;AAAA,IACF;AAEA,aAAS,sBAAsB;AACzB,UAAA,SAAS,UAAU,GAAG;AACf,iBAAA,IAAI,SAAS,CAAC,EAAE,OAAO,SAAS,CAAC,EAAE,KAAK;AAAA,MAAA,OAC5C;AACC,cAAA,IAAI,OAAO,SAAS,CAAC,EAAE,QAAQ,SAAS,CAAC,EAAE;AAC3C,cAAA,IAAI,OAAO,SAAS,CAAC,EAAE,QAAQ,SAAS,CAAC,EAAE;AAExC,iBAAA,IAAI,GAAG,CAAC;AAAA,MACnB;AAAA,IACF;AAEA,aAAS,wBAAwB;AAC/B,YAAM,KAAK,SAAS,CAAC,EAAE,QAAQ,SAAS,CAAC,EAAE;AAC3C,YAAM,KAAK,SAAS,CAAC,EAAE,QAAQ,SAAS,CAAC,EAAE;AAC3C,YAAM,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAEjC,iBAAA,IAAI,GAAG,QAAQ;AAAA,IAC5B;AAEA,aAAS,2BAA2B;AAClC,UAAI,MAAM;AAAkC;AAC5C,UAAI,MAAM;AAA+B;IAC3C;AAEA,aAAS,8BAA8B;AACrC,UAAI,MAAM;AAAkC;AAC5C,UAAI,MAAM;AAAqC;IACjD;AAEA,aAAS,sBAAsB,OAAqB;AAC9C,UAAA,SAAS,UAAU,GAAG;AACxB,kBAAU,IAAI,MAAM,OAAO,MAAM,KAAK;AAAA,MAAA,OACjC;AACC,cAAA,WAAW,yBAAyB,KAAK;AAC/C,cAAM,IAAI,OAAO,MAAM,QAAQ,SAAS;AACxC,cAAM,IAAI,OAAO,MAAM,QAAQ,SAAS;AAC9B,kBAAA,IAAI,GAAG,CAAC;AAAA,MACpB;AAEA,kBAAY,WAAW,WAAW,WAAW,EAAE,eAAe,MAAM,WAAW;AAE/E,YAAM,UAAU,MAAM;AAEtB,UAAI,SAAS;AACX,mBAAY,IAAI,KAAK,KAAK,YAAY,IAAK,QAAQ,YAAY;AAC/D,iBAAU,IAAI,KAAK,KAAK,YAAY,IAAK,QAAQ,YAAY;AAAA,MAC/D;AACA,kBAAY,KAAK,SAAS;AAAA,IAC5B;AAEA,aAAS,mBAAmB,OAAqB;AAC3C,UAAA,SAAS,UAAU,GAAG;AACxB,eAAO,IAAI,MAAM,OAAO,MAAM,KAAK;AAAA,MAAA,OAC9B;AACC,cAAA,WAAW,yBAAyB,KAAK;AAC/C,cAAM,IAAI,OAAO,MAAM,QAAQ,SAAS;AACxC,cAAM,IAAI,OAAO,MAAM,QAAQ,SAAS;AACjC,eAAA,IAAI,GAAG,CAAC;AAAA,MACjB;AAEA,eAAS,WAAW,QAAQ,QAAQ,EAAE,eAAe,MAAM,QAAQ;AAC/D,UAAA,SAAS,GAAG,SAAS,CAAC;AAC1B,eAAS,KAAK,MAAM;AAAA,IACtB;AAEA,aAAS,qBAAqB,OAAqB;AAC3C,YAAA,WAAW,yBAAyB,KAAK;AACzC,YAAA,KAAK,MAAM,QAAQ,SAAS;AAC5B,YAAA,KAAK,MAAM,QAAQ,SAAS;AAClC,YAAM,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAEnC,eAAA,IAAI,GAAG,QAAQ;AACb,iBAAA,IAAI,GAAG,KAAK,IAAI,SAAS,IAAI,WAAW,GAAG,MAAM,SAAS,CAAC;AACtE,eAAS,WAAW,CAAC;AACrB,iBAAW,KAAK,QAAQ;AAAA,IAC1B;AAEA,aAAS,wBAAwB,OAAqB;AACpD,UAAI,MAAM;AAAY,6BAAqB,KAAK;AAChD,UAAI,MAAM;AAAW,2BAAmB,KAAK;AAAA,IAC/C;AAEA,aAAS,2BAA2B,OAAqB;AACvD,UAAI,MAAM;AAAY,6BAAqB,KAAK;AAChD,UAAI,MAAM;AAAc,8BAAsB,KAAK;AAAA,IACrD;AAMA,aAAS,cAAc,OAAqB;;AAC1C,UAAI,MAAM,YAAY;AAAO;AAEzB,UAAA,SAAS,WAAW,GAAG;AACzB,oBAAM,eAAN,mBAAkB,cAAc,iBAAiB,eAAe;AAChE,oBAAM,eAAN,mBAAkB,cAAc,iBAAiB,aAAa;AAAA,MAChE;AAEA,iBAAW,KAAK;AAEZ,UAAA,MAAM,gBAAgB,SAAS;AACjC,qBAAa,KAAK;AAAA,MAAA,OACb;AACL,oBAAY,KAAK;AAAA,MACnB;AAAA,IACF;AAEA,aAAS,cAAc,OAAqB;AAC1C,UAAI,MAAM,YAAY;AAAO;AAEzB,UAAA,MAAM,gBAAgB,SAAS;AACjC,oBAAY,KAAK;AAAA,MAAA,OACZ;AACL,oBAAY,KAAK;AAAA,MACnB;AAAA,IACF;AAEA,aAAS,YAAY,OAAqB;;AACxC,oBAAc,KAAK;AAEf,UAAA,SAAS,WAAW,GAAG;AACnB,oBAAA,eAAA,mBAAY,sBAAsB,MAAM;AAE9C,oBAAM,eAAN,mBAAkB,cAAc,oBAAoB,eAAe;AACnE,oBAAM,eAAN,mBAAkB,cAAc,oBAAoB,aAAa;AAAA,MACnE;AAGA,YAAM,cAAc,QAAQ;AAE5B,cAAQ,MAAM;AAAA,IAChB;AAEA,aAAS,YAAY,OAAmB;AAClC,UAAA;AAEJ,cAAQ,MAAM,QAAQ;AAAA,QACpB,KAAK;AACH,wBAAc,MAAM,aAAa;AACjC;AAAA,QAEF,KAAK;AACH,wBAAc,MAAM,aAAa;AACjC;AAAA,QAEF,KAAK;AACH,wBAAc,MAAM,aAAa;AACjC;AAAA,QAEF;AACgB,wBAAA;AAAA,MAClB;AAEA,cAAQ,aAAa;AAAA,QACnB,KAAK,MAAM;AACT,cAAI,MAAM,eAAe;AAAO;AAChC,+BAAqB,KAAK;AAC1B,kBAAQ,MAAM;AACd;AAAA,QAEF,KAAK,MAAM;AACT,cAAI,MAAM,WAAW,MAAM,WAAW,MAAM,UAAU;AACpD,gBAAI,MAAM,cAAc;AAAO;AAC/B,+BAAmB,KAAK;AACxB,oBAAQ,MAAM;AAAA,UAAA,OACT;AACL,gBAAI,MAAM,iBAAiB;AAAO;AAClC,kCAAsB,KAAK;AAC3B,oBAAQ,MAAM;AAAA,UAChB;AACA;AAAA,QAEF,KAAK,MAAM;AACT,cAAI,MAAM,WAAW,MAAM,WAAW,MAAM,UAAU;AACpD,gBAAI,MAAM,iBAAiB;AAAO;AAClC,kCAAsB,KAAK;AAC3B,oBAAQ,MAAM;AAAA,UAAA,OACT;AACL,gBAAI,MAAM,cAAc;AAAO;AAC/B,+BAAmB,KAAK;AACxB,oBAAQ,MAAM;AAAA,UAChB;AACA;AAAA,QAEF;AACE,kBAAQ,MAAM;AAAA,MAClB;AAEI,UAAA,UAAU,MAAM,MAAM;AAExB,cAAM,cAAc,UAAU;AAAA,MAChC;AAAA,IACF;AAEA,aAAS,YAAY,OAAmB;AACtC,UAAI,MAAM,YAAY;AAAO;AAE7B,cAAQ,OAAO;AAAA,QACb,KAAK,MAAM;AACT,cAAI,MAAM,iBAAiB;AAAO;AAClC,gCAAsB,KAAK;AAC3B;AAAA,QAEF,KAAK,MAAM;AACT,cAAI,MAAM,eAAe;AAAO;AAChC,+BAAqB,KAAK;AAC1B;AAAA,QAEF,KAAK,MAAM;AACT,cAAI,MAAM,cAAc;AAAO;AAC/B,6BAAmB,KAAK;AACxB;AAAA,MACJ;AAAA,IACF;AAEA,aAAS,aAAa,OAAmB;AACnC,UAAA,MAAM,YAAY,SAAS,MAAM,eAAe,SAAU,UAAU,MAAM,QAAQ,UAAU,MAAM,QAAS;AAC7G;AAAA,MACF;AAEA,YAAM,eAAe;AAGrB,YAAM,cAAc,UAAU;AAE9B,uBAAiB,KAAK;AAGtB,YAAM,cAAc,QAAQ;AAAA,IAC9B;AAEA,aAAS,UAAU,OAAsB;AACvC,UAAI,MAAM,YAAY,SAAS,MAAM,cAAc;AAAO;AAC1D,oBAAc,KAAK;AAAA,IACrB;AAEA,aAAS,aAAa,OAAqB;AACzC,mBAAa,KAAK;AAElB,cAAQ,SAAS,QAAQ;AAAA,QACvB,KAAK;AACK,kBAAA,MAAM,QAAQ,KAAK;AAAA,YACzB,KAAK,MAAM;AACT,kBAAI,MAAM,iBAAiB;AAAO;AACX;AACvB,sBAAQ,MAAM;AACd;AAAA,YAEF,KAAK,MAAM;AACT,kBAAI,MAAM,cAAc;AAAO;AACX;AACpB,sBAAQ,MAAM;AACd;AAAA,YAEF;AACE,sBAAQ,MAAM;AAAA,UAClB;AAEA;AAAA,QAEF,KAAK;AACK,kBAAA,MAAM,QAAQ,KAAK;AAAA,YACzB,KAAK,MAAM;AACT,kBAAI,MAAM,eAAe,SAAS,MAAM,cAAc;AAAO;AACpC;AACzB,sBAAQ,MAAM;AACd;AAAA,YAEF,KAAK,MAAM;AACT,kBAAI,MAAM,eAAe,SAAS,MAAM,iBAAiB;AAAO;AACpC;AAC5B,sBAAQ,MAAM;AACd;AAAA,YAEF;AACE,sBAAQ,MAAM;AAAA,UAClB;AAEA;AAAA,QAEF;AACE,kBAAQ,MAAM;AAAA,MAClB;AAEI,UAAA,UAAU,MAAM,MAAM;AAExB,cAAM,cAAc,UAAU;AAAA,MAChC;AAAA,IACF;AAEA,aAAS,YAAY,OAAqB;AACxC,mBAAa,KAAK;AAElB,cAAQ,OAAO;AAAA,QACb,KAAK,MAAM;AACT,cAAI,MAAM,iBAAiB;AAAO;AAClC,gCAAsB,KAAK;AAC3B,gBAAM,OAAO;AACb;AAAA,QAEF,KAAK,MAAM;AACT,cAAI,MAAM,cAAc;AAAO;AAC/B,6BAAmB,KAAK;AACxB,gBAAM,OAAO;AACb;AAAA,QAEF,KAAK,MAAM;AACT,cAAI,MAAM,eAAe,SAAS,MAAM,cAAc;AAAO;AAC7D,kCAAwB,KAAK;AAC7B,gBAAM,OAAO;AACb;AAAA,QAEF,KAAK,MAAM;AACT,cAAI,MAAM,eAAe,SAAS,MAAM,iBAAiB;AAAO;AAChE,qCAA2B,KAAK;AAChC,gBAAM,OAAO;AACb;AAAA,QAEF;AACE,kBAAQ,MAAM;AAAA,MAClB;AAAA,IACF;AAEA,aAAS,cAAc,OAAc;AACnC,UAAI,MAAM,YAAY;AAAO;AAC7B,YAAM,eAAe;AAAA,IACvB;AAEA,aAAS,WAAW,OAAqB;AACvC,eAAS,KAAK,KAAK;AAAA,IACrB;AAEA,aAAS,cAAc,OAAqB;AACnC,aAAA,iBAAiB,MAAM,SAAS;AAEvC,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAI,SAAS,CAAC,EAAE,aAAa,MAAM,WAAW;AACnC,mBAAA,OAAO,GAAG,CAAC;AACpB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,aAAS,aAAa,OAAqB;AACrC,UAAA,WAAW,iBAAiB,MAAM,SAAS;AAE/C,UAAI,aAAa,QAAW;AAC1B,mBAAW,IAAI;AACE,yBAAA,MAAM,SAAS,IAAI;AAAA,MACtC;AAEA,eAAS,IAAI,MAAM,OAAO,MAAM,KAAK;AAAA,IACvC;AAEA,aAAS,yBAAyB,OAAqB;AAC/C,YAAA,UAAU,MAAM,cAAc,SAAS,CAAC,EAAE,YAAY,SAAS,CAAC,IAAI,SAAS,CAAC;AAC7E,aAAA,iBAAiB,QAAQ,SAAS;AAAA,IAC3C;AAIA,SAAK,UAAU,CAAC,aAAa,aAAA,MAAmB;AAC9C,cAAQ,UAAU;AAClB,YAAM,OAAO;AAAA,IAAA;AAGf,SAAK,WAAW,CAAC,aAAa,aAAA,MAAmB;AAC/C,eAAS,UAAU;AACnB,YAAM,OAAO;AAAA,IAAA;AAGf,SAAK,WAAW,MAAM;AACb,aAAA;AAAA,IAAA;AAGJ,SAAA,WAAW,CAAC,aAAa;AAC5B,eAAS,QAAQ;AACjB,YAAM,OAAO;AAAA,IAAA;AAGf,SAAK,eAAe,MAAM;AACxB,aAAO,aAAa;AAAA,IAAA;AAItB,QAAI,eAAe;AAAW,WAAK,QAAQ,UAAU;AAErD,SAAK,OAAO;AAAA,EACd;AACF;AAUA,MAAM,oBAAoB,cAAc;AAAA,EACtC,YAAY,QAAgD,YAA0B;AACpF,UAAM,QAAQ,UAAU;AAExB,SAAK,qBAAqB;AAErB,SAAA,aAAa,OAAO,MAAM;AAC1B,SAAA,aAAa,QAAQ,MAAM;AAE3B,SAAA,QAAQ,MAAM,MAAM;AACpB,SAAA,QAAQ,MAAM,MAAM;AAAA,EAC3B;AACF;"}