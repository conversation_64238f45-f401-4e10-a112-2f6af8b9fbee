{"version": 3, "file": "SelectionHelper.js", "sources": ["../../src/interactive/SelectionHelper.js"], "sourcesContent": ["import { Vector2 } from 'three'\n\nclass SelectionHelper {\n  constructor(selectionBox, renderer, cssClassName) {\n    this.element = document.createElement('div')\n    this.element.classList.add(cssClassName)\n    this.element.style.pointerEvents = 'none'\n\n    this.renderer = renderer\n\n    this.startPoint = new Vector2()\n    this.pointTopLeft = new Vector2()\n    this.pointBottomRight = new Vector2()\n\n    this.isDown = false\n\n    this.renderer.domElement.addEventListener('pointerdown', (event) => {\n      this.isDown = true\n      this.onSelectStart(event)\n    })\n\n    this.renderer.domElement.addEventListener('pointermove', (event) => {\n      if (this.isDown) {\n        this.onSelectMove(event)\n      }\n    })\n\n    this.renderer.domElement.addEventListener('pointerup', (event) => {\n      this.isDown = false\n      this.onSelectOver(event)\n    })\n  }\n\n  onSelectStart(event) {\n    this.renderer.domElement.parentElement.appendChild(this.element)\n\n    this.element.style.left = `${event.clientX}px`\n    this.element.style.top = `${event.clientY}px`\n    this.element.style.width = '0px'\n    this.element.style.height = '0px'\n\n    this.startPoint.x = event.clientX\n    this.startPoint.y = event.clientY\n  }\n\n  onSelectMove(event) {\n    this.pointBottomRight.x = Math.max(this.startPoint.x, event.clientX)\n    this.pointBottomRight.y = Math.max(this.startPoint.y, event.clientY)\n    this.pointTopLeft.x = Math.min(this.startPoint.x, event.clientX)\n    this.pointTopLeft.y = Math.min(this.startPoint.y, event.clientY)\n\n    this.element.style.left = `${this.pointTopLeft.x}px`\n    this.element.style.top = `${this.pointTopLeft.y}px`\n    this.element.style.width = `${this.pointBottomRight.x - this.pointTopLeft.x}px`\n    this.element.style.height = `${this.pointBottomRight.y - this.pointTopLeft.y}px`\n  }\n\n  onSelectOver() {\n    this.element.parentElement.removeChild(this.element)\n  }\n}\n\nexport { SelectionHelper }\n"], "names": [], "mappings": ";AAEA,MAAM,gBAAgB;AAAA,EACpB,YAAY,cAAc,UAAU,cAAc;AAChD,SAAK,UAAU,SAAS,cAAc,KAAK;AAC3C,SAAK,QAAQ,UAAU,IAAI,YAAY;AACvC,SAAK,QAAQ,MAAM,gBAAgB;AAEnC,SAAK,WAAW;AAEhB,SAAK,aAAa,IAAI,QAAS;AAC/B,SAAK,eAAe,IAAI,QAAS;AACjC,SAAK,mBAAmB,IAAI,QAAS;AAErC,SAAK,SAAS;AAEd,SAAK,SAAS,WAAW,iBAAiB,eAAe,CAAC,UAAU;AAClE,WAAK,SAAS;AACd,WAAK,cAAc,KAAK;AAAA,IAC9B,CAAK;AAED,SAAK,SAAS,WAAW,iBAAiB,eAAe,CAAC,UAAU;AAClE,UAAI,KAAK,QAAQ;AACf,aAAK,aAAa,KAAK;AAAA,MACxB;AAAA,IACP,CAAK;AAED,SAAK,SAAS,WAAW,iBAAiB,aAAa,CAAC,UAAU;AAChE,WAAK,SAAS;AACd,WAAK,aAAa,KAAK;AAAA,IAC7B,CAAK;AAAA,EACF;AAAA,EAED,cAAc,OAAO;AACnB,SAAK,SAAS,WAAW,cAAc,YAAY,KAAK,OAAO;AAE/D,SAAK,QAAQ,MAAM,OAAO,GAAG,MAAM;AACnC,SAAK,QAAQ,MAAM,MAAM,GAAG,MAAM;AAClC,SAAK,QAAQ,MAAM,QAAQ;AAC3B,SAAK,QAAQ,MAAM,SAAS;AAE5B,SAAK,WAAW,IAAI,MAAM;AAC1B,SAAK,WAAW,IAAI,MAAM;AAAA,EAC3B;AAAA,EAED,aAAa,OAAO;AAClB,SAAK,iBAAiB,IAAI,KAAK,IAAI,KAAK,WAAW,GAAG,MAAM,OAAO;AACnE,SAAK,iBAAiB,IAAI,KAAK,IAAI,KAAK,WAAW,GAAG,MAAM,OAAO;AACnE,SAAK,aAAa,IAAI,KAAK,IAAI,KAAK,WAAW,GAAG,MAAM,OAAO;AAC/D,SAAK,aAAa,IAAI,KAAK,IAAI,KAAK,WAAW,GAAG,MAAM,OAAO;AAE/D,SAAK,QAAQ,MAAM,OAAO,GAAG,KAAK,aAAa;AAC/C,SAAK,QAAQ,MAAM,MAAM,GAAG,KAAK,aAAa;AAC9C,SAAK,QAAQ,MAAM,QAAQ,GAAG,KAAK,iBAAiB,IAAI,KAAK,aAAa;AAC1E,SAAK,QAAQ,MAAM,SAAS,GAAG,KAAK,iBAAiB,IAAI,KAAK,aAAa;AAAA,EAC5E;AAAA,EAED,eAAe;AACb,SAAK,QAAQ,cAAc,YAAY,KAAK,OAAO;AAAA,EACpD;AACH;"}