{"version": 3, "file": "RGBMLoader.cjs", "sources": ["../../src/loaders/RGBMLoader.js"], "sourcesContent": ["import { DataTextureLoader, RGBAFormat, LinearFilter, CubeTexture, HalfFloatType, DataUtils } from 'three'\n\nlet UPNG\n\nfunction init() {\n  if (UPNG) return UPNG\n  // from https://github.com/photopea/UPNG.js (MIT License)\n\n  UPNG = {}\n\n  UPNG.toRGBA8 = function (out) {\n    var w = out.width,\n      h = out.height\n    if (out.tabs.acTL == null) return [UPNG.toRGBA8.decodeImage(out.data, w, h, out).buffer]\n\n    var frms = []\n    if (out.frames[0].data == null) out.frames[0].data = out.data\n\n    var len = w * h * 4,\n      img = new Uint8Array(len),\n      empty = new Uint8Array(len),\n      prev = new Uint8Array(len)\n    for (var i = 0; i < out.frames.length; i++) {\n      var frm = out.frames[i]\n      var fx = frm.rect.x,\n        fy = frm.rect.y,\n        fw = frm.rect.width,\n        fh = frm.rect.height\n      var fdata = UPNG.toRGBA8.decodeImage(frm.data, fw, fh, out)\n\n      if (i != 0) for (var j = 0; j < len; j++) prev[j] = img[j]\n\n      if (frm.blend == 0) UPNG._copyTile(fdata, fw, fh, img, w, h, fx, fy, 0)\n      else if (frm.blend == 1) UPNG._copyTile(fdata, fw, fh, img, w, h, fx, fy, 1)\n\n      frms.push(img.buffer.slice(0))\n\n      if (frm.dispose == 1) UPNG._copyTile(empty, fw, fh, img, w, h, fx, fy, 0)\n      else if (frm.dispose == 2) for (var j = 0; j < len; j++) img[j] = prev[j]\n    }\n\n    return frms\n  }\n\n  UPNG.toRGBA8.decodeImage = function (data, w, h, out) {\n    var area = w * h,\n      bpp = UPNG.decode._getBPP(out)\n    var bpl = Math.ceil((w * bpp) / 8) // bytes per line\n\n    var bf = new Uint8Array(area * 4),\n      bf32 = new Uint32Array(bf.buffer)\n    var ctype = out.ctype,\n      depth = out.depth\n    var rs = UPNG._bin.readUshort\n\n    if (ctype == 6) {\n      // RGB + alpha\n\n      var qarea = area << 2\n      if (depth == 8) {\n        for (var i = 0; i < qarea; i += 4) {\n          bf[i] = data[i]\n          bf[i + 1] = data[i + 1]\n          bf[i + 2] = data[i + 2]\n          bf[i + 3] = data[i + 3]\n        }\n      }\n\n      if (depth == 16) {\n        for (var i = 0; i < qarea; i++) {\n          bf[i] = data[i << 1]\n        }\n      }\n    } else if (ctype == 2) {\n      // RGB\n\n      var ts = out.tabs['tRNS']\n      if (ts == null) {\n        if (depth == 8) {\n          for (var i = 0; i < area; i++) {\n            var ti = i * 3\n            bf32[i] = (255 << 24) | (data[ti + 2] << 16) | (data[ti + 1] << 8) | data[ti]\n          }\n        }\n\n        if (depth == 16) {\n          for (var i = 0; i < area; i++) {\n            var ti = i * 6\n            bf32[i] = (255 << 24) | (data[ti + 4] << 16) | (data[ti + 2] << 8) | data[ti]\n          }\n        }\n      } else {\n        var tr = ts[0],\n          tg = ts[1],\n          tb = ts[2]\n        if (depth == 8) {\n          for (var i = 0; i < area; i++) {\n            var qi = i << 2,\n              ti = i * 3\n            bf32[i] = (255 << 24) | (data[ti + 2] << 16) | (data[ti + 1] << 8) | data[ti]\n            if (data[ti] == tr && data[ti + 1] == tg && data[ti + 2] == tb) bf[qi + 3] = 0\n          }\n        }\n\n        if (depth == 16) {\n          for (var i = 0; i < area; i++) {\n            var qi = i << 2,\n              ti = i * 6\n            bf32[i] = (255 << 24) | (data[ti + 4] << 16) | (data[ti + 2] << 8) | data[ti]\n            if (rs(data, ti) == tr && rs(data, ti + 2) == tg && rs(data, ti + 4) == tb) bf[qi + 3] = 0\n          }\n        }\n      }\n    } else if (ctype == 3) {\n      // palette\n\n      var p = out.tabs['PLTE'],\n        ap = out.tabs['tRNS'],\n        tl = ap ? ap.length : 0\n      //console.log(p, ap);\n      if (depth == 1) {\n        for (var y = 0; y < h; y++) {\n          var s0 = y * bpl,\n            t0 = y * w\n          for (var i = 0; i < w; i++) {\n            var qi = (t0 + i) << 2,\n              j = (data[s0 + (i >> 3)] >> (7 - ((i & 7) << 0))) & 1,\n              cj = 3 * j\n            bf[qi] = p[cj]\n            bf[qi + 1] = p[cj + 1]\n            bf[qi + 2] = p[cj + 2]\n            bf[qi + 3] = j < tl ? ap[j] : 255\n          }\n        }\n      }\n\n      if (depth == 2) {\n        for (var y = 0; y < h; y++) {\n          var s0 = y * bpl,\n            t0 = y * w\n          for (var i = 0; i < w; i++) {\n            var qi = (t0 + i) << 2,\n              j = (data[s0 + (i >> 2)] >> (6 - ((i & 3) << 1))) & 3,\n              cj = 3 * j\n            bf[qi] = p[cj]\n            bf[qi + 1] = p[cj + 1]\n            bf[qi + 2] = p[cj + 2]\n            bf[qi + 3] = j < tl ? ap[j] : 255\n          }\n        }\n      }\n\n      if (depth == 4) {\n        for (var y = 0; y < h; y++) {\n          var s0 = y * bpl,\n            t0 = y * w\n          for (var i = 0; i < w; i++) {\n            var qi = (t0 + i) << 2,\n              j = (data[s0 + (i >> 1)] >> (4 - ((i & 1) << 2))) & 15,\n              cj = 3 * j\n            bf[qi] = p[cj]\n            bf[qi + 1] = p[cj + 1]\n            bf[qi + 2] = p[cj + 2]\n            bf[qi + 3] = j < tl ? ap[j] : 255\n          }\n        }\n      }\n\n      if (depth == 8) {\n        for (var i = 0; i < area; i++) {\n          var qi = i << 2,\n            j = data[i],\n            cj = 3 * j\n          bf[qi] = p[cj]\n          bf[qi + 1] = p[cj + 1]\n          bf[qi + 2] = p[cj + 2]\n          bf[qi + 3] = j < tl ? ap[j] : 255\n        }\n      }\n    } else if (ctype == 4) {\n      // gray + alpha\n\n      if (depth == 8) {\n        for (var i = 0; i < area; i++) {\n          var qi = i << 2,\n            di = i << 1,\n            gr = data[di]\n          bf[qi] = gr\n          bf[qi + 1] = gr\n          bf[qi + 2] = gr\n          bf[qi + 3] = data[di + 1]\n        }\n      }\n\n      if (depth == 16) {\n        for (var i = 0; i < area; i++) {\n          var qi = i << 2,\n            di = i << 2,\n            gr = data[di]\n          bf[qi] = gr\n          bf[qi + 1] = gr\n          bf[qi + 2] = gr\n          bf[qi + 3] = data[di + 2]\n        }\n      }\n    } else if (ctype == 0) {\n      // gray\n\n      var tr = out.tabs['tRNS'] ? out.tabs['tRNS'] : -1\n      for (var y = 0; y < h; y++) {\n        var off = y * bpl,\n          to = y * w\n        if (depth == 1) {\n          for (var x = 0; x < w; x++) {\n            var gr = 255 * ((data[off + (x >>> 3)] >>> (7 - (x & 7))) & 1),\n              al = gr == tr * 255 ? 0 : 255\n            bf32[to + x] = (al << 24) | (gr << 16) | (gr << 8) | gr\n          }\n        } else if (depth == 2) {\n          for (var x = 0; x < w; x++) {\n            var gr = 85 * ((data[off + (x >>> 2)] >>> (6 - ((x & 3) << 1))) & 3),\n              al = gr == tr * 85 ? 0 : 255\n            bf32[to + x] = (al << 24) | (gr << 16) | (gr << 8) | gr\n          }\n        } else if (depth == 4) {\n          for (var x = 0; x < w; x++) {\n            var gr = 17 * ((data[off + (x >>> 1)] >>> (4 - ((x & 1) << 2))) & 15),\n              al = gr == tr * 17 ? 0 : 255\n            bf32[to + x] = (al << 24) | (gr << 16) | (gr << 8) | gr\n          }\n        } else if (depth == 8) {\n          for (var x = 0; x < w; x++) {\n            var gr = data[off + x],\n              al = gr == tr ? 0 : 255\n            bf32[to + x] = (al << 24) | (gr << 16) | (gr << 8) | gr\n          }\n        } else if (depth == 16) {\n          for (var x = 0; x < w; x++) {\n            var gr = data[off + (x << 1)],\n              al = rs(data, off + (x << 1)) == tr ? 0 : 255\n            bf32[to + x] = (al << 24) | (gr << 16) | (gr << 8) | gr\n          }\n        }\n      }\n    }\n\n    //console.log(Date.now()-time);\n    return bf\n  }\n\n  UPNG.decode = function (buff) {\n    var data = new Uint8Array(buff),\n      offset = 8,\n      bin = UPNG._bin,\n      rUs = bin.readUshort,\n      rUi = bin.readUint\n    var out = { tabs: {}, frames: [] }\n    var dd = new Uint8Array(data.length),\n      doff = 0 // put all IDAT data into it\n    var fd,\n      foff = 0 // frames\n    var text, keyw, bfr\n\n    var mgck = [0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a]\n    for (var i = 0; i < 8; i++) if (data[i] != mgck[i]) throw new Error('The input is not a PNG file!')\n\n    while (offset < data.length) {\n      var len = bin.readUint(data, offset)\n      offset += 4\n      var type = bin.readASCII(data, offset, 4)\n      offset += 4\n      //console.log(type,len);\n\n      if (type == 'IHDR') {\n        UPNG.decode._IHDR(data, offset, out)\n      } else if (type == 'CgBI') {\n        out.tabs[type] = data.slice(offset, offset + 4)\n      } else if (type == 'IDAT') {\n        for (var i = 0; i < len; i++) dd[doff + i] = data[offset + i]\n        doff += len\n      } else if (type == 'acTL') {\n        out.tabs[type] = { num_frames: rUi(data, offset), num_plays: rUi(data, offset + 4) }\n        fd = new Uint8Array(data.length)\n      } else if (type == 'fcTL') {\n        if (foff != 0) {\n          var fr = out.frames[out.frames.length - 1]\n          fr.data = UPNG.decode._decompress(out, fd.slice(0, foff), fr.rect.width, fr.rect.height)\n          foff = 0\n        }\n\n        var rct = {\n          x: rUi(data, offset + 12),\n          y: rUi(data, offset + 16),\n          width: rUi(data, offset + 4),\n          height: rUi(data, offset + 8),\n        }\n        var del = rUs(data, offset + 22)\n        del = rUs(data, offset + 20) / (del == 0 ? 100 : del)\n        var frm = { rect: rct, delay: Math.round(del * 1000), dispose: data[offset + 24], blend: data[offset + 25] }\n        //console.log(frm);\n        out.frames.push(frm)\n      } else if (type == 'fdAT') {\n        for (var i = 0; i < len - 4; i++) fd[foff + i] = data[offset + i + 4]\n        foff += len - 4\n      } else if (type == 'pHYs') {\n        out.tabs[type] = [bin.readUint(data, offset), bin.readUint(data, offset + 4), data[offset + 8]]\n      } else if (type == 'cHRM') {\n        out.tabs[type] = []\n        for (var i = 0; i < 8; i++) out.tabs[type].push(bin.readUint(data, offset + i * 4))\n      } else if (type == 'tEXt' || type == 'zTXt') {\n        if (out.tabs[type] == null) out.tabs[type] = {}\n        var nz = bin.nextZero(data, offset)\n        keyw = bin.readASCII(data, offset, nz - offset)\n        var tl = offset + len - nz - 1\n        if (type == 'tEXt') {\n          text = bin.readASCII(data, nz + 1, tl)\n        } else {\n          bfr = UPNG.decode._inflate(data.slice(nz + 2, nz + 2 + tl))\n          text = bin.readUTF8(bfr, 0, bfr.length)\n        }\n\n        out.tabs[type][keyw] = text\n      } else if (type == 'iTXt') {\n        if (out.tabs[type] == null) out.tabs[type] = {}\n        var nz = 0,\n          off = offset\n        nz = bin.nextZero(data, off)\n        keyw = bin.readASCII(data, off, nz - off)\n        off = nz + 1\n        var cflag = data[off]\n        off += 2\n        nz = bin.nextZero(data, off)\n        bin.readASCII(data, off, nz - off)\n        off = nz + 1\n        nz = bin.nextZero(data, off)\n        bin.readUTF8(data, off, nz - off)\n        off = nz + 1\n        var tl = len - (off - offset)\n        if (cflag == 0) {\n          text = bin.readUTF8(data, off, tl)\n        } else {\n          bfr = UPNG.decode._inflate(data.slice(off, off + tl))\n          text = bin.readUTF8(bfr, 0, bfr.length)\n        }\n\n        out.tabs[type][keyw] = text\n      } else if (type == 'PLTE') {\n        out.tabs[type] = bin.readBytes(data, offset, len)\n      } else if (type == 'hIST') {\n        var pl = out.tabs['PLTE'].length / 3\n        out.tabs[type] = []\n        for (var i = 0; i < pl; i++) out.tabs[type].push(rUs(data, offset + i * 2))\n      } else if (type == 'tRNS') {\n        if (out.ctype == 3) out.tabs[type] = bin.readBytes(data, offset, len)\n        else if (out.ctype == 0) out.tabs[type] = rUs(data, offset)\n        else if (out.ctype == 2) out.tabs[type] = [rUs(data, offset), rUs(data, offset + 2), rUs(data, offset + 4)]\n        //else console.log(\"tRNS for unsupported color type\",out.ctype, len);\n      } else if (type == 'gAMA') {\n        out.tabs[type] = bin.readUint(data, offset) / 100000\n      } else if (type == 'sRGB') {\n        out.tabs[type] = data[offset]\n      } else if (type == 'bKGD') {\n        if (out.ctype == 0 || out.ctype == 4) {\n          out.tabs[type] = [rUs(data, offset)]\n        } else if (out.ctype == 2 || out.ctype == 6) {\n          out.tabs[type] = [rUs(data, offset), rUs(data, offset + 2), rUs(data, offset + 4)]\n        } else if (out.ctype == 3) {\n          out.tabs[type] = data[offset]\n        }\n      } else if (type == 'IEND') {\n        break\n      }\n\n      //else {  console.log(\"unknown chunk type\", type, len);  out.tabs[type]=data.slice(offset,offset+len);  }\n      offset += len\n      bin.readUint(data, offset)\n      offset += 4\n    }\n\n    if (foff != 0) {\n      var fr = out.frames[out.frames.length - 1]\n      fr.data = UPNG.decode._decompress(out, fd.slice(0, foff), fr.rect.width, fr.rect.height)\n    }\n\n    out.data = UPNG.decode._decompress(out, dd, out.width, out.height)\n\n    delete out.compress\n    delete out.interlace\n    delete out.filter\n    return out\n  }\n\n  UPNG.decode._decompress = function (out, dd, w, h) {\n    var bpp = UPNG.decode._getBPP(out),\n      bpl = Math.ceil((w * bpp) / 8),\n      buff = new Uint8Array((bpl + 1 + out.interlace) * h)\n    if (out.tabs['CgBI']) dd = UPNG.inflateRaw(dd, buff)\n    else dd = UPNG.decode._inflate(dd, buff)\n\n    if (out.interlace == 0) dd = UPNG.decode._filterZero(dd, out, 0, w, h)\n    else if (out.interlace == 1) dd = UPNG.decode._readInterlace(dd, out)\n\n    return dd\n  }\n\n  UPNG.decode._inflate = function (data, buff) {\n    var out = UPNG['inflateRaw'](new Uint8Array(data.buffer, 2, data.length - 6), buff)\n    return out\n  }\n\n  UPNG.inflateRaw = (function () {\n    var H = {}\n    H.H = {}\n    H.H.N = function (N, W) {\n      var R = Uint8Array,\n        i = 0,\n        m = 0,\n        J = 0,\n        h = 0,\n        Q = 0,\n        X = 0,\n        u = 0,\n        w = 0,\n        d = 0,\n        v,\n        C\n      if (N[0] == 3 && N[1] == 0) return W ? W : new R(0)\n      var V = H.H,\n        n = V.b,\n        A = V.e,\n        l = V.R,\n        M = V.n,\n        I = V.A,\n        e = V.Z,\n        b = V.m,\n        Z = W == null\n      if (Z) W = new R((N.length >>> 2) << 5)\n      while (i == 0) {\n        i = n(N, d, 1)\n        m = n(N, d + 1, 2)\n        d += 3\n        if (m == 0) {\n          if ((d & 7) != 0) d += 8 - (d & 7)\n          var D = (d >>> 3) + 4,\n            q = N[D - 4] | (N[D - 3] << 8)\n          if (Z) W = H.H.W(W, w + q)\n          W.set(new R(N.buffer, N.byteOffset + D, q), w)\n          d = (D + q) << 3\n          w += q\n          continue\n        }\n\n        if (Z) W = H.H.W(W, w + (1 << 17))\n        if (m == 1) {\n          v = b.J\n          C = b.h\n          X = (1 << 9) - 1\n          u = (1 << 5) - 1\n        }\n\n        if (m == 2) {\n          J = A(N, d, 5) + 257\n          h = A(N, d + 5, 5) + 1\n          Q = A(N, d + 10, 4) + 4\n          d += 14\n          var j = 1\n          for (var c = 0; c < 38; c += 2) {\n            b.Q[c] = 0\n            b.Q[c + 1] = 0\n          }\n\n          for (var c = 0; c < Q; c++) {\n            var K = A(N, d + c * 3, 3)\n            b.Q[(b.X[c] << 1) + 1] = K\n            if (K > j) j = K\n          }\n\n          d += 3 * Q\n          M(b.Q, j)\n          I(b.Q, j, b.u)\n          v = b.w\n          C = b.d\n          d = l(b.u, (1 << j) - 1, J + h, N, d, b.v)\n          var r = V.V(b.v, 0, J, b.C)\n          X = (1 << r) - 1\n          var S = V.V(b.v, J, h, b.D)\n          u = (1 << S) - 1\n          M(b.C, r)\n          I(b.C, r, v)\n          M(b.D, S)\n          I(b.D, S, C)\n        }\n\n        while (!0) {\n          var T = v[e(N, d) & X]\n          d += T & 15\n          var p = T >>> 4\n          if (p >>> 8 == 0) {\n            W[w++] = p\n          } else if (p == 256) {\n            break\n          } else {\n            var z = w + p - 254\n            if (p > 264) {\n              var _ = b.q[p - 257]\n              z = w + (_ >>> 3) + A(N, d, _ & 7)\n              d += _ & 7\n            }\n\n            var $ = C[e(N, d) & u]\n            d += $ & 15\n            var s = $ >>> 4,\n              Y = b.c[s],\n              a = (Y >>> 4) + n(N, d, Y & 15)\n            d += Y & 15\n            while (w < z) {\n              W[w] = W[w++ - a]\n              W[w] = W[w++ - a]\n              W[w] = W[w++ - a]\n              W[w] = W[w++ - a]\n            }\n\n            w = z\n          }\n        }\n      }\n\n      return W.length == w ? W : W.slice(0, w)\n    }\n\n    H.H.W = function (N, W) {\n      var R = N.length\n      if (W <= R) return N\n      var V = new Uint8Array(R << 1)\n      V.set(N, 0)\n      return V\n    }\n\n    H.H.R = function (N, W, R, V, n, A) {\n      var l = H.H.e,\n        M = H.H.Z,\n        I = 0\n      while (I < R) {\n        var e = N[M(V, n) & W]\n        n += e & 15\n        var b = e >>> 4\n        if (b <= 15) {\n          A[I] = b\n          I++\n        } else {\n          var Z = 0,\n            m = 0\n          if (b == 16) {\n            m = 3 + l(V, n, 2)\n            n += 2\n            Z = A[I - 1]\n          } else if (b == 17) {\n            m = 3 + l(V, n, 3)\n            n += 3\n          } else if (b == 18) {\n            m = 11 + l(V, n, 7)\n            n += 7\n          }\n\n          var J = I + m\n          while (I < J) {\n            A[I] = Z\n            I++\n          }\n        }\n      }\n\n      return n\n    }\n\n    H.H.V = function (N, W, R, V) {\n      var n = 0,\n        A = 0,\n        l = V.length >>> 1\n      while (A < R) {\n        var M = N[A + W]\n        V[A << 1] = 0\n        V[(A << 1) + 1] = M\n        if (M > n) n = M\n        A++\n      }\n\n      while (A < l) {\n        V[A << 1] = 0\n        V[(A << 1) + 1] = 0\n        A++\n      }\n\n      return n\n    }\n\n    H.H.n = function (N, W) {\n      var R = H.H.m,\n        V = N.length,\n        n,\n        A,\n        l,\n        M,\n        I,\n        e = R.j\n      for (var M = 0; M <= W; M++) e[M] = 0\n      for (M = 1; M < V; M += 2) e[N[M]]++\n      var b = R.K\n      n = 0\n      e[0] = 0\n      for (A = 1; A <= W; A++) {\n        n = (n + e[A - 1]) << 1\n        b[A] = n\n      }\n\n      for (l = 0; l < V; l += 2) {\n        I = N[l + 1]\n        if (I != 0) {\n          N[l] = b[I]\n          b[I]++\n        }\n      }\n    }\n\n    H.H.A = function (N, W, R) {\n      var V = N.length,\n        n = H.H.m,\n        A = n.r\n      for (var l = 0; l < V; l += 2) {\n        if (N[l + 1] != 0) {\n          var M = l >> 1,\n            I = N[l + 1],\n            e = (M << 4) | I,\n            b = W - I,\n            Z = N[l] << b,\n            m = Z + (1 << b)\n          while (Z != m) {\n            var J = A[Z] >>> (15 - W)\n            R[J] = e\n            Z++\n          }\n        }\n      }\n    }\n\n    H.H.l = function (N, W) {\n      var R = H.H.m.r,\n        V = 15 - W\n      for (var n = 0; n < N.length; n += 2) {\n        var A = N[n] << (W - N[n + 1])\n        N[n] = R[A] >>> V\n      }\n    }\n\n    H.H.M = function (N, W, R) {\n      R = R << (W & 7)\n      var V = W >>> 3\n      N[V] |= R\n      N[V + 1] |= R >>> 8\n    }\n\n    H.H.I = function (N, W, R) {\n      R = R << (W & 7)\n      var V = W >>> 3\n      N[V] |= R\n      N[V + 1] |= R >>> 8\n      N[V + 2] |= R >>> 16\n    }\n\n    H.H.e = function (N, W, R) {\n      return ((N[W >>> 3] | (N[(W >>> 3) + 1] << 8)) >>> (W & 7)) & ((1 << R) - 1)\n    }\n\n    H.H.b = function (N, W, R) {\n      return ((N[W >>> 3] | (N[(W >>> 3) + 1] << 8) | (N[(W >>> 3) + 2] << 16)) >>> (W & 7)) & ((1 << R) - 1)\n    }\n\n    H.H.Z = function (N, W) {\n      return (N[W >>> 3] | (N[(W >>> 3) + 1] << 8) | (N[(W >>> 3) + 2] << 16)) >>> (W & 7)\n    }\n\n    H.H.i = function (N, W) {\n      return (N[W >>> 3] | (N[(W >>> 3) + 1] << 8) | (N[(W >>> 3) + 2] << 16) | (N[(W >>> 3) + 3] << 24)) >>> (W & 7)\n    }\n\n    H.H.m = (function () {\n      var N = Uint16Array,\n        W = Uint32Array\n      return {\n        K: new N(16),\n        j: new N(16),\n        X: [16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15],\n        S: [\n          3,\n          4,\n          5,\n          6,\n          7,\n          8,\n          9,\n          10,\n          11,\n          13,\n          15,\n          17,\n          19,\n          23,\n          27,\n          31,\n          35,\n          43,\n          51,\n          59,\n          67,\n          83,\n          99,\n          115,\n          131,\n          163,\n          195,\n          227,\n          258,\n          999,\n          999,\n          999,\n        ],\n        T: [0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0, 0, 0, 0],\n        q: new N(32),\n        p: [\n          1,\n          2,\n          3,\n          4,\n          5,\n          7,\n          9,\n          13,\n          17,\n          25,\n          33,\n          49,\n          65,\n          97,\n          129,\n          193,\n          257,\n          385,\n          513,\n          769,\n          1025,\n          1537,\n          2049,\n          3073,\n          4097,\n          6145,\n          8193,\n          12289,\n          16385,\n          24577,\n          65535,\n          65535,\n        ],\n        z: [0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 0, 0],\n        c: new W(32),\n        J: new N(512),\n        _: [],\n        h: new N(32),\n        $: [],\n        w: new N(32768),\n        C: [],\n        v: [],\n        d: new N(32768),\n        D: [],\n        u: new N(512),\n        Q: [],\n        r: new N(1 << 15),\n        s: new W(286),\n        Y: new W(30),\n        a: new W(19),\n        t: new W(15e3),\n        k: new N(1 << 16),\n        g: new N(1 << 15),\n      }\n    })()\n    ;(function () {\n      var N = H.H.m,\n        W = 1 << 15\n      for (var R = 0; R < W; R++) {\n        var V = R\n        V = ((V & 2863311530) >>> 1) | ((V & 1431655765) << 1)\n        V = ((V & 3435973836) >>> 2) | ((V & 858993459) << 2)\n        V = ((V & 4042322160) >>> 4) | ((V & 252645135) << 4)\n        V = ((V & 4278255360) >>> 8) | ((V & 16711935) << 8)\n        N.r[R] = ((V >>> 16) | (V << 16)) >>> 17\n      }\n\n      function n(A, l, M) {\n        while (l-- != 0) A.push(0, M)\n      }\n\n      for (var R = 0; R < 32; R++) {\n        N.q[R] = (N.S[R] << 3) | N.T[R]\n        N.c[R] = (N.p[R] << 4) | N.z[R]\n      }\n\n      n(N._, 144, 8)\n      n(N._, 255 - 143, 9)\n      n(N._, 279 - 255, 7)\n      n(N._, 287 - 279, 8)\n      H.H.n(N._, 9)\n      H.H.A(N._, 9, N.J)\n      H.H.l(N._, 9)\n      n(N.$, 32, 5)\n      H.H.n(N.$, 5)\n      H.H.A(N.$, 5, N.h)\n      H.H.l(N.$, 5)\n      n(N.Q, 19, 0)\n      n(N.C, 286, 0)\n      n(N.D, 30, 0)\n      n(N.v, 320, 0)\n    })()\n\n    return H.H.N\n  })()\n\n  UPNG.decode._readInterlace = function (data, out) {\n    var w = out.width,\n      h = out.height\n    var bpp = UPNG.decode._getBPP(out),\n      cbpp = bpp >> 3,\n      bpl = Math.ceil((w * bpp) / 8)\n    var img = new Uint8Array(h * bpl)\n    var di = 0\n\n    var starting_row = [0, 0, 4, 0, 2, 0, 1]\n    var starting_col = [0, 4, 0, 2, 0, 1, 0]\n    var row_increment = [8, 8, 8, 4, 4, 2, 2]\n    var col_increment = [8, 8, 4, 4, 2, 2, 1]\n\n    var pass = 0\n    while (pass < 7) {\n      var ri = row_increment[pass],\n        ci = col_increment[pass]\n      var sw = 0,\n        sh = 0\n      var cr = starting_row[pass]\n      while (cr < h) {\n        cr += ri\n        sh++\n      }\n\n      var cc = starting_col[pass]\n      while (cc < w) {\n        cc += ci\n        sw++\n      }\n\n      var bpll = Math.ceil((sw * bpp) / 8)\n      UPNG.decode._filterZero(data, out, di, sw, sh)\n\n      var y = 0,\n        row = starting_row[pass]\n      var val\n\n      while (row < h) {\n        var col = starting_col[pass]\n        var cdi = (di + y * bpll) << 3\n\n        while (col < w) {\n          if (bpp == 1) {\n            val = data[cdi >> 3]\n            val = (val >> (7 - (cdi & 7))) & 1\n            img[row * bpl + (col >> 3)] |= val << (7 - ((col & 7) << 0))\n          }\n\n          if (bpp == 2) {\n            val = data[cdi >> 3]\n            val = (val >> (6 - (cdi & 7))) & 3\n            img[row * bpl + (col >> 2)] |= val << (6 - ((col & 3) << 1))\n          }\n\n          if (bpp == 4) {\n            val = data[cdi >> 3]\n            val = (val >> (4 - (cdi & 7))) & 15\n            img[row * bpl + (col >> 1)] |= val << (4 - ((col & 1) << 2))\n          }\n\n          if (bpp >= 8) {\n            var ii = row * bpl + col * cbpp\n            for (var j = 0; j < cbpp; j++) img[ii + j] = data[(cdi >> 3) + j]\n          }\n\n          cdi += bpp\n          col += ci\n        }\n\n        y++\n        row += ri\n      }\n\n      if (sw * sh != 0) di += sh * (1 + bpll)\n      pass = pass + 1\n    }\n\n    return img\n  }\n\n  UPNG.decode._getBPP = function (out) {\n    var noc = [1, null, 3, 1, 2, null, 4][out.ctype]\n    return noc * out.depth\n  }\n\n  UPNG.decode._filterZero = function (data, out, off, w, h) {\n    var bpp = UPNG.decode._getBPP(out),\n      bpl = Math.ceil((w * bpp) / 8),\n      paeth = UPNG.decode._paeth\n    bpp = Math.ceil(bpp / 8)\n\n    var i,\n      di,\n      type = data[off],\n      x = 0\n\n    if (type > 1) data[off] = [0, 0, 1][type - 2]\n    if (type == 3) for (x = bpp; x < bpl; x++) data[x + 1] = (data[x + 1] + (data[x + 1 - bpp] >>> 1)) & 255\n\n    for (var y = 0; y < h; y++) {\n      i = off + y * bpl\n      di = i + y + 1\n      type = data[di - 1]\n      x = 0\n\n      if (type == 0) {\n        for (; x < bpl; x++) data[i + x] = data[di + x]\n      } else if (type == 1) {\n        for (; x < bpp; x++) data[i + x] = data[di + x]\n        for (; x < bpl; x++) data[i + x] = data[di + x] + data[i + x - bpp]\n      } else if (type == 2) {\n        for (; x < bpl; x++) data[i + x] = data[di + x] + data[i + x - bpl]\n      } else if (type == 3) {\n        for (; x < bpp; x++) data[i + x] = data[di + x] + (data[i + x - bpl] >>> 1)\n        for (; x < bpl; x++) data[i + x] = data[di + x] + ((data[i + x - bpl] + data[i + x - bpp]) >>> 1)\n      } else {\n        for (; x < bpp; x++) data[i + x] = data[di + x] + paeth(0, data[i + x - bpl], 0)\n        for (; x < bpl; x++) {\n          data[i + x] = data[di + x] + paeth(data[i + x - bpp], data[i + x - bpl], data[i + x - bpp - bpl])\n        }\n      }\n    }\n\n    return data\n  }\n\n  UPNG.decode._paeth = function (a, b, c) {\n    var p = a + b - c,\n      pa = p - a,\n      pb = p - b,\n      pc = p - c\n    if (pa * pa <= pb * pb && pa * pa <= pc * pc) return a\n    else if (pb * pb <= pc * pc) return b\n    return c\n  }\n\n  UPNG.decode._IHDR = function (data, offset, out) {\n    var bin = UPNG._bin\n    out.width = bin.readUint(data, offset)\n    offset += 4\n    out.height = bin.readUint(data, offset)\n    offset += 4\n    out.depth = data[offset]\n    offset++\n    out.ctype = data[offset]\n    offset++\n    out.compress = data[offset]\n    offset++\n    out.filter = data[offset]\n    offset++\n    out.interlace = data[offset]\n    offset++\n  }\n\n  UPNG._bin = {\n    nextZero: function (data, p) {\n      while (data[p] != 0) p++\n      return p\n    },\n    readUshort: function (buff, p) {\n      return (buff[p] << 8) | buff[p + 1]\n    },\n    writeUshort: function (buff, p, n) {\n      buff[p] = (n >> 8) & 255\n      buff[p + 1] = n & 255\n    },\n    readUint: function (buff, p) {\n      return buff[p] * (256 * 256 * 256) + ((buff[p + 1] << 16) | (buff[p + 2] << 8) | buff[p + 3])\n    },\n    writeUint: function (buff, p, n) {\n      buff[p] = (n >> 24) & 255\n      buff[p + 1] = (n >> 16) & 255\n      buff[p + 2] = (n >> 8) & 255\n      buff[p + 3] = n & 255\n    },\n    readASCII: function (buff, p, l) {\n      var s = ''\n      for (var i = 0; i < l; i++) s += String.fromCharCode(buff[p + i])\n      return s\n    },\n    writeASCII: function (data, p, s) {\n      for (var i = 0; i < s.length; i++) data[p + i] = s.charCodeAt(i)\n    },\n    readBytes: function (buff, p, l) {\n      var arr = []\n      for (var i = 0; i < l; i++) arr.push(buff[p + i])\n      return arr\n    },\n    pad: function (n) {\n      return n.length < 2 ? '0' + n : n\n    },\n    readUTF8: function (buff, p, l) {\n      var s = '',\n        ns\n      for (var i = 0; i < l; i++) s += '%' + UPNG._bin.pad(buff[p + i].toString(16))\n      try {\n        ns = decodeURIComponent(s)\n      } catch (e) {\n        return UPNG._bin.readASCII(buff, p, l)\n      }\n\n      return ns\n    },\n  }\n  UPNG._copyTile = function (sb, sw, sh, tb, tw, th, xoff, yoff, mode) {\n    var w = Math.min(sw, tw),\n      h = Math.min(sh, th)\n    var si = 0,\n      ti = 0\n    for (var y = 0; y < h; y++) {\n      for (var x = 0; x < w; x++) {\n        if (xoff >= 0 && yoff >= 0) {\n          si = (y * sw + x) << 2\n          ti = ((yoff + y) * tw + xoff + x) << 2\n        } else {\n          si = ((-yoff + y) * sw - xoff + x) << 2\n          ti = (y * tw + x) << 2\n        }\n\n        if (mode == 0) {\n          tb[ti] = sb[si]\n          tb[ti + 1] = sb[si + 1]\n          tb[ti + 2] = sb[si + 2]\n          tb[ti + 3] = sb[si + 3]\n        } else if (mode == 1) {\n          var fa = sb[si + 3] * (1 / 255),\n            fr = sb[si] * fa,\n            fg = sb[si + 1] * fa,\n            fb = sb[si + 2] * fa\n          var ba = tb[ti + 3] * (1 / 255),\n            br = tb[ti] * ba,\n            bg = tb[ti + 1] * ba,\n            bb = tb[ti + 2] * ba\n\n          var ifa = 1 - fa,\n            oa = fa + ba * ifa,\n            ioa = oa == 0 ? 0 : 1 / oa\n          tb[ti + 3] = 255 * oa\n          tb[ti + 0] = (fr + br * ifa) * ioa\n          tb[ti + 1] = (fg + bg * ifa) * ioa\n          tb[ti + 2] = (fb + bb * ifa) * ioa\n        } else if (mode == 2) {\n          // copy only differences, otherwise zero\n\n          var fa = sb[si + 3],\n            fr = sb[si],\n            fg = sb[si + 1],\n            fb = sb[si + 2]\n          var ba = tb[ti + 3],\n            br = tb[ti],\n            bg = tb[ti + 1],\n            bb = tb[ti + 2]\n          if (fa == ba && fr == br && fg == bg && fb == bb) {\n            tb[ti] = 0\n            tb[ti + 1] = 0\n            tb[ti + 2] = 0\n            tb[ti + 3] = 0\n          } else {\n            tb[ti] = fr\n            tb[ti + 1] = fg\n            tb[ti + 2] = fb\n            tb[ti + 3] = fa\n          }\n        } else if (mode == 3) {\n          // check if can be blended\n\n          var fa = sb[si + 3],\n            fr = sb[si],\n            fg = sb[si + 1],\n            fb = sb[si + 2]\n          var ba = tb[ti + 3],\n            br = tb[ti],\n            bg = tb[ti + 1],\n            bb = tb[ti + 2]\n          if (fa == ba && fr == br && fg == bg && fb == bb) continue\n          //if(fa!=255 && ba!=0) return false;\n          if (fa < 220 && ba > 20) return false\n        }\n      }\n    }\n\n    return true\n  }\n}\n\nclass RGBMLoader extends DataTextureLoader {\n  constructor(manager) {\n    super(manager)\n\n    this.type = HalfFloatType\n    this.maxRange = 7 // more information about this property at https://iwasbeingirony.blogspot.com/2010/06/difference-between-rgbm-and-rgbd.html\n  }\n\n  setDataType(value) {\n    this.type = value\n    return this\n  }\n\n  setMaxRange(value) {\n    this.maxRange = value\n    return this\n  }\n\n  loadCubemap(urls, onLoad, onProgress, onError) {\n    const texture = new CubeTexture()\n\n    let loaded = 0\n\n    const scope = this\n\n    function loadTexture(i) {\n      scope.load(\n        urls[i],\n        function (image) {\n          texture.images[i] = image\n\n          loaded++\n\n          if (loaded === 6) {\n            texture.needsUpdate = true\n\n            if (onLoad) onLoad(texture)\n          }\n        },\n        undefined,\n        onError,\n      )\n    }\n\n    for (let i = 0; i < urls.length; ++i) {\n      loadTexture(i)\n    }\n\n    texture.type = this.type\n    texture.format = RGBAFormat\n    texture.minFilter = LinearFilter\n    texture.generateMipmaps = false\n\n    return texture\n  }\n\n  parse(buffer) {\n    init()\n    const img = UPNG.decode(buffer)\n    const rgba = UPNG.toRGBA8(img)[0]\n\n    const data = new Uint8Array(rgba)\n    const size = img.width * img.height * 4\n\n    const output = this.type === HalfFloatType ? new Uint16Array(size) : new Float32Array(size)\n\n    // decode RGBM\n\n    for (let i = 0; i < data.length; i += 4) {\n      const r = data[i + 0] / 255\n      const g = data[i + 1] / 255\n      const b = data[i + 2] / 255\n      const a = data[i + 3] / 255\n\n      if (this.type === HalfFloatType) {\n        output[i + 0] = DataUtils.toHalfFloat(Math.min(r * a * this.maxRange, 65504))\n        output[i + 1] = DataUtils.toHalfFloat(Math.min(g * a * this.maxRange, 65504))\n        output[i + 2] = DataUtils.toHalfFloat(Math.min(b * a * this.maxRange, 65504))\n        output[i + 3] = DataUtils.toHalfFloat(1)\n      } else {\n        output[i + 0] = r * a * this.maxRange\n        output[i + 1] = g * a * this.maxRange\n        output[i + 2] = b * a * this.maxRange\n        output[i + 3] = 1\n      }\n    }\n\n    return {\n      width: img.width,\n      height: img.height,\n      data: output,\n      format: RGBAFormat,\n      type: this.type,\n      flipY: true,\n    }\n  }\n}\n\nexport { RGBMLoader }\n"], "names": ["DataTextureLoader", "HalfFloatType", "CubeTexture", "RGBAFormat", "LinearFilter", "DataUtils"], "mappings": ";;;AAEA,IAAI;AAEJ,SAAS,OAAO;AACd,MAAI;AAAM,WAAO;AAGjB,SAAO,CAAE;AAET,OAAK,UAAU,SAAU,KAAK;AAC5B,QAAI,IAAI,IAAI,OACV,IAAI,IAAI;AACV,QAAI,IAAI,KAAK,QAAQ;AAAM,aAAO,CAAC,KAAK,QAAQ,YAAY,IAAI,MAAM,GAAG,GAAG,GAAG,EAAE,MAAM;AAEvF,QAAI,OAAO,CAAE;AACb,QAAI,IAAI,OAAO,CAAC,EAAE,QAAQ;AAAM,UAAI,OAAO,CAAC,EAAE,OAAO,IAAI;AAEzD,QAAI,MAAM,IAAI,IAAI,GAChB,MAAM,IAAI,WAAW,GAAG,GACxB,QAAQ,IAAI,WAAW,GAAG,GAC1B,OAAO,IAAI,WAAW,GAAG;AAC3B,aAAS,IAAI,GAAG,IAAI,IAAI,OAAO,QAAQ,KAAK;AAC1C,UAAI,MAAM,IAAI,OAAO,CAAC;AACtB,UAAI,KAAK,IAAI,KAAK,GAChB,KAAK,IAAI,KAAK,GACd,KAAK,IAAI,KAAK,OACd,KAAK,IAAI,KAAK;AAChB,UAAI,QAAQ,KAAK,QAAQ,YAAY,IAAI,MAAM,IAAI,IAAI,GAAG;AAE1D,UAAI,KAAK;AAAG,iBAAS,IAAI,GAAG,IAAI,KAAK;AAAK,eAAK,CAAC,IAAI,IAAI,CAAC;AAEzD,UAAI,IAAI,SAAS;AAAG,aAAK,UAAU,OAAO,IAAI,IAAI,KAAK,GAAG,GAAG,IAAI,IAAI,CAAC;AAAA,eAC7D,IAAI,SAAS;AAAG,aAAK,UAAU,OAAO,IAAI,IAAI,KAAK,GAAG,GAAG,IAAI,IAAI,CAAC;AAE3E,WAAK,KAAK,IAAI,OAAO,MAAM,CAAC,CAAC;AAE7B,UAAI,IAAI,WAAW;AAAG,aAAK,UAAU,OAAO,IAAI,IAAI,KAAK,GAAG,GAAG,IAAI,IAAI,CAAC;AAAA,eAC/D,IAAI,WAAW;AAAG,iBAAS,IAAI,GAAG,IAAI,KAAK;AAAK,cAAI,CAAC,IAAI,KAAK,CAAC;AAAA,IACzE;AAED,WAAO;AAAA,EACR;AAED,OAAK,QAAQ,cAAc,SAAU,MAAM,GAAG,GAAG,KAAK;AACpD,QAAI,OAAO,IAAI,GACb,MAAM,KAAK,OAAO,QAAQ,GAAG;AAC/B,QAAI,MAAM,KAAK,KAAM,IAAI,MAAO,CAAC;AAEjC,QAAI,KAAK,IAAI,WAAW,OAAO,CAAC,GAC9B,OAAO,IAAI,YAAY,GAAG,MAAM;AAClC,QAAI,QAAQ,IAAI,OACd,QAAQ,IAAI;AACd,QAAI,KAAK,KAAK,KAAK;AAEnB,QAAI,SAAS,GAAG;AAGd,UAAI,QAAQ,QAAQ;AACpB,UAAI,SAAS,GAAG;AACd,iBAAS,IAAI,GAAG,IAAI,OAAO,KAAK,GAAG;AACjC,aAAG,CAAC,IAAI,KAAK,CAAC;AACd,aAAG,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;AACtB,aAAG,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;AACtB,aAAG,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;AAAA,QACvB;AAAA,MACF;AAED,UAAI,SAAS,IAAI;AACf,iBAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,aAAG,CAAC,IAAI,KAAK,KAAK,CAAC;AAAA,QACpB;AAAA,MACF;AAAA,IACP,WAAe,SAAS,GAAG;AAGrB,UAAI,KAAK,IAAI,KAAK,MAAM;AACxB,UAAI,MAAM,MAAM;AACd,YAAI,SAAS,GAAG;AACd,mBAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,gBAAI,KAAK,IAAI;AACb,iBAAK,CAAC,IAAK,OAAO,KAAO,KAAK,KAAK,CAAC,KAAK,KAAO,KAAK,KAAK,CAAC,KAAK,IAAK,KAAK,EAAE;AAAA,UAC7E;AAAA,QACF;AAED,YAAI,SAAS,IAAI;AACf,mBAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,gBAAI,KAAK,IAAI;AACb,iBAAK,CAAC,IAAK,OAAO,KAAO,KAAK,KAAK,CAAC,KAAK,KAAO,KAAK,KAAK,CAAC,KAAK,IAAK,KAAK,EAAE;AAAA,UAC7E;AAAA,QACF;AAAA,MACT,OAAa;AACL,YAAI,KAAK,GAAG,CAAC,GACX,KAAK,GAAG,CAAC,GACT,KAAK,GAAG,CAAC;AACX,YAAI,SAAS,GAAG;AACd,mBAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,gBAAI,KAAK,KAAK,GACZ,KAAK,IAAI;AACX,iBAAK,CAAC,IAAK,OAAO,KAAO,KAAK,KAAK,CAAC,KAAK,KAAO,KAAK,KAAK,CAAC,KAAK,IAAK,KAAK,EAAE;AAC5E,gBAAI,KAAK,EAAE,KAAK,MAAM,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,CAAC,KAAK;AAAI,iBAAG,KAAK,CAAC,IAAI;AAAA,UAC9E;AAAA,QACF;AAED,YAAI,SAAS,IAAI;AACf,mBAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,gBAAI,KAAK,KAAK,GACZ,KAAK,IAAI;AACX,iBAAK,CAAC,IAAK,OAAO,KAAO,KAAK,KAAK,CAAC,KAAK,KAAO,KAAK,KAAK,CAAC,KAAK,IAAK,KAAK,EAAE;AAC5E,gBAAI,GAAG,MAAM,EAAE,KAAK,MAAM,GAAG,MAAM,KAAK,CAAC,KAAK,MAAM,GAAG,MAAM,KAAK,CAAC,KAAK;AAAI,iBAAG,KAAK,CAAC,IAAI;AAAA,UAC1F;AAAA,QACF;AAAA,MACF;AAAA,IACP,WAAe,SAAS,GAAG;AAGrB,UAAI,IAAI,IAAI,KAAK,MAAM,GACrB,KAAK,IAAI,KAAK,MAAM,GACpB,KAAK,KAAK,GAAG,SAAS;AAExB,UAAI,SAAS,GAAG;AACd,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,cAAI,KAAK,IAAI,KACX,KAAK,IAAI;AACX,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,gBAAI,KAAM,KAAK,KAAM,GACnB,IAAK,KAAK,MAAM,KAAK,EAAE,KAAM,MAAM,IAAI,MAAM,KAAO,GACpD,KAAK,IAAI;AACX,eAAG,EAAE,IAAI,EAAE,EAAE;AACb,eAAG,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC;AACrB,eAAG,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC;AACrB,eAAG,KAAK,CAAC,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI;AAAA,UAC/B;AAAA,QACF;AAAA,MACF;AAED,UAAI,SAAS,GAAG;AACd,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,cAAI,KAAK,IAAI,KACX,KAAK,IAAI;AACX,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,gBAAI,KAAM,KAAK,KAAM,GACnB,IAAK,KAAK,MAAM,KAAK,EAAE,KAAM,MAAM,IAAI,MAAM,KAAO,GACpD,KAAK,IAAI;AACX,eAAG,EAAE,IAAI,EAAE,EAAE;AACb,eAAG,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC;AACrB,eAAG,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC;AACrB,eAAG,KAAK,CAAC,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI;AAAA,UAC/B;AAAA,QACF;AAAA,MACF;AAED,UAAI,SAAS,GAAG;AACd,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,cAAI,KAAK,IAAI,KACX,KAAK,IAAI;AACX,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,gBAAI,KAAM,KAAK,KAAM,GACnB,IAAK,KAAK,MAAM,KAAK,EAAE,KAAM,MAAM,IAAI,MAAM,KAAO,IACpD,KAAK,IAAI;AACX,eAAG,EAAE,IAAI,EAAE,EAAE;AACb,eAAG,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC;AACrB,eAAG,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC;AACrB,eAAG,KAAK,CAAC,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI;AAAA,UAC/B;AAAA,QACF;AAAA,MACF;AAED,UAAI,SAAS,GAAG;AACd,iBAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,cAAI,KAAK,KAAK,GACZ,IAAI,KAAK,CAAC,GACV,KAAK,IAAI;AACX,aAAG,EAAE,IAAI,EAAE,EAAE;AACb,aAAG,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC;AACrB,aAAG,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC;AACrB,aAAG,KAAK,CAAC,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI;AAAA,QAC/B;AAAA,MACF;AAAA,IACP,WAAe,SAAS,GAAG;AAGrB,UAAI,SAAS,GAAG;AACd,iBAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,cAAI,KAAK,KAAK,GACZ,KAAK,KAAK,GACV,KAAK,KAAK,EAAE;AACd,aAAG,EAAE,IAAI;AACT,aAAG,KAAK,CAAC,IAAI;AACb,aAAG,KAAK,CAAC,IAAI;AACb,aAAG,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC;AAAA,QACzB;AAAA,MACF;AAED,UAAI,SAAS,IAAI;AACf,iBAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,cAAI,KAAK,KAAK,GACZ,KAAK,KAAK,GACV,KAAK,KAAK,EAAE;AACd,aAAG,EAAE,IAAI;AACT,aAAG,KAAK,CAAC,IAAI;AACb,aAAG,KAAK,CAAC,IAAI;AACb,aAAG,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC;AAAA,QACzB;AAAA,MACF;AAAA,IACP,WAAe,SAAS,GAAG;AAGrB,UAAI,KAAK,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,MAAM,IAAI;AAC/C,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAI,MAAM,IAAI,KACZ,KAAK,IAAI;AACX,YAAI,SAAS,GAAG;AACd,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,gBAAI,KAAK,OAAQ,KAAK,OAAO,MAAM,EAAE,MAAO,KAAK,IAAI,KAAO,IAC1D,KAAK,MAAM,KAAK,MAAM,IAAI;AAC5B,iBAAK,KAAK,CAAC,IAAK,MAAM,KAAO,MAAM,KAAO,MAAM,IAAK;AAAA,UACtD;AAAA,QACX,WAAmB,SAAS,GAAG;AACrB,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,gBAAI,KAAK,MAAO,KAAK,OAAO,MAAM,EAAE,MAAO,MAAM,IAAI,MAAM,KAAO,IAChE,KAAK,MAAM,KAAK,KAAK,IAAI;AAC3B,iBAAK,KAAK,CAAC,IAAK,MAAM,KAAO,MAAM,KAAO,MAAM,IAAK;AAAA,UACtD;AAAA,QACX,WAAmB,SAAS,GAAG;AACrB,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,gBAAI,KAAK,MAAO,KAAK,OAAO,MAAM,EAAE,MAAO,MAAM,IAAI,MAAM,KAAO,KAChE,KAAK,MAAM,KAAK,KAAK,IAAI;AAC3B,iBAAK,KAAK,CAAC,IAAK,MAAM,KAAO,MAAM,KAAO,MAAM,IAAK;AAAA,UACtD;AAAA,QACX,WAAmB,SAAS,GAAG;AACrB,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,gBAAI,KAAK,KAAK,MAAM,CAAC,GACnB,KAAK,MAAM,KAAK,IAAI;AACtB,iBAAK,KAAK,CAAC,IAAK,MAAM,KAAO,MAAM,KAAO,MAAM,IAAK;AAAA,UACtD;AAAA,QACX,WAAmB,SAAS,IAAI;AACtB,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,gBAAI,KAAK,KAAK,OAAO,KAAK,EAAE,GAC1B,KAAK,GAAG,MAAM,OAAO,KAAK,EAAE,KAAK,KAAK,IAAI;AAC5C,iBAAK,KAAK,CAAC,IAAK,MAAM,KAAO,MAAM,KAAO,MAAM,IAAK;AAAA,UACtD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAGD,WAAO;AAAA,EACR;AAED,OAAK,SAAS,SAAU,MAAM;AAC5B,QAAI,OAAO,IAAI,WAAW,IAAI,GAC5B,SAAS,GACT,MAAM,KAAK,MACX,MAAM,IAAI,YACV,MAAM,IAAI;AACZ,QAAI,MAAM,EAAE,MAAM,CAAA,GAAI,QAAQ,CAAA,EAAI;AAClC,QAAI,KAAK,IAAI,WAAW,KAAK,MAAM,GACjC,OAAO;AACT,QAAI,IACF,OAAO;AACT,QAAI,MAAM,MAAM;AAEhB,QAAI,OAAO,CAAC,KAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,EAAI;AAC1D,aAAS,IAAI,GAAG,IAAI,GAAG;AAAK,UAAI,KAAK,CAAC,KAAK,KAAK,CAAC;AAAG,cAAM,IAAI,MAAM,8BAA8B;AAElG,WAAO,SAAS,KAAK,QAAQ;AAC3B,UAAI,MAAM,IAAI,SAAS,MAAM,MAAM;AACnC,gBAAU;AACV,UAAI,OAAO,IAAI,UAAU,MAAM,QAAQ,CAAC;AACxC,gBAAU;AAGV,UAAI,QAAQ,QAAQ;AAClB,aAAK,OAAO,MAAM,MAAM,QAAQ,GAAG;AAAA,MAC3C,WAAiB,QAAQ,QAAQ;AACzB,YAAI,KAAK,IAAI,IAAI,KAAK,MAAM,QAAQ,SAAS,CAAC;AAAA,MACtD,WAAiB,QAAQ,QAAQ;AACzB,iBAAS,IAAI,GAAG,IAAI,KAAK;AAAK,aAAG,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC;AAC5D,gBAAQ;AAAA,MAChB,WAAiB,QAAQ,QAAQ;AACzB,YAAI,KAAK,IAAI,IAAI,EAAE,YAAY,IAAI,MAAM,MAAM,GAAG,WAAW,IAAI,MAAM,SAAS,CAAC,EAAG;AACpF,aAAK,IAAI,WAAW,KAAK,MAAM;AAAA,MACvC,WAAiB,QAAQ,QAAQ;AACzB,YAAI,QAAQ,GAAG;AACb,cAAI,KAAK,IAAI,OAAO,IAAI,OAAO,SAAS,CAAC;AACzC,aAAG,OAAO,KAAK,OAAO,YAAY,KAAK,GAAG,MAAM,GAAG,IAAI,GAAG,GAAG,KAAK,OAAO,GAAG,KAAK,MAAM;AACvF,iBAAO;AAAA,QACR;AAED,YAAI,MAAM;AAAA,UACR,GAAG,IAAI,MAAM,SAAS,EAAE;AAAA,UACxB,GAAG,IAAI,MAAM,SAAS,EAAE;AAAA,UACxB,OAAO,IAAI,MAAM,SAAS,CAAC;AAAA,UAC3B,QAAQ,IAAI,MAAM,SAAS,CAAC;AAAA,QAC7B;AACD,YAAI,MAAM,IAAI,MAAM,SAAS,EAAE;AAC/B,cAAM,IAAI,MAAM,SAAS,EAAE,KAAK,OAAO,IAAI,MAAM;AACjD,YAAI,MAAM,EAAE,MAAM,KAAK,OAAO,KAAK,MAAM,MAAM,GAAI,GAAG,SAAS,KAAK,SAAS,EAAE,GAAG,OAAO,KAAK,SAAS,EAAE,EAAG;AAE5G,YAAI,OAAO,KAAK,GAAG;AAAA,MAC3B,WAAiB,QAAQ,QAAQ;AACzB,iBAAS,IAAI,GAAG,IAAI,MAAM,GAAG;AAAK,aAAG,OAAO,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC;AACpE,gBAAQ,MAAM;AAAA,MACtB,WAAiB,QAAQ,QAAQ;AACzB,YAAI,KAAK,IAAI,IAAI,CAAC,IAAI,SAAS,MAAM,MAAM,GAAG,IAAI,SAAS,MAAM,SAAS,CAAC,GAAG,KAAK,SAAS,CAAC,CAAC;AAAA,MACtG,WAAiB,QAAQ,QAAQ;AACzB,YAAI,KAAK,IAAI,IAAI,CAAE;AACnB,iBAAS,IAAI,GAAG,IAAI,GAAG;AAAK,cAAI,KAAK,IAAI,EAAE,KAAK,IAAI,SAAS,MAAM,SAAS,IAAI,CAAC,CAAC;AAAA,MACnF,WAAU,QAAQ,UAAU,QAAQ,QAAQ;AAC3C,YAAI,IAAI,KAAK,IAAI,KAAK;AAAM,cAAI,KAAK,IAAI,IAAI,CAAE;AAC/C,YAAI,KAAK,IAAI,SAAS,MAAM,MAAM;AAClC,eAAO,IAAI,UAAU,MAAM,QAAQ,KAAK,MAAM;AAC9C,YAAI,KAAK,SAAS,MAAM,KAAK;AAC7B,YAAI,QAAQ,QAAQ;AAClB,iBAAO,IAAI,UAAU,MAAM,KAAK,GAAG,EAAE;AAAA,QAC/C,OAAe;AACL,gBAAM,KAAK,OAAO,SAAS,KAAK,MAAM,KAAK,GAAG,KAAK,IAAI,EAAE,CAAC;AAC1D,iBAAO,IAAI,SAAS,KAAK,GAAG,IAAI,MAAM;AAAA,QACvC;AAED,YAAI,KAAK,IAAI,EAAE,IAAI,IAAI;AAAA,MAC/B,WAAiB,QAAQ,QAAQ;AACzB,YAAI,IAAI,KAAK,IAAI,KAAK;AAAM,cAAI,KAAK,IAAI,IAAI,CAAE;AAC/C,YAAI,KAAK,GACP,MAAM;AACR,aAAK,IAAI,SAAS,MAAM,GAAG;AAC3B,eAAO,IAAI,UAAU,MAAM,KAAK,KAAK,GAAG;AACxC,cAAM,KAAK;AACX,YAAI,QAAQ,KAAK,GAAG;AACpB,eAAO;AACP,aAAK,IAAI,SAAS,MAAM,GAAG;AAC3B,YAAI,UAAU,MAAM,KAAK,KAAK,GAAG;AACjC,cAAM,KAAK;AACX,aAAK,IAAI,SAAS,MAAM,GAAG;AAC3B,YAAI,SAAS,MAAM,KAAK,KAAK,GAAG;AAChC,cAAM,KAAK;AACX,YAAI,KAAK,OAAO,MAAM;AACtB,YAAI,SAAS,GAAG;AACd,iBAAO,IAAI,SAAS,MAAM,KAAK,EAAE;AAAA,QAC3C,OAAe;AACL,gBAAM,KAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM,EAAE,CAAC;AACpD,iBAAO,IAAI,SAAS,KAAK,GAAG,IAAI,MAAM;AAAA,QACvC;AAED,YAAI,KAAK,IAAI,EAAE,IAAI,IAAI;AAAA,MAC/B,WAAiB,QAAQ,QAAQ;AACzB,YAAI,KAAK,IAAI,IAAI,IAAI,UAAU,MAAM,QAAQ,GAAG;AAAA,MACxD,WAAiB,QAAQ,QAAQ;AACzB,YAAI,KAAK,IAAI,KAAK,MAAM,EAAE,SAAS;AACnC,YAAI,KAAK,IAAI,IAAI,CAAE;AACnB,iBAAS,IAAI,GAAG,IAAI,IAAI;AAAK,cAAI,KAAK,IAAI,EAAE,KAAK,IAAI,MAAM,SAAS,IAAI,CAAC,CAAC;AAAA,MAClF,WAAiB,QAAQ,QAAQ;AACzB,YAAI,IAAI,SAAS;AAAG,cAAI,KAAK,IAAI,IAAI,IAAI,UAAU,MAAM,QAAQ,GAAG;AAAA,iBAC3D,IAAI,SAAS;AAAG,cAAI,KAAK,IAAI,IAAI,IAAI,MAAM,MAAM;AAAA,iBACjD,IAAI,SAAS;AAAG,cAAI,KAAK,IAAI,IAAI,CAAC,IAAI,MAAM,MAAM,GAAG,IAAI,MAAM,SAAS,CAAC,GAAG,IAAI,MAAM,SAAS,CAAC,CAAC;AAAA,MAElH,WAAiB,QAAQ,QAAQ;AACzB,YAAI,KAAK,IAAI,IAAI,IAAI,SAAS,MAAM,MAAM,IAAI;AAAA,MACtD,WAAiB,QAAQ,QAAQ;AACzB,YAAI,KAAK,IAAI,IAAI,KAAK,MAAM;AAAA,MACpC,WAAiB,QAAQ,QAAQ;AACzB,YAAI,IAAI,SAAS,KAAK,IAAI,SAAS,GAAG;AACpC,cAAI,KAAK,IAAI,IAAI,CAAC,IAAI,MAAM,MAAM,CAAC;AAAA,QAC7C,WAAmB,IAAI,SAAS,KAAK,IAAI,SAAS,GAAG;AAC3C,cAAI,KAAK,IAAI,IAAI,CAAC,IAAI,MAAM,MAAM,GAAG,IAAI,MAAM,SAAS,CAAC,GAAG,IAAI,MAAM,SAAS,CAAC,CAAC;AAAA,QAC3F,WAAmB,IAAI,SAAS,GAAG;AACzB,cAAI,KAAK,IAAI,IAAI,KAAK,MAAM;AAAA,QAC7B;AAAA,MACT,WAAiB,QAAQ,QAAQ;AACzB;AAAA,MACD;AAGD,gBAAU;AACV,UAAI,SAAS,MAAM,MAAM;AACzB,gBAAU;AAAA,IACX;AAED,QAAI,QAAQ,GAAG;AACb,UAAI,KAAK,IAAI,OAAO,IAAI,OAAO,SAAS,CAAC;AACzC,SAAG,OAAO,KAAK,OAAO,YAAY,KAAK,GAAG,MAAM,GAAG,IAAI,GAAG,GAAG,KAAK,OAAO,GAAG,KAAK,MAAM;AAAA,IACxF;AAED,QAAI,OAAO,KAAK,OAAO,YAAY,KAAK,IAAI,IAAI,OAAO,IAAI,MAAM;AAEjE,WAAO,IAAI;AACX,WAAO,IAAI;AACX,WAAO,IAAI;AACX,WAAO;AAAA,EACR;AAED,OAAK,OAAO,cAAc,SAAU,KAAK,IAAI,GAAG,GAAG;AACjD,QAAI,MAAM,KAAK,OAAO,QAAQ,GAAG,GAC/B,MAAM,KAAK,KAAM,IAAI,MAAO,CAAC,GAC7B,OAAO,IAAI,YAAY,MAAM,IAAI,IAAI,aAAa,CAAC;AACrD,QAAI,IAAI,KAAK,MAAM;AAAG,WAAK,KAAK,WAAW,IAAI,IAAI;AAAA;AAC9C,WAAK,KAAK,OAAO,SAAS,IAAI,IAAI;AAEvC,QAAI,IAAI,aAAa;AAAG,WAAK,KAAK,OAAO,YAAY,IAAI,KAAK,GAAG,GAAG,CAAC;AAAA,aAC5D,IAAI,aAAa;AAAG,WAAK,KAAK,OAAO,eAAe,IAAI,GAAG;AAEpE,WAAO;AAAA,EACR;AAED,OAAK,OAAO,WAAW,SAAU,MAAM,MAAM;AAC3C,QAAI,MAAM,KAAK,YAAY,EAAE,IAAI,WAAW,KAAK,QAAQ,GAAG,KAAK,SAAS,CAAC,GAAG,IAAI;AAClF,WAAO;AAAA,EACR;AAED,OAAK,aAAc,WAAY;AAC7B,QAAI,IAAI,CAAE;AACV,MAAE,IAAI,CAAE;AACR,MAAE,EAAE,IAAI,SAAU,GAAG,GAAG;AACtB,UAAI,IAAI,YACN,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,GACA;AACF,UAAI,EAAE,CAAC,KAAK,KAAK,EAAE,CAAC,KAAK;AAAG,eAAO,IAAI,IAAI,IAAI,EAAE,CAAC;AAClD,UAAI,IAAI,EAAE,GACR,IAAI,EAAE,GACN,IAAI,EAAE,GACN,IAAI,EAAE,GACN,IAAI,EAAE,GACN,IAAI,EAAE,GACN,IAAI,EAAE,GACN,IAAI,EAAE,GACN,IAAI,KAAK;AACX,UAAI;AAAG,YAAI,IAAI,EAAG,EAAE,WAAW,KAAM,CAAC;AACtC,aAAO,KAAK,GAAG;AACb,YAAI,EAAE,GAAG,GAAG,CAAC;AACb,YAAI,EAAE,GAAG,IAAI,GAAG,CAAC;AACjB,aAAK;AACL,YAAI,KAAK,GAAG;AACV,eAAK,IAAI,MAAM;AAAG,iBAAK,KAAK,IAAI;AAChC,cAAI,KAAK,MAAM,KAAK,GAClB,IAAI,EAAE,IAAI,CAAC,IAAK,EAAE,IAAI,CAAC,KAAK;AAC9B,cAAI;AAAG,gBAAI,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;AACzB,YAAE,IAAI,IAAI,EAAE,EAAE,QAAQ,EAAE,aAAa,GAAG,CAAC,GAAG,CAAC;AAC7C,cAAK,IAAI,KAAM;AACf,eAAK;AACL;AAAA,QACD;AAED,YAAI;AAAG,cAAI,EAAE,EAAE,EAAE,GAAG,KAAK,KAAK,GAAG;AACjC,YAAI,KAAK,GAAG;AACV,cAAI,EAAE;AACN,cAAI,EAAE;AACN,eAAK,KAAK,KAAK;AACf,eAAK,KAAK,KAAK;AAAA,QAChB;AAED,YAAI,KAAK,GAAG;AACV,cAAI,EAAE,GAAG,GAAG,CAAC,IAAI;AACjB,cAAI,EAAE,GAAG,IAAI,GAAG,CAAC,IAAI;AACrB,cAAI,EAAE,GAAG,IAAI,IAAI,CAAC,IAAI;AACtB,eAAK;AACL,cAAI,IAAI;AACR,mBAAS,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AAC9B,cAAE,EAAE,CAAC,IAAI;AACT,cAAE,EAAE,IAAI,CAAC,IAAI;AAAA,UACd;AAED,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,gBAAI,IAAI,EAAE,GAAG,IAAI,IAAI,GAAG,CAAC;AACzB,cAAE,GAAG,EAAE,EAAE,CAAC,KAAK,KAAK,CAAC,IAAI;AACzB,gBAAI,IAAI;AAAG,kBAAI;AAAA,UAChB;AAED,eAAK,IAAI;AACT,YAAE,EAAE,GAAG,CAAC;AACR,YAAE,EAAE,GAAG,GAAG,EAAE,CAAC;AACb,cAAI,EAAE;AACN,cAAI,EAAE;AACN,cAAI,EAAE,EAAE,IAAI,KAAK,KAAK,GAAG,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC;AACzC,cAAI,IAAI,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;AAC1B,eAAK,KAAK,KAAK;AACf,cAAI,IAAI,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;AAC1B,eAAK,KAAK,KAAK;AACf,YAAE,EAAE,GAAG,CAAC;AACR,YAAE,EAAE,GAAG,GAAG,CAAC;AACX,YAAE,EAAE,GAAG,CAAC;AACR,YAAE,EAAE,GAAG,GAAG,CAAC;AAAA,QACZ;AAED,eAAO,MAAI;AACT,cAAI,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC;AACrB,eAAK,IAAI;AACT,cAAI,IAAI,MAAM;AACd,cAAI,MAAM,KAAK,GAAG;AAChB,cAAE,GAAG,IAAI;AAAA,UACrB,WAAqB,KAAK,KAAK;AACnB;AAAA,UACZ,OAAiB;AACL,gBAAI,IAAI,IAAI,IAAI;AAChB,gBAAI,IAAI,KAAK;AACX,kBAAI,IAAI,EAAE,EAAE,IAAI,GAAG;AACnB,kBAAI,KAAK,MAAM,KAAK,EAAE,GAAG,GAAG,IAAI,CAAC;AACjC,mBAAK,IAAI;AAAA,YACV;AAED,gBAAI,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC;AACrB,iBAAK,IAAI;AACT,gBAAI,IAAI,MAAM,GACZ,IAAI,EAAE,EAAE,CAAC,GACT,KAAK,MAAM,KAAK,EAAE,GAAG,GAAG,IAAI,EAAE;AAChC,iBAAK,IAAI;AACT,mBAAO,IAAI,GAAG;AACZ,gBAAE,CAAC,IAAI,EAAE,MAAM,CAAC;AAChB,gBAAE,CAAC,IAAI,EAAE,MAAM,CAAC;AAChB,gBAAE,CAAC,IAAI,EAAE,MAAM,CAAC;AAChB,gBAAE,CAAC,IAAI,EAAE,MAAM,CAAC;AAAA,YACjB;AAED,gBAAI;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAED,aAAO,EAAE,UAAU,IAAI,IAAI,EAAE,MAAM,GAAG,CAAC;AAAA,IACxC;AAED,MAAE,EAAE,IAAI,SAAU,GAAG,GAAG;AACtB,UAAI,IAAI,EAAE;AACV,UAAI,KAAK;AAAG,eAAO;AACnB,UAAI,IAAI,IAAI,WAAW,KAAK,CAAC;AAC7B,QAAE,IAAI,GAAG,CAAC;AACV,aAAO;AAAA,IACR;AAED,MAAE,EAAE,IAAI,SAAU,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,UAAI,IAAI,EAAE,EAAE,GACV,IAAI,EAAE,EAAE,GACR,IAAI;AACN,aAAO,IAAI,GAAG;AACZ,YAAI,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC;AACrB,aAAK,IAAI;AACT,YAAI,IAAI,MAAM;AACd,YAAI,KAAK,IAAI;AACX,YAAE,CAAC,IAAI;AACP;AAAA,QACV,OAAe;AACL,cAAI,IAAI,GACN,IAAI;AACN,cAAI,KAAK,IAAI;AACX,gBAAI,IAAI,EAAE,GAAG,GAAG,CAAC;AACjB,iBAAK;AACL,gBAAI,EAAE,IAAI,CAAC;AAAA,UACvB,WAAqB,KAAK,IAAI;AAClB,gBAAI,IAAI,EAAE,GAAG,GAAG,CAAC;AACjB,iBAAK;AAAA,UACjB,WAAqB,KAAK,IAAI;AAClB,gBAAI,KAAK,EAAE,GAAG,GAAG,CAAC;AAClB,iBAAK;AAAA,UACN;AAED,cAAI,IAAI,IAAI;AACZ,iBAAO,IAAI,GAAG;AACZ,cAAE,CAAC,IAAI;AACP;AAAA,UACD;AAAA,QACF;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,MAAE,EAAE,IAAI,SAAU,GAAG,GAAG,GAAG,GAAG;AAC5B,UAAI,IAAI,GACN,IAAI,GACJ,IAAI,EAAE,WAAW;AACnB,aAAO,IAAI,GAAG;AACZ,YAAI,IAAI,EAAE,IAAI,CAAC;AACf,UAAE,KAAK,CAAC,IAAI;AACZ,WAAG,KAAK,KAAK,CAAC,IAAI;AAClB,YAAI,IAAI;AAAG,cAAI;AACf;AAAA,MACD;AAED,aAAO,IAAI,GAAG;AACZ,UAAE,KAAK,CAAC,IAAI;AACZ,WAAG,KAAK,KAAK,CAAC,IAAI;AAClB;AAAA,MACD;AAED,aAAO;AAAA,IACR;AAED,MAAE,EAAE,IAAI,SAAU,GAAG,GAAG;AACtB,UAAI,IAAI,EAAE,EAAE,GACV,IAAI,EAAE,QACN,GACA,GACA,GACA,GACA,GACA,IAAI,EAAE;AACR,eAAS,IAAI,GAAG,KAAK,GAAG;AAAK,UAAE,CAAC,IAAI;AACpC,WAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AAAG,UAAE,EAAE,CAAC,CAAC;AACjC,UAAI,IAAI,EAAE;AACV,UAAI;AACJ,QAAE,CAAC,IAAI;AACP,WAAK,IAAI,GAAG,KAAK,GAAG,KAAK;AACvB,YAAK,IAAI,EAAE,IAAI,CAAC,KAAM;AACtB,UAAE,CAAC,IAAI;AAAA,MACR;AAED,WAAK,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AACzB,YAAI,EAAE,IAAI,CAAC;AACX,YAAI,KAAK,GAAG;AACV,YAAE,CAAC,IAAI,EAAE,CAAC;AACV,YAAE,CAAC;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AAED,MAAE,EAAE,IAAI,SAAU,GAAG,GAAG,GAAG;AACzB,UAAI,IAAI,EAAE,QACR,IAAI,EAAE,EAAE,GACR,IAAI,EAAE;AACR,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AAC7B,YAAI,EAAE,IAAI,CAAC,KAAK,GAAG;AACjB,cAAI,IAAI,KAAK,GACX,IAAI,EAAE,IAAI,CAAC,GACX,IAAK,KAAK,IAAK,GACf,IAAI,IAAI,GACR,IAAI,EAAE,CAAC,KAAK,GACZ,IAAI,KAAK,KAAK;AAChB,iBAAO,KAAK,GAAG;AACb,gBAAI,IAAI,EAAE,CAAC,MAAO,KAAK;AACvB,cAAE,CAAC,IAAI;AACP;AAAA,UACD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAED,MAAE,EAAE,IAAI,SAAU,GAAG,GAAG;AACtB,UAAI,IAAI,EAAE,EAAE,EAAE,GACZ,IAAI,KAAK;AACX,eAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK,GAAG;AACpC,YAAI,IAAI,EAAE,CAAC,KAAM,IAAI,EAAE,IAAI,CAAC;AAC5B,UAAE,CAAC,IAAI,EAAE,CAAC,MAAM;AAAA,MACjB;AAAA,IACF;AAED,MAAE,EAAE,IAAI,SAAU,GAAG,GAAG,GAAG;AACzB,UAAI,MAAM,IAAI;AACd,UAAI,IAAI,MAAM;AACd,QAAE,CAAC,KAAK;AACR,QAAE,IAAI,CAAC,KAAK,MAAM;AAAA,IACnB;AAED,MAAE,EAAE,IAAI,SAAU,GAAG,GAAG,GAAG;AACzB,UAAI,MAAM,IAAI;AACd,UAAI,IAAI,MAAM;AACd,QAAE,CAAC,KAAK;AACR,QAAE,IAAI,CAAC,KAAK,MAAM;AAClB,QAAE,IAAI,CAAC,KAAK,MAAM;AAAA,IACnB;AAED,MAAE,EAAE,IAAI,SAAU,GAAG,GAAG,GAAG;AACzB,cAAS,EAAE,MAAM,CAAC,IAAK,GAAG,MAAM,KAAK,CAAC,KAAK,QAAS,IAAI,MAAQ,KAAK,KAAK;AAAA,IAC3E;AAED,MAAE,EAAE,IAAI,SAAU,GAAG,GAAG,GAAG;AACzB,cAAS,EAAE,MAAM,CAAC,IAAK,GAAG,MAAM,KAAK,CAAC,KAAK,IAAM,GAAG,MAAM,KAAK,CAAC,KAAK,SAAU,IAAI,MAAQ,KAAK,KAAK;AAAA,IACtG;AAED,MAAE,EAAE,IAAI,SAAU,GAAG,GAAG;AACtB,cAAQ,EAAE,MAAM,CAAC,IAAK,GAAG,MAAM,KAAK,CAAC,KAAK,IAAM,GAAG,MAAM,KAAK,CAAC,KAAK,SAAU,IAAI;AAAA,IACnF;AAED,MAAE,EAAE,IAAI,SAAU,GAAG,GAAG;AACtB,cAAQ,EAAE,MAAM,CAAC,IAAK,GAAG,MAAM,KAAK,CAAC,KAAK,IAAM,GAAG,MAAM,KAAK,CAAC,KAAK,KAAO,GAAG,MAAM,KAAK,CAAC,KAAK,SAAU,IAAI;AAAA,IAC9G;AAED,MAAE,EAAE,IAAK,WAAY;AACnB,UAAI,IAAI,aACN,IAAI;AACN,aAAO;AAAA,QACL,GAAG,IAAI,EAAE,EAAE;AAAA,QACX,GAAG,IAAI,EAAE,EAAE;AAAA,QACX,GAAG,CAAC,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE;AAAA,QACpE,GAAG;AAAA,UACD;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,QACD,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,QAClG,GAAG,IAAI,EAAE,EAAE;AAAA,QACX,GAAG;AAAA,UACD;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,QACD,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,CAAC;AAAA,QAC1G,GAAG,IAAI,EAAE,EAAE;AAAA,QACX,GAAG,IAAI,EAAE,GAAG;AAAA,QACZ,GAAG,CAAE;AAAA,QACL,GAAG,IAAI,EAAE,EAAE;AAAA,QACX,GAAG,CAAE;AAAA,QACL,GAAG,IAAI,EAAE,KAAK;AAAA,QACd,GAAG,CAAE;AAAA,QACL,GAAG,CAAE;AAAA,QACL,GAAG,IAAI,EAAE,KAAK;AAAA,QACd,GAAG,CAAE;AAAA,QACL,GAAG,IAAI,EAAE,GAAG;AAAA,QACZ,GAAG,CAAE;AAAA,QACL,GAAG,IAAI,EAAE,KAAK,EAAE;AAAA,QAChB,GAAG,IAAI,EAAE,GAAG;AAAA,QACZ,GAAG,IAAI,EAAE,EAAE;AAAA,QACX,GAAG,IAAI,EAAE,EAAE;AAAA,QACX,GAAG,IAAI,EAAE,IAAI;AAAA,QACb,GAAG,IAAI,EAAE,KAAK,EAAE;AAAA,QAChB,GAAG,IAAI,EAAE,KAAK,EAAE;AAAA,MACjB;AAAA,IACP,EAAQ;AACH,KAAC,WAAY;AACZ,UAAI,IAAI,EAAE,EAAE,GACV,IAAI,KAAK;AACX,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAI,IAAI;AACR,aAAM,IAAI,gBAAgB,KAAO,IAAI,eAAe;AACpD,aAAM,IAAI,gBAAgB,KAAO,IAAI,cAAc;AACnD,aAAM,IAAI,gBAAgB,KAAO,IAAI,cAAc;AACnD,aAAM,IAAI,gBAAgB,KAAO,IAAI,aAAa;AAClD,UAAE,EAAE,CAAC,KAAM,MAAM,KAAO,KAAK,QAAS;AAAA,MACvC;AAED,eAAS,EAAE,GAAG,GAAG,GAAG;AAClB,eAAO,OAAO;AAAG,YAAE,KAAK,GAAG,CAAC;AAAA,MAC7B;AAED,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,UAAE,EAAE,CAAC,IAAK,EAAE,EAAE,CAAC,KAAK,IAAK,EAAE,EAAE,CAAC;AAC9B,UAAE,EAAE,CAAC,IAAK,EAAE,EAAE,CAAC,KAAK,IAAK,EAAE,EAAE,CAAC;AAAA,MAC/B;AAED,QAAE,EAAE,GAAG,KAAK,CAAC;AACb,QAAE,EAAE,GAAG,MAAM,KAAK,CAAC;AACnB,QAAE,EAAE,GAAG,MAAM,KAAK,CAAC;AACnB,QAAE,EAAE,GAAG,MAAM,KAAK,CAAC;AACnB,QAAE,EAAE,EAAE,EAAE,GAAG,CAAC;AACZ,QAAE,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,CAAC;AACjB,QAAE,EAAE,EAAE,EAAE,GAAG,CAAC;AACZ,QAAE,EAAE,GAAG,IAAI,CAAC;AACZ,QAAE,EAAE,EAAE,EAAE,GAAG,CAAC;AACZ,QAAE,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,CAAC;AACjB,QAAE,EAAE,EAAE,EAAE,GAAG,CAAC;AACZ,QAAE,EAAE,GAAG,IAAI,CAAC;AACZ,QAAE,EAAE,GAAG,KAAK,CAAC;AACb,QAAE,EAAE,GAAG,IAAI,CAAC;AACZ,QAAE,EAAE,GAAG,KAAK,CAAC;AAAA,IACnB,GAAQ;AAEJ,WAAO,EAAE,EAAE;AAAA,EACf,EAAM;AAEJ,OAAK,OAAO,iBAAiB,SAAU,MAAM,KAAK;AAChD,QAAI,IAAI,IAAI,OACV,IAAI,IAAI;AACV,QAAI,MAAM,KAAK,OAAO,QAAQ,GAAG,GAC/B,OAAO,OAAO,GACd,MAAM,KAAK,KAAM,IAAI,MAAO,CAAC;AAC/B,QAAI,MAAM,IAAI,WAAW,IAAI,GAAG;AAChC,QAAI,KAAK;AAET,QAAI,eAAe,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACvC,QAAI,eAAe,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACvC,QAAI,gBAAgB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACxC,QAAI,gBAAgB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAExC,QAAI,OAAO;AACX,WAAO,OAAO,GAAG;AACf,UAAI,KAAK,cAAc,IAAI,GACzB,KAAK,cAAc,IAAI;AACzB,UAAI,KAAK,GACP,KAAK;AACP,UAAI,KAAK,aAAa,IAAI;AAC1B,aAAO,KAAK,GAAG;AACb,cAAM;AACN;AAAA,MACD;AAED,UAAI,KAAK,aAAa,IAAI;AAC1B,aAAO,KAAK,GAAG;AACb,cAAM;AACN;AAAA,MACD;AAED,UAAI,OAAO,KAAK,KAAM,KAAK,MAAO,CAAC;AACnC,WAAK,OAAO,YAAY,MAAM,KAAK,IAAI,IAAI,EAAE;AAE7C,UAAI,IAAI,GACN,MAAM,aAAa,IAAI;AACzB,UAAI;AAEJ,aAAO,MAAM,GAAG;AACd,YAAI,MAAM,aAAa,IAAI;AAC3B,YAAI,MAAO,KAAK,IAAI,QAAS;AAE7B,eAAO,MAAM,GAAG;AACd,cAAI,OAAO,GAAG;AACZ,kBAAM,KAAK,OAAO,CAAC;AACnB,kBAAO,OAAQ,KAAK,MAAM,KAAO;AACjC,gBAAI,MAAM,OAAO,OAAO,EAAE,KAAK,OAAQ,MAAM,MAAM,MAAM;AAAA,UAC1D;AAED,cAAI,OAAO,GAAG;AACZ,kBAAM,KAAK,OAAO,CAAC;AACnB,kBAAO,OAAQ,KAAK,MAAM,KAAO;AACjC,gBAAI,MAAM,OAAO,OAAO,EAAE,KAAK,OAAQ,MAAM,MAAM,MAAM;AAAA,UAC1D;AAED,cAAI,OAAO,GAAG;AACZ,kBAAM,KAAK,OAAO,CAAC;AACnB,kBAAO,OAAQ,KAAK,MAAM,KAAO;AACjC,gBAAI,MAAM,OAAO,OAAO,EAAE,KAAK,OAAQ,MAAM,MAAM,MAAM;AAAA,UAC1D;AAED,cAAI,OAAO,GAAG;AACZ,gBAAI,KAAK,MAAM,MAAM,MAAM;AAC3B,qBAAS,IAAI,GAAG,IAAI,MAAM;AAAK,kBAAI,KAAK,CAAC,IAAI,MAAM,OAAO,KAAK,CAAC;AAAA,UACjE;AAED,iBAAO;AACP,iBAAO;AAAA,QACR;AAED;AACA,eAAO;AAAA,MACR;AAED,UAAI,KAAK,MAAM;AAAG,cAAM,MAAM,IAAI;AAClC,aAAO,OAAO;AAAA,IACf;AAED,WAAO;AAAA,EACR;AAED,OAAK,OAAO,UAAU,SAAU,KAAK;AACnC,QAAI,MAAM,CAAC,GAAG,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC,EAAE,IAAI,KAAK;AAC/C,WAAO,MAAM,IAAI;AAAA,EAClB;AAED,OAAK,OAAO,cAAc,SAAU,MAAM,KAAK,KAAK,GAAG,GAAG;AACxD,QAAI,MAAM,KAAK,OAAO,QAAQ,GAAG,GAC/B,MAAM,KAAK,KAAM,IAAI,MAAO,CAAC,GAC7B,QAAQ,KAAK,OAAO;AACtB,UAAM,KAAK,KAAK,MAAM,CAAC;AAEvB,QAAI,GACF,IACA,OAAO,KAAK,GAAG,GACf,IAAI;AAEN,QAAI,OAAO;AAAG,WAAK,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,OAAO,CAAC;AAC5C,QAAI,QAAQ;AAAG,WAAK,IAAI,KAAK,IAAI,KAAK;AAAK,aAAK,IAAI,CAAC,IAAK,KAAK,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,GAAG,MAAM,KAAM;AAErG,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,UAAI,MAAM,IAAI;AACd,WAAK,IAAI,IAAI;AACb,aAAO,KAAK,KAAK,CAAC;AAClB,UAAI;AAEJ,UAAI,QAAQ,GAAG;AACb,eAAO,IAAI,KAAK;AAAK,eAAK,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC;AAAA,MACtD,WAAiB,QAAQ,GAAG;AACpB,eAAO,IAAI,KAAK;AAAK,eAAK,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC;AAC9C,eAAO,IAAI,KAAK;AAAK,eAAK,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,IAAI,IAAI,GAAG;AAAA,MAC1E,WAAiB,QAAQ,GAAG;AACpB,eAAO,IAAI,KAAK;AAAK,eAAK,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,IAAI,IAAI,GAAG;AAAA,MAC1E,WAAiB,QAAQ,GAAG;AACpB,eAAO,IAAI,KAAK;AAAK,eAAK,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,KAAK,KAAK,IAAI,IAAI,GAAG,MAAM;AACzE,eAAO,IAAI,KAAK;AAAK,eAAK,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,KAAM,KAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI,GAAG,MAAO;AAAA,MACvG,OAAa;AACL,eAAO,IAAI,KAAK;AAAK,eAAK,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,MAAM,GAAG,KAAK,IAAI,IAAI,GAAG,GAAG,CAAC;AAC/E,eAAO,IAAI,KAAK,KAAK;AACnB,eAAK,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,MAAM,KAAK,IAAI,IAAI,GAAG,GAAG,KAAK,IAAI,IAAI,GAAG,GAAG,KAAK,IAAI,IAAI,MAAM,GAAG,CAAC;AAAA,QACjG;AAAA,MACF;AAAA,IACF;AAED,WAAO;AAAA,EACR;AAED,OAAK,OAAO,SAAS,SAAU,GAAG,GAAG,GAAG;AACtC,QAAI,IAAI,IAAI,IAAI,GACd,KAAK,IAAI,GACT,KAAK,IAAI,GACT,KAAK,IAAI;AACX,QAAI,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAAI,aAAO;AAAA,aAC5C,KAAK,MAAM,KAAK;AAAI,aAAO;AACpC,WAAO;AAAA,EACR;AAED,OAAK,OAAO,QAAQ,SAAU,MAAM,QAAQ,KAAK;AAC/C,QAAI,MAAM,KAAK;AACf,QAAI,QAAQ,IAAI,SAAS,MAAM,MAAM;AACrC,cAAU;AACV,QAAI,SAAS,IAAI,SAAS,MAAM,MAAM;AACtC,cAAU;AACV,QAAI,QAAQ,KAAK,MAAM;AACvB;AACA,QAAI,QAAQ,KAAK,MAAM;AACvB;AACA,QAAI,WAAW,KAAK,MAAM;AAC1B;AACA,QAAI,SAAS,KAAK,MAAM;AACxB;AACA,QAAI,YAAY,KAAK,MAAM;AAC3B;AAAA,EACD;AAED,OAAK,OAAO;AAAA,IACV,UAAU,SAAU,MAAM,GAAG;AAC3B,aAAO,KAAK,CAAC,KAAK;AAAG;AACrB,aAAO;AAAA,IACR;AAAA,IACD,YAAY,SAAU,MAAM,GAAG;AAC7B,aAAQ,KAAK,CAAC,KAAK,IAAK,KAAK,IAAI,CAAC;AAAA,IACnC;AAAA,IACD,aAAa,SAAU,MAAM,GAAG,GAAG;AACjC,WAAK,CAAC,IAAK,KAAK,IAAK;AACrB,WAAK,IAAI,CAAC,IAAI,IAAI;AAAA,IACnB;AAAA,IACD,UAAU,SAAU,MAAM,GAAG;AAC3B,aAAO,KAAK,CAAC,KAAK,MAAM,MAAM,QAAS,KAAK,IAAI,CAAC,KAAK,KAAO,KAAK,IAAI,CAAC,KAAK,IAAK,KAAK,IAAI,CAAC;AAAA,IAC5F;AAAA,IACD,WAAW,SAAU,MAAM,GAAG,GAAG;AAC/B,WAAK,CAAC,IAAK,KAAK,KAAM;AACtB,WAAK,IAAI,CAAC,IAAK,KAAK,KAAM;AAC1B,WAAK,IAAI,CAAC,IAAK,KAAK,IAAK;AACzB,WAAK,IAAI,CAAC,IAAI,IAAI;AAAA,IACnB;AAAA,IACD,WAAW,SAAU,MAAM,GAAG,GAAG;AAC/B,UAAI,IAAI;AACR,eAAS,IAAI,GAAG,IAAI,GAAG;AAAK,aAAK,OAAO,aAAa,KAAK,IAAI,CAAC,CAAC;AAChE,aAAO;AAAA,IACR;AAAA,IACD,YAAY,SAAU,MAAM,GAAG,GAAG;AAChC,eAAS,IAAI,GAAG,IAAI,EAAE,QAAQ;AAAK,aAAK,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC;AAAA,IAChE;AAAA,IACD,WAAW,SAAU,MAAM,GAAG,GAAG;AAC/B,UAAI,MAAM,CAAE;AACZ,eAAS,IAAI,GAAG,IAAI,GAAG;AAAK,YAAI,KAAK,KAAK,IAAI,CAAC,CAAC;AAChD,aAAO;AAAA,IACR;AAAA,IACD,KAAK,SAAU,GAAG;AAChB,aAAO,EAAE,SAAS,IAAI,MAAM,IAAI;AAAA,IACjC;AAAA,IACD,UAAU,SAAU,MAAM,GAAG,GAAG;AAC9B,UAAI,IAAI,IACN;AACF,eAAS,IAAI,GAAG,IAAI,GAAG;AAAK,aAAK,MAAM,KAAK,KAAK,IAAI,KAAK,IAAI,CAAC,EAAE,SAAS,EAAE,CAAC;AAC7E,UAAI;AACF,aAAK,mBAAmB,CAAC;AAAA,MAC1B,SAAQ,GAAP;AACA,eAAO,KAAK,KAAK,UAAU,MAAM,GAAG,CAAC;AAAA,MACtC;AAED,aAAO;AAAA,IACR;AAAA,EACF;AACD,OAAK,YAAY,SAAU,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,MAAM,MAAM;AACnE,QAAI,IAAI,KAAK,IAAI,IAAI,EAAE,GACrB,IAAI,KAAK,IAAI,IAAI,EAAE;AACrB,QAAI,KAAK,GACP,KAAK;AACP,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAI,QAAQ,KAAK,QAAQ,GAAG;AAC1B,eAAM,IAAI,KAAK,KAAM;AACrB,gBAAO,OAAO,KAAK,KAAK,OAAO,KAAM;AAAA,QAC/C,OAAe;AACL,gBAAO,CAAC,OAAO,KAAK,KAAK,OAAO,KAAM;AACtC,eAAM,IAAI,KAAK,KAAM;AAAA,QACtB;AAED,YAAI,QAAQ,GAAG;AACb,aAAG,EAAE,IAAI,GAAG,EAAE;AACd,aAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC;AACtB,aAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC;AACtB,aAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC;AAAA,QAChC,WAAmB,QAAQ,GAAG;AACpB,cAAI,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,MACzB,KAAK,GAAG,EAAE,IAAI,IACd,KAAK,GAAG,KAAK,CAAC,IAAI,IAClB,KAAK,GAAG,KAAK,CAAC,IAAI;AACpB,cAAI,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,MACzB,KAAK,GAAG,EAAE,IAAI,IACd,KAAK,GAAG,KAAK,CAAC,IAAI,IAClB,KAAK,GAAG,KAAK,CAAC,IAAI;AAEpB,cAAI,MAAM,IAAI,IACZ,KAAK,KAAK,KAAK,KACf,MAAM,MAAM,IAAI,IAAI,IAAI;AAC1B,aAAG,KAAK,CAAC,IAAI,MAAM;AACnB,aAAG,KAAK,CAAC,KAAK,KAAK,KAAK,OAAO;AAC/B,aAAG,KAAK,CAAC,KAAK,KAAK,KAAK,OAAO;AAC/B,aAAG,KAAK,CAAC,KAAK,KAAK,KAAK,OAAO;AAAA,QACzC,WAAmB,QAAQ,GAAG;AAGpB,cAAI,KAAK,GAAG,KAAK,CAAC,GAChB,KAAK,GAAG,EAAE,GACV,KAAK,GAAG,KAAK,CAAC,GACd,KAAK,GAAG,KAAK,CAAC;AAChB,cAAI,KAAK,GAAG,KAAK,CAAC,GAChB,KAAK,GAAG,EAAE,GACV,KAAK,GAAG,KAAK,CAAC,GACd,KAAK,GAAG,KAAK,CAAC;AAChB,cAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAChD,eAAG,EAAE,IAAI;AACT,eAAG,KAAK,CAAC,IAAI;AACb,eAAG,KAAK,CAAC,IAAI;AACb,eAAG,KAAK,CAAC,IAAI;AAAA,UACzB,OAAiB;AACL,eAAG,EAAE,IAAI;AACT,eAAG,KAAK,CAAC,IAAI;AACb,eAAG,KAAK,CAAC,IAAI;AACb,eAAG,KAAK,CAAC,IAAI;AAAA,UACd;AAAA,QACX,WAAmB,QAAQ,GAAG;AAGpB,cAAI,KAAK,GAAG,KAAK,CAAC,GAChB,KAAK,GAAG,EAAE,GACV,KAAK,GAAG,KAAK,CAAC,GACd,KAAK,GAAG,KAAK,CAAC;AAChB,cAAI,KAAK,GAAG,KAAK,CAAC,GAChB,KAAK,GAAG,EAAE,GACV,KAAK,GAAG,KAAK,CAAC,GACd,KAAK,GAAG,KAAK,CAAC;AAChB,cAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM;AAAI;AAElD,cAAI,KAAK,OAAO,KAAK;AAAI,mBAAO;AAAA,QACjC;AAAA,MACF;AAAA,IACF;AAED,WAAO;AAAA,EACR;AACH;AAEA,MAAM,mBAAmBA,MAAAA,kBAAkB;AAAA,EACzC,YAAY,SAAS;AACnB,UAAM,OAAO;AAEb,SAAK,OAAOC,MAAa;AACzB,SAAK,WAAW;AAAA,EACjB;AAAA,EAED,YAAY,OAAO;AACjB,SAAK,OAAO;AACZ,WAAO;AAAA,EACR;AAAA,EAED,YAAY,OAAO;AACjB,SAAK,WAAW;AAChB,WAAO;AAAA,EACR;AAAA,EAED,YAAY,MAAM,QAAQ,YAAY,SAAS;AAC7C,UAAM,UAAU,IAAIC,kBAAa;AAEjC,QAAI,SAAS;AAEb,UAAM,QAAQ;AAEd,aAAS,YAAY,GAAG;AACtB,YAAM;AAAA,QACJ,KAAK,CAAC;AAAA,QACN,SAAU,OAAO;AACf,kBAAQ,OAAO,CAAC,IAAI;AAEpB;AAEA,cAAI,WAAW,GAAG;AAChB,oBAAQ,cAAc;AAEtB,gBAAI;AAAQ,qBAAO,OAAO;AAAA,UAC3B;AAAA,QACF;AAAA,QACD;AAAA,QACA;AAAA,MACD;AAAA,IACF;AAED,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,kBAAY,CAAC;AAAA,IACd;AAED,YAAQ,OAAO,KAAK;AACpB,YAAQ,SAASC,MAAU;AAC3B,YAAQ,YAAYC,MAAY;AAChC,YAAQ,kBAAkB;AAE1B,WAAO;AAAA,EACR;AAAA,EAED,MAAM,QAAQ;AACZ,SAAM;AACN,UAAM,MAAM,KAAK,OAAO,MAAM;AAC9B,UAAM,OAAO,KAAK,QAAQ,GAAG,EAAE,CAAC;AAEhC,UAAM,OAAO,IAAI,WAAW,IAAI;AAChC,UAAM,OAAO,IAAI,QAAQ,IAAI,SAAS;AAEtC,UAAM,SAAS,KAAK,SAASH,MAAa,gBAAG,IAAI,YAAY,IAAI,IAAI,IAAI,aAAa,IAAI;AAI1F,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACvC,YAAM,IAAI,KAAK,IAAI,CAAC,IAAI;AACxB,YAAM,IAAI,KAAK,IAAI,CAAC,IAAI;AACxB,YAAM,IAAI,KAAK,IAAI,CAAC,IAAI;AACxB,YAAM,IAAI,KAAK,IAAI,CAAC,IAAI;AAExB,UAAI,KAAK,SAASA,qBAAe;AAC/B,eAAO,IAAI,CAAC,IAAII,MAAAA,UAAU,YAAY,KAAK,IAAI,IAAI,IAAI,KAAK,UAAU,KAAK,CAAC;AAC5E,eAAO,IAAI,CAAC,IAAIA,MAAAA,UAAU,YAAY,KAAK,IAAI,IAAI,IAAI,KAAK,UAAU,KAAK,CAAC;AAC5E,eAAO,IAAI,CAAC,IAAIA,MAAAA,UAAU,YAAY,KAAK,IAAI,IAAI,IAAI,KAAK,UAAU,KAAK,CAAC;AAC5E,eAAO,IAAI,CAAC,IAAIA,MAAAA,UAAU,YAAY,CAAC;AAAA,MAC/C,OAAa;AACL,eAAO,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK;AAC7B,eAAO,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK;AAC7B,eAAO,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK;AAC7B,eAAO,IAAI,CAAC,IAAI;AAAA,MACjB;AAAA,IACF;AAED,WAAO;AAAA,MACL,OAAO,IAAI;AAAA,MACX,QAAQ,IAAI;AAAA,MACZ,MAAM;AAAA,MACN,QAAQF,MAAU;AAAA,MAClB,MAAM,KAAK;AAAA,MACX,OAAO;AAAA,IACR;AAAA,EACF;AACH;;"}