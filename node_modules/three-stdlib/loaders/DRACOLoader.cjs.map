{"version": 3, "file": "DRACOLoader.cjs", "sources": ["../../src/loaders/DRACOLoader.js"], "sourcesContent": ["import { BufferAttribute, <PERSON>ufferGeometry, FileLoader, Loader } from 'three'\n\nconst _taskCache = new WeakMap()\n\nclass DRACOLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n\n    this.decoderPath = ''\n    this.decoderConfig = {}\n    this.decoderBinary = null\n    this.decoderPending = null\n\n    this.workerLimit = 4\n    this.workerPool = []\n    this.workerNextTaskID = 1\n    this.workerSourceURL = ''\n\n    this.defaultAttributeIDs = {\n      position: 'POSITION',\n      normal: 'NORMAL',\n      color: 'COLOR',\n      uv: 'TEX_COORD',\n    }\n    this.defaultAttributeTypes = {\n      position: 'Float32Array',\n      normal: 'Float32Array',\n      color: 'Float32Array',\n      uv: 'Float32Array',\n    }\n  }\n\n  setDecoderPath(path) {\n    this.decoderPath = path\n\n    return this\n  }\n\n  setDecoderConfig(config) {\n    this.decoderConfig = config\n\n    return this\n  }\n\n  setWorkerLimit(workerLimit) {\n    this.workerLimit = workerLimit\n\n    return this\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const loader = new FileLoader(this.manager)\n\n    loader.setPath(this.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(this.requestHeader)\n    loader.setWithCredentials(this.withCredentials)\n\n    loader.load(\n      url,\n      (buffer) => {\n        const taskConfig = {\n          attributeIDs: this.defaultAttributeIDs,\n          attributeTypes: this.defaultAttributeTypes,\n          useUniqueIDs: false,\n        }\n\n        this.decodeGeometry(buffer, taskConfig).then(onLoad).catch(onError)\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  /** @deprecated Kept for backward-compatibility with previous DRACOLoader versions. */\n  decodeDracoFile(buffer, callback, attributeIDs, attributeTypes) {\n    const taskConfig = {\n      attributeIDs: attributeIDs || this.defaultAttributeIDs,\n      attributeTypes: attributeTypes || this.defaultAttributeTypes,\n      useUniqueIDs: !!attributeIDs,\n    }\n\n    this.decodeGeometry(buffer, taskConfig).then(callback)\n  }\n\n  decodeGeometry(buffer, taskConfig) {\n    // TODO: For backward-compatibility, support 'attributeTypes' objects containing\n    // references (rather than names) to typed array constructors. These must be\n    // serialized before sending them to the worker.\n    for (const attribute in taskConfig.attributeTypes) {\n      const type = taskConfig.attributeTypes[attribute]\n\n      if (type.BYTES_PER_ELEMENT !== undefined) {\n        taskConfig.attributeTypes[attribute] = type.name\n      }\n    }\n\n    //\n\n    const taskKey = JSON.stringify(taskConfig)\n\n    // Check for an existing task using this buffer. A transferred buffer cannot be transferred\n    // again from this thread.\n    if (_taskCache.has(buffer)) {\n      const cachedTask = _taskCache.get(buffer)\n\n      if (cachedTask.key === taskKey) {\n        return cachedTask.promise\n      } else if (buffer.byteLength === 0) {\n        // Technically, it would be possible to wait for the previous task to complete,\n        // transfer the buffer back, and decode again with the second configuration. That\n        // is complex, and I don't know of any reason to decode a Draco buffer twice in\n        // different ways, so this is left unimplemented.\n        throw new Error(\n          'THREE.DRACOLoader: Unable to re-decode a buffer with different ' +\n            'settings. Buffer has already been transferred.',\n        )\n      }\n    }\n\n    //\n\n    let worker\n    const taskID = this.workerNextTaskID++\n    const taskCost = buffer.byteLength\n\n    // Obtain a worker and assign a task, and construct a geometry instance\n    // when the task completes.\n    const geometryPending = this._getWorker(taskID, taskCost)\n      .then((_worker) => {\n        worker = _worker\n\n        return new Promise((resolve, reject) => {\n          worker._callbacks[taskID] = { resolve, reject }\n\n          worker.postMessage({ type: 'decode', id: taskID, taskConfig, buffer }, [buffer])\n\n          // this.debug();\n        })\n      })\n      .then((message) => this._createGeometry(message.geometry))\n\n    // Remove task from the task list.\n    // Note: replaced '.finally()' with '.catch().then()' block - iOS 11 support (#19416)\n    geometryPending\n      .catch(() => true)\n      .then(() => {\n        if (worker && taskID) {\n          this._releaseTask(worker, taskID)\n\n          // this.debug();\n        }\n      })\n\n    // Cache the task result.\n    _taskCache.set(buffer, {\n      key: taskKey,\n      promise: geometryPending,\n    })\n\n    return geometryPending\n  }\n\n  _createGeometry(geometryData) {\n    const geometry = new BufferGeometry()\n\n    if (geometryData.index) {\n      geometry.setIndex(new BufferAttribute(geometryData.index.array, 1))\n    }\n\n    for (let i = 0; i < geometryData.attributes.length; i++) {\n      const attribute = geometryData.attributes[i]\n      const name = attribute.name\n      const array = attribute.array\n      const itemSize = attribute.itemSize\n\n      geometry.setAttribute(name, new BufferAttribute(array, itemSize))\n    }\n\n    return geometry\n  }\n\n  _loadLibrary(url, responseType) {\n    const loader = new FileLoader(this.manager)\n    loader.setPath(this.decoderPath)\n    loader.setResponseType(responseType)\n    loader.setWithCredentials(this.withCredentials)\n\n    return new Promise((resolve, reject) => {\n      loader.load(url, resolve, undefined, reject)\n    })\n  }\n\n  preload() {\n    this._initDecoder()\n\n    return this\n  }\n\n  _initDecoder() {\n    if (this.decoderPending) return this.decoderPending\n\n    const useJS = typeof WebAssembly !== 'object' || this.decoderConfig.type === 'js'\n    const librariesPending = []\n\n    if (useJS) {\n      librariesPending.push(this._loadLibrary('draco_decoder.js', 'text'))\n    } else {\n      librariesPending.push(this._loadLibrary('draco_wasm_wrapper.js', 'text'))\n      librariesPending.push(this._loadLibrary('draco_decoder.wasm', 'arraybuffer'))\n    }\n\n    this.decoderPending = Promise.all(librariesPending).then((libraries) => {\n      const jsContent = libraries[0]\n\n      if (!useJS) {\n        this.decoderConfig.wasmBinary = libraries[1]\n      }\n\n      const fn = DRACOWorker.toString()\n\n      const body = [\n        '/* draco decoder */',\n        jsContent,\n        '',\n        '/* worker */',\n        fn.substring(fn.indexOf('{') + 1, fn.lastIndexOf('}')),\n      ].join('\\n')\n\n      this.workerSourceURL = URL.createObjectURL(new Blob([body]))\n    })\n\n    return this.decoderPending\n  }\n\n  _getWorker(taskID, taskCost) {\n    return this._initDecoder().then(() => {\n      if (this.workerPool.length < this.workerLimit) {\n        const worker = new Worker(this.workerSourceURL)\n\n        worker._callbacks = {}\n        worker._taskCosts = {}\n        worker._taskLoad = 0\n\n        worker.postMessage({ type: 'init', decoderConfig: this.decoderConfig })\n\n        worker.onmessage = function (e) {\n          const message = e.data\n\n          switch (message.type) {\n            case 'decode':\n              worker._callbacks[message.id].resolve(message)\n              break\n\n            case 'error':\n              worker._callbacks[message.id].reject(message)\n              break\n\n            default:\n              console.error('THREE.DRACOLoader: Unexpected message, \"' + message.type + '\"')\n          }\n        }\n\n        this.workerPool.push(worker)\n      } else {\n        this.workerPool.sort(function (a, b) {\n          return a._taskLoad > b._taskLoad ? -1 : 1\n        })\n      }\n\n      const worker = this.workerPool[this.workerPool.length - 1]\n      worker._taskCosts[taskID] = taskCost\n      worker._taskLoad += taskCost\n      return worker\n    })\n  }\n\n  _releaseTask(worker, taskID) {\n    worker._taskLoad -= worker._taskCosts[taskID]\n    delete worker._callbacks[taskID]\n    delete worker._taskCosts[taskID]\n  }\n\n  debug() {\n    console.log(\n      'Task load: ',\n      this.workerPool.map((worker) => worker._taskLoad),\n    )\n  }\n\n  dispose() {\n    for (let i = 0; i < this.workerPool.length; ++i) {\n      this.workerPool[i].terminate()\n    }\n\n    this.workerPool.length = 0\n\n    return this\n  }\n}\n\n/* WEB WORKER */\n\nfunction DRACOWorker() {\n  let decoderConfig\n  let decoderPending\n\n  onmessage = function (e) {\n    const message = e.data\n\n    switch (message.type) {\n      case 'init':\n        decoderConfig = message.decoderConfig\n        decoderPending = new Promise(function (resolve /*, reject*/) {\n          decoderConfig.onModuleLoaded = function (draco) {\n            // Module is Promise-like. Wrap before resolving to avoid loop.\n            resolve({ draco: draco })\n          }\n\n          DracoDecoderModule(decoderConfig)\n        })\n        break\n\n      case 'decode':\n        const buffer = message.buffer\n        const taskConfig = message.taskConfig\n        decoderPending.then((module) => {\n          const draco = module.draco\n          const decoder = new draco.Decoder()\n          const decoderBuffer = new draco.DecoderBuffer()\n          decoderBuffer.Init(new Int8Array(buffer), buffer.byteLength)\n\n          try {\n            const geometry = decodeGeometry(draco, decoder, decoderBuffer, taskConfig)\n\n            const buffers = geometry.attributes.map((attr) => attr.array.buffer)\n\n            if (geometry.index) buffers.push(geometry.index.array.buffer)\n\n            self.postMessage({ type: 'decode', id: message.id, geometry }, buffers)\n          } catch (error) {\n            console.error(error)\n\n            self.postMessage({ type: 'error', id: message.id, error: error.message })\n          } finally {\n            draco.destroy(decoderBuffer)\n            draco.destroy(decoder)\n          }\n        })\n        break\n    }\n  }\n\n  function decodeGeometry(draco, decoder, decoderBuffer, taskConfig) {\n    const attributeIDs = taskConfig.attributeIDs\n    const attributeTypes = taskConfig.attributeTypes\n\n    let dracoGeometry\n    let decodingStatus\n\n    const geometryType = decoder.GetEncodedGeometryType(decoderBuffer)\n\n    if (geometryType === draco.TRIANGULAR_MESH) {\n      dracoGeometry = new draco.Mesh()\n      decodingStatus = decoder.DecodeBufferToMesh(decoderBuffer, dracoGeometry)\n    } else if (geometryType === draco.POINT_CLOUD) {\n      dracoGeometry = new draco.PointCloud()\n      decodingStatus = decoder.DecodeBufferToPointCloud(decoderBuffer, dracoGeometry)\n    } else {\n      throw new Error('THREE.DRACOLoader: Unexpected geometry type.')\n    }\n\n    if (!decodingStatus.ok() || dracoGeometry.ptr === 0) {\n      throw new Error('THREE.DRACOLoader: Decoding failed: ' + decodingStatus.error_msg())\n    }\n\n    const geometry = { index: null, attributes: [] }\n\n    // Gather all vertex attributes.\n    for (const attributeName in attributeIDs) {\n      const attributeType = self[attributeTypes[attributeName]]\n\n      let attribute\n      let attributeID\n\n      // A Draco file may be created with default vertex attributes, whose attribute IDs\n      // are mapped 1:1 from their semantic name (POSITION, NORMAL, ...). Alternatively,\n      // a Draco file may contain a custom set of attributes, identified by known unique\n      // IDs. glTF files always do the latter, and `.drc` files typically do the former.\n      if (taskConfig.useUniqueIDs) {\n        attributeID = attributeIDs[attributeName]\n        attribute = decoder.GetAttributeByUniqueId(dracoGeometry, attributeID)\n      } else {\n        attributeID = decoder.GetAttributeId(dracoGeometry, draco[attributeIDs[attributeName]])\n\n        if (attributeID === -1) continue\n\n        attribute = decoder.GetAttribute(dracoGeometry, attributeID)\n      }\n\n      geometry.attributes.push(decodeAttribute(draco, decoder, dracoGeometry, attributeName, attributeType, attribute))\n    }\n\n    // Add index.\n    if (geometryType === draco.TRIANGULAR_MESH) {\n      geometry.index = decodeIndex(draco, decoder, dracoGeometry)\n    }\n\n    draco.destroy(dracoGeometry)\n\n    return geometry\n  }\n\n  function decodeIndex(draco, decoder, dracoGeometry) {\n    const numFaces = dracoGeometry.num_faces()\n    const numIndices = numFaces * 3\n    const byteLength = numIndices * 4\n\n    const ptr = draco._malloc(byteLength)\n    decoder.GetTrianglesUInt32Array(dracoGeometry, byteLength, ptr)\n    const index = new Uint32Array(draco.HEAPF32.buffer, ptr, numIndices).slice()\n    draco._free(ptr)\n\n    return { array: index, itemSize: 1 }\n  }\n\n  function decodeAttribute(draco, decoder, dracoGeometry, attributeName, attributeType, attribute) {\n    const numComponents = attribute.num_components()\n    const numPoints = dracoGeometry.num_points()\n    const numValues = numPoints * numComponents\n    const byteLength = numValues * attributeType.BYTES_PER_ELEMENT\n    const dataType = getDracoDataType(draco, attributeType)\n\n    const ptr = draco._malloc(byteLength)\n    decoder.GetAttributeDataArrayForAllPoints(dracoGeometry, attribute, dataType, byteLength, ptr)\n    const array = new attributeType(draco.HEAPF32.buffer, ptr, numValues).slice()\n    draco._free(ptr)\n\n    return {\n      name: attributeName,\n      array: array,\n      itemSize: numComponents,\n    }\n  }\n\n  function getDracoDataType(draco, attributeType) {\n    switch (attributeType) {\n      case Float32Array:\n        return draco.DT_FLOAT32\n      case Int8Array:\n        return draco.DT_INT8\n      case Int16Array:\n        return draco.DT_INT16\n      case Int32Array:\n        return draco.DT_INT32\n      case Uint8Array:\n        return draco.DT_UINT8\n      case Uint16Array:\n        return draco.DT_UINT16\n      case Uint32Array:\n        return draco.DT_UINT32\n    }\n  }\n}\n\nexport { DRACOLoader }\n"], "names": ["Loader", "<PERSON><PERSON><PERSON><PERSON>", "BufferGeometry", "BufferAttribute", "worker", "module"], "mappings": ";;;AAEA,MAAM,aAAa,oBAAI,QAAS;AAEhC,MAAM,oBAAoBA,MAAAA,OAAO;AAAA,EAC/B,YAAY,SAAS;AACnB,UAAM,OAAO;AAEb,SAAK,cAAc;AACnB,SAAK,gBAAgB,CAAE;AACvB,SAAK,gBAAgB;AACrB,SAAK,iBAAiB;AAEtB,SAAK,cAAc;AACnB,SAAK,aAAa,CAAE;AACpB,SAAK,mBAAmB;AACxB,SAAK,kBAAkB;AAEvB,SAAK,sBAAsB;AAAA,MACzB,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,IAAI;AAAA,IACL;AACD,SAAK,wBAAwB;AAAA,MAC3B,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,IAAI;AAAA,IACL;AAAA,EACF;AAAA,EAED,eAAe,MAAM;AACnB,SAAK,cAAc;AAEnB,WAAO;AAAA,EACR;AAAA,EAED,iBAAiB,QAAQ;AACvB,SAAK,gBAAgB;AAErB,WAAO;AAAA,EACR;AAAA,EAED,eAAe,aAAa;AAC1B,SAAK,cAAc;AAEnB,WAAO;AAAA,EACR;AAAA,EAED,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,UAAM,SAAS,IAAIC,iBAAW,KAAK,OAAO;AAE1C,WAAO,QAAQ,KAAK,IAAI;AACxB,WAAO,gBAAgB,aAAa;AACpC,WAAO,iBAAiB,KAAK,aAAa;AAC1C,WAAO,mBAAmB,KAAK,eAAe;AAE9C,WAAO;AAAA,MACL;AAAA,MACA,CAAC,WAAW;AACV,cAAM,aAAa;AAAA,UACjB,cAAc,KAAK;AAAA,UACnB,gBAAgB,KAAK;AAAA,UACrB,cAAc;AAAA,QACf;AAED,aAAK,eAAe,QAAQ,UAAU,EAAE,KAAK,MAAM,EAAE,MAAM,OAAO;AAAA,MACnE;AAAA,MACD;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAAA;AAAA,EAGD,gBAAgB,QAAQ,UAAU,cAAc,gBAAgB;AAC9D,UAAM,aAAa;AAAA,MACjB,cAAc,gBAAgB,KAAK;AAAA,MACnC,gBAAgB,kBAAkB,KAAK;AAAA,MACvC,cAAc,CAAC,CAAC;AAAA,IACjB;AAED,SAAK,eAAe,QAAQ,UAAU,EAAE,KAAK,QAAQ;AAAA,EACtD;AAAA,EAED,eAAe,QAAQ,YAAY;AAIjC,eAAW,aAAa,WAAW,gBAAgB;AACjD,YAAM,OAAO,WAAW,eAAe,SAAS;AAEhD,UAAI,KAAK,sBAAsB,QAAW;AACxC,mBAAW,eAAe,SAAS,IAAI,KAAK;AAAA,MAC7C;AAAA,IACF;AAID,UAAM,UAAU,KAAK,UAAU,UAAU;AAIzC,QAAI,WAAW,IAAI,MAAM,GAAG;AAC1B,YAAM,aAAa,WAAW,IAAI,MAAM;AAExC,UAAI,WAAW,QAAQ,SAAS;AAC9B,eAAO,WAAW;AAAA,MAC1B,WAAiB,OAAO,eAAe,GAAG;AAKlC,cAAM,IAAI;AAAA,UACR;AAAA,QAED;AAAA,MACF;AAAA,IACF;AAID,QAAI;AACJ,UAAM,SAAS,KAAK;AACpB,UAAM,WAAW,OAAO;AAIxB,UAAM,kBAAkB,KAAK,WAAW,QAAQ,QAAQ,EACrD,KAAK,CAAC,YAAY;AACjB,eAAS;AAET,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,eAAO,WAAW,MAAM,IAAI,EAAE,SAAS,OAAQ;AAE/C,eAAO,YAAY,EAAE,MAAM,UAAU,IAAI,QAAQ,YAAY,UAAU,CAAC,MAAM,CAAC;AAAA,MAGzF,CAAS;AAAA,IACT,CAAO,EACA,KAAK,CAAC,YAAY,KAAK,gBAAgB,QAAQ,QAAQ,CAAC;AAI3D,oBACG,MAAM,MAAM,IAAI,EAChB,KAAK,MAAM;AACV,UAAI,UAAU,QAAQ;AACpB,aAAK,aAAa,QAAQ,MAAM;AAAA,MAGjC;AAAA,IACT,CAAO;AAGH,eAAW,IAAI,QAAQ;AAAA,MACrB,KAAK;AAAA,MACL,SAAS;AAAA,IACf,CAAK;AAED,WAAO;AAAA,EACR;AAAA,EAED,gBAAgB,cAAc;AAC5B,UAAM,WAAW,IAAIC,qBAAgB;AAErC,QAAI,aAAa,OAAO;AACtB,eAAS,SAAS,IAAIC,MAAe,gBAAC,aAAa,MAAM,OAAO,CAAC,CAAC;AAAA,IACnE;AAED,aAAS,IAAI,GAAG,IAAI,aAAa,WAAW,QAAQ,KAAK;AACvD,YAAM,YAAY,aAAa,WAAW,CAAC;AAC3C,YAAM,OAAO,UAAU;AACvB,YAAM,QAAQ,UAAU;AACxB,YAAM,WAAW,UAAU;AAE3B,eAAS,aAAa,MAAM,IAAIA,MAAAA,gBAAgB,OAAO,QAAQ,CAAC;AAAA,IACjE;AAED,WAAO;AAAA,EACR;AAAA,EAED,aAAa,KAAK,cAAc;AAC9B,UAAM,SAAS,IAAIF,iBAAW,KAAK,OAAO;AAC1C,WAAO,QAAQ,KAAK,WAAW;AAC/B,WAAO,gBAAgB,YAAY;AACnC,WAAO,mBAAmB,KAAK,eAAe;AAE9C,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,aAAO,KAAK,KAAK,SAAS,QAAW,MAAM;AAAA,IACjD,CAAK;AAAA,EACF;AAAA,EAED,UAAU;AACR,SAAK,aAAc;AAEnB,WAAO;AAAA,EACR;AAAA,EAED,eAAe;AACb,QAAI,KAAK;AAAgB,aAAO,KAAK;AAErC,UAAM,QAAQ,OAAO,gBAAgB,YAAY,KAAK,cAAc,SAAS;AAC7E,UAAM,mBAAmB,CAAE;AAE3B,QAAI,OAAO;AACT,uBAAiB,KAAK,KAAK,aAAa,oBAAoB,MAAM,CAAC;AAAA,IACzE,OAAW;AACL,uBAAiB,KAAK,KAAK,aAAa,yBAAyB,MAAM,CAAC;AACxE,uBAAiB,KAAK,KAAK,aAAa,sBAAsB,aAAa,CAAC;AAAA,IAC7E;AAED,SAAK,iBAAiB,QAAQ,IAAI,gBAAgB,EAAE,KAAK,CAAC,cAAc;AACtE,YAAM,YAAY,UAAU,CAAC;AAE7B,UAAI,CAAC,OAAO;AACV,aAAK,cAAc,aAAa,UAAU,CAAC;AAAA,MAC5C;AAED,YAAM,KAAK,YAAY,SAAU;AAEjC,YAAM,OAAO;AAAA,QACX;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,GAAG,UAAU,GAAG,QAAQ,GAAG,IAAI,GAAG,GAAG,YAAY,GAAG,CAAC;AAAA,MAC7D,EAAQ,KAAK,IAAI;AAEX,WAAK,kBAAkB,IAAI,gBAAgB,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC;AAAA,IACjE,CAAK;AAED,WAAO,KAAK;AAAA,EACb;AAAA,EAED,WAAW,QAAQ,UAAU;AAC3B,WAAO,KAAK,eAAe,KAAK,MAAM;AACpC,UAAI,KAAK,WAAW,SAAS,KAAK,aAAa;AAC7C,cAAMG,UAAS,IAAI,OAAO,KAAK,eAAe;AAE9C,QAAAA,QAAO,aAAa,CAAE;AACtB,QAAAA,QAAO,aAAa,CAAE;AACtB,QAAAA,QAAO,YAAY;AAEnB,QAAAA,QAAO,YAAY,EAAE,MAAM,QAAQ,eAAe,KAAK,eAAe;AAEtE,QAAAA,QAAO,YAAY,SAAU,GAAG;AAC9B,gBAAM,UAAU,EAAE;AAElB,kBAAQ,QAAQ,MAAI;AAAA,YAClB,KAAK;AACH,cAAAA,QAAO,WAAW,QAAQ,EAAE,EAAE,QAAQ,OAAO;AAC7C;AAAA,YAEF,KAAK;AACH,cAAAA,QAAO,WAAW,QAAQ,EAAE,EAAE,OAAO,OAAO;AAC5C;AAAA,YAEF;AACE,sBAAQ,MAAM,6CAA6C,QAAQ,OAAO,GAAG;AAAA,UAChF;AAAA,QACF;AAED,aAAK,WAAW,KAAKA,OAAM;AAAA,MACnC,OAAa;AACL,aAAK,WAAW,KAAK,SAAU,GAAG,GAAG;AACnC,iBAAO,EAAE,YAAY,EAAE,YAAY,KAAK;AAAA,QAClD,CAAS;AAAA,MACF;AAED,YAAM,SAAS,KAAK,WAAW,KAAK,WAAW,SAAS,CAAC;AACzD,aAAO,WAAW,MAAM,IAAI;AAC5B,aAAO,aAAa;AACpB,aAAO;AAAA,IACb,CAAK;AAAA,EACF;AAAA,EAED,aAAa,QAAQ,QAAQ;AAC3B,WAAO,aAAa,OAAO,WAAW,MAAM;AAC5C,WAAO,OAAO,WAAW,MAAM;AAC/B,WAAO,OAAO,WAAW,MAAM;AAAA,EAChC;AAAA,EAED,QAAQ;AACN,YAAQ;AAAA,MACN;AAAA,MACA,KAAK,WAAW,IAAI,CAAC,WAAW,OAAO,SAAS;AAAA,IACjD;AAAA,EACF;AAAA,EAED,UAAU;AACR,aAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,EAAE,GAAG;AAC/C,WAAK,WAAW,CAAC,EAAE,UAAW;AAAA,IAC/B;AAED,SAAK,WAAW,SAAS;AAEzB,WAAO;AAAA,EACR;AACH;AAIA,SAAS,cAAc;AACrB,MAAI;AACJ,MAAI;AAEJ,cAAY,SAAU,GAAG;AACvB,UAAM,UAAU,EAAE;AAElB,YAAQ,QAAQ,MAAI;AAAA,MAClB,KAAK;AACH,wBAAgB,QAAQ;AACxB,yBAAiB,IAAI,QAAQ,SAAU,SAAsB;AAC3D,wBAAc,iBAAiB,SAAU,OAAO;AAE9C,oBAAQ,EAAE,OAAc;AAAA,UACzB;AAED,6BAAmB,aAAa;AAAA,QAC1C,CAAS;AACD;AAAA,MAEF,KAAK;AACH,cAAM,SAAS,QAAQ;AACvB,cAAM,aAAa,QAAQ;AAC3B,uBAAe,KAAK,CAACC,YAAW;AAC9B,gBAAM,QAAQA,QAAO;AACrB,gBAAM,UAAU,IAAI,MAAM,QAAS;AACnC,gBAAM,gBAAgB,IAAI,MAAM,cAAe;AAC/C,wBAAc,KAAK,IAAI,UAAU,MAAM,GAAG,OAAO,UAAU;AAE3D,cAAI;AACF,kBAAM,WAAW,eAAe,OAAO,SAAS,eAAe,UAAU;AAEzE,kBAAM,UAAU,SAAS,WAAW,IAAI,CAAC,SAAS,KAAK,MAAM,MAAM;AAEnE,gBAAI,SAAS;AAAO,sBAAQ,KAAK,SAAS,MAAM,MAAM,MAAM;AAE5D,iBAAK,YAAY,EAAE,MAAM,UAAU,IAAI,QAAQ,IAAI,SAAU,GAAE,OAAO;AAAA,UACvE,SAAQ,OAAP;AACA,oBAAQ,MAAM,KAAK;AAEnB,iBAAK,YAAY,EAAE,MAAM,SAAS,IAAI,QAAQ,IAAI,OAAO,MAAM,QAAO,CAAE;AAAA,UACpF,UAAoB;AACR,kBAAM,QAAQ,aAAa;AAC3B,kBAAM,QAAQ,OAAO;AAAA,UACtB;AAAA,QACX,CAAS;AACD;AAAA,IACH;AAAA,EACF;AAED,WAAS,eAAe,OAAO,SAAS,eAAe,YAAY;AACjE,UAAM,eAAe,WAAW;AAChC,UAAM,iBAAiB,WAAW;AAElC,QAAI;AACJ,QAAI;AAEJ,UAAM,eAAe,QAAQ,uBAAuB,aAAa;AAEjE,QAAI,iBAAiB,MAAM,iBAAiB;AAC1C,sBAAgB,IAAI,MAAM,KAAM;AAChC,uBAAiB,QAAQ,mBAAmB,eAAe,aAAa;AAAA,IAC9E,WAAe,iBAAiB,MAAM,aAAa;AAC7C,sBAAgB,IAAI,MAAM,WAAY;AACtC,uBAAiB,QAAQ,yBAAyB,eAAe,aAAa;AAAA,IACpF,OAAW;AACL,YAAM,IAAI,MAAM,8CAA8C;AAAA,IAC/D;AAED,QAAI,CAAC,eAAe,GAAE,KAAM,cAAc,QAAQ,GAAG;AACnD,YAAM,IAAI,MAAM,yCAAyC,eAAe,UAAS,CAAE;AAAA,IACpF;AAED,UAAM,WAAW,EAAE,OAAO,MAAM,YAAY,CAAA,EAAI;AAGhD,eAAW,iBAAiB,cAAc;AACxC,YAAM,gBAAgB,KAAK,eAAe,aAAa,CAAC;AAExD,UAAI;AACJ,UAAI;AAMJ,UAAI,WAAW,cAAc;AAC3B,sBAAc,aAAa,aAAa;AACxC,oBAAY,QAAQ,uBAAuB,eAAe,WAAW;AAAA,MAC7E,OAAa;AACL,sBAAc,QAAQ,eAAe,eAAe,MAAM,aAAa,aAAa,CAAC,CAAC;AAEtF,YAAI,gBAAgB;AAAI;AAExB,oBAAY,QAAQ,aAAa,eAAe,WAAW;AAAA,MAC5D;AAED,eAAS,WAAW,KAAK,gBAAgB,OAAO,SAAS,eAAe,eAAe,eAAe,SAAS,CAAC;AAAA,IACjH;AAGD,QAAI,iBAAiB,MAAM,iBAAiB;AAC1C,eAAS,QAAQ,YAAY,OAAO,SAAS,aAAa;AAAA,IAC3D;AAED,UAAM,QAAQ,aAAa;AAE3B,WAAO;AAAA,EACR;AAED,WAAS,YAAY,OAAO,SAAS,eAAe;AAClD,UAAM,WAAW,cAAc,UAAW;AAC1C,UAAM,aAAa,WAAW;AAC9B,UAAM,aAAa,aAAa;AAEhC,UAAM,MAAM,MAAM,QAAQ,UAAU;AACpC,YAAQ,wBAAwB,eAAe,YAAY,GAAG;AAC9D,UAAM,QAAQ,IAAI,YAAY,MAAM,QAAQ,QAAQ,KAAK,UAAU,EAAE,MAAO;AAC5E,UAAM,MAAM,GAAG;AAEf,WAAO,EAAE,OAAO,OAAO,UAAU,EAAG;AAAA,EACrC;AAED,WAAS,gBAAgB,OAAO,SAAS,eAAe,eAAe,eAAe,WAAW;AAC/F,UAAM,gBAAgB,UAAU,eAAgB;AAChD,UAAM,YAAY,cAAc,WAAY;AAC5C,UAAM,YAAY,YAAY;AAC9B,UAAM,aAAa,YAAY,cAAc;AAC7C,UAAM,WAAW,iBAAiB,OAAO,aAAa;AAEtD,UAAM,MAAM,MAAM,QAAQ,UAAU;AACpC,YAAQ,kCAAkC,eAAe,WAAW,UAAU,YAAY,GAAG;AAC7F,UAAM,QAAQ,IAAI,cAAc,MAAM,QAAQ,QAAQ,KAAK,SAAS,EAAE,MAAO;AAC7E,UAAM,MAAM,GAAG;AAEf,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,MACA,UAAU;AAAA,IACX;AAAA,EACF;AAED,WAAS,iBAAiB,OAAO,eAAe;AAC9C,YAAQ,eAAa;AAAA,MACnB,KAAK;AACH,eAAO,MAAM;AAAA,MACf,KAAK;AACH,eAAO,MAAM;AAAA,MACf,KAAK;AACH,eAAO,MAAM;AAAA,MACf,KAAK;AACH,eAAO,MAAM;AAAA,MACf,KAAK;AACH,eAAO,MAAM;AAAA,MACf,KAAK;AACH,eAAO,MAAM;AAAA,MACf,KAAK;AACH,eAAO,MAAM;AAAA,IAChB;AAAA,EACF;AACH;;"}