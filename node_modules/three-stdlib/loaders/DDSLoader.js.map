{"version": 3, "file": "DDSLoader.js", "sources": ["../../src/loaders/DDSLoader.js"], "sourcesContent": ["import {\n  CompressedTextureLoader,\n  RGBAFormat,\n  RGBA_S3TC_DXT3_Format,\n  RGBA_S3TC_DXT5_Format,\n  RGB_ETC1_Format,\n  RGB_S3TC_DXT1_Format,\n} from 'three'\n\nclass DDSLoader extends CompressedTextureLoader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  parse(buffer, loadMipmaps) {\n    const dds = { mipmaps: [], width: 0, height: 0, format: null, mipmapCount: 1 }\n\n    // Adapted from @toji's DDS utils\n    // https://github.com/toji/webgl-texture-utils/blob/master/texture-util/dds.js\n\n    // All values and structures referenced from:\n    // http://msdn.microsoft.com/en-us/library/bb943991.aspx/\n\n    const DDS_MAGIC = 0x20534444\n\n    // let DDSD_CAPS = 0x1;\n    // let DDSD_HEIGHT = 0x2;\n    // let DDSD_WIDTH = 0x4;\n    // let DDSD_PITCH = 0x8;\n    // let DDSD_PIXELFORMAT = 0x1000;\n    const DDSD_MIPMAPCOUNT = 0x20000\n    // let DDSD_LINEARSIZE = 0x80000;\n    // let DDSD_DEPTH = 0x800000;\n\n    // let DDSCAPS_COMPLEX = 0x8;\n    // let DDSCAPS_MIPMAP = 0x400000;\n    // let DDSCAPS_TEXTURE = 0x1000;\n\n    const DDSCAPS2_CUBEMAP = 0x200\n    const DDSCAPS2_CUBEMAP_POSITIVEX = 0x400\n    const DDSCAPS2_CUBEMAP_NEGATIVEX = 0x800\n    const DDSCAPS2_CUBEMAP_POSITIVEY = 0x1000\n    const DDSCAPS2_CUBEMAP_NEGATIVEY = 0x2000\n    const DDSCAPS2_CUBEMAP_POSITIVEZ = 0x4000\n    const DDSCAPS2_CUBEMAP_NEGATIVEZ = 0x8000\n    // let DDSCAPS2_VOLUME = 0x200000;\n\n    // let DDPF_ALPHAPIXELS = 0x1;\n    // let DDPF_ALPHA = 0x2;\n    const DDPF_FOURCC = 0x4\n    // let DDPF_RGB = 0x40;\n    // let DDPF_YUV = 0x200;\n    // let DDPF_LUMINANCE = 0x20000;\n\n    function fourCCToInt32(value) {\n      return (\n        value.charCodeAt(0) + (value.charCodeAt(1) << 8) + (value.charCodeAt(2) << 16) + (value.charCodeAt(3) << 24)\n      )\n    }\n\n    function int32ToFourCC(value) {\n      return String.fromCharCode(value & 0xff, (value >> 8) & 0xff, (value >> 16) & 0xff, (value >> 24) & 0xff)\n    }\n\n    function loadARGBMip(buffer, dataOffset, width, height) {\n      const dataLength = width * height * 4\n      const srcBuffer = new Uint8Array(buffer, dataOffset, dataLength)\n      const byteArray = new Uint8Array(dataLength)\n      let dst = 0\n      let src = 0\n      for (let y = 0; y < height; y++) {\n        for (let x = 0; x < width; x++) {\n          const b = srcBuffer[src]\n          src++\n          const g = srcBuffer[src]\n          src++\n          const r = srcBuffer[src]\n          src++\n          const a = srcBuffer[src]\n          src++\n          byteArray[dst] = r\n          dst++ //r\n          byteArray[dst] = g\n          dst++ //g\n          byteArray[dst] = b\n          dst++ //b\n          byteArray[dst] = a\n          dst++ //a\n        }\n      }\n\n      return byteArray\n    }\n\n    const FOURCC_DXT1 = fourCCToInt32('DXT1')\n    const FOURCC_DXT3 = fourCCToInt32('DXT3')\n    const FOURCC_DXT5 = fourCCToInt32('DXT5')\n    const FOURCC_ETC1 = fourCCToInt32('ETC1')\n\n    const headerLengthInt = 31 // The header length in 32 bit ints\n\n    // Offsets into the header array\n\n    const off_magic = 0\n\n    const off_size = 1\n    const off_flags = 2\n    const off_height = 3\n    const off_width = 4\n\n    const off_mipmapCount = 7\n\n    const off_pfFlags = 20\n    const off_pfFourCC = 21\n    const off_RGBBitCount = 22\n    const off_RBitMask = 23\n    const off_GBitMask = 24\n    const off_BBitMask = 25\n    const off_ABitMask = 26\n\n    // let off_caps = 27;\n    const off_caps2 = 28\n    // let off_caps3 = 29;\n    // let off_caps4 = 30;\n\n    // Parse header\n\n    const header = new Int32Array(buffer, 0, headerLengthInt)\n\n    if (header[off_magic] !== DDS_MAGIC) {\n      console.error('THREE.DDSLoader.parse: Invalid magic number in DDS header.')\n      return dds\n    }\n\n    if (!header[off_pfFlags] & DDPF_FOURCC) {\n      console.error('THREE.DDSLoader.parse: Unsupported format, must contain a FourCC code.')\n      return dds\n    }\n\n    let blockBytes\n\n    const fourCC = header[off_pfFourCC]\n\n    let isRGBAUncompressed = false\n\n    switch (fourCC) {\n      case FOURCC_DXT1:\n        blockBytes = 8\n        dds.format = RGB_S3TC_DXT1_Format\n        break\n\n      case FOURCC_DXT3:\n        blockBytes = 16\n        dds.format = RGBA_S3TC_DXT3_Format\n        break\n\n      case FOURCC_DXT5:\n        blockBytes = 16\n        dds.format = RGBA_S3TC_DXT5_Format\n        break\n\n      case FOURCC_ETC1:\n        blockBytes = 8\n        dds.format = RGB_ETC1_Format\n        break\n\n      default:\n        if (\n          header[off_RGBBitCount] === 32 &&\n          header[off_RBitMask] & 0xff0000 &&\n          header[off_GBitMask] & 0xff00 &&\n          header[off_BBitMask] & 0xff &&\n          header[off_ABitMask] & 0xff000000\n        ) {\n          isRGBAUncompressed = true\n          blockBytes = 64\n          dds.format = RGBAFormat\n        } else {\n          console.error('THREE.DDSLoader.parse: Unsupported FourCC code ', int32ToFourCC(fourCC))\n          return dds\n        }\n    }\n\n    dds.mipmapCount = 1\n\n    if (header[off_flags] & DDSD_MIPMAPCOUNT && loadMipmaps !== false) {\n      dds.mipmapCount = Math.max(1, header[off_mipmapCount])\n    }\n\n    const caps2 = header[off_caps2]\n    dds.isCubemap = caps2 & DDSCAPS2_CUBEMAP ? true : false\n    if (\n      dds.isCubemap &&\n      (!(caps2 & DDSCAPS2_CUBEMAP_POSITIVEX) ||\n        !(caps2 & DDSCAPS2_CUBEMAP_NEGATIVEX) ||\n        !(caps2 & DDSCAPS2_CUBEMAP_POSITIVEY) ||\n        !(caps2 & DDSCAPS2_CUBEMAP_NEGATIVEY) ||\n        !(caps2 & DDSCAPS2_CUBEMAP_POSITIVEZ) ||\n        !(caps2 & DDSCAPS2_CUBEMAP_NEGATIVEZ))\n    ) {\n      console.error('THREE.DDSLoader.parse: Incomplete cubemap faces')\n      return dds\n    }\n\n    dds.width = header[off_width]\n    dds.height = header[off_height]\n\n    let dataOffset = header[off_size] + 4\n\n    // Extract mipmaps buffers\n\n    const faces = dds.isCubemap ? 6 : 1\n\n    for (let face = 0; face < faces; face++) {\n      let width = dds.width\n      let height = dds.height\n\n      for (let i = 0; i < dds.mipmapCount; i++) {\n        let byteArray, dataLength\n\n        if (isRGBAUncompressed) {\n          byteArray = loadARGBMip(buffer, dataOffset, width, height)\n          dataLength = byteArray.length\n        } else {\n          dataLength = (((Math.max(4, width) / 4) * Math.max(4, height)) / 4) * blockBytes\n          byteArray = new Uint8Array(buffer, dataOffset, dataLength)\n        }\n\n        const mipmap = { data: byteArray, width: width, height: height }\n        dds.mipmaps.push(mipmap)\n\n        dataOffset += dataLength\n\n        width = Math.max(width >> 1, 1)\n        height = Math.max(height >> 1, 1)\n      }\n    }\n\n    return dds\n  }\n}\n\nexport { DDSLoader }\n"], "names": ["buffer", "dataOffset"], "mappings": ";AASA,MAAM,kBAAkB,wBAAwB;AAAA,EAC9C,YAAY,SAAS;AACnB,UAAM,OAAO;AAAA,EACd;AAAA,EAED,MAAM,QAAQ,aAAa;AACzB,UAAM,MAAM,EAAE,SAAS,CAAA,GAAI,OAAO,GAAG,QAAQ,GAAG,QAAQ,MAAM,aAAa,EAAG;AAQ9E,UAAM,YAAY;AAOlB,UAAM,mBAAmB;AAQzB,UAAM,mBAAmB;AACzB,UAAM,6BAA6B;AACnC,UAAM,6BAA6B;AACnC,UAAM,6BAA6B;AACnC,UAAM,6BAA6B;AACnC,UAAM,6BAA6B;AACnC,UAAM,6BAA6B;AAKnC,UAAM,cAAc;AAKpB,aAAS,cAAc,OAAO;AAC5B,aACE,MAAM,WAAW,CAAC,KAAK,MAAM,WAAW,CAAC,KAAK,MAAM,MAAM,WAAW,CAAC,KAAK,OAAO,MAAM,WAAW,CAAC,KAAK;AAAA,IAE5G;AAED,aAAS,cAAc,OAAO;AAC5B,aAAO,OAAO,aAAa,QAAQ,KAAO,SAAS,IAAK,KAAO,SAAS,KAAM,KAAO,SAAS,KAAM,GAAI;AAAA,IACzG;AAED,aAAS,YAAYA,SAAQC,aAAY,OAAO,QAAQ;AACtD,YAAM,aAAa,QAAQ,SAAS;AACpC,YAAM,YAAY,IAAI,WAAWD,SAAQC,aAAY,UAAU;AAC/D,YAAM,YAAY,IAAI,WAAW,UAAU;AAC3C,UAAI,MAAM;AACV,UAAI,MAAM;AACV,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,iBAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,gBAAM,IAAI,UAAU,GAAG;AACvB;AACA,gBAAM,IAAI,UAAU,GAAG;AACvB;AACA,gBAAM,IAAI,UAAU,GAAG;AACvB;AACA,gBAAM,IAAI,UAAU,GAAG;AACvB;AACA,oBAAU,GAAG,IAAI;AACjB;AACA,oBAAU,GAAG,IAAI;AACjB;AACA,oBAAU,GAAG,IAAI;AACjB;AACA,oBAAU,GAAG,IAAI;AACjB;AAAA,QACD;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,UAAM,cAAc,cAAc,MAAM;AACxC,UAAM,cAAc,cAAc,MAAM;AACxC,UAAM,cAAc,cAAc,MAAM;AACxC,UAAM,cAAc,cAAc,MAAM;AAExC,UAAM,kBAAkB;AAIxB,UAAM,YAAY;AAElB,UAAM,WAAW;AACjB,UAAM,YAAY;AAClB,UAAM,aAAa;AACnB,UAAM,YAAY;AAElB,UAAM,kBAAkB;AAExB,UAAM,cAAc;AACpB,UAAM,eAAe;AACrB,UAAM,kBAAkB;AACxB,UAAM,eAAe;AACrB,UAAM,eAAe;AACrB,UAAM,eAAe;AACrB,UAAM,eAAe;AAGrB,UAAM,YAAY;AAMlB,UAAM,SAAS,IAAI,WAAW,QAAQ,GAAG,eAAe;AAExD,QAAI,OAAO,SAAS,MAAM,WAAW;AACnC,cAAQ,MAAM,4DAA4D;AAC1E,aAAO;AAAA,IACR;AAED,QAAI,CAAC,OAAO,WAAW,IAAI,aAAa;AACtC,cAAQ,MAAM,wEAAwE;AACtF,aAAO;AAAA,IACR;AAED,QAAI;AAEJ,UAAM,SAAS,OAAO,YAAY;AAElC,QAAI,qBAAqB;AAEzB,YAAQ,QAAM;AAAA,MACZ,KAAK;AACH,qBAAa;AACb,YAAI,SAAS;AACb;AAAA,MAEF,KAAK;AACH,qBAAa;AACb,YAAI,SAAS;AACb;AAAA,MAEF,KAAK;AACH,qBAAa;AACb,YAAI,SAAS;AACb;AAAA,MAEF,KAAK;AACH,qBAAa;AACb,YAAI,SAAS;AACb;AAAA,MAEF;AACE,YACE,OAAO,eAAe,MAAM,MAC5B,OAAO,YAAY,IAAI,YACvB,OAAO,YAAY,IAAI,SACvB,OAAO,YAAY,IAAI,OACvB,OAAO,YAAY,IAAI,YACvB;AACA,+BAAqB;AACrB,uBAAa;AACb,cAAI,SAAS;AAAA,QACvB,OAAe;AACL,kBAAQ,MAAM,mDAAmD,cAAc,MAAM,CAAC;AACtF,iBAAO;AAAA,QACR;AAAA,IACJ;AAED,QAAI,cAAc;AAElB,QAAI,OAAO,SAAS,IAAI,oBAAoB,gBAAgB,OAAO;AACjE,UAAI,cAAc,KAAK,IAAI,GAAG,OAAO,eAAe,CAAC;AAAA,IACtD;AAED,UAAM,QAAQ,OAAO,SAAS;AAC9B,QAAI,YAAY,QAAQ,mBAAmB,OAAO;AAClD,QACE,IAAI,cACH,EAAE,QAAQ,+BACT,EAAE,QAAQ,+BACV,EAAE,QAAQ,+BACV,EAAE,QAAQ,+BACV,EAAE,QAAQ,+BACV,EAAE,QAAQ,8BACZ;AACA,cAAQ,MAAM,iDAAiD;AAC/D,aAAO;AAAA,IACR;AAED,QAAI,QAAQ,OAAO,SAAS;AAC5B,QAAI,SAAS,OAAO,UAAU;AAE9B,QAAI,aAAa,OAAO,QAAQ,IAAI;AAIpC,UAAM,QAAQ,IAAI,YAAY,IAAI;AAElC,aAAS,OAAO,GAAG,OAAO,OAAO,QAAQ;AACvC,UAAI,QAAQ,IAAI;AAChB,UAAI,SAAS,IAAI;AAEjB,eAAS,IAAI,GAAG,IAAI,IAAI,aAAa,KAAK;AACxC,YAAI,WAAW;AAEf,YAAI,oBAAoB;AACtB,sBAAY,YAAY,QAAQ,YAAY,OAAO,MAAM;AACzD,uBAAa,UAAU;AAAA,QACjC,OAAe;AACL,uBAAgB,KAAK,IAAI,GAAG,KAAK,IAAI,IAAK,KAAK,IAAI,GAAG,MAAM,IAAK,IAAK;AACtE,sBAAY,IAAI,WAAW,QAAQ,YAAY,UAAU;AAAA,QAC1D;AAED,cAAM,SAAS,EAAE,MAAM,WAAW,OAAc,OAAgB;AAChE,YAAI,QAAQ,KAAK,MAAM;AAEvB,sBAAc;AAEd,gBAAQ,KAAK,IAAI,SAAS,GAAG,CAAC;AAC9B,iBAAS,KAAK,IAAI,UAAU,GAAG,CAAC;AAAA,MACjC;AAAA,IACF;AAED,WAAO;AAAA,EACR;AACH;"}