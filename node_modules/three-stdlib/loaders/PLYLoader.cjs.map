{"version": 3, "file": "PLYLoader.cjs", "sources": ["../../src/loaders/PLYLoader.js"], "sourcesContent": ["import { BufferGeometry, FileLoader, Float32BufferAttribute, Loader, LoaderUtils } from 'three'\nimport { decodeText } from '../_polyfill/LoaderUtils'\n\n/**\n * Description: A THREE loader for PLY ASCII files (known as the Polygon\n * File Format or the Stanford Triangle Format).\n *\n * Limitations: ASCII decoding assumes file is UTF-8.\n *\n * Usage:\n *\tconst loader = new PLYLoader();\n *\tloader.load('./models/ply/ascii/dolphins.ply', function (geometry) {\n *\n *\t\tscene.add( new THREE.Mesh( geometry ) );\n *\n *\t} );\n *\n * If the PLY file uses non standard property names, they can be mapped while\n * loading. For example, the following maps the properties\n * “diffuse_(red|green|blue)” in the file to standard color names.\n *\n * loader.setPropertyNameMapping( {\n *\tdiffuse_red: 'red',\n *\tdiffuse_green: 'green',\n *\tdiffuse_blue: 'blue'\n * } );\n *\n */\n\nclass PLYLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n\n    this.propertyNameMapping = {}\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(this.manager)\n    loader.setPath(this.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(this.requestHeader)\n    loader.setWithCredentials(this.withCredentials)\n    loader.load(\n      url,\n      function (text) {\n        try {\n          onLoad(scope.parse(text))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  setPropertyNameMapping(mapping) {\n    this.propertyNameMapping = mapping\n  }\n\n  parse(data) {\n    function parseHeader(data) {\n      const patternHeader = /ply([\\s\\S]*)end_header\\r?\\n/\n      let headerText = ''\n      let headerLength = 0\n      const result = patternHeader.exec(data)\n\n      if (result !== null) {\n        headerText = result[1]\n        headerLength = new Blob([result[0]]).size\n      }\n\n      const header = {\n        comments: [],\n        elements: [],\n        headerLength: headerLength,\n        objInfo: '',\n      }\n\n      const lines = headerText.split('\\n')\n      let currentElement\n\n      function make_ply_element_property(propertValues, propertyNameMapping) {\n        const property = { type: propertValues[0] }\n\n        if (property.type === 'list') {\n          property.name = propertValues[3]\n          property.countType = propertValues[1]\n          property.itemType = propertValues[2]\n        } else {\n          property.name = propertValues[1]\n        }\n\n        if (property.name in propertyNameMapping) {\n          property.name = propertyNameMapping[property.name]\n        }\n\n        return property\n      }\n\n      for (let i = 0; i < lines.length; i++) {\n        let line = lines[i]\n        line = line.trim()\n\n        if (line === '') continue\n\n        const lineValues = line.split(/\\s+/)\n        const lineType = lineValues.shift()\n        line = lineValues.join(' ')\n\n        switch (lineType) {\n          case 'format':\n            header.format = lineValues[0]\n            header.version = lineValues[1]\n\n            break\n\n          case 'comment':\n            header.comments.push(line)\n\n            break\n\n          case 'element':\n            if (currentElement !== undefined) {\n              header.elements.push(currentElement)\n            }\n\n            currentElement = {}\n            currentElement.name = lineValues[0]\n            currentElement.count = parseInt(lineValues[1])\n            currentElement.properties = []\n\n            break\n\n          case 'property':\n            currentElement.properties.push(make_ply_element_property(lineValues, scope.propertyNameMapping))\n\n            break\n\n          case 'obj_info':\n            header.objInfo = line\n\n            break\n\n          default:\n            console.log('unhandled', lineType, lineValues)\n        }\n      }\n\n      if (currentElement !== undefined) {\n        header.elements.push(currentElement)\n      }\n\n      return header\n    }\n\n    function parseASCIINumber(n, type) {\n      switch (type) {\n        case 'char':\n        case 'uchar':\n        case 'short':\n        case 'ushort':\n        case 'int':\n        case 'uint':\n        case 'int8':\n        case 'uint8':\n        case 'int16':\n        case 'uint16':\n        case 'int32':\n        case 'uint32':\n          return parseInt(n)\n\n        case 'float':\n        case 'double':\n        case 'float32':\n        case 'float64':\n          return parseFloat(n)\n      }\n    }\n\n    function parseASCIIElement(properties, line) {\n      const values = line.split(/\\s+/)\n\n      const element = {}\n\n      for (let i = 0; i < properties.length; i++) {\n        if (properties[i].type === 'list') {\n          const list = []\n          const n = parseASCIINumber(values.shift(), properties[i].countType)\n\n          for (let j = 0; j < n; j++) {\n            list.push(parseASCIINumber(values.shift(), properties[i].itemType))\n          }\n\n          element[properties[i].name] = list\n        } else {\n          element[properties[i].name] = parseASCIINumber(values.shift(), properties[i].type)\n        }\n      }\n\n      return element\n    }\n\n    function parseASCII(data, header) {\n      // PLY ascii format specification, as per http://en.wikipedia.org/wiki/PLY_(file_format)\n\n      const buffer = {\n        indices: [],\n        vertices: [],\n        normals: [],\n        uvs: [],\n        faceVertexUvs: [],\n        colors: [],\n      }\n\n      let result\n\n      const patternBody = /end_header\\s([\\s\\S]*)$/\n      let body = ''\n      if ((result = patternBody.exec(data)) !== null) {\n        body = result[1]\n      }\n\n      const lines = body.split('\\n')\n      let currentElement = 0\n      let currentElementCount = 0\n\n      for (let i = 0; i < lines.length; i++) {\n        let line = lines[i]\n        line = line.trim()\n        if (line === '') {\n          continue\n        }\n\n        if (currentElementCount >= header.elements[currentElement].count) {\n          currentElement++\n          currentElementCount = 0\n        }\n\n        const element = parseASCIIElement(header.elements[currentElement].properties, line)\n\n        handleElement(buffer, header.elements[currentElement].name, element)\n\n        currentElementCount++\n      }\n\n      return postProcess(buffer)\n    }\n\n    function postProcess(buffer) {\n      let geometry = new BufferGeometry()\n\n      // mandatory buffer data\n\n      if (buffer.indices.length > 0) {\n        geometry.setIndex(buffer.indices)\n      }\n\n      geometry.setAttribute('position', new Float32BufferAttribute(buffer.vertices, 3))\n\n      // optional buffer data\n\n      if (buffer.normals.length > 0) {\n        geometry.setAttribute('normal', new Float32BufferAttribute(buffer.normals, 3))\n      }\n\n      if (buffer.uvs.length > 0) {\n        geometry.setAttribute('uv', new Float32BufferAttribute(buffer.uvs, 2))\n      }\n\n      if (buffer.colors.length > 0) {\n        geometry.setAttribute('color', new Float32BufferAttribute(buffer.colors, 3))\n      }\n\n      if (buffer.faceVertexUvs.length > 0) {\n        geometry = geometry.toNonIndexed()\n        geometry.setAttribute('uv', new Float32BufferAttribute(buffer.faceVertexUvs, 2))\n      }\n\n      geometry.computeBoundingSphere()\n\n      return geometry\n    }\n\n    function handleElement(buffer, elementName, element) {\n      if (elementName === 'vertex') {\n        buffer.vertices.push(element.x, element.y, element.z)\n\n        if ('nx' in element && 'ny' in element && 'nz' in element) {\n          buffer.normals.push(element.nx, element.ny, element.nz)\n        }\n\n        if ('s' in element && 't' in element) {\n          buffer.uvs.push(element.s, element.t)\n        }\n\n        if ('red' in element && 'green' in element && 'blue' in element) {\n          buffer.colors.push(element.red / 255.0, element.green / 255.0, element.blue / 255.0)\n        }\n      } else if (elementName === 'face') {\n        const vertex_indices = element.vertex_indices || element.vertex_index // issue #9338\n        const texcoord = element.texcoord\n\n        if (vertex_indices.length === 3) {\n          buffer.indices.push(vertex_indices[0], vertex_indices[1], vertex_indices[2])\n\n          if (texcoord && texcoord.length === 6) {\n            buffer.faceVertexUvs.push(texcoord[0], texcoord[1])\n            buffer.faceVertexUvs.push(texcoord[2], texcoord[3])\n            buffer.faceVertexUvs.push(texcoord[4], texcoord[5])\n          }\n        } else if (vertex_indices.length === 4) {\n          buffer.indices.push(vertex_indices[0], vertex_indices[1], vertex_indices[3])\n          buffer.indices.push(vertex_indices[1], vertex_indices[2], vertex_indices[3])\n        }\n      }\n    }\n\n    function binaryRead(dataview, at, type, little_endian) {\n      switch (type) {\n        // corespondences for non-specific length types here match rply:\n        case 'int8':\n        case 'char':\n          return [dataview.getInt8(at), 1]\n        case 'uint8':\n        case 'uchar':\n          return [dataview.getUint8(at), 1]\n        case 'int16':\n        case 'short':\n          return [dataview.getInt16(at, little_endian), 2]\n        case 'uint16':\n        case 'ushort':\n          return [dataview.getUint16(at, little_endian), 2]\n        case 'int32':\n        case 'int':\n          return [dataview.getInt32(at, little_endian), 4]\n        case 'uint32':\n        case 'uint':\n          return [dataview.getUint32(at, little_endian), 4]\n        case 'float32':\n        case 'float':\n          return [dataview.getFloat32(at, little_endian), 4]\n        case 'float64':\n        case 'double':\n          return [dataview.getFloat64(at, little_endian), 8]\n      }\n    }\n\n    function binaryReadElement(dataview, at, properties, little_endian) {\n      const element = {}\n      let result,\n        read = 0\n\n      for (let i = 0; i < properties.length; i++) {\n        if (properties[i].type === 'list') {\n          const list = []\n\n          result = binaryRead(dataview, at + read, properties[i].countType, little_endian)\n          const n = result[0]\n          read += result[1]\n\n          for (let j = 0; j < n; j++) {\n            result = binaryRead(dataview, at + read, properties[i].itemType, little_endian)\n            list.push(result[0])\n            read += result[1]\n          }\n\n          element[properties[i].name] = list\n        } else {\n          result = binaryRead(dataview, at + read, properties[i].type, little_endian)\n          element[properties[i].name] = result[0]\n          read += result[1]\n        }\n      }\n\n      return [element, read]\n    }\n\n    function parseBinary(data, header) {\n      const buffer = {\n        indices: [],\n        vertices: [],\n        normals: [],\n        uvs: [],\n        faceVertexUvs: [],\n        colors: [],\n      }\n\n      const little_endian = header.format === 'binary_little_endian'\n      const body = new DataView(data, header.headerLength)\n      let result,\n        loc = 0\n\n      for (let currentElement = 0; currentElement < header.elements.length; currentElement++) {\n        for (\n          let currentElementCount = 0;\n          currentElementCount < header.elements[currentElement].count;\n          currentElementCount++\n        ) {\n          result = binaryReadElement(body, loc, header.elements[currentElement].properties, little_endian)\n          loc += result[1]\n          const element = result[0]\n\n          handleElement(buffer, header.elements[currentElement].name, element)\n        }\n      }\n\n      return postProcess(buffer)\n    }\n\n    //\n\n    let geometry\n    const scope = this\n\n    if (data instanceof ArrayBuffer) {\n      const text = decodeText(new Uint8Array(data))\n      const header = parseHeader(text)\n\n      geometry = header.format === 'ascii' ? parseASCII(text, header) : parseBinary(data, header)\n    } else {\n      geometry = parseASCII(data, parseHeader(data))\n    }\n\n    return geometry\n  }\n}\n\nexport { PLYLoader }\n"], "names": ["Loader", "<PERSON><PERSON><PERSON><PERSON>", "data", "geometry", "BufferGeometry", "Float32BufferAttribute", "decodeText"], "mappings": ";;;;AA6BA,MAAM,kBAAkBA,MAAAA,OAAO;AAAA,EAC7B,YAAY,SAAS;AACnB,UAAM,OAAO;AAEb,SAAK,sBAAsB,CAAE;AAAA,EAC9B;AAAA,EAED,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,UAAM,QAAQ;AAEd,UAAM,SAAS,IAAIC,iBAAW,KAAK,OAAO;AAC1C,WAAO,QAAQ,KAAK,IAAI;AACxB,WAAO,gBAAgB,aAAa;AACpC,WAAO,iBAAiB,KAAK,aAAa;AAC1C,WAAO,mBAAmB,KAAK,eAAe;AAC9C,WAAO;AAAA,MACL;AAAA,MACA,SAAU,MAAM;AACd,YAAI;AACF,iBAAO,MAAM,MAAM,IAAI,CAAC;AAAA,QACzB,SAAQ,GAAP;AACA,cAAI,SAAS;AACX,oBAAQ,CAAC;AAAA,UACrB,OAAiB;AACL,oBAAQ,MAAM,CAAC;AAAA,UAChB;AAED,gBAAM,QAAQ,UAAU,GAAG;AAAA,QAC5B;AAAA,MACF;AAAA,MACD;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAAA,EAED,uBAAuB,SAAS;AAC9B,SAAK,sBAAsB;AAAA,EAC5B;AAAA,EAED,MAAM,MAAM;AACV,aAAS,YAAYC,OAAM;AACzB,YAAM,gBAAgB;AACtB,UAAI,aAAa;AACjB,UAAI,eAAe;AACnB,YAAM,SAAS,cAAc,KAAKA,KAAI;AAEtC,UAAI,WAAW,MAAM;AACnB,qBAAa,OAAO,CAAC;AACrB,uBAAe,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;AAAA,MACtC;AAED,YAAM,SAAS;AAAA,QACb,UAAU,CAAE;AAAA,QACZ,UAAU,CAAE;AAAA,QACZ;AAAA,QACA,SAAS;AAAA,MACV;AAED,YAAM,QAAQ,WAAW,MAAM,IAAI;AACnC,UAAI;AAEJ,eAAS,0BAA0B,eAAe,qBAAqB;AACrE,cAAM,WAAW,EAAE,MAAM,cAAc,CAAC,EAAG;AAE3C,YAAI,SAAS,SAAS,QAAQ;AAC5B,mBAAS,OAAO,cAAc,CAAC;AAC/B,mBAAS,YAAY,cAAc,CAAC;AACpC,mBAAS,WAAW,cAAc,CAAC;AAAA,QAC7C,OAAe;AACL,mBAAS,OAAO,cAAc,CAAC;AAAA,QAChC;AAED,YAAI,SAAS,QAAQ,qBAAqB;AACxC,mBAAS,OAAO,oBAAoB,SAAS,IAAI;AAAA,QAClD;AAED,eAAO;AAAA,MACR;AAED,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAI,OAAO,MAAM,CAAC;AAClB,eAAO,KAAK,KAAM;AAElB,YAAI,SAAS;AAAI;AAEjB,cAAM,aAAa,KAAK,MAAM,KAAK;AACnC,cAAM,WAAW,WAAW,MAAO;AACnC,eAAO,WAAW,KAAK,GAAG;AAE1B,gBAAQ,UAAQ;AAAA,UACd,KAAK;AACH,mBAAO,SAAS,WAAW,CAAC;AAC5B,mBAAO,UAAU,WAAW,CAAC;AAE7B;AAAA,UAEF,KAAK;AACH,mBAAO,SAAS,KAAK,IAAI;AAEzB;AAAA,UAEF,KAAK;AACH,gBAAI,mBAAmB,QAAW;AAChC,qBAAO,SAAS,KAAK,cAAc;AAAA,YACpC;AAED,6BAAiB,CAAE;AACnB,2BAAe,OAAO,WAAW,CAAC;AAClC,2BAAe,QAAQ,SAAS,WAAW,CAAC,CAAC;AAC7C,2BAAe,aAAa,CAAE;AAE9B;AAAA,UAEF,KAAK;AACH,2BAAe,WAAW,KAAK,0BAA0B,YAAY,MAAM,mBAAmB,CAAC;AAE/F;AAAA,UAEF,KAAK;AACH,mBAAO,UAAU;AAEjB;AAAA,UAEF;AACE,oBAAQ,IAAI,aAAa,UAAU,UAAU;AAAA,QAChD;AAAA,MACF;AAED,UAAI,mBAAmB,QAAW;AAChC,eAAO,SAAS,KAAK,cAAc;AAAA,MACpC;AAED,aAAO;AAAA,IACR;AAED,aAAS,iBAAiB,GAAG,MAAM;AACjC,cAAQ,MAAI;AAAA,QACV,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,SAAS,CAAC;AAAA,QAEnB,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,WAAW,CAAC;AAAA,MACtB;AAAA,IACF;AAED,aAAS,kBAAkB,YAAY,MAAM;AAC3C,YAAM,SAAS,KAAK,MAAM,KAAK;AAE/B,YAAM,UAAU,CAAE;AAElB,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,YAAI,WAAW,CAAC,EAAE,SAAS,QAAQ;AACjC,gBAAM,OAAO,CAAE;AACf,gBAAM,IAAI,iBAAiB,OAAO,MAAO,GAAE,WAAW,CAAC,EAAE,SAAS;AAElE,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,iBAAK,KAAK,iBAAiB,OAAO,MAAO,GAAE,WAAW,CAAC,EAAE,QAAQ,CAAC;AAAA,UACnE;AAED,kBAAQ,WAAW,CAAC,EAAE,IAAI,IAAI;AAAA,QACxC,OAAe;AACL,kBAAQ,WAAW,CAAC,EAAE,IAAI,IAAI,iBAAiB,OAAO,MAAK,GAAI,WAAW,CAAC,EAAE,IAAI;AAAA,QAClF;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,WAAWA,OAAM,QAAQ;AAGhC,YAAM,SAAS;AAAA,QACb,SAAS,CAAE;AAAA,QACX,UAAU,CAAE;AAAA,QACZ,SAAS,CAAE;AAAA,QACX,KAAK,CAAE;AAAA,QACP,eAAe,CAAE;AAAA,QACjB,QAAQ,CAAE;AAAA,MACX;AAED,UAAI;AAEJ,YAAM,cAAc;AACpB,UAAI,OAAO;AACX,WAAK,SAAS,YAAY,KAAKA,KAAI,OAAO,MAAM;AAC9C,eAAO,OAAO,CAAC;AAAA,MAChB;AAED,YAAM,QAAQ,KAAK,MAAM,IAAI;AAC7B,UAAI,iBAAiB;AACrB,UAAI,sBAAsB;AAE1B,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAI,OAAO,MAAM,CAAC;AAClB,eAAO,KAAK,KAAM;AAClB,YAAI,SAAS,IAAI;AACf;AAAA,QACD;AAED,YAAI,uBAAuB,OAAO,SAAS,cAAc,EAAE,OAAO;AAChE;AACA,gCAAsB;AAAA,QACvB;AAED,cAAM,UAAU,kBAAkB,OAAO,SAAS,cAAc,EAAE,YAAY,IAAI;AAElF,sBAAc,QAAQ,OAAO,SAAS,cAAc,EAAE,MAAM,OAAO;AAEnE;AAAA,MACD;AAED,aAAO,YAAY,MAAM;AAAA,IAC1B;AAED,aAAS,YAAY,QAAQ;AAC3B,UAAIC,YAAW,IAAIC,qBAAgB;AAInC,UAAI,OAAO,QAAQ,SAAS,GAAG;AAC7B,QAAAD,UAAS,SAAS,OAAO,OAAO;AAAA,MACjC;AAED,MAAAA,UAAS,aAAa,YAAY,IAAIE,MAAsB,uBAAC,OAAO,UAAU,CAAC,CAAC;AAIhF,UAAI,OAAO,QAAQ,SAAS,GAAG;AAC7B,QAAAF,UAAS,aAAa,UAAU,IAAIE,MAAsB,uBAAC,OAAO,SAAS,CAAC,CAAC;AAAA,MAC9E;AAED,UAAI,OAAO,IAAI,SAAS,GAAG;AACzB,QAAAF,UAAS,aAAa,MAAM,IAAIE,MAAsB,uBAAC,OAAO,KAAK,CAAC,CAAC;AAAA,MACtE;AAED,UAAI,OAAO,OAAO,SAAS,GAAG;AAC5B,QAAAF,UAAS,aAAa,SAAS,IAAIE,MAAsB,uBAAC,OAAO,QAAQ,CAAC,CAAC;AAAA,MAC5E;AAED,UAAI,OAAO,cAAc,SAAS,GAAG;AACnC,QAAAF,YAAWA,UAAS,aAAc;AAClC,QAAAA,UAAS,aAAa,MAAM,IAAIE,MAAsB,uBAAC,OAAO,eAAe,CAAC,CAAC;AAAA,MAChF;AAED,MAAAF,UAAS,sBAAuB;AAEhC,aAAOA;AAAA,IACR;AAED,aAAS,cAAc,QAAQ,aAAa,SAAS;AACnD,UAAI,gBAAgB,UAAU;AAC5B,eAAO,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAEpD,YAAI,QAAQ,WAAW,QAAQ,WAAW,QAAQ,SAAS;AACzD,iBAAO,QAAQ,KAAK,QAAQ,IAAI,QAAQ,IAAI,QAAQ,EAAE;AAAA,QACvD;AAED,YAAI,OAAO,WAAW,OAAO,SAAS;AACpC,iBAAO,IAAI,KAAK,QAAQ,GAAG,QAAQ,CAAC;AAAA,QACrC;AAED,YAAI,SAAS,WAAW,WAAW,WAAW,UAAU,SAAS;AAC/D,iBAAO,OAAO,KAAK,QAAQ,MAAM,KAAO,QAAQ,QAAQ,KAAO,QAAQ,OAAO,GAAK;AAAA,QACpF;AAAA,MACT,WAAiB,gBAAgB,QAAQ;AACjC,cAAM,iBAAiB,QAAQ,kBAAkB,QAAQ;AACzD,cAAM,WAAW,QAAQ;AAEzB,YAAI,eAAe,WAAW,GAAG;AAC/B,iBAAO,QAAQ,KAAK,eAAe,CAAC,GAAG,eAAe,CAAC,GAAG,eAAe,CAAC,CAAC;AAE3E,cAAI,YAAY,SAAS,WAAW,GAAG;AACrC,mBAAO,cAAc,KAAK,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC;AAClD,mBAAO,cAAc,KAAK,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC;AAClD,mBAAO,cAAc,KAAK,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC;AAAA,UACnD;AAAA,QACX,WAAmB,eAAe,WAAW,GAAG;AACtC,iBAAO,QAAQ,KAAK,eAAe,CAAC,GAAG,eAAe,CAAC,GAAG,eAAe,CAAC,CAAC;AAC3E,iBAAO,QAAQ,KAAK,eAAe,CAAC,GAAG,eAAe,CAAC,GAAG,eAAe,CAAC,CAAC;AAAA,QAC5E;AAAA,MACF;AAAA,IACF;AAED,aAAS,WAAW,UAAU,IAAI,MAAM,eAAe;AACrD,cAAQ,MAAI;AAAA,QAEV,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,CAAC,SAAS,QAAQ,EAAE,GAAG,CAAC;AAAA,QACjC,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,CAAC,SAAS,SAAS,EAAE,GAAG,CAAC;AAAA,QAClC,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,CAAC,SAAS,SAAS,IAAI,aAAa,GAAG,CAAC;AAAA,QACjD,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,CAAC,SAAS,UAAU,IAAI,aAAa,GAAG,CAAC;AAAA,QAClD,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,CAAC,SAAS,SAAS,IAAI,aAAa,GAAG,CAAC;AAAA,QACjD,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,CAAC,SAAS,UAAU,IAAI,aAAa,GAAG,CAAC;AAAA,QAClD,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,CAAC,SAAS,WAAW,IAAI,aAAa,GAAG,CAAC;AAAA,QACnD,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,CAAC,SAAS,WAAW,IAAI,aAAa,GAAG,CAAC;AAAA,MACpD;AAAA,IACF;AAED,aAAS,kBAAkB,UAAU,IAAI,YAAY,eAAe;AAClE,YAAM,UAAU,CAAE;AAClB,UAAI,QACF,OAAO;AAET,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,YAAI,WAAW,CAAC,EAAE,SAAS,QAAQ;AACjC,gBAAM,OAAO,CAAE;AAEf,mBAAS,WAAW,UAAU,KAAK,MAAM,WAAW,CAAC,EAAE,WAAW,aAAa;AAC/E,gBAAM,IAAI,OAAO,CAAC;AAClB,kBAAQ,OAAO,CAAC;AAEhB,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,qBAAS,WAAW,UAAU,KAAK,MAAM,WAAW,CAAC,EAAE,UAAU,aAAa;AAC9E,iBAAK,KAAK,OAAO,CAAC,CAAC;AACnB,oBAAQ,OAAO,CAAC;AAAA,UACjB;AAED,kBAAQ,WAAW,CAAC,EAAE,IAAI,IAAI;AAAA,QACxC,OAAe;AACL,mBAAS,WAAW,UAAU,KAAK,MAAM,WAAW,CAAC,EAAE,MAAM,aAAa;AAC1E,kBAAQ,WAAW,CAAC,EAAE,IAAI,IAAI,OAAO,CAAC;AACtC,kBAAQ,OAAO,CAAC;AAAA,QACjB;AAAA,MACF;AAED,aAAO,CAAC,SAAS,IAAI;AAAA,IACtB;AAED,aAAS,YAAYD,OAAM,QAAQ;AACjC,YAAM,SAAS;AAAA,QACb,SAAS,CAAE;AAAA,QACX,UAAU,CAAE;AAAA,QACZ,SAAS,CAAE;AAAA,QACX,KAAK,CAAE;AAAA,QACP,eAAe,CAAE;AAAA,QACjB,QAAQ,CAAE;AAAA,MACX;AAED,YAAM,gBAAgB,OAAO,WAAW;AACxC,YAAM,OAAO,IAAI,SAASA,OAAM,OAAO,YAAY;AACnD,UAAI,QACF,MAAM;AAER,eAAS,iBAAiB,GAAG,iBAAiB,OAAO,SAAS,QAAQ,kBAAkB;AACtF,iBACM,sBAAsB,GAC1B,sBAAsB,OAAO,SAAS,cAAc,EAAE,OACtD,uBACA;AACA,mBAAS,kBAAkB,MAAM,KAAK,OAAO,SAAS,cAAc,EAAE,YAAY,aAAa;AAC/F,iBAAO,OAAO,CAAC;AACf,gBAAM,UAAU,OAAO,CAAC;AAExB,wBAAc,QAAQ,OAAO,SAAS,cAAc,EAAE,MAAM,OAAO;AAAA,QACpE;AAAA,MACF;AAED,aAAO,YAAY,MAAM;AAAA,IAC1B;AAID,QAAI;AACJ,UAAM,QAAQ;AAEd,QAAI,gBAAgB,aAAa;AAC/B,YAAM,OAAOI,YAAU,WAAC,IAAI,WAAW,IAAI,CAAC;AAC5C,YAAM,SAAS,YAAY,IAAI;AAE/B,iBAAW,OAAO,WAAW,UAAU,WAAW,MAAM,MAAM,IAAI,YAAY,MAAM,MAAM;AAAA,IAChG,OAAW;AACL,iBAAW,WAAW,MAAM,YAAY,IAAI,CAAC;AAAA,IAC9C;AAED,WAAO;AAAA,EACR;AACH;;"}