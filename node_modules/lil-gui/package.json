{"name": "lil-gui", "version": "0.17.0", "author": {"name": "<PERSON>"}, "description": "Makes a floating panel for controllers on the web.", "homepage": "https://lil-gui.georgealways.com", "module": "dist/lil-gui.esm.js", "main": "dist/lil-gui.umd.js", "types": "dist/lil-gui.esm.d.ts", "config": {"style": "dist/lil-gui.css", "styleMin": "dist/lil-gui.min.css"}, "scripts": {"build": "npm run icons && npm run sass && npm run postcss && npm run rollup", "build:all": "npm run build && npm run api && npm run homepage", "sass": "sass style/index.scss dist/lil-gui.css --no-charset --no-source-map", "postcss": "postcss dist/lil-gui.css -u postcss-combine-media-query -u cssnano --no-map -o dist/lil-gui.min.css", "rollup": "rollup -c", "lint": "eslint .", "icons": "node -r esm scripts/icons.js", "test": "node -r esm tests/utils/runner.js", "api": "node -r esm scripts/api.js --write", "types": "tsc dist/lil-gui.esm.js --declaration --allowJs --emitDeclarationOnly --outDir dist", "homepage": "node -r esm scripts/homepage.js", "server": "browser-sync start -s -f 'dist' 'index.html' 'homepage' 'examples' --no-open --no-notify --no-ui --no-ghost-mode --no-inject-changes", "clean": "rimraf -rf dist index.html API.md style/icons.scss", "preversion": "npm run build && npm test", "prepublishOnly": "npm run clean && npm run build && npm run types && npm test", "dev": "npm run build:all && node -r esm scripts/dev.js"}, "files": ["dist"], "license": "MIT", "devDependencies": {"browser-sync": "^2.27.7", "concurrently": "^6.3.0", "cssnano": "^5.0.12", "eslint": "^8.1.0", "eslint-plugin-jsdoc": "^37.0.3", "esm": "^3.2.25", "handlebars": "^4.3.1", "highlight.js": "^10.7.3", "jsdoc-api": "^5.0.2", "markdown-it": "^12.3.2", "onchange": "^6.0.0", "postcss": "^8.4.4", "postcss-cli": "^9.0.2", "postcss-combine-media-query": "^1.0.1", "rimraf": "^3.0.2", "rollup": "^1.17.0", "sass": "^1.22.10", "terser": "^4.3.8", "typescript": "^4.5.2", "webfont": "^11.2.26"}, "eslintIgnore": ["dist"]}