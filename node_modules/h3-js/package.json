{"name": "h3-js", "version": "4.2.1", "description": "Pure-Javascript version of the H3 library, a hexagon-based geographic grid system", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/uber/h3-js.git"}, "keywords": ["h3", "hexagon", "spatial-indexing", "emscripten", "geolocation"], "main": "dist/h3-js.js", "umd:main": "dist/h3-js.umd.js", "unpkg": "dist/h3-js.umd.js", "module": "dist/h3-js.es.js", "es2015": "dist/h3-js.es.js", "types": "dist/types.d.ts", "source": "lib/h3core.js", "scripts": {"build-update-h3": "bash scripts/update-h3.sh", "build-emscripten": "yarn build-update-h3 && yarn docker-emscripten", "build-legacy": "node scripts/build-legacy-api.js", "build-docs": "jsdoc2md --no-cache --global-index-format grouped --partial doc-files/scope.hbs --helper ./doc-files/insert-version.js --separators --template doc-files/README.tmpl.md lib/h3core.js lib/errors.js > README.md", "build-tsd-core": "tsc lib/h3core.js --noResolve --skipLibCheck --allowJs --declaration --emitDeclarationOnly --outFile dist/types.d.ts && bash scripts/postprocess-types.sh", "build-tsd-legacy": "node scripts/build-legacy-types.js", "build-tsd": "yarn build-tsd-core && yarn build-tsd-legacy", "bundle-umd": "microbundle --name h3 --format=umd", "bundle-cjs": "microbundle --format=cjs --no-compress", "bundle-es": "microbundle --format=es --no-compress", "bundle-cjs-browser": "microbundle -o dist/browser --format=cjs --no-compress --alias ../out/libh3=$(printf '%q' \"$PWD\")/dist/libh3-browser", "bundle-es-browser": "microbundle -o dist/browser --format=es --no-compress --alias ../out/libh3=$(printf '%q' \"$PWD\")/dist/libh3-browser", "dist": "yarn dist-clean && yarn docker-emscripten-browser && yarn bundle-umd && yarn bundle-cjs && yarn bundle-cjs-browser && yarn bundle-es && yarn bundle-es-browser && yarn build-legacy && yarn build-tsd", "dist-clean": "rm -rf dist", "rollup-test": "rollup test/index.js --file dist/test.js --sourcemap --format=cjs --external=tape,fs,path", "rollup-bindings": "rollup build/print-bindings.js --file dist/print-bindings.js --format cjs", "rollup-benchmark-browser": "rollup benchmark/browser.js --file dist/benchmark.browser.js --format=umd --external=benchmark --globals=benchmark:Benchmark", "rollup-benchmark-node": "rollup benchmark/node.js --file dist/benchmark.node.js --format=cjs --external=benchmark", "docker-boot": "docker run -dit --name emscripten -v $(pwd):/src:Z trzeci/emscripten:sdk-tag-1.38.43-64bit bash", "docker-reboot": "docker stop emscripten && docker rm emscripten && yarn docker-boot", "docker-emscripten": "yarn docker-emscripten-umd", "docker-emscripten-run": "yarn rollup-bindings && docker exec emscripten bash scripts/update-emscripten.sh", "docker-emscripten-umd": "yarn docker-emscripten-run -o libh3.js", "docker-emscripten-browser": "yarn docker-emscripten-run -s ENVIRONMENT=web -o libh3-browser.js && mv out/libh3-browser.js dist", "check-prettier": "yarn prettier && git diff --exit-code", "check-docs": "yarn build-docs && git diff --exit-code", "check-tsd": "yarn build-tsd && tsc --strict --noEmit dist/types.d.ts && tsc --strict --noEmit dist/legacy-types.d.ts", "lint": "tsc --noEmit && eslint lib* test/*", "test": "yarn lint && yarn && yarn test-fast && yarn test-legacy", "test-fast": "yarn test-raw | faucet", "test-raw": "yarn rollup-test && node dist/test.js", "test-legacy": "node test/legacy.spec.js | faucet", "cover": "yarn rollup-test && nyc --clean --reporter=lcov --reporter=text node dist/test.js", "cover-view": "yarn rollup-test && nyc --clean --reporter=html node dist/test.js && open coverage/index.html", "benchmark-node": "yarn rollup-benchmark-node && node dist/benchmark.node.js", "benchmark-browser": "yarn rollup-benchmark-browser && budo dist/benchmark.browser.js --open --title 'h3-js benchmarks'", "prepublishOnly": "yarn dist && git diff --exit-code", "prettier": "prettier --write --config .prettierrc 'lib/**/*.js' 'build/**/*.js' 'test/**/*.js'"}, "browser": {"./dist/h3-js.js": "./dist/browser/h3-js.js", "./dist/h3-js.es.js": "./dist/browser/h3-js.es.js"}, "devDependencies": {"@types/markdown-it": "14.1.2", "benchmark": "^2.1.4", "budo": "^11.5.0", "eslint": "^4.19.1", "eslint-config-prettier": "^2.9.0", "eslint-config-uber-es2015": "^3.1.2", "eslint-plugin-prettier": "^2.6.0", "faucet": "0.0.1", "jsdoc": "^3.6.6", "jsdoc-to-markdown": "^6.0.1", "microbundle": "^0.11.0", "nyc": "^14.1.1", "prettier": "^1.12.1", "rollup": "^2.79.2", "tape": "^5.5.3", "typescript": "^4.7.4"}, "resolutions": {"js-yaml": "3.13.1", "ws": "3.3.3"}, "engines": {"node": ">=4", "npm": ">=3", "yarn": ">=1.3.0"}, "volta": {"node": "12.19.0", "yarn": "1.22.10"}, "nyc": {"exclude": ["**/out/**", "**/test/**"]}, "dependencies": {}}