!function(A,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(A.h3={})}(this,function(A){var e=function(A){var e,r=void 0!==(A=A||{})?A:{},n={};for(e in r)r.hasOwnProperty(e)&&(n[e]=r[e]);var i,t,a,f,o=[],l=!1;i="object"==typeof window,t="function"==typeof importScripts,a="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,l=a&&!i&&!t,f=!i&&!l&&!t;var u,c,s,b,g,d="";l?(d=__dirname+"/",u=function(A,e){var r;return(r=iA(A))||(b||(b=require("fs")),g||(g=require("path")),A=g.normalize(A),r=b.readFileSync(A)),e?r:r.toString()},s=function(A){var e=u(A,!0);return e.buffer||(e=new Uint8Array(e)),m(e.buffer),e},process.argv.length>1&&process.argv[1].replace(/\\/g,"/"),o=process.argv.slice(2),r.inspect=function(){return"[Emscripten Module object]"}):f?("undefined"!=typeof read&&(u=function(A){var e=iA(A);return e?rA(e):read(A)}),s=function(A){var e;return(e=iA(A))?e:"function"==typeof readbuffer?new Uint8Array(readbuffer(A)):(m("object"==typeof(e=read(A,"binary"))),e)},"undefined"!=typeof scriptArgs?o=scriptArgs:void 0!==arguments&&(o=arguments),"undefined"!=typeof print&&("undefined"==typeof console&&(console={}),console.log=print,console.warn=console.error="undefined"!=typeof printErr?printErr:print)):(i||t)&&(t?d=self.location.href:"undefined"!=typeof document&&document.currentScript&&(d=document.currentScript.src),d=0!==d.indexOf("blob:")?d.substr(0,d.lastIndexOf("/")+1):"",u=function(A){try{var e=new XMLHttpRequest;return e.open("GET",A,!1),e.send(null),e.responseText}catch(e){var r=iA(A);if(r)return rA(r);throw e}},t&&(s=function(A){try{var e=new XMLHttpRequest;return e.open("GET",A,!1),e.responseType="arraybuffer",e.send(null),new Uint8Array(e.response)}catch(e){var r=iA(A);if(r)return r;throw e}}),c=function(A,e,r){var n=new XMLHttpRequest;n.open("GET",A,!0),n.responseType="arraybuffer",n.onload=function(){if(200==n.status||0==n.status&&n.response)e(n.response);else{var i=iA(A);i?e(i.buffer):r()}},n.onerror=r,n.send(null)});var w=r.print||console.log.bind(console),B=r.printErr||console.warn.bind(console);for(e in n)n.hasOwnProperty(e)&&(r[e]=n[e]);n=null,r.arguments&&(o=r.arguments);var v=0,k=!1;function m(A,e){A||vA("Assertion failed: "+e)}function D(A){var e=r["_"+A];return m(e,"Cannot call unknown function "+A+", make sure it is exported"),e}var Q,h,C,E,_,y,M,P="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function I(A,e){return A?function(A,e,r){for(var n=e+r,i=e;A[i]&&!(i>=n);)++i;if(i-e>16&&A.subarray&&P)return P.decode(A.subarray(e,i));for(var t="";e<i;){var a=A[e++];if(128&a){var f=63&A[e++];if(192!=(224&a)){var o=63&A[e++];if((a=224==(240&a)?(15&a)<<12|f<<6|o:(7&a)<<18|f<<12|o<<6|63&A[e++])<65536)t+=String.fromCharCode(a);else{var l=a-65536;t+=String.fromCharCode(55296|l>>10,56320|1023&l)}}else t+=String.fromCharCode((31&a)<<6|f)}else t+=String.fromCharCode(a)}return t}(C,A,e):""}function p(A,e){return A%e>0&&(A+=e-A%e),A}function T(A){Q=A,r.HEAP8=h=new Int8Array(A),r.HEAP16=E=new Int16Array(A),r.HEAP32=_=new Int32Array(A),r.HEAPU8=C=new Uint8Array(A),r.HEAPU16=new Uint16Array(A),r.HEAPU32=new Uint32Array(A),r.HEAPF32=y=new Float32Array(A),r.HEAPF64=M=new Float64Array(A)}"undefined"!=typeof TextDecoder&&new TextDecoder("utf-16le");var F=r.TOTAL_MEMORY||33554432;function L(A){for(;A.length>0;){var e=A.shift();if("function"!=typeof e){var n=e.func;"number"==typeof n?void 0===e.arg?r.dynCall_v(n):r.dynCall_vi(n,e.arg):n(void 0===e.arg?null:e.arg)}else e()}}F=(Q=r.buffer?r.buffer:new ArrayBuffer(F)).byteLength,T(Q),_[7152]=5271520;var G=[],z=[],U=[],x=[],R=Math.abs,Y=Math.ceil,V=Math.floor,O=Math.min,S=0,H=null,W=null;r.preloadedImages={},r.preloadedAudios={};var Z,J,K=null,j="data:application/octet-stream;base64,";function N(A){return String.prototype.startsWith?A.startsWith(j):0===A.indexOf(j)}function X(A){return A}function q(A){return A.replace(/\b__Z[\w\d_]+/g,function(A){var e=X(A);return A===e?A:e+" ["+A+"]"})}function $(){var A=new Error;if(!A.stack){try{throw new Error(0)}catch(e){A=e}if(!A.stack)return"(no stack trace available)"}return A.stack.toString()}function AA(){return h.length}function eA(A){try{var e=new ArrayBuffer(A);if(e.byteLength!=A)return;return new Int8Array(e).set(h),oA(e),T(e),1}catch(A){}}function rA(A){for(var e=[],r=0;r<A.length;r++){var n=A[r];n>255&&(n&=255),e.push(String.fromCharCode(n))}return e.join("")}K="data:application/octet-stream;base64,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";var nA="function"==typeof atob?atob:function(A){var e,r,n,i,t,a,f="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",o="",l=0;A=A.replace(/[^A-Za-z0-9\+\/\=]/g,"");do{n=f.indexOf(A.charAt(l++)),e=(15&(i=f.indexOf(A.charAt(l++))))<<4|(t=f.indexOf(A.charAt(l++)))>>2,r=(3&t)<<6|(a=f.indexOf(A.charAt(l++))),o+=String.fromCharCode(n<<2|i>>4),64!==t&&(o+=String.fromCharCode(e)),64!==a&&(o+=String.fromCharCode(r))}while(l<A.length);return o};function iA(A){if(N(A))return function(A){if("boolean"==typeof l&&l){var e;try{e=Buffer.from(A,"base64")}catch(r){e=new Buffer(A,"base64")}return new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}try{for(var r=nA(A),n=new Uint8Array(r.length),i=0;i<r.length;++i)n[i]=r.charCodeAt(i);return n}catch(A){throw new Error("Converting base64 string to bytes failed.")}}(A.slice(j.length))}var tA,aA,fA=function(A,e,r){var n=new A.Int8Array(r),i=new A.Int32Array(r),t=(new A.Uint8Array(r),new A.Float32Array(r),new A.Float64Array(r)),a=0|e.o,f=0|e.p,o=A.Math.floor,l=A.Math.abs,u=A.Math.sqrt,c=A.Math.pow,s=A.Math.cos,b=A.Math.sin,g=A.Math.tan,d=A.Math.acos,w=A.Math.asin,B=A.Math.atan,v=A.Math.atan2,k=A.Math.ceil,m=A.Math.imul,D=A.Math.min,Q=A.Math.max,h=A.Math.clz32,C=e.b,E=e.c,_=e.d,y=e.e,M=e.f,P=e.g,I=e.h,p=e.i,T=28640;function F(A,e,r,n){return 0|L(A|=0,e|=0,r|=0,n|=0,0)}function L(A,e,r,n,t){var a,f=0,o=0,l=0,u=0;if(a=T,T=T+16|0,o=a,!(0|G(A|=0,e|=0,r|=0,n|=0,t|=0)))return T=a,0;do{if((0|r)>=0){if((0|r)>13780509){if(0|(f=0|se(15,o)))break;o=0|i[(l=o)>>2],l=0|i[l+4>>2]}else u=0|rr(0|r,0|(f=((0|r)<0)<<31>>31),3,0),l=0|E(),f=0|Xe(0|r,0|f,1,0),f=0|Xe(0|(f=0|rr(0|u,0|l,0|f,0|E())),0|E(),1,0),l=0|E(),i[o>>2]=f,i[o+4>>2]=l,o=f;if(br(0|n,0,o<<3|0),0|t){br(0|t,0,o<<2|0),f=0|z(A,e,r,n,t,o,l,0);break}(f=0|Ne(o,4))?(u=0|z(A,e,r,n,f,o,l,0),je(f),f=u):f=13}else f=2}while(0);return T=a,0|f}function G(A,e,r,n,t){e|=0,r|=0,n|=0,t|=0;var a,f,o,l=0,u=0,c=0,s=0,b=0,g=0,d=0,w=0;if(f=T,T=T+16|0,o=f+8|0,i[(w=a=f)>>2]=A|=0,i[w+4>>2]=e,(0|r)<0)return T=f,2;if(i[(l=n)>>2]=A,i[l+4>>2]=e,(l=0!=(0|t))&&(i[t>>2]=0),0|SA(A,e))return T=f,9;i[o>>2]=0;A:do{if((0|r)>=1)if(l)for(g=1,b=0,d=0,w=1,l=A;;){if(!(b|d)){if(0|(l=0|U(l,e,4,o,a)))break A;if(0|SA(l=0|i[(e=a)>>2],e=0|i[e+4>>2])){l=9;break A}}if(0|(l=0|U(l,e,0|i[26800+(d<<2)>>2],o,a)))break A;if(l=0|i[(e=a)>>2],e=0|i[e+4>>2],i[(A=n+(g<<3)|0)>>2]=l,i[A+4>>2]=e,i[t+(g<<2)>>2]=w,u=(0|(A=b+1|0))==(0|w),s=6==(0|(c=d+1|0)),0|SA(l,e)){l=9;break A}if((0|(w=w+(s&u&1)|0))>(0|r)){l=0;break}g=g+1|0,b=u?0:A,d=u?s?0:c:d}else for(g=1,b=0,d=0,w=1,l=A;;){if(!(b|d)){if(0|(l=0|U(l,e,4,o,a)))break A;if(0|SA(l=0|i[(e=a)>>2],e=0|i[e+4>>2])){l=9;break A}}if(0|(l=0|U(l,e,0|i[26800+(d<<2)>>2],o,a)))break A;if(l=0|i[(e=a)>>2],e=0|i[e+4>>2],i[(A=n+(g<<3)|0)>>2]=l,i[A+4>>2]=e,u=(0|(A=b+1|0))==(0|w),s=6==(0|(c=d+1|0)),0|SA(l,e)){l=9;break A}if((0|(w=w+(s&u&1)|0))>(0|r)){l=0;break}g=g+1|0,b=u?0:A,d=u?s?0:c:d}else l=0}while(0);return T=f,0|l}function z(A,e,r,n,t,a,f,o){r|=0,n|=0,t|=0,o|=0;var l,u,c,s=0,b=0,g=0,d=0,w=0,B=0,v=0;if(c=T,T=T+16|0,l=c+8|0,u=c,s=0|ir(0|(A|=0),0|(e|=0),0|(a|=0),0|(f|=0)),g=0|E(),!(0==(0|(v=0|i[(B=d=n+(s<<3)|0)>>2]))&0==(0|(B=0|i[B+4>>2]))|(b=(0|v)==(0|A)&(0|B)==(0|e))))do{s=0|nr(0|(s=0|Xe(0|s,0|g,1,0)),0|E(),0|a,0|f),g=0|E(),b=(0|(B=0|i[(v=d=n+(s<<3)|0)>>2]))==(0|A)&(0|(v=0|i[v+4>>2]))==(0|e)}while(!(0==(0|B)&0==(0|v)|b));if(s=t+(s<<2)|0,b&&(0|i[s>>2])<=(0|o))return T=c,0;if(i[(v=d)>>2]=A,i[v+4>>2]=e,i[s>>2]=o,(0|o)>=(0|r))return T=c,0;switch(b=o+1|0,i[l>>2]=0,0|(s=0|U(A,e,2,l,u))){case 9:w=9;break;case 0:(s=0|z(0|i[(s=u)>>2],0|i[s+4>>2],r,n,t,a,f,b))||(w=9)}A:do{if(9==(0|w)){switch(i[l>>2]=0,0|(s=0|U(A,e,3,l,u))){case 9:break;case 0:if(0|(s=0|z(0|i[(s=u)>>2],0|i[s+4>>2],r,n,t,a,f,b)))break A;break;default:break A}switch(i[l>>2]=0,0|(s=0|U(A,e,1,l,u))){case 9:break;case 0:if(0|(s=0|z(0|i[(s=u)>>2],0|i[s+4>>2],r,n,t,a,f,b)))break A;break;default:break A}switch(i[l>>2]=0,0|(s=0|U(A,e,5,l,u))){case 9:break;case 0:if(0|(s=0|z(0|i[(s=u)>>2],0|i[s+4>>2],r,n,t,a,f,b)))break A;break;default:break A}switch(i[l>>2]=0,0|(s=0|U(A,e,4,l,u))){case 9:break;case 0:if(0|(s=0|z(0|i[(s=u)>>2],0|i[s+4>>2],r,n,t,a,f,b)))break A;break;default:break A}switch(i[l>>2]=0,0|(s=0|U(A,e,6,l,u))){case 9:break;case 0:if(0|(s=0|z(0|i[(s=u)>>2],0|i[s+4>>2],r,n,t,a,f,b)))break A;break;default:break A}return T=c,0}}while(0);return T=c,0|s}function U(A,e,r,n,t){A|=0,e|=0,t|=0;var a,f,o=0,l=0,u=0,c=0,s=0,b=0,g=0;if((r|=0)>>>0>6)return 1;if(i[(n|=0)>>2]=s=(0|i[n>>2])%6|0,(0|s)>0){o=0;do{r=0|kA(r),o=o+1|0}while((0|o)<(0|i[n>>2]))}if(s=0|ar(0|A,0|e,45),E(),(f=127&s)>>>0>121)return 5;a=0|ZA(A,e),o=0|ar(0|A,0|e,52),E(),o&=15;A:do{if(o){for(;;){if(u=0|ar(0|A,0|e,0|(l=3*(15-o|0)|0)),E(),7==(0|(u&=7))){e=5;break}if(g=0==(0|XA(o)),o=o+-1|0,b=0|fr(7,0,0|l),e&=~(0|E()),A=0|fr(0|i[(g?432:16)+(28*u|0)+(r<<2)>>2],0,0|l)|A&~b,e|=l=0|E(),!(r=0|i[(g?640:224)+(28*u|0)+(r<<2)>>2])){r=0;break A}if(!o){c=8;break A}}return 0|e}c=8}while(0);8==(0|c)&&(A|=0|fr(0|(g=0|i[848+(28*f|0)+(r<<2)>>2]),0,45),e=0|E()|-1040385&e,r=0|i[4272+(28*f|0)+(r<<2)>>2],127==(127&g|0)&&(g=0|fr(0|i[848+(28*f|0)+20>>2],0,45),e=0|E()|-1040385&e,r=0|i[4272+(28*f|0)+20>>2],A=0|KA(g|A,e),e=0|E(),i[n>>2]=1+(0|i[n>>2]))),u=0|ar(0|A,0|e,45),E(),u&=127;A:do{if(0|Y(u)){e:do{if(1==(0|ZA(A,e))){if((0|f)!=(0|u)){if(0|H(u,0|i[7696+(28*f|0)>>2])){A=0|NA(A,e),l=1,e=0|E();break}_(27795,26864,436,26872)}switch(0|a){case 3:A=0|KA(A,e),e=0|E(),i[n>>2]=1+(0|i[n>>2]),l=0;break e;case 5:A=0|NA(A,e),e=0|E(),i[n>>2]=5+(0|i[n>>2]),l=0;break e;case 0:return 9;default:return 1}}else l=0}while(0);if((0|r)>0){o=0;do{A=0|JA(A,e),e=0|E(),o=o+1|0}while((0|o)!=(0|r))}if((0|f)!=(0|u)){if(!(0|V(u))){if(0!=(0|l)|5!=(0|ZA(A,e)))break;i[n>>2]=1+(0|i[n>>2]);break}switch(127&s){case 8:case 118:break A}3!=(0|ZA(A,e))&&(i[n>>2]=1+(0|i[n>>2]))}}else if((0|r)>0){o=0;do{A=0|KA(A,e),e=0|E(),o=o+1|0}while((0|o)!=(0|r))}}while(0);return i[n>>2]=((0|i[n>>2])+r|0)%6|0,i[(g=t)>>2]=A,i[g+4>>2]=e,0}function x(A,e,r,n){r|=0,n|=0;var t,a,f,o=0,l=0;for(f=T,T=T+16|0,t=f,a=f+8|0,o=(o=0==(0|SA(A|=0,e|=0)))?1:2;;){if(i[a>>2]=0,0==(0|U(A,e,o,a,t))&((0|i[(l=t)>>2])==(0|r)?(0|i[l+4>>2])==(0|n):0)){A=4;break}if((o=o+1|0)>>>0>=7){o=7,A=4;break}}return 4==(0|A)?(T=f,0|o):0}function R(A,e,r,n,a,f,o){e|=0,r|=0,n|=0,a|=0,f|=0,o|=0;var l,u,c,s,b,g,d,w,B,v,k=0,m=0,D=0,Q=0,h=0,C=0,_=0,y=0,M=0,P=0,I=0,p=0,F=0,L=0,G=0,z=0,U=0,x=0,R=0;if(v=T,T=T+64|0,B=v+24|0,u=v,(0|(m=0|i[(A|=0)>>2]))<=0)return T=v,0;for(c=A+4|0,s=8+(d=v+48|0)|0,b=8+(w=v+32|0)|0,g=8+(l=v+8|0)|0,k=0,F=0;;){i[d>>2]=i[(p=(D=0|i[c>>2])+(F<<4)|0)>>2],i[d+4>>2]=i[p+4>>2],i[d+8>>2]=i[p+8>>2],i[d+12>>2]=i[p+12>>2],(0|F)==(m+-1|0)?(i[w>>2]=i[D>>2],i[w+4>>2]=i[D+4>>2],i[w+8>>2]=i[D+8>>2],i[w+12>>2]=i[D+12>>2]):(i[w>>2]=i[(p=D+(F+1<<4)|0)>>2],i[w+4>>2]=i[p+4>>2],i[w+8>>2]=i[p+8>>2],i[w+12>>2]=i[p+12>>2]),m=0|eA(d,w,n,B);A:do{if(m)D=0,k=m;else if(D=0|i[(m=B)>>2],(0|(m=0|i[m+4>>2]))>0|0==(0|m)&D>>>0>0){I=0,p=0;e:for(;;){if(U=1/(+(D>>>0)+4294967296*+(0|m)),R=+t[d>>3],x=+((m=0|qe(0|D,0|m,0|I,0|p))>>>0)+4294967296*+(0|E()),t[l>>3]=U*(R*x)+U*(+t[w>>3]*(z=+(I>>>0)+4294967296*+(0|p))),t[g>>3]=U*(+t[s>>3]*x)+U*(+t[b>>3]*z),0|(m=0|qA(l,n,u))){k=m;break}C=0|ir(0|(M=0|i[(P=u)>>2]),0|(P=0|i[P+4>>2]),0|e,0|r),Q=0|E(),D=0|i[(h=m=o+(C<<3)|0)>>2],h=0|i[h+4>>2];r:do{if(0==(0|D)&0==(0|h))L=m,G=16;else for(_=0,y=0;;){if((0|_)>(0|r)|(0|_)==(0|r)&y>>>0>e>>>0){k=1;break e}if((0|D)==(0|M)&(0|h)==(0|P))break r;if(C=0|nr(0|(m=0|Xe(0|C,0|Q,1,0)),0|E(),0|e,0|r),Q=0|E(),y=0|Xe(0|y,0|_,1,0),_=0|E(),0==(0|(D=0|i[(h=m=o+(C<<3)|0)>>2]))&0==(0|(h=0|i[h+4>>2]))){L=m,G=16;break}}}while(0);if(16!=(0|G)||(G=0,0==(0|M)&0==(0|P))||(i[(y=L)>>2]=M,i[y+4>>2]=P,i[(y=f+(i[a>>2]<<3)|0)>>2]=M,i[y+4>>2]=P,y=0|Xe(0|i[(y=a)>>2],0|i[y+4>>2],1,0),M=0|E(),i[(P=a)>>2]=y,i[P+4>>2]=M),I=0|Xe(0|I,0|p,1,0),p=0|E(),D=0|i[(m=B)>>2],!((0|(m=0|i[m+4>>2]))>(0|p)|(0|m)==(0|p)&D>>>0>I>>>0)){D=1;break A}}D=0}else D=1}while(0);if(F=F+1|0,!D){G=21;break}if((0|F)>=(0|(m=0|i[A>>2]))){k=0,G=21;break}}return 21==(0|G)?(T=v,0|k):0}function Y(A){return(A|=0)>>>0>121?0|(A=0):0|(A=0|i[7696+(28*A|0)+16>>2])}function V(A){return 4==(0|(A|=0))|117==(0|A)|0}function O(A){return 0|i[11120+(216*(0|i[(A|=0)>>2])|0)+(72*(0|i[A+4>>2])|0)+(24*(0|i[A+8>>2])|0)+(i[A+12>>2]<<3)>>2]}function S(A){return 0|i[11120+(216*(0|i[(A|=0)>>2])|0)+(72*(0|i[A+4>>2])|0)+(24*(0|i[A+8>>2])|0)+(i[A+12>>2]<<3)+4>>2]}function H(A,e){return(0|i[7696+(28*(A|=0)|0)+20>>2])==(0|(e|=0))?0|(e=1):0|(e=(0|i[7696+(28*A|0)+24>>2])==(0|e))}function W(A,e){return 0|i[848+(28*(A|=0)|0)+((e|=0)<<2)>>2]}function Z(A,e){return(0|i[848+(28*(A|=0)|0)>>2])==(0|(e|=0))?0|(e=0):(0|i[848+(28*A|0)+4>>2])==(0|e)?0|(e=1):(0|i[848+(28*A|0)+8>>2])==(0|e)?0|(e=2):(0|i[848+(28*A|0)+12>>2])==(0|e)?0|(e=3):(0|i[848+(28*A|0)+16>>2])==(0|e)?0|(e=4):(0|i[848+(28*A|0)+20>>2])==(0|e)?0|(e=5):0|((0|i[848+(28*A|0)+24>>2])==(0|e)?6:7)}function J(A){var e,r,n;return e=(n=+t[16+(A|=0)>>3])-(r=+t[A+24>>3]),+(n<r?e+6.283185307179586:e)}function K(A){return+t[16+(A|=0)>>3]<+t[A+24>>3]|0}function j(A){return+(+t[(A|=0)>>3]-+t[A+8>>3])}function N(A,e){var r,n,i=0;return(i=+t[(e|=0)>>3])>=+t[8+(A|=0)>>3]&&i<=+t[A>>3]?(e=(n=+t[e+8>>3])>=(i=+t[A+24>>3]),A=n<=(r=+t[A+16>>3])&1,r<i?e&&(A=1):e||(A=0),0|(e=0!=(0|A))):0|(e=0)}function X(A,e){var r,n,i,a,f,o=0,l=0,u=0,c=0;return+t[(A|=0)>>3]<+t[8+(e|=0)>>3]?0|(o=0):+t[A+8>>3]>+t[e>>3]?0|(o=0):(u=(f=+t[(o=e+16|0)>>3])<(a=+t[(n=e+24|0)>>3]),e=(c=+t[(r=A+24|0)>>3])-f<a-(l=+t[A+16>>3]),A=(i=l<c)?u|e?1:2:0,e=u?i?1:e?2:1:0,(l=+le(l,A))<+le(+t[n>>3],e)?0|(u=0):(c=+le(+t[r>>3],A))>+le(+t[o>>3],e)?0|(u=0):0|(u=1))}function q(A,e){var r,n,i,a,f,o=0,l=0,u=0,c=0;return+t[(A|=0)>>3]<+t[(e|=0)>>3]?0|(o=0):+t[A+8>>3]>+t[e+8>>3]?0|(o=0):(u=(c=+t[(r=e+16|0)>>3])<(f=+t[(n=e+24|0)>>3]),e=(l=+t[A+24>>3])-c<f-(a=+t[(o=A+16|0)>>3]),A=(i=a<l)?u|e?1:2:0,e=u?i?1:e?2:1:0,(l=+le(l,A))<=+le(+t[n>>3],e)?0|(u=(c=+le(+t[o>>3],A))>=+le(+t[r>>3],e)):0|(u=0))}function $(A,e){A|=0,e|=0;var r,n,a,f,o,l=0;a=T,T=T+176|0,i[(n=a)>>2]=4,t[n+8>>3]=l=+t[e>>3],t[n+16>>3]=f=+t[e+16>>3],t[n+24>>3]=l,t[n+32>>3]=l=+t[e+24>>3],t[n+40>>3]=o=+t[e+8>>3],t[n+48>>3]=l,t[n+56>>3]=o,t[n+64>>3]=f,r=96+(e=n+72|0)|0;do{i[e>>2]=0,e=e+4|0}while((0|e)<(0|r));sr(0|A,0|n,168),T=a}function AA(A,e,r){A|=0,e|=0,r|=0;var n,f,u,c,s,b,g,d=0,w=0,B=0,v=0,m=0,Q=0,h=0;b=T,T=T+288|0,s=b+264|0,v=b+96|0,w=(d=B=b)+96|0;do{i[d>>2]=0,d=d+4|0}while((0|d)<(0|w));return 0|(e=0|ne(e,B))?(T=b,0|(Q=e)):(ee(B=0|i[(w=B)>>2],w=0|i[w+4>>2],s),re(B,w,v),c=+ue(s,v+8|0),t[s>>3]=+t[A>>3],t[(w=s+8|0)>>3]=+t[A+16>>3],t[v>>3]=+t[A+8>>3],t[(B=v+8|0)>>3]=+t[A+24>>3],f=+ue(s,v),u=+l(+(h=+t[w>>3]-+t[B>>3])),n=+l(+(g=+t[s>>3]-+t[v>>3])),0==h|0==g||(h=+lr(+u,+n),h=+k(+f*f/+ur(+h/+ur(+u,+n),3)/(c*(2.59807621135*c)*.8)),t[a>>3]=h,m=~~h>>>0,Q=+l(h)>=1?h>0?~~+D(+o(h/4294967296),4294967295)>>>0:~~+k((h-+(~~h>>>0))/4294967296)>>>0:0,2146435072==(2146435072&i[a+4>>2]|0))?e=1:(i[(e=r)>>2]=(v=0==(0|m)&0==(0|Q))?1:m,i[e+4>>2]=v?0:Q,e=0),T=b,0|(Q=e))}function eA(A,e,r,n){A|=0,e|=0,r|=0,n|=0;var f,u,c,s,b=0,g=0,d=0;s=T,T=T+288|0,u=s+264|0,c=s+96|0,f=(b=g=s)+96|0;do{i[b>>2]=0,b=b+4|0}while((0|b)<(0|f));return 0|(r=0|ne(r,g))?(T=s,0|(n=r)):(ee(b=0|i[(r=g)>>2],r=0|i[r+4>>2],u),re(b,r,c),d=+ue(u,c+8|0),d=+k(+ +ue(A,e)/(2*d)),t[a>>3]=d,r=~~d>>>0,b=+l(d)>=1?d>0?~~+D(+o(d/4294967296),4294967295)>>>0:~~+k((d-+(~~d>>>0))/4294967296)>>>0:0,2146435072==(2146435072&i[a+4>>2]|0)?(T=s,0|(n=1)):(i[n>>2]=(g=0==(0|r)&0==(0|b))?1:r,i[n+4>>2]=g?0:b,T=s,0|(n=0)))}function rA(A,e,r,n){r|=0,n|=0,i[(A|=0)>>2]=e|=0,i[A+4>>2]=r,i[A+8>>2]=n}function nA(A,e){A|=0;var r,n,a,f,o=0,u=0,c=0,s=0,b=0,g=0,d=0;i[(f=8+(e|=0)|0)>>2]=0,s=+l(+(n=+t[A>>3])),s+=.5*(b=1.1547005383792515*+l(+(a=+t[A+8>>3]))),s-=+(0|(o=~~s)),b-=+(0|(A=~~b));do{if(s<.5){if(s<.3333333333333333){if(i[e>>2]=o,b<.5*(s+1)){i[e+4>>2]=A;break}i[e+4>>2]=A=A+1|0;break}if(i[e+4>>2]=A=(1&!(b<(d=1-s)))+A|0,d<=b&b<2*s){i[e>>2]=o=o+1|0;break}i[e>>2]=o;break}if(!(s<.6666666666666666)){if(i[e>>2]=o=o+1|0,b<.5*s){i[e+4>>2]=A;break}i[e+4>>2]=A=A+1|0;break}if(b<1-s){if(i[e+4>>2]=A,2*s-1<b){i[e>>2]=o;break}}else i[e+4>>2]=A=A+1|0;i[e>>2]=o=o+1|0}while(0);do{if(n<0){if(1&A){o=~~(+(0|o)-(2*(+((g=0|qe(0|o,((0|o)<0)<<31>>31|0,0|(g=(A+1|0)/2|0),((0|g)<0)<<31>>31|0))>>>0)+4294967296*+(0|E()))+1)),i[e>>2]=o;break}o=~~(+(0|o)-2*(+((g=0|qe(0|o,((0|o)<0)<<31>>31|0,0|(g=(0|A)/2|0),((0|g)<0)<<31>>31|0))>>>0)+4294967296*+(0|E()))),i[e>>2]=o;break}}while(0);g=e+4|0,a<0&&(i[e>>2]=o=o-((1|A<<1)/2|0)|0,i[g>>2]=A=0-A|0),u=A-o|0,(0|o)<0?(c=0-o|0,i[g>>2]=u,i[f>>2]=c,i[e>>2]=0,A=u,o=0):c=0,(0|A)<0&&(i[e>>2]=o=o-A|0,i[f>>2]=c=c-A|0,i[g>>2]=0,A=0),r=o-c|0,u=A-c|0,(0|c)<0&&(i[e>>2]=r,i[g>>2]=u,i[f>>2]=0,A=u,o=r,c=0),(0|(u=(0|c)<(0|(u=(0|A)<(0|o)?A:o))?c:u))<=0||(i[e>>2]=o-u,i[g>>2]=A-u,i[f>>2]=c-u)}function iA(A){var e,r=0,n=0,t=0,a=0,f=0;n=0|i[(e=4+(A|=0)|0)>>2],(0|(r=0|i[A>>2]))<0&&(i[e>>2]=n=n-r|0,i[(f=A+8|0)>>2]=(0|i[f>>2])-r,i[A>>2]=0,r=0),(0|n)<0?(i[A>>2]=r=r-n|0,i[(f=A+8|0)>>2]=a=(0|i[f>>2])-n|0,i[e>>2]=0,n=0):(f=a=A+8|0,a=0|i[a>>2]),(0|a)<0&&(i[A>>2]=r=r-a|0,i[e>>2]=n=n-a|0,i[f>>2]=0,a=0),(0|(t=(0|a)<(0|(t=(0|n)<(0|r)?n:r))?a:t))<=0||(i[A>>2]=r-t,i[e>>2]=n-t,i[f>>2]=a-t)}function tA(A,e){var r,n;t[(e|=0)>>3]=+((0|i[(A|=0)>>2])-(n=0|i[A+8>>2])|0)-.5*(r=+((0|i[A+4>>2])-n|0)),t[e+8>>3]=.8660254037844386*r}function aA(A,e,r){i[(r|=0)>>2]=(0|i[(e|=0)>>2])+(0|i[(A|=0)>>2]),i[r+4>>2]=(0|i[e+4>>2])+(0|i[A+4>>2]),i[r+8>>2]=(0|i[e+8>>2])+(0|i[A+8>>2])}function fA(A,e,r){i[(r|=0)>>2]=(0|i[(A|=0)>>2])-(0|i[(e|=0)>>2]),i[r+4>>2]=(0|i[A+4>>2])-(0|i[e+4>>2]),i[r+8>>2]=(0|i[A+8>>2])-(0|i[e+8>>2])}function oA(A,e){var r,n=0;n=0|m(0|i[(A|=0)>>2],e|=0),i[A>>2]=n,r=0|m(0|i[(n=A+4|0)>>2],e),i[n>>2]=r,e=0|m(0|i[(A=A+8|0)>>2],e),i[A>>2]=e}function lA(A){var e,r,n,t=0,a=0,f=0,o=0;A=(A=(a=(0|(o=(0|i[8+(A|=0)>>2])-((n=(0|(r=0|i[A>>2]))<0)?r:0)+((e=(0|(f=(0|i[A+4>>2])-(n?r:0)|0))<0)?0-f|0:0)|0))<0)?0:o)-((f=(0|(a=(0|A)<(0|(a=(0|(t=(e?0:f)-(a?o:0)|0))<(0|(o=(n?0:r)-(e?f:0)-(a?o:0)|0))?t:o))?A:a))>0)?a:0)|0,t=t-(f?a:0)|0;A:do{switch(o-(f?a:0)|0){case 0:switch(0|t){case 0:return 0|(0==(0|A)?0:1==(0|A)?1:7);case 1:return 0|(0==(0|A)?2:1==(0|A)?3:7);default:break A}case 1:switch(0|t){case 0:return 0|(0==(0|A)?4:1==(0|A)?5:7);case 1:if(A)break A;return 6;default:break A}}}while(0);return 7}function uA(A){var e,r,n=0,t=0,a=0,f=0,o=0,l=0,u=0;if((e=(0|i[(A|=0)>>2])-(l=0|i[(r=A+8|0)>>2])|0)>>>0>715827881|(l=(0|i[(u=A+4|0)>>2])-l|0)>>>0>715827881){if(f=2147483647-e|0,o=-2147483648-e|0,(a=(0|e)>0)?(0|f)<(0|e):(0|o)>(0|e))return 1;if(t=e<<1,a?(2147483647-t|0)<(0|e):(-2147483648-t|0)>(0|e))return 1;if((0|l)>0?(2147483647-l|0)<(0|l):(-2147483648-l|0)>(0|l))return 1;if(n=3*e|0,t=l<<1,(a?(0|f)<(0|t):(0|o)>(0|t))||((0|e)>-1?(-2147483648|n|0)>=(0|l):(-2147483648^n|0)<(0|l)))return 1}else t=l<<1,n=3*e|0;return a=0|Je(.14285714285714285*+(n-l|0)),i[A>>2]=a,f=0|Je(.14285714285714285*+(t+e|0)),i[u>>2]=f,i[r>>2]=0,n=(t=(0|f)<(0|a))?a:f,(0|(t=t?f:a))<0&&((-2147483648==(0|t)||((0|n)>0?(2147483647-n|0)<(0|t):(-2147483648-n|0)>(0|t)))&&_(27795,26892,354,26903),((0|n)>-1?(-2147483648|n|0)>=(0|t):(-2147483648^n|0)<(0|t))&&_(27795,26892,354,26903)),n=f-a|0,(0|a)<0?(t=0-a|0,i[u>>2]=n,i[r>>2]=t,i[A>>2]=0,a=0):(n=f,t=0),(0|n)<0&&(i[A>>2]=a=a-n|0,i[r>>2]=t=t-n|0,i[u>>2]=0,n=0),o=a-t|0,f=n-t|0,(0|t)<0?(i[A>>2]=o,i[u>>2]=f,i[r>>2]=0,n=f,f=o,t=0):f=a,(0|(a=(0|t)<(0|(a=(0|n)<(0|f)?n:f))?t:a))<=0?0|(u=0):(i[A>>2]=f-a,i[u>>2]=n-a,i[r>>2]=t-a,0|(u=0))}function cA(A){var e,r,n=0,t=0,a=0,f=0,o=0,l=0;if((o=(0|i[(A|=0)>>2])-(f=0|i[(e=A+8|0)>>2])|0)>>>0>715827881|(f=(0|i[(r=A+4|0)>>2])-f|0)>>>0>715827881){if((t=(0|o)>0)?(2147483647-o|0)<(0|o):(-2147483648-o|0)>(0|o))return 1;if(n=o<<1,(a=(0|f)>0)?(2147483647-f|0)<(0|f):(-2147483648-f|0)>(0|f))return 1;if(l=f<<1,a?(2147483647-l|0)<(0|f):(-2147483648-l|0)>(0|f))return 1;if(t?(2147483647-n|0)<(0|f):(-2147483648-n|0)>(0|f))return 1;if(t=3*f|0,(0|f)>-1?(-2147483648|t|0)>=(0|o):(-2147483648^t|0)<(0|o))return 1}else t=3*f|0,n=o<<1;return a=0|Je(.14285714285714285*+(n+f|0)),i[A>>2]=a,f=0|Je(.14285714285714285*+(t-o|0)),i[r>>2]=f,i[e>>2]=0,n=(t=(0|f)<(0|a))?a:f,(0|(t=t?f:a))<0&&((-2147483648==(0|t)||((0|n)>0?(2147483647-n|0)<(0|t):(-2147483648-n|0)>(0|t)))&&_(27795,26892,402,26917),((0|n)>-1?(-2147483648|n|0)>=(0|t):(-2147483648^n|0)<(0|t))&&_(27795,26892,402,26917)),n=f-a|0,(0|a)<0?(t=0-a|0,i[r>>2]=n,i[e>>2]=t,i[A>>2]=0,a=0):(n=f,t=0),(0|n)<0&&(i[A>>2]=a=a-n|0,i[e>>2]=t=t-n|0,i[r>>2]=0,n=0),o=a-t|0,f=n-t|0,(0|t)<0?(i[A>>2]=o,i[r>>2]=f,i[e>>2]=0,n=f,f=o,t=0):f=a,(0|(a=(0|t)<(0|(a=(0|n)<(0|f)?n:f))?t:a))<=0?0|(l=0):(i[A>>2]=f-a,i[r>>2]=n-a,i[e>>2]=t-a,0|(l=0))}function sA(A){var e,r,n=0,t=0,a=0,f=0,o=0;a=0|Je(.14285714285714285*+((3*(n=(0|i[(A|=0)>>2])-(t=0|i[(e=A+8|0)>>2])|0)|0)-(t=(0|i[(r=A+4|0)>>2])-t|0)|0)),i[A>>2]=a,n=0|Je(.14285714285714285*+((t<<1)+n|0)),i[r>>2]=n,i[e>>2]=0,t=n-a|0,(0|a)<0?(o=0-a|0,i[r>>2]=t,i[e>>2]=o,i[A>>2]=0,n=t,a=0,t=o):t=0,(0|n)<0&&(i[A>>2]=a=a-n|0,i[e>>2]=t=t-n|0,i[r>>2]=0,n=0),o=a-t|0,f=n-t|0,(0|t)<0?(i[A>>2]=o,i[r>>2]=f,i[e>>2]=0,n=f,f=o,t=0):f=a,(0|(a=(0|t)<(0|(a=(0|n)<(0|f)?n:f))?t:a))<=0||(i[A>>2]=f-a,i[r>>2]=n-a,i[e>>2]=t-a)}function bA(A){var e,r,n=0,t=0,a=0,f=0,o=0;a=0|Je(.14285714285714285*+(((n=(0|i[(A|=0)>>2])-(t=0|i[(e=A+8|0)>>2])|0)<<1)+(t=(0|i[(r=A+4|0)>>2])-t|0)|0)),i[A>>2]=a,n=0|Je(.14285714285714285*+((3*t|0)-n|0)),i[r>>2]=n,i[e>>2]=0,t=n-a|0,(0|a)<0?(o=0-a|0,i[r>>2]=t,i[e>>2]=o,i[A>>2]=0,n=t,a=0,t=o):t=0,(0|n)<0&&(i[A>>2]=a=a-n|0,i[e>>2]=t=t-n|0,i[r>>2]=0,n=0),o=a-t|0,f=n-t|0,(0|t)<0?(i[A>>2]=o,i[r>>2]=f,i[e>>2]=0,n=f,f=o,t=0):f=a,(0|(a=(0|t)<(0|(a=(0|n)<(0|f)?n:f))?t:a))<=0||(i[A>>2]=f-a,i[r>>2]=n-a,i[e>>2]=t-a)}function gA(A){var e,r,n,t=0,a=0,f=0,o=0;f=0|i[(n=8+(A|=0)|0)>>2],i[A>>2]=o=(a=0|i[(r=A+4|0)>>2])+(3*(t=0|i[A>>2])|0)|0,i[r>>2]=a=f+(3*a|0)|0,i[n>>2]=t=(3*f|0)+t|0,f=a-o|0,(0|o)<0?(t=t-o|0,i[r>>2]=f,i[n>>2]=t,i[A>>2]=0,a=f,f=0):f=o,(0|a)<0&&(i[A>>2]=f=f-a|0,i[n>>2]=t=t-a|0,i[r>>2]=0,a=0),e=f-t|0,o=a-t|0,(0|t)<0?(i[A>>2]=e,i[r>>2]=o,i[n>>2]=0,f=e,t=0):o=a,(0|(a=(0|t)<(0|(a=(0|o)<(0|f)?o:f))?t:a))<=0||(i[A>>2]=f-a,i[r>>2]=o-a,i[n>>2]=t-a)}function dA(A){var e,r,n,t=0,a=0,f=0,o=0;f=(3*(t=0|i[(r=4+(A|=0)|0)>>2])|0)+(o=0|i[A>>2])|0,i[A>>2]=o=(a=0|i[(n=A+8|0)>>2])+(3*o|0)|0,i[r>>2]=f,i[n>>2]=t=(3*a|0)+t|0,a=f-o|0,(0|o)<0?(t=t-o|0,i[r>>2]=a,i[n>>2]=t,i[A>>2]=0,o=0):a=f,(0|a)<0&&(i[A>>2]=o=o-a|0,i[n>>2]=t=t-a|0,i[r>>2]=0,a=0),e=o-t|0,f=a-t|0,(0|t)<0?(i[A>>2]=e,i[r>>2]=f,i[n>>2]=0,o=e,t=0):f=a,(0|(a=(0|t)<(0|(a=(0|f)<(0|o)?f:o))?t:a))<=0||(i[A>>2]=o-a,i[r>>2]=f-a,i[n>>2]=t-a)}function wA(A,e){A|=0;var r,n,t,a=0,f=0,o=0;((e|=0)-1|0)>>>0>=6||(i[A>>2]=o=(0|i[15440+(12*e|0)>>2])+(0|i[A>>2])|0,i[(t=A+4|0)>>2]=f=(0|i[15440+(12*e|0)+4>>2])+(0|i[t>>2])|0,i[(n=A+8|0)>>2]=e=(0|i[15440+(12*e|0)+8>>2])+(0|i[n>>2])|0,a=f-o|0,(0|o)<0?(e=e-o|0,i[t>>2]=a,i[n>>2]=e,i[A>>2]=0,f=0):(a=f,f=o),(0|a)<0&&(i[A>>2]=f=f-a|0,i[n>>2]=e=e-a|0,i[t>>2]=0,a=0),r=f-e|0,o=a-e|0,(0|e)<0?(i[A>>2]=r,i[t>>2]=o,i[n>>2]=0,f=r,e=0):o=a,(0|(a=(0|e)<(0|(a=(0|o)<(0|f)?o:f))?e:a))<=0||(i[A>>2]=f-a,i[t>>2]=o-a,i[n>>2]=e-a))}function BA(A){var e,r,n,t=0,a=0,f=0,o=0;f=(t=0|i[(r=4+(A|=0)|0)>>2])+(o=0|i[A>>2])|0,i[A>>2]=o=(a=0|i[(n=A+8|0)>>2])+o|0,i[r>>2]=f,i[n>>2]=t=a+t|0,a=f-o|0,(0|o)<0?(t=t-o|0,i[r>>2]=a,i[n>>2]=t,i[A>>2]=0,f=0):(a=f,f=o),(0|a)<0&&(i[A>>2]=f=f-a|0,i[n>>2]=t=t-a|0,i[r>>2]=0,a=0),e=f-t|0,o=a-t|0,(0|t)<0?(i[A>>2]=e,i[r>>2]=o,i[n>>2]=0,f=e,t=0):o=a,(0|(a=(0|t)<(0|(a=(0|o)<(0|f)?o:f))?t:a))<=0||(i[A>>2]=f-a,i[r>>2]=o-a,i[n>>2]=t-a)}function vA(A){var e,r,n,t=0,a=0,f=0,o=0;a=0|i[(n=8+(A|=0)|0)>>2],i[A>>2]=o=(f=0|i[(r=A+4|0)>>2])+(t=0|i[A>>2])|0,i[r>>2]=f=a+f|0,i[n>>2]=t=a+t|0,a=f-o|0,(0|o)<0?(t=t-o|0,i[r>>2]=a,i[n>>2]=t,i[A>>2]=0,f=0):(a=f,f=o),(0|a)<0&&(i[A>>2]=f=f-a|0,i[n>>2]=t=t-a|0,i[r>>2]=0,a=0),e=f-t|0,o=a-t|0,(0|t)<0?(i[A>>2]=e,i[r>>2]=o,i[n>>2]=0,f=e,t=0):o=a,(0|(a=(0|t)<(0|(a=(0|o)<(0|f)?o:f))?t:a))<=0||(i[A>>2]=f-a,i[r>>2]=o-a,i[n>>2]=t-a)}function kA(A){switch(0|(A|=0)){case 1:A=5;break;case 5:A=4;break;case 4:A=6;break;case 6:A=2;break;case 2:A=3;break;case 3:A=1}return 0|A}function mA(A){switch(0|(A|=0)){case 1:A=3;break;case 3:A=2;break;case 2:A=6;break;case 6:A=4;break;case 4:A=5;break;case 5:A=1}return 0|A}function DA(A){var e,r,n,t=0,a=0,f=0,o=0;f=0|i[(n=8+(A|=0)|0)>>2],i[A>>2]=o=(a=0|i[(r=A+4|0)>>2])+((t=0|i[A>>2])<<1)|0,i[r>>2]=a=f+(a<<1)|0,i[n>>2]=t=(f<<1)+t|0,f=a-o|0,(0|o)<0?(t=t-o|0,i[r>>2]=f,i[n>>2]=t,i[A>>2]=0,a=f,f=0):f=o,(0|a)<0&&(i[A>>2]=f=f-a|0,i[n>>2]=t=t-a|0,i[r>>2]=0,a=0),e=f-t|0,o=a-t|0,(0|t)<0?(i[A>>2]=e,i[r>>2]=o,i[n>>2]=0,f=e,t=0):o=a,(0|(a=(0|t)<(0|(a=(0|o)<(0|f)?o:f))?t:a))<=0||(i[A>>2]=f-a,i[r>>2]=o-a,i[n>>2]=t-a)}function QA(A){var e,r,n,t=0,a=0,f=0,o=0;f=((t=0|i[(r=4+(A|=0)|0)>>2])<<1)+(o=0|i[A>>2])|0,i[A>>2]=o=(a=0|i[(n=A+8|0)>>2])+(o<<1)|0,i[r>>2]=f,i[n>>2]=t=(a<<1)+t|0,a=f-o|0,(0|o)<0?(t=t-o|0,i[r>>2]=a,i[n>>2]=t,i[A>>2]=0,o=0):a=f,(0|a)<0&&(i[A>>2]=o=o-a|0,i[n>>2]=t=t-a|0,i[r>>2]=0,a=0),e=o-t|0,f=a-t|0,(0|t)<0?(i[A>>2]=e,i[r>>2]=f,i[n>>2]=0,o=e,t=0):f=a,(0|(a=(0|t)<(0|(a=(0|f)<(0|o)?f:o))?t:a))<=0||(i[A>>2]=o-a,i[r>>2]=f-a,i[n>>2]=t-a)}function hA(A,e){var r,n,t,a=0,f=0,o=0;return e=(e=(A=(0|(o=((t=(0|(n=(0|i[(A|=0)>>2])-(0|i[(e|=0)>>2])|0))<0)?0-n|0:0)+(0|i[A+8>>2])-(0|i[e+8>>2])+((r=(0|(f=(0|i[A+4>>2])-(0|i[e+4>>2])-(t?n:0)|0))<0)?0-f|0:0)|0))<0)?0:o)-((f=(0|(A=(0|e)<(0|(A=(0|(a=(r?0:f)-(A?o:0)|0))<(0|(o=(t?0:n)-(r?f:0)-(A?o:0)|0))?a:o))?e:A))>0)?A:0)|0,a=a-(f?A:0)|0,0|((0|(A=(0|(A=o-(f?A:0)|0))>-1?A:0-A|0))>(0|(e=(0|(a=(0|a)>-1?a:0-a|0))>(0|(e=(0|e)>-1?e:0-e|0))?a:e))?A:e)}function CA(A,e){var r;i[(e|=0)>>2]=(0|i[(A|=0)>>2])-(r=0|i[A+8>>2]),i[e+4>>2]=(0|i[A+4>>2])-r}function EA(A,e){var r,n,t,a=0,f=0,o=0;if(i[(e|=0)>>2]=f=0|i[(A|=0)>>2],i[(n=e+4|0)>>2]=o=0|i[A+4>>2],i[(t=e+8|0)>>2]=0,A=(a=(0|o)<(0|f))?f:o,(0|(a=a?o:f))<0){if(-2147483648==(0|a)||((0|A)>0?(2147483647-A|0)<(0|a):(-2147483648-A|0)>(0|a)))return 1;if((0|A)>-1?(-2147483648|A|0)>=(0|a):(-2147483648^A|0)<(0|a))return 1}return A=o-f|0,(0|f)<0?(a=0-f|0,i[n>>2]=A,i[t>>2]=a,i[e>>2]=0,f=0):(A=o,a=0),(0|A)<0&&(i[e>>2]=f=f-A|0,i[t>>2]=a=a-A|0,i[n>>2]=0,A=0),r=f-a|0,o=A-a|0,(0|a)<0?(i[e>>2]=r,i[n>>2]=o,i[t>>2]=0,A=o,o=r,a=0):o=f,(0|(f=(0|a)<(0|(f=(0|A)<(0|o)?A:o))?a:f))<=0?0|(e=0):(i[e>>2]=o-f,i[n>>2]=A-f,i[t>>2]=a-f,0|(e=0))}function _A(A){var e,r,n,t;i[(A|=0)>>2]=r=(t=0|i[(e=A+8|0)>>2])-(0|i[A>>2])|0,i[(n=A+4|0)>>2]=A=(0|i[n>>2])-t|0,i[e>>2]=0-(A+r)}function yA(A){var e,r,n=0,t=0,a=0,f=0,o=0;i[(A|=0)>>2]=n=0-(t=0|i[A>>2])|0,i[(e=A+8|0)>>2]=0,f=(a=0|i[(r=A+4|0)>>2])+t|0,(0|t)>0?(i[r>>2]=f,i[e>>2]=t,i[A>>2]=0,n=0,a=f):t=0,(0|a)<0?(i[A>>2]=o=n-a|0,i[e>>2]=t=t-a|0,i[r>>2]=0,f=o-t|0,n=0-t|0,(0|t)<0?(i[A>>2]=f,i[r>>2]=n,i[e>>2]=0,a=n,t=0):(a=0,f=o)):f=n,(0|(n=(0|t)<(0|(n=(0|a)<(0|f)?a:f))?t:n))<=0||(i[A>>2]=f-n,i[r>>2]=a-n,i[e>>2]=t-n)}function MA(A,e,r){r|=0;var n,t,a=0,f=0;return t=T,T=T+16|0,n=t,f=-2130706433&(e|=0)|134217728,!0&268435456==(2013265920&e|0)?(a=0|ar(0|(A|=0),0|e,56),E(),-1==(0|(a=0|function(A,e,r){r|=0;var n,t,a=0;if(n=T,T=T+16|0,t=n,a=0|SA(A|=0,e|=0),(r+-1|0)>>>0>5)return T=n,-1;if(1==(0|r)&(a=0!=(0|a)))return T=n,-1;do{if(!(0|xe(A,e,t))){if(a){a=(5+(0|i[26352+(r<<2)>>2])-(0|i[t>>2])|0)%5|0;break}a=(6+(0|i[26384+(r<<2)>>2])-(0|i[t>>2])|0)%6|0;break}a=-1}while(0);return T=n,0|a}(A,f,7&a)))?(i[r>>2]=0,T=t,0|(f=6)):(0|Ae(A,f,n)&&_(27795,26932,282,26947),e=0|ar(0|A,0|e,52),E(),e&=15,0|SA(A,f)?TA(n,e,a,2,r):zA(n,e,a,2,r),T=t,0|(f=0))):(T=t,0|(f=6))}function PA(A,e,r,n){e|=0,n|=0;var a,f=0,o=0,l=0,u=0;if(a=T,T=T+16|0,function(A,e,r){e|=0,r|=0;var n,a,f=0;a=T,T=T+32|0,function(A,e){e|=0;var r,n,i=0;r=+s(+(i=+t[(A|=0)>>3])),i=+b(+i),t[e+16>>3]=i,n=r*+s(+(i=+t[A+8>>3])),t[e>>3]=n,i=r*+b(+i),t[e+8>>3]=i}(A|=0,n=a),i[e>>2]=0,t[r>>3]=5,(f=+Ue(16400,n))<+t[r>>3]&&(i[e>>2]=0,t[r>>3]=f),(f=+Ue(16424,n))<+t[r>>3]&&(i[e>>2]=1,t[r>>3]=f),(f=+Ue(16448,n))<+t[r>>3]&&(i[e>>2]=2,t[r>>3]=f),(f=+Ue(16472,n))<+t[r>>3]&&(i[e>>2]=3,t[r>>3]=f),(f=+Ue(16496,n))<+t[r>>3]&&(i[e>>2]=4,t[r>>3]=f),(f=+Ue(16520,n))<+t[r>>3]&&(i[e>>2]=5,t[r>>3]=f),(f=+Ue(16544,n))<+t[r>>3]&&(i[e>>2]=6,t[r>>3]=f),(f=+Ue(16568,n))<+t[r>>3]&&(i[e>>2]=7,t[r>>3]=f),(f=+Ue(16592,n))<+t[r>>3]&&(i[e>>2]=8,t[r>>3]=f),(f=+Ue(16616,n))<+t[r>>3]&&(i[e>>2]=9,t[r>>3]=f),(f=+Ue(16640,n))<+t[r>>3]&&(i[e>>2]=10,t[r>>3]=f),(f=+Ue(16664,n))<+t[r>>3]&&(i[e>>2]=11,t[r>>3]=f),(f=+Ue(16688,n))<+t[r>>3]&&(i[e>>2]=12,t[r>>3]=f),(f=+Ue(16712,n))<+t[r>>3]&&(i[e>>2]=13,t[r>>3]=f),(f=+Ue(16736,n))<+t[r>>3]&&(i[e>>2]=14,t[r>>3]=f),(f=+Ue(16760,n))<+t[r>>3]&&(i[e>>2]=15,t[r>>3]=f),(f=+Ue(16784,n))<+t[r>>3]&&(i[e>>2]=16,t[r>>3]=f),(f=+Ue(16808,n))<+t[r>>3]&&(i[e>>2]=17,t[r>>3]=f),(f=+Ue(16832,n))<+t[r>>3]&&(i[e>>2]=18,t[r>>3]=f),(f=+Ue(16856,n))<+t[r>>3]?(i[e>>2]=19,t[r>>3]=f,T=a):T=a}(A|=0,r|=0,u=a),(o=+d(+(1-.5*+t[u>>3])))<1e-16)return i[n>>2]=0,i[n+4>>2]=0,i[n+8>>2]=0,i[n+12>>2]=0,void(T=a);if(f=+fe((f=+t[15920+(24*(u=0|i[r>>2])|0)>>3])-+fe(+function(A,e){var r,n,i,a,f;return A|=0,f=(n=+s(+(a=+t[(e|=0)>>3])))*+b(+(i=+t[e+8>>3]-+t[A+8>>3])),r=+t[A>>3],+ +v(+f,+(+b(+a)*+s(+r)-+s(+i)*(n*+b(+r))))}(15600+(u<<4)|0,A))),l=0|XA(e)?+fe(f+-.3334731722518321):f,f=2.618033988749896*+g(+o),(0|e)>0){A=0;do{f*=2.6457513110645907,A=A+1|0}while((0|A)!=(0|e))}o=+s(+l)*f,t[n>>3]=o,l=+b(+l)*f,t[n+8>>3]=l,T=a}function IA(A,e,r,n,a){e|=0,r|=0,n|=0,a|=0;var f,o=0,c=0;if((o=+function(A){var e,r;return+ +u(+((r=+t[(A|=0)>>3])*r+(e=+t[A+8>>3])*e))}(A|=0))<1e-16)return i[a>>2]=i[(e=15600+(e<<4)|0)>>2],i[a+4>>2]=i[e+4>>2],i[a+8>>2]=i[e+8>>2],void(i[a+12>>2]=i[e+12>>2]);if(c=+v(+ +t[A+8>>3],+ +t[A>>3]),(0|r)>0){A=0;do{o*=.37796447300922725,A=A+1|0}while((0|A)!=(0|r))}f=.3333333333333333*o,n?(r=0==(0|XA(r)),o=+B(.381966011250105*(r?f:.37796447300922725*f))):(o=+B(.381966011250105*o),0|XA(r)&&(c=+fe(c+.3334731722518321))),function(A,e,r,n){A|=0,e=+e,n|=0;var a=0,f=0,o=0,u=0;if((r=+r)<1e-16)return i[n>>2]=i[A>>2],i[n+4>>2]=i[A+4>>2],i[n+8>>2]=i[A+8>>2],void(i[n+12>>2]=i[A+12>>2]);f=e<0?e+6.283185307179586:e,f=e>=6.283185307179586?f+-6.283185307179586:f;do{if(!(f<1e-16)){if(a=+l(+(f+-3.141592653589793))<1e-16,e=+t[A>>3],a){t[n>>3]=e-=r,a=n;break}if(o=+s(+r),r=+b(+r),e=o*+b(+e)+ +s(+f)*(r*+s(+e)),e=+w(+((e=e>1?1:e)<-1?-1:e)),t[n>>3]=e,+l(+(e+-1.5707963267948966))<1e-16)return t[n>>3]=1.5707963267948966,void(t[n+8>>3]=0);if(+l(+(e+1.5707963267948966))<1e-16)return t[n>>3]=-1.5707963267948966,void(t[n+8>>3]=0);if(u=1/+s(+e),f=r*+b(+f)*u,r=+t[A>>3],e=u*((o-+b(+e)*+b(+r))/+s(+r)),(e=+t[A+8>>3]+ +v(+((o=f>1?1:f)<-1?-1:o),+((e=e>1?1:e)<-1?-1:e)))>3.141592653589793)do{e+=-6.283185307179586}while(e>3.141592653589793);if(e<-3.141592653589793)do{e+=6.283185307179586}while(e<-3.141592653589793);return void(t[n+8>>3]=e)}t[n>>3]=e=+t[A>>3]+r,a=n}while(0);if(+l(+(e+-1.5707963267948966))<1e-16)return t[a>>3]=1.5707963267948966,void(t[n+8>>3]=0);if(+l(+(e+1.5707963267948966))<1e-16)return t[a>>3]=-1.5707963267948966,void(t[n+8>>3]=0);if((e=+t[A+8>>3])>3.141592653589793)do{e+=-6.283185307179586}while(e>3.141592653589793);if(e<-3.141592653589793)do{e+=6.283185307179586}while(e<-3.141592653589793);t[n+8>>3]=e}(15600+(e<<4)|0,+fe(+t[15920+(24*e|0)>>3]-c),o,a)}function pA(A,e,r){var n,t;e|=0,r|=0,n=T,T=T+16|0,tA(4+(A|=0)|0,t=n),IA(t,0|i[A>>2],e,0,r),T=n}function TA(A,e,r,n,a){A|=0,r|=0,n|=0,a|=0;var f,o,l,u,c,s,b,g,d,w,B,v,k,m,D,Q,h,C,E,y,M,P,I=0,p=0,F=0,L=0,G=0,z=0;if(P=T,T=T+272|0,L=P+240|0,E=P,y=P+224|0,M=P+208|0,B=P+176|0,v=P+160|0,k=P+192|0,m=P+144|0,D=P+128|0,Q=P+112|0,h=P+96|0,C=P+80|0,i[(I=P+256|0)>>2]=e|=0,i[L>>2]=i[A>>2],i[L+4>>2]=i[A+4>>2],i[L+8>>2]=i[A+8>>2],i[L+12>>2]=i[A+12>>2],FA(L,I,E),i[a>>2]=0,(0|(L=n+r+(5==(0|n)&1)|0))<=(0|r))T=P;else{o=y+4|0,l=B+4|0,u=r+5|0,c=16880+((f=0|i[I>>2])<<2)|0,s=16960+(f<<2)|0,b=D+8|0,g=Q+8|0,d=h+8|0,w=M+4|0,F=r;A:for(;;){i[M>>2]=i[(p=E+(((0|F)%5|0)<<4)|0)>>2],i[M+4>>2]=i[p+4>>2],i[M+8>>2]=i[p+8>>2],i[M+12>>2]=i[p+12>>2];do{}while(2==(0|LA(M,f,0,1)));if((0|F)>(0|r)&0!=(0|XA(e))){if(i[B>>2]=i[M>>2],i[B+4>>2]=i[M+4>>2],i[B+8>>2]=i[M+8>>2],i[B+12>>2]=i[M+12>>2],tA(o,v),i[B>>2]=i[18640+(80*(n=0|i[B>>2])|0)+(20*(I=0|i[17040+(80*n|0)+(i[y>>2]<<2)>>2])|0)>>2],(0|(p=0|i[18640+(80*n|0)+(20*I|0)+16>>2]))>0){A=0;do{BA(l),A=A+1|0}while((0|A)<(0|p))}switch(i[k>>2]=i[(p=18640+(80*n|0)+(20*I|0)+4|0)>>2],i[k+4>>2]=i[p+4>>2],i[k+8>>2]=i[p+8>>2],oA(k,3*(0|i[c>>2])|0),aA(l,k,l),iA(l),tA(l,m),t[D>>3]=3*(G=+(0|i[s>>2])),t[b>>3]=0,t[Q>>3]=z=-1.5*G,t[g>>3]=2.598076211353316*G,t[h>>3]=z,t[d>>3]=-2.598076211353316*G,0|i[17040+(80*(0|i[B>>2])|0)+(i[M>>2]<<2)>>2]){case 1:A=Q,n=D;break;case 3:A=h,n=Q;break;case 2:A=D,n=h;break;default:A=12;break A}Ge(v,m,n,A,C),IA(C,0|i[B>>2],f,1,a+8+(i[a>>2]<<4)|0),i[a>>2]=1+(0|i[a>>2])}if((0|F)<(0|u)&&(tA(w,B),IA(B,0|i[M>>2],f,1,a+8+(i[a>>2]<<4)|0),i[a>>2]=1+(0|i[a>>2])),i[y>>2]=i[M>>2],i[y+4>>2]=i[M+4>>2],i[y+8>>2]=i[M+8>>2],i[y+12>>2]=i[M+12>>2],(0|(F=F+1|0))>=(0|L)){A=3;break}}3!=(0|A)?12==(0|A)&&_(26970,27017,572,27027):T=P}}function FA(A,e,r){A|=0,e|=0,r|=0;var n,t=0,a=0,f=0,o=0,l=0;n=T,T=T+128|0,a=n,o=20240,l=60+(f=t=n+64|0)|0;do{i[f>>2]=i[o>>2],f=f+4|0,o=o+4|0}while((0|f)<(0|l));o=20304,l=(f=a)+60|0;do{i[f>>2]=i[o>>2],f=f+4|0,o=o+4|0}while((0|f)<(0|l));t=(l=0==(0|XA(0|i[e>>2])))?t:a,DA(a=A+4|0),QA(a),0|XA(0|i[e>>2])&&(dA(a),i[e>>2]=1+(0|i[e>>2])),i[r>>2]=i[A>>2],aA(a,t,e=r+4|0),iA(e),i[r+16>>2]=i[A>>2],aA(a,t+12|0,e=r+20|0),iA(e),i[r+32>>2]=i[A>>2],aA(a,t+24|0,e=r+36|0),iA(e),i[r+48>>2]=i[A>>2],aA(a,t+36|0,e=r+52|0),iA(e),i[r+64>>2]=i[A>>2],aA(a,t+48|0,r=r+68|0),iA(r),T=n}function LA(A,e,r,n){r|=0;var t,a,f,o,l,u,c,s=0,b=0,g=0,d=0;if(u=T,T=T+32|0,l=u+12|0,a=u,d=0|i[16960+((e|=0)<<2)>>2],d=(o=0!=(0|(n|=0)))?3*d|0:d,s=0|i[(c=4+(A|=0)|0)>>2],t=0|i[(f=A+8|0)>>2],o){if((0|(s=t+s+(n=0|i[(b=A+12|0)>>2])|0))==(0|d))return T=u,1;g=b}else s=t+s+(n=0|i[(g=A+12|0)>>2])|0;if((0|s)<=(0|d))return T=u,0;do{if((0|n)>0){if(n=0|i[A>>2],(0|t)>0){b=18640+(80*n|0)+60|0,n=A;break}n=18640+(80*n|0)+40|0,r?(rA(l,d,0,0),fA(c,l,a),vA(a),aA(a,l,c),b=n,n=A):(b=n,n=A)}else b=18640+(80*(0|i[A>>2])|0)+20|0,n=A}while(0);if(i[n>>2]=i[b>>2],(0|i[(s=b+16|0)>>2])>0){n=0;do{BA(c),n=n+1|0}while((0|n)<(0|i[s>>2]))}return i[l>>2]=i[(A=b+4|0)>>2],i[l+4>>2]=i[A+4>>2],i[l+8>>2]=i[A+8>>2],e=0|i[16880+(e<<2)>>2],oA(l,o?3*e|0:e),aA(c,l,c),iA(c),T=u,0|(o&&((0|i[f>>2])+(0|i[c>>2])+(0|i[g>>2])|0)==(0|d)?1:2)}function GA(A,e){A|=0,e|=0;var r=0;do{r=0|LA(A,e,0,1)}while(2==(0|r));return 0|r}function zA(A,e,r,n,a){A|=0,r|=0,n|=0,a|=0;var f,o,l,u,c,s,b,g,d,w,B,v,k,m,D,Q,h,C,E=0,y=0,M=0,P=0,I=0;if(C=T,T=T+240|0,m=C+208|0,D=C,Q=C+192|0,h=C+176|0,d=C+160|0,w=C+144|0,B=C+128|0,v=C+112|0,k=C+96|0,i[(E=C+224|0)>>2]=e|=0,i[m>>2]=i[A>>2],i[m+4>>2]=i[A+4>>2],i[m+8>>2]=i[A+8>>2],i[m+12>>2]=i[A+12>>2],UA(m,E,D),i[a>>2]=0,(0|(g=n+r+(6==(0|n)&1)|0))<=(0|r))T=C;else{o=r+6|0,l=16960+((f=0|i[E>>2])<<2)|0,u=w+8|0,c=B+8|0,s=v+8|0,b=Q+4|0,y=0,M=r,n=-1;A:for(;;){if(i[Q>>2]=i[(A=D+((E=(0|M)%6|0)<<4)|0)>>2],i[Q+4>>2]=i[A+4>>2],i[Q+8>>2]=i[A+8>>2],i[Q+12>>2]=i[A+12>>2],A=y,y=0|LA(Q,f,0,1),(0|M)>(0|r)&0!=(0|XA(e))&&1!=(0|A)&&(0|i[Q>>2])!=(0|n)){switch(tA(D+(((E+5|0)%6|0)<<4)+4|0,h),tA(D+(E<<4)+4|0,d),t[w>>3]=3*(P=+(0|i[l>>2])),t[u>>3]=0,t[B>>3]=I=-1.5*P,t[c>>3]=2.598076211353316*P,t[v>>3]=I,t[s>>3]=-2.598076211353316*P,0|i[17040+(80*(E=0|i[m>>2])|0)+(((0|n)==(0|E)?0|i[Q>>2]:n)<<2)>>2]){case 1:A=B,n=w;break;case 3:A=v,n=B;break;case 2:A=w,n=v;break;default:A=8;break A}Ge(h,d,n,A,k),0|ze(h,k)||0|ze(d,k)||(IA(k,0|i[m>>2],f,1,a+8+(i[a>>2]<<4)|0),i[a>>2]=1+(0|i[a>>2]))}if((0|M)<(0|o)&&(tA(b,h),IA(h,0|i[Q>>2],f,1,a+8+(i[a>>2]<<4)|0),i[a>>2]=1+(0|i[a>>2])),(0|(M=M+1|0))>=(0|g)){A=3;break}n=0|i[Q>>2]}3!=(0|A)?8==(0|A)&&_(27054,27017,737,27099):T=C}}function UA(A,e,r){A|=0,e|=0,r|=0;var n,t=0,a=0,f=0,o=0,l=0;n=T,T=T+160|0,a=n,o=20368,l=72+(f=t=n+80|0)|0;do{i[f>>2]=i[o>>2],f=f+4|0,o=o+4|0}while((0|f)<(0|l));o=20448,l=(f=a)+72|0;do{i[f>>2]=i[o>>2],f=f+4|0,o=o+4|0}while((0|f)<(0|l));t=(l=0==(0|XA(0|i[e>>2])))?t:a,DA(a=A+4|0),QA(a),0|XA(0|i[e>>2])&&(dA(a),i[e>>2]=1+(0|i[e>>2])),i[r>>2]=i[A>>2],aA(a,t,e=r+4|0),iA(e),i[r+16>>2]=i[A>>2],aA(a,t+12|0,e=r+20|0),iA(e),i[r+32>>2]=i[A>>2],aA(a,t+24|0,e=r+36|0),iA(e),i[r+48>>2]=i[A>>2],aA(a,t+36|0,e=r+52|0),iA(e),i[r+64>>2]=i[A>>2],aA(a,t+48|0,e=r+68|0),iA(e),i[r+80>>2]=i[A>>2],aA(a,t+60|0,r=r+84|0),iA(r),T=n}function xA(A,e){return e=0|ar(0|(A|=0),0|(e|=0),45),E(),127&e|0}function RA(A,e){A|=0;var r=0,i=0,t=0,a=0;return!0&134217728==(-16777216&(e|=0)|0)?(i=0|ar(0|A,0|e,52),E(),i&=15,r=0|ar(0|A,0|e,45),E(),(r&=127)>>>0>121?0|(A=0):0==(613566756&(a=0|fr(0|(t=0|ar(0|A,0|e,0|(a=3*(15^i)|0))),0|E(),0|a))&(0|qe(-1227133514,-1171,0|a,0|(t=0|E())))|0)&0==(4681&t&(0|E())|0)&&15==(0|i)|0==(0|(a=0|ar(0|fr(0|~A,0|~e,0|(a=19+(3*i|0)|0)),0|E(),0|a)))&0==(0|E())?0|n[20528+r>>0]?0==(0|A)&0==(0|(e&=8191))?0|(a=1):(a=0|or(0|A,0|e),E(),0!=(0|(63-a|0)%3)|0):0|(a=1):0|(a=0)):0|(A=0)}function YA(A,e,r,n){A|=0,r|=0,n|=0;var t=0,a=0,f=0,o=0;if(t=0|fr(0|(e|=0),0,52),a=0|E(),r=0|fr(0|r,0,45),r=0|a|E()|134225919,(0|e)<1)return n=r,i[(e=A)>>2]=a=-1,void(i[(A=A+4|0)>>2]=n);for(a=1,t=-1;o=0|fr(7,0,0|(f=3*(15-a|0)|0)),r&=~(0|E()),t=t&~o|(f=0|fr(0|n,0,0|f)),r=0|r|E(),(0|a)!=(0|e);)a=a+1|0;i[(f=o=A)>>2]=t,i[(o=o+4|0)>>2]=r}function VA(A,e,r,n){r|=0,n|=0;var t=0,a=0;if(a=0|ar(0|(A|=0),0|(e|=0),52),E(),r>>>0>15)return 4;if((0|(a&=15))<(0|r))return 12;if((0|a)==(0|r))return i[n>>2]=A,i[n+4>>2]=e,0;if(t=0|fr(0|r,0,52),t|=A,A=0|E()|-15728641&e,(0|a)>(0|r))do{e=0|fr(7,0,3*(14-r|0)|0),r=r+1|0,t|=e,A=0|E()|A}while((0|r)<(0|a));return i[n>>2]=t,i[n+4>>2]=A,0}function OA(A,e,r,n){r|=0,n|=0;var t=0,a=0,f=0;if(a=0|ar(0|(A|=0),0|(e|=0),52),E(),!((0|r)<16&(0|(a&=15))<=(0|r)))return 4;t=r-a|0,r=0|ar(0|A,0|e,45),E();A:do{if(0|Y(127&r)){e:do{if(0|a){for(r=1;0==((f=0|fr(7,0,3*(15-r|0)|0))&A|0)&0==((0|E())&e|0);){if(!(r>>>0<a>>>0))break e;r=r+1|0}r=0|Qe(7,0,t,((0|t)<0)<<31>>31),t=0|E();break A}}while(0);r=0|Xe(0|(r=0|rr(0|(r=0|Qe(7,0,t,((0|t)<0)<<31>>31)),0|E(),5,0)),0|E(),-5,-1),r=0|Xe(0|(r=0|er(0|r,0|E(),6,0)),0|E(),1,0),t=0|E()}else r=0|Qe(7,0,t,((0|t)<0)<<31>>31),t=0|E()}while(0);return i[(f=n)>>2]=r,i[f+4>>2]=t,0}function SA(A,e){var r=0,n=0,i=0;if(i=0|ar(0|(A|=0),0|(e|=0),45),E(),!(0|Y(127&i)))return 0;i=0|ar(0|A,0|e,52),E(),i&=15;A:do{if(i)for(n=1;;){if(r=0|ar(0|A,0|e,3*(15-n|0)|0),E(),0|(r&=7))break A;if(!(n>>>0<i>>>0)){r=0;break}n=n+1|0}else r=0}while(0);return 0|0==(0|r)&1}function HA(A,e,r,n){return A|=0,e|=0,(0|(n|=0))<(0|(r|=0))?(n=A,C(0|(r=e)),0|n):(r=0|fr(-1,-1,3+(3*(n-r|0)|0)|0),n=0|fr(0|~r,0|~(0|E()),3*(15-n|0)|0),r=~(0|E())&e,n=~n&A,C(0|r),0|n)}function WA(A,e,r,n){r|=0,n|=0;var t=0;return t=0|ar(0|(A|=0),0|(e|=0),52),E(),(0|r)<16&(0|(t&=15))<=(0|r)?((0|t)<(0|r)&&(t=0|fr(-1,-1,3+(3*(r+-1-t|0)|0)|0),t=0|fr(0|~t,0|~(0|E()),3*(15-r|0)|0),e=~(0|E())&e,A&=~t),t=0|fr(0|r,0,52),r=-15728641&e|0|E(),i[n>>2]=A|t,i[n+4>>2]=r,0|(n=0)):0|(n=4)}function ZA(A,e){var r=0,n=0,i=0;if(i=0|ar(0|(A|=0),0|(e|=0),52),E(),!(i&=15))return 0;for(n=1;;){if(r=0|ar(0|A,0|e,3*(15-n|0)|0),E(),0|(r&=7)){n=5;break}if(!(n>>>0<i>>>0)){r=0,n=5;break}n=n+1|0}return 5==(0|n)?0|r:0}function JA(A,e){var r=0,n=0,i=0,t=0,a=0,f=0,o=0;if(o=0|ar(0|(A|=0),0|(e|=0),52),E(),!(o&=15))return o=A,C(0|(f=e)),0|o;for(f=1,r=0;;){n=0|fr(7,0,0|(t=3*(15-f|0)|0)),i=0|E(),a=0|ar(0|A,0|e,0|t),E(),A=(t=0|fr(0|kA(7&a),0,0|t))|A&~n,e=(a=0|E())|e&~i;A:do{if(!r)if(0==(t&n|0)&0==(a&i|0))r=0;else if(n=0|ar(0|A,0|e,52),E(),n&=15){r=1;e:for(;;){switch(a=0|ar(0|A,0|e,3*(15-r|0)|0),E(),7&a){case 1:break e;case 0:break;default:r=1;break A}if(!(r>>>0<n>>>0)){r=1;break A}r=r+1|0}for(r=1;;){if(i=0|ar(0|A,0|e,0|(a=3*(15-r|0)|0)),E(),t=0|fr(7,0,0|a),e&=~(0|E()),A=A&~t|(a=0|fr(0|kA(7&i),0,0|a)),e=0|e|E(),!(r>>>0<n>>>0)){r=1;break}r=r+1|0}}else r=1}while(0);if(!(f>>>0<o>>>0))break;f=f+1|0}return C(0|e),0|A}function KA(A,e){var r=0,n=0,i=0,t=0,a=0;if(n=0|ar(0|(A|=0),0|(e|=0),52),E(),!(n&=15))return n=A,C(0|(r=e)),0|n;for(r=1;a=0|ar(0|A,0|e,0|(t=3*(15-r|0)|0)),E(),i=0|fr(7,0,0|t),e&=~(0|E()),A=(t=0|fr(0|kA(7&a),0,0|t))|A&~i,e=0|E()|e,r>>>0<n>>>0;)r=r+1|0;return C(0|e),0|A}function jA(A,e){var r=0,n=0,i=0,t=0,a=0,f=0,o=0;if(o=0|ar(0|(A|=0),0|(e|=0),52),E(),!(o&=15))return o=A,C(0|(f=e)),0|o;for(f=1,r=0;;){n=0|fr(7,0,0|(t=3*(15-f|0)|0)),i=0|E(),a=0|ar(0|A,0|e,0|t),E(),A=(t=0|fr(0|mA(7&a),0,0|t))|A&~n,e=(a=0|E())|e&~i;A:do{if(!r)if(0==(t&n|0)&0==(a&i|0))r=0;else if(n=0|ar(0|A,0|e,52),E(),n&=15){r=1;e:for(;;){switch(a=0|ar(0|A,0|e,3*(15-r|0)|0),E(),7&a){case 1:break e;case 0:break;default:r=1;break A}if(!(r>>>0<n>>>0)){r=1;break A}r=r+1|0}for(r=1;;){if(t=0|fr(7,0,0|(i=3*(15-r|0)|0)),a=e&~(0|E()),e=0|ar(0|A,0|e,0|i),E(),A=A&~t|(e=0|fr(0|mA(7&e),0,0|i)),e=0|a|E(),!(r>>>0<n>>>0)){r=1;break}r=r+1|0}}else r=1}while(0);if(!(f>>>0<o>>>0))break;f=f+1|0}return C(0|e),0|A}function NA(A,e){var r=0,n=0,i=0,t=0,a=0;if(n=0|ar(0|(A|=0),0|(e|=0),52),E(),!(n&=15))return n=A,C(0|(r=e)),0|n;for(r=1;t=0|fr(7,0,0|(a=3*(15-r|0)|0)),i=e&~(0|E()),e=0|ar(0|A,0|e,0|a),E(),A=(e=0|fr(0|mA(7&e),0,0|a))|A&~t,e=0|E()|i,r>>>0<n>>>0;)r=r+1|0;return C(0|e),0|A}function XA(A){return 0|(0|(A|=0))%2}function qA(A,e,r){r|=0;var n,t=0;return n=T,T=T+16|0,t=n,(e|=0)>>>0>15?(T=n,0|(t=4)):2146435072==(2146435072&i[4+(A|=0)>>2]|0)?(T=n,0|(t=3)):2146435072==(2146435072&i[A+8+4>>2]|0)?(T=n,0|(t=3)):(function(A,e,r){var n,i;n=T,T=T+16|0,PA(A|=0,e|=0,r|=0,i=n),nA(i,r+4|0),T=n}(A,e,t),e=0|function(A,e){A|=0;var r,n=0,t=0,a=0,f=0,o=0,l=0,u=0,c=0;if(r=T,T=T+64|0,l=r+40|0,t=r+24|0,a=r+12|0,f=r,fr(0|(e|=0),0,52),n=134225919|E(),!e)return(0|i[A+4>>2])>2?(l=0,C(0|(o=0)),T=r,0|l):(0|i[A+8>>2])>2?(l=0,C(0|(o=0)),T=r,0|l):(0|i[A+12>>2])>2?(l=0,C(0|(o=0)),T=r,0|l):(fr(0|O(A),0,45),o=0|E()|n,l=-1,C(0|o),T=r,0|l);if(i[l>>2]=i[A>>2],i[l+4>>2]=i[A+4>>2],i[l+8>>2]=i[A+8>>2],i[l+12>>2]=i[A+12>>2],o=l+4|0,(0|e)>0)for(A=-1;i[t>>2]=i[o>>2],i[t+4>>2]=i[o+4>>2],i[t+8>>2]=i[o+8>>2],1&e?(sA(o),i[a>>2]=i[o>>2],i[a+4>>2]=i[o+4>>2],i[a+8>>2]=i[o+8>>2],gA(a)):(bA(o),i[a>>2]=i[o>>2],i[a+4>>2]=i[o+4>>2],i[a+8>>2]=i[o+8>>2],dA(a)),fA(t,a,f),iA(f),u=0|fr(7,0,0|(c=3*(15-e|0)|0)),n&=~(0|E()),A=(c=0|fr(0|lA(f),0,0|c))|A&~u,n=0|E()|n,(0|e)>1;)e=e+-1|0;else A=-1;A:do{if((0|i[o>>2])<=2&&(0|i[l+8>>2])<=2&&(0|i[l+12>>2])<=2){if(e=0|fr(0|(t=0|O(l)),0,45),e|=A,A=0|E()|-1040385&n,f=0|S(l),!(0|Y(t))){if((0|f)<=0)break;for(a=0;;){if(t=0|ar(0|e,0|A,52),E(),t&=15)for(n=1;l=0|ar(0|e,0|A,0|(c=3*(15-n|0)|0)),E(),u=0|fr(7,0,0|c),A&=~(0|E()),e=e&~u|(c=0|fr(0|kA(7&l),0,0|c)),A=0|A|E(),n>>>0<t>>>0;)n=n+1|0;if((0|(a=a+1|0))==(0|f))break A}}a=0|ar(0|e,0|A,52),E(),a&=15;e:do{if(a){n=1;r:for(;;){switch(c=0|ar(0|e,0|A,3*(15-n|0)|0),E(),7&c){case 1:break r;case 0:break;default:break e}if(!(n>>>0<a>>>0))break e;n=n+1|0}if(0|H(t,0|i[l>>2]))for(n=1;u=0|fr(7,0,0|(l=3*(15-n|0)|0)),c=A&~(0|E()),A=0|ar(0|e,0|A,0|l),E(),e=e&~u|(A=0|fr(0|mA(7&A),0,0|l)),A=0|c|E(),n>>>0<a>>>0;)n=n+1|0;else for(n=1;l=0|ar(0|e,0|A,0|(c=3*(15-n|0)|0)),E(),u=0|fr(7,0,0|c),A&=~(0|E()),e=e&~u|(c=0|fr(0|kA(7&l),0,0|c)),A=0|A|E(),n>>>0<a>>>0;)n=n+1|0}}while(0);if((0|f)>0){n=0;do{e=0|JA(e,A),A=0|E(),n=n+1|0}while((0|n)!=(0|f))}}else e=0,A=0}while(0);return c=e,C(0|(u=A)),T=r,0|c}(t,e),t=0|E(),i[r>>2]=e,i[r+4>>2]=t,0==(0|e)&0==(0|t)&&_(27795,27122,959,27145),T=n,0|(t=0))}function $A(A,e,r){var n,t=0,a=0,f=0;if(n=4+(r|=0)|0,a=0|ar(0|(A|=0),0|(e|=0),52),E(),a&=15,f=0|ar(0|A,0|e,45),E(),t=0==(0|a),0|Y(127&f)){if(t)return 1;t=1}else{if(t)return 0;t=0==(0|i[n>>2])&&0==(0|i[r+8>>2])?0!=(0|i[r+12>>2])&1:1}for(r=1;1&r?gA(n):dA(n),f=0|ar(0|A,0|e,3*(15-r|0)|0),E(),wA(n,7&f),r>>>0<a>>>0;)r=r+1|0;return 0|t}function Ae(A,e,r){r|=0;var n,t,a=0,f=0,o=0,l=0,u=0,c=0;if(t=T,T=T+16|0,n=t,c=0|ar(0|(A|=0),0|(e|=0),45),E(),(c&=127)>>>0>121)return i[r>>2]=0,i[r+4>>2]=0,i[r+8>>2]=0,i[r+12>>2]=0,T=t,5;A:do{if(0!=(0|Y(c))&&(o=0|ar(0|A,0|e,52),E(),0!=(0|(o&=15)))){a=1;e:for(;;){switch(u=0|ar(0|A,0|e,3*(15-a|0)|0),E(),7&u){case 5:break e;case 0:break;default:a=e;break A}if(!(a>>>0<o>>>0)){a=e;break A}a=a+1|0}for(f=1,a=e;l=0|fr(7,0,0|(e=3*(15-f|0)|0)),u=a&~(0|E()),a=0|ar(0|A,0|a,0|e),E(),A=A&~l|(a=0|fr(0|mA(7&a),0,0|e)),a=0|u|E(),f>>>0<o>>>0;)f=f+1|0}else a=e}while(0);if(i[r>>2]=i[(u=7696+(28*c|0)|0)>>2],i[r+4>>2]=i[u+4>>2],i[r+8>>2]=i[u+8>>2],i[r+12>>2]=i[u+12>>2],!(0|$A(A,a,r)))return T=t,0;if(i[n>>2]=i[(l=r+4|0)>>2],i[n+4>>2]=i[l+4>>2],i[n+8>>2]=i[l+8>>2],o=0|ar(0|A,0|a,52),E(),u=15&o,1&o?(dA(l),o=u+1|0):o=u,0|Y(c)){A:do{if(u)for(e=1;;){if(f=0|ar(0|A,0|a,3*(15-e|0)|0),E(),0|(f&=7)){a=f;break A}if(!(e>>>0<u>>>0)){a=0;break}e=e+1|0}else a=0}while(0);a=4==(0|a)&1}else a=0;if(0|LA(r,o,a,0)){if(0|Y(c))do{}while(0!=(0|LA(r,o,0,0)));(0|o)!=(0|u)&&bA(l)}else(0|o)!=(0|u)&&(i[l>>2]=i[n>>2],i[l+4>>2]=i[n+4>>2],i[l+8>>2]=i[n+8>>2]);return T=t,0}function ee(A,e,r){r|=0;var n,i,t=0;return i=T,T=T+16|0,0|(t=0|Ae(A|=0,e|=0,n=i))?(T=i,0|t):(t=0|ar(0|A,0|e,52),E(),pA(n,15&t,r),T=i,0|(t=0))}function re(A,e,r){r|=0;var n,i,t=0,a=0;if(n=T,T=T+16|0,0|(t=0|Ae(A|=0,e|=0,i=n)))return T=n,0|t;t=0|ar(0|A,0|e,45),E(),t=0==(0|Y(127&t)),a=0|ar(0|A,0|e,52),E(),a&=15;A:do{if(!t){if(0|a)for(t=1;;){if(!(0==((0|fr(7,0,3*(15-t|0)|0))&A|0)&0==((0|E())&e|0)))break A;if(!(t>>>0<a>>>0))break;t=t+1|0}return TA(i,a,0,5,r),T=n,0}}while(0);return zA(i,a,0,6,r),T=n,0}function ne(A,e){e|=0;var r,n=0,t=0,a=0,f=0,o=0,l=0;if((A|=0)>>>0>15)return 4;if(fr(0|A,0,52),r=134225919|E(),!A){n=0,t=0;do{0|Y(t)&&(fr(0|t,0,45),o=0|r|E(),i[(A=e+(n<<3)|0)>>2]=-1,i[A+4>>2]=o,n=n+1|0),t=t+1|0}while(122!=(0|t));return 0}n=0,o=0;do{if(0|Y(o)){for(fr(0|o,0,45),t=1,a=-1,f=0|r|E();a&=~(l=0|fr(7,0,3*(15-t|0)|0)),f&=~(0|E()),(0|t)!=(0|A);)t=t+1|0;i[(l=e+(n<<3)|0)>>2]=a,i[l+4>>2]=f,n=n+1|0}o=o+1|0}while(122!=(0|o));return 0}function ie(A,e,r,n){A|=0,n|=0;var t=0,a=0,f=0;t=0|ar(0|(e|=0),0|(r|=0),52),E(),0==(0|e)&0==(0|r)|(0|n)>15|(0|(t&=15))>(0|n)?(a=-1,e=-1,r=0,t=0):(e=0|HA(e,r,t+1|0,n),f=-15728641&(0|E()),r=0|fr(0|n,0,52),a=t,e=(e=0==(0|SA(r|=e,f=0|f|E())))?-1:n,t=f),i[(f=A)>>2]=r,i[f+4>>2]=t,i[A+8>>2]=a,i[A+12>>2]=e}function te(A,e,r,n){r|=0,n|=0;var t=0,a=0;return t=0|ar(0|(A|=0),0|(e|=0),52),E(),i[(a=n+8|0)>>2]=t&=15,0==(0|A)&0==(0|e)|(0|r)>15|(0|t)>(0|r)?(i[(r=n)>>2]=0,i[r+4>>2]=0,i[a>>2]=-1,void(i[n+12>>2]=-1)):(A=0|HA(A,e,t+1|0,r),a=-15728641&(0|E()),t=0|fr(0|r,0,52),t|=A,a=0|a|E(),i[(A=n)>>2]=t,i[A+4>>2]=a,A=n+12|0,0|SA(t,a)?void(i[A>>2]=r):void(i[A>>2]=-1))}function ae(A){var e,r=0,n=0,t=0,a=0,f=0,o=0,l=0,u=0;if(!(0==(0|(r=0|i[(n=A|=0)>>2]))&0==(0|(n=0|i[n+4>>2]))||(t=0|ar(0|r,0|n,52),E(),r=0|Xe(0|(l=0|fr(1,0,3*(15^(t&=15))|0)),0|E(),0|r,0|n),n=0|E(),i[(l=A)>>2]=r,i[l+4>>2]=n,(0|t)<(0|(o=0|i[(l=A+8|0)>>2]))))){for(e=A+12|0,f=t;;){if((0|f)==(0|o)){t=5;break}if(u=(0|f)==(0|i[e>>2]),t=0|ar(0|r,0|n,0|(a=3*(15-f|0)|0)),E(),u&1==(0|(t&=7))&!0){t=7;break}if(!(7==(0|t)&!0)){t=10;break}if(r=0|Xe(0|r,0|n,0|(u=0|fr(1,0,0|a)),0|E()),n=0|E(),i[(u=A)>>2]=r,i[u+4>>2]=n,!((0|f)>(0|o))){t=10;break}f=f+-1|0}if(5==(0|t))return i[(u=A)>>2]=0,i[u+4>>2]=0,i[l>>2]=-1,void(i[e>>2]=-1);if(7==(0|t))return o=0|Xe(0|r,0|n,0|(o=0|fr(1,0,0|a)),0|E()),l=0|E(),i[(u=A)>>2]=o,i[u+4>>2]=l,void(i[e>>2]=f+-1)}}function fe(A){var e;return e=(A=+A)<0?A+6.283185307179586:A,+(A>=6.283185307179586?e+-6.283185307179586:e)}function oe(A,e){return+l(+(+t[(A|=0)>>3]-+t[(e|=0)>>3]))<1.7453292519943298e-11?0|(e=+l(+(+t[A+8>>3]-+t[e+8>>3]))<1.7453292519943298e-11):0|(e=0)}function le(A,e){switch(A=+A,0|(e|=0)){case 1:A=A<0?A+6.283185307179586:A;break;case 2:A=A>0?A+-6.283185307179586:A}return+A}function ue(A,e){var r,n,i,a=0;return a=(i=+b(.5*((n=+t[(e|=0)>>3])-(r=+t[(A|=0)>>3]))))*i+(a=+b(.5*(+t[e+8>>3]-+t[A+8>>3])))*(+s(+n)*+s(+r)*a),2*+v(+ +u(+a),+ +u(+(1-a)))*6371.007180918475}function ce(A,e){return e|=0,(A|=0)>>>0>15?0|(e=4):(t[e>>3]=+t[20656+(A<<3)>>3],0|(e=0))}function se(A,e){e|=0;var r=0;return(A|=0)>>>0>15?0|(e=4):(r=0|rr(0|(r=0|Qe(7,0,A,((0|A)<0)<<31>>31)),0|E(),120,0),A=0|E(),i[e>>2]=2|r,i[e+4>>2]=A,0|(e=0))}function be(A,e,r){r|=0;var n,i,a,f,o=0,l=0,c=0,d=0,w=0,k=0;return w=+b(.5*((k=+t[(e|=0)>>3])-(a=+t[(A|=0)>>3]))),d=+b(.5*((c=+t[e+8>>3])-(i=+t[A+8>>3]))),n=+s(+a),f=+s(+k),d=2*+v(+ +u(+(d=w*w+d*(f*n*d))),+ +u(+(1-d))),k=+b(.5*((w=+t[r>>3])-k)),c=+b(.5*((o=+t[r+8>>3])-c)),l=+s(+w),c=2*+v(+ +u(+(c=k*k+c*(f*l*c))),+ +u(+(1-c))),w=+b(.5*(a-w)),o=+b(.5*(i-o)),o=2*+v(+ +u(+(o=w*w+o*(n*l*o))),+ +u(+(1-o))),4*+B(+ +u(+ +g(.5*(l=.5*(d+c+o)))*+g(.5*(l-d))*+g(.5*(l-c))*+g(.5*(l-o))))}function ge(A,e,r){r|=0;var n,a,f,o=0,l=0;if(f=T,T=T+192|0,a=f,0|(l=0|ee(A|=0,e|=0,n=f+168|0)))return T=f,0|l;if(0|re(A,e,a)&&_(27795,27190,415,27199),(0|(e=0|i[a>>2]))>0){if(o=+be(a+8|0,a+8+((1!=(0|e)&1)<<4)|0,n)+0,1!=(0|e)){A=1;do{o+=+be(a+8+((l=A)<<4)|0,a+8+(((0|(A=A+1|0))%(0|e)|0)<<4)|0,n)}while((0|A)<(0|e))}}else o=0;return t[r>>3]=o,T=f,0}function de(A){A|=0;var e,r,n=0;return(e=0|Ne(1,12))||_(27280,27235,49,27293),0|(n=0|i[(r=A+4|0)>>2])?(i[(n=n+8|0)>>2]=e,i[r>>2]=e,0|e):(0|i[A>>2]&&_(27310,27235,61,27333),i[(n=A)>>2]=e,i[r>>2]=e,0|e)}function we(A,e){var r,n;return A|=0,e|=0,(n=0|Ke(24))||_(27347,27235,78,27361),i[n>>2]=i[e>>2],i[n+4>>2]=i[e+4>>2],i[n+8>>2]=i[e+8>>2],i[n+12>>2]=i[e+12>>2],i[n+16>>2]=0,0|(r=0|i[(e=A+4|0)>>2])?(i[r+16>>2]=n,i[e>>2]=n,0|n):(0|i[A>>2]&&_(27376,27235,82,27361),i[A>>2]=n,i[e>>2]=n,0|n)}function Be(A){var e=0,r=0,n=0,t=0;if(A|=0)for(n=1;;){if(0|(e=0|i[A>>2]))do{if(0|(r=0|i[e>>2]))do{t=r,r=0|i[r+16>>2],je(t)}while(0!=(0|r));t=e,e=0|i[e+8>>2],je(t)}while(0!=(0|e));if(e=A,A=0|i[A+8>>2],n||je(e),!A)break;n=0}}function ve(A){var e,r,n=0,a=0,f=0,o=0,u=0,c=0,s=0,b=0,g=0,d=0,w=0,B=0,v=0,k=0,m=0,D=0,Q=0,h=0,C=0,E=0,y=0,M=0,P=0,I=0,p=0,T=0,F=0,L=0,G=0,z=0;if(0|i[(o=8+(A|=0)|0)>>2])return 1;if(!(f=0|i[A>>2]))return 0;n=f,a=0;do{a=a+1|0,n=0|i[n+8>>2]}while(0!=(0|n));if(a>>>0<2)return 0;(r=0|Ke(a<<2))||_(27396,27235,317,27415),(e=0|Ke(a<<5))||_(27437,27235,321,27415),i[A>>2]=0,i[(M=A+4|0)>>2]=0,i[o>>2]=0,a=0,L=0,y=0,w=0;A:for(;;){if(d=0|i[f>>2]){u=0,c=d;do{if(n=c,+l(+((b=+t[c+8>>3])-(s=+t[(o=(g=0==(0|(c=0|i[c+16>>2])))?d:c)+8>>3])))>3.141592653589793){z=14;break}u+=(s-b)*(+t[n>>3]+ +t[o>>3])}while(!g);if(14==(0|z)){z=0,u=0,n=d;do{u+=(+t[n>>3]+ +t[(T=0==(0|(T=0|i[(F=n+16|0)>>2]))?d:T)>>3])*(((C=+t[T+8>>3])<0?C+6.283185307179586:C)-((E=+t[n+8>>3])<0?E+6.283185307179586:E)),n=0|i[(0==(0|n)?f:F)>>2]}while(0!=(0|n))}u>0?(i[r+(L<<2)>>2]=f,L=L+1|0,o=y,n=w):z=19}else z=19;if(19==(0|z)){z=0;do{if(!a){if(w){o=M,c=w+8|0,n=f,a=A;break}if(0|i[A>>2]){z=27;break A}o=M,c=A,n=f,a=A;break}if(0|i[(n=a+8|0)>>2]){z=21;break A}if(!(a=0|Ne(1,12))){z=23;break A}i[n>>2]=a,o=a+4|0,c=a,n=w}while(0);if(i[c>>2]=f,i[o>>2]=f,c=e+(y<<5)|0,g=0|i[f>>2]){for(t[(d=e+(y<<5)+8|0)>>3]=1.7976931348623157e308,t[(w=e+(y<<5)+24|0)>>3]=1.7976931348623157e308,t[c>>3]=-1.7976931348623157e308,t[(B=e+(y<<5)+16|0)>>3]=-1.7976931348623157e308,Q=1.7976931348623157e308,h=-1.7976931348623157e308,o=0,v=g,b=1.7976931348623157e308,m=1.7976931348623157e308,D=-1.7976931348623157e308,s=-1.7976931348623157e308;u=+t[v>>3],E=+t[v+8>>3],C=+t[((k=0==(0|(v=0|i[v+16>>2])))?g:v)+8>>3],u<b&&(t[d>>3]=u,b=u),E<m&&(t[w>>3]=E,m=E),u>D?t[c>>3]=u:u=D,E>s&&(t[B>>3]=E,s=E),Q=E>0&E<Q?E:Q,h=E<0&E>h?E:h,o|=+l(+(E-C))>3.141592653589793,!k;)D=u;o&&(t[B>>3]=h,t[w>>3]=Q)}else i[c>>2]=0,i[c+4>>2]=0,i[c+8>>2]=0,i[c+12>>2]=0,i[c+16>>2]=0,i[c+20>>2]=0,i[c+24>>2]=0,i[c+28>>2]=0;o=y+1|0}if(f=0|i[(F=f+8|0)>>2],i[F>>2]=0,!f){z=45;break}y=o,w=n}if(21==(0|z))_(27213,27235,35,27247);else if(23==(0|z))_(27267,27235,37,27247);else if(27==(0|z))_(27310,27235,61,27333);else if(45==(0|z)){A:do{if((0|L)>0){for(F=0==(0|o),p=o<<2,T=0==(0|A),I=0,n=0;;){if(P=0|i[r+(I<<2)>>2],F)z=73;else{if(!(y=0|Ke(p))){z=50;break}if(!(M=0|Ke(p))){z=52;break}e:do{if(T)a=0;else{for(o=0,a=0,c=A;0|ke(0|i[c>>2],f=e+(o<<5)|0,0|i[P>>2])?(i[y+(a<<2)>>2]=c,i[M+(a<<2)>>2]=f,k=a+1|0):k=a,c=0|i[c+8>>2];)o=o+1|0,a=k;if((0|k)>0)if(f=0|i[y>>2],1==(0|k))a=f;else for(B=0,v=-1,a=f,w=f;;){for(g=0|i[w>>2],f=0,c=0;d=(0|(o=0|i[i[y+(c<<2)>>2]>>2]))==(0|g)?f:f+(1&(0|ke(o,0|i[M+(c<<2)>>2],0|i[g>>2])))|0,(0|(c=c+1|0))!=(0|k);)f=d;if(a=(o=(0|d)>(0|v))?w:a,(0|(f=B+1|0))==(0|k))break e;B=f,v=o?d:v,w=0|i[y+(f<<2)>>2]}else a=0}}while(0);if(je(y),je(M),a){if(f=0|i[(o=a+4|0)>>2])a=f+8|0;else if(0|i[a>>2]){z=70;break}i[a>>2]=P,i[o>>2]=P}else z=73}if(73==(0|z)){if(z=0,0|(n=0|i[P>>2]))do{M=n,n=0|i[n+16>>2],je(M)}while(0!=(0|n));je(P),n=1}if((0|(I=I+1|0))>=(0|L)){G=n;break A}}50==(0|z)?_(27452,27235,249,27471):52==(0|z)?_(27490,27235,252,27471):70==(0|z)&&_(27310,27235,61,27333)}else G=0}while(0);return je(r),je(e),0|G}return 0}function ke(A,e,r){A|=0;var n=0,a=0,f=0,o=0,l=0,u=0,c=0,s=0;if(!(0|N(e|=0,r|=0)))return 0;if(e=0|K(e),n=+t[r>>3],a=e&(a=+t[r+8>>3])<0?a+6.283185307179586:a,!(A=0|i[A>>2]))return 0;if(e){e=0,c=a,r=A;A:for(;;){for(;o=+t[r>>3],a=+t[r+8>>3],l=+t[(s=0==(0|(s=0|i[(r=r+16|0)>>2]))?A:s)+8>>3],o>(f=+t[s>>3])?(u=o,o=l):(u=f,f=o,o=a,a=l),(n=n==f|n==u?n+2.220446049250313e-16:n)<f|n>u;)if(!(r=0|i[r>>2])){r=22;break A}if(((u=(l=o<0?o+6.283185307179586:o)+(n-f)/(u-f)*((o=a<0?a+6.283185307179586:a)-l))<0?u+6.283185307179586:u)>(c=l==c|o==c?c+-2.220446049250313e-16:c)&&(e^=1),!(r=0|i[r>>2])){r=22;break}}if(22==(0|r))return 0|e}else{e=0,c=a,r=A;A:for(;;){for(;o=+t[r>>3],a=+t[r+8>>3],l=+t[(s=0==(0|(s=0|i[(r=r+16|0)>>2]))?A:s)+8>>3],o>(f=+t[s>>3])?(u=o,o=l):(u=f,f=o,o=a,a=l),(n=n==f|n==u?n+2.220446049250313e-16:n)<f|n>u;)if(!(r=0|i[r>>2])){r=22;break A}if(o+(n-f)/(u-f)*(a-o)>(c=o==c|a==c?c+-2.220446049250313e-16:c)&&(e^=1),!(r=0|i[r>>2])){r=22;break}}if(22==(0|r))return 0|e}return 0}function me(A,e,r,t,a){r|=0,t|=0,a|=0;var f,o,l,u,c,s=0,b=0,g=0,d=0,w=0,B=0,v=0,k=0,m=0;if(u=T,T=T+32|0,c=u+16|0,l=u,s=0|ar(0|(A|=0),0|(e|=0),52),E(),s&=15,v=0|ar(0|r,0|t,52),E(),(0|s)!=(15&v|0))return T=u,12;if(w=0|ar(0|A,0|e,45),E(),w&=127,B=0|ar(0|r,0|t,45),E(),w>>>0>121|(B&=127)>>>0>121)return T=u,5;if(v=(0|w)!=(0|B)){if(7==(0|(g=0|Z(w,B))))return T=u,1;7==(0|(d=0|Z(B,w)))?_(27514,27538,161,27548):(k=g,b=d)}else k=0,b=0;f=0|Y(w),o=0|Y(B),i[c>>2]=0,i[c+4>>2]=0,i[c+8>>2]=0,i[c+12>>2]=0;do{if(k){if(g=(0|(B=0|i[4272+(28*w|0)+(k<<2)>>2]))>0,o)if(g){w=0,d=r,g=t;do{d=0|jA(d,g),g=0|E(),1==(0|(b=0|mA(b)))&&(b=0|mA(1)),w=w+1|0}while((0|w)!=(0|B));B=b,w=d,d=g}else B=b,w=r,d=t;else if(g){w=0,d=r,g=t;do{d=0|NA(d,g),g=0|E(),b=0|mA(b),w=w+1|0}while((0|w)!=(0|B));B=b,w=d,d=g}else B=b,w=r,d=t;if($A(w,d,c),v||_(27563,27538,191,27548),(g=0!=(0|f))&(b=0!=(0|o))&&_(27590,27538,192,27548),g){if(7==(0|(b=0|ZA(A,e)))){s=5;break}if(0|n[22e3+(7*b|0)+k>>0]){s=1;break}w=d=0|i[21168+(28*b|0)+(k<<2)>>2]}else if(b){if(7==(0|(b=0|ZA(w,d)))){s=5;break}if(0|n[22e3+(7*b|0)+B>>0]){s=1;break}w=0,d=0|i[21168+(28*B|0)+(b<<2)>>2]}else w=0,d=0;if((w|d|0)<0)s=5;else{if((0|d)>0){g=c+4|0,b=0;do{vA(g),b=b+1|0}while((0|b)!=(0|d))}if(i[l>>2]=0,i[l+4>>2]=0,i[l+8>>2]=0,wA(l,k),0|s)for(;0|XA(s)?gA(l):dA(l),(0|s)>1;)s=s+-1|0;if((0|w)>0){s=0;do{vA(l),s=s+1|0}while((0|s)!=(0|w))}aA(m=c+4|0,l,m),iA(m),m=51}}else if($A(r,t,c),0!=(0|f)&0!=(0|o))if((0|B)!=(0|w)&&_(27621,27538,261,27548),7==(0|(b=0|ZA(A,e)))|7==(0|(s=0|ZA(r,t))))s=5;else if(0|n[22e3+(7*b|0)+s>>0])s=1;else if((0|(b=0|i[21168+(28*b|0)+(s<<2)>>2]))>0){g=c+4|0,s=0;do{vA(g),s=s+1|0}while((0|s)!=(0|b));m=51}else m=51;else m=51}while(0);return 51==(0|m)&&(i[a>>2]=i[(s=c+4|0)>>2],i[a+4>>2]=i[s+4>>2],i[a+8>>2]=i[s+8>>2],s=0),T=u,0|s}function De(A,e,r,n){r|=0,n|=0;var t,a,f,o=0,l=0,u=0,c=0,s=0,b=0,g=0,d=0,w=0,B=0,v=0,k=0,m=0;if(f=T,T=T+48|0,t=f+36|0,u=f+24|0,c=f+12|0,s=f,o=0|ar(0|(A|=0),0|(e|=0),52),E(),o&=15,g=0|ar(0|A,0|e,45),E(),(g&=127)>>>0>121)return T=f,5;if(a=0|Y(g),fr(0|o,0,52),B=134225919|E(),i[(l=n)>>2]=-1,i[l+4>>2]=B,!o)return 7==(0|(o=0|lA(r)))?(T=f,0|(B=1)):127==(0|(o=0|W(g,o)))?(T=f,0|(B=1)):(d=0|fr(0|o,0,45),w=0|E(),w|=-1040385&i[(g=n)+4>>2],i[(B=n)>>2]=i[g>>2]|d,i[B+4>>2]=w,T=f,0|(B=0));for(i[t>>2]=i[r>>2],i[t+4>>2]=i[r+4>>2],i[t+8>>2]=i[r+8>>2],r=o;;){if(l=r,r=r+-1|0,i[u>>2]=i[t>>2],i[u+4>>2]=i[t+4>>2],i[u+8>>2]=i[t+8>>2],0|XA(l)){if(0|(o=0|uA(t))){r=13;break}i[c>>2]=i[t>>2],i[c+4>>2]=i[t+4>>2],i[c+8>>2]=i[t+8>>2],gA(c)}else{if(0|(o=0|cA(t))){r=13;break}i[c>>2]=i[t>>2],i[c+4>>2]=i[t+4>>2],i[c+8>>2]=i[t+8>>2],dA(c)}if(fA(u,c,s),iA(s),k=0|i[(o=n)>>2],o=0|i[o+4>>2],v=0|fr(7,0,0|(m=3*(15-l|0)|0)),o&=~(0|E()),m=0|fr(0|lA(s),0,0|m),o=0|E()|o,i[(B=n)>>2]=m|k&~v,i[B+4>>2]=o,(0|l)<=1){r=14;break}}A:do{if(13!=(0|r)&&14==(0|r))if((0|i[t>>2])<=1&&(0|i[t+4>>2])<=1&&(0|i[t+8>>2])<=1){s=127==(0|(o=0|W(g,r=0|lA(t))))?0:0|Y(o);e:do{if(r){if(a){if(7==(0|(o=0|ZA(A,e)))){o=5;break A}if((0|(l=0|i[21376+(28*o|0)+(r<<2)>>2]))>0){o=r,r=0;do{o=0|kA(o),r=r+1|0}while((0|r)!=(0|l))}else o=r;if(1==(0|o)){o=9;break A}127==(0|(r=0|W(g,o)))&&_(27648,27538,411,27678),0|Y(r)?_(27693,27538,412,27678):(w=r,d=l,b=o)}else w=o,d=0,b=r;if((0|(c=0|i[4272+(28*g|0)+(b<<2)>>2]))<=-1&&_(27724,27538,419,27678),!s){if((0|d)<0){o=5;break A}if(0|d){o=0,r=0|i[(l=n)>>2],l=0|i[l+4>>2];do{r=0|KA(r,l),l=0|E(),i[(m=n)>>2]=r,i[m+4>>2]=l,o=o+1|0}while((0|o)<(0|d))}if((0|c)<=0){o=w,r=58;break}for(o=0,r=0|i[(l=n)>>2],l=0|i[l+4>>2];;)if(r=0|KA(r,l),l=0|E(),i[(m=n)>>2]=r,i[m+4>>2]=l,(0|(o=o+1|0))==(0|c)){o=w,r=58;break e}}if(7==(0|(u=0|Z(w,g)))&&_(27514,27538,428,27678),r=0|i[(o=n)>>2],o=0|i[o+4>>2],(0|c)>0){l=0;do{r=0|KA(r,o),o=0|E(),i[(m=n)>>2]=r,i[m+4>>2]=o,l=l+1|0}while((0|l)!=(0|c))}if(7==(0|(o=0|ZA(r,o)))&&_(27795,27538,440,27678),r=0|V(w),(0|(r=0|i[(r?21792:21584)+(28*u|0)+(o<<2)>>2]))<0&&_(27795,27538,454,27678),r){o=0,l=0|i[(u=n)>>2],u=0|i[u+4>>2];do{l=0|JA(l,u),u=0|E(),i[(m=n)>>2]=l,i[m+4>>2]=u,o=o+1|0}while((0|o)<(0|r));o=w,r=58}else o=w,r=58}else if(0!=(0|a)&0!=(0|s)){if(7==(0|(r=0|ZA(A,e)))|7==(0|(l=0|ZA(0|i[(l=n)>>2],0|i[l+4>>2])))){o=5;break A}if((0|(l=0|i[21376+(28*r|0)+(l<<2)>>2]))<0){o=5;break A}if(l){r=0,u=0|i[(c=n)>>2],c=0|i[c+4>>2];do{u=0|KA(u,c),c=0|E(),i[(m=n)>>2]=u,i[m+4>>2]=c,r=r+1|0}while((0|r)<(0|l));r=58}else r=59}else r=58}while(0);if(58==(0|r)&&s&&(r=59),59==(0|r)&&1==(0|ZA(0|i[(m=n)>>2],0|i[m+4>>2]))){o=9;break}v=0|i[(m=n)>>2],m=-1040385&i[m+4>>2],k=0|fr(0|o,0,45),m=0|m|E(),i[(o=n)>>2]=v|k,i[o+4>>2]=m,o=0}else o=1}while(0);return T=f,0|o}function Qe(A,e,r,n){A|=0,e|=0;var i=0,t=0,a=0;if(0==(0|(r|=0))&0==(0|(n|=0)))return t=1,C(0|(i=0)),0|t;t=A,i=e,A=1,e=0;do{A=0|rr(0|((a=0==(1&r|0)&!0)?1:t),0|(a?0:i),0|A,0|e),e=0|E(),r=0|tr(0|r,0|n,1),n=0|E(),t=0|rr(0|t,0|i,0|t,0|i),i=0|E()}while(!(0==(0|r)&0==(0|n)));return C(0|e),0|A}function he(A,e,r,n){r|=0,n|=0;var a,f=0,o=0,l=0,u=0,c=0,b=0;a=T,T=T+16|0,o=a,l=0|ar(0|(A|=0),0|(e|=0),52),E(),l&=15;do{if(l){if(!(f=0|ee(A,e,o))){u=1/+s(+(c=+t[o>>3])),t[r>>3]=c+(b=+t[25968+(l<<3)>>3]),t[r+8>>3]=c-b,t[r+16>>3]=(u*=b)+(c=+t[o+8>>3]),t[r+24>>3]=c-u;break}return T=a,0|f}if(f=0|ar(0|A,0|e,45),E(),(f&=127)>>>0>121)return T=a,5;i[r>>2]=i[(o=22064+(f<<5)|0)>>2],i[r+4>>2]=i[o+4>>2],i[r+8>>2]=i[o+8>>2],i[r+12>>2]=i[o+12>>2],i[r+16>>2]=i[o+16>>2],i[r+20>>2]=i[o+20>>2],i[r+24>>2]=i[o+24>>2],i[r+28>>2]=i[o+28>>2];break}while(0);return function(A,e){var r,n,i,a,f,o,l,u=0,c=0;u=(a=+t[(i=16+(A|=0)|0)>>3])-(n=+t[(r=A+24|0)>>3]),c=+t[A>>3],u=.5*((u=a<n?u+6.283185307179586:u)*(e=+e)-u),t[A>>3]=(c+=e=.5*((l=c-(o=+t[(f=A+8|0)>>3]))*e-l))>1.5707963267948966?1.5707963267948966:c,t[f>>3]=(e=o-e)<-1.5707963267948966?-1.5707963267948966:e,t[i>>3]=(e=(e=a+u)>3.141592653589793?e+-6.283185307179586:e)<-3.141592653589793?e+6.283185307179586:e,t[r>>3]=(e=(e=n-u)>3.141592653589793?e+-6.283185307179586:e)<-3.141592653589793?e+6.283185307179586:e}(r,n?1.4:1.1),(0|i[(n=26096+(l<<3)|0)>>2])==(0|A)&&(0|i[n+4>>2])==(0|e)&&(t[r>>3]=1.5707963267948966),(0|i[(l=26224+(l<<3)|0)>>2])==(0|A)&&(0|i[l+4>>2])==(0|e)&&(t[r+8>>3]=-1.5707963267948966),1.5707963267948966!=+t[r>>3]&&-1.5707963267948966!=+t[r+8>>3]?(T=a,0|(l=0)):(t[r+16>>3]=3.141592653589793,t[r+24>>3]=-3.141592653589793,T=a,0|(l=0))}function Ce(A,e,r,t){A|=0,e|=0,r|=0,t|=0;var a,f,o,l=0,u=0,c=0,s=0;o=T,T=T+48|0,a=o+40|0,f=o,YA(u=o+32|0,0,0,0),c=0|i[u>>2],u=0|i[u+4>>2];do{if(r>>>0<=15){if(0|(l=0|Pe(t))){i[(t=f)>>2]=0,i[t+4>>2]=0,i[f+8>>2]=l,i[f+12>>2]=-1,c=f+29|0,i[(t=f+16|0)>>2]=0,i[t+4>>2]=0,i[t+8>>2]=0,n[t+12>>0]=0,n[c>>0]=0|n[a>>0],n[c+1>>0]=0|n[a+1>>0],n[c+2>>0]=0|n[a+2>>0];break}if(l=0|Ne(1+(0|i[e+8>>2])|0,32)){Ie(e,l),i[(s=f)>>2]=c,i[s+4>>2]=u,i[f+8>>2]=0,i[f+12>>2]=r,i[f+16>>2]=t,i[f+20>>2]=e,i[f+24>>2]=l,n[f+28>>0]=0,n[(c=f+29|0)>>0]=0|n[a>>0],n[c+1>>0]=0|n[a+1>>0],n[c+2>>0]=0|n[a+2>>0];break}i[(t=f)>>2]=0,i[t+4>>2]=0,i[f+8>>2]=13,i[f+12>>2]=-1,c=f+29|0,i[(t=f+16|0)>>2]=0,i[t+4>>2]=0,i[t+8>>2]=0,n[t+12>>0]=0,n[c>>0]=0|n[a>>0],n[c+1>>0]=0|n[a+1>>0],n[c+2>>0]=0|n[a+2>>0];break}i[(c=f)>>2]=0,i[c+4>>2]=0,i[f+8>>2]=4,i[f+12>>2]=-1,s=f+29|0,i[(c=f+16|0)>>2]=0,i[c+4>>2]=0,i[c+8>>2]=0,n[c+12>>0]=0,n[s>>0]=0|n[a>>0],n[s+1>>0]=0|n[a+1>>0],n[s+2>>0]=0|n[a+2>>0]}while(0);Ee(f),i[A>>2]=i[f>>2],i[A+4>>2]=i[f+4>>2],i[A+8>>2]=i[f+8>>2],i[A+12>>2]=i[f+12>>2],i[A+16>>2]=i[f+16>>2],i[A+20>>2]=i[f+20>>2],i[A+24>>2]=i[f+24>>2],i[A+28>>2]=i[f+28>>2],T=o}function Ee(A){var e,r,t,a,f=0,o=0,l=0,u=0,c=0,s=0,b=0,g=0,d=0,w=0,B=0,v=0,k=0,m=0,D=0,Q=0;if(a=T,T=T+336|0,e=a+168|0,r=a,0==(0|(o=0|i[(l=A|=0)>>2]))&0==(0|(l=0|i[l+4>>2])))T=a;else{if(0|n[(f=A+28|0)>>0]?(o=0|_e(o,l),l=0|E()):n[f>>0]=1,!(0|i[i[(t=A+20|0)>>2]>>2]))return 0|(o=0|i[(f=A+24|0)>>2])&&je(o),i[(Q=A)>>2]=0,i[Q+4>>2]=0,i[A+8>>2]=0,i[t>>2]=0,i[A+12>>2]=-1,i[A+16>>2]=0,i[f>>2]=0,void(T=a);u=15&(f=0|i[(Q=A+16|0)>>2]);A:do{if(0==(0|o)&0==(0|l))D=A+24|0;else{k=A+12|0,B=3==(0|u),w=255&f,g=3==(1|u),v=A+24|0,d=(u+-1|0)>>>0<3,s=3==(2|u),b=r+8|0;e:for(;;){if(c=0|ar(0|o,0|l,52),E(),(0|(c&=15))==(0|i[k>>2])){switch(15&w){case 0:case 2:case 3:if(0|(u=0|ee(o,l,e))){m=15;break e}if(0|pe(0|i[t>>2],0|i[v>>2],e)){m=19;break e}}if(g&&(i[e>>2]=i[(u=0|i[4+(0|i[t>>2])>>2])>>2],i[e+4>>2]=i[u+4>>2],i[e+8>>2]=i[u+8>>2],i[e+12>>2]=i[u+12>>2],0|N(26832,e))){if(0|qA(0|i[4+(0|i[t>>2])>>2],c,r)){m=25;break}if((0|i[(u=r)>>2])==(0|o)&&(0|i[u+4>>2])==(0|l)){m=29;break}}if(d){if(0|(u=0|re(o,l,e))){m=32;break}if(0|he(o,l,r,0)){m=36;break}if(s&&0|Te(0|i[t>>2],0|i[v>>2],e,r)){m=42;break}if(g&&0|Le(0|i[t>>2],0|i[v>>2],e,r)){m=42;break}}if(B){if(f=0|he(o,l,e,1),u=0|i[v>>2],0|f){m=45;break}if(0|X(u,e)){if($(r,e),0|q(e,0|i[v>>2])){m=53;break}if(0|pe(0|i[t>>2],0|i[v>>2],b)){m=53;break}if(0|Le(0|i[t>>2],0|i[v>>2],r,e)){m=53;break}}}}do{if((0|c)<(0|i[k>>2])){if(f=0|he(o,l,e,1),u=0|i[v>>2],0|f){m=58;break e}if(!(0|X(u,e))){m=73;break}if(0|q(0|i[v>>2],e)&&($(r,e),0|Te(0|i[t>>2],0|i[v>>2],r,e))){m=65;break e}if(0|(o=0|WA(o,l,c+1|0,r))){m=67;break e}o=0|i[(l=r)>>2],l=0|i[l+4>>2]}else m=73}while(0);if(73==(0|m)&&(m=0,o=0|_e(o,l),l=0|E()),0==(0|o)&0==(0|l)){D=v;break A}}switch(0|m){case 15:0|(f=0|i[v>>2])&&je(f),i[(m=A)>>2]=0,i[m+4>>2]=0,i[t>>2]=0,i[k>>2]=-1,i[Q>>2]=0,i[v>>2]=0,i[A+8>>2]=u,m=20;break;case 19:i[A>>2]=o,i[A+4>>2]=l,m=20;break;case 25:_(27795,27761,470,27772);break;case 29:return i[A>>2]=o,i[A+4>>2]=l,void(T=a);case 32:return 0|(f=0|i[v>>2])&&je(f),i[(D=A)>>2]=0,i[D+4>>2]=0,i[t>>2]=0,i[k>>2]=-1,i[Q>>2]=0,i[v>>2]=0,i[A+8>>2]=u,void(T=a);case 36:_(27795,27761,493,27772);break;case 42:return i[A>>2]=o,i[A+4>>2]=l,void(T=a);case 45:0|u&&je(u),i[(m=A)>>2]=0,i[m+4>>2]=0,i[t>>2]=0,i[k>>2]=-1,i[Q>>2]=0,i[v>>2]=0,i[A+8>>2]=f,m=55;break;case 53:i[A>>2]=o,i[A+4>>2]=l,m=55;break;case 58:0|u&&je(u),i[(m=A)>>2]=0,i[m+4>>2]=0,i[t>>2]=0,i[k>>2]=-1,i[Q>>2]=0,i[v>>2]=0,i[A+8>>2]=f,m=71;break;case 65:i[A>>2]=o,i[A+4>>2]=l,m=71;break;case 67:return 0|(f=0|i[v>>2])&&je(f),i[(D=A)>>2]=0,i[D+4>>2]=0,i[t>>2]=0,i[k>>2]=-1,i[Q>>2]=0,i[v>>2]=0,i[A+8>>2]=o,void(T=a)}if(20==(0|m))return void(T=a);if(55==(0|m))return void(T=a);if(71==(0|m))return void(T=a)}}while(0);0|(f=0|i[D>>2])&&je(f),i[(m=A)>>2]=0,i[m+4>>2]=0,i[A+8>>2]=0,i[t>>2]=0,i[A+12>>2]=-1,i[Q>>2]=0,i[D>>2]=0,T=a}}function _e(A,e){var r,n=0,t=0,a=0,f=0,o=0,l=0,u=0,c=0,s=0;r=T,T=T+16|0,s=r,t=0|ar(0|(A|=0),0|(e|=0),52),E(),t&=15,n=0|ar(0|A,0|e,45),E();do{if(t){for(;n=0|fr(t+4095|0,0,52),a=0|E()|-15728641&e,n=n|A|(o=0|fr(7,0,0|(f=3*(15-t|0)|0))),a|=l=0|E(),u=0|ar(0|A,0|e,0|f),E(),t=t+-1|0,!((u&=7)>>>0<6);){if(!t){c=4;break}e=a,A=n}if(4==(0|c)){n=0|ar(0|n,0|a,45),E();break}return s=0|fr(((s=0==(0|u)&0!=(0|SA(n,a)))?2:1)+u|0,0,0|f),c=0|E()|e&~l,s|=A&~o,C(0|c),T=r,0|s}}while(0);return(n&=127)>>>0>120?(s=0,C(0|(c=0)),T=r,0|s):(YA(s,0,n+1|0,0),c=0|i[s+4>>2],s=0|i[s>>2],C(0|c),T=r,0|s)}function ye(A,e,r){A|=0;var n,a,f=0,o=0,l=0,u=0,c=0,s=0,b=0,g=0;if(!(0|N(e|=0,r|=0)))return 0;if(e=0|K(e),f=+t[r>>3],o=e&(o=+t[r+8>>3])<0?o+6.283185307179586:o,(0|(a=0|i[A>>2]))<=0)return 0;if(n=0|i[A+4>>2],e){e=0,g=o,r=-1,A=0;A:for(;;){for(b=A;o=+t[n+(b<<4)+8>>3],c=+t[n+((A=(r+2|0)%(0|a)|0)<<4)+8>>3],(u=+t[n+(b<<4)>>3])>(l=+t[n+(A<<4)>>3])?(s=u,u=c):(s=l,l=u,u=o,o=c),(f=f==l|f==s?f+2.220446049250313e-16:f)<l|f>s;){if((0|(r=b+1|0))>=(0|a)){r=22;break A}A=b,b=r,r=A}if(((s=(c=u<0?u+6.283185307179586:u)+(f-l)/(s-l)*((u=o<0?o+6.283185307179586:o)-c))<0?s+6.283185307179586:s)>(g=c==g|u==g?g+-2.220446049250313e-16:g)&&(e^=1),(0|(A=b+1|0))>=(0|a)){r=22;break}r=b}if(22==(0|r))return 0|e}else{e=0,g=o,r=-1,A=0;A:for(;;){for(b=A;o=+t[n+(b<<4)+8>>3],c=+t[n+((A=(r+2|0)%(0|a)|0)<<4)+8>>3],(u=+t[n+(b<<4)>>3])>(l=+t[n+(A<<4)>>3])?(s=u,u=c):(s=l,l=u,u=o,o=c),(f=f==l|f==s?f+2.220446049250313e-16:f)<l|f>s;){if((0|(r=b+1|0))>=(0|a)){r=22;break A}A=b,b=r,r=A}if(u+(f-l)/(s-l)*(o-u)>(g=u==g|o==g?g+-2.220446049250313e-16:g)&&(e^=1),(0|(A=b+1|0))>=(0|a)){r=22;break}r=b}if(22==(0|r))return 0|e}return 0}function Me(A,e){e|=0;var r,n,a,f,o,u=0,c=0,s=0,b=0,g=0,d=0,w=0,B=0,v=0,k=0,m=0,D=0;if(!(n=0|i[(A|=0)>>2]))return i[e>>2]=0,i[e+4>>2]=0,i[e+8>>2]=0,i[e+12>>2]=0,i[e+16>>2]=0,i[e+20>>2]=0,i[e+24>>2]=0,void(i[e+28>>2]=0);if(t[(a=e+8|0)>>3]=1.7976931348623157e308,t[(f=e+24|0)>>3]=1.7976931348623157e308,t[e>>3]=-1.7976931348623157e308,t[(o=e+16|0)>>3]=-1.7976931348623157e308,!((0|n)<=0)){for(r=0|i[A+4>>2],B=1.7976931348623157e308,v=-1.7976931348623157e308,k=0,A=-1,b=1.7976931348623157e308,g=1.7976931348623157e308,w=-1.7976931348623157e308,c=-1.7976931348623157e308,m=0;d=+t[r+(m<<4)+8>>3],s=+t[r+(((0|(A=A+2|0))==(0|n)?0:A)<<4)+8>>3],(u=+t[r+(m<<4)>>3])<b&&(t[a>>3]=u,b=u),d<g&&(t[f>>3]=d,g=d),u>w?t[e>>3]=u:u=w,d>c&&(t[o>>3]=d,c=d),B=d>0&d<B?d:B,v=d<0&d>v?d:v,k|=+l(+(d-s))>3.141592653589793,(0|(A=m+1|0))!=(0|n);)D=m,w=u,m=A,A=D;k&&(t[o>>3]=v,t[f>>3]=B)}}function Pe(A){return 0|((A|=0)>>>0<4?0:15)}function Ie(A,e){e|=0;var r,n=0,a=0,f=0,o=0,u=0,c=0,s=0,b=0,g=0,d=0,w=0,B=0,v=0,k=0,m=0,D=0,Q=0,h=0,C=0,E=0,_=0;if(v=0|i[(A|=0)>>2]){if(t[(k=e+8|0)>>3]=1.7976931348623157e308,t[(m=e+24|0)>>3]=1.7976931348623157e308,t[e>>3]=-1.7976931348623157e308,t[(D=e+16|0)>>3]=-1.7976931348623157e308,(0|v)>0){for(f=0|i[A+4>>2],w=1.7976931348623157e308,B=-1.7976931348623157e308,a=0,n=-1,s=1.7976931348623157e308,b=1.7976931348623157e308,d=-1.7976931348623157e308,u=-1.7976931348623157e308,Q=0;g=+t[f+(Q<<4)+8>>3],c=+t[f+(((0|(E=n+2|0))==(0|v)?0:E)<<4)+8>>3],(o=+t[f+(Q<<4)>>3])<s&&(t[k>>3]=o,s=o),g<b&&(t[m>>3]=g,b=g),o>d?t[e>>3]=o:o=d,g>u&&(t[D>>3]=g,u=g),w=g>0&g<w?g:w,B=g<0&g>B?g:B,a|=+l(+(g-c))>3.141592653589793,(0|(n=Q+1|0))!=(0|v);)E=Q,d=o,Q=n,n=E;a&&(t[D>>3]=B,t[m>>3]=w)}}else i[e>>2]=0,i[e+4>>2]=0,i[e+8>>2]=0,i[e+12>>2]=0,i[e+16>>2]=0,i[e+20>>2]=0,i[e+24>>2]=0,i[e+28>>2]=0;if(!((0|(n=0|i[(E=A+8|0)>>2]))<=0)){r=A+12|0,C=0;do{if(a=C,m=e+((C=C+1|0)<<5)|0,D=0|i[(f=0|i[r>>2])+(a<<3)>>2]){if(t[(Q=e+(C<<5)+8|0)>>3]=1.7976931348623157e308,t[(A=e+(C<<5)+24|0)>>3]=1.7976931348623157e308,t[m>>3]=-1.7976931348623157e308,t[(h=e+(C<<5)+16|0)>>3]=-1.7976931348623157e308,(0|D)>0){for(v=0|i[f+(a<<3)+4>>2],w=1.7976931348623157e308,B=-1.7976931348623157e308,f=0,a=-1,k=0,s=1.7976931348623157e308,b=1.7976931348623157e308,g=-1.7976931348623157e308,u=-1.7976931348623157e308;d=+t[v+(k<<4)+8>>3],c=+t[v+(((0|(a=a+2|0))==(0|D)?0:a)<<4)+8>>3],(o=+t[v+(k<<4)>>3])<s&&(t[Q>>3]=o,s=o),d<b&&(t[A>>3]=d,b=d),o>g?t[m>>3]=o:o=g,d>u&&(t[h>>3]=d,u=d),w=d>0&d<w?d:w,B=d<0&d>B?d:B,f|=+l(+(d-c))>3.141592653589793,(0|(a=k+1|0))!=(0|D);)_=k,k=a,g=o,a=_;f&&(t[h>>3]=B,t[A>>3]=w)}}else i[m>>2]=0,i[m+4>>2]=0,i[m+8>>2]=0,i[m+12>>2]=0,i[m+16>>2]=0,i[m+20>>2]=0,i[m+24>>2]=0,i[m+28>>2]=0,n=0|i[E>>2]}while((0|C)<(0|n))}}function pe(A,e,r){var n,t=0;if(!(0|ye(A|=0,e|=0,r|=0)))return 0;if((0|i[(n=A+8|0)>>2])<=0)return 1;for(t=A+12|0,A=0;;){if(0|ye((0|i[t>>2])+(A<<3)|0,e+((A=A+1|0)<<5)|0,r)){A=0,t=6;break}if((0|A)>=(0|i[n>>2])){A=1,t=6;break}}return 6==(0|t)?0|A:0}function Te(A,e,r,n){n|=0;var t,a,f,o=0,l=0,u=0;if(a=T,T=T+16|0,t=a,!(0|ye(A|=0,e|=0,u=8+(r|=0)|0)))return T=a,0;f=A+8|0;A:do{if((0|i[f>>2])>0){for(l=A+12|0,o=0;;){if(0|ye((0|i[l>>2])+(o<<3)|0,e+((o=o+1|0)<<5)|0,u)){o=0;break}if((0|o)>=(0|i[f>>2]))break A}return T=a,0|o}}while(0);if(0|Fe(A,e,r,n))return T=a,0;i[t>>2]=i[r>>2],i[t+4>>2]=u,o=0|i[f>>2];A:do{if((0|o)>0)for(A=A+12|0,u=0,l=o;;){if((0|i[(o=0|i[A>>2])+(u<<3)>>2])>0){if(0|ye(t,n,0|i[o+(u<<3)+4>>2])){o=0;break A}if(0|Fe((0|i[A>>2])+(u<<3)|0,e+((o=u+1|0)<<5)|0,r,n)){o=0;break A}l=0|i[f>>2]}else o=u+1|0;if(!((0|o)<(0|l))){o=1;break}u=o}else o=1}while(0);return T=a,0|o}function Fe(A,e,r,n){A|=0,r|=0;var a,f,o,l,u,c,s=0,b=0,g=0,d=0,w=0,B=0,v=0,k=0,m=0,D=0,Q=0,h=0,C=0,E=0,_=0;if(c=T,T=T+176|0,l=c+172|0,s=c+168|0,u=c,!(0|X(e|=0,n|=0)))return T=c,0;if(function(A,e,r,n){var a,f,o,l,u;n|=0,a=(l=+t[16+(e|=0)>>3])<(o=+t[e+24>>3]),e=(u=+t[24+(A|=0)>>3])-l<o-(f=+t[A+16>>3]),i[(r|=0)>>2]=(A=f<u)?a|e?1:2:0,i[n>>2]=a?A?1:e?2:1:0}(e,n,l,s),sr(0|u,0|r,168),(0|i[r>>2])>0){e=0;do{Q=+le(+t[(E=u+8+(e<<4)+8|0)>>3],0|i[s>>2]),t[E>>3]=Q,e=e+1|0}while((0|e)<(0|i[r>>2]))}f=+t[n>>3],o=+t[n+8>>3],Q=+le(+t[n+16>>3],0|i[s>>2]),a=+le(+t[n+24>>3],0|i[s>>2]);A:do{if((0|i[A>>2])>0){if(n=A+4|0,(0|(s=0|i[u>>2]))<=0)for(e=0;;)if((0|(e=e+1|0))>=(0|i[A>>2])){e=0;break A}for(r=0;;){if(m=+t[(e=0|i[n>>2])+(r<<4)>>3],D=+le(+t[e+(r<<4)+8>>3],0|i[l>>2]),b=+t[(e=0|i[n>>2])+((E=(0|(r=r+1|0))%(0|i[A>>2])|0)<<4)>>3],g=+le(+t[e+(E<<4)+8>>3],0|i[l>>2]),!(m>=f)|!(b>=f)&&!(m<=o)|!(b<=o)&&!(D<=a)|!(g<=a)&&!(D>=Q)|!(g>=Q)){k=b-m,B=g-D,e=0;do{if(_=e,!(0==(v=k*(g=+t[u+8+((E=(0|(e=e+1|0))==(0|s)?0:e)<<4)+8>>3]-(b=+t[u+8+(_<<4)+8>>3]))-B*(w=+t[u+8+(E<<4)>>3]-(d=+t[u+8+(_<<4)>>3])))||(h=D-b,C=m-d,w=(h*w-g*C)/v,w<0|w>1))&&(v=(k*h-B*C)/v)>=0&v<=1){e=1;break A}}while((0|e)<(0|s))}if((0|r)>=(0|i[A>>2])){e=0;break}}}else e=0}while(0);return T=c,0|e}function Le(A,e,r,n){var t,a=0;if(0|Fe(A|=0,e|=0,r|=0,n|=0))return 1;if((0|i[(t=A+8|0)>>2])<=0)return 0;for(a=A+12|0,A=0;;){if(0|Fe((0|i[a>>2])+(A<<3)|0,e+((A=A+1|0)<<5)|0,r,n)){A=1,a=6;break}if((0|A)>=(0|i[t>>2])){A=0,a=6;break}}return 6==(0|a)?0|A:0}function Ge(A,e,r,n,i){var a,f,o,l,u,c,s,b=0;t[(i|=0)>>3]=(l=+t[(A|=0)>>3])+(o=+t[(e|=0)>>3]-l)*(b=((b=+t[(n|=0)>>3]-(c=+t[(r|=0)>>3]))*((f=+t[A+8>>3])-(s=+t[r+8>>3]))-(l-c)*(u=+t[n+8>>3]-s))/(o*u-(a=+t[e+8>>3]-f)*b)),t[i+8>>3]=f+a*b}function ze(A,e){return+l(+(+t[(A|=0)>>3]-+t[(e|=0)>>3]))<1.1920928955078125e-7?0|(e=+l(+(+t[A+8>>3]-+t[e+8>>3]))<1.1920928955078125e-7):0|(e=0)}function Ue(A,e){var r,n,i;return+((i=+t[(A|=0)>>3]-+t[(e|=0)>>3])*i+(n=+t[A+8>>3]-+t[e+8>>3])*n+(r=+t[A+16>>3]-+t[e+16>>3])*r)}function xe(A,e,r){r|=0;var n,t,a,f,o=0,l=0,u=0;if(f=T,T=T+32|0,u=f,0|(o=0|Ae(A|=0,e|=0,t=f+16|0)))return T=f,0|o;n=0|xA(A,e),a=0|ZA(A,e),function(A,e){i[(e|=0)>>2]=i[(A=7696+(28*(A|=0)|0)|0)>>2],i[e+4>>2]=i[A+4>>2],i[e+8>>2]=i[A+8>>2],i[e+12>>2]=i[A+12>>2]}(n,u),o=0|function(A,e){A|=0;var r=0,n=0;if((e|=0)>>>0>20)return-1;do{if((0|i[11120+(216*e|0)>>2])!=(0|A))if((0|i[11120+(216*e|0)+8>>2])!=(0|A))if((0|i[11120+(216*e|0)+16>>2])!=(0|A))if((0|i[11120+(216*e|0)+24>>2])!=(0|A))if((0|i[11120+(216*e|0)+32>>2])!=(0|A))if((0|i[11120+(216*e|0)+40>>2])!=(0|A))if((0|i[11120+(216*e|0)+48>>2])!=(0|A))if((0|i[11120+(216*e|0)+56>>2])!=(0|A))if((0|i[11120+(216*e|0)+64>>2])!=(0|A))if((0|i[11120+(216*e|0)+72>>2])!=(0|A))if((0|i[11120+(216*e|0)+80>>2])!=(0|A))if((0|i[11120+(216*e|0)+88>>2])!=(0|A))if((0|i[11120+(216*e|0)+96>>2])!=(0|A))if((0|i[11120+(216*e|0)+104>>2])!=(0|A))if((0|i[11120+(216*e|0)+112>>2])!=(0|A))if((0|i[11120+(216*e|0)+120>>2])!=(0|A))if((0|i[11120+(216*e|0)+128>>2])!=(0|A)){if((0|i[11120+(216*e|0)+136>>2])!=(0|A)){if((0|i[11120+(216*e|0)+144>>2])==(0|A)){A=0,r=2,n=0;break}if((0|i[11120+(216*e|0)+152>>2])==(0|A)){A=0,r=2,n=1;break}if((0|i[11120+(216*e|0)+160>>2])==(0|A)){A=0,r=2,n=2;break}if((0|i[11120+(216*e|0)+168>>2])==(0|A)){A=1,r=2,n=0;break}if((0|i[11120+(216*e|0)+176>>2])==(0|A)){A=1,r=2,n=1;break}if((0|i[11120+(216*e|0)+184>>2])==(0|A)){A=1,r=2,n=2;break}if((0|i[11120+(216*e|0)+192>>2])==(0|A)){A=2,r=2,n=0;break}if((0|i[11120+(216*e|0)+200>>2])==(0|A)){A=2,r=2,n=1;break}if((0|i[11120+(216*e|0)+208>>2])==(0|A)){A=2,r=2,n=2;break}return-1}A=2,r=1,n=2}else A=2,r=1,n=1;else A=2,r=1,n=0;else A=1,r=1,n=2;else A=1,r=1,n=1;else A=1,r=1,n=0;else A=0,r=1,n=2;else A=0,r=1,n=1;else A=0,r=1,n=0;else A=2,r=0,n=2;else A=2,r=0,n=1;else A=2,r=0,n=0;else A=1,r=0,n=2;else A=1,r=0,n=1;else A=1,r=0,n=0;else A=0,r=0,n=2;else A=0,r=0,n=1;else A=0,r=0,n=0}while(0);return 0|i[11120+(216*e|0)+(72*r|0)+(24*A|0)+(n<<3)+4>>2]}(n,0|i[t>>2]);A:do{if(0|Y(n)){switch(0|n){case 4:A=0;break;case 14:A=1;break;case 24:A=2;break;case 38:A=3;break;case 49:A=4;break;case 58:A=5;break;case 63:A=6;break;case 72:A=7;break;case 83:A=8;break;case 97:A=9;break;case 107:A=10;break;case 117:A=11;break;default:o=1;break A}if(l=0|i[26416+(24*A|0)+8>>2],e=0|i[26416+(24*A|0)+16>>2],(0|(A=0|i[t>>2]))!=(0|i[u>>2])&&(u=0|V(n))|(0|(A=0|i[t>>2]))==(0|e)&&(o=(o+1|0)%6|0),3==(0|a)&(0|A)==(0|e)){o=(o+5|0)%6|0,l=22;break}5==(0|a)&(0|A)==(0|l)?(o=(o+1|0)%6|0,l=22):l=22}else l=22}while(0);return 22==(0|l)&&(i[r>>2]=o,o=0),T=f,0|o}function Re(A,e,r,n){r|=0,n|=0;var t,a,f,o,l,u=0,c=0,s=0,b=0,g=0,d=0,w=0,B=0,v=0,k=0,m=0;if(l=T,T=T+32|0,m=l+24|0,o=l+20|0,f=l+8|0,a=l+16|0,t=l,g=(g=0==(0|SA(A|=0,e|=0)))?6:5,w=0|ar(0|A,0|e,52),E(),g>>>0<=r>>>0)return T=l,2;!(B=0==(0|(w&=15)))&&0==((v=0|fr(7,0,3*(15^w)|0))&A|0)&0==((0|E())&e|0)?u=r:c=4;A:do{if(4==(0|c)){if((0|((u=0!=(0|SA(A,e)))?4:5))<(0|r))return T=l,1;if(0|xe(A,e,m))return T=l,1;if(c=(0|i[m>>2])+r|0,7==(0|(v=0|i[(u=u?26704+(((0|c)%5|0)<<2)|0:26736+(((0|c)%6|0)<<2)|0)>>2])))return T=l,1;i[o>>2]=0,u=0|U(A,e,v,o,f);do{if(!u){if(d=0|i[(b=f)>>2],c=(s=(b=0|i[b+4>>2])>>>0<e>>>0|(0|b)==(0|e)&d>>>0<A>>>0)?d:A,s=s?b:e,!B&&0==(d&(B=0|fr(7,0,3*(15^w)|0))|0)&0==(b&(0|E())|0))u=r;else{if(b=(r+-1+g|0)%(0|g)|0,u=0|SA(A,e),(0|b)<0&&_(27795,27797,246,27806),(0|((g=0!=(0|u))?4:5))<(0|b)&&_(27795,27797,246,27806),0|xe(A,e,m)&&_(27795,27797,246,27806),u=(0|i[m>>2])+b|0,7==(0|(b=0|i[(u=g?26704+(((0|u)%5|0)<<2)|0:26736+(((0|u)%6|0)<<2)|0)>>2]))&&_(27795,27797,246,27806),i[a>>2]=0,0|(u=0|U(A,e,b,a,t)))break;g=0|i[(d=t)>>2],d=0|i[d+4>>2];do{if(d>>>0<s>>>0|(0|d)==(0|s)&g>>>0<c>>>0){if(c=0|SA(g,d)?0|x(g,d,A,e):0|i[26800+((((0|i[a>>2])+(0|i[26768+(b<<2)>>2])|0)%6|0)<<2)>>2],u=0|SA(g,d),(c+-1|0)>>>0>5){u=-1,c=g,s=d;break}if(1==(0|c)&(u=0!=(0|u))){u=-1,c=g,s=d;break}do{if(!(0|xe(g,d,m))){if(u){u=(5+(0|i[26352+(c<<2)>>2])-(0|i[m>>2])|0)%5|0;break}u=(6+(0|i[26384+(c<<2)>>2])-(0|i[m>>2])|0)%6|0;break}u=-1}while(0);c=g,s=d}else u=r}while(0);d=0|i[(b=f)>>2],b=0|i[b+4>>2]}if((0|c)==(0|d)&(0|s)==(0|b)){if(A=(g=0!=(0|SA(d,b)))?0|x(d,b,A,e):0|i[26800+((((0|i[o>>2])+(0|i[26768+(v<<2)>>2])|0)%6|0)<<2)>>2],u=0|SA(d,b),(A+-1|0)>>>0<=5&&!(1==(0|A)&(k=0!=(0|u))))do{if(!(0|xe(d,b,m))){if(k){u=(5+(0|i[26352+(A<<2)>>2])-(0|i[m>>2])|0)%5|0;break}u=(6+(0|i[26384+(A<<2)>>2])-(0|i[m>>2])|0)%6|0;break}u=-1}while(0);else u=-1;u=6==(0|(u=u+1|0))|g&5==(0|u)?0:u}e=s,A=c;break A}}while(0);return T=l,0|u}}while(0);return k=0|fr(0|u,0,56),m=0|E()|-2130706433&e|536870912,i[n>>2]=k|A,i[n+4>>2]=m,T=l,0}function Ye(A,e,r){A|=0,r|=0;var n=0;(0|(e|=0))>0?(n=0|Ne(e,4),i[A>>2]=n,n||_(27819,27842,40,27856)):i[A>>2]=0,i[A+4>>2]=e,i[A+8>>2]=0,i[A+12>>2]=r}function Ve(A){var e,r,n,a=0,f=0,o=0,u=0;e=4+(A|=0)|0,r=A+12|0,n=A+8|0;A:for(;;){for(f=0|i[e>>2],a=0;;){if((0|a)>=(0|f))break A;if(u=0|i[(o=0|i[A>>2])+(a<<2)>>2])break;a=a+1|0}a=o+(~~(+l(+ +c(10,+ +(15-(0|i[r>>2])|0))*(+t[u>>3]+ +t[u+8>>3]))%+(0|f))>>>0<<2)|0,f=0|i[a>>2];e:do{if(0|f){if(o=u+32|0,(0|f)==(0|u))i[a>>2]=i[o>>2];else{if(!(a=0|i[(f=f+32|0)>>2]))break;for(;(0|a)!=(0|u);)if(!(a=0|i[(f=a+32|0)>>2]))break e;i[f>>2]=i[o>>2]}je(u),i[n>>2]=(0|i[n>>2])-1}}while(0)}je(0|i[A>>2])}function Oe(A){var e,r=0,n=0;for(e=0|i[4+(A|=0)>>2],n=0;;){if((0|n)>=(0|e)){r=0,n=4;break}if(r=0|i[(0|i[A>>2])+(n<<2)>>2]){n=4;break}n=n+1|0}return 4==(0|n)?0|r:0}function Se(A,e){e|=0;var r=0,n=0,a=0,f=0;if(r=~~(+l(+ +c(10,+ +(15-(0|i[12+(A|=0)>>2])|0))*(+t[e>>3]+ +t[e+8>>3]))%+(0|i[A+4>>2]))>>>0,!(n=0|i[(r=(0|i[A>>2])+(r<<2)|0)>>2]))return 1;f=e+32|0;do{if((0|n)!=(0|e)){if(!(r=0|i[n+32>>2]))return 1;for(a=r;;){if((0|a)==(0|e)){a=8;break}if(!(r=0|i[a+32>>2])){r=1,a=10;break}n=a,a=r}if(8==(0|a)){i[n+32>>2]=i[f>>2];break}if(10==(0|a))return 0|r}else i[r>>2]=i[f>>2]}while(0);return je(e),i[(f=A+8|0)>>2]=(0|i[f>>2])-1,0}function He(A,e,r){A|=0,e|=0,r|=0;var n,a=0,f=0,o=0;(n=0|Ke(40))||_(27872,27842,98,27885),i[n>>2]=i[e>>2],i[n+4>>2]=i[e+4>>2],i[n+8>>2]=i[e+8>>2],i[n+12>>2]=i[e+12>>2],i[(f=n+16|0)>>2]=i[r>>2],i[f+4>>2]=i[r+4>>2],i[f+8>>2]=i[r+8>>2],i[f+12>>2]=i[r+12>>2],i[n+32>>2]=0,f=~~(+l(+ +c(10,+ +(15-(0|i[A+12>>2])|0))*(+t[e>>3]+ +t[e+8>>3]))%+(0|i[A+4>>2]))>>>0,a=0|i[(f=(0|i[A>>2])+(f<<2)|0)>>2];do{if(a){for(;!(0|oe(a,e)&&0|oe(a+16|0,r));)if(!(0|i[(a=0==(0|(f=0|i[a+32>>2]))?a:f)+32>>2])){o=10;break}if(10==(0|o)){i[a+32>>2]=n;break}return je(n),0|a}i[f>>2]=n}while(0);return i[(o=A+8|0)>>2]=1+(0|i[o>>2]),0|n}function We(A,e,r){e|=0,r|=0;var n=0,a=0;if(a=~~(+l(+ +c(10,+ +(15-(0|i[12+(A|=0)>>2])|0))*(+t[e>>3]+ +t[e+8>>3]))%+(0|i[A+4>>2]))>>>0,!(a=0|i[(0|i[A>>2])+(a<<2)>>2]))return 0;if(!r){for(A=a;;){if(0|oe(A,e)){n=10;break}if(!(A=0|i[A+32>>2])){A=0,n=10;break}}if(10==(0|n))return 0|A}for(A=a;;){if(0|oe(A,e)&&0|oe(A+16|0,r)){n=10;break}if(!(A=0|i[A+32>>2])){A=0,n=10;break}}return 10==(0|n)?0|A:0}function Ze(A,e){var r;if(e|=0,r=~~(+l(+ +c(10,+ +(15-(0|i[12+(A|=0)>>2])|0))*(+t[e>>3]+ +t[e+8>>3]))%+(0|i[A+4>>2]))>>>0,!(A=0|i[(0|i[A>>2])+(r<<2)>>2]))return 0;for(;;){if(0|oe(A,e)){e=5;break}if(!(A=0|i[A+32>>2])){A=0,e=5;break}}return 5==(0|e)?0|A:0}function Je(A){return 0|~~+gr(+(A=+A))}function Ke(A){A|=0;var e,r=0,n=0,t=0,a=0,f=0,o=0,l=0,u=0,c=0,s=0,b=0,g=0,d=0,w=0,B=0,v=0,k=0,m=0,D=0,Q=0;e=T,T=T+16|0,g=e;do{if(A>>>0<245){if(3&(n=(b=0|i[6977])>>>(A=(c=A>>>0<11?16:A+11&-8)>>>3))|0)return(0|(f=0|i[(a=8+(t=0|i[(n=8+(A=27948+((r=(1&n^1)+A|0)<<1<<2)|0)|0)>>2])|0)>>2]))==(0|A)?i[6977]=b&~(1<<r):(i[f+12>>2]=A,i[n>>2]=f),i[t+4>>2]=3|(Q=r<<3),i[(Q=t+Q+4|0)>>2]=1|i[Q>>2],T=e,0|a;if(c>>>0>(s=0|i[6979])>>>0){if(0|n)return r=((r=n<<A&((r=2<<A)|0-r))&0-r)-1|0,(0|(n=0|i[(l=8+(f=0|i[(A=8+(r=27948+((t=((n=(r>>>=l=r>>>12&16)>>>5&8)|l|(f=(r>>>=n)>>>2&4)|(A=(r>>>=f)>>>1&2)|(t=(r>>>=A)>>>1&1))+(r>>>t)|0)<<1<<2)|0)|0)>>2])|0)>>2]))==(0|r)?i[6977]=A=b&~(1<<t):(i[n+12>>2]=r,i[A>>2]=n,A=b),o=(Q=t<<3)-c|0,i[f+4>>2]=3|c,i[4+(a=f+c|0)>>2]=1|o,i[f+Q>>2]=o,0|s&&(t=0|i[6982],n=27948+((r=s>>>3)<<1<<2)|0,A&(r=1<<r)?r=0|i[(A=n+8|0)>>2]:(i[6977]=A|r,r=n,A=n+8|0),i[A>>2]=t,i[r+12>>2]=t,i[t+8>>2]=r,i[t+12>>2]=n),i[6979]=o,i[6982]=a,T=e,0|l;if(f=0|i[6978]){for(n=(f&0-f)-1|0,n=u=0|i[28212+(((t=(n>>>=a=n>>>12&16)>>>5&8)|a|(o=(n>>>=t)>>>2&4)|(l=(n>>>=o)>>>1&2)|(u=(n>>>=l)>>>1&1))+(n>>>u)<<2)>>2],l=u,u=(-8&i[u+4>>2])-c|0;(A=0|i[n+16>>2])||(A=0|i[n+20>>2]);)n=A,l=(a=(o=(-8&i[A+4>>2])-c|0)>>>0<u>>>0)?A:l,u=a?o:u;if((o=l+c|0)>>>0>l>>>0){a=0|i[l+24>>2],r=0|i[l+12>>2];do{if((0|r)==(0|l)){if(!(r=0|i[(A=l+20|0)>>2])&&!(r=0|i[(A=l+16|0)>>2])){n=0;break}for(;;)if(n=0|i[(t=r+20|0)>>2])r=n,A=t;else{if(!(n=0|i[(t=r+16|0)>>2]))break;r=n,A=t}i[A>>2]=0,n=r}else i[12+(n=0|i[l+8>>2])>>2]=r,i[r+8>>2]=n,n=r}while(0);do{if(0|a){if((0|l)==(0|i[(A=28212+((r=0|i[l+28>>2])<<2)|0)>>2])){if(i[A>>2]=n,!n){i[6978]=f&~(1<<r);break}}else if(i[((0|i[(Q=a+16|0)>>2])==(0|l)?Q:a+20|0)>>2]=n,!n)break;i[n+24>>2]=a,0|(r=0|i[l+16>>2])&&(i[n+16>>2]=r,i[r+24>>2]=n),0|(r=0|i[l+20>>2])&&(i[n+20>>2]=r,i[r+24>>2]=n)}}while(0);return u>>>0<16?(i[l+4>>2]=3|(Q=u+c|0),i[(Q=l+Q+4|0)>>2]=1|i[Q>>2]):(i[l+4>>2]=3|c,i[o+4>>2]=1|u,i[o+u>>2]=u,0|s&&(t=0|i[6982],n=27948+((r=s>>>3)<<1<<2)|0,(r=1<<r)&b?r=0|i[(A=n+8|0)>>2]:(i[6977]=r|b,r=n,A=n+8|0),i[A>>2]=t,i[r+12>>2]=t,i[t+8>>2]=r,i[t+12>>2]=n),i[6979]=u,i[6982]=o),T=e,0|l+8}b=c}else b=c}else b=c}else if(A>>>0<=4294967231)if(c=-8&(A=A+11|0),t=0|i[6978]){a=0-c|0,u=(A>>>=8)?c>>>0>16777215?31:c>>>(7+(u=14-((l=(520192+(B=A<<(b=(A+1048320|0)>>>16&8))|0)>>>16&4)|b|(u=(245760+(B<<=l)|0)>>>16&2))+(B<<u>>>15)|0)|0)&1|u<<1:0,n=0|i[28212+(u<<2)>>2];A:do{if(n)for(A=0,l=c<<(31==(0|u)?0:25-(u>>>1)|0),f=0;;){if((o=(-8&i[n+4>>2])-c|0)>>>0<a>>>0){if(!o){A=n,a=0,B=65;break A}A=n,a=o}if(f=0==(0|(B=0|i[n+20>>2]))|(0|B)==(0|(n=0|i[n+16+(l>>>31<<2)>>2]))?f:B,!n){n=f,B=61;break}l<<=1}else n=0,A=0,B=61}while(0);if(61==(0|B)){if(0==(0|n)&0==(0|A)){if(!(A=((A=2<<u)|0-A)&t)){b=c;break}b=(A&0-A)-1|0,A=0,n=0|i[28212+(((f=(b>>>=o=b>>>12&16)>>>5&8)|o|(l=(b>>>=f)>>>2&4)|(u=(b>>>=l)>>>1&2)|(n=(b>>>=u)>>>1&1))+(b>>>n)<<2)>>2]}n?B=65:(l=A,o=a)}if(65==(0|B))for(f=n;;){if(a=(n=(b=(-8&i[f+4>>2])-c|0)>>>0<a>>>0)?b:a,A=n?f:A,(n=0|i[f+16>>2])||(n=0|i[f+20>>2]),!n){l=A,o=a;break}f=n}if(0!=(0|l)&&o>>>0<((0|i[6979])-c|0)>>>0&&(s=l+c|0)>>>0>l>>>0){f=0|i[l+24>>2],r=0|i[l+12>>2];do{if((0|r)==(0|l)){if(!(r=0|i[(A=l+20|0)>>2])&&!(r=0|i[(A=l+16|0)>>2])){r=0;break}for(;;)if(n=0|i[(a=r+20|0)>>2])r=n,A=a;else{if(!(n=0|i[(a=r+16|0)>>2]))break;r=n,A=a}i[A>>2]=0}else i[12+(Q=0|i[l+8>>2])>>2]=r,i[r+8>>2]=Q}while(0);do{if(f){if((0|l)==(0|i[(n=28212+((A=0|i[l+28>>2])<<2)|0)>>2])){if(i[n>>2]=r,!r){i[6978]=t&=~(1<<A);break}}else if(i[((0|i[(Q=f+16|0)>>2])==(0|l)?Q:f+20|0)>>2]=r,!r)break;i[r+24>>2]=f,0|(A=0|i[l+16>>2])&&(i[r+16>>2]=A,i[A+24>>2]=r),(A=0|i[l+20>>2])&&(i[r+20>>2]=A,i[A+24>>2]=r)}}while(0);A:do{if(o>>>0<16)i[l+4>>2]=3|(Q=o+c|0),i[(Q=l+Q+4|0)>>2]=1|i[Q>>2];else{if(i[l+4>>2]=3|c,i[s+4>>2]=1|o,i[s+o>>2]=o,r=o>>>3,o>>>0<256){n=27948+(r<<1<<2)|0,(A=0|i[6977])&(r=1<<r)?r=0|i[(A=n+8|0)>>2]:(i[6977]=A|r,r=n,A=n+8|0),i[A>>2]=s,i[r+12>>2]=s,i[s+8>>2]=r,i[s+12>>2]=n;break}if(r=28212+((n=(r=o>>>8)?o>>>0>16777215?31:o>>>(7+(n=14-((m=(520192+(Q=r<<(D=(r+1048320|0)>>>16&8))|0)>>>16&4)|D|(n=(245760+(Q<<=m)|0)>>>16&2))+(Q<<n>>>15)|0)|0)&1|n<<1:0)<<2)|0,i[s+28>>2]=n,i[4+(A=s+16|0)>>2]=0,i[A>>2]=0,!(t&(A=1<<n))){i[6978]=t|A,i[r>>2]=s,i[s+24>>2]=r,i[s+12>>2]=s,i[s+8>>2]=s;break}r=0|i[r>>2];e:do{if((-8&i[r+4>>2]|0)!=(0|o)){for(t=o<<(31==(0|n)?0:25-(n>>>1)|0);A=0|i[(n=r+16+(t>>>31<<2)|0)>>2];){if((-8&i[A+4>>2]|0)==(0|o)){r=A;break e}t<<=1,r=A}i[n>>2]=s,i[s+24>>2]=r,i[s+12>>2]=s,i[s+8>>2]=s;break A}}while(0);i[12+(Q=0|i[(D=r+8|0)>>2])>>2]=s,i[D>>2]=s,i[s+8>>2]=Q,i[s+12>>2]=r,i[s+24>>2]=0}}while(0);return T=e,0|l+8}b=c}else b=c;else b=-1}while(0);if((n=0|i[6979])>>>0>=b>>>0)return A=0|i[6982],(r=n-b|0)>>>0>15?(i[6982]=Q=A+b|0,i[6979]=r,i[Q+4>>2]=1|r,i[A+n>>2]=r,i[A+4>>2]=3|b):(i[6979]=0,i[6982]=0,i[A+4>>2]=3|n,i[(Q=A+n+4|0)>>2]=1|i[Q>>2]),T=e,0|A+8;if((o=0|i[6980])>>>0>b>>>0)return i[6980]=m=o-b|0,i[6983]=D=(Q=0|i[6983])+b|0,i[D+4>>2]=1|m,i[Q+4>>2]=3|b,T=e,0|Q+8;if(0|i[7095]?A=0|i[7097]:(i[7097]=4096,i[7096]=4096,i[7098]=-1,i[7099]=-1,i[7100]=0,i[7088]=0,i[7095]=-16&g^1431655768,A=4096),l=b+48|0,(c=(f=A+(u=b+47|0)|0)&(a=0-A|0))>>>0<=b>>>0)return T=e,0;if(0|(A=0|i[7087])&&(g=(s=0|i[7085])+c|0)>>>0<=s>>>0|g>>>0>A>>>0)return T=e,0;A:do{if(4&i[7088])r=0,B=143;else{n=0|i[6983];e:do{if(n){for(t=28356;!((g=0|i[t>>2])>>>0<=n>>>0&&(g+(0|i[t+4>>2])|0)>>>0>n>>>0);){if(!(A=0|i[t+8>>2])){B=128;break e}t=A}if((r=f-o&a)>>>0<2147483647)if((0|(A=0|dr(0|r)))==((0|i[t>>2])+(0|i[t+4>>2])|0)){if(-1!=(0|A)){o=r,f=A,B=145;break A}}else t=A,B=136;else r=0}else B=128}while(0);do{if(128==(0|B))if(-1!=(0|(n=0|dr(0)))&&(w=(r=(0==((w=(d=0|i[7096])-1|0)&(r=n)|0)?0:(w+r&0-d)-r|0)+c|0)+(d=0|i[7085])|0,r>>>0>b>>>0&r>>>0<2147483647)){if(0|(g=0|i[7087])&&w>>>0<=d>>>0|w>>>0>g>>>0){r=0;break}if((0|(A=0|dr(0|r)))==(0|n)){o=r,f=n,B=145;break A}t=A,B=136}else r=0}while(0);do{if(136==(0|B)){if(n=0-r|0,!(l>>>0>r>>>0&r>>>0<2147483647&-1!=(0|t))){if(-1==(0|t)){r=0;break}o=r,f=t,B=145;break A}if((A=u-r+(A=0|i[7097])&0-A)>>>0>=2147483647){o=r,f=t,B=145;break A}if(-1==(0|dr(0|A))){dr(0|n),r=0;break}o=A+r|0,f=t,B=145;break A}}while(0);i[7088]=4|i[7088],B=143}}while(0);if(143==(0|B)&&c>>>0<2147483647&&!(-1==(0|(m=0|dr(0|c)))|1^(k=(v=(w=0|dr(0))-m|0)>>>0>(b+40|0)>>>0)|m>>>0<w>>>0&-1!=(0|m)&-1!=(0|w)^1)&&(o=k?v:r,f=m,B=145),145==(0|B)){i[7085]=r=(0|i[7085])+o|0,r>>>0>(0|i[7086])>>>0&&(i[7086]=r),u=0|i[6983];A:do{if(u){for(r=28356;;){if((0|f)==((A=0|i[r>>2])+(n=0|i[r+4>>2])|0)){B=154;break}if(!(t=0|i[r+8>>2]))break;r=t}if(154==(0|B)&&(D=r+4|0,0==(8&i[r+12>>2]|0))&&f>>>0>u>>>0&A>>>0<=u>>>0){i[D>>2]=n+o,D=u+(m=0==(7&(m=u+8|0)|0)?0:0-m&7)|0,m=(Q=(0|i[6980])+o|0)-m|0,i[6983]=D,i[6980]=m,i[D+4>>2]=1|m,i[u+Q+4>>2]=40,i[6984]=i[7099];break}for(f>>>0<(0|i[6981])>>>0&&(i[6981]=f),n=f+o|0,r=28356;;){if((0|i[r>>2])==(0|n)){B=162;break}if(!(A=0|i[r+8>>2]))break;r=A}if(162==(0|B)&&0==(8&i[r+12>>2]|0)){i[r>>2]=f,i[(s=r+4|0)>>2]=(0|i[s>>2])+o,c=(s=f+(0==(7&(s=f+8|0)|0)?0:0-s&7)|0)+b|0,l=(r=n+(0==(7&(r=n+8|0)|0)?0:0-r&7)|0)-s-b|0,i[s+4>>2]=3|b;e:do{if((0|u)==(0|r))i[6980]=Q=(0|i[6980])+l|0,i[6983]=c,i[c+4>>2]=1|Q;else{if((0|i[6982])==(0|r)){i[6979]=Q=(0|i[6979])+l|0,i[6982]=c,i[c+4>>2]=1|Q,i[c+Q>>2]=Q;break}if(1==(3&(A=0|i[r+4>>2])|0)){o=-8&A,t=A>>>3;r:do{if(A>>>0<256){if((0|(n=0|i[r+12>>2]))==(0|(A=0|i[r+8>>2]))){i[6977]=i[6977]&~(1<<t);break}i[A+12>>2]=n,i[n+8>>2]=A;break}f=0|i[r+24>>2],A=0|i[r+12>>2];do{if((0|A)==(0|r)){if(A=0|i[(t=4+(n=r+16|0)|0)>>2])n=t;else if(!(A=0|i[n>>2])){A=0;break}for(;;)if(t=0|i[(a=A+20|0)>>2])A=t,n=a;else{if(!(t=0|i[(a=A+16|0)>>2]))break;A=t,n=a}i[n>>2]=0}else i[12+(Q=0|i[r+8>>2])>>2]=A,i[A+8>>2]=Q}while(0);if(!f)break;t=28212+((n=0|i[r+28>>2])<<2)|0;do{if((0|i[t>>2])==(0|r)){if(i[t>>2]=A,0|A)break;i[6978]=i[6978]&~(1<<n);break r}if(i[((0|i[(Q=f+16|0)>>2])==(0|r)?Q:f+20|0)>>2]=A,!A)break r}while(0);if(i[A+24>>2]=f,0|(t=0|i[(n=r+16|0)>>2])&&(i[A+16>>2]=t,i[t+24>>2]=A),!(n=0|i[n+4>>2]))break;i[A+20>>2]=n,i[n+24>>2]=A}while(0);r=r+o|0,a=o+l|0}else a=l;if(i[(r=r+4|0)>>2]=-2&i[r>>2],i[c+4>>2]=1|a,i[c+a>>2]=a,r=a>>>3,a>>>0<256){n=27948+(r<<1<<2)|0,(A=0|i[6977])&(r=1<<r)?r=0|i[(A=n+8|0)>>2]:(i[6977]=A|r,r=n,A=n+8|0),i[A>>2]=c,i[r+12>>2]=c,i[c+8>>2]=r,i[c+12>>2]=n;break}r=a>>>8;do{if(r){if(a>>>0>16777215){t=31;break}t=a>>>(7+(t=14-((m=(520192+(Q=r<<(D=(r+1048320|0)>>>16&8))|0)>>>16&4)|D|(t=(245760+(Q<<=m)|0)>>>16&2))+(Q<<t>>>15)|0)|0)&1|t<<1}else t=0}while(0);if(r=28212+(t<<2)|0,i[c+28>>2]=t,i[4+(A=c+16|0)>>2]=0,i[A>>2]=0,!((A=0|i[6978])&(n=1<<t))){i[6978]=A|n,i[r>>2]=c,i[c+24>>2]=r,i[c+12>>2]=c,i[c+8>>2]=c;break}r=0|i[r>>2];r:do{if((-8&i[r+4>>2]|0)!=(0|a)){for(t=a<<(31==(0|t)?0:25-(t>>>1)|0);A=0|i[(n=r+16+(t>>>31<<2)|0)>>2];){if((-8&i[A+4>>2]|0)==(0|a)){r=A;break r}t<<=1,r=A}i[n>>2]=c,i[c+24>>2]=r,i[c+12>>2]=c,i[c+8>>2]=c;break e}}while(0);i[12+(Q=0|i[(D=r+8|0)>>2])>>2]=c,i[D>>2]=c,i[c+8>>2]=Q,i[c+12>>2]=r,i[c+24>>2]=0}}while(0);return T=e,0|s+8}for(r=28356;!((A=0|i[r>>2])>>>0<=u>>>0&&(Q=A+(0|i[r+4>>2])|0)>>>0>u>>>0);)r=0|i[r+8>>2];r=(A=(A=(a=Q+-47|0)+(0==(7&(A=a+8|0)|0)?0:0-A&7)|0)>>>0<(a=u+16|0)>>>0?u:A)+8|0,D=f+(m=0==(7&(m=f+8|0)|0)?0:0-m&7)|0,m=(n=o+-40|0)-m|0,i[6983]=D,i[6980]=m,i[D+4>>2]=1|m,i[f+n+4>>2]=40,i[6984]=i[7099],i[(n=A+4|0)>>2]=27,i[r>>2]=i[7089],i[r+4>>2]=i[7090],i[r+8>>2]=i[7091],i[r+12>>2]=i[7092],i[7089]=f,i[7090]=o,i[7092]=0,i[7091]=r,r=A+24|0;do{D=r,i[(r=r+4|0)>>2]=7}while((D+8|0)>>>0<Q>>>0);if((0|A)!=(0|u)){if(f=A-u|0,i[n>>2]=-2&i[n>>2],i[u+4>>2]=1|f,i[A>>2]=f,r=f>>>3,f>>>0<256){n=27948+(r<<1<<2)|0,(A=0|i[6977])&(r=1<<r)?r=0|i[(A=n+8|0)>>2]:(i[6977]=A|r,r=n,A=n+8|0),i[A>>2]=u,i[r+12>>2]=u,i[u+8>>2]=r,i[u+12>>2]=n;break}if(n=28212+((t=(r=f>>>8)?f>>>0>16777215?31:f>>>(7+(t=14-((m=(520192+(Q=r<<(D=(r+1048320|0)>>>16&8))|0)>>>16&4)|D|(t=(245760+(Q<<=m)|0)>>>16&2))+(Q<<t>>>15)|0)|0)&1|t<<1:0)<<2)|0,i[u+28>>2]=t,i[u+20>>2]=0,i[a>>2]=0,!((r=0|i[6978])&(A=1<<t))){i[6978]=r|A,i[n>>2]=u,i[u+24>>2]=n,i[u+12>>2]=u,i[u+8>>2]=u;break}r=0|i[n>>2];e:do{if((-8&i[r+4>>2]|0)!=(0|f)){for(t=f<<(31==(0|t)?0:25-(t>>>1)|0);A=0|i[(n=r+16+(t>>>31<<2)|0)>>2];){if((-8&i[A+4>>2]|0)==(0|f)){r=A;break e}t<<=1,r=A}i[n>>2]=u,i[u+24>>2]=r,i[u+12>>2]=u,i[u+8>>2]=u;break A}}while(0);i[12+(Q=0|i[(D=r+8|0)>>2])>>2]=u,i[D>>2]=u,i[u+8>>2]=Q,i[u+12>>2]=r,i[u+24>>2]=0}}else 0==(0|(Q=0|i[6981]))|f>>>0<Q>>>0&&(i[6981]=f),i[7089]=f,i[7090]=o,i[7092]=0,i[6986]=i[7095],i[6985]=-1,i[6990]=27948,i[6989]=27948,i[6992]=27956,i[6991]=27956,i[6994]=27964,i[6993]=27964,i[6996]=27972,i[6995]=27972,i[6998]=27980,i[6997]=27980,i[7e3]=27988,i[6999]=27988,i[7002]=27996,i[7001]=27996,i[7004]=28004,i[7003]=28004,i[7006]=28012,i[7005]=28012,i[7008]=28020,i[7007]=28020,i[7010]=28028,i[7009]=28028,i[7012]=28036,i[7011]=28036,i[7014]=28044,i[7013]=28044,i[7016]=28052,i[7015]=28052,i[7018]=28060,i[7017]=28060,i[7020]=28068,i[7019]=28068,i[7022]=28076,i[7021]=28076,i[7024]=28084,i[7023]=28084,i[7026]=28092,i[7025]=28092,i[7028]=28100,i[7027]=28100,i[7030]=28108,i[7029]=28108,i[7032]=28116,i[7031]=28116,i[7034]=28124,i[7033]=28124,i[7036]=28132,i[7035]=28132,i[7038]=28140,i[7037]=28140,i[7040]=28148,i[7039]=28148,i[7042]=28156,i[7041]=28156,i[7044]=28164,i[7043]=28164,i[7046]=28172,i[7045]=28172,i[7048]=28180,i[7047]=28180,i[7050]=28188,i[7049]=28188,i[7052]=28196,i[7051]=28196,D=f+(m=0==(7&(m=f+8|0)|0)?0:0-m&7)|0,m=(Q=o+-40|0)-m|0,i[6983]=D,i[6980]=m,i[D+4>>2]=1|m,i[f+Q+4>>2]=40,i[6984]=i[7099]}while(0);if((r=0|i[6980])>>>0>b>>>0)return i[6980]=m=r-b|0,i[6983]=D=(Q=0|i[6983])+b|0,i[D+4>>2]=1|m,i[Q+4>>2]=3|b,T=e,0|Q+8}return i[(Q=27904)>>2]=12,T=e,0}function je(A){var e=0,r=0,n=0,t=0,a=0,f=0,o=0,l=0;if(A|=0){t=0|i[6981],l=(r=A+-8|0)+(e=-8&(A=0|i[A+-4>>2]))|0;do{if(1&A)o=r,f=r;else{if(!(3&A))return;if(a=(n=0|i[r>>2])+e|0,(f=r+(0-n)|0)>>>0<t>>>0)return;if((0|i[6982])==(0|f)){if(3!=(3&(e=0|i[(A=l+4|0)>>2])|0)){o=f,e=a;break}return i[6979]=a,i[A>>2]=-2&e,i[f+4>>2]=1|a,void(i[f+a>>2]=a)}if(r=n>>>3,n>>>0<256){if((0|(e=0|i[f+12>>2]))==(0|(A=0|i[f+8>>2]))){i[6977]=i[6977]&~(1<<r),o=f,e=a;break}i[A+12>>2]=e,i[e+8>>2]=A,o=f,e=a;break}t=0|i[f+24>>2],A=0|i[f+12>>2];do{if((0|A)==(0|f)){if(A=0|i[(r=4+(e=f+16|0)|0)>>2])e=r;else if(!(A=0|i[e>>2])){A=0;break}for(;;)if(r=0|i[(n=A+20|0)>>2])A=r,e=n;else{if(!(r=0|i[(n=A+16|0)>>2]))break;A=r,e=n}i[e>>2]=0}else i[12+(o=0|i[f+8>>2])>>2]=A,i[A+8>>2]=o}while(0);if(t){if((0|i[(r=28212+((e=0|i[f+28>>2])<<2)|0)>>2])==(0|f)){if(i[r>>2]=A,!A){i[6978]=i[6978]&~(1<<e),o=f,e=a;break}}else if(i[((0|i[(o=t+16|0)>>2])==(0|f)?o:t+20|0)>>2]=A,!A){o=f,e=a;break}i[A+24>>2]=t,0|(r=0|i[(e=f+16|0)>>2])&&(i[A+16>>2]=r,i[r+24>>2]=A),(e=0|i[e+4>>2])?(i[A+20>>2]=e,i[e+24>>2]=A,o=f,e=a):(o=f,e=a)}else o=f,e=a}}while(0);if(!(f>>>0>=l>>>0)&&1&(n=0|i[(A=l+4|0)>>2])){if(2&n)i[A>>2]=-2&n,i[o+4>>2]=1|e,i[f+e>>2]=e,t=e;else{if((0|i[6983])==(0|l)){if(i[6980]=l=(0|i[6980])+e|0,i[6983]=o,i[o+4>>2]=1|l,(0|o)!=(0|i[6982]))return;return i[6982]=0,void(i[6979]=0)}if((0|i[6982])==(0|l))return i[6979]=l=(0|i[6979])+e|0,i[6982]=f,i[o+4>>2]=1|l,void(i[f+l>>2]=l);t=(-8&n)+e|0,r=n>>>3;do{if(n>>>0<256){if((0|(A=0|i[l+12>>2]))==(0|(e=0|i[l+8>>2]))){i[6977]=i[6977]&~(1<<r);break}i[e+12>>2]=A,i[A+8>>2]=e;break}a=0|i[l+24>>2],A=0|i[l+12>>2];do{if((0|A)==(0|l)){if(A=0|i[(r=4+(e=l+16|0)|0)>>2])e=r;else if(!(A=0|i[e>>2])){r=0;break}for(;;)if(r=0|i[(n=A+20|0)>>2])A=r,e=n;else{if(!(r=0|i[(n=A+16|0)>>2]))break;A=r,e=n}i[e>>2]=0,r=A}else i[12+(r=0|i[l+8>>2])>>2]=A,i[A+8>>2]=r,r=A}while(0);if(0|a){if((0|i[(e=28212+((A=0|i[l+28>>2])<<2)|0)>>2])==(0|l)){if(i[e>>2]=r,!r){i[6978]=i[6978]&~(1<<A);break}}else if(i[((0|i[(n=a+16|0)>>2])==(0|l)?n:a+20|0)>>2]=r,!r)break;i[r+24>>2]=a,0|(e=0|i[(A=l+16|0)>>2])&&(i[r+16>>2]=e,i[e+24>>2]=r),0|(A=0|i[A+4>>2])&&(i[r+20>>2]=A,i[A+24>>2]=r)}}while(0);if(i[o+4>>2]=1|t,i[f+t>>2]=t,(0|o)==(0|i[6982]))return void(i[6979]=t)}if(A=t>>>3,t>>>0<256)return r=27948+(A<<1<<2)|0,(e=0|i[6977])&(A=1<<A)?A=0|i[(e=r+8|0)>>2]:(i[6977]=e|A,A=r,e=r+8|0),i[e>>2]=o,i[A+12>>2]=o,i[o+8>>2]=A,void(i[o+12>>2]=r);A=28212+((n=(A=t>>>8)?t>>>0>16777215?31:t>>>(7+(n=14-((a=(520192+(l=A<<(f=(A+1048320|0)>>>16&8))|0)>>>16&4)|f|(n=(245760+(l<<=a)|0)>>>16&2))+(l<<n>>>15)|0)|0)&1|n<<1:0)<<2)|0,i[o+28>>2]=n,i[o+20>>2]=0,i[o+16>>2]=0,e=0|i[6978],r=1<<n;A:do{if(e&r){A=0|i[A>>2];e:do{if((-8&i[A+4>>2]|0)!=(0|t)){for(n=t<<(31==(0|n)?0:25-(n>>>1)|0);e=0|i[(r=A+16+(n>>>31<<2)|0)>>2];){if((-8&i[e+4>>2]|0)==(0|t)){A=e;break e}n<<=1,A=e}i[r>>2]=o,i[o+24>>2]=A,i[o+12>>2]=o,i[o+8>>2]=o;break A}}while(0);i[12+(l=0|i[(f=A+8|0)>>2])>>2]=o,i[f>>2]=o,i[o+8>>2]=l,i[o+12>>2]=A,i[o+24>>2]=0}else i[6978]=e|r,i[A>>2]=o,i[o+24>>2]=A,i[o+12>>2]=o,i[o+8>>2]=o}while(0);if(i[6985]=l=(0|i[6985])-1|0,!(0|l)){for(A=28364;A=0|i[A>>2];)A=A+8|0;i[6985]=-1}}}}function Ne(A,e){e|=0;var r=0;return(A|=0)?(r=0|m(e,A),(e|A)>>>0>65535&&(r=(0|(r>>>0)/(A>>>0))==(0|e)?r:-1)):r=0,(A=0|Ke(r))&&3&i[A+-4>>2]?(br(0|A,0,0|r),0|A):0|A}function Xe(A,e,r,n){return 0|(C((e|=0)+(n|=0)+((r=(A|=0)+(r|=0)>>>0)>>>0<A>>>0|0)>>>0|0),0|r)}function qe(A,e,r,n){return 0|(C(0|(n=(e|=0)-(n|=0)-((r|=0)>>>0>(A|=0)>>>0|0)>>>0)),A-r>>>0|0)}function $e(A){return 0|((A|=0)?31-(0|h(A^A-1))|0:32)}function Ar(A,e,r,n,t){t|=0;var a=0,f=0,o=0,l=0,u=0,c=0,s=0,b=0,g=0,d=0;if(c=A|=0,f=r|=0,o=b=n|=0,!(u=l=e|=0))return a=0!=(0|t),o?a?(i[t>>2]=0|A,i[t+4>>2]=0&e,t=0,0|(C(0|(b=0)),t)):(t=0,0|(C(0|(b=0)),t)):(a&&(i[t>>2]=(c>>>0)%(f>>>0),i[t+4>>2]=0),t=(c>>>0)/(f>>>0)>>>0,0|(C(0|(b=0)),t));a=0==(0|o);do{if(f){if(!a){if((a=(0|h(0|o))-(0|h(0|u))|0)>>>0<=31){f=s=a+1|0,A=c>>>(s>>>0)&(e=a-31>>31)|u<<(o=31-a|0),e&=u>>>(s>>>0),a=0,o=c<<o;break}return t?(i[t>>2]=0|A,i[t+4>>2]=l|0&e,t=0,0|(C(0|(b=0)),t)):(t=0,0|(C(0|(b=0)),t))}if((a=f-1|0)&f|0){f=o=33+(0|h(0|f))-(0|h(0|u))|0,A=(s=32-o|0)-1>>31&u>>>((g=o-32|0)>>>0)|(u<<s|c>>>(o>>>0))&(e=g>>31),e&=u>>>(o>>>0),a=c<<(d=64-o|0)&(l=s>>31),o=(u<<d|c>>>(g>>>0))&l|c<<s&o-33>>31;break}return 0|t&&(i[t>>2]=a&c,i[t+4>>2]=0),1==(0|f)?(d=0|A,0|(C(0|(g=l|0&e)),d)):(g=u>>>((d=0|$e(0|f))>>>0)|0,d=u<<32-d|c>>>(d>>>0)|0,0|(C(0|g),d))}if(a)return 0|t&&(i[t>>2]=(u>>>0)%(f>>>0),i[t+4>>2]=0),d=(u>>>0)/(f>>>0)>>>0,0|(C(0|(g=0)),d);if(!c)return 0|t&&(i[t>>2]=0,i[t+4>>2]=(u>>>0)%(o>>>0)),d=(u>>>0)/(o>>>0)>>>0,0|(C(0|(g=0)),d);if(!((a=o-1|0)&o))return 0|t&&(i[t>>2]=0|A,i[t+4>>2]=a&u|0&e),g=0,d=u>>>((0|$e(0|o))>>>0),0|(C(0|g),d);if((a=(0|h(0|o))-(0|h(0|u))|0)>>>0<=30){f=e=a+1|0,A=u<<(o=31-a|0)|c>>>(e>>>0),e=u>>>(e>>>0),a=0,o=c<<o;break}return t?(i[t>>2]=0|A,i[t+4>>2]=l|0&e,d=0,0|(C(0|(g=0)),d)):(d=0,0|(C(0|(g=0)),d))}while(0);if(f){u=0|Xe(0|(s=0|r),0|(c=b|0&n),-1,-1),r=0|E(),l=o,o=0;do{n=l,l=a>>>31|l<<1,a=o|a<<1,qe(0|u,0|r,0|(n=A<<1|n>>>31|0),0|(b=A>>>31|e<<1|0)),o=1&(g=(d=0|E())>>31|((0|d)<0?-1:0)<<1),A=0|qe(0|n,0|b,g&s|0,(((0|d)<0?-1:0)>>31|((0|d)<0?-1:0)<<1)&c|0),e=0|E(),f=f-1|0}while(0!=(0|f));u=l,l=0}else u=o,l=0,o=0;return f=0,0|t&&(i[t>>2]=A,i[t+4>>2]=e),d=-2&(a<<1|0)|o,0|(C(0|(g=(0|a)>>>31|(u|f)<<1|0&(f<<1|a>>>31)|l)),d)}function er(A,e,r,n){var i,t,a,f,o;return r|=0,t=(n|=0)>>31|((0|n)<0?-1:0)<<1,i=((0|n)<0?-1:0)>>31|((0|n)<0?-1:0)<<1,a=0|qe((o=(e|=0)>>31|((0|e)<0?-1:0)<<1)^(A|=0)|0,(f=((0|e)<0?-1:0)>>31|((0|e)<0?-1:0)<<1)^e|0,0|o,0|f),A=t^o,e=i^f,0|qe((0|Ar(a,0|E(),0|qe(t^r|0,i^n|0,0|t,0|i),0|E(),0))^A|0,(0|E())^e|0,0|A,0|e)}function rr(A,e,r,n){var i,t;return e|=0,n|=0,r=0|function(A,e){var r,n,i,t=0;return A=((r=0|m(t=65535&(e|=0),i=65535&(A|=0)))>>>16)+(0|m(t,n=A>>>16))|0,e=0|m(t=e>>>16,i),0|(C((A>>>16)+(0|m(t,n))+(((65535&A)+e|0)>>>16)|0),A+e<<16|65535&r|0)}(i=A|=0,t=r|=0),A=0|E(),0|(C((0|m(e,t))+(0|m(n,i))+A|0&A|0),0|r)}function nr(A,e,r,n){var t,a,f,o,l,u;return r|=0,t=T,T=T+16|0,o=0|t,u=(n|=0)>>31|((0|n)<0?-1:0)<<1,l=((0|n)<0?-1:0)>>31|((0|n)<0?-1:0)<<1,Ar(A=0|qe((f=(e|=0)>>31|((0|e)<0?-1:0)<<1)^(A|=0)|0,(a=((0|e)<0?-1:0)>>31|((0|e)<0?-1:0)<<1)^e|0,0|f,0|a),e=0|E(),0|qe(u^r|0,l^n|0,0|u,0|l),0|E(),o),n=0|qe(i[o>>2]^f|0,i[o+4>>2]^a|0,0|f,0|a),r=0|E(),T=t,0|(C(0|r),n)}function ir(A,e,r,n){var t,a;return a=T,T=T+16|0,Ar(A|=0,e|=0,r|=0,n|=0,t=0|a),T=a,0|(C(0|i[t+4>>2]),0|i[t>>2])}function tr(A,e,r){return A|=0,e|=0,(0|(r|=0))<32?(C(e>>r|0),A>>>r|(e&(1<<r)-1)<<32-r):(C(0|((0|e)<0?-1:0)),e>>r-32|0)}function ar(A,e,r){return A|=0,e|=0,(0|(r|=0))<32?(C(e>>>r|0),A>>>r|(e&(1<<r)-1)<<32-r):(C(0),e>>>r-32|0)}function fr(A,e,r){return A|=0,(0|(r|=0))<32?(C((e|=0)<<r|(A&(1<<r)-1<<32-r)>>>32-r|0),A<<r):(C(A<<r-32|0),0)}function or(A,e,r){return A|=0,32==(0|(e=0|h(e|=0)))&&(e=e+(0|h(A))|0),C(0),0|e}function lr(A,e){return e=+e,(A=+A)!=A?+e:e!=e?+A:+Q(+A,+e)}function ur(A,e){return e=+e,(A=+A)!=A?+e:e!=e?+A:+D(+A,+e)}function cr(A){return(A=+A)>=0?+o(A+.5):+k(A-.5)}function sr(A,e,r){A|=0,e|=0;var t,a,f=0;if((0|(r|=0))>=8192)return P(0|A,0|e,0|r),0|A;if(a=0|A,t=A+r|0,(3&A)==(3&e)){for(;3&A;){if(!r)return 0|a;n[A>>0]=0|n[e>>0],A=A+1|0,e=e+1|0,r=r-1|0}for(f=(r=-4&t|0)-64|0;(0|A)<=(0|f);)i[A>>2]=i[e>>2],i[A+4>>2]=i[e+4>>2],i[A+8>>2]=i[e+8>>2],i[A+12>>2]=i[e+12>>2],i[A+16>>2]=i[e+16>>2],i[A+20>>2]=i[e+20>>2],i[A+24>>2]=i[e+24>>2],i[A+28>>2]=i[e+28>>2],i[A+32>>2]=i[e+32>>2],i[A+36>>2]=i[e+36>>2],i[A+40>>2]=i[e+40>>2],i[A+44>>2]=i[e+44>>2],i[A+48>>2]=i[e+48>>2],i[A+52>>2]=i[e+52>>2],i[A+56>>2]=i[e+56>>2],i[A+60>>2]=i[e+60>>2],A=A+64|0,e=e+64|0;for(;(0|A)<(0|r);)i[A>>2]=i[e>>2],A=A+4|0,e=e+4|0}else for(r=t-4|0;(0|A)<(0|r);)n[A>>0]=0|n[e>>0],n[A+1>>0]=0|n[e+1>>0],n[A+2>>0]=0|n[e+2>>0],n[A+3>>0]=0|n[e+3>>0],A=A+4|0,e=e+4|0;for(;(0|A)<(0|t);)n[A>>0]=0|n[e>>0],A=A+1|0,e=e+1|0;return 0|a}function br(A,e,r){e|=0;var t,a=0,f=0,o=0;if(t=(A|=0)+(r|=0)|0,e&=255,(0|r)>=67){for(;3&A;)n[A>>0]=e,A=A+1|0;for(o=e|e<<8|e<<16|e<<24,f=(a=-4&t|0)-64|0;(0|A)<=(0|f);)i[A>>2]=o,i[A+4>>2]=o,i[A+8>>2]=o,i[A+12>>2]=o,i[A+16>>2]=o,i[A+20>>2]=o,i[A+24>>2]=o,i[A+28>>2]=o,i[A+32>>2]=o,i[A+36>>2]=o,i[A+40>>2]=o,i[A+44>>2]=o,i[A+48>>2]=o,i[A+52>>2]=o,i[A+56>>2]=o,i[A+60>>2]=o,A=A+64|0;for(;(0|A)<(0|a);)i[A>>2]=o,A=A+4|0}for(;(0|A)<(0|t);)n[A>>0]=e,A=A+1|0;return t-r|0}function gr(A){return(A=+A)>=0?+o(A+.5):+k(A-.5)}function dr(A){var e,r,n;return A|=0,n=0|M(),(0|A)>0&(0|(e=(r=0|i[f>>2])+A|0))<(0|r)|(0|e)<0?(p(0|e),y(12),-1):(0|e)>(0|n)&&!(0|I(0|e))?(y(12),-1):(i[f>>2]=e,0|r)}return{___divdi3:er,___muldi3:rr,___remdi3:nr,___uremdi3:ir,_areNeighborCells:function(A,e,r,n,t){t|=0;var a,f,o,l=0,u=0,c=0,s=0;if(o=T,T=T+64|0,f=o,a=o+56|0,!(!0&134217728==(2013265920&(e|=0)|0)&!0&134217728==(2013265920&(n|=0)|0)))return T=o,5;if((0|(A|=0))==(0|(r|=0))&(0|e)==(0|n))return i[t>>2]=0,T=o,0;if(u=0|ar(0|A,0|e,52),E(),u&=15,s=0|ar(0|r,0|n,52),E(),(0|u)!=(15&s|0))return T=o,12;if(l=u+-1|0,u>>>0>1){VA(A,e,l,f),VA(r,n,l,a),c=0|i[(s=f)>>2],s=0|i[s+4>>2];A:do{if((0|c)==(0|i[a>>2])&&(0|s)==(0|i[a+4>>2])){l=0|ar(0|A,0|e,0|(u=3*(15^u)|0)),E(),l&=7,u=0|ar(0|r,0|n,0|u),E(),u&=7;do{if(0==(0|l)|0==(0|u))i[t>>2]=1,l=0;else if(7==(0|l))l=5;else{if(1==(0|l)|1==(0|u)&&0|SA(c,s)){l=5;break}if((0|i[15536+(l<<2)>>2])!=(0|u)&&(0|i[15568+(l<<2)>>2])!=(0|u))break A;i[t>>2]=1,l=0}}while(0);return T=o,0|l}}while(0)}u=(l=f)+56|0;do{i[l>>2]=0,l=l+4|0}while((0|l)<(0|u));return F(A,e,1,f),l=(0|i[(e=f)>>2])==(0|r)&&(0|i[e+4>>2])==(0|n)||(0|i[(e=f+8|0)>>2])==(0|r)&&(0|i[e+4>>2])==(0|n)||(0|i[(e=f+16|0)>>2])==(0|r)&&(0|i[e+4>>2])==(0|n)||(0|i[(e=f+24|0)>>2])==(0|r)&&(0|i[e+4>>2])==(0|n)||(0|i[(e=f+32|0)>>2])==(0|r)&&(0|i[e+4>>2])==(0|n)||(0|i[(e=f+40|0)>>2])==(0|r)&&(0|i[e+4>>2])==(0|n)?1:1&((0|i[(l=f+48|0)>>2])==(0|r)?(0|i[l+4>>2])==(0|n):0),i[t>>2]=l,T=o,0},_bitshift64Ashr:tr,_bitshift64Lshr:ar,_bitshift64Shl:fr,_calloc:Ne,_cellAreaKm2:function(A,e,r){return 0|(A=0|ge(A|=0,e|=0,r|=0))?0|A:(t[r>>3]=6371.007180918475*+t[r>>3]*6371.007180918475,0|A)},_cellAreaM2:function(A,e,r){return 0|(A=0|ge(A|=0,e|=0,r|=0))?0|A:(t[r>>3]=6371.007180918475*+t[r>>3]*6371.007180918475*1e3*1e3,0|A)},_cellAreaRads2:ge,_cellToBoundary:re,_cellToCenterChild:WA,_cellToChildPos:function(A,e,r,n){r|=0,n|=0;var t,a=0,f=0,o=0,l=0,u=0,c=0,s=0,b=0,g=0,d=0,w=0,B=0,v=0,k=0;if(t=T,T=T+16|0,v=t,k=0|ar(0|(A|=0),0|(e|=0),52),E(),r>>>0>15)return T=t,4;if((0|(k&=15))<(0|r))return T=t,12;if((0|k)!=(0|r))if(f=0|fr(0|r,0,52),f|=A,l=0|E()|-15728641&e,(0|k)>(0|r)){u=r;do{B=0|fr(7,0,3*(14-u|0)|0),u=u+1|0,f|=B,l=0|E()|l}while((0|u)<(0|k));B=f}else B=f;else B=A,l=e;w=0|ar(0|B,0|l,45),E();A:do{if(0|Y(127&w)){if(u=0|ar(0|B,0|l,52),E(),0|(u&=15))for(f=1;;){if(!(0==((w=0|fr(7,0,3*(15-f|0)|0))&B|0)&0==((0|E())&l|0))){c=33;break A}if(!(f>>>0<u>>>0))break;f=f+1|0}if(i[(w=n)>>2]=0,i[w+4>>2]=0,(0|k)>(0|r)){for(w=-15728641&e,d=k;;){if(g=d,(d=d+-1|0)>>>0>15|(0|k)<(0|d)){c=19;break}if((0|k)!=(0|d))if(f=0|fr(0|d,0,52),f|=A,u=0|E()|w,(0|k)<(0|g))b=f;else{c=d;do{b=0|fr(7,0,3*(14-c|0)|0),c=c+1|0,f|=b,u=0|E()|u}while((0|c)<(0|k));b=f}else b=A,u=e;if(s=0|ar(0|b,0|u,45),E(),0|Y(127&s)){s=0|ar(0|b,0|u,52),E(),s&=15;e:do{if(s)for(c=1;;){if(f=0|ar(0|b,0|u,3*(15-c|0)|0),E(),0|(f&=7))break e;if(!(c>>>0<s>>>0)){f=0;break}c=c+1|0}else f=0}while(0);f=0==(0|f)&1}else f=0;if(u=0|ar(0|A,0|e,3*(15-g|0)|0),E(),7==(0|(u&=7))){a=5,c=42;break}if(1==(0|u)&(f=0!=(0|f))){a=5,c=42;break}if(0|(b=u+((0!=(0|u)&f)<<31>>31)|0)&&(c=0|Qe(7,0,c=k-g|0,((0|c)<0)<<31>>31),s=0|E(),f?(f=0|Xe(0|(f=0|rr(0|c,0|s,5,0)),0|E(),-5,-1),f=0|Xe(0|(f=0|er(0|f,0|E(),6,0)),0|E(),1,0),u=0|E()):(f=c,u=s),g=0|Xe(0|f,0|u,0|(g=0|rr(0|c,0|s,0|(g=b+-1|0),((0|g)<0)<<31>>31|0)),0|E()),s=0|Xe(0|g,0|(b=0|E()),0|i[(s=n)>>2],0|i[s+4>>2]),b=0|E(),i[(g=n)>>2]=s,i[g+4>>2]=b),(0|d)<=(0|r)){c=37;break}}if(19==(0|c))_(27795,27122,1276,27158);else{if(37==(0|c)){a=0|i[(o=n)+4>>2],o=0|i[o>>2];break}if(42==(0|c))return T=t,0|a}}else a=0,o=0}else c=33}while(0);A:do{if(33==(0|c)){if(i[(w=n)>>2]=0,i[w+4>>2]=0,(0|k)>(0|r)){for(f=k;;){if(a=0|ar(0|A,0|e,3*(15-f|0)|0),E(),7==(0|(a&=7))){a=5;break}if(a=0|rr(0|(o=0|Qe(7,0,o=k-f|0,((0|o)<0)<<31>>31)),0|E(),0|a,0),o=0|E(),o=0|Xe(0|i[(w=n)>>2],0|i[w+4>>2],0|a,0|o),a=0|E(),i[(w=n)>>2]=o,i[w+4>>2]=a,(0|(f=f+-1|0))<=(0|r))break A}return T=t,0|a}a=0,o=0}}while(0);return 0|OA(B,l,k,v)&&_(27795,27122,1236,27173),((0|a)>-1|-1==(0|a)&o>>>0>4294967295)&((0|(v=0|i[(k=v)+4>>2]))>(0|a)|((0|v)==(0|a)?(0|i[k>>2])>>>0>o>>>0:0))?(T=t,0|(k=0)):(_(27795,27122,1316,27158),0)},_cellToChildren:function(A,e,r,n){n|=0;var t,a,f=0,o=0;if(a=T,T=T+16|0,ie(t=a,A|=0,e|=0,r|=0),0==(0|(A=0|i[(e=t)>>2]))&0==(0|(e=0|i[e+4>>2])))return T=a,0;f=0,r=0;do{i[(o=n+(f<<3)|0)>>2]=A,i[o+4>>2]=e,f=0|Xe(0|f,0|r,1,0),r=0|E(),ae(t),A=0|i[(o=t)>>2],e=0|i[o+4>>2]}while(!(0==(0|A)&0==(0|e)));return T=a,0},_cellToChildrenSize:OA,_cellToLatLng:ee,_cellToLocalIj:function(A,e,r,n,i,t){var a,f;return A|=0,t|=0,f=T,T=T+16|0,a=f,(i|=0)?A=15:(A=0|me(A,e|=0,r|=0,n|=0,a))||(CA(a,t),A=0),T=f,0|A},_cellToParent:VA,_cellToVertex:Re,_cellToVertexes:function(A,e,r){r|=0;var n,t=0,a=0;return a=0==(0|SA(A|=0,e|=0)),n=0==(0|(t=0|Re(A,e,0,r))),a?n?0|(t=0|Re(A,e,1,r+8|0))?0|(a=t):0|(t=0|Re(A,e,2,r+16|0))?0|(a=t):0|(t=0|Re(A,e,3,r+24|0))?0|(a=t):(t=0|Re(A,e,4,r+32|0))?0|(a=t):0|Re(A,e,5,r+40|0):0|(a=t):n?0|(t=0|Re(A,e,1,r+8|0))?0|(a=t):0|(t=0|Re(A,e,2,r+16|0))?0|(a=t):0|(t=0|Re(A,e,3,r+24|0))?0|(a=t):0|(t=0|Re(A,e,4,r+32|0))?0|(a=t):(i[(a=r+40|0)>>2]=0,i[a+4>>2]=0,0|(a=0)):0|(a=t)},_cellsToDirectedEdge:function(A,e,r,n,t){return t|=0,7==(0|(r=0|x(A|=0,e|=0,r|=0,n|=0)))?0|(t=11):(n=0|fr(0|r,0,56),e=-2130706433&e|0|E()|268435456,i[t>>2]=A|n,i[t+4>>2]=e,0|(t=0))},_cellsToLinkedMultiPolygon:function(A,e,r){r|=0;var n,t,a,f=0;if(a=T,T=T+32|0,n=a,0|(A=0|function(A,e,r){A|=0,r|=0;var n,t,a=0,f=0,o=0,l=0,u=0,c=0;if(t=T,T=T+176|0,n=t,(0|(e|=0))<1)return Ye(r,0,0),T=t,0;for(u=0|ar(0|i[(u=A)>>2],0|i[u+4>>2],52),E(),Ye(r,(0|e)>6?e:6,15&u),u=0;!(0|(a=0|re(0|i[(a=A+(u<<3)|0)>>2],0|i[a+4>>2],n)));){if((0|(a=0|i[n>>2]))>0){l=0;do{o=n+8+(l<<4)|0,(f=0|We(r,a=n+8+(((0|(l=l+1|0))%(0|a)|0)<<4)|0,o))?Se(r,f):He(r,o,a),a=0|i[n>>2]}while((0|l)<(0|a))}if((0|(u=u+1|0))>=(0|e)){a=0,c=13;break}}return 13==(0|c)?(T=t,0|a):(Ve(r),T=t,0|(c=a))}(A|=0,e|=0,t=a+16|0)))return T=a,0|A;if(i[r>>2]=0,i[r+4>>2]=0,i[r+8>>2]=0,0|(A=0|Oe(t)))do{e=0|de(r);do{we(e,A),i[n>>2]=i[(f=A+16|0)>>2],i[n+4>>2]=i[f+4>>2],i[n+8>>2]=i[f+8>>2],i[n+12>>2]=i[f+12>>2],Se(t,A),A=0|Ze(t,n)}while(0!=(0|A));A=0|Oe(t)}while(0!=(0|A));return Ve(t),(A=0|ve(r))?(Be(r),T=a,0|(f=A)):(T=a,0|(f=0))},_childPosToCell:function(A,e,r,n,t,a){A|=0,e|=0,a|=0;var f,o=0,l=0,u=0,c=0,s=0,b=0,g=0,d=0,w=0;if(f=T,T=T+16|0,o=f,(t|=0)>>>0>15)return T=f,4;if(l=0|ar(0|(r|=0),0|(n|=0),52),E(),(0|(l&=15))>(0|t))return T=f,12;if(0|OA(r,n,t,o)&&_(27795,27122,1236,27173),!(((0|e)>-1|-1==(0|e)&A>>>0>4294967295)&((0|(c=0|i[(s=o)+4>>2]))>(0|e)|((0|c)==(0|e)?(0|i[s>>2])>>>0>A>>>0:0))))return T=f,2;s=t-l|0,t=0|fr(0|t,0,52),u=0|E()|-15728641&n,i[(c=a)>>2]=t|r,i[c+4>>2]=u,c=0|ar(0|r,0|n,45),E();A:do{if(0|Y(127&c)){if(0|l)for(o=1;;){if(!(0==((c=0|fr(7,0,3*(15-o|0)|0))&r|0)&0==((0|E())&n|0)))break A;if(!(o>>>0<l>>>0))break;o=o+1|0}if((0|s)<1)return T=f,0;for(c=15^l,n=-1,u=1,o=1;;){l=0|Qe(7,0,l=s-u|0,((0|l)<0)<<31>>31),r=0|E();do{if(o){if(o=0|er(0|(o=0|Xe(0|(o=0|rr(0|l,0|r,5,0)),0|E(),-5,-1)),0|E(),6,0),(0|e)>(0|(t=0|E()))|(0|e)==(0|t)&A>>>0>o>>>0){e=0|qe(0|(e=0|Xe(0|A,0|e,-1,-1)),0|E(),0|o,0|t),o=0|E(),d=0|i[(b=a)>>2],b=0|i[b+4>>2],g=0|fr(7,0,0|(w=3*(c+n|0)|0)),b&=~(0|E()),w=0|fr(0|(t=0|Xe(0|(n=0|er(0|e,0|o,0|l,0|r)),0|(A=0|E()),2,0)),0|E(),0|w),b=0|E()|b,i[(t=a)>>2]=w|d&~g,i[t+4>>2]=b,A=0|qe(0|e,0|o,0|(A=0|rr(0|n,0|A,0|l,0|r)),0|E()),o=0,e=0|E();break}g=0|i[(w=a)>>2],w=0|i[w+4>>2],d=0|fr(7,0,3*(c+n|0)|0),w&=~(0|E()),i[(o=a)>>2]=g&~d,i[o+4>>2]=w,o=1;break}t=0|i[(g=a)>>2],g=0|i[g+4>>2],b=0|fr(7,0,0|(n=3*(c+n|0)|0)),g&=~(0|E()),n=0|fr(0|(w=0|er(0|A,0|e,0|l,0|r)),0|(o=0|E()),0|n),g=0|E()|g,i[(d=a)>>2]=n|t&~b,i[d+4>>2]=g,A=0|qe(0|A,0|e,0|(o=0|rr(0|w,0|o,0|l,0|r)),0|E()),o=0,e=0|E()}while(0);if(!((0|s)>(0|u))){e=0;break}n=~u,u=u+1|0}return T=f,0|e}}while(0);if((0|s)<1)return T=f,0;for(t=15^l,o=1;;){if(d=0|Qe(7,0,d=s-o|0,((0|d)<0)<<31>>31),w=0|E(),r=0|i[(u=a)>>2],u=0|i[u+4>>2],n=0|fr(7,0,0|(l=3*(t-o|0)|0)),u&=~(0|E()),l=0|fr(0|(b=0|er(0|A,0|e,0|d,0|w)),0|(g=0|E()),0|l),u=0|E()|u,i[(c=a)>>2]=l|r&~n,i[c+4>>2]=u,A=0|qe(0|A,0|e,0|(w=0|rr(0|b,0|g,0|d,0|w)),0|E()),e=0|E(),(0|s)<=(0|o)){e=0;break}o=o+1|0}return T=f,0|e},_compactCells:function(A,e,r,n){e|=0;var t,a=0,f=0,o=0,l=0,u=0,c=0,s=0,b=0,g=0,d=0,w=0,B=0,v=0,k=0,m=0,D=0,Q=0,h=0,C=0,y=0,M=0,P=0,I=0,p=0,T=0;if(0==(0|(r|=0))&0==(0|(n|=0)))return 0;if(f=0|i[(a=A|=0)>>2],!0&0==(15728640&(a=0|i[a+4>>2])|0)){if(!((0|n)>0|0==(0|n)&r>>>0>0))return 0;if(i[(T=e)>>2]=f,i[T+4>>2]=a,1==(0|r)&0==(0|n))return 0;a=1,f=0;do{p=0|i[4+(I=A+(a<<3)|0)>>2],i[(T=e+(a<<3)|0)>>2]=i[I>>2],i[T+4>>2]=p,a=0|Xe(0|a,0|f,1,0),f=0|E()}while((0|f)<(0|n)|(0|f)==(0|n)&a>>>0<r>>>0);return 0}if(!(p=0|Ke(t=r<<3)))return 13;if(sr(0|p,0|A,0|t),!(I=0|Ne(r,8)))return je(p),13;A:for(;;){M=0|ar(0|(c=0|i[(a=p)>>2]),0|(a=0|i[a+4>>2]),52),E(),P=(M&=15)-1|0,y=0!=(0|M),C=(0|n)>0|0==(0|n)&r>>>0>0;e:do{if(y&C){if(m=0|fr(0|P,0,52),D=0|E(),P>>>0>15){if(!(0==(0|c)&0==(0|a))){T=16;break A}for(f=0,A=0;;){if(f=0|Xe(0|f,0|A,1,0),!((0|(A=0|E()))<(0|n)|(0|A)==(0|n)&f>>>0<r>>>0))break e;if(!(0==(0|(h=0|i[(o=p+(f<<3)|0)>>2]))&0==(0|(o=0|i[o+4>>2])))){a=o,T=16;break A}}}for(l=c,A=a,f=0,o=0;;){if(!(0==(0|l)&0==(0|A))){if(!(!0&0==(117440512&A|0))){T=21;break A}if(s=0|ar(0|l,0|A,52),E(),(0|(s&=15))<(0|P)){a=12,T=27;break A}if((0|s)!=(0|P)&&(l|=m,A=-15728641&A|D,s>>>0>=M>>>0)){u=P;do{h=0|fr(7,0,3*(14-u|0)|0),u=u+1|0,l|=h,A=0|E()|A}while(u>>>0<s>>>0)}if(g=0|ir(0|l,0|A,0|r,0|n),d=0|E(),!(0==(0|(b=0|i[(s=u=I+(g<<3)|0)>>2]))&0==(0|(s=0|i[s+4>>2])))){v=0,k=0;do{if((0|v)>(0|n)|(0|v)==(0|n)&k>>>0>r>>>0){T=31;break A}if((0|b)==(0|l)&(-117440513&s|0)==(0|A)){w=0|ar(0|b,0|s,56),E(),B=1+(w&=7)|0,h=0|ar(0|b,0|s,45),E();r:do{if(0|Y(127&h)){if(b=0|ar(0|b,0|s,52),E(),!(b&=15)){s=6;break}for(s=1;;){if(!(0==((h=0|fr(7,0,3*(15-s|0)|0))&l|0)&0==((0|E())&A|0))){s=7;break r}if(!(s>>>0<b>>>0)){s=6;break}s=s+1|0}}else s=7}while(0);if((w+2|0)>>>0>s>>>0){T=41;break A}h=0|fr(0|B,0,56),A=0|E()|-117440513&A,i[(Q=u)>>2]=0,i[Q+4>>2]=0,l|=h}else g=0|nr(0|(g=0|Xe(0|g,0|d,1,0)),0|E(),0|r,0|n),d=0|E();k=0|Xe(0|k,0|v,1,0),v=0|E(),b=0|i[(s=u=I+(g<<3)|0)>>2],s=0|i[s+4>>2]}while(!(0==(0|b)&0==(0|s)))}i[(h=u)>>2]=l,i[h+4>>2]=A}if(f=0|Xe(0|f,0|o,1,0),!((0|(o=0|E()))<(0|n)|(0|o)==(0|n)&f>>>0<r>>>0))break e;l=0|i[(A=p+(f<<3)|0)>>2],A=0|i[A+4>>2]}}}while(0);if(h=0|Xe(0|r,0|n,5,0),(Q=0|E())>>>0<0|0==(0|Q)&h>>>0<11){T=85;break}if(h=0|er(0|r,0|n,6,0),E(),!(h=0|Ne(h,8))){T=48;break}do{if(C){for(B=0,A=0,w=0,v=0;;){if(0==(0|(f=0|i[(o=s=I+(B<<3)|0)>>2]))&0==(0|(o=0|i[o+4>>2])))Q=w;else{b=0|ar(0|f,0|o,56),E(),l=1+(b&=7)|0,g=-117440513&o,Q=0|ar(0|f,0|o,45),E();e:do{if(0|Y(127&Q)){if(d=0|ar(0|f,0|o,52),E(),0|(d&=15))for(u=1;;){if(!(0==(f&(Q=0|fr(7,0,3*(15-u|0)|0))|0)&0==(g&(0|E())|0)))break e;if(!(u>>>0<d>>>0))break;u=u+1|0}f|=o=0|fr(0|l,0,56),o=0|E()|g,i[(l=s)>>2]=f,i[l+4>>2]=o,l=b+2|0}}while(0);7==(0|l)?(i[(Q=h+(A<<3)|0)>>2]=f,i[Q+4>>2]=-117440513&o,A=0|Xe(0|A,0|w,1,0),Q=0|E()):Q=w}if(B=0|Xe(0|B,0|v,1,0),!((0|(v=0|E()))<(0|n)|(0|v)==(0|n)&B>>>0<r>>>0))break;w=Q}if(C){if(k=P>>>0>15,m=0|fr(0|P,0,52),D=0|E(),!y){for(f=0,u=0,l=0,o=0;0==(0|c)&0==(0|a)||(i[(P=e+(f<<3)|0)>>2]=c,i[P+4>>2]=a,f=0|Xe(0|f,0|u,1,0),u=0|E()),l=0|Xe(0|l,0|o,1,0),(0|(o=0|E()))<(0|n)|(0|o)==(0|n)&l>>>0<r>>>0;)c=0|i[(a=p+(l<<3)|0)>>2],a=0|i[a+4>>2];a=Q;break}for(f=0,u=0,o=0,l=0;;){do{if(!(0==(0|c)&0==(0|a))){if(d=0|ar(0|c,0|a,52),E(),k|(0|(d&=15))<(0|P)){T=80;break A}if((0|d)!=(0|P)){if(s=c|m,b=-15728641&a|D,d>>>0>=M>>>0){g=P;do{y=0|fr(7,0,3*(14-g|0)|0),g=g+1|0,s|=y,b=0|E()|b}while(g>>>0<d>>>0)}}else s=c,b=a;w=0|ir(0|s,0|b,0|r,0|n),g=0,d=0,v=0|E();do{if((0|g)>(0|n)|(0|g)==(0|n)&d>>>0>r>>>0){T=81;break A}if((-117440513&(B=0|i[4+(y=I+(w<<3)|0)>>2])|0)==(0|b)&&(0|i[y>>2])==(0|s)){T=65;break}w=0|nr(0|(y=0|Xe(0|w,0|v,1,0)),0|E(),0|r,0|n),v=0|E(),d=0|Xe(0|d,0|g,1,0),g=0|E(),y=I+(w<<3)|0}while((0|i[y>>2])!=(0|s)||(0|i[y+4>>2])!=(0|b));if(65==(0|T)&&(T=0,!0&100663296==(117440512&B|0)))break;i[(y=e+(f<<3)|0)>>2]=c,i[y+4>>2]=a,f=0|Xe(0|f,0|u,1,0),u=0|E()}}while(0);if(o=0|Xe(0|o,0|l,1,0),!((0|(l=0|E()))<(0|n)|(0|l)==(0|n)&o>>>0<r>>>0))break;c=0|i[(a=p+(o<<3)|0)>>2],a=0|i[a+4>>2]}a=Q}else f=0,a=Q}else f=0,A=0,a=0}while(0);if(br(0|I,0,0|t),sr(0|p,0|h,A<<3|0),je(h),0==(0|A)&0==(0|a)){T=89;break}e=e+(f<<3)|0,n=a,r=A}if(16==(0|T))!0&0==(117440512&a|0)?(a=4,T=27):T=21;else if(31==(0|T))_(27795,27122,529,27132);else{if(41==(0|T))return je(p),je(I),10;if(48==(0|T))return je(p),je(I),13;80==(0|T)?_(27795,27122,620,27132):81==(0|T)?_(27795,27122,632,27132):85==(0|T)&&(sr(0|e,0|p,r<<3|0),T=89)}return 21==(0|T)?(je(p),je(I),0|(T=5)):27==(0|T)?(je(p),je(I),0|(T=a)):89==(0|T)?(je(p),je(I),0|(T=0)):0},_destroyLinkedMultiPolygon:Be,_directedEdgeToBoundary:MA,_directedEdgeToCells:function(A,e,r){A|=0;var n,t,a,f=0;return n=T,T=T+16|0,f=n,!0&268435456==(2013265920&(e|=0)|0)?(t=-2130706433&e|134217728,i[(a=r|=0)>>2]=A,i[a+4>>2]=t,i[f>>2]=0,e=0|ar(0|A,0|e,56),E(),f=0|U(A,t,7&e,f,r+8|0),T=n,0|f):(T=n,0|(f=6))},_edgeLengthKm:function(A,e,r){r|=0;var n,a=0,f=0,o=0,l=0,c=0,g=0,d=0;if(n=T,T=T+176|0,0|(A=0|MA(A|=0,e|=0,l=n)))return l=A,o=+t[r>>3],t[r>>3]=o*=6371.007180918475,T=n,0|l;if(t[r>>3]=0,(0|(A=0|i[l>>2]))<=1)return l=0,o=0,t[r>>3]=o*=6371.007180918475,T=n,0|l;e=A+-1|0,A=0,a=+t[l+8>>3],f=+t[l+16>>3],o=0;do{g=a,c=f,c=(d=+b(.5*((a=+t[l+8+((A=A+1|0)<<4)>>3])-g)))*d+(c=+b(.5*((f=+t[l+8+(A<<4)+8>>3])-c)))*(+s(+g)*+s(+a)*c),o+=2*+v(+ +u(+c),+ +u(+(1-c)))}while((0|A)!=(0|e));return t[r>>3]=o,l=0,d=o,t[r>>3]=d*=6371.007180918475,T=n,0|l},_edgeLengthM:function(A,e,r){r|=0;var n,a=0,f=0,o=0,l=0,c=0,g=0,d=0;if(n=T,T=T+176|0,0|(A=0|MA(A|=0,e|=0,l=n)))return l=A,o=+t[r>>3],o*=6371.007180918475,t[r>>3]=o*=1e3,T=n,0|l;if(t[r>>3]=0,(0|(A=0|i[l>>2]))<=1)return l=0,o=0,o*=6371.007180918475,t[r>>3]=o*=1e3,T=n,0|l;e=A+-1|0,A=0,a=+t[l+8>>3],f=+t[l+16>>3],o=0;do{g=a,c=f,c=(d=+b(.5*((a=+t[l+8+((A=A+1|0)<<4)>>3])-g)))*d+(c=+b(.5*((f=+t[l+8+(A<<4)+8>>3])-c)))*(+s(+g)*+s(+a)*c),o+=2*+v(+ +u(+c),+ +u(+(1-c)))}while((0|A)!=(0|e));return t[r>>3]=o,l=0,d=o,d*=6371.007180918475,t[r>>3]=d*=1e3,T=n,0|l},_edgeLengthRads:function(A,e,r){r|=0;var n,a,f=0,o=0,l=0,c=0,g=0,d=0;if(n=T,T=T+176|0,0|(A=0|MA(A|=0,e|=0,a=n)))return T=n,0|A;if(t[r>>3]=0,(0|(A=0|i[a>>2]))<=1)return T=n,0;e=A+-1|0,A=0,f=+t[a+8>>3],o=+t[a+16>>3],l=0;do{g=f,c=o,c=(d=+b(.5*((f=+t[a+8+((A=A+1|0)<<4)>>3])-g)))*d+(c=+b(.5*((o=+t[a+8+(A<<4)+8>>3])-c)))*(+s(+f)*+s(+g)*c),l+=2*+v(+ +u(+c),+ +u(+(1-c)))}while((0|A)<(0|e));return t[r>>3]=l,T=n,0},_emscripten_replace_memory:function(A){return n=new Int8Array(A),new Uint8Array(A),i=new Int32Array(A),new Float32Array(A),t=new Float64Array(A),r=A,!0},_free:je,_getBaseCellNumber:xA,_getDirectedEdgeDestination:function(A,e,r){A|=0,e|=0,r|=0;var n,t,a=0;return n=T,T=T+16|0,i[(a=n)>>2]=0,!0&268435456==(2013265920&e|0)?(t=0|ar(0|A,0|e,56),E(),a=0|U(A,-2130706433&e|134217728,7&t,a,r),T=n,0|a):(T=n,0|(a=6))},_getDirectedEdgeOrigin:function(A,e,r){return r|=0,!0&268435456==(2013265920&(e|=0)|0)?(i[r>>2]=A|=0,i[r+4>>2]=-2130706433&e|134217728,0|(r=0)):0|(r=6)},_getHexagonAreaAvgKm2:ce,_getHexagonAreaAvgM2:function(A,e){return e|=0,(A|=0)>>>0>15?0|(e=4):(t[e>>3]=+t[20784+(A<<3)>>3],0|(e=0))},_getHexagonEdgeLengthAvgKm:function(A,e){return e|=0,(A|=0)>>>0>15?0|(e=4):(t[e>>3]=+t[20912+(A<<3)>>3],0|(e=0))},_getHexagonEdgeLengthAvgM:function(A,e){return e|=0,(A|=0)>>>0>15?0|(e=4):(t[e>>3]=+t[21040+(A<<3)>>3],0|(e=0))},_getIcosahedronFaces:function A(e,r,n){n|=0;var t,a=0,f=0,o=0,l=0,u=0,c=0,s=0,b=0;t=T,T=T+128|0,s=t+112|0,o=t+96|0,b=t,f=0|ar(0|(e|=0),0|(r|=0),52),E(),i[s>>2]=u=15&f,l=0|ar(0|e,0|r,45),E(),l&=127;A:do{if(0|Y(l)){if(0|u)for(a=1;;){if(!(0==((c=0|fr(7,0,3*(15-a|0)|0))&e|0)&0==((0|E())&r|0))){f=0;break A}if(!(a>>>0<u>>>0))break;a=a+1|0}if(!(1&f))return c=0|fr(u+1|0,0,52),b=0|E()|-15728641&r,b=0|A((c|e)&~(s=0|fr(7,0,3*(14-u|0)|0)),b&~(0|E()),n),T=t,0|b;f=1}else f=0}while(0);if(!(a=0|Ae(e,r,o))){f?(FA(o,s,b),c=5):(UA(o,s,b),c=6);A:do{if(0|Y(l))if(u)for(a=1;;){if(!(0==((l=0|fr(7,0,3*(15-a|0)|0))&e|0)&0==((0|E())&r|0))){e=2;break A}if(!(a>>>0<u>>>0)){e=5;break}a=a+1|0}else e=5;else e=2}while(0);br(0|n,-1,e<<2|0);A:do{if(f)for(o=0;;){if(GA(l=b+(o<<4)|0,0|i[s>>2]),-1==(0|(u=0|i[n>>2]))|(0|u)==(0|(l=0|i[l>>2])))a=n;else{f=0;do{if((f=f+1|0)>>>0>=e>>>0){a=1;break A}u=0|i[(a=n+(f<<2)|0)>>2]}while(!(-1==(0|u)|(0|u)==(0|l)))}if(i[a>>2]=l,(o=o+1|0)>>>0>=c>>>0){a=0;break}}else for(o=0;;){if(LA(l=b+(o<<4)|0,0|i[s>>2],0,1),-1==(0|(u=0|i[n>>2]))|(0|u)==(0|(l=0|i[l>>2])))a=n;else{f=0;do{if((f=f+1|0)>>>0>=e>>>0){a=1;break A}u=0|i[(a=n+(f<<2)|0)>>2]}while(!(-1==(0|u)|(0|u)==(0|l)))}if(i[a>>2]=l,(o=o+1|0)>>>0>=c>>>0){a=0;break}}}while(0)}return T=t,0|a},_getNumCells:se,_getPentagons:ne,_getRes0Cells:function(A){A|=0;var e=0,r=0,n=0;e=0;do{fr(0|e,0,45),n=134225919|E(),i[(r=A+(e<<3)|0)>>2]=-1,i[r+4>>2]=n,e=e+1|0}while(122!=(0|e));return 0},_getResolution:function(A,e){return e=0|ar(0|(A|=0),0|(e|=0),52),E(),15&e|0},_greatCircleDistanceKm:ue,_greatCircleDistanceM:function(A,e){var r,n,i,a=0;return a=(i=+b(.5*((n=+t[(e|=0)>>3])-(r=+t[(A|=0)>>3]))))*i+(a=+b(.5*(+t[e+8>>3]-+t[A+8>>3])))*(+s(+n)*+s(+r)*a),2*+v(+ +u(+a),+ +u(+(1-a)))*6371.007180918475*1e3},_greatCircleDistanceRads:function(A,e){var r,n,i,a=0;return a=(i=+b(.5*((n=+t[(e|=0)>>3])-(r=+t[(A|=0)>>3]))))*i+(a=+b(.5*(+t[e+8>>3]-+t[A+8>>3])))*(+s(+n)*+s(+r)*a),2*+v(+ +u(+a),+ +u(+(1-a)))},_gridDisk:F,_gridDiskDistances:L,_gridDistance:function(A,e,r,n,t){r|=0,n|=0,t|=0;var a,f,o=0,l=0;return f=T,T=T+32|0,l=f,0|(a=0|me(A|=0,e|=0,A,e,o=f+12|0))?(T=f,0|(l=a)):0|(A=0|me(A,e,r,n,l))?(T=f,0|(l=A)):(o=0|hA(o,l),i[(l=t)>>2]=o,i[l+4>>2]=((0|o)<0)<<31>>31,T=f,0|(l=0))},_gridPathCells:function(A,e,r,n,t){r|=0,n|=0,t|=0;var a,f,o,u=0,c=0,s=0,b=0,g=0,d=0,w=0,B=0,v=0,k=0,m=0,D=0,Q=0,h=0,C=0,y=0,M=0;if(f=T,T=T+48|0,c=f+12|0,o=f,!(u=0|me(A|=0,e|=0,A,e,a=f+24|0))&&!(u=0|me(A,e,r,n,c))){y=((0|(C=0|hA(a,c)))<0)<<31>>31,i[a>>2]=0,i[a+4>>2]=0,i[a+8>>2]=0,i[c>>2]=0,i[c+4>>2]=0,i[c+8>>2]=0,0|me(A,e,A,e,a)&&_(27795,27538,692,27747),0|me(A,e,r,n,c)&&_(27795,27538,697,27747),_A(a),_A(c),m=(d=0==(0|C)?0:1/+(0|C))*+((0|i[c>>2])-(r=0|i[a>>2])|0),Q=d*+((0|i[c+4>>2])-(n=0|i[(D=a+4|0)>>2])|0),d*=+((0|i[c+8>>2])-(u=0|i[(h=a+8|0)>>2])|0),i[o>>2]=r,i[(w=o+4|0)>>2]=n,i[(B=o+8|0)>>2]=u;A:do{if((0|C)<0)u=0;else for(v=0,k=0;;){M=m*(b=+(k>>>0)+4294967296*+(0|v))+ +(0|r),s=Q*b+ +(0|n),b=d*b+ +(0|u),r=~~+cr(+M),c=~~+cr(+s),u=~~+cr(+b),M=+l(+(+(0|r)-M)),s=+l(+(+(0|c)-s)),b=+l(+(+(0|u)-b));do{if(!(M>s&M>b)){if(g=0-r|0,s>b){n=g-u|0;break}n=c,u=g-c|0;break}r=0-(c+u)|0,n=c}while(0);if(i[o>>2]=r,i[w>>2]=n,i[B>>2]=u,yA(o),0|(u=0|De(A,e,o,t+(k<<3)|0)))break A;if(!((0|v)<(0|y)|(0|v)==(0|y)&k>>>0<C>>>0)){u=0;break A}r=0|Xe(0|k,0|v,1,0),v=n=0|E(),k=r,r=0|i[a>>2],n=0|i[D>>2],u=0|i[h>>2]}}while(0);return T=f,0|u}return T=f,0|u},_gridPathCellsSize:function(A,e,r,n,t){r|=0,n|=0,t|=0;var a,f=0,o=0,l=0;return a=T,T=T+32|0,l=a,(f=0|me(A|=0,e|=0,A,e,o=a+12|0))||(f=0|me(A,e,r,n,l))?(T=a,0|(l=f)):(n=0|Xe(0|(n=0|hA(o,l)),((0|n)<0)<<31>>31|0,1,0),o=0|E(),i[(l=t)>>2]=n,i[l+4>>2]=o,T=a,0|(l=0))},_gridRingUnsafe:function(A,e,r,n){e|=0,r|=0,n|=0;var t,a,f=0,o=0,l=0,u=0,c=0,s=0,b=0,g=0,d=0;if(a=T,T=T+16|0,d=a+8|0,i[(g=t=a)>>2]=A|=0,i[g+4>>2]=e,!r)return i[(d=n)>>2]=A,i[d+4>>2]=e,T=a,0;i[d>>2]=0;A:do{if(0|SA(A,e))A=9;else{if(o=(0|r)>0){f=0,g=A;do{if(0|(A=0|U(g,e,4,d,t)))break A;if(f=f+1|0,0|SA(g=0|i[(e=t)>>2],e=0|i[e+4>>2])){A=9;break A}}while((0|f)<(0|r));if(i[(b=n)>>2]=g,i[b+4>>2]=e,b=r+-1|0,o){s=0,A=1;do{if(f=26800+(s<<2)|0,5==(0|s))for(l=0|i[f>>2],o=0,f=A;;){if(0|(A=0|U(0|i[(A=t)>>2],0|i[A+4>>2],l,d,t)))break A;if((0|o)!=(0|b)){if(u=0|i[(c=t)>>2],c=0|i[c+4>>2],i[(A=n+(f<<3)|0)>>2]=u,i[A+4>>2]=c,0|SA(u,c)){A=9;break A}A=f+1|0}else A=f;if((0|(o=o+1|0))>=(0|r))break;f=A}else for(c=0|i[f>>2],u=0,f=A,o=0|i[(l=t)>>2],l=0|i[l+4>>2];;){if(0|(A=0|U(o,l,c,d,t)))break A;if(o=0|i[(l=t)>>2],l=0|i[l+4>>2],i[(A=n+(f<<3)|0)>>2]=o,i[A+4>>2]=l,A=f+1|0,0|SA(o,l)){A=9;break A}if((0|(u=u+1|0))>=(0|r))break;f=A}s=s+1|0}while(s>>>0<6);l=g,f=0|i[(A=t)>>2],o=e,A=0|i[A+4>>2]}else l=g,f=g,o=e,A=e}else i[(l=n)>>2]=A,i[l+4>>2]=e,l=A,f=A,o=e,A=e;A=(0|l)==(0|f)&(0|o)==(0|A)?0:9}}while(0);return T=a,0|A},_i64Add:Xe,_i64Subtract:qe,_isPentagon:SA,_isResClassIII:function(A,e){return e=0|ar(0|(A|=0),0|(e|=0),52),E(),1&e|0},_isValidCell:RA,_isValidDirectedEdge:function(A,e){var r=0;switch(r=0|ar(0|(A|=0),0|(e|=0),56),E(),7&r){case 0:case 7:return 0}return r=-2130706433&e|134217728,!0&268435456==(2013265920&e|0)?!0&16777216==(117440512&e|0)&0!=(0|SA(A,r))?0|(r=0):0|(r=0|RA(A,r)):0|(r=0)},_isValidVertex:function(A,e){A|=0;var r,n,t=0,a=0;return n=T,T=T+16|0,r=n,!0&536870912==(2013265920&(e|=0)|0)&&0|RA(A,t=-2130706433&e|134217728)?(a=0|ar(0|A,0|e,56),E(),a=0==(0|Re(A,t,7&a,r)),T=n,0|(t=a&((0|i[(t=r)>>2])==(0|A)?(0|i[t+4>>2])==(0|e):0)&1)):(T=n,0|(t=0))},_latLngToCell:qA,_llvm_ctlz_i64:or,_llvm_maxnum_f64:lr,_llvm_minnum_f64:ur,_llvm_round_f64:cr,_localIjToCell:function(A,e,r,n,i){var t,a;return A|=0,e|=0,r|=0,i|=0,a=T,T=T+16|0,t=a,(n|=0)?r=15:(r=0|EA(r,t))||(r=0|De(A,e,t,i)),T=a,0|r},_malloc:Ke,_maxFaceCount:function(A,e,r){r|=0;var n=0,t=0;if(t=0|ar(0|(A|=0),0|(e|=0),45),E(),!(0|Y(127&t)))return i[r>>2]=t=2,0;if(t=0|ar(0|A,0|e,52),E(),!(t&=15))return i[r>>2]=t=5,0;for(n=1;;){if(!(0==((0|fr(7,0,3*(15-n|0)|0))&A|0)&0==((0|E())&e|0))){n=2,A=6;break}if(!(n>>>0<t>>>0)){n=5,A=6;break}n=n+1|0}return 6==(0|A)?(i[r>>2]=n,0):0},_maxGridDiskSize:function(A,e){e|=0;var r=0,n=0,t=0;return(0|(A|=0))<0?0|(e=2):(0|A)>13780509?0|(e=0|se(15,e)):(t=0|rr(0|A,0|(r=((0|A)<0)<<31>>31),3,0),n=0|E(),r=0|Xe(0|A,0|r,1,0),r=0|Xe(0|(r=0|rr(0|t,0|n,0|r,0|E())),0|E(),1,0),A=0|E(),i[e>>2]=r,i[e+4>>2]=A,0|(e=0))},_maxPolygonToCellsSize:function(A,e,r,n){A|=0,e|=0,n|=0;var t,a,f=0,o=0,l=0,u=0;if(t=T,T=T+48|0,f=t+16|0,o=t+8|0,a=t,0|(r=0|Pe(r|=0)))return T=t,0|r;if(l=0|i[(u=A)+4>>2],i[(r=o)>>2]=i[u>>2],i[r+4>>2]=l,Me(o,f),!(r=0|AA(f,e,a))){if(e=0|i[o>>2],(0|(o=0|i[A+8>>2]))>0){f=0|i[A+12>>2],r=0;do{e=(0|i[f+(r<<3)>>2])+e|0,r=r+1|0}while((0|r)<(0|o))}f=0|i[(r=a)>>2],(0|(r=0|i[r+4>>2]))<(0|(o=((0|e)<0)<<31>>31))|(0|r)==(0|o)&f>>>0<e>>>0?(i[(r=a)>>2]=e,i[r+4>>2]=o,r=o):e=f,l=0|Xe(0|e,0|r,12,0),u=0|E(),i[(r=a)>>2]=l,i[r+4>>2]=u,i[(r=n)>>2]=l,i[r+4>>2]=u,r=0}return T=t,0|r},_maxPolygonToCellsSizeExperimental:function(A,e,r,a){e|=0,r|=0,a|=0;var f,o,u=0,c=0,b=0,g=0,d=0,w=0,B=0,v=0;if(o=T,T=T+48|0,d=o+32|0,g=o+40|0,f=o,!(0|i[(A|=0)>>2]))return i[(w=a)>>2]=0,i[w+4>>2]=0,T=o,0;YA(d,0,0,0),u=0|i[(b=d)>>2],b=0|i[b+4>>2];do{if(e>>>0>15)i[(w=f)>>2]=0,i[w+4>>2]=0,i[f+8>>2]=4,i[f+12>>2]=-1,r=f+29|0,i[(w=f+16|0)>>2]=0,i[w+4>>2]=0,i[w+8>>2]=0,n[w+12>>0]=0,n[r>>0]=0|n[g>>0],n[r+1>>0]=0|n[g+1>>0],n[r+2>>0]=0|n[g+2>>0],r=4,w=9;else{if(0|(r=0|Pe(r))){i[(d=f)>>2]=0,i[d+4>>2]=0,i[f+8>>2]=r,i[f+12>>2]=-1,w=f+29|0,i[(d=f+16|0)>>2]=0,i[d+4>>2]=0,i[d+8>>2]=0,n[d+12>>0]=0,n[w>>0]=0|n[g>>0],n[w+1>>0]=0|n[g+1>>0],n[w+2>>0]=0|n[g+2>>0],w=9;break}if(!(r=0|Ne(1+(0|i[A+8>>2])|0,32))){i[(w=f)>>2]=0,i[w+4>>2]=0,i[f+8>>2]=13,i[f+12>>2]=-1,r=f+29|0,i[(w=f+16|0)>>2]=0,i[w+4>>2]=0,i[w+8>>2]=0,n[w+12>>0]=0,n[r>>0]=0|n[g>>0],n[r+1>>0]=0|n[g+1>>0],n[r+2>>0]=0|n[g+2>>0],r=13,w=9;break}Ie(A,r),i[(v=f)>>2]=u,i[v+4>>2]=b,i[(b=f+8|0)>>2]=0,i[f+12>>2]=e,i[f+20>>2]=A,i[f+24>>2]=r,n[f+28>>0]=0,n[(u=f+29|0)>>0]=0|n[g>>0],n[u+1>>0]=0|n[g+1>>0],n[u+2>>0]=0|n[g+2>>0],i[f+16>>2]=3,B=+j(r),B*=+J(r),c=+l(+ +t[r>>3]),c=B/+s(+ +ur(+c,+ +l(+ +t[r+8>>3])))*6371.007180918475*6371.007180918475,r=0|i[(u=f+12|0)>>2];A:do{if((0|r)>0)do{if(ce(r+-1|0,d),!(c/+t[d>>3]>10))break A;i[u>>2]=r=(v=0|i[u>>2])-1|0}while((0|v)>1)}while(0);if(Ee(f),i[(u=a)>>2]=0,i[u+4>>2]=0,!(0==(0|(r=0|i[(u=f)>>2]))&0==(0|(u=0|i[u+4>>2]))))do{OA(r,u,e,d),g=0|Xe(0|i[(A=a)>>2],0|i[A+4>>2],0|i[(g=d)>>2],0|i[g+4>>2]),A=0|E(),i[(v=a)>>2]=g,i[v+4>>2]=A,Ee(f),r=0|i[(v=f)>>2],u=0|i[v+4>>2]}while(!(0==(0|r)&0==(0|u)));r=0|i[b>>2]}}while(0);return T=o,0|r},_memcpy:sr,_memset:br,_originToDirectedEdges:function(A,e,r){r|=0;var n,t=0;return n=0==(0|SA(A|=0,e|=0)),e&=-2130706433,i[(t=r)>>2]=n?A:0,i[t+4>>2]=n?285212672|e:0,i[(t=r+8|0)>>2]=A,i[t+4>>2]=301989888|e,i[(t=r+16|0)>>2]=A,i[t+4>>2]=318767104|e,i[(t=r+24|0)>>2]=A,i[t+4>>2]=335544320|e,i[(t=r+32|0)>>2]=A,i[t+4>>2]=352321536|e,i[(r=r+40|0)>>2]=A,i[r+4>>2]=369098752|e,0},_pentagonCount:function(){return 12},_polygonToCells:function(A,e,r,n){A|=0,e|=0,n|=0;var t,a,f,o,l,u=0,c=0,s=0,b=0,g=0,d=0,w=0,B=0,v=0,k=0,m=0,D=0,Q=0,h=0,C=0,_=0,y=0,M=0,P=0,I=0,p=0,F=0,L=0,U=0,x=0,Y=0;if(o=T,T=T+112|0,t=o+80|0,g=o+72|0,a=o,f=o+56|0,0|(u=0|Pe(r|=0)))return T=o,0|u;if(!(l=0|Ke(32+(i[(d=A+8|0)>>2]<<5)|0)))return T=o,13;if(Ie(A,l),!(u=0|Pe(r))){if(Y=0|i[(x=A)+4>>2],i[(u=g)>>2]=i[x>>2],i[u+4>>2]=Y,Me(g,t),u=0|AA(t,e,a))x=0,Y=0;else{if(u=0|i[g>>2],(0|(c=0|i[d>>2]))>0){s=0|i[A+12>>2],r=0;do{u=(0|i[s+(r<<3)>>2])+u|0,r=r+1|0}while((0|r)!=(0|c));r=u}else r=u;c=0|i[(u=a)>>2],(0|(u=0|i[u+4>>2]))<(0|(s=((0|r)<0)<<31>>31))|(0|u)==(0|s)&c>>>0<r>>>0?(i[(u=a)>>2]=r,i[u+4>>2]=s,u=s):r=c,x=0|Xe(0|r,0|u,12,0),Y=0|E(),i[(u=a)>>2]=x,i[u+4>>2]=Y,u=0}if(!u){if(!(r=0|Ne(x,8)))return je(l),T=o,13;if(!(b=0|Ne(x,8)))return je(l),je(r),T=o,13;i[(L=t)>>2]=0,i[L+4>>2]=0,U=0|i[(L=A)+4>>2],i[(u=g)>>2]=i[L>>2],i[u+4>>2]=U,u=0|R(g,x,Y,e,t,r,b);A:do{if(u)je(r),je(b),je(l);else{e:do{if((0|i[d>>2])>0){for(s=A+12|0,c=0;u=0|R((0|i[s>>2])+(c<<3)|0,x,Y,e,t,r,b),c=c+1|0,!(0|u);)if((0|c)>=(0|i[d>>2]))break e;je(r),je(b),je(l);break A}}while(0);(0|Y)>0|0==(0|Y)&x>>>0>0&&br(0|b,0,x<<3|0),L=0|i[(U=t)+4>>2];e:do{if((0|L)>0|0==(0|L)&(0|i[U>>2])>>>0>0){I=r,p=b,F=r,L=b,U=r,u=r,y=r,M=b,P=b,r=b;r:for(;;){for(Q=0,h=0,C=0,_=0,c=0,s=0;;){g=(b=a)+56|0;do{i[b>>2]=0,b=b+4|0}while((0|b)<(0|g));if(0|G(d=0|i[(e=I+(Q<<3)|0)>>2],e=0|i[e+4>>2],1,a,0)){g=(b=a)+56|0;do{i[b>>2]=0,b=b+4|0}while((0|b)<(0|g));0|(b=0|Ne(7,4))&&(z(d,e,1,a,b,7,0,0),je(b))}for(D=0;;){k=0|i[(m=a+(D<<3)|0)>>2],m=0|i[m+4>>2];n:do{if(0==(0|k)&0==(0|m))b=c,g=s;else{if(w=0|ir(0|k,0|m,0|x,0|Y),d=0|E(),!(0==(0|(g=0|i[(e=b=n+(w<<3)|0)>>2]))&0==(0|(e=0|i[e+4>>2])))){B=0,v=0;do{if((0|B)>(0|Y)|(0|B)==(0|Y)&v>>>0>x>>>0)break r;if((0|g)==(0|k)&(0|e)==(0|m)){b=c,g=s;break n}w=0|nr(0|(b=0|Xe(0|w,0|d,1,0)),0|E(),0|x,0|Y),d=0|E(),v=0|Xe(0|v,0|B,1,0),B=0|E(),g=0|i[(e=b=n+(w<<3)|0)>>2],e=0|i[e+4>>2]}while(!(0==(0|g)&0==(0|e)))}if(0==(0|k)&0==(0|m)){b=c,g=s;break}ee(k,m,f),0|pe(A,l,f)&&(v=0|Xe(0|c,0|s,1,0),s=0|E(),i[(B=b)>>2]=k,i[B+4>>2]=m,i[(c=p+(c<<3)|0)>>2]=k,i[c+4>>2]=m,c=v),b=c,g=s}}while(0);if((D=D+1|0)>>>0>=7)break;c=b,s=g}if(Q=0|Xe(0|Q,0|h,1,0),h=0|E(),C=0|Xe(0|C,0|_,1,0),_=0|E(),c=0|i[(s=t)>>2],!((0|_)<(0|(s=0|i[s+4>>2]))|(0|_)==(0|s)&C>>>0<c>>>0))break;c=b,s=g}if((0|s)>0|0==(0|s)&c>>>0>0){c=0,s=0;do{i[(_=I+(c<<3)|0)>>2]=0,i[_+4>>2]=0,c=0|Xe(0|c,0|s,1,0),s=0|E(),C=0|i[(_=t)+4>>2]}while((0|s)<(0|C)|((0|s)==(0|C)?c>>>0<(0|i[_>>2])>>>0:0))}if(i[(_=t)>>2]=b,i[_+4>>2]=g,!((0|g)>0|0==(0|g)&b>>>0>0))break e;D=r,Q=P,h=U,C=M,_=p,r=y,P=u,M=F,y=D,u=Q,U=L,L=h,F=C,p=I,I=_}je(F),je(L),je(l),u=1;break A}u=b}while(0);je(l),je(r),je(u),u=0}}while(0);return T=o,0|u}}return je(l),T=o,0|u},_polygonToCellsExperimental:function(A,e,r,n,t,a){n|=0,t|=0,a|=0;var f,o,l,u=0,c=0,s=0,b=0,g=0,d=0,w=0,B=0;l=T,T=T+160|0,c=l+64|0,g=l+112|0,B=l,Ce(b=l+80|0,A|=0,e|=0,r|=0),ie(c,0|i[(s=b)>>2],0|i[s+4>>2],e),f=0|i[(s=c)>>2],s=0|i[s+4>>2],u=0|i[b+8>>2],i[(d=g+4|0)>>2]=i[b>>2],i[d+4>>2]=i[b+4>>2],i[d+8>>2]=i[b+8>>2],i[d+12>>2]=i[b+12>>2],i[d+16>>2]=i[b+16>>2],i[d+20>>2]=i[b+20>>2],i[d+24>>2]=i[b+24>>2],i[d+28>>2]=i[b+28>>2],i[(d=B)>>2]=f,i[d+4>>2]=s,i[(d=B+8|0)>>2]=u,e=g,r=36+(A=B+12|0)|0;do{i[A>>2]=i[e>>2],A=A+4|0,e=e+4|0}while((0|A)<(0|r));if(i[(g=B+48|0)>>2]=i[c>>2],i[g+4>>2]=i[c+4>>2],i[g+8>>2]=i[c+8>>2],i[g+12>>2]=i[c+12>>2],0==(0|f)&0==(0|s))return T=l,0|u;r=B+16|0,o=B+24|0,b=B+28|0,u=0,c=0,e=f,A=s;do{if(!((0|u)<(0|t)|(0|u)==(0|t)&c>>>0<n>>>0)){w=4;break}if(s=c,c=0|Xe(0|c,0|u,1,0),u=0|E(),i[(s=a+(s<<3)|0)>>2]=e,i[s+4>>2]=A,ae(g),0==(0|(e=0|i[(A=g)>>2]))&0==(0|(A=0|i[A+4>>2]))){if(Ee(r),0==(0|(A=0|i[(e=r)>>2]))&0==(0|(e=0|i[e+4>>2]))){w=10;break}te(A,e,0|i[b>>2],g),e=0|i[(A=g)>>2],A=0|i[A+4>>2]}i[(s=B)>>2]=e,i[s+4>>2]=A}while(!(0==(0|e)&0==(0|A)));return 4==(0|w)?(0|(e=0|i[(A=B+40|0)>>2])&&je(e),i[(w=B+16|0)>>2]=0,i[w+4>>2]=0,i[o>>2]=0,i[B+36>>2]=0,i[b>>2]=-1,i[B+32>>2]=0,i[A>>2]=0,te(0,0,0,g),i[B>>2]=0,i[B+4>>2]=0,i[d>>2]=0,T=l,0|(B=14)):(10==(0|w)&&(i[B>>2]=0,i[B+4>>2]=0,i[d>>2]=i[o>>2]),T=l,0|(B=0|i[d>>2]))},_readInt64AsDoubleFromPointer:function(A){return+(+((0|i[(A|=0)>>2])>>>0)+4294967296*+(0|i[A+4>>2]))},_res0CellCount:function(){return 122},_round:gr,_sbrk:dr,_sizeOfCellBoundary:function(){return 168},_sizeOfCoordIJ:function(){return 8},_sizeOfGeoLoop:function(){return 8},_sizeOfGeoPolygon:function(){return 16},_sizeOfH3Index:function(){return 8},_sizeOfLatLng:function(){return 16},_sizeOfLinkedGeoPolygon:function(){return 12},_uncompactCells:function(A,e,r,n,t,a,f){A|=0,n|=0,t|=0,a|=0;var o,l=0,u=0,c=0,s=0,b=0,g=0,d=0,w=0;if(o=T,T=T+16|0,w=o,!((0|(r|=0))>0|0==(0|r)&(e|=0)>>>0>0))return T=o,0;if((0|(f|=0))>=16)return T=o,12;g=0,d=0,b=0,l=0;A:for(;;){if(s=0|ar(0|(u=0|i[(c=A+(g<<3)|0)>>2]),0|(c=0|i[c+4>>2]),52),E(),(15&s|0)>(0|f)){l=12,u=11;break}if(ie(w,u,c,f),0==(0|(c=0|i[(s=w)>>2]))&0==(0|(s=0|i[s+4>>2])))u=b;else{u=b;do{if(!((0|l)<(0|a)|(0|l)==(0|a)&u>>>0<t>>>0)){u=10;break A}i[(b=n+(u<<3)|0)>>2]=c,i[b+4>>2]=s,u=0|Xe(0|u,0|l,1,0),l=0|E(),ae(w),c=0|i[(b=w)>>2],s=0|i[b+4>>2]}while(!(0==(0|c)&0==(0|s)))}if(g=0|Xe(0|g,0|d,1,0),!((0|(d=0|E()))<(0|r)|(0|d)==(0|r)&g>>>0<e>>>0)){l=0,u=11;break}b=u}return 10==(0|u)?(T=o,0|(w=14)):11==(0|u)?(T=o,0|l):0},_uncompactCellsSize:function(A,e,r,n,t){A|=0,e|=0,r|=0,n|=0,t|=0;var a,f,o=0,l=0,u=0,c=0,s=0,b=0;f=T,T=T+16|0,a=f;A:do{if((0|r)>0|0==(0|r)&e>>>0>0){for(s=0,l=0,o=0,b=0;;){if(!(0==(0|(u=0|i[(c=A+(s<<3)|0)>>2]))&0==(0|(c=0|i[c+4>>2]))||(c=0==(0|OA(u,c,n,a)),l=0|Xe(0|i[(u=a)>>2],0|i[u+4>>2],0|l,0|o),o=0|E(),c))){o=12;break}if(s=0|Xe(0|s,0|b,1,0),!((0|(b=0|E()))<(0|r)|(0|b)==(0|r)&s>>>0<e>>>0))break A}return T=f,0|o}l=0,o=0}while(0);return i[t>>2]=l,i[t+4>>2]=o,T=f,0},_vertexToLatLng:function(A,e,r){r|=0;var n,t,a,f,o=0,l=0;return f=T,T=T+192|0,t=f,a=f+168|0,o=0|ar(0|(A|=0),0|(e|=0),56),E(),o&=7,0|(n=0|Ae(A,l=-2130706433&e|134217728,a))?(T=f,0|(l=n)):(e=0|ar(0|A,0|e,52),E(),e&=15,0|SA(A,l)?TA(a,e,o,1,t):zA(a,e,o,1,t),i[r>>2]=i[(l=t+8|0)>>2],i[r+4>>2]=i[l+4>>2],i[r+8>>2]=i[l+8>>2],i[r+12>>2]=i[l+12>>2],T=f,0|(l=0))},establishStackSpace:function(A,e){T=A|=0},stackAlloc:function(A){var e;return e=T,T=15+(T=T+(A|=0)|0)&-16,0|e},stackRestore:function(A){T=A|=0},stackSave:function(){return 0|T}}}({Math:Math,Int8Array:Int8Array,Int32Array:Int32Array,Uint8Array:Uint8Array,Float32Array:Float32Array,Float64Array:Float64Array},{a:vA,b:function(A){v=A},c:function(){return v},d:function(A,e,r,n){vA("Assertion failed: "+I(A)+", at: "+[e?I(e):"unknown filename",r,n?I(n):"unknown function"])},e:function(A){return r.___errno_location&&(_[r.___errno_location()>>2]=A),A},f:AA,g:function(A,e,r){C.set(C.subarray(e,e+r),A)},h:function(A){var e=AA();if(A>2130706432)return!1;for(var r=Math.max(e,16777216);r<A;)r=r<=536870912?p(2*r,16777216):Math.min(p((3*r+2147483648)/4,16777216),2130706432);return!!eA(r)},i:function(A){vA("OOM")},j:X,k:q,l:eA,m:$,n:function(){var A=$();return r.extraStackTrace&&(A+="\n"+r.extraStackTrace()),q(A)},o:28624,p:28608},Q),oA=(r.___divdi3=fA.___divdi3,r.___muldi3=fA.___muldi3,r.___remdi3=fA.___remdi3,r.___uremdi3=fA.___uremdi3,r._areNeighborCells=fA._areNeighborCells,r._bitshift64Ashr=fA._bitshift64Ashr,r._bitshift64Lshr=fA._bitshift64Lshr,r._bitshift64Shl=fA._bitshift64Shl,r._calloc=fA._calloc,r._cellAreaKm2=fA._cellAreaKm2,r._cellAreaM2=fA._cellAreaM2,r._cellAreaRads2=fA._cellAreaRads2,r._cellToBoundary=fA._cellToBoundary,r._cellToCenterChild=fA._cellToCenterChild,r._cellToChildPos=fA._cellToChildPos,r._cellToChildren=fA._cellToChildren,r._cellToChildrenSize=fA._cellToChildrenSize,r._cellToLatLng=fA._cellToLatLng,r._cellToLocalIj=fA._cellToLocalIj,r._cellToParent=fA._cellToParent,r._cellToVertex=fA._cellToVertex,r._cellToVertexes=fA._cellToVertexes,r._cellsToDirectedEdge=fA._cellsToDirectedEdge,r._cellsToLinkedMultiPolygon=fA._cellsToLinkedMultiPolygon,r._childPosToCell=fA._childPosToCell,r._compactCells=fA._compactCells,r._destroyLinkedMultiPolygon=fA._destroyLinkedMultiPolygon,r._directedEdgeToBoundary=fA._directedEdgeToBoundary,r._directedEdgeToCells=fA._directedEdgeToCells,r._edgeLengthKm=fA._edgeLengthKm,r._edgeLengthM=fA._edgeLengthM,r._edgeLengthRads=fA._edgeLengthRads,r._emscripten_replace_memory=fA._emscripten_replace_memory),lA=(r._free=fA._free,r._getBaseCellNumber=fA._getBaseCellNumber,r._getDirectedEdgeDestination=fA._getDirectedEdgeDestination,r._getDirectedEdgeOrigin=fA._getDirectedEdgeOrigin,r._getHexagonAreaAvgKm2=fA._getHexagonAreaAvgKm2,r._getHexagonAreaAvgM2=fA._getHexagonAreaAvgM2,r._getHexagonEdgeLengthAvgKm=fA._getHexagonEdgeLengthAvgKm,r._getHexagonEdgeLengthAvgM=fA._getHexagonEdgeLengthAvgM,r._getIcosahedronFaces=fA._getIcosahedronFaces,r._getNumCells=fA._getNumCells,r._getPentagons=fA._getPentagons,r._getRes0Cells=fA._getRes0Cells,r._getResolution=fA._getResolution,r._greatCircleDistanceKm=fA._greatCircleDistanceKm,r._greatCircleDistanceM=fA._greatCircleDistanceM,r._greatCircleDistanceRads=fA._greatCircleDistanceRads,r._gridDisk=fA._gridDisk,r._gridDiskDistances=fA._gridDiskDistances,r._gridDistance=fA._gridDistance,r._gridPathCells=fA._gridPathCells,r._gridPathCellsSize=fA._gridPathCellsSize,r._gridRingUnsafe=fA._gridRingUnsafe,r._i64Add=fA._i64Add,r._i64Subtract=fA._i64Subtract,r._isPentagon=fA._isPentagon,r._isResClassIII=fA._isResClassIII,r._isValidCell=fA._isValidCell,r._isValidDirectedEdge=fA._isValidDirectedEdge,r._isValidVertex=fA._isValidVertex,r._latLngToCell=fA._latLngToCell,r._llvm_ctlz_i64=fA._llvm_ctlz_i64,r._llvm_maxnum_f64=fA._llvm_maxnum_f64,r._llvm_minnum_f64=fA._llvm_minnum_f64,r._llvm_round_f64=fA._llvm_round_f64,r._localIjToCell=fA._localIjToCell,r._malloc=fA._malloc,r._maxFaceCount=fA._maxFaceCount,r._maxGridDiskSize=fA._maxGridDiskSize,r._maxPolygonToCellsSize=fA._maxPolygonToCellsSize,r._maxPolygonToCellsSizeExperimental=fA._maxPolygonToCellsSizeExperimental,r._memcpy=fA._memcpy,r._memset=fA._memset,r._originToDirectedEdges=fA._originToDirectedEdges,r._pentagonCount=fA._pentagonCount,r._polygonToCells=fA._polygonToCells,r._polygonToCellsExperimental=fA._polygonToCellsExperimental,r._readInt64AsDoubleFromPointer=fA._readInt64AsDoubleFromPointer,r._res0CellCount=fA._res0CellCount,r._round=fA._round,r._sbrk=fA._sbrk,r._sizeOfCellBoundary=fA._sizeOfCellBoundary,r._sizeOfCoordIJ=fA._sizeOfCoordIJ,r._sizeOfGeoLoop=fA._sizeOfGeoLoop,r._sizeOfGeoPolygon=fA._sizeOfGeoPolygon,r._sizeOfH3Index=fA._sizeOfH3Index,r._sizeOfLatLng=fA._sizeOfLatLng,r._sizeOfLinkedGeoPolygon=fA._sizeOfLinkedGeoPolygon,r._uncompactCells=fA._uncompactCells,r._uncompactCellsSize=fA._uncompactCellsSize,r._vertexToLatLng=fA._vertexToLatLng,r.establishStackSpace=fA.establishStackSpace,r.stackAlloc=fA.stackAlloc),uA=r.stackRestore=fA.stackRestore,cA=r.stackSave=fA.stackSave;if(r.asm=fA,r.cwrap=function(A,e,r,n){var i=(r=r||[]).every(function(A){return"number"===A});return"string"!==e&&i&&!n?D(A):function(){return function(A,e,r,n,i){var t={string:function(A){var e=0;return null!=A&&0!==A&&(e=lA(1+(A.length<<2))),e},array:function(A){var e=lA(A.length);return h.set(A,e),e}},a=D(A),f=[],o=0;if(n)for(var l=0;l<n.length;l++){var u=t[r[l]];u?(0===o&&(o=cA()),f[l]=u(n[l])):f[l]=n[l]}var c=a.apply(null,f);return c=function(A){return"string"===e?I(A):"boolean"===e?Boolean(A):A}(c),0!==o&&uA(o),c}(A,e,r,arguments)}},r.setValue=function(A,e,r,n){switch("*"===(r=r||"i8").charAt(r.length-1)&&(r="i32"),r){case"i1":case"i8":h[A>>0]=e;break;case"i16":E[A>>1]=e;break;case"i32":_[A>>2]=e;break;case"i64":J=[e>>>0,(Z=e,+R(Z)>=1?Z>0?(0|O(+V(Z/4294967296),4294967295))>>>0:~~+Y((Z-+(~~Z>>>0))/4294967296)>>>0:0)],_[A>>2]=J[0],_[A+4>>2]=J[1];break;case"float":y[A>>2]=e;break;case"double":M[A>>3]=e;break;default:vA("invalid type for setValue: "+r)}},r.getValue=function(A,e,r){switch("*"===(e=e||"i8").charAt(e.length-1)&&(e="i32"),e){case"i1":case"i8":return h[A>>0];case"i16":return E[A>>1];case"i32":case"i64":return _[A>>2];case"float":return y[A>>2];case"double":return M[A>>3];default:vA("invalid type for getValue: "+e)}return null},K)if(N(K)||(tA=K,K=r.locateFile?r.locateFile(tA,d):d+tA),l||f){var sA=s(K);C.set(sA,8)}else{S++,r.monitorRunDependencies&&r.monitorRunDependencies(S);var bA=function(A){A.byteLength&&(A=new Uint8Array(A)),C.set(A,8),r.memoryInitializerRequest&&delete r.memoryInitializerRequest.response,function(A){if(S--,r.monitorRunDependencies&&r.monitorRunDependencies(S),0==S&&(null!==H&&(clearInterval(H),H=null),W)){var e=W;W=null,e()}}()},gA=function(){c(K,bA,function(){throw"could not load memory initializer "+K})},dA=iA(K);if(dA)bA(dA.buffer);else if(r.memoryInitializerRequest){var wA=function(){var A=r.memoryInitializerRequest,e=A.response;if(200!==A.status&&0!==A.status){var n=iA(r.memoryInitializerRequestURL);if(!n)return console.warn("a problem seems to have happened with Module.memoryInitializerRequest, status: "+A.status+", retrying "+K),void gA();e=n.buffer}bA(e)};r.memoryInitializerRequest.response?setTimeout(wA,0):r.memoryInitializerRequest.addEventListener("load",wA)}else gA()}function BA(A){function e(){aA||(aA=!0,k||(L(z),L(U),r.onRuntimeInitialized&&r.onRuntimeInitialized(),function(){if(r.postRun)for("function"==typeof r.postRun&&(r.postRun=[r.postRun]);r.postRun.length;)A=r.postRun.shift(),x.unshift(A);var A;L(x)}()))}A=A||o,S>0||(function(){if(r.preRun)for("function"==typeof r.preRun&&(r.preRun=[r.preRun]);r.preRun.length;)A=r.preRun.shift(),G.unshift(A);var A;L(G)}(),S>0||(r.setStatus?(r.setStatus("Running..."),setTimeout(function(){setTimeout(function(){r.setStatus("")},1),e()},1)):e()))}function vA(A){throw r.onAbort&&r.onAbort(A),w(A+=""),B(A),k=!0,"abort("+A+"). Build with -s ASSERTIONS=1 for more info."}if(W=function A(){aA||BA(),aA||(W=A)},r.run=BA,r.abort=vA,r.preInit)for("function"==typeof r.preInit&&(r.preInit=[r.preInit]);r.preInit.length>0;)r.preInit.pop()();return BA(),A}("object"==typeof e?e:{}),r="number",n=[["sizeOfH3Index",r],["sizeOfLatLng",r],["sizeOfCellBoundary",r],["sizeOfGeoLoop",r],["sizeOfGeoPolygon",r],["sizeOfLinkedGeoPolygon",r],["sizeOfCoordIJ",r],["readInt64AsDoubleFromPointer",r],["isValidCell","number",["number","number"]],["latLngToCell","number",[r,r,"number",r]],["cellToLatLng","number",["number","number",r]],["cellToBoundary","number",["number","number",r]],["maxGridDiskSize","number",[r,r]],["gridDisk","number",["number","number",r,r]],["gridDiskDistances","number",["number","number",r,r,r]],["gridRingUnsafe","number",["number","number",r,r]],["maxPolygonToCellsSize","number",[r,"number",r,r]],["polygonToCells","number",[r,"number",r,r]],["maxPolygonToCellsSizeExperimental","number",[r,"number",r,r]],["polygonToCellsExperimental","number",[r,"number",r,r,r,r]],["cellsToLinkedMultiPolygon","number",[r,r,r]],["destroyLinkedMultiPolygon",null,[r]],["compactCells","number",[r,r,r,r]],["uncompactCells","number",[r,r,r,r,r,"number"]],["uncompactCellsSize","number",[r,r,r,"number",r]],["isPentagon","number",["number","number"]],["isResClassIII","number",["number","number"]],["getBaseCellNumber",r,["number","number"]],["getResolution",r,["number","number"]],["maxFaceCount","number",["number","number",r]],["getIcosahedronFaces","number",["number","number",r]],["cellToParent","number",["number","number","number",r]],["cellToChildren","number",["number","number","number",r]],["cellToCenterChild","number",["number","number","number",r]],["cellToChildrenSize","number",["number","number","number",r]],["cellToChildPos","number",["number","number","number",r]],["childPosToCell","number",[r,r,"number","number","number",r]],["areNeighborCells","number",["number","number","number","number",r]],["cellsToDirectedEdge","number",["number","number","number","number",r]],["getDirectedEdgeOrigin","number",["number","number",r]],["getDirectedEdgeDestination","number",["number","number",r]],["isValidDirectedEdge","number",["number","number"]],["directedEdgeToCells","number",["number","number",r]],["originToDirectedEdges","number",["number","number",r]],["directedEdgeToBoundary","number",["number","number",r]],["gridDistance","number",["number","number","number","number",r]],["gridPathCells","number",["number","number","number","number",r]],["gridPathCellsSize","number",["number","number","number","number",r]],["cellToLocalIj","number",["number","number","number","number",r,r]],["localIjToCell","number",["number","number",r,r,r]],["getHexagonAreaAvgM2","number",["number",r]],["getHexagonAreaAvgKm2","number",["number",r]],["getHexagonEdgeLengthAvgM","number",["number",r]],["getHexagonEdgeLengthAvgKm","number",["number",r]],["greatCircleDistanceM",r,[r,r]],["greatCircleDistanceKm",r,[r,r]],["greatCircleDistanceRads",r,[r,r]],["cellAreaM2","number",["number","number",r]],["cellAreaKm2","number",["number","number",r]],["cellAreaRads2","number",["number","number",r]],["edgeLengthM","number",["number","number",r]],["edgeLengthKm","number",["number","number",r]],["edgeLengthRads","number",["number","number",r]],["getNumCells","number",["number",r]],["getRes0Cells","number",[r]],["res0CellCount",r],["getPentagons","number",[r,r]],["pentagonCount",r],["cellToVertex","number",["number","number",r,r]],["cellToVertexes","number",["number","number",r]],["vertexToLatLng","number",["number","number",r]],["isValidVertex","number",["number","number"]]],i=4,t=5,a={0:"Success",1:"The operation failed but a more specific error is not available",2:"Argument was outside of acceptable range",3:"Latitude or longitude arguments were outside of acceptable range"};a[i]="Resolution argument was outside of acceptable range",a[t]="Cell argument was not valid",a[6]="Directed edge argument was not valid",a[7]="Undirected edge argument was not valid",a[8]="Vertex argument was not valid",a[9]="Pentagon distortion was encountered",a[10]="Duplicate input",a[11]="Cell arguments were not neighbors",a[12]="Cell arguments had incompatible resolutions",a[13]="Memory allocation failed",a[14]="Bounds of provided memory were insufficient",a[15]="Mode or flags argument was not valid";var f=1001,o=1002,l={1000:"Unknown unit"};l[f]="Array length out of bounds",l[o]="Got unexpected null value for H3 index";var u="Unknown error";function c(A,e,r){var n=new Error((A[e]||u)+" (code: "+e+(r&&"value"in r?", value: "+r.value:"")+")");return n.code=e,n}function s(A,e){return c(a,A,2===arguments.length?{value:e}:{})}function b(A,e){return c(l,A,2===arguments.length?{value:e}:{})}function g(A){if(0!==A)throw s(A)}var d={};n.forEach(function(A){d[A[0]]=e.cwrap.apply(e,A)});var w=16,B=4,v=8,k=8,m=d.sizeOfH3Index(),D=d.sizeOfLatLng(),Q=d.sizeOfCellBoundary(),h=d.sizeOfGeoPolygon(),C=d.sizeOfGeoLoop(),E=d.sizeOfLinkedGeoPolygon(),_=d.sizeOfCoordIJ(),y={m:"m",m2:"m2",km:"km",km2:"km2",rads:"rads",rads2:"rads2"},M={containmentCenter:"containmentCenter",containmentFull:"containmentFull",containmentOverlapping:"containmentOverlapping",containmentOverlappingBbox:"containmentOverlappingBbox"};function P(A){if("number"!=typeof A||A<0||A>15||Math.floor(A)!==A)throw s(i,A);return A}function I(A){if(!A)throw b(o);return A}var p=Math.pow(2,32)-1;function T(A){if(A>p)throw b(f,A);return A}var F=/[^0-9a-fA-F]/;function L(A){if(Array.isArray(A)&&2===A.length&&Number.isInteger(A[0])&&Number.isInteger(A[1]))return A;if("string"!=typeof A||F.test(A))return[0,0];var e=parseInt(A.substring(0,A.length-8),w);return[parseInt(A.substring(A.length-8),w),e]}function G(A){if(A>=0)return A.toString(w);var e=U(8,(A&=2147483647).toString(w));return(parseInt(e[0],w)+8).toString(w)+e.substring(1)}function z(A,e){return G(e)+U(8,G(A))}function U(A,e){for(var r=A-e.length,n="",i=0;i<r;i++)n+="0";return n+e}var x=Math.pow(2,32);function R(A,r,n){for(var i=A.length,t=e._calloc(i,D),a=n?1:0,f=n?0:1,o=0;o<2*i;o+=2)e.HEAPF64.set([A[o/2][a],A[o/2][f]].map(eA),t/v+o);return e.HEAPU32.set([i,t],r/B),r}function Y(A,r){var n,i=A.length-1,t=e._calloc(h),a=0+C,f=a+B;if(R(A[0],t+0,r),i>0){n=e._calloc(i,C);for(var o=0;o<i;o++)R(A[o+1],n+C*o,r)}return e.setValue(t+a,i,"i32"),e.setValue(t+f,n,"i32"),t}function V(A){var r=0+C,n=r+B,i=B;e._free(e.getValue(A+0+i,"i8*"));var t=e.getValue(A+r,"i32");if(t>0){for(var a=e.getValue(A+n,"i32"),f=0;f<t;f++)e._free(e.getValue(a+C*f+i,"i8*"));e._free(a)}e._free(A)}function O(A,r){void 0===r&&(r=0);var n=e.getValue(A+m*r,"i32"),i=e.getValue(A+m*r+B,"i32");return i?z(n,i):null}function S(A,r){return void 0===r&&(r=0),e.getValue(A+v*r,"double")}function H(A){return d.readInt64AsDoubleFromPointer(A)}function W(A,r,n){e.HEAPU32.set(L(A),r/B+2*n)}function Z(A,e){for(var r=[],n=0;n<e;n++){var i=O(A,n);null!==i&&r.push(i)}return r}function J(A,e){for(var r=e.length,n=0;n<r;n++)W(e[n],A,n)}function K(A,r){var n=e._calloc(1,D);return e.HEAPF64.set([A,r].map(eA),n/v),n}function j(A){return rA(e.getValue(A,"double"))}function N(A){return[j(A),j(A+v)]}function X(A){return[j(A+v),j(A)]}function q(A,r,n){for(var i=e.getValue(A,"i32"),t=A+v,a=[],f=r?X:N,o=0;o<2*i;o+=2)a.push(f(t+v*o));return n&&a.push(a[0]),a}function $(A){var e=L(A);return Boolean(d.isValidCell(e[0],e[1]))}function AA(A,r){if(!$(A))throw s(t);var n=L(A),i=n[0],a=n[1],f=e._malloc(k);try{return g(d.cellToChildrenSize(i,a,r,f)),H(f)}finally{e._free(f)}}function eA(A){return A*Math.PI/180}function rA(A){return 180*A/Math.PI}A.UNITS=y,A.POLYGON_TO_CELLS_FLAGS=M,A.h3IndexToSplitLong=L,A.splitLongToH3Index=z,A.isValidCell=$,A.isPentagon=function(A){var e=L(A);return Boolean(d.isPentagon(e[0],e[1]))},A.isResClassIII=function(A){var e=L(A);return Boolean(d.isResClassIII(e[0],e[1]))},A.getBaseCellNumber=function(A){var e=L(A);return d.getBaseCellNumber(e[0],e[1])},A.getIcosahedronFaces=function(A){var r=L(A),n=r[0],i=r[1],t=e._malloc(B);try{g(d.maxFaceCount(n,i,t));var a=e.getValue(t,"i32"),f=e._malloc(B*a);try{return g(d.getIcosahedronFaces(n,i,f)),function(A,r){for(var n=[],i=0;i<r;i++){var t=e.getValue(A+B*i,"i32");t>=0&&n.push(t)}return n}(f,a)}finally{e._free(f)}}finally{e._free(t)}},A.getResolution=function(A){var e=L(A),r=e[0],n=e[1];return d.isValidCell(r,n)?d.getResolution(r,n):-1},A.latLngToCell=function(A,r,n){var i=e._malloc(D);e.HEAPF64.set([A,r].map(eA),i/v);var t=e._malloc(m);try{return g(d.latLngToCell(i,n,t)),I(O(t))}finally{e._free(t),e._free(i)}},A.cellToLatLng=function(A){var r=e._malloc(D),n=L(A),i=n[0],t=n[1];try{return g(d.cellToLatLng(i,t,r)),N(r)}finally{e._free(r)}},A.cellToBoundary=function(A,r){var n=e._malloc(Q),i=L(A),t=i[0],a=i[1];try{return g(d.cellToBoundary(t,a,n)),q(n,r,r)}finally{e._free(n)}},A.cellToParent=function(A,r){var n=L(A),i=n[0],t=n[1],a=e._malloc(m);try{return g(d.cellToParent(i,t,r,a)),I(O(a))}finally{e._free(a)}},A.cellToChildren=function(A,r){if(!$(A))return[];var n=L(A),i=n[0],t=n[1],a=T(AA(A,r)),f=e._calloc(a,m);try{return g(d.cellToChildren(i,t,r,f)),Z(f,a)}finally{e._free(f)}},A.cellToChildrenSize=AA,A.cellToCenterChild=function(A,r){var n=L(A),i=n[0],t=n[1],a=e._malloc(m);try{return g(d.cellToCenterChild(i,t,r,a)),I(O(a))}finally{e._free(a)}},A.cellToChildPos=function(A,r){var n=L(A),i=n[0],t=n[1],a=e._malloc(k);try{return g(d.cellToChildPos(i,t,r,a)),H(a)}finally{e._free(a)}},A.childPosToCell=function(A,r,n){var i,t="number"!=typeof(i=A)?[0,0]:[0|i,i/x|0],a=t[0],f=t[1],o=L(r),l=o[0],u=o[1],c=e._malloc(m);try{return g(d.childPosToCell(a,f,l,u,n,c)),I(O(c))}finally{e._free(c)}},A.gridDisk=function(A,r){var n=L(A),i=n[0],t=n[1],a=e._malloc(k);try{g(d.maxGridDiskSize(r,a));var f=T(H(a)),o=e._calloc(f,m);try{return g(d.gridDisk(i,t,r,o)),Z(o,f)}finally{e._free(o)}}finally{e._free(a)}},A.gridDiskDistances=function(A,r){var n=L(A),i=n[0],t=n[1],a=e._malloc(k);try{g(d.maxGridDiskSize(r,a));var f=T(H(a)),o=e._calloc(f,m),l=e._calloc(f,B);try{g(d.gridDiskDistances(i,t,r,o,l));for(var u=[],c=0;c<r+1;c++)u.push([]);for(var s=0;s<f;s++){var b=O(o,s),w=e.getValue(l+B*s,"i32");null!==b&&u[w].push(b)}return u}finally{e._free(o),e._free(l)}}finally{e._free(a)}},A.gridRingUnsafe=function(A,r){var n=0===r?1:6*r,i=e._calloc(n,m);try{return g(d.gridRingUnsafe.apply(d,L(A).concat([r],[i]))),Z(i,n)}finally{e._free(i)}},A.polygonToCells=function(A,r,n){if(P(r),n=Boolean(n),0===A.length||0===A[0].length)return[];var i=Y("number"==typeof A[0][0]?[A]:A,n),t=e._malloc(k);try{g(d.maxPolygonToCellsSize(i,r,0,t));var a=T(H(t)),f=e._calloc(a,m);try{return g(d.polygonToCells(i,r,0,f)),Z(f,a)}finally{e._free(f)}}finally{e._free(t),V(i)}},A.polygonToCellsExperimental=function(A,r,n,i){P(r),i=Boolean(i);var t=function(A){switch(A){case M.containmentCenter:return 0;case M.containmentFull:return 1;case M.containmentOverlapping:return 2;case M.containmentOverlappingBbox:return 3;default:throw b(15,A)}}(n);if(0===A.length||0===A[0].length)return[];var a=Y("number"==typeof A[0][0]?[A]:A,i),f=e._malloc(k);try{g(d.maxPolygonToCellsSizeExperimental(a,r,t,f));var o=T(H(f)),l=e._calloc(o,m);try{return g(d.polygonToCellsExperimental(a,r,t,o,0,l)),Z(l,o)}finally{e._free(l)}}finally{e._free(f),V(a)}},A.cellsToMultiPolygon=function(A,r){if(!A||!A.length)return[];var n=A.length,i=e._calloc(n,m);J(i,A);var t=e._calloc(E);try{return g(d.cellsToLinkedMultiPolygon(i,n,t)),function(A,r){for(var n,i,t,a,f=[],o=r?X:N;A;){for(f.push(n=[]),i=e.getValue(A,"i8*");i;){for(n.push(t=[]),a=e.getValue(i,"i8*");a;)t.push(o(a)),a=e.getValue(a+2*v,"i8*");r&&t.push(t[0]),i=e.getValue(i+8,"i8*")}A=e.getValue(A+8,"i8*")}return f}(t,r)}finally{d.destroyLinkedMultiPolygon(t),e._free(t),e._free(i)}},A.compactCells=function(A){if(!A||!A.length)return[];var r=A.length,n=e._calloc(r,m);J(n,A);var i=e._calloc(r,m);try{return g(d.compactCells(n,i,r,0)),Z(i,r)}finally{e._free(n),e._free(i)}},A.uncompactCells=function(A,r){if(P(r),!A||!A.length)return[];var n=A.length,i=e._calloc(n,m);J(i,A);var t=e._malloc(k);try{g(d.uncompactCellsSize(i,n,0,r,t));var a=T(H(t)),f=e._calloc(a,m);try{return g(d.uncompactCells(i,n,0,f,a,0,r)),Z(f,a)}finally{e._free(i),e._free(f)}}finally{e._free(t)}},A.areNeighborCells=function(A,r){var n=L(A),i=n[0],t=n[1],a=L(r),f=a[0],o=a[1],l=e._malloc(B);try{return g(d.areNeighborCells(i,t,f,o,l)),function(A,r){void 0===r&&(r=0);var n=e.getValue(l+B*r,"i32");return Boolean(n)}()}finally{e._free(l)}},A.cellsToDirectedEdge=function(A,r){var n=L(A),i=n[0],t=n[1],a=L(r),f=a[0],o=a[1],l=e._malloc(m);try{return g(d.cellsToDirectedEdge(i,t,f,o,l)),I(O(l))}finally{e._free(l)}},A.getDirectedEdgeOrigin=function(A){var r=L(A),n=r[0],i=r[1],t=e._malloc(m);try{return g(d.getDirectedEdgeOrigin(n,i,t)),I(O(t))}finally{e._free(t)}},A.getDirectedEdgeDestination=function(A){var r=L(A),n=r[0],i=r[1],t=e._malloc(m);try{return g(d.getDirectedEdgeDestination(n,i,t)),I(O(t))}finally{e._free(t)}},A.isValidDirectedEdge=function(A){var e=L(A);return Boolean(d.isValidDirectedEdge(e[0],e[1]))},A.directedEdgeToCells=function(A){var r=L(A),n=r[0],i=r[1],t=e._calloc(2,m);try{return g(d.directedEdgeToCells(n,i,t)),Z(t,2)}finally{e._free(t)}},A.originToDirectedEdges=function(A){var r=L(A),n=r[0],i=r[1],t=e._calloc(6,m);try{return g(d.originToDirectedEdges(n,i,t)),Z(t,6)}finally{e._free(t)}},A.directedEdgeToBoundary=function(A,r){var n=e._malloc(Q),i=L(A),t=i[0],a=i[1];try{return g(d.directedEdgeToBoundary(t,a,n)),q(n,r)}finally{e._free(n)}},A.gridDistance=function(A,r){var n=L(A),i=n[0],t=n[1],a=L(r),f=a[0],o=a[1],l=e._malloc(k);try{return g(d.gridDistance(i,t,f,o,l)),H(l)}finally{e._free(l)}},A.gridPathCells=function(A,r){var n=L(A),i=n[0],t=n[1],a=L(r),f=a[0],o=a[1],l=e._malloc(k);try{g(d.gridPathCellsSize(i,t,f,o,l));var u=T(H(l)),c=e._calloc(u,m);try{return d.gridPathCells(i,t,f,o,c),Z(c,u)}finally{e._free(c)}}finally{e._free(l)}},A.cellToLocalIj=function(A,r){var n,i=e._malloc(_);try{return g(d.cellToLocalIj.apply(d,L(A).concat(L(r),[0],[i]))),{i:e.getValue(n=i,"i32"),j:e.getValue(n+B,"i32")}}finally{e._free(i)}},A.localIjToCell=function(A,r){if(!r||"number"!=typeof r.i||"number"!=typeof r.j)throw new Error("Coordinates must be provided as an {i, j} object");var n,i,t,a=e._malloc(_),f=e._malloc(m);t=(i=r).j,e.setValue(n=a,i.i,"i32"),e.setValue(n+B,t,"i32");try{return g(d.localIjToCell.apply(d,L(A).concat([a],[0],[f]))),I(O(f))}finally{e._free(a),e._free(f)}},A.greatCircleDistance=function(A,r,n){var i,t=K(A[0],A[1]),a=K(r[0],r[1]);switch(n){case y.m:i=d.greatCircleDistanceM(t,a);break;case y.km:i=d.greatCircleDistanceKm(t,a);break;case y.rads:i=d.greatCircleDistanceRads(t,a);break;default:i=null}if(e._free(t),e._free(a),null===i)throw b(1e3,n);return i},A.cellArea=function(A,r){var n=L(A),i=n[0],t=n[1],a=e._malloc(v);try{switch(r){case y.m2:g(d.cellAreaM2(i,t,a));break;case y.km2:g(d.cellAreaKm2(i,t,a));break;case y.rads2:g(d.cellAreaRads2(i,t,a));break;default:throw b(1e3,r)}return S(a)}finally{e._free(a)}},A.edgeLength=function(A,r){var n=L(A),i=n[0],t=n[1],a=e._malloc(v);try{switch(r){case y.m:g(d.edgeLengthM(i,t,a));break;case y.km:g(d.edgeLengthKm(i,t,a));break;case y.rads:g(d.edgeLengthRads(i,t,a));break;default:throw b(1e3,r)}return S(a)}finally{e._free(a)}},A.getHexagonAreaAvg=function(A,r){P(A);var n=e._malloc(v);try{switch(r){case y.m2:g(d.getHexagonAreaAvgM2(A,n));break;case y.km2:g(d.getHexagonAreaAvgKm2(A,n));break;default:throw b(1e3,r)}return S(n)}finally{e._free(n)}},A.getHexagonEdgeLengthAvg=function(A,r){P(A);var n=e._malloc(v);try{switch(r){case y.m:g(d.getHexagonEdgeLengthAvgM(A,n));break;case y.km:g(d.getHexagonEdgeLengthAvgKm(A,n));break;default:throw b(1e3,r)}return S(n)}finally{e._free(n)}},A.cellToVertex=function(A,r){var n=L(A),i=n[0],t=n[1],a=e._malloc(m);try{return g(d.cellToVertex(i,t,r,a)),I(O(a))}finally{e._free(a)}},A.cellToVertexes=function(A){var r=L(A),n=r[0],i=r[1],t=e._calloc(6,m);try{return g(d.cellToVertexes(n,i,t)),Z(t,6)}finally{e._free(t)}},A.vertexToLatLng=function(A){var r=e._malloc(D),n=L(A),i=n[0],t=n[1];try{return g(d.vertexToLatLng(i,t,r)),N(r)}finally{e._free(r)}},A.isValidVertex=function(A){var e=L(A);return Boolean(d.isValidVertex(e[0],e[1]))},A.getNumCells=function(A){P(A);var r=e._malloc(k);try{return g(d.getNumCells(A,r)),H(r)}finally{e._free(r)}},A.getRes0Cells=function(){var A=d.res0CellCount(),r=e._malloc(m*A);try{return g(d.getRes0Cells(r)),Z(r,A)}finally{e._free(r)}},A.getPentagons=function(A){P(A);var r=d.pentagonCount(),n=e._malloc(m*r);try{return g(d.getPentagons(A,n)),Z(n,r)}finally{e._free(n)}},A.degsToRads=eA,A.radsToDegs=rA});
//# sourceMappingURL=h3-js.umd.js.map
