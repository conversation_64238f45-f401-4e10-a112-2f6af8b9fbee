// @ts-nocheck

var libh3 = function (libh3) {
  libh3 = libh3 || {};
  var Module = typeof libh3 !== "undefined" ? libh3 : {};
  var moduleOverrides = {};
  var key;
  for (key in Module) {
    if (Module.hasOwnProperty(key)) {
      moduleOverrides[key] = Module[key];
    }
  }
  var arguments_ = [];
  var thisProgram = "./this.program";
  var ENVIRONMENT_IS_WEB = false;
  var ENVIRONMENT_IS_WORKER = false;
  var ENVIRONMENT_IS_NODE = false;
  var ENVIRONMENT_HAS_NODE = false;
  var ENVIRONMENT_IS_SHELL = false;
  ENVIRONMENT_IS_WEB = typeof window === "object";
  ENVIRONMENT_IS_WORKER = typeof importScripts === "function";
  ENVIRONMENT_HAS_NODE = typeof process === "object" && typeof process.versions === "object" && typeof process.versions.node === "string";
  ENVIRONMENT_IS_NODE = ENVIRONMENT_HAS_NODE && !ENVIRONMENT_IS_WEB && !ENVIRONMENT_IS_WORKER;
  ENVIRONMENT_IS_SHELL = !ENVIRONMENT_IS_WEB && !ENVIRONMENT_IS_NODE && !ENVIRONMENT_IS_WORKER;
  var scriptDirectory = "";
  function locateFile(path) {
    if (Module["locateFile"]) {
      return Module["locateFile"](path, scriptDirectory);
    }
    return scriptDirectory + path;
  }
  var read_, readAsync, readBinary;
  if (ENVIRONMENT_IS_NODE) {
    scriptDirectory = __dirname + "/";
    var nodeFS;
    var nodePath;
    read_ = function shell_read(filename, binary) {
      var ret;
      ret = tryParseAsDataURI(filename);
      if (!ret) {
        if (!nodeFS) { nodeFS = require("fs"); }
        if (!nodePath) { nodePath = require("path"); }
        filename = nodePath["normalize"](filename);
        ret = nodeFS["readFileSync"](filename);
      }
      return binary ? ret : ret.toString();
    };
    readBinary = function readBinary(filename) {
      var ret = read_(filename, true);
      if (!ret.buffer) {
        ret = new Uint8Array(ret);
      }
      assert(ret.buffer);
      return ret;
    };
    if (process["argv"].length > 1) {
      thisProgram = process["argv"][1].replace(/\\/g, "/");
    }
    arguments_ = process["argv"].slice(2);
    Module["inspect"] = function () {
      return "[Emscripten Module object]";
    };
  } else if (ENVIRONMENT_IS_SHELL) {
    if (typeof read != "undefined") {
      read_ = function shell_read(f) {
        var data = tryParseAsDataURI(f);
        if (data) {
          return intArrayToString(data);
        }
        return read(f);
      };
    }
    readBinary = function readBinary(f) {
      var data;
      data = tryParseAsDataURI(f);
      if (data) {
        return data;
      }
      if (typeof readbuffer === "function") {
        return new Uint8Array(readbuffer(f));
      }
      data = read(f, "binary");
      assert(typeof data === "object");
      return data;
    };
    if (typeof scriptArgs != "undefined") {
      arguments_ = scriptArgs;
    } else if (typeof arguments != "undefined") {
      arguments_ = arguments;
    }
    if (typeof print !== "undefined") {
      if (typeof console === "undefined") { console = {}; }
      console.log = print;
      console.warn = console.error = typeof printErr !== "undefined" ? printErr : print;
    }
  } else if (ENVIRONMENT_IS_WEB || ENVIRONMENT_IS_WORKER) {
    if (ENVIRONMENT_IS_WORKER) {
      scriptDirectory = self.location.href;
    } else if (typeof document !== "undefined" && document.currentScript) {
      scriptDirectory = document.currentScript.src;
    }
    if (scriptDirectory.indexOf("blob:") !== 0) {
      scriptDirectory = scriptDirectory.substr(0, scriptDirectory.lastIndexOf("/") + 1);
    } else {
      scriptDirectory = "";
    }
    read_ = function shell_read(url) {
      try {
        var xhr = new XMLHttpRequest();
        xhr.open("GET", url, false);
        xhr.send(null);
        return xhr.responseText;
      } catch (err) {
        var data = tryParseAsDataURI(url);
        if (data) {
          return intArrayToString(data);
        }
        throw err;
      }
    };
    if (ENVIRONMENT_IS_WORKER) {
      readBinary = function readBinary(url) {
        try {
          var xhr = new XMLHttpRequest();
          xhr.open("GET", url, false);
          xhr.responseType = "arraybuffer";
          xhr.send(null);
          return new Uint8Array(xhr.response);
        } catch (err) {
          var data = tryParseAsDataURI(url);
          if (data) {
            return data;
          }
          throw err;
        }
      };
    }
    readAsync = function readAsync(url, onload, onerror) {
      var xhr = new XMLHttpRequest();
      xhr.open("GET", url, true);
      xhr.responseType = "arraybuffer";
      xhr.onload = function xhr_onload() {
        if (xhr.status == 200 || xhr.status == 0 && xhr.response) {
          onload(xhr.response);
          return;
        }
        var data = tryParseAsDataURI(url);
        if (data) {
          onload(data.buffer);
          return;
        }
        onerror();
      };
      xhr.onerror = onerror;
      xhr.send(null);
    };
  }
  var out = Module["print"] || console.log.bind(console);
  var err = Module["printErr"] || console.warn.bind(console);
  for (key in moduleOverrides) {
    if (moduleOverrides.hasOwnProperty(key)) {
      Module[key] = moduleOverrides[key];
    }
  }
  moduleOverrides = null;
  if (Module["arguments"]) { arguments_ = Module["arguments"]; }
  if (Module["thisProgram"]) { thisProgram = Module["thisProgram"]; }
  var tempRet0 = 0;
  var setTempRet0 = function (value) {
    tempRet0 = value;
  };
  var getTempRet0 = function () {
    return tempRet0;
  };
  var GLOBAL_BASE = 8;
  function setValue(ptr, value, type, noSafe) {
    type = type || "i8";
    if (type.charAt(type.length - 1) === "*") { type = "i32"; }
    switch (type) {
      case "i1":
        HEAP8[ptr >> 0] = value;
        break;
      case "i8":
        HEAP8[ptr >> 0] = value;
        break;
      case "i16":
        HEAP16[ptr >> 1] = value;
        break;
      case "i32":
        HEAP32[ptr >> 2] = value;
        break;
      case "i64":
        tempI64 = [value >>> 0, (tempDouble = value, +Math_abs(tempDouble) >= +1 ? tempDouble > +0 ? (Math_min(+Math_floor(tempDouble / +4294967296), +4294967295) | 0) >>> 0 : ~~+Math_ceil((tempDouble - +(~~tempDouble >>> 0)) / +4294967296) >>> 0 : 0)], HEAP32[ptr >> 2] = tempI64[0], HEAP32[ptr + 4 >> 2] = tempI64[1];
        break;
      case "float":
        HEAPF32[ptr >> 2] = value;
        break;
      case "double":
        HEAPF64[ptr >> 3] = value;
        break;
      default:
        abort("invalid type for setValue: " + type);
    }
  }
  function getValue(ptr, type, noSafe) {
    type = type || "i8";
    if (type.charAt(type.length - 1) === "*") { type = "i32"; }
    switch (type) {
      case "i1":
        return HEAP8[ptr >> 0];
      case "i8":
        return HEAP8[ptr >> 0];
      case "i16":
        return HEAP16[ptr >> 1];
      case "i32":
        return HEAP32[ptr >> 2];
      case "i64":
        return HEAP32[ptr >> 2];
      case "float":
        return HEAPF32[ptr >> 2];
      case "double":
        return HEAPF64[ptr >> 3];
      default:
        abort("invalid type for getValue: " + type);
    }
    return null;
  }
  var ABORT = false;
  function assert(condition, text) {
    if (!condition) {
      abort("Assertion failed: " + text);
    }
  }
  function getCFunc(ident) {
    var func = Module["_" + ident];
    assert(func, "Cannot call unknown function " + ident + ", make sure it is exported");
    return func;
  }
  function ccall(ident, returnType, argTypes, args, opts) {
    var toC = {
      "string": function (str) {
        var ret = 0;
        if (str !== null && str !== undefined && str !== 0) {
          var len = (str.length << 2) + 1;
          ret = stackAlloc(len);
          stringToUTF8(str, ret, len);
        }
        return ret;
      },
      "array": function (arr) {
        var ret = stackAlloc(arr.length);
        writeArrayToMemory(arr, ret);
        return ret;
      }
    };
    function convertReturnValue(ret) {
      if (returnType === "string") { return UTF8ToString(ret); }
      if (returnType === "boolean") { return Boolean(ret); }
      return ret;
    }
    var func = getCFunc(ident);
    var cArgs = [];
    var stack = 0;
    if (args) {
      for (var i = 0; i < args.length; i++) {
        var converter = toC[argTypes[i]];
        if (converter) {
          if (stack === 0) { stack = stackSave(); }
          cArgs[i] = converter(args[i]);
        } else {
          cArgs[i] = args[i];
        }
      }
    }
    var ret = func.apply(null, cArgs);
    ret = convertReturnValue(ret);
    if (stack !== 0) { stackRestore(stack); }
    return ret;
  }
  function cwrap(ident, returnType, argTypes, opts) {
    argTypes = argTypes || [];
    var numericArgs = argTypes.every(function (type) {
      return type === "number";
    });
    var numericRet = returnType !== "string";
    if (numericRet && numericArgs && !opts) {
      return getCFunc(ident);
    }
    return function () {
      return ccall(ident, returnType, argTypes, arguments, opts);
    };
  }
  var UTF8Decoder = typeof TextDecoder !== "undefined" ? new TextDecoder("utf8") : undefined;
  function UTF8ArrayToString(u8Array, idx, maxBytesToRead) {
    var endIdx = idx + maxBytesToRead;
    var endPtr = idx;
    while (u8Array[endPtr] && !(endPtr >= endIdx)) { ++endPtr; }
    if (endPtr - idx > 16 && u8Array.subarray && UTF8Decoder) {
      return UTF8Decoder.decode(u8Array.subarray(idx, endPtr));
    } else {
      var str = "";
      while (idx < endPtr) {
        var u0 = u8Array[idx++];
        if (!(u0 & 128)) {
          str += String.fromCharCode(u0);
          continue;
        }
        var u1 = u8Array[idx++] & 63;
        if ((u0 & 224) == 192) {
          str += String.fromCharCode((u0 & 31) << 6 | u1);
          continue;
        }
        var u2 = u8Array[idx++] & 63;
        if ((u0 & 240) == 224) {
          u0 = (u0 & 15) << 12 | u1 << 6 | u2;
        } else {
          u0 = (u0 & 7) << 18 | u1 << 12 | u2 << 6 | u8Array[idx++] & 63;
        }
        if (u0 < 65536) {
          str += String.fromCharCode(u0);
        } else {
          var ch = u0 - 65536;
          str += String.fromCharCode(55296 | ch >> 10, 56320 | ch & 1023);
        }
      }
    }
    return str;
  }
  function UTF8ToString(ptr, maxBytesToRead) {
    return ptr ? UTF8ArrayToString(HEAPU8, ptr, maxBytesToRead) : "";
  }
  function stringToUTF8Array(str, outU8Array, outIdx, maxBytesToWrite) {
    if (!(maxBytesToWrite > 0)) { return 0; }
    var startIdx = outIdx;
    var endIdx = outIdx + maxBytesToWrite - 1;
    for (var i = 0; i < str.length; ++i) {
      var u = str.charCodeAt(i);
      if (u >= 55296 && u <= 57343) {
        var u1 = str.charCodeAt(++i);
        u = 65536 + ((u & 1023) << 10) | u1 & 1023;
      }
      if (u <= 127) {
        if (outIdx >= endIdx) { break; }
        outU8Array[outIdx++] = u;
      } else if (u <= 2047) {
        if (outIdx + 1 >= endIdx) { break; }
        outU8Array[outIdx++] = 192 | u >> 6;
        outU8Array[outIdx++] = 128 | u & 63;
      } else if (u <= 65535) {
        if (outIdx + 2 >= endIdx) { break; }
        outU8Array[outIdx++] = 224 | u >> 12;
        outU8Array[outIdx++] = 128 | u >> 6 & 63;
        outU8Array[outIdx++] = 128 | u & 63;
      } else {
        if (outIdx + 3 >= endIdx) { break; }
        outU8Array[outIdx++] = 240 | u >> 18;
        outU8Array[outIdx++] = 128 | u >> 12 & 63;
        outU8Array[outIdx++] = 128 | u >> 6 & 63;
        outU8Array[outIdx++] = 128 | u & 63;
      }
    }
    outU8Array[outIdx] = 0;
    return outIdx - startIdx;
  }
  function stringToUTF8(str, outPtr, maxBytesToWrite) {
    return stringToUTF8Array(str, HEAPU8, outPtr, maxBytesToWrite);
  }
  var UTF16Decoder = typeof TextDecoder !== "undefined" ? new TextDecoder("utf-16le") : undefined;
  function writeArrayToMemory(array, buffer) {
    HEAP8.set(array, buffer);
  }
  function alignUp(x, multiple) {
    if (x % multiple > 0) {
      x += multiple - x % multiple;
    }
    return x;
  }
  var buffer, HEAP8, HEAPU8, HEAP16, HEAPU16, HEAP32, HEAPU32, HEAPF32, HEAPF64;
  function updateGlobalBufferAndViews(buf) {
    buffer = buf;
    Module["HEAP8"] = HEAP8 = new Int8Array(buf);
    Module["HEAP16"] = HEAP16 = new Int16Array(buf);
    Module["HEAP32"] = HEAP32 = new Int32Array(buf);
    Module["HEAPU8"] = HEAPU8 = new Uint8Array(buf);
    Module["HEAPU16"] = HEAPU16 = new Uint16Array(buf);
    Module["HEAPU32"] = HEAPU32 = new Uint32Array(buf);
    Module["HEAPF32"] = HEAPF32 = new Float32Array(buf);
    Module["HEAPF64"] = HEAPF64 = new Float64Array(buf);
  }
  var DYNAMIC_BASE = 5271520,
    DYNAMICTOP_PTR = 28608;
  var INITIAL_TOTAL_MEMORY = Module["TOTAL_MEMORY"] || 33554432;
  if (Module["buffer"]) {
    buffer = Module["buffer"];
  } else {
    buffer = new ArrayBuffer(INITIAL_TOTAL_MEMORY);
  }
  INITIAL_TOTAL_MEMORY = buffer.byteLength;
  updateGlobalBufferAndViews(buffer);
  HEAP32[DYNAMICTOP_PTR >> 2] = DYNAMIC_BASE;
  function callRuntimeCallbacks(callbacks) {
    while (callbacks.length > 0) {
      var callback = callbacks.shift();
      if (typeof callback == "function") {
        callback();
        continue;
      }
      var func = callback.func;
      if (typeof func === "number") {
        if (callback.arg === undefined) {
          Module["dynCall_v"](func);
        } else {
          Module["dynCall_vi"](func, callback.arg);
        }
      } else {
        func(callback.arg === undefined ? null : callback.arg);
      }
    }
  }
  var __ATPRERUN__ = [];
  var __ATINIT__ = [];
  var __ATMAIN__ = [];
  var __ATPOSTRUN__ = [];
  function preRun() {
    if (Module["preRun"]) {
      if (typeof Module["preRun"] == "function") { Module["preRun"] = [Module["preRun"]]; }
      while (Module["preRun"].length) {
        addOnPreRun(Module["preRun"].shift());
      }
    }
    callRuntimeCallbacks(__ATPRERUN__);
  }
  function initRuntime() {
    callRuntimeCallbacks(__ATINIT__);
  }
  function preMain() {
    callRuntimeCallbacks(__ATMAIN__);
  }
  function postRun() {
    if (Module["postRun"]) {
      if (typeof Module["postRun"] == "function") { Module["postRun"] = [Module["postRun"]]; }
      while (Module["postRun"].length) {
        addOnPostRun(Module["postRun"].shift());
      }
    }
    callRuntimeCallbacks(__ATPOSTRUN__);
  }
  function addOnPreRun(cb) {
    __ATPRERUN__.unshift(cb);
  }
  function addOnPostRun(cb) {
    __ATPOSTRUN__.unshift(cb);
  }
  var Math_abs = Math.abs;
  var Math_ceil = Math.ceil;
  var Math_floor = Math.floor;
  var Math_min = Math.min;
  var runDependencies = 0;
  var runDependencyWatcher = null;
  var dependenciesFulfilled = null;
  function addRunDependency(id) {
    runDependencies++;
    if (Module["monitorRunDependencies"]) {
      Module["monitorRunDependencies"](runDependencies);
    }
  }
  function removeRunDependency(id) {
    runDependencies--;
    if (Module["monitorRunDependencies"]) {
      Module["monitorRunDependencies"](runDependencies);
    }
    if (runDependencies == 0) {
      if (runDependencyWatcher !== null) {
        clearInterval(runDependencyWatcher);
        runDependencyWatcher = null;
      }
      if (dependenciesFulfilled) {
        var callback = dependenciesFulfilled;
        dependenciesFulfilled = null;
        callback();
      }
    }
  }
  Module["preloadedImages"] = {};
  Module["preloadedAudios"] = {};
  var memoryInitializer = null;
  var dataURIPrefix = "data:application/octet-stream;base64,";
  function isDataURI(filename) {
    return String.prototype.startsWith ? filename.startsWith(dataURIPrefix) : filename.indexOf(dataURIPrefix) === 0;
  }
  var tempDouble;
  var tempI64;
  memoryInitializer = "data:application/octet-stream;base64,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";
  var tempDoublePtr = 28624;
  function demangle(func) {
    return func;
  }
  function demangleAll(text) {
    var regex = /\b__Z[\w\d_]+/g;
    return text.replace(regex, function (x) {
      var y = demangle(x);
      return x === y ? x : y + " [" + x + "]";
    });
  }
  function jsStackTrace() {
    var err = new Error();
    if (!err.stack) {
      try {
        throw new Error(0);
      } catch (e) {
        err = e;
      }
      if (!err.stack) {
        return "(no stack trace available)";
      }
    }
    return err.stack.toString();
  }
  function stackTrace() {
    var js = jsStackTrace();
    if (Module["extraStackTrace"]) { js += "\n" + Module["extraStackTrace"](); }
    return demangleAll(js);
  }
  function ___assert_fail(condition, filename, line, func) {
    abort("Assertion failed: " + UTF8ToString(condition) + ", at: " + [filename ? UTF8ToString(filename) : "unknown filename", line, func ? UTF8ToString(func) : "unknown function"]);
  }
  function _emscripten_get_heap_size() {
    return HEAP8.length;
  }
  function _emscripten_memcpy_big(dest, src, num) {
    HEAPU8.set(HEAPU8.subarray(src, src + num), dest);
  }
  function ___setErrNo(value) {
    if (Module["___errno_location"]) { HEAP32[Module["___errno_location"]() >> 2] = value; }
    return value;
  }
  function abortOnCannotGrowMemory(requestedSize) {
    abort("OOM");
  }
  function emscripten_realloc_buffer(size) {
    try {
      var newBuffer = new ArrayBuffer(size);
      if (newBuffer.byteLength != size) { return; }
      new Int8Array(newBuffer).set(HEAP8);
      _emscripten_replace_memory(newBuffer);
      updateGlobalBufferAndViews(newBuffer);
      return 1;
    } catch (e) {}
  }
  function _emscripten_resize_heap(requestedSize) {
    var oldSize = _emscripten_get_heap_size();
    var PAGE_MULTIPLE = 16777216;
    var LIMIT = 2147483648 - PAGE_MULTIPLE;
    if (requestedSize > LIMIT) {
      return false;
    }
    var MIN_TOTAL_MEMORY = 16777216;
    var newSize = Math.max(oldSize, MIN_TOTAL_MEMORY);
    while (newSize < requestedSize) {
      if (newSize <= 536870912) {
        newSize = alignUp(2 * newSize, PAGE_MULTIPLE);
      } else {
        newSize = Math.min(alignUp((3 * newSize + 2147483648) / 4, PAGE_MULTIPLE), LIMIT);
      }
    }
    var replacement = emscripten_realloc_buffer(newSize);
    if (!replacement) {
      return false;
    }
    return true;
  }
  function intArrayToString(array) {
    var ret = [];
    for (var i = 0; i < array.length; i++) {
      var chr = array[i];
      if (chr > 255) {
        chr &= 255;
      }
      ret.push(String.fromCharCode(chr));
    }
    return ret.join("");
  }
  var decodeBase64 = typeof atob === "function" ? atob : function (input) {
    var keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
    var output = "";
    var chr1, chr2, chr3;
    var enc1, enc2, enc3, enc4;
    var i = 0;
    input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");
    do {
      enc1 = keyStr.indexOf(input.charAt(i++));
      enc2 = keyStr.indexOf(input.charAt(i++));
      enc3 = keyStr.indexOf(input.charAt(i++));
      enc4 = keyStr.indexOf(input.charAt(i++));
      chr1 = enc1 << 2 | enc2 >> 4;
      chr2 = (enc2 & 15) << 4 | enc3 >> 2;
      chr3 = (enc3 & 3) << 6 | enc4;
      output = output + String.fromCharCode(chr1);
      if (enc3 !== 64) {
        output = output + String.fromCharCode(chr2);
      }
      if (enc4 !== 64) {
        output = output + String.fromCharCode(chr3);
      }
    } while (i < input.length);
    return output;
  };
  function intArrayFromBase64(s) {
    if (typeof ENVIRONMENT_IS_NODE === "boolean" && ENVIRONMENT_IS_NODE) {
      var buf;
      try {
        buf = Buffer.from(s, "base64");
      } catch (_) {
        buf = new Buffer(s, "base64");
      }
      return new Uint8Array(buf.buffer, buf.byteOffset, buf.byteLength);
    }
    try {
      var decoded = decodeBase64(s);
      var bytes = new Uint8Array(decoded.length);
      for (var i = 0; i < decoded.length; ++i) {
        bytes[i] = decoded.charCodeAt(i);
      }
      return bytes;
    } catch (_) {
      throw new Error("Converting base64 string to bytes failed.");
    }
  }
  function tryParseAsDataURI(filename) {
    if (!isDataURI(filename)) {
      return;
    }
    return intArrayFromBase64(filename.slice(dataURIPrefix.length));
  }
  var asmGlobalArg = {
    "Math": Math,
    "Int8Array": Int8Array,
    "Int32Array": Int32Array,
    "Uint8Array": Uint8Array,
    "Float32Array": Float32Array,
    "Float64Array": Float64Array
  };
  var asmLibraryArg = {
    "a": abort,
    "b": setTempRet0,
    "c": getTempRet0,
    "d": ___assert_fail,
    "e": ___setErrNo,
    "f": _emscripten_get_heap_size,
    "g": _emscripten_memcpy_big,
    "h": _emscripten_resize_heap,
    "i": abortOnCannotGrowMemory,
    "j": demangle,
    "k": demangleAll,
    "l": emscripten_realloc_buffer,
    "m": jsStackTrace,
    "n": stackTrace,
    "o": tempDoublePtr,
    "p": DYNAMICTOP_PTR
  }; // EMSCRIPTEN_START_ASM
  var asm = (/** @suppress {uselessCode} */function (global, env, buffer) {
    "almost asm";

    var a = new global.Int8Array(buffer),
      b = new global.Int32Array(buffer),
      c = new global.Uint8Array(buffer),
      d = new global.Float32Array(buffer),
      e = new global.Float64Array(buffer),
      f = env.o | 0,
      g = env.p | 0,
      p = global.Math.floor,
      q = global.Math.abs,
      r = global.Math.sqrt,
      s = global.Math.pow,
      t = global.Math.cos,
      u = global.Math.sin,
      v = global.Math.tan,
      w = global.Math.acos,
      x = global.Math.asin,
      y = global.Math.atan,
      z = global.Math.atan2,
      A = global.Math.ceil,
      B = global.Math.imul,
      C = global.Math.min,
      D = global.Math.max,
      E = global.Math.clz32,
      G = env.b,
      H = env.c,
      I = env.d,
      J = env.e,
      K = env.f,
      L = env.g,
      M = env.h,
      N = env.i,
      T = 28640;
    function W(newBuffer) {
      a = new Int8Array(newBuffer);
      c = new Uint8Array(newBuffer);
      b = new Int32Array(newBuffer);
      d = new Float32Array(newBuffer);
      e = new Float64Array(newBuffer);
      buffer = newBuffer;
      return true;
    }
    // EMSCRIPTEN_START_FUNCS
    function X(a) {
      a = a | 0;
      var b = 0;
      b = T;
      T = T + a | 0;
      T = T + 15 & -16;
      return b | 0;
    }
    function Y() {
      return T | 0;
    }
    function Z(a) {
      a = a | 0;
      T = a;
    }
    function _(a, b) {
      a = a | 0;
      b = b | 0;
      T = a;
    }
    function $(a, c) {
      a = a | 0;
      c = c | 0;
      var d = 0,
        e = 0,
        f = 0;
      if ((a | 0) < 0) {
        c = 2;
        return c | 0;
      }
      if ((a | 0) > 13780509) {
        c = sc(15, c) | 0;
        return c | 0;
      } else {
        d = ((a | 0) < 0) << 31 >> 31;
        f = Kd(a | 0, d | 0, 3, 0) | 0;
        e = H() | 0;
        d = Ed(a | 0, d | 0, 1, 0) | 0;
        d = Kd(f | 0, e | 0, d | 0, H() | 0) | 0;
        d = Ed(d | 0, H() | 0, 1, 0) | 0;
        a = H() | 0;
        b[c >> 2] = d;
        b[c + 4 >> 2] = a;
        c = 0;
        return c | 0;
      }
      return 0;
    }
    function aa(a, b, c, d) {
      a = a | 0;
      b = b | 0;
      c = c | 0;
      d = d | 0;
      return ba(a, b, c, d, 0) | 0;
    }
    function ba(a, c, d, e, f) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      f = f | 0;
      var g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0;
      j = T;
      T = T + 16 | 0;
      h = j;
      if (!(ca(a, c, d, e, f) | 0)) {
        e = 0;
        T = j;
        return e | 0;
      }
      do { if ((d | 0) >= 0) {
        if ((d | 0) > 13780509) {
          g = sc(15, h) | 0;
          if (g | 0) { break; }
          i = h;
          h = b[i >> 2] | 0;
          i = b[i + 4 >> 2] | 0;
        } else {
          g = ((d | 0) < 0) << 31 >> 31;
          k = Kd(d | 0, g | 0, 3, 0) | 0;
          i = H() | 0;
          g = Ed(d | 0, g | 0, 1, 0) | 0;
          g = Kd(k | 0, i | 0, g | 0, H() | 0) | 0;
          g = Ed(g | 0, H() | 0, 1, 0) | 0;
          i = H() | 0;
          b[h >> 2] = g;
          b[h + 4 >> 2] = i;
          h = g;
        }
        Vd(e | 0, 0, h << 3 | 0) | 0;
        if (f | 0) {
          Vd(f | 0, 0, h << 2 | 0) | 0;
          g = da(a, c, d, e, f, h, i, 0) | 0;
          break;
        }
        g = Dd(h, 4) | 0;
        if (!g) { g = 13; }else {
          k = da(a, c, d, e, g, h, i, 0) | 0;
          Cd(g);
          g = k;
        }
      } else { g = 2; } } while (0);
      k = g;
      T = j;
      return k | 0;
    }
    function ca(a, c, d, e, f) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      f = f | 0;
      var g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0,
        m = 0,
        n = 0,
        o = 0,
        p = 0,
        q = 0;
      q = T;
      T = T + 16 | 0;
      o = q;
      p = q + 8 | 0;
      n = o;
      b[n >> 2] = a;
      b[n + 4 >> 2] = c;
      if ((d | 0) < 0) {
        p = 2;
        T = q;
        return p | 0;
      }
      g = e;
      b[g >> 2] = a;
      b[g + 4 >> 2] = c;
      g = (f | 0) != 0;
      if (g) { b[f >> 2] = 0; }
      if (Fb(a, c) | 0) {
        p = 9;
        T = q;
        return p | 0;
      }
      b[p >> 2] = 0;
      a: do { if ((d | 0) >= 1) {
        if (g) {
          l = 1;
          k = 0;
          m = 0;
          n = 1;
          g = a;
          while (1) {
            if (!(k | m)) {
              g = ea(g, c, 4, p, o) | 0;
              if (g | 0) { break a; }
              c = o;
              g = b[c >> 2] | 0;
              c = b[c + 4 >> 2] | 0;
              if (Fb(g, c) | 0) {
                g = 9;
                break a;
              }
            }
            g = ea(g, c, b[26800 + (m << 2) >> 2] | 0, p, o) | 0;
            if (g | 0) { break a; }
            c = o;
            g = b[c >> 2] | 0;
            c = b[c + 4 >> 2] | 0;
            a = e + (l << 3) | 0;
            b[a >> 2] = g;
            b[a + 4 >> 2] = c;
            b[f + (l << 2) >> 2] = n;
            a = k + 1 | 0;
            h = (a | 0) == (n | 0);
            i = m + 1 | 0;
            j = (i | 0) == 6;
            if (Fb(g, c) | 0) {
              g = 9;
              break a;
            }
            n = n + (j & h & 1) | 0;
            if ((n | 0) > (d | 0)) {
              g = 0;
              break;
            } else {
              l = l + 1 | 0;
              k = h ? 0 : a;
              m = h ? j ? 0 : i : m;
            }
          }
        } else {
          l = 1;
          k = 0;
          m = 0;
          n = 1;
          g = a;
          while (1) {
            if (!(k | m)) {
              g = ea(g, c, 4, p, o) | 0;
              if (g | 0) { break a; }
              c = o;
              g = b[c >> 2] | 0;
              c = b[c + 4 >> 2] | 0;
              if (Fb(g, c) | 0) {
                g = 9;
                break a;
              }
            }
            g = ea(g, c, b[26800 + (m << 2) >> 2] | 0, p, o) | 0;
            if (g | 0) { break a; }
            c = o;
            g = b[c >> 2] | 0;
            c = b[c + 4 >> 2] | 0;
            a = e + (l << 3) | 0;
            b[a >> 2] = g;
            b[a + 4 >> 2] = c;
            a = k + 1 | 0;
            h = (a | 0) == (n | 0);
            i = m + 1 | 0;
            j = (i | 0) == 6;
            if (Fb(g, c) | 0) {
              g = 9;
              break a;
            }
            n = n + (j & h & 1) | 0;
            if ((n | 0) > (d | 0)) {
              g = 0;
              break;
            } else {
              l = l + 1 | 0;
              k = h ? 0 : a;
              m = h ? j ? 0 : i : m;
            }
          }
        }
      } else { g = 0; } } while (0);
      p = g;
      T = q;
      return p | 0;
    }
    function da(a, c, d, e, f, g, h, i) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      f = f | 0;
      g = g | 0;
      h = h | 0;
      i = i | 0;
      var j = 0,
        k = 0,
        l = 0,
        m = 0,
        n = 0,
        o = 0,
        p = 0,
        q = 0,
        r = 0,
        s = 0;
      q = T;
      T = T + 16 | 0;
      o = q + 8 | 0;
      p = q;
      j = Md(a | 0, c | 0, g | 0, h | 0) | 0;
      l = H() | 0;
      m = e + (j << 3) | 0;
      r = m;
      s = b[r >> 2] | 0;
      r = b[r + 4 >> 2] | 0;
      k = (s | 0) == (a | 0) & (r | 0) == (c | 0);
      if (!((s | 0) == 0 & (r | 0) == 0 | k)) { do {
        j = Ed(j | 0, l | 0, 1, 0) | 0;
        j = Ld(j | 0, H() | 0, g | 0, h | 0) | 0;
        l = H() | 0;
        m = e + (j << 3) | 0;
        s = m;
        r = b[s >> 2] | 0;
        s = b[s + 4 >> 2] | 0;
        k = (r | 0) == (a | 0) & (s | 0) == (c | 0);
      } while (!((r | 0) == 0 & (s | 0) == 0 | k)); }
      j = f + (j << 2) | 0;
      if (k ? (b[j >> 2] | 0) <= (i | 0) : 0) {
        s = 0;
        T = q;
        return s | 0;
      }
      s = m;
      b[s >> 2] = a;
      b[s + 4 >> 2] = c;
      b[j >> 2] = i;
      if ((i | 0) >= (d | 0)) {
        s = 0;
        T = q;
        return s | 0;
      }
      k = i + 1 | 0;
      b[o >> 2] = 0;
      j = ea(a, c, 2, o, p) | 0;
      switch (j | 0) {
        case 9:
          {
            n = 9;
            break;
          }
        case 0:
          {
            j = p;
            j = da(b[j >> 2] | 0, b[j + 4 >> 2] | 0, d, e, f, g, h, k) | 0;
            if (!j) { n = 9; }
            break;
          }
        default:

      }
      a: do { if ((n | 0) == 9) {
        b[o >> 2] = 0;
        j = ea(a, c, 3, o, p) | 0;
        switch (j | 0) {
          case 9:
            break;
          case 0:
            {
              j = p;
              j = da(b[j >> 2] | 0, b[j + 4 >> 2] | 0, d, e, f, g, h, k) | 0;
              if (j | 0) { break a; }
              break;
            }
          default:
            break a;
        }
        b[o >> 2] = 0;
        j = ea(a, c, 1, o, p) | 0;
        switch (j | 0) {
          case 9:
            break;
          case 0:
            {
              j = p;
              j = da(b[j >> 2] | 0, b[j + 4 >> 2] | 0, d, e, f, g, h, k) | 0;
              if (j | 0) { break a; }
              break;
            }
          default:
            break a;
        }
        b[o >> 2] = 0;
        j = ea(a, c, 5, o, p) | 0;
        switch (j | 0) {
          case 9:
            break;
          case 0:
            {
              j = p;
              j = da(b[j >> 2] | 0, b[j + 4 >> 2] | 0, d, e, f, g, h, k) | 0;
              if (j | 0) { break a; }
              break;
            }
          default:
            break a;
        }
        b[o >> 2] = 0;
        j = ea(a, c, 4, o, p) | 0;
        switch (j | 0) {
          case 9:
            break;
          case 0:
            {
              j = p;
              j = da(b[j >> 2] | 0, b[j + 4 >> 2] | 0, d, e, f, g, h, k) | 0;
              if (j | 0) { break a; }
              break;
            }
          default:
            break a;
        }
        b[o >> 2] = 0;
        j = ea(a, c, 6, o, p) | 0;
        switch (j | 0) {
          case 9:
            break;
          case 0:
            {
              j = p;
              j = da(b[j >> 2] | 0, b[j + 4 >> 2] | 0, d, e, f, g, h, k) | 0;
              if (j | 0) { break a; }
              break;
            }
          default:
            break a;
        }
        s = 0;
        T = q;
        return s | 0;
      } } while (0);
      s = j;
      T = q;
      return s | 0;
    }
    function ea(a, c, d, e, f) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      f = f | 0;
      var g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0,
        m = 0,
        n = 0,
        o = 0,
        p = 0;
      if (d >>> 0 > 6) {
        f = 1;
        return f | 0;
      }
      m = (b[e >> 2] | 0) % 6 | 0;
      b[e >> 2] = m;
      if ((m | 0) > 0) {
        g = 0;
        do {
          d = Za(d) | 0;
          g = g + 1 | 0;
        } while ((g | 0) < (b[e >> 2] | 0));
      }
      m = Od(a | 0, c | 0, 45) | 0;
      H() | 0;
      l = m & 127;
      if (l >>> 0 > 121) {
        f = 5;
        return f | 0;
      }
      j = Nb(a, c) | 0;
      g = Od(a | 0, c | 0, 52) | 0;
      H() | 0;
      g = g & 15;
      a: do { if (!g) { k = 8; }else {
        while (1) {
          h = (15 - g | 0) * 3 | 0;
          i = Od(a | 0, c | 0, h | 0) | 0;
          H() | 0;
          i = i & 7;
          if ((i | 0) == 7) {
            c = 5;
            break;
          }
          p = (Tb(g) | 0) == 0;
          g = g + -1 | 0;
          n = Pd(7, 0, h | 0) | 0;
          c = c & ~(H() | 0);
          o = Pd(b[(p ? 432 : 16) + (i * 28 | 0) + (d << 2) >> 2] | 0, 0, h | 0) | 0;
          h = H() | 0;
          d = b[(p ? 640 : 224) + (i * 28 | 0) + (d << 2) >> 2] | 0;
          a = o | a & ~n;
          c = h | c;
          if (!d) {
            d = 0;
            break a;
          }
          if (!g) {
            k = 8;
            break a;
          }
        }
        return c | 0;
      } } while (0);
      if ((k | 0) == 8) {
        p = b[848 + (l * 28 | 0) + (d << 2) >> 2] | 0;
        o = Pd(p | 0, 0, 45) | 0;
        a = o | a;
        c = H() | 0 | c & -1040385;
        d = b[4272 + (l * 28 | 0) + (d << 2) >> 2] | 0;
        if ((p & 127 | 0) == 127) {
          p = Pd(b[848 + (l * 28 | 0) + 20 >> 2] | 0, 0, 45) | 0;
          c = H() | 0 | c & -1040385;
          d = b[4272 + (l * 28 | 0) + 20 >> 2] | 0;
          a = Pb(p | a, c) | 0;
          c = H() | 0;
          b[e >> 2] = (b[e >> 2] | 0) + 1;
        }
      }
      i = Od(a | 0, c | 0, 45) | 0;
      H() | 0;
      i = i & 127;
      b: do { if (!(ma(i) | 0)) {
        if ((d | 0) > 0) {
          g = 0;
          do {
            a = Pb(a, c) | 0;
            c = H() | 0;
            g = g + 1 | 0;
          } while ((g | 0) != (d | 0));
        }
      } else {
        c: do { if ((Nb(a, c) | 0) == 1) {
          if ((l | 0) != (i | 0)) { if (sa(i, b[7696 + (l * 28 | 0) >> 2] | 0) | 0) {
            a = Rb(a, c) | 0;
            h = 1;
            c = H() | 0;
            break;
          } else { I(27795, 26864, 436, 26872); } }
          switch (j | 0) {
            case 3:
              {
                a = Pb(a, c) | 0;
                c = H() | 0;
                b[e >> 2] = (b[e >> 2] | 0) + 1;
                h = 0;
                break c;
              }
            case 5:
              {
                a = Rb(a, c) | 0;
                c = H() | 0;
                b[e >> 2] = (b[e >> 2] | 0) + 5;
                h = 0;
                break c;
              }
            case 0:
              {
                p = 9;
                return p | 0;
              }
            default:
              {
                p = 1;
                return p | 0;
              }
          }
        } else { h = 0; } } while (0);
        if ((d | 0) > 0) {
          g = 0;
          do {
            a = Ob(a, c) | 0;
            c = H() | 0;
            g = g + 1 | 0;
          } while ((g | 0) != (d | 0));
        }
        if ((l | 0) != (i | 0)) {
          if (!(na(i) | 0)) {
            if ((h | 0) != 0 | (Nb(a, c) | 0) != 5) { break; }
            b[e >> 2] = (b[e >> 2] | 0) + 1;
            break;
          }
          switch (m & 127) {
            case 8:
            case 118:
              break b;
            default:

          }
          if ((Nb(a, c) | 0) != 3) { b[e >> 2] = (b[e >> 2] | 0) + 1; }
        }
      } } while (0);
      b[e >> 2] = ((b[e >> 2] | 0) + d | 0) % 6 | 0;
      p = f;
      b[p >> 2] = a;
      b[p + 4 >> 2] = c;
      p = 0;
      return p | 0;
    }
    function fa(a, c, d, e) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      var f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0;
      i = T;
      T = T + 16 | 0;
      g = i;
      h = i + 8 | 0;
      f = (Fb(a, c) | 0) == 0;
      f = f ? 1 : 2;
      while (1) {
        b[h >> 2] = 0;
        k = (ea(a, c, f, h, g) | 0) == 0;
        j = g;
        if (k & ((b[j >> 2] | 0) == (d | 0) ? (b[j + 4 >> 2] | 0) == (e | 0) : 0)) {
          a = 4;
          break;
        }
        f = f + 1 | 0;
        if (f >>> 0 >= 7) {
          f = 7;
          a = 4;
          break;
        }
      }
      if ((a | 0) == 4) {
        T = i;
        return f | 0;
      }
      return 0;
    }
    function ga(a, c, d, e) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      var f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0,
        m = 0,
        n = 0,
        o = 0,
        p = 0;
      p = T;
      T = T + 16 | 0;
      n = p;
      o = p + 8 | 0;
      m = n;
      b[m >> 2] = a;
      b[m + 4 >> 2] = c;
      if (!d) {
        o = e;
        b[o >> 2] = a;
        b[o + 4 >> 2] = c;
        o = 0;
        T = p;
        return o | 0;
      }
      b[o >> 2] = 0;
      a: do { if (!(Fb(a, c) | 0)) {
        g = (d | 0) > 0;
        if (g) {
          f = 0;
          m = a;
          do {
            a = ea(m, c, 4, o, n) | 0;
            if (a | 0) { break a; }
            c = n;
            m = b[c >> 2] | 0;
            c = b[c + 4 >> 2] | 0;
            f = f + 1 | 0;
            if (Fb(m, c) | 0) {
              a = 9;
              break a;
            }
          } while ((f | 0) < (d | 0));
          l = e;
          b[l >> 2] = m;
          b[l + 4 >> 2] = c;
          l = d + -1 | 0;
          if (g) {
            k = 0;
            a = 1;
            do {
              f = 26800 + (k << 2) | 0;
              if ((k | 0) == 5) {
                h = b[f >> 2] | 0;
                g = 0;
                f = a;
                while (1) {
                  a = n;
                  a = ea(b[a >> 2] | 0, b[a + 4 >> 2] | 0, h, o, n) | 0;
                  if (a | 0) { break a; }
                  if ((g | 0) != (l | 0)) {
                    j = n;
                    i = b[j >> 2] | 0;
                    j = b[j + 4 >> 2] | 0;
                    a = e + (f << 3) | 0;
                    b[a >> 2] = i;
                    b[a + 4 >> 2] = j;
                    if (!(Fb(i, j) | 0)) { a = f + 1 | 0; }else {
                      a = 9;
                      break a;
                    }
                  } else { a = f; }
                  g = g + 1 | 0;
                  if ((g | 0) >= (d | 0)) { break; }else { f = a; }
                }
              } else {
                h = n;
                j = b[f >> 2] | 0;
                i = 0;
                f = a;
                g = b[h >> 2] | 0;
                h = b[h + 4 >> 2] | 0;
                while (1) {
                  a = ea(g, h, j, o, n) | 0;
                  if (a | 0) { break a; }
                  h = n;
                  g = b[h >> 2] | 0;
                  h = b[h + 4 >> 2] | 0;
                  a = e + (f << 3) | 0;
                  b[a >> 2] = g;
                  b[a + 4 >> 2] = h;
                  a = f + 1 | 0;
                  if (Fb(g, h) | 0) {
                    a = 9;
                    break a;
                  }
                  i = i + 1 | 0;
                  if ((i | 0) >= (d | 0)) { break; }else { f = a; }
                }
              }
              k = k + 1 | 0;
            } while (k >>> 0 < 6);
            a = n;
            h = m;
            f = b[a >> 2] | 0;
            g = c;
            a = b[a + 4 >> 2] | 0;
          } else {
            h = m;
            f = m;
            g = c;
            a = c;
          }
        } else {
          h = e;
          b[h >> 2] = a;
          b[h + 4 >> 2] = c;
          h = a;
          f = a;
          g = c;
          a = c;
        }
        a = (h | 0) == (f | 0) & (g | 0) == (a | 0) ? 0 : 9;
      } else { a = 9; } } while (0);
      o = a;
      T = p;
      return o | 0;
    }
    function ha(a, c, d, e) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      var f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0;
      i = T;
      T = T + 48 | 0;
      f = i + 16 | 0;
      g = i + 8 | 0;
      h = i;
      d = Vc(d) | 0;
      if (d | 0) {
        h = d;
        T = i;
        return h | 0;
      }
      k = a;
      j = b[k + 4 >> 2] | 0;
      d = g;
      b[d >> 2] = b[k >> 2];
      b[d + 4 >> 2] = j;
      Uc(g, f);
      d = Fa(f, c, h) | 0;
      if (!d) {
        c = b[g >> 2] | 0;
        g = b[a + 8 >> 2] | 0;
        if ((g | 0) > 0) {
          f = b[a + 12 >> 2] | 0;
          d = 0;
          do {
            c = (b[f + (d << 3) >> 2] | 0) + c | 0;
            d = d + 1 | 0;
          } while ((d | 0) < (g | 0));
        }
        d = h;
        f = b[d >> 2] | 0;
        d = b[d + 4 >> 2] | 0;
        g = ((c | 0) < 0) << 31 >> 31;
        if ((d | 0) < (g | 0) | (d | 0) == (g | 0) & f >>> 0 < c >>> 0) {
          d = h;
          b[d >> 2] = c;
          b[d + 4 >> 2] = g;
          d = g;
        } else { c = f; }
        j = Ed(c | 0, d | 0, 12, 0) | 0;
        k = H() | 0;
        d = h;
        b[d >> 2] = j;
        b[d + 4 >> 2] = k;
        d = e;
        b[d >> 2] = j;
        b[d + 4 >> 2] = k;
        d = 0;
      }
      k = d;
      T = i;
      return k | 0;
    }
    function ia(a, c, d, f, g, h, i) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      f = f | 0;
      g = g | 0;
      h = h | 0;
      i = i | 0;
      var j = 0,
        k = 0,
        l = 0,
        m = 0,
        n = 0,
        o = 0,
        p = 0,
        q = 0,
        r = 0,
        s = 0,
        t = 0,
        u = 0,
        v = 0,
        w = 0,
        x = 0,
        y = 0,
        z = 0,
        A = 0,
        B = 0,
        C = 0,
        D = 0,
        E = 0,
        F = 0,
        G = 0,
        I = 0,
        J = 0.0,
        K = 0.0,
        L = 0.0,
        M = 0.0;
      I = T;
      T = T + 64 | 0;
      D = I + 48 | 0;
      E = I + 32 | 0;
      F = I + 24 | 0;
      x = I + 8 | 0;
      y = I;
      k = b[a >> 2] | 0;
      if ((k | 0) <= 0) {
        G = 0;
        T = I;
        return G | 0;
      }
      z = a + 4 | 0;
      A = D + 8 | 0;
      B = E + 8 | 0;
      C = x + 8 | 0;
      j = 0;
      v = 0;
      while (1) {
        l = b[z >> 2] | 0;
        u = l + (v << 4) | 0;
        b[D >> 2] = b[u >> 2];
        b[D + 4 >> 2] = b[u + 4 >> 2];
        b[D + 8 >> 2] = b[u + 8 >> 2];
        b[D + 12 >> 2] = b[u + 12 >> 2];
        if ((v | 0) == (k + -1 | 0)) {
          b[E >> 2] = b[l >> 2];
          b[E + 4 >> 2] = b[l + 4 >> 2];
          b[E + 8 >> 2] = b[l + 8 >> 2];
          b[E + 12 >> 2] = b[l + 12 >> 2];
        } else {
          u = l + (v + 1 << 4) | 0;
          b[E >> 2] = b[u >> 2];
          b[E + 4 >> 2] = b[u + 4 >> 2];
          b[E + 8 >> 2] = b[u + 8 >> 2];
          b[E + 12 >> 2] = b[u + 12 >> 2];
        }
        k = Ga(D, E, f, F) | 0;
        a: do { if (!k) {
          k = F;
          l = b[k >> 2] | 0;
          k = b[k + 4 >> 2] | 0;
          if ((k | 0) > 0 | (k | 0) == 0 & l >>> 0 > 0) {
            t = 0;
            u = 0;
            b: while (1) {
              K = 1.0 / (+(l >>> 0) + 4294967296.0 * +(k | 0));
              M = +e[D >> 3];
              k = Fd(l | 0, k | 0, t | 0, u | 0) | 0;
              L = +(k >>> 0) + 4294967296.0 * +(H() | 0);
              J = +(t >>> 0) + 4294967296.0 * +(u | 0);
              e[x >> 3] = K * (M * L) + K * (+e[E >> 3] * J);
              e[C >> 3] = K * (+e[A >> 3] * L) + K * (+e[B >> 3] * J);
              k = Ub(x, f, y) | 0;
              if (k | 0) {
                j = k;
                break;
              }
              s = y;
              r = b[s >> 2] | 0;
              s = b[s + 4 >> 2] | 0;
              o = Md(r | 0, s | 0, c | 0, d | 0) | 0;
              m = H() | 0;
              k = i + (o << 3) | 0;
              n = k;
              l = b[n >> 2] | 0;
              n = b[n + 4 >> 2] | 0;
              c: do { if ((l | 0) == 0 & (n | 0) == 0) {
                w = k;
                G = 16;
              } else {
                p = 0;
                q = 0;
                while (1) {
                  if ((p | 0) > (d | 0) | (p | 0) == (d | 0) & q >>> 0 > c >>> 0) {
                    j = 1;
                    break b;
                  }
                  if ((l | 0) == (r | 0) & (n | 0) == (s | 0)) { break c; }
                  k = Ed(o | 0, m | 0, 1, 0) | 0;
                  o = Ld(k | 0, H() | 0, c | 0, d | 0) | 0;
                  m = H() | 0;
                  q = Ed(q | 0, p | 0, 1, 0) | 0;
                  p = H() | 0;
                  k = i + (o << 3) | 0;
                  n = k;
                  l = b[n >> 2] | 0;
                  n = b[n + 4 >> 2] | 0;
                  if ((l | 0) == 0 & (n | 0) == 0) {
                    w = k;
                    G = 16;
                    break;
                  }
                }
              } } while (0);
              if ((G | 0) == 16 ? (G = 0, !((r | 0) == 0 & (s | 0) == 0)) : 0) {
                q = w;
                b[q >> 2] = r;
                b[q + 4 >> 2] = s;
                q = h + (b[g >> 2] << 3) | 0;
                b[q >> 2] = r;
                b[q + 4 >> 2] = s;
                q = g;
                q = Ed(b[q >> 2] | 0, b[q + 4 >> 2] | 0, 1, 0) | 0;
                r = H() | 0;
                s = g;
                b[s >> 2] = q;
                b[s + 4 >> 2] = r;
              }
              t = Ed(t | 0, u | 0, 1, 0) | 0;
              u = H() | 0;
              k = F;
              l = b[k >> 2] | 0;
              k = b[k + 4 >> 2] | 0;
              if (!((k | 0) > (u | 0) | (k | 0) == (u | 0) & l >>> 0 > t >>> 0)) {
                l = 1;
                break a;
              }
            }
            l = 0;
          } else { l = 1; }
        } else {
          l = 0;
          j = k;
        } } while (0);
        v = v + 1 | 0;
        if (!l) {
          G = 21;
          break;
        }
        k = b[a >> 2] | 0;
        if ((v | 0) >= (k | 0)) {
          j = 0;
          G = 21;
          break;
        }
      }
      if ((G | 0) == 21) {
        T = I;
        return j | 0;
      }
      return 0;
    }
    function ja(a, c, d, e) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      var f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0,
        m = 0,
        n = 0,
        o = 0,
        p = 0,
        q = 0,
        r = 0,
        s = 0,
        t = 0,
        u = 0,
        v = 0,
        w = 0,
        x = 0,
        y = 0,
        z = 0,
        A = 0,
        B = 0,
        C = 0,
        D = 0,
        E = 0,
        F = 0,
        G = 0,
        I = 0,
        J = 0,
        K = 0;
      K = T;
      T = T + 112 | 0;
      F = K + 80 | 0;
      j = K + 72 | 0;
      G = K;
      I = K + 56 | 0;
      f = Vc(d) | 0;
      if (f | 0) {
        J = f;
        T = K;
        return J | 0;
      }
      k = a + 8 | 0;
      J = Bd((b[k >> 2] << 5) + 32 | 0) | 0;
      if (!J) {
        J = 13;
        T = K;
        return J | 0;
      }
      Wc(a, J);
      f = Vc(d) | 0;
      if (!f) {
        D = a;
        E = b[D + 4 >> 2] | 0;
        f = j;
        b[f >> 2] = b[D >> 2];
        b[f + 4 >> 2] = E;
        Uc(j, F);
        f = Fa(F, c, G) | 0;
        if (!f) {
          f = b[j >> 2] | 0;
          g = b[k >> 2] | 0;
          if ((g | 0) > 0) {
            h = b[a + 12 >> 2] | 0;
            d = 0;
            do {
              f = (b[h + (d << 3) >> 2] | 0) + f | 0;
              d = d + 1 | 0;
            } while ((d | 0) != (g | 0));
            d = f;
          } else { d = f; }
          f = G;
          g = b[f >> 2] | 0;
          f = b[f + 4 >> 2] | 0;
          h = ((d | 0) < 0) << 31 >> 31;
          if ((f | 0) < (h | 0) | (f | 0) == (h | 0) & g >>> 0 < d >>> 0) {
            f = G;
            b[f >> 2] = d;
            b[f + 4 >> 2] = h;
            f = h;
          } else { d = g; }
          D = Ed(d | 0, f | 0, 12, 0) | 0;
          E = H() | 0;
          f = G;
          b[f >> 2] = D;
          b[f + 4 >> 2] = E;
          f = 0;
        } else {
          D = 0;
          E = 0;
        }
        if (!f) {
          d = Dd(D, 8) | 0;
          if (!d) {
            Cd(J);
            J = 13;
            T = K;
            return J | 0;
          }
          i = Dd(D, 8) | 0;
          if (!i) {
            Cd(J);
            Cd(d);
            J = 13;
            T = K;
            return J | 0;
          }
          B = F;
          b[B >> 2] = 0;
          b[B + 4 >> 2] = 0;
          B = a;
          C = b[B + 4 >> 2] | 0;
          f = j;
          b[f >> 2] = b[B >> 2];
          b[f + 4 >> 2] = C;
          f = ia(j, D, E, c, F, d, i) | 0;
          a: do { if (!f) {
            b: do { if ((b[k >> 2] | 0) > 0) {
              h = a + 12 | 0;
              g = 0;
              while (1) {
                f = ia((b[h >> 2] | 0) + (g << 3) | 0, D, E, c, F, d, i) | 0;
                g = g + 1 | 0;
                if (f | 0) { break; }
                if ((g | 0) >= (b[k >> 2] | 0)) { break b; }
              }
              Cd(d);
              Cd(i);
              Cd(J);
              break a;
            } } while (0);
            if ((E | 0) > 0 | (E | 0) == 0 & D >>> 0 > 0) { Vd(i | 0, 0, D << 3 | 0) | 0; }
            C = F;
            B = b[C + 4 >> 2] | 0;
            c: do { if ((B | 0) > 0 | (B | 0) == 0 & (b[C >> 2] | 0) >>> 0 > 0) {
              y = d;
              z = i;
              A = d;
              B = i;
              C = d;
              f = d;
              v = d;
              w = i;
              x = i;
              d = i;
              d: while (1) {
                r = 0;
                s = 0;
                t = 0;
                u = 0;
                g = 0;
                h = 0;
                while (1) {
                  i = G;
                  j = i + 56 | 0;
                  do {
                    b[i >> 2] = 0;
                    i = i + 4 | 0;
                  } while ((i | 0) < (j | 0));
                  c = y + (r << 3) | 0;
                  k = b[c >> 2] | 0;
                  c = b[c + 4 >> 2] | 0;
                  if (ca(k, c, 1, G, 0) | 0) {
                    i = G;
                    j = i + 56 | 0;
                    do {
                      b[i >> 2] = 0;
                      i = i + 4 | 0;
                    } while ((i | 0) < (j | 0));
                    i = Dd(7, 4) | 0;
                    if (i | 0) {
                      da(k, c, 1, G, i, 7, 0, 0) | 0;
                      Cd(i);
                    }
                  }
                  q = 0;
                  while (1) {
                    p = G + (q << 3) | 0;
                    o = b[p >> 2] | 0;
                    p = b[p + 4 >> 2] | 0;
                    e: do { if ((o | 0) == 0 & (p | 0) == 0) {
                      i = g;
                      j = h;
                    } else {
                      l = Md(o | 0, p | 0, D | 0, E | 0) | 0;
                      k = H() | 0;
                      i = e + (l << 3) | 0;
                      c = i;
                      j = b[c >> 2] | 0;
                      c = b[c + 4 >> 2] | 0;
                      if (!((j | 0) == 0 & (c | 0) == 0)) {
                        m = 0;
                        n = 0;
                        do {
                          if ((m | 0) > (E | 0) | (m | 0) == (E | 0) & n >>> 0 > D >>> 0) { break d; }
                          if ((j | 0) == (o | 0) & (c | 0) == (p | 0)) {
                            i = g;
                            j = h;
                            break e;
                          }
                          i = Ed(l | 0, k | 0, 1, 0) | 0;
                          l = Ld(i | 0, H() | 0, D | 0, E | 0) | 0;
                          k = H() | 0;
                          n = Ed(n | 0, m | 0, 1, 0) | 0;
                          m = H() | 0;
                          i = e + (l << 3) | 0;
                          c = i;
                          j = b[c >> 2] | 0;
                          c = b[c + 4 >> 2] | 0;
                        } while (!((j | 0) == 0 & (c | 0) == 0));
                      }
                      if ((o | 0) == 0 & (p | 0) == 0) {
                        i = g;
                        j = h;
                        break;
                      }
                      Xb(o, p, I) | 0;
                      if (Xc(a, J, I) | 0) {
                        n = Ed(g | 0, h | 0, 1, 0) | 0;
                        h = H() | 0;
                        m = i;
                        b[m >> 2] = o;
                        b[m + 4 >> 2] = p;
                        g = z + (g << 3) | 0;
                        b[g >> 2] = o;
                        b[g + 4 >> 2] = p;
                        g = n;
                      }
                      i = g;
                      j = h;
                    } } while (0);
                    q = q + 1 | 0;
                    if (q >>> 0 >= 7) { break; }else {
                      g = i;
                      h = j;
                    }
                  }
                  r = Ed(r | 0, s | 0, 1, 0) | 0;
                  s = H() | 0;
                  t = Ed(t | 0, u | 0, 1, 0) | 0;
                  u = H() | 0;
                  h = F;
                  g = b[h >> 2] | 0;
                  h = b[h + 4 >> 2] | 0;
                  if (!((u | 0) < (h | 0) | (u | 0) == (h | 0) & t >>> 0 < g >>> 0)) { break; }else {
                    g = i;
                    h = j;
                  }
                }
                if ((h | 0) > 0 | (h | 0) == 0 & g >>> 0 > 0) {
                  g = 0;
                  h = 0;
                  do {
                    u = y + (g << 3) | 0;
                    b[u >> 2] = 0;
                    b[u + 4 >> 2] = 0;
                    g = Ed(g | 0, h | 0, 1, 0) | 0;
                    h = H() | 0;
                    u = F;
                    t = b[u + 4 >> 2] | 0;
                  } while ((h | 0) < (t | 0) | ((h | 0) == (t | 0) ? g >>> 0 < (b[u >> 2] | 0) >>> 0 : 0));
                }
                u = F;
                b[u >> 2] = i;
                b[u + 4 >> 2] = j;
                if ((j | 0) > 0 | (j | 0) == 0 & i >>> 0 > 0) {
                  q = d;
                  r = x;
                  s = C;
                  t = w;
                  u = z;
                  d = v;
                  x = f;
                  w = A;
                  v = q;
                  f = r;
                  C = B;
                  B = s;
                  A = t;
                  z = y;
                  y = u;
                } else { break c; }
              }
              Cd(A);
              Cd(B);
              Cd(J);
              f = 1;
              break a;
            } else { f = i; } } while (0);
            Cd(J);
            Cd(d);
            Cd(f);
            f = 0;
          } else {
            Cd(d);
            Cd(i);
            Cd(J);
          } } while (0);
          J = f;
          T = K;
          return J | 0;
        }
      }
      Cd(J);
      J = f;
      T = K;
      return J | 0;
    }
    function ka(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0;
      l = T;
      T = T + 176 | 0;
      j = l;
      if ((c | 0) < 1) {
        sd(d, 0, 0);
        k = 0;
        T = l;
        return k | 0;
      }
      i = a;
      i = Od(b[i >> 2] | 0, b[i + 4 >> 2] | 0, 52) | 0;
      H() | 0;
      sd(d, (c | 0) > 6 ? c : 6, i & 15);
      i = 0;
      while (1) {
        e = a + (i << 3) | 0;
        e = Yb(b[e >> 2] | 0, b[e + 4 >> 2] | 0, j) | 0;
        if (e | 0) { break; }
        e = b[j >> 2] | 0;
        if ((e | 0) > 0) {
          h = 0;
          do {
            g = j + 8 + (h << 4) | 0;
            h = h + 1 | 0;
            e = j + 8 + (((h | 0) % (e | 0) | 0) << 4) | 0;
            f = xd(d, e, g) | 0;
            if (!f) { wd(d, g, e) | 0; }else { vd(d, f) | 0; }
            e = b[j >> 2] | 0;
          } while ((h | 0) < (e | 0));
        }
        i = i + 1 | 0;
        if ((i | 0) >= (c | 0)) {
          e = 0;
          k = 13;
          break;
        }
      }
      if ((k | 0) == 13) {
        T = l;
        return e | 0;
      }
      td(d);
      k = e;
      T = l;
      return k | 0;
    }
    function la(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var e = 0,
        f = 0,
        g = 0,
        h = 0;
      g = T;
      T = T + 32 | 0;
      e = g;
      f = g + 16 | 0;
      a = ka(a, c, f) | 0;
      if (a | 0) {
        d = a;
        T = g;
        return d | 0;
      }
      b[d >> 2] = 0;
      b[d + 4 >> 2] = 0;
      b[d + 8 >> 2] = 0;
      a = ud(f) | 0;
      if (a | 0) { do {
        c = Ac(d) | 0;
        do {
          Bc(c, a) | 0;
          h = a + 16 | 0;
          b[e >> 2] = b[h >> 2];
          b[e + 4 >> 2] = b[h + 4 >> 2];
          b[e + 8 >> 2] = b[h + 8 >> 2];
          b[e + 12 >> 2] = b[h + 12 >> 2];
          vd(f, a) | 0;
          a = yd(f, e) | 0;
        } while ((a | 0) != 0);
        a = ud(f) | 0;
      } while ((a | 0) != 0); }
      td(f);
      a = Dc(d) | 0;
      if (!a) {
        h = 0;
        T = g;
        return h | 0;
      }
      Cc(d);
      h = a;
      T = g;
      return h | 0;
    }
    function ma(a) {
      a = a | 0;
      if (a >>> 0 > 121) {
        a = 0;
        return a | 0;
      }
      a = b[7696 + (a * 28 | 0) + 16 >> 2] | 0;
      return a | 0;
    }
    function na(a) {
      a = a | 0;
      return (a | 0) == 4 | (a | 0) == 117 | 0;
    }
    function oa(a) {
      a = a | 0;
      return b[11120 + ((b[a >> 2] | 0) * 216 | 0) + ((b[a + 4 >> 2] | 0) * 72 | 0) + ((b[a + 8 >> 2] | 0) * 24 | 0) + (b[a + 12 >> 2] << 3) >> 2] | 0;
    }
    function pa(a) {
      a = a | 0;
      return b[11120 + ((b[a >> 2] | 0) * 216 | 0) + ((b[a + 4 >> 2] | 0) * 72 | 0) + ((b[a + 8 >> 2] | 0) * 24 | 0) + (b[a + 12 >> 2] << 3) + 4 >> 2] | 0;
    }
    function qa(a, c) {
      a = a | 0;
      c = c | 0;
      a = 7696 + (a * 28 | 0) | 0;
      b[c >> 2] = b[a >> 2];
      b[c + 4 >> 2] = b[a + 4 >> 2];
      b[c + 8 >> 2] = b[a + 8 >> 2];
      b[c + 12 >> 2] = b[a + 12 >> 2];
      return;
    }
    function ra(a, c) {
      a = a | 0;
      c = c | 0;
      var d = 0,
        e = 0;
      if (c >>> 0 > 20) {
        c = -1;
        return c | 0;
      }
      do { if ((b[11120 + (c * 216 | 0) >> 2] | 0) != (a | 0)) {
        if ((b[11120 + (c * 216 | 0) + 8 >> 2] | 0) != (a | 0)) {
          if ((b[11120 + (c * 216 | 0) + 16 >> 2] | 0) != (a | 0)) {
            if ((b[11120 + (c * 216 | 0) + 24 >> 2] | 0) != (a | 0)) {
              if ((b[11120 + (c * 216 | 0) + 32 >> 2] | 0) != (a | 0)) {
                if ((b[11120 + (c * 216 | 0) + 40 >> 2] | 0) != (a | 0)) {
                  if ((b[11120 + (c * 216 | 0) + 48 >> 2] | 0) != (a | 0)) {
                    if ((b[11120 + (c * 216 | 0) + 56 >> 2] | 0) != (a | 0)) {
                      if ((b[11120 + (c * 216 | 0) + 64 >> 2] | 0) != (a | 0)) {
                        if ((b[11120 + (c * 216 | 0) + 72 >> 2] | 0) != (a | 0)) {
                          if ((b[11120 + (c * 216 | 0) + 80 >> 2] | 0) != (a | 0)) {
                            if ((b[11120 + (c * 216 | 0) + 88 >> 2] | 0) != (a | 0)) {
                              if ((b[11120 + (c * 216 | 0) + 96 >> 2] | 0) != (a | 0)) {
                                if ((b[11120 + (c * 216 | 0) + 104 >> 2] | 0) != (a | 0)) {
                                  if ((b[11120 + (c * 216 | 0) + 112 >> 2] | 0) != (a | 0)) {
                                    if ((b[11120 + (c * 216 | 0) + 120 >> 2] | 0) != (a | 0)) {
                                      if ((b[11120 + (c * 216 | 0) + 128 >> 2] | 0) != (a | 0)) {
                                        if ((b[11120 + (c * 216 | 0) + 136 >> 2] | 0) == (a | 0)) {
                                          a = 2;
                                          d = 1;
                                          e = 2;
                                        } else {
                                          if ((b[11120 + (c * 216 | 0) + 144 >> 2] | 0) == (a | 0)) {
                                            a = 0;
                                            d = 2;
                                            e = 0;
                                            break;
                                          }
                                          if ((b[11120 + (c * 216 | 0) + 152 >> 2] | 0) == (a | 0)) {
                                            a = 0;
                                            d = 2;
                                            e = 1;
                                            break;
                                          }
                                          if ((b[11120 + (c * 216 | 0) + 160 >> 2] | 0) == (a | 0)) {
                                            a = 0;
                                            d = 2;
                                            e = 2;
                                            break;
                                          }
                                          if ((b[11120 + (c * 216 | 0) + 168 >> 2] | 0) == (a | 0)) {
                                            a = 1;
                                            d = 2;
                                            e = 0;
                                            break;
                                          }
                                          if ((b[11120 + (c * 216 | 0) + 176 >> 2] | 0) == (a | 0)) {
                                            a = 1;
                                            d = 2;
                                            e = 1;
                                            break;
                                          }
                                          if ((b[11120 + (c * 216 | 0) + 184 >> 2] | 0) == (a | 0)) {
                                            a = 1;
                                            d = 2;
                                            e = 2;
                                            break;
                                          }
                                          if ((b[11120 + (c * 216 | 0) + 192 >> 2] | 0) == (a | 0)) {
                                            a = 2;
                                            d = 2;
                                            e = 0;
                                            break;
                                          }
                                          if ((b[11120 + (c * 216 | 0) + 200 >> 2] | 0) == (a | 0)) {
                                            a = 2;
                                            d = 2;
                                            e = 1;
                                            break;
                                          }
                                          if ((b[11120 + (c * 216 | 0) + 208 >> 2] | 0) == (a | 0)) {
                                            a = 2;
                                            d = 2;
                                            e = 2;
                                            break;
                                          } else { a = -1; }
                                          return a | 0;
                                        }
                                      } else {
                                        a = 2;
                                        d = 1;
                                        e = 1;
                                      }
                                    } else {
                                      a = 2;
                                      d = 1;
                                      e = 0;
                                    }
                                  } else {
                                    a = 1;
                                    d = 1;
                                    e = 2;
                                  }
                                } else {
                                  a = 1;
                                  d = 1;
                                  e = 1;
                                }
                              } else {
                                a = 1;
                                d = 1;
                                e = 0;
                              }
                            } else {
                              a = 0;
                              d = 1;
                              e = 2;
                            }
                          } else {
                            a = 0;
                            d = 1;
                            e = 1;
                          }
                        } else {
                          a = 0;
                          d = 1;
                          e = 0;
                        }
                      } else {
                        a = 2;
                        d = 0;
                        e = 2;
                      }
                    } else {
                      a = 2;
                      d = 0;
                      e = 1;
                    }
                  } else {
                    a = 2;
                    d = 0;
                    e = 0;
                  }
                } else {
                  a = 1;
                  d = 0;
                  e = 2;
                }
              } else {
                a = 1;
                d = 0;
                e = 1;
              }
            } else {
              a = 1;
              d = 0;
              e = 0;
            }
          } else {
            a = 0;
            d = 0;
            e = 2;
          }
        } else {
          a = 0;
          d = 0;
          e = 1;
        }
      } else {
        a = 0;
        d = 0;
        e = 0;
      } } while (0);
      c = b[11120 + (c * 216 | 0) + (d * 72 | 0) + (a * 24 | 0) + (e << 3) + 4 >> 2] | 0;
      return c | 0;
    }
    function sa(a, c) {
      a = a | 0;
      c = c | 0;
      if ((b[7696 + (a * 28 | 0) + 20 >> 2] | 0) == (c | 0)) {
        c = 1;
        return c | 0;
      }
      c = (b[7696 + (a * 28 | 0) + 24 >> 2] | 0) == (c | 0);
      return c | 0;
    }
    function ta(a, c) {
      a = a | 0;
      c = c | 0;
      return b[848 + (a * 28 | 0) + (c << 2) >> 2] | 0;
    }
    function ua(a, c) {
      a = a | 0;
      c = c | 0;
      if ((b[848 + (a * 28 | 0) >> 2] | 0) == (c | 0)) {
        c = 0;
        return c | 0;
      }
      if ((b[848 + (a * 28 | 0) + 4 >> 2] | 0) == (c | 0)) {
        c = 1;
        return c | 0;
      }
      if ((b[848 + (a * 28 | 0) + 8 >> 2] | 0) == (c | 0)) {
        c = 2;
        return c | 0;
      }
      if ((b[848 + (a * 28 | 0) + 12 >> 2] | 0) == (c | 0)) {
        c = 3;
        return c | 0;
      }
      if ((b[848 + (a * 28 | 0) + 16 >> 2] | 0) == (c | 0)) {
        c = 4;
        return c | 0;
      }
      if ((b[848 + (a * 28 | 0) + 20 >> 2] | 0) == (c | 0)) {
        c = 5;
        return c | 0;
      } else { return ((b[848 + (a * 28 | 0) + 24 >> 2] | 0) == (c | 0) ? 6 : 7) | 0; }
      return 0;
    }
    function va() {
      return 122;
    }
    function wa(a) {
      a = a | 0;
      var c = 0,
        d = 0,
        e = 0;
      c = 0;
      do {
        Pd(c | 0, 0, 45) | 0;
        e = H() | 0 | 134225919;
        d = a + (c << 3) | 0;
        b[d >> 2] = -1;
        b[d + 4 >> 2] = e;
        c = c + 1 | 0;
      } while ((c | 0) != 122);
      return 0;
    }
    function xa(a) {
      a = a | 0;
      var b = 0.0,
        c = 0.0,
        d = 0.0;
      d = +e[a + 16 >> 3];
      c = +e[a + 24 >> 3];
      b = d - c;
      return +(d < c ? b + 6.283185307179586 : b);
    }
    function ya(a) {
      a = a | 0;
      return +e[a + 16 >> 3] < +e[a + 24 >> 3] | 0;
    }
    function za(a) {
      a = a | 0;
      return +(+e[a >> 3] - +e[a + 8 >> 3]);
    }
    function Aa(a, b) {
      a = a | 0;
      b = b | 0;
      var c = 0.0,
        d = 0.0,
        f = 0.0;
      c = +e[b >> 3];
      if (!(c >= +e[a + 8 >> 3])) {
        b = 0;
        return b | 0;
      }
      if (!(c <= +e[a >> 3])) {
        b = 0;
        return b | 0;
      }
      d = +e[a + 16 >> 3];
      c = +e[a + 24 >> 3];
      f = +e[b + 8 >> 3];
      b = f >= c;
      a = f <= d & 1;
      if (d < c) {
        if (b) { a = 1; }
      } else if (!b) { a = 0; }
      b = (a | 0) != 0;
      return b | 0;
    }
    function Ba(a, b) {
      a = a | 0;
      b = b | 0;
      var c = 0,
        d = 0,
        f = 0,
        g = 0.0,
        h = 0,
        i = 0,
        j = 0.0,
        k = 0.0,
        l = 0.0;
      if (+e[a >> 3] < +e[b + 8 >> 3]) {
        d = 0;
        return d | 0;
      }
      if (+e[a + 8 >> 3] > +e[b >> 3]) {
        d = 0;
        return d | 0;
      }
      g = +e[a + 16 >> 3];
      c = a + 24 | 0;
      l = +e[c >> 3];
      h = g < l;
      d = b + 16 | 0;
      k = +e[d >> 3];
      f = b + 24 | 0;
      j = +e[f >> 3];
      i = k < j;
      b = l - k < j - g;
      a = h ? i | b ? 1 : 2 : 0;
      b = i ? h ? 1 : b ? 2 : 1 : 0;
      g = +ic(g, a);
      if (g < +ic(+e[f >> 3], b)) {
        i = 0;
        return i | 0;
      }
      l = +ic(+e[c >> 3], a);
      if (l > +ic(+e[d >> 3], b)) {
        i = 0;
        return i | 0;
      }
      i = 1;
      return i | 0;
    }
    function Ca(a, c, d, f) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      f = f | 0;
      var g = 0,
        h = 0.0,
        i = 0.0,
        j = 0.0,
        k = 0.0;
      h = +e[a + 16 >> 3];
      k = +e[a + 24 >> 3];
      a = h < k;
      j = +e[c + 16 >> 3];
      i = +e[c + 24 >> 3];
      g = j < i;
      c = k - j < i - h;
      b[d >> 2] = a ? g | c ? 1 : 2 : 0;
      b[f >> 2] = g ? a ? 1 : c ? 2 : 1 : 0;
      return;
    }
    function Da(a, b) {
      a = a | 0;
      b = b | 0;
      var c = 0,
        d = 0,
        f = 0,
        g = 0.0,
        h = 0,
        i = 0,
        j = 0.0,
        k = 0.0,
        l = 0.0;
      if (+e[a >> 3] < +e[b >> 3]) {
        d = 0;
        return d | 0;
      }
      if (+e[a + 8 >> 3] > +e[b + 8 >> 3]) {
        d = 0;
        return d | 0;
      }
      d = a + 16 | 0;
      j = +e[d >> 3];
      g = +e[a + 24 >> 3];
      h = j < g;
      c = b + 16 | 0;
      l = +e[c >> 3];
      f = b + 24 | 0;
      k = +e[f >> 3];
      i = l < k;
      b = g - l < k - j;
      a = h ? i | b ? 1 : 2 : 0;
      b = i ? h ? 1 : b ? 2 : 1 : 0;
      g = +ic(g, a);
      if (!(g <= +ic(+e[f >> 3], b))) {
        i = 0;
        return i | 0;
      }
      l = +ic(+e[d >> 3], a);
      i = l >= +ic(+e[c >> 3], b);
      return i | 0;
    }
    function Ea(a, c) {
      a = a | 0;
      c = c | 0;
      var d = 0,
        f = 0,
        g = 0,
        h = 0.0,
        i = 0.0,
        j = 0.0;
      g = T;
      T = T + 176 | 0;
      f = g;
      b[f >> 2] = 4;
      j = +e[c >> 3];
      e[f + 8 >> 3] = j;
      h = +e[c + 16 >> 3];
      e[f + 16 >> 3] = h;
      e[f + 24 >> 3] = j;
      j = +e[c + 24 >> 3];
      e[f + 32 >> 3] = j;
      i = +e[c + 8 >> 3];
      e[f + 40 >> 3] = i;
      e[f + 48 >> 3] = j;
      e[f + 56 >> 3] = i;
      e[f + 64 >> 3] = h;
      c = f + 72 | 0;
      d = c + 96 | 0;
      do {
        b[c >> 2] = 0;
        c = c + 4 | 0;
      } while ((c | 0) < (d | 0));
      Ud(a | 0, f | 0, 168) | 0;
      T = g;
      return;
    }
    function Fa(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var g = 0.0,
        h = 0.0,
        i = 0.0,
        j = 0.0,
        k = 0,
        l = 0,
        m = 0,
        n = 0,
        o = 0,
        r = 0,
        s = 0,
        t = 0,
        u = 0.0,
        v = 0.0;
      t = T;
      T = T + 288 | 0;
      n = t + 264 | 0;
      o = t + 96 | 0;
      m = t;
      k = m;
      l = k + 96 | 0;
      do {
        b[k >> 2] = 0;
        k = k + 4 | 0;
      } while ((k | 0) < (l | 0));
      c = ac(c, m) | 0;
      if (c | 0) {
        s = c;
        T = t;
        return s | 0;
      }
      l = m;
      m = b[l >> 2] | 0;
      l = b[l + 4 >> 2] | 0;
      Xb(m, l, n) | 0;
      Yb(m, l, o) | 0;
      j = +kc(n, o + 8 | 0);
      e[n >> 3] = +e[a >> 3];
      l = n + 8 | 0;
      e[l >> 3] = +e[a + 16 >> 3];
      e[o >> 3] = +e[a + 8 >> 3];
      m = o + 8 | 0;
      e[m >> 3] = +e[a + 24 >> 3];
      h = +kc(n, o);
      v = +e[l >> 3] - +e[m >> 3];
      i = +q(+v);
      u = +e[n >> 3] - +e[o >> 3];
      g = +q(+u);
      if (!(v == 0.0 | u == 0.0) ? (v = +Rd(+i, +g), v = +A(+(h * h / +Sd(+(v / +Sd(+i, +g)), 3.0) / (j * (j * 2.59807621135) * .8))), e[f >> 3] = v, r = ~~v >>> 0, s = +q(v) >= 1.0 ? v > 0.0 ? ~~+C(+p(v / 4294967296.0), 4294967295.0) >>> 0 : ~~+A((v - +(~~v >>> 0)) / 4294967296.0) >>> 0 : 0, !((b[f + 4 >> 2] & 2146435072 | 0) == 2146435072)) : 0) {
        o = (r | 0) == 0 & (s | 0) == 0;
        c = d;
        b[c >> 2] = o ? 1 : r;
        b[c + 4 >> 2] = o ? 0 : s;
        c = 0;
      } else { c = 1; }
      s = c;
      T = t;
      return s | 0;
    }
    function Ga(a, c, d, g) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      g = g | 0;
      var h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0,
        m = 0,
        n = 0.0;
      m = T;
      T = T + 288 | 0;
      j = m + 264 | 0;
      k = m + 96 | 0;
      l = m;
      h = l;
      i = h + 96 | 0;
      do {
        b[h >> 2] = 0;
        h = h + 4 | 0;
      } while ((h | 0) < (i | 0));
      d = ac(d, l) | 0;
      if (d | 0) {
        g = d;
        T = m;
        return g | 0;
      }
      d = l;
      h = b[d >> 2] | 0;
      d = b[d + 4 >> 2] | 0;
      Xb(h, d, j) | 0;
      Yb(h, d, k) | 0;
      n = +kc(j, k + 8 | 0);
      n = +A(+(+kc(a, c) / (n * 2.0)));
      e[f >> 3] = n;
      d = ~~n >>> 0;
      h = +q(n) >= 1.0 ? n > 0.0 ? ~~+C(+p(n / 4294967296.0), 4294967295.0) >>> 0 : ~~+A((n - +(~~n >>> 0)) / 4294967296.0) >>> 0 : 0;
      if ((b[f + 4 >> 2] & 2146435072 | 0) == 2146435072) {
        g = 1;
        T = m;
        return g | 0;
      }
      l = (d | 0) == 0 & (h | 0) == 0;
      b[g >> 2] = l ? 1 : d;
      b[g + 4 >> 2] = l ? 0 : h;
      g = 0;
      T = m;
      return g | 0;
    }
    function Ha(a, b) {
      a = a | 0;
      b = +b;
      var c = 0,
        d = 0.0,
        f = 0.0,
        g = 0,
        h = 0.0,
        i = 0,
        j = 0.0,
        k = 0.0,
        l = 0.0;
      g = a + 16 | 0;
      h = +e[g >> 3];
      c = a + 24 | 0;
      f = +e[c >> 3];
      d = h - f;
      d = h < f ? d + 6.283185307179586 : d;
      k = +e[a >> 3];
      i = a + 8 | 0;
      j = +e[i >> 3];
      l = k - j;
      d = (d * b - d) * .5;
      b = (l * b - l) * .5;
      k = k + b;
      e[a >> 3] = k > 1.5707963267948966 ? 1.5707963267948966 : k;
      b = j - b;
      e[i >> 3] = b < -1.5707963267948966 ? -1.5707963267948966 : b;
      b = h + d;
      b = b > 3.141592653589793 ? b + -6.283185307179586 : b;
      e[g >> 3] = b < -3.141592653589793 ? b + 6.283185307179586 : b;
      b = f - d;
      b = b > 3.141592653589793 ? b + -6.283185307179586 : b;
      e[c >> 3] = b < -3.141592653589793 ? b + 6.283185307179586 : b;
      return;
    }
    function Ia(a, c, d, e) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      b[a >> 2] = c;
      b[a + 4 >> 2] = d;
      b[a + 8 >> 2] = e;
      return;
    }
    function Ja(a, c) {
      a = a | 0;
      c = c | 0;
      var d = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0.0,
        j = 0.0,
        k = 0.0,
        l = 0.0,
        m = 0,
        n = 0,
        o = 0.0;
      n = c + 8 | 0;
      b[n >> 2] = 0;
      k = +e[a >> 3];
      i = +q(+k);
      l = +e[a + 8 >> 3];
      j = +q(+l) * 1.1547005383792515;
      i = i + j * .5;
      d = ~~i;
      a = ~~j;
      i = i - +(d | 0);
      j = j - +(a | 0);
      do { if (i < .5) {
        if (i < .3333333333333333) {
          b[c >> 2] = d;
          if (j < (i + 1.0) * .5) {
            b[c + 4 >> 2] = a;
            break;
          } else {
            a = a + 1 | 0;
            b[c + 4 >> 2] = a;
            break;
          }
        } else {
          o = 1.0 - i;
          a = (!(j < o) & 1) + a | 0;
          b[c + 4 >> 2] = a;
          if (o <= j & j < i * 2.0) {
            d = d + 1 | 0;
            b[c >> 2] = d;
            break;
          } else {
            b[c >> 2] = d;
            break;
          }
        }
      } else {
        if (!(i < .6666666666666666)) {
          d = d + 1 | 0;
          b[c >> 2] = d;
          if (j < i * .5) {
            b[c + 4 >> 2] = a;
            break;
          } else {
            a = a + 1 | 0;
            b[c + 4 >> 2] = a;
            break;
          }
        }
        if (j < 1.0 - i) {
          b[c + 4 >> 2] = a;
          if (i * 2.0 + -1.0 < j) {
            b[c >> 2] = d;
            break;
          }
        } else {
          a = a + 1 | 0;
          b[c + 4 >> 2] = a;
        }
        d = d + 1 | 0;
        b[c >> 2] = d;
      } } while (0);
      do { if (k < 0.0) { if (!(a & 1)) {
        m = (a | 0) / 2 | 0;
        m = Fd(d | 0, ((d | 0) < 0) << 31 >> 31 | 0, m | 0, ((m | 0) < 0) << 31 >> 31 | 0) | 0;
        d = ~~(+(d | 0) - (+(m >>> 0) + 4294967296.0 * +(H() | 0)) * 2.0);
        b[c >> 2] = d;
        break;
      } else {
        m = (a + 1 | 0) / 2 | 0;
        m = Fd(d | 0, ((d | 0) < 0) << 31 >> 31 | 0, m | 0, ((m | 0) < 0) << 31 >> 31 | 0) | 0;
        d = ~~(+(d | 0) - ((+(m >>> 0) + 4294967296.0 * +(H() | 0)) * 2.0 + 1.0));
        b[c >> 2] = d;
        break;
      } } } while (0);
      m = c + 4 | 0;
      if (l < 0.0) {
        d = d - ((a << 1 | 1 | 0) / 2 | 0) | 0;
        b[c >> 2] = d;
        a = 0 - a | 0;
        b[m >> 2] = a;
      }
      f = a - d | 0;
      if ((d | 0) < 0) {
        g = 0 - d | 0;
        b[m >> 2] = f;
        b[n >> 2] = g;
        b[c >> 2] = 0;
        a = f;
        d = 0;
      } else { g = 0; }
      if ((a | 0) < 0) {
        d = d - a | 0;
        b[c >> 2] = d;
        g = g - a | 0;
        b[n >> 2] = g;
        b[m >> 2] = 0;
        a = 0;
      }
      h = d - g | 0;
      f = a - g | 0;
      if ((g | 0) < 0) {
        b[c >> 2] = h;
        b[m >> 2] = f;
        b[n >> 2] = 0;
        a = f;
        d = h;
        g = 0;
      }
      f = (a | 0) < (d | 0) ? a : d;
      f = (g | 0) < (f | 0) ? g : f;
      if ((f | 0) <= 0) { return; }
      b[c >> 2] = d - f;
      b[m >> 2] = a - f;
      b[n >> 2] = g - f;
      return;
    }
    function Ka(a) {
      a = a | 0;
      var c = 0,
        d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0;
      c = b[a >> 2] | 0;
      h = a + 4 | 0;
      d = b[h >> 2] | 0;
      if ((c | 0) < 0) {
        d = d - c | 0;
        b[h >> 2] = d;
        g = a + 8 | 0;
        b[g >> 2] = (b[g >> 2] | 0) - c;
        b[a >> 2] = 0;
        c = 0;
      }
      if ((d | 0) < 0) {
        c = c - d | 0;
        b[a >> 2] = c;
        g = a + 8 | 0;
        f = (b[g >> 2] | 0) - d | 0;
        b[g >> 2] = f;
        b[h >> 2] = 0;
        d = 0;
      } else {
        f = a + 8 | 0;
        g = f;
        f = b[f >> 2] | 0;
      }
      if ((f | 0) < 0) {
        c = c - f | 0;
        b[a >> 2] = c;
        d = d - f | 0;
        b[h >> 2] = d;
        b[g >> 2] = 0;
        f = 0;
      }
      e = (d | 0) < (c | 0) ? d : c;
      e = (f | 0) < (e | 0) ? f : e;
      if ((e | 0) <= 0) { return; }
      b[a >> 2] = c - e;
      b[h >> 2] = d - e;
      b[g >> 2] = f - e;
      return;
    }
    function La(a, c) {
      a = a | 0;
      c = c | 0;
      var d = 0.0,
        f = 0;
      f = b[a + 8 >> 2] | 0;
      d = +((b[a + 4 >> 2] | 0) - f | 0);
      e[c >> 3] = +((b[a >> 2] | 0) - f | 0) - d * .5;
      e[c + 8 >> 3] = d * .8660254037844386;
      return;
    }
    function Ma(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      b[d >> 2] = (b[c >> 2] | 0) + (b[a >> 2] | 0);
      b[d + 4 >> 2] = (b[c + 4 >> 2] | 0) + (b[a + 4 >> 2] | 0);
      b[d + 8 >> 2] = (b[c + 8 >> 2] | 0) + (b[a + 8 >> 2] | 0);
      return;
    }
    function Na(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      b[d >> 2] = (b[a >> 2] | 0) - (b[c >> 2] | 0);
      b[d + 4 >> 2] = (b[a + 4 >> 2] | 0) - (b[c + 4 >> 2] | 0);
      b[d + 8 >> 2] = (b[a + 8 >> 2] | 0) - (b[c + 8 >> 2] | 0);
      return;
    }
    function Oa(a, c) {
      a = a | 0;
      c = c | 0;
      var d = 0,
        e = 0;
      d = B(b[a >> 2] | 0, c) | 0;
      b[a >> 2] = d;
      d = a + 4 | 0;
      e = B(b[d >> 2] | 0, c) | 0;
      b[d >> 2] = e;
      a = a + 8 | 0;
      c = B(b[a >> 2] | 0, c) | 0;
      b[a >> 2] = c;
      return;
    }
    function Pa(a) {
      a = a | 0;
      var c = 0,
        d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0;
      h = b[a >> 2] | 0;
      i = (h | 0) < 0;
      e = (b[a + 4 >> 2] | 0) - (i ? h : 0) | 0;
      g = (e | 0) < 0;
      f = (g ? 0 - e | 0 : 0) + ((b[a + 8 >> 2] | 0) - (i ? h : 0)) | 0;
      d = (f | 0) < 0;
      a = d ? 0 : f;
      c = (g ? 0 : e) - (d ? f : 0) | 0;
      f = (i ? 0 : h) - (g ? e : 0) - (d ? f : 0) | 0;
      d = (c | 0) < (f | 0) ? c : f;
      d = (a | 0) < (d | 0) ? a : d;
      e = (d | 0) > 0;
      a = a - (e ? d : 0) | 0;
      c = c - (e ? d : 0) | 0;
      a: do { switch (f - (e ? d : 0) | 0) {
        case 0:
          switch (c | 0) {
            case 0:
              {
                i = (a | 0) == 0 ? 0 : (a | 0) == 1 ? 1 : 7;
                return i | 0;
              }
            case 1:
              {
                i = (a | 0) == 0 ? 2 : (a | 0) == 1 ? 3 : 7;
                return i | 0;
              }
            default:
              break a;
          }
        case 1:
          switch (c | 0) {
            case 0:
              {
                i = (a | 0) == 0 ? 4 : (a | 0) == 1 ? 5 : 7;
                return i | 0;
              }
            case 1:
              {
                if (!a) { a = 6; }else { break a; }
                return a | 0;
              }
            default:
              break a;
          }
        default:

      } } while (0);
      i = 7;
      return i | 0;
    }
    function Qa(a) {
      a = a | 0;
      var c = 0,
        d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0;
      j = a + 8 | 0;
      h = b[j >> 2] | 0;
      i = (b[a >> 2] | 0) - h | 0;
      k = a + 4 | 0;
      h = (b[k >> 2] | 0) - h | 0;
      if (i >>> 0 > 715827881 | h >>> 0 > 715827881) {
        e = (i | 0) > 0;
        f = 2147483647 - i | 0;
        g = -2147483648 - i | 0;
        if (e ? (f | 0) < (i | 0) : (g | 0) > (i | 0)) {
          k = 1;
          return k | 0;
        }
        d = i << 1;
        if (e ? (2147483647 - d | 0) < (i | 0) : (-2147483648 - d | 0) > (i | 0)) {
          k = 1;
          return k | 0;
        }
        if ((h | 0) > 0 ? (2147483647 - h | 0) < (h | 0) : (-2147483648 - h | 0) > (h | 0)) {
          k = 1;
          return k | 0;
        }
        c = i * 3 | 0;
        d = h << 1;
        if ((e ? (f | 0) < (d | 0) : (g | 0) > (d | 0)) ? 1 : (i | 0) > -1 ? (c | -2147483648 | 0) >= (h | 0) : (c ^ -2147483648 | 0) < (h | 0)) {
          k = 1;
          return k | 0;
        }
      } else {
        d = h << 1;
        c = i * 3 | 0;
      }
      e = Ad(+(c - h | 0) * .14285714285714285) | 0;
      b[a >> 2] = e;
      f = Ad(+(d + i | 0) * .14285714285714285) | 0;
      b[k >> 2] = f;
      b[j >> 2] = 0;
      d = (f | 0) < (e | 0);
      c = d ? e : f;
      d = d ? f : e;
      if ((d | 0) < 0) {
        if ((d | 0) == -2147483648 ? 1 : (c | 0) > 0 ? (2147483647 - c | 0) < (d | 0) : (-2147483648 - c | 0) > (d | 0)) { I(27795, 26892, 354, 26903); }
        if ((c | 0) > -1 ? (c | -2147483648 | 0) >= (d | 0) : (c ^ -2147483648 | 0) < (d | 0)) { I(27795, 26892, 354, 26903); }
      }
      c = f - e | 0;
      if ((e | 0) < 0) {
        d = 0 - e | 0;
        b[k >> 2] = c;
        b[j >> 2] = d;
        b[a >> 2] = 0;
        e = 0;
      } else {
        c = f;
        d = 0;
      }
      if ((c | 0) < 0) {
        e = e - c | 0;
        b[a >> 2] = e;
        d = d - c | 0;
        b[j >> 2] = d;
        b[k >> 2] = 0;
        c = 0;
      }
      g = e - d | 0;
      f = c - d | 0;
      if ((d | 0) < 0) {
        b[a >> 2] = g;
        b[k >> 2] = f;
        b[j >> 2] = 0;
        c = f;
        f = g;
        d = 0;
      } else { f = e; }
      e = (c | 0) < (f | 0) ? c : f;
      e = (d | 0) < (e | 0) ? d : e;
      if ((e | 0) <= 0) {
        k = 0;
        return k | 0;
      }
      b[a >> 2] = f - e;
      b[k >> 2] = c - e;
      b[j >> 2] = d - e;
      k = 0;
      return k | 0;
    }
    function Ra(a) {
      a = a | 0;
      var c = 0,
        d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0;
      h = a + 8 | 0;
      f = b[h >> 2] | 0;
      g = (b[a >> 2] | 0) - f | 0;
      i = a + 4 | 0;
      f = (b[i >> 2] | 0) - f | 0;
      if (g >>> 0 > 715827881 | f >>> 0 > 715827881) {
        d = (g | 0) > 0;
        if (d ? (2147483647 - g | 0) < (g | 0) : (-2147483648 - g | 0) > (g | 0)) {
          i = 1;
          return i | 0;
        }
        c = g << 1;
        e = (f | 0) > 0;
        if (e ? (2147483647 - f | 0) < (f | 0) : (-2147483648 - f | 0) > (f | 0)) {
          i = 1;
          return i | 0;
        }
        j = f << 1;
        if (e ? (2147483647 - j | 0) < (f | 0) : (-2147483648 - j | 0) > (f | 0)) {
          j = 1;
          return j | 0;
        }
        if (d ? (2147483647 - c | 0) < (f | 0) : (-2147483648 - c | 0) > (f | 0)) {
          j = 1;
          return j | 0;
        }
        d = f * 3 | 0;
        if ((f | 0) > -1 ? (d | -2147483648 | 0) >= (g | 0) : (d ^ -2147483648 | 0) < (g | 0)) {
          j = 1;
          return j | 0;
        }
      } else {
        d = f * 3 | 0;
        c = g << 1;
      }
      e = Ad(+(c + f | 0) * .14285714285714285) | 0;
      b[a >> 2] = e;
      f = Ad(+(d - g | 0) * .14285714285714285) | 0;
      b[i >> 2] = f;
      b[h >> 2] = 0;
      d = (f | 0) < (e | 0);
      c = d ? e : f;
      d = d ? f : e;
      if ((d | 0) < 0) {
        if ((d | 0) == -2147483648 ? 1 : (c | 0) > 0 ? (2147483647 - c | 0) < (d | 0) : (-2147483648 - c | 0) > (d | 0)) { I(27795, 26892, 402, 26917); }
        if ((c | 0) > -1 ? (c | -2147483648 | 0) >= (d | 0) : (c ^ -2147483648 | 0) < (d | 0)) { I(27795, 26892, 402, 26917); }
      }
      c = f - e | 0;
      if ((e | 0) < 0) {
        d = 0 - e | 0;
        b[i >> 2] = c;
        b[h >> 2] = d;
        b[a >> 2] = 0;
        e = 0;
      } else {
        c = f;
        d = 0;
      }
      if ((c | 0) < 0) {
        e = e - c | 0;
        b[a >> 2] = e;
        d = d - c | 0;
        b[h >> 2] = d;
        b[i >> 2] = 0;
        c = 0;
      }
      g = e - d | 0;
      f = c - d | 0;
      if ((d | 0) < 0) {
        b[a >> 2] = g;
        b[i >> 2] = f;
        b[h >> 2] = 0;
        c = f;
        f = g;
        d = 0;
      } else { f = e; }
      e = (c | 0) < (f | 0) ? c : f;
      e = (d | 0) < (e | 0) ? d : e;
      if ((e | 0) <= 0) {
        j = 0;
        return j | 0;
      }
      b[a >> 2] = f - e;
      b[i >> 2] = c - e;
      b[h >> 2] = d - e;
      j = 0;
      return j | 0;
    }
    function Sa(a) {
      a = a | 0;
      var c = 0,
        d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0;
      h = a + 8 | 0;
      d = b[h >> 2] | 0;
      c = (b[a >> 2] | 0) - d | 0;
      i = a + 4 | 0;
      d = (b[i >> 2] | 0) - d | 0;
      e = Ad(+((c * 3 | 0) - d | 0) * .14285714285714285) | 0;
      b[a >> 2] = e;
      c = Ad(+((d << 1) + c | 0) * .14285714285714285) | 0;
      b[i >> 2] = c;
      b[h >> 2] = 0;
      d = c - e | 0;
      if ((e | 0) < 0) {
        g = 0 - e | 0;
        b[i >> 2] = d;
        b[h >> 2] = g;
        b[a >> 2] = 0;
        c = d;
        e = 0;
        d = g;
      } else { d = 0; }
      if ((c | 0) < 0) {
        e = e - c | 0;
        b[a >> 2] = e;
        d = d - c | 0;
        b[h >> 2] = d;
        b[i >> 2] = 0;
        c = 0;
      }
      g = e - d | 0;
      f = c - d | 0;
      if ((d | 0) < 0) {
        b[a >> 2] = g;
        b[i >> 2] = f;
        b[h >> 2] = 0;
        c = f;
        f = g;
        d = 0;
      } else { f = e; }
      e = (c | 0) < (f | 0) ? c : f;
      e = (d | 0) < (e | 0) ? d : e;
      if ((e | 0) <= 0) { return; }
      b[a >> 2] = f - e;
      b[i >> 2] = c - e;
      b[h >> 2] = d - e;
      return;
    }
    function Ta(a) {
      a = a | 0;
      var c = 0,
        d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0;
      h = a + 8 | 0;
      d = b[h >> 2] | 0;
      c = (b[a >> 2] | 0) - d | 0;
      i = a + 4 | 0;
      d = (b[i >> 2] | 0) - d | 0;
      e = Ad(+((c << 1) + d | 0) * .14285714285714285) | 0;
      b[a >> 2] = e;
      c = Ad(+((d * 3 | 0) - c | 0) * .14285714285714285) | 0;
      b[i >> 2] = c;
      b[h >> 2] = 0;
      d = c - e | 0;
      if ((e | 0) < 0) {
        g = 0 - e | 0;
        b[i >> 2] = d;
        b[h >> 2] = g;
        b[a >> 2] = 0;
        c = d;
        e = 0;
        d = g;
      } else { d = 0; }
      if ((c | 0) < 0) {
        e = e - c | 0;
        b[a >> 2] = e;
        d = d - c | 0;
        b[h >> 2] = d;
        b[i >> 2] = 0;
        c = 0;
      }
      g = e - d | 0;
      f = c - d | 0;
      if ((d | 0) < 0) {
        b[a >> 2] = g;
        b[i >> 2] = f;
        b[h >> 2] = 0;
        c = f;
        f = g;
        d = 0;
      } else { f = e; }
      e = (c | 0) < (f | 0) ? c : f;
      e = (d | 0) < (e | 0) ? d : e;
      if ((e | 0) <= 0) { return; }
      b[a >> 2] = f - e;
      b[i >> 2] = c - e;
      b[h >> 2] = d - e;
      return;
    }
    function Ua(a) {
      a = a | 0;
      var c = 0,
        d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0;
      c = b[a >> 2] | 0;
      h = a + 4 | 0;
      d = b[h >> 2] | 0;
      i = a + 8 | 0;
      e = b[i >> 2] | 0;
      f = d + (c * 3 | 0) | 0;
      b[a >> 2] = f;
      d = e + (d * 3 | 0) | 0;
      b[h >> 2] = d;
      c = (e * 3 | 0) + c | 0;
      b[i >> 2] = c;
      e = d - f | 0;
      if ((f | 0) < 0) {
        c = c - f | 0;
        b[h >> 2] = e;
        b[i >> 2] = c;
        b[a >> 2] = 0;
        d = e;
        e = 0;
      } else { e = f; }
      if ((d | 0) < 0) {
        e = e - d | 0;
        b[a >> 2] = e;
        c = c - d | 0;
        b[i >> 2] = c;
        b[h >> 2] = 0;
        d = 0;
      }
      g = e - c | 0;
      f = d - c | 0;
      if ((c | 0) < 0) {
        b[a >> 2] = g;
        b[h >> 2] = f;
        b[i >> 2] = 0;
        e = g;
        c = 0;
      } else { f = d; }
      d = (f | 0) < (e | 0) ? f : e;
      d = (c | 0) < (d | 0) ? c : d;
      if ((d | 0) <= 0) { return; }
      b[a >> 2] = e - d;
      b[h >> 2] = f - d;
      b[i >> 2] = c - d;
      return;
    }
    function Va(a) {
      a = a | 0;
      var c = 0,
        d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0;
      f = b[a >> 2] | 0;
      h = a + 4 | 0;
      c = b[h >> 2] | 0;
      i = a + 8 | 0;
      d = b[i >> 2] | 0;
      e = (c * 3 | 0) + f | 0;
      f = d + (f * 3 | 0) | 0;
      b[a >> 2] = f;
      b[h >> 2] = e;
      c = (d * 3 | 0) + c | 0;
      b[i >> 2] = c;
      d = e - f | 0;
      if ((f | 0) < 0) {
        c = c - f | 0;
        b[h >> 2] = d;
        b[i >> 2] = c;
        b[a >> 2] = 0;
        f = 0;
      } else { d = e; }
      if ((d | 0) < 0) {
        f = f - d | 0;
        b[a >> 2] = f;
        c = c - d | 0;
        b[i >> 2] = c;
        b[h >> 2] = 0;
        d = 0;
      }
      g = f - c | 0;
      e = d - c | 0;
      if ((c | 0) < 0) {
        b[a >> 2] = g;
        b[h >> 2] = e;
        b[i >> 2] = 0;
        f = g;
        c = 0;
      } else { e = d; }
      d = (e | 0) < (f | 0) ? e : f;
      d = (c | 0) < (d | 0) ? c : d;
      if ((d | 0) <= 0) { return; }
      b[a >> 2] = f - d;
      b[h >> 2] = e - d;
      b[i >> 2] = c - d;
      return;
    }
    function Wa(a, c) {
      a = a | 0;
      c = c | 0;
      var d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0;
      if ((c + -1 | 0) >>> 0 >= 6) { return; }
      f = (b[15440 + (c * 12 | 0) >> 2] | 0) + (b[a >> 2] | 0) | 0;
      b[a >> 2] = f;
      i = a + 4 | 0;
      e = (b[15440 + (c * 12 | 0) + 4 >> 2] | 0) + (b[i >> 2] | 0) | 0;
      b[i >> 2] = e;
      h = a + 8 | 0;
      c = (b[15440 + (c * 12 | 0) + 8 >> 2] | 0) + (b[h >> 2] | 0) | 0;
      b[h >> 2] = c;
      d = e - f | 0;
      if ((f | 0) < 0) {
        c = c - f | 0;
        b[i >> 2] = d;
        b[h >> 2] = c;
        b[a >> 2] = 0;
        e = 0;
      } else {
        d = e;
        e = f;
      }
      if ((d | 0) < 0) {
        e = e - d | 0;
        b[a >> 2] = e;
        c = c - d | 0;
        b[h >> 2] = c;
        b[i >> 2] = 0;
        d = 0;
      }
      g = e - c | 0;
      f = d - c | 0;
      if ((c | 0) < 0) {
        b[a >> 2] = g;
        b[i >> 2] = f;
        b[h >> 2] = 0;
        e = g;
        c = 0;
      } else { f = d; }
      d = (f | 0) < (e | 0) ? f : e;
      d = (c | 0) < (d | 0) ? c : d;
      if ((d | 0) <= 0) { return; }
      b[a >> 2] = e - d;
      b[i >> 2] = f - d;
      b[h >> 2] = c - d;
      return;
    }
    function Xa(a) {
      a = a | 0;
      var c = 0,
        d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0;
      f = b[a >> 2] | 0;
      h = a + 4 | 0;
      c = b[h >> 2] | 0;
      i = a + 8 | 0;
      d = b[i >> 2] | 0;
      e = c + f | 0;
      f = d + f | 0;
      b[a >> 2] = f;
      b[h >> 2] = e;
      c = d + c | 0;
      b[i >> 2] = c;
      d = e - f | 0;
      if ((f | 0) < 0) {
        c = c - f | 0;
        b[h >> 2] = d;
        b[i >> 2] = c;
        b[a >> 2] = 0;
        e = 0;
      } else {
        d = e;
        e = f;
      }
      if ((d | 0) < 0) {
        e = e - d | 0;
        b[a >> 2] = e;
        c = c - d | 0;
        b[i >> 2] = c;
        b[h >> 2] = 0;
        d = 0;
      }
      g = e - c | 0;
      f = d - c | 0;
      if ((c | 0) < 0) {
        b[a >> 2] = g;
        b[h >> 2] = f;
        b[i >> 2] = 0;
        e = g;
        c = 0;
      } else { f = d; }
      d = (f | 0) < (e | 0) ? f : e;
      d = (c | 0) < (d | 0) ? c : d;
      if ((d | 0) <= 0) { return; }
      b[a >> 2] = e - d;
      b[h >> 2] = f - d;
      b[i >> 2] = c - d;
      return;
    }
    function Ya(a) {
      a = a | 0;
      var c = 0,
        d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0;
      c = b[a >> 2] | 0;
      h = a + 4 | 0;
      e = b[h >> 2] | 0;
      i = a + 8 | 0;
      d = b[i >> 2] | 0;
      f = e + c | 0;
      b[a >> 2] = f;
      e = d + e | 0;
      b[h >> 2] = e;
      c = d + c | 0;
      b[i >> 2] = c;
      d = e - f | 0;
      if ((f | 0) < 0) {
        c = c - f | 0;
        b[h >> 2] = d;
        b[i >> 2] = c;
        b[a >> 2] = 0;
        e = 0;
      } else {
        d = e;
        e = f;
      }
      if ((d | 0) < 0) {
        e = e - d | 0;
        b[a >> 2] = e;
        c = c - d | 0;
        b[i >> 2] = c;
        b[h >> 2] = 0;
        d = 0;
      }
      g = e - c | 0;
      f = d - c | 0;
      if ((c | 0) < 0) {
        b[a >> 2] = g;
        b[h >> 2] = f;
        b[i >> 2] = 0;
        e = g;
        c = 0;
      } else { f = d; }
      d = (f | 0) < (e | 0) ? f : e;
      d = (c | 0) < (d | 0) ? c : d;
      if ((d | 0) <= 0) { return; }
      b[a >> 2] = e - d;
      b[h >> 2] = f - d;
      b[i >> 2] = c - d;
      return;
    }
    function Za(a) {
      a = a | 0;
      switch (a | 0) {
        case 1:
          {
            a = 5;
            break;
          }
        case 5:
          {
            a = 4;
            break;
          }
        case 4:
          {
            a = 6;
            break;
          }
        case 6:
          {
            a = 2;
            break;
          }
        case 2:
          {
            a = 3;
            break;
          }
        case 3:
          {
            a = 1;
            break;
          }
        default:

      }
      return a | 0;
    }
    function _a(a) {
      a = a | 0;
      switch (a | 0) {
        case 1:
          {
            a = 3;
            break;
          }
        case 3:
          {
            a = 2;
            break;
          }
        case 2:
          {
            a = 6;
            break;
          }
        case 6:
          {
            a = 4;
            break;
          }
        case 4:
          {
            a = 5;
            break;
          }
        case 5:
          {
            a = 1;
            break;
          }
        default:

      }
      return a | 0;
    }
    function $a(a) {
      a = a | 0;
      var c = 0,
        d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0;
      c = b[a >> 2] | 0;
      h = a + 4 | 0;
      d = b[h >> 2] | 0;
      i = a + 8 | 0;
      e = b[i >> 2] | 0;
      f = d + (c << 1) | 0;
      b[a >> 2] = f;
      d = e + (d << 1) | 0;
      b[h >> 2] = d;
      c = (e << 1) + c | 0;
      b[i >> 2] = c;
      e = d - f | 0;
      if ((f | 0) < 0) {
        c = c - f | 0;
        b[h >> 2] = e;
        b[i >> 2] = c;
        b[a >> 2] = 0;
        d = e;
        e = 0;
      } else { e = f; }
      if ((d | 0) < 0) {
        e = e - d | 0;
        b[a >> 2] = e;
        c = c - d | 0;
        b[i >> 2] = c;
        b[h >> 2] = 0;
        d = 0;
      }
      g = e - c | 0;
      f = d - c | 0;
      if ((c | 0) < 0) {
        b[a >> 2] = g;
        b[h >> 2] = f;
        b[i >> 2] = 0;
        e = g;
        c = 0;
      } else { f = d; }
      d = (f | 0) < (e | 0) ? f : e;
      d = (c | 0) < (d | 0) ? c : d;
      if ((d | 0) <= 0) { return; }
      b[a >> 2] = e - d;
      b[h >> 2] = f - d;
      b[i >> 2] = c - d;
      return;
    }
    function ab(a) {
      a = a | 0;
      var c = 0,
        d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0;
      f = b[a >> 2] | 0;
      h = a + 4 | 0;
      c = b[h >> 2] | 0;
      i = a + 8 | 0;
      d = b[i >> 2] | 0;
      e = (c << 1) + f | 0;
      f = d + (f << 1) | 0;
      b[a >> 2] = f;
      b[h >> 2] = e;
      c = (d << 1) + c | 0;
      b[i >> 2] = c;
      d = e - f | 0;
      if ((f | 0) < 0) {
        c = c - f | 0;
        b[h >> 2] = d;
        b[i >> 2] = c;
        b[a >> 2] = 0;
        f = 0;
      } else { d = e; }
      if ((d | 0) < 0) {
        f = f - d | 0;
        b[a >> 2] = f;
        c = c - d | 0;
        b[i >> 2] = c;
        b[h >> 2] = 0;
        d = 0;
      }
      g = f - c | 0;
      e = d - c | 0;
      if ((c | 0) < 0) {
        b[a >> 2] = g;
        b[h >> 2] = e;
        b[i >> 2] = 0;
        f = g;
        c = 0;
      } else { e = d; }
      d = (e | 0) < (f | 0) ? e : f;
      d = (c | 0) < (d | 0) ? c : d;
      if ((d | 0) <= 0) { return; }
      b[a >> 2] = f - d;
      b[h >> 2] = e - d;
      b[i >> 2] = c - d;
      return;
    }
    function bb(a, c) {
      a = a | 0;
      c = c | 0;
      var d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0;
      h = (b[a >> 2] | 0) - (b[c >> 2] | 0) | 0;
      i = (h | 0) < 0;
      e = (b[a + 4 >> 2] | 0) - (b[c + 4 >> 2] | 0) - (i ? h : 0) | 0;
      g = (e | 0) < 0;
      f = (i ? 0 - h | 0 : 0) + (b[a + 8 >> 2] | 0) - (b[c + 8 >> 2] | 0) + (g ? 0 - e | 0 : 0) | 0;
      a = (f | 0) < 0;
      c = a ? 0 : f;
      d = (g ? 0 : e) - (a ? f : 0) | 0;
      f = (i ? 0 : h) - (g ? e : 0) - (a ? f : 0) | 0;
      a = (d | 0) < (f | 0) ? d : f;
      a = (c | 0) < (a | 0) ? c : a;
      e = (a | 0) > 0;
      c = c - (e ? a : 0) | 0;
      d = d - (e ? a : 0) | 0;
      a = f - (e ? a : 0) | 0;
      a = (a | 0) > -1 ? a : 0 - a | 0;
      d = (d | 0) > -1 ? d : 0 - d | 0;
      c = (c | 0) > -1 ? c : 0 - c | 0;
      c = (d | 0) > (c | 0) ? d : c;
      return ((a | 0) > (c | 0) ? a : c) | 0;
    }
    function cb(a, c) {
      a = a | 0;
      c = c | 0;
      var d = 0;
      d = b[a + 8 >> 2] | 0;
      b[c >> 2] = (b[a >> 2] | 0) - d;
      b[c + 4 >> 2] = (b[a + 4 >> 2] | 0) - d;
      return;
    }
    function db(a, c) {
      a = a | 0;
      c = c | 0;
      var d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0;
      e = b[a >> 2] | 0;
      b[c >> 2] = e;
      f = b[a + 4 >> 2] | 0;
      h = c + 4 | 0;
      b[h >> 2] = f;
      i = c + 8 | 0;
      b[i >> 2] = 0;
      d = (f | 0) < (e | 0);
      a = d ? e : f;
      d = d ? f : e;
      if ((d | 0) < 0) {
        if ((d | 0) == -2147483648 ? 1 : (a | 0) > 0 ? (2147483647 - a | 0) < (d | 0) : (-2147483648 - a | 0) > (d | 0)) {
          c = 1;
          return c | 0;
        }
        if ((a | 0) > -1 ? (a | -2147483648 | 0) >= (d | 0) : (a ^ -2147483648 | 0) < (d | 0)) {
          c = 1;
          return c | 0;
        }
      }
      a = f - e | 0;
      if ((e | 0) < 0) {
        d = 0 - e | 0;
        b[h >> 2] = a;
        b[i >> 2] = d;
        b[c >> 2] = 0;
        e = 0;
      } else {
        a = f;
        d = 0;
      }
      if ((a | 0) < 0) {
        e = e - a | 0;
        b[c >> 2] = e;
        d = d - a | 0;
        b[i >> 2] = d;
        b[h >> 2] = 0;
        a = 0;
      }
      g = e - d | 0;
      f = a - d | 0;
      if ((d | 0) < 0) {
        b[c >> 2] = g;
        b[h >> 2] = f;
        b[i >> 2] = 0;
        a = f;
        f = g;
        d = 0;
      } else { f = e; }
      e = (a | 0) < (f | 0) ? a : f;
      e = (d | 0) < (e | 0) ? d : e;
      if ((e | 0) <= 0) {
        c = 0;
        return c | 0;
      }
      b[c >> 2] = f - e;
      b[h >> 2] = a - e;
      b[i >> 2] = d - e;
      c = 0;
      return c | 0;
    }
    function eb(a) {
      a = a | 0;
      var c = 0,
        d = 0,
        e = 0,
        f = 0;
      c = a + 8 | 0;
      f = b[c >> 2] | 0;
      d = f - (b[a >> 2] | 0) | 0;
      b[a >> 2] = d;
      e = a + 4 | 0;
      a = (b[e >> 2] | 0) - f | 0;
      b[e >> 2] = a;
      b[c >> 2] = 0 - (a + d);
      return;
    }
    function fb(a) {
      a = a | 0;
      var c = 0,
        d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0;
      d = b[a >> 2] | 0;
      c = 0 - d | 0;
      b[a >> 2] = c;
      h = a + 8 | 0;
      b[h >> 2] = 0;
      i = a + 4 | 0;
      e = b[i >> 2] | 0;
      f = e + d | 0;
      if ((d | 0) > 0) {
        b[i >> 2] = f;
        b[h >> 2] = d;
        b[a >> 2] = 0;
        c = 0;
        e = f;
      } else { d = 0; }
      if ((e | 0) < 0) {
        g = c - e | 0;
        b[a >> 2] = g;
        d = d - e | 0;
        b[h >> 2] = d;
        b[i >> 2] = 0;
        f = g - d | 0;
        c = 0 - d | 0;
        if ((d | 0) < 0) {
          b[a >> 2] = f;
          b[i >> 2] = c;
          b[h >> 2] = 0;
          e = c;
          d = 0;
        } else {
          e = 0;
          f = g;
        }
      } else { f = c; }
      c = (e | 0) < (f | 0) ? e : f;
      c = (d | 0) < (c | 0) ? d : c;
      if ((c | 0) <= 0) { return; }
      b[a >> 2] = f - c;
      b[i >> 2] = e - c;
      b[h >> 2] = d - c;
      return;
    }
    function gb(a, c, d, e, f) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      f = f | 0;
      var g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0,
        m = 0;
      m = T;
      T = T + 64 | 0;
      l = m;
      i = m + 56 | 0;
      if (!(0 == 0 & (c & 2013265920 | 0) == 134217728 & (0 == 0 & (e & 2013265920 | 0) == 134217728))) {
        f = 5;
        T = m;
        return f | 0;
      }
      if ((a | 0) == (d | 0) & (c | 0) == (e | 0)) {
        b[f >> 2] = 0;
        f = 0;
        T = m;
        return f | 0;
      }
      h = Od(a | 0, c | 0, 52) | 0;
      H() | 0;
      h = h & 15;
      k = Od(d | 0, e | 0, 52) | 0;
      H() | 0;
      if ((h | 0) != (k & 15 | 0)) {
        f = 12;
        T = m;
        return f | 0;
      }
      g = h + -1 | 0;
      if (h >>> 0 > 1) {
        Db(a, c, g, l) | 0;
        Db(d, e, g, i) | 0;
        k = l;
        j = b[k >> 2] | 0;
        k = b[k + 4 >> 2] | 0;
        a: do { if ((j | 0) == (b[i >> 2] | 0) ? (k | 0) == (b[i + 4 >> 2] | 0) : 0) {
          h = (h ^ 15) * 3 | 0;
          g = Od(a | 0, c | 0, h | 0) | 0;
          H() | 0;
          g = g & 7;
          h = Od(d | 0, e | 0, h | 0) | 0;
          H() | 0;
          h = h & 7;
          do { if (!((g | 0) == 0 | (h | 0) == 0)) {
            if ((g | 0) == 7) { g = 5; }else {
              if ((g | 0) == 1 | (h | 0) == 1 ? Fb(j, k) | 0 : 0) {
                g = 5;
                break;
              }
              if ((b[15536 + (g << 2) >> 2] | 0) != (h | 0) ? (b[15568 + (g << 2) >> 2] | 0) != (h | 0) : 0) { break a; }
              b[f >> 2] = 1;
              g = 0;
            }
          } else {
            b[f >> 2] = 1;
            g = 0;
          } } while (0);
          f = g;
          T = m;
          return f | 0;
        } } while (0);
      }
      g = l;
      h = g + 56 | 0;
      do {
        b[g >> 2] = 0;
        g = g + 4 | 0;
      } while ((g | 0) < (h | 0));
      aa(a, c, 1, l) | 0;
      c = l;
      if (((((!((b[c >> 2] | 0) == (d | 0) ? (b[c + 4 >> 2] | 0) == (e | 0) : 0) ? (c = l + 8 | 0, !((b[c >> 2] | 0) == (d | 0) ? (b[c + 4 >> 2] | 0) == (e | 0) : 0)) : 0) ? (c = l + 16 | 0, !((b[c >> 2] | 0) == (d | 0) ? (b[c + 4 >> 2] | 0) == (e | 0) : 0)) : 0) ? (c = l + 24 | 0, !((b[c >> 2] | 0) == (d | 0) ? (b[c + 4 >> 2] | 0) == (e | 0) : 0)) : 0) ? (c = l + 32 | 0, !((b[c >> 2] | 0) == (d | 0) ? (b[c + 4 >> 2] | 0) == (e | 0) : 0)) : 0) ? (c = l + 40 | 0, !((b[c >> 2] | 0) == (d | 0) ? (b[c + 4 >> 2] | 0) == (e | 0) : 0)) : 0) {
        g = l + 48 | 0;
        g = ((b[g >> 2] | 0) == (d | 0) ? (b[g + 4 >> 2] | 0) == (e | 0) : 0) & 1;
      } else { g = 1; }
      b[f >> 2] = g;
      f = 0;
      T = m;
      return f | 0;
    }
    function hb(a, c, d, e, f) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      f = f | 0;
      d = fa(a, c, d, e) | 0;
      if ((d | 0) == 7) {
        f = 11;
        return f | 0;
      }
      e = Pd(d | 0, 0, 56) | 0;
      c = c & -2130706433 | (H() | 0) | 268435456;
      b[f >> 2] = a | e;
      b[f + 4 >> 2] = c;
      f = 0;
      return f | 0;
    }
    function ib(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      if (!(0 == 0 & (c & 2013265920 | 0) == 268435456)) {
        d = 6;
        return d | 0;
      }
      b[d >> 2] = a;
      b[d + 4 >> 2] = c & -2130706433 | 134217728;
      d = 0;
      return d | 0;
    }
    function jb(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var e = 0,
        f = 0,
        g = 0;
      f = T;
      T = T + 16 | 0;
      e = f;
      b[e >> 2] = 0;
      if (!(0 == 0 & (c & 2013265920 | 0) == 268435456)) {
        e = 6;
        T = f;
        return e | 0;
      }
      g = Od(a | 0, c | 0, 56) | 0;
      H() | 0;
      e = ea(a, c & -2130706433 | 134217728, g & 7, e, d) | 0;
      T = f;
      return e | 0;
    }
    function kb(a, b) {
      a = a | 0;
      b = b | 0;
      var c = 0;
      c = Od(a | 0, b | 0, 56) | 0;
      H() | 0;
      switch (c & 7) {
        case 0:
        case 7:
          {
            c = 0;
            return c | 0;
          }
        default:

      }
      c = b & -2130706433 | 134217728;
      if (!(0 == 0 & (b & 2013265920 | 0) == 268435456)) {
        c = 0;
        return c | 0;
      }
      if (0 == 0 & (b & 117440512 | 0) == 16777216 & (Fb(a, c) | 0) != 0) {
        c = 0;
        return c | 0;
      }
      c = Bb(a, c) | 0;
      return c | 0;
    }
    function lb(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var e = 0,
        f = 0,
        g = 0,
        h = 0;
      f = T;
      T = T + 16 | 0;
      e = f;
      if (!(0 == 0 & (c & 2013265920 | 0) == 268435456)) {
        e = 6;
        T = f;
        return e | 0;
      }
      g = c & -2130706433 | 134217728;
      h = d;
      b[h >> 2] = a;
      b[h + 4 >> 2] = g;
      b[e >> 2] = 0;
      c = Od(a | 0, c | 0, 56) | 0;
      H() | 0;
      e = ea(a, g, c & 7, e, d + 8 | 0) | 0;
      T = f;
      return e | 0;
    }
    function mb(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var e = 0,
        f = 0;
      f = (Fb(a, c) | 0) == 0;
      c = c & -2130706433;
      e = d;
      b[e >> 2] = f ? a : 0;
      b[e + 4 >> 2] = f ? c | 285212672 : 0;
      e = d + 8 | 0;
      b[e >> 2] = a;
      b[e + 4 >> 2] = c | 301989888;
      e = d + 16 | 0;
      b[e >> 2] = a;
      b[e + 4 >> 2] = c | 318767104;
      e = d + 24 | 0;
      b[e >> 2] = a;
      b[e + 4 >> 2] = c | 335544320;
      e = d + 32 | 0;
      b[e >> 2] = a;
      b[e + 4 >> 2] = c | 352321536;
      d = d + 40 | 0;
      b[d >> 2] = a;
      b[d + 4 >> 2] = c | 369098752;
      return 0;
    }
    function nb(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var e = 0,
        f = 0,
        g = 0,
        h = 0;
      h = T;
      T = T + 16 | 0;
      f = h;
      g = c & -2130706433 | 134217728;
      if (!(0 == 0 & (c & 2013265920 | 0) == 268435456)) {
        g = 6;
        T = h;
        return g | 0;
      }
      e = Od(a | 0, c | 0, 56) | 0;
      H() | 0;
      e = md(a, g, e & 7) | 0;
      if ((e | 0) == -1) {
        b[d >> 2] = 0;
        g = 6;
        T = h;
        return g | 0;
      }
      if (Wb(a, g, f) | 0) { I(27795, 26932, 282, 26947); }
      c = Od(a | 0, c | 0, 52) | 0;
      H() | 0;
      c = c & 15;
      if (!(Fb(a, g) | 0)) { xb(f, c, e, 2, d); }else { tb(f, c, e, 2, d); }
      g = 0;
      T = h;
      return g | 0;
    }
    function ob(a, b, c) {
      a = a | 0;
      b = b | 0;
      c = c | 0;
      var d = 0,
        e = 0;
      d = T;
      T = T + 16 | 0;
      e = d;
      pb(a, b, c, e);
      Ja(e, c + 4 | 0);
      T = d;
      return;
    }
    function pb(a, c, d, f) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      f = f | 0;
      var g = 0.0,
        h = 0.0,
        i = 0.0,
        j = 0,
        k = 0;
      j = T;
      T = T + 16 | 0;
      k = j;
      qb(a, d, k);
      h = +w(+(1.0 - +e[k >> 3] * .5));
      if (h < 1.0e-16) {
        b[f >> 2] = 0;
        b[f + 4 >> 2] = 0;
        b[f + 8 >> 2] = 0;
        b[f + 12 >> 2] = 0;
        T = j;
        return;
      }
      k = b[d >> 2] | 0;
      g = +e[15920 + (k * 24 | 0) >> 3];
      g = +gc(g - +gc(+mc(15600 + (k << 4) | 0, a)));
      if (!(Tb(c) | 0)) { i = g; }else { i = +gc(g + -.3334731722518321); }
      g = +v(+h) * 2.618033988749896;
      if ((c | 0) > 0) {
        a = 0;
        do {
          g = g * 2.6457513110645907;
          a = a + 1 | 0;
        } while ((a | 0) != (c | 0));
      }
      h = +t(+i) * g;
      e[f >> 3] = h;
      i = +u(+i) * g;
      e[f + 8 >> 3] = i;
      T = j;
      return;
    }
    function qb(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var f = 0.0,
        g = 0,
        h = 0;
      h = T;
      T = T + 32 | 0;
      g = h;
      ld(a, g);
      b[c >> 2] = 0;
      e[d >> 3] = 5.0;
      f = +kd(16400, g);
      if (f < +e[d >> 3]) {
        b[c >> 2] = 0;
        e[d >> 3] = f;
      }
      f = +kd(16424, g);
      if (f < +e[d >> 3]) {
        b[c >> 2] = 1;
        e[d >> 3] = f;
      }
      f = +kd(16448, g);
      if (f < +e[d >> 3]) {
        b[c >> 2] = 2;
        e[d >> 3] = f;
      }
      f = +kd(16472, g);
      if (f < +e[d >> 3]) {
        b[c >> 2] = 3;
        e[d >> 3] = f;
      }
      f = +kd(16496, g);
      if (f < +e[d >> 3]) {
        b[c >> 2] = 4;
        e[d >> 3] = f;
      }
      f = +kd(16520, g);
      if (f < +e[d >> 3]) {
        b[c >> 2] = 5;
        e[d >> 3] = f;
      }
      f = +kd(16544, g);
      if (f < +e[d >> 3]) {
        b[c >> 2] = 6;
        e[d >> 3] = f;
      }
      f = +kd(16568, g);
      if (f < +e[d >> 3]) {
        b[c >> 2] = 7;
        e[d >> 3] = f;
      }
      f = +kd(16592, g);
      if (f < +e[d >> 3]) {
        b[c >> 2] = 8;
        e[d >> 3] = f;
      }
      f = +kd(16616, g);
      if (f < +e[d >> 3]) {
        b[c >> 2] = 9;
        e[d >> 3] = f;
      }
      f = +kd(16640, g);
      if (f < +e[d >> 3]) {
        b[c >> 2] = 10;
        e[d >> 3] = f;
      }
      f = +kd(16664, g);
      if (f < +e[d >> 3]) {
        b[c >> 2] = 11;
        e[d >> 3] = f;
      }
      f = +kd(16688, g);
      if (f < +e[d >> 3]) {
        b[c >> 2] = 12;
        e[d >> 3] = f;
      }
      f = +kd(16712, g);
      if (f < +e[d >> 3]) {
        b[c >> 2] = 13;
        e[d >> 3] = f;
      }
      f = +kd(16736, g);
      if (f < +e[d >> 3]) {
        b[c >> 2] = 14;
        e[d >> 3] = f;
      }
      f = +kd(16760, g);
      if (f < +e[d >> 3]) {
        b[c >> 2] = 15;
        e[d >> 3] = f;
      }
      f = +kd(16784, g);
      if (f < +e[d >> 3]) {
        b[c >> 2] = 16;
        e[d >> 3] = f;
      }
      f = +kd(16808, g);
      if (f < +e[d >> 3]) {
        b[c >> 2] = 17;
        e[d >> 3] = f;
      }
      f = +kd(16832, g);
      if (f < +e[d >> 3]) {
        b[c >> 2] = 18;
        e[d >> 3] = f;
      }
      f = +kd(16856, g);
      if (!(f < +e[d >> 3])) {
        T = h;
        return;
      }
      b[c >> 2] = 19;
      e[d >> 3] = f;
      T = h;
      return;
    }
    function rb(a, c, d, f, g) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      f = f | 0;
      g = g | 0;
      var h = 0.0,
        i = 0.0,
        j = 0.0;
      h = +hd(a);
      if (h < 1.0e-16) {
        c = 15600 + (c << 4) | 0;
        b[g >> 2] = b[c >> 2];
        b[g + 4 >> 2] = b[c + 4 >> 2];
        b[g + 8 >> 2] = b[c + 8 >> 2];
        b[g + 12 >> 2] = b[c + 12 >> 2];
        return;
      }
      i = +z(+ +e[a + 8 >> 3], + +e[a >> 3]);
      if ((d | 0) > 0) {
        a = 0;
        do {
          h = h * .37796447300922725;
          a = a + 1 | 0;
        } while ((a | 0) != (d | 0));
      }
      j = h * .3333333333333333;
      if (!f) {
        h = +y(+(h * .381966011250105));
        if (Tb(d) | 0) { i = +gc(i + .3334731722518321); }
      } else {
        d = (Tb(d) | 0) == 0;
        h = +y(+((d ? j : j * .37796447300922725) * .381966011250105));
      }
      nc(15600 + (c << 4) | 0, +gc(+e[15920 + (c * 24 | 0) >> 3] - i), h, g);
      return;
    }
    function sb(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var e = 0,
        f = 0;
      e = T;
      T = T + 16 | 0;
      f = e;
      La(a + 4 | 0, f);
      rb(f, b[a >> 2] | 0, c, 0, d);
      T = e;
      return;
    }
    function tb(a, c, d, f, g) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      f = f | 0;
      g = g | 0;
      var h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0,
        m = 0,
        n = 0,
        o = 0,
        p = 0,
        q = 0,
        r = 0,
        s = 0,
        t = 0,
        u = 0,
        v = 0,
        w = 0,
        x = 0,
        y = 0,
        z = 0,
        A = 0,
        B = 0,
        C = 0,
        D = 0,
        E = 0,
        F = 0,
        G = 0,
        H = 0.0,
        J = 0.0;
      G = T;
      T = T + 272 | 0;
      h = G + 256 | 0;
      u = G + 240 | 0;
      D = G;
      E = G + 224 | 0;
      F = G + 208 | 0;
      v = G + 176 | 0;
      w = G + 160 | 0;
      x = G + 192 | 0;
      y = G + 144 | 0;
      z = G + 128 | 0;
      A = G + 112 | 0;
      B = G + 96 | 0;
      C = G + 80 | 0;
      b[h >> 2] = c;
      b[u >> 2] = b[a >> 2];
      b[u + 4 >> 2] = b[a + 4 >> 2];
      b[u + 8 >> 2] = b[a + 8 >> 2];
      b[u + 12 >> 2] = b[a + 12 >> 2];
      ub(u, h, D);
      b[g >> 2] = 0;
      u = f + d + ((f | 0) == 5 & 1) | 0;
      if ((u | 0) <= (d | 0)) {
        T = G;
        return;
      }
      k = b[h >> 2] | 0;
      l = E + 4 | 0;
      m = v + 4 | 0;
      n = d + 5 | 0;
      o = 16880 + (k << 2) | 0;
      p = 16960 + (k << 2) | 0;
      q = z + 8 | 0;
      r = A + 8 | 0;
      s = B + 8 | 0;
      t = F + 4 | 0;
      j = d;
      a: while (1) {
        i = D + (((j | 0) % 5 | 0) << 4) | 0;
        b[F >> 2] = b[i >> 2];
        b[F + 4 >> 2] = b[i + 4 >> 2];
        b[F + 8 >> 2] = b[i + 8 >> 2];
        b[F + 12 >> 2] = b[i + 12 >> 2];
        do {} while ((vb(F, k, 0, 1) | 0) == 2);
        if ((j | 0) > (d | 0) & (Tb(c) | 0) != 0) {
          b[v >> 2] = b[F >> 2];
          b[v + 4 >> 2] = b[F + 4 >> 2];
          b[v + 8 >> 2] = b[F + 8 >> 2];
          b[v + 12 >> 2] = b[F + 12 >> 2];
          La(l, w);
          f = b[v >> 2] | 0;
          h = b[17040 + (f * 80 | 0) + (b[E >> 2] << 2) >> 2] | 0;
          b[v >> 2] = b[18640 + (f * 80 | 0) + (h * 20 | 0) >> 2];
          i = b[18640 + (f * 80 | 0) + (h * 20 | 0) + 16 >> 2] | 0;
          if ((i | 0) > 0) {
            a = 0;
            do {
              Xa(m);
              a = a + 1 | 0;
            } while ((a | 0) < (i | 0));
          }
          i = 18640 + (f * 80 | 0) + (h * 20 | 0) + 4 | 0;
          b[x >> 2] = b[i >> 2];
          b[x + 4 >> 2] = b[i + 4 >> 2];
          b[x + 8 >> 2] = b[i + 8 >> 2];
          Oa(x, (b[o >> 2] | 0) * 3 | 0);
          Ma(m, x, m);
          Ka(m);
          La(m, y);
          H = +(b[p >> 2] | 0);
          e[z >> 3] = H * 3.0;
          e[q >> 3] = 0.0;
          J = H * -1.5;
          e[A >> 3] = J;
          e[r >> 3] = H * 2.598076211353316;
          e[B >> 3] = J;
          e[s >> 3] = H * -2.598076211353316;
          switch (b[17040 + ((b[v >> 2] | 0) * 80 | 0) + (b[F >> 2] << 2) >> 2] | 0) {
            case 1:
              {
                a = A;
                f = z;
                break;
              }
            case 3:
              {
                a = B;
                f = A;
                break;
              }
            case 2:
              {
                a = z;
                f = B;
                break;
              }
            default:
              {
                a = 12;
                break a;
              }
          }
          id(w, y, f, a, C);
          rb(C, b[v >> 2] | 0, k, 1, g + 8 + (b[g >> 2] << 4) | 0);
          b[g >> 2] = (b[g >> 2] | 0) + 1;
        }
        if ((j | 0) < (n | 0)) {
          La(t, v);
          rb(v, b[F >> 2] | 0, k, 1, g + 8 + (b[g >> 2] << 4) | 0);
          b[g >> 2] = (b[g >> 2] | 0) + 1;
        }
        b[E >> 2] = b[F >> 2];
        b[E + 4 >> 2] = b[F + 4 >> 2];
        b[E + 8 >> 2] = b[F + 8 >> 2];
        b[E + 12 >> 2] = b[F + 12 >> 2];
        j = j + 1 | 0;
        if ((j | 0) >= (u | 0)) {
          a = 3;
          break;
        }
      }
      if ((a | 0) == 3) {
        T = G;
        return;
      } else if ((a | 0) == 12) { I(26970, 27017, 572, 27027); }
    }
    function ub(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0;
      j = T;
      T = T + 128 | 0;
      e = j + 64 | 0;
      f = j;
      g = e;
      h = 20240;
      i = g + 60 | 0;
      do {
        b[g >> 2] = b[h >> 2];
        g = g + 4 | 0;
        h = h + 4 | 0;
      } while ((g | 0) < (i | 0));
      g = f;
      h = 20304;
      i = g + 60 | 0;
      do {
        b[g >> 2] = b[h >> 2];
        g = g + 4 | 0;
        h = h + 4 | 0;
      } while ((g | 0) < (i | 0));
      i = (Tb(b[c >> 2] | 0) | 0) == 0;
      e = i ? e : f;
      f = a + 4 | 0;
      $a(f);
      ab(f);
      if (Tb(b[c >> 2] | 0) | 0) {
        Va(f);
        b[c >> 2] = (b[c >> 2] | 0) + 1;
      }
      b[d >> 2] = b[a >> 2];
      c = d + 4 | 0;
      Ma(f, e, c);
      Ka(c);
      b[d + 16 >> 2] = b[a >> 2];
      c = d + 20 | 0;
      Ma(f, e + 12 | 0, c);
      Ka(c);
      b[d + 32 >> 2] = b[a >> 2];
      c = d + 36 | 0;
      Ma(f, e + 24 | 0, c);
      Ka(c);
      b[d + 48 >> 2] = b[a >> 2];
      c = d + 52 | 0;
      Ma(f, e + 36 | 0, c);
      Ka(c);
      b[d + 64 >> 2] = b[a >> 2];
      d = d + 68 | 0;
      Ma(f, e + 48 | 0, d);
      Ka(d);
      T = j;
      return;
    }
    function vb(a, c, d, e) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      var f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0,
        m = 0,
        n = 0,
        o = 0,
        p = 0;
      p = T;
      T = T + 32 | 0;
      n = p + 12 | 0;
      i = p;
      o = a + 4 | 0;
      m = b[16960 + (c << 2) >> 2] | 0;
      l = (e | 0) != 0;
      m = l ? m * 3 | 0 : m;
      f = b[o >> 2] | 0;
      k = a + 8 | 0;
      h = b[k >> 2] | 0;
      if (l) {
        g = a + 12 | 0;
        e = b[g >> 2] | 0;
        f = h + f + e | 0;
        if ((f | 0) == (m | 0)) {
          o = 1;
          T = p;
          return o | 0;
        } else { j = g; }
      } else {
        j = a + 12 | 0;
        e = b[j >> 2] | 0;
        f = h + f + e | 0;
      }
      if ((f | 0) <= (m | 0)) {
        o = 0;
        T = p;
        return o | 0;
      }
      do { if ((e | 0) > 0) {
        e = b[a >> 2] | 0;
        if ((h | 0) > 0) {
          g = 18640 + (e * 80 | 0) + 60 | 0;
          e = a;
          break;
        }
        e = 18640 + (e * 80 | 0) + 40 | 0;
        if (!d) {
          g = e;
          e = a;
        } else {
          Ia(n, m, 0, 0);
          Na(o, n, i);
          Ya(i);
          Ma(i, n, o);
          g = e;
          e = a;
        }
      } else {
        g = 18640 + ((b[a >> 2] | 0) * 80 | 0) + 20 | 0;
        e = a;
      } } while (0);
      b[e >> 2] = b[g >> 2];
      f = g + 16 | 0;
      if ((b[f >> 2] | 0) > 0) {
        e = 0;
        do {
          Xa(o);
          e = e + 1 | 0;
        } while ((e | 0) < (b[f >> 2] | 0));
      }
      a = g + 4 | 0;
      b[n >> 2] = b[a >> 2];
      b[n + 4 >> 2] = b[a + 4 >> 2];
      b[n + 8 >> 2] = b[a + 8 >> 2];
      c = b[16880 + (c << 2) >> 2] | 0;
      Oa(n, l ? c * 3 | 0 : c);
      Ma(o, n, o);
      Ka(o);
      if (l) { e = ((b[k >> 2] | 0) + (b[o >> 2] | 0) + (b[j >> 2] | 0) | 0) == (m | 0) ? 1 : 2; }else { e = 2; }
      o = e;
      T = p;
      return o | 0;
    }
    function wb(a, b) {
      a = a | 0;
      b = b | 0;
      var c = 0;
      do { c = vb(a, b, 0, 1) | 0; } while ((c | 0) == 2);
      return c | 0;
    }
    function xb(a, c, d, f, g) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      f = f | 0;
      g = g | 0;
      var h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0,
        m = 0,
        n = 0,
        o = 0,
        p = 0,
        q = 0,
        r = 0,
        s = 0,
        t = 0,
        u = 0,
        v = 0,
        w = 0,
        x = 0,
        y = 0,
        z = 0,
        A = 0,
        B = 0,
        C = 0.0,
        D = 0.0;
      B = T;
      T = T + 240 | 0;
      h = B + 224 | 0;
      x = B + 208 | 0;
      y = B;
      z = B + 192 | 0;
      A = B + 176 | 0;
      s = B + 160 | 0;
      t = B + 144 | 0;
      u = B + 128 | 0;
      v = B + 112 | 0;
      w = B + 96 | 0;
      b[h >> 2] = c;
      b[x >> 2] = b[a >> 2];
      b[x + 4 >> 2] = b[a + 4 >> 2];
      b[x + 8 >> 2] = b[a + 8 >> 2];
      b[x + 12 >> 2] = b[a + 12 >> 2];
      yb(x, h, y);
      b[g >> 2] = 0;
      r = f + d + ((f | 0) == 6 & 1) | 0;
      if ((r | 0) <= (d | 0)) {
        T = B;
        return;
      }
      k = b[h >> 2] | 0;
      l = d + 6 | 0;
      m = 16960 + (k << 2) | 0;
      n = t + 8 | 0;
      o = u + 8 | 0;
      p = v + 8 | 0;
      q = z + 4 | 0;
      i = 0;
      j = d;
      f = -1;
      a: while (1) {
        h = (j | 0) % 6 | 0;
        a = y + (h << 4) | 0;
        b[z >> 2] = b[a >> 2];
        b[z + 4 >> 2] = b[a + 4 >> 2];
        b[z + 8 >> 2] = b[a + 8 >> 2];
        b[z + 12 >> 2] = b[a + 12 >> 2];
        a = i;
        i = vb(z, k, 0, 1) | 0;
        if ((j | 0) > (d | 0) & (Tb(c) | 0) != 0 ? (a | 0) != 1 ? (b[z >> 2] | 0) != (f | 0) : 0 : 0) {
          La(y + (((h + 5 | 0) % 6 | 0) << 4) + 4 | 0, A);
          La(y + (h << 4) + 4 | 0, s);
          C = +(b[m >> 2] | 0);
          e[t >> 3] = C * 3.0;
          e[n >> 3] = 0.0;
          D = C * -1.5;
          e[u >> 3] = D;
          e[o >> 3] = C * 2.598076211353316;
          e[v >> 3] = D;
          e[p >> 3] = C * -2.598076211353316;
          h = b[x >> 2] | 0;
          switch (b[17040 + (h * 80 | 0) + (((f | 0) == (h | 0) ? b[z >> 2] | 0 : f) << 2) >> 2] | 0) {
            case 1:
              {
                a = u;
                f = t;
                break;
              }
            case 3:
              {
                a = v;
                f = u;
                break;
              }
            case 2:
              {
                a = t;
                f = v;
                break;
              }
            default:
              {
                a = 8;
                break a;
              }
          }
          id(A, s, f, a, w);
          if (!(jd(A, w) | 0) ? !(jd(s, w) | 0) : 0) {
            rb(w, b[x >> 2] | 0, k, 1, g + 8 + (b[g >> 2] << 4) | 0);
            b[g >> 2] = (b[g >> 2] | 0) + 1;
          }
        }
        if ((j | 0) < (l | 0)) {
          La(q, A);
          rb(A, b[z >> 2] | 0, k, 1, g + 8 + (b[g >> 2] << 4) | 0);
          b[g >> 2] = (b[g >> 2] | 0) + 1;
        }
        j = j + 1 | 0;
        if ((j | 0) >= (r | 0)) {
          a = 3;
          break;
        } else { f = b[z >> 2] | 0; }
      }
      if ((a | 0) == 3) {
        T = B;
        return;
      } else if ((a | 0) == 8) { I(27054, 27017, 737, 27099); }
    }
    function yb(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0;
      j = T;
      T = T + 160 | 0;
      e = j + 80 | 0;
      f = j;
      g = e;
      h = 20368;
      i = g + 72 | 0;
      do {
        b[g >> 2] = b[h >> 2];
        g = g + 4 | 0;
        h = h + 4 | 0;
      } while ((g | 0) < (i | 0));
      g = f;
      h = 20448;
      i = g + 72 | 0;
      do {
        b[g >> 2] = b[h >> 2];
        g = g + 4 | 0;
        h = h + 4 | 0;
      } while ((g | 0) < (i | 0));
      i = (Tb(b[c >> 2] | 0) | 0) == 0;
      e = i ? e : f;
      f = a + 4 | 0;
      $a(f);
      ab(f);
      if (Tb(b[c >> 2] | 0) | 0) {
        Va(f);
        b[c >> 2] = (b[c >> 2] | 0) + 1;
      }
      b[d >> 2] = b[a >> 2];
      c = d + 4 | 0;
      Ma(f, e, c);
      Ka(c);
      b[d + 16 >> 2] = b[a >> 2];
      c = d + 20 | 0;
      Ma(f, e + 12 | 0, c);
      Ka(c);
      b[d + 32 >> 2] = b[a >> 2];
      c = d + 36 | 0;
      Ma(f, e + 24 | 0, c);
      Ka(c);
      b[d + 48 >> 2] = b[a >> 2];
      c = d + 52 | 0;
      Ma(f, e + 36 | 0, c);
      Ka(c);
      b[d + 64 >> 2] = b[a >> 2];
      c = d + 68 | 0;
      Ma(f, e + 48 | 0, c);
      Ka(c);
      b[d + 80 >> 2] = b[a >> 2];
      d = d + 84 | 0;
      Ma(f, e + 60 | 0, d);
      Ka(d);
      T = j;
      return;
    }
    function zb(a, b) {
      a = a | 0;
      b = b | 0;
      b = Od(a | 0, b | 0, 52) | 0;
      H() | 0;
      return b & 15 | 0;
    }
    function Ab(a, b) {
      a = a | 0;
      b = b | 0;
      b = Od(a | 0, b | 0, 45) | 0;
      H() | 0;
      return b & 127 | 0;
    }
    function Bb(b, c) {
      b = b | 0;
      c = c | 0;
      var d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0;
      if (!(0 == 0 & (c & -16777216 | 0) == 134217728)) {
        b = 0;
        return b | 0;
      }
      e = Od(b | 0, c | 0, 52) | 0;
      H() | 0;
      e = e & 15;
      d = Od(b | 0, c | 0, 45) | 0;
      H() | 0;
      d = d & 127;
      if (d >>> 0 > 121) {
        b = 0;
        return b | 0;
      }
      h = (e ^ 15) * 3 | 0;
      f = Od(b | 0, c | 0, h | 0) | 0;
      h = Pd(f | 0, H() | 0, h | 0) | 0;
      f = H() | 0;
      g = Fd(-1227133514, -1171, h | 0, f | 0) | 0;
      if (!((h & 613566756 & g | 0) == 0 & (f & 4681 & (H() | 0) | 0) == 0)) {
        h = 0;
        return h | 0;
      }
      h = (e * 3 | 0) + 19 | 0;
      g = Pd(~b | 0, ~c | 0, h | 0) | 0;
      h = Od(g | 0, H() | 0, h | 0) | 0;
      if (!((e | 0) == 15 | (h | 0) == 0 & (H() | 0) == 0)) {
        h = 0;
        return h | 0;
      }
      if (!(a[20528 + d >> 0] | 0)) {
        h = 1;
        return h | 0;
      }
      c = c & 8191;
      if ((b | 0) == 0 & (c | 0) == 0) {
        h = 1;
        return h | 0;
      } else {
        h = Qd(b | 0, c | 0, 0) | 0;
        H() | 0;
        return ((63 - h | 0) % 3 | 0 | 0) != 0 | 0;
      }
      return 0;
    }
    function Cb(a, c, d, e) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      var f = 0,
        g = 0,
        h = 0,
        i = 0;
      f = Pd(c | 0, 0, 52) | 0;
      g = H() | 0;
      d = Pd(d | 0, 0, 45) | 0;
      d = g | (H() | 0) | 134225919;
      if ((c | 0) < 1) {
        g = -1;
        e = d;
        c = a;
        b[c >> 2] = g;
        a = a + 4 | 0;
        b[a >> 2] = e;
        return;
      }
      g = 1;
      f = -1;
      while (1) {
        h = (15 - g | 0) * 3 | 0;
        i = Pd(7, 0, h | 0) | 0;
        d = d & ~(H() | 0);
        h = Pd(e | 0, 0, h | 0) | 0;
        f = f & ~i | h;
        d = d | (H() | 0);
        if ((g | 0) == (c | 0)) { break; }else { g = g + 1 | 0; }
      }
      i = a;
      h = i;
      b[h >> 2] = f;
      i = i + 4 | 0;
      b[i >> 2] = d;
      return;
    }
    function Db(a, c, d, e) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      var f = 0,
        g = 0;
      g = Od(a | 0, c | 0, 52) | 0;
      H() | 0;
      g = g & 15;
      if (d >>> 0 > 15) {
        e = 4;
        return e | 0;
      }
      if ((g | 0) < (d | 0)) {
        e = 12;
        return e | 0;
      }
      if ((g | 0) == (d | 0)) {
        b[e >> 2] = a;
        b[e + 4 >> 2] = c;
        e = 0;
        return e | 0;
      }
      f = Pd(d | 0, 0, 52) | 0;
      f = f | a;
      a = H() | 0 | c & -15728641;
      if ((g | 0) > (d | 0)) { do {
        c = Pd(7, 0, (14 - d | 0) * 3 | 0) | 0;
        d = d + 1 | 0;
        f = c | f;
        a = H() | 0 | a;
      } while ((d | 0) < (g | 0)); }
      b[e >> 2] = f;
      b[e + 4 >> 2] = a;
      e = 0;
      return e | 0;
    }
    function Eb(a, c, d, e) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      var f = 0,
        g = 0,
        h = 0;
      g = Od(a | 0, c | 0, 52) | 0;
      H() | 0;
      g = g & 15;
      if (!((d | 0) < 16 & (g | 0) <= (d | 0))) {
        e = 4;
        return e | 0;
      }
      f = d - g | 0;
      d = Od(a | 0, c | 0, 45) | 0;
      H() | 0;
      a: do { if (!(ma(d & 127) | 0)) {
        d = Mc(7, 0, f, ((f | 0) < 0) << 31 >> 31) | 0;
        f = H() | 0;
      } else {
        b: do { if (g | 0) {
          d = 1;
          while (1) {
            h = Pd(7, 0, (15 - d | 0) * 3 | 0) | 0;
            if (!((h & a | 0) == 0 & ((H() | 0) & c | 0) == 0)) { break; }
            if (d >>> 0 < g >>> 0) { d = d + 1 | 0; }else { break b; }
          }
          d = Mc(7, 0, f, ((f | 0) < 0) << 31 >> 31) | 0;
          f = H() | 0;
          break a;
        } } while (0);
        d = Mc(7, 0, f, ((f | 0) < 0) << 31 >> 31) | 0;
        d = Kd(d | 0, H() | 0, 5, 0) | 0;
        d = Ed(d | 0, H() | 0, -5, -1) | 0;
        d = Id(d | 0, H() | 0, 6, 0) | 0;
        d = Ed(d | 0, H() | 0, 1, 0) | 0;
        f = H() | 0;
      } } while (0);
      h = e;
      b[h >> 2] = d;
      b[h + 4 >> 2] = f;
      h = 0;
      return h | 0;
    }
    function Fb(a, b) {
      a = a | 0;
      b = b | 0;
      var c = 0,
        d = 0,
        e = 0;
      e = Od(a | 0, b | 0, 45) | 0;
      H() | 0;
      if (!(ma(e & 127) | 0)) {
        e = 0;
        return e | 0;
      }
      e = Od(a | 0, b | 0, 52) | 0;
      H() | 0;
      e = e & 15;
      a: do { if (!e) { c = 0; }else {
        d = 1;
        while (1) {
          c = Od(a | 0, b | 0, (15 - d | 0) * 3 | 0) | 0;
          H() | 0;
          c = c & 7;
          if (c | 0) { break a; }
          if (d >>> 0 < e >>> 0) { d = d + 1 | 0; }else {
            c = 0;
            break;
          }
        }
      } } while (0);
      e = (c | 0) == 0 & 1;
      return e | 0;
    }
    function Gb(a, c, d, e) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      var f = 0,
        g = 0,
        h = 0,
        i = 0;
      h = T;
      T = T + 16 | 0;
      g = h;
      dc(g, a, c, d);
      c = g;
      a = b[c >> 2] | 0;
      c = b[c + 4 >> 2] | 0;
      if ((a | 0) == 0 & (c | 0) == 0) {
        T = h;
        return 0;
      }
      f = 0;
      d = 0;
      do {
        i = e + (f << 3) | 0;
        b[i >> 2] = a;
        b[i + 4 >> 2] = c;
        f = Ed(f | 0, d | 0, 1, 0) | 0;
        d = H() | 0;
        fc(g);
        i = g;
        a = b[i >> 2] | 0;
        c = b[i + 4 >> 2] | 0;
      } while (!((a | 0) == 0 & (c | 0) == 0));
      T = h;
      return 0;
    }
    function Hb(a, b, c, d) {
      a = a | 0;
      b = b | 0;
      c = c | 0;
      d = d | 0;
      if ((d | 0) < (c | 0)) {
        c = b;
        d = a;
        G(c | 0);
        return d | 0;
      }
      c = Pd(-1, -1, ((d - c | 0) * 3 | 0) + 3 | 0) | 0;
      d = Pd(~c | 0, ~(H() | 0) | 0, (15 - d | 0) * 3 | 0) | 0;
      c = ~(H() | 0) & b;
      d = ~d & a;
      G(c | 0);
      return d | 0;
    }
    function Ib(a, c, d, e) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      var f = 0;
      f = Od(a | 0, c | 0, 52) | 0;
      H() | 0;
      f = f & 15;
      if (!((d | 0) < 16 & (f | 0) <= (d | 0))) {
        e = 4;
        return e | 0;
      }
      if ((f | 0) < (d | 0)) {
        f = Pd(-1, -1, ((d + -1 - f | 0) * 3 | 0) + 3 | 0) | 0;
        f = Pd(~f | 0, ~(H() | 0) | 0, (15 - d | 0) * 3 | 0) | 0;
        c = ~(H() | 0) & c;
        a = ~f & a;
      }
      f = Pd(d | 0, 0, 52) | 0;
      d = c & -15728641 | (H() | 0);
      b[e >> 2] = a | f;
      b[e + 4 >> 2] = d;
      e = 0;
      return e | 0;
    }
    function Jb(a, c, d, e) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      var f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0,
        m = 0,
        n = 0,
        o = 0,
        p = 0,
        q = 0,
        r = 0,
        s = 0,
        t = 0,
        u = 0,
        v = 0,
        w = 0,
        x = 0,
        y = 0,
        z = 0,
        A = 0,
        B = 0,
        C = 0,
        D = 0,
        E = 0;
      if ((d | 0) == 0 & (e | 0) == 0) {
        E = 0;
        return E | 0;
      }
      f = a;
      g = b[f >> 2] | 0;
      f = b[f + 4 >> 2] | 0;
      if (0 == 0 & (f & 15728640 | 0) == 0) {
        if (!((e | 0) > 0 | (e | 0) == 0 & d >>> 0 > 0)) {
          E = 0;
          return E | 0;
        }
        E = c;
        b[E >> 2] = g;
        b[E + 4 >> 2] = f;
        if ((d | 0) == 1 & (e | 0) == 0) {
          E = 0;
          return E | 0;
        }
        f = 1;
        g = 0;
        do {
          C = a + (f << 3) | 0;
          D = b[C + 4 >> 2] | 0;
          E = c + (f << 3) | 0;
          b[E >> 2] = b[C >> 2];
          b[E + 4 >> 2] = D;
          f = Ed(f | 0, g | 0, 1, 0) | 0;
          g = H() | 0;
        } while ((g | 0) < (e | 0) | (g | 0) == (e | 0) & f >>> 0 < d >>> 0);
        f = 0;
        return f | 0;
      }
      B = d << 3;
      D = Bd(B) | 0;
      if (!D) {
        E = 13;
        return E | 0;
      }
      Ud(D | 0, a | 0, B | 0) | 0;
      C = Dd(d, 8) | 0;
      if (!C) {
        Cd(D);
        E = 13;
        return E | 0;
      }
      a: while (1) {
        f = D;
        k = b[f >> 2] | 0;
        f = b[f + 4 >> 2] | 0;
        z = Od(k | 0, f | 0, 52) | 0;
        H() | 0;
        z = z & 15;
        A = z + -1 | 0;
        y = (z | 0) != 0;
        x = (e | 0) > 0 | (e | 0) == 0 & d >>> 0 > 0;
        b: do { if (y & x) {
          t = Pd(A | 0, 0, 52) | 0;
          u = H() | 0;
          if (A >>> 0 > 15) {
            if (!((k | 0) == 0 & (f | 0) == 0)) {
              E = 16;
              break a;
            }
            g = 0;
            a = 0;
            while (1) {
              g = Ed(g | 0, a | 0, 1, 0) | 0;
              a = H() | 0;
              if (!((a | 0) < (e | 0) | (a | 0) == (e | 0) & g >>> 0 < d >>> 0)) { break b; }
              h = D + (g << 3) | 0;
              w = b[h >> 2] | 0;
              h = b[h + 4 >> 2] | 0;
              if (!((w | 0) == 0 & (h | 0) == 0)) {
                f = h;
                E = 16;
                break a;
              }
            }
          }
          i = k;
          a = f;
          g = 0;
          h = 0;
          while (1) {
            if (!((i | 0) == 0 & (a | 0) == 0)) {
              if (!(0 == 0 & (a & 117440512 | 0) == 0)) {
                E = 21;
                break a;
              }
              l = Od(i | 0, a | 0, 52) | 0;
              H() | 0;
              l = l & 15;
              if ((l | 0) < (A | 0)) {
                f = 12;
                E = 27;
                break a;
              }
              if ((l | 0) != (A | 0)) {
                i = i | t;
                a = a & -15728641 | u;
                if (l >>> 0 >= z >>> 0) {
                  j = A;
                  do {
                    w = Pd(7, 0, (14 - j | 0) * 3 | 0) | 0;
                    j = j + 1 | 0;
                    i = w | i;
                    a = H() | 0 | a;
                  } while (j >>> 0 < l >>> 0);
                }
              }
              n = Md(i | 0, a | 0, d | 0, e | 0) | 0;
              o = H() | 0;
              j = C + (n << 3) | 0;
              l = j;
              m = b[l >> 2] | 0;
              l = b[l + 4 >> 2] | 0;
              if (!((m | 0) == 0 & (l | 0) == 0)) {
                r = 0;
                s = 0;
                do {
                  if ((r | 0) > (e | 0) | (r | 0) == (e | 0) & s >>> 0 > d >>> 0) {
                    E = 31;
                    break a;
                  }
                  if ((m | 0) == (i | 0) & (l & -117440513 | 0) == (a | 0)) {
                    p = Od(m | 0, l | 0, 56) | 0;
                    H() | 0;
                    p = p & 7;
                    q = p + 1 | 0;
                    w = Od(m | 0, l | 0, 45) | 0;
                    H() | 0;
                    c: do { if (!(ma(w & 127) | 0)) { l = 7; }else {
                      m = Od(m | 0, l | 0, 52) | 0;
                      H() | 0;
                      m = m & 15;
                      if (!m) {
                        l = 6;
                        break;
                      }
                      l = 1;
                      while (1) {
                        w = Pd(7, 0, (15 - l | 0) * 3 | 0) | 0;
                        if (!((w & i | 0) == 0 & ((H() | 0) & a | 0) == 0)) {
                          l = 7;
                          break c;
                        }
                        if (l >>> 0 < m >>> 0) { l = l + 1 | 0; }else {
                          l = 6;
                          break;
                        }
                      }
                    } } while (0);
                    if ((p + 2 | 0) >>> 0 > l >>> 0) {
                      E = 41;
                      break a;
                    }
                    w = Pd(q | 0, 0, 56) | 0;
                    a = H() | 0 | a & -117440513;
                    v = j;
                    b[v >> 2] = 0;
                    b[v + 4 >> 2] = 0;
                    i = w | i;
                  } else {
                    n = Ed(n | 0, o | 0, 1, 0) | 0;
                    n = Ld(n | 0, H() | 0, d | 0, e | 0) | 0;
                    o = H() | 0;
                  }
                  s = Ed(s | 0, r | 0, 1, 0) | 0;
                  r = H() | 0;
                  j = C + (n << 3) | 0;
                  l = j;
                  m = b[l >> 2] | 0;
                  l = b[l + 4 >> 2] | 0;
                } while (!((m | 0) == 0 & (l | 0) == 0));
              }
              w = j;
              b[w >> 2] = i;
              b[w + 4 >> 2] = a;
            }
            g = Ed(g | 0, h | 0, 1, 0) | 0;
            h = H() | 0;
            if (!((h | 0) < (e | 0) | (h | 0) == (e | 0) & g >>> 0 < d >>> 0)) { break b; }
            a = D + (g << 3) | 0;
            i = b[a >> 2] | 0;
            a = b[a + 4 >> 2] | 0;
          }
        } } while (0);
        w = Ed(d | 0, e | 0, 5, 0) | 0;
        v = H() | 0;
        if (v >>> 0 < 0 | (v | 0) == 0 & w >>> 0 < 11) {
          E = 85;
          break;
        }
        w = Id(d | 0, e | 0, 6, 0) | 0;
        H() | 0;
        w = Dd(w, 8) | 0;
        if (!w) {
          E = 48;
          break;
        }
        do { if (x) {
          q = 0;
          a = 0;
          p = 0;
          r = 0;
          while (1) {
            l = C + (q << 3) | 0;
            h = l;
            g = b[h >> 2] | 0;
            h = b[h + 4 >> 2] | 0;
            if (!((g | 0) == 0 & (h | 0) == 0)) {
              m = Od(g | 0, h | 0, 56) | 0;
              H() | 0;
              m = m & 7;
              i = m + 1 | 0;
              n = h & -117440513;
              v = Od(g | 0, h | 0, 45) | 0;
              H() | 0;
              d: do { if (ma(v & 127) | 0) {
                o = Od(g | 0, h | 0, 52) | 0;
                H() | 0;
                o = o & 15;
                if (o | 0) {
                  j = 1;
                  while (1) {
                    v = Pd(7, 0, (15 - j | 0) * 3 | 0) | 0;
                    if (!((g & v | 0) == 0 & (n & (H() | 0) | 0) == 0)) { break d; }
                    if (j >>> 0 < o >>> 0) { j = j + 1 | 0; }else { break; }
                  }
                }
                h = Pd(i | 0, 0, 56) | 0;
                g = h | g;
                h = H() | 0 | n;
                i = l;
                b[i >> 2] = g;
                b[i + 4 >> 2] = h;
                i = m + 2 | 0;
              } } while (0);
              if ((i | 0) == 7) {
                v = w + (a << 3) | 0;
                b[v >> 2] = g;
                b[v + 4 >> 2] = h & -117440513;
                a = Ed(a | 0, p | 0, 1, 0) | 0;
                v = H() | 0;
              } else { v = p; }
            } else { v = p; }
            q = Ed(q | 0, r | 0, 1, 0) | 0;
            r = H() | 0;
            if (!((r | 0) < (e | 0) | (r | 0) == (e | 0) & q >>> 0 < d >>> 0)) { break; }else { p = v; }
          }
          if (x) {
            s = A >>> 0 > 15;
            t = Pd(A | 0, 0, 52) | 0;
            u = H() | 0;
            if (!y) {
              g = 0;
              j = 0;
              i = 0;
              h = 0;
              while (1) {
                if (!((k | 0) == 0 & (f | 0) == 0)) {
                  A = c + (g << 3) | 0;
                  b[A >> 2] = k;
                  b[A + 4 >> 2] = f;
                  g = Ed(g | 0, j | 0, 1, 0) | 0;
                  j = H() | 0;
                }
                i = Ed(i | 0, h | 0, 1, 0) | 0;
                h = H() | 0;
                if (!((h | 0) < (e | 0) | (h | 0) == (e | 0) & i >>> 0 < d >>> 0)) { break; }
                f = D + (i << 3) | 0;
                k = b[f >> 2] | 0;
                f = b[f + 4 >> 2] | 0;
              }
              f = v;
              break;
            }
            g = 0;
            j = 0;
            h = 0;
            i = 0;
            while (1) {
              do { if (!((k | 0) == 0 & (f | 0) == 0)) {
                o = Od(k | 0, f | 0, 52) | 0;
                H() | 0;
                o = o & 15;
                if (s | (o | 0) < (A | 0)) {
                  E = 80;
                  break a;
                }
                if ((o | 0) != (A | 0)) {
                  l = k | t;
                  m = f & -15728641 | u;
                  if (o >>> 0 >= z >>> 0) {
                    n = A;
                    do {
                      y = Pd(7, 0, (14 - n | 0) * 3 | 0) | 0;
                      n = n + 1 | 0;
                      l = y | l;
                      m = H() | 0 | m;
                    } while (n >>> 0 < o >>> 0);
                  }
                } else {
                  l = k;
                  m = f;
                }
                p = Md(l | 0, m | 0, d | 0, e | 0) | 0;
                n = 0;
                o = 0;
                r = H() | 0;
                do {
                  if ((n | 0) > (e | 0) | (n | 0) == (e | 0) & o >>> 0 > d >>> 0) {
                    E = 81;
                    break a;
                  }
                  y = C + (p << 3) | 0;
                  q = b[y + 4 >> 2] | 0;
                  if ((q & -117440513 | 0) == (m | 0) ? (b[y >> 2] | 0) == (l | 0) : 0) {
                    E = 65;
                    break;
                  }
                  y = Ed(p | 0, r | 0, 1, 0) | 0;
                  p = Ld(y | 0, H() | 0, d | 0, e | 0) | 0;
                  r = H() | 0;
                  o = Ed(o | 0, n | 0, 1, 0) | 0;
                  n = H() | 0;
                  y = C + (p << 3) | 0;
                } while (!((b[y >> 2] | 0) == (l | 0) ? (b[y + 4 >> 2] | 0) == (m | 0) : 0));
                if ((E | 0) == 65 ? (E = 0, 0 == 0 & (q & 117440512 | 0) == 100663296) : 0) { break; }
                y = c + (g << 3) | 0;
                b[y >> 2] = k;
                b[y + 4 >> 2] = f;
                g = Ed(g | 0, j | 0, 1, 0) | 0;
                j = H() | 0;
              } } while (0);
              h = Ed(h | 0, i | 0, 1, 0) | 0;
              i = H() | 0;
              if (!((i | 0) < (e | 0) | (i | 0) == (e | 0) & h >>> 0 < d >>> 0)) { break; }
              f = D + (h << 3) | 0;
              k = b[f >> 2] | 0;
              f = b[f + 4 >> 2] | 0;
            }
            f = v;
          } else {
            g = 0;
            f = v;
          }
        } else {
          g = 0;
          a = 0;
          f = 0;
        } } while (0);
        Vd(C | 0, 0, B | 0) | 0;
        Ud(D | 0, w | 0, a << 3 | 0) | 0;
        Cd(w);
        if ((a | 0) == 0 & (f | 0) == 0) {
          E = 89;
          break;
        } else {
          c = c + (g << 3) | 0;
          e = f;
          d = a;
        }
      }
      if ((E | 0) == 16) {
        if (0 == 0 & (f & 117440512 | 0) == 0) {
          f = 4;
          E = 27;
        } else { E = 21; }
      } else if ((E | 0) == 31) { I(27795, 27122, 529, 27132); }else if ((E | 0) == 41) {
        Cd(D);
        Cd(C);
        E = 10;
        return E | 0;
      } else if ((E | 0) == 48) {
        Cd(D);
        Cd(C);
        E = 13;
        return E | 0;
      } else if ((E | 0) == 80) { I(27795, 27122, 620, 27132); }else if ((E | 0) == 81) { I(27795, 27122, 632, 27132); }else if ((E | 0) == 85) {
        Ud(c | 0, D | 0, d << 3 | 0) | 0;
        E = 89;
      }
      if ((E | 0) == 21) {
        Cd(D);
        Cd(C);
        E = 5;
        return E | 0;
      } else if ((E | 0) == 27) {
        Cd(D);
        Cd(C);
        E = f;
        return E | 0;
      } else if ((E | 0) == 89) {
        Cd(D);
        Cd(C);
        E = 0;
        return E | 0;
      }
      return 0;
    }
    function Kb(a, c, d, e, f, g, h) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      f = f | 0;
      g = g | 0;
      h = h | 0;
      var i = 0,
        j = 0,
        k = 0,
        l = 0,
        m = 0,
        n = 0,
        o = 0,
        p = 0,
        q = 0;
      q = T;
      T = T + 16 | 0;
      p = q;
      if (!((d | 0) > 0 | (d | 0) == 0 & c >>> 0 > 0)) {
        p = 0;
        T = q;
        return p | 0;
      }
      if ((h | 0) >= 16) {
        p = 12;
        T = q;
        return p | 0;
      }
      n = 0;
      o = 0;
      m = 0;
      i = 0;
      a: while (1) {
        k = a + (n << 3) | 0;
        j = b[k >> 2] | 0;
        k = b[k + 4 >> 2] | 0;
        l = Od(j | 0, k | 0, 52) | 0;
        H() | 0;
        if ((l & 15 | 0) > (h | 0)) {
          i = 12;
          j = 11;
          break;
        }
        dc(p, j, k, h);
        l = p;
        k = b[l >> 2] | 0;
        l = b[l + 4 >> 2] | 0;
        if ((k | 0) == 0 & (l | 0) == 0) { j = m; }else {
          j = m;
          do {
            if (!((i | 0) < (g | 0) | (i | 0) == (g | 0) & j >>> 0 < f >>> 0)) {
              j = 10;
              break a;
            }
            m = e + (j << 3) | 0;
            b[m >> 2] = k;
            b[m + 4 >> 2] = l;
            j = Ed(j | 0, i | 0, 1, 0) | 0;
            i = H() | 0;
            fc(p);
            m = p;
            k = b[m >> 2] | 0;
            l = b[m + 4 >> 2] | 0;
          } while (!((k | 0) == 0 & (l | 0) == 0));
        }
        n = Ed(n | 0, o | 0, 1, 0) | 0;
        o = H() | 0;
        if (!((o | 0) < (d | 0) | (o | 0) == (d | 0) & n >>> 0 < c >>> 0)) {
          i = 0;
          j = 11;
          break;
        } else { m = j; }
      }
      if ((j | 0) == 10) {
        p = 14;
        T = q;
        return p | 0;
      } else if ((j | 0) == 11) {
        T = q;
        return i | 0;
      }
      return 0;
    }
    function Lb(a, c, d, e, f) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      f = f | 0;
      var g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0,
        m = 0,
        n = 0;
      n = T;
      T = T + 16 | 0;
      m = n;
      a: do { if ((d | 0) > 0 | (d | 0) == 0 & c >>> 0 > 0) {
        k = 0;
        h = 0;
        g = 0;
        l = 0;
        while (1) {
          j = a + (k << 3) | 0;
          i = b[j >> 2] | 0;
          j = b[j + 4 >> 2] | 0;
          if (!((i | 0) == 0 & (j | 0) == 0)) {
            j = (Eb(i, j, e, m) | 0) == 0;
            i = m;
            h = Ed(b[i >> 2] | 0, b[i + 4 >> 2] | 0, h | 0, g | 0) | 0;
            g = H() | 0;
            if (!j) {
              g = 12;
              break;
            }
          }
          k = Ed(k | 0, l | 0, 1, 0) | 0;
          l = H() | 0;
          if (!((l | 0) < (d | 0) | (l | 0) == (d | 0) & k >>> 0 < c >>> 0)) { break a; }
        }
        T = n;
        return g | 0;
      } else {
        h = 0;
        g = 0;
      } } while (0);
      b[f >> 2] = h;
      b[f + 4 >> 2] = g;
      f = 0;
      T = n;
      return f | 0;
    }
    function Mb(a, b) {
      a = a | 0;
      b = b | 0;
      b = Od(a | 0, b | 0, 52) | 0;
      H() | 0;
      return b & 1 | 0;
    }
    function Nb(a, b) {
      a = a | 0;
      b = b | 0;
      var c = 0,
        d = 0,
        e = 0;
      e = Od(a | 0, b | 0, 52) | 0;
      H() | 0;
      e = e & 15;
      if (!e) {
        e = 0;
        return e | 0;
      }
      d = 1;
      while (1) {
        c = Od(a | 0, b | 0, (15 - d | 0) * 3 | 0) | 0;
        H() | 0;
        c = c & 7;
        if (c | 0) {
          d = 5;
          break;
        }
        if (d >>> 0 < e >>> 0) { d = d + 1 | 0; }else {
          c = 0;
          d = 5;
          break;
        }
      }
      if ((d | 0) == 5) { return c | 0; }
      return 0;
    }
    function Ob(a, b) {
      a = a | 0;
      b = b | 0;
      var c = 0,
        d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0;
      i = Od(a | 0, b | 0, 52) | 0;
      H() | 0;
      i = i & 15;
      if (!i) {
        h = b;
        i = a;
        G(h | 0);
        return i | 0;
      }
      h = 1;
      c = 0;
      while (1) {
        f = (15 - h | 0) * 3 | 0;
        d = Pd(7, 0, f | 0) | 0;
        e = H() | 0;
        g = Od(a | 0, b | 0, f | 0) | 0;
        H() | 0;
        f = Pd(Za(g & 7) | 0, 0, f | 0) | 0;
        g = H() | 0;
        a = f | a & ~d;
        b = g | b & ~e;
        a: do { if (!c) { if (!((f & d | 0) == 0 & (g & e | 0) == 0)) {
          d = Od(a | 0, b | 0, 52) | 0;
          H() | 0;
          d = d & 15;
          if (!d) { c = 1; }else {
            c = 1;
            b: while (1) {
              g = Od(a | 0, b | 0, (15 - c | 0) * 3 | 0) | 0;
              H() | 0;
              switch (g & 7) {
                case 1:
                  break b;
                case 0:
                  break;
                default:
                  {
                    c = 1;
                    break a;
                  }
              }
              if (c >>> 0 < d >>> 0) { c = c + 1 | 0; }else {
                c = 1;
                break a;
              }
            }
            c = 1;
            while (1) {
              g = (15 - c | 0) * 3 | 0;
              e = Od(a | 0, b | 0, g | 0) | 0;
              H() | 0;
              f = Pd(7, 0, g | 0) | 0;
              b = b & ~(H() | 0);
              g = Pd(Za(e & 7) | 0, 0, g | 0) | 0;
              a = a & ~f | g;
              b = b | (H() | 0);
              if (c >>> 0 < d >>> 0) { c = c + 1 | 0; }else {
                c = 1;
                break;
              }
            }
          }
        } else { c = 0; } } } while (0);
        if (h >>> 0 < i >>> 0) { h = h + 1 | 0; }else { break; }
      }
      G(b | 0);
      return a | 0;
    }
    function Pb(a, b) {
      a = a | 0;
      b = b | 0;
      var c = 0,
        d = 0,
        e = 0,
        f = 0,
        g = 0;
      d = Od(a | 0, b | 0, 52) | 0;
      H() | 0;
      d = d & 15;
      if (!d) {
        c = b;
        d = a;
        G(c | 0);
        return d | 0;
      }
      c = 1;
      while (1) {
        f = (15 - c | 0) * 3 | 0;
        g = Od(a | 0, b | 0, f | 0) | 0;
        H() | 0;
        e = Pd(7, 0, f | 0) | 0;
        b = b & ~(H() | 0);
        f = Pd(Za(g & 7) | 0, 0, f | 0) | 0;
        a = f | a & ~e;
        b = H() | 0 | b;
        if (c >>> 0 < d >>> 0) { c = c + 1 | 0; }else { break; }
      }
      G(b | 0);
      return a | 0;
    }
    function Qb(a, b) {
      a = a | 0;
      b = b | 0;
      var c = 0,
        d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0;
      i = Od(a | 0, b | 0, 52) | 0;
      H() | 0;
      i = i & 15;
      if (!i) {
        h = b;
        i = a;
        G(h | 0);
        return i | 0;
      }
      h = 1;
      c = 0;
      while (1) {
        f = (15 - h | 0) * 3 | 0;
        d = Pd(7, 0, f | 0) | 0;
        e = H() | 0;
        g = Od(a | 0, b | 0, f | 0) | 0;
        H() | 0;
        f = Pd(_a(g & 7) | 0, 0, f | 0) | 0;
        g = H() | 0;
        a = f | a & ~d;
        b = g | b & ~e;
        a: do { if (!c) { if (!((f & d | 0) == 0 & (g & e | 0) == 0)) {
          d = Od(a | 0, b | 0, 52) | 0;
          H() | 0;
          d = d & 15;
          if (!d) { c = 1; }else {
            c = 1;
            b: while (1) {
              g = Od(a | 0, b | 0, (15 - c | 0) * 3 | 0) | 0;
              H() | 0;
              switch (g & 7) {
                case 1:
                  break b;
                case 0:
                  break;
                default:
                  {
                    c = 1;
                    break a;
                  }
              }
              if (c >>> 0 < d >>> 0) { c = c + 1 | 0; }else {
                c = 1;
                break a;
              }
            }
            c = 1;
            while (1) {
              e = (15 - c | 0) * 3 | 0;
              f = Pd(7, 0, e | 0) | 0;
              g = b & ~(H() | 0);
              b = Od(a | 0, b | 0, e | 0) | 0;
              H() | 0;
              b = Pd(_a(b & 7) | 0, 0, e | 0) | 0;
              a = a & ~f | b;
              b = g | (H() | 0);
              if (c >>> 0 < d >>> 0) { c = c + 1 | 0; }else {
                c = 1;
                break;
              }
            }
          }
        } else { c = 0; } } } while (0);
        if (h >>> 0 < i >>> 0) { h = h + 1 | 0; }else { break; }
      }
      G(b | 0);
      return a | 0;
    }
    function Rb(a, b) {
      a = a | 0;
      b = b | 0;
      var c = 0,
        d = 0,
        e = 0,
        f = 0,
        g = 0;
      d = Od(a | 0, b | 0, 52) | 0;
      H() | 0;
      d = d & 15;
      if (!d) {
        c = b;
        d = a;
        G(c | 0);
        return d | 0;
      }
      c = 1;
      while (1) {
        g = (15 - c | 0) * 3 | 0;
        f = Pd(7, 0, g | 0) | 0;
        e = b & ~(H() | 0);
        b = Od(a | 0, b | 0, g | 0) | 0;
        H() | 0;
        b = Pd(_a(b & 7) | 0, 0, g | 0) | 0;
        a = b | a & ~f;
        b = H() | 0 | e;
        if (c >>> 0 < d >>> 0) { c = c + 1 | 0; }else { break; }
      }
      G(b | 0);
      return a | 0;
    }
    function Sb(a, c) {
      a = a | 0;
      c = c | 0;
      var d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0;
      j = T;
      T = T + 64 | 0;
      i = j + 40 | 0;
      e = j + 24 | 0;
      f = j + 12 | 0;
      g = j;
      Pd(c | 0, 0, 52) | 0;
      d = H() | 0 | 134225919;
      if (!c) {
        if ((b[a + 4 >> 2] | 0) > 2) {
          h = 0;
          i = 0;
          G(h | 0);
          T = j;
          return i | 0;
        }
        if ((b[a + 8 >> 2] | 0) > 2) {
          h = 0;
          i = 0;
          G(h | 0);
          T = j;
          return i | 0;
        }
        if ((b[a + 12 >> 2] | 0) > 2) {
          h = 0;
          i = 0;
          G(h | 0);
          T = j;
          return i | 0;
        }
        Pd(oa(a) | 0, 0, 45) | 0;
        h = H() | 0 | d;
        i = -1;
        G(h | 0);
        T = j;
        return i | 0;
      }
      b[i >> 2] = b[a >> 2];
      b[i + 4 >> 2] = b[a + 4 >> 2];
      b[i + 8 >> 2] = b[a + 8 >> 2];
      b[i + 12 >> 2] = b[a + 12 >> 2];
      h = i + 4 | 0;
      if ((c | 0) > 0) {
        a = -1;
        while (1) {
          b[e >> 2] = b[h >> 2];
          b[e + 4 >> 2] = b[h + 4 >> 2];
          b[e + 8 >> 2] = b[h + 8 >> 2];
          if (!(c & 1)) {
            Ta(h);
            b[f >> 2] = b[h >> 2];
            b[f + 4 >> 2] = b[h + 4 >> 2];
            b[f + 8 >> 2] = b[h + 8 >> 2];
            Va(f);
          } else {
            Sa(h);
            b[f >> 2] = b[h >> 2];
            b[f + 4 >> 2] = b[h + 4 >> 2];
            b[f + 8 >> 2] = b[h + 8 >> 2];
            Ua(f);
          }
          Na(e, f, g);
          Ka(g);
          l = (15 - c | 0) * 3 | 0;
          k = Pd(7, 0, l | 0) | 0;
          d = d & ~(H() | 0);
          l = Pd(Pa(g) | 0, 0, l | 0) | 0;
          a = l | a & ~k;
          d = H() | 0 | d;
          if ((c | 0) > 1) { c = c + -1 | 0; }else { break; }
        }
      } else { a = -1; }
      a: do { if (((b[h >> 2] | 0) <= 2 ? (b[i + 8 >> 2] | 0) <= 2 : 0) ? (b[i + 12 >> 2] | 0) <= 2 : 0) {
        e = oa(i) | 0;
        c = Pd(e | 0, 0, 45) | 0;
        c = c | a;
        a = H() | 0 | d & -1040385;
        g = pa(i) | 0;
        if (!(ma(e) | 0)) {
          if ((g | 0) <= 0) { break; }
          f = 0;
          while (1) {
            e = Od(c | 0, a | 0, 52) | 0;
            H() | 0;
            e = e & 15;
            if (e) {
              d = 1;
              while (1) {
                l = (15 - d | 0) * 3 | 0;
                i = Od(c | 0, a | 0, l | 0) | 0;
                H() | 0;
                k = Pd(7, 0, l | 0) | 0;
                a = a & ~(H() | 0);
                l = Pd(Za(i & 7) | 0, 0, l | 0) | 0;
                c = c & ~k | l;
                a = a | (H() | 0);
                if (d >>> 0 < e >>> 0) { d = d + 1 | 0; }else { break; }
              }
            }
            f = f + 1 | 0;
            if ((f | 0) == (g | 0)) { break a; }
          }
        }
        f = Od(c | 0, a | 0, 52) | 0;
        H() | 0;
        f = f & 15;
        b: do { if (f) {
          d = 1;
          c: while (1) {
            l = Od(c | 0, a | 0, (15 - d | 0) * 3 | 0) | 0;
            H() | 0;
            switch (l & 7) {
              case 1:
                break c;
              case 0:
                break;
              default:
                break b;
            }
            if (d >>> 0 < f >>> 0) { d = d + 1 | 0; }else { break b; }
          }
          if (sa(e, b[i >> 2] | 0) | 0) {
            d = 1;
            while (1) {
              i = (15 - d | 0) * 3 | 0;
              k = Pd(7, 0, i | 0) | 0;
              l = a & ~(H() | 0);
              a = Od(c | 0, a | 0, i | 0) | 0;
              H() | 0;
              a = Pd(_a(a & 7) | 0, 0, i | 0) | 0;
              c = c & ~k | a;
              a = l | (H() | 0);
              if (d >>> 0 < f >>> 0) { d = d + 1 | 0; }else { break; }
            }
          } else {
            d = 1;
            while (1) {
              l = (15 - d | 0) * 3 | 0;
              i = Od(c | 0, a | 0, l | 0) | 0;
              H() | 0;
              k = Pd(7, 0, l | 0) | 0;
              a = a & ~(H() | 0);
              l = Pd(Za(i & 7) | 0, 0, l | 0) | 0;
              c = c & ~k | l;
              a = a | (H() | 0);
              if (d >>> 0 < f >>> 0) { d = d + 1 | 0; }else { break; }
            }
          }
        } } while (0);
        if ((g | 0) > 0) {
          d = 0;
          do {
            c = Ob(c, a) | 0;
            a = H() | 0;
            d = d + 1 | 0;
          } while ((d | 0) != (g | 0));
        }
      } else {
        c = 0;
        a = 0;
      } } while (0);
      k = a;
      l = c;
      G(k | 0);
      T = j;
      return l | 0;
    }
    function Tb(a) {
      a = a | 0;
      return (a | 0) % 2 | 0 | 0;
    }
    function Ub(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var e = 0,
        f = 0;
      f = T;
      T = T + 16 | 0;
      e = f;
      if (c >>> 0 > 15) {
        e = 4;
        T = f;
        return e | 0;
      }
      if ((b[a + 4 >> 2] & 2146435072 | 0) == 2146435072) {
        e = 3;
        T = f;
        return e | 0;
      }
      if ((b[a + 8 + 4 >> 2] & 2146435072 | 0) == 2146435072) {
        e = 3;
        T = f;
        return e | 0;
      }
      ob(a, c, e);
      c = Sb(e, c) | 0;
      e = H() | 0;
      b[d >> 2] = c;
      b[d + 4 >> 2] = e;
      if ((c | 0) == 0 & (e | 0) == 0) { I(27795, 27122, 959, 27145); }
      e = 0;
      T = f;
      return e | 0;
    }
    function Vb(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var e = 0,
        f = 0,
        g = 0,
        h = 0;
      f = d + 4 | 0;
      g = Od(a | 0, c | 0, 52) | 0;
      H() | 0;
      g = g & 15;
      h = Od(a | 0, c | 0, 45) | 0;
      H() | 0;
      e = (g | 0) == 0;
      if (!(ma(h & 127) | 0)) {
        if (e) {
          h = 0;
          return h | 0;
        }
        if ((b[f >> 2] | 0) == 0 ? (b[d + 8 >> 2] | 0) == 0 : 0) { e = (b[d + 12 >> 2] | 0) != 0 & 1; }else { e = 1; }
      } else if (e) {
        h = 1;
        return h | 0;
      } else { e = 1; }
      d = 1;
      while (1) {
        if (!(d & 1)) { Va(f); }else { Ua(f); }
        h = Od(a | 0, c | 0, (15 - d | 0) * 3 | 0) | 0;
        H() | 0;
        Wa(f, h & 7);
        if (d >>> 0 < g >>> 0) { d = d + 1 | 0; }else { break; }
      }
      return e | 0;
    }
    function Wb(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0;
      l = T;
      T = T + 16 | 0;
      j = l;
      k = Od(a | 0, c | 0, 45) | 0;
      H() | 0;
      k = k & 127;
      if (k >>> 0 > 121) {
        b[d >> 2] = 0;
        b[d + 4 >> 2] = 0;
        b[d + 8 >> 2] = 0;
        b[d + 12 >> 2] = 0;
        k = 5;
        T = l;
        return k | 0;
      }
      a: do { if ((ma(k) | 0) != 0 ? (g = Od(a | 0, c | 0, 52) | 0, H() | 0, g = g & 15, (g | 0) != 0) : 0) {
        e = 1;
        b: while (1) {
          i = Od(a | 0, c | 0, (15 - e | 0) * 3 | 0) | 0;
          H() | 0;
          switch (i & 7) {
            case 5:
              break b;
            case 0:
              break;
            default:
              {
                e = c;
                break a;
              }
          }
          if (e >>> 0 < g >>> 0) { e = e + 1 | 0; }else {
            e = c;
            break a;
          }
        }
        f = 1;
        e = c;
        while (1) {
          c = (15 - f | 0) * 3 | 0;
          h = Pd(7, 0, c | 0) | 0;
          i = e & ~(H() | 0);
          e = Od(a | 0, e | 0, c | 0) | 0;
          H() | 0;
          e = Pd(_a(e & 7) | 0, 0, c | 0) | 0;
          a = a & ~h | e;
          e = i | (H() | 0);
          if (f >>> 0 < g >>> 0) { f = f + 1 | 0; }else { break; }
        }
      } else { e = c; } } while (0);
      i = 7696 + (k * 28 | 0) | 0;
      b[d >> 2] = b[i >> 2];
      b[d + 4 >> 2] = b[i + 4 >> 2];
      b[d + 8 >> 2] = b[i + 8 >> 2];
      b[d + 12 >> 2] = b[i + 12 >> 2];
      if (!(Vb(a, e, d) | 0)) {
        k = 0;
        T = l;
        return k | 0;
      }
      h = d + 4 | 0;
      b[j >> 2] = b[h >> 2];
      b[j + 4 >> 2] = b[h + 4 >> 2];
      b[j + 8 >> 2] = b[h + 8 >> 2];
      g = Od(a | 0, e | 0, 52) | 0;
      H() | 0;
      i = g & 15;
      if (!(g & 1)) { g = i; }else {
        Va(h);
        g = i + 1 | 0;
      }
      if (!(ma(k) | 0)) { e = 0; }else {
        c: do { if (!i) { e = 0; }else {
          c = 1;
          while (1) {
            f = Od(a | 0, e | 0, (15 - c | 0) * 3 | 0) | 0;
            H() | 0;
            f = f & 7;
            if (f | 0) {
              e = f;
              break c;
            }
            if (c >>> 0 < i >>> 0) { c = c + 1 | 0; }else {
              e = 0;
              break;
            }
          }
        } } while (0);
        e = (e | 0) == 4 & 1;
      }
      if (!(vb(d, g, e, 0) | 0)) {
        if ((g | 0) != (i | 0)) {
          b[h >> 2] = b[j >> 2];
          b[h + 4 >> 2] = b[j + 4 >> 2];
          b[h + 8 >> 2] = b[j + 8 >> 2];
        }
      } else {
        if (ma(k) | 0) { do {} while ((vb(d, g, 0, 0) | 0) != 0); }
        if ((g | 0) != (i | 0)) { Ta(h); }
      }
      k = 0;
      T = l;
      return k | 0;
    }
    function Xb(a, b, c) {
      a = a | 0;
      b = b | 0;
      c = c | 0;
      var d = 0,
        e = 0,
        f = 0;
      f = T;
      T = T + 16 | 0;
      d = f;
      e = Wb(a, b, d) | 0;
      if (e | 0) {
        T = f;
        return e | 0;
      }
      e = Od(a | 0, b | 0, 52) | 0;
      H() | 0;
      sb(d, e & 15, c);
      e = 0;
      T = f;
      return e | 0;
    }
    function Yb(a, b, c) {
      a = a | 0;
      b = b | 0;
      c = c | 0;
      var d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0;
      g = T;
      T = T + 16 | 0;
      f = g;
      d = Wb(a, b, f) | 0;
      if (d | 0) {
        f = d;
        T = g;
        return f | 0;
      }
      d = Od(a | 0, b | 0, 45) | 0;
      H() | 0;
      d = (ma(d & 127) | 0) == 0;
      e = Od(a | 0, b | 0, 52) | 0;
      H() | 0;
      e = e & 15;
      a: do { if (!d) {
        if (e | 0) {
          d = 1;
          while (1) {
            h = Pd(7, 0, (15 - d | 0) * 3 | 0) | 0;
            if (!((h & a | 0) == 0 & ((H() | 0) & b | 0) == 0)) { break a; }
            if (d >>> 0 < e >>> 0) { d = d + 1 | 0; }else { break; }
          }
        }
        tb(f, e, 0, 5, c);
        h = 0;
        T = g;
        return h | 0;
      } } while (0);
      xb(f, e, 0, 6, c);
      h = 0;
      T = g;
      return h | 0;
    }
    function Zb(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var e = 0,
        f = 0,
        g = 0;
      f = Od(a | 0, c | 0, 45) | 0;
      H() | 0;
      if (!(ma(f & 127) | 0)) {
        f = 2;
        b[d >> 2] = f;
        return 0;
      }
      f = Od(a | 0, c | 0, 52) | 0;
      H() | 0;
      f = f & 15;
      if (!f) {
        f = 5;
        b[d >> 2] = f;
        return 0;
      }
      e = 1;
      while (1) {
        g = Pd(7, 0, (15 - e | 0) * 3 | 0) | 0;
        if (!((g & a | 0) == 0 & ((H() | 0) & c | 0) == 0)) {
          e = 2;
          a = 6;
          break;
        }
        if (e >>> 0 < f >>> 0) { e = e + 1 | 0; }else {
          e = 5;
          a = 6;
          break;
        }
      }
      if ((a | 0) == 6) {
        b[d >> 2] = e;
        return 0;
      }
      return 0;
    }
    function _b(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0,
        m = 0;
      m = T;
      T = T + 128 | 0;
      k = m + 112 | 0;
      g = m + 96 | 0;
      l = m;
      f = Od(a | 0, c | 0, 52) | 0;
      H() | 0;
      i = f & 15;
      b[k >> 2] = i;
      h = Od(a | 0, c | 0, 45) | 0;
      H() | 0;
      h = h & 127;
      a: do { if (ma(h) | 0) {
        if (i | 0) {
          e = 1;
          while (1) {
            j = Pd(7, 0, (15 - e | 0) * 3 | 0) | 0;
            if (!((j & a | 0) == 0 & ((H() | 0) & c | 0) == 0)) {
              f = 0;
              break a;
            }
            if (e >>> 0 < i >>> 0) { e = e + 1 | 0; }else { break; }
          }
        }
        if (!(f & 1)) {
          j = Pd(i + 1 | 0, 0, 52) | 0;
          l = H() | 0 | c & -15728641;
          k = Pd(7, 0, (14 - i | 0) * 3 | 0) | 0;
          l = _b((j | a) & ~k, l & ~(H() | 0), d) | 0;
          T = m;
          return l | 0;
        } else { f = 1; }
      } else { f = 0; } } while (0);
      e = Wb(a, c, g) | 0;
      if (!e) {
        if (f) {
          ub(g, k, l);
          j = 5;
        } else {
          yb(g, k, l);
          j = 6;
        }
        b: do { if (ma(h) | 0) {
          if (!i) { a = 5; }else {
            e = 1;
            while (1) {
              h = Pd(7, 0, (15 - e | 0) * 3 | 0) | 0;
              if (!((h & a | 0) == 0 & ((H() | 0) & c | 0) == 0)) {
                a = 2;
                break b;
              }
              if (e >>> 0 < i >>> 0) { e = e + 1 | 0; }else {
                a = 5;
                break;
              }
            }
          }
        } else { a = 2; } } while (0);
        Vd(d | 0, -1, a << 2 | 0) | 0;
        c: do { if (f) {
          g = 0;
          while (1) {
            h = l + (g << 4) | 0;
            wb(h, b[k >> 2] | 0) | 0;
            h = b[h >> 2] | 0;
            i = b[d >> 2] | 0;
            if ((i | 0) == -1 | (i | 0) == (h | 0)) { e = d; }else {
              f = 0;
              do {
                f = f + 1 | 0;
                if (f >>> 0 >= a >>> 0) {
                  e = 1;
                  break c;
                }
                e = d + (f << 2) | 0;
                i = b[e >> 2] | 0;
              } while (!((i | 0) == -1 | (i | 0) == (h | 0)));
            }
            b[e >> 2] = h;
            g = g + 1 | 0;
            if (g >>> 0 >= j >>> 0) {
              e = 0;
              break;
            }
          }
        } else {
          g = 0;
          while (1) {
            h = l + (g << 4) | 0;
            vb(h, b[k >> 2] | 0, 0, 1) | 0;
            h = b[h >> 2] | 0;
            i = b[d >> 2] | 0;
            if ((i | 0) == -1 | (i | 0) == (h | 0)) { e = d; }else {
              f = 0;
              do {
                f = f + 1 | 0;
                if (f >>> 0 >= a >>> 0) {
                  e = 1;
                  break c;
                }
                e = d + (f << 2) | 0;
                i = b[e >> 2] | 0;
              } while (!((i | 0) == -1 | (i | 0) == (h | 0)));
            }
            b[e >> 2] = h;
            g = g + 1 | 0;
            if (g >>> 0 >= j >>> 0) {
              e = 0;
              break;
            }
          }
        } } while (0);
      }
      l = e;
      T = m;
      return l | 0;
    }
    function $b() {
      return 12;
    }
    function ac(a, c) {
      a = a | 0;
      c = c | 0;
      var d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0;
      if (a >>> 0 > 15) {
        i = 4;
        return i | 0;
      }
      Pd(a | 0, 0, 52) | 0;
      i = H() | 0 | 134225919;
      if (!a) {
        d = 0;
        e = 0;
        do {
          if (ma(e) | 0) {
            Pd(e | 0, 0, 45) | 0;
            h = i | (H() | 0);
            a = c + (d << 3) | 0;
            b[a >> 2] = -1;
            b[a + 4 >> 2] = h;
            d = d + 1 | 0;
          }
          e = e + 1 | 0;
        } while ((e | 0) != 122);
        d = 0;
        return d | 0;
      }
      d = 0;
      h = 0;
      do {
        if (ma(h) | 0) {
          Pd(h | 0, 0, 45) | 0;
          e = 1;
          f = -1;
          g = i | (H() | 0);
          while (1) {
            j = Pd(7, 0, (15 - e | 0) * 3 | 0) | 0;
            f = f & ~j;
            g = g & ~(H() | 0);
            if ((e | 0) == (a | 0)) { break; }else { e = e + 1 | 0; }
          }
          j = c + (d << 3) | 0;
          b[j >> 2] = f;
          b[j + 4 >> 2] = g;
          d = d + 1 | 0;
        }
        h = h + 1 | 0;
      } while ((h | 0) != 122);
      d = 0;
      return d | 0;
    }
    function bc(a, c, d, e) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      var f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0,
        m = 0,
        n = 0,
        o = 0,
        p = 0,
        q = 0,
        r = 0,
        s = 0,
        t = 0;
      t = T;
      T = T + 16 | 0;
      r = t;
      s = Od(a | 0, c | 0, 52) | 0;
      H() | 0;
      s = s & 15;
      if (d >>> 0 > 15) {
        s = 4;
        T = t;
        return s | 0;
      }
      if ((s | 0) < (d | 0)) {
        s = 12;
        T = t;
        return s | 0;
      }
      if ((s | 0) != (d | 0)) {
        g = Pd(d | 0, 0, 52) | 0;
        g = g | a;
        i = H() | 0 | c & -15728641;
        if ((s | 0) > (d | 0)) {
          j = d;
          do {
            q = Pd(7, 0, (14 - j | 0) * 3 | 0) | 0;
            j = j + 1 | 0;
            g = q | g;
            i = H() | 0 | i;
          } while ((j | 0) < (s | 0));
          q = g;
        } else { q = g; }
      } else {
        q = a;
        i = c;
      }
      p = Od(q | 0, i | 0, 45) | 0;
      H() | 0;
      a: do { if (ma(p & 127) | 0) {
        j = Od(q | 0, i | 0, 52) | 0;
        H() | 0;
        j = j & 15;
        if (j | 0) {
          g = 1;
          while (1) {
            p = Pd(7, 0, (15 - g | 0) * 3 | 0) | 0;
            if (!((p & q | 0) == 0 & ((H() | 0) & i | 0) == 0)) {
              k = 33;
              break a;
            }
            if (g >>> 0 < j >>> 0) { g = g + 1 | 0; }else { break; }
          }
        }
        p = e;
        b[p >> 2] = 0;
        b[p + 4 >> 2] = 0;
        if ((s | 0) > (d | 0)) {
          p = c & -15728641;
          o = s;
          while (1) {
            n = o;
            o = o + -1 | 0;
            if (o >>> 0 > 15 | (s | 0) < (o | 0)) {
              k = 19;
              break;
            }
            if ((s | 0) != (o | 0)) {
              g = Pd(o | 0, 0, 52) | 0;
              g = g | a;
              j = H() | 0 | p;
              if ((s | 0) < (n | 0)) { m = g; }else {
                k = o;
                do {
                  m = Pd(7, 0, (14 - k | 0) * 3 | 0) | 0;
                  k = k + 1 | 0;
                  g = m | g;
                  j = H() | 0 | j;
                } while ((k | 0) < (s | 0));
                m = g;
              }
            } else {
              m = a;
              j = c;
            }
            l = Od(m | 0, j | 0, 45) | 0;
            H() | 0;
            if (!(ma(l & 127) | 0)) { g = 0; }else {
              l = Od(m | 0, j | 0, 52) | 0;
              H() | 0;
              l = l & 15;
              b: do { if (!l) { g = 0; }else {
                k = 1;
                while (1) {
                  g = Od(m | 0, j | 0, (15 - k | 0) * 3 | 0) | 0;
                  H() | 0;
                  g = g & 7;
                  if (g | 0) { break b; }
                  if (k >>> 0 < l >>> 0) { k = k + 1 | 0; }else {
                    g = 0;
                    break;
                  }
                }
              } } while (0);
              g = (g | 0) == 0 & 1;
            }
            j = Od(a | 0, c | 0, (15 - n | 0) * 3 | 0) | 0;
            H() | 0;
            j = j & 7;
            if ((j | 0) == 7) {
              f = 5;
              k = 42;
              break;
            }
            g = (g | 0) != 0;
            if ((j | 0) == 1 & g) {
              f = 5;
              k = 42;
              break;
            }
            m = j + (((j | 0) != 0 & g) << 31 >> 31) | 0;
            if (m | 0) {
              k = s - n | 0;
              k = Mc(7, 0, k, ((k | 0) < 0) << 31 >> 31) | 0;
              l = H() | 0;
              if (g) {
                g = Kd(k | 0, l | 0, 5, 0) | 0;
                g = Ed(g | 0, H() | 0, -5, -1) | 0;
                g = Id(g | 0, H() | 0, 6, 0) | 0;
                g = Ed(g | 0, H() | 0, 1, 0) | 0;
                j = H() | 0;
              } else {
                g = k;
                j = l;
              }
              n = m + -1 | 0;
              n = Kd(k | 0, l | 0, n | 0, ((n | 0) < 0) << 31 >> 31 | 0) | 0;
              n = Ed(g | 0, j | 0, n | 0, H() | 0) | 0;
              m = H() | 0;
              l = e;
              l = Ed(n | 0, m | 0, b[l >> 2] | 0, b[l + 4 >> 2] | 0) | 0;
              m = H() | 0;
              n = e;
              b[n >> 2] = l;
              b[n + 4 >> 2] = m;
            }
            if ((o | 0) <= (d | 0)) {
              k = 37;
              break;
            }
          }
          if ((k | 0) == 19) { I(27795, 27122, 1276, 27158); }else if ((k | 0) == 37) {
            h = e;
            f = b[h + 4 >> 2] | 0;
            h = b[h >> 2] | 0;
            break;
          } else if ((k | 0) == 42) {
            T = t;
            return f | 0;
          }
        } else {
          f = 0;
          h = 0;
        }
      } else { k = 33; } } while (0);
      c: do { if ((k | 0) == 33) {
        p = e;
        b[p >> 2] = 0;
        b[p + 4 >> 2] = 0;
        if ((s | 0) > (d | 0)) {
          g = s;
          while (1) {
            f = Od(a | 0, c | 0, (15 - g | 0) * 3 | 0) | 0;
            H() | 0;
            f = f & 7;
            if ((f | 0) == 7) {
              f = 5;
              break;
            }
            h = s - g | 0;
            h = Mc(7, 0, h, ((h | 0) < 0) << 31 >> 31) | 0;
            f = Kd(h | 0, H() | 0, f | 0, 0) | 0;
            h = H() | 0;
            p = e;
            h = Ed(b[p >> 2] | 0, b[p + 4 >> 2] | 0, f | 0, h | 0) | 0;
            f = H() | 0;
            p = e;
            b[p >> 2] = h;
            b[p + 4 >> 2] = f;
            g = g + -1 | 0;
            if ((g | 0) <= (d | 0)) { break c; }
          }
          T = t;
          return f | 0;
        } else {
          f = 0;
          h = 0;
        }
      } } while (0);
      if (Eb(q, i, s, r) | 0) { I(27795, 27122, 1236, 27173); }
      s = r;
      r = b[s + 4 >> 2] | 0;
      if (((f | 0) > -1 | (f | 0) == -1 & h >>> 0 > 4294967295) & ((r | 0) > (f | 0) | ((r | 0) == (f | 0) ? (b[s >> 2] | 0) >>> 0 > h >>> 0 : 0))) {
        s = 0;
        T = t;
        return s | 0;
      } else { I(27795, 27122, 1316, 27158); }
      return 0;
    }
    function cc(a, c, d, e, f, g) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      f = f | 0;
      g = g | 0;
      var h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0,
        m = 0,
        n = 0,
        o = 0,
        p = 0,
        q = 0;
      m = T;
      T = T + 16 | 0;
      h = m;
      if (f >>> 0 > 15) {
        g = 4;
        T = m;
        return g | 0;
      }
      i = Od(d | 0, e | 0, 52) | 0;
      H() | 0;
      i = i & 15;
      if ((i | 0) > (f | 0)) {
        g = 12;
        T = m;
        return g | 0;
      }
      if (Eb(d, e, f, h) | 0) { I(27795, 27122, 1236, 27173); }
      l = h;
      k = b[l + 4 >> 2] | 0;
      if (!(((c | 0) > -1 | (c | 0) == -1 & a >>> 0 > 4294967295) & ((k | 0) > (c | 0) | ((k | 0) == (c | 0) ? (b[l >> 2] | 0) >>> 0 > a >>> 0 : 0)))) {
        g = 2;
        T = m;
        return g | 0;
      }
      l = f - i | 0;
      f = Pd(f | 0, 0, 52) | 0;
      j = H() | 0 | e & -15728641;
      k = g;
      b[k >> 2] = f | d;
      b[k + 4 >> 2] = j;
      k = Od(d | 0, e | 0, 45) | 0;
      H() | 0;
      a: do { if (ma(k & 127) | 0) {
        if (i | 0) {
          h = 1;
          while (1) {
            k = Pd(7, 0, (15 - h | 0) * 3 | 0) | 0;
            if (!((k & d | 0) == 0 & ((H() | 0) & e | 0) == 0)) { break a; }
            if (h >>> 0 < i >>> 0) { h = h + 1 | 0; }else { break; }
          }
        }
        if ((l | 0) < 1) {
          g = 0;
          T = m;
          return g | 0;
        }
        k = i ^ 15;
        e = -1;
        j = 1;
        h = 1;
        while (1) {
          i = l - j | 0;
          i = Mc(7, 0, i, ((i | 0) < 0) << 31 >> 31) | 0;
          d = H() | 0;
          do { if (h) {
            h = Kd(i | 0, d | 0, 5, 0) | 0;
            h = Ed(h | 0, H() | 0, -5, -1) | 0;
            h = Id(h | 0, H() | 0, 6, 0) | 0;
            f = H() | 0;
            if ((c | 0) > (f | 0) | (c | 0) == (f | 0) & a >>> 0 > h >>> 0) {
              c = Ed(a | 0, c | 0, -1, -1) | 0;
              c = Fd(c | 0, H() | 0, h | 0, f | 0) | 0;
              h = H() | 0;
              n = g;
              p = b[n >> 2] | 0;
              n = b[n + 4 >> 2] | 0;
              q = (k + e | 0) * 3 | 0;
              o = Pd(7, 0, q | 0) | 0;
              n = n & ~(H() | 0);
              e = Id(c | 0, h | 0, i | 0, d | 0) | 0;
              a = H() | 0;
              f = Ed(e | 0, a | 0, 2, 0) | 0;
              q = Pd(f | 0, H() | 0, q | 0) | 0;
              n = H() | 0 | n;
              f = g;
              b[f >> 2] = q | p & ~o;
              b[f + 4 >> 2] = n;
              a = Kd(e | 0, a | 0, i | 0, d | 0) | 0;
              a = Fd(c | 0, h | 0, a | 0, H() | 0) | 0;
              h = 0;
              c = H() | 0;
              break;
            } else {
              q = g;
              o = b[q >> 2] | 0;
              q = b[q + 4 >> 2] | 0;
              p = Pd(7, 0, (k + e | 0) * 3 | 0) | 0;
              q = q & ~(H() | 0);
              h = g;
              b[h >> 2] = o & ~p;
              b[h + 4 >> 2] = q;
              h = 1;
              break;
            }
          } else {
            o = g;
            f = b[o >> 2] | 0;
            o = b[o + 4 >> 2] | 0;
            e = (k + e | 0) * 3 | 0;
            n = Pd(7, 0, e | 0) | 0;
            o = o & ~(H() | 0);
            q = Id(a | 0, c | 0, i | 0, d | 0) | 0;
            h = H() | 0;
            e = Pd(q | 0, h | 0, e | 0) | 0;
            o = H() | 0 | o;
            p = g;
            b[p >> 2] = e | f & ~n;
            b[p + 4 >> 2] = o;
            h = Kd(q | 0, h | 0, i | 0, d | 0) | 0;
            a = Fd(a | 0, c | 0, h | 0, H() | 0) | 0;
            h = 0;
            c = H() | 0;
          } } while (0);
          if ((l | 0) > (j | 0)) {
            e = ~j;
            j = j + 1 | 0;
          } else {
            c = 0;
            break;
          }
        }
        T = m;
        return c | 0;
      } } while (0);
      if ((l | 0) < 1) {
        q = 0;
        T = m;
        return q | 0;
      }
      f = i ^ 15;
      h = 1;
      while (1) {
        p = l - h | 0;
        p = Mc(7, 0, p, ((p | 0) < 0) << 31 >> 31) | 0;
        q = H() | 0;
        j = g;
        d = b[j >> 2] | 0;
        j = b[j + 4 >> 2] | 0;
        i = (f - h | 0) * 3 | 0;
        e = Pd(7, 0, i | 0) | 0;
        j = j & ~(H() | 0);
        n = Id(a | 0, c | 0, p | 0, q | 0) | 0;
        o = H() | 0;
        i = Pd(n | 0, o | 0, i | 0) | 0;
        j = H() | 0 | j;
        k = g;
        b[k >> 2] = i | d & ~e;
        b[k + 4 >> 2] = j;
        q = Kd(n | 0, o | 0, p | 0, q | 0) | 0;
        a = Fd(a | 0, c | 0, q | 0, H() | 0) | 0;
        c = H() | 0;
        if ((l | 0) <= (h | 0)) {
          c = 0;
          break;
        } else { h = h + 1 | 0; }
      }
      T = m;
      return c | 0;
    }
    function dc(a, c, d, e) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      var f = 0,
        g = 0,
        h = 0;
      f = Od(c | 0, d | 0, 52) | 0;
      H() | 0;
      f = f & 15;
      if ((c | 0) == 0 & (d | 0) == 0 | ((e | 0) > 15 | (f | 0) > (e | 0))) {
        g = -1;
        c = -1;
        d = 0;
        f = 0;
      } else {
        c = Hb(c, d, f + 1 | 0, e) | 0;
        h = (H() | 0) & -15728641;
        d = Pd(e | 0, 0, 52) | 0;
        d = c | d;
        h = h | (H() | 0);
        c = (Fb(d, h) | 0) == 0;
        g = f;
        c = c ? -1 : e;
        f = h;
      }
      h = a;
      b[h >> 2] = d;
      b[h + 4 >> 2] = f;
      b[a + 8 >> 2] = g;
      b[a + 12 >> 2] = c;
      return;
    }
    function ec(a, c, d, e) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      var f = 0,
        g = 0;
      f = Od(a | 0, c | 0, 52) | 0;
      H() | 0;
      f = f & 15;
      g = e + 8 | 0;
      b[g >> 2] = f;
      if ((a | 0) == 0 & (c | 0) == 0 | ((d | 0) > 15 | (f | 0) > (d | 0))) {
        d = e;
        b[d >> 2] = 0;
        b[d + 4 >> 2] = 0;
        b[g >> 2] = -1;
        b[e + 12 >> 2] = -1;
        return;
      }
      a = Hb(a, c, f + 1 | 0, d) | 0;
      g = (H() | 0) & -15728641;
      f = Pd(d | 0, 0, 52) | 0;
      f = a | f;
      g = g | (H() | 0);
      a = e;
      b[a >> 2] = f;
      b[a + 4 >> 2] = g;
      a = e + 12 | 0;
      if (!(Fb(f, g) | 0)) {
        b[a >> 2] = -1;
        return;
      } else {
        b[a >> 2] = d;
        return;
      }
    }
    function fc(a) {
      a = a | 0;
      var c = 0,
        d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0;
      d = a;
      c = b[d >> 2] | 0;
      d = b[d + 4 >> 2] | 0;
      if ((c | 0) == 0 & (d | 0) == 0) { return; }
      e = Od(c | 0, d | 0, 52) | 0;
      H() | 0;
      e = e & 15;
      i = Pd(1, 0, (e ^ 15) * 3 | 0) | 0;
      c = Ed(i | 0, H() | 0, c | 0, d | 0) | 0;
      d = H() | 0;
      i = a;
      b[i >> 2] = c;
      b[i + 4 >> 2] = d;
      i = a + 8 | 0;
      h = b[i >> 2] | 0;
      if ((e | 0) < (h | 0)) { return; }
      j = a + 12 | 0;
      g = e;
      while (1) {
        if ((g | 0) == (h | 0)) {
          e = 5;
          break;
        }
        k = (g | 0) == (b[j >> 2] | 0);
        f = (15 - g | 0) * 3 | 0;
        e = Od(c | 0, d | 0, f | 0) | 0;
        H() | 0;
        e = e & 7;
        if (k & ((e | 0) == 1 & 0 == 0)) {
          e = 7;
          break;
        }
        if (!((e | 0) == 7 & 0 == 0)) {
          e = 10;
          break;
        }
        k = Pd(1, 0, f | 0) | 0;
        c = Ed(c | 0, d | 0, k | 0, H() | 0) | 0;
        d = H() | 0;
        k = a;
        b[k >> 2] = c;
        b[k + 4 >> 2] = d;
        if ((g | 0) > (h | 0)) { g = g + -1 | 0; }else {
          e = 10;
          break;
        }
      }
      if ((e | 0) == 5) {
        k = a;
        b[k >> 2] = 0;
        b[k + 4 >> 2] = 0;
        b[i >> 2] = -1;
        b[j >> 2] = -1;
        return;
      } else if ((e | 0) == 7) {
        h = Pd(1, 0, f | 0) | 0;
        h = Ed(c | 0, d | 0, h | 0, H() | 0) | 0;
        i = H() | 0;
        k = a;
        b[k >> 2] = h;
        b[k + 4 >> 2] = i;
        b[j >> 2] = g + -1;
        return;
      } else if ((e | 0) == 10) { return; }
    }
    function gc(a) {
      a = +a;
      var b = 0.0;
      b = a < 0.0 ? a + 6.283185307179586 : a;
      return +(!(a >= 6.283185307179586) ? b : b + -6.283185307179586);
    }
    function hc(a, b) {
      a = a | 0;
      b = b | 0;
      if (!(+q(+(+e[a >> 3] - +e[b >> 3])) < 1.7453292519943298e-11)) {
        b = 0;
        return b | 0;
      }
      b = +q(+(+e[a + 8 >> 3] - +e[b + 8 >> 3])) < 1.7453292519943298e-11;
      return b | 0;
    }
    function ic(a, b) {
      a = +a;
      b = b | 0;
      switch (b | 0) {
        case 1:
          {
            a = a < 0.0 ? a + 6.283185307179586 : a;
            break;
          }
        case 2:
          {
            a = a > 0.0 ? a + -6.283185307179586 : a;
            break;
          }
        default:

      }
      return +a;
    }
    function jc(a, b) {
      a = a | 0;
      b = b | 0;
      var c = 0.0,
        d = 0.0,
        f = 0.0,
        g = 0.0;
      f = +e[b >> 3];
      d = +e[a >> 3];
      g = +u(+((f - d) * .5));
      c = +u(+((+e[b + 8 >> 3] - +e[a + 8 >> 3]) * .5));
      c = g * g + c * (+t(+f) * +t(+d) * c);
      return +(+z(+ +r(+c), + +r(+(1.0 - c))) * 2.0);
    }
    function kc(a, b) {
      a = a | 0;
      b = b | 0;
      var c = 0.0,
        d = 0.0,
        f = 0.0,
        g = 0.0;
      f = +e[b >> 3];
      d = +e[a >> 3];
      g = +u(+((f - d) * .5));
      c = +u(+((+e[b + 8 >> 3] - +e[a + 8 >> 3]) * .5));
      c = g * g + c * (+t(+f) * +t(+d) * c);
      return +(+z(+ +r(+c), + +r(+(1.0 - c))) * 2.0 * 6371.007180918475);
    }
    function lc(a, b) {
      a = a | 0;
      b = b | 0;
      var c = 0.0,
        d = 0.0,
        f = 0.0,
        g = 0.0;
      f = +e[b >> 3];
      d = +e[a >> 3];
      g = +u(+((f - d) * .5));
      c = +u(+((+e[b + 8 >> 3] - +e[a + 8 >> 3]) * .5));
      c = g * g + c * (+t(+f) * +t(+d) * c);
      return +(+z(+ +r(+c), + +r(+(1.0 - c))) * 2.0 * 6371.007180918475 * 1.0e3);
    }
    function mc(a, b) {
      a = a | 0;
      b = b | 0;
      var c = 0.0,
        d = 0.0,
        f = 0.0,
        g = 0.0,
        h = 0.0;
      g = +e[b >> 3];
      d = +t(+g);
      f = +e[b + 8 >> 3] - +e[a + 8 >> 3];
      h = d * +u(+f);
      c = +e[a >> 3];
      return + +z(+h, +(+u(+g) * +t(+c) - +t(+f) * (d * +u(+c))));
    }
    function nc(a, c, d, f) {
      a = a | 0;
      c = +c;
      d = +d;
      f = f | 0;
      var g = 0,
        h = 0.0,
        i = 0.0,
        j = 0.0;
      if (d < 1.0e-16) {
        b[f >> 2] = b[a >> 2];
        b[f + 4 >> 2] = b[a + 4 >> 2];
        b[f + 8 >> 2] = b[a + 8 >> 2];
        b[f + 12 >> 2] = b[a + 12 >> 2];
        return;
      }
      h = c < 0.0 ? c + 6.283185307179586 : c;
      h = !(c >= 6.283185307179586) ? h : h + -6.283185307179586;
      do { if (h < 1.0e-16) {
        c = +e[a >> 3] + d;
        e[f >> 3] = c;
        g = f;
      } else {
        g = +q(+(h + -3.141592653589793)) < 1.0e-16;
        c = +e[a >> 3];
        if (g) {
          c = c - d;
          e[f >> 3] = c;
          g = f;
          break;
        }
        i = +t(+d);
        d = +u(+d);
        c = i * +u(+c) + +t(+h) * (d * +t(+c));
        c = c > 1.0 ? 1.0 : c;
        c = +x(+(c < -1.0 ? -1.0 : c));
        e[f >> 3] = c;
        if (+q(+(c + -1.5707963267948966)) < 1.0e-16) {
          e[f >> 3] = 1.5707963267948966;
          e[f + 8 >> 3] = 0.0;
          return;
        }
        if (+q(+(c + 1.5707963267948966)) < 1.0e-16) {
          e[f >> 3] = -1.5707963267948966;
          e[f + 8 >> 3] = 0.0;
          return;
        }
        j = 1.0 / +t(+c);
        h = d * +u(+h) * j;
        d = +e[a >> 3];
        c = j * ((i - +u(+c) * +u(+d)) / +t(+d));
        i = h > 1.0 ? 1.0 : h;
        c = c > 1.0 ? 1.0 : c;
        c = +e[a + 8 >> 3] + +z(+(i < -1.0 ? -1.0 : i), +(c < -1.0 ? -1.0 : c));
        if (c > 3.141592653589793) { do { c = c + -6.283185307179586; } while (c > 3.141592653589793); }
        if (c < -3.141592653589793) { do { c = c + 6.283185307179586; } while (c < -3.141592653589793); }
        e[f + 8 >> 3] = c;
        return;
      } } while (0);
      if (+q(+(c + -1.5707963267948966)) < 1.0e-16) {
        e[g >> 3] = 1.5707963267948966;
        e[f + 8 >> 3] = 0.0;
        return;
      }
      if (+q(+(c + 1.5707963267948966)) < 1.0e-16) {
        e[g >> 3] = -1.5707963267948966;
        e[f + 8 >> 3] = 0.0;
        return;
      }
      c = +e[a + 8 >> 3];
      if (c > 3.141592653589793) { do { c = c + -6.283185307179586; } while (c > 3.141592653589793); }
      if (c < -3.141592653589793) { do { c = c + 6.283185307179586; } while (c < -3.141592653589793); }
      e[f + 8 >> 3] = c;
      return;
    }
    function oc(a, b) {
      a = a | 0;
      b = b | 0;
      if (a >>> 0 > 15) {
        b = 4;
        return b | 0;
      }
      e[b >> 3] = +e[20656 + (a << 3) >> 3];
      b = 0;
      return b | 0;
    }
    function pc(a, b) {
      a = a | 0;
      b = b | 0;
      if (a >>> 0 > 15) {
        b = 4;
        return b | 0;
      }
      e[b >> 3] = +e[20784 + (a << 3) >> 3];
      b = 0;
      return b | 0;
    }
    function qc(a, b) {
      a = a | 0;
      b = b | 0;
      if (a >>> 0 > 15) {
        b = 4;
        return b | 0;
      }
      e[b >> 3] = +e[20912 + (a << 3) >> 3];
      b = 0;
      return b | 0;
    }
    function rc(a, b) {
      a = a | 0;
      b = b | 0;
      if (a >>> 0 > 15) {
        b = 4;
        return b | 0;
      }
      e[b >> 3] = +e[21040 + (a << 3) >> 3];
      b = 0;
      return b | 0;
    }
    function sc(a, c) {
      a = a | 0;
      c = c | 0;
      var d = 0;
      if (a >>> 0 > 15) {
        c = 4;
        return c | 0;
      }
      d = Mc(7, 0, a, ((a | 0) < 0) << 31 >> 31) | 0;
      d = Kd(d | 0, H() | 0, 120, 0) | 0;
      a = H() | 0;
      b[c >> 2] = d | 2;
      b[c + 4 >> 2] = a;
      c = 0;
      return c | 0;
    }
    function tc(a, b, c) {
      a = a | 0;
      b = b | 0;
      c = c | 0;
      var d = 0.0,
        f = 0.0,
        g = 0.0,
        h = 0.0,
        i = 0.0,
        j = 0.0,
        k = 0.0,
        l = 0.0,
        m = 0.0,
        n = 0.0;
      n = +e[b >> 3];
      l = +e[a >> 3];
      j = +u(+((n - l) * .5));
      g = +e[b + 8 >> 3];
      k = +e[a + 8 >> 3];
      h = +u(+((g - k) * .5));
      i = +t(+l);
      m = +t(+n);
      h = j * j + h * (m * i * h);
      h = +z(+ +r(+h), + +r(+(1.0 - h))) * 2.0;
      j = +e[c >> 3];
      n = +u(+((j - n) * .5));
      d = +e[c + 8 >> 3];
      g = +u(+((d - g) * .5));
      f = +t(+j);
      g = n * n + g * (m * f * g);
      g = +z(+ +r(+g), + +r(+(1.0 - g))) * 2.0;
      j = +u(+((l - j) * .5));
      d = +u(+((k - d) * .5));
      d = j * j + d * (i * f * d);
      d = +z(+ +r(+d), + +r(+(1.0 - d))) * 2.0;
      f = (h + g + d) * .5;
      return +(+y(+ +r(+(+v(+(f * .5)) * +v(+((f - h) * .5)) * +v(+((f - g) * .5)) * +v(+((f - d) * .5))))) * 4.0);
    }
    function uc(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var f = 0.0,
        g = 0,
        h = 0,
        i = 0,
        j = 0;
      j = T;
      T = T + 192 | 0;
      h = j + 168 | 0;
      i = j;
      g = Xb(a, c, h) | 0;
      if (g | 0) {
        d = g;
        T = j;
        return d | 0;
      }
      if (Yb(a, c, i) | 0) { I(27795, 27190, 415, 27199); }
      c = b[i >> 2] | 0;
      if ((c | 0) > 0) {
        f = +tc(i + 8 | 0, i + 8 + (((c | 0) != 1 & 1) << 4) | 0, h) + 0.0;
        if ((c | 0) != 1) {
          a = 1;
          do {
            g = a;
            a = a + 1 | 0;
            f = f + +tc(i + 8 + (g << 4) | 0, i + 8 + (((a | 0) % (c | 0) | 0) << 4) | 0, h);
          } while ((a | 0) < (c | 0));
        }
      } else { f = 0.0; }
      e[d >> 3] = f;
      d = 0;
      T = j;
      return d | 0;
    }
    function vc(a, b, c) {
      a = a | 0;
      b = b | 0;
      c = c | 0;
      a = uc(a, b, c) | 0;
      if (a | 0) { return a | 0; }
      e[c >> 3] = +e[c >> 3] * 6371.007180918475 * 6371.007180918475;
      return a | 0;
    }
    function wc(a, b, c) {
      a = a | 0;
      b = b | 0;
      c = c | 0;
      a = uc(a, b, c) | 0;
      if (a | 0) { return a | 0; }
      e[c >> 3] = +e[c >> 3] * 6371.007180918475 * 6371.007180918475 * 1.0e3 * 1.0e3;
      return a | 0;
    }
    function xc(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var f = 0.0,
        g = 0.0,
        h = 0.0,
        i = 0,
        j = 0,
        k = 0.0,
        l = 0.0,
        m = 0.0;
      j = T;
      T = T + 176 | 0;
      i = j;
      a = nb(a, c, i) | 0;
      if (a | 0) {
        i = a;
        T = j;
        return i | 0;
      }
      e[d >> 3] = 0.0;
      a = b[i >> 2] | 0;
      if ((a | 0) <= 1) {
        i = 0;
        T = j;
        return i | 0;
      }
      c = a + -1 | 0;
      a = 0;
      f = +e[i + 8 >> 3];
      g = +e[i + 16 >> 3];
      h = 0.0;
      do {
        a = a + 1 | 0;
        l = f;
        f = +e[i + 8 + (a << 4) >> 3];
        m = +u(+((f - l) * .5));
        k = g;
        g = +e[i + 8 + (a << 4) + 8 >> 3];
        k = +u(+((g - k) * .5));
        k = m * m + k * (+t(+f) * +t(+l) * k);
        h = h + +z(+ +r(+k), + +r(+(1.0 - k))) * 2.0;
      } while ((a | 0) < (c | 0));
      e[d >> 3] = h;
      i = 0;
      T = j;
      return i | 0;
    }
    function yc(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var f = 0.0,
        g = 0.0,
        h = 0.0,
        i = 0,
        j = 0,
        k = 0.0,
        l = 0.0,
        m = 0.0;
      j = T;
      T = T + 176 | 0;
      i = j;
      a = nb(a, c, i) | 0;
      if (a | 0) {
        i = a;
        h = +e[d >> 3];
        h = h * 6371.007180918475;
        e[d >> 3] = h;
        T = j;
        return i | 0;
      }
      e[d >> 3] = 0.0;
      a = b[i >> 2] | 0;
      if ((a | 0) <= 1) {
        i = 0;
        h = 0.0;
        h = h * 6371.007180918475;
        e[d >> 3] = h;
        T = j;
        return i | 0;
      }
      c = a + -1 | 0;
      a = 0;
      f = +e[i + 8 >> 3];
      g = +e[i + 16 >> 3];
      h = 0.0;
      do {
        a = a + 1 | 0;
        l = f;
        f = +e[i + 8 + (a << 4) >> 3];
        m = +u(+((f - l) * .5));
        k = g;
        g = +e[i + 8 + (a << 4) + 8 >> 3];
        k = +u(+((g - k) * .5));
        k = m * m + k * (+t(+l) * +t(+f) * k);
        h = h + +z(+ +r(+k), + +r(+(1.0 - k))) * 2.0;
      } while ((a | 0) != (c | 0));
      e[d >> 3] = h;
      i = 0;
      m = h;
      m = m * 6371.007180918475;
      e[d >> 3] = m;
      T = j;
      return i | 0;
    }
    function zc(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var f = 0.0,
        g = 0.0,
        h = 0.0,
        i = 0,
        j = 0,
        k = 0.0,
        l = 0.0,
        m = 0.0;
      j = T;
      T = T + 176 | 0;
      i = j;
      a = nb(a, c, i) | 0;
      if (a | 0) {
        i = a;
        h = +e[d >> 3];
        h = h * 6371.007180918475;
        h = h * 1.0e3;
        e[d >> 3] = h;
        T = j;
        return i | 0;
      }
      e[d >> 3] = 0.0;
      a = b[i >> 2] | 0;
      if ((a | 0) <= 1) {
        i = 0;
        h = 0.0;
        h = h * 6371.007180918475;
        h = h * 1.0e3;
        e[d >> 3] = h;
        T = j;
        return i | 0;
      }
      c = a + -1 | 0;
      a = 0;
      f = +e[i + 8 >> 3];
      g = +e[i + 16 >> 3];
      h = 0.0;
      do {
        a = a + 1 | 0;
        l = f;
        f = +e[i + 8 + (a << 4) >> 3];
        m = +u(+((f - l) * .5));
        k = g;
        g = +e[i + 8 + (a << 4) + 8 >> 3];
        k = +u(+((g - k) * .5));
        k = m * m + k * (+t(+l) * +t(+f) * k);
        h = h + +z(+ +r(+k), + +r(+(1.0 - k))) * 2.0;
      } while ((a | 0) != (c | 0));
      e[d >> 3] = h;
      i = 0;
      m = h;
      m = m * 6371.007180918475;
      m = m * 1.0e3;
      e[d >> 3] = m;
      T = j;
      return i | 0;
    }
    function Ac(a) {
      a = a | 0;
      var c = 0,
        d = 0,
        e = 0;
      c = Dd(1, 12) | 0;
      if (!c) { I(27280, 27235, 49, 27293); }
      d = a + 4 | 0;
      e = b[d >> 2] | 0;
      if (e | 0) {
        e = e + 8 | 0;
        b[e >> 2] = c;
        b[d >> 2] = c;
        return c | 0;
      }
      if (b[a >> 2] | 0) { I(27310, 27235, 61, 27333); }
      e = a;
      b[e >> 2] = c;
      b[d >> 2] = c;
      return c | 0;
    }
    function Bc(a, c) {
      a = a | 0;
      c = c | 0;
      var d = 0,
        e = 0;
      e = Bd(24) | 0;
      if (!e) { I(27347, 27235, 78, 27361); }
      b[e >> 2] = b[c >> 2];
      b[e + 4 >> 2] = b[c + 4 >> 2];
      b[e + 8 >> 2] = b[c + 8 >> 2];
      b[e + 12 >> 2] = b[c + 12 >> 2];
      b[e + 16 >> 2] = 0;
      c = a + 4 | 0;
      d = b[c >> 2] | 0;
      if (d | 0) {
        b[d + 16 >> 2] = e;
        b[c >> 2] = e;
        return e | 0;
      }
      if (b[a >> 2] | 0) { I(27376, 27235, 82, 27361); }
      b[a >> 2] = e;
      b[c >> 2] = e;
      return e | 0;
    }
    function Cc(a) {
      a = a | 0;
      var c = 0,
        d = 0,
        e = 0,
        f = 0;
      if (!a) { return; }
      e = 1;
      while (1) {
        c = b[a >> 2] | 0;
        if (c | 0) { do {
          d = b[c >> 2] | 0;
          if (d | 0) { do {
            f = d;
            d = b[d + 16 >> 2] | 0;
            Cd(f);
          } while ((d | 0) != 0); }
          f = c;
          c = b[c + 8 >> 2] | 0;
          Cd(f);
        } while ((c | 0) != 0); }
        c = a;
        a = b[a + 8 >> 2] | 0;
        if (!e) { Cd(c); }
        if (!a) { break; }else { e = 0; }
      }
      return;
    }
    function Dc(a) {
      a = a | 0;
      var c = 0,
        d = 0,
        f = 0,
        g = 0,
        h = 0.0,
        i = 0,
        j = 0.0,
        k = 0.0,
        l = 0,
        m = 0,
        n = 0,
        o = 0,
        p = 0,
        r = 0,
        s = 0.0,
        t = 0.0,
        u = 0.0,
        v = 0.0,
        w = 0.0,
        x = 0.0,
        y = 0,
        z = 0,
        A = 0,
        B = 0,
        C = 0,
        D = 0,
        E = 0,
        F = 0,
        G = 0,
        H = 0,
        J = 0,
        K = 0;
      g = a + 8 | 0;
      if (b[g >> 2] | 0) {
        K = 1;
        return K | 0;
      }
      f = b[a >> 2] | 0;
      if (!f) {
        K = 0;
        return K | 0;
      }
      c = f;
      d = 0;
      do {
        d = d + 1 | 0;
        c = b[c + 8 >> 2] | 0;
      } while ((c | 0) != 0);
      if (d >>> 0 < 2) {
        K = 0;
        return K | 0;
      }
      H = Bd(d << 2) | 0;
      if (!H) { I(27396, 27235, 317, 27415); }
      G = Bd(d << 5) | 0;
      if (!G) { I(27437, 27235, 321, 27415); }
      b[a >> 2] = 0;
      z = a + 4 | 0;
      b[z >> 2] = 0;
      b[g >> 2] = 0;
      d = 0;
      F = 0;
      y = 0;
      n = 0;
      a: while (1) {
        m = b[f >> 2] | 0;
        if (m) {
          h = 0.0;
          i = m;
          do {
            k = +e[i + 8 >> 3];
            c = i;
            i = b[i + 16 >> 2] | 0;
            l = (i | 0) == 0;
            g = l ? m : i;
            j = +e[g + 8 >> 3];
            if (+q(+(k - j)) > 3.141592653589793) {
              K = 14;
              break;
            }
            h = h + (j - k) * (+e[c >> 3] + +e[g >> 3]);
          } while (!l);
          if ((K | 0) == 14) {
            K = 0;
            h = 0.0;
            c = m;
            do {
              x = +e[c + 8 >> 3];
              E = c + 16 | 0;
              D = b[E >> 2] | 0;
              D = (D | 0) == 0 ? m : D;
              w = +e[D + 8 >> 3];
              h = h + (+e[c >> 3] + +e[D >> 3]) * ((w < 0.0 ? w + 6.283185307179586 : w) - (x < 0.0 ? x + 6.283185307179586 : x));
              c = b[((c | 0) == 0 ? f : E) >> 2] | 0;
            } while ((c | 0) != 0);
          }
          if (h > 0.0) {
            b[H + (F << 2) >> 2] = f;
            F = F + 1 | 0;
            g = y;
            c = n;
          } else { K = 19; }
        } else { K = 19; }
        if ((K | 0) == 19) {
          K = 0;
          do { if (!d) {
            if (!n) {
              if (!(b[a >> 2] | 0)) {
                g = z;
                i = a;
                c = f;
                d = a;
                break;
              } else {
                K = 27;
                break a;
              }
            } else {
              g = z;
              i = n + 8 | 0;
              c = f;
              d = a;
              break;
            }
          } else {
            c = d + 8 | 0;
            if (b[c >> 2] | 0) {
              K = 21;
              break a;
            }
            d = Dd(1, 12) | 0;
            if (!d) {
              K = 23;
              break a;
            }
            b[c >> 2] = d;
            g = d + 4 | 0;
            i = d;
            c = n;
          } } while (0);
          b[i >> 2] = f;
          b[g >> 2] = f;
          i = G + (y << 5) | 0;
          l = b[f >> 2] | 0;
          if (l) {
            m = G + (y << 5) + 8 | 0;
            e[m >> 3] = 1797693134862315708145274.0e284;
            n = G + (y << 5) + 24 | 0;
            e[n >> 3] = 1797693134862315708145274.0e284;
            e[i >> 3] = -1797693134862315708145274.0e284;
            o = G + (y << 5) + 16 | 0;
            e[o >> 3] = -1797693134862315708145274.0e284;
            u = 1797693134862315708145274.0e284;
            v = -1797693134862315708145274.0e284;
            g = 0;
            p = l;
            k = 1797693134862315708145274.0e284;
            s = 1797693134862315708145274.0e284;
            t = -1797693134862315708145274.0e284;
            j = -1797693134862315708145274.0e284;
            while (1) {
              h = +e[p >> 3];
              x = +e[p + 8 >> 3];
              p = b[p + 16 >> 2] | 0;
              r = (p | 0) == 0;
              w = +e[(r ? l : p) + 8 >> 3];
              if (h < k) {
                e[m >> 3] = h;
                k = h;
              }
              if (x < s) {
                e[n >> 3] = x;
                s = x;
              }
              if (h > t) { e[i >> 3] = h; }else { h = t; }
              if (x > j) {
                e[o >> 3] = x;
                j = x;
              }
              u = x > 0.0 & x < u ? x : u;
              v = x < 0.0 & x > v ? x : v;
              g = g | +q(+(x - w)) > 3.141592653589793;
              if (r) { break; }else { t = h; }
            }
            if (g) {
              e[o >> 3] = v;
              e[n >> 3] = u;
            }
          } else {
            b[i >> 2] = 0;
            b[i + 4 >> 2] = 0;
            b[i + 8 >> 2] = 0;
            b[i + 12 >> 2] = 0;
            b[i + 16 >> 2] = 0;
            b[i + 20 >> 2] = 0;
            b[i + 24 >> 2] = 0;
            b[i + 28 >> 2] = 0;
          }
          g = y + 1 | 0;
        }
        E = f + 8 | 0;
        f = b[E >> 2] | 0;
        b[E >> 2] = 0;
        if (!f) {
          K = 45;
          break;
        } else {
          y = g;
          n = c;
        }
      }
      if ((K | 0) == 21) { I(27213, 27235, 35, 27247); }else if ((K | 0) == 23) { I(27267, 27235, 37, 27247); }else if ((K | 0) == 27) { I(27310, 27235, 61, 27333); }else if ((K | 0) == 45) {
        b: do { if ((F | 0) > 0) {
          E = (g | 0) == 0;
          C = g << 2;
          D = (a | 0) == 0;
          B = 0;
          c = 0;
          while (1) {
            A = b[H + (B << 2) >> 2] | 0;
            if (!E) {
              y = Bd(C) | 0;
              if (!y) {
                K = 50;
                break;
              }
              z = Bd(C) | 0;
              if (!z) {
                K = 52;
                break;
              }
              c: do { if (!D) {
                g = 0;
                d = 0;
                i = a;
                while (1) {
                  f = G + (g << 5) | 0;
                  if (Ec(b[i >> 2] | 0, f, b[A >> 2] | 0) | 0) {
                    b[y + (d << 2) >> 2] = i;
                    b[z + (d << 2) >> 2] = f;
                    r = d + 1 | 0;
                  } else { r = d; }
                  i = b[i + 8 >> 2] | 0;
                  if (!i) { break; }else {
                    g = g + 1 | 0;
                    d = r;
                  }
                }
                if ((r | 0) > 0) {
                  f = b[y >> 2] | 0;
                  if ((r | 0) == 1) { d = f; }else {
                    o = 0;
                    p = -1;
                    d = f;
                    n = f;
                    while (1) {
                      l = b[n >> 2] | 0;
                      f = 0;
                      i = 0;
                      while (1) {
                        g = b[b[y + (i << 2) >> 2] >> 2] | 0;
                        if ((g | 0) == (l | 0)) { m = f; }else { m = f + ((Ec(g, b[z + (i << 2) >> 2] | 0, b[l >> 2] | 0) | 0) & 1) | 0; }
                        i = i + 1 | 0;
                        if ((i | 0) == (r | 0)) { break; }else { f = m; }
                      }
                      g = (m | 0) > (p | 0);
                      d = g ? n : d;
                      f = o + 1 | 0;
                      if ((f | 0) == (r | 0)) { break c; }
                      o = f;
                      p = g ? m : p;
                      n = b[y + (f << 2) >> 2] | 0;
                    }
                  }
                } else { d = 0; }
              } else { d = 0; } } while (0);
              Cd(y);
              Cd(z);
              if (d) {
                g = d + 4 | 0;
                f = b[g >> 2] | 0;
                if (!f) {
                  if (b[d >> 2] | 0) {
                    K = 70;
                    break;
                  }
                } else { d = f + 8 | 0; }
                b[d >> 2] = A;
                b[g >> 2] = A;
              } else { K = 73; }
            } else { K = 73; }
            if ((K | 0) == 73) {
              K = 0;
              c = b[A >> 2] | 0;
              if (c | 0) { do {
                z = c;
                c = b[c + 16 >> 2] | 0;
                Cd(z);
              } while ((c | 0) != 0); }
              Cd(A);
              c = 1;
            }
            B = B + 1 | 0;
            if ((B | 0) >= (F | 0)) {
              J = c;
              break b;
            }
          }
          if ((K | 0) == 50) { I(27452, 27235, 249, 27471); }else if ((K | 0) == 52) { I(27490, 27235, 252, 27471); }else if ((K | 0) == 70) { I(27310, 27235, 61, 27333); }
        } else { J = 0; } } while (0);
        Cd(H);
        Cd(G);
        K = J;
        return K | 0;
      }
      return 0;
    }
    function Ec(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var f = 0.0,
        g = 0.0,
        h = 0.0,
        i = 0.0,
        j = 0.0,
        k = 0.0,
        l = 0.0,
        m = 0;
      if (!(Aa(c, d) | 0)) {
        a = 0;
        return a | 0;
      }
      c = ya(c) | 0;
      f = +e[d >> 3];
      g = +e[d + 8 >> 3];
      g = c & g < 0.0 ? g + 6.283185307179586 : g;
      a = b[a >> 2] | 0;
      if (!a) {
        a = 0;
        return a | 0;
      }
      if (c) {
        c = 0;
        l = g;
        d = a;
        a: while (1) {
          while (1) {
            i = +e[d >> 3];
            g = +e[d + 8 >> 3];
            d = d + 16 | 0;
            m = b[d >> 2] | 0;
            m = (m | 0) == 0 ? a : m;
            h = +e[m >> 3];
            j = +e[m + 8 >> 3];
            if (i > h) {
              k = i;
              i = j;
            } else {
              k = h;
              h = i;
              i = g;
              g = j;
            }
            f = f == h | f == k ? f + 2.220446049250313e-16 : f;
            if (!(f < h | f > k)) { break; }
            d = b[d >> 2] | 0;
            if (!d) {
              d = 22;
              break a;
            }
          }
          j = i < 0.0 ? i + 6.283185307179586 : i;
          i = g < 0.0 ? g + 6.283185307179586 : g;
          l = j == l | i == l ? l + -2.220446049250313e-16 : l;
          k = j + (i - j) * ((f - h) / (k - h));
          if ((k < 0.0 ? k + 6.283185307179586 : k) > l) { c = c ^ 1; }
          d = b[d >> 2] | 0;
          if (!d) {
            d = 22;
            break;
          }
        }
        if ((d | 0) == 22) { return c | 0; }
      } else {
        c = 0;
        l = g;
        d = a;
        b: while (1) {
          while (1) {
            i = +e[d >> 3];
            g = +e[d + 8 >> 3];
            d = d + 16 | 0;
            m = b[d >> 2] | 0;
            m = (m | 0) == 0 ? a : m;
            h = +e[m >> 3];
            j = +e[m + 8 >> 3];
            if (i > h) {
              k = i;
              i = j;
            } else {
              k = h;
              h = i;
              i = g;
              g = j;
            }
            f = f == h | f == k ? f + 2.220446049250313e-16 : f;
            if (!(f < h | f > k)) { break; }
            d = b[d >> 2] | 0;
            if (!d) {
              d = 22;
              break b;
            }
          }
          l = i == l | g == l ? l + -2.220446049250313e-16 : l;
          if (i + (g - i) * ((f - h) / (k - h)) > l) { c = c ^ 1; }
          d = b[d >> 2] | 0;
          if (!d) {
            d = 22;
            break;
          }
        }
        if ((d | 0) == 22) { return c | 0; }
      }
      return 0;
    }
    function Fc(c, d, e, f, g) {
      c = c | 0;
      d = d | 0;
      e = e | 0;
      f = f | 0;
      g = g | 0;
      var h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0,
        m = 0,
        n = 0,
        o = 0,
        p = 0,
        q = 0,
        r = 0,
        s = 0,
        t = 0,
        u = 0;
      u = T;
      T = T + 32 | 0;
      t = u + 16 | 0;
      s = u;
      h = Od(c | 0, d | 0, 52) | 0;
      H() | 0;
      h = h & 15;
      p = Od(e | 0, f | 0, 52) | 0;
      H() | 0;
      if ((h | 0) != (p & 15 | 0)) {
        t = 12;
        T = u;
        return t | 0;
      }
      l = Od(c | 0, d | 0, 45) | 0;
      H() | 0;
      l = l & 127;
      m = Od(e | 0, f | 0, 45) | 0;
      H() | 0;
      m = m & 127;
      if (l >>> 0 > 121 | m >>> 0 > 121) {
        t = 5;
        T = u;
        return t | 0;
      }
      p = (l | 0) != (m | 0);
      if (p) {
        j = ua(l, m) | 0;
        if ((j | 0) == 7) {
          t = 1;
          T = u;
          return t | 0;
        }
        k = ua(m, l) | 0;
        if ((k | 0) == 7) { I(27514, 27538, 161, 27548); }else {
          q = j;
          i = k;
        }
      } else {
        q = 0;
        i = 0;
      }
      n = ma(l) | 0;
      o = ma(m) | 0;
      b[t >> 2] = 0;
      b[t + 4 >> 2] = 0;
      b[t + 8 >> 2] = 0;
      b[t + 12 >> 2] = 0;
      do { if (!q) {
        Vb(e, f, t) | 0;
        if ((n | 0) != 0 & (o | 0) != 0) {
          if ((m | 0) != (l | 0)) { I(27621, 27538, 261, 27548); }
          i = Nb(c, d) | 0;
          h = Nb(e, f) | 0;
          if (!((i | 0) == 7 | (h | 0) == 7)) {
            if (!(a[22e3 + (i * 7 | 0) + h >> 0] | 0)) {
              i = b[21168 + (i * 28 | 0) + (h << 2) >> 2] | 0;
              if ((i | 0) > 0) {
                j = t + 4 | 0;
                h = 0;
                do {
                  Ya(j);
                  h = h + 1 | 0;
                } while ((h | 0) != (i | 0));
                r = 51;
              } else { r = 51; }
            } else { h = 1; }
          } else { h = 5; }
        } else { r = 51; }
      } else {
        m = b[4272 + (l * 28 | 0) + (q << 2) >> 2] | 0;
        j = (m | 0) > 0;
        if (!o) {
          if (j) {
            l = 0;
            k = e;
            j = f;
            do {
              k = Rb(k, j) | 0;
              j = H() | 0;
              i = _a(i) | 0;
              l = l + 1 | 0;
            } while ((l | 0) != (m | 0));
            m = i;
            l = k;
            k = j;
          } else {
            m = i;
            l = e;
            k = f;
          }
        } else if (j) {
          l = 0;
          k = e;
          j = f;
          do {
            k = Qb(k, j) | 0;
            j = H() | 0;
            i = _a(i) | 0;
            if ((i | 0) == 1) { i = _a(1) | 0; }
            l = l + 1 | 0;
          } while ((l | 0) != (m | 0));
          m = i;
          l = k;
          k = j;
        } else {
          m = i;
          l = e;
          k = f;
        }
        Vb(l, k, t) | 0;
        if (!p) { I(27563, 27538, 191, 27548); }
        j = (n | 0) != 0;
        i = (o | 0) != 0;
        if (j & i) { I(27590, 27538, 192, 27548); }
        if (!j) {
          if (i) {
            i = Nb(l, k) | 0;
            if ((i | 0) == 7) {
              h = 5;
              break;
            }
            if (a[22e3 + (i * 7 | 0) + m >> 0] | 0) {
              h = 1;
              break;
            }
            l = 0;
            k = b[21168 + (m * 28 | 0) + (i << 2) >> 2] | 0;
          } else {
            l = 0;
            k = 0;
          }
        } else {
          i = Nb(c, d) | 0;
          if ((i | 0) == 7) {
            h = 5;
            break;
          }
          if (a[22e3 + (i * 7 | 0) + q >> 0] | 0) {
            h = 1;
            break;
          }
          k = b[21168 + (i * 28 | 0) + (q << 2) >> 2] | 0;
          l = k;
        }
        if ((l | k | 0) < 0) { h = 5; }else {
          if ((k | 0) > 0) {
            j = t + 4 | 0;
            i = 0;
            do {
              Ya(j);
              i = i + 1 | 0;
            } while ((i | 0) != (k | 0));
          }
          b[s >> 2] = 0;
          b[s + 4 >> 2] = 0;
          b[s + 8 >> 2] = 0;
          Wa(s, q);
          if (h | 0) { while (1) {
            if (!(Tb(h) | 0)) { Va(s); }else { Ua(s); }
            if ((h | 0) > 1) { h = h + -1 | 0; }else { break; }
          } }
          if ((l | 0) > 0) {
            h = 0;
            do {
              Ya(s);
              h = h + 1 | 0;
            } while ((h | 0) != (l | 0));
          }
          r = t + 4 | 0;
          Ma(r, s, r);
          Ka(r);
          r = 51;
        }
      } } while (0);
      if ((r | 0) == 51) {
        h = t + 4 | 0;
        b[g >> 2] = b[h >> 2];
        b[g + 4 >> 2] = b[h + 4 >> 2];
        b[g + 8 >> 2] = b[h + 8 >> 2];
        h = 0;
      }
      t = h;
      T = u;
      return t | 0;
    }
    function Gc(a, c, d, e) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      var f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0,
        m = 0,
        n = 0,
        o = 0,
        p = 0,
        q = 0,
        r = 0,
        s = 0,
        t = 0,
        u = 0;
      q = T;
      T = T + 48 | 0;
      k = q + 36 | 0;
      h = q + 24 | 0;
      i = q + 12 | 0;
      j = q;
      f = Od(a | 0, c | 0, 52) | 0;
      H() | 0;
      f = f & 15;
      n = Od(a | 0, c | 0, 45) | 0;
      H() | 0;
      n = n & 127;
      if (n >>> 0 > 121) {
        e = 5;
        T = q;
        return e | 0;
      }
      l = ma(n) | 0;
      Pd(f | 0, 0, 52) | 0;
      r = H() | 0 | 134225919;
      g = e;
      b[g >> 2] = -1;
      b[g + 4 >> 2] = r;
      if (!f) {
        f = Pa(d) | 0;
        if ((f | 0) == 7) {
          r = 1;
          T = q;
          return r | 0;
        }
        f = ta(n, f) | 0;
        if ((f | 0) == 127) {
          r = 1;
          T = q;
          return r | 0;
        }
        o = Pd(f | 0, 0, 45) | 0;
        p = H() | 0;
        n = e;
        p = b[n + 4 >> 2] & -1040385 | p;
        r = e;
        b[r >> 2] = b[n >> 2] | o;
        b[r + 4 >> 2] = p;
        r = 0;
        T = q;
        return r | 0;
      }
      b[k >> 2] = b[d >> 2];
      b[k + 4 >> 2] = b[d + 4 >> 2];
      b[k + 8 >> 2] = b[d + 8 >> 2];
      d = f;
      while (1) {
        g = d;
        d = d + -1 | 0;
        b[h >> 2] = b[k >> 2];
        b[h + 4 >> 2] = b[k + 4 >> 2];
        b[h + 8 >> 2] = b[k + 8 >> 2];
        if (!(Tb(g) | 0)) {
          f = Ra(k) | 0;
          if (f | 0) {
            d = 13;
            break;
          }
          b[i >> 2] = b[k >> 2];
          b[i + 4 >> 2] = b[k + 4 >> 2];
          b[i + 8 >> 2] = b[k + 8 >> 2];
          Va(i);
        } else {
          f = Qa(k) | 0;
          if (f | 0) {
            d = 13;
            break;
          }
          b[i >> 2] = b[k >> 2];
          b[i + 4 >> 2] = b[k + 4 >> 2];
          b[i + 8 >> 2] = b[k + 8 >> 2];
          Ua(i);
        }
        Na(h, i, j);
        Ka(j);
        f = e;
        t = b[f >> 2] | 0;
        f = b[f + 4 >> 2] | 0;
        u = (15 - g | 0) * 3 | 0;
        s = Pd(7, 0, u | 0) | 0;
        f = f & ~(H() | 0);
        u = Pd(Pa(j) | 0, 0, u | 0) | 0;
        f = H() | 0 | f;
        r = e;
        b[r >> 2] = u | t & ~s;
        b[r + 4 >> 2] = f;
        if ((g | 0) <= 1) {
          d = 14;
          break;
        }
      }
      a: do { if ((d | 0) != 13) { if ((d | 0) == 14) { if (((b[k >> 2] | 0) <= 1 ? (b[k + 4 >> 2] | 0) <= 1 : 0) ? (b[k + 8 >> 2] | 0) <= 1 : 0) {
        d = Pa(k) | 0;
        f = ta(n, d) | 0;
        if ((f | 0) == 127) { j = 0; }else { j = ma(f) | 0; }
        b: do { if (!d) {
          if ((l | 0) != 0 & (j | 0) != 0) {
            d = Nb(a, c) | 0;
            g = e;
            g = Nb(b[g >> 2] | 0, b[g + 4 >> 2] | 0) | 0;
            if ((d | 0) == 7 | (g | 0) == 7) {
              f = 5;
              break a;
            }
            g = b[21376 + (d * 28 | 0) + (g << 2) >> 2] | 0;
            if ((g | 0) < 0) {
              f = 5;
              break a;
            }
            if (!g) { d = 59; }else {
              i = e;
              d = 0;
              h = b[i >> 2] | 0;
              i = b[i + 4 >> 2] | 0;
              do {
                h = Pb(h, i) | 0;
                i = H() | 0;
                u = e;
                b[u >> 2] = h;
                b[u + 4 >> 2] = i;
                d = d + 1 | 0;
              } while ((d | 0) < (g | 0));
              d = 58;
            }
          } else { d = 58; }
        } else {
          if (l) {
            f = Nb(a, c) | 0;
            if ((f | 0) == 7) {
              f = 5;
              break a;
            }
            g = b[21376 + (f * 28 | 0) + (d << 2) >> 2] | 0;
            if ((g | 0) > 0) {
              f = d;
              d = 0;
              do {
                f = Za(f) | 0;
                d = d + 1 | 0;
              } while ((d | 0) != (g | 0));
            } else { f = d; }
            if ((f | 0) == 1) {
              f = 9;
              break a;
            }
            d = ta(n, f) | 0;
            if ((d | 0) == 127) { I(27648, 27538, 411, 27678); }
            if (!(ma(d) | 0)) {
              p = d;
              o = g;
              m = f;
            } else { I(27693, 27538, 412, 27678); }
          } else {
            p = f;
            o = 0;
            m = d;
          }
          i = b[4272 + (n * 28 | 0) + (m << 2) >> 2] | 0;
          if ((i | 0) <= -1) { I(27724, 27538, 419, 27678); }
          if (!j) {
            if ((o | 0) < 0) {
              f = 5;
              break a;
            }
            if (o | 0) {
              g = e;
              f = 0;
              d = b[g >> 2] | 0;
              g = b[g + 4 >> 2] | 0;
              do {
                d = Pb(d, g) | 0;
                g = H() | 0;
                u = e;
                b[u >> 2] = d;
                b[u + 4 >> 2] = g;
                f = f + 1 | 0;
              } while ((f | 0) < (o | 0));
            }
            if ((i | 0) <= 0) {
              f = p;
              d = 58;
              break;
            }
            g = e;
            f = 0;
            d = b[g >> 2] | 0;
            g = b[g + 4 >> 2] | 0;
            while (1) {
              d = Pb(d, g) | 0;
              g = H() | 0;
              u = e;
              b[u >> 2] = d;
              b[u + 4 >> 2] = g;
              f = f + 1 | 0;
              if ((f | 0) == (i | 0)) {
                f = p;
                d = 58;
                break b;
              }
            }
          }
          h = ua(p, n) | 0;
          if ((h | 0) == 7) { I(27514, 27538, 428, 27678); }
          f = e;
          d = b[f >> 2] | 0;
          f = b[f + 4 >> 2] | 0;
          if ((i | 0) > 0) {
            g = 0;
            do {
              d = Pb(d, f) | 0;
              f = H() | 0;
              u = e;
              b[u >> 2] = d;
              b[u + 4 >> 2] = f;
              g = g + 1 | 0;
            } while ((g | 0) != (i | 0));
          }
          f = Nb(d, f) | 0;
          if ((f | 0) == 7) { I(27795, 27538, 440, 27678); }
          d = na(p) | 0;
          d = b[(d ? 21792 : 21584) + (h * 28 | 0) + (f << 2) >> 2] | 0;
          if ((d | 0) < 0) { I(27795, 27538, 454, 27678); }
          if (!d) {
            f = p;
            d = 58;
          } else {
            h = e;
            f = 0;
            g = b[h >> 2] | 0;
            h = b[h + 4 >> 2] | 0;
            do {
              g = Ob(g, h) | 0;
              h = H() | 0;
              u = e;
              b[u >> 2] = g;
              b[u + 4 >> 2] = h;
              f = f + 1 | 0;
            } while ((f | 0) < (d | 0));
            f = p;
            d = 58;
          }
        } } while (0);
        if ((d | 0) == 58) { if (j) { d = 59; } }
        if ((d | 0) == 59) {
          u = e;
          if ((Nb(b[u >> 2] | 0, b[u + 4 >> 2] | 0) | 0) == 1) {
            f = 9;
            break;
          }
        }
        u = e;
        s = b[u >> 2] | 0;
        u = b[u + 4 >> 2] & -1040385;
        t = Pd(f | 0, 0, 45) | 0;
        u = u | (H() | 0);
        f = e;
        b[f >> 2] = s | t;
        b[f + 4 >> 2] = u;
        f = 0;
      } else { f = 1; } } } } while (0);
      u = f;
      T = q;
      return u | 0;
    }
    function Hc(a, b, c, d, e, f) {
      a = a | 0;
      b = b | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      f = f | 0;
      var g = 0,
        h = 0;
      h = T;
      T = T + 16 | 0;
      g = h;
      if (!e) {
        a = Fc(a, b, c, d, g) | 0;
        if (!a) {
          cb(g, f);
          a = 0;
        }
      } else { a = 15; }
      T = h;
      return a | 0;
    }
    function Ic(a, b, c, d, e) {
      a = a | 0;
      b = b | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      var f = 0,
        g = 0;
      g = T;
      T = T + 16 | 0;
      f = g;
      if (!d) {
        c = db(c, f) | 0;
        if (!c) { c = Gc(a, b, f, e) | 0; }
      } else { c = 15; }
      T = g;
      return c | 0;
    }
    function Jc(a, c, d, e, f) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      f = f | 0;
      var g = 0,
        h = 0,
        i = 0,
        j = 0;
      j = T;
      T = T + 32 | 0;
      h = j + 12 | 0;
      i = j;
      g = Fc(a, c, a, c, h) | 0;
      if (g | 0) {
        i = g;
        T = j;
        return i | 0;
      }
      a = Fc(a, c, d, e, i) | 0;
      if (a | 0) {
        i = a;
        T = j;
        return i | 0;
      }
      h = bb(h, i) | 0;
      i = f;
      b[i >> 2] = h;
      b[i + 4 >> 2] = ((h | 0) < 0) << 31 >> 31;
      i = 0;
      T = j;
      return i | 0;
    }
    function Kc(a, c, d, e, f) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      f = f | 0;
      var g = 0,
        h = 0,
        i = 0,
        j = 0;
      j = T;
      T = T + 32 | 0;
      h = j + 12 | 0;
      i = j;
      g = Fc(a, c, a, c, h) | 0;
      if (!g) {
        g = Fc(a, c, d, e, i) | 0;
        if (!g) {
          e = bb(h, i) | 0;
          e = Ed(e | 0, ((e | 0) < 0) << 31 >> 31 | 0, 1, 0) | 0;
          h = H() | 0;
          i = f;
          b[i >> 2] = e;
          b[i + 4 >> 2] = h;
          i = 0;
          T = j;
          return i | 0;
        }
      }
      i = g;
      T = j;
      return i | 0;
    }
    function Lc(a, c, d, e, f) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      f = f | 0;
      var g = 0,
        h = 0,
        i = 0.0,
        j = 0.0,
        k = 0,
        l = 0.0,
        m = 0,
        n = 0,
        o = 0,
        p = 0,
        r = 0.0,
        s = 0,
        t = 0.0,
        u = 0,
        v = 0,
        w = 0,
        x = 0,
        y = 0,
        z = 0,
        A = 0.0;
      z = T;
      T = T + 48 | 0;
      x = z + 24 | 0;
      h = z + 12 | 0;
      y = z;
      g = Fc(a, c, a, c, x) | 0;
      if (!g) {
        g = Fc(a, c, d, e, h) | 0;
        if (!g) {
          v = bb(x, h) | 0;
          w = ((v | 0) < 0) << 31 >> 31;
          b[x >> 2] = 0;
          b[x + 4 >> 2] = 0;
          b[x + 8 >> 2] = 0;
          b[h >> 2] = 0;
          b[h + 4 >> 2] = 0;
          b[h + 8 >> 2] = 0;
          if (Fc(a, c, a, c, x) | 0) { I(27795, 27538, 692, 27747); }
          if (Fc(a, c, d, e, h) | 0) { I(27795, 27538, 697, 27747); }
          eb(x);
          eb(h);
          l = (v | 0) == 0 ? 0.0 : 1.0 / +(v | 0);
          d = b[x >> 2] | 0;
          r = l * +((b[h >> 2] | 0) - d | 0);
          s = x + 4 | 0;
          e = b[s >> 2] | 0;
          t = l * +((b[h + 4 >> 2] | 0) - e | 0);
          u = x + 8 | 0;
          g = b[u >> 2] | 0;
          l = l * +((b[h + 8 >> 2] | 0) - g | 0);
          b[y >> 2] = d;
          m = y + 4 | 0;
          b[m >> 2] = e;
          n = y + 8 | 0;
          b[n >> 2] = g;
          a: do { if ((v | 0) < 0) { g = 0; }else {
            o = 0;
            p = 0;
            while (1) {
              j = +(p >>> 0) + 4294967296.0 * +(o | 0);
              A = r * j + +(d | 0);
              i = t * j + +(e | 0);
              j = l * j + +(g | 0);
              d = ~~+Td(+A);
              h = ~~+Td(+i);
              g = ~~+Td(+j);
              A = +q(+(+(d | 0) - A));
              i = +q(+(+(h | 0) - i));
              j = +q(+(+(g | 0) - j));
              do { if (!(A > i & A > j)) {
                k = 0 - d | 0;
                if (i > j) {
                  e = k - g | 0;
                  break;
                } else {
                  e = h;
                  g = k - h | 0;
                  break;
                }
              } else {
                d = 0 - (h + g) | 0;
                e = h;
              } } while (0);
              b[y >> 2] = d;
              b[m >> 2] = e;
              b[n >> 2] = g;
              fb(y);
              g = Gc(a, c, y, f + (p << 3) | 0) | 0;
              if (g | 0) { break a; }
              if (!((o | 0) < (w | 0) | (o | 0) == (w | 0) & p >>> 0 < v >>> 0)) {
                g = 0;
                break a;
              }
              d = Ed(p | 0, o | 0, 1, 0) | 0;
              e = H() | 0;
              o = e;
              p = d;
              d = b[x >> 2] | 0;
              e = b[s >> 2] | 0;
              g = b[u >> 2] | 0;
            }
          } } while (0);
          y = g;
          T = z;
          return y | 0;
        }
      }
      y = g;
      T = z;
      return y | 0;
    }
    function Mc(a, b, c, d) {
      a = a | 0;
      b = b | 0;
      c = c | 0;
      d = d | 0;
      var e = 0,
        f = 0,
        g = 0;
      if ((c | 0) == 0 & (d | 0) == 0) {
        e = 0;
        f = 1;
        G(e | 0);
        return f | 0;
      }
      f = a;
      e = b;
      a = 1;
      b = 0;
      do {
        g = (c & 1 | 0) == 0 & 0 == 0;
        a = Kd((g ? 1 : f) | 0, (g ? 0 : e) | 0, a | 0, b | 0) | 0;
        b = H() | 0;
        c = Nd(c | 0, d | 0, 1) | 0;
        d = H() | 0;
        f = Kd(f | 0, e | 0, f | 0, e | 0) | 0;
        e = H() | 0;
      } while (!((c | 0) == 0 & (d | 0) == 0));
      G(b | 0);
      return a | 0;
    }
    function Nc(a, c, d, f) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      f = f | 0;
      var g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0.0,
        l = 0.0,
        m = 0.0;
      j = T;
      T = T + 16 | 0;
      h = j;
      i = Od(a | 0, c | 0, 52) | 0;
      H() | 0;
      i = i & 15;
      do { if (!i) {
        g = Od(a | 0, c | 0, 45) | 0;
        H() | 0;
        g = g & 127;
        if (g >>> 0 > 121) {
          i = 5;
          T = j;
          return i | 0;
        } else {
          h = 22064 + (g << 5) | 0;
          b[d >> 2] = b[h >> 2];
          b[d + 4 >> 2] = b[h + 4 >> 2];
          b[d + 8 >> 2] = b[h + 8 >> 2];
          b[d + 12 >> 2] = b[h + 12 >> 2];
          b[d + 16 >> 2] = b[h + 16 >> 2];
          b[d + 20 >> 2] = b[h + 20 >> 2];
          b[d + 24 >> 2] = b[h + 24 >> 2];
          b[d + 28 >> 2] = b[h + 28 >> 2];
          break;
        }
      } else {
        g = Xb(a, c, h) | 0;
        if (!g) {
          l = +e[h >> 3];
          k = 1.0 / +t(+l);
          m = +e[25968 + (i << 3) >> 3];
          e[d >> 3] = l + m;
          e[d + 8 >> 3] = l - m;
          l = +e[h + 8 >> 3];
          k = m * k;
          e[d + 16 >> 3] = k + l;
          e[d + 24 >> 3] = l - k;
          break;
        }
        i = g;
        T = j;
        return i | 0;
      } } while (0);
      Ha(d, f ? 1.4 : 1.1);
      f = 26096 + (i << 3) | 0;
      if ((b[f >> 2] | 0) == (a | 0) ? (b[f + 4 >> 2] | 0) == (c | 0) : 0) { e[d >> 3] = 1.5707963267948966; }
      i = 26224 + (i << 3) | 0;
      if ((b[i >> 2] | 0) == (a | 0) ? (b[i + 4 >> 2] | 0) == (c | 0) : 0) { e[d + 8 >> 3] = -1.5707963267948966; }
      if (!(+e[d >> 3] == 1.5707963267948966) ? !(+e[d + 8 >> 3] == -1.5707963267948966) : 0) {
        i = 0;
        T = j;
        return i | 0;
      }
      e[d + 16 >> 3] = 3.141592653589793;
      e[d + 24 >> 3] = -3.141592653589793;
      i = 0;
      T = j;
      return i | 0;
    }
    function Oc(c, d, e, f) {
      c = c | 0;
      d = d | 0;
      e = e | 0;
      f = f | 0;
      var g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0,
        m = 0;
      l = T;
      T = T + 48 | 0;
      i = l + 32 | 0;
      h = l + 40 | 0;
      j = l;
      Cb(i, 0, 0, 0);
      k = b[i >> 2] | 0;
      i = b[i + 4 >> 2] | 0;
      do { if (e >>> 0 <= 15) {
        g = Vc(f) | 0;
        if (g | 0) {
          f = j;
          b[f >> 2] = 0;
          b[f + 4 >> 2] = 0;
          b[j + 8 >> 2] = g;
          b[j + 12 >> 2] = -1;
          f = j + 16 | 0;
          k = j + 29 | 0;
          b[f >> 2] = 0;
          b[f + 4 >> 2] = 0;
          b[f + 8 >> 2] = 0;
          a[f + 12 >> 0] = 0;
          a[k >> 0] = a[h >> 0] | 0;
          a[k + 1 >> 0] = a[h + 1 >> 0] | 0;
          a[k + 2 >> 0] = a[h + 2 >> 0] | 0;
          break;
        }
        g = Dd((b[d + 8 >> 2] | 0) + 1 | 0, 32) | 0;
        if (!g) {
          f = j;
          b[f >> 2] = 0;
          b[f + 4 >> 2] = 0;
          b[j + 8 >> 2] = 13;
          b[j + 12 >> 2] = -1;
          f = j + 16 | 0;
          k = j + 29 | 0;
          b[f >> 2] = 0;
          b[f + 4 >> 2] = 0;
          b[f + 8 >> 2] = 0;
          a[f + 12 >> 0] = 0;
          a[k >> 0] = a[h >> 0] | 0;
          a[k + 1 >> 0] = a[h + 1 >> 0] | 0;
          a[k + 2 >> 0] = a[h + 2 >> 0] | 0;
          break;
        } else {
          Wc(d, g);
          m = j;
          b[m >> 2] = k;
          b[m + 4 >> 2] = i;
          b[j + 8 >> 2] = 0;
          b[j + 12 >> 2] = e;
          b[j + 16 >> 2] = f;
          b[j + 20 >> 2] = d;
          b[j + 24 >> 2] = g;
          a[j + 28 >> 0] = 0;
          k = j + 29 | 0;
          a[k >> 0] = a[h >> 0] | 0;
          a[k + 1 >> 0] = a[h + 1 >> 0] | 0;
          a[k + 2 >> 0] = a[h + 2 >> 0] | 0;
          break;
        }
      } else {
        k = j;
        b[k >> 2] = 0;
        b[k + 4 >> 2] = 0;
        b[j + 8 >> 2] = 4;
        b[j + 12 >> 2] = -1;
        k = j + 16 | 0;
        m = j + 29 | 0;
        b[k >> 2] = 0;
        b[k + 4 >> 2] = 0;
        b[k + 8 >> 2] = 0;
        a[k + 12 >> 0] = 0;
        a[m >> 0] = a[h >> 0] | 0;
        a[m + 1 >> 0] = a[h + 1 >> 0] | 0;
        a[m + 2 >> 0] = a[h + 2 >> 0] | 0;
      } } while (0);
      Pc(j);
      b[c >> 2] = b[j >> 2];
      b[c + 4 >> 2] = b[j + 4 >> 2];
      b[c + 8 >> 2] = b[j + 8 >> 2];
      b[c + 12 >> 2] = b[j + 12 >> 2];
      b[c + 16 >> 2] = b[j + 16 >> 2];
      b[c + 20 >> 2] = b[j + 20 >> 2];
      b[c + 24 >> 2] = b[j + 24 >> 2];
      b[c + 28 >> 2] = b[j + 28 >> 2];
      T = l;
      return;
    }
    function Pc(c) {
      c = c | 0;
      var d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0,
        m = 0,
        n = 0,
        o = 0,
        p = 0,
        q = 0,
        r = 0,
        s = 0,
        t = 0,
        u = 0,
        v = 0,
        w = 0;
      w = T;
      T = T + 336 | 0;
      p = w + 168 | 0;
      q = w;
      f = c;
      e = b[f >> 2] | 0;
      f = b[f + 4 >> 2] | 0;
      if ((e | 0) == 0 & (f | 0) == 0) {
        T = w;
        return;
      }
      d = c + 28 | 0;
      if (!(a[d >> 0] | 0)) { a[d >> 0] = 1; }else {
        e = Qc(e, f) | 0;
        f = H() | 0;
      }
      v = c + 20 | 0;
      if (!(b[b[v >> 2] >> 2] | 0)) {
        d = c + 24 | 0;
        e = b[d >> 2] | 0;
        if (e | 0) { Cd(e); }
        u = c;
        b[u >> 2] = 0;
        b[u + 4 >> 2] = 0;
        b[c + 8 >> 2] = 0;
        b[v >> 2] = 0;
        b[c + 12 >> 2] = -1;
        b[c + 16 >> 2] = 0;
        b[d >> 2] = 0;
        T = w;
        return;
      }
      u = c + 16 | 0;
      d = b[u >> 2] | 0;
      g = d & 15;
      a: do { if (!((e | 0) == 0 & (f | 0) == 0)) {
        r = c + 12 | 0;
        n = (g | 0) == 3;
        m = d & 255;
        k = (g | 1 | 0) == 3;
        o = c + 24 | 0;
        l = (g + -1 | 0) >>> 0 < 3;
        i = (g | 2 | 0) == 3;
        j = q + 8 | 0;
        b: while (1) {
          h = Od(e | 0, f | 0, 52) | 0;
          H() | 0;
          h = h & 15;
          if ((h | 0) == (b[r >> 2] | 0)) {
            switch (m & 15) {
              case 0:
              case 2:
              case 3:
                {
                  g = Xb(e, f, p) | 0;
                  if (g | 0) {
                    s = 15;
                    break b;
                  }
                  if (Xc(b[v >> 2] | 0, b[o >> 2] | 0, p) | 0) {
                    s = 19;
                    break b;
                  }
                  break;
                }
              default:

            }
            if (k ? (g = b[(b[v >> 2] | 0) + 4 >> 2] | 0, b[p >> 2] = b[g >> 2], b[p + 4 >> 2] = b[g + 4 >> 2], b[p + 8 >> 2] = b[g + 8 >> 2], b[p + 12 >> 2] = b[g + 12 >> 2], Aa(26832, p) | 0) : 0) {
              if (Ub(b[(b[v >> 2] | 0) + 4 >> 2] | 0, h, q) | 0) {
                s = 25;
                break;
              }
              g = q;
              if ((b[g >> 2] | 0) == (e | 0) ? (b[g + 4 >> 2] | 0) == (f | 0) : 0) {
                s = 29;
                break;
              }
            }
            if (l) {
              g = Yb(e, f, p) | 0;
              if (g | 0) {
                s = 32;
                break;
              }
              if (Nc(e, f, q, 0) | 0) {
                s = 36;
                break;
              }
              if (i ? Yc(b[v >> 2] | 0, b[o >> 2] | 0, p, q) | 0 : 0) {
                s = 42;
                break;
              }
              if (k ? _c(b[v >> 2] | 0, b[o >> 2] | 0, p, q) | 0 : 0) {
                s = 42;
                break;
              }
            }
            if (n) {
              d = Nc(e, f, p, 1) | 0;
              g = b[o >> 2] | 0;
              if (d | 0) {
                s = 45;
                break;
              }
              if (Ba(g, p) | 0) {
                Ea(q, p);
                if (Da(p, b[o >> 2] | 0) | 0) {
                  s = 53;
                  break;
                }
                if (Xc(b[v >> 2] | 0, b[o >> 2] | 0, j) | 0) {
                  s = 53;
                  break;
                }
                if (_c(b[v >> 2] | 0, b[o >> 2] | 0, q, p) | 0) {
                  s = 53;
                  break;
                }
              }
            }
          }
          do { if ((h | 0) < (b[r >> 2] | 0)) {
            d = Nc(e, f, p, 1) | 0;
            g = b[o >> 2] | 0;
            if (d | 0) {
              s = 58;
              break b;
            }
            if (!(Ba(g, p) | 0)) {
              s = 73;
              break;
            }
            if (Da(b[o >> 2] | 0, p) | 0 ? (Ea(q, p), Yc(b[v >> 2] | 0, b[o >> 2] | 0, q, p) | 0) : 0) {
              s = 65;
              break b;
            }
            e = Ib(e, f, h + 1 | 0, q) | 0;
            if (e | 0) {
              s = 67;
              break b;
            }
            f = q;
            e = b[f >> 2] | 0;
            f = b[f + 4 >> 2] | 0;
          } else { s = 73; } } while (0);
          if ((s | 0) == 73) {
            s = 0;
            e = Qc(e, f) | 0;
            f = H() | 0;
          }
          if ((e | 0) == 0 & (f | 0) == 0) {
            t = o;
            break a;
          }
        }
        switch (s | 0) {
          case 15:
            {
              d = b[o >> 2] | 0;
              if (d | 0) { Cd(d); }
              s = c;
              b[s >> 2] = 0;
              b[s + 4 >> 2] = 0;
              b[v >> 2] = 0;
              b[r >> 2] = -1;
              b[u >> 2] = 0;
              b[o >> 2] = 0;
              b[c + 8 >> 2] = g;
              s = 20;
              break;
            }
          case 19:
            {
              b[c >> 2] = e;
              b[c + 4 >> 2] = f;
              s = 20;
              break;
            }
          case 25:
            {
              I(27795, 27761, 470, 27772);
              break;
            }
          case 29:
            {
              b[c >> 2] = e;
              b[c + 4 >> 2] = f;
              T = w;
              return;
            }
          case 32:
            {
              d = b[o >> 2] | 0;
              if (d | 0) { Cd(d); }
              t = c;
              b[t >> 2] = 0;
              b[t + 4 >> 2] = 0;
              b[v >> 2] = 0;
              b[r >> 2] = -1;
              b[u >> 2] = 0;
              b[o >> 2] = 0;
              b[c + 8 >> 2] = g;
              T = w;
              return;
            }
          case 36:
            {
              I(27795, 27761, 493, 27772);
              break;
            }
          case 42:
            {
              b[c >> 2] = e;
              b[c + 4 >> 2] = f;
              T = w;
              return;
            }
          case 45:
            {
              if (g | 0) { Cd(g); }
              s = c;
              b[s >> 2] = 0;
              b[s + 4 >> 2] = 0;
              b[v >> 2] = 0;
              b[r >> 2] = -1;
              b[u >> 2] = 0;
              b[o >> 2] = 0;
              b[c + 8 >> 2] = d;
              s = 55;
              break;
            }
          case 53:
            {
              b[c >> 2] = e;
              b[c + 4 >> 2] = f;
              s = 55;
              break;
            }
          case 58:
            {
              if (g | 0) { Cd(g); }
              s = c;
              b[s >> 2] = 0;
              b[s + 4 >> 2] = 0;
              b[v >> 2] = 0;
              b[r >> 2] = -1;
              b[u >> 2] = 0;
              b[o >> 2] = 0;
              b[c + 8 >> 2] = d;
              s = 71;
              break;
            }
          case 65:
            {
              b[c >> 2] = e;
              b[c + 4 >> 2] = f;
              s = 71;
              break;
            }
          case 67:
            {
              d = b[o >> 2] | 0;
              if (d | 0) { Cd(d); }
              t = c;
              b[t >> 2] = 0;
              b[t + 4 >> 2] = 0;
              b[v >> 2] = 0;
              b[r >> 2] = -1;
              b[u >> 2] = 0;
              b[o >> 2] = 0;
              b[c + 8 >> 2] = e;
              T = w;
              return;
            }
        }
        if ((s | 0) == 20) {
          T = w;
          return;
        } else if ((s | 0) == 55) {
          T = w;
          return;
        } else if ((s | 0) == 71) {
          T = w;
          return;
        }
      } else { t = c + 24 | 0; } } while (0);
      d = b[t >> 2] | 0;
      if (d | 0) { Cd(d); }
      s = c;
      b[s >> 2] = 0;
      b[s + 4 >> 2] = 0;
      b[c + 8 >> 2] = 0;
      b[v >> 2] = 0;
      b[c + 12 >> 2] = -1;
      b[u >> 2] = 0;
      b[t >> 2] = 0;
      T = w;
      return;
    }
    function Qc(a, c) {
      a = a | 0;
      c = c | 0;
      var d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0,
        m = 0;
      m = T;
      T = T + 16 | 0;
      l = m;
      e = Od(a | 0, c | 0, 52) | 0;
      H() | 0;
      e = e & 15;
      d = Od(a | 0, c | 0, 45) | 0;
      H() | 0;
      do { if (e) {
        while (1) {
          d = Pd(e + 4095 | 0, 0, 52) | 0;
          f = H() | 0 | c & -15728641;
          g = (15 - e | 0) * 3 | 0;
          h = Pd(7, 0, g | 0) | 0;
          i = H() | 0;
          d = d | a | h;
          f = f | i;
          j = Od(a | 0, c | 0, g | 0) | 0;
          H() | 0;
          j = j & 7;
          e = e + -1 | 0;
          if (j >>> 0 < 6) { break; }
          if (!e) {
            k = 4;
            break;
          } else {
            c = f;
            a = d;
          }
        }
        if ((k | 0) == 4) {
          d = Od(d | 0, f | 0, 45) | 0;
          H() | 0;
          break;
        }
        l = (j | 0) == 0 & (Fb(d, f) | 0) != 0;
        l = Pd((l ? 2 : 1) + j | 0, 0, g | 0) | 0;
        k = H() | 0 | c & ~i;
        l = l | a & ~h;
        G(k | 0);
        T = m;
        return l | 0;
      } } while (0);
      d = d & 127;
      if (d >>> 0 > 120) {
        k = 0;
        l = 0;
        G(k | 0);
        T = m;
        return l | 0;
      }
      Cb(l, 0, d + 1 | 0, 0);
      k = b[l + 4 >> 2] | 0;
      l = b[l >> 2] | 0;
      G(k | 0);
      T = m;
      return l | 0;
    }
    function Rc(a, c, d, e, f, g) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      f = f | 0;
      g = g | 0;
      var h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0,
        m = 0,
        n = 0,
        o = 0,
        p = 0,
        q = 0,
        r = 0;
      r = T;
      T = T + 160 | 0;
      m = r + 80 | 0;
      i = r + 64 | 0;
      n = r + 112 | 0;
      q = r;
      Oc(m, a, c, d);
      k = m;
      dc(i, b[k >> 2] | 0, b[k + 4 >> 2] | 0, c);
      k = i;
      j = b[k >> 2] | 0;
      k = b[k + 4 >> 2] | 0;
      h = b[m + 8 >> 2] | 0;
      o = n + 4 | 0;
      b[o >> 2] = b[m >> 2];
      b[o + 4 >> 2] = b[m + 4 >> 2];
      b[o + 8 >> 2] = b[m + 8 >> 2];
      b[o + 12 >> 2] = b[m + 12 >> 2];
      b[o + 16 >> 2] = b[m + 16 >> 2];
      b[o + 20 >> 2] = b[m + 20 >> 2];
      b[o + 24 >> 2] = b[m + 24 >> 2];
      b[o + 28 >> 2] = b[m + 28 >> 2];
      o = q;
      b[o >> 2] = j;
      b[o + 4 >> 2] = k;
      o = q + 8 | 0;
      b[o >> 2] = h;
      a = q + 12 | 0;
      c = n;
      d = a + 36 | 0;
      do {
        b[a >> 2] = b[c >> 2];
        a = a + 4 | 0;
        c = c + 4 | 0;
      } while ((a | 0) < (d | 0));
      n = q + 48 | 0;
      b[n >> 2] = b[i >> 2];
      b[n + 4 >> 2] = b[i + 4 >> 2];
      b[n + 8 >> 2] = b[i + 8 >> 2];
      b[n + 12 >> 2] = b[i + 12 >> 2];
      if ((j | 0) == 0 & (k | 0) == 0) {
        q = h;
        T = r;
        return q | 0;
      }
      d = q + 16 | 0;
      l = q + 24 | 0;
      m = q + 28 | 0;
      h = 0;
      i = 0;
      c = j;
      a = k;
      do {
        if (!((h | 0) < (f | 0) | (h | 0) == (f | 0) & i >>> 0 < e >>> 0)) {
          p = 4;
          break;
        }
        k = i;
        i = Ed(i | 0, h | 0, 1, 0) | 0;
        h = H() | 0;
        k = g + (k << 3) | 0;
        b[k >> 2] = c;
        b[k + 4 >> 2] = a;
        fc(n);
        a = n;
        c = b[a >> 2] | 0;
        a = b[a + 4 >> 2] | 0;
        if ((c | 0) == 0 & (a | 0) == 0) {
          Pc(d);
          c = d;
          a = b[c >> 2] | 0;
          c = b[c + 4 >> 2] | 0;
          if ((a | 0) == 0 & (c | 0) == 0) {
            p = 10;
            break;
          }
          ec(a, c, b[m >> 2] | 0, n);
          a = n;
          c = b[a >> 2] | 0;
          a = b[a + 4 >> 2] | 0;
        }
        k = q;
        b[k >> 2] = c;
        b[k + 4 >> 2] = a;
      } while (!((c | 0) == 0 & (a | 0) == 0));
      if ((p | 0) == 4) {
        a = q + 40 | 0;
        c = b[a >> 2] | 0;
        if (c | 0) { Cd(c); }
        p = q + 16 | 0;
        b[p >> 2] = 0;
        b[p + 4 >> 2] = 0;
        b[l >> 2] = 0;
        b[q + 36 >> 2] = 0;
        b[m >> 2] = -1;
        b[q + 32 >> 2] = 0;
        b[a >> 2] = 0;
        ec(0, 0, 0, n);
        b[q >> 2] = 0;
        b[q + 4 >> 2] = 0;
        b[o >> 2] = 0;
        q = 14;
        T = r;
        return q | 0;
      } else if ((p | 0) == 10) {
        b[q >> 2] = 0;
        b[q + 4 >> 2] = 0;
        b[o >> 2] = b[l >> 2];
      }
      q = b[o >> 2] | 0;
      T = r;
      return q | 0;
    }
    function Sc(c, d, f, g) {
      c = c | 0;
      d = d | 0;
      f = f | 0;
      g = g | 0;
      var h = 0,
        i = 0.0,
        j = 0,
        k = 0,
        l = 0,
        m = 0,
        n = 0,
        o = 0,
        p = 0.0,
        r = 0;
      o = T;
      T = T + 48 | 0;
      l = o + 32 | 0;
      k = o + 40 | 0;
      m = o;
      if (!(b[c >> 2] | 0)) {
        n = g;
        b[n >> 2] = 0;
        b[n + 4 >> 2] = 0;
        n = 0;
        T = o;
        return n | 0;
      }
      Cb(l, 0, 0, 0);
      j = l;
      h = b[j >> 2] | 0;
      j = b[j + 4 >> 2] | 0;
      do { if (d >>> 0 > 15) {
        n = m;
        b[n >> 2] = 0;
        b[n + 4 >> 2] = 0;
        b[m + 8 >> 2] = 4;
        b[m + 12 >> 2] = -1;
        n = m + 16 | 0;
        f = m + 29 | 0;
        b[n >> 2] = 0;
        b[n + 4 >> 2] = 0;
        b[n + 8 >> 2] = 0;
        a[n + 12 >> 0] = 0;
        a[f >> 0] = a[k >> 0] | 0;
        a[f + 1 >> 0] = a[k + 1 >> 0] | 0;
        a[f + 2 >> 0] = a[k + 2 >> 0] | 0;
        f = 4;
        n = 9;
      } else {
        f = Vc(f) | 0;
        if (f | 0) {
          l = m;
          b[l >> 2] = 0;
          b[l + 4 >> 2] = 0;
          b[m + 8 >> 2] = f;
          b[m + 12 >> 2] = -1;
          l = m + 16 | 0;
          n = m + 29 | 0;
          b[l >> 2] = 0;
          b[l + 4 >> 2] = 0;
          b[l + 8 >> 2] = 0;
          a[l + 12 >> 0] = 0;
          a[n >> 0] = a[k >> 0] | 0;
          a[n + 1 >> 0] = a[k + 1 >> 0] | 0;
          a[n + 2 >> 0] = a[k + 2 >> 0] | 0;
          n = 9;
          break;
        }
        f = Dd((b[c + 8 >> 2] | 0) + 1 | 0, 32) | 0;
        if (!f) {
          n = m;
          b[n >> 2] = 0;
          b[n + 4 >> 2] = 0;
          b[m + 8 >> 2] = 13;
          b[m + 12 >> 2] = -1;
          n = m + 16 | 0;
          f = m + 29 | 0;
          b[n >> 2] = 0;
          b[n + 4 >> 2] = 0;
          b[n + 8 >> 2] = 0;
          a[n + 12 >> 0] = 0;
          a[f >> 0] = a[k >> 0] | 0;
          a[f + 1 >> 0] = a[k + 1 >> 0] | 0;
          a[f + 2 >> 0] = a[k + 2 >> 0] | 0;
          f = 13;
          n = 9;
          break;
        }
        Wc(c, f);
        r = m;
        b[r >> 2] = h;
        b[r + 4 >> 2] = j;
        j = m + 8 | 0;
        b[j >> 2] = 0;
        b[m + 12 >> 2] = d;
        b[m + 20 >> 2] = c;
        b[m + 24 >> 2] = f;
        a[m + 28 >> 0] = 0;
        h = m + 29 | 0;
        a[h >> 0] = a[k >> 0] | 0;
        a[h + 1 >> 0] = a[k + 1 >> 0] | 0;
        a[h + 2 >> 0] = a[k + 2 >> 0] | 0;
        b[m + 16 >> 2] = 3;
        p = +za(f);
        p = p * +xa(f);
        i = +q(+ +e[f >> 3]);
        i = p / +t(+ +Sd(+i, + +q(+ +e[f + 8 >> 3]))) * 6371.007180918475 * 6371.007180918475;
        h = m + 12 | 0;
        f = b[h >> 2] | 0;
        a: do { if ((f | 0) > 0) { do {
          oc(f + -1 | 0, l) | 0;
          if (!(i / +e[l >> 3] > 10.0)) { break a; }
          r = b[h >> 2] | 0;
          f = r + -1 | 0;
          b[h >> 2] = f;
        } while ((r | 0) > 1); } } while (0);
        Pc(m);
        h = g;
        b[h >> 2] = 0;
        b[h + 4 >> 2] = 0;
        h = m;
        f = b[h >> 2] | 0;
        h = b[h + 4 >> 2] | 0;
        if (!((f | 0) == 0 & (h | 0) == 0)) { do {
          Eb(f, h, d, l) | 0;
          k = l;
          c = g;
          k = Ed(b[c >> 2] | 0, b[c + 4 >> 2] | 0, b[k >> 2] | 0, b[k + 4 >> 2] | 0) | 0;
          c = H() | 0;
          r = g;
          b[r >> 2] = k;
          b[r + 4 >> 2] = c;
          Pc(m);
          r = m;
          f = b[r >> 2] | 0;
          h = b[r + 4 >> 2] | 0;
        } while (!((f | 0) == 0 & (h | 0) == 0)); }
        f = b[j >> 2] | 0;
      } } while (0);
      r = f;
      T = o;
      return r | 0;
    }
    function Tc(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var f = 0.0,
        g = 0.0,
        h = 0.0,
        i = 0.0,
        j = 0.0,
        k = 0.0,
        l = 0,
        m = 0.0,
        n = 0,
        o = 0;
      if (!(Aa(c, d) | 0)) {
        o = 0;
        return o | 0;
      }
      c = ya(c) | 0;
      f = +e[d >> 3];
      g = +e[d + 8 >> 3];
      g = c & g < 0.0 ? g + 6.283185307179586 : g;
      o = b[a >> 2] | 0;
      if ((o | 0) <= 0) {
        o = 0;
        return o | 0;
      }
      n = b[a + 4 >> 2] | 0;
      if (c) {
        c = 0;
        m = g;
        d = -1;
        a = 0;
        a: while (1) {
          l = a;
          while (1) {
            i = +e[n + (l << 4) >> 3];
            g = +e[n + (l << 4) + 8 >> 3];
            a = (d + 2 | 0) % (o | 0) | 0;
            h = +e[n + (a << 4) >> 3];
            j = +e[n + (a << 4) + 8 >> 3];
            if (i > h) {
              k = i;
              i = j;
            } else {
              k = h;
              h = i;
              i = g;
              g = j;
            }
            f = f == h | f == k ? f + 2.220446049250313e-16 : f;
            if (!(f < h | f > k)) { break; }
            d = l + 1 | 0;
            if ((d | 0) >= (o | 0)) {
              d = 22;
              break a;
            } else {
              a = l;
              l = d;
              d = a;
            }
          }
          j = i < 0.0 ? i + 6.283185307179586 : i;
          i = g < 0.0 ? g + 6.283185307179586 : g;
          m = j == m | i == m ? m + -2.220446049250313e-16 : m;
          k = j + (i - j) * ((f - h) / (k - h));
          if ((k < 0.0 ? k + 6.283185307179586 : k) > m) { c = c ^ 1; }
          a = l + 1 | 0;
          if ((a | 0) >= (o | 0)) {
            d = 22;
            break;
          } else { d = l; }
        }
        if ((d | 0) == 22) { return c | 0; }
      } else {
        c = 0;
        m = g;
        d = -1;
        a = 0;
        b: while (1) {
          l = a;
          while (1) {
            i = +e[n + (l << 4) >> 3];
            g = +e[n + (l << 4) + 8 >> 3];
            a = (d + 2 | 0) % (o | 0) | 0;
            h = +e[n + (a << 4) >> 3];
            j = +e[n + (a << 4) + 8 >> 3];
            if (i > h) {
              k = i;
              i = j;
            } else {
              k = h;
              h = i;
              i = g;
              g = j;
            }
            f = f == h | f == k ? f + 2.220446049250313e-16 : f;
            if (!(f < h | f > k)) { break; }
            d = l + 1 | 0;
            if ((d | 0) >= (o | 0)) {
              d = 22;
              break b;
            } else {
              a = l;
              l = d;
              d = a;
            }
          }
          m = i == m | g == m ? m + -2.220446049250313e-16 : m;
          if (i + (g - i) * ((f - h) / (k - h)) > m) { c = c ^ 1; }
          a = l + 1 | 0;
          if ((a | 0) >= (o | 0)) {
            d = 22;
            break;
          } else { d = l; }
        }
        if ((d | 0) == 22) { return c | 0; }
      }
      return 0;
    }
    function Uc(a, c) {
      a = a | 0;
      c = c | 0;
      var d = 0.0,
        f = 0.0,
        g = 0.0,
        h = 0.0,
        i = 0.0,
        j = 0.0,
        k = 0.0,
        l = 0.0,
        m = 0.0,
        n = 0,
        o = 0,
        p = 0,
        r = 0,
        s = 0,
        t = 0,
        u = 0,
        v = 0;
      r = b[a >> 2] | 0;
      if (!r) {
        b[c >> 2] = 0;
        b[c + 4 >> 2] = 0;
        b[c + 8 >> 2] = 0;
        b[c + 12 >> 2] = 0;
        b[c + 16 >> 2] = 0;
        b[c + 20 >> 2] = 0;
        b[c + 24 >> 2] = 0;
        b[c + 28 >> 2] = 0;
        return;
      }
      s = c + 8 | 0;
      e[s >> 3] = 1797693134862315708145274.0e284;
      t = c + 24 | 0;
      e[t >> 3] = 1797693134862315708145274.0e284;
      e[c >> 3] = -1797693134862315708145274.0e284;
      u = c + 16 | 0;
      e[u >> 3] = -1797693134862315708145274.0e284;
      if ((r | 0) <= 0) { return; }
      o = b[a + 4 >> 2] | 0;
      l = 1797693134862315708145274.0e284;
      m = -1797693134862315708145274.0e284;
      n = 0;
      a = -1;
      h = 1797693134862315708145274.0e284;
      i = 1797693134862315708145274.0e284;
      k = -1797693134862315708145274.0e284;
      f = -1797693134862315708145274.0e284;
      p = 0;
      while (1) {
        d = +e[o + (p << 4) >> 3];
        j = +e[o + (p << 4) + 8 >> 3];
        a = a + 2 | 0;
        g = +e[o + (((a | 0) == (r | 0) ? 0 : a) << 4) + 8 >> 3];
        if (d < h) {
          e[s >> 3] = d;
          h = d;
        }
        if (j < i) {
          e[t >> 3] = j;
          i = j;
        }
        if (d > k) { e[c >> 3] = d; }else { d = k; }
        if (j > f) {
          e[u >> 3] = j;
          f = j;
        }
        l = j > 0.0 & j < l ? j : l;
        m = j < 0.0 & j > m ? j : m;
        n = n | +q(+(j - g)) > 3.141592653589793;
        a = p + 1 | 0;
        if ((a | 0) == (r | 0)) { break; }else {
          v = p;
          k = d;
          p = a;
          a = v;
        }
      }
      if (!n) { return; }
      e[u >> 3] = m;
      e[t >> 3] = l;
      return;
    }
    function Vc(a) {
      a = a | 0;
      return (a >>> 0 < 4 ? 0 : 15) | 0;
    }
    function Wc(a, c) {
      a = a | 0;
      c = c | 0;
      var d = 0,
        f = 0,
        g = 0,
        h = 0.0,
        i = 0.0,
        j = 0.0,
        k = 0.0,
        l = 0.0,
        m = 0.0,
        n = 0.0,
        o = 0.0,
        p = 0.0,
        r = 0,
        s = 0,
        t = 0,
        u = 0,
        v = 0,
        w = 0,
        x = 0,
        y = 0,
        z = 0,
        A = 0;
      r = b[a >> 2] | 0;
      if (r) {
        s = c + 8 | 0;
        e[s >> 3] = 1797693134862315708145274.0e284;
        t = c + 24 | 0;
        e[t >> 3] = 1797693134862315708145274.0e284;
        e[c >> 3] = -1797693134862315708145274.0e284;
        u = c + 16 | 0;
        e[u >> 3] = -1797693134862315708145274.0e284;
        if ((r | 0) > 0) {
          g = b[a + 4 >> 2] | 0;
          o = 1797693134862315708145274.0e284;
          p = -1797693134862315708145274.0e284;
          f = 0;
          d = -1;
          k = 1797693134862315708145274.0e284;
          l = 1797693134862315708145274.0e284;
          n = -1797693134862315708145274.0e284;
          i = -1797693134862315708145274.0e284;
          v = 0;
          while (1) {
            h = +e[g + (v << 4) >> 3];
            m = +e[g + (v << 4) + 8 >> 3];
            z = d + 2 | 0;
            j = +e[g + (((z | 0) == (r | 0) ? 0 : z) << 4) + 8 >> 3];
            if (h < k) {
              e[s >> 3] = h;
              k = h;
            }
            if (m < l) {
              e[t >> 3] = m;
              l = m;
            }
            if (h > n) { e[c >> 3] = h; }else { h = n; }
            if (m > i) {
              e[u >> 3] = m;
              i = m;
            }
            o = m > 0.0 & m < o ? m : o;
            p = m < 0.0 & m > p ? m : p;
            f = f | +q(+(m - j)) > 3.141592653589793;
            d = v + 1 | 0;
            if ((d | 0) == (r | 0)) { break; }else {
              z = v;
              n = h;
              v = d;
              d = z;
            }
          }
          if (f) {
            e[u >> 3] = p;
            e[t >> 3] = o;
          }
        }
      } else {
        b[c >> 2] = 0;
        b[c + 4 >> 2] = 0;
        b[c + 8 >> 2] = 0;
        b[c + 12 >> 2] = 0;
        b[c + 16 >> 2] = 0;
        b[c + 20 >> 2] = 0;
        b[c + 24 >> 2] = 0;
        b[c + 28 >> 2] = 0;
      }
      z = a + 8 | 0;
      d = b[z >> 2] | 0;
      if ((d | 0) <= 0) { return; }
      y = a + 12 | 0;
      x = 0;
      do {
        g = b[y >> 2] | 0;
        f = x;
        x = x + 1 | 0;
        t = c + (x << 5) | 0;
        u = b[g + (f << 3) >> 2] | 0;
        if (u) {
          v = c + (x << 5) + 8 | 0;
          e[v >> 3] = 1797693134862315708145274.0e284;
          a = c + (x << 5) + 24 | 0;
          e[a >> 3] = 1797693134862315708145274.0e284;
          e[t >> 3] = -1797693134862315708145274.0e284;
          w = c + (x << 5) + 16 | 0;
          e[w >> 3] = -1797693134862315708145274.0e284;
          if ((u | 0) > 0) {
            r = b[g + (f << 3) + 4 >> 2] | 0;
            o = 1797693134862315708145274.0e284;
            p = -1797693134862315708145274.0e284;
            g = 0;
            f = -1;
            s = 0;
            k = 1797693134862315708145274.0e284;
            l = 1797693134862315708145274.0e284;
            m = -1797693134862315708145274.0e284;
            i = -1797693134862315708145274.0e284;
            while (1) {
              h = +e[r + (s << 4) >> 3];
              n = +e[r + (s << 4) + 8 >> 3];
              f = f + 2 | 0;
              j = +e[r + (((f | 0) == (u | 0) ? 0 : f) << 4) + 8 >> 3];
              if (h < k) {
                e[v >> 3] = h;
                k = h;
              }
              if (n < l) {
                e[a >> 3] = n;
                l = n;
              }
              if (h > m) { e[t >> 3] = h; }else { h = m; }
              if (n > i) {
                e[w >> 3] = n;
                i = n;
              }
              o = n > 0.0 & n < o ? n : o;
              p = n < 0.0 & n > p ? n : p;
              g = g | +q(+(n - j)) > 3.141592653589793;
              f = s + 1 | 0;
              if ((f | 0) == (u | 0)) { break; }else {
                A = s;
                s = f;
                m = h;
                f = A;
              }
            }
            if (g) {
              e[w >> 3] = p;
              e[a >> 3] = o;
            }
          }
        } else {
          b[t >> 2] = 0;
          b[t + 4 >> 2] = 0;
          b[t + 8 >> 2] = 0;
          b[t + 12 >> 2] = 0;
          b[t + 16 >> 2] = 0;
          b[t + 20 >> 2] = 0;
          b[t + 24 >> 2] = 0;
          b[t + 28 >> 2] = 0;
          d = b[z >> 2] | 0;
        }
      } while ((x | 0) < (d | 0));
      return;
    }
    function Xc(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var e = 0,
        f = 0,
        g = 0;
      if (!(Tc(a, c, d) | 0)) {
        f = 0;
        return f | 0;
      }
      f = a + 8 | 0;
      if ((b[f >> 2] | 0) <= 0) {
        f = 1;
        return f | 0;
      }
      e = a + 12 | 0;
      a = 0;
      while (1) {
        g = a;
        a = a + 1 | 0;
        if (Tc((b[e >> 2] | 0) + (g << 3) | 0, c + (a << 5) | 0, d) | 0) {
          a = 0;
          e = 6;
          break;
        }
        if ((a | 0) >= (b[f >> 2] | 0)) {
          a = 1;
          e = 6;
          break;
        }
      }
      if ((e | 0) == 6) { return a | 0; }
      return 0;
    }
    function Yc(a, c, d, e) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      var f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0;
      k = T;
      T = T + 16 | 0;
      i = k;
      h = d + 8 | 0;
      if (!(Tc(a, c, h) | 0)) {
        j = 0;
        T = k;
        return j | 0;
      }
      j = a + 8 | 0;
      a: do { if ((b[j >> 2] | 0) > 0) {
        g = a + 12 | 0;
        f = 0;
        while (1) {
          l = f;
          f = f + 1 | 0;
          if (Tc((b[g >> 2] | 0) + (l << 3) | 0, c + (f << 5) | 0, h) | 0) {
            f = 0;
            break;
          }
          if ((f | 0) >= (b[j >> 2] | 0)) { break a; }
        }
        T = k;
        return f | 0;
      } } while (0);
      if (Zc(a, c, d, e) | 0) {
        l = 0;
        T = k;
        return l | 0;
      }
      b[i >> 2] = b[d >> 2];
      b[i + 4 >> 2] = h;
      f = b[j >> 2] | 0;
      b: do { if ((f | 0) > 0) {
        a = a + 12 | 0;
        h = 0;
        g = f;
        while (1) {
          f = b[a >> 2] | 0;
          if ((b[f + (h << 3) >> 2] | 0) > 0) {
            if (Tc(i, e, b[f + (h << 3) + 4 >> 2] | 0) | 0) {
              f = 0;
              break b;
            }
            f = h + 1 | 0;
            if (Zc((b[a >> 2] | 0) + (h << 3) | 0, c + (f << 5) | 0, d, e) | 0) {
              f = 0;
              break b;
            }
            g = b[j >> 2] | 0;
          } else { f = h + 1 | 0; }
          if ((f | 0) < (g | 0)) { h = f; }else {
            f = 1;
            break;
          }
        }
      } else { f = 1; } } while (0);
      l = f;
      T = k;
      return l | 0;
    }
    function Zc(a, c, d, f) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      f = f | 0;
      var g = 0,
        h = 0.0,
        i = 0.0,
        j = 0.0,
        k = 0.0,
        l = 0.0,
        m = 0.0,
        n = 0.0,
        o = 0.0,
        p = 0.0,
        q = 0.0,
        r = 0.0,
        s = 0.0,
        t = 0.0,
        u = 0,
        v = 0,
        w = 0.0,
        x = 0.0,
        y = 0,
        z = 0,
        A = 0;
      y = T;
      T = T + 176 | 0;
      u = y + 172 | 0;
      g = y + 168 | 0;
      v = y;
      if (!(Ba(c, f) | 0)) {
        a = 0;
        T = y;
        return a | 0;
      }
      Ca(c, f, u, g);
      Ud(v | 0, d | 0, 168) | 0;
      if ((b[d >> 2] | 0) > 0) {
        c = 0;
        do {
          z = v + 8 + (c << 4) + 8 | 0;
          t = +ic(+e[z >> 3], b[g >> 2] | 0);
          e[z >> 3] = t;
          c = c + 1 | 0;
        } while ((c | 0) < (b[d >> 2] | 0));
      }
      r = +e[f >> 3];
      s = +e[f + 8 >> 3];
      t = +ic(+e[f + 16 >> 3], b[g >> 2] | 0);
      p = +ic(+e[f + 24 >> 3], b[g >> 2] | 0);
      a: do { if ((b[a >> 2] | 0) > 0) {
        f = a + 4 | 0;
        g = b[v >> 2] | 0;
        if ((g | 0) <= 0) {
          c = 0;
          while (1) {
            c = c + 1 | 0;
            if ((c | 0) >= (b[a >> 2] | 0)) {
              c = 0;
              break a;
            }
          }
        }
        d = 0;
        while (1) {
          c = b[f >> 2] | 0;
          o = +e[c + (d << 4) >> 3];
          q = +ic(+e[c + (d << 4) + 8 >> 3], b[u >> 2] | 0);
          c = b[f >> 2] | 0;
          d = d + 1 | 0;
          z = (d | 0) % (b[a >> 2] | 0) | 0;
          h = +e[c + (z << 4) >> 3];
          i = +ic(+e[c + (z << 4) + 8 >> 3], b[u >> 2] | 0);
          if (((!(o >= r) | !(h >= r) ? !(o <= s) | !(h <= s) : 0) ? !(q <= p) | !(i <= p) : 0) ? !(q >= t) | !(i >= t) : 0) {
            n = h - o;
            l = i - q;
            c = 0;
            do {
              A = c;
              c = c + 1 | 0;
              z = (c | 0) == (g | 0) ? 0 : c;
              h = +e[v + 8 + (A << 4) + 8 >> 3];
              i = +e[v + 8 + (z << 4) + 8 >> 3] - h;
              j = +e[v + 8 + (A << 4) >> 3];
              k = +e[v + 8 + (z << 4) >> 3] - j;
              m = n * i - l * k;
              if ((m != 0.0 ? (w = q - h, x = o - j, k = (w * k - i * x) / m, !(k < 0.0 | k > 1.0)) : 0) ? (m = (n * w - l * x) / m, m >= 0.0 & m <= 1.0) : 0) {
                c = 1;
                break a;
              }
            } while ((c | 0) < (g | 0));
          }
          if ((d | 0) >= (b[a >> 2] | 0)) {
            c = 0;
            break;
          }
        }
      } else { c = 0; } } while (0);
      A = c;
      T = y;
      return A | 0;
    }
    function _c(a, c, d, e) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      var f = 0,
        g = 0,
        h = 0;
      if (Zc(a, c, d, e) | 0) {
        g = 1;
        return g | 0;
      }
      g = a + 8 | 0;
      if ((b[g >> 2] | 0) <= 0) {
        g = 0;
        return g | 0;
      }
      f = a + 12 | 0;
      a = 0;
      while (1) {
        h = a;
        a = a + 1 | 0;
        if (Zc((b[f >> 2] | 0) + (h << 3) | 0, c + (a << 5) | 0, d, e) | 0) {
          a = 1;
          f = 6;
          break;
        }
        if ((a | 0) >= (b[g >> 2] | 0)) {
          a = 0;
          f = 6;
          break;
        }
      }
      if ((f | 0) == 6) { return a | 0; }
      return 0;
    }
    function $c() {
      return 8;
    }
    function ad() {
      return 16;
    }
    function bd() {
      return 168;
    }
    function cd() {
      return 8;
    }
    function dd() {
      return 16;
    }
    function ed() {
      return 12;
    }
    function fd() {
      return 8;
    }
    function gd(a) {
      a = a | 0;
      return +(+((b[a >> 2] | 0) >>> 0) + 4294967296.0 * +(b[a + 4 >> 2] | 0));
    }
    function hd(a) {
      a = a | 0;
      var b = 0.0,
        c = 0.0;
      c = +e[a >> 3];
      b = +e[a + 8 >> 3];
      return + +r(+(c * c + b * b));
    }
    function id(a, b, c, d, f) {
      a = a | 0;
      b = b | 0;
      c = c | 0;
      d = d | 0;
      f = f | 0;
      var g = 0.0,
        h = 0.0,
        i = 0.0,
        j = 0.0,
        k = 0.0,
        l = 0.0,
        m = 0.0,
        n = 0.0;
      k = +e[a >> 3];
      j = +e[b >> 3] - k;
      i = +e[a + 8 >> 3];
      h = +e[b + 8 >> 3] - i;
      m = +e[c >> 3];
      g = +e[d >> 3] - m;
      n = +e[c + 8 >> 3];
      l = +e[d + 8 >> 3] - n;
      g = (g * (i - n) - (k - m) * l) / (j * l - h * g);
      e[f >> 3] = k + j * g;
      e[f + 8 >> 3] = i + h * g;
      return;
    }
    function jd(a, b) {
      a = a | 0;
      b = b | 0;
      if (!(+q(+(+e[a >> 3] - +e[b >> 3])) < 1.1920928955078125e-07)) {
        b = 0;
        return b | 0;
      }
      b = +q(+(+e[a + 8 >> 3] - +e[b + 8 >> 3])) < 1.1920928955078125e-07;
      return b | 0;
    }
    function kd(a, b) {
      a = a | 0;
      b = b | 0;
      var c = 0.0,
        d = 0.0,
        f = 0.0;
      f = +e[a >> 3] - +e[b >> 3];
      d = +e[a + 8 >> 3] - +e[b + 8 >> 3];
      c = +e[a + 16 >> 3] - +e[b + 16 >> 3];
      return +(f * f + d * d + c * c);
    }
    function ld(a, b) {
      a = a | 0;
      b = b | 0;
      var c = 0.0,
        d = 0.0,
        f = 0.0;
      c = +e[a >> 3];
      d = +t(+c);
      c = +u(+c);
      e[b + 16 >> 3] = c;
      c = +e[a + 8 >> 3];
      f = d * +t(+c);
      e[b >> 3] = f;
      c = d * +u(+c);
      e[b + 8 >> 3] = c;
      return;
    }
    function md(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var e = 0,
        f = 0,
        g = 0;
      g = T;
      T = T + 16 | 0;
      f = g;
      e = Fb(a, c) | 0;
      if ((d + -1 | 0) >>> 0 > 5) {
        f = -1;
        T = g;
        return f | 0;
      }
      e = (e | 0) != 0;
      if ((d | 0) == 1 & e) {
        f = -1;
        T = g;
        return f | 0;
      }
      do { if (!(nd(a, c, f) | 0)) {
        if (e) {
          e = ((b[26352 + (d << 2) >> 2] | 0) + 5 - (b[f >> 2] | 0) | 0) % 5 | 0;
          break;
        } else {
          e = ((b[26384 + (d << 2) >> 2] | 0) + 6 - (b[f >> 2] | 0) | 0) % 6 | 0;
          break;
        }
      } else { e = -1; } } while (0);
      f = e;
      T = g;
      return f | 0;
    }
    function nd(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0;
      k = T;
      T = T + 32 | 0;
      h = k + 16 | 0;
      i = k;
      e = Wb(a, c, h) | 0;
      if (e | 0) {
        d = e;
        T = k;
        return d | 0;
      }
      g = Ab(a, c) | 0;
      j = Nb(a, c) | 0;
      qa(g, i);
      e = ra(g, b[h >> 2] | 0) | 0;
      a: do { if (ma(g) | 0) {
        do { switch (g | 0) {
          case 4:
            {
              a = 0;
              break;
            }
          case 14:
            {
              a = 1;
              break;
            }
          case 24:
            {
              a = 2;
              break;
            }
          case 38:
            {
              a = 3;
              break;
            }
          case 49:
            {
              a = 4;
              break;
            }
          case 58:
            {
              a = 5;
              break;
            }
          case 63:
            {
              a = 6;
              break;
            }
          case 72:
            {
              a = 7;
              break;
            }
          case 83:
            {
              a = 8;
              break;
            }
          case 97:
            {
              a = 9;
              break;
            }
          case 107:
            {
              a = 10;
              break;
            }
          case 117:
            {
              a = 11;
              break;
            }
          default:
            {
              e = 1;
              break a;
            }
        } } while (0);
        f = b[26416 + (a * 24 | 0) + 8 >> 2] | 0;
        c = b[26416 + (a * 24 | 0) + 16 >> 2] | 0;
        a = b[h >> 2] | 0;
        if ((a | 0) != (b[i >> 2] | 0)) {
          i = na(g) | 0;
          a = b[h >> 2] | 0;
          if (i | (a | 0) == (c | 0)) { e = (e + 1 | 0) % 6 | 0; }
        }
        if ((j | 0) == 3 & (a | 0) == (c | 0)) {
          e = (e + 5 | 0) % 6 | 0;
          f = 22;
          break;
        }
        if ((j | 0) == 5 & (a | 0) == (f | 0)) {
          e = (e + 1 | 0) % 6 | 0;
          f = 22;
        } else { f = 22; }
      } else { f = 22; } } while (0);
      if ((f | 0) == 22) {
        b[d >> 2] = e;
        e = 0;
      }
      d = e;
      T = k;
      return d | 0;
    }
    function od(a, c, d, e) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      var f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0,
        m = 0,
        n = 0,
        o = 0,
        p = 0,
        q = 0,
        r = 0,
        s = 0,
        t = 0,
        u = 0;
      u = T;
      T = T + 32 | 0;
      t = u + 24 | 0;
      r = u + 20 | 0;
      p = u + 8 | 0;
      o = u + 16 | 0;
      n = u;
      j = (Fb(a, c) | 0) == 0;
      j = j ? 6 : 5;
      l = Od(a | 0, c | 0, 52) | 0;
      H() | 0;
      l = l & 15;
      if (j >>> 0 <= d >>> 0) {
        e = 2;
        T = u;
        return e | 0;
      }
      m = (l | 0) == 0;
      if (!m ? (q = Pd(7, 0, (l ^ 15) * 3 | 0) | 0, (q & a | 0) == 0 & ((H() | 0) & c | 0) == 0) : 0) { f = d; }else { g = 4; }
      a: do { if ((g | 0) == 4) {
        f = (Fb(a, c) | 0) != 0;
        if (((f ? 4 : 5) | 0) < (d | 0)) {
          e = 1;
          T = u;
          return e | 0;
        }
        if (nd(a, c, t) | 0) {
          e = 1;
          T = u;
          return e | 0;
        }
        g = (b[t >> 2] | 0) + d | 0;
        if (f) { f = 26704 + (((g | 0) % 5 | 0) << 2) | 0; }else { f = 26736 + (((g | 0) % 6 | 0) << 2) | 0; }
        q = b[f >> 2] | 0;
        if ((q | 0) == 7) {
          e = 1;
          T = u;
          return e | 0;
        }
        b[r >> 2] = 0;
        f = ea(a, c, q, r, p) | 0;
        do { if (!f) {
          i = p;
          k = b[i >> 2] | 0;
          i = b[i + 4 >> 2] | 0;
          h = i >>> 0 < c >>> 0 | (i | 0) == (c | 0) & k >>> 0 < a >>> 0;
          g = h ? k : a;
          h = h ? i : c;
          if (!m ? (m = Pd(7, 0, (l ^ 15) * 3 | 0) | 0, (k & m | 0) == 0 & (i & (H() | 0) | 0) == 0) : 0) { f = d; }else {
            i = (d + -1 + j | 0) % (j | 0) | 0;
            f = Fb(a, c) | 0;
            if ((i | 0) < 0) { I(27795, 27797, 246, 27806); }
            j = (f | 0) != 0;
            if (((j ? 4 : 5) | 0) < (i | 0)) { I(27795, 27797, 246, 27806); }
            if (nd(a, c, t) | 0) { I(27795, 27797, 246, 27806); }
            f = (b[t >> 2] | 0) + i | 0;
            if (j) { f = 26704 + (((f | 0) % 5 | 0) << 2) | 0; }else { f = 26736 + (((f | 0) % 6 | 0) << 2) | 0; }
            i = b[f >> 2] | 0;
            if ((i | 0) == 7) { I(27795, 27797, 246, 27806); }
            b[o >> 2] = 0;
            f = ea(a, c, i, o, n) | 0;
            if (f | 0) { break; }
            k = n;
            j = b[k >> 2] | 0;
            k = b[k + 4 >> 2] | 0;
            do { if (k >>> 0 < h >>> 0 | (k | 0) == (h | 0) & j >>> 0 < g >>> 0) {
              if (!(Fb(j, k) | 0)) { g = b[26800 + ((((b[o >> 2] | 0) + (b[26768 + (i << 2) >> 2] | 0) | 0) % 6 | 0) << 2) >> 2] | 0; }else { g = fa(j, k, a, c) | 0; }
              f = Fb(j, k) | 0;
              if ((g + -1 | 0) >>> 0 > 5) {
                f = -1;
                g = j;
                h = k;
                break;
              }
              f = (f | 0) != 0;
              if ((g | 0) == 1 & f) {
                f = -1;
                g = j;
                h = k;
                break;
              }
              do { if (!(nd(j, k, t) | 0)) {
                if (f) {
                  f = ((b[26352 + (g << 2) >> 2] | 0) + 5 - (b[t >> 2] | 0) | 0) % 5 | 0;
                  break;
                } else {
                  f = ((b[26384 + (g << 2) >> 2] | 0) + 6 - (b[t >> 2] | 0) | 0) % 6 | 0;
                  break;
                }
              } else { f = -1; } } while (0);
              g = j;
              h = k;
            } else { f = d; } } while (0);
            i = p;
            k = b[i >> 2] | 0;
            i = b[i + 4 >> 2] | 0;
          }
          if ((g | 0) == (k | 0) & (h | 0) == (i | 0)) {
            j = (Fb(k, i) | 0) != 0;
            if (j) { a = fa(k, i, a, c) | 0; }else { a = b[26800 + ((((b[r >> 2] | 0) + (b[26768 + (q << 2) >> 2] | 0) | 0) % 6 | 0) << 2) >> 2] | 0; }
            f = Fb(k, i) | 0;
            if ((a + -1 | 0) >>> 0 <= 5 ? (s = (f | 0) != 0, !((a | 0) == 1 & s)) : 0) {
              do { if (!(nd(k, i, t) | 0)) {
                if (s) {
                  f = ((b[26352 + (a << 2) >> 2] | 0) + 5 - (b[t >> 2] | 0) | 0) % 5 | 0;
                  break;
                } else {
                  f = ((b[26384 + (a << 2) >> 2] | 0) + 6 - (b[t >> 2] | 0) | 0) % 6 | 0;
                  break;
                }
              } else { f = -1; } } while (0);
            } else { f = -1; }
            f = f + 1 | 0;
            f = (f | 0) == 6 | j & (f | 0) == 5 ? 0 : f;
          }
          c = h;
          a = g;
          break a;
        } } while (0);
        e = f;
        T = u;
        return e | 0;
      } } while (0);
      s = Pd(f | 0, 0, 56) | 0;
      t = H() | 0 | c & -2130706433 | 536870912;
      b[e >> 2] = s | a;
      b[e + 4 >> 2] = t;
      e = 0;
      T = u;
      return e | 0;
    }
    function pd(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var e = 0,
        f = 0,
        g = 0;
      g = (Fb(a, c) | 0) == 0;
      e = od(a, c, 0, d) | 0;
      f = (e | 0) == 0;
      if (g) {
        if (!f) {
          g = e;
          return g | 0;
        }
        e = od(a, c, 1, d + 8 | 0) | 0;
        if (e | 0) {
          g = e;
          return g | 0;
        }
        e = od(a, c, 2, d + 16 | 0) | 0;
        if (e | 0) {
          g = e;
          return g | 0;
        }
        e = od(a, c, 3, d + 24 | 0) | 0;
        if (e | 0) {
          g = e;
          return g | 0;
        }
        e = od(a, c, 4, d + 32 | 0) | 0;
        if (!e) { return od(a, c, 5, d + 40 | 0) | 0; }else {
          g = e;
          return g | 0;
        }
      }
      if (!f) {
        g = e;
        return g | 0;
      }
      e = od(a, c, 1, d + 8 | 0) | 0;
      if (e | 0) {
        g = e;
        return g | 0;
      }
      e = od(a, c, 2, d + 16 | 0) | 0;
      if (e | 0) {
        g = e;
        return g | 0;
      }
      e = od(a, c, 3, d + 24 | 0) | 0;
      if (e | 0) {
        g = e;
        return g | 0;
      }
      e = od(a, c, 4, d + 32 | 0) | 0;
      if (e | 0) {
        g = e;
        return g | 0;
      }
      g = d + 40 | 0;
      b[g >> 2] = 0;
      b[g + 4 >> 2] = 0;
      g = 0;
      return g | 0;
    }
    function qd(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0;
      j = T;
      T = T + 192 | 0;
      f = j;
      g = j + 168 | 0;
      h = Od(a | 0, c | 0, 56) | 0;
      H() | 0;
      h = h & 7;
      i = c & -2130706433 | 134217728;
      e = Wb(a, i, g) | 0;
      if (e | 0) {
        i = e;
        T = j;
        return i | 0;
      }
      c = Od(a | 0, c | 0, 52) | 0;
      H() | 0;
      c = c & 15;
      if (!(Fb(a, i) | 0)) { xb(g, c, h, 1, f); }else { tb(g, c, h, 1, f); }
      i = f + 8 | 0;
      b[d >> 2] = b[i >> 2];
      b[d + 4 >> 2] = b[i + 4 >> 2];
      b[d + 8 >> 2] = b[i + 8 >> 2];
      b[d + 12 >> 2] = b[i + 12 >> 2];
      i = 0;
      T = j;
      return i | 0;
    }
    function rd(a, c) {
      a = a | 0;
      c = c | 0;
      var d = 0,
        e = 0,
        f = 0,
        g = 0;
      f = T;
      T = T + 16 | 0;
      d = f;
      if (!(0 == 0 & (c & 2013265920 | 0) == 536870912)) {
        e = 0;
        T = f;
        return e | 0;
      }
      e = c & -2130706433 | 134217728;
      if (!(Bb(a, e) | 0)) {
        e = 0;
        T = f;
        return e | 0;
      }
      g = Od(a | 0, c | 0, 56) | 0;
      H() | 0;
      g = (od(a, e, g & 7, d) | 0) == 0;
      e = d;
      e = g & ((b[e >> 2] | 0) == (a | 0) ? (b[e + 4 >> 2] | 0) == (c | 0) : 0) & 1;
      T = f;
      return e | 0;
    }
    function sd(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var e = 0;
      if ((c | 0) > 0) {
        e = Dd(c, 4) | 0;
        b[a >> 2] = e;
        if (!e) { I(27819, 27842, 40, 27856); }
      } else { b[a >> 2] = 0; }
      b[a + 4 >> 2] = c;
      b[a + 8 >> 2] = 0;
      b[a + 12 >> 2] = d;
      return;
    }
    function td(a) {
      a = a | 0;
      var c = 0,
        d = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0;
      g = a + 4 | 0;
      h = a + 12 | 0;
      i = a + 8 | 0;
      a: while (1) {
        d = b[g >> 2] | 0;
        c = 0;
        while (1) {
          if ((c | 0) >= (d | 0)) { break a; }
          f = b[a >> 2] | 0;
          j = b[f + (c << 2) >> 2] | 0;
          if (!j) { c = c + 1 | 0; }else { break; }
        }
        c = f + (~~(+q(+(+s(10.0, + +(15 - (b[h >> 2] | 0) | 0)) * (+e[j >> 3] + +e[j + 8 >> 3]))) % +(d | 0)) >>> 0 << 2) | 0;
        d = b[c >> 2] | 0;
        b: do { if (d | 0) {
          f = j + 32 | 0;
          if ((d | 0) == (j | 0)) { b[c >> 2] = b[f >> 2]; }else {
            d = d + 32 | 0;
            c = b[d >> 2] | 0;
            if (!c) { break; }
            while (1) {
              if ((c | 0) == (j | 0)) { break; }
              d = c + 32 | 0;
              c = b[d >> 2] | 0;
              if (!c) { break b; }
            }
            b[d >> 2] = b[f >> 2];
          }
          Cd(j);
          b[i >> 2] = (b[i >> 2] | 0) + -1;
        } } while (0);
      }
      Cd(b[a >> 2] | 0);
      return;
    }
    function ud(a) {
      a = a | 0;
      var c = 0,
        d = 0,
        e = 0;
      e = b[a + 4 >> 2] | 0;
      d = 0;
      while (1) {
        if ((d | 0) >= (e | 0)) {
          c = 0;
          d = 4;
          break;
        }
        c = b[(b[a >> 2] | 0) + (d << 2) >> 2] | 0;
        if (!c) { d = d + 1 | 0; }else {
          d = 4;
          break;
        }
      }
      if ((d | 0) == 4) { return c | 0; }
      return 0;
    }
    function vd(a, c) {
      a = a | 0;
      c = c | 0;
      var d = 0,
        f = 0,
        g = 0,
        h = 0;
      d = ~~(+q(+(+s(10.0, + +(15 - (b[a + 12 >> 2] | 0) | 0)) * (+e[c >> 3] + +e[c + 8 >> 3]))) % +(b[a + 4 >> 2] | 0)) >>> 0;
      d = (b[a >> 2] | 0) + (d << 2) | 0;
      f = b[d >> 2] | 0;
      if (!f) {
        h = 1;
        return h | 0;
      }
      h = c + 32 | 0;
      do { if ((f | 0) != (c | 0)) {
        d = b[f + 32 >> 2] | 0;
        if (!d) {
          h = 1;
          return h | 0;
        }
        g = d;
        while (1) {
          if ((g | 0) == (c | 0)) {
            g = 8;
            break;
          }
          d = b[g + 32 >> 2] | 0;
          if (!d) {
            d = 1;
            g = 10;
            break;
          } else {
            f = g;
            g = d;
          }
        }
        if ((g | 0) == 8) {
          b[f + 32 >> 2] = b[h >> 2];
          break;
        } else if ((g | 0) == 10) { return d | 0; }
      } else { b[d >> 2] = b[h >> 2]; } } while (0);
      Cd(c);
      h = a + 8 | 0;
      b[h >> 2] = (b[h >> 2] | 0) + -1;
      h = 0;
      return h | 0;
    }
    function wd(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var f = 0,
        g = 0,
        h = 0,
        i = 0;
      h = Bd(40) | 0;
      if (!h) { I(27872, 27842, 98, 27885); }
      b[h >> 2] = b[c >> 2];
      b[h + 4 >> 2] = b[c + 4 >> 2];
      b[h + 8 >> 2] = b[c + 8 >> 2];
      b[h + 12 >> 2] = b[c + 12 >> 2];
      g = h + 16 | 0;
      b[g >> 2] = b[d >> 2];
      b[g + 4 >> 2] = b[d + 4 >> 2];
      b[g + 8 >> 2] = b[d + 8 >> 2];
      b[g + 12 >> 2] = b[d + 12 >> 2];
      b[h + 32 >> 2] = 0;
      g = ~~(+q(+(+s(10.0, + +(15 - (b[a + 12 >> 2] | 0) | 0)) * (+e[c >> 3] + +e[c + 8 >> 3]))) % +(b[a + 4 >> 2] | 0)) >>> 0;
      g = (b[a >> 2] | 0) + (g << 2) | 0;
      f = b[g >> 2] | 0;
      do { if (!f) { b[g >> 2] = h; }else {
        while (1) {
          if (hc(f, c) | 0 ? hc(f + 16 | 0, d) | 0 : 0) { break; }
          g = b[f + 32 >> 2] | 0;
          f = (g | 0) == 0 ? f : g;
          if (!(b[f + 32 >> 2] | 0)) {
            i = 10;
            break;
          }
        }
        if ((i | 0) == 10) {
          b[f + 32 >> 2] = h;
          break;
        }
        Cd(h);
        i = f;
        return i | 0;
      } } while (0);
      i = a + 8 | 0;
      b[i >> 2] = (b[i >> 2] | 0) + 1;
      i = h;
      return i | 0;
    }
    function xd(a, c, d) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      var f = 0,
        g = 0;
      g = ~~(+q(+(+s(10.0, + +(15 - (b[a + 12 >> 2] | 0) | 0)) * (+e[c >> 3] + +e[c + 8 >> 3]))) % +(b[a + 4 >> 2] | 0)) >>> 0;
      g = b[(b[a >> 2] | 0) + (g << 2) >> 2] | 0;
      if (!g) {
        d = 0;
        return d | 0;
      }
      if (!d) {
        a = g;
        while (1) {
          if (hc(a, c) | 0) {
            f = 10;
            break;
          }
          a = b[a + 32 >> 2] | 0;
          if (!a) {
            a = 0;
            f = 10;
            break;
          }
        }
        if ((f | 0) == 10) { return a | 0; }
      }
      a = g;
      while (1) {
        if (hc(a, c) | 0 ? hc(a + 16 | 0, d) | 0 : 0) {
          f = 10;
          break;
        }
        a = b[a + 32 >> 2] | 0;
        if (!a) {
          a = 0;
          f = 10;
          break;
        }
      }
      if ((f | 0) == 10) { return a | 0; }
      return 0;
    }
    function yd(a, c) {
      a = a | 0;
      c = c | 0;
      var d = 0;
      d = ~~(+q(+(+s(10.0, + +(15 - (b[a + 12 >> 2] | 0) | 0)) * (+e[c >> 3] + +e[c + 8 >> 3]))) % +(b[a + 4 >> 2] | 0)) >>> 0;
      a = b[(b[a >> 2] | 0) + (d << 2) >> 2] | 0;
      if (!a) {
        d = 0;
        return d | 0;
      }
      while (1) {
        if (hc(a, c) | 0) {
          c = 5;
          break;
        }
        a = b[a + 32 >> 2] | 0;
        if (!a) {
          a = 0;
          c = 5;
          break;
        }
      }
      if ((c | 0) == 5) { return a | 0; }
      return 0;
    }
    function zd() {
      return 27904;
    }
    function Ad(a) {
      a = +a;
      return ~~+Wd(+a) | 0;
    }
    function Bd(a) {
      a = a | 0;
      var c = 0,
        d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0,
        m = 0,
        n = 0,
        o = 0,
        p = 0,
        q = 0,
        r = 0,
        s = 0,
        t = 0,
        u = 0,
        v = 0,
        w = 0;
      w = T;
      T = T + 16 | 0;
      n = w;
      do { if (a >>> 0 < 245) {
        k = a >>> 0 < 11 ? 16 : a + 11 & -8;
        a = k >>> 3;
        m = b[6977] | 0;
        d = m >>> a;
        if (d & 3 | 0) {
          c = (d & 1 ^ 1) + a | 0;
          a = 27948 + (c << 1 << 2) | 0;
          d = a + 8 | 0;
          e = b[d >> 2] | 0;
          f = e + 8 | 0;
          g = b[f >> 2] | 0;
          if ((g | 0) == (a | 0)) { b[6977] = m & ~(1 << c); }else {
            b[g + 12 >> 2] = a;
            b[d >> 2] = g;
          }
          v = c << 3;
          b[e + 4 >> 2] = v | 3;
          v = e + v + 4 | 0;
          b[v >> 2] = b[v >> 2] | 1;
          v = f;
          T = w;
          return v | 0;
        }
        l = b[6979] | 0;
        if (k >>> 0 > l >>> 0) {
          if (d | 0) {
            c = 2 << a;
            c = d << a & (c | 0 - c);
            c = (c & 0 - c) + -1 | 0;
            i = c >>> 12 & 16;
            c = c >>> i;
            d = c >>> 5 & 8;
            c = c >>> d;
            g = c >>> 2 & 4;
            c = c >>> g;
            a = c >>> 1 & 2;
            c = c >>> a;
            e = c >>> 1 & 1;
            e = (d | i | g | a | e) + (c >>> e) | 0;
            c = 27948 + (e << 1 << 2) | 0;
            a = c + 8 | 0;
            g = b[a >> 2] | 0;
            i = g + 8 | 0;
            d = b[i >> 2] | 0;
            if ((d | 0) == (c | 0)) {
              a = m & ~(1 << e);
              b[6977] = a;
            } else {
              b[d + 12 >> 2] = c;
              b[a >> 2] = d;
              a = m;
            }
            v = e << 3;
            h = v - k | 0;
            b[g + 4 >> 2] = k | 3;
            f = g + k | 0;
            b[f + 4 >> 2] = h | 1;
            b[g + v >> 2] = h;
            if (l | 0) {
              e = b[6982] | 0;
              c = l >>> 3;
              d = 27948 + (c << 1 << 2) | 0;
              c = 1 << c;
              if (!(a & c)) {
                b[6977] = a | c;
                c = d;
                a = d + 8 | 0;
              } else {
                a = d + 8 | 0;
                c = b[a >> 2] | 0;
              }
              b[a >> 2] = e;
              b[c + 12 >> 2] = e;
              b[e + 8 >> 2] = c;
              b[e + 12 >> 2] = d;
            }
            b[6979] = h;
            b[6982] = f;
            v = i;
            T = w;
            return v | 0;
          }
          g = b[6978] | 0;
          if (g) {
            d = (g & 0 - g) + -1 | 0;
            f = d >>> 12 & 16;
            d = d >>> f;
            e = d >>> 5 & 8;
            d = d >>> e;
            h = d >>> 2 & 4;
            d = d >>> h;
            i = d >>> 1 & 2;
            d = d >>> i;
            j = d >>> 1 & 1;
            j = b[28212 + ((e | f | h | i | j) + (d >>> j) << 2) >> 2] | 0;
            d = j;
            i = j;
            j = (b[j + 4 >> 2] & -8) - k | 0;
            while (1) {
              a = b[d + 16 >> 2] | 0;
              if (!a) {
                a = b[d + 20 >> 2] | 0;
                if (!a) { break; }
              }
              h = (b[a + 4 >> 2] & -8) - k | 0;
              f = h >>> 0 < j >>> 0;
              d = a;
              i = f ? a : i;
              j = f ? h : j;
            }
            h = i + k | 0;
            if (h >>> 0 > i >>> 0) {
              f = b[i + 24 >> 2] | 0;
              c = b[i + 12 >> 2] | 0;
              do { if ((c | 0) == (i | 0)) {
                a = i + 20 | 0;
                c = b[a >> 2] | 0;
                if (!c) {
                  a = i + 16 | 0;
                  c = b[a >> 2] | 0;
                  if (!c) {
                    d = 0;
                    break;
                  }
                }
                while (1) {
                  e = c + 20 | 0;
                  d = b[e >> 2] | 0;
                  if (!d) {
                    e = c + 16 | 0;
                    d = b[e >> 2] | 0;
                    if (!d) { break; }else {
                      c = d;
                      a = e;
                    }
                  } else {
                    c = d;
                    a = e;
                  }
                }
                b[a >> 2] = 0;
                d = c;
              } else {
                d = b[i + 8 >> 2] | 0;
                b[d + 12 >> 2] = c;
                b[c + 8 >> 2] = d;
                d = c;
              } } while (0);
              do { if (f | 0) {
                c = b[i + 28 >> 2] | 0;
                a = 28212 + (c << 2) | 0;
                if ((i | 0) == (b[a >> 2] | 0)) {
                  b[a >> 2] = d;
                  if (!d) {
                    b[6978] = g & ~(1 << c);
                    break;
                  }
                } else {
                  v = f + 16 | 0;
                  b[((b[v >> 2] | 0) == (i | 0) ? v : f + 20 | 0) >> 2] = d;
                  if (!d) { break; }
                }
                b[d + 24 >> 2] = f;
                c = b[i + 16 >> 2] | 0;
                if (c | 0) {
                  b[d + 16 >> 2] = c;
                  b[c + 24 >> 2] = d;
                }
                c = b[i + 20 >> 2] | 0;
                if (c | 0) {
                  b[d + 20 >> 2] = c;
                  b[c + 24 >> 2] = d;
                }
              } } while (0);
              if (j >>> 0 < 16) {
                v = j + k | 0;
                b[i + 4 >> 2] = v | 3;
                v = i + v + 4 | 0;
                b[v >> 2] = b[v >> 2] | 1;
              } else {
                b[i + 4 >> 2] = k | 3;
                b[h + 4 >> 2] = j | 1;
                b[h + j >> 2] = j;
                if (l | 0) {
                  e = b[6982] | 0;
                  c = l >>> 3;
                  d = 27948 + (c << 1 << 2) | 0;
                  c = 1 << c;
                  if (!(c & m)) {
                    b[6977] = c | m;
                    c = d;
                    a = d + 8 | 0;
                  } else {
                    a = d + 8 | 0;
                    c = b[a >> 2] | 0;
                  }
                  b[a >> 2] = e;
                  b[c + 12 >> 2] = e;
                  b[e + 8 >> 2] = c;
                  b[e + 12 >> 2] = d;
                }
                b[6979] = j;
                b[6982] = h;
              }
              v = i + 8 | 0;
              T = w;
              return v | 0;
            } else { m = k; }
          } else { m = k; }
        } else { m = k; }
      } else if (a >>> 0 <= 4294967231) {
        a = a + 11 | 0;
        k = a & -8;
        e = b[6978] | 0;
        if (e) {
          f = 0 - k | 0;
          a = a >>> 8;
          if (a) {
            if (k >>> 0 > 16777215) { j = 31; }else {
              m = (a + 1048320 | 0) >>> 16 & 8;
              q = a << m;
              i = (q + 520192 | 0) >>> 16 & 4;
              q = q << i;
              j = (q + 245760 | 0) >>> 16 & 2;
              j = 14 - (i | m | j) + (q << j >>> 15) | 0;
              j = k >>> (j + 7 | 0) & 1 | j << 1;
            }
          } else { j = 0; }
          d = b[28212 + (j << 2) >> 2] | 0;
          a: do { if (!d) {
            d = 0;
            a = 0;
            q = 61;
          } else {
            a = 0;
            i = k << ((j | 0) == 31 ? 0 : 25 - (j >>> 1) | 0);
            g = 0;
            while (1) {
              h = (b[d + 4 >> 2] & -8) - k | 0;
              if (h >>> 0 < f >>> 0) { if (!h) {
                a = d;
                f = 0;
                q = 65;
                break a;
              } else {
                a = d;
                f = h;
              } }
              q = b[d + 20 >> 2] | 0;
              d = b[d + 16 + (i >>> 31 << 2) >> 2] | 0;
              g = (q | 0) == 0 | (q | 0) == (d | 0) ? g : q;
              if (!d) {
                d = g;
                q = 61;
                break;
              } else { i = i << 1; }
            }
          } } while (0);
          if ((q | 0) == 61) {
            if ((d | 0) == 0 & (a | 0) == 0) {
              a = 2 << j;
              a = (a | 0 - a) & e;
              if (!a) {
                m = k;
                break;
              }
              m = (a & 0 - a) + -1 | 0;
              h = m >>> 12 & 16;
              m = m >>> h;
              g = m >>> 5 & 8;
              m = m >>> g;
              i = m >>> 2 & 4;
              m = m >>> i;
              j = m >>> 1 & 2;
              m = m >>> j;
              d = m >>> 1 & 1;
              a = 0;
              d = b[28212 + ((g | h | i | j | d) + (m >>> d) << 2) >> 2] | 0;
            }
            if (!d) {
              i = a;
              h = f;
            } else { q = 65; }
          }
          if ((q | 0) == 65) {
            g = d;
            while (1) {
              m = (b[g + 4 >> 2] & -8) - k | 0;
              d = m >>> 0 < f >>> 0;
              f = d ? m : f;
              a = d ? g : a;
              d = b[g + 16 >> 2] | 0;
              if (!d) { d = b[g + 20 >> 2] | 0; }
              if (!d) {
                i = a;
                h = f;
                break;
              } else { g = d; }
            }
          }
          if (((i | 0) != 0 ? h >>> 0 < ((b[6979] | 0) - k | 0) >>> 0 : 0) ? (l = i + k | 0, l >>> 0 > i >>> 0) : 0) {
            g = b[i + 24 >> 2] | 0;
            c = b[i + 12 >> 2] | 0;
            do { if ((c | 0) == (i | 0)) {
              a = i + 20 | 0;
              c = b[a >> 2] | 0;
              if (!c) {
                a = i + 16 | 0;
                c = b[a >> 2] | 0;
                if (!c) {
                  c = 0;
                  break;
                }
              }
              while (1) {
                f = c + 20 | 0;
                d = b[f >> 2] | 0;
                if (!d) {
                  f = c + 16 | 0;
                  d = b[f >> 2] | 0;
                  if (!d) { break; }else {
                    c = d;
                    a = f;
                  }
                } else {
                  c = d;
                  a = f;
                }
              }
              b[a >> 2] = 0;
            } else {
              v = b[i + 8 >> 2] | 0;
              b[v + 12 >> 2] = c;
              b[c + 8 >> 2] = v;
            } } while (0);
            do { if (g) {
              a = b[i + 28 >> 2] | 0;
              d = 28212 + (a << 2) | 0;
              if ((i | 0) == (b[d >> 2] | 0)) {
                b[d >> 2] = c;
                if (!c) {
                  e = e & ~(1 << a);
                  b[6978] = e;
                  break;
                }
              } else {
                v = g + 16 | 0;
                b[((b[v >> 2] | 0) == (i | 0) ? v : g + 20 | 0) >> 2] = c;
                if (!c) { break; }
              }
              b[c + 24 >> 2] = g;
              a = b[i + 16 >> 2] | 0;
              if (a | 0) {
                b[c + 16 >> 2] = a;
                b[a + 24 >> 2] = c;
              }
              a = b[i + 20 >> 2] | 0;
              if (a) {
                b[c + 20 >> 2] = a;
                b[a + 24 >> 2] = c;
              }
            } } while (0);
            b: do { if (h >>> 0 < 16) {
              v = h + k | 0;
              b[i + 4 >> 2] = v | 3;
              v = i + v + 4 | 0;
              b[v >> 2] = b[v >> 2] | 1;
            } else {
              b[i + 4 >> 2] = k | 3;
              b[l + 4 >> 2] = h | 1;
              b[l + h >> 2] = h;
              c = h >>> 3;
              if (h >>> 0 < 256) {
                d = 27948 + (c << 1 << 2) | 0;
                a = b[6977] | 0;
                c = 1 << c;
                if (!(a & c)) {
                  b[6977] = a | c;
                  c = d;
                  a = d + 8 | 0;
                } else {
                  a = d + 8 | 0;
                  c = b[a >> 2] | 0;
                }
                b[a >> 2] = l;
                b[c + 12 >> 2] = l;
                b[l + 8 >> 2] = c;
                b[l + 12 >> 2] = d;
                break;
              }
              c = h >>> 8;
              if (c) {
                if (h >>> 0 > 16777215) { d = 31; }else {
                  u = (c + 1048320 | 0) >>> 16 & 8;
                  v = c << u;
                  t = (v + 520192 | 0) >>> 16 & 4;
                  v = v << t;
                  d = (v + 245760 | 0) >>> 16 & 2;
                  d = 14 - (t | u | d) + (v << d >>> 15) | 0;
                  d = h >>> (d + 7 | 0) & 1 | d << 1;
                }
              } else { d = 0; }
              c = 28212 + (d << 2) | 0;
              b[l + 28 >> 2] = d;
              a = l + 16 | 0;
              b[a + 4 >> 2] = 0;
              b[a >> 2] = 0;
              a = 1 << d;
              if (!(e & a)) {
                b[6978] = e | a;
                b[c >> 2] = l;
                b[l + 24 >> 2] = c;
                b[l + 12 >> 2] = l;
                b[l + 8 >> 2] = l;
                break;
              }
              c = b[c >> 2] | 0;
              c: do { if ((b[c + 4 >> 2] & -8 | 0) != (h | 0)) {
                e = h << ((d | 0) == 31 ? 0 : 25 - (d >>> 1) | 0);
                while (1) {
                  d = c + 16 + (e >>> 31 << 2) | 0;
                  a = b[d >> 2] | 0;
                  if (!a) { break; }
                  if ((b[a + 4 >> 2] & -8 | 0) == (h | 0)) {
                    c = a;
                    break c;
                  } else {
                    e = e << 1;
                    c = a;
                  }
                }
                b[d >> 2] = l;
                b[l + 24 >> 2] = c;
                b[l + 12 >> 2] = l;
                b[l + 8 >> 2] = l;
                break b;
              } } while (0);
              u = c + 8 | 0;
              v = b[u >> 2] | 0;
              b[v + 12 >> 2] = l;
              b[u >> 2] = l;
              b[l + 8 >> 2] = v;
              b[l + 12 >> 2] = c;
              b[l + 24 >> 2] = 0;
            } } while (0);
            v = i + 8 | 0;
            T = w;
            return v | 0;
          } else { m = k; }
        } else { m = k; }
      } else { m = -1; } } while (0);
      d = b[6979] | 0;
      if (d >>> 0 >= m >>> 0) {
        c = d - m | 0;
        a = b[6982] | 0;
        if (c >>> 0 > 15) {
          v = a + m | 0;
          b[6982] = v;
          b[6979] = c;
          b[v + 4 >> 2] = c | 1;
          b[a + d >> 2] = c;
          b[a + 4 >> 2] = m | 3;
        } else {
          b[6979] = 0;
          b[6982] = 0;
          b[a + 4 >> 2] = d | 3;
          v = a + d + 4 | 0;
          b[v >> 2] = b[v >> 2] | 1;
        }
        v = a + 8 | 0;
        T = w;
        return v | 0;
      }
      h = b[6980] | 0;
      if (h >>> 0 > m >>> 0) {
        t = h - m | 0;
        b[6980] = t;
        v = b[6983] | 0;
        u = v + m | 0;
        b[6983] = u;
        b[u + 4 >> 2] = t | 1;
        b[v + 4 >> 2] = m | 3;
        v = v + 8 | 0;
        T = w;
        return v | 0;
      }
      if (!(b[7095] | 0)) {
        b[7097] = 4096;
        b[7096] = 4096;
        b[7098] = -1;
        b[7099] = -1;
        b[7100] = 0;
        b[7088] = 0;
        b[7095] = n & -16 ^ 1431655768;
        a = 4096;
      } else { a = b[7097] | 0; }
      i = m + 48 | 0;
      j = m + 47 | 0;
      g = a + j | 0;
      f = 0 - a | 0;
      k = g & f;
      if (k >>> 0 <= m >>> 0) {
        v = 0;
        T = w;
        return v | 0;
      }
      a = b[7087] | 0;
      if (a | 0 ? (l = b[7085] | 0, n = l + k | 0, n >>> 0 <= l >>> 0 | n >>> 0 > a >>> 0) : 0) {
        v = 0;
        T = w;
        return v | 0;
      }
      d: do { if (!(b[7088] & 4)) {
        d = b[6983] | 0;
        e: do { if (d) {
          e = 28356;
          while (1) {
            n = b[e >> 2] | 0;
            if (n >>> 0 <= d >>> 0 ? (n + (b[e + 4 >> 2] | 0) | 0) >>> 0 > d >>> 0 : 0) { break; }
            a = b[e + 8 >> 2] | 0;
            if (!a) {
              q = 128;
              break e;
            } else { e = a; }
          }
          c = g - h & f;
          if (c >>> 0 < 2147483647) {
            a = Xd(c | 0) | 0;
            if ((a | 0) == ((b[e >> 2] | 0) + (b[e + 4 >> 2] | 0) | 0)) {
              if ((a | 0) != (-1 | 0)) {
                h = c;
                g = a;
                q = 145;
                break d;
              }
            } else {
              e = a;
              q = 136;
            }
          } else { c = 0; }
        } else { q = 128; } } while (0);
        do { if ((q | 0) == 128) {
          d = Xd(0) | 0;
          if ((d | 0) != (-1 | 0) ? (c = d, o = b[7096] | 0, p = o + -1 | 0, c = ((p & c | 0) == 0 ? 0 : (p + c & 0 - o) - c | 0) + k | 0, o = b[7085] | 0, p = c + o | 0, c >>> 0 > m >>> 0 & c >>> 0 < 2147483647) : 0) {
            n = b[7087] | 0;
            if (n | 0 ? p >>> 0 <= o >>> 0 | p >>> 0 > n >>> 0 : 0) {
              c = 0;
              break;
            }
            a = Xd(c | 0) | 0;
            if ((a | 0) == (d | 0)) {
              h = c;
              g = d;
              q = 145;
              break d;
            } else {
              e = a;
              q = 136;
            }
          } else { c = 0; }
        } } while (0);
        do { if ((q | 0) == 136) {
          d = 0 - c | 0;
          if (!(i >>> 0 > c >>> 0 & (c >>> 0 < 2147483647 & (e | 0) != (-1 | 0)))) { if ((e | 0) == (-1 | 0)) {
            c = 0;
            break;
          } else {
            h = c;
            g = e;
            q = 145;
            break d;
          } }
          a = b[7097] | 0;
          a = j - c + a & 0 - a;
          if (a >>> 0 >= 2147483647) {
            h = c;
            g = e;
            q = 145;
            break d;
          }
          if ((Xd(a | 0) | 0) == (-1 | 0)) {
            Xd(d | 0) | 0;
            c = 0;
            break;
          } else {
            h = a + c | 0;
            g = e;
            q = 145;
            break d;
          }
        } } while (0);
        b[7088] = b[7088] | 4;
        q = 143;
      } else {
        c = 0;
        q = 143;
      } } while (0);
      if (((q | 0) == 143 ? k >>> 0 < 2147483647 : 0) ? (t = Xd(k | 0) | 0, p = Xd(0) | 0, r = p - t | 0, s = r >>> 0 > (m + 40 | 0) >>> 0, !((t | 0) == (-1 | 0) | s ^ 1 | t >>> 0 < p >>> 0 & ((t | 0) != (-1 | 0) & (p | 0) != (-1 | 0)) ^ 1)) : 0) {
        h = s ? r : c;
        g = t;
        q = 145;
      }
      if ((q | 0) == 145) {
        c = (b[7085] | 0) + h | 0;
        b[7085] = c;
        if (c >>> 0 > (b[7086] | 0) >>> 0) { b[7086] = c; }
        j = b[6983] | 0;
        f: do { if (j) {
          c = 28356;
          while (1) {
            a = b[c >> 2] | 0;
            d = b[c + 4 >> 2] | 0;
            if ((g | 0) == (a + d | 0)) {
              q = 154;
              break;
            }
            e = b[c + 8 >> 2] | 0;
            if (!e) { break; }else { c = e; }
          }
          if (((q | 0) == 154 ? (u = c + 4 | 0, (b[c + 12 >> 2] & 8 | 0) == 0) : 0) ? g >>> 0 > j >>> 0 & a >>> 0 <= j >>> 0 : 0) {
            b[u >> 2] = d + h;
            v = (b[6980] | 0) + h | 0;
            t = j + 8 | 0;
            t = (t & 7 | 0) == 0 ? 0 : 0 - t & 7;
            u = j + t | 0;
            t = v - t | 0;
            b[6983] = u;
            b[6980] = t;
            b[u + 4 >> 2] = t | 1;
            b[j + v + 4 >> 2] = 40;
            b[6984] = b[7099];
            break;
          }
          if (g >>> 0 < (b[6981] | 0) >>> 0) { b[6981] = g; }
          d = g + h | 0;
          c = 28356;
          while (1) {
            if ((b[c >> 2] | 0) == (d | 0)) {
              q = 162;
              break;
            }
            a = b[c + 8 >> 2] | 0;
            if (!a) { break; }else { c = a; }
          }
          if ((q | 0) == 162 ? (b[c + 12 >> 2] & 8 | 0) == 0 : 0) {
            b[c >> 2] = g;
            l = c + 4 | 0;
            b[l >> 2] = (b[l >> 2] | 0) + h;
            l = g + 8 | 0;
            l = g + ((l & 7 | 0) == 0 ? 0 : 0 - l & 7) | 0;
            c = d + 8 | 0;
            c = d + ((c & 7 | 0) == 0 ? 0 : 0 - c & 7) | 0;
            k = l + m | 0;
            i = c - l - m | 0;
            b[l + 4 >> 2] = m | 3;
            g: do { if ((j | 0) == (c | 0)) {
              v = (b[6980] | 0) + i | 0;
              b[6980] = v;
              b[6983] = k;
              b[k + 4 >> 2] = v | 1;
            } else {
              if ((b[6982] | 0) == (c | 0)) {
                v = (b[6979] | 0) + i | 0;
                b[6979] = v;
                b[6982] = k;
                b[k + 4 >> 2] = v | 1;
                b[k + v >> 2] = v;
                break;
              }
              a = b[c + 4 >> 2] | 0;
              if ((a & 3 | 0) == 1) {
                h = a & -8;
                e = a >>> 3;
                h: do { if (a >>> 0 < 256) {
                  a = b[c + 8 >> 2] | 0;
                  d = b[c + 12 >> 2] | 0;
                  if ((d | 0) == (a | 0)) {
                    b[6977] = b[6977] & ~(1 << e);
                    break;
                  } else {
                    b[a + 12 >> 2] = d;
                    b[d + 8 >> 2] = a;
                    break;
                  }
                } else {
                  g = b[c + 24 >> 2] | 0;
                  a = b[c + 12 >> 2] | 0;
                  do { if ((a | 0) == (c | 0)) {
                    d = c + 16 | 0;
                    e = d + 4 | 0;
                    a = b[e >> 2] | 0;
                    if (!a) {
                      a = b[d >> 2] | 0;
                      if (!a) {
                        a = 0;
                        break;
                      }
                    } else { d = e; }
                    while (1) {
                      f = a + 20 | 0;
                      e = b[f >> 2] | 0;
                      if (!e) {
                        f = a + 16 | 0;
                        e = b[f >> 2] | 0;
                        if (!e) { break; }else {
                          a = e;
                          d = f;
                        }
                      } else {
                        a = e;
                        d = f;
                      }
                    }
                    b[d >> 2] = 0;
                  } else {
                    v = b[c + 8 >> 2] | 0;
                    b[v + 12 >> 2] = a;
                    b[a + 8 >> 2] = v;
                  } } while (0);
                  if (!g) { break; }
                  d = b[c + 28 >> 2] | 0;
                  e = 28212 + (d << 2) | 0;
                  do { if ((b[e >> 2] | 0) != (c | 0)) {
                    v = g + 16 | 0;
                    b[((b[v >> 2] | 0) == (c | 0) ? v : g + 20 | 0) >> 2] = a;
                    if (!a) { break h; }
                  } else {
                    b[e >> 2] = a;
                    if (a | 0) { break; }
                    b[6978] = b[6978] & ~(1 << d);
                    break h;
                  } } while (0);
                  b[a + 24 >> 2] = g;
                  d = c + 16 | 0;
                  e = b[d >> 2] | 0;
                  if (e | 0) {
                    b[a + 16 >> 2] = e;
                    b[e + 24 >> 2] = a;
                  }
                  d = b[d + 4 >> 2] | 0;
                  if (!d) { break; }
                  b[a + 20 >> 2] = d;
                  b[d + 24 >> 2] = a;
                } } while (0);
                c = c + h | 0;
                f = h + i | 0;
              } else { f = i; }
              c = c + 4 | 0;
              b[c >> 2] = b[c >> 2] & -2;
              b[k + 4 >> 2] = f | 1;
              b[k + f >> 2] = f;
              c = f >>> 3;
              if (f >>> 0 < 256) {
                d = 27948 + (c << 1 << 2) | 0;
                a = b[6977] | 0;
                c = 1 << c;
                if (!(a & c)) {
                  b[6977] = a | c;
                  c = d;
                  a = d + 8 | 0;
                } else {
                  a = d + 8 | 0;
                  c = b[a >> 2] | 0;
                }
                b[a >> 2] = k;
                b[c + 12 >> 2] = k;
                b[k + 8 >> 2] = c;
                b[k + 12 >> 2] = d;
                break;
              }
              c = f >>> 8;
              do { if (!c) { e = 0; }else {
                if (f >>> 0 > 16777215) {
                  e = 31;
                  break;
                }
                u = (c + 1048320 | 0) >>> 16 & 8;
                v = c << u;
                t = (v + 520192 | 0) >>> 16 & 4;
                v = v << t;
                e = (v + 245760 | 0) >>> 16 & 2;
                e = 14 - (t | u | e) + (v << e >>> 15) | 0;
                e = f >>> (e + 7 | 0) & 1 | e << 1;
              } } while (0);
              c = 28212 + (e << 2) | 0;
              b[k + 28 >> 2] = e;
              a = k + 16 | 0;
              b[a + 4 >> 2] = 0;
              b[a >> 2] = 0;
              a = b[6978] | 0;
              d = 1 << e;
              if (!(a & d)) {
                b[6978] = a | d;
                b[c >> 2] = k;
                b[k + 24 >> 2] = c;
                b[k + 12 >> 2] = k;
                b[k + 8 >> 2] = k;
                break;
              }
              c = b[c >> 2] | 0;
              i: do { if ((b[c + 4 >> 2] & -8 | 0) != (f | 0)) {
                e = f << ((e | 0) == 31 ? 0 : 25 - (e >>> 1) | 0);
                while (1) {
                  d = c + 16 + (e >>> 31 << 2) | 0;
                  a = b[d >> 2] | 0;
                  if (!a) { break; }
                  if ((b[a + 4 >> 2] & -8 | 0) == (f | 0)) {
                    c = a;
                    break i;
                  } else {
                    e = e << 1;
                    c = a;
                  }
                }
                b[d >> 2] = k;
                b[k + 24 >> 2] = c;
                b[k + 12 >> 2] = k;
                b[k + 8 >> 2] = k;
                break g;
              } } while (0);
              u = c + 8 | 0;
              v = b[u >> 2] | 0;
              b[v + 12 >> 2] = k;
              b[u >> 2] = k;
              b[k + 8 >> 2] = v;
              b[k + 12 >> 2] = c;
              b[k + 24 >> 2] = 0;
            } } while (0);
            v = l + 8 | 0;
            T = w;
            return v | 0;
          }
          c = 28356;
          while (1) {
            a = b[c >> 2] | 0;
            if (a >>> 0 <= j >>> 0 ? (v = a + (b[c + 4 >> 2] | 0) | 0, v >>> 0 > j >>> 0) : 0) { break; }
            c = b[c + 8 >> 2] | 0;
          }
          f = v + -47 | 0;
          a = f + 8 | 0;
          a = f + ((a & 7 | 0) == 0 ? 0 : 0 - a & 7) | 0;
          f = j + 16 | 0;
          a = a >>> 0 < f >>> 0 ? j : a;
          c = a + 8 | 0;
          d = h + -40 | 0;
          t = g + 8 | 0;
          t = (t & 7 | 0) == 0 ? 0 : 0 - t & 7;
          u = g + t | 0;
          t = d - t | 0;
          b[6983] = u;
          b[6980] = t;
          b[u + 4 >> 2] = t | 1;
          b[g + d + 4 >> 2] = 40;
          b[6984] = b[7099];
          d = a + 4 | 0;
          b[d >> 2] = 27;
          b[c >> 2] = b[7089];
          b[c + 4 >> 2] = b[7090];
          b[c + 8 >> 2] = b[7091];
          b[c + 12 >> 2] = b[7092];
          b[7089] = g;
          b[7090] = h;
          b[7092] = 0;
          b[7091] = c;
          c = a + 24 | 0;
          do {
            u = c;
            c = c + 4 | 0;
            b[c >> 2] = 7;
          } while ((u + 8 | 0) >>> 0 < v >>> 0);
          if ((a | 0) != (j | 0)) {
            g = a - j | 0;
            b[d >> 2] = b[d >> 2] & -2;
            b[j + 4 >> 2] = g | 1;
            b[a >> 2] = g;
            c = g >>> 3;
            if (g >>> 0 < 256) {
              d = 27948 + (c << 1 << 2) | 0;
              a = b[6977] | 0;
              c = 1 << c;
              if (!(a & c)) {
                b[6977] = a | c;
                c = d;
                a = d + 8 | 0;
              } else {
                a = d + 8 | 0;
                c = b[a >> 2] | 0;
              }
              b[a >> 2] = j;
              b[c + 12 >> 2] = j;
              b[j + 8 >> 2] = c;
              b[j + 12 >> 2] = d;
              break;
            }
            c = g >>> 8;
            if (c) {
              if (g >>> 0 > 16777215) { e = 31; }else {
                u = (c + 1048320 | 0) >>> 16 & 8;
                v = c << u;
                t = (v + 520192 | 0) >>> 16 & 4;
                v = v << t;
                e = (v + 245760 | 0) >>> 16 & 2;
                e = 14 - (t | u | e) + (v << e >>> 15) | 0;
                e = g >>> (e + 7 | 0) & 1 | e << 1;
              }
            } else { e = 0; }
            d = 28212 + (e << 2) | 0;
            b[j + 28 >> 2] = e;
            b[j + 20 >> 2] = 0;
            b[f >> 2] = 0;
            c = b[6978] | 0;
            a = 1 << e;
            if (!(c & a)) {
              b[6978] = c | a;
              b[d >> 2] = j;
              b[j + 24 >> 2] = d;
              b[j + 12 >> 2] = j;
              b[j + 8 >> 2] = j;
              break;
            }
            c = b[d >> 2] | 0;
            j: do { if ((b[c + 4 >> 2] & -8 | 0) != (g | 0)) {
              e = g << ((e | 0) == 31 ? 0 : 25 - (e >>> 1) | 0);
              while (1) {
                d = c + 16 + (e >>> 31 << 2) | 0;
                a = b[d >> 2] | 0;
                if (!a) { break; }
                if ((b[a + 4 >> 2] & -8 | 0) == (g | 0)) {
                  c = a;
                  break j;
                } else {
                  e = e << 1;
                  c = a;
                }
              }
              b[d >> 2] = j;
              b[j + 24 >> 2] = c;
              b[j + 12 >> 2] = j;
              b[j + 8 >> 2] = j;
              break f;
            } } while (0);
            u = c + 8 | 0;
            v = b[u >> 2] | 0;
            b[v + 12 >> 2] = j;
            b[u >> 2] = j;
            b[j + 8 >> 2] = v;
            b[j + 12 >> 2] = c;
            b[j + 24 >> 2] = 0;
          }
        } else {
          v = b[6981] | 0;
          if ((v | 0) == 0 | g >>> 0 < v >>> 0) { b[6981] = g; }
          b[7089] = g;
          b[7090] = h;
          b[7092] = 0;
          b[6986] = b[7095];
          b[6985] = -1;
          b[6990] = 27948;
          b[6989] = 27948;
          b[6992] = 27956;
          b[6991] = 27956;
          b[6994] = 27964;
          b[6993] = 27964;
          b[6996] = 27972;
          b[6995] = 27972;
          b[6998] = 27980;
          b[6997] = 27980;
          b[7e3] = 27988;
          b[6999] = 27988;
          b[7002] = 27996;
          b[7001] = 27996;
          b[7004] = 28004;
          b[7003] = 28004;
          b[7006] = 28012;
          b[7005] = 28012;
          b[7008] = 28020;
          b[7007] = 28020;
          b[7010] = 28028;
          b[7009] = 28028;
          b[7012] = 28036;
          b[7011] = 28036;
          b[7014] = 28044;
          b[7013] = 28044;
          b[7016] = 28052;
          b[7015] = 28052;
          b[7018] = 28060;
          b[7017] = 28060;
          b[7020] = 28068;
          b[7019] = 28068;
          b[7022] = 28076;
          b[7021] = 28076;
          b[7024] = 28084;
          b[7023] = 28084;
          b[7026] = 28092;
          b[7025] = 28092;
          b[7028] = 28100;
          b[7027] = 28100;
          b[7030] = 28108;
          b[7029] = 28108;
          b[7032] = 28116;
          b[7031] = 28116;
          b[7034] = 28124;
          b[7033] = 28124;
          b[7036] = 28132;
          b[7035] = 28132;
          b[7038] = 28140;
          b[7037] = 28140;
          b[7040] = 28148;
          b[7039] = 28148;
          b[7042] = 28156;
          b[7041] = 28156;
          b[7044] = 28164;
          b[7043] = 28164;
          b[7046] = 28172;
          b[7045] = 28172;
          b[7048] = 28180;
          b[7047] = 28180;
          b[7050] = 28188;
          b[7049] = 28188;
          b[7052] = 28196;
          b[7051] = 28196;
          v = h + -40 | 0;
          t = g + 8 | 0;
          t = (t & 7 | 0) == 0 ? 0 : 0 - t & 7;
          u = g + t | 0;
          t = v - t | 0;
          b[6983] = u;
          b[6980] = t;
          b[u + 4 >> 2] = t | 1;
          b[g + v + 4 >> 2] = 40;
          b[6984] = b[7099];
        } } while (0);
        c = b[6980] | 0;
        if (c >>> 0 > m >>> 0) {
          t = c - m | 0;
          b[6980] = t;
          v = b[6983] | 0;
          u = v + m | 0;
          b[6983] = u;
          b[u + 4 >> 2] = t | 1;
          b[v + 4 >> 2] = m | 3;
          v = v + 8 | 0;
          T = w;
          return v | 0;
        }
      }
      v = zd() | 0;
      b[v >> 2] = 12;
      v = 0;
      T = w;
      return v | 0;
    }
    function Cd(a) {
      a = a | 0;
      var c = 0,
        d = 0,
        e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0;
      if (!a) { return; }
      d = a + -8 | 0;
      f = b[6981] | 0;
      a = b[a + -4 >> 2] | 0;
      c = a & -8;
      j = d + c | 0;
      do { if (!(a & 1)) {
        e = b[d >> 2] | 0;
        if (!(a & 3)) { return; }
        h = d + (0 - e) | 0;
        g = e + c | 0;
        if (h >>> 0 < f >>> 0) { return; }
        if ((b[6982] | 0) == (h | 0)) {
          a = j + 4 | 0;
          c = b[a >> 2] | 0;
          if ((c & 3 | 0) != 3) {
            i = h;
            c = g;
            break;
          }
          b[6979] = g;
          b[a >> 2] = c & -2;
          b[h + 4 >> 2] = g | 1;
          b[h + g >> 2] = g;
          return;
        }
        d = e >>> 3;
        if (e >>> 0 < 256) {
          a = b[h + 8 >> 2] | 0;
          c = b[h + 12 >> 2] | 0;
          if ((c | 0) == (a | 0)) {
            b[6977] = b[6977] & ~(1 << d);
            i = h;
            c = g;
            break;
          } else {
            b[a + 12 >> 2] = c;
            b[c + 8 >> 2] = a;
            i = h;
            c = g;
            break;
          }
        }
        f = b[h + 24 >> 2] | 0;
        a = b[h + 12 >> 2] | 0;
        do { if ((a | 0) == (h | 0)) {
          c = h + 16 | 0;
          d = c + 4 | 0;
          a = b[d >> 2] | 0;
          if (!a) {
            a = b[c >> 2] | 0;
            if (!a) {
              a = 0;
              break;
            }
          } else { c = d; }
          while (1) {
            e = a + 20 | 0;
            d = b[e >> 2] | 0;
            if (!d) {
              e = a + 16 | 0;
              d = b[e >> 2] | 0;
              if (!d) { break; }else {
                a = d;
                c = e;
              }
            } else {
              a = d;
              c = e;
            }
          }
          b[c >> 2] = 0;
        } else {
          i = b[h + 8 >> 2] | 0;
          b[i + 12 >> 2] = a;
          b[a + 8 >> 2] = i;
        } } while (0);
        if (f) {
          c = b[h + 28 >> 2] | 0;
          d = 28212 + (c << 2) | 0;
          if ((b[d >> 2] | 0) == (h | 0)) {
            b[d >> 2] = a;
            if (!a) {
              b[6978] = b[6978] & ~(1 << c);
              i = h;
              c = g;
              break;
            }
          } else {
            i = f + 16 | 0;
            b[((b[i >> 2] | 0) == (h | 0) ? i : f + 20 | 0) >> 2] = a;
            if (!a) {
              i = h;
              c = g;
              break;
            }
          }
          b[a + 24 >> 2] = f;
          c = h + 16 | 0;
          d = b[c >> 2] | 0;
          if (d | 0) {
            b[a + 16 >> 2] = d;
            b[d + 24 >> 2] = a;
          }
          c = b[c + 4 >> 2] | 0;
          if (c) {
            b[a + 20 >> 2] = c;
            b[c + 24 >> 2] = a;
            i = h;
            c = g;
          } else {
            i = h;
            c = g;
          }
        } else {
          i = h;
          c = g;
        }
      } else {
        i = d;
        h = d;
      } } while (0);
      if (h >>> 0 >= j >>> 0) { return; }
      a = j + 4 | 0;
      e = b[a >> 2] | 0;
      if (!(e & 1)) { return; }
      if (!(e & 2)) {
        if ((b[6983] | 0) == (j | 0)) {
          j = (b[6980] | 0) + c | 0;
          b[6980] = j;
          b[6983] = i;
          b[i + 4 >> 2] = j | 1;
          if ((i | 0) != (b[6982] | 0)) { return; }
          b[6982] = 0;
          b[6979] = 0;
          return;
        }
        if ((b[6982] | 0) == (j | 0)) {
          j = (b[6979] | 0) + c | 0;
          b[6979] = j;
          b[6982] = h;
          b[i + 4 >> 2] = j | 1;
          b[h + j >> 2] = j;
          return;
        }
        f = (e & -8) + c | 0;
        d = e >>> 3;
        do { if (e >>> 0 < 256) {
          c = b[j + 8 >> 2] | 0;
          a = b[j + 12 >> 2] | 0;
          if ((a | 0) == (c | 0)) {
            b[6977] = b[6977] & ~(1 << d);
            break;
          } else {
            b[c + 12 >> 2] = a;
            b[a + 8 >> 2] = c;
            break;
          }
        } else {
          g = b[j + 24 >> 2] | 0;
          a = b[j + 12 >> 2] | 0;
          do { if ((a | 0) == (j | 0)) {
            c = j + 16 | 0;
            d = c + 4 | 0;
            a = b[d >> 2] | 0;
            if (!a) {
              a = b[c >> 2] | 0;
              if (!a) {
                d = 0;
                break;
              }
            } else { c = d; }
            while (1) {
              e = a + 20 | 0;
              d = b[e >> 2] | 0;
              if (!d) {
                e = a + 16 | 0;
                d = b[e >> 2] | 0;
                if (!d) { break; }else {
                  a = d;
                  c = e;
                }
              } else {
                a = d;
                c = e;
              }
            }
            b[c >> 2] = 0;
            d = a;
          } else {
            d = b[j + 8 >> 2] | 0;
            b[d + 12 >> 2] = a;
            b[a + 8 >> 2] = d;
            d = a;
          } } while (0);
          if (g | 0) {
            a = b[j + 28 >> 2] | 0;
            c = 28212 + (a << 2) | 0;
            if ((b[c >> 2] | 0) == (j | 0)) {
              b[c >> 2] = d;
              if (!d) {
                b[6978] = b[6978] & ~(1 << a);
                break;
              }
            } else {
              e = g + 16 | 0;
              b[((b[e >> 2] | 0) == (j | 0) ? e : g + 20 | 0) >> 2] = d;
              if (!d) { break; }
            }
            b[d + 24 >> 2] = g;
            a = j + 16 | 0;
            c = b[a >> 2] | 0;
            if (c | 0) {
              b[d + 16 >> 2] = c;
              b[c + 24 >> 2] = d;
            }
            a = b[a + 4 >> 2] | 0;
            if (a | 0) {
              b[d + 20 >> 2] = a;
              b[a + 24 >> 2] = d;
            }
          }
        } } while (0);
        b[i + 4 >> 2] = f | 1;
        b[h + f >> 2] = f;
        if ((i | 0) == (b[6982] | 0)) {
          b[6979] = f;
          return;
        }
      } else {
        b[a >> 2] = e & -2;
        b[i + 4 >> 2] = c | 1;
        b[h + c >> 2] = c;
        f = c;
      }
      a = f >>> 3;
      if (f >>> 0 < 256) {
        d = 27948 + (a << 1 << 2) | 0;
        c = b[6977] | 0;
        a = 1 << a;
        if (!(c & a)) {
          b[6977] = c | a;
          a = d;
          c = d + 8 | 0;
        } else {
          c = d + 8 | 0;
          a = b[c >> 2] | 0;
        }
        b[c >> 2] = i;
        b[a + 12 >> 2] = i;
        b[i + 8 >> 2] = a;
        b[i + 12 >> 2] = d;
        return;
      }
      a = f >>> 8;
      if (a) {
        if (f >>> 0 > 16777215) { e = 31; }else {
          h = (a + 1048320 | 0) >>> 16 & 8;
          j = a << h;
          g = (j + 520192 | 0) >>> 16 & 4;
          j = j << g;
          e = (j + 245760 | 0) >>> 16 & 2;
          e = 14 - (g | h | e) + (j << e >>> 15) | 0;
          e = f >>> (e + 7 | 0) & 1 | e << 1;
        }
      } else { e = 0; }
      a = 28212 + (e << 2) | 0;
      b[i + 28 >> 2] = e;
      b[i + 20 >> 2] = 0;
      b[i + 16 >> 2] = 0;
      c = b[6978] | 0;
      d = 1 << e;
      a: do { if (!(c & d)) {
        b[6978] = c | d;
        b[a >> 2] = i;
        b[i + 24 >> 2] = a;
        b[i + 12 >> 2] = i;
        b[i + 8 >> 2] = i;
      } else {
        a = b[a >> 2] | 0;
        b: do { if ((b[a + 4 >> 2] & -8 | 0) != (f | 0)) {
          e = f << ((e | 0) == 31 ? 0 : 25 - (e >>> 1) | 0);
          while (1) {
            d = a + 16 + (e >>> 31 << 2) | 0;
            c = b[d >> 2] | 0;
            if (!c) { break; }
            if ((b[c + 4 >> 2] & -8 | 0) == (f | 0)) {
              a = c;
              break b;
            } else {
              e = e << 1;
              a = c;
            }
          }
          b[d >> 2] = i;
          b[i + 24 >> 2] = a;
          b[i + 12 >> 2] = i;
          b[i + 8 >> 2] = i;
          break a;
        } } while (0);
        h = a + 8 | 0;
        j = b[h >> 2] | 0;
        b[j + 12 >> 2] = i;
        b[h >> 2] = i;
        b[i + 8 >> 2] = j;
        b[i + 12 >> 2] = a;
        b[i + 24 >> 2] = 0;
      } } while (0);
      j = (b[6985] | 0) + -1 | 0;
      b[6985] = j;
      if (j | 0) { return; }
      a = 28364;
      while (1) {
        a = b[a >> 2] | 0;
        if (!a) { break; }else { a = a + 8 | 0; }
      }
      b[6985] = -1;
      return;
    }
    function Dd(a, c) {
      a = a | 0;
      c = c | 0;
      var d = 0;
      if (a) {
        d = B(c, a) | 0;
        if ((c | a) >>> 0 > 65535) { d = ((d >>> 0) / (a >>> 0) | 0 | 0) == (c | 0) ? d : -1; }
      } else { d = 0; }
      a = Bd(d) | 0;
      if (!a) { return a | 0; }
      if (!(b[a + -4 >> 2] & 3)) { return a | 0; }
      Vd(a | 0, 0, d | 0) | 0;
      return a | 0;
    }
    function Ed(a, b, c, d) {
      a = a | 0;
      b = b | 0;
      c = c | 0;
      d = d | 0;
      c = a + c >>> 0;
      return (G(b + d + (c >>> 0 < a >>> 0 | 0) >>> 0 | 0), c | 0) | 0;
    }
    function Fd(a, b, c, d) {
      a = a | 0;
      b = b | 0;
      c = c | 0;
      d = d | 0;
      d = b - d - (c >>> 0 > a >>> 0 | 0) >>> 0;
      return (G(d | 0), a - c >>> 0 | 0) | 0;
    }
    function Gd(a) {
      a = a | 0;
      return (a ? 31 - (E(a ^ a - 1) | 0) | 0 : 32) | 0;
    }
    function Hd(a, c, d, e, f) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      f = f | 0;
      var g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0,
        l = 0,
        m = 0,
        n = 0,
        o = 0,
        p = 0;
      l = a;
      j = c;
      k = j;
      h = d;
      n = e;
      i = n;
      if (!k) {
        g = (f | 0) != 0;
        if (!i) {
          if (g) {
            b[f >> 2] = (l >>> 0) % (h >>> 0);
            b[f + 4 >> 2] = 0;
          }
          n = 0;
          f = (l >>> 0) / (h >>> 0) >>> 0;
          return (G(n | 0), f) | 0;
        } else {
          if (!g) {
            n = 0;
            f = 0;
            return (G(n | 0), f) | 0;
          }
          b[f >> 2] = a | 0;
          b[f + 4 >> 2] = c & 0;
          n = 0;
          f = 0;
          return (G(n | 0), f) | 0;
        }
      }
      g = (i | 0) == 0;
      do { if (h) {
        if (!g) {
          g = (E(i | 0) | 0) - (E(k | 0) | 0) | 0;
          if (g >>> 0 <= 31) {
            m = g + 1 | 0;
            i = 31 - g | 0;
            c = g - 31 >> 31;
            h = m;
            a = l >>> (m >>> 0) & c | k << i;
            c = k >>> (m >>> 0) & c;
            g = 0;
            i = l << i;
            break;
          }
          if (!f) {
            n = 0;
            f = 0;
            return (G(n | 0), f) | 0;
          }
          b[f >> 2] = a | 0;
          b[f + 4 >> 2] = j | c & 0;
          n = 0;
          f = 0;
          return (G(n | 0), f) | 0;
        }
        g = h - 1 | 0;
        if (g & h | 0) {
          i = (E(h | 0) | 0) + 33 - (E(k | 0) | 0) | 0;
          p = 64 - i | 0;
          m = 32 - i | 0;
          j = m >> 31;
          o = i - 32 | 0;
          c = o >> 31;
          h = i;
          a = m - 1 >> 31 & k >>> (o >>> 0) | (k << m | l >>> (i >>> 0)) & c;
          c = c & k >>> (i >>> 0);
          g = l << p & j;
          i = (k << p | l >>> (o >>> 0)) & j | l << m & i - 33 >> 31;
          break;
        }
        if (f | 0) {
          b[f >> 2] = g & l;
          b[f + 4 >> 2] = 0;
        }
        if ((h | 0) == 1) {
          o = j | c & 0;
          p = a | 0 | 0;
          return (G(o | 0), p) | 0;
        } else {
          p = Gd(h | 0) | 0;
          o = k >>> (p >>> 0) | 0;
          p = k << 32 - p | l >>> (p >>> 0) | 0;
          return (G(o | 0), p) | 0;
        }
      } else {
        if (g) {
          if (f | 0) {
            b[f >> 2] = (k >>> 0) % (h >>> 0);
            b[f + 4 >> 2] = 0;
          }
          o = 0;
          p = (k >>> 0) / (h >>> 0) >>> 0;
          return (G(o | 0), p) | 0;
        }
        if (!l) {
          if (f | 0) {
            b[f >> 2] = 0;
            b[f + 4 >> 2] = (k >>> 0) % (i >>> 0);
          }
          o = 0;
          p = (k >>> 0) / (i >>> 0) >>> 0;
          return (G(o | 0), p) | 0;
        }
        g = i - 1 | 0;
        if (!(g & i)) {
          if (f | 0) {
            b[f >> 2] = a | 0;
            b[f + 4 >> 2] = g & k | c & 0;
          }
          o = 0;
          p = k >>> ((Gd(i | 0) | 0) >>> 0);
          return (G(o | 0), p) | 0;
        }
        g = (E(i | 0) | 0) - (E(k | 0) | 0) | 0;
        if (g >>> 0 <= 30) {
          c = g + 1 | 0;
          i = 31 - g | 0;
          h = c;
          a = k << i | l >>> (c >>> 0);
          c = k >>> (c >>> 0);
          g = 0;
          i = l << i;
          break;
        }
        if (!f) {
          o = 0;
          p = 0;
          return (G(o | 0), p) | 0;
        }
        b[f >> 2] = a | 0;
        b[f + 4 >> 2] = j | c & 0;
        o = 0;
        p = 0;
        return (G(o | 0), p) | 0;
      } } while (0);
      if (!h) {
        k = i;
        j = 0;
        i = 0;
      } else {
        m = d | 0 | 0;
        l = n | e & 0;
        k = Ed(m | 0, l | 0, -1, -1) | 0;
        d = H() | 0;
        j = i;
        i = 0;
        do {
          e = j;
          j = g >>> 31 | j << 1;
          g = i | g << 1;
          e = a << 1 | e >>> 31 | 0;
          n = a >>> 31 | c << 1 | 0;
          Fd(k | 0, d | 0, e | 0, n | 0) | 0;
          p = H() | 0;
          o = p >> 31 | ((p | 0) < 0 ? -1 : 0) << 1;
          i = o & 1;
          a = Fd(e | 0, n | 0, o & m | 0, (((p | 0) < 0 ? -1 : 0) >> 31 | ((p | 0) < 0 ? -1 : 0) << 1) & l | 0) | 0;
          c = H() | 0;
          h = h - 1 | 0;
        } while ((h | 0) != 0);
        k = j;
        j = 0;
      }
      h = 0;
      if (f | 0) {
        b[f >> 2] = a;
        b[f + 4 >> 2] = c;
      }
      o = (g | 0) >>> 31 | (k | h) << 1 | (h << 1 | g >>> 31) & 0 | j;
      p = (g << 1 | 0 >>> 31) & -2 | i;
      return (G(o | 0), p) | 0;
    }
    function Id(a, b, c, d) {
      a = a | 0;
      b = b | 0;
      c = c | 0;
      d = d | 0;
      var e = 0,
        f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0;
      j = b >> 31 | ((b | 0) < 0 ? -1 : 0) << 1;
      i = ((b | 0) < 0 ? -1 : 0) >> 31 | ((b | 0) < 0 ? -1 : 0) << 1;
      f = d >> 31 | ((d | 0) < 0 ? -1 : 0) << 1;
      e = ((d | 0) < 0 ? -1 : 0) >> 31 | ((d | 0) < 0 ? -1 : 0) << 1;
      h = Fd(j ^ a | 0, i ^ b | 0, j | 0, i | 0) | 0;
      g = H() | 0;
      a = f ^ j;
      b = e ^ i;
      return Fd((Hd(h, g, Fd(f ^ c | 0, e ^ d | 0, f | 0, e | 0) | 0, H() | 0, 0) | 0) ^ a | 0, (H() | 0) ^ b | 0, a | 0, b | 0) | 0;
    }
    function Jd(a, b) {
      a = a | 0;
      b = b | 0;
      var c = 0,
        d = 0,
        e = 0,
        f = 0;
      f = a & 65535;
      e = b & 65535;
      c = B(e, f) | 0;
      d = a >>> 16;
      a = (c >>> 16) + (B(e, d) | 0) | 0;
      e = b >>> 16;
      b = B(e, f) | 0;
      return (G((a >>> 16) + (B(e, d) | 0) + (((a & 65535) + b | 0) >>> 16) | 0), a + b << 16 | c & 65535 | 0) | 0;
    }
    function Kd(a, b, c, d) {
      a = a | 0;
      b = b | 0;
      c = c | 0;
      d = d | 0;
      var e = 0,
        f = 0;
      e = a;
      f = c;
      c = Jd(e, f) | 0;
      a = H() | 0;
      return (G((B(b, f) | 0) + (B(d, e) | 0) + a | a & 0 | 0), c | 0 | 0) | 0;
    }
    function Ld(a, c, d, e) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      var f = 0,
        g = 0,
        h = 0,
        i = 0,
        j = 0,
        k = 0;
      f = T;
      T = T + 16 | 0;
      i = f | 0;
      h = c >> 31 | ((c | 0) < 0 ? -1 : 0) << 1;
      g = ((c | 0) < 0 ? -1 : 0) >> 31 | ((c | 0) < 0 ? -1 : 0) << 1;
      k = e >> 31 | ((e | 0) < 0 ? -1 : 0) << 1;
      j = ((e | 0) < 0 ? -1 : 0) >> 31 | ((e | 0) < 0 ? -1 : 0) << 1;
      a = Fd(h ^ a | 0, g ^ c | 0, h | 0, g | 0) | 0;
      c = H() | 0;
      Hd(a, c, Fd(k ^ d | 0, j ^ e | 0, k | 0, j | 0) | 0, H() | 0, i) | 0;
      e = Fd(b[i >> 2] ^ h | 0, b[i + 4 >> 2] ^ g | 0, h | 0, g | 0) | 0;
      d = H() | 0;
      T = f;
      return (G(d | 0), e) | 0;
    }
    function Md(a, c, d, e) {
      a = a | 0;
      c = c | 0;
      d = d | 0;
      e = e | 0;
      var f = 0,
        g = 0;
      g = T;
      T = T + 16 | 0;
      f = g | 0;
      Hd(a, c, d, e, f) | 0;
      T = g;
      return (G(b[f + 4 >> 2] | 0), b[f >> 2] | 0) | 0;
    }
    function Nd(a, b, c) {
      a = a | 0;
      b = b | 0;
      c = c | 0;
      if ((c | 0) < 32) {
        G(b >> c | 0);
        return a >>> c | (b & (1 << c) - 1) << 32 - c;
      }
      G(((b | 0) < 0 ? -1 : 0) | 0);
      return b >> c - 32 | 0;
    }
    function Od(a, b, c) {
      a = a | 0;
      b = b | 0;
      c = c | 0;
      if ((c | 0) < 32) {
        G(b >>> c | 0);
        return a >>> c | (b & (1 << c) - 1) << 32 - c;
      }
      G(0);
      return b >>> c - 32 | 0;
    }
    function Pd(a, b, c) {
      a = a | 0;
      b = b | 0;
      c = c | 0;
      if ((c | 0) < 32) {
        G(b << c | (a & (1 << c) - 1 << 32 - c) >>> 32 - c | 0);
        return a << c;
      }
      G(a << c - 32 | 0);
      return 0;
    }
    function Qd(a, b, c) {
      a = a | 0;
      b = b | 0;
      c = c | 0;
      b = E(b) | 0;
      if ((b | 0) == 32) { b = b + (E(a) | 0) | 0; }
      G(0);
      return b | 0;
    }
    function Rd(a, b) {
      a = +a;
      b = +b;
      if (a != a) { return +b; }
      if (b != b) { return +a; }
      return +D(+a, +b);
    }
    function Sd(a, b) {
      a = +a;
      b = +b;
      if (a != a) { return +b; }
      if (b != b) { return +a; }
      return +C(+a, +b);
    }
    function Td(a) {
      a = +a;
      return a >= 0.0 ? +p(a + .5) : +A(a - .5);
    }
    function Ud(c, d, e) {
      c = c | 0;
      d = d | 0;
      e = e | 0;
      var f = 0,
        g = 0,
        h = 0;
      if ((e | 0) >= 8192) {
        L(c | 0, d | 0, e | 0) | 0;
        return c | 0;
      }
      h = c | 0;
      g = c + e | 0;
      if ((c & 3) == (d & 3)) {
        while (c & 3) {
          if (!e) { return h | 0; }
          a[c >> 0] = a[d >> 0] | 0;
          c = c + 1 | 0;
          d = d + 1 | 0;
          e = e - 1 | 0;
        }
        e = g & -4 | 0;
        f = e - 64 | 0;
        while ((c | 0) <= (f | 0)) {
          b[c >> 2] = b[d >> 2];
          b[c + 4 >> 2] = b[d + 4 >> 2];
          b[c + 8 >> 2] = b[d + 8 >> 2];
          b[c + 12 >> 2] = b[d + 12 >> 2];
          b[c + 16 >> 2] = b[d + 16 >> 2];
          b[c + 20 >> 2] = b[d + 20 >> 2];
          b[c + 24 >> 2] = b[d + 24 >> 2];
          b[c + 28 >> 2] = b[d + 28 >> 2];
          b[c + 32 >> 2] = b[d + 32 >> 2];
          b[c + 36 >> 2] = b[d + 36 >> 2];
          b[c + 40 >> 2] = b[d + 40 >> 2];
          b[c + 44 >> 2] = b[d + 44 >> 2];
          b[c + 48 >> 2] = b[d + 48 >> 2];
          b[c + 52 >> 2] = b[d + 52 >> 2];
          b[c + 56 >> 2] = b[d + 56 >> 2];
          b[c + 60 >> 2] = b[d + 60 >> 2];
          c = c + 64 | 0;
          d = d + 64 | 0;
        }
        while ((c | 0) < (e | 0)) {
          b[c >> 2] = b[d >> 2];
          c = c + 4 | 0;
          d = d + 4 | 0;
        }
      } else {
        e = g - 4 | 0;
        while ((c | 0) < (e | 0)) {
          a[c >> 0] = a[d >> 0] | 0;
          a[c + 1 >> 0] = a[d + 1 >> 0] | 0;
          a[c + 2 >> 0] = a[d + 2 >> 0] | 0;
          a[c + 3 >> 0] = a[d + 3 >> 0] | 0;
          c = c + 4 | 0;
          d = d + 4 | 0;
        }
      }
      while ((c | 0) < (g | 0)) {
        a[c >> 0] = a[d >> 0] | 0;
        c = c + 1 | 0;
        d = d + 1 | 0;
      }
      return h | 0;
    }
    function Vd(c, d, e) {
      c = c | 0;
      d = d | 0;
      e = e | 0;
      var f = 0,
        g = 0,
        h = 0,
        i = 0;
      h = c + e | 0;
      d = d & 255;
      if ((e | 0) >= 67) {
        while (c & 3) {
          a[c >> 0] = d;
          c = c + 1 | 0;
        }
        f = h & -4 | 0;
        i = d | d << 8 | d << 16 | d << 24;
        g = f - 64 | 0;
        while ((c | 0) <= (g | 0)) {
          b[c >> 2] = i;
          b[c + 4 >> 2] = i;
          b[c + 8 >> 2] = i;
          b[c + 12 >> 2] = i;
          b[c + 16 >> 2] = i;
          b[c + 20 >> 2] = i;
          b[c + 24 >> 2] = i;
          b[c + 28 >> 2] = i;
          b[c + 32 >> 2] = i;
          b[c + 36 >> 2] = i;
          b[c + 40 >> 2] = i;
          b[c + 44 >> 2] = i;
          b[c + 48 >> 2] = i;
          b[c + 52 >> 2] = i;
          b[c + 56 >> 2] = i;
          b[c + 60 >> 2] = i;
          c = c + 64 | 0;
        }
        while ((c | 0) < (f | 0)) {
          b[c >> 2] = i;
          c = c + 4 | 0;
        }
      }
      while ((c | 0) < (h | 0)) {
        a[c >> 0] = d;
        c = c + 1 | 0;
      }
      return h - e | 0;
    }
    function Wd(a) {
      a = +a;
      return a >= 0.0 ? +p(a + .5) : +A(a - .5);
    }
    function Xd(a) {
      a = a | 0;
      var c = 0,
        d = 0,
        e = 0;
      e = K() | 0;
      d = b[g >> 2] | 0;
      c = d + a | 0;
      if ((a | 0) > 0 & (c | 0) < (d | 0) | (c | 0) < 0) {
        N(c | 0) | 0;
        J(12);
        return -1;
      }
      if ((c | 0) > (e | 0)) { if (!(M(c | 0) | 0)) {
        J(12);
        return -1;
      } }
      b[g >> 2] = c;
      return d | 0;
    }

    // EMSCRIPTEN_END_FUNCS
    return {
      ___divdi3: Id,
      ___muldi3: Kd,
      ___remdi3: Ld,
      ___uremdi3: Md,
      _areNeighborCells: gb,
      _bitshift64Ashr: Nd,
      _bitshift64Lshr: Od,
      _bitshift64Shl: Pd,
      _calloc: Dd,
      _cellAreaKm2: vc,
      _cellAreaM2: wc,
      _cellAreaRads2: uc,
      _cellToBoundary: Yb,
      _cellToCenterChild: Ib,
      _cellToChildPos: bc,
      _cellToChildren: Gb,
      _cellToChildrenSize: Eb,
      _cellToLatLng: Xb,
      _cellToLocalIj: Hc,
      _cellToParent: Db,
      _cellToVertex: od,
      _cellToVertexes: pd,
      _cellsToDirectedEdge: hb,
      _cellsToLinkedMultiPolygon: la,
      _childPosToCell: cc,
      _compactCells: Jb,
      _destroyLinkedMultiPolygon: Cc,
      _directedEdgeToBoundary: nb,
      _directedEdgeToCells: lb,
      _edgeLengthKm: yc,
      _edgeLengthM: zc,
      _edgeLengthRads: xc,
      _emscripten_replace_memory: W,
      _free: Cd,
      _getBaseCellNumber: Ab,
      _getDirectedEdgeDestination: jb,
      _getDirectedEdgeOrigin: ib,
      _getHexagonAreaAvgKm2: oc,
      _getHexagonAreaAvgM2: pc,
      _getHexagonEdgeLengthAvgKm: qc,
      _getHexagonEdgeLengthAvgM: rc,
      _getIcosahedronFaces: _b,
      _getNumCells: sc,
      _getPentagons: ac,
      _getRes0Cells: wa,
      _getResolution: zb,
      _greatCircleDistanceKm: kc,
      _greatCircleDistanceM: lc,
      _greatCircleDistanceRads: jc,
      _gridDisk: aa,
      _gridDiskDistances: ba,
      _gridDistance: Jc,
      _gridPathCells: Lc,
      _gridPathCellsSize: Kc,
      _gridRingUnsafe: ga,
      _i64Add: Ed,
      _i64Subtract: Fd,
      _isPentagon: Fb,
      _isResClassIII: Mb,
      _isValidCell: Bb,
      _isValidDirectedEdge: kb,
      _isValidVertex: rd,
      _latLngToCell: Ub,
      _llvm_ctlz_i64: Qd,
      _llvm_maxnum_f64: Rd,
      _llvm_minnum_f64: Sd,
      _llvm_round_f64: Td,
      _localIjToCell: Ic,
      _malloc: Bd,
      _maxFaceCount: Zb,
      _maxGridDiskSize: $,
      _maxPolygonToCellsSize: ha,
      _maxPolygonToCellsSizeExperimental: Sc,
      _memcpy: Ud,
      _memset: Vd,
      _originToDirectedEdges: mb,
      _pentagonCount: $b,
      _polygonToCells: ja,
      _polygonToCellsExperimental: Rc,
      _readInt64AsDoubleFromPointer: gd,
      _res0CellCount: va,
      _round: Wd,
      _sbrk: Xd,
      _sizeOfCellBoundary: bd,
      _sizeOfCoordIJ: fd,
      _sizeOfGeoLoop: cd,
      _sizeOfGeoPolygon: dd,
      _sizeOfH3Index: $c,
      _sizeOfLatLng: ad,
      _sizeOfLinkedGeoPolygon: ed,
      _uncompactCells: Kb,
      _uncompactCellsSize: Lb,
      _vertexToLatLng: qd,
      establishStackSpace: _,
      stackAlloc: X,
      stackRestore: Z,
      stackSave: Y
    };
  }

  // EMSCRIPTEN_END_ASM
  )(asmGlobalArg, asmLibraryArg, buffer);
  var ___divdi3 = Module["___divdi3"] = asm["___divdi3"];
  var ___muldi3 = Module["___muldi3"] = asm["___muldi3"];
  var ___remdi3 = Module["___remdi3"] = asm["___remdi3"];
  var ___uremdi3 = Module["___uremdi3"] = asm["___uremdi3"];
  var _areNeighborCells = Module["_areNeighborCells"] = asm["_areNeighborCells"];
  var _bitshift64Ashr = Module["_bitshift64Ashr"] = asm["_bitshift64Ashr"];
  var _bitshift64Lshr = Module["_bitshift64Lshr"] = asm["_bitshift64Lshr"];
  var _bitshift64Shl = Module["_bitshift64Shl"] = asm["_bitshift64Shl"];
  var _calloc = Module["_calloc"] = asm["_calloc"];
  var _cellAreaKm2 = Module["_cellAreaKm2"] = asm["_cellAreaKm2"];
  var _cellAreaM2 = Module["_cellAreaM2"] = asm["_cellAreaM2"];
  var _cellAreaRads2 = Module["_cellAreaRads2"] = asm["_cellAreaRads2"];
  var _cellToBoundary = Module["_cellToBoundary"] = asm["_cellToBoundary"];
  var _cellToCenterChild = Module["_cellToCenterChild"] = asm["_cellToCenterChild"];
  var _cellToChildPos = Module["_cellToChildPos"] = asm["_cellToChildPos"];
  var _cellToChildren = Module["_cellToChildren"] = asm["_cellToChildren"];
  var _cellToChildrenSize = Module["_cellToChildrenSize"] = asm["_cellToChildrenSize"];
  var _cellToLatLng = Module["_cellToLatLng"] = asm["_cellToLatLng"];
  var _cellToLocalIj = Module["_cellToLocalIj"] = asm["_cellToLocalIj"];
  var _cellToParent = Module["_cellToParent"] = asm["_cellToParent"];
  var _cellToVertex = Module["_cellToVertex"] = asm["_cellToVertex"];
  var _cellToVertexes = Module["_cellToVertexes"] = asm["_cellToVertexes"];
  var _cellsToDirectedEdge = Module["_cellsToDirectedEdge"] = asm["_cellsToDirectedEdge"];
  var _cellsToLinkedMultiPolygon = Module["_cellsToLinkedMultiPolygon"] = asm["_cellsToLinkedMultiPolygon"];
  var _childPosToCell = Module["_childPosToCell"] = asm["_childPosToCell"];
  var _compactCells = Module["_compactCells"] = asm["_compactCells"];
  var _destroyLinkedMultiPolygon = Module["_destroyLinkedMultiPolygon"] = asm["_destroyLinkedMultiPolygon"];
  var _directedEdgeToBoundary = Module["_directedEdgeToBoundary"] = asm["_directedEdgeToBoundary"];
  var _directedEdgeToCells = Module["_directedEdgeToCells"] = asm["_directedEdgeToCells"];
  var _edgeLengthKm = Module["_edgeLengthKm"] = asm["_edgeLengthKm"];
  var _edgeLengthM = Module["_edgeLengthM"] = asm["_edgeLengthM"];
  var _edgeLengthRads = Module["_edgeLengthRads"] = asm["_edgeLengthRads"];
  var _emscripten_replace_memory = Module["_emscripten_replace_memory"] = asm["_emscripten_replace_memory"];
  var _free = Module["_free"] = asm["_free"];
  var _getBaseCellNumber = Module["_getBaseCellNumber"] = asm["_getBaseCellNumber"];
  var _getDirectedEdgeDestination = Module["_getDirectedEdgeDestination"] = asm["_getDirectedEdgeDestination"];
  var _getDirectedEdgeOrigin = Module["_getDirectedEdgeOrigin"] = asm["_getDirectedEdgeOrigin"];
  var _getHexagonAreaAvgKm2 = Module["_getHexagonAreaAvgKm2"] = asm["_getHexagonAreaAvgKm2"];
  var _getHexagonAreaAvgM2 = Module["_getHexagonAreaAvgM2"] = asm["_getHexagonAreaAvgM2"];
  var _getHexagonEdgeLengthAvgKm = Module["_getHexagonEdgeLengthAvgKm"] = asm["_getHexagonEdgeLengthAvgKm"];
  var _getHexagonEdgeLengthAvgM = Module["_getHexagonEdgeLengthAvgM"] = asm["_getHexagonEdgeLengthAvgM"];
  var _getIcosahedronFaces = Module["_getIcosahedronFaces"] = asm["_getIcosahedronFaces"];
  var _getNumCells = Module["_getNumCells"] = asm["_getNumCells"];
  var _getPentagons = Module["_getPentagons"] = asm["_getPentagons"];
  var _getRes0Cells = Module["_getRes0Cells"] = asm["_getRes0Cells"];
  var _getResolution = Module["_getResolution"] = asm["_getResolution"];
  var _greatCircleDistanceKm = Module["_greatCircleDistanceKm"] = asm["_greatCircleDistanceKm"];
  var _greatCircleDistanceM = Module["_greatCircleDistanceM"] = asm["_greatCircleDistanceM"];
  var _greatCircleDistanceRads = Module["_greatCircleDistanceRads"] = asm["_greatCircleDistanceRads"];
  var _gridDisk = Module["_gridDisk"] = asm["_gridDisk"];
  var _gridDiskDistances = Module["_gridDiskDistances"] = asm["_gridDiskDistances"];
  var _gridDistance = Module["_gridDistance"] = asm["_gridDistance"];
  var _gridPathCells = Module["_gridPathCells"] = asm["_gridPathCells"];
  var _gridPathCellsSize = Module["_gridPathCellsSize"] = asm["_gridPathCellsSize"];
  var _gridRingUnsafe = Module["_gridRingUnsafe"] = asm["_gridRingUnsafe"];
  var _i64Add = Module["_i64Add"] = asm["_i64Add"];
  var _i64Subtract = Module["_i64Subtract"] = asm["_i64Subtract"];
  var _isPentagon = Module["_isPentagon"] = asm["_isPentagon"];
  var _isResClassIII = Module["_isResClassIII"] = asm["_isResClassIII"];
  var _isValidCell = Module["_isValidCell"] = asm["_isValidCell"];
  var _isValidDirectedEdge = Module["_isValidDirectedEdge"] = asm["_isValidDirectedEdge"];
  var _isValidVertex = Module["_isValidVertex"] = asm["_isValidVertex"];
  var _latLngToCell = Module["_latLngToCell"] = asm["_latLngToCell"];
  var _llvm_ctlz_i64 = Module["_llvm_ctlz_i64"] = asm["_llvm_ctlz_i64"];
  var _llvm_maxnum_f64 = Module["_llvm_maxnum_f64"] = asm["_llvm_maxnum_f64"];
  var _llvm_minnum_f64 = Module["_llvm_minnum_f64"] = asm["_llvm_minnum_f64"];
  var _llvm_round_f64 = Module["_llvm_round_f64"] = asm["_llvm_round_f64"];
  var _localIjToCell = Module["_localIjToCell"] = asm["_localIjToCell"];
  var _malloc = Module["_malloc"] = asm["_malloc"];
  var _maxFaceCount = Module["_maxFaceCount"] = asm["_maxFaceCount"];
  var _maxGridDiskSize = Module["_maxGridDiskSize"] = asm["_maxGridDiskSize"];
  var _maxPolygonToCellsSize = Module["_maxPolygonToCellsSize"] = asm["_maxPolygonToCellsSize"];
  var _maxPolygonToCellsSizeExperimental = Module["_maxPolygonToCellsSizeExperimental"] = asm["_maxPolygonToCellsSizeExperimental"];
  var _memcpy = Module["_memcpy"] = asm["_memcpy"];
  var _memset = Module["_memset"] = asm["_memset"];
  var _originToDirectedEdges = Module["_originToDirectedEdges"] = asm["_originToDirectedEdges"];
  var _pentagonCount = Module["_pentagonCount"] = asm["_pentagonCount"];
  var _polygonToCells = Module["_polygonToCells"] = asm["_polygonToCells"];
  var _polygonToCellsExperimental = Module["_polygonToCellsExperimental"] = asm["_polygonToCellsExperimental"];
  var _readInt64AsDoubleFromPointer = Module["_readInt64AsDoubleFromPointer"] = asm["_readInt64AsDoubleFromPointer"];
  var _res0CellCount = Module["_res0CellCount"] = asm["_res0CellCount"];
  var _round = Module["_round"] = asm["_round"];
  var _sbrk = Module["_sbrk"] = asm["_sbrk"];
  var _sizeOfCellBoundary = Module["_sizeOfCellBoundary"] = asm["_sizeOfCellBoundary"];
  var _sizeOfCoordIJ = Module["_sizeOfCoordIJ"] = asm["_sizeOfCoordIJ"];
  var _sizeOfGeoLoop = Module["_sizeOfGeoLoop"] = asm["_sizeOfGeoLoop"];
  var _sizeOfGeoPolygon = Module["_sizeOfGeoPolygon"] = asm["_sizeOfGeoPolygon"];
  var _sizeOfH3Index = Module["_sizeOfH3Index"] = asm["_sizeOfH3Index"];
  var _sizeOfLatLng = Module["_sizeOfLatLng"] = asm["_sizeOfLatLng"];
  var _sizeOfLinkedGeoPolygon = Module["_sizeOfLinkedGeoPolygon"] = asm["_sizeOfLinkedGeoPolygon"];
  var _uncompactCells = Module["_uncompactCells"] = asm["_uncompactCells"];
  var _uncompactCellsSize = Module["_uncompactCellsSize"] = asm["_uncompactCellsSize"];
  var _vertexToLatLng = Module["_vertexToLatLng"] = asm["_vertexToLatLng"];
  var establishStackSpace = Module["establishStackSpace"] = asm["establishStackSpace"];
  var stackAlloc = Module["stackAlloc"] = asm["stackAlloc"];
  var stackRestore = Module["stackRestore"] = asm["stackRestore"];
  var stackSave = Module["stackSave"] = asm["stackSave"];
  Module["asm"] = asm;
  Module["cwrap"] = cwrap;
  Module["setValue"] = setValue;
  Module["getValue"] = getValue;
  if (memoryInitializer) {
    if (!isDataURI(memoryInitializer)) {
      memoryInitializer = locateFile(memoryInitializer);
    }
    if (ENVIRONMENT_IS_NODE || ENVIRONMENT_IS_SHELL) {
      var data = readBinary(memoryInitializer);
      HEAPU8.set(data, GLOBAL_BASE);
    } else {
      addRunDependency("memory initializer");
      var applyMemoryInitializer = function (data) {
        if (data.byteLength) { data = new Uint8Array(data); }
        HEAPU8.set(data, GLOBAL_BASE);
        if (Module["memoryInitializerRequest"]) { delete Module["memoryInitializerRequest"].response; }
        removeRunDependency("memory initializer");
      };
      var doBrowserLoad = function () {
        readAsync(memoryInitializer, applyMemoryInitializer, function () {
          throw "could not load memory initializer " + memoryInitializer;
        });
      };
      var memoryInitializerBytes = tryParseAsDataURI(memoryInitializer);
      if (memoryInitializerBytes) {
        applyMemoryInitializer(memoryInitializerBytes.buffer);
      } else if (Module["memoryInitializerRequest"]) {
        var useRequest = function () {
          var request = Module["memoryInitializerRequest"];
          var response = request.response;
          if (request.status !== 200 && request.status !== 0) {
            var data = tryParseAsDataURI(Module["memoryInitializerRequestURL"]);
            if (data) {
              response = data.buffer;
            } else {
              console.warn("a problem seems to have happened with Module.memoryInitializerRequest, status: " + request.status + ", retrying " + memoryInitializer);
              doBrowserLoad();
              return;
            }
          }
          applyMemoryInitializer(response);
        };
        if (Module["memoryInitializerRequest"].response) {
          setTimeout(useRequest, 0);
        } else {
          Module["memoryInitializerRequest"].addEventListener("load", useRequest);
        }
      } else {
        doBrowserLoad();
      }
    }
  }
  var calledRun;
  dependenciesFulfilled = function runCaller() {
    if (!calledRun) { run(); }
    if (!calledRun) { dependenciesFulfilled = runCaller; }
  };
  function run(args) {
    args = args || arguments_;
    if (runDependencies > 0) {
      return;
    }
    preRun();
    if (runDependencies > 0) { return; }
    function doRun() {
      if (calledRun) { return; }
      calledRun = true;
      if (ABORT) { return; }
      initRuntime();
      preMain();
      if (Module["onRuntimeInitialized"]) { Module["onRuntimeInitialized"](); }
      postRun();
    }
    if (Module["setStatus"]) {
      Module["setStatus"]("Running...");
      setTimeout(function () {
        setTimeout(function () {
          Module["setStatus"]("");
        }, 1);
        doRun();
      }, 1);
    } else {
      doRun();
    }
  }
  Module["run"] = run;
  function abort(what) {
    if (Module["onAbort"]) {
      Module["onAbort"](what);
    }
    what += "";
    out(what);
    err(what);
    ABORT = true;
    throw "abort(" + what + "). Build with -s ASSERTIONS=1 for more info.";
  }
  Module["abort"] = abort;
  if (Module["preInit"]) {
    if (typeof Module["preInit"] == "function") { Module["preInit"] = [Module["preInit"]]; }
    while (Module["preInit"].length > 0) {
      Module["preInit"].pop()();
    }
  }
  run();
  return libh3;
}(typeof libh3 === 'object' ? libh3 : {});

/*
 * Copyright 2018-2019, 2022 Uber Technologies, Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *         http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Define the C bindings for the h3 library

// Add some aliases to make the function definitions more intelligible
var NUMBER = 'number';
var H3_ERROR = NUMBER;
var BOOLEAN = NUMBER;
var H3_LOWER = NUMBER;
var H3_UPPER = NUMBER;
var RESOLUTION = NUMBER;
var POINTER = NUMBER;

// Define the bindings to functions in the C lib. Functions are defined as
// [name, return type, [arg types]]. You must run `npm run build-emscripten`
// before new functions added here will be available.
/** @type {([string, string] | [string, string | null, string[]])[]} */
var BINDINGS = [
// The size functions are inserted via build/sizes.h
['sizeOfH3Index', NUMBER], ['sizeOfLatLng', NUMBER], ['sizeOfCellBoundary', NUMBER], ['sizeOfGeoLoop', NUMBER], ['sizeOfGeoPolygon', NUMBER], ['sizeOfLinkedGeoPolygon', NUMBER], ['sizeOfCoordIJ', NUMBER], ['readInt64AsDoubleFromPointer', NUMBER],
// The remaining functions are defined in the core lib in h3Api.h
['isValidCell', BOOLEAN, [H3_LOWER, H3_UPPER]], ['latLngToCell', H3_ERROR, [NUMBER, NUMBER, RESOLUTION, POINTER]], ['cellToLatLng', H3_ERROR, [H3_LOWER, H3_UPPER, POINTER]], ['cellToBoundary', H3_ERROR, [H3_LOWER, H3_UPPER, POINTER]], ['maxGridDiskSize', H3_ERROR, [NUMBER, POINTER]], ['gridDisk', H3_ERROR, [H3_LOWER, H3_UPPER, NUMBER, POINTER]], ['gridDiskDistances', H3_ERROR, [H3_LOWER, H3_UPPER, NUMBER, POINTER, POINTER]], ['gridRingUnsafe', H3_ERROR, [H3_LOWER, H3_UPPER, NUMBER, POINTER]], ['maxPolygonToCellsSize', H3_ERROR, [POINTER, RESOLUTION, NUMBER, POINTER]], ['polygonToCells', H3_ERROR, [POINTER, RESOLUTION, NUMBER, POINTER]], ['maxPolygonToCellsSizeExperimental', H3_ERROR, [POINTER, RESOLUTION, NUMBER, POINTER]], ['polygonToCellsExperimental', H3_ERROR, [POINTER, RESOLUTION, NUMBER, NUMBER, NUMBER, POINTER]], ['cellsToLinkedMultiPolygon', H3_ERROR, [POINTER, NUMBER, POINTER]], ['destroyLinkedMultiPolygon', null, [POINTER]], ['compactCells', H3_ERROR, [POINTER, POINTER, NUMBER, NUMBER]], ['uncompactCells', H3_ERROR, [POINTER, NUMBER, NUMBER, POINTER, NUMBER, RESOLUTION]], ['uncompactCellsSize', H3_ERROR, [POINTER, NUMBER, NUMBER, RESOLUTION, POINTER]], ['isPentagon', BOOLEAN, [H3_LOWER, H3_UPPER]], ['isResClassIII', BOOLEAN, [H3_LOWER, H3_UPPER]], ['getBaseCellNumber', NUMBER, [H3_LOWER, H3_UPPER]], ['getResolution', NUMBER, [H3_LOWER, H3_UPPER]], ['maxFaceCount', H3_ERROR, [H3_LOWER, H3_UPPER, POINTER]], ['getIcosahedronFaces', H3_ERROR, [H3_LOWER, H3_UPPER, POINTER]], ['cellToParent', H3_ERROR, [H3_LOWER, H3_UPPER, RESOLUTION, POINTER]], ['cellToChildren', H3_ERROR, [H3_LOWER, H3_UPPER, RESOLUTION, POINTER]], ['cellToCenterChild', H3_ERROR, [H3_LOWER, H3_UPPER, RESOLUTION, POINTER]], ['cellToChildrenSize', H3_ERROR, [H3_LOWER, H3_UPPER, RESOLUTION, POINTER]], ['cellToChildPos', H3_ERROR, [H3_LOWER, H3_UPPER, RESOLUTION, POINTER]], ['childPosToCell', H3_ERROR, [NUMBER, NUMBER, H3_LOWER, H3_UPPER, RESOLUTION, POINTER]], ['areNeighborCells', H3_ERROR, [H3_LOWER, H3_UPPER, H3_LOWER, H3_UPPER, POINTER]], ['cellsToDirectedEdge', H3_ERROR, [H3_LOWER, H3_UPPER, H3_LOWER, H3_UPPER, POINTER]], ['getDirectedEdgeOrigin', H3_ERROR, [H3_LOWER, H3_UPPER, POINTER]], ['getDirectedEdgeDestination', H3_ERROR, [H3_LOWER, H3_UPPER, POINTER]], ['isValidDirectedEdge', BOOLEAN, [H3_LOWER, H3_UPPER]], ['directedEdgeToCells', H3_ERROR, [H3_LOWER, H3_UPPER, POINTER]], ['originToDirectedEdges', H3_ERROR, [H3_LOWER, H3_UPPER, POINTER]], ['directedEdgeToBoundary', H3_ERROR, [H3_LOWER, H3_UPPER, POINTER]], ['gridDistance', H3_ERROR, [H3_LOWER, H3_UPPER, H3_LOWER, H3_UPPER, POINTER]], ['gridPathCells', H3_ERROR, [H3_LOWER, H3_UPPER, H3_LOWER, H3_UPPER, POINTER]], ['gridPathCellsSize', H3_ERROR, [H3_LOWER, H3_UPPER, H3_LOWER, H3_UPPER, POINTER]], ['cellToLocalIj', H3_ERROR, [H3_LOWER, H3_UPPER, H3_LOWER, H3_UPPER, NUMBER, POINTER]], ['localIjToCell', H3_ERROR, [H3_LOWER, H3_UPPER, POINTER, NUMBER, POINTER]], ['getHexagonAreaAvgM2', H3_ERROR, [RESOLUTION, POINTER]], ['getHexagonAreaAvgKm2', H3_ERROR, [RESOLUTION, POINTER]], ['getHexagonEdgeLengthAvgM', H3_ERROR, [RESOLUTION, POINTER]], ['getHexagonEdgeLengthAvgKm', H3_ERROR, [RESOLUTION, POINTER]], ['greatCircleDistanceM', NUMBER, [POINTER, POINTER]], ['greatCircleDistanceKm', NUMBER, [POINTER, POINTER]], ['greatCircleDistanceRads', NUMBER, [POINTER, POINTER]], ['cellAreaM2', H3_ERROR, [H3_LOWER, H3_UPPER, POINTER]], ['cellAreaKm2', H3_ERROR, [H3_LOWER, H3_UPPER, POINTER]], ['cellAreaRads2', H3_ERROR, [H3_LOWER, H3_UPPER, POINTER]], ['edgeLengthM', H3_ERROR, [H3_LOWER, H3_UPPER, POINTER]], ['edgeLengthKm', H3_ERROR, [H3_LOWER, H3_UPPER, POINTER]], ['edgeLengthRads', H3_ERROR, [H3_LOWER, H3_UPPER, POINTER]], ['getNumCells', H3_ERROR, [RESOLUTION, POINTER]], ['getRes0Cells', H3_ERROR, [POINTER]], ['res0CellCount', NUMBER], ['getPentagons', H3_ERROR, [NUMBER, POINTER]], ['pentagonCount', NUMBER], ['cellToVertex', H3_ERROR, [H3_LOWER, H3_UPPER, NUMBER, POINTER]], ['cellToVertexes', H3_ERROR, [H3_LOWER, H3_UPPER, POINTER]], ['vertexToLatLng', H3_ERROR, [H3_LOWER, H3_UPPER, POINTER]], ['isValidVertex', BOOLEAN, [H3_LOWER, H3_UPPER]]];

/*
 * Copyright 2018-2019, 2022 Uber Technologies, Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *         http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Error codes from the code library, aliased here for legibility
var E_SUCCESS = 0;
var E_FAILED = 1;
var E_DOMAIN = 2;
var E_LATLNG_DOMAIN = 3;
var E_RES_DOMAIN = 4;
var E_CELL_INVALID = 5;
var E_DIR_EDGE_INVALID = 6;
var E_UNDIR_EDGE_INVALID = 7;
var E_VERTEX_INVALID = 8;
var E_PENTAGON = 9;
var E_DUPLICATE_INPUT = 10;
var E_NOT_NEIGHBORS = 11;
var E_RES_MISMATCH = 12;
var E_MEMORY_ALLOC = 13;
var E_MEMORY_BOUNDS = 14;
var E_OPTION_INVALID = 15;

/**
 * Error messages corresponding to the core library error codes. See
 * https://h3geo.org/docs/library/errors#table-of-error-codes
 * @private
 */
var H3_ERROR_MSGS = {};
H3_ERROR_MSGS[E_SUCCESS] = 'Success';
H3_ERROR_MSGS[E_FAILED] = 'The operation failed but a more specific error is not available';
H3_ERROR_MSGS[E_DOMAIN] = 'Argument was outside of acceptable range';
H3_ERROR_MSGS[E_LATLNG_DOMAIN] = 'Latitude or longitude arguments were outside of acceptable range';
H3_ERROR_MSGS[E_RES_DOMAIN] = 'Resolution argument was outside of acceptable range';
H3_ERROR_MSGS[E_CELL_INVALID] = 'Cell argument was not valid';
H3_ERROR_MSGS[E_DIR_EDGE_INVALID] = 'Directed edge argument was not valid';
H3_ERROR_MSGS[E_UNDIR_EDGE_INVALID] = 'Undirected edge argument was not valid';
H3_ERROR_MSGS[E_VERTEX_INVALID] = 'Vertex argument was not valid';
H3_ERROR_MSGS[E_PENTAGON] = 'Pentagon distortion was encountered';
H3_ERROR_MSGS[E_DUPLICATE_INPUT] = 'Duplicate input';
H3_ERROR_MSGS[E_NOT_NEIGHBORS] = 'Cell arguments were not neighbors';
H3_ERROR_MSGS[E_RES_MISMATCH] = 'Cell arguments had incompatible resolutions';
H3_ERROR_MSGS[E_MEMORY_ALLOC] = 'Memory allocation failed';
H3_ERROR_MSGS[E_MEMORY_BOUNDS] = 'Bounds of provided memory were insufficient';
H3_ERROR_MSGS[E_OPTION_INVALID] = 'Mode or flags argument was not valid';

// Error codes for JS errors thrown in the bindings
var E_UNKNOWN_UNIT = 1000;
var E_ARRAY_LENGTH = 1001;
var E_NULL_INDEX = 1002;

/**
 * Error messages for errors thrown in the binding code. These don't strictly
 * need error codes, but it's simpler to treat all of the errors consistently
 * @private
 */
var JS_ERROR_MESSAGES = {};
JS_ERROR_MESSAGES[E_UNKNOWN_UNIT] = 'Unknown unit';
JS_ERROR_MESSAGES[E_ARRAY_LENGTH] = 'Array length out of bounds';
JS_ERROR_MESSAGES[E_NULL_INDEX] = 'Got unexpected null value for H3 index';
var UNKNOWN_ERROR_MSG = 'Unknown error';

/**
 * Create an error with an attached code
 * @private
 * @param {Record<number, string>} messages  Map of code-to-messages to use
 * @param {number} errCode                   Numeric error code
 * @param {{value: unknown} | {}} [meta]     Metadata with value to associate with the error
 */
function createError(messages, errCode, meta) {
  // The error value may be "undefined", so check if the argument was provided
  var hasValue = meta && 'value' in meta;
  // Throw a custom error type with the code attached
  var err = new Error(((messages[errCode] || UNKNOWN_ERROR_MSG) + " (code: " + errCode + (hasValue ? (", value: " + (meta.value)) : '') + ")"));
  // @ts-expect-error - TS doesn't like extending Error
  err.code = errCode;
  return err;
}

/**
 * Custom error for H3Error codes
 * @private
 * @param {number} errCode     Error code from the H3 library
 * @param {unknown} [value]    Value to associate with the error, if any
 * @returns {Error}
 */
function H3LibraryError(errCode, value) {
  // The error value may be "undefined", so check if the argument was provided
  var meta = arguments.length === 2 ? {
    value: value
  } : {};
  return createError(H3_ERROR_MSGS, errCode, meta);
}

/**
 * Custom errors thrown from the JS bindings.
 * @private
 * @param {number} errCode     Error code from the H3 library
 * @param {unknown} [value]    Value to associate with the error, if any
 * @returns {Error}
 */
function JSBindingError(errCode, value) {
  // The error value may be "undefined", so check if the argument was provided
  var meta = arguments.length === 2 ? {
    value: value
  } : {};
  return createError(JS_ERROR_MESSAGES, errCode, meta);
}

/**
 * Throw a JavaScript error if the C library return code is an error
 * @private
 * @param {number} errCode     Error code from the H3 library
 * @throws {Error} Error if err is not E_SUCCESS (0)
 */
function throwIfError(errCode) {
  if (errCode !== 0) {
    throw H3LibraryError(errCode);
  }
}

/*
 * Copyright 2018-2019, 2022 Uber Technologies, Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *         http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * Map of C-defined functions
 * @type {any}
 * @private
 */
var H3 = {};

// Create the bound functions themselves
BINDINGS.forEach(function bind(def) {
  H3[def[0]] = libh3.cwrap.apply(libh3, def);
});

// Alias the hexidecimal base for legibility
var BASE_16 = 16;

// Alias unused bits for legibility
var UNUSED_UPPER_32_BITS = 0;

// ----------------------------------------------------------------------------
// Byte size imports

var SZ_INT = 4;
var SZ_PTR = 4;
var SZ_DBL = 8;
var SZ_INT64 = 8;
var SZ_H3INDEX = H3.sizeOfH3Index();
var SZ_LATLNG = H3.sizeOfLatLng();
var SZ_CELLBOUNDARY = H3.sizeOfCellBoundary();
var SZ_GEOPOLYGON = H3.sizeOfGeoPolygon();
var SZ_GEOLOOP = H3.sizeOfGeoLoop();
var SZ_LINKED_GEOPOLYGON = H3.sizeOfLinkedGeoPolygon();
var SZ_COORDIJ = H3.sizeOfCoordIJ();

// ----------------------------------------------------------------------------
// Custom types

/**
 * 64-bit hexidecimal string representation of an H3 index
 * @static
 * @typedef {string} H3Index
 */

/**
 * 64-bit hexidecimal string representation of an H3 index,
 * or two 32-bit integers in little endian order in an array.
 * @static
 * @typedef {string | number[]} H3IndexInput
 */

/**
 * Coordinates as an `{i, j}` pair
 * @static
 * @typedef CoordIJ
 * @property {number} i
 * @property {number} j
 */

/**
 * Custom JS Error instance with an attached error code. Error codes come from the
 * core H3 library and can be found [in the H3 docs](https://h3geo.org/docs/library/errors#table-of-error-codes).
 * @static
 * @typedef H3Error
 * @property {string} message
 * @property {number} code
 */

/**
 * Pair of lat,lng coordinates (or lng,lat if GeoJSON output is specified)
 * @static
 * @typedef {number[]} CoordPair
 */

/**
 * Pair of lower,upper 32-bit ints representing a 64-bit value
 * @static
 * @typedef {number[]} SplitLong
 */

// ----------------------------------------------------------------------------
// Unit constants

/**
 * Length/Area units
 * @static
 * @property {string} m
 * @property {string} m2
 * @property {string} km
 * @property {string} km2
 * @property {string} rads
 * @property {string} rads2
 */
var UNITS = {
  m: 'm',
  m2: 'm2',
  km: 'km',
  km2: 'km2',
  rads: 'rads',
  rads2: 'rads2'
};

// ----------------------------------------------------------------------------
// Flags

/**
 * Mode flags for polygonToCells
 * @static
 * @property {string} containmentCenter
 * @property {string} containmentFull
 * @property {string} containmentOverlapping
 * @property {string} containmentOverlappingBbox
 */
var POLYGON_TO_CELLS_FLAGS = {
  containmentCenter: 'containmentCenter',
  containmentFull: 'containmentFull',
  containmentOverlapping: 'containmentOverlapping',
  containmentOverlappingBbox: 'containmentOverlappingBbox'
};

// ----------------------------------------------------------------------------
// Utilities and helpers

/**
 * @private
 * @param {string} flags Value from POLYGON_TO_CELLS_FLAGS
 * @returns {number} Flag value
 * @throws {H3Error} If invalid
 */
function polygonToCellsFlagsToNumber(flags) {
  switch (flags) {
    case POLYGON_TO_CELLS_FLAGS.containmentCenter:
      return 0;
    case POLYGON_TO_CELLS_FLAGS.containmentFull:
      return 1;
    case POLYGON_TO_CELLS_FLAGS.containmentOverlapping:
      return 2;
    case POLYGON_TO_CELLS_FLAGS.containmentOverlappingBbox:
      return 3;
    default:
      throw JSBindingError(E_OPTION_INVALID, flags);
  }
}

/**
 * Validate a resolution, throwing an error if invalid
 * @private
 * @param  {unknown} res Value to validate
 * @return {number}      Valid res
 * @throws {H3Error}     If invalid
 */
function validateRes(res) {
  if (typeof res !== 'number' || res < 0 || res > 15 || Math.floor(res) !== res) {
    throw H3LibraryError(E_RES_DOMAIN, res);
  }
  return res;
}

/**
 * Assert H3 index output, throwing an error if null
 * @private
 * @param {H3Index | null} h3Index    Index to validate
 * @return {H3Index}
 * @throws {H3Error}     If invalid
 */
function validateH3Index(h3Index) {
  if (!h3Index) { throw JSBindingError(E_NULL_INDEX); }
  return h3Index;
}
var MAX_JS_ARRAY_LENGTH = Math.pow(2, 32) - 1;

/**
 * Validate an array length. JS will throw its own error if you try
 * to create an array larger than 2^32 - 1, but validating beforehand
 * allows us to exit early before we try to process large amounts
 * of data that won't even fit in an output array
 * @private
 * @param  {number} length  Length to validate
 * @return {number}         Valid array length
 * @throws {H3Error}        If invalid
 */
function validateArrayLength(length) {
  if (length > MAX_JS_ARRAY_LENGTH) {
    throw JSBindingError(E_ARRAY_LENGTH, length);
  }
  return length;
}
var INVALID_HEXIDECIMAL_CHAR = /[^0-9a-fA-F]/;

/**
 * Convert an H3 index (64-bit hexidecimal string) into a "split long" - a pair of 32-bit ints
 * @param  {H3IndexInput} h3Index  H3 index to check
 * @return {SplitLong}             A two-element array with 32 lower bits and 32 upper bits
 */
function h3IndexToSplitLong(h3Index) {
  if (Array.isArray(h3Index) && h3Index.length === 2 && Number.isInteger(h3Index[0]) && Number.isInteger(h3Index[1])) {
    return h3Index;
  }
  if (typeof h3Index !== 'string' || INVALID_HEXIDECIMAL_CHAR.test(h3Index)) {
    return [0, 0];
  }
  var upper = parseInt(h3Index.substring(0, h3Index.length - 8), BASE_16);
  var lower = parseInt(h3Index.substring(h3Index.length - 8), BASE_16);
  return [lower, upper];
}

/**
 * Convert a 32-bit int to a hexdecimal string
 * @private
 * @param  {number} num  Integer to convert
 * @return {H3Index}     Hexidecimal string
 */
function hexFrom32Bit(num) {
  if (num >= 0) {
    return num.toString(BASE_16);
  }

  // Handle negative numbers
  num = num & 0x7fffffff;
  var tempStr = zeroPad(8, num.toString(BASE_16));
  var topNum = (parseInt(tempStr[0], BASE_16) + 8).toString(BASE_16);
  tempStr = topNum + tempStr.substring(1);
  return tempStr;
}

/**
 * Get a H3 index string from a split long (pair of 32-bit ints)
 * @param  {number} lower Lower 32 bits
 * @param  {number} upper Upper 32 bits
 * @return {H3Index}       H3 index
 */
function splitLongToH3Index(lower, upper) {
  return hexFrom32Bit(upper) + zeroPad(8, hexFrom32Bit(lower));
}

/**
 * Zero-pad a string to a given length
 * @private
 * @param  {number} fullLen Target length
 * @param  {string} numStr  String to zero-pad
 * @return {string}         Zero-padded string
 */
function zeroPad(fullLen, numStr) {
  var numZeroes = fullLen - numStr.length;
  var outStr = '';
  for (var i = 0; i < numZeroes; i++) {
    outStr += '0';
  }
  outStr = outStr + numStr;
  return outStr;
}

// One more than the max size of an unsigned 32-bit int.
// Dividing by this number is equivalent to num >>> 32
var UPPER_BIT_DIVISOR = Math.pow(2, 32);

/**
 * Convert a JS double-precision floating point number to a split long
 * @private
 * @param  {number} num  Number to convert
 * @return {SplitLong}     A two-element array with 32 lower bits and 32 upper bits
 */
function numberToSplitLong(num) {
  if (typeof num !== 'number') {
    return [0, 0];
  }
  return [num | 0, num / UPPER_BIT_DIVISOR | 0];
}

/**
 * Populate a C-appropriate GeoLoop struct from a polygon array
 * @private
 * @param  {number[][]} polygonArray  Polygon, as an array of coordinate pairs
 * @param  {number}  geoLoop          C pointer to a GeoLoop struct
 * @param  {boolean} isGeoJson        Whether coordinates are in [lng, lat] order per GeoJSON spec
 * @return {number}                   C pointer to populated GeoLoop struct
 */
function polygonArrayToGeoLoop(polygonArray, geoLoop, isGeoJson) {
  var numVerts = polygonArray.length;
  var geoCoordArray = libh3._calloc(numVerts, SZ_LATLNG);
  // Support [lng, lat] pairs if GeoJSON is specified
  var latIndex = isGeoJson ? 1 : 0;
  var lngIndex = isGeoJson ? 0 : 1;
  for (var i = 0; i < numVerts * 2; i += 2) {
    libh3.HEAPF64.set([polygonArray[i / 2][latIndex], polygonArray[i / 2][lngIndex]].map(degsToRads), geoCoordArray / SZ_DBL + i);
  }
  libh3.HEAPU32.set([numVerts, geoCoordArray], geoLoop / SZ_INT);
  return geoLoop;
}

/**
 * Create a C-appropriate GeoPolygon struct from an array of polygons
 * @private
 * @param  {number[][][]} coordinates Array of polygons, each an array of coordinate pairs
 * @param  {boolean} isGeoJson        Whether coordinates are in [lng, lat] order per GeoJSON spec
 * @return {number}                   C pointer to populated GeoPolygon struct
 */
function coordinatesToGeoPolygon(coordinates, isGeoJson) {
  // Any loops beyond the first loop are holes
  var numHoles = coordinates.length - 1;
  var geoPolygon = libh3._calloc(SZ_GEOPOLYGON);
  // Byte positions within the struct
  var geoLoopOffset = 0;
  var numHolesOffset = geoLoopOffset + SZ_GEOLOOP;
  var holesOffset = numHolesOffset + SZ_INT;
  // geoLoop is first part of struct
  polygonArrayToGeoLoop(coordinates[0], geoPolygon + geoLoopOffset, isGeoJson);
  var holes;
  if (numHoles > 0) {
    holes = libh3._calloc(numHoles, SZ_GEOLOOP);
    for (var i = 0; i < numHoles; i++) {
      polygonArrayToGeoLoop(coordinates[i + 1], holes + SZ_GEOLOOP * i, isGeoJson);
    }
  }
  libh3.setValue(geoPolygon + numHolesOffset, numHoles, 'i32');
  libh3.setValue(geoPolygon + holesOffset, holes, 'i32');
  return geoPolygon;
}

/**
 * Free memory allocated for a GeoPolygon struct. It is an error to access the struct
 * after passing it to this method.
 * @private
 * @param {number} geoPolygon     C pointer to GeoPolygon struct
 * @return {void}
 */
function destroyGeoPolygon(geoPolygon) {
  // Byte positions within the struct
  var geoLoopOffset = 0;
  var numHolesOffset = geoLoopOffset + SZ_GEOLOOP;
  var holesOffset = numHolesOffset + SZ_INT;
  // Offset of the geoLoop vertex array pointer within the GeoLoop struct
  var geoLoopArrayOffset = SZ_INT;
  // Free the outer vertex array
  libh3._free(libh3.getValue(geoPolygon + geoLoopOffset + geoLoopArrayOffset, 'i8*'));
  // Free the vertex array for the holes, if any
  var numHoles = libh3.getValue(geoPolygon + numHolesOffset, 'i32');
  if (numHoles > 0) {
    var holes = libh3.getValue(geoPolygon + holesOffset, 'i32');
    for (var i = 0; i < numHoles; i++) {
      libh3._free(libh3.getValue(holes + SZ_GEOLOOP * i + geoLoopArrayOffset, 'i8*'));
    }
    libh3._free(holes);
  }
  libh3._free(geoPolygon);
}

/**
 * Read an H3 index from a pointer to C memory.
 * @private
 * @param  {number} cAddress  Pointer to allocated C memory
 * @param {number} offset     Offset, in number of H3 indexes, in case we're
 *                            reading an array
 * @return {H3Index | null}   H3 index, or null if index was invalid
 */
function readH3IndexFromPointer(cAddress, offset) {
  if ( offset === void 0 ) offset = 0;

  var lower = libh3.getValue(cAddress + SZ_H3INDEX * offset, 'i32');
  var upper = libh3.getValue(cAddress + SZ_H3INDEX * offset + SZ_INT, 'i32');
  // The lower bits are allowed to be 0s, but if the upper bits are 0
  // this represents an invalid H3 index
  return upper ? splitLongToH3Index(lower, upper) : null;
}

/**
 * Read a boolean (32 bit) from a pointer to C memory.
 * @private
 * @param  {number} cAddress  Pointer to allocated C memory
 * @param {number} offset     Offset, in number of booleans, in case we're
 *                            reading an array
 * @return {Boolean} Boolean value
 */
function readBooleanFromPointer(cAddress, offset) {
  if ( offset === void 0 ) offset = 0;

  var val = libh3.getValue(cAddress + SZ_INT * offset, 'i32');
  return Boolean(val);
}

/**
 * Read a double from a pointer to C memory.
 * @private
 * @param  {number} cAddress  Pointer to allocated C memory
 * @param {number} offset     Offset, in number of doubles, in case we're
 *                            reading an array
 * @return {number} Double value
 */
function readDoubleFromPointer(cAddress, offset) {
  if ( offset === void 0 ) offset = 0;

  return libh3.getValue(cAddress + SZ_DBL * offset, 'double');
}

/**
 * Read a 64-bit int from a pointer to C memory into a JS 64-bit float.
 * Note that this may lose precision if larger than MAX_SAFE_INTEGER
 * @private
 * @param  {number} cAddress  Pointer to allocated C memory
 * @return {number} Double value
 */
function readInt64AsDoubleFromPointer(cAddress) {
  return H3.readInt64AsDoubleFromPointer(cAddress);
}

/**
 * Store an H3 index in C memory. Primarily used as an efficient way to
 * write sets of hexagons.
 * @private
 * @param  {H3IndexInput} h3Index  H3 index to store
 * @param  {number} cAddress  Pointer to allocated C memory
 * @param {number} offset     Offset, in number of H3 indexes from beginning
 *                            of the current array
 */
function storeH3Index(h3Index, cAddress, offset) {
  // HEAPU32 is a typed array projection on the index space
  // as unsigned 32-bit integers. This means the index needs
  // to be divided by SZ_INT (4) to access correctly. Also,
  // the H3 index is 64 bits, so we skip by twos as we're writing
  // to 32-bit integers in the proper order.
  libh3.HEAPU32.set(h3IndexToSplitLong(h3Index), cAddress / SZ_INT + 2 * offset);
}

/**
 * Read an array of 64-bit H3 indexes from C and convert to a JS array of
 * H3 index strings
 * @private
 * @param  {number} cAddress    Pointer to C ouput array
 * @param  {number} maxCount    Max number of hexagons in array. Hexagons with
 *                              the value 0 will be skipped, so this isn't
 *                              necessarily the length of the output array.
 * @return {H3Index[]}          Array of H3 indexes
 */
function readArrayOfH3Indexes(cAddress, maxCount) {
  var out = [];
  for (var i = 0; i < maxCount; i++) {
    var h3Index = readH3IndexFromPointer(cAddress, i);
    if (h3Index !== null) {
      out.push(h3Index);
    }
  }
  return out;
}

/**
 * Store an array of H3 index strings as a C array of 64-bit integers.
 * @private
 * @param  {number} cAddress    Pointer to C input array
 * @param  {H3IndexInput[]} hexagons H3 indexes to pass to the C lib
 */
function storeArrayOfH3Indexes(cAddress, hexagons) {
  // Assuming the cAddress points to an already appropriately
  // allocated space
  var count = hexagons.length;
  for (var i = 0; i < count; i++) {
    storeH3Index(hexagons[i], cAddress, i);
  }
}

/**
 * Populate a C-appropriate LatLng struct from a [lat, lng] array
 * @private
 * @param {number} lat     Coordinate latitude
 * @param {number} lng     Coordinate longitude
 * @return {number}        C pointer to populated LatLng struct
 */
function storeLatLng(lat, lng) {
  var geoCoord = libh3._calloc(1, SZ_LATLNG);
  libh3.HEAPF64.set([lat, lng].map(degsToRads), geoCoord / SZ_DBL);
  return geoCoord;
}

/**
 * Read a single lat or lng value
 * @private
 * @param  {number} cAddress Pointer to C value
 * @return {number}
 */
function readSingleCoord(cAddress) {
  return radsToDegs(libh3.getValue(cAddress, 'double'));
}

/**
 * Read a LatLng from C and return a [lat, lng] pair.
 * @private
 * @param  {number} cAddress    Pointer to C struct
 * @return {CoordPair}          [lat, lng] pair
 */
function readLatLng(cAddress) {
  return [readSingleCoord(cAddress), readSingleCoord(cAddress + SZ_DBL)];
}

/**
 * Read a LatLng from C and return a GeoJSON-style [lng, lat] pair.
 * @private
 * @param  {number} cAddress    Pointer to C struct
 * @return {CoordPair}          [lng, lat] pair
 */
function readLatLngGeoJson(cAddress) {
  return [readSingleCoord(cAddress + SZ_DBL), readSingleCoord(cAddress)];
}

/**
 * Read the CellBoundary structure into a list of geo coordinate pairs
 * @private
 * @param {number}  cellBoundary       C pointer to CellBoundary struct
 * @param {boolean} [geoJsonCoords]    Whether to provide GeoJSON coordinate order: [lng, lat]
 * @param {boolean} [closedLoop]       Whether to close the loop
 * @return {CoordPair[]}               Array of geo coordinate pairs
 */
function readCellBoundary(cellBoundary, geoJsonCoords, closedLoop) {
  var numVerts = libh3.getValue(cellBoundary, 'i32');
  // Note that though numVerts is an int, the coordinate doubles have to be
  // aligned to 8 bytes, hence the 8-byte offset here
  var vertsPos = cellBoundary + SZ_DBL;
  var out = [];
  // Support [lng, lat] pairs if GeoJSON is specified
  var readCoord = geoJsonCoords ? readLatLngGeoJson : readLatLng;
  for (var i = 0; i < numVerts * 2; i += 2) {
    out.push(readCoord(vertsPos + SZ_DBL * i));
  }
  if (closedLoop) {
    // Close loop if GeoJSON is specified
    out.push(out[0]);
  }
  return out;
}

/**
 * Read the LinkedGeoPolygon structure into a nested array of MultiPolygon coordinates
 * @private
 * @param {number}  polygon           C pointer to LinkedGeoPolygon struct
 * @param {boolean} [formatAsGeoJson] Whether to provide GeoJSON output: [lng, lat], closed loops
 * @return {CoordPair[][][]}          MultiPolygon-style output.
 */
function readMultiPolygon(polygon, formatAsGeoJson) {
  var output = [];
  var readCoord = formatAsGeoJson ? readLatLngGeoJson : readLatLng;
  var loops;
  var loop;
  var coords;
  var coord;
  // Loop through the linked structure, building the output
  while (polygon) {
    output.push(loops = []);
    // Follow ->first pointer
    loop = libh3.getValue(polygon, 'i8*');
    while (loop) {
      loops.push(coords = []);
      // Follow ->first pointer
      coord = libh3.getValue(loop, 'i8*');
      while (coord) {
        coords.push(readCoord(coord));
        // Follow ->next pointer
        coord = libh3.getValue(coord + SZ_DBL * 2, 'i8*');
      }
      if (formatAsGeoJson) {
        // Close loop if GeoJSON is requested
        coords.push(coords[0]);
      }
      // Follow ->next pointer
      loop = libh3.getValue(loop + SZ_PTR * 2, 'i8*');
    }
    // Follow ->next pointer
    polygon = libh3.getValue(polygon + SZ_PTR * 2, 'i8*');
  }
  return output;
}

/**
 * Read a CoordIJ from C and return an {i, j} pair.
 * @private
 * @param  {number} cAddress    Pointer to C struct
 * @return {CoordIJ}            {i, j} pair
 */
function readCoordIJ(cAddress) {
  return {
    i: libh3.getValue(cAddress, 'i32'),
    j: libh3.getValue(cAddress + SZ_INT, 'i32')
  };
}

/**
 * Store an {i, j} pair to a C CoordIJ struct.
 * @private
 * @param  {number} cAddress    Pointer to C memory
 * @param {CoordIJ} ij          {i,j} pair to store
 * @return {void}
 */
function storeCoordIJ(cAddress, ref) {
  var i = ref.i;
  var j = ref.j;

  libh3.setValue(cAddress, i, 'i32');
  libh3.setValue(cAddress + SZ_INT, j, 'i32');
}

/**
 * Read an array of positive integers array from C. Negative
 * values are considered invalid and ignored in output.
 * @private
 * @param  {number} cAddress    Pointer to C array
 * @param  {number} count       Length of C array
 * @return {number[]}           Javascript integer array
 */
function readArrayOfPositiveIntegers(cAddress, count) {
  var out = [];
  for (var i = 0; i < count; i++) {
    var int = libh3.getValue(cAddress + SZ_INT * i, 'i32');
    if (int >= 0) {
      out.push(int);
    }
  }
  return out;
}

// ----------------------------------------------------------------------------
// Public API functions: Core

/**
 * Whether a given string represents a valid H3 index
 * @static
 * @param  {H3IndexInput} h3Index  H3 index to check
 * @return {boolean}          Whether the index is valid
 */
function isValidCell(h3Index) {
  var ref = h3IndexToSplitLong(h3Index);
  var lower = ref[0];
  var upper = ref[1];
  return Boolean(H3.isValidCell(lower, upper));
}

/**
 * Whether the given H3 index is a pentagon
 * @static
 * @param  {H3IndexInput} h3Index  H3 index to check
 * @return {boolean}          isPentagon
 */
function isPentagon(h3Index) {
  var ref = h3IndexToSplitLong(h3Index);
  var lower = ref[0];
  var upper = ref[1];
  return Boolean(H3.isPentagon(lower, upper));
}

/**
 * Whether the given H3 index is in a Class III resolution (rotated versus
 * the icosahedron and subject to shape distortion adding extra points on
 * icosahedron edges, making them not true hexagons).
 * @static
 * @param  {H3IndexInput} h3Index  H3 index to check
 * @return {boolean}          isResClassIII
 */
function isResClassIII(h3Index) {
  var ref = h3IndexToSplitLong(h3Index);
  var lower = ref[0];
  var upper = ref[1];
  return Boolean(H3.isResClassIII(lower, upper));
}

/**
 * Get the number of the base cell for a given H3 index
 * @static
 * @param  {H3IndexInput} h3Index  H3 index to get the base cell for
 * @return {number}           Index of the base cell (0-121)
 */
function getBaseCellNumber(h3Index) {
  var ref = h3IndexToSplitLong(h3Index);
  var lower = ref[0];
  var upper = ref[1];
  return H3.getBaseCellNumber(lower, upper);
}

/**
 * Get the indices of all icosahedron faces intersected by a given H3 index
 * @static
 * @param  {H3IndexInput} h3Index  H3 index to get faces for
 * @return {number[]}              Indices (0-19) of all intersected faces
 * @throws {H3Error}               If input is invalid
 */
function getIcosahedronFaces(h3Index) {
  var ref = h3IndexToSplitLong(h3Index);
  var lower = ref[0];
  var upper = ref[1];
  var countPtr = libh3._malloc(SZ_INT);
  try {
    throwIfError(H3.maxFaceCount(lower, upper, countPtr));
    var count = libh3.getValue(countPtr, 'i32');
    var faces = libh3._malloc(SZ_INT * count);
    try {
      throwIfError(H3.getIcosahedronFaces(lower, upper, faces));
      return readArrayOfPositiveIntegers(faces, count);
    } finally {
      libh3._free(faces);
    }
  } finally {
    libh3._free(countPtr);
  }
}

/**
 * Returns the resolution of an H3 index
 * @static
 * @param  {H3IndexInput} h3Index H3 index to get resolution
 * @return {number}          The number (0-15) resolution, or -1 if invalid
 */
function getResolution(h3Index) {
  var ref = h3IndexToSplitLong(h3Index);
  var lower = ref[0];
  var upper = ref[1];
  if (!H3.isValidCell(lower, upper)) {
    // Compatability with stated API
    return -1;
  }
  return H3.getResolution(lower, upper);
}

/**
 * Get the hexagon containing a lat,lon point
 * @static
 * @param  {number} lat Latitude of point
 * @param  {number} lng Longtitude of point
 * @param  {number} res Resolution of hexagons to return
 * @return {H3Index}    H3 index
 * @throws {H3Error}    If input is invalid
 */
function latLngToCell(lat, lng, res) {
  var latLng = libh3._malloc(SZ_LATLNG);
  // Slightly more efficient way to set the memory
  libh3.HEAPF64.set([lat, lng].map(degsToRads), latLng / SZ_DBL);
  // Read value as a split long
  var h3Index = libh3._malloc(SZ_H3INDEX);
  try {
    throwIfError(H3.latLngToCell(latLng, res, h3Index));
    return validateH3Index(readH3IndexFromPointer(h3Index));
  } finally {
    libh3._free(h3Index);
    libh3._free(latLng);
  }
}

/**
 * Get the lat,lon center of a given hexagon
 * @static
 * @param  {H3IndexInput} h3Index  H3 index
 * @return {CoordPair}             Point as a [lat, lng] pair
 * @throws {H3Error}               If input is invalid
 */
function cellToLatLng(h3Index) {
  var latLng = libh3._malloc(SZ_LATLNG);
  var ref = h3IndexToSplitLong(h3Index);
  var lower = ref[0];
  var upper = ref[1];
  try {
    throwIfError(H3.cellToLatLng(lower, upper, latLng));
    return readLatLng(latLng);
  } finally {
    libh3._free(latLng);
  }
}

/**
 * Get the vertices of a given hexagon (or pentagon), as an array of [lat, lng]
 * points. For pentagons and hexagons on the edge of an icosahedron face, this
 * function may return up to 10 vertices.
 * @static
 * @param  {H3IndexInput} h3Index          H3 index
 * @param {boolean} [formatAsGeoJson] Whether to provide GeoJSON output: [lng, lat], closed loops
 * @return {CoordPair[]}              Array of [lat, lng] pairs
 * @throws {H3Error}                  If input is invalid
 */
function cellToBoundary(h3Index, formatAsGeoJson) {
  var cellBoundary = libh3._malloc(SZ_CELLBOUNDARY);
  var ref = h3IndexToSplitLong(h3Index);
  var lower = ref[0];
  var upper = ref[1];
  try {
    throwIfError(H3.cellToBoundary(lower, upper, cellBoundary));
    return readCellBoundary(cellBoundary, formatAsGeoJson, formatAsGeoJson);
  } finally {
    libh3._free(cellBoundary);
  }
}

// ----------------------------------------------------------------------------
// Public API functions: Algorithms

/**
 * Get the parent of the given hexagon at a particular resolution
 * @static
 * @param  {H3IndexInput} h3Index  H3 index to get parent for
 * @param  {number} res       Resolution of hexagon to return
 * @return {H3Index}          H3 index of parent, or null for invalid input
 * @throws {H3Error}          If input is invalid
 */
function cellToParent(h3Index, res) {
  var ref = h3IndexToSplitLong(h3Index);
  var lower = ref[0];
  var upper = ref[1];
  var parent = libh3._malloc(SZ_H3INDEX);
  try {
    throwIfError(H3.cellToParent(lower, upper, res, parent));
    return validateH3Index(readH3IndexFromPointer(parent));
  } finally {
    libh3._free(parent);
  }
}

/**
 * Get the children/descendents of the given hexagon at a particular resolution
 * @static
 * @param  {H3IndexInput} h3Index  H3 index to get children for
 * @param  {number} res       Resolution of hexagons to return
 * @return {H3Index[]}        H3 indexes of children, or empty array for invalid input
 * @throws {H3Error}          If resolution is invalid or output is too large for JS
 */
function cellToChildren(h3Index, res) {
  // Bad input in this case can potentially result in high computation volume
  // using the current C algorithm. Validate and return an empty array on failure.
  if (!isValidCell(h3Index)) {
    return [];
  }
  var ref = h3IndexToSplitLong(h3Index);
  var lower = ref[0];
  var upper = ref[1];
  var count = validateArrayLength(cellToChildrenSize(h3Index, res));
  var hexagons = libh3._calloc(count, SZ_H3INDEX);
  try {
    throwIfError(H3.cellToChildren(lower, upper, res, hexagons));
    return readArrayOfH3Indexes(hexagons, count);
  } finally {
    libh3._free(hexagons);
  }
}

/**
 * Get the number of children for a cell at a given resolution
 * @static
 * @param  {H3IndexInput} h3Index  H3 index to get child count for
 * @param  {number} res            Child resolution
 * @return {number}                Number of children at res for the given cell
 * @throws {H3Error}               If cell or parentRes are invalid
 */
function cellToChildrenSize(h3Index, res) {
  if (!isValidCell(h3Index)) {
    throw H3LibraryError(E_CELL_INVALID);
  }
  var ref = h3IndexToSplitLong(h3Index);
  var lower = ref[0];
  var upper = ref[1];
  var countPtr = libh3._malloc(SZ_INT64);
  try {
    throwIfError(H3.cellToChildrenSize(lower, upper, res, countPtr));
    return readInt64AsDoubleFromPointer(countPtr);
  } finally {
    libh3._free(countPtr);
  }
}

/**
 * Get the center child of the given hexagon at a particular resolution
 * @static
 * @param  {H3IndexInput} h3Index  H3 index to get center child for
 * @param  {number} res       Resolution of cell to return
 * @return {H3Index}          H3 index of child, or null for invalid input
 * @throws {H3Error}          If resolution is invalid
 */
function cellToCenterChild(h3Index, res) {
  var ref = h3IndexToSplitLong(h3Index);
  var lower = ref[0];
  var upper = ref[1];
  var centerChild = libh3._malloc(SZ_H3INDEX);
  try {
    throwIfError(H3.cellToCenterChild(lower, upper, res, centerChild));
    return validateH3Index(readH3IndexFromPointer(centerChild));
  } finally {
    libh3._free(centerChild);
  }
}

/**
 * Get the position of the cell within an ordered list of all children of the
 * cell's parent at the specified resolution.
 * @static
 * @param  {H3IndexInput} h3Index  H3 index to get child pos for
 * @param  {number} parentRes      Resolution of reference parent
 * @return {number}                Position of child within parent at parentRes
 * @throws {H3Error}               If cell or parentRes are invalid
 */
function cellToChildPos(h3Index, parentRes) {
  var ref = h3IndexToSplitLong(h3Index);
  var lower = ref[0];
  var upper = ref[1];
  var childPos = libh3._malloc(SZ_INT64);
  try {
    throwIfError(H3.cellToChildPos(lower, upper, parentRes, childPos));
    return readInt64AsDoubleFromPointer(childPos);
  } finally {
    libh3._free(childPos);
  }
}

/**
 * Get the child cell at a given position within an ordered list of all children
 * at the specified resolution
 * @static
 * @param  {number} childPos       Position of the child cell to get
 * @param  {H3IndexInput} h3Index  H3 index of the parent cell
 * @param  {number} childRes       Resolution of child cell to return
 * @return {H3Index}          H3 index of child
 * @throws {H3Error}          If input is invalid
 */
function childPosToCell(childPos, h3Index, childRes) {
  var ref = numberToSplitLong(childPos);
  var cpLower = ref[0];
  var cpUpper = ref[1];
  var ref$1 = h3IndexToSplitLong(h3Index);
  var lower = ref$1[0];
  var upper = ref$1[1];
  var child = libh3._malloc(SZ_H3INDEX);
  try {
    throwIfError(H3.childPosToCell(cpLower, cpUpper, lower, upper, childRes, child));
    return validateH3Index(readH3IndexFromPointer(child));
  } finally {
    libh3._free(child);
  }
}

/**
 * Get all hexagons in a k-ring around a given center. The order of the hexagons is undefined.
 * @static
 * @param  {H3IndexInput} h3Index  H3 index of center hexagon
 * @param  {number} ringSize  Radius of k-ring
 * @return {H3Index[]}        H3 indexes for all hexagons in ring
 * @throws {H3Error}          If input is invalid or output is too large for JS
 */
function gridDisk(h3Index, ringSize) {
  var ref = h3IndexToSplitLong(h3Index);
  var lower = ref[0];
  var upper = ref[1];
  var countPtr = libh3._malloc(SZ_INT64);
  try {
    throwIfError(H3.maxGridDiskSize(ringSize, countPtr));
    var count = validateArrayLength(readInt64AsDoubleFromPointer(countPtr));
    var hexagons = libh3._calloc(count, SZ_H3INDEX);
    try {
      throwIfError(H3.gridDisk(lower, upper, ringSize, hexagons));
      return readArrayOfH3Indexes(hexagons, count);
    } finally {
      libh3._free(hexagons);
    }
  } finally {
    libh3._free(countPtr);
  }
}

/**
 * Get all hexagons in a k-ring around a given center, in an array of arrays
 * ordered by distance from the origin. The order of the hexagons within each ring is undefined.
 * @static
 * @param  {H3IndexInput} h3Index  H3 index of center hexagon
 * @param  {number} ringSize  Radius of k-ring
 * @return {H3Index[][]}      Array of arrays with H3 indexes for all hexagons each ring
 * @throws {H3Error}          If input is invalid or output is too large for JS
 */
function gridDiskDistances(h3Index, ringSize) {
  var ref = h3IndexToSplitLong(h3Index);
  var lower = ref[0];
  var upper = ref[1];
  var countPtr = libh3._malloc(SZ_INT64);
  try {
    throwIfError(H3.maxGridDiskSize(ringSize, countPtr));
    var count = validateArrayLength(readInt64AsDoubleFromPointer(countPtr));
    var kRings = libh3._calloc(count, SZ_H3INDEX);
    var distances = libh3._calloc(count, SZ_INT);
    try {
      throwIfError(H3.gridDiskDistances(lower, upper, ringSize, kRings, distances));
      /**
       * An array of empty arrays to hold the output
       * @type {string[][]}
       * @private
       */
      var out = [];
      for (var i = 0; i < ringSize + 1; i++) {
        out.push([]);
      }
      // Read the array of hexagons, putting them into the appropriate rings
      for (var i$1 = 0; i$1 < count; i$1++) {
        var cell = readH3IndexFromPointer(kRings, i$1);
        var index = libh3.getValue(distances + SZ_INT * i$1, 'i32');
        // eslint-disable-next-line max-depth
        if (cell !== null) {
          out[index].push(cell);
        }
      }
      return out;
    } finally {
      libh3._free(kRings);
      libh3._free(distances);
    }
  } finally {
    libh3._free(countPtr);
  }
}

/**
 * Get all hexagons in a hollow hexagonal ring centered at origin with sides of a given length.
 * Unlike gridDisk, this function will throw an error if there is a pentagon anywhere in the ring.
 * @static
 * @param  {H3IndexInput} h3Index  H3 index of center hexagon
 * @param  {number} ringSize  Radius of ring
 * @return {H3Index[]}        H3 indexes for all hexagons in ring
 * @throws {Error}            If the algorithm could not calculate the ring
 * @throws {H3Error}          If input is invalid
 */
function gridRingUnsafe(h3Index, ringSize) {
  var maxCount = ringSize === 0 ? 1 : 6 * ringSize;
  var hexagons = libh3._calloc(maxCount, SZ_H3INDEX);
  try {
    throwIfError(H3.gridRingUnsafe.apply(H3, h3IndexToSplitLong(h3Index).concat( [ringSize], [hexagons] )));
    return readArrayOfH3Indexes(hexagons, maxCount);
  } finally {
    libh3._free(hexagons);
  }
}

/**
 * Get all hexagons with centers contained in a given polygon. The polygon
 * is specified with GeoJson semantics as an array of loops. Each loop is
 * an array of [lat, lng] pairs (or [lng, lat] if isGeoJson is specified).
 * The first loop is the perimeter of the polygon, and subsequent loops are
 * expected to be holes.
 * @static
 * @param  {number[][] | number[][][]} coordinates
 *                                  Array of loops, or a single loop
 * @param  {number} res             Resolution of hexagons to return
 * @param  {boolean} [isGeoJson]    Whether to expect GeoJson-style [lng, lat]
 *                                  pairs instead of [lat, lng]
 * @return {H3Index[]}              H3 indexes for all hexagons in polygon
 * @throws {H3Error}                If input is invalid or output is too large for JS
 */
function polygonToCells(coordinates, res, isGeoJson) {
  validateRes(res);
  isGeoJson = Boolean(isGeoJson);
  // Guard against empty input
  if (coordinates.length === 0 || coordinates[0].length === 0) {
    return [];
  }
  // Wrap to expected format if a single loop is provided
  var polygon = typeof coordinates[0][0] === 'number' ? [coordinates] : coordinates;
  var geoPolygon = coordinatesToGeoPolygon(
  // @ts-expect-error - There's no way to convince TS that polygon is now number[][][]
  polygon, isGeoJson);
  var countPtr = libh3._malloc(SZ_INT64);
  try {
    throwIfError(H3.maxPolygonToCellsSize(geoPolygon, res, 0, countPtr));
    var count = validateArrayLength(readInt64AsDoubleFromPointer(countPtr));
    var hexagons = libh3._calloc(count, SZ_H3INDEX);
    try {
      throwIfError(H3.polygonToCells(geoPolygon, res, 0, hexagons));
      return readArrayOfH3Indexes(hexagons, count);
    } finally {
      libh3._free(hexagons);
    }
  } finally {
    libh3._free(countPtr);
    destroyGeoPolygon(geoPolygon);
  }
}

/**
 * Get all hexagons with centers contained in a given polygon. The polygon
 * is specified with GeoJson semantics as an array of loops. Each loop is
 * an array of [lat, lng] pairs (or [lng, lat] if isGeoJson is specified).
 * The first loop is the perimeter of the polygon, and subsequent loops are
 * expected to be holes.
 * @static
 * @param  {number[][] | number[][][]} coordinates
 *                                  Array of loops, or a single loop
 * @param  {number} res             Resolution of hexagons to return
 * @param  {string} flags           Value from POLYGON_TO_CELLS_FLAGS
 * @param  {boolean} [isGeoJson]    Whether to expect GeoJson-style [lng, lat]
 *                                  pairs instead of [lat, lng]
 * @return {H3Index[]}              H3 indexes for all hexagons in polygon
 * @throws {H3Error}                If input is invalid or output is too large for JS
 */
function polygonToCellsExperimental(coordinates, res, flags, isGeoJson) {
  validateRes(res);
  isGeoJson = Boolean(isGeoJson);
  var flagsInt = polygonToCellsFlagsToNumber(flags);
  // Guard against empty input
  if (coordinates.length === 0 || coordinates[0].length === 0) {
    return [];
  }
  // Wrap to expected format if a single loop is provided
  var polygon = typeof coordinates[0][0] === 'number' ? [coordinates] : coordinates;
  var geoPolygon = coordinatesToGeoPolygon(
  // @ts-expect-error - There's no way to convince TS that polygon is now number[][][]
  polygon, isGeoJson);
  var countPtr = libh3._malloc(SZ_INT64);
  try {
    throwIfError(H3.maxPolygonToCellsSizeExperimental(geoPolygon, res, flagsInt, countPtr));
    var count = validateArrayLength(readInt64AsDoubleFromPointer(countPtr));
    var hexagons = libh3._calloc(count, SZ_H3INDEX);
    try {
      throwIfError(H3.polygonToCellsExperimental(geoPolygon, res, flagsInt, count, UNUSED_UPPER_32_BITS, hexagons));
      return readArrayOfH3Indexes(hexagons, count);
    } finally {
      libh3._free(hexagons);
    }
  } finally {
    libh3._free(countPtr);
    destroyGeoPolygon(geoPolygon);
  }
}

/**
 * Get the outlines of a set of H3 hexagons, returned in GeoJSON MultiPolygon
 * format (an array of polygons, each with an array of loops, each an array of
 * coordinates). Coordinates are returned as [lat, lng] pairs unless GeoJSON
 * is requested.
 *
 * It is the responsibility of the caller to ensure that all hexagons in the
 * set have the same resolution and that the set contains no duplicates. Behavior
 * is undefined if duplicates or multiple resolutions are present, and the
 * algorithm may produce unexpected or invalid polygons.
 *
 * @static
 * @param {H3IndexInput[]} h3Indexes  H3 indexes to get outlines for
 * @param {boolean} [formatAsGeoJson] Whether to provide GeoJSON output: [lng, lat], closed loops
 * @return {CoordPair[][][]}          MultiPolygon-style output.
 * @throws {H3Error}                  If input is invalid
 */
function cellsToMultiPolygon(h3Indexes, formatAsGeoJson) {
  // Early exit on empty input
  if (!h3Indexes || !h3Indexes.length) {
    return [];
  }
  // Set up input set
  var indexCount = h3Indexes.length;
  var set = libh3._calloc(indexCount, SZ_H3INDEX);
  storeArrayOfH3Indexes(set, h3Indexes);
  // Allocate memory for output linked polygon
  var polygon = libh3._calloc(SZ_LINKED_GEOPOLYGON);
  try {
    throwIfError(H3.cellsToLinkedMultiPolygon(set, indexCount, polygon));
    return readMultiPolygon(polygon, formatAsGeoJson);
  } finally {
    // Clean up
    H3.destroyLinkedMultiPolygon(polygon);
    libh3._free(polygon);
    libh3._free(set);
  }
}

/**
 * Compact a set of hexagons of the same resolution into a set of hexagons across
 * multiple levels that represents the same area.
 * @static
 * @param  {H3IndexInput[]} h3Set H3 indexes to compact
 * @return {H3Index[]}       Compacted H3 indexes
 * @throws {H3Error}         If the input is invalid (e.g. duplicate hexagons)
 */
function compactCells(h3Set) {
  if (!h3Set || !h3Set.length) {
    return [];
  }
  // Set up input set
  var count = h3Set.length;
  var set = libh3._calloc(count, SZ_H3INDEX);
  storeArrayOfH3Indexes(set, h3Set);
  // Allocate memory for compacted hexagons, worst-case is no compaction
  var compactedSet = libh3._calloc(count, SZ_H3INDEX);
  try {
    throwIfError(H3.compactCells(set, compactedSet, count, UNUSED_UPPER_32_BITS));
    return readArrayOfH3Indexes(compactedSet, count);
  } finally {
    libh3._free(set);
    libh3._free(compactedSet);
  }
}

/**
 * Uncompact a compacted set of hexagons to hexagons of the same resolution
 * @static
 * @param  {H3IndexInput[]} compactedSet H3 indexes to uncompact
 * @param  {number}    res          The resolution to uncompact to
 * @return {H3Index[]}              The uncompacted H3 indexes
 * @throws {H3Error}                If the input is invalid (e.g. invalid resolution)
 */
function uncompactCells(compactedSet, res) {
  validateRes(res);
  if (!compactedSet || !compactedSet.length) {
    return [];
  }
  // Set up input set
  var count = compactedSet.length;
  var set = libh3._calloc(count, SZ_H3INDEX);
  storeArrayOfH3Indexes(set, compactedSet);
  // Estimate how many hexagons we need (always overestimates if in error)
  var uncompactCellSizePtr = libh3._malloc(SZ_INT64);
  try {
    throwIfError(H3.uncompactCellsSize(set, count, UNUSED_UPPER_32_BITS, res, uncompactCellSizePtr));
    var uncompactCellSize = validateArrayLength(readInt64AsDoubleFromPointer(uncompactCellSizePtr));
    // Allocate memory for uncompacted hexagons
    var uncompactedSet = libh3._calloc(uncompactCellSize, SZ_H3INDEX);
    try {
      throwIfError(H3.uncompactCells(set, count, UNUSED_UPPER_32_BITS, uncompactedSet, uncompactCellSize, UNUSED_UPPER_32_BITS, res));
      return readArrayOfH3Indexes(uncompactedSet, uncompactCellSize);
    } finally {
      libh3._free(set);
      libh3._free(uncompactedSet);
    }
  } finally {
    libh3._free(uncompactCellSizePtr);
  }
}

// ----------------------------------------------------------------------------
// Public API functions: Directed edges

/**
 * Whether two H3 indexes are neighbors (share an edge)
 * @static
 * @param  {H3IndexInput} origin      Origin hexagon index
 * @param  {H3IndexInput} destination Destination hexagon index
 * @return {boolean}             Whether the hexagons share an edge
 * @throws {H3Error}             If the input is invalid
 */
function areNeighborCells(origin, destination) {
  var ref = h3IndexToSplitLong(origin);
  var oLower = ref[0];
  var oUpper = ref[1];
  var ref$1 = h3IndexToSplitLong(destination);
  var dLower = ref$1[0];
  var dUpper = ref$1[1];
  var out = libh3._malloc(SZ_INT);
  try {
    throwIfError(H3.areNeighborCells(oLower, oUpper, dLower, dUpper, out));
    return readBooleanFromPointer(out);
  } finally {
    libh3._free(out);
  }
}

/**
 * Get an H3 index representing a unidirectional edge for a given origin and destination
 * @static
 * @param  {H3IndexInput} origin      Origin hexagon index
 * @param  {H3IndexInput} destination Destination hexagon index
 * @return {H3Index}             H3 index of the edge, or null if no edge is shared
 * @throws {H3Error}             If the input is invalid
 */
function cellsToDirectedEdge(origin, destination) {
  var ref = h3IndexToSplitLong(origin);
  var oLower = ref[0];
  var oUpper = ref[1];
  var ref$1 = h3IndexToSplitLong(destination);
  var dLower = ref$1[0];
  var dUpper = ref$1[1];
  var h3Index = libh3._malloc(SZ_H3INDEX);
  try {
    throwIfError(H3.cellsToDirectedEdge(oLower, oUpper, dLower, dUpper, h3Index));
    return validateH3Index(readH3IndexFromPointer(h3Index));
  } finally {
    libh3._free(h3Index);
  }
}

/**
 * Get the origin hexagon from an H3 index representing a unidirectional edge
 * @static
 * @param  {H3IndexInput} edgeIndex H3 index of the edge
 * @return {H3Index}           H3 index of the edge origin
 * @throws {H3Error}           If the input is invalid
 */
function getDirectedEdgeOrigin(edgeIndex) {
  var ref = h3IndexToSplitLong(edgeIndex);
  var lower = ref[0];
  var upper = ref[1];
  var h3Index = libh3._malloc(SZ_H3INDEX);
  try {
    throwIfError(H3.getDirectedEdgeOrigin(lower, upper, h3Index));
    return validateH3Index(readH3IndexFromPointer(h3Index));
  } finally {
    libh3._free(h3Index);
  }
}

/**
 * Get the destination hexagon from an H3 index representing a unidirectional edge
 * @static
 * @param  {H3IndexInput} edgeIndex H3 index of the edge
 * @return {H3Index}           H3 index of the edge destination
 * @throws {H3Error}           If the input is invalid
 */
function getDirectedEdgeDestination(edgeIndex) {
  var ref = h3IndexToSplitLong(edgeIndex);
  var lower = ref[0];
  var upper = ref[1];
  var h3Index = libh3._malloc(SZ_H3INDEX);
  try {
    throwIfError(H3.getDirectedEdgeDestination(lower, upper, h3Index));
    return validateH3Index(readH3IndexFromPointer(h3Index));
  } finally {
    libh3._free(h3Index);
  }
}

/**
 * Whether the input is a valid unidirectional edge
 * @static
 * @param  {H3IndexInput} edgeIndex H3 index of the edge
 * @return {boolean}           Whether the index is valid
 */
function isValidDirectedEdge(edgeIndex) {
  var ref = h3IndexToSplitLong(edgeIndex);
  var lower = ref[0];
  var upper = ref[1];
  return Boolean(H3.isValidDirectedEdge(lower, upper));
}

/**
 * Get the [origin, destination] pair represented by a unidirectional edge
 * @static
 * @param  {H3IndexInput} edgeIndex H3 index of the edge
 * @return {H3Index[]}         [origin, destination] pair as H3 indexes
 * @throws {H3Error}           If the input is invalid
 */
function directedEdgeToCells(edgeIndex) {
  var ref = h3IndexToSplitLong(edgeIndex);
  var lower = ref[0];
  var upper = ref[1];
  var count = 2;
  var hexagons = libh3._calloc(count, SZ_H3INDEX);
  try {
    throwIfError(H3.directedEdgeToCells(lower, upper, hexagons));
    return readArrayOfH3Indexes(hexagons, count);
  } finally {
    libh3._free(hexagons);
  }
}

/**
 * Get all of the unidirectional edges with the given H3 index as the origin (i.e. an edge to
 * every neighbor)
 * @static
 * @param  {H3IndexInput} h3Index   H3 index of the origin hexagon
 * @return {H3Index[]}         List of unidirectional edges
 * @throws {H3Error}           If the input is invalid
 */
function originToDirectedEdges(h3Index) {
  var ref = h3IndexToSplitLong(h3Index);
  var lower = ref[0];
  var upper = ref[1];
  var count = 6;
  var edges = libh3._calloc(count, SZ_H3INDEX);
  try {
    throwIfError(H3.originToDirectedEdges(lower, upper, edges));
    return readArrayOfH3Indexes(edges, count);
  } finally {
    libh3._free(edges);
  }
}

/**
 * Get the vertices of a given edge as an array of [lat, lng] points. Note that for edges that
 * cross the edge of an icosahedron face, this may return 3 coordinates.
 * @static
 * @param  {H3IndexInput} edgeIndex        H3 index of the edge
 * @param {boolean} [formatAsGeoJson] Whether to provide GeoJSON output: [lng, lat]
 * @return {CoordPair[]}              Array of geo coordinate pairs
 * @throws {H3Error}                  If the input is invalid
 */
function directedEdgeToBoundary(edgeIndex, formatAsGeoJson) {
  var cellBoundary = libh3._malloc(SZ_CELLBOUNDARY);
  var ref = h3IndexToSplitLong(edgeIndex);
  var lower = ref[0];
  var upper = ref[1];
  try {
    throwIfError(H3.directedEdgeToBoundary(lower, upper, cellBoundary));
    return readCellBoundary(cellBoundary, formatAsGeoJson);
  } finally {
    libh3._free(cellBoundary);
  }
}

/**
 * Get the grid distance between two hex indexes. This function may fail
 * to find the distance between two indexes if they are very far apart or
 * on opposite sides of a pentagon.
 * @static
 * @param  {H3IndexInput} origin      Origin hexagon index
 * @param  {H3IndexInput} destination Destination hexagon index
 * @return {number}          Distance between hexagons
 * @throws {H3Error}         If input is invalid or the distance could not be calculated
 */
function gridDistance(origin, destination) {
  var ref = h3IndexToSplitLong(origin);
  var oLower = ref[0];
  var oUpper = ref[1];
  var ref$1 = h3IndexToSplitLong(destination);
  var dLower = ref$1[0];
  var dUpper = ref$1[1];
  var countPtr = libh3._malloc(SZ_INT64);
  try {
    throwIfError(H3.gridDistance(oLower, oUpper, dLower, dUpper, countPtr));
    return readInt64AsDoubleFromPointer(countPtr);
  } finally {
    libh3._free(countPtr);
  }
}

/**
 * Given two H3 indexes, return the line of indexes between them (inclusive).
 *
 * This function may fail to find the line between two indexes, for
 * example if they are very far apart. It may also fail when finding
 * distances for indexes on opposite sides of a pentagon.
 *
 * Notes:
 *
 *  - The specific output of this function should not be considered stable
 *    across library versions. The only guarantees the library provides are
 *    that the line length will be `h3Distance(start, end) + 1` and that
 *    every index in the line will be a neighbor of the preceding index.
 *  - Lines are drawn in grid space, and may not correspond exactly to either
 *    Cartesian lines or great arcs.
 *
 * @static
 * @param  {H3IndexInput} origin      Origin hexagon index
 * @param  {H3IndexInput} destination Destination hexagon index
 * @return {H3Index[]}           H3 indexes connecting origin and destination
 * @throws {H3Error}             If input is invalid or the line cannot be calculated
 */
function gridPathCells(origin, destination) {
  var ref = h3IndexToSplitLong(origin);
  var oLower = ref[0];
  var oUpper = ref[1];
  var ref$1 = h3IndexToSplitLong(destination);
  var dLower = ref$1[0];
  var dUpper = ref$1[1];
  var countPtr = libh3._malloc(SZ_INT64);
  try {
    throwIfError(H3.gridPathCellsSize(oLower, oUpper, dLower, dUpper, countPtr));
    var count = validateArrayLength(readInt64AsDoubleFromPointer(countPtr));
    var hexagons = libh3._calloc(count, SZ_H3INDEX);
    try {
      H3.gridPathCells(oLower, oUpper, dLower, dUpper, hexagons);
      return readArrayOfH3Indexes(hexagons, count);
    } finally {
      libh3._free(hexagons);
    }
  } finally {
    libh3._free(countPtr);
  }
}
var LOCAL_IJ_DEFAULT_MODE = 0;

/**
 * Produces IJ coordinates for an H3 index anchored by an origin.
 *
 * - The coordinate space used by this function may have deleted
 * regions or warping due to pentagonal distortion.
 * - Coordinates are only comparable if they come from the same
 * origin index.
 * - Failure may occur if the index is too far away from the origin
 * or if the index is on the other side of a pentagon.
 * - This function is experimental, and its output is not guaranteed
 * to be compatible across different versions of H3.
 * @static
 * @param  {H3IndexInput} origin      Origin H3 index
 * @param  {H3IndexInput} destination H3 index for which to find relative coordinates
 * @return {CoordIJ}             Coordinates as an `{i, j}` pair
 * @throws {H3Error}             If the IJ coordinates cannot be calculated
 */
function cellToLocalIj(origin, destination) {
  var ij = libh3._malloc(SZ_COORDIJ);
  try {
    throwIfError(H3.cellToLocalIj.apply(H3, h3IndexToSplitLong(origin).concat( h3IndexToSplitLong(destination), [LOCAL_IJ_DEFAULT_MODE], [ij] )));
    return readCoordIJ(ij);
  } finally {
    libh3._free(ij);
  }
}

/**
 * Produces an H3 index for IJ coordinates anchored by an origin.
 *
 * - The coordinate space used by this function may have deleted
 * regions or warping due to pentagonal distortion.
 * - Coordinates are only comparable if they come from the same
 * origin index.
 * - Failure may occur if the index is too far away from the origin
 * or if the index is on the other side of a pentagon.
 * - This function is experimental, and its output is not guaranteed
 * to be compatible across different versions of H3.
 * @static
 * @param  {H3IndexInput} origin     Origin H3 index
 * @param  {CoordIJ} coords     Coordinates as an `{i, j}` pair
 * @return {H3Index}            H3 index at the relative coordinates
 * @throws {H3Error}            If the H3 index cannot be calculated
 */
function localIjToCell(origin, coords) {
  // Validate input coords
  if (!coords || typeof coords.i !== 'number' || typeof coords.j !== 'number') {
    throw new Error('Coordinates must be provided as an {i, j} object');
  }
  // Allocate memory for the CoordIJ struct and an H3 index to hold the return value
  var ij = libh3._malloc(SZ_COORDIJ);
  var out = libh3._malloc(SZ_H3INDEX);
  storeCoordIJ(ij, coords);
  try {
    throwIfError(H3.localIjToCell.apply(H3, h3IndexToSplitLong(origin).concat( [ij], [LOCAL_IJ_DEFAULT_MODE], [out] )));
    return validateH3Index(readH3IndexFromPointer(out));
  } finally {
    libh3._free(ij);
    libh3._free(out);
  }
}

// ----------------------------------------------------------------------------
// Public API functions: Distance/area utilities

/**
 * Great circle distance between two geo points. This is not specific to H3,
 * but is implemented in the library and provided here as a convenience.
 * @static
 * @param  {number[]} latLng1 Origin coordinate as [lat, lng]
 * @param  {number[]} latLng2 Destination coordinate as [lat, lng]
 * @param  {string}   unit    Distance unit (either UNITS.m, UNITS.km, or UNITS.rads)
 * @return {number}           Great circle distance
 * @throws {H3Error}          If the unit is invalid
 */
function greatCircleDistance(latLng1, latLng2, unit) {
  var coord1 = storeLatLng(latLng1[0], latLng1[1]);
  var coord2 = storeLatLng(latLng2[0], latLng2[1]);
  var result;
  switch (unit) {
    case UNITS.m:
      result = H3.greatCircleDistanceM(coord1, coord2);
      break;
    case UNITS.km:
      result = H3.greatCircleDistanceKm(coord1, coord2);
      break;
    case UNITS.rads:
      result = H3.greatCircleDistanceRads(coord1, coord2);
      break;
    default:
      result = null;
  }
  libh3._free(coord1);
  libh3._free(coord2);
  if (result === null) {
    throw JSBindingError(E_UNKNOWN_UNIT, unit);
  }
  return result;
}

/**
 * Exact area of a given cell
 * @static
 * @param  {H3IndexInput} h3Index  H3 index of the hexagon to measure
 * @param  {string}  unit     Distance unit (either UNITS.m2, UNITS.km2, or UNITS.rads2)
 * @return {number}           Cell area
 * @throws {H3Error}          If the input is invalid
 */
function cellArea(h3Index, unit) {
  var ref = h3IndexToSplitLong(h3Index);
  var lower = ref[0];
  var upper = ref[1];
  var out = libh3._malloc(SZ_DBL);
  try {
    switch (unit) {
      case UNITS.m2:
        throwIfError(H3.cellAreaM2(lower, upper, out));
        break;
      case UNITS.km2:
        throwIfError(H3.cellAreaKm2(lower, upper, out));
        break;
      case UNITS.rads2:
        throwIfError(H3.cellAreaRads2(lower, upper, out));
        break;
      default:
        throw JSBindingError(E_UNKNOWN_UNIT, unit);
    }
    return readDoubleFromPointer(out);
  } finally {
    libh3._free(out);
  }
}

/**
 * Calculate length of a given unidirectional edge
 * @static
 * @param  {H3IndexInput} edge     H3 index of the edge to measure
 * @param  {string}  unit     Distance unit (either UNITS.m, UNITS.km, or UNITS.rads)
 * @return {number}           Cell area
 * @throws {H3Error}          If the input is invalid
 */
function edgeLength(edge, unit) {
  var ref = h3IndexToSplitLong(edge);
  var lower = ref[0];
  var upper = ref[1];
  var out = libh3._malloc(SZ_DBL);
  try {
    switch (unit) {
      case UNITS.m:
        throwIfError(H3.edgeLengthM(lower, upper, out));
        break;
      case UNITS.km:
        throwIfError(H3.edgeLengthKm(lower, upper, out));
        break;
      case UNITS.rads:
        throwIfError(H3.edgeLengthRads(lower, upper, out));
        break;
      default:
        throw JSBindingError(E_UNKNOWN_UNIT, unit);
    }
    return readDoubleFromPointer(out);
  } finally {
    libh3._free(out);
  }
}

/**
 * Average hexagon area at a given resolution
 * @static
 * @param  {number} res  Hexagon resolution
 * @param  {string} unit Area unit (either UNITS.m2, UNITS.km2, or UNITS.rads2)
 * @return {number}      Average area
 * @throws {H3Error}     If the input is invalid
 */
function getHexagonAreaAvg(res, unit) {
  validateRes(res);
  var out = libh3._malloc(SZ_DBL);
  try {
    switch (unit) {
      case UNITS.m2:
        throwIfError(H3.getHexagonAreaAvgM2(res, out));
        break;
      case UNITS.km2:
        throwIfError(H3.getHexagonAreaAvgKm2(res, out));
        break;
      default:
        throw JSBindingError(E_UNKNOWN_UNIT, unit);
    }
    return readDoubleFromPointer(out);
  } finally {
    libh3._free(out);
  }
}

/**
 * Average hexagon edge length at a given resolution
 * @static
 * @param  {number} res  Hexagon resolution
 * @param  {string} unit Distance unit (either UNITS.m, UNITS.km, or UNITS.rads)
 * @return {number}      Average edge length
 * @throws {H3Error}     If the input is invalid
 */
function getHexagonEdgeLengthAvg(res, unit) {
  validateRes(res);
  var out = libh3._malloc(SZ_DBL);
  try {
    switch (unit) {
      case UNITS.m:
        throwIfError(H3.getHexagonEdgeLengthAvgM(res, out));
        break;
      case UNITS.km:
        throwIfError(H3.getHexagonEdgeLengthAvgKm(res, out));
        break;
      default:
        throw JSBindingError(E_UNKNOWN_UNIT, unit);
    }
    return readDoubleFromPointer(out);
  } finally {
    libh3._free(out);
  }
}

// ----------------------------------------------------------------------------
// Public API functions: Vertex mode

/**
 * Find the index for a vertex of a cell.
 * @static
 * @param {H3IndexInput} h3Index     Cell to find the vertex for
 * @param {number} vertexNum         Number (index) of the vertex to calculate
 * @return {H3Index}     Vertex index
 * @throws {H3Error}     If the input is invalid
 */
function cellToVertex(h3Index, vertexNum) {
  var ref = h3IndexToSplitLong(h3Index);
  var lower = ref[0];
  var upper = ref[1];
  var vertexIndex = libh3._malloc(SZ_H3INDEX);
  try {
    throwIfError(H3.cellToVertex(lower, upper, vertexNum, vertexIndex));
    return validateH3Index(readH3IndexFromPointer(vertexIndex));
  } finally {
    libh3._free(vertexIndex);
  }
}

/**
 * Find the indexes for all vertexes of a cell.
 * @static
 * @param {H3IndexInput} h3Index     Cell to find all vertexes for
 * @return {H3Index[]}   All vertex indexes of this cell
 * @throws {H3Error}     If the input is invalid
 */
function cellToVertexes(h3Index) {
  var ref = h3IndexToSplitLong(h3Index);
  var lower = ref[0];
  var upper = ref[1];
  var maxNumVertexes = 6;
  var vertexIndexes = libh3._calloc(maxNumVertexes, SZ_H3INDEX);
  try {
    throwIfError(H3.cellToVertexes(lower, upper, vertexIndexes));
    return readArrayOfH3Indexes(vertexIndexes, maxNumVertexes);
  } finally {
    libh3._free(vertexIndexes);
  }
}

/**
 * Get the lat, lng of a given vertex
 * @static
 * @param {H3IndexInput} h3Index A vertex index
 * @returns {CoordPair}          Latitude, longitude coordinates of the vertex
 * @throws {H3Error}             If the input is invalid
 */
function vertexToLatLng(h3Index) {
  var latlng = libh3._malloc(SZ_LATLNG);
  var ref = h3IndexToSplitLong(h3Index);
  var lower = ref[0];
  var upper = ref[1];
  try {
    throwIfError(H3.vertexToLatLng(lower, upper, latlng));
    return readLatLng(latlng);
  } finally {
    libh3._free(latlng);
  }
}

/**
 * Returns true if the input is a valid vertex index.
 * @static
 * @param {H3IndexInput} h3Index An index to test for being a vertex index
 * @returns {boolean} True if the index represents a vertex
 */
function isValidVertex(h3Index) {
  var ref = h3IndexToSplitLong(h3Index);
  var lower = ref[0];
  var upper = ref[1];
  return Boolean(H3.isValidVertex(lower, upper));
}

// ----------------------------------------------------------------------------
// Public informational utilities

/**
 * The total count of hexagons in the world at a given resolution. Note that above
 * resolution 8 the exact count cannot be represented in a JavaScript 32-bit number,
 * so consumers should use caution when applying further operations to the output.
 * @static
 * @param  {number} res  Hexagon resolution
 * @return {number}      Count
 * @throws {H3Error}     If the resolution is invalid
 */
function getNumCells(res) {
  validateRes(res);
  var countPtr = libh3._malloc(SZ_INT64);
  try {
    // Get number as a long value
    throwIfError(H3.getNumCells(res, countPtr));
    return readInt64AsDoubleFromPointer(countPtr);
  } finally {
    libh3._free(countPtr);
  }
}

/**
 * Get all H3 indexes at resolution 0. As every index at every resolution > 0 is
 * the descendant of a res 0 index, this can be used with h3ToChildren to iterate
 * over H3 indexes at any resolution.
 * @static
 * @return {H3Index[]}  All H3 indexes at res 0
 */
function getRes0Cells() {
  var count = H3.res0CellCount();
  var hexagons = libh3._malloc(SZ_H3INDEX * count);
  try {
    throwIfError(H3.getRes0Cells(hexagons));
    return readArrayOfH3Indexes(hexagons, count);
  } finally {
    libh3._free(hexagons);
  }
}

/**
 * Get the twelve pentagon indexes at a given resolution.
 * @static
 * @param  {number} res  Hexagon resolution
 * @return {H3Index[]}   All H3 pentagon indexes at res
 * @throws {H3Error}     If the resolution is invalid
 */
function getPentagons(res) {
  validateRes(res);
  var count = H3.pentagonCount();
  var hexagons = libh3._malloc(SZ_H3INDEX * count);
  try {
    throwIfError(H3.getPentagons(res, hexagons));
    return readArrayOfH3Indexes(hexagons, count);
  } finally {
    libh3._free(hexagons);
  }
}

/**
 * Convert degrees to radians
 * @static
 * @param  {number} deg Value in degrees
 * @return {number}     Value in radians
 */
function degsToRads(deg) {
  return deg * Math.PI / 180;
}

/**
 * Convert radians to degrees
 * @static
 * @param  {number} rad Value in radians
 * @return {number}     Value in degrees
 */
function radsToDegs(rad) {
  return rad * 180 / Math.PI;
}

exports.UNITS = UNITS;
exports.POLYGON_TO_CELLS_FLAGS = POLYGON_TO_CELLS_FLAGS;
exports.h3IndexToSplitLong = h3IndexToSplitLong;
exports.splitLongToH3Index = splitLongToH3Index;
exports.isValidCell = isValidCell;
exports.isPentagon = isPentagon;
exports.isResClassIII = isResClassIII;
exports.getBaseCellNumber = getBaseCellNumber;
exports.getIcosahedronFaces = getIcosahedronFaces;
exports.getResolution = getResolution;
exports.latLngToCell = latLngToCell;
exports.cellToLatLng = cellToLatLng;
exports.cellToBoundary = cellToBoundary;
exports.cellToParent = cellToParent;
exports.cellToChildren = cellToChildren;
exports.cellToChildrenSize = cellToChildrenSize;
exports.cellToCenterChild = cellToCenterChild;
exports.cellToChildPos = cellToChildPos;
exports.childPosToCell = childPosToCell;
exports.gridDisk = gridDisk;
exports.gridDiskDistances = gridDiskDistances;
exports.gridRingUnsafe = gridRingUnsafe;
exports.polygonToCells = polygonToCells;
exports.polygonToCellsExperimental = polygonToCellsExperimental;
exports.cellsToMultiPolygon = cellsToMultiPolygon;
exports.compactCells = compactCells;
exports.uncompactCells = uncompactCells;
exports.areNeighborCells = areNeighborCells;
exports.cellsToDirectedEdge = cellsToDirectedEdge;
exports.getDirectedEdgeOrigin = getDirectedEdgeOrigin;
exports.getDirectedEdgeDestination = getDirectedEdgeDestination;
exports.isValidDirectedEdge = isValidDirectedEdge;
exports.directedEdgeToCells = directedEdgeToCells;
exports.originToDirectedEdges = originToDirectedEdges;
exports.directedEdgeToBoundary = directedEdgeToBoundary;
exports.gridDistance = gridDistance;
exports.gridPathCells = gridPathCells;
exports.cellToLocalIj = cellToLocalIj;
exports.localIjToCell = localIjToCell;
exports.greatCircleDistance = greatCircleDistance;
exports.cellArea = cellArea;
exports.edgeLength = edgeLength;
exports.getHexagonAreaAvg = getHexagonAreaAvg;
exports.getHexagonEdgeLengthAvg = getHexagonEdgeLengthAvg;
exports.cellToVertex = cellToVertex;
exports.cellToVertexes = cellToVertexes;
exports.vertexToLatLng = vertexToLatLng;
exports.isValidVertex = isValidVertex;
exports.getNumCells = getNumCells;
exports.getRes0Cells = getRes0Cells;
exports.getPentagons = getPentagons;
exports.degsToRads = degsToRads;
exports.radsToDegs = radsToDegs;
//# sourceMappingURL=h3-js.js.map
