
const h3v4 = require('./h3-js');

module.exports = {
UNITS: h3v4.UNITS,
h3IndexToSplitLong: h3v4.h3IndexToSplitLong,
splitLongToH3Index: h3v4.splitLongToH3Index,
h3IsValid: h3v4.isValidCell,
h3IsPentagon: h3v4.isPentagon,
h3IsResClassIII: h3v4.isResClassIII,
h3GetBaseCell: h3v4.getBaseCellNumber,
h3GetFaces: h3v4.getIcosahedronFaces,
h3GetResolution: h3v4.getResolution,
geoToH3: h3v4.latLngToCell,
h3ToGeo: h3v4.cellToLatLng,
h3ToGeoBoundary: h3v4.cellToBoundary,
h3ToParent: h3v4.cellToParent,
h3ToChildren: h3v4.cellToChildren,
h3ToCenterChild: h3v4.cellToCenterChild,
kRing: h3v4.gridDisk,
kRingDistances: h3v4.gridDiskDistances,
hexRing: h3v4.gridRingUnsafe,
polyfill: h3v4.polygonToCells,
h3SetToMultiPolygon: h3v4.cellsToMultiPolygon,
compact: h3v4.compactCells,
uncompact: h3v4.uncompactCells,
h3IndexesAreNeighbors: h3v4.areNeighborCells,
getH3UnidirectionalEdge: h3v4.cellsToDirectedEdge,
getOriginH3IndexFromUnidirectionalEdge: h3v4.getDirectedEdgeOrigin,
getDestinationH3IndexFromUnidirectionalEdge: h3v4.getDirectedEdgeDestination,
h3UnidirectionalEdgeIsValid: h3v4.isValidDirectedEdge,
getH3IndexesFromUnidirectionalEdge: h3v4.directedEdgeToCells,
getH3UnidirectionalEdgesFromHexagon: h3v4.originToDirectedEdges,
getH3UnidirectionalEdgeBoundary: h3v4.directedEdgeToBoundary,
h3Distance: h3v4.gridDistance,
h3Line: h3v4.gridPathCells,
experimentalH3ToLocalIj: h3v4.cellToLocalIj,
experimentalLocalIjToH3: h3v4.localIjToCell,
pointDist: h3v4.greatCircleDistance,
cellArea: h3v4.cellArea,
exactEdgeLength: h3v4.edgeLength,
hexArea: h3v4.getHexagonAreaAvg,
edgeLength: h3v4.getHexagonEdgeLengthAvg,
numHexagons: h3v4.getNumCells,
getRes0Indexes: h3v4.getRes0Cells,
getPentagonIndexes: h3v4.getPentagons,
degsToRads: h3v4.degsToRads,
radsToDegs: h3v4.radsToDegs
};
