{"name": "three-geojson-geometry", "version": "2.1.1", "description": "ThreeJS geometry for stroking GeoJSON objects on a sphere", "type": "module", "unpkg": "dist/three-geojson-geometry.min.js", "main": "dist/three-geojson-geometry.mjs", "module": "dist/three-geojson-geometry.mjs", "types": "dist/three-geojson-geometry.d.ts", "exports": {"types": "./dist/three-geojson-geometry.d.ts", "umd": "./dist/three-geojson-geometry.min.js", "default": "./dist/three-geojson-geometry.mjs"}, "homepage": "https://github.com/vasturiano/three-geojson-geometry", "repository": {"type": "git", "url": "git+https://github.com/vasturiano/three-geojson-geometry.git"}, "bugs": {"url": "https://github.com/vasturiano/three-geojson-geometry/issues"}, "license": "MIT", "keywords": ["3d", "three", "polygons", "sphere", "stroke", "webgl", "g<PERSON><PERSON><PERSON>"], "author": {"name": "Vasco <PERSON>turiano", "url": "https://github.com/vasturiano"}, "files": ["dist/**/*", "example/**/*"], "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -w -c", "prepare": "npm run build"}, "dependencies": {"d3-geo": "1 - 3", "d3-interpolate": "1 - 3", "earcut": "3"}, "peerDependencies": {"three": ">=0.72.0"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.0", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.2", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-terser": "^0.4.4", "@types/three": ">=0.72.0", "rimraf": "^6.0.1", "rollup": "^4.29.1", "rollup-plugin-dts": "^6.1.1", "typescript": "^5.7.2"}, "engines": {"node": ">=12"}}