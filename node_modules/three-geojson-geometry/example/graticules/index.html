<head>
  <style> body { margin: 0; } </style>

  <script type="importmap">{ "imports": {
    "three": "https://esm.sh/three",
    "three/addons/": "https://esm.sh/three/examples/jsm/"
  }}</script>

<!--  <script type="module">import * as THREE from 'three'; window.THREE = THREE;</script>-->
<!--  <script src="../../dist/three-geojson-geometry.js" defer></script>-->
</head>

<body>
<div id="viz"></div>

<script type="module">
  import GeoJsonGeometry from 'https://esm.sh/three-geojson-geometry?external=three';
  import * as THREE from 'three';
  import { TrackballControls } from 'three/addons/controls/TrackballControls.js?external=three';
  import { geoGraticule10 } from 'https://esm.sh/d3-geo';

  const alt = 50;
  const material = new THREE.LineBasicMaterial({ color: 'blue' });

  const graticuleObj = new THREE.LineSegments(
    new GeoJsonGeometry(geoGraticule10(), alt),
    material
  );

  // Setup renderer
  const renderer = new THREE.WebGLRenderer();
  renderer.setSize(window.innerWidth, window.innerHeight);
  document.getElementById('viz').appendChild(renderer.domElement);

  // Setup scene
  const scene = new THREE.Scene();
  scene.add(graticuleObj);
  scene.add(new THREE.AmbientLight(0xbbbbbb));
  scene.add(new THREE.DirectionalLight(0xffffff, 0.6));

  // Setup camera
  const camera = new THREE.PerspectiveCamera();
  camera.aspect = window.innerWidth/ window.innerHeight;
  camera.updateProjectionMatrix();
  camera.position.z = 200;

  // Add camera controls
  const tbControls = new TrackballControls(camera, renderer.domElement);
  tbControls.minDistance = 1;
  tbControls.rotateSpeed = 5;
  tbControls.zoomSpeed = 0.8;

  // Kick-off renderer
  (function animate() { // IIFE
    // Frame cycle
    tbControls.update();
    renderer.render(scene, camera);
    requestAnimationFrame(animate);
  })();
</script>
</body>