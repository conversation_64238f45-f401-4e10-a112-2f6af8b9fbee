// Version 2.1.1 three-geojson-geometry - https://github.com/vasturiano/three-geojson-geometry
!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n(require("three")):"function"==typeof define&&define.amd?define(["three"],n):(t="undefined"!=typeof globalThis?globalThis:t||self).GeoJsonGeometry=n(t.THREE)}(this,(function(t){"use strict";function n(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=Array(n);e<n;e++)r[e]=t[e];return r}function e(t,n,e){return n=r(n),function(t,n){if(n&&("object"==typeof n||"function"==typeof n))return n;if(void 0!==n)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,o()?Reflect.construct(n,[],r(t).constructor):n.apply(t,e))}function r(t){return r=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},r(t)}function o(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(o=function(){return!!t})()}function i(t,n){return i=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,n){return t.__proto__=n,t},i(t,n)}function u(t,n){return function(t){if(Array.isArray(t))return t}(t)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,o,i,u,a=[],c=!0,f=!1;try{if(i=(e=e.call(t)).next,0===n){if(Object(e)!==e)return;c=!1}else for(;!(c=(r=i.call(e)).done)&&(a.push(r.value),a.length!==n);c=!0);}catch(t){f=!0,o=t}finally{try{if(!c&&null!=e.return&&(u=e.return(),Object(u)!==u))return}finally{if(f)throw o}}return a}}(t,n)||c(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(t){return function(t){if(Array.isArray(t))return n(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||c(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(t,e){if(t){if("string"==typeof t)return n(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(t,e):void 0}}function f(t){const n=[],e=[],r=t[0][0].length;let o=0,i=0;for(const u of t){for(const t of u)for(let e=0;e<r;e++)n.push(t[e]);i&&(o+=i,e.push(o)),i=u.length}return{vertices:n,holes:e,dimensions:r}}class l{constructor(){this._partials=new Float64Array(32),this._n=0}add(t){const n=this._partials;let e=0;for(let r=0;r<this._n&&r<32;r++){const o=n[r],i=t+o,u=Math.abs(t)<Math.abs(o)?t-(i-o):o-(i-t);u&&(n[e++]=u),t=i}return n[e]=t,this._n=e+1,this}valueOf(){const t=this._partials;let n,e,r,o=this._n,i=0;if(o>0){for(i=t[--o];o>0&&(n=i,e=t[--o],i=n+e,r=e-(i-n),!r););o>0&&(r<0&&t[o-1]<0||r>0&&t[o-1]>0)&&(e=2*r,n=i+e,e==n-i&&(i=n))}return i}}var s=Math.PI,h=s/2,d=180/s,p=s/180,y=Math.abs,v=Math.atan2,b=Math.cos,g=Math.sin,m=Math.sqrt;function w(t){return(t=g(t/2))*t}function M(){}function E(t,n){t&&_.hasOwnProperty(t.type)&&_[t.type](t,n)}var S,P,O,A,j={Feature:function(t,n){E(t.geometry,n)},FeatureCollection:function(t,n){for(var e=t.features,r=-1,o=e.length;++r<o;)E(e[r].geometry,n)}},_={Sphere:function(t,n){n.sphere()},Point:function(t,n){t=t.coordinates,n.point(t[0],t[1],t[2])},MultiPoint:function(t,n){for(var e=t.coordinates,r=-1,o=e.length;++r<o;)t=e[r],n.point(t[0],t[1],t[2])},LineString:function(t,n){I(t.coordinates,n,0)},MultiLineString:function(t,n){for(var e=t.coordinates,r=-1,o=e.length;++r<o;)I(e[r],n,0)},Polygon:function(t,n){T(t.coordinates,n)},MultiPolygon:function(t,n){for(var e=t.coordinates,r=-1,o=e.length;++r<o;)T(e[r],n)},GeometryCollection:function(t,n){for(var e=t.geometries,r=-1,o=e.length;++r<o;)E(e[r],n)}};function I(t,n,e){var r,o=-1,i=t.length-e;for(n.lineStart();++o<i;)r=t[o],n.point(r[0],r[1],r[2]);n.lineEnd()}function T(t,n){var e=-1,r=t.length;for(n.polygonStart();++e<r;)I(t[e],n,1);n.polygonEnd()}var G={sphere:M,point:M,lineStart:function(){G.point=x,G.lineEnd=B},lineEnd:M,polygonStart:M,polygonEnd:M};function B(){G.point=G.lineEnd=M}function x(t,n){P=t*=p,O=g(n*=p),A=b(n),G.point=F}function F(t,n){t*=p;var e=g(n*=p),r=b(n),o=y(t-P),i=b(o),u=r*g(o),a=A*e-O*r*i,c=O*e+A*r*i;S.add(v(m(u*u+a*a),c)),P=t,O=e,A=r}function R(t){return S=new l,function(t,n){j.hasOwnProperty(t.type)?j[t.type](t,n):E(t,n)}(t,G),+S}var L=[null,null],C={type:"LineString",coordinates:L};function H(t,n){var e,r=t[0]*p,o=t[1]*p,i=n[0]*p,u=n[1]*p,a=b(o),c=g(o),f=b(u),l=g(u),s=a*b(r),y=a*g(r),M=f*b(i),E=f*g(i),S=2*((e=m(w(u-o)+a*f*w(i-r)))>1?h:e<-1?-h:Math.asin(e)),P=g(S),O=S?function(t){var n=g(t*=S)/P,e=g(S-t)/P,r=e*s+n*M,o=e*y+n*E,i=e*c+n*l;return[v(o,r)*d,v(i,m(r*r+o*o))*d]}:function(){return[r*d,o*d]};return O.distance=S,O}var J=function(){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=[],e=null;return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).forEach((function(r){if(e){var o=180*(s=r,h=e,L[0]=s,L[1]=h,R(C))/Math.PI;if(o>t)for(var i=H(e,r),u=e.length>2||r.length>2?function(t,n){return t=+t,n=+n,function(e){return t*(1-e)+n*e}}(e[2]||0,r[2]||0):null,c=u?function(t){return[].concat(a(i(t)),[u(t)])}:i,f=1/Math.ceil(o/t),l=f;l<1;)n.push(c(l)),l+=f}var s,h;n.push(e=r)})),n},q="undefined"!=typeof window&&window.THREE?window.THREE:{BufferGeometry:t.BufferGeometry,Float32BufferAttribute:t.Float32BufferAttribute},D=(new q.BufferGeometry).setAttribute?"setAttribute":"addAttribute",U=function(t){function n(t){var r,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5;!function(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}(this,n),(r=e(this,n)).type="GeoJsonGeometry",r.parameters={geoJson:t,radius:o,resolution:i};var a=({Point:h,MultiPoint:function(t,n){var e={vertices:[],indices:[]};return t.map((function(t){return h(t,n)})).forEach((function(t){var n=u(t,1)[0];$(e,n)})),[e]},LineString:d,MultiLineString:function(t,n){var e={vertices:[],indices:[]};return t.map((function(t){return d(t,n)})).forEach((function(t){var n=u(t,1)[0];$(e,n)})),[e]},Polygon:p,MultiPolygon:function(t,n){var e={vertices:[],indices:[]},r={vertices:[],indices:[]};t.map((function(t){return p(t,n)})).forEach((function(t){var n=u(t,2),o=n[0],i=n[1];$(e,o),i&&$(r,i)}));var o=[e];return r.vertices.length&&o.push(r),o}}[t.type]||function(){return[]})(t.coordinates,o),c=[],l=[],s=0;function h(t,n){return[{vertices:z(t[1],t[0],n+(t[2]||0)),indices:[]}]}function d(t,n){for(var e=f([J(t,i).map((function(t){var e=u(t,3),r=e[0],o=e[1],i=e[2];return z(o,r,n+(void 0===i?0:i))}))]).vertices,r=Math.round(e.length/3),o=[],a=1;a<r;a++)o.push(a-1,a);return[{vertices:e,indices:o}]}function p(t,n){for(var e=f(t.map((function(t){return J(t,i).map((function(t){var e=u(t,3),r=e[0],o=e[1],i=e[2];return z(o,r,n+(void 0===i?0:i))}))}))),r=e.vertices,o=e.holes,a=o[0]||1/0,c=r.slice(0,3*a),l=r.slice(3*a),s=new Set(o),h=Math.round(r.length/3),d=[],p=[],y=1;y<h;y++)s.has(y)||(y<a?d.push(y-1,y):p.push(y-1-a,y-a));var v=[{indices:d,vertices:c}];return o.length&&v.push({indices:p,vertices:l}),v}return a.forEach((function(t){var n=c.length;$({indices:c,vertices:l},t),r.addGroup(n,c.length-n,s++)})),c.length&&r.setIndex(c),l.length&&r[D]("position",new q.Float32BufferAttribute(l,3)),r}return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),n&&i(t,n)}(n,t),r=n,Object.defineProperty(r,"prototype",{writable:!1}),r;var r}(q.BufferGeometry);function $(t,n){var e=Math.round(t.vertices.length/3);k(t.vertices,n.vertices),k(t.indices,n.indices.map((function(t){return t+e})))}function k(t,n){var e,r=function(t,n){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=c(t))||n){e&&(t=e);var r=0,o=function(){};return{s:o,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,u=!0,a=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return u=t.done,t},e:function(t){a=!0,i=t},f:function(){try{u||null==e.return||e.return()}finally{if(a)throw i}}}}(n);try{for(r.s();!(e=r.n()).done;){var o=e.value;t.push(o)}}catch(t){r.e(t)}finally{r.f()}}function z(t,n){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=(90-t)*Math.PI/180,o=(90-n)*Math.PI/180;return[e*Math.sin(r)*Math.cos(o),e*Math.cos(r),e*Math.sin(r)*Math.sin(o)]}return U}));
