{"version": 3, "file": "three-geojson-geometry.js", "sources": ["../node_modules/earcut/src/earcut.js", "../node_modules/d3-array/src/fsum.js", "../node_modules/d3-geo/src/math.js", "../node_modules/d3-geo/src/noop.js", "../node_modules/d3-geo/src/stream.js", "../node_modules/d3-geo/src/length.js", "../node_modules/d3-geo/src/distance.js", "../node_modules/d3-geo/src/interpolate.js", "../node_modules/d3-interpolate/src/number.js", "../src/interpolateLine.js", "../src/index.js"], "sourcesContent": ["\nexport default function earcut(data, holeIndices, dim = 2) {\n\n    const hasHoles = holeIndices && holeIndices.length;\n    const outerLen = hasHoles ? holeIndices[0] * dim : data.length;\n    let outerNode = linkedList(data, 0, outerLen, dim, true);\n    const triangles = [];\n\n    if (!outerNode || outerNode.next === outerNode.prev) return triangles;\n\n    let minX, minY, invSize;\n\n    if (hasHoles) outerNode = eliminateHoles(data, holeIndices, outerNode, dim);\n\n    // if the shape is not too simple, we'll use z-order curve hash later; calculate polygon bbox\n    if (data.length > 80 * dim) {\n        minX = Infinity;\n        minY = Infinity;\n        let maxX = -Infinity;\n        let maxY = -Infinity;\n\n        for (let i = dim; i < outerLen; i += dim) {\n            const x = data[i];\n            const y = data[i + 1];\n            if (x < minX) minX = x;\n            if (y < minY) minY = y;\n            if (x > maxX) maxX = x;\n            if (y > maxY) maxY = y;\n        }\n\n        // minX, minY and invSize are later used to transform coords into integers for z-order calculation\n        invSize = Math.max(maxX - minX, maxY - minY);\n        invSize = invSize !== 0 ? 32767 / invSize : 0;\n    }\n\n    earcutLinked(outerNode, triangles, dim, minX, minY, invSize, 0);\n\n    return triangles;\n}\n\n// create a circular doubly linked list from polygon points in the specified winding order\nfunction linkedList(data, start, end, dim, clockwise) {\n    let last;\n\n    if (clockwise === (signedArea(data, start, end, dim) > 0)) {\n        for (let i = start; i < end; i += dim) last = insertNode(i / dim | 0, data[i], data[i + 1], last);\n    } else {\n        for (let i = end - dim; i >= start; i -= dim) last = insertNode(i / dim | 0, data[i], data[i + 1], last);\n    }\n\n    if (last && equals(last, last.next)) {\n        removeNode(last);\n        last = last.next;\n    }\n\n    return last;\n}\n\n// eliminate colinear or duplicate points\nfunction filterPoints(start, end) {\n    if (!start) return start;\n    if (!end) end = start;\n\n    let p = start,\n        again;\n    do {\n        again = false;\n\n        if (!p.steiner && (equals(p, p.next) || area(p.prev, p, p.next) === 0)) {\n            removeNode(p);\n            p = end = p.prev;\n            if (p === p.next) break;\n            again = true;\n\n        } else {\n            p = p.next;\n        }\n    } while (again || p !== end);\n\n    return end;\n}\n\n// main ear slicing loop which triangulates a polygon (given as a linked list)\nfunction earcutLinked(ear, triangles, dim, minX, minY, invSize, pass) {\n    if (!ear) return;\n\n    // interlink polygon nodes in z-order\n    if (!pass && invSize) indexCurve(ear, minX, minY, invSize);\n\n    let stop = ear;\n\n    // iterate through ears, slicing them one by one\n    while (ear.prev !== ear.next) {\n        const prev = ear.prev;\n        const next = ear.next;\n\n        if (invSize ? isEarHashed(ear, minX, minY, invSize) : isEar(ear)) {\n            triangles.push(prev.i, ear.i, next.i); // cut off the triangle\n\n            removeNode(ear);\n\n            // skipping the next vertex leads to less sliver triangles\n            ear = next.next;\n            stop = next.next;\n\n            continue;\n        }\n\n        ear = next;\n\n        // if we looped through the whole remaining polygon and can't find any more ears\n        if (ear === stop) {\n            // try filtering points and slicing again\n            if (!pass) {\n                earcutLinked(filterPoints(ear), triangles, dim, minX, minY, invSize, 1);\n\n            // if this didn't work, try curing all small self-intersections locally\n            } else if (pass === 1) {\n                ear = cureLocalIntersections(filterPoints(ear), triangles);\n                earcutLinked(ear, triangles, dim, minX, minY, invSize, 2);\n\n            // as a last resort, try splitting the remaining polygon into two\n            } else if (pass === 2) {\n                splitEarcut(ear, triangles, dim, minX, minY, invSize);\n            }\n\n            break;\n        }\n    }\n}\n\n// check whether a polygon node forms a valid ear with adjacent nodes\nfunction isEar(ear) {\n    const a = ear.prev,\n        b = ear,\n        c = ear.next;\n\n    if (area(a, b, c) >= 0) return false; // reflex, can't be an ear\n\n    // now make sure we don't have other points inside the potential ear\n    const ax = a.x, bx = b.x, cx = c.x, ay = a.y, by = b.y, cy = c.y;\n\n    // triangle bbox\n    const x0 = Math.min(ax, bx, cx),\n        y0 = Math.min(ay, by, cy),\n        x1 = Math.max(ax, bx, cx),\n        y1 = Math.max(ay, by, cy);\n\n    let p = c.next;\n    while (p !== a) {\n        if (p.x >= x0 && p.x <= x1 && p.y >= y0 && p.y <= y1 &&\n            pointInTriangleExceptFirst(ax, ay, bx, by, cx, cy, p.x, p.y) &&\n            area(p.prev, p, p.next) >= 0) return false;\n        p = p.next;\n    }\n\n    return true;\n}\n\nfunction isEarHashed(ear, minX, minY, invSize) {\n    const a = ear.prev,\n        b = ear,\n        c = ear.next;\n\n    if (area(a, b, c) >= 0) return false; // reflex, can't be an ear\n\n    const ax = a.x, bx = b.x, cx = c.x, ay = a.y, by = b.y, cy = c.y;\n\n    // triangle bbox\n    const x0 = Math.min(ax, bx, cx),\n        y0 = Math.min(ay, by, cy),\n        x1 = Math.max(ax, bx, cx),\n        y1 = Math.max(ay, by, cy);\n\n    // z-order range for the current triangle bbox;\n    const minZ = zOrder(x0, y0, minX, minY, invSize),\n        maxZ = zOrder(x1, y1, minX, minY, invSize);\n\n    let p = ear.prevZ,\n        n = ear.nextZ;\n\n    // look for points inside the triangle in both directions\n    while (p && p.z >= minZ && n && n.z <= maxZ) {\n        if (p.x >= x0 && p.x <= x1 && p.y >= y0 && p.y <= y1 && p !== a && p !== c &&\n            pointInTriangleExceptFirst(ax, ay, bx, by, cx, cy, p.x, p.y) && area(p.prev, p, p.next) >= 0) return false;\n        p = p.prevZ;\n\n        if (n.x >= x0 && n.x <= x1 && n.y >= y0 && n.y <= y1 && n !== a && n !== c &&\n            pointInTriangleExceptFirst(ax, ay, bx, by, cx, cy, n.x, n.y) && area(n.prev, n, n.next) >= 0) return false;\n        n = n.nextZ;\n    }\n\n    // look for remaining points in decreasing z-order\n    while (p && p.z >= minZ) {\n        if (p.x >= x0 && p.x <= x1 && p.y >= y0 && p.y <= y1 && p !== a && p !== c &&\n            pointInTriangleExceptFirst(ax, ay, bx, by, cx, cy, p.x, p.y) && area(p.prev, p, p.next) >= 0) return false;\n        p = p.prevZ;\n    }\n\n    // look for remaining points in increasing z-order\n    while (n && n.z <= maxZ) {\n        if (n.x >= x0 && n.x <= x1 && n.y >= y0 && n.y <= y1 && n !== a && n !== c &&\n            pointInTriangleExceptFirst(ax, ay, bx, by, cx, cy, n.x, n.y) && area(n.prev, n, n.next) >= 0) return false;\n        n = n.nextZ;\n    }\n\n    return true;\n}\n\n// go through all polygon nodes and cure small local self-intersections\nfunction cureLocalIntersections(start, triangles) {\n    let p = start;\n    do {\n        const a = p.prev,\n            b = p.next.next;\n\n        if (!equals(a, b) && intersects(a, p, p.next, b) && locallyInside(a, b) && locallyInside(b, a)) {\n\n            triangles.push(a.i, p.i, b.i);\n\n            // remove two nodes involved\n            removeNode(p);\n            removeNode(p.next);\n\n            p = start = b;\n        }\n        p = p.next;\n    } while (p !== start);\n\n    return filterPoints(p);\n}\n\n// try splitting polygon into two and triangulate them independently\nfunction splitEarcut(start, triangles, dim, minX, minY, invSize) {\n    // look for a valid diagonal that divides the polygon into two\n    let a = start;\n    do {\n        let b = a.next.next;\n        while (b !== a.prev) {\n            if (a.i !== b.i && isValidDiagonal(a, b)) {\n                // split the polygon in two by the diagonal\n                let c = splitPolygon(a, b);\n\n                // filter colinear points around the cuts\n                a = filterPoints(a, a.next);\n                c = filterPoints(c, c.next);\n\n                // run earcut on each half\n                earcutLinked(a, triangles, dim, minX, minY, invSize, 0);\n                earcutLinked(c, triangles, dim, minX, minY, invSize, 0);\n                return;\n            }\n            b = b.next;\n        }\n        a = a.next;\n    } while (a !== start);\n}\n\n// link every hole into the outer loop, producing a single-ring polygon without holes\nfunction eliminateHoles(data, holeIndices, outerNode, dim) {\n    const queue = [];\n\n    for (let i = 0, len = holeIndices.length; i < len; i++) {\n        const start = holeIndices[i] * dim;\n        const end = i < len - 1 ? holeIndices[i + 1] * dim : data.length;\n        const list = linkedList(data, start, end, dim, false);\n        if (list === list.next) list.steiner = true;\n        queue.push(getLeftmost(list));\n    }\n\n    queue.sort(compareXYSlope);\n\n    // process holes from left to right\n    for (let i = 0; i < queue.length; i++) {\n        outerNode = eliminateHole(queue[i], outerNode);\n    }\n\n    return outerNode;\n}\n\nfunction compareXYSlope(a, b) {\n    let result = a.x - b.x;\n    // when the left-most point of 2 holes meet at a vertex, sort the holes counterclockwise so that when we find\n    // the bridge to the outer shell is always the point that they meet at.\n    if (result === 0) {\n        result = a.y - b.y;\n        if (result === 0) {\n            const aSlope = (a.next.y - a.y) / (a.next.x - a.x);\n            const bSlope = (b.next.y - b.y) / (b.next.x - b.x);\n            result = aSlope - bSlope;\n        }\n    }\n    return result;\n}\n\n// find a bridge between vertices that connects hole with an outer ring and and link it\nfunction eliminateHole(hole, outerNode) {\n    const bridge = findHoleBridge(hole, outerNode);\n    if (!bridge) {\n        return outerNode;\n    }\n\n    const bridgeReverse = splitPolygon(bridge, hole);\n\n    // filter collinear points around the cuts\n    filterPoints(bridgeReverse, bridgeReverse.next);\n    return filterPoints(bridge, bridge.next);\n}\n\n// David Eberly's algorithm for finding a bridge between hole and outer polygon\nfunction findHoleBridge(hole, outerNode) {\n    let p = outerNode;\n    const hx = hole.x;\n    const hy = hole.y;\n    let qx = -Infinity;\n    let m;\n\n    // find a segment intersected by a ray from the hole's leftmost point to the left;\n    // segment's endpoint with lesser x will be potential connection point\n    // unless they intersect at a vertex, then choose the vertex\n    if (equals(hole, p)) return p;\n    do {\n        if (equals(hole, p.next)) return p.next;\n        else if (hy <= p.y && hy >= p.next.y && p.next.y !== p.y) {\n            const x = p.x + (hy - p.y) * (p.next.x - p.x) / (p.next.y - p.y);\n            if (x <= hx && x > qx) {\n                qx = x;\n                m = p.x < p.next.x ? p : p.next;\n                if (x === hx) return m; // hole touches outer segment; pick leftmost endpoint\n            }\n        }\n        p = p.next;\n    } while (p !== outerNode);\n\n    if (!m) return null;\n\n    // look for points inside the triangle of hole point, segment intersection and endpoint;\n    // if there are no points found, we have a valid connection;\n    // otherwise choose the point of the minimum angle with the ray as connection point\n\n    const stop = m;\n    const mx = m.x;\n    const my = m.y;\n    let tanMin = Infinity;\n\n    p = m;\n\n    do {\n        if (hx >= p.x && p.x >= mx && hx !== p.x &&\n                pointInTriangle(hy < my ? hx : qx, hy, mx, my, hy < my ? qx : hx, hy, p.x, p.y)) {\n\n            const tan = Math.abs(hy - p.y) / (hx - p.x); // tangential\n\n            if (locallyInside(p, hole) &&\n                (tan < tanMin || (tan === tanMin && (p.x > m.x || (p.x === m.x && sectorContainsSector(m, p)))))) {\n                m = p;\n                tanMin = tan;\n            }\n        }\n\n        p = p.next;\n    } while (p !== stop);\n\n    return m;\n}\n\n// whether sector in vertex m contains sector in vertex p in the same coordinates\nfunction sectorContainsSector(m, p) {\n    return area(m.prev, m, p.prev) < 0 && area(p.next, m, m.next) < 0;\n}\n\n// interlink polygon nodes in z-order\nfunction indexCurve(start, minX, minY, invSize) {\n    let p = start;\n    do {\n        if (p.z === 0) p.z = zOrder(p.x, p.y, minX, minY, invSize);\n        p.prevZ = p.prev;\n        p.nextZ = p.next;\n        p = p.next;\n    } while (p !== start);\n\n    p.prevZ.nextZ = null;\n    p.prevZ = null;\n\n    sortLinked(p);\n}\n\n// Simon Tatham's linked list merge sort algorithm\n// http://www.chiark.greenend.org.uk/~sgtatham/algorithms/listsort.html\nfunction sortLinked(list) {\n    let numMerges;\n    let inSize = 1;\n\n    do {\n        let p = list;\n        let e;\n        list = null;\n        let tail = null;\n        numMerges = 0;\n\n        while (p) {\n            numMerges++;\n            let q = p;\n            let pSize = 0;\n            for (let i = 0; i < inSize; i++) {\n                pSize++;\n                q = q.nextZ;\n                if (!q) break;\n            }\n            let qSize = inSize;\n\n            while (pSize > 0 || (qSize > 0 && q)) {\n\n                if (pSize !== 0 && (qSize === 0 || !q || p.z <= q.z)) {\n                    e = p;\n                    p = p.nextZ;\n                    pSize--;\n                } else {\n                    e = q;\n                    q = q.nextZ;\n                    qSize--;\n                }\n\n                if (tail) tail.nextZ = e;\n                else list = e;\n\n                e.prevZ = tail;\n                tail = e;\n            }\n\n            p = q;\n        }\n\n        tail.nextZ = null;\n        inSize *= 2;\n\n    } while (numMerges > 1);\n\n    return list;\n}\n\n// z-order of a point given coords and inverse of the longer side of data bbox\nfunction zOrder(x, y, minX, minY, invSize) {\n    // coords are transformed into non-negative 15-bit integer range\n    x = (x - minX) * invSize | 0;\n    y = (y - minY) * invSize | 0;\n\n    x = (x | (x << 8)) & 0x00FF00FF;\n    x = (x | (x << 4)) & 0x0F0F0F0F;\n    x = (x | (x << 2)) & 0x33333333;\n    x = (x | (x << 1)) & 0x55555555;\n\n    y = (y | (y << 8)) & 0x00FF00FF;\n    y = (y | (y << 4)) & 0x0F0F0F0F;\n    y = (y | (y << 2)) & 0x33333333;\n    y = (y | (y << 1)) & 0x55555555;\n\n    return x | (y << 1);\n}\n\n// find the leftmost node of a polygon ring\nfunction getLeftmost(start) {\n    let p = start,\n        leftmost = start;\n    do {\n        if (p.x < leftmost.x || (p.x === leftmost.x && p.y < leftmost.y)) leftmost = p;\n        p = p.next;\n    } while (p !== start);\n\n    return leftmost;\n}\n\n// check if a point lies within a convex triangle\nfunction pointInTriangle(ax, ay, bx, by, cx, cy, px, py) {\n    return (cx - px) * (ay - py) >= (ax - px) * (cy - py) &&\n           (ax - px) * (by - py) >= (bx - px) * (ay - py) &&\n           (bx - px) * (cy - py) >= (cx - px) * (by - py);\n}\n\n// check if a point lies within a convex triangle but false if its equal to the first point of the triangle\nfunction pointInTriangleExceptFirst(ax, ay, bx, by, cx, cy, px, py) {\n    return !(ax === px && ay === py) && pointInTriangle(ax, ay, bx, by, cx, cy, px, py);\n}\n\n// check if a diagonal between two polygon nodes is valid (lies in polygon interior)\nfunction isValidDiagonal(a, b) {\n    return a.next.i !== b.i && a.prev.i !== b.i && !intersectsPolygon(a, b) && // dones't intersect other edges\n           (locallyInside(a, b) && locallyInside(b, a) && middleInside(a, b) && // locally visible\n            (area(a.prev, a, b.prev) || area(a, b.prev, b)) || // does not create opposite-facing sectors\n            equals(a, b) && area(a.prev, a, a.next) > 0 && area(b.prev, b, b.next) > 0); // special zero-length case\n}\n\n// signed area of a triangle\nfunction area(p, q, r) {\n    return (q.y - p.y) * (r.x - q.x) - (q.x - p.x) * (r.y - q.y);\n}\n\n// check if two points are equal\nfunction equals(p1, p2) {\n    return p1.x === p2.x && p1.y === p2.y;\n}\n\n// check if two segments intersect\nfunction intersects(p1, q1, p2, q2) {\n    const o1 = sign(area(p1, q1, p2));\n    const o2 = sign(area(p1, q1, q2));\n    const o3 = sign(area(p2, q2, p1));\n    const o4 = sign(area(p2, q2, q1));\n\n    if (o1 !== o2 && o3 !== o4) return true; // general case\n\n    if (o1 === 0 && onSegment(p1, p2, q1)) return true; // p1, q1 and p2 are collinear and p2 lies on p1q1\n    if (o2 === 0 && onSegment(p1, q2, q1)) return true; // p1, q1 and q2 are collinear and q2 lies on p1q1\n    if (o3 === 0 && onSegment(p2, p1, q2)) return true; // p2, q2 and p1 are collinear and p1 lies on p2q2\n    if (o4 === 0 && onSegment(p2, q1, q2)) return true; // p2, q2 and q1 are collinear and q1 lies on p2q2\n\n    return false;\n}\n\n// for collinear points p, q, r, check if point q lies on segment pr\nfunction onSegment(p, q, r) {\n    return q.x <= Math.max(p.x, r.x) && q.x >= Math.min(p.x, r.x) && q.y <= Math.max(p.y, r.y) && q.y >= Math.min(p.y, r.y);\n}\n\nfunction sign(num) {\n    return num > 0 ? 1 : num < 0 ? -1 : 0;\n}\n\n// check if a polygon diagonal intersects any polygon segments\nfunction intersectsPolygon(a, b) {\n    let p = a;\n    do {\n        if (p.i !== a.i && p.next.i !== a.i && p.i !== b.i && p.next.i !== b.i &&\n                intersects(p, p.next, a, b)) return true;\n        p = p.next;\n    } while (p !== a);\n\n    return false;\n}\n\n// check if a polygon diagonal is locally inside the polygon\nfunction locallyInside(a, b) {\n    return area(a.prev, a, a.next) < 0 ?\n        area(a, b, a.next) >= 0 && area(a, a.prev, b) >= 0 :\n        area(a, b, a.prev) < 0 || area(a, a.next, b) < 0;\n}\n\n// check if the middle point of a polygon diagonal is inside the polygon\nfunction middleInside(a, b) {\n    let p = a;\n    let inside = false;\n    const px = (a.x + b.x) / 2;\n    const py = (a.y + b.y) / 2;\n    do {\n        if (((p.y > py) !== (p.next.y > py)) && p.next.y !== p.y &&\n                (px < (p.next.x - p.x) * (py - p.y) / (p.next.y - p.y) + p.x))\n            inside = !inside;\n        p = p.next;\n    } while (p !== a);\n\n    return inside;\n}\n\n// link two polygon vertices with a bridge; if the vertices belong to the same ring, it splits polygon into two;\n// if one belongs to the outer ring and another to a hole, it merges it into a single ring\nfunction splitPolygon(a, b) {\n    const a2 = createNode(a.i, a.x, a.y),\n        b2 = createNode(b.i, b.x, b.y),\n        an = a.next,\n        bp = b.prev;\n\n    a.next = b;\n    b.prev = a;\n\n    a2.next = an;\n    an.prev = a2;\n\n    b2.next = a2;\n    a2.prev = b2;\n\n    bp.next = b2;\n    b2.prev = bp;\n\n    return b2;\n}\n\n// create a node and optionally link it with previous one (in a circular doubly linked list)\nfunction insertNode(i, x, y, last) {\n    const p = createNode(i, x, y);\n\n    if (!last) {\n        p.prev = p;\n        p.next = p;\n\n    } else {\n        p.next = last.next;\n        p.prev = last;\n        last.next.prev = p;\n        last.next = p;\n    }\n    return p;\n}\n\nfunction removeNode(p) {\n    p.next.prev = p.prev;\n    p.prev.next = p.next;\n\n    if (p.prevZ) p.prevZ.nextZ = p.nextZ;\n    if (p.nextZ) p.nextZ.prevZ = p.prevZ;\n}\n\nfunction createNode(i, x, y) {\n    return {\n        i, // vertex index in coordinates array\n        x, y, // vertex coordinates\n        prev: null, // previous and next vertex nodes in a polygon ring\n        next: null,\n        z: 0, // z-order curve value\n        prevZ: null, // previous and next nodes in z-order\n        nextZ: null,\n        steiner: false // indicates whether this is a steiner point\n    };\n}\n\n// return a percentage difference between the polygon area and its triangulation area;\n// used to verify correctness of triangulation\nexport function deviation(data, holeIndices, dim, triangles) {\n    const hasHoles = holeIndices && holeIndices.length;\n    const outerLen = hasHoles ? holeIndices[0] * dim : data.length;\n\n    let polygonArea = Math.abs(signedArea(data, 0, outerLen, dim));\n    if (hasHoles) {\n        for (let i = 0, len = holeIndices.length; i < len; i++) {\n            const start = holeIndices[i] * dim;\n            const end = i < len - 1 ? holeIndices[i + 1] * dim : data.length;\n            polygonArea -= Math.abs(signedArea(data, start, end, dim));\n        }\n    }\n\n    let trianglesArea = 0;\n    for (let i = 0; i < triangles.length; i += 3) {\n        const a = triangles[i] * dim;\n        const b = triangles[i + 1] * dim;\n        const c = triangles[i + 2] * dim;\n        trianglesArea += Math.abs(\n            (data[a] - data[c]) * (data[b + 1] - data[a + 1]) -\n            (data[a] - data[b]) * (data[c + 1] - data[a + 1]));\n    }\n\n    return polygonArea === 0 && trianglesArea === 0 ? 0 :\n        Math.abs((trianglesArea - polygonArea) / polygonArea);\n}\n\nfunction signedArea(data, start, end, dim) {\n    let sum = 0;\n    for (let i = start, j = end - dim; i < end; i += dim) {\n        sum += (data[j] - data[i]) * (data[i + 1] + data[j + 1]);\n        j = i;\n    }\n    return sum;\n}\n\n// turn a polygon in a multi-dimensional array form (e.g. as in GeoJSON) into a form Earcut accepts\nexport function flatten(data) {\n    const vertices = [];\n    const holes = [];\n    const dimensions = data[0][0].length;\n    let holeIndex = 0;\n    let prevLen = 0;\n\n    for (const ring of data) {\n        for (const p of ring) {\n            for (let d = 0; d < dimensions; d++) vertices.push(p[d]);\n        }\n        if (prevLen) {\n            holeIndex += prevLen;\n            holes.push(holeIndex);\n        }\n        prevLen = ring.length;\n    }\n    return {vertices, holes, dimensions};\n}\n", "// https://github.com/python/cpython/blob/a74eea238f5baba15797e2e8b570d153bc8690a7/Modules/mathmodule.c#L1423\nexport class Adder {\n  constructor() {\n    this._partials = new Float64Array(32);\n    this._n = 0;\n  }\n  add(x) {\n    const p = this._partials;\n    let i = 0;\n    for (let j = 0; j < this._n && j < 32; j++) {\n      const y = p[j],\n        hi = x + y,\n        lo = Math.abs(x) < Math.abs(y) ? x - (hi - y) : y - (hi - x);\n      if (lo) p[i++] = lo;\n      x = hi;\n    }\n    p[i] = x;\n    this._n = i + 1;\n    return this;\n  }\n  valueOf() {\n    const p = this._partials;\n    let n = this._n, x, y, lo, hi = 0;\n    if (n > 0) {\n      hi = p[--n];\n      while (n > 0) {\n        x = hi;\n        y = p[--n];\n        hi = x + y;\n        lo = y - (hi - x);\n        if (lo) break;\n      }\n      if (n > 0 && ((lo < 0 && p[n - 1] < 0) || (lo > 0 && p[n - 1] > 0))) {\n        y = lo * 2;\n        x = hi + y;\n        if (y == x - hi) hi = x;\n      }\n    }\n    return hi;\n  }\n}\n\nexport function fsum(values, valueof) {\n  const adder = new Adder();\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        adder.add(value);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        adder.add(value);\n      }\n    }\n  }\n  return +adder;\n}\n\nexport function fcumsum(values, valueof) {\n  const adder = new Adder();\n  let index = -1;\n  return Float64Array.from(values, valueof === undefined\n      ? v => adder.add(+v || 0)\n      : v => adder.add(+valueof(v, ++index, values) || 0)\n  );\n}\n", "export var epsilon = 1e-6;\nexport var epsilon2 = 1e-12;\nexport var pi = Math.PI;\nexport var halfPi = pi / 2;\nexport var quarterPi = pi / 4;\nexport var tau = pi * 2;\n\nexport var degrees = 180 / pi;\nexport var radians = pi / 180;\n\nexport var abs = Math.abs;\nexport var atan = Math.atan;\nexport var atan2 = Math.atan2;\nexport var cos = Math.cos;\nexport var ceil = Math.ceil;\nexport var exp = Math.exp;\nexport var floor = Math.floor;\nexport var hypot = Math.hypot;\nexport var log = Math.log;\nexport var pow = Math.pow;\nexport var sin = Math.sin;\nexport var sign = Math.sign || function(x) { return x > 0 ? 1 : x < 0 ? -1 : 0; };\nexport var sqrt = Math.sqrt;\nexport var tan = Math.tan;\n\nexport function acos(x) {\n  return x > 1 ? 0 : x < -1 ? pi : Math.acos(x);\n}\n\nexport function asin(x) {\n  return x > 1 ? halfPi : x < -1 ? -halfPi : Math.asin(x);\n}\n\nexport function haversin(x) {\n  return (x = sin(x / 2)) * x;\n}\n", "export default function noop() {}\n", "function streamGeometry(geometry, stream) {\n  if (geometry && streamGeometryType.hasOwnProperty(geometry.type)) {\n    streamGeometryType[geometry.type](geometry, stream);\n  }\n}\n\nvar streamObjectType = {\n  Feature: function(object, stream) {\n    streamGeometry(object.geometry, stream);\n  },\n  FeatureCollection: function(object, stream) {\n    var features = object.features, i = -1, n = features.length;\n    while (++i < n) streamGeometry(features[i].geometry, stream);\n  }\n};\n\nvar streamGeometryType = {\n  Sphere: function(object, stream) {\n    stream.sphere();\n  },\n  Point: function(object, stream) {\n    object = object.coordinates;\n    stream.point(object[0], object[1], object[2]);\n  },\n  MultiPoint: function(object, stream) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) object = coordinates[i], stream.point(object[0], object[1], object[2]);\n  },\n  LineString: function(object, stream) {\n    streamLine(object.coordinates, stream, 0);\n  },\n  MultiLineString: function(object, stream) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) streamLine(coordinates[i], stream, 0);\n  },\n  Polygon: function(object, stream) {\n    streamPolygon(object.coordinates, stream);\n  },\n  MultiPolygon: function(object, stream) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) streamPolygon(coordinates[i], stream);\n  },\n  GeometryCollection: function(object, stream) {\n    var geometries = object.geometries, i = -1, n = geometries.length;\n    while (++i < n) streamGeometry(geometries[i], stream);\n  }\n};\n\nfunction streamLine(coordinates, stream, closed) {\n  var i = -1, n = coordinates.length - closed, coordinate;\n  stream.lineStart();\n  while (++i < n) coordinate = coordinates[i], stream.point(coordinate[0], coordinate[1], coordinate[2]);\n  stream.lineEnd();\n}\n\nfunction streamPolygon(coordinates, stream) {\n  var i = -1, n = coordinates.length;\n  stream.polygonStart();\n  while (++i < n) streamLine(coordinates[i], stream, 1);\n  stream.polygonEnd();\n}\n\nexport default function(object, stream) {\n  if (object && streamObjectType.hasOwnProperty(object.type)) {\n    streamObjectType[object.type](object, stream);\n  } else {\n    streamGeometry(object, stream);\n  }\n}\n", "import {Adder} from \"d3-array\";\nimport {abs, atan2, cos, radians, sin, sqrt} from \"./math.js\";\nimport noop from \"./noop.js\";\nimport stream from \"./stream.js\";\n\nvar lengthSum,\n    lambda0,\n    sinPhi0,\n    cosPhi0;\n\nvar lengthStream = {\n  sphere: noop,\n  point: noop,\n  lineStart: lengthLineStart,\n  lineEnd: noop,\n  polygonStart: noop,\n  polygonEnd: noop\n};\n\nfunction lengthLineStart() {\n  lengthStream.point = lengthPointFirst;\n  lengthStream.lineEnd = lengthLineEnd;\n}\n\nfunction lengthLineEnd() {\n  lengthStream.point = lengthStream.lineEnd = noop;\n}\n\nfunction lengthPointFirst(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  lambda0 = lambda, sinPhi0 = sin(phi), cosPhi0 = cos(phi);\n  lengthStream.point = lengthPoint;\n}\n\nfunction lengthPoint(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  var sinPhi = sin(phi),\n      cosPhi = cos(phi),\n      delta = abs(lambda - lambda0),\n      cosDelta = cos(delta),\n      sinDelta = sin(delta),\n      x = cosPhi * sinDelta,\n      y = cosPhi0 * sinPhi - sinPhi0 * cosPhi * cosDelta,\n      z = sinPhi0 * sinPhi + cosPhi0 * cosPhi * cosDelta;\n  lengthSum.add(atan2(sqrt(x * x + y * y), z));\n  lambda0 = lambda, sinPhi0 = sinPhi, cosPhi0 = cosPhi;\n}\n\nexport default function(object) {\n  lengthSum = new Adder();\n  stream(object, lengthStream);\n  return +lengthSum;\n}\n", "import length from \"./length.js\";\n\nvar coordinates = [null, null],\n    object = {type: \"LineString\", coordinates: coordinates};\n\nexport default function(a, b) {\n  coordinates[0] = a;\n  coordinates[1] = b;\n  return length(object);\n}\n", "import {asin, atan2, cos, degrees, haversin, radians, sin, sqrt} from \"./math.js\";\n\nexport default function(a, b) {\n  var x0 = a[0] * radians,\n      y0 = a[1] * radians,\n      x1 = b[0] * radians,\n      y1 = b[1] * radians,\n      cy0 = cos(y0),\n      sy0 = sin(y0),\n      cy1 = cos(y1),\n      sy1 = sin(y1),\n      kx0 = cy0 * cos(x0),\n      ky0 = cy0 * sin(x0),\n      kx1 = cy1 * cos(x1),\n      ky1 = cy1 * sin(x1),\n      d = 2 * asin(sqrt(haversin(y1 - y0) + cy0 * cy1 * haversin(x1 - x0))),\n      k = sin(d);\n\n  var interpolate = d ? function(t) {\n    var B = sin(t *= d) / k,\n        A = sin(d - t) / k,\n        x = A * kx0 + B * kx1,\n        y = A * ky0 + B * ky1,\n        z = A * sy0 + B * sy1;\n    return [\n      atan2(y, x) * degrees,\n      atan2(z, sqrt(x * x + y * y)) * degrees\n    ];\n  } : function() {\n    return [x0 * degrees, y0 * degrees];\n  };\n\n  interpolate.distance = d;\n\n  return interpolate;\n}\n", "export default function(a, b) {\n  return a = +a, b = +b, function(t) {\n    return a * (1 - t) + b * t;\n  };\n}\n", "import { geoDistance, geoInterpolate } from 'd3-geo';\nimport { interpolateNumber } from 'd3-interpolate';\n\nconst interpolateLine = (lineCoords = [], maxDegDistance = 1) => {\n  const result = [];\n\n  let prevPnt = null;\n  lineCoords.forEach(pnt => {\n    if (prevPnt) {\n      const dist = geoDistance(pnt, prevPnt) * 180 / Math.PI;\n      if (dist > maxDegDistance) {\n        const geoInterpol = geoInterpolate(prevPnt, pnt);\n        const altInterpol = prevPnt.length > 2 || pnt.length > 2 ? interpolateNumber(prevPnt[2] || 0, pnt[2] || 0) : null;\n        const interpol = altInterpol ? t => [...geoInterpol(t), altInterpol(t)]: geoInterpol;\n\n        const tStep = 1 / Math.ceil(dist / maxDegDistance);\n\n        let t = tStep;\n        while (t < 1) {\n          result.push(interpol(t));\n          t += tStep;\n        }\n      }\n    }\n\n    result.push(prevPnt = pnt);\n  });\n\n  return result;\n};\n\nexport default interpolateLine;\n", "import {\n  BufferGeo<PERSON>,\n  Float32BufferAttribute\n} from 'three';\n\nconst THREE = (typeof window !== 'undefined') && window.THREE\n  ? window.THREE // Prefer consumption from global THREE, if exists\n  : {\n  BufferGeometry,\n  Float32BufferAttribute\n};\n\nimport { flatten as earcutFlatten } from 'earcut';\n\nimport interpolateLine from './interpolateLine';\n\n// support both modes for backwards threejs compatibility\nconst setAttributeFn = new THREE.BufferGeometry().setAttribute ? 'setAttribute' : 'addAttribute';\n\nclass GeoJsonGeometry extends THREE.BufferGeometry {\n  constructor(geoJson, radius = 1, resolution = 5) {\n    super();\n\n    this.type = 'GeoJsonGeometry';\n\n    this.parameters = {\n      geoJson,\n      radius,\n      resolution\n    };\n\n    // process various geometry types\n    const groups = ({\n      Point: genPoint,\n      MultiPoint: genMultiPoint,\n      LineString: genLineString,\n      MultiLineString: genMultiLineString,\n      Polygon: genPolygon,\n      MultiPolygon: genMultiPolygon\n    }[geoJson.type] || (() => []))(geoJson.coordinates, radius);\n\n    // concat groups\n    let indices = [], vertices = [];\n    let groupCnt = 0;\n    groups.forEach(newG => {\n      const prevIndCnt = indices.length;\n      concatGroup({indices, vertices}, newG);\n\n      this.addGroup(prevIndCnt, indices.length - prevIndCnt, groupCnt++);\n    });\n\n    // build geometry\n    indices.length && this.setIndex(indices);\n    vertices.length && this[setAttributeFn]('position', new THREE.Float32BufferAttribute(vertices, 3));\n\n    //\n\n    function genPoint(coords, r) {\n      const vertices = polar2Cartesian(coords[1], coords[0], r + (coords[2] || 0));\n      const indices = [];\n\n      return [{vertices, indices}];\n    }\n\n    function genMultiPoint(coords, r) {\n      const result = {vertices: [], indices: []};\n\n      coords.map(c => genPoint(c, r)).forEach(([newPnt]) => {\n        concatGroup(result, newPnt);\n      });\n\n      return [result];\n    }\n\n    function genLineString(coords, r) {\n      const coords3d = interpolateLine(coords, resolution)\n        .map(([lng, lat, alt = 0]) => polar2Cartesian(lat, lng, r + alt));\n\n      const { vertices} = earcutFlatten([coords3d]);\n\n      const numPoints = Math.round(vertices.length / 3);\n\n      const indices = [];\n\n      for (let vIdx = 1; vIdx < numPoints; vIdx++) {\n        indices.push(vIdx - 1, vIdx);\n      }\n\n      return [{vertices, indices}];\n    }\n\n    function genMultiLineString(coords, r) {\n      const result = {vertices: [], indices: []};\n\n      coords.map(c => genLineString(c, r)).forEach(([newLine]) => {\n        concatGroup(result, newLine);\n      });\n\n      return [result];\n    }\n\n    function genPolygon(coords, r) {\n      const coords3d = coords\n        .map(coordsSegment => interpolateLine(coordsSegment, resolution)\n          .map(([lng, lat, alt = 0]) => polar2Cartesian(lat, lng, r + alt)));\n\n      // Each point generates 3 vertice items (x,y,z).\n      const {vertices, holes} = earcutFlatten(coords3d);\n\n      const firstHoleIdx = holes[0] || Infinity;\n      const outerVertices = vertices.slice(0, firstHoleIdx * 3);\n      const holeVertices = vertices.slice(firstHoleIdx * 3);\n\n      const holesIdx = new Set(holes);\n\n      const numPoints = Math.round(vertices.length / 3);\n\n      const outerIndices = [], holeIndices = [];\n      for (let vIdx = 1; vIdx < numPoints; vIdx++) {\n        if (!holesIdx.has(vIdx)) {\n          if (vIdx < firstHoleIdx) {\n            outerIndices.push(vIdx - 1, vIdx)\n          } else {\n            holeIndices.push(vIdx - 1 - firstHoleIdx, vIdx - firstHoleIdx);\n          }\n        }\n      }\n\n      const groups = [{indices: outerIndices, vertices: outerVertices}];\n\n      if (holes.length) {\n        groups.push({indices: holeIndices, vertices: holeVertices});\n      }\n\n      return groups;\n    }\n\n    function genMultiPolygon(coords, r) {\n      const outer = {vertices: [], indices: []};\n      const holes = {vertices: [], indices: []};\n\n      coords.map(c => genPolygon(c, r)).forEach(([newOuter, newHoles]) => {\n        concatGroup(outer, newOuter);\n        newHoles && concatGroup(holes, newHoles);\n      });\n\n      const groups = [outer];\n      holes.vertices.length && groups.push(holes);\n\n      return groups;\n    }\n  }\n}\n\n//\n\nfunction concatGroup(main, extra) {\n  const prevVertCnt = Math.round(main.vertices.length / 3);\n  concatArr(main.vertices, extra.vertices);\n  concatArr(main.indices, extra.indices.map(ind => ind + prevVertCnt));\n}\n\nfunction concatArr(target, src) {\n  for (let e of src) target.push(e);\n}\n\nfunction polar2Cartesian(lat, lng, r = 0) {\n  const phi = (90 - lat) * Math.PI / 180;\n  const theta = (90 - lng) * Math.PI / 180;\n  return [\n    r * Math.sin(phi) * Math.cos(theta), // x\n    r * Math.cos(phi), // y\n    r * Math.sin(phi) * Math.sin(theta) // z\n  ];\n}\n\nexport default GeoJsonGeometry;\n"], "names": ["stream", "interpolateLine", "lineCoords", "arguments", "length", "undefined", "maxDegDistance", "result", "prevPnt", "for<PERSON>ach", "pnt", "dist", "geoDistance", "Math", "PI", "geoInterpol", "geoInterpolate", "altInterpol", "interpolateNumber", "interpol", "t", "concat", "_toConsumableArray", "tStep", "ceil", "push", "THREE", "window", "BufferGeometry", "Float32BufferAttribute", "setAttributeFn", "setAttribute", "GeoJsonGeometry", "_THREE$BufferGeometry", "geoJson", "_this", "radius", "resolution", "_classCallCheck", "_callSuper", "type", "parameters", "groups", "Point", "genPoint", "MultiPoint", "genMultiPoint", "LineString", "genLineString", "MultiLineString", "genMultiLineString", "Polygon", "genPolygon", "MultiPolygon", "genMultiPolygon", "coordinates", "indices", "vertices", "groupCnt", "newG", "prevIndCnt", "concatGroup", "addGroup", "setIndex", "coords", "r", "polar2Cartesian", "map", "c", "_ref", "_ref2", "_slicedToArray", "newPnt", "coords3d", "_ref3", "_ref4", "lng", "lat", "_ref4$", "alt", "_earcutFlatten", "earcutFlatten", "numPoints", "round", "vIdx", "_ref5", "_ref6", "newLine", "coordsSegment", "_ref7", "_ref8", "_ref8$", "_earcutFlatten2", "holes", "firstHoleIdx", "Infinity", "outerVertices", "slice", "holeVertices", "holesIdx", "Set", "outerIndices", "holeIndices", "has", "outer", "_ref9", "_ref10", "newOuter", "newHoles", "_inherits", "_createClass", "main", "extra", "prevVertCnt", "concatArr", "ind", "target", "src", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "e", "value", "err", "f", "phi", "theta", "sin", "cos"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAspBA;EACO,SAAS,OAAO,CAAC,IAAI,EAAE;EAC9B,IAAI,MAAM,QAAQ,GAAG,EAAE;EACvB,IAAI,MAAM,KAAK,GAAG,EAAE;EACpB,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM;EACxC,IAAI,IAAI,SAAS,GAAG,CAAC;EACrB,IAAI,IAAI,OAAO,GAAG,CAAC;;EAEnB,IAAI,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE;EAC7B,QAAQ,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE;EAC9B,YAAY,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpE;EACA,QAAQ,IAAI,OAAO,EAAE;EACrB,YAAY,SAAS,IAAI,OAAO;EAChC,YAAY,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC;EACjC;EACA,QAAQ,OAAO,GAAG,IAAI,CAAC,MAAM;EAC7B;EACA,IAAI,OAAO,CAAC,QAAQ,EAAE,KAAK,EAAE,UAAU,CAAC;EACxC;;ECzqBA;EACO,MAAM,KAAK,CAAC;EACnB,EAAE,WAAW,GAAG;EAChB,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,YAAY,CAAC,EAAE,CAAC;EACzC,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC;EACf;EACA,EAAE,GAAG,CAAC,CAAC,EAAE;EACT,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS;EAC5B,IAAI,IAAI,CAAC,GAAG,CAAC;EACb,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;EAChD,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACpB,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAC;EAClB,QAAQ,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;EACpE,MAAM,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE;EACzB,MAAM,CAAC,GAAG,EAAE;EACZ;EACA,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;EACZ,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC;EACnB,IAAI,OAAO,IAAI;EACf;EACA,EAAE,OAAO,GAAG;EACZ,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS;EAC5B,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;EACrC,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE;EACf,MAAM,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EACjB,MAAM,OAAO,CAAC,GAAG,CAAC,EAAE;EACpB,QAAQ,CAAC,GAAG,EAAE;EACd,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EAClB,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAC;EAClB,QAAQ,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;EACzB,QAAQ,IAAI,EAAE,EAAE;EAChB;EACA,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;EAC3E,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC;EAClB,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC;EAClB,QAAQ,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;EAC/B;EACA;EACA,IAAI,OAAO,EAAE;EACb;EACA;;ECtCO,IAAI,EAAE,GAAG,IAAI,CAAC,EAAE;EAChB,IAAI,MAAM,GAAG,EAAE,GAAG,CAAC;;EAInB,IAAI,OAAO,GAAG,GAAG,GAAG,EAAE;EACtB,IAAI,OAAO,GAAG,EAAE,GAAG,GAAG;;EAEtB,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG;EAElB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK;EACtB,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG;EAOlB,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG;EAElB,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI;;EAOpB,SAAS,IAAI,CAAC,CAAC,EAAE;EACxB,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;EACzD;;EAEO,SAAS,QAAQ,CAAC,CAAC,EAAE;EAC5B,EAAE,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;EAC7B;;ECnCe,SAAS,IAAI,GAAG;;ECA/B,SAAS,cAAc,CAAC,QAAQ,EAAE,MAAM,EAAE;EAC1C,EAAE,IAAI,QAAQ,IAAI,kBAAkB,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;EACpE,IAAI,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC;EACvD;EACA;;EAEA,IAAI,gBAAgB,GAAG;EACvB,EAAE,OAAO,EAAE,SAAS,MAAM,EAAE,MAAM,EAAE;EACpC,IAAI,cAAc,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC;EAC3C,GAAG;EACH,EAAE,iBAAiB,EAAE,SAAS,MAAM,EAAE,MAAM,EAAE;EAC9C,IAAI,IAAI,QAAQ,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM;EAC/D,IAAI,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC;EAChE;EACA,CAAC;;EAED,IAAI,kBAAkB,GAAG;EACzB,EAAE,MAAM,EAAE,SAAS,MAAM,EAAE,MAAM,EAAE;EACnC,IAAI,MAAM,CAAC,MAAM,EAAE;EACnB,GAAG;EACH,EAAE,KAAK,EAAE,SAAS,MAAM,EAAE,MAAM,EAAE;EAClC,IAAI,MAAM,GAAG,MAAM,CAAC,WAAW;EAC/B,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;EACjD,GAAG;EACH,EAAE,UAAU,EAAE,SAAS,MAAM,EAAE,MAAM,EAAE;EACvC,IAAI,IAAI,WAAW,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM;EACxE,IAAI,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;EAC1F,GAAG;EACH,EAAE,UAAU,EAAE,SAAS,MAAM,EAAE,MAAM,EAAE;EACvC,IAAI,UAAU,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,EAAE,CAAC,CAAC;EAC7C,GAAG;EACH,EAAE,eAAe,EAAE,SAAS,MAAM,EAAE,MAAM,EAAE;EAC5C,IAAI,IAAI,WAAW,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM;EACxE,IAAI,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;EACzD,GAAG;EACH,EAAE,OAAO,EAAE,SAAS,MAAM,EAAE,MAAM,EAAE;EACpC,IAAI,aAAa,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC;EAC7C,GAAG;EACH,EAAE,YAAY,EAAE,SAAS,MAAM,EAAE,MAAM,EAAE;EACzC,IAAI,IAAI,WAAW,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM;EACxE,IAAI,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC;EACzD,GAAG;EACH,EAAE,kBAAkB,EAAE,SAAS,MAAM,EAAE,MAAM,EAAE;EAC/C,IAAI,IAAI,UAAU,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM;EACrE,IAAI,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC;EACzD;EACA,CAAC;;EAED,SAAS,UAAU,CAAC,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE;EACjD,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,GAAG,MAAM,EAAE,UAAU;EACzD,EAAE,MAAM,CAAC,SAAS,EAAE;EACpB,EAAE,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;EACxG,EAAE,MAAM,CAAC,OAAO,EAAE;EAClB;;EAEA,SAAS,aAAa,CAAC,WAAW,EAAE,MAAM,EAAE;EAC5C,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM;EACpC,EAAE,MAAM,CAAC,YAAY,EAAE;EACvB,EAAE,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;EACvD,EAAE,MAAM,CAAC,UAAU,EAAE;EACrB;;EAEe,kBAAQ,CAAC,MAAM,EAAE,MAAM,EAAE;EACxC,EAAE,IAAc,gBAAgB,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;EAC9D,IAAI,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC;EACjD,GAAG,MAAM;EACT,IAAI,cAAc,CAAC,MAAM,EAAE,MAAM,CAAC;EAClC;EACA;;EC/DA,IAAI,SAAS;EACb,IAAI,OAAO;EACX,IAAI,OAAO;EACX,IAAI,OAAO;;EAEX,IAAI,YAAY,GAAG;EACnB,EAAE,MAAM,EAAE,IAAI;EACd,EAAE,KAAK,EAAE,IAAI;EACb,EAAE,SAAS,EAAE,eAAe;EAC5B,EAAE,OAAO,EAAE,IAAI;EACf,EAAE,YAAY,EAAE,IAAI;EACpB,EAAE,UAAU,EAAE;EACd,CAAC;;EAED,SAAS,eAAe,GAAG;EAC3B,EAAE,YAAY,CAAC,KAAK,GAAG,gBAAgB;EACvC,EAAE,YAAY,CAAC,OAAO,GAAG,aAAa;EACtC;;EAEA,SAAS,aAAa,GAAG;EACzB,EAAE,YAAY,CAAC,KAAK,GAAG,YAAY,CAAC,OAAO,GAAG,IAAI;EAClD;;EAEA,SAAS,gBAAgB,CAAC,MAAM,EAAE,GAAG,EAAE;EACvC,EAAE,MAAM,IAAI,OAAO,EAAE,GAAG,IAAI,OAAO;EACnC,EAAE,OAAO,GAAG,MAAM,EAAE,OAAO,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO,GAAG,GAAG,CAAC,GAAG,CAAC;EAC1D,EAAE,YAAY,CAAC,KAAK,GAAG,WAAW;EAClC;;EAEA,SAAS,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE;EAClC,EAAE,MAAM,IAAI,OAAO,EAAE,GAAG,IAAI,OAAO;EACnC,EAAE,IAAI,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC;EACvB,MAAM,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC;EACvB,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC;EACnC,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC;EAC3B,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC;EAC3B,MAAM,CAAC,GAAG,MAAM,GAAG,QAAQ;EAC3B,MAAM,CAAC,GAAG,OAAO,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM,GAAG,QAAQ;EACxD,MAAM,CAAC,GAAG,OAAO,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM,GAAG,QAAQ;EACxD,EAAE,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9C,EAAE,OAAO,GAAG,MAAM,EAAE,OAAO,GAAG,MAAM,EAAE,OAAO,GAAG,MAAM;EACtD;;EAEe,eAAQ,CAAC,MAAM,EAAE;EAChC,EAAE,SAAS,GAAG,IAAI,KAAK,EAAE;EACzB,EAAEA,SAAM,CAAC,MAAM,EAAE,YAAY,CAAC;EAC9B,EAAE,OAAO,CAAC,SAAS;EACnB;;EClDA,IAAI,WAAW,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;EAC9B,IAAI,MAAM,GAAG,CAAC,IAAI,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,CAAC;;EAE5C,oBAAQ,CAAC,CAAC,EAAE,CAAC,EAAE;EAC9B,EAAE,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC;EACpB,EAAE,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC;EACpB,EAAE,OAAO,MAAM,CAAC,MAAM,CAAC;EACvB;;ECPe,uBAAQ,CAAC,CAAC,EAAE,CAAC,EAAE;EAC9B,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO;EACzB,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO;EACzB,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO;EACzB,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO;EACzB,MAAM,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC;EACnB,MAAM,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC;EACnB,MAAM,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC;EACnB,MAAM,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC;EACnB,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC;EACzB,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC;EACzB,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC;EACzB,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC;EACzB,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;EAC3E,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;;EAEhB,EAAE,IAAI,WAAW,GAAG,CAAC,GAAG,SAAS,CAAC,EAAE;EACpC,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;EAC3B,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EAC1B,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG;EAC7B,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG;EAC7B,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG;EAC7B,IAAI,OAAO;EACX,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,OAAO;EAC3B,MAAM,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;EACtC,KAAK;EACL,GAAG,GAAG,WAAW;EACjB,IAAI,OAAO,CAAC,EAAE,GAAG,OAAO,EAAE,EAAE,GAAG,OAAO,CAAC;EACvC,GAAG;;EAEH,EAAE,WAAW,CAAC,QAAQ,GAAG,CAAC;;EAE1B,EAAE,OAAO,WAAW;EACpB;;ECnCe,0BAAQ,CAAC,CAAC,EAAE,CAAC,EAAE;EAC9B,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE;EACrC,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;EAC9B,GAAG;EACH;;ECDA,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,GAA4C;EAAA,EAAA,IAAxCC,UAAU,GAAAC,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE;EAAA,EAAA,IAAEG,cAAc,GAAAH,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAG,CAAC;IAC1D,IAAMI,MAAM,GAAG,EAAE;IAEjB,IAAIC,OAAO,GAAG,IAAI;EAClBN,EAAAA,UAAU,CAACO,OAAO,CAAC,UAAAC,GAAG,EAAI;EACxB,IAAA,IAAIF,OAAO,EAAE;EACX,MAAA,IAAMG,IAAI,GAAGC,WAAW,CAACF,GAAG,EAAEF,OAAO,CAAC,GAAG,GAAG,GAAGK,IAAI,CAACC,EAAE;QACtD,IAAIH,IAAI,GAAGL,cAAc,EAAE;EACzB,QAAA,IAAMS,WAAW,GAAGC,cAAc,CAACR,OAAO,EAAEE,GAAG,CAAC;EAChD,QAAA,IAAMO,WAAW,GAAGT,OAAO,CAACJ,MAAM,GAAG,CAAC,IAAIM,GAAG,CAACN,MAAM,GAAG,CAAC,GAAGc,iBAAiB,CAACV,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,EAAEE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI;EACjH,QAAA,IAAMS,QAAQ,GAAGF,WAAW,GAAG,UAAAG,CAAC,EAAA;EAAA,UAAA,OAAA,EAAA,CAAAC,MAAA,CAAAC,kBAAA,CAAQP,WAAW,CAACK,CAAC,CAAC,CAAEH,EAAAA,CAAAA,WAAW,CAACG,CAAC,CAAC,CAAA,CAAA;EAAA,SAAC,GAAEL,WAAW;UAEpF,IAAMQ,KAAK,GAAG,CAAC,GAAGV,IAAI,CAACW,IAAI,CAACb,IAAI,GAAGL,cAAc,CAAC;UAElD,IAAIc,CAAC,GAAGG,KAAK;UACb,OAAOH,CAAC,GAAG,CAAC,EAAE;EACZb,UAAAA,MAAM,CAACkB,IAAI,CAACN,QAAQ,CAACC,CAAC,CAAC,CAAC;EACxBA,UAAAA,CAAC,IAAIG,KAAK;EACZ;EACF;EACF;EAEAhB,IAAAA,MAAM,CAACkB,IAAI,CAACjB,OAAO,GAAGE,GAAG,CAAC;EAC5B,GAAC,CAAC;EAEF,EAAA,OAAOH,MAAM;EACf,CAAC;;ECxBD,IAAMmB,KAAK,GAAI,OAAOC,MAAM,KAAK,WAAW,IAAKA,MAAM,CAACD,KAAK,GACzDC,MAAM,CAACD,KAAK;EAAC,EACb;EACFE,EAAAA,cAAc,EAAdA,oBAAc;EACdC,EAAAA,sBAAsB,EAAtBA;EACF,CAAC;;EAMD;EACA,IAAMC,cAAc,GAAG,IAAIJ,KAAK,CAACE,cAAc,EAAE,CAACG,YAAY,GAAG,cAAc,GAAG,cAAc;AAE1FC,MAAAA,eAAe,0BAAAC,qBAAA,EAAA;IACnB,SAAAD,eAAAA,CAAYE,OAAO,EAA8B;EAAA,IAAA,IAAAC,KAAA;EAAA,IAAA,IAA5BC,MAAM,GAAAjC,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAG,CAAC;EAAA,IAAA,IAAEkC,UAAU,GAAAlC,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAG,CAAC;EAAAmC,IAAAA,eAAA,OAAAN,eAAA,CAAA;MAC7CG,KAAA,GAAAI,UAAA,CAAA,IAAA,EAAAP,eAAA,CAAA;MAEAG,KAAA,CAAKK,IAAI,GAAG,iBAAiB;MAE7BL,KAAA,CAAKM,UAAU,GAAG;EAChBP,MAAAA,OAAO,EAAPA,OAAO;EACPE,MAAAA,MAAM,EAANA,MAAM;EACNC,MAAAA,UAAU,EAAVA;OACD;;EAED;MACA,IAAMK,MAAM,GAAG,CAAC;EACdC,MAAAA,KAAK,EAAEC,QAAQ;EACfC,MAAAA,UAAU,EAAEC,aAAa;EACzBC,MAAAA,UAAU,EAAEC,aAAa;EACzBC,MAAAA,eAAe,EAAEC,kBAAkB;EACnCC,MAAAA,OAAO,EAAEC,UAAU;EACnBC,MAAAA,YAAY,EAAEC;EAChB,KAAC,CAACpB,OAAO,CAACM,IAAI,CAAC,IAAK,YAAA;EAAA,MAAA,OAAM,EAAE;EAAA,KAAC,EAAEN,OAAO,CAACqB,WAAW,EAAEnB,MAAM,CAAC;;EAE3D;MACA,IAAIoB,OAAO,GAAG,EAAE;EAAEC,MAAAA,QAAQ,GAAG,EAAE;MAC/B,IAAIC,QAAQ,GAAG,CAAC;EAChBhB,IAAAA,MAAM,CAACjC,OAAO,CAAC,UAAAkD,IAAI,EAAI;EACrB,MAAA,IAAMC,UAAU,GAAGJ,OAAO,CAACpD,MAAM;EACjCyD,MAAAA,WAAW,CAAC;EAACL,QAAAA,OAAO,EAAPA,OAAO;EAAEC,QAAAA,QAAQ,EAARA;SAAS,EAAEE,IAAI,CAAC;EAEtCxB,MAAAA,KAAA,CAAK2B,QAAQ,CAACF,UAAU,EAAEJ,OAAO,CAACpD,MAAM,GAAGwD,UAAU,EAAEF,QAAQ,EAAE,CAAC;EACpE,KAAC,CAAC;;EAEF;MACAF,OAAO,CAACpD,MAAM,IAAI+B,KAAA,CAAK4B,QAAQ,CAACP,OAAO,CAAC;EACxCC,IAAAA,QAAQ,CAACrD,MAAM,IAAI+B,KAAA,CAAKL,cAAc,CAAC,CAAC,UAAU,EAAE,IAAIJ,KAAK,CAACG,sBAAsB,CAAC4B,QAAQ,EAAE,CAAC,CAAC,CAAC;;EAElG;;EAEA,IAAA,SAASb,QAAQA,CAACoB,MAAM,EAAEC,CAAC,EAAE;QAC3B,IAAMR,QAAQ,GAAGS,eAAe,CAACF,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEC,CAAC,IAAID,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5E,IAAMR,OAAO,GAAG,EAAE;EAElB,MAAA,OAAO,CAAC;EAACC,QAAAA,QAAQ,EAARA,QAAQ;EAAED,QAAAA,OAAO,EAAPA;EAAO,OAAC,CAAC;EAC9B;EAEA,IAAA,SAASV,aAAaA,CAACkB,MAAM,EAAEC,CAAC,EAAE;EAChC,MAAA,IAAM1D,MAAM,GAAG;EAACkD,QAAAA,QAAQ,EAAE,EAAE;EAAED,QAAAA,OAAO,EAAE;SAAG;EAE1CQ,MAAAA,MAAM,CAACG,GAAG,CAAC,UAAAC,CAAC,EAAA;EAAA,QAAA,OAAIxB,QAAQ,CAACwB,CAAC,EAAEH,CAAC,CAAC;EAAA,OAAA,CAAC,CAACxD,OAAO,CAAC,UAAA4D,IAAA,EAAc;EAAA,QAAA,IAAAC,KAAA,GAAAC,cAAA,CAAAF,IAAA,EAAA,CAAA,CAAA;EAAZG,UAAAA,MAAM,GAAAF,KAAA,CAAA,CAAA,CAAA;EAC9CT,QAAAA,WAAW,CAACtD,MAAM,EAAEiE,MAAM,CAAC;EAC7B,OAAC,CAAC;QAEF,OAAO,CAACjE,MAAM,CAAC;EACjB;EAEA,IAAA,SAASyC,aAAaA,CAACgB,MAAM,EAAEC,CAAC,EAAE;EAChC,MAAA,IAAMQ,QAAQ,GAAGxE,eAAe,CAAC+D,MAAM,EAAE3B,UAAU,CAAC,CACjD8B,GAAG,CAAC,UAAAO,KAAA,EAAA;EAAA,QAAA,IAAAC,KAAA,GAAAJ,cAAA,CAAAG,KAAA,EAAA,CAAA,CAAA;EAAEE,UAAAA,GAAG,GAAAD,KAAA,CAAA,CAAA,CAAA;EAAEE,UAAAA,GAAG,GAAAF,KAAA,CAAA,CAAA,CAAA;EAAAG,UAAAA,MAAA,GAAAH,KAAA,CAAA,CAAA,CAAA;EAAEI,UAAAA,GAAG,GAAAD,MAAA,KAAG,KAAA,CAAA,GAAA,CAAC,GAAAA,MAAA;UAAA,OAAMZ,eAAe,CAACW,GAAG,EAAED,GAAG,EAAEX,CAAC,GAAGc,GAAG,CAAC;SAAC,CAAA;EAEnE,MAAA,IAAAC,cAAA,GAAoBC,OAAa,CAAC,CAACR,QAAQ,CAAC,CAAC;UAArChB,QAAQ,GAAAuB,cAAA,CAARvB,QAAQ;QAEhB,IAAMyB,SAAS,GAAGrE,IAAI,CAACsE,KAAK,CAAC1B,QAAQ,CAACrD,MAAM,GAAG,CAAC,CAAC;QAEjD,IAAMoD,OAAO,GAAG,EAAE;QAElB,KAAK,IAAI4B,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,SAAS,EAAEE,IAAI,EAAE,EAAE;UAC3C5B,OAAO,CAAC/B,IAAI,CAAC2D,IAAI,GAAG,CAAC,EAAEA,IAAI,CAAC;EAC9B;EAEA,MAAA,OAAO,CAAC;EAAC3B,QAAAA,QAAQ,EAARA,QAAQ;EAAED,QAAAA,OAAO,EAAPA;EAAO,OAAC,CAAC;EAC9B;EAEA,IAAA,SAASN,kBAAkBA,CAACc,MAAM,EAAEC,CAAC,EAAE;EACrC,MAAA,IAAM1D,MAAM,GAAG;EAACkD,QAAAA,QAAQ,EAAE,EAAE;EAAED,QAAAA,OAAO,EAAE;SAAG;EAE1CQ,MAAAA,MAAM,CAACG,GAAG,CAAC,UAAAC,CAAC,EAAA;EAAA,QAAA,OAAIpB,aAAa,CAACoB,CAAC,EAAEH,CAAC,CAAC;EAAA,OAAA,CAAC,CAACxD,OAAO,CAAC,UAAA4E,KAAA,EAAe;EAAA,QAAA,IAAAC,KAAA,GAAAf,cAAA,CAAAc,KAAA,EAAA,CAAA,CAAA;EAAbE,UAAAA,OAAO,GAAAD,KAAA,CAAA,CAAA,CAAA;EACpDzB,QAAAA,WAAW,CAACtD,MAAM,EAAEgF,OAAO,CAAC;EAC9B,OAAC,CAAC;QAEF,OAAO,CAAChF,MAAM,CAAC;EACjB;EAEA,IAAA,SAAS6C,UAAUA,CAACY,MAAM,EAAEC,CAAC,EAAE;EAC7B,MAAA,IAAMQ,QAAQ,GAAGT,MAAM,CACpBG,GAAG,CAAC,UAAAqB,aAAa,EAAA;UAAA,OAAIvF,eAAe,CAACuF,aAAa,EAAEnD,UAAU,CAAC,CAC7D8B,GAAG,CAAC,UAAAsB,KAAA,EAAA;EAAA,UAAA,IAAAC,KAAA,GAAAnB,cAAA,CAAAkB,KAAA,EAAA,CAAA,CAAA;EAAEb,YAAAA,GAAG,GAAAc,KAAA,CAAA,CAAA,CAAA;EAAEb,YAAAA,GAAG,GAAAa,KAAA,CAAA,CAAA,CAAA;EAAAC,YAAAA,MAAA,GAAAD,KAAA,CAAA,CAAA,CAAA;EAAEX,YAAAA,GAAG,GAAAY,MAAA,KAAG,KAAA,CAAA,GAAA,CAAC,GAAAA,MAAA;YAAA,OAAMzB,eAAe,CAACW,GAAG,EAAED,GAAG,EAAEX,CAAC,GAAGc,GAAG,CAAC;WAAC,CAAA;SAAC,CAAA;;EAEtE;EACA,MAAA,IAAAa,eAAA,GAA0BX,OAAa,CAACR,QAAQ,CAAC;UAA1ChB,QAAQ,GAAAmC,eAAA,CAARnC,QAAQ;UAAEoC,KAAK,GAAAD,eAAA,CAALC,KAAK;EAEtB,MAAA,IAAMC,YAAY,GAAGD,KAAK,CAAC,CAAC,CAAC,IAAIE,QAAQ;QACzC,IAAMC,aAAa,GAAGvC,QAAQ,CAACwC,KAAK,CAAC,CAAC,EAAEH,YAAY,GAAG,CAAC,CAAC;QACzD,IAAMI,YAAY,GAAGzC,QAAQ,CAACwC,KAAK,CAACH,YAAY,GAAG,CAAC,CAAC;EAErD,MAAA,IAAMK,QAAQ,GAAG,IAAIC,GAAG,CAACP,KAAK,CAAC;QAE/B,IAAMX,SAAS,GAAGrE,IAAI,CAACsE,KAAK,CAAC1B,QAAQ,CAACrD,MAAM,GAAG,CAAC,CAAC;QAEjD,IAAMiG,YAAY,GAAG,EAAE;EAAEC,QAAAA,WAAW,GAAG,EAAE;QACzC,KAAK,IAAIlB,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,SAAS,EAAEE,IAAI,EAAE,EAAE;EAC3C,QAAA,IAAI,CAACe,QAAQ,CAACI,GAAG,CAACnB,IAAI,CAAC,EAAE;YACvB,IAAIA,IAAI,GAAGU,YAAY,EAAE;cACvBO,YAAY,CAAC5E,IAAI,CAAC2D,IAAI,GAAG,CAAC,EAAEA,IAAI,CAAC;EACnC,WAAC,MAAM;EACLkB,YAAAA,WAAW,CAAC7E,IAAI,CAAC2D,IAAI,GAAG,CAAC,GAAGU,YAAY,EAAEV,IAAI,GAAGU,YAAY,CAAC;EAChE;EACF;EACF;QAEA,IAAMpD,MAAM,GAAG,CAAC;EAACc,QAAAA,OAAO,EAAE6C,YAAY;EAAE5C,QAAAA,QAAQ,EAAEuC;EAAa,OAAC,CAAC;QAEjE,IAAIH,KAAK,CAACzF,MAAM,EAAE;UAChBsC,MAAM,CAACjB,IAAI,CAAC;EAAC+B,UAAAA,OAAO,EAAE8C,WAAW;EAAE7C,UAAAA,QAAQ,EAAEyC;EAAY,SAAC,CAAC;EAC7D;EAEA,MAAA,OAAOxD,MAAM;EACf;EAEA,IAAA,SAASY,eAAeA,CAACU,MAAM,EAAEC,CAAC,EAAE;EAClC,MAAA,IAAMuC,KAAK,GAAG;EAAC/C,QAAAA,QAAQ,EAAE,EAAE;EAAED,QAAAA,OAAO,EAAE;SAAG;EACzC,MAAA,IAAMqC,KAAK,GAAG;EAACpC,QAAAA,QAAQ,EAAE,EAAE;EAAED,QAAAA,OAAO,EAAE;SAAG;EAEzCQ,MAAAA,MAAM,CAACG,GAAG,CAAC,UAAAC,CAAC,EAAA;EAAA,QAAA,OAAIhB,UAAU,CAACgB,CAAC,EAAEH,CAAC,CAAC;EAAA,OAAA,CAAC,CAACxD,OAAO,CAAC,UAAAgG,KAAA,EAA0B;EAAA,QAAA,IAAAC,MAAA,GAAAnC,cAAA,CAAAkC,KAAA,EAAA,CAAA,CAAA;EAAxBE,UAAAA,QAAQ,GAAAD,MAAA,CAAA,CAAA,CAAA;EAAEE,UAAAA,QAAQ,GAAAF,MAAA,CAAA,CAAA,CAAA;EAC5D7C,QAAAA,WAAW,CAAC2C,KAAK,EAAEG,QAAQ,CAAC;EAC5BC,QAAAA,QAAQ,IAAI/C,WAAW,CAACgC,KAAK,EAAEe,QAAQ,CAAC;EAC1C,OAAC,CAAC;EAEF,MAAA,IAAMlE,MAAM,GAAG,CAAC8D,KAAK,CAAC;QACtBX,KAAK,CAACpC,QAAQ,CAACrD,MAAM,IAAIsC,MAAM,CAACjB,IAAI,CAACoE,KAAK,CAAC;EAE3C,MAAA,OAAOnD,MAAM;EACf;EAAC,IAAA,OAAAP,KAAA;EACH;IAAC0E,SAAA,CAAA7E,eAAA,EAAAC,qBAAA,CAAA;IAAA,OAAA6E,YAAA,CAAA9E,eAAA,CAAA;EAAA,CApI2BN,CAAAA,KAAK,CAACE,cAAc,EAuIlD;EAEA,SAASiC,WAAWA,CAACkD,IAAI,EAAEC,KAAK,EAAE;EAChC,EAAA,IAAMC,WAAW,GAAGpG,IAAI,CAACsE,KAAK,CAAC4B,IAAI,CAACtD,QAAQ,CAACrD,MAAM,GAAG,CAAC,CAAC;IACxD8G,SAAS,CAACH,IAAI,CAACtD,QAAQ,EAAEuD,KAAK,CAACvD,QAAQ,CAAC;EACxCyD,EAAAA,SAAS,CAACH,IAAI,CAACvD,OAAO,EAAEwD,KAAK,CAACxD,OAAO,CAACW,GAAG,CAAC,UAAAgD,GAAG,EAAA;MAAA,OAAIA,GAAG,GAAGF,WAAW;EAAA,GAAA,CAAC,CAAC;EACtE;EAEA,SAASC,SAASA,CAACE,MAAM,EAAEC,GAAG,EAAE;EAAA,EAAA,IAAAC,SAAA,GAAAC,0BAAA,CAChBF,GAAG,CAAA;MAAAG,KAAA;EAAA,EAAA,IAAA;MAAjB,KAAAF,SAAA,CAAAG,CAAA,EAAAD,EAAAA,CAAAA,CAAAA,KAAA,GAAAF,SAAA,CAAAI,CAAA,EAAAC,EAAAA,IAAA,GAAmB;EAAA,MAAA,IAAVC,CAAC,GAAAJ,KAAA,CAAAK,KAAA;EAAST,MAAAA,MAAM,CAAC3F,IAAI,CAACmG,CAAC,CAAC;EAAA;EAAC,GAAA,CAAA,OAAAE,GAAA,EAAA;MAAAR,SAAA,CAAAM,CAAA,CAAAE,GAAA,CAAA;EAAA,GAAA,SAAA;EAAAR,IAAAA,SAAA,CAAAS,CAAA,EAAA;EAAA;EACpC;EAEA,SAAS7D,eAAeA,CAACW,GAAG,EAAED,GAAG,EAAS;EAAA,EAAA,IAAPX,CAAC,GAAA9D,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAG,CAAC;IACtC,IAAM6H,GAAG,GAAG,CAAC,EAAE,GAAGnD,GAAG,IAAIhE,IAAI,CAACC,EAAE,GAAG,GAAG;IACtC,IAAMmH,KAAK,GAAG,CAAC,EAAE,GAAGrD,GAAG,IAAI/D,IAAI,CAACC,EAAE,GAAG,GAAG;EACxC,EAAA,OAAO,CACLmD,CAAC,GAAGpD,IAAI,CAACqH,GAAG,CAACF,GAAG,CAAC,GAAGnH,IAAI,CAACsH,GAAG,CAACF,KAAK,CAAC;EAAE;EACrChE,EAAAA,CAAC,GAAGpD,IAAI,CAACsH,GAAG,CAACH,GAAG,CAAC;EAAE;EACnB/D,EAAAA,CAAC,GAAGpD,IAAI,CAACqH,GAAG,CAACF,GAAG,CAAC,GAAGnH,IAAI,CAACqH,GAAG,CAACD,KAAK,CAAC;KACpC;EACH;;;;;;;;", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8]}