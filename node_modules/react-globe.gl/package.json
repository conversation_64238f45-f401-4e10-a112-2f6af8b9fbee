{"name": "react-globe.gl", "version": "2.33.2", "description": "React component for Globe Data Visualization using ThreeJS/WebGL", "license": "MIT", "type": "module", "unpkg": "dist/react-globe.gl.min.js", "jsdelivr": "dist/react-globe.gl.min.js", "main": "dist/react-globe.gl.mjs", "module": "dist/react-globe.gl.mjs", "types": "dist/react-globe.gl.d.ts", "exports": {"types": "./dist/react-globe.gl.d.ts", "umd": "./dist/react-globe.gl.min.js", "default": "./dist/react-globe.gl.mjs"}, "sideEffects": false, "repository": {"type": "git", "url": "git+https://github.com/vasturiano/react-globe.gl.git"}, "keywords": ["react", "webgl", "three", "globe", "geo", "spherical", "projection", "orthographic"], "author": {"name": "Vasco <PERSON>turiano", "url": "https://github.com/vasturiano"}, "bugs": {"url": "https://github.com/vasturiano/react-globe.gl/issues"}, "homepage": "https://github.com/vasturiano/react-globe.gl", "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -w -c rollup.config.dev.js", "prepare": "npm run build"}, "files": ["dist/**/*"], "dependencies": {"globe.gl": "^2.41", "prop-types": "15", "react-kapsule": "^2.5"}, "peerDependencies": {"react": "*"}, "devDependencies": {"@babel/core": "^7.26.9", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.2", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@types/react": "^19.0.10", "rimraf": "^6.0.1", "rollup": "^4.34.9", "rollup-plugin-dts": "^6.1.1", "typescript": "^5.8.2"}, "engines": {"node": ">=12"}}