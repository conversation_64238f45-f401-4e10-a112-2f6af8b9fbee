{"version": 3, "file": "index.cjs.js", "sources": ["throttle.js", "debounce.js"], "sourcesContent": ["/* eslint-disable no-undefined,no-param-reassign,no-shadow */\n\n/**\n * Throttle execution of a function. Especially useful for rate limiting\n * execution of handlers on events like resize and scroll.\n *\n * @param  {number}    delay -          A zero-or-greater delay in milliseconds. For event callbacks, values around 100 or 250 (or even higher) are most useful.\n * @param  {boolean}   [noTrailing] -   Optional, defaults to false. If noTrailing is true, callback will only execute every `delay` milliseconds while the\n *                                    throttled-function is being called. If noTrailing is false or unspecified, callback will be executed one final time\n *                                    after the last throttled-function call. (After the throttled-function has not been called for `delay` milliseconds,\n *                                    the internal counter is reset).\n * @param  {Function}  callback -       A function to be executed after delay milliseconds. The `this` context and all arguments are passed through, as-is,\n *                                    to `callback` when the throttled-function is executed.\n * @param  {boolean}   [debounceMode] - If `debounceMode` is true (at begin), schedule `clear` to execute after `delay` ms. If `debounceMode` is false (at end),\n *                                    schedule `callback` to execute after `delay` ms.\n *\n * @returns {Function}  A new, throttled, function.\n */\nexport default function(delay, noTrailing, callback, debounceMode) {\n\t/*\n\t * After wrapper has stopped being called, this timeout ensures that\n\t * `callback` is executed at the proper times in `throttle` and `end`\n\t * debounce modes.\n\t */\n\tlet timeoutID;\n\tlet cancelled = false;\n\n\t// Keep track of the last time `callback` was executed.\n\tlet lastExec = 0;\n\n\t// Function to clear existing timeout\n\tfunction clearExistingTimeout() {\n\t\tif (timeoutID) {\n\t\t\tclearTimeout(timeoutID);\n\t\t}\n\t}\n\n\t// Function to cancel next exec\n\tfunction cancel() {\n\t\tclearExistingTimeout();\n\t\tcancelled = true;\n\t}\n\n\t// `noTrailing` defaults to falsy.\n\tif (typeof noTrailing !== 'boolean') {\n\t\tdebounceMode = callback;\n\t\tcallback = noTrailing;\n\t\tnoTrailing = undefined;\n\t}\n\n\t/*\n\t * The `wrapper` function encapsulates all of the throttling / debouncing\n\t * functionality and when executed will limit the rate at which `callback`\n\t * is executed.\n\t */\n\tfunction wrapper(...arguments_) {\n\t\tlet self = this;\n\t\tlet elapsed = Date.now() - lastExec;\n\n\t\tif (cancelled) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Execute `callback` and update the `lastExec` timestamp.\n\t\tfunction exec() {\n\t\t\tlastExec = Date.now();\n\t\t\tcallback.apply(self, arguments_);\n\t\t}\n\n\t\t/*\n\t\t * If `debounceMode` is true (at begin) this is used to clear the flag\n\t\t * to allow future `callback` executions.\n\t\t */\n\t\tfunction clear() {\n\t\t\ttimeoutID = undefined;\n\t\t}\n\n\t\tif (debounceMode && !timeoutID) {\n\t\t\t/*\n\t\t\t * Since `wrapper` is being called for the first time and\n\t\t\t * `debounceMode` is true (at begin), execute `callback`.\n\t\t\t */\n\t\t\texec();\n\t\t}\n\n\t\tclearExistingTimeout();\n\n\t\tif (debounceMode === undefined && elapsed > delay) {\n\t\t\t/*\n\t\t\t * In throttle mode, if `delay` time has been exceeded, execute\n\t\t\t * `callback`.\n\t\t\t */\n\t\t\texec();\n\t\t} else if (noTrailing !== true) {\n\t\t\t/*\n\t\t\t * In trailing throttle mode, since `delay` time has not been\n\t\t\t * exceeded, schedule `callback` to execute `delay` ms after most\n\t\t\t * recent execution.\n\t\t\t *\n\t\t\t * If `debounceMode` is true (at begin), schedule `clear` to execute\n\t\t\t * after `delay` ms.\n\t\t\t *\n\t\t\t * If `debounceMode` is false (at end), schedule `callback` to\n\t\t\t * execute after `delay` ms.\n\t\t\t */\n\t\t\ttimeoutID = setTimeout(\n\t\t\t\tdebounceMode ? clear : exec,\n\t\t\t\tdebounceMode === undefined ? delay - elapsed : delay\n\t\t\t);\n\t\t}\n\t}\n\n\twrapper.cancel = cancel;\n\n\t// Return the wrapper function.\n\treturn wrapper;\n}\n", "/* eslint-disable no-undefined */\n\nimport throttle from './throttle';\n\n/**\n * Debounce execution of a function. Debouncing, unlike throttling,\n * guarantees that a function is only executed a single time, either at the\n * very beginning of a series of calls, or at the very end.\n *\n * @param  {number}   delay -         A zero-or-greater delay in milliseconds. For event callbacks, values around 100 or 250 (or even higher) are most useful.\n * @param  {boolean}  [atBegin] -     Optional, defaults to false. If atBegin is false or unspecified, callback will only be executed `delay` milliseconds\n *                                  after the last debounced-function call. If atBegin is true, callback will be executed only at the first debounced-function call.\n *                                  (After the throttled-function has not been called for `delay` milliseconds, the internal counter is reset).\n * @param  {Function} callback -      A function to be executed after delay milliseconds. The `this` context and all arguments are passed through, as-is,\n *                                  to `callback` when the debounced-function is executed.\n *\n * @returns {Function} A new, debounced function.\n */\nexport default function(delay, atBegin, callback) {\n\treturn callback === undefined\n\t\t? throttle(delay, atBegin, false)\n\t\t: throttle(delay, callback, atBegin !== false);\n}\n"], "names": ["delay", "noTrailing", "callback", "debounceMode", "timeoutID", "cancelled", "lastExec", "clearExistingTimeout", "clearTimeout", "cancel", "undefined", "wrapper", "arguments_", "self", "elapsed", "Date", "now", "exec", "apply", "clear", "setTimeout", "atBegin", "throttle"], "mappings": ";;;;AAAA;;AAEA;;;;;;;;;;;;;;;;AAgBe,mBAASA,KAAT,EAAgBC,UAAhB,EAA4BC,QAA5B,EAAsCC,YAAtC,EAAoD;AAClE;;;;;AAKA,MAAIC,SAAJ;AACA,MAAIC,SAAS,GAAG,KAAhB,CAPkE;;AAUlE,MAAIC,QAAQ,GAAG,CAAf,CAVkE;;AAalE,WAASC,oBAAT,GAAgC;AAC/B,QAAIH,SAAJ,EAAe;AACdI,MAAAA,YAAY,CAACJ,SAAD,CAAZ;AACA;AACD,GAjBiE;;;AAoBlE,WAASK,MAAT,GAAkB;AACjBF,IAAAA,oBAAoB;AACpBF,IAAAA,SAAS,GAAG,IAAZ;AACA,GAvBiE;;;AA0BlE,MAAI,OAAOJ,UAAP,KAAsB,SAA1B,EAAqC;AACpCE,IAAAA,YAAY,GAAGD,QAAf;AACAA,IAAAA,QAAQ,GAAGD,UAAX;AACAA,IAAAA,UAAU,GAAGS,SAAb;AACA;AAED;;;;;;;AAKA,WAASC,OAAT,GAAgC;AAAA,sCAAZC,UAAY;AAAZA,MAAAA,UAAY;AAAA;;AAC/B,QAAIC,IAAI,GAAG,IAAX;AACA,QAAIC,OAAO,GAAGC,IAAI,CAACC,GAAL,KAAaV,QAA3B;;AAEA,QAAID,SAAJ,EAAe;AACd;AACA,KAN8B;;;AAS/B,aAASY,IAAT,GAAgB;AACfX,MAAAA,QAAQ,GAAGS,IAAI,CAACC,GAAL,EAAX;AACAd,MAAAA,QAAQ,CAACgB,KAAT,CAAeL,IAAf,EAAqBD,UAArB;AACA;AAED;;;;;;AAIA,aAASO,KAAT,GAAiB;AAChBf,MAAAA,SAAS,GAAGM,SAAZ;AACA;;AAED,QAAIP,YAAY,IAAI,CAACC,SAArB,EAAgC;AAC/B;;;;AAIAa,MAAAA,IAAI;AACJ;;AAEDV,IAAAA,oBAAoB;;AAEpB,QAAIJ,YAAY,KAAKO,SAAjB,IAA8BI,OAAO,GAAGd,KAA5C,EAAmD;AAClD;;;;AAIAiB,MAAAA,IAAI;AACJ,KAND,MAMO,IAAIhB,UAAU,KAAK,IAAnB,EAAyB;AAC/B;;;;;;;;;;;AAWAG,MAAAA,SAAS,GAAGgB,UAAU,CACrBjB,YAAY,GAAGgB,KAAH,GAAWF,IADF,EAErBd,YAAY,KAAKO,SAAjB,GAA6BV,KAAK,GAAGc,OAArC,GAA+Cd,KAF1B,CAAtB;AAIA;AACD;;AAEDW,EAAAA,OAAO,CAACF,MAAR,GAAiBA,MAAjB,CA9FkE;;AAiGlE,SAAOE,OAAP;AACA;;ACpHD;AAEA,AAEA;;;;;;;;;;;;;;;AAcA,AAAe,mBAASX,KAAT,EAAgBqB,OAAhB,EAAyBnB,QAAzB,EAAmC;AACjD,SAAOA,QAAQ,KAAKQ,SAAb,GACJY,QAAQ,CAACtB,KAAD,EAAQqB,OAAR,EAAiB,KAAjB,CADJ,GAEJC,QAAQ,CAACtB,KAAD,EAAQE,QAAR,EAAkBmB,OAAO,KAAK,KAA9B,CAFX;AAGA;;;;;"}