{"name": "claygl", "version": "1.3.0", "description": "WebGL graphic library", "keywords": ["graphic", "webgl"], "author": {"name": "<PERSON>", "url": "https://github.com/pissang"}, "jsnext:main": "dist/claygl.es.js", "module": "src/claygl.js", "main": "dist/claygl.js", "types": "index.d.ts", "sideEffects": false, "scripts": {"build": "npm run glsl2js && npm run gen:entry && rollup -c && uglifyjs dist/claygl.js -c -m > dist/claygl.min.js", "build:sourcemap": "npm run gen:entry && rollup --sourcemap inline -c", "dev": "rollup --sourcemap inline -c -w", "glsl2js": "node build/glsl2js.js", "gen:entry": "node build/genentry.js", "gen:fixture": "npm run pretest && electron-mocha --require-main \"./test/genfixture-env.js\" --renderer \"./test/integration/**/*.spec.js\"", "prepublish": "npm run build", "debug": "npm run pretest && electron-mocha --debug-brk --renderer \"./test/**/*.spec.js\"", "instrument": "istanbul instrument ./dist/claygl.src.js --output ./dist/claygl.js --no-compact --complete-copy --es-modules", "pretest": "npm run build:sourcemap", "tdd": "npm run pretest && electron-mocha --interactive --renderer \"./test/**/*.spec.js\"", "test:cov": "npm run pretest -- test/rollup.config.js && npm run instrument && npm run test:main -- -R test/reporter/istanbul-reporter && remap-istanbul -i coverage/coverage-final.json -o coverage/html-report -t html && node -e \"require('fs').unlinkSync('./dist/claygl.src.js')\"", "test:fixture": "npm run pretest && electron-mocha --require-main ./test/ci-env.js --renderer \"./test/integration/**/*.spec.js\" -R test/reporter/fixture-reporter", "test:main": "electron-mocha --require-main ./test/ci-env.js --renderer \"./test/**/*.spec.js\"", "test": "npm run test:main"}, "repository": {"type": "git", "url": "https://github.com/pissang/claygl.git"}, "devDependencies": {"electron": "1.7.9", "electron-mocha": "^4.0.3", "glob": "^7.0.0", "istanbul": "^1.1.0-alpha.1", "minami": "^1.2.3", "mocha": "^4.0.1", "remap-istanbul": "^0.9.5", "rollup": "^0.49.3", "rollup-plugin-commonjs": "^8.2.6", "rollup-watch": "^4.3.1"}, "dependencies": {}}