!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(t.clay={})}(this,function(t){"use strict";function e(){}function r(t,e){return t[e]}function n(t,e,r){t[e]=r}function i(t,e,r){return(e-t)*r+t}function a(t,e,r,n,a){var o=t.length;if(1==a)for(var s=0;s<o;s++)n[s]=i(t[s],e[s],r);else for(var u=t[0].length,s=0;s<o;s++)for(var l=0;l<u;l++)n[s][l]=i(t[s][l],e[s][l],r)}function o(t){return void 0!==t&&("string"!=typeof t&&"number"==typeof t.length)}function s(t){if(o(t)){var e=t.length;if(o(t[0])){for(var r=[],n=0;n<e;n++)r.push(ge.call(t[n]));return r}return ge.call(t)}return t}function u(t,e,r,n,i,a,o,s,u){var h=t.length;if(1==u)for(var c=0;c<h;c++)s[c]=l(t[c],e[c],r[c],n[c],i,a,o);else for(var f=t[0].length,c=0;c<h;c++)for(var d=0;d<f;d++)s[c][d]=l(t[c][d],e[c][d],r[c][d],n[c][d],i,a,o)}function l(t,e,r,n,i,a,o){var s=.5*(r-t),u=.5*(n-e);return(2*(e-r)+s+u)*o+(-3*(e-r)-2*s-u)*a+s*i+e}function h(t,e,r){var n=t.length,i=e.length;if(n!==i){if(n>i)t.length=i;else for(var a=n;a<i;a++)t.push(1===r?e[a]:ge.call(e[a]))}for(var o=t[0]&&t[0].length,a=0;a<t.length;a++)if(1===r)isNaN(t[a])&&(t[a]=e[a]);else for(var s=0;s<o;s++)isNaN(t[a][s])&&(t[a][s]=e[a][s])}function c(t,e,r){if(t===e)return!0;var n=t.length;if(n!==e.length)return!1;if(1===r){for(var i=0;i<n;i++)if(t[i]!==e[i])return!1}else for(var a=t[0].length,i=0;i<n;i++)for(var o=0;o<a;o++)if(t[i][o]!==e[i][o])return!1;return!0}function f(t,e,r,n,s,f,d){var p=t._getter,m=t._setter,_="spline"===e,g=n.length;if(g){var v=n[0].value,y=o(v),T=y&&o(v[0])?2:1;n.sort(function(t,e){return t.time-e.time});for(var x=[],E=[],A=[],b=n[0].value,S=!0,M=0;M<g;M++){x.push(n[M].time/d);var w=n[M].value;y&&c(w,b,T)||!y&&w===b||(S=!1),b=w,E.push(w),A.push(n[M].easing)}if(!S){for(var C=E[g-1],M=0;M<g-1;M++)y?h(E[M],C,T):isNaN(E[M])&&!isNaN(C)&&(E[M]=C);y&&h(p(t._target,s),C,T);var R,M,L,N,P,I,O,D=0,B=0,U=function(t,e){if(e<B){for(R=Math.min(D+1,g-1),M=R;M>=0&&!(x[M]<=e);M--);M=Math.min(M,g-2)}else{for(M=D;M<g&&!(x[M]>e);M++);M=Math.min(M-1,g-2)}D=M,B=e;var r=x[M+1]-x[M];0!==r&&(L=(e-x[M])/r,L=Math.max(Math.min(1,L),0),L=A[M+1](L),_?(P=E[M],N=E[0===M?M:M-1],I=E[M>g-2?g-1:M+1],O=E[M>g-3?g-1:M+2],f?m(t,s,f(p(t,s),N,P,I,O,L)):y?u(N,P,I,O,L,L*L,L*L*L,p(t,s),T):m(t,s,l(N,P,I,O,L,L*L,L*L*L))):f?m(t,s,f(p(t,s),E[M],E[M+1],L)):y?a(E[M],E[M+1],L,p(t,s),T):m(t,s,i(E[M],E[M+1],L)))},F=new _e({target:t._target,life:d,loop:t._loop,delay:t._delay,onframe:U,onfinish:r});return e&&"spline"!==e&&F.setEasing(e),F}}}function d(t,e,i,a,o){this._tracks={},this._target=t,this._loop=e||!1,this._getter=i||r,this._setter=a||n,this._interpolater=o||null,this._delay=0,this._doneList=[],this._onframeList=[],this._clipList=[],this._maxTime=0,this._lastKFTime=0}function p(t){return t}function m(t){var e,r,n,i,a,o,s=Number.POSITIVE_INFINITY,u=Number.POSITIVE_INFINITY,l=Number.NEGATIVE_INFINITY,h=Number.NEGATIVE_INFINITY;for(e=t.length;e--;)t[e][0]<s&&(s=t[e][0]),t[e][0]>l&&(l=t[e][0]),t[e][1]<u&&(u=t[e][1]),t[e][1]>h&&(h=t[e][1]);return r=l-s,n=h-u,i=Math.max(r,n),a=s+.5*r,o=u+.5*n,[[a-20*i,o-i],[a,o+20*i],[a+20*i,o-i]]}function _(t,e,r,n){var i,a,o,s,u,l,h,c,f,d,p=t[e][0],m=t[e][1],_=t[r][0],g=t[r][1],v=t[n][0],y=t[n][1],T=Math.abs(m-g),x=Math.abs(g-y);if(T<Te&&x<Te)throw new Error("Eek! Coincident points!");return T<Te?(s=-(v-_)/(y-g),l=(_+v)/2,c=(g+y)/2,i=(_+p)/2,a=s*(i-l)+c):x<Te?(o=-(_-p)/(g-m),u=(p+_)/2,h=(m+g)/2,i=(v+_)/2,a=o*(i-u)+h):(o=-(_-p)/(g-m),s=-(v-_)/(y-g),u=(p+_)/2,l=(_+v)/2,h=(m+g)/2,c=(g+y)/2,i=(o*u-s*l+c-h)/(o-s),a=T>x?o*(i-u)+h:s*(i-l)+c),f=_-i,d=g-a,{i:e,j:r,k:n,x:i,y:a,r:f*f+d*d}}function g(t){var e,r,n,i,a,o;for(r=t.length;r;)for(i=t[--r],n=t[--r],e=r;e;)if(o=t[--e],a=t[--e],n===a&&i===o||n===o&&i===a){t.splice(r,2),t.splice(e,2);break}}function v(t,e,r,n,i,a){var o=e[i],s=e[i+1],u=e[i+2];return t[0]=o+n*(r[a]-o),t[1]=s+n*(r[a+1]-s),t[2]=u+n*(r[a+2]-u),t}function y(t,e,r,n,i,a){var o,s,u,l,h,c=e[0+i],f=e[1+i],d=e[2+i],p=e[3+i],m=r[0+a],_=r[1+a],g=r[2+a],v=r[3+a];return s=c*m+f*_+d*g+p*v,s<0&&(s=-s,m=-m,_=-_,g=-g,v=-v),1-s>1e-6?(o=Math.acos(s),u=Math.sin(o),l=Math.sin((1-n)*o)/u,h=Math.sin(n*o)/u):(l=1-n,h=n),t[0]=l*c+h*m,t[1]=l*f+h*_,t[2]=l*d+h*g,t[3]=l*p+h*v,t}function T(t,e,r){"object"==typeof e&&(r=e,e=null);var n,i=this;if(!(t instanceof Function)){n=[];for(var a in t)t.hasOwnProperty(a)&&n.push(a)}var o=function(e){if(i.apply(this,arguments),t instanceof Function?x(this,t.call(this,e)):E(this,t,n),this.constructor===o)for(var r=o.__initializers__,a=0;a<r.length;a++)r[a].apply(this,arguments)};o.__super__=i,i.__initializers__?o.__initializers__=i.__initializers__.slice():o.__initializers__=[],e&&o.__initializers__.push(e);var s=function(){};return s.prototype=i.prototype,o.prototype=new s,o.prototype.constructor=o,x(o.prototype,r),o.extend=i.extend,o.derive=i.extend,o}function x(t,e){if(e)for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])}function E(t,e,r){for(var n=0;n<r.length;n++){var i=r[n];t[i]=e[i]}}function A(t,e){this.action=t,this.context=e}function b(t){var e=new XMLHttpRequest;e.open("get",t.url),e.responseType=t.responseType||"text",t.onprogress&&(e.onprogress=function(e){if(e.lengthComputable){var r=e.loaded/e.total;t.onprogress(r,e.loaded,e.total)}else t.onprogress(null)}),e.onload=function(r){e.status>=400?t.onerror&&t.onerror():t.onload&&t.onload(e.response)},t.onerror&&(e.onerror=t.onerror),e.send(null)}function S(t){function e(e){if(t.getExtension){var n=t.getExtension(e);n||(n=t.getExtension("MOZ_"+e)),n||(n=t.getExtension("WEBKIT_"+e)),r[e]=n}}for(var r={},n={},i=0;i<je.length;i++){e(je[i])}for(var i=0;i<qe.length;i++){var a=qe[i];n[a]=t.getParameter(t[a])}this.getExtension=function(t){return t in r||e(t),r[t]},this.getParameter=function(t){return n[t]}}function M(t){return t=Math.round(t),t<0?0:t>255?255:t}function w(t){return t=Math.round(t),t<0?0:t>360?360:t}function C(t){return t<0?0:t>1?1:t}function R(t){return M(t.length&&"%"===t.charAt(t.length-1)?parseFloat(t)/100*255:parseInt(t,10))}function L(t){return C(t.length&&"%"===t.charAt(t.length-1)?parseFloat(t)/100:parseFloat(t))}function N(t,e,r){return r<0?r+=1:r>1&&(r-=1),6*r<1?t+(e-t)*r*6:2*r<1?e:3*r<2?t+(e-t)*(2/3-r)*6:t}function P(t,e,r){return t+(e-t)*r}function I(t,e,r,n,i){return t[0]=e,t[1]=r,t[2]=n,t[3]=i,t}function O(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}function D(t,e){tr&&O(tr,e),tr=$e.put(t,tr||e.slice())}function B(t,e){var r=(parseFloat(t[0])%360+360)%360/360,n=L(t[1]),i=L(t[2]),a=i<=.5?i*(n+1):i+n-i*n,o=2*i-a;return e=e||[],I(e,M(255*N(o,a,r+1/3)),M(255*N(o,a,r)),M(255*N(o,a,r-1/3)),1),4===t.length&&(e[3]=t[3]),e}function U(t){if(t){var e,r,n=t[0]/255,i=t[1]/255,a=t[2]/255,o=Math.min(n,i,a),s=Math.max(n,i,a),u=s-o,l=(s+o)/2;if(0===u)e=0,r=0;else{r=l<.5?u/(s+o):u/(2-s-o);var h=((s-n)/6+u/2)/u,c=((s-i)/6+u/2)/u,f=((s-a)/6+u/2)/u;n===s?e=f-c:i===s?e=1/3+h-f:a===s&&(e=2/3+c-h),e<0&&(e+=1),e>1&&(e-=1)}var d=[360*e,r,l];return null!=t[3]&&d.push(t[3]),d}}function F(t){var e=Object.keys(t);e.sort();for(var r=[],n=0;n<e.length;n++){var i=e[n],a=t[i];null===a?r.push(i):r.push(i+" "+a.toString())}return r.join("\n")}function k(t,e,r){r.sort();for(var n=[],i=0;i<r.length;i++){var a=r[i];n.push(a)}var o=F(t)+"\n"+F(e)+"\n"+n.join("\n");if(rr[o])return rr[o];var s=Fe.genGUID();return rr[o]=s,s}function H(t){for(var e=t.split("\n"),r=0,n=e.length;r<n;r++)e[r]=r+1+": "+e[r];return e.join("\n")}function G(t,e,r){if(!t.getShaderParameter(e,t.COMPILE_STATUS))return[t.getShaderInfoLog(e),H(r)].join("\n")}function V(t,e,r){function n(t,r,n,a){var o="";isNaN(r)&&(r=r in e?e[r]:i[r]),isNaN(n)&&(n=n in e?e[n]:i[n]);for(var s=parseInt(r);s<parseInt(n);s++)o+="{"+a.replace(/float\s*\(\s*_idx_\s*\)/g,s.toFixed(1)).replace(/_idx_/g,s)+"}";return o}var i={};for(var a in r)i[a+"_COUNT"]=r[a];return t.replace(sr,n)}function W(t,e,r){var n=[];if(e)for(var i in e){var a=e[i];a>0&&n.push("#define "+i.toUpperCase()+"_COUNT "+a)}if(r)for(var o=0;o<r.length;o++){var s=r[o];n.push("#define "+s.toUpperCase()+"_ENABLED")}for(var s in t){var u=t[s];null===u?n.push("#define "+s):n.push("#define "+s+" "+u.toString())}return n.join("\n")}function z(t){for(var e=[],r=0;r<t.length;r++)e.push("#extension GL_"+t[r]+" : enable");return e.join("\n")}function X(t){return["precision",t,"float"].join(" ")+";\n"+["precision",t,"int"].join(" ")+";\n"+["precision",t,"sampler2D"].join(" ")+";\n"}function j(t){this._renderer=t,this._cache={}}function q(t){for(var e=[],r=0;r<t;r++)e[r]=0;return e}function Y(t,e){var r="vertex:"+t+"fragment:"+e;if(gr[r])return gr[r];var n=Fe.genGUID();return gr[r]=n,vr[n]={vertex:t,fragment:e},n}function K(t){return t.replace(/[ \t]*\/\/.*\n/g,"").replace(/[ \t]*\/\*[\s\S]*?\*\//g,"")}function Z(){console.error("Wrong uniform/attributes syntax")}function J(t,e){function r(t){t||Z();var e=t.match(/\[(.*?)\]/);s=t.replace(/\[(.*?)\]/,""),l[s]={},e&&(l[s].isArray=!0,l[s].arraySize=e[1])}for(var n=/[,=\(\):]/,i=e.replace(/:\s*\[\s*(.*)\s*\]/g,"="+t+"($1)").replace(/\s+/g,"").split(/(?=[,=\(\):])/g),a=[],o=0;o<i.length;o++)i[o].match(n)?a.push(i[o].charAt(0),i[o].slice(1)):a.push(i[o]);i=a;var s,u=0,l={},h=null;r(i[0]);for(var o=1;o<i.length;o++){var c=i[o];if(c)if("="!==c)if(":"!==c)if(","!==c)if(")"!==c)if("("!==c)if(c.indexOf("vec")>=0){if(1!==u&&4!==u){Z();break}u=2,h=[]}else if(1!==u)if(4!==u)r(c),u=0;else{var f=c;dr.indexOf(f)>=0||pr.indexOf(f)>=0||mr.indexOf(f)>=0?l[s].semantic=f:"ignore"===f||"unconfigurable"===f?l[s].ignore=!0:l[s].value="bool"===t?"true"===f:parseFloat(f)}else l[s].value="bool"===t?"true"===c:parseFloat(c),h=null;else{if(2!==u){Z();break}if(!(h instanceof Array)){Z();break}h.push(+i[++o])}else l[s].value=new Ve.Float32Array(h),h=null,u=5;else if(2===u){if(!(h instanceof Array)){Z();break}h.push(+i[++o])}else u=5;else u=4;else{if(0!==u&&3!==u){Z();break}u=1}}return l}function Q(t,e){"object"==typeof t&&(e=t.fragment,t=t.vertex),t=K(t),e=K(e),this._shaderID=Y(t,e),this._vertexCode=Q.parseImport(t),this._fragmentCode=Q.parseImport(e),this.attributeSemantics={},this.matrixSemantics={},this.uniformSemantics={},this.matrixSemanticKeys=[],this.uniformTemplates={},this.attributes={},this.textures={},this.vertexDefines={},this.fragmentDefines={},this._parseAttributes(),this._parseUniforms(),this._parseDefines()}function $(t){return t.material}function tt(t,e,r){return e.uniforms[r].value}function et(t,e,r,n){return r!==n}function rt(t){return!0}function nt(){}function it(t,e,r){this.availableAttributes=t,this.availableAttributeSymbols=e,this.indicesBuffer=r,this.vao=null}function at(t){var e,r;this.bind=function(t){e||(e=Ve.createCanvas(),e.width=e.height=1,e.getContext("2d"));var n=t.gl,i=!r;i&&(r=n.createTexture()),n.bindTexture(n.TEXTURE_2D,r),i&&n.texImage2D(n.TEXTURE_2D,0,n.RGBA,n.RGBA,n.UNSIGNED_BYTE,e)},this.unbind=function(t){t.gl.bindTexture(t.gl.TEXTURE_2D,null)},this.isRenderable=function(){return!0}}function ot(t,e,r){return t<e?e:t>r?r:t}function st(t){var e=[],r=Object.keys(t);r.sort();for(var n=0;n<r.length;n++){var i=r[n];e.push(i+" "+t[i])}var a=e.join("\n");if(on[a])return on[a];var o=Fe.genGUID();return on[a]=o,o}function ut(){this.opaque=[],this.transparent=[],this._opaqueCount=0,this._transparentCount=0}function lt(t,e){if(e.castShadow&&!t.castShadow)return!0}function ht(t){return{byte:Ve.Int8Array,ubyte:Ve.Uint8Array,short:Ve.Int16Array,ushort:Ve.Uint16Array}[t]||Ve.Float32Array}function ct(t){return"attr_"+t}function ft(t,e,r,n){switch(this.name=t,this.type=e,this.size=r,this.semantic=n||"",this.value=null,r){case 1:this.get=function(t){return this.value[t]},this.set=function(t,e){this.value[t]=e},this.copy=function(t,e){this.value[t]=this.value[t]};break;case 2:this.get=function(t,e){var r=this.value;return e[0]=r[2*t],e[1]=r[2*t+1],e},this.set=function(t,e){var r=this.value;r[2*t]=e[0],r[2*t+1]=e[1]},this.copy=function(t,e){var r=this.value;e*=2,t*=2,r[t]=r[e],r[t+1]=r[e+1]};break;case 3:this.get=function(t,e){var r=3*t,n=this.value;return e[0]=n[r],e[1]=n[r+1],e[2]=n[r+2],e},this.set=function(t,e){var r=3*t,n=this.value;n[r]=e[0],n[r+1]=e[1],n[r+2]=e[2]},this.copy=function(t,e){var r=this.value;e*=3,t*=3,r[t]=r[e],r[t+1]=r[e+1],r[t+2]=r[e+2]};break;case 4:this.get=function(t,e){var r=this.value,n=4*t;return e[0]=r[n],e[1]=r[n+1],e[2]=r[n+2],e[3]=r[n+3],e},this.set=function(t,e){var r=this.value,n=4*t;r[n]=e[0],r[n+1]=e[1],r[n+2]=e[2],r[n+3]=e[3]},this.copy=function(t,e){var r=this.value;e*=4,t*=4,r[t]=r[e],r[t+1]=r[e+1],r[t+2]=r[e+2],r[t+3]=r[e+3]}}}function dt(t,e,r,n,i){this.name=t,this.type=e,this.buffer=r,this.size=n,this.semantic=i,this.symbol="",this.needsRemove=!1}function pt(t){this.buffer=t,this.count=0}function mt(t,e,r){_n.identity();var n=new mn({widthSegments:e,heightSegments:r});switch(t){case"px":Ur.translate(_n,_n,Cr.POSITIVE_X),Ur.rotateY(_n,_n,Math.PI/2);break;case"nx":Ur.translate(_n,_n,Cr.NEGATIVE_X),Ur.rotateY(_n,_n,-Math.PI/2);break;case"py":Ur.translate(_n,_n,Cr.POSITIVE_Y),Ur.rotateX(_n,_n,-Math.PI/2);break;case"ny":Ur.translate(_n,_n,Cr.NEGATIVE_Y),Ur.rotateX(_n,_n,Math.PI/2);break;case"pz":Ur.translate(_n,_n,Cr.POSITIVE_Z);break;case"nz":Ur.translate(_n,_n,Cr.NEGATIVE_Z),Ur.rotateY(_n,_n,Math.PI)}return n.applyTransform(_n),n}function _t(t){return Math.pow(2,Math.round(Math.log(t)/Math.LN2))}function gt(t,e){var r=_t(t.width),n=_t(t.height);return e=e||document.createElement("canvas"),e.width=r,e.height=n,e.getContext("2d").drawImage(t.image,0,0,r,n),e}function vt(t){return t.width>0&&t.height>0}function yt(){this._pool={}}function Tt(t,e,r){kn[t]={vertex:e,fragment:r}}function xt(t,e,r,n){var i=t.accessors[r],a=e.bufferViews[i.bufferView],o=i.byteOffset||0,s=Kn[i.componentType]||Ve.Float32Array,u=Zn[i.type];null==u&&n&&(u=1);var l=new s(a,o,u*i.count),h=i.extensions&&i.extensions.WEB3D_quantized_attributes;if(h){for(var c,f,d=new Ve.Float32Array(u*i.count),p=h.decodeMatrix,c=new Array(u),f=new Array(u),m=0;m<u;m++)c[m]=p[u*(u+1)+m],f[m]=p[m*(u+1)+m];for(var _=0;_<i.count;_++)for(var m=0;m<u;m++)d[_*u+m]=l[_*u+m]*f[m]+c[m];l=d}return l}function Et(t,e){for(var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n=new Uint8Array(130),i=0;i<r.length;i++)n[r.charCodeAt(i)]=i;var a=t.length-e;"="===t.charAt(a-1)&&a--,"="===t.charAt(a-1)&&a--;for(var o=new Uint8Array(a/4*3),i=0,s=e;i<o.length;){var u=n[t.charCodeAt(s++)],l=n[t.charCodeAt(s++)],h=n[t.charCodeAt(s++)],c=n[t.charCodeAt(s++)];o[i++]=u<<2|l>>4,o[i++]=(15&l)<<4|h>>2,o[i++]=(3&h)<<6|c}return o.buffer}function At(t){return t.charCodeAt(0)+(t.charCodeAt(1)<<8)+(t.charCodeAt(2)<<16)+(t.charCodeAt(3)<<24)}function bt(t,e,r,n){if(t[3]>0){var i=Math.pow(2,t[3]-128-8+n);e[r+0]=t[0]*i,e[r+1]=t[1]*i,e[r+2]=t[2]*i}else e[r+0]=0,e[r+1]=0,e[r+2]=0;return e[r+3]=1,e}function St(t,e,r){for(var n="",i=e;i<r;i++)n+=vi(t[i]);return n}function Mt(t,e){e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3]}function wt(t,e,r,n){for(var i=0,a=0,o=n;o>0;)if(t[a][0]=e[r++],t[a][1]=e[r++],t[a][2]=e[r++],t[a][3]=e[r++],1===t[a][0]&&1===t[a][1]&&1===t[a][2]){for(var s=t[a][3]<<i>>>0;s>0;s--)Mt(t[a-1],t[a]),a++,o--;i+=8}else a++,o--,i=0;return r}function Ct(t,e,r,n){if(n<yi|n>Ti)return wt(t,e,r,n);var i=e[r++];if(2!=i)return wt(t,e,r-1,n);if(t[0][1]=e[r++],t[0][2]=e[r++],i=e[r++],(t[0][2]<<8>>>0|i)>>>0!==n)return null;for(var i=0;i<4;i++)for(var a=0;a<n;){var o=e[r++];if(o>128){o=(127&o)>>>0;for(var s=e[r++];o--;)t[a++][i]=s}else for(;o--;)t[a++][i]=e[r++]}return r}function Rt(t){Fe.defaultsWithPropList(t,Ci,Ri),Lt(t);for(var e="",r=0;r<Ri.length;r++){e+=t[Ri[r]].toString()}return e}function Lt(t){var e=Nt(t.width,t.height);t.format===Ye.DEPTH_COMPONENT&&(t.useMipmap=!1),e&&t.useMipmap||(t.minFilter==Ye.NEAREST_MIPMAP_NEAREST||t.minFilter==Ye.NEAREST_MIPMAP_LINEAR?t.minFilter=Ye.NEAREST:t.minFilter!=Ye.LINEAR_MIPMAP_LINEAR&&t.minFilter!=Ye.LINEAR_MIPMAP_NEAREST||(t.minFilter=Ye.LINEAR)),e||(t.wrapS=Ye.CLAMP_TO_EDGE,t.wrapT=Ye.CLAMP_TO_EDGE)}function Nt(t,e){return 0==(t&t-1)&&0==(e&e-1)}function Pt(t,e,r){if("alphaMap"===r)return t.material.get("diffuseMap");if("alphaCutoff"===r){if(t.material.isDefined("fragment","ALPHA_TEST")&&t.material.get("diffuseMap")){return t.material.get("alphaCutoff")||0}return 0}return"uvRepeat"===r?t.material.get("uvRepeat"):"uvOffset"===r?t.material.get("uvOffset"):e.get(r)}function It(t,e){var r=t.material,n=e.material;return r.get("diffuseMap")!==n.get("diffuseMap")||(r.get("alphaCutoff")||0)!==(n.get("alphaCutoff")||0)}function Ot(t,e){var r=t[0],n=t[1],i=t[2];return 0===e?1:1===e?r:2===e?n:3===e?i:4===e?r*i:5===e?n*i:6===e?r*n:7===e?3*i*i-1:r*r-n*n}function Dt(t,e,r,n){for(var i=new Ve.Float32Array(27),a=Ce.create(),o=Ce.create(),s=Ce.create(),u=0;u<9;u++){for(var l=Ce.create(),h=0;h<Oi.length;h++){for(var c=e[Oi[h]],f=Ce.create(),d=0,p=0,m=Di[Oi[h]],_=0;_<n;_++)for(var g=0;g<r;g++){a[0]=g/(r-1)*2-1,a[1]=_/(n-1)*2-1,a[2]=-1,Ce.normalize(a,a),s[0]=a[m[0]]*m[3],s[1]=a[m[1]]*m[4],s[2]=a[m[2]]*m[5],o[0]=c[p++]/255,o[1]=c[p++]/255,o[2]=c[p++]/255;var v=c[p++]/255*8.12;o[0]*=v,o[1]*=v,o[2]*=v,Ce.scaleAndAdd(f,f,o,Ot(s,u)*-a[2]),d+=-a[2]}Ce.scaleAndAdd(l,l,f,1/d)}i[3*u]=l[0]/6,i[3*u+1]=l[1]/6,i[3*u+2]=l[2]/6}return i}function Bt(t,e){if(e=e||{},e.graphic=e.graphic||{},null==e.autoRender&&(e.autoRender=!0),"string"==typeof t&&(t=document.querySelector(t)),!t)throw new Error("Invalid dom");var r=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase(),n={};r&&(n.canvas=t),e.devicePixelRatio&&(n.devicePixelRatio=e.devicePixelRatio);var i=new Mr(n),a=e.width||t.clientWidth,o=e.height||t.clientHeight,s=new sn,u=new ze,l=e.graphic.shadow&&new Ni,h=e.event&&new Pi({scene:s,renderer:i});!r&&t.appendChild(i.canvas),i.resize(a,o);var c=0,f=0;u.start();var d={};for(var p in e.methods)d[p]=e.methods[p].bind(e,this);Object.defineProperties(this,{container:{get:function(){return t}},renderer:{get:function(){return i}},scene:{get:function(){return s}},timeline:{get:function(){return u}},frameTime:{get:function(){return c}},elapsedTime:{get:function(){return f}},width:{get:function(){return i.getWidth()}},height:{get:function(){return i.getHeight()}},methods:{get:function(){return d}},_shadowPass:{get:function(){return l}},_appNS:{get:function(){return e}}}),this.resize=function(r,n){a=r||e.width||t.clientWidth,o=n||t.height||t.clientHeight,i.resize(a,o)},this.dispose=function(){this._disposed=!0,e.dispose&&e.dispose(this),u.stop(),i.disposeScene(s),l&&l.dispose(i),t.innerHTML="",Ui.forEach(function(e){this[kt(e)]&&Ve.removeEventListener(t,kt(e))},this)},h&&this._initMouseEvents(h),this._geoCache=new Ze(20),this._texCache=new Ze(20),this._texturesList={},this._geometriesList={};var m=Promise.resolve(e.init&&e.init(this));h&&(h.camera=s.getMainCamera()),e.loop||console.warn("Miss loop method.");var _=this;m.then(function(){u.on("frame",function(t){c=t,f+=t;var r=s.getMainCamera();r&&(r.aspect=i.getViewportAspect()),h&&(h.camera=r),e.loop&&e.loop(_),e.autoRender&&_.render(),_.collectResources()},this)}),s.on("beforerender",function(t,r,n,i){this._inRender&&(this._updateGraphicOptions(e.graphic,i.opaque,!1),this._updateGraphicOptions(e.graphic,i.transparent,!1))},this)}function Ut(t){return"undefined"!=typeof Image&&t instanceof Image||"undefined"!=typeof HTMLCanvasElement&&t instanceof HTMLCanvasElement||"undefined"!=typeof HTMLVideoElement&&t instanceof HTMLVideoElement}function Ft(t){return"string"==typeof t?t:t.__key__||(t.__key__=Fe.genGUID())}function kt(t){return"_"+t+"Handler"}function Ht(t,e,r,n,i){var a=Fe.clone(e);return a.type=t,a.offsetX=r,a.offsetY=n,null!==i&&(a.wheelDelta=i),a}function Gt(t,e){for(;t&&!e.cancelBubble;)t.trigger(e.type,e),t=t.getParent()}function Vt(t){for(var e=0;e<t.length;e++)t[e].__used=0}function Wt(t,e){for(var r=0;r<e.length;r++)e[r].__used||e[r].dispose(t)}function zt(t,e){t.__used=t.__used||0,1===++t.__used&&e.push(t)}function Xt(t,e,r){var n,i;t.traverse(function(t){if(t.isRenderable()){var a=t.geometry,o=t.material;if(o!==n)for(var s=o.getTextureUniforms(),u=0;u<s.length;u++){var l=s[u],h=o.uniforms[l].value,c=o.uniforms[l].type;if(h)if("t"===c)zt(h,e);else if("tv"===c)for(var f=0;f<h.length;f++)h[f]&&zt(h[f],e)}a!==i&&zt(a,r),n=o,i=a}});for(var a=0;a<t.lights.length;a++)t.lights[a].cubemap&&zt(t.lights[a].cubemap,e)}function jt(){this._fullfilled=!1,this._rejected=!1}function qt(t,e){var r=new jt;return Ve.request.get({url:t,responseType:e,onload:function(t){r.resolve(t)},onerror:function(t){r.reject(t)}}),r}function Yt(t){t.import(qi),t.import(Yi),t.import(Ki),t.import(Zi),t.import(Ji),t.import(Qi),t.import($i),t.import(ta),t.import(ea),t.import(ra),t.import(na),t.import(ia),t.import(aa)}function Kt(t,e){var r=new Wi;e=e||{};var n={textures:{},parameters:{}},i=function(i,a){for(var o=0;o<t.nodes.length;o++){var s=t.nodes[o],u=Zt(s,n,e);u&&r.addNode(u)}};for(var a in t.parameters){var o=t.parameters[a];n.parameters[a]=$t(o)}return te(t,n,e,function(t){n.textures=t,i()}),r}function Zt(t,e,r){var n,i,a,o=t.type||"filter";if("filter"===o){var s=t.shader.trim(),u=oa.exec(s);if(u?n=Q.source(u[1].trim()):"#"===s.charAt(0)&&(n=e.shaders[s.substr(1)]),n||(n=s),!n)return}if(t.inputs){i={};for(var l in t.inputs)"string"==typeof t.inputs[l]?i[l]=t.inputs[l]:i[l]={node:t.inputs[l].node,pin:t.inputs[l].pin}}if(t.outputs){a={};for(var l in t.outputs){var h=t.outputs[l];a[l]={},null!=h.attachment&&(a[l].attachment=h.attachment),null!=h.keepLastFrame&&(a[l].keepLastFrame=h.keepLastFrame),null!=h.outputLastFrame&&(a[l].outputLastFrame=h.outputLastFrame),h.parameters&&(a[l].parameters=$t(h.parameters))}}var c;if(c="scene"===o?new zi({name:t.name,scene:r.scene,camera:r.camera,outputs:a}):"texture"===o?new Xi({name:t.name,outputs:a}):new ji({name:t.name,shader:n,inputs:i,outputs:a})){if(t.parameters)for(var l in t.parameters){var f=t.parameters[l];"string"==typeof f?(f=f.trim(),"#"===f.charAt(0)?f=e.textures[f.substr(1)]:c.on("beforerender",ee(l,ne(f)))):"function"==typeof f&&c.on("beforerender",f),c.setParameter(l,f)}if(t.defines&&c.pass)for(var l in t.defines){var f=t.defines[l];c.pass.material.define("fragment",l,f)}}return c}function Jt(t,e){return t}function Qt(t,e){return e}function $t(t){var e={};if(!t)return e;["type","minFilter","magFilter","wrapS","wrapT","flipY","useMipmap"].forEach(function(r){var n=t[r];null!=n&&("string"==typeof n&&(n=Tn[n]),e[r]=n)});var r=t.scale||1;return["width","height"].forEach(function(n){if(null!=t[n]){var i=t[n];"string"==typeof i?(i=i.trim(),e[n]=re(n,ne(i),r)):e[n]=i}}),e.width||(e.width=Jt),e.height||(e.height=Qt),null!=t.useMipmap&&(e.useMipmap=t.useMipmap),e}function te(t,e,r,n){if(!t.textures)return void n({});var i={},a=0,o=!1,s=r.textureRootPath;Fe.each(t.textures,function(t,e){var r,u=t.path,l=$t(t.parameters);if(Array.isArray(u)&&6===u.length)s&&(u=u.map(function(t){return Fe.relative2absolute(t,s)})),r=new Mn(l);else{if("string"!=typeof u)return;s&&(u=Fe.relative2absolute(u,s)),r=new An(l)}r.load(u),a++,r.once("success",function(){i[e]=r,0===--a&&(n(i),o=!0)})}),0!==a||o||n(i)}function ee(t,e){return function(r){var n=r.getDevicePixelRatio(),i=r.getWidth(),a=r.getHeight(),o=e(i,a,n);this.setParameter(t,o)}}function re(t,e,r){return r=r||1,function(t){var n=t.getDevicePixelRatio(),i=t.getWidth()*r,a=t.getHeight()*r;return e(i,a,n)}}function ne(t){var e=/^expr\((.*)\)$/.exec(t);if(e)try{var r=new Function("width","height","dpr","return "+e[1]);return r(1,1),r}catch(t){throw new Error("Invalid expression.")}}function ie(t){var e=document.createElement("canvas");e.width=e.height=1;var r=e.getContext("2d");return r.fillStyle=t||"#000",r.fillRect(0,0,1,1),e}function ae(t,e,r){return function(e,n,i){var a=e.material;if("doubleSided"===i)return a.isDefined("fragment","DOUBLE_SIDED");if("uvRepeat"===i||"uvOffset"===i||"alpha"===i)return a.get(i);if("normalMap"===i)return a.get(i)||t;if("diffuseMap"===i)return a.get(i)||r;if("alphaCutoff"===i){if(a.isDefined("fragment","ALPHA_TEST")){return a.get("alphaCutoff")||0}return 0}var o=a.isDefined("fragment","USE_ROUGHNESS"),s=o?a.get("roughnessMap"):a.get("glossinessMap");switch(i){case"glossiness":return o?1-a.get("roughness"):a.get("glossiness");case"roughGlossMap":return s;case"useRoughGlossMap":return!!s;case"useRoughness":return o;case"roughGlossChannel":return o?a.getDefine("fragment","ROUGHNESS_CHANNEL"):a.getDefine("fragment","GLOSSINESS_CHANNEL")}}}function oe(t,e){return function(r,n,i){var a=r.material;switch(i){case"color":case"uvRepeat":case"uvOffset":case"alpha":return a.get(i);case"metalness":return a.get("metalness")||0;case"diffuseMap":return a.get(i)||t;case"metalnessMap":return a.get(i)||e;case"useMetalnessMap":return!!a.get("metalnessMap");case"linear":return a.isDefined("SRGB_DECODE");case"alphaCutoff":if(a.isDefined("fragment","ALPHA_TEST")){return a.get("alphaCutoff")||0}return 0}}}function se(t){var e=t>>16,r=t-(e<<8)>>8;return[e,r,t-(e<<16)-(r<<8)]}function ue(t,e,r){return(t<<16)+(e<<8)+r}function le(t){var e=t[1][0]-t[0][0],r=t[1][1]-t[0][1];return Math.sqrt(e*e+r*r)}function he(t){return[(t[0][0]+t[1][0])/2,(t[0][1]+t[1][1])/2]}function ce(t){return Array.isArray(t)||(t=[t,t]),t}function fe(t,e){return{name:t.name,type:t.type,size:t.size,semantic:t.semantic,value:de(t.value,e)}}function de(t,e){return e?t:new t.constructor(t)}function pe(t,e,r){return t*(1-r)+e*r}var me={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,r=.1;return 0===t?0:1===t?1:(!r||r<1?(r=1,e=.1):e=.4*Math.asin(1/r)/(2*Math.PI),-r*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/.4))},elasticOut:function(t){var e,r=.1;return 0===t?0:1===t?1:(!r||r<1?(r=1,e=.1):e=.4*Math.asin(1/r)/(2*Math.PI),r*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,r=.1;return 0===t?0:1===t?1:(!r||r<1?(r=1,e=.1):e=.4*Math.asin(1/r)/(2*Math.PI),(t*=2)<1?r*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/.4)*-.5:r*Math.pow(2,-10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/.4)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((e+1)*t-e)*.5:.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(t){return 1-me.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*me.bounceIn(2*t):.5*me.bounceOut(2*t-1)+.5}},_e=function(t){t=t||{},this.name=t.name||"",this.target=t.target,this.life=t.life||1e3,this.delay=t.delay||0,this.gap=t.gap||0,this.playbackRate=t.playbackRate||1,this._initialized=!1,this._elapsedTime=0,this._loop=null!=t.loop&&t.loop,this.setLoop(this._loop),null!=t.easing&&this.setEasing(t.easing),this.onframe=t.onframe||e,this.onfinish=t.onfinish||e,this.onrestart=t.onrestart||e,this._paused=!1};_e.prototype={gap:0,life:0,delay:0,setLoop:function(t){this._loop=t,t&&(this._loopRemained="number"==typeof t?t:1/0)},setEasing:function(t){"string"==typeof t&&(t=me[t]),this.easing=t},step:function(t,e,r){if(this._initialized||(this._startTime=t+this.delay,this._initialized=!0),null!=this._currentTime&&(e=t-this._currentTime),this._currentTime=t,this._paused)return"paused";if(!(t<this._startTime)){this._elapse(t,e);var n=Math.min(this._elapsedTime/this.life,1);if(!(n<0)){var i;return i=this.easing?this.easing(n):n,r||this.fire("frame",i),1===n?this._loop&&this._loopRemained>0?(this._restartInLoop(t),this._loopRemained--,"restart"):(this._needsRemove=!0,"finish"):null}}},setTime:function(t){return this.step(t+this._startTime)},restart:function(t){var e=0;t&&(this._elapse(t),e=this._elapsedTime%this.life),t=t||Date.now(),this._startTime=t-e+this.delay,this._elapsedTime=0,this._needsRemove=!1,this._paused=!1},getElapsedTime:function(){return this._elapsedTime},_restartInLoop:function(t){this._startTime=t+this.gap,this._elapsedTime=0},_elapse:function(t,e){this._elapsedTime+=e*this.playbackRate},fire:function(t,e){var r="on"+t;this[r]&&this[r](this.target,e)},clone:function(){var t=new this.constructor;return t.name=this.name,t._loop=this._loop,t._loopRemained=this._loopRemained,t.life=this.life,t.gap=this.gap,t.delay=this.delay,t},pause:function(){this._paused=!0},resume:function(){this._paused=!1}},_e.prototype.constructor=_e;var ge=Array.prototype.slice;d.prototype={constructor:d,when:function(t,e,r){this._maxTime=Math.max(t,this._maxTime),r=("function"==typeof r?r:me[r])||p;for(var n in e)this._tracks[n]||(this._tracks[n]=[],0!==t&&this._tracks[n].push({time:0,value:s(this._getter(this._target,n)),easing:r})),this._tracks[n].push({time:parseInt(t),value:e[n],easing:r});return this},then:function(t,e,r){return this.when(t+this._lastKFTime,e,r),this._lastKFTime+=t,this},during:function(t){return this._onframeList.push(t),this},_doneCallback:function(){this._tracks={},this._clipList.length=0;for(var t=this._doneList,e=t.length,r=0;r<e;r++)t[r].call(this)},start:function(t){var e,r=this,n=0,i=function(){0===--n&&r._doneCallback()};for(var a in this._tracks){var o=f(this,t,i,this._tracks[a],a,r._interpolater,r._maxTime);o&&(this._clipList.push(o),n++,this.animation&&this.animation.addClip(o),e=o)}if(e){var s=e.onframe;e.onframe=function(t,e){s(t,e);for(var n=0;n<r._onframeList.length;n++)r._onframeList[n](t,e)}}return n||this._doneCallback(),this},stop:function(){for(var t=0;t<this._clipList.length;t++){var e=this._clipList[t];this.animation.removeClip(e)}this._clipList=[]},delay:function(t){return this._delay=t,this},done:function(t){return t&&this._doneList.push(t),this},getClips:function(){return this._clipList}};var ve=function(t,e){return t.position<e.position},ye=function(t){t=t||{},_e.call(this,t),this.output=t.output||null,this.inputs=t.inputs||[],this.position=0,this._cacheKey=0,this._cachePosition=-1/0,this.inputs.sort(ve)};ye.prototype=new _e,ye.prototype.constructor=ye,ye.prototype.addInput=function(t,e,r){var n={position:t,clip:e,offset:r||0};if(this.life=Math.max(e.life,this.life),!this.inputs.length)return this.inputs.push(n),n;var i=this.inputs.length;if(this.inputs[0].position>t)this.inputs.unshift(n);else if(this.inputs[i-1].position<=t)this.inputs.push(n);else{var a=this._findKey(t);this.inputs.splice(a,n)}return n},ye.prototype.step=function(t,e,r){var n=_e.prototype.step.call(this,t);return"finish"!==n&&this.setTime(this.getElapsedTime()),r||"paused"===n||this.fire("frame"),n},ye.prototype.setTime=function(t){var e=this.position,r=this.inputs,n=r.length,i=r[0].position,a=r[n-1].position;if(e<=i||e>=a){var o=e<=i?r[0]:r[n-1],s=o.clip,u=o.offset;s.setTime((t+u)%s.life),s.output instanceof _e?this.output.copy(s.output):this.output.copy(s)}else{var l=this._findKey(e),h=r[l],c=r[l+1],f=h.clip,d=c.clip;f.setTime((t+h.offset)%f.life),d.setTime((t+c.offset)%d.life)
;var p=(this.position-h.position)/(c.position-h.position),m=f.output instanceof _e?f.output:f,_=d.output instanceof _e?d.output:d;this.output.blend1D(m,_,p)}},ye.prototype.clone=function(t){var e=_e.prototype.clone.call(this);e.output=this.output.clone();for(var r=0;r<this.inputs.length;r++){var n=t?this.inputs[r].clip.clone(!0):this.inputs[r].clip;e.addInput(this.inputs[r].position,n,this.inputs[r].offset)}return e},ye.prototype._findKey=function(t){var e=-1,r=this.inputs,n=r.length;if(this._cachePosition<t)for(var i=this._cacheKey;i<n-1;i++)t>=r[i].position&&t<r[i+1].position&&(e=i);else for(var a=Math.min(n-2,this._cacheKey),i=a;i>=0;i--)t>=r[i].position&&t<r[i+1].position&&(e=i);return e>=0&&(this._cacheKey=e,this._cachePosition=t),e};var Te=1/1048576,xe={triangulate:function(t,e){var r,n,i,a,o,s,u,l,h,c,f,d,p=t.length;if(p<3)return[];if(t=t.slice(0),e)for(r=p;r--;)t[r]=t[r][e];for(i=new Array(p),r=p;r--;)i[r]=r;for(i.sort(function(e,r){var n=t[r][0]-t[e][0];return 0!==n?n:e-r}),a=m(t),t.push(a[0],a[1],a[2]),o=[_(t,p+0,p+1,p+2)],s=[],u=[],r=i.length;r--;u.length=0){for(d=i[r],n=o.length;n--;)l=t[d][0]-o[n].x,l>0&&l*l>o[n].r?(s.push(o[n]),o.splice(n,1)):(h=t[d][1]-o[n].y,l*l+h*h-o[n].r>Te||(u.push(o[n].i,o[n].j,o[n].j,o[n].k,o[n].k,o[n].i),o.splice(n,1)));for(g(u),n=u.length;n;)f=u[--n],c=u[--n],o.push(_(t,c,f,d))}for(r=o.length;r--;)s.push(o[r]);for(o.length=0,r=s.length;r--;)s[r].i<p&&s[r].j<p&&s[r].k<p&&o.push(s[r].i,s[r].j,s[r].k);return o},contains:function(t,e){if(e[0]<t[0][0]&&e[0]<t[1][0]&&e[0]<t[2][0]||e[0]>t[0][0]&&e[0]>t[1][0]&&e[0]>t[2][0]||e[1]<t[0][1]&&e[1]<t[1][1]&&e[1]<t[2][1]||e[1]>t[0][1]&&e[1]>t[1][1]&&e[1]>t[2][1])return null;var r=t[1][0]-t[0][0],n=t[2][0]-t[0][0],i=t[1][1]-t[0][1],a=t[2][1]-t[0][1],o=r*a-n*i;if(0===o)return null;var s=(a*(e[0]-t[0][0])-n*(e[1]-t[0][1]))/o,u=(r*(e[1]-t[0][1])-i*(e[0]-t[0][0]))/o;return s<0||u<0||s+u>1?null:[s,u]}},Ee=Array,Ae=Math.random,be={};be.create=function(){var t=new Ee(2);return t[0]=0,t[1]=0,t},be.clone=function(t){var e=new Ee(2);return e[0]=t[0],e[1]=t[1],e},be.fromValues=function(t,e){var r=new Ee(2);return r[0]=t,r[1]=e,r},be.copy=function(t,e){return t[0]=e[0],t[1]=e[1],t},be.set=function(t,e,r){return t[0]=e,t[1]=r,t},be.add=function(t,e,r){return t[0]=e[0]+r[0],t[1]=e[1]+r[1],t},be.subtract=function(t,e,r){return t[0]=e[0]-r[0],t[1]=e[1]-r[1],t},be.sub=be.subtract,be.multiply=function(t,e,r){return t[0]=e[0]*r[0],t[1]=e[1]*r[1],t},be.mul=be.multiply,be.divide=function(t,e,r){return t[0]=e[0]/r[0],t[1]=e[1]/r[1],t},be.div=be.divide,be.min=function(t,e,r){return t[0]=Math.min(e[0],r[0]),t[1]=Math.min(e[1],r[1]),t},be.max=function(t,e,r){return t[0]=Math.max(e[0],r[0]),t[1]=Math.max(e[1],r[1]),t},be.scale=function(t,e,r){return t[0]=e[0]*r,t[1]=e[1]*r,t},be.scaleAndAdd=function(t,e,r,n){return t[0]=e[0]+r[0]*n,t[1]=e[1]+r[1]*n,t},be.distance=function(t,e){var r=e[0]-t[0],n=e[1]-t[1];return Math.sqrt(r*r+n*n)},be.dist=be.distance,be.squaredDistance=function(t,e){var r=e[0]-t[0],n=e[1]-t[1];return r*r+n*n},be.sqrDist=be.squaredDistance,be.length=function(t){var e=t[0],r=t[1];return Math.sqrt(e*e+r*r)},be.len=be.length,be.squaredLength=function(t){var e=t[0],r=t[1];return e*e+r*r},be.sqrLen=be.squaredLength,be.negate=function(t,e){return t[0]=-e[0],t[1]=-e[1],t},be.inverse=function(t,e){return t[0]=1/e[0],t[1]=1/e[1],t},be.normalize=function(t,e){var r=e[0],n=e[1],i=r*r+n*n;return i>0&&(i=1/Math.sqrt(i),t[0]=e[0]*i,t[1]=e[1]*i),t},be.dot=function(t,e){return t[0]*e[0]+t[1]*e[1]},be.cross=function(t,e,r){var n=e[0]*r[1]-e[1]*r[0];return t[0]=t[1]=0,t[2]=n,t},be.lerp=function(t,e,r,n){var i=e[0],a=e[1];return t[0]=i+n*(r[0]-i),t[1]=a+n*(r[1]-a),t},be.random=function(t,e){e=e||1;var r=2*GLMAT_RANDOM()*Math.PI;return t[0]=Math.cos(r)*e,t[1]=Math.sin(r)*e,t},be.transformMat2=function(t,e,r){var n=e[0],i=e[1];return t[0]=r[0]*n+r[2]*i,t[1]=r[1]*n+r[3]*i,t},be.transformMat2d=function(t,e,r){var n=e[0],i=e[1];return t[0]=r[0]*n+r[2]*i+r[4],t[1]=r[1]*n+r[3]*i+r[5],t},be.transformMat3=function(t,e,r){var n=e[0],i=e[1];return t[0]=r[0]*n+r[3]*i+r[6],t[1]=r[1]*n+r[4]*i+r[7],t},be.transformMat4=function(t,e,r){var n=e[0],i=e[1];return t[0]=r[0]*n+r[4]*i+r[12],t[1]=r[1]*n+r[5]*i+r[13],t},be.forEach=function(){var t=be.create();return function(e,r,n,i,a,o){var s,u;for(r||(r=2),n||(n=0),u=i?Math.min(i*r+n,e.length):e.length,s=n;s<u;s+=r)t[0]=e[s],t[1]=e[s+1],a(t,t,o),e[s]=t[0],e[s+1]=t[1];return e}}();var Se=function(t,e){t=t||0,e=e||0,this.array=be.fromValues(t,e),this._dirty=!0};if(Se.prototype={constructor:Se,add:function(t){return be.add(this.array,this.array,t.array),this._dirty=!0,this},set:function(t,e){return this.array[0]=t,this.array[1]=e,this._dirty=!0,this},setArray:function(t){return this.array[0]=t[0],this.array[1]=t[1],this._dirty=!0,this},clone:function(){return new Se(this.x,this.y)},copy:function(t){return be.copy(this.array,t.array),this._dirty=!0,this},cross:function(t,e){return be.cross(t.array,this.array,e.array),t._dirty=!0,this},dist:function(t){return be.dist(this.array,t.array)},distance:function(t){return be.distance(this.array,t.array)},div:function(t){return be.div(this.array,this.array,t.array),this._dirty=!0,this},divide:function(t){return be.divide(this.array,this.array,t.array),this._dirty=!0,this},dot:function(t){return be.dot(this.array,t.array)},len:function(){return be.len(this.array)},length:function(){return be.length(this.array)},lerp:function(t,e,r){return be.lerp(this.array,t.array,e.array,r),this._dirty=!0,this},min:function(t){return be.min(this.array,this.array,t.array),this._dirty=!0,this},max:function(t){return be.max(this.array,this.array,t.array),this._dirty=!0,this},mul:function(t){return be.mul(this.array,this.array,t.array),this._dirty=!0,this},multiply:function(t){return be.multiply(this.array,this.array,t.array),this._dirty=!0,this},negate:function(){return be.negate(this.array,this.array),this._dirty=!0,this},normalize:function(){return be.normalize(this.array,this.array),this._dirty=!0,this},random:function(t){return be.random(this.array,t),this._dirty=!0,this},scale:function(t){return be.scale(this.array,this.array,t),this._dirty=!0,this},scaleAndAdd:function(t,e){return be.scaleAndAdd(this.array,this.array,t.array,e),this._dirty=!0,this},sqrDist:function(t){return be.sqrDist(this.array,t.array)},squaredDistance:function(t){return be.squaredDistance(this.array,t.array)},sqrLen:function(){return be.sqrLen(this.array)},squaredLength:function(){return be.squaredLength(this.array)},sub:function(t){return be.sub(this.array,this.array,t.array),this._dirty=!0,this},subtract:function(t){return be.subtract(this.array,this.array,t.array),this._dirty=!0,this},transformMat2:function(t){return be.transformMat2(this.array,this.array,t.array),this._dirty=!0,this},transformMat2d:function(t){return be.transformMat2d(this.array,this.array,t.array),this._dirty=!0,this},transformMat3:function(t){return be.transformMat3(this.array,this.array,t.array),this._dirty=!0,this},transformMat4:function(t){return be.transformMat4(this.array,this.array,t.array),this._dirty=!0,this},toString:function(){return"["+Array.prototype.join.call(this.array,",")+"]"},toArray:function(){return Array.prototype.slice.call(this.array)}},Object.defineProperty){var Me=Se.prototype;Object.defineProperty(Me,"x",{get:function(){return this.array[0]},set:function(t){this.array[0]=t,this._dirty=!0}}),Object.defineProperty(Me,"y",{get:function(){return this.array[1]},set:function(t){this.array[1]=t,this._dirty=!0}})}Se.add=function(t,e,r){return be.add(t.array,e.array,r.array),t._dirty=!0,t},Se.set=function(t,e,r){return be.set(t.array,e,r),t._dirty=!0,t},Se.copy=function(t,e){return be.copy(t.array,e.array),t._dirty=!0,t},Se.cross=function(t,e,r){return be.cross(t.array,e.array,r.array),t._dirty=!0,t},Se.dist=function(t,e){return be.distance(t.array,e.array)},Se.distance=Se.dist,Se.div=function(t,e,r){return be.divide(t.array,e.array,r.array),t._dirty=!0,t},Se.divide=Se.div,Se.dot=function(t,e){return be.dot(t.array,e.array)},Se.len=function(t){return be.length(t.array)},Se.lerp=function(t,e,r,n){return be.lerp(t.array,e.array,r.array,n),t._dirty=!0,t},Se.min=function(t,e,r){return be.min(t.array,e.array,r.array),t._dirty=!0,t},Se.max=function(t,e,r){return be.max(t.array,e.array,r.array),t._dirty=!0,t},Se.mul=function(t,e,r){return be.multiply(t.array,e.array,r.array),t._dirty=!0,t},Se.multiply=Se.mul,Se.negate=function(t,e){return be.negate(t.array,e.array),t._dirty=!0,t},Se.normalize=function(t,e){return be.normalize(t.array,e.array),t._dirty=!0,t},Se.random=function(t,e){return be.random(t.array,e),t._dirty=!0,t},Se.scale=function(t,e,r){return be.scale(t.array,e.array,r),t._dirty=!0,t},Se.scaleAndAdd=function(t,e,r,n){return be.scaleAndAdd(t.array,e.array,r.array,n),t._dirty=!0,t},Se.sqrDist=function(t,e){return be.sqrDist(t.array,e.array)},Se.squaredDistance=Se.sqrDist,Se.sqrLen=function(t){return be.sqrLen(t.array)},Se.squaredLength=Se.sqrLen,Se.sub=function(t,e,r){return be.subtract(t.array,e.array,r.array),t._dirty=!0,t},Se.subtract=Se.sub,Se.transformMat2=function(t,e,r){return be.transformMat2(t.array,e.array,r.array),t._dirty=!0,t},Se.transformMat2d=function(t,e,r){return be.transformMat2d(t.array,e.array,r.array),t._dirty=!0,t},Se.transformMat3=function(t,e,r){return be.transformMat3(t.array,e.array,r.array),t._dirty=!0,t},Se.transformMat4=function(t,e,r){return be.transformMat4(t.array,e.array,r.array),t._dirty=!0,t};var we=function(t){t=t||{},_e.call(this,t),this.output=t.output||null,this.inputs=t.inputs||[],this.position=new Se,this._cacheTriangle=null,this._triangles=[],this._updateTriangles()};we.prototype=new _e,we.prototype.constructor=we,we.prototype.addInput=function(t,e,r){var n={position:t,clip:e,offset:r||0};return this.inputs.push(n),this.life=Math.max(e.life,this.life),this._updateTriangles(),n},we.prototype._updateTriangles=function(){var t=this.inputs.map(function(t){return t.position});this._triangles=xe.triangulate(t,"array")},we.prototype.step=function(t,e,r){var n=_e.prototype.step.call(this,t);return"finish"!==n&&this.setTime(this.getElapsedTime()),r||"paused"===n||this.fire("frame"),n},we.prototype.setTime=function(t){var e=this._findTriangle(this.position);if(e){var r=e[1],n=e[2],i=e[0],a=this.inputs[i.indices[0]],o=this.inputs[i.indices[1]],s=this.inputs[i.indices[2]],u=a.clip,l=o.clip,h=s.clip;u.setTime((t+a.offset)%u.life),l.setTime((t+o.offset)%l.life),h.setTime((t+s.offset)%h.life);var c=u.output instanceof _e?u.output:u,f=l.output instanceof _e?l.output:l,d=h.output instanceof _e?h.output:h;this.output.blend2D(c,f,d,r,n)}},we.prototype.clone=function(t){var e=_e.prototype.clone.call(this);e.output=this.output.clone();for(var r=0;r<this.inputs.length;r++){var n=t?this.inputs[r].clip.clone(!0):this.inputs[r].clip;e.addInput(this.inputs[r].position,n,this.inputs[r].offset)}return e},we.prototype._findTriangle=function(t){if(this._cacheTriangle){var e=xe.contains(this._cacheTriangle.vertices,t.array);if(e)return[this._cacheTriangle,e[0],e[1]]}for(var r=0;r<this._triangles.length;r++){var n=this._triangles[r],e=xe.contains(n.vertices,this.position.array);if(e)return this._cacheTriangle=n,[n,e[0],e[1]]}};var Ce={};Ce.create=function(){var t=new Ee(3);return t[0]=0,t[1]=0,t[2]=0,t},Ce.clone=function(t){var e=new Ee(3);return e[0]=t[0],e[1]=t[1],e[2]=t[2],e},Ce.fromValues=function(t,e,r){var n=new Ee(3);return n[0]=t,n[1]=e,n[2]=r,n},Ce.copy=function(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t},Ce.set=function(t,e,r,n){return t[0]=e,t[1]=r,t[2]=n,t},Ce.add=function(t,e,r){return t[0]=e[0]+r[0],t[1]=e[1]+r[1],t[2]=e[2]+r[2],t},Ce.subtract=function(t,e,r){return t[0]=e[0]-r[0],t[1]=e[1]-r[1],t[2]=e[2]-r[2],t},Ce.sub=Ce.subtract,Ce.multiply=function(t,e,r){return t[0]=e[0]*r[0],t[1]=e[1]*r[1],t[2]=e[2]*r[2],t},Ce.mul=Ce.multiply,Ce.divide=function(t,e,r){return t[0]=e[0]/r[0],t[1]=e[1]/r[1],t[2]=e[2]/r[2],t},Ce.div=Ce.divide,Ce.min=function(t,e,r){return t[0]=Math.min(e[0],r[0]),t[1]=Math.min(e[1],r[1]),t[2]=Math.min(e[2],r[2]),t},Ce.max=function(t,e,r){return t[0]=Math.max(e[0],r[0]),t[1]=Math.max(e[1],r[1]),t[2]=Math.max(e[2],r[2]),t},Ce.scale=function(t,e,r){return t[0]=e[0]*r,t[1]=e[1]*r,t[2]=e[2]*r,t},Ce.scaleAndAdd=function(t,e,r,n){return t[0]=e[0]+r[0]*n,t[1]=e[1]+r[1]*n,t[2]=e[2]+r[2]*n,t},Ce.distance=function(t,e){var r=e[0]-t[0],n=e[1]-t[1],i=e[2]-t[2];return Math.sqrt(r*r+n*n+i*i)},Ce.dist=Ce.distance,Ce.squaredDistance=function(t,e){var r=e[0]-t[0],n=e[1]-t[1],i=e[2]-t[2];return r*r+n*n+i*i},Ce.sqrDist=Ce.squaredDistance,Ce.length=function(t){var e=t[0],r=t[1],n=t[2];return Math.sqrt(e*e+r*r+n*n)},Ce.len=Ce.length,Ce.squaredLength=function(t){var e=t[0],r=t[1],n=t[2];return e*e+r*r+n*n},Ce.sqrLen=Ce.squaredLength,Ce.negate=function(t,e){return t[0]=-e[0],t[1]=-e[1],t[2]=-e[2],t},Ce.inverse=function(t,e){return t[0]=1/e[0],t[1]=1/e[1],t[2]=1/e[2],t},Ce.normalize=function(t,e){var r=e[0],n=e[1],i=e[2],a=r*r+n*n+i*i;return a>0&&(a=1/Math.sqrt(a),t[0]=e[0]*a,t[1]=e[1]*a,t[2]=e[2]*a),t},Ce.dot=function(t,e){return t[0]*e[0]+t[1]*e[1]+t[2]*e[2]},Ce.cross=function(t,e,r){var n=e[0],i=e[1],a=e[2],o=r[0],s=r[1],u=r[2];return t[0]=i*u-a*s,t[1]=a*o-n*u,t[2]=n*s-i*o,t},Ce.lerp=function(t,e,r,n){var i=e[0],a=e[1],o=e[2];return t[0]=i+n*(r[0]-i),t[1]=a+n*(r[1]-a),t[2]=o+n*(r[2]-o),t},Ce.random=function(t,e){e=e||1;var r=2*Ae()*Math.PI,n=2*Ae()-1,i=Math.sqrt(1-n*n)*e;return t[0]=Math.cos(r)*i,t[1]=Math.sin(r)*i,t[2]=n*e,t},Ce.transformMat4=function(t,e,r){var n=e[0],i=e[1],a=e[2],o=r[3]*n+r[7]*i+r[11]*a+r[15];return o=o||1,t[0]=(r[0]*n+r[4]*i+r[8]*a+r[12])/o,t[1]=(r[1]*n+r[5]*i+r[9]*a+r[13])/o,t[2]=(r[2]*n+r[6]*i+r[10]*a+r[14])/o,t},Ce.transformMat3=function(t,e,r){var n=e[0],i=e[1],a=e[2];return t[0]=n*r[0]+i*r[3]+a*r[6],t[1]=n*r[1]+i*r[4]+a*r[7],t[2]=n*r[2]+i*r[5]+a*r[8],t},Ce.transformQuat=function(t,e,r){var n=e[0],i=e[1],a=e[2],o=r[0],s=r[1],u=r[2],l=r[3],h=l*n+s*a-u*i,c=l*i+u*n-o*a,f=l*a+o*i-s*n,d=-o*n-s*i-u*a;return t[0]=h*l+d*-o+c*-u-f*-s,t[1]=c*l+d*-s+f*-o-h*-u,t[2]=f*l+d*-u+h*-s-c*-o,t},Ce.rotateX=function(t,e,r,n){var i=[],a=[];return i[0]=e[0]-r[0],i[1]=e[1]-r[1],i[2]=e[2]-r[2],a[0]=i[0],a[1]=i[1]*Math.cos(n)-i[2]*Math.sin(n),a[2]=i[1]*Math.sin(n)+i[2]*Math.cos(n),t[0]=a[0]+r[0],t[1]=a[1]+r[1],t[2]=a[2]+r[2],t},Ce.rotateY=function(t,e,r,n){var i=[],a=[];return i[0]=e[0]-r[0],i[1]=e[1]-r[1],i[2]=e[2]-r[2],a[0]=i[2]*Math.sin(n)+i[0]*Math.cos(n),a[1]=i[1],a[2]=i[2]*Math.cos(n)-i[0]*Math.sin(n),t[0]=a[0]+r[0],t[1]=a[1]+r[1],t[2]=a[2]+r[2],t},Ce.rotateZ=function(t,e,r,n){var i=[],a=[];return i[0]=e[0]-r[0],i[1]=e[1]-r[1],i[2]=e[2]-r[2],a[0]=i[0]*Math.cos(n)-i[1]*Math.sin(n),a[1]=i[0]*Math.sin(n)+i[1]*Math.cos(n),a[2]=i[2],t[0]=a[0]+r[0],t[1]=a[1]+r[1],t[2]=a[2]+r[2],t},Ce.forEach=function(){var t=Ce.create();return function(e,r,n,i,a,o){var s,u;for(r||(r=3),n||(n=0),u=i?Math.min(i*r+n,e.length):e.length,s=n;s<u;s+=r)t[0]=e[s],t[1]=e[s+1],t[2]=e[s+2],a(t,t,o),e[s]=t[0],e[s+1]=t[1],e[s+2]=t[2];return e}}(),Ce.angle=function(t,e){var r=Ce.fromValues(t[0],t[1],t[2]),n=Ce.fromValues(e[0],e[1],e[2]);Ce.normalize(r,r),Ce.normalize(n,n);var i=Ce.dot(r,n);return i>1?0:Math.acos(i)};var Re={};Re.create=function(){var t=new Ee(4);return t[0]=0,t[1]=0,t[2]=0,t[3]=0,t},Re.clone=function(t){var e=new Ee(4);return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e},Re.fromValues=function(t,e,r,n){var i=new Ee(4);return i[0]=t,i[1]=e,i[2]=r,i[3]=n,i},Re.copy=function(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t},Re.set=function(t,e,r,n,i){return t[0]=e,t[1]=r,t[2]=n,t[3]=i,t},Re.add=function(t,e,r){return t[0]=e[0]+r[0],t[1]=e[1]+r[1],t[2]=e[2]+r[2],t[3]=e[3]+r[3],t},Re.subtract=function(t,e,r){return t[0]=e[0]-r[0],t[1]=e[1]-r[1],t[2]=e[2]-r[2],t[3]=e[3]-r[3],t},Re.sub=Re.subtract,Re.multiply=function(t,e,r){return t[0]=e[0]*r[0],t[1]=e[1]*r[1],t[2]=e[2]*r[2],t[3]=e[3]*r[3],t},Re.mul=Re.multiply,Re.divide=function(t,e,r){return t[0]=e[0]/r[0],t[1]=e[1]/r[1],t[2]=e[2]/r[2],t[3]=e[3]/r[3],t},Re.div=Re.divide,Re.min=function(t,e,r){return t[0]=Math.min(e[0],r[0]),t[1]=Math.min(e[1],r[1]),t[2]=Math.min(e[2],r[2]),t[3]=Math.min(e[3],r[3]),t},Re.max=function(t,e,r){return t[0]=Math.max(e[0],r[0]),t[1]=Math.max(e[1],r[1]),t[2]=Math.max(e[2],r[2]),t[3]=Math.max(e[3],r[3]),t},Re.scale=function(t,e,r){return t[0]=e[0]*r,t[1]=e[1]*r,t[2]=e[2]*r,t[3]=e[3]*r,t},Re.scaleAndAdd=function(t,e,r,n){return t[0]=e[0]+r[0]*n,t[1]=e[1]+r[1]*n,t[2]=e[2]+r[2]*n,t[3]=e[3]+r[3]*n,t},Re.distance=function(t,e){var r=e[0]-t[0],n=e[1]-t[1],i=e[2]-t[2],a=e[3]-t[3];return Math.sqrt(r*r+n*n+i*i+a*a)},Re.dist=Re.distance,Re.squaredDistance=function(t,e){var r=e[0]-t[0],n=e[1]-t[1],i=e[2]-t[2],a=e[3]-t[3];return r*r+n*n+i*i+a*a},Re.sqrDist=Re.squaredDistance,Re.length=function(t){var e=t[0],r=t[1],n=t[2],i=t[3];return Math.sqrt(e*e+r*r+n*n+i*i)},Re.len=Re.length,Re.squaredLength=function(t){var e=t[0],r=t[1],n=t[2],i=t[3];return e*e+r*r+n*n+i*i},Re.sqrLen=Re.squaredLength,Re.negate=function(t,e){return t[0]=-e[0],t[1]=-e[1],t[2]=-e[2],t[3]=-e[3],t},Re.inverse=function(t,e){return t[0]=1/e[0],t[1]=1/e[1],t[2]=1/e[2],t[3]=1/e[3],t},Re.normalize=function(t,e){var r=e[0],n=e[1],i=e[2],a=e[3],o=r*r+n*n+i*i+a*a;return o>0&&(o=1/Math.sqrt(o),t[0]=e[0]*o,t[1]=e[1]*o,t[2]=e[2]*o,t[3]=e[3]*o),t},Re.dot=function(t,e){return t[0]*e[0]+t[1]*e[1]+t[2]*e[2]+t[3]*e[3]},Re.lerp=function(t,e,r,n){var i=e[0],a=e[1],o=e[2],s=e[3];return t[0]=i+n*(r[0]-i),t[1]=a+n*(r[1]-a),t[2]=o+n*(r[2]-o),t[3]=s+n*(r[3]-s),t},Re.random=function(t,e){return e=e||1,t[0]=Ae(),t[1]=Ae(),t[2]=Ae(),t[3]=Ae(),Re.normalize(t,t),Re.scale(t,t,e),t},Re.transformMat4=function(t,e,r){var n=e[0],i=e[1],a=e[2],o=e[3];return t[0]=r[0]*n+r[4]*i+r[8]*a+r[12]*o,t[1]=r[1]*n+r[5]*i+r[9]*a+r[13]*o,t[2]=r[2]*n+r[6]*i+r[10]*a+r[14]*o,t[3]=r[3]*n+r[7]*i+r[11]*a+r[15]*o,t},Re.transformQuat=function(t,e,r){var n=e[0],i=e[1],a=e[2],o=r[0],s=r[1],u=r[2],l=r[3],h=l*n+s*a-u*i,c=l*i+u*n-o*a,f=l*a+o*i-s*n,d=-o*n-s*i-u*a;return t[0]=h*l+d*-o+c*-u-f*-s,t[1]=c*l+d*-s+f*-o-h*-u,t[2]=f*l+d*-u+h*-s-c*-o,t},Re.forEach=function(){var t=Re.create();return function(e,r,n,i,a,o){var s,u;for(r||(r=4),n||(n=0),u=i?Math.min(i*r+n,e.length):e.length,s=n;s<u;s+=r)t[0]=e[s],t[1]=e[s+1],t[2]=e[s+2],t[3]=e[s+3],a(t,t,o),e[s]=t[0],e[s+1]=t[1],e[s+2]=t[2],e[s+3]=t[3];return e}}();var Le={};Le.create=function(){var t=new Ee(9);return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t},Le.fromMat4=function(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[4],t[4]=e[5],t[5]=e[6],t[6]=e[8],t[7]=e[9],t[8]=e[10],t},Le.clone=function(t){var e=new Ee(9);return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4],e[5]=t[5],e[6]=t[6],e[7]=t[7],e[8]=t[8],e},Le.copy=function(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[8]=e[8],t},Le.identity=function(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t},Le.transpose=function(t,e){if(t===e){var r=e[1],n=e[2],i=e[5];t[1]=e[3],t[2]=e[6],t[3]=r,t[5]=e[7],t[6]=n,t[7]=i}else t[0]=e[0],t[1]=e[3],t[2]=e[6],t[3]=e[1],t[4]=e[4],t[5]=e[7],t[6]=e[2],t[7]=e[5],t[8]=e[8];return t},Le.invert=function(t,e){var r=e[0],n=e[1],i=e[2],a=e[3],o=e[4],s=e[5],u=e[6],l=e[7],h=e[8],c=h*o-s*l,f=-h*a+s*u,d=l*a-o*u,p=r*c+n*f+i*d;return p?(p=1/p,t[0]=c*p,t[1]=(-h*n+i*l)*p,t[2]=(s*n-i*o)*p,t[3]=f*p,t[4]=(h*r-i*u)*p,t[5]=(-s*r+i*a)*p,t[6]=d*p,t[7]=(-l*r+n*u)*p,t[8]=(o*r-n*a)*p,t):null},Le.adjoint=function(t,e){var r=e[0],n=e[1],i=e[2],a=e[3],o=e[4],s=e[5],u=e[6],l=e[7],h=e[8];return t[0]=o*h-s*l,t[1]=i*l-n*h,t[2]=n*s-i*o,t[3]=s*u-a*h,t[4]=r*h-i*u,t[5]=i*a-r*s,t[6]=a*l-o*u,t[7]=n*u-r*l,t[8]=r*o-n*a,t},Le.determinant=function(t){var e=t[0],r=t[1],n=t[2],i=t[3],a=t[4],o=t[5],s=t[6],u=t[7],l=t[8];return e*(l*a-o*u)+r*(-l*i+o*s)+n*(u*i-a*s)},Le.multiply=function(t,e,r){var n=e[0],i=e[1],a=e[2],o=e[3],s=e[4],u=e[5],l=e[6],h=e[7],c=e[8],f=r[0],d=r[1],p=r[2],m=r[3],_=r[4],g=r[5],v=r[6],y=r[7],T=r[8];return t[0]=f*n+d*o+p*l,t[1]=f*i+d*s+p*h,t[2]=f*a+d*u+p*c,t[3]=m*n+_*o+g*l,t[4]=m*i+_*s+g*h,t[5]=m*a+_*u+g*c,t[6]=v*n+y*o+T*l,t[7]=v*i+y*s+T*h,t[8]=v*a+y*u+T*c,t},Le.mul=Le.multiply,Le.translate=function(t,e,r){var n=e[0],i=e[1],a=e[2],o=e[3],s=e[4],u=e[5],l=e[6],h=e[7],c=e[8],f=r[0],d=r[1];return t[0]=n,t[1]=i,t[2]=a,t[3]=o,t[4]=s,t[5]=u,t[6]=f*n+d*o+l,t[7]=f*i+d*s+h,t[8]=f*a+d*u+c,t},Le.rotate=function(t,e,r){var n=e[0],i=e[1],a=e[2],o=e[3],s=e[4],u=e[5],l=e[6],h=e[7],c=e[8],f=Math.sin(r),d=Math.cos(r);return t[0]=d*n+f*o,t[1]=d*i+f*s,t[2]=d*a+f*u,t[3]=d*o-f*n,t[4]=d*s-f*i,t[5]=d*u-f*a,t[6]=l,t[7]=h,t[8]=c,t},Le.scale=function(t,e,r){var n=r[0],i=r[1];return t[0]=n*e[0],t[1]=n*e[1],t[2]=n*e[2],t[3]=i*e[3],t[4]=i*e[4],t[5]=i*e[5],t[6]=e[6],t[7]=e[7],t[8]=e[8],t},Le.fromMat2d=function(t,e){return t[0]=e[0],t[1]=e[1],t[2]=0,t[3]=e[2],t[4]=e[3],t[5]=0,t[6]=e[4],t[7]=e[5],t[8]=1,t},Le.fromQuat=function(t,e){var r=e[0],n=e[1],i=e[2],a=e[3],o=r+r,s=n+n,u=i+i,l=r*o,h=n*o,c=n*s,f=i*o,d=i*s,p=i*u,m=a*o,_=a*s,g=a*u;return t[0]=1-c-p,t[3]=h-g,t[6]=f+_,t[1]=h+g,t[4]=1-l-p,t[7]=d-m,t[2]=f-_,t[5]=d+m,t[8]=1-l-c,t},Le.normalFromMat4=function(t,e){var r=e[0],n=e[1],i=e[2],a=e[3],o=e[4],s=e[5],u=e[6],l=e[7],h=e[8],c=e[9],f=e[10],d=e[11],p=e[12],m=e[13],_=e[14],g=e[15],v=r*s-n*o,y=r*u-i*o,T=r*l-a*o,x=n*u-i*s,E=n*l-a*s,A=i*l-a*u,b=h*m-c*p,S=h*_-f*p,M=h*g-d*p,w=c*_-f*m,C=c*g-d*m,R=f*g-d*_,L=v*R-y*C+T*w+x*M-E*S+A*b;return L?(L=1/L,t[0]=(s*R-u*C+l*w)*L,t[1]=(u*M-o*R-l*S)*L,t[2]=(o*C-s*M+l*b)*L,t[3]=(i*C-n*R-a*w)*L,t[4]=(r*R-i*M+a*S)*L,t[5]=(n*M-r*C-a*b)*L,t[6]=(m*A-_*E+g*x)*L,t[7]=(_*T-p*A-g*y)*L,t[8]=(p*E-m*T+g*v)*L,t):null},Le.frob=function(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2)+Math.pow(t[2],2)+Math.pow(t[3],2)+Math.pow(t[4],2)+Math.pow(t[5],2)+Math.pow(t[6],2)+Math.pow(t[7],2)+Math.pow(t[8],2))};var Ne={};Ne.create=function(){var t=new Ee(4);return t[0]=0,t[1]=0,t[2]=0,t[3]=1,t},Ne.rotationTo=function(){var t=Ce.create(),e=Ce.fromValues(1,0,0),r=Ce.fromValues(0,1,0);return function(n,i,a){var o=Ce.dot(i,a);return o<-.999999?(Ce.cross(t,e,i),Ce.length(t)<1e-6&&Ce.cross(t,r,i),Ce.normalize(t,t),Ne.setAxisAngle(n,t,Math.PI),n):o>.999999?(n[0]=0,n[1]=0,n[2]=0,n[3]=1,n):(Ce.cross(t,i,a),n[0]=t[0],n[1]=t[1],n[2]=t[2],n[3]=1+o,Ne.normalize(n,n))}}(),Ne.setAxes=function(){var t=Le.create();return function(e,r,n,i){return t[0]=n[0],t[3]=n[1],t[6]=n[2],t[1]=i[0],t[4]=i[1],t[7]=i[2],t[2]=-r[0],t[5]=-r[1],t[8]=-r[2],Ne.normalize(e,Ne.fromMat3(e,t))}}(),Ne.clone=Re.clone,Ne.fromValues=Re.fromValues,Ne.copy=Re.copy,Ne.set=Re.set,Ne.identity=function(t){return t[0]=0,t[1]=0,t[2]=0,t[3]=1,t},Ne.setAxisAngle=function(t,e,r){r*=.5;var n=Math.sin(r);return t[0]=n*e[0],t[1]=n*e[1],t[2]=n*e[2],t[3]=Math.cos(r),t},Ne.add=Re.add,Ne.multiply=function(t,e,r){var n=e[0],i=e[1],a=e[2],o=e[3],s=r[0],u=r[1],l=r[2],h=r[3];return t[0]=n*h+o*s+i*l-a*u,t[1]=i*h+o*u+a*s-n*l,t[2]=a*h+o*l+n*u-i*s,t[3]=o*h-n*s-i*u-a*l,t},Ne.mul=Ne.multiply,Ne.scale=Re.scale,Ne.rotateX=function(t,e,r){r*=.5;var n=e[0],i=e[1],a=e[2],o=e[3],s=Math.sin(r),u=Math.cos(r);return t[0]=n*u+o*s,t[1]=i*u+a*s,t[2]=a*u-i*s,t[3]=o*u-n*s,t},Ne.rotateY=function(t,e,r){r*=.5;var n=e[0],i=e[1],a=e[2],o=e[3],s=Math.sin(r),u=Math.cos(r);return t[0]=n*u-a*s,t[1]=i*u+o*s,t[2]=a*u+n*s,t[3]=o*u-i*s,t},Ne.rotateZ=function(t,e,r){r*=.5;var n=e[0],i=e[1],a=e[2],o=e[3],s=Math.sin(r),u=Math.cos(r);return t[0]=n*u+i*s,t[1]=i*u-n*s,t[2]=a*u+o*s,t[3]=o*u-a*s,t},Ne.calculateW=function(t,e){var r=e[0],n=e[1],i=e[2];return t[0]=r,t[1]=n,t[2]=i,t[3]=Math.sqrt(Math.abs(1-r*r-n*n-i*i)),t},Ne.dot=Re.dot,Ne.lerp=Re.lerp,Ne.slerp=function(t,e,r,n){var i,a,o,s,u,l=e[0],h=e[1],c=e[2],f=e[3],d=r[0],p=r[1],m=r[2],_=r[3];return a=l*d+h*p+c*m+f*_,a<0&&(a=-a,d=-d,p=-p,m=-m,_=-_),1-a>1e-6?(i=Math.acos(a),o=Math.sin(i),s=Math.sin((1-n)*i)/o,u=Math.sin(n*i)/o):(s=1-n,u=n),t[0]=s*l+u*d,t[1]=s*h+u*p,t[2]=s*c+u*m,t[3]=s*f+u*_,t},Ne.invert=function(t,e){var r=e[0],n=e[1],i=e[2],a=e[3],o=r*r+n*n+i*i+a*a,s=o?1/o:0;return t[0]=-r*s,t[1]=-n*s,t[2]=-i*s,t[3]=a*s,t},Ne.conjugate=function(t,e){return t[0]=-e[0],t[1]=-e[1],t[2]=-e[2],t[3]=e[3],t},Ne.length=Re.length,Ne.len=Ne.length,Ne.squaredLength=Re.squaredLength,Ne.sqrLen=Ne.squaredLength,Ne.normalize=Re.normalize,Ne.fromMat3=function(t,e){var r,n=e[0]+e[4]+e[8];if(n>0)r=Math.sqrt(n+1),t[3]=.5*r,r=.5/r,t[0]=(e[5]-e[7])*r,t[1]=(e[6]-e[2])*r,t[2]=(e[1]-e[3])*r;else{var i=0;e[4]>e[0]&&(i=1),e[8]>e[3*i+i]&&(i=2);var a=(i+1)%3,o=(i+2)%3;r=Math.sqrt(e[3*i+i]-e[3*a+a]-e[3*o+o]+1),t[i]=.5*r,r=.5/r,t[3]=(e[3*a+o]-e[3*o+a])*r,t[a]=(e[3*a+i]+e[3*i+a])*r,t[o]=(e[3*o+i]+e[3*i+o])*r}return t};var Pe=function(t){t=t||{},this.name=t.name||"",this.target=t.target||null,this.position=Ce.create(),this.rotation=Ne.create(),this.scale=Ce.fromValues(1,1,1),this.channels={time:null,position:null,rotation:null,scale:null},this._cacheKey=0,this._cacheTime=0};Pe.prototype.setTime=function(t){if(this.channels.time){var e=this.channels,r=e.time.length,n=-1;if(1===r)return e.rotation&&Ne.copy(this.rotation,e.rotation),e.position&&Ce.copy(this.position,e.position),void(e.scale&&Ce.copy(this.scale,e.scale));if(t<=e.time[0])t=e.time[0],n=0;else if(t>=e.time[r-1])t=e.time[r-1],n=r-2;else if(t<this._cacheTime){for(var i=Math.min(r-1,this._cacheKey+1),a=i;a>=0;a--)if(e.time[a-1]<=t&&e.time[a]>t){n=a-1;break}}else for(var a=this._cacheKey;a<r-1;a++)if(e.time[a]<=t&&e.time[a+1]>t){n=a;break}if(n>-1){this._cacheKey=n,this._cacheTime=t;var o=n,s=n+1,u=e.time[o],l=e.time[s],h=l-u,c=0===h?0:(t-u)/h;e.rotation&&y(this.rotation,e.rotation,e.rotation,c,4*o,4*s),e.position&&v(this.position,e.position,e.position,c,3*o,3*s),e.scale&&v(this.scale,e.scale,e.scale,c,3*o,3*s)}n===r-2&&(this._cacheKey=0,this._cacheTime=0),this.updateTarget()}},Pe.prototype.updateTarget=function(){var t=this.channels;this.target&&(t.position&&this.target.position.setArray(this.position),t.rotation&&this.target.rotation.setArray(this.rotation),t.scale&&this.target.scale.setArray(this.scale))},Pe.prototype.getMaxTime=function(){return this.channels.time[this.channels.time.length-1]},Pe.prototype.getSubTrack=function(t,e){var r=new Pe({name:this.name}),n=this.channels.time[0];t=Math.min(Math.max(t,n),this.life),e=Math.min(Math.max(e,n),this.life);var i=this._findRange(t),a=this._findRange(e),o=a[0]-i[0]+1;0===i[1]&&0===a[1]&&(o-=1),this.channels.rotation&&(r.channels.rotation=new Float32Array(4*o)),this.channels.position&&(r.channels.position=new Float32Array(3*o)),this.channels.scale&&(r.channels.scale=new Float32Array(3*o)),this.channels.time&&(r.channels.time=new Float32Array(o)),this.setTime(t);for(var s=0;s<3;s++)r.channels.rotation[s]=this.rotation[s],r.channels.position[s]=this.position[s],r.channels.scale[s]=this.scale[s];r.channels.time[0]=0,r.channels.rotation[3]=this.rotation[3];for(var s=1;s<o-1;s++){for(var u,l=0;l<3;l++)u=i[0]+s,r.channels.rotation[4*s+l]=this.channels.rotation[4*u+l],r.channels.position[3*s+l]=this.channels.position[3*u+l],r.channels.scale[3*s+l]=this.channels.scale[3*u+l];r.channels.time[s]=this.channels.time[u]-t,r.channels.rotation[4*s+3]=this.channels.rotation[4*u+3]}this.setTime(e);for(var s=0;s<3;s++)r.channels.rotation[4*(o-1)+s]=this.rotation[s],r.channels.position[3*(o-1)+s]=this.position[s],r.channels.scale[3*(o-1)+s]=this.scale[s];return r.channels.time[o-1]=e-t,r.channels.rotation[4*(o-1)+3]=this.rotation[3],r.life=e-t,r},Pe.prototype._findRange=function(t){for(var e=this.channels,r=e.time.length,n=-1,i=0;i<r-1;i++)e.time[i]<=t&&e.time[i+1]>t&&(n=i);var a=0;if(n>=0)var o=e.time[n],s=e.time[n+1],a=(t-o)/(s-o);return[n,a]},Pe.prototype.blend1D=function(t,e,r){Ce.lerp(this.position,t.position,e.position,r),Ce.lerp(this.scale,t.scale,e.scale,r),Ne.slerp(this.rotation,t.rotation,e.rotation,r)},Pe.prototype.blend2D=function(){var t=Ne.create(),e=Ne.create();return function(r,n,i,a,o){var s=1-a-o;this.position[0]=r.position[0]*s+n.position[0]*a+i.position[0]*o,this.position[1]=r.position[1]*s+n.position[1]*a+i.position[1]*o,this.position[2]=r.position[2]*s+n.position[2]*a+i.position[2]*o,this.scale[0]=r.scale[0]*s+n.scale[0]*a+i.scale[0]*o,this.scale[1]=r.scale[1]*s+n.scale[1]*a+i.scale[1]*o,this.scale[2]=r.scale[2]*s+n.scale[2]*a+i.scale[2]*o;var u=a+o;0===u?Ne.copy(this.rotation,r.rotation):(Ne.slerp(t,r.rotation,n.rotation,u),Ne.slerp(e,r.rotation,i.rotation,u),Ne.slerp(this.rotation,t,e,o/u))}}(),Pe.prototype.additiveBlend=function(t,e){Ce.add(this.position,t.position,e.position),Ce.add(this.scale,t.scale,e.scale),Ne.multiply(this.rotation,e.rotation,t.rotation)},Pe.prototype.subtractiveBlend=function(t,e){Ce.sub(this.position,t.position,e.position),Ce.sub(this.scale,t.scale,e.scale),Ne.invert(this.rotation,e.rotation),Ne.multiply(this.rotation,this.rotation,t.rotation)},Pe.prototype.clone=function(){var t=Pe.prototype.clone.call(this);return t.channels={time:this.channels.time||null,position:this.channels.position||null,rotation:this.channels.rotation||null,scale:this.channels.scale||null},Ce.copy(t.position,this.position),Ne.copy(t.rotation,this.rotation),Ce.copy(t.scale,this.scale),t.target=this.target,t.updateTarget(),t};var Ie={extend:T,derive:T},Oe={trigger:function(t){if(this.hasOwnProperty("__handlers__")&&this.__handlers__.hasOwnProperty(t)){var e=this.__handlers__[t],r=e.length,n=-1,i=arguments;switch(i.length){case 1:for(;++n<r;)e[n].action.call(e[n].context);return;case 2:for(;++n<r;)e[n].action.call(e[n].context,i[1]);return;case 3:for(;++n<r;)e[n].action.call(e[n].context,i[1],i[2]);return;case 4:for(;++n<r;)e[n].action.call(e[n].context,i[1],i[2],i[3]);return;case 5:for(;++n<r;)e[n].action.call(e[n].context,i[1],i[2],i[3],i[4]);return;default:for(;++n<r;)e[n].action.apply(e[n].context,Array.prototype.slice.call(i,1));return}}},on:function(t,e,r){if(t&&e){var n=this.__handlers__||(this.__handlers__={});if(n[t]){if(this.has(t,e))return}else n[t]=[];var i=new A(e,r||this);return n[t].push(i),this}},once:function(t,e,r){function n(){i.off(t,n),e.apply(this,arguments)}if(t&&e){var i=this;return this.on(t,n,r)}},before:function(t,e,r){if(t&&e)return t="before"+t,this.on(t,e,r)},after:function(t,e,r){if(t&&e)return t="after"+t,this.on(t,e,r)},success:function(t,e){return this.once("success",t,e)},error:function(t,e){return this.once("error",t,e)},off:function(t,e){var r=this.__handlers__||(this.__handlers__={});if(!e)return void(r[t]=[]);if(r[t]){for(var n=r[t],i=[],a=0;a<n.length;a++)e&&n[a].action!==e&&i.push(n[a]);r[t]=i}return this},has:function(t,e){var r=this.__handlers__;if(!r||!r[t])return!1;for(var n=r[t],i=0;i<n.length;i++)if(n[i].action===e)return!0}},De=0,Be=Array.prototype,Ue=Be.forEach,Fe={genGUID:function(){return++De},relative2absolute:function(t,e){if(!e||t.match(/^\//))return t;for(var r=t.split("/"),n=e.split("/"),i=r[0];"."===i||".."===i;)".."===i&&n.pop(),r.shift(),i=r[0];return n.join("/")+"/"+r.join("/")},extend:function(t,e){if(e)for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r]);return t},defaults:function(t,e){if(e)for(var r in e)void 0===t[r]&&(t[r]=e[r]);return t},extendWithPropList:function(t,e,r){if(e)for(var n=0;n<r.length;n++){var i=r[n];t[i]=e[i]}return t},defaultsWithPropList:function(t,e,r){if(e)for(var n=0;n<r.length;n++){var i=r[n];null==t[i]&&(t[i]=e[i])}return t},each:function(t,e,r){if(t&&e)if(t.forEach&&t.forEach===Ue)t.forEach(e,r);else if(t.length===+t.length)for(var n=0,i=t.length;n<i;n++)e.call(r,t[n],n,t);else for(var a in t)t.hasOwnProperty(a)&&e.call(r,t[a],a,t)},isObject:function(t){return t===Object(t)},isArray:function(t){return Array.isArray(t)},isArrayLike:function(t){return!!t&&t.length===+t.length},clone:function(t){if(Fe.isObject(t)){if(Fe.isArray(t))return t.slice();if(Fe.isArrayLike(t)){for(var e=new t.constructor(t.length),r=0;r<t.length;r++)e[r]=t[r];return e}return Fe.extend({},t)}return t}},ke=function(){this.__uid__=Fe.genGUID()};ke.__initializers__=[function(t){Fe.extend(this,t)}],Fe.extend(ke,Ie),Fe.extend(ke.prototype,Oe);var He,Ge={get:b},Ve={};Ve.supportWebGL=function(){if(null==He)try{var t=document.createElement("canvas")
;if(!(t.getContext("webgl")||t.getContext("experimental-webgl")))throw new Error}catch(t){He=!1}return He},Ve.Int8Array="undefined"==typeof Int8Array?Array:Int8Array,Ve.Uint8Array="undefined"==typeof Uint8Array?Array:Uint8Array,Ve.Uint16Array="undefined"==typeof Uint16Array?Array:Uint16Array,Ve.Uint32Array="undefined"==typeof Uint32Array?Array:Uint32Array,Ve.Int16Array="undefined"==typeof Int16Array?Array:Int16Array,Ve.Float32Array="undefined"==typeof Float32Array?Array:Float32Array,Ve.Float64Array="undefined"==typeof Float64Array?Array:Float64Array;var We={};"undefined"!=typeof window?We=window:"undefined"!=typeof global&&(We=global),Ve.requestAnimationFrame=We.requestAnimationFrame||We.msRequestAnimationFrame||We.mozRequestAnimationFrame||We.webkitRequestAnimationFrame||function(t){setTimeout(t,16)},Ve.createCanvas=function(){return document.createElement("canvas")},Ve.createImage=function(){return new We.Image},Ve.request={get:Ge.get},Ve.addEventListener=function(t,e,r,n){t.addEventListener(e,r,n)},Ve.removeEventListener=function(t,e,r){t.removeEventListener(e,r)};var ze=ke.extend(function(){return{stage:null,_clips:[],_running:!1,_time:0,_paused:!1,_pausedTime:0}},{addAnimator:function(t){t.animation=this;for(var e=t.getClips(),r=0;r<e.length;r++)this.addClip(e[r])},addClip:function(t){this._clips.indexOf(t)<0&&this._clips.push(t)},removeClip:function(t){var e=this._clips.indexOf(t);e>=0&&this._clips.splice(e,1)},removeAnimator:function(t){for(var e=t.getClips(),r=0;r<e.length;r++)this.removeClip(e[r]);t.animation=null},_update:function(){for(var t=Date.now()-this._pausedTime,e=t-this._time,r=this._clips,n=r.length,i=[],a=[],o=0;o<n;o++){var s=r[o],u=s.step(t,e,!1);u&&(i.push(u),a.push(s))}for(var o=0;o<n;)r[o]._needsRemove?(r[o]=r[n-1],r.pop(),n--):o++;n=i.length;for(var o=0;o<n;o++)a[o].fire(i[o]);this._time=t,this.trigger("frame",e),this.stage&&this.stage.render&&this.stage.render()},start:function(){function t(){e._running&&(r(t),e._paused||e._update())}var e=this;this._running=!0,this._time=Date.now(),this._pausedTime=0;var r=Ve.requestAnimationFrame;r(t)},stop:function(){this._running=!1},pause:function(){this._paused||(this._pauseStart=Date.now(),this._paused=!0)},resume:function(){this._paused&&(this._pausedTime+=Date.now()-this._pauseStart,this._paused=!1)},removeClipsAll:function(){this._clips=[]},animate:function(t,e){e=e||{};var r=new d(t,e.loop,e.getter,e.setter,e.interpolater);return r.animation=this,r}}),Xe=function(t){t=t||{},_e.call(this,t),this.tracks=t.tracks||[],this.calcLifeFromTracks()};Xe.prototype=Object.create(_e.prototype),Xe.prototype.constructor=Xe,Xe.prototype.step=function(t,e,r){var n=_e.prototype.step.call(this,t,e,!0);if("finish"!==n){var t=this.getElapsedTime();this._range&&(t=this._range[0]+t),this.setTime(t)}return r||"paused"===n||this.fire("frame"),n},Xe.prototype.setRange=function(t){this.calcLifeFromTracks(),this._range=t,t&&(t[1]=Math.min(t[1],this.life),t[0]=Math.min(t[0],this.life),this.life=t[1]-t[0])},Xe.prototype.setTime=function(t){for(var e=0;e<this.tracks.length;e++)this.tracks[e].setTime(t)},Xe.prototype.calcLifeFromTracks=function(){this.life=0;for(var t=0;t<this.tracks.length;t++)this.life=Math.max(this.life,this.tracks[t].getMaxTime())},Xe.prototype.addTrack=function(t){this.tracks.push(t),this.calcLifeFromTracks()},Xe.prototype.removeTarck=function(t){var e=this.tracks.indexOf(t);e>=0&&this.tracks.splice(e,1)},Xe.prototype.getSubClip=function(t,e,r){for(var n=new Xe({name:this.name}),i=0;i<this.tracks.length;i++){var a=this.tracks[i].getSubTrack(t,e);n.addTrack(a)}return void 0!==r&&n.setLoop(r),n.life=e-t,n},Xe.prototype.blend1D=function(t,e,r){for(var n=0;n<this.tracks.length;n++){var i=t.tracks[n],a=e.tracks[n];this.tracks[n].blend1D(i,a,r)}},Xe.prototype.additiveBlend=function(t,e){for(var r=0;r<this.tracks.length;r++){var n=t.tracks[r],i=e.tracks[r];this.tracks[r].additiveBlend(n,i)}},Xe.prototype.subtractiveBlend=function(t,e){for(var r=0;r<this.tracks.length;r++){var n=t.tracks[r],i=e.tracks[r];this.tracks[r].subtractiveBlend(n,i)}},Xe.prototype.blend2D=function(t,e,r,n,i){for(var a=0;a<this.tracks.length;a++){var o=t.tracks[a],s=e.tracks[a],u=r.tracks[a];this.tracks[a].blend2D(o,s,u,n,i)}},Xe.prototype.copy=function(t){for(var e=0;e<this.tracks.length;e++){var r=t.tracks[e],n=this.tracks[e];Ce.copy(n.position,r.position),Ce.copy(n.scale,r.scale),Ne.copy(n.rotation,r.rotation)}},Xe.prototype.clone=function(){for(var t=_e.prototype.clone.call(this),e=0;e<this.tracks.length;e++)t.addTrack(this.tracks[e].clone());return t.life=this.life,t};var je=["OES_texture_float","OES_texture_half_float","OES_texture_float_linear","OES_texture_half_float_linear","OES_standard_derivatives","OES_vertex_array_object","OES_element_index_uint","WEBGL_compressed_texture_s3tc","WEBGL_depth_texture","EXT_texture_filter_anisotropic","EXT_shader_texture_lod","WEBGL_draw_buffers","EXT_frag_depth","EXT_sRGB","ANGLE_instanced_arrays"],qe=["MAX_TEXTURE_SIZE","MAX_CUBE_MAP_TEXTURE_SIZE"],Ye={DEPTH_BUFFER_BIT:256,STENCIL_BUFFER_BIT:1024,COLOR_BUFFER_BIT:16384,POINTS:0,LINES:1,LINE_LOOP:2,LINE_STRIP:3,TRIANGLES:4,TRIANGLE_STRIP:5,TRIANGLE_FAN:6,ZERO:0,ONE:1,SRC_COLOR:768,ONE_MINUS_SRC_COLOR:769,SRC_ALPHA:770,ONE_MINUS_SRC_ALPHA:771,DST_ALPHA:772,ONE_MINUS_DST_ALPHA:773,DST_COLOR:774,ONE_MINUS_DST_COLOR:775,SRC_ALPHA_SATURATE:776,FUNC_ADD:32774,BLEND_EQUATION:32777,BLEND_EQUATION_RGB:32777,BLEND_EQUATION_ALPHA:34877,FUNC_SUBTRACT:32778,FUNC_REVERSE_SUBTRACT:32779,BLEND_DST_RGB:32968,BLEND_SRC_RGB:32969,BLEND_DST_ALPHA:32970,BLEND_SRC_ALPHA:32971,CONSTANT_COLOR:32769,ONE_MINUS_CONSTANT_COLOR:32770,CONSTANT_ALPHA:32771,ONE_MINUS_CONSTANT_ALPHA:32772,BLEND_COLOR:32773,ARRAY_BUFFER:34962,ELEMENT_ARRAY_BUFFER:34963,ARRAY_BUFFER_BINDING:34964,ELEMENT_ARRAY_BUFFER_BINDING:34965,STREAM_DRAW:35040,STATIC_DRAW:35044,DYNAMIC_DRAW:35048,BUFFER_SIZE:34660,BUFFER_USAGE:34661,CURRENT_VERTEX_ATTRIB:34342,FRONT:1028,BACK:1029,FRONT_AND_BACK:1032,CULL_FACE:2884,BLEND:3042,DITHER:3024,STENCIL_TEST:2960,DEPTH_TEST:2929,SCISSOR_TEST:3089,POLYGON_OFFSET_FILL:32823,SAMPLE_ALPHA_TO_COVERAGE:32926,SAMPLE_COVERAGE:32928,NO_ERROR:0,INVALID_ENUM:1280,INVALID_VALUE:1281,INVALID_OPERATION:1282,OUT_OF_MEMORY:1285,CW:2304,CCW:2305,LINE_WIDTH:2849,ALIASED_POINT_SIZE_RANGE:33901,ALIASED_LINE_WIDTH_RANGE:33902,CULL_FACE_MODE:2885,FRONT_FACE:2886,DEPTH_RANGE:2928,DEPTH_WRITEMASK:2930,DEPTH_CLEAR_VALUE:2931,DEPTH_FUNC:2932,STENCIL_CLEAR_VALUE:2961,STENCIL_FUNC:2962,STENCIL_FAIL:2964,STENCIL_PASS_DEPTH_FAIL:2965,STENCIL_PASS_DEPTH_PASS:2966,STENCIL_REF:2967,STENCIL_VALUE_MASK:2963,STENCIL_WRITEMASK:2968,STENCIL_BACK_FUNC:34816,STENCIL_BACK_FAIL:34817,STENCIL_BACK_PASS_DEPTH_FAIL:34818,STENCIL_BACK_PASS_DEPTH_PASS:34819,STENCIL_BACK_REF:36003,STENCIL_BACK_VALUE_MASK:36004,STENCIL_BACK_WRITEMASK:36005,VIEWPORT:2978,SCISSOR_BOX:3088,COLOR_CLEAR_VALUE:3106,COLOR_WRITEMASK:3107,UNPACK_ALIGNMENT:3317,PACK_ALIGNMENT:3333,MAX_TEXTURE_SIZE:3379,MAX_VIEWPORT_DIMS:3386,SUBPIXEL_BITS:3408,RED_BITS:3410,GREEN_BITS:3411,BLUE_BITS:3412,ALPHA_BITS:3413,DEPTH_BITS:3414,STENCIL_BITS:3415,POLYGON_OFFSET_UNITS:10752,POLYGON_OFFSET_FACTOR:32824,TEXTURE_BINDING_2D:32873,SAMPLE_BUFFERS:32936,SAMPLES:32937,SAMPLE_COVERAGE_VALUE:32938,SAMPLE_COVERAGE_INVERT:32939,COMPRESSED_TEXTURE_FORMATS:34467,DONT_CARE:4352,FASTEST:4353,NICEST:4354,GENERATE_MIPMAP_HINT:33170,BYTE:5120,UNSIGNED_BYTE:5121,SHORT:5122,UNSIGNED_SHORT:5123,INT:5124,UNSIGNED_INT:5125,FLOAT:5126,DEPTH_COMPONENT:6402,ALPHA:6406,RGB:6407,RGBA:6408,LUMINANCE:6409,LUMINANCE_ALPHA:6410,UNSIGNED_SHORT_4_4_4_4:32819,UNSIGNED_SHORT_5_5_5_1:32820,UNSIGNED_SHORT_5_6_5:33635,FRAGMENT_SHADER:35632,VERTEX_SHADER:35633,MAX_VERTEX_ATTRIBS:34921,MAX_VERTEX_UNIFORM_VECTORS:36347,MAX_VARYING_VECTORS:36348,MAX_COMBINED_TEXTURE_IMAGE_UNITS:35661,MAX_VERTEX_TEXTURE_IMAGE_UNITS:35660,MAX_TEXTURE_IMAGE_UNITS:34930,MAX_FRAGMENT_UNIFORM_VECTORS:36349,SHADER_TYPE:35663,DELETE_STATUS:35712,LINK_STATUS:35714,VALIDATE_STATUS:35715,ATTACHED_SHADERS:35717,ACTIVE_UNIFORMS:35718,ACTIVE_ATTRIBUTES:35721,SHADING_LANGUAGE_VERSION:35724,CURRENT_PROGRAM:35725,NEVER:512,LESS:513,EQUAL:514,LEQUAL:515,GREATER:516,NOTEQUAL:517,GEQUAL:518,ALWAYS:519,KEEP:7680,REPLACE:7681,INCR:7682,DECR:7683,INVERT:5386,INCR_WRAP:34055,DECR_WRAP:34056,VENDOR:7936,RENDERER:7937,VERSION:7938,NEAREST:9728,LINEAR:9729,NEAREST_MIPMAP_NEAREST:9984,LINEAR_MIPMAP_NEAREST:9985,NEAREST_MIPMAP_LINEAR:9986,LINEAR_MIPMAP_LINEAR:9987,TEXTURE_MAG_FILTER:10240,TEXTURE_MIN_FILTER:10241,TEXTURE_WRAP_S:10242,TEXTURE_WRAP_T:10243,TEXTURE_2D:3553,TEXTURE:5890,TEXTURE_CUBE_MAP:34067,TEXTURE_BINDING_CUBE_MAP:34068,TEXTURE_CUBE_MAP_POSITIVE_X:34069,TEXTURE_CUBE_MAP_NEGATIVE_X:34070,TEXTURE_CUBE_MAP_POSITIVE_Y:34071,TEXTURE_CUBE_MAP_NEGATIVE_Y:34072,TEXTURE_CUBE_MAP_POSITIVE_Z:34073,TEXTURE_CUBE_MAP_NEGATIVE_Z:34074,MAX_CUBE_MAP_TEXTURE_SIZE:34076,TEXTURE0:33984,TEXTURE1:33985,TEXTURE2:33986,TEXTURE3:33987,TEXTURE4:33988,TEXTURE5:33989,TEXTURE6:33990,TEXTURE7:33991,TEXTURE8:33992,TEXTURE9:33993,TEXTURE10:33994,TEXTURE11:33995,TEXTURE12:33996,TEXTURE13:33997,TEXTURE14:33998,TEXTURE15:33999,TEXTURE16:34e3,TEXTURE17:34001,TEXTURE18:34002,TEXTURE19:34003,TEXTURE20:34004,TEXTURE21:34005,TEXTURE22:34006,TEXTURE23:34007,TEXTURE24:34008,TEXTURE25:34009,TEXTURE26:34010,TEXTURE27:34011,TEXTURE28:34012,TEXTURE29:34013,TEXTURE30:34014,TEXTURE31:34015,ACTIVE_TEXTURE:34016,REPEAT:10497,CLAMP_TO_EDGE:33071,MIRRORED_REPEAT:33648,FLOAT_VEC2:35664,FLOAT_VEC3:35665,FLOAT_VEC4:35666,INT_VEC2:35667,INT_VEC3:35668,INT_VEC4:35669,BOOL:35670,BOOL_VEC2:35671,BOOL_VEC3:35672,BOOL_VEC4:35673,FLOAT_MAT2:35674,FLOAT_MAT3:35675,FLOAT_MAT4:35676,SAMPLER_2D:35678,SAMPLER_CUBE:35680,VERTEX_ATTRIB_ARRAY_ENABLED:34338,VERTEX_ATTRIB_ARRAY_SIZE:34339,VERTEX_ATTRIB_ARRAY_STRIDE:34340,VERTEX_ATTRIB_ARRAY_TYPE:34341,VERTEX_ATTRIB_ARRAY_NORMALIZED:34922,VERTEX_ATTRIB_ARRAY_POINTER:34373,VERTEX_ATTRIB_ARRAY_BUFFER_BINDING:34975,COMPILE_STATUS:35713,LOW_FLOAT:36336,MEDIUM_FLOAT:36337,HIGH_FLOAT:36338,LOW_INT:36339,MEDIUM_INT:36340,HIGH_INT:36341,FRAMEBUFFER:36160,RENDERBUFFER:36161,RGBA4:32854,RGB5_A1:32855,RGB565:36194,DEPTH_COMPONENT16:33189,STENCIL_INDEX:6401,STENCIL_INDEX8:36168,DEPTH_STENCIL:34041,RENDERBUFFER_WIDTH:36162,RENDERBUFFER_HEIGHT:36163,RENDERBUFFER_INTERNAL_FORMAT:36164,RENDERBUFFER_RED_SIZE:36176,RENDERBUFFER_GREEN_SIZE:36177,RENDERBUFFER_BLUE_SIZE:36178,RENDERBUFFER_ALPHA_SIZE:36179,RENDERBUFFER_DEPTH_SIZE:36180,RENDERBUFFER_STENCIL_SIZE:36181,FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE:36048,FRAMEBUFFER_ATTACHMENT_OBJECT_NAME:36049,FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL:36050,FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE:36051,COLOR_ATTACHMENT0:36064,DEPTH_ATTACHMENT:36096,STENCIL_ATTACHMENT:36128,DEPTH_STENCIL_ATTACHMENT:33306,NONE:0,FRAMEBUFFER_COMPLETE:36053,FRAMEBUFFER_INCOMPLETE_ATTACHMENT:36054,FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT:36055,FRAMEBUFFER_INCOMPLETE_DIMENSIONS:36057,FRAMEBUFFER_UNSUPPORTED:36061,FRAMEBUFFER_BINDING:36006,RENDERBUFFER_BINDING:36007,MAX_RENDERBUFFER_SIZE:34024,INVALID_FRAMEBUFFER_OPERATION:1286,UNPACK_FLIP_Y_WEBGL:37440,UNPACK_PREMULTIPLY_ALPHA_WEBGL:37441,CONTEXT_LOST_WEBGL:37442,UNPACK_COLORSPACE_CONVERSION_WEBGL:37443,BROWSER_DEFAULT_WEBGL:37444},Ke=function(){this.head=null,this.tail=null,this._length=0};Ke.prototype.insert=function(t){var e=new Ke.Entry(t);return this.insertEntry(e),e},Ke.prototype.insertAt=function(t,e){if(!(t<0)){for(var r=this.head,n=0;r&&n!=t;)r=r.next,n++;if(r){var i=new Ke.Entry(e),a=r.prev;a?(a.next=i,i.prev=a):this.head=i,i.next=r,r.prev=i}else this.insert(e)}},Ke.prototype.insertBeforeEntry=function(t,e){var r=new Ke.Entry(t),n=e.prev;n?(n.next=r,r.prev=n):this.head=r,r.next=e,e.prev=r,this._length++},Ke.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,this.tail=t):this.head=this.tail=t,this._length++},Ke.prototype.remove=function(t){var e=t.prev,r=t.next;e?e.next=r:this.head=r,r?r.prev=e:this.tail=e,t.next=t.prev=null,this._length--},Ke.prototype.removeAt=function(t){if(!(t<0)){for(var e=this.head,r=0;e&&r!=t;)e=e.next,r++;return e?(this.remove(e),e.value):void 0}},Ke.prototype.getHead=function(){if(this.head)return this.head.value},Ke.prototype.getTail=function(){if(this.tail)return this.tail.value},Ke.prototype.getAt=function(t){if(!(t<0)){for(var e=this.head,r=0;e&&r!=t;)e=e.next,r++;return e.value}},Ke.prototype.indexOf=function(t){for(var e=this.head,r=0;e;){if(e.value===t)return r;e=e.next,r++}},Ke.prototype.length=function(){return this._length},Ke.prototype.isEmpty=function(){return 0===this._length},Ke.prototype.forEach=function(t,e){for(var r=this.head,n=0,i=void 0!==e;r;)i?t.call(e,r.value,n):t(r.value,n),r=r.next,n++},Ke.prototype.clear=function(){this.tail=this.head=null,this._length=0},Ke.Entry=function(t){this.value=t,this.next=null,this.prev=null};var Ze=function(t){this._list=new Ke,this._map={},this._maxSize=t||10};Ze.prototype.setMaxSize=function(t){this._maxSize=t},Ze.prototype.put=function(t,e){if(!this._map.hasOwnProperty(t)){var r=this._list.length();if(r>=this._maxSize&&r>0){var n=this._list.head;this._list.remove(n),delete this._map[n.key]}var i=this._list.insert(e);i.key=t,this._map[t]=i}},Ze.prototype.get=function(t){var e=this._map[t];if(this._map.hasOwnProperty(t))return e!==this._list.tail&&(this._list.remove(e),this._list.insertEntry(e)),e.value},Ze.prototype.remove=function(t){var e=this._map[t];void 0!==e&&(delete this._map[t],this._list.remove(e))},Ze.prototype.clear=function(){this._list.clear(),this._map={}};var Je={},Qe={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]},$e=new Ze(20),tr=null;Je.parse=function(t,e){if(t){e=e||[];var r=$e.get(t);if(r)return O(e,r);t+="";var n=t.replace(/ /g,"").toLowerCase();if(n in Qe)return O(e,Qe[n]),D(t,e),e;if("#"!==n.charAt(0)){var i=n.indexOf("("),a=n.indexOf(")");if(-1!==i&&a+1===n.length){var o=n.substr(0,i),s=n.substr(i+1,a-(i+1)).split(","),u=1;switch(o){case"rgba":if(4!==s.length)return void I(e,0,0,0,1);u=L(s.pop());case"rgb":return 3!==s.length?void I(e,0,0,0,1):(I(e,R(s[0]),R(s[1]),R(s[2]),u),D(t,e),e);case"hsla":return 4!==s.length?void I(e,0,0,0,1):(s[3]=L(s[3]),B(s,e),D(t,e),e);case"hsl":return 3!==s.length?void I(e,0,0,0,1):(B(s,e),D(t,e),e);default:return}}I(e,0,0,0,1)}else{if(4===n.length){var l=parseInt(n.substr(1),16);return l>=0&&l<=4095?(I(e,(3840&l)>>4|(3840&l)>>8,240&l|(240&l)>>4,15&l|(15&l)<<4,1),D(t,e),e):void I(e,0,0,0,1)}if(7===n.length){var l=parseInt(n.substr(1),16);return l>=0&&l<=16777215?(I(e,(16711680&l)>>16,(65280&l)>>8,255&l,1),D(t,e),e):void I(e,0,0,0,1)}}}},Je.parseToFloat=function(t,e){if(e=Je.parse(t,e))return e[0]/=255,e[1]/=255,e[2]/=255,e},Je.lift=function(t,e){var r=Je.parse(t);if(r){for(var n=0;n<3;n++)r[n]=e<0?r[n]*(1-e)|0:(255-r[n])*e+r[n]|0;return Je.stringify(r,4===r.length?"rgba":"rgb")}},Je.toHex=function(t){var e=Je.parse(t);if(e)return((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1)},Je.fastLerp=function(t,e,r){if(e&&e.length&&t>=0&&t<=1){r=r||[];var n=t*(e.length-1),i=Math.floor(n),a=Math.ceil(n),o=e[i],s=e[a],u=n-i;return r[0]=M(P(o[0],s[0],u)),r[1]=M(P(o[1],s[1],u)),r[2]=M(P(o[2],s[2],u)),r[3]=C(P(o[3],s[3],u)),r}},Je.fastMapToColor=Je.fastLerp,Je.lerp=function(t,e,r){if(e&&e.length&&t>=0&&t<=1){var n=t*(e.length-1),i=Math.floor(n),a=Math.ceil(n),o=Je.parse(e[i]),s=Je.parse(e[a]),u=n-i,l=Je.stringify([M(P(o[0],s[0],u)),M(P(o[1],s[1],u)),M(P(o[2],s[2],u)),C(P(o[3],s[3],u))],"rgba");return r?{color:l,leftIndex:i,rightIndex:a,value:n}:l}},Je.mapToColor=Je.lerp,Je.modifyHSL=function(t,e,r,n){if(t=Je.parse(t))return t=U(t),null!=e&&(t[0]=w(e)),null!=r&&(t[1]=L(r)),null!=n&&(t[2]=L(n)),Je.stringify(B(t),"rgba")},Je.modifyAlpha=function(t,e){if((t=Je.parse(t))&&null!=e)return t[3]=C(e),Je.stringify(t,"rgba")},Je.stringify=function(t,e){if(t&&t.length){var r=t[0]+","+t[1]+","+t[2];return"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(r+=","+t[3]),e+"("+r+")"}};var er=Je.parseToFloat,rr={},nr=ke.extend(function(){return{name:"",depthTest:!0,depthMask:!0,transparent:!1,blend:null,autoUpdateTextureStatus:!0,uniforms:{},vertexDefines:{},fragmentDefines:{},_textureStatus:{},_enabledUniforms:null}},function(){this.name||(this.name="MATERIAL_"+this.__uid__),this.shader&&this.attachShader(this.shader,!0)},{precision:"highp",setUniform:function(t,e){void 0===e&&console.warn('Uniform value "'+t+'" is undefined');var r=this.uniforms[t];r&&("string"==typeof e&&(e=er(e)||e),r.value=e,this.autoUpdateTextureStatus&&"t"===r.type&&(e?this.enableTexture(t):this.disableTexture(t)))},setUniforms:function(t){for(var e in t){var r=t[e];this.setUniform(e,r)}},isUniformEnabled:function(t){return this._enabledUniforms.indexOf(t)>=0},getEnabledUniforms:function(){return this._enabledUniforms},getTextureUniforms:function(){return this._textureUniforms},set:function(t,e){if("object"==typeof t)for(var r in t){var n=t[r];this.setUniform(r,n)}else this.setUniform(t,e)},get:function(t){var e=this.uniforms[t];if(e)return e.value},attachShader:function(t,e){var r=this.uniforms;this.uniforms=t.createUniforms(),this.shader=t;var n=this.uniforms;this._enabledUniforms=Object.keys(n),this._enabledUniforms.sort(),this._textureUniforms=this._enabledUniforms.filter(function(t){var e=this.uniforms[t].type;return"t"===e||"tv"===e},this);var i=this.vertexDefines,a=this.fragmentDefines;if(this.vertexDefines=Fe.clone(t.vertexDefines),this.fragmentDefines=Fe.clone(t.fragmentDefines),e){for(var o in r)n[o]&&(n[o].value=r[o].value);Fe.defaults(this.vertexDefines,i),Fe.defaults(this.fragmentDefines,a)}var s={};for(var u in t.textures)s[u]={shaderType:t.textures[u].shaderType,type:t.textures[u].type,enabled:!(!e||!this._textureStatus[u])&&this._textureStatus[u].enabled};this._textureStatus=s,this._programKey=""},clone:function(){var t=new this.constructor({name:this.name,shader:this.shader});for(var e in this.uniforms)t.uniforms[e].value=this.uniforms[e].value;return t.depthTest=this.depthTest,t.depthMask=this.depthMask,t.transparent=this.transparent,t.blend=this.blend,t.vertexDefines=Fe.clone(this.vertexDefines),t.fragmentDefines=Fe.clone(this.fragmentDefines),t.enableTexture(this.getEnabledTextures()),t.precision=this.precision,t},define:function(t,e,r){var n=this.vertexDefines,i=this.fragmentDefines;"vertex"!==t&&"fragment"!==t&&"both"!==t&&arguments.length<3&&(r=e,e=t,t="both"),r=null!=r?r:null,"vertex"!==t&&"both"!==t||n[e]!==r&&(n[e]=r,this._programKey=""),"fragment"!==t&&"both"!==t||i[e]!==r&&(i[e]=r,"both"!==t&&(this._programKey=""))},undefine:function(t,e){"vertex"!==t&&"fragment"!==t&&"both"!==t&&arguments.length<2&&(e=t,t="both"),"vertex"!==t&&"both"!==t||this.isDefined("vertex",e)&&(delete this.vertexDefines[e],this._programKey=""),"fragment"!==t&&"both"!==t||this.isDefined("fragment",e)&&(delete this.fragmentDefines[e],"both"!==t&&(this._programKey=""))},isDefined:function(t,e){switch(t){case"vertex":return void 0!==this.vertexDefines[e];case"fragment":return void 0!==this.fragmentDefines[e]}},getDefine:function(t,e){switch(t){case"vertex":return this.vertexDefines[e];case"fragment":return this.fragmentDefines[e]}},enableTexture:function(t){if(Array.isArray(t))for(var e=0;e<t.length;e++)this.enableTexture(t[e]);else{var r=this._textureStatus[t];if(r){r.enabled||(r.enabled=!0,this._programKey="")}}},enableTexturesAll:function(){var t=this._textureStatus;for(var e in t)t[e].enabled=!0;this._programKey=""},disableTexture:function(t){if(Array.isArray(t))for(var e=0;e<t.length;e++)this.disableTexture(t[e]);else{var r=this._textureStatus[t];if(r){!r.enabled||(r.enabled=!1,this._programKey="")}}},disableTexturesAll:function(){var t=this._textureStatus;for(var e in t)t[e].enabled=!1;this._programKey=""},isTextureEnabled:function(t){var e=this._textureStatus;return!!e[t]&&e[t].enabled},getEnabledTextures:function(){var t=[],e=this._textureStatus;for(var r in e)e[r].enabled&&t.push(r);return t},dirtyDefines:function(){this._programKey=""},getProgramKey:function(){return this._programKey||(this._programKey=k(this.vertexDefines,this.fragmentDefines,this.getEnabledTextures())),this._programKey}}),ir={},ar=new Ve.Float32Array(16),or=ke.extend({uniformSemantics:{},attributes:{}},function(){this._locations={},this._textureSlot=0,this._program=null},{bind:function(t){this._textureSlot=0,t.gl.useProgram(this._program)},hasUniform:function(t){var e=this._locations[t];return null!==e&&void 0!==e},useTextureSlot:function(t,e,r){e&&(t.gl.activeTexture(t.gl.TEXTURE0+r),e.isRenderable()?e.bind(t):e.unbind(t))},currentTextureSlot:function(){return this._textureSlot},resetTextureSlot:function(t){this._textureSlot=t||0},takeCurrentTextureSlot:function(t,e){var r=this._textureSlot;return this.useTextureSlot(t,e,r),this._textureSlot++,r},setUniform:function(t,e,r,n){var i=this._locations,a=i[r];if(null===a||void 0===a)return!1;switch(e){case"m4":if(!(n instanceof Float32Array)){for(var o=0;o<n.length;o++)ar[o]=n[o];n=ar}t.uniformMatrix4fv(a,!1,n);break;case"2i":t.uniform2i(a,n[0],n[1]);break;case"2f":t.uniform2f(a,n[0],n[1]);break;case"3i":t.uniform3i(a,n[0],n[1],n[2]);break;case"3f":t.uniform3f(a,n[0],n[1],n[2]);break;case"4i":t.uniform4i(a,n[0],n[1],n[2],n[3]);break;case"4f":t.uniform4f(a,n[0],n[1],n[2],n[3]);break;case"1i":t.uniform1i(a,n);break;case"1f":t.uniform1f(a,n);break;case"1fv":t.uniform1fv(a,n);break;case"1iv":t.uniform1iv(a,n);break;case"2iv":t.uniform2iv(a,n);break;case"2fv":t.uniform2fv(a,n);break;case"3iv":t.uniform3iv(a,n);break;case"3fv":t.uniform3fv(a,n);break;case"4iv":t.uniform4iv(a,n);break;case"4fv":t.uniform4fv(a,n);break;case"m2":case"m2v":t.uniformMatrix2fv(a,!1,n);break;case"m3":case"m3v":t.uniformMatrix3fv(a,!1,n);break;case"m4v":if(Array.isArray(n)&&Array.isArray(n[0])){for(var s=new Ve.Float32Array(16*n.length),u=0,o=0;o<n.length;o++)for(var l=n[o],h=0;h<16;h++)s[u++]=l[h];t.uniformMatrix4fv(a,!1,s)}else t.uniformMatrix4fv(a,!1,n)}return!0},setUniformOfSemantic:function(t,e,r){var n=this.uniformSemantics[e];return!!n&&this.setUniform(t,n.type,n.symbol,r)},enableAttributes:function(t,e,r){var n,i=t.gl,a=this._program,o=this._locations;(n=r?r.__enabledAttributeList:ir[t.__uid__])||(n=r?r.__enabledAttributeList=[]:ir[t.__uid__]=[]);for(var s=[],u=0;u<e.length;u++){var l=e[u];if(this.attributes[l]){var h=o[l];if(null==h){if(-1===(h=i.getAttribLocation(a,l))){s[u]=-1;continue}o[l]=h}s[u]=h,n[h]?n[h]=2:n[h]=1}else s[u]=-1}for(var u=0;u<n.length;u++)switch(n[u]){case 1:i.enableVertexAttribArray(u),n[u]=3;break;case 2:n[u]=3;break;case 3:i.disableVertexAttribArray(u),n[u]=0}return s},getAttribLocation:function(t,e){var r=this._locations,n=r[e];return null==n&&(n=t.getAttribLocation(this._program,e),r[e]=n),n},buildProgram:function(t,e,r,n){var i=t.createShader(t.VERTEX_SHADER),a=t.createProgram();t.shaderSource(i,r),t.compileShader(i);var o=t.createShader(t.FRAGMENT_SHADER);t.shaderSource(o,n),t.compileShader(o);var s=G(t,i,r);if(s)return s;if(s=G(t,o,n))return s;if(t.attachShader(a,i),t.attachShader(a,o),e.attributeSemantics.POSITION)t.bindAttribLocation(a,0,e.attributeSemantics.POSITION.symbol);else{var u=Object.keys(this.attributes);t.bindAttribLocation(a,0,u[0])}if(t.linkProgram(a),t.deleteShader(i),t.deleteShader(o),this._program=a,this.vertexCode=r,this.fragmentCode=n,!t.getProgramParameter(a,t.LINK_STATUS))return"Could not link program\n"+t.getProgramInfoLog(a);for(var l=0;l<e.uniforms.length;l++){var h=e.uniforms[l];this._locations[h]=t.getUniformLocation(a,h)}}}),sr=/for\s*?\(int\s*?_idx_\s*\=\s*([\w-]+)\;\s*_idx_\s*<\s*([\w-]+);\s*_idx_\s*\+\+\s*\)\s*\{\{([\s\S]+?)(?=\}\})\}\}/g;j.prototype.getProgram=function(t,e,r){var n=this._cache,i=t.isSkinnedMesh&&t.isSkinnedMesh(),a=t.isInstancedMesh&&t.isInstancedMesh(),o="s"+e.shader.shaderID+"m"+e.getProgramKey();r&&(o+="se"+r.getProgramKey(t.lightGroup)),i&&(o+=",sk"+t.joints.length),a&&(o+=",is");var s=n[o];if(s)return s;var u=r?r.getLightsNumbers(t.lightGroup):{},l=this._renderer,h=l.gl,c=e.getEnabledTextures(),f="";if(i){var d={SKINNING:null,JOINT_COUNT:t.joints.length};t.joints.length>l.getMaxJointNumber()&&(d.USE_SKIN_MATRICES_TEXTURE=null),f+="\n"+W(d)+"\n"}a&&(f+="\n#define INSTANCING\n");var p=f+W(e.vertexDefines,u,c),m=f+W(e.fragmentDefines,u,c),_=p+"\n"+e.shader.vertex,g=["OES_standard_derivatives","EXT_shader_texture_lod"].filter(function(t){return null!=l.getGLExtension(t)});g.indexOf("EXT_shader_texture_lod")>=0&&(m+="\n#define SUPPORT_TEXTURE_LOD"),g.indexOf("OES_standard_derivatives")>=0&&(m+="\n#define SUPPORT_STANDARD_DERIVATIVES");var v=z(g)+"\n"+X(e.precision)+"\n"+m+"\n"+e.shader.fragment,y=V(_,e.vertexDefines,u),T=V(v,e.fragmentDefines,u),s=new or;s.uniformSemantics=e.shader.uniformSemantics,s.attributes=e.shader.attributes;var x=s.buildProgram(h,e.shader,y,T);return s.__error=x,n[o]=s,s};var ur=/uniform\s+(bool|float|int|vec2|vec3|vec4|ivec2|ivec3|ivec4|mat2|mat3|mat4|sampler2D|samplerCube)\s+([\s\S]*?);/g,lr=/attribute\s+(float|int|vec2|vec3|vec4)\s+([\s\S]*?);/g,hr=/#define\s+(\w+)?(\s+[\d-.]+)?\s*;?\s*\n/g,cr={bool:"1i",int:"1i",sampler2D:"t",samplerCube:"t",float:"1f",vec2:"2f",vec3:"3f",vec4:"4f",ivec2:"2i",ivec3:"3i",ivec4:"4i",mat2:"m2",mat3:"m3",mat4:"m4"},fr={bool:function(){return!0},int:function(){return 0},float:function(){return 0},sampler2D:function(){return null},samplerCube:function(){return null},vec2:function(){return q(2)},vec3:function(){return q(3)},vec4:function(){return q(4)},ivec2:function(){return q(2)},ivec3:function(){return q(3)},ivec4:function(){return q(4)},mat2:function(){return q(4)},mat3:function(){return q(9)},mat4:function(){return q(16)},array:function(){return[]}},dr=["POSITION","NORMAL","BINORMAL","TANGENT","TEXCOORD","TEXCOORD_0","TEXCOORD_1","COLOR","JOINT","WEIGHT"],pr=["SKIN_MATRIX","VIEWPORT_SIZE","VIEWPORT","DEVICEPIXELRATIO","WINDOW_SIZE","NEAR","FAR","TIME"],mr=["WORLD","VIEW","PROJECTION","WORLDVIEW","VIEWPROJECTION","WORLDVIEWPROJECTION","WORLDINVERSE","VIEWINVERSE","PROJECTIONINVERSE","WORLDVIEWINVERSE","VIEWPROJECTIONINVERSE","WORLDVIEWPROJECTIONINVERSE","WORLDTRANSPOSE","VIEWTRANSPOSE","PROJECTIONTRANSPOSE","WORLDVIEWTRANSPOSE","VIEWPROJECTIONTRANSPOSE","WORLDVIEWPROJECTIONTRANSPOSE","WORLDINVERSETRANSPOSE","VIEWINVERSETRANSPOSE","PROJECTIONINVERSETRANSPOSE","WORLDVIEWINVERSETRANSPOSE","VIEWPROJECTIONINVERSETRANSPOSE","WORLDVIEWPROJECTIONINVERSETRANSPOSE"],_r={vec4:4,vec3:3,vec2:2,float:1},gr={},vr={};Q.prototype={constructor:Q,createUniforms:function(){var t={};for(var e in this.uniformTemplates){var r=this.uniformTemplates[e];t[e]={type:r.type,value:r.value()}}return t},_parseImport:function(){this._vertexCode=Q.parseImport(this.vertex),this._fragmentCode=Q.parseImport(this.fragment)},_addSemanticUniform:function(t,e,r){if(dr.indexOf(r)>=0)this.attributeSemantics[r]={symbol:t,type:e};else if(mr.indexOf(r)>=0){var n=!1,i=r;r.match(/TRANSPOSE$/)&&(n=!0,i=r.slice(0,-9)),this.matrixSemantics[r]={symbol:t,type:e,isTranspose:n,semanticNoTranspose:i}}else pr.indexOf(r)>=0&&(this.uniformSemantics[r]={symbol:t,type:e})},_addMaterialUniform:function(t,e,r,n,i,a){a[t]={type:r,value:i?fr.array:n||fr[e],semantic:null}},_parseUniforms:function(){function t(t){return null!=t?function(){return t}:null}function e(e,a,o){var s=J(a,o),u=[];for(var l in s){var h=s[l],c=h.semantic,f=l,d=cr[a],p=t(s[l].value);s[l].isArray&&(f+="["+s[l].arraySize+"]",d+="v"),u.push(f),n._uniformList.push(l),h.ignore||("sampler2D"!==a&&"samplerCube"!==a||(n.textures[l]={shaderType:i,type:a}),c?n._addSemanticUniform(l,d,c):n._addMaterialUniform(l,a,d,p,s[l].isArray,r))}return u.length>0?"uniform "+a+" "+u.join(",")+";\n":""}var r={},n=this,i="vertex";this._uniformList=[],this._vertexCode=this._vertexCode.replace(ur,e),i="fragment",this._fragmentCode=this._fragmentCode.replace(ur,e),n.matrixSemanticKeys=Object.keys(this.matrixSemantics),this.uniformTemplates=r},_parseAttributes:function(){function t(t,n,i){var a=J(n,i),o=_r[n]||1,s=[];for(var u in a){var l=a[u].semantic;if(e[u]={type:"float",size:o,semantic:l||null},l){if(dr.indexOf(l)<0)throw new Error('Unkown semantic "'+l+'"');r.attributeSemantics[l]={symbol:u,type:n}}s.push(u)}return"attribute "+n+" "+s.join(",")+";\n"}var e={},r=this;this._vertexCode=this._vertexCode.replace(lr,t),this.attributes=e},_parseDefines:function(){function t(t,n,i){var a="vertex"===r?e.vertexDefines:e.fragmentDefines;return a[n]||(a[n]="false"!==i&&("true"===i||(i?isNaN(parseFloat(i))?i.trim():parseFloat(i):null))),
""}var e=this,r="vertex";this._vertexCode=this._vertexCode.replace(hr,t),r="fragment",this._fragmentCode=this._fragmentCode.replace(hr,t)},clone:function(){var t=vr[this._shaderID];return new Q(t.vertex,t.fragment)}},Object.defineProperty&&(Object.defineProperty(Q.prototype,"shaderID",{get:function(){return this._shaderID}}),Object.defineProperty(Q.prototype,"vertex",{get:function(){return this._vertexCode}}),Object.defineProperty(Q.prototype,"fragment",{get:function(){return this._fragmentCode}}),Object.defineProperty(Q.prototype,"uniforms",{get:function(){return this._uniformList}}));var yr=/(@import)\s*([0-9a-zA-Z_\-\.]*)/g;Q.parseImport=function(t){return t=t.replace(yr,function(t,e,r){var t=Q.source(r);return t?Q.parseImport(t):(console.error('Shader chunk "'+r+'" not existed in library'),"")})};var Tr=/(@export)\s*([0-9a-zA-Z_\-\.]*)\s*\n([\s\S]*?)@end/g;Q.import=function(t){t.replace(Tr,function(t,e,r,n){var n=n.replace(/(^[\s\t\xa0\u3000]+)|([\u3000\xa0\s\t]+\x24)/g,"");if(n){for(var i,a=r.split("."),o=Q.codes,s=0;s<a.length-1;)i=a[s++],o[i]||(o[i]={}),o=o[i];i=a[s],o[i]=n}return n})},Q.codes={},Q.source=function(t){for(var e=t.split("."),r=Q.codes,n=0;r&&n<e.length;){r=r[e[n++]]}return"string"!=typeof r?(console.error('Shader "'+t+'" not existed in library'),""):r};var xr="@export clay.prez.vertex\nuniform mat4 WVP : WORLDVIEWPROJECTION;\nattribute vec3 pos : POSITION;\nattribute vec2 uv : TEXCOORD_0;\nuniform vec2 uvRepeat : [1.0, 1.0];\nuniform vec2 uvOffset : [0.0, 0.0];\n@import clay.chunk.skinning_header\n@import clay.chunk.instancing_header\nvarying vec2 v_Texcoord;\nvoid main()\n{\n vec4 P = vec4(pos, 1.0);\n#ifdef SKINNING\n @import clay.chunk.skin_matrix\n P = skinMatrixWS * P;\n#endif\n#ifdef INSTANCING\n @import clay.chunk.instancing_matrix\n P = instanceMat * P;\n#endif\n gl_Position = WVP * P;\n v_Texcoord = uv * uvRepeat + uvOffset;\n}\n@end\n@export clay.prez.fragment\nuniform sampler2D alphaMap;\nuniform float alphaCutoff: 0.0;\nvarying vec2 v_Texcoord;\nvoid main()\n{\n if (alphaCutoff > 0.0) {\n if (texture2D(alphaMap, v_Texcoord).a <= alphaCutoff) {\n discard;\n }\n }\n gl_FragColor = vec4(0.0,0.0,0.0,1.0);\n}\n@end",Er={};Er.create=function(){var t=new Ee(16);return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t},Er.clone=function(t){var e=new Ee(16);return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4],e[5]=t[5],e[6]=t[6],e[7]=t[7],e[8]=t[8],e[9]=t[9],e[10]=t[10],e[11]=t[11],e[12]=t[12],e[13]=t[13],e[14]=t[14],e[15]=t[15],e},Er.copy=function(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[8]=e[8],t[9]=e[9],t[10]=e[10],t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t},Er.identity=function(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t},Er.transpose=function(t,e){if(t===e){var r=e[1],n=e[2],i=e[3],a=e[6],o=e[7],s=e[11];t[1]=e[4],t[2]=e[8],t[3]=e[12],t[4]=r,t[6]=e[9],t[7]=e[13],t[8]=n,t[9]=a,t[11]=e[14],t[12]=i,t[13]=o,t[14]=s}else t[0]=e[0],t[1]=e[4],t[2]=e[8],t[3]=e[12],t[4]=e[1],t[5]=e[5],t[6]=e[9],t[7]=e[13],t[8]=e[2],t[9]=e[6],t[10]=e[10],t[11]=e[14],t[12]=e[3],t[13]=e[7],t[14]=e[11],t[15]=e[15];return t},Er.invert=function(t,e){var r=e[0],n=e[1],i=e[2],a=e[3],o=e[4],s=e[5],u=e[6],l=e[7],h=e[8],c=e[9],f=e[10],d=e[11],p=e[12],m=e[13],_=e[14],g=e[15],v=r*s-n*o,y=r*u-i*o,T=r*l-a*o,x=n*u-i*s,E=n*l-a*s,A=i*l-a*u,b=h*m-c*p,S=h*_-f*p,M=h*g-d*p,w=c*_-f*m,C=c*g-d*m,R=f*g-d*_,L=v*R-y*C+T*w+x*M-E*S+A*b;return L?(L=1/L,t[0]=(s*R-u*C+l*w)*L,t[1]=(i*C-n*R-a*w)*L,t[2]=(m*A-_*E+g*x)*L,t[3]=(f*E-c*A-d*x)*L,t[4]=(u*M-o*R-l*S)*L,t[5]=(r*R-i*M+a*S)*L,t[6]=(_*T-p*A-g*y)*L,t[7]=(h*A-f*T+d*y)*L,t[8]=(o*C-s*M+l*b)*L,t[9]=(n*M-r*C-a*b)*L,t[10]=(p*E-m*T+g*v)*L,t[11]=(c*T-h*E-d*v)*L,t[12]=(s*S-o*w-u*b)*L,t[13]=(r*w-n*S+i*b)*L,t[14]=(m*y-p*x-_*v)*L,t[15]=(h*x-c*y+f*v)*L,t):null},Er.adjoint=function(t,e){var r=e[0],n=e[1],i=e[2],a=e[3],o=e[4],s=e[5],u=e[6],l=e[7],h=e[8],c=e[9],f=e[10],d=e[11],p=e[12],m=e[13],_=e[14],g=e[15];return t[0]=s*(f*g-d*_)-c*(u*g-l*_)+m*(u*d-l*f),t[1]=-(n*(f*g-d*_)-c*(i*g-a*_)+m*(i*d-a*f)),t[2]=n*(u*g-l*_)-s*(i*g-a*_)+m*(i*l-a*u),t[3]=-(n*(u*d-l*f)-s*(i*d-a*f)+c*(i*l-a*u)),t[4]=-(o*(f*g-d*_)-h*(u*g-l*_)+p*(u*d-l*f)),t[5]=r*(f*g-d*_)-h*(i*g-a*_)+p*(i*d-a*f),t[6]=-(r*(u*g-l*_)-o*(i*g-a*_)+p*(i*l-a*u)),t[7]=r*(u*d-l*f)-o*(i*d-a*f)+h*(i*l-a*u),t[8]=o*(c*g-d*m)-h*(s*g-l*m)+p*(s*d-l*c),t[9]=-(r*(c*g-d*m)-h*(n*g-a*m)+p*(n*d-a*c)),t[10]=r*(s*g-l*m)-o*(n*g-a*m)+p*(n*l-a*s),t[11]=-(r*(s*d-l*c)-o*(n*d-a*c)+h*(n*l-a*s)),t[12]=-(o*(c*_-f*m)-h*(s*_-u*m)+p*(s*f-u*c)),t[13]=r*(c*_-f*m)-h*(n*_-i*m)+p*(n*f-i*c),t[14]=-(r*(s*_-u*m)-o*(n*_-i*m)+p*(n*u-i*s)),t[15]=r*(s*f-u*c)-o*(n*f-i*c)+h*(n*u-i*s),t},Er.determinant=function(t){var e=t[0],r=t[1],n=t[2],i=t[3],a=t[4],o=t[5],s=t[6],u=t[7],l=t[8],h=t[9],c=t[10],f=t[11],d=t[12],p=t[13],m=t[14],_=t[15];return(e*o-r*a)*(c*_-f*m)-(e*s-n*a)*(h*_-f*p)+(e*u-i*a)*(h*m-c*p)+(r*s-n*o)*(l*_-f*d)-(r*u-i*o)*(l*m-c*d)+(n*u-i*s)*(l*p-h*d)},Er.multiply=function(t,e,r){var n=e[0],i=e[1],a=e[2],o=e[3],s=e[4],u=e[5],l=e[6],h=e[7],c=e[8],f=e[9],d=e[10],p=e[11],m=e[12],_=e[13],g=e[14],v=e[15],y=r[0],T=r[1],x=r[2],E=r[3];return t[0]=y*n+T*s+x*c+E*m,t[1]=y*i+T*u+x*f+E*_,t[2]=y*a+T*l+x*d+E*g,t[3]=y*o+T*h+x*p+E*v,y=r[4],T=r[5],x=r[6],E=r[7],t[4]=y*n+T*s+x*c+E*m,t[5]=y*i+T*u+x*f+E*_,t[6]=y*a+T*l+x*d+E*g,t[7]=y*o+T*h+x*p+E*v,y=r[8],T=r[9],x=r[10],E=r[11],t[8]=y*n+T*s+x*c+E*m,t[9]=y*i+T*u+x*f+E*_,t[10]=y*a+T*l+x*d+E*g,t[11]=y*o+T*h+x*p+E*v,y=r[12],T=r[13],x=r[14],E=r[15],t[12]=y*n+T*s+x*c+E*m,t[13]=y*i+T*u+x*f+E*_,t[14]=y*a+T*l+x*d+E*g,t[15]=y*o+T*h+x*p+E*v,t},Er.multiplyAffine=function(t,e,r){var n=e[0],i=e[1],a=e[2],o=e[4],s=e[5],u=e[6],l=e[8],h=e[9],c=e[10],f=e[12],d=e[13],p=e[14],m=r[0],_=r[1],g=r[2];return t[0]=m*n+_*o+g*l,t[1]=m*i+_*s+g*h,t[2]=m*a+_*u+g*c,m=r[4],_=r[5],g=r[6],t[4]=m*n+_*o+g*l,t[5]=m*i+_*s+g*h,t[6]=m*a+_*u+g*c,m=r[8],_=r[9],g=r[10],t[8]=m*n+_*o+g*l,t[9]=m*i+_*s+g*h,t[10]=m*a+_*u+g*c,m=r[12],_=r[13],g=r[14],t[12]=m*n+_*o+g*l+f,t[13]=m*i+_*s+g*h+d,t[14]=m*a+_*u+g*c+p,t},Er.mul=Er.multiply,Er.mulAffine=Er.multiplyAffine,Er.translate=function(t,e,r){var n,i,a,o,s,u,l,h,c,f,d,p,m=r[0],_=r[1],g=r[2];return e===t?(t[12]=e[0]*m+e[4]*_+e[8]*g+e[12],t[13]=e[1]*m+e[5]*_+e[9]*g+e[13],t[14]=e[2]*m+e[6]*_+e[10]*g+e[14],t[15]=e[3]*m+e[7]*_+e[11]*g+e[15]):(n=e[0],i=e[1],a=e[2],o=e[3],s=e[4],u=e[5],l=e[6],h=e[7],c=e[8],f=e[9],d=e[10],p=e[11],t[0]=n,t[1]=i,t[2]=a,t[3]=o,t[4]=s,t[5]=u,t[6]=l,t[7]=h,t[8]=c,t[9]=f,t[10]=d,t[11]=p,t[12]=n*m+s*_+c*g+e[12],t[13]=i*m+u*_+f*g+e[13],t[14]=a*m+l*_+d*g+e[14],t[15]=o*m+h*_+p*g+e[15]),t},Er.scale=function(t,e,r){var n=r[0],i=r[1],a=r[2];return t[0]=e[0]*n,t[1]=e[1]*n,t[2]=e[2]*n,t[3]=e[3]*n,t[4]=e[4]*i,t[5]=e[5]*i,t[6]=e[6]*i,t[7]=e[7]*i,t[8]=e[8]*a,t[9]=e[9]*a,t[10]=e[10]*a,t[11]=e[11]*a,t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t},Er.rotate=function(t,e,r,n){var i,a,o,s,u,l,h,c,f,d,p,m,_,g,v,y,T,x,E,A,b,S,M,w,C=n[0],R=n[1],L=n[2],N=Math.sqrt(C*C+R*R+L*L);return Math.abs(N)<1e-6?null:(N=1/N,C*=N,R*=N,L*=N,i=Math.sin(r),a=Math.cos(r),o=1-a,s=e[0],u=e[1],l=e[2],h=e[3],c=e[4],f=e[5],d=e[6],p=e[7],m=e[8],_=e[9],g=e[10],v=e[11],y=C*C*o+a,T=R*C*o+L*i,x=L*C*o-R*i,E=C*R*o-L*i,A=R*R*o+a,b=L*R*o+C*i,S=C*L*o+R*i,M=R*L*o-C*i,w=L*L*o+a,t[0]=s*y+c*T+m*x,t[1]=u*y+f*T+_*x,t[2]=l*y+d*T+g*x,t[3]=h*y+p*T+v*x,t[4]=s*E+c*A+m*b,t[5]=u*E+f*A+_*b,t[6]=l*E+d*A+g*b,t[7]=h*E+p*A+v*b,t[8]=s*S+c*M+m*w,t[9]=u*S+f*M+_*w,t[10]=l*S+d*M+g*w,t[11]=h*S+p*M+v*w,e!==t&&(t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15]),t)},Er.rotateX=function(t,e,r){var n=Math.sin(r),i=Math.cos(r),a=e[4],o=e[5],s=e[6],u=e[7],l=e[8],h=e[9],c=e[10],f=e[11];return e!==t&&(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15]),t[4]=a*i+l*n,t[5]=o*i+h*n,t[6]=s*i+c*n,t[7]=u*i+f*n,t[8]=l*i-a*n,t[9]=h*i-o*n,t[10]=c*i-s*n,t[11]=f*i-u*n,t},Er.rotateY=function(t,e,r){var n=Math.sin(r),i=Math.cos(r),a=e[0],o=e[1],s=e[2],u=e[3],l=e[8],h=e[9],c=e[10],f=e[11];return e!==t&&(t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15]),t[0]=a*i-l*n,t[1]=o*i-h*n,t[2]=s*i-c*n,t[3]=u*i-f*n,t[8]=a*n+l*i,t[9]=o*n+h*i,t[10]=s*n+c*i,t[11]=u*n+f*i,t},Er.rotateZ=function(t,e,r){var n=Math.sin(r),i=Math.cos(r),a=e[0],o=e[1],s=e[2],u=e[3],l=e[4],h=e[5],c=e[6],f=e[7];return e!==t&&(t[8]=e[8],t[9]=e[9],t[10]=e[10],t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15]),t[0]=a*i+l*n,t[1]=o*i+h*n,t[2]=s*i+c*n,t[3]=u*i+f*n,t[4]=l*i-a*n,t[5]=h*i-o*n,t[6]=c*i-s*n,t[7]=f*i-u*n,t},Er.fromRotationTranslation=function(t,e,r){var n=e[0],i=e[1],a=e[2],o=e[3],s=n+n,u=i+i,l=a+a,h=n*s,c=n*u,f=n*l,d=i*u,p=i*l,m=a*l,_=o*s,g=o*u,v=o*l;return t[0]=1-(d+m),t[1]=c+v,t[2]=f-g,t[3]=0,t[4]=c-v,t[5]=1-(h+m),t[6]=p+_,t[7]=0,t[8]=f+g,t[9]=p-_,t[10]=1-(h+d),t[11]=0,t[12]=r[0],t[13]=r[1],t[14]=r[2],t[15]=1,t},Er.fromQuat=function(t,e){var r=e[0],n=e[1],i=e[2],a=e[3],o=r+r,s=n+n,u=i+i,l=r*o,h=n*o,c=n*s,f=i*o,d=i*s,p=i*u,m=a*o,_=a*s,g=a*u;return t[0]=1-c-p,t[1]=h+g,t[2]=f-_,t[3]=0,t[4]=h-g,t[5]=1-l-p,t[6]=d+m,t[7]=0,t[8]=f+_,t[9]=d-m,t[10]=1-l-c,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t},Er.frustum=function(t,e,r,n,i,a,o){var s=1/(r-e),u=1/(i-n),l=1/(a-o);return t[0]=2*a*s,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=2*a*u,t[6]=0,t[7]=0,t[8]=(r+e)*s,t[9]=(i+n)*u,t[10]=(o+a)*l,t[11]=-1,t[12]=0,t[13]=0,t[14]=o*a*2*l,t[15]=0,t},Er.perspective=function(t,e,r,n,i){var a=1/Math.tan(e/2),o=1/(n-i);return t[0]=a/r,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=a,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=(i+n)*o,t[11]=-1,t[12]=0,t[13]=0,t[14]=2*i*n*o,t[15]=0,t},Er.ortho=function(t,e,r,n,i,a,o){var s=1/(e-r),u=1/(n-i),l=1/(a-o);return t[0]=-2*s,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=-2*u,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=2*l,t[11]=0,t[12]=(e+r)*s,t[13]=(i+n)*u,t[14]=(o+a)*l,t[15]=1,t},Er.lookAt=function(t,e,r,n){var i,a,o,s,u,l,h,c,f,d,p=e[0],m=e[1],_=e[2],g=n[0],v=n[1],y=n[2],T=r[0],x=r[1],E=r[2];return Math.abs(p-T)<1e-6&&Math.abs(m-x)<1e-6&&Math.abs(_-E)<1e-6?Er.identity(t):(h=p-T,c=m-x,f=_-E,d=1/Math.sqrt(h*h+c*c+f*f),h*=d,c*=d,f*=d,i=v*f-y*c,a=y*h-g*f,o=g*c-v*h,d=Math.sqrt(i*i+a*a+o*o),d?(d=1/d,i*=d,a*=d,o*=d):(i=0,a=0,o=0),s=c*o-f*a,u=f*i-h*o,l=h*a-c*i,d=Math.sqrt(s*s+u*u+l*l),d?(d=1/d,s*=d,u*=d,l*=d):(s=0,u=0,l=0),t[0]=i,t[1]=s,t[2]=h,t[3]=0,t[4]=a,t[5]=u,t[6]=c,t[7]=0,t[8]=o,t[9]=l,t[10]=f,t[11]=0,t[12]=-(i*p+a*m+o*_),t[13]=-(s*p+u*m+l*_),t[14]=-(h*p+c*m+f*_),t[15]=1,t)},Er.frob=function(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2)+Math.pow(t[2],2)+Math.pow(t[3],2)+Math.pow(t[4],2)+Math.pow(t[5],2)+Math.pow(t[6],2)+Math.pow(t[7],2)+Math.pow(t[8],2)+Math.pow(t[9],2)+Math.pow(t[10],2)+Math.pow(t[11],2)+Math.pow(t[12],2)+Math.pow(t[13],2)+Math.pow(t[14],2)+Math.pow(t[15],2))},Q.import(xr);var Ar=Er.create,br={},Sr={float:Ye.FLOAT,byte:Ye.BYTE,ubyte:Ye.UNSIGNED_BYTE,short:Ye.SHORT,ushort:Ye.UNSIGNED_SHORT},Mr=ke.extend(function(){return{canvas:null,_width:100,_height:100,devicePixelRatio:"undefined"!=typeof window&&window.devicePixelRatio||1,clearColor:[0,0,0,0],clearBit:17664,alpha:!0,depth:!0,stencil:!1,antialias:!0,premultipliedAlpha:!0,preserveDrawingBuffer:!1,throwError:!0,gl:null,viewport:{},maxJointNumber:20,__currentFrameBuffer:null,_viewportStack:[],_clearStack:[],_sceneRendering:null}},function(){this.canvas||(this.canvas=Ve.createCanvas());var t=this.canvas;try{var e={alpha:this.alpha,depth:this.depth,stencil:this.stencil,antialias:this.antialias,premultipliedAlpha:this.premultipliedAlpha,preserveDrawingBuffer:this.preserveDrawingBuffer};if(this.gl=t.getContext("webgl",e)||t.getContext("experimental-webgl",e),!this.gl)throw new Error;this._glinfo=new S(this.gl),this.gl.targetRenderer&&console.error("Already created a renderer"),this.gl.targetRenderer=this,this.resize()}catch(t){throw"Error creating WebGL Context "+t}this._programMgr=new j(this),this._placeholderTexture=new at(this)},{resize:function(t,e){var r=this.canvas,n=this.devicePixelRatio;null!=t?(r.style&&(r.style.width=t+"px",r.style.height=e+"px"),r.width=t*n,r.height=e*n,this._width=t,this._height=e):(this._width=r.width/n,this._height=r.height/n),this.setViewport(0,0,this._width,this._height)},getWidth:function(){return this._width},getHeight:function(){return this._height},getViewportAspect:function(){var t=this.viewport;return t.width/t.height},setDevicePixelRatio:function(t){this.devicePixelRatio=t,this.resize(this._width,this._height)},getDevicePixelRatio:function(){return this.devicePixelRatio},getGLExtension:function(t){return this._glinfo.getExtension(t)},getGLParameter:function(t){return this._glinfo.getParameter(t)},setViewport:function(t,e,r,n,i){if("object"==typeof t){var a=t;t=a.x,e=a.y,r=a.width,n=a.height,i=a.devicePixelRatio}i=i||this.devicePixelRatio,this.gl.viewport(t*i,e*i,r*i,n*i),this.viewport={x:t,y:e,width:r,height:n,devicePixelRatio:i}},saveViewport:function(){this._viewportStack.push(this.viewport)},restoreViewport:function(){this._viewportStack.length>0&&this.setViewport(this._viewportStack.pop())},saveClear:function(){this._clearStack.push({clearBit:this.clearBit,clearColor:this.clearColor})},restoreClear:function(){if(this._clearStack.length>0){var t=this._clearStack.pop();this.clearColor=t.clearColor,this.clearBit=t.clearBit}},bindSceneRendering:function(t){this._sceneRendering=t},render:function(t,e,r,n){var i=this.gl,a=this.clearColor;if(this.clearBit){i.colorMask(!0,!0,!0,!0),i.depthMask(!0);var o=this.viewport,s=!1,u=o.devicePixelRatio;(o.width!==this._width||o.height!==this._height||u&&u!==this.devicePixelRatio||o.x||o.y)&&(s=!0,i.enable(i.SCISSOR_TEST),i.scissor(o.x*u,o.y*u,o.width*u,o.height*u)),i.clearColor(a[0],a[1],a[2],a[3]),i.clear(this.clearBit),s&&i.disable(i.SCISSOR_TEST)}if(r||t.update(!1),t.updateLights(),!(e=e||t.getMainCamera()))return void console.error("Can't find camera in the scene.");e.update();var l=t.updateRenderList(e,!0);this._sceneRendering=t;var h=l.opaque,c=l.transparent,f=t.material;t.trigger("beforerender",this,t,e,l),n?(this.renderPreZ(h,t,e),i.depthFunc(i.LEQUAL)):i.depthFunc(i.LESS);for(var d=Ar(),p=Ce.create(),m=0;m<c.length;m++){var _=c[m];Er.multiplyAffine(d,e.viewMatrix.array,_.worldTransform.array),Ce.transformMat4(p,_.position.array,d),_.__depth=p[2]}this.renderPass(h,e,{getMaterial:function(t){return f||t.material},sortCompare:this.opaqueSortCompare}),this.renderPass(c,e,{getMaterial:function(t){return f||t.material},sortCompare:this.transparentSortCompare}),t.trigger("afterrender",this,t,e,l),this._sceneRendering=null},getProgram:function(t,e,r){return e=e||t.material,this._programMgr.getProgram(t,e,r)},validateProgram:function(t){if(t.__error){var e=t.__error;if(br[t.__uid__])return;if(br[t.__uid__]=!0,this.throwError)throw new Error(e);this.trigger("error",e)}},updatePrograms:function(t,e,r){var n=r&&r.getMaterial||$;e=e||null;for(var i=0;i<t.length;i++){var a=t[i],o=n.call(this,a);if(i>0){var s=t[i-1],u=s.joints?s.joints.length:0;if((a.joints?a.joints.length:0)===u&&a.material===s.material&&a.lightGroup===s.lightGroup){a.__program=s.__program;continue}}var l=this._programMgr.getProgram(a,o,e);this.validateProgram(l),a.__program=l}},renderPass:function(t,e,r){this.trigger("beforerenderpass",this,t,e,r),r=r||{},r.getMaterial=r.getMaterial||$,r.getUniform=r.getUniform||tt,r.isMaterialChanged=r.isMaterialChanged||et,r.beforeRender=r.beforeRender||nt,r.afterRender=r.afterRender||nt;var n=r.ifRender||rt;this.updatePrograms(t,this._sceneRendering,r),r.sortCompare&&t.sort(r.sortCompare);var i=this.viewport,a=i.devicePixelRatio,o=[i.x*a,i.y*a,i.width*a,i.height*a],s=this.devicePixelRatio,u=this.__currentFrameBuffer?[this.__currentFrameBuffer.getTextureWidth(),this.__currentFrameBuffer.getTextureHeight()]:[this._width*s,this._height*s],l=[o[2],o[3]],h=Date.now();e?(Er.copy(wr.VIEW,e.viewMatrix.array),Er.copy(wr.PROJECTION,e.projectionMatrix.array),Er.copy(wr.VIEWINVERSE,e.worldTransform.array)):(Er.identity(wr.VIEW),Er.identity(wr.PROJECTION),Er.identity(wr.VIEWINVERSE)),Er.multiply(wr.VIEWPROJECTION,wr.PROJECTION,wr.VIEW),Er.invert(wr.PROJECTIONINVERSE,wr.PROJECTION),Er.invert(wr.VIEWPROJECTIONINVERSE,wr.VIEWPROJECTION);for(var c,f,d,p,m,_,g,v,y,T,x,E,A=this.gl,b=this._sceneRendering,S=null,M=0;M<t.length;M++){var w,C=t[M],R=null!=C.worldTransform;if(n(C)){R&&(w=C.isSkinnedMesh&&C.isSkinnedMesh()?C.offsetMatrix?C.offsetMatrix.array:wr.IDENTITY:C.worldTransform.array);var L=C.geometry,N=r.getMaterial.call(this,C),P=C.__program,I=N.shader,O=L.__uid__+"-"+P.__uid__,D=O!==T;T=O,D&&S&&S.bindVertexArrayOES(null),R&&(Er.copy(wr.WORLD,w),Er.multiply(wr.WORLDVIEWPROJECTION,wr.VIEWPROJECTION,w),Er.multiplyAffine(wr.WORLDVIEW,wr.VIEW,w),(I.matrixSemantics.WORLDINVERSE||I.matrixSemantics.WORLDINVERSETRANSPOSE)&&Er.invert(wr.WORLDINVERSE,w),(I.matrixSemantics.WORLDVIEWINVERSE||I.matrixSemantics.WORLDVIEWINVERSETRANSPOSE)&&Er.invert(wr.WORLDVIEWINVERSE,wr.WORLDVIEW),(I.matrixSemantics.WORLDVIEWPROJECTIONINVERSE||I.matrixSemantics.WORLDVIEWPROJECTIONINVERSETRANSPOSE)&&Er.invert(wr.WORLDVIEWPROJECTIONINVERSE,wr.WORLDVIEWPROJECTION)),C.beforeRender&&C.beforeRender(this),r.beforeRender.call(this,C,N,c);var B=P!==f;B?(P.bind(this),P.setUniformOfSemantic(A,"VIEWPORT",o),P.setUniformOfSemantic(A,"WINDOW_SIZE",u),e&&(P.setUniformOfSemantic(A,"NEAR",e.near),P.setUniformOfSemantic(A,"FAR",e.far)),P.setUniformOfSemantic(A,"DEVICEPIXELRATIO",a),P.setUniformOfSemantic(A,"TIME",h),P.setUniformOfSemantic(A,"VIEWPORT_SIZE",l),b&&b.setLightUniforms(P,C.lightGroup,this)):P=f,(B||r.isMaterialChanged(C,d,N,c))&&(N.depthTest!==p&&(N.depthTest?A.enable(A.DEPTH_TEST):A.disable(A.DEPTH_TEST),p=N.depthTest),N.depthMask!==m&&(A.depthMask(N.depthMask),m=N.depthMask),N.transparent!==y&&(N.transparent?A.enable(A.BLEND):A.disable(A.BLEND),y=N.transparent),N.transparent&&(N.blend?N.blend(A):(A.blendEquationSeparate(A.FUNC_ADD,A.FUNC_ADD),A.blendFuncSeparate(A.SRC_ALPHA,A.ONE_MINUS_SRC_ALPHA,A.ONE,A.ONE_MINUS_SRC_ALPHA))),E=this._bindMaterial(C,N,P,d||null,c||null,f||null,r.getUniform),c=N);var U=I.matrixSemanticKeys;if(R)for(var F=0;F<U.length;F++){var k=U[F],H=I.matrixSemantics[k],G=wr[k];if(H.isTranspose){var V=wr[H.semanticNoTranspose];Er.transpose(G,V)}P.setUniform(A,H.type,H.symbol,G)}C.cullFace!==g&&(g=C.cullFace,A.cullFace(g)),C.frontFace!==v&&(v=C.frontFace,A.frontFace(v)),C.culling!==_&&(_=C.culling,_?A.enable(A.CULL_FACE):A.disable(A.CULL_FACE)),this._updateSkeleton(C,P,E),D&&(x=this._bindVAO(S,I,L,P)),this._renderObject(C,x,P),r.afterRender(this,C),C.afterRender&&C.afterRender(this),f=P,d=C}}S&&S.bindVertexArrayOES(null),this.trigger("afterrenderpass",this,t,e,r)},getMaxJointNumber:function(){return this.maxJointNumber},_updateSkeleton:function(t,e,r){var n=this.gl,i=t.skeleton;if(i)if(i.update(),t.joints.length>this.getMaxJointNumber()){var a=i.getSubSkinMatricesTexture(t.__uid__,t.joints);e.useTextureSlot(this,a,r),e.setUniform(n,"1i","skinMatricesTexture",r),e.setUniform(n,"1f","skinMatricesTextureSize",a.width)}else{var o=i.getSubSkinMatrices(t.__uid__,t.joints);e.setUniformOfSemantic(n,"SKIN_MATRIX",o)}},_renderObject:function(t,e,r){var n=this.gl,i=t.geometry,a=t.mode;null==a&&(a=4);var o=null,s=t.isInstancedMesh&&t.isInstancedMesh();if(s&&!(o=this.getGLExtension("ANGLE_instanced_arrays")))return void console.warn("Device not support ANGLE_instanced_arrays extension");var u;if(s&&(u=this._bindInstancedAttributes(t,r,o)),e.indicesBuffer){var l=this.getGLExtension("OES_element_index_uint"),h=l&&i.indices instanceof Uint32Array,c=h?n.UNSIGNED_INT:n.UNSIGNED_SHORT;s?o.drawElementsInstancedANGLE(a,e.indicesBuffer.count,c,0,t.getInstanceCount()):n.drawElements(a,e.indicesBuffer.count,c,0)}else s?o.drawArraysInstancedANGLE(a,0,i.vertexCount,t.getInstanceCount()):n.drawArrays(a,0,i.vertexCount);if(s)for(var f=0;f<u.length;f++)n.disableVertexAttribArray(u[f])},_bindInstancedAttributes:function(t,e,r){for(var n=this.gl,i=t.getInstancedAttributesBuffers(this),a=[],o=0;o<i.length;o++){var s=i[o],u=e.getAttribLocation(n,s.symbol);if(!(u<0)){var l=Sr[s.type]||n.FLOAT;n.enableVertexAttribArray(u),n.bindBuffer(n.ARRAY_BUFFER,s.buffer),n.vertexAttribPointer(u,s.size,l,!1,0,0),r.vertexAttribDivisorANGLE(u,s.divisor),a.push(u)}}return a},_bindMaterial:function(t,e,r,n,i,a,o){for(var s=this.gl,u=a===r,l=r.currentTextureSlot(),h=e.getEnabledUniforms(),c=e.getTextureUniforms(),f=this._placeholderTexture,d=0;d<c.length;d++){var p=c[d],m=o(t,e,p),_=e.uniforms[p].type;if("t"===_&&m)m.__slot=-1;else if("tv"===_)for(var g=0;g<m.length;g++)m[g]&&(m[g].__slot=-1)}f.__slot=-1;for(var d=0;d<h.length;d++){var p=h[d],v=e.uniforms[p],m=o(t,e,p),_=v.type,y="t"===_;if(y&&(m&&m.isRenderable()||(m=f)),i&&u){var T=o(n,i,p);if(y&&(T&&T.isRenderable()||(T=f)),T===m){if(y)r.takeCurrentTextureSlot(this,null);else if("tv"===_&&m)for(var g=0;g<m.length;g++)r.takeCurrentTextureSlot(this,null);continue}}if(null!=m)if(y)if(m.__slot<0){var x=r.currentTextureSlot(),E=r.setUniform(s,"1i",p,x);E&&(r.takeCurrentTextureSlot(this,m),m.__slot=x)}else r.setUniform(s,"1i",p,m.__slot);else if(Array.isArray(m)){if(0===m.length)continue;if("tv"===_){if(!r.hasUniform(p))continue;for(var A=[],g=0;g<m.length;g++){var b=m[g];if(b.__slot<0){var x=r.currentTextureSlot();A.push(x),r.takeCurrentTextureSlot(this,b),b.__slot=x}else A.push(b.__slot)}r.setUniform(s,"1iv",p,A)}else r.setUniform(s,v.type,p,m)}else r.setUniform(s,v.type,p,m)}var S=r.currentTextureSlot();return r.resetTextureSlot(l),S},_bindVAO:function(t,e,r,n){var i=!r.dynamic,a=this.gl,o=this.__uid__+"-"+n.__uid__,s=r.__vaoCache[o];if(!s){var u=r.getBufferChunks(this);if(!u||!u.length)return;for(var l=u[0],h=l.attributeBuffers,c=l.indicesBuffer,f=[],d=[],p=0;p<h.length;p++){var m,_=h[p],g=_.name,v=_.semantic;if(v){var y=e.attributeSemantics[v];m=y&&y.symbol}else m=g;m&&n.attributes[m]&&(f.push(_),d.push(m))}s=new it(f,d,c),i&&(r.__vaoCache[o]=s)}var T=!0;t&&i&&(null==s.vao?s.vao=t.createVertexArrayOES():T=!1,t.bindVertexArrayOES(s.vao));var f=s.availableAttributes,c=s.indicesBuffer;if(T){for(var x=n.enableAttributes(this,s.availableAttributeSymbols,t&&i&&s),p=0;p<f.length;p++){var E=x[p];if(-1!==E){var _=f[p],A=_.buffer,b=_.size,S=Sr[_.type]||a.FLOAT;a.bindBuffer(a.ARRAY_BUFFER,A),a.vertexAttribPointer(E,b,S,!1,0,0)}}r.isUseIndices()&&a.bindBuffer(a.ELEMENT_ARRAY_BUFFER,c.buffer)}return s},renderPreZ:function(t,e,r){var n=this.gl,i=this._prezMaterial||new nr({shader:new Q(Q.source("clay.prez.vertex"),Q.source("clay.prez.fragment"))});this._prezMaterial=i,n.colorMask(!1,!1,!1,!1),n.depthMask(!0),this.renderPass(t,r,{ifRender:function(t){return!t.ignorePreZ},isMaterialChanged:function(t,e){var r=t.material,n=e.material;return r.get("diffuseMap")!==n.get("diffuseMap")||(r.get("alphaCutoff")||0)!==(n.get("alphaCutoff")||0)},getUniform:function(t,e,r){if("alphaMap"===r)return t.material.get("diffuseMap");if("alphaCutoff"===r){if(t.material.isDefined("fragment","ALPHA_TEST")&&t.material.get("diffuseMap")){return t.material.get("alphaCutoff")||0}return 0}return"uvRepeat"===r?t.material.get("uvRepeat"):"uvOffset"===r?t.material.get("uvOffset"):e.get(r)},getMaterial:function(){return i},sort:this.opaqueSortCompare}),n.colorMask(!0,!0,!0,!0),n.depthMask(!0)},disposeScene:function(t){this.disposeNode(t,!0,!0),t.dispose()},disposeNode:function(t,e,r){t.getParent()&&t.getParent().remove(t);var n={};t.traverse(function(t){var i=t.material;if(t.geometry&&e&&t.geometry.dispose(this),r&&i&&!n[i.__uid__]){for(var a=i.getTextureUniforms(),o=0;o<a.length;o++){var s=a[o],u=i.uniforms[s].value,l=i.uniforms[s].type;if(u)if("t"===l)u.dispose&&u.dispose(this);else if("tv"===l)for(var h=0;h<u.length;h++)u[h]&&u[h].dispose&&u[h].dispose(this)}n[i.__uid__]=!0}t.dispose&&t.dispose(this)},this)},disposeGeometry:function(t){t.dispose(this)},disposeTexture:function(t){t.dispose(this)},disposeFrameBuffer:function(t){t.dispose(this)},dispose:function(){},screenToNDC:function(t,e,r){r||(r=new Se),e=this._height-e;var n=this.viewport,i=r.array;return i[0]=(t-n.x)/n.width,i[0]=2*i[0]-1,i[1]=(e-n.y)/n.height,i[1]=2*i[1]-1,r}});Mr.opaqueSortCompare=Mr.prototype.opaqueSortCompare=function(t,e){return t.renderOrder===e.renderOrder?t.__program===e.__program?t.material===e.material?t.geometry.__uid__-e.geometry.__uid__:t.material.__uid__-e.material.__uid__:t.__program&&e.__program?t.__program.__uid__-e.__program.__uid__:0:t.renderOrder-e.renderOrder},Mr.transparentSortCompare=Mr.prototype.transparentSortCompare=function(t,e){return t.renderOrder===e.renderOrder?t.__depth===e.__depth?t.__program===e.__program?t.material===e.material?t.geometry.__uid__-e.geometry.__uid__:t.material.__uid__-e.material.__uid__:t.__program&&e.__program?t.__program.__uid__-e.__program.__uid__:0:t.__depth-e.__depth:t.renderOrder-e.renderOrder};var wr={IDENTITY:Ar(),WORLD:Ar(),VIEW:Ar(),PROJECTION:Ar(),WORLDVIEW:Ar(),VIEWPROJECTION:Ar(),WORLDVIEWPROJECTION:Ar(),WORLDINVERSE:Ar(),VIEWINVERSE:Ar(),PROJECTIONINVERSE:Ar(),WORLDVIEWINVERSE:Ar(),VIEWPROJECTIONINVERSE:Ar(),WORLDVIEWPROJECTIONINVERSE:Ar(),WORLDTRANSPOSE:Ar(),VIEWTRANSPOSE:Ar(),PROJECTIONTRANSPOSE:Ar(),WORLDVIEWTRANSPOSE:Ar(),VIEWPROJECTIONTRANSPOSE:Ar(),WORLDVIEWPROJECTIONTRANSPOSE:Ar(),WORLDINVERSETRANSPOSE:Ar(),VIEWINVERSETRANSPOSE:Ar(),PROJECTIONINVERSETRANSPOSE:Ar(),WORLDVIEWINVERSETRANSPOSE:Ar(),VIEWPROJECTIONINVERSETRANSPOSE:Ar(),WORLDVIEWPROJECTIONINVERSETRANSPOSE:Ar()};Mr.COLOR_BUFFER_BIT=Ye.COLOR_BUFFER_BIT,Mr.DEPTH_BUFFER_BIT=Ye.DEPTH_BUFFER_BIT,Mr.STENCIL_BUFFER_BIT=Ye.STENCIL_BUFFER_BIT;var Cr=function(t,e,r){t=t||0,e=e||0,r=r||0,this.array=Ce.fromValues(t,e,r),this._dirty=!0};Cr.prototype={constructor:Cr,add:function(t){return Ce.add(this.array,this.array,t.array),this._dirty=!0,this},set:function(t,e,r){return this.array[0]=t,this.array[1]=e,this.array[2]=r,this._dirty=!0,this},setArray:function(t){return this.array[0]=t[0],this.array[1]=t[1],this.array[2]=t[2],this._dirty=!0,this},clone:function(){return new Cr(this.x,this.y,this.z)},copy:function(t){return Ce.copy(this.array,t.array),this._dirty=!0,this},cross:function(t,e){return Ce.cross(this.array,t.array,e.array),this._dirty=!0,this},dist:function(t){return Ce.dist(this.array,t.array)},distance:function(t){return Ce.distance(this.array,t.array)},div:function(t){return Ce.div(this.array,this.array,t.array),this._dirty=!0,this},divide:function(t){return Ce.divide(this.array,this.array,t.array),this._dirty=!0,this},dot:function(t){return Ce.dot(this.array,t.array)},len:function(){return Ce.len(this.array)},length:function(){return Ce.length(this.array)},lerp:function(t,e,r){return Ce.lerp(this.array,t.array,e.array,r),this._dirty=!0,this},min:function(t){return Ce.min(this.array,this.array,t.array),this._dirty=!0,this},max:function(t){return Ce.max(this.array,this.array,t.array),this._dirty=!0,this},mul:function(t){return Ce.mul(this.array,this.array,t.array),this._dirty=!0,this},multiply:function(t){return Ce.multiply(this.array,this.array,t.array),this._dirty=!0,this},negate:function(){return Ce.negate(this.array,this.array),this._dirty=!0,this},normalize:function(){return Ce.normalize(this.array,this.array),this._dirty=!0,this},random:function(t){return Ce.random(this.array,t),this._dirty=!0,this},scale:function(t){return Ce.scale(this.array,this.array,t),this._dirty=!0,this},scaleAndAdd:function(t,e){return Ce.scaleAndAdd(this.array,this.array,t.array,e),this._dirty=!0,this},sqrDist:function(t){return Ce.sqrDist(this.array,t.array)},squaredDistance:function(t){return Ce.squaredDistance(this.array,t.array)},sqrLen:function(){return Ce.sqrLen(this.array)},squaredLength:function(){return Ce.squaredLength(this.array)},sub:function(t){return Ce.sub(this.array,this.array,t.array),this._dirty=!0,this},subtract:function(t){return Ce.subtract(this.array,this.array,t.array),this._dirty=!0,this},transformMat3:function(t){return Ce.transformMat3(this.array,this.array,t.array),this._dirty=!0,this},transformMat4:function(t){return Ce.transformMat4(this.array,this.array,t.array),this._dirty=!0,this},transformQuat:function(t){return Ce.transformQuat(this.array,this.array,t.array),this._dirty=!0,this},applyProjection:function(t){var e=this.array;if(t=t.array,0===t[15]){var r=-1/e[2];e[0]=t[0]*e[0]*r,e[1]=t[5]*e[1]*r,e[2]=(t[10]*e[2]+t[14])*r}else e[0]=t[0]*e[0]+t[12],e[1]=t[5]*e[1]+t[13],e[2]=t[10]*e[2]+t[14];return this._dirty=!0,this},eulerFromQuat:function(t,e){Cr.eulerFromQuat(this,t,e)},eulerFromMat3:function(t,e){Cr.eulerFromMat3(this,t,e)},toString:function(){return"["+Array.prototype.join.call(this.array,",")+"]"},toArray:function(){return Array.prototype.slice.call(this.array)}};var Rr=Object.defineProperty;if(Rr){var Lr=Cr.prototype;Rr(Lr,"x",{get:function(){return this.array[0]},set:function(t){this.array[0]=t,this._dirty=!0}}),Rr(Lr,"y",{get:function(){return this.array[1]},set:function(t){this.array[1]=t,this._dirty=!0}}),Rr(Lr,"z",{get:function(){return this.array[2]},set:function(t){this.array[2]=t,this._dirty=!0}})}Cr.add=function(t,e,r){return Ce.add(t.array,e.array,r.array),t._dirty=!0,t},Cr.set=function(t,e,r,n){Ce.set(t.array,e,r,n),t._dirty=!0},Cr.copy=function(t,e){return Ce.copy(t.array,e.array),t._dirty=!0,t},Cr.cross=function(t,e,r){return Ce.cross(t.array,e.array,r.array),t._dirty=!0,t},Cr.dist=function(t,e){return Ce.distance(t.array,e.array)},Cr.distance=Cr.dist,Cr.div=function(t,e,r){return Ce.divide(t.array,e.array,r.array),t._dirty=!0,t},Cr.divide=Cr.div,Cr.dot=function(t,e){return Ce.dot(t.array,e.array)},Cr.len=function(t){return Ce.length(t.array)},Cr.lerp=function(t,e,r,n){return Ce.lerp(t.array,e.array,r.array,n),t._dirty=!0,t},Cr.min=function(t,e,r){return Ce.min(t.array,e.array,r.array),t._dirty=!0,t},Cr.max=function(t,e,r){return Ce.max(t.array,e.array,r.array),t._dirty=!0,t},Cr.mul=function(t,e,r){return Ce.multiply(t.array,e.array,r.array),t._dirty=!0,t},Cr.multiply=Cr.mul,Cr.negate=function(t,e){return Ce.negate(t.array,e.array),t._dirty=!0,t},Cr.normalize=function(t,e){return Ce.normalize(t.array,e.array),t._dirty=!0,t},Cr.random=function(t,e){return Ce.random(t.array,e),t._dirty=!0,t},Cr.scale=function(t,e,r){return Ce.scale(t.array,e.array,r),t._dirty=!0,t},Cr.scaleAndAdd=function(t,e,r,n){return Ce.scaleAndAdd(t.array,e.array,r.array,n),t._dirty=!0,t},Cr.sqrDist=function(t,e){return Ce.sqrDist(t.array,e.array)},Cr.squaredDistance=Cr.sqrDist,Cr.sqrLen=function(t){return Ce.sqrLen(t.array)},Cr.squaredLength=Cr.sqrLen,Cr.sub=function(t,e,r){return Ce.subtract(t.array,e.array,r.array),t._dirty=!0,t},Cr.subtract=Cr.sub,Cr.transformMat3=function(t,e,r){return Ce.transformMat3(t.array,e.array,r.array),t._dirty=!0,t},Cr.transformMat4=function(t,e,r){return Ce.transformMat4(t.array,e.array,r.array),t._dirty=!0,t},Cr.transformQuat=function(t,e,r){return Ce.transformQuat(t.array,e.array,r.array),t._dirty=!0,t};var Nr=Math.atan2,Pr=Math.asin,Ir=Math.abs;Cr.eulerFromQuat=function(t,e,r){t._dirty=!0,e=e.array;var n=t.array,i=e[0],a=e[1],o=e[2],s=e[3],u=i*i,l=a*a,h=o*o,c=s*s,r=(r||"XYZ").toUpperCase();switch(r){case"XYZ":n[0]=Nr(2*(i*s-a*o),c-u-l+h),n[1]=Pr(ot(2*(i*o+a*s),-1,1)),n[2]=Nr(2*(o*s-i*a),c+u-l-h);break;case"YXZ":n[0]=Pr(ot(2*(i*s-a*o),-1,1)),n[1]=Nr(2*(i*o+a*s),c-u-l+h),n[2]=Nr(2*(i*a+o*s),c-u+l-h);break;case"ZXY":n[0]=Pr(ot(2*(i*s+a*o),-1,1)),n[1]=Nr(2*(a*s-o*i),c-u-l+h),n[2]=Nr(2*(o*s-i*a),c-u+l-h);break;case"ZYX":n[0]=Nr(2*(i*s+o*a),c-u-l+h),n[1]=Pr(ot(2*(a*s-i*o),-1,1)),n[2]=Nr(2*(i*a+o*s),c+u-l-h);break;case"YZX":n[0]=Nr(2*(i*s-o*a),c-u+l-h),n[1]=Nr(2*(a*s-i*o),c+u-l-h),n[2]=Pr(ot(2*(i*a+o*s),-1,1));break;case"XZY":n[0]=Nr(2*(i*s+a*o),c-u+l-h),n[1]=Nr(2*(i*o+a*s),c+u-l-h),n[2]=Pr(ot(2*(o*s-i*a),-1,1));break;default:console.warn("Unkown order: "+r)}return t},Cr.eulerFromMat3=function(t,e,r){var n=e.array,i=n[0],a=n[3],o=n[6],s=n[1],u=n[4],l=n[7],h=n[2],c=n[5],f=n[8],d=t.array,r=(r||"XYZ").toUpperCase();switch(r){case"XYZ":d[1]=Pr(ot(o,-1,1)),Ir(o)<.99999?(d[0]=Nr(-l,f),d[2]=Nr(-a,i)):(d[0]=Nr(c,u),d[2]=0);break;case"YXZ":d[0]=Pr(-ot(l,-1,1)),Ir(l)<.99999?(d[1]=Nr(o,f),
d[2]=Nr(s,u)):(d[1]=Nr(-h,i),d[2]=0);break;case"ZXY":d[0]=Pr(ot(c,-1,1)),Ir(c)<.99999?(d[1]=Nr(-h,f),d[2]=Nr(-a,u)):(d[1]=0,d[2]=Nr(s,i));break;case"ZYX":d[1]=Pr(-ot(h,-1,1)),Ir(h)<.99999?(d[0]=Nr(c,f),d[2]=Nr(s,i)):(d[0]=0,d[2]=Nr(-a,u));break;case"YZX":d[2]=Pr(ot(s,-1,1)),Ir(s)<.99999?(d[0]=Nr(-l,u),d[1]=Nr(-h,i)):(d[0]=0,d[1]=Nr(o,f));break;case"XZY":d[2]=Pr(-ot(a,-1,1)),Ir(a)<.99999?(d[0]=Nr(c,u),d[1]=Nr(o,i)):(d[0]=Nr(-l,f),d[1]=0);break;default:console.warn("Unkown order: "+r)}return t._dirty=!0,t},Object.defineProperties(Cr,{POSITIVE_X:{get:function(){return new Cr(1,0,0)}},NEGATIVE_X:{get:function(){return new Cr(-1,0,0)}},POSITIVE_Y:{get:function(){return new Cr(0,1,0)}},NEGATIVE_Y:{get:function(){return new Cr(0,-1,0)}},POSITIVE_Z:{get:function(){return new Cr(0,0,1)}},NEGATIVE_Z:{get:function(){return new Cr(0,0,-1)}},UP:{get:function(){return new Cr(0,1,0)}},ZERO:{get:function(){return new Cr}}});var Or=function(t,e,r,n){t=t||0,e=e||0,r=r||0,n=void 0===n?1:n,this.array=Ne.fromValues(t,e,r,n),this._dirty=!0};Or.prototype={constructor:Or,add:function(t){return Ne.add(this.array,this.array,t.array),this._dirty=!0,this},calculateW:function(){return Ne.calculateW(this.array,this.array),this._dirty=!0,this},set:function(t,e,r,n){return this.array[0]=t,this.array[1]=e,this.array[2]=r,this.array[3]=n,this._dirty=!0,this},setArray:function(t){return this.array[0]=t[0],this.array[1]=t[1],this.array[2]=t[2],this.array[3]=t[3],this._dirty=!0,this},clone:function(){return new Or(this.x,this.y,this.z,this.w)},conjugate:function(){return Ne.conjugate(this.array,this.array),this._dirty=!0,this},copy:function(t){return Ne.copy(this.array,t.array),this._dirty=!0,this},dot:function(t){return Ne.dot(this.array,t.array)},fromMat3:function(t){return Ne.fromMat3(this.array,t.array),this._dirty=!0,this},fromMat4:function(){var t=Le.create();return function(e){return Le.fromMat4(t,e.array),Le.transpose(t,t),Ne.fromMat3(this.array,t),this._dirty=!0,this}}(),identity:function(){return Ne.identity(this.array),this._dirty=!0,this},invert:function(){return Ne.invert(this.array,this.array),this._dirty=!0,this},len:function(){return Ne.len(this.array)},length:function(){return Ne.length(this.array)},lerp:function(t,e,r){return Ne.lerp(this.array,t.array,e.array,r),this._dirty=!0,this},mul:function(t){return Ne.mul(this.array,this.array,t.array),this._dirty=!0,this},mulLeft:function(t){return Ne.multiply(this.array,t.array,this.array),this._dirty=!0,this},multiply:function(t){return Ne.multiply(this.array,this.array,t.array),this._dirty=!0,this},multiplyLeft:function(t){return Ne.multiply(this.array,t.array,this.array),this._dirty=!0,this},normalize:function(){return Ne.normalize(this.array,this.array),this._dirty=!0,this},rotateX:function(t){return Ne.rotateX(this.array,this.array,t),this._dirty=!0,this},rotateY:function(t){return Ne.rotateY(this.array,this.array,t),this._dirty=!0,this},rotateZ:function(t){return Ne.rotateZ(this.array,this.array,t),this._dirty=!0,this},rotationTo:function(t,e){return Ne.rotationTo(this.array,t.array,e.array),this._dirty=!0,this},setAxes:function(t,e,r){return Ne.setAxes(this.array,t.array,e.array,r.array),this._dirty=!0,this},setAxisAngle:function(t,e){return Ne.setAxisAngle(this.array,t.array,e),this._dirty=!0,this},slerp:function(t,e,r){return Ne.slerp(this.array,t.array,e.array,r),this._dirty=!0,this},sqrLen:function(){return Ne.sqrLen(this.array)},squaredLength:function(){return Ne.squaredLength(this.array)},fromEuler:function(t,e){return Or.fromEuler(this,t,e)},toString:function(){return"["+Array.prototype.join.call(this.array,",")+"]"},toArray:function(){return Array.prototype.slice.call(this.array)}};var Dr=Object.defineProperty;if(Dr){var Br=Or.prototype;Dr(Br,"x",{get:function(){return this.array[0]},set:function(t){this.array[0]=t,this._dirty=!0}}),Dr(Br,"y",{get:function(){return this.array[1]},set:function(t){this.array[1]=t,this._dirty=!0}}),Dr(Br,"z",{get:function(){return this.array[2]},set:function(t){this.array[2]=t,this._dirty=!0}}),Dr(Br,"w",{get:function(){return this.array[3]},set:function(t){this.array[3]=t,this._dirty=!0}})}Or.add=function(t,e,r){return Ne.add(t.array,e.array,r.array),t._dirty=!0,t},Or.set=function(t,e,r,n,i){Ne.set(t.array,e,r,n,i),t._dirty=!0},Or.copy=function(t,e){return Ne.copy(t.array,e.array),t._dirty=!0,t},Or.calculateW=function(t,e){return Ne.calculateW(t.array,e.array),t._dirty=!0,t},Or.conjugate=function(t,e){return Ne.conjugate(t.array,e.array),t._dirty=!0,t},Or.identity=function(t){return Ne.identity(t.array),t._dirty=!0,t},Or.invert=function(t,e){return Ne.invert(t.array,e.array),t._dirty=!0,t},Or.dot=function(t,e){return Ne.dot(t.array,e.array)},Or.len=function(t){return Ne.length(t.array)},Or.lerp=function(t,e,r,n){return Ne.lerp(t.array,e.array,r.array,n),t._dirty=!0,t},Or.slerp=function(t,e,r,n){return Ne.slerp(t.array,e.array,r.array,n),t._dirty=!0,t},Or.mul=function(t,e,r){return Ne.multiply(t.array,e.array,r.array),t._dirty=!0,t},Or.multiply=Or.mul,Or.rotateX=function(t,e,r){return Ne.rotateX(t.array,e.array,r),t._dirty=!0,t},Or.rotateY=function(t,e,r){return Ne.rotateY(t.array,e.array,r),t._dirty=!0,t},Or.rotateZ=function(t,e,r){return Ne.rotateZ(t.array,e.array,r),t._dirty=!0,t},Or.setAxisAngle=function(t,e,r){return Ne.setAxisAngle(t.array,e.array,r),t._dirty=!0,t},Or.normalize=function(t,e){return Ne.normalize(t.array,e.array),t._dirty=!0,t},Or.sqrLen=function(t){return Ne.sqrLen(t.array)},Or.squaredLength=Or.sqrLen,Or.fromMat3=function(t,e){return Ne.fromMat3(t.array,e.array),t._dirty=!0,t},Or.setAxes=function(t,e,r,n){return Ne.setAxes(t.array,e.array,r.array,n.array),t._dirty=!0,t},Or.rotationTo=function(t,e,r){return Ne.rotationTo(t.array,e.array,r.array),t._dirty=!0,t},Or.fromEuler=function(t,e,r){t._dirty=!0,e=e.array;var n=t.array,i=Math.cos(e[0]/2),a=Math.cos(e[1]/2),o=Math.cos(e[2]/2),s=Math.sin(e[0]/2),u=Math.sin(e[1]/2),l=Math.sin(e[2]/2),r=(r||"XYZ").toUpperCase();switch(r){case"XYZ":n[0]=s*a*o+i*u*l,n[1]=i*u*o-s*a*l,n[2]=i*a*l+s*u*o,n[3]=i*a*o-s*u*l;break;case"YXZ":n[0]=s*a*o+i*u*l,n[1]=i*u*o-s*a*l,n[2]=i*a*l-s*u*o,n[3]=i*a*o+s*u*l;break;case"ZXY":n[0]=s*a*o-i*u*l,n[1]=i*u*o+s*a*l,n[2]=i*a*l+s*u*o,n[3]=i*a*o-s*u*l;break;case"ZYX":n[0]=s*a*o-i*u*l,n[1]=i*u*o+s*a*l,n[2]=i*a*l-s*u*o,n[3]=i*a*o+s*u*l;break;case"YZX":n[0]=s*a*o+i*u*l,n[1]=i*u*o+s*a*l,n[2]=i*a*l-s*u*o,n[3]=i*a*o-s*u*l;break;case"XZY":n[0]=s*a*o-i*u*l,n[1]=i*u*o-s*a*l,n[2]=i*a*l+s*u*o,n[3]=i*a*o+s*u*l}};var Ur=function(){this._axisX=new Cr,this._axisY=new Cr,this._axisZ=new Cr,this.array=Er.create(),this._dirty=!0};Ur.prototype={constructor:Ur,setArray:function(t){for(var e=0;e<this.array.length;e++)this.array[e]=t[e];return this._dirty=!0,this},adjoint:function(){return Er.adjoint(this.array,this.array),this._dirty=!0,this},clone:function(){return(new Ur).copy(this)},copy:function(t){return Er.copy(this.array,t.array),this._dirty=!0,this},determinant:function(){return Er.determinant(this.array)},fromQuat:function(t){return Er.fromQuat(this.array,t.array),this._dirty=!0,this},fromRotationTranslation:function(t,e){return Er.fromRotationTranslation(this.array,t.array,e.array),this._dirty=!0,this},fromMat2d:function(t){return Ur.fromMat2d(this,t),this},frustum:function(t,e,r,n,i,a){return Er.frustum(this.array,t,e,r,n,i,a),this._dirty=!0,this},identity:function(){return Er.identity(this.array),this._dirty=!0,this},invert:function(){return Er.invert(this.array,this.array),this._dirty=!0,this},lookAt:function(t,e,r){return Er.lookAt(this.array,t.array,e.array,r.array),this._dirty=!0,this},mul:function(t){return Er.mul(this.array,this.array,t.array),this._dirty=!0,this},mulLeft:function(t){return Er.mul(this.array,t.array,this.array),this._dirty=!0,this},multiply:function(t){return Er.multiply(this.array,this.array,t.array),this._dirty=!0,this},multiplyLeft:function(t){return Er.multiply(this.array,t.array,this.array),this._dirty=!0,this},ortho:function(t,e,r,n,i,a){return Er.ortho(this.array,t,e,r,n,i,a),this._dirty=!0,this},perspective:function(t,e,r,n){return Er.perspective(this.array,t,e,r,n),this._dirty=!0,this},rotate:function(t,e){return Er.rotate(this.array,this.array,t,e.array),this._dirty=!0,this},rotateX:function(t){return Er.rotateX(this.array,this.array,t),this._dirty=!0,this},rotateY:function(t){return Er.rotateY(this.array,this.array,t),this._dirty=!0,this},rotateZ:function(t){return Er.rotateZ(this.array,this.array,t),this._dirty=!0,this},scale:function(t){return Er.scale(this.array,this.array,t.array),this._dirty=!0,this},translate:function(t){return Er.translate(this.array,this.array,t.array),this._dirty=!0,this},transpose:function(){return Er.transpose(this.array,this.array),this._dirty=!0,this},decomposeMatrix:function(){var t=Ce.create(),e=Ce.create(),r=Ce.create(),n=Le.create();return function(i,a,o){var s=this.array;Ce.set(t,s[0],s[1],s[2]),Ce.set(e,s[4],s[5],s[6]),Ce.set(r,s[8],s[9],s[10]);var u=Ce.length(t),l=Ce.length(e),h=Ce.length(r);this.determinant()<0&&(u=-u),i&&i.set(u,l,h),o.set(s[12],s[13],s[14]),Le.fromMat4(n,s),n[0]/=u,n[1]/=u,n[2]/=u,n[3]/=l,n[4]/=l,n[5]/=l,n[6]/=h,n[7]/=h,n[8]/=h,Ne.fromMat3(a.array,n),Ne.normalize(a.array,a.array),a._dirty=!0,o._dirty=!0}}(),toString:function(){return"["+Array.prototype.join.call(this.array,",")+"]"},toArray:function(){return Array.prototype.slice.call(this.array)}};var Fr=Object.defineProperty;if(Fr){var kr=Ur.prototype;Fr(kr,"z",{get:function(){var t=this.array;return this._axisZ.set(t[8],t[9],t[10]),this._axisZ},set:function(t){var e=this.array;t=t.array,e[8]=t[0],e[9]=t[1],e[10]=t[2],this._dirty=!0}}),Fr(kr,"y",{get:function(){var t=this.array;return this._axisY.set(t[4],t[5],t[6]),this._axisY},set:function(t){var e=this.array;t=t.array,e[4]=t[0],e[5]=t[1],e[6]=t[2],this._dirty=!0}}),Fr(kr,"x",{get:function(){var t=this.array;return this._axisX.set(t[0],t[1],t[2]),this._axisX},set:function(t){var e=this.array;t=t.array,e[0]=t[0],e[1]=t[1],e[2]=t[2],this._dirty=!0}})}Ur.adjoint=function(t,e){return Er.adjoint(t.array,e.array),t._dirty=!0,t},Ur.copy=function(t,e){return Er.copy(t.array,e.array),t._dirty=!0,t},Ur.determinant=function(t){return Er.determinant(t.array)},Ur.identity=function(t){return Er.identity(t.array),t._dirty=!0,t},Ur.ortho=function(t,e,r,n,i,a,o){return Er.ortho(t.array,e,r,n,i,a,o),t._dirty=!0,t},Ur.perspective=function(t,e,r,n,i){return Er.perspective(t.array,e,r,n,i),t._dirty=!0,t},Ur.lookAt=function(t,e,r,n){return Er.lookAt(t.array,e.array,r.array,n.array),t._dirty=!0,t},Ur.invert=function(t,e){return Er.invert(t.array,e.array),t._dirty=!0,t},Ur.mul=function(t,e,r){return Er.mul(t.array,e.array,r.array),t._dirty=!0,t},Ur.multiply=Ur.mul,Ur.fromQuat=function(t,e){return Er.fromQuat(t.array,e.array),t._dirty=!0,t},Ur.fromRotationTranslation=function(t,e,r){return Er.fromRotationTranslation(t.array,e.array,r.array),t._dirty=!0,t},Ur.fromMat2d=function(t,e){t._dirty=!0;var e=e.array,t=t.array;return t[0]=e[0],t[4]=e[2],t[12]=e[4],t[1]=e[1],t[5]=e[3],t[13]=e[5],t},Ur.rotate=function(t,e,r,n){return Er.rotate(t.array,e.array,r,n.array),t._dirty=!0,t},Ur.rotateX=function(t,e,r){return Er.rotateX(t.array,e.array,r),t._dirty=!0,t},Ur.rotateY=function(t,e,r){return Er.rotateY(t.array,e.array,r),t._dirty=!0,t},Ur.rotateZ=function(t,e,r){return Er.rotateZ(t.array,e.array,r),t._dirty=!0,t},Ur.scale=function(t,e,r){return Er.scale(t.array,e.array,r.array),t._dirty=!0,t},Ur.transpose=function(t,e){return Er.transpose(t.array,e.array),t._dirty=!0,t},Ur.translate=function(t,e,r){return Er.translate(t.array,e.array,r.array),t._dirty=!0,t};var Hr=Ce.set,Gr=Ce.copy,Vr=function(t,e){this.min=t||new Cr(1/0,1/0,1/0),this.max=e||new Cr(-1/0,-1/0,-1/0),this.vertices=null};Vr.prototype={constructor:Vr,updateFromVertices:function(t){if(t.length>0){var e=this.min,r=this.max,n=e.array,i=r.array;Gr(n,t[0]),Gr(i,t[0]);for(var a=1;a<t.length;a++){var o=t[a];o[0]<n[0]&&(n[0]=o[0]),o[1]<n[1]&&(n[1]=o[1]),o[2]<n[2]&&(n[2]=o[2]),o[0]>i[0]&&(i[0]=o[0]),o[1]>i[1]&&(i[1]=o[1]),o[2]>i[2]&&(i[2]=o[2])}e._dirty=!0,r._dirty=!0}},union:function(t){var e=this.min,r=this.max;return Ce.min(e.array,e.array,t.min.array),Ce.max(r.array,r.array,t.max.array),e._dirty=!0,r._dirty=!0,this},intersection:function(t){var e=this.min,r=this.max;return Ce.max(e.array,e.array,t.min.array),Ce.min(r.array,r.array,t.max.array),e._dirty=!0,r._dirty=!0,this},intersectBoundingBox:function(t){var e=this.min.array,r=this.max.array,n=t.min.array,i=t.max.array;return!(e[0]>i[0]||e[1]>i[1]||e[2]>i[2]||r[0]<n[0]||r[1]<n[1]||r[2]<n[2])},containBoundingBox:function(t){var e=this.min.array,r=this.max.array,n=t.min.array,i=t.max.array;return e[0]<=n[0]&&e[1]<=n[1]&&e[2]<=n[2]&&r[0]>=i[0]&&r[1]>=i[1]&&r[2]>=i[2]},containPoint:function(t){var e=this.min.array,r=this.max.array,n=t.array;return e[0]<=n[0]&&e[1]<=n[1]&&e[2]<=n[2]&&r[0]>=n[0]&&r[1]>=n[1]&&r[2]>=n[2]},isFinite:function(){var t=this.min.array,e=this.max.array;return isFinite(t[0])&&isFinite(t[1])&&isFinite(t[2])&&isFinite(e[0])&&isFinite(e[1])&&isFinite(e[2])},applyTransform:function(t){this.transformFrom(this,t)},transformFrom:function(){var t=Ce.create(),e=Ce.create(),r=Ce.create(),n=Ce.create(),i=Ce.create(),a=Ce.create();return function(o,s){var u=o.min.array,l=o.max.array,h=s.array;return t[0]=h[0]*u[0],t[1]=h[1]*u[0],t[2]=h[2]*u[0],e[0]=h[0]*l[0],e[1]=h[1]*l[0],e[2]=h[2]*l[0],r[0]=h[4]*u[1],r[1]=h[5]*u[1],r[2]=h[6]*u[1],n[0]=h[4]*l[1],n[1]=h[5]*l[1],n[2]=h[6]*l[1],i[0]=h[8]*u[2],i[1]=h[9]*u[2],i[2]=h[10]*u[2],a[0]=h[8]*l[2],a[1]=h[9]*l[2],a[2]=h[10]*l[2],u=this.min.array,l=this.max.array,u[0]=Math.min(t[0],e[0])+Math.min(r[0],n[0])+Math.min(i[0],a[0])+h[12],u[1]=Math.min(t[1],e[1])+Math.min(r[1],n[1])+Math.min(i[1],a[1])+h[13],u[2]=Math.min(t[2],e[2])+Math.min(r[2],n[2])+Math.min(i[2],a[2])+h[14],l[0]=Math.max(t[0],e[0])+Math.max(r[0],n[0])+Math.max(i[0],a[0])+h[12],l[1]=Math.max(t[1],e[1])+Math.max(r[1],n[1])+Math.max(i[1],a[1])+h[13],l[2]=Math.max(t[2],e[2])+Math.max(r[2],n[2])+Math.max(i[2],a[2])+h[14],this.min._dirty=!0,this.max._dirty=!0,this}}(),applyProjection:function(t){var e=this.min.array,r=this.max.array,n=t.array,i=e[0],a=e[1],o=e[2],s=r[0],u=r[1],l=e[2],h=r[0],c=r[1],f=r[2];if(1===n[15])e[0]=n[0]*i+n[12],e[1]=n[5]*a+n[13],r[2]=n[10]*o+n[14],r[0]=n[0]*h+n[12],r[1]=n[5]*c+n[13],e[2]=n[10]*f+n[14];else{var d=-1/o;e[0]=n[0]*i*d,e[1]=n[5]*a*d,r[2]=(n[10]*o+n[14])*d,d=-1/l,r[0]=n[0]*s*d,r[1]=n[5]*u*d,d=-1/f,e[2]=(n[10]*f+n[14])*d}return this.min._dirty=!0,this.max._dirty=!0,this},updateVertices:function(){var t=this.vertices;if(!t){t=[];for(var e=0;e<8;e++)t[e]=Ce.fromValues(0,0,0);this.vertices=t}var r=this.min.array,n=this.max.array;return Hr(t[0],r[0],r[1],r[2]),Hr(t[1],r[0],n[1],r[2]),Hr(t[2],n[0],r[1],r[2]),Hr(t[3],n[0],n[1],r[2]),Hr(t[4],r[0],r[1],n[2]),Hr(t[5],r[0],n[1],n[2]),Hr(t[6],n[0],r[1],n[2]),Hr(t[7],n[0],n[1],n[2]),this},copy:function(t){var e=this.min,r=this.max;return Gr(e.array,t.min.array),Gr(r.array,t.max.array),e._dirty=!0,r._dirty=!0,this},clone:function(){var t=new Vr;return t.copy(this),t}};var Wr=0,zr=ke.extend({name:"",position:null,rotation:null,scale:null,worldTransform:null,localTransform:null,autoUpdateLocalTransform:!0,_parent:null,_scene:null,_needsUpdateWorldTransform:!0,_inIterating:!1,__depth:0},function(){this.name||(this.name=(this.type||"NODE")+"_"+Wr++),this.position||(this.position=new Cr),this.rotation||(this.rotation=new Or),this.scale||(this.scale=new Cr(1,1,1)),this.worldTransform=new Ur,this.localTransform=new Ur,this._children=[]},{target:null,invisible:!1,isSkinnedMesh:function(){return!1},isRenderable:function(){return!1},setName:function(t){var e=this._scene;if(e){var r=e._nodeRepository;delete r[this.name],r[t]=this}this.name=t},add:function(t){var e=t._parent;if(e!==this){e&&e.remove(t),t._parent=this,this._children.push(t);var r=this._scene;r&&r!==t.scene&&t.traverse(this._addSelfToScene,this),t._needsUpdateWorldTransform=!0}},remove:function(t){var e=this._children,r=e.indexOf(t);r<0||(e.splice(r,1),t._parent=null,this._scene&&t.traverse(this._removeSelfFromScene,this))},removeAll:function(){for(var t=this._children,e=0;e<t.length;e++)t[e]._parent=null,this._scene&&t[e].traverse(this._removeSelfFromScene,this);this._children=[]},getScene:function(){return this._scene},getParent:function(){return this._parent},_removeSelfFromScene:function(t){t._scene.removeFromScene(t),t._scene=null},_addSelfToScene:function(t){this._scene.addToScene(t),t._scene=this._scene},isAncestor:function(t){for(var e=t._parent;e;){if(e===this)return!0;e=e._parent}return!1},children:function(){return this._children.slice()},childAt:function(t){return this._children[t]},getChildByName:function(t){for(var e=this._children,r=0;r<e.length;r++)if(e[r].name===t)return e[r]},getDescendantByName:function(t){for(var e=this._children,r=0;r<e.length;r++){var n=e[r];if(n.name===t)return n;var i=n.getDescendantByName(t);if(i)return i}},queryNode:function(t){if(t){for(var e=t.split("/"),r=this,n=0;n<e.length;n++){var i=e[n];if(i){for(var a=!1,o=r._children,s=0;s<o.length;s++){var u=o[s];if(u.name===i){r=u,a=!0;break}}if(!a)return}}return r}},getPath:function(t){if(!this._parent)return"/";for(var e=this._parent,r=this.name;e._parent&&(r=e.name+"/"+r,e._parent!=t);)e=e._parent;return!e._parent&&t?null:r},traverse:function(t,e){t.call(e,this);for(var r=this._children,n=0,i=r.length;n<i;n++)r[n].traverse(t,e)},eachChild:function(t,e){for(var r=this._children,n=0,i=r.length;n<i;n++){var a=r[n];t.call(e,a,n)}},setLocalTransform:function(t){Er.copy(this.localTransform.array,t.array),this.decomposeLocalTransform()},decomposeLocalTransform:function(t){var e=t?null:this.scale;this.localTransform.decomposeMatrix(e,this.rotation,this.position)},setWorldTransform:function(t){Er.copy(this.worldTransform.array,t.array),this.decomposeWorldTransform()},decomposeWorldTransform:function(){var t=Er.create();return function(e){var r=this.localTransform,n=this.worldTransform;this._parent?(Er.invert(t,this._parent.worldTransform.array),Er.multiply(r.array,t,n.array)):Er.copy(r.array,n.array);var i=e?null:this.scale;r.decomposeMatrix(i,this.rotation,this.position)}}(),transformNeedsUpdate:function(){return this.position._dirty||this.rotation._dirty||this.scale._dirty},updateLocalTransform:function(){var t=this.position,e=this.rotation,r=this.scale;if(this.transformNeedsUpdate()){var n=this.localTransform.array;Er.fromRotationTranslation(n,e.array,t.array),Er.scale(n,n,r.array),e._dirty=!1,r._dirty=!1,t._dirty=!1,this._needsUpdateWorldTransform=!0}},_updateWorldTransformTopDown:function(){var t=this.localTransform.array,e=this.worldTransform.array;this._parent?Er.multiplyAffine(e,this._parent.worldTransform.array,t):Er.copy(e,t)},updateWorldTransform:function(){for(var t=this;t&&t.getParent()&&t.getParent().transformNeedsUpdate();)t=t.getParent();t.update()},update:function(t){this.autoUpdateLocalTransform?this.updateLocalTransform():t=!0,(t||this._needsUpdateWorldTransform)&&(this._updateWorldTransformTopDown(),t=!0,this._needsUpdateWorldTransform=!1);for(var e=this._children,r=0,n=e.length;r<n;r++)e[r].update(t)},getBoundingBox:function(){function t(t){return!t.invisible&&t.geometry}var e=new Vr,r=new Ur,n=new Ur;return function(i,a){return a=a||new Vr,i=i||t,this._parent?Ur.invert(n,this._parent.worldTransform):Ur.identity(n),this.traverse(function(t){t.geometry&&t.geometry.boundingBox&&(e.copy(t.geometry.boundingBox),Ur.multiply(r,n,t.worldTransform),e.applyTransform(r),a.union(e))},this,t),a}}(),getWorldPosition:function(t){this.transformNeedsUpdate()&&this.updateWorldTransform();var e=this.worldTransform.array;if(t){var r=t.array;return r[0]=e[12],r[1]=e[13],r[2]=e[14],t}return new Cr(e[12],e[13],e[14])},clone:function(){var t=new this.constructor,e=this._children;t.setName(this.name),t.position.copy(this.position),t.rotation.copy(this.rotation),t.scale.copy(this.scale);for(var r=0;r<e.length;r++)t.add(e[r].clone());return t},rotateAround:function(){var t=new Cr,e=new Ur;return function(r,n,i){t.copy(this.position).subtract(r);var a=this.localTransform;a.identity(),a.translate(r),a.rotate(i,n),e.fromRotationTranslation(this.rotation,t),a.multiply(e),a.scale(this.scale),this.decomposeLocalTransform(),this._needsUpdateWorldTransform=!0}}(),lookAt:function(){var t=new Ur;return function(e,r){t.lookAt(this.position,e,r||this.localTransform.y).invert(),this.setLocalTransform(t),this.target=e}}()}),Xr=":unconfigurable;",jr=["@export clay.header.directional_light","uniform vec3 directionalLightDirection[DIRECTIONAL_LIGHT_COUNT]"+Xr,"uniform vec3 directionalLightColor[DIRECTIONAL_LIGHT_COUNT]"+Xr,"@end","@export clay.header.ambient_light","uniform vec3 ambientLightColor[AMBIENT_LIGHT_COUNT]"+Xr,"@end","@export clay.header.ambient_sh_light","uniform vec3 ambientSHLightColor[AMBIENT_SH_LIGHT_COUNT]"+Xr,"uniform vec3 ambientSHLightCoefficients[AMBIENT_SH_LIGHT_COUNT * 9]"+Xr,"vec3 calcAmbientSHLight(int idx, vec3 N) {\n int offset = 9 * idx;\n return ambientSHLightCoefficients[0]\n + ambientSHLightCoefficients[1] * N.x\n + ambientSHLightCoefficients[2] * N.y\n + ambientSHLightCoefficients[3] * N.z\n + ambientSHLightCoefficients[4] * N.x * N.z\n + ambientSHLightCoefficients[5] * N.z * N.y\n + ambientSHLightCoefficients[6] * N.y * N.x\n + ambientSHLightCoefficients[7] * (3.0 * N.z * N.z - 1.0)\n + ambientSHLightCoefficients[8] * (N.x * N.x - N.y * N.y);\n}","@end","@export clay.header.ambient_cubemap_light","uniform vec3 ambientCubemapLightColor[AMBIENT_CUBEMAP_LIGHT_COUNT]"+Xr,"uniform samplerCube ambientCubemapLightCubemap[AMBIENT_CUBEMAP_LIGHT_COUNT]"+Xr,"uniform sampler2D ambientCubemapLightBRDFLookup[AMBIENT_CUBEMAP_LIGHT_COUNT]"+Xr,"@end","@export clay.header.point_light","uniform vec3 pointLightPosition[POINT_LIGHT_COUNT]"+Xr,"uniform float pointLightRange[POINT_LIGHT_COUNT]"+Xr,"uniform vec3 pointLightColor[POINT_LIGHT_COUNT]"+Xr,"@end","@export clay.header.spot_light","uniform vec3 spotLightPosition[SPOT_LIGHT_COUNT]"+Xr,"uniform vec3 spotLightDirection[SPOT_LIGHT_COUNT]"+Xr,"uniform float spotLightRange[SPOT_LIGHT_COUNT]"+Xr,"uniform float spotLightUmbraAngleCosine[SPOT_LIGHT_COUNT]"+Xr,"uniform float spotLightPenumbraAngleCosine[SPOT_LIGHT_COUNT]"+Xr,"uniform float spotLightFalloffFactor[SPOT_LIGHT_COUNT]"+Xr,"uniform vec3 spotLightColor[SPOT_LIGHT_COUNT]"+Xr,"@end"].join("\n");Q.import(jr);var qr=zr.extend(function(){return{color:[1,1,1],intensity:1,castShadow:!0,shadowResolution:512,group:0}},{type:"",clone:function(){var t=zr.prototype.clone.call(this);return t.color=Array.prototype.slice.call(this.color),t.intensity=this.intensity,t.castShadow=this.castShadow,t.shadowResolution=this.shadowResolution,t}}),Yr=function(t,e){this.normal=t||new Cr(0,1,0),this.distance=e||0};Yr.prototype={constructor:Yr,distanceToPoint:function(t){return Ce.dot(t.array,this.normal.array)-this.distance},projectPoint:function(t,e){e||(e=new Cr);var r=this.distanceToPoint(t);return Ce.scaleAndAdd(e.array,t.array,this.normal.array,-r),e._dirty=!0,e},normalize:function(){var t=1/Ce.len(this.normal.array);Ce.scale(this.normal.array,t),this.distance*=t},intersectFrustum:function(t){for(var e=t.vertices,r=this.normal.array,n=Ce.dot(e[0].array,r)>this.distance,i=1;i<8;i++)if(Ce.dot(e[i].array,r)>this.distance!=n)return!0},intersectLine:function(){var t=Ce.create();return function(e,r,n){var i=this.distanceToPoint(e),a=this.distanceToPoint(r);if(i>0&&a>0||i<0&&a<0)return null;var o=this.normal.array,s=this.distance,u=e.array;Ce.sub(t,r.array,e.array),Ce.normalize(t,t);var l=Ce.dot(o,t);if(0===l)return null;n||(n=new Cr);var h=(Ce.dot(o,u)-s)/l;return Ce.scaleAndAdd(n.array,u,t,-h),n._dirty=!0,n}}(),applyTransform:function(){var t=Er.create(),e=Re.create(),r=Re.create();return r[3]=1,function(n){n=n.array,Ce.scale(r,this.normal.array,this.distance),Re.transformMat4(r,r,n),this.distance=Ce.dot(r,this.normal.array),Er.invert(t,n),Er.transpose(t,t),e[3]=0,Ce.copy(e,this.normal.array),Re.transformMat4(e,e,t),Ce.copy(this.normal.array,e)}}(),copy:function(t){Ce.copy(this.normal.array,t.normal.array),this.normal._dirty=!0,this.distance=t.distance},clone:function(){var t=new Yr;return t.copy(this),t}};var Kr=Ce.set,Zr=Ce.copy,Jr=Ce.transformMat4,Qr=Math.min,$r=Math.max,tn=function(){this.planes=[];for(var t=0;t<6;t++)this.planes.push(new Yr);this.boundingBox=new Vr,this.vertices=[];for(var t=0;t<8;t++)this.vertices[t]=Ce.fromValues(0,0,0)};tn.prototype={setFromProjection:function(t){var e=this.planes,r=t.array,n=r[0],i=r[1],a=r[2],o=r[3],s=r[4],u=r[5],l=r[6],h=r[7],c=r[8],f=r[9],d=r[10],p=r[11],m=r[12],_=r[13],g=r[14],v=r[15];Kr(e[0].normal.array,o-n,h-s,p-c),e[0].distance=-(v-m),e[0].normalize(),Kr(e[1].normal.array,o+n,h+s,p+c),e[1].distance=-(v+m),e[1].normalize(),Kr(e[2].normal.array,o+i,h+u,p+f),e[2].distance=-(v+_),e[2].normalize(),Kr(e[3].normal.array,o-i,h-u,p-f),e[3].distance=-(v-_),e[3].normalize(),Kr(e[4].normal.array,o-a,h-l,p-d),e[4].distance=-(v-g),e[4].normalize(),Kr(e[5].normal.array,o+a,h+l,p+d),e[5].distance=-(v+g),e[5].normalize();var y=this.boundingBox,T=this.vertices;if(0===v){var x=u/n,E=-g/(d-1),A=-g/(d+1),b=-A/u,S=-E/u;y.min.set(-b*x,-b,A),y.max.set(b*x,b,E),Kr(T[0],-b*x,-b,A),Kr(T[1],-b*x,b,A),Kr(T[2],b*x,-b,A),Kr(T[3],b*x,b,A),Kr(T[4],-S*x,-S,E),Kr(T[5],-S*x,S,E),Kr(T[6],S*x,-S,E),Kr(T[7],S*x,S,E)}else{var M=(-1-m)/n,w=(1-m)/n,C=(1-_)/u,R=(-1-_)/u,L=(-1-g)/d,N=(1-g)/d;y.min.set(Math.min(M,w),Math.min(R,C),Math.min(N,L)),y.max.set(Math.max(w,M),Math.max(C,R),Math.max(L,N));var P=y.min.array,I=y.max.array;Kr(T[0],P[0],P[1],P[2]),Kr(T[1],P[0],I[1],P[2]),Kr(T[2],I[0],P[1],P[2]),Kr(T[3],I[0],I[1],P[2]),Kr(T[4],P[0],P[1],I[2]),Kr(T[5],P[0],I[1],I[2]),Kr(T[6],I[0],P[1],I[2]),Kr(T[7],I[0],I[1],I[2])}},getTransformedBoundingBox:function(){var t=Ce.create();return function(e,r){var n=this.vertices,i=r.array,a=e.min,o=e.max,s=a.array,u=o.array,l=n[0];Jr(t,l,i),Zr(s,t),Zr(u,t);for(var h=1;h<8;h++)l=n[h],Jr(t,l,i),s[0]=Qr(t[0],s[0]),s[1]=Qr(t[1],s[1]),s[2]=Qr(t[2],s[2]),u[0]=$r(t[0],u[0]),u[1]=$r(t[1],u[1]),u[2]=$r(t[2],u[2]);return a._dirty=!0,o._dirty=!0,e}}()};var en=function(t,e){this.origin=t||new Cr,this.direction=e||new Cr};en.prototype={constructor:en,intersectPlane:function(t,e){var r=t.normal.array,n=t.distance,i=this.origin.array,a=this.direction.array,o=Ce.dot(r,a);if(0===o)return null;e||(e=new Cr);var s=(Ce.dot(r,i)-n)/o;return Ce.scaleAndAdd(e.array,i,a,-s),e._dirty=!0,e},mirrorAgainstPlane:function(t){var e=Ce.dot(t.normal.array,this.direction.array);Ce.scaleAndAdd(this.direction.array,this.direction.array,t.normal.array,2*-e),this.direction._dirty=!0},distanceToPoint:function(){var t=Ce.create();return function(e){Ce.sub(t,e,this.origin.array);var r=Ce.dot(t,this.direction.array);if(r<0)return Ce.distance(this.origin.array,e);var n=Ce.lenSquared(t);return Math.sqrt(n-r*r)}}(),intersectSphere:function(){var t=Ce.create();return function(e,r,n){var i=this.origin.array,a=this.direction.array;e=e.array,Ce.sub(t,e,i);var o=Ce.dot(t,a),s=Ce.squaredLength(t),u=s-o*o,l=r*r;if(!(u>l)){var h=Math.sqrt(l-u),c=o-h,f=o+h;return n||(n=new Cr),c<0?f<0?null:(Ce.scaleAndAdd(n.array,i,a,f),n):(Ce.scaleAndAdd(n.array,i,a,c),n)}}}(),intersectBoundingBox:function(t,e){var r,n,i,a,o,s,u=this.direction.array,l=this.origin.array,h=t.min.array,c=t.max.array,f=1/u[0],d=1/u[1],p=1/u[2];if(f>=0?(r=(h[0]-l[0])*f,n=(c[0]-l[0])*f):(n=(h[0]-l[0])*f,r=(c[0]-l[0])*f),d>=0?(i=(h[1]-l[1])*d,a=(c[1]-l[1])*d):(a=(h[1]-l[1])*d,i=(c[1]-l[1])*d),r>a||i>n)return null;if((i>r||r!==r)&&(r=i),(a<n||n!==n)&&(n=a),p>=0?(o=(h[2]-l[2])*p,s=(c[2]-l[2])*p):(s=(h[2]-l[2])*p,o=(c[2]-l[2])*p),r>s||o>n)return null;if((o>r||r!==r)&&(r=o),(s<n||n!==n)&&(n=s),n<0)return null;var m=r>=0?r:n;return e||(e=new Cr),Ce.scaleAndAdd(e.array,l,u,m),e},intersectTriangle:function(){var t=Ce.create(),e=Ce.create(),r=Ce.create(),n=Ce.create();return function(i,a,o,s,u,l){var h=this.direction.array,c=this.origin.array;i=i.array,a=a.array,o=o.array,Ce.sub(t,a,i),Ce.sub(e,o,i),Ce.cross(n,e,h);var f=Ce.dot(t,n);if(s){if(f>-1e-5)return null}else if(f>-1e-5&&f<1e-5)return null;Ce.sub(r,c,i);var d=Ce.dot(n,r)/f;if(d<0||d>1)return null;Ce.cross(n,t,r);var p=Ce.dot(h,n)/f;if(p<0||p>1||d+p>1)return null;Ce.cross(n,t,e);var m=-Ce.dot(r,n)/f;return m<0?null:(u||(u=new Cr),l&&Cr.set(l,1-d-p,d,p),Ce.scaleAndAdd(u.array,c,h,m),u)}}(),applyTransform:function(t){Cr.add(this.direction,this.direction,this.origin),Cr.transformMat4(this.origin,this.origin,t),Cr.transformMat4(this.direction,this.direction,t),Cr.sub(this.direction,this.direction,this.origin),Cr.normalize(this.direction,this.direction)},copy:function(t){Cr.copy(this.origin,t.origin),Cr.copy(this.direction,t.direction)},clone:function(){var t=new en;return t.copy(this),t}};var rn=zr.extend(function(){return{projectionMatrix:new Ur,invProjectionMatrix:new Ur,viewMatrix:new Ur,frustum:new tn}},function(){this.update(!0)},{update:function(t){zr.prototype.update.call(this,t),Ur.invert(this.viewMatrix,this.worldTransform),this.updateProjectionMatrix(),Ur.invert(this.invProjectionMatrix,this.projectionMatrix),this.frustum.setFromProjection(this.projectionMatrix)},setViewMatrix:function(t){Ur.copy(this.viewMatrix,t),Ur.invert(this.worldTransform,t),this.decomposeWorldTransform()},decomposeProjectionMatrix:function(){},setProjectionMatrix:function(t){Ur.copy(this.projectionMatrix,t),Ur.invert(this.invProjectionMatrix,t),this.decomposeProjectionMatrix()},updateProjectionMatrix:function(){},castRay:function(){var t=Re.create();return function(e,r){var n=void 0!==r?r:new en,i=e.array[0],a=e.array[1];return Re.set(t,i,a,-1,1),Re.transformMat4(t,t,this.invProjectionMatrix.array),Re.transformMat4(t,t,this.worldTransform.array),Ce.scale(n.origin.array,t,1/t[3]),Re.set(t,i,a,1,1),Re.transformMat4(t,t,this.invProjectionMatrix.array),Re.transformMat4(t,t,this.worldTransform.array),Ce.scale(t,t,1/t[3]),Ce.sub(n.direction.array,t,n.origin.array),Ce.normalize(n.direction.array,n.direction.array),n.direction._dirty=!0,n.origin._dirty=!0,n}}()}),nn=Er.create(),an=Er.create(),on={};ut.prototype.startCount=function(){this._opaqueCount=0,this._transparentCount=0},ut.prototype.add=function(t,e){e?this.transparent[this._transparentCount++]=t:this.opaque[this._opaqueCount++]=t},ut.prototype.endCount=function(){this.transparent.length=this._transparentCount,this.opaque.length=this._opaqueCount};var sn=zr.extend(function(){return{material:null,lights:[],viewBoundingBoxLastFrame:new Vr,shadowUniforms:{},_cameraList:[],_lightUniforms:{},_previousLightNumber:{},_lightNumber:{},_lightProgramKeys:{},_nodeRepository:{},_renderLists:new Ze(20)}},function(){this._scene=this},{addToScene:function(t){t instanceof rn?(this._cameraList.length>0&&console.warn("Found multiple camera in one scene. Use the fist one."),this._cameraList.push(t)):t instanceof qr&&this.lights.push(t),t.name&&(this._nodeRepository[t.name]=t)},removeFromScene:function(t){var e;t instanceof rn?(e=this._cameraList.indexOf(t))>=0&&this._cameraList.splice(e,1):t instanceof qr&&(e=this.lights.indexOf(t))>=0&&this.lights.splice(e,1),t.name&&delete this._nodeRepository[t.name]},getNode:function(t){return this._nodeRepository[t]},setMainCamera:function(t){var e=this._cameraList.indexOf(t);e>=0&&this._cameraList.splice(e,1),this._cameraList.unshift(t)},getMainCamera:function(){return this._cameraList[0]},getLights:function(){return this.lights},updateLights:function(){var t=this.lights;this._previousLightNumber=this._lightNumber;for(var e={},r=0;r<t.length;r++){var n=t[r];if(!n.invisible){var i=n.group;e[i]||(e[i]={}),e[i][n.type]=e[i][n.type]||0,e[i][n.type]++}}this._lightNumber=e;for(var a in e)this._lightProgramKeys[a]=st(e[a]);this._updateLightUniforms()},cloneNode:function(t){function e(t,r){n[t.__uid__]=r;for(var i=0;i<t._children.length;i++){e(t._children[i],r._children[i])}}var r=t.clone(),n={};return e(t,r),r.traverse(function(t){t.skeleton&&(t.skeleton=t.skeleton.clone(n)),t.material&&(t.material=t.material.clone())}),r},updateRenderList:function(t,e){
var r=t.__uid__,n=this._renderLists.get(r);n||(n=new ut,this._renderLists.put(r,n)),n.startCount(),e&&(this.viewBoundingBoxLastFrame.min.set(1/0,1/0,1/0),this.viewBoundingBoxLastFrame.max.set(-1/0,-1/0,-1/0));var i=this.material&&this.material.transparent||!1;return this._doUpdateRenderList(this,t,i,n,e),n.endCount(),n},getRenderList:function(t){return this._renderLists.get(t.__uid__)},_doUpdateRenderList:function(t,e,r,n,i){if(!t.invisible)for(var a=0;a<t._children.length;a++){var o=t._children[a];if(o.isRenderable()){var s=o.isSkinnedMesh()?nn:o.worldTransform.array,u=o.geometry;Er.multiplyAffine(an,e.viewMatrix.array,s),(i&&!u.boundingBox||!this.isFrustumCulled(o,e,an))&&n.add(o,o.material.transparent||r)}o._children.length>0&&this._doUpdateRenderList(o,e,r,n,i)}},isFrustumCulled:function(){var t=new Vr,e=new Ur;return function(r,n,i){var a=r.boundingBox;if(a||(a=r.skeleton&&r.skeleton.boundingBox?r.skeleton.boundingBox:r.geometry.boundingBox),!a)return!1;if(e.array=i,t.transformFrom(a,e),r.castShadow&&this.viewBoundingBoxLastFrame.union(t),r.frustumCulling){if(!t.intersectBoundingBox(n.frustum.boundingBox))return!0;e.array=n.projectionMatrix.array,t.max.array[2]>0&&t.min.array[2]<0&&(t.max.array[2]=-1e-20),t.applyProjection(e);var o=t.min.array,s=t.max.array;if(s[0]<-1||o[0]>1||s[1]<-1||o[1]>1||s[2]<-1||o[2]>1)return!0}return!1}}(),_updateLightUniforms:function(){var t=this.lights;t.sort(lt);var e=this._lightUniforms;for(var r in e)for(var n in e[r])e[r][n].value.length=0;for(var i=0;i<t.length;i++){var a=t[i];if(!a.invisible){var r=a.group;for(var n in a.uniformTemplates){var o=a.uniformTemplates[n],s=o.value(a);if(null!=s){e[r]||(e[r]={}),e[r][n]||(e[r][n]={type:"",value:[]});var u=e[r][n];switch(u.type=o.type+"v",o.type){case"1i":case"1f":case"t":u.value.push(s);break;case"2f":case"3f":case"4f":for(var l=0;l<s.length;l++)u.value.push(s[l]);break;default:console.error("Unkown light uniform type "+o.type)}}}}}},getLightGroups:function(){var t=[];for(var e in this._lightNumber)t.push(e);return t},getNumberChangedLightGroups:function(){var t=[];for(var e in this._lightNumber)this.isLightNumberChanged(e)&&t.push(e);return t},isLightNumberChanged:function(t){var e=this._previousLightNumber,r=this._lightNumber;for(var n in r[t]){if(!e[t])return!0;if(r[t][n]!==e[t][n])return!0}for(var n in e[t]){if(!r[t])return!0;if(r[t][n]!==e[t][n])return!0}return!1},getLightsNumbers:function(t){return this._lightNumber[t]},getProgramKey:function(t){return this._lightProgramKeys[t]},setLightUniforms:function(){function t(t,e,r){for(var n in t){var i=t[n];if("tv"===i.type){if(!e.hasUniform(n))continue;for(var a=[],o=0;o<i.value.length;o++){var s=i.value[o],u=e.takeCurrentTextureSlot(r,s);a.push(u)}e.setUniform(r.gl,"1iv",n,a)}else e.setUniform(r.gl,i.type,n,i.value)}}return function(e,r,n){t(this._lightUniforms[r],e,n),t(this.shadowUniforms,e,n)}}(),dispose:function(){this.material=null,this._opaqueList=[],this._transparentList=[],this.lights=[],this._lightUniforms={},this._lightNumber={},this._nodeRepository={}}}),un=function(){this._contextId=0,this._caches=[],this._context={}};un.prototype={use:function(t,e){var r=this._caches;r[t]||(r[t]={},e&&(r[t]=e())),this._contextId=t,this._context=r[t]},put:function(t,e){this._context[t]=e},get:function(t){return this._context[t]},dirty:function(t){t=t||"";var e="__dt__"+t;this.put(e,!0)},dirtyAll:function(t){t=t||"";for(var e="__dt__"+t,r=this._caches,n=0;n<r.length;n++)r[n]&&(r[n][e]=!0)},fresh:function(t){t=t||"";var e="__dt__"+t;this.put(e,!1)},freshAll:function(t){t=t||"";for(var e="__dt__"+t,r=this._caches,n=0;n<r.length;n++)r[n]&&(r[n][e]=!1)},isDirty:function(t){t=t||"";var e="__dt__"+t,r=this._context;return!r.hasOwnProperty(e)||!0===r[e]},deleteContext:function(t){delete this._caches[t],this._context={}},delete:function(t){delete this._context[t]},clearAll:function(){this._caches={}},getContext:function(){return this._context},eachContext:function(t,e){Object.keys(this._caches).forEach(function(r){t&&t.call(e,r)})},miss:function(t){return!this._context.hasOwnProperty(t)}},un.prototype.constructor=un,ft.prototype.init=function(t){if(!this.value||this.value.length!==t*this.size){var e=ht(this.type);this.value=new e(t*this.size)}},ft.prototype.fromArray=function(t){var e,r=ht(this.type);if(t[0]&&t[0].length){var n=0,i=this.size;e=new r(t.length*i);for(var a=0;a<t.length;a++)for(var o=0;o<i;o++)e[n++]=t[a][o]}else e=new r(t);this.value=e},ft.prototype.clone=function(t){var e=new ft(this.name,this.type,this.size,this.semantic);return t&&console.warn("todo"),e};var ln=ke.extend(function(){return{attributes:{},indices:null,dynamic:!0,_enabledAttributes:null,__used:0}},function(){this._cache=new un,this._attributeList=Object.keys(this.attributes),this.__vaoCache={}},{mainAttribute:"",pick:null,pickByRay:null,dirty:function(){for(var t=this.getEnabledAttributes(),e=0;e<t.length;e++)this.dirtyAttribute(t[e]);this.dirtyIndices(),this._enabledAttributes=null,this._cache.dirty("any")},dirtyIndices:function(){this._cache.dirtyAll("indices")},dirtyAttribute:function(t){this._cache.dirtyAll(ct(t)),this._cache.dirtyAll("attributes")},getTriangleIndices:function(t,e){if(t<this.triangleCount&&t>=0){e||(e=[]);var r=this.indices;return e[0]=r[3*t],e[1]=r[3*t+1],e[2]=r[3*t+2],e}},setTriangleIndices:function(t,e){var r=this.indices;r[3*t]=e[0],r[3*t+1]=e[1],r[3*t+2]=e[2]},isUseIndices:function(){return!!this.indices},initIndicesFromArray:function(t){var e,r=this.vertexCount>65535?Ve.Uint32Array:Ve.Uint16Array;if(t[0]&&t[0].length){var n=0;e=new r(3*t.length);for(var i=0;i<t.length;i++)for(var a=0;a<3;a++)e[n++]=t[i][a]}else e=new r(t);this.indices=e},createAttribute:function(t,e,r,n){var i=new ft(t,e,r,n);return this.attributes[t]&&this.removeAttribute(t),this.attributes[t]=i,this._attributeList.push(t),i},removeAttribute:function(t){var e=this._attributeList,r=e.indexOf(t);return r>=0&&(e.splice(r,1),delete this.attributes[t],!0)},getAttribute:function(t){return this.attributes[t]},getEnabledAttributes:function(){var t=this._enabledAttributes,e=this._attributeList;if(t)return t;for(var r=[],n=this.vertexCount,i=0;i<e.length;i++){var a=e[i],o=this.attributes[a];o.value&&o.value.length===n*o.size&&r.push(a)}return this._enabledAttributes=r,r},getBufferChunks:function(t){var e=this._cache;e.use(t.__uid__);var r=e.isDirty("attributes"),n=e.isDirty("indices");if(r||n){this._updateBuffer(t.gl,r,n);for(var i=this.getEnabledAttributes(),a=0;a<i.length;a++)e.fresh(ct(i[a]));e.fresh("attributes"),e.fresh("indices")}return e.fresh("any"),e.get("chunks")},_updateBuffer:function(t,e,r){var n=this._cache,i=n.get("chunks"),a=!1;i||(i=[],i[0]={attributeBuffers:[],indicesBuffer:null},n.put("chunks",i),a=!0);var o=i[0],s=o.attributeBuffers,u=o.indicesBuffer;if(e||a){var l=this.getEnabledAttributes(),h={};if(!a)for(var c=0;c<s.length;c++)h[s[c].name]=s[c];for(var f=0;f<l.length;f++){var d,p=l[f],m=this.attributes[p];a||(d=h[p]);var _;_=d?d.buffer:t.createBuffer(),n.isDirty(ct(p))&&(t.bindBuffer(t.ARRAY_BUFFER,_),t.bufferData(t.ARRAY_BUFFER,m.value,this.dynamic?t.DYNAMIC_DRAW:t.STATIC_DRAW)),s[f]=new dt(p,m.type,_,m.size,m.semantic)}for(var c=f;c<s.length;c++)t.deleteBuffer(s[c].buffer);s.length=f}this.isUseIndices()&&(r||a)&&(u||(u=new pt(t.createBuffer()),o.indicesBuffer=u),u.count=this.indices.length,t.bindBuffer(t.ELEMENT_ARRAY_BUFFER,u.buffer),t.bufferData(t.ELEMENT_ARRAY_BUFFER,this.indices,this.dynamic?t.DYNAMIC_DRAW:t.STATIC_DRAW))},dispose:function(t){var e=this._cache;e.use(t.__uid__);var r=e.get("chunks");if(r)for(var n=0;n<r.length;n++){for(var i=r[n],a=0;a<i.attributeBuffers.length;a++){var o=i.attributeBuffers[a];t.gl.deleteBuffer(o.buffer)}i.indicesBuffer&&t.gl.deleteBuffer(i.indicesBuffer.buffer)}if(this.__vaoCache){var s=t.getGLExtension("OES_vertex_array_object");for(var u in this.__vaoCache){var l=this.__vaoCache[u].vao;l&&s.deleteVertexArrayOES(l)}}this.__vaoCache={},e.deleteContext(t.__uid__)}});Object.defineProperty&&(Object.defineProperty(ln.prototype,"vertexCount",{enumerable:!1,get:function(){var t=this.attributes[this.mainAttribute];return t||(t=this.attributes[this._attributeList[0]]),t&&t.value?t.value.length/t.size:0}}),Object.defineProperty(ln.prototype,"triangleCount",{enumerable:!1,get:function(){var t=this.indices;return t?t.length/3:0}})),ln.STATIC_DRAW=Ye.STATIC_DRAW,ln.DYNAMIC_DRAW=Ye.DYNAMIC_DRAW,ln.STREAM_DRAW=Ye.STREAM_DRAW,ln.AttributeBuffer=dt,ln.IndicesBuffer=pt,ln.Attribute=ft;var hn=Ce.create,cn=Ce.add,fn=Ce.set,dn=ln.Attribute,pn=ln.extend(function(){return{attributes:{position:new dn("position","float",3,"POSITION"),texcoord0:new dn("texcoord0","float",2,"TEXCOORD_0"),texcoord1:new dn("texcoord1","float",2,"TEXCOORD_1"),normal:new dn("normal","float",3,"NORMAL"),tangent:new dn("tangent","float",4,"TANGENT"),color:new dn("color","float",4,"COLOR"),weight:new dn("weight","float",3,"WEIGHT"),joint:new dn("joint","float",4,"JOINT"),barycentric:new dn("barycentric","float",3,null)},boundingBox:null}},{mainAttribute:"position",updateBoundingBox:function(){var t=this.boundingBox;t||(t=this.boundingBox=new Vr);var e=this.attributes.position.value;if(e&&e.length){var r=t.min,n=t.max,i=r.array,a=n.array;Ce.set(i,e[0],e[1],e[2]),Ce.set(a,e[0],e[1],e[2]);for(var o=3;o<e.length;){var s=e[o++],u=e[o++],l=e[o++];s<i[0]&&(i[0]=s),u<i[1]&&(i[1]=u),l<i[2]&&(i[2]=l),s>a[0]&&(a[0]=s),u>a[1]&&(a[1]=u),l>a[2]&&(a[2]=l)}r._dirty=!0,n._dirty=!0}},generateVertexNormals:function(){if(this.vertexCount){var t=this.indices,e=this.attributes,r=e.position.value,n=e.normal.value;if(n&&n.length===r.length)for(var i=0;i<n.length;i++)n[i]=0;else n=e.normal.value=new Ve.Float32Array(r.length);for(var a,o,s,u=hn(),l=hn(),h=hn(),c=hn(),f=hn(),d=hn(),p=t?t.length:this.vertexCount,m=0;m<p;){t?(a=t[m++],o=t[m++],s=t[m++]):(a=m++,o=m++,s=m++),fn(u,r[3*a],r[3*a+1],r[3*a+2]),fn(l,r[3*o],r[3*o+1],r[3*o+2]),fn(h,r[3*s],r[3*s+1],r[3*s+2]),Ce.sub(c,u,l),Ce.sub(f,l,h),Ce.cross(d,c,f);for(var i=0;i<3;i++)n[3*a+i]=n[3*a+i]+d[i],n[3*o+i]=n[3*o+i]+d[i],n[3*s+i]=n[3*s+i]+d[i]}for(var i=0;i<n.length;)fn(d,n[i],n[i+1],n[i+2]),Ce.normalize(d,d),n[i++]=d[0],n[i++]=d[1],n[i++]=d[2];this.dirty()}},generateFaceNormals:function(){if(this.vertexCount){this.isUniqueVertex()||this.generateUniqueVertex();var t=this.indices,e=this.attributes,r=e.position.value,n=e.normal.value,i=hn(),a=hn(),o=hn(),s=hn(),u=hn(),l=hn();n||(n=e.normal.value=new Float32Array(r.length));for(var h,c,f,d=t?t.length:this.vertexCount,p=0;p<d;){t?(h=t[p++],c=t[p++],f=t[p++]):(h=p++,c=p++,f=p++),fn(i,r[3*h],r[3*h+1],r[3*h+2]),fn(a,r[3*c],r[3*c+1],r[3*c+2]),fn(o,r[3*f],r[3*f+1],r[3*f+2]),Ce.sub(s,i,a),Ce.sub(u,a,o),Ce.cross(l,s,u),Ce.normalize(l,l);for(var m=0;m<3;m++)n[3*h+m]=l[m],n[3*c+m]=l[m],n[3*f+m]=l[m]}this.dirty()}},generateTangents:function(){if(this.vertexCount){var t=this.vertexCount,e=this.attributes;e.tangent.value||(e.tangent.value=new Float32Array(4*t));var r=e.texcoord0.value,n=e.position.value,i=e.tangent.value,a=e.normal.value;if(!r)return void console.warn("Geometry without texcoords can't generate tangents.");for(var o=[],s=[],u=0;u<t;u++)o[u]=[0,0,0],s[u]=[0,0,0];for(var l,h,c,f=[0,0,0],d=[0,0,0],p=this.indices,m=p?p.length:this.vertexCount,u=0;u<m;){p?(l=p[u++],h=p[u++],c=p[u++]):(l=u++,h=u++,c=u++);var _=r[2*l],g=r[2*h],v=r[2*c],y=r[2*l+1],T=r[2*h+1],x=r[2*c+1],E=n[3*l],A=n[3*h],b=n[3*c],S=n[3*l+1],M=n[3*h+1],w=n[3*c+1],C=n[3*l+2],R=n[3*h+2],L=n[3*c+2],N=A-E,P=b-E,I=M-S,O=w-S,D=R-C,B=L-C,U=g-_,F=v-_,k=T-y,H=x-y,G=1/(U*H-k*F);f[0]=(H*N-k*P)*G,f[1]=(H*I-k*O)*G,f[2]=(H*D-k*B)*G,d[0]=(U*P-F*N)*G,d[1]=(U*O-F*I)*G,d[2]=(U*B-F*D)*G,cn(o[l],o[l],f),cn(o[h],o[h],f),cn(o[c],o[c],f),cn(s[l],s[l],d),cn(s[h],s[h],d),cn(s[c],s[c],d)}for(var V=hn(),W=hn(),z=hn(),u=0;u<t;u++){z[0]=a[3*u],z[1]=a[3*u+1],z[2]=a[3*u+2];var X=o[u];Ce.scale(V,z,Ce.dot(z,X)),Ce.sub(V,X,V),Ce.normalize(V,V),Ce.cross(W,z,X),i[4*u]=V[0],i[4*u+1]=V[1],i[4*u+2]=V[2],i[4*u+3]=Ce.dot(W,s[u])<0?-1:1}this.dirty()}},isUniqueVertex:function(){return!this.isUseIndices()||this.vertexCount===this.indices.length},generateUniqueVertex:function(){if(this.vertexCount&&this.indices){this.indices.length>65535&&(this.indices=new Ve.Uint32Array(this.indices));for(var t=this.attributes,e=this.indices,r=this.getEnabledAttributes(),n={},i=0;i<r.length;i++){var a=r[i];n[a]=t[a].value,t[a].init(this.indices.length)}for(var o=0,s=0;s<e.length;s++){for(var u=e[s],i=0;i<r.length;i++)for(var a=r[i],l=t[a].value,h=t[a].size,c=0;c<h;c++)l[o*h+c]=n[a][u*h+c];e[s]=o,o++}this.dirty()}},generateBarycentric:function(){if(this.vertexCount){this.isUniqueVertex()||this.generateUniqueVertex();var t=this.attributes,e=t.barycentric.value,r=this.indices;if(!e||e.length!==3*r.length){e=t.barycentric.value=new Float32Array(3*r.length);for(var n=0;n<(r?r.length:this.vertexCount/3);)for(var i=0;i<3;i++){var a=r?r[n++]:3*n+i;e[3*a+i]=1}this.dirty()}}},applyTransform:function(t){var e=this.attributes,r=e.position.value,n=e.normal.value,i=e.tangent.value;t=t.array;var a=Er.create();Er.invert(a,t),Er.transpose(a,a);var o=Ce.transformMat4,s=Ce.forEach;s(r,3,0,null,o,t),n&&s(n,3,0,null,o,a),i&&s(i,4,0,null,o,a),this.boundingBox&&this.updateBoundingBox()},dispose:function(t){var e=this._cache;e.use(t.__uid__);var r=e.get("chunks");if(r)for(var n=0;n<r.length;n++){for(var i=r[n],a=0;a<i.attributeBuffers.length;a++){var o=i.attributeBuffers[a];t.gl.deleteBuffer(o.buffer)}i.indicesBuffer&&t.gl.deleteBuffer(i.indicesBuffer.buffer)}if(this.__vaoCache){var s=t.getGLExtension("OES_vertex_array_object");for(var u in this.__vaoCache){var l=this.__vaoCache[u].vao;l&&s.deleteVertexArrayOES(l)}}this.__vaoCache={},e.deleteContext(t.__uid__)}});pn.STATIC_DRAW=ln.STATIC_DRAW,pn.DYNAMIC_DRAW=ln.DYNAMIC_DRAW,pn.STREAM_DRAW=ln.STREAM_DRAW,pn.AttributeBuffer=ln.AttributeBuffer,pn.IndicesBuffer=ln.IndicesBuffer,pn.Attribute=dn;var mn=pn.extend({dynamic:!1,widthSegments:1,heightSegments:1},function(){this.build()},{build:function(){for(var t=this.heightSegments,e=this.widthSegments,r=this.attributes,n=[],i=[],a=[],o=[],s=0;s<=t;s++)for(var u=s/t,l=0;l<=e;l++){var h=l/e;if(n.push([2*h-1,2*u-1,0]),i&&i.push([h,u]),a&&a.push([0,0,1]),l<e&&s<t){var c=l+s*(e+1);o.push([c,c+1,c+e+1]),o.push([c+e+1,c+1,c+e+2])}}r.position.fromArray(n),r.texcoord0.fromArray(i),r.normal.fromArray(a),this.initIndicesFromArray(o),this.boundingBox=new Vr,this.boundingBox.min.set(-1,-1,0),this.boundingBox.max.set(1,1,0)}}),_n=new Ur,gn=pn.extend({dynamic:!1,widthSegments:1,heightSegments:1,depthSegments:1,inside:!1},function(){this.build()},{build:function(){var t={px:mt("px",this.depthSegments,this.heightSegments),nx:mt("nx",this.depthSegments,this.heightSegments),py:mt("py",this.widthSegments,this.depthSegments),ny:mt("ny",this.widthSegments,this.depthSegments),pz:mt("pz",this.widthSegments,this.heightSegments),nz:mt("nz",this.widthSegments,this.heightSegments)},e=["position","texcoord0","normal"],r=0,n=0;for(var i in t)r+=t[i].vertexCount,n+=t[i].indices.length;for(var a=0;a<e.length;a++)this.attributes[e[a]].init(r);this.indices=new Ve.Uint16Array(n);var o=0,s=0;for(var i in t){for(var u=t[i],a=0;a<e.length;a++)for(var l=e[a],h=u.attributes[l].value,c=u.attributes[l].size,f="normal"===l,d=0;d<h.length;d++){var p=h[d];this.inside&&f&&(p=-p),this.attributes[l].value[d+c*s]=p}for(var m=u.indices.length,d=0;d<u.indices.length;d++)this.indices[d+o]=s+u.indices[this.inside?m-d-1:d];o+=u.indices.length,s+=u.vertexCount}this.boundingBox=new Vr,this.boundingBox.max.set(1,1,1),this.boundingBox.min.set(-1,-1,-1)}}),vn=pn.extend({dynamic:!1,widthSegments:40,heightSegments:20,phiStart:0,phiLength:2*Math.PI,thetaStart:0,thetaLength:Math.PI,radius:1},function(){this.build()},{build:function(){var t=this.heightSegments,e=this.widthSegments,r=this.attributes.position,n=this.attributes.texcoord0,i=this.attributes.normal,a=(e+1)*(t+1);r.init(a),n.init(a),i.init(a);var o,s,u,l,h,c,f,d=a>65535?Uint32Array:Uint16Array,p=this.indices=new d(e*t*6),m=this.radius,_=this.phiStart,g=this.phiLength,v=this.thetaStart,y=this.thetaLength,m=this.radius,T=[],x=[],E=0,A=1/m;for(f=0;f<=t;f++)for(c=0;c<=e;c++)l=c/e,h=f/t,o=-m*Math.cos(_+l*g)*Math.sin(v+h*y),s=m*Math.cos(v+h*y),u=m*Math.sin(_+l*g)*Math.sin(v+h*y),T[0]=o,T[1]=s,T[2]=u,x[0]=l,x[1]=h,r.set(E,T),n.set(E,x),T[0]*=A,T[1]*=A,T[2]*=A,i.set(E,T),E++;var b,S,M,w,C=e+1,R=0;for(f=0;f<t;f++)for(c=0;c<e;c++)S=f*C+c,b=f*C+c+1,w=(f+1)*C+c+1,M=(f+1)*C+c,p[R++]=b,p[R++]=S,p[R++]=w,p[R++]=S,p[R++]=M,p[R++]=w;this.boundingBox=new Vr,this.boundingBox.max.set(m,m,m),this.boundingBox.min.set(-m,-m,-m)}}),yn=pn.extend({dynamic:!1,generator:null},function(){this.build()},{build:function(){var t=this.generator;if(!(t&&t.x&&t.y&&t.z))throw new Error("Invalid generator");var e=t.x,r=t.y,n=t.z,i=t.u||[0,1,.05],a=t.v||[0,1,.05],o=Math.floor((i[1]-i[0]+i[2])/i[2]),s=Math.floor((a[1]-a[0]+a[2])/a[2]);if(!isFinite(o)||!isFinite(s))throw new Error("Infinite generator");var u=o*s;this.attributes.position.init(u),this.attributes.texcoord0.init(u);for(var l=[],h=[],c=0,f=0;f<s;f++)for(var d=0;d<o;d++){var p=d*i[2]+i[0],m=f*a[2]+a[0];l[0]=e(p,m),l[1]=r(p,m),l[2]=n(p,m),h[0]=d/(o-1),h[1]=f/(s-1),this.attributes.position.set(c,l),this.attributes.texcoord0.set(c,h),c++}for(var _=u>65535?Uint32Array:Uint16Array,g=(o-1)*(s-1)*6,v=this.indices=new _(g),y=0,f=0;f<s-1;f++)for(var d=0;d<o-1;d++){var T=f*o+d,x=f*o+d+1,E=(f+1)*o+d+1,A=(f+1)*o+d;v[y++]=x,v[y++]=T,v[y++]=E,v[y++]=T,v[y++]=A,v[y++]=E}this.generateVertexNormals(),this.updateBoundingBox()}}),Tn=ke.extend({width:512,height:512,type:Ye.UNSIGNED_BYTE,format:Ye.RGBA,wrapS:Ye.REPEAT,wrapT:Ye.REPEAT,minFilter:Ye.LINEAR_MIPMAP_LINEAR,magFilter:Ye.LINEAR,useMipmap:!0,anisotropic:1,flipY:!0,sRGB:!0,unpackAlignment:4,premultiplyAlpha:!1,dynamic:!1,NPOT:!1,__used:0},function(){this._cache=new un},{getWebGLTexture:function(t){var e=t.gl,r=this._cache;return r.use(t.__uid__),r.miss("webgl_texture")&&r.put("webgl_texture",e.createTexture()),this.dynamic?this.update(t):r.isDirty()&&(this.update(t),r.fresh()),r.get("webgl_texture")},bind:function(){},unbind:function(){},dirty:function(){this._cache&&this._cache.dirtyAll()},update:function(t){},updateCommon:function(t){var e=t.gl;e.pixelStorei(e.UNPACK_FLIP_Y_WEBGL,this.flipY),e.pixelStorei(e.UNPACK_PREMULTIPLY_ALPHA_WEBGL,this.premultiplyAlpha),e.pixelStorei(e.UNPACK_ALIGNMENT,this.unpackAlignment),this.format===Ye.DEPTH_COMPONENT&&(this.useMipmap=!1);var r=t.getGLExtension("EXT_sRGB");this.format!==Tn.SRGB||r||(this.format=Tn.RGB),this.format!==Tn.SRGB_ALPHA||r||(this.format=Tn.RGBA),this.NPOT=!this.isPowerOfTwo()},getAvailableWrapS:function(){return this.NPOT?Ye.CLAMP_TO_EDGE:this.wrapS},getAvailableWrapT:function(){return this.NPOT?Ye.CLAMP_TO_EDGE:this.wrapT},getAvailableMinFilter:function(){var t=this.minFilter;return this.NPOT||!this.useMipmap?t===Ye.NEAREST_MIPMAP_NEAREST||t===Ye.NEAREST_MIPMAP_LINEAR?Ye.NEAREST:t===Ye.LINEAR_MIPMAP_LINEAR||t===Ye.LINEAR_MIPMAP_NEAREST?Ye.LINEAR:t:t},getAvailableMagFilter:function(){return this.magFilter},nextHighestPowerOfTwo:function(t){--t;for(var e=1;e<32;e<<=1)t|=t>>e;return t+1},dispose:function(t){var e=this._cache;e.use(t.__uid__);var r=e.get("webgl_texture");r&&t.gl.deleteTexture(r),e.deleteContext(t.__uid__)},isRenderable:function(){},isPowerOfTwo:function(){}});Object.defineProperty(Tn.prototype,"width",{get:function(){return this._width},set:function(t){this._width=t}}),Object.defineProperty(Tn.prototype,"height",{get:function(){return this._height},set:function(t){this._height=t}}),Tn.BYTE=Ye.BYTE,Tn.UNSIGNED_BYTE=Ye.UNSIGNED_BYTE,Tn.SHORT=Ye.SHORT,Tn.UNSIGNED_SHORT=Ye.UNSIGNED_SHORT,Tn.INT=Ye.INT,Tn.UNSIGNED_INT=Ye.UNSIGNED_INT,Tn.FLOAT=Ye.FLOAT,Tn.HALF_FLOAT=36193,Tn.UNSIGNED_INT_24_8_WEBGL=34042,Tn.DEPTH_COMPONENT=Ye.DEPTH_COMPONENT,Tn.DEPTH_STENCIL=Ye.DEPTH_STENCIL,Tn.ALPHA=Ye.ALPHA,Tn.RGB=Ye.RGB,Tn.RGBA=Ye.RGBA,Tn.LUMINANCE=Ye.LUMINANCE,Tn.LUMINANCE_ALPHA=Ye.LUMINANCE_ALPHA,Tn.SRGB=35904,Tn.SRGB_ALPHA=35906,Tn.COMPRESSED_RGB_S3TC_DXT1_EXT=33776,Tn.COMPRESSED_RGBA_S3TC_DXT1_EXT=33777,Tn.COMPRESSED_RGBA_S3TC_DXT3_EXT=33778,Tn.COMPRESSED_RGBA_S3TC_DXT5_EXT=33779,Tn.NEAREST=Ye.NEAREST,Tn.LINEAR=Ye.LINEAR,Tn.NEAREST_MIPMAP_NEAREST=Ye.NEAREST_MIPMAP_NEAREST,Tn.LINEAR_MIPMAP_NEAREST=Ye.LINEAR_MIPMAP_NEAREST,Tn.NEAREST_MIPMAP_LINEAR=Ye.NEAREST_MIPMAP_LINEAR,Tn.LINEAR_MIPMAP_LINEAR=Ye.LINEAR_MIPMAP_LINEAR,Tn.REPEAT=Ye.REPEAT,Tn.CLAMP_TO_EDGE=Ye.CLAMP_TO_EDGE,Tn.MIRRORED_REPEAT=Ye.MIRRORED_REPEAT;var xn={};xn.isPowerOfTwo=function(t){return 0==(t&t-1)},xn.nextPowerOfTwo=function(t){return t--,t|=t>>1,t|=t>>2,t|=t>>4,t|=t>>8,t|=t>>16,++t},xn.nearestPowerOfTwo=function(t){return Math.pow(2,Math.round(Math.log(t)/Math.LN2))};var En=xn.isPowerOfTwo,An=Tn.extend(function(){return{image:null,pixels:null,mipmaps:[],convertToPOT:!1}},{textureType:"texture2D",update:function(t){var e=t.gl;e.bindTexture(e.TEXTURE_2D,this._cache.get("webgl_texture")),this.updateCommon(t);var r=this.format,n=this.type,i=!(!this.convertToPOT||this.mipmaps.length||!this.image||this.wrapS!==Tn.REPEAT&&this.wrapT!==Tn.REPEAT||!this.NPOT);e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,i?this.wrapS:this.getAvailableWrapS()),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,i?this.wrapT:this.getAvailableWrapT()),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,i?this.magFilter:this.getAvailableMagFilter()),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,i?this.minFilter:this.getAvailableMinFilter());var a=t.getGLExtension("EXT_texture_filter_anisotropic");if(a&&this.anisotropic>1&&e.texParameterf(e.TEXTURE_2D,a.TEXTURE_MAX_ANISOTROPY_EXT,this.anisotropic),36193===n){t.getGLExtension("OES_texture_half_float")||(n=Ye.FLOAT)}if(this.mipmaps.length)for(var o=this.width,s=this.height,u=0;u<this.mipmaps.length;u++){var l=this.mipmaps[u];this._updateTextureData(e,l,u,o,s,r,n,!1),o/=2,s/=2}else this._updateTextureData(e,this,0,this.width,this.height,r,n,i),!this.useMipmap||this.NPOT&&!i||e.generateMipmap(e.TEXTURE_2D);e.bindTexture(e.TEXTURE_2D,null)},_updateTextureData:function(t,e,r,n,i,a,o,s){if(e.image){var u=e.image;s&&(this._potCanvas=gt(this,this._potCanvas),u=this._potCanvas),t.texImage2D(t.TEXTURE_2D,r,a,a,o,u)}else a<=Tn.COMPRESSED_RGBA_S3TC_DXT5_EXT&&a>=Tn.COMPRESSED_RGB_S3TC_DXT1_EXT?t.compressedTexImage2D(t.TEXTURE_2D,r,a,n,i,0,e.pixels):t.texImage2D(t.TEXTURE_2D,r,a,n,i,0,a,o,e.pixels)},generateMipmap:function(t){var e=t.gl;this.useMipmap&&!this.NPOT&&(e.bindTexture(e.TEXTURE_2D,this._cache.get("webgl_texture")),e.generateMipmap(e.TEXTURE_2D))},isPowerOfTwo:function(){return En(this.width)&&En(this.height)},isRenderable:function(){return this.image?this.image.width>0&&this.image.height>0:!(!this.width||!this.height)},bind:function(t){t.gl.bindTexture(t.gl.TEXTURE_2D,this.getWebGLTexture(t))},unbind:function(t){t.gl.bindTexture(t.gl.TEXTURE_2D,null)},load:function(t,e){var r=Ve.createImage();e&&(r.crossOrigin=e);var n=this;return r.onload=function(){n.dirty(),n.trigger("success",n)},r.onerror=function(){n.trigger("error",n)},r.src=t,this.image=r,this}});Object.defineProperty(An.prototype,"width",{get:function(){return this.image?this.image.width:this._width},set:function(t){this.image?console.warn("Texture from image can't set width"):(this._width!==t&&this.dirty(),this._width=t)}}),Object.defineProperty(An.prototype,"height",{get:function(){return this.image?this.image.height:this._height},set:function(t){this.image?console.warn("Texture from image can't set height"):(this._height!==t&&this.dirty(),this._height=t)}});var bn=xn.isPowerOfTwo,Sn=["px","nx","py","ny","pz","nz"],Mn=Tn.extend(function(){return{image:{px:null,nx:null,py:null,ny:null,pz:null,nz:null},pixels:{px:null,nx:null,py:null,ny:null,pz:null,nz:null},mipmaps:[]}},{textureType:"textureCube",update:function(t){var e=t.gl;e.bindTexture(e.TEXTURE_CUBE_MAP,this._cache.get("webgl_texture")),this.updateCommon(t);var r=this.format,n=this.type;e.texParameteri(e.TEXTURE_CUBE_MAP,e.TEXTURE_WRAP_S,this.getAvailableWrapS()),e.texParameteri(e.TEXTURE_CUBE_MAP,e.TEXTURE_WRAP_T,this.getAvailableWrapT()),e.texParameteri(e.TEXTURE_CUBE_MAP,e.TEXTURE_MAG_FILTER,this.getAvailableMagFilter()),e.texParameteri(e.TEXTURE_CUBE_MAP,e.TEXTURE_MIN_FILTER,this.getAvailableMinFilter());var i=t.getGLExtension("EXT_texture_filter_anisotropic");if(i&&this.anisotropic>1&&e.texParameterf(e.TEXTURE_CUBE_MAP,i.TEXTURE_MAX_ANISOTROPY_EXT,this.anisotropic),36193===n){t.getGLExtension("OES_texture_half_float")||(n=Ye.FLOAT)}if(this.mipmaps.length)for(var a=this.width,o=this.height,s=0;s<this.mipmaps.length;s++){var u=this.mipmaps[s];this._updateTextureData(e,u,s,a,o,r,n),a/=2,o/=2}else this._updateTextureData(e,this,0,this.width,this.height,r,n),!this.NPOT&&this.useMipmap&&e.generateMipmap(e.TEXTURE_CUBE_MAP);e.bindTexture(e.TEXTURE_CUBE_MAP,null)},_updateTextureData:function(t,e,r,n,i,a,o){for(var s=0;s<6;s++){var u=Sn[s],l=e.image&&e.image[u];l?t.texImage2D(t.TEXTURE_CUBE_MAP_POSITIVE_X+s,r,a,a,o,l):t.texImage2D(t.TEXTURE_CUBE_MAP_POSITIVE_X+s,r,a,n,i,0,a,o,e.pixels&&e.pixels[u])}},generateMipmap:function(t){var e=t.gl;this.useMipmap&&!this.NPOT&&(e.bindTexture(e.TEXTURE_CUBE_MAP,this._cache.get("webgl_texture")),e.generateMipmap(e.TEXTURE_CUBE_MAP))},bind:function(t){t.gl.bindTexture(t.gl.TEXTURE_CUBE_MAP,this.getWebGLTexture(t))},unbind:function(t){t.gl.bindTexture(t.gl.TEXTURE_CUBE_MAP,null)},isPowerOfTwo:function(){return this.image.px?bn(this.image.px.width)&&bn(this.image.px.height):bn(this.width)&&bn(this.height)},isRenderable:function(){return this.image.px?vt(this.image.px)&&vt(this.image.nx)&&vt(this.image.py)&&vt(this.image.ny)&&vt(this.image.pz)&&vt(this.image.nz):!(!this.width||!this.height)},load:function(t,e){var r=0,n=this;return Fe.each(t,function(t,i){var a=Ve.createImage();e&&(a.crossOrigin=e),a.onload=function(){0===--r&&(n.dirty(),n.trigger("success",n))},a.onerror=function(){r--},r++,a.src=t,n.image[i]=a}),this}});Object.defineProperty(Mn.prototype,"width",{get:function(){return this.image&&this.image.px?this.image.px.width:this._width},set:function(t){this.image&&this.image.px?console.warn("Texture from image can't set width"):(this._width!==t&&this.dirty(),this._width=t)}}),Object.defineProperty(Mn.prototype,"height",{get:function(){return this.image&&this.image.px?this.image.px.height:this._height},set:function(t){this.image&&this.image.px?console.warn("Texture from image can't set height"):(this._height!==t&&this.dirty(),this._height=t)}});var wn=zr.extend({material:null,geometry:null,mode:Ye.TRIANGLES,_renderInfo:null},{__program:null,lightGroup:0,renderOrder:0,culling:!0,cullFace:Ye.BACK,frontFace:Ye.CCW,frustumCulling:!0,receiveShadow:!0,castShadow:!0,ignorePicking:!1,ignorePreZ:!1,ignoreGBuffer:!1,isRenderable:function(){return this.geometry&&this.material&&this.material.shader&&!this.invisible&&this.geometry.vertexCount>0},beforeRender:function(t){},afterRender:function(t,e){},getBoundingBox:function(t,e){return e=zr.prototype.getBoundingBox.call(this,t,e),this.geometry&&this.geometry.boundingBox&&e.union(this.geometry.boundingBox),e},clone:function(){var t=["castShadow","receiveShadow","mode","culling","cullFace","frontFace","frustumCulling","renderOrder","lineWidth","ignorePicking","ignorePreZ","ignoreGBuffer"];return function(){var e=zr.prototype.clone.call(this);e.geometry=this.geometry,e.material=this.material;for(var r=0;r<t.length;r++){var n=t[r];e[n]!==this[n]&&(e[n]=this[n])}return e}}()});wn.POINTS=Ye.POINTS,wn.LINES=Ye.LINES,wn.LINE_LOOP=Ye.LINE_LOOP,wn.LINE_STRIP=Ye.LINE_STRIP,wn.TRIANGLES=Ye.TRIANGLES,wn.TRIANGLE_STRIP=Ye.TRIANGLE_STRIP,wn.TRIANGLE_FAN=Ye.TRIANGLE_FAN,wn.BACK=Ye.BACK,wn.FRONT=Ye.FRONT,wn.FRONT_AND_BACK=Ye.FRONT_AND_BACK,wn.CW=Ye.CW,wn.CCW=Ye.CCW;var Cn=wn.extend({skeleton:null,joints:null},function(){this.joints||(this.joints=[])},{offsetMatrix:null,isInstancedMesh:function(){return!1},isSkinnedMesh:function(){return!!(this.skeleton&&this.joints&&this.joints.length>0)},clone:function(){var t=wn.prototype.clone.call(this);return t.skeleton=this.skeleton,this.joints&&(t.joints=this.joints.slice()),t}});Cn.POINTS=Ye.POINTS,Cn.LINES=Ye.LINES,Cn.LINE_LOOP=Ye.LINE_LOOP,Cn.LINE_STRIP=Ye.LINE_STRIP,Cn.TRIANGLES=Ye.TRIANGLES,Cn.TRIANGLE_STRIP=Ye.TRIANGLE_STRIP,Cn.TRIANGLE_FAN=Ye.TRIANGLE_FAN,Cn.BACK=Ye.BACK,Cn.FRONT=Ye.FRONT,Cn.FRONT_AND_BACK=Ye.FRONT_AND_BACK,Cn.CW=Ye.CW,Cn.CCW=Ye.CCW;var Rn=rn.extend({fov:50,aspect:1,near:.1,far:2e3},{updateProjectionMatrix:function(){var t=this.fov/180*Math.PI;this.projectionMatrix.perspective(t,this.aspect,this.near,this.far)},decomposeProjectionMatrix:function(){var t=this.projectionMatrix.array,e=2*Math.atan(1/t[5]);this.fov=e/Math.PI*180,this.aspect=t[5]/t[0],this.near=t[14]/(t[10]-1),this.far=t[14]/(t[10]+1)},clone:function(){var t=rn.prototype.clone.call(this);return t.fov=this.fov,t.aspect=this.aspect,t.near=this.near,t.far=this.far,t}}),Ln=rn.extend({left:-1,right:1,near:-1,far:1,top:1,bottom:-1},{updateProjectionMatrix:function(){this.projectionMatrix.ortho(this.left,this.right,this.bottom,this.top,this.near,this.far)},decomposeProjectionMatrix:function(){var t=this.projectionMatrix.array;this.left=(-1-t[12])/t[0],this.right=(1-t[12])/t[0],this.top=(1-t[13])/t[5],this.bottom=(-1-t[13])/t[5],this.near=-(-1-t[14])/t[10],this.far=-(1-t[14])/t[10]},clone:function(){var t=rn.prototype.clone.call(this);return t.left=this.left,t.right=this.right,t.near=this.near,t.far=this.far,t.top=this.top,t.bottom=this.bottom,t}
}),Nn="\n@export clay.standard.chunk.varying\nvarying vec2 v_Texcoord;\nvarying vec3 v_Normal;\nvarying vec3 v_WorldPosition;\nvarying vec3 v_Barycentric;\n#if defined(PARALLAXOCCLUSIONMAP_ENABLED) || defined(NORMALMAP_ENABLED)\nvarying vec3 v_Tangent;\nvarying vec3 v_Bitangent;\n#endif\n#if defined(AOMAP_ENABLED)\nvarying vec2 v_Texcoord2;\n#endif\n#ifdef VERTEX_COLOR\nvarying vec4 v_Color;\n#endif\n@end\n@export clay.standard.chunk.light_header\n#ifdef AMBIENT_LIGHT_COUNT\n@import clay.header.ambient_light\n#endif\n#ifdef AMBIENT_SH_LIGHT_COUNT\n@import clay.header.ambient_sh_light\n#endif\n#ifdef AMBIENT_CUBEMAP_LIGHT_COUNT\n@import clay.header.ambient_cubemap_light\n#endif\n#ifdef POINT_LIGHT_COUNT\n@import clay.header.point_light\n#endif\n#ifdef DIRECTIONAL_LIGHT_COUNT\n@import clay.header.directional_light\n#endif\n#ifdef SPOT_LIGHT_COUNT\n@import clay.header.spot_light\n#endif\n@end\n@export clay.standard.vertex\n#define SHADER_NAME standard\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\nuniform mat4 worldInverseTranspose : WORLDINVERSETRANSPOSE;\nuniform mat4 world : WORLD;\nuniform vec2 uvRepeat = vec2(1.0, 1.0);\nuniform vec2 uvOffset = vec2(0.0, 0.0);\nattribute vec3 position : POSITION;\nattribute vec2 texcoord : TEXCOORD_0;\n#if defined(AOMAP_ENABLED)\nattribute vec2 texcoord2 : TEXCOORD_1;\n#endif\nattribute vec3 normal : NORMAL;\nattribute vec4 tangent : TANGENT;\n#ifdef VERTEX_COLOR\nattribute vec4 a_Color : COLOR;\n#endif\nattribute vec3 barycentric;\n@import clay.standard.chunk.varying\n@import clay.chunk.skinning_header\n@import clay.chunk.instancing_header\nvoid main()\n{\n vec4 skinnedPosition = vec4(position, 1.0);\n vec4 skinnedNormal = vec4(normal, 0.0);\n vec4 skinnedTangent = vec4(tangent.xyz, 0.0);\n#ifdef SKINNING\n @import clay.chunk.skin_matrix\n skinnedPosition = skinMatrixWS * skinnedPosition;\n skinnedNormal = skinMatrixWS * skinnedNormal;\n skinnedTangent = skinMatrixWS * skinnedTangent;\n#endif\n#ifdef INSTANCING\n @import clay.chunk.instancing_matrix\n skinnedPosition = instanceMat * skinnedPosition;\n skinnedNormal = instanceMat * skinnedNormal;\n skinnedTangent = instanceMat * skinnedTangent;\n#endif\n gl_Position = worldViewProjection * skinnedPosition;\n v_Texcoord = texcoord * uvRepeat + uvOffset;\n v_WorldPosition = (world * skinnedPosition).xyz;\n v_Barycentric = barycentric;\n v_Normal = normalize((worldInverseTranspose * skinnedNormal).xyz);\n#if defined(PARALLAXOCCLUSIONMAP_ENABLED) || defined(NORMALMAP_ENABLED)\n v_Tangent = normalize((worldInverseTranspose * skinnedTangent).xyz);\n v_Bitangent = normalize(cross(v_Normal, v_Tangent) * tangent.w);\n#endif\n#ifdef VERTEX_COLOR\n v_Color = a_Color;\n#endif\n#if defined(AOMAP_ENABLED)\n v_Texcoord2 = texcoord2;\n#endif\n}\n@end\n@export clay.standard.fragment\n#define PI 3.14159265358979\n#define GLOSSINESS_CHANNEL 0\n#define ROUGHNESS_CHANNEL 0\n#define METALNESS_CHANNEL 1\n#define DIFFUSEMAP_ALPHA_ALPHA\n@import clay.standard.chunk.varying\nuniform mat4 viewInverse : VIEWINVERSE;\n#ifdef NORMALMAP_ENABLED\nuniform sampler2D normalMap;\n#endif\nuniform float normalScale: 1.0;\n#ifdef DIFFUSEMAP_ENABLED\nuniform sampler2D diffuseMap;\n#endif\n#ifdef SPECULARMAP_ENABLED\nuniform sampler2D specularMap;\n#endif\n#ifdef USE_ROUGHNESS\nuniform float roughness : 0.5;\n #ifdef ROUGHNESSMAP_ENABLED\nuniform sampler2D roughnessMap;\n #endif\n#else\nuniform float glossiness: 0.5;\n #ifdef GLOSSINESSMAP_ENABLED\nuniform sampler2D glossinessMap;\n #endif\n#endif\n#ifdef METALNESSMAP_ENABLED\nuniform sampler2D metalnessMap;\n#endif\n#ifdef OCCLUSIONMAP_ENABLED\nuniform sampler2D occlusionMap;\n#endif\n#ifdef ENVIRONMENTMAP_ENABLED\nuniform samplerCube environmentMap;\n #ifdef PARALLAX_CORRECTED\nuniform vec3 environmentBoxMin;\nuniform vec3 environmentBoxMax;\n #endif\n#endif\n#ifdef BRDFLOOKUP_ENABLED\nuniform sampler2D brdfLookup;\n#endif\n#ifdef EMISSIVEMAP_ENABLED\nuniform sampler2D emissiveMap;\n#endif\n#ifdef SSAOMAP_ENABLED\nuniform sampler2D ssaoMap;\nuniform vec4 viewport : VIEWPORT;\n#endif\n#ifdef AOMAP_ENABLED\nuniform sampler2D aoMap;\nuniform float aoIntensity;\n#endif\nuniform vec3 color : [1.0, 1.0, 1.0];\nuniform float alpha : 1.0;\n#ifdef ALPHA_TEST\nuniform float alphaCutoff: 0.9;\n#endif\n#ifdef USE_METALNESS\nuniform float metalness : 0.0;\n#else\nuniform vec3 specularColor : [0.1, 0.1, 0.1];\n#endif\nuniform vec3 emission : [0.0, 0.0, 0.0];\nuniform float emissionIntensity: 1;\nuniform float lineWidth : 0.0;\nuniform vec4 lineColor : [0.0, 0.0, 0.0, 0.6];\n#ifdef ENVIRONMENTMAP_PREFILTER\nuniform float maxMipmapLevel: 5;\n#endif\n@import clay.standard.chunk.light_header\n@import clay.util.calculate_attenuation\n@import clay.util.edge_factor\n@import clay.util.rgbm\n@import clay.util.srgb\n@import clay.plugin.compute_shadow_map\n@import clay.util.parallax_correct\n@import clay.util.ACES\nfloat G_Smith(float g, float ndv, float ndl)\n{\n float roughness = 1.0 - g;\n float k = roughness * roughness / 2.0;\n float G1V = ndv / (ndv * (1.0 - k) + k);\n float G1L = ndl / (ndl * (1.0 - k) + k);\n return G1L * G1V;\n}\nvec3 F_Schlick(float ndv, vec3 spec) {\n return spec + (1.0 - spec) * pow(1.0 - ndv, 5.0);\n}\nfloat D_Phong(float g, float ndh) {\n float a = pow(8192.0, g);\n return (a + 2.0) / 8.0 * pow(ndh, a);\n}\nfloat D_GGX(float g, float ndh) {\n float r = 1.0 - g;\n float a = r * r;\n float tmp = ndh * ndh * (a - 1.0) + 1.0;\n return a / (PI * tmp * tmp);\n}\n#ifdef PARALLAXOCCLUSIONMAP_ENABLED\nuniform float parallaxOcclusionScale : 0.02;\nuniform float parallaxMaxLayers : 20;\nuniform float parallaxMinLayers : 5;\nuniform sampler2D parallaxOcclusionMap;\nmat3 transpose(in mat3 inMat)\n{\n vec3 i0 = inMat[0];\n vec3 i1 = inMat[1];\n vec3 i2 = inMat[2];\n return mat3(\n vec3(i0.x, i1.x, i2.x),\n vec3(i0.y, i1.y, i2.y),\n vec3(i0.z, i1.z, i2.z)\n );\n}\nvec2 parallaxUv(vec2 uv, vec3 viewDir)\n{\n float numLayers = mix(parallaxMaxLayers, parallaxMinLayers, abs(dot(vec3(0.0, 0.0, 1.0), viewDir)));\n float layerHeight = 1.0 / numLayers;\n float curLayerHeight = 0.0;\n vec2 deltaUv = viewDir.xy * parallaxOcclusionScale / (viewDir.z * numLayers);\n vec2 curUv = uv;\n float height = 1.0 - texture2D(parallaxOcclusionMap, curUv).r;\n for (int i = 0; i < 30; i++) {\n curLayerHeight += layerHeight;\n curUv -= deltaUv;\n height = 1.0 - texture2D(parallaxOcclusionMap, curUv).r;\n if (height < curLayerHeight) {\n break;\n }\n }\n vec2 prevUv = curUv + deltaUv;\n float next = height - curLayerHeight;\n float prev = 1.0 - texture2D(parallaxOcclusionMap, prevUv).r - curLayerHeight + layerHeight;\n return mix(curUv, prevUv, next / (next - prev));\n}\n#endif\nvoid main() {\n vec4 albedoColor = vec4(color, alpha);\n#ifdef VERTEX_COLOR\n albedoColor *= v_Color;\n#endif\n#ifdef SRGB_DECODE\n albedoColor = sRGBToLinear(albedoColor);\n#endif\n vec3 eyePos = viewInverse[3].xyz;\n vec3 V = normalize(eyePos - v_WorldPosition);\n vec2 uv = v_Texcoord;\n#if defined(PARALLAXOCCLUSIONMAP_ENABLED) || defined(NORMALMAP_ENABLED)\n mat3 tbn = mat3(v_Tangent, v_Bitangent, v_Normal);\n#endif\n#ifdef PARALLAXOCCLUSIONMAP_ENABLED\n uv = parallaxUv(v_Texcoord, normalize(transpose(tbn) * -V));\n#endif\n#ifdef DIFFUSEMAP_ENABLED\n vec4 texel = texture2D(diffuseMap, uv);\n #ifdef SRGB_DECODE\n texel = sRGBToLinear(texel);\n #endif\n albedoColor.rgb *= texel.rgb;\n #ifdef DIFFUSEMAP_ALPHA_ALPHA\n albedoColor.a *= texel.a;\n #endif\n#endif\n#ifdef USE_METALNESS\n float m = metalness;\n #ifdef METALNESSMAP_ENABLED\n float m2 = texture2D(metalnessMap, uv)[METALNESS_CHANNEL];\n m = clamp(m2 + (m - 0.5) * 2.0, 0.0, 1.0);\n #endif\n vec3 baseColor = albedoColor.rgb;\n albedoColor.rgb = baseColor * (1.0 - m);\n vec3 spec = mix(vec3(0.04), baseColor, m);\n#else\n vec3 spec = specularColor;\n#endif\n#ifdef USE_ROUGHNESS\n float g = clamp(1.0 - roughness, 0.0, 1.0);\n #ifdef ROUGHNESSMAP_ENABLED\n float g2 = 1.0 - texture2D(roughnessMap, uv)[ROUGHNESS_CHANNEL];\n g = clamp(g2 + (g - 0.5) * 2.0, 0.0, 1.0);\n #endif\n#else\n float g = glossiness;\n #ifdef GLOSSINESSMAP_ENABLED\n float g2 = texture2D(glossinessMap, uv)[GLOSSINESS_CHANNEL];\n g = clamp(g2 + (g - 0.5) * 2.0, 0.0, 1.0);\n #endif\n#endif\n#ifdef SPECULARMAP_ENABLED\n spec *= sRGBToLinear(texture2D(specularMap, uv)).rgb;\n#endif\n vec3 N = v_Normal;\n#ifdef DOUBLE_SIDED\n if (dot(N, V) < 0.0) {\n N = -N;\n }\n#endif\n#ifdef NORMALMAP_ENABLED\n if (dot(v_Tangent, v_Tangent) > 0.0) {\n vec3 normalTexel = texture2D(normalMap, uv).xyz;\n if (dot(normalTexel, normalTexel) > 0.0) { N = (normalTexel * 2.0 - 1.0);\n N = normalize(N * vec3(normalScale, normalScale, 1.0));\n tbn[1] = -tbn[1];\n N = normalize(tbn * N);\n }\n }\n#endif\n vec3 diffuseTerm = vec3(0.0, 0.0, 0.0);\n vec3 specularTerm = vec3(0.0, 0.0, 0.0);\n float ndv = clamp(dot(N, V), 0.0, 1.0);\n vec3 fresnelTerm = F_Schlick(ndv, spec);\n#ifdef AMBIENT_LIGHT_COUNT\n for(int _idx_ = 0; _idx_ < AMBIENT_LIGHT_COUNT; _idx_++)\n {{\n diffuseTerm += ambientLightColor[_idx_];\n }}\n#endif\n#ifdef AMBIENT_SH_LIGHT_COUNT\n for(int _idx_ = 0; _idx_ < AMBIENT_SH_LIGHT_COUNT; _idx_++)\n {{\n diffuseTerm += calcAmbientSHLight(_idx_, N) * ambientSHLightColor[_idx_];\n }}\n#endif\n#ifdef POINT_LIGHT_COUNT\n#if defined(POINT_LIGHT_SHADOWMAP_COUNT)\n float shadowContribsPoint[POINT_LIGHT_COUNT];\n if(shadowEnabled)\n {\n computeShadowOfPointLights(v_WorldPosition, shadowContribsPoint);\n }\n#endif\n for(int _idx_ = 0; _idx_ < POINT_LIGHT_COUNT; _idx_++)\n {{\n vec3 lightPosition = pointLightPosition[_idx_];\n vec3 lc = pointLightColor[_idx_];\n float range = pointLightRange[_idx_];\n vec3 L = lightPosition - v_WorldPosition;\n float dist = length(L);\n float attenuation = lightAttenuation(dist, range);\n L /= dist;\n vec3 H = normalize(L + V);\n float ndl = clamp(dot(N, L), 0.0, 1.0);\n float ndh = clamp(dot(N, H), 0.0, 1.0);\n float shadowContrib = 1.0;\n#if defined(POINT_LIGHT_SHADOWMAP_COUNT)\n if(shadowEnabled)\n {\n shadowContrib = shadowContribsPoint[_idx_];\n }\n#endif\n vec3 li = lc * ndl * attenuation * shadowContrib;\n diffuseTerm += li;\n specularTerm += li * fresnelTerm * D_Phong(g, ndh);\n }}\n#endif\n#ifdef DIRECTIONAL_LIGHT_COUNT\n#if defined(DIRECTIONAL_LIGHT_SHADOWMAP_COUNT)\n float shadowContribsDir[DIRECTIONAL_LIGHT_COUNT];\n if(shadowEnabled)\n {\n computeShadowOfDirectionalLights(v_WorldPosition, shadowContribsDir);\n }\n#endif\n for(int _idx_ = 0; _idx_ < DIRECTIONAL_LIGHT_COUNT; _idx_++)\n {{\n vec3 L = -normalize(directionalLightDirection[_idx_]);\n vec3 lc = directionalLightColor[_idx_];\n vec3 H = normalize(L + V);\n float ndl = clamp(dot(N, L), 0.0, 1.0);\n float ndh = clamp(dot(N, H), 0.0, 1.0);\n float shadowContrib = 1.0;\n#if defined(DIRECTIONAL_LIGHT_SHADOWMAP_COUNT)\n if(shadowEnabled)\n {\n shadowContrib = shadowContribsDir[_idx_];\n }\n#endif\n vec3 li = lc * ndl * shadowContrib;\n diffuseTerm += li;\n specularTerm += li * fresnelTerm * D_Phong(g, ndh);\n }}\n#endif\n#ifdef SPOT_LIGHT_COUNT\n#if defined(SPOT_LIGHT_SHADOWMAP_COUNT)\n float shadowContribsSpot[SPOT_LIGHT_COUNT];\n if(shadowEnabled)\n {\n computeShadowOfSpotLights(v_WorldPosition, shadowContribsSpot);\n }\n#endif\n for(int i = 0; i < SPOT_LIGHT_COUNT; i++)\n {\n vec3 lightPosition = spotLightPosition[i];\n vec3 spotLightDirection = -normalize(spotLightDirection[i]);\n vec3 lc = spotLightColor[i];\n float range = spotLightRange[i];\n float a = spotLightUmbraAngleCosine[i];\n float b = spotLightPenumbraAngleCosine[i];\n float falloffFactor = spotLightFalloffFactor[i];\n vec3 L = lightPosition - v_WorldPosition;\n float dist = length(L);\n float attenuation = lightAttenuation(dist, range);\n L /= dist;\n float c = dot(spotLightDirection, L);\n float falloff;\n falloff = clamp((c - a) /(b - a), 0.0, 1.0);\n falloff = pow(falloff, falloffFactor);\n vec3 H = normalize(L + V);\n float ndl = clamp(dot(N, L), 0.0, 1.0);\n float ndh = clamp(dot(N, H), 0.0, 1.0);\n float shadowContrib = 1.0;\n#if defined(SPOT_LIGHT_SHADOWMAP_COUNT)\n if (shadowEnabled)\n {\n shadowContrib = shadowContribsSpot[i];\n }\n#endif\n vec3 li = lc * attenuation * (1.0 - falloff) * shadowContrib * ndl;\n diffuseTerm += li;\n specularTerm += li * fresnelTerm * D_Phong(g, ndh);\n }\n#endif\n vec4 outColor = albedoColor;\n outColor.rgb *= max(diffuseTerm, vec3(0.0));\n outColor.rgb += max(specularTerm, vec3(0.0));\n#ifdef AMBIENT_CUBEMAP_LIGHT_COUNT\n vec3 L = reflect(-V, N);\n float rough2 = clamp(1.0 - g, 0.0, 1.0);\n float bias2 = rough2 * 5.0;\n vec2 brdfParam2 = texture2D(ambientCubemapLightBRDFLookup[0], vec2(rough2, ndv)).xy;\n vec3 envWeight2 = spec * brdfParam2.x + brdfParam2.y;\n vec3 envTexel2;\n for(int _idx_ = 0; _idx_ < AMBIENT_CUBEMAP_LIGHT_COUNT; _idx_++)\n {{\n #ifdef SUPPORT_TEXTURE_LOD\n envTexel2 = RGBMDecode(textureCubeLodEXT(ambientCubemapLightCubemap[_idx_], L, bias2), 8.12);\n #else\n envTexel2 = RGBMDecode(textureCube(ambientCubemapLightCubemap[_idx_], L), 8.12);\n #endif\n outColor.rgb += ambientCubemapLightColor[_idx_] * envTexel2 * envWeight2;\n }}\n#endif\n#ifdef ENVIRONMENTMAP_ENABLED\n vec3 envWeight = g * fresnelTerm;\n vec3 L = reflect(-V, N);\n #ifdef PARALLAX_CORRECTED\n L = parallaxCorrect(L, v_WorldPosition, environmentBoxMin, environmentBoxMax);\n#endif\n #ifdef ENVIRONMENTMAP_PREFILTER\n float rough = clamp(1.0 - g, 0.0, 1.0);\n float bias = rough * maxMipmapLevel;\n #ifdef SUPPORT_TEXTURE_LOD\n vec3 envTexel = decodeHDR(textureCubeLodEXT(environmentMap, L, bias)).rgb;\n #else\n vec3 envTexel = decodeHDR(textureCube(environmentMap, L)).rgb;\n #endif\n #ifdef BRDFLOOKUP_ENABLED\n vec2 brdfParam = texture2D(brdfLookup, vec2(rough, ndv)).xy;\n envWeight = spec * brdfParam.x + brdfParam.y;\n #endif\n #else\n vec3 envTexel = textureCube(environmentMap, L).xyz;\n #endif\n outColor.rgb += envTexel * envWeight;\n#endif\n float aoFactor = 1.0;\n#ifdef SSAOMAP_ENABLED\n aoFactor = min(texture2D(ssaoMap, (gl_FragCoord.xy - viewport.xy) / viewport.zw).r, aoFactor);\n#endif\n#ifdef AOMAP_ENABLED\n aoFactor = min(1.0 - clamp((1.0 - texture2D(aoMap, v_Texcoord2).r) * aoIntensity, 0.0, 1.0), aoFactor);\n#endif\n#ifdef OCCLUSIONMAP_ENABLED\n aoFactor = min(1.0 - clamp((1.0 - texture2D(occlusionMap, v_Texcoord).r), 0.0, 1.0), aoFactor);\n#endif\n outColor.rgb *= aoFactor;\n vec3 lEmission = emission;\n#ifdef EMISSIVEMAP_ENABLED\n lEmission *= texture2D(emissiveMap, uv).rgb;\n#endif\n outColor.rgb += lEmission * emissionIntensity;\n if(lineWidth > 0.)\n {\n outColor.rgb = mix(outColor.rgb, lineColor.rgb, (1.0 - edgeFactor(lineWidth)) * lineColor.a);\n }\n#ifdef ALPHA_TEST\n if (outColor.a < alphaCutoff) {\n discard;\n }\n#endif\n#ifdef TONEMAPPING\n outColor.rgb = ACESToneMapping(outColor.rgb);\n#endif\n#ifdef SRGB_ENCODE\n outColor = linearTosRGB(outColor);\n#endif\n gl_FragColor = encodeHDR(outColor);\n}\n@end\n@export clay.standardMR.vertex\n@import clay.standard.vertex\n@end\n@export clay.standardMR.fragment\n#define USE_METALNESS\n#define USE_ROUGHNESS\n@import clay.standard.fragment\n@end";Q.import(Nn);var Pn,In=["diffuseMap","normalMap","roughnessMap","metalnessMap","emissiveMap","environmentMap","brdfLookup","ssaoMap","aoMap"],On=["color","emission","emissionIntensity","alpha","roughness","metalness","uvRepeat","uvOffset","aoIntensity","alphaCutoff","normalScale"],Dn=["linear","encodeRGBM","decodeRGBM","doubleSided","alphaTest","roughnessChannel","metalnessChannel","environmentMapPrefiltered"],Bn={roughnessChannel:"ROUGHNESS_CHANNEL",metalnessChannel:"METALNESS_CHANNEL"},Un={linear:"SRGB_DECODE",encodeRGBM:"RGBM_ENCODE",decodeRGBM:"RGBM_DECODE",doubleSided:"DOUBLE_SIDED",alphaTest:"ALPHA_TEST",environmentMapPrefiltered:"ENVIRONMENTMAP_PREFILTER"},Fn=nr.extend(function(){return Pn||(Pn=new Q(Q.source("clay.standardMR.vertex"),Q.source("clay.standardMR.fragment"))),{shader:Pn}},function(t){Fe.extend(this,t),Fe.defaults(this,{color:[1,1,1],emission:[0,0,0],emissionIntensity:0,roughness:.5,metalness:0,alpha:1,alphaTest:!1,alphaCutoff:.9,normalScale:1,doubleSided:!1,diffuseMap:null,normalMap:null,roughnessMap:null,metalnessMap:null,emissiveMap:null,environmentMap:null,environmentBox:null,brdfLookup:null,ssaoMap:null,aoMap:null,uvRepeat:[1,1],uvOffset:[0,0],aoIntensity:1,environmentMapPrefiltered:!1,linear:!1,encodeRGBM:!1,decodeRGBM:!1,roughnessChannel:0,metalnessChannel:1})},{clone:function(){var t=new Fn({name:this.name});return In.forEach(function(e){this[e]&&(t[e]=this[e])},this),On.concat(Dn).forEach(function(e){t[e]=this[e]},this),t}});On.forEach(function(t){Object.defineProperty(Fn.prototype,t,{get:function(){return this.get(t)},set:function(e){this.setUniform(t,e)}})}),In.forEach(function(t){Object.defineProperty(Fn.prototype,t,{get:function(){return this.get(t)},set:function(e){this.setUniform(t,e)}})}),Dn.forEach(function(t){var e="_"+t;Object.defineProperty(Fn.prototype,t,{get:function(){return this[e]},set:function(r){if(this[e]=r,t in Bn){var n=Bn[t];this.define("fragment",n,r)}else{var n=Un[t];r?this.define("fragment",n):this.undefine("fragment",n)}}})}),Object.defineProperty(Fn.prototype,"environmentBox",{get:function(){var t=this._environmentBox;return t&&(t.min.setArray(this.get("environmentBoxMin")),t.max.setArray(this.get("environmentBoxMax"))),t},set:function(t){this._environmentBox=t;var e=this.uniforms=this.uniforms||{};e.environmentBoxMin=e.environmentBoxMin||{value:null},e.environmentBoxMax=e.environmentBoxMax||{value:null},t&&(this.setUniform("environmentBoxMin",t.min.array),this.setUniform("environmentBoxMax",t.max.array)),t?this.define("fragment","PARALLAX_CORRECTED"):this.undefine("fragment","PARALLAX_CORRECTED")}});var kn={};yt.prototype.get=function(t){var e=t;if(this._pool[e])return this._pool[e];var r=kn[t];if(!r)return void console.error('Shader "'+t+'" is not in the library');var n=new Q(r.vertex,r.fragment);return this._pool[e]=n,n},yt.prototype.clear=function(){this._pool={}};var Hn=new yt,Gn={createLibrary:function(){return new yt},get:function(){return Hn.get.apply(Hn,arguments)},template:Tt,clear:function(){return Hn.clear()}},Vn=ke.extend({name:"",index:-1,node:null}),Wn=new Vr,zn=new Ur,Xn=ke.extend(function(){return{relativeRootNode:null,name:"",joints:[],boundingBox:null,_clips:[],_invBindPoseMatricesArray:null,_jointMatricesSubArrays:[],_skinMatricesArray:null,_skinMatricesSubArrays:[],_subSkinMatricesArray:{}}},{addClip:function(t,e){for(var r=0;r<this._clips.length;r++)if(this._clips[r].clip===t)return;for(var n=[],r=0;r<this.joints.length;r++)n[r]=-1;for(var r=0;r<t.tracks.length;r++)for(var i=0;i<this.joints.length;i++){var a=this.joints[i],o=t.tracks[r],s=a.name;if(e&&(s=e[s]),o.name===s){n[i]=r;break}}return this._clips.push({maps:n,clip:t}),this._clips.length-1},removeClip:function(t){for(var e=-1,r=0;r<this._clips.length;r++)if(this._clips[r].clip===t){e=r;break}e>0&&this._clips.splice(e,1)},removeClipsAll:function(){this._clips=[]},getClip:function(t){if(this._clips[t])return this._clips[t].clip},getClipNumber:function(){return this._clips.length},updateJointMatrices:function(){var t=Er.create();return function(){this._invBindPoseMatricesArray=new Float32Array(16*this.joints.length),this._skinMatricesArray=new Float32Array(16*this.joints.length);for(var e=0;e<this.joints.length;e++){var r=this.joints[e];Er.copy(t,r.node.worldTransform.array),Er.invert(t,t);for(var n=16*e,i=0;i<16;i++)this._invBindPoseMatricesArray[n+i]=t[i]}this.updateMatricesSubArrays()}}(),updateJointsBoundingBoxes:function(t){for(var e=t.attributes,r=e.position,n=e.joint,i=e.weight,a=[],o=0;o<this.joints.length;o++)a[o]=new Vr,a[o].__updated=!1;for(var s=[],u=[],l=[],h=0,o=0;o<t.vertexCount;o++){n.get(o,s),r.get(o,u),i.get(o,l);for(var c=0;c<4;c++)if(l[c]>.01){var f=s[c];h=Math.max(h,f);var d=a[f].min.array,p=a[f].max.array;a[f].__updated=!0,d=Ce.min(d,d,u),p=Ce.max(p,p,u)}}this._jointsBoundingBoxes=a,this.boundingBox=new Vr,h<this.joints.length-1&&console.warn("Geometry joints and skeleton joints don't match")},setJointMatricesArray:function(t){this._invBindPoseMatricesArray=t,this._skinMatricesArray=new Float32Array(t.length),this.updateMatricesSubArrays()},updateMatricesSubArrays:function(){for(var t=0;t<this.joints.length;t++)this._jointMatricesSubArrays[t]=this._invBindPoseMatricesArray.subarray(16*t,16*(t+1)),this._skinMatricesSubArrays[t]=this._skinMatricesArray.subarray(16*t,16*(t+1))},update:function(){this._setPose();for(var t=this._jointsBoundingBoxes,e=0;e<this.joints.length;e++){var r=this.joints[e];Er.multiply(this._skinMatricesSubArrays[e],r.node.worldTransform.array,this._jointMatricesSubArrays[e])}if(this.boundingBox){this.boundingBox.min.set(1/0,1/0,1/0),this.boundingBox.max.set(-1/0,-1/0,-1/0);for(var e=0;e<this.joints.length;e++){var r=this.joints[e],n=t[e];n.__updated&&(Wn.copy(n),zn.array=this._skinMatricesSubArrays[e],Wn.applyTransform(zn),this.boundingBox.union(Wn))}}},getSubSkinMatrices:function(t,e){var r=this._subSkinMatricesArray[t];r||(r=this._subSkinMatricesArray[t]=new Float32Array(16*e.length));for(var n=0,i=0;i<e.length;i++)for(var a=e[i],o=0;o<16;o++)r[n++]=this._skinMatricesArray[16*a+o];return r},getSubSkinMatricesTexture:function(t,e){var r,n=this.getSubSkinMatrices(t,e),i=this.joints.length;r=i>256?64:i>64?32:i>16?16:8;var a=this._skinMatricesTexture=this._skinMatricesTexture||new An({type:Tn.FLOAT,minFilter:Tn.NEAREST,magFilter:Tn.NEAREST,useMipmap:!1,flipY:!1});return a.width=r,a.height=r,a.pixels&&a.pixels.length===r*r*4||(a.pixels=new Float32Array(r*r*4)),a.pixels.set(n),a.dirty(),a},getSkinMatricesTexture:function(){return this._skinMatricesTexture},_setPose:function(){if(this._clips[0])for(var t=this._clips[0].clip,e=this._clips[0].maps,r=0;r<this.joints.length;r++){var n=this.joints[r];if(-1!==e[r]){var i=t.tracks[e[r]];i.channels.position&&Ce.copy(n.node.position.array,i.position),i.channels.rotation&&Ne.copy(n.node.rotation.array,i.rotation),i.channels.scale&&Ce.copy(n.node.scale.array,i.scale),n.node.position._dirty=!0,n.node.rotation._dirty=!0,n.node.scale._dirty=!0}}},clone:function(t){var e=new Xn;e.name=this.name;for(var r=0;r<this.joints.length;r++){var n=new Vn,i=this.joints[r];if(n.name=i.name,n.index=i.index,t){var a=t[i.node.__uid__];a||console.warn("Can't find node"),n.node=a||i.node}else n.node=i.node;e.joints.push(n)}if(this._invBindPoseMatricesArray){var o=this._invBindPoseMatricesArray.length;e._invBindPoseMatricesArray=new Float32Array(o);for(var r=0;r<o;r++)e._invBindPoseMatricesArray[r]=this._invBindPoseMatricesArray[r];e._skinMatricesArray=new Float32Array(o),e.updateMatricesSubArrays()}return e._jointsBoundingBoxe=(this._jointsBoundingBoxes||[]).map(function(t){return t.clone()}),e.update(),e}}),jn="\n@export clay.util.rand\nhighp float rand(vec2 uv) {\n const highp float a = 12.9898, b = 78.233, c = 43758.5453;\n highp float dt = dot(uv.xy, vec2(a,b)), sn = mod(dt, 3.141592653589793);\n return fract(sin(sn) * c);\n}\n@end\n@export clay.util.calculate_attenuation\nuniform float attenuationFactor : 5.0;\nfloat lightAttenuation(float dist, float range)\n{\n float attenuation = 1.0;\n attenuation = dist*dist/(range*range+1.0);\n float att_s = attenuationFactor;\n attenuation = 1.0/(attenuation*att_s+1.0);\n att_s = 1.0/(att_s+1.0);\n attenuation = attenuation - att_s;\n attenuation /= 1.0 - att_s;\n return clamp(attenuation, 0.0, 1.0);\n}\n@end\n@export clay.util.edge_factor\n#ifdef SUPPORT_STANDARD_DERIVATIVES\nfloat edgeFactor(float width)\n{\n vec3 d = fwidth(v_Barycentric);\n vec3 a3 = smoothstep(vec3(0.0), d * width, v_Barycentric);\n return min(min(a3.x, a3.y), a3.z);\n}\n#else\nfloat edgeFactor(float width)\n{\n return 1.0;\n}\n#endif\n@end\n@export clay.util.encode_float\nvec4 encodeFloat(const in float depth)\n{\n const vec4 bitShifts = vec4(256.0*256.0*256.0, 256.0*256.0, 256.0, 1.0);\n const vec4 bit_mask = vec4(0.0, 1.0/256.0, 1.0/256.0, 1.0/256.0);\n vec4 res = fract(depth * bitShifts);\n res -= res.xxyz * bit_mask;\n return res;\n}\n@end\n@export clay.util.decode_float\nfloat decodeFloat(const in vec4 color)\n{\n const vec4 bitShifts = vec4(1.0/(256.0*256.0*256.0), 1.0/(256.0*256.0), 1.0/256.0, 1.0);\n return dot(color, bitShifts);\n}\n@end\n@export clay.util.float\n@import clay.util.encode_float\n@import clay.util.decode_float\n@end\n@export clay.util.rgbm_decode\nvec3 RGBMDecode(vec4 rgbm, float range) {\n return range * rgbm.rgb * rgbm.a;\n}\n@end\n@export clay.util.rgbm_encode\nvec4 RGBMEncode(vec3 color, float range) {\n if (dot(color, color) == 0.0) {\n return vec4(0.0);\n }\n vec4 rgbm;\n color /= range;\n rgbm.a = clamp(max(max(color.r, color.g), max(color.b, 1e-6)), 0.0, 1.0);\n rgbm.a = ceil(rgbm.a * 255.0) / 255.0;\n rgbm.rgb = color / rgbm.a;\n return rgbm;\n}\n@end\n@export clay.util.rgbm\n@import clay.util.rgbm_decode\n@import clay.util.rgbm_encode\nvec4 decodeHDR(vec4 color)\n{\n#if defined(RGBM_DECODE) || defined(RGBM)\n return vec4(RGBMDecode(color, 8.12), 1.0);\n#else\n return color;\n#endif\n}\nvec4 encodeHDR(vec4 color)\n{\n#if defined(RGBM_ENCODE) || defined(RGBM)\n return RGBMEncode(color.xyz, 8.12);\n#else\n return color;\n#endif\n}\n@end\n@export clay.util.srgb\nvec4 sRGBToLinear(in vec4 value) {\n return vec4(mix(pow(value.rgb * 0.9478672986 + vec3(0.0521327014), vec3(2.4)), value.rgb * 0.0773993808, vec3(lessThanEqual(value.rgb, vec3(0.04045)))), value.w);\n}\nvec4 linearTosRGB(in vec4 value) {\n return vec4(mix(pow(value.rgb, vec3(0.41666)) * 1.055 - vec3(0.055), value.rgb * 12.92, vec3(lessThanEqual(value.rgb, vec3(0.0031308)))), value.w);\n}\n@end\n@export clay.chunk.skinning_header\n#ifdef SKINNING\nattribute vec3 weight : WEIGHT;\nattribute vec4 joint : JOINT;\n#ifdef USE_SKIN_MATRICES_TEXTURE\nuniform sampler2D skinMatricesTexture : ignore;\nuniform float skinMatricesTextureSize: ignore;\nmat4 getSkinMatrix(sampler2D tex, float idx) {\n float j = idx * 4.0;\n float x = mod(j, skinMatricesTextureSize);\n float y = floor(j / skinMatricesTextureSize) + 0.5;\n vec2 scale = vec2(skinMatricesTextureSize);\n return mat4(\n texture2D(tex, vec2(x + 0.5, y) / scale),\n texture2D(tex, vec2(x + 1.5, y) / scale),\n texture2D(tex, vec2(x + 2.5, y) / scale),\n texture2D(tex, vec2(x + 3.5, y) / scale)\n );\n}\nmat4 getSkinMatrix(float idx) {\n return getSkinMatrix(skinMatricesTexture, idx);\n}\n#else\nuniform mat4 skinMatrix[JOINT_COUNT] : SKIN_MATRIX;\nmat4 getSkinMatrix(float idx) {\n return skinMatrix[int(idx)];\n}\n#endif\n#endif\n@end\n@export clay.chunk.skin_matrix\nmat4 skinMatrixWS = getSkinMatrix(joint.x) * weight.x;\nif (weight.y > 1e-4)\n{\n skinMatrixWS += getSkinMatrix(joint.y) * weight.y;\n}\nif (weight.z > 1e-4)\n{\n skinMatrixWS += getSkinMatrix(joint.z) * weight.z;\n}\nfloat weightW = 1.0-weight.x-weight.y-weight.z;\nif (weightW > 1e-4)\n{\n skinMatrixWS += getSkinMatrix(joint.w) * weightW;\n}\n@end\n@export clay.chunk.instancing_header\n#ifdef INSTANCING\nattribute vec4 instanceMat1;\nattribute vec4 instanceMat2;\nattribute vec4 instanceMat3;\n#endif\n@end\n@export clay.chunk.instancing_matrix\nmat4 instanceMat = mat4(\n vec4(instanceMat1.xyz, 0.0),\n vec4(instanceMat2.xyz, 0.0),\n vec4(instanceMat3.xyz, 0.0),\n vec4(instanceMat1.w, instanceMat2.w, instanceMat3.w, 1.0)\n);\n@end\n@export clay.util.parallax_correct\nvec3 parallaxCorrect(in vec3 dir, in vec3 pos, in vec3 boxMin, in vec3 boxMax) {\n vec3 first = (boxMax - pos) / dir;\n vec3 second = (boxMin - pos) / dir;\n vec3 further = max(first, second);\n float dist = min(further.x, min(further.y, further.z));\n vec3 fixedPos = pos + dir * dist;\n vec3 boxCenter = (boxMax + boxMin) * 0.5;\n return normalize(fixedPos - boxCenter);\n}\n@end\n@export clay.util.clamp_sample\nvec4 clampSample(const in sampler2D texture, const in vec2 coord)\n{\n#ifdef STEREO\n float eye = step(0.5, coord.x) * 0.5;\n vec2 coordClamped = clamp(coord, vec2(eye, 0.0), vec2(0.5 + eye, 1.0));\n#else\n vec2 coordClamped = clamp(coord, vec2(0.0), vec2(1.0));\n#endif\n return texture2D(texture, coordClamped);\n}\n@end\n@export clay.util.ACES\nvec3 ACESToneMapping(vec3 color)\n{\n const float A = 2.51;\n const float B = 0.03;\n const float C = 2.43;\n const float D = 0.59;\n const float E = 0.14;\n return (color * (A * color + B)) / (color * (C * color + D) + E);\n}\n@end",qn="@export clay.skybox.vertex\n#define SHADER_NAME skybox\nuniform mat4 world : WORLD;\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\nattribute vec3 position : POSITION;\nvarying vec3 v_WorldPosition;\nvoid main()\n{\n v_WorldPosition = (world * vec4(position, 1.0)).xyz;\n gl_Position = worldViewProjection * vec4(position, 1.0);\n}\n@end\n@export clay.skybox.fragment\n#define PI 3.1415926\nuniform mat4 viewInverse : VIEWINVERSE;\n#ifdef EQUIRECTANGULAR\nuniform sampler2D environmentMap;\n#else\nuniform samplerCube environmentMap;\n#endif\nuniform float lod: 0.0;\nvarying vec3 v_WorldPosition;\n@import clay.util.rgbm\n@import clay.util.srgb\n@import clay.util.ACES\nvoid main()\n{\n vec3 eyePos = viewInverse[3].xyz;\n vec3 V = normalize(v_WorldPosition - eyePos);\n#ifdef EQUIRECTANGULAR\n float phi = acos(V.y);\n float theta = atan(-V.x, V.z) + PI * 0.5;\n vec2 uv = vec2(theta / 2.0 / PI, phi / PI);\n vec4 texel = decodeHDR(texture2D(environmentMap, fract(uv)));\n#else\n #if defined(LOD) || defined(SUPPORT_TEXTURE_LOD)\n vec4 texel = decodeHDR(textureCubeLodEXT(environmentMap, V, lod));\n #else\n vec4 texel = decodeHDR(textureCube(environmentMap, V));\n #endif\n#endif\n#ifdef SRGB_DECODE\n texel = sRGBToLinear(texel);\n#endif\n#ifdef TONEMAPPING\n texel.rgb = ACESToneMapping(texel.rgb);\n#endif\n#ifdef SRGB_ENCODE\n texel = linearTosRGB(texel);\n#endif\n gl_FragColor = encodeHDR(vec4(texel.rgb, 1.0));\n}\n@end";Q.import(jr),Q.import(jn),
Q.import("@export clay.basic.vertex\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\nuniform vec2 uvRepeat : [1.0, 1.0];\nuniform vec2 uvOffset : [0.0, 0.0];\nattribute vec2 texcoord : TEXCOORD_0;\nattribute vec3 position : POSITION;\nattribute vec3 barycentric;\n@import clay.chunk.skinning_header\n@import clay.chunk.instancing_header\nvarying vec2 v_Texcoord;\nvarying vec3 v_Barycentric;\n#ifdef VERTEX_COLOR\nattribute vec4 a_Color : COLOR;\nvarying vec4 v_Color;\n#endif\nvoid main()\n{\n vec4 skinnedPosition = vec4(position, 1.0);\n#ifdef SKINNING\n @import clay.chunk.skin_matrix\n skinnedPosition = skinMatrixWS * skinnedPosition;\n#endif\n#ifdef INSTANCING\n @import clay.chunk.instancing_matrix\n skinnedPosition = instanceMat * skinnedPosition;\n#endif\n v_Texcoord = texcoord * uvRepeat + uvOffset;\n v_Barycentric = barycentric;\n gl_Position = worldViewProjection * skinnedPosition;\n#ifdef VERTEX_COLOR\n v_Color = a_Color;\n#endif\n}\n@end\n@export clay.basic.fragment\n#define DIFFUSEMAP_ALPHA_ALPHA\nvarying vec2 v_Texcoord;\nuniform sampler2D diffuseMap;\nuniform vec3 color : [1.0, 1.0, 1.0];\nuniform vec3 emission : [0.0, 0.0, 0.0];\nuniform float alpha : 1.0;\n#ifdef ALPHA_TEST\nuniform float alphaCutoff: 0.9;\n#endif\n#ifdef VERTEX_COLOR\nvarying vec4 v_Color;\n#endif\nuniform float lineWidth : 0.0;\nuniform vec4 lineColor : [0.0, 0.0, 0.0, 0.6];\nvarying vec3 v_Barycentric;\n@import clay.util.edge_factor\n@import clay.util.rgbm\n@import clay.util.srgb\n@import clay.util.ACES\nvoid main()\n{\n gl_FragColor = vec4(color, alpha);\n#ifdef VERTEX_COLOR\n gl_FragColor *= v_Color;\n#endif\n#ifdef SRGB_DECODE\n gl_FragColor = sRGBToLinear(gl_FragColor);\n#endif\n#ifdef DIFFUSEMAP_ENABLED\n vec4 texel = decodeHDR(texture2D(diffuseMap, v_Texcoord));\n#ifdef SRGB_DECODE\n texel = sRGBToLinear(texel);\n#endif\n#if defined(DIFFUSEMAP_ALPHA_ALPHA)\n gl_FragColor.a = texel.a;\n#endif\n gl_FragColor.rgb *= texel.rgb;\n#endif\n gl_FragColor.rgb += emission;\n if( lineWidth > 0.)\n {\n gl_FragColor.rgb = mix(gl_FragColor.rgb, lineColor.rgb, (1.0 - edgeFactor(lineWidth)) * lineColor.a);\n }\n#ifdef ALPHA_TEST\n if (gl_FragColor.a < alphaCutoff) {\n discard;\n }\n#endif\n#ifdef TONEMAPPING\n gl_FragColor.rgb = ACESToneMapping(gl_FragColor.rgb);\n#endif\n#ifdef SRGB_ENCODE\n gl_FragColor = linearTosRGB(gl_FragColor);\n#endif\n gl_FragColor = encodeHDR(gl_FragColor);\n}\n@end"),Q.import("\n@export clay.lambert.vertex\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\nuniform mat4 worldInverseTranspose : WORLDINVERSETRANSPOSE;\nuniform mat4 world : WORLD;\nuniform vec2 uvRepeat : [1.0, 1.0];\nuniform vec2 uvOffset : [0.0, 0.0];\nattribute vec3 position : POSITION;\nattribute vec2 texcoord : TEXCOORD_0;\nattribute vec3 normal : NORMAL;\nattribute vec3 barycentric;\n#ifdef VERTEX_COLOR\nattribute vec4 a_Color : COLOR;\nvarying vec4 v_Color;\n#endif\n@import clay.chunk.skinning_header\n@import clay.chunk.instancing_header\nvarying vec2 v_Texcoord;\nvarying vec3 v_Normal;\nvarying vec3 v_WorldPosition;\nvarying vec3 v_Barycentric;\nvoid main()\n{\n vec4 skinnedPosition = vec4(position, 1.0);\n vec4 skinnedNormal = vec4(normal, 0.0);\n#ifdef SKINNING\n @import clay.chunk.skin_matrix\n skinnedPosition = skinMatrixWS * skinnedPosition;\n skinnedNormal = skinMatrixWS * skinnedNormal;\n#endif\n#ifdef INSTANCING\n @import clay.chunk.instancing_matrix\n skinnedPosition = instanceMat * skinnedPosition;\n skinnedNormal = instanceMat * skinnedNormal;\n#endif\n gl_Position = worldViewProjection * skinnedPosition;\n v_Texcoord = texcoord * uvRepeat + uvOffset;\n v_Normal = normalize((worldInverseTranspose * skinnedNormal).xyz);\n v_WorldPosition = ( world * skinnedPosition ).xyz;\n v_Barycentric = barycentric;\n#ifdef VERTEX_COLOR\n v_Color = a_Color;\n#endif\n}\n@end\n@export clay.lambert.fragment\n#define DIFFUSEMAP_ALPHA_ALPHA\nvarying vec2 v_Texcoord;\nvarying vec3 v_Normal;\nvarying vec3 v_WorldPosition;\nuniform sampler2D diffuseMap;\nuniform sampler2D alphaMap;\nuniform vec3 color : [1.0, 1.0, 1.0];\nuniform vec3 emission : [0.0, 0.0, 0.0];\nuniform float alpha : 1.0;\n#ifdef ALPHA_TEST\nuniform float alphaCutoff: 0.9;\n#endif\nuniform float lineWidth : 0.0;\nuniform vec4 lineColor : [0.0, 0.0, 0.0, 0.6];\nvarying vec3 v_Barycentric;\n#ifdef VERTEX_COLOR\nvarying vec4 v_Color;\n#endif\n#ifdef AMBIENT_LIGHT_COUNT\n@import clay.header.ambient_light\n#endif\n#ifdef AMBIENT_SH_LIGHT_COUNT\n@import clay.header.ambient_sh_light\n#endif\n#ifdef POINT_LIGHT_COUNT\n@import clay.header.point_light\n#endif\n#ifdef DIRECTIONAL_LIGHT_COUNT\n@import clay.header.directional_light\n#endif\n#ifdef SPOT_LIGHT_COUNT\n@import clay.header.spot_light\n#endif\n@import clay.util.calculate_attenuation\n@import clay.util.edge_factor\n@import clay.util.rgbm\n@import clay.plugin.compute_shadow_map\n@import clay.util.ACES\nvoid main()\n{\n gl_FragColor = vec4(color, alpha);\n#ifdef VERTEX_COLOR\n gl_FragColor *= v_Color;\n#endif\n#ifdef SRGB_DECODE\n gl_FragColor = sRGBToLinear(gl_FragColor);\n#endif\n#ifdef DIFFUSEMAP_ENABLED\n vec4 tex = texture2D( diffuseMap, v_Texcoord );\n#ifdef SRGB_DECODE\n tex.rgb = pow(tex.rgb, vec3(2.2));\n#endif\n gl_FragColor.rgb *= tex.rgb;\n#ifdef DIFFUSEMAP_ALPHA_ALPHA\n gl_FragColor.a *= tex.a;\n#endif\n#endif\n vec3 diffuseColor = vec3(0.0, 0.0, 0.0);\n#ifdef AMBIENT_LIGHT_COUNT\n for(int _idx_ = 0; _idx_ < AMBIENT_LIGHT_COUNT; _idx_++)\n {\n diffuseColor += ambientLightColor[_idx_];\n }\n#endif\n#ifdef AMBIENT_SH_LIGHT_COUNT\n for(int _idx_ = 0; _idx_ < AMBIENT_SH_LIGHT_COUNT; _idx_++)\n {{\n diffuseColor += calcAmbientSHLight(_idx_, v_Normal) * ambientSHLightColor[_idx_];\n }}\n#endif\n#ifdef POINT_LIGHT_COUNT\n#if defined(POINT_LIGHT_SHADOWMAP_COUNT)\n float shadowContribsPoint[POINT_LIGHT_COUNT];\n if( shadowEnabled )\n {\n computeShadowOfPointLights(v_WorldPosition, shadowContribsPoint);\n }\n#endif\n for(int i = 0; i < POINT_LIGHT_COUNT; i++)\n {\n vec3 lightPosition = pointLightPosition[i];\n vec3 lightColor = pointLightColor[i];\n float range = pointLightRange[i];\n vec3 lightDirection = lightPosition - v_WorldPosition;\n float dist = length(lightDirection);\n float attenuation = lightAttenuation(dist, range);\n lightDirection /= dist;\n float ndl = dot( v_Normal, lightDirection );\n float shadowContrib = 1.0;\n#if defined(POINT_LIGHT_SHADOWMAP_COUNT)\n if( shadowEnabled )\n {\n shadowContrib = shadowContribsPoint[i];\n }\n#endif\n diffuseColor += lightColor * clamp(ndl, 0.0, 1.0) * attenuation * shadowContrib;\n }\n#endif\n#ifdef DIRECTIONAL_LIGHT_COUNT\n#if defined(DIRECTIONAL_LIGHT_SHADOWMAP_COUNT)\n float shadowContribsDir[DIRECTIONAL_LIGHT_COUNT];\n if(shadowEnabled)\n {\n computeShadowOfDirectionalLights(v_WorldPosition, shadowContribsDir);\n }\n#endif\n for(int i = 0; i < DIRECTIONAL_LIGHT_COUNT; i++)\n {\n vec3 lightDirection = -directionalLightDirection[i];\n vec3 lightColor = directionalLightColor[i];\n float ndl = dot(v_Normal, normalize(lightDirection));\n float shadowContrib = 1.0;\n#if defined(DIRECTIONAL_LIGHT_SHADOWMAP_COUNT)\n if( shadowEnabled )\n {\n shadowContrib = shadowContribsDir[i];\n }\n#endif\n diffuseColor += lightColor * clamp(ndl, 0.0, 1.0) * shadowContrib;\n }\n#endif\n#ifdef SPOT_LIGHT_COUNT\n#if defined(SPOT_LIGHT_SHADOWMAP_COUNT)\n float shadowContribsSpot[SPOT_LIGHT_COUNT];\n if(shadowEnabled)\n {\n computeShadowOfSpotLights(v_WorldPosition, shadowContribsSpot);\n }\n#endif\n for(int i = 0; i < SPOT_LIGHT_COUNT; i++)\n {\n vec3 lightPosition = -spotLightPosition[i];\n vec3 spotLightDirection = -normalize( spotLightDirection[i] );\n vec3 lightColor = spotLightColor[i];\n float range = spotLightRange[i];\n float a = spotLightUmbraAngleCosine[i];\n float b = spotLightPenumbraAngleCosine[i];\n float falloffFactor = spotLightFalloffFactor[i];\n vec3 lightDirection = lightPosition - v_WorldPosition;\n float dist = length(lightDirection);\n float attenuation = lightAttenuation(dist, range);\n lightDirection /= dist;\n float c = dot(spotLightDirection, lightDirection);\n float falloff;\n falloff = clamp((c - a) /( b - a), 0.0, 1.0);\n falloff = pow(falloff, falloffFactor);\n float ndl = dot(v_Normal, lightDirection);\n ndl = clamp(ndl, 0.0, 1.0);\n float shadowContrib = 1.0;\n#if defined(SPOT_LIGHT_SHADOWMAP_COUNT)\n if( shadowEnabled )\n {\n shadowContrib = shadowContribsSpot[i];\n }\n#endif\n diffuseColor += lightColor * ndl * attenuation * (1.0-falloff) * shadowContrib;\n }\n#endif\n gl_FragColor.rgb *= diffuseColor;\n gl_FragColor.rgb += emission;\n if(lineWidth > 0.)\n {\n gl_FragColor.rgb = mix(gl_FragColor.rgb, lineColor.rgb, (1.0 - edgeFactor(lineWidth)) * lineColor.a);\n }\n#ifdef ALPHA_TEST\n if (gl_FragColor.a < alphaCutoff) {\n discard;\n }\n#endif\n#ifdef TONEMAPPING\n gl_FragColor.rgb = ACESToneMapping(gl_FragColor.rgb);\n#endif\n#ifdef SRGB_ENCODE\n gl_FragColor = linearTosRGB(gl_FragColor);\n#endif\n gl_FragColor = encodeHDR(gl_FragColor);\n}\n@end"),Q.import(Nn),Q.import("@export clay.wireframe.vertex\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\nuniform mat4 world : WORLD;\nattribute vec3 position : POSITION;\nattribute vec3 barycentric;\n@import clay.chunk.skinning_header\nvarying vec3 v_Barycentric;\nvoid main()\n{\n vec3 skinnedPosition = position;\n#ifdef SKINNING\n @import clay.chunk.skin_matrix\n skinnedPosition = (skinMatrixWS * vec4(position, 1.0)).xyz;\n#endif\n gl_Position = worldViewProjection * vec4(skinnedPosition, 1.0 );\n v_Barycentric = barycentric;\n}\n@end\n@export clay.wireframe.fragment\nuniform vec3 color : [0.0, 0.0, 0.0];\nuniform float alpha : 1.0;\nuniform float lineWidth : 1.0;\nvarying vec3 v_Barycentric;\n@import clay.util.edge_factor\nvoid main()\n{\n gl_FragColor.rgb = color;\n gl_FragColor.a = (1.0-edgeFactor(lineWidth)) * alpha;\n}\n@end"),Q.import(qn),Q.import(xr),Gn.template("clay.basic",Q.source("clay.basic.vertex"),Q.source("clay.basic.fragment")),Gn.template("clay.lambert",Q.source("clay.lambert.vertex"),Q.source("clay.lambert.fragment")),Gn.template("clay.wireframe",Q.source("clay.wireframe.vertex"),Q.source("clay.wireframe.fragment")),Gn.template("clay.skybox",Q.source("clay.skybox.vertex"),Q.source("clay.skybox.fragment")),Gn.template("clay.prez",Q.source("clay.prez.vertex"),Q.source("clay.prez.fragment")),Gn.template("clay.standard",Q.source("clay.standard.vertex"),Q.source("clay.standard.fragment")),Gn.template("clay.standardMR",Q.source("clay.standardMR.vertex"),Q.source("clay.standardMR.fragment"));var Yn={NORMAL:"normal",POSITION:"position",TEXCOORD_0:"texcoord0",TEXCOORD_1:"texcoord1",WEIGHTS_0:"weight",JOINTS_0:"joint",COLOR_0:"color"},Kn={5120:Ve.Int8Array,5121:Ve.Uint8Array,5122:Ve.Int16Array,5123:Ve.Uint16Array,5125:Ve.Uint32Array,5126:Ve.Float32Array},Zn={SCALAR:1,VEC2:2,VEC3:3,VEC4:4,MAT2:4,MAT3:9,MAT4:16},Jn=ke.extend({rootNode:null,rootPath:null,textureRootPath:null,bufferRootPath:null,shader:"clay.standard",useStandardMaterial:!1,includeCamera:!0,includeAnimation:!0,includeMesh:!0,includeTexture:!0,crossOrigin:"",textureFlipY:!1,textureConvertToPOT:!1,shaderLibrary:null},function(){this.shaderLibrary||(this.shaderLibrary=Gn.createLibrary())},{load:function(t){var e=this,r=t.endsWith(".glb");null==this.rootPath&&(this.rootPath=t.slice(0,t.lastIndexOf("/"))),Ve.request.get({url:t,onprogress:function(t,r,n){e.trigger("progress",t,r,n)},onerror:function(t){e.trigger("error",t)},responseType:r?"arraybuffer":"text",onload:function(t){r?e.parseBinary(t):("string"==typeof t&&(t=JSON.parse(t)),e.parse(t))}})},parseBinary:function(t){var e=new Uint32Array(t,0,4);if(1179937895!==e[0])return void this.trigger("error","Invalid glTF binary format: Invalid header");if(e[0]<2)return void this.trigger("error","Only glTF2.0 is supported.");for(var r,n=new DataView(t,12),i=[],a=0;a<n.byteLength;){var o=n.getUint32(a,!0);a+=4;var s=n.getUint32(a,!0);if(a+=4,1313821514===s){var u=new Uint8Array(t,a+12,o),l=new TextDecoder,h=l.decode(u);try{r=JSON.parse(h)}catch(t){return void this.trigger("error","JSON Parse error:"+t.toString())}}else 5130562===s&&i.push(t.slice(a+12,a+12+o));a+=o}return r?this.parse(r,i):void this.trigger("error","Invalid glTF binary format: Can't find JSON.")},parse:function(t,e){function r(){0===--u&&i()}function n(){return{json:t,scene:a.rootNode?null:s,rootNode:a.rootNode?s:null,cameras:o.cameras,textures:o.textures,materials:o.materials,skeletons:o.skeletons,meshes:o.instancedMeshes,clips:o.clips,nodes:o.nodes}}function i(e){if(o.buffers.length!==t.buffers.length)return void setTimeout(function(){a.trigger("error","Buffer not load complete.")});if(t.bufferViews.forEach(function(t,e){o.bufferViews[e]=o.buffers[t.buffer].slice(t.byteOffset||0,(t.byteOffset||0)+(t.byteLength||0))}),o.buffers=null,a.includeMesh&&(a.includeTexture&&a._parseTextures(t,o),a._parseMaterials(t,o),a._parseMeshes(t,o)),a._parseNodes(t,o),t.scenes){var r=t.scenes[t.scene||0];if(r)for(var i=0;i<r.nodes.length;i++){var u=o.nodes[r.nodes[i]];u.update(),s.add(u)}}a.includeMesh&&a._parseSkins(t,o),a.includeAnimation&&a._parseAnimations(t,o),e?setTimeout(function(){a.trigger("success",n())}):a.trigger("success",n())}var a=this,o={json:t,buffers:[],bufferViews:[],materials:[],textures:[],meshes:[],joints:[],skeletons:[],cameras:[],nodes:[],clips:[]},s=this.rootNode||new sn,u=0;return e?(o.buffers=e.slice(),i(!0)):Fe.each(t.buffers,function(t,e){u++;var n=t.uri;a._loadBuffers(n,function(t){o.buffers[e]=t,r()},r)}),n()},resolveBufferPath:function(t){if(t&&t.match(/^data:(.*?)base64,/))return t;var e=this.bufferRootPath;return null==e&&(e=this.rootPath),Fe.relative2absolute(t,e)},resolveTexturePath:function(t){if(t&&t.match(/^data:(.*?)base64,/))return t;var e=this.textureRootPath;return null==e&&(e=this.rootPath),Fe.relative2absolute(t,e)},loadBuffer:function(t,e,r){Ve.request.get({url:t,responseType:"arraybuffer",onload:function(t){e&&e(t)},onerror:function(t){r&&r(t)}})},_getShader:function(){return"string"==typeof this.shader?this.shaderLibrary.get(this.shader):this.shader instanceof Q?this.shader:void 0},_loadBuffers:function(t,e,r){var n="data:application/octet-stream;base64,";t.substr(0,n.length)===n?e(Et(t,n.length)):this.loadBuffer(this.resolveBufferPath(t),e,r)},_parseSkins:function(t,e){function r(t,e,r){t.skeleton=e,t.joints=r,e.boundingBox||e.updateJointsBoundingBoxes(t.geometry)}function n(t){return t.index}Fe.each(t.skins,function(r,n){for(var i=new Xn({name:r.name}),a=0;a<r.joints.length;a++){var o=r.joints[a],s=e.nodes[o],u=new Vn({name:s.name,node:s,index:i.joints.length});i.joints.push(u)}if(i.relativeRootNode=e.nodes[r.skeleton]||this.rootNode,r.inverseBindMatrices){var l=t.accessors[r.inverseBindMatrices],h=e.bufferViews[l.bufferView],c=l.byteOffset||0,f=16*l.count,d=new Ve.Float32Array(h,c,f);i.setJointMatricesArray(d)}else i.updateJointMatrices();e.skeletons[n]=i},this),Fe.each(t.nodes,function(t,i){if(null!=t.skin){var a=t.skin,o=e.skeletons[a],s=e.nodes[i],u=o.joints.map(n);if(s instanceof Cn)r(s,o,u);else for(var l=s.children(),h=0;h<l.length;h++)r(l[h],o,u)}},this)},_parseTextures:function(t,e){Fe.each(t.textures,function(r,n){var i=t.samplers&&t.samplers[r.sampler]||{},a={};["wrapS","wrapT","magFilter","minFilter"].forEach(function(t){var e=i[t];null!=e&&(a[t]=e)}),Fe.defaults(a,{wrapS:Tn.REPEAT,wrapT:Tn.REPEAT,flipY:this.textureFlipY,convertToPOT:this.textureConvertToPOT});var o=r.target||Ye.TEXTURE_2D,s=r.format;if(null!=s&&(a.format=s),o===Ye.TEXTURE_2D){var u,l=new An(a),h=t.images[r.source];h.uri?u=this.resolveTexturePath(h.uri):null!=h.bufferView&&(u=URL.createObjectURL(new Blob([e.bufferViews[h.bufferView]],{type:h.mimeType}))),u&&(l.load(u,this.crossOrigin),e.textures[n]=l)}},this)},_KHRCommonMaterialToStandard:function(t,e){var r={};r=t.extensions.KHR_materials_common.values||{},"number"==typeof r.diffuse&&(r.diffuse=e.textures[r.diffuse]||null),"number"==typeof r.emission&&(r.emission=e.textures[r.emission]||null);var n=[];r.diffuse instanceof An&&n.push("diffuseMap"),t.normalTexture&&n.push("normalMap"),r.emission instanceof An&&n.push("emissiveMap");var i,a=this.useStandardMaterial;a?i=new Fn({name:t.name,doubleSided:t.doubleSided}):(i=new nr({name:t.name,shader:this._getShader()}),i.define("fragment","USE_ROUGHNESS"),i.define("fragment","USE_METALNESS"),t.doubleSided&&i.define("fragment","DOUBLE_SIDED")),r.transparent&&(i.depthMask=!1,i.depthTest=!0,i.transparent=!0);var o=r.diffuse;o&&(Array.isArray(o)?(o=o.slice(0,3),a?i.color=o:i.set("color",o)):a?i.diffuseMap=o:i.set("diffuseMap",o));var s=r.emission;if(null!=s&&(Array.isArray(s)?(s=s.slice(0,3),a?i.emission=s:i.set("emission",s)):a?i.emissiveMap=s:i.set("emissiveMap",s)),null!=t.normalTexture){var u=t.normalTexture.index;a?i.normalMap=e.textures[u]||null:i.set("normalMap",e.textures[u]||null)}if(null!=r.shininess){var l=Math.log(r.shininess)/Math.log(8192);i.set("glossiness",l),i.set("roughness",1-l)}else i.set("glossiness",.3),i.set("roughness",.3);return null!=r.specular&&i.set("specularColor",r.specular.slice(0,3)),null!=r.transparency&&i.set("alpha",r.transparency),i},_pbrMetallicRoughnessToStandard:function(t,e,r){var n,i,a,o,s,u,l,h="MASK"===t.alphaMode,c=this.useStandardMaterial,f=[],d=1;e.baseColorTexture&&(i=r.textures[e.baseColorTexture.index]||null)&&f.push("diffuseMap"),e.metallicRoughnessTexture&&(a=o=r.textures[e.metallicRoughnessTexture.index]||null)&&f.push("metalnessMap","roughnessMap"),t.normalTexture&&(s=r.textures[t.normalTexture.index]||null,s&&f.push("normalMap"),"number"==typeof t.normalTexture.scale&&(d=t.normalTexture.scale)),t.emissiveTexture&&(u=r.textures[t.emissiveTexture.index]||null)&&f.push("emissiveMap"),t.occlusionTexture&&(l=r.textures[t.occlusionTexture.index]||null)&&f.push("occlusionMap");var p=e.baseColorFactor||[1,1,1,1],m={diffuseMap:i||null,roughnessMap:a||null,metalnessMap:o||null,normalMap:s||null,occlusionMap:l||null,emissiveMap:u||null,color:p.slice(0,3),alpha:p[3],metalness:e.metallicFactor||0,roughness:e.roughnessFactor||0,emission:t.emissiveFactor||[0,0,0],emissionIntensity:1,alphaCutoff:t.alphaCutoff||0,normalScale:d};return m.roughnessMap&&(m.metalness=.5,m.roughness=.5),c?n=new Fn(Fe.extend({name:t.name,alphaTest:h,doubleSided:t.doubleSided,roughnessChannel:1,metalnessChannel:2},m)):(n=new nr({name:t.name,shader:this._getShader()}),n.define("fragment","USE_ROUGHNESS"),n.define("fragment","USE_METALNESS"),n.define("fragment","ROUGHNESS_CHANNEL",1),n.define("fragment","METALNESS_CHANNEL",2),n.define("fragment","DIFFUSEMAP_ALPHA_ALPHA"),h&&n.define("fragment","ALPHA_TEST"),t.doubleSided&&n.define("fragment","DOUBLE_SIDED"),n.set(m)),"BLEND"===t.alphaMode&&(n.depthMask=!1,n.depthTest=!0,n.transparent=!0),n},_pbrSpecularGlossinessToStandard:function(t,e,r){var n="MASK"===t.alphaMode;this.useStandardMaterial&&console.error("StandardMaterial doesn't support specular glossiness workflow yet");var i,a,o,s,u,l,h,c=[];e.diffuseTexture&&(a=r.textures[e.diffuseTexture.index]||null)&&c.push("diffuseMap"),e.specularGlossinessTexture&&(o=s=r.textures[e.specularGlossinessTexture.index]||null)&&c.push("specularMap","glossinessMap"),t.normalTexture&&(u=r.textures[t.normalTexture.index]||null)&&c.push("normalMap"),t.emissiveTexture&&(l=r.textures[t.emissiveTexture.index]||null)&&c.push("emissiveMap"),t.occlusionTexture&&(h=r.textures[t.occlusionTexture.index]||null)&&c.push("occlusionMap");var f=e.diffuseFactor||[1,1,1,1],d={diffuseMap:a||null,glossinessMap:o||null,specularMap:s||null,normalMap:u||null,emissiveMap:l||null,occlusionMap:h||null,color:f.slice(0,3),alpha:f[3],specularColor:e.specularFactor||[1,1,1],glossiness:e.glossinessFactor||0,emission:t.emissiveFactor||[0,0,0],emissionIntensity:1,alphaCutoff:null==t.alphaCutoff?.9:t.alphaCutoff};return d.glossinessMap&&(d.glossiness=.5),d.specularMap&&(d.specularColor=[1,1,1]),i=new nr({name:t.name,shader:this._getShader()}),i.define("fragment","GLOSSINESS_CHANNEL",3),i.define("fragment","DIFFUSEMAP_ALPHA_ALPHA"),n&&i.define("fragment","ALPHA_TEST"),t.doubleSided&&i.define("fragment","DOUBLE_SIDED"),i.set(d),"BLEND"===t.alphaMode&&(i.depthMask=!1,i.depthTest=!0,i.transparent=!0),i},_parseMaterials:function(t,e){Fe.each(t.materials,function(t,r){t.extensions&&t.extensions.KHR_materials_common?e.materials[r]=this._KHRCommonMaterialToStandard(t,e):t.extensions&&t.extensions.KHR_materials_pbrSpecularGlossiness?e.materials[r]=this._pbrSpecularGlossinessToStandard(t,t.extensions.KHR_materials_pbrSpecularGlossiness,e):e.materials[r]=this._pbrMetallicRoughnessToStandard(t,t.pbrMetallicRoughness||{},e)},this)},_parseMeshes:function(t,e){var r=this;Fe.each(t.meshes,function(n,i){e.meshes[i]=[];for(var a=0;a<n.primitives.length;a++){for(var o=n.primitives[a],s=new pn({dynamic:!1,name:n.name,boundingBox:new Vr}),u=Object.keys(o.attributes),l=0;l<u.length;l++){var h=u[l],c=o.attributes[h],f=t.accessors[c],d=Yn[h];if(d){var p=Zn[f.type],m=xt(t,e,c);if(m instanceof Ve.Uint32Array&&(m=new Float32Array(m)),"WEIGHTS_0"===h&&4===p){for(var _=new m.constructor(3*f.count),g=0;g<f.count;g++){var v=4*g,y=3*g,T=m[v],x=m[v+1],E=m[v+2],A=m[v+3],b=T+x+E+A;_[y]=T/b,_[y+1]=x/b,_[y+2]=E/b}s.attributes[d].value=_}else if("COLOR_0"===h&&3===p){for(var S=new m.constructor(4*f.count),g=0;g<f.count;g++){var v=4*g,y=3*g;S[v]=m[y],S[v+1]=m[y+1],S[v+2]=m[y+2],S[v+3]=1}s.attributes[d].value=S}else s.attributes[d].value=m;var M="float";if(m instanceof Ve.Uint16Array?M="ushort":m instanceof Ve.Int16Array?M="short":m instanceof Ve.Uint8Array?M="ubyte":m instanceof Ve.Int8Array&&(M="byte"),s.attributes[d].type=M,"POSITION"===h){var w=f.min,C=f.max;w&&s.boundingBox.min.set(w[0],w[1],w[2]),C&&s.boundingBox.max.set(C[0],C[1],C[2])}}}null!=o.indices&&(s.indices=xt(t,e,o.indices,!0),s.vertexCount<=65535&&s.indices instanceof Ve.Uint32Array&&(s.indices=new Ve.Uint16Array(s.indices)),s.indices instanceof Ve.Uint8Array&&(s.indices=new Ve.Uint16Array(s.indices)));var R=e.materials[o.material],L=(t.materials||[])[o.material];R||(R=new nr({shader:r._getShader()}));var N=new Cn({geometry:s,material:R,mode:[Cn.POINTS,Cn.LINES,Cn.LINE_LOOP,Cn.LINE_STRIP,Cn.TRIANGLES,Cn.TRIANGLE_STRIP,Cn.TRIANGLE_FAN][o.mode]||Cn.TRIANGLES,ignoreGBuffer:R.transparent});null!=L&&(N.culling=!L.doubleSided),N.geometry.attributes.normal.value||N.geometry.generateVertexNormals(),(R instanceof Fn&&R.normalMap||R.isTextureEnabled("normalMap"))&&(N.geometry.attributes.tangent.value||N.geometry.generateTangents()),N.geometry.attributes.color.value&&N.material.define("VERTEX_COLOR"),N.name=Jn.generateMeshName(t.meshes,i,a),e.meshes[i].push(N)}},this)},_instanceCamera:function(t,e){var r=t.cameras[e.camera];if("perspective"===r.type){var n=r.perspective||{};return new Rn({name:e.name,aspect:n.aspectRatio,fov:n.yfov/Math.PI*180,far:n.zfar,near:n.znear})}var i=r.orthographic||{};return new Ln({name:e.name,top:i.ymag,right:i.xmag,left:-i.xmag,bottom:-i.ymag,near:i.znear,far:i.zfar})},_parseNodes:function(t,e){function r(t){return new Cn({name:t.name,geometry:t.geometry,material:t.material,culling:t.culling,mode:t.mode})}e.instancedMeshes=[],Fe.each(t.nodes,function(n,i){var a;if(null!=n.camera&&this.includeCamera)a=this._instanceCamera(t,n),e.cameras.push(a);else if(null!=n.mesh&&this.includeMesh){var o=e.meshes[n.mesh];if(o)if(1===o.length)a=r(o[0]),a.setName(n.name),e.instancedMeshes.push(a);else{a=new zr,a.setName(n.name);for(var s=0;s<o.length;s++){var u=r(o[s]);a.add(u),e.instancedMeshes.push(u)}}}else a=new zr,a.setName(n.name);n.matrix?(a.localTransform.setArray(n.matrix),a.decomposeLocalTransform()):(n.translation&&a.position.setArray(n.translation),n.rotation&&a.rotation.setArray(n.rotation),n.scale&&a.scale.setArray(n.scale)),e.nodes[i]=a},this),Fe.each(t.nodes,function(t,r){var n=e.nodes[r];if(t.children)for(var i=0;i<t.children.length;i++){var a=t.children[i],o=e.nodes[a];n.add(o)}})},_parseAnimations:function(t,e){function r(t){return"weights"!==t.path||(console.warn("GLTFLoader not support morph targets yet."),!1)}function n(t,e){return t.target.node+"_"+e.samplers[t.sampler].input}var i={};Fe.each(t.animations,function(a,o){var s=a.channels.filter(r);if(s.length){for(var u={},l=0;l<s.length;l++){var h=s[l],c=n(h,a),f=e.nodes[h.target.node],d=u[c],p=a.samplers[h.sampler];if(!d){d=u[c]=new Pe({name:f?f.name:"",target:f}),d.targetNodeIndex=h.target.node,d.channels.time=xt(t,e,p.input);var m=d.channels.time.length;if(!i[p.input]){for(var _=0;_<m;_++)d.channels.time[_]*=1e3;i[p.input]=!0}}"LINEAR"!==(p.interpolation||"LINEAR")&&console.warn("GLTFLoader only support LINEAR interpolation.");var g=h.target.path;"translation"===g&&(g="position"),d.channels[g]=xt(t,e,p.output)}var v=[];for(var y in u)v.push(u[y]);var T=new Xe({name:a.name,loop:!0,tracks:v});e.clips.push(T)}},this);var a=e.clips.reduce(function(t,e){return Math.max(t,e.life)},0);return e.clips.forEach(function(t){t.life=a}),e.clips}});Jn.generateMeshName=function(t,e,r){var n=t[e],i=n.name||"mesh_"+e;return 0===r?i:i+"$"+r};var Qn=qr.extend({shadowBias:.001,shadowSlopeScale:2,shadowCascade:1,cascadeSplitLogFactor:.2},{type:"DIRECTIONAL_LIGHT",uniformTemplates:{directionalLightDirection:{type:"3f",value:function(t){return t.__dir=t.__dir||new Cr,t.__dir.copy(t.worldTransform.z).normalize().negate().array}},directionalLightColor:{type:"3f",value:function(t){var e=t.color,r=t.intensity;return[e[0]*r,e[1]*r,e[2]*r]}}},clone:function(){var t=qr.prototype.clone.call(this);return t.shadowBias=this.shadowBias,t.shadowSlopeScale=this.shadowSlopeScale,t}}),$n=qr.extend({range:100,castShadow:!1},{type:"POINT_LIGHT",uniformTemplates:{pointLightPosition:{type:"3f",value:function(t){return t.getWorldPosition().array}},pointLightRange:{type:"1f",value:function(t){return t.range}},pointLightColor:{type:"3f",value:function(t){var e=t.color,r=t.intensity;return[e[0]*r,e[1]*r,e[2]*r]}}},clone:function(){var t=qr.prototype.clone.call(this);return t.range=this.range,t}}),ti=qr.extend({range:20,umbraAngle:30,penumbraAngle:45,falloffFactor:2,shadowBias:.001,shadowSlopeScale:2},{type:"SPOT_LIGHT",uniformTemplates:{spotLightPosition:{type:"3f",value:function(t){return t.getWorldPosition().array}},spotLightRange:{type:"1f",value:function(t){return t.range}},spotLightUmbraAngleCosine:{type:"1f",value:function(t){return Math.cos(t.umbraAngle*Math.PI/180)}},spotLightPenumbraAngleCosine:{type:"1f",value:function(t){return Math.cos(t.penumbraAngle*Math.PI/180)}},spotLightFalloffFactor:{type:"1f",value:function(t){return t.falloffFactor}},spotLightDirection:{type:"3f",value:function(t){return t.__dir=t.__dir||new Cr,t.__dir.copy(t.worldTransform.z).negate().array}},spotLightColor:{type:"3f",value:function(t){var e=t.color,r=t.intensity;return[e[0]*r,e[1]*r,e[2]*r]}}},clone:function(){var t=qr.prototype.clone.call(this);return t.range=this.range,t.umbraAngle=this.umbraAngle,t.penumbraAngle=this.penumbraAngle,t.falloffFactor=this.falloffFactor,t.shadowBias=this.shadowBias,t.shadowSlopeScale=this.shadowSlopeScale,t}}),ei=qr.extend({castShadow:!1},{type:"AMBIENT_LIGHT",uniformTemplates:{ambientLightColor:{type:"3f",value:function(t){var e=t.color,r=t.intensity;return[e[0]*r,e[1]*r,e[2]*r]}}}}),ri=Ye.FRAMEBUFFER,ni=Ye.RENDERBUFFER,ii=Ye.DEPTH_ATTACHMENT,ai=Ye.COLOR_ATTACHMENT0,oi=ke.extend({depthBuffer:!0,viewport:null,_width:0,_height:0,_textures:null,_boundRenderer:null},function(){this._cache=new un,this._textures={}},{getTextureWidth:function(){return this._width},getTextureHeight:function(){return this._height},bind:function(t){if(t.__currentFrameBuffer){if(t.__currentFrameBuffer===this)return;console.warn("Renderer already bound with another framebuffer. Unbind it first")}t.__currentFrameBuffer=this;var e=t.gl;e.bindFramebuffer(ri,this._getFrameBufferGL(t)),this._boundRenderer=t;var r=this._cache;r.put("viewport",t.viewport);var n,i,a=!1;for(var o in this._textures){a=!0;var s=this._textures[o];s&&(n=s.texture.width,i=s.texture.height,this._doAttach(t,s.texture,o,s.target))}this._width=n,this._height=i,!a&&this.depthBuffer&&console.error("Must attach texture before bind, or renderbuffer may have incorrect width and height."),this.viewport?t.setViewport(this.viewport):t.setViewport(0,0,n,i,1);var u=r.get("attached_textures");if(u)for(var o in u)if(!this._textures[o]){var l=u[o];this._doDetach(e,o,l)}if(!r.get("depthtexture_attached")&&this.depthBuffer){r.miss("renderbuffer")&&r.put("renderbuffer",e.createRenderbuffer());var h=r.get("renderbuffer");n===r.get("renderbuffer_width")&&i===r.get("renderbuffer_height")||(e.bindRenderbuffer(ni,h),e.renderbufferStorage(ni,e.DEPTH_COMPONENT16,n,i),r.put("renderbuffer_width",n),r.put("renderbuffer_height",i),e.bindRenderbuffer(ni,null)),r.get("renderbuffer_attached")||(e.framebufferRenderbuffer(ri,ii,ni,h),r.put("renderbuffer_attached",!0))}},unbind:function(t){t.__currentFrameBuffer=null,t.gl.bindFramebuffer(ri,null),this._boundRenderer=null,this._cache.use(t.__uid__);var e=this._cache.get("viewport");e&&t.setViewport(e),this.updateMipmap(t)},updateMipmap:function(t){var e=t.gl;for(var r in this._textures){var n=this._textures[r];if(n){var i=n.texture;if(!i.NPOT&&i.useMipmap&&i.minFilter===Tn.LINEAR_MIPMAP_LINEAR){var a="textureCube"===i.textureType?Ye.TEXTURE_CUBE_MAP:Ye.TEXTURE_2D;e.bindTexture(a,i.getWebGLTexture(t)),e.generateMipmap(a),e.bindTexture(a,null)}}}},checkStatus:function(t){return t.checkFramebufferStatus(ri)},_getFrameBufferGL:function(t){var e=this._cache;return e.use(t.__uid__),e.miss("framebuffer")&&e.put("framebuffer",t.gl.createFramebuffer()),e.get("framebuffer")},attach:function(t,e,r){if(!t.width)throw new Error("The texture attached to color buffer is not a valid.");e=e||ai,r=r||Ye.TEXTURE_2D;var n,i=this._boundRenderer,a=i&&i.gl;if(a){var o=this._cache;o.use(i.__uid__),n=o.get("attached_textures")}var s=this._textures[e];if(!s||s.target!==r||s.texture!==t||!n||null==n[e]){var u=!0;i&&(u=this._doAttach(i,t,e,r),this.viewport||i.setViewport(0,0,t.width,t.height,1)),u&&(this._textures[e]=this._textures[e]||{},this._textures[e].texture=t,this._textures[e].target=r)}},_doAttach:function(t,e,r,n){var i=t.gl,a=e.getWebGLTexture(t),o=this._cache.get("attached_textures");if(o&&o[r]){var s=o[r];if(s.texture===e&&s.target===n)return}r=+r;var u=!0;if(r===ii||r===Ye.DEPTH_STENCIL_ATTACHMENT){if(t.getGLExtension("WEBGL_depth_texture")||(console.error("Depth texture is not supported by the browser"),u=!1),e.format!==Ye.DEPTH_COMPONENT&&e.format!==Ye.DEPTH_STENCIL&&(console.error("The texture attached to depth buffer is not a valid."),u=!1),u){var l=this._cache.get("renderbuffer");l&&(i.framebufferRenderbuffer(ri,ii,ni,null),i.deleteRenderbuffer(l),this._cache.put("renderbuffer",!1)),this._cache.put("renderbuffer_attached",!1),this._cache.put("depthtexture_attached",!0)}}return i.framebufferTexture2D(ri,r,n,a,0),o||(o={},this._cache.put("attached_textures",o)),o[r]=o[r]||{},o[r].texture=e,o[r].target=n,u},_doDetach:function(t,e,r){t.framebufferTexture2D(ri,e,r,null,0);var n=this._cache.get("attached_textures");n&&n[e]&&(n[e]=null),e!==ii&&e!==Ye.DEPTH_STENCIL_ATTACHMENT||this._cache.put("depthtexture_attached",!1)},detach:function(t,e){if(this._textures[t]=null,this._boundRenderer){this._cache.use(this._boundRenderer.__uid__),this._doDetach(this._boundRenderer.gl,t,e)}},dispose:function(t){var e=t.gl,r=this._cache;r.use(t.__uid__);var n=r.get("renderbuffer");n&&e.deleteRenderbuffer(n);var i=r.get("framebuffer");i&&e.deleteFramebuffer(i),r.deleteContext(t.__uid__),this._textures={}}});oi.DEPTH_ATTACHMENT=ii,oi.COLOR_ATTACHMENT0=ai,oi.STENCIL_ATTACHMENT=Ye.STENCIL_ATTACHMENT,oi.DEPTH_STENCIL_ATTACHMENT=Ye.DEPTH_STENCIL_ATTACHMENT;Q.import("\n@export clay.compositor.vertex\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\nattribute vec3 position : POSITION;\nattribute vec2 texcoord : TEXCOORD_0;\nvarying vec2 v_Texcoord;\nvoid main()\n{\n v_Texcoord = texcoord;\n gl_Position = worldViewProjection * vec4(position, 1.0);\n}\n@end");var si=new mn,ui=new Cn({geometry:si,frustumCulling:!1
}),li=new Ln,hi=ke.extend(function(){return{fragment:"",outputs:null,material:null,blendWithPrevious:!1,clearColor:!1,clearDepth:!0}},function(){var t=new Q(Q.source("clay.compositor.vertex"),this.fragment),e=new nr({shader:t});e.enableTexturesAll(),this.material=e},{setUniform:function(t,e){this.material.setUniform(t,e)},getUniform:function(t){var e=this.material.uniforms[t];if(e)return e.value},attachOutput:function(t,e){this.outputs||(this.outputs={}),e=e||Ye.COLOR_ATTACHMENT0,this.outputs[e]=t},detachOutput:function(t){for(var e in this.outputs)this.outputs[e]===t&&(this.outputs[e]=null)},bind:function(t,e){if(this.outputs)for(var r in this.outputs){var n=this.outputs[r];n&&e.attach(n,r)}e&&e.bind(t)},unbind:function(t,e){e.unbind(t)},render:function(t,e){var r=t.gl;if(e){this.bind(t,e);var n=t.getGLExtension("EXT_draw_buffers");if(n&&this.outputs){var i=[];for(var a in this.outputs)(a=+a)>=r.COLOR_ATTACHMENT0&&a<=r.COLOR_ATTACHMENT0+8&&i.push(a);n.drawBuffersEXT(i)}}this.trigger("beforerender",this,t);var o=this.clearDepth?r.DEPTH_BUFFER_BIT:0;if(r.depthMask(!0),this.clearColor){o|=r.COLOR_BUFFER_BIT,r.colorMask(!0,!0,!0,!0);var s=this.clearColor;Array.isArray(s)&&r.clearColor(s[0],s[1],s[2],s[3])}r.clear(o),this.blendWithPrevious?(r.enable(r.BLEND),this.material.transparent=!0):(r.disable(r.BLEND),this.material.transparent=!1),this.renderQuad(t),this.trigger("afterrender",this,t),e&&this.unbind(t,e)},renderQuad:function(t){ui.material=this.material,t.renderPass([ui],li)},dispose:function(t){}});Q.import(qn);var ci=Cn.extend(function(){var t=new Q({vertex:Q.source("clay.skybox.vertex"),fragment:Q.source("clay.skybox.fragment")}),e=new nr({shader:t,depthMask:!1});return{scene:null,geometry:new gn,material:e,environmentMap:null,culling:!1,_dummyCamera:new Rn}},function(){var t=this.scene;t&&this.attachScene(t),this.environmentMap&&this.setEnvironmentMap(this.environmentMap)},{attachScene:function(t){this.scene&&this.detachScene(),t.skybox=this,this.scene=t,t.on("beforerender",this._beforeRenderScene,this)},detachScene:function(){this.scene&&(this.scene.off("beforerender",this._beforeRenderScene),this.scene.skybox=null),this.scene=null},dispose:function(t){this.detachScene(),this.geometry.dispose(t)},setEnvironmentMap:function(t){"texture2D"===t.textureType?(this.material.define("EQUIRECTANGULAR"),t.minFilter=Tn.LINEAR):this.material.undefine("EQUIRECTANGULAR"),this.material.set("environmentMap",t)},getEnvironmentMap:function(){return this.material.get("environmentMap")},_beforeRenderScene:function(t,e,r){this.renderSkybox(t,r)},renderSkybox:function(t,e){var r=this._dummyCamera;r.aspect=t.getViewportAspect(),r.fov=e.fov||50,r.updateProjectionMatrix(),Ur.invert(r.invProjectionMatrix,r.projectionMatrix),r.worldTransform.copy(e.worldTransform),r.viewMatrix.copy(e.viewMatrix),this.position.copy(e.getWorldPosition()),this.update(),t.gl.disable(t.gl.BLEND),this.material.get("lod")>0?this.material.define("fragment","LOD"):this.material.undefine("fragment","LOD"),t.renderPass([this],r)}}),fi=["px","nx","py","ny","pz","nz"],di=ke.extend(function(){var t={position:new Cr,far:1e3,near:.1,texture:null,shadowMapPass:null},e=t._cameras={px:new Rn({fov:90}),nx:new Rn({fov:90}),py:new Rn({fov:90}),ny:new Rn({fov:90}),pz:new Rn({fov:90}),nz:new Rn({fov:90})};return e.px.lookAt(Cr.POSITIVE_X,Cr.NEGATIVE_Y),e.nx.lookAt(Cr.NEGATIVE_X,Cr.NEGATIVE_Y),e.py.lookAt(Cr.POSITIVE_Y,Cr.POSITIVE_Z),e.ny.lookAt(Cr.NEGATIVE_Y,Cr.NEGATIVE_Z),e.pz.lookAt(Cr.POSITIVE_Z,Cr.NEGATIVE_Y),e.nz.lookAt(Cr.NEGATIVE_Z,Cr.NEGATIVE_Y),t._frameBuffer=new oi,t},{getCamera:function(t){return this._cameras[t]},render:function(t,e,r){var n=t.gl;r||e.update();for(var i=this.texture.width,a=2*Math.atan(i/(i-.5))/Math.PI*180,o=0;o<6;o++){var s=fi[o],u=this._cameras[s];if(Cr.copy(u.position,this.position),u.far=this.far,u.near=this.near,u.fov=a,this.shadowMapPass){u.update();var l=e.getBoundingBox();l.applyTransform(u.viewMatrix),e.viewBoundingBoxLastFrame.copy(l),this.shadowMapPass.render(t,e,u,!0)}this._frameBuffer.attach(this.texture,n.COLOR_ATTACHMENT0,n.TEXTURE_CUBE_MAP_POSITIVE_X+o),this._frameBuffer.bind(t),t.render(e,u,!0),this._frameBuffer.unbind(t)}},dispose:function(t){this._frameBuffer.dispose(t)}}),pi=At("DXT1"),mi=At("DXT3"),_i=At("DXT5"),gi={parse:function(t,e){var r=new Int32Array(t,0,31);if(542327876!==r[0])return null;if(4&!r(20))return null;var n,i,a=r(21),o=r[4],s=r[3],u=512&r[28],l=131072&r[2];switch(a){case pi:n=8,i=Tn.COMPRESSED_RGB_S3TC_DXT1_EXT;break;case mi:n=16,i=Tn.COMPRESSED_RGBA_S3TC_DXT3_EXT;break;case _i:n=16,i=Tn.COMPRESSED_RGBA_S3TC_DXT5_EXT;break;default:return null}var h=r[1]+4,c=u?6:1,f=1;l&&(f=Math.max(1,r[7]));for(var d=[],p=0;p<c;p++){var m=o,_=s;d[p]=new An({width:m,height:_,format:i});for(var g=[],v=0;v<f;v++){var y=Math.max(4,m)/4*Math.max(4,_)/4*n,T=new Uint8Array(t,h,y);h+=y,m*=.5,_*=.5,g[v]=T}d[p].pixels=g[0],l&&(d[p].mipmaps=g)}if(!e)return d[0];e.width=d[0].width,e.height=d[0].height,e.format=d[0].format,e.pixels=d[0].pixels,e.mipmaps=d[0].mipmaps}},vi=String.fromCharCode,yi=8,Ti=32767,xi={parseRGBE:function(t,e,r){null==r&&(r=0);var n=new Uint8Array(t),i=n.length;if("#?"===St(n,0,2)){for(var a=2;a<i&&("\n"!==vi(n[a])||"\n"!==vi(n[a+1]));a++);if(!(a>=i)){a+=2;for(var o="";a<i;a++){var s=vi(n[a]);if("\n"===s)break;o+=s}var u=o.split(" "),l=parseInt(u[1]),h=parseInt(u[3]);if(h&&l){for(var c=a+1,f=[],d=0;d<h;d++){f[d]=[];for(var p=0;p<4;p++)f[d][p]=0}for(var m=new Float32Array(h*l*4),_=0,g=0;g<l;g++){var c=Ct(f,n,c,h);if(!c)return null;for(var d=0;d<h;d++)bt(f[d],m,_,r),_+=4}return e||(e=new An),e.width=h,e.height=l,e.pixels=m,e.type=Tn.FLOAT,e}}}},parseRGBEFromPNG:function(t){}},Ei={loadTexture:function(t,e,r,n){var i;if("function"==typeof e?(r=e,n=r,e={}):e=e||{},"string"==typeof t){if(t.match(/.hdr$/)||"hdr"===e.fileType)return i=new An({width:0,height:0,sRGB:!1}),Ei._fetchTexture(t,function(t){xi.parseRGBE(t,i,e.exposure),i.dirty(),r&&r(i)},n),i;t.match(/.dds$/)||"dds"===e.fileType?(i=new An({width:0,height:0}),Ei._fetchTexture(t,function(t){gi.parse(t,i),i.dirty(),r&&r(i)},n)):(i=new An,i.load(t),i.success(r),i.error(n))}else"object"==typeof t&&void 0!==t.px&&(i=new Mn,i.load(t),i.success(r),i.error(n));return i},loadPanorama:function(t,e,r,n,i,a){var o=this;"function"==typeof n?(i=n,a=i,n={}):n=n||{},Ei.loadTexture(e,n,function(e){e.flipY=n.flipY||!1,o.panoramaToCubeMap(t,e,r,n),e.dispose(t),i&&i(r)},a)},panoramaToCubeMap:function(t,e,r,n){var i=new di,a=new ci({scene:new sn});return a.setEnvironmentMap(e),n=n||{},n.encodeRGBM&&a.material.define("fragment","RGBM_ENCODE"),r.sRGB=e.sRGB,i.texture=r,i.render(t,a.scene),i.texture=null,i.dispose(t),r},heightToNormal:function(t,e){var r=document.createElement("canvas"),n=r.width=t.width,i=r.height=t.height,a=r.getContext("2d");a.drawImage(t,0,0,n,i),e=e||!1;for(var o=a.getImageData(0,0,n,i),s=a.createImageData(n,i),u=0;u<o.data.length;u+=4){if(e){var l=o.data[u],h=o.data[u+1],c=o.data[u+2];if(Math.abs(l-h)+Math.abs(h-c)>20)return console.warn("Given image is not a height map"),t}var f,d,p,m;u%(4*n)==0?(f=o.data[u],p=o.data[u+4]):u%(4*n)==4*(n-1)?(f=o.data[u-4],p=o.data[u]):(f=o.data[u-4],p=o.data[u+4]),u<4*n?(d=o.data[u],m=o.data[u+4*n]):u>n*(i-1)*4?(d=o.data[u-4*n],m=o.data[u]):(d=o.data[u-4*n],m=o.data[u+4*n]),s.data[u]=f-p+127,s.data[u+1]=d-m+127,s.data[u+2]=255,s.data[u+3]=255}return a.putImageData(s,0,0),r},isHeightImage:function(t,e,r){if(!t||!t.width||!t.height)return!1;var n=document.createElement("canvas"),i=n.getContext("2d"),a=e||32;r=r||20,n.width=n.height=a,i.drawImage(t,0,0,a,a);for(var o=i.getImageData(0,0,a,a),s=0;s<o.data.length;s+=4){var u=o.data[s],l=o.data[s+1],h=o.data[s+2];if(Math.abs(u-l)+Math.abs(l-h)>r)return!1}return!0},_fetchTexture:function(t,e,r){Ve.request.get({url:t,responseType:"arraybuffer",onload:e,onerror:r})},createChessboard:function(t,e,r,n){t=t||512,e=e||64,r=r||"black",n=n||"white";var i=Math.ceil(t/e),a=document.createElement("canvas");a.width=t,a.height=t;var o=a.getContext("2d");o.fillStyle=n,o.fillRect(0,0,t,t),o.fillStyle=r;for(var s=0;s<i;s++)for(var u=0;u<i;u++){var l=u%2?s%2:s%2-1;l&&o.fillRect(s*e,u*e,e,e)}return new An({image:a,anisotropic:8})},createBlank:function(t){var e=document.createElement("canvas");e.width=1,e.height=1;var r=e.getContext("2d");return r.fillStyle=t,r.fillRect(0,0,1,1),new An({image:e})}},Ai={},bi=["px","nx","py","ny","pz","nz"];Ai.prefilterEnvironmentMap=function(t,e,r,n,i){i&&n||(n=Ai.generateNormalDistribution(),i=Ai.integrateBRDF(t,n)),r=r||{};var a=r.width||64,o=r.height||64,s=r.type||e.type,u=new Mn({width:a,height:o,type:s,flipY:!1,mipmaps:[]});u.isPowerOfTwo()||console.warn("Width and height must be power of two to enable mipmap.");var l=Math.min(a,o),h=Math.log(l)/Math.log(2)+1,c=new nr({shader:new Q({vertex:Q.source("clay.skybox.vertex"),fragment:"#define SHADER_NAME prefilter\n#define SAMPLE_NUMBER 1024\n#define PI 3.14159265358979\nuniform mat4 viewInverse : VIEWINVERSE;\nuniform samplerCube environmentMap;\nuniform sampler2D normalDistribution;\nuniform float roughness : 0.5;\nvarying vec2 v_Texcoord;\nvarying vec3 v_WorldPosition;\n@import clay.util.rgbm\nvec3 importanceSampleNormal(float i, float roughness, vec3 N) {\n vec3 H = texture2D(normalDistribution, vec2(roughness, i)).rgb;\n vec3 upVector = abs(N.y) > 0.999 ? vec3(1.0, 0.0, 0.0) : vec3(0.0, 1.0, 0.0);\n vec3 tangentX = normalize(cross(N, upVector));\n vec3 tangentZ = cross(N, tangentX);\n return normalize(tangentX * H.x + N * H.y + tangentZ * H.z);\n}\nvoid main() {\n vec3 eyePos = viewInverse[3].xyz;\n vec3 V = normalize(v_WorldPosition - eyePos);\n vec3 N = V;\n vec3 prefilteredColor = vec3(0.0);\n float totalWeight = 0.0;\n float fMaxSampleNumber = float(SAMPLE_NUMBER);\n for (int i = 0; i < SAMPLE_NUMBER; i++) {\n vec3 H = importanceSampleNormal(float(i) / fMaxSampleNumber, roughness, N);\n vec3 L = reflect(-V, H);\n float NoL = clamp(dot(N, L), 0.0, 1.0);\n if (NoL > 0.0) {\n prefilteredColor += decodeHDR(textureCube(environmentMap, L)).rgb * NoL;\n totalWeight += NoL;\n }\n }\n gl_FragColor = encodeHDR(vec4(prefilteredColor / totalWeight, 1.0));\n}\n"})});c.set("normalDistribution",n),r.encodeRGBM&&c.define("fragment","RGBM_ENCODE"),r.decodeRGBM&&c.define("fragment","RGBM_DECODE");var f,d=new sn;if("texture2D"===e.textureType){var p=new Mn({width:a,height:o,type:s===Tn.FLOAT?Tn.HALF_FLOAT:s});Ei.panoramaToCubeMap(t,e,p,{encodeRGBM:r.decodeRGBM}),e=p}f=new ci({scene:d,material:c}),f.material.set("environmentMap",e);var m=new di({texture:u});r.encodeRGBM&&(s=u.type=Tn.UNSIGNED_BYTE);for(var _=new An({width:a,height:o,type:s}),g=new oi({depthBuffer:!1}),v=Ve[s===Tn.UNSIGNED_BYTE?"Uint8Array":"Float32Array"],y=0;y<h;y++){u.mipmaps[y]={pixels:{}},f.material.set("roughness",y/(h-1));for(var T=_.width,x=2*Math.atan(T/(T-.5))/Math.PI*180,E=0;E<bi.length;E++){var A=new v(_.width*_.height*4);g.attach(_),g.bind(t);var b=m.getCamera(bi[E]);b.fov=x,t.render(d,b),t.gl.readPixels(0,0,_.width,_.height,Tn.RGBA,s,A),g.unbind(t),u.mipmaps[y].pixels[bi[E]]=A}_.width/=2,_.height/=2,_.dirty()}return g.dispose(t),_.dispose(t),f.dispose(t),n.dispose(t),{environmentMap:u,brdfLookup:i,normalDistribution:n,maxMipmapLevel:h}},Ai.integrateBRDF=function(t,e){e=e||Ai.generateNormalDistribution();var r=new oi({depthBuffer:!1}),n=new hi({fragment:"#define SAMPLE_NUMBER 1024\n#define PI 3.14159265358979\nuniform sampler2D normalDistribution;\nuniform vec2 viewportSize : [512, 256];\nconst vec3 N = vec3(0.0, 0.0, 1.0);\nconst float fSampleNumber = float(SAMPLE_NUMBER);\nvec3 importanceSampleNormal(float i, float roughness, vec3 N) {\n vec3 H = texture2D(normalDistribution, vec2(roughness, i)).rgb;\n vec3 upVector = abs(N.y) > 0.999 ? vec3(1.0, 0.0, 0.0) : vec3(0.0, 1.0, 0.0);\n vec3 tangentX = normalize(cross(N, upVector));\n vec3 tangentZ = cross(N, tangentX);\n return normalize(tangentX * H.x + N * H.y + tangentZ * H.z);\n}\nfloat G_Smith(float roughness, float NoV, float NoL) {\n float k = roughness * roughness / 2.0;\n float G1V = NoV / (NoV * (1.0 - k) + k);\n float G1L = NoL / (NoL * (1.0 - k) + k);\n return G1L * G1V;\n}\nvoid main() {\n vec2 uv = gl_FragCoord.xy / viewportSize;\n float NoV = uv.x;\n float roughness = uv.y;\n vec3 V;\n V.x = sqrt(1.0 - NoV * NoV);\n V.y = 0.0;\n V.z = NoV;\n float A = 0.0;\n float B = 0.0;\n for (int i = 0; i < SAMPLE_NUMBER; i++) {\n vec3 H = importanceSampleNormal(float(i) / fSampleNumber, roughness, N);\n vec3 L = reflect(-V, H);\n float NoL = clamp(L.z, 0.0, 1.0);\n float NoH = clamp(H.z, 0.0, 1.0);\n float VoH = clamp(dot(V, H), 0.0, 1.0);\n if (NoL > 0.0) {\n float G = G_Smith(roughness, NoV, NoL);\n float G_Vis = G * VoH / (NoH * NoV);\n float Fc = pow(1.0 - VoH, 5.0);\n A += (1.0 - Fc) * G_Vis;\n B += Fc * G_Vis;\n }\n }\n gl_FragColor = vec4(vec2(A, B) / fSampleNumber, 0.0, 1.0);\n}\n"}),i=new An({width:512,height:256,type:Tn.HALF_FLOAT,wrapS:Tn.CLAMP_TO_EDGE,wrapT:Tn.CLAMP_TO_EDGE,minFilter:Tn.NEAREST,magFilter:Tn.NEAREST,useMipmap:!1});return n.setUniform("normalDistribution",e),n.setUniform("viewportSize",[512,256]),n.attachOutput(i),n.render(t,r),r.dispose(t),i},Ai.generateNormalDistribution=function(t,e){for(var t=t||256,e=e||1024,r=new An({width:t,height:e,type:Tn.FLOAT,minFilter:Tn.NEAREST,magFilter:Tn.NEAREST,wrapS:Tn.CLAMP_TO_EDGE,wrapT:Tn.CLAMP_TO_EDGE,useMipmap:!1}),n=new Float32Array(e*t*4),i=[],a=0;a<t;a++){for(var o=a/t,s=o*o,u=0;u<e;u++){var l=(u<<16|u>>>16)>>>0;l=((1431655765&l)<<1|(2863311530&l)>>>1)>>>0,l=((858993459&l)<<2|(3435973836&l)>>>2)>>>0,l=((252645135&l)<<4|(4042322160&l)>>>4)>>>0,l=(((16711935&l)<<8|(4278255360&l)>>>8)>>>0)/4294967296;var h=Math.sqrt((1-l)/(1+(s*s-1)*l));i[u]=h}for(var u=0;u<e;u++){var c=4*(u*t+a),h=i[u],f=Math.sqrt(1-h*h),d=u/e,p=2*Math.PI*d;n[c]=f*Math.cos(p),n[c+1]=h,n[c+2]=f*Math.sin(p),n[c+3]=1}}return r.pixels=n,r};var Si=qr.extend({cubemap:null,castShadow:!1,_normalDistribution:null,_brdfLookup:null},{type:"AMBIENT_CUBEMAP_LIGHT",prefilter:function(t,e){if(!t.getGLExtension("EXT_shader_texture_lod"))return void console.warn("Device not support textureCubeLodEXT");this._brdfLookup||(this._normalDistribution=Ai.generateNormalDistribution(),this._brdfLookup=Ai.integrateBRDF(t,this._normalDistribution));var r=this.cubemap;if(!r.__prefiltered){var n=Ai.prefilterEnvironmentMap(t,r,{encodeRGBM:!0,width:e,height:e},this._normalDistribution,this._brdfLookup);this.cubemap=n.environmentMap,this.cubemap.__prefiltered=!0,r.dispose(t)}},getBRDFLookup:function(){return this._brdfLookup},uniformTemplates:{ambientCubemapLightColor:{type:"3f",value:function(t){var e=t.color,r=t.intensity;return[e[0]*r,e[1]*r,e[2]*r]}},ambientCubemapLightCubemap:{type:"t",value:function(t){return t.cubemap}},ambientCubemapLightBRDFLookup:{type:"t",value:function(t){return t._brdfLookup}}}}),Mi=qr.extend({castShadow:!1,coefficients:[]},function(){this._coefficientsTmpArr=new Ve.Float32Array(27)},{type:"AMBIENT_SH_LIGHT",uniformTemplates:{ambientSHLightColor:{type:"3f",value:function(t){var e=t.color,r=t.intensity;return[e[0]*r,e[1]*r,e[2]*r]}},ambientSHLightCoefficients:{type:"3f",value:function(t){for(var e=t._coefficientsTmpArr,r=0;r<t.coefficients.length;r++)e[r]=t.coefficients[r];return e}}}}),wi=function(){this._pool={},this._allocatedTextures=[]};wi.prototype={constructor:wi,get:function(t){var e=Rt(t);this._pool.hasOwnProperty(e)||(this._pool[e]=[]);var r=this._pool[e];if(!r.length){var n=new An(t);return this._allocatedTextures.push(n),n}return r.pop()},put:function(t){var e=Rt(t);this._pool.hasOwnProperty(e)||(this._pool[e]=[]),this._pool[e].push(t)},clear:function(t){for(var e=0;e<this._allocatedTextures.length;e++)this._allocatedTextures[e].dispose(t);this._pool={},this._allocatedTextures=[]}};var Ci={width:512,height:512,type:Ye.UNSIGNED_BYTE,format:Ye.RGBA,wrapS:Ye.CLAMP_TO_EDGE,wrapT:Ye.CLAMP_TO_EDGE,minFilter:Ye.LINEAR_MIPMAP_LINEAR,magFilter:Ye.LINEAR,useMipmap:!0,anisotropic:1,flipY:!0,unpackAlignment:4,premultiplyAlpha:!1},Ri=Object.keys(Ci),Li=["px","nx","py","ny","pz","nz"];Q.import("@export clay.sm.depth.vertex\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\nattribute vec3 position : POSITION;\nattribute vec2 texcoord : TEXCOORD_0;\nuniform vec2 uvRepeat = vec2(1.0, 1.0);\nuniform vec2 uvOffset = vec2(0.0, 0.0);\n@import clay.chunk.skinning_header\n@import clay.chunk.instancing_header\nvarying vec4 v_ViewPosition;\nvarying vec2 v_Texcoord;\nvoid main(){\n vec4 P = vec4(position, 1.0);\n#ifdef SKINNING\n @import clay.chunk.skin_matrix\n P = skinMatrixWS * P;\n#endif\n#ifdef INSTANCING\n @import clay.chunk.instancing_matrix\n P = instanceMat * P;\n#endif\n v_ViewPosition = worldViewProjection * P;\n gl_Position = v_ViewPosition;\n v_Texcoord = texcoord * uvRepeat + uvOffset;\n}\n@end\n@export clay.sm.depth.fragment\nvarying vec4 v_ViewPosition;\nvarying vec2 v_Texcoord;\nuniform float bias : 0.001;\nuniform float slopeScale : 1.0;\nuniform sampler2D alphaMap;\nuniform float alphaCutoff: 0.0;\n@import clay.util.encode_float\nvoid main(){\n float depth = v_ViewPosition.z / v_ViewPosition.w;\n if (alphaCutoff > 0.0) {\n if (texture2D(alphaMap, v_Texcoord).a <= alphaCutoff) {\n discard;\n }\n }\n#ifdef USE_VSM\n depth = depth * 0.5 + 0.5;\n float moment1 = depth;\n float moment2 = depth * depth;\n #ifdef SUPPORT_STANDARD_DERIVATIVES\n float dx = dFdx(depth);\n float dy = dFdy(depth);\n moment2 += 0.25*(dx*dx+dy*dy);\n #endif\n gl_FragColor = vec4(moment1, moment2, 0.0, 1.0);\n#else\n #ifdef SUPPORT_STANDARD_DERIVATIVES\n float dx = dFdx(depth);\n float dy = dFdy(depth);\n depth += sqrt(dx*dx + dy*dy) * slopeScale + bias;\n #else\n depth += bias;\n #endif\n gl_FragColor = encodeFloat(depth * 0.5 + 0.5);\n#endif\n}\n@end\n@export clay.sm.debug_depth\nuniform sampler2D depthMap;\nvarying vec2 v_Texcoord;\n@import clay.util.decode_float\nvoid main() {\n vec4 tex = texture2D(depthMap, v_Texcoord);\n#ifdef USE_VSM\n gl_FragColor = vec4(tex.rgb, 1.0);\n#else\n float depth = decodeFloat(tex);\n gl_FragColor = vec4(depth, depth, depth, 1.0);\n#endif\n}\n@end\n@export clay.sm.distance.vertex\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\nuniform mat4 world : WORLD;\nattribute vec3 position : POSITION;\n@import clay.chunk.skinning_header\nvarying vec3 v_WorldPosition;\nvoid main (){\n vec4 P = vec4(position, 1.0);\n#ifdef SKINNING\n @import clay.chunk.skin_matrix\n P = skinMatrixWS * P;\n#endif\n#ifdef INSTANCING\n @import clay.chunk.instancing_matrix\n P = instanceMat * P;\n#endif\n gl_Position = worldViewProjection * P;\n v_WorldPosition = (world * P).xyz;\n}\n@end\n@export clay.sm.distance.fragment\nuniform vec3 lightPosition;\nuniform float range : 100;\nvarying vec3 v_WorldPosition;\n@import clay.util.encode_float\nvoid main(){\n float dist = distance(lightPosition, v_WorldPosition);\n#ifdef USE_VSM\n gl_FragColor = vec4(dist, dist * dist, 0.0, 0.0);\n#else\n dist = dist / range;\n gl_FragColor = encodeFloat(dist);\n#endif\n}\n@end\n@export clay.plugin.shadow_map_common\n@import clay.util.decode_float\nfloat tapShadowMap(sampler2D map, vec2 uv, float z){\n vec4 tex = texture2D(map, uv);\n return step(z, decodeFloat(tex) * 2.0 - 1.0);\n}\nfloat pcf(sampler2D map, vec2 uv, float z, float textureSize, vec2 scale) {\n float shadowContrib = tapShadowMap(map, uv, z);\n vec2 offset = vec2(1.0 / textureSize) * scale;\n#ifdef PCF_KERNEL_SIZE\n for (int _idx_ = 0; _idx_ < PCF_KERNEL_SIZE; _idx_++) {{\n shadowContrib += tapShadowMap(map, uv + offset * pcfKernel[_idx_], z);\n }}\n return shadowContrib / float(PCF_KERNEL_SIZE + 1);\n#else\n shadowContrib += tapShadowMap(map, uv+vec2(offset.x, 0.0), z);\n shadowContrib += tapShadowMap(map, uv+vec2(offset.x, offset.y), z);\n shadowContrib += tapShadowMap(map, uv+vec2(-offset.x, offset.y), z);\n shadowContrib += tapShadowMap(map, uv+vec2(0.0, offset.y), z);\n shadowContrib += tapShadowMap(map, uv+vec2(-offset.x, 0.0), z);\n shadowContrib += tapShadowMap(map, uv+vec2(-offset.x, -offset.y), z);\n shadowContrib += tapShadowMap(map, uv+vec2(offset.x, -offset.y), z);\n shadowContrib += tapShadowMap(map, uv+vec2(0.0, -offset.y), z);\n return shadowContrib / 9.0;\n#endif\n}\nfloat pcf(sampler2D map, vec2 uv, float z, float textureSize) {\n return pcf(map, uv, z, textureSize, vec2(1.0));\n}\nfloat chebyshevUpperBound(vec2 moments, float z){\n float p = 0.0;\n z = z * 0.5 + 0.5;\n if (z <= moments.x) {\n p = 1.0;\n }\n float variance = moments.y - moments.x * moments.x;\n variance = max(variance, 0.0000001);\n float mD = moments.x - z;\n float pMax = variance / (variance + mD * mD);\n pMax = clamp((pMax-0.4)/(1.0-0.4), 0.0, 1.0);\n return max(p, pMax);\n}\nfloat computeShadowContrib(\n sampler2D map, mat4 lightVPM, vec3 position, float textureSize, vec2 scale, vec2 offset\n) {\n vec4 posInLightSpace = lightVPM * vec4(position, 1.0);\n posInLightSpace.xyz /= posInLightSpace.w;\n float z = posInLightSpace.z;\n if(all(greaterThan(posInLightSpace.xyz, vec3(-0.99, -0.99, -1.0))) &&\n all(lessThan(posInLightSpace.xyz, vec3(0.99, 0.99, 1.0)))){\n vec2 uv = (posInLightSpace.xy+1.0) / 2.0;\n #ifdef USE_VSM\n vec2 moments = texture2D(map, uv * scale + offset).xy;\n return chebyshevUpperBound(moments, z);\n #else\n return pcf(map, uv * scale + offset, z, textureSize, scale);\n #endif\n }\n return 1.0;\n}\nfloat computeShadowContrib(sampler2D map, mat4 lightVPM, vec3 position, float textureSize) {\n return computeShadowContrib(map, lightVPM, position, textureSize, vec2(1.0), vec2(0.0));\n}\nfloat computeShadowContribOmni(samplerCube map, vec3 direction, float range)\n{\n float dist = length(direction);\n vec4 shadowTex = textureCube(map, direction);\n#ifdef USE_VSM\n vec2 moments = shadowTex.xy;\n float variance = moments.y - moments.x * moments.x;\n float mD = moments.x - dist;\n float p = variance / (variance + mD * mD);\n if(moments.x + 0.001 < dist){\n return clamp(p, 0.0, 1.0);\n }else{\n return 1.0;\n }\n#else\n return step(dist, (decodeFloat(shadowTex) + 0.0002) * range);\n#endif\n}\n@end\n@export clay.plugin.compute_shadow_map\n#if defined(SPOT_LIGHT_SHADOWMAP_COUNT) || defined(DIRECTIONAL_LIGHT_SHADOWMAP_COUNT) || defined(POINT_LIGHT_SHADOWMAP_COUNT)\n#ifdef SPOT_LIGHT_SHADOWMAP_COUNT\nuniform sampler2D spotLightShadowMaps[SPOT_LIGHT_SHADOWMAP_COUNT]:unconfigurable;\nuniform mat4 spotLightMatrices[SPOT_LIGHT_SHADOWMAP_COUNT]:unconfigurable;\nuniform float spotLightShadowMapSizes[SPOT_LIGHT_SHADOWMAP_COUNT]:unconfigurable;\n#endif\n#ifdef DIRECTIONAL_LIGHT_SHADOWMAP_COUNT\n#if defined(SHADOW_CASCADE)\nuniform sampler2D directionalLightShadowMaps[1]:unconfigurable;\nuniform mat4 directionalLightMatrices[SHADOW_CASCADE]:unconfigurable;\nuniform float directionalLightShadowMapSizes[1]:unconfigurable;\nuniform float shadowCascadeClipsNear[SHADOW_CASCADE]:unconfigurable;\nuniform float shadowCascadeClipsFar[SHADOW_CASCADE]:unconfigurable;\n#else\nuniform sampler2D directionalLightShadowMaps[DIRECTIONAL_LIGHT_SHADOWMAP_COUNT]:unconfigurable;\nuniform mat4 directionalLightMatrices[DIRECTIONAL_LIGHT_SHADOWMAP_COUNT]:unconfigurable;\nuniform float directionalLightShadowMapSizes[DIRECTIONAL_LIGHT_SHADOWMAP_COUNT]:unconfigurable;\n#endif\n#endif\n#ifdef POINT_LIGHT_SHADOWMAP_COUNT\nuniform samplerCube pointLightShadowMaps[POINT_LIGHT_SHADOWMAP_COUNT]:unconfigurable;\n#endif\nuniform bool shadowEnabled : true;\n#ifdef PCF_KERNEL_SIZE\nuniform vec2 pcfKernel[PCF_KERNEL_SIZE];\n#endif\n@import clay.plugin.shadow_map_common\n#if defined(SPOT_LIGHT_SHADOWMAP_COUNT)\nvoid computeShadowOfSpotLights(vec3 position, inout float shadowContribs[SPOT_LIGHT_COUNT] ) {\n float shadowContrib;\n for(int _idx_ = 0; _idx_ < SPOT_LIGHT_SHADOWMAP_COUNT; _idx_++) {{\n shadowContrib = computeShadowContrib(\n spotLightShadowMaps[_idx_], spotLightMatrices[_idx_], position,\n spotLightShadowMapSizes[_idx_]\n );\n shadowContribs[_idx_] = shadowContrib;\n }}\n for(int _idx_ = SPOT_LIGHT_SHADOWMAP_COUNT; _idx_ < SPOT_LIGHT_COUNT; _idx_++){{\n shadowContribs[_idx_] = 1.0;\n }}\n}\n#endif\n#if defined(DIRECTIONAL_LIGHT_SHADOWMAP_COUNT)\n#ifdef SHADOW_CASCADE\nvoid computeShadowOfDirectionalLights(vec3 position, inout float shadowContribs[DIRECTIONAL_LIGHT_COUNT]){\n float depth = (2.0 * gl_FragCoord.z - gl_DepthRange.near - gl_DepthRange.far)\n / (gl_DepthRange.far - gl_DepthRange.near);\n float shadowContrib;\n shadowContribs[0] = 1.0;\n for (int _idx_ = 0; _idx_ < SHADOW_CASCADE; _idx_++) {{\n if (\n depth >= shadowCascadeClipsNear[_idx_] &&\n depth <= shadowCascadeClipsFar[_idx_]\n ) {\n shadowContrib = computeShadowContrib(\n directionalLightShadowMaps[0], directionalLightMatrices[_idx_], position,\n directionalLightShadowMapSizes[0],\n vec2(1.0 / float(SHADOW_CASCADE), 1.0),\n vec2(float(_idx_) / float(SHADOW_CASCADE), 0.0)\n );\n shadowContribs[0] = shadowContrib;\n }\n }}\n for(int _idx_ = DIRECTIONAL_LIGHT_SHADOWMAP_COUNT; _idx_ < DIRECTIONAL_LIGHT_COUNT; _idx_++) {{\n shadowContribs[_idx_] = 1.0;\n }}\n}\n#else\nvoid computeShadowOfDirectionalLights(vec3 position, inout float shadowContribs[DIRECTIONAL_LIGHT_COUNT]){\n float shadowContrib;\n for(int _idx_ = 0; _idx_ < DIRECTIONAL_LIGHT_SHADOWMAP_COUNT; _idx_++) {{\n shadowContrib = computeShadowContrib(\n directionalLightShadowMaps[_idx_], directionalLightMatrices[_idx_], position,\n directionalLightShadowMapSizes[_idx_]\n );\n shadowContribs[_idx_] = shadowContrib;\n }}\n for(int _idx_ = DIRECTIONAL_LIGHT_SHADOWMAP_COUNT; _idx_ < DIRECTIONAL_LIGHT_COUNT; _idx_++) {{\n shadowContribs[_idx_] = 1.0;\n }}\n}\n#endif\n#endif\n#if defined(POINT_LIGHT_SHADOWMAP_COUNT)\nvoid computeShadowOfPointLights(vec3 position, inout float shadowContribs[POINT_LIGHT_COUNT] ){\n vec3 lightPosition;\n vec3 direction;\n for(int _idx_ = 0; _idx_ < POINT_LIGHT_SHADOWMAP_COUNT; _idx_++) {{\n lightPosition = pointLightPosition[_idx_];\n direction = position - lightPosition;\n shadowContribs[_idx_] = computeShadowContribOmni(pointLightShadowMaps[_idx_], direction, pointLightRange[_idx_]);\n }}\n for(int _idx_ = POINT_LIGHT_SHADOWMAP_COUNT; _idx_ < POINT_LIGHT_COUNT; _idx_++) {{\n shadowContribs[_idx_] = 1.0;\n }}\n}\n#endif\n#endif\n@end");var Ni=ke.extend(function(){return{softShadow:Ni.PCF,shadowBlur:1,lightFrustumBias:"auto",kernelPCF:new Float32Array([1,0,1,1,-1,1,0,1,-1,0,-1,-1,1,-1,0,-1]),precision:"highp",_lastRenderNotCastShadow:!1,_frameBuffer:new oi,_textures:{},_shadowMapNumber:{POINT_LIGHT:0,DIRECTIONAL_LIGHT:0,SPOT_LIGHT:0},_depthMaterials:{},_distanceMaterials:{},_receivers:[],_lightsCastShadow:[],_lightCameras:{},_lightMaterials:{},_texturePool:new wi}},function(){this._gaussianPassH=new hi({fragment:Q.source("clay.compositor.gaussian_blur")}),this._gaussianPassV=new hi({fragment:Q.source("clay.compositor.gaussian_blur")}),this._gaussianPassH.setUniform("blurSize",this.shadowBlur),this._gaussianPassH.setUniform("blurDir",0),this._gaussianPassV.setUniform("blurSize",this.shadowBlur),this._gaussianPassV.setUniform("blurDir",1),this._outputDepthPass=new hi({fragment:Q.source("clay.sm.debug_depth")})},{render:function(t,e,r,n){r||(r=e.getMainCamera()),this.trigger("beforerender",this,t,e,r),this._renderShadowPass(t,e,r,n),this.trigger("afterrender",this,t,e,r)},renderDebug:function(t,e){t.saveClear();var r=t.viewport,n=0,i=e||r.width/4,a=i;this.softShadow===Ni.VSM?this._outputDepthPass.material.define("fragment","USE_VSM"):this._outputDepthPass.material.undefine("fragment","USE_VSM");for(var o in this._textures){var s=this._textures[o];t.setViewport(n,0,i*s.width/s.height,a),this._outputDepthPass.setUniform("depthMap",s),this._outputDepthPass.render(t),n+=i*s.width/s.height}t.setViewport(r),t.restoreClear()},_updateReceivers:function(t,e){if(e.receiveShadow?(this._receivers.push(e),e.material.set("shadowEnabled",1),e.material.set("pcfKernel",this.kernelPCF)):e.material.set("shadowEnabled",0),this.softShadow===Ni.VSM)e.material.define("fragment","USE_VSM"),e.material.undefine("fragment","PCF_KERNEL_SIZE");else{e.material.undefine("fragment","USE_VSM");var r=this.kernelPCF;r&&r.length?e.material.define("fragment","PCF_KERNEL_SIZE",r.length/2):e.material.undefine("fragment","PCF_KERNEL_SIZE")}},_update:function(t,e){var r=this;e.traverse(function(e){e.isRenderable()&&r._updateReceivers(t,e)});for(var n=0;n<e.lights.length;n++){var i=e.lights[n];i.castShadow&&!i.invisible&&this._lightsCastShadow.push(i)}},_renderShadowPass:function(t,e,r,n){function i(t){return t.height}for(var a in this._shadowMapNumber)this._shadowMapNumber[a]=0;this._lightsCastShadow.length=0,this._receivers.length=0;var o=t.gl;if(n||e.update(),r&&r.update(),e.updateLights(),this._update(t,e),this._lightsCastShadow.length||!this._lastRenderNotCastShadow){this._lastRenderNotCastShadow=0===this._lightsCastShadow,o.enable(o.DEPTH_TEST),o.depthMask(!0),o.disable(o.BLEND),o.clearColor(1,1,1,1);for(var s,u=[],l=[],h=[],c=[],f=[],d=[],p=0;p<this._lightsCastShadow.length;p++){var m=this._lightsCastShadow[p];if("DIRECTIONAL_LIGHT"===m.type){if(s){console.warn("Only one direectional light supported with shadow cascade");continue}if(m.shadowCascade>4){console.warn("Support at most 4 cascade");continue}m.shadowCascade>1&&(s=m),this.renderDirectionalLightShadow(t,e,r,m,f,c,h)}else"SPOT_LIGHT"===m.type?this.renderSpotLightShadow(t,e,m,l,u):"POINT_LIGHT"===m.type&&this.renderPointLightShadow(t,e,m,d);this._shadowMapNumber[m.type]++}for(var _ in this._shadowMapNumber)for(var g=this._shadowMapNumber[_],v=_+"_SHADOWMAP_COUNT",p=0;p<this._receivers.length;p++){var y=this._receivers[p],T=y.material;T.fragmentDefines[v]!==g&&(g>0?T.define("fragment",v,g):T.isDefined("fragment",v)&&T.undefine("fragment",v))}for(var p=0;p<this._receivers.length;p++){var y=this._receivers[p],T=y.material;s?T.define("fragment","SHADOW_CASCADE",s.shadowCascade):T.undefine("fragment","SHADOW_CASCADE")}var x=e.shadowUniforms;if(h.length>0){var E=h.map(i);if(x.directionalLightShadowMaps={value:h,type:"tv"},x.directionalLightMatrices={value:c,type:"m4v"},x.directionalLightShadowMapSizes={value:E,type:"1fv"},s){var A=f.slice(),b=f.slice();A.pop(),b.shift(),A.reverse(),b.reverse(),c.reverse(),x.shadowCascadeClipsNear={value:A,type:"1fv"},x.shadowCascadeClipsFar={value:b,type:"1fv"}}}if(u.length>0){var S=u.map(i),x=e.shadowUniforms;x.spotLightShadowMaps={value:u,type:"tv"},x.spotLightMatrices={value:l,type:"m4v"},x.spotLightShadowMapSizes={value:S,type:"1fv"}}d.length>0&&(x.pointLightShadowMaps={value:d,type:"tv"})}},renderDirectionalLightShadow:function(){var t=new tn,e=new Ur,r=new Vr,n=new Ur,i=new Ur,a=new Ur,o=new Ur;return function(s,u,l,h,c,f,d){var p=this._getDepthMaterial(h),m={getMaterial:function(t){return t.shadowDepthMaterial||p},isMaterialChanged:It,getUniform:Pt,ifRender:function(t){return t.castShadow},sortCompare:Mr.opaqueSortCompare};if(!u.viewBoundingBoxLastFrame.isFinite()){var _=u.getBoundingBox();u.viewBoundingBoxLastFrame.copy(_).applyTransform(l.viewMatrix)}var g=Math.min(-u.viewBoundingBoxLastFrame.min.z,l.far),v=Math.max(-u.viewBoundingBoxLastFrame.max.z,l.near),y=this._getDirectionalLightCamera(h,u,l),T=a.array;o.copy(y.projectionMatrix),Er.invert(i.array,y.worldTransform.array),Er.multiply(i.array,i.array,l.worldTransform.array),Er.multiply(T,o.array,i.array);for(var x=[],E=l instanceof Rn,A=(l.near+l.far)/(l.near-l.far),b=2*l.near*l.far/(l.near-l.far),S=0;S<=h.shadowCascade;S++){var M=v*Math.pow(g/v,S/h.shadowCascade),w=v+(g-v)*S/h.shadowCascade,C=M*h.cascadeSplitLogFactor+w*(1-h.cascadeSplitLogFactor);x.push(C),c.push(-(-C*A+b)/-C)}var R=this._getTexture(h,h.shadowCascade);d.push(R);var L=s.viewport,N=s.gl;this._frameBuffer.attach(R),this._frameBuffer.bind(s),N.clear(N.COLOR_BUFFER_BIT|N.DEPTH_BUFFER_BIT);for(var S=0;S<h.shadowCascade;S++){var P=x[S],I=x[S+1];E?Er.perspective(e.array,l.fov/180*Math.PI,l.aspect,P,I):Er.ortho(e.array,l.left,l.right,l.bottom,l.top,P,I),t.setFromProjection(e),t.getTransformedBoundingBox(r,i),r.applyProjection(o);var O=r.min.array,D=r.max.array;O[0]=Math.max(O[0],-1),O[1]=Math.max(O[1],-1),D[0]=Math.min(D[0],1),
D[1]=Math.min(D[1],1),n.ortho(O[0],D[0],O[1],D[1],1,-1),y.projectionMatrix.multiplyLeft(n);var B=h.shadowResolution||512;s.setViewport((h.shadowCascade-S-1)*B,0,B,B,1);var U=u.updateRenderList(y);s.renderPass(U.opaque,y,m),this.softShadow===Ni.VSM&&this._gaussianFilter(s,R,R.width);var F=new Ur;F.copy(y.viewMatrix).multiplyLeft(y.projectionMatrix),f.push(F.array),y.projectionMatrix.copy(o)}this._frameBuffer.unbind(s),s.setViewport(L)}}(),renderSpotLightShadow:function(t,e,r,n,i){var a=this._getTexture(r),o=this._getSpotLightCamera(r),s=t.gl;this._frameBuffer.attach(a),this._frameBuffer.bind(t),s.clear(s.COLOR_BUFFER_BIT|s.DEPTH_BUFFER_BIT);var u=this._getDepthMaterial(r),l={getMaterial:function(t){return t.shadowDepthMaterial||u},isMaterialChanged:It,getUniform:Pt,ifRender:function(t){return t.castShadow},sortCompare:Mr.opaqueSortCompare},h=e.updateRenderList(o);t.renderPass(h.opaque,o,l),this._frameBuffer.unbind(t),this.softShadow===Ni.VSM&&this._gaussianFilter(t,a,a.width);var c=new Ur;c.copy(o.worldTransform).invert().multiplyLeft(o.projectionMatrix),i.push(a),n.push(c.array)},renderPointLightShadow:function(t,e,r,n){var i=this._getTexture(r),a=t.gl;n.push(i);var o=this._getDepthMaterial(r),s={getMaterial:function(t){return t.shadowDepthMaterial||o},getUniform:Pt,sortCompare:Mr.opaqueSortCompare},u={px:[],py:[],pz:[],nx:[],ny:[],nz:[]},l=new Vr,h=r.getWorldPosition().array,c=new Vr,f=r.range;c.min.setArray(h),c.max.setArray(h);var d=new Cr(f,f,f);c.max.add(d),c.min.sub(d);var p={px:!1,py:!1,pz:!1,nx:!1,ny:!1,nz:!1};e.traverse(function(t){if(t.isRenderable()&&t.castShadow){var e=t.geometry;if(!e.boundingBox){for(var r=0;r<Li.length;r++)u[Li[r]].push(t);return}if(l.transformFrom(e.boundingBox,t.worldTransform),!l.intersectBoundingBox(c))return;l.updateVertices();for(var r=0;r<Li.length;r++)p[Li[r]]=!1;for(var r=0;r<8;r++){var n=l.vertices[r],i=n[0]-h[0],a=n[1]-h[1],o=n[2]-h[2],s=Math.abs(i),f=Math.abs(a),d=Math.abs(o);s>f?s>d?p[i>0?"px":"nx"]=!0:p[o>0?"pz":"nz"]=!0:f>d?p[a>0?"py":"ny"]=!0:p[o>0?"pz":"nz"]=!0}for(var r=0;r<Li.length;r++)p[Li[r]]&&u[Li[r]].push(t)}});for(var m=0;m<6;m++){var _=Li[m],g=this._getPointLightCamera(r,_);this._frameBuffer.attach(i,a.COLOR_ATTACHMENT0,a.TEXTURE_CUBE_MAP_POSITIVE_X+m),this._frameBuffer.bind(t),a.clear(a.COLOR_BUFFER_BIT|a.DEPTH_BUFFER_BIT),t.renderPass(u[_],g,s)}this._frameBuffer.unbind(t)},_getDepthMaterial:function(t){var e=this._lightMaterials[t.__uid__],r="POINT_LIGHT"===t.type;if(!e){var n=r?"clay.sm.distance.":"clay.sm.depth.";e=new nr({precision:this.precision,shader:new Q(Q.source(n+"vertex"),Q.source(n+"fragment"))}),this._lightMaterials[t.__uid__]=e}return null!=t.shadowSlopeScale&&e.setUniform("slopeScale",t.shadowSlopeScale),null!=t.shadowBias&&e.setUniform("bias",t.shadowBias),this.softShadow===Ni.VSM?e.define("fragment","USE_VSM"):e.undefine("fragment","USE_VSM"),r&&(e.set("lightPosition",t.getWorldPosition().array),e.set("range",t.range)),e},_gaussianFilter:function(t,e,r){var n={width:r,height:r,type:Tn.FLOAT},i=this._texturePool.get(n);this._frameBuffer.attach(i),this._frameBuffer.bind(t),this._gaussianPassH.setUniform("texture",e),this._gaussianPassH.setUniform("textureWidth",r),this._gaussianPassH.render(t),this._frameBuffer.attach(e),this._gaussianPassV.setUniform("texture",i),this._gaussianPassV.setUniform("textureHeight",r),this._gaussianPassV.render(t),this._frameBuffer.unbind(t),this._texturePool.put(i)},_getTexture:function(t,e){var r=t.__uid__,n=this._textures[r],i=t.shadowResolution||512;return e=e||1,n||(n="POINT_LIGHT"===t.type?new Mn:new An,n.width=i*e,n.height=i,this.softShadow===Ni.VSM?(n.type=Tn.FLOAT,n.anisotropic=4):(n.minFilter=Ye.NEAREST,n.magFilter=Ye.NEAREST,n.useMipmap=!1),this._textures[r]=n),n},_getPointLightCamera:function(t,e){this._lightCameras.point||(this._lightCameras.point={px:new Rn,nx:new Rn,py:new Rn,ny:new Rn,pz:new Rn,nz:new Rn});var r=this._lightCameras.point[e];switch(r.far=t.range,r.fov=90,r.position.set(0,0,0),e){case"px":r.lookAt(Cr.POSITIVE_X,Cr.NEGATIVE_Y);break;case"nx":r.lookAt(Cr.NEGATIVE_X,Cr.NEGATIVE_Y);break;case"py":r.lookAt(Cr.POSITIVE_Y,Cr.POSITIVE_Z);break;case"ny":r.lookAt(Cr.NEGATIVE_Y,Cr.NEGATIVE_Z);break;case"pz":r.lookAt(Cr.POSITIVE_Z,Cr.NEGATIVE_Y);break;case"nz":r.lookAt(Cr.NEGATIVE_Z,Cr.NEGATIVE_Y)}return t.getWorldPosition(r.position),r.update(),r},_getDirectionalLightCamera:function(){var t=new Ur,e=new Vr,r=new Vr;return function(n,i,a){this._lightCameras.directional||(this._lightCameras.directional=new Ln);var o=this._lightCameras.directional;e.copy(i.viewBoundingBoxLastFrame),e.intersection(a.frustum.boundingBox),o.position.copy(e.min).add(e.max).scale(.5).transformMat4(a.worldTransform),o.rotation.copy(n.rotation),o.scale.copy(n.scale),o.updateWorldTransform(),Ur.invert(t,o.worldTransform),Ur.multiply(t,t,a.worldTransform),r.copy(e).applyTransform(t);var s=r.min.array,u=r.max.array;return o.position.set((s[0]+u[0])/2,(s[1]+u[1])/2,u[2]).transformMat4(o.worldTransform),o.near=0,o.far=-s[2]+u[2],isNaN(this.lightFrustumBias)?o.far*=4:o.far+=this.lightFrustumBias,o.left=s[0],o.right=u[0],o.top=u[1],o.bottom=s[1],o.update(!0),o}}(),_getSpotLightCamera:function(t){this._lightCameras.spot||(this._lightCameras.spot=new Rn);var e=this._lightCameras.spot;return e.fov=2*t.penumbraAngle,e.far=t.range,e.worldTransform.copy(t.worldTransform),e.updateProjectionMatrix(),Er.invert(e.viewMatrix.array,e.worldTransform.array),e},dispose:function(t){var e=t.gl||t;this._frameBuffer&&this._frameBuffer.dispose(e);for(var r in this._textures)this._textures[r].dispose(e);this._texturePool.clear(t.gl),this._depthMaterials={},this._distanceMaterials={},this._textures={},this._lightCameras={},this._shadowMapNumber={POINT_LIGHT:0,DIRECTIONAL_LIGHT:0,SPOT_LIGHT:0},this._meshMaterials={};for(var n=0;n<this._receivers.length;n++){var i=this._receivers[n];if(i.material){var a=i.material;a.undefine("fragment","POINT_LIGHT_SHADOW_COUNT"),a.undefine("fragment","DIRECTIONAL_LIGHT_SHADOW_COUNT"),a.undefine("fragment","AMBIENT_LIGHT_SHADOW_COUNT"),a.set("shadowEnabled",0)}}this._receivers=[],this._lightsCastShadow=[]}});Ni.VSM=1,Ni.PCF=2;var Pi=ke.extend({scene:null,camera:null,renderer:null},function(){this._ray=new en,this._ndc=new Se},{pick:function(t,e,r){return this.pickAll(t,e,[],r)[0]||null},pickAll:function(t,e,r,n){return this.renderer.screenToNDC(t,e,this._ndc),this.camera.castRay(this._ndc,this._ray),r=r||[],this._intersectNode(this.scene,r,n||!1),r.sort(this._intersectionCompareFunc),r},_intersectNode:function(t,e,r){t instanceof wn&&t.isRenderable()&&(t.ignorePicking&&!r||!(t.mode===Ye.TRIANGLES&&t.geometry.isUseIndices()||t.geometry.pickByRay||t.geometry.pick)||this._intersectRenderable(t,e));for(var n=0;n<t._children.length;n++)this._intersectNode(t._children[n],e,r)},_intersectRenderable:function(){var t=new Cr,e=new Cr,r=new Cr,n=new en,i=new Ur;return function(a,o){var s=a.isSkinnedMesh();n.copy(this._ray),Ur.invert(i,a.worldTransform),s||n.applyTransform(i);var u=a.geometry,l=s?a.skeleton.boundingBox:u.boundingBox;if(!l||n.intersectBoundingBox(l)){if(u.pick)return void u.pick(this._ndc.x,this._ndc.y,this.renderer,this.camera,a,o);if(u.pickByRay)return void u.pickByRay(n,a,o);var h,c,f=a.cullFace===Ye.BACK&&a.frontFace===Ye.CCW||a.cullFace===Ye.FRONT&&a.frontFace===Ye.CW,d=u.indices,p=u.attributes.position,m=u.attributes.weight,_=u.attributes.joint,g=[];if(p&&p.value&&d){if(s){c=a.skeleton.getSubSkinMatrices(a.__uid__,a.joints);for(var v=0;v<a.joints.length;v++){g[v]=g[v]||[];for(var y=0;y<16;y++)g[v][y]=c[16*v+y]}var T=[],x=[],E=[],A=[],b=[],S=u.attributes.skinnedPosition;S&&S.value||(u.createAttribute("skinnedPosition","f",3),S=u.attributes.skinnedPosition,S.init(u.vertexCount));for(var v=0;v<u.vertexCount;v++){p.get(v,T),m.get(v,x),_.get(v,E),x[3]=1-x[0]-x[1]-x[2],Ce.set(A,0,0,0);for(var y=0;y<4;y++)E[y]>=0&&x[y]>1e-4&&(Ce.transformMat4(b,T,g[E[y]]),Ce.scaleAndAdd(A,A,b,x[y]));S.set(v,A)}}for(var v=0;v<d.length;v+=3){var M=d[v],w=d[v+1],C=d[v+2],R=s?u.attributes.skinnedPosition:p;if(R.get(M,t.array),R.get(w,e.array),R.get(C,r.array),h=f?n.intersectTriangle(t,e,r,a.culling):n.intersectTriangle(t,r,e,a.culling)){var L=new Cr;s?Cr.copy(L,h):Cr.transformMat4(L,h,a.worldTransform),o.push(new Pi.Intersection(h,L,a,[M,w,C],v/3,Cr.dist(L,this._ray.origin)))}}}}}}(),_intersectionCompareFunc:function(t,e){return t.distance-e.distance}});Pi.Intersection=function(t,e,r,n,i,a){this.point=t,this.pointWorld=e,this.target=r,this.triangle=n,this.triangleIndex=i,this.distance=a};var Ii={},Oi=["px","nx","py","ny","pz","nz"],Di={px:[2,1,0,-1,-1,1],nx:[2,1,0,1,-1,-1],py:[0,2,1,1,-1,-1],ny:[0,2,1,1,1,1],pz:[0,1,2,-1,-1,-1],nz:[0,1,2,1,-1,1]};Ii.projectEnvironmentMap=function(t,e,r){r=r||{},r.lod=r.lod||0;var n,i=new sn,a=64;"texture2D"===e.textureType?n=new ci({scene:i,environmentMap:e}):(a=e.image&&e.image.px?e.image.px.width:e.width,n=new ci({scene:i,environmentMap:e}));var o=Math.ceil(a/Math.pow(2,r.lod)),s=Math.ceil(a/Math.pow(2,r.lod)),u=new An({width:o,height:s}),l=new oi;n.material.define("fragment","RGBM_ENCODE"),r.decodeRGBM&&n.material.define("fragment","RGBM_DECODE"),n.material.set("lod",r.lod);for(var h=new di({texture:u}),c={},f=0;f<Oi.length;f++){c[Oi[f]]=new Uint8Array(o*s*4);var d=h.getCamera(Oi[f]);d.fov=90,l.attach(u),l.bind(t),t.render(i,d),t.gl.readPixels(0,0,o,s,Tn.RGBA,Tn.UNSIGNED_BYTE,c[Oi[f]]),l.unbind(t)}return n.dispose(t),l.dispose(t),u.dispose(t),Dt(t,c,o,s)};var Bi=Je.parseToFloat,Ui=["click","dblclick","mouseover","mouseout","mousemove","touchstart","touchend","touchmove","mousewheel","DOMMouseScroll"];Bt.prototype._initMouseEvents=function(t){var e=this.container,r=null;Ui.forEach(function(n){Ve.addEventListener(e,n,this[kt(n)]=function(i){if(t.camera){i.preventDefault&&i.preventDefault();var a,o,s=e.getBoundingClientRect(),u=n;if(u.indexOf("touch")>=0){var l="touchend"!==u?i.targetTouches[0]:i.changedTouches[0];"touchstart"===u?u="mousedown":"touchend"===u?u="mouseup":"touchmove"===u&&(u="mousemove"),a=l.clientX-s.left,o=l.clientY-s.top}else a=i.clientX-s.left,o=i.clientY-s.top;var h,c=t.pick(a,o);if("DOMMouseScroll"!==u&&"mousewheel"!==u||(h=i.wheelDelta?i.wheelDelta/120:-(i.detail||0)/3),c){if(c.target.silent)return;if("mousemove"===u){var f=c.target!==r;f&&r&&Gt(r,Ht("mouseout",{target:r},a,o)),Gt(c.target,Ht("mousemove",c,a,o)),f&&Gt(c.target,Ht("mouseover",c,a,o))}else Gt(c.target,Ht(u,c,a,o,h));r=c.target}else r&&(Gt(r,Ht("mouseout",{target:r},a,o)),r=null)}})},this)},Bt.prototype._updateGraphicOptions=function(t,e,r){for(var n,i=!!t.tonemapping,a=!!t.linear,o=0;o<e.length;o++){var s=e[o].material;if(s!==n){if(i?s.define("fragment","TONEMAPPING"):s.undefine("fragment","TONEMAPPING"),a){var u=!0;r&&s.get("environmentMap")&&!s.get("environmentMap").sRGB&&(u=!1),u&&s.define("fragment","SRGB_DECODE"),s.define("fragment","SRGB_ENCODE")}else s.undefine("fragment","SRGB_DECODE"),s.undefine("fragment","SRGB_ENCODE");n=s}}},Bt.prototype._doRender=function(t,e){var r=e.getMainCamera();t.render(e,r,!0)},Bt.prototype.render=function(){this._inRender=!0;var t=this._appNS;t.beforeRender&&t.beforeRender(self);var e=this.scene,r=this.renderer,n=this._shadowPass;e.update();var i=[];e.skybox&&i.push(e.skybox),e.skydome&&i.push(e.skydome),this._updateGraphicOptions(t.graphic,i,!0),n&&n.render(r,e,null,!0),this._doRender(r,e,!0),t.afterRender&&t.afterRender(self),this._inRender=!1},Bt.prototype.collectResources=function(){var t=this.renderer,e=this.scene,r=this._texturesList,n=this._geometriesList;Vt(r),Vt(n);var i=[],a=[];Xt(e,i,a),Wt(t,r),Wt(t,n),this._texturesList=i,this._geometriesList=a},Bt.prototype.loadTexture=function(t,e,r){var n=this,i=Ft(t);if(r&&this._texCache.get(i))return this._texCache.get(i);var a=new Promise(function(r,i){var a=n.loadTextureSync(t,e);a.isRenderable()?r(a):(a.success(function(){n._disposed||r(a)}),a.error(function(){n._disposed||i()}))});return r&&this._texCache.put(i,a),a},Bt.prototype.loadTextureSync=function(t,e){var r=new An(e);if("string"==typeof t)if(t.match(/.hdr$|^data:application\/octet-stream/)){r=Ei.loadTexture(t,{exposure:e&&e.exposure,fileType:"hdr"},function(){r.dirty(),r.trigger("success")});for(var n in e)r[n]=e[n]}else r.load(t);else Ut(t)&&(r.image=t,r.dynamic=t instanceof HTMLVideoElement);return r},Bt.prototype.loadTextureCube=function(t,e){var r=this.loadTextureCubeSync(t,e);return new Promise(function(t,e){r.isRenderable()?t(r):r.success(function(){t(r)}).error(function(){e()})})},Bt.prototype.loadTextureCubeSync=function(t,e){e=e||{},e.flipY=e.flipY||!1;var r=new Mn(e);if(!(t&&t.px&&t.nx&&t.py&&t.ny&&t.pz&&t.nz))throw new Error("Invalid cubemap format. Should be an object including px,nx,py,ny,pz,nz");return"string"==typeof t.px?r.load(t):r.image=Fe.clone(t),r},Bt.prototype.createMaterial=function(t){t=t||{},t.shader=t.shader||"clay.standardMR";var e=t.shader instanceof Q?t.shader:Gn.get(t.shader),r=new nr({shader:e});t.name&&(r.name=t.name);var n=[];for(var i in t)if(r.uniforms[i]){var a=t[i];"t"!==r.uniforms[i].type&&!Ut(a)||a instanceof Tn?r.setUniform(i,a):n.push(this.loadTexture(a,{convertToPOT:t.textureConvertToPOT||!1,flipY:null==t.textureFlipY||t.textureFlipY}).then(function(e){return function(n){return r.setUniform(e,n),t.textureLoaded&&t.textureLoaded(e,n),n}}(i)))}return t.transparent&&(t.depthMask=!1,t.transparent=!0),t.texturesReady&&Promise.all(n).then(function(e){t.texturesReady(e)}),r},Bt.prototype.createCube=function(t,e,r){null==r&&(r=1),"number"==typeof r&&(r=[r,r,r]);var n="cube-"+r.join("-"),i=this._geoCache.get(n);return i||(i=new gn({widthSegments:r[0],heightSegments:r[1],depthSegments:r[2]}),i.generateTangents(),this._geoCache.put(n,i)),this.createMesh(i,t,e)},Bt.prototype.createCubeInside=function(t,e,r){null==r&&(r=1),"number"==typeof r&&(r=[r,r,r]);var n="cubeInside-"+r.join("-"),i=this._geoCache.get(n);return i||(i=new gn({inside:!0,widthSegments:r[0],heightSegments:r[1],depthSegments:r[2]}),i.generateTangents(),this._geoCache.put(n,i)),this.createMesh(i,t,e)},Bt.prototype.createSphere=function(t,e,r){null==r&&(r=20);var n="sphere-"+r,i=this._geoCache.get(n);return i||(i=new vn({widthSegments:2*r,heightSegments:r}),i.generateTangents(),this._geoCache.put(n,i)),this.createMesh(i,t,e)},Bt.prototype.createPlane=function(t,e,r){null==r&&(r=1),"number"==typeof r&&(r=[r,r]);var n="plane-"+r.join("-"),i=this._geoCache.get(n);return i||(i=new mn({widthSegments:r[0],heightSegments:r[1]}),i.generateTangents(),this._geoCache.put(n,i)),this.createMesh(i,t,e)},Bt.prototype.createParametricSurface=function(t,e,r){var n=new yn({generator:r});return n.generateTangents(),this.createMesh(n,t,e)},Bt.prototype.createMesh=function(t,e,r){var n=new Cn({geometry:t,material:e instanceof nr?e:this.createMaterial(e)});return r=r||this.scene,r.add(n),n},Bt.prototype.createNode=function(t){var e=new zr;return t=t||this.scene,t.add(e),e},Bt.prototype.createCamera=function(t,e,r,n){var i,a=!1;"ortho"===r||"orthographic"===r?(a=!0,i=Ln):(r&&"perspective"!==r&&console.error("Unkown camera type "+r+". Use default perspective camera"),i=Rn);var o=new i;return t instanceof Cr?o.position.copy(t):t instanceof Array&&o.position.setArray(t),e instanceof Array&&(e=new Cr(e[0],e[1],e[2])),e instanceof Cr&&o.lookAt(e),n&&a?(n=n.array||n,o.left=-n[0]/2,o.right=n[0]/2,o.top=n[1]/2,o.bottom=-n[1]/2,o.near=0,o.far=n[2]):o.aspect=this.renderer.getViewportAspect(),this.scene.add(o),o},Bt.prototype.createDirectionalLight=function(t,e,r){var n=new Qn;return t instanceof Cr&&(t=t.array),n.position.setArray(t).negate(),n.lookAt(Cr.ZERO),"string"==typeof e&&(e=Bi(e)),null!=e&&(n.color=e),null!=r&&(n.intensity=r),this.scene.add(n),n},Bt.prototype.createSpotLight=function(t,e,r,n,i,a,o){var s=new ti;return s.position.setArray(t instanceof Cr?t.array:t),e instanceof Array&&(e=new Cr(e[0],e[1],e[2])),e instanceof Cr&&s.lookAt(e),"string"==typeof n&&(n=Bi(n)),null!=r&&(s.range=r),null!=n&&(s.color=n),null!=i&&(s.intensity=i),null!=a&&(s.umbraAngle=a),null!=o&&(s.penumbraAngle=o),this.scene.add(s),s},Bt.prototype.createPointLight=function(t,e,r,n){var i=new $n;return i.position.setArray(t instanceof Cr?t.array:t),"string"==typeof r&&(r=Bi(r)),null!=e&&(i.range=e),null!=r&&(i.color=r),null!=n&&(i.intensity=n),this.scene.add(i),i},Bt.prototype.createAmbientLight=function(t,e){var r=new ei;return"string"==typeof t&&(t=Bi(t)),null!=t&&(r.color=t),null!=e&&(r.intensity=e),this.scene.add(r),r},Bt.prototype.createAmbientCubemapLight=function(t,e,r,n,i){var a=this;null==n&&(n=0),null==i&&(i=32);var o,s=this.scene;return o="textureCube"===t.textureType?t.isRenderable()?Promise.resolve(t):new Promise(function(e,r){t.success(function(){e(t)})}):this.loadTexture(t,{exposure:n}),o.then(function(t){var n=new Si({intensity:null!=e?e:.7});n.cubemap=t,t.flipY=!1,n.prefilter(a.renderer,32);var i=new Mi({intensity:null!=r?r:.7,coefficients:Ii.projectEnvironmentMap(a.renderer,n.cubemap,{lod:1})});return s.add(n),s.add(i),{specular:n,diffuse:i,environmentMap:t}})},Bt.prototype.loadModel=function(t,e,r){if("string"!=typeof t)throw new Error("Invalid URL.");e=e||{},null==e.autoPlayAnimation&&(e.autoPlayAnimation=!0);var n=e.shader||"clay.standard",i={rootNode:new zr,shader:n,textureRootPath:e.textureRootPath,crossOrigin:"Anonymous",textureFlipY:e.textureFlipY,textureConvertToPOT:e.textureConvertToPOT};e.upAxis&&"z"===e.upAxis.toLowerCase()&&i.rootNode.rotation.identity().rotateX(-Math.PI/2);var a=new Jn(i);r=r||this.scene;var o=this.timeline,s=this;return new Promise(function(n,i){function u(t){s._disposed||(r.add(t.rootNode),e.autoPlayAnimation&&t.clips.forEach(function(t){o.addClip(t)}),n(t))}a.success(function(t){s._disposed||(e.waitTextureLoaded?Promise.all(t.textures.map(function(t){return t.isRenderable()?Promise.resolve(t):new Promise(function(e){t.success(e),t.error(e)})})).then(function(){u(t)}).catch(function(){u(t)}):u(t))}),a.error(function(){i()}),a.load(t)})},Bt.prototype.cloneNode=function(t,e){e=e||t.getParent();var r=this.scene.cloneNode(t,e);return e&&e.add(r),r};var Fi={App3D:Bt,create:function(t,e){return new Bt(t,e)}};jt.prototype.resolve=function(t){this._fullfilled=!0,this._rejected=!1,this.trigger("success",t)},jt.prototype.reject=function(t){this._rejected=!0,this._fullfilled=!1,this.trigger("error",t)},jt.prototype.isFullfilled=function(){return this._fullfilled},jt.prototype.isRejected=function(){return this._rejected},jt.prototype.isSettled=function(){return this._fullfilled||this._rejected},Fe.extend(jt.prototype,Oe),jt.makeRequestTask=function(t,e){if("string"==typeof t)return qt(t,e);if(t.url){var r=t;return qt(r.url,r.responseType)}if(Array.isArray(t)){var n=t,i=[];return n.forEach(function(t){var e,r;"string"==typeof t?e=t:Object(t)===t&&(e=t.url,r=t.responseType),i.push(qt(e,r))}),i}},jt.makeTask=function(){return new jt},Fe.extend(jt.prototype,Oe);var ki=function(){jt.apply(this,arguments),this._tasks=[],this._fulfilledNumber=0,this._rejectedNumber=0},Hi=function(){};Hi.prototype=jt.prototype,ki.prototype=new Hi,ki.prototype.constructor=ki,ki.prototype.all=function(t){var e=0,r=this,n=[];return this._tasks=t,this._fulfilledNumber=0,this._rejectedNumber=0,Fe.each(t,function(t,i){t&&t.once&&(e++,t.once("success",function(a){e--,r._fulfilledNumber++,t._fulfilled=!0,t._rejected=!1,n[i]=a,0===e&&r.resolve(n)}),t.once("error",function(){r._rejectedNumber++,t._fulfilled=!1,t._rejected=!0,r.reject(t)}))}),0===e?(setTimeout(function(){r.resolve(n)}),this):this},ki.prototype.allSettled=function(t){var e=0,r=this,n=[];return 0===t.length?(setTimeout(function(){r.trigger("success",n)}),this):(this._tasks=t,Fe.each(t,function(t,i){t&&t.once&&(e++,t.once("success",function(a){e--,r._fulfilledNumber++,t._fulfilled=!0,t._rejected=!1,n[i]=a,0===e&&r.resolve(n)}),t.once("error",function(a){e--,r._rejectedNumber++,t._fulfilled=!1,t._rejected=!0,n[i]=null,0===e&&r.resolve(n)}))}),this)},ki.prototype.getFulfilledNumber=function(t){if(t){for(var e=0,r=0;r<this._tasks.length;r++){var n=this._tasks[r];n instanceof ki?e+=n.getFulfilledNumber(t):n._fulfilled&&(e+=1)}return e}return this._fulfilledNumber},ki.prototype.getRejectedNumber=function(t){if(t){for(var e=0,r=0;r<this._tasks.length;r++){var n=this._tasks[r];n instanceof ki?e+=n.getRejectedNumber(t):n._rejected&&(e+=1)}return e}return this._rejectedNumber},ki.prototype.getSettledNumber=function(t){if(t){for(var e=0,r=0;r<this._tasks.length;r++){var n=this._tasks[r];n instanceof ki?e+=n.getSettledNumber(t):(n._rejected||n._fulfilled)&&(e+=1)}return e}return this._fulfilledNumber+this._rejectedNumber},ki.prototype.getTaskNumber=function(t){if(t){for(var e=0,r=0;r<this._tasks.length;r++){var n=this._tasks[r];e+=n instanceof ki?n.getTaskNumber(t):1}return e}return this._tasks.length};var Gi=ke.extend(function(){return{name:"",inputLinks:{},outputLinks:{},_prevOutputTextures:{},_outputTextures:{},_outputReferences:{},_rendering:!1,_rendered:!1,_compositor:null}},{updateParameter:function(t,e){var r=this.outputs[t],n=r.parameters,i=r._parametersCopy;if(i||(i=r._parametersCopy={}),n)for(var a in n)"width"!==a&&"height"!==a&&(i[a]=n[a]);var o,s;return o=n.width instanceof Function?n.width.call(this,e):n.width,s=n.height instanceof Function?n.height.call(this,e):n.height,i.width===o&&i.height===s||this._outputTextures[t]&&this._outputTextures[t].dispose(e.gl),i.width=o,i.height=s,i},setParameter:function(t,e){},getParameter:function(t){},setParameters:function(t){for(var e in t)this.setParameter(e,t[e])},render:function(){},getOutput:function(t,e){if(null==e)return e=t,this._outputTextures[e];var r=this.outputs[e];if(r)return this._rendered?r.outputLastFrame?this._prevOutputTextures[e]:this._outputTextures[e]:this._rendering?(this._prevOutputTextures[e]||(this._prevOutputTextures[e]=this._compositor.allocateTexture(r.parameters||{})),this._prevOutputTextures[e]):(this.render(t),this._outputTextures[e])},removeReference:function(t){if(0===--this._outputReferences[t]){this.outputs[t].keepLastFrame?(this._prevOutputTextures[t]&&this._compositor.releaseTexture(this._prevOutputTextures[t]),this._prevOutputTextures[t]=this._outputTextures[t]):this._compositor.releaseTexture(this._outputTextures[t])}},link:function(t,e,r){this.inputLinks[t]={node:e,pin:r},e.outputLinks[r]||(e.outputLinks[r]=[]),e.outputLinks[r].push({node:this,pin:t}),this.pass.material.enableTexture(t)},clear:function(){this.inputLinks={},this.outputLinks={}},updateReference:function(t){if(!this._rendering){this._rendering=!0;for(var e in this.inputLinks){var r=this.inputLinks[e];r.node.updateReference(r.pin)}this._rendering=!1}t&&this._outputReferences[t]++},beforeFrame:function(){this._rendered=!1;for(var t in this.outputLinks)this._outputReferences[t]=0},afterFrame:function(){for(var t in this.outputLinks)if(this._outputReferences[t]>0){var e=this.outputs[t];e.keepLastFrame?(this._prevOutputTextures[t]&&this._compositor.releaseTexture(this._prevOutputTextures[t]),this._prevOutputTextures[t]=this._outputTextures[t]):this._compositor.releaseTexture(this._outputTextures[t])}}}),Vi=ke.extend(function(){return{nodes:[]}},{dirty:function(){this._dirty=!0},addNode:function(t){this.nodes.indexOf(t)>=0||(this.nodes.push(t),this._dirty=!0)},removeNode:function(t){"string"==typeof t&&(t=this.getNodeByName(t));var e=this.nodes.indexOf(t);e>=0&&(this.nodes.splice(e,1),this._dirty=!0)},getNodeByName:function(t){for(var e=0;e<this.nodes.length;e++)if(this.nodes[e].name===t)return this.nodes[e]},update:function(){for(var t=0;t<this.nodes.length;t++)this.nodes[t].clear();for(var t=0;t<this.nodes.length;t++){var e=this.nodes[t];if(e.inputs)for(var r in e.inputs)if(e.inputs[r])if(!e.pass||e.pass.material.isUniformEnabled(r)){var n=e.inputs[r],i=this.findPin(n);i?e.link(r,i.node,i.pin):"string"==typeof n?console.warn("Node "+n+" not exist"):console.warn("Pin of "+n.node+"."+n.pin+" not exist")}else console.warn("Pin "+e.name+"."+r+" not used.")}},findPin:function(t){var e;if(("string"==typeof t||t instanceof Gi)&&(t={node:t}),"string"==typeof t.node)for(var r=0;r<this.nodes.length;r++){var n=this.nodes[r];n.name===t.node&&(e=n)}else e=t.node;if(e){var i=t.pin;if(i||e.outputs&&(i=Object.keys(e.outputs)[0]),e.outputs[i])return{node:e,pin:i}}}}),Wi=Vi.extend(function(){return{_outputs:[],_texturePool:new wi,_frameBuffer:new oi({depthBuffer:!1})}},{addNode:function(t){Vi.prototype.addNode.call(this,t),t._compositor=this},render:function(t,e){if(this._dirty){this.update(),this._dirty=!1,this._outputs.length=0;for(var r=0;r<this.nodes.length;r++)this.nodes[r].outputs||this._outputs.push(this.nodes[r])}for(var r=0;r<this.nodes.length;r++)this.nodes[r].beforeFrame();for(var r=0;r<this._outputs.length;r++)this._outputs[r].updateReference();for(var r=0;r<this._outputs.length;r++)this._outputs[r].render(t,e);for(var r=0;r<this.nodes.length;r++)this.nodes[r].afterFrame()},allocateTexture:function(t){return this._texturePool.get(t)},releaseTexture:function(t){this._texturePool.put(t)},getFrameBuffer:function(){return this._frameBuffer},dispose:function(t){this._texturePool.clear(t)}}),zi=Gi.extend({name:"scene",scene:null,camera:null,autoUpdateScene:!0,preZ:!1},function(){this.frameBuffer=new oi},{render:function(t){this._rendering=!0;var e=t.gl;this.trigger("beforerender");var r;if(this.outputs){var n=this.frameBuffer;for(var i in this.outputs){var a=this.updateParameter(i,t),o=this.outputs[i],s=this._compositor.allocateTexture(a);this._outputTextures[i]=s;var u=o.attachment||e.COLOR_ATTACHMENT0;"string"==typeof u&&(u=e[u]),n.attach(s,u)}n.bind(t);var l=t.getGLExtension("EXT_draw_buffers");if(l){var h=[];for(var u in this.outputs)(u=parseInt(u))>=e.COLOR_ATTACHMENT0&&u<=e.COLOR_ATTACHMENT0+8&&h.push(u);l.drawBuffersEXT(h)}t.saveClear(),t.clearBit=Ye.DEPTH_BUFFER_BIT|Ye.COLOR_BUFFER_BIT,r=t.render(this.scene,this.camera,!this.autoUpdateScene,this.preZ),t.restoreClear(),n.unbind(t)}else r=t.render(this.scene,this.camera,!this.autoUpdateScene,this.preZ);this.trigger("afterrender",r),this._rendering=!1,this._rendered=!0}}),Xi=Gi.extend(function(){return{texture:null,outputs:{color:{}}}},function(){},{getOutput:function(t,e){return this.texture},beforeFrame:function(){},afterFrame:function(){}}),ji=Gi.extend(function(){return{name:"",inputs:{},outputs:null,shader:"",inputLinks:{},outputLinks:{},pass:null,_prevOutputTextures:{},_outputTextures:{},_outputReferences:{},_rendering:!1,_rendered:!1,_compositor:null}},function(){var t=new hi({fragment:this.shader});this.pass=t},{render:function(t,e){this.trigger("beforerender",t),this._rendering=!0;var r=t.gl;for(var n in this.inputLinks){var i=this.inputLinks[n],a=i.node.getOutput(t,i.pin);this.pass.setUniform(n,a)}if(this.outputs){this.pass.outputs={};var o={};for(var s in this.outputs){var u=this.updateParameter(s,t);isNaN(u.width)&&this.updateParameter(s,t);var l=this.outputs[s],h=this._compositor.allocateTexture(u);this._outputTextures[s]=h;var c=l.attachment||r.COLOR_ATTACHMENT0;"string"==typeof c&&(c=r[c]),o[c]=h}this._compositor.getFrameBuffer().bind(t);for(var c in o)this._compositor.getFrameBuffer().attach(o[c],c);this.pass.render(t),this._compositor.getFrameBuffer().updateMipmap(t)}else this.pass.outputs=null,this._compositor.getFrameBuffer().unbind(t),this.pass.render(t,e);for(var n in this.inputLinks){var i=this.inputLinks[n];i.node.removeReference(i.pin)}this._rendering=!1,this._rendered=!0,this.trigger("afterrender",t)},updateParameter:function(t,e){var r=this.outputs[t],n=r.parameters,i=r._parametersCopy;if(i||(i=r._parametersCopy={}),n)for(var a in n)"width"!==a&&"height"!==a&&(i[a]=n[a]);var o,s;return o="function"==typeof n.width?n.width.call(this,e):n.width,s="function"==typeof n.height?n.height.call(this,e):n.height,o=Math.ceil(o),s=Math.ceil(s),i.width===o&&i.height===s||this._outputTextures[t]&&this._outputTextures[t].dispose(e),i.width=o,i.height=s,i},setParameter:function(t,e){this.pass.setUniform(t,e)},getParameter:function(t){return this.pass.getUniform(t)},setParameters:function(t){for(var e in t)this.setParameter(e,t[e])},define:function(t,e){this.pass.material.define("fragment",t,e)},undefine:function(t){this.pass.material.undefine("fragment",t)},removeReference:function(t){if(0===--this._outputReferences[t]){this.outputs[t].keepLastFrame?(this._prevOutputTextures[t]&&this._compositor.releaseTexture(this._prevOutputTextures[t]),this._prevOutputTextures[t]=this._outputTextures[t]):this._compositor.releaseTexture(this._outputTextures[t])}},clear:function(){Gi.prototype.clear.call(this),this.pass.material.disableTexturesAll()}
}),qi="@export clay.compositor.coloradjust\nvarying vec2 v_Texcoord;\nuniform sampler2D texture;\nuniform float brightness : 0.0;\nuniform float contrast : 1.0;\nuniform float exposure : 0.0;\nuniform float gamma : 1.0;\nuniform float saturation : 1.0;\nconst vec3 w = vec3(0.2125, 0.7154, 0.0721);\nvoid main()\n{\n vec4 tex = texture2D( texture, v_Texcoord);\n vec3 color = clamp(tex.rgb + vec3(brightness), 0.0, 1.0);\n color = clamp( (color-vec3(0.5))*contrast+vec3(0.5), 0.0, 1.0);\n color = clamp( color * pow(2.0, exposure), 0.0, 1.0);\n color = clamp( pow(color, vec3(gamma)), 0.0, 1.0);\n float luminance = dot( color, w );\n color = mix(vec3(luminance), color, saturation);\n gl_FragColor = vec4(color, tex.a);\n}\n@end\n@export clay.compositor.brightness\nvarying vec2 v_Texcoord;\nuniform sampler2D texture;\nuniform float brightness : 0.0;\nvoid main()\n{\n vec4 tex = texture2D( texture, v_Texcoord);\n vec3 color = tex.rgb + vec3(brightness);\n gl_FragColor = vec4(color, tex.a);\n}\n@end\n@export clay.compositor.contrast\nvarying vec2 v_Texcoord;\nuniform sampler2D texture;\nuniform float contrast : 1.0;\nvoid main()\n{\n vec4 tex = texture2D( texture, v_Texcoord);\n vec3 color = (tex.rgb-vec3(0.5))*contrast+vec3(0.5);\n gl_FragColor = vec4(color, tex.a);\n}\n@end\n@export clay.compositor.exposure\nvarying vec2 v_Texcoord;\nuniform sampler2D texture;\nuniform float exposure : 0.0;\nvoid main()\n{\n vec4 tex = texture2D(texture, v_Texcoord);\n vec3 color = tex.rgb * pow(2.0, exposure);\n gl_FragColor = vec4(color, tex.a);\n}\n@end\n@export clay.compositor.gamma\nvarying vec2 v_Texcoord;\nuniform sampler2D texture;\nuniform float gamma : 1.0;\nvoid main()\n{\n vec4 tex = texture2D(texture, v_Texcoord);\n vec3 color = pow(tex.rgb, vec3(gamma));\n gl_FragColor = vec4(color, tex.a);\n}\n@end\n@export clay.compositor.saturation\nvarying vec2 v_Texcoord;\nuniform sampler2D texture;\nuniform float saturation : 1.0;\nconst vec3 w = vec3(0.2125, 0.7154, 0.0721);\nvoid main()\n{\n vec4 tex = texture2D(texture, v_Texcoord);\n vec3 color = tex.rgb;\n float luminance = dot(color, w);\n color = mix(vec3(luminance), color, saturation);\n gl_FragColor = vec4(color, tex.a);\n}\n@end",Yi="@export clay.compositor.kernel.gaussian_9\nfloat gaussianKernel[9];\ngaussianKernel[0] = 0.07;\ngaussianKernel[1] = 0.09;\ngaussianKernel[2] = 0.12;\ngaussianKernel[3] = 0.14;\ngaussianKernel[4] = 0.16;\ngaussianKernel[5] = 0.14;\ngaussianKernel[6] = 0.12;\ngaussianKernel[7] = 0.09;\ngaussianKernel[8] = 0.07;\n@end\n@export clay.compositor.kernel.gaussian_13\nfloat gaussianKernel[13];\ngaussianKernel[0] = 0.02;\ngaussianKernel[1] = 0.03;\ngaussianKernel[2] = 0.06;\ngaussianKernel[3] = 0.08;\ngaussianKernel[4] = 0.11;\ngaussianKernel[5] = 0.13;\ngaussianKernel[6] = 0.14;\ngaussianKernel[7] = 0.13;\ngaussianKernel[8] = 0.11;\ngaussianKernel[9] = 0.08;\ngaussianKernel[10] = 0.06;\ngaussianKernel[11] = 0.03;\ngaussianKernel[12] = 0.02;\n@end\n@export clay.compositor.gaussian_blur\n#define SHADER_NAME gaussian_blur\nuniform sampler2D texture;varying vec2 v_Texcoord;\nuniform float blurSize : 2.0;\nuniform vec2 textureSize : [512.0, 512.0];\nuniform float blurDir : 0.0;\n@import clay.util.rgbm\n@import clay.util.clamp_sample\nvoid main (void)\n{\n @import clay.compositor.kernel.gaussian_9\n vec2 off = blurSize / textureSize;\n off *= vec2(1.0 - blurDir, blurDir);\n vec4 sum = vec4(0.0);\n float weightAll = 0.0;\n for (int i = 0; i < 9; i++) {\n float w = gaussianKernel[i];\n vec4 texel = decodeHDR(clampSample(texture, v_Texcoord + float(i - 4) * off));\n sum += texel * w;\n weightAll += w;\n }\n gl_FragColor = encodeHDR(sum / max(weightAll, 0.01));\n}\n@end\n",Ki="@export clay.compositor.hdr.log_lum\nvarying vec2 v_Texcoord;\nuniform sampler2D texture;\nconst vec3 w = vec3(0.2125, 0.7154, 0.0721);\n@import clay.util.rgbm\nvoid main()\n{\n vec4 tex = decodeHDR(texture2D(texture, v_Texcoord));\n float luminance = dot(tex.rgb, w);\n luminance = log(luminance + 0.001);\n gl_FragColor = encodeHDR(vec4(vec3(luminance), 1.0));\n}\n@end\n@export clay.compositor.hdr.lum_adaption\nvarying vec2 v_Texcoord;\nuniform sampler2D adaptedLum;\nuniform sampler2D currentLum;\nuniform float frameTime : 0.02;\n@import clay.util.rgbm\nvoid main()\n{\n float fAdaptedLum = decodeHDR(texture2D(adaptedLum, vec2(0.5, 0.5))).r;\n float fCurrentLum = exp(encodeHDR(texture2D(currentLum, vec2(0.5, 0.5))).r);\n fAdaptedLum += (fCurrentLum - fAdaptedLum) * (1.0 - pow(0.98, 30.0 * frameTime));\n gl_FragColor = encodeHDR(vec4(vec3(fAdaptedLum), 1.0));\n}\n@end\n@export clay.compositor.lum\nvarying vec2 v_Texcoord;\nuniform sampler2D texture;\nconst vec3 w = vec3(0.2125, 0.7154, 0.0721);\nvoid main()\n{\n vec4 tex = texture2D( texture, v_Texcoord );\n float luminance = dot(tex.rgb, w);\n gl_FragColor = vec4(vec3(luminance), 1.0);\n}\n@end",Zi="\n@export clay.compositor.lut\nvarying vec2 v_Texcoord;\nuniform sampler2D texture;\nuniform sampler2D lookup;\nvoid main()\n{\n vec4 tex = texture2D(texture, v_Texcoord);\n float blueColor = tex.b * 63.0;\n vec2 quad1;\n quad1.y = floor(floor(blueColor) / 8.0);\n quad1.x = floor(blueColor) - (quad1.y * 8.0);\n vec2 quad2;\n quad2.y = floor(ceil(blueColor) / 8.0);\n quad2.x = ceil(blueColor) - (quad2.y * 8.0);\n vec2 texPos1;\n texPos1.x = (quad1.x * 0.125) + 0.5/512.0 + ((0.125 - 1.0/512.0) * tex.r);\n texPos1.y = (quad1.y * 0.125) + 0.5/512.0 + ((0.125 - 1.0/512.0) * tex.g);\n vec2 texPos2;\n texPos2.x = (quad2.x * 0.125) + 0.5/512.0 + ((0.125 - 1.0/512.0) * tex.r);\n texPos2.y = (quad2.y * 0.125) + 0.5/512.0 + ((0.125 - 1.0/512.0) * tex.g);\n vec4 newColor1 = texture2D(lookup, texPos1);\n vec4 newColor2 = texture2D(lookup, texPos2);\n vec4 newColor = mix(newColor1, newColor2, fract(blueColor));\n gl_FragColor = vec4(newColor.rgb, tex.w);\n}\n@end",Ji="@export clay.compositor.vignette\n#define OUTPUT_ALPHA\nvarying vec2 v_Texcoord;\nuniform sampler2D texture;\nuniform float darkness: 1;\nuniform float offset: 1;\n@import clay.util.rgbm\nvoid main()\n{\n vec4 texel = decodeHDR(texture2D(texture, v_Texcoord));\n gl_FragColor.rgb = texel.rgb;\n vec2 uv = (v_Texcoord - vec2(0.5)) * vec2(offset);\n gl_FragColor = encodeHDR(vec4(mix(texel.rgb, vec3(1.0 - darkness), dot(uv, uv)), texel.a));\n}\n@end",Qi="@export clay.compositor.output\n#define OUTPUT_ALPHA\nvarying vec2 v_Texcoord;\nuniform sampler2D texture;\n@import clay.util.rgbm\nvoid main()\n{\n vec4 tex = decodeHDR(texture2D(texture, v_Texcoord));\n gl_FragColor.rgb = tex.rgb;\n#ifdef OUTPUT_ALPHA\n gl_FragColor.a = tex.a;\n#else\n gl_FragColor.a = 1.0;\n#endif\n gl_FragColor = encodeHDR(gl_FragColor);\n#ifdef PREMULTIPLY_ALPHA\n gl_FragColor.rgb *= gl_FragColor.a;\n#endif\n}\n@end",$i="@export clay.compositor.bright\nuniform sampler2D texture;\nuniform float threshold : 1;\nuniform float scale : 1.0;\nuniform vec2 textureSize: [512, 512];\nvarying vec2 v_Texcoord;\nconst vec3 lumWeight = vec3(0.2125, 0.7154, 0.0721);\n@import clay.util.rgbm\nvec4 median(vec4 a, vec4 b, vec4 c)\n{\n return a + b + c - min(min(a, b), c) - max(max(a, b), c);\n}\nvoid main()\n{\n vec4 texel = decodeHDR(texture2D(texture, v_Texcoord));\n#ifdef ANTI_FLICKER\n vec3 d = 1.0 / textureSize.xyx * vec3(1.0, 1.0, 0.0);\n vec4 s1 = decodeHDR(texture2D(texture, v_Texcoord - d.xz));\n vec4 s2 = decodeHDR(texture2D(texture, v_Texcoord + d.xz));\n vec4 s3 = decodeHDR(texture2D(texture, v_Texcoord - d.zy));\n vec4 s4 = decodeHDR(texture2D(texture, v_Texcoord + d.zy));\n texel = median(median(texel, s1, s2), s3, s4);\n#endif\n float lum = dot(texel.rgb , lumWeight);\n vec4 color;\n if (lum > threshold && texel.a > 0.0)\n {\n color = vec4(texel.rgb * scale, texel.a * scale);\n }\n else\n {\n color = vec4(0.0);\n }\n gl_FragColor = encodeHDR(color);\n}\n@end\n",ta="@export clay.compositor.downsample\nuniform sampler2D texture;\nuniform vec2 textureSize : [512, 512];\nvarying vec2 v_Texcoord;\n@import clay.util.rgbm\nfloat brightness(vec3 c)\n{\n return max(max(c.r, c.g), c.b);\n}\n@import clay.util.clamp_sample\nvoid main()\n{\n vec4 d = vec4(-1.0, -1.0, 1.0, 1.0) / textureSize.xyxy;\n#ifdef ANTI_FLICKER\n vec3 s1 = decodeHDR(clampSample(texture, v_Texcoord + d.xy)).rgb;\n vec3 s2 = decodeHDR(clampSample(texture, v_Texcoord + d.zy)).rgb;\n vec3 s3 = decodeHDR(clampSample(texture, v_Texcoord + d.xw)).rgb;\n vec3 s4 = decodeHDR(clampSample(texture, v_Texcoord + d.zw)).rgb;\n float s1w = 1.0 / (brightness(s1) + 1.0);\n float s2w = 1.0 / (brightness(s2) + 1.0);\n float s3w = 1.0 / (brightness(s3) + 1.0);\n float s4w = 1.0 / (brightness(s4) + 1.0);\n float oneDivideSum = 1.0 / (s1w + s2w + s3w + s4w);\n vec4 color = vec4(\n (s1 * s1w + s2 * s2w + s3 * s3w + s4 * s4w) * oneDivideSum,\n 1.0\n );\n#else\n vec4 color = decodeHDR(clampSample(texture, v_Texcoord + d.xy));\n color += decodeHDR(clampSample(texture, v_Texcoord + d.zy));\n color += decodeHDR(clampSample(texture, v_Texcoord + d.xw));\n color += decodeHDR(clampSample(texture, v_Texcoord + d.zw));\n color *= 0.25;\n#endif\n gl_FragColor = encodeHDR(color);\n}\n@end",ea="\n@export clay.compositor.upsample\n#define HIGH_QUALITY\nuniform sampler2D texture;\nuniform vec2 textureSize : [512, 512];\nuniform float sampleScale: 0.5;\nvarying vec2 v_Texcoord;\n@import clay.util.rgbm\n@import clay.util.clamp_sample\nvoid main()\n{\n#ifdef HIGH_QUALITY\n vec4 d = vec4(1.0, 1.0, -1.0, 0.0) / textureSize.xyxy * sampleScale;\n vec4 s;\n s = decodeHDR(clampSample(texture, v_Texcoord - d.xy));\n s += decodeHDR(clampSample(texture, v_Texcoord - d.wy)) * 2.0;\n s += decodeHDR(clampSample(texture, v_Texcoord - d.zy));\n s += decodeHDR(clampSample(texture, v_Texcoord + d.zw)) * 2.0;\n s += decodeHDR(clampSample(texture, v_Texcoord )) * 4.0;\n s += decodeHDR(clampSample(texture, v_Texcoord + d.xw)) * 2.0;\n s += decodeHDR(clampSample(texture, v_Texcoord + d.zy));\n s += decodeHDR(clampSample(texture, v_Texcoord + d.wy)) * 2.0;\n s += decodeHDR(clampSample(texture, v_Texcoord + d.xy));\n gl_FragColor = encodeHDR(s / 16.0);\n#else\n vec4 d = vec4(-1.0, -1.0, +1.0, +1.0) / textureSize.xyxy;\n vec4 s;\n s = decodeHDR(clampSample(texture, v_Texcoord + d.xy));\n s += decodeHDR(clampSample(texture, v_Texcoord + d.zy));\n s += decodeHDR(clampSample(texture, v_Texcoord + d.xw));\n s += decodeHDR(clampSample(texture, v_Texcoord + d.zw));\n gl_FragColor = encodeHDR(s / 4.0);\n#endif\n}\n@end",ra="@export clay.compositor.hdr.composite\n#define TONEMAPPING\nuniform sampler2D texture;\n#ifdef BLOOM_ENABLED\nuniform sampler2D bloom;\n#endif\n#ifdef LENSFLARE_ENABLED\nuniform sampler2D lensflare;\nuniform sampler2D lensdirt;\n#endif\n#ifdef LUM_ENABLED\nuniform sampler2D lum;\n#endif\n#ifdef LUT_ENABLED\nuniform sampler2D lut;\n#endif\n#ifdef COLOR_CORRECTION\nuniform float brightness : 0.0;\nuniform float contrast : 1.0;\nuniform float saturation : 1.0;\n#endif\n#ifdef VIGNETTE\nuniform float vignetteDarkness: 1.0;\nuniform float vignetteOffset: 1.0;\n#endif\nuniform float exposure : 1.0;\nuniform float bloomIntensity : 0.25;\nuniform float lensflareIntensity : 1;\nvarying vec2 v_Texcoord;\n@import clay.util.srgb\nvec3 ACESToneMapping(vec3 color)\n{\n const float A = 2.51;\n const float B = 0.03;\n const float C = 2.43;\n const float D = 0.59;\n const float E = 0.14;\n return (color * (A * color + B)) / (color * (C * color + D) + E);\n}\nfloat eyeAdaption(float fLum)\n{\n return mix(0.2, fLum, 0.5);\n}\n#ifdef LUT_ENABLED\nvec3 lutTransform(vec3 color) {\n float blueColor = color.b * 63.0;\n vec2 quad1;\n quad1.y = floor(floor(blueColor) / 8.0);\n quad1.x = floor(blueColor) - (quad1.y * 8.0);\n vec2 quad2;\n quad2.y = floor(ceil(blueColor) / 8.0);\n quad2.x = ceil(blueColor) - (quad2.y * 8.0);\n vec2 texPos1;\n texPos1.x = (quad1.x * 0.125) + 0.5/512.0 + ((0.125 - 1.0/512.0) * color.r);\n texPos1.y = (quad1.y * 0.125) + 0.5/512.0 + ((0.125 - 1.0/512.0) * color.g);\n vec2 texPos2;\n texPos2.x = (quad2.x * 0.125) + 0.5/512.0 + ((0.125 - 1.0/512.0) * color.r);\n texPos2.y = (quad2.y * 0.125) + 0.5/512.0 + ((0.125 - 1.0/512.0) * color.g);\n vec4 newColor1 = texture2D(lut, texPos1);\n vec4 newColor2 = texture2D(lut, texPos2);\n vec4 newColor = mix(newColor1, newColor2, fract(blueColor));\n return newColor.rgb;\n}\n#endif\n@import clay.util.rgbm\nvoid main()\n{\n vec4 texel = vec4(0.0);\n vec4 originalTexel = vec4(0.0);\n#ifdef TEXTURE_ENABLED\n texel = decodeHDR(texture2D(texture, v_Texcoord));\n originalTexel = texel;\n#endif\n#ifdef BLOOM_ENABLED\n vec4 bloomTexel = decodeHDR(texture2D(bloom, v_Texcoord));\n texel.rgb += bloomTexel.rgb * bloomIntensity;\n texel.a += bloomTexel.a * bloomIntensity;\n#endif\n#ifdef LENSFLARE_ENABLED\n texel += decodeHDR(texture2D(lensflare, v_Texcoord)) * texture2D(lensdirt, v_Texcoord) * lensflareIntensity;\n#endif\n texel.a = min(texel.a, 1.0);\n#ifdef LUM_ENABLED\n float fLum = texture2D(lum, vec2(0.5, 0.5)).r;\n float adaptedLumDest = 3.0 / (max(0.1, 1.0 + 10.0*eyeAdaption(fLum)));\n float exposureBias = adaptedLumDest * exposure;\n#else\n float exposureBias = exposure;\n#endif\n#ifdef TONEMAPPING\n texel.rgb *= exposureBias;\n texel.rgb = ACESToneMapping(texel.rgb);\n#endif\n texel = linearTosRGB(texel);\n#ifdef LUT_ENABLED\n texel.rgb = lutTransform(clamp(texel.rgb,vec3(0.0),vec3(1.0)));\n#endif\n#ifdef COLOR_CORRECTION\n texel.rgb = clamp(texel.rgb + vec3(brightness), 0.0, 1.0);\n texel.rgb = clamp((texel.rgb - vec3(0.5))*contrast+vec3(0.5), 0.0, 1.0);\n float lum = dot(texel.rgb, vec3(0.2125, 0.7154, 0.0721));\n texel.rgb = mix(vec3(lum), texel.rgb, saturation);\n#endif\n#ifdef VIGNETTE\n vec2 uv = (v_Texcoord - vec2(0.5)) * vec2(vignetteOffset);\n texel.rgb = mix(texel.rgb, vec3(1.0 - vignetteDarkness), dot(uv, uv));\n#endif\n gl_FragColor = encodeHDR(texel);\n#ifdef DEBUG\n #if DEBUG == 1\n gl_FragColor = encodeHDR(decodeHDR(texture2D(texture, v_Texcoord)));\n #elif DEBUG == 2\n gl_FragColor = encodeHDR(decodeHDR(texture2D(bloom, v_Texcoord)) * bloomIntensity);\n #elif DEBUG == 3\n gl_FragColor = encodeHDR(decodeHDR(texture2D(lensflare, v_Texcoord) * lensflareIntensity));\n #endif\n#endif\n if (originalTexel.a <= 0.01 && gl_FragColor.a > 1e-5) {\n gl_FragColor.a = dot(gl_FragColor.rgb, vec3(0.2125, 0.7154, 0.0721));\n }\n#ifdef PREMULTIPLY_ALPHA\n gl_FragColor.rgb *= gl_FragColor.a;\n#endif\n}\n@end",na="@export clay.compositor.lensflare\n#define SAMPLE_NUMBER 8\nuniform sampler2D texture;\nuniform sampler2D lenscolor;\nuniform vec2 textureSize : [512, 512];\nuniform float dispersal : 0.3;\nuniform float haloWidth : 0.4;\nuniform float distortion : 1.0;\nvarying vec2 v_Texcoord;\n@import clay.util.rgbm\nvec4 textureDistorted(\n in vec2 texcoord,\n in vec2 direction,\n in vec3 distortion\n) {\n return vec4(\n decodeHDR(texture2D(texture, texcoord + direction * distortion.r)).r,\n decodeHDR(texture2D(texture, texcoord + direction * distortion.g)).g,\n decodeHDR(texture2D(texture, texcoord + direction * distortion.b)).b,\n 1.0\n );\n}\nvoid main()\n{\n vec2 texcoord = -v_Texcoord + vec2(1.0); vec2 textureOffset = 1.0 / textureSize;\n vec2 ghostVec = (vec2(0.5) - texcoord) * dispersal;\n vec2 haloVec = normalize(ghostVec) * haloWidth;\n vec3 distortion = vec3(-textureOffset.x * distortion, 0.0, textureOffset.x * distortion);\n vec4 result = vec4(0.0);\n for (int i = 0; i < SAMPLE_NUMBER; i++)\n {\n vec2 offset = fract(texcoord + ghostVec * float(i));\n float weight = length(vec2(0.5) - offset) / length(vec2(0.5));\n weight = pow(1.0 - weight, 10.0);\n result += textureDistorted(offset, normalize(ghostVec), distortion) * weight;\n }\n result *= texture2D(lenscolor, vec2(length(vec2(0.5) - texcoord)) / length(vec2(0.5)));\n float weight = length(vec2(0.5) - fract(texcoord + haloVec)) / length(vec2(0.5));\n weight = pow(1.0 - weight, 10.0);\n vec2 offset = fract(texcoord + haloVec);\n result += textureDistorted(offset, normalize(ghostVec), distortion) * weight;\n gl_FragColor = result;\n}\n@end",ia="@export clay.compositor.blend\n#define SHADER_NAME blend\n#ifdef TEXTURE1_ENABLED\nuniform sampler2D texture1;\nuniform float weight1 : 1.0;\n#endif\n#ifdef TEXTURE2_ENABLED\nuniform sampler2D texture2;\nuniform float weight2 : 1.0;\n#endif\n#ifdef TEXTURE3_ENABLED\nuniform sampler2D texture3;\nuniform float weight3 : 1.0;\n#endif\n#ifdef TEXTURE4_ENABLED\nuniform sampler2D texture4;\nuniform float weight4 : 1.0;\n#endif\n#ifdef TEXTURE5_ENABLED\nuniform sampler2D texture5;\nuniform float weight5 : 1.0;\n#endif\n#ifdef TEXTURE6_ENABLED\nuniform sampler2D texture6;\nuniform float weight6 : 1.0;\n#endif\nvarying vec2 v_Texcoord;\n@import clay.util.rgbm\nvoid main()\n{\n vec4 tex = vec4(0.0);\n#ifdef TEXTURE1_ENABLED\n tex += decodeHDR(texture2D(texture1, v_Texcoord)) * weight1;\n#endif\n#ifdef TEXTURE2_ENABLED\n tex += decodeHDR(texture2D(texture2, v_Texcoord)) * weight2;\n#endif\n#ifdef TEXTURE3_ENABLED\n tex += decodeHDR(texture2D(texture3, v_Texcoord)) * weight3;\n#endif\n#ifdef TEXTURE4_ENABLED\n tex += decodeHDR(texture2D(texture4, v_Texcoord)) * weight4;\n#endif\n#ifdef TEXTURE5_ENABLED\n tex += decodeHDR(texture2D(texture5, v_Texcoord)) * weight5;\n#endif\n#ifdef TEXTURE6_ENABLED\n tex += decodeHDR(texture2D(texture6, v_Texcoord)) * weight6;\n#endif\n gl_FragColor = encodeHDR(tex);\n}\n@end",aa="@export clay.compositor.fxaa\nuniform sampler2D texture;\nuniform vec4 viewport : VIEWPORT;\nvarying vec2 v_Texcoord;\n#define FXAA_REDUCE_MIN (1.0/128.0)\n#define FXAA_REDUCE_MUL (1.0/8.0)\n#define FXAA_SPAN_MAX 8.0\n@import clay.util.rgbm\nvoid main()\n{\n vec2 resolution = 1.0 / viewport.zw;\n vec3 rgbNW = decodeHDR( texture2D( texture, ( gl_FragCoord.xy + vec2( -1.0, -1.0 ) ) * resolution ) ).xyz;\n vec3 rgbNE = decodeHDR( texture2D( texture, ( gl_FragCoord.xy + vec2( 1.0, -1.0 ) ) * resolution ) ).xyz;\n vec3 rgbSW = decodeHDR( texture2D( texture, ( gl_FragCoord.xy + vec2( -1.0, 1.0 ) ) * resolution ) ).xyz;\n vec3 rgbSE = decodeHDR( texture2D( texture, ( gl_FragCoord.xy + vec2( 1.0, 1.0 ) ) * resolution ) ).xyz;\n vec4 rgbaM = decodeHDR( texture2D( texture, gl_FragCoord.xy * resolution ) );\n vec3 rgbM = rgbaM.xyz;\n float opacity = rgbaM.w;\n vec3 luma = vec3( 0.299, 0.587, 0.114 );\n float lumaNW = dot( rgbNW, luma );\n float lumaNE = dot( rgbNE, luma );\n float lumaSW = dot( rgbSW, luma );\n float lumaSE = dot( rgbSE, luma );\n float lumaM = dot( rgbM, luma );\n float lumaMin = min( lumaM, min( min( lumaNW, lumaNE ), min( lumaSW, lumaSE ) ) );\n float lumaMax = max( lumaM, max( max( lumaNW, lumaNE) , max( lumaSW, lumaSE ) ) );\n vec2 dir;\n dir.x = -((lumaNW + lumaNE) - (lumaSW + lumaSE));\n dir.y = ((lumaNW + lumaSW) - (lumaNE + lumaSE));\n float dirReduce = max( ( lumaNW + lumaNE + lumaSW + lumaSE ) * ( 0.25 * FXAA_REDUCE_MUL ), FXAA_REDUCE_MIN );\n float rcpDirMin = 1.0 / ( min( abs( dir.x ), abs( dir.y ) ) + dirReduce );\n dir = min( vec2( FXAA_SPAN_MAX, FXAA_SPAN_MAX),\n max( vec2(-FXAA_SPAN_MAX, -FXAA_SPAN_MAX),\n dir * rcpDirMin)) * resolution;\n vec3 rgbA = decodeHDR( texture2D( texture, gl_FragCoord.xy * resolution + dir * ( 1.0 / 3.0 - 0.5 ) ) ).xyz;\n rgbA += decodeHDR( texture2D( texture, gl_FragCoord.xy * resolution + dir * ( 2.0 / 3.0 - 0.5 ) ) ).xyz;\n rgbA *= 0.5;\n vec3 rgbB = decodeHDR( texture2D( texture, gl_FragCoord.xy * resolution + dir * -0.5 ) ).xyz;\n rgbB += decodeHDR( texture2D( texture, gl_FragCoord.xy * resolution + dir * 0.5 ) ).xyz;\n rgbB *= 0.25;\n rgbB += rgbA * 0.5;\n float lumaB = dot( rgbB, luma );\n if ( ( lumaB < lumaMin ) || ( lumaB > lumaMax ) )\n {\n gl_FragColor = vec4( rgbA, opacity );\n }\n else {\n gl_FragColor = vec4( rgbB, opacity );\n }\n}\n@end";Yt(Q);var oa=/^#source\((.*?)\)/;Q.import("@export clay.deferred.gbuffer.vertex\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\nattribute vec3 position : POSITION;\n#if defined(SECOND_PASS) || defined(FIRST_PASS)\nattribute vec2 texcoord : TEXCOORD_0;\nuniform vec2 uvRepeat;\nuniform vec2 uvOffset;\nvarying vec2 v_Texcoord;\n#endif\n#ifdef FIRST_PASS\nuniform mat4 worldInverseTranspose : WORLDINVERSETRANSPOSE;\nuniform mat4 world : WORLD;\nvarying vec3 v_Normal;\nattribute vec3 normal : NORMAL;\nattribute vec4 tangent : TANGENT;\nvarying vec3 v_Tangent;\nvarying vec3 v_Bitangent;\nvarying vec3 v_WorldPosition;\n#endif\n@import clay.chunk.skinning_header\n#ifdef THIRD_PASS\nuniform mat4 prevWorldViewProjection;\nvarying vec4 v_ViewPosition;\nvarying vec4 v_PrevViewPosition;\n#ifdef SKINNING\n#ifdef USE_SKIN_MATRICES_TEXTURE\nuniform sampler2D prevSkinMatricesTexture;\nmat4 getPrevSkinMatrix(float idx) {\n return getSkinMatrix(prevSkinMatricesTexture, idx);\n}\n#else\nuniform mat4 prevSkinMatrix[JOINT_COUNT];\nmat4 getPrevSkinMatrix(float idx) {\n return prevSkinMatrix[int(idx)];\n}\n#endif\n#endif\n#endif\nvoid main()\n{\n vec3 skinnedPosition = position;\n vec3 prevSkinnedPosition = position;\n#ifdef FIRST_PASS\n vec3 skinnedNormal = normal;\n vec3 skinnedTangent = tangent.xyz;\n bool hasTangent = dot(tangent, tangent) > 0.0;\n#endif\n#ifdef SKINNING\n @import clay.chunk.skin_matrix\n skinnedPosition = (skinMatrixWS * vec4(position, 1.0)).xyz;\n #ifdef FIRST_PASS\n skinnedNormal = (skinMatrixWS * vec4(normal, 0.0)).xyz;\n if (hasTangent) {\n skinnedTangent = (skinMatrixWS * vec4(tangent.xyz, 0.0)).xyz;\n }\n #endif\n #ifdef THIRD_PASS\n {\n mat4 prevSkinMatrixWS = getPrevSkinMatrix(joint.x) * weight.x;\n if (weight.y > 1e-4) { prevSkinMatrixWS += getPrevSkinMatrix(joint.y) * weight.y; }\n if (weight.z > 1e-4) { prevSkinMatrixWS += getPrevSkinMatrix(joint.z) * weight.z; }\n float weightW = 1.0-weight.x-weight.y-weight.z;\n if (weightW > 1e-4) { prevSkinMatrixWS += getPrevSkinMatrix(joint.w) * weightW; }\n prevSkinnedPosition = (prevSkinMatrixWS * vec4(position, 1.0)).xyz;\n }\n #endif\n#endif\n#if defined(SECOND_PASS) || defined(FIRST_PASS)\n v_Texcoord = texcoord * uvRepeat + uvOffset;\n#endif\n#ifdef FIRST_PASS\n v_Normal = normalize((worldInverseTranspose * vec4(skinnedNormal, 0.0)).xyz);\n if (hasTangent) {\n v_Tangent = normalize((worldInverseTranspose * vec4(skinnedTangent, 0.0)).xyz);\n v_Bitangent = normalize(cross(v_Normal, v_Tangent) * tangent.w);\n }\n v_WorldPosition = (world * vec4(skinnedPosition, 1.0)).xyz;\n#endif\n#ifdef THIRD_PASS\n v_ViewPosition = worldViewProjection * vec4(skinnedPosition, 1.0);\n v_PrevViewPosition = prevWorldViewProjection * vec4(prevSkinnedPosition, 1.0);\n#endif\n gl_Position = worldViewProjection * vec4(skinnedPosition, 1.0);\n}\n@end\n@export clay.deferred.gbuffer1.fragment\nuniform mat4 viewInverse : VIEWINVERSE;\nuniform float glossiness;\nvarying vec2 v_Texcoord;\nvarying vec3 v_Normal;\nvarying vec3 v_WorldPosition;\nuniform sampler2D normalMap;\nuniform sampler2D diffuseMap;\nvarying vec3 v_Tangent;\nvarying vec3 v_Bitangent;\nuniform sampler2D roughGlossMap;\nuniform bool useRoughGlossMap;\nuniform bool useRoughness;\nuniform bool doubleSided;\nuniform float alphaCutoff: 0.0;\nuniform float alpha: 1.0;\nuniform int roughGlossChannel: 0;\nfloat indexingTexel(in vec4 texel, in int idx) {\n if (idx == 3) return texel.a;\n else if (idx == 1) return texel.g;\n else if (idx == 2) return texel.b;\n else return texel.r;\n}\nvoid main()\n{\n vec3 N = v_Normal;\n if (doubleSided) {\n vec3 eyePos = viewInverse[3].xyz;\n vec3 V = eyePos - v_WorldPosition;\n if (dot(N, V) < 0.0) {\n N = -N;\n }\n }\n if (alphaCutoff > 0.0) {\n float a = texture2D(diffuseMap, v_Texcoord).a * alpha;\n if (a < alphaCutoff) {\n discard;\n }\n }\n if (dot(v_Tangent, v_Tangent) > 0.0) {\n vec3 normalTexel = texture2D(normalMap, v_Texcoord).xyz;\n if (dot(normalTexel, normalTexel) > 0.0) { N = normalTexel * 2.0 - 1.0;\n mat3 tbn = mat3(v_Tangent, v_Bitangent, v_Normal);\n N = normalize(tbn * N);\n }\n }\n gl_FragColor.rgb = (N + 1.0) * 0.5;\n float g = glossiness;\n if (useRoughGlossMap) {\n float g2 = indexingTexel(texture2D(roughGlossMap, v_Texcoord), roughGlossChannel);\n if (useRoughness) {\n g2 = 1.0 - g2;\n }\n g = clamp(g2 + (g - 0.5) * 2.0, 0.0, 1.0);\n }\n gl_FragColor.a = g + 0.005;\n}\n@end\n@export clay.deferred.gbuffer2.fragment\nuniform sampler2D diffuseMap;\nuniform sampler2D metalnessMap;\nuniform vec3 color;\nuniform float metalness;\nuniform bool useMetalnessMap;\nuniform bool linear;\nuniform float alphaCutoff: 0.0;\nuniform float alpha: 1.0;\nvarying vec2 v_Texcoord;\n@import clay.util.srgb\nvoid main()\n{\n float m = metalness;\n if (useMetalnessMap) {\n vec4 metalnessTexel = texture2D(metalnessMap, v_Texcoord);\n m = clamp(metalnessTexel.r + (m * 2.0 - 1.0), 0.0, 1.0);\n }\n vec4 texel = texture2D(diffuseMap, v_Texcoord);\n if (linear) {\n texel = sRGBToLinear(texel);\n }\n if (alphaCutoff > 0.0) {\n float a = texel.a * alpha;\n if (a < alphaCutoff) {\n discard;\n }\n }\n gl_FragColor.rgb = texel.rgb * color;\n gl_FragColor.a = m + 0.005;\n}\n@end\n@export clay.deferred.gbuffer3.fragment\nuniform bool firstRender;\nvarying vec4 v_ViewPosition;\nvarying vec4 v_PrevViewPosition;\nvoid main()\n{\n vec2 a = v_ViewPosition.xy / v_ViewPosition.w;\n vec2 b = v_PrevViewPosition.xy / v_PrevViewPosition.w;\n if (firstRender) {\n gl_FragColor = vec4(0.0);\n }\n else {\n gl_FragColor = vec4((a - b) * 0.5 + 0.5, 0.0, 1.0);\n }\n}\n@end\n@export clay.deferred.gbuffer.debug\n@import clay.deferred.chunk.light_head\nuniform sampler2D gBufferTexture4;\nuniform int debug: 0;\nvoid main ()\n{\n @import clay.deferred.chunk.gbuffer_read\n if (debug == 0) {\n gl_FragColor = vec4(N, 1.0);\n }\n else if (debug == 1) {\n gl_FragColor = vec4(vec3(z), 1.0);\n }\n else if (debug == 2) {\n gl_FragColor = vec4(position, 1.0);\n }\n else if (debug == 3) {\n gl_FragColor = vec4(vec3(glossiness), 1.0);\n }\n else if (debug == 4) {\n gl_FragColor = vec4(vec3(metalness), 1.0);\n }\n else if (debug == 5) {\n gl_FragColor = vec4(albedo, 1.0);\n }\n else {\n vec4 color = texture2D(gBufferTexture4, uv);\n color.rg -= 0.5;\n color.rg *= 2.0;\n gl_FragColor = color;\n }\n}\n@end"),Q.import("@export clay.deferred.chunk.light_head\nuniform sampler2D gBufferTexture1;\nuniform sampler2D gBufferTexture2;\nuniform sampler2D gBufferTexture3;\nuniform vec2 windowSize: WINDOW_SIZE;\nuniform vec4 viewport: VIEWPORT;\nuniform mat4 viewProjectionInv;\n#ifdef DEPTH_ENCODED\n@import clay.util.decode_float\n#endif\n@end\n@export clay.deferred.chunk.gbuffer_read\n vec2 uv = gl_FragCoord.xy / windowSize;\n vec2 uv2 = (gl_FragCoord.xy - viewport.xy) / viewport.zw;\n vec4 texel1 = texture2D(gBufferTexture1, uv);\n vec4 texel3 = texture2D(gBufferTexture3, uv);\n if (dot(texel1.rgb, vec3(1.0)) == 0.0) {\n discard;\n }\n float glossiness = texel1.a;\n float metalness = texel3.a;\n vec3 N = texel1.rgb * 2.0 - 1.0;\n float z = texture2D(gBufferTexture2, uv).r * 2.0 - 1.0;\n vec2 xy = uv2 * 2.0 - 1.0;\n vec4 projectedPos = vec4(xy, z, 1.0);\n vec4 p4 = viewProjectionInv * projectedPos;\n vec3 position = p4.xyz / p4.w;\n vec3 albedo = texel3.rgb;\n vec3 diffuseColor = albedo * (1.0 - metalness);\n vec3 specularColor = mix(vec3(0.04), albedo, metalness);\n@end\n@export clay.deferred.chunk.light_equation\nfloat D_Phong(in float g, in float ndh) {\n float a = pow(8192.0, g);\n return (a + 2.0) / 8.0 * pow(ndh, a);\n}\nfloat D_GGX(in float g, in float ndh) {\n float r = 1.0 - g;\n float a = r * r;\n float tmp = ndh * ndh * (a - 1.0) + 1.0;\n return a / (3.1415926 * tmp * tmp);\n}\nvec3 F_Schlick(in float ndv, vec3 spec) {\n return spec + (1.0 - spec) * pow(1.0 - ndv, 5.0);\n}\nvec3 lightEquation(\n in vec3 lightColor, in vec3 diffuseColor, in vec3 specularColor,\n in float ndl, in float ndh, in float ndv, in float g\n)\n{\n return ndl * lightColor\n * (diffuseColor + D_Phong(g, ndh) * F_Schlick(ndv, specularColor));\n}\n@end");var sa=ke.extend(function(){var t={minFilter:Tn.NEAREST,magFilter:Tn.NEAREST,wrapS:Tn.CLAMP_TO_EDGE,wrapT:Tn.CLAMP_TO_EDGE};return{enableTargetTexture1:!0,enableTargetTexture2:!0,enableTargetTexture3:!0,enableTargetTexture4:!1,renderTransparent:!1,_gBufferRenderList:[],_gBufferTex1:new An(Object.assign({type:Tn.HALF_FLOAT},t)),_gBufferTex2:new An(Object.assign({format:Tn.DEPTH_STENCIL,type:Tn.UNSIGNED_INT_24_8_WEBGL},t)),_gBufferTex3:new An(t),_gBufferTex4:new An(Object.assign({type:Tn.HALF_FLOAT},t)),_defaultNormalMap:new An({image:ie("#000")}),_defaultRoughnessMap:new An({image:ie("#fff")}),_defaultMetalnessMap:new An({image:ie("#fff")}),_defaultDiffuseMap:new An({image:ie("#fff")}),_frameBuffer:new oi,_gBufferMaterial1:new nr({shader:new Q(Q.source("clay.deferred.gbuffer.vertex"),Q.source("clay.deferred.gbuffer1.fragment")),vertexDefines:{FIRST_PASS:null},fragmentDefines:{FIRST_PASS:null}}),_gBufferMaterial2:new nr({shader:new Q(Q.source("clay.deferred.gbuffer.vertex"),Q.source("clay.deferred.gbuffer2.fragment")),vertexDefines:{SECOND_PASS:null},fragmentDefines:{SECOND_PASS:null}}),_gBufferMaterial3:new nr({shader:new Q(Q.source("clay.deferred.gbuffer.vertex"),Q.source("clay.deferred.gbuffer3.fragment")),vertexDefines:{THIRD_PASS:null},fragmentDefines:{THIRD_PASS:null}}),_debugPass:new hi({fragment:Q.source("clay.deferred.gbuffer.debug")})}},{resize:function(t,e){this._gBufferTex1.width===t&&this._gBufferTex1.height===e||(this._gBufferTex1.width=t,this._gBufferTex1.height=e,this._gBufferTex2.width=t,this._gBufferTex2.height=e,this._gBufferTex3.width=t,this._gBufferTex3.height=e,this._gBufferTex4.width=t,this._gBufferTex4.height=e)},setViewport:function(t,e,r,n,i){var a;a="object"==typeof t?t:{x:t,y:e,width:r,height:n,devicePixelRatio:i||1},this._frameBuffer.viewport=a},getViewport:function(){return this._frameBuffer.viewport?this._frameBuffer.viewport:{x:0,y:0,width:this._gBufferTex1.width,height:this._gBufferTex1.height,devicePixelRatio:1}},update:function(t,e,r,n){function i(){if(u){var t=u.devicePixelRatio;o.enable(o.SCISSOR_TEST),o.scissor(u.x*t,u.y*t,u.width*t,u.height*t)}o.clear(o.COLOR_BUFFER_BIT|o.DEPTH_BUFFER_BIT),u&&o.disable(o.SCISSOR_TEST)}function a(t,e){return t.material!==e.material}n=n||{};for(var o=t.gl,s=this._frameBuffer,u=s.viewport,l=e.updateRenderList(r,!0),h=l.opaque,c=l.transparent,f=0,d=this._gBufferRenderList,p=0;p<h.length;p++)h[p].ignoreGBuffer||(d[f++]=h[p]);if(this.renderTransparent)for(var p=0;p<c.length;p++)c[p].ignoreGBuffer||(d[f++]=c[p]);d.length=f,o.clearColor(0,0,0,0),o.depthMask(!0),o.colorMask(!0,!0,!0,!0),o.disable(o.BLEND);var m=this.enableTargetTexture1,_=this.enableTargetTexture2,g=this.enableTargetTexture3,v=this.enableTargetTexture4;if(m||g||v||(console.warn("Can't disable targetTexture1, targetTexture3, targetTexture4 both"),m=!0),_&&s.attach(n.targetTexture2||this._gBufferTex2,t.gl.DEPTH_STENCIL_ATTACHMENT),t.bindSceneRendering(e),m){s.attach(n.targetTexture1||this._gBufferTex1),s.bind(t),i();var y=this._gBufferMaterial1,T={getMaterial:function(){return y},getUniform:ae(this._defaultNormalMap,this._defaultRoughnessMap,this._defaultDiffuseMap),isMaterialChanged:a,sortCompare:t.opaqueSortCompare};t.renderPass(d,r,T)}if(g){s.attach(n.targetTexture3||this._gBufferTex3),s.bind(t),i();var x=this._gBufferMaterial2,T={getMaterial:function(){return x},getUniform:oe(this._defaultDiffuseMap,this._defaultMetalnessMap),isMaterialChanged:a,sortCompare:t.opaqueSortCompare};t.renderPass(d,r,T)}if(v){s.bind(t),s.attach(n.targetTexture4||this._gBufferTex4),i(),r.update();var E=this._gBufferMaterial3,A=Er.create();Er.multiply(A,r.projectionMatrix.array,r.viewMatrix.array);var T={getMaterial:function(){return E},afterRender:function(t,e){var r=e.isSkinnedMesh();if(r){var n=e.skeleton,i=e.joints;if(i.length>t.getMaxJointNumber()){var a=n.getSubSkinMatricesTexture(e.__uid__,i),o=e.__prevSkinMatricesTexture;if(o||(o=e.__prevSkinMatricesTexture=new An({type:Tn.FLOAT,minFilter:Tn.NEAREST,magFilter:Tn.NEAREST,useMipmap:!1,flipY:!1})),o.pixels&&o.pixels.length===a.pixels.length)for(var s=0;s<a.pixels.length;s++)o.pixels[s]=a.pixels[s];else o.pixels=new Float32Array(a.pixels);o.width=a.width,o.height=a.height}else{var u=n.getSubSkinMatrices(e.__uid__,i)
;e.__prevSkinMatricesArray&&e.__prevSkinMatricesArray.length===u.length||(e.__prevSkinMatricesArray=new Float32Array(u.length)),e.__prevSkinMatricesArray.set(u)}}e.__prevWorldViewProjection=e.__prevWorldViewProjection||Er.create(),r?Er.copy(e.__prevWorldViewProjection,A):Er.multiply(e.__prevWorldViewProjection,A,e.worldTransform.array)},getUniform:function(t,e,r){return"prevWorldViewProjection"===r?t.__prevWorldViewProjection:"prevSkinMatrix"===r?t.__prevSkinMatricesArray:"prevSkinMatricesTexture"===r?t.__prevSkinMatricesTexture:"firstRender"===r?!t.__prevWorldViewProjection:e.get(r)},isMaterialChanged:function(){return!0},sortCompare:t.opaqueSortCompare};t.renderPass(d,r,T)}t.bindSceneRendering(null),s.unbind(t)},renderDebug:function(t,e,r,n){var i={normal:0,depth:1,position:2,glossiness:3,metalness:4,albedo:5,velocity:6};null==i[r]&&(console.warn('Unkown type "'+r+'"'),r="normal"),t.saveClear(),t.saveViewport(),t.clearBit=t.gl.DEPTH_BUFFER_BIT,n&&t.setViewport(n);var a=new Ur;Ur.multiply(a,e.worldTransform,e.invProjectionMatrix);var o=this._debugPass;o.setUniform("viewportSize",[t.getWidth(),t.getHeight()]),o.setUniform("gBufferTexture1",this._gBufferTex1),o.setUniform("gBufferTexture2",this._gBufferTex2),o.setUniform("gBufferTexture3",this._gBufferTex3),o.setUniform("gBufferTexture4",this._gBufferTex4),o.setUniform("debug",i[r]),o.setUniform("viewProjectionInv",a.array),o.render(t),t.restoreViewport(),t.restoreClear()},getTargetTexture1:function(){return this._gBufferTex1},getTargetTexture2:function(){return this._gBufferTex2},getTargetTexture3:function(){return this._gBufferTex3},getTargetTexture4:function(){return this._gBufferTex4},dispose:function(t){this._gBufferTex1.dispose(t),this._gBufferTex2.dispose(t),this._gBufferTex3.dispose(t),this._defaultNormalMap.dispose(t),this._defaultRoughnessMap.dispose(t),this._defaultMetalnessMap.dispose(t),this._defaultDiffuseMap.dispose(t),this._frameBuffer.dispose(t)}}),ua=pn.extend({dynamic:!1,topRadius:0,bottomRadius:1,height:2,capSegments:20,heightSegments:1},function(){this.build()},{build:function(){var t=[],e=[],r=[];t.length=0,e.length=0,r.length=0;for(var n=2*Math.PI/this.capSegments,i=[],a=[],o=this.topRadius,s=this.bottomRadius,u=this.height/2,l=Ce.fromValues(0,u,0),h=Ce.fromValues(0,-u,0),c=0;c<this.capSegments;c++){var f=c*n,d=o*Math.sin(f),p=o*Math.cos(f);i.push(Ce.fromValues(d,u,p)),d=s*Math.sin(f),p=s*Math.cos(f),a.push(Ce.fromValues(d,-u,p))}t.push(l),e.push(be.fromValues(0,1));for(var m=this.capSegments,c=0;c<m;c++)t.push(i[c]),e.push(be.fromValues(c/m,0)),r.push([0,c+1,(c+1)%m+1]);var _=t.length;t.push(h),e.push(be.fromValues(0,1));for(var c=0;c<m;c++)t.push(a[c]),e.push(be.fromValues(c/m,0)),r.push([_,_+((c+1)%m+1),_+c+1]);_=t.length;for(var g=this.heightSegments,c=0;c<m;c++)for(var v=0;v<g+1;v++){var y=v/g;t.push(Ce.lerp(Ce.create(),i[c],a[c],y)),e.push(be.fromValues(c/m,y))}for(var c=0;c<m;c++)for(var v=0;v<g;v++){var T=c*(g+1)+v,x=(c+1)%m*(g+1)+v,E=(c+1)%m*(g+1)+v+1,A=c*(g+1)+v+1;r.push([_+x,_+T,_+A]),r.push([_+A,_+E,_+x])}this.attributes.position.fromArray(t),this.attributes.texcoord0.fromArray(e),this.initIndicesFromArray(r),this.generateVertexNormals(),this.boundingBox=new Vr;var b=Math.max(this.topRadius,this.bottomRadius);this.boundingBox.min.set(-b,-this.height/2,-b),this.boundingBox.max.set(b,this.height/2,b)}}),la=pn.extend({dynamic:!1,radius:1,height:2,capSegments:50,heightSegments:1},function(){this.build()},{build:function(){var t=new ua({topRadius:this.radius,bottomRadius:this.radius,capSegments:this.capSegments,heightSegments:this.heightSegments,height:this.height});this.attributes.position.value=t.attributes.position.value,this.attributes.normal.value=t.attributes.normal.value,this.attributes.texcoord0.value=t.attributes.texcoord0.value,this.indices=t.indices,this.boundingBox=t.boundingBox}});Q.import(xr),Q.import(jn),Q.import("@export clay.deferred.light_volume.vertex\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\nattribute vec3 position : POSITION;\nvarying vec3 v_Position;\nvoid main()\n{\n gl_Position = worldViewProjection * vec4(position, 1.0);\n v_Position = position;\n}\n@end"),Q.import("@export clay.deferred.spot_light\n@import clay.deferred.chunk.light_head\n@import clay.deferred.chunk.light_equation\n@import clay.util.calculate_attenuation\nuniform vec3 lightPosition;\nuniform vec3 lightDirection;\nuniform vec3 lightColor;\nuniform float umbraAngleCosine;\nuniform float penumbraAngleCosine;\nuniform float lightRange;\nuniform float falloffFactor;\nuniform vec3 eyePosition;\n#ifdef SHADOWMAP_ENABLED\nuniform sampler2D lightShadowMap;\nuniform mat4 lightMatrix;\nuniform float lightShadowMapSize;\n#endif\n@import clay.plugin.shadow_map_common\nvoid main()\n{\n @import clay.deferred.chunk.gbuffer_read\n vec3 L = lightPosition - position;\n vec3 V = normalize(eyePosition - position);\n float dist = length(L);\n L /= dist;\n float attenuation = lightAttenuation(dist, lightRange);\n float c = dot(-normalize(lightDirection), L);\n float falloff = clamp((c - umbraAngleCosine) / (penumbraAngleCosine - umbraAngleCosine), 0.0, 1.0);\n falloff = pow(falloff, falloffFactor);\n vec3 H = normalize(L + V);\n float ndl = clamp(dot(N, L), 0.0, 1.0);\n float ndh = clamp(dot(N, H), 0.0, 1.0);\n float ndv = clamp(dot(N, V), 0.0, 1.0);\n gl_FragColor.rgb = (1.0 - falloff) * attenuation * lightEquation(\n lightColor, diffuseColor, specularColor, ndl, ndh, ndv, glossiness\n );\n#ifdef SHADOWMAP_ENABLED\n float shadowContrib = computeShadowContrib(\n lightShadowMap, lightMatrix, position, lightShadowMapSize\n );\n gl_FragColor.rgb *= shadowContrib;\n#endif\n gl_FragColor.a = 1.0;\n}\n@end\n"),Q.import("@export clay.deferred.directional_light\n@import clay.deferred.chunk.light_head\n@import clay.deferred.chunk.light_equation\nuniform vec3 lightDirection;\nuniform vec3 lightColor;\nuniform vec3 eyePosition;\n#ifdef SHADOWMAP_ENABLED\nuniform sampler2D lightShadowMap;\nuniform float lightShadowMapSize;\nuniform mat4 lightMatrices[SHADOW_CASCADE];\nuniform float shadowCascadeClipsNear[SHADOW_CASCADE];\nuniform float shadowCascadeClipsFar[SHADOW_CASCADE];\n#endif\n@import clay.plugin.shadow_map_common\nvoid main()\n{\n @import clay.deferred.chunk.gbuffer_read\n vec3 L = -normalize(lightDirection);\n vec3 V = normalize(eyePosition - position);\n vec3 H = normalize(L + V);\n float ndl = clamp(dot(N, L), 0.0, 1.0);\n float ndh = clamp(dot(N, H), 0.0, 1.0);\n float ndv = clamp(dot(N, V), 0.0, 1.0);\n gl_FragColor.rgb = lightEquation(\n lightColor, diffuseColor, specularColor, ndl, ndh, ndv, glossiness\n );\n#ifdef SHADOWMAP_ENABLED\n float shadowContrib = 1.0;\n for (int _idx_ = 0; _idx_ < SHADOW_CASCADE; _idx_++) {{\n if (\n z >= shadowCascadeClipsNear[_idx_] &&\n z <= shadowCascadeClipsFar[_idx_]\n ) {\n shadowContrib = computeShadowContrib(\n lightShadowMap, lightMatrices[_idx_], position, lightShadowMapSize,\n vec2(1.0 / float(SHADOW_CASCADE), 1.0),\n vec2(float(_idx_) / float(SHADOW_CASCADE), 0.0)\n );\n }\n }}\n gl_FragColor.rgb *= shadowContrib;\n#endif\n gl_FragColor.a = 1.0;\n}\n@end\n"),Q.import("@export clay.deferred.ambient_light\nuniform sampler2D gBufferTexture1;\nuniform sampler2D gBufferTexture3;\nuniform vec3 lightColor;\nuniform vec2 windowSize: WINDOW_SIZE;\nvoid main()\n{\n vec2 uv = gl_FragCoord.xy / windowSize;\n vec4 texel1 = texture2D(gBufferTexture1, uv);\n if (dot(texel1.rgb, vec3(1.0)) == 0.0) {\n discard;\n }\n vec3 albedo = texture2D(gBufferTexture3, uv).rgb;\n gl_FragColor.rgb = lightColor * albedo;\n gl_FragColor.a = 1.0;\n}\n@end"),Q.import("@export clay.deferred.ambient_sh_light\nuniform sampler2D gBufferTexture1;\nuniform sampler2D gBufferTexture3;\nuniform vec3 lightColor;\nuniform vec3 lightCoefficients[9];\nuniform vec2 windowSize: WINDOW_SIZE;\nvec3 calcAmbientSHLight(vec3 N) {\n return lightCoefficients[0]\n + lightCoefficients[1] * N.x\n + lightCoefficients[2] * N.y\n + lightCoefficients[3] * N.z\n + lightCoefficients[4] * N.x * N.z\n + lightCoefficients[5] * N.z * N.y\n + lightCoefficients[6] * N.y * N.x\n + lightCoefficients[7] * (3.0 * N.z * N.z - 1.0)\n + lightCoefficients[8] * (N.x * N.x - N.y * N.y);\n}\nvoid main()\n{\n vec2 uv = gl_FragCoord.xy / windowSize;\n vec4 texel1 = texture2D(gBufferTexture1, uv);\n if (dot(texel1.rgb, vec3(1.0)) == 0.0) {\n discard;\n }\n vec3 N = texel1.rgb * 2.0 - 1.0;\n vec3 albedo = texture2D(gBufferTexture3, uv).rgb;\n gl_FragColor.rgb = lightColor * albedo * calcAmbientSHLight(N);\n gl_FragColor.a = 1.0;\n}\n@end"),Q.import("@export clay.deferred.ambient_cubemap_light\n@import clay.deferred.chunk.light_head\nuniform vec3 lightColor;\nuniform samplerCube lightCubemap;\nuniform sampler2D brdfLookup;\nuniform vec3 eyePosition;\n@import clay.util.rgbm\nvoid main()\n{\n @import clay.deferred.chunk.gbuffer_read\n vec3 V = normalize(eyePosition - position);\n vec3 L = reflect(-V, N);\n float ndv = clamp(dot(N, V), 0.0, 1.0);\n float rough = clamp(1.0 - glossiness, 0.0, 1.0);\n float bias = rough * 5.0;\n vec2 brdfParam = texture2D(brdfLookup, vec2(rough, ndv)).xy;\n vec3 envWeight = specularColor * brdfParam.x + brdfParam.y;\n vec3 envTexel = RGBMDecode(textureCubeLodEXT(lightCubemap, L, bias), 8.12);\n gl_FragColor.rgb = lightColor * envTexel * envWeight;\n gl_FragColor.a = 1.0;\n}\n@end"),Q.import("@export clay.deferred.point_light\n@import clay.deferred.chunk.light_head\n@import clay.util.calculate_attenuation\n@import clay.deferred.chunk.light_equation\nuniform vec3 lightPosition;\nuniform vec3 lightColor;\nuniform float lightRange;\nuniform vec3 eyePosition;\n#ifdef SHADOWMAP_ENABLED\nuniform samplerCube lightShadowMap;\nuniform float lightShadowMapSize;\n#endif\nvarying vec3 v_Position;\n@import clay.plugin.shadow_map_common\nvoid main()\n{\n @import clay.deferred.chunk.gbuffer_read\n vec3 L = lightPosition - position;\n vec3 V = normalize(eyePosition - position);\n float dist = length(L);\n L /= dist;\n vec3 H = normalize(L + V);\n float ndl = clamp(dot(N, L), 0.0, 1.0);\n float ndh = clamp(dot(N, H), 0.0, 1.0);\n float ndv = clamp(dot(N, V), 0.0, 1.0);\n float attenuation = lightAttenuation(dist, lightRange);\n gl_FragColor.rgb = attenuation * lightEquation(\n lightColor, diffuseColor, specularColor, ndl, ndh, ndv, glossiness\n );\n#ifdef SHADOWMAP_ENABLED\n float shadowContrib = computeShadowContribOmni(\n lightShadowMap, -L * dist, lightRange\n );\n gl_FragColor.rgb *= clamp(shadowContrib, 0.0, 1.0);\n#endif\n gl_FragColor.a = 1.0;\n}\n@end"),Q.import("@export clay.deferred.sphere_light\n@import clay.deferred.chunk.light_head\n@import clay.util.calculate_attenuation\n@import clay.deferred.chunk.light_equation\nuniform vec3 lightPosition;\nuniform vec3 lightColor;\nuniform float lightRange;\nuniform float lightRadius;\nuniform vec3 eyePosition;\nvarying vec3 v_Position;\nvoid main()\n{\n @import clay.deferred.chunk.gbuffer_read\n vec3 L = lightPosition - position;\n vec3 V = normalize(eyePosition - position);\n float dist = length(L);\n vec3 R = reflect(V, N);\n float tmp = dot(L, R);\n vec3 cToR = tmp * R - L;\n float d = length(cToR);\n L = L + cToR * clamp(lightRadius / d, 0.0, 1.0);\n L = normalize(L);\n vec3 H = normalize(L + V);\n float ndl = clamp(dot(N, L), 0.0, 1.0);\n float ndh = clamp(dot(N, H), 0.0, 1.0);\n float ndv = clamp(dot(N, V), 0.0, 1.0);\n float attenuation = lightAttenuation(dist, lightRange);\n gl_FragColor.rgb = lightColor * ndl * attenuation;\n glossiness = clamp(glossiness - lightRadius / 2.0 / dist, 0.0, 1.0);\n gl_FragColor.rgb = attenuation * lightEquation(\n lightColor, diffuseColor, specularColor, ndl, ndh, ndv, glossiness\n );\n gl_FragColor.a = 1.0;\n}\n@end"),Q.import("@export clay.deferred.tube_light\n@import clay.deferred.chunk.light_head\n@import clay.util.calculate_attenuation\n@import clay.deferred.chunk.light_equation\nuniform vec3 lightPosition;\nuniform vec3 lightColor;\nuniform float lightRange;\nuniform vec3 lightExtend;\nuniform vec3 eyePosition;\nvarying vec3 v_Position;\nvoid main()\n{\n @import clay.deferred.chunk.gbuffer_read\n vec3 L = lightPosition - position;\n vec3 V = normalize(eyePosition - position);\n vec3 R = reflect(V, N);\n vec3 L0 = lightPosition - lightExtend - position;\n vec3 L1 = lightPosition + lightExtend - position;\n vec3 LD = L1 - L0;\n float len0 = length(L0);\n float len1 = length(L1);\n float irra = 2.0 * clamp(dot(N, L0) / (2.0 * len0) + dot(N, L1) / (2.0 * len1), 0.0, 1.0);\n float LDDotR = dot(R, LD);\n float t = (LDDotR * dot(R, L0) - dot(L0, LD)) / (dot(LD, LD) - LDDotR * LDDotR);\n t = clamp(t, 0.0, 1.0);\n L = L0 + t * LD;\n float dist = length(L);\n L /= dist;\n vec3 H = normalize(L + V);\n float ndh = clamp(dot(N, H), 0.0, 1.0);\n float ndv = clamp(dot(N, V), 0.0, 1.0);\n glossiness = clamp(glossiness - 0.0 / 2.0 / dist, 0.0, 1.0);\n gl_FragColor.rgb = lightColor * irra * lightAttenuation(dist, lightRange)\n * (diffuseColor + D_Phong(glossiness, ndh) * F_Schlick(ndv, specularColor));\n gl_FragColor.a = 1.0;\n}\n@end"),Q.import(xr);var ha=ke.extend(function(){var t=Q.source("clay.compositor.vertex"),e=Q.source("clay.deferred.light_volume.vertex"),r=new Q(t,Q.source("clay.deferred.directional_light")),n=function(t){t.blendEquation(t.FUNC_ADD),t.blendFuncSeparate(t.ONE,t.ONE,t.ONE,t.ONE)},i=function(t){return new nr({shader:t,blend:n,transparent:!0,depthMask:!1})},a=function(t){return new Q(e,Q.source("clay.deferred."+t))},o=new ua({capSegments:10}),s=new Ur;s.rotateX(Math.PI/2).translate(new Cr(0,-1,0)),o.applyTransform(s);var u=new la({capSegments:10});return s.identity().rotateZ(Math.PI/2),u.applyTransform(s),{shadowMapPass:null,autoResize:!0,_createLightPassMat:i,_gBuffer:new sa,_lightAccumFrameBuffer:new oi,_lightAccumTex:new An({type:Tn.HALF_FLOAT,minFilter:Tn.NEAREST,magFilter:Tn.NEAREST}),_fullQuadPass:new hi({blendWithPrevious:!0}),_directionalLightMat:i(r),_ambientMat:i(new Q(t,Q.source("clay.deferred.ambient_light"))),_ambientSHMat:i(new Q(t,Q.source("clay.deferred.ambient_sh_light"))),_ambientCubemapMat:i(new Q(t,Q.source("clay.deferred.ambient_cubemap_light"))),_spotLightShader:a("spot_light"),_pointLightShader:a("point_light"),_sphereLightShader:a("sphere_light"),_tubeLightShader:a("tube_light"),_lightSphereGeo:new vn({widthSegments:10,heightSegements:10}),_lightConeGeo:o,_lightCylinderGeo:u,_outputPass:new hi({fragment:Q.source("clay.compositor.output")})}},{render:function(t,e,r,n){n=n||{},n.renderToTarget=n.renderToTarget||!1,n.notUpdateShadow=n.notUpdateShadow||!1,n.notUpdateScene=n.notUpdateScene||!1,n.notUpdateScene||e.update(!1,!0),e.updateLights(),r.update(!0);var i=t.getDevicePixelRatio();!this.autoResize||t.getWidth()*i===this._lightAccumTex.width&&t.getHeight()*i===this._lightAccumTex.height||this.resize(t.getWidth()*i,t.getHeight()*i),this._gBuffer.update(t,e,r),this._accumulateLightBuffer(t,e,r,!n.notUpdateShadow),n.renderToTarget||(this._outputPass.setUniform("texture",this._lightAccumTex),this._outputPass.render(t))},getTargetTexture:function(){return this._lightAccumTex},getTargetFrameBuffer:function(){return this._lightAccumFrameBuffer},getGBuffer:function(){return this._gBuffer},setViewport:function(t,e,r,n,i){this._gBuffer.setViewport(t,e,r,n,i),this._lightAccumFrameBuffer.viewport=this._gBuffer.getViewport()},resize:function(t,e){this._lightAccumTex.width=t,this._lightAccumTex.height=e,this._gBuffer.resize(t,e)},_accumulateLightBuffer:function(t,e,r,n){for(var i=t.gl,a=this._lightAccumTex,o=this._lightAccumFrameBuffer,s=r.getWorldPosition().array,u=0;u<e.lights.length;u++)e.lights[u].invisible||this._updateLightProxy(e.lights[u]);var l=this.shadowMapPass;l&&n&&(i.clearColor(1,1,1,1),this._prepareLightShadow(t,e,r)),this.trigger("beforelightaccumulate",t,e,r,n),o.attach(a),o.bind(t);var h=t.clearColor,c=o.viewport;if(c){var f=c.devicePixelRatio;i.enable(i.SCISSOR_TEST),i.scissor(c.x*f,c.y*f,c.width*f,c.height*f)}i.clearColor(h[0],h[1],h[2],h[3]),i.clear(i.COLOR_BUFFER_BIT),i.enable(i.BLEND),c&&i.disable(i.SCISSOR_TEST),this.trigger("startlightaccumulate",t,e,r);var d=new Ur;Ur.multiply(d,r.worldTransform,r.invProjectionMatrix);for(var p=[],u=0;u<e.lights.length;u++){var m=e.lights[u];if(!m.invisible){var _=m.uniformTemplates,g=m.volumeMesh||m.__volumeMesh;if(g){var v=g.material;g.castShadow=!1;var y=!1;switch(m.type){case"POINT_LIGHT":v.setUniform("lightColor",_.pointLightColor.value(m)),v.setUniform("lightRange",_.pointLightRange.value(m)),v.setUniform("lightPosition",_.pointLightPosition.value(m));break;case"SPOT_LIGHT":v.setUniform("lightPosition",_.spotLightPosition.value(m)),v.setUniform("lightColor",_.spotLightColor.value(m)),v.setUniform("lightRange",_.spotLightRange.value(m)),v.setUniform("lightDirection",_.spotLightDirection.value(m)),v.setUniform("umbraAngleCosine",_.spotLightUmbraAngleCosine.value(m)),v.setUniform("penumbraAngleCosine",_.spotLightPenumbraAngleCosine.value(m)),v.setUniform("falloffFactor",_.spotLightFalloffFactor.value(m));break;case"SPHERE_LIGHT":v.setUniform("lightColor",_.sphereLightColor.value(m)),v.setUniform("lightRange",_.sphereLightRange.value(m)),v.setUniform("lightRadius",_.sphereLightRadius.value(m)),v.setUniform("lightPosition",_.sphereLightPosition.value(m));break;case"TUBE_LIGHT":v.setUniform("lightColor",_.tubeLightColor.value(m)),v.setUniform("lightRange",_.tubeLightRange.value(m)),v.setUniform("lightExtend",_.tubeLightExtend.value(m)),v.setUniform("lightPosition",_.tubeLightPosition.value(m));break;default:y=!0}if(y)continue;v.setUniform("eyePosition",s),v.setUniform("viewProjectionInv",d.array),v.setUniform("gBufferTexture1",this._gBuffer.getTargetTexture1()),v.setUniform("gBufferTexture2",this._gBuffer.getTargetTexture2()),v.setUniform("gBufferTexture3",this._gBuffer.getTargetTexture3()),p.push(g)}else{var T=this._fullQuadPass,y=!1;switch(m.type){case"AMBIENT_LIGHT":T.material=this._ambientMat,T.material.setUniform("lightColor",_.ambientLightColor.value(m));break;case"AMBIENT_SH_LIGHT":T.material=this._ambientSHMat,T.material.setUniform("lightColor",_.ambientSHLightColor.value(m)),T.material.setUniform("lightCoefficients",_.ambientSHLightCoefficients.value(m));break;case"AMBIENT_CUBEMAP_LIGHT":T.material=this._ambientCubemapMat,T.material.setUniform("lightColor",_.ambientCubemapLightColor.value(m)),T.material.setUniform("lightCubemap",_.ambientCubemapLightCubemap.value(m)),T.material.setUniform("brdfLookup",_.ambientCubemapLightBRDFLookup.value(m));break;case"DIRECTIONAL_LIGHT":var x=l&&m.castShadow;T.material=this._directionalLightMat,T.material[x?"define":"undefine"]("fragment","SHADOWMAP_ENABLED"),x&&T.material.define("fragment","SHADOW_CASCADE",m.shadowCascade),T.material.setUniform("lightColor",_.directionalLightColor.value(m)),T.material.setUniform("lightDirection",_.directionalLightDirection.value(m));break;default:y=!0}if(y)continue;var E=T.material;E.setUniform("eyePosition",s),E.setUniform("viewProjectionInv",d.array),E.setUniform("gBufferTexture1",this._gBuffer.getTargetTexture1()),E.setUniform("gBufferTexture2",this._gBuffer.getTargetTexture2()),E.setUniform("gBufferTexture3",this._gBuffer.getTargetTexture3()),l&&m.castShadow&&(E.setUniform("lightShadowMap",m.__shadowMap),E.setUniform("lightMatrices",m.__lightMatrices),E.setUniform("shadowCascadeClipsNear",m.__cascadeClipsNear),E.setUniform("shadowCascadeClipsFar",m.__cascadeClipsFar),E.setUniform("lightShadowMapSize",m.shadowResolution)),T.renderQuad(t)}}}this._renderVolumeMeshList(t,e,r,p),this.trigger("lightaccumulate",t,e,r),o.unbind(t),this.trigger("afterlightaccumulate",t,e,r)},_prepareLightShadow:function(){var t=new Ur;return function(e,r,n){for(var i=0;i<r.lights.length;i++){var a=r.lights[i],o=a.volumeMesh||a.__volumeMesh;if(a.castShadow&&!a.invisible)switch(a.type){case"POINT_LIGHT":case"SPOT_LIGHT":if(Ur.multiply(t,n.viewMatrix,o.worldTransform),r.isFrustumCulled(o,n,t.array))continue;this._prepareSingleLightShadow(e,r,n,a,o.material);break;case"DIRECTIONAL_LIGHT":this._prepareSingleLightShadow(e,r,n,a,null)}}}}(),_prepareSingleLightShadow:function(t,e,r,n,i){switch(n.type){case"POINT_LIGHT":var a=[];this.shadowMapPass.renderPointLightShadow(t,e,n,a),i.setUniform("lightShadowMap",a[0]),i.setUniform("lightShadowMapSize",n.shadowResolution);break;case"SPOT_LIGHT":var a=[],o=[];this.shadowMapPass.renderSpotLightShadow(t,e,n,o,a),i.setUniform("lightShadowMap",a[0]),i.setUniform("lightMatrix",o[0]),i.setUniform("lightShadowMapSize",n.shadowResolution);break;case"DIRECTIONAL_LIGHT":var a=[],o=[],s=[];this.shadowMapPass.renderDirectionalLightShadow(t,e,r,n,s,o,a);var u=s.slice(),l=s.slice();u.pop(),l.shift(),u.reverse(),l.reverse(),o.reverse(),n.__cascadeClipsNear=u,n.__cascadeClipsFar=l,n.__shadowMap=a[0],n.__lightMatrices=o}},_updateLightProxy:function(t){var e;if(t.volumeMesh)e=t.volumeMesh;else switch(t.type){case"POINT_LIGHT":case"SPHERE_LIGHT":var r="SPHERE_LIGHT"===t.type?this._sphereLightShader:this._pointLightShader;t.__volumeMesh||(t.__volumeMesh=new Cn({material:this._createLightPassMat(r),geometry:this._lightSphereGeo,culling:!1})),e=t.__volumeMesh;var n=t.range+(t.radius||0);e.scale.set(n,n,n);break;case"SPOT_LIGHT":t.__volumeMesh=t.__volumeMesh||new Cn({material:this._createLightPassMat(this._spotLightShader),geometry:this._lightConeGeo,culling:!1}),e=t.__volumeMesh;var i=Math.tan(t.penumbraAngle*Math.PI/180),a=t.range;e.scale.set(i*a,i*a,a/2);break;case"TUBE_LIGHT":t.__volumeMesh=t.__volumeMesh||new Cn({material:this._createLightPassMat(this._tubeLightShader),geometry:this._lightCylinderGeo,culling:!1}),e=t.__volumeMesh;var a=t.range;e.scale.set(t.length/2+a,a,a)}if(e){e.update(),Ur.multiply(e.worldTransform,t.worldTransform,e.worldTransform);var o=this.shadowMapPass&&t.castShadow;e.material[o?"define":"undefine"]("fragment","SHADOWMAP_ENABLED")}},_renderVolumeMeshList:function(){function t(){return r}var e=new Ur,r=new nr({shader:new Q(Q.source("clay.prez.vertex"),Q.source("clay.prez.fragment"))});return function(r,n,i,a){var o=r.gl;o.depthFunc(o.LEQUAL);for(var s=0;s<a.length;s++){var u=a[s];Ur.multiply(e,i.viewMatrix,u.worldTransform),n.isFrustumCulled(u,i,e.array)||(o.colorMask(!1,!1,!1,!1),o.depthMask(!0),o.clear(o.DEPTH_BUFFER_BIT),r.renderPass([u],i,{getMaterial:t}),o.colorMask(!0,!0,!0,!0),u.material.depthMask=!0,r.renderPass([u],i))}o.depthFunc(o.LESS)}}(),dispose:function(t){this._gBuffer.dispose(t),this._lightAccumFrameBuffer.dispose(t),this._lightAccumTex.dispose(t),this._lightConeGeo.dispose(t),this._lightCylinderGeo.dispose(t),this._lightSphereGeo.dispose(t),this._fullQuadPass.dispose(t),this._outputPass.dispose(t),this._directionalLightMat.dispose(t),this.shadowMapPass.dispose(t)}}),ca={};ca.create=function(){var t=new Ee(4);return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t},ca.clone=function(t){var e=new Ee(4);return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e},ca.copy=function(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t},ca.identity=function(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t},ca.transpose=function(t,e){if(t===e){var r=e[1];t[1]=e[2],t[2]=r}else t[0]=e[0],t[1]=e[2],t[2]=e[1],t[3]=e[3];return t},ca.invert=function(t,e){var r=e[0],n=e[1],i=e[2],a=e[3],o=r*a-i*n;return o?(o=1/o,t[0]=a*o,t[1]=-n*o,t[2]=-i*o,t[3]=r*o,t):null},ca.adjoint=function(t,e){var r=e[0];return t[0]=e[3],t[1]=-e[1],t[2]=-e[2],t[3]=r,t},ca.determinant=function(t){return t[0]*t[3]-t[2]*t[1]},ca.multiply=function(t,e,r){var n=e[0],i=e[1],a=e[2],o=e[3],s=r[0],u=r[1],l=r[2],h=r[3];return t[0]=n*s+a*u,t[1]=i*s+o*u,t[2]=n*l+a*h,t[3]=i*l+o*h,t},ca.mul=ca.multiply,ca.rotate=function(t,e,r){var n=e[0],i=e[1],a=e[2],o=e[3],s=Math.sin(r),u=Math.cos(r);return t[0]=n*u+a*s,t[1]=i*u+o*s,t[2]=n*-s+a*u,t[3]=i*-s+o*u,t},ca.scale=function(t,e,r){var n=e[0],i=e[1],a=e[2],o=e[3],s=r[0],u=r[1];return t[0]=n*s,t[1]=i*s,t[2]=a*u,t[3]=o*u,t},ca.frob=function(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2)+Math.pow(t[2],2)+Math.pow(t[3],2))},ca.LDU=function(t,e,r,n){return t[2]=n[2]/n[0],r[0]=n[0],r[1]=n[1],r[3]=n[3]-t[2]*r[1],[t,e,r]};var fa={};fa.create=function(){var t=new Ee(6);return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t},fa.clone=function(t){var e=new Ee(6);return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4],e[5]=t[5],e},fa.copy=function(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t},fa.identity=function(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t},fa.invert=function(t,e){var r=e[0],n=e[1],i=e[2],a=e[3],o=e[4],s=e[5],u=r*a-n*i;return u?(u=1/u,t[0]=a*u,t[1]=-n*u,t[2]=-i*u,t[3]=r*u,t[4]=(i*s-a*o)*u,t[5]=(n*o-r*s)*u,t):null},fa.determinant=function(t){return t[0]*t[3]-t[1]*t[2]},fa.multiply=function(t,e,r){var n=e[0],i=e[1],a=e[2],o=e[3],s=e[4],u=e[5],l=r[0],h=r[1],c=r[2],f=r[3],d=r[4],p=r[5];return t[0]=n*l+a*h,t[1]=i*l+o*h,t[2]=n*c+a*f,t[3]=i*c+o*f,t[4]=n*d+a*p+s,t[5]=i*d+o*p+u,t},fa.mul=fa.multiply,fa.rotate=function(t,e,r){var n=e[0],i=e[1],a=e[2],o=e[3],s=e[4],u=e[5],l=Math.sin(r),h=Math.cos(r);return t[0]=n*h+a*l,t[1]=i*h+o*l,t[2]=n*-l+a*h,t[3]=i*-l+o*h,t[4]=s,t[5]=u,t},fa.scale=function(t,e,r){var n=e[0],i=e[1],a=e[2],o=e[3],s=e[4],u=e[5],l=r[0],h=r[1];return t[0]=n*l,t[1]=i*l,t[2]=a*h,t[3]=o*h,t[4]=s,t[5]=u,t},fa.translate=function(t,e,r){var n=e[0],i=e[1],a=e[2],o=e[3],s=e[4],u=e[5],l=r[0],h=r[1];return t[0]=n,t[1]=i,t[2]=a,t[3]=o,t[4]=n*l+a*h+s,t[5]=i*l+o*h+u,t},fa.frob=function(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2)+Math.pow(t[2],2)+Math.pow(t[3],2)+Math.pow(t[4],2)+Math.pow(t[5],2)+1)};var da={vec2:be,vec3:Ce,vec4:Re,mat2:ca,mat2d:fa,mat3:Le,mat4:Er,quat:Ne},pa=new Vr,ma=Cn.extend(function(){return{instances:[],instancedAttributes:{},_attributesSymbols:[]}},function(){this._cache=new un,this.createInstancedAttribute("instanceMat1","float",4,1),this.createInstancedAttribute("instanceMat2","float",4,1),this.createInstancedAttribute("instanceMat3","float",4,1)},{isInstancedMesh:function(){return!0},getInstanceCount:function(){return this.instances.length},removeAttribute:function(t){var e=this._attributesSymbols.indexOf(t);e>=0&&this._attributesSymbols.splice(e,1),delete this.instancedAttributes[t]},createInstancedAttribute:function(t,e,r,n){this.instancedAttributes[t]||(this.instancedAttributes[t]={symbol:t,type:e,size:r,divisor:null==n?1:n,value:null},this._attributesSymbols.push(t))},getInstancedAttributesBuffers:function(t){var e=this._cache;e.use(t.__uid__);var r=e.get("buffers")||[];if(e.isDirty("dirty")){for(var n=t.gl,i=0;i<this._attributesSymbols.length;i++){var a=this.instancedAttributes[this._attributesSymbols[i]],o=r[i];o||(o={buffer:n.createBuffer()},r[i]=o),o.symbol=a.symbol,o.divisor=a.divisor,o.size=a.size,o.type=a.type,n.bindBuffer(n.ARRAY_BUFFER,o.buffer),n.bufferData(n.ARRAY_BUFFER,a.value,n.DYNAMIC_DRAW)}e.fresh("dirty"),e.put("buffers",r)}return r},update:function(t){Cn.prototype.update.call(this,t);var e=4*this.getInstanceCount(),r=this.instancedAttributes,n=r.instanceMat1.value,i=r.instanceMat2.value,a=r.instanceMat3.value;n&&n.length===e||(n=r.instanceMat1.value=new Float32Array(e),i=r.instanceMat2.value=new Float32Array(e),a=r.instanceMat3.value=new Float32Array(e));var o=this.skeleton&&this.skeleton.boundingBox||this.geometry.boundingBox,s=null!=o&&(this.castShadow||this.frustumCulling);s&&this.instances.length>0?(this.boundingBox=this.boundingBox||new Vr,this.boundingBox.min.set(1/0,1/0,1/0),this.boundingBox.max.set(-1/0,-1/0,-1/0)):this.boundingBox=null;for(var u=0;u<this.instances.length;u++){var l=this.instances[u],h=l.node;if(!h)throw new Error("Instance must include node");var c=h.worldTransform.array,f=4*u;n[f]=c[0],n[f+1]=c[1],n[f+2]=c[2],n[f+3]=c[12],i[f]=c[4],i[f+1]=c[5],i[f+2]=c[6],i[f+3]=c[13],a[f]=c[8],a[f+1]=c[9],a[f+2]=c[10],a[f+3]=c[14],s&&(pa.transformFrom(o,h.worldTransform),this.boundingBox.union(pa,this.boundingBox))}this._cache.dirty("dirty")}}),_a=qr.extend({range:100,radius:5},{type:"SPHERE_LIGHT",uniformTemplates:{sphereLightPosition:{type:"3f",value:function(t){return t.getWorldPosition().array}},sphereLightRange:{type:"1f",value:function(t){return t.range}},sphereLightRadius:{type:"1f",value:function(t){return t.radius}},sphereLightColor:{type:"3f",value:function(t){var e=t.color,r=t.intensity;return[e[0]*r,e[1]*r,e[2]*r]}}}}),ga=qr.extend({range:100,length:10},{type:"TUBE_LIGHT",uniformTemplates:{tubeLightPosition:{type:"3f",value:function(t){return t.getWorldPosition().array}},tubeLightExtend:{type:"3f",value:function(){var t=new Cr;return function(e){return t.copy(e.worldTransform.x).normalize().scale(e.length/2).array}}()},tubeLightRange:{type:"1f",value:function(t){return t.range}},tubeLightColor:{type:"3f",value:function(t){var e=t.color,r=t.intensity;return[e[0]*r,e[1]*r,e[2]*r]}}}}),va=ke.extend({rootPath:"",textureRootPath:"",shaderRootPath:"",scene:null,camera:null},{load:function(t){var e=this;this.rootPath||(this.rootPath=t.slice(0,t.lastIndexOf("/"))),Ve.request.get({url:t,onprogress:function(t,r,n){e.trigger("progress",t,r,n)},onerror:function(t){e.trigger("error",t)},responseType:"text",onload:function(t){Kt(JSON.parse(t),{textureRootPath:this.textureRootPath||this.rootPath,camera:this.camera,scene:this.scene})}})}}),ya=function(){this.array=ca.create(),this._dirty=!0};ya.prototype={constructor:ya,setArray:function(t){for(var e=0;e<this.array.length;e++)this.array[e]=t[e];return this._dirty=!0,this},clone:function(){return(new ya).copy(this)},copy:function(t){return ca.copy(this.array,t.array),this._dirty=!0,this},adjoint:function(){return ca.adjoint(this.array,this.array),this._dirty=!0,this},determinant:function(){return ca.determinant(this.array)},identity:function(){return ca.identity(this.array),this._dirty=!0,this},invert:function(){return ca.invert(this.array,this.array),this._dirty=!0,this},mul:function(t){return ca.mul(this.array,this.array,t.array),this._dirty=!0,this},mulLeft:function(t){return ca.mul(this.array,t.array,this.array),this._dirty=!0,this},multiply:function(t){return ca.multiply(this.array,this.array,t.array),this._dirty=!0,this},multiplyLeft:function(t){return ca.multiply(this.array,t.array,this.array),this._dirty=!0,this},rotate:function(t){return ca.rotate(this.array,this.array,t),this._dirty=!0,this},scale:function(t){return ca.scale(this.array,this.array,t.array),this._dirty=!0,this},transpose:function(){return ca.transpose(this.array,this.array),this._dirty=!0,this},toString:function(){return"["+Array.prototype.join.call(this.array,",")+"]"},toArray:function(){return Array.prototype.slice.call(this.array)}},ya.adjoint=function(t,e){return ca.adjoint(t.array,e.array),t._dirty=!0,t},ya.copy=function(t,e){return ca.copy(t.array,e.array),t._dirty=!0,t},ya.determinant=function(t){return ca.determinant(t.array)},ya.identity=function(t){return ca.identity(t.array),t._dirty=!0,t},ya.invert=function(t,e){return ca.invert(t.array,e.array),t._dirty=!0,t},ya.mul=function(t,e,r){return ca.mul(t.array,e.array,r.array),t._dirty=!0,t},ya.multiply=ya.mul,ya.rotate=function(t,e,r){return ca.rotate(t.array,e.array,r),t._dirty=!0,t},ya.scale=function(t,e,r){return ca.scale(t.array,e.array,r.array),t._dirty=!0,t},ya.transpose=function(t,e){return ca.transpose(t.array,e.array),t._dirty=!0,t};var Ta=function(){this.array=fa.create(),this._dirty=!0};Ta.prototype={constructor:Ta,setArray:function(t){for(var e=0;e<this.array.length;e++)this.array[e]=t[e];return this._dirty=!0,this},clone:function(){return(new Ta).copy(this)},copy:function(t){return fa.copy(this.array,t.array),this._dirty=!0,this},determinant:function(){return fa.determinant(this.array)},identity:function(){return fa.identity(this.array),this._dirty=!0,this},invert:function(){return fa.invert(this.array,this.array),this._dirty=!0,this},mul:function(t){return fa.mul(this.array,this.array,t.array),this._dirty=!0,this},mulLeft:function(t){return fa.mul(this.array,t.array,this.array),this._dirty=!0,this},multiply:function(t){return fa.multiply(this.array,this.array,t.array),this._dirty=!0,this},multiplyLeft:function(t){return fa.multiply(this.array,t.array,this.array),this._dirty=!0,this},rotate:function(t){
return fa.rotate(this.array,this.array,t),this._dirty=!0,this},scale:function(t){return fa.scale(this.array,this.array,t.array),this._dirty=!0,this},translate:function(t){return fa.translate(this.array,this.array,t.array),this._dirty=!0,this},toString:function(){return"["+Array.prototype.join.call(this.array,",")+"]"},toArray:function(){return Array.prototype.slice.call(this.array)}},Ta.copy=function(t,e){return fa.copy(t.array,e.array),t._dirty=!0,t},Ta.determinant=function(t){return fa.determinant(t.array)},Ta.identity=function(t){return fa.identity(t.array),t._dirty=!0,t},Ta.invert=function(t,e){return fa.invert(t.array,e.array),t._dirty=!0,t},Ta.mul=function(t,e,r){return fa.mul(t.array,e.array,r.array),t._dirty=!0,t},Ta.multiply=Ta.mul,Ta.rotate=function(t,e,r){return fa.rotate(t.array,e.array,r),t._dirty=!0,t},Ta.scale=function(t,e,r){return fa.scale(t.array,e.array,r.array),t._dirty=!0,t},Ta.translate=function(t,e,r){return fa.translate(t.array,e.array,r.array),t._dirty=!0,t};var xa=function(){this.array=Le.create(),this._dirty=!0};xa.prototype={constructor:xa,setArray:function(t){for(var e=0;e<this.array.length;e++)this.array[e]=t[e];return this._dirty=!0,this},adjoint:function(){return Le.adjoint(this.array,this.array),this._dirty=!0,this},clone:function(){return(new xa).copy(this)},copy:function(t){return Le.copy(this.array,t.array),this._dirty=!0,this},determinant:function(){return Le.determinant(this.array)},fromMat2d:function(t){return Le.fromMat2d(this.array,t.array),this._dirty=!0,this},fromMat4:function(t){return Le.fromMat4(this.array,t.array),this._dirty=!0,this},fromQuat:function(t){return Le.fromQuat(this.array,t.array),this._dirty=!0,this},identity:function(){return Le.identity(this.array),this._dirty=!0,this},invert:function(){return Le.invert(this.array,this.array),this._dirty=!0,this},mul:function(t){return Le.mul(this.array,this.array,t.array),this._dirty=!0,this},mulLeft:function(t){return Le.mul(this.array,t.array,this.array),this._dirty=!0,this},multiply:function(t){return Le.multiply(this.array,this.array,t.array),this._dirty=!0,this},multiplyLeft:function(t){return Le.multiply(this.array,t.array,this.array),this._dirty=!0,this},rotate:function(t){return Le.rotate(this.array,this.array,t),this._dirty=!0,this},scale:function(t){return Le.scale(this.array,this.array,t.array),this._dirty=!0,this},translate:function(t){return Le.translate(this.array,this.array,t.array),this._dirty=!0,this},normalFromMat4:function(t){return Le.normalFromMat4(this.array,t.array),this._dirty=!0,this},transpose:function(){return Le.transpose(this.array,this.array),this._dirty=!0,this},toString:function(){return"["+Array.prototype.join.call(this.array,",")+"]"},toArray:function(){return Array.prototype.slice.call(this.array)}},xa.adjoint=function(t,e){return Le.adjoint(t.array,e.array),t._dirty=!0,t},xa.copy=function(t,e){return Le.copy(t.array,e.array),t._dirty=!0,t},xa.determinant=function(t){return Le.determinant(t.array)},xa.identity=function(t){return Le.identity(t.array),t._dirty=!0,t},xa.invert=function(t,e){return Le.invert(t.array,e.array),t},xa.mul=function(t,e,r){return Le.mul(t.array,e.array,r.array),t._dirty=!0,t},xa.multiply=xa.mul,xa.fromMat2d=function(t,e){return Le.fromMat2d(t.array,e.array),t._dirty=!0,t},xa.fromMat4=function(t,e){return Le.fromMat4(t.array,e.array),t._dirty=!0,t},xa.fromQuat=function(t,e){return Le.fromQuat(t.array,e.array),t._dirty=!0,t},xa.normalFromMat4=function(t,e){return Le.normalFromMat4(t.array,e.array),t._dirty=!0,t},xa.rotate=function(t,e,r){return Le.rotate(t.array,e.array,r),t._dirty=!0,t},xa.scale=function(t,e,r){return Le.scale(t.array,e.array,r.array),t._dirty=!0,t},xa.transpose=function(t,e){return Le.transpose(t.array,e.array),t._dirty=!0,t},xa.translate=function(t,e,r){return Le.translate(t.array,e.array,r.array),t._dirty=!0,t};var Ea=function(){};Ea.prototype.get=function(t){};var Aa=function(t){this.get=function(){return t}};Aa.prototype=new Ea,Aa.prototype.constructor=Aa;var ba=function(t){var e=t.constructor;this.get=function(r){return r||(r=new e),r.copy(t),r}};ba.prototype=new Ea,ba.prototype.constructor=ba;var Sa=function(t,e){var r=e-t;this.get=function(){return Math.random()*r+t}};Sa.prototype=new Ea,Sa.prototype.constructor=Sa;var Ma=function(t,e){var r=e.x-t.x,n=e.y-t.y;this.get=function(e){return e||(e=new Se),Se.set(e,r*Math.random()+t.array[0],n*Math.random()+t.array[1]),e}};Ma.prototype=new Ea,Ma.prototype.constructor=Ma;var wa=function(t,e){var r=e.x-t.x,n=e.y-t.y,i=e.z-t.z;this.get=function(e){return e||(e=new Cr),Cr.set(e,r*Math.random()+t.array[0],n*Math.random()+t.array[1],i*Math.random()+t.array[2]),e}};wa.prototype=new Ea,wa.prototype.constructor=wa,Ea.constant=function(t){return new Aa(t)},Ea.vector=function(t){return new ba(t)},Ea.random1D=function(t,e){return new Sa(t,e)},Ea.random2D=function(t,e){return new Ma(t,e)},Ea.random3D=function(t,e){return new wa(t,e)};var Ca=function(t,e,r,n){t=t||0,e=e||0,r=r||0,n=n||0,this.array=Re.fromValues(t,e,r,n),this._dirty=!0};Ca.prototype={constructor:Ca,add:function(t){return Re.add(this.array,this.array,t.array),this._dirty=!0,this},set:function(t,e,r,n){return this.array[0]=t,this.array[1]=e,this.array[2]=r,this.array[3]=n,this._dirty=!0,this},setArray:function(t){return this.array[0]=t[0],this.array[1]=t[1],this.array[2]=t[2],this.array[3]=t[3],this._dirty=!0,this},clone:function(){return new Ca(this.x,this.y,this.z,this.w)},copy:function(t){return Re.copy(this.array,t.array),this._dirty=!0,this},dist:function(t){return Re.dist(this.array,t.array)},distance:function(t){return Re.distance(this.array,t.array)},div:function(t){return Re.div(this.array,this.array,t.array),this._dirty=!0,this},divide:function(t){return Re.divide(this.array,this.array,t.array),this._dirty=!0,this},dot:function(t){return Re.dot(this.array,t.array)},len:function(){return Re.len(this.array)},length:function(){return Re.length(this.array)},lerp:function(t,e,r){return Re.lerp(this.array,t.array,e.array,r),this._dirty=!0,this},min:function(t){return Re.min(this.array,this.array,t.array),this._dirty=!0,this},max:function(t){return Re.max(this.array,this.array,t.array),this._dirty=!0,this},mul:function(t){return Re.mul(this.array,this.array,t.array),this._dirty=!0,this},multiply:function(t){return Re.multiply(this.array,this.array,t.array),this._dirty=!0,this},negate:function(){return Re.negate(this.array,this.array),this._dirty=!0,this},normalize:function(){return Re.normalize(this.array,this.array),this._dirty=!0,this},random:function(t){return Re.random(this.array,t),this._dirty=!0,this},scale:function(t){return Re.scale(this.array,this.array,t),this._dirty=!0,this},scaleAndAdd:function(t,e){return Re.scaleAndAdd(this.array,this.array,t.array,e),this._dirty=!0,this},sqrDist:function(t){return Re.sqrDist(this.array,t.array)},squaredDistance:function(t){return Re.squaredDistance(this.array,t.array)},sqrLen:function(){return Re.sqrLen(this.array)},squaredLength:function(){return Re.squaredLength(this.array)},sub:function(t){return Re.sub(this.array,this.array,t.array),this._dirty=!0,this},subtract:function(t){return Re.subtract(this.array,this.array,t.array),this._dirty=!0,this},transformMat4:function(t){return Re.transformMat4(this.array,this.array,t.array),this._dirty=!0,this},transformQuat:function(t){return Re.transformQuat(this.array,this.array,t.array),this._dirty=!0,this},toString:function(){return"["+Array.prototype.join.call(this.array,",")+"]"},toArray:function(){return Array.prototype.slice.call(this.array)}};var Ra=Object.defineProperty;if(Ra){var La=Ca.prototype;Ra(La,"x",{get:function(){return this.array[0]},set:function(t){this.array[0]=t,this._dirty=!0}}),Ra(La,"y",{get:function(){return this.array[1]},set:function(t){this.array[1]=t,this._dirty=!0}}),Ra(La,"z",{get:function(){return this.array[2]},set:function(t){this.array[2]=t,this._dirty=!0}}),Ra(La,"w",{get:function(){return this.array[3]},set:function(t){this.array[3]=t,this._dirty=!0}})}Ca.add=function(t,e,r){return Re.add(t.array,e.array,r.array),t._dirty=!0,t},Ca.set=function(t,e,r,n,i){Re.set(t.array,e,r,n,i),t._dirty=!0},Ca.copy=function(t,e){return Re.copy(t.array,e.array),t._dirty=!0,t},Ca.dist=function(t,e){return Re.distance(t.array,e.array)},Ca.distance=Ca.dist,Ca.div=function(t,e,r){return Re.divide(t.array,e.array,r.array),t._dirty=!0,t},Ca.divide=Ca.div,Ca.dot=function(t,e){return Re.dot(t.array,e.array)},Ca.len=function(t){return Re.length(t.array)},Ca.lerp=function(t,e,r,n){return Re.lerp(t.array,e.array,r.array,n),t._dirty=!0,t},Ca.min=function(t,e,r){return Re.min(t.array,e.array,r.array),t._dirty=!0,t},Ca.max=function(t,e,r){return Re.max(t.array,e.array,r.array),t._dirty=!0,t},Ca.mul=function(t,e,r){return Re.multiply(t.array,e.array,r.array),t._dirty=!0,t},Ca.multiply=Ca.mul,Ca.negate=function(t,e){return Re.negate(t.array,e.array),t._dirty=!0,t},Ca.normalize=function(t,e){return Re.normalize(t.array,e.array),t._dirty=!0,t},Ca.random=function(t,e){return Re.random(t.array,e),t._dirty=!0,t},Ca.scale=function(t,e,r){return Re.scale(t.array,e.array,r),t._dirty=!0,t},Ca.scaleAndAdd=function(t,e,r,n){return Re.scaleAndAdd(t.array,e.array,r.array,n),t._dirty=!0,t},Ca.sqrDist=function(t,e){return Re.sqrDist(t.array,e.array)},Ca.squaredDistance=Ca.sqrDist,Ca.sqrLen=function(t){return Re.sqrLen(t.array)},Ca.squaredLength=Ca.sqrLen,Ca.sub=function(t,e,r){return Re.subtract(t.array,e.array,r.array),t._dirty=!0,t},Ca.subtract=Ca.sub,Ca.transformMat4=function(t,e,r){return Re.transformMat4(t.array,e.array,r.array),t._dirty=!0,t},Ca.transformQuat=function(t,e,r){return Re.transformQuat(t.array,e.array,r.array),t._dirty=!0,t};var Na=function(){this.position=new Cr,this.rotation=new Cr,this.velocity=null,this.angularVelocity=null,this.life=1,this.age=0,this.spriteSize=1,this.weight=1,this.emitter=null};Na.prototype.update=function(t){this.velocity&&Ce.scaleAndAdd(this.position.array,this.position.array,this.velocity.array,t),this.angularVelocity&&Ce.scaleAndAdd(this.rotation.array,this.rotation.array,this.angularVelocity.array,t)};var Pa=ke.extend({max:1e3,amount:20,life:null,position:null,rotation:null,velocity:null,angularVelocity:null,spriteSize:null,weight:null,_particlePool:null},function(){this._particlePool=[];for(var t=0;t<this.max;t++){var e=new Na;e.emitter=this,this._particlePool.push(e),this.velocity&&(e.velocity=new Cr),this.angularVelocity&&(e.angularVelocity=new Cr)}},{emit:function(t){for(var e,r=Math.min(this._particlePool.length,this.amount),n=0;n<r;n++)e=this._particlePool.pop(),this.position&&this.position.get(e.position),this.rotation&&this.rotation.get(e.rotation),this.velocity&&this.velocity.get(e.velocity),this.angularVelocity&&this.angularVelocity.get(e.angularVelocity),this.life&&(e.life=this.life.get()),this.spriteSize&&(e.spriteSize=this.spriteSize.get()),this.weight&&(e.weight=this.weight.get()),e.age=0,t.push(e)},kill:function(t){this._particlePool.push(t)}});Pa.constant=Ea.constant,Pa.vector=Ea.vector,Pa.random1D=Ea.random1D,Pa.random2D=Ea.random2D,Pa.random3D=Ea.random3D;var Ia=ke.extend({},{applyTo:function(t,e,r,n){}}),Oa=Ia.extend(function(){return{force:new Cr}},{applyTo:function(t,e,r,n){r>0&&Ce.scaleAndAdd(t.array,t.array,this.force.array,n/r)}});Q.import("@export clay.particle.vertex\nuniform mat4 worldView : WORLDVIEW;\nuniform mat4 projection : PROJECTION;\nattribute vec3 position : POSITION;\nattribute vec3 normal : NORMAL;\n#ifdef UV_ANIMATION\nattribute vec2 texcoord0 : TEXCOORD_0;\nattribute vec2 texcoord1 : TEXCOORD_1;\nvarying vec2 v_Uv0;\nvarying vec2 v_Uv1;\n#endif\nvarying float v_Age;\nvoid main() {\n v_Age = normal.x;\n float rotation = normal.y;\n vec4 worldViewPosition = worldView * vec4(position, 1.0);\n gl_Position = projection * worldViewPosition;\n float w = gl_Position.w;\n gl_PointSize = normal.z * projection[0].x / w;\n #ifdef UV_ANIMATION\n v_Uv0 = texcoord0;\n v_Uv1 = texcoord1;\n #endif\n}\n@end\n@export clay.particle.fragment\nuniform sampler2D sprite;\nuniform sampler2D gradient;\nuniform vec3 color : [1.0, 1.0, 1.0];\nuniform float alpha : 1.0;\nvarying float v_Age;\n#ifdef UV_ANIMATION\nvarying vec2 v_Uv0;\nvarying vec2 v_Uv1;\n#endif\nvoid main() {\n vec4 color = vec4(color, alpha);\n #ifdef SPRITE_ENABLED\n #ifdef UV_ANIMATION\n color *= texture2D(sprite, mix(v_Uv0, v_Uv1, gl_PointCoord));\n #else\n color *= texture2D(sprite, gl_PointCoord);\n #endif\n #endif\n #ifdef GRADIENT_ENABLED\n color *= texture2D(gradient, vec2(v_Age, 0.5));\n #endif\n gl_FragColor = color;\n}\n@end");var Da=new Q(Q.source("clay.particle.vertex"),Q.source("clay.particle.fragment")),Ba=wn.extend({loop:!0,oneshot:!1,duration:1,spriteAnimationTileX:1,spriteAnimationTileY:1,spriteAnimationRepeat:0,mode:wn.POINTS,ignorePicking:!0,_elapsedTime:0,_emitting:!0},function(){this.geometry=new pn({dynamic:!0}),this.material||(this.material=new nr({shader:Da,transparent:!0,depthMask:!1}),this.material.enableTexture("sprite")),this._particles=[],this._fields=[],this._emitters=[]},{culling:!1,frustumCulling:!1,castShadow:!1,receiveShadow:!1,addEmitter:function(t){this._emitters.push(t)},removeEmitter:function(t){this._emitters.splice(this._emitters.indexOf(t),1)},addField:function(t){this._fields.push(t)},removeField:function(t){this._fields.splice(this._fields.indexOf(t),1)},reset:function(){for(var t=0;t<this._particles.length;t++){var e=this._particles[t];e.emitter.kill(e)}this._particles.length=0,this._elapsedTime=0,this._emitting=!0},updateParticles:function(t){t/=1e3,this._elapsedTime+=t;var e=this._particles;if(this._emitting){for(var r=0;r<this._emitters.length;r++)this._emitters[r].emit(e);this.oneshot&&(this._emitting=!1)}for(var n=e.length,r=0;r<n;){var i=e[r];i.age+=t,i.age>=i.life?(i.emitter.kill(i),e[r]=e[n-1],e.pop(),n--):r++}for(var r=0;r<n;r++){var i=e[r];if(this._fields.length>0)for(var a=0;a<this._fields.length;a++)this._fields[a].applyTo(i.velocity,i.position,i.weight,t);i.update(t)}this._updateVertices()},_updateVertices:function(){var t=this.geometry,e=this.spriteAnimationTileX,r=this.spriteAnimationTileY,n=this.spriteAnimationRepeat,i=r*e*n,a=i>1,o=t.attributes.position.value,s=t.attributes.normal.value,u=t.attributes.texcoord0.value,l=t.attributes.texcoord1.value,h=this._particles.length;o&&o.length===3*h||(o=t.attributes.position.value=new Float32Array(3*h),s=t.attributes.normal.value=new Float32Array(3*h),a&&(u=t.attributes.texcoord0.value=new Float32Array(2*h),l=t.attributes.texcoord1.value=new Float32Array(2*h)));for(var c=1/e,f=0;f<h;f++){for(var d=this._particles[f],p=3*f,m=0;m<3;m++)o[p+m]=d.position.array[m],s[p]=d.age/d.life,s[p+1]=0,s[p+2]=d.spriteSize;var _=2*f;if(a){var g=d.age/d.life,v=Math.round(g*(i-1))*n,y=Math.floor(v*c),T=v-y*e;u[_]=T/e,u[_+1]=1-y/r,l[_]=(T+1)/e,l[_+1]=1-(y+1)/r}}t.dirty()},isFinished:function(){return this._elapsedTime>this.duration&&!this.loop},dispose:function(t){for(var e=0;e<this._particles.length;e++){var r=this._particles[e];r.emitter.kill(r)}this.geometry.dispose(t)},clone:function(){var t=new Ba({material:this.material});t.loop=this.loop,t.duration=this.duration,t.oneshot=this.oneshot,t.spriteAnimationRepeat=this.spriteAnimationRepeat,t.spriteAnimationTileY=this.spriteAnimationTileY,t.spriteAnimationTileX=this.spriteAnimationTileX,t.position.copy(this.position),t.rotation.copy(this.rotation),t.scale.copy(this.scale);for(var e=0;e<this._children.length;e++)t.add(this._children[e].clone());return t}});Q.import("@export clay.picking.color.vertex\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\nattribute vec3 position : POSITION;\n@import clay.chunk.skinning_header\nvoid main(){\n vec3 skinnedPosition = position;\n #ifdef SKINNING\n @import clay.chunk.skin_matrix\n skinnedPosition = (skinMatrixWS * vec4(position, 1.0)).xyz;\n #endif\n gl_Position = worldViewProjection * vec4(skinnedPosition, 1.0);\n}\n@end\n@end\n@export clay.picking.color.fragment\nuniform vec4 color : [1.0, 1.0, 1.0, 1.0];\nvoid main() {\n gl_FragColor = color;\n}\n@end");var Ua=ke.extend(function(){return{renderer:null,downSampleRatio:1,width:100,height:100,lookupOffset:1,_frameBuffer:null,_texture:null,_shader:null,_idMaterials:[],_lookupTable:[],_meshMaterials:[],_idOffset:0}},function(){this.renderer&&(this.width=this.renderer.getWidth(),this.height=this.renderer.getHeight()),this._init()},{_init:function(){this._texture=new An({width:this.width*this.downSampleRatio,height:this.height*this.downSampleRatio}),this._frameBuffer=new oi,this._shader=new Q(Q.source("clay.picking.color.vertex"),Q.source("clay.picking.color.fragment"))},setPrecision:function(t){this._texture.width=this.width*t,this._texture.height=this.height*t,this.downSampleRatio=t},resize:function(t,e){this._texture.width=t*this.downSampleRatio,this._texture.height=e*this.downSampleRatio,this.width=t,this.height=e,this._texture.dirty()},update:function(t,e){var r=this.renderer;r.getWidth()===this.width&&r.getHeight()===this.height||this.resize(r.width,r.height),this._frameBuffer.attach(this._texture),this._frameBuffer.bind(r),this._idOffset=this.lookupOffset,this._setMaterial(t),r.render(t,e),this._restoreMaterial(),this._frameBuffer.unbind(r)},_setMaterial:function(t){for(var e=0;e<t._children.length;e++){var r=t._children[e];if(r.geometry&&r.material&&r.material.shader){var n=this._idOffset++,i=n-this.lookupOffset,a=this._idMaterials[i];if(!a){a=new nr({shader:this._shader});var o=se(n);o[0]/=255,o[1]/=255,o[2]/=255,o[3]=1,a.set("color",o),this._idMaterials[i]=a}this._meshMaterials[i]=r.material,this._lookupTable[i]=r,r.material=a}r._children.length&&this._setMaterial(r)}},pick:function(t,e){var r=this.renderer,n=this.downSampleRatio;t=Math.ceil(n*t),e=Math.ceil(n*(this.height-e)),this._frameBuffer.bind(r);var i=new Uint8Array(4),a=r.gl;if(a.readPixels(t,e,1,1,a.RGBA,a.UNSIGNED_BYTE,i),this._frameBuffer.unbind(r),255===i[3]){var o=ue(i[0],i[1],i[2]);if(o){return this._lookupTable[o-this.lookupOffset]}}},_restoreMaterial:function(){for(var t=0;t<this._lookupTable.length;t++)this._lookupTable[t].material=this._meshMaterials[t]},dispose:function(t){this._frameBuffer.dispose(t)}}),Fa="undefined"==typeof document?{}:document,ka=ke.extend(function(){return{target:null,domElement:null,sensitivity:1,speed:.4,up:new Cr(0,1,0),verticalMoveLock:!1,timeline:null,_moveForward:!1,_moveBackward:!1,_moveLeft:!1,_moveRight:!1,_offsetPitch:0,_offsetRoll:0}},function(){this._lockChange=this._lockChange.bind(this),this._keyDown=this._keyDown.bind(this),this._keyUp=this._keyUp.bind(this),this._mouseMove=this._mouseMove.bind(this),this.domElement&&this.init()},{init:function(){var t=this.domElement;Ve.addEventListener(t,"click",this._requestPointerLock),Ve.addEventListener(Fa,"pointerlockchange",this._lockChange),Ve.addEventListener(Fa,"mozpointerlockchange",this._lockChange),Ve.addEventListener(Fa,"webkitpointerlockchange",this._lockChange),Ve.addEventListener(Fa,"keydown",this._keyDown),Ve.addEventListener(Fa,"keyup",this._keyUp),this.timeline&&this.timeline.on("frame",this._detectMovementChange,this)},dispose:function(){var t=this.domElement;t.exitPointerLock=t.exitPointerLock||t.mozExitPointerLock||t.webkitExitPointerLock,t.exitPointerLock&&t.exitPointerLock(),Ve.removeEventListener(t,"click",this._requestPointerLock),Ve.removeEventListener(Fa,"pointerlockchange",this._lockChange),Ve.removeEventListener(Fa,"mozpointerlockchange",this._lockChange),Ve.removeEventListener(Fa,"webkitpointerlockchange",this._lockChange),Ve.removeEventListener(Fa,"keydown",this._keyDown),Ve.removeEventListener(Fa,"keyup",this._keyUp),this.timeline&&this.timeline.off("frame",this._detectMovementChange)},_requestPointerLock:function(){var t=this;t.requestPointerLock=t.requestPointerLock||t.mozRequestPointerLock||t.webkitRequestPointerLock,t.requestPointerLock()},update:function(t){var e=this.target,r=this.target.position,n=e.localTransform.x.normalize(),i=e.localTransform.z.normalize();this.verticalMoveLock&&(i.y=0,i.normalize());var a=this.speed*t/20;this._moveForward&&r.scaleAndAdd(i,-a),this._moveBackward&&r.scaleAndAdd(i,a),this._moveLeft&&r.scaleAndAdd(n,-a/2),this._moveRight&&r.scaleAndAdd(n,a/2),e.rotateAround(e.position,this.up,-this._offsetPitch*t*Math.PI/360);var n=e.localTransform.x;e.rotateAround(e.position,n,-this._offsetRoll*t*Math.PI/360),this._offsetRoll=this._offsetPitch=0},_lockChange:function(){Fa.pointerLockElement===this.domElement||Fa.mozPointerLockElement===this.domElement||Fa.webkitPointerLockElement===this.domElement?Ve.addEventListener(Fa,"mousemove",this._mouseMove,!1):Ve.removeEventListener(Fa,"mousemove",this._mouseMove)},_mouseMove:function(t){var e=t.movementX||t.mozMovementX||t.webkitMovementX||0,r=t.movementY||t.mozMovementY||t.webkitMovementY||0;this._offsetPitch+=e*this.sensitivity/200,this._offsetRoll+=r*this.sensitivity/200,this.trigger("change")},_detectMovementChange:function(t){(this._moveForward||this._moveBackward||this._moveLeft||this._moveRight)&&this.trigger("change"),this.update(t)},_keyDown:function(t){switch(t.keyCode){case 87:case 37:this._moveForward=!0;break;case 83:case 40:this._moveBackward=!0;break;case 65:case 37:this._moveLeft=!0;break;case 68:case 39:this._moveRight=!0}this.trigger("change")},_keyUp:function(t){switch(t.keyCode){case 87:case 37:this._moveForward=!1;break;case 83:case 40:this._moveBackward=!1;break;case 65:case 37:this._moveLeft=!1;break;case 68:case 39:this._moveRight=!1}}}),Ha=ke.extend(function(){return{target:null,moveSpeed:.1,lookAroundSpeed:.1,up:new Cr(0,1,0),timeline:null,onStandardGamepadReady:function(t){},onGamepadDisconnected:function(t){},_moveForward:!1,_moveBackward:!1,_moveLeft:!1,_moveRight:!1,_offsetPitch:0,_offsetRoll:0,_connectedGamepadIndex:0,_standardGamepadAvailable:!1,_gamepadAxisThreshold:.3}},function(){this._checkGamepadCompatibility=this._checkGamepadCompatibility.bind(this),this._disconnectGamepad=this._disconnectGamepad.bind(this),this._getStandardGamepad=this._getStandardGamepad.bind(this),this._scanPressedGamepadButtons=this._scanPressedGamepadButtons.bind(this),this._scanInclinedGamepadAxes=this._scanInclinedGamepadAxes.bind(this),this.update=this.update.bind(this),"function"==typeof navigator.getGamepads&&this.init()},{init:function(){Ve.addEventListener(window,"gamepadconnected",this._checkGamepadCompatibility),this.timeline&&this.timeline.on("frame",this.update),Ve.addEventListener(window,"gamepaddisconnected",this._disconnectGamepad)},dispose:function(){Ve.removeEventListener(window,"gamepadconnected",this._checkGamepadCompatibility),this.timeline&&this.timeline.off("frame",this.update),Ve.removeEventListener(window,"gamepaddisconnected",this._disconnectGamepad)},update:function(t){if(this._standardGamepadAvailable){this._scanPressedGamepadButtons(),this._scanInclinedGamepadAxes();var e=this.target,r=this.target.position,n=e.localTransform.x.normalize(),i=e.localTransform.z.normalize(),a=this.moveSpeed*t/20;this._moveForward&&r.scaleAndAdd(i,-a),this._moveBackward&&r.scaleAndAdd(i,a),this._moveLeft&&r.scaleAndAdd(n,-a),this._moveRight&&r.scaleAndAdd(n,a),e.rotateAround(e.position,this.up,-this._offsetPitch*t*Math.PI/360);var n=e.localTransform.x;e.rotateAround(e.position,n,-this._offsetRoll*t*Math.PI/360),!0!==this._moveForward&&!0!==this._moveBackward&&!0!==this._moveLeft&&!0!==this._moveRight&&0===this._offsetPitch&&0===this._offsetRoll||this.trigger("update"),this._moveForward=this._moveBackward=this._moveLeft=this._moveRight=!1,this._offsetPitch=this._offsetRoll=0}},_checkGamepadCompatibility:function(t){"standard"===t.gamepad.mapping&&(this._standardGamepadIndex=t.gamepad.index,this._standardGamepadAvailable=!0,this.onStandardGamepadReady(t.gamepad))},_disconnectGamepad:function(t){this._standardGamepadAvailable=!1,this.onGamepadDisconnected(t.gamepad)},_getStandardGamepad:function(){return navigator.getGamepads()[this._standardGamepadIndex]},_scanPressedGamepadButtons:function(){for(var t=this._getStandardGamepad().buttons,e=0;e<t.length;e++){if(t[e].pressed)switch(e){case 12:this._moveForward=!0;break;case 13:this._moveBackward=!0;break;case 14:this._moveLeft=!0;break;case 15:this._moveRight=!0}}},_scanInclinedGamepadAxes:function(){for(var t=this._getStandardGamepad().axes,e=0;e<t.length;e++){var r=t[e];if(Math.abs(r)>this._gamepadAxisThreshold)switch(e){case 0:this._moveLeft=r<0,this._moveRight=r>0;break;case 1:this._moveForward=r<0,this._moveBackward=r>0;break;case 2:this._offsetPitch+=r*this.lookAroundSpeed;break;case 3:this._offsetRoll+=r*this.lookAroundSpeed}}}}),Ga=function(){this._track=[]};Ga.prototype={constructor:Ga,recognize:function(t,e,r){return this._doTrack(t,e,r),this._recognize(t)},clear:function(){return this._track.length=0,this},_doTrack:function(t,e,r){var n=t.targetTouches;if(n){for(var i={points:[],touches:[],target:e,event:t},a=0,o=n.length;a<o;a++){var s=n[a];i.points.push([s.clientX,s.clientY]),i.touches.push(s)}this._track.push(i)}},_recognize:function(t){for(var e in Va)if(Va.hasOwnProperty(e)){var r=Va[e](this._track,t);if(r)return r}}};var Va={pinch:function(t,e){var r=t.length;if(r){var n=(t[r-1]||{}).points,i=(t[r-2]||{}).points||n;if(i&&i.length>1&&n&&n.length>1){var a=le(n)/le(i);!isFinite(a)&&(a=1),e.pinchScale=a;var o=he(n);return e.pinchX=o[0],e.pinchY=o[1],{type:"pinch",target:t[0].target,event:e}}}}},Wa=[[0,0],[0,1],[1,1],[1,0]],za=[0,1,2,2,3,0],Xa=Cn.extend({camera:null,plane:null,maxGrid:0,frustumCulling:!1},function(){var t=this.geometry=new pn({dynamic:!0});t.attributes.position.init(6),t.attributes.normal.init(6),t.attributes.texcoord0.init(6),t.indices=new Uint16Array(6),this.plane=new Yr},{updateGeometry:function(){var t=this._unProjectGrid();if(t){for(var e=this.geometry.attributes.position,r=this.geometry.attributes.normal,n=this.geometry.attributes.texcoord0,i=this.geometry.indices,a=0;a<6;a++){var o=za[a];e.set(a,t[o].array),r.set(a,this.plane.normal.array),n.set(a,Wa[o]),i[a]=a}this.geometry.dirty()}},_unProjectGrid:function(){for(var t=new Yr,e=[0,1,0,2,1,3,2,3,4,5,4,6,5,7,6,7,0,4,1,5,2,6,3,7],r=new Cr,n=new Cr,i=[],a=[],o=0;o<4;o++)a[o]=new Cr(0,0);var s=new en;return function(){t.copy(this.plane),t.applyTransform(this.camera.viewMatrix);for(var o=this.camera.frustum.vertices,u=0,l=0;l<12;l++){r.array=o[e[2*l]],n.array=o[e[2*l+1]];var h=t.intersectLine(r,n,i[u]);h&&(i[u]||(i[u]=h),u++)}if(0!==u){for(var l=0;l<u;l++)i[l].applyProjection(this.camera.projectionMatrix);for(var c=i[0].array[0],f=i[0].array[1],d=i[0].array[0],p=i[0].array[1],l=1;l<u;l++)d=Math.max(d,i[l].array[0]),p=Math.max(p,i[l].array[1]),c=Math.min(c,i[l].array[0]),f=Math.min(f,i[l].array[1]);if(c!=d&&f!=p){a[0].array[0]=c,a[0].array[1]=f,a[1].array[0]=c,a[1].array[1]=p,a[2].array[0]=d,a[2].array[1]=p,a[3].array[0]=d,a[3].array[1]=f;for(var l=0;l<4;l++)this.camera.castRay(a[l],s),s.intersectPlane(this.plane,a[l]);return a}}}}()}),ja={left:0,middle:1,right:2},qa=ke.extend(function(){return{timeline:null,domElement:null,target:null,_center:new Cr,minDistance:.1,maxDistance:1e3,maxOrthographicSize:300,minOrthographicSize:30,orthographicAspect:1,minAlpha:-90,maxAlpha:90,minBeta:-1/0,maxBeta:1/0,autoRotateAfterStill:0,autoRotateDirection:"cw",autoRotateSpeed:60,panMouseButton:"middle",rotateMouseButton:"left",_mode:"rotate",damping:.8,rotateSensitivity:1,zoomSensitivity:1,panSensitivity:1,_needsUpdate:!1,_rotating:!1,_phi:0,_theta:0,_mouseX:0,_mouseY:0,_rotateVelocity:new Se,_panVelocity:new Se,_distance:20,_zoomSpeed:0,_stillTimeout:0,_animators:[],_gestureMgr:new Ga}},function(){this._mouseDownHandler=this._mouseDownHandler.bind(this),this._mouseWheelHandler=this._mouseWheelHandler.bind(this),this._mouseMoveHandler=this._mouseMoveHandler.bind(this),this._mouseUpHandler=this._mouseUpHandler.bind(this),this._pinchHandler=this._pinchHandler.bind(this),this.init()},{init:function(){var t=this.domElement;Ve.addEventListener(t,"touchstart",this._mouseDownHandler),Ve.addEventListener(t,"mousedown",this._mouseDownHandler),Ve.addEventListener(t,"wheel",this._mouseWheelHandler),this.timeline&&this.timeline.on("frame",this.update,this),this.target&&this.decomposeTransform()},dispose:function(){var t=this.domElement;Ve.removeEventListener(t,"touchstart",this._mouseDownHandler),Ve.removeEventListener(t,"touchmove",this._mouseMoveHandler),Ve.removeEventListener(t,"touchend",this._mouseUpHandler),Ve.removeEventListener(t,"mousedown",this._mouseDownHandler),Ve.removeEventListener(t,"mousemove",this._mouseMoveHandler),Ve.removeEventListener(t,"mouseup",this._mouseUpHandler),Ve.removeEventListener(t,"wheel",this._mouseWheelHandler),Ve.removeEventListener(t,"mouseout",this._mouseUpHandler),this.timeline&&this.timeline.off("frame",this.update),this.stopAllAnimation()},getDistance:function(){return this._distance},setDistance:function(t){this._distance=t,this._needsUpdate=!0},getOrthographicSize:function(){return this._orthoSize},setOrthographicSize:function(t){this._orthoSize=t,this._needsUpdate=!0},getAlpha:function(){return this._theta/Math.PI*180},getBeta:function(){return-this._phi/Math.PI*180},getCenter:function(){return this._center.toArray()},setAlpha:function(t){t=Math.max(Math.min(this.maxAlpha,t),this.minAlpha),this._theta=t/180*Math.PI,this._needsUpdate=!0},setBeta:function(t){t=Math.max(Math.min(this.maxBeta,t),this.minBeta),this._phi=-t/180*Math.PI,this._needsUpdate=!0},setCenter:function(t){this._center.setArray(t)},setOption:function(t){t=t||{},["autoRotate","autoRotateAfterStill","autoRotateDirection","autoRotateSpeed","damping","minDistance","maxDistance","minOrthographicSize","maxOrthographicSize","orthographicAspect","minAlpha","maxAlpha","minBeta","maxBeta","rotateSensitivity","zoomSensitivity","panSensitivity"].forEach(function(e){null!=t[e]&&(this[e]=t[e])},this),null!=t.distance&&this.setDistance(t.distance),null!=t.orthographicSize&&this.setOrthographicSize(t.orthographicSize),null!=t.alpha&&this.setAlpha(t.alpha),null!=t.beta&&this.setBeta(t.beta),t.center&&this.setCenter(t.center),this.target&&(this._updateTransform(),this.target.update())},animateTo:function(t){var e=this,r={},n={},i=this.timeline;if(i)return null!=t.distance&&(r.distance=this.getDistance(),n.distance=t.distance),null!=t.orthographicSize&&(r.orthographicSize=this.getOrthographicSize(),n.orthographicSize=t.orthographicSize),null!=t.alpha&&(r.alpha=this.getAlpha(),n.alpha=t.alpha),null!=t.beta&&(r.beta=this.getBeta(),n.beta=t.beta),null!=t.center&&(r.center=this.getCenter(),n.center=t.center),this._addAnimator(i.animate(r).when(t.duration||1e3,n).during(function(){null!=r.alpha&&e.setAlpha(r.alpha),null!=r.beta&&e.setBeta(r.beta),null!=r.distance&&e.setDistance(r.distance),null!=r.orthographicSize&&e.setOrthographicSize(r.orthographicSize),null!=r.center&&e.setCenter(r.center),e._needsUpdate=!0}).done(t.done)).start(t.easing||"linear")},stopAllAnimation:function(){for(var t=0;t<this._animators.length;t++)this._animators[t].stop();this._animators.length=0},_isAnimating:function(){return this._animators.length>0},update:function(t){if(t=t||16,this._rotating){var e=("cw"===this.autoRotateDirection?1:-1)*this.autoRotateSpeed/180*Math.PI;this._phi-=e*t/1e3,this._needsUpdate=!0}else this._rotateVelocity.len()>0&&(this._needsUpdate=!0);(Math.abs(this._zoomSpeed)>.01||this._panVelocity.len()>0)&&(this._needsUpdate=!0),this._needsUpdate&&(this._updateDistanceOrSize(Math.min(t,50)),this._updatePan(Math.min(t,50)),this._updateRotate(Math.min(t,50)),this._updateTransform(),this.target.update(),this.trigger("update"),this._needsUpdate=!1)},_updateRotate:function(t){var e=this._rotateVelocity;this._phi=e.y*t/20+this._phi,this._theta=e.x*t/20+this._theta,this.setAlpha(this.getAlpha()),this.setBeta(this.getBeta()),this._vectorDamping(e,this.damping)},_updateDistanceOrSize:function(t){this._setDistance(this._distance+this._zoomSpeed*t/20),this.target instanceof Rn||this._setOrthoSize(this._orthoSize+this._zoomSpeed*t/20),this._zoomSpeed*=Math.pow(this.damping,t/16)},_setDistance:function(t){
this._distance=Math.max(Math.min(t,this.maxDistance),this.minDistance)},_setOrthoSize:function(t){this._orthoSize=Math.max(Math.min(t,this.maxOrthographicSize),this.minOrthographicSize);var e=this.target,r=this._orthoSize,n=r*this.orthographicAspect;e.left=-n/2,e.right=n/2,e.top=r/2,e.bottom=-r/2},_updatePan:function(t){var e=this._panVelocity,r=this._distance,n=this.target,i=n.worldTransform.y,a=n.worldTransform.x;this._center.scaleAndAdd(a,-e.x*r/200).scaleAndAdd(i,-e.y*r/200),this._vectorDamping(e,0),e.x=e.y=0},_updateTransform:function(){var t=this.target,e=new Cr,r=this._theta+Math.PI/2,n=this._phi+Math.PI/2,i=Math.sin(r);e.x=i*Math.cos(n),e.y=-Math.cos(r),e.z=i*Math.sin(n),t.position.copy(this._center).scaleAndAdd(e,this._distance),t.rotation.identity().rotateY(-this._phi).rotateX(-this._theta)},_startCountingStill:function(){clearTimeout(this._stillTimeout);var t=this.autoRotateAfterStill,e=this;!isNaN(t)&&t>0&&(this._stillTimeout=setTimeout(function(){e._rotating=!0},1e3*t))},_vectorDamping:function(t,e){var r=t.len();r*=e,r<1e-4&&(r=0),t.normalize().scale(r)},decomposeTransform:function(){if(this.target){this.target.updateWorldTransform();var t=this.target.worldTransform.z,e=Math.asin(t.y),r=Math.atan2(t.x,t.z);this._theta=e,this._phi=-r,this.setBeta(this.getBeta()),this.setAlpha(this.getAlpha()),this._setDistance(this.target.position.dist(this._center)),this.target instanceof Rn||this._setOrthoSize(this.target.top-this.target.bottom)}},_mouseDownHandler:function(t){if(!this._isAnimating()){var e=t.clientX,r=t.clientY;if(t.targetTouches){var n=t.targetTouches[0];e=n.clientX,r=n.clientY,this._mode="rotate",this._processGesture(t,"start")}else t.button===ja[this.rotateMouseButton]?this._mode="rotate":t.button===ja[this.panMouseButton]?(this._mode="pan",t.preventDefault()):this._mode=null;var i=this.domElement;Ve.addEventListener(i,"touchmove",this._mouseMoveHandler),Ve.addEventListener(i,"touchend",this._mouseUpHandler),Ve.addEventListener(i,"mousemove",this._mouseMoveHandler),Ve.addEventListener(i,"mouseup",this._mouseUpHandler),Ve.addEventListener(i,"mouseout",this._mouseUpHandler),this._rotateVelocity.set(0,0),this._rotating=!1,this.autoRotate&&this._startCountingStill(),this._mouseX=e,this._mouseY=r}},_mouseMoveHandler:function(t){if(!this._isAnimating()){var e,r=t.clientX,n=t.clientY;if(t.targetTouches){var i=t.targetTouches[0];r=i.clientX,n=i.clientY,e=this._processGesture(t,"change")}var a=ce(this.panSensitivity),o=ce(this.rotateSensitivity);e||("rotate"===this._mode?(this._rotateVelocity.y+=(r-this._mouseX)/this.domElement.clientWidth*2*o[0],this._rotateVelocity.x+=(n-this._mouseY)/this.domElement.clientHeight*2*o[1]):"pan"===this._mode&&(this._panVelocity.x+=(r-this._mouseX)/this.domElement.clientWidth*a[0]*400,this._panVelocity.y+=(-n+this._mouseY)/this.domElement.clientHeight*a[1]*400)),this._mouseX=r,this._mouseY=n,t.preventDefault&&t.preventDefault()}},_mouseWheelHandler:function(t){if(!this._isAnimating()){var e=t.deltaY;0!==e&&this._zoomHandler(t,e>0?1:-1)}},_pinchHandler:function(t){this._isAnimating()||this._zoomHandler(t,t.pinchScale>1?.4:-.4)},_zoomHandler:function(t,e){var r;r=this.target instanceof Rn?Math.max(Math.max(Math.min(this._distance-this.minDistance,this.maxDistance-this._distance))/20,.5):Math.max(Math.max(Math.min(this._orthoSize-this.minOrthographicSize,this.maxOrthographicSize-this._orthoSize))/20,.5),this._zoomSpeed=(e>0?-1:1)*r*this.zoomSensitivity,this._rotating=!1,this.autoRotate&&"rotate"===this._mode&&this._startCountingStill(),t.preventDefault&&t.preventDefault()},_mouseUpHandler:function(t){var e=this.domElement;Ve.removeEventListener(e,"touchmove",this._mouseMoveHandler),Ve.removeEventListener(e,"touchend",this._mouseUpHandler),Ve.removeEventListener(e,"mousemove",this._mouseMoveHandler),Ve.removeEventListener(e,"mouseup",this._mouseUpHandler),Ve.removeEventListener(e,"mouseout",this._mouseUpHandler),this._processGesture(t,"end")},_addAnimator:function(t){var e=this._animators;return e.push(t),t.done(function(){var r=e.indexOf(t);r>=0&&e.splice(r,1)}),t},_processGesture:function(t,e){var r=this._gestureMgr;"start"===e&&r.clear();var n=r.recognize(t,null,this.domElement);if("end"===e&&r.clear(),n){var i=n.type;t.gestureEvent=i,this._pinchHandler(n.event)}return n}});Object.defineProperty(qa.prototype,"autoRotate",{get:function(){return this._autoRotate},set:function(t){this._autoRotate=t,this._rotating=t}}),Object.defineProperty(qa.prototype,"target",{get:function(){return this._target},set:function(t){t&&t.target&&this.setCenter(t.target.toArray()),this._target=t,this.decomposeTransform()}});var Ya=pn.extend({dynamic:!1}),Ka={merge:function(t,e){if(t.length){var r=t[0],n=r.geometry,i=r.material,a=new pn({dynamic:!1});a.boundingBox=new Vr;for(var o=n.getEnabledAttributes(),s=0;s<o.length;s++){var u=o[s],l=n.attributes[u];a.attributes[u]||(a.attributes[u]=l.clone(!1))}for(var h=Er.create(),c=0,f=0,d=0;d<t.length;d++){var p=t[d].geometry;p.boundingBox&&(p.boundingBox.applyTransform(e?t[d].worldTransform:t[d].localTransform),a.boundingBox.union(p.boundingBox)),c+=p.vertexCount,f+=p.triangleCount}for(var m=0;m<o.length;m++){var u=o[m];a.attributes[u].init(c)}a.indices=c>=65535?new Uint32Array(3*f):new Uint16Array(3*f);for(var _=0,g=0,v=n.isUseIndices(),y=0;y<t.length;y++){var T=t[y],p=T.geometry,c=p.vertexCount,x=e?T.worldTransform.array:T.localTransform.array;Er.invert(h,x),Er.transpose(h,h);for(var E=0;E<o.length;E++){var u=o[E],A=p.attributes[u],b=a.attributes[u];if(A.value.length){for(var S=A.value.length,M=A.size,w=_*M,C=S/M,s=0;s<S;s++)b.value[w+s]=A.value[s];"position"===u?Ce.forEach(b.value,M,w,C,Ce.transformMat4,x):"normal"!==u&&"tangent"!==u||Ce.forEach(b.value,M,w,C,Ce.transformMat4,h)}}if(v){for(var S=p.indices.length,s=0;s<S;s++)a.indices[s+g]=p.indices[s]+_;g+=S}_+=c}return new Cn({material:i,geometry:a})}},splitByJoints:function(t,e,r){var n=t.geometry,i=t.skeleton,a=t.material,o=t.joints;if(n&&i&&o.length){if(o.length<e)return t;for(var s=n.indices,u=n.triangleCount,l=u,h=[],c=n.attributes.joint.value,f=0;f<u;f++)h[f]=!1;for(var d=[],p=[],m=function(t){return o[t]};l>0;){for(var _=[],g=[],v=[],y=0,f=0;f<o.length;f++)g[f]=-1;for(var T=0;T<u;T++)if(!h[T]){for(var x=!0,E=0,f=0;f<3;f++)for(var A=s[3*T+f],b=0;b<4;b++){var S=c[4*A+b];S>=0&&-1===g[S]&&(y<e?(g[S]=y,v[y++]=S,d[E++]=S):x=!1)}if(x)_.push(s.subarray(3*T,3*(T+1))),h[T]=!0,l--;else for(var f=0;f<E;f++)g[d[f]]=-1,v.pop(),y--}p.push({triangles:_,joints:v.map(m),jointReverseMap:g})}var M=new zr({name:t.name}),w=n.getEnabledAttributes();w.splice(w.indexOf("joint"),1);for(var C=[],R=0;R<p.length;R++){for(var L=p[R],N=L.jointReverseMap,y=L.joints.length,P=new pn,I=new Cn({name:[t.name,f].join("-"),material:a,geometry:P,skeleton:i,joints:L.joints.slice()}),O=0,D=n.vertexCount,f=0;f<D;f++)C[f]=-1;for(var T=0;T<L.triangles.length;T++)for(var B=L.triangles[T],f=0;f<3;f++){var A=B[f];-1===C[A]&&(C[A]=O,O++)}for(var U=0;U<w.length;U++){var F=w[U],k=P.attributes[F];k.init(O)}P.attributes.joint.value=new Float32Array(4*O),P.indices=O>65535?new Uint32Array(3*L.triangles.length):new Uint16Array(3*L.triangles.length);var H=0;O=0;for(var f=0;f<D;f++)C[f]=-1;for(var T=0;T<L.triangles.length;T++)for(var G=L.triangles[T],f=0;f<3;f++){var A=G[f];if(-1===C[A]){C[A]=O;for(var U=0;U<w.length;U++)for(var F=w[U],V=n.attributes[F],k=P.attributes[F],W=V.size,b=0;b<W;b++)k.value[O*W+b]=V.value[A*W+b];for(var b=0;b<4;b++){var S=n.attributes.joint.value[4*A+b],z=4*O+b;P.attributes.joint.value[z]=S>=0?N[S]:-1}O++}P.indices[H++]=C[A]}P.updateBoundingBox(),M.add(I)}for(var X=t.children(),f=0;f<X.length;f++)M.add(X[f]);if(M.position.copy(t.position),M.rotation.copy(t.rotation),M.scale.copy(t.scale),r&&t.getParent()){var j=t.getParent();j.remove(t),j.add(M)}return M}}},Za={version:1,type:"Geometry",generator:"util.transferable.toObject"},Ja={toObject:function(t,e){if(!t)return null;var r={metadata:Fe.extend({},Za)},n=[];r.dynamic=t.dynamic,t.boundingBox&&(r.boundingBox={min:t.boundingBox.min.toArray(),max:t.boundingBox.max.toArray()}),t.indices&&t.indices.length>0&&(r.indices=de(t.indices,e),n.push(r.indices.buffer)),r.attributes={};for(var i in t.attributes)if(t.attributes.hasOwnProperty(i)){var a=t.attributes[i];a&&a.value&&a.value.length>0&&(a=r.attributes[i]=fe(a,e),n.push(a.value.buffer))}return{data:r,buffers:n}},toGeometry:function(t){if(!t)return null;if(t.data&&t.buffers)return Ja.toGeometry(t.data);if(!t.metadata||t.metadata.generator!==Za.generator)throw new Error("[util.transferable.toGeometry] the object is not generated by util.transferable.");var e={dynamic:t.dynamic,indices:t.indices};if(t.boundingBox){var r=(new Cr).setArray(t.boundingBox.min),n=(new Cr).setArray(t.boundingBox.max);e.boundingBox=new Vr(r,n)}var i=new pn(e);for(var a in t.attributes)if(t.attributes.hasOwnProperty(a)){var o=t.attributes[a];i.attributes[a]=new pn.Attribute(o.name,o.type,o.size,o.semantic),i.attributes[a].value=o.value}return i}};Q.import("@export clay.vr.disorter.output.vertex\nattribute vec2 texcoord: TEXCOORD_0;\nattribute vec3 position: POSITION;\nvarying vec2 v_Texcoord;\nvoid main()\n{\n v_Texcoord = texcoord;\n gl_Position = vec4(position.xy, 0.5, 1.0);\n}\n@end\n@export clay.vr.disorter.output.fragment\nuniform sampler2D texture;\nvarying vec2 v_Texcoord;\nvoid main()\n{\n gl_FragColor = texture2D(texture, v_Texcoord);\n}\n@end");var Qa=ke.extend(function(){return{clearColor:[0,0,0,1],_mesh:new Cn({geometry:new pn({dynamic:!0}),culling:!1,material:new nr({depthTest:!1,shader:new Q({vertex:Q.source("clay.vr.disorter.output.vertex"),fragment:Q.source("clay.vr.disorter.output.fragment")})})}),_fakeCamera:new Rn}},{render:function(t,e){var r=this.clearColor,n=t.gl;n.clearColor(r[0],r[1],r[2],r[3]),n.clear(n.COLOR_BUFFER_BIT),n.disable(n.BLEND),this._mesh.material.set("texture",e),t.saveViewport(),t.setViewport(0,0,t.getWidth(),t.getHeight()),t.renderPass([this._mesh],this._fakeCamera),t.restoreViewport()},updateFromVRDisplay:function(t){t.deviceInfo_?this._updateMesh(20,20,t.deviceInfo_):console.warn("Cant get vrDisplay.deviceInfo_, seems code changed")},_updateMesh:function(t,e,r){var n=this._mesh.geometry.attributes.position,i=this._mesh.geometry.attributes.texcoord0;n.init(2*t*e),i.init(2*t*e);for(var a=r.getLeftEyeVisibleTanAngles(),o=r.getLeftEyeNoLensTanAngles(),s=r.getLeftEyeVisibleScreenRect(o),u=0,l=[],h=[],c=0;c<2;c++){for(var f=0;f<e;f++)for(var d=0;d<t;d++,u++){var p=d/(t-1),m=f/(e-1),_=p,g=m,v=pe(a[0],a[2],p),y=pe(a[3],a[1],m),T=Math.sqrt(v*v+y*y),x=r.distortion.distortInverse(T),E=v*x/T,A=y*x/T;p=(E-o[0])/(o[2]-o[0]),m=(A-o[3])/(o[1]-o[3]),p=2*(s.x+p*s.width-.5),m=2*(s.y+m*s.height-.5),l[0]=p,l[1]=m,l[2]=0,h[0]=.5*_+.5*c,h[1]=g,n.set(u,l),i.set(u,h)}var b=a[2]-a[0];a[0]=-(b+a[0]),a[2]=b-a[2],b=o[2]-o[0],o[0]=-(b+o[0]),o[2]=b-o[2],s.x=1-(s.x+s.width)}for(var S=new Uint16Array(2*(t-1)*(e-1)*6),M=t/2,w=e/2,u=0,C=0,c=0;c<2;c++)for(var f=0;f<e;f++)for(var d=0;d<t;d++,u++)0!==d&&0!==f&&(d<=M==f<=w?(S[C++]=u,S[C++]=u-t-1,S[C++]=u-t,S[C++]=u-t-1,S[C++]=u,S[C++]=u-1):(S[C++]=u-1,S[C++]=u-t,S[C++]=u,S[C++]=u-t,S[C++]=u-1,S[C++]=u-t-1));this._mesh.geometry.indices=S,this._mesh.geometry.dirty()}}),$a=new Ur,to=zr.extend(function(){return{aspect:.5,_leftCamera:new Rn,_rightCamera:new Rn,_eyeLeft:new Ur,_eyeRight:new Ur,_frameData:null}},{updateFromCamera:function(t,e,r,n){t.transformNeedsUpdate()&&console.warn("Node transform is not updated"),e=null==e?10:e,r=null==r?1:r,n=null==n?.064:n;var i=t.fov,a=t.aspect*this.aspect,o=t.near;$a.copy(t.projectionMatrix);var s,u,n=n/2,l=n*o/e,h=o*Math.tan(Math.PI/180*i*.5)/r;this._eyeLeft.array[12]=-n,this._eyeRight.array[12]=n,s=-h*a+l,u=h*a+l,$a.array[0]=2*o/(u-s),$a.array[8]=(u+s)/(u-s),this._leftCamera.projectionMatrix.copy($a),s=-h*a-l,u=h*a-l,$a.array[0]=2*o/(u-s),$a.array[8]=(u+s)/(u-s),this._rightCamera.projectionMatrix.copy($a),this._leftCamera.worldTransform.copy(t.worldTransform).multiply(this._eyeLeft),this._rightCamera.worldTransform.copy(t.worldTransform).multiply(this._eyeRight),this._leftCamera.decomposeWorldTransform(),this._leftCamera.decomposeProjectionMatrix(),this._rightCamera.decomposeWorldTransform(),this._rightCamera.decomposeProjectionMatrix()},updateFromVRDisplay:function(t,e){if("undefined"!=typeof VRFrameData){var r=this._frameData||(this._frameData=new VRFrameData);t.getFrameData(r);var n=this._leftCamera,i=this._rightCamera;n.projectionMatrix.setArray(r.leftProjectionMatrix),n.decomposeProjectionMatrix(),n.viewMatrix.setArray(r.leftViewMatrix),n.setViewMatrix(n.viewMatrix),i.projectionMatrix.setArray(r.rightProjectionMatrix),i.decomposeProjectionMatrix(),i.viewMatrix.setArray(r.rightViewMatrix),i.setViewMatrix(i.viewMatrix),e&&e.worldTransform&&(e.transformNeedsUpdate()&&console.warn("Node transform is not updated"),n.worldTransform.multiplyLeft(e.worldTransform),n.decomposeWorldTransform(),i.worldTransform.multiplyLeft(e.worldTransform),i.decomposeWorldTransform())}},getLeftCamera:function(){return this._leftCamera},getRightCamera:function(){return this._rightCamera}}),eo={Animator:d,Blend1DClip:ye,Blend2DClip:we,Clip:_e,easing:me,SamplerTrack:Pe,Timeline:ze,TrackClip:Xe},ro={Task:jt,TaskGroup:ki},no={Orthographic:Ln,Perspective:Rn},io={Compositor:Wi,CompositorNode:Gi,createCompositor:Kt,FilterNode:ji,Graph:Vi,Pass:hi,SceneNode:zi,TextureNode:Xi,TexturePool:wi},ao={Base:ke,Cache:un,color:Je,glenum:Ye,GLInfo:S,LinkedList:Ke,LRU:Ze,mixin:{extend:Ie,notifier:Oe},request:Ge,util:Fe,vendor:Ve},oo={GBuffer:sa,Renderer:ha},so={glmatrix:da},uo={Cone:ua,Cube:gn,Cylinder:la,ParametricSurface:yn,Plane:mn,Sphere:vn},lo={Ambient:ei,AmbientCubemap:Si,AmbientSH:Mi,Directional:Qn,Point:$n,Sphere:_a,Spot:ti,Tube:ga},ho={FX:va,GLTF:Jn},co={BoundingBox:Vr,Frustum:tn,Matrix2:ya,Matrix2d:Ta,Matrix3:xa,Matrix4:Ur,Plane:Yr,Quaternion:Or,Ray:en,util:xn,Value:Ea,Vector2:Se,Vector3:Cr,Vector4:Ca},fo={Emitter:Pa,Field:Ia,ForceField:Oa,Particle:Na,ParticleRenderable:Ba},po={PixelPicking:Ua,RayPicking:Pi},mo={FreeControl:ka,GamepadControl:Ha,GestureMgr:Ga,InfinitePlane:Xa,OrbitControl:qa,Skybox:ci,Skydome:ci},_o={EnvironmentMap:di,ShadowMap:Ni},go={library:Gn,registerBuiltinCompositor:Yt,source:{header:{light:jr}}},vo={cubemap:Ai,dds:gi,delaunay:xe,hdr:xi,mesh:Ka,sh:Ii,texture:Ei,transferable:Ja},yo={CardboardDistorter:Qa,StereoCamera:to};t.animation=eo,t.application=Fi,t.async=ro,t.Camera=rn,t.camera=no,t.compositor=io,t.core=ao,t.createCompositor=Kt,t.deferred=oo,t.dep=so,t.FrameBuffer=oi,t.Geometry=pn,t.geometry=uo,t.GeometryBase=ln,t.InstancedMesh=ma,t.Joint=Vn,t.Light=qr,t.light=lo,t.loader=ho,t.Material=nr,t.math=co,t.BoundingBox=Vr,t.Frustum=tn,t.Matrix2=ya,t.Matrix2d=Ta,t.Matrix3=xa,t.Matrix4=Ur,t.Plane=Yr,t.Quaternion=Or,t.Ray=en,t.Value=Ea,t.Vector2=Se,t.Vector3=Cr,t.Vector4=Ca,t.Mesh=Cn,t.Node=zr,t.particle=fo,t.picking=po,t.plugin=mo,t.prePass=_o,t.Renderable=wn,t.Renderer=Mr,t.Scene=sn,t.Shader=Q,t.shader=go,t.Skeleton=Xn,t.StandardMaterial=Fn,t.StaticGeometry=Ya,t.Texture=Tn,t.Texture2D=An,t.TextureCube=Mn,t.Timeline=ze,t.util=vo,t.version="1.3.0",t.vr=yo,Object.defineProperty(t,"__esModule",{value:!0})});
