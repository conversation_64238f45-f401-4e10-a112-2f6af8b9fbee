<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/core/mixin/</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../../index.html">all files</a> src/core/mixin/
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">69.83% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>81/116</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">58.82% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>40/68</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">81.25% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>13/16</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">69.3% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>79/114</span>
      </div>
    </div>
  </div>
  <div class='status-line medium'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="extend.js"><a href="extend.js.html">extend.js</a></td>
	<td data-value="97.56" class="pic high"><div class="chart"><div class="cover-fill" style="width: 97%;"></div><div class="cover-empty" style="width:3%;"></div></div></td>
	<td data-value="97.56" class="pct high">97.56%</td>
	<td data-value="41" class="abs high">40/41</td>
	<td data-value="83.33" class="pct high">83.33%</td>
	<td data-value="18" class="abs high">15/18</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="5" class="abs high">5/5</td>
	<td data-value="97.56" class="pct high">97.56%</td>
	<td data-value="41" class="abs high">40/41</td>
	</tr>

<tr>
	<td class="file medium" data-value="notifier.js"><a href="notifier.js.html">notifier.js</a></td>
	<td data-value="54.67" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 54%;"></div><div class="cover-empty" style="width:46%;"></div></div></td>
	<td data-value="54.67" class="pct medium">54.67%</td>
	<td data-value="75" class="abs medium">41/75</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="50" class="abs medium">25/50</td>
	<td data-value="72.73" class="pct medium">72.73%</td>
	<td data-value="11" class="abs medium">8/11</td>
	<td data-value="53.42" class="pct medium">53.42%</td>
	<td data-value="73" class="abs medium">39/73</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../../sorter.js"></script>
</body>
</html>
