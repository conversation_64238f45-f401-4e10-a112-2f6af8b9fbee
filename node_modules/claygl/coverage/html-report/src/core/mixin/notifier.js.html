<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/core/mixin/notifier.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../../index.html">all files</a> / <a href="index.html">src/core/mixin/</a> notifier.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">54.67% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>41/75</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">50% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>25/50</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">72.73% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>8/11</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">53.42% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>39/73</span>
      </div>
    </div>
  </div>
  <div class='status-line medium'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">14×</span>
<span class="cline-any cline-yes">14×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">551×</span>
<span class="cline-any cline-yes">412×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">139×</span>
<span class="cline-any cline-yes">48×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">91×</span>
<span class="cline-any cline-yes">91×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">91×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">43×</span>
<span class="cline-any cline-yes">43×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">43×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">48×</span>
<span class="cline-any cline-yes">48×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">48×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">14×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">14×</span>
<span class="cline-any cline-yes">14×</span>
<span class="cline-any cline-yes">14×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">14×</span>
<span class="cline-any cline-yes">14×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">14×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">function Handler(action, context) {
    this.action = action;
    this.context = context;
}
/**
 * @mixin
 * @alias clay.core.mixin.notifier
 */
var notifier = {
    /**
     * Trigger event
     * @param  {string} name
     */
    trigger: function(name) {
        if (!this.hasOwnProperty('__handlers__')) {
            return;
        }
        if (!this.__handlers__.hasOwnProperty(name)) {
            return;
        }
&nbsp;
        var hdls = this.__handlers__[name];
        var l = hdls.length, i = -1, args = arguments;
        // Optimize advise from backbone
        switch (args.length) {
<span class="branch-0 cbranch-no" title="branch not covered" >            case 1:</span>
<span class="cstat-no" title="statement not covered" >                while (++i &lt; l) {</span>
<span class="cstat-no" title="statement not covered" >                    hdls[i].action.call(hdls[i].context);</span>
                }
<span class="cstat-no" title="statement not covered" >                return;</span>
            case 2:
                while (++i &lt; l) {
                    hdls[i].action.call(hdls[i].context, args[1]);
                }
                return;
<span class="branch-2 cbranch-no" title="branch not covered" >            case 3:</span>
<span class="cstat-no" title="statement not covered" >                while (++i &lt; l) {</span>
<span class="cstat-no" title="statement not covered" >                    hdls[i].action.call(hdls[i].context, args[1], args[2]);</span>
                }
<span class="cstat-no" title="statement not covered" >                return;</span>
<span class="branch-3 cbranch-no" title="branch not covered" >            case 4:</span>
<span class="cstat-no" title="statement not covered" >                while (++i &lt; l) {</span>
<span class="cstat-no" title="statement not covered" >                    hdls[i].action.call(hdls[i].context, args[1], args[2], args[3]);</span>
                }
<span class="cstat-no" title="statement not covered" >                return;</span>
            case 5:
                while (++i &lt; l) {
                    hdls[i].action.call(hdls[i].context, args[1], args[2], args[3], args[4]);
                }
                return;
<span class="branch-5 cbranch-no" title="branch not covered" >            default:</span>
<span class="cstat-no" title="statement not covered" >                while (++i &lt; l) {</span>
<span class="cstat-no" title="statement not covered" >                    hdls[i].action.apply(hdls[i].context, Array.prototype.slice.call(args, 1));</span>
                }
<span class="cstat-no" title="statement not covered" >                return;</span>
        }
    },
    /**
     * Register event handler
     * @param  {string} name
     * @param  {Function} action
     * @param  {Object} [context]
     * @chainable
     */
    on: function(name, action, context) {
        <span class="missing-if-branch" title="if path not taken" >I</span>if (!name || !action) {
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
        var handlers = this.__handlers__ || (this.__handlers__={});
        <span class="missing-if-branch" title="else path not taken" >E</span>if (!handlers[name]) {
            handlers[name] = [];
        }
        else {
<span class="cstat-no" title="statement not covered" >            if (this.has(name, action)) {</span>
<span class="cstat-no" title="statement not covered" >                return;</span>
            }
        }
        var handler = new Handler(action, context || this);
        handlers[name].push(handler);
&nbsp;
        return this;
    },
&nbsp;
    /**
     * Register event, event will only be triggered once and then removed
     * @param  {string} name
     * @param  {Function} action
     * @param  {Object} [context]
     * @chainable
     */
    once: function(name, action, context) {
        <span class="missing-if-branch" title="if path not taken" >I</span>if (!name || !action) {
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
        var self = this;
        function wrapper() {
            self.off(name, wrapper);
            action.apply(this, arguments);
        }
        return this.on(name, wrapper, context);
    },
&nbsp;
    /**
     * Alias of once('before' + name)
     * @param  {string} name
     * @param  {Function} action
     * @param  {Object} [context]
     * @chainable
     */
    before: function(name, action, context) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        if (!name || !action) {</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
<span class="cstat-no" title="statement not covered" >        name = 'before' + name;</span>
<span class="cstat-no" title="statement not covered" >        return this.on(name, action, context);</span>
    },
&nbsp;
    /**
     * Alias of once('after' + name)
     * @param  {string} name
     * @param  {Function} action
     * @param  {Object} [context]
     * @chainable
     */
    after: function(name, action, context) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        if (!name || !action) {</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
<span class="cstat-no" title="statement not covered" >        name = 'after' + name;</span>
<span class="cstat-no" title="statement not covered" >        return this.on(name, action, context);</span>
    },
&nbsp;
    /**
     * Alias of on('success')
     * @param  {Function} action
     * @param  {Object} [context]
     * @chainable
     */
    success: function(action, context) {
        return this.once('success', action, context);
    },
&nbsp;
    /**
     * Alias of on('error')
     * @param  {Function} action
     * @param  {Object} [context]
     * @chainable
     */
    error: function(action, context) {
        return this.once('error', action, context);
    },
&nbsp;
    /**
     * Remove event listener
     * @param  {Function} action
     * @param  {Object} [context]
     * @chainable
     */
    off: function(name, action) {
&nbsp;
        var handlers = this.__handlers__ || (this.__handlers__={});
&nbsp;
        <span class="missing-if-branch" title="if path not taken" >I</span>if (!action) {
<span class="cstat-no" title="statement not covered" >            handlers[name] = [];</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
        if (handlers[name]) {
            var hdls = handlers[name];
            var retains = [];
            for (var i = 0; i &lt; hdls.length; i++) {
                <span class="missing-if-branch" title="if path not taken" >I</span>if (action &amp;&amp; hdls[i].action !== action) {
<span class="cstat-no" title="statement not covered" >                    retains.push(hdls[i]);</span>
                }
            }
            handlers[name] = retains;
        }
&nbsp;
        return this;
    },
&nbsp;
    /**
     * If registered the event handler
     * @param  {string}  name
     * @param  {Function}  action
     * @return {boolean}
     */
    has: function(name, action) <span class="fstat-no" title="function not covered" >{</span>
        var handlers = <span class="cstat-no" title="statement not covered" >this.__handlers__;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (! handlers ||</span>
            ! handlers[name]) {
<span class="cstat-no" title="statement not covered" >            return false;</span>
        }
        var hdls = <span class="cstat-no" title="statement not covered" >handlers[name];</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; hdls.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >            if (hdls[i].action === action) {</span>
<span class="cstat-no" title="statement not covered" >                return true;</span>
            }
        }
    }
};
&nbsp;
export default notifier;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../../sorter.js"></script>
</body>
</html>
