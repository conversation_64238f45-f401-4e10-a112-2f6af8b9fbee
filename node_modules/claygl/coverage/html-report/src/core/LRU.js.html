<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/core/LRU.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/core/</a> LRU.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">67.74% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>21/31</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">50% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>7/14</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">50% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>3/6</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">67.74% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>21/31</span>
      </div>
    </div>
  </div>
  <div class='status-line medium'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">30×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">30×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">30×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">28×</span>
<span class="cline-any cline-yes">28×</span>
<span class="cline-any cline-yes">28×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">28×</span>
<span class="cline-any cline-yes">28×</span>
<span class="cline-any cline-yes">28×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">64×</span>
<span class="cline-any cline-yes">64×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">36×</span>
<span class="cline-any cline-yes">36×</span>
<span class="cline-any cline-yes">36×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">36×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import LinkedList from './LinkedList';
&nbsp;
/**
 * LRU Cache
 * @constructor
 * @alias clay.core.LRU
 */
var LRU = function(maxSize) {
&nbsp;
    this._list = new LinkedList();
&nbsp;
    this._map = {};
&nbsp;
    this._maxSize = maxSize || <span class="branch-1 cbranch-no" title="branch not covered" >10;</span>
};
&nbsp;
/**
 * Set cache max size
 * @param {number} size
 */
LRU.prototype.setMaxSize = function(size) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    this._maxSize = size;</span>
};
&nbsp;
/**
 * @param  {string} key
 * @param  {} value
 */
LRU.prototype.put = function(key, value) {
    <span class="missing-if-branch" title="else path not taken" >E</span>if (typeof(this._map[key]) == 'undefined') {
        var len = this._list.length();
        <span class="missing-if-branch" title="if path not taken" >I</span>if (len &gt;= this._maxSize &amp;&amp; <span class="branch-1 cbranch-no" title="branch not covered" >len &gt; 0)</span> {
            // Remove the least recently used
            var leastUsedEntry = <span class="cstat-no" title="statement not covered" >this._list.head;</span>
<span class="cstat-no" title="statement not covered" >            this._list.remove(leastUsedEntry);</span>
<span class="cstat-no" title="statement not covered" >            delete this._map[leastUsedEntry.key];</span>
        }
&nbsp;
        var entry = this._list.insert(value);
        entry.key = key;
        this._map[key] = entry;
    }
};
&nbsp;
/**
 * @param  {string} key
 * @return {}
 */
LRU.prototype.get = function(key) {
    var entry = this._map[key];
    if (typeof(entry) != 'undefined') {
        // Put the latest used entry in the tail
        <span class="missing-if-branch" title="else path not taken" >E</span>if (entry !== this._list.tail) {
            this._list.remove(entry);
            this._list.insertEntry(entry);
        }
&nbsp;
        return entry.value;
    }
};
&nbsp;
/**
 * @param {string} key
 */
LRU.prototype.remove = function(key) <span class="fstat-no" title="function not covered" >{</span>
    var entry = <span class="cstat-no" title="statement not covered" >this._map[key];</span>
<span class="cstat-no" title="statement not covered" >    if (typeof(entry) != 'undefined') {</span>
<span class="cstat-no" title="statement not covered" >        delete this._map[key];</span>
<span class="cstat-no" title="statement not covered" >        this._list.remove(entry);</span>
    }
};
&nbsp;
/**
 * Clear the cache
 */
LRU.prototype.clear = function() <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    this._list.clear();</span>
<span class="cstat-no" title="statement not covered" >    this._map = {};</span>
};
&nbsp;
export default LRU;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
