<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/application.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../index.html">all files</a> / <a href="index.html">src/</a> application.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">5.46% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>26/476</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/333</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/73</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">5.47% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>26/475</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
350
351
352
353
354
355
356
357
358
359
360
361
362
363
364
365
366
367
368
369
370
371
372
373
374
375
376
377
378
379
380
381
382
383
384
385
386
387
388
389
390
391
392
393
394
395
396
397
398
399
400
401
402
403
404
405
406
407
408
409
410
411
412
413
414
415
416
417
418
419
420
421
422
423
424
425
426
427
428
429
430
431
432
433
434
435
436
437
438
439
440
441
442
443
444
445
446
447
448
449
450
451
452
453
454
455
456
457
458
459
460
461
462
463
464
465
466
467
468
469
470
471
472
473
474
475
476
477
478
479
480
481
482
483
484
485
486
487
488
489
490
491
492
493
494
495
496
497
498
499
500
501
502
503
504
505
506
507
508
509
510
511
512
513
514
515
516
517
518
519
520
521
522
523
524
525
526
527
528
529
530
531
532
533
534
535
536
537
538
539
540
541
542
543
544
545
546
547
548
549
550
551
552
553
554
555
556
557
558
559
560
561
562
563
564
565
566
567
568
569
570
571
572
573
574
575
576
577
578
579
580
581
582
583
584
585
586
587
588
589
590
591
592
593
594
595
596
597
598
599
600
601
602
603
604
605
606
607
608
609
610
611
612
613
614
615
616
617
618
619
620
621
622
623
624
625
626
627
628
629
630
631
632
633
634
635
636
637
638
639
640
641
642
643
644
645
646
647
648
649
650
651
652
653
654
655
656
657
658
659
660
661
662
663
664
665
666
667
668
669
670
671
672
673
674
675
676
677
678
679
680
681
682
683
684
685
686
687
688
689
690
691
692
693
694
695
696
697
698
699
700
701
702
703
704
705
706
707
708
709
710
711
712
713
714
715
716
717
718
719
720
721
722
723
724
725
726
727
728
729
730
731
732
733
734
735
736
737
738
739
740
741
742
743
744
745
746
747
748
749
750
751
752
753
754
755
756
757
758
759
760
761
762
763
764
765
766
767
768
769
770
771
772
773
774
775
776
777
778
779
780
781
782
783
784
785
786
787
788
789
790
791
792
793
794
795
796
797
798
799
800
801
802
803
804
805
806
807
808
809
810
811
812
813
814
815
816
817
818
819
820
821
822
823
824
825
826
827
828
829
830
831
832
833
834
835
836
837
838
839
840
841
842
843
844
845
846
847
848
849
850
851
852
853
854
855
856
857
858
859
860
861
862
863
864
865
866
867
868
869
870
871
872
873
874
875
876
877
878
879
880
881
882
883
884
885
886
887
888
889
890
891
892
893
894
895
896
897
898
899
900
901
902
903
904
905
906
907
908
909
910
911
912
913
914
915
916
917
918
919
920
921
922
923
924
925
926
927
928
929
930
931
932
933
934
935
936
937
938
939
940
941
942
943
944
945
946
947
948
949
950
951
952
953
954
955
956
957
958
959
960
961
962
963
964
965
966
967
968
969
970
971
972
973
974
975
976
977
978
979
980
981
982
983
984
985
986
987
988
989
990
991
992
993
994
995
996
997
998
999
1000
1001
1002
1003
1004
1005
1006
1007
1008
1009
1010
1011
1012
1013
1014
1015
1016
1017
1018
1019
1020
1021
1022
1023
1024
1025
1026
1027
1028
1029
1030
1031
1032
1033
1034
1035
1036
1037
1038
1039
1040
1041
1042
1043
1044
1045
1046
1047
1048
1049
1050
1051
1052
1053
1054
1055
1056
1057
1058
1059
1060
1061
1062
1063
1064
1065
1066
1067
1068
1069
1070
1071
1072
1073
1074
1075
1076
1077
1078
1079
1080
1081
1082
1083
1084
1085
1086
1087
1088
1089
1090
1091
1092
1093
1094
1095
1096
1097
1098
1099
1100
1101
1102
1103
1104
1105
1106
1107
1108
1109
1110
1111
1112
1113
1114
1115
1116
1117
1118
1119
1120
1121
1122
1123
1124
1125
1126
1127
1128
1129
1130
1131
1132
1133
1134
1135
1136
1137
1138
1139
1140
1141
1142
1143
1144
1145
1146
1147
1148
1149
1150
1151
1152
1153
1154
1155
1156
1157
1158
1159
1160
1161
1162
1163
1164
1165
1166
1167
1168
1169
1170
1171
1172
1173
1174
1175
1176
1177
1178
1179
1180
1181
1182
1183
1184
1185
1186
1187
1188
1189
1190
1191
1192
1193
1194
1195
1196
1197
1198
1199
1200
1201
1202
1203
1204
1205
1206
1207
1208
1209
1210
1211
1212
1213
1214
1215
1216
1217
1218
1219
1220
1221
1222
1223
1224
1225
1226
1227
1228
1229
1230
1231
1232
1233
1234
1235
1236
1237
1238
1239
1240
1241
1242
1243
1244
1245
1246
1247
1248
1249
1250
1251
1252
1253
1254
1255
1256
1257
1258
1259
1260
1261
1262
1263
1264
1265
1266
1267
1268
1269
1270
1271
1272
1273
1274
1275
1276
1277
1278</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">&nbsp;
/**
 * Helpers for creating a common 3d application.
 * @namespace clay.application
 */
&nbsp;
 // TODO createCompositor
 // TODO Dispose test. geoCache test.
 // TODO Tonemapping exposure
 // TODO fitModel.
 // TODO Particle ?
import Renderer from './Renderer';
import Scene from './Scene';
import Timeline from './Timeline';
import CubeGeo from './geometry/Cube';
import SphereGeo from './geometry/Sphere';
import PlaneGeo from './geometry/Plane';
import ParametricSurfaceGeo from './geometry/ParametricSurface';
import Texture2D from './Texture2D';
import TextureCube from './TextureCube';
import Texture from './Texture';
import shaderLibrary from './shader/library';
import Mesh from './Mesh';
import Material from './Material';
import PerspectiveCamera from './camera/Perspective';
import OrthographicCamera from './camera/Orthographic';
import Vector3 from './math/Vector3';
import GLTFLoader from './loader/GLTF';
import Node from './Node';
import DirectionalLight from './light/Directional';
import PointLight from './light/Point';
import SpotLight from './light/Spot';
import AmbientLight from './light/Ambient';
import AmbientCubemapLight from './light/AmbientCubemap';
import AmbientSHLight from './light/AmbientSH';
import ShadowMapPass from './prePass/ShadowMap';
import RayPicking from './picking/RayPicking';
import LRUCache from './core/LRU';
import util from './core/util';
import shUtil from './util/sh';
import textureUtil from './util/texture';
&nbsp;
import colorUtil from './core/color';
var parseColor = colorUtil.parseToFloat;
&nbsp;
import './shader/builtin';
import Shader from './Shader';
&nbsp;
var EVE_NAMES = ['click', 'dblclick', 'mouseover', 'mouseout', 'mousemove',
    'touchstart', 'touchend', 'touchmove',
    'mousewheel', 'DOMMouseScroll'
];
&nbsp;
/**
 * @typedef {string|HTMLCanvasElement|HTMLImageElement|HTMLVideoElement} ImageLike
 */
/**
 * @typedef {string|Array.&lt;number&gt;} Color
 */
/**
 * @typedef {HTMLElement|string} DomQuery
 */
&nbsp;
/**
 * @typedef {Object} App3DNamespace
 * @property {Function} init Initialization callback that will be called when initing app.
 *                      You can return a promise in init to start the loop asynchronously when the promise is resolved.
 * @property {Function} loop Loop callback that will be called each frame.
 * @property {Function} beforeRender
 * @property {Function} afterRender
 * @property {number} [width] Container width.
 * @property {number} [height] Container height.
 * @property {number} [devicePixelRatio]
 * @property {Object} [graphic] Graphic configuration including shadow, color space.
 * @property {boolean} [graphic.shadow=false] If enable shadow
 * @property {boolean} [graphic.linear=false] If use linear color space
 * @property {boolean} [graphic.tonemapping=false] If enable ACES tone mapping.
 * @property {boolean} [event=false] If enable mouse/touch event. It will slow down the system if geometries are complex.
 */
&nbsp;
/**
 * Using App3D is a much more convenient way to create and manage your 3D application.
 *
 * It provides the abilities to:
 *
 * + Manage application loop and rendering.
 * + Collect GPU resource automatically without memory leak concern.
 * + Mouse event management.
 * + Create scene objects, materials, textures with simpler code.
 * + Load models with one line of code.
 * + Promised interfaces.
 *
 * Here is a basic example to create a rotating cube.
 *
```js
var app = clay.application.create('#viewport', {
    init: function (app) {
        // Create a perspective camera.
        // First parameter is the camera position. Which is in front of the cube.
        // Second parameter is the camera lookAt target. Which is the origin of the world, and where the cube puts.
        this._camera = app.createCamera([0, 2, 5], [0, 0, 0]);
        // Create a sample cube
        this._cube = app.createCube();
        // Create a directional light. The direction is from top right to left bottom, away from camera.
        this._mainLight = app.createDirectionalLight([-1, -1, -1]);
    },
    loop: function (app) {
        // Simply rotating the cube every frame.
        this._cube.rotation.rotateY(app.frameTime / 1000);
    }
});
```
 * @constructor
 * @alias clay.application.App3D
 * @param {DomQuery} dom Container dom element or a selector string that can be used in `querySelector`
 * @param {App3DNamespace} appNS Options and namespace used in creating app3D
 */
function App3D(dom, appNS) <span class="fstat-no" title="function not covered" >{</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    appNS = appNS || {};</span>
<span class="cstat-no" title="statement not covered" >    appNS.graphic = appNS.graphic || {};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (typeof dom === 'string') {</span>
<span class="cstat-no" title="statement not covered" >        dom = document.querySelector(dom);</span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (!dom) { <span class="cstat-no" title="statement not covered" >throw new Error('Invalid dom'); </span>}</span>
&nbsp;
    var isDomCanvas = <span class="cstat-no" title="statement not covered" >dom.nodeName.toUpperCase() === 'CANVAS';</span>
    var rendererOpts = <span class="cstat-no" title="statement not covered" >{};</span>
<span class="cstat-no" title="statement not covered" >    isDomCanvas &amp;&amp; (rendererOpts.canvas = dom);</span>
<span class="cstat-no" title="statement not covered" >    appNS.devicePixelRatio &amp;&amp; (rendererOpts.devicePixelRatio = appNS.devicePixelRatio);</span>
&nbsp;
    var gRenderer = <span class="cstat-no" title="statement not covered" >new Renderer(rendererOpts);</span>
    var gWidth = <span class="cstat-no" title="statement not covered" >appNS.width || dom.clientWidth;</span>
    var gHeight = <span class="cstat-no" title="statement not covered" >appNS.height || dom.clientHeight;</span>
&nbsp;
    var gScene = <span class="cstat-no" title="statement not covered" >new Scene();</span>
    var gTimeline = <span class="cstat-no" title="statement not covered" >new Timeline();</span>
    var gShadowPass = <span class="cstat-no" title="statement not covered" >appNS.graphic.shadow &amp;&amp; new ShadowMapPass();</span>
    var gRayPicking = <span class="cstat-no" title="statement not covered" >appNS.event &amp;&amp; new RayPicking({</span>
        scene: gScene,
        renderer: gRenderer
    });
&nbsp;
<span class="cstat-no" title="statement not covered" >    !isDomCanvas &amp;&amp; dom.appendChild(gRenderer.canvas);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    gRenderer.resize(gWidth, gHeight);</span>
&nbsp;
    var gFrameTime = <span class="cstat-no" title="statement not covered" >0;</span>
    var gElapsedTime = <span class="cstat-no" title="statement not covered" >0;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    gTimeline.start();</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    Object.defineProperties(this, {</span>
        /**
         * Container dom element
         * @name clay.application.App3D#container
         * @type {HTMLElement}
         */
        container: { get: function () <span class="fstat-no" title="function not covered" >{ <span class="cstat-no" title="statement not covered" >return dom; </span>} </span>},
        /**
         * @name clay.application.App3D#renderer
         * @type {clay.Renderer}
         */
        renderer: { get: function () <span class="fstat-no" title="function not covered" >{ <span class="cstat-no" title="statement not covered" >return gRenderer; </span>}}</span>,
        /**
         * @name clay.application.App3D#scene
         * @type {clay.Renderer}
         */
        scene: { get: function () <span class="fstat-no" title="function not covered" >{ <span class="cstat-no" title="statement not covered" >return gScene; </span>}}</span>,
        /**
         * @name clay.application.App3D#timeline
         * @type {clay.Renderer}
         */
        timeline: { get: function () <span class="fstat-no" title="function not covered" >{ <span class="cstat-no" title="statement not covered" >return gTimeline; </span>}}</span>,
        /**
         * Time elapsed since last frame. Can be used in loop to calculate the movement.
         * @name clay.application.App3D#frameTime
         * @type {number}
         */
        frameTime: { get: function () <span class="fstat-no" title="function not covered" >{ <span class="cstat-no" title="statement not covered" >return gFrameTime; </span>}}</span>,
        /**
         * Time elapsed since application created.
         * @name clay.application.App3D#elapsedTime
         * @type {number}
         */
        elapsedTime: { get: function () <span class="fstat-no" title="function not covered" >{ <span class="cstat-no" title="statement not covered" >return gElapsedTime; </span>}}</span>
    });
&nbsp;
    /**
     * Resize the application. Will use the container clientWidth/clientHeight if width/height in parameters are not given.
     * @function
     * @memberOf {clay.application.App3D}
     * @param {number} [width]
     * @param {number} [height]
     */
<span class="cstat-no" title="statement not covered" >    this.resize = function (width, height) <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >        gWidth = width || appNS.width || dom.clientWidth;</span>
<span class="cstat-no" title="statement not covered" >        gHeight = height || dom.height || dom.clientHeight;</span>
<span class="cstat-no" title="statement not covered" >        gRenderer.resize(gWidth, gHeight);</span>
    };
&nbsp;
    /**
     * Dispose the application
     * @function
     */
<span class="cstat-no" title="statement not covered" >    this.dispose = function () <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >        this._disposed = true;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (appNS.dispose) {</span>
<span class="cstat-no" title="statement not covered" >            appNS.dispose(this);</span>
        }
<span class="cstat-no" title="statement not covered" >        gTimeline.stop();</span>
<span class="cstat-no" title="statement not covered" >        gRenderer.disposeScene(gScene);</span>
<span class="cstat-no" title="statement not covered" >        gShadowPass &amp;&amp; gShadowPass.dispose(gRenderer);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        dom.innerHTML = '';</span>
<span class="cstat-no" title="statement not covered" >        EVE_NAMES.forEach(function (eveType) <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >            this[makeHandlerName(eveType)] &amp;&amp; dom.removeEventListener(makeHandlerName(eveType));</span>
        });
    };
&nbsp;
<span class="cstat-no" title="statement not covered" >    gRayPicking &amp;&amp; this._initMouseEvents(gRayPicking);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this._geoCache = new LRUCache(20);</span>
<span class="cstat-no" title="statement not covered" >    this._texCache = new LRUCache(20);</span>
&nbsp;
    // Do init the application.
    var initPromise = <span class="cstat-no" title="statement not covered" >Promise.resolve(appNS.init &amp;&amp; appNS.init(this));</span>
    // Use the inited camera.
<span class="cstat-no" title="statement not covered" >    gRayPicking &amp;&amp; (gRayPicking.camera = gScene.getMainCamera());</span>
&nbsp;
    var gTexturesList = <span class="cstat-no" title="statement not covered" >{};</span>
    var gGeometriesList = <span class="cstat-no" title="statement not covered" >{};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (!appNS.loop) {</span>
<span class="cstat-no" title="statement not covered" >        console.warn('Miss loop method.');</span>
    }
&nbsp;
    var self = <span class="cstat-no" title="statement not covered" >this;</span>
<span class="cstat-no" title="statement not covered" >    initPromise.then(function () <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >        appNS.loop &amp;&amp; gTimeline.on('frame', function (frameTime) <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >            gFrameTime = frameTime;</span>
<span class="cstat-no" title="statement not covered" >            gElapsedTime += frameTime;</span>
<span class="cstat-no" title="statement not covered" >            appNS.loop(self);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            gScene.update();</span>
            var skyboxList = <span class="cstat-no" title="statement not covered" >[];</span>
<span class="cstat-no" title="statement not covered" >            gScene.skybox &amp;&amp; skyboxList.push(gScene.skybox);</span>
<span class="cstat-no" title="statement not covered" >            gScene.skydome &amp;&amp; skyboxList.push(gScene.skydome);</span>
<span class="cstat-no" title="statement not covered" >            self._updateGraphicOptions(appNS.graphic, skyboxList, true);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            gRayPicking &amp;&amp; (gRayPicking.camera = gScene.getMainCamera());</span>
            // Render shadow pass
<span class="cstat-no" title="statement not covered" >            gShadowPass &amp;&amp; gShadowPass.render(gRenderer, gScene, null, true);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            appNS.beforeRender &amp;&amp; appNS.beforeRender(self);</span>
<span class="cstat-no" title="statement not covered" >            self._doRender(gRenderer, gScene, true);</span>
<span class="cstat-no" title="statement not covered" >            appNS.afterRender &amp;&amp; appNS.afterRender(self);</span>
&nbsp;
            // Mark all resources unused;
<span class="cstat-no" title="statement not covered" >            markUnused(gTexturesList);</span>
<span class="cstat-no" title="statement not covered" >            markUnused(gGeometriesList);</span>
&nbsp;
            // Collect resources used in this frame.
            var newTexturesList = <span class="cstat-no" title="statement not covered" >[];</span>
            var newGeometriesList = <span class="cstat-no" title="statement not covered" >[];</span>
<span class="cstat-no" title="statement not covered" >            collectResources(gScene, newTexturesList, newGeometriesList);</span>
&nbsp;
            // Dispose those unsed resources.
<span class="cstat-no" title="statement not covered" >            checkAndDispose(gRenderer, gTexturesList);</span>
<span class="cstat-no" title="statement not covered" >            checkAndDispose(gRenderer, gGeometriesList);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            gTexturesList = newTexturesList;</span>
<span class="cstat-no" title="statement not covered" >            gGeometriesList = newGeometriesList;</span>
        });
    });
&nbsp;
<span class="cstat-no" title="statement not covered" >    gScene.on('beforerender', function (renderer, scene, camera, renderList) <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >        this._updateGraphicOptions(appNS.graphic, renderList.opaque, false);</span>
<span class="cstat-no" title="statement not covered" >        this._updateGraphicOptions(appNS.graphic, renderList.transparent, false);</span>
    }, this);
}
&nbsp;
function isImageLikeElement(val) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return val instanceof Image</span>
        || val instanceof HTMLCanvasElement
        || val instanceof HTMLVideoElement;
}
&nbsp;
function getKeyFromImageLike(val) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    typeof val === 'string'</span>
        ? val : (val.__key__ || (val.__key__ = util.genGUID()));
}
&nbsp;
function makeHandlerName(eveType) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return '_' + eveType + 'Handler';</span>
}
&nbsp;
function packageEvent(eventType, pickResult, offsetX, offsetY, wheelDelta) <span class="fstat-no" title="function not covered" >{</span>
    var event = <span class="cstat-no" title="statement not covered" >util.clone(pickResult);</span>
<span class="cstat-no" title="statement not covered" >    event.type = eventType;</span>
<span class="cstat-no" title="statement not covered" >    event.offsetX = offsetX;</span>
<span class="cstat-no" title="statement not covered" >    event.offsetY = offsetY;</span>
<span class="cstat-no" title="statement not covered" >    if (wheelDelta !== null) {</span>
<span class="cstat-no" title="statement not covered" >        event.wheelDelta = wheelDelta;</span>
    }
<span class="cstat-no" title="statement not covered" >    return event;</span>
}
&nbsp;
function bubblingEvent(target, event) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    while (target &amp;&amp; !event.cancelBubble) {</span>
<span class="cstat-no" title="statement not covered" >        target.trigger(event.type, event);</span>
<span class="cstat-no" title="statement not covered" >        target = target.getParent();</span>
    }
}
&nbsp;
App3D.prototype._initMouseEvents = function (rayPicking) <span class="fstat-no" title="function not covered" >{</span>
    var dom = <span class="cstat-no" title="statement not covered" >this.container;</span>
&nbsp;
    var oldTarget = <span class="cstat-no" title="statement not covered" >null;</span>
<span class="cstat-no" title="statement not covered" >    EVE_NAMES.forEach(function (_eveType) <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >        dom.addEventListener(_eveType, this[makeHandlerName(_eveType)] = function (e) <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >            if (!rayPicking.camera) { // Not have camera yet.</span>
<span class="cstat-no" title="statement not covered" >                return;</span>
            }
<span class="cstat-no" title="statement not covered" >            e.preventDefault();</span>
&nbsp;
            var box = <span class="cstat-no" title="statement not covered" >dom.getBoundingClientRect();</span>
            var offsetX, offsetY;
            var eveType = <span class="cstat-no" title="statement not covered" >_eveType;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (eveType.indexOf('touch') &gt;= 0) {</span>
                var touch = <span class="cstat-no" title="statement not covered" >eveType !== 'touchend'</span>
                    ? e.targetTouches[0]
                    : e.changedTouches[0];
<span class="cstat-no" title="statement not covered" >                if (eveType === 'touchstart') {</span>
<span class="cstat-no" title="statement not covered" >                    eveType = 'mousedown';</span>
                }
                else <span class="cstat-no" title="statement not covered" >if (eveType === 'touchend') {</span>
<span class="cstat-no" title="statement not covered" >                    eveType = 'mouseup';</span>
                }
                else <span class="cstat-no" title="statement not covered" >if (eveType === 'touchmove') {</span>
<span class="cstat-no" title="statement not covered" >                    eveType = 'mousemove';</span>
                }
<span class="cstat-no" title="statement not covered" >                offsetX = touch.clientX - box.left;</span>
<span class="cstat-no" title="statement not covered" >                offsetY = touch.clientY - box.top;</span>
            }
            else {
<span class="cstat-no" title="statement not covered" >                offsetX = e.clientX - box.left;</span>
<span class="cstat-no" title="statement not covered" >                offsetY = e.clientY - box.top;</span>
            }
&nbsp;
            var pickResult = <span class="cstat-no" title="statement not covered" >rayPicking.pick(offsetX, offsetY);</span>
&nbsp;
            var delta;
<span class="cstat-no" title="statement not covered" >            if (eveType === 'DOMMouseScroll' || eveType === 'mousewheel') {</span>
<span class="cstat-no" title="statement not covered" >                delta = (e.wheelDelta) ? e.wheelDelta / 120 : -(e.detail || 0) / 3;</span>
            }
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (pickResult) {</span>
                // Just ignore silent element.
<span class="cstat-no" title="statement not covered" >                if (pickResult.target.silent) {</span>
<span class="cstat-no" title="statement not covered" >                    return;</span>
                }
&nbsp;
<span class="cstat-no" title="statement not covered" >                if (eveType === 'mousemove') {</span>
                    // PENDING touchdown should trigger mouseover event ?
                    var targetChanged = <span class="cstat-no" title="statement not covered" >pickResult.target !== oldTarget;</span>
<span class="cstat-no" title="statement not covered" >                    if (targetChanged) {</span>
<span class="cstat-no" title="statement not covered" >                        oldTarget &amp;&amp; bubblingEvent(oldTarget, packageEvent('mouseout', {</span>
                            target: oldTarget
                        }, offsetX, offsetY));
                    }
<span class="cstat-no" title="statement not covered" >                    bubblingEvent(pickResult.target, packageEvent('mousemove', pickResult, offsetX, offsetY));</span>
<span class="cstat-no" title="statement not covered" >                    if (targetChanged) {</span>
<span class="cstat-no" title="statement not covered" >                        bubblingEvent(pickResult.target, packageEvent('mouseover', pickResult, offsetX, offsetY));</span>
                    }
                }
                else {
<span class="cstat-no" title="statement not covered" >                    bubblingEvent(pickResult.target, packageEvent(eveType, pickResult, offsetX, offsetY, delta));</span>
                }
<span class="cstat-no" title="statement not covered" >                oldTarget = pickResult.target;</span>
            }
            else <span class="cstat-no" title="statement not covered" >if (oldTarget) {</span>
<span class="cstat-no" title="statement not covered" >                bubblingEvent(oldTarget, packageEvent('mouseout', {</span>
                    target: oldTarget
                }, offsetX, offsetY));
<span class="cstat-no" title="statement not covered" >                oldTarget = null;</span>
            }
        });
    }, this);
};
&nbsp;
App3D.prototype._updateGraphicOptions = function (graphicOpts, list, isSkybox) <span class="fstat-no" title="function not covered" >{</span>
    var enableTonemapping = <span class="cstat-no" title="statement not covered" >!!graphicOpts.tonemapping;</span>
    var isLinearSpace = <span class="cstat-no" title="statement not covered" >!!graphicOpts.linear;</span>
&nbsp;
    var prevMaterial;
&nbsp;
<span class="cstat-no" title="statement not covered" >    for (var i = 0; i &lt; list.length; i++) {</span>
        var mat = <span class="cstat-no" title="statement not covered" >list[i].material;</span>
<span class="cstat-no" title="statement not covered" >        if (mat === prevMaterial) {</span>
<span class="cstat-no" title="statement not covered" >            continue;</span>
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        enableTonemapping ? mat.define('fragment', 'TONEMAPPING') : mat.undefine('fragment', 'TONEMAPPING');</span>
<span class="cstat-no" title="statement not covered" >        if (isLinearSpace) {</span>
            var decodeSRGB = <span class="cstat-no" title="statement not covered" >true;</span>
<span class="cstat-no" title="statement not covered" >            if (isSkybox &amp;&amp; mat.get('environmentMap') &amp;&amp; !mat.get('environmentMap').sRGB) {</span>
<span class="cstat-no" title="statement not covered" >                decodeSRGB = false;</span>
            }
<span class="cstat-no" title="statement not covered" >            decodeSRGB &amp;&amp; mat.define('fragment', 'SRGB_DECODE');</span>
<span class="cstat-no" title="statement not covered" >            mat.define('fragment', 'SRGB_ENCODE');</span>
        }
        else {
<span class="cstat-no" title="statement not covered" >            mat.undefine('fragment', 'SRGB_DECODE');</span>
<span class="cstat-no" title="statement not covered" >            mat.undefine('fragment', 'SRGB_ENCODE');</span>
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        prevMaterial = mat;</span>
    }
};
&nbsp;
App3D.prototype._doRender = function (renderer, scene) <span class="fstat-no" title="function not covered" >{</span>
    var camera = <span class="cstat-no" title="statement not covered" >scene.getMainCamera();</span>
<span class="cstat-no" title="statement not covered" >    camera.aspect = renderer.getViewportAspect();</span>
<span class="cstat-no" title="statement not covered" >    renderer.render(scene, camera, true);</span>
};
&nbsp;
&nbsp;
function markUnused(resourceList) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    for (var i = 0; i &lt; resourceList.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >        resourceList[i].__used = 0;</span>
    }
}
&nbsp;
function checkAndDispose(renderer, resourceList) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    for (var i = 0; i &lt; resourceList.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >        if (!resourceList[i].__used) {</span>
<span class="cstat-no" title="statement not covered" >            resourceList[i].dispose(renderer);</span>
        }
    }
}
&nbsp;
function updateUsed(resource, list) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    resource.__used = resource.__used || 0;</span>
<span class="cstat-no" title="statement not covered" >    resource.__used++;</span>
<span class="cstat-no" title="statement not covered" >    if (resource.__used === 1) {</span>
        // Don't push to the list twice.
<span class="cstat-no" title="statement not covered" >        list.push(resource);</span>
    }
}
function collectResources(scene, textureResourceList, geometryResourceList) <span class="fstat-no" title="function not covered" >{</span>
    var prevMaterial;
    var prevGeometry;
<span class="cstat-no" title="statement not covered" >    scene.traverse(function (renderable) <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >        if (renderable.isRenderable()) {</span>
            var geometry = <span class="cstat-no" title="statement not covered" >renderable.geometry;</span>
            var material = <span class="cstat-no" title="statement not covered" >renderable.material;</span>
&nbsp;
            // TODO optimize!!
<span class="cstat-no" title="statement not covered" >            if (material !== prevMaterial) {</span>
                var textureUniforms = <span class="cstat-no" title="statement not covered" >material.getTextureUniforms();</span>
<span class="cstat-no" title="statement not covered" >                for (var u = 0; u &lt; textureUniforms.length; u++) {</span>
                    var uniformName = <span class="cstat-no" title="statement not covered" >textureUniforms[u];</span>
                    var val = <span class="cstat-no" title="statement not covered" >material.uniforms[uniformName].value;</span>
                    var uniformType = <span class="cstat-no" title="statement not covered" >material.uniforms[uniformName].type;</span>
<span class="cstat-no" title="statement not covered" >                    if (!val) {</span>
<span class="cstat-no" title="statement not covered" >                        continue;</span>
                    }
<span class="cstat-no" title="statement not covered" >                    if (uniformType === 't') {</span>
<span class="cstat-no" title="statement not covered" >                        updateUsed(val, textureResourceList);</span>
                    }
                    else <span class="cstat-no" title="statement not covered" >if (uniformType === 'tv') {</span>
<span class="cstat-no" title="statement not covered" >                        for (var k = 0; k &lt; val.length; k++) {</span>
<span class="cstat-no" title="statement not covered" >                            if (val[k]) {</span>
<span class="cstat-no" title="statement not covered" >                                updateUsed(val[k], textureResourceList);</span>
                            }
                        }
                    }
                }
            }
<span class="cstat-no" title="statement not covered" >            if (geometry !== prevGeometry) {</span>
<span class="cstat-no" title="statement not covered" >                updateUsed(geometry, geometryResourceList);</span>
            }
&nbsp;
<span class="cstat-no" title="statement not covered" >            prevMaterial = material;</span>
<span class="cstat-no" title="statement not covered" >            prevGeometry = geometry;</span>
        }
    });
&nbsp;
<span class="cstat-no" title="statement not covered" >    for (var k = 0; k &lt; scene.lights.length; k++) {</span>
        // Track AmbientCubemap
<span class="cstat-no" title="statement not covered" >        if (scene.lights[k].cubemap) {</span>
<span class="cstat-no" title="statement not covered" >            updateUsed(scene.lights[k].cubemap, textureResourceList);</span>
        }
    }
}
/**
 * Load a texture from image or string.
 * @param {ImageLike} img
 * @param {Object} [opts] Texture options.
 * @param {boolean} [opts.flipY=true] If flipY. See {@link clay.Texture.flipY}
 * @param {boolean} [opts.convertToPOT=false] Force convert None Power of Two texture to Power of two so it can be tiled.
 * @param {number} [opts.anisotropic] Anisotropic filtering. See {@link clay.Texture.anisotropic}
 * @param {number} [opts.wrapS=clay.Texture.REPEAT] See {@link clay.Texture.wrapS}
 * @param {number} [opts.wrapT=clay.Texture.REPEAT] See {@link clay.Texture.wrapT}
 * @param {number} [opts.minFilter=clay.Texture.LINEAR_MIPMAP_LINEAR] See {@link clay.Texture.minFilter}
 * @param {number} [opts.magFilter=clay.Texture.LINEAR] See {@link clay.Texture.magFilter}
 * @param {number} [opts.exposure] Only be used when source is a HDR image.
 * @param {boolean} [useCache] If use cache.
 * @return {Promise}
 * @example
 *  app.loadTexture('diffuseMap.jpg')
 *      .then(function (texture) {
 *          material.set('diffuseMap', texture);
 *      });
 */
App3D.prototype.loadTexture = function (urlOrImg, opts, useCache) <span class="fstat-no" title="function not covered" >{</span>
    var self = <span class="cstat-no" title="statement not covered" >this;</span>
    var key = <span class="cstat-no" title="statement not covered" >getKeyFromImageLike(urlOrImg);</span>
<span class="cstat-no" title="statement not covered" >    if (useCache) {</span>
<span class="cstat-no" title="statement not covered" >        if (this._texCache.get(key)) {</span>
<span class="cstat-no" title="statement not covered" >            return this._texCache.get(key);</span>
        }
    }
    // TODO Promise ?
    var promise = <span class="cstat-no" title="statement not covered" >new Promise(function (resolve, reject) <span class="fstat-no" title="function not covered" >{</span></span>
        var texture = <span class="cstat-no" title="statement not covered" >self.loadTextureSync(urlOrImg, opts);</span>
<span class="cstat-no" title="statement not covered" >        if (!texture.isRenderable()) {</span>
<span class="cstat-no" title="statement not covered" >            texture.success(function () <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >                if (self._disposed) {</span>
<span class="cstat-no" title="statement not covered" >                    return;</span>
                }
<span class="cstat-no" title="statement not covered" >                resolve(texture);</span>
            });
<span class="cstat-no" title="statement not covered" >            texture.error(function () <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >                if (self._disposed) {</span>
<span class="cstat-no" title="statement not covered" >                    return;</span>
                }
<span class="cstat-no" title="statement not covered" >                reject();</span>
            });
        }
        else {
<span class="cstat-no" title="statement not covered" >            resolve(texture);</span>
        }
    });
<span class="cstat-no" title="statement not covered" >    if (useCache) {</span>
<span class="cstat-no" title="statement not covered" >        this._texCache.put(key, promise);</span>
    }
<span class="cstat-no" title="statement not covered" >    return promise;</span>
};
&nbsp;
function nearestPowerOfTwo(val) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return Math.pow(2, Math.round(Math.log(val) / Math.LN2));</span>
}
function convertTextureToPowerOfTwo(texture) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    if ((texture.wrapS === Texture.REPEAT || texture.wrapT === Texture.REPEAT)</span>
     &amp;&amp; texture.image) {
        // var canvas = document.createElement('canvas');
        var width = <span class="cstat-no" title="statement not covered" >nearestPowerOfTwo(texture.width);</span>
        var height = <span class="cstat-no" title="statement not covered" >nearestPowerOfTwo(texture.height);</span>
<span class="cstat-no" title="statement not covered" >        if (width !== texture.width || height !== texture.height) {</span>
            var canvas = <span class="cstat-no" title="statement not covered" >document.createElement('canvas');</span>
<span class="cstat-no" title="statement not covered" >            canvas.width = width;</span>
<span class="cstat-no" title="statement not covered" >            canvas.height = height;</span>
            var ctx = <span class="cstat-no" title="statement not covered" >canvas.getContext('2d');</span>
<span class="cstat-no" title="statement not covered" >            ctx.drawImage(texture.image, 0, 0, width, height);</span>
<span class="cstat-no" title="statement not covered" >            canvas.srcImage = texture.image;</span>
<span class="cstat-no" title="statement not covered" >            texture.image = canvas;</span>
<span class="cstat-no" title="statement not covered" >            texture.dirty();</span>
        }
    }
}
&nbsp;
/**
 * Create a texture from image or string synchronously. Texture can be use directly and don't have to wait for it's loaded.
 * @param {ImageLike} img
 * @param {Object} [opts] Texture options.
 * @param {boolean} [opts.flipY=true] If flipY. See {@link clay.Texture.flipY}
 * @param {boolean} [opts.convertToPOT=false] Force convert None Power of Two texture to Power of two so it can be tiled.
 * @param {number} [opts.anisotropic] Anisotropic filtering. See {@link clay.Texture.anisotropic}
 * @param {number} [opts.wrapS=clay.Texture.REPEAT] See {@link clay.Texture.wrapS}
 * @param {number} [opts.wrapT=clay.Texture.REPEAT] See {@link clay.Texture.wrapT}
 * @param {number} [opts.minFilter=clay.Texture.LINEAR_MIPMAP_LINEAR] See {@link clay.Texture.minFilter}
 * @param {number} [opts.magFilter=clay.Texture.LINEAR] See {@link clay.Texture.magFilter}
 * @param {number} [opts.exposure] Only be used when source is a HDR image.
 * @return {clay.Texture2D}
 * @example
 *  var texture = app.loadTexture('diffuseMap.jpg', {
 *      anisotropic: 8,
 *      flipY: false
 *  });
 *  material.set('diffuseMap', texture);
 */
App3D.prototype.loadTextureSync = function (urlOrImg, opts) <span class="fstat-no" title="function not covered" >{</span>
    var texture = <span class="cstat-no" title="statement not covered" >new Texture2D(opts);</span>
<span class="cstat-no" title="statement not covered" >    if (typeof urlOrImg === 'string') {</span>
<span class="cstat-no" title="statement not covered" >        if (urlOrImg.match(/.hdr$|^data:application\/octet-stream/)) {</span>
<span class="cstat-no" title="statement not covered" >            texture = textureUtil.loadTexture(urlOrImg, {</span>
                exposure: opts &amp;&amp; opts.exposure,
                fileType: 'hdr'
            }, function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >                texture.dirty();</span>
<span class="cstat-no" title="statement not covered" >                texture.trigger('success');</span>
            });
<span class="cstat-no" title="statement not covered" >            for (var key in opts) {</span>
<span class="cstat-no" title="statement not covered" >                texture[key] = opts[key];</span>
            }
        }
        else {
<span class="cstat-no" title="statement not covered" >            texture.load(urlOrImg);</span>
<span class="cstat-no" title="statement not covered" >            if (opts &amp;&amp; opts.convertToPOT) {</span>
<span class="cstat-no" title="statement not covered" >                texture.success(function () <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >                    convertTextureToPowerOfTwo(texture);</span>
                });
            }
        }
    }
    else <span class="cstat-no" title="statement not covered" >if (isImageLikeElement(urlOrImg)) {</span>
<span class="cstat-no" title="statement not covered" >        texture.image = urlOrImg;</span>
<span class="cstat-no" title="statement not covered" >        texture.dynamic = urlOrImg instanceof HTMLVideoElement;</span>
    }
<span class="cstat-no" title="statement not covered" >    return texture;</span>
};
&nbsp;
/**
 * Create a texture from image or string synchronously. Texture can be use directly and don't have to wait for it's loaded.
 * @param {ImageLike} img
 * @param {Object} [opts] Texture options.
 * @param {boolean} [opts.flipY=false] If flipY. See {@link clay.Texture.flipY}
 * @return {Promise}
 * @example
 *  app.loadTextureCube({
 *      px: 'skybox/px.jpg', py: 'skybox/py.jpg', pz: 'skybox/pz.jpg',
 *      nx: 'skybox/nx.jpg', ny: 'skybox/ny.jpg', nz: 'skybox/nz.jpg'
 *  }).then(function (texture) {
 *      skybox.setEnvironmentMap(texture);
 *  })
 */
App3D.prototype.loadTextureCube = function (imgList, opts) <span class="fstat-no" title="function not covered" >{</span>
    var textureCube = <span class="cstat-no" title="statement not covered" >this.loadTextureCubeSync(imgList, opts);</span>
<span class="cstat-no" title="statement not covered" >    return new Promise(function (resolve, reject) <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >        if (textureCube.isRenderable()) {</span>
<span class="cstat-no" title="statement not covered" >            resolve(textureCube);</span>
        }
        else {
<span class="cstat-no" title="statement not covered" >            textureCube.success(function () <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >                resolve(textureCube);</span>
            }).error(function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >                reject();</span>
            });
        }
    });
};
&nbsp;
/**
 * Create a texture from image or string synchronously. Texture can be use directly and don't have to wait for it's loaded.
 * @param {ImageLike} img
 * @param {Object} [opts] Texture options.
 * @param {boolean} [opts.flipY=false] If flipY. See {@link clay.Texture.flipY}
 * @return {clay.TextureCube}
 * @example
 *  var texture = app.loadTextureCubeSync({
 *      px: 'skybox/px.jpg', py: 'skybox/py.jpg', pz: 'skybox/pz.jpg',
 *      nx: 'skybox/nx.jpg', ny: 'skybox/ny.jpg', nz: 'skybox/nz.jpg'
 *  });
 *  skybox.setEnvironmentMap(texture);
 */
App3D.prototype.loadTextureCubeSync = function (imgList, opts) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    opts = opts || {};</span>
<span class="cstat-no" title="statement not covered" >    opts.flipY = opts.flipY || false;</span>
    var textureCube = <span class="cstat-no" title="statement not covered" >new TextureCube(opts);</span>
<span class="cstat-no" title="statement not covered" >    if (!imgList || !imgList.px || !imgList.nx || !imgList.py || !imgList.ny || !imgList.pz || !imgList.nz) {</span>
<span class="cstat-no" title="statement not covered" >        throw new Error('Invalid cubemap format. Should be an object including px,nx,py,ny,pz,nz');</span>
    }
<span class="cstat-no" title="statement not covered" >    if (typeof imgList.px === 'string') {</span>
<span class="cstat-no" title="statement not covered" >        textureCube.load(imgList);</span>
    }
    else {
<span class="cstat-no" title="statement not covered" >        textureCube.image = util.clone(imgList);</span>
    }
<span class="cstat-no" title="statement not covered" >    return textureCube;</span>
};
&nbsp;
/**
 * Create a material.
 * @param {Object} materialConfig. materialConfig contains `shader`, `transparent` and uniforms that used in corresponding uniforms.
 *                                 Uniforms can be `color`, `alpha` `diffuseMap` etc.
 * @param {string|clay.Shader} [shader='clay.standardMR'] Default to be standard shader with metalness and roughness workflow.
 * @param {boolean} [transparent=false] If material is transparent.
 * @param {boolean} [convertTextureToPOT=false] Force convert None Power of Two texture to Power of two so it can be tiled.
 * @return {clay.Material}
 */
App3D.prototype.createMaterial = function (matConfig) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    matConfig = matConfig || {};</span>
<span class="cstat-no" title="statement not covered" >    matConfig.shader = matConfig.shader || 'clay.standardMR';</span>
    var shader = <span class="cstat-no" title="statement not covered" >matConfig.shader instanceof Shader ? matConfig.shader : shaderLibrary.get(matConfig.shader);</span>
    var material = <span class="cstat-no" title="statement not covered" >new Material({</span>
        shader: shader
    });
    function makeTextureSetter(key) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return function (texture) <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >            material.setUniform(key, texture);</span>
        };
    }
<span class="cstat-no" title="statement not covered" >    for (var key in matConfig) {</span>
<span class="cstat-no" title="statement not covered" >        if (material.uniforms[key]) {</span>
            var val = <span class="cstat-no" title="statement not covered" >matConfig[key];</span>
<span class="cstat-no" title="statement not covered" >            if ((material.uniforms[key].type === 't' || isImageLikeElement(val))</span>
                &amp;&amp; !(val instanceof Texture)
            ) {
                // Try to load a texture.
<span class="cstat-no" title="statement not covered" >                this.loadTexture(val, {</span>
                    convertToPOT: matConfig.convertTextureToPOT
                }).then(makeTextureSetter(key));
            }
            else {
<span class="cstat-no" title="statement not covered" >                material.setUniform(key, val);</span>
            }
        }
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (matConfig.transparent) {</span>
<span class="cstat-no" title="statement not covered" >        matConfig.depthMask = false;</span>
<span class="cstat-no" title="statement not covered" >        matConfig.transparent = true;</span>
    }
<span class="cstat-no" title="statement not covered" >    return material;</span>
};
&nbsp;
/**
 * Create a cube mesh and add it to the scene or the given parent node.
 * @function
 * @param {Object|clay.Material} [material]
 * @param {clay.Node} [parentNode] Parent node to append. Default to be scene.
 * @param {Array.&lt;number&gt;|number} [subdivision=1] Subdivision of cube.
 *          Can be a number to represent both width, height and depth dimensions. Or an array to represent them respectively.
 * @return {clay.Mesh}
 * @example
 *  // Create a white cube.
 *  app.createCube()
 */
App3D.prototype.createCube = function (material, parentNode, subdiv) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    if (subdiv == null) {</span>
<span class="cstat-no" title="statement not covered" >        subdiv = 1;</span>
    }
<span class="cstat-no" title="statement not covered" >    if (typeof subdiv === 'number') {</span>
<span class="cstat-no" title="statement not covered" >        subdiv = [subdiv, subdiv, subdiv];</span>
    }
&nbsp;
    var geoKey = <span class="cstat-no" title="statement not covered" >'cube-' + subdiv.join('-');</span>
    var cube = <span class="cstat-no" title="statement not covered" >this._geoCache.get(geoKey);</span>
<span class="cstat-no" title="statement not covered" >    if (!cube) {</span>
<span class="cstat-no" title="statement not covered" >        cube = new CubeGeo({</span>
            widthSegments: subdiv[0],
            heightSegments: subdiv[1],
            depthSegments: subdiv[2]
        });
<span class="cstat-no" title="statement not covered" >        cube.generateTangents();</span>
<span class="cstat-no" title="statement not covered" >        this._geoCache.put(geoKey, cube);</span>
    }
<span class="cstat-no" title="statement not covered" >    return this.createMesh(cube, material, parentNode);</span>
};
&nbsp;
/**
 * Create a cube mesh that camera is inside the cube.
 * @function
 * @param {Object|clay.Material} [material]
 * @param {clay.Node} [parentNode] Parent node to append. Default to be scene.
 * @param {Array.&lt;number&gt;|number} [subdivision=1] Subdivision of cube.
 *          Can be a number to represent both width, height and depth dimensions. Or an array to represent them respectively.
 * @return {clay.Mesh}
 * @example
 *  // Create a white cube inside.
 *  app.createCubeInside()
 */
App3D.prototype.createCubeInside = function (material, parentNode, subdiv) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    if (subdiv == null) {</span>
<span class="cstat-no" title="statement not covered" >        subdiv = 1;</span>
    }
<span class="cstat-no" title="statement not covered" >    if (typeof subdiv === 'number') {</span>
<span class="cstat-no" title="statement not covered" >        subdiv = [subdiv, subdiv, subdiv];</span>
    }
    var geoKey = <span class="cstat-no" title="statement not covered" >'cubeInside-' + subdiv.join('-');</span>
    var cube = <span class="cstat-no" title="statement not covered" >this._geoCache.get(geoKey);</span>
<span class="cstat-no" title="statement not covered" >    if (!cube) {</span>
<span class="cstat-no" title="statement not covered" >        cube = new CubeGeo({</span>
            inside: true,
            widthSegments: subdiv[0],
            heightSegments: subdiv[1],
            depthSegments: subdiv[2]
        });
<span class="cstat-no" title="statement not covered" >        cube.generateTangents();</span>
<span class="cstat-no" title="statement not covered" >        this._geoCache.put(geoKey, cube);</span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    return this.createMesh(cube, material, parentNode);</span>
};
&nbsp;
/**
 * Create a sphere mesh and add it to the scene or the given parent node.
 * @function
 * @param {Object|clay.Material} [material]
 * @param {clay.Node} [parentNode] Parent node to append. Default to be scene.
 * @param {number} [subdivision=20] Subdivision of sphere.
 * @return {clay.Mesh}
 * @example
 *  // Create a semi-transparent sphere.
 *  app.createSphere({
 *      color: [0, 0, 1],
 *      transparent: true,
 *      alpha: 0.5
 *  })
 */
App3D.prototype.createSphere = function (material, parentNode, subdivision) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    if (subdivision == null) {</span>
<span class="cstat-no" title="statement not covered" >        subdivision = 20;</span>
    }
    var geoKey = <span class="cstat-no" title="statement not covered" >'sphere-' + subdivision;</span>
    var sphere = <span class="cstat-no" title="statement not covered" >this._geoCache.get(geoKey);</span>
<span class="cstat-no" title="statement not covered" >    if (!sphere) {</span>
<span class="cstat-no" title="statement not covered" >        sphere = new SphereGeo({</span>
            widthSegments: subdivision * 2,
            heightSegments: subdivision
        });
<span class="cstat-no" title="statement not covered" >        sphere.generateTangents();</span>
<span class="cstat-no" title="statement not covered" >        this._geoCache.put(geoKey, sphere);</span>
    }
<span class="cstat-no" title="statement not covered" >    return this.createMesh(sphere, material, parentNode);</span>
};
&nbsp;
/**
 * Create a plane mesh and add it to the scene or the given parent node.
 * @function
 * @param {Object|clay.Material} [material]
 * @param {clay.Node} [parentNode] Parent node to append. Default to be scene.
 * @param {Array.&lt;number&gt;|number} [subdivision=1] Subdivision of plane.
 *          Can be a number to represent both width and height dimensions. Or an array to represent them respectively.
 * @return {clay.Mesh}
 * @example
 *  // Create a red color plane.
 *  app.createPlane({
 *      color: [1, 0, 0]
 *  })
 */
App3D.prototype.createPlane = function (material, parentNode, subdiv) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    if (subdiv == null) {</span>
<span class="cstat-no" title="statement not covered" >        subdiv = 1;</span>
    }
<span class="cstat-no" title="statement not covered" >    if (typeof subdiv === 'number') {</span>
<span class="cstat-no" title="statement not covered" >        subdiv = [subdiv, subdiv];</span>
    }
    var geoKey = <span class="cstat-no" title="statement not covered" >'plane-' + subdiv.join('-');</span>
    var planeGeo = <span class="cstat-no" title="statement not covered" >this._geoCache.get(geoKey);</span>
<span class="cstat-no" title="statement not covered" >    if (!planeGeo) {</span>
<span class="cstat-no" title="statement not covered" >        planeGeo = new PlaneGeo({</span>
            widthSegments: subdiv[0],
            heightSegments: subdiv[1]
        });
<span class="cstat-no" title="statement not covered" >        planeGeo.generateTangents();</span>
<span class="cstat-no" title="statement not covered" >        this._geoCache.put(geoKey, planeGeo);</span>
    }
<span class="cstat-no" title="statement not covered" >    return this.createMesh(planeGeo, material, parentNode);</span>
};
&nbsp;
/**
 * Create mesh with parametric surface function
 * @param {Object|clay.Material} [material]
 * @param {clay.Node} [parentNode] Parent node to append. Default to be scene.
 * @param {Object} generator
 * @param {Function} generator.x
 * @param {Function} generator.y
 * @param {Function} generator.z
 * @param {Array} [generator.u=[0, 1, 0.05]]
 * @param {Array} [generator.v=[0, 1, 0.05]]
 * @return {clay.Mesh}
 */
App3D.prototype.createParametricSurface = function (material, parentNode, generator) <span class="fstat-no" title="function not covered" >{</span>
    var geo = <span class="cstat-no" title="statement not covered" >new ParametricSurfaceGeo({</span>
        generator: generator
    });
<span class="cstat-no" title="statement not covered" >    geo.generateTangents();</span>
<span class="cstat-no" title="statement not covered" >    return this.createMesh(geo, material, parentNode);</span>
};
&nbsp;
&nbsp;
/**
 * Create a general mesh with given geometry instance and material config.
 * @param {clay.Geometry} geometry
 * @return {clay.Mesh}
 */
App3D.prototype.createMesh = function (geometry, mat, parentNode) <span class="fstat-no" title="function not covered" >{</span>
    var mesh = <span class="cstat-no" title="statement not covered" >new Mesh({</span>
        geometry: geometry,
        material: mat instanceof Material ? mat : this.createMaterial(mat)
    });
<span class="cstat-no" title="statement not covered" >    parentNode = parentNode || this.scene;</span>
<span class="cstat-no" title="statement not covered" >    parentNode.add(mesh);</span>
<span class="cstat-no" title="statement not covered" >    return mesh;</span>
};
&nbsp;
/**
 * Create an empty node
 * @param {clay.Node} parentNode
 * @return {clay.Node}
 */
App3D.prototype.createNode = function (parentNode) <span class="fstat-no" title="function not covered" >{</span>
    var node = <span class="cstat-no" title="statement not covered" >new Node();</span>
<span class="cstat-no" title="statement not covered" >    parentNode = parentNode || this.scene;</span>
<span class="cstat-no" title="statement not covered" >    parentNode.add(node);</span>
<span class="cstat-no" title="statement not covered" >    return node;</span>
};
&nbsp;
/**
 * Create a perspective or orthographic camera and add it to the scene.
 * @param {Array.&lt;number&gt;|clay.Vector3} position
 * @param {Array.&lt;number&gt;|clay.Vector3} target
 * @param {string} [type="perspective"] Can be 'perspective' or 'orthographic'(in short 'ortho')
 * @param {Array.&lt;number&gt;|clay.Vector3} [extent] Extent is available only if type is orthographic.
 * @return {clay.camera.Perspective|clay.camera.Orthographic}
 */
App3D.prototype.createCamera = function (position, target, type, extent) <span class="fstat-no" title="function not covered" >{</span>
    var CameraCtor;
    var isOrtho = <span class="cstat-no" title="statement not covered" >false;</span>
<span class="cstat-no" title="statement not covered" >    if (type === 'ortho' || type === 'orthographic') {</span>
<span class="cstat-no" title="statement not covered" >        isOrtho = true;</span>
<span class="cstat-no" title="statement not covered" >        CameraCtor = OrthographicCamera;</span>
    }
    else {
<span class="cstat-no" title="statement not covered" >        if (type &amp;&amp; type !== 'perspective') {</span>
<span class="cstat-no" title="statement not covered" >            console.error('Unkown camera type ' + type + '. Use default perspective camera');</span>
        }
<span class="cstat-no" title="statement not covered" >        CameraCtor = PerspectiveCamera;</span>
    }
&nbsp;
    var camera = <span class="cstat-no" title="statement not covered" >new CameraCtor();</span>
<span class="cstat-no" title="statement not covered" >    if (position instanceof Vector3) {</span>
<span class="cstat-no" title="statement not covered" >        camera.position.copy(position);</span>
    }
    else <span class="cstat-no" title="statement not covered" >if (position instanceof Array) {</span>
<span class="cstat-no" title="statement not covered" >        camera.position.setArray(position);</span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (target instanceof Array) {</span>
<span class="cstat-no" title="statement not covered" >        target = new Vector3(target[0], target[1], target[2]);</span>
    }
<span class="cstat-no" title="statement not covered" >    if (target instanceof Vector3) {</span>
<span class="cstat-no" title="statement not covered" >        camera.lookAt(target);</span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (extent &amp;&amp; isOrtho) {</span>
<span class="cstat-no" title="statement not covered" >        extent = extent.array || extent;</span>
<span class="cstat-no" title="statement not covered" >        camera.left = -extent[0] / 2;</span>
<span class="cstat-no" title="statement not covered" >        camera.right = extent[0] / 2;</span>
<span class="cstat-no" title="statement not covered" >        camera.top = extent[1] / 2;</span>
<span class="cstat-no" title="statement not covered" >        camera.bottom = -extent[1] / 2;</span>
<span class="cstat-no" title="statement not covered" >        camera.near = 0;</span>
<span class="cstat-no" title="statement not covered" >        camera.far = extent[2];</span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.scene.add(camera);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return camera;</span>
};
&nbsp;
/**
 * Create a directional light and add it to the scene.
 * @param {Array.&lt;number&gt;|clay.Vector3} dir A Vector3 or array to represent the direction.
 * @param {Color} [color='#fff'] Color of directional light, default to be white.
 * @param {number} [intensity] Intensity of directional light, default to be 1.
 *
 * @example
 *  app.createDirectionalLight([-1, -1, -1], '#fff', 2);
 */
App3D.prototype.createDirectionalLight = function (dir, color, intensity) <span class="fstat-no" title="function not covered" >{</span>
    var light = <span class="cstat-no" title="statement not covered" >new DirectionalLight();</span>
<span class="cstat-no" title="statement not covered" >    if (dir instanceof Vector3) {</span>
<span class="cstat-no" title="statement not covered" >        dir = dir.array;</span>
    }
<span class="cstat-no" title="statement not covered" >    light.position.setArray(dir).negate();</span>
<span class="cstat-no" title="statement not covered" >    light.lookAt(Vector3.ZERO);</span>
<span class="cstat-no" title="statement not covered" >    if (typeof color === 'string') {</span>
<span class="cstat-no" title="statement not covered" >        color = parseColor(color);</span>
    }
<span class="cstat-no" title="statement not covered" >    color != null &amp;&amp; (light.color = color);</span>
<span class="cstat-no" title="statement not covered" >    intensity != null &amp;&amp; (light.intensity = intensity);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.scene.add(light);</span>
<span class="cstat-no" title="statement not covered" >    return light;</span>
};
&nbsp;
/**
 * Create a spot light and add it to the scene.
 * @param {Array.&lt;number&gt;|clay.Vector3} position Position of the spot light.
 * @param {Array.&lt;number&gt;|clay.Vector3} [target] Target position where spot light points to.
 * @param {number} [range=20] Falloff range of spot light. Default to be 20.
 * @param {Color} [color='#fff'] Color of spot light, default to be white.
 * @param {number} [intensity=1] Intensity of spot light, default to be 1.
 * @param {number} [umbraAngle=30] Umbra angle of spot light. Default to be 30 degree from the middle line.
 * @param {number} [penumbraAngle=45] Penumbra angle of spot light. Default to be 45 degree from the middle line.
 *
 * @example
 *  app.createSpotLight([5, 5, 5], [0, 0, 0], 20, #900);
 */
App3D.prototype.createSpotLight = function (position, target, range, color, intensity, umbraAngle, penumbraAngle) <span class="fstat-no" title="function not covered" >{</span>
    var light = <span class="cstat-no" title="statement not covered" >new SpotLight();</span>
<span class="cstat-no" title="statement not covered" >    light.position.setArray(position instanceof Vector3 ? position.array : position);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (target instanceof Array) {</span>
<span class="cstat-no" title="statement not covered" >        target = new Vector3(target[0], target[1], target[2]);</span>
    }
<span class="cstat-no" title="statement not covered" >    if (target instanceof Vector3) {</span>
<span class="cstat-no" title="statement not covered" >        light.lookAt(target);</span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (typeof color === 'string') {</span>
<span class="cstat-no" title="statement not covered" >        color = parseColor(color);</span>
    }
<span class="cstat-no" title="statement not covered" >    range != null &amp;&amp; (light.range = range);</span>
<span class="cstat-no" title="statement not covered" >    color != null &amp;&amp; (light.color = color);</span>
<span class="cstat-no" title="statement not covered" >    intensity != null &amp;&amp; (light.intensity = intensity);</span>
<span class="cstat-no" title="statement not covered" >    umbraAngle != null &amp;&amp; (light.umbraAngle = umbraAngle);</span>
<span class="cstat-no" title="statement not covered" >    penumbraAngle != null &amp;&amp; (light.penumbraAngle = penumbraAngle);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.scene.add(light);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return light;</span>
};
&nbsp;
/**
 * Create a point light.
 * @param {Array.&lt;number&gt;|clay.Vector3} position Position of point light..
 * @param {number} [range=100] Falloff range of point light.
 * @param {Color} [color='#fff'] Color of point light.
 * @param {number} [intensity=1] Intensity of point light.
 */
App3D.prototype.createPointLight = function (position, range, color, intensity) <span class="fstat-no" title="function not covered" >{</span>
    var light = <span class="cstat-no" title="statement not covered" >new PointLight();</span>
<span class="cstat-no" title="statement not covered" >    light.position.setArray(position instanceof Vector3 ? position.array : position);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (typeof color === 'string') {</span>
<span class="cstat-no" title="statement not covered" >        color = parseColor(color);</span>
    }
<span class="cstat-no" title="statement not covered" >    range != null &amp;&amp; (light.range = range);</span>
<span class="cstat-no" title="statement not covered" >    color != null &amp;&amp; (light.color = color);</span>
<span class="cstat-no" title="statement not covered" >    intensity != null &amp;&amp; (light.intensity = intensity);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.scene.add(light);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return light;</span>
};
&nbsp;
/**
 * Create a ambient light.
 * @param {Color} [color='#fff'] Color of ambient light.
 * @param {number} [intensity=1] Intensity of ambient light.
 */
App3D.prototype.createAmbientLight = function (color, intensity) <span class="fstat-no" title="function not covered" >{</span>
    var light = <span class="cstat-no" title="statement not covered" >new AmbientLight();</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (typeof color === 'string') {</span>
<span class="cstat-no" title="statement not covered" >        color = parseColor(color);</span>
    }
<span class="cstat-no" title="statement not covered" >    color != null &amp;&amp; (light.color = color);</span>
<span class="cstat-no" title="statement not covered" >    intensity != null &amp;&amp; (light.intensity = intensity);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.scene.add(light);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return light;</span>
};
&nbsp;
/**
 * Create an cubemap ambient light and an spherical harmonic ambient light
 * for specular and diffuse lighting in PBR rendering
 * @param {ImageLike|TextureCube} [envImage] Panorama environment image, HDR format is better. Or a pre loaded texture cube
 * @param {number} [specularIntenstity=0.7] Intensity of specular light.
 * @param {number} [diffuseIntenstity=0.7] Intensity of diffuse light.
 * @param {number} [exposure=1] Exposure of HDR image. Only if image in first paramter is HDR.
 * @param {number} [prefilteredCubemapSize=32] The size of prefilerted cubemap. Larger value will take more time to do expensive prefiltering.
 */
App3D.prototype.createAmbientCubemapLight = function (envImage, specIntensity, diffIntensity, exposure, prefilteredCubemapSize) <span class="fstat-no" title="function not covered" >{</span>
    var self = <span class="cstat-no" title="statement not covered" >this;</span>
<span class="cstat-no" title="statement not covered" >    if (exposure == null) {</span>
<span class="cstat-no" title="statement not covered" >        exposure = 1;</span>
    }
<span class="cstat-no" title="statement not covered" >    if (prefilteredCubemapSize == null) {</span>
<span class="cstat-no" title="statement not covered" >        prefilteredCubemapSize = 32;</span>
    }
&nbsp;
    var scene = <span class="cstat-no" title="statement not covered" >this.scene;</span>
&nbsp;
    var loadPromise;
<span class="cstat-no" title="statement not covered" >    if (envImage.textureType === 'textureCube') {</span>
<span class="cstat-no" title="statement not covered" >        loadPromise = envImage.isRenderable()</span>
            ? Promise.resolve(envImage)
            : new Promise(function (resolve, reject) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >                envImage.success(function () <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >                    resolve(envImage);</span>
                });
            });
    }
    else {
<span class="cstat-no" title="statement not covered" >        loadPromise = this.loadTexture(envImage, {</span>
            exposure: exposure
        });
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    return loadPromise.then(function (envTexture) <span class="fstat-no" title="function not covered" >{</span></span>
        var specLight = <span class="cstat-no" title="statement not covered" >new AmbientCubemapLight({</span>
            intensity: specIntensity != null ? specIntensity : 0.7
        });
<span class="cstat-no" title="statement not covered" >        specLight.cubemap = envTexture;</span>
<span class="cstat-no" title="statement not covered" >        envTexture.flipY = false;</span>
        // TODO Cache prefilter ?
<span class="cstat-no" title="statement not covered" >        specLight.prefilter(self.renderer, 32);</span>
&nbsp;
        var diffLight = <span class="cstat-no" title="statement not covered" >new AmbientSHLight({</span>
            intensity: diffIntensity != null ? diffIntensity : 0.7,
            coefficients: shUtil.projectEnvironmentMap(
                self.renderer, specLight.cubemap, {
                    lod: 1
                }
            )
        });
<span class="cstat-no" title="statement not covered" >        scene.add(specLight);</span>
<span class="cstat-no" title="statement not covered" >        scene.add(diffLight);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        return {</span>
            specular: specLight,
            diffuse: diffLight,
            // Original environment map
            environmentMap: envTexture
        };
    });
};
&nbsp;
/**
 * Load a [glTF](https://github.com/KhronosGroup/glTF) format model.
 * You can convert FBX/DAE/OBJ format models to [glTF](https://github.com/KhronosGroup/glTF) with [fbx2gltf](https://github.com/pissang/claygl#fbx-to-gltf20-converter) python script,
 * or simply using the [Clay Viewer](https://github.com/pissang/clay-viewer) client application.
 * @param {string} url
 * @param {Object} opts
 * @param {string|clay.Shader} [opts.shader='clay.standard'] 'basic'|'lambert'|'standard'.
 * @param {boolean} [opts.waitTextureLoaded=false] If add to scene util textures are all loaded.
 * @param {boolean} [opts.autoPlayAnimation=true] If autoplay the animation of model.
 * @param {boolean} [opts.upAxis='y'] Change model to y up if upAxis is 'z'
 * @param {boolean} [opts.textureFlipY=false]
 * @param {string} [opts.textureRootPath] Root path of texture. Default to be relative with glTF file.
 * @param {clay.Node} [parentNode] Parent node that model will be mounted. Default to be scene
 * @return {Promise}
 */
App3D.prototype.loadModel = function (url, opts, parentNode) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    if (typeof url !== 'string') {</span>
<span class="cstat-no" title="statement not covered" >        throw new Error('Invalid URL.');</span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    opts = opts || {};</span>
<span class="cstat-no" title="statement not covered" >    if (opts.autoPlayAnimation == null) {</span>
<span class="cstat-no" title="statement not covered" >        opts.autoPlayAnimation = true;</span>
    }
    var shader = <span class="cstat-no" title="statement not covered" >opts.shader || 'clay.standard';</span>
&nbsp;
    var loaderOpts = <span class="cstat-no" title="statement not covered" >{</span>
        rootNode: new Node(),
        shader: shader,
        textureRootPath: opts.textureRootPath,
        crossOrigin: 'Anonymous',
        textureFlipY: opts.textureFlipY
    };
<span class="cstat-no" title="statement not covered" >    if (opts.upAxis &amp;&amp; opts.upAxis.toLowerCase() === 'z') {</span>
<span class="cstat-no" title="statement not covered" >        loaderOpts.rootNode.rotation.identity().rotateX(-Math.PI / 2);</span>
    }
&nbsp;
    var loader = <span class="cstat-no" title="statement not covered" >new GLTFLoader(loaderOpts);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    parentNode = parentNode || this.scene;</span>
    var timeline = <span class="cstat-no" title="statement not covered" >this.timeline;</span>
    var self = <span class="cstat-no" title="statement not covered" >this;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return new Promise(function (resolve, reject) <span class="fstat-no" title="function not covered" >{</span></span>
        function afterLoad(result) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            if (self._disposed) {</span>
<span class="cstat-no" title="statement not covered" >                return;</span>
            }
&nbsp;
<span class="cstat-no" title="statement not covered" >            parentNode.add(result.rootNode);</span>
<span class="cstat-no" title="statement not covered" >            if (opts.autoPlayAnimation) {</span>
<span class="cstat-no" title="statement not covered" >                result.clips.forEach(function (clip) <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >                    timeline.addClip(clip);</span>
                });
            }
<span class="cstat-no" title="statement not covered" >            resolve(result);</span>
        }
<span class="cstat-no" title="statement not covered" >        loader.success(function (result) <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >            if (self._disposed) {</span>
<span class="cstat-no" title="statement not covered" >                return;</span>
            }
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (!opts.waitTextureLoaded) {</span>
<span class="cstat-no" title="statement not covered" >                afterLoad(result);</span>
            }
            else {
<span class="cstat-no" title="statement not covered" >                Promise.all(result.textures.map(function (texture) <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >                    if (texture.isRenderable()) {</span>
<span class="cstat-no" title="statement not covered" >                        return Promise.resolve(texture);</span>
                    }
<span class="cstat-no" title="statement not covered" >                    return new Promise(function (resolve) <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >                        texture.success(resolve);</span>
<span class="cstat-no" title="statement not covered" >                        texture.error(resolve);</span>
                    });
                })).then(function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >                    afterLoad(result);</span>
                }).catch(function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >                    afterLoad(result);</span>
                });
            }
        });
<span class="cstat-no" title="statement not covered" >        loader.error(function () <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >            reject();</span>
        });
<span class="cstat-no" title="statement not covered" >        loader.load(url);</span>
    });
};
&nbsp;
&nbsp;
// TODO cloneModel
&nbsp;
/**
 * Similar to `app.scene.cloneNode`, except it will mount the cloned node to the scene automatically.
 * See more in {@link clay.Scene#cloneNode}
 *
 * @param {clay.Node} node
 * @param {clay.Node} [parentNode] Parent node that new cloned node will be mounted.
 *          Default to have same parent with source node.
 * @return {clay.Node}
 */
App3D.prototype.cloneNode = function (node, parentNode) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    parentNode = parentNode || node.getParent();</span>
&nbsp;
    var newNode = <span class="cstat-no" title="statement not covered" >this.scene.cloneNode(node, parentNode);</span>
<span class="cstat-no" title="statement not covered" >    if (parentNode) {</span>
<span class="cstat-no" title="statement not covered" >        parentNode.add(newNode);</span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    return newNode;</span>
};
&nbsp;
&nbsp;
export default {
    App3D: App3D,
    /**
     * Create a 3D application that will manage the app initialization and loop.
     *
     * See more details at {@link clay.application.App3D}
     *
     * @name clay.application.create
     * @method
     * @param {HTMLElement|string} dom Container dom element or a selector string that can be used in `querySelector`
     * @param {App3DNamespace} appNS Options and namespace used in creating app3D
     *
     * @return {clay.application.App3D}
     *
     * @example
     *  clay.application.create('#app', {
     *      init: function (app) {
     *          app.createCube();
     *          var camera = app.createCamera();
     *          camera.position.set(0, 0, 2);
     *      },
     *      loop: function () { // noop }
     *  })
     */
    create: function (dom, appNS) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return new App3D(dom, appNS);</span>
    }
};</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
