<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/math/</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> src/math/
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">35.15% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>695/1977</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">20.17% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>47/233</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">15.73% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>73/464</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">34.62% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>671/1938</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="BoundingBox.js"><a href="BoundingBox.js.html">BoundingBox.js</a></td>
	<td data-value="78.13" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 78%;"></div><div class="cover-empty" style="width:22%;"></div></div></td>
	<td data-value="78.13" class="pct medium">78.13%</td>
	<td data-value="160" class="abs medium">125/160</td>
	<td data-value="32.61" class="pct low">32.61%</td>
	<td data-value="46" class="abs low">15/46</td>
	<td data-value="73.33" class="pct medium">73.33%</td>
	<td data-value="15" class="abs medium">11/15</td>
	<td data-value="79.58" class="pct medium">79.58%</td>
	<td data-value="142" class="abs medium">113/142</td>
	</tr>

<tr>
	<td class="file high" data-value="Frustum.js"><a href="Frustum.js.html">Frustum.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="111" class="abs high">111/111</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="99" class="abs high">99/99</td>
	</tr>

<tr>
	<td class="file low" data-value="Matrix2.js"><a href="Matrix2.js.html">Matrix2.js</a></td>
	<td data-value="16.05" class="pic low"><div class="chart"><div class="cover-fill" style="width: 16%;"></div><div class="cover-empty" style="width:84%;"></div></div></td>
	<td data-value="16.05" class="pct low">16.05%</td>
	<td data-value="81" class="abs low">13/81</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="26" class="abs low">0/26</td>
	<td data-value="16.05" class="pct low">16.05%</td>
	<td data-value="81" class="abs low">13/81</td>
	</tr>

<tr>
	<td class="file low" data-value="Matrix2d.js"><a href="Matrix2d.js.html">Matrix2d.js</a></td>
	<td data-value="16.22" class="pic low"><div class="chart"><div class="cover-fill" style="width: 16%;"></div><div class="cover-empty" style="width:84%;"></div></div></td>
	<td data-value="16.22" class="pct low">16.22%</td>
	<td data-value="74" class="abs low">12/74</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="24" class="abs low">0/24</td>
	<td data-value="16.22" class="pct low">16.22%</td>
	<td data-value="74" class="abs low">12/74</td>
	</tr>

<tr>
	<td class="file low" data-value="Matrix3.js"><a href="Matrix3.js.html">Matrix3.js</a></td>
	<td data-value="15.65" class="pic low"><div class="chart"><div class="cover-fill" style="width: 15%;"></div><div class="cover-empty" style="width:85%;"></div></div></td>
	<td data-value="15.65" class="pct low">15.65%</td>
	<td data-value="115" class="abs low">18/115</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="36" class="abs low">0/36</td>
	<td data-value="15.65" class="pct low">15.65%</td>
	<td data-value="115" class="abs low">18/115</td>
	</tr>

<tr>
	<td class="file medium" data-value="Matrix4.js"><a href="Matrix4.js.html">Matrix4.js</a></td>
	<td data-value="53.68" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 53%;"></div><div class="cover-empty" style="width:47%;"></div></div></td>
	<td data-value="53.68" class="pct medium">53.68%</td>
	<td data-value="231" class="abs medium">124/231</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="6" class="abs medium">3/6</td>
	<td data-value="41.82" class="pct low">41.82%</td>
	<td data-value="55" class="abs low">23/55</td>
	<td data-value="53.68" class="pct medium">53.68%</td>
	<td data-value="231" class="abs medium">124/231</td>
	</tr>

<tr>
	<td class="file low" data-value="Plane.js"><a href="Plane.js.html">Plane.js</a></td>
	<td data-value="26.56" class="pic low"><div class="chart"><div class="cover-fill" style="width: 26%;"></div><div class="cover-empty" style="width:74%;"></div></div></td>
	<td data-value="26.56" class="pct low">26.56%</td>
	<td data-value="64" class="abs low">17/64</td>
	<td data-value="22.22" class="pct low">22.22%</td>
	<td data-value="18" class="abs low">4/18</td>
	<td data-value="36.36" class="pct low">36.36%</td>
	<td data-value="11" class="abs low">4/11</td>
	<td data-value="26.56" class="pct low">26.56%</td>
	<td data-value="64" class="abs low">17/64</td>
	</tr>

<tr>
	<td class="file low" data-value="Quaternion.js"><a href="Quaternion.js.html">Quaternion.js</a></td>
	<td data-value="23.11" class="pic low"><div class="chart"><div class="cover-fill" style="width: 23%;"></div><div class="cover-empty" style="width:77%;"></div></div></td>
	<td data-value="23.11" class="pct low">23.11%</td>
	<td data-value="238" class="abs low">55/238</td>
	<td data-value="44.44" class="pct low">44.44%</td>
	<td data-value="18" class="abs low">8/18</td>
	<td data-value="9.38" class="pct low">9.38%</td>
	<td data-value="64" class="abs low">6/64</td>
	<td data-value="23.11" class="pct low">23.11%</td>
	<td data-value="238" class="abs low">55/238</td>
	</tr>

<tr>
	<td class="file low" data-value="Ray.js"><a href="Ray.js.html">Ray.js</a></td>
	<td data-value="10.56" class="pic low"><div class="chart"><div class="cover-fill" style="width: 10%;"></div><div class="cover-empty" style="width:90%;"></div></div></td>
	<td data-value="10.56" class="pct low">10.56%</td>
	<td data-value="142" class="abs low">15/142</td>
	<td data-value="5.19" class="pct low">5.19%</td>
	<td data-value="77" class="abs low">4/77</td>
	<td data-value="30.77" class="pct low">30.77%</td>
	<td data-value="13" class="abs low">4/13</td>
	<td data-value="10.56" class="pct low">10.56%</td>
	<td data-value="142" class="abs low">15/142</td>
	</tr>

<tr>
	<td class="file low" data-value="Value.js"><a href="Value.js.html">Value.js</a></td>
	<td data-value="41.51" class="pic low"><div class="chart"><div class="cover-fill" style="width: 41%;"></div><div class="cover-empty" style="width:59%;"></div></div></td>
	<td data-value="41.51" class="pct low">41.51%</td>
	<td data-value="53" class="abs low">22/53</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	<td data-value="5.88" class="pct low">5.88%</td>
	<td data-value="17" class="abs low">1/17</td>
	<td data-value="41.51" class="pct low">41.51%</td>
	<td data-value="53" class="abs low">22/53</td>
	</tr>

<tr>
	<td class="file low" data-value="Vector2.js"><a href="Vector2.js.html">Vector2.js</a></td>
	<td data-value="21.35" class="pic low"><div class="chart"><div class="cover-fill" style="width: 21%;"></div><div class="cover-empty" style="width:79%;"></div></div></td>
	<td data-value="21.35" class="pct low">21.35%</td>
	<td data-value="192" class="abs low">41/192</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="6" class="abs medium">3/6</td>
	<td data-value="1.56" class="pct low">1.56%</td>
	<td data-value="64" class="abs low">1/64</td>
	<td data-value="21.35" class="pct low">21.35%</td>
	<td data-value="192" class="abs low">41/192</td>
	</tr>

<tr>
	<td class="file low" data-value="Vector3.js"><a href="Vector3.js.html">Vector3.js</a></td>
	<td data-value="31.55" class="pic low"><div class="chart"><div class="cover-fill" style="width: 31%;"></div><div class="cover-empty" style="width:69%;"></div></div></td>
	<td data-value="31.55" class="pct low">31.55%</td>
	<td data-value="317" class="abs low">100/317</td>
	<td data-value="15.91" class="pct low">15.91%</td>
	<td data-value="44" class="abs low">7/44</td>
	<td data-value="25.71" class="pct low">25.71%</td>
	<td data-value="70" class="abs low">18/70</td>
	<td data-value="32.47" class="pct low">32.47%</td>
	<td data-value="308" class="abs low">100/308</td>
	</tr>

<tr>
	<td class="file low" data-value="Vector4.js"><a href="Vector4.js.html">Vector4.js</a></td>
	<td data-value="20" class="pic low"><div class="chart"><div class="cover-fill" style="width: 20%;"></div><div class="cover-empty" style="width:80%;"></div></div></td>
	<td data-value="20" class="pct low">20%</td>
	<td data-value="185" class="abs low">37/185</td>
	<td data-value="10" class="pct low">10%</td>
	<td data-value="10" class="abs low">1/10</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="62" class="abs low">0/62</td>
	<td data-value="20" class="pct low">20%</td>
	<td data-value="185" class="abs low">37/185</td>
	</tr>

<tr>
	<td class="file low" data-value="util.js"><a href="util.js.html">util.js</a></td>
	<td data-value="35.71" class="pic low"><div class="chart"><div class="cover-fill" style="width: 35%;"></div><div class="cover-empty" style="width:65%;"></div></div></td>
	<td data-value="35.71" class="pct low">35.71%</td>
	<td data-value="14" class="abs low">5/14</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="3" class="abs low">1/3</td>
	<td data-value="35.71" class="pct low">35.71%</td>
	<td data-value="14" class="abs low">5/14</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
