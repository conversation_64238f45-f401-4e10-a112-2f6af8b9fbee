<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/math/Vector4.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/math/</a> Vector4.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">20% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>37/185</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">10% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>1/10</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/62</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">20% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>37/185</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
350
351
352
353
354
355
356
357
358
359
360
361
362
363
364
365
366
367
368
369
370
371
372
373
374
375
376
377
378
379
380
381
382
383
384
385
386
387
388
389
390
391
392
393
394
395
396
397
398
399
400
401
402
403
404
405
406
407
408
409
410
411
412
413
414
415
416
417
418
419
420
421
422
423
424
425
426
427
428
429
430
431
432
433
434
435
436
437
438
439
440
441
442
443
444
445
446
447
448
449
450
451
452
453
454
455
456
457
458
459
460
461
462
463
464
465
466
467
468
469
470
471
472
473
474
475
476
477
478
479
480
481
482
483
484
485
486
487
488
489
490
491
492
493
494
495
496
497
498
499
500
501
502
503
504
505
506
507
508
509
510
511
512
513
514
515
516
517
518
519
520
521
522
523
524
525
526
527
528
529
530
531
532
533
534
535
536
537
538
539
540
541
542
543
544
545
546
547
548
549
550
551
552
553
554
555
556
557
558
559
560
561
562
563
564
565
566
567
568
569
570
571
572
573
574
575
576
577
578
579
580
581
582
583
584
585
586
587
588
589
590
591
592
593
594
595
596
597
598
599
600
601
602
603
604
605
606
607
608
609
610
611
612
613
614
615
616
617
618
619
620
621
622
623
624
625
626
627
628
629
630
631
632
633
634
635
636
637
638
639
640
641
642
643
644
645
646
647
648
649
650
651
652
653
654
655
656
657
658
659
660
661
662
663
664
665
666
667
668
669
670
671
672
673
674
675
676
677
678
679
680
681
682
683
684
685
686
687
688
689
690
691
692
693
694
695
696
697
698
699
700
701
702
703
704
705
706
707
708
709
710
711
712
713
714
715
716
717
718
719
720
721</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import glMatrix from '../dep/glmatrix';
var vec4 = glMatrix.vec4;
&nbsp;
/**
 * @constructor
 * @alias clay.Vector4
 * @param {number} x
 * @param {number} y
 * @param {number} z
 * @param {number} w
 */
var Vector4 = function(x, y, z, w) <span class="fstat-no" title="function not covered" >{</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    x = x || 0;</span>
<span class="cstat-no" title="statement not covered" >    y = y || 0;</span>
<span class="cstat-no" title="statement not covered" >    z = z || 0;</span>
<span class="cstat-no" title="statement not covered" >    w = w || 0;</span>
&nbsp;
    /**
     * Storage of Vector4, read and write of x, y, z, w will change the values in array
     * All methods also operate on the array instead of x, y, z, w components
     * @name array
     * @type {Float32Array}
     * @memberOf clay.Vector4#
     */
<span class="cstat-no" title="statement not covered" >    this.array = vec4.fromValues(x, y, z, w);</span>
&nbsp;
    /**
     * Dirty flag is used by the Node to determine
     * if the matrix is updated to latest
     * @name _dirty
     * @type {boolean}
     * @memberOf clay.Vector4#
     */
<span class="cstat-no" title="statement not covered" >    this._dirty = true;</span>
};
&nbsp;
Vector4.prototype = {
&nbsp;
    constructor: Vector4,
&nbsp;
    /**
     * Add b to self
     * @param  {clay.Vector4} b
     * @return {clay.Vector4}
     */
    add: function(b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        vec4.add(this.array, this.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Set x, y and z components
     * @param  {number}  x
     * @param  {number}  y
     * @param  {number}  z
     * @param  {number}  w
     * @return {clay.Vector4}
     */
    set: function(x, y, z, w) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        this.array[0] = x;</span>
<span class="cstat-no" title="statement not covered" >        this.array[1] = y;</span>
<span class="cstat-no" title="statement not covered" >        this.array[2] = z;</span>
<span class="cstat-no" title="statement not covered" >        this.array[3] = w;</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Set x, y, z and w components from array
     * @param  {Float32Array|number[]} arr
     * @return {clay.Vector4}
     */
    setArray: function(arr) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        this.array[0] = arr[0];</span>
<span class="cstat-no" title="statement not covered" >        this.array[1] = arr[1];</span>
<span class="cstat-no" title="statement not covered" >        this.array[2] = arr[2];</span>
<span class="cstat-no" title="statement not covered" >        this.array[3] = arr[3];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Clone a new Vector4
     * @return {clay.Vector4}
     */
    clone: function() <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return new Vector4(this.x, this.y, this.z, this.w);</span>
    },
&nbsp;
    /**
     * Copy from b
     * @param  {clay.Vector4} b
     * @return {clay.Vector4}
     */
    copy: function(b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        vec4.copy(this.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Alias for distance
     * @param  {clay.Vector4} b
     * @return {number}
     */
    dist: function(b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return vec4.dist(this.array, b.array);</span>
    },
&nbsp;
    /**
     * Distance between self and b
     * @param  {clay.Vector4} b
     * @return {number}
     */
    distance: function(b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return vec4.distance(this.array, b.array);</span>
    },
&nbsp;
    /**
     * Alias for divide
     * @param  {clay.Vector4} b
     * @return {clay.Vector4}
     */
    div: function(b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        vec4.div(this.array, this.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Divide self by b
     * @param  {clay.Vector4} b
     * @return {clay.Vector4}
     */
    divide: function(b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        vec4.divide(this.array, this.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Dot product of self and b
     * @param  {clay.Vector4} b
     * @return {number}
     */
    dot: function(b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return vec4.dot(this.array, b.array);</span>
    },
&nbsp;
    /**
     * Alias of length
     * @return {number}
     */
    len: function() <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return vec4.len(this.array);</span>
    },
&nbsp;
    /**
     * Calculate the length
     * @return {number}
     */
    length: function() <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return vec4.length(this.array);</span>
    },
    /**
     * Linear interpolation between a and b
     * @param  {clay.Vector4} a
     * @param  {clay.Vector4} b
     * @param  {number}  t
     * @return {clay.Vector4}
     */
    lerp: function(a, b, t) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        vec4.lerp(this.array, a.array, b.array, t);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Minimum of self and b
     * @param  {clay.Vector4} b
     * @return {clay.Vector4}
     */
    min: function(b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        vec4.min(this.array, this.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Maximum of self and b
     * @param  {clay.Vector4} b
     * @return {clay.Vector4}
     */
    max: function(b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        vec4.max(this.array, this.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Alias for multiply
     * @param  {clay.Vector4} b
     * @return {clay.Vector4}
     */
    mul: function(b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        vec4.mul(this.array, this.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Mutiply self and b
     * @param  {clay.Vector4} b
     * @return {clay.Vector4}
     */
    multiply: function(b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        vec4.multiply(this.array, this.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Negate self
     * @return {clay.Vector4}
     */
    negate: function() <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        vec4.negate(this.array, this.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Normalize self
     * @return {clay.Vector4}
     */
    normalize: function() <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        vec4.normalize(this.array, this.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Generate random x, y, z, w components with a given scale
     * @param  {number} scale
     * @return {clay.Vector4}
     */
    random: function(scale) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        vec4.random(this.array, scale);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Scale self
     * @param  {number}  scale
     * @return {clay.Vector4}
     */
    scale: function(s) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        vec4.scale(this.array, this.array, s);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
    /**
     * Scale b and add to self
     * @param  {clay.Vector4} b
     * @param  {number}  scale
     * @return {clay.Vector4}
     */
    scaleAndAdd: function(b, s) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        vec4.scaleAndAdd(this.array, this.array, b.array, s);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Alias for squaredDistance
     * @param  {clay.Vector4} b
     * @return {number}
     */
    sqrDist: function(b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return vec4.sqrDist(this.array, b.array);</span>
    },
&nbsp;
    /**
     * Squared distance between self and b
     * @param  {clay.Vector4} b
     * @return {number}
     */
    squaredDistance: function(b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return vec4.squaredDistance(this.array, b.array);</span>
    },
&nbsp;
    /**
     * Alias for squaredLength
     * @return {number}
     */
    sqrLen: function() <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return vec4.sqrLen(this.array);</span>
    },
&nbsp;
    /**
     * Squared length of self
     * @return {number}
     */
    squaredLength: function() <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return vec4.squaredLength(this.array);</span>
    },
&nbsp;
    /**
     * Alias for subtract
     * @param  {clay.Vector4} b
     * @return {clay.Vector4}
     */
    sub: function(b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        vec4.sub(this.array, this.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Subtract b from self
     * @param  {clay.Vector4} b
     * @return {clay.Vector4}
     */
    subtract: function(b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        vec4.subtract(this.array, this.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Transform self with a Matrix4 m
     * @param  {clay.Matrix4} m
     * @return {clay.Vector4}
     */
    transformMat4: function(m) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        vec4.transformMat4(this.array, this.array, m.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Transform self with a Quaternion q
     * @param  {clay.Quaternion} q
     * @return {clay.Vector4}
     */
    transformQuat: function(q) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        vec4.transformQuat(this.array, this.array, q.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    toString: function() <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return '[' + Array.prototype.join.call(this.array, ',') + ']';</span>
    },
&nbsp;
    toArray: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return Array.prototype.slice.call(this.array);</span>
    }
};
&nbsp;
var defineProperty = Object.defineProperty;
// Getter and Setter
<span class="missing-if-branch" title="else path not taken" >E</span>if (defineProperty) {
&nbsp;
    var proto = Vector4.prototype;
    /**
     * @name x
     * @type {number}
     * @memberOf clay.Vector4
     * @instance
     */
    defineProperty(proto, 'x', {
        get: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            return this.array[0];</span>
        },
        set: function (value) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            this.array[0] = value;</span>
<span class="cstat-no" title="statement not covered" >            this._dirty = true;</span>
        }
    });
&nbsp;
    /**
     * @name y
     * @type {number}
     * @memberOf clay.Vector4
     * @instance
     */
    defineProperty(proto, 'y', {
        get: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            return this.array[1];</span>
        },
        set: function (value) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            this.array[1] = value;</span>
<span class="cstat-no" title="statement not covered" >            this._dirty = true;</span>
        }
    });
&nbsp;
    /**
     * @name z
     * @type {number}
     * @memberOf clay.Vector4
     * @instance
     */
    defineProperty(proto, 'z', {
        get: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            return this.array[2];</span>
        },
        set: function (value) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            this.array[2] = value;</span>
<span class="cstat-no" title="statement not covered" >            this._dirty = true;</span>
        }
    });
&nbsp;
    /**
     * @name w
     * @type {number}
     * @memberOf clay.Vector4
     * @instance
     */
    defineProperty(proto, 'w', {
        get: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            return this.array[3];</span>
        },
        set: function (value) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            this.array[3] = value;</span>
<span class="cstat-no" title="statement not covered" >            this._dirty = true;</span>
        }
    });
}
&nbsp;
// Supply methods that are not in place
&nbsp;
/**
 * @param  {clay.Vector4} out
 * @param  {clay.Vector4} a
 * @param  {clay.Vector4} b
 * @return {clay.Vector4}
 */
Vector4.add = function(out, a, b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    vec4.add(out.array, a.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Vector4} out
 * @param  {number}  x
 * @param  {number}  y
 * @param  {number}  z
 * @return {clay.Vector4}
 */
Vector4.set = function(out, x, y, z, w) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    vec4.set(out.array, x, y, z, w);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
};
&nbsp;
/**
 * @param  {clay.Vector4} out
 * @param  {clay.Vector4} b
 * @return {clay.Vector4}
 */
Vector4.copy = function(out, b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    vec4.copy(out.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Vector4} a
 * @param  {clay.Vector4} b
 * @return {number}
 */
Vector4.dist = function(a, b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return vec4.distance(a.array, b.array);</span>
};
&nbsp;
/**
 * @function
 * @param  {clay.Vector4} a
 * @param  {clay.Vector4} b
 * @return {number}
 */
Vector4.distance = Vector4.dist;
&nbsp;
/**
 * @param  {clay.Vector4} out
 * @param  {clay.Vector4} a
 * @param  {clay.Vector4} b
 * @return {clay.Vector4}
 */
Vector4.div = function(out, a, b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    vec4.divide(out.array, a.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @function
 * @param  {clay.Vector4} out
 * @param  {clay.Vector4} a
 * @param  {clay.Vector4} b
 * @return {clay.Vector4}
 */
Vector4.divide = Vector4.div;
&nbsp;
/**
 * @param  {clay.Vector4} a
 * @param  {clay.Vector4} b
 * @return {number}
 */
Vector4.dot = function(a, b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return vec4.dot(a.array, b.array);</span>
};
&nbsp;
/**
 * @param  {clay.Vector4} a
 * @return {number}
 */
Vector4.len = function(b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return vec4.length(b.array);</span>
};
&nbsp;
// Vector4.length = Vector4.len;
&nbsp;
/**
 * @param  {clay.Vector4} out
 * @param  {clay.Vector4} a
 * @param  {clay.Vector4} b
 * @param  {number}  t
 * @return {clay.Vector4}
 */
Vector4.lerp = function(out, a, b, t) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    vec4.lerp(out.array, a.array, b.array, t);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Vector4} out
 * @param  {clay.Vector4} a
 * @param  {clay.Vector4} b
 * @return {clay.Vector4}
 */
Vector4.min = function(out, a, b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    vec4.min(out.array, a.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Vector4} out
 * @param  {clay.Vector4} a
 * @param  {clay.Vector4} b
 * @return {clay.Vector4}
 */
Vector4.max = function(out, a, b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    vec4.max(out.array, a.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Vector4} out
 * @param  {clay.Vector4} a
 * @param  {clay.Vector4} b
 * @return {clay.Vector4}
 */
Vector4.mul = function(out, a, b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    vec4.multiply(out.array, a.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @function
 * @param  {clay.Vector4} out
 * @param  {clay.Vector4} a
 * @param  {clay.Vector4} b
 * @return {clay.Vector4}
 */
Vector4.multiply = Vector4.mul;
&nbsp;
/**
 * @param  {clay.Vector4} out
 * @param  {clay.Vector4} a
 * @return {clay.Vector4}
 */
Vector4.negate = function(out, a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    vec4.negate(out.array, a.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Vector4} out
 * @param  {clay.Vector4} a
 * @return {clay.Vector4}
 */
Vector4.normalize = function(out, a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    vec4.normalize(out.array, a.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Vector4} out
 * @param  {number}  scale
 * @return {clay.Vector4}
 */
Vector4.random = function(out, scale) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    vec4.random(out.array, scale);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Vector4} out
 * @param  {clay.Vector4} a
 * @param  {number}  scale
 * @return {clay.Vector4}
 */
Vector4.scale = function(out, a, scale) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    vec4.scale(out.array, a.array, scale);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Vector4} out
 * @param  {clay.Vector4} a
 * @param  {clay.Vector4} b
 * @param  {number}  scale
 * @return {clay.Vector4}
 */
Vector4.scaleAndAdd = function(out, a, b, scale) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    vec4.scaleAndAdd(out.array, a.array, b.array, scale);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Vector4} a
 * @param  {clay.Vector4} b
 * @return {number}
 */
Vector4.sqrDist = function(a, b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return vec4.sqrDist(a.array, b.array);</span>
};
&nbsp;
/**
 * @function
 * @param  {clay.Vector4} a
 * @param  {clay.Vector4} b
 * @return {number}
 */
Vector4.squaredDistance = Vector4.sqrDist;
&nbsp;
/**
 * @param  {clay.Vector4} a
 * @return {number}
 */
Vector4.sqrLen = function(a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return vec4.sqrLen(a.array);</span>
};
/**
 * @function
 * @param  {clay.Vector4} a
 * @return {number}
 */
Vector4.squaredLength = Vector4.sqrLen;
&nbsp;
/**
 * @param  {clay.Vector4} out
 * @param  {clay.Vector4} a
 * @param  {clay.Vector4} b
 * @return {clay.Vector4}
 */
Vector4.sub = function(out, a, b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    vec4.subtract(out.array, a.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
/**
 * @function
 * @param  {clay.Vector4} out
 * @param  {clay.Vector4} a
 * @param  {clay.Vector4} b
 * @return {clay.Vector4}
 */
Vector4.subtract = Vector4.sub;
&nbsp;
/**
 * @param  {clay.Vector4} out
 * @param  {clay.Vector4} a
 * @param  {clay.Matrix4} m
 * @return {clay.Vector4}
 */
Vector4.transformMat4 = function(out, a, m) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    vec4.transformMat4(out.array, a.array, m.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Vector4} out
 * @param  {clay.Vector4} a
 * @param  {clay.Quaternion} q
 * @return {clay.Vector4}
 */
Vector4.transformQuat = function(out, a, q) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    vec4.transformQuat(out.array, a.array, q.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
export default Vector4;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
