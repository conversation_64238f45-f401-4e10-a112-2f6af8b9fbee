<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/math/Matrix4.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/math/</a> Matrix4.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">53.68% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>124/231</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">50% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>3/6</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">41.82% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>23/55</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">53.68% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>124/231</span>
      </div>
    </div>
  </div>
  <div class='status-line medium'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
350
351
352
353
354
355
356
357
358
359
360
361
362
363
364
365
366
367
368
369
370
371
372
373
374
375
376
377
378
379
380
381
382
383
384
385
386
387
388
389
390
391
392
393
394
395
396
397
398
399
400
401
402
403
404
405
406
407
408
409
410
411
412
413
414
415
416
417
418
419
420
421
422
423
424
425
426
427
428
429
430
431
432
433
434
435
436
437
438
439
440
441
442
443
444
445
446
447
448
449
450
451
452
453
454
455
456
457
458
459
460
461
462
463
464
465
466
467
468
469
470
471
472
473
474
475
476
477
478
479
480
481
482
483
484
485
486
487
488
489
490
491
492
493
494
495
496
497
498
499
500
501
502
503
504
505
506
507
508
509
510
511
512
513
514
515
516
517
518
519
520
521
522
523
524
525
526
527
528
529
530
531
532
533
534
535
536
537
538
539
540
541
542
543
544
545
546
547
548
549
550
551
552
553
554
555
556
557
558
559
560
561
562
563
564
565
566
567
568
569
570
571
572
573
574
575
576
577
578
579
580
581
582
583
584
585
586
587
588
589
590
591
592
593
594
595
596
597
598
599
600
601
602
603
604
605
606
607
608
609
610
611
612
613
614
615
616
617
618
619
620
621
622
623
624
625
626
627
628
629
630
631
632
633
634
635
636
637
638
639
640
641
642
643
644
645
646
647
648
649
650
651
652
653
654
655
656
657
658
659
660
661
662
663
664
665
666
667
668
669
670
671
672
673
674
675
676
677
678
679
680
681
682
683
684
685
686
687
688
689
690
691
692
693
694
695
696
697
698
699
700
701
702
703
704
705
706
707
708
709
710
711
712
713
714
715
716
717
718
719
720
721</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">531×</span>
<span class="cline-any cline-yes">531×</span>
<span class="cline-any cline-yes">531×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">531×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">531×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">212×</span>
<span class="cline-any cline-yes">212×</span>
<span class="cline-any cline-yes">212×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">33×</span>
<span class="cline-any cline-yes">33×</span>
<span class="cline-any cline-yes">33×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">33×</span>
<span class="cline-any cline-yes">33×</span>
<span class="cline-any cline-yes">33×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">12×</span>
<span class="cline-any cline-yes">12×</span>
<span class="cline-any cline-yes">12×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">134×</span>
<span class="cline-any cline-yes">134×</span>
<span class="cline-any cline-yes">134×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-yes">34×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">14×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">9×</span>
<span class="cline-any cline-yes">9×</span>
<span class="cline-any cline-yes">9×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">298×</span>
<span class="cline-any cline-yes">298×</span>
<span class="cline-any cline-yes">298×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">11×</span>
<span class="cline-any cline-yes">11×</span>
<span class="cline-any cline-yes">11×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">70×</span>
<span class="cline-any cline-yes">70×</span>
<span class="cline-any cline-yes">70×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">105×</span>
<span class="cline-any cline-yes">105×</span>
<span class="cline-any cline-yes">105×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">210×</span>
<span class="cline-any cline-yes">210×</span>
<span class="cline-any cline-yes">210×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import glMatrix from '../dep/glmatrix';
import Vector3 from './Vector3';
var mat4 = glMatrix.mat4;
var vec3 = glMatrix.vec3;
var mat3 = glMatrix.mat3;
var quat = glMatrix.quat;
&nbsp;
/**
 * @constructor
 * @alias clay.Matrix4
 */
var Matrix4 = function() {
&nbsp;
    this._axisX = new Vector3();
    this._axisY = new Vector3();
    this._axisZ = new Vector3();
&nbsp;
    /**
     * Storage of Matrix4
     * @name array
     * @type {Float32Array}
     * @memberOf clay.Matrix4#
     */
    this.array = mat4.create();
&nbsp;
    /**
     * @name _dirty
     * @type {boolean}
     * @memberOf clay.Matrix4#
     */
    this._dirty = true;
};
&nbsp;
Matrix4.prototype = {
&nbsp;
    constructor: Matrix4,
&nbsp;
    /**
     * Set components from array
     * @param  {Float32Array|number[]} arr
     */
    setArray: function (arr) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; this.array.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >            this.array[i] = arr[i];</span>
        }
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
    /**
     * Calculate the adjugate of self, in-place
     * @return {clay.Matrix4}
     */
    adjoint: function() <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat4.adjoint(this.array, this.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Clone a new Matrix4
     * @return {clay.Matrix4}
     */
    clone: function() <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return (new Matrix4()).copy(this);</span>
    },
&nbsp;
    /**
     * Copy from b
     * @param  {clay.Matrix4} b
     * @return {clay.Matrix4}
     */
    copy: function(a) {
        mat4.copy(this.array, a.array);
        this._dirty = true;
        return this;
    },
&nbsp;
    /**
     * Calculate matrix determinant
     * @return {number}
     */
    determinant: function() {
        return mat4.determinant(this.array);
    },
&nbsp;
    /**
     * Set upper 3x3 part from quaternion
     * @param  {clay.Quaternion} q
     * @return {clay.Matrix4}
     */
    fromQuat: function(q) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat4.fromQuat(this.array, q.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Set from a quaternion rotation and a vector translation
     * @param  {clay.Quaternion} q
     * @param  {clay.Vector3} v
     * @return {clay.Matrix4}
     */
    fromRotationTranslation: function(q, v) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat4.fromRotationTranslation(this.array, q.array, v.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Set from Matrix2d, it is used when converting a 2d shape to 3d space.
     * In 3d space it is equivalent to ranslate on xy plane and rotate about z axis
     * @param  {clay.Matrix2d} m2d
     * @return {clay.Matrix4}
     */
    fromMat2d: function(m2d) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        Matrix4.fromMat2d(this, m2d);</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Set from frustum bounds
     * @param  {number} left
     * @param  {number} right
     * @param  {number} bottom
     * @param  {number} top
     * @param  {number} near
     * @param  {number} far
     * @return {clay.Matrix4}
     */
    frustum: function (left, right, bottom, top, near, far) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat4.frustum(this.array, left, right, bottom, top, near, far);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Set to a identity matrix
     * @return {clay.Matrix4}
     */
    identity: function() {
        mat4.identity(this.array);
        this._dirty = true;
        return this;
    },
&nbsp;
    /**
     * Invert self
     * @return {clay.Matrix4}
     */
    invert: function() {
        mat4.invert(this.array, this.array);
        this._dirty = true;
        return this;
    },
&nbsp;
    /**
     * Set as a matrix with the given eye position, focal point, and up axis
     * @param  {clay.Vector3} eye
     * @param  {clay.Vector3} center
     * @param  {clay.Vector3} up
     * @return {clay.Matrix4}
     */
    lookAt: function(eye, center, up) {
        mat4.lookAt(this.array, eye.array, center.array, up.array);
        this._dirty = true;
        return this;
    },
&nbsp;
    /**
     * Alias for mutiply
     * @param  {clay.Matrix4} b
     * @return {clay.Matrix4}
     */
    mul: function(b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat4.mul(this.array, this.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Alias for multiplyLeft
     * @param  {clay.Matrix4} a
     * @return {clay.Matrix4}
     */
    mulLeft: function(a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat4.mul(this.array, a.array, this.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Multiply self and b
     * @param  {clay.Matrix4} b
     * @return {clay.Matrix4}
     */
    multiply: function(b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat4.multiply(this.array, this.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Multiply a and self, a is on the left
     * @param  {clay.Matrix3} a
     * @return {clay.Matrix3}
     */
    multiplyLeft: function(a) {
        mat4.multiply(this.array, a.array, this.array);
        this._dirty = true;
        return this;
    },
&nbsp;
    /**
     * Set as a orthographic projection matrix
     * @param  {number} left
     * @param  {number} right
     * @param  {number} bottom
     * @param  {number} top
     * @param  {number} near
     * @param  {number} far
     * @return {clay.Matrix4}
     */
    ortho: function(left, right, bottom, top, near, far) {
        mat4.ortho(this.array, left, right, bottom, top, near, far);
        this._dirty = true;
        return this;
    },
    /**
     * Set as a perspective projection matrix
     * @param  {number} fovy
     * @param  {number} aspect
     * @param  {number} near
     * @param  {number} far
     * @return {clay.Matrix4}
     */
    perspective: function(fovy, aspect, near, far) {
        mat4.perspective(this.array, fovy, aspect, near, far);
        this._dirty = true;
        return this;
    },
&nbsp;
    /**
     * Rotate self by rad about axis.
     * Equal to right-multiply a rotaion matrix
     * @param  {number}   rad
     * @param  {clay.Vector3} axis
     * @return {clay.Matrix4}
     */
    rotate: function(rad, axis) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat4.rotate(this.array, this.array, rad, axis.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Rotate self by a given radian about X axis.
     * Equal to right-multiply a rotaion matrix
     * @param {number} rad
     * @return {clay.Matrix4}
     */
    rotateX: function(rad) {
        mat4.rotateX(this.array, this.array, rad);
        this._dirty = true;
        return this;
    },
&nbsp;
    /**
     * Rotate self by a given radian about Y axis.
     * Equal to right-multiply a rotaion matrix
     * @param {number} rad
     * @return {clay.Matrix4}
     */
    rotateY: function(rad) {
        mat4.rotateY(this.array, this.array, rad);
        this._dirty = true;
        return this;
    },
&nbsp;
    /**
     * Rotate self by a given radian about Z axis.
     * Equal to right-multiply a rotaion matrix
     * @param {number} rad
     * @return {clay.Matrix4}
     */
    rotateZ: function(rad) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat4.rotateZ(this.array, this.array, rad);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Scale self by s
     * Equal to right-multiply a scale matrix
     * @param  {clay.Vector3}  s
     * @return {clay.Matrix4}
     */
    scale: function(v) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat4.scale(this.array, this.array, v.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Translate self by v.
     * Equal to right-multiply a translate matrix
     * @param  {clay.Vector3}  v
     * @return {clay.Matrix4}
     */
    translate: function(v) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat4.translate(this.array, this.array, v.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Transpose self, in-place.
     * @return {clay.Matrix2}
     */
    transpose: function() <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat4.transpose(this.array, this.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Decompose a matrix to SRT
     * @param {clay.Vector3} [scale]
     * @param {clay.Quaternion} rotation
     * @param {clay.Vector} position
     * @see http://msdn.microsoft.com/en-us/library/microsoft.xna.framework.matrix.decompose.aspx
     */
    decomposeMatrix: (function() {
&nbsp;
        var x = vec3.create();
        var y = vec3.create();
        var z = vec3.create();
&nbsp;
        var m3 = mat3.create();
&nbsp;
        return function(scale, rotation, position) {
&nbsp;
            var el = this.array;
            vec3.set(x, el[0], el[1], el[2]);
            vec3.set(y, el[4], el[5], el[6]);
            vec3.set(z, el[8], el[9], el[10]);
&nbsp;
            var sx = vec3.length(x);
            var sy = vec3.length(y);
            var sz = vec3.length(z);
&nbsp;
            // if determine is negative, we need to invert one scale
            var det = this.determinant();
            <span class="missing-if-branch" title="if path not taken" >I</span>if (det &lt; 0) {
<span class="cstat-no" title="statement not covered" >                sx = -sx;</span>
            }
&nbsp;
            <span class="missing-if-branch" title="else path not taken" >E</span>if (scale) {
                scale.set(sx, sy, sz);
            }
&nbsp;
            position.set(el[12], el[13], el[14]);
&nbsp;
            mat3.fromMat4(m3, el);
            // Not like mat4, mat3 in glmatrix seems to be row-based
            // Seems fixed in gl-matrix 2.2.2
            // https://github.com/toji/gl-matrix/issues/114
            // mat3.transpose(m3, m3);
&nbsp;
            m3[0] /= sx;
            m3[1] /= sx;
            m3[2] /= sx;
&nbsp;
            m3[3] /= sy;
            m3[4] /= sy;
            m3[5] /= sy;
&nbsp;
            m3[6] /= sz;
            m3[7] /= sz;
            m3[8] /= sz;
&nbsp;
            quat.fromMat3(rotation.array, m3);
            quat.normalize(rotation.array, rotation.array);
&nbsp;
            rotation._dirty = true;
            position._dirty = true;
        };
    })(),
&nbsp;
    toString: function() <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return '[' + Array.prototype.join.call(this.array, ',') + ']';</span>
    },
&nbsp;
    toArray: function () {
        return Array.prototype.slice.call(this.array);
    }
};
&nbsp;
var defineProperty = Object.defineProperty;
&nbsp;
<span class="missing-if-branch" title="else path not taken" >E</span>if (defineProperty) {
    var proto = Matrix4.prototype;
    /**
     * Z Axis of local transform
     * @name z
     * @type {clay.Vector3}
     * @memberOf clay.Matrix4
     * @instance
     */
    defineProperty(proto, 'z', {
        get: function () {
            var el = this.array;
            this._axisZ.set(el[8], el[9], el[10]);
            return this._axisZ;
        },
        set: function (v) <span class="fstat-no" title="function not covered" >{</span>
            // TODO Here has a problem
            // If only set an item of vector will not work
            var el = <span class="cstat-no" title="statement not covered" >this.array;</span>
<span class="cstat-no" title="statement not covered" >            v = v.array;</span>
<span class="cstat-no" title="statement not covered" >            el[8] = v[0];</span>
<span class="cstat-no" title="statement not covered" >            el[9] = v[1];</span>
<span class="cstat-no" title="statement not covered" >            el[10] = v[2];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            this._dirty = true;</span>
        }
    });
&nbsp;
    /**
     * Y Axis of local transform
     * @name y
     * @type {clay.Vector3}
     * @memberOf clay.Matrix4
     * @instance
     */
    defineProperty(proto, 'y', {
        get: function () {
            var el = this.array;
            this._axisY.set(el[4], el[5], el[6]);
            return this._axisY;
        },
        set: function (v) <span class="fstat-no" title="function not covered" >{</span>
            var el = <span class="cstat-no" title="statement not covered" >this.array;</span>
<span class="cstat-no" title="statement not covered" >            v = v.array;</span>
<span class="cstat-no" title="statement not covered" >            el[4] = v[0];</span>
<span class="cstat-no" title="statement not covered" >            el[5] = v[1];</span>
<span class="cstat-no" title="statement not covered" >            el[6] = v[2];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            this._dirty = true;</span>
        }
    });
&nbsp;
    /**
     * X Axis of local transform
     * @name x
     * @type {clay.Vector3}
     * @memberOf clay.Matrix4
     * @instance
     */
    defineProperty(proto, 'x', {
        get: function () <span class="fstat-no" title="function not covered" >{</span>
            var el = <span class="cstat-no" title="statement not covered" >this.array;</span>
<span class="cstat-no" title="statement not covered" >            this._axisX.set(el[0], el[1], el[2]);</span>
<span class="cstat-no" title="statement not covered" >            return this._axisX;</span>
        },
        set: function (v) <span class="fstat-no" title="function not covered" >{</span>
            var el = <span class="cstat-no" title="statement not covered" >this.array;</span>
<span class="cstat-no" title="statement not covered" >            v = v.array;</span>
<span class="cstat-no" title="statement not covered" >            el[0] = v[0];</span>
<span class="cstat-no" title="statement not covered" >            el[1] = v[1];</span>
<span class="cstat-no" title="statement not covered" >            el[2] = v[2];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            this._dirty = true;</span>
        }
    })
}
&nbsp;
/**
 * @param  {clay.Matrix4} out
 * @param  {clay.Matrix4} a
 * @return {clay.Matrix4}
 */
Matrix4.adjoint = function(out, a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    mat4.adjoint(out.array, a.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Matrix4} out
 * @param  {clay.Matrix4} a
 * @return {clay.Matrix4}
 */
Matrix4.copy = function(out, a) {
    mat4.copy(out.array, a.array);
    out._dirty = true;
    return out;
};
&nbsp;
/**
 * @param  {clay.Matrix4} a
 * @return {number}
 */
Matrix4.determinant = function(a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return mat4.determinant(a.array);</span>
};
&nbsp;
/**
 * @param  {clay.Matrix4} out
 * @return {clay.Matrix4}
 */
Matrix4.identity = function(out) {
    mat4.identity(out.array);
    out._dirty = true;
    return out;
};
&nbsp;
/**
 * @param  {clay.Matrix4} out
 * @param  {number}  left
 * @param  {number}  right
 * @param  {number}  bottom
 * @param  {number}  top
 * @param  {number}  near
 * @param  {number}  far
 * @return {clay.Matrix4}
 */
Matrix4.ortho = function(out, left, right, bottom, top, near, far) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    mat4.ortho(out.array, left, right, bottom, top, near, far);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Matrix4} out
 * @param  {number}  fovy
 * @param  {number}  aspect
 * @param  {number}  near
 * @param  {number}  far
 * @return {clay.Matrix4}
 */
Matrix4.perspective = function(out, fovy, aspect, near, far) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    mat4.perspective(out.array, fovy, aspect, near, far);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Matrix4} out
 * @param  {clay.Vector3} eye
 * @param  {clay.Vector3} center
 * @param  {clay.Vector3} up
 * @return {clay.Matrix4}
 */
Matrix4.lookAt = function(out, eye, center, up) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    mat4.lookAt(out.array, eye.array, center.array, up.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Matrix4} out
 * @param  {clay.Matrix4} a
 * @return {clay.Matrix4}
 */
Matrix4.invert = function(out, a) {
    mat4.invert(out.array, a.array);
    out._dirty = true;
    return out;
};
&nbsp;
/**
 * @param  {clay.Matrix4} out
 * @param  {clay.Matrix4} a
 * @param  {clay.Matrix4} b
 * @return {clay.Matrix4}
 */
Matrix4.mul = function(out, a, b) {
    mat4.mul(out.array, a.array, b.array);
    out._dirty = true;
    return out;
};
&nbsp;
/**
 * @function
 * @param  {clay.Matrix4} out
 * @param  {clay.Matrix4} a
 * @param  {clay.Matrix4} b
 * @return {clay.Matrix4}
 */
Matrix4.multiply = Matrix4.mul;
&nbsp;
/**
 * @param  {clay.Matrix4}    out
 * @param  {clay.Quaternion} q
 * @return {clay.Matrix4}
 */
Matrix4.fromQuat = function(out, q) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    mat4.fromQuat(out.array, q.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Matrix4}    out
 * @param  {clay.Quaternion} q
 * @param  {clay.Vector3}    v
 * @return {clay.Matrix4}
 */
Matrix4.fromRotationTranslation = function(out, q, v) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    mat4.fromRotationTranslation(out.array, q.array, v.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Matrix4} m4
 * @param  {clay.Matrix2d} m2d
 * @return {clay.Matrix4}
 */
Matrix4.fromMat2d = function(m4, m2d) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    m4._dirty = true;</span>
    var m2d = <span class="cstat-no" title="statement not covered" >m2d.array;</span>
    var m4 = <span class="cstat-no" title="statement not covered" >m4.array;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    m4[0] = m2d[0];</span>
<span class="cstat-no" title="statement not covered" >    m4[4] = m2d[2];</span>
<span class="cstat-no" title="statement not covered" >    m4[12] = m2d[4];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    m4[1] = m2d[1];</span>
<span class="cstat-no" title="statement not covered" >    m4[5] = m2d[3];</span>
<span class="cstat-no" title="statement not covered" >    m4[13] = m2d[5];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return m4;</span>
};
&nbsp;
/**
 * @param  {clay.Matrix4} out
 * @param  {clay.Matrix4} a
 * @param  {number}  rad
 * @param  {clay.Vector3} axis
 * @return {clay.Matrix4}
 */
Matrix4.rotate = function(out, a, rad, axis) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    mat4.rotate(out.array, a.array, rad, axis.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Matrix4} out
 * @param  {clay.Matrix4} a
 * @param  {number}  rad
 * @return {clay.Matrix4}
 */
Matrix4.rotateX = function(out, a, rad) {
    mat4.rotateX(out.array, a.array, rad);
    out._dirty = true;
    return out;
};
&nbsp;
/**
 * @param  {clay.Matrix4} out
 * @param  {clay.Matrix4} a
 * @param  {number}  rad
 * @return {clay.Matrix4}
 */
Matrix4.rotateY = function(out, a, rad) {
    mat4.rotateY(out.array, a.array, rad);
    out._dirty = true;
    return out;
};
&nbsp;
/**
 * @param  {clay.Matrix4} out
 * @param  {clay.Matrix4} a
 * @param  {number}  rad
 * @return {clay.Matrix4}
 */
Matrix4.rotateZ = function(out, a, rad) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    mat4.rotateZ(out.array, a.array, rad);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Matrix4} out
 * @param  {clay.Matrix4} a
 * @param  {clay.Vector3} v
 * @return {clay.Matrix4}
 */
Matrix4.scale = function(out, a, v) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    mat4.scale(out.array, a.array, v.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Matrix4} out
 * @param  {clay.Matrix4} a
 * @return {clay.Matrix4}
 */
Matrix4.transpose = function(out, a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    mat4.transpose(out.array, a.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Matrix4} out
 * @param  {clay.Matrix4} a
 * @param  {clay.Vector3} v
 * @return {clay.Matrix4}
 */
Matrix4.translate = function(out, a, v) {
    mat4.translate(out.array, a.array, v.array);
    out._dirty = true;
    return out;
};
&nbsp;
export default Matrix4;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
