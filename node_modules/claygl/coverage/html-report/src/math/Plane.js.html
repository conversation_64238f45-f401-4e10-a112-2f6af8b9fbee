<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/math/Plane.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/math/</a> Plane.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">26.56% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>17/64</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">22.22% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>4/18</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">36.36% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>4/11</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">26.56% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>17/64</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">379×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">379×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">882×</span>
<span class="cline-any cline-yes">882×</span>
<span class="cline-any cline-yes">882×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import Vector3 from './Vector3';
import glMatrix from '../dep/glmatrix';
var vec3 = glMatrix.vec3;
var mat4 = glMatrix.mat4;
var vec4 = glMatrix.vec4;
&nbsp;
/**
 * @constructor
 * @alias clay.Plane
 * @param {clay.Vector3} [normal]
 * @param {number} [distance]
 */
var Plane = function(normal, distance) {
    /**
     * Normal of the plane
     * @type {clay.Vector3}
     */
    this.normal = normal || new Vector3(0, 1, 0);
&nbsp;
    /**
     * Constant of the plane equation, used as distance to the origin
     * @type {number}
     */
    this.distance = distance || 0;
};
&nbsp;
Plane.prototype = {
&nbsp;
    constructor: Plane,
&nbsp;
    /**
     * Distance from a given point to the plane
     * @param  {clay.Vector3} point
     * @return {number}
     */
    distanceToPoint: function(point) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return vec3.dot(point.array, this.normal.array) - this.distance;</span>
    },
&nbsp;
    /**
     * Calculate the projection point on the plane
     * @param  {clay.Vector3} point
     * @param  {clay.Vector3} out
     * @return {clay.Vector3}
     */
    projectPoint: function(point, out) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        if (!out) {</span>
<span class="cstat-no" title="statement not covered" >            out = new Vector3();</span>
        }
        var d = <span class="cstat-no" title="statement not covered" >this.distanceToPoint(point);</span>
<span class="cstat-no" title="statement not covered" >        vec3.scaleAndAdd(out.array, point.array, this.normal.array, -d);</span>
<span class="cstat-no" title="statement not covered" >        out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return out;</span>
    },
&nbsp;
    /**
     * Normalize the plane's normal and calculate the distance
     */
    normalize: function() {
        var invLen = 1 / vec3.len(this.normal.array);
        vec3.scale(this.normal.array, invLen);
        this.distance *= invLen;
    },
&nbsp;
    /**
     * If the plane intersect a frustum
     * @param  {clay.Frustum} Frustum
     * @return {boolean}
     */
    intersectFrustum: function(frustum) <span class="fstat-no" title="function not covered" >{</span>
        // Check if all coords of frustum is on plane all under plane
        var coords = <span class="cstat-no" title="statement not covered" >frustum.vertices;</span>
        var normal = <span class="cstat-no" title="statement not covered" >this.normal.array;</span>
        var onPlane = <span class="cstat-no" title="statement not covered" >vec3.dot(coords[0].array, normal) &gt; this.distance;</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 1; i &lt; 8; i++) {</span>
<span class="cstat-no" title="statement not covered" >            if ((vec3.dot(coords[i].array, normal) &gt; this.distance) != onPlane) {</span>
<span class="cstat-no" title="statement not covered" >                return true;</span>
            }
        }
    },
&nbsp;
    /**
     * Calculate the intersection point between plane and a given line
     * @function
     * @param {clay.Vector3} start start point of line
     * @param {clay.Vector3} end end point of line
     * @param {clay.Vector3} [out]
     * @return {clay.Vector3}
     */
    intersectLine: (function() {
        var rd = vec3.create();
        return function(start, end, out) <span class="fstat-no" title="function not covered" >{</span>
            var d0 = <span class="cstat-no" title="statement not covered" >this.distanceToPoint(start);</span>
            var d1 = <span class="cstat-no" title="statement not covered" >this.distanceToPoint(end);</span>
<span class="cstat-no" title="statement not covered" >            if ((d0 &gt; 0 &amp;&amp; d1 &gt; 0) || (d0 &lt; 0 &amp;&amp; d1 &lt; 0)) {</span>
<span class="cstat-no" title="statement not covered" >                return null;</span>
            }
            // Ray intersection
            var pn = <span class="cstat-no" title="statement not covered" >this.normal.array;</span>
            var d = <span class="cstat-no" title="statement not covered" >this.distance;</span>
            var ro = <span class="cstat-no" title="statement not covered" >start.array;</span>
            // direction
<span class="cstat-no" title="statement not covered" >            vec3.sub(rd, end.array, start.array);</span>
<span class="cstat-no" title="statement not covered" >            vec3.normalize(rd, rd);</span>
&nbsp;
            var divider = <span class="cstat-no" title="statement not covered" >vec3.dot(pn, rd);</span>
            // ray is parallel to the plane
<span class="cstat-no" title="statement not covered" >            if (divider === 0) {</span>
<span class="cstat-no" title="statement not covered" >                return null;</span>
            }
<span class="cstat-no" title="statement not covered" >            if (!out) {</span>
<span class="cstat-no" title="statement not covered" >                out = new Vector3();</span>
            }
            var t = <span class="cstat-no" title="statement not covered" >(vec3.dot(pn, ro) - d) / divider;</span>
<span class="cstat-no" title="statement not covered" >            vec3.scaleAndAdd(out.array, ro, rd, -t);</span>
<span class="cstat-no" title="statement not covered" >            out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >            return out;</span>
        };
    })(),
&nbsp;
    /**
     * Apply an affine transform matrix to plane
     * @function
     * @return {clay.Matrix4}
     */
    applyTransform: (function() {
        var inverseTranspose = mat4.create();
        var normalv4 = vec4.create();
        var pointv4 = vec4.create();
        pointv4[3] = 1;
        return function(m4) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            m4 = m4.array;</span>
            // Transform point on plane
<span class="cstat-no" title="statement not covered" >            vec3.scale(pointv4, this.normal.array, this.distance);</span>
<span class="cstat-no" title="statement not covered" >            vec4.transformMat4(pointv4, pointv4, m4);</span>
<span class="cstat-no" title="statement not covered" >            this.distance = vec3.dot(pointv4, this.normal.array);</span>
            // Transform plane normal
<span class="cstat-no" title="statement not covered" >            mat4.invert(inverseTranspose, m4);</span>
<span class="cstat-no" title="statement not covered" >            mat4.transpose(inverseTranspose, inverseTranspose);</span>
<span class="cstat-no" title="statement not covered" >            normalv4[3] = 0;</span>
<span class="cstat-no" title="statement not covered" >            vec3.copy(normalv4, this.normal.array);</span>
<span class="cstat-no" title="statement not covered" >            vec4.transformMat4(normalv4, normalv4, inverseTranspose);</span>
<span class="cstat-no" title="statement not covered" >            vec3.copy(this.normal.array, normalv4);</span>
        };
    })(),
&nbsp;
    /**
     * Copy from another plane
     * @param  {clay.Vector3} plane
     */
    copy: function(plane) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        vec3.copy(this.normal.array, plane.normal.array);</span>
<span class="cstat-no" title="statement not covered" >        this.normal._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        this.distance = plane.distance;</span>
    },
&nbsp;
    /**
     * Clone a new plane
     * @return {clay.Plane}
     */
    clone: function() <span class="fstat-no" title="function not covered" >{</span>
        var plane = <span class="cstat-no" title="statement not covered" >new Plane();</span>
<span class="cstat-no" title="statement not covered" >        plane.copy(this);</span>
<span class="cstat-no" title="statement not covered" >        return plane;</span>
    }
};
&nbsp;
export default Plane;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
