<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/gpu/</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> src/gpu/
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">83.04% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>191/230</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">69.47% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>66/95</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">95% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>19/20</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">83.04% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>191/230</span>
      </div>
    </div>
  </div>
  <div class='status-line high'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="GLProgram.js"><a href="GLProgram.js.html">GLProgram.js</a></td>
	<td data-value="76.88" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 76%;"></div><div class="cover-empty" style="width:24%;"></div></div></td>
	<td data-value="76.88" class="pct medium">76.88%</td>
	<td data-value="160" class="abs medium">123/160</td>
	<td data-value="65.22" class="pct medium">65.22%</td>
	<td data-value="69" class="abs medium">45/69</td>
	<td data-value="92.31" class="pct high">92.31%</td>
	<td data-value="13" class="abs high">12/13</td>
	<td data-value="76.88" class="pct medium">76.88%</td>
	<td data-value="160" class="abs medium">123/160</td>
	</tr>

<tr>
	<td class="file high" data-value="ProgramManager.js"><a href="ProgramManager.js.html">ProgramManager.js</a></td>
	<td data-value="97.14" class="pic high"><div class="chart"><div class="cover-fill" style="width: 97%;"></div><div class="cover-empty" style="width:3%;"></div></div></td>
	<td data-value="97.14" class="pct high">97.14%</td>
	<td data-value="70" class="abs high">68/70</td>
	<td data-value="80.77" class="pct high">80.77%</td>
	<td data-value="26" class="abs high">21/26</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="7" class="abs high">7/7</td>
	<td data-value="97.14" class="pct high">97.14%</td>
	<td data-value="70" class="abs high">68/70</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
