<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/gpu/ProgramManager.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/gpu/</a> ProgramManager.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">97.14% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>68/70</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">80.77% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>21/26</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>7/7</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">97.14% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>68/70</span>
      </div>
    </div>
  </div>
  <div class='status-line high'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">154×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">154×</span>
<span class="cline-any cline-yes">44×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">154×</span>
<span class="cline-any cline-yes">154×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">146×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">154×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">49×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">154×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">48×</span>
<span class="cline-any cline-yes">48×</span>
<span class="cline-any cline-yes">26×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">48×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">48×</span>
<span class="cline-any cline-yes">48×</span>
<span class="cline-any cline-yes">42×</span>
<span class="cline-any cline-yes">26×</span>
<span class="cline-any cline-yes">26×</span>
<span class="cline-any cline-yes">26×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">48×</span>
<span class="cline-any cline-yes">48×</span>
<span class="cline-any cline-yes">20×</span>
<span class="cline-any cline-yes">20×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">48×</span>
<span class="cline-any cline-yes">73×</span>
<span class="cline-any cline-yes">73×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">67×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">48×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-yes">48×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">26×</span>
<span class="cline-any cline-yes">26×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">89×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">89×</span>
<span class="cline-any cline-yes">89×</span>
<span class="cline-any cline-yes">69×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">89×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">89×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">89×</span>
<span class="cline-any cline-yes">65×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import GLProgram from './GLProgram';
&nbsp;
var loopRegex = /for\s*?\(int\s*?_idx_\s*\=\s*([\w-]+)\;\s*_idx_\s*&lt;\s*([\w-]+);\s*_idx_\s*\+\+\s*\)\s*\{\{([\s\S]+?)(?=\}\})\}\}/g;
&nbsp;
function unrollLoop(shaderStr, defines, lightsNumbers) {
    // Loop unroll from three.js, https://github.com/mrdoob/three.js/blob/master/src/renderers/webgl/WebGLProgram.js#L175
    // In some case like shadowMap in loop use 'i' to index value much slower.
&nbsp;
    // Loop use _idx_ and increased with _idx_++ will be unrolled
    // Use {{ }} to match the pair so the if statement will not be affected
    // Write like following
    // for (int _idx_ = 0; _idx_ &lt; 4; _idx_++) {{
    //     vec3 color = texture2D(textures[_idx_], uv).rgb;
    // }}
    function replace(match, start, end, snippet) {
        var unroll = '';
        // Try to treat as define
        if (isNaN(start)) {
            if (start in defines) {
                start = defines[start];
            }
            else {
                start = lightNumberDefines[start];
            }
        }
        <span class="missing-if-branch" title="else path not taken" >E</span>if (isNaN(end)) {
            if (end in defines) {
                end = defines[end];
            }
            else {
                end = lightNumberDefines[end];
            }
        }
        // TODO Error checking
&nbsp;
        for (var idx = parseInt(start); idx &lt; parseInt(end); idx++) {
            // PENDING Add scope?
            unroll += '{'
                + snippet
                    .replace(/float\s*\(\s*_idx_\s*\)/g, idx.toFixed(1))
                    .replace(/_idx_/g, idx)
            + '}';
        }
&nbsp;
        return unroll;
    }
&nbsp;
    var lightNumberDefines = {};
    for (var lightType in lightsNumbers) {
        lightNumberDefines[lightType + '_COUNT'] = lightsNumbers[lightType];
    }
    return shaderStr.replace(loopRegex, replace);
}
&nbsp;
function getDefineCode(defines, lightsNumbers, enabledTextures) {
    var defineStr = [];
    if (lightsNumbers) {
        for (var lightType in lightsNumbers) {
            var count = lightsNumbers[lightType];
            <span class="missing-if-branch" title="else path not taken" >E</span>if (count &gt; 0) {
                defineStr.push('#define ' + lightType.toUpperCase() + '_COUNT ' + count);
            }
        }
    }
    <span class="missing-if-branch" title="else path not taken" >E</span>if (enabledTextures) {
        for (var i = 0; i &lt; enabledTextures.length; i++) {
            var symbol = enabledTextures[i];
            defineStr.push('#define ' + symbol.toUpperCase() + '_ENABLED');
        }
    }
    // Custom Defines
    for (var symbol in defines) {
        var value = defines[symbol];
        if (value === null) {
            defineStr.push('#define ' + symbol);
        }
        else{
            defineStr.push('#define ' + symbol + ' ' + value.toString());
        }
    }
    return defineStr.join('\n');
}
&nbsp;
function getExtensionCode(exts) {
    // Extension declaration must before all non-preprocessor codes
    // TODO vertex ? extension enum ?
    var extensionStr = [];
    for (var i = 0; i &lt; exts.length; i++) {
        extensionStr.push('#extension GL_' + exts[i] + ' : enable');
    }
    return extensionStr.join('\n');
}
&nbsp;
function getPrecisionCode(precision) {
    return ['precision', precision, 'float'].join(' ') + ';\n'
        + ['precision', precision, 'int'].join(' ') + ';\n'
        // depth texture may have precision problem on iOS device.
        + ['precision', precision, 'sampler2D'].join(' ') + ';\n';
}
&nbsp;
function ProgramManager(renderer) {
    this._renderer = renderer;
    this._cache = {};
}
&nbsp;
ProgramManager.prototype.getProgram = function (renderable, material, scene) {
    var cache = this._cache;
&nbsp;
    var key = 's' + material.shader.shaderID + 'm' + material.getProgramKey();
    if (scene) {
        key += 'se' + scene.getProgramKey(renderable.lightGroup);
    }
    <span class="missing-if-branch" title="if path not taken" >I</span>if (renderable.isSkinnedMesh()) {
<span class="cstat-no" title="statement not covered" >        key += ',' + renderable.joints.length;</span>
    }
    var program = cache[key];
&nbsp;
    if (program) {
        return program;
    }
&nbsp;
    var lightsNumbers = scene ? scene.getLightsNumbers(renderable.lightGroup) : {};
    var renderer = this._renderer;
    var _gl = renderer.gl;
    var enabledTextures = material.getEnabledTextures();
    var skinDefineCode = '';
    <span class="missing-if-branch" title="if path not taken" >I</span>if (renderable.isSkinnedMesh()) {
        // TODO Add skinning code?
<span class="cstat-no" title="statement not covered" >        skinDefineCode = '\n' + getDefineCode({</span>
            SKINNING: null,
            JOINT_COUNT: renderable.joints.length
        }) + '\n';
    }
    // TODO Optimize key generation
    // VERTEX
    var vertexDefineStr = skinDefineCode + getDefineCode(material.vertexDefines, lightsNumbers, enabledTextures);
    // FRAGMENT
    var fragmentDefineStr = skinDefineCode + getDefineCode(material.fragmentDefines, lightsNumbers, enabledTextures);
&nbsp;
    var vertexCode = vertexDefineStr + '\n' + material.shader.vertex;
    var fragmentCode = getExtensionCode([
            // TODO Not hard coded
            'OES_standard_derivatives',
            'EXT_shader_texture_lod'
        ]) + '\n'
            + getPrecisionCode(material.precision) + '\n'
            + fragmentDefineStr + '\n' + material.shader.fragment;
&nbsp;
    var finalVertexCode = unrollLoop(vertexCode, material.vertexDefines, lightsNumbers);
    var finalFragmentCode = unrollLoop(fragmentCode, material.fragmentDefines, lightsNumbers);
&nbsp;
    var program = new GLProgram();
    program.uniformSemantics = material.shader.uniformSemantics;
    program.attributes = material.shader.attributes;
    var errorMsg = program.buildProgram(_gl, material.shader, finalVertexCode, finalFragmentCode);
    program.__error = errorMsg;
&nbsp;
    cache[key] = program;
&nbsp;
    return program;
};
&nbsp;
export default ProgramManager;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
