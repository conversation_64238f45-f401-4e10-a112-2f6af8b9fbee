<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/gpu/GLProgram.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/gpu/</a> GLProgram.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">76.88% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>123/160</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">65.22% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>45/69</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">92.31% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>12/13</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">76.88% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>123/160</span>
      </div>
    </div>
  </div>
  <div class='status-line medium'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">48×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">66×</span>
<span class="cline-any cline-yes">66×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">103×</span>
<span class="cline-any cline-yes">103×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">103×</span>
<span class="cline-any cline-yes">102×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">303×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">76×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">103×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">103×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">103×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">103×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">884×</span>
<span class="cline-any cline-yes">884×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">884×</span>
<span class="cline-any cline-yes">290×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">594×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">232×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">232×</span>
<span class="cline-any cline-yes">3712×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">232×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">232×</span>
<span class="cline-any cline-yes">232×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">39×</span>
<span class="cline-any cline-yes">39×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">50×</span>
<span class="cline-any cline-yes">50×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">19×</span>
<span class="cline-any cline-yes">19×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">100×</span>
<span class="cline-any cline-yes">100×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">114×</span>
<span class="cline-any cline-yes">114×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">22×</span>
<span class="cline-any cline-yes">22×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">32×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">594×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">455×</span>
<span class="cline-any cline-yes">455×</span>
<span class="cline-any cline-yes">11×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">444×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">36×</span>
<span class="cline-any cline-yes">36×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">36×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">36×</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">36×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">36×</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">36×</span>
<span class="cline-any cline-yes">36×</span>
<span class="cline-any cline-yes">84×</span>
<span class="cline-any cline-yes">84×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">84×</span>
<span class="cline-any cline-yes">84×</span>
<span class="cline-any cline-yes">52×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">52×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">46×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">78×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">78×</span>
<span class="cline-any cline-yes">78×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">36×</span>
<span class="cline-any cline-yes">116×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">78×</span>
<span class="cline-any cline-yes">78×</span>
<span class="cline-any cline-yes">78×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">36×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-yes">842×</span>
<span class="cline-any cline-yes">842×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-yes">24×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import vendor from '../core/vendor';
import Base from '../core/Base';
&nbsp;
var SHADER_STATE_TO_ENABLE = 1;
var SHADER_STATE_KEEP_ENABLE = 2;
var SHADER_STATE_PENDING = 3;
&nbsp;
// Enable attribute operation is global to all programs
// Here saved the list of all enabled attribute index
// http://www.mjbshaw.com/2013/03/webgl-fixing-invalidoperation.html
var enabledAttributeList = {};
&nbsp;
// some util functions
function addLineNumbers(string) <span class="fstat-no" title="function not covered" >{</span>
    var chunks = <span class="cstat-no" title="statement not covered" >string.split('\n');</span>
<span class="cstat-no" title="statement not covered" >    for (var i = 0, il = chunks.length; i &lt; il; i ++) {</span>
        // Chrome reports shader errors on lines
        // starting counting from 1
<span class="cstat-no" title="statement not covered" >        chunks[i] = (i + 1) + ': ' + chunks[i];</span>
    }
<span class="cstat-no" title="statement not covered" >    return chunks.join('\n');</span>
}
&nbsp;
// Return true or error msg if error happened
function checkShaderErrorMsg(_gl, shader, shaderString) {
    <span class="missing-if-branch" title="if path not taken" >I</span>if (!_gl.getShaderParameter(shader, _gl.COMPILE_STATUS)) {
<span class="cstat-no" title="statement not covered" >        return [_gl.getShaderInfoLog(shader), addLineNumbers(shaderString)].join('\n');</span>
    }
}
&nbsp;
var tmpFloat32Array16 = new vendor.Float32Array(16);
&nbsp;
var GLProgram = Base.extend({
&nbsp;
    uniformSemantics: {},
    attributes: {}
&nbsp;
}, function () {
    this._locations = {};
&nbsp;
    this._textureSlot = 0;
&nbsp;
    this._program = null;
}, {
&nbsp;
    bind: function (renderer) {
        this._textureSlot = 0;
        renderer.gl.useProgram(this._program);
    },
&nbsp;
    hasUniform: function (symbol) {
        var location = this._locations[symbol];
        return location !== null &amp;&amp; location !== undefined;
    },
&nbsp;
    useTextureSlot: function (renderer, texture, slot) {
        <span class="missing-if-branch" title="else path not taken" >E</span>if (texture) {
            renderer.gl.activeTexture(renderer.gl.TEXTURE0 + slot);
            // Maybe texture is not loaded yet;
            if (texture.isRenderable()) {
                texture.bind(renderer);
            }
            else {
                // Bind texture to null
                texture.unbind(renderer);
            }
        }
    },
&nbsp;
    currentTextureSlot: function () {
        return this._textureSlot;
    },
&nbsp;
    resetTextureSlot: function (slot) {
        this._textureSlot = slot || 0;
    },
&nbsp;
    takeCurrentTextureSlot: function (renderer, texture) {
        var textureSlot = this._textureSlot;
&nbsp;
        this.useTextureSlot(renderer, texture, textureSlot);
&nbsp;
        this._textureSlot++;
&nbsp;
        return textureSlot;
    },
&nbsp;
    setUniform: function (_gl, type, symbol, value) {
        var locationMap = this._locations;
        var location = locationMap[symbol];
        // Uniform is not existed in the shader
        if (location === null || location === undefined) {
            return false;
        }
&nbsp;
        switch (type) {
            case 'm4':
                <span class="missing-if-branch" title="else path not taken" >E</span>if (!(value instanceof Float32Array)) {
                    // Use Float32Array is much faster than array when uniformMatrix4fv.
                    for (var i = 0; i &lt; value.length; i++) {
                        tmpFloat32Array16[i] = value[i];
                    }
                    value = tmpFloat32Array16;
                }
                _gl.uniformMatrix4fv(location, false, value);
                break;
<span class="branch-1 cbranch-no" title="branch not covered" >            case '2i':</span>
<span class="cstat-no" title="statement not covered" >                _gl.uniform2i(location, value[0], value[1]);</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
            case '2f':
                _gl.uniform2f(location, value[0], value[1]);
                break;
<span class="branch-3 cbranch-no" title="branch not covered" >            case '3i':</span>
<span class="cstat-no" title="statement not covered" >                _gl.uniform3i(location, value[0], value[1], value[2]);</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
            case '3f':
                _gl.uniform3f(location, value[0], value[1], value[2]);
                break;
<span class="branch-5 cbranch-no" title="branch not covered" >            case '4i':</span>
<span class="cstat-no" title="statement not covered" >                _gl.uniform4i(location, value[0], value[1], value[2], value[3]);</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
            case '4f':
                _gl.uniform4f(location, value[0], value[1], value[2], value[3]);
                break;
            case '1i':
                _gl.uniform1i(location, value);
                break;
            case '1f':
                _gl.uniform1f(location, value);
                break;
            case '1fv':
                _gl.uniform1fv(location, value);
                break;
            case '1iv':
                _gl.uniform1iv(location, value);
                break;
<span class="branch-11 cbranch-no" title="branch not covered" >            case '2iv':</span>
<span class="cstat-no" title="statement not covered" >                _gl.uniform2iv(location, value);</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
            case '2fv':
                _gl.uniform2fv(location, value);
                break;
<span class="branch-13 cbranch-no" title="branch not covered" >            case '3iv':</span>
<span class="cstat-no" title="statement not covered" >                _gl.uniform3iv(location, value);</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
            case '3fv':
                _gl.uniform3fv(location, value);
                break;
<span class="branch-15 cbranch-no" title="branch not covered" >            case '4iv':</span>
<span class="cstat-no" title="statement not covered" >                _gl.uniform4iv(location, value);</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
<span class="branch-16 cbranch-no" title="branch not covered" >            case '4fv':</span>
<span class="cstat-no" title="statement not covered" >                _gl.uniform4fv(location, value);</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
<span class="branch-17 cbranch-no" title="branch not covered" >            case 'm2':</span>
<span class="branch-18 cbranch-no" title="branch not covered" >            case 'm2v':</span>
<span class="cstat-no" title="statement not covered" >                _gl.uniformMatrix2fv(location, false, value);</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
<span class="branch-19 cbranch-no" title="branch not covered" >            case 'm3':</span>
<span class="branch-20 cbranch-no" title="branch not covered" >            case 'm3v':</span>
<span class="cstat-no" title="statement not covered" >                _gl.uniformMatrix3fv(location, false, value);</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
            case 'm4v':
                // Raw value
                <span class="missing-if-branch" title="else path not taken" >E</span>if (Array.isArray(value) &amp;&amp; Array.isArray(value[0])) {
                    var array = new vendor.Float32Array(value.length * 16);
                    var cursor = 0;
                    for (var i = 0; i &lt; value.length; i++) {
                        var item = value[i];
                        for (var j = 0; j &lt; 16; j++) {
                            array[cursor++] = item[j];
                        }
                    }
                    _gl.uniformMatrix4fv(location, false, array);
                }
                else {   // ArrayBufferView
<span class="cstat-no" title="statement not covered" >                    _gl.uniformMatrix4fv(location, false, value);</span>
                }
                break;
        }
        return true;
    },
&nbsp;
    setUniformOfSemantic: function (_gl, semantic, val) {
        var semanticInfo = this.uniformSemantics[semantic];
        if (semanticInfo) {
            return this.setUniform(_gl, semanticInfo.type, semanticInfo.symbol, val);
        }
        return false;
    },
&nbsp;
    // Used for creating VAO
    // Enable the attributes passed in and disable the rest
    // Example Usage:
    // enableAttributes(renderer, ["position", "texcoords"])
    enableAttributes: function (renderer, attribList, vao) {
        var _gl = renderer.gl;
        var program = this._program;
&nbsp;
        var locationMap = this._locations;
&nbsp;
        var enabledAttributeListInContext;
        if (vao) {
            enabledAttributeListInContext = vao.__enabledAttributeList;
        }
        else {
            enabledAttributeListInContext = enabledAttributeList[renderer.__uid__];
        }
        <span class="missing-if-branch" title="else path not taken" >E</span>if (!enabledAttributeListInContext) {
            // In vertex array object context
            // PENDING Each vao object needs to enable attributes again?
            if (vao) {
                enabledAttributeListInContext
                    = vao.__enabledAttributeList
                    = [];
            }
            else {
                enabledAttributeListInContext
                    = enabledAttributeList[renderer.__uid__]
                    = [];
            }
        }
        var locationList = [];
        for (var i = 0; i &lt; attribList.length; i++) {
            var symbol = attribList[i];
            <span class="missing-if-branch" title="if path not taken" >I</span>if (!this.attributes[symbol]) {
<span class="cstat-no" title="statement not covered" >                locationList[i] = -1;</span>
<span class="cstat-no" title="statement not covered" >                continue;</span>
            }
            var location = locationMap[symbol];
            if (location == null) {
                location = _gl.getAttribLocation(program, symbol);
                // Attrib location is a number from 0 to ...
                if (location === -1) {
                    locationList[i] = -1;
                    continue;
                }
                locationMap[symbol] = location;
            }
            locationList[i] = location;
&nbsp;
            <span class="missing-if-branch" title="else path not taken" >E</span>if (!enabledAttributeListInContext[location]) {
                enabledAttributeListInContext[location] = SHADER_STATE_TO_ENABLE;
            }
            else {
<span class="cstat-no" title="statement not covered" >                enabledAttributeListInContext[location] = SHADER_STATE_KEEP_ENABLE;</span>
            }
        }
&nbsp;
        for (var i = 0; i &lt; enabledAttributeListInContext.length; i++) {
            switch(enabledAttributeListInContext[i]){
                case SHADER_STATE_TO_ENABLE:
                    _gl.enableVertexAttribArray(i);
                    enabledAttributeListInContext[i] = SHADER_STATE_PENDING;
                    break;
<span class="branch-1 cbranch-no" title="branch not covered" >                case SHADER_STATE_KEEP_ENABLE:</span>
<span class="cstat-no" title="statement not covered" >                    enabledAttributeListInContext[i] = SHADER_STATE_PENDING;</span>
<span class="cstat-no" title="statement not covered" >                    break;</span>
                // Expired
<span class="branch-2 cbranch-no" title="branch not covered" >                case SHADER_STATE_PENDING:</span>
<span class="cstat-no" title="statement not covered" >                    _gl.disableVertexAttribArray(i);</span>
<span class="cstat-no" title="statement not covered" >                    enabledAttributeListInContext[i] = 0;</span>
<span class="cstat-no" title="statement not covered" >                    break;</span>
            }
        }
&nbsp;
        return locationList;
    },
&nbsp;
    buildProgram: function (_gl, shader, vertexShaderCode, fragmentShaderCode) {
        var vertexShader = _gl.createShader(_gl.VERTEX_SHADER);
        var program = _gl.createProgram();
&nbsp;
        _gl.shaderSource(vertexShader, vertexShaderCode);
        _gl.compileShader(vertexShader);
&nbsp;
        var fragmentShader = _gl.createShader(_gl.FRAGMENT_SHADER);
        _gl.shaderSource(fragmentShader, fragmentShaderCode);
        _gl.compileShader(fragmentShader);
&nbsp;
        var msg = checkShaderErrorMsg(_gl, vertexShader, vertexShaderCode);
        <span class="missing-if-branch" title="if path not taken" >I</span>if (msg) {
<span class="cstat-no" title="statement not covered" >            return msg;</span>
        }
        msg = checkShaderErrorMsg(_gl, fragmentShader, fragmentShaderCode);
        <span class="missing-if-branch" title="if path not taken" >I</span>if (msg) {
<span class="cstat-no" title="statement not covered" >            return msg;</span>
        }
&nbsp;
        _gl.attachShader(program, vertexShader);
        _gl.attachShader(program, fragmentShader);
        // Force the position bind to location 0;
        <span class="missing-if-branch" title="else path not taken" >E</span>if (shader.attributeSemantics['POSITION']) {
            _gl.bindAttribLocation(program, 0, shader.attributeSemantics['POSITION'].symbol);
        }
        else {
            // Else choose an attribute and bind to location 0;
            var keys = <span class="cstat-no" title="statement not covered" >Object.keys(this.attributes);</span>
<span class="cstat-no" title="statement not covered" >            _gl.bindAttribLocation(program, 0, keys[0]);</span>
        }
&nbsp;
        _gl.linkProgram(program);
&nbsp;
        <span class="missing-if-branch" title="if path not taken" >I</span>if (!_gl.getProgramParameter(program, _gl.LINK_STATUS)) {
<span class="cstat-no" title="statement not covered" >            return 'Could not link program\n' + 'VALIDATE_STATUS: ' + _gl.getProgramParameter(program, _gl.VALIDATE_STATUS) + ', gl error [' + _gl.getError() + ']';</span>
        }
&nbsp;
        // Cache uniform locations
        for (var i = 0; i &lt; shader.uniforms.length; i++) {
            var uniformSymbol = shader.uniforms[i];
            this._locations[uniformSymbol] = _gl.getUniformLocation(program, uniformSymbol);
        }
&nbsp;
        _gl.deleteShader(vertexShader);
        _gl.deleteShader(fragmentShader);
&nbsp;
        this._program = program;
&nbsp;
        // Save code.
        this.vertexCode = vertexShaderCode;
        this.fragmentCode = fragmentShaderCode;
    }
});
&nbsp;
export default GLProgram;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
