<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/util/dds.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/util/</a> dds.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">27.94% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>19/68</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/16</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">50% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>1/2</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">27.94% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>19/68</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import Texture from '../Texture';
import Texture2D from '../Texture2D';
import TextureCube from '../TextureCube';
&nbsp;
// http://msdn.microsoft.com/en-us/library/windows/desktop/bb943991(v=vs.85).aspx
// https://github.com/toji/webgl-texture-utils/blob/master/texture-util/dds.js
var DDS_MAGIC = 0x20534444;
&nbsp;
var DDSD_CAPS = 0x1;
var DDSD_HEIGHT = 0x2;
var DDSD_WIDTH = 0x4;
var DDSD_PITCH = 0x8;
var DDSD_PIXELFORMAT = 0x1000;
var DDSD_MIPMAPCOUNT = 0x20000;
var DDSD_LINEARSIZE = 0x80000;
var DDSD_DEPTH = 0x800000;
&nbsp;
var DDSCAPS_COMPLEX = 0x8;
var DDSCAPS_MIPMAP = 0x400000;
var DDSCAPS_TEXTURE = 0x1000;
&nbsp;
var DDSCAPS2_CUBEMAP = 0x200;
var DDSCAPS2_CUBEMAP_POSITIVEX = 0x400;
var DDSCAPS2_CUBEMAP_NEGATIVEX = 0x800;
var DDSCAPS2_CUBEMAP_POSITIVEY = 0x1000;
var DDSCAPS2_CUBEMAP_NEGATIVEY = 0x2000;
var DDSCAPS2_CUBEMAP_POSITIVEZ = 0x4000;
var DDSCAPS2_CUBEMAP_NEGATIVEZ = 0x8000;
var DDSCAPS2_VOLUME = 0x200000;
&nbsp;
var DDPF_ALPHAPIXELS = 0x1;
var DDPF_ALPHA = 0x2;
var DDPF_FOURCC = 0x4;
var DDPF_RGB = 0x40;
var DDPF_YUV = 0x200;
var DDPF_LUMINANCE = 0x20000;
&nbsp;
function fourCCToInt32(value) {
    return value.charCodeAt(0) +
        (value.charCodeAt(1) &lt;&lt; 8) +
        (value.charCodeAt(2) &lt;&lt; 16) +
        (value.charCodeAt(3) &lt;&lt; 24);
}
&nbsp;
function int32ToFourCC(value) {
    return String.fromCharCode(
        value &amp; 0xff,
        (value &gt;&gt; 8) &amp; 0xff,
        (value &gt;&gt; 16) &amp; 0xff,
        (value &gt;&gt; 24) &amp; 0xff
    );
}
&nbsp;
var headerLengthInt = 31; // The header length in 32 bit ints
&nbsp;
var FOURCC_DXT1 = fourCCToInt32('DXT1');
var FOURCC_DXT3 = fourCCToInt32('DXT3');
var FOURCC_DXT5 = fourCCToInt32('DXT5');
// Offsets into the header array
var off_magic = 0;
&nbsp;
var off_size = 1;
var off_flags = 2;
var off_height = 3;
var off_width = 4;
&nbsp;
var off_mipmapCount = 7;
&nbsp;
var off_pfFlags = 20;
var off_pfFourCC = 21;
&nbsp;
var off_caps = 27;
var off_caps2 = 28;
var off_caps3 = 29;
var off_caps4 = 30;
&nbsp;
var ret = {
    parse: function(arrayBuffer, out) <span class="fstat-no" title="function not covered" >{</span>
        var header = <span class="cstat-no" title="statement not covered" >new Int32Array(arrayBuffer, 0, headerLengthInt);</span>
<span class="cstat-no" title="statement not covered" >        if (header[off_magic] !== DDS_MAGIC) {</span>
<span class="cstat-no" title="statement not covered" >            return null;</span>
        }
<span class="cstat-no" title="statement not covered" >        if (!header(off_pfFlags) &amp; DDPF_FOURCC) {</span>
<span class="cstat-no" title="statement not covered" >            return null;</span>
        }
&nbsp;
        var fourCC = <span class="cstat-no" title="statement not covered" >header(off_pfFourCC);</span>
        var width = <span class="cstat-no" title="statement not covered" >header[off_width];</span>
        var height = <span class="cstat-no" title="statement not covered" >header[off_height];</span>
        var isCubeMap = <span class="cstat-no" title="statement not covered" >header[off_caps2] &amp; DDSCAPS2_CUBEMAP;</span>
        var hasMipmap = <span class="cstat-no" title="statement not covered" >header[off_flags] &amp; DDSD_MIPMAPCOUNT;</span>
        var blockBytes, internalFormat;
<span class="cstat-no" title="statement not covered" >        switch(fourCC) {</span>
            case FOURCC_DXT1:
<span class="cstat-no" title="statement not covered" >                blockBytes = 8;</span>
<span class="cstat-no" title="statement not covered" >                internalFormat = Texture.COMPRESSED_RGB_S3TC_DXT1_EXT;</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
            case FOURCC_DXT3:
<span class="cstat-no" title="statement not covered" >                blockBytes = 16;</span>
<span class="cstat-no" title="statement not covered" >                internalFormat = Texture.COMPRESSED_RGBA_S3TC_DXT3_EXT;</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
            case FOURCC_DXT5:
<span class="cstat-no" title="statement not covered" >                blockBytes = 16;</span>
<span class="cstat-no" title="statement not covered" >                internalFormat = Texture.COMPRESSED_RGBA_S3TC_DXT5_EXT;</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
            default:
<span class="cstat-no" title="statement not covered" >                return null;</span>
        }
        var dataOffset = <span class="cstat-no" title="statement not covered" >header[off_size] + 4;</span>
        // TODO: Suppose all face are existed
        var faceNumber = <span class="cstat-no" title="statement not covered" >isCubeMap ? 6 : 1;</span>
        var mipmapCount = <span class="cstat-no" title="statement not covered" >1;</span>
<span class="cstat-no" title="statement not covered" >        if (hasMipmap) {</span>
<span class="cstat-no" title="statement not covered" >            mipmapCount = Math.max(1, header[off_mipmapCount]);</span>
        }
&nbsp;
        var textures = <span class="cstat-no" title="statement not covered" >[];</span>
<span class="cstat-no" title="statement not covered" >        for (var f = 0; f &lt; faceNumber; f++) {</span>
            var _width = <span class="cstat-no" title="statement not covered" >width;</span>
            var _height = <span class="cstat-no" title="statement not covered" >height;</span>
<span class="cstat-no" title="statement not covered" >            textures[f] = new Texture2D({</span>
                width: _width,
                height: _height,
                format: internalFormat
            });
            var mipmaps = <span class="cstat-no" title="statement not covered" >[];</span>
<span class="cstat-no" title="statement not covered" >            for (var i = 0; i &lt; mipmapCount; i++) {</span>
                var dataLength = <span class="cstat-no" title="statement not covered" >Math.max(4, _width) / 4 * Math.max(4, _height) / 4 * blockBytes;</span>
                var byteArray = <span class="cstat-no" title="statement not covered" >new Uint8Array(arrayBuffer, dataOffset, dataLength);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                dataOffset += dataLength;</span>
<span class="cstat-no" title="statement not covered" >                _width *= 0.5;</span>
<span class="cstat-no" title="statement not covered" >                _height *= 0.5;</span>
<span class="cstat-no" title="statement not covered" >                mipmaps[i] = byteArray;</span>
            }
<span class="cstat-no" title="statement not covered" >            textures[f].pixels = mipmaps[0];</span>
<span class="cstat-no" title="statement not covered" >            if (hasMipmap) {</span>
<span class="cstat-no" title="statement not covered" >                textures[f].mipmaps = mipmaps;</span>
            }
        }
        // TODO
        // return isCubeMap ? textures : textures[0];
<span class="cstat-no" title="statement not covered" >        if (out) {</span>
<span class="cstat-no" title="statement not covered" >            out.width = textures[0].width;</span>
<span class="cstat-no" title="statement not covered" >            out.height = textures[0].height;</span>
<span class="cstat-no" title="statement not covered" >            out.format = textures[0].format;</span>
<span class="cstat-no" title="statement not covered" >            out.pixels = textures[0].pixels;</span>
<span class="cstat-no" title="statement not covered" >            out.mipmaps = textures[0].mipmaps;</span>
        }
        else {
<span class="cstat-no" title="statement not covered" >            return textures[0];</span>
        }
    }
};
&nbsp;
export default ret;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
