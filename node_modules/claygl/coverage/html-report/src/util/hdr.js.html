<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/util/hdr.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/util/</a> hdr.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">70.75% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>75/106</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">60% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>21/35</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">57.14% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>4/7</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">72.12% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>75/104</span>
      </div>
    </div>
  </div>
  <div class='status-line medium'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">524288×</span>
<span class="cline-any cline-yes">468143×</span>
<span class="cline-any cline-yes">468143×</span>
<span class="cline-any cline-yes">468143×</span>
<span class="cline-any cline-yes">468143×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">56145×</span>
<span class="cline-any cline-yes">56145×</span>
<span class="cline-any cline-yes">56145×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">524288×</span>
<span class="cline-any cline-yes">524288×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">512×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">512×</span>
<span class="cline-any cline-yes">512×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">512×</span>
<span class="cline-any cline-yes">512×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">512×</span>
<span class="cline-any cline-yes">512×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">512×</span>
<span class="cline-any cline-yes">2048×</span>
<span class="cline-any cline-yes">83718×</span>
<span class="cline-any cline-yes">83718×</span>
<span class="cline-any cline-yes">50288×</span>
<span class="cline-any cline-yes">50288×</span>
<span class="cline-any cline-yes">50288×</span>
<span class="cline-any cline-yes">827515×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">33430×</span>
<span class="cline-any cline-yes">1269637×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">512×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">60×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">15×</span>
<span class="cline-any cline-yes">15×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">14×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1024×</span>
<span class="cline-any cline-yes">1024×</span>
<span class="cline-any cline-yes">4096×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">512×</span>
<span class="cline-any cline-yes">512×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">512×</span>
<span class="cline-any cline-yes">524288×</span>
<span class="cline-any cline-yes">524288×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import Texture from '../Texture';
import Texture2D from '../Texture2D';
var toChar = String.fromCharCode;
&nbsp;
var MINELEN = 8;
var MAXELEN = 0x7fff;
function rgbe2float(rgbe, buffer, offset, exposure) {
    if (rgbe[3] &gt; 0) {
        var f = Math.pow(2.0, rgbe[3] - 128 - 8 + exposure);
        buffer[offset + 0] = rgbe[0] * f;
        buffer[offset + 1] = rgbe[1] * f;
        buffer[offset + 2] = rgbe[2] * f;
    }
    else {
        buffer[offset + 0] = 0;
        buffer[offset + 1] = 0;
        buffer[offset + 2] = 0;
    }
    buffer[offset + 3] = 1.0;
    return buffer;
}
&nbsp;
function uint82string(array, offset, size) {
    var str = '';
    for (var i = offset; i &lt; size; i++) {
        str += toChar(array[i]);
    }
    return str;
}
&nbsp;
function copyrgbe(s, t) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    t[0] = s[0];</span>
<span class="cstat-no" title="statement not covered" >    t[1] = s[1];</span>
<span class="cstat-no" title="statement not covered" >    t[2] = s[2];</span>
<span class="cstat-no" title="statement not covered" >    t[3] = s[3];</span>
}
&nbsp;
// TODO : check
function oldReadColors(scan, buffer, offset, xmax) <span class="fstat-no" title="function not covered" >{</span>
    var rshift = <span class="cstat-no" title="statement not covered" >0,</span> x = <span class="cstat-no" title="statement not covered" >0,</span> len = <span class="cstat-no" title="statement not covered" >xmax;</span>
<span class="cstat-no" title="statement not covered" >    while (len &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >        scan[x][0] = buffer[offset++];</span>
<span class="cstat-no" title="statement not covered" >        scan[x][1] = buffer[offset++];</span>
<span class="cstat-no" title="statement not covered" >        scan[x][2] = buffer[offset++];</span>
<span class="cstat-no" title="statement not covered" >        scan[x][3] = buffer[offset++];</span>
<span class="cstat-no" title="statement not covered" >        if (scan[x][0] === 1 &amp;&amp; scan[x][1] === 1 &amp;&amp; scan[x][2] === 1) {</span>
            // exp is count of repeated pixels
<span class="cstat-no" title="statement not covered" >            for (var i = (scan[x][3] &lt;&lt; rshift) &gt;&gt;&gt; 0; i &gt; 0; i--) {</span>
<span class="cstat-no" title="statement not covered" >                copyrgbe(scan[x-1], scan[x]);</span>
<span class="cstat-no" title="statement not covered" >                x++;</span>
<span class="cstat-no" title="statement not covered" >                len--;</span>
            }
<span class="cstat-no" title="statement not covered" >            rshift += 8;</span>
        } else {
<span class="cstat-no" title="statement not covered" >            x++;</span>
<span class="cstat-no" title="statement not covered" >            len--;</span>
<span class="cstat-no" title="statement not covered" >            rshift = 0;</span>
        }
    }
<span class="cstat-no" title="statement not covered" >    return offset;</span>
}
&nbsp;
function readColors(scan, buffer, offset, xmax) {
    <span class="missing-if-branch" title="if path not taken" >I</span>if ((xmax &lt; MINELEN) | (xmax &gt; MAXELEN)) {
<span class="cstat-no" title="statement not covered" >        return oldReadColors(scan, buffer, offset, xmax);</span>
    }
    var i = buffer[offset++];
    <span class="missing-if-branch" title="if path not taken" >I</span>if (i != 2) {
<span class="cstat-no" title="statement not covered" >        return oldReadColors(scan, buffer, offset - 1, xmax);</span>
    }
    scan[0][1] = buffer[offset++];
    scan[0][2] = buffer[offset++];
&nbsp;
    i = buffer[offset++];
    <span class="missing-if-branch" title="if path not taken" >I</span>if ((((scan[0][2] &lt;&lt; 8) &gt;&gt;&gt; 0) | i) &gt;&gt;&gt; 0 !== xmax) {
<span class="cstat-no" title="statement not covered" >        return null;</span>
    }
    for (var i = 0; i &lt; 4; i++) {
        for (var x = 0; x &lt; xmax;) {
            var code = buffer[offset++];
            if (code &gt; 128) {
                code = (code &amp; 127) &gt;&gt;&gt; 0;
                var val = buffer[offset++];
                while (code--) {
                    scan[x++][i] = val;
                }
            } else {
                while (code--) {
                    scan[x++][i] = buffer[offset++];
                }
            }
        }
    }
    return offset;
}
&nbsp;
&nbsp;
var ret = {
    // http://www.graphics.cornell.edu/~bjw/rgbe.html
    // Blender source
    // http://radsite.lbl.gov/radiance/refer/Notes/picture_format.html
    parseRGBE: function(arrayBuffer, texture, exposure) {
        <span class="missing-if-branch" title="if path not taken" >I</span>if (exposure == null) {
<span class="cstat-no" title="statement not covered" >            exposure = 0;</span>
        }
        var data = new Uint8Array(arrayBuffer);
        var size = data.length;
        <span class="missing-if-branch" title="if path not taken" >I</span>if (uint82string(data, 0, 2) !== '#?') {
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
        // find empty line, next line is resolution info
        for (var i = 2; i &lt; size; i++) {
            if (toChar(data[i]) === '\n' &amp;&amp; toChar(data[i+1]) === '\n') {
                break;
            }
        }
        <span class="missing-if-branch" title="if path not taken" >I</span>if (i &gt;= size) { // not found
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
        // find resolution info line
        i += 2;
        var str = '';
        for (; i &lt; size; i++) {
            var _char = toChar(data[i]);
            if (_char === '\n') {
                break;
            }
            str += _char;
        }
        // -Y M +X N
        var tmp = str.split(' ');
        var height = parseInt(tmp[1]);
        var width = parseInt(tmp[3]);
        <span class="missing-if-branch" title="if path not taken" >I</span>if (!width || !height) {
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
&nbsp;
        // read and decode actual data
        var offset = i+1;
        var scanline = [];
        // memzero
        for (var x = 0; x &lt; width; x++) {
            scanline[x] = [];
            for (var j = 0; j &lt; 4; j++) {
                scanline[x][j] = 0;
            }
        }
        var pixels = new Float32Array(width * height * 4);
        var offset2 = 0;
        for (var y = 0; y &lt; height; y++) {
            var offset = readColors(scanline, data, offset, width);
            <span class="missing-if-branch" title="if path not taken" >I</span>if (!offset) {
<span class="cstat-no" title="statement not covered" >                return null;</span>
            }
            for (var x = 0; x &lt; width; x++) {
                rgbe2float(scanline[x], pixels, offset2, exposure);
                offset2 += 4;
            }
        }
&nbsp;
        <span class="missing-if-branch" title="if path not taken" >I</span>if (!texture) {
<span class="cstat-no" title="statement not covered" >            texture = new Texture2D();</span>
        }
        texture.width = width;
        texture.height = height;
        texture.pixels = pixels;
        // HALF_FLOAT can't use Float32Array
        texture.type = Texture.FLOAT;
        return texture;
    },
&nbsp;
    parseRGBEFromPNG: function(png) <span class="fstat-no" title="function not covered" >{</span>
&nbsp;
    }
};
&nbsp;
export default ret;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
