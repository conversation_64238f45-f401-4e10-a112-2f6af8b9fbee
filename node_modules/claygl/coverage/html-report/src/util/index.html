<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/util/</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> src/util/
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">31.6% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>268/848</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">25.15% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>82/326</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">42.5% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>17/40</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">31.83% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>268/842</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="cubemap.js"><a href="cubemap.js.html">cubemap.js</a></td>
	<td data-value="96.91" class="pic high"><div class="chart"><div class="cover-fill" style="width: 96%;"></div><div class="cover-empty" style="width:4%;"></div></div></td>
	<td data-value="96.91" class="pct high">96.91%</td>
	<td data-value="97" class="abs high">94/97</td>
	<td data-value="71.88" class="pct medium">71.88%</td>
	<td data-value="32" class="abs medium">23/32</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="96.91" class="pct high">96.91%</td>
	<td data-value="97" class="abs high">94/97</td>
	</tr>

<tr>
	<td class="file low" data-value="dds.js"><a href="dds.js.html">dds.js</a></td>
	<td data-value="27.94" class="pic low"><div class="chart"><div class="cover-fill" style="width: 27%;"></div><div class="cover-empty" style="width:73%;"></div></div></td>
	<td data-value="27.94" class="pct low">27.94%</td>
	<td data-value="68" class="abs low">19/68</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="16" class="abs low">0/16</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="2" class="abs medium">1/2</td>
	<td data-value="27.94" class="pct low">27.94%</td>
	<td data-value="68" class="abs low">19/68</td>
	</tr>

<tr>
	<td class="file low" data-value="delaunay.js"><a href="delaunay.js.html">delaunay.js</a></td>
	<td data-value="1.65" class="pic low"><div class="chart"><div class="cover-fill" style="width: 1%;"></div><div class="cover-empty" style="width:99%;"></div></div></td>
	<td data-value="1.65" class="pct low">1.65%</td>
	<td data-value="121" class="abs low">2/121</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="62" class="abs low">0/62</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	<td data-value="1.71" class="pct low">1.71%</td>
	<td data-value="117" class="abs low">2/117</td>
	</tr>

<tr>
	<td class="file medium" data-value="hdr.js"><a href="hdr.js.html">hdr.js</a></td>
	<td data-value="70.75" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 70%;"></div><div class="cover-empty" style="width:30%;"></div></div></td>
	<td data-value="70.75" class="pct medium">70.75%</td>
	<td data-value="106" class="abs medium">75/106</td>
	<td data-value="60" class="pct medium">60%</td>
	<td data-value="35" class="abs medium">21/35</td>
	<td data-value="57.14" class="pct medium">57.14%</td>
	<td data-value="7" class="abs medium">4/7</td>
	<td data-value="72.12" class="pct medium">72.12%</td>
	<td data-value="104" class="abs medium">75/104</td>
	</tr>

<tr>
	<td class="file low" data-value="mesh.js"><a href="mesh.js.html">mesh.js</a></td>
	<td data-value="1.6" class="pic low"><div class="chart"><div class="cover-fill" style="width: 1%;"></div><div class="cover-empty" style="width:99%;"></div></div></td>
	<td data-value="1.6" class="pct low">1.6%</td>
	<td data-value="187" class="abs low">3/187</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="51" class="abs low">0/51</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="1.6" class="pct low">1.6%</td>
	<td data-value="187" class="abs low">3/187</td>
	</tr>

<tr>
	<td class="file low" data-value="sh.js"><a href="sh.js.html">sh.js</a></td>
	<td data-value="5.49" class="pic low"><div class="chart"><div class="cover-fill" style="width: 5%;"></div><div class="cover-empty" style="width:95%;"></div></div></td>
	<td data-value="5.49" class="pct low">5.49%</td>
	<td data-value="91" class="abs low">5/91</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="28" class="abs low">0/28</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="5.49" class="pct low">5.49%</td>
	<td data-value="91" class="abs low">5/91</td>
	</tr>

<tr>
	<td class="file low" data-value="texture.js"><a href="texture.js.html">texture.js</a></td>
	<td data-value="23.36" class="pic low"><div class="chart"><div class="cover-fill" style="width: 23%;"></div><div class="cover-empty" style="width:77%;"></div></div></td>
	<td data-value="23.36" class="pct low">23.36%</td>
	<td data-value="137" class="abs low">32/137</td>
	<td data-value="21.13" class="pct low">21.13%</td>
	<td data-value="71" class="abs low">15/71</td>
	<td data-value="36.36" class="pct low">36.36%</td>
	<td data-value="11" class="abs low">4/11</td>
	<td data-value="23.36" class="pct low">23.36%</td>
	<td data-value="137" class="abs low">32/137</td>
	</tr>

<tr>
	<td class="file high" data-value="transferable.js"><a href="transferable.js.html">transferable.js</a></td>
	<td data-value="92.68" class="pic high"><div class="chart"><div class="cover-fill" style="width: 92%;"></div><div class="cover-empty" style="width:8%;"></div></div></td>
	<td data-value="92.68" class="pct high">92.68%</td>
	<td data-value="41" class="abs high">38/41</td>
	<td data-value="74.19" class="pct medium">74.19%</td>
	<td data-value="31" class="abs medium">23/31</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="92.68" class="pct high">92.68%</td>
	<td data-value="41" class="abs high">38/41</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
