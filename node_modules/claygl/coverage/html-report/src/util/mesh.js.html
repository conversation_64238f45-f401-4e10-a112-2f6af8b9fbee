<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/util/mesh.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/util/</a> mesh.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">1.6% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>3/187</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/51</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/3</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">1.6% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>3/187</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// TODO test
import Geometry from '../Geometry';
import Mesh from '../Mesh';
import Node from '../Node';
import StandardMaterial from '../StandardMaterial';
import BoundingBox from '../math/BoundingBox';
import glMatrix from '../dep/glmatrix';
var mat4 = glMatrix.mat4;
var vec3 = glMatrix.vec3;
&nbsp;
/**
 * @namespace clay.util.mesh
 */
var meshUtil = {
    /**
     * Merge multiple meshes to one.
     * Note that these meshes must have the same material
     *
     * @param {Array.&lt;clay.Mesh&gt;} meshes
     * @param {boolean} applyWorldTransform
     * @return {clay.Mesh}
     * @memberOf clay.util.mesh
     */
    merge: function (meshes, applyWorldTransform) <span class="fstat-no" title="function not covered" >{</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (! meshes.length) {</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
&nbsp;
        var templateMesh = <span class="cstat-no" title="statement not covered" >meshes[0];</span>
        var templateGeo = <span class="cstat-no" title="statement not covered" >templateMesh.geometry;</span>
        var material = <span class="cstat-no" title="statement not covered" >templateMesh.material;</span>
&nbsp;
        var geometry = <span class="cstat-no" title="statement not covered" >new Geometry({</span>
            dynamic: false
        });
<span class="cstat-no" title="statement not covered" >        geometry.boundingBox = new BoundingBox();</span>
&nbsp;
        var attributeNames = <span class="cstat-no" title="statement not covered" >templateGeo.getEnabledAttributes();</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; attributeNames.length; i++) {</span>
            var name = <span class="cstat-no" title="statement not covered" >attributeNames[i];</span>
            var attr = <span class="cstat-no" title="statement not covered" >templateGeo.attributes[name];</span>
            // Extend custom attributes
<span class="cstat-no" title="statement not covered" >            if (!geometry.attributes[name]) {</span>
<span class="cstat-no" title="statement not covered" >                geometry.attributes[name] = attr.clone(false);</span>
            }
        }
&nbsp;
        var inverseTransposeMatrix = <span class="cstat-no" title="statement not covered" >mat4.create();</span>
        // Initialize the array data and merge bounding box
        var nVertex = <span class="cstat-no" title="statement not covered" >0;</span>
        var nFace = <span class="cstat-no" title="statement not covered" >0;</span>
<span class="cstat-no" title="statement not covered" >        for (var k = 0; k &lt; meshes.length; k++) {</span>
            var currentGeo = <span class="cstat-no" title="statement not covered" >meshes[k].geometry;</span>
<span class="cstat-no" title="statement not covered" >            if (currentGeo.boundingBox) {</span>
<span class="cstat-no" title="statement not covered" >                currentGeo.boundingBox.applyTransform(applyWorldTransform ? meshes[k].worldTransform : meshes[k].localTransform);</span>
<span class="cstat-no" title="statement not covered" >                geometry.boundingBox.union(currentGeo.boundingBox);</span>
            }
<span class="cstat-no" title="statement not covered" >            nVertex += currentGeo.vertexCount;</span>
<span class="cstat-no" title="statement not covered" >            nFace += currentGeo.triangleCount;</span>
        }
<span class="cstat-no" title="statement not covered" >        for (var n = 0; n &lt; attributeNames.length; n++) {</span>
            var name = <span class="cstat-no" title="statement not covered" >attributeNames[n];</span>
            var attrib = <span class="cstat-no" title="statement not covered" >geometry.attributes[name];</span>
<span class="cstat-no" title="statement not covered" >            attrib.init(nVertex);</span>
        }
<span class="cstat-no" title="statement not covered" >        if (nVertex &gt;= 0xffff) {</span>
<span class="cstat-no" title="statement not covered" >            geometry.indices = new Uint32Array(nFace * 3);</span>
        }
        else {
<span class="cstat-no" title="statement not covered" >            geometry.indices = new Uint16Array(nFace * 3);</span>
        }
&nbsp;
        var vertexOffset = <span class="cstat-no" title="statement not covered" >0;</span>
        var indicesOffset = <span class="cstat-no" title="statement not covered" >0;</span>
        var useIndices = <span class="cstat-no" title="statement not covered" >templateGeo.isUseIndices();</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        for (var mm = 0; mm &lt; meshes.length; mm++) {</span>
            var mesh = <span class="cstat-no" title="statement not covered" >meshes[mm];</span>
            var currentGeo = <span class="cstat-no" title="statement not covered" >mesh.geometry;</span>
&nbsp;
            var nVertex = <span class="cstat-no" title="statement not covered" >currentGeo.vertexCount;</span>
&nbsp;
            var matrix = <span class="cstat-no" title="statement not covered" >applyWorldTransform ? mesh.worldTransform.array : mesh.localTransform.array;</span>
<span class="cstat-no" title="statement not covered" >            mat4.invert(inverseTransposeMatrix, matrix);</span>
<span class="cstat-no" title="statement not covered" >            mat4.transpose(inverseTransposeMatrix, inverseTransposeMatrix);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            for (var nn = 0; nn &lt; attributeNames.length; nn++) {</span>
                var name = <span class="cstat-no" title="statement not covered" >attributeNames[nn];</span>
                var currentAttr = <span class="cstat-no" title="statement not covered" >currentGeo.attributes[name];</span>
                var targetAttr = <span class="cstat-no" title="statement not covered" >geometry.attributes[name];</span>
                // Skip the unused attributes;
<span class="cstat-no" title="statement not covered" >                if (!currentAttr.value.length) {</span>
<span class="cstat-no" title="statement not covered" >                    continue;</span>
                }
                var len = <span class="cstat-no" title="statement not covered" >currentAttr.value.length;</span>
                var size = <span class="cstat-no" title="statement not covered" >currentAttr.size;</span>
                var offset = <span class="cstat-no" title="statement not covered" >vertexOffset * size;</span>
                var count = <span class="cstat-no" title="statement not covered" >len / size;</span>
<span class="cstat-no" title="statement not covered" >                for (var i = 0; i &lt; len; i++) {</span>
<span class="cstat-no" title="statement not covered" >                    targetAttr.value[offset + i] = currentAttr.value[i];</span>
                }
                // Transform position, normal and tangent
<span class="cstat-no" title="statement not covered" >                if (name === 'position') {</span>
<span class="cstat-no" title="statement not covered" >                    vec3.forEach(targetAttr.value, size, offset, count, vec3.transformMat4, matrix);</span>
                }
                else <span class="cstat-no" title="statement not covered" >if (name === 'normal' || name === 'tangent') {</span>
<span class="cstat-no" title="statement not covered" >                    vec3.forEach(targetAttr.value, size, offset, count, vec3.transformMat4, inverseTransposeMatrix);</span>
                }
            }
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (useIndices) {</span>
                var len = <span class="cstat-no" title="statement not covered" >currentGeo.indices.length;</span>
<span class="cstat-no" title="statement not covered" >                for (var i = 0; i &lt; len; i++) {</span>
<span class="cstat-no" title="statement not covered" >                    geometry.indices[i + indicesOffset] = currentGeo.indices[i] + vertexOffset;</span>
                }
<span class="cstat-no" title="statement not covered" >                indicesOffset += len;</span>
            }
&nbsp;
<span class="cstat-no" title="statement not covered" >            vertexOffset += nVertex;</span>
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        return new Mesh({</span>
            material: material,
            geometry: geometry
        });
    },
&nbsp;
    /**
     * Split mesh into sub meshes, each mesh will have maxJointNumber joints.
     * @param {clay.Mesh} mesh
     * @param {number} maxJointNumber
     * @param {boolean} inPlace
     * @return {clay.Node}
     *
     * @memberOf clay.util.mesh
     */
&nbsp;
    // FIXME, Have issues on some models
    splitByJoints: function (mesh, maxJointNumber, inPlace) <span class="fstat-no" title="function not covered" >{</span>
        var geometry = <span class="cstat-no" title="statement not covered" >mesh.geometry;</span>
        var skeleton = <span class="cstat-no" title="statement not covered" >mesh.skeleton;</span>
        var material = <span class="cstat-no" title="statement not covered" >mesh.material;</span>
        var joints = <span class="cstat-no" title="statement not covered" >mesh.joints;</span>
<span class="cstat-no" title="statement not covered" >        if (!geometry || !skeleton || !joints.length) {</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
<span class="cstat-no" title="statement not covered" >        if (joints.length &lt; maxJointNumber) {</span>
<span class="cstat-no" title="statement not covered" >            return mesh;</span>
        }
&nbsp;
&nbsp;
        var indices = <span class="cstat-no" title="statement not covered" >geometry.indices;</span>
&nbsp;
        var faceLen = <span class="cstat-no" title="statement not covered" >geometry.triangleCount;</span>
        var rest = <span class="cstat-no" title="statement not covered" >faceLen;</span>
        var isFaceAdded = <span class="cstat-no" title="statement not covered" >[];</span>
        var jointValues = <span class="cstat-no" title="statement not covered" >geometry.attributes.joint.value;</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; faceLen; i++) {</span>
<span class="cstat-no" title="statement not covered" >            isFaceAdded[i] = false;</span>
        }
        var addedJointIdxPerFace = <span class="cstat-no" title="statement not covered" >[];</span>
&nbsp;
        var buckets = <span class="cstat-no" title="statement not covered" >[];</span>
&nbsp;
        var getJointByIndex = <span class="cstat-no" title="statement not covered" >function (idx) <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >            return joints[idx];</span>
        };
<span class="cstat-no" title="statement not covered" >        while (rest &gt; 0) {</span>
            var bucketTriangles = <span class="cstat-no" title="statement not covered" >[];</span>
            var bucketJointReverseMap = <span class="cstat-no" title="statement not covered" >[];</span>
            var bucketJoints = <span class="cstat-no" title="statement not covered" >[];</span>
            var subJointNumber = <span class="cstat-no" title="statement not covered" >0;</span>
<span class="cstat-no" title="statement not covered" >            for (var i = 0; i &lt; joints.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >                bucketJointReverseMap[i] = -1;</span>
            }
<span class="cstat-no" title="statement not covered" >            for (var f = 0; f &lt; faceLen; f++) {</span>
<span class="cstat-no" title="statement not covered" >                if (isFaceAdded[f]) {</span>
<span class="cstat-no" title="statement not covered" >                    continue;</span>
                }
                var canAddToBucket = <span class="cstat-no" title="statement not covered" >true;</span>
                var addedNumber = <span class="cstat-no" title="statement not covered" >0;</span>
<span class="cstat-no" title="statement not covered" >                for (var i = 0; i &lt; 3; i++) {</span>
&nbsp;
                    var idx = <span class="cstat-no" title="statement not covered" >indices[f * 3 + i];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                    for (var j = 0; j &lt; 4; j++) {</span>
                        var jointIdx = <span class="cstat-no" title="statement not covered" >jointValues[idx * 4 + j];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                        if (jointIdx &gt;= 0) {</span>
<span class="cstat-no" title="statement not covered" >                            if (bucketJointReverseMap[jointIdx] === -1) {</span>
<span class="cstat-no" title="statement not covered" >                                if (subJointNumber &lt; maxJointNumber) {</span>
<span class="cstat-no" title="statement not covered" >                                    bucketJointReverseMap[jointIdx] = subJointNumber;</span>
<span class="cstat-no" title="statement not covered" >                                    bucketJoints[subJointNumber++] = jointIdx;</span>
<span class="cstat-no" title="statement not covered" >                                    addedJointIdxPerFace[addedNumber++] = jointIdx;</span>
                                }
                                else {
<span class="cstat-no" title="statement not covered" >                                    canAddToBucket = false;</span>
                                }
                            }
                        }
                    }
                }
<span class="cstat-no" title="statement not covered" >                if (!canAddToBucket) {</span>
                    // Reverse operation
<span class="cstat-no" title="statement not covered" >                    for (var i = 0; i &lt; addedNumber; i++) {</span>
<span class="cstat-no" title="statement not covered" >                        bucketJointReverseMap[addedJointIdxPerFace[i]] = -1;</span>
<span class="cstat-no" title="statement not covered" >                        bucketJoints.pop();</span>
<span class="cstat-no" title="statement not covered" >                        subJointNumber--;</span>
                    }
                }
                else {
<span class="cstat-no" title="statement not covered" >                    bucketTriangles.push(indices.subarray(f * 3, (f + 1) * 3));</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                    isFaceAdded[f] = true;</span>
<span class="cstat-no" title="statement not covered" >                    rest--;</span>
                }
            }
<span class="cstat-no" title="statement not covered" >            buckets.push({</span>
                triangles: bucketTriangles,
                joints: bucketJoints.map(getJointByIndex),
                jointReverseMap: bucketJointReverseMap
            });
        }
&nbsp;
        var root = <span class="cstat-no" title="statement not covered" >new Node({</span>
            name: mesh.name
        });
        var attribNames = <span class="cstat-no" title="statement not covered" >geometry.getEnabledAttributes();</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        attribNames.splice(attribNames.indexOf('joint'), 1);</span>
        // Map from old vertex index to new vertex index
        var newIndices = <span class="cstat-no" title="statement not covered" >[];</span>
<span class="cstat-no" title="statement not covered" >        for (var b = 0; b &lt; buckets.length; b++) {</span>
            var bucket = <span class="cstat-no" title="statement not covered" >buckets[b];</span>
            var jointReverseMap = <span class="cstat-no" title="statement not covered" >bucket.jointReverseMap;</span>
            var subJointNumber = <span class="cstat-no" title="statement not covered" >bucket.joints.length;</span>
&nbsp;
            var subMat = <span class="cstat-no" title="statement not covered" >material.clone();</span>
<span class="cstat-no" title="statement not covered" >            subMat.name = [material.name, b].join('-');</span>
&nbsp;
            var subGeo = <span class="cstat-no" title="statement not covered" >new Geometry();</span>
&nbsp;
            var subMesh = <span class="cstat-no" title="statement not covered" >new Mesh({</span>
                name: [mesh.name, i].join('-'),
                material: subMat,
                geometry: subGeo,
                skeleton: skeleton,
                joints: bucket.joints.slice()
            });
            var nVertex = <span class="cstat-no" title="statement not covered" >0;</span>
            var nVertex2 = <span class="cstat-no" title="statement not covered" >geometry.vertexCount;</span>
<span class="cstat-no" title="statement not covered" >            for (var i = 0; i &lt; nVertex2; i++) {</span>
<span class="cstat-no" title="statement not covered" >                newIndices[i] = -1;</span>
            }
            // Count sub geo number
<span class="cstat-no" title="statement not covered" >            for (var f = 0; f &lt; bucket.triangles.length; f++) {</span>
                var face = <span class="cstat-no" title="statement not covered" >bucket.triangles[f];</span>
<span class="cstat-no" title="statement not covered" >                for (var i = 0; i &lt; 3; i++) {</span>
                    var idx = <span class="cstat-no" title="statement not covered" >face[i];</span>
<span class="cstat-no" title="statement not covered" >                    if (newIndices[idx] === -1) {</span>
<span class="cstat-no" title="statement not covered" >                        newIndices[idx] = nVertex;</span>
<span class="cstat-no" title="statement not covered" >                        nVertex++;</span>
                    }
                }
            }
<span class="cstat-no" title="statement not covered" >            for (var a = 0; a &lt; attribNames.length; a++) {</span>
                var attribName = <span class="cstat-no" title="statement not covered" >attribNames[a];</span>
                var subAttrib = <span class="cstat-no" title="statement not covered" >subGeo.attributes[attribName];</span>
<span class="cstat-no" title="statement not covered" >                subAttrib.init(nVertex);</span>
            }
<span class="cstat-no" title="statement not covered" >            subGeo.attributes.joint.value = new Float32Array(nVertex * 4);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (nVertex &gt; 0xffff) {</span>
<span class="cstat-no" title="statement not covered" >                subGeo.indices = new Uint32Array(bucket.triangles.length * 3);</span>
            }
            else {
<span class="cstat-no" title="statement not covered" >                subGeo.indices = new Uint16Array(bucket.triangles.length * 3);</span>
            }
&nbsp;
            var indicesOffset = <span class="cstat-no" title="statement not covered" >0;</span>
<span class="cstat-no" title="statement not covered" >            nVertex = 0;</span>
<span class="cstat-no" title="statement not covered" >            for (var i = 0; i &lt; nVertex2; i++) {</span>
<span class="cstat-no" title="statement not covered" >                newIndices[i] = -1;</span>
            }
&nbsp;
<span class="cstat-no" title="statement not covered" >            for (var f = 0; f &lt; bucket.triangles.length; f++) {</span>
                var triangle = <span class="cstat-no" title="statement not covered" >bucket.triangles[f];</span>
<span class="cstat-no" title="statement not covered" >                for (var i = 0; i &lt; 3; i++) {</span>
&nbsp;
                    var idx = <span class="cstat-no" title="statement not covered" >triangle[i];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                    if (newIndices[idx] === -1) {</span>
<span class="cstat-no" title="statement not covered" >                        newIndices[idx] = nVertex;</span>
<span class="cstat-no" title="statement not covered" >                        for (var a = 0; a &lt; attribNames.length; a++) {</span>
                            var attribName = <span class="cstat-no" title="statement not covered" >attribNames[a];</span>
                            var attrib = <span class="cstat-no" title="statement not covered" >geometry.attributes[attribName];</span>
                            var subAttrib = <span class="cstat-no" title="statement not covered" >subGeo.attributes[attribName];</span>
                            var size = <span class="cstat-no" title="statement not covered" >attrib.size;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                            for (var j = 0; j &lt; size; j++) {</span>
<span class="cstat-no" title="statement not covered" >                                subAttrib.value[nVertex * size + j] = attrib.value[idx * size + j];</span>
                            }
                        }
<span class="cstat-no" title="statement not covered" >                        for (var j = 0; j &lt; 4; j++) {</span>
                            var jointIdx = <span class="cstat-no" title="statement not covered" >geometry.attributes.joint.value[idx * 4 + j];</span>
                            var offset = <span class="cstat-no" title="statement not covered" >nVertex * 4 + j;</span>
<span class="cstat-no" title="statement not covered" >                            if (jointIdx &gt;= 0) {</span>
<span class="cstat-no" title="statement not covered" >                                subGeo.attributes.joint.value[offset] = jointReverseMap[jointIdx];</span>
                            }
                            else {
<span class="cstat-no" title="statement not covered" >                                subGeo.attributes.joint.value[offset] = -1;</span>
                            }
                        }
<span class="cstat-no" title="statement not covered" >                        nVertex++;</span>
                    }
<span class="cstat-no" title="statement not covered" >                    subGeo.indices[indicesOffset++] = newIndices[idx];</span>
                }
            }
<span class="cstat-no" title="statement not covered" >            subGeo.updateBoundingBox();</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            root.add(subMesh);</span>
        }
        var children = <span class="cstat-no" title="statement not covered" >mesh.children();</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; children.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >            root.add(children[i]);</span>
        }
<span class="cstat-no" title="statement not covered" >        root.position.copy(mesh.position);</span>
<span class="cstat-no" title="statement not covered" >        root.rotation.copy(mesh.rotation);</span>
<span class="cstat-no" title="statement not covered" >        root.scale.copy(mesh.scale);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (inPlace) {</span>
<span class="cstat-no" title="statement not covered" >            if (mesh.getParent()) {</span>
                var parent = <span class="cstat-no" title="statement not covered" >mesh.getParent();</span>
<span class="cstat-no" title="statement not covered" >                parent.remove(mesh);</span>
<span class="cstat-no" title="statement not covered" >                parent.add(root);</span>
            }
        }
<span class="cstat-no" title="statement not covered" >        return root;</span>
    }
};
&nbsp;
export default meshUtil;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
