<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/async/TaskGroup.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/async/</a> TaskGroup.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">9.91% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>11/111</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/42</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">6.25% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>1/16</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">9.91% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>11/111</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import util from '../core/util';
import Task from './Task';
&nbsp;
/**
 * @constructor
 * @alias clay.async.TaskGroup
 * @extends clay.async.Task
 */
var TaskGroup = function () <span class="fstat-no" title="function not covered" >{</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    Task.apply(this, arguments);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this._tasks = [];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this._fulfilledNumber = 0;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this._rejectedNumber = 0;</span>
};
&nbsp;
var Ctor = function (){};
Ctor.prototype = Task.prototype;
TaskGroup.prototype = new Ctor();
&nbsp;
TaskGroup.prototype.constructor = TaskGroup;
&nbsp;
/**
 * Wait for all given tasks successed, task can also be any notifier object which will trigger success and error events. Like {@link clay.Texture2D}, {@link clay.TextureCube}, {@link clay.loader.GLTF}.
 * @param  {Array.&lt;clay.async.Task&gt;} tasks
 * @chainable
 * @example
 *     // Load texture list
 *     var list = ['a.jpg', 'b.jpg', 'c.jpg']
 *     var textures = list.map(function (src) {
 *         var texture = new clay.Texture2D();
 *         texture.load(src);
 *         return texture;
 *     });
 *     var taskGroup = new clay.async.TaskGroup();
 *     taskGroup.all(textures).success(function () {
 *         // Do some thing after all textures loaded
 *     });
 */
TaskGroup.prototype.all = function (tasks) <span class="fstat-no" title="function not covered" >{</span>
    var count = <span class="cstat-no" title="statement not covered" >0;</span>
    var self = <span class="cstat-no" title="statement not covered" >this;</span>
    var data = <span class="cstat-no" title="statement not covered" >[];</span>
<span class="cstat-no" title="statement not covered" >    this._tasks = tasks;</span>
<span class="cstat-no" title="statement not covered" >    this._fulfilledNumber = 0;</span>
<span class="cstat-no" title="statement not covered" >    this._rejectedNumber = 0;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    util.each(tasks, function (task, idx) <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >        if (!task || !task.once) {</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
<span class="cstat-no" title="statement not covered" >        count++;</span>
<span class="cstat-no" title="statement not covered" >        task.once('success', function (res) <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >            count--;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            self._fulfilledNumber++;</span>
            // TODO
            // Some tasks like texture, loader are not inherited from task
            // We need to set the states here
<span class="cstat-no" title="statement not covered" >            task._fulfilled = true;</span>
<span class="cstat-no" title="statement not covered" >            task._rejected = false;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            data[idx] = res;</span>
<span class="cstat-no" title="statement not covered" >            if (count === 0) {</span>
<span class="cstat-no" title="statement not covered" >                self.resolve(data);</span>
            }
        });
<span class="cstat-no" title="statement not covered" >        task.once('error', function () <span class="fstat-no" title="function not covered" >{</span></span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            self._rejectedNumber ++;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            task._fulfilled = false;</span>
<span class="cstat-no" title="statement not covered" >            task._rejected = true;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            self.reject(task);</span>
        });
    });
<span class="cstat-no" title="statement not covered" >    if (count === 0) {</span>
<span class="cstat-no" title="statement not covered" >        setTimeout(function () <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >            self.resolve(data);</span>
        });
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
<span class="cstat-no" title="statement not covered" >    return this;</span>
};
/**
 * Wait for all given tasks finished, either successed or failed
 * @param  {Array.&lt;clay.async.Task&gt;} tasks
 * @return {clay.async.TaskGroup}
 */
TaskGroup.prototype.allSettled = function (tasks) <span class="fstat-no" title="function not covered" >{</span>
    var count = <span class="cstat-no" title="statement not covered" >0;</span>
    var self = <span class="cstat-no" title="statement not covered" >this;</span>
    var data = <span class="cstat-no" title="statement not covered" >[];</span>
<span class="cstat-no" title="statement not covered" >    if (tasks.length === 0) {</span>
<span class="cstat-no" title="statement not covered" >        setTimeout(function () <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >            self.trigger('success', data);</span>
        });
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
<span class="cstat-no" title="statement not covered" >    this._tasks = tasks;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    util.each(tasks, function (task, idx) <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >        if (!task || !task.once) {</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
<span class="cstat-no" title="statement not covered" >        count++;</span>
<span class="cstat-no" title="statement not covered" >        task.once('success', function (res) <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >            count--;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            self._fulfilledNumber++;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            task._fulfilled = true;</span>
<span class="cstat-no" title="statement not covered" >            task._rejected = false;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            data[idx] = res;</span>
<span class="cstat-no" title="statement not covered" >            if (count === 0) {</span>
<span class="cstat-no" title="statement not covered" >                self.resolve(data);</span>
            }
        });
<span class="cstat-no" title="statement not covered" >        task.once('error', function (err) <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >            count--;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            self._rejectedNumber++;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            task._fulfilled = false;</span>
<span class="cstat-no" title="statement not covered" >            task._rejected = true;</span>
&nbsp;
            // TODO
<span class="cstat-no" title="statement not covered" >            data[idx] = null;</span>
<span class="cstat-no" title="statement not covered" >            if (count === 0) {</span>
<span class="cstat-no" title="statement not covered" >                self.resolve(data);</span>
            }
        });
    });
<span class="cstat-no" title="statement not covered" >    return this;</span>
};
/**
 * Get successed sub tasks number, recursive can be true if sub task is also a TaskGroup.
 * @param  {boolean} [recursive]
 * @return {number}
 */
TaskGroup.prototype.getFulfilledNumber = function (recursive) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    if (recursive) {</span>
        var nFulfilled = <span class="cstat-no" title="statement not covered" >0;</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; this._tasks.length; i++) {</span>
            var task = <span class="cstat-no" title="statement not covered" >this._tasks[i];</span>
<span class="cstat-no" title="statement not covered" >            if (task instanceof TaskGroup) {</span>
<span class="cstat-no" title="statement not covered" >                nFulfilled += task.getFulfilledNumber(recursive);</span>
            } else <span class="cstat-no" title="statement not covered" >if(task._fulfilled) {</span>
<span class="cstat-no" title="statement not covered" >                nFulfilled += 1;</span>
            }
        }
<span class="cstat-no" title="statement not covered" >        return nFulfilled;</span>
    } else {
<span class="cstat-no" title="statement not covered" >        return this._fulfilledNumber;</span>
    }
};
&nbsp;
/**
 * Get failed sub tasks number, recursive can be true if sub task is also a TaskGroup.
 * @param  {boolean} [recursive]
 * @return {number}
 */
TaskGroup.prototype.getRejectedNumber = function (recursive) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    if (recursive) {</span>
        var nRejected = <span class="cstat-no" title="statement not covered" >0;</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; this._tasks.length; i++) {</span>
            var task = <span class="cstat-no" title="statement not covered" >this._tasks[i];</span>
<span class="cstat-no" title="statement not covered" >            if (task instanceof TaskGroup) {</span>
<span class="cstat-no" title="statement not covered" >                nRejected += task.getRejectedNumber(recursive);</span>
            } else <span class="cstat-no" title="statement not covered" >if(task._rejected) {</span>
<span class="cstat-no" title="statement not covered" >                nRejected += 1;</span>
            }
        }
<span class="cstat-no" title="statement not covered" >        return nRejected;</span>
    } else {
<span class="cstat-no" title="statement not covered" >        return this._rejectedNumber;</span>
    }
};
&nbsp;
/**
 * Get finished sub tasks number, recursive can be true if sub task is also a TaskGroup.
 * @param  {boolean} [recursive]
 * @return {number}
 */
TaskGroup.prototype.getSettledNumber = function (recursive) <span class="fstat-no" title="function not covered" >{</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (recursive) {</span>
        var nSettled = <span class="cstat-no" title="statement not covered" >0;</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; this._tasks.length; i++) {</span>
            var task = <span class="cstat-no" title="statement not covered" >this._tasks[i];</span>
<span class="cstat-no" title="statement not covered" >            if (task instanceof TaskGroup) {</span>
<span class="cstat-no" title="statement not covered" >                nSettled += task.getSettledNumber(recursive);</span>
            } else <span class="cstat-no" title="statement not covered" >if(task._rejected || task._fulfilled) {</span>
<span class="cstat-no" title="statement not covered" >                nSettled += 1;</span>
            }
        }
<span class="cstat-no" title="statement not covered" >        return nSettled;</span>
    } else {
<span class="cstat-no" title="statement not covered" >        return this._fulfilledNumber + this._rejectedNumber;</span>
    }
};
&nbsp;
/**
 * Get all sub tasks number, recursive can be true if sub task is also a TaskGroup.
 * @param  {boolean} [recursive]
 * @return {number}
 */
TaskGroup.prototype.getTaskNumber = function (recursive) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    if (recursive) {</span>
        var nTask = <span class="cstat-no" title="statement not covered" >0;</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; this._tasks.length; i++) {</span>
            var task = <span class="cstat-no" title="statement not covered" >this._tasks[i];</span>
<span class="cstat-no" title="statement not covered" >            if (task instanceof TaskGroup) {</span>
<span class="cstat-no" title="statement not covered" >                nTask += task.getTaskNumber(recursive);</span>
            } else {
<span class="cstat-no" title="statement not covered" >                nTask += 1;</span>
            }
        }
<span class="cstat-no" title="statement not covered" >        return nTask;</span>
    } else {
<span class="cstat-no" title="statement not covered" >        return this._tasks.length;</span>
    }
};
&nbsp;
export default TaskGroup;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
