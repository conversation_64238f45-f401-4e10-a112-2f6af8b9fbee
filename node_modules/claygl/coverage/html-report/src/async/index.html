<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/async/</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> src/async/
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">13.64% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>21/154</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/54</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">3.57% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>1/28</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">13.64% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>21/154</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="Task.js"><a href="Task.js.html">Task.js</a></td>
	<td data-value="23.26" class="pic low"><div class="chart"><div class="cover-fill" style="width: 23%;"></div><div class="cover-empty" style="width:77%;"></div></div></td>
	<td data-value="23.26" class="pct low">23.26%</td>
	<td data-value="43" class="abs low">10/43</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="23.26" class="pct low">23.26%</td>
	<td data-value="43" class="abs low">10/43</td>
	</tr>

<tr>
	<td class="file low" data-value="TaskGroup.js"><a href="TaskGroup.js.html">TaskGroup.js</a></td>
	<td data-value="9.91" class="pic low"><div class="chart"><div class="cover-fill" style="width: 9%;"></div><div class="cover-empty" style="width:91%;"></div></div></td>
	<td data-value="9.91" class="pct low">9.91%</td>
	<td data-value="111" class="abs low">11/111</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="42" class="abs low">0/42</td>
	<td data-value="6.25" class="pct low">6.25%</td>
	<td data-value="16" class="abs low">1/16</td>
	<td data-value="9.91" class="pct low">9.91%</td>
	<td data-value="111" class="abs low">11/111</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
