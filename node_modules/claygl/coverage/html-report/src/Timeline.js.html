<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/Timeline.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../index.html">all files</a> / <a href="index.html">src/</a> Timeline.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">78.13% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>50/64</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">58.62% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>17/29</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">64.29% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>9/14</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">78.13% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>50/64</span>
      </div>
    </div>
  </div>
  <div class='status-line medium'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-yes">60×</span>
<span class="cline-any cline-yes">60×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">60×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-yes">60×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">57×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">46×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">44×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">44×</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import Base from './core/Base';
import Animator from './animation/Animator';
&nbsp;
var g = typeof window === 'undefined' ? <span class="branch-0 cbranch-no" title="branch not covered" >global </span>: window;
&nbsp;
var requestAnimationFrame = g.requestAnimationFrame
                            || <span class="branch-1 cbranch-no" title="branch not covered" >g.msRequestAnimationFrame</span>
                            || <span class="branch-2 cbranch-no" title="branch not covered" >g.mozRequestAnimationFrame</span>
                            || <span class="branch-3 cbranch-no" title="branch not covered" >g.webkitRequestAnimationFrame</span>
                            || <span class="branch-4 cbranch-no" title="branch not covered" >function (func)<span class="fstat-no" title="function not covered" >{ <span class="cstat-no" title="statement not covered" >setTimeout(func, 16); </span>};</span></span>
&nbsp;
/**
 * Animation is global timeline that schedule all clips. each frame animation will set the time of clips to current and update the states of clips
 * @constructor clay.Timeline
 * @extends clay.core.Base
 *
 * @example
 * var animation = new clay.Timeline();
 * var node = new clay.Node();
 * animation.animate(node.position)
 *     .when(1000, {
 *         x: 500,
 *         y: 500
 *     })
 *     .when(2000, {
 *         x: 100,
 *         y: 100
 *     })
 *     .when(3000, {
 *         z: 10
 *     })
 *     .start('spline');
 */
var Timeline = Base.extend(function () {
    return /** @lends clay.Timeline# */{
        /**
         * stage is an object with render method, each frame if there exists any animating clips, stage.render will be called
         * @type {Object}
         */
        stage: null,
&nbsp;
        _clips: [],
&nbsp;
        _running: false,
&nbsp;
        _time: 0,
&nbsp;
        _paused: false,
&nbsp;
        _pausedTime: 0
    };
},
/** @lends clay.Timeline.prototype */
{
&nbsp;
    /**
     * Add animator
     * @param {clay.animate.Animator} animator
     */
    addAnimator: function (animator) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        animator.animation = this;</span>
        var clips = <span class="cstat-no" title="statement not covered" >animator.getClips();</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; clips.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >            this.addClip(clips[i]);</span>
        }
    },
&nbsp;
    /**
     * @param {clay.animation.Clip} clip
     */
    addClip: function (clip) {
        <span class="missing-if-branch" title="else path not taken" >E</span>if (this._clips.indexOf(clip) &lt; 0) {
            this._clips.push(clip);
        }
    },
&nbsp;
    /**
     * @param  {clay.animation.Clip} clip
     */
    removeClip: function (clip) <span class="fstat-no" title="function not covered" >{</span>
        var idx = <span class="cstat-no" title="statement not covered" >this._clips.indexOf(clip);</span>
<span class="cstat-no" title="statement not covered" >        if (idx &gt;= 0) {</span>
<span class="cstat-no" title="statement not covered" >            this._clips.splice(idx, 1);</span>
        }
    },
&nbsp;
    /**
     * Remove animator
     * @param {clay.animate.Animator} animator
     */
    removeAnimator: function (animator) <span class="fstat-no" title="function not covered" >{</span>
        var clips = <span class="cstat-no" title="statement not covered" >animator.getClips();</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; clips.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >            this.removeClip(clips[i]);</span>
        }
<span class="cstat-no" title="statement not covered" >        animator.animation = null;</span>
    },
&nbsp;
    _update: function () {
&nbsp;
        var time = Date.now() - this._pausedTime;
        var delta = time - this._time;
        var clips = this._clips;
        var len = clips.length;
&nbsp;
        var deferredEvents = [];
        var deferredClips = [];
        for (var i = 0; i &lt; len; i++) {
            var clip = clips[i];
            var e = clip.step(time, delta, false);
            // Throw out the events need to be called after
            // stage.render, like finish
            if (e) {
                deferredEvents.push(e);
                deferredClips.push(clip);
            }
        }
&nbsp;
        // Remove the finished clip
        for (var i = 0; i &lt; len;) {
            if (clips[i]._needsRemove) {
                clips[i] = clips[len-1];
                clips.pop();
                len--;
            } else {
                i++;
            }
        }
&nbsp;
        len = deferredEvents.length;
        for (var i = 0; i &lt; len; i++) {
            deferredClips[i].fire(deferredEvents[i]);
        }
&nbsp;
        this._time = time;
&nbsp;
        this.trigger('frame', delta);
&nbsp;
        <span class="missing-if-branch" title="if path not taken" >I</span>if (this.stage &amp;&amp; <span class="branch-1 cbranch-no" title="branch not covered" >this.stage.render)</span> {
<span class="cstat-no" title="statement not covered" >            this.stage.render();</span>
        }
    },
    /**
     * Start running animation
     */
    start: function () {
        var self = this;
&nbsp;
        this._running = true;
        this._time = Date.now();
&nbsp;
        this._pausedTime = 0;
&nbsp;
        function step() {
            if (self._running) {
&nbsp;
                requestAnimationFrame(step);
&nbsp;
                if (!self._paused) {
                    self._update();
                }
            }
        }
&nbsp;
        requestAnimationFrame(step);
&nbsp;
    },
    /**
     * Stop running animation
     */
    stop: function () {
        this._running = false;
    },
&nbsp;
    /**
     * Pause
     */
    pause: function () {
        <span class="missing-if-branch" title="else path not taken" >E</span>if (!this._paused) {
            this._pauseStart = Date.now();
            this._paused = true;
        }
    },
&nbsp;
    /**
     * Resume
     */
    resume: function () {
        <span class="missing-if-branch" title="else path not taken" >E</span>if (this._paused) {
            this._pausedTime += Date.now() - this._pauseStart;
            this._paused = false;
        }
    },
&nbsp;
    /**
     * Remove all clips
     */
    removeClipsAll: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        this._clips = [];</span>
    },
    /**
     * Create an animator
     * @param  {Object} target
     * @param  {Object} [options]
     * @param  {boolean} [options.loop]
     * @param  {Function} [options.getter]
     * @param  {Function} [options.setter]
     * @param  {Function} [options.interpolater]
     * @return {clay.animation.Animator}
     */
    animate: function (target, options) {
        options = options || {};
        var animator = new Animator(
            target,
            options.loop,
            options.getter,
            options.setter,
            options.interpolater
        );
        animator.animation = this;
        return animator;
    }
});
&nbsp;
export default Timeline;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
