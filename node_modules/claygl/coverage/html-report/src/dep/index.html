<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/dep/</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> src/dep/
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">44.28% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>936/2114</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">33.91% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>39/115</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">30.39% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>55/181</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">46.31% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>841/1816</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="glmatrix.js"><a href="glmatrix.js.html">glmatrix.js</a></td>
	<td data-value="44.28" class="pic low"><div class="chart"><div class="cover-fill" style="width: 44%;"></div><div class="cover-empty" style="width:56%;"></div></div></td>
	<td data-value="44.28" class="pct low">44.28%</td>
	<td data-value="2114" class="abs low">936/2114</td>
	<td data-value="33.91" class="pct low">33.91%</td>
	<td data-value="115" class="abs low">39/115</td>
	<td data-value="30.39" class="pct low">30.39%</td>
	<td data-value="181" class="abs low">55/181</td>
	<td data-value="46.31" class="pct low">46.31%</td>
	<td data-value="1816" class="abs low">841/1816</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
