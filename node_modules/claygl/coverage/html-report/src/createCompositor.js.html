<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/createCompositor.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../index.html">all files</a> / <a href="index.html">src/</a> createCompositor.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">0.74% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>1/135</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/78</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/16</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0.74% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>1/135</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import util from './core/util';
import Compositor from './compositor/Compositor';
import CompoSceneNode from './compositor/SceneNode';
import CompoTextureNode from './compositor/TextureNode';
import CompoFilterNode from './compositor/FilterNode';
import Shader from './Shader';
import Texture from './Texture';
import Texture2D from './Texture2D';
import TextureCube from './TextureCube';
&nbsp;
var shaderSourceReg = /^#source\((.*?)\)/;
&nbsp;
/**
 * @name clay.createCompositor
 * @function
 * @param {Object} json
 * @param {Object} [opts]
 * @return {clay.compositor.Compositor}
 */
function createCompositor(json, opts) <span class="fstat-no" title="function not covered" >{</span>
    var compositor = <span class="cstat-no" title="statement not covered" >new Compositor();</span>
<span class="cstat-no" title="statement not covered" >    opts = opts || {};</span>
&nbsp;
    var lib = <span class="cstat-no" title="statement not covered" >{</span>
        textures: {},
        parameters: {}
    };
    var afterLoad = <span class="cstat-no" title="statement not covered" >function(shaderLib, textureLib) <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; json.nodes.length; i++) {</span>
            var nodeInfo = <span class="cstat-no" title="statement not covered" >json.nodes[i];</span>
            var node = <span class="cstat-no" title="statement not covered" >createNode(nodeInfo, lib, opts);</span>
<span class="cstat-no" title="statement not covered" >            if (node) {</span>
<span class="cstat-no" title="statement not covered" >                compositor.addNode(node);</span>
            }
        }
    };
&nbsp;
<span class="cstat-no" title="statement not covered" >    for (var name in json.parameters) {</span>
        var paramInfo = <span class="cstat-no" title="statement not covered" >json.parameters[name];</span>
<span class="cstat-no" title="statement not covered" >        lib.parameters[name] = convertParameter(paramInfo);</span>
    }
    // TODO load texture asynchronous
<span class="cstat-no" title="statement not covered" >    loadTextures(json, lib, opts, function(textureLib) <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >        lib.textures = textureLib;</span>
<span class="cstat-no" title="statement not covered" >        afterLoad();</span>
    });
&nbsp;
<span class="cstat-no" title="statement not covered" >    return compositor;</span>
}
&nbsp;
function createNode(nodeInfo, lib, opts) <span class="fstat-no" title="function not covered" >{</span>
    var type = <span class="cstat-no" title="statement not covered" >nodeInfo.type || 'filter';</span>
    var shaderSource;
    var inputs;
    var outputs;
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (type === 'filter') {</span>
        var shaderExp = <span class="cstat-no" title="statement not covered" >nodeInfo.shader.trim();</span>
        var res = <span class="cstat-no" title="statement not covered" >shaderSourceReg.exec(shaderExp);</span>
<span class="cstat-no" title="statement not covered" >        if (res) {</span>
<span class="cstat-no" title="statement not covered" >            shaderSource = Shader.source(res[1].trim());</span>
        }
        else <span class="cstat-no" title="statement not covered" >if (shaderExp.charAt(0) === '#') {</span>
<span class="cstat-no" title="statement not covered" >            shaderSource = lib.shaders[shaderExp.substr(1)];</span>
        }
<span class="cstat-no" title="statement not covered" >        if (!shaderSource) {</span>
<span class="cstat-no" title="statement not covered" >            shaderSource = shaderExp;</span>
        }
<span class="cstat-no" title="statement not covered" >        if (!shaderSource) {</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (nodeInfo.inputs) {</span>
<span class="cstat-no" title="statement not covered" >        inputs = {};</span>
<span class="cstat-no" title="statement not covered" >        for (var name in nodeInfo.inputs) {</span>
<span class="cstat-no" title="statement not covered" >            if (typeof nodeInfo.inputs[name] === 'string') {</span>
<span class="cstat-no" title="statement not covered" >                inputs[name] = nodeInfo.inputs[name];</span>
            }
            else {
<span class="cstat-no" title="statement not covered" >                inputs[name] = {</span>
                    node: nodeInfo.inputs[name].node,
                    pin: nodeInfo.inputs[name].pin
                };
            }
        }
    }
<span class="cstat-no" title="statement not covered" >    if (nodeInfo.outputs) {</span>
<span class="cstat-no" title="statement not covered" >        outputs = {};</span>
<span class="cstat-no" title="statement not covered" >        for (var name in nodeInfo.outputs) {</span>
            var outputInfo = <span class="cstat-no" title="statement not covered" >nodeInfo.outputs[name];</span>
<span class="cstat-no" title="statement not covered" >            outputs[name] = {};</span>
<span class="cstat-no" title="statement not covered" >            if (outputInfo.attachment != null) {</span>
<span class="cstat-no" title="statement not covered" >                outputs[name].attachment = outputInfo.attachment;</span>
            }
<span class="cstat-no" title="statement not covered" >            if (outputInfo.keepLastFrame != null) {</span>
<span class="cstat-no" title="statement not covered" >                outputs[name].keepLastFrame = outputInfo.keepLastFrame;</span>
            }
<span class="cstat-no" title="statement not covered" >            if (outputInfo.outputLastFrame != null) {</span>
<span class="cstat-no" title="statement not covered" >                outputs[name].outputLastFrame = outputInfo.outputLastFrame;</span>
            }
<span class="cstat-no" title="statement not covered" >            if (outputInfo.parameters) {</span>
<span class="cstat-no" title="statement not covered" >                outputs[name].parameters = convertParameter(outputInfo.parameters);</span>
            }
        }
    }
    var node;
<span class="cstat-no" title="statement not covered" >    if (type === 'scene') {</span>
<span class="cstat-no" title="statement not covered" >        node = new CompoSceneNode({</span>
            name: nodeInfo.name,
            scene: opts.scene,
            camera: opts.camera,
            outputs: outputs
        });
    }
    else <span class="cstat-no" title="statement not covered" >if (type === 'texture') {</span>
<span class="cstat-no" title="statement not covered" >        node = new CompoTextureNode({</span>
            name: nodeInfo.name,
            outputs: outputs
        });
    }
    // Default is filter
    else {
<span class="cstat-no" title="statement not covered" >        node = new CompoFilterNode({</span>
            name: nodeInfo.name,
            shader: shaderSource,
            inputs: inputs,
            outputs: outputs
        });
    }
<span class="cstat-no" title="statement not covered" >    if (node) {</span>
<span class="cstat-no" title="statement not covered" >        if (nodeInfo.parameters) {</span>
<span class="cstat-no" title="statement not covered" >            for (var name in nodeInfo.parameters) {</span>
                var val = <span class="cstat-no" title="statement not covered" >nodeInfo.parameters[name];</span>
<span class="cstat-no" title="statement not covered" >                if (typeof(val) === 'string') {</span>
<span class="cstat-no" title="statement not covered" >                    val = val.trim();</span>
<span class="cstat-no" title="statement not covered" >                    if (val.charAt(0) === '#') {</span>
<span class="cstat-no" title="statement not covered" >                        val = lib.textures[val.substr(1)];</span>
                    }
                    else {
<span class="cstat-no" title="statement not covered" >                        node.on(</span>
                            'beforerender', createSizeSetHandler(
                                name, tryConvertExpr(val)
                            )
                        );
                    }
                }
<span class="cstat-no" title="statement not covered" >                node.setParameter(name, val);</span>
            }
        }
<span class="cstat-no" title="statement not covered" >        if (nodeInfo.defines &amp;&amp; node.pass) {</span>
<span class="cstat-no" title="statement not covered" >            for (var name in nodeInfo.defines) {</span>
                var val = <span class="cstat-no" title="statement not covered" >nodeInfo.defines[name];</span>
<span class="cstat-no" title="statement not covered" >                node.pass.material.define('fragment', name, val);</span>
            }
        }
    }
<span class="cstat-no" title="statement not covered" >    return node;</span>
}
&nbsp;
function convertParameter(paramInfo) <span class="fstat-no" title="function not covered" >{</span>
    var param = <span class="cstat-no" title="statement not covered" >{};</span>
<span class="cstat-no" title="statement not covered" >    if (!paramInfo) {</span>
<span class="cstat-no" title="statement not covered" >        return param;</span>
    }
<span class="cstat-no" title="statement not covered" >    ['type', 'minFilter', 'magFilter', 'wrapS', 'wrapT', 'flipY', 'useMipmap']</span>
        .forEach(function(name) <span class="fstat-no" title="function not covered" >{</span>
            var val = <span class="cstat-no" title="statement not covered" >paramInfo[name];</span>
<span class="cstat-no" title="statement not covered" >            if (val != null) {</span>
                // Convert string to enum
<span class="cstat-no" title="statement not covered" >                if (typeof val === 'string') {</span>
<span class="cstat-no" title="statement not covered" >                    val = Texture[val];</span>
                }
<span class="cstat-no" title="statement not covered" >                param[name] = val;</span>
            }
        });
<span class="cstat-no" title="statement not covered" >    ['width', 'height']</span>
        .forEach(function(name) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            if (paramInfo[name] != null) {</span>
                var val = <span class="cstat-no" title="statement not covered" >paramInfo[name];</span>
<span class="cstat-no" title="statement not covered" >                if (typeof val === 'string') {</span>
<span class="cstat-no" title="statement not covered" >                    val = val.trim();</span>
<span class="cstat-no" title="statement not covered" >                    param[name] = createSizeParser(</span>
                        name, tryConvertExpr(val)
                    );
                }
                else {
<span class="cstat-no" title="statement not covered" >                    param[name] = val;</span>
                }
            }
        });
<span class="cstat-no" title="statement not covered" >    if (paramInfo.useMipmap != null) {</span>
<span class="cstat-no" title="statement not covered" >        param.useMipmap = paramInfo.useMipmap;</span>
    }
<span class="cstat-no" title="statement not covered" >    return param;</span>
}
&nbsp;
function loadTextures(json, lib, opts, callback) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    if (!json.textures) {</span>
<span class="cstat-no" title="statement not covered" >        callback({});</span>
<span class="cstat-no" title="statement not covered" >        return;</span>
    }
    var textures = <span class="cstat-no" title="statement not covered" >{};</span>
    var loading = <span class="cstat-no" title="statement not covered" >0;</span>
&nbsp;
    var cbd = <span class="cstat-no" title="statement not covered" >false;</span>
    var textureRootPath = <span class="cstat-no" title="statement not covered" >opts.textureRootPath;</span>
<span class="cstat-no" title="statement not covered" >    util.each(json.textures, function(textureInfo, name) <span class="fstat-no" title="function not covered" >{</span></span>
        var texture;
        var path = <span class="cstat-no" title="statement not covered" >textureInfo.path;</span>
        var parameters = <span class="cstat-no" title="statement not covered" >convertParameter(textureInfo.parameters);</span>
<span class="cstat-no" title="statement not covered" >        if (Array.isArray(path) &amp;&amp; path.length === 6) {</span>
<span class="cstat-no" title="statement not covered" >            if (textureRootPath) {</span>
<span class="cstat-no" title="statement not covered" >                path = path.map(function(item) <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >                    return util.relative2absolute(item, textureRootPath);</span>
                });
            }
<span class="cstat-no" title="statement not covered" >            texture = new TextureCube(parameters);</span>
        }
        else <span class="cstat-no" title="statement not covered" >if(typeof path === 'string') {</span>
<span class="cstat-no" title="statement not covered" >            if (textureRootPath) {</span>
<span class="cstat-no" title="statement not covered" >                path = util.relative2absolute(path, textureRootPath);</span>
            }
<span class="cstat-no" title="statement not covered" >            texture = new Texture2D(parameters);</span>
        }
        else {
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        texture.load(path);</span>
<span class="cstat-no" title="statement not covered" >        loading++;</span>
<span class="cstat-no" title="statement not covered" >        texture.once('success', function() <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >            textures[name] = texture;</span>
<span class="cstat-no" title="statement not covered" >            loading--;</span>
<span class="cstat-no" title="statement not covered" >            if (loading === 0) {</span>
<span class="cstat-no" title="statement not covered" >                callback(textures);</span>
<span class="cstat-no" title="statement not covered" >                cbd = true;</span>
            }
        });
    });
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (loading === 0 &amp;&amp; !cbd) {</span>
<span class="cstat-no" title="statement not covered" >        callback(textures);</span>
    }
}
&nbsp;
function createSizeSetHandler(name, exprFunc) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return function (renderer) <span class="fstat-no" title="function not covered" >{</span></span>
        // PENDING viewport size or window size
        var dpr = <span class="cstat-no" title="statement not covered" >renderer.getDevicePixelRatio();</span>
        // PENDING If multiply dpr ?
        var width = <span class="cstat-no" title="statement not covered" >renderer.getWidth();</span>
        var height = <span class="cstat-no" title="statement not covered" >renderer.getHeight();</span>
        var result = <span class="cstat-no" title="statement not covered" >exprFunc(width, height, dpr);</span>
<span class="cstat-no" title="statement not covered" >        this.setParameter(name, result);</span>
    };
}
&nbsp;
function createSizeParser(name, exprFunc) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return function (renderer) <span class="fstat-no" title="function not covered" >{</span></span>
        var dpr = <span class="cstat-no" title="statement not covered" >renderer.getDevicePixelRatio();</span>
        var width = <span class="cstat-no" title="statement not covered" >renderer.getWidth();</span>
        var height = <span class="cstat-no" title="statement not covered" >renderer.getHeight();</span>
<span class="cstat-no" title="statement not covered" >        return exprFunc(width, height, dpr);</span>
    };
}
&nbsp;
function tryConvertExpr(string) <span class="fstat-no" title="function not covered" >{</span>
    // PENDING
    var exprRes = <span class="cstat-no" title="statement not covered" >/^expr\((.*)\)$/.exec(string);</span>
<span class="cstat-no" title="statement not covered" >    if (exprRes) {</span>
<span class="cstat-no" title="statement not covered" >        try {</span>
            var func = <span class="cstat-no" title="statement not covered" >new Function('width', 'height', 'dpr', 'return ' + exprRes[1]);</span>
            // Try run t
<span class="cstat-no" title="statement not covered" >            func(1, 1);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            return func;</span>
        }
        catch (e) {
<span class="cstat-no" title="statement not covered" >            throw new Error('Invalid expression.');</span>
        }
    }
}
&nbsp;
export default createCompositor;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
