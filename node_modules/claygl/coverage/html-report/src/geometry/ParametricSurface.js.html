<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/geometry/ParametricSurface.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/geometry/</a> ParametricSurface.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">2% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>1/50</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/16</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/2</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">2% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>1/50</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import Geometry from '../Geometry';
&nbsp;
/**
 * @constructor clay.geometry.ParametricSurface
 * @extends clay.Geometry
 * @param {Object} [opt]
 * @param {Object} [generator]
 * @param {Function} generator.x
 * @param {Function} generator.y
 * @param {Function} generator.z
 * @param {Array} [generator.u=[0, 1, 0.05]]
 * @param {Array} [generator.v=[0, 1, 0.05]]
 */
var ParametricSurface = Geometry.extend(
/** @lends clay.geometry.ParametricSurface# */
{
    dynamic: false,
    /**
     * @type {Object}
     */
    generator: null
&nbsp;
}, function() <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    this.build();</span>
},
/** @lends clay.geometry.ParametricSurface.prototype */
{
    /**
     * Build parametric surface geometry
     */
    build: function () <span class="fstat-no" title="function not covered" >{</span>
        var generator = <span class="cstat-no" title="statement not covered" >this.generator;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (!generator || !generator.x || !generator.y || !generator.z) {</span>
<span class="cstat-no" title="statement not covered" >            throw new Error('Invalid generator');</span>
        }
        var xFunc = <span class="cstat-no" title="statement not covered" >generator.x;</span>
        var yFunc = <span class="cstat-no" title="statement not covered" >generator.y;</span>
        var zFunc = <span class="cstat-no" title="statement not covered" >generator.z;</span>
        var uRange = <span class="cstat-no" title="statement not covered" >generator.u || [0, 1, 0.05];</span>
        var vRange = <span class="cstat-no" title="statement not covered" >generator.v || [0, 1, 0.05];</span>
&nbsp;
        var uNum = <span class="cstat-no" title="statement not covered" >Math.floor((uRange[1] - uRange[0] + uRange[2]) / uRange[2]);</span>
        var vNum = <span class="cstat-no" title="statement not covered" >Math.floor((vRange[1] - vRange[0] + vRange[2]) / vRange[2]);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (!isFinite(uNum) || !isFinite(vNum)) {</span>
<span class="cstat-no" title="statement not covered" >            throw new Error('Infinite generator');</span>
        }
&nbsp;
        var vertexNum = <span class="cstat-no" title="statement not covered" >uNum * vNum;</span>
<span class="cstat-no" title="statement not covered" >        this.attributes.position.init(vertexNum);</span>
<span class="cstat-no" title="statement not covered" >        this.attributes.texcoord0.init(vertexNum);</span>
&nbsp;
        var pos = <span class="cstat-no" title="statement not covered" >[];</span>
        var texcoord = <span class="cstat-no" title="statement not covered" >[];</span>
        var nVertex = <span class="cstat-no" title="statement not covered" >0;</span>
<span class="cstat-no" title="statement not covered" >        for (var j = 0; j &lt; vNum; j++) {</span>
<span class="cstat-no" title="statement not covered" >            for (var i = 0; i &lt; uNum; i++) {</span>
                var u = <span class="cstat-no" title="statement not covered" >i * uRange[2] + uRange[0];</span>
                var v = <span class="cstat-no" title="statement not covered" >j * vRange[2] + vRange[0];</span>
<span class="cstat-no" title="statement not covered" >                pos[0] = xFunc(u, v);</span>
<span class="cstat-no" title="statement not covered" >                pos[1] = yFunc(u, v);</span>
<span class="cstat-no" title="statement not covered" >                pos[2] = zFunc(u, v);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                texcoord[0] = i / (uNum - 1);</span>
<span class="cstat-no" title="statement not covered" >                texcoord[1] = j / (vNum - 1);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                this.attributes.position.set(nVertex, pos);</span>
<span class="cstat-no" title="statement not covered" >                this.attributes.texcoord0.set(nVertex, texcoord);</span>
<span class="cstat-no" title="statement not covered" >                nVertex++;</span>
            }
        }
&nbsp;
        var IndicesCtor = <span class="cstat-no" title="statement not covered" >vertexNum &gt; 0xffff ? Uint32Array : Uint16Array;</span>
        var nIndices = <span class="cstat-no" title="statement not covered" >(uNum - 1) * (vNum - 1) * 6;</span>
        var indices = <span class="cstat-no" title="statement not covered" >this.indices = new IndicesCtor(nIndices);</span>
&nbsp;
        var n = <span class="cstat-no" title="statement not covered" >0;</span>
<span class="cstat-no" title="statement not covered" >        for (var j = 0; j &lt; vNum - 1; j++) {</span>
<span class="cstat-no" title="statement not covered" >            for (var i = 0; i &lt; uNum - 1; i++) {</span>
                var i2 = <span class="cstat-no" title="statement not covered" >j * uNum + i;</span>
                var i1 = (<span class="cstat-no" title="statement not covered" >j * uNum + i + 1)</span>;
                var i4 = <span class="cstat-no" title="statement not covered" >(j + 1) * uNum + i + 1;</span>
                var i3 = <span class="cstat-no" title="statement not covered" >(j + 1) * uNum + i;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                indices[n++] = i1;</span>
<span class="cstat-no" title="statement not covered" >                indices[n++] = i2;</span>
<span class="cstat-no" title="statement not covered" >                indices[n++] = i4;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                indices[n++] = i2;</span>
<span class="cstat-no" title="statement not covered" >                indices[n++] = i3;</span>
<span class="cstat-no" title="statement not covered" >                indices[n++] = i4;</span>
            }
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        this.generateVertexNormals();</span>
<span class="cstat-no" title="statement not covered" >        this.updateBoundingBox();</span>
    }
});
&nbsp;
export default ParametricSurface;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
