<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/compositor/Graph.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/compositor/</a> Graph.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">2% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>1/50</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/36</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/7</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">2% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>1/50</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import Base from '../core/Base';
import GraphNode from './CompositorNode';
&nbsp;
/**
 * @constructor clay.compositor.Graph
 * @extends clay.core.Base
 */
var Graph = Base.extend(function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return /** @lends clay.compositor.Graph# */ {</span>
        /**
         * @type {Array.&lt;clay.compositor.CompositorNode&gt;}
         */
        nodes: []
    };
},
/** @lends clay.compositor.Graph.prototype */
{
&nbsp;
    /**
     * Mark to update
     */
    dirty: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
    },
    /**
     * @param {clay.compositor.CompositorNode} node
     */
    addNode: function (node) <span class="fstat-no" title="function not covered" >{</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (this.nodes.indexOf(node) &gt;= 0) {</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        this.nodes.push(node);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
    },
    /**
     * @param  {clay.compositor.CompositorNode|string} node
     */
    removeNode: function (node) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        if (typeof node === 'string') {</span>
<span class="cstat-no" title="statement not covered" >            node = this.getNodeByName(node);</span>
        }
        var idx = <span class="cstat-no" title="statement not covered" >this.nodes.indexOf(node);</span>
<span class="cstat-no" title="statement not covered" >        if (idx &gt;= 0) {</span>
<span class="cstat-no" title="statement not covered" >            this.nodes.splice(idx, 1);</span>
<span class="cstat-no" title="statement not covered" >            this._dirty = true;</span>
        }
    },
    /**
     * @param {string} name
     * @return {clay.compositor.CompositorNode}
     */
    getNodeByName: function (name) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; this.nodes.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >            if (this.nodes[i].name === name) {</span>
<span class="cstat-no" title="statement not covered" >                return this.nodes[i];</span>
            }
        }
    },
    /**
     * Update links of graph
     */
    update: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; this.nodes.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >            this.nodes[i].clear();</span>
        }
        // Traverse all the nodes and build the graph
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; this.nodes.length; i++) {</span>
            var node = <span class="cstat-no" title="statement not covered" >this.nodes[i];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (!node.inputs) {</span>
<span class="cstat-no" title="statement not covered" >                continue;</span>
            }
<span class="cstat-no" title="statement not covered" >            for (var inputName in node.inputs) {</span>
<span class="cstat-no" title="statement not covered" >                if (!node.inputs[inputName]) {</span>
<span class="cstat-no" title="statement not covered" >                    continue;</span>
                }
<span class="cstat-no" title="statement not covered" >                if (node.pass &amp;&amp; !node.pass.material.isUniformEnabled(inputName)) {</span>
<span class="cstat-no" title="statement not covered" >                    console.warn('Pin '  + node.name + '.' + inputName + ' not used.');</span>
<span class="cstat-no" title="statement not covered" >                    continue;</span>
                }
                var fromPinInfo = <span class="cstat-no" title="statement not covered" >node.inputs[inputName];</span>
&nbsp;
                var fromPin = <span class="cstat-no" title="statement not covered" >this.findPin(fromPinInfo);</span>
<span class="cstat-no" title="statement not covered" >                if (fromPin) {</span>
<span class="cstat-no" title="statement not covered" >                    node.link(inputName, fromPin.node, fromPin.pin);</span>
                }
                else {
<span class="cstat-no" title="statement not covered" >                    if (typeof fromPinInfo === 'string') {</span>
<span class="cstat-no" title="statement not covered" >                        console.warn('Node ' + fromPinInfo + ' not exist');</span>
                    }
                    else {
<span class="cstat-no" title="statement not covered" >                        console.warn('Pin of ' + fromPinInfo.node + '.' + fromPinInfo.pin + ' not exist');</span>
                    }
                }
            }
        }
    },
&nbsp;
    findPin: function (input) <span class="fstat-no" title="function not covered" >{</span>
        var node;
        // Try to take input as a directly a node
<span class="cstat-no" title="statement not covered" >        if (typeof input === 'string' || input instanceof GraphNode) {</span>
<span class="cstat-no" title="statement not covered" >            input = {</span>
                node: input
            };
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (typeof input.node === 'string') {</span>
<span class="cstat-no" title="statement not covered" >            for (var i = 0; i &lt; this.nodes.length; i++) {</span>
                var tmp = <span class="cstat-no" title="statement not covered" >this.nodes[i];</span>
<span class="cstat-no" title="statement not covered" >                if (tmp.name === input.node) {</span>
<span class="cstat-no" title="statement not covered" >                    node = tmp;</span>
                }
            }
        }
        else {
<span class="cstat-no" title="statement not covered" >            node = input.node;</span>
        }
<span class="cstat-no" title="statement not covered" >        if (node) {</span>
            var inputPin = <span class="cstat-no" title="statement not covered" >input.pin;</span>
<span class="cstat-no" title="statement not covered" >            if (!inputPin) {</span>
                // Use first pin defaultly
<span class="cstat-no" title="statement not covered" >                if (node.outputs) {</span>
<span class="cstat-no" title="statement not covered" >                    inputPin = Object.keys(node.outputs)[0];</span>
                }
            }
<span class="cstat-no" title="statement not covered" >            if (node.outputs[inputPin]) {</span>
<span class="cstat-no" title="statement not covered" >                return {</span>
                    node: node,
                    pin: inputPin
                };
            }
        }
    }
});
&nbsp;
export default Graph;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
