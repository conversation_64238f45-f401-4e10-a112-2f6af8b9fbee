<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/compositor/TexturePool.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/compositor/</a> TexturePool.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">13.64% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>6/44</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/24</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">14.29% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>1/7</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">13.64% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>6/44</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import Texture2D from '../Texture2D';
import glenum from '../core/glenum';
import util from '../core/util';
&nbsp;
var TexturePool = function () {
&nbsp;
    this._pool = {};
&nbsp;
    this._allocatedTextures = [];
};
&nbsp;
TexturePool.prototype = {
&nbsp;
    constructor: TexturePool,
&nbsp;
    get: function (parameters) <span class="fstat-no" title="function not covered" >{</span>
        var key = <span class="cstat-no" title="statement not covered" >generateKey(parameters);</span>
<span class="cstat-no" title="statement not covered" >        if (!this._pool.hasOwnProperty(key)) {</span>
<span class="cstat-no" title="statement not covered" >            this._pool[key] = [];</span>
        }
        var list = <span class="cstat-no" title="statement not covered" >this._pool[key];</span>
<span class="cstat-no" title="statement not covered" >        if (!list.length) {</span>
            var texture = <span class="cstat-no" title="statement not covered" >new Texture2D(parameters);</span>
<span class="cstat-no" title="statement not covered" >            this._allocatedTextures.push(texture);</span>
<span class="cstat-no" title="statement not covered" >            return texture;</span>
        }
<span class="cstat-no" title="statement not covered" >        return list.pop();</span>
    },
&nbsp;
    put: function (texture) <span class="fstat-no" title="function not covered" >{</span>
        var key = <span class="cstat-no" title="statement not covered" >generateKey(texture);</span>
<span class="cstat-no" title="statement not covered" >        if (!this._pool.hasOwnProperty(key)) {</span>
<span class="cstat-no" title="statement not covered" >            this._pool[key] = [];</span>
        }
        var list = <span class="cstat-no" title="statement not covered" >this._pool[key];</span>
<span class="cstat-no" title="statement not covered" >        list.push(texture);</span>
    },
&nbsp;
    clear: function (renderer) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; this._allocatedTextures.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >            this._allocatedTextures[i].dispose(renderer);</span>
        }
<span class="cstat-no" title="statement not covered" >        this._pool = {};</span>
<span class="cstat-no" title="statement not covered" >        this._allocatedTextures = [];</span>
    }
};
&nbsp;
var defaultParams = {
    width: 512,
    height: 512,
    type: glenum.UNSIGNED_BYTE,
    format: glenum.RGBA,
    wrapS: glenum.CLAMP_TO_EDGE,
    wrapT: glenum.CLAMP_TO_EDGE,
    minFilter: glenum.LINEAR_MIPMAP_LINEAR,
    magFilter: glenum.LINEAR,
    useMipmap: true,
    anisotropic: 1,
    flipY: true,
    unpackAlignment: 4,
    premultiplyAlpha: false
};
&nbsp;
var defaultParamPropList = Object.keys(defaultParams);
&nbsp;
function generateKey(parameters) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    util.defaultsWithPropList(parameters, defaultParams, defaultParamPropList);</span>
<span class="cstat-no" title="statement not covered" >    fallBack(parameters);</span>
&nbsp;
    var key = <span class="cstat-no" title="statement not covered" >'';</span>
<span class="cstat-no" title="statement not covered" >    for (var i = 0; i &lt; defaultParamPropList.length; i++) {</span>
        var name = <span class="cstat-no" title="statement not covered" >defaultParamPropList[i];</span>
        var chunk = <span class="cstat-no" title="statement not covered" >parameters[name].toString();</span>
<span class="cstat-no" title="statement not covered" >        key += chunk;</span>
    }
<span class="cstat-no" title="statement not covered" >    return key;</span>
}
&nbsp;
function fallBack(target) <span class="fstat-no" title="function not covered" >{</span>
&nbsp;
    var IPOT = <span class="cstat-no" title="statement not covered" >isPowerOfTwo(target.width, target.height);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (target.format === glenum.DEPTH_COMPONENT) {</span>
<span class="cstat-no" title="statement not covered" >        target.useMipmap = false;</span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (!IPOT || !target.useMipmap) {</span>
<span class="cstat-no" title="statement not covered" >        if (target.minFilter == glenum.NEAREST_MIPMAP_NEAREST ||</span>
            target.minFilter == glenum.NEAREST_MIPMAP_LINEAR) {
<span class="cstat-no" title="statement not covered" >            target.minFilter = glenum.NEAREST;</span>
        } else <span class="cstat-no" title="statement not covered" >if (</span>
            target.minFilter == glenum.LINEAR_MIPMAP_LINEAR ||
            target.minFilter == glenum.LINEAR_MIPMAP_NEAREST
        ) {
<span class="cstat-no" title="statement not covered" >            target.minFilter = glenum.LINEAR;</span>
        }
    }
<span class="cstat-no" title="statement not covered" >    if (!IPOT) {</span>
<span class="cstat-no" title="statement not covered" >        target.wrapS = glenum.CLAMP_TO_EDGE;</span>
<span class="cstat-no" title="statement not covered" >        target.wrapT = glenum.CLAMP_TO_EDGE;</span>
    }
}
&nbsp;
function isPowerOfTwo(width, height) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return (width &amp; (width-1)) === 0 &amp;&amp;</span>
            (height &amp; (height-1)) === 0;
}
&nbsp;
export default TexturePool;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
