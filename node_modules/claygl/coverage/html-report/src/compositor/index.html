<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/compositor/</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> src/compositor/
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">14.75% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>54/366</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">6.77% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>13/192</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">14.29% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>9/63</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">14.75% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>54/366</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="Compositor.js"><a href="Compositor.js.html">Compositor.js</a></td>
	<td data-value="4.35" class="pic low"><div class="chart"><div class="cover-fill" style="width: 4%;"></div><div class="cover-empty" style="width:96%;"></div></div></td>
	<td data-value="4.35" class="pct low">4.35%</td>
	<td data-value="23" class="abs low">1/23</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="7" class="abs low">0/7</td>
	<td data-value="4.35" class="pct low">4.35%</td>
	<td data-value="23" class="abs low">1/23</td>
	</tr>

<tr>
	<td class="file low" data-value="CompositorNode.js"><a href="CompositorNode.js.html">CompositorNode.js</a></td>
	<td data-value="1.33" class="pic low"><div class="chart"><div class="cover-fill" style="width: 1%;"></div><div class="cover-empty" style="width:99%;"></div></div></td>
	<td data-value="1.33" class="pct low">1.33%</td>
	<td data-value="75" class="abs low">1/75</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="50" class="abs low">0/50</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="13" class="abs low">0/13</td>
	<td data-value="1.33" class="pct low">1.33%</td>
	<td data-value="75" class="abs low">1/75</td>
	</tr>

<tr>
	<td class="file low" data-value="FilterNode.js"><a href="FilterNode.js.html">FilterNode.js</a></td>
	<td data-value="1.32" class="pic low"><div class="chart"><div class="cover-fill" style="width: 1%;"></div><div class="cover-empty" style="width:99%;"></div></div></td>
	<td data-value="1.32" class="pct low">1.32%</td>
	<td data-value="76" class="abs low">1/76</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="32" class="abs low">0/32</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="11" class="abs low">0/11</td>
	<td data-value="1.32" class="pct low">1.32%</td>
	<td data-value="76" class="abs low">1/76</td>
	</tr>

<tr>
	<td class="file low" data-value="Graph.js"><a href="Graph.js.html">Graph.js</a></td>
	<td data-value="2" class="pic low"><div class="chart"><div class="cover-fill" style="width: 2%;"></div><div class="cover-empty" style="width:98%;"></div></div></td>
	<td data-value="2" class="pct low">2%</td>
	<td data-value="50" class="abs low">1/50</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="36" class="abs low">0/36</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="7" class="abs low">0/7</td>
	<td data-value="2" class="pct low">2%</td>
	<td data-value="50" class="abs low">1/50</td>
	</tr>

<tr>
	<td class="file medium" data-value="Pass.js"><a href="Pass.js.html">Pass.js</a></td>
	<td data-value="68.85" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 68%;"></div><div class="cover-empty" style="width:32%;"></div></div></td>
	<td data-value="68.85" class="pct medium">68.85%</td>
	<td data-value="61" class="abs medium">42/61</td>
	<td data-value="38.24" class="pct low">38.24%</td>
	<td data-value="34" class="abs low">13/34</td>
	<td data-value="72.73" class="pct medium">72.73%</td>
	<td data-value="11" class="abs medium">8/11</td>
	<td data-value="68.85" class="pct medium">68.85%</td>
	<td data-value="61" class="abs medium">42/61</td>
	</tr>

<tr>
	<td class="file low" data-value="SceneNode.js"><a href="SceneNode.js.html">SceneNode.js</a></td>
	<td data-value="2.94" class="pic low"><div class="chart"><div class="cover-fill" style="width: 2%;"></div><div class="cover-empty" style="width:98%;"></div></div></td>
	<td data-value="2.94" class="pct low">2.94%</td>
	<td data-value="34" class="abs low">1/34</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="2.94" class="pct low">2.94%</td>
	<td data-value="34" class="abs low">1/34</td>
	</tr>

<tr>
	<td class="file low" data-value="TextureNode.js"><a href="TextureNode.js.html">TextureNode.js</a></td>
	<td data-value="33.33" class="pic low"><div class="chart"><div class="cover-fill" style="width: 33%;"></div><div class="cover-empty" style="width:67%;"></div></div></td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="3" class="abs low">1/3</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="5" class="abs low">0/5</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="3" class="abs low">1/3</td>
	</tr>

<tr>
	<td class="file low" data-value="TexturePool.js"><a href="TexturePool.js.html">TexturePool.js</a></td>
	<td data-value="13.64" class="pic low"><div class="chart"><div class="cover-fill" style="width: 13%;"></div><div class="cover-empty" style="width:87%;"></div></div></td>
	<td data-value="13.64" class="pct low">13.64%</td>
	<td data-value="44" class="abs low">6/44</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="24" class="abs low">0/24</td>
	<td data-value="14.29" class="pct low">14.29%</td>
	<td data-value="7" class="abs low">1/7</td>
	<td data-value="13.64" class="pct low">13.64%</td>
	<td data-value="44" class="abs low">6/44</td>
	</tr>

<tr>
	<td class="file high" data-value="createCompositor.js"><a href="createCompositor.js.html">createCompositor.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
