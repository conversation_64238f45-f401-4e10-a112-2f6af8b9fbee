<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/compositor/SceneNode.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/compositor/</a> SceneNode.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">2.94% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>1/34</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/12</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/2</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">2.94% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>1/34</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import CompositorNode from './CompositorNode';
import glenum from '../core/glenum';
import FrameBuffer from '../FrameBuffer';
&nbsp;
/**
 * @constructor clay.compositor.SceneNode
 * @extends clay.compositor.CompositorNode
 */
var SceneNode = CompositorNode.extend(
/** @lends clay.compositor.SceneNode# */
{
    name: 'scene',
    /**
     * @type {clay.Scene}
     */
    scene: null,
    /**
     * @type {clay.Camera}
     */
    camera: null,
    /**
     * @type {boolean}
     */
    autoUpdateScene: true,
    /**
     * @type {boolean}
     */
    preZ: false
&nbsp;
}, function() <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    this.frameBuffer = new FrameBuffer();</span>
}, {
    render: function(renderer) <span class="fstat-no" title="function not covered" >{</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        this._rendering = true;</span>
        var _gl = <span class="cstat-no" title="statement not covered" >renderer.gl;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        this.trigger('beforerender');</span>
&nbsp;
        var renderInfo;
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (!this.outputs) {</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            renderInfo = renderer.render(this.scene, this.camera, !this.autoUpdateScene, this.preZ);</span>
&nbsp;
        }
        else {
&nbsp;
            var frameBuffer = <span class="cstat-no" title="statement not covered" >this.frameBuffer;</span>
<span class="cstat-no" title="statement not covered" >            for (var name in this.outputs) {</span>
                var parameters = <span class="cstat-no" title="statement not covered" >this.updateParameter(name, renderer);</span>
                var outputInfo = <span class="cstat-no" title="statement not covered" >this.outputs[name];</span>
                var texture = <span class="cstat-no" title="statement not covered" >this._compositor.allocateTexture(parameters);</span>
<span class="cstat-no" title="statement not covered" >                this._outputTextures[name] = texture;</span>
&nbsp;
                var attachment = <span class="cstat-no" title="statement not covered" >outputInfo.attachment || _gl.COLOR_ATTACHMENT0;</span>
<span class="cstat-no" title="statement not covered" >                if (typeof(attachment) == 'string') {</span>
<span class="cstat-no" title="statement not covered" >                    attachment = _gl[attachment];</span>
                }
<span class="cstat-no" title="statement not covered" >                frameBuffer.attach(texture, attachment);</span>
            }
<span class="cstat-no" title="statement not covered" >            frameBuffer.bind(renderer);</span>
&nbsp;
            // MRT Support in chrome
            // https://www.khronos.org/registry/webgl/sdk/tests/conformance/extensions/ext-draw-buffers.html
            var ext = <span class="cstat-no" title="statement not covered" >renderer.getGLExtension('EXT_draw_buffers');</span>
<span class="cstat-no" title="statement not covered" >            if (ext) {</span>
                var bufs = <span class="cstat-no" title="statement not covered" >[];</span>
<span class="cstat-no" title="statement not covered" >                for (var attachment in this.outputs) {</span>
<span class="cstat-no" title="statement not covered" >                    attachment = parseInt(attachment);</span>
<span class="cstat-no" title="statement not covered" >                    if (attachment &gt;= _gl.COLOR_ATTACHMENT0 &amp;&amp; attachment &lt;= _gl.COLOR_ATTACHMENT0 + 8) {</span>
<span class="cstat-no" title="statement not covered" >                        bufs.push(attachment);</span>
                    }
                }
<span class="cstat-no" title="statement not covered" >                ext.drawBuffersEXT(bufs);</span>
            }
&nbsp;
            // Always clear
            // PENDING
<span class="cstat-no" title="statement not covered" >            renderer.saveClear();</span>
<span class="cstat-no" title="statement not covered" >            renderer.clearBit = glenum.DEPTH_BUFFER_BIT | glenum.COLOR_BUFFER_BIT;</span>
<span class="cstat-no" title="statement not covered" >            renderInfo = renderer.render(this.scene, this.camera, !this.autoUpdateScene, this.preZ);</span>
<span class="cstat-no" title="statement not covered" >            renderer.restoreClear();</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            frameBuffer.unbind(renderer);</span>
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        this.trigger('afterrender', renderInfo);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        this._rendering = false;</span>
<span class="cstat-no" title="statement not covered" >        this._rendered = true;</span>
    }
});
&nbsp;
export default SceneNode;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
