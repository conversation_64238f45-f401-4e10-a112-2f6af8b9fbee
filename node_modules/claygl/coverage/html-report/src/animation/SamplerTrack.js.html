<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/animation/SamplerTrack.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/animation/</a> SamplerTrack.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">8% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>16/200</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/82</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">7.14% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>1/14</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">8.25% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>16/194</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
350
351
352
353
354
355
356
357
358
359
360
361
362
363
364
365
366
367
368
369
370
371
372
373
374
375
376
377
378
379
380
381
382
383
384
385
386
387
388
389
390</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// Sampler clip is especially for the animation sampler in glTF
// Use Typed Array can reduce a lot of heap memory
&nbsp;
import glMatrix from '../dep/glmatrix';
var quat = glMatrix.quat;
var vec3 = glMatrix.vec3;
&nbsp;
// lerp function with offset in large array
function vec3lerp(out, a, b, t, oa, ob) <span class="fstat-no" title="function not covered" >{</span>
    var ax = <span class="cstat-no" title="statement not covered" >a[oa];</span>
    var ay = <span class="cstat-no" title="statement not covered" >a[oa + 1];</span>
    var az = <span class="cstat-no" title="statement not covered" >a[oa + 2];</span>
<span class="cstat-no" title="statement not covered" >    out[0] = ax + t * (b[ob] - ax);</span>
<span class="cstat-no" title="statement not covered" >    out[1] = ay + t * (b[ob + 1] - ay);</span>
<span class="cstat-no" title="statement not covered" >    out[2] = az + t * (b[ob + 2] - az);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return out;</span>
}
&nbsp;
function quatSlerp(out, a, b, t, oa, ob) <span class="fstat-no" title="function not covered" >{</span>
    // benchmarks:
    //    http://jsperf.com/quaternion-slerp-implementations
&nbsp;
    var ax = <span class="cstat-no" title="statement not covered" >a[0 + oa],</span> ay = <span class="cstat-no" title="statement not covered" >a[1 + oa],</span> az = <span class="cstat-no" title="statement not covered" >a[2 + oa],</span> aw = <span class="cstat-no" title="statement not covered" >a[3 + oa],</span>
        bx = <span class="cstat-no" title="statement not covered" >b[0 + ob],</span> by = <span class="cstat-no" title="statement not covered" >b[1 + ob],</span> bz = <span class="cstat-no" title="statement not covered" >b[2 + ob],</span> bw = <span class="cstat-no" title="statement not covered" >b[3 + ob];</span>
&nbsp;
    var omega, cosom, sinom, scale0, scale1;
&nbsp;
    // calc cosine
<span class="cstat-no" title="statement not covered" >    cosom = ax * bx + ay * by + az * bz + aw * bw;</span>
    // adjust signs (if necessary)
<span class="cstat-no" title="statement not covered" >    if (cosom &lt; 0.0) {</span>
<span class="cstat-no" title="statement not covered" >        cosom = -cosom;</span>
<span class="cstat-no" title="statement not covered" >        bx = - bx;</span>
<span class="cstat-no" title="statement not covered" >        by = - by;</span>
<span class="cstat-no" title="statement not covered" >        bz = - bz;</span>
<span class="cstat-no" title="statement not covered" >        bw = - bw;</span>
    }
    // calculate coefficients
<span class="cstat-no" title="statement not covered" >    if ((1.0 - cosom) &gt; 0.000001) {</span>
        // standard case (slerp)
<span class="cstat-no" title="statement not covered" >        omega  = Math.acos(cosom);</span>
<span class="cstat-no" title="statement not covered" >        sinom  = Math.sin(omega);</span>
<span class="cstat-no" title="statement not covered" >        scale0 = Math.sin((1.0 - t) * omega) / sinom;</span>
<span class="cstat-no" title="statement not covered" >        scale1 = Math.sin(t * omega) / sinom;</span>
    }
    else {
        // 'from' and 'to' quaternions are very close
        //  ... so we can do a linear interpolation
<span class="cstat-no" title="statement not covered" >        scale0 = 1.0 - t;</span>
<span class="cstat-no" title="statement not covered" >        scale1 = t;</span>
    }
    // calculate final values
<span class="cstat-no" title="statement not covered" >    out[0] = scale0 * ax + scale1 * bx;</span>
<span class="cstat-no" title="statement not covered" >    out[1] = scale0 * ay + scale1 * by;</span>
<span class="cstat-no" title="statement not covered" >    out[2] = scale0 * az + scale1 * bz;</span>
<span class="cstat-no" title="statement not covered" >    out[3] = scale0 * aw + scale1 * bw;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return out;</span>
}
&nbsp;
/**
 * SamplerTrack manages `position`, `rotation`, `scale` tracks in animation of single scene node.
 * @constructor
 * @alias clay.animation.SamplerTrack
 * @param {Object} [opts]
 * @param {string} [opts.name] Track name
 * @param {clay.Node} [opts.target] Target node's transform will updated automatically
 */
var SamplerTrack = function (opts) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    opts = opts || {};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.name = opts.name || '';</span>
    /**
     * @param {clay.Node}
     */
<span class="cstat-no" title="statement not covered" >    this.target = opts.target || null;</span>
    /**
     * @type {Array}
     */
<span class="cstat-no" title="statement not covered" >    this.position = vec3.create();</span>
    /**
     * Rotation is represented by a quaternion
     * @type {Array}
     */
<span class="cstat-no" title="statement not covered" >    this.rotation = quat.create();</span>
    /**
     * @type {Array}
     */
<span class="cstat-no" title="statement not covered" >    this.scale = vec3.fromValues(1, 1, 1);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.channels = {</span>
        time: null,
        position: null,
        rotation: null,
        scale: null
    };
&nbsp;
<span class="cstat-no" title="statement not covered" >    this._cacheKey = 0;</span>
<span class="cstat-no" title="statement not covered" >    this._cacheTime = 0;</span>
};
&nbsp;
SamplerTrack.prototype.setTime = function (time) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    if (!this.channels.time) {</span>
<span class="cstat-no" title="statement not covered" >        return;</span>
    }
    var channels = <span class="cstat-no" title="statement not covered" >this.channels;</span>
    var len = <span class="cstat-no" title="statement not covered" >channels.time.length;</span>
    var key = <span class="cstat-no" title="statement not covered" >-1;</span>
    // Only one frame
<span class="cstat-no" title="statement not covered" >    if (len === 1) {</span>
<span class="cstat-no" title="statement not covered" >        if (channels.rotation) {</span>
<span class="cstat-no" title="statement not covered" >            quat.copy(this.rotation, channels.rotation);</span>
        }
<span class="cstat-no" title="statement not covered" >        if (channels.position) {</span>
<span class="cstat-no" title="statement not covered" >            vec3.copy(this.position, channels.position);</span>
        }
<span class="cstat-no" title="statement not covered" >        if (channels.scale) {</span>
<span class="cstat-no" title="statement not covered" >            vec3.copy(this.scale, channels.scale);</span>
        }
<span class="cstat-no" title="statement not covered" >        return;</span>
    }
    // Clamp
    else <span class="cstat-no" title="statement not covered" >if (time &lt;= channels.time[0]) {</span>
<span class="cstat-no" title="statement not covered" >        time = channels.time[0];</span>
<span class="cstat-no" title="statement not covered" >        key = 0;</span>
    }
    else <span class="cstat-no" title="statement not covered" >if (time &gt;= channels.time[len - 1]) {</span>
<span class="cstat-no" title="statement not covered" >        time = channels.time[len - 1];</span>
<span class="cstat-no" title="statement not covered" >        key = len - 2;</span>
    }
    else {
<span class="cstat-no" title="statement not covered" >        if (time &lt; this._cacheTime) {</span>
            var s = <span class="cstat-no" title="statement not covered" >Math.min(len - 1, this._cacheKey + 1);</span>
<span class="cstat-no" title="statement not covered" >            for (var i = s; i &gt;= 0; i--) {</span>
<span class="cstat-no" title="statement not covered" >                if (channels.time[i - 1] &lt;= time &amp;&amp; channels.time[i] &gt; time) {</span>
<span class="cstat-no" title="statement not covered" >                    key = i - 1;</span>
<span class="cstat-no" title="statement not covered" >                    break;</span>
                }
            }
        }
        else {
<span class="cstat-no" title="statement not covered" >            for (var i = this._cacheKey; i &lt; len - 1; i++) {</span>
<span class="cstat-no" title="statement not covered" >                if (channels.time[i] &lt;= time &amp;&amp; channels.time[i + 1] &gt; time) {</span>
<span class="cstat-no" title="statement not covered" >                    key = i;</span>
<span class="cstat-no" title="statement not covered" >                    break;</span>
                }
            }
        }
    }
<span class="cstat-no" title="statement not covered" >    if (key &gt; -1) {</span>
<span class="cstat-no" title="statement not covered" >        this._cacheKey = key;</span>
<span class="cstat-no" title="statement not covered" >        this._cacheTime = time;</span>
        var start = <span class="cstat-no" title="statement not covered" >key;</span>
        var end = <span class="cstat-no" title="statement not covered" >key + 1;</span>
        var startTime = <span class="cstat-no" title="statement not covered" >channels.time[start];</span>
        var endTime = <span class="cstat-no" title="statement not covered" >channels.time[end];</span>
        var range = <span class="cstat-no" title="statement not covered" >endTime - startTime;</span>
        var percent = <span class="cstat-no" title="statement not covered" >range === 0 ? 0 : (time - startTime) / range;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (channels.rotation) {</span>
<span class="cstat-no" title="statement not covered" >            quatSlerp(this.rotation, channels.rotation, channels.rotation, percent, start * 4, end * 4);</span>
        }
<span class="cstat-no" title="statement not covered" >        if (channels.position) {</span>
<span class="cstat-no" title="statement not covered" >            vec3lerp(this.position, channels.position, channels.position, percent, start * 3, end * 3);</span>
        }
<span class="cstat-no" title="statement not covered" >        if (channels.scale) {</span>
<span class="cstat-no" title="statement not covered" >            vec3lerp(this.scale, channels.scale, channels.scale, percent, start * 3, end * 3);</span>
        }
    }
    // Loop handling
<span class="cstat-no" title="statement not covered" >    if (key === len - 2) {</span>
<span class="cstat-no" title="statement not covered" >        this._cacheKey = 0;</span>
<span class="cstat-no" title="statement not covered" >        this._cacheTime = 0;</span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.updateTarget();</span>
};
&nbsp;
/**
 * Update transform of target node manually
 */
SamplerTrack.prototype.updateTarget = function () <span class="fstat-no" title="function not covered" >{</span>
    var channels = <span class="cstat-no" title="statement not covered" >this.channels;</span>
<span class="cstat-no" title="statement not covered" >    if (this.target) {</span>
        // Only update target prop if have data.
<span class="cstat-no" title="statement not covered" >        if (channels.position) {</span>
<span class="cstat-no" title="statement not covered" >            this.target.position.setArray(this.position);</span>
        }
<span class="cstat-no" title="statement not covered" >        if (channels.rotation) {</span>
<span class="cstat-no" title="statement not covered" >            this.target.rotation.setArray(this.rotation);</span>
        }
<span class="cstat-no" title="statement not covered" >        if (channels.scale) {</span>
<span class="cstat-no" title="statement not covered" >            this.target.scale.setArray(this.scale);</span>
        }
    }
};
&nbsp;
/**
 * @return {number}
 */
SamplerTrack.prototype.getMaxTime = function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return this.channels.time[this.channels.time.length - 1];</span>
};
&nbsp;
/**
 * @param {number} startTime
 * @param {number} endTime
 * @return {clay.animation.SamplerTrack}
 */
SamplerTrack.prototype.getSubTrack = function (startTime, endTime) <span class="fstat-no" title="function not covered" >{</span>
&nbsp;
    var subClip = <span class="cstat-no" title="statement not covered" >new SamplerTrack({</span>
        name: this.name
    });
    var minTime = <span class="cstat-no" title="statement not covered" >this.channels.time[0];</span>
<span class="cstat-no" title="statement not covered" >    startTime = Math.min(Math.max(startTime, minTime), this.life);</span>
<span class="cstat-no" title="statement not covered" >    endTime = Math.min(Math.max(endTime, minTime), this.life);</span>
&nbsp;
    var rangeStart = <span class="cstat-no" title="statement not covered" >this._findRange(startTime);</span>
    var rangeEnd = <span class="cstat-no" title="statement not covered" >this._findRange(endTime);</span>
&nbsp;
    var count = <span class="cstat-no" title="statement not covered" >rangeEnd[0] - rangeStart[0] + 1;</span>
<span class="cstat-no" title="statement not covered" >    if (rangeStart[1] === 0 &amp;&amp; rangeEnd[1] === 0) {</span>
<span class="cstat-no" title="statement not covered" >        count -= 1;</span>
    }
<span class="cstat-no" title="statement not covered" >    if (this.channels.rotation) {</span>
<span class="cstat-no" title="statement not covered" >        subClip.channels.rotation = new Float32Array(count * 4);</span>
    }
<span class="cstat-no" title="statement not covered" >    if (this.channels.position) {</span>
<span class="cstat-no" title="statement not covered" >        subClip.channels.position = new Float32Array(count * 3);</span>
    }
<span class="cstat-no" title="statement not covered" >    if (this.channels.scale) {</span>
<span class="cstat-no" title="statement not covered" >        subClip.channels.scale = new Float32Array(count * 3);</span>
    }
<span class="cstat-no" title="statement not covered" >    if (this.channels.time) {</span>
<span class="cstat-no" title="statement not covered" >        subClip.channels.time = new Float32Array(count);</span>
    }
    // Clip at the start
<span class="cstat-no" title="statement not covered" >    this.setTime(startTime);</span>
<span class="cstat-no" title="statement not covered" >    for (var i = 0; i &lt; 3; i++) {</span>
<span class="cstat-no" title="statement not covered" >        subClip.channels.rotation[i] = this.rotation[i];</span>
<span class="cstat-no" title="statement not covered" >        subClip.channels.position[i] = this.position[i];</span>
<span class="cstat-no" title="statement not covered" >        subClip.channels.scale[i] = this.scale[i];</span>
    }
<span class="cstat-no" title="statement not covered" >    subClip.channels.time[0] = 0;</span>
<span class="cstat-no" title="statement not covered" >    subClip.channels.rotation[3] = this.rotation[3];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    for (var i = 1; i &lt; count-1; i++) {</span>
        var i2;
<span class="cstat-no" title="statement not covered" >        for (var j = 0; j &lt; 3; j++) {</span>
<span class="cstat-no" title="statement not covered" >            i2 = rangeStart[0] + i;</span>
<span class="cstat-no" title="statement not covered" >            subClip.channels.rotation[i * 4 + j] = this.channels.rotation[i2 * 4 + j];</span>
<span class="cstat-no" title="statement not covered" >            subClip.channels.position[i * 3 + j] = this.channels.position[i2 * 3 + j];</span>
<span class="cstat-no" title="statement not covered" >            subClip.channels.scale[i * 3 + j] = this.channels.scale[i2 * 3 + j];</span>
        }
<span class="cstat-no" title="statement not covered" >        subClip.channels.time[i] = this.channels.time[i2] - startTime;</span>
<span class="cstat-no" title="statement not covered" >        subClip.channels.rotation[i * 4 + 3] = this.channels.rotation[i2 * 4 + 3];</span>
    }
    // Clip at the end
<span class="cstat-no" title="statement not covered" >    this.setTime(endTime);</span>
<span class="cstat-no" title="statement not covered" >    for (var i = 0; i &lt; 3; i++) {</span>
<span class="cstat-no" title="statement not covered" >        subClip.channels.rotation[(count - 1) * 4 + i] = this.rotation[i];</span>
<span class="cstat-no" title="statement not covered" >        subClip.channels.position[(count - 1) * 3 + i] = this.position[i];</span>
<span class="cstat-no" title="statement not covered" >        subClip.channels.scale[(count - 1) * 3 + i] = this.scale[i];</span>
    }
<span class="cstat-no" title="statement not covered" >    subClip.channels.time[(count - 1)] = endTime - startTime;</span>
<span class="cstat-no" title="statement not covered" >    subClip.channels.rotation[(count - 1) * 4 + 3] = this.rotation[3];</span>
&nbsp;
    // TODO set back ?
<span class="cstat-no" title="statement not covered" >    subClip.life = endTime - startTime;</span>
<span class="cstat-no" title="statement not covered" >    return subClip;</span>
};
&nbsp;
SamplerTrack.prototype._findRange = function (time) <span class="fstat-no" title="function not covered" >{</span>
    var channels = <span class="cstat-no" title="statement not covered" >this.channels;</span>
    var len = <span class="cstat-no" title="statement not covered" >channels.time.length;</span>
    var start = <span class="cstat-no" title="statement not covered" >-1;</span>
<span class="cstat-no" title="statement not covered" >    for (var i = 0; i &lt; len - 1; i++) {</span>
<span class="cstat-no" title="statement not covered" >        if (channels.time[i] &lt;= time &amp;&amp; channels.time[i+1] &gt; time) {</span>
<span class="cstat-no" title="statement not covered" >            start = i;</span>
        }
    }
    var percent = <span class="cstat-no" title="statement not covered" >0;</span>
<span class="cstat-no" title="statement not covered" >    if (start &gt;= 0) {</span>
        var startTime = <span class="cstat-no" title="statement not covered" >channels.time[start];</span>
        var endTime = <span class="cstat-no" title="statement not covered" >channels.time[start+1];</span>
        var percent = <span class="cstat-no" title="statement not covered" >(time-startTime) / (endTime-startTime);</span>
    }
    // Percent [0, 1)
<span class="cstat-no" title="statement not covered" >    return [start, percent];</span>
};
&nbsp;
/**
 * 1D blending between two clips
 * @function
 * @param  {clay.animation.SamplerTrack|clay.animation.TransformTrack} c1
 * @param  {clay.animation.SamplerTrack|clay.animation.TransformTrack} c2
 * @param  {number} w
 */
SamplerTrack.prototype.blend1D = function (t1, t2, w) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    vec3.lerp(this.position, t1.position, t2.position, w);</span>
<span class="cstat-no" title="statement not covered" >    vec3.lerp(this.scale, t1.scale, t2.scale, w);</span>
<span class="cstat-no" title="statement not covered" >    quat.slerp(this.rotation, t1.rotation, t2.rotation, w);</span>
};
/**
 * 2D blending between three clips
 * @function
 * @param  {clay.animation.SamplerTrack|clay.animation.TransformTrack} c1
 * @param  {clay.animation.SamplerTrack|clay.animation.TransformTrack} c2
 * @param  {clay.animation.SamplerTrack|clay.animation.TransformTrack} c3
 * @param  {number} f
 * @param  {number} g
 */
SamplerTrack.prototype.blend2D = (function () {
    var q1 = quat.create();
    var q2 = quat.create();
    return function (t1, t2, t3, f, g) <span class="fstat-no" title="function not covered" >{</span>
        var a = <span class="cstat-no" title="statement not covered" >1 - f - g;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        this.position[0] = t1.position[0] * a + t2.position[0] * f + t3.position[0] * g;</span>
<span class="cstat-no" title="statement not covered" >        this.position[1] = t1.position[1] * a + t2.position[1] * f + t3.position[1] * g;</span>
<span class="cstat-no" title="statement not covered" >        this.position[2] = t1.position[2] * a + t2.position[2] * f + t3.position[2] * g;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        this.scale[0] = t1.scale[0] * a + t2.scale[0] * f + t3.scale[0] * g;</span>
<span class="cstat-no" title="statement not covered" >        this.scale[1] = t1.scale[1] * a + t2.scale[1] * f + t3.scale[1] * g;</span>
<span class="cstat-no" title="statement not covered" >        this.scale[2] = t1.scale[2] * a + t2.scale[2] * f + t3.scale[2] * g;</span>
&nbsp;
        // http://msdn.microsoft.com/en-us/library/windows/desktop/bb205403(v=vs.85).aspx
        // http://msdn.microsoft.com/en-us/library/windows/desktop/microsoft.directx_sdk.quaternion.xmquaternionbarycentric(v=vs.85).aspx
        var s = <span class="cstat-no" title="statement not covered" >f + g;</span>
<span class="cstat-no" title="statement not covered" >        if (s === 0) {</span>
<span class="cstat-no" title="statement not covered" >            quat.copy(this.rotation, t1.rotation);</span>
        }
        else {
<span class="cstat-no" title="statement not covered" >            quat.slerp(q1, t1.rotation, t2.rotation, s);</span>
<span class="cstat-no" title="statement not covered" >            quat.slerp(q2, t1.rotation, t3.rotation, s);</span>
<span class="cstat-no" title="statement not covered" >            quat.slerp(this.rotation, q1, q2, g / s);</span>
        }
    };
})();
/**
 * Additive blending between two clips
 * @function
 * @param  {clay.animation.SamplerTrack|clay.animation.TransformTrack} c1
 * @param  {clay.animation.SamplerTrack|clay.animation.TransformTrack} c2
 */
SamplerTrack.prototype.additiveBlend = function (t1, t2) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    vec3.add(this.position, t1.position, t2.position);</span>
<span class="cstat-no" title="statement not covered" >    vec3.add(this.scale, t1.scale, t2.scale);</span>
<span class="cstat-no" title="statement not covered" >    quat.multiply(this.rotation, t2.rotation, t1.rotation);</span>
};
/**
 * Subtractive blending between two clips
 * @function
 * @param  {clay.animation.SamplerTrack|clay.animation.TransformTrack} c1
 * @param  {clay.animation.SamplerTrack|clay.animation.TransformTrack} c2
 */
SamplerTrack.prototype.subtractiveBlend = function (t1, t2) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    vec3.sub(this.position, t1.position, t2.position);</span>
<span class="cstat-no" title="statement not covered" >    vec3.sub(this.scale, t1.scale, t2.scale);</span>
<span class="cstat-no" title="statement not covered" >    quat.invert(this.rotation, t2.rotation);</span>
<span class="cstat-no" title="statement not covered" >    quat.multiply(this.rotation, this.rotation, t1.rotation);</span>
};
&nbsp;
/**
 * Clone a new SamplerTrack
 * @return {clay.animation.SamplerTrack}
 */
SamplerTrack.prototype.clone = function () <span class="fstat-no" title="function not covered" >{</span>
    var track = <span class="cstat-no" title="statement not covered" >SamplerTrack.prototype.clone.call(this);</span>
<span class="cstat-no" title="statement not covered" >    track.channels = {</span>
        time: this.channels.time || null,
        position: this.channels.position || null,
        rotation: this.channels.rotation || null,
        scale: this.channels.scale || null
    };
<span class="cstat-no" title="statement not covered" >    vec3.copy(track.position, this.position);</span>
<span class="cstat-no" title="statement not covered" >    quat.copy(track.rotation, this.rotation);</span>
<span class="cstat-no" title="statement not covered" >    vec3.copy(track.scale, this.scale);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    track.target = this.target;</span>
<span class="cstat-no" title="statement not covered" >    track.updateTarget();</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return track;</span>
&nbsp;
};
&nbsp;
export default SamplerTrack;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
