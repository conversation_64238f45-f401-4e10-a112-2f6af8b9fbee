<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/animation/TransformTrack.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/animation/</a> TransformTrack.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">17.39% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>20/115</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/46</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">6.67% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>1/15</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">17.39% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>20/115</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import Clip from './Clip';
&nbsp;
import glMatrix from '../dep/glmatrix';
var quat = glMatrix.quat;
var vec3 = glMatrix.vec3;
&nbsp;
function keyframeSort(a, b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return a.time - b.time;</span>
}
&nbsp;
var TransformTrack = function (opts) <span class="fstat-no" title="function not covered" >{</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.name = opts.name || '';</span>
    //[{
    //  time: //ms
    //  position:  // optional
    //  rotation:  // optional
    //  scale:     // optional
    //}]
<span class="cstat-no" title="statement not covered" >    this.keyFrames = [];</span>
<span class="cstat-no" title="statement not covered" >    if (opts.keyFrames) {</span>
<span class="cstat-no" title="statement not covered" >        this.addKeyFrames(opts.keyFrames);</span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.position = vec3.create();</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.rotation = quat.create();</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.scale = vec3.fromValues(1, 1, 1);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this._cacheKey = 0;</span>
<span class="cstat-no" title="statement not covered" >    this._cacheTime = 0;</span>
};
&nbsp;
TransformTrack.prototype = Object.create(Clip.prototype);
&nbsp;
TransformTrack.prototype.constructor = TransformTrack;
&nbsp;
TransformTrack.prototype.step = function (time, dTime, silent) <span class="fstat-no" title="function not covered" >{</span>
&nbsp;
    var ret = <span class="cstat-no" title="statement not covered" >Clip.prototype.step.call(this, time, dTime, true);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (ret !== 'finish') {</span>
<span class="cstat-no" title="statement not covered" >        this.setTime(this.getElapsedTime());</span>
    }
&nbsp;
    // PENDING Schedule
<span class="cstat-no" title="statement not covered" >    if (!silent &amp;&amp; ret !== 'paused') {</span>
<span class="cstat-no" title="statement not covered" >        this.fire('frame');</span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    return ret;</span>
};
&nbsp;
TransformTrack.prototype.setTime = function (time) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    this._interpolateField(time, 'position');</span>
<span class="cstat-no" title="statement not covered" >    this._interpolateField(time, 'rotation');</span>
<span class="cstat-no" title="statement not covered" >    this._interpolateField(time, 'scale');</span>
};
&nbsp;
TransformTrack.prototype.getMaxTime = function () <span class="fstat-no" title="function not covered" >{</span>
    var kf = <span class="cstat-no" title="statement not covered" >this.keyFrames[this.keyFrames.length - 1];</span>
<span class="cstat-no" title="statement not covered" >    return kf ? kf.time : 0;</span>
};
&nbsp;
TransformTrack.prototype.addKeyFrame = function (kf) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    for (var i = 0; i &lt; this.keyFrames.length - 1; i++) {</span>
        var prevFrame = <span class="cstat-no" title="statement not covered" >this.keyFrames[i];</span>
        var nextFrame = <span class="cstat-no" title="statement not covered" >this.keyFrames[i + 1];</span>
<span class="cstat-no" title="statement not covered" >        if (prevFrame.time &lt;= kf.time &amp;&amp; nextFrame.time &gt;= kf.time) {</span>
<span class="cstat-no" title="statement not covered" >            this.keyFrames.splice(i, 0, kf);</span>
<span class="cstat-no" title="statement not covered" >            return i;</span>
        }
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.life = kf.time;</span>
<span class="cstat-no" title="statement not covered" >    this.keyFrames.push(kf);</span>
};
&nbsp;
TransformTrack.prototype.addKeyFrames = function (kfs) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    for (var i = 0; i &lt; kfs.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >        this.keyFrames.push(kfs[i]);</span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.keyFrames.sort(keyframeSort);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.life = this.keyFrames[this.keyFrames.length - 1].time;</span>
};
&nbsp;
TransformTrack.prototype._interpolateField = function (time, fieldName) <span class="fstat-no" title="function not covered" >{</span>
    var kfs = <span class="cstat-no" title="statement not covered" >this.keyFrames;</span>
    var len = <span class="cstat-no" title="statement not covered" >kfs.length;</span>
    var start;
    var end;
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (!kfs.length) {</span>
<span class="cstat-no" title="statement not covered" >        return;</span>
    }
<span class="cstat-no" title="statement not covered" >    if (time &lt; kfs[0].time || time &gt; kfs[kfs.length-1].time) {</span>
<span class="cstat-no" title="statement not covered" >        return;</span>
    }
<span class="cstat-no" title="statement not covered" >    if (time &lt; this._cacheTime) {</span>
        var s = <span class="cstat-no" title="statement not covered" >this._cacheKey &gt;= len-1 ? len-1 : this._cacheKey+1;</span>
<span class="cstat-no" title="statement not covered" >        for (var i = s; i &gt;= 0; i--) {</span>
<span class="cstat-no" title="statement not covered" >            if (kfs[i].time &lt;= time &amp;&amp; kfs[i][fieldName]) {</span>
<span class="cstat-no" title="statement not covered" >                start = kfs[i];</span>
<span class="cstat-no" title="statement not covered" >                this._cacheKey = i;</span>
<span class="cstat-no" title="statement not covered" >                this._cacheTime = time;</span>
            } else <span class="cstat-no" title="statement not covered" >if (kfs[i][fieldName]) {</span>
<span class="cstat-no" title="statement not covered" >                end = kfs[i];</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
            }
        }
    } else {
<span class="cstat-no" title="statement not covered" >        for (var i = this._cacheKey; i &lt; len; i++) {</span>
<span class="cstat-no" title="statement not covered" >            if (kfs[i].time &lt;= time &amp;&amp; kfs[i][fieldName]) {</span>
<span class="cstat-no" title="statement not covered" >                start = kfs[i];</span>
<span class="cstat-no" title="statement not covered" >                this._cacheKey = i;</span>
<span class="cstat-no" title="statement not covered" >                this._cacheTime = time;</span>
            } else <span class="cstat-no" title="statement not covered" >if (kfs[i][fieldName]) {</span>
<span class="cstat-no" title="statement not covered" >                end = kfs[i];</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
            }
        }
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (start &amp;&amp; end) {</span>
        var percent = <span class="cstat-no" title="statement not covered" >(time - start.time) / (end.time - start.time);</span>
<span class="cstat-no" title="statement not covered" >        percent = Math.max(Math.min(percent, 1), 0);</span>
<span class="cstat-no" title="statement not covered" >        if (fieldName === 'rotation') {</span>
<span class="cstat-no" title="statement not covered" >            quat.slerp(this[fieldName], start[fieldName], end[fieldName], percent);</span>
        } else {
<span class="cstat-no" title="statement not covered" >            vec3.lerp(this[fieldName], start[fieldName], end[fieldName], percent);</span>
        }
    } else {
<span class="cstat-no" title="statement not covered" >        this._cacheKey = 0;</span>
<span class="cstat-no" title="statement not covered" >        this._cacheTime = 0;</span>
    }
};
&nbsp;
TransformTrack.prototype.blend1D = function (t1, t2, w) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    vec3.lerp(this.position, t1.position, t2.position, w);</span>
<span class="cstat-no" title="statement not covered" >    vec3.lerp(this.scale, t1.scale, t2.scale, w);</span>
<span class="cstat-no" title="statement not covered" >    quat.slerp(this.rotation, t1.rotation, t2.rotation, w);</span>
};
&nbsp;
TransformTrack.prototype.blend2D = (function () {
    var q1 = quat.create();
    var q2 = quat.create();
    return function (t1, t2, t3, f, g) <span class="fstat-no" title="function not covered" >{</span>
        var a = <span class="cstat-no" title="statement not covered" >1 - f - g;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        this.position[0] = t1.position[0] * a + t2.position[0] * f + t3.position[0] * g;</span>
<span class="cstat-no" title="statement not covered" >        this.position[1] = t1.position[1] * a + t2.position[1] * f + t3.position[1] * g;</span>
<span class="cstat-no" title="statement not covered" >        this.position[2] = t1.position[2] * a + t2.position[2] * f + t3.position[2] * g;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        this.scale[0] = t1.scale[0] * a + t2.scale[0] * f + t3.scale[0] * g;</span>
<span class="cstat-no" title="statement not covered" >        this.scale[1] = t1.scale[1] * a + t2.scale[1] * f + t3.scale[1] * g;</span>
<span class="cstat-no" title="statement not covered" >        this.scale[2] = t1.scale[2] * a + t2.scale[2] * f + t3.scale[2] * g;</span>
&nbsp;
        // http://msdn.microsoft.com/en-us/library/windows/desktop/bb205403(v=vs.85).aspx
        // http://msdn.microsoft.com/en-us/library/windows/desktop/microsoft.directx_sdk.quaternion.xmquaternionbarycentric(v=vs.85).aspx
        var s = <span class="cstat-no" title="statement not covered" >f + g;</span>
<span class="cstat-no" title="statement not covered" >        if (s === 0) {</span>
<span class="cstat-no" title="statement not covered" >            quat.copy(this.rotation, t1.rotation);</span>
        } else {
<span class="cstat-no" title="statement not covered" >            quat.slerp(q1, t1.rotation, t2.rotation, s);</span>
<span class="cstat-no" title="statement not covered" >            quat.slerp(q2, t1.rotation, c3.rotation, s);</span>
<span class="cstat-no" title="statement not covered" >            quat.slerp(this.rotation, q1, q2, g / s);</span>
        }
    };
})();
&nbsp;
TransformTrack.prototype.additiveBlend = function (t1, t2) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    vec3.add(this.position, t1.position, t2.position);</span>
<span class="cstat-no" title="statement not covered" >    vec3.add(this.scale, t1.scale, t2.scale);</span>
<span class="cstat-no" title="statement not covered" >    quat.multiply(this.rotation, t2.rotation, t1.rotation);</span>
};
&nbsp;
TransformTrack.prototype.subtractiveBlend = function (t1, t2) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    vec3.sub(this.position, t1.position, t2.position);</span>
<span class="cstat-no" title="statement not covered" >    vec3.sub(this.scale, t1.scale, t2.scale);</span>
<span class="cstat-no" title="statement not covered" >    quat.invert(this.rotation, t2.rotation);</span>
<span class="cstat-no" title="statement not covered" >    quat.multiply(this.rotation, this.rotation, t1.rotation);</span>
};
&nbsp;
TransformTrack.prototype.getSubClip = function (startTime, endTime) <span class="fstat-no" title="function not covered" >{</span>
    // TODO
<span class="cstat-no" title="statement not covered" >    console.warn('TODO');</span>
};
&nbsp;
TransformTrack.prototype.clone = function () <span class="fstat-no" title="function not covered" >{</span>
    var track = <span class="cstat-no" title="statement not covered" >TransformTrack.prototype.clone.call(this);</span>
<span class="cstat-no" title="statement not covered" >    track.keyFrames = this.keyFrames;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    vec3.copy(track.position, this.position);</span>
<span class="cstat-no" title="statement not covered" >    quat.copy(track.rotation, this.rotation);</span>
<span class="cstat-no" title="statement not covered" >    vec3.copy(track.scale, this.scale);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return track;</span>
};
&nbsp;
&nbsp;
export default TransformTrack;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Sun Jan 07 2018 14:10:25 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
