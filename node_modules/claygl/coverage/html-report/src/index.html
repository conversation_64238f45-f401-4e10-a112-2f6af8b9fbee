<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../index.html">all files</a> src/
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">59.87% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>1913/3195</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">45.81% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>743/1622</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">55.01% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>247/449</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">59.84% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>1907/3187</span>
      </div>
    </div>
  </div>
  <div class='status-line medium'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="Camera.js"><a href="Camera.js.html">Camera.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="34" class="abs high">34/34</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="2" class="abs medium">1/2</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="9" class="abs high">9/9</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="34" class="abs high">34/34</td>
	</tr>

<tr>
	<td class="file high" data-value="FrameBuffer.js"><a href="FrameBuffer.js.html">FrameBuffer.js</a></td>
	<td data-value="81.21" class="pic high"><div class="chart"><div class="cover-fill" style="width: 81%;"></div><div class="cover-empty" style="width:19%;"></div></div></td>
	<td data-value="81.21" class="pct high">81.21%</td>
	<td data-value="165" class="abs high">134/165</td>
	<td data-value="66.98" class="pct medium">66.98%</td>
	<td data-value="106" class="abs medium">71/106</td>
	<td data-value="92.31" class="pct high">92.31%</td>
	<td data-value="13" class="abs high">12/13</td>
	<td data-value="81.21" class="pct high">81.21%</td>
	<td data-value="165" class="abs high">134/165</td>
	</tr>

<tr>
	<td class="file medium" data-value="Geometry.js"><a href="Geometry.js.html">Geometry.js</a></td>
	<td data-value="77.82" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 77%;"></div><div class="cover-empty" style="width:23%;"></div></div></td>
	<td data-value="77.82" class="pct medium">77.82%</td>
	<td data-value="514" class="abs medium">400/514</td>
	<td data-value="57.76" class="pct medium">57.76%</td>
	<td data-value="161" class="abs medium">93/161</td>
	<td data-value="65.22" class="pct medium">65.22%</td>
	<td data-value="46" class="abs medium">30/46</td>
	<td data-value="77.76" class="pct medium">77.76%</td>
	<td data-value="508" class="abs medium">395/508</td>
	</tr>

<tr>
	<td class="file high" data-value="Joint.js"><a href="Joint.js.html">Joint.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	</tr>

<tr>
	<td class="file low" data-value="Light.js"><a href="Light.js.html">Light.js</a></td>
	<td data-value="25" class="pic low"><div class="chart"><div class="cover-fill" style="width: 25%;"></div><div class="cover-empty" style="width:75%;"></div></div></td>
	<td data-value="25" class="pct low">25%</td>
	<td data-value="8" class="abs low">2/8</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="2" class="abs medium">1/2</td>
	<td data-value="25" class="pct low">25%</td>
	<td data-value="8" class="abs low">2/8</td>
	</tr>

<tr>
	<td class="file high" data-value="Material.js"><a href="Material.js.html">Material.js</a></td>
	<td data-value="81.71" class="pic high"><div class="chart"><div class="cover-fill" style="width: 81%;"></div><div class="cover-empty" style="width:19%;"></div></div></td>
	<td data-value="81.71" class="pct high">81.71%</td>
	<td data-value="164" class="abs high">134/164</td>
	<td data-value="65" class="pct medium">65%</td>
	<td data-value="100" class="abs medium">65/100</td>
	<td data-value="88.46" class="pct high">88.46%</td>
	<td data-value="26" class="abs high">23/26</td>
	<td data-value="81.71" class="pct high">81.71%</td>
	<td data-value="164" class="abs high">134/164</td>
	</tr>

<tr>
	<td class="file high" data-value="Mesh.js"><a href="Mesh.js.html">Mesh.js</a></td>
	<td data-value="91.3" class="pic high"><div class="chart"><div class="cover-fill" style="width: 91%;"></div><div class="cover-empty" style="width:9%;"></div></div></td>
	<td data-value="91.3" class="pct high">91.3%</td>
	<td data-value="23" class="abs high">21/23</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="9" class="abs low">3/9</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="4" class="abs medium">3/4</td>
	<td data-value="91.3" class="pct high">91.3%</td>
	<td data-value="23" class="abs high">21/23</td>
	</tr>

<tr>
	<td class="file medium" data-value="Node.js"><a href="Node.js.html">Node.js</a></td>
	<td data-value="54.46" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 54%;"></div><div class="cover-empty" style="width:46%;"></div></div></td>
	<td data-value="54.46" class="pct medium">54.46%</td>
	<td data-value="213" class="abs medium">116/213</td>
	<td data-value="52.22" class="pct medium">52.22%</td>
	<td data-value="90" class="abs medium">47/90</td>
	<td data-value="60" class="pct medium">60%</td>
	<td data-value="40" class="abs medium">24/40</td>
	<td data-value="54.46" class="pct medium">54.46%</td>
	<td data-value="213" class="abs medium">116/213</td>
	</tr>

<tr>
	<td class="file high" data-value="Renderable.js"><a href="Renderable.js.html">Renderable.js</a></td>
	<td data-value="96.43" class="pic high"><div class="chart"><div class="cover-fill" style="width: 96%;"></div><div class="cover-empty" style="width:4%;"></div></div></td>
	<td data-value="96.43" class="pct high">96.43%</td>
	<td data-value="28" class="abs high">27/28</td>
	<td data-value="81.82" class="pct high">81.82%</td>
	<td data-value="11" class="abs high">9/11</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="6" class="abs high">6/6</td>
	<td data-value="96.43" class="pct high">96.43%</td>
	<td data-value="28" class="abs high">27/28</td>
	</tr>

<tr>
	<td class="file medium" data-value="Renderer.js"><a href="Renderer.js.html">Renderer.js</a></td>
	<td data-value="70.02" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 70%;"></div><div class="cover-empty" style="width:30%;"></div></div></td>
	<td data-value="70.02" class="pct medium">70.02%</td>
	<td data-value="447" class="abs medium">313/447</td>
	<td data-value="61.96" class="pct medium">61.96%</td>
	<td data-value="276" class="abs medium">171/276</td>
	<td data-value="46.51" class="pct low">46.51%</td>
	<td data-value="43" class="abs low">20/43</td>
	<td data-value="70.02" class="pct medium">70.02%</td>
	<td data-value="447" class="abs medium">313/447</td>
	</tr>

<tr>
	<td class="file medium" data-value="Scene.js"><a href="Scene.js.html">Scene.js</a></td>
	<td data-value="71.9" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 71%;"></div><div class="cover-empty" style="width:29%;"></div></div></td>
	<td data-value="71.9" class="pct medium">71.9%</td>
	<td data-value="210" class="abs medium">151/210</td>
	<td data-value="60.19" class="pct medium">60.19%</td>
	<td data-value="108" class="abs medium">65/108</td>
	<td data-value="75.76" class="pct medium">75.76%</td>
	<td data-value="33" class="abs medium">25/33</td>
	<td data-value="71.9" class="pct medium">71.9%</td>
	<td data-value="210" class="abs medium">151/210</td>
	</tr>

<tr>
	<td class="file high" data-value="Shader.js"><a href="Shader.js.html">Shader.js</a></td>
	<td data-value="89.05" class="pic high"><div class="chart"><div class="cover-fill" style="width: 89%;"></div><div class="cover-empty" style="width:11%;"></div></div></td>
	<td data-value="89.05" class="pct high">89.05%</td>
	<td data-value="274" class="abs high">244/274</td>
	<td data-value="81.74" class="pct high">81.74%</td>
	<td data-value="115" class="abs high">94/115</td>
	<td data-value="74.42" class="pct medium">74.42%</td>
	<td data-value="43" class="abs medium">32/43</td>
	<td data-value="89.01" class="pct high">89.01%</td>
	<td data-value="273" class="abs high">243/273</td>
	</tr>

<tr>
	<td class="file low" data-value="Skeleton.js"><a href="Skeleton.js.html">Skeleton.js</a></td>
	<td data-value="5.71" class="pic low"><div class="chart"><div class="cover-fill" style="width: 5%;"></div><div class="cover-empty" style="width:95%;"></div></div></td>
	<td data-value="5.71" class="pct low">5.71%</td>
	<td data-value="105" class="abs low">6/105</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="32" class="abs low">0/32</td>
	<td data-value="7.14" class="pct low">7.14%</td>
	<td data-value="14" class="abs low">1/14</td>
	<td data-value="5.71" class="pct low">5.71%</td>
	<td data-value="105" class="abs low">6/105</td>
	</tr>

<tr>
	<td class="file medium" data-value="StandardMaterial.js"><a href="StandardMaterial.js.html">StandardMaterial.js</a></td>
	<td data-value="70.37" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 70%;"></div><div class="cover-empty" style="width:30%;"></div></div></td>
	<td data-value="70.37" class="pct medium">70.37%</td>
	<td data-value="54" class="abs medium">38/54</td>
	<td data-value="35" class="pct low">35%</td>
	<td data-value="20" class="abs low">7/20</td>
	<td data-value="87.5" class="pct high">87.5%</td>
	<td data-value="16" class="abs high">14/16</td>
	<td data-value="70.37" class="pct medium">70.37%</td>
	<td data-value="54" class="abs medium">38/54</td>
	</tr>

<tr>
	<td class="file high" data-value="StaticGeometry.js"><a href="StaticGeometry.js.html">StaticGeometry.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	</tr>

<tr>
	<td class="file high" data-value="Texture.js"><a href="Texture.js.html">Texture.js</a></td>
	<td data-value="86.52" class="pic high"><div class="chart"><div class="cover-fill" style="width: 86%;"></div><div class="cover-empty" style="width:14%;"></div></div></td>
	<td data-value="86.52" class="pct high">86.52%</td>
	<td data-value="89" class="abs high">77/89</td>
	<td data-value="69.44" class="pct medium">69.44%</td>
	<td data-value="36" class="abs medium">25/36</td>
	<td data-value="84.21" class="pct high">84.21%</td>
	<td data-value="19" class="abs high">16/19</td>
	<td data-value="86.52" class="pct high">86.52%</td>
	<td data-value="89" class="abs high">77/89</td>
	</tr>

<tr>
	<td class="file medium" data-value="Texture2D.js"><a href="Texture2D.js.html">Texture2D.js</a></td>
	<td data-value="77.11" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 77%;"></div><div class="cover-empty" style="width:23%;"></div></div></td>
	<td data-value="77.11" class="pct medium">77.11%</td>
	<td data-value="83" class="abs medium">64/83</td>
	<td data-value="77.55" class="pct medium">77.55%</td>
	<td data-value="49" class="abs medium">38/49</td>
	<td data-value="80" class="pct high">80%</td>
	<td data-value="15" class="abs high">12/15</td>
	<td data-value="77.11" class="pct medium">77.11%</td>
	<td data-value="83" class="abs medium">64/83</td>
	</tr>

<tr>
	<td class="file medium" data-value="TextureCube.js"><a href="TextureCube.js.html">TextureCube.js</a></td>
	<td data-value="61.36" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 61%;"></div><div class="cover-empty" style="width:39%;"></div></div></td>
	<td data-value="61.36" class="pct medium">61.36%</td>
	<td data-value="88" class="abs medium">54/88</td>
	<td data-value="55.22" class="pct medium">55.22%</td>
	<td data-value="67" class="abs medium">37/67</td>
	<td data-value="58.82" class="pct medium">58.82%</td>
	<td data-value="17" class="abs medium">10/17</td>
	<td data-value="61.36" class="pct medium">61.36%</td>
	<td data-value="88" class="abs medium">54/88</td>
	</tr>

<tr>
	<td class="file medium" data-value="Timeline.js"><a href="Timeline.js.html">Timeline.js</a></td>
	<td data-value="78.13" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 78%;"></div><div class="cover-empty" style="width:22%;"></div></div></td>
	<td data-value="78.13" class="pct medium">78.13%</td>
	<td data-value="64" class="abs medium">50/64</td>
	<td data-value="58.62" class="pct medium">58.62%</td>
	<td data-value="29" class="abs medium">17/29</td>
	<td data-value="64.29" class="pct medium">64.29%</td>
	<td data-value="14" class="abs medium">9/14</td>
	<td data-value="78.13" class="pct medium">78.13%</td>
	<td data-value="64" class="abs medium">50/64</td>
	</tr>

<tr>
	<td class="file low" data-value="application.js"><a href="application.js.html">application.js</a></td>
	<td data-value="5.46" class="pic low"><div class="chart"><div class="cover-fill" style="width: 5%;"></div><div class="cover-empty" style="width:95%;"></div></div></td>
	<td data-value="5.46" class="pct low">5.46%</td>
	<td data-value="476" class="abs low">26/476</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="333" class="abs low">0/333</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="73" class="abs low">0/73</td>
	<td data-value="5.47" class="pct low">5.47%</td>
	<td data-value="475" class="abs low">26/475</td>
	</tr>

<tr>
	<td class="file high" data-value="claygl.js"><a href="claygl.js.html">claygl.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="18" class="abs high">18/18</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="18" class="abs high">18/18</td>
	</tr>

<tr>
	<td class="file low" data-value="createCompositor.js"><a href="createCompositor.js.html">createCompositor.js</a></td>
	<td data-value="0.74" class="pic low"><div class="chart"><div class="cover-fill" style="width: 0%;"></div><div class="cover-empty" style="width:100%;"></div></div></td>
	<td data-value="0.74" class="pct low">0.74%</td>
	<td data-value="135" class="abs low">1/135</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="78" class="abs low">0/78</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="16" class="abs low">0/16</td>
	<td data-value="0.74" class="pct low">0.74%</td>
	<td data-value="135" class="abs low">1/135</td>
	</tr>

<tr>
	<td class="file high" data-value="version.js"><a href="version.js.html">version.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
