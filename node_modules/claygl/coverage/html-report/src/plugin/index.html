<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/plugin/</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> src/plugin/
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">13.8% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>69/500</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">4.07% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>9/221</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">15.85% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>13/82</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">13.8% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>69/500</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="FreeControl.js"><a href="FreeControl.js.html">FreeControl.js</a></td>
	<td data-value="1.22" class="pic low"><div class="chart"><div class="cover-fill" style="width: 1%;"></div><div class="cover-empty" style="width:99%;"></div></div></td>
	<td data-value="1.22" class="pct low">1.22%</td>
	<td data-value="82" class="abs low">1/82</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="59" class="abs low">0/59</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="11" class="abs low">0/11</td>
	<td data-value="1.22" class="pct low">1.22%</td>
	<td data-value="82" class="abs low">1/82</td>
	</tr>

<tr>
	<td class="file low" data-value="GestureMgr.js"><a href="GestureMgr.js.html">GestureMgr.js</a></td>
	<td data-value="7.69" class="pic low"><div class="chart"><div class="cover-fill" style="width: 7%;"></div><div class="cover-empty" style="width:93%;"></div></div></td>
	<td data-value="7.69" class="pct low">7.69%</td>
	<td data-value="39" class="abs low">3/39</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="22" class="abs low">0/22</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="7.69" class="pct low">7.69%</td>
	<td data-value="39" class="abs low">3/39</td>
	</tr>

<tr>
	<td class="file low" data-value="InfinitePlane.js"><a href="InfinitePlane.js.html">InfinitePlane.js</a></td>
	<td data-value="18.06" class="pic low"><div class="chart"><div class="cover-fill" style="width: 18%;"></div><div class="cover-empty" style="width:82%;"></div></div></td>
	<td data-value="18.06" class="pct low">18.06%</td>
	<td data-value="72" class="abs low">13/72</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="25" class="pct low">25%</td>
	<td data-value="4" class="abs low">1/4</td>
	<td data-value="18.06" class="pct low">18.06%</td>
	<td data-value="72" class="abs low">13/72</td>
	</tr>

<tr>
	<td class="file low" data-value="OrbitControl.js"><a href="OrbitControl.js.html">OrbitControl.js</a></td>
	<td data-value="1.2" class="pic low"><div class="chart"><div class="cover-fill" style="width: 1%;"></div><div class="cover-empty" style="width:99%;"></div></div></td>
	<td data-value="1.2" class="pct low">1.2%</td>
	<td data-value="249" class="abs low">3/249</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="110" class="abs low">0/110</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="42" class="abs low">0/42</td>
	<td data-value="1.2" class="pct low">1.2%</td>
	<td data-value="249" class="abs low">3/249</td>
	</tr>

<tr>
	<td class="file high" data-value="Skybox.js"><a href="Skybox.js.html">Skybox.js</a></td>
	<td data-value="87.1" class="pic high"><div class="chart"><div class="cover-fill" style="width: 87%;"></div><div class="cover-empty" style="width:13%;"></div></div></td>
	<td data-value="87.1" class="pct high">87.1%</td>
	<td data-value="31" class="abs high">27/31</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="10" class="abs medium">5/10</td>
	<td data-value="77.78" class="pct medium">77.78%</td>
	<td data-value="9" class="abs medium">7/9</td>
	<td data-value="87.1" class="pct high">87.1%</td>
	<td data-value="31" class="abs high">27/31</td>
	</tr>

<tr>
	<td class="file high" data-value="Skydome.js"><a href="Skydome.js.html">Skydome.js</a></td>
	<td data-value="81.48" class="pic high"><div class="chart"><div class="cover-fill" style="width: 81%;"></div><div class="cover-empty" style="width:19%;"></div></div></td>
	<td data-value="81.48" class="pct high">81.48%</td>
	<td data-value="27" class="abs high">22/27</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="8" class="abs medium">4/8</td>
	<td data-value="62.5" class="pct medium">62.5%</td>
	<td data-value="8" class="abs medium">5/8</td>
	<td data-value="81.48" class="pct high">81.48%</td>
	<td data-value="27" class="abs high">22/27</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
