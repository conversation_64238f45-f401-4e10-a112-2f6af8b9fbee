<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/particle/</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> src/particle/
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">10.13% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>16/158</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/44</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/19</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">10.13% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>16/158</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="Emitter.js"><a href="Emitter.js.html">Emitter.js</a></td>
	<td data-value="17.14" class="pic low"><div class="chart"><div class="cover-fill" style="width: 17%;"></div><div class="cover-empty" style="width:83%;"></div></div></td>
	<td data-value="17.14" class="pct low">17.14%</td>
	<td data-value="35" class="abs low">6/35</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="18" class="abs low">0/18</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="17.14" class="pct low">17.14%</td>
	<td data-value="35" class="abs low">6/35</td>
	</tr>

<tr>
	<td class="file high" data-value="Field.js"><a href="Field.js.html">Field.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	</tr>

<tr>
	<td class="file low" data-value="ForceField.js"><a href="ForceField.js.html">ForceField.js</a></td>
	<td data-value="40" class="pic low"><div class="chart"><div class="cover-fill" style="width: 40%;"></div><div class="cover-empty" style="width:60%;"></div></div></td>
	<td data-value="40" class="pct low">40%</td>
	<td data-value="5" class="abs low">2/5</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="40" class="pct low">40%</td>
	<td data-value="5" class="abs low">2/5</td>
	</tr>

<tr>
	<td class="file low" data-value="Particle.js"><a href="Particle.js.html">Particle.js</a></td>
	<td data-value="18.75" class="pic low"><div class="chart"><div class="cover-fill" style="width: 18%;"></div><div class="cover-empty" style="width:82%;"></div></div></td>
	<td data-value="18.75" class="pct low">18.75%</td>
	<td data-value="16" class="abs low">3/16</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="18.75" class="pct low">18.75%</td>
	<td data-value="16" class="abs low">3/16</td>
	</tr>

<tr>
	<td class="file low" data-value="ParticleRenderable.js"><a href="ParticleRenderable.js.html">ParticleRenderable.js</a></td>
	<td data-value="3" class="pic low"><div class="chart"><div class="cover-fill" style="width: 3%;"></div><div class="cover-empty" style="width:97%;"></div></div></td>
	<td data-value="3" class="pct low">3%</td>
	<td data-value="100" class="abs low">3/100</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="20" class="abs low">0/20</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="11" class="abs low">0/11</td>
	<td data-value="3" class="pct low">3%</td>
	<td data-value="100" class="abs low">3/100</td>
	</tr>

<tr>
	<td class="file high" data-value="particle.glsl.js"><a href="particle.glsl.js.html">particle.glsl.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
