<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/particle/ParticleRenderable.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/particle/</a> ParticleRenderable.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">3% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>3/100</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/20</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/11</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">3% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>3/100</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import Renderable from '../Renderable';
&nbsp;
import Geometry from '../Geometry';
import Material from '../Material';
import Shader from '../Shader';
&nbsp;
import particleEssl from './particle.glsl.js';
Shader['import'](particleEssl);
&nbsp;
var particleShader = new Shader(Shader.source('clay.particle.vertex'), Shader.source('clay.particle.fragment'));
&nbsp;
/**
 * @constructor clay.particle.ParticleRenderable
 * @extends clay.Renderable
 *
 * @example
 *     var particleRenderable = new clay.particle.ParticleRenderable({
 *         spriteAnimationTileX: 4,
 *         spriteAnimationTileY: 4,
 *         spriteAnimationRepeat: 1
 *     });
 *     scene.add(particleRenderable);
 *     // Enable uv animation in the shader
 *     particleRenderable.material.define('both', 'UV_ANIMATION');
 *     var Emitter = clay.particle.Emitter;
 *     var Vector3 = clay.Vector3;
 *     var emitter = new Emitter({
 *         max: 2000,
 *         amount: 100,
 *         life: Emitter.random1D(10, 20),
 *         position: Emitter.vector(new Vector3()),
 *         velocity: Emitter.random3D(new Vector3(-10, 0, -10), new Vector3(10, 0, 10));
 *     });
 *     particleRenderable.addEmitter(emitter);
 *     var gravityField = new clay.particle.ForceField();
 *     gravityField.force.y = -10;
 *     particleRenderable.addField(gravityField);
 *     ...
 *     animation.on('frame', function(frameTime) {
 *         particleRenderable.updateParticles(frameTime);
 *         renderer.render(scene, camera);
 *     });
 */
var ParticleRenderable = Renderable.extend(/** @lends clay.particle.ParticleRenderable# */ {
    /**
     * @type {boolean}
     */
    loop: true,
    /**
     * @type {boolean}
     */
    oneshot: false,
    /**
     * Duration of particle system in milliseconds
     * @type {number}
     */
    duration: 1,
&nbsp;
    // UV Animation
    /**
     * @type {number}
     */
    spriteAnimationTileX: 1,
    /**
     * @type {number}
     */
    spriteAnimationTileY: 1,
    /**
     * @type {number}
     */
    spriteAnimationRepeat: 0,
&nbsp;
    mode: Renderable.POINTS,
&nbsp;
    ignorePicking: true,
&nbsp;
    _elapsedTime: 0,
&nbsp;
    _emitting: true
&nbsp;
}, function()<span class="fstat-no" title="function not covered" >{</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.geometry = new Geometry({</span>
        dynamic: true
    });
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (!this.material) {</span>
<span class="cstat-no" title="statement not covered" >        this.material = new Material({</span>
            shader: particleShader,
            transparent: true,
            depthMask: false
        });
&nbsp;
<span class="cstat-no" title="statement not covered" >        this.material.enableTexture('sprite');</span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    this._particles = [];</span>
<span class="cstat-no" title="statement not covered" >    this._fields = [];</span>
<span class="cstat-no" title="statement not covered" >    this._emitters = [];</span>
},
/** @lends clay.particle.ParticleRenderable.prototype */
{
&nbsp;
    culling: false,
&nbsp;
    frustumCulling: false,
&nbsp;
    castShadow: false,
    receiveShadow: false,
&nbsp;
    /**
     * Add emitter
     * @param {clay.particle.Emitter} emitter
     */
    addEmitter: function(emitter) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        this._emitters.push(emitter);</span>
    },
&nbsp;
    /**
     * Remove emitter
     * @param {clay.particle.Emitter} emitter
     */
    removeEmitter: function(emitter) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        this._emitters.splice(this._emitters.indexOf(emitter), 1);</span>
    },
&nbsp;
    /**
     * Add field
     * @param {clay.particle.Field} field
     */
    addField: function(field) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        this._fields.push(field);</span>
    },
&nbsp;
    /**
     * Remove field
     * @param {clay.particle.Field} field
     */
    removeField: function(field) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        this._fields.splice(this._fields.indexOf(field), 1);</span>
    },
&nbsp;
    /**
     * Reset the particle system.
     */
    reset: function() <span class="fstat-no" title="function not covered" >{</span>
        // Put all the particles back
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; this._particles.length; i++) {</span>
            var p = <span class="cstat-no" title="statement not covered" >this._particles[i];</span>
<span class="cstat-no" title="statement not covered" >            p.emitter.kill(p);</span>
        }
<span class="cstat-no" title="statement not covered" >        this._particles.length = 0;</span>
<span class="cstat-no" title="statement not covered" >        this._elapsedTime = 0;</span>
<span class="cstat-no" title="statement not covered" >        this._emitting = true;</span>
    },
&nbsp;
    /**
     * @param  {number} deltaTime
     */
    updateParticles: function(deltaTime) <span class="fstat-no" title="function not covered" >{</span>
&nbsp;
        // MS =&gt; Seconds
<span class="cstat-no" title="statement not covered" >        deltaTime /= 1000;</span>
<span class="cstat-no" title="statement not covered" >        this._elapsedTime += deltaTime;</span>
&nbsp;
        var particles = <span class="cstat-no" title="statement not covered" >this._particles;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (this._emitting) {</span>
<span class="cstat-no" title="statement not covered" >            for (var i = 0; i &lt; this._emitters.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >                this._emitters[i].emit(particles);</span>
            }
<span class="cstat-no" title="statement not covered" >            if (this.oneshot) {</span>
<span class="cstat-no" title="statement not covered" >                this._emitting = false;</span>
            }
        }
&nbsp;
        // Aging
        var len = <span class="cstat-no" title="statement not covered" >particles.length;</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; len;) {</span>
            var p = <span class="cstat-no" title="statement not covered" >particles[i];</span>
<span class="cstat-no" title="statement not covered" >            p.age += deltaTime;</span>
<span class="cstat-no" title="statement not covered" >            if (p.age &gt;= p.life) {</span>
<span class="cstat-no" title="statement not covered" >                p.emitter.kill(p);</span>
<span class="cstat-no" title="statement not covered" >                particles[i] = particles[len-1];</span>
<span class="cstat-no" title="statement not covered" >                particles.pop();</span>
<span class="cstat-no" title="statement not covered" >                len--;</span>
            } else {
<span class="cstat-no" title="statement not covered" >                i++;</span>
            }
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; len; i++) {</span>
            // Update
            var p = <span class="cstat-no" title="statement not covered" >particles[i];</span>
<span class="cstat-no" title="statement not covered" >            if (this._fields.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >                for (var j = 0; j &lt; this._fields.length; j++) {</span>
<span class="cstat-no" title="statement not covered" >                    this._fields[j].applyTo(p.velocity, p.position, p.weight, deltaTime);</span>
                }
            }
<span class="cstat-no" title="statement not covered" >            p.update(deltaTime);</span>
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        this._updateVertices();</span>
    },
&nbsp;
    _updateVertices: function() <span class="fstat-no" title="function not covered" >{</span>
        var geometry = <span class="cstat-no" title="statement not covered" >this.geometry;</span>
        // If has uv animation
        var animTileX = <span class="cstat-no" title="statement not covered" >this.spriteAnimationTileX;</span>
        var animTileY = <span class="cstat-no" title="statement not covered" >this.spriteAnimationTileY;</span>
        var animRepeat = <span class="cstat-no" title="statement not covered" >this.spriteAnimationRepeat;</span>
        var nUvAnimFrame = <span class="cstat-no" title="statement not covered" >animTileY * animTileX * animRepeat;</span>
        var hasUvAnimation = <span class="cstat-no" title="statement not covered" >nUvAnimFrame &gt; 1;</span>
        var positions = <span class="cstat-no" title="statement not covered" >geometry.attributes.position.value;</span>
        // Put particle status in normal
        var normals = <span class="cstat-no" title="statement not covered" >geometry.attributes.normal.value;</span>
        var uvs = <span class="cstat-no" title="statement not covered" >geometry.attributes.texcoord0.value;</span>
        var uvs2 = <span class="cstat-no" title="statement not covered" >geometry.attributes.texcoord1.value;</span>
&nbsp;
        var len = <span class="cstat-no" title="statement not covered" >this._particles.length;</span>
<span class="cstat-no" title="statement not covered" >        if (!positions || positions.length !== len * 3) {</span>
            // TODO Optimize
<span class="cstat-no" title="statement not covered" >            positions = geometry.attributes.position.value = new Float32Array(len * 3);</span>
<span class="cstat-no" title="statement not covered" >            normals = geometry.attributes.normal.value = new Float32Array(len * 3);</span>
<span class="cstat-no" title="statement not covered" >            if (hasUvAnimation) {</span>
<span class="cstat-no" title="statement not covered" >                uvs = geometry.attributes.texcoord0.value = new Float32Array(len * 2);</span>
<span class="cstat-no" title="statement not covered" >                uvs2 = geometry.attributes.texcoord1.value = new Float32Array(len * 2);</span>
            }
        }
&nbsp;
        var invAnimTileX = <span class="cstat-no" title="statement not covered" >1 / animTileX;</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; len; i++) {</span>
            var particle = <span class="cstat-no" title="statement not covered" >this._particles[i];</span>
            var offset = <span class="cstat-no" title="statement not covered" >i * 3;</span>
<span class="cstat-no" title="statement not covered" >            for (var j = 0; j &lt; 3; j++) {</span>
<span class="cstat-no" title="statement not covered" >                positions[offset + j] = particle.position.array[j];</span>
<span class="cstat-no" title="statement not covered" >                normals[offset] = particle.age / particle.life;</span>
                // normals[offset + 1] = particle.rotation;
<span class="cstat-no" title="statement not covered" >                normals[offset + 1] = 0;</span>
<span class="cstat-no" title="statement not covered" >                normals[offset + 2] = particle.spriteSize;</span>
            }
            var offset2 = <span class="cstat-no" title="statement not covered" >i * 2;</span>
<span class="cstat-no" title="statement not covered" >            if (hasUvAnimation) {</span>
                // TODO
                var p = <span class="cstat-no" title="statement not covered" >particle.age / particle.life;</span>
                var stage = <span class="cstat-no" title="statement not covered" >Math.round(p * (nUvAnimFrame - 1)) * animRepeat;</span>
                var v = <span class="cstat-no" title="statement not covered" >Math.floor(stage * invAnimTileX);</span>
                var u = <span class="cstat-no" title="statement not covered" >stage - v * animTileX;</span>
<span class="cstat-no" title="statement not covered" >                uvs[offset2] = u / animTileX;</span>
<span class="cstat-no" title="statement not covered" >                uvs[offset2 + 1] = 1 - v / animTileY;</span>
<span class="cstat-no" title="statement not covered" >                uvs2[offset2] = (u + 1) / animTileX;</span>
<span class="cstat-no" title="statement not covered" >                uvs2[offset2 + 1] = 1 - (v + 1) / animTileY;</span>
            }
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        geometry.dirty();</span>
    },
&nbsp;
    /**
     * @return {boolean}
     */
    isFinished: function() <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return this._elapsedTime &gt; this.duration &amp;&amp; !this.loop;</span>
    },
&nbsp;
    /**
     * @param  {clay.Renderer} renderer
     */
    dispose: function(renderer) <span class="fstat-no" title="function not covered" >{</span>
        // Put all the particles back
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; this._particles.length; i++) {</span>
            var p = <span class="cstat-no" title="statement not covered" >this._particles[i];</span>
<span class="cstat-no" title="statement not covered" >            p.emitter.kill(p);</span>
        }
<span class="cstat-no" title="statement not covered" >        this.geometry.dispose(renderer);</span>
        // TODO Dispose texture ?
    },
&nbsp;
    /**
     * @return {clay.particle.ParticleRenderable}
     */
    clone: function() <span class="fstat-no" title="function not covered" >{</span>
        var particleRenderable = <span class="cstat-no" title="statement not covered" >new ParticleRenderable({</span>
            material: this.material
        });
<span class="cstat-no" title="statement not covered" >        particleRenderable.loop = this.loop;</span>
<span class="cstat-no" title="statement not covered" >        particleRenderable.duration = this.duration;</span>
<span class="cstat-no" title="statement not covered" >        particleRenderable.oneshot = this.oneshot;</span>
<span class="cstat-no" title="statement not covered" >        particleRenderable.spriteAnimationRepeat = this.spriteAnimationRepeat;</span>
<span class="cstat-no" title="statement not covered" >        particleRenderable.spriteAnimationTileY = this.spriteAnimationTileY;</span>
<span class="cstat-no" title="statement not covered" >        particleRenderable.spriteAnimationTileX = this.spriteAnimationTileX;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        particleRenderable.position.copy(this.position);</span>
<span class="cstat-no" title="statement not covered" >        particleRenderable.rotation.copy(this.rotation);</span>
<span class="cstat-no" title="statement not covered" >        particleRenderable.scale.copy(this.scale);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; this._children.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >            particleRenderable.add(this._children[i].clone());</span>
        }
<span class="cstat-no" title="statement not covered" >        return particleRenderable;</span>
    }
});
&nbsp;
export default ParticleRenderable;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
