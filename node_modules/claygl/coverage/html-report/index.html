<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      /
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">40.74% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>5380/13206</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">28.25% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>1280/4531</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">31.84% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>560/1759</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">40.94% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>5250/12825</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="src/"><a href="src/index.html">src/</a></td>
	<td data-value="59.87" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 59%;"></div><div class="cover-empty" style="width:41%;"></div></div></td>
	<td data-value="59.87" class="pct medium">59.87%</td>
	<td data-value="3195" class="abs medium">1913/3195</td>
	<td data-value="45.81" class="pct low">45.81%</td>
	<td data-value="1622" class="abs low">743/1622</td>
	<td data-value="55.01" class="pct medium">55.01%</td>
	<td data-value="449" class="abs medium">247/449</td>
	<td data-value="59.84" class="pct medium">59.84%</td>
	<td data-value="3187" class="abs medium">1907/3187</td>
	</tr>

<tr>
	<td class="file low" data-value="src/animation/"><a href="src/animation/index.html">src/animation/</a></td>
	<td data-value="25.31" class="pic low"><div class="chart"><div class="cover-fill" style="width: 25%;"></div><div class="cover-empty" style="width:75%;"></div></div></td>
	<td data-value="25.31" class="pct low">25.31%</td>
	<td data-value="818" class="abs low">207/818</td>
	<td data-value="20.25" class="pct low">20.25%</td>
	<td data-value="400" class="abs low">81/400</td>
	<td data-value="18.42" class="pct low">18.42%</td>
	<td data-value="114" class="abs low">21/114</td>
	<td data-value="25.68" class="pct low">25.68%</td>
	<td data-value="806" class="abs low">207/806</td>
	</tr>

<tr>
	<td class="file low" data-value="src/async/"><a href="src/async/index.html">src/async/</a></td>
	<td data-value="13.64" class="pic low"><div class="chart"><div class="cover-fill" style="width: 13%;"></div><div class="cover-empty" style="width:87%;"></div></div></td>
	<td data-value="13.64" class="pct low">13.64%</td>
	<td data-value="154" class="abs low">21/154</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="54" class="abs low">0/54</td>
	<td data-value="3.57" class="pct low">3.57%</td>
	<td data-value="28" class="abs low">1/28</td>
	<td data-value="13.64" class="pct low">13.64%</td>
	<td data-value="154" class="abs low">21/154</td>
	</tr>

<tr>
	<td class="file high" data-value="src/camera/"><a href="src/camera/index.html">src/camera/</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="32" class="abs high">32/32</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="6" class="abs high">6/6</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="32" class="abs high">32/32</td>
	</tr>

<tr>
	<td class="file low" data-value="src/compositor/"><a href="src/compositor/index.html">src/compositor/</a></td>
	<td data-value="14.75" class="pic low"><div class="chart"><div class="cover-fill" style="width: 14%;"></div><div class="cover-empty" style="width:86%;"></div></div></td>
	<td data-value="14.75" class="pct low">14.75%</td>
	<td data-value="366" class="abs low">54/366</td>
	<td data-value="6.77" class="pct low">6.77%</td>
	<td data-value="192" class="abs low">13/192</td>
	<td data-value="14.29" class="pct low">14.29%</td>
	<td data-value="63" class="abs low">9/63</td>
	<td data-value="14.75" class="pct low">14.75%</td>
	<td data-value="366" class="abs low">54/366</td>
	</tr>

<tr>
	<td class="file low" data-value="src/core/"><a href="src/core/index.html">src/core/</a></td>
	<td data-value="35.17" class="pic low"><div class="chart"><div class="cover-fill" style="width: 35%;"></div><div class="cover-empty" style="width:65%;"></div></div></td>
	<td data-value="35.17" class="pct low">35.17%</td>
	<td data-value="526" class="abs low">185/526</td>
	<td data-value="18.79" class="pct low">18.79%</td>
	<td data-value="298" class="abs low">56/298</td>
	<td data-value="44.59" class="pct low">44.59%</td>
	<td data-value="74" class="abs low">33/74</td>
	<td data-value="35.65" class="pct low">35.65%</td>
	<td data-value="519" class="abs low">185/519</td>
	</tr>

<tr>
	<td class="file medium" data-value="src/core/mixin/"><a href="src/core/mixin/index.html">src/core/mixin/</a></td>
	<td data-value="69.83" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 69%;"></div><div class="cover-empty" style="width:31%;"></div></div></td>
	<td data-value="69.83" class="pct medium">69.83%</td>
	<td data-value="116" class="abs medium">81/116</td>
	<td data-value="58.82" class="pct medium">58.82%</td>
	<td data-value="68" class="abs medium">40/68</td>
	<td data-value="81.25" class="pct high">81.25%</td>
	<td data-value="16" class="abs high">13/16</td>
	<td data-value="69.3" class="pct medium">69.3%</td>
	<td data-value="114" class="abs medium">79/114</td>
	</tr>

<tr>
	<td class="file low" data-value="src/deferred/"><a href="src/deferred/index.html">src/deferred/</a></td>
	<td data-value="4.7" class="pic low"><div class="chart"><div class="cover-fill" style="width: 4%;"></div><div class="cover-empty" style="width:96%;"></div></div></td>
	<td data-value="4.7" class="pct low">4.7%</td>
	<td data-value="468" class="abs low">22/468</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="179" class="abs low">0/179</td>
	<td data-value="5.41" class="pct low">5.41%</td>
	<td data-value="37" class="abs low">2/37</td>
	<td data-value="4.7" class="pct low">4.7%</td>
	<td data-value="468" class="abs low">22/468</td>
	</tr>

<tr>
	<td class="file low" data-value="src/dep/"><a href="src/dep/index.html">src/dep/</a></td>
	<td data-value="44.28" class="pic low"><div class="chart"><div class="cover-fill" style="width: 44%;"></div><div class="cover-empty" style="width:56%;"></div></div></td>
	<td data-value="44.28" class="pct low">44.28%</td>
	<td data-value="2114" class="abs low">936/2114</td>
	<td data-value="33.91" class="pct low">33.91%</td>
	<td data-value="115" class="abs low">39/115</td>
	<td data-value="30.39" class="pct low">30.39%</td>
	<td data-value="181" class="abs low">55/181</td>
	<td data-value="46.31" class="pct low">46.31%</td>
	<td data-value="1816" class="abs low">841/1816</td>
	</tr>

<tr>
	<td class="file high" data-value="src/geometry/"><a href="src/geometry/index.html">src/geometry/</a></td>
	<td data-value="81.2" class="pic high"><div class="chart"><div class="cover-fill" style="width: 81%;"></div><div class="cover-empty" style="width:19%;"></div></div></td>
	<td data-value="81.2" class="pct high">81.2%</td>
	<td data-value="266" class="abs high">216/266</td>
	<td data-value="42.11" class="pct low">42.11%</td>
	<td data-value="38" class="abs low">16/38</td>
	<td data-value="84.62" class="pct high">84.62%</td>
	<td data-value="13" class="abs high">11/13</td>
	<td data-value="80.99" class="pct high">80.99%</td>
	<td data-value="263" class="abs high">213/263</td>
	</tr>

<tr>
	<td class="file high" data-value="src/gpu/"><a href="src/gpu/index.html">src/gpu/</a></td>
	<td data-value="83.04" class="pic high"><div class="chart"><div class="cover-fill" style="width: 83%;"></div><div class="cover-empty" style="width:17%;"></div></div></td>
	<td data-value="83.04" class="pct high">83.04%</td>
	<td data-value="230" class="abs high">191/230</td>
	<td data-value="69.47" class="pct medium">69.47%</td>
	<td data-value="95" class="abs medium">66/95</td>
	<td data-value="95" class="pct high">95%</td>
	<td data-value="20" class="abs high">19/20</td>
	<td data-value="83.04" class="pct high">83.04%</td>
	<td data-value="230" class="abs high">191/230</td>
	</tr>

<tr>
	<td class="file low" data-value="src/light/"><a href="src/light/index.html">src/light/</a></td>
	<td data-value="44.58" class="pic low"><div class="chart"><div class="cover-fill" style="width: 44%;"></div><div class="cover-empty" style="width:56%;"></div></div></td>
	<td data-value="44.58" class="pct low">44.58%</td>
	<td data-value="83" class="abs low">37/83</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="8" class="abs medium">4/8</td>
	<td data-value="34.38" class="pct low">34.38%</td>
	<td data-value="32" class="abs low">11/32</td>
	<td data-value="44.58" class="pct low">44.58%</td>
	<td data-value="83" class="abs low">37/83</td>
	</tr>

<tr>
	<td class="file low" data-value="src/loader/"><a href="src/loader/index.html">src/loader/</a></td>
	<td data-value="1.2" class="pic low"><div class="chart"><div class="cover-fill" style="width: 1%;"></div><div class="cover-empty" style="width:99%;"></div></div></td>
	<td data-value="1.2" class="pct low">1.2%</td>
	<td data-value="499" class="abs low">6/499</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="388" class="abs low">0/388</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="53" class="abs low">0/53</td>
	<td data-value="1.21" class="pct low">1.21%</td>
	<td data-value="494" class="abs low">6/494</td>
	</tr>

<tr>
	<td class="file low" data-value="src/math/"><a href="src/math/index.html">src/math/</a></td>
	<td data-value="35.15" class="pic low"><div class="chart"><div class="cover-fill" style="width: 35%;"></div><div class="cover-empty" style="width:65%;"></div></div></td>
	<td data-value="35.15" class="pct low">35.15%</td>
	<td data-value="1977" class="abs low">695/1977</td>
	<td data-value="20.17" class="pct low">20.17%</td>
	<td data-value="233" class="abs low">47/233</td>
	<td data-value="15.73" class="pct low">15.73%</td>
	<td data-value="464" class="abs low">73/464</td>
	<td data-value="34.62" class="pct low">34.62%</td>
	<td data-value="1938" class="abs low">671/1938</td>
	</tr>

<tr>
	<td class="file low" data-value="src/particle/"><a href="src/particle/index.html">src/particle/</a></td>
	<td data-value="10.13" class="pic low"><div class="chart"><div class="cover-fill" style="width: 10%;"></div><div class="cover-empty" style="width:90%;"></div></div></td>
	<td data-value="10.13" class="pct low">10.13%</td>
	<td data-value="158" class="abs low">16/158</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="44" class="abs low">0/44</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="19" class="abs low">0/19</td>
	<td data-value="10.13" class="pct low">10.13%</td>
	<td data-value="158" class="abs low">16/158</td>
	</tr>

<tr>
	<td class="file low" data-value="src/picking/"><a href="src/picking/index.html">src/picking/</a></td>
	<td data-value="7.06" class="pic low"><div class="chart"><div class="cover-fill" style="width: 7%;"></div><div class="cover-empty" style="width:93%;"></div></div></td>
	<td data-value="7.06" class="pct low">7.06%</td>
	<td data-value="170" class="abs low">12/170</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="78" class="abs low">0/78</td>
	<td data-value="5" class="pct low">5%</td>
	<td data-value="20" class="abs low">1/20</td>
	<td data-value="7.06" class="pct low">7.06%</td>
	<td data-value="170" class="abs low">12/170</td>
	</tr>

<tr>
	<td class="file low" data-value="src/plugin/"><a href="src/plugin/index.html">src/plugin/</a></td>
	<td data-value="13.8" class="pic low"><div class="chart"><div class="cover-fill" style="width: 13%;"></div><div class="cover-empty" style="width:87%;"></div></div></td>
	<td data-value="13.8" class="pct low">13.8%</td>
	<td data-value="500" class="abs low">69/500</td>
	<td data-value="4.07" class="pct low">4.07%</td>
	<td data-value="221" class="abs low">9/221</td>
	<td data-value="15.85" class="pct low">15.85%</td>
	<td data-value="82" class="abs low">13/82</td>
	<td data-value="13.8" class="pct low">13.8%</td>
	<td data-value="500" class="abs low">69/500</td>
	</tr>

<tr>
	<td class="file medium" data-value="src/prePass/"><a href="src/prePass/index.html">src/prePass/</a></td>
	<td data-value="71.84" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 71%;"></div><div class="cover-empty" style="width:29%;"></div></div></td>
	<td data-value="71.84" class="pct medium">71.84%</td>
	<td data-value="451" class="abs medium">324/451</td>
	<td data-value="57.04" class="pct medium">57.04%</td>
	<td data-value="142" class="abs medium">81/142</td>
	<td data-value="77.42" class="pct medium">77.42%</td>
	<td data-value="31" class="abs medium">24/31</td>
	<td data-value="72" class="pct medium">72%</td>
	<td data-value="450" class="abs medium">324/450</td>
	</tr>

<tr>
	<td class="file high" data-value="src/shader/"><a href="src/shader/index.html">src/shader/</a></td>
	<td data-value="90.2" class="pic high"><div class="chart"><div class="cover-fill" style="width: 90%;"></div><div class="cover-empty" style="width:10%;"></div></div></td>
	<td data-value="90.2" class="pct high">90.2%</td>
	<td data-value="51" class="abs high">46/51</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="4" class="abs medium">3/4</td>
	<td data-value="57.14" class="pct medium">57.14%</td>
	<td data-value="7" class="abs medium">4/7</td>
	<td data-value="90.2" class="pct high">90.2%</td>
	<td data-value="51" class="abs high">46/51</td>
	</tr>

<tr>
	<td class="file high" data-value="src/shader/source/"><a href="src/shader/source/index.html">src/shader/source/</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="8" class="abs high">8/8</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="8" class="abs high">8/8</td>
	</tr>

<tr>
	<td class="file high" data-value="src/shader/source/compositor/"><a href="src/shader/source/compositor/index.html">src/shader/source/compositor/</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="16" class="abs high">16/16</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="16" class="abs high">16/16</td>
	</tr>

<tr>
	<td class="file high" data-value="src/shader/source/deferred/"><a href="src/shader/source/deferred/index.html">src/shader/source/deferred/</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="11" class="abs high">11/11</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="11" class="abs high">11/11</td>
	</tr>

<tr>
	<td class="file high" data-value="src/shader/source/header/"><a href="src/shader/source/header/index.html">src/shader/source/header/</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="7" class="abs high">7/7</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="7" class="abs high">7/7</td>
	</tr>

<tr>
	<td class="file low" data-value="src/util/"><a href="src/util/index.html">src/util/</a></td>
	<td data-value="31.6" class="pic low"><div class="chart"><div class="cover-fill" style="width: 31%;"></div><div class="cover-empty" style="width:69%;"></div></div></td>
	<td data-value="31.6" class="pct low">31.6%</td>
	<td data-value="848" class="abs low">268/848</td>
	<td data-value="25.15" class="pct low">25.15%</td>
	<td data-value="326" class="abs low">82/326</td>
	<td data-value="42.5" class="pct low">42.5%</td>
	<td data-value="40" class="abs low">17/40</td>
	<td data-value="31.83" class="pct low">31.83%</td>
	<td data-value="842" class="abs low">268/842</td>
	</tr>

<tr>
	<td class="file high" data-value="src/util/shader/"><a href="src/util/shader/index.html">src/util/shader/</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	</tr>

<tr>
	<td class="file low" data-value="src/vr/"><a href="src/vr/index.html">src/vr/</a></td>
	<td data-value="3.57" class="pic low"><div class="chart"><div class="cover-fill" style="width: 3%;"></div><div class="cover-empty" style="width:97%;"></div></div></td>
	<td data-value="3.57" class="pct low">3.57%</td>
	<td data-value="140" class="abs low">5/140</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="26" class="abs low">0/26</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="10" class="abs low">0/10</td>
	<td data-value="3.57" class="pct low">3.57%</td>
	<td data-value="140" class="abs low">5/140</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="sorter.js"></script>
</body>
</html>
