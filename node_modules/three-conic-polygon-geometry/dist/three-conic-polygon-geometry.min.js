// Version 2.1.2 three-conic-polygon-geometry - https://github.com/vasturiano/three-conic-polygon-geometry
!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n(require("three")):"function"==typeof define&&define.amd?define(["three"],n):(t="undefined"!=typeof globalThis?globalThis:t||self).ConicPolygonGeometry=n(t.THREE)}(this,(function(t){"use strict";function n(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=Array(n);e<n;e++)r[e]=t[e];return r}function e(t,n,e){return n=r(n),function(t,n){if(n&&("object"==typeof n||"function"==typeof n))return n;if(void 0!==n)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,i()?Reflect.construct(n,[],r(t).constructor):n.apply(t,e))}function r(t){return r=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},r(t)}function i(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(i=function(){return!!t})()}function o(t,n){return o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,n){return t.__proto__=n,t},o(t,n)}function u(t,n){return function(t){if(Array.isArray(t))return t}(t)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,i,o,u,l=[],a=!0,s=!1;try{if(o=(e=e.call(t)).next,0===n);else for(;!(a=(r=o.call(e)).done)&&(l.push(r.value),l.length!==n);a=!0);}catch(t){s=!0,i=t}finally{try{if(!a&&null!=e.return&&(u=e.return(),Object(u)!==u))return}finally{if(s)throw i}}return l}}(t,n)||a(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(t){return function(t){if(Array.isArray(t))return n(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||a(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(t,e){if(t){if("string"==typeof t)return n(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(t,e):void 0}}function s(t,n){return null==t||null==n?NaN:t<n?-1:t>n?1:t>=n?0:NaN}function c(t,n){return null==t||null==n?NaN:n<t?-1:n>t?1:n>=t?0:NaN}function f(t){let n,e,r;function i(t,r,i=0,o=t.length){if(i<o){if(0!==n(r,r))return o;do{const n=i+o>>>1;e(t[n],r)<0?i=n+1:o=n}while(i<o)}return i}return 2!==t.length?(n=s,e=(n,e)=>s(t(n),e),r=(n,e)=>t(n)-e):(n=t===s||t===c?t:h,e=t,r=t),{left:i,center:function(t,n,e=0,o=t.length){const u=i(t,n,e,o-1);return u>e&&r(t[u-1],n)>-r(t[u],n)?u-1:u},right:function(t,r,i=0,o=t.length){if(i<o){if(0!==n(r,r))return o;do{const n=i+o>>>1;e(t[n],r)<=0?i=n+1:o=n}while(i<o)}return i}}}function h(){return 0}const p=f(s).right;function d(t,n){let e,r;if(void 0===n)for(const n of t)null!=n&&(void 0===e?n>=n&&(e=r=n):(e>n&&(e=n),r<n&&(r=n)));else{let i=-1;for(let o of t)null!=(o=n(o,++i,t))&&(void 0===e?o>=o&&(e=r=o):(e>o&&(e=o),r<o&&(r=o)))}return[e,r]}f((function(t){return null===t?NaN:+t})).center;class g{constructor(){this._partials=new Float64Array(32),this._n=0}add(t){const n=this._partials;let e=0;for(let r=0;r<this._n&&r<32;r++){const i=n[r],o=t+i,u=Math.abs(t)<Math.abs(i)?t-(o-i):i-(o-t);u&&(n[e++]=u),t=o}return n[e]=t,this._n=e+1,this}valueOf(){const t=this._partials;let n,e,r,i=this._n,o=0;if(i>0){for(o=t[--i];i>0&&(n=o,e=t[--i],o=n+e,r=e-(o-n),!r););i>0&&(r<0&&t[i-1]<0||r>0&&t[i-1]>0)&&(e=2*r,n=o+e,e==n-o&&(o=n))}return o}}const y=Math.sqrt(50),v=Math.sqrt(10),m=Math.sqrt(2);function x(t,n,e){const r=(n-t)/Math.max(0,e),i=Math.floor(Math.log10(r)),o=r/Math.pow(10,i),u=o>=y?10:o>=v?5:o>=m?2:1;let l,a,s;return i<0?(s=Math.pow(10,-i)/u,l=Math.round(t*s),a=Math.round(n*s),l/s<t&&++l,a/s>n&&--a,s=-s):(s=Math.pow(10,i)*u,l=Math.round(t/s),a=Math.round(n/s),l*s<t&&++l,a*s>n&&--a),a<l&&.5<=e&&e<2?x(t,n,2*e):[l,a,s]}function w(t,n,e){return x(t=+t,n=+n,e=+e)[2]}function _(t){return Array.from(function*(t){for(const n of t)yield*n}(t))}function b(t,n,e=2){const r=n&&n.length,i=r?n[0]*e:t.length;let o=M(t,0,i,e,!0);const u=[];if(!o||o.next===o.prev)return u;let l,a,s;if(r&&(o=function(t,n,e,r){const i=[];for(let e=0,o=n.length;e<o;e++){const u=M(t,n[e]*r,e<o-1?n[e+1]*r:t.length,r,!1);u===u.next&&(u.steiner=!0),i.push(z(u))}i.sort(P);for(let t=0;t<i.length;t++)e=T(i[t],e);return e}(t,n,o,e)),t.length>80*e){l=1/0,a=1/0;let n=-1/0,r=-1/0;for(let o=e;o<i;o+=e){const e=t[o],i=t[o+1];e<l&&(l=e),i<a&&(a=i),e>n&&(n=e),i>r&&(r=i)}s=Math.max(n-l,r-a),s=0!==s?32767/s:0}return E(o,u,e,l,a,s,0),u}function M(t,n,e,r,i){let o;if(i===function(t,n,e,r){let i=0;for(let o=n,u=e-r;o<e;o+=r)i+=(t[u]-t[o])*(t[o+1]+t[u+1]),u=o;return i}(t,n,e,r)>0)for(let i=n;i<e;i+=r)o=U(i/r|0,t[i],t[i+1],o);else for(let i=e-r;i>=n;i-=r)o=U(i/r|0,t[i],t[i+1],o);return o&&q(o,o.next)&&(K(o),o=o.next),o}function S(t,n){if(!t)return t;n||(n=t);let e,r=t;do{if(e=!1,r.steiner||!q(r,r.next)&&0!==L(r.prev,r,r.next))r=r.next;else{if(K(r),r=n=r.prev,r===r.next)break;e=!0}}while(e||r!==n);return n}function E(t,n,e,r,i,o,u){if(!t)return;!u&&o&&function(t,n,e,r){let i=t;do{0===i.z&&(i.z=F(i.x,i.y,n,e,r)),i.prevZ=i.prev,i.nextZ=i.next,i=i.next}while(i!==t);i.prevZ.nextZ=null,i.prevZ=null,function(t){let n,e=1;do{let r,i=t;t=null;let o=null;for(n=0;i;){n++;let u=i,l=0;for(let t=0;t<e&&(l++,u=u.nextZ,u);t++);let a=e;for(;l>0||a>0&&u;)0!==l&&(0===a||!u||i.z<=u.z)?(r=i,i=i.nextZ,l--):(r=u,u=u.nextZ,a--),o?o.nextZ=r:t=r,r.prevZ=o,o=r;i=u}o.nextZ=null,e*=2}while(n>1)}(i)}(t,r,i,o);let l=t;for(;t.prev!==t.next;){const a=t.prev,s=t.next;if(o?k(t,r,i,o):N(t))n.push(a.i,t.i,s.i),K(t),t=s.next,l=s.next;else if((t=s)===l){u?1===u?E(t=$(S(t),n),n,e,r,i,o,2):2===u&&A(t,n,e,r,i,o):E(S(t),n,e,r,i,o,1);break}}}function N(t){const n=t.prev,e=t,r=t.next;if(L(n,e,r)>=0)return!1;const i=n.x,o=e.x,u=r.x,l=n.y,a=e.y,s=r.y,c=Math.min(i,o,u),f=Math.min(l,a,s),h=Math.max(i,o,u),p=Math.max(l,a,s);let d=r.next;for(;d!==n;){if(d.x>=c&&d.x<=h&&d.y>=f&&d.y<=p&&Z(i,l,o,a,u,s,d.x,d.y)&&L(d.prev,d,d.next)>=0)return!1;d=d.next}return!0}function k(t,n,e,r){const i=t.prev,o=t,u=t.next;if(L(i,o,u)>=0)return!1;const l=i.x,a=o.x,s=u.x,c=i.y,f=o.y,h=u.y,p=Math.min(l,a,s),d=Math.min(c,f,h),g=Math.max(l,a,s),y=Math.max(c,f,h),v=F(p,d,n,e,r),m=F(g,y,n,e,r);let x=t.prevZ,w=t.nextZ;for(;x&&x.z>=v&&w&&w.z<=m;){if(x.x>=p&&x.x<=g&&x.y>=d&&x.y<=y&&x!==i&&x!==u&&Z(l,c,a,f,s,h,x.x,x.y)&&L(x.prev,x,x.next)>=0)return!1;if(x=x.prevZ,w.x>=p&&w.x<=g&&w.y>=d&&w.y<=y&&w!==i&&w!==u&&Z(l,c,a,f,s,h,w.x,w.y)&&L(w.prev,w,w.next)>=0)return!1;w=w.nextZ}for(;x&&x.z>=v;){if(x.x>=p&&x.x<=g&&x.y>=d&&x.y<=y&&x!==i&&x!==u&&Z(l,c,a,f,s,h,x.x,x.y)&&L(x.prev,x,x.next)>=0)return!1;x=x.prevZ}for(;w&&w.z<=m;){if(w.x>=p&&w.x<=g&&w.y>=d&&w.y<=y&&w!==i&&w!==u&&Z(l,c,a,f,s,h,w.x,w.y)&&L(w.prev,w,w.next)>=0)return!1;w=w.nextZ}return!0}function $(t,n){let e=t;do{const r=e.prev,i=e.next.next;!q(r,i)&&H(r,e,e.next,i)&&B(r,i)&&B(i,r)&&(n.push(r.i,e.i,i.i),K(e),K(e.next),e=t=i),e=e.next}while(e!==t);return S(e)}function A(t,n,e,r,i,o){let u=t;do{let t=u.next.next;for(;t!==u.prev;){if(u.i!==t.i&&O(u,t)){let l=G(u,t);return u=S(u,u.next),l=S(l,l.next),E(u,n,e,r,i,o,0),void E(l,n,e,r,i,o,0)}t=t.next}u=u.next}while(u!==t)}function P(t,n){let e=t.x-n.x;if(0===e&&(e=t.y-n.y,0===e)){e=(t.next.y-t.y)/(t.next.x-t.x)-(n.next.y-n.y)/(n.next.x-n.x)}return e}function T(t,n){const e=function(t,n){let e=n;const r=t.x,i=t.y;let o,u=-1/0;if(q(t,e))return e;do{if(q(t,e.next))return e.next;if(i<=e.y&&i>=e.next.y&&e.next.y!==e.y){const t=e.x+(i-e.y)*(e.next.x-e.x)/(e.next.y-e.y);if(t<=r&&t>u&&(u=t,o=e.x<e.next.x?e:e.next,t===r))return o}e=e.next}while(e!==n);if(!o)return null;const l=o,a=o.x,s=o.y;let c=1/0;e=o;do{if(r>=e.x&&e.x>=a&&r!==e.x&&I(i<s?r:u,i,a,s,i<s?u:r,i,e.x,e.y)){const n=Math.abs(i-e.y)/(r-e.x);B(e,t)&&(n<c||n===c&&(e.x>o.x||e.x===o.x&&j(o,e)))&&(o=e,c=n)}e=e.next}while(e!==l);return o}(t,n);if(!e)return n;const r=G(e,t);return S(r,r.next),S(e,e.next)}function j(t,n){return L(t.prev,t,n.prev)<0&&L(n.next,t,t.next)<0}function F(t,n,e,r,i){return(t=1431655765&((t=858993459&((t=252645135&((t=16711935&((t=(t-e)*i|0)|t<<8))|t<<4))|t<<2))|t<<1))|(n=1431655765&((n=858993459&((n=252645135&((n=16711935&((n=(n-r)*i|0)|n<<8))|n<<4))|n<<2))|n<<1))<<1}function z(t){let n=t,e=t;do{(n.x<e.x||n.x===e.x&&n.y<e.y)&&(e=n),n=n.next}while(n!==t);return e}function I(t,n,e,r,i,o,u,l){return(i-u)*(n-l)>=(t-u)*(o-l)&&(t-u)*(r-l)>=(e-u)*(n-l)&&(e-u)*(o-l)>=(i-u)*(r-l)}function Z(t,n,e,r,i,o,u,l){return!(t===u&&n===l)&&I(t,n,e,r,i,o,u,l)}function O(t,n){return t.next.i!==n.i&&t.prev.i!==n.i&&!function(t,n){let e=t;do{if(e.i!==t.i&&e.next.i!==t.i&&e.i!==n.i&&e.next.i!==n.i&&H(e,e.next,t,n))return!0;e=e.next}while(e!==t);return!1}(t,n)&&(B(t,n)&&B(n,t)&&function(t,n){let e=t,r=!1;const i=(t.x+n.x)/2,o=(t.y+n.y)/2;do{e.y>o!=e.next.y>o&&e.next.y!==e.y&&i<(e.next.x-e.x)*(o-e.y)/(e.next.y-e.y)+e.x&&(r=!r),e=e.next}while(e!==t);return r}(t,n)&&(L(t.prev,t,n.prev)||L(t,n.prev,n))||q(t,n)&&L(t.prev,t,t.next)>0&&L(n.prev,n,n.next)>0)}function L(t,n,e){return(n.y-t.y)*(e.x-n.x)-(n.x-t.x)*(e.y-n.y)}function q(t,n){return t.x===n.x&&t.y===n.y}function H(t,n,e,r){const i=R(L(t,n,e)),o=R(L(t,n,r)),u=R(L(e,r,t)),l=R(L(e,r,n));return i!==o&&u!==l||(!(0!==i||!C(t,e,n))||(!(0!==o||!C(t,r,n))||(!(0!==u||!C(e,t,r))||!(0!==l||!C(e,n,r)))))}function C(t,n,e){return n.x<=Math.max(t.x,e.x)&&n.x>=Math.min(t.x,e.x)&&n.y<=Math.max(t.y,e.y)&&n.y>=Math.min(t.y,e.y)}function R(t){return t>0?1:t<0?-1:0}function B(t,n){return L(t.prev,t,t.next)<0?L(t,n,t.next)>=0&&L(t,t.prev,n)>=0:L(t,n,t.prev)<0||L(t,t.next,n)<0}function G(t,n){const e=D(t.i,t.x,t.y),r=D(n.i,n.x,n.y),i=t.next,o=n.prev;return t.next=n,n.prev=t,e.next=i,i.prev=e,r.next=e,e.prev=r,o.next=r,r.prev=o,r}function U(t,n,e,r){const i=D(t,n,e);return r?(i.next=r.next,i.prev=r,r.next.prev=i,r.next=i):(i.prev=i,i.next=i),i}function K(t){t.next.prev=t.prev,t.prev.next=t.next,t.prevZ&&(t.prevZ.nextZ=t.nextZ),t.nextZ&&(t.nextZ.prevZ=t.prevZ)}function D(t,n,e){return{i:t,x:n,y:e,prev:null,next:null,z:0,prevZ:null,nextZ:null,steiner:!1}}function V(t){const n=[],e=[],r=t[0][0].length;let i=0,o=0;for(const u of t){for(const t of u)for(let e=0;e<r;e++)n.push(t[e]);o&&(i+=o,e.push(i)),o=u.length}return{vertices:n,holes:e,dimensions:r}}const X=134217729;function J(t,n,e,r,i){let o,u,l,a,s=n[0],c=r[0],f=0,h=0;c>s==c>-s?(o=s,s=n[++f]):(o=c,c=r[++h]);let p=0;if(f<t&&h<e)for(c>s==c>-s?(u=s+o,l=o-(u-s),s=n[++f]):(u=c+o,l=o-(u-c),c=r[++h]),o=u,0!==l&&(i[p++]=l);f<t&&h<e;)c>s==c>-s?(u=o+s,a=u-o,l=o-(u-a)+(s-a),s=n[++f]):(u=o+c,a=u-o,l=o-(u-a)+(c-a),c=r[++h]),o=u,0!==l&&(i[p++]=l);for(;f<t;)u=o+s,a=u-o,l=o-(u-a)+(s-a),s=n[++f],o=u,0!==l&&(i[p++]=l);for(;h<e;)u=o+c,a=u-o,l=o-(u-a)+(c-a),c=r[++h],o=u,0!==l&&(i[p++]=l);return 0===o&&0!==p||(i[p++]=o),p}function Y(t){return new Float64Array(t)}const W=Y(4),Q=Y(8),tt=Y(12),nt=Y(16),et=Y(4);function rt(t,n,e,r,i,o){const u=(n-o)*(e-i),l=(t-i)*(r-o),a=u-l,s=Math.abs(u+l);return Math.abs(a)>=33306690738754716e-32*s?a:-function(t,n,e,r,i,o,u){let l,a,s,c,f,h,p,d,g,y,v,m,x,w,_,b,M,S;const E=t-i,N=e-i,k=n-o,$=r-o;w=E*$,h=X*E,p=h-(h-E),d=E-p,h=X*$,g=h-(h-$),y=$-g,_=d*y-(w-p*g-d*g-p*y),b=k*N,h=X*k,p=h-(h-k),d=k-p,h=X*N,g=h-(h-N),y=N-g,M=d*y-(b-p*g-d*g-p*y),v=_-M,f=_-v,W[0]=_-(v+f)+(f-M),m=w+v,f=m-w,x=w-(m-f)+(v-f),v=x-b,f=x-v,W[1]=x-(v+f)+(f-b),S=m+v,f=S-m,W[2]=m-(S-f)+(v-f),W[3]=S;let A=function(t,n){let e=n[0];for(let r=1;r<t;r++)e+=n[r];return e}(4,W),P=22204460492503146e-32*u;if(A>=P||-A>=P)return A;if(f=t-E,l=t-(E+f)+(f-i),f=e-N,s=e-(N+f)+(f-i),f=n-k,a=n-(k+f)+(f-o),f=r-$,c=r-($+f)+(f-o),0===l&&0===a&&0===s&&0===c)return A;if(P=11093356479670487e-47*u+33306690738754706e-32*Math.abs(A),A+=E*c+$*l-(k*s+N*a),A>=P||-A>=P)return A;w=l*$,h=X*l,p=h-(h-l),d=l-p,h=X*$,g=h-(h-$),y=$-g,_=d*y-(w-p*g-d*g-p*y),b=a*N,h=X*a,p=h-(h-a),d=a-p,h=X*N,g=h-(h-N),y=N-g,M=d*y-(b-p*g-d*g-p*y),v=_-M,f=_-v,et[0]=_-(v+f)+(f-M),m=w+v,f=m-w,x=w-(m-f)+(v-f),v=x-b,f=x-v,et[1]=x-(v+f)+(f-b),S=m+v,f=S-m,et[2]=m-(S-f)+(v-f),et[3]=S;const T=J(4,W,4,et,Q);w=E*c,h=X*E,p=h-(h-E),d=E-p,h=X*c,g=h-(h-c),y=c-g,_=d*y-(w-p*g-d*g-p*y),b=k*s,h=X*k,p=h-(h-k),d=k-p,h=X*s,g=h-(h-s),y=s-g,M=d*y-(b-p*g-d*g-p*y),v=_-M,f=_-v,et[0]=_-(v+f)+(f-M),m=w+v,f=m-w,x=w-(m-f)+(v-f),v=x-b,f=x-v,et[1]=x-(v+f)+(f-b),S=m+v,f=S-m,et[2]=m-(S-f)+(v-f),et[3]=S;const j=J(T,Q,4,et,tt);w=l*c,h=X*l,p=h-(h-l),d=l-p,h=X*c,g=h-(h-c),y=c-g,_=d*y-(w-p*g-d*g-p*y),b=a*s,h=X*a,p=h-(h-a),d=a-p,h=X*s,g=h-(h-s),y=s-g,M=d*y-(b-p*g-d*g-p*y),v=_-M,f=_-v,et[0]=_-(v+f)+(f-M),m=w+v,f=m-w,x=w-(m-f)+(v-f),v=x-b,f=x-v,et[1]=x-(v+f)+(f-b),S=m+v,f=S-m,et[2]=m-(S-f)+(v-f),et[3]=S;const F=J(j,tt,4,et,nt);return nt[F-1]}(t,n,e,r,i,o,s)}const it=Math.pow(2,-52),ot=new Uint32Array(512);class ut{static from(t,n=ht,e=pt){const r=t.length,i=new Float64Array(2*r);for(let o=0;o<r;o++){const r=t[o];i[2*o]=n(r),i[2*o+1]=e(r)}return new ut(i)}constructor(t){const n=t.length>>1;if(n>0&&"number"!=typeof t[0])throw new Error("Expected coords to contain numbers.");this.coords=t;const e=Math.max(2*n-5,0);this._triangles=new Uint32Array(3*e),this._halfedges=new Int32Array(3*e),this._hashSize=Math.ceil(Math.sqrt(n)),this._hullPrev=new Uint32Array(n),this._hullNext=new Uint32Array(n),this._hullTri=new Uint32Array(n),this._hullHash=new Int32Array(this._hashSize),this._ids=new Uint32Array(n),this._dists=new Float64Array(n),this.update()}update(){const{coords:t,_hullPrev:n,_hullNext:e,_hullTri:r,_hullHash:i}=this,o=t.length>>1;let u=1/0,l=1/0,a=-1/0,s=-1/0;for(let n=0;n<o;n++){const e=t[2*n],r=t[2*n+1];e<u&&(u=e),r<l&&(l=r),e>a&&(a=e),r>s&&(s=r),this._ids[n]=n}const c=(u+a)/2,f=(l+s)/2;let h,p,d;for(let n=0,e=1/0;n<o;n++){const r=lt(c,f,t[2*n],t[2*n+1]);r<e&&(h=n,e=r)}const g=t[2*h],y=t[2*h+1];for(let n=0,e=1/0;n<o;n++){if(n===h)continue;const r=lt(g,y,t[2*n],t[2*n+1]);r<e&&r>0&&(p=n,e=r)}let v=t[2*p],m=t[2*p+1],x=1/0;for(let n=0;n<o;n++){if(n===h||n===p)continue;const e=st(g,y,v,m,t[2*n],t[2*n+1]);e<x&&(d=n,x=e)}let w=t[2*d],_=t[2*d+1];if(x===1/0){for(let n=0;n<o;n++)this._dists[n]=t[2*n]-t[0]||t[2*n+1]-t[1];ct(this._ids,this._dists,0,o-1);const n=new Uint32Array(o);let e=0;for(let t=0,r=-1/0;t<o;t++){const i=this._ids[t],o=this._dists[i];o>r&&(n[e++]=i,r=o)}return this.hull=n.subarray(0,e),this.triangles=new Uint32Array(0),void(this.halfedges=new Uint32Array(0))}if(rt(g,y,v,m,w,_)<0){const t=p,n=v,e=m;p=d,v=w,m=_,d=t,w=n,_=e}const b=function(t,n,e,r,i,o){const u=e-t,l=r-n,a=i-t,s=o-n,c=u*u+l*l,f=a*a+s*s,h=.5/(u*s-l*a);return{x:t+(s*c-l*f)*h,y:n+(u*f-a*c)*h}}(g,y,v,m,w,_);this._cx=b.x,this._cy=b.y;for(let n=0;n<o;n++)this._dists[n]=lt(t[2*n],t[2*n+1],b.x,b.y);ct(this._ids,this._dists,0,o-1),this._hullStart=h;let M=3;e[h]=n[d]=p,e[p]=n[h]=d,e[d]=n[p]=h,r[h]=0,r[p]=1,r[d]=2,i.fill(-1),i[this._hashKey(g,y)]=h,i[this._hashKey(v,m)]=p,i[this._hashKey(w,_)]=d,this.trianglesLen=0,this._addTriangle(h,p,d,-1,-1,-1);for(let o,u,l=0;l<this._ids.length;l++){const a=this._ids[l],s=t[2*a],c=t[2*a+1];if(l>0&&Math.abs(s-o)<=it&&Math.abs(c-u)<=it)continue;if(o=s,u=c,a===h||a===p||a===d)continue;let f=0;for(let t=0,n=this._hashKey(s,c);t<this._hashSize&&(f=i[(n+t)%this._hashSize],-1===f||f===e[f]);t++);f=n[f];let g,y=f;for(;g=e[y],rt(s,c,t[2*y],t[2*y+1],t[2*g],t[2*g+1])>=0;)if(y=g,y===f){y=-1;break}if(-1===y)continue;let v=this._addTriangle(y,a,e[y],-1,-1,r[y]);r[a]=this._legalize(v+2),r[y]=v,M++;let m=e[y];for(;g=e[m],rt(s,c,t[2*m],t[2*m+1],t[2*g],t[2*g+1])<0;)v=this._addTriangle(m,a,g,r[a],-1,r[m]),r[a]=this._legalize(v+2),e[m]=m,M--,m=g;if(y===f)for(;g=n[y],rt(s,c,t[2*g],t[2*g+1],t[2*y],t[2*y+1])<0;)v=this._addTriangle(g,a,y,-1,r[y],r[g]),this._legalize(v+2),r[g]=v,e[y]=y,M--,y=g;this._hullStart=n[a]=y,e[y]=n[m]=a,e[a]=m,i[this._hashKey(s,c)]=a,i[this._hashKey(t[2*y],t[2*y+1])]=y}this.hull=new Uint32Array(M);for(let t=0,n=this._hullStart;t<M;t++)this.hull[t]=n,n=e[n];this.triangles=this._triangles.subarray(0,this.trianglesLen),this.halfedges=this._halfedges.subarray(0,this.trianglesLen)}_hashKey(t,n){return Math.floor(function(t,n){const e=t/(Math.abs(t)+Math.abs(n));return(n>0?3-e:1+e)/4}(t-this._cx,n-this._cy)*this._hashSize)%this._hashSize}_legalize(t){const{_triangles:n,_halfedges:e,coords:r}=this;let i=0,o=0;for(;;){const u=e[t],l=t-t%3;if(o=l+(t+2)%3,-1===u){if(0===i)break;t=ot[--i];continue}const a=u-u%3,s=l+(t+1)%3,c=a+(u+2)%3,f=n[o],h=n[t],p=n[s],d=n[c];if(at(r[2*f],r[2*f+1],r[2*h],r[2*h+1],r[2*p],r[2*p+1],r[2*d],r[2*d+1])){n[t]=d,n[u]=f;const r=e[c];if(-1===r){let n=this._hullStart;do{if(this._hullTri[n]===c){this._hullTri[n]=t;break}n=this._hullPrev[n]}while(n!==this._hullStart)}this._link(t,r),this._link(u,e[o]),this._link(o,c);const l=a+(u+1)%3;i<ot.length&&(ot[i++]=l)}else{if(0===i)break;t=ot[--i]}}return o}_link(t,n){this._halfedges[t]=n,-1!==n&&(this._halfedges[n]=t)}_addTriangle(t,n,e,r,i,o){const u=this.trianglesLen;return this._triangles[u]=t,this._triangles[u+1]=n,this._triangles[u+2]=e,this._link(u,r),this._link(u+1,i),this._link(u+2,o),this.trianglesLen+=3,u}}function lt(t,n,e,r){const i=t-e,o=n-r;return i*i+o*o}function at(t,n,e,r,i,o,u,l){const a=t-u,s=n-l,c=e-u,f=r-l,h=i-u,p=o-l,d=c*c+f*f,g=h*h+p*p;return a*(f*g-d*p)-s*(c*g-d*h)+(a*a+s*s)*(c*p-f*h)<0}function st(t,n,e,r,i,o){const u=e-t,l=r-n,a=i-t,s=o-n,c=u*u+l*l,f=a*a+s*s,h=.5/(u*s-l*a),p=(s*c-l*f)*h,d=(u*f-a*c)*h;return p*p+d*d}function ct(t,n,e,r){if(r-e<=20)for(let i=e+1;i<=r;i++){const r=t[i],o=n[r];let u=i-1;for(;u>=e&&n[t[u]]>o;)t[u+1]=t[u--];t[u+1]=r}else{let i=e+1,o=r;ft(t,e+r>>1,i),n[t[e]]>n[t[r]]&&ft(t,e,r),n[t[i]]>n[t[r]]&&ft(t,i,r),n[t[e]]>n[t[i]]&&ft(t,e,i);const u=t[i],l=n[u];for(;;){do{i++}while(n[t[i]]<l);do{o--}while(n[t[o]]>l);if(o<i)break;ft(t,i,o)}t[e+1]=t[o],t[o]=u,r-i+1>=o-e?(ct(t,n,i,r),ct(t,n,e,o-1)):(ct(t,n,e,o-1),ct(t,n,i,r))}}function ft(t,n,e){const r=t[n];t[n]=t[e],t[e]=r}function ht(t){return t[0]}function pt(t){return t[1]}function dt(t,n){var e,r,i,o,u,l,a,s,c,f=0,h=t[0],p=t[1],d=n.length;for(e=0;e<d;e++){r=0;var g=n[e],y=g.length-1;if((s=g[0])[0]!==g[y][0]&&s[1]!==g[y][1])throw new Error("First and last coordinates in a ring must be the same");for(o=s[0]-h,u=s[1]-p;r<y;r++){if(l=(c=g[r+1])[0]-h,a=c[1]-p,0===u&&0===a){if(l<=0&&o>=0||o<=0&&l>=0)return 0}else if(a>=0&&u<=0||a<=0&&u>=0){if(0===(i=rt(o,l,u,a,0,0)))return 0;(i>0&&a>0&&u<=0||i<0&&a<=0&&u>0)&&f++}s=c,u=a,o=l}}return f%2!=0}var gt=function(t,n,e={}){if(!t)throw new Error("point is required");if(!n)throw new Error("polygon is required");const r=function(t){if(!t)throw new Error("coord is required");if(!Array.isArray(t)){if("Feature"===t.type&&null!==t.geometry&&"Point"===t.geometry.type)return[...t.geometry.coordinates];if("Point"===t.type)return[...t.coordinates]}if(Array.isArray(t)&&t.length>=2&&!Array.isArray(t[0])&&!Array.isArray(t[1]))return[...t];throw new Error("coord must be GeoJSON Point or an Array of numbers")}(t),i="Feature"===(o=n).type?o.geometry:o;var o;const u=i.type,l=n.bbox;let a=i.coordinates;if(l&&!1===function(t,n){return n[0]<=t[0]&&n[1]<=t[1]&&n[2]>=t[0]&&n[3]>=t[1]}(r,l))return!1;"Polygon"===u&&(a=[a]);let s=!1;for(var c=0;c<a.length;++c){const t=dt(r,a[c]);if(0===t)return!e.ignoreBoundary;t&&(s=!0)}return s},yt=1e-6,vt=1e-12,mt=Math.PI,xt=mt/2,wt=mt/4,_t=2*mt,bt=180/mt,Mt=mt/180,St=Math.abs,Et=Math.atan,Nt=Math.atan2,kt=Math.cos,$t=Math.hypot,At=Math.sin,Pt=Math.sign||function(t){return t>0?1:t<0?-1:0},Tt=Math.sqrt;function jt(t){return t>1?xt:t<-1?-xt:Math.asin(t)}function Ft(t){return(t=At(t/2))*t}function zt(){}function It(t,n){t&&Ot.hasOwnProperty(t.type)&&Ot[t.type](t,n)}var Zt={Feature:function(t,n){It(t.geometry,n)},FeatureCollection:function(t,n){for(var e=t.features,r=-1,i=e.length;++r<i;)It(e[r].geometry,n)}},Ot={Sphere:function(t,n){n.sphere()},Point:function(t,n){t=t.coordinates,n.point(t[0],t[1],t[2])},MultiPoint:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)t=e[r],n.point(t[0],t[1],t[2])},LineString:function(t,n){Lt(t.coordinates,n,0)},MultiLineString:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)Lt(e[r],n,0)},Polygon:function(t,n){qt(t.coordinates,n)},MultiPolygon:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)qt(e[r],n)},GeometryCollection:function(t,n){for(var e=t.geometries,r=-1,i=e.length;++r<i;)It(e[r],n)}};function Lt(t,n,e){var r,i=-1,o=t.length-e;for(n.lineStart();++i<o;)r=t[i],n.point(r[0],r[1],r[2]);n.lineEnd()}function qt(t,n){var e=-1,r=t.length;for(n.polygonStart();++e<r;)Lt(t[e],n,1);n.polygonEnd()}function Ht(t,n){t&&Zt.hasOwnProperty(t.type)?Zt[t.type](t,n):It(t,n)}var Ct,Rt,Bt,Gt,Ut,Kt,Dt,Vt,Xt,Jt,Yt,Wt,Qt,tn,nn,en,rn=new g,on=new g,un={point:zt,lineStart:zt,lineEnd:zt,polygonStart:function(){rn=new g,un.lineStart=ln,un.lineEnd=an},polygonEnd:function(){var t=+rn;on.add(t<0?_t+t:t),this.lineStart=this.lineEnd=this.point=zt},sphere:function(){on.add(_t)}};function ln(){un.point=sn}function an(){cn(Ct,Rt)}function sn(t,n){un.point=cn,Ct=t,Rt=n,Bt=t*=Mt,Gt=kt(n=(n*=Mt)/2+wt),Ut=At(n)}function cn(t,n){var e=(t*=Mt)-Bt,r=e>=0?1:-1,i=r*e,o=kt(n=(n*=Mt)/2+wt),u=At(n),l=Ut*u,a=Gt*o+l*kt(i),s=l*r*At(i);rn.add(Nt(s,a)),Bt=t,Gt=o,Ut=u}function fn(t){return[Nt(t[1],t[0]),jt(t[2])]}function hn(t){var n=t[0],e=t[1],r=kt(e);return[r*kt(n),r*At(n),At(e)]}function pn(t,n){return t[0]*n[0]+t[1]*n[1]+t[2]*n[2]}function dn(t,n){return[t[1]*n[2]-t[2]*n[1],t[2]*n[0]-t[0]*n[2],t[0]*n[1]-t[1]*n[0]]}function gn(t,n){t[0]+=n[0],t[1]+=n[1],t[2]+=n[2]}function yn(t,n){return[t[0]*n,t[1]*n,t[2]*n]}function vn(t){var n=Tt(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]);t[0]/=n,t[1]/=n,t[2]/=n}var mn,xn,wn,_n,bn,Mn,Sn,En,Nn,kn,$n,An,Pn,Tn,jn,Fn,zn={point:In,lineStart:On,lineEnd:Ln,polygonStart:function(){zn.point=qn,zn.lineStart=Hn,zn.lineEnd=Cn,tn=new g,un.polygonStart()},polygonEnd:function(){un.polygonEnd(),zn.point=In,zn.lineStart=On,zn.lineEnd=Ln,rn<0?(Kt=-(Vt=180),Dt=-(Xt=90)):tn>yt?Xt=90:tn<-1e-6&&(Dt=-90),en[0]=Kt,en[1]=Vt},sphere:function(){Kt=-(Vt=180),Dt=-(Xt=90)}};function In(t,n){nn.push(en=[Kt=t,Vt=t]),n<Dt&&(Dt=n),n>Xt&&(Xt=n)}function Zn(t,n){var e=hn([t*Mt,n*Mt]);if(Qt){var r=dn(Qt,e),i=dn([r[1],-r[0],0],r);vn(i),i=fn(i);var o,u=t-Jt,l=u>0?1:-1,a=i[0]*bt*l,s=St(u)>180;s^(l*Jt<a&&a<l*t)?(o=i[1]*bt)>Xt&&(Xt=o):s^(l*Jt<(a=(a+360)%360-180)&&a<l*t)?(o=-i[1]*bt)<Dt&&(Dt=o):(n<Dt&&(Dt=n),n>Xt&&(Xt=n)),s?t<Jt?Rn(Kt,t)>Rn(Kt,Vt)&&(Vt=t):Rn(t,Vt)>Rn(Kt,Vt)&&(Kt=t):Vt>=Kt?(t<Kt&&(Kt=t),t>Vt&&(Vt=t)):t>Jt?Rn(Kt,t)>Rn(Kt,Vt)&&(Vt=t):Rn(t,Vt)>Rn(Kt,Vt)&&(Kt=t)}else nn.push(en=[Kt=t,Vt=t]);n<Dt&&(Dt=n),n>Xt&&(Xt=n),Qt=e,Jt=t}function On(){zn.point=Zn}function Ln(){en[0]=Kt,en[1]=Vt,zn.point=In,Qt=null}function qn(t,n){if(Qt){var e=t-Jt;tn.add(St(e)>180?e+(e>0?360:-360):e)}else Yt=t,Wt=n;un.point(t,n),Zn(t,n)}function Hn(){un.lineStart()}function Cn(){qn(Yt,Wt),un.lineEnd(),St(tn)>yt&&(Kt=-(Vt=180)),en[0]=Kt,en[1]=Vt,Qt=null}function Rn(t,n){return(n-=t)<0?n+360:n}function Bn(t,n){return t[0]-n[0]}function Gn(t,n){return t[0]<=t[1]?t[0]<=n&&n<=t[1]:n<t[0]||t[1]<n}function Un(t){var n,e,r,i,o,u,l;if(Xt=Vt=-(Kt=Dt=1/0),nn=[],Ht(t,zn),e=nn.length){for(nn.sort(Bn),n=1,o=[r=nn[0]];n<e;++n)Gn(r,(i=nn[n])[0])||Gn(r,i[1])?(Rn(r[0],i[1])>Rn(r[0],r[1])&&(r[1]=i[1]),Rn(i[0],r[1])>Rn(r[0],r[1])&&(r[0]=i[0])):o.push(r=i);for(u=-1/0,n=0,r=o[e=o.length-1];n<=e;r=i,++n)i=o[n],(l=Rn(r[1],i[0]))>u&&(u=l,Kt=i[0],Vt=r[1])}return nn=en=null,Kt===1/0||Dt===1/0?[[NaN,NaN],[NaN,NaN]]:[[Kt,Dt],[Vt,Xt]]}var Kn={sphere:zt,point:Dn,lineStart:Xn,lineEnd:Wn,polygonStart:function(){Kn.lineStart=Qn,Kn.lineEnd=te},polygonEnd:function(){Kn.lineStart=Xn,Kn.lineEnd=Wn}};function Dn(t,n){t*=Mt;var e=kt(n*=Mt);Vn(e*kt(t),e*At(t),At(n))}function Vn(t,n,e){++mn,wn+=(t-wn)/mn,_n+=(n-_n)/mn,bn+=(e-bn)/mn}function Xn(){Kn.point=Jn}function Jn(t,n){t*=Mt;var e=kt(n*=Mt);Tn=e*kt(t),jn=e*At(t),Fn=At(n),Kn.point=Yn,Vn(Tn,jn,Fn)}function Yn(t,n){t*=Mt;var e=kt(n*=Mt),r=e*kt(t),i=e*At(t),o=At(n),u=Nt(Tt((u=jn*o-Fn*i)*u+(u=Fn*r-Tn*o)*u+(u=Tn*i-jn*r)*u),Tn*r+jn*i+Fn*o);xn+=u,Mn+=u*(Tn+(Tn=r)),Sn+=u*(jn+(jn=i)),En+=u*(Fn+(Fn=o)),Vn(Tn,jn,Fn)}function Wn(){Kn.point=Dn}function Qn(){Kn.point=ne}function te(){ee(An,Pn),Kn.point=Dn}function ne(t,n){An=t,Pn=n,t*=Mt,n*=Mt,Kn.point=ee;var e=kt(n);Tn=e*kt(t),jn=e*At(t),Fn=At(n),Vn(Tn,jn,Fn)}function ee(t,n){t*=Mt;var e=kt(n*=Mt),r=e*kt(t),i=e*At(t),o=At(n),u=jn*o-Fn*i,l=Fn*r-Tn*o,a=Tn*i-jn*r,s=$t(u,l,a),c=jt(s),f=s&&-c/s;Nn.add(f*u),kn.add(f*l),$n.add(f*a),xn+=c,Mn+=c*(Tn+(Tn=r)),Sn+=c*(jn+(jn=i)),En+=c*(Fn+(Fn=o)),Vn(Tn,jn,Fn)}function re(t){mn=xn=wn=_n=bn=Mn=Sn=En=0,Nn=new g,kn=new g,$n=new g,Ht(t,Kn);var n=+Nn,e=+kn,r=+$n,i=$t(n,e,r);return i<vt&&(n=Mn,e=Sn,r=En,xn<yt&&(n=wn,e=_n,r=bn),(i=$t(n,e,r))<vt)?[NaN,NaN]:[Nt(e,n)*bt,jt(r/i)*bt]}function ie(t,n){function e(e,r){return e=t(e,r),n(e[0],e[1])}return t.invert&&n.invert&&(e.invert=function(e,r){return(e=n.invert(e,r))&&t.invert(e[0],e[1])}),e}function oe(t,n){return St(t)>mt&&(t-=Math.round(t/_t)*_t),[t,n]}function ue(t,n,e){return(t%=_t)?n||e?ie(ae(t),se(n,e)):ae(t):n||e?se(n,e):oe}function le(t){return function(n,e){return St(n+=t)>mt&&(n-=Math.round(n/_t)*_t),[n,e]}}function ae(t){var n=le(t);return n.invert=le(-t),n}function se(t,n){var e=kt(t),r=At(t),i=kt(n),o=At(n);function u(t,n){var u=kt(n),l=kt(t)*u,a=At(t)*u,s=At(n),c=s*e+l*r;return[Nt(a*i-c*o,l*e-s*r),jt(c*i+a*o)]}return u.invert=function(t,n){var u=kt(n),l=kt(t)*u,a=At(t)*u,s=At(n),c=s*i-a*o;return[Nt(a*i+s*o,l*e+c*r),jt(c*e-l*r)]},u}function ce(t,n){(n=hn(n))[0]-=t,vn(n);var e,r=(e=-n[1])>1?0:e<-1?mt:Math.acos(e);return((-n[2]<0?-r:r)+_t-yt)%_t}function fe(){var t,n=[];return{point:function(n,e,r){t.push([n,e,r])},lineStart:function(){n.push(t=[])},lineEnd:zt,rejoin:function(){n.length>1&&n.push(n.pop().concat(n.shift()))},result:function(){var e=n;return n=[],t=null,e}}}function he(t,n){return St(t[0]-n[0])<yt&&St(t[1]-n[1])<yt}function pe(t,n,e,r){this.x=t,this.z=n,this.o=e,this.e=r,this.v=!1,this.n=this.p=null}function de(t,n,e,r,i){var o,u,l=[],a=[];if(t.forEach((function(t){if(!((n=t.length-1)<=0)){var n,e,r=t[0],u=t[n];if(he(r,u)){if(!r[2]&&!u[2]){for(i.lineStart(),o=0;o<n;++o)i.point((r=t[o])[0],r[1]);return void i.lineEnd()}u[0]+=2e-6}l.push(e=new pe(r,t,null,!0)),a.push(e.o=new pe(r,null,e,!1)),l.push(e=new pe(u,t,null,!1)),a.push(e.o=new pe(u,null,e,!0))}})),l.length){for(a.sort(n),ge(l),ge(a),o=0,u=a.length;o<u;++o)a[o].e=e=!e;for(var s,c,f=l[0];;){for(var h=f,p=!0;h.v;)if((h=h.n)===f)return;s=h.z,i.lineStart();do{if(h.v=h.o.v=!0,h.e){if(p)for(o=0,u=s.length;o<u;++o)i.point((c=s[o])[0],c[1]);else r(h.x,h.n.x,1,i);h=h.n}else{if(p)for(s=h.p.z,o=s.length-1;o>=0;--o)i.point((c=s[o])[0],c[1]);else r(h.x,h.p.x,-1,i);h=h.p}s=(h=h.o).z,p=!p}while(!h.v);i.lineEnd()}}}function ge(t){if(n=t.length){for(var n,e,r=0,i=t[0];++r<n;)i.n=e=t[r],e.p=i,i=e;i.n=e=t[0],e.p=i}}function ye(t){return St(t[0])<=mt?t[0]:Pt(t[0])*((St(t[0])+mt)%_t-mt)}function ve(t,n){var e=ye(n),r=n[1],i=At(r),o=[At(e),-kt(e),0],u=0,l=0,a=new g;1===i?r=xt+yt:-1===i&&(r=-xt-yt);for(var s=0,c=t.length;s<c;++s)if(h=(f=t[s]).length)for(var f,h,p=f[h-1],d=ye(p),y=p[1]/2+wt,v=At(y),m=kt(y),x=0;x<h;++x,d=_,v=M,m=S,p=w){var w=f[x],_=ye(w),b=w[1]/2+wt,M=At(b),S=kt(b),E=_-d,N=E>=0?1:-1,k=N*E,$=k>mt,A=v*M;if(a.add(Nt(A*N*At(k),m*S+A*kt(k))),u+=$?E+N*_t:E,$^d>=e^_>=e){var P=dn(hn(p),hn(w));vn(P);var T=dn(o,P);vn(T);var j=($^E>=0?-1:1)*jt(T[2]);(r>j||r===j&&(P[0]||P[1]))&&(l+=$^E>=0?1:-1)}}return(u<-1e-6||u<yt&&a<-1e-12)^1&l}function me(t,n,e,r){return function(i){var o,u,l,a=n(i),s=fe(),c=n(s),f=!1,h={point:p,lineStart:g,lineEnd:y,polygonStart:function(){h.point=v,h.lineStart=m,h.lineEnd=x,u=[],o=[]},polygonEnd:function(){h.point=p,h.lineStart=g,h.lineEnd=y,u=_(u);var t=ve(o,r);u.length?(f||(i.polygonStart(),f=!0),de(u,we,t,e,i)):t&&(f||(i.polygonStart(),f=!0),i.lineStart(),e(null,null,1,i),i.lineEnd()),f&&(i.polygonEnd(),f=!1),u=o=null},sphere:function(){i.polygonStart(),i.lineStart(),e(null,null,1,i),i.lineEnd(),i.polygonEnd()}};function p(n,e){t(n,e)&&i.point(n,e)}function d(t,n){a.point(t,n)}function g(){h.point=d,a.lineStart()}function y(){h.point=p,a.lineEnd()}function v(t,n){l.push([t,n]),c.point(t,n)}function m(){c.lineStart(),l=[]}function x(){v(l[0][0],l[0][1]),c.lineEnd();var t,n,e,r,a=c.clean(),h=s.result(),p=h.length;if(l.pop(),o.push(l),l=null,p)if(1&a){if((n=(e=h[0]).length-1)>0){for(f||(i.polygonStart(),f=!0),i.lineStart(),t=0;t<n;++t)i.point((r=e[t])[0],r[1]);i.lineEnd()}}else p>1&&2&a&&h.push(h.pop().concat(h.shift())),u.push(h.filter(xe))}return h}}function xe(t){return t.length>1}function we(t,n){return((t=t.x)[0]<0?t[1]-xt-yt:xt-t[1])-((n=n.x)[0]<0?n[1]-xt-yt:xt-n[1])}oe.invert=oe;var _e=me((function(){return!0}),(function(t){var n,e=NaN,r=NaN,i=NaN;return{lineStart:function(){t.lineStart(),n=1},point:function(o,u){var l=o>0?mt:-mt,a=St(o-e);St(a-mt)<yt?(t.point(e,r=(r+u)/2>0?xt:-xt),t.point(i,r),t.lineEnd(),t.lineStart(),t.point(l,r),t.point(o,r),n=0):i!==l&&a>=mt&&(St(e-i)<yt&&(e-=i*yt),St(o-l)<yt&&(o-=l*yt),r=function(t,n,e,r){var i,o,u=At(t-e);return St(u)>yt?Et((At(n)*(o=kt(r))*At(e)-At(r)*(i=kt(n))*At(t))/(i*o*u)):(n+r)/2}(e,r,o,u),t.point(i,r),t.lineEnd(),t.lineStart(),t.point(l,r),n=0),t.point(e=o,r=u),i=l},lineEnd:function(){t.lineEnd(),e=r=NaN},clean:function(){return 2-n}}}),(function(t,n,e,r){var i;if(null==t)i=e*xt,r.point(-mt,i),r.point(0,i),r.point(mt,i),r.point(mt,0),r.point(mt,-i),r.point(0,-i),r.point(-mt,-i),r.point(-mt,0),r.point(-mt,i);else if(St(t[0]-n[0])>yt){var o=t[0]<n[0]?mt:-mt;i=e*o/2,r.point(-o,i),r.point(0,i),r.point(o,i)}else r.point(n[0],n[1])}),[-mt,-xt]);function be(t){var n=kt(t),e=2*Mt,r=n>0,i=St(n)>yt;function o(t,e){return kt(t)*kt(e)>n}function u(t,e,r){var i=[1,0,0],o=dn(hn(t),hn(e)),u=pn(o,o),l=o[0],a=u-l*l;if(!a)return!r&&t;var s=n*u/a,c=-n*l/a,f=dn(i,o),h=yn(i,s);gn(h,yn(o,c));var p=f,d=pn(h,p),g=pn(p,p),y=d*d-g*(pn(h,h)-1);if(!(y<0)){var v=Tt(y),m=yn(p,(-d-v)/g);if(gn(m,h),m=fn(m),!r)return m;var x,w=t[0],_=e[0],b=t[1],M=e[1];_<w&&(x=w,w=_,_=x);var S=_-w,E=St(S-mt)<yt;if(!E&&M<b&&(x=b,b=M,M=x),E||S<yt?E?b+M>0^m[1]<(St(m[0]-w)<yt?b:M):b<=m[1]&&m[1]<=M:S>mt^(w<=m[0]&&m[0]<=_)){var N=yn(p,(-d+v)/g);return gn(N,h),[m,fn(N)]}}}function l(n,e){var i=r?t:mt-t,o=0;return n<-i?o|=1:n>i&&(o|=2),e<-i?o|=4:e>i&&(o|=8),o}return me(o,(function(t){var n,e,a,s,c;return{lineStart:function(){s=a=!1,c=1},point:function(f,h){var p,d=[f,h],g=o(f,h),y=r?g?0:l(f,h):g?l(f+(f<0?mt:-mt),h):0;if(!n&&(s=a=g)&&t.lineStart(),g!==a&&(!(p=u(n,d))||he(n,p)||he(d,p))&&(d[2]=1),g!==a)c=0,g?(t.lineStart(),p=u(d,n),t.point(p[0],p[1])):(p=u(n,d),t.point(p[0],p[1],2),t.lineEnd()),n=p;else if(i&&n&&r^g){var v;y&e||!(v=u(d,n,!0))||(c=0,r?(t.lineStart(),t.point(v[0][0],v[0][1]),t.point(v[1][0],v[1][1]),t.lineEnd()):(t.point(v[1][0],v[1][1]),t.lineEnd(),t.lineStart(),t.point(v[0][0],v[0][1],3)))}!g||n&&he(n,d)||t.point(d[0],d[1]),n=d,a=g,e=y},lineEnd:function(){a&&t.lineEnd(),n=null},clean:function(){return c|(s&&a)<<1}}}),(function(n,r,i,o){!function(t,n,e,r,i,o){if(e){var u=kt(n),l=At(n),a=r*e;null==i?(i=n+r*_t,o=n-a/2):(i=ce(u,i),o=ce(u,o),(r>0?i<o:i>o)&&(i+=r*_t));for(var s,c=i;r>0?c>o:c<o;c-=a)s=fn([u,-l*kt(c),-l*At(c)]),t.point(s[0],s[1])}}(o,t,e,i,n,r)}),r?[0,-t]:[-mt,t-mt])}var Me,Se,Ee,Ne,ke=1e9,$e=-1e9;function Ae(t,n,e,r){function i(i,o){return t<=i&&i<=e&&n<=o&&o<=r}function o(i,o,l,s){var c=0,f=0;if(null==i||(c=u(i,l))!==(f=u(o,l))||a(i,o)<0^l>0)do{s.point(0===c||3===c?t:e,c>1?r:n)}while((c=(c+l+4)%4)!==f);else s.point(o[0],o[1])}function u(r,i){return St(r[0]-t)<yt?i>0?0:3:St(r[0]-e)<yt?i>0?2:1:St(r[1]-n)<yt?i>0?1:0:i>0?3:2}function l(t,n){return a(t.x,n.x)}function a(t,n){var e=u(t,1),r=u(n,1);return e!==r?e-r:0===e?n[1]-t[1]:1===e?t[0]-n[0]:2===e?t[1]-n[1]:n[0]-t[0]}return function(u){var a,s,c,f,h,p,d,g,y,v,m,x=u,w=fe(),b={point:M,lineStart:function(){b.point=S,s&&s.push(c=[]);v=!0,y=!1,d=g=NaN},lineEnd:function(){a&&(S(f,h),p&&y&&w.rejoin(),a.push(w.result()));b.point=M,y&&x.lineEnd()},polygonStart:function(){x=w,a=[],s=[],m=!0},polygonEnd:function(){var n=function(){for(var n=0,e=0,i=s.length;e<i;++e)for(var o,u,l=s[e],a=1,c=l.length,f=l[0],h=f[0],p=f[1];a<c;++a)o=h,u=p,h=(f=l[a])[0],p=f[1],u<=r?p>r&&(h-o)*(r-u)>(p-u)*(t-o)&&++n:p<=r&&(h-o)*(r-u)<(p-u)*(t-o)&&--n;return n}(),e=m&&n,i=(a=_(a)).length;(e||i)&&(u.polygonStart(),e&&(u.lineStart(),o(null,null,1,u),u.lineEnd()),i&&de(a,l,n,o,u),u.polygonEnd());x=u,a=s=c=null}};function M(t,n){i(t,n)&&x.point(t,n)}function S(o,u){var l=i(o,u);if(s&&c.push([o,u]),v)f=o,h=u,p=l,v=!1,l&&(x.lineStart(),x.point(o,u));else if(l&&y)x.point(o,u);else{var a=[d=Math.max($e,Math.min(ke,d)),g=Math.max($e,Math.min(ke,g))],w=[o=Math.max($e,Math.min(ke,o)),u=Math.max($e,Math.min(ke,u))];!function(t,n,e,r,i,o){var u,l=t[0],a=t[1],s=0,c=1,f=n[0]-l,h=n[1]-a;if(u=e-l,f||!(u>0)){if(u/=f,f<0){if(u<s)return;u<c&&(c=u)}else if(f>0){if(u>c)return;u>s&&(s=u)}if(u=i-l,f||!(u<0)){if(u/=f,f<0){if(u>c)return;u>s&&(s=u)}else if(f>0){if(u<s)return;u<c&&(c=u)}if(u=r-a,h||!(u>0)){if(u/=h,h<0){if(u<s)return;u<c&&(c=u)}else if(h>0){if(u>c)return;u>s&&(s=u)}if(u=o-a,h||!(u<0)){if(u/=h,h<0){if(u>c)return;u>s&&(s=u)}else if(h>0){if(u<s)return;u<c&&(c=u)}return s>0&&(t[0]=l+s*f,t[1]=a+s*h),c<1&&(n[0]=l+c*f,n[1]=a+c*h),!0}}}}}(a,w,t,n,e,r)?l&&(x.lineStart(),x.point(o,u),m=!1):(y||(x.lineStart(),x.point(a[0],a[1])),x.point(w[0],w[1]),l||x.lineEnd(),m=!1)}d=o,g=u,y=l}return b}}var Pe={sphere:zt,point:zt,lineStart:function(){Pe.point=je,Pe.lineEnd=Te},lineEnd:zt,polygonStart:zt,polygonEnd:zt};function Te(){Pe.point=Pe.lineEnd=zt}function je(t,n){Se=t*=Mt,Ee=At(n*=Mt),Ne=kt(n),Pe.point=Fe}function Fe(t,n){t*=Mt;var e=At(n*=Mt),r=kt(n),i=St(t-Se),o=kt(i),u=r*At(i),l=Ne*e-Ee*r*o,a=Ee*e+Ne*r*o;Me.add(Nt(Tt(u*u+l*l),a)),Se=t,Ee=e,Ne=r}var ze=[null,null],Ie={type:"LineString",coordinates:ze};function Ze(t,n){return ze[0]=t,ze[1]=n,function(t){return Me=new g,Ht(t,Pe),+Me}(Ie)}var Oe={Feature:function(t,n){return qe(t.geometry,n)},FeatureCollection:function(t,n){for(var e=t.features,r=-1,i=e.length;++r<i;)if(qe(e[r].geometry,n))return!0;return!1}},Le={Sphere:function(){return!0},Point:function(t,n){return He(t.coordinates,n)},MultiPoint:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(He(e[r],n))return!0;return!1},LineString:function(t,n){return Ce(t.coordinates,n)},MultiLineString:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(Ce(e[r],n))return!0;return!1},Polygon:function(t,n){return Re(t.coordinates,n)},MultiPolygon:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(Re(e[r],n))return!0;return!1},GeometryCollection:function(t,n){for(var e=t.geometries,r=-1,i=e.length;++r<i;)if(qe(e[r],n))return!0;return!1}};function qe(t,n){return!(!t||!Le.hasOwnProperty(t.type))&&Le[t.type](t,n)}function He(t,n){return 0===Ze(t,n)}function Ce(t,n){for(var e,r,i,o=0,u=t.length;o<u;o++){if(0===(r=Ze(t[o],n)))return!0;if(o>0&&(i=Ze(t[o],t[o-1]))>0&&e<=i&&r<=i&&(e+r-i)*(1-Math.pow((e-r)/i,2))<vt*i)return!0;e=r}return!1}function Re(t,n){return!!ve(t.map(Be),Ge(n))}function Be(t){return(t=t.map(Ge)).pop(),t}function Ge(t){return[t[0]*Mt,t[1]*Mt]}var Ue=t=>t,Ke=1/0,De=Ke,Ve=-Ke,Xe=Ve,Je={point:function(t,n){t<Ke&&(Ke=t);t>Ve&&(Ve=t);n<De&&(De=n);n>Xe&&(Xe=n)},lineStart:zt,lineEnd:zt,polygonStart:zt,polygonEnd:zt,result:function(){var t=[[Ke,De],[Ve,Xe]];return Ve=Xe=-(De=Ke=1/0),t}};function Ye(t){return function(n){var e=new We;for(var r in t)e[r]=t[r];return e.stream=n,e}}function We(){}function Qe(t,n,e){var r=t.clipExtent&&t.clipExtent();return t.scale(150).translate([0,0]),null!=r&&t.clipExtent(null),Ht(e,t.stream(Je)),n(Je.result()),null!=r&&t.clipExtent(r),t}function tr(t,n,e){return Qe(t,(function(e){var r=n[1][0]-n[0][0],i=n[1][1]-n[0][1],o=Math.min(r/(e[1][0]-e[0][0]),i/(e[1][1]-e[0][1])),u=+n[0][0]+(r-o*(e[1][0]+e[0][0]))/2,l=+n[0][1]+(i-o*(e[1][1]+e[0][1]))/2;t.scale(150*o).translate([u,l])}),e)}We.prototype={constructor:We,point:function(t,n){this.stream.point(t,n)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};var nr=kt(30*Mt);function er(t,n){return+n?function(t,n){function e(r,i,o,u,l,a,s,c,f,h,p,d,g,y){var v=s-r,m=c-i,x=v*v+m*m;if(x>4*n&&g--){var w=u+h,_=l+p,b=a+d,M=Tt(w*w+_*_+b*b),S=jt(b/=M),E=St(St(b)-1)<yt||St(o-f)<yt?(o+f)/2:Nt(_,w),N=t(E,S),k=N[0],$=N[1],A=k-r,P=$-i,T=m*A-v*P;(T*T/x>n||St((v*A+m*P)/x-.5)>.3||u*h+l*p+a*d<nr)&&(e(r,i,o,u,l,a,k,$,E,w/=M,_/=M,b,g,y),y.point(k,$),e(k,$,E,w,_,b,s,c,f,h,p,d,g,y))}}return function(n){var r,i,o,u,l,a,s,c,f,h,p,d,g={point:y,lineStart:v,lineEnd:x,polygonStart:function(){n.polygonStart(),g.lineStart=w},polygonEnd:function(){n.polygonEnd(),g.lineStart=v}};function y(e,r){e=t(e,r),n.point(e[0],e[1])}function v(){c=NaN,g.point=m,n.lineStart()}function m(r,i){var o=hn([r,i]),u=t(r,i);e(c,f,s,h,p,d,c=u[0],f=u[1],s=r,h=o[0],p=o[1],d=o[2],16,n),n.point(c,f)}function x(){g.point=y,n.lineEnd()}function w(){v(),g.point=_,g.lineEnd=b}function _(t,n){m(r=t,n),i=c,o=f,u=h,l=p,a=d,g.point=m}function b(){e(c,f,s,h,p,d,i,o,r,u,l,a,16,n),g.lineEnd=x,x()}return g}}(t,n):function(t){return Ye({point:function(n,e){n=t(n,e),this.stream.point(n[0],n[1])}})}(t)}var rr=Ye({point:function(t,n){this.stream.point(t*Mt,n*Mt)}});function ir(t,n,e,r,i,o){if(!o)return function(t,n,e,r,i){function o(o,u){return[n+t*(o*=r),e-t*(u*=i)]}return o.invert=function(o,u){return[(o-n)/t*r,(e-u)/t*i]},o}(t,n,e,r,i);var u=kt(o),l=At(o),a=u*t,s=l*t,c=u/t,f=l/t,h=(l*e-u*n)/t,p=(l*n+u*e)/t;function d(t,o){return[a*(t*=r)-s*(o*=i)+n,e-s*t-a*o]}return d.invert=function(t,n){return[r*(c*t-f*n+h),i*(p-f*t-c*n)]},d}function or(t){return function(t){var n,e,r,i,o,u,l,a,s,c,f=150,h=480,p=250,d=0,g=0,y=0,v=0,m=0,x=0,w=1,_=1,b=null,M=_e,S=null,E=Ue,N=.5;function k(t){return a(t[0]*Mt,t[1]*Mt)}function $(t){return(t=a.invert(t[0],t[1]))&&[t[0]*bt,t[1]*bt]}function A(){var t=ir(f,0,0,w,_,x).apply(null,n(d,g)),r=ir(f,h-t[0],p-t[1],w,_,x);return e=ue(y,v,m),l=ie(n,r),a=ie(e,l),u=er(l,N),P()}function P(){return s=c=null,k}return k.stream=function(t){return s&&c===t?s:s=rr(function(t){return Ye({point:function(n,e){var r=t(n,e);return this.stream.point(r[0],r[1])}})}(e)(M(u(E(c=t)))))},k.preclip=function(t){return arguments.length?(M=t,b=void 0,P()):M},k.postclip=function(t){return arguments.length?(E=t,S=r=i=o=null,P()):E},k.clipAngle=function(t){return arguments.length?(M=+t?be(b=t*Mt):(b=null,_e),P()):b*bt},k.clipExtent=function(t){return arguments.length?(E=null==t?(S=r=i=o=null,Ue):Ae(S=+t[0][0],r=+t[0][1],i=+t[1][0],o=+t[1][1]),P()):null==S?null:[[S,r],[i,o]]},k.scale=function(t){return arguments.length?(f=+t,A()):f},k.translate=function(t){return arguments.length?(h=+t[0],p=+t[1],A()):[h,p]},k.center=function(t){return arguments.length?(d=t[0]%360*Mt,g=t[1]%360*Mt,A()):[d*bt,g*bt]},k.rotate=function(t){return arguments.length?(y=t[0]%360*Mt,v=t[1]%360*Mt,m=t.length>2?t[2]%360*Mt:0,A()):[y*bt,v*bt,m*bt]},k.angle=function(t){return arguments.length?(x=t%360*Mt,A()):x*bt},k.reflectX=function(t){return arguments.length?(w=t?-1:1,A()):w<0},k.reflectY=function(t){return arguments.length?(_=t?-1:1,A()):_<0},k.precision=function(t){return arguments.length?(u=er(l,N=t*t),P()):Tt(N)},k.fitExtent=function(t,n){return tr(k,t,n)},k.fitSize=function(t,n){return function(t,n,e){return tr(t,[[0,0],n],e)}(k,t,n)},k.fitWidth=function(t,n){return function(t,n,e){return Qe(t,(function(e){var r=+n,i=r/(e[1][0]-e[0][0]),o=(r-i*(e[1][0]+e[0][0]))/2,u=-i*e[0][1];t.scale(150*i).translate([o,u])}),e)}(k,t,n)},k.fitHeight=function(t,n){return function(t,n,e){return Qe(t,(function(e){var r=+n,i=r/(e[1][1]-e[0][1]),o=-i*e[0][0],u=(r-i*(e[1][1]+e[0][1]))/2;t.scale(150*i).translate([o,u])}),e)}(k,t,n)},function(){return n=t.apply(this,arguments),k.invert=n.invert&&$,A()}}((function(){return t}))()}function ur(t,n){var e=kt(n),r=1+kt(t)*e;return[e*At(t)/r,At(n)/r]}function lr(){return or(ur).scale(250).clipAngle(142)}ur.invert=function(t){return function(n,e){var r=Tt(n*n+e*e),i=t(r),o=At(i),u=kt(i);return[Nt(n*o,r*u),jt(r&&e*o/r)]}}((function(t){return 2*Et(t)}));const ar=1e-6;class sr{constructor(){this._x0=this._y0=this._x1=this._y1=null,this._=""}moveTo(t,n){this._+=`M${this._x0=this._x1=+t},${this._y0=this._y1=+n}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._+="Z")}lineTo(t,n){this._+=`L${this._x1=+t},${this._y1=+n}`}arc(t,n,e){const r=(t=+t)+(e=+e),i=n=+n;if(e<0)throw new Error("negative radius");null===this._x1?this._+=`M${r},${i}`:(Math.abs(this._x1-r)>ar||Math.abs(this._y1-i)>ar)&&(this._+="L"+r+","+i),e&&(this._+=`A${e},${e},0,1,1,${t-e},${n}A${e},${e},0,1,1,${this._x1=r},${this._y1=i}`)}rect(t,n,e,r){this._+=`M${this._x0=this._x1=+t},${this._y0=this._y1=+n}h${+e}v${+r}h${-e}Z`}value(){return this._||null}}class cr{constructor(){this._=[]}moveTo(t,n){this._.push([t,n])}closePath(){this._.push(this._[0].slice())}lineTo(t,n){this._.push([t,n])}value(){return this._.length?this._:null}}class fr{constructor(t,[n,e,r,i]=[0,0,960,500]){if(!((r=+r)>=(n=+n)&&(i=+i)>=(e=+e)))throw new Error("invalid bounds");this.delaunay=t,this._circumcenters=new Float64Array(2*t.points.length),this.vectors=new Float64Array(2*t.points.length),this.xmax=r,this.xmin=n,this.ymax=i,this.ymin=e,this._init()}update(){return this.delaunay.update(),this._init(),this}_init(){const{delaunay:{points:t,hull:n,triangles:e},vectors:r}=this;let i,o;const u=this.circumcenters=this._circumcenters.subarray(0,e.length/3*2);for(let r,l,a=0,s=0,c=e.length;a<c;a+=3,s+=2){const c=2*e[a],f=2*e[a+1],h=2*e[a+2],p=t[c],d=t[c+1],g=t[f],y=t[f+1],v=t[h],m=t[h+1],x=g-p,w=y-d,_=v-p,b=m-d,M=2*(x*b-w*_);if(Math.abs(M)<1e-9){if(void 0===i){i=o=0;for(const e of n)i+=t[2*e],o+=t[2*e+1];i/=n.length,o/=n.length}const e=1e9*Math.sign((i-p)*b-(o-d)*_);r=(p+v)/2-e*b,l=(d+m)/2+e*_}else{const t=1/M,n=x*x+w*w,e=_*_+b*b;r=p+(b*n-w*e)*t,l=d+(x*e-_*n)*t}u[s]=r,u[s+1]=l}let l,a,s,c=n[n.length-1],f=4*c,h=t[2*c],p=t[2*c+1];r.fill(0);for(let e=0;e<n.length;++e)c=n[e],l=f,a=h,s=p,f=4*c,h=t[2*c],p=t[2*c+1],r[l+2]=r[f]=s-p,r[l+3]=r[f+1]=h-a}render(t){const n=null==t?t=new sr:void 0,{delaunay:{halfedges:e,inedges:r,hull:i},circumcenters:o,vectors:u}=this;if(i.length<=1)return null;for(let n=0,r=e.length;n<r;++n){const r=e[n];if(r<n)continue;const i=2*Math.floor(n/3),u=2*Math.floor(r/3),l=o[i],a=o[i+1],s=o[u],c=o[u+1];this._renderSegment(l,a,s,c,t)}let l,a=i[i.length-1];for(let n=0;n<i.length;++n){l=a,a=i[n];const e=2*Math.floor(r[a]/3),s=o[e],c=o[e+1],f=4*l,h=this._project(s,c,u[f+2],u[f+3]);h&&this._renderSegment(s,c,h[0],h[1],t)}return n&&n.value()}renderBounds(t){const n=null==t?t=new sr:void 0;return t.rect(this.xmin,this.ymin,this.xmax-this.xmin,this.ymax-this.ymin),n&&n.value()}renderCell(t,n){const e=null==n?n=new sr:void 0,r=this._clip(t);if(null===r||!r.length)return;n.moveTo(r[0],r[1]);let i=r.length;for(;r[0]===r[i-2]&&r[1]===r[i-1]&&i>1;)i-=2;for(let t=2;t<i;t+=2)r[t]===r[t-2]&&r[t+1]===r[t-1]||n.lineTo(r[t],r[t+1]);return n.closePath(),e&&e.value()}*cellPolygons(){const{delaunay:{points:t}}=this;for(let n=0,e=t.length/2;n<e;++n){const t=this.cellPolygon(n);t&&(t.index=n,yield t)}}cellPolygon(t){const n=new cr;return this.renderCell(t,n),n.value()}_renderSegment(t,n,e,r,i){let o;const u=this._regioncode(t,n),l=this._regioncode(e,r);0===u&&0===l?(i.moveTo(t,n),i.lineTo(e,r)):(o=this._clipSegment(t,n,e,r,u,l))&&(i.moveTo(o[0],o[1]),i.lineTo(o[2],o[3]))}contains(t,n,e){return(n=+n)==n&&(e=+e)==e&&this.delaunay._step(t,n,e)===t}*neighbors(t){const n=this._clip(t);if(n)for(const e of this.delaunay.neighbors(t)){const t=this._clip(e);if(t)t:for(let r=0,i=n.length;r<i;r+=2)for(let o=0,u=t.length;o<u;o+=2)if(n[r]===t[o]&&n[r+1]===t[o+1]&&n[(r+2)%i]===t[(o+u-2)%u]&&n[(r+3)%i]===t[(o+u-1)%u]){yield e;break t}}}_cell(t){const{circumcenters:n,delaunay:{inedges:e,halfedges:r,triangles:i}}=this,o=e[t];if(-1===o)return null;const u=[];let l=o;do{const e=Math.floor(l/3);if(u.push(n[2*e],n[2*e+1]),l=l%3==2?l-2:l+1,i[l]!==t)break;l=r[l]}while(l!==o&&-1!==l);return u}_clip(t){if(0===t&&1===this.delaunay.hull.length)return[this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax,this.xmin,this.ymin];const n=this._cell(t);if(null===n)return null;const{vectors:e}=this,r=4*t;return this._simplify(e[r]||e[r+1]?this._clipInfinite(t,n,e[r],e[r+1],e[r+2],e[r+3]):this._clipFinite(t,n))}_clipFinite(t,n){const e=n.length;let r,i,o,u,l=null,a=n[e-2],s=n[e-1],c=this._regioncode(a,s),f=0;for(let h=0;h<e;h+=2)if(r=a,i=s,a=n[h],s=n[h+1],o=c,c=this._regioncode(a,s),0===o&&0===c)u=f,f=0,l?l.push(a,s):l=[a,s];else{let n,e,h,p,d;if(0===o){if(null===(n=this._clipSegment(r,i,a,s,o,c)))continue;[e,h,p,d]=n}else{if(null===(n=this._clipSegment(a,s,r,i,c,o)))continue;[p,d,e,h]=n,u=f,f=this._edgecode(e,h),u&&f&&this._edge(t,u,f,l,l.length),l?l.push(e,h):l=[e,h]}u=f,f=this._edgecode(p,d),u&&f&&this._edge(t,u,f,l,l.length),l?l.push(p,d):l=[p,d]}if(l)u=f,f=this._edgecode(l[0],l[1]),u&&f&&this._edge(t,u,f,l,l.length);else if(this.contains(t,(this.xmin+this.xmax)/2,(this.ymin+this.ymax)/2))return[this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax,this.xmin,this.ymin];return l}_clipSegment(t,n,e,r,i,o){const u=i<o;for(u&&([t,n,e,r,i,o]=[e,r,t,n,o,i]);;){if(0===i&&0===o)return u?[e,r,t,n]:[t,n,e,r];if(i&o)return null;let l,a,s=i||o;8&s?(l=t+(e-t)*(this.ymax-n)/(r-n),a=this.ymax):4&s?(l=t+(e-t)*(this.ymin-n)/(r-n),a=this.ymin):2&s?(a=n+(r-n)*(this.xmax-t)/(e-t),l=this.xmax):(a=n+(r-n)*(this.xmin-t)/(e-t),l=this.xmin),i?(t=l,n=a,i=this._regioncode(t,n)):(e=l,r=a,o=this._regioncode(e,r))}}_clipInfinite(t,n,e,r,i,o){let u,l=Array.from(n);if((u=this._project(l[0],l[1],e,r))&&l.unshift(u[0],u[1]),(u=this._project(l[l.length-2],l[l.length-1],i,o))&&l.push(u[0],u[1]),l=this._clipFinite(t,l))for(let n,e=0,r=l.length,i=this._edgecode(l[r-2],l[r-1]);e<r;e+=2)n=i,i=this._edgecode(l[e],l[e+1]),n&&i&&(e=this._edge(t,n,i,l,e),r=l.length);else this.contains(t,(this.xmin+this.xmax)/2,(this.ymin+this.ymax)/2)&&(l=[this.xmin,this.ymin,this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax]);return l}_edge(t,n,e,r,i){for(;n!==e;){let e,o;switch(n){case 5:n=4;continue;case 4:n=6,e=this.xmax,o=this.ymin;break;case 6:n=2;continue;case 2:n=10,e=this.xmax,o=this.ymax;break;case 10:n=8;continue;case 8:n=9,e=this.xmin,o=this.ymax;break;case 9:n=1;continue;case 1:n=5,e=this.xmin,o=this.ymin}r[i]===e&&r[i+1]===o||!this.contains(t,e,o)||(r.splice(i,0,e,o),i+=2)}return i}_project(t,n,e,r){let i,o,u,l=1/0;if(r<0){if(n<=this.ymin)return null;(i=(this.ymin-n)/r)<l&&(u=this.ymin,o=t+(l=i)*e)}else if(r>0){if(n>=this.ymax)return null;(i=(this.ymax-n)/r)<l&&(u=this.ymax,o=t+(l=i)*e)}if(e>0){if(t>=this.xmax)return null;(i=(this.xmax-t)/e)<l&&(o=this.xmax,u=n+(l=i)*r)}else if(e<0){if(t<=this.xmin)return null;(i=(this.xmin-t)/e)<l&&(o=this.xmin,u=n+(l=i)*r)}return[o,u]}_edgecode(t,n){return(t===this.xmin?1:t===this.xmax?2:0)|(n===this.ymin?4:n===this.ymax?8:0)}_regioncode(t,n){return(t<this.xmin?1:t>this.xmax?2:0)|(n<this.ymin?4:n>this.ymax?8:0)}_simplify(t){if(t&&t.length>4){for(let n=0;n<t.length;n+=2){const e=(n+2)%t.length,r=(n+4)%t.length;(t[n]===t[e]&&t[e]===t[r]||t[n+1]===t[e+1]&&t[e+1]===t[r+1])&&(t.splice(e,2),n-=2)}t.length||(t=null)}return t}}const hr=2*Math.PI,pr=Math.pow;function dr(t){return t[0]}function gr(t){return t[1]}function yr(t,n,e){return[t+Math.sin(t+n)*e,n+Math.cos(t-n)*e]}class vr{static from(t,n=dr,e=gr,r){return new vr("length"in t?function(t,n,e,r){const i=t.length,o=new Float64Array(2*i);for(let u=0;u<i;++u){const i=t[u];o[2*u]=n.call(r,i,u,t),o[2*u+1]=e.call(r,i,u,t)}return o}(t,n,e,r):Float64Array.from(function*(t,n,e,r){let i=0;for(const o of t)yield n.call(r,o,i,t),yield e.call(r,o,i,t),++i}(t,n,e,r)))}constructor(t){this._delaunator=new ut(t),this.inedges=new Int32Array(t.length/2),this._hullIndex=new Int32Array(t.length/2),this.points=this._delaunator.coords,this._init()}update(){return this._delaunator.update(),this._init(),this}_init(){const t=this._delaunator,n=this.points;if(t.hull&&t.hull.length>2&&function(t){const{triangles:n,coords:e}=t;for(let t=0;t<n.length;t+=3){const r=2*n[t],i=2*n[t+1],o=2*n[t+2];if((e[o]-e[r])*(e[i+1]-e[r+1])-(e[i]-e[r])*(e[o+1]-e[r+1])>1e-10)return!1}return!0}(t)){this.collinear=Int32Array.from({length:n.length/2},((t,n)=>n)).sort(((t,e)=>n[2*t]-n[2*e]||n[2*t+1]-n[2*e+1]));const t=this.collinear[0],e=this.collinear[this.collinear.length-1],r=[n[2*t],n[2*t+1],n[2*e],n[2*e+1]],i=1e-8*Math.hypot(r[3]-r[1],r[2]-r[0]);for(let t=0,e=n.length/2;t<e;++t){const e=yr(n[2*t],n[2*t+1],i);n[2*t]=e[0],n[2*t+1]=e[1]}this._delaunator=new ut(n)}else delete this.collinear;const e=this.halfedges=this._delaunator.halfedges,r=this.hull=this._delaunator.hull,i=this.triangles=this._delaunator.triangles,o=this.inedges.fill(-1),u=this._hullIndex.fill(-1);for(let t=0,n=e.length;t<n;++t){const n=i[t%3==2?t-2:t+1];-1!==e[t]&&-1!==o[n]||(o[n]=t)}for(let t=0,n=r.length;t<n;++t)u[r[t]]=t;r.length<=2&&r.length>0&&(this.triangles=new Int32Array(3).fill(-1),this.halfedges=new Int32Array(3).fill(-1),this.triangles[0]=r[0],o[r[0]]=1,2===r.length&&(o[r[1]]=0,this.triangles[1]=r[1],this.triangles[2]=r[1]))}voronoi(t){return new fr(this,t)}*neighbors(t){const{inedges:n,hull:e,_hullIndex:r,halfedges:i,triangles:o,collinear:u}=this;if(u){const n=u.indexOf(t);return n>0&&(yield u[n-1]),void(n<u.length-1&&(yield u[n+1]))}const l=n[t];if(-1===l)return;let a=l,s=-1;do{if(yield s=o[a],a=a%3==2?a-2:a+1,o[a]!==t)return;if(a=i[a],-1===a){const n=e[(r[t]+1)%e.length];return void(n!==s&&(yield n))}}while(a!==l)}find(t,n,e=0){if((t=+t)!=t||(n=+n)!=n)return-1;const r=e;let i;for(;(i=this._step(e,t,n))>=0&&i!==e&&i!==r;)e=i;return i}_step(t,n,e){const{inedges:r,hull:i,_hullIndex:o,halfedges:u,triangles:l,points:a}=this;if(-1===r[t]||!a.length)return(t+1)%(a.length>>1);let s=t,c=pr(n-a[2*t],2)+pr(e-a[2*t+1],2);const f=r[t];let h=f;do{let r=l[h];const f=pr(n-a[2*r],2)+pr(e-a[2*r+1],2);if(f<c&&(c=f,s=r),h=h%3==2?h-2:h+1,l[h]!==t)break;if(h=u[h],-1===h){if(h=i[(o[t]+1)%i.length],h!==r&&pr(n-a[2*h],2)+pr(e-a[2*h+1],2)<c)return h;break}}while(h!==f);return s}render(t){const n=null==t?t=new sr:void 0,{points:e,halfedges:r,triangles:i}=this;for(let n=0,o=r.length;n<o;++n){const o=r[n];if(o<n)continue;const u=2*i[n],l=2*i[o];t.moveTo(e[u],e[u+1]),t.lineTo(e[l],e[l+1])}return this.renderHull(t),n&&n.value()}renderPoints(t,n){void 0!==n||t&&"function"==typeof t.moveTo||(n=t,t=null),n=null==n?2:+n;const e=null==t?t=new sr:void 0,{points:r}=this;for(let e=0,i=r.length;e<i;e+=2){const i=r[e],o=r[e+1];t.moveTo(i+n,o),t.arc(i,o,n,0,hr)}return e&&e.value()}renderHull(t){const n=null==t?t=new sr:void 0,{hull:e,points:r}=this,i=2*e[0],o=e.length;t.moveTo(r[i],r[i+1]);for(let n=1;n<o;++n){const i=2*e[n];t.lineTo(r[i],r[i+1])}return t.closePath(),n&&n.value()}hullPolygon(){const t=new cr;return this.renderHull(t),t.value()}renderTriangle(t,n){const e=null==n?n=new sr:void 0,{points:r,triangles:i}=this,o=2*i[t*=3],u=2*i[t+1],l=2*i[t+2];return n.moveTo(r[o],r[o+1]),n.lineTo(r[u],r[u+1]),n.lineTo(r[l],r[l+1]),n.closePath(),e&&e.value()}*trianglePolygons(){const{triangles:t}=this;for(let n=0,e=t.length/3;n<e;++n)yield this.trianglePolygon(n)}trianglePolygon(t){const n=new cr;return this.renderTriangle(t,n),n.value()}}const mr=Math.PI,xr=mr/2,wr=180/mr,_r=mr/180,br=Math.atan2,Mr=Math.cos,Sr=Math.max,Er=Math.min,Nr=Math.sin,kr=Math.sign||function(t){return t>0?1:t<0?-1:0},$r=Math.sqrt;function Ar(t,n){return t[0]*n[0]+t[1]*n[1]+t[2]*n[2]}function Pr(t,n){return[t[1]*n[2]-t[2]*n[1],t[2]*n[0]-t[0]*n[2],t[0]*n[1]-t[1]*n[0]]}function Tr(t,n){return[t[0]+n[0],t[1]+n[1],t[2]+n[2]]}function jr(t){var n=$r(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]);return[t[0]/n,t[1]/n,t[2]/n]}function Fr(t){return[br(t[1],t[0])*wr,(n=Sr(-1,Er(1,t[2])),(n>1?xr:n<-1?-xr:Math.asin(n))*wr)];var n}function zr(t){const n=t[0]*_r,e=t[1]*_r,r=Mr(e);return[r*Mr(n),r*Nr(n),Nr(e)]}function Ir(t){return Ar((t=t.map((t=>zr(t))))[0],Pr(t[2],t[1]))}function Zr(t){const n=function(t){if(t.length<2)return{};let n=0;for(;isNaN(t[n][0]+t[n][1])&&n++<t.length;);const e=function(t){function n(n){return(n=t(n[0]*Mt,n[1]*Mt))[0]*=bt,n[1]*=bt,n}return t=ue(t[0]*Mt,t[1]*Mt,t.length>2?t[2]*Mt:0),n.invert=function(n){return(n=t.invert(n[0]*Mt,n[1]*Mt))[0]*=bt,n[1]*=bt,n},n}(t[n]),r=lr().translate([0,0]).scale(1).rotate(e.invert([180,0]));t=t.map(r);const i=[];let o=1;for(let n=0,e=t.length;n<e;n++){let e=t[n][0]**2+t[n][1]**2;!isFinite(e)||e>1e32?i.push(n):e>o&&(o=e)}const u=1e6*$r(o);i.forEach((n=>t[n]=[u,0])),t.push([0,u]),t.push([-u,0]),t.push([0,-u]);const l=vr.from(t);l.projection=r;const{triangles:a,halfedges:s,inedges:c}=l;for(let e=0,r=s.length;e<r;e++)if(s[e]<0){const t=e%3==2?e-2:e+1,r=e%3==0?e+2:e-1,i=s[t],o=s[r];s[i]=o,s[o]=i,s[t]=s[r]=-1,a[e]=a[t]=a[r]=n,c[a[i]]=i%3==0?i+2:i-1,c[a[o]]=o%3==0?o+2:o-1,e+=2-e%3}else a[e]>t.length-3-1&&(a[e]=n);return l}(t),e=function(t){const{triangles:n}=t;if(!n)return[];const e=[];for(let t=0,r=n.length/3;t<r;t++){const r=n[3*t],i=n[3*t+1],o=n[3*t+2];r!==i&&i!==o&&e.push([r,o,i])}return e}(n),r=function(t,n){const e=new Set;return 2===n.length?[[0,1]]:(t.forEach((t=>{if(t[0]!==t[1]&&!(Ir(t.map((t=>n[t])))<0))for(let n,r=0;r<3;r++)n=(r+1)%3,e.add(d([t[r],t[n]]).join("-"))})),Array.from(e,(t=>t.split("-").map(Number))))}(e,t),i=function(t,n){const e=[];t.forEach((t=>{for(let n=0;n<3;n++){const r=t[n],i=t[(n+1)%3];e[r]=e[r]||[],e[r].push(i)}})),0===t.length&&(2===n?(e[0]=[1],e[1]=[0]):1===n&&(e[0]=[]));return e}(e,t.length),o=function(t,n){function e(t,n){let e=t[0]-n[0],r=t[1]-n[1],i=t[2]-n[2];return e*e+r*r+i*i}return function(r,i,o){void 0===o&&(o=0);let u,l,a=o;const s=zr([r,i]);do{u=o,o=null,l=e(s,zr(n[u])),t[u].forEach((t=>{let r=e(s,zr(n[t]));if(r<l)return l=r,o=t,void(a=t)}))}while(null!==o);return a}}(i,t),u=function(t,n){return t.map((t=>{const e=t.map((t=>n[t])).map(zr);return Fr(jr(Tr(Tr(Pr(e[1],e[0]),Pr(e[2],e[1])),Pr(e[0],e[2]))))}))}(e,t),{polygons:l,centers:a}=function(t,n,e){const r=[],i=t.slice();if(0===n.length){if(e.length<2)return{polygons:r,centers:i};if(2===e.length){const t=zr(e[0]),n=zr(e[1]),o=jr(Tr(t,n)),l=jr(Pr(t,n)),a=Pr(o,l),s=[o,Pr(o,a),Pr(Pr(o,a),a),Pr(Pr(Pr(o,a),a),a)].map(Fr).map(u);return r.push(s),r.push(s.slice().reverse()),{polygons:r,centers:i}}}n.forEach(((t,n)=>{for(let e=0;e<3;e++){const i=t[e],o=t[(e+1)%3],u=t[(e+2)%3];r[i]=r[i]||[],r[i].push([o,u,n,[i,o,u]])}}));const o=r.map((t=>{const n=[t[0][2]];let r=t[0][1];for(let e=1;e<t.length;e++)for(let e=0;e<t.length;e++)if(t[e][0]==r){r=t[e][1],n.push(t[e][2]);break}if(n.length>2)return n;if(2==n.length){const r=Or(e[t[0][3][0]],e[t[0][3][1]],i[n[0]]),o=Or(e[t[0][3][2]],e[t[0][3][0]],i[n[0]]),l=u(r),a=u(o);return[n[0],a,n[1],l]}}));function u(t){let e=-1;return i.slice(n.length,1/0).forEach(((r,i)=>{r[0]===t[0]&&r[1]===t[1]&&(e=i+n.length)})),e<0&&(e=i.length,i.push(t)),e}return{polygons:o,centers:i}}(u,e,t),s=function(t){const n=[];return t.forEach((t=>{if(!t)return;let e=t[t.length-1];for(let r of t)r>e&&n.push([e,r]),e=r})),n}(l),c=function(t,n){const e=new Set,r=[];t.map((t=>{if(!(Ir(t.map((t=>n[t>n.length?0:t])))>1e-12))for(let n=0;n<3;n++){let r=[t[n],t[(n+1)%3]],i=`${r[0]}-${r[1]}`;e.has(i)?e.delete(i):e.add(`${r[1]}-${r[0]}`)}}));const i=new Map;let o;if(e.forEach((t=>{t=t.split("-").map(Number),i.set(t[0],t[1]),o=t[0]})),void 0===o)return r;let u=o;do{r.push(u);let t=i.get(u);i.set(u,-1),u=t}while(u>-1&&u!==o);return r}(e,t),f=function(t,n){return function(e){const r=new Map,i=new Map;return t.forEach(((t,n)=>{const o=t.join("-");r.set(o,e[n]),i.set(o,!0)})),n.forEach((t=>{let n=0,e=-1;for(let i=0;i<3;i++){let o=d([t[i],t[(i+1)%3]]).join("-");r.get(o)>n&&(n=r.get(o),e=o)}i.set(e,!1)})),t.map((t=>i.get(t.join("-"))))}}(r,e);return{delaunay:n,edges:r,triangles:e,centers:a,neighbors:i,polygons:l,mesh:s,hull:c,urquhart:f,find:o}}function Or(t,n,e){t=zr(t),n=zr(n),e=zr(e);const r=kr(Ar(Pr(n,t),e));return Fr(jr(Tr(t,n)).map((t=>r*t)))}function Lr(t,n){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(n).domain(t)}return this}function qr(t,n,e){t.prototype=n.prototype=e,e.constructor=t}function Hr(t,n){var e=Object.create(t.prototype);for(var r in n)e[r]=n[r];return e}function Cr(){}var Rr=.7,Br=1/Rr,Gr="\\s*([+-]?\\d+)\\s*",Ur="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Kr="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Dr=/^#([0-9a-f]{3,8})$/,Vr=new RegExp(`^rgb\\(${Gr},${Gr},${Gr}\\)$`),Xr=new RegExp(`^rgb\\(${Kr},${Kr},${Kr}\\)$`),Jr=new RegExp(`^rgba\\(${Gr},${Gr},${Gr},${Ur}\\)$`),Yr=new RegExp(`^rgba\\(${Kr},${Kr},${Kr},${Ur}\\)$`),Wr=new RegExp(`^hsl\\(${Ur},${Kr},${Kr}\\)$`),Qr=new RegExp(`^hsla\\(${Ur},${Kr},${Kr},${Ur}\\)$`),ti={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function ni(){return this.rgb().formatHex()}function ei(){return this.rgb().formatRgb()}function ri(t){var n,e;return t=(t+"").trim().toLowerCase(),(n=Dr.exec(t))?(e=n[1].length,n=parseInt(n[1],16),6===e?ii(n):3===e?new li(n>>8&15|n>>4&240,n>>4&15|240&n,(15&n)<<4|15&n,1):8===e?oi(n>>24&255,n>>16&255,n>>8&255,(255&n)/255):4===e?oi(n>>12&15|n>>8&240,n>>8&15|n>>4&240,n>>4&15|240&n,((15&n)<<4|15&n)/255):null):(n=Vr.exec(t))?new li(n[1],n[2],n[3],1):(n=Xr.exec(t))?new li(255*n[1]/100,255*n[2]/100,255*n[3]/100,1):(n=Jr.exec(t))?oi(n[1],n[2],n[3],n[4]):(n=Yr.exec(t))?oi(255*n[1]/100,255*n[2]/100,255*n[3]/100,n[4]):(n=Wr.exec(t))?pi(n[1],n[2]/100,n[3]/100,1):(n=Qr.exec(t))?pi(n[1],n[2]/100,n[3]/100,n[4]):ti.hasOwnProperty(t)?ii(ti[t]):"transparent"===t?new li(NaN,NaN,NaN,0):null}function ii(t){return new li(t>>16&255,t>>8&255,255&t,1)}function oi(t,n,e,r){return r<=0&&(t=n=e=NaN),new li(t,n,e,r)}function ui(t,n,e,r){return 1===arguments.length?((i=t)instanceof Cr||(i=ri(i)),i?new li((i=i.rgb()).r,i.g,i.b,i.opacity):new li):new li(t,n,e,null==r?1:r);var i}function li(t,n,e,r){this.r=+t,this.g=+n,this.b=+e,this.opacity=+r}function ai(){return`#${hi(this.r)}${hi(this.g)}${hi(this.b)}`}function si(){const t=ci(this.opacity);return`${1===t?"rgb(":"rgba("}${fi(this.r)}, ${fi(this.g)}, ${fi(this.b)}${1===t?")":`, ${t})`}`}function ci(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function fi(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function hi(t){return((t=fi(t))<16?"0":"")+t.toString(16)}function pi(t,n,e,r){return r<=0?t=n=e=NaN:e<=0||e>=1?t=n=NaN:n<=0&&(t=NaN),new gi(t,n,e,r)}function di(t){if(t instanceof gi)return new gi(t.h,t.s,t.l,t.opacity);if(t instanceof Cr||(t=ri(t)),!t)return new gi;if(t instanceof gi)return t;var n=(t=t.rgb()).r/255,e=t.g/255,r=t.b/255,i=Math.min(n,e,r),o=Math.max(n,e,r),u=NaN,l=o-i,a=(o+i)/2;return l?(u=n===o?(e-r)/l+6*(e<r):e===o?(r-n)/l+2:(n-e)/l+4,l/=a<.5?o+i:2-o-i,u*=60):l=a>0&&a<1?0:u,new gi(u,l,a,t.opacity)}function gi(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}function yi(t){return(t=(t||0)%360)<0?t+360:t}function vi(t){return Math.max(0,Math.min(1,t||0))}function mi(t,n,e){return 255*(t<60?n+(e-n)*t/60:t<180?e:t<240?n+(e-n)*(240-t)/60:n)}qr(Cr,ri,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:ni,formatHex:ni,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return di(this).formatHsl()},formatRgb:ei,toString:ei}),qr(li,ui,Hr(Cr,{brighter(t){return t=null==t?Br:Math.pow(Br,t),new li(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?Rr:Math.pow(Rr,t),new li(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new li(fi(this.r),fi(this.g),fi(this.b),ci(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:ai,formatHex:ai,formatHex8:function(){return`#${hi(this.r)}${hi(this.g)}${hi(this.b)}${hi(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:si,toString:si})),qr(gi,(function(t,n,e,r){return 1===arguments.length?di(t):new gi(t,n,e,null==r?1:r)}),Hr(Cr,{brighter(t){return t=null==t?Br:Math.pow(Br,t),new gi(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?Rr:Math.pow(Rr,t),new gi(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+360*(this.h<0),n=isNaN(t)||isNaN(this.s)?0:this.s,e=this.l,r=e+(e<.5?e:1-e)*n,i=2*e-r;return new li(mi(t>=240?t-240:t+120,i,r),mi(t,i,r),mi(t<120?t+240:t-120,i,r),this.opacity)},clamp(){return new gi(yi(this.h),vi(this.s),vi(this.l),ci(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=ci(this.opacity);return`${1===t?"hsl(":"hsla("}${yi(this.h)}, ${100*vi(this.s)}%, ${100*vi(this.l)}%${1===t?")":`, ${t})`}`}}));var xi=t=>()=>t;function wi(t){return 1==(t=+t)?_i:function(n,e){return e-n?function(t,n,e){return t=Math.pow(t,e),n=Math.pow(n,e)-t,e=1/e,function(r){return Math.pow(t+r*n,e)}}(n,e,t):xi(isNaN(n)?e:n)}}function _i(t,n){var e=n-t;return e?function(t,n){return function(e){return t+e*n}}(t,e):xi(isNaN(t)?n:t)}var bi=function t(n){var e=wi(n);function r(t,n){var r=e((t=ui(t)).r,(n=ui(n)).r),i=e(t.g,n.g),o=e(t.b,n.b),u=_i(t.opacity,n.opacity);return function(n){return t.r=r(n),t.g=i(n),t.b=o(n),t.opacity=u(n),t+""}}return r.gamma=t,r}(1);function Mi(t,n){n||(n=[]);var e,r=t?Math.min(n.length,t.length):0,i=n.slice();return function(o){for(e=0;e<r;++e)i[e]=t[e]*(1-o)+n[e]*o;return i}}function Si(t,n){var e,r=n?n.length:0,i=t?Math.min(r,t.length):0,o=new Array(i),u=new Array(r);for(e=0;e<i;++e)o[e]=Ti(t[e],n[e]);for(;e<r;++e)u[e]=n[e];return function(t){for(e=0;e<i;++e)u[e]=o[e](t);return u}}function Ei(t,n){var e=new Date;return t=+t,n=+n,function(r){return e.setTime(t*(1-r)+n*r),e}}function Ni(t,n){return t=+t,n=+n,function(e){return t*(1-e)+n*e}}function ki(t,n){var e,r={},i={};for(e in null!==t&&"object"==typeof t||(t={}),null!==n&&"object"==typeof n||(n={}),n)e in t?r[e]=Ti(t[e],n[e]):i[e]=n[e];return function(t){for(e in r)i[e]=r[e](t);return i}}var $i=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Ai=new RegExp($i.source,"g");function Pi(t,n){var e,r,i,o=$i.lastIndex=Ai.lastIndex=0,u=-1,l=[],a=[];for(t+="",n+="";(e=$i.exec(t))&&(r=Ai.exec(n));)(i=r.index)>o&&(i=n.slice(o,i),l[u]?l[u]+=i:l[++u]=i),(e=e[0])===(r=r[0])?l[u]?l[u]+=r:l[++u]=r:(l[++u]=null,a.push({i:u,x:Ni(e,r)})),o=Ai.lastIndex;return o<n.length&&(i=n.slice(o),l[u]?l[u]+=i:l[++u]=i),l.length<2?a[0]?function(t){return function(n){return t(n)+""}}(a[0].x):function(t){return function(){return t}}(n):(n=a.length,function(t){for(var e,r=0;r<n;++r)l[(e=a[r]).i]=e.x(t);return l.join("")})}function Ti(t,n){var e,r,i=typeof n;return null==n||"boolean"===i?xi(n):("number"===i?Ni:"string"===i?(e=ri(n))?(n=e,bi):Pi:n instanceof ri?bi:n instanceof Date?Ei:(r=n,!ArrayBuffer.isView(r)||r instanceof DataView?Array.isArray(n)?Si:"function"!=typeof n.valueOf&&"function"!=typeof n.toString||isNaN(n)?ki:Ni:Mi))(t,n)}function ji(t,n){return t=+t,n=+n,function(e){return Math.round(t*(1-e)+n*e)}}function Fi(t){return+t}var zi=[0,1];function Ii(t){return t}function Zi(t,n){return(n-=t=+t)?function(e){return(e-t)/n}:(e=isNaN(n)?NaN:.5,function(){return e});var e}function Oi(t,n,e){var r=t[0],i=t[1],o=n[0],u=n[1];return i<r?(r=Zi(i,r),o=e(u,o)):(r=Zi(r,i),o=e(o,u)),function(t){return o(r(t))}}function Li(t,n,e){var r=Math.min(t.length,n.length)-1,i=new Array(r),o=new Array(r),u=-1;for(t[r]<t[0]&&(t=t.slice().reverse(),n=n.slice().reverse());++u<r;)i[u]=Zi(t[u],t[u+1]),o[u]=e(n[u],n[u+1]);return function(n){var e=p(t,n,1,r)-1;return o[e](i[e](n))}}function qi(){var t,n,e,r,i,o,u=zi,l=zi,a=Ti,s=Ii;function c(){var t,n,e,a=Math.min(u.length,l.length);return s!==Ii&&(t=u[0],n=u[a-1],t>n&&(e=t,t=n,n=e),s=function(e){return Math.max(t,Math.min(n,e))}),r=a>2?Li:Oi,i=o=null,f}function f(n){return null==n||isNaN(n=+n)?e:(i||(i=r(u.map(t),l,a)))(t(s(n)))}return f.invert=function(e){return s(n((o||(o=r(l,u.map(t),Ni)))(e)))},f.domain=function(t){return arguments.length?(u=Array.from(t,Fi),c()):u.slice()},f.range=function(t){return arguments.length?(l=Array.from(t),c()):l.slice()},f.rangeRound=function(t){return l=Array.from(t),a=ji,c()},f.clamp=function(t){return arguments.length?(s=!!t||Ii,c()):s!==Ii},f.interpolate=function(t){return arguments.length?(a=t,c()):a},f.unknown=function(t){return arguments.length?(e=t,f):e},function(e,r){return t=e,n=r,c()}}function Hi(t,n){if((e=(t=n?t.toExponential(n-1):t.toExponential()).indexOf("e"))<0)return null;var e,r=t.slice(0,e);return[r.length>1?r[0]+r.slice(2):r,+t.slice(e+1)]}function Ci(t){return(t=Hi(Math.abs(t)))?t[1]:NaN}var Ri,Bi=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Gi(t){if(!(n=Bi.exec(t)))throw new Error("invalid format: "+t);var n;return new Ui({fill:n[1],align:n[2],sign:n[3],symbol:n[4],zero:n[5],width:n[6],comma:n[7],precision:n[8]&&n[8].slice(1),trim:n[9],type:n[10]})}function Ui(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function Ki(t,n){var e=Hi(t,n);if(!e)return t+"";var r=e[0],i=e[1];return i<0?"0."+new Array(-i).join("0")+r:r.length>i+1?r.slice(0,i+1)+"."+r.slice(i+1):r+new Array(i-r.length+2).join("0")}Gi.prototype=Ui.prototype,Ui.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};var Di={"%":(t,n)=>(100*t).toFixed(n),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,n)=>t.toExponential(n),f:(t,n)=>t.toFixed(n),g:(t,n)=>t.toPrecision(n),o:t=>Math.round(t).toString(8),p:(t,n)=>Ki(100*t,n),r:Ki,s:function(t,n){var e=Hi(t,n);if(!e)return t+"";var r=e[0],i=e[1],o=i-(Ri=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,u=r.length;return o===u?r:o>u?r+new Array(o-u+1).join("0"):o>0?r.slice(0,o)+"."+r.slice(o):"0."+new Array(1-o).join("0")+Hi(t,Math.max(0,n+o-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function Vi(t){return t}var Xi,Ji,Yi,Wi=Array.prototype.map,Qi=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function to(t){var n,e,r=void 0===t.grouping||void 0===t.thousands?Vi:(n=Wi.call(t.grouping,Number),e=t.thousands+"",function(t,r){for(var i=t.length,o=[],u=0,l=n[0],a=0;i>0&&l>0&&(a+l+1>r&&(l=Math.max(1,r-a)),o.push(t.substring(i-=l,i+l)),!((a+=l+1)>r));)l=n[u=(u+1)%n.length];return o.reverse().join(e)}),i=void 0===t.currency?"":t.currency[0]+"",o=void 0===t.currency?"":t.currency[1]+"",u=void 0===t.decimal?".":t.decimal+"",l=void 0===t.numerals?Vi:function(t){return function(n){return n.replace(/[0-9]/g,(function(n){return t[+n]}))}}(Wi.call(t.numerals,String)),a=void 0===t.percent?"%":t.percent+"",s=void 0===t.minus?"−":t.minus+"",c=void 0===t.nan?"NaN":t.nan+"";function f(t){var n=(t=Gi(t)).fill,e=t.align,f=t.sign,h=t.symbol,p=t.zero,d=t.width,g=t.comma,y=t.precision,v=t.trim,m=t.type;"n"===m?(g=!0,m="g"):Di[m]||(void 0===y&&(y=12),v=!0,m="g"),(p||"0"===n&&"="===e)&&(p=!0,n="0",e="=");var x="$"===h?i:"#"===h&&/[boxX]/.test(m)?"0"+m.toLowerCase():"",w="$"===h?o:/[%p]/.test(m)?a:"",_=Di[m],b=/[defgprs%]/.test(m);function M(t){var i,o,a,h=x,M=w;if("c"===m)M=_(t)+M,t="";else{var S=(t=+t)<0||1/t<0;if(t=isNaN(t)?c:_(Math.abs(t),y),v&&(t=function(t){t:for(var n,e=t.length,r=1,i=-1;r<e;++r)switch(t[r]){case".":i=n=r;break;case"0":0===i&&(i=r),n=r;break;default:if(!+t[r])break t;i>0&&(i=0)}return i>0?t.slice(0,i)+t.slice(n+1):t}(t)),S&&0==+t&&"+"!==f&&(S=!1),h=(S?"("===f?f:s:"-"===f||"("===f?"":f)+h,M=("s"===m?Qi[8+Ri/3]:"")+M+(S&&"("===f?")":""),b)for(i=-1,o=t.length;++i<o;)if(48>(a=t.charCodeAt(i))||a>57){M=(46===a?u+t.slice(i+1):t.slice(i))+M,t=t.slice(0,i);break}}g&&!p&&(t=r(t,1/0));var E=h.length+t.length+M.length,N=E<d?new Array(d-E+1).join(n):"";switch(g&&p&&(t=r(N+t,N.length?d-M.length:1/0),N=""),e){case"<":t=h+t+M+N;break;case"=":t=h+N+t+M;break;case"^":t=N.slice(0,E=N.length>>1)+h+t+M+N.slice(E);break;default:t=N+h+t+M}return l(t)}return y=void 0===y?6:/[gprs]/.test(m)?Math.max(1,Math.min(21,y)):Math.max(0,Math.min(20,y)),M.toString=function(){return t+""},M}return{format:f,formatPrefix:function(t,n){var e=f(((t=Gi(t)).type="f",t)),r=3*Math.max(-8,Math.min(8,Math.floor(Ci(n)/3))),i=Math.pow(10,-r),o=Qi[8+r/3];return function(t){return e(i*t)+o}}}}function no(t,n,e,r){var i,o=function(t,n,e){e=+e;const r=(n=+n)<(t=+t),i=r?w(n,t,e):w(t,n,e);return(r?-1:1)*(i<0?1/-i:i)}(t,n,e);switch((r=Gi(null==r?",f":r)).type){case"s":var u=Math.max(Math.abs(t),Math.abs(n));return null!=r.precision||isNaN(i=function(t,n){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(Ci(n)/3)))-Ci(Math.abs(t)))}(o,u))||(r.precision=i),Yi(r,u);case"":case"e":case"g":case"p":case"r":null!=r.precision||isNaN(i=function(t,n){return t=Math.abs(t),n=Math.abs(n)-t,Math.max(0,Ci(n)-Ci(t))+1}(o,Math.max(Math.abs(t),Math.abs(n))))||(r.precision=i-("e"===r.type));break;case"f":case"%":null!=r.precision||isNaN(i=function(t){return Math.max(0,-Ci(Math.abs(t)))}(o))||(r.precision=i-2*("%"===r.type))}return Ji(r)}function eo(t){var n=t.domain;return t.ticks=function(t){var e=n();return function(t,n,e){if(!((e=+e)>0))return[];if((t=+t)==(n=+n))return[t];const r=n<t,[i,o,u]=r?x(n,t,e):x(t,n,e);if(!(o>=i))return[];const l=o-i+1,a=new Array(l);if(r)if(u<0)for(let t=0;t<l;++t)a[t]=(o-t)/-u;else for(let t=0;t<l;++t)a[t]=(o-t)*u;else if(u<0)for(let t=0;t<l;++t)a[t]=(i+t)/-u;else for(let t=0;t<l;++t)a[t]=(i+t)*u;return a}(e[0],e[e.length-1],null==t?10:t)},t.tickFormat=function(t,e){var r=n();return no(r[0],r[r.length-1],null==t?10:t,e)},t.nice=function(e){null==e&&(e=10);var r,i,o=n(),u=0,l=o.length-1,a=o[u],s=o[l],c=10;for(s<a&&(i=a,a=s,s=i,i=u,u=l,l=i);c-- >0;){if((i=w(a,s,e))===r)return o[u]=a,o[l]=s,n(o);if(i>0)a=Math.floor(a/i)*i,s=Math.ceil(s/i)*i;else{if(!(i<0))break;a=Math.ceil(a*i)/i,s=Math.floor(s*i)/i}r=i}return t},t}function ro(){var t=qi()(Ii,Ii);return t.copy=function(){return n=t,ro().domain(n.domain()).range(n.range()).interpolate(n.interpolate()).clamp(n.clamp()).unknown(n.unknown());var n},Lr.apply(t,arguments),eo(t)}function io(t){var n=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).resolution,e=void 0===n?1/0:n,r=function(t,n){return t.map((function(t){var e,r=[];return t.forEach((function(t){if(e){var i=180*Ze(t,e)/Math.PI;if(i>n)for(var o=function(t,n){var e=t[0]*Mt,r=t[1]*Mt,i=n[0]*Mt,o=n[1]*Mt,u=kt(r),l=At(r),a=kt(o),s=At(o),c=u*kt(e),f=u*At(e),h=a*kt(i),p=a*At(i),d=2*jt(Tt(Ft(o-r)+u*a*Ft(i-e))),g=At(d),y=d?function(t){var n=At(t*=d)/g,e=At(d-t)/g,r=e*c+n*h,i=e*f+n*p,o=e*l+n*s;return[Nt(i,r)*bt,Nt(o,Tt(r*r+i*i))*bt]}:function(){return[e*bt,r*bt]};return y.distance=d,y}(e,t),u=1/Math.ceil(i/n),l=u;l<1;)r.push(o(l)),l+=u}r.push(e=t)})),r}))}(t,e),i=_(r),o=function(t,n){var e={type:"Polygon",coordinates:t},r=Un(e),i=u(r,2),o=u(i[0],2),l=o[0],a=o[1],s=u(i[1],2),c=s[0],f=s[1];if(Math.min(Math.abs(c-l),Math.abs(f-a))<n)return[];var h=l>c||f>=89||a<=-89;return function(t){for(var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},e=n.minLng,r=n.maxLng,i=n.minLat,o=n.maxLat,u=Math.round(Math.pow(360/t,2)/Math.PI),l=(1+Math.sqrt(5))/2,a=function(t){return t/l*360%360-180},s=function(t){return Math.acos(2*t/u-1)/Math.PI*180-90},c=function(t){return u*(Math.cos((t+90)*Math.PI/180)+1)/2},f=[void 0!==o?Math.ceil(c(o)):0,void 0!==i?Math.floor(c(i)):u-1],h=void 0===e&&void 0===r?function(){return!0}:void 0===e?function(t){return t<=r}:void 0===r?function(t){return t>=e}:r>=e?function(t){return t>=e&&t<=r}:function(t){return t>=e||t<=r},p=[],d=f[0];d<=f[1];d++){var g=a(d);h(g)&&p.push([g,s(d)])}return p}(n,{minLng:l,maxLng:c,minLat:a,maxLat:f}).filter((function(t){return oo(t,e,h)}))}(t,e),a=[].concat(l(i),l(o)),s={type:"Polygon",coordinates:t},c=u(Un(s),2),f=u(c[0],2),h=f[0],p=f[1],g=u(c[1],2),y=g[0],v=g[1],m=h>y||v>=89||p<=-89,x=[];if(m){var w=function(t){const n=function(t){if(n.delaunay=null,n._data=t,"object"==typeof n._data&&"FeatureCollection"===n._data.type&&(n._data=n._data.features),"object"==typeof n._data){const t=n._data.map((t=>[n._vx(t),n._vy(t),t])).filter((t=>isFinite(t[0]+t[1])));n.points=t.map((t=>[t[0],t[1]])),n.valid=t.map((t=>t[2])),n.delaunay=Zr(n.points)}return n};return n._vx=function(t){return"object"==typeof t&&"type"in t?re(t)[0]:0 in t?t[0]:void 0},n._vy=function(t){return"object"==typeof t&&"type"in t?re(t)[1]:1 in t?t[1]:void 0},n.x=function(t){return t?(n._vx=t,n):n._vx},n.y=function(t){return t?(n._vy=t,n):n._vy},n.polygons=function(t){if(void 0!==t&&n(t),!n.delaunay)return!1;const e={type:"FeatureCollection",features:[]};return 0===n.valid.length||(n.delaunay.polygons.forEach(((t,r)=>e.features.push({type:"Feature",geometry:t?{type:"Polygon",coordinates:[[...t,t[0]].map((t=>n.delaunay.centers[t]))]}:null,properties:{site:n.valid[r],sitecoordinates:n.points[r],neighbours:n.delaunay.neighbors[r]}}))),1===n.valid.length&&e.features.push({type:"Feature",geometry:{type:"Sphere"},properties:{site:n.valid[0],sitecoordinates:n.points[0],neighbours:[]}})),e},n.triangles=function(t){return void 0!==t&&n(t),!!n.delaunay&&{type:"FeatureCollection",features:n.delaunay.triangles.map(((t,e)=>((t=t.map((t=>n.points[t]))).center=n.delaunay.centers[e],t))).filter((t=>Ir(t)>0)).map((t=>({type:"Feature",properties:{circumcenter:t.center},geometry:{type:"Polygon",coordinates:[[...t,t[0]]]}})))}},n.links=function(t){if(void 0!==t&&n(t),!n.delaunay)return!1;const e=n.delaunay.edges.map((t=>Ze(n.points[t[0]],n.points[t[1]]))),r=n.delaunay.urquhart(e);return{type:"FeatureCollection",features:n.delaunay.edges.map(((t,i)=>({type:"Feature",properties:{source:n.valid[t[0]],target:n.valid[t[1]],length:e[i],urquhart:!!r[i]},geometry:{type:"LineString",coordinates:[n.points[t[0]],n.points[t[1]]]}})))}},n.mesh=function(t){return void 0!==t&&n(t),!!n.delaunay&&{type:"MultiLineString",coordinates:n.delaunay.edges.map((t=>[n.points[t[0]],n.points[t[1]]]))}},n.cellMesh=function(t){if(void 0!==t&&n(t),!n.delaunay)return!1;const{centers:e,polygons:r}=n.delaunay,i=[];for(const t of r)if(t)for(let n=t.length,r=t[n-1],o=t[0],u=0;u<n;r=o,o=t[++u])o>r&&i.push([e[r],e[o]]);return{type:"MultiLineString",coordinates:i}},n._found=void 0,n.find=function(t,e,r){if(n._found=n.delaunay.find(t,e,n._found),!r||Ze([t,e],n.points[n._found])<r)return n._found},n.hull=function(t){void 0!==t&&n(t);const e=n.delaunay.hull,r=n.points;return 0===e.length?null:{type:"Polygon",coordinates:[[...e.map((t=>r[t])),r[e[0]]]]}},n(t)}(a).triangles(),M=new Map(a.map((function(t,n){var e=u(t,2),r=e[0],i=e[1];return["".concat(r,"-").concat(i),n]})));w.features.forEach((function(t){var n,e=t.geometry.coordinates[0].slice(0,3).reverse(),r=[];if(e.forEach((function(t){var n=u(t,2),e=n[0],i=n[1],o="".concat(e,"-").concat(i);M.has(o)&&r.push(M.get(o))})),3===r.length){if(r.some((function(t){return t<i.length})))if(!oo(t.properties.circumcenter,s,m))return;(n=x).push.apply(n,r)}}))}else if(o.length)for(var S=ut.from(a),E=function(t){var n,e=[2,1,0].map((function(n){return S.triangles[t+n]})),r=e.map((function(t){return a[t]}));if(e.some((function(t){return t<i.length}))){var o=[0,1].map((function(t){return function(t,n){let e=0,r=0;if(void 0===n)for(let n of t)null!=n&&(n=+n)>=n&&(++e,r+=n);else{let i=-1;for(let o of t)null!=(o=n(o,++i,t))&&(o=+o)>=o&&(++e,r+=o)}if(e)return r/e}(r,(function(n){return n[t]}))}));if(!oo(o,s,m))return 1}(n=x).push.apply(n,l(e))},N=0,k=S.triangles.length;N<k;N+=3)E(N);else{var $=V(r),A=$.vertices,P=$.holes;x=b(A,void 0===P?[]:P,2)}var T=ro(d(a,(function(t){return t[0]})),[0,1]),j=ro(d(a,(function(t){return t[1]})),[0,1]),F=a.map((function(t){var n=u(t,2),e=n[0],r=n[1];return[T(e),j(r)]}));return{contour:r,triangles:{points:a,indices:x,uvs:F}}}function oo(t,n){return arguments.length>2&&void 0!==arguments[2]&&arguments[2]?function(t,n){return(t&&Oe.hasOwnProperty(t.type)?Oe[t.type]:qe)(t,n)}(n,t):gt(t,n)}Xi=to({thousands:",",grouping:[3],currency:["$",""]}),Ji=Xi.format,Yi=Xi.formatPrefix;var uo=window.THREE?window.THREE:{BufferGeometry:t.BufferGeometry,Float32BufferAttribute:t.Float32BufferAttribute},lo=(new uo.BufferGeometry).setAttribute?"setAttribute":"addAttribute",ao=function(t){function n(t,r,i,o,l,a,s){var c;!function(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}(this,n),(c=e(this,n)).type="ConicPolygonGeometry",c.parameters={polygonGeoJson:t,bottomHeight:r,topHeight:i,closedBottom:o,closedTop:l,includeSides:a,curvatureResolution:s},r=r||0,i=i||1,o=void 0===o||o,l=void 0===l||l,a=void 0===a||a;var f=io(t,{resolution:s=s||5}),h=f.contour,p=f.triangles,d=_(p.uvs),g=[],y=[],v=[],m=0,x=function(t){var n=Math.round(g.length/3),e=v.length;g=g.concat(t.vertices),y=y.concat(t.uvs),v=v.concat(n?t.indices.map((function(t){return t+n})):t.indices),c.addGroup(e,v.length-e,m++)};function w(t,n){var e="function"==typeof n?n:function(){return n},r=t.map((function(t){return t.map((function(t){var n=u(t,2),r=n[0],i=n[1];return function(t,n){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=(90-t)*Math.PI/180,i=(90-n)*Math.PI/180;return[e*Math.sin(r)*Math.cos(i),e*Math.cos(r),e*Math.sin(r)*Math.sin(i)]}(i,r,e(r,i))}))}));return V(r)}function b(t){return{indices:!(arguments.length>1&&void 0!==arguments[1])||arguments[1]?p.indices:p.indices.slice().reverse(),vertices:w([p.points],t).vertices,uvs:d}}return a&&x(function(){for(var t=w(h,r),n=t.vertices,e=t.holes,o=w(h,i).vertices,u=_([o,n]),l=Math.round(o.length/3),a=new Set(e),s=0,c=[],f=0;f<l;f++){var p=f+1;if(p===l)p=s;else if(a.has(p)){var d=p;p=s,s=d}c.push(f,f+l,p+l),c.push(p+l,p,f)}for(var g=[],y=1;y>=0;y--)for(var v=0;v<l;v+=1)g.push(v/(l-1),y);return{indices:c,vertices:u,uvs:g}}()),o&&x(b(r,!1)),l&&x(b(i,!0)),c.setIndex(v),c[lo]("position",new uo.Float32BufferAttribute(g,3)),c[lo]("uv",new uo.Float32BufferAttribute(y,2)),c.computeVertexNormals(),c}return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),n&&o(t,n)}(n,t),r=n,Object.defineProperty(r,"prototype",{writable:!1}),r;var r}(uo.BufferGeometry);return ao}));
