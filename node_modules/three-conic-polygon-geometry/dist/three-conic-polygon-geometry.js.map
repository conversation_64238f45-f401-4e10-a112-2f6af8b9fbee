{"version": 3, "file": "three-conic-polygon-geometry.js", "sources": ["../node_modules/d3-array/src/ascending.js", "../node_modules/d3-array/src/descending.js", "../node_modules/d3-array/src/bisector.js", "../node_modules/d3-array/src/number.js", "../node_modules/d3-array/src/bisect.js", "../node_modules/d3-array/src/extent.js", "../node_modules/d3-array/src/fsum.js", "../node_modules/d3-array/src/ticks.js", "../node_modules/d3-array/src/mean.js", "../node_modules/d3-array/src/merge.js", "../node_modules/earcut/src/earcut.js", "../node_modules/robust-predicates/esm/util.js", "../node_modules/robust-predicates/esm/orient2d.js", "../node_modules/delaunator/index.js", "../node_modules/point-in-polygon-hao/dist/esm/index.js", "../node_modules/@turf/invariant/dist/esm/index.js", "../node_modules/@turf/boolean-point-in-polygon/dist/esm/index.js", "../node_modules/d3-geo/src/math.js", "../node_modules/d3-geo/src/noop.js", "../node_modules/d3-geo/src/stream.js", "../node_modules/d3-geo/src/area.js", "../node_modules/d3-geo/src/cartesian.js", "../node_modules/d3-geo/src/bounds.js", "../node_modules/d3-geo/src/centroid.js", "../node_modules/d3-geo/src/compose.js", "../node_modules/d3-geo/src/rotation.js", "../node_modules/d3-geo/src/circle.js", "../node_modules/d3-geo/src/clip/buffer.js", "../node_modules/d3-geo/src/pointEqual.js", "../node_modules/d3-geo/src/clip/rejoin.js", "../node_modules/d3-geo/src/polygonContains.js", "../node_modules/d3-geo/src/clip/index.js", "../node_modules/d3-geo/src/clip/antimeridian.js", "../node_modules/d3-geo/src/clip/circle.js", "../node_modules/d3-geo/src/clip/line.js", "../node_modules/d3-geo/src/clip/rectangle.js", "../node_modules/d3-geo/src/length.js", "../node_modules/d3-geo/src/distance.js", "../node_modules/d3-geo/src/contains.js", "../node_modules/d3-geo/src/interpolate.js", "../node_modules/d3-geo/src/identity.js", "../node_modules/d3-geo/src/path/bounds.js", "../node_modules/d3-geo/src/transform.js", "../node_modules/d3-geo/src/projection/fit.js", "../node_modules/d3-geo/src/projection/resample.js", "../node_modules/d3-geo/src/projection/index.js", "../node_modules/d3-geo/src/projection/azimuthal.js", "../node_modules/d3-geo/src/projection/stereographic.js", "../node_modules/d3-delaunay/src/path.js", "../node_modules/d3-delaunay/src/polygon.js", "../node_modules/d3-delaunay/src/voronoi.js", "../node_modules/d3-delaunay/src/delaunay.js", "../node_modules/d3-geo-voronoi/src/math.js", "../node_modules/d3-geo-voronoi/src/cartesian.js", "../node_modules/d3-geo-voronoi/src/delaunay.js", "../node_modules/d3-geo-voronoi/src/voronoi.js", "../node_modules/d3-scale/src/init.js", "../node_modules/d3-color/src/define.js", "../node_modules/d3-color/src/color.js", "../node_modules/d3-interpolate/src/constant.js", "../node_modules/d3-interpolate/src/color.js", "../node_modules/d3-interpolate/src/rgb.js", "../node_modules/d3-interpolate/src/numberArray.js", "../node_modules/d3-interpolate/src/array.js", "../node_modules/d3-interpolate/src/date.js", "../node_modules/d3-interpolate/src/number.js", "../node_modules/d3-interpolate/src/object.js", "../node_modules/d3-interpolate/src/string.js", "../node_modules/d3-interpolate/src/value.js", "../node_modules/d3-interpolate/src/round.js", "../node_modules/d3-scale/src/constant.js", "../node_modules/d3-scale/src/number.js", "../node_modules/d3-scale/src/continuous.js", "../node_modules/d3-format/src/formatDecimal.js", "../node_modules/d3-format/src/exponent.js", "../node_modules/d3-format/src/formatGroup.js", "../node_modules/d3-format/src/formatNumerals.js", "../node_modules/d3-format/src/formatSpecifier.js", "../node_modules/d3-format/src/formatTrim.js", "../node_modules/d3-format/src/formatPrefixAuto.js", "../node_modules/d3-format/src/formatRounded.js", "../node_modules/d3-format/src/formatTypes.js", "../node_modules/d3-format/src/identity.js", "../node_modules/d3-format/src/locale.js", "../node_modules/d3-format/src/defaultLocale.js", "../node_modules/d3-format/src/precisionFixed.js", "../node_modules/d3-format/src/precisionPrefix.js", "../node_modules/d3-format/src/precisionRound.js", "../node_modules/d3-scale/src/tickFormat.js", "../node_modules/d3-scale/src/linear.js", "../src/geoPolygonTriangulate.js", "../src/index.js"], "sourcesContent": ["export default function ascending(a, b) {\n  return a == null || b == null ? NaN : a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\n", "export default function descending(a, b) {\n  return a == null || b == null ? NaN\n    : b < a ? -1\n    : b > a ? 1\n    : b >= a ? 0\n    : NaN;\n}\n", "import ascending from \"./ascending.js\";\nimport descending from \"./descending.js\";\n\nexport default function bisector(f) {\n  let compare1, compare2, delta;\n\n  // If an accessor is specified, promote it to a comparator. In this case we\n  // can test whether the search value is (self-) comparable. We can’t do this\n  // for a comparator (except for specific, known comparators) because we can’t\n  // tell if the comparator is symmetric, and an asymmetric comparator can’t be\n  // used to test whether a single value is comparable.\n  if (f.length !== 2) {\n    compare1 = ascending;\n    compare2 = (d, x) => ascending(f(d), x);\n    delta = (d, x) => f(d) - x;\n  } else {\n    compare1 = f === ascending || f === descending ? f : zero;\n    compare2 = f;\n    delta = f;\n  }\n\n  function left(a, x, lo = 0, hi = a.length) {\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = (lo + hi) >>> 1;\n        if (compare2(a[mid], x) < 0) lo = mid + 1;\n        else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n\n  function right(a, x, lo = 0, hi = a.length) {\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = (lo + hi) >>> 1;\n        if (compare2(a[mid], x) <= 0) lo = mid + 1;\n        else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n\n  function center(a, x, lo = 0, hi = a.length) {\n    const i = left(a, x, lo, hi - 1);\n    return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;\n  }\n\n  return {left, center, right};\n}\n\nfunction zero() {\n  return 0;\n}\n", "export default function number(x) {\n  return x === null ? NaN : +x;\n}\n\nexport function* numbers(values, valueof) {\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  }\n}\n", "import ascending from \"./ascending.js\";\nimport bisector from \"./bisector.js\";\nimport number from \"./number.js\";\n\nconst ascendingBisect = bisector(ascending);\nexport const bisectRight = ascendingBisect.right;\nexport const bisectLeft = ascendingBisect.left;\nexport const bisectCenter = bisector(number).center;\nexport default bisectRight;\n", "export default function extent(values, valueof) {\n  let min;\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null) {\n        if (min === undefined) {\n          if (value >= value) min = max = value;\n        } else {\n          if (min > value) min = value;\n          if (max < value) max = value;\n        }\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null) {\n        if (min === undefined) {\n          if (value >= value) min = max = value;\n        } else {\n          if (min > value) min = value;\n          if (max < value) max = value;\n        }\n      }\n    }\n  }\n  return [min, max];\n}\n", "// https://github.com/python/cpython/blob/a74eea238f5baba15797e2e8b570d153bc8690a7/Modules/mathmodule.c#L1423\nexport class Adder {\n  constructor() {\n    this._partials = new Float64Array(32);\n    this._n = 0;\n  }\n  add(x) {\n    const p = this._partials;\n    let i = 0;\n    for (let j = 0; j < this._n && j < 32; j++) {\n      const y = p[j],\n        hi = x + y,\n        lo = Math.abs(x) < Math.abs(y) ? x - (hi - y) : y - (hi - x);\n      if (lo) p[i++] = lo;\n      x = hi;\n    }\n    p[i] = x;\n    this._n = i + 1;\n    return this;\n  }\n  valueOf() {\n    const p = this._partials;\n    let n = this._n, x, y, lo, hi = 0;\n    if (n > 0) {\n      hi = p[--n];\n      while (n > 0) {\n        x = hi;\n        y = p[--n];\n        hi = x + y;\n        lo = y - (hi - x);\n        if (lo) break;\n      }\n      if (n > 0 && ((lo < 0 && p[n - 1] < 0) || (lo > 0 && p[n - 1] > 0))) {\n        y = lo * 2;\n        x = hi + y;\n        if (y == x - hi) hi = x;\n      }\n    }\n    return hi;\n  }\n}\n\nexport function fsum(values, valueof) {\n  const adder = new Adder();\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        adder.add(value);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        adder.add(value);\n      }\n    }\n  }\n  return +adder;\n}\n\nexport function fcumsum(values, valueof) {\n  const adder = new Adder();\n  let index = -1;\n  return Float64Array.from(values, valueof === undefined\n      ? v => adder.add(+v || 0)\n      : v => adder.add(+valueof(v, ++index, values) || 0)\n  );\n}\n", "const e10 = Math.sqrt(50),\n    e5 = Math.sqrt(10),\n    e2 = Math.sqrt(2);\n\nfunction tickSpec(start, stop, count) {\n  const step = (stop - start) / Math.max(0, count),\n      power = Math.floor(Math.log10(step)),\n      error = step / Math.pow(10, power),\n      factor = error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1;\n  let i1, i2, inc;\n  if (power < 0) {\n    inc = Math.pow(10, -power) / factor;\n    i1 = Math.round(start * inc);\n    i2 = Math.round(stop * inc);\n    if (i1 / inc < start) ++i1;\n    if (i2 / inc > stop) --i2;\n    inc = -inc;\n  } else {\n    inc = Math.pow(10, power) * factor;\n    i1 = Math.round(start / inc);\n    i2 = Math.round(stop / inc);\n    if (i1 * inc < start) ++i1;\n    if (i2 * inc > stop) --i2;\n  }\n  if (i2 < i1 && 0.5 <= count && count < 2) return tickSpec(start, stop, count * 2);\n  return [i1, i2, inc];\n}\n\nexport default function ticks(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  if (!(count > 0)) return [];\n  if (start === stop) return [start];\n  const reverse = stop < start, [i1, i2, inc] = reverse ? tickSpec(stop, start, count) : tickSpec(start, stop, count);\n  if (!(i2 >= i1)) return [];\n  const n = i2 - i1 + 1, ticks = new Array(n);\n  if (reverse) {\n    if (inc < 0) for (let i = 0; i < n; ++i) ticks[i] = (i2 - i) / -inc;\n    else for (let i = 0; i < n; ++i) ticks[i] = (i2 - i) * inc;\n  } else {\n    if (inc < 0) for (let i = 0; i < n; ++i) ticks[i] = (i1 + i) / -inc;\n    else for (let i = 0; i < n; ++i) ticks[i] = (i1 + i) * inc;\n  }\n  return ticks;\n}\n\nexport function tickIncrement(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  return tickSpec(start, stop, count)[2];\n}\n\nexport function tickStep(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  const reverse = stop < start, inc = reverse ? tickIncrement(stop, start, count) : tickIncrement(start, stop, count);\n  return (reverse ? -1 : 1) * (inc < 0 ? 1 / -inc : inc);\n}\n", "export default function mean(values, valueof) {\n  let count = 0;\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        ++count, sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        ++count, sum += value;\n      }\n    }\n  }\n  if (count) return sum / count;\n}\n", "function* flatten(arrays) {\n  for (const array of arrays) {\n    yield* array;\n  }\n}\n\nexport default function merge(arrays) {\n  return Array.from(flatten(arrays));\n}\n", "\nexport default function earcut(data, holeIndices, dim = 2) {\n\n    const hasHoles = holeIndices && holeIndices.length;\n    const outerLen = hasHoles ? holeIndices[0] * dim : data.length;\n    let outerNode = linkedList(data, 0, outerLen, dim, true);\n    const triangles = [];\n\n    if (!outerNode || outerNode.next === outerNode.prev) return triangles;\n\n    let minX, minY, invSize;\n\n    if (hasHoles) outerNode = eliminateHoles(data, holeIndices, outerNode, dim);\n\n    // if the shape is not too simple, we'll use z-order curve hash later; calculate polygon bbox\n    if (data.length > 80 * dim) {\n        minX = Infinity;\n        minY = Infinity;\n        let maxX = -Infinity;\n        let maxY = -Infinity;\n\n        for (let i = dim; i < outerLen; i += dim) {\n            const x = data[i];\n            const y = data[i + 1];\n            if (x < minX) minX = x;\n            if (y < minY) minY = y;\n            if (x > maxX) maxX = x;\n            if (y > maxY) maxY = y;\n        }\n\n        // minX, minY and invSize are later used to transform coords into integers for z-order calculation\n        invSize = Math.max(maxX - minX, maxY - minY);\n        invSize = invSize !== 0 ? 32767 / invSize : 0;\n    }\n\n    earcutLinked(outerNode, triangles, dim, minX, minY, invSize, 0);\n\n    return triangles;\n}\n\n// create a circular doubly linked list from polygon points in the specified winding order\nfunction linkedList(data, start, end, dim, clockwise) {\n    let last;\n\n    if (clockwise === (signedArea(data, start, end, dim) > 0)) {\n        for (let i = start; i < end; i += dim) last = insertNode(i / dim | 0, data[i], data[i + 1], last);\n    } else {\n        for (let i = end - dim; i >= start; i -= dim) last = insertNode(i / dim | 0, data[i], data[i + 1], last);\n    }\n\n    if (last && equals(last, last.next)) {\n        removeNode(last);\n        last = last.next;\n    }\n\n    return last;\n}\n\n// eliminate colinear or duplicate points\nfunction filterPoints(start, end) {\n    if (!start) return start;\n    if (!end) end = start;\n\n    let p = start,\n        again;\n    do {\n        again = false;\n\n        if (!p.steiner && (equals(p, p.next) || area(p.prev, p, p.next) === 0)) {\n            removeNode(p);\n            p = end = p.prev;\n            if (p === p.next) break;\n            again = true;\n\n        } else {\n            p = p.next;\n        }\n    } while (again || p !== end);\n\n    return end;\n}\n\n// main ear slicing loop which triangulates a polygon (given as a linked list)\nfunction earcutLinked(ear, triangles, dim, minX, minY, invSize, pass) {\n    if (!ear) return;\n\n    // interlink polygon nodes in z-order\n    if (!pass && invSize) indexCurve(ear, minX, minY, invSize);\n\n    let stop = ear;\n\n    // iterate through ears, slicing them one by one\n    while (ear.prev !== ear.next) {\n        const prev = ear.prev;\n        const next = ear.next;\n\n        if (invSize ? isEarHashed(ear, minX, minY, invSize) : isEar(ear)) {\n            triangles.push(prev.i, ear.i, next.i); // cut off the triangle\n\n            removeNode(ear);\n\n            // skipping the next vertex leads to less sliver triangles\n            ear = next.next;\n            stop = next.next;\n\n            continue;\n        }\n\n        ear = next;\n\n        // if we looped through the whole remaining polygon and can't find any more ears\n        if (ear === stop) {\n            // try filtering points and slicing again\n            if (!pass) {\n                earcutLinked(filterPoints(ear), triangles, dim, minX, minY, invSize, 1);\n\n            // if this didn't work, try curing all small self-intersections locally\n            } else if (pass === 1) {\n                ear = cureLocalIntersections(filterPoints(ear), triangles);\n                earcutLinked(ear, triangles, dim, minX, minY, invSize, 2);\n\n            // as a last resort, try splitting the remaining polygon into two\n            } else if (pass === 2) {\n                splitEarcut(ear, triangles, dim, minX, minY, invSize);\n            }\n\n            break;\n        }\n    }\n}\n\n// check whether a polygon node forms a valid ear with adjacent nodes\nfunction isEar(ear) {\n    const a = ear.prev,\n        b = ear,\n        c = ear.next;\n\n    if (area(a, b, c) >= 0) return false; // reflex, can't be an ear\n\n    // now make sure we don't have other points inside the potential ear\n    const ax = a.x, bx = b.x, cx = c.x, ay = a.y, by = b.y, cy = c.y;\n\n    // triangle bbox\n    const x0 = Math.min(ax, bx, cx),\n        y0 = Math.min(ay, by, cy),\n        x1 = Math.max(ax, bx, cx),\n        y1 = Math.max(ay, by, cy);\n\n    let p = c.next;\n    while (p !== a) {\n        if (p.x >= x0 && p.x <= x1 && p.y >= y0 && p.y <= y1 &&\n            pointInTriangleExceptFirst(ax, ay, bx, by, cx, cy, p.x, p.y) &&\n            area(p.prev, p, p.next) >= 0) return false;\n        p = p.next;\n    }\n\n    return true;\n}\n\nfunction isEarHashed(ear, minX, minY, invSize) {\n    const a = ear.prev,\n        b = ear,\n        c = ear.next;\n\n    if (area(a, b, c) >= 0) return false; // reflex, can't be an ear\n\n    const ax = a.x, bx = b.x, cx = c.x, ay = a.y, by = b.y, cy = c.y;\n\n    // triangle bbox\n    const x0 = Math.min(ax, bx, cx),\n        y0 = Math.min(ay, by, cy),\n        x1 = Math.max(ax, bx, cx),\n        y1 = Math.max(ay, by, cy);\n\n    // z-order range for the current triangle bbox;\n    const minZ = zOrder(x0, y0, minX, minY, invSize),\n        maxZ = zOrder(x1, y1, minX, minY, invSize);\n\n    let p = ear.prevZ,\n        n = ear.nextZ;\n\n    // look for points inside the triangle in both directions\n    while (p && p.z >= minZ && n && n.z <= maxZ) {\n        if (p.x >= x0 && p.x <= x1 && p.y >= y0 && p.y <= y1 && p !== a && p !== c &&\n            pointInTriangleExceptFirst(ax, ay, bx, by, cx, cy, p.x, p.y) && area(p.prev, p, p.next) >= 0) return false;\n        p = p.prevZ;\n\n        if (n.x >= x0 && n.x <= x1 && n.y >= y0 && n.y <= y1 && n !== a && n !== c &&\n            pointInTriangleExceptFirst(ax, ay, bx, by, cx, cy, n.x, n.y) && area(n.prev, n, n.next) >= 0) return false;\n        n = n.nextZ;\n    }\n\n    // look for remaining points in decreasing z-order\n    while (p && p.z >= minZ) {\n        if (p.x >= x0 && p.x <= x1 && p.y >= y0 && p.y <= y1 && p !== a && p !== c &&\n            pointInTriangleExceptFirst(ax, ay, bx, by, cx, cy, p.x, p.y) && area(p.prev, p, p.next) >= 0) return false;\n        p = p.prevZ;\n    }\n\n    // look for remaining points in increasing z-order\n    while (n && n.z <= maxZ) {\n        if (n.x >= x0 && n.x <= x1 && n.y >= y0 && n.y <= y1 && n !== a && n !== c &&\n            pointInTriangleExceptFirst(ax, ay, bx, by, cx, cy, n.x, n.y) && area(n.prev, n, n.next) >= 0) return false;\n        n = n.nextZ;\n    }\n\n    return true;\n}\n\n// go through all polygon nodes and cure small local self-intersections\nfunction cureLocalIntersections(start, triangles) {\n    let p = start;\n    do {\n        const a = p.prev,\n            b = p.next.next;\n\n        if (!equals(a, b) && intersects(a, p, p.next, b) && locallyInside(a, b) && locallyInside(b, a)) {\n\n            triangles.push(a.i, p.i, b.i);\n\n            // remove two nodes involved\n            removeNode(p);\n            removeNode(p.next);\n\n            p = start = b;\n        }\n        p = p.next;\n    } while (p !== start);\n\n    return filterPoints(p);\n}\n\n// try splitting polygon into two and triangulate them independently\nfunction splitEarcut(start, triangles, dim, minX, minY, invSize) {\n    // look for a valid diagonal that divides the polygon into two\n    let a = start;\n    do {\n        let b = a.next.next;\n        while (b !== a.prev) {\n            if (a.i !== b.i && isValidDiagonal(a, b)) {\n                // split the polygon in two by the diagonal\n                let c = splitPolygon(a, b);\n\n                // filter colinear points around the cuts\n                a = filterPoints(a, a.next);\n                c = filterPoints(c, c.next);\n\n                // run earcut on each half\n                earcutLinked(a, triangles, dim, minX, minY, invSize, 0);\n                earcutLinked(c, triangles, dim, minX, minY, invSize, 0);\n                return;\n            }\n            b = b.next;\n        }\n        a = a.next;\n    } while (a !== start);\n}\n\n// link every hole into the outer loop, producing a single-ring polygon without holes\nfunction eliminateHoles(data, holeIndices, outerNode, dim) {\n    const queue = [];\n\n    for (let i = 0, len = holeIndices.length; i < len; i++) {\n        const start = holeIndices[i] * dim;\n        const end = i < len - 1 ? holeIndices[i + 1] * dim : data.length;\n        const list = linkedList(data, start, end, dim, false);\n        if (list === list.next) list.steiner = true;\n        queue.push(getLeftmost(list));\n    }\n\n    queue.sort(compareXYSlope);\n\n    // process holes from left to right\n    for (let i = 0; i < queue.length; i++) {\n        outerNode = eliminateHole(queue[i], outerNode);\n    }\n\n    return outerNode;\n}\n\nfunction compareXYSlope(a, b) {\n    let result = a.x - b.x;\n    // when the left-most point of 2 holes meet at a vertex, sort the holes counterclockwise so that when we find\n    // the bridge to the outer shell is always the point that they meet at.\n    if (result === 0) {\n        result = a.y - b.y;\n        if (result === 0) {\n            const aSlope = (a.next.y - a.y) / (a.next.x - a.x);\n            const bSlope = (b.next.y - b.y) / (b.next.x - b.x);\n            result = aSlope - bSlope;\n        }\n    }\n    return result;\n}\n\n// find a bridge between vertices that connects hole with an outer ring and and link it\nfunction eliminateHole(hole, outerNode) {\n    const bridge = findHoleBridge(hole, outerNode);\n    if (!bridge) {\n        return outerNode;\n    }\n\n    const bridgeReverse = splitPolygon(bridge, hole);\n\n    // filter collinear points around the cuts\n    filterPoints(bridgeReverse, bridgeReverse.next);\n    return filterPoints(bridge, bridge.next);\n}\n\n// David Eberly's algorithm for finding a bridge between hole and outer polygon\nfunction findHoleBridge(hole, outerNode) {\n    let p = outerNode;\n    const hx = hole.x;\n    const hy = hole.y;\n    let qx = -Infinity;\n    let m;\n\n    // find a segment intersected by a ray from the hole's leftmost point to the left;\n    // segment's endpoint with lesser x will be potential connection point\n    // unless they intersect at a vertex, then choose the vertex\n    if (equals(hole, p)) return p;\n    do {\n        if (equals(hole, p.next)) return p.next;\n        else if (hy <= p.y && hy >= p.next.y && p.next.y !== p.y) {\n            const x = p.x + (hy - p.y) * (p.next.x - p.x) / (p.next.y - p.y);\n            if (x <= hx && x > qx) {\n                qx = x;\n                m = p.x < p.next.x ? p : p.next;\n                if (x === hx) return m; // hole touches outer segment; pick leftmost endpoint\n            }\n        }\n        p = p.next;\n    } while (p !== outerNode);\n\n    if (!m) return null;\n\n    // look for points inside the triangle of hole point, segment intersection and endpoint;\n    // if there are no points found, we have a valid connection;\n    // otherwise choose the point of the minimum angle with the ray as connection point\n\n    const stop = m;\n    const mx = m.x;\n    const my = m.y;\n    let tanMin = Infinity;\n\n    p = m;\n\n    do {\n        if (hx >= p.x && p.x >= mx && hx !== p.x &&\n                pointInTriangle(hy < my ? hx : qx, hy, mx, my, hy < my ? qx : hx, hy, p.x, p.y)) {\n\n            const tan = Math.abs(hy - p.y) / (hx - p.x); // tangential\n\n            if (locallyInside(p, hole) &&\n                (tan < tanMin || (tan === tanMin && (p.x > m.x || (p.x === m.x && sectorContainsSector(m, p)))))) {\n                m = p;\n                tanMin = tan;\n            }\n        }\n\n        p = p.next;\n    } while (p !== stop);\n\n    return m;\n}\n\n// whether sector in vertex m contains sector in vertex p in the same coordinates\nfunction sectorContainsSector(m, p) {\n    return area(m.prev, m, p.prev) < 0 && area(p.next, m, m.next) < 0;\n}\n\n// interlink polygon nodes in z-order\nfunction indexCurve(start, minX, minY, invSize) {\n    let p = start;\n    do {\n        if (p.z === 0) p.z = zOrder(p.x, p.y, minX, minY, invSize);\n        p.prevZ = p.prev;\n        p.nextZ = p.next;\n        p = p.next;\n    } while (p !== start);\n\n    p.prevZ.nextZ = null;\n    p.prevZ = null;\n\n    sortLinked(p);\n}\n\n// Simon Tatham's linked list merge sort algorithm\n// http://www.chiark.greenend.org.uk/~sgtatham/algorithms/listsort.html\nfunction sortLinked(list) {\n    let numMerges;\n    let inSize = 1;\n\n    do {\n        let p = list;\n        let e;\n        list = null;\n        let tail = null;\n        numMerges = 0;\n\n        while (p) {\n            numMerges++;\n            let q = p;\n            let pSize = 0;\n            for (let i = 0; i < inSize; i++) {\n                pSize++;\n                q = q.nextZ;\n                if (!q) break;\n            }\n            let qSize = inSize;\n\n            while (pSize > 0 || (qSize > 0 && q)) {\n\n                if (pSize !== 0 && (qSize === 0 || !q || p.z <= q.z)) {\n                    e = p;\n                    p = p.nextZ;\n                    pSize--;\n                } else {\n                    e = q;\n                    q = q.nextZ;\n                    qSize--;\n                }\n\n                if (tail) tail.nextZ = e;\n                else list = e;\n\n                e.prevZ = tail;\n                tail = e;\n            }\n\n            p = q;\n        }\n\n        tail.nextZ = null;\n        inSize *= 2;\n\n    } while (numMerges > 1);\n\n    return list;\n}\n\n// z-order of a point given coords and inverse of the longer side of data bbox\nfunction zOrder(x, y, minX, minY, invSize) {\n    // coords are transformed into non-negative 15-bit integer range\n    x = (x - minX) * invSize | 0;\n    y = (y - minY) * invSize | 0;\n\n    x = (x | (x << 8)) & 0x00FF00FF;\n    x = (x | (x << 4)) & 0x0F0F0F0F;\n    x = (x | (x << 2)) & 0x33333333;\n    x = (x | (x << 1)) & 0x55555555;\n\n    y = (y | (y << 8)) & 0x00FF00FF;\n    y = (y | (y << 4)) & 0x0F0F0F0F;\n    y = (y | (y << 2)) & 0x33333333;\n    y = (y | (y << 1)) & 0x55555555;\n\n    return x | (y << 1);\n}\n\n// find the leftmost node of a polygon ring\nfunction getLeftmost(start) {\n    let p = start,\n        leftmost = start;\n    do {\n        if (p.x < leftmost.x || (p.x === leftmost.x && p.y < leftmost.y)) leftmost = p;\n        p = p.next;\n    } while (p !== start);\n\n    return leftmost;\n}\n\n// check if a point lies within a convex triangle\nfunction pointInTriangle(ax, ay, bx, by, cx, cy, px, py) {\n    return (cx - px) * (ay - py) >= (ax - px) * (cy - py) &&\n           (ax - px) * (by - py) >= (bx - px) * (ay - py) &&\n           (bx - px) * (cy - py) >= (cx - px) * (by - py);\n}\n\n// check if a point lies within a convex triangle but false if its equal to the first point of the triangle\nfunction pointInTriangleExceptFirst(ax, ay, bx, by, cx, cy, px, py) {\n    return !(ax === px && ay === py) && pointInTriangle(ax, ay, bx, by, cx, cy, px, py);\n}\n\n// check if a diagonal between two polygon nodes is valid (lies in polygon interior)\nfunction isValidDiagonal(a, b) {\n    return a.next.i !== b.i && a.prev.i !== b.i && !intersectsPolygon(a, b) && // dones't intersect other edges\n           (locallyInside(a, b) && locallyInside(b, a) && middleInside(a, b) && // locally visible\n            (area(a.prev, a, b.prev) || area(a, b.prev, b)) || // does not create opposite-facing sectors\n            equals(a, b) && area(a.prev, a, a.next) > 0 && area(b.prev, b, b.next) > 0); // special zero-length case\n}\n\n// signed area of a triangle\nfunction area(p, q, r) {\n    return (q.y - p.y) * (r.x - q.x) - (q.x - p.x) * (r.y - q.y);\n}\n\n// check if two points are equal\nfunction equals(p1, p2) {\n    return p1.x === p2.x && p1.y === p2.y;\n}\n\n// check if two segments intersect\nfunction intersects(p1, q1, p2, q2) {\n    const o1 = sign(area(p1, q1, p2));\n    const o2 = sign(area(p1, q1, q2));\n    const o3 = sign(area(p2, q2, p1));\n    const o4 = sign(area(p2, q2, q1));\n\n    if (o1 !== o2 && o3 !== o4) return true; // general case\n\n    if (o1 === 0 && onSegment(p1, p2, q1)) return true; // p1, q1 and p2 are collinear and p2 lies on p1q1\n    if (o2 === 0 && onSegment(p1, q2, q1)) return true; // p1, q1 and q2 are collinear and q2 lies on p1q1\n    if (o3 === 0 && onSegment(p2, p1, q2)) return true; // p2, q2 and p1 are collinear and p1 lies on p2q2\n    if (o4 === 0 && onSegment(p2, q1, q2)) return true; // p2, q2 and q1 are collinear and q1 lies on p2q2\n\n    return false;\n}\n\n// for collinear points p, q, r, check if point q lies on segment pr\nfunction onSegment(p, q, r) {\n    return q.x <= Math.max(p.x, r.x) && q.x >= Math.min(p.x, r.x) && q.y <= Math.max(p.y, r.y) && q.y >= Math.min(p.y, r.y);\n}\n\nfunction sign(num) {\n    return num > 0 ? 1 : num < 0 ? -1 : 0;\n}\n\n// check if a polygon diagonal intersects any polygon segments\nfunction intersectsPolygon(a, b) {\n    let p = a;\n    do {\n        if (p.i !== a.i && p.next.i !== a.i && p.i !== b.i && p.next.i !== b.i &&\n                intersects(p, p.next, a, b)) return true;\n        p = p.next;\n    } while (p !== a);\n\n    return false;\n}\n\n// check if a polygon diagonal is locally inside the polygon\nfunction locallyInside(a, b) {\n    return area(a.prev, a, a.next) < 0 ?\n        area(a, b, a.next) >= 0 && area(a, a.prev, b) >= 0 :\n        area(a, b, a.prev) < 0 || area(a, a.next, b) < 0;\n}\n\n// check if the middle point of a polygon diagonal is inside the polygon\nfunction middleInside(a, b) {\n    let p = a;\n    let inside = false;\n    const px = (a.x + b.x) / 2;\n    const py = (a.y + b.y) / 2;\n    do {\n        if (((p.y > py) !== (p.next.y > py)) && p.next.y !== p.y &&\n                (px < (p.next.x - p.x) * (py - p.y) / (p.next.y - p.y) + p.x))\n            inside = !inside;\n        p = p.next;\n    } while (p !== a);\n\n    return inside;\n}\n\n// link two polygon vertices with a bridge; if the vertices belong to the same ring, it splits polygon into two;\n// if one belongs to the outer ring and another to a hole, it merges it into a single ring\nfunction splitPolygon(a, b) {\n    const a2 = createNode(a.i, a.x, a.y),\n        b2 = createNode(b.i, b.x, b.y),\n        an = a.next,\n        bp = b.prev;\n\n    a.next = b;\n    b.prev = a;\n\n    a2.next = an;\n    an.prev = a2;\n\n    b2.next = a2;\n    a2.prev = b2;\n\n    bp.next = b2;\n    b2.prev = bp;\n\n    return b2;\n}\n\n// create a node and optionally link it with previous one (in a circular doubly linked list)\nfunction insertNode(i, x, y, last) {\n    const p = createNode(i, x, y);\n\n    if (!last) {\n        p.prev = p;\n        p.next = p;\n\n    } else {\n        p.next = last.next;\n        p.prev = last;\n        last.next.prev = p;\n        last.next = p;\n    }\n    return p;\n}\n\nfunction removeNode(p) {\n    p.next.prev = p.prev;\n    p.prev.next = p.next;\n\n    if (p.prevZ) p.prevZ.nextZ = p.nextZ;\n    if (p.nextZ) p.nextZ.prevZ = p.prevZ;\n}\n\nfunction createNode(i, x, y) {\n    return {\n        i, // vertex index in coordinates array\n        x, y, // vertex coordinates\n        prev: null, // previous and next vertex nodes in a polygon ring\n        next: null,\n        z: 0, // z-order curve value\n        prevZ: null, // previous and next nodes in z-order\n        nextZ: null,\n        steiner: false // indicates whether this is a steiner point\n    };\n}\n\n// return a percentage difference between the polygon area and its triangulation area;\n// used to verify correctness of triangulation\nexport function deviation(data, holeIndices, dim, triangles) {\n    const hasHoles = holeIndices && holeIndices.length;\n    const outerLen = hasHoles ? holeIndices[0] * dim : data.length;\n\n    let polygonArea = Math.abs(signedArea(data, 0, outerLen, dim));\n    if (hasHoles) {\n        for (let i = 0, len = holeIndices.length; i < len; i++) {\n            const start = holeIndices[i] * dim;\n            const end = i < len - 1 ? holeIndices[i + 1] * dim : data.length;\n            polygonArea -= Math.abs(signedArea(data, start, end, dim));\n        }\n    }\n\n    let trianglesArea = 0;\n    for (let i = 0; i < triangles.length; i += 3) {\n        const a = triangles[i] * dim;\n        const b = triangles[i + 1] * dim;\n        const c = triangles[i + 2] * dim;\n        trianglesArea += Math.abs(\n            (data[a] - data[c]) * (data[b + 1] - data[a + 1]) -\n            (data[a] - data[b]) * (data[c + 1] - data[a + 1]));\n    }\n\n    return polygonArea === 0 && trianglesArea === 0 ? 0 :\n        Math.abs((trianglesArea - polygonArea) / polygonArea);\n}\n\nfunction signedArea(data, start, end, dim) {\n    let sum = 0;\n    for (let i = start, j = end - dim; i < end; i += dim) {\n        sum += (data[j] - data[i]) * (data[i + 1] + data[j + 1]);\n        j = i;\n    }\n    return sum;\n}\n\n// turn a polygon in a multi-dimensional array form (e.g. as in GeoJSON) into a form Earcut accepts\nexport function flatten(data) {\n    const vertices = [];\n    const holes = [];\n    const dimensions = data[0][0].length;\n    let holeIndex = 0;\n    let prevLen = 0;\n\n    for (const ring of data) {\n        for (const p of ring) {\n            for (let d = 0; d < dimensions; d++) vertices.push(p[d]);\n        }\n        if (prevLen) {\n            holeIndex += prevLen;\n            holes.push(holeIndex);\n        }\n        prevLen = ring.length;\n    }\n    return {vertices, holes, dimensions};\n}\n", "export const epsilon = 1.1102230246251565e-16;\nexport const splitter = 134217729;\nexport const resulterrbound = (3 + 8 * epsilon) * epsilon;\n\n// fast_expansion_sum_zeroelim routine from oritinal code\nexport function sum(elen, e, flen, f, h) {\n    let Q, Qnew, hh, bvirt;\n    let enow = e[0];\n    let fnow = f[0];\n    let eindex = 0;\n    let findex = 0;\n    if ((fnow > enow) === (fnow > -enow)) {\n        Q = enow;\n        enow = e[++eindex];\n    } else {\n        Q = fnow;\n        fnow = f[++findex];\n    }\n    let hindex = 0;\n    if (eindex < elen && findex < flen) {\n        if ((fnow > enow) === (fnow > -enow)) {\n            Qnew = enow + Q;\n            hh = Q - (Qnew - enow);\n            enow = e[++eindex];\n        } else {\n            Qnew = fnow + Q;\n            hh = Q - (Qnew - fnow);\n            fnow = f[++findex];\n        }\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n        while (eindex < elen && findex < flen) {\n            if ((fnow > enow) === (fnow > -enow)) {\n                Qnew = Q + enow;\n                bvirt = Qnew - Q;\n                hh = Q - (Qnew - bvirt) + (enow - bvirt);\n                enow = e[++eindex];\n            } else {\n                Qnew = Q + fnow;\n                bvirt = Qnew - Q;\n                hh = Q - (Qnew - bvirt) + (fnow - bvirt);\n                fnow = f[++findex];\n            }\n            Q = Qnew;\n            if (hh !== 0) {\n                h[hindex++] = hh;\n            }\n        }\n    }\n    while (eindex < elen) {\n        Qnew = Q + enow;\n        bvirt = Qnew - Q;\n        hh = Q - (Qnew - bvirt) + (enow - bvirt);\n        enow = e[++eindex];\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    while (findex < flen) {\n        Qnew = Q + fnow;\n        bvirt = Qnew - Q;\n        hh = Q - (Qnew - bvirt) + (fnow - bvirt);\n        fnow = f[++findex];\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    if (Q !== 0 || hindex === 0) {\n        h[hindex++] = Q;\n    }\n    return hindex;\n}\n\nexport function sum_three(alen, a, blen, b, clen, c, tmp, out) {\n    return sum(sum(alen, a, blen, b, tmp), tmp, clen, c, out);\n}\n\n// scale_expansion_zeroelim routine from oritinal code\nexport function scale(elen, e, b, h) {\n    let Q, sum, hh, product1, product0;\n    let bvirt, c, ahi, alo, bhi, blo;\n\n    c = splitter * b;\n    bhi = c - (c - b);\n    blo = b - bhi;\n    let enow = e[0];\n    Q = enow * b;\n    c = splitter * enow;\n    ahi = c - (c - enow);\n    alo = enow - ahi;\n    hh = alo * blo - (Q - ahi * bhi - alo * bhi - ahi * blo);\n    let hindex = 0;\n    if (hh !== 0) {\n        h[hindex++] = hh;\n    }\n    for (let i = 1; i < elen; i++) {\n        enow = e[i];\n        product1 = enow * b;\n        c = splitter * enow;\n        ahi = c - (c - enow);\n        alo = enow - ahi;\n        product0 = alo * blo - (product1 - ahi * bhi - alo * bhi - ahi * blo);\n        sum = Q + product0;\n        bvirt = sum - Q;\n        hh = Q - (sum - bvirt) + (product0 - bvirt);\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n        Q = product1 + sum;\n        hh = sum - (Q - product1);\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    if (Q !== 0 || hindex === 0) {\n        h[hindex++] = Q;\n    }\n    return hindex;\n}\n\nexport function negate(elen, e) {\n    for (let i = 0; i < elen; i++) e[i] = -e[i];\n    return elen;\n}\n\nexport function estimate(elen, e) {\n    let Q = e[0];\n    for (let i = 1; i < elen; i++) Q += e[i];\n    return Q;\n}\n\nexport function vec(n) {\n    return new Float64Array(n);\n}\n", "import {epsilon, splitter, resulterrbound, estimate, vec, sum} from './util.js';\n\nconst ccwerrboundA = (3 + 16 * epsilon) * epsilon;\nconst ccwerrboundB = (2 + 12 * epsilon) * epsilon;\nconst ccwerrboundC = (9 + 64 * epsilon) * epsilon * epsilon;\n\nconst B = vec(4);\nconst C1 = vec(8);\nconst C2 = vec(12);\nconst D = vec(16);\nconst u = vec(4);\n\nfunction orient2dadapt(ax, ay, bx, by, cx, cy, detsum) {\n    let acxtail, acytail, bcxtail, bcytail;\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n\n    const acx = ax - cx;\n    const bcx = bx - cx;\n    const acy = ay - cy;\n    const bcy = by - cy;\n\n    s1 = acx * bcy;\n    c = splitter * acx;\n    ahi = c - (c - acx);\n    alo = acx - ahi;\n    c = splitter * bcy;\n    bhi = c - (c - bcy);\n    blo = bcy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acy * bcx;\n    c = splitter * acy;\n    ahi = c - (c - acy);\n    alo = acy - ahi;\n    c = splitter * bcx;\n    bhi = c - (c - bcx);\n    blo = bcx - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    B[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    B[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    B[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    B[3] = u3;\n\n    let det = estimate(4, B);\n    let errbound = ccwerrboundB * detsum;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - acx;\n    acxtail = ax - (acx + bvirt) + (bvirt - cx);\n    bvirt = bx - bcx;\n    bcxtail = bx - (bcx + bvirt) + (bvirt - cx);\n    bvirt = ay - acy;\n    acytail = ay - (acy + bvirt) + (bvirt - cy);\n    bvirt = by - bcy;\n    bcytail = by - (bcy + bvirt) + (bvirt - cy);\n\n    if (acxtail === 0 && acytail === 0 && bcxtail === 0 && bcytail === 0) {\n        return det;\n    }\n\n    errbound = ccwerrboundC * detsum + resulterrbound * Math.abs(det);\n    det += (acx * bcytail + bcy * acxtail) - (acy * bcxtail + bcx * acytail);\n    if (det >= errbound || -det >= errbound) return det;\n\n    s1 = acxtail * bcy;\n    c = splitter * acxtail;\n    ahi = c - (c - acxtail);\n    alo = acxtail - ahi;\n    c = splitter * bcy;\n    bhi = c - (c - bcy);\n    blo = bcy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acytail * bcx;\n    c = splitter * acytail;\n    ahi = c - (c - acytail);\n    alo = acytail - ahi;\n    c = splitter * bcx;\n    bhi = c - (c - bcx);\n    blo = bcx - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const C1len = sum(4, B, 4, u, C1);\n\n    s1 = acx * bcytail;\n    c = splitter * acx;\n    ahi = c - (c - acx);\n    alo = acx - ahi;\n    c = splitter * bcytail;\n    bhi = c - (c - bcytail);\n    blo = bcytail - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acy * bcxtail;\n    c = splitter * acy;\n    ahi = c - (c - acy);\n    alo = acy - ahi;\n    c = splitter * bcxtail;\n    bhi = c - (c - bcxtail);\n    blo = bcxtail - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const C2len = sum(C1len, C1, 4, u, C2);\n\n    s1 = acxtail * bcytail;\n    c = splitter * acxtail;\n    ahi = c - (c - acxtail);\n    alo = acxtail - ahi;\n    c = splitter * bcytail;\n    bhi = c - (c - bcytail);\n    blo = bcytail - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acytail * bcxtail;\n    c = splitter * acytail;\n    ahi = c - (c - acytail);\n    alo = acytail - ahi;\n    c = splitter * bcxtail;\n    bhi = c - (c - bcxtail);\n    blo = bcxtail - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const Dlen = sum(C2len, C2, 4, u, D);\n\n    return D[Dlen - 1];\n}\n\nexport function orient2d(ax, ay, bx, by, cx, cy) {\n    const detleft = (ay - cy) * (bx - cx);\n    const detright = (ax - cx) * (by - cy);\n    const det = detleft - detright;\n\n    const detsum = Math.abs(detleft + detright);\n    if (Math.abs(det) >= ccwerrboundA * detsum) return det;\n\n    return -orient2dadapt(ax, ay, bx, by, cx, cy, detsum);\n}\n\nexport function orient2dfast(ax, ay, bx, by, cx, cy) {\n    return (ay - cy) * (bx - cx) - (ax - cx) * (by - cy);\n}\n", "\nconst EPSILON = Math.pow(2, -52);\nconst EDGE_STACK = new Uint32Array(512);\n\nimport {orient2d} from 'robust-predicates';\n\nexport default class Delaunator {\n\n    static from(points, getX = defaultGetX, getY = defaultGetY) {\n        const n = points.length;\n        const coords = new Float64Array(n * 2);\n\n        for (let i = 0; i < n; i++) {\n            const p = points[i];\n            coords[2 * i] = getX(p);\n            coords[2 * i + 1] = getY(p);\n        }\n\n        return new Delaunator(coords);\n    }\n\n    constructor(coords) {\n        const n = coords.length >> 1;\n        if (n > 0 && typeof coords[0] !== 'number') throw new Error('Expected coords to contain numbers.');\n\n        this.coords = coords;\n\n        // arrays that will store the triangulation graph\n        const maxTriangles = Math.max(2 * n - 5, 0);\n        this._triangles = new Uint32Array(maxTriangles * 3);\n        this._halfedges = new Int32Array(maxTriangles * 3);\n\n        // temporary arrays for tracking the edges of the advancing convex hull\n        this._hashSize = Math.ceil(Math.sqrt(n));\n        this._hullPrev = new Uint32Array(n); // edge to prev edge\n        this._hullNext = new Uint32Array(n); // edge to next edge\n        this._hullTri = new Uint32Array(n); // edge to adjacent triangle\n        this._hullHash = new Int32Array(this._hashSize); // angular edge hash\n\n        // temporary arrays for sorting points\n        this._ids = new Uint32Array(n);\n        this._dists = new Float64Array(n);\n\n        this.update();\n    }\n\n    update() {\n        const {coords, _hullPrev: hullPrev, _hullNext: hullNext, _hullTri: hullTri, _hullHash: hullHash} =  this;\n        const n = coords.length >> 1;\n\n        // populate an array of point indices; calculate input data bbox\n        let minX = Infinity;\n        let minY = Infinity;\n        let maxX = -Infinity;\n        let maxY = -Infinity;\n\n        for (let i = 0; i < n; i++) {\n            const x = coords[2 * i];\n            const y = coords[2 * i + 1];\n            if (x < minX) minX = x;\n            if (y < minY) minY = y;\n            if (x > maxX) maxX = x;\n            if (y > maxY) maxY = y;\n            this._ids[i] = i;\n        }\n        const cx = (minX + maxX) / 2;\n        const cy = (minY + maxY) / 2;\n\n        let i0, i1, i2;\n\n        // pick a seed point close to the center\n        for (let i = 0, minDist = Infinity; i < n; i++) {\n            const d = dist(cx, cy, coords[2 * i], coords[2 * i + 1]);\n            if (d < minDist) {\n                i0 = i;\n                minDist = d;\n            }\n        }\n        const i0x = coords[2 * i0];\n        const i0y = coords[2 * i0 + 1];\n\n        // find the point closest to the seed\n        for (let i = 0, minDist = Infinity; i < n; i++) {\n            if (i === i0) continue;\n            const d = dist(i0x, i0y, coords[2 * i], coords[2 * i + 1]);\n            if (d < minDist && d > 0) {\n                i1 = i;\n                minDist = d;\n            }\n        }\n        let i1x = coords[2 * i1];\n        let i1y = coords[2 * i1 + 1];\n\n        let minRadius = Infinity;\n\n        // find the third point which forms the smallest circumcircle with the first two\n        for (let i = 0; i < n; i++) {\n            if (i === i0 || i === i1) continue;\n            const r = circumradius(i0x, i0y, i1x, i1y, coords[2 * i], coords[2 * i + 1]);\n            if (r < minRadius) {\n                i2 = i;\n                minRadius = r;\n            }\n        }\n        let i2x = coords[2 * i2];\n        let i2y = coords[2 * i2 + 1];\n\n        if (minRadius === Infinity) {\n            // order collinear points by dx (or dy if all x are identical)\n            // and return the list as a hull\n            for (let i = 0; i < n; i++) {\n                this._dists[i] = (coords[2 * i] - coords[0]) || (coords[2 * i + 1] - coords[1]);\n            }\n            quicksort(this._ids, this._dists, 0, n - 1);\n            const hull = new Uint32Array(n);\n            let j = 0;\n            for (let i = 0, d0 = -Infinity; i < n; i++) {\n                const id = this._ids[i];\n                const d = this._dists[id];\n                if (d > d0) {\n                    hull[j++] = id;\n                    d0 = d;\n                }\n            }\n            this.hull = hull.subarray(0, j);\n            this.triangles = new Uint32Array(0);\n            this.halfedges = new Uint32Array(0);\n            return;\n        }\n\n        // swap the order of the seed points for counter-clockwise orientation\n        if (orient2d(i0x, i0y, i1x, i1y, i2x, i2y) < 0) {\n            const i = i1;\n            const x = i1x;\n            const y = i1y;\n            i1 = i2;\n            i1x = i2x;\n            i1y = i2y;\n            i2 = i;\n            i2x = x;\n            i2y = y;\n        }\n\n        const center = circumcenter(i0x, i0y, i1x, i1y, i2x, i2y);\n        this._cx = center.x;\n        this._cy = center.y;\n\n        for (let i = 0; i < n; i++) {\n            this._dists[i] = dist(coords[2 * i], coords[2 * i + 1], center.x, center.y);\n        }\n\n        // sort the points by distance from the seed triangle circumcenter\n        quicksort(this._ids, this._dists, 0, n - 1);\n\n        // set up the seed triangle as the starting hull\n        this._hullStart = i0;\n        let hullSize = 3;\n\n        hullNext[i0] = hullPrev[i2] = i1;\n        hullNext[i1] = hullPrev[i0] = i2;\n        hullNext[i2] = hullPrev[i1] = i0;\n\n        hullTri[i0] = 0;\n        hullTri[i1] = 1;\n        hullTri[i2] = 2;\n\n        hullHash.fill(-1);\n        hullHash[this._hashKey(i0x, i0y)] = i0;\n        hullHash[this._hashKey(i1x, i1y)] = i1;\n        hullHash[this._hashKey(i2x, i2y)] = i2;\n\n        this.trianglesLen = 0;\n        this._addTriangle(i0, i1, i2, -1, -1, -1);\n\n        for (let k = 0, xp, yp; k < this._ids.length; k++) {\n            const i = this._ids[k];\n            const x = coords[2 * i];\n            const y = coords[2 * i + 1];\n\n            // skip near-duplicate points\n            if (k > 0 && Math.abs(x - xp) <= EPSILON && Math.abs(y - yp) <= EPSILON) continue;\n            xp = x;\n            yp = y;\n\n            // skip seed triangle points\n            if (i === i0 || i === i1 || i === i2) continue;\n\n            // find a visible edge on the convex hull using edge hash\n            let start = 0;\n            for (let j = 0, key = this._hashKey(x, y); j < this._hashSize; j++) {\n                start = hullHash[(key + j) % this._hashSize];\n                if (start !== -1 && start !== hullNext[start]) break;\n            }\n\n            start = hullPrev[start];\n            let e = start, q;\n            while (q = hullNext[e], orient2d(x, y, coords[2 * e], coords[2 * e + 1], coords[2 * q], coords[2 * q + 1]) >= 0) {\n                e = q;\n                if (e === start) {\n                    e = -1;\n                    break;\n                }\n            }\n            if (e === -1) continue; // likely a near-duplicate point; skip it\n\n            // add the first triangle from the point\n            let t = this._addTriangle(e, i, hullNext[e], -1, -1, hullTri[e]);\n\n            // recursively flip triangles from the point until they satisfy the Delaunay condition\n            hullTri[i] = this._legalize(t + 2);\n            hullTri[e] = t; // keep track of boundary triangles on the hull\n            hullSize++;\n\n            // walk forward through the hull, adding more triangles and flipping recursively\n            let n = hullNext[e];\n            while (q = hullNext[n], orient2d(x, y, coords[2 * n], coords[2 * n + 1], coords[2 * q], coords[2 * q + 1]) < 0) {\n                t = this._addTriangle(n, i, q, hullTri[i], -1, hullTri[n]);\n                hullTri[i] = this._legalize(t + 2);\n                hullNext[n] = n; // mark as removed\n                hullSize--;\n                n = q;\n            }\n\n            // walk backward from the other side, adding more triangles and flipping\n            if (e === start) {\n                while (q = hullPrev[e], orient2d(x, y, coords[2 * q], coords[2 * q + 1], coords[2 * e], coords[2 * e + 1]) < 0) {\n                    t = this._addTriangle(q, i, e, -1, hullTri[e], hullTri[q]);\n                    this._legalize(t + 2);\n                    hullTri[q] = t;\n                    hullNext[e] = e; // mark as removed\n                    hullSize--;\n                    e = q;\n                }\n            }\n\n            // update the hull indices\n            this._hullStart = hullPrev[i] = e;\n            hullNext[e] = hullPrev[n] = i;\n            hullNext[i] = n;\n\n            // save the two new edges in the hash table\n            hullHash[this._hashKey(x, y)] = i;\n            hullHash[this._hashKey(coords[2 * e], coords[2 * e + 1])] = e;\n        }\n\n        this.hull = new Uint32Array(hullSize);\n        for (let i = 0, e = this._hullStart; i < hullSize; i++) {\n            this.hull[i] = e;\n            e = hullNext[e];\n        }\n\n        // trim typed triangle mesh arrays\n        this.triangles = this._triangles.subarray(0, this.trianglesLen);\n        this.halfedges = this._halfedges.subarray(0, this.trianglesLen);\n    }\n\n    _hashKey(x, y) {\n        return Math.floor(pseudoAngle(x - this._cx, y - this._cy) * this._hashSize) % this._hashSize;\n    }\n\n    _legalize(a) {\n        const {_triangles: triangles, _halfedges: halfedges, coords} = this;\n\n        let i = 0;\n        let ar = 0;\n\n        // recursion eliminated with a fixed-size stack\n        while (true) {\n            const b = halfedges[a];\n\n            /* if the pair of triangles doesn't satisfy the Delaunay condition\n             * (p1 is inside the circumcircle of [p0, pl, pr]), flip them,\n             * then do the same check/flip recursively for the new pair of triangles\n             *\n             *           pl                    pl\n             *          /||\\                  /  \\\n             *       al/ || \\bl            al/    \\a\n             *        /  ||  \\              /      \\\n             *       /  a||b  \\    flip    /___ar___\\\n             *     p0\\   ||   /p1   =>   p0\\---bl---/p1\n             *        \\  ||  /              \\      /\n             *       ar\\ || /br             b\\    /br\n             *          \\||/                  \\  /\n             *           pr                    pr\n             */\n            const a0 = a - a % 3;\n            ar = a0 + (a + 2) % 3;\n\n            if (b === -1) { // convex hull edge\n                if (i === 0) break;\n                a = EDGE_STACK[--i];\n                continue;\n            }\n\n            const b0 = b - b % 3;\n            const al = a0 + (a + 1) % 3;\n            const bl = b0 + (b + 2) % 3;\n\n            const p0 = triangles[ar];\n            const pr = triangles[a];\n            const pl = triangles[al];\n            const p1 = triangles[bl];\n\n            const illegal = inCircle(\n                coords[2 * p0], coords[2 * p0 + 1],\n                coords[2 * pr], coords[2 * pr + 1],\n                coords[2 * pl], coords[2 * pl + 1],\n                coords[2 * p1], coords[2 * p1 + 1]);\n\n            if (illegal) {\n                triangles[a] = p1;\n                triangles[b] = p0;\n\n                const hbl = halfedges[bl];\n\n                // edge swapped on the other side of the hull (rare); fix the halfedge reference\n                if (hbl === -1) {\n                    let e = this._hullStart;\n                    do {\n                        if (this._hullTri[e] === bl) {\n                            this._hullTri[e] = a;\n                            break;\n                        }\n                        e = this._hullPrev[e];\n                    } while (e !== this._hullStart);\n                }\n                this._link(a, hbl);\n                this._link(b, halfedges[ar]);\n                this._link(ar, bl);\n\n                const br = b0 + (b + 1) % 3;\n\n                // don't worry about hitting the cap: it can only happen on extremely degenerate input\n                if (i < EDGE_STACK.length) {\n                    EDGE_STACK[i++] = br;\n                }\n            } else {\n                if (i === 0) break;\n                a = EDGE_STACK[--i];\n            }\n        }\n\n        return ar;\n    }\n\n    _link(a, b) {\n        this._halfedges[a] = b;\n        if (b !== -1) this._halfedges[b] = a;\n    }\n\n    // add a new triangle given vertex indices and adjacent half-edge ids\n    _addTriangle(i0, i1, i2, a, b, c) {\n        const t = this.trianglesLen;\n\n        this._triangles[t] = i0;\n        this._triangles[t + 1] = i1;\n        this._triangles[t + 2] = i2;\n\n        this._link(t, a);\n        this._link(t + 1, b);\n        this._link(t + 2, c);\n\n        this.trianglesLen += 3;\n\n        return t;\n    }\n}\n\n// monotonically increases with real angle, but doesn't need expensive trigonometry\nfunction pseudoAngle(dx, dy) {\n    const p = dx / (Math.abs(dx) + Math.abs(dy));\n    return (dy > 0 ? 3 - p : 1 + p) / 4; // [0..1]\n}\n\nfunction dist(ax, ay, bx, by) {\n    const dx = ax - bx;\n    const dy = ay - by;\n    return dx * dx + dy * dy;\n}\n\nfunction inCircle(ax, ay, bx, by, cx, cy, px, py) {\n    const dx = ax - px;\n    const dy = ay - py;\n    const ex = bx - px;\n    const ey = by - py;\n    const fx = cx - px;\n    const fy = cy - py;\n\n    const ap = dx * dx + dy * dy;\n    const bp = ex * ex + ey * ey;\n    const cp = fx * fx + fy * fy;\n\n    return dx * (ey * cp - bp * fy) -\n           dy * (ex * cp - bp * fx) +\n           ap * (ex * fy - ey * fx) < 0;\n}\n\nfunction circumradius(ax, ay, bx, by, cx, cy) {\n    const dx = bx - ax;\n    const dy = by - ay;\n    const ex = cx - ax;\n    const ey = cy - ay;\n\n    const bl = dx * dx + dy * dy;\n    const cl = ex * ex + ey * ey;\n    const d = 0.5 / (dx * ey - dy * ex);\n\n    const x = (ey * bl - dy * cl) * d;\n    const y = (dx * cl - ex * bl) * d;\n\n    return x * x + y * y;\n}\n\nfunction circumcenter(ax, ay, bx, by, cx, cy) {\n    const dx = bx - ax;\n    const dy = by - ay;\n    const ex = cx - ax;\n    const ey = cy - ay;\n\n    const bl = dx * dx + dy * dy;\n    const cl = ex * ex + ey * ey;\n    const d = 0.5 / (dx * ey - dy * ex);\n\n    const x = ax + (ey * bl - dy * cl) * d;\n    const y = ay + (dx * cl - ex * bl) * d;\n\n    return {x, y};\n}\n\nfunction quicksort(ids, dists, left, right) {\n    if (right - left <= 20) {\n        for (let i = left + 1; i <= right; i++) {\n            const temp = ids[i];\n            const tempDist = dists[temp];\n            let j = i - 1;\n            while (j >= left && dists[ids[j]] > tempDist) ids[j + 1] = ids[j--];\n            ids[j + 1] = temp;\n        }\n    } else {\n        const median = (left + right) >> 1;\n        let i = left + 1;\n        let j = right;\n        swap(ids, median, i);\n        if (dists[ids[left]] > dists[ids[right]]) swap(ids, left, right);\n        if (dists[ids[i]] > dists[ids[right]]) swap(ids, i, right);\n        if (dists[ids[left]] > dists[ids[i]]) swap(ids, left, i);\n\n        const temp = ids[i];\n        const tempDist = dists[temp];\n        while (true) {\n            do i++; while (dists[ids[i]] < tempDist);\n            do j--; while (dists[ids[j]] > tempDist);\n            if (j < i) break;\n            swap(ids, i, j);\n        }\n        ids[left + 1] = ids[j];\n        ids[j] = temp;\n\n        if (right - i + 1 >= j - left) {\n            quicksort(ids, dists, i, right);\n            quicksort(ids, dists, left, j - 1);\n        } else {\n            quicksort(ids, dists, left, j - 1);\n            quicksort(ids, dists, i, right);\n        }\n    }\n}\n\nfunction swap(arr, i, j) {\n    const tmp = arr[i];\n    arr[i] = arr[j];\n    arr[j] = tmp;\n}\n\nfunction defaultGetX(p) {\n    return p[0];\n}\nfunction defaultGetY(p) {\n    return p[1];\n}\n", "import { orient2d } from 'robust-predicates';\n\nfunction pointInPolygon(p, polygon) {\n    var i;\n    var ii;\n    var k = 0;\n    var f;\n    var u1;\n    var v1;\n    var u2;\n    var v2;\n    var currentP;\n    var nextP;\n\n    var x = p[0];\n    var y = p[1];\n\n    var numContours = polygon.length;\n    for (i = 0; i < numContours; i++) {\n        ii = 0;\n        var contour = polygon[i];\n        var contourLen = contour.length - 1;\n\n        currentP = contour[0];\n        if (currentP[0] !== contour[contourLen][0] &&\n            currentP[1] !== contour[contourLen][1]) {\n            throw new Error('First and last coordinates in a ring must be the same')\n        }\n\n        u1 = currentP[0] - x;\n        v1 = currentP[1] - y;\n\n        for (ii; ii < contourLen; ii++) {\n            nextP = contour[ii + 1];\n\n            u2 = nextP[0] - x;\n            v2 = nextP[1] - y;\n\n            if (v1 === 0 && v2 === 0) {\n                if ((u2 <= 0 && u1 >= 0) || (u1 <= 0 && u2 >= 0)) { return 0 }\n            } else if ((v2 >= 0 && v1 <= 0) || (v2 <= 0 && v1 >= 0)) {\n                f = orient2d(u1, u2, v1, v2, 0, 0);\n                if (f === 0) { return 0 }\n                if ((f > 0 && v2 > 0 && v1 <= 0) || (f < 0 && v2 <= 0 && v1 > 0)) { k++; }\n            }\n            currentP = nextP;\n            v1 = v2;\n            u1 = u2;\n        }\n    }\n\n    if (k % 2 === 0) { return false }\n    return true\n}\n\nexport { pointInPolygon as default };\n", "// index.ts\nimport { isNumber } from \"@turf/helpers\";\nfunction getCoord(coord) {\n  if (!coord) {\n    throw new Error(\"coord is required\");\n  }\n  if (!Array.isArray(coord)) {\n    if (coord.type === \"Feature\" && coord.geometry !== null && coord.geometry.type === \"Point\") {\n      return [...coord.geometry.coordinates];\n    }\n    if (coord.type === \"Point\") {\n      return [...coord.coordinates];\n    }\n  }\n  if (Array.isArray(coord) && coord.length >= 2 && !Array.isArray(coord[0]) && !Array.isArray(coord[1])) {\n    return [...coord];\n  }\n  throw new Error(\"coord must be GeoJSON Point or an Array of numbers\");\n}\nfunction getCoords(coords) {\n  if (Array.isArray(coords)) {\n    return coords;\n  }\n  if (coords.type === \"Feature\") {\n    if (coords.geometry !== null) {\n      return coords.geometry.coordinates;\n    }\n  } else {\n    if (coords.coordinates) {\n      return coords.coordinates;\n    }\n  }\n  throw new Error(\n    \"coords must be GeoJSON Feature, Geometry Object or an Array\"\n  );\n}\nfunction containsNumber(coordinates) {\n  if (coordinates.length > 1 && isNumber(coordinates[0]) && isNumber(coordinates[1])) {\n    return true;\n  }\n  if (Array.isArray(coordinates[0]) && coordinates[0].length) {\n    return containsNumber(coordinates[0]);\n  }\n  throw new Error(\"coordinates must only contain numbers\");\n}\nfunction geojsonType(value, type, name) {\n  if (!type || !name) {\n    throw new Error(\"type and name required\");\n  }\n  if (!value || value.type !== type) {\n    throw new Error(\n      \"Invalid input to \" + name + \": must be a \" + type + \", given \" + value.type\n    );\n  }\n}\nfunction featureOf(feature, type, name) {\n  if (!feature) {\n    throw new Error(\"No feature passed\");\n  }\n  if (!name) {\n    throw new Error(\".featureOf() requires a name\");\n  }\n  if (!feature || feature.type !== \"Feature\" || !feature.geometry) {\n    throw new Error(\n      \"Invalid input to \" + name + \", Feature with geometry required\"\n    );\n  }\n  if (!feature.geometry || feature.geometry.type !== type) {\n    throw new Error(\n      \"Invalid input to \" + name + \": must be a \" + type + \", given \" + feature.geometry.type\n    );\n  }\n}\nfunction collectionOf(featureCollection, type, name) {\n  if (!featureCollection) {\n    throw new Error(\"No featureCollection passed\");\n  }\n  if (!name) {\n    throw new Error(\".collectionOf() requires a name\");\n  }\n  if (!featureCollection || featureCollection.type !== \"FeatureCollection\") {\n    throw new Error(\n      \"Invalid input to \" + name + \", FeatureCollection required\"\n    );\n  }\n  for (const feature of featureCollection.features) {\n    if (!feature || feature.type !== \"Feature\" || !feature.geometry) {\n      throw new Error(\n        \"Invalid input to \" + name + \", Feature with geometry required\"\n      );\n    }\n    if (!feature.geometry || feature.geometry.type !== type) {\n      throw new Error(\n        \"Invalid input to \" + name + \": must be a \" + type + \", given \" + feature.geometry.type\n      );\n    }\n  }\n}\nfunction getGeom(geojson) {\n  if (geojson.type === \"Feature\") {\n    return geojson.geometry;\n  }\n  return geojson;\n}\nfunction getType(geojson, _name) {\n  if (geojson.type === \"FeatureCollection\") {\n    return \"FeatureCollection\";\n  }\n  if (geojson.type === \"GeometryCollection\") {\n    return \"GeometryCollection\";\n  }\n  if (geojson.type === \"Feature\" && geojson.geometry !== null) {\n    return geojson.geometry.type;\n  }\n  return geojson.type;\n}\nexport {\n  collectionOf,\n  containsNumber,\n  featureOf,\n  geojsonType,\n  getCoord,\n  getCoords,\n  getGeom,\n  getType\n};\n//# sourceMappingURL=index.js.map", "// index.ts\nimport pip from \"point-in-polygon-hao\";\nimport { getCoord, getGeom } from \"@turf/invariant\";\nfunction booleanPointInPolygon(point, polygon, options = {}) {\n  if (!point) {\n    throw new Error(\"point is required\");\n  }\n  if (!polygon) {\n    throw new Error(\"polygon is required\");\n  }\n  const pt = getCoord(point);\n  const geom = getGeom(polygon);\n  const type = geom.type;\n  const bbox = polygon.bbox;\n  let polys = geom.coordinates;\n  if (bbox && inBBox(pt, bbox) === false) {\n    return false;\n  }\n  if (type === \"Polygon\") {\n    polys = [polys];\n  }\n  let result = false;\n  for (var i = 0; i < polys.length; ++i) {\n    const polyResult = pip(pt, polys[i]);\n    if (polyResult === 0) return options.ignoreBoundary ? false : true;\n    else if (polyResult) result = true;\n  }\n  return result;\n}\nfunction inBBox(pt, bbox) {\n  return bbox[0] <= pt[0] && bbox[1] <= pt[1] && bbox[2] >= pt[0] && bbox[3] >= pt[1];\n}\nvar turf_boolean_point_in_polygon_default = booleanPointInPolygon;\nexport {\n  booleanPointInPolygon,\n  turf_boolean_point_in_polygon_default as default\n};\n//# sourceMappingURL=index.js.map", "export var epsilon = 1e-6;\nexport var epsilon2 = 1e-12;\nexport var pi = Math.PI;\nexport var halfPi = pi / 2;\nexport var quarterPi = pi / 4;\nexport var tau = pi * 2;\n\nexport var degrees = 180 / pi;\nexport var radians = pi / 180;\n\nexport var abs = Math.abs;\nexport var atan = Math.atan;\nexport var atan2 = Math.atan2;\nexport var cos = Math.cos;\nexport var ceil = Math.ceil;\nexport var exp = Math.exp;\nexport var floor = Math.floor;\nexport var hypot = Math.hypot;\nexport var log = Math.log;\nexport var pow = Math.pow;\nexport var sin = Math.sin;\nexport var sign = Math.sign || function(x) { return x > 0 ? 1 : x < 0 ? -1 : 0; };\nexport var sqrt = Math.sqrt;\nexport var tan = Math.tan;\n\nexport function acos(x) {\n  return x > 1 ? 0 : x < -1 ? pi : Math.acos(x);\n}\n\nexport function asin(x) {\n  return x > 1 ? halfPi : x < -1 ? -halfPi : Math.asin(x);\n}\n\nexport function haversin(x) {\n  return (x = sin(x / 2)) * x;\n}\n", "export default function noop() {}\n", "function streamGeometry(geometry, stream) {\n  if (geometry && streamGeometryType.hasOwnProperty(geometry.type)) {\n    streamGeometryType[geometry.type](geometry, stream);\n  }\n}\n\nvar streamObjectType = {\n  Feature: function(object, stream) {\n    streamGeometry(object.geometry, stream);\n  },\n  FeatureCollection: function(object, stream) {\n    var features = object.features, i = -1, n = features.length;\n    while (++i < n) streamGeometry(features[i].geometry, stream);\n  }\n};\n\nvar streamGeometryType = {\n  Sphere: function(object, stream) {\n    stream.sphere();\n  },\n  Point: function(object, stream) {\n    object = object.coordinates;\n    stream.point(object[0], object[1], object[2]);\n  },\n  MultiPoint: function(object, stream) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) object = coordinates[i], stream.point(object[0], object[1], object[2]);\n  },\n  LineString: function(object, stream) {\n    streamLine(object.coordinates, stream, 0);\n  },\n  MultiLineString: function(object, stream) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) streamLine(coordinates[i], stream, 0);\n  },\n  Polygon: function(object, stream) {\n    streamPolygon(object.coordinates, stream);\n  },\n  MultiPolygon: function(object, stream) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) streamPolygon(coordinates[i], stream);\n  },\n  GeometryCollection: function(object, stream) {\n    var geometries = object.geometries, i = -1, n = geometries.length;\n    while (++i < n) streamGeometry(geometries[i], stream);\n  }\n};\n\nfunction streamLine(coordinates, stream, closed) {\n  var i = -1, n = coordinates.length - closed, coordinate;\n  stream.lineStart();\n  while (++i < n) coordinate = coordinates[i], stream.point(coordinate[0], coordinate[1], coordinate[2]);\n  stream.lineEnd();\n}\n\nfunction streamPolygon(coordinates, stream) {\n  var i = -1, n = coordinates.length;\n  stream.polygonStart();\n  while (++i < n) streamLine(coordinates[i], stream, 1);\n  stream.polygonEnd();\n}\n\nexport default function(object, stream) {\n  if (object && streamObjectType.hasOwnProperty(object.type)) {\n    streamObjectType[object.type](object, stream);\n  } else {\n    streamGeometry(object, stream);\n  }\n}\n", "import {Adder} from \"d3-array\";\nimport {atan2, cos, quarterPi, radians, sin, tau} from \"./math.js\";\nimport noop from \"./noop.js\";\nimport stream from \"./stream.js\";\n\nexport var areaRingSum = new Adder();\n\n// hello?\n\nvar areaSum = new Adder(),\n    lambda00,\n    phi00,\n    lambda0,\n    cosPhi0,\n    sinPhi0;\n\nexport var areaStream = {\n  point: noop,\n  lineStart: noop,\n  lineEnd: noop,\n  polygonStart: function() {\n    areaRingSum = new Adder();\n    areaStream.lineStart = areaRingStart;\n    areaStream.lineEnd = areaRingEnd;\n  },\n  polygonEnd: function() {\n    var areaRing = +areaRingSum;\n    areaSum.add(areaRing < 0 ? tau + areaRing : areaRing);\n    this.lineStart = this.lineEnd = this.point = noop;\n  },\n  sphere: function() {\n    areaSum.add(tau);\n  }\n};\n\nfunction areaRingStart() {\n  areaStream.point = areaPointFirst;\n}\n\nfunction areaRingEnd() {\n  areaPoint(lambda00, phi00);\n}\n\nfunction areaPointFirst(lambda, phi) {\n  areaStream.point = areaPoint;\n  lambda00 = lambda, phi00 = phi;\n  lambda *= radians, phi *= radians;\n  lambda0 = lambda, cosPhi0 = cos(phi = phi / 2 + quarterPi), sinPhi0 = sin(phi);\n}\n\nfunction areaPoint(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  phi = phi / 2 + quarterPi; // half the angular distance from south pole\n\n  // Spherical excess E for a spherical triangle with vertices: south pole,\n  // previous point, current point.  Uses a formula derived from Cagnoli’s\n  // theorem.  See Todhunter, Spherical Trig. (1871), Sec. 103, Eq. (2).\n  var dLambda = lambda - lambda0,\n      sdLambda = dLambda >= 0 ? 1 : -1,\n      adLambda = sdLambda * dLambda,\n      cosPhi = cos(phi),\n      sinPhi = sin(phi),\n      k = sinPhi0 * sinPhi,\n      u = cosPhi0 * cosPhi + k * cos(adLambda),\n      v = k * sdLambda * sin(adLambda);\n  areaRingSum.add(atan2(v, u));\n\n  // Advance the previous points.\n  lambda0 = lambda, cosPhi0 = cosPhi, sinPhi0 = sinPhi;\n}\n\nexport default function(object) {\n  areaSum = new Adder();\n  stream(object, areaStream);\n  return areaSum * 2;\n}\n", "import {asin, atan2, cos, sin, sqrt} from \"./math.js\";\n\nexport function spherical(cartesian) {\n  return [atan2(cartesian[1], cartesian[0]), asin(cartesian[2])];\n}\n\nexport function cartesian(spherical) {\n  var lambda = spherical[0], phi = spherical[1], cosPhi = cos(phi);\n  return [cosPhi * cos(lambda), cosPhi * sin(lambda), sin(phi)];\n}\n\nexport function cartesianDot(a, b) {\n  return a[0] * b[0] + a[1] * b[1] + a[2] * b[2];\n}\n\nexport function cartesianCross(a, b) {\n  return [a[1] * b[2] - a[2] * b[1], a[2] * b[0] - a[0] * b[2], a[0] * b[1] - a[1] * b[0]];\n}\n\n// TODO return a\nexport function cartesianAddInPlace(a, b) {\n  a[0] += b[0], a[1] += b[1], a[2] += b[2];\n}\n\nexport function cartesianScale(vector, k) {\n  return [vector[0] * k, vector[1] * k, vector[2] * k];\n}\n\n// TODO return d\nexport function cartesianNormalizeInPlace(d) {\n  var l = sqrt(d[0] * d[0] + d[1] * d[1] + d[2] * d[2]);\n  d[0] /= l, d[1] /= l, d[2] /= l;\n}\n", "import {Adder} from \"d3-array\";\nimport {areaStream, areaRingSum} from \"./area.js\";\nimport {cartesian, cartesianCross, cartesianNormalizeInPlace, spherical} from \"./cartesian.js\";\nimport {abs, degrees, epsilon, radians} from \"./math.js\";\nimport stream from \"./stream.js\";\n\nvar lambda0, phi0, lambda1, phi1, // bounds\n    lambda2, // previous lambda-coordinate\n    lambda00, phi00, // first point\n    p0, // previous 3D point\n    deltaSum,\n    ranges,\n    range;\n\nvar boundsStream = {\n  point: boundsPoint,\n  lineStart: boundsLineStart,\n  lineEnd: boundsLineEnd,\n  polygonStart: function() {\n    boundsStream.point = boundsRingPoint;\n    boundsStream.lineStart = boundsRingStart;\n    boundsStream.lineEnd = boundsRingEnd;\n    deltaSum = new Adder();\n    areaStream.polygonStart();\n  },\n  polygonEnd: function() {\n    areaStream.polygonEnd();\n    boundsStream.point = boundsPoint;\n    boundsStream.lineStart = boundsLineStart;\n    boundsStream.lineEnd = boundsLineEnd;\n    if (areaRingSum < 0) lambda0 = -(lambda1 = 180), phi0 = -(phi1 = 90);\n    else if (deltaSum > epsilon) phi1 = 90;\n    else if (deltaSum < -epsilon) phi0 = -90;\n    range[0] = lambda0, range[1] = lambda1;\n  },\n  sphere: function() {\n    lambda0 = -(lambda1 = 180), phi0 = -(phi1 = 90);\n  }\n};\n\nfunction boundsPoint(lambda, phi) {\n  ranges.push(range = [lambda0 = lambda, lambda1 = lambda]);\n  if (phi < phi0) phi0 = phi;\n  if (phi > phi1) phi1 = phi;\n}\n\nfunction linePoint(lambda, phi) {\n  var p = cartesian([lambda * radians, phi * radians]);\n  if (p0) {\n    var normal = cartesianCross(p0, p),\n        equatorial = [normal[1], -normal[0], 0],\n        inflection = cartesianCross(equatorial, normal);\n    cartesianNormalizeInPlace(inflection);\n    inflection = spherical(inflection);\n    var delta = lambda - lambda2,\n        sign = delta > 0 ? 1 : -1,\n        lambdai = inflection[0] * degrees * sign,\n        phii,\n        antimeridian = abs(delta) > 180;\n    if (antimeridian ^ (sign * lambda2 < lambdai && lambdai < sign * lambda)) {\n      phii = inflection[1] * degrees;\n      if (phii > phi1) phi1 = phii;\n    } else if (lambdai = (lambdai + 360) % 360 - 180, antimeridian ^ (sign * lambda2 < lambdai && lambdai < sign * lambda)) {\n      phii = -inflection[1] * degrees;\n      if (phii < phi0) phi0 = phii;\n    } else {\n      if (phi < phi0) phi0 = phi;\n      if (phi > phi1) phi1 = phi;\n    }\n    if (antimeridian) {\n      if (lambda < lambda2) {\n        if (angle(lambda0, lambda) > angle(lambda0, lambda1)) lambda1 = lambda;\n      } else {\n        if (angle(lambda, lambda1) > angle(lambda0, lambda1)) lambda0 = lambda;\n      }\n    } else {\n      if (lambda1 >= lambda0) {\n        if (lambda < lambda0) lambda0 = lambda;\n        if (lambda > lambda1) lambda1 = lambda;\n      } else {\n        if (lambda > lambda2) {\n          if (angle(lambda0, lambda) > angle(lambda0, lambda1)) lambda1 = lambda;\n        } else {\n          if (angle(lambda, lambda1) > angle(lambda0, lambda1)) lambda0 = lambda;\n        }\n      }\n    }\n  } else {\n    ranges.push(range = [lambda0 = lambda, lambda1 = lambda]);\n  }\n  if (phi < phi0) phi0 = phi;\n  if (phi > phi1) phi1 = phi;\n  p0 = p, lambda2 = lambda;\n}\n\nfunction boundsLineStart() {\n  boundsStream.point = linePoint;\n}\n\nfunction boundsLineEnd() {\n  range[0] = lambda0, range[1] = lambda1;\n  boundsStream.point = boundsPoint;\n  p0 = null;\n}\n\nfunction boundsRingPoint(lambda, phi) {\n  if (p0) {\n    var delta = lambda - lambda2;\n    deltaSum.add(abs(delta) > 180 ? delta + (delta > 0 ? 360 : -360) : delta);\n  } else {\n    lambda00 = lambda, phi00 = phi;\n  }\n  areaStream.point(lambda, phi);\n  linePoint(lambda, phi);\n}\n\nfunction boundsRingStart() {\n  areaStream.lineStart();\n}\n\nfunction boundsRingEnd() {\n  boundsRingPoint(lambda00, phi00);\n  areaStream.lineEnd();\n  if (abs(deltaSum) > epsilon) lambda0 = -(lambda1 = 180);\n  range[0] = lambda0, range[1] = lambda1;\n  p0 = null;\n}\n\n// Finds the left-right distance between two longitudes.\n// This is almost the same as (lambda1 - lambda0 + 360°) % 360°, except that we want\n// the distance between ±180° to be 360°.\nfunction angle(lambda0, lambda1) {\n  return (lambda1 -= lambda0) < 0 ? lambda1 + 360 : lambda1;\n}\n\nfunction rangeCompare(a, b) {\n  return a[0] - b[0];\n}\n\nfunction rangeContains(range, x) {\n  return range[0] <= range[1] ? range[0] <= x && x <= range[1] : x < range[0] || range[1] < x;\n}\n\nexport default function(feature) {\n  var i, n, a, b, merged, deltaMax, delta;\n\n  phi1 = lambda1 = -(lambda0 = phi0 = Infinity);\n  ranges = [];\n  stream(feature, boundsStream);\n\n  // First, sort ranges by their minimum longitudes.\n  if (n = ranges.length) {\n    ranges.sort(rangeCompare);\n\n    // Then, merge any ranges that overlap.\n    for (i = 1, a = ranges[0], merged = [a]; i < n; ++i) {\n      b = ranges[i];\n      if (rangeContains(a, b[0]) || rangeContains(a, b[1])) {\n        if (angle(a[0], b[1]) > angle(a[0], a[1])) a[1] = b[1];\n        if (angle(b[0], a[1]) > angle(a[0], a[1])) a[0] = b[0];\n      } else {\n        merged.push(a = b);\n      }\n    }\n\n    // Finally, find the largest gap between the merged ranges.\n    // The final bounding box will be the inverse of this gap.\n    for (deltaMax = -Infinity, n = merged.length - 1, i = 0, a = merged[n]; i <= n; a = b, ++i) {\n      b = merged[i];\n      if ((delta = angle(a[1], b[0])) > deltaMax) deltaMax = delta, lambda0 = b[0], lambda1 = a[1];\n    }\n  }\n\n  ranges = range = null;\n\n  return lambda0 === Infinity || phi0 === Infinity\n      ? [[NaN, NaN], [NaN, NaN]]\n      : [[lambda0, phi0], [lambda1, phi1]];\n}\n", "import {Adder} from \"d3-array\";\nimport {asin, atan2, cos, degrees, epsilon, epsilon2, hypot, radians, sin, sqrt} from \"./math.js\";\nimport noop from \"./noop.js\";\nimport stream from \"./stream.js\";\n\nvar W0, W1,\n    X0, Y0, Z0,\n    X1, Y1, Z1,\n    X2, Y2, Z2,\n    lambda00, phi00, // first point\n    x0, y0, z0; // previous point\n\nvar centroidStream = {\n  sphere: noop,\n  point: centroidPoint,\n  lineStart: centroidLineStart,\n  lineEnd: centroidLineEnd,\n  polygonStart: function() {\n    centroidStream.lineStart = centroidRingStart;\n    centroidStream.lineEnd = centroidRingEnd;\n  },\n  polygonEnd: function() {\n    centroidStream.lineStart = centroidLineStart;\n    centroidStream.lineEnd = centroidLineEnd;\n  }\n};\n\n// Arithmetic mean of Cartesian vectors.\nfunction centroidPoint(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  var cosPhi = cos(phi);\n  centroidPointCartesian(cosPhi * cos(lambda), cosPhi * sin(lambda), sin(phi));\n}\n\nfunction centroidPointCartesian(x, y, z) {\n  ++W0;\n  X0 += (x - X0) / W0;\n  Y0 += (y - Y0) / W0;\n  Z0 += (z - Z0) / W0;\n}\n\nfunction centroidLineStart() {\n  centroidStream.point = centroidLinePointFirst;\n}\n\nfunction centroidLinePointFirst(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  var cosPhi = cos(phi);\n  x0 = cosPhi * cos(lambda);\n  y0 = cosPhi * sin(lambda);\n  z0 = sin(phi);\n  centroidStream.point = centroidLinePoint;\n  centroidPointCartesian(x0, y0, z0);\n}\n\nfunction centroidLinePoint(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  var cosPhi = cos(phi),\n      x = cosPhi * cos(lambda),\n      y = cosPhi * sin(lambda),\n      z = sin(phi),\n      w = atan2(sqrt((w = y0 * z - z0 * y) * w + (w = z0 * x - x0 * z) * w + (w = x0 * y - y0 * x) * w), x0 * x + y0 * y + z0 * z);\n  W1 += w;\n  X1 += w * (x0 + (x0 = x));\n  Y1 += w * (y0 + (y0 = y));\n  Z1 += w * (z0 + (z0 = z));\n  centroidPointCartesian(x0, y0, z0);\n}\n\nfunction centroidLineEnd() {\n  centroidStream.point = centroidPoint;\n}\n\n// See J. E. Brock, The Inertia Tensor for a Spherical Triangle,\n// J. Applied Mechanics 42, 239 (1975).\nfunction centroidRingStart() {\n  centroidStream.point = centroidRingPointFirst;\n}\n\nfunction centroidRingEnd() {\n  centroidRingPoint(lambda00, phi00);\n  centroidStream.point = centroidPoint;\n}\n\nfunction centroidRingPointFirst(lambda, phi) {\n  lambda00 = lambda, phi00 = phi;\n  lambda *= radians, phi *= radians;\n  centroidStream.point = centroidRingPoint;\n  var cosPhi = cos(phi);\n  x0 = cosPhi * cos(lambda);\n  y0 = cosPhi * sin(lambda);\n  z0 = sin(phi);\n  centroidPointCartesian(x0, y0, z0);\n}\n\nfunction centroidRingPoint(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  var cosPhi = cos(phi),\n      x = cosPhi * cos(lambda),\n      y = cosPhi * sin(lambda),\n      z = sin(phi),\n      cx = y0 * z - z0 * y,\n      cy = z0 * x - x0 * z,\n      cz = x0 * y - y0 * x,\n      m = hypot(cx, cy, cz),\n      w = asin(m), // line weight = angle\n      v = m && -w / m; // area weight multiplier\n  X2.add(v * cx);\n  Y2.add(v * cy);\n  Z2.add(v * cz);\n  W1 += w;\n  X1 += w * (x0 + (x0 = x));\n  Y1 += w * (y0 + (y0 = y));\n  Z1 += w * (z0 + (z0 = z));\n  centroidPointCartesian(x0, y0, z0);\n}\n\nexport default function(object) {\n  W0 = W1 =\n  X0 = Y0 = Z0 =\n  X1 = Y1 = Z1 = 0;\n  X2 = new Adder();\n  Y2 = new Adder();\n  Z2 = new Adder();\n  stream(object, centroidStream);\n\n  var x = +X2,\n      y = +Y2,\n      z = +Z2,\n      m = hypot(x, y, z);\n\n  // If the area-weighted ccentroid is undefined, fall back to length-weighted ccentroid.\n  if (m < epsilon2) {\n    x = X1, y = Y1, z = Z1;\n    // If the feature has zero length, fall back to arithmetic mean of point vectors.\n    if (W1 < epsilon) x = X0, y = Y0, z = Z0;\n    m = hypot(x, y, z);\n    // If the feature still has an undefined ccentroid, then return.\n    if (m < epsilon2) return [NaN, NaN];\n  }\n\n  return [atan2(y, x) * degrees, asin(z / m) * degrees];\n}\n", "export default function(a, b) {\n\n  function compose(x, y) {\n    return x = a(x, y), b(x[0], x[1]);\n  }\n\n  if (a.invert && b.invert) compose.invert = function(x, y) {\n    return x = b.invert(x, y), x && a.invert(x[0], x[1]);\n  };\n\n  return compose;\n}\n", "import compose from \"./compose.js\";\nimport {abs, asin, atan2, cos, degrees, pi, radians, sin, tau} from \"./math.js\";\n\nfunction rotationIdentity(lambda, phi) {\n  if (abs(lambda) > pi) lambda -= Math.round(lambda / tau) * tau;\n  return [lambda, phi];\n}\n\nrotationIdentity.invert = rotationIdentity;\n\nexport function rotateRadians(deltaLambda, deltaPhi, deltaGamma) {\n  return (deltaLambda %= tau) ? (deltaPhi || deltaGamma ? compose(rotationLambda(deltaLambda), rotationPhiGamma(deltaPhi, deltaGamma))\n    : rotationLambda(deltaLambda))\n    : (deltaPhi || deltaGamma ? rotationPhiGamma(deltaPhi, deltaGamma)\n    : rotationIdentity);\n}\n\nfunction forwardRotationLambda(deltaLambda) {\n  return function(lambda, phi) {\n    lambda += deltaLambda;\n    if (abs(lambda) > pi) lambda -= Math.round(lambda / tau) * tau;\n    return [lambda, phi];\n  };\n}\n\nfunction rotationLambda(deltaLambda) {\n  var rotation = forwardRotationLambda(deltaLambda);\n  rotation.invert = forwardRotationLambda(-deltaLambda);\n  return rotation;\n}\n\nfunction rotationPhiGamma(deltaPhi, deltaGamma) {\n  var cosDeltaPhi = cos(deltaPhi),\n      sinDeltaPhi = sin(deltaPhi),\n      cosDeltaGamma = cos(deltaGamma),\n      sinDeltaGamma = sin(deltaGamma);\n\n  function rotation(lambda, phi) {\n    var cosPhi = cos(phi),\n        x = cos(lambda) * cosPhi,\n        y = sin(lambda) * cosPhi,\n        z = sin(phi),\n        k = z * cosDeltaPhi + x * sinDeltaPhi;\n    return [\n      atan2(y * cosDeltaGamma - k * sinDeltaGamma, x * cosDeltaPhi - z * sinDeltaPhi),\n      asin(k * cosDeltaGamma + y * sinDeltaGamma)\n    ];\n  }\n\n  rotation.invert = function(lambda, phi) {\n    var cosPhi = cos(phi),\n        x = cos(lambda) * cosPhi,\n        y = sin(lambda) * cosPhi,\n        z = sin(phi),\n        k = z * cosDeltaGamma - y * sinDeltaGamma;\n    return [\n      atan2(y * cosDeltaGamma + z * sinDeltaGamma, x * cosDeltaPhi + k * sinDeltaPhi),\n      asin(k * cosDeltaPhi - x * sinDeltaPhi)\n    ];\n  };\n\n  return rotation;\n}\n\nexport default function(rotate) {\n  rotate = rotateRadians(rotate[0] * radians, rotate[1] * radians, rotate.length > 2 ? rotate[2] * radians : 0);\n\n  function forward(coordinates) {\n    coordinates = rotate(coordinates[0] * radians, coordinates[1] * radians);\n    return coordinates[0] *= degrees, coordinates[1] *= degrees, coordinates;\n  }\n\n  forward.invert = function(coordinates) {\n    coordinates = rotate.invert(coordinates[0] * radians, coordinates[1] * radians);\n    return coordinates[0] *= degrees, coordinates[1] *= degrees, coordinates;\n  };\n\n  return forward;\n}\n", "import {cartesian, cartesianNormalizeInPlace, spherical} from \"./cartesian.js\";\nimport constant from \"./constant.js\";\nimport {acos, cos, degrees, epsilon, radians, sin, tau} from \"./math.js\";\nimport {rotateRadians} from \"./rotation.js\";\n\n// Generates a circle centered at [0°, 0°], with a given radius and precision.\nexport function circleStream(stream, radius, delta, direction, t0, t1) {\n  if (!delta) return;\n  var cosRadius = cos(radius),\n      sinRadius = sin(radius),\n      step = direction * delta;\n  if (t0 == null) {\n    t0 = radius + direction * tau;\n    t1 = radius - step / 2;\n  } else {\n    t0 = circleRadius(cosRadius, t0);\n    t1 = circleRadius(cosRadius, t1);\n    if (direction > 0 ? t0 < t1 : t0 > t1) t0 += direction * tau;\n  }\n  for (var point, t = t0; direction > 0 ? t > t1 : t < t1; t -= step) {\n    point = spherical([cosRadius, -sinRadius * cos(t), -sinRadius * sin(t)]);\n    stream.point(point[0], point[1]);\n  }\n}\n\n// Returns the signed angle of a cartesian point relative to [cosRadius, 0, 0].\nfunction circleRadius(cosRadius, point) {\n  point = cartesian(point), point[0] -= cosRadius;\n  cartesianNormalizeInPlace(point);\n  var radius = acos(-point[1]);\n  return ((-point[2] < 0 ? -radius : radius) + tau - epsilon) % tau;\n}\n\nexport default function() {\n  var center = constant([0, 0]),\n      radius = constant(90),\n      precision = constant(2),\n      ring,\n      rotate,\n      stream = {point: point};\n\n  function point(x, y) {\n    ring.push(x = rotate(x, y));\n    x[0] *= degrees, x[1] *= degrees;\n  }\n\n  function circle() {\n    var c = center.apply(this, arguments),\n        r = radius.apply(this, arguments) * radians,\n        p = precision.apply(this, arguments) * radians;\n    ring = [];\n    rotate = rotateRadians(-c[0] * radians, -c[1] * radians, 0).invert;\n    circleStream(stream, r, p, 1);\n    c = {type: \"Polygon\", coordinates: [ring]};\n    ring = rotate = null;\n    return c;\n  }\n\n  circle.center = function(_) {\n    return arguments.length ? (center = typeof _ === \"function\" ? _ : constant([+_[0], +_[1]]), circle) : center;\n  };\n\n  circle.radius = function(_) {\n    return arguments.length ? (radius = typeof _ === \"function\" ? _ : constant(+_), circle) : radius;\n  };\n\n  circle.precision = function(_) {\n    return arguments.length ? (precision = typeof _ === \"function\" ? _ : constant(+_), circle) : precision;\n  };\n\n  return circle;\n}\n", "import noop from \"../noop.js\";\n\nexport default function() {\n  var lines = [],\n      line;\n  return {\n    point: function(x, y, m) {\n      line.push([x, y, m]);\n    },\n    lineStart: function() {\n      lines.push(line = []);\n    },\n    lineEnd: noop,\n    rejoin: function() {\n      if (lines.length > 1) lines.push(lines.pop().concat(lines.shift()));\n    },\n    result: function() {\n      var result = lines;\n      lines = [];\n      line = null;\n      return result;\n    }\n  };\n}\n", "import {abs, epsilon} from \"./math.js\";\n\nexport default function(a, b) {\n  return abs(a[0] - b[0]) < epsilon && abs(a[1] - b[1]) < epsilon;\n}\n", "import pointEqual from \"../pointEqual.js\";\nimport {epsilon} from \"../math.js\";\n\nfunction Intersection(point, points, other, entry) {\n  this.x = point;\n  this.z = points;\n  this.o = other; // another intersection\n  this.e = entry; // is an entry?\n  this.v = false; // visited\n  this.n = this.p = null; // next & previous\n}\n\n// A generalized polygon clipping algorithm: given a polygon that has been cut\n// into its visible line segments, and rejoins the segments by interpolating\n// along the clip edge.\nexport default function(segments, compareIntersection, startInside, interpolate, stream) {\n  var subject = [],\n      clip = [],\n      i,\n      n;\n\n  segments.forEach(function(segment) {\n    if ((n = segment.length - 1) <= 0) return;\n    var n, p0 = segment[0], p1 = segment[n], x;\n\n    if (pointEqual(p0, p1)) {\n      if (!p0[2] && !p1[2]) {\n        stream.lineStart();\n        for (i = 0; i < n; ++i) stream.point((p0 = segment[i])[0], p0[1]);\n        stream.lineEnd();\n        return;\n      }\n      // handle degenerate cases by moving the point\n      p1[0] += 2 * epsilon;\n    }\n\n    subject.push(x = new Intersection(p0, segment, null, true));\n    clip.push(x.o = new Intersection(p0, null, x, false));\n    subject.push(x = new Intersection(p1, segment, null, false));\n    clip.push(x.o = new Intersection(p1, null, x, true));\n  });\n\n  if (!subject.length) return;\n\n  clip.sort(compareIntersection);\n  link(subject);\n  link(clip);\n\n  for (i = 0, n = clip.length; i < n; ++i) {\n    clip[i].e = startInside = !startInside;\n  }\n\n  var start = subject[0],\n      points,\n      point;\n\n  while (1) {\n    // Find first unvisited intersection.\n    var current = start,\n        isSubject = true;\n    while (current.v) if ((current = current.n) === start) return;\n    points = current.z;\n    stream.lineStart();\n    do {\n      current.v = current.o.v = true;\n      if (current.e) {\n        if (isSubject) {\n          for (i = 0, n = points.length; i < n; ++i) stream.point((point = points[i])[0], point[1]);\n        } else {\n          interpolate(current.x, current.n.x, 1, stream);\n        }\n        current = current.n;\n      } else {\n        if (isSubject) {\n          points = current.p.z;\n          for (i = points.length - 1; i >= 0; --i) stream.point((point = points[i])[0], point[1]);\n        } else {\n          interpolate(current.x, current.p.x, -1, stream);\n        }\n        current = current.p;\n      }\n      current = current.o;\n      points = current.z;\n      isSubject = !isSubject;\n    } while (!current.v);\n    stream.lineEnd();\n  }\n}\n\nfunction link(array) {\n  if (!(n = array.length)) return;\n  var n,\n      i = 0,\n      a = array[0],\n      b;\n  while (++i < n) {\n    a.n = b = array[i];\n    b.p = a;\n    a = b;\n  }\n  a.n = b = array[0];\n  b.p = a;\n}\n", "import {<PERSON>der} from \"d3-array\";\nimport {cartesian, cartesianCross, cartesianNormalizeInPlace} from \"./cartesian.js\";\nimport {abs, asin, atan2, cos, epsilon, epsilon2, halfPi, pi, quarterPi, sign, sin, tau} from \"./math.js\";\n\nfunction longitude(point) {\n  return abs(point[0]) <= pi ? point[0] : sign(point[0]) * ((abs(point[0]) + pi) % tau - pi);\n}\n\nexport default function(polygon, point) {\n  var lambda = longitude(point),\n      phi = point[1],\n      sinPhi = sin(phi),\n      normal = [sin(lambda), -cos(lambda), 0],\n      angle = 0,\n      winding = 0;\n\n  var sum = new Adder();\n\n  if (sinPhi === 1) phi = halfPi + epsilon;\n  else if (sinPhi === -1) phi = -halfPi - epsilon;\n\n  for (var i = 0, n = polygon.length; i < n; ++i) {\n    if (!(m = (ring = polygon[i]).length)) continue;\n    var ring,\n        m,\n        point0 = ring[m - 1],\n        lambda0 = longitude(point0),\n        phi0 = point0[1] / 2 + quarterPi,\n        sinPhi0 = sin(phi0),\n        cosPhi0 = cos(phi0);\n\n    for (var j = 0; j < m; ++j, lambda0 = lambda1, sinPhi0 = sinPhi1, cosPhi0 = cosPhi1, point0 = point1) {\n      var point1 = ring[j],\n          lambda1 = longitude(point1),\n          phi1 = point1[1] / 2 + quarterPi,\n          sinPhi1 = sin(phi1),\n          cosPhi1 = cos(phi1),\n          delta = lambda1 - lambda0,\n          sign = delta >= 0 ? 1 : -1,\n          absDelta = sign * delta,\n          antimeridian = absDelta > pi,\n          k = sinPhi0 * sinPhi1;\n\n      sum.add(atan2(k * sign * sin(absDelta), cosPhi0 * cosPhi1 + k * cos(absDelta)));\n      angle += antimeridian ? delta + sign * tau : delta;\n\n      // Are the longitudes either side of the point’s meridian (lambda),\n      // and are the latitudes smaller than the parallel (phi)?\n      if (antimeridian ^ lambda0 >= lambda ^ lambda1 >= lambda) {\n        var arc = cartesianCross(cartesian(point0), cartesian(point1));\n        cartesianNormalizeInPlace(arc);\n        var intersection = cartesianCross(normal, arc);\n        cartesianNormalizeInPlace(intersection);\n        var phiArc = (antimeridian ^ delta >= 0 ? -1 : 1) * asin(intersection[2]);\n        if (phi > phiArc || phi === phiArc && (arc[0] || arc[1])) {\n          winding += antimeridian ^ delta >= 0 ? 1 : -1;\n        }\n      }\n    }\n  }\n\n  // First, determine whether the South pole is inside or outside:\n  //\n  // It is inside if:\n  // * the polygon winds around it in a clockwise direction.\n  // * the polygon does not (cumulatively) wind around it, but has a negative\n  //   (counter-clockwise) area.\n  //\n  // Second, count the (signed) number of times a segment crosses a lambda\n  // from the point to the South pole.  If it is zero, then the point is the\n  // same side as the South pole.\n\n  return (angle < -epsilon || angle < epsilon && sum < -epsilon2) ^ (winding & 1);\n}\n", "import clipBuffer from \"./buffer.js\";\nimport clipRejoin from \"./rejoin.js\";\nimport {epsilon, halfPi} from \"../math.js\";\nimport polygonContains from \"../polygonContains.js\";\nimport {merge} from \"d3-array\";\n\nexport default function(pointVisible, clipLine, interpolate, start) {\n  return function(sink) {\n    var line = clipLine(sink),\n        ringBuffer = clipBuffer(),\n        ringSink = clipLine(ringBuffer),\n        polygonStarted = false,\n        polygon,\n        segments,\n        ring;\n\n    var clip = {\n      point: point,\n      lineStart: lineStart,\n      lineEnd: lineEnd,\n      polygonStart: function() {\n        clip.point = pointRing;\n        clip.lineStart = ringStart;\n        clip.lineEnd = ringEnd;\n        segments = [];\n        polygon = [];\n      },\n      polygonEnd: function() {\n        clip.point = point;\n        clip.lineStart = lineStart;\n        clip.lineEnd = lineEnd;\n        segments = merge(segments);\n        var startInside = polygonContains(polygon, start);\n        if (segments.length) {\n          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n          clipRejoin(segments, compareIntersection, startInside, interpolate, sink);\n        } else if (startInside) {\n          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n          sink.lineStart();\n          interpolate(null, null, 1, sink);\n          sink.lineEnd();\n        }\n        if (polygonStarted) sink.polygonEnd(), polygonStarted = false;\n        segments = polygon = null;\n      },\n      sphere: function() {\n        sink.polygonStart();\n        sink.lineStart();\n        interpolate(null, null, 1, sink);\n        sink.lineEnd();\n        sink.polygonEnd();\n      }\n    };\n\n    function point(lambda, phi) {\n      if (pointVisible(lambda, phi)) sink.point(lambda, phi);\n    }\n\n    function pointLine(lambda, phi) {\n      line.point(lambda, phi);\n    }\n\n    function lineStart() {\n      clip.point = pointLine;\n      line.lineStart();\n    }\n\n    function lineEnd() {\n      clip.point = point;\n      line.lineEnd();\n    }\n\n    function pointRing(lambda, phi) {\n      ring.push([lambda, phi]);\n      ringSink.point(lambda, phi);\n    }\n\n    function ringStart() {\n      ringSink.lineStart();\n      ring = [];\n    }\n\n    function ringEnd() {\n      pointRing(ring[0][0], ring[0][1]);\n      ringSink.lineEnd();\n\n      var clean = ringSink.clean(),\n          ringSegments = ringBuffer.result(),\n          i, n = ringSegments.length, m,\n          segment,\n          point;\n\n      ring.pop();\n      polygon.push(ring);\n      ring = null;\n\n      if (!n) return;\n\n      // No intersections.\n      if (clean & 1) {\n        segment = ringSegments[0];\n        if ((m = segment.length - 1) > 0) {\n          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n          sink.lineStart();\n          for (i = 0; i < m; ++i) sink.point((point = segment[i])[0], point[1]);\n          sink.lineEnd();\n        }\n        return;\n      }\n\n      // Rejoin connected segments.\n      // TODO reuse ringBuffer.rejoin()?\n      if (n > 1 && clean & 2) ringSegments.push(ringSegments.pop().concat(ringSegments.shift()));\n\n      segments.push(ringSegments.filter(validSegment));\n    }\n\n    return clip;\n  };\n}\n\nfunction validSegment(segment) {\n  return segment.length > 1;\n}\n\n// Intersections are sorted along the clip edge. For both antimeridian cutting\n// and circle clipping, the same comparison is used.\nfunction compareIntersection(a, b) {\n  return ((a = a.x)[0] < 0 ? a[1] - halfPi - epsilon : halfPi - a[1])\n       - ((b = b.x)[0] < 0 ? b[1] - halfPi - epsilon : halfPi - b[1]);\n}\n", "import clip from \"./index.js\";\nimport {abs, atan, cos, epsilon, halfPi, pi, sin} from \"../math.js\";\n\nexport default clip(\n  function() { return true; },\n  clipAntimeridianLine,\n  clipAntimeridianInterpolate,\n  [-pi, -halfPi]\n);\n\n// Takes a line and cuts into visible segments. Return values: 0 - there were\n// intersections or the line was empty; 1 - no intersections; 2 - there were\n// intersections, and the first and last segments should be rejoined.\nfunction clipAntimeridianLine(stream) {\n  var lambda0 = NaN,\n      phi0 = NaN,\n      sign0 = NaN,\n      clean; // no intersections\n\n  return {\n    lineStart: function() {\n      stream.lineStart();\n      clean = 1;\n    },\n    point: function(lambda1, phi1) {\n      var sign1 = lambda1 > 0 ? pi : -pi,\n          delta = abs(lambda1 - lambda0);\n      if (abs(delta - pi) < epsilon) { // line crosses a pole\n        stream.point(lambda0, phi0 = (phi0 + phi1) / 2 > 0 ? halfPi : -halfPi);\n        stream.point(sign0, phi0);\n        stream.lineEnd();\n        stream.lineStart();\n        stream.point(sign1, phi0);\n        stream.point(lambda1, phi0);\n        clean = 0;\n      } else if (sign0 !== sign1 && delta >= pi) { // line crosses antimeridian\n        if (abs(lambda0 - sign0) < epsilon) lambda0 -= sign0 * epsilon; // handle degeneracies\n        if (abs(lambda1 - sign1) < epsilon) lambda1 -= sign1 * epsilon;\n        phi0 = clipAntimeridianIntersect(lambda0, phi0, lambda1, phi1);\n        stream.point(sign0, phi0);\n        stream.lineEnd();\n        stream.lineStart();\n        stream.point(sign1, phi0);\n        clean = 0;\n      }\n      stream.point(lambda0 = lambda1, phi0 = phi1);\n      sign0 = sign1;\n    },\n    lineEnd: function() {\n      stream.lineEnd();\n      lambda0 = phi0 = NaN;\n    },\n    clean: function() {\n      return 2 - clean; // if intersections, rejoin first and last segments\n    }\n  };\n}\n\nfunction clipAntimeridianIntersect(lambda0, phi0, lambda1, phi1) {\n  var cosPhi0,\n      cosPhi1,\n      sinLambda0Lambda1 = sin(lambda0 - lambda1);\n  return abs(sinLambda0Lambda1) > epsilon\n      ? atan((sin(phi0) * (cosPhi1 = cos(phi1)) * sin(lambda1)\n          - sin(phi1) * (cosPhi0 = cos(phi0)) * sin(lambda0))\n          / (cosPhi0 * cosPhi1 * sinLambda0Lambda1))\n      : (phi0 + phi1) / 2;\n}\n\nfunction clipAntimeridianInterpolate(from, to, direction, stream) {\n  var phi;\n  if (from == null) {\n    phi = direction * halfPi;\n    stream.point(-pi, phi);\n    stream.point(0, phi);\n    stream.point(pi, phi);\n    stream.point(pi, 0);\n    stream.point(pi, -phi);\n    stream.point(0, -phi);\n    stream.point(-pi, -phi);\n    stream.point(-pi, 0);\n    stream.point(-pi, phi);\n  } else if (abs(from[0] - to[0]) > epsilon) {\n    var lambda = from[0] < to[0] ? pi : -pi;\n    phi = direction * lambda / 2;\n    stream.point(-lambda, phi);\n    stream.point(0, phi);\n    stream.point(lambda, phi);\n  } else {\n    stream.point(to[0], to[1]);\n  }\n}\n", "import {cartesian, cartesianAddInPlace, cartesianCross, cartesianDot, cartesianScale, spherical} from \"../cartesian.js\";\nimport {circleStream} from \"../circle.js\";\nimport {abs, cos, epsilon, pi, radians, sqrt} from \"../math.js\";\nimport pointEqual from \"../pointEqual.js\";\nimport clip from \"./index.js\";\n\nexport default function(radius) {\n  var cr = cos(radius),\n      delta = 2 * radians,\n      smallRadius = cr > 0,\n      notHemisphere = abs(cr) > epsilon; // TODO optimise for this common case\n\n  function interpolate(from, to, direction, stream) {\n    circleStream(stream, radius, delta, direction, from, to);\n  }\n\n  function visible(lambda, phi) {\n    return cos(lambda) * cos(phi) > cr;\n  }\n\n  // Takes a line and cuts into visible segments. Return values used for polygon\n  // clipping: 0 - there were intersections or the line was empty; 1 - no\n  // intersections 2 - there were intersections, and the first and last segments\n  // should be rejoined.\n  function clipLine(stream) {\n    var point0, // previous point\n        c0, // code for previous point\n        v0, // visibility of previous point\n        v00, // visibility of first point\n        clean; // no intersections\n    return {\n      lineStart: function() {\n        v00 = v0 = false;\n        clean = 1;\n      },\n      point: function(lambda, phi) {\n        var point1 = [lambda, phi],\n            point2,\n            v = visible(lambda, phi),\n            c = smallRadius\n              ? v ? 0 : code(lambda, phi)\n              : v ? code(lambda + (lambda < 0 ? pi : -pi), phi) : 0;\n        if (!point0 && (v00 = v0 = v)) stream.lineStart();\n        if (v !== v0) {\n          point2 = intersect(point0, point1);\n          if (!point2 || pointEqual(point0, point2) || pointEqual(point1, point2))\n            point1[2] = 1;\n        }\n        if (v !== v0) {\n          clean = 0;\n          if (v) {\n            // outside going in\n            stream.lineStart();\n            point2 = intersect(point1, point0);\n            stream.point(point2[0], point2[1]);\n          } else {\n            // inside going out\n            point2 = intersect(point0, point1);\n            stream.point(point2[0], point2[1], 2);\n            stream.lineEnd();\n          }\n          point0 = point2;\n        } else if (notHemisphere && point0 && smallRadius ^ v) {\n          var t;\n          // If the codes for two points are different, or are both zero,\n          // and there this segment intersects with the small circle.\n          if (!(c & c0) && (t = intersect(point1, point0, true))) {\n            clean = 0;\n            if (smallRadius) {\n              stream.lineStart();\n              stream.point(t[0][0], t[0][1]);\n              stream.point(t[1][0], t[1][1]);\n              stream.lineEnd();\n            } else {\n              stream.point(t[1][0], t[1][1]);\n              stream.lineEnd();\n              stream.lineStart();\n              stream.point(t[0][0], t[0][1], 3);\n            }\n          }\n        }\n        if (v && (!point0 || !pointEqual(point0, point1))) {\n          stream.point(point1[0], point1[1]);\n        }\n        point0 = point1, v0 = v, c0 = c;\n      },\n      lineEnd: function() {\n        if (v0) stream.lineEnd();\n        point0 = null;\n      },\n      // Rejoin first and last segments if there were intersections and the first\n      // and last points were visible.\n      clean: function() {\n        return clean | ((v00 && v0) << 1);\n      }\n    };\n  }\n\n  // Intersects the great circle between a and b with the clip circle.\n  function intersect(a, b, two) {\n    var pa = cartesian(a),\n        pb = cartesian(b);\n\n    // We have two planes, n1.p = d1 and n2.p = d2.\n    // Find intersection line p(t) = c1 n1 + c2 n2 + t (n1 ⨯ n2).\n    var n1 = [1, 0, 0], // normal\n        n2 = cartesianCross(pa, pb),\n        n2n2 = cartesianDot(n2, n2),\n        n1n2 = n2[0], // cartesianDot(n1, n2),\n        determinant = n2n2 - n1n2 * n1n2;\n\n    // Two polar points.\n    if (!determinant) return !two && a;\n\n    var c1 =  cr * n2n2 / determinant,\n        c2 = -cr * n1n2 / determinant,\n        n1xn2 = cartesianCross(n1, n2),\n        A = cartesianScale(n1, c1),\n        B = cartesianScale(n2, c2);\n    cartesianAddInPlace(A, B);\n\n    // Solve |p(t)|^2 = 1.\n    var u = n1xn2,\n        w = cartesianDot(A, u),\n        uu = cartesianDot(u, u),\n        t2 = w * w - uu * (cartesianDot(A, A) - 1);\n\n    if (t2 < 0) return;\n\n    var t = sqrt(t2),\n        q = cartesianScale(u, (-w - t) / uu);\n    cartesianAddInPlace(q, A);\n    q = spherical(q);\n\n    if (!two) return q;\n\n    // Two intersection points.\n    var lambda0 = a[0],\n        lambda1 = b[0],\n        phi0 = a[1],\n        phi1 = b[1],\n        z;\n\n    if (lambda1 < lambda0) z = lambda0, lambda0 = lambda1, lambda1 = z;\n\n    var delta = lambda1 - lambda0,\n        polar = abs(delta - pi) < epsilon,\n        meridian = polar || delta < epsilon;\n\n    if (!polar && phi1 < phi0) z = phi0, phi0 = phi1, phi1 = z;\n\n    // Check that the first point is between a and b.\n    if (meridian\n        ? polar\n          ? phi0 + phi1 > 0 ^ q[1] < (abs(q[0] - lambda0) < epsilon ? phi0 : phi1)\n          : phi0 <= q[1] && q[1] <= phi1\n        : delta > pi ^ (lambda0 <= q[0] && q[0] <= lambda1)) {\n      var q1 = cartesianScale(u, (-w + t) / uu);\n      cartesianAddInPlace(q1, A);\n      return [q, spherical(q1)];\n    }\n  }\n\n  // Generates a 4-bit vector representing the location of a point relative to\n  // the small circle's bounding box.\n  function code(lambda, phi) {\n    var r = smallRadius ? radius : pi - radius,\n        code = 0;\n    if (lambda < -r) code |= 1; // left\n    else if (lambda > r) code |= 2; // right\n    if (phi < -r) code |= 4; // below\n    else if (phi > r) code |= 8; // above\n    return code;\n  }\n\n  return clip(visible, clipLine, interpolate, smallRadius ? [0, -radius] : [-pi, radius - pi]);\n}\n", "export default function(a, b, x0, y0, x1, y1) {\n  var ax = a[0],\n      ay = a[1],\n      bx = b[0],\n      by = b[1],\n      t0 = 0,\n      t1 = 1,\n      dx = bx - ax,\n      dy = by - ay,\n      r;\n\n  r = x0 - ax;\n  if (!dx && r > 0) return;\n  r /= dx;\n  if (dx < 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  } else if (dx > 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  }\n\n  r = x1 - ax;\n  if (!dx && r < 0) return;\n  r /= dx;\n  if (dx < 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  } else if (dx > 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  }\n\n  r = y0 - ay;\n  if (!dy && r > 0) return;\n  r /= dy;\n  if (dy < 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  } else if (dy > 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  }\n\n  r = y1 - ay;\n  if (!dy && r < 0) return;\n  r /= dy;\n  if (dy < 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  } else if (dy > 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  }\n\n  if (t0 > 0) a[0] = ax + t0 * dx, a[1] = ay + t0 * dy;\n  if (t1 < 1) b[0] = ax + t1 * dx, b[1] = ay + t1 * dy;\n  return true;\n}\n", "import {abs, epsilon} from \"../math.js\";\nimport clipBuffer from \"./buffer.js\";\nimport clipLine from \"./line.js\";\nimport clipRejoin from \"./rejoin.js\";\nimport {merge} from \"d3-array\";\n\nvar clipMax = 1e9, clipMin = -clipMax;\n\n// TODO Use d3-polygon’s polygonContains here for the ring check?\n// TODO Eliminate duplicate buffering in clipBuffer and polygon.push?\n\nexport default function clipRectangle(x0, y0, x1, y1) {\n\n  function visible(x, y) {\n    return x0 <= x && x <= x1 && y0 <= y && y <= y1;\n  }\n\n  function interpolate(from, to, direction, stream) {\n    var a = 0, a1 = 0;\n    if (from == null\n        || (a = corner(from, direction)) !== (a1 = corner(to, direction))\n        || comparePoint(from, to) < 0 ^ direction > 0) {\n      do stream.point(a === 0 || a === 3 ? x0 : x1, a > 1 ? y1 : y0);\n      while ((a = (a + direction + 4) % 4) !== a1);\n    } else {\n      stream.point(to[0], to[1]);\n    }\n  }\n\n  function corner(p, direction) {\n    return abs(p[0] - x0) < epsilon ? direction > 0 ? 0 : 3\n        : abs(p[0] - x1) < epsilon ? direction > 0 ? 2 : 1\n        : abs(p[1] - y0) < epsilon ? direction > 0 ? 1 : 0\n        : direction > 0 ? 3 : 2; // abs(p[1] - y1) < epsilon\n  }\n\n  function compareIntersection(a, b) {\n    return comparePoint(a.x, b.x);\n  }\n\n  function comparePoint(a, b) {\n    var ca = corner(a, 1),\n        cb = corner(b, 1);\n    return ca !== cb ? ca - cb\n        : ca === 0 ? b[1] - a[1]\n        : ca === 1 ? a[0] - b[0]\n        : ca === 2 ? a[1] - b[1]\n        : b[0] - a[0];\n  }\n\n  return function(stream) {\n    var activeStream = stream,\n        bufferStream = clipBuffer(),\n        segments,\n        polygon,\n        ring,\n        x__, y__, v__, // first point\n        x_, y_, v_, // previous point\n        first,\n        clean;\n\n    var clipStream = {\n      point: point,\n      lineStart: lineStart,\n      lineEnd: lineEnd,\n      polygonStart: polygonStart,\n      polygonEnd: polygonEnd\n    };\n\n    function point(x, y) {\n      if (visible(x, y)) activeStream.point(x, y);\n    }\n\n    function polygonInside() {\n      var winding = 0;\n\n      for (var i = 0, n = polygon.length; i < n; ++i) {\n        for (var ring = polygon[i], j = 1, m = ring.length, point = ring[0], a0, a1, b0 = point[0], b1 = point[1]; j < m; ++j) {\n          a0 = b0, a1 = b1, point = ring[j], b0 = point[0], b1 = point[1];\n          if (a1 <= y1) { if (b1 > y1 && (b0 - a0) * (y1 - a1) > (b1 - a1) * (x0 - a0)) ++winding; }\n          else { if (b1 <= y1 && (b0 - a0) * (y1 - a1) < (b1 - a1) * (x0 - a0)) --winding; }\n        }\n      }\n\n      return winding;\n    }\n\n    // Buffer geometry within a polygon and then clip it en masse.\n    function polygonStart() {\n      activeStream = bufferStream, segments = [], polygon = [], clean = true;\n    }\n\n    function polygonEnd() {\n      var startInside = polygonInside(),\n          cleanInside = clean && startInside,\n          visible = (segments = merge(segments)).length;\n      if (cleanInside || visible) {\n        stream.polygonStart();\n        if (cleanInside) {\n          stream.lineStart();\n          interpolate(null, null, 1, stream);\n          stream.lineEnd();\n        }\n        if (visible) {\n          clipRejoin(segments, compareIntersection, startInside, interpolate, stream);\n        }\n        stream.polygonEnd();\n      }\n      activeStream = stream, segments = polygon = ring = null;\n    }\n\n    function lineStart() {\n      clipStream.point = linePoint;\n      if (polygon) polygon.push(ring = []);\n      first = true;\n      v_ = false;\n      x_ = y_ = NaN;\n    }\n\n    // TODO rather than special-case polygons, simply handle them separately.\n    // Ideally, coincident intersection points should be jittered to avoid\n    // clipping issues.\n    function lineEnd() {\n      if (segments) {\n        linePoint(x__, y__);\n        if (v__ && v_) bufferStream.rejoin();\n        segments.push(bufferStream.result());\n      }\n      clipStream.point = point;\n      if (v_) activeStream.lineEnd();\n    }\n\n    function linePoint(x, y) {\n      var v = visible(x, y);\n      if (polygon) ring.push([x, y]);\n      if (first) {\n        x__ = x, y__ = y, v__ = v;\n        first = false;\n        if (v) {\n          activeStream.lineStart();\n          activeStream.point(x, y);\n        }\n      } else {\n        if (v && v_) activeStream.point(x, y);\n        else {\n          var a = [x_ = Math.max(clipMin, Math.min(clipMax, x_)), y_ = Math.max(clipMin, Math.min(clipMax, y_))],\n              b = [x = Math.max(clipMin, Math.min(clipMax, x)), y = Math.max(clipMin, Math.min(clipMax, y))];\n          if (clipLine(a, b, x0, y0, x1, y1)) {\n            if (!v_) {\n              activeStream.lineStart();\n              activeStream.point(a[0], a[1]);\n            }\n            activeStream.point(b[0], b[1]);\n            if (!v) activeStream.lineEnd();\n            clean = false;\n          } else if (v) {\n            activeStream.lineStart();\n            activeStream.point(x, y);\n            clean = false;\n          }\n        }\n      }\n      x_ = x, y_ = y, v_ = v;\n    }\n\n    return clipStream;\n  };\n}\n", "import {Adder} from \"d3-array\";\nimport {abs, atan2, cos, radians, sin, sqrt} from \"./math.js\";\nimport noop from \"./noop.js\";\nimport stream from \"./stream.js\";\n\nvar lengthSum,\n    lambda0,\n    sinPhi0,\n    cosPhi0;\n\nvar lengthStream = {\n  sphere: noop,\n  point: noop,\n  lineStart: lengthLineStart,\n  lineEnd: noop,\n  polygonStart: noop,\n  polygonEnd: noop\n};\n\nfunction lengthLineStart() {\n  lengthStream.point = lengthPointFirst;\n  lengthStream.lineEnd = lengthLineEnd;\n}\n\nfunction lengthLineEnd() {\n  lengthStream.point = lengthStream.lineEnd = noop;\n}\n\nfunction lengthPointFirst(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  lambda0 = lambda, sinPhi0 = sin(phi), cosPhi0 = cos(phi);\n  lengthStream.point = lengthPoint;\n}\n\nfunction lengthPoint(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  var sinPhi = sin(phi),\n      cosPhi = cos(phi),\n      delta = abs(lambda - lambda0),\n      cosDelta = cos(delta),\n      sinDelta = sin(delta),\n      x = cosPhi * sinDelta,\n      y = cosPhi0 * sinPhi - sinPhi0 * cosPhi * cosDelta,\n      z = sinPhi0 * sinPhi + cosPhi0 * cosPhi * cosDelta;\n  lengthSum.add(atan2(sqrt(x * x + y * y), z));\n  lambda0 = lambda, sinPhi0 = sinPhi, cosPhi0 = cosPhi;\n}\n\nexport default function(object) {\n  lengthSum = new Adder();\n  stream(object, lengthStream);\n  return +lengthSum;\n}\n", "import length from \"./length.js\";\n\nvar coordinates = [null, null],\n    object = {type: \"LineString\", coordinates: coordinates};\n\nexport default function(a, b) {\n  coordinates[0] = a;\n  coordinates[1] = b;\n  return length(object);\n}\n", "import {default as polygonContains} from \"./polygonContains.js\";\nimport {default as distance} from \"./distance.js\";\nimport {epsilon2, radians} from \"./math.js\";\n\nvar containsObjectType = {\n  Feature: function(object, point) {\n    return containsGeometry(object.geometry, point);\n  },\n  FeatureCollection: function(object, point) {\n    var features = object.features, i = -1, n = features.length;\n    while (++i < n) if (containsGeometry(features[i].geometry, point)) return true;\n    return false;\n  }\n};\n\nvar containsGeometryType = {\n  Sphere: function() {\n    return true;\n  },\n  Point: function(object, point) {\n    return containsPoint(object.coordinates, point);\n  },\n  MultiPoint: function(object, point) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) if (containsPoint(coordinates[i], point)) return true;\n    return false;\n  },\n  LineString: function(object, point) {\n    return containsLine(object.coordinates, point);\n  },\n  MultiLineString: function(object, point) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) if (containsLine(coordinates[i], point)) return true;\n    return false;\n  },\n  Polygon: function(object, point) {\n    return containsPolygon(object.coordinates, point);\n  },\n  MultiPolygon: function(object, point) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) if (containsPolygon(coordinates[i], point)) return true;\n    return false;\n  },\n  GeometryCollection: function(object, point) {\n    var geometries = object.geometries, i = -1, n = geometries.length;\n    while (++i < n) if (containsGeometry(geometries[i], point)) return true;\n    return false;\n  }\n};\n\nfunction containsGeometry(geometry, point) {\n  return geometry && containsGeometryType.hasOwnProperty(geometry.type)\n      ? containsGeometryType[geometry.type](geometry, point)\n      : false;\n}\n\nfunction containsPoint(coordinates, point) {\n  return distance(coordinates, point) === 0;\n}\n\nfunction containsLine(coordinates, point) {\n  var ao, bo, ab;\n  for (var i = 0, n = coordinates.length; i < n; i++) {\n    bo = distance(coordinates[i], point);\n    if (bo === 0) return true;\n    if (i > 0) {\n      ab = distance(coordinates[i], coordinates[i - 1]);\n      if (\n        ab > 0 &&\n        ao <= ab &&\n        bo <= ab &&\n        (ao + bo - ab) * (1 - Math.pow((ao - bo) / ab, 2)) < epsilon2 * ab\n      )\n        return true;\n    }\n    ao = bo;\n  }\n  return false;\n}\n\nfunction containsPolygon(coordinates, point) {\n  return !!polygonContains(coordinates.map(ringRadians), pointRadians(point));\n}\n\nfunction ringRadians(ring) {\n  return ring = ring.map(pointRadians), ring.pop(), ring;\n}\n\nfunction pointRadians(point) {\n  return [point[0] * radians, point[1] * radians];\n}\n\nexport default function(object, point) {\n  return (object && containsObjectType.hasOwnProperty(object.type)\n      ? containsObjectType[object.type]\n      : containsGeometry)(object, point);\n}\n", "import {asin, atan2, cos, degrees, haversin, radians, sin, sqrt} from \"./math.js\";\n\nexport default function(a, b) {\n  var x0 = a[0] * radians,\n      y0 = a[1] * radians,\n      x1 = b[0] * radians,\n      y1 = b[1] * radians,\n      cy0 = cos(y0),\n      sy0 = sin(y0),\n      cy1 = cos(y1),\n      sy1 = sin(y1),\n      kx0 = cy0 * cos(x0),\n      ky0 = cy0 * sin(x0),\n      kx1 = cy1 * cos(x1),\n      ky1 = cy1 * sin(x1),\n      d = 2 * asin(sqrt(haversin(y1 - y0) + cy0 * cy1 * haversin(x1 - x0))),\n      k = sin(d);\n\n  var interpolate = d ? function(t) {\n    var B = sin(t *= d) / k,\n        A = sin(d - t) / k,\n        x = A * kx0 + B * kx1,\n        y = A * ky0 + B * ky1,\n        z = A * sy0 + B * sy1;\n    return [\n      atan2(y, x) * degrees,\n      atan2(z, sqrt(x * x + y * y)) * degrees\n    ];\n  } : function() {\n    return [x0 * degrees, y0 * degrees];\n  };\n\n  interpolate.distance = d;\n\n  return interpolate;\n}\n", "export default x => x;\n", "import noop from \"../noop.js\";\n\nvar x0 = Infinity,\n    y0 = x0,\n    x1 = -x0,\n    y1 = x1;\n\nvar boundsStream = {\n  point: boundsPoint,\n  lineStart: noop,\n  lineEnd: noop,\n  polygonStart: noop,\n  polygonEnd: noop,\n  result: function() {\n    var bounds = [[x0, y0], [x1, y1]];\n    x1 = y1 = -(y0 = x0 = Infinity);\n    return bounds;\n  }\n};\n\nfunction boundsPoint(x, y) {\n  if (x < x0) x0 = x;\n  if (x > x1) x1 = x;\n  if (y < y0) y0 = y;\n  if (y > y1) y1 = y;\n}\n\nexport default boundsStream;\n", "export default function(methods) {\n  return {\n    stream: transformer(methods)\n  };\n}\n\nexport function transformer(methods) {\n  return function(stream) {\n    var s = new TransformStream;\n    for (var key in methods) s[key] = methods[key];\n    s.stream = stream;\n    return s;\n  };\n}\n\nfunction TransformStream() {}\n\nTransformStream.prototype = {\n  constructor: TransformStream,\n  point: function(x, y) { this.stream.point(x, y); },\n  sphere: function() { this.stream.sphere(); },\n  lineStart: function() { this.stream.lineStart(); },\n  lineEnd: function() { this.stream.lineEnd(); },\n  polygonStart: function() { this.stream.polygonStart(); },\n  polygonEnd: function() { this.stream.polygonEnd(); }\n};\n", "import {default as geoStream} from \"../stream.js\";\nimport boundsStream from \"../path/bounds.js\";\n\nfunction fit(projection, fitBounds, object) {\n  var clip = projection.clipExtent && projection.clipExtent();\n  projection.scale(150).translate([0, 0]);\n  if (clip != null) projection.clipExtent(null);\n  geoStream(object, projection.stream(boundsStream));\n  fitBounds(boundsStream.result());\n  if (clip != null) projection.clipExtent(clip);\n  return projection;\n}\n\nexport function fitExtent(projection, extent, object) {\n  return fit(projection, function(b) {\n    var w = extent[1][0] - extent[0][0],\n        h = extent[1][1] - extent[0][1],\n        k = Math.min(w / (b[1][0] - b[0][0]), h / (b[1][1] - b[0][1])),\n        x = +extent[0][0] + (w - k * (b[1][0] + b[0][0])) / 2,\n        y = +extent[0][1] + (h - k * (b[1][1] + b[0][1])) / 2;\n    projection.scale(150 * k).translate([x, y]);\n  }, object);\n}\n\nexport function fitSize(projection, size, object) {\n  return fitExtent(projection, [[0, 0], size], object);\n}\n\nexport function fitWidth(projection, width, object) {\n  return fit(projection, function(b) {\n    var w = +width,\n        k = w / (b[1][0] - b[0][0]),\n        x = (w - k * (b[1][0] + b[0][0])) / 2,\n        y = -k * b[0][1];\n    projection.scale(150 * k).translate([x, y]);\n  }, object);\n}\n\nexport function fitHeight(projection, height, object) {\n  return fit(projection, function(b) {\n    var h = +height,\n        k = h / (b[1][1] - b[0][1]),\n        x = -k * b[0][0],\n        y = (h - k * (b[1][1] + b[0][1])) / 2;\n    projection.scale(150 * k).translate([x, y]);\n  }, object);\n}\n", "import {cartesian} from \"../cartesian.js\";\nimport {abs, asin, atan2, cos, epsilon, radians, sqrt} from \"../math.js\";\nimport {transformer} from \"../transform.js\";\n\nvar maxDepth = 16, // maximum depth of subdivision\n    cosMinDistance = cos(30 * radians); // cos(minimum angular distance)\n\nexport default function(project, delta2) {\n  return +delta2 ? resample(project, delta2) : resampleNone(project);\n}\n\nfunction resampleNone(project) {\n  return transformer({\n    point: function(x, y) {\n      x = project(x, y);\n      this.stream.point(x[0], x[1]);\n    }\n  });\n}\n\nfunction resample(project, delta2) {\n\n  function resampleLineTo(x0, y0, lambda0, a0, b0, c0, x1, y1, lambda1, a1, b1, c1, depth, stream) {\n    var dx = x1 - x0,\n        dy = y1 - y0,\n        d2 = dx * dx + dy * dy;\n    if (d2 > 4 * delta2 && depth--) {\n      var a = a0 + a1,\n          b = b0 + b1,\n          c = c0 + c1,\n          m = sqrt(a * a + b * b + c * c),\n          phi2 = asin(c /= m),\n          lambda2 = abs(abs(c) - 1) < epsilon || abs(lambda0 - lambda1) < epsilon ? (lambda0 + lambda1) / 2 : atan2(b, a),\n          p = project(lambda2, phi2),\n          x2 = p[0],\n          y2 = p[1],\n          dx2 = x2 - x0,\n          dy2 = y2 - y0,\n          dz = dy * dx2 - dx * dy2;\n      if (dz * dz / d2 > delta2 // perpendicular projected distance\n          || abs((dx * dx2 + dy * dy2) / d2 - 0.5) > 0.3 // midpoint close to an end\n          || a0 * a1 + b0 * b1 + c0 * c1 < cosMinDistance) { // angular distance\n        resampleLineTo(x0, y0, lambda0, a0, b0, c0, x2, y2, lambda2, a /= m, b /= m, c, depth, stream);\n        stream.point(x2, y2);\n        resampleLineTo(x2, y2, lambda2, a, b, c, x1, y1, lambda1, a1, b1, c1, depth, stream);\n      }\n    }\n  }\n  return function(stream) {\n    var lambda00, x00, y00, a00, b00, c00, // first point\n        lambda0, x0, y0, a0, b0, c0; // previous point\n\n    var resampleStream = {\n      point: point,\n      lineStart: lineStart,\n      lineEnd: lineEnd,\n      polygonStart: function() { stream.polygonStart(); resampleStream.lineStart = ringStart; },\n      polygonEnd: function() { stream.polygonEnd(); resampleStream.lineStart = lineStart; }\n    };\n\n    function point(x, y) {\n      x = project(x, y);\n      stream.point(x[0], x[1]);\n    }\n\n    function lineStart() {\n      x0 = NaN;\n      resampleStream.point = linePoint;\n      stream.lineStart();\n    }\n\n    function linePoint(lambda, phi) {\n      var c = cartesian([lambda, phi]), p = project(lambda, phi);\n      resampleLineTo(x0, y0, lambda0, a0, b0, c0, x0 = p[0], y0 = p[1], lambda0 = lambda, a0 = c[0], b0 = c[1], c0 = c[2], maxDepth, stream);\n      stream.point(x0, y0);\n    }\n\n    function lineEnd() {\n      resampleStream.point = point;\n      stream.lineEnd();\n    }\n\n    function ringStart() {\n      lineStart();\n      resampleStream.point = ringPoint;\n      resampleStream.lineEnd = ringEnd;\n    }\n\n    function ringPoint(lambda, phi) {\n      linePoint(lambda00 = lambda, phi), x00 = x0, y00 = y0, a00 = a0, b00 = b0, c00 = c0;\n      resampleStream.point = linePoint;\n    }\n\n    function ringEnd() {\n      resampleLineTo(x0, y0, lambda0, a0, b0, c0, x00, y00, lambda00, a00, b00, c00, maxDepth, stream);\n      resampleStream.lineEnd = lineEnd;\n      lineEnd();\n    }\n\n    return resampleStream;\n  };\n}\n", "import clipAntimeridian from \"../clip/antimeridian.js\";\nimport clipCircle from \"../clip/circle.js\";\nimport clipRectangle from \"../clip/rectangle.js\";\nimport compose from \"../compose.js\";\nimport identity from \"../identity.js\";\nimport {cos, degrees, radians, sin, sqrt} from \"../math.js\";\nimport {rotateRadians} from \"../rotation.js\";\nimport {transformer} from \"../transform.js\";\nimport {fitExtent, fitSize, fitWidth, fitHeight} from \"./fit.js\";\nimport resample from \"./resample.js\";\n\nvar transformRadians = transformer({\n  point: function(x, y) {\n    this.stream.point(x * radians, y * radians);\n  }\n});\n\nfunction transformRotate(rotate) {\n  return transformer({\n    point: function(x, y) {\n      var r = rotate(x, y);\n      return this.stream.point(r[0], r[1]);\n    }\n  });\n}\n\nfunction scaleTranslate(k, dx, dy, sx, sy) {\n  function transform(x, y) {\n    x *= sx; y *= sy;\n    return [dx + k * x, dy - k * y];\n  }\n  transform.invert = function(x, y) {\n    return [(x - dx) / k * sx, (dy - y) / k * sy];\n  };\n  return transform;\n}\n\nfunction scaleTranslateRotate(k, dx, dy, sx, sy, alpha) {\n  if (!alpha) return scaleTranslate(k, dx, dy, sx, sy);\n  var cosAlpha = cos(alpha),\n      sinAlpha = sin(alpha),\n      a = cosAlpha * k,\n      b = sinAlpha * k,\n      ai = cosAlpha / k,\n      bi = sinAlpha / k,\n      ci = (sinAlpha * dy - cosAlpha * dx) / k,\n      fi = (sinAlpha * dx + cosAlpha * dy) / k;\n  function transform(x, y) {\n    x *= sx; y *= sy;\n    return [a * x - b * y + dx, dy - b * x - a * y];\n  }\n  transform.invert = function(x, y) {\n    return [sx * (ai * x - bi * y + ci), sy * (fi - bi * x - ai * y)];\n  };\n  return transform;\n}\n\nexport default function projection(project) {\n  return projectionMutator(function() { return project; })();\n}\n\nexport function projectionMutator(projectAt) {\n  var project,\n      k = 150, // scale\n      x = 480, y = 250, // translate\n      lambda = 0, phi = 0, // center\n      deltaLambda = 0, deltaPhi = 0, deltaGamma = 0, rotate, // pre-rotate\n      alpha = 0, // post-rotate angle\n      sx = 1, // reflectX\n      sy = 1, // reflectX\n      theta = null, preclip = clipAntimeridian, // pre-clip angle\n      x0 = null, y0, x1, y1, postclip = identity, // post-clip extent\n      delta2 = 0.5, // precision\n      projectResample,\n      projectTransform,\n      projectRotateTransform,\n      cache,\n      cacheStream;\n\n  function projection(point) {\n    return projectRotateTransform(point[0] * radians, point[1] * radians);\n  }\n\n  function invert(point) {\n    point = projectRotateTransform.invert(point[0], point[1]);\n    return point && [point[0] * degrees, point[1] * degrees];\n  }\n\n  projection.stream = function(stream) {\n    return cache && cacheStream === stream ? cache : cache = transformRadians(transformRotate(rotate)(preclip(projectResample(postclip(cacheStream = stream)))));\n  };\n\n  projection.preclip = function(_) {\n    return arguments.length ? (preclip = _, theta = undefined, reset()) : preclip;\n  };\n\n  projection.postclip = function(_) {\n    return arguments.length ? (postclip = _, x0 = y0 = x1 = y1 = null, reset()) : postclip;\n  };\n\n  projection.clipAngle = function(_) {\n    return arguments.length ? (preclip = +_ ? clipCircle(theta = _ * radians) : (theta = null, clipAntimeridian), reset()) : theta * degrees;\n  };\n\n  projection.clipExtent = function(_) {\n    return arguments.length ? (postclip = _ == null ? (x0 = y0 = x1 = y1 = null, identity) : clipRectangle(x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1]), reset()) : x0 == null ? null : [[x0, y0], [x1, y1]];\n  };\n\n  projection.scale = function(_) {\n    return arguments.length ? (k = +_, recenter()) : k;\n  };\n\n  projection.translate = function(_) {\n    return arguments.length ? (x = +_[0], y = +_[1], recenter()) : [x, y];\n  };\n\n  projection.center = function(_) {\n    return arguments.length ? (lambda = _[0] % 360 * radians, phi = _[1] % 360 * radians, recenter()) : [lambda * degrees, phi * degrees];\n  };\n\n  projection.rotate = function(_) {\n    return arguments.length ? (deltaLambda = _[0] % 360 * radians, deltaPhi = _[1] % 360 * radians, deltaGamma = _.length > 2 ? _[2] % 360 * radians : 0, recenter()) : [deltaLambda * degrees, deltaPhi * degrees, deltaGamma * degrees];\n  };\n\n  projection.angle = function(_) {\n    return arguments.length ? (alpha = _ % 360 * radians, recenter()) : alpha * degrees;\n  };\n\n  projection.reflectX = function(_) {\n    return arguments.length ? (sx = _ ? -1 : 1, recenter()) : sx < 0;\n  };\n\n  projection.reflectY = function(_) {\n    return arguments.length ? (sy = _ ? -1 : 1, recenter()) : sy < 0;\n  };\n\n  projection.precision = function(_) {\n    return arguments.length ? (projectResample = resample(projectTransform, delta2 = _ * _), reset()) : sqrt(delta2);\n  };\n\n  projection.fitExtent = function(extent, object) {\n    return fitExtent(projection, extent, object);\n  };\n\n  projection.fitSize = function(size, object) {\n    return fitSize(projection, size, object);\n  };\n\n  projection.fitWidth = function(width, object) {\n    return fitWidth(projection, width, object);\n  };\n\n  projection.fitHeight = function(height, object) {\n    return fitHeight(projection, height, object);\n  };\n\n  function recenter() {\n    var center = scaleTranslateRotate(k, 0, 0, sx, sy, alpha).apply(null, project(lambda, phi)),\n        transform = scaleTranslateRotate(k, x - center[0], y - center[1], sx, sy, alpha);\n    rotate = rotateRadians(deltaLambda, deltaPhi, deltaGamma);\n    projectTransform = compose(project, transform);\n    projectRotateTransform = compose(rotate, projectTransform);\n    projectResample = resample(projectTransform, delta2);\n    return reset();\n  }\n\n  function reset() {\n    cache = cacheStream = null;\n    return projection;\n  }\n\n  return function() {\n    project = projectAt.apply(this, arguments);\n    projection.invert = project.invert && invert;\n    return recenter();\n  };\n}\n", "import {asin, atan2, cos, sin, sqrt} from \"../math.js\";\n\nexport function azimuthalRaw(scale) {\n  return function(x, y) {\n    var cx = cos(x),\n        cy = cos(y),\n        k = scale(cx * cy);\n        if (k === Infinity) return [2, 0];\n    return [\n      k * cy * sin(x),\n      k * sin(y)\n    ];\n  }\n}\n\nexport function azimuthalInvert(angle) {\n  return function(x, y) {\n    var z = sqrt(x * x + y * y),\n        c = angle(z),\n        sc = sin(c),\n        cc = cos(c);\n    return [\n      atan2(x * sc, z * cc),\n      asin(z && y * sc / z)\n    ];\n  }\n}\n", "import {atan, cos, sin} from \"../math.js\";\nimport {azimuthalInvert} from \"./azimuthal.js\";\nimport projection from \"./index.js\";\n\nexport function stereographicRaw(x, y) {\n  var cy = cos(y), k = 1 + cos(x) * cy;\n  return [cy * sin(x) / k, sin(y) / k];\n}\n\nstereographicRaw.invert = azimuthalInvert(function(z) {\n  return 2 * atan(z);\n});\n\nexport default function() {\n  return projection(stereographicRaw)\n      .scale(250)\n      .clipAngle(142);\n}\n", "const epsilon = 1e-6;\n\nexport default class Path {\n  constructor() {\n    this._x0 = this._y0 = // start of current subpath\n    this._x1 = this._y1 = null; // end of current subpath\n    this._ = \"\";\n  }\n  moveTo(x, y) {\n    this._ += `M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}`;\n  }\n  closePath() {\n    if (this._x1 !== null) {\n      this._x1 = this._x0, this._y1 = this._y0;\n      this._ += \"Z\";\n    }\n  }\n  lineTo(x, y) {\n    this._ += `L${this._x1 = +x},${this._y1 = +y}`;\n  }\n  arc(x, y, r) {\n    x = +x, y = +y, r = +r;\n    const x0 = x + r;\n    const y0 = y;\n    if (r < 0) throw new Error(\"negative radius\");\n    if (this._x1 === null) this._ += `M${x0},${y0}`;\n    else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) this._ += \"L\" + x0 + \",\" + y0;\n    if (!r) return;\n    this._ += `A${r},${r},0,1,1,${x - r},${y}A${r},${r},0,1,1,${this._x1 = x0},${this._y1 = y0}`;\n  }\n  rect(x, y, w, h) {\n    this._ += `M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}h${+w}v${+h}h${-w}Z`;\n  }\n  value() {\n    return this._ || null;\n  }\n}\n", "export default class Polygon {\n  constructor() {\n    this._ = [];\n  }\n  moveTo(x, y) {\n    this._.push([x, y]);\n  }\n  closePath() {\n    this._.push(this._[0].slice());\n  }\n  lineTo(x, y) {\n    this._.push([x, y]);\n  }\n  value() {\n    return this._.length ? this._ : null;\n  }\n}\n", "import Path from \"./path.js\";\nimport Polygon from \"./polygon.js\";\n\nexport default class Voronoi {\n  constructor(delaunay, [xmin, ymin, xmax, ymax] = [0, 0, 960, 500]) {\n    if (!((xmax = +xmax) >= (xmin = +xmin)) || !((ymax = +ymax) >= (ymin = +ymin))) throw new Error(\"invalid bounds\");\n    this.delaunay = delaunay;\n    this._circumcenters = new Float64Array(delaunay.points.length * 2);\n    this.vectors = new Float64Array(delaunay.points.length * 2);\n    this.xmax = xmax, this.xmin = xmin;\n    this.ymax = ymax, this.ymin = ymin;\n    this._init();\n  }\n  update() {\n    this.delaunay.update();\n    this._init();\n    return this;\n  }\n  _init() {\n    const {delaunay: {points, hull, triangles}, vectors} = this;\n    let bx, by; // lazily computed barycenter of the hull\n\n    // Compute circumcenters.\n    const circumcenters = this.circumcenters = this._circumcenters.subarray(0, triangles.length / 3 * 2);\n    for (let i = 0, j = 0, n = triangles.length, x, y; i < n; i += 3, j += 2) {\n      const t1 = triangles[i] * 2;\n      const t2 = triangles[i + 1] * 2;\n      const t3 = triangles[i + 2] * 2;\n      const x1 = points[t1];\n      const y1 = points[t1 + 1];\n      const x2 = points[t2];\n      const y2 = points[t2 + 1];\n      const x3 = points[t3];\n      const y3 = points[t3 + 1];\n\n      const dx = x2 - x1;\n      const dy = y2 - y1;\n      const ex = x3 - x1;\n      const ey = y3 - y1;\n      const ab = (dx * ey - dy * ex) * 2;\n\n      if (Math.abs(ab) < 1e-9) {\n        // For a degenerate triangle, the circumcenter is at the infinity, in a\n        // direction orthogonal to the halfedge and away from the “center” of\n        // the diagram <bx, by>, defined as the hull’s barycenter.\n        if (bx === undefined) {\n          bx = by = 0;\n          for (const i of hull) bx += points[i * 2], by += points[i * 2 + 1];\n          bx /= hull.length, by /= hull.length;\n        }\n        const a = 1e9 * Math.sign((bx - x1) * ey - (by - y1) * ex);\n        x = (x1 + x3) / 2 - a * ey;\n        y = (y1 + y3) / 2 + a * ex;\n      } else {\n        const d = 1 / ab;\n        const bl = dx * dx + dy * dy;\n        const cl = ex * ex + ey * ey;\n        x = x1 + (ey * bl - dy * cl) * d;\n        y = y1 + (dx * cl - ex * bl) * d;\n      }\n      circumcenters[j] = x;\n      circumcenters[j + 1] = y;\n    }\n\n    // Compute exterior cell rays.\n    let h = hull[hull.length - 1];\n    let p0, p1 = h * 4;\n    let x0, x1 = points[2 * h];\n    let y0, y1 = points[2 * h + 1];\n    vectors.fill(0);\n    for (let i = 0; i < hull.length; ++i) {\n      h = hull[i];\n      p0 = p1, x0 = x1, y0 = y1;\n      p1 = h * 4, x1 = points[2 * h], y1 = points[2 * h + 1];\n      vectors[p0 + 2] = vectors[p1] = y0 - y1;\n      vectors[p0 + 3] = vectors[p1 + 1] = x1 - x0;\n    }\n  }\n  render(context) {\n    const buffer = context == null ? context = new Path : undefined;\n    const {delaunay: {halfedges, inedges, hull}, circumcenters, vectors} = this;\n    if (hull.length <= 1) return null;\n    for (let i = 0, n = halfedges.length; i < n; ++i) {\n      const j = halfedges[i];\n      if (j < i) continue;\n      const ti = Math.floor(i / 3) * 2;\n      const tj = Math.floor(j / 3) * 2;\n      const xi = circumcenters[ti];\n      const yi = circumcenters[ti + 1];\n      const xj = circumcenters[tj];\n      const yj = circumcenters[tj + 1];\n      this._renderSegment(xi, yi, xj, yj, context);\n    }\n    let h0, h1 = hull[hull.length - 1];\n    for (let i = 0; i < hull.length; ++i) {\n      h0 = h1, h1 = hull[i];\n      const t = Math.floor(inedges[h1] / 3) * 2;\n      const x = circumcenters[t];\n      const y = circumcenters[t + 1];\n      const v = h0 * 4;\n      const p = this._project(x, y, vectors[v + 2], vectors[v + 3]);\n      if (p) this._renderSegment(x, y, p[0], p[1], context);\n    }\n    return buffer && buffer.value();\n  }\n  renderBounds(context) {\n    const buffer = context == null ? context = new Path : undefined;\n    context.rect(this.xmin, this.ymin, this.xmax - this.xmin, this.ymax - this.ymin);\n    return buffer && buffer.value();\n  }\n  renderCell(i, context) {\n    const buffer = context == null ? context = new Path : undefined;\n    const points = this._clip(i);\n    if (points === null || !points.length) return;\n    context.moveTo(points[0], points[1]);\n    let n = points.length;\n    while (points[0] === points[n-2] && points[1] === points[n-1] && n > 1) n -= 2;\n    for (let i = 2; i < n; i += 2) {\n      if (points[i] !== points[i-2] || points[i+1] !== points[i-1])\n        context.lineTo(points[i], points[i + 1]);\n    }\n    context.closePath();\n    return buffer && buffer.value();\n  }\n  *cellPolygons() {\n    const {delaunay: {points}} = this;\n    for (let i = 0, n = points.length / 2; i < n; ++i) {\n      const cell = this.cellPolygon(i);\n      if (cell) cell.index = i, yield cell;\n    }\n  }\n  cellPolygon(i) {\n    const polygon = new Polygon;\n    this.renderCell(i, polygon);\n    return polygon.value();\n  }\n  _renderSegment(x0, y0, x1, y1, context) {\n    let S;\n    const c0 = this._regioncode(x0, y0);\n    const c1 = this._regioncode(x1, y1);\n    if (c0 === 0 && c1 === 0) {\n      context.moveTo(x0, y0);\n      context.lineTo(x1, y1);\n    } else if (S = this._clipSegment(x0, y0, x1, y1, c0, c1)) {\n      context.moveTo(S[0], S[1]);\n      context.lineTo(S[2], S[3]);\n    }\n  }\n  contains(i, x, y) {\n    if ((x = +x, x !== x) || (y = +y, y !== y)) return false;\n    return this.delaunay._step(i, x, y) === i;\n  }\n  *neighbors(i) {\n    const ci = this._clip(i);\n    if (ci) for (const j of this.delaunay.neighbors(i)) {\n      const cj = this._clip(j);\n      // find the common edge\n      if (cj) loop: for (let ai = 0, li = ci.length; ai < li; ai += 2) {\n        for (let aj = 0, lj = cj.length; aj < lj; aj += 2) {\n          if (ci[ai] === cj[aj]\n              && ci[ai + 1] === cj[aj + 1]\n              && ci[(ai + 2) % li] === cj[(aj + lj - 2) % lj]\n              && ci[(ai + 3) % li] === cj[(aj + lj - 1) % lj]) {\n            yield j;\n            break loop;\n          }\n        }\n      }\n    }\n  }\n  _cell(i) {\n    const {circumcenters, delaunay: {inedges, halfedges, triangles}} = this;\n    const e0 = inedges[i];\n    if (e0 === -1) return null; // coincident point\n    const points = [];\n    let e = e0;\n    do {\n      const t = Math.floor(e / 3);\n      points.push(circumcenters[t * 2], circumcenters[t * 2 + 1]);\n      e = e % 3 === 2 ? e - 2 : e + 1;\n      if (triangles[e] !== i) break; // bad triangulation\n      e = halfedges[e];\n    } while (e !== e0 && e !== -1);\n    return points;\n  }\n  _clip(i) {\n    // degenerate case (1 valid point: return the box)\n    if (i === 0 && this.delaunay.hull.length === 1) {\n      return [this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax, this.xmin, this.ymin];\n    }\n    const points = this._cell(i);\n    if (points === null) return null;\n    const {vectors: V} = this;\n    const v = i * 4;\n    return this._simplify(V[v] || V[v + 1]\n        ? this._clipInfinite(i, points, V[v], V[v + 1], V[v + 2], V[v + 3])\n        : this._clipFinite(i, points));\n  }\n  _clipFinite(i, points) {\n    const n = points.length;\n    let P = null;\n    let x0, y0, x1 = points[n - 2], y1 = points[n - 1];\n    let c0, c1 = this._regioncode(x1, y1);\n    let e0, e1 = 0;\n    for (let j = 0; j < n; j += 2) {\n      x0 = x1, y0 = y1, x1 = points[j], y1 = points[j + 1];\n      c0 = c1, c1 = this._regioncode(x1, y1);\n      if (c0 === 0 && c1 === 0) {\n        e0 = e1, e1 = 0;\n        if (P) P.push(x1, y1);\n        else P = [x1, y1];\n      } else {\n        let S, sx0, sy0, sx1, sy1;\n        if (c0 === 0) {\n          if ((S = this._clipSegment(x0, y0, x1, y1, c0, c1)) === null) continue;\n          [sx0, sy0, sx1, sy1] = S;\n        } else {\n          if ((S = this._clipSegment(x1, y1, x0, y0, c1, c0)) === null) continue;\n          [sx1, sy1, sx0, sy0] = S;\n          e0 = e1, e1 = this._edgecode(sx0, sy0);\n          if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n          if (P) P.push(sx0, sy0);\n          else P = [sx0, sy0];\n        }\n        e0 = e1, e1 = this._edgecode(sx1, sy1);\n        if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n        if (P) P.push(sx1, sy1);\n        else P = [sx1, sy1];\n      }\n    }\n    if (P) {\n      e0 = e1, e1 = this._edgecode(P[0], P[1]);\n      if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n    } else if (this.contains(i, (this.xmin + this.xmax) / 2, (this.ymin + this.ymax) / 2)) {\n      return [this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax, this.xmin, this.ymin];\n    }\n    return P;\n  }\n  _clipSegment(x0, y0, x1, y1, c0, c1) {\n    // for more robustness, always consider the segment in the same order\n    const flip = c0 < c1;\n    if (flip) [x0, y0, x1, y1, c0, c1] = [x1, y1, x0, y0, c1, c0];\n    while (true) {\n      if (c0 === 0 && c1 === 0) return flip ? [x1, y1, x0, y0] : [x0, y0, x1, y1];\n      if (c0 & c1) return null;\n      let x, y, c = c0 || c1;\n      if (c & 0b1000) x = x0 + (x1 - x0) * (this.ymax - y0) / (y1 - y0), y = this.ymax;\n      else if (c & 0b0100) x = x0 + (x1 - x0) * (this.ymin - y0) / (y1 - y0), y = this.ymin;\n      else if (c & 0b0010) y = y0 + (y1 - y0) * (this.xmax - x0) / (x1 - x0), x = this.xmax;\n      else y = y0 + (y1 - y0) * (this.xmin - x0) / (x1 - x0), x = this.xmin;\n      if (c0) x0 = x, y0 = y, c0 = this._regioncode(x0, y0);\n      else x1 = x, y1 = y, c1 = this._regioncode(x1, y1);\n    }\n  }\n  _clipInfinite(i, points, vx0, vy0, vxn, vyn) {\n    let P = Array.from(points), p;\n    if (p = this._project(P[0], P[1], vx0, vy0)) P.unshift(p[0], p[1]);\n    if (p = this._project(P[P.length - 2], P[P.length - 1], vxn, vyn)) P.push(p[0], p[1]);\n    if (P = this._clipFinite(i, P)) {\n      for (let j = 0, n = P.length, c0, c1 = this._edgecode(P[n - 2], P[n - 1]); j < n; j += 2) {\n        c0 = c1, c1 = this._edgecode(P[j], P[j + 1]);\n        if (c0 && c1) j = this._edge(i, c0, c1, P, j), n = P.length;\n      }\n    } else if (this.contains(i, (this.xmin + this.xmax) / 2, (this.ymin + this.ymax) / 2)) {\n      P = [this.xmin, this.ymin, this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax];\n    }\n    return P;\n  }\n  _edge(i, e0, e1, P, j) {\n    while (e0 !== e1) {\n      let x, y;\n      switch (e0) {\n        case 0b0101: e0 = 0b0100; continue; // top-left\n        case 0b0100: e0 = 0b0110, x = this.xmax, y = this.ymin; break; // top\n        case 0b0110: e0 = 0b0010; continue; // top-right\n        case 0b0010: e0 = 0b1010, x = this.xmax, y = this.ymax; break; // right\n        case 0b1010: e0 = 0b1000; continue; // bottom-right\n        case 0b1000: e0 = 0b1001, x = this.xmin, y = this.ymax; break; // bottom\n        case 0b1001: e0 = 0b0001; continue; // bottom-left\n        case 0b0001: e0 = 0b0101, x = this.xmin, y = this.ymin; break; // left\n      }\n      // Note: this implicitly checks for out of bounds: if P[j] or P[j+1] are\n      // undefined, the conditional statement will be executed.\n      if ((P[j] !== x || P[j + 1] !== y) && this.contains(i, x, y)) {\n        P.splice(j, 0, x, y), j += 2;\n      }\n    }\n    return j;\n  }\n  _project(x0, y0, vx, vy) {\n    let t = Infinity, c, x, y;\n    if (vy < 0) { // top\n      if (y0 <= this.ymin) return null;\n      if ((c = (this.ymin - y0) / vy) < t) y = this.ymin, x = x0 + (t = c) * vx;\n    } else if (vy > 0) { // bottom\n      if (y0 >= this.ymax) return null;\n      if ((c = (this.ymax - y0) / vy) < t) y = this.ymax, x = x0 + (t = c) * vx;\n    }\n    if (vx > 0) { // right\n      if (x0 >= this.xmax) return null;\n      if ((c = (this.xmax - x0) / vx) < t) x = this.xmax, y = y0 + (t = c) * vy;\n    } else if (vx < 0) { // left\n      if (x0 <= this.xmin) return null;\n      if ((c = (this.xmin - x0) / vx) < t) x = this.xmin, y = y0 + (t = c) * vy;\n    }\n    return [x, y];\n  }\n  _edgecode(x, y) {\n    return (x === this.xmin ? 0b0001\n        : x === this.xmax ? 0b0010 : 0b0000)\n        | (y === this.ymin ? 0b0100\n        : y === this.ymax ? 0b1000 : 0b0000);\n  }\n  _regioncode(x, y) {\n    return (x < this.xmin ? 0b0001\n        : x > this.xmax ? 0b0010 : 0b0000)\n        | (y < this.ymin ? 0b0100\n        : y > this.ymax ? 0b1000 : 0b0000);\n  }\n  _simplify(P) {\n    if (P && P.length > 4) {\n      for (let i = 0; i < P.length; i+= 2) {\n        const j = (i + 2) % P.length, k = (i + 4) % P.length;\n        if (P[i] === P[j] && P[j] === P[k] || P[i + 1] === P[j + 1] && P[j + 1] === P[k + 1]) {\n          P.splice(j, 2), i -= 2;\n        }\n      }\n      if (!P.length) P = null;\n    }\n    return P;\n  }\n}\n", "import Delaunator from \"delaunator\";\nimport Path from \"./path.js\";\nimport Polygon from \"./polygon.js\";\nimport Voronoi from \"./voronoi.js\";\n\nconst tau = 2 * Math.PI, pow = Math.pow;\n\nfunction pointX(p) {\n  return p[0];\n}\n\nfunction pointY(p) {\n  return p[1];\n}\n\n// A triangulation is collinear if all its triangles have a non-null area\nfunction collinear(d) {\n  const {triangles, coords} = d;\n  for (let i = 0; i < triangles.length; i += 3) {\n    const a = 2 * triangles[i],\n          b = 2 * triangles[i + 1],\n          c = 2 * triangles[i + 2],\n          cross = (coords[c] - coords[a]) * (coords[b + 1] - coords[a + 1])\n                - (coords[b] - coords[a]) * (coords[c + 1] - coords[a + 1]);\n    if (cross > 1e-10) return false;\n  }\n  return true;\n}\n\nfunction jitter(x, y, r) {\n  return [x + Math.sin(x + y) * r, y + Math.cos(x - y) * r];\n}\n\nexport default class Delaunay {\n  static from(points, fx = pointX, fy = pointY, that) {\n    return new Delaunay(\"length\" in points\n        ? flatArray(points, fx, fy, that)\n        : Float64Array.from(flatIterable(points, fx, fy, that)));\n  }\n  constructor(points) {\n    this._delaunator = new Delaunator(points);\n    this.inedges = new Int32Array(points.length / 2);\n    this._hullIndex = new Int32Array(points.length / 2);\n    this.points = this._delaunator.coords;\n    this._init();\n  }\n  update() {\n    this._delaunator.update();\n    this._init();\n    return this;\n  }\n  _init() {\n    const d = this._delaunator, points = this.points;\n\n    // check for collinear\n    if (d.hull && d.hull.length > 2 && collinear(d)) {\n      this.collinear = Int32Array.from({length: points.length/2}, (_,i) => i)\n        .sort((i, j) => points[2 * i] - points[2 * j] || points[2 * i + 1] - points[2 * j + 1]); // for exact neighbors\n      const e = this.collinear[0], f = this.collinear[this.collinear.length - 1],\n        bounds = [ points[2 * e], points[2 * e + 1], points[2 * f], points[2 * f + 1] ],\n        r = 1e-8 * Math.hypot(bounds[3] - bounds[1], bounds[2] - bounds[0]);\n      for (let i = 0, n = points.length / 2; i < n; ++i) {\n        const p = jitter(points[2 * i], points[2 * i + 1], r);\n        points[2 * i] = p[0];\n        points[2 * i + 1] = p[1];\n      }\n      this._delaunator = new Delaunator(points);\n    } else {\n      delete this.collinear;\n    }\n\n    const halfedges = this.halfedges = this._delaunator.halfedges;\n    const hull = this.hull = this._delaunator.hull;\n    const triangles = this.triangles = this._delaunator.triangles;\n    const inedges = this.inedges.fill(-1);\n    const hullIndex = this._hullIndex.fill(-1);\n\n    // Compute an index from each point to an (arbitrary) incoming halfedge\n    // Used to give the first neighbor of each point; for this reason,\n    // on the hull we give priority to exterior halfedges\n    for (let e = 0, n = halfedges.length; e < n; ++e) {\n      const p = triangles[e % 3 === 2 ? e - 2 : e + 1];\n      if (halfedges[e] === -1 || inedges[p] === -1) inedges[p] = e;\n    }\n    for (let i = 0, n = hull.length; i < n; ++i) {\n      hullIndex[hull[i]] = i;\n    }\n\n    // degenerate case: 1 or 2 (distinct) points\n    if (hull.length <= 2 && hull.length > 0) {\n      this.triangles = new Int32Array(3).fill(-1);\n      this.halfedges = new Int32Array(3).fill(-1);\n      this.triangles[0] = hull[0];\n      inedges[hull[0]] = 1;\n      if (hull.length === 2) {\n        inedges[hull[1]] = 0;\n        this.triangles[1] = hull[1];\n        this.triangles[2] = hull[1];\n      }\n    }\n  }\n  voronoi(bounds) {\n    return new Voronoi(this, bounds);\n  }\n  *neighbors(i) {\n    const {inedges, hull, _hullIndex, halfedges, triangles, collinear} = this;\n\n    // degenerate case with several collinear points\n    if (collinear) {\n      const l = collinear.indexOf(i);\n      if (l > 0) yield collinear[l - 1];\n      if (l < collinear.length - 1) yield collinear[l + 1];\n      return;\n    }\n\n    const e0 = inedges[i];\n    if (e0 === -1) return; // coincident point\n    let e = e0, p0 = -1;\n    do {\n      yield p0 = triangles[e];\n      e = e % 3 === 2 ? e - 2 : e + 1;\n      if (triangles[e] !== i) return; // bad triangulation\n      e = halfedges[e];\n      if (e === -1) {\n        const p = hull[(_hullIndex[i] + 1) % hull.length];\n        if (p !== p0) yield p;\n        return;\n      }\n    } while (e !== e0);\n  }\n  find(x, y, i = 0) {\n    if ((x = +x, x !== x) || (y = +y, y !== y)) return -1;\n    const i0 = i;\n    let c;\n    while ((c = this._step(i, x, y)) >= 0 && c !== i && c !== i0) i = c;\n    return c;\n  }\n  _step(i, x, y) {\n    const {inedges, hull, _hullIndex, halfedges, triangles, points} = this;\n    if (inedges[i] === -1 || !points.length) return (i + 1) % (points.length >> 1);\n    let c = i;\n    let dc = pow(x - points[i * 2], 2) + pow(y - points[i * 2 + 1], 2);\n    const e0 = inedges[i];\n    let e = e0;\n    do {\n      let t = triangles[e];\n      const dt = pow(x - points[t * 2], 2) + pow(y - points[t * 2 + 1], 2);\n      if (dt < dc) dc = dt, c = t;\n      e = e % 3 === 2 ? e - 2 : e + 1;\n      if (triangles[e] !== i) break; // bad triangulation\n      e = halfedges[e];\n      if (e === -1) {\n        e = hull[(_hullIndex[i] + 1) % hull.length];\n        if (e !== t) {\n          if (pow(x - points[e * 2], 2) + pow(y - points[e * 2 + 1], 2) < dc) return e;\n        }\n        break;\n      }\n    } while (e !== e0);\n    return c;\n  }\n  render(context) {\n    const buffer = context == null ? context = new Path : undefined;\n    const {points, halfedges, triangles} = this;\n    for (let i = 0, n = halfedges.length; i < n; ++i) {\n      const j = halfedges[i];\n      if (j < i) continue;\n      const ti = triangles[i] * 2;\n      const tj = triangles[j] * 2;\n      context.moveTo(points[ti], points[ti + 1]);\n      context.lineTo(points[tj], points[tj + 1]);\n    }\n    this.renderHull(context);\n    return buffer && buffer.value();\n  }\n  renderPoints(context, r) {\n    if (r === undefined && (!context || typeof context.moveTo !== \"function\")) r = context, context = null;\n    r = r == undefined ? 2 : +r;\n    const buffer = context == null ? context = new Path : undefined;\n    const {points} = this;\n    for (let i = 0, n = points.length; i < n; i += 2) {\n      const x = points[i], y = points[i + 1];\n      context.moveTo(x + r, y);\n      context.arc(x, y, r, 0, tau);\n    }\n    return buffer && buffer.value();\n  }\n  renderHull(context) {\n    const buffer = context == null ? context = new Path : undefined;\n    const {hull, points} = this;\n    const h = hull[0] * 2, n = hull.length;\n    context.moveTo(points[h], points[h + 1]);\n    for (let i = 1; i < n; ++i) {\n      const h = 2 * hull[i];\n      context.lineTo(points[h], points[h + 1]);\n    }\n    context.closePath();\n    return buffer && buffer.value();\n  }\n  hullPolygon() {\n    const polygon = new Polygon;\n    this.renderHull(polygon);\n    return polygon.value();\n  }\n  renderTriangle(i, context) {\n    const buffer = context == null ? context = new Path : undefined;\n    const {points, triangles} = this;\n    const t0 = triangles[i *= 3] * 2;\n    const t1 = triangles[i + 1] * 2;\n    const t2 = triangles[i + 2] * 2;\n    context.moveTo(points[t0], points[t0 + 1]);\n    context.lineTo(points[t1], points[t1 + 1]);\n    context.lineTo(points[t2], points[t2 + 1]);\n    context.closePath();\n    return buffer && buffer.value();\n  }\n  *trianglePolygons() {\n    const {triangles} = this;\n    for (let i = 0, n = triangles.length / 3; i < n; ++i) {\n      yield this.trianglePolygon(i);\n    }\n  }\n  trianglePolygon(i) {\n    const polygon = new Polygon;\n    this.renderTriangle(i, polygon);\n    return polygon.value();\n  }\n}\n\nfunction flatArray(points, fx, fy, that) {\n  const n = points.length;\n  const array = new Float64Array(n * 2);\n  for (let i = 0; i < n; ++i) {\n    const p = points[i];\n    array[i * 2] = fx.call(that, p, i, points);\n    array[i * 2 + 1] = fy.call(that, p, i, points);\n  }\n  return array;\n}\n\nfunction* flatIterable(points, fx, fy, that) {\n  let i = 0;\n  for (const p of points) {\n    yield fx.call(that, p, i, points);\n    yield fy.call(that, p, i, points);\n    ++i;\n  }\n}\n", "export const epsilon = 1e-6;\nexport const epsilon2 = 1e-12;\nexport const pi = Math.PI;\nexport const halfPi = pi / 2;\nexport const quarterPi = pi / 4;\nexport const tau = pi * 2;\n\nexport const degrees = 180 / pi;\nexport const radians = pi / 180;\n\nexport const abs = Math.abs;\nexport const atan = Math.atan;\nexport const atan2 = Math.atan2;\nexport const cos = Math.cos;\nexport const ceil = Math.ceil;\nexport const exp = Math.exp;\nexport const floor = Math.floor;\nexport const log = Math.log;\nexport const max = Math.max;\nexport const min = Math.min;\nexport const pow = Math.pow;\nexport const sin = Math.sin;\nexport const sign =\n  Math.sign ||\n  function (x) {\n    return x > 0 ? 1 : x < 0 ? -1 : 0;\n  };\nexport const sqrt = Math.sqrt;\nexport const tan = Math.tan;\n\nexport function acos(x) {\n  return x > 1 ? 0 : x < -1 ? pi : Math.acos(x);\n}\n\nexport function asin(x) {\n  return x > 1 ? halfPi : x < -1 ? -halfPi : Math.asin(x);\n}\n\nexport function haversin(x) {\n  return (x = sin(x / 2)) * x;\n}\n", "import { asin, atan2, cos, sin, sqrt } from \"./math.js\";\n\nexport function spherical(cartesian) {\n  return [atan2(cartesian[1], cartesian[0]), asin(cartesian[2])];\n}\n\nexport function cartesian(spherical) {\n  const lambda = spherical[0],\n    phi = spherical[1],\n    cosPhi = cos(phi);\n  return [cosPhi * cos(lambda), cosPhi * sin(lambda), sin(phi)];\n}\n\nexport function cartesianDot(a, b) {\n  return a[0] * b[0] + a[1] * b[1] + a[2] * b[2];\n}\n\nexport function cartesianCross(a, b) {\n  return [\n    a[1] * b[2] - a[2] * b[1],\n    a[2] * b[0] - a[0] * b[2],\n    a[0] * b[1] - a[1] * b[0],\n  ];\n}\n\n// TODO return a\nexport function cartesianAddInPlace(a, b) {\n  (a[0] += b[0]), (a[1] += b[1]), (a[2] += b[2]);\n}\n\nexport function cartesianAdd(a, b) {\n  return [a[0] + b[0], a[1] + b[1], a[2] + b[2]];\n}\n\nexport function cartesianScale(vector, k) {\n  return [vector[0] * k, vector[1] * k, vector[2] * k];\n}\n\n// TODO return d\nexport function cartesianNormalizeInPlace(d) {\n  var l = sqrt(d[0] * d[0] + d[1] * d[1] + d[2] * d[2]);\n  (d[0] /= l), (d[1] /= l), (d[2] /= l);\n}\n\nexport function cartesianNormalize(d) {\n  var l = sqrt(d[0] * d[0] + d[1] * d[1] + d[2] * d[2]);\n  return [d[0] / l, d[1] / l, d[2] / l];\n}\n", "import { Delaunay } from \"d3-delaunay\";\nimport { geoRotation, geoStereographic } from \"d3-geo\";\nimport { extent } from \"d3-array\";\nimport {\n  asin,\n  atan2,\n  cos,\n  degrees,\n  max,\n  min,\n  radians,\n  sign,\n  sin,\n  sqrt,\n} from \"./math.js\";\nimport {\n  cartesianNormalize as normalize,\n  cartesian<PERSON>ross as cross,\n  cartesianDot as dot,\n  cartesianAdd,\n} from \"./cartesian.js\";\n\n// Converts 3D Cartesian to spherical coordinates (degrees).\nfunction spherical(cartesian) {\n  return [\n    atan2(cartesian[1], cartesian[0]) * degrees,\n    asin(max(-1, min(1, cartesian[2]))) * degrees,\n  ];\n}\n\n// Converts spherical coordinates (degrees) to 3D Cartesian.\nfunction cartesian(coordinates) {\n  const lambda = coordinates[0] * radians,\n    phi = coordinates[1] * radians,\n    cosphi = cos(phi);\n  return [cosphi * cos(lambda), cosphi * sin(lambda), sin(phi)];\n}\n\n// Spherical excess of a triangle (in spherical coordinates)\nexport function excess(triangle) {\n  triangle = triangle.map((p) => cartesian(p));\n  return dot(triangle[0], cross(triangle[2], triangle[1]));\n}\n\nexport function geoDelaunay(points) {\n  const delaunay = geo_delaunay_from(points),\n    triangles = geo_triangles(delaunay),\n    edges = geo_edges(triangles, points),\n    neighbors = geo_neighbors(triangles, points.length),\n    find = geo_find(neighbors, points),\n    // Voronoi ; could take a center function as an argument\n    circumcenters = geo_circumcenters(triangles, points),\n    { polygons, centers } = geo_polygons(circumcenters, triangles, points),\n    mesh = geo_mesh(polygons),\n    hull = geo_hull(triangles, points),\n    // Urquhart ; returns a function that takes a distance array as argument.\n    urquhart = geo_urquhart(edges, triangles);\n  return {\n    delaunay,\n    edges,\n    triangles,\n    centers,\n    neighbors,\n    polygons,\n    mesh,\n    hull,\n    urquhart,\n    find,\n  };\n}\n\nfunction geo_find(neighbors, points) {\n  function distance2(a, b) {\n    let x = a[0] - b[0],\n      y = a[1] - b[1],\n      z = a[2] - b[2];\n    return x * x + y * y + z * z;\n  }\n\n  return function find(x, y, next) {\n    if (next === undefined) next = 0;\n    let cell,\n      dist,\n      found = next;\n    const xyz = cartesian([x, y]);\n    do {\n      cell = next;\n      next = null;\n      dist = distance2(xyz, cartesian(points[cell]));\n      neighbors[cell].forEach((i) => {\n        let ndist = distance2(xyz, cartesian(points[i]));\n        if (ndist < dist) {\n          dist = ndist;\n          next = i;\n          found = i;\n          return;\n        }\n      });\n    } while (next !== null);\n\n    return found;\n  };\n}\n\nfunction geo_delaunay_from(points) {\n  if (points.length < 2) return {};\n\n  // find a valid point to send to infinity\n  let pivot = 0;\n  while (isNaN(points[pivot][0] + points[pivot][1]) && pivot++ < points.length);\n\n  const r = geoRotation(points[pivot]),\n    projection = geoStereographic()\n      .translate([0, 0])\n      .scale(1)\n      .rotate(r.invert([180, 0]));\n  points = points.map(projection);\n\n  const zeros = [];\n  let max2 = 1;\n  for (let i = 0, n = points.length; i < n; i++) {\n    let m = points[i][0] ** 2 + points[i][1] ** 2;\n    if (!isFinite(m) || m > 1e32) zeros.push(i);\n    else if (m > max2) max2 = m;\n  }\n\n  const FAR = 1e6 * sqrt(max2);\n\n  zeros.forEach((i) => (points[i] = [FAR, 0]));\n\n  // Add infinite horizon points\n  points.push([0, FAR]);\n  points.push([-FAR, 0]);\n  points.push([0, -FAR]);\n\n  const delaunay = Delaunay.from(points);\n\n  delaunay.projection = projection;\n\n  // clean up the triangulation\n  const { triangles, halfedges, inedges } = delaunay;\n  const degenerate = [];\n  for (let i = 0, l = halfedges.length; i < l; i++) {\n    if (halfedges[i] < 0) {\n      const j = i % 3 == 2 ? i - 2 : i + 1;\n      const k = i % 3 == 0 ? i + 2 : i - 1;\n      const a = halfedges[j];\n      const b = halfedges[k];\n      halfedges[a] = b;\n      halfedges[b] = a;\n      halfedges[j] = halfedges[k] = -1;\n      triangles[i] = triangles[j] = triangles[k] = pivot;\n      inedges[triangles[a]] = a % 3 == 0 ? a + 2 : a - 1;\n      inedges[triangles[b]] = b % 3 == 0 ? b + 2 : b - 1;\n      degenerate.push(Math.min(i, j, k));\n      i += 2 - (i % 3);\n    } else if (triangles[i] > points.length - 3 - 1) {\n      triangles[i] = pivot;\n    }\n  }\n\n  // there should always be 4 degenerate triangles\n  // console.warn(degenerate);\n  return delaunay;\n}\n\nfunction geo_edges(triangles, points) {\n  const _index = new Set();\n  if (points.length === 2) return [[0, 1]];\n  triangles.forEach((tri) => {\n    if (tri[0] === tri[1]) return;\n    if (excess(tri.map((i) => points[i])) < 0) return;\n    for (let i = 0, j; i < 3; i++) {\n      j = (i + 1) % 3;\n      _index.add(extent([tri[i], tri[j]]).join(\"-\"));\n    }\n  });\n  return Array.from(_index, (d) => d.split(\"-\").map(Number));\n}\n\nfunction geo_triangles(delaunay) {\n  const { triangles } = delaunay;\n  if (!triangles) return [];\n\n  const geo_triangles = [];\n  for (let i = 0, n = triangles.length / 3; i < n; i++) {\n    const a = triangles[3 * i],\n      b = triangles[3 * i + 1],\n      c = triangles[3 * i + 2];\n    if (a !== b && b !== c) {\n      geo_triangles.push([a, c, b]);\n    }\n  }\n  return geo_triangles;\n}\n\nfunction geo_circumcenters(triangles, points) {\n  // if (!use_centroids) {\n  return triangles.map((tri) => {\n    const c = tri.map((i) => points[i]).map(cartesian),\n      V = cartesianAdd(\n        cartesianAdd(cross(c[1], c[0]), cross(c[2], c[1])),\n        cross(c[0], c[2])\n      );\n    return spherical(normalize(V));\n  });\n  /*} else {\n    return triangles.map(tri => {\n      return d3.geoCentroid({\n        type: \"MultiPoint\",\n        coordinates: tri.map(i => points[i])\n      });\n    });\n  }*/\n}\n\nfunction geo_neighbors(triangles, npoints) {\n  const neighbors = [];\n  triangles.forEach((tri) => {\n    for (let j = 0; j < 3; j++) {\n      const a = tri[j],\n        b = tri[(j + 1) % 3];\n      neighbors[a] = neighbors[a] || [];\n      neighbors[a].push(b);\n    }\n  });\n\n  // degenerate cases\n  if (triangles.length === 0) {\n    if (npoints === 2) (neighbors[0] = [1]), (neighbors[1] = [0]);\n    else if (npoints === 1) neighbors[0] = [];\n  }\n\n  return neighbors;\n}\n\nfunction geo_polygons(circumcenters, triangles, points) {\n  const polygons = [];\n\n  const centers = circumcenters.slice();\n\n  if (triangles.length === 0) {\n    if (points.length < 2) return { polygons, centers };\n    if (points.length === 2) {\n      // two hemispheres\n      const a = cartesian(points[0]),\n        b = cartesian(points[1]),\n        m = normalize(cartesianAdd(a, b)),\n        d = normalize(cross(a, b)),\n        c = cross(m, d);\n      const poly = [\n        m,\n        cross(m, c),\n        cross(cross(m, c), c),\n        cross(cross(cross(m, c), c), c),\n      ]\n        .map(spherical)\n        .map(supplement);\n      return (\n        polygons.push(poly),\n        polygons.push(poly.slice().reverse()),\n        { polygons, centers }\n      );\n    }\n  }\n\n  triangles.forEach((tri, t) => {\n    for (let j = 0; j < 3; j++) {\n      const a = tri[j],\n        b = tri[(j + 1) % 3],\n        c = tri[(j + 2) % 3];\n      polygons[a] = polygons[a] || [];\n      polygons[a].push([b, c, t, [a, b, c]]);\n    }\n  });\n\n  // reorder each polygon\n  const reordered = polygons.map((poly) => {\n    const p = [poly[0][2]]; // t\n    let k = poly[0][1]; // k = c\n    for (let i = 1; i < poly.length; i++) {\n      // look for b = k\n      for (let j = 0; j < poly.length; j++) {\n        if (poly[j][0] == k) {\n          k = poly[j][1];\n          p.push(poly[j][2]);\n          break;\n        }\n      }\n    }\n\n    if (p.length > 2) {\n      return p;\n    } else if (p.length == 2) {\n      const R0 = o_midpoint(\n          points[poly[0][3][0]],\n          points[poly[0][3][1]],\n          centers[p[0]]\n        ),\n        R1 = o_midpoint(\n          points[poly[0][3][2]],\n          points[poly[0][3][0]],\n          centers[p[0]]\n        );\n      const i0 = supplement(R0),\n        i1 = supplement(R1);\n      return [p[0], i1, p[1], i0];\n    }\n  });\n\n  function supplement(point) {\n    let f = -1;\n    centers.slice(triangles.length, Infinity).forEach((p, i) => {\n      if (p[0] === point[0] && p[1] === point[1]) f = i + triangles.length;\n    });\n    if (f < 0) (f = centers.length), centers.push(point);\n    return f;\n  }\n\n  return { polygons: reordered, centers };\n}\n\nfunction o_midpoint(a, b, c) {\n  a = cartesian(a);\n  b = cartesian(b);\n  c = cartesian(c);\n  const s = sign(dot(cross(b, a), c));\n  return spherical(normalize(cartesianAdd(a, b)).map((d) => s * d));\n}\n\nfunction geo_mesh(polygons) {\n  const mesh = [];\n  polygons.forEach((poly) => {\n    if (!poly) return;\n    let p = poly[poly.length - 1];\n    for (let q of poly) {\n      if (q > p) mesh.push([p, q]);\n      p = q;\n    }\n  });\n  return mesh;\n}\n\nfunction geo_urquhart(edges, triangles) {\n  return function (distances) {\n    const _lengths = new Map(),\n      _urquhart = new Map();\n    edges.forEach((edge, i) => {\n      const u = edge.join(\"-\");\n      _lengths.set(u, distances[i]);\n      _urquhart.set(u, true);\n    });\n\n    triangles.forEach((tri) => {\n      let l = 0,\n        remove = -1;\n      for (let j = 0; j < 3; j++) {\n        let u = extent([tri[j], tri[(j + 1) % 3]]).join(\"-\");\n        if (_lengths.get(u) > l) {\n          l = _lengths.get(u);\n          remove = u;\n        }\n      }\n      _urquhart.set(remove, false);\n    });\n\n    return edges.map((edge) => _urquhart.get(edge.join(\"-\")));\n  };\n}\n\nfunction geo_hull(triangles, points) {\n  const _hull = new Set(),\n    hull = [];\n  triangles.map((tri) => {\n    if (excess(tri.map((i) => points[i > points.length ? 0 : i])) > 1e-12)\n      return;\n    for (let i = 0; i < 3; i++) {\n      let e = [tri[i], tri[(i + 1) % 3]],\n        code = `${e[0]}-${e[1]}`;\n      if (_hull.has(code)) _hull.delete(code);\n      else _hull.add(`${e[1]}-${e[0]}`);\n    }\n  });\n\n  const _index = new Map();\n  let start;\n  _hull.forEach((e) => {\n    e = e.split(\"-\").map(Number);\n    _index.set(e[0], e[1]);\n    start = e[0];\n  });\n\n  if (start === undefined) return hull;\n\n  let next = start;\n  do {\n    hull.push(next);\n    let n = _index.get(next);\n    _index.set(next, -1);\n    next = n;\n  } while (next > -1 && next !== start);\n\n  return hull;\n}\n", "import { geoCentroid, geoDistance } from \"d3-geo\";\nimport { geoDelaunay, excess } from \"./delaunay.js\";\n\nexport function geoVoronoi(data) {\n  const v = function (data) {\n    v.delaunay = null;\n    v._data = data;\n\n    if (typeof v._data === \"object\" && v._data.type === \"FeatureCollection\") {\n      v._data = v._data.features;\n    }\n    if (typeof v._data === \"object\") {\n      const temp = v._data\n        .map((d) => [v._vx(d), v._vy(d), d])\n        .filter((d) => isFinite(d[0] + d[1]));\n      v.points = temp.map((d) => [d[0], d[1]]);\n      v.valid = temp.map((d) => d[2]);\n      v.delaunay = geoDelaunay(v.points);\n    }\n    return v;\n  };\n\n  v._vx = function (d) {\n    if (typeof d == \"object\" && \"type\" in d) {\n      return geoCentroid(d)[0];\n    }\n    if (0 in d) return d[0];\n  };\n  v._vy = function (d) {\n    if (typeof d == \"object\" && \"type\" in d) {\n      return geoCentroid(d)[1];\n    }\n    if (1 in d) return d[1];\n  };\n\n  v.x = function (f) {\n    if (!f) return v._vx;\n    v._vx = f;\n    return v;\n  };\n  v.y = function (f) {\n    if (!f) return v._vy;\n    v._vy = f;\n    return v;\n  };\n\n  v.polygons = function (data) {\n    if (data !== undefined) {\n      v(data);\n    }\n\n    if (!v.delaunay) return false;\n    const coll = {\n      type: \"FeatureCollection\",\n      features: [],\n    };\n    if (v.valid.length === 0) return coll;\n    v.delaunay.polygons.forEach((poly, i) =>\n      coll.features.push({\n        type: \"Feature\",\n        geometry: !poly\n          ? null\n          : {\n              type: \"Polygon\",\n              coordinates: [\n                [...poly, poly[0]].map((i) => v.delaunay.centers[i]),\n              ],\n            },\n        properties: {\n          site: v.valid[i],\n          sitecoordinates: v.points[i],\n          neighbours: v.delaunay.neighbors[i], // not part of the public API\n        },\n      })\n    );\n    if (v.valid.length === 1)\n      coll.features.push({\n        type: \"Feature\",\n        geometry: { type: \"Sphere\" },\n        properties: {\n          site: v.valid[0],\n          sitecoordinates: v.points[0],\n          neighbours: [],\n        },\n      });\n    return coll;\n  };\n\n  v.triangles = function (data) {\n    if (data !== undefined) {\n      v(data);\n    }\n    if (!v.delaunay) return false;\n\n    return {\n      type: \"FeatureCollection\",\n      features: v.delaunay.triangles\n        .map((tri, index) => {\n          tri = tri.map((i) => v.points[i]);\n          tri.center = v.delaunay.centers[index];\n          return tri;\n        })\n        .filter((tri) => excess(tri) > 0)\n        .map((tri) => ({\n          type: \"Feature\",\n          properties: {\n            circumcenter: tri.center,\n          },\n          geometry: {\n            type: \"Polygon\",\n            coordinates: [[...tri, tri[0]]],\n          },\n        })),\n    };\n  };\n\n  v.links = function (data) {\n    if (data !== undefined) {\n      v(data);\n    }\n    if (!v.delaunay) return false;\n    const _distances = v.delaunay.edges.map((e) =>\n        geoDistance(v.points[e[0]], v.points[e[1]])\n      ),\n      _urquart = v.delaunay.urquhart(_distances);\n    return {\n      type: \"FeatureCollection\",\n      features: v.delaunay.edges.map((e, i) => ({\n        type: \"Feature\",\n        properties: {\n          source: v.valid[e[0]],\n          target: v.valid[e[1]],\n          length: _distances[i],\n          urquhart: !!_urquart[i],\n        },\n        geometry: {\n          type: \"LineString\",\n          coordinates: [v.points[e[0]], v.points[e[1]]],\n        },\n      })),\n    };\n  };\n\n  v.mesh = function (data) {\n    if (data !== undefined) {\n      v(data);\n    }\n    if (!v.delaunay) return false;\n    return {\n      type: \"MultiLineString\",\n      coordinates: v.delaunay.edges.map((e) => [\n        v.points[e[0]],\n        v.points[e[1]],\n      ]),\n    };\n  };\n\n  v.cellMesh = function (data) {\n    if (data !== undefined) {\n      v(data);\n    }\n    if (!v.delaunay) return false;\n    const { centers, polygons } = v.delaunay;\n    const coordinates = [];\n    for (const p of polygons) {\n      if (!p) continue;\n      for (\n        let n = p.length, p0 = p[n - 1], p1 = p[0], i = 0;\n        i < n;\n        p0 = p1, p1 = p[++i]\n      ) {\n        if (p1 > p0) {\n          coordinates.push([centers[p0], centers[p1]]);\n        }\n      }\n    }\n    return {\n      type: \"MultiLineString\",\n      coordinates,\n    };\n  };\n\n  v._found = undefined;\n  v.find = function (x, y, radius) {\n    v._found = v.delaunay.find(x, y, v._found);\n    if (!radius || geoDistance([x, y], v.points[v._found]) < radius)\n      return v._found;\n  };\n\n  v.hull = function (data) {\n    if (data !== undefined) {\n      v(data);\n    }\n    const hull = v.delaunay.hull,\n      points = v.points;\n    return hull.length === 0\n      ? null\n      : {\n          type: \"Polygon\",\n          coordinates: [[...hull.map((i) => points[i]), points[hull[0]]]],\n        };\n  };\n\n  return data ? v(data) : v;\n}\n", "export function initRange(domain, range) {\n  switch (arguments.length) {\n    case 0: break;\n    case 1: this.range(domain); break;\n    default: this.range(range).domain(domain); break;\n  }\n  return this;\n}\n\nexport function initInterpolator(domain, interpolator) {\n  switch (arguments.length) {\n    case 0: break;\n    case 1: {\n      if (typeof domain === \"function\") this.interpolator(domain);\n      else this.range(domain);\n      break;\n    }\n    default: {\n      this.domain(domain);\n      if (typeof interpolator === \"function\") this.interpolator(interpolator);\n      else this.range(interpolator);\n      break;\n    }\n  }\n  return this;\n}\n", "export default function(constructor, factory, prototype) {\n  constructor.prototype = factory.prototype = prototype;\n  prototype.constructor = constructor;\n}\n\nexport function extend(parent, definition) {\n  var prototype = Object.create(parent.prototype);\n  for (var key in definition) prototype[key] = definition[key];\n  return prototype;\n}\n", "import define, {extend} from \"./define.js\";\n\nexport function Color() {}\n\nexport var darker = 0.7;\nexport var brighter = 1 / darker;\n\nvar reI = \"\\\\s*([+-]?\\\\d+)\\\\s*\",\n    reN = \"\\\\s*([+-]?(?:\\\\d*\\\\.)?\\\\d+(?:[eE][+-]?\\\\d+)?)\\\\s*\",\n    reP = \"\\\\s*([+-]?(?:\\\\d*\\\\.)?\\\\d+(?:[eE][+-]?\\\\d+)?)%\\\\s*\",\n    reHex = /^#([0-9a-f]{3,8})$/,\n    reRgbInteger = new RegExp(`^rgb\\\\(${reI},${reI},${reI}\\\\)$`),\n    reRgbPercent = new RegExp(`^rgb\\\\(${reP},${reP},${reP}\\\\)$`),\n    reRgbaInteger = new RegExp(`^rgba\\\\(${reI},${reI},${reI},${reN}\\\\)$`),\n    reRgbaPercent = new RegExp(`^rgba\\\\(${reP},${reP},${reP},${reN}\\\\)$`),\n    reHslPercent = new RegExp(`^hsl\\\\(${reN},${reP},${reP}\\\\)$`),\n    reHslaPercent = new RegExp(`^hsla\\\\(${reN},${reP},${reP},${reN}\\\\)$`);\n\nvar named = {\n  aliceblue: 0xf0f8ff,\n  antiquewhite: 0xfaebd7,\n  aqua: 0x00ffff,\n  aquamarine: 0x7fffd4,\n  azure: 0xf0ffff,\n  beige: 0xf5f5dc,\n  bisque: 0xffe4c4,\n  black: 0x000000,\n  blanchedalmond: 0xffebcd,\n  blue: 0x0000ff,\n  blueviolet: 0x8a2be2,\n  brown: 0xa52a2a,\n  burlywood: 0xdeb887,\n  cadetblue: 0x5f9ea0,\n  chartreuse: 0x7fff00,\n  chocolate: 0xd2691e,\n  coral: 0xff7f50,\n  cornflowerblue: 0x6495ed,\n  cornsilk: 0xfff8dc,\n  crimson: 0xdc143c,\n  cyan: 0x00ffff,\n  darkblue: 0x00008b,\n  darkcyan: 0x008b8b,\n  darkgoldenrod: 0xb8860b,\n  darkgray: 0xa9a9a9,\n  darkgreen: 0x006400,\n  darkgrey: 0xa9a9a9,\n  darkkhaki: 0xbdb76b,\n  darkmagenta: 0x8b008b,\n  darkolivegreen: 0x556b2f,\n  darkorange: 0xff8c00,\n  darkorchid: 0x9932cc,\n  darkred: 0x8b0000,\n  darksalmon: 0xe9967a,\n  darkseagreen: 0x8fbc8f,\n  darkslateblue: 0x483d8b,\n  darkslategray: 0x2f4f4f,\n  darkslategrey: 0x2f4f4f,\n  darkturquoise: 0x00ced1,\n  darkviolet: 0x9400d3,\n  deeppink: 0xff1493,\n  deepskyblue: 0x00bfff,\n  dimgray: 0x696969,\n  dimgrey: 0x696969,\n  dodgerblue: 0x1e90ff,\n  firebrick: 0xb22222,\n  floralwhite: 0xfffaf0,\n  forestgreen: 0x228b22,\n  fuchsia: 0xff00ff,\n  gainsboro: 0xdcdcdc,\n  ghostwhite: 0xf8f8ff,\n  gold: 0xffd700,\n  goldenrod: 0xdaa520,\n  gray: 0x808080,\n  green: 0x008000,\n  greenyellow: 0xadff2f,\n  grey: 0x808080,\n  honeydew: 0xf0fff0,\n  hotpink: 0xff69b4,\n  indianred: 0xcd5c5c,\n  indigo: 0x4b0082,\n  ivory: 0xfffff0,\n  khaki: 0xf0e68c,\n  lavender: 0xe6e6fa,\n  lavenderblush: 0xfff0f5,\n  lawngreen: 0x7cfc00,\n  lemonchiffon: 0xfffacd,\n  lightblue: 0xadd8e6,\n  lightcoral: 0xf08080,\n  lightcyan: 0xe0ffff,\n  lightgoldenrodyellow: 0xfafad2,\n  lightgray: 0xd3d3d3,\n  lightgreen: 0x90ee90,\n  lightgrey: 0xd3d3d3,\n  lightpink: 0xffb6c1,\n  lightsalmon: 0xffa07a,\n  lightseagreen: 0x20b2aa,\n  lightskyblue: 0x87cefa,\n  lightslategray: 0x778899,\n  lightslategrey: 0x778899,\n  lightsteelblue: 0xb0c4de,\n  lightyellow: 0xffffe0,\n  lime: 0x00ff00,\n  limegreen: 0x32cd32,\n  linen: 0xfaf0e6,\n  magenta: 0xff00ff,\n  maroon: 0x800000,\n  mediumaquamarine: 0x66cdaa,\n  mediumblue: 0x0000cd,\n  mediumorchid: 0xba55d3,\n  mediumpurple: 0x9370db,\n  mediumseagreen: 0x3cb371,\n  mediumslateblue: 0x7b68ee,\n  mediumspringgreen: 0x00fa9a,\n  mediumturquoise: 0x48d1cc,\n  mediumvioletred: 0xc71585,\n  midnightblue: 0x191970,\n  mintcream: 0xf5fffa,\n  mistyrose: 0xffe4e1,\n  moccasin: 0xffe4b5,\n  navajowhite: 0xffdead,\n  navy: 0x000080,\n  oldlace: 0xfdf5e6,\n  olive: 0x808000,\n  olivedrab: 0x6b8e23,\n  orange: 0xffa500,\n  orangered: 0xff4500,\n  orchid: 0xda70d6,\n  palegoldenrod: 0xeee8aa,\n  palegreen: 0x98fb98,\n  paleturquoise: 0xafeeee,\n  palevioletred: 0xdb7093,\n  papayawhip: 0xffefd5,\n  peachpuff: 0xffdab9,\n  peru: 0xcd853f,\n  pink: 0xffc0cb,\n  plum: 0xdda0dd,\n  powderblue: 0xb0e0e6,\n  purple: 0x800080,\n  rebeccapurple: 0x663399,\n  red: 0xff0000,\n  rosybrown: 0xbc8f8f,\n  royalblue: 0x4169e1,\n  saddlebrown: 0x8b4513,\n  salmon: 0xfa8072,\n  sandybrown: 0xf4a460,\n  seagreen: 0x2e8b57,\n  seashell: 0xfff5ee,\n  sienna: 0xa0522d,\n  silver: 0xc0c0c0,\n  skyblue: 0x87ceeb,\n  slateblue: 0x6a5acd,\n  slategray: 0x708090,\n  slategrey: 0x708090,\n  snow: 0xfffafa,\n  springgreen: 0x00ff7f,\n  steelblue: 0x4682b4,\n  tan: 0xd2b48c,\n  teal: 0x008080,\n  thistle: 0xd8bfd8,\n  tomato: 0xff6347,\n  turquoise: 0x40e0d0,\n  violet: 0xee82ee,\n  wheat: 0xf5deb3,\n  white: 0xffffff,\n  whitesmoke: 0xf5f5f5,\n  yellow: 0xffff00,\n  yellowgreen: 0x9acd32\n};\n\ndefine(Color, color, {\n  copy(channels) {\n    return Object.assign(new this.constructor, this, channels);\n  },\n  displayable() {\n    return this.rgb().displayable();\n  },\n  hex: color_formatHex, // Deprecated! Use color.formatHex.\n  formatHex: color_formatHex,\n  formatHex8: color_formatHex8,\n  formatHsl: color_formatHsl,\n  formatRgb: color_formatRgb,\n  toString: color_formatRgb\n});\n\nfunction color_formatHex() {\n  return this.rgb().formatHex();\n}\n\nfunction color_formatHex8() {\n  return this.rgb().formatHex8();\n}\n\nfunction color_formatHsl() {\n  return hslConvert(this).formatHsl();\n}\n\nfunction color_formatRgb() {\n  return this.rgb().formatRgb();\n}\n\nexport default function color(format) {\n  var m, l;\n  format = (format + \"\").trim().toLowerCase();\n  return (m = reHex.exec(format)) ? (l = m[1].length, m = parseInt(m[1], 16), l === 6 ? rgbn(m) // #ff0000\n      : l === 3 ? new Rgb((m >> 8 & 0xf) | (m >> 4 & 0xf0), (m >> 4 & 0xf) | (m & 0xf0), ((m & 0xf) << 4) | (m & 0xf), 1) // #f00\n      : l === 8 ? rgba(m >> 24 & 0xff, m >> 16 & 0xff, m >> 8 & 0xff, (m & 0xff) / 0xff) // #ff000000\n      : l === 4 ? rgba((m >> 12 & 0xf) | (m >> 8 & 0xf0), (m >> 8 & 0xf) | (m >> 4 & 0xf0), (m >> 4 & 0xf) | (m & 0xf0), (((m & 0xf) << 4) | (m & 0xf)) / 0xff) // #f000\n      : null) // invalid hex\n      : (m = reRgbInteger.exec(format)) ? new Rgb(m[1], m[2], m[3], 1) // rgb(255, 0, 0)\n      : (m = reRgbPercent.exec(format)) ? new Rgb(m[1] * 255 / 100, m[2] * 255 / 100, m[3] * 255 / 100, 1) // rgb(100%, 0%, 0%)\n      : (m = reRgbaInteger.exec(format)) ? rgba(m[1], m[2], m[3], m[4]) // rgba(255, 0, 0, 1)\n      : (m = reRgbaPercent.exec(format)) ? rgba(m[1] * 255 / 100, m[2] * 255 / 100, m[3] * 255 / 100, m[4]) // rgb(100%, 0%, 0%, 1)\n      : (m = reHslPercent.exec(format)) ? hsla(m[1], m[2] / 100, m[3] / 100, 1) // hsl(120, 50%, 50%)\n      : (m = reHslaPercent.exec(format)) ? hsla(m[1], m[2] / 100, m[3] / 100, m[4]) // hsla(120, 50%, 50%, 1)\n      : named.hasOwnProperty(format) ? rgbn(named[format]) // eslint-disable-line no-prototype-builtins\n      : format === \"transparent\" ? new Rgb(NaN, NaN, NaN, 0)\n      : null;\n}\n\nfunction rgbn(n) {\n  return new Rgb(n >> 16 & 0xff, n >> 8 & 0xff, n & 0xff, 1);\n}\n\nfunction rgba(r, g, b, a) {\n  if (a <= 0) r = g = b = NaN;\n  return new Rgb(r, g, b, a);\n}\n\nexport function rgbConvert(o) {\n  if (!(o instanceof Color)) o = color(o);\n  if (!o) return new Rgb;\n  o = o.rgb();\n  return new Rgb(o.r, o.g, o.b, o.opacity);\n}\n\nexport function rgb(r, g, b, opacity) {\n  return arguments.length === 1 ? rgbConvert(r) : new Rgb(r, g, b, opacity == null ? 1 : opacity);\n}\n\nexport function Rgb(r, g, b, opacity) {\n  this.r = +r;\n  this.g = +g;\n  this.b = +b;\n  this.opacity = +opacity;\n}\n\ndefine(Rgb, rgb, extend(Color, {\n  brighter(k) {\n    k = k == null ? brighter : Math.pow(brighter, k);\n    return new Rgb(this.r * k, this.g * k, this.b * k, this.opacity);\n  },\n  darker(k) {\n    k = k == null ? darker : Math.pow(darker, k);\n    return new Rgb(this.r * k, this.g * k, this.b * k, this.opacity);\n  },\n  rgb() {\n    return this;\n  },\n  clamp() {\n    return new Rgb(clampi(this.r), clampi(this.g), clampi(this.b), clampa(this.opacity));\n  },\n  displayable() {\n    return (-0.5 <= this.r && this.r < 255.5)\n        && (-0.5 <= this.g && this.g < 255.5)\n        && (-0.5 <= this.b && this.b < 255.5)\n        && (0 <= this.opacity && this.opacity <= 1);\n  },\n  hex: rgb_formatHex, // Deprecated! Use color.formatHex.\n  formatHex: rgb_formatHex,\n  formatHex8: rgb_formatHex8,\n  formatRgb: rgb_formatRgb,\n  toString: rgb_formatRgb\n}));\n\nfunction rgb_formatHex() {\n  return `#${hex(this.r)}${hex(this.g)}${hex(this.b)}`;\n}\n\nfunction rgb_formatHex8() {\n  return `#${hex(this.r)}${hex(this.g)}${hex(this.b)}${hex((isNaN(this.opacity) ? 1 : this.opacity) * 255)}`;\n}\n\nfunction rgb_formatRgb() {\n  const a = clampa(this.opacity);\n  return `${a === 1 ? \"rgb(\" : \"rgba(\"}${clampi(this.r)}, ${clampi(this.g)}, ${clampi(this.b)}${a === 1 ? \")\" : `, ${a})`}`;\n}\n\nfunction clampa(opacity) {\n  return isNaN(opacity) ? 1 : Math.max(0, Math.min(1, opacity));\n}\n\nfunction clampi(value) {\n  return Math.max(0, Math.min(255, Math.round(value) || 0));\n}\n\nfunction hex(value) {\n  value = clampi(value);\n  return (value < 16 ? \"0\" : \"\") + value.toString(16);\n}\n\nfunction hsla(h, s, l, a) {\n  if (a <= 0) h = s = l = NaN;\n  else if (l <= 0 || l >= 1) h = s = NaN;\n  else if (s <= 0) h = NaN;\n  return new Hsl(h, s, l, a);\n}\n\nexport function hslConvert(o) {\n  if (o instanceof Hsl) return new Hsl(o.h, o.s, o.l, o.opacity);\n  if (!(o instanceof Color)) o = color(o);\n  if (!o) return new Hsl;\n  if (o instanceof Hsl) return o;\n  o = o.rgb();\n  var r = o.r / 255,\n      g = o.g / 255,\n      b = o.b / 255,\n      min = Math.min(r, g, b),\n      max = Math.max(r, g, b),\n      h = NaN,\n      s = max - min,\n      l = (max + min) / 2;\n  if (s) {\n    if (r === max) h = (g - b) / s + (g < b) * 6;\n    else if (g === max) h = (b - r) / s + 2;\n    else h = (r - g) / s + 4;\n    s /= l < 0.5 ? max + min : 2 - max - min;\n    h *= 60;\n  } else {\n    s = l > 0 && l < 1 ? 0 : h;\n  }\n  return new Hsl(h, s, l, o.opacity);\n}\n\nexport function hsl(h, s, l, opacity) {\n  return arguments.length === 1 ? hslConvert(h) : new Hsl(h, s, l, opacity == null ? 1 : opacity);\n}\n\nfunction Hsl(h, s, l, opacity) {\n  this.h = +h;\n  this.s = +s;\n  this.l = +l;\n  this.opacity = +opacity;\n}\n\ndefine(Hsl, hsl, extend(Color, {\n  brighter(k) {\n    k = k == null ? brighter : Math.pow(brighter, k);\n    return new Hsl(this.h, this.s, this.l * k, this.opacity);\n  },\n  darker(k) {\n    k = k == null ? darker : Math.pow(darker, k);\n    return new Hsl(this.h, this.s, this.l * k, this.opacity);\n  },\n  rgb() {\n    var h = this.h % 360 + (this.h < 0) * 360,\n        s = isNaN(h) || isNaN(this.s) ? 0 : this.s,\n        l = this.l,\n        m2 = l + (l < 0.5 ? l : 1 - l) * s,\n        m1 = 2 * l - m2;\n    return new Rgb(\n      hsl2rgb(h >= 240 ? h - 240 : h + 120, m1, m2),\n      hsl2rgb(h, m1, m2),\n      hsl2rgb(h < 120 ? h + 240 : h - 120, m1, m2),\n      this.opacity\n    );\n  },\n  clamp() {\n    return new Hsl(clamph(this.h), clampt(this.s), clampt(this.l), clampa(this.opacity));\n  },\n  displayable() {\n    return (0 <= this.s && this.s <= 1 || isNaN(this.s))\n        && (0 <= this.l && this.l <= 1)\n        && (0 <= this.opacity && this.opacity <= 1);\n  },\n  formatHsl() {\n    const a = clampa(this.opacity);\n    return `${a === 1 ? \"hsl(\" : \"hsla(\"}${clamph(this.h)}, ${clampt(this.s) * 100}%, ${clampt(this.l) * 100}%${a === 1 ? \")\" : `, ${a})`}`;\n  }\n}));\n\nfunction clamph(value) {\n  value = (value || 0) % 360;\n  return value < 0 ? value + 360 : value;\n}\n\nfunction clampt(value) {\n  return Math.max(0, Math.min(1, value || 0));\n}\n\n/* From FvD 13.37, CSS Color Module Level 3 */\nfunction hsl2rgb(h, m1, m2) {\n  return (h < 60 ? m1 + (m2 - m1) * h / 60\n      : h < 180 ? m2\n      : h < 240 ? m1 + (m2 - m1) * (240 - h) / 60\n      : m1) * 255;\n}\n", "export default x => () => x;\n", "import constant from \"./constant.js\";\n\nfunction linear(a, d) {\n  return function(t) {\n    return a + t * d;\n  };\n}\n\nfunction exponential(a, b, y) {\n  return a = Math.pow(a, y), b = Math.pow(b, y) - a, y = 1 / y, function(t) {\n    return Math.pow(a + t * b, y);\n  };\n}\n\nexport function hue(a, b) {\n  var d = b - a;\n  return d ? linear(a, d > 180 || d < -180 ? d - 360 * Math.round(d / 360) : d) : constant(isNaN(a) ? b : a);\n}\n\nexport function gamma(y) {\n  return (y = +y) === 1 ? nogamma : function(a, b) {\n    return b - a ? exponential(a, b, y) : constant(isNaN(a) ? b : a);\n  };\n}\n\nexport default function nogamma(a, b) {\n  var d = b - a;\n  return d ? linear(a, d) : constant(isNaN(a) ? b : a);\n}\n", "import {rgb as colorRgb} from \"d3-color\";\nimport basis from \"./basis.js\";\nimport basisClosed from \"./basisClosed.js\";\nimport nogamma, {gamma} from \"./color.js\";\n\nexport default (function rgbGamma(y) {\n  var color = gamma(y);\n\n  function rgb(start, end) {\n    var r = color((start = colorRgb(start)).r, (end = colorRgb(end)).r),\n        g = color(start.g, end.g),\n        b = color(start.b, end.b),\n        opacity = nogamma(start.opacity, end.opacity);\n    return function(t) {\n      start.r = r(t);\n      start.g = g(t);\n      start.b = b(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  }\n\n  rgb.gamma = rgbGamma;\n\n  return rgb;\n})(1);\n\nfunction rgbSpline(spline) {\n  return function(colors) {\n    var n = colors.length,\n        r = new Array(n),\n        g = new Array(n),\n        b = new Array(n),\n        i, color;\n    for (i = 0; i < n; ++i) {\n      color = colorRgb(colors[i]);\n      r[i] = color.r || 0;\n      g[i] = color.g || 0;\n      b[i] = color.b || 0;\n    }\n    r = spline(r);\n    g = spline(g);\n    b = spline(b);\n    color.opacity = 1;\n    return function(t) {\n      color.r = r(t);\n      color.g = g(t);\n      color.b = b(t);\n      return color + \"\";\n    };\n  };\n}\n\nexport var rgbBasis = rgbSpline(basis);\nexport var rgbBasisClosed = rgbSpline(basisClosed);\n", "export default function(a, b) {\n  if (!b) b = [];\n  var n = a ? Math.min(b.length, a.length) : 0,\n      c = b.slice(),\n      i;\n  return function(t) {\n    for (i = 0; i < n; ++i) c[i] = a[i] * (1 - t) + b[i] * t;\n    return c;\n  };\n}\n\nexport function isNumberArray(x) {\n  return ArrayBuffer.isView(x) && !(x instanceof DataView);\n}\n", "import value from \"./value.js\";\nimport numberArray, {isNumberArray} from \"./numberArray.js\";\n\nexport default function(a, b) {\n  return (isNumberArray(b) ? numberArray : genericArray)(a, b);\n}\n\nexport function genericArray(a, b) {\n  var nb = b ? b.length : 0,\n      na = a ? Math.min(nb, a.length) : 0,\n      x = new Array(na),\n      c = new Array(nb),\n      i;\n\n  for (i = 0; i < na; ++i) x[i] = value(a[i], b[i]);\n  for (; i < nb; ++i) c[i] = b[i];\n\n  return function(t) {\n    for (i = 0; i < na; ++i) c[i] = x[i](t);\n    return c;\n  };\n}\n", "export default function(a, b) {\n  var d = new Date;\n  return a = +a, b = +b, function(t) {\n    return d.setTime(a * (1 - t) + b * t), d;\n  };\n}\n", "export default function(a, b) {\n  return a = +a, b = +b, function(t) {\n    return a * (1 - t) + b * t;\n  };\n}\n", "import value from \"./value.js\";\n\nexport default function(a, b) {\n  var i = {},\n      c = {},\n      k;\n\n  if (a === null || typeof a !== \"object\") a = {};\n  if (b === null || typeof b !== \"object\") b = {};\n\n  for (k in b) {\n    if (k in a) {\n      i[k] = value(a[k], b[k]);\n    } else {\n      c[k] = b[k];\n    }\n  }\n\n  return function(t) {\n    for (k in i) c[k] = i[k](t);\n    return c;\n  };\n}\n", "import number from \"./number.js\";\n\nvar reA = /[-+]?(?:\\d+\\.?\\d*|\\.?\\d+)(?:[eE][-+]?\\d+)?/g,\n    reB = new RegExp(reA.source, \"g\");\n\nfunction zero(b) {\n  return function() {\n    return b;\n  };\n}\n\nfunction one(b) {\n  return function(t) {\n    return b(t) + \"\";\n  };\n}\n\nexport default function(a, b) {\n  var bi = reA.lastIndex = reB.lastIndex = 0, // scan index for next number in b\n      am, // current match in a\n      bm, // current match in b\n      bs, // string preceding current number in b, if any\n      i = -1, // index in s\n      s = [], // string constants and placeholders\n      q = []; // number interpolators\n\n  // Coerce inputs to strings.\n  a = a + \"\", b = b + \"\";\n\n  // Interpolate pairs of numbers in a & b.\n  while ((am = reA.exec(a))\n      && (bm = reB.exec(b))) {\n    if ((bs = bm.index) > bi) { // a string precedes the next number in b\n      bs = b.slice(bi, bs);\n      if (s[i]) s[i] += bs; // coalesce with previous string\n      else s[++i] = bs;\n    }\n    if ((am = am[0]) === (bm = bm[0])) { // numbers in a & b match\n      if (s[i]) s[i] += bm; // coalesce with previous string\n      else s[++i] = bm;\n    } else { // interpolate non-matching numbers\n      s[++i] = null;\n      q.push({i: i, x: number(am, bm)});\n    }\n    bi = reB.lastIndex;\n  }\n\n  // Add remains of b.\n  if (bi < b.length) {\n    bs = b.slice(bi);\n    if (s[i]) s[i] += bs; // coalesce with previous string\n    else s[++i] = bs;\n  }\n\n  // Special optimization for only a single match.\n  // Otherwise, interpolate each of the numbers and rejoin the string.\n  return s.length < 2 ? (q[0]\n      ? one(q[0].x)\n      : zero(b))\n      : (b = q.length, function(t) {\n          for (var i = 0, o; i < b; ++i) s[(o = q[i]).i] = o.x(t);\n          return s.join(\"\");\n        });\n}\n", "import {color} from \"d3-color\";\nimport rgb from \"./rgb.js\";\nimport {genericArray} from \"./array.js\";\nimport date from \"./date.js\";\nimport number from \"./number.js\";\nimport object from \"./object.js\";\nimport string from \"./string.js\";\nimport constant from \"./constant.js\";\nimport numberArray, {isNumberArray} from \"./numberArray.js\";\n\nexport default function(a, b) {\n  var t = typeof b, c;\n  return b == null || t === \"boolean\" ? constant(b)\n      : (t === \"number\" ? number\n      : t === \"string\" ? ((c = color(b)) ? (b = c, rgb) : string)\n      : b instanceof color ? rgb\n      : b instanceof Date ? date\n      : isNumberArray(b) ? numberArray\n      : Array.isArray(b) ? genericArray\n      : typeof b.valueOf !== \"function\" && typeof b.toString !== \"function\" || isNaN(b) ? object\n      : number)(a, b);\n}\n", "export default function(a, b) {\n  return a = +a, b = +b, function(t) {\n    return Math.round(a * (1 - t) + b * t);\n  };\n}\n", "export default function constants(x) {\n  return function() {\n    return x;\n  };\n}\n", "export default function number(x) {\n  return +x;\n}\n", "import {bisect} from \"d3-array\";\nimport {interpolate as interpolateValue, interpolateNumber, interpolateRound} from \"d3-interpolate\";\nimport constant from \"./constant.js\";\nimport number from \"./number.js\";\n\nvar unit = [0, 1];\n\nexport function identity(x) {\n  return x;\n}\n\nfunction normalize(a, b) {\n  return (b -= (a = +a))\n      ? function(x) { return (x - a) / b; }\n      : constant(isNaN(b) ? NaN : 0.5);\n}\n\nfunction clamper(a, b) {\n  var t;\n  if (a > b) t = a, a = b, b = t;\n  return function(x) { return Math.max(a, Math.min(b, x)); };\n}\n\n// normalize(a, b)(x) takes a domain value x in [a,b] and returns the corresponding parameter t in [0,1].\n// interpolate(a, b)(t) takes a parameter t in [0,1] and returns the corresponding range value x in [a,b].\nfunction bimap(domain, range, interpolate) {\n  var d0 = domain[0], d1 = domain[1], r0 = range[0], r1 = range[1];\n  if (d1 < d0) d0 = normalize(d1, d0), r0 = interpolate(r1, r0);\n  else d0 = normalize(d0, d1), r0 = interpolate(r0, r1);\n  return function(x) { return r0(d0(x)); };\n}\n\nfunction polymap(domain, range, interpolate) {\n  var j = Math.min(domain.length, range.length) - 1,\n      d = new Array(j),\n      r = new Array(j),\n      i = -1;\n\n  // Reverse descending domains.\n  if (domain[j] < domain[0]) {\n    domain = domain.slice().reverse();\n    range = range.slice().reverse();\n  }\n\n  while (++i < j) {\n    d[i] = normalize(domain[i], domain[i + 1]);\n    r[i] = interpolate(range[i], range[i + 1]);\n  }\n\n  return function(x) {\n    var i = bisect(domain, x, 1, j) - 1;\n    return r[i](d[i](x));\n  };\n}\n\nexport function copy(source, target) {\n  return target\n      .domain(source.domain())\n      .range(source.range())\n      .interpolate(source.interpolate())\n      .clamp(source.clamp())\n      .unknown(source.unknown());\n}\n\nexport function transformer() {\n  var domain = unit,\n      range = unit,\n      interpolate = interpolateValue,\n      transform,\n      untransform,\n      unknown,\n      clamp = identity,\n      piecewise,\n      output,\n      input;\n\n  function rescale() {\n    var n = Math.min(domain.length, range.length);\n    if (clamp !== identity) clamp = clamper(domain[0], domain[n - 1]);\n    piecewise = n > 2 ? polymap : bimap;\n    output = input = null;\n    return scale;\n  }\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : (output || (output = piecewise(domain.map(transform), range, interpolate)))(transform(clamp(x)));\n  }\n\n  scale.invert = function(y) {\n    return clamp(untransform((input || (input = piecewise(range, domain.map(transform), interpolateNumber)))(y)));\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain = Array.from(_, number), rescale()) : domain.slice();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), rescale()) : range.slice();\n  };\n\n  scale.rangeRound = function(_) {\n    return range = Array.from(_), interpolate = interpolateRound, rescale();\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (clamp = _ ? true : identity, rescale()) : clamp !== identity;\n  };\n\n  scale.interpolate = function(_) {\n    return arguments.length ? (interpolate = _, rescale()) : interpolate;\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  return function(t, u) {\n    transform = t, untransform = u;\n    return rescale();\n  };\n}\n\nexport default function continuous() {\n  return transformer()(identity, identity);\n}\n", "export default function(x) {\n  return Math.abs(x = Math.round(x)) >= 1e21\n      ? x.toLocaleString(\"en\").replace(/,/g, \"\")\n      : x.toString(10);\n}\n\n// Computes the decimal coefficient and exponent of the specified number x with\n// significant digits p, where x is positive and p is in [1, 21] or undefined.\n// For example, formatDecimalParts(1.23) returns [\"123\", 0].\nexport function formatDecimalParts(x, p) {\n  if ((i = (x = p ? x.toExponential(p - 1) : x.toExponential()).indexOf(\"e\")) < 0) return null; // NaN, ±Infinity\n  var i, coefficient = x.slice(0, i);\n\n  // The string returned by toExponential either has the form \\d\\.\\d+e[-+]\\d+\n  // (e.g., 1.2e+3) or the form \\de[-+]\\d+ (e.g., 1e+3).\n  return [\n    coefficient.length > 1 ? coefficient[0] + coefficient.slice(2) : coefficient,\n    +x.slice(i + 1)\n  ];\n}\n", "import {formatDecimalParts} from \"./formatDecimal.js\";\n\nexport default function(x) {\n  return x = formatDecimalParts(Math.abs(x)), x ? x[1] : NaN;\n}\n", "export default function(grouping, thousands) {\n  return function(value, width) {\n    var i = value.length,\n        t = [],\n        j = 0,\n        g = grouping[0],\n        length = 0;\n\n    while (i > 0 && g > 0) {\n      if (length + g + 1 > width) g = Math.max(1, width - length);\n      t.push(value.substring(i -= g, i + g));\n      if ((length += g + 1) > width) break;\n      g = grouping[j = (j + 1) % grouping.length];\n    }\n\n    return t.reverse().join(thousands);\n  };\n}\n", "export default function(numerals) {\n  return function(value) {\n    return value.replace(/[0-9]/g, function(i) {\n      return numerals[+i];\n    });\n  };\n}\n", "// [[fill]align][sign][symbol][0][width][,][.precision][~][type]\nvar re = /^(?:(.)?([<>=^]))?([+\\-( ])?([$#])?(0)?(\\d+)?(,)?(\\.\\d+)?(~)?([a-z%])?$/i;\n\nexport default function formatSpecifier(specifier) {\n  if (!(match = re.exec(specifier))) throw new Error(\"invalid format: \" + specifier);\n  var match;\n  return new FormatSpecifier({\n    fill: match[1],\n    align: match[2],\n    sign: match[3],\n    symbol: match[4],\n    zero: match[5],\n    width: match[6],\n    comma: match[7],\n    precision: match[8] && match[8].slice(1),\n    trim: match[9],\n    type: match[10]\n  });\n}\n\nformatSpecifier.prototype = FormatSpecifier.prototype; // instanceof\n\nexport function FormatSpecifier(specifier) {\n  this.fill = specifier.fill === undefined ? \" \" : specifier.fill + \"\";\n  this.align = specifier.align === undefined ? \">\" : specifier.align + \"\";\n  this.sign = specifier.sign === undefined ? \"-\" : specifier.sign + \"\";\n  this.symbol = specifier.symbol === undefined ? \"\" : specifier.symbol + \"\";\n  this.zero = !!specifier.zero;\n  this.width = specifier.width === undefined ? undefined : +specifier.width;\n  this.comma = !!specifier.comma;\n  this.precision = specifier.precision === undefined ? undefined : +specifier.precision;\n  this.trim = !!specifier.trim;\n  this.type = specifier.type === undefined ? \"\" : specifier.type + \"\";\n}\n\nFormatSpecifier.prototype.toString = function() {\n  return this.fill\n      + this.align\n      + this.sign\n      + this.symbol\n      + (this.zero ? \"0\" : \"\")\n      + (this.width === undefined ? \"\" : Math.max(1, this.width | 0))\n      + (this.comma ? \",\" : \"\")\n      + (this.precision === undefined ? \"\" : \".\" + Math.max(0, this.precision | 0))\n      + (this.trim ? \"~\" : \"\")\n      + this.type;\n};\n", "// Trims insignificant zeros, e.g., replaces 1.2000k with 1.2k.\nexport default function(s) {\n  out: for (var n = s.length, i = 1, i0 = -1, i1; i < n; ++i) {\n    switch (s[i]) {\n      case \".\": i0 = i1 = i; break;\n      case \"0\": if (i0 === 0) i0 = i; i1 = i; break;\n      default: if (!+s[i]) break out; if (i0 > 0) i0 = 0; break;\n    }\n  }\n  return i0 > 0 ? s.slice(0, i0) + s.slice(i1 + 1) : s;\n}\n", "import {formatDecimalParts} from \"./formatDecimal.js\";\n\nexport var prefixExponent;\n\nexport default function(x, p) {\n  var d = formatDecimalParts(x, p);\n  if (!d) return x + \"\";\n  var coefficient = d[0],\n      exponent = d[1],\n      i = exponent - (prefixExponent = Math.max(-8, Math.min(8, Math.floor(exponent / 3))) * 3) + 1,\n      n = coefficient.length;\n  return i === n ? coefficient\n      : i > n ? coefficient + new Array(i - n + 1).join(\"0\")\n      : i > 0 ? coefficient.slice(0, i) + \".\" + coefficient.slice(i)\n      : \"0.\" + new Array(1 - i).join(\"0\") + formatDecimalParts(x, Math.max(0, p + i - 1))[0]; // less than 1y!\n}\n", "import {formatDecimalParts} from \"./formatDecimal.js\";\n\nexport default function(x, p) {\n  var d = formatDecimalParts(x, p);\n  if (!d) return x + \"\";\n  var coefficient = d[0],\n      exponent = d[1];\n  return exponent < 0 ? \"0.\" + new Array(-exponent).join(\"0\") + coefficient\n      : coefficient.length > exponent + 1 ? coefficient.slice(0, exponent + 1) + \".\" + coefficient.slice(exponent + 1)\n      : coefficient + new Array(exponent - coefficient.length + 2).join(\"0\");\n}\n", "import formatDecimal from \"./formatDecimal.js\";\nimport formatPrefixAuto from \"./formatPrefixAuto.js\";\nimport formatRounded from \"./formatRounded.js\";\n\nexport default {\n  \"%\": (x, p) => (x * 100).toFixed(p),\n  \"b\": (x) => Math.round(x).toString(2),\n  \"c\": (x) => x + \"\",\n  \"d\": formatDecimal,\n  \"e\": (x, p) => x.toExponential(p),\n  \"f\": (x, p) => x.toFixed(p),\n  \"g\": (x, p) => x.toPrecision(p),\n  \"o\": (x) => Math.round(x).toString(8),\n  \"p\": (x, p) => formatRounded(x * 100, p),\n  \"r\": formatRounded,\n  \"s\": formatPrefixAuto,\n  \"X\": (x) => Math.round(x).toString(16).toUpperCase(),\n  \"x\": (x) => Math.round(x).toString(16)\n};\n", "export default function(x) {\n  return x;\n}\n", "import exponent from \"./exponent.js\";\nimport formatGroup from \"./formatGroup.js\";\nimport formatNumerals from \"./formatNumerals.js\";\nimport formatSpecifier from \"./formatSpecifier.js\";\nimport formatTrim from \"./formatTrim.js\";\nimport formatTypes from \"./formatTypes.js\";\nimport {prefixExponent} from \"./formatPrefixAuto.js\";\nimport identity from \"./identity.js\";\n\nvar map = Array.prototype.map,\n    prefixes = [\"y\",\"z\",\"a\",\"f\",\"p\",\"n\",\"µ\",\"m\",\"\",\"k\",\"M\",\"G\",\"T\",\"P\",\"E\",\"Z\",\"Y\"];\n\nexport default function(locale) {\n  var group = locale.grouping === undefined || locale.thousands === undefined ? identity : formatGroup(map.call(locale.grouping, Number), locale.thousands + \"\"),\n      currencyPrefix = locale.currency === undefined ? \"\" : locale.currency[0] + \"\",\n      currencySuffix = locale.currency === undefined ? \"\" : locale.currency[1] + \"\",\n      decimal = locale.decimal === undefined ? \".\" : locale.decimal + \"\",\n      numerals = locale.numerals === undefined ? identity : formatNumerals(map.call(locale.numerals, String)),\n      percent = locale.percent === undefined ? \"%\" : locale.percent + \"\",\n      minus = locale.minus === undefined ? \"−\" : locale.minus + \"\",\n      nan = locale.nan === undefined ? \"NaN\" : locale.nan + \"\";\n\n  function newFormat(specifier) {\n    specifier = formatSpecifier(specifier);\n\n    var fill = specifier.fill,\n        align = specifier.align,\n        sign = specifier.sign,\n        symbol = specifier.symbol,\n        zero = specifier.zero,\n        width = specifier.width,\n        comma = specifier.comma,\n        precision = specifier.precision,\n        trim = specifier.trim,\n        type = specifier.type;\n\n    // The \"n\" type is an alias for \",g\".\n    if (type === \"n\") comma = true, type = \"g\";\n\n    // The \"\" type, and any invalid type, is an alias for \".12~g\".\n    else if (!formatTypes[type]) precision === undefined && (precision = 12), trim = true, type = \"g\";\n\n    // If zero fill is specified, padding goes after sign and before digits.\n    if (zero || (fill === \"0\" && align === \"=\")) zero = true, fill = \"0\", align = \"=\";\n\n    // Compute the prefix and suffix.\n    // For SI-prefix, the suffix is lazily computed.\n    var prefix = symbol === \"$\" ? currencyPrefix : symbol === \"#\" && /[boxX]/.test(type) ? \"0\" + type.toLowerCase() : \"\",\n        suffix = symbol === \"$\" ? currencySuffix : /[%p]/.test(type) ? percent : \"\";\n\n    // What format function should we use?\n    // Is this an integer type?\n    // Can this type generate exponential notation?\n    var formatType = formatTypes[type],\n        maybeSuffix = /[defgprs%]/.test(type);\n\n    // Set the default precision if not specified,\n    // or clamp the specified precision to the supported range.\n    // For significant precision, it must be in [1, 21].\n    // For fixed precision, it must be in [0, 20].\n    precision = precision === undefined ? 6\n        : /[gprs]/.test(type) ? Math.max(1, Math.min(21, precision))\n        : Math.max(0, Math.min(20, precision));\n\n    function format(value) {\n      var valuePrefix = prefix,\n          valueSuffix = suffix,\n          i, n, c;\n\n      if (type === \"c\") {\n        valueSuffix = formatType(value) + valueSuffix;\n        value = \"\";\n      } else {\n        value = +value;\n\n        // Determine the sign. -0 is not less than 0, but 1 / -0 is!\n        var valueNegative = value < 0 || 1 / value < 0;\n\n        // Perform the initial formatting.\n        value = isNaN(value) ? nan : formatType(Math.abs(value), precision);\n\n        // Trim insignificant zeros.\n        if (trim) value = formatTrim(value);\n\n        // If a negative value rounds to zero after formatting, and no explicit positive sign is requested, hide the sign.\n        if (valueNegative && +value === 0 && sign !== \"+\") valueNegative = false;\n\n        // Compute the prefix and suffix.\n        valuePrefix = (valueNegative ? (sign === \"(\" ? sign : minus) : sign === \"-\" || sign === \"(\" ? \"\" : sign) + valuePrefix;\n        valueSuffix = (type === \"s\" ? prefixes[8 + prefixExponent / 3] : \"\") + valueSuffix + (valueNegative && sign === \"(\" ? \")\" : \"\");\n\n        // Break the formatted value into the integer “value” part that can be\n        // grouped, and fractional or exponential “suffix” part that is not.\n        if (maybeSuffix) {\n          i = -1, n = value.length;\n          while (++i < n) {\n            if (c = value.charCodeAt(i), 48 > c || c > 57) {\n              valueSuffix = (c === 46 ? decimal + value.slice(i + 1) : value.slice(i)) + valueSuffix;\n              value = value.slice(0, i);\n              break;\n            }\n          }\n        }\n      }\n\n      // If the fill character is not \"0\", grouping is applied before padding.\n      if (comma && !zero) value = group(value, Infinity);\n\n      // Compute the padding.\n      var length = valuePrefix.length + value.length + valueSuffix.length,\n          padding = length < width ? new Array(width - length + 1).join(fill) : \"\";\n\n      // If the fill character is \"0\", grouping is applied after padding.\n      if (comma && zero) value = group(padding + value, padding.length ? width - valueSuffix.length : Infinity), padding = \"\";\n\n      // Reconstruct the final output based on the desired alignment.\n      switch (align) {\n        case \"<\": value = valuePrefix + value + valueSuffix + padding; break;\n        case \"=\": value = valuePrefix + padding + value + valueSuffix; break;\n        case \"^\": value = padding.slice(0, length = padding.length >> 1) + valuePrefix + value + valueSuffix + padding.slice(length); break;\n        default: value = padding + valuePrefix + value + valueSuffix; break;\n      }\n\n      return numerals(value);\n    }\n\n    format.toString = function() {\n      return specifier + \"\";\n    };\n\n    return format;\n  }\n\n  function formatPrefix(specifier, value) {\n    var f = newFormat((specifier = formatSpecifier(specifier), specifier.type = \"f\", specifier)),\n        e = Math.max(-8, Math.min(8, Math.floor(exponent(value) / 3))) * 3,\n        k = Math.pow(10, -e),\n        prefix = prefixes[8 + e / 3];\n    return function(value) {\n      return f(k * value) + prefix;\n    };\n  }\n\n  return {\n    format: newFormat,\n    formatPrefix: formatPrefix\n  };\n}\n", "import formatLocale from \"./locale.js\";\n\nvar locale;\nexport var format;\nexport var formatPrefix;\n\ndefaultLocale({\n  thousands: \",\",\n  grouping: [3],\n  currency: [\"$\", \"\"]\n});\n\nexport default function defaultLocale(definition) {\n  locale = formatLocale(definition);\n  format = locale.format;\n  formatPrefix = locale.formatPrefix;\n  return locale;\n}\n", "import exponent from \"./exponent.js\";\n\nexport default function(step) {\n  return Math.max(0, -exponent(Math.abs(step)));\n}\n", "import exponent from \"./exponent.js\";\n\nexport default function(step, value) {\n  return Math.max(0, Math.max(-8, Math.min(8, Math.floor(exponent(value) / 3))) * 3 - exponent(Math.abs(step)));\n}\n", "import exponent from \"./exponent.js\";\n\nexport default function(step, max) {\n  step = Math.abs(step), max = Math.abs(max) - step;\n  return Math.max(0, exponent(max) - exponent(step)) + 1;\n}\n", "import {tickStep} from \"d3-array\";\nimport {format, formatPrefix, formatSpecifier, precisionFixed, precisionPrefix, precisionRound} from \"d3-format\";\n\nexport default function tickFormat(start, stop, count, specifier) {\n  var step = tickStep(start, stop, count),\n      precision;\n  specifier = formatSpecifier(specifier == null ? \",f\" : specifier);\n  switch (specifier.type) {\n    case \"s\": {\n      var value = Math.max(Math.abs(start), Math.abs(stop));\n      if (specifier.precision == null && !isNaN(precision = precisionPrefix(step, value))) specifier.precision = precision;\n      return formatPrefix(specifier, value);\n    }\n    case \"\":\n    case \"e\":\n    case \"g\":\n    case \"p\":\n    case \"r\": {\n      if (specifier.precision == null && !isNaN(precision = precisionRound(step, Math.max(Math.abs(start), Math.abs(stop))))) specifier.precision = precision - (specifier.type === \"e\");\n      break;\n    }\n    case \"f\":\n    case \"%\": {\n      if (specifier.precision == null && !isNaN(precision = precisionFixed(step))) specifier.precision = precision - (specifier.type === \"%\") * 2;\n      break;\n    }\n  }\n  return format(specifier);\n}\n", "import {ticks, tickIncrement} from \"d3-array\";\nimport continuous, {copy} from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\nimport tickFormat from \"./tickFormat.js\";\n\nexport function linearish(scale) {\n  var domain = scale.domain;\n\n  scale.ticks = function(count) {\n    var d = domain();\n    return ticks(d[0], d[d.length - 1], count == null ? 10 : count);\n  };\n\n  scale.tickFormat = function(count, specifier) {\n    var d = domain();\n    return tickFormat(d[0], d[d.length - 1], count == null ? 10 : count, specifier);\n  };\n\n  scale.nice = function(count) {\n    if (count == null) count = 10;\n\n    var d = domain();\n    var i0 = 0;\n    var i1 = d.length - 1;\n    var start = d[i0];\n    var stop = d[i1];\n    var prestep;\n    var step;\n    var maxIter = 10;\n\n    if (stop < start) {\n      step = start, start = stop, stop = step;\n      step = i0, i0 = i1, i1 = step;\n    }\n    \n    while (maxIter-- > 0) {\n      step = tickIncrement(start, stop, count);\n      if (step === prestep) {\n        d[i0] = start\n        d[i1] = stop\n        return domain(d);\n      } else if (step > 0) {\n        start = Math.floor(start / step) * step;\n        stop = Math.ceil(stop / step) * step;\n      } else if (step < 0) {\n        start = Math.ceil(start * step) / step;\n        stop = Math.floor(stop * step) / step;\n      } else {\n        break;\n      }\n      prestep = step;\n    }\n\n    return scale;\n  };\n\n  return scale;\n}\n\nexport default function linear() {\n  var scale = continuous();\n\n  scale.copy = function() {\n    return copy(scale, linear());\n  };\n\n  initRange.apply(scale, arguments);\n\n  return linearish(scale);\n}\n", "import Delaunator from 'delaunator';\nimport earcut, { flatten as earcutFlatten } from 'earcut';\nimport turfPointInPolygon from '@turf/boolean-point-in-polygon';\nimport { geoDistance, geoInterpolate, geoBounds, geoContains } from 'd3-geo';\nimport { geoVoronoi } from 'd3-geo-voronoi';\nimport { mean, merge as flatten, extent } from 'd3-array';\nimport { scaleLinear } from 'd3-scale';\n\nfunction geoPolygonTriangulate(polygon, {\n  resolution = Infinity // curvature resolution, in spherical degrees\n} = {}) {\n  const contour = interpolateContourPoints(polygon, resolution);\n\n  const edgePoints = flatten(contour);\n  const innerPoints = getInnerGeoPoints(polygon, resolution);\n  const points = [...edgePoints, ...innerPoints];\n\n  const boundariesGeojson = { type: 'Polygon', coordinates: polygon };\n  const [[minLng, minLat], [maxLng, maxLat]] = geoBounds(boundariesGeojson);\n  const crossesPoleOrAntimeridian = minLng > maxLng // crosses antimeridian\n    || maxLat >= 89 // crosses north pole\n    || minLat <= -89; // crosses south pole\n\n  let indices = [];\n\n  if (crossesPoleOrAntimeridian) {\n    // Use d3-geo-voronoi. Slowest, but most accurate for polygons that cross poles or anti-meridian\n    const vt = geoVoronoi(points).triangles(); // geoDelaunay generates more triangles than needed\n    const pntMap = new Map(points.map(([lng, lat], idx) => [`${lng}-${lat}`, idx]));\n    vt.features.forEach(f => {\n      const triangle = f.geometry.coordinates[0].slice(0, 3).reverse(); // reverse wound to match earcut\n\n      const inds = [];\n      triangle.forEach(([lng, lat]) => {\n        const k = `${lng}-${lat}`;\n        pntMap.has(k) && inds.push(pntMap.get(k));\n      });\n\n      if (inds.length !== 3) return; // triangle malfunction\n\n      // exclude edge triangles outside polygon perimeter or through holes\n      if (inds.some(ind => ind < edgePoints.length)) {\n        const triangleCentroid = f.properties.circumcenter;\n        if (!pointInside(triangleCentroid, boundariesGeojson, crossesPoleOrAntimeridian)) return;\n      }\n\n      indices.push(...inds);\n    });\n  } else if (!innerPoints.length) {\n    // earcut triangulation slightly more performing if it's only using the polygon perimeter\n    const { vertices, holes = [] } = earcutFlatten(contour);\n    indices = earcut(vertices, holes, 2);\n  } else {\n    // use delaunator\n    const delaunay = Delaunator.from(points);\n\n    for (let i = 0, len = delaunay.triangles.length; i < len; i += 3) {\n      const inds = [2, 1, 0].map(idx => delaunay.triangles[i + idx]); // reverse wound to have same orientation as earcut\n      const triangle = inds.map(indice => points[indice]);\n\n      // exclude edge triangles outside polygon perimeter or through holes\n      if (inds.some(ind => ind < edgePoints.length)) {\n        const triangleCentroid = [0, 1].map(coordIdx => mean(triangle, p => p[coordIdx]));\n        if (!pointInside(triangleCentroid, boundariesGeojson, crossesPoleOrAntimeridian)) continue;\n      }\n\n      indices.push(...inds);\n    }\n  }\n\n  // calc uvs\n  const lngUvScale = scaleLinear(extent(points, d => d[0]), [0,1]);\n  const latUvScale = scaleLinear(extent(points, d => d[1]), [0,1]);\n  const uvs = points.map(([lng, lat]) => [lngUvScale(lng), latUvScale(lat)]);\n\n  const triangles = { points, indices, uvs };\n\n  return { contour, triangles };\n}\n\nfunction interpolateContourPoints(polygon, maxDistance) {\n  // add interpolated points for segments that are further apart than the max distance\n  return polygon.map(coords => {\n    const pnts = [];\n\n    let prevPnt;\n    coords.forEach(pnt => {\n      if (prevPnt) {\n        const dist = geoDistance(pnt, prevPnt) * 180 / Math.PI;\n        if (dist > maxDistance) {\n          const interpol = geoInterpolate(prevPnt, pnt);\n          const tStep = 1 / Math.ceil(dist / maxDistance);\n\n          let t = tStep;\n          while (t < 1) {\n            pnts.push(interpol(t));\n            t += tStep;\n          }\n        }\n      }\n\n      pnts.push(prevPnt = pnt);\n    });\n\n    return pnts;\n  });\n}\n\nfunction getInnerGeoPoints(polygon, maxDistance) {\n  const boundariesGeojson = { type: 'Polygon', coordinates: polygon };\n  const [[minLng, minLat], [maxLng, maxLat]] = geoBounds(boundariesGeojson);\n\n  // polygon smaller than maxDistance -> no inner points\n  if (Math.min(Math.abs(maxLng - minLng), Math.abs(maxLat - minLat)) < maxDistance) return [];\n\n  const crossesPoleOrAntimeridian = minLng > maxLng || maxLat >= 89 || minLat <= -89;\n\n  return getGeoSpiralGrid(maxDistance, { minLng, maxLng, minLat, maxLat })\n    .filter(pnt => pointInside(pnt, boundariesGeojson, crossesPoleOrAntimeridian));\n}\n\nfunction getGeoSpiralGrid(\n  distanceBetweenPoints, // in degrees\n  { minLng, maxLng, minLat, maxLat } = {}\n) {\n  const numPoints = Math.round((360 / distanceBetweenPoints)**2 / Math.PI);\n\n  // https://observablehq.com/@mbostock/spherical-fibonacci-lattice\n  const phi = (1 + Math.sqrt(5)) / 2; // golden ratio\n\n  const getPntLng = idx => idx / phi * 360 % 360 - 180;\n  const getPntLat = idx => Math.acos(2 * idx / numPoints - 1) / Math.PI * 180 - 90;\n  const getPntIdx = lat => numPoints * (Math.cos((lat + 90) * Math.PI / 180) + 1) / 2;\n\n  const pntIdxRange = [\n    maxLat !== undefined ? Math.ceil(getPntIdx(maxLat)) : 0,\n    minLat !== undefined ? Math.floor(getPntIdx(minLat)) : numPoints - 1,\n  ];\n\n  const isLngInRange = minLng === undefined && maxLng === undefined\n    ? () => true\n    : minLng === undefined\n      ? lng => lng <= maxLng\n      : maxLng === undefined\n        ? lng => lng >= minLng\n        : maxLng >= minLng\n          ? lng => lng >= minLng && lng <= maxLng\n          : lng => lng >= minLng || lng <= maxLng; // for ranges that cross the anti-meridian\n\n  const pnts = [];\n  for (let i = pntIdxRange[0]; i <= pntIdxRange[1]; i++) {\n    const lng = getPntLng(i);\n    isLngInRange(lng) && pnts.push([lng, getPntLat(i)]);\n  }\n\n  return pnts;\n}\n\nfunction pointInside(pnt, polygon, crossesPoleOrAntimeridian = false) {\n  // turf method is more performing but malfunctions if polygon includes a pole (lat = 90 | -90) or crosses the antimeridian (lng = 180 | -180)\n  return crossesPoleOrAntimeridian\n    ? geoContains(polygon, pnt)\n    : turfPointInPolygon(pnt, polygon);\n}\n\nexport default geoPolygonTriangulate;\n", "import {\n  BufferGeometry,\n  Float32BufferAttribute\n} from 'three';\n\nconst THREE = window.THREE\n  ? window.THREE // Prefer consumption from global THREE, if exists\n  : {\n  BufferGeometry,\n  Float32BufferAttribute\n};\n\nimport { merge as flatten } from 'd3-array';\nimport { flatten as earcutFlatten } from 'earcut';\n\nimport geoPolygonTriangulate from './geoPolygonTriangulate';\n\n// support both modes for backwards threejs compatibility\nconst setAttributeFn = new THREE.BufferGeometry().setAttribute ? 'setAttribute' : 'addAttribute';\n\nclass ConicPolygonGeometry extends THREE.BufferGeometry {\n  constructor(polygonGeoJson, bottomHeight, topHeight, closedBottom, closedTop, includeSides, curvatureResolution) {\n    super();\n\n    this.type = 'ConicPolygonGeometry';\n\n    this.parameters = {\n      polygonGeoJson,\n      bottomHeight,\n      topHeight,\n      closedBottom,\n      closedTop,\n      includeSides,\n      curvatureResolution\n    };\n\n    // defaults\n    bottomHeight = bottomHeight || 0;\n    topHeight = topHeight || 1;\n    closedBottom = closedBottom !== undefined ? closedBottom : true;\n    closedTop = closedTop !== undefined ? closedTop : true;\n    includeSides = includeSides !== undefined ? includeSides : true;\n    curvatureResolution = curvatureResolution || 5; // in angular degrees\n\n    // pre-calculate contour, triangulation and UV maps\n    const { contour, triangles } = geoPolygonTriangulate(polygonGeoJson, {resolution: curvatureResolution});\n    const flatUvs = flatten(triangles.uvs);\n\n    let vertices = [];\n    let uvs = [];\n    let indices = [];\n    let groupCnt = 0; // add groups to apply different materials to torso / caps\n\n    const addGroup = groupData => {\n      const prevVertCnt = Math.round(vertices.length / 3);\n      const prevIndCnt = indices.length;\n\n      vertices = vertices.concat(groupData.vertices);\n      uvs = uvs.concat(groupData.uvs);\n      indices = indices.concat(!prevVertCnt ? groupData.indices : groupData.indices.map(ind => ind + prevVertCnt));\n\n      this.addGroup(prevIndCnt, indices.length - prevIndCnt, groupCnt++);\n    };\n\n    includeSides && addGroup(generateTorso());\n    closedBottom && addGroup(generateCap(bottomHeight, false));\n    closedTop && addGroup(generateCap(topHeight, true));\n\n    // build geometry\n    this.setIndex(indices);\n    this[setAttributeFn]('position', new THREE.Float32BufferAttribute(vertices, 3));\n    this[setAttributeFn]('uv', new THREE.Float32BufferAttribute(uvs, 2));\n\n    // auto-calculate normals\n    this.computeVertexNormals();\n\n    //\n\n    function generateVertices(polygon, altitude) {\n      const altFn = typeof altitude === 'function' ? altitude : () => altitude;\n      const coords3d = polygon.map(coords => coords.map(([lng, lat]) => polar2Cartesian(lat, lng, altFn(lng, lat))));\n      // returns { vertices, holes, coordinates }. Each point generates 3 vertice items (x,y,z).\n      return earcutFlatten(coords3d);\n    }\n\n    function generateTorso() {\n      const {vertices: bottomVerts, holes} = generateVertices(contour, bottomHeight);\n      const {vertices: topVerts} = generateVertices(contour, topHeight);\n\n      const vertices = flatten([topVerts, bottomVerts]);\n      const numPoints = Math.round(topVerts.length / 3);\n\n      const holesIdx = new Set(holes);\n      let lastHoleIdx = 0;\n\n      const indices = [];\n      for (let v0Idx = 0; v0Idx < numPoints; v0Idx++) {\n        let v1Idx = v0Idx + 1; // next point\n        if (v1Idx === numPoints) {\n          v1Idx = lastHoleIdx; // close final loop\n        } else if (holesIdx.has(v1Idx)) {\n          const holeIdx = v1Idx;\n          v1Idx = lastHoleIdx; // close hole loop\n          lastHoleIdx = holeIdx;\n        }\n\n        // Each pair of coords generates two triangles (faces)\n        indices.push(v0Idx, v0Idx + numPoints, v1Idx + numPoints);\n        indices.push(v1Idx + numPoints, v1Idx, v0Idx);\n      }\n\n      const uvs = []; // wrap texture around perimeter (u), with v=1 on top\n      for (let v=1; v>=0; v--)\n        for (let i=0; i<numPoints; i+=1)\n          uvs.push(i/(numPoints-1), v);\n\n      return { indices, vertices, uvs };\n    }\n\n    function generateCap(radius, isTop = true) {\n      return {\n        // need to reverse-wind the bottom triangles to make them face outwards\n        indices: isTop ? triangles.indices : triangles.indices.slice().reverse(),\n        vertices: generateVertices([triangles.points], radius).vertices,\n        uvs: flatUvs\n      }\n    }\n  }\n}\n\n//\n\nfunction polar2Cartesian(lat, lng, r = 0) {\n  const phi = (90 - lat) * Math.PI / 180;\n  const theta = (90 - lng) * Math.PI / 180;\n  return [\n    r * Math.sin(phi) * Math.cos(theta), // x\n    r * Math.cos(phi), // y\n    r * Math.sin(phi) * Math.sin(theta) // z\n  ];\n}\n\nexport default ConicPolygonGeometry;\n"], "names": ["zero", "number", "flatten", "sign", "epsilon", "pip", "pi", "halfPi", "tau", "degrees", "radians", "atan2", "cos", "sin", "sqrt", "asin", "lambda00", "phi00", "lambda0", "cosPhi0", "sinPhi0", "spherical", "cartesian", "cartesianDot", "cartesianCross", "boundsStream", "boundsPoint", "stream", "x0", "y0", "object", "distance", "transformer", "resample", "identity", "dot", "cross", "normalize", "rgb", "linear", "colorRgb", "value", "constant", "bisect", "interpolate", "interpolateV<PERSON>ue", "geoPolygonTriangulate", "polygon", "_ref", "arguments", "length", "undefined", "_ref$resolution", "resolution", "Infinity", "contour", "interpolateContourPoints", "edgePoints", "innerPoints", "getInnerGeoPoints", "points", "concat", "_toConsumableArray", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "coordinates", "_geoBounds", "geoBounds", "_geoBounds2", "_slicedToArray", "_geoBounds2$", "minLng", "minLat", "_geoBounds2$2", "maxLng", "maxLat", "crossesPoleOrAntimeridian", "indices", "vt", "geoVoronoi", "triangles", "pntMap", "Map", "map", "_ref2", "idx", "_ref3", "lng", "lat", "features", "for<PERSON>ach", "f", "_indices", "triangle", "geometry", "slice", "reverse", "inds", "_ref4", "_ref5", "k", "has", "push", "get", "some", "ind", "triangleCentroid", "properties", "circumcenter", "pointInside", "apply", "_earcutFlatten", "earcutFlatten", "vertices", "_earcutFlatten$holes", "holes", "earcut", "delaunay", "Delaunator", "from", "_loop", "i", "_indices2", "indice", "coordIdx", "mean", "p", "len", "lngUvScale", "scaleLinear", "extent", "d", "latUvScale", "uvs", "_ref6", "_ref7", "maxDistance", "coords", "pnts", "prevPnt", "pnt", "dist", "geoDistance", "Math", "PI", "interpol", "geoInterpolate", "tStep", "ceil", "t", "_geoBounds3", "_geoBounds4", "_geoBounds4$", "_geoBounds4$2", "min", "abs", "getGeoSpiralGrid", "filter", "distanceBetweenPoints", "_ref8", "numPoints", "round", "pow", "phi", "getPntLng", "getPntLat", "acos", "getPntIdx", "pntIdxRange", "floor", "isLngInRange", "geoContains", "turfPointInPolygon", "THREE", "window", "BufferGeometry", "Float32BufferAttribute", "setAttributeFn", "setAttribute", "ConicPolygonGeometry", "_THREE$BufferGeometry", "polygonGeoJson", "bottomHeight", "topHeight", "closedBottom", "closedTop", "includeSides", "curvatureResolution", "_this", "_classCallCheck", "_callSuper", "parameters", "_geoPolygonTriangulat", "flatUvs", "groupCnt", "addGroup", "groupData", "prevVertCnt", "prevIndCnt", "generateTorso", "generateCap", "setIndex", "computeVertexNormals", "generateVertices", "altitude", "altFn", "coords3d", "polar2Cartesian", "_generateVertices", "bottomVerts", "_generateVertices2", "top<PERSON><PERSON>s", "holesIdx", "Set", "lastHoleIdx", "v0Idx", "v1Idx", "holeIdx", "v", "radius", "isTop", "_inherits", "_createClass", "r", "theta"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAe,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;EACxC,EAAE,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;EACjF;;ECFe,SAAS,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE;EACzC,EAAE,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,GAAG;EAClC,MAAM,CAAC,GAAG,CAAC,GAAG;EACd,MAAM,CAAC,GAAG,CAAC,GAAG;EACd,MAAM,CAAC,IAAI,CAAC,GAAG;EACf,MAAM,GAAG;EACT;;ECHe,SAAS,QAAQ,CAAC,CAAC,EAAE;EACpC,EAAE,IAAI,QAAQ,EAAE,QAAQ,EAAE,KAAK;;EAE/B;EACA;EACA;EACA;EACA;EACA,EAAE,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;EACtB,IAAI,QAAQ,GAAG,SAAS;EACxB,IAAI,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EAC3C,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;EAC9B,GAAG,MAAM;EACT,IAAI,QAAQ,GAAG,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,UAAU,GAAG,CAAC,GAAGA,MAAI;EAC7D,IAAI,QAAQ,GAAG,CAAC;EAChB,IAAI,KAAK,GAAG,CAAC;EACb;;EAEA,EAAE,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE;EAC7C,IAAI,IAAI,EAAE,GAAG,EAAE,EAAE;EACjB,MAAM,IAAI,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE;EACzC,MAAM,GAAG;EACT,QAAQ,MAAM,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC;EACnC,QAAQ,IAAI,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,GAAG,CAAC;EACjD,aAAa,EAAE,GAAG,GAAG;EACrB,OAAO,QAAQ,EAAE,GAAG,EAAE;EACtB;EACA,IAAI,OAAO,EAAE;EACb;;EAEA,EAAE,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE;EAC9C,IAAI,IAAI,EAAE,GAAG,EAAE,EAAE;EACjB,MAAM,IAAI,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE;EACzC,MAAM,GAAG;EACT,QAAQ,MAAM,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC;EACnC,QAAQ,IAAI,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,GAAG,GAAG,CAAC;EAClD,aAAa,EAAE,GAAG,GAAG;EACrB,OAAO,QAAQ,EAAE,GAAG,EAAE;EACtB;EACA,IAAI,OAAO,EAAE;EACb;;EAEA,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE;EAC/C,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;EACpC,IAAI,OAAO,CAAC,GAAG,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;EACrE;;EAEA,EAAE,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC;EAC9B;;EAEA,SAASA,MAAI,GAAG;EAChB,EAAE,OAAO,CAAC;EACV;;ECvDe,SAASC,QAAM,CAAC,CAAC,EAAE;EAClC,EAAE,OAAO,CAAC,KAAK,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;EAC9B;;ECEA,MAAM,eAAe,GAAG,QAAQ,CAAC,SAAS,CAAC;EACpC,MAAM,WAAW,GAAG,eAAe,CAAC,KAAK;EAEpB,QAAQ,CAACA,QAAM,CAAC,CAAC;;ECP9B,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE;EAChD,EAAE,IAAI,GAAG;EACT,EAAE,IAAI,GAAG;EACT,EAAE,IAAI,OAAO,KAAK,SAAS,EAAE;EAC7B,IAAI,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;EAChC,MAAM,IAAI,KAAK,IAAI,IAAI,EAAE;EACzB,QAAQ,IAAI,GAAG,KAAK,SAAS,EAAE;EAC/B,UAAU,IAAI,KAAK,IAAI,KAAK,EAAE,GAAG,GAAG,GAAG,GAAG,KAAK;EAC/C,SAAS,MAAM;EACf,UAAU,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG,GAAG,KAAK;EACtC,UAAU,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG,GAAG,KAAK;EACtC;EACA;EACA;EACA,GAAG,MAAM;EACT,IAAI,IAAI,KAAK,GAAG,EAAE;EAClB,IAAI,KAAK,IAAI,KAAK,IAAI,MAAM,EAAE;EAC9B,MAAM,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE;EAC7D,QAAQ,IAAI,GAAG,KAAK,SAAS,EAAE;EAC/B,UAAU,IAAI,KAAK,IAAI,KAAK,EAAE,GAAG,GAAG,GAAG,GAAG,KAAK;EAC/C,SAAS,MAAM;EACf,UAAU,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG,GAAG,KAAK;EACtC,UAAU,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG,GAAG,KAAK;EACtC;EACA;EACA;EACA;EACA,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EACnB;;EC5BA;EACO,MAAM,KAAK,CAAC;EACnB,EAAE,WAAW,GAAG;EAChB,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,YAAY,CAAC,EAAE,CAAC;EACzC,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC;EACf;EACA,EAAE,GAAG,CAAC,CAAC,EAAE;EACT,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS;EAC5B,IAAI,IAAI,CAAC,GAAG,CAAC;EACb,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;EAChD,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACpB,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAC;EAClB,QAAQ,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;EACpE,MAAM,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE;EACzB,MAAM,CAAC,GAAG,EAAE;EACZ;EACA,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;EACZ,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC;EACnB,IAAI,OAAO,IAAI;EACf;EACA,EAAE,OAAO,GAAG;EACZ,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS;EAC5B,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;EACrC,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE;EACf,MAAM,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EACjB,MAAM,OAAO,CAAC,GAAG,CAAC,EAAE;EACpB,QAAQ,CAAC,GAAG,EAAE;EACd,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EAClB,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAC;EAClB,QAAQ,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;EACzB,QAAQ,IAAI,EAAE,EAAE;EAChB;EACA,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;EAC3E,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC;EAClB,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC;EAClB,QAAQ,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;EAC/B;EACA;EACA,IAAI,OAAO,EAAE;EACb;EACA;;ECxCA,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;EACzB,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;EACtB,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;;EAErB,SAAS,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE;EACtC,EAAE,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC;EAClD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;EAC1C,MAAM,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC;EACxC,MAAM,MAAM,GAAG,KAAK,IAAI,GAAG,GAAG,EAAE,GAAG,KAAK,IAAI,EAAE,GAAG,CAAC,GAAG,KAAK,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC;EACxE,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,GAAG;EACjB,EAAE,IAAI,KAAK,GAAG,CAAC,EAAE;EACjB,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,GAAG,MAAM;EACvC,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC;EAChC,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC;EAC/B,IAAI,IAAI,EAAE,GAAG,GAAG,GAAG,KAAK,EAAE,EAAE,EAAE;EAC9B,IAAI,IAAI,EAAE,GAAG,GAAG,GAAG,IAAI,EAAE,EAAE,EAAE;EAC7B,IAAI,GAAG,GAAG,CAAC,GAAG;EACd,GAAG,MAAM;EACT,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,GAAG,MAAM;EACtC,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC;EAChC,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC;EAC/B,IAAI,IAAI,EAAE,GAAG,GAAG,GAAG,KAAK,EAAE,EAAE,EAAE;EAC9B,IAAI,IAAI,EAAE,GAAG,GAAG,GAAG,IAAI,EAAE,EAAE,EAAE;EAC7B;EACA,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,GAAG,IAAI,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,OAAO,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,GAAG,CAAC,CAAC;EACnF,EAAE,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;EACtB;;EAEe,SAAS,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE;EAClD,EAAE,IAAI,GAAG,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,KAAK;EAC9C,EAAE,IAAI,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE;EAC7B,EAAE,IAAI,KAAK,KAAK,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC;EACpC,EAAE,MAAM,OAAO,GAAG,IAAI,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,GAAG,OAAO,GAAG,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,GAAG,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;EACrH,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE;EAC5B,EAAE,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC;EAC7C,EAAE,IAAI,OAAO,EAAE;EACf,IAAI,IAAI,GAAG,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG;EACvE,SAAS,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,IAAI,GAAG;EAC9D,GAAG,MAAM;EACT,IAAI,IAAI,GAAG,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG;EACvE,SAAS,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,IAAI,GAAG;EAC9D;EACA,EAAE,OAAO,KAAK;EACd;;EAEO,SAAS,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE;EAClD,EAAE,IAAI,GAAG,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,KAAK;EAC9C,EAAE,OAAO,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;EACxC;;EAEO,SAAS,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE;EAC7C,EAAE,IAAI,GAAG,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,KAAK;EAC9C,EAAE,MAAM,OAAO,GAAG,IAAI,GAAG,KAAK,EAAE,GAAG,GAAG,OAAO,GAAG,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,GAAG,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;EACrH,EAAE,OAAO,CAAC,OAAO,GAAG,EAAE,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC;EACxD;;ECtDe,SAAS,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE;EAC9C,EAAE,IAAI,KAAK,GAAG,CAAC;EACf,EAAE,IAAI,GAAG,GAAG,CAAC;EACb,EAAE,IAAI,OAAO,KAAK,SAAS,EAAE;EAC7B,IAAI,KAAK,IAAI,KAAK,IAAI,MAAM,EAAE;EAC9B,MAAM,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,EAAE;EACtD,QAAQ,EAAE,KAAK,EAAE,GAAG,IAAI,KAAK;EAC7B;EACA;EACA,GAAG,MAAM;EACT,IAAI,IAAI,KAAK,GAAG,EAAE;EAClB,IAAI,KAAK,IAAI,KAAK,IAAI,MAAM,EAAE;EAC9B,MAAM,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,EAAE;EAC1F,QAAQ,EAAE,KAAK,EAAE,GAAG,IAAI,KAAK;EAC7B;EACA;EACA;EACA,EAAE,IAAI,KAAK,EAAE,OAAO,GAAG,GAAG,KAAK;EAC/B;;EClBA,UAAUC,SAAO,CAAC,MAAM,EAAE;EAC1B,EAAE,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;EAC9B,IAAI,OAAO,KAAK;EAChB;EACA;;EAEe,SAAS,KAAK,CAAC,MAAM,EAAE;EACtC,EAAE,OAAO,KAAK,CAAC,IAAI,CAACA,SAAO,CAAC,MAAM,CAAC,CAAC;EACpC;;ECPe,SAAS,MAAM,CAAC,IAAI,EAAE,WAAW,EAAE,GAAG,GAAG,CAAC,EAAE;;EAE3D,IAAI,MAAM,QAAQ,GAAG,WAAW,IAAI,WAAW,CAAC,MAAM;EACtD,IAAI,MAAM,QAAQ,GAAG,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM;EAClE,IAAI,IAAI,SAAS,GAAG,UAAU,CAAC,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,CAAC;EAC5D,IAAI,MAAM,SAAS,GAAG,EAAE;;EAExB,IAAI,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,EAAE,OAAO,SAAS;;EAEzE,IAAI,IAAI,IAAI,EAAE,IAAI,EAAE,OAAO;;EAE3B,IAAI,IAAI,QAAQ,EAAE,SAAS,GAAG,cAAc,CAAC,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAC;;EAE/E;EACA,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,GAAG,EAAE;EAChC,QAAQ,IAAI,GAAG,QAAQ;EACvB,QAAQ,IAAI,GAAG,QAAQ;EACvB,QAAQ,IAAI,IAAI,GAAG,CAAC,QAAQ;EAC5B,QAAQ,IAAI,IAAI,GAAG,CAAC,QAAQ;;EAE5B,QAAQ,KAAK,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,IAAI,GAAG,EAAE;EAClD,YAAY,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;EAC7B,YAAY,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;EACjC,YAAY,IAAI,CAAC,GAAG,IAAI,EAAE,IAAI,GAAG,CAAC;EAClC,YAAY,IAAI,CAAC,GAAG,IAAI,EAAE,IAAI,GAAG,CAAC;EAClC,YAAY,IAAI,CAAC,GAAG,IAAI,EAAE,IAAI,GAAG,CAAC;EAClC,YAAY,IAAI,CAAC,GAAG,IAAI,EAAE,IAAI,GAAG,CAAC;EAClC;;EAEA;EACA,QAAQ,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC;EACpD,QAAQ,OAAO,GAAG,OAAO,KAAK,CAAC,GAAG,KAAK,GAAG,OAAO,GAAG,CAAC;EACrD;;EAEA,IAAI,YAAY,CAAC,SAAS,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;;EAEnE,IAAI,OAAO,SAAS;EACpB;;EAEA;EACA,SAAS,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,SAAS,EAAE;EACtD,IAAI,IAAI,IAAI;;EAEZ,IAAI,IAAI,SAAS,MAAM,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;EAC/D,QAAQ,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE,IAAI,GAAG,UAAU,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;EACzG,KAAK,MAAM;EACX,QAAQ,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,GAAG,EAAE,IAAI,GAAG,UAAU,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;EAChH;;EAEA,IAAI,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;EACzC,QAAQ,UAAU,CAAC,IAAI,CAAC;EACxB,QAAQ,IAAI,GAAG,IAAI,CAAC,IAAI;EACxB;;EAEA,IAAI,OAAO,IAAI;EACf;;EAEA;EACA,SAAS,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE;EAClC,IAAI,IAAI,CAAC,KAAK,EAAE,OAAO,KAAK;EAC5B,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,KAAK;;EAEzB,IAAI,IAAI,CAAC,GAAG,KAAK;EACjB,QAAQ,KAAK;EACb,IAAI,GAAG;EACP,QAAQ,KAAK,GAAG,KAAK;;EAErB,QAAQ,IAAI,CAAC,CAAC,CAAC,OAAO,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;EAChF,YAAY,UAAU,CAAC,CAAC,CAAC;EACzB,YAAY,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,IAAI;EAC5B,YAAY,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE;EAC9B,YAAY,KAAK,GAAG,IAAI;;EAExB,SAAS,MAAM;EACf,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI;EACtB;EACA,KAAK,QAAQ,KAAK,IAAI,CAAC,KAAK,GAAG;;EAE/B,IAAI,OAAO,GAAG;EACd;;EAEA;EACA,SAAS,YAAY,CAAC,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE;EACtE,IAAI,IAAI,CAAC,GAAG,EAAE;;EAEd;EACA,IAAI,IAAI,CAAC,IAAI,IAAI,OAAO,EAAE,UAAU,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC;;EAE9D,IAAI,IAAI,IAAI,GAAG,GAAG;;EAElB;EACA,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,EAAE;EAClC,QAAQ,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI;EAC7B,QAAQ,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI;;EAE7B,QAAQ,IAAI,OAAO,GAAG,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,EAAE;EAC1E,YAAY,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;;EAElD,YAAY,UAAU,CAAC,GAAG,CAAC;;EAE3B;EACA,YAAY,GAAG,GAAG,IAAI,CAAC,IAAI;EAC3B,YAAY,IAAI,GAAG,IAAI,CAAC,IAAI;;EAE5B,YAAY;EACZ;;EAEA,QAAQ,GAAG,GAAG,IAAI;;EAElB;EACA,QAAQ,IAAI,GAAG,KAAK,IAAI,EAAE;EAC1B;EACA,YAAY,IAAI,CAAC,IAAI,EAAE;EACvB,gBAAgB,YAAY,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;;EAEvF;EACA,aAAa,MAAM,IAAI,IAAI,KAAK,CAAC,EAAE;EACnC,gBAAgB,GAAG,GAAG,sBAAsB,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC;EAC1E,gBAAgB,YAAY,CAAC,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;;EAEzE;EACA,aAAa,MAAM,IAAI,IAAI,KAAK,CAAC,EAAE;EACnC,gBAAgB,WAAW,CAAC,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC;EACrE;;EAEA,YAAY;EACZ;EACA;EACA;;EAEA;EACA,SAAS,KAAK,CAAC,GAAG,EAAE;EACpB,IAAI,MAAM,CAAC,GAAG,GAAG,CAAC,IAAI;EACtB,QAAQ,CAAC,GAAG,GAAG;EACf,QAAQ,CAAC,GAAG,GAAG,CAAC,IAAI;;EAEpB,IAAI,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,KAAK,CAAC;;EAEzC;EACA,IAAI,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;;EAEpE;EACA,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACnC,QAAQ,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACjC,QAAQ,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACjC,QAAQ,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;EAEjC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI;EAClB,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;EACpB,QAAQ,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;EAC5D,YAAY,0BAA0B,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACxE,YAAY,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,KAAK;EACtD,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI;EAClB;;EAEA,IAAI,OAAO,IAAI;EACf;;EAEA,SAAS,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE;EAC/C,IAAI,MAAM,CAAC,GAAG,GAAG,CAAC,IAAI;EACtB,QAAQ,CAAC,GAAG,GAAG;EACf,QAAQ,CAAC,GAAG,GAAG,CAAC,IAAI;;EAEpB,IAAI,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,KAAK,CAAC;;EAEzC,IAAI,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;;EAEpE;EACA,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACnC,QAAQ,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACjC,QAAQ,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACjC,QAAQ,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;EAEjC;EACA,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC;EACpD,QAAQ,IAAI,GAAG,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC;;EAElD,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK;EACrB,QAAQ,CAAC,GAAG,GAAG,CAAC,KAAK;;EAErB;EACA,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;EACjD,QAAQ,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;EAClF,YAAY,0BAA0B,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,KAAK;EACtH,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK;;EAEnB,QAAQ,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;EAClF,YAAY,0BAA0B,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,KAAK;EACtH,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK;EACnB;;EAEA;EACA,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;EAC7B,QAAQ,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;EAClF,YAAY,0BAA0B,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,KAAK;EACtH,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK;EACnB;;EAEA;EACA,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;EAC7B,QAAQ,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;EAClF,YAAY,0BAA0B,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,KAAK;EACtH,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK;EACnB;;EAEA,IAAI,OAAO,IAAI;EACf;;EAEA;EACA,SAAS,sBAAsB,CAAC,KAAK,EAAE,SAAS,EAAE;EAClD,IAAI,IAAI,CAAC,GAAG,KAAK;EACjB,IAAI,GAAG;EACP,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI;EACxB,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI;;EAE3B,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;;EAExG,YAAY,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEzC;EACA,YAAY,UAAU,CAAC,CAAC,CAAC;EACzB,YAAY,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC;;EAE9B,YAAY,CAAC,GAAG,KAAK,GAAG,CAAC;EACzB;EACA,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI;EAClB,KAAK,QAAQ,CAAC,KAAK,KAAK;;EAExB,IAAI,OAAO,YAAY,CAAC,CAAC,CAAC;EAC1B;;EAEA;EACA,SAAS,WAAW,CAAC,KAAK,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE;EACjE;EACA,IAAI,IAAI,CAAC,GAAG,KAAK;EACjB,IAAI,GAAG;EACP,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI;EAC3B,QAAQ,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE;EAC7B,YAAY,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EACtD;EACA,gBAAgB,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC;;EAE1C;EACA,gBAAgB,CAAC,GAAG,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC;EAC3C,gBAAgB,CAAC,GAAG,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC;;EAE3C;EACA,gBAAgB,YAAY,CAAC,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;EACvE,gBAAgB,YAAY,CAAC,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;EACvE,gBAAgB;EAChB;EACA,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI;EACtB;EACA,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI;EAClB,KAAK,QAAQ,CAAC,KAAK,KAAK;EACxB;;EAEA;EACA,SAAS,cAAc,CAAC,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,EAAE;EAC3D,IAAI,MAAM,KAAK,GAAG,EAAE;;EAEpB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EAC5D,QAAQ,MAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG;EAC1C,QAAQ,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM;EACxE,QAAQ,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC;EAC7D,QAAQ,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI;EACnD,QAAQ,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;EACrC;;EAEA,IAAI,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC;;EAE9B;EACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EAC3C,QAAQ,SAAS,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC;EACtD;;EAEA,IAAI,OAAO,SAAS;EACpB;;EAEA,SAAS,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE;EAC9B,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAC1B;EACA;EACA,IAAI,IAAI,MAAM,KAAK,CAAC,EAAE;EACtB,QAAQ,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAC1B,QAAQ,IAAI,MAAM,KAAK,CAAC,EAAE;EAC1B,YAAY,MAAM,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC9D,YAAY,MAAM,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC9D,YAAY,MAAM,GAAG,MAAM,GAAG,MAAM;EACpC;EACA;EACA,IAAI,OAAO,MAAM;EACjB;;EAEA;EACA,SAAS,aAAa,CAAC,IAAI,EAAE,SAAS,EAAE;EACxC,IAAI,MAAM,MAAM,GAAG,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC;EAClD,IAAI,IAAI,CAAC,MAAM,EAAE;EACjB,QAAQ,OAAO,SAAS;EACxB;;EAEA,IAAI,MAAM,aAAa,GAAG,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC;;EAEpD;EACA,IAAI,YAAY,CAAC,aAAa,EAAE,aAAa,CAAC,IAAI,CAAC;EACnD,IAAI,OAAO,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;EAC5C;;EAEA;EACA,SAAS,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE;EACzC,IAAI,IAAI,CAAC,GAAG,SAAS;EACrB,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;EACrB,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;EACrB,IAAI,IAAI,EAAE,GAAG,CAAC,QAAQ;EACtB,IAAI,IAAI,CAAC;;EAET;EACA;EACA;EACA,IAAI,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC;EACjC,IAAI,GAAG;EACP,QAAQ,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,IAAI;EAC/C,aAAa,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;EAClE,YAAY,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC5E,YAAY,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE;EACnC,gBAAgB,EAAE,GAAG,CAAC;EACtB,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI;EAC/C,gBAAgB,IAAI,CAAC,KAAK,EAAE,EAAE,OAAO,CAAC,CAAC;EACvC;EACA;EACA,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI;EAClB,KAAK,QAAQ,CAAC,KAAK,SAAS;;EAE5B,IAAI,IAAI,CAAC,CAAC,EAAE,OAAO,IAAI;;EAEvB;EACA;EACA;;EAEA,IAAI,MAAM,IAAI,GAAG,CAAC;EAClB,IAAI,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;EAClB,IAAI,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;EAClB,IAAI,IAAI,MAAM,GAAG,QAAQ;;EAEzB,IAAI,CAAC,GAAG,CAAC;;EAET,IAAI,GAAG;EACP,QAAQ,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;EAChD,gBAAgB,eAAe,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;;EAEjG,YAAY,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;EAExD,YAAY,IAAI,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC;EACtC,iBAAiB,GAAG,GAAG,MAAM,KAAK,GAAG,KAAK,MAAM,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAClH,gBAAgB,CAAC,GAAG,CAAC;EACrB,gBAAgB,MAAM,GAAG,GAAG;EAC5B;EACA;;EAEA,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI;EAClB,KAAK,QAAQ,CAAC,KAAK,IAAI;;EAEvB,IAAI,OAAO,CAAC;EACZ;;EAEA;EACA,SAAS,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE;EACpC,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;EACrE;;EAEA;EACA,SAAS,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE;EAChD,IAAI,IAAI,CAAC,GAAG,KAAK;EACjB,IAAI,GAAG;EACP,QAAQ,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC;EAClE,QAAQ,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI;EACxB,QAAQ,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI;EACxB,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI;EAClB,KAAK,QAAQ,CAAC,KAAK,KAAK;;EAExB,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;EACxB,IAAI,CAAC,CAAC,KAAK,GAAG,IAAI;;EAElB,IAAI,UAAU,CAAC,CAAC,CAAC;EACjB;;EAEA;EACA;EACA,SAAS,UAAU,CAAC,IAAI,EAAE;EAC1B,IAAI,IAAI,SAAS;EACjB,IAAI,IAAI,MAAM,GAAG,CAAC;;EAElB,IAAI,GAAG;EACP,QAAQ,IAAI,CAAC,GAAG,IAAI;EACpB,QAAQ,IAAI,CAAC;EACb,QAAQ,IAAI,GAAG,IAAI;EACnB,QAAQ,IAAI,IAAI,GAAG,IAAI;EACvB,QAAQ,SAAS,GAAG,CAAC;;EAErB,QAAQ,OAAO,CAAC,EAAE;EAClB,YAAY,SAAS,EAAE;EACvB,YAAY,IAAI,CAAC,GAAG,CAAC;EACrB,YAAY,IAAI,KAAK,GAAG,CAAC;EACzB,YAAY,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;EAC7C,gBAAgB,KAAK,EAAE;EACvB,gBAAgB,CAAC,GAAG,CAAC,CAAC,KAAK;EAC3B,gBAAgB,IAAI,CAAC,CAAC,EAAE;EACxB;EACA,YAAY,IAAI,KAAK,GAAG,MAAM;;EAE9B,YAAY,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE;;EAElD,gBAAgB,IAAI,KAAK,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;EACtE,oBAAoB,CAAC,GAAG,CAAC;EACzB,oBAAoB,CAAC,GAAG,CAAC,CAAC,KAAK;EAC/B,oBAAoB,KAAK,EAAE;EAC3B,iBAAiB,MAAM;EACvB,oBAAoB,CAAC,GAAG,CAAC;EACzB,oBAAoB,CAAC,GAAG,CAAC,CAAC,KAAK;EAC/B,oBAAoB,KAAK,EAAE;EAC3B;;EAEA,gBAAgB,IAAI,IAAI,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC;EACxC,qBAAqB,IAAI,GAAG,CAAC;;EAE7B,gBAAgB,CAAC,CAAC,KAAK,GAAG,IAAI;EAC9B,gBAAgB,IAAI,GAAG,CAAC;EACxB;;EAEA,YAAY,CAAC,GAAG,CAAC;EACjB;;EAEA,QAAQ,IAAI,CAAC,KAAK,GAAG,IAAI;EACzB,QAAQ,MAAM,IAAI,CAAC;;EAEnB,KAAK,QAAQ,SAAS,GAAG,CAAC;;EAE1B,IAAI,OAAO,IAAI;EACf;;EAEA;EACA,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE;EAC3C;EACA,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,IAAI,OAAO,GAAG,CAAC;EAChC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,IAAI,OAAO,GAAG,CAAC;;EAEhC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,UAAU;EACnC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,UAAU;EACnC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,UAAU;EACnC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,UAAU;;EAEnC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,UAAU;EACnC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,UAAU;EACnC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,UAAU;EACnC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,UAAU;;EAEnC,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EACvB;;EAEA;EACA,SAAS,WAAW,CAAC,KAAK,EAAE;EAC5B,IAAI,IAAI,CAAC,GAAG,KAAK;EACjB,QAAQ,QAAQ,GAAG,KAAK;EACxB,IAAI,GAAG;EACP,QAAQ,IAAI,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC;EACtF,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI;EAClB,KAAK,QAAQ,CAAC,KAAK,KAAK;;EAExB,IAAI,OAAO,QAAQ;EACnB;;EAEA;EACA,SAAS,eAAe,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;EACzD,IAAI,OAAO,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;EACzD,WAAW,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;EACzD,WAAW,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;EACzD;;EAEA;EACA,SAAS,0BAA0B,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;EACpE,IAAI,OAAO,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,IAAI,eAAe,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACvF;;EAEA;EACA,SAAS,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE;EAC/B,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;EAC3E,YAAY,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC;EAC5E,aAAa,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;EAC3D,YAAY,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;EACxF;;EAEA;EACA,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;EACvB,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAChE;;EAEA;EACA,SAAS,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE;EACxB,IAAI,OAAO,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;EACzC;;EAEA;EACA,SAAS,UAAU,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;EACpC,IAAI,MAAM,EAAE,GAAGC,MAAI,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EACrC,IAAI,MAAM,EAAE,GAAGA,MAAI,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EACrC,IAAI,MAAM,EAAE,GAAGA,MAAI,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EACrC,IAAI,MAAM,EAAE,GAAGA,MAAI,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;;EAErC,IAAI,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,OAAO,IAAI,CAAC;;EAE5C,IAAI,IAAI,EAAE,KAAK,CAAC,IAAI,SAAS,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,OAAO,IAAI,CAAC;EACvD,IAAI,IAAI,EAAE,KAAK,CAAC,IAAI,SAAS,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,OAAO,IAAI,CAAC;EACvD,IAAI,IAAI,EAAE,KAAK,CAAC,IAAI,SAAS,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,OAAO,IAAI,CAAC;EACvD,IAAI,IAAI,EAAE,KAAK,CAAC,IAAI,SAAS,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,OAAO,IAAI,CAAC;;EAEvD,IAAI,OAAO,KAAK;EAChB;;EAEA;EACA,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;EAC5B,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC3H;;EAEA,SAASA,MAAI,CAAC,GAAG,EAAE;EACnB,IAAI,OAAO,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;EACzC;;EAEA;EACA,SAAS,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE;EACjC,IAAI,IAAI,CAAC,GAAG,CAAC;EACb,IAAI,GAAG;EACP,QAAQ,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;EAC9E,gBAAgB,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,IAAI;EACxD,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI;EAClB,KAAK,QAAQ,CAAC,KAAK,CAAC;;EAEpB,IAAI,OAAO,KAAK;EAChB;;EAEA;EACA,SAAS,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE;EAC7B,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;EACtC,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC;EAC1D,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC;EACxD;;EAEA;EACA,SAAS,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE;EAC5B,IAAI,IAAI,CAAC,GAAG,CAAC;EACb,IAAI,IAAI,MAAM,GAAG,KAAK;EACtB,IAAI,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;EAC9B,IAAI,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;EAC9B,IAAI,GAAG;EACP,QAAQ,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;EAChE,iBAAiB,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC7E,YAAY,MAAM,GAAG,CAAC,MAAM;EAC5B,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI;EAClB,KAAK,QAAQ,CAAC,KAAK,CAAC;;EAEpB,IAAI,OAAO,MAAM;EACjB;;EAEA;EACA;EACA,SAAS,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE;EAC5B,IAAI,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACxC,QAAQ,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACtC,QAAQ,EAAE,GAAG,CAAC,CAAC,IAAI;EACnB,QAAQ,EAAE,GAAG,CAAC,CAAC,IAAI;;EAEnB,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC;EACd,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC;;EAEd,IAAI,EAAE,CAAC,IAAI,GAAG,EAAE;EAChB,IAAI,EAAE,CAAC,IAAI,GAAG,EAAE;;EAEhB,IAAI,EAAE,CAAC,IAAI,GAAG,EAAE;EAChB,IAAI,EAAE,CAAC,IAAI,GAAG,EAAE;;EAEhB,IAAI,EAAE,CAAC,IAAI,GAAG,EAAE;EAChB,IAAI,EAAE,CAAC,IAAI,GAAG,EAAE;;EAEhB,IAAI,OAAO,EAAE;EACb;;EAEA;EACA,SAAS,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE;EACnC,IAAI,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;EAEjC,IAAI,IAAI,CAAC,IAAI,EAAE;EACf,QAAQ,CAAC,CAAC,IAAI,GAAG,CAAC;EAClB,QAAQ,CAAC,CAAC,IAAI,GAAG,CAAC;;EAElB,KAAK,MAAM;EACX,QAAQ,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;EAC1B,QAAQ,CAAC,CAAC,IAAI,GAAG,IAAI;EACrB,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;EAC1B,QAAQ,IAAI,CAAC,IAAI,GAAG,CAAC;EACrB;EACA,IAAI,OAAO,CAAC;EACZ;;EAEA,SAAS,UAAU,CAAC,CAAC,EAAE;EACvB,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI;EACxB,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI;;EAExB,IAAI,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK;EACxC,IAAI,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK;EACxC;;EAEA,SAAS,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;EAC7B,IAAI,OAAO;EACX,QAAQ,CAAC;EACT,QAAQ,CAAC,EAAE,CAAC;EACZ,QAAQ,IAAI,EAAE,IAAI;EAClB,QAAQ,IAAI,EAAE,IAAI;EAClB,QAAQ,CAAC,EAAE,CAAC;EACZ,QAAQ,KAAK,EAAE,IAAI;EACnB,QAAQ,KAAK,EAAE,IAAI;EACnB,QAAQ,OAAO,EAAE,KAAK;EACtB,KAAK;EACL;;EA+BA,SAAS,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE;EAC3C,IAAI,IAAI,GAAG,GAAG,CAAC;EACf,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE;EAC1D,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAChE,QAAQ,CAAC,GAAG,CAAC;EACb;EACA,IAAI,OAAO,GAAG;EACd;;EAEA;EACO,SAAS,OAAO,CAAC,IAAI,EAAE;EAC9B,IAAI,MAAM,QAAQ,GAAG,EAAE;EACvB,IAAI,MAAM,KAAK,GAAG,EAAE;EACpB,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM;EACxC,IAAI,IAAI,SAAS,GAAG,CAAC;EACrB,IAAI,IAAI,OAAO,GAAG,CAAC;;EAEnB,IAAI,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE;EAC7B,QAAQ,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE;EAC9B,YAAY,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpE;EACA,QAAQ,IAAI,OAAO,EAAE;EACrB,YAAY,SAAS,IAAI,OAAO;EAChC,YAAY,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC;EACjC;EACA,QAAQ,OAAO,GAAG,IAAI,CAAC,MAAM;EAC7B;EACA,IAAI,OAAO,CAAC,QAAQ,EAAE,KAAK,EAAE,UAAU,CAAC;EACxC;;ECzqBO,MAAMC,SAAO,GAAG,sBAAsB;EACtC,MAAM,QAAQ,GAAG,SAAS;EAC1B,MAAM,cAAc,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGA,SAAO,IAAIA,SAAO;;EAEzD;EACO,SAAS,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE;EACzC,IAAI,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK;EAC1B,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;EACnB,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;EACnB,IAAI,IAAI,MAAM,GAAG,CAAC;EAClB,IAAI,IAAI,MAAM,GAAG,CAAC;EAClB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE;EAC1C,QAAQ,CAAC,GAAG,IAAI;EAChB,QAAQ,IAAI,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC;EAC1B,KAAK,MAAM;EACX,QAAQ,CAAC,GAAG,IAAI;EAChB,QAAQ,IAAI,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC;EAC1B;EACA,IAAI,IAAI,MAAM,GAAG,CAAC;EAClB,IAAI,IAAI,MAAM,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,EAAE;EACxC,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE;EAC9C,YAAY,IAAI,GAAG,IAAI,GAAG,CAAC;EAC3B,YAAY,EAAE,GAAG,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC;EAClC,YAAY,IAAI,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC;EAC9B,SAAS,MAAM;EACf,YAAY,IAAI,GAAG,IAAI,GAAG,CAAC;EAC3B,YAAY,EAAE,GAAG,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC;EAClC,YAAY,IAAI,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC;EAC9B;EACA,QAAQ,CAAC,GAAG,IAAI;EAChB,QAAQ,IAAI,EAAE,KAAK,CAAC,EAAE;EACtB,YAAY,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE;EAC5B;EACA,QAAQ,OAAO,MAAM,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,EAAE;EAC/C,YAAY,IAAI,CAAC,IAAI,GAAG,IAAI,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE;EAClD,gBAAgB,IAAI,GAAG,CAAC,GAAG,IAAI;EAC/B,gBAAgB,KAAK,GAAG,IAAI,GAAG,CAAC;EAChC,gBAAgB,EAAE,GAAG,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC;EACxD,gBAAgB,IAAI,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC;EAClC,aAAa,MAAM;EACnB,gBAAgB,IAAI,GAAG,CAAC,GAAG,IAAI;EAC/B,gBAAgB,KAAK,GAAG,IAAI,GAAG,CAAC;EAChC,gBAAgB,EAAE,GAAG,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC;EACxD,gBAAgB,IAAI,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC;EAClC;EACA,YAAY,CAAC,GAAG,IAAI;EACpB,YAAY,IAAI,EAAE,KAAK,CAAC,EAAE;EAC1B,gBAAgB,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE;EAChC;EACA;EACA;EACA,IAAI,OAAO,MAAM,GAAG,IAAI,EAAE;EAC1B,QAAQ,IAAI,GAAG,CAAC,GAAG,IAAI;EACvB,QAAQ,KAAK,GAAG,IAAI,GAAG,CAAC;EACxB,QAAQ,EAAE,GAAG,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC;EAChD,QAAQ,IAAI,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC;EAC1B,QAAQ,CAAC,GAAG,IAAI;EAChB,QAAQ,IAAI,EAAE,KAAK,CAAC,EAAE;EACtB,YAAY,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE;EAC5B;EACA;EACA,IAAI,OAAO,MAAM,GAAG,IAAI,EAAE;EAC1B,QAAQ,IAAI,GAAG,CAAC,GAAG,IAAI;EACvB,QAAQ,KAAK,GAAG,IAAI,GAAG,CAAC;EACxB,QAAQ,EAAE,GAAG,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC;EAChD,QAAQ,IAAI,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC;EAC1B,QAAQ,CAAC,GAAG,IAAI;EAChB,QAAQ,IAAI,EAAE,KAAK,CAAC,EAAE;EACtB,YAAY,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE;EAC5B;EACA;EACA,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,MAAM,KAAK,CAAC,EAAE;EACjC,QAAQ,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC;EACvB;EACA,IAAI,OAAO,MAAM;EACjB;;EAsDO,SAAS,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE;EAClC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAChB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;EAC5C,IAAI,OAAO,CAAC;EACZ;;EAEO,SAAS,GAAG,CAAC,CAAC,EAAE;EACvB,IAAI,OAAO,IAAI,YAAY,CAAC,CAAC,CAAC;EAC9B;;ECvIA,MAAM,YAAY,GAAG,CAAC,CAAC,GAAG,EAAE,GAAGA,SAAO,IAAIA,SAAO;EACjD,MAAM,YAAY,GAAG,CAAC,CAAC,GAAG,EAAE,GAAGA,SAAO,IAAIA,SAAO;EACjD,MAAM,YAAY,GAAG,CAAC,CAAC,GAAG,EAAE,GAAGA,SAAO,IAAIA,SAAO,GAAGA,SAAO;;EAE3D,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;EAChB,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;EACjB,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;EAClB,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC;EACjB,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;;EAEhB,SAAS,aAAa,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;EACvD,IAAI,IAAI,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;EAC1C,IAAI,IAAI,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;;EAEpE,IAAI,MAAM,GAAG,GAAG,EAAE,GAAG,EAAE;EACvB,IAAI,MAAM,GAAG,GAAG,EAAE,GAAG,EAAE;EACvB,IAAI,MAAM,GAAG,GAAG,EAAE,GAAG,EAAE;EACvB,IAAI,MAAM,GAAG,GAAG,EAAE,GAAG,EAAE;;EAEvB,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG;EAClB,IAAI,CAAC,GAAG,QAAQ,GAAG,GAAG;EACtB,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;EACvB,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG;EACnB,IAAI,CAAC,GAAG,QAAQ,GAAG,GAAG;EACtB,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;EACvB,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG;EACnB,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EAC7D,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG;EAClB,IAAI,CAAC,GAAG,QAAQ,GAAG,GAAG;EACtB,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;EACvB,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG;EACnB,IAAI,CAAC,GAAG,QAAQ,GAAG,GAAG;EACtB,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;EACvB,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG;EACnB,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EAC7D,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;EAChB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE;EACnB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;EAC3C,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;EAChB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE;EACnB,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC;EACzC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;EAChB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE;EACnB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;EAC3C,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;EAChB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE;EACnB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC;EAC3C,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;;EAEb,IAAI,IAAI,GAAG,GAAG,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EAC5B,IAAI,IAAI,QAAQ,GAAG,YAAY,GAAG,MAAM;EACxC,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAI,CAAC,GAAG,IAAI,QAAQ,EAAE;EAC7C,QAAQ,OAAO,GAAG;EAClB;;EAEA,IAAI,KAAK,GAAG,EAAE,GAAG,GAAG;EACpB,IAAI,OAAO,GAAG,EAAE,IAAI,GAAG,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;EAC/C,IAAI,KAAK,GAAG,EAAE,GAAG,GAAG;EACpB,IAAI,OAAO,GAAG,EAAE,IAAI,GAAG,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;EAC/C,IAAI,KAAK,GAAG,EAAE,GAAG,GAAG;EACpB,IAAI,OAAO,GAAG,EAAE,IAAI,GAAG,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;EAC/C,IAAI,KAAK,GAAG,EAAE,GAAG,GAAG;EACpB,IAAI,OAAO,GAAG,EAAE,IAAI,GAAG,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;;EAE/C,IAAI,IAAI,OAAO,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,EAAE;EAC1E,QAAQ,OAAO,GAAG;EAClB;;EAEA,IAAI,QAAQ,GAAG,YAAY,GAAG,MAAM,GAAG,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;EACrE,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,OAAO,GAAG,GAAG,GAAG,OAAO,KAAK,GAAG,GAAG,OAAO,GAAG,GAAG,GAAG,OAAO,CAAC;EAC5E,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAI,CAAC,GAAG,IAAI,QAAQ,EAAE,OAAO,GAAG;;EAEvD,IAAI,EAAE,GAAG,OAAO,GAAG,GAAG;EACtB,IAAI,CAAC,GAAG,QAAQ,GAAG,OAAO;EAC1B,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;EAC3B,IAAI,GAAG,GAAG,OAAO,GAAG,GAAG;EACvB,IAAI,CAAC,GAAG,QAAQ,GAAG,GAAG;EACtB,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;EACvB,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG;EACnB,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EAC7D,IAAI,EAAE,GAAG,OAAO,GAAG,GAAG;EACtB,IAAI,CAAC,GAAG,QAAQ,GAAG,OAAO;EAC1B,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;EAC3B,IAAI,GAAG,GAAG,OAAO,GAAG,GAAG;EACvB,IAAI,CAAC,GAAG,QAAQ,GAAG,GAAG;EACtB,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;EACvB,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG;EACnB,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EAC7D,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;EAChB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE;EACnB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;EAC3C,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;EAChB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE;EACnB,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC;EACzC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;EAChB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE;EACnB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;EAC3C,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;EAChB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE;EACnB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC;EAC3C,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;EACb,IAAI,MAAM,KAAK,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;;EAErC,IAAI,EAAE,GAAG,GAAG,GAAG,OAAO;EACtB,IAAI,CAAC,GAAG,QAAQ,GAAG,GAAG;EACtB,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;EACvB,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG;EACnB,IAAI,CAAC,GAAG,QAAQ,GAAG,OAAO;EAC1B,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;EAC3B,IAAI,GAAG,GAAG,OAAO,GAAG,GAAG;EACvB,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EAC7D,IAAI,EAAE,GAAG,GAAG,GAAG,OAAO;EACtB,IAAI,CAAC,GAAG,QAAQ,GAAG,GAAG;EACtB,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;EACvB,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG;EACnB,IAAI,CAAC,GAAG,QAAQ,GAAG,OAAO;EAC1B,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;EAC3B,IAAI,GAAG,GAAG,OAAO,GAAG,GAAG;EACvB,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EAC7D,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;EAChB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE;EACnB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;EAC3C,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;EAChB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE;EACnB,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC;EACzC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;EAChB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE;EACnB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;EAC3C,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;EAChB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE;EACnB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC;EAC3C,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;EACb,IAAI,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;;EAE1C,IAAI,EAAE,GAAG,OAAO,GAAG,OAAO;EAC1B,IAAI,CAAC,GAAG,QAAQ,GAAG,OAAO;EAC1B,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;EAC3B,IAAI,GAAG,GAAG,OAAO,GAAG,GAAG;EACvB,IAAI,CAAC,GAAG,QAAQ,GAAG,OAAO;EAC1B,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;EAC3B,IAAI,GAAG,GAAG,OAAO,GAAG,GAAG;EACvB,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EAC7D,IAAI,EAAE,GAAG,OAAO,GAAG,OAAO;EAC1B,IAAI,CAAC,GAAG,QAAQ,GAAG,OAAO;EAC1B,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;EAC3B,IAAI,GAAG,GAAG,OAAO,GAAG,GAAG;EACvB,IAAI,CAAC,GAAG,QAAQ,GAAG,OAAO;EAC1B,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;EAC3B,IAAI,GAAG,GAAG,OAAO,GAAG,GAAG;EACvB,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EAC7D,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;EAChB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE;EACnB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;EAC3C,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;EAChB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE;EACnB,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC;EACzC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;EAChB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE;EACnB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;EAC3C,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;EAChB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE;EACnB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC;EAC3C,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;EACb,IAAI,MAAM,IAAI,GAAG,GAAG,CAAC,KAAK,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;EAExC,IAAI,OAAO,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;EACtB;;EAEO,SAAS,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;EACjD,IAAI,MAAM,OAAO,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;EACzC,IAAI,MAAM,QAAQ,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;EAC1C,IAAI,MAAM,GAAG,GAAG,OAAO,GAAG,QAAQ;;EAElC,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,QAAQ,CAAC;EAC/C,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,YAAY,GAAG,MAAM,EAAE,OAAO,GAAG;;EAE1D,IAAI,OAAO,CAAC,aAAa,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC;EACzD;;EClLA,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;EAChC,MAAM,UAAU,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC;;EAIxB,MAAM,UAAU,CAAC;;EAEhC,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE,IAAI,GAAG,WAAW,EAAE,IAAI,GAAG,WAAW,EAAE;EAChE,QAAQ,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM;EAC/B,QAAQ,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC;;EAE9C,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;EACpC,YAAY,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;EAC/B,YAAY,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;EACnC,YAAY,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;EACvC;;EAEA,QAAQ,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC;EACrC;;EAEA,IAAI,WAAW,CAAC,MAAM,EAAE;EACxB,QAAQ,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC;EACpC,QAAQ,IAAI,CAAC,GAAG,CAAC,IAAI,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC;;EAE1G,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM;;EAE5B;EACA,QAAQ,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;EACnD,QAAQ,IAAI,CAAC,UAAU,GAAG,IAAI,WAAW,CAAC,YAAY,GAAG,CAAC,CAAC;EAC3D,QAAQ,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU,CAAC,YAAY,GAAG,CAAC,CAAC;;EAE1D;EACA,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;EAChD,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;EAC5C,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;EAC5C,QAAQ,IAAI,CAAC,QAAQ,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;EAC3C,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;;EAExD;EACA,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC;EACtC,QAAQ,IAAI,CAAC,MAAM,GAAG,IAAI,YAAY,CAAC,CAAC,CAAC;;EAEzC,QAAQ,IAAI,CAAC,MAAM,EAAE;EACrB;;EAEA,IAAI,MAAM,GAAG;EACb,QAAQ,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC,IAAI,IAAI;EAChH,QAAQ,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC;;EAEpC;EACA,QAAQ,IAAI,IAAI,GAAG,QAAQ;EAC3B,QAAQ,IAAI,IAAI,GAAG,QAAQ;EAC3B,QAAQ,IAAI,IAAI,GAAG,CAAC,QAAQ;EAC5B,QAAQ,IAAI,IAAI,GAAG,CAAC,QAAQ;;EAE5B,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;EACpC,YAAY,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;EACnC,YAAY,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACvC,YAAY,IAAI,CAAC,GAAG,IAAI,EAAE,IAAI,GAAG,CAAC;EAClC,YAAY,IAAI,CAAC,GAAG,IAAI,EAAE,IAAI,GAAG,CAAC;EAClC,YAAY,IAAI,CAAC,GAAG,IAAI,EAAE,IAAI,GAAG,CAAC;EAClC,YAAY,IAAI,CAAC,GAAG,IAAI,EAAE,IAAI,GAAG,CAAC;EAClC,YAAY,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;EAC5B;EACA,QAAQ,MAAM,EAAE,GAAG,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC;EACpC,QAAQ,MAAM,EAAE,GAAG,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC;;EAEpC,QAAQ,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE;;EAEtB;EACA,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,OAAO,GAAG,QAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;EACxD,YAAY,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EACpE,YAAY,IAAI,CAAC,GAAG,OAAO,EAAE;EAC7B,gBAAgB,EAAE,GAAG,CAAC;EACtB,gBAAgB,OAAO,GAAG,CAAC;EAC3B;EACA;EACA,QAAQ,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;EAClC,QAAQ,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;;EAEtC;EACA,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,OAAO,GAAG,QAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;EACxD,YAAY,IAAI,CAAC,KAAK,EAAE,EAAE;EAC1B,YAAY,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EACtE,YAAY,IAAI,CAAC,GAAG,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE;EACtC,gBAAgB,EAAE,GAAG,CAAC;EACtB,gBAAgB,OAAO,GAAG,CAAC;EAC3B;EACA;EACA,QAAQ,IAAI,GAAG,GAAG,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;EAChC,QAAQ,IAAI,GAAG,GAAG,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;;EAEpC,QAAQ,IAAI,SAAS,GAAG,QAAQ;;EAEhC;EACA,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;EACpC,YAAY,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE;EACtC,YAAY,MAAM,CAAC,GAAG,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EACxF,YAAY,IAAI,CAAC,GAAG,SAAS,EAAE;EAC/B,gBAAgB,EAAE,GAAG,CAAC;EACtB,gBAAgB,SAAS,GAAG,CAAC;EAC7B;EACA;EACA,QAAQ,IAAI,GAAG,GAAG,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;EAChC,QAAQ,IAAI,GAAG,GAAG,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;;EAEpC,QAAQ,IAAI,SAAS,KAAK,QAAQ,EAAE;EACpC;EACA;EACA,YAAY,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;EACxC,gBAAgB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/F;EACA,YAAY,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;EACvD,YAAY,MAAM,IAAI,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC;EAC3C,YAAY,IAAI,CAAC,GAAG,CAAC;EACrB,YAAY,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;EACxD,gBAAgB,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;EACvC,gBAAgB,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;EACzC,gBAAgB,IAAI,CAAC,GAAG,EAAE,EAAE;EAC5B,oBAAoB,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE;EAClC,oBAAoB,EAAE,GAAG,CAAC;EAC1B;EACA;EACA,YAAY,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EAC3C,YAAY,IAAI,CAAC,SAAS,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC;EAC/C,YAAY,IAAI,CAAC,SAAS,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC;EAC/C,YAAY;EACZ;;EAEA;EACA,QAAQ,IAAI,QAAQ,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE;EACxD,YAAY,MAAM,CAAC,GAAG,EAAE;EACxB,YAAY,MAAM,CAAC,GAAG,GAAG;EACzB,YAAY,MAAM,CAAC,GAAG,GAAG;EACzB,YAAY,EAAE,GAAG,EAAE;EACnB,YAAY,GAAG,GAAG,GAAG;EACrB,YAAY,GAAG,GAAG,GAAG;EACrB,YAAY,EAAE,GAAG,CAAC;EAClB,YAAY,GAAG,GAAG,CAAC;EACnB,YAAY,GAAG,GAAG,CAAC;EACnB;;EAEA,QAAQ,MAAM,MAAM,GAAG,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACjE,QAAQ,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC;EAC3B,QAAQ,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC;;EAE3B,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;EACpC,YAAY,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;EACvF;;EAEA;EACA,QAAQ,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;;EAEnD;EACA,QAAQ,IAAI,CAAC,UAAU,GAAG,EAAE;EAC5B,QAAQ,IAAI,QAAQ,GAAG,CAAC;;EAExB,QAAQ,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE;EACxC,QAAQ,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE;EACxC,QAAQ,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE;;EAExC,QAAQ,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC;EACvB,QAAQ,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC;EACvB,QAAQ,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC;;EAEvB,QAAQ,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;EACzB,QAAQ,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE;EAC9C,QAAQ,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE;EAC9C,QAAQ,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE;;EAE9C,QAAQ,IAAI,CAAC,YAAY,GAAG,CAAC;EAC7B,QAAQ,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;EAEjD,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EAC3D,YAAY,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;EAClC,YAAY,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;EACnC,YAAY,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;;EAEvC;EACA,YAAY,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,OAAO,EAAE;EACrF,YAAY,EAAE,GAAG,CAAC;EAClB,YAAY,EAAE,GAAG,CAAC;;EAElB;EACA,YAAY,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE;;EAElD;EACA,YAAY,IAAI,KAAK,GAAG,CAAC;EACzB,YAAY,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,EAAE;EAChF,gBAAgB,KAAK,GAAG,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC;EAC5D,gBAAgB,IAAI,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,QAAQ,CAAC,KAAK,CAAC,EAAE;EAC/D;;EAEA,YAAY,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;EACnC,YAAY,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC;EAC5B,YAAY,OAAO,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;EAC7H,gBAAgB,CAAC,GAAG,CAAC;EACrB,gBAAgB,IAAI,CAAC,KAAK,KAAK,EAAE;EACjC,oBAAoB,CAAC,GAAG,EAAE;EAC1B,oBAAoB;EACpB;EACA;EACA,YAAY,IAAI,CAAC,KAAK,EAAE,EAAE,SAAS;;EAEnC;EACA,YAAY,IAAI,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;;EAE5E;EACA,YAAY,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;EAC9C,YAAY,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAC3B,YAAY,QAAQ,EAAE;;EAEtB;EACA,YAAY,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;EAC/B,YAAY,OAAO,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;EAC5H,gBAAgB,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;EAC1E,gBAAgB,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;EAClD,gBAAgB,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAChC,gBAAgB,QAAQ,EAAE;EAC1B,gBAAgB,CAAC,GAAG,CAAC;EACrB;;EAEA;EACA,YAAY,IAAI,CAAC,KAAK,KAAK,EAAE;EAC7B,gBAAgB,OAAO,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;EAChI,oBAAoB,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;EAC9E,oBAAoB,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;EACzC,oBAAoB,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EAClC,oBAAoB,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACpC,oBAAoB,QAAQ,EAAE;EAC9B,oBAAoB,CAAC,GAAG,CAAC;EACzB;EACA;;EAEA;EACA,YAAY,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;EAC7C,YAAY,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;EACzC,YAAY,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;;EAE3B;EACA,YAAY,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;EAC7C,YAAY,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;EACzE;;EAEA,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,WAAW,CAAC,QAAQ,CAAC;EAC7C,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;EAChE,YAAY,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;EAC5B,YAAY,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;EAC3B;;EAEA;EACA,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC;EACvE,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC;EACvE;;EAEA,IAAI,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE;EACnB,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS;EACpG;;EAEA,IAAI,SAAS,CAAC,CAAC,EAAE;EACjB,QAAQ,MAAM,CAAC,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,CAAC,GAAG,IAAI;;EAE3E,QAAQ,IAAI,CAAC,GAAG,CAAC;EACjB,QAAQ,IAAI,EAAE,GAAG,CAAC;;EAElB;EACA,QAAQ,OAAO,IAAI,EAAE;EACrB,YAAY,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;;EAElC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,YAAY,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;EAChC,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;;EAEjC,YAAY,IAAI,CAAC,KAAK,EAAE,EAAE;EAC1B,gBAAgB,IAAI,CAAC,KAAK,CAAC,EAAE;EAC7B,gBAAgB,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC;EACnC,gBAAgB;EAChB;;EAEA,YAAY,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;EAChC,YAAY,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;EACvC,YAAY,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;;EAEvC,YAAY,MAAM,EAAE,GAAG,SAAS,CAAC,EAAE,CAAC;EACpC,YAAY,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC;EACnC,YAAY,MAAM,EAAE,GAAG,SAAS,CAAC,EAAE,CAAC;EACpC,YAAY,MAAM,EAAE,GAAG,SAAS,CAAC,EAAE,CAAC;;EAEpC,YAAY,MAAM,OAAO,GAAG,QAAQ;EACpC,gBAAgB,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;EAClD,gBAAgB,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;EAClD,gBAAgB,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;EAClD,gBAAgB,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;;EAEnD,YAAY,IAAI,OAAO,EAAE;EACzB,gBAAgB,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EACjC,gBAAgB,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;;EAEjC,gBAAgB,MAAM,GAAG,GAAG,SAAS,CAAC,EAAE,CAAC;;EAEzC;EACA,gBAAgB,IAAI,GAAG,KAAK,EAAE,EAAE;EAChC,oBAAoB,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU;EAC3C,oBAAoB,GAAG;EACvB,wBAAwB,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;EACrD,4BAA4B,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;EAChD,4BAA4B;EAC5B;EACA,wBAAwB,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;EAC7C,qBAAqB,QAAQ,CAAC,KAAK,IAAI,CAAC,UAAU;EAClD;EACA,gBAAgB,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC;EAClC,gBAAgB,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC;EAC5C,gBAAgB,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;;EAElC,gBAAgB,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;;EAE3C;EACA,gBAAgB,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE;EAC3C,oBAAoB,UAAU,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE;EACxC;EACA,aAAa,MAAM;EACnB,gBAAgB,IAAI,CAAC,KAAK,CAAC,EAAE;EAC7B,gBAAgB,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC;EACnC;EACA;;EAEA,QAAQ,OAAO,EAAE;EACjB;;EAEA,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;EAChB,QAAQ,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;EAC9B,QAAQ,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;EAC5C;;EAEA;EACA,IAAI,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;EACtC,QAAQ,MAAM,CAAC,GAAG,IAAI,CAAC,YAAY;;EAEnC,QAAQ,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE;EAC/B,QAAQ,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE;EACnC,QAAQ,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE;;EAEnC,QAAQ,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACxB,QAAQ,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;EAC5B,QAAQ,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;;EAE5B,QAAQ,IAAI,CAAC,YAAY,IAAI,CAAC;;EAE9B,QAAQ,OAAO,CAAC;EAChB;EACA;;EAEA;EACA,SAAS,WAAW,CAAC,EAAE,EAAE,EAAE,EAAE;EAC7B,IAAI,MAAM,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;EAChD,IAAI,OAAO,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;EACxC;;EAEA,SAAS,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;EAC9B,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;EACtB,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;EACtB,IAAI,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;EAC5B;;EAEA,SAAS,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;EAClD,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;EACtB,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;EACtB,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;EACtB,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;EACtB,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;EACtB,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;;EAEtB,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;EAChC,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;EAChC,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;;EAEhC,IAAI,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;EACnC,WAAW,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;EACnC,WAAW,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;EACvC;;EAEA,SAAS,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;EAC9C,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;EACtB,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;EACtB,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;EACtB,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;;EAEtB,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;EAChC,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;EAChC,IAAI,MAAM,CAAC,GAAG,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;;EAEvC,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;EACrC,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;;EAErC,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;EACxB;;EAEA,SAAS,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;EAC9C,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;EACtB,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;EACtB,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;EACtB,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;;EAEtB,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;EAChC,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;EAChC,IAAI,MAAM,CAAC,GAAG,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;;EAEvC,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;EAC1C,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;;EAE1C,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EACjB;;EAEA,SAAS,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE;EAC5C,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,EAAE,EAAE;EAC5B,QAAQ,KAAK,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE;EAChD,YAAY,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;EAC/B,YAAY,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC;EACxC,YAAY,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;EACzB,YAAY,OAAO,CAAC,IAAI,IAAI,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC;EAC/E,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;EAC7B;EACA,KAAK,MAAM;EACX,QAAQ,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,KAAK,KAAK,CAAC;EAC1C,QAAQ,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC;EACxB,QAAQ,IAAI,CAAC,GAAG,KAAK;EACrB,QAAQ,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,CAAC,CAAC;EAC5B,QAAQ,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC;EACxE,QAAQ,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC;EAClE,QAAQ,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC;;EAEhE,QAAQ,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;EAC3B,QAAQ,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC;EACpC,QAAQ,OAAO,IAAI,EAAE;EACrB,YAAY,GAAG,CAAC,EAAE,CAAC,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ;EACnD,YAAY,GAAG,CAAC,EAAE,CAAC,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ;EACnD,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE;EACvB,YAAY,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAC3B;EACA,QAAQ,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;EAC9B,QAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI;;EAErB,QAAQ,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE;EACvC,YAAY,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC;EAC3C,YAAY,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC;EAC9C,SAAS,MAAM;EACf,YAAY,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC;EAC9C,YAAY,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC;EAC3C;EACA;EACA;;EAEA,SAAS,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE;EACzB,IAAI,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;EACtB,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;EACnB,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EAChB;;EAEA,SAAS,WAAW,CAAC,CAAC,EAAE;EACxB,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;EACf;EACA,SAAS,WAAW,CAAC,CAAC,EAAE;EACxB,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;EACf;;EC7dA,SAAS,cAAc,CAAC,CAAC,EAAE,OAAO,EAAE;EACpC,IAAI,IAAI,CAAC;EACT,IAAI,IAAI,EAAE;EACV,IAAI,IAAI,CAAC,GAAG,CAAC;EACb,IAAI,IAAI,CAAC;EACT,IAAI,IAAI,EAAE;EACV,IAAI,IAAI,EAAE;EACV,IAAI,IAAI,EAAE;EACV,IAAI,IAAI,EAAE;EACV,IAAI,IAAI,QAAQ;EAChB,IAAI,IAAI,KAAK;;EAEb,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAChB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;EAEhB,IAAI,IAAI,WAAW,GAAG,OAAO,CAAC,MAAM;EACpC,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE;EACtC,QAAQ,EAAE,GAAG,CAAC;EACd,QAAQ,IAAI,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC;EAChC,QAAQ,IAAI,UAAU,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC;;EAE3C,QAAQ,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC;EAC7B,QAAQ,IAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;EAClD,YAAY,QAAQ,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;EACpD,YAAY,MAAM,IAAI,KAAK,CAAC,uDAAuD;EACnF;;EAEA,QAAQ,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;EAC5B,QAAQ,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;;EAE5B,QAAQ,KAAK,EAAE,EAAE,EAAE,GAAG,UAAU,EAAE,EAAE,EAAE,EAAE;EACxC,YAAY,KAAK,GAAG,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC;;EAEnC,YAAY,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;EAC7B,YAAY,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;;EAE7B,YAAY,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;EACtC,gBAAgB,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC;EAC5E,aAAa,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE;EACrE,gBAAgB,CAAC,GAAG,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;EAClD,gBAAgB,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,OAAO,CAAC;EACvC,gBAAgB,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;EACxF;EACA,YAAY,QAAQ,GAAG,KAAK;EAC5B,YAAY,EAAE,GAAG,EAAE;EACnB,YAAY,EAAE,GAAG,EAAE;EACnB;EACA;;EAEA,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,OAAO,KAAK;EACnC,IAAI,OAAO;EACX;;ECrDA;EAEA,SAAS,QAAQ,CAAC,KAAK,EAAE;EACzB,EAAE,IAAI,CAAC,KAAK,EAAE;EACd,IAAI,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC;EACxC;EACA,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;EAC7B,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,KAAK,OAAO,EAAE;EAChG,MAAM,OAAO,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC;EAC5C;EACA,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;EAChC,MAAM,OAAO,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC;EACnC;EACA;EACA,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;EACzG,IAAI,OAAO,CAAC,GAAG,KAAK,CAAC;EACrB;EACA,EAAE,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC;EACvE;EAgFA,SAAS,OAAO,CAAC,OAAO,EAAE;EAC1B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;EAClC,IAAI,OAAO,OAAO,CAAC,QAAQ;EAC3B;EACA,EAAE,OAAO,OAAO;EAChB;;ECvGA;EAGA,SAAS,qBAAqB,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,GAAG,EAAE,EAAE;EAC7D,EAAE,IAAI,CAAC,KAAK,EAAE;EACd,IAAI,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC;EACxC;EACA,EAAE,IAAI,CAAC,OAAO,EAAE;EAChB,IAAI,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC;EAC1C;EACA,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC;EAC5B,EAAE,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC;EAC/B,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI;EACxB,EAAE,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI;EAC3B,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,WAAW;EAC9B,EAAE,IAAI,IAAI,IAAI,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,KAAK,EAAE;EAC1C,IAAI,OAAO,KAAK;EAChB;EACA,EAAE,IAAI,IAAI,KAAK,SAAS,EAAE;EAC1B,IAAI,KAAK,GAAG,CAAC,KAAK,CAAC;EACnB;EACA,EAAE,IAAI,MAAM,GAAG,KAAK;EACpB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;EACzC,IAAI,MAAM,UAAU,GAAGC,cAAG,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;EACxC,IAAI,IAAI,UAAU,KAAK,CAAC,EAAE,OAAO,OAAO,CAAC,cAAc,GAAG,KAAK,GAAG,IAAI;EACtE,SAAS,IAAI,UAAU,EAAE,MAAM,GAAG,IAAI;EACtC;EACA,EAAE,OAAO,MAAM;EACf;EACA,SAAS,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE;EAC1B,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;EACrF;EACA,IAAI,qCAAqC,GAAG,qBAAqB;;EChC1D,IAAID,SAAO,GAAG,IAAI;EAClB,IAAI,QAAQ,GAAG,KAAK;EACpB,IAAIE,IAAE,GAAG,IAAI,CAAC,EAAE;EAChB,IAAIC,QAAM,GAAGD,IAAE,GAAG,CAAC;EACnB,IAAI,SAAS,GAAGA,IAAE,GAAG,CAAC;EACtB,IAAIE,KAAG,GAAGF,IAAE,GAAG,CAAC;;EAEhB,IAAIG,SAAO,GAAG,GAAG,GAAGH,IAAE;EACtB,IAAII,SAAO,GAAGJ,IAAE,GAAG,GAAG;;EAEtB,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG;EAClB,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI;EACpB,IAAIK,OAAK,GAAG,IAAI,CAAC,KAAK;EACtB,IAAIC,KAAG,GAAG,IAAI,CAAC,GAAG;EAIlB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK;EAGtB,IAAIC,KAAG,GAAG,IAAI,CAAC,GAAG;EAClB,IAAIV,MAAI,GAAG,IAAI,CAAC,IAAI,IAAI,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE;EAC1E,IAAIW,MAAI,GAAG,IAAI,CAAC,IAAI;;EAGpB,SAAS,IAAI,CAAC,CAAC,EAAE;EACxB,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,GAAGR,IAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;EAC/C;;EAEO,SAASS,MAAI,CAAC,CAAC,EAAE;EACxB,EAAE,OAAO,CAAC,GAAG,CAAC,GAAGR,QAAM,GAAG,CAAC,GAAG,EAAE,GAAG,CAACA,QAAM,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;EACzD;;EAEO,SAAS,QAAQ,CAAC,CAAC,EAAE;EAC5B,EAAE,OAAO,CAAC,CAAC,GAAGM,KAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;EAC7B;;ECnCe,SAAS,IAAI,GAAG;;ECA/B,SAAS,cAAc,CAAC,QAAQ,EAAE,MAAM,EAAE;EAC1C,EAAE,IAAI,QAAQ,IAAI,kBAAkB,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;EACpE,IAAI,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC;EACvD;EACA;;EAEA,IAAI,gBAAgB,GAAG;EACvB,EAAE,OAAO,EAAE,SAAS,MAAM,EAAE,MAAM,EAAE;EACpC,IAAI,cAAc,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC;EAC3C,GAAG;EACH,EAAE,iBAAiB,EAAE,SAAS,MAAM,EAAE,MAAM,EAAE;EAC9C,IAAI,IAAI,QAAQ,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM;EAC/D,IAAI,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC;EAChE;EACA,CAAC;;EAED,IAAI,kBAAkB,GAAG;EACzB,EAAE,MAAM,EAAE,SAAS,MAAM,EAAE,MAAM,EAAE;EACnC,IAAI,MAAM,CAAC,MAAM,EAAE;EACnB,GAAG;EACH,EAAE,KAAK,EAAE,SAAS,MAAM,EAAE,MAAM,EAAE;EAClC,IAAI,MAAM,GAAG,MAAM,CAAC,WAAW;EAC/B,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;EACjD,GAAG;EACH,EAAE,UAAU,EAAE,SAAS,MAAM,EAAE,MAAM,EAAE;EACvC,IAAI,IAAI,WAAW,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM;EACxE,IAAI,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;EAC1F,GAAG;EACH,EAAE,UAAU,EAAE,SAAS,MAAM,EAAE,MAAM,EAAE;EACvC,IAAI,UAAU,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,EAAE,CAAC,CAAC;EAC7C,GAAG;EACH,EAAE,eAAe,EAAE,SAAS,MAAM,EAAE,MAAM,EAAE;EAC5C,IAAI,IAAI,WAAW,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM;EACxE,IAAI,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;EACzD,GAAG;EACH,EAAE,OAAO,EAAE,SAAS,MAAM,EAAE,MAAM,EAAE;EACpC,IAAI,aAAa,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC;EAC7C,GAAG;EACH,EAAE,YAAY,EAAE,SAAS,MAAM,EAAE,MAAM,EAAE;EACzC,IAAI,IAAI,WAAW,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM;EACxE,IAAI,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC;EACzD,GAAG;EACH,EAAE,kBAAkB,EAAE,SAAS,MAAM,EAAE,MAAM,EAAE;EAC/C,IAAI,IAAI,UAAU,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM;EACrE,IAAI,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC;EACzD;EACA,CAAC;;EAED,SAAS,UAAU,CAAC,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE;EACjD,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,GAAG,MAAM,EAAE,UAAU;EACzD,EAAE,MAAM,CAAC,SAAS,EAAE;EACpB,EAAE,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;EACxG,EAAE,MAAM,CAAC,OAAO,EAAE;EAClB;;EAEA,SAAS,aAAa,CAAC,WAAW,EAAE,MAAM,EAAE;EAC5C,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM;EACpC,EAAE,MAAM,CAAC,YAAY,EAAE;EACvB,EAAE,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;EACvD,EAAE,MAAM,CAAC,UAAU,EAAE;EACrB;;EAEe,kBAAQ,CAAC,MAAM,EAAE,MAAM,EAAE;EACxC,EAAE,IAAI,MAAM,IAAI,gBAAgB,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;EAC9D,IAAI,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC;EACjD,GAAG,MAAM;EACT,IAAI,cAAc,CAAC,MAAM,EAAE,MAAM,CAAC;EAClC;EACA;;EC/DO,IAAI,WAAW,GAAG,IAAI,KAAK,EAAE;;EAEpC;;EAEA,IAAI,OAAO,GAAG,IAAI,KAAK,EAAE;EACzB,IAAIG,UAAQ;EACZ,IAAIC,OAAK;EACT,IAAIC,SAAO;EACX,IAAIC,SAAO;EACX,IAAIC,SAAO;;EAEJ,IAAI,UAAU,GAAG;EACxB,EAAE,KAAK,EAAE,IAAI;EACb,EAAE,SAAS,EAAE,IAAI;EACjB,EAAE,OAAO,EAAE,IAAI;EACf,EAAE,YAAY,EAAE,WAAW;EAC3B,IAAI,WAAW,GAAG,IAAI,KAAK,EAAE;EAC7B,IAAI,UAAU,CAAC,SAAS,GAAG,aAAa;EACxC,IAAI,UAAU,CAAC,OAAO,GAAG,WAAW;EACpC,GAAG;EACH,EAAE,UAAU,EAAE,WAAW;EACzB,IAAI,IAAI,QAAQ,GAAG,CAAC,WAAW;EAC/B,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,GAAG,CAAC,GAAGZ,KAAG,GAAG,QAAQ,GAAG,QAAQ,CAAC;EACzD,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI;EACrD,GAAG;EACH,EAAE,MAAM,EAAE,WAAW;EACrB,IAAI,OAAO,CAAC,GAAG,CAACA,KAAG,CAAC;EACpB;EACA,CAAC;;EAED,SAAS,aAAa,GAAG;EACzB,EAAE,UAAU,CAAC,KAAK,GAAG,cAAc;EACnC;;EAEA,SAAS,WAAW,GAAG;EACvB,EAAE,SAAS,CAACQ,UAAQ,EAAEC,OAAK,CAAC;EAC5B;;EAEA,SAAS,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE;EACrC,EAAE,UAAU,CAAC,KAAK,GAAG,SAAS;EAC9B,EAAED,UAAQ,GAAG,MAAM,EAAEC,OAAK,GAAG,GAAG;EAChC,EAAE,MAAM,IAAIP,SAAO,EAAE,GAAG,IAAIA,SAAO;EACnC,EAAEQ,SAAO,GAAG,MAAM,EAAEC,SAAO,GAAGP,KAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,SAAS,CAAC,EAAEQ,SAAO,GAAGP,KAAG,CAAC,GAAG,CAAC;EAChF;;EAEA,SAAS,SAAS,CAAC,MAAM,EAAE,GAAG,EAAE;EAChC,EAAE,MAAM,IAAIH,SAAO,EAAE,GAAG,IAAIA,SAAO;EACnC,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,SAAS,CAAC;;EAE5B;EACA;EACA;EACA,EAAE,IAAI,OAAO,GAAG,MAAM,GAAGQ,SAAO;EAChC,MAAM,QAAQ,GAAG,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;EACtC,MAAM,QAAQ,GAAG,QAAQ,GAAG,OAAO;EACnC,MAAM,MAAM,GAAGN,KAAG,CAAC,GAAG,CAAC;EACvB,MAAM,MAAM,GAAGC,KAAG,CAAC,GAAG,CAAC;EACvB,MAAM,CAAC,GAAGO,SAAO,GAAG,MAAM;EAC1B,MAAM,CAAC,GAAGD,SAAO,GAAG,MAAM,GAAG,CAAC,GAAGP,KAAG,CAAC,QAAQ,CAAC;EAC9C,MAAM,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAGC,KAAG,CAAC,QAAQ,CAAC;EACtC,EAAE,WAAW,CAAC,GAAG,CAACF,OAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;EAE9B;EACA,EAAEO,SAAO,GAAG,MAAM,EAAEC,SAAO,GAAG,MAAM,EAAEC,SAAO,GAAG,MAAM;EACtD;;ECnEO,SAASC,WAAS,CAAC,SAAS,EAAE;EACrC,EAAE,OAAO,CAACV,OAAK,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EAAEI,MAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;EAChE;;EAEO,SAASO,WAAS,CAAC,SAAS,EAAE;EACrC,EAAE,IAAI,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,MAAM,GAAGV,KAAG,CAAC,GAAG,CAAC;EAClE,EAAE,OAAO,CAAC,MAAM,GAAGA,KAAG,CAAC,MAAM,CAAC,EAAE,MAAM,GAAGC,KAAG,CAAC,MAAM,CAAC,EAAEA,KAAG,CAAC,GAAG,CAAC,CAAC;EAC/D;;EAEO,SAASU,cAAY,CAAC,CAAC,EAAE,CAAC,EAAE;EACnC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAChD;;EAEO,SAASC,gBAAc,CAAC,CAAC,EAAE,CAAC,EAAE;EACrC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1F;;EAEA;EACO,SAAS,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE;EAC1C,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;EAC1C;;EAEO,SAAS,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE;EAC1C,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACtD;;EAEA;EACO,SAAS,yBAAyB,CAAC,CAAC,EAAE;EAC7C,EAAE,IAAI,CAAC,GAAGV,MAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACvD,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;EACjC;;EC1BA,IAAII,SAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI;EAChC,IAAI,OAAO;EACX,IAAIF,UAAQ,EAAEC,OAAK;EACnB,IAAI,EAAE;EACN,IAAI,QAAQ;EACZ,IAAI,MAAM;EACV,IAAI,KAAK;;EAET,IAAIQ,cAAY,GAAG;EACnB,EAAE,KAAK,EAAEC,aAAW;EACpB,EAAE,SAAS,EAAE,eAAe;EAC5B,EAAE,OAAO,EAAE,aAAa;EACxB,EAAE,YAAY,EAAE,WAAW;EAC3B,IAAID,cAAY,CAAC,KAAK,GAAG,eAAe;EACxC,IAAIA,cAAY,CAAC,SAAS,GAAG,eAAe;EAC5C,IAAIA,cAAY,CAAC,OAAO,GAAG,aAAa;EACxC,IAAI,QAAQ,GAAG,IAAI,KAAK,EAAE;EAC1B,IAAI,UAAU,CAAC,YAAY,EAAE;EAC7B,GAAG;EACH,EAAE,UAAU,EAAE,WAAW;EACzB,IAAI,UAAU,CAAC,UAAU,EAAE;EAC3B,IAAIA,cAAY,CAAC,KAAK,GAAGC,aAAW;EACpC,IAAID,cAAY,CAAC,SAAS,GAAG,eAAe;EAC5C,IAAIA,cAAY,CAAC,OAAO,GAAG,aAAa;EACxC,IAAI,IAAI,WAAW,GAAG,CAAC,EAAEP,SAAO,GAAG,EAAE,OAAO,GAAG,GAAG,CAAC,EAAE,IAAI,GAAG,EAAE,IAAI,GAAG,EAAE,CAAC;EACxE,SAAS,IAAI,QAAQ,GAAGd,SAAO,EAAE,IAAI,GAAG,EAAE;EAC1C,SAAS,IAAI,QAAQ,GAAG,KAAQ,EAAE,IAAI,GAAG,GAAG;EAC5C,IAAI,KAAK,CAAC,CAAC,CAAC,GAAGc,SAAO,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO;EAC1C,GAAG;EACH,EAAE,MAAM,EAAE,WAAW;EACrB,IAAIA,SAAO,GAAG,EAAE,OAAO,GAAG,GAAG,CAAC,EAAE,IAAI,GAAG,EAAE,IAAI,GAAG,EAAE,CAAC;EACnD;EACA,CAAC;;EAED,SAASQ,aAAW,CAAC,MAAM,EAAE,GAAG,EAAE;EAClC,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,CAACR,SAAO,GAAG,MAAM,EAAE,OAAO,GAAG,MAAM,CAAC,CAAC;EAC3D,EAAE,IAAI,GAAG,GAAG,IAAI,EAAE,IAAI,GAAG,GAAG;EAC5B,EAAE,IAAI,GAAG,GAAG,IAAI,EAAE,IAAI,GAAG,GAAG;EAC5B;;EAEA,SAAS,SAAS,CAAC,MAAM,EAAE,GAAG,EAAE;EAChC,EAAE,IAAI,CAAC,GAAGI,WAAS,CAAC,CAAC,MAAM,GAAGZ,SAAO,EAAE,GAAG,GAAGA,SAAO,CAAC,CAAC;EACtD,EAAE,IAAI,EAAE,EAAE;EACV,IAAI,IAAI,MAAM,GAAGc,gBAAc,CAAC,EAAE,EAAE,CAAC,CAAC;EACtC,QAAQ,UAAU,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EAC/C,QAAQ,UAAU,GAAGA,gBAAc,CAAC,UAAU,EAAE,MAAM,CAAC;EACvD,IAAI,yBAAyB,CAAC,UAAU,CAAC;EACzC,IAAI,UAAU,GAAGH,WAAS,CAAC,UAAU,CAAC;EACtC,IAAI,IAAI,KAAK,GAAG,MAAM,GAAG,OAAO;EAChC,QAAQ,IAAI,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE;EACjC,QAAQ,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC,GAAGZ,SAAO,GAAG,IAAI;EAChD,QAAQ,IAAI;EACZ,QAAQ,YAAY,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG;EACvC,IAAI,IAAI,YAAY,IAAI,IAAI,GAAG,OAAO,GAAG,OAAO,IAAI,OAAO,GAAG,IAAI,GAAG,MAAM,CAAC,EAAE;EAC9E,MAAM,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,GAAGA,SAAO;EACpC,MAAM,IAAI,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,IAAI;EAClC,KAAK,MAAM,IAAI,OAAO,GAAG,CAAC,OAAO,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,EAAE,YAAY,IAAI,IAAI,GAAG,OAAO,GAAG,OAAO,IAAI,OAAO,GAAG,IAAI,GAAG,MAAM,CAAC,EAAE;EAC5H,MAAM,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,GAAGA,SAAO;EACrC,MAAM,IAAI,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,IAAI;EAClC,KAAK,MAAM;EACX,MAAM,IAAI,GAAG,GAAG,IAAI,EAAE,IAAI,GAAG,GAAG;EAChC,MAAM,IAAI,GAAG,GAAG,IAAI,EAAE,IAAI,GAAG,GAAG;EAChC;EACA,IAAI,IAAI,YAAY,EAAE;EACtB,MAAM,IAAI,MAAM,GAAG,OAAO,EAAE;EAC5B,QAAQ,IAAI,KAAK,CAACS,SAAO,EAAE,MAAM,CAAC,GAAG,KAAK,CAACA,SAAO,EAAE,OAAO,CAAC,EAAE,OAAO,GAAG,MAAM;EAC9E,OAAO,MAAM;EACb,QAAQ,IAAI,KAAK,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,KAAK,CAACA,SAAO,EAAE,OAAO,CAAC,EAAEA,SAAO,GAAG,MAAM;EAC9E;EACA,KAAK,MAAM;EACX,MAAM,IAAI,OAAO,IAAIA,SAAO,EAAE;EAC9B,QAAQ,IAAI,MAAM,GAAGA,SAAO,EAAEA,SAAO,GAAG,MAAM;EAC9C,QAAQ,IAAI,MAAM,GAAG,OAAO,EAAE,OAAO,GAAG,MAAM;EAC9C,OAAO,MAAM;EACb,QAAQ,IAAI,MAAM,GAAG,OAAO,EAAE;EAC9B,UAAU,IAAI,KAAK,CAACA,SAAO,EAAE,MAAM,CAAC,GAAG,KAAK,CAACA,SAAO,EAAE,OAAO,CAAC,EAAE,OAAO,GAAG,MAAM;EAChF,SAAS,MAAM;EACf,UAAU,IAAI,KAAK,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,KAAK,CAACA,SAAO,EAAE,OAAO,CAAC,EAAEA,SAAO,GAAG,MAAM;EAChF;EACA;EACA;EACA,GAAG,MAAM;EACT,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,CAACA,SAAO,GAAG,MAAM,EAAE,OAAO,GAAG,MAAM,CAAC,CAAC;EAC7D;EACA,EAAE,IAAI,GAAG,GAAG,IAAI,EAAE,IAAI,GAAG,GAAG;EAC5B,EAAE,IAAI,GAAG,GAAG,IAAI,EAAE,IAAI,GAAG,GAAG;EAC5B,EAAE,EAAE,GAAG,CAAC,EAAE,OAAO,GAAG,MAAM;EAC1B;;EAEA,SAAS,eAAe,GAAG;EAC3B,EAAEO,cAAY,CAAC,KAAK,GAAG,SAAS;EAChC;;EAEA,SAAS,aAAa,GAAG;EACzB,EAAE,KAAK,CAAC,CAAC,CAAC,GAAGP,SAAO,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO;EACxC,EAAEO,cAAY,CAAC,KAAK,GAAGC,aAAW;EAClC,EAAE,EAAE,GAAG,IAAI;EACX;;EAEA,SAAS,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE;EACtC,EAAE,IAAI,EAAE,EAAE;EACV,IAAI,IAAI,KAAK,GAAG,MAAM,GAAG,OAAO;EAChC,IAAI,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,KAAK,IAAI,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC;EAC7E,GAAG,MAAM;EACT,IAAIV,UAAQ,GAAG,MAAM,EAAEC,OAAK,GAAG,GAAG;EAClC;EACA,EAAE,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC;EAC/B,EAAE,SAAS,CAAC,MAAM,EAAE,GAAG,CAAC;EACxB;;EAEA,SAAS,eAAe,GAAG;EAC3B,EAAE,UAAU,CAAC,SAAS,EAAE;EACxB;;EAEA,SAAS,aAAa,GAAG;EACzB,EAAE,eAAe,CAACD,UAAQ,EAAEC,OAAK,CAAC;EAClC,EAAE,UAAU,CAAC,OAAO,EAAE;EACtB,EAAE,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAGb,SAAO,EAAEc,SAAO,GAAG,EAAE,OAAO,GAAG,GAAG,CAAC;EACzD,EAAE,KAAK,CAAC,CAAC,CAAC,GAAGA,SAAO,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO;EACxC,EAAE,EAAE,GAAG,IAAI;EACX;;EAEA;EACA;EACA;EACA,SAAS,KAAK,CAAC,OAAO,EAAE,OAAO,EAAE;EACjC,EAAE,OAAO,CAAC,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,GAAG,GAAG,OAAO;EAC3D;;EAEA,SAAS,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE;EAC5B,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACpB;;EAEA,SAAS,aAAa,CAAC,KAAK,EAAE,CAAC,EAAE;EACjC,EAAE,OAAO,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;EAC7F;;EAEe,kBAAQ,CAAC,OAAO,EAAE;EACjC,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK;;EAEzC,EAAE,IAAI,GAAG,OAAO,GAAG,EAAEA,SAAO,GAAG,IAAI,GAAG,QAAQ,CAAC;EAC/C,EAAE,MAAM,GAAG,EAAE;EACb,EAAES,SAAM,CAAC,OAAO,EAAEF,cAAY,CAAC;;EAE/B;EACA,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE;EACzB,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;;EAE7B;EACA,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;EACzD,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;EACnB,MAAM,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC5D,QAAQ,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC9D,QAAQ,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC9D,OAAO,MAAM;EACb,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;EAC1B;EACA;;EAEA;EACA;EACA,IAAI,KAAK,QAAQ,GAAG,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;EAChG,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;EACnB,MAAM,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE,QAAQ,GAAG,KAAK,EAAEP,SAAO,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;EAClG;EACA;;EAEA,EAAE,MAAM,GAAG,KAAK,GAAG,IAAI;;EAEvB,EAAE,OAAOA,SAAO,KAAK,QAAQ,IAAI,IAAI,KAAK;EAC1C,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EAC/B,QAAQ,CAAC,CAACA,SAAO,EAAE,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;EAC1C;;EC7KA,IAAI,EAAE,EAAE,EAAE;EACV,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE;EACd,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE;EACd,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE;EACd,IAAI,QAAQ,EAAE,KAAK;EACnB,IAAIU,IAAE,EAAEC,IAAE,EAAE,EAAE,CAAC;;EAEf,IAAI,cAAc,GAAG;EACrB,EAAE,MAAM,EAAE,IAAI;EACd,EAAE,KAAK,EAAE,aAAa;EACtB,EAAE,SAAS,EAAE,iBAAiB;EAC9B,EAAE,OAAO,EAAE,eAAe;EAC1B,EAAE,YAAY,EAAE,WAAW;EAC3B,IAAI,cAAc,CAAC,SAAS,GAAG,iBAAiB;EAChD,IAAI,cAAc,CAAC,OAAO,GAAG,eAAe;EAC5C,GAAG;EACH,EAAE,UAAU,EAAE,WAAW;EACzB,IAAI,cAAc,CAAC,SAAS,GAAG,iBAAiB;EAChD,IAAI,cAAc,CAAC,OAAO,GAAG,eAAe;EAC5C;EACA,CAAC;;EAED;EACA,SAAS,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE;EACpC,EAAE,MAAM,IAAInB,SAAO,EAAE,GAAG,IAAIA,SAAO;EACnC,EAAE,IAAI,MAAM,GAAGE,KAAG,CAAC,GAAG,CAAC;EACvB,EAAE,sBAAsB,CAAC,MAAM,GAAGA,KAAG,CAAC,MAAM,CAAC,EAAE,MAAM,GAAGC,KAAG,CAAC,MAAM,CAAC,EAAEA,KAAG,CAAC,GAAG,CAAC,CAAC;EAC9E;;EAEA,SAAS,sBAAsB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;EACzC,EAAE,EAAE,EAAE;EACN,EAAE,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE;EACrB,EAAE,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE;EACrB,EAAE,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE;EACrB;;EAEA,SAAS,iBAAiB,GAAG;EAC7B,EAAE,cAAc,CAAC,KAAK,GAAG,sBAAsB;EAC/C;;EAEA,SAAS,sBAAsB,CAAC,MAAM,EAAE,GAAG,EAAE;EAC7C,EAAE,MAAM,IAAIH,SAAO,EAAE,GAAG,IAAIA,SAAO;EACnC,EAAE,IAAI,MAAM,GAAGE,KAAG,CAAC,GAAG,CAAC;EACvB,EAAEgB,IAAE,GAAG,MAAM,GAAGhB,KAAG,CAAC,MAAM,CAAC;EAC3B,EAAEiB,IAAE,GAAG,MAAM,GAAGhB,KAAG,CAAC,MAAM,CAAC;EAC3B,EAAE,EAAE,GAAGA,KAAG,CAAC,GAAG,CAAC;EACf,EAAE,cAAc,CAAC,KAAK,GAAG,iBAAiB;EAC1C,EAAE,sBAAsB,CAACe,IAAE,EAAEC,IAAE,EAAE,EAAE,CAAC;EACpC;;EAEA,SAAS,iBAAiB,CAAC,MAAM,EAAE,GAAG,EAAE;EACxC,EAAE,MAAM,IAAInB,SAAO,EAAE,GAAG,IAAIA,SAAO;EACnC,EAAE,IAAI,MAAM,GAAGE,KAAG,CAAC,GAAG,CAAC;EACvB,MAAM,CAAC,GAAG,MAAM,GAAGA,KAAG,CAAC,MAAM,CAAC;EAC9B,MAAM,CAAC,GAAG,MAAM,GAAGC,KAAG,CAAC,MAAM,CAAC;EAC9B,MAAM,CAAC,GAAGA,KAAG,CAAC,GAAG,CAAC;EAClB,MAAM,CAAC,GAAGF,OAAK,CAACG,MAAI,CAAC,CAAC,CAAC,GAAGe,IAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,GAAGD,IAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAGA,IAAE,GAAG,CAAC,GAAGC,IAAE,GAAG,CAAC,IAAI,CAAC,CAAC,EAAED,IAAE,GAAG,CAAC,GAAGC,IAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;EAClI,EAAE,EAAE,IAAI,CAAC;EACT,EAAE,EAAE,IAAI,CAAC,IAAID,IAAE,IAAIA,IAAE,GAAG,CAAC,CAAC,CAAC;EAC3B,EAAE,EAAE,IAAI,CAAC,IAAIC,IAAE,IAAIA,IAAE,GAAG,CAAC,CAAC,CAAC;EAC3B,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;EAC3B,EAAE,sBAAsB,CAACD,IAAE,EAAEC,IAAE,EAAE,EAAE,CAAC;EACpC;;EAEA,SAAS,eAAe,GAAG;EAC3B,EAAE,cAAc,CAAC,KAAK,GAAG,aAAa;EACtC;;EAEA;EACA;EACA,SAAS,iBAAiB,GAAG;EAC7B,EAAE,cAAc,CAAC,KAAK,GAAG,sBAAsB;EAC/C;;EAEA,SAAS,eAAe,GAAG;EAC3B,EAAE,iBAAiB,CAAC,QAAQ,EAAE,KAAK,CAAC;EACpC,EAAE,cAAc,CAAC,KAAK,GAAG,aAAa;EACtC;;EAEA,SAAS,sBAAsB,CAAC,MAAM,EAAE,GAAG,EAAE;EAC7C,EAAE,QAAQ,GAAG,MAAM,EAAE,KAAK,GAAG,GAAG;EAChC,EAAE,MAAM,IAAInB,SAAO,EAAE,GAAG,IAAIA,SAAO;EACnC,EAAE,cAAc,CAAC,KAAK,GAAG,iBAAiB;EAC1C,EAAE,IAAI,MAAM,GAAGE,KAAG,CAAC,GAAG,CAAC;EACvB,EAAEgB,IAAE,GAAG,MAAM,GAAGhB,KAAG,CAAC,MAAM,CAAC;EAC3B,EAAEiB,IAAE,GAAG,MAAM,GAAGhB,KAAG,CAAC,MAAM,CAAC;EAC3B,EAAE,EAAE,GAAGA,KAAG,CAAC,GAAG,CAAC;EACf,EAAE,sBAAsB,CAACe,IAAE,EAAEC,IAAE,EAAE,EAAE,CAAC;EACpC;;EAEA,SAAS,iBAAiB,CAAC,MAAM,EAAE,GAAG,EAAE;EACxC,EAAE,MAAM,IAAInB,SAAO,EAAE,GAAG,IAAIA,SAAO;EACnC,EAAE,IAAI,MAAM,GAAGE,KAAG,CAAC,GAAG,CAAC;EACvB,MAAM,CAAC,GAAG,MAAM,GAAGA,KAAG,CAAC,MAAM,CAAC;EAC9B,MAAM,CAAC,GAAG,MAAM,GAAGC,KAAG,CAAC,MAAM,CAAC;EAC9B,MAAM,CAAC,GAAGA,KAAG,CAAC,GAAG,CAAC;EAClB,MAAM,EAAE,GAAGgB,IAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;EAC1B,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,GAAGD,IAAE,GAAG,CAAC;EAC1B,MAAM,EAAE,GAAGA,IAAE,GAAG,CAAC,GAAGC,IAAE,GAAG,CAAC;EAC1B,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC3B,MAAM,CAAC,GAAGd,MAAI,CAAC,CAAC,CAAC;EACjB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;EACtB,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;EAChB,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;EAChB,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;EAChB,EAAE,EAAE,IAAI,CAAC;EACT,EAAE,EAAE,IAAI,CAAC,IAAIa,IAAE,IAAIA,IAAE,GAAG,CAAC,CAAC,CAAC;EAC3B,EAAE,EAAE,IAAI,CAAC,IAAIC,IAAE,IAAIA,IAAE,GAAG,CAAC,CAAC,CAAC;EAC3B,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;EAC3B,EAAE,sBAAsB,CAACD,IAAE,EAAEC,IAAE,EAAE,EAAE,CAAC;EACpC;;EAEe,oBAAQ,CAAC,MAAM,EAAE;EAChC,EAAE,EAAE,GAAG,EAAE;EACT,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;EACd,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAClB,EAAE,EAAE,GAAG,IAAI,KAAK,EAAE;EAClB,EAAE,EAAE,GAAG,IAAI,KAAK,EAAE;EAClB,EAAE,EAAE,GAAG,IAAI,KAAK,EAAE;EAClB,EAAEF,SAAM,CAAC,MAAM,EAAE,cAAc,CAAC;;EAEhC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE;EACb,MAAM,CAAC,GAAG,CAAC,EAAE;EACb,MAAM,CAAC,GAAG,CAAC,EAAE;EACb,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;EAExB;EACA,EAAE,IAAI,CAAC,GAAG,QAAQ,EAAE;EACpB,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE;EAC1B;EACA,IAAI,IAAI,EAAE,GAAGvB,SAAO,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE;EAC5C,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACtB;EACA,IAAI,IAAI,CAAC,GAAG,QAAQ,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EACvC;;EAEA,EAAE,OAAO,CAACO,OAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGF,SAAO,EAAEM,MAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGN,SAAO,CAAC;EACvD;;EC9Ie,gBAAQ,CAAC,CAAC,EAAE,CAAC,EAAE;;EAE9B,EAAE,SAAS,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE;EACzB,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACrC;;EAEA,EAAE,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;EAC5D,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACxD,GAAG;;EAEH,EAAE,OAAO,OAAO;EAChB;;ECRA,SAAS,gBAAgB,CAAC,MAAM,EAAE,GAAG,EAAE;EACvC,EAAE,IAAI,GAAG,CAAC,MAAM,CAAC,GAAGH,IAAE,EAAE,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAGE,KAAG,CAAC,GAAGA,KAAG;EAChE,EAAE,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;EACtB;;EAEA,gBAAgB,CAAC,MAAM,GAAG,gBAAgB;;EAEnC,SAAS,aAAa,CAAC,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE;EACjE,EAAE,OAAO,CAAC,WAAW,IAAIA,KAAG,KAAK,QAAQ,IAAI,UAAU,GAAG,OAAO,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE,gBAAgB,CAAC,QAAQ,EAAE,UAAU,CAAC;EACrI,MAAM,cAAc,CAAC,WAAW,CAAC;EACjC,OAAO,QAAQ,IAAI,UAAU,GAAG,gBAAgB,CAAC,QAAQ,EAAE,UAAU;EACrE,MAAM,gBAAgB,CAAC;EACvB;;EAEA,SAAS,qBAAqB,CAAC,WAAW,EAAE;EAC5C,EAAE,OAAO,SAAS,MAAM,EAAE,GAAG,EAAE;EAC/B,IAAI,MAAM,IAAI,WAAW;EACzB,IAAI,IAAI,GAAG,CAAC,MAAM,CAAC,GAAGF,IAAE,EAAE,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAGE,KAAG,CAAC,GAAGA,KAAG;EAClE,IAAI,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;EACxB,GAAG;EACH;;EAEA,SAAS,cAAc,CAAC,WAAW,EAAE;EACrC,EAAE,IAAI,QAAQ,GAAG,qBAAqB,CAAC,WAAW,CAAC;EACnD,EAAE,QAAQ,CAAC,MAAM,GAAG,qBAAqB,CAAC,CAAC,WAAW,CAAC;EACvD,EAAE,OAAO,QAAQ;EACjB;;EAEA,SAAS,gBAAgB,CAAC,QAAQ,EAAE,UAAU,EAAE;EAChD,EAAE,IAAI,WAAW,GAAGI,KAAG,CAAC,QAAQ,CAAC;EACjC,MAAM,WAAW,GAAGC,KAAG,CAAC,QAAQ,CAAC;EACjC,MAAM,aAAa,GAAGD,KAAG,CAAC,UAAU,CAAC;EACrC,MAAM,aAAa,GAAGC,KAAG,CAAC,UAAU,CAAC;;EAErC,EAAE,SAAS,QAAQ,CAAC,MAAM,EAAE,GAAG,EAAE;EACjC,IAAI,IAAI,MAAM,GAAGD,KAAG,CAAC,GAAG,CAAC;EACzB,QAAQ,CAAC,GAAGA,KAAG,CAAC,MAAM,CAAC,GAAG,MAAM;EAChC,QAAQ,CAAC,GAAGC,KAAG,CAAC,MAAM,CAAC,GAAG,MAAM;EAChC,QAAQ,CAAC,GAAGA,KAAG,CAAC,GAAG,CAAC;EACpB,QAAQ,CAAC,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,WAAW;EAC7C,IAAI,OAAO;EACX,MAAMF,OAAK,CAAC,CAAC,GAAG,aAAa,GAAG,CAAC,GAAG,aAAa,EAAE,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,WAAW,CAAC;EACrF,MAAMI,MAAI,CAAC,CAAC,GAAG,aAAa,GAAG,CAAC,GAAG,aAAa;EAChD,KAAK;EACL;;EAEA,EAAE,QAAQ,CAAC,MAAM,GAAG,SAAS,MAAM,EAAE,GAAG,EAAE;EAC1C,IAAI,IAAI,MAAM,GAAGH,KAAG,CAAC,GAAG,CAAC;EACzB,QAAQ,CAAC,GAAGA,KAAG,CAAC,MAAM,CAAC,GAAG,MAAM;EAChC,QAAQ,CAAC,GAAGC,KAAG,CAAC,MAAM,CAAC,GAAG,MAAM;EAChC,QAAQ,CAAC,GAAGA,KAAG,CAAC,GAAG,CAAC;EACpB,QAAQ,CAAC,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC,GAAG,aAAa;EACjD,IAAI,OAAO;EACX,MAAMF,OAAK,CAAC,CAAC,GAAG,aAAa,GAAG,CAAC,GAAG,aAAa,EAAE,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,WAAW,CAAC;EACrF,MAAMI,MAAI,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,WAAW;EAC5C,KAAK;EACL,GAAG;;EAEH,EAAE,OAAO,QAAQ;EACjB;;EAEe,oBAAQ,CAAC,MAAM,EAAE;EAChC,EAAE,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,GAAGL,SAAO,EAAE,MAAM,CAAC,CAAC,CAAC,GAAGA,SAAO,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAGA,SAAO,GAAG,CAAC,CAAC;;EAE/G,EAAE,SAAS,OAAO,CAAC,WAAW,EAAE;EAChC,IAAI,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,GAAGA,SAAO,EAAE,WAAW,CAAC,CAAC,CAAC,GAAGA,SAAO,CAAC;EAC5E,IAAI,OAAO,WAAW,CAAC,CAAC,CAAC,IAAID,SAAO,EAAE,WAAW,CAAC,CAAC,CAAC,IAAIA,SAAO,EAAE,WAAW;EAC5E;;EAEA,EAAE,OAAO,CAAC,MAAM,GAAG,SAAS,WAAW,EAAE;EACzC,IAAI,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,GAAGC,SAAO,EAAE,WAAW,CAAC,CAAC,CAAC,GAAGA,SAAO,CAAC;EACnF,IAAI,OAAO,WAAW,CAAC,CAAC,CAAC,IAAID,SAAO,EAAE,WAAW,CAAC,CAAC,CAAC,IAAIA,SAAO,EAAE,WAAW;EAC5E,GAAG;;EAEH,EAAE,OAAO,OAAO;EAChB;;ECzEA;EACO,SAAS,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE;EACvE,EAAE,IAAI,CAAC,KAAK,EAAE;EACd,EAAE,IAAI,SAAS,GAAGG,KAAG,CAAC,MAAM,CAAC;EAC7B,MAAM,SAAS,GAAGC,KAAG,CAAC,MAAM,CAAC;EAC7B,MAAM,IAAI,GAAG,SAAS,GAAG,KAAK;EAC9B,EAAE,IAAI,EAAE,IAAI,IAAI,EAAE;EAClB,IAAI,EAAE,GAAG,MAAM,GAAG,SAAS,GAAGL,KAAG;EACjC,IAAI,EAAE,GAAG,MAAM,GAAG,IAAI,GAAG,CAAC;EAC1B,GAAG,MAAM;EACT,IAAI,EAAE,GAAG,YAAY,CAAC,SAAS,EAAE,EAAE,CAAC;EACpC,IAAI,EAAE,GAAG,YAAY,CAAC,SAAS,EAAE,EAAE,CAAC;EACpC,IAAI,IAAI,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,SAAS,GAAGA,KAAG;EAChE;EACA,EAAE,KAAK,IAAI,KAAK,EAAE,CAAC,GAAG,EAAE,EAAE,SAAS,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,IAAI,EAAE;EACtE,IAAI,KAAK,GAAGa,WAAS,CAAC,CAAC,SAAS,EAAE,CAAC,SAAS,GAAGT,KAAG,CAAC,CAAC,CAAC,EAAE,CAAC,SAAS,GAAGC,KAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5E,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;EACpC;EACA;;EAEA;EACA,SAAS,YAAY,CAAC,SAAS,EAAE,KAAK,EAAE;EACxC,EAAE,KAAK,GAAGS,WAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,SAAS;EACjD,EAAE,yBAAyB,CAAC,KAAK,CAAC;EAClC,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EAC9B,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,MAAM,IAAId,KAAG,GAAGJ,SAAO,IAAII,KAAG;EACnE;;EC7Be,mBAAQ,GAAG;EAC1B,EAAE,IAAI,KAAK,GAAG,EAAE;EAChB,MAAM,IAAI;EACV,EAAE,OAAO;EACT,IAAI,KAAK,EAAE,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;EAC7B,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAC1B,KAAK;EACL,IAAI,SAAS,EAAE,WAAW;EAC1B,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;EAC3B,KAAK;EACL,IAAI,OAAO,EAAE,IAAI;EACjB,IAAI,MAAM,EAAE,WAAW;EACvB,MAAM,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;EACzE,KAAK;EACL,IAAI,MAAM,EAAE,WAAW;EACvB,MAAM,IAAI,MAAM,GAAG,KAAK;EACxB,MAAM,KAAK,GAAG,EAAE;EAChB,MAAM,IAAI,GAAG,IAAI;EACjB,MAAM,OAAO,MAAM;EACnB;EACA,GAAG;EACH;;ECrBe,mBAAQ,CAAC,CAAC,EAAE,CAAC,EAAE;EAC9B,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGJ,SAAO,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,SAAO;EACjE;;ECDA,SAAS,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE;EACnD,EAAE,IAAI,CAAC,CAAC,GAAG,KAAK;EAChB,EAAE,IAAI,CAAC,CAAC,GAAG,MAAM;EACjB,EAAE,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;EACjB,EAAE,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;EACjB,EAAE,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;EACjB,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;EACzB;;EAEA;EACA;EACA;EACe,mBAAQ,CAAC,QAAQ,EAAE,mBAAmB,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,EAAE;EACzF,EAAE,IAAI,OAAO,GAAG,EAAE;EAClB,MAAM,IAAI,GAAG,EAAE;EACf,MAAM,CAAC;EACP,MAAM,CAAC;;EAEP,EAAE,QAAQ,CAAC,OAAO,CAAC,SAAS,OAAO,EAAE;EACrC,IAAI,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;EACvC,IAAI,IAAI,CAAC,EAAE,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;;EAE9C,IAAI,IAAI,UAAU,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE;EAC5B,MAAM,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;EAC5B,QAAQ,MAAM,CAAC,SAAS,EAAE;EAC1B,QAAQ,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EACzE,QAAQ,MAAM,CAAC,OAAO,EAAE;EACxB,QAAQ;EACR;EACA;EACA,MAAM,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGA,SAAO;EAC1B;;EAEA,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,YAAY,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EAC/D,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,YAAY,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;EACzD,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,YAAY,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;EAChE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,YAAY,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;EACxD,GAAG,CAAC;;EAEJ,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;;EAEvB,EAAE,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC;EAChC,EAAE,IAAI,CAAC,OAAO,CAAC;EACf,EAAE,IAAI,CAAC,IAAI,CAAC;;EAEZ,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;EAC3C,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,WAAW;EAC1C;;EAEA,EAAE,IAAI,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC;EACxB,MAAM,MAAM;EACZ,MAAM,KAAK;;EAEX,EAAE,OAAO,CAAC,EAAE;EACZ;EACA,IAAI,IAAI,OAAO,GAAG,KAAK;EACvB,QAAQ,SAAS,GAAG,IAAI;EACxB,IAAI,OAAO,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,CAAC,MAAM,KAAK,EAAE;EAC3D,IAAI,MAAM,GAAG,OAAO,CAAC,CAAC;EACtB,IAAI,MAAM,CAAC,SAAS,EAAE;EACtB,IAAI,GAAG;EACP,MAAM,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;EACpC,MAAM,IAAI,OAAO,CAAC,CAAC,EAAE;EACrB,QAAQ,IAAI,SAAS,EAAE;EACvB,UAAU,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;EACnG,SAAS,MAAM;EACf,UAAU,WAAW,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC;EACxD;EACA,QAAQ,OAAO,GAAG,OAAO,CAAC,CAAC;EAC3B,OAAO,MAAM;EACb,QAAQ,IAAI,SAAS,EAAE;EACvB,UAAU,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;EAC9B,UAAU,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;EACjG,SAAS,MAAM;EACf,UAAU,WAAW,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,MAAM,CAAC;EACzD;EACA,QAAQ,OAAO,GAAG,OAAO,CAAC,CAAC;EAC3B;EACA,MAAM,OAAO,GAAG,OAAO,CAAC,CAAC;EACzB,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC;EACxB,MAAM,SAAS,GAAG,CAAC,SAAS;EAC5B,KAAK,QAAQ,CAAC,OAAO,CAAC,CAAC;EACvB,IAAI,MAAM,CAAC,OAAO,EAAE;EACpB;EACA;;EAEA,SAAS,IAAI,CAAC,KAAK,EAAE;EACrB,EAAE,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,EAAE;EAC3B,EAAE,IAAI,CAAC;EACP,MAAM,CAAC,GAAG,CAAC;EACX,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;EAClB,MAAM,CAAC;EACP,EAAE,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE;EAClB,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;EACtB,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;EACX,IAAI,CAAC,GAAG,CAAC;EACT;EACA,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;EACpB,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;EACT;;EClGA,SAAS,SAAS,CAAC,KAAK,EAAE;EAC1B,EAAE,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIE,IAAE,GAAG,KAAK,CAAC,CAAC,CAAC,GAAGH,MAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGG,IAAE,IAAIE,KAAG,GAAGF,IAAE,CAAC;EAC5F;;EAEe,wBAAQ,CAAC,OAAO,EAAE,KAAK,EAAE;EACxC,EAAE,IAAI,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC;EAC/B,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC;EACpB,MAAM,MAAM,GAAGO,KAAG,CAAC,GAAG,CAAC;EACvB,MAAM,MAAM,GAAG,CAACA,KAAG,CAAC,MAAM,CAAC,EAAE,CAACD,KAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;EAC7C,MAAM,KAAK,GAAG,CAAC;EACf,MAAM,OAAO,GAAG,CAAC;;EAEjB,EAAE,IAAI,GAAG,GAAG,IAAI,KAAK,EAAE;;EAEvB,EAAE,IAAI,MAAM,KAAK,CAAC,EAAE,GAAG,GAAGL,QAAM,GAAGH,SAAO;EAC1C,OAAO,IAAI,MAAM,KAAK,EAAE,EAAE,GAAG,GAAG,CAACG,QAAM,GAAGH,SAAO;;EAEjD,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;EAClD,IAAI,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE;EAC3C,IAAI,IAAI,IAAI;EACZ,QAAQ,CAAC;EACT,QAAQ,MAAM,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;EAC5B,QAAQ,OAAO,GAAG,SAAS,CAAC,MAAM,CAAC;EACnC,QAAQ,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,SAAS;EACxC,QAAQ,OAAO,GAAGS,KAAG,CAAC,IAAI,CAAC;EAC3B,QAAQ,OAAO,GAAGD,KAAG,CAAC,IAAI,CAAC;;EAE3B,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,OAAO,GAAG,OAAO,EAAE,OAAO,GAAG,OAAO,EAAE,OAAO,GAAG,OAAO,EAAE,MAAM,GAAG,MAAM,EAAE;EAC1G,MAAM,IAAI,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;EAC1B,UAAU,OAAO,GAAG,SAAS,CAAC,MAAM,CAAC;EACrC,UAAU,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,SAAS;EAC1C,UAAU,OAAO,GAAGC,KAAG,CAAC,IAAI,CAAC;EAC7B,UAAU,OAAO,GAAGD,KAAG,CAAC,IAAI,CAAC;EAC7B,UAAU,KAAK,GAAG,OAAO,GAAG,OAAO;EACnC,UAAU,IAAI,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;EACpC,UAAU,QAAQ,GAAG,IAAI,GAAG,KAAK;EACjC,UAAU,YAAY,GAAG,QAAQ,GAAGN,IAAE;EACtC,UAAU,CAAC,GAAG,OAAO,GAAG,OAAO;;EAE/B,MAAM,GAAG,CAAC,GAAG,CAACK,OAAK,CAAC,CAAC,GAAG,IAAI,GAAGE,KAAG,CAAC,QAAQ,CAAC,EAAE,OAAO,GAAG,OAAO,GAAG,CAAC,GAAGD,KAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;EACrF,MAAM,KAAK,IAAI,YAAY,GAAG,KAAK,GAAG,IAAI,GAAGJ,KAAG,GAAG,KAAK;;EAExD;EACA;EACA,MAAM,IAAI,YAAY,GAAG,OAAO,IAAI,MAAM,GAAG,OAAO,IAAI,MAAM,EAAE;EAChE,QAAQ,IAAI,GAAG,GAAGgB,gBAAc,CAACF,WAAS,CAAC,MAAM,CAAC,EAAEA,WAAS,CAAC,MAAM,CAAC,CAAC;EACtE,QAAQ,yBAAyB,CAAC,GAAG,CAAC;EACtC,QAAQ,IAAI,YAAY,GAAGE,gBAAc,CAAC,MAAM,EAAE,GAAG,CAAC;EACtD,QAAQ,yBAAyB,CAAC,YAAY,CAAC;EAC/C,QAAQ,IAAI,MAAM,GAAG,CAAC,YAAY,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,IAAIT,MAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;EACjF,QAAQ,IAAI,GAAG,GAAG,MAAM,IAAI,GAAG,KAAK,MAAM,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;EAClE,UAAU,OAAO,IAAI,YAAY,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;EACvD;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,EAAE,OAAO,CAAC,KAAK,GAAG,KAAQ,IAAI,KAAK,GAAGX,SAAO,IAAI,GAAG,GAAG,MAAS,KAAK,OAAO,GAAG,CAAC,CAAC;EACjF;;ECnEe,aAAQ,CAAC,YAAY,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,EAAE;EACpE,EAAE,OAAO,SAAS,IAAI,EAAE;EACxB,IAAI,IAAI,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;EAC7B,QAAQ,UAAU,GAAG,UAAU,EAAE;EACjC,QAAQ,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC;EACvC,QAAQ,cAAc,GAAG,KAAK;EAC9B,QAAQ,OAAO;EACf,QAAQ,QAAQ;EAChB,QAAQ,IAAI;;EAEZ,IAAI,IAAI,IAAI,GAAG;EACf,MAAM,KAAK,EAAE,KAAK;EAClB,MAAM,SAAS,EAAE,SAAS;EAC1B,MAAM,OAAO,EAAE,OAAO;EACtB,MAAM,YAAY,EAAE,WAAW;EAC/B,QAAQ,IAAI,CAAC,KAAK,GAAG,SAAS;EAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,SAAS;EAClC,QAAQ,IAAI,CAAC,OAAO,GAAG,OAAO;EAC9B,QAAQ,QAAQ,GAAG,EAAE;EACrB,QAAQ,OAAO,GAAG,EAAE;EACpB,OAAO;EACP,MAAM,UAAU,EAAE,WAAW;EAC7B,QAAQ,IAAI,CAAC,KAAK,GAAG,KAAK;EAC1B,QAAQ,IAAI,CAAC,SAAS,GAAG,SAAS;EAClC,QAAQ,IAAI,CAAC,OAAO,GAAG,OAAO;EAC9B,QAAQ,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;EAClC,QAAQ,IAAI,WAAW,GAAG,eAAe,CAAC,OAAO,EAAE,KAAK,CAAC;EACzD,QAAQ,IAAI,QAAQ,CAAC,MAAM,EAAE;EAC7B,UAAU,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,cAAc,GAAG,IAAI;EACzE,UAAU,UAAU,CAAC,QAAQ,EAAE,mBAAmB,EAAE,WAAW,EAAE,WAAW,EAAE,IAAI,CAAC;EACnF,SAAS,MAAM,IAAI,WAAW,EAAE;EAChC,UAAU,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,cAAc,GAAG,IAAI;EACzE,UAAU,IAAI,CAAC,SAAS,EAAE;EAC1B,UAAU,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;EAC1C,UAAU,IAAI,CAAC,OAAO,EAAE;EACxB;EACA,QAAQ,IAAI,cAAc,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,cAAc,GAAG,KAAK;EACrE,QAAQ,QAAQ,GAAG,OAAO,GAAG,IAAI;EACjC,OAAO;EACP,MAAM,MAAM,EAAE,WAAW;EACzB,QAAQ,IAAI,CAAC,YAAY,EAAE;EAC3B,QAAQ,IAAI,CAAC,SAAS,EAAE;EACxB,QAAQ,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;EACxC,QAAQ,IAAI,CAAC,OAAO,EAAE;EACtB,QAAQ,IAAI,CAAC,UAAU,EAAE;EACzB;EACA,KAAK;;EAEL,IAAI,SAAS,KAAK,CAAC,MAAM,EAAE,GAAG,EAAE;EAChC,MAAM,IAAI,YAAY,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC;EAC5D;;EAEA,IAAI,SAAS,SAAS,CAAC,MAAM,EAAE,GAAG,EAAE;EACpC,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC;EAC7B;;EAEA,IAAI,SAAS,SAAS,GAAG;EACzB,MAAM,IAAI,CAAC,KAAK,GAAG,SAAS;EAC5B,MAAM,IAAI,CAAC,SAAS,EAAE;EACtB;;EAEA,IAAI,SAAS,OAAO,GAAG;EACvB,MAAM,IAAI,CAAC,KAAK,GAAG,KAAK;EACxB,MAAM,IAAI,CAAC,OAAO,EAAE;EACpB;;EAEA,IAAI,SAAS,SAAS,CAAC,MAAM,EAAE,GAAG,EAAE;EACpC,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;EAC9B,MAAM,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC;EACjC;;EAEA,IAAI,SAAS,SAAS,GAAG;EACzB,MAAM,QAAQ,CAAC,SAAS,EAAE;EAC1B,MAAM,IAAI,GAAG,EAAE;EACf;;EAEA,IAAI,SAAS,OAAO,GAAG;EACvB,MAAM,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvC,MAAM,QAAQ,CAAC,OAAO,EAAE;;EAExB,MAAM,IAAI,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE;EAClC,UAAU,YAAY,GAAG,UAAU,CAAC,MAAM,EAAE;EAC5C,UAAU,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC;EACvC,UAAU,OAAO;EACjB,UAAU,KAAK;;EAEf,MAAM,IAAI,CAAC,GAAG,EAAE;EAChB,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;EACxB,MAAM,IAAI,GAAG,IAAI;;EAEjB,MAAM,IAAI,CAAC,CAAC,EAAE;;EAEd;EACA,MAAM,IAAI,KAAK,GAAG,CAAC,EAAE;EACrB,QAAQ,OAAO,GAAG,YAAY,CAAC,CAAC,CAAC;EACjC,QAAQ,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE;EAC1C,UAAU,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,cAAc,GAAG,IAAI;EACzE,UAAU,IAAI,CAAC,SAAS,EAAE;EAC1B,UAAU,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;EAC/E,UAAU,IAAI,CAAC,OAAO,EAAE;EACxB;EACA,QAAQ;EACR;;EAEA;EACA;EACA,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC;;EAEhG,MAAM,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;EACtD;;EAEA,IAAI,OAAO,IAAI;EACf,GAAG;EACH;;EAEA,SAAS,YAAY,CAAC,OAAO,EAAE;EAC/B,EAAE,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC;EAC3B;;EAEA;EACA;EACA,SAAS,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE;EACnC,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGG,QAAM,GAAGH,SAAO,GAAGG,QAAM,GAAG,CAAC,CAAC,CAAC,CAAC;EACpE,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGA,QAAM,GAAGH,SAAO,GAAGG,QAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACrE;;AC/HA,yBAAe,IAAI;EACnB,EAAE,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE;EAC7B,EAAE,oBAAoB;EACtB,EAAE,2BAA2B;EAC7B,EAAE,CAAC,CAACD,IAAE,EAAE,CAACC,QAAM;EACf,CAAC;;EAED;EACA;EACA;EACA,SAAS,oBAAoB,CAAC,MAAM,EAAE;EACtC,EAAE,IAAI,OAAO,GAAG,GAAG;EACnB,MAAM,IAAI,GAAG,GAAG;EAChB,MAAM,KAAK,GAAG,GAAG;EACjB,MAAM,KAAK,CAAC;;EAEZ,EAAE,OAAO;EACT,IAAI,SAAS,EAAE,WAAW;EAC1B,MAAM,MAAM,CAAC,SAAS,EAAE;EACxB,MAAM,KAAK,GAAG,CAAC;EACf,KAAK;EACL,IAAI,KAAK,EAAE,SAAS,OAAO,EAAE,IAAI,EAAE;EACnC,MAAM,IAAI,KAAK,GAAG,OAAO,GAAG,CAAC,GAAGD,IAAE,GAAG,CAACA,IAAE;EACxC,UAAU,KAAK,GAAG,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC;EACxC,MAAM,IAAI,GAAG,CAAC,KAAK,GAAGA,IAAE,CAAC,GAAGF,SAAO,EAAE;EACrC,QAAQ,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,GAAGG,QAAM,GAAG,CAACA,QAAM,CAAC;EAC9E,QAAQ,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC;EACjC,QAAQ,MAAM,CAAC,OAAO,EAAE;EACxB,QAAQ,MAAM,CAAC,SAAS,EAAE;EAC1B,QAAQ,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC;EACjC,QAAQ,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;EACnC,QAAQ,KAAK,GAAG,CAAC;EACjB,OAAO,MAAM,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAID,IAAE,EAAE;EACjD,QAAQ,IAAI,GAAG,CAAC,OAAO,GAAG,KAAK,CAAC,GAAGF,SAAO,EAAE,OAAO,IAAI,KAAK,GAAGA,SAAO,CAAC;EACvE,QAAQ,IAAI,GAAG,CAAC,OAAO,GAAG,KAAK,CAAC,GAAGA,SAAO,EAAE,OAAO,IAAI,KAAK,GAAGA,SAAO;EACtE,QAAQ,IAAI,GAAG,yBAAyB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC;EACtE,QAAQ,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC;EACjC,QAAQ,MAAM,CAAC,OAAO,EAAE;EACxB,QAAQ,MAAM,CAAC,SAAS,EAAE;EAC1B,QAAQ,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC;EACjC,QAAQ,KAAK,GAAG,CAAC;EACjB;EACA,MAAM,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,EAAE,IAAI,GAAG,IAAI,CAAC;EAClD,MAAM,KAAK,GAAG,KAAK;EACnB,KAAK;EACL,IAAI,OAAO,EAAE,WAAW;EACxB,MAAM,MAAM,CAAC,OAAO,EAAE;EACtB,MAAM,OAAO,GAAG,IAAI,GAAG,GAAG;EAC1B,KAAK;EACL,IAAI,KAAK,EAAE,WAAW;EACtB,MAAM,OAAO,CAAC,GAAG,KAAK,CAAC;EACvB;EACA,GAAG;EACH;;EAEA,SAAS,yBAAyB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE;EACjE,EAAE,IAAI,OAAO;EACb,MAAM,OAAO;EACb,MAAM,iBAAiB,GAAGS,KAAG,CAAC,OAAO,GAAG,OAAO,CAAC;EAChD,EAAE,OAAO,GAAG,CAAC,iBAAiB,CAAC,GAAGT;EAClC,QAAQ,IAAI,CAAC,CAACS,KAAG,CAAC,IAAI,CAAC,IAAI,OAAO,GAAGD,KAAG,CAAC,IAAI,CAAC,CAAC,GAAGC,KAAG,CAAC,OAAO;EAC7D,YAAYA,KAAG,CAAC,IAAI,CAAC,IAAI,OAAO,GAAGD,KAAG,CAAC,IAAI,CAAC,CAAC,GAAGC,KAAG,CAAC,OAAO,CAAC;EAC5D,aAAa,OAAO,GAAG,OAAO,GAAG,iBAAiB,CAAC;EACnD,QAAQ,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC;EACzB;;EAEA,SAAS,2BAA2B,CAAC,IAAI,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;EAClE,EAAE,IAAI,GAAG;EACT,EAAE,IAAI,IAAI,IAAI,IAAI,EAAE;EACpB,IAAI,GAAG,GAAG,SAAS,GAAGN,QAAM;EAC5B,IAAI,MAAM,CAAC,KAAK,CAAC,CAACD,IAAE,EAAE,GAAG,CAAC;EAC1B,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC;EACxB,IAAI,MAAM,CAAC,KAAK,CAACA,IAAE,EAAE,GAAG,CAAC;EACzB,IAAI,MAAM,CAAC,KAAK,CAACA,IAAE,EAAE,CAAC,CAAC;EACvB,IAAI,MAAM,CAAC,KAAK,CAACA,IAAE,EAAE,CAAC,GAAG,CAAC;EAC1B,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC;EACzB,IAAI,MAAM,CAAC,KAAK,CAAC,CAACA,IAAE,EAAE,CAAC,GAAG,CAAC;EAC3B,IAAI,MAAM,CAAC,KAAK,CAAC,CAACA,IAAE,EAAE,CAAC,CAAC;EACxB,IAAI,MAAM,CAAC,KAAK,CAAC,CAACA,IAAE,EAAE,GAAG,CAAC;EAC1B,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAGF,SAAO,EAAE;EAC7C,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAGE,IAAE,GAAG,CAACA,IAAE;EAC3C,IAAI,GAAG,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC;EAChC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC;EAC9B,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC;EACxB,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC;EAC7B,GAAG,MAAM;EACT,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EAC9B;EACA;;ECrFe,mBAAQ,CAAC,MAAM,EAAE;EAChC,EAAE,IAAI,EAAE,GAAGM,KAAG,CAAC,MAAM,CAAC;EACtB,MAAM,KAAK,GAAG,CAAC,GAAGF,SAAO;EACzB,MAAM,WAAW,GAAG,EAAE,GAAG,CAAC;EAC1B,MAAM,aAAa,GAAG,GAAG,CAAC,EAAE,CAAC,GAAGN,SAAO,CAAC;;EAExC,EAAE,SAAS,WAAW,CAAC,IAAI,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;EACpD,IAAI,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC;EAC5D;;EAEA,EAAE,SAAS,OAAO,CAAC,MAAM,EAAE,GAAG,EAAE;EAChC,IAAI,OAAOQ,KAAG,CAAC,MAAM,CAAC,GAAGA,KAAG,CAAC,GAAG,CAAC,GAAG,EAAE;EACtC;;EAEA;EACA;EACA;EACA;EACA,EAAE,SAAS,QAAQ,CAAC,MAAM,EAAE;EAC5B,IAAI,IAAI,MAAM;EACd,QAAQ,EAAE;EACV,QAAQ,EAAE;EACV,QAAQ,GAAG;EACX,QAAQ,KAAK,CAAC;EACd,IAAI,OAAO;EACX,MAAM,SAAS,EAAE,WAAW;EAC5B,QAAQ,GAAG,GAAG,EAAE,GAAG,KAAK;EACxB,QAAQ,KAAK,GAAG,CAAC;EACjB,OAAO;EACP,MAAM,KAAK,EAAE,SAAS,MAAM,EAAE,GAAG,EAAE;EACnC,QAAQ,IAAI,MAAM,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC;EAClC,YAAY,MAAM;EAClB,YAAY,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;EACpC,YAAY,CAAC,GAAG;EAChB,gBAAgB,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG;EACxC,gBAAgB,CAAC,GAAG,IAAI,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,GAAGN,IAAE,GAAG,CAACA,IAAE,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC;EACnE,QAAQ,IAAI,CAAC,MAAM,KAAK,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,SAAS,EAAE;EACzD,QAAQ,IAAI,CAAC,KAAK,EAAE,EAAE;EACtB,UAAU,MAAM,GAAG,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC;EAC5C,UAAU,IAAI,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC;EACjF,YAAY,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;EACzB;EACA,QAAQ,IAAI,CAAC,KAAK,EAAE,EAAE;EACtB,UAAU,KAAK,GAAG,CAAC;EACnB,UAAU,IAAI,CAAC,EAAE;EACjB;EACA,YAAY,MAAM,CAAC,SAAS,EAAE;EAC9B,YAAY,MAAM,GAAG,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC;EAC9C,YAAY,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;EAC9C,WAAW,MAAM;EACjB;EACA,YAAY,MAAM,GAAG,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC;EAC9C,YAAY,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACjD,YAAY,MAAM,CAAC,OAAO,EAAE;EAC5B;EACA,UAAU,MAAM,GAAG,MAAM;EACzB,SAAS,MAAM,IAAI,aAAa,IAAI,MAAM,IAAI,WAAW,GAAG,CAAC,EAAE;EAC/D,UAAU,IAAI,CAAC;EACf;EACA;EACA,UAAU,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,EAAE;EAClE,YAAY,KAAK,GAAG,CAAC;EACrB,YAAY,IAAI,WAAW,EAAE;EAC7B,cAAc,MAAM,CAAC,SAAS,EAAE;EAChC,cAAc,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5C,cAAc,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5C,cAAc,MAAM,CAAC,OAAO,EAAE;EAC9B,aAAa,MAAM;EACnB,cAAc,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5C,cAAc,MAAM,CAAC,OAAO,EAAE;EAC9B,cAAc,MAAM,CAAC,SAAS,EAAE;EAChC,cAAc,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EAC/C;EACA;EACA;EACA,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,EAAE;EAC3D,UAAU,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;EAC5C;EACA,QAAQ,MAAM,GAAG,MAAM,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC;EACvC,OAAO;EACP,MAAM,OAAO,EAAE,WAAW;EAC1B,QAAQ,IAAI,EAAE,EAAE,MAAM,CAAC,OAAO,EAAE;EAChC,QAAQ,MAAM,GAAG,IAAI;EACrB,OAAO;EACP;EACA;EACA,MAAM,KAAK,EAAE,WAAW;EACxB,QAAQ,OAAO,KAAK,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,CAAC,CAAC;EACzC;EACA,KAAK;EACL;;EAEA;EACA,EAAE,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE;EAChC,IAAI,IAAI,EAAE,GAAGgB,WAAS,CAAC,CAAC,CAAC;EACzB,QAAQ,EAAE,GAAGA,WAAS,CAAC,CAAC,CAAC;;EAEzB;EACA;EACA,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACtB,QAAQ,EAAE,GAAGE,gBAAc,CAAC,EAAE,EAAE,EAAE,CAAC;EACnC,QAAQ,IAAI,GAAGD,cAAY,CAAC,EAAE,EAAE,EAAE,CAAC;EACnC,QAAQ,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;EACpB,QAAQ,WAAW,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI;;EAExC;EACA,IAAI,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC;;EAEtC,IAAI,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,GAAG,WAAW;EACrC,QAAQ,EAAE,GAAG,CAAC,EAAE,GAAG,IAAI,GAAG,WAAW;EACrC,QAAQ,KAAK,GAAGC,gBAAc,CAAC,EAAE,EAAE,EAAE,CAAC;EACtC,QAAQ,CAAC,GAAG,cAAc,CAAC,EAAE,EAAE,EAAE,CAAC;EAClC,QAAQ,CAAC,GAAG,cAAc,CAAC,EAAE,EAAE,EAAE,CAAC;EAClC,IAAI,mBAAmB,CAAC,CAAC,EAAE,CAAC,CAAC;;EAE7B;EACA,IAAI,IAAI,CAAC,GAAG,KAAK;EACjB,QAAQ,CAAC,GAAGD,cAAY,CAAC,CAAC,EAAE,CAAC,CAAC;EAC9B,QAAQ,EAAE,GAAGA,cAAY,CAAC,CAAC,EAAE,CAAC,CAAC;EAC/B,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,IAAIA,cAAY,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;;EAElD,IAAI,IAAI,EAAE,GAAG,CAAC,EAAE;;EAEhB,IAAI,IAAI,CAAC,GAAGT,MAAI,CAAC,EAAE,CAAC;EACpB,QAAQ,CAAC,GAAG,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;EAC5C,IAAI,mBAAmB,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7B,IAAI,CAAC,GAAGO,WAAS,CAAC,CAAC,CAAC;;EAEpB,IAAI,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC;;EAEtB;EACA,IAAI,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;EACtB,QAAQ,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;EACtB,QAAQ,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;EACnB,QAAQ,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;EACnB,QAAQ,CAAC;;EAET,IAAI,IAAI,OAAO,GAAG,OAAO,EAAE,CAAC,GAAG,OAAO,EAAE,OAAO,GAAG,OAAO,EAAE,OAAO,GAAG,CAAC;;EAEtE,IAAI,IAAI,KAAK,GAAG,OAAO,GAAG,OAAO;EACjC,QAAQ,KAAK,GAAG,GAAG,CAAC,KAAK,GAAGf,IAAE,CAAC,GAAGF,SAAO;EACzC,QAAQ,QAAQ,GAAG,KAAK,IAAI,KAAK,GAAGA,SAAO;;EAE3C,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,CAAC;;EAE9D;EACA,IAAI,IAAI;EACR,UAAU;EACV,YAAY,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,GAAGA,SAAO,GAAG,IAAI,GAAG,IAAI;EACjF,YAAY,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI;EACpC,UAAU,KAAK,GAAGE,IAAE,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,EAAE;EAC7D,MAAM,IAAI,EAAE,GAAG,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;EAC/C,MAAM,mBAAmB,CAAC,EAAE,EAAE,CAAC,CAAC;EAChC,MAAM,OAAO,CAAC,CAAC,EAAEe,WAAS,CAAC,EAAE,CAAC,CAAC;EAC/B;EACA;;EAEA;EACA;EACA,EAAE,SAAS,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE;EAC7B,IAAI,IAAI,CAAC,GAAG,WAAW,GAAG,MAAM,GAAGf,IAAE,GAAG,MAAM;EAC9C,QAAQ,IAAI,GAAG,CAAC;EAChB,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,IAAI,CAAC,CAAC;EAC/B,SAAS,IAAI,MAAM,GAAG,CAAC,EAAE,IAAI,IAAI,CAAC,CAAC;EACnC,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE,IAAI,IAAI,CAAC,CAAC;EAC5B,SAAS,IAAI,GAAG,GAAG,CAAC,EAAE,IAAI,IAAI,CAAC,CAAC;EAChC,IAAI,OAAO,IAAI;EACf;;EAEA,EAAE,OAAO,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAACA,IAAE,EAAE,MAAM,GAAGA,IAAE,CAAC,CAAC;EAC9F;;EChLe,iBAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;EAC9C,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;EACf,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;EACf,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;EACf,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;EACf,MAAM,EAAE,GAAG,CAAC;EACZ,MAAM,EAAE,GAAG,CAAC;EACZ,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;EAClB,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;EAClB,MAAM,CAAC;;EAEP,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE;EACb,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE;EACpB,EAAE,CAAC,IAAI,EAAE;EACT,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE;EACd,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;EAChB,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;EACtB,GAAG,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE;EACrB,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;EAChB,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;EACtB;;EAEA,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE;EACb,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE;EACpB,EAAE,CAAC,IAAI,EAAE;EACT,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE;EACd,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;EAChB,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;EACtB,GAAG,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE;EACrB,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;EAChB,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;EACtB;;EAEA,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE;EACb,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE;EACpB,EAAE,CAAC,IAAI,EAAE;EACT,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE;EACd,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;EAChB,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;EACtB,GAAG,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE;EACrB,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;EAChB,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;EACtB;;EAEA,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE;EACb,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE;EACpB,EAAE,CAAC,IAAI,EAAE;EACT,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE;EACd,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;EAChB,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;EACtB,GAAG,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE;EACrB,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;EAChB,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;EACtB;;EAEA,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;EACtD,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;EACtD,EAAE,OAAO,IAAI;EACb;;ECpDA,IAAI,OAAO,GAAG,GAAG,EAAE,OAAO,GAAG,IAAQ;;EAErC;EACA;;EAEe,SAAS,aAAa,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;;EAEtD,EAAE,SAAS,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE;EACzB,IAAI,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;EACnD;;EAEA,EAAE,SAAS,WAAW,CAAC,IAAI,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;EACpD,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC;EACrB,IAAI,IAAI,IAAI,IAAI;EAChB,WAAW,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,SAAS,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,EAAE,SAAS,CAAC;EACxE,WAAW,YAAY,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,EAAE;EACvD,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;EACpE,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE;EACjD,KAAK,MAAM;EACX,MAAM,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EAChC;EACA;;EAEA,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,EAAE;EAChC,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,GAAGF,SAAO,GAAG,SAAS,GAAG,CAAC,GAAG,CAAC,GAAG;EAC1D,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,GAAGA,SAAO,GAAG,SAAS,GAAG,CAAC,GAAG,CAAC,GAAG;EACzD,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,GAAGA,SAAO,GAAG,SAAS,GAAG,CAAC,GAAG,CAAC,GAAG;EACzD,UAAU,SAAS,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAChC;;EAEA,EAAE,SAAS,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE;EACrC,IAAI,OAAO,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjC;;EAEA,EAAE,SAAS,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE;EAC9B,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;EACzB,QAAQ,EAAE,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;EACzB,IAAI,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG;EAC5B,UAAU,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAC/B,UAAU,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAC/B,UAAU,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAC/B,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,EAAE,OAAO,SAAS,MAAM,EAAE;EAC1B,IAAI,IAAI,YAAY,GAAG,MAAM;EAC7B,QAAQ,YAAY,GAAG,UAAU,EAAE;EACnC,QAAQ,QAAQ;EAChB,QAAQ,OAAO;EACf,QAAQ,IAAI;EACZ,QAAQ,GAAG,EAAE,GAAG,EAAE,GAAG;EACrB,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE;EAClB,QAAQ,KAAK;EACb,QAAQ,KAAK;;EAEb,IAAI,IAAI,UAAU,GAAG;EACrB,MAAM,KAAK,EAAE,KAAK;EAClB,MAAM,SAAS,EAAE,SAAS;EAC1B,MAAM,OAAO,EAAE,OAAO;EACtB,MAAM,YAAY,EAAE,YAAY;EAChC,MAAM,UAAU,EAAE;EAClB,KAAK;;EAEL,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;EACzB,MAAM,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACjD;;EAEA,IAAI,SAAS,aAAa,GAAG;EAC7B,MAAM,IAAI,OAAO,GAAG,CAAC;;EAErB,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;EACtD,QAAQ,KAAK,IAAI,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;EAC/H,UAAU,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;EACzE,UAAU,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,OAAO,CAAC;EAClG,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,OAAO,CAAC;EAC1F;EACA;;EAEA,MAAM,OAAO,OAAO;EACpB;;EAEA;EACA,IAAI,SAAS,YAAY,GAAG;EAC5B,MAAM,YAAY,GAAG,YAAY,EAAE,QAAQ,GAAG,EAAE,EAAE,OAAO,GAAG,EAAE,EAAE,KAAK,GAAG,IAAI;EAC5E;;EAEA,IAAI,SAAS,UAAU,GAAG;EAC1B,MAAM,IAAI,WAAW,GAAG,aAAa,EAAE;EACvC,UAAU,WAAW,GAAG,KAAK,IAAI,WAAW;EAC5C,UAAU,OAAO,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,EAAE,MAAM;EACvD,MAAM,IAAI,WAAW,IAAI,OAAO,EAAE;EAClC,QAAQ,MAAM,CAAC,YAAY,EAAE;EAC7B,QAAQ,IAAI,WAAW,EAAE;EACzB,UAAU,MAAM,CAAC,SAAS,EAAE;EAC5B,UAAU,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,CAAC;EAC5C,UAAU,MAAM,CAAC,OAAO,EAAE;EAC1B;EACA,QAAQ,IAAI,OAAO,EAAE;EACrB,UAAU,UAAU,CAAC,QAAQ,EAAE,mBAAmB,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,CAAC;EACrF;EACA,QAAQ,MAAM,CAAC,UAAU,EAAE;EAC3B;EACA,MAAM,YAAY,GAAG,MAAM,EAAE,QAAQ,GAAG,OAAO,GAAG,IAAI,GAAG,IAAI;EAC7D;;EAEA,IAAI,SAAS,SAAS,GAAG;EACzB,MAAM,UAAU,CAAC,KAAK,GAAG,SAAS;EAClC,MAAM,IAAI,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;EAC1C,MAAM,KAAK,GAAG,IAAI;EAClB,MAAM,EAAE,GAAG,KAAK;EAChB,MAAM,EAAE,GAAG,EAAE,GAAG,GAAG;EACnB;;EAEA;EACA;EACA;EACA,IAAI,SAAS,OAAO,GAAG;EACvB,MAAM,IAAI,QAAQ,EAAE;EACpB,QAAQ,SAAS,CAAC,GAAG,EAAE,GAAG,CAAC;EAC3B,QAAQ,IAAI,GAAG,IAAI,EAAE,EAAE,YAAY,CAAC,MAAM,EAAE;EAC5C,QAAQ,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;EAC5C;EACA,MAAM,UAAU,CAAC,KAAK,GAAG,KAAK;EAC9B,MAAM,IAAI,EAAE,EAAE,YAAY,CAAC,OAAO,EAAE;EACpC;;EAEA,IAAI,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;EAC7B,MAAM,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EAC3B,MAAM,IAAI,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACpC,MAAM,IAAI,KAAK,EAAE;EACjB,QAAQ,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC;EACjC,QAAQ,KAAK,GAAG,KAAK;EACrB,QAAQ,IAAI,CAAC,EAAE;EACf,UAAU,YAAY,CAAC,SAAS,EAAE;EAClC,UAAU,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAClC;EACA,OAAO,MAAM;EACb,QAAQ,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7C,aAAa;EACb,UAAU,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;EAChH,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;EAC5G,UAAU,IAAI,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE;EAC9C,YAAY,IAAI,CAAC,EAAE,EAAE;EACrB,cAAc,YAAY,CAAC,SAAS,EAAE;EACtC,cAAc,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5C;EACA,YAAY,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1C,YAAY,IAAI,CAAC,CAAC,EAAE,YAAY,CAAC,OAAO,EAAE;EAC1C,YAAY,KAAK,GAAG,KAAK;EACzB,WAAW,MAAM,IAAI,CAAC,EAAE;EACxB,YAAY,YAAY,CAAC,SAAS,EAAE;EACpC,YAAY,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACpC,YAAY,KAAK,GAAG,KAAK;EACzB;EACA;EACA;EACA,MAAM,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC;EAC5B;;EAEA,IAAI,OAAO,UAAU;EACrB,GAAG;EACH;;EClKA,IAAI,SAAS;EACb,IAAI,OAAO;EACX,IAAI,OAAO;EACX,IAAI,OAAO;;EAEX,IAAI,YAAY,GAAG;EACnB,EAAE,MAAM,EAAE,IAAI;EACd,EAAE,KAAK,EAAE,IAAI;EACb,EAAE,SAAS,EAAE,eAAe;EAC5B,EAAE,OAAO,EAAE,IAAI;EACf,EAAE,YAAY,EAAE,IAAI;EACpB,EAAE,UAAU,EAAE;EACd,CAAC;;EAED,SAAS,eAAe,GAAG;EAC3B,EAAE,YAAY,CAAC,KAAK,GAAG,gBAAgB;EACvC,EAAE,YAAY,CAAC,OAAO,GAAG,aAAa;EACtC;;EAEA,SAAS,aAAa,GAAG;EACzB,EAAE,YAAY,CAAC,KAAK,GAAG,YAAY,CAAC,OAAO,GAAG,IAAI;EAClD;;EAEA,SAAS,gBAAgB,CAAC,MAAM,EAAE,GAAG,EAAE;EACvC,EAAE,MAAM,IAAIM,SAAO,EAAE,GAAG,IAAIA,SAAO;EACnC,EAAE,OAAO,GAAG,MAAM,EAAE,OAAO,GAAGG,KAAG,CAAC,GAAG,CAAC,EAAE,OAAO,GAAGD,KAAG,CAAC,GAAG,CAAC;EAC1D,EAAE,YAAY,CAAC,KAAK,GAAG,WAAW;EAClC;;EAEA,SAAS,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE;EAClC,EAAE,MAAM,IAAIF,SAAO,EAAE,GAAG,IAAIA,SAAO;EACnC,EAAE,IAAI,MAAM,GAAGG,KAAG,CAAC,GAAG,CAAC;EACvB,MAAM,MAAM,GAAGD,KAAG,CAAC,GAAG,CAAC;EACvB,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC;EACnC,MAAM,QAAQ,GAAGA,KAAG,CAAC,KAAK,CAAC;EAC3B,MAAM,QAAQ,GAAGC,KAAG,CAAC,KAAK,CAAC;EAC3B,MAAM,CAAC,GAAG,MAAM,GAAG,QAAQ;EAC3B,MAAM,CAAC,GAAG,OAAO,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM,GAAG,QAAQ;EACxD,MAAM,CAAC,GAAG,OAAO,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM,GAAG,QAAQ;EACxD,EAAE,SAAS,CAAC,GAAG,CAACF,OAAK,CAACG,MAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9C,EAAE,OAAO,GAAG,MAAM,EAAE,OAAO,GAAG,MAAM,EAAE,OAAO,GAAG,MAAM;EACtD;;EAEe,eAAQ,CAAC,MAAM,EAAE;EAChC,EAAE,SAAS,GAAG,IAAI,KAAK,EAAE;EACzB,EAAEa,SAAM,CAAC,MAAM,EAAE,YAAY,CAAC;EAC9B,EAAE,OAAO,CAAC,SAAS;EACnB;;EClDA,IAAI,WAAW,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;EAC9B,IAAIG,QAAM,GAAG,CAAC,IAAI,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,CAAC;;EAE5C,oBAAQ,CAAC,CAAC,EAAE,CAAC,EAAE;EAC9B,EAAE,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC;EACpB,EAAE,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC;EACpB,EAAE,OAAO,MAAM,CAACA,QAAM,CAAC;EACvB;;ECLA,IAAI,kBAAkB,GAAG;EACzB,EAAE,OAAO,EAAE,SAAS,MAAM,EAAE,KAAK,EAAE;EACnC,IAAI,OAAO,gBAAgB,CAAC,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC;EACnD,GAAG;EACH,EAAE,iBAAiB,EAAE,SAAS,MAAM,EAAE,KAAK,EAAE;EAC7C,IAAI,IAAI,QAAQ,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM;EAC/D,IAAI,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,OAAO,IAAI;EAClF,IAAI,OAAO,KAAK;EAChB;EACA,CAAC;;EAED,IAAI,oBAAoB,GAAG;EAC3B,EAAE,MAAM,EAAE,WAAW;EACrB,IAAI,OAAO,IAAI;EACf,GAAG;EACH,EAAE,KAAK,EAAE,SAAS,MAAM,EAAE,KAAK,EAAE;EACjC,IAAI,OAAO,aAAa,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,CAAC;EACnD,GAAG;EACH,EAAE,UAAU,EAAE,SAAS,MAAM,EAAE,KAAK,EAAE;EACtC,IAAI,IAAI,WAAW,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM;EACxE,IAAI,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,OAAO,IAAI;EACzE,IAAI,OAAO,KAAK;EAChB,GAAG;EACH,EAAE,UAAU,EAAE,SAAS,MAAM,EAAE,KAAK,EAAE;EACtC,IAAI,OAAO,YAAY,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,CAAC;EAClD,GAAG;EACH,EAAE,eAAe,EAAE,SAAS,MAAM,EAAE,KAAK,EAAE;EAC3C,IAAI,IAAI,WAAW,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM;EACxE,IAAI,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,OAAO,IAAI;EACxE,IAAI,OAAO,KAAK;EAChB,GAAG;EACH,EAAE,OAAO,EAAE,SAAS,MAAM,EAAE,KAAK,EAAE;EACnC,IAAI,OAAO,eAAe,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,CAAC;EACrD,GAAG;EACH,EAAE,YAAY,EAAE,SAAS,MAAM,EAAE,KAAK,EAAE;EACxC,IAAI,IAAI,WAAW,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM;EACxE,IAAI,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,OAAO,IAAI;EAC3E,IAAI,OAAO,KAAK;EAChB,GAAG;EACH,EAAE,kBAAkB,EAAE,SAAS,MAAM,EAAE,KAAK,EAAE;EAC9C,IAAI,IAAI,UAAU,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM;EACrE,IAAI,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,OAAO,IAAI;EAC3E,IAAI,OAAO,KAAK;EAChB;EACA,CAAC;;EAED,SAAS,gBAAgB,CAAC,QAAQ,EAAE,KAAK,EAAE;EAC3C,EAAE,OAAO,QAAQ,IAAI,oBAAoB,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI;EACtE,QAAQ,oBAAoB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,KAAK;EAC3D,QAAQ,KAAK;EACb;;EAEA,SAAS,aAAa,CAAC,WAAW,EAAE,KAAK,EAAE;EAC3C,EAAE,OAAOC,WAAQ,CAAC,WAAW,EAAE,KAAK,CAAC,KAAK,CAAC;EAC3C;;EAEA,SAAS,YAAY,CAAC,WAAW,EAAE,KAAK,EAAE;EAC1C,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE;EAChB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;EACtD,IAAI,EAAE,GAAGA,WAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;EACxC,IAAI,IAAI,EAAE,KAAK,CAAC,EAAE,OAAO,IAAI;EAC7B,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE;EACf,MAAM,EAAE,GAAGA,WAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EACvD,MAAM;EACN,QAAQ,EAAE,GAAG,CAAC;EACd,QAAQ,EAAE,IAAI,EAAE;EAChB,QAAQ,EAAE,IAAI,EAAE;EAChB,QAAQ,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,QAAQ,GAAG;EACxE;EACA,QAAQ,OAAO,IAAI;EACnB;EACA,IAAI,EAAE,GAAG,EAAE;EACX;EACA,EAAE,OAAO,KAAK;EACd;;EAEA,SAAS,eAAe,CAAC,WAAW,EAAE,KAAK,EAAE;EAC7C,EAAE,OAAO,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC;EAC7E;;EAEA,SAAS,WAAW,CAAC,IAAI,EAAE;EAC3B,EAAE,OAAO,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,IAAI;EACxD;;EAEA,SAAS,YAAY,CAAC,KAAK,EAAE;EAC7B,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,GAAGrB,SAAO,EAAE,KAAK,CAAC,CAAC,CAAC,GAAGA,SAAO,CAAC;EACjD;;EAEe,oBAAQ,CAAC,MAAM,EAAE,KAAK,EAAE;EACvC,EAAE,OAAO,CAAC,MAAM,IAAI,kBAAkB,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI;EACjE,QAAQ,kBAAkB,CAAC,MAAM,CAAC,IAAI;EACtC,QAAQ,gBAAgB,EAAE,MAAM,EAAE,KAAK,CAAC;EACxC;;EC9Fe,uBAAQ,CAAC,CAAC,EAAE,CAAC,EAAE;EAC9B,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGA,SAAO;EACzB,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGA,SAAO;EACzB,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGA,SAAO;EACzB,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGA,SAAO;EACzB,MAAM,GAAG,GAAGE,KAAG,CAAC,EAAE,CAAC;EACnB,MAAM,GAAG,GAAGC,KAAG,CAAC,EAAE,CAAC;EACnB,MAAM,GAAG,GAAGD,KAAG,CAAC,EAAE,CAAC;EACnB,MAAM,GAAG,GAAGC,KAAG,CAAC,EAAE,CAAC;EACnB,MAAM,GAAG,GAAG,GAAG,GAAGD,KAAG,CAAC,EAAE,CAAC;EACzB,MAAM,GAAG,GAAG,GAAG,GAAGC,KAAG,CAAC,EAAE,CAAC;EACzB,MAAM,GAAG,GAAG,GAAG,GAAGD,KAAG,CAAC,EAAE,CAAC;EACzB,MAAM,GAAG,GAAG,GAAG,GAAGC,KAAG,CAAC,EAAE,CAAC;EACzB,MAAM,CAAC,GAAG,CAAC,GAAGE,MAAI,CAACD,MAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;EAC3E,MAAM,CAAC,GAAGD,KAAG,CAAC,CAAC,CAAC;;EAEhB,EAAE,IAAI,WAAW,GAAG,CAAC,GAAG,SAAS,CAAC,EAAE;EACpC,IAAI,IAAI,CAAC,GAAGA,KAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;EAC3B,QAAQ,CAAC,GAAGA,KAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EAC1B,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG;EAC7B,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG;EAC7B,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG;EAC7B,IAAI,OAAO;EACX,MAAMF,OAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGF,SAAO;EAC3B,MAAME,OAAK,CAAC,CAAC,EAAEG,MAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAGL;EACtC,KAAK;EACL,GAAG,GAAG,WAAW;EACjB,IAAI,OAAO,CAAC,EAAE,GAAGA,SAAO,EAAE,EAAE,GAAGA,SAAO,CAAC;EACvC,GAAG;;EAEH,EAAE,WAAW,CAAC,QAAQ,GAAG,CAAC;;EAE1B,EAAE,OAAO,WAAW;EACpB;;ACnCA,mBAAe,CAAC,IAAI,CAAC;;ECErB,IAAI,EAAE,GAAG,QAAQ;EACjB,IAAI,EAAE,GAAG,EAAE;EACX,IAAI,EAAE,GAAG,CAAC,EAAE;EACZ,IAAI,EAAE,GAAG,EAAE;;EAEX,IAAI,YAAY,GAAG;EACnB,EAAE,KAAK,EAAE,WAAW;EACpB,EAAE,SAAS,EAAE,IAAI;EACjB,EAAE,OAAO,EAAE,IAAI;EACf,EAAE,YAAY,EAAE,IAAI;EACpB,EAAE,UAAU,EAAE,IAAI;EAClB,EAAE,MAAM,EAAE,WAAW;EACrB,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EACrC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,QAAQ,CAAC;EACnC,IAAI,OAAO,MAAM;EACjB;EACA,CAAC;;EAED,SAAS,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE;EAC3B,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;EACpB,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;EACpB,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;EACpB,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;EACpB;;ECnBO,SAASuB,aAAW,CAAC,OAAO,EAAE;EACrC,EAAE,OAAO,SAAS,MAAM,EAAE;EAC1B,IAAI,IAAI,CAAC,GAAG,IAAI,eAAe;EAC/B,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC;EAClD,IAAI,CAAC,CAAC,MAAM,GAAG,MAAM;EACrB,IAAI,OAAO,CAAC;EACZ,GAAG;EACH;;EAEA,SAAS,eAAe,GAAG;;EAE3B,eAAe,CAAC,SAAS,GAAG;EAC5B,EAAE,WAAW,EAAE,eAAe;EAC9B,EAAE,KAAK,EAAE,SAAS,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;EACpD,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE;EAC9C,EAAE,SAAS,EAAE,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,EAAE;EACpD,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE;EAChD,EAAE,YAAY,EAAE,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,EAAE;EAC1D,EAAE,UAAU,EAAE,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;EACpD,CAAC;;ECtBD,SAAS,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,MAAM,EAAE;EAC5C,EAAE,IAAI,IAAI,GAAG,UAAU,CAAC,UAAU,IAAI,UAAU,CAAC,UAAU,EAAE;EAC7D,EAAE,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACzC,EAAE,IAAI,IAAI,IAAI,IAAI,EAAE,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC;EAC/C,EAAE,SAAS,CAAC,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;EACpD,EAAE,SAAS,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;EAClC,EAAE,IAAI,IAAI,IAAI,IAAI,EAAE,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC;EAC/C,EAAE,OAAO,UAAU;EACnB;;EAEO,SAAS,SAAS,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE;EACtD,EAAE,OAAO,GAAG,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE;EACrC,IAAI,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvC,QAAQ,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvC,QAAQ,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtE,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;EAC7D,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;EAC7D,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC/C,GAAG,EAAE,MAAM,CAAC;EACZ;;EAEO,SAAS,OAAO,CAAC,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE;EAClD,EAAE,OAAO,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,MAAM,CAAC;EACtD;;EAEO,SAAS,QAAQ,CAAC,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE;EACpD,EAAE,OAAO,GAAG,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE;EACrC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK;EAClB,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;EAC7C,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC/C,GAAG,EAAE,MAAM,CAAC;EACZ;;EAEO,SAAS,SAAS,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE;EACtD,EAAE,OAAO,GAAG,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE;EACrC,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM;EACnB,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;EAC7C,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC/C,GAAG,EAAE,MAAM,CAAC;EACZ;;EC1CA,IAAI,QAAQ,GAAG,EAAE;EACjB,IAAI,cAAc,GAAGpB,KAAG,CAAC,EAAE,GAAGF,SAAO,CAAC,CAAC;;EAExB,iBAAQ,CAAC,OAAO,EAAE,MAAM,EAAE;EACzC,EAAE,OAAO,CAAC,MAAM,GAAGuB,UAAQ,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC;EACpE;;EAEA,SAAS,YAAY,CAAC,OAAO,EAAE;EAC/B,EAAE,OAAOD,aAAW,CAAC;EACrB,IAAI,KAAK,EAAE,SAAS,CAAC,EAAE,CAAC,EAAE;EAC1B,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EACvB,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACnC;EACA,GAAG,CAAC;EACJ;;EAEA,SAASC,UAAQ,CAAC,OAAO,EAAE,MAAM,EAAE;;EAEnC,EAAE,SAAS,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE;EACnG,IAAI,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;EACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE;EACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;EAC9B,IAAI,IAAI,EAAE,GAAG,CAAC,GAAG,MAAM,IAAI,KAAK,EAAE,EAAE;EACpC,MAAM,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE;EACrB,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE;EACrB,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE;EACrB,UAAU,CAAC,GAAGnB,MAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACzC,UAAU,IAAI,GAAGC,MAAI,CAAC,CAAC,IAAI,CAAC,CAAC;EAC7B,UAAU,OAAO,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGX,SAAO,IAAI,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC,GAAGA,SAAO,GAAG,CAAC,OAAO,GAAG,OAAO,IAAI,CAAC,GAAGO,OAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACzH,UAAU,CAAC,GAAG,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;EACpC,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;EACnB,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;EACnB,UAAU,GAAG,GAAG,EAAE,GAAG,EAAE;EACvB,UAAU,GAAG,GAAG,EAAE,GAAG,EAAE;EACvB,UAAU,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG;EAClC,MAAM,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,MAAM;EAC/B,aAAa,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG;EACxD,aAAa,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,cAAc,EAAE;EAC3D,QAAQ,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC;EACtG,QAAQ,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;EAC5B,QAAQ,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,CAAC;EAC5F;EACA;EACA;EACA,EAAE,OAAO,SAAS,MAAM,EAAE;EAC1B,IAAI,IAAI,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EACzC,QAAQ,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;EAEpC,IAAI,IAAI,cAAc,GAAG;EACzB,MAAM,KAAK,EAAE,KAAK;EAClB,MAAM,SAAS,EAAE,SAAS;EAC1B,MAAM,OAAO,EAAE,OAAO;EACtB,MAAM,YAAY,EAAE,WAAW,EAAE,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,cAAc,CAAC,SAAS,GAAG,SAAS,CAAC,EAAE;EAC/F,MAAM,UAAU,EAAE,WAAW,EAAE,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,cAAc,CAAC,SAAS,GAAG,SAAS,CAAC;EACzF,KAAK;;EAEL,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;EACzB,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EACvB,MAAM,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B;;EAEA,IAAI,SAAS,SAAS,GAAG;EACzB,MAAM,EAAE,GAAG,GAAG;EACd,MAAM,cAAc,CAAC,KAAK,GAAG,SAAS;EACtC,MAAM,MAAM,CAAC,SAAS,EAAE;EACxB;;EAEA,IAAI,SAAS,SAAS,CAAC,MAAM,EAAE,GAAG,EAAE;EACpC,MAAM,IAAI,CAAC,GAAGW,WAAS,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;EAChE,MAAM,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,GAAG,MAAM,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC;EAC5I,MAAM,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;EAC1B;;EAEA,IAAI,SAAS,OAAO,GAAG;EACvB,MAAM,cAAc,CAAC,KAAK,GAAG,KAAK;EAClC,MAAM,MAAM,CAAC,OAAO,EAAE;EACtB;;EAEA,IAAI,SAAS,SAAS,GAAG;EACzB,MAAM,SAAS,EAAE;EACjB,MAAM,cAAc,CAAC,KAAK,GAAG,SAAS;EACtC,MAAM,cAAc,CAAC,OAAO,GAAG,OAAO;EACtC;;EAEA,IAAI,SAAS,SAAS,CAAC,MAAM,EAAE,GAAG,EAAE;EACpC,MAAM,SAAS,CAAC,QAAQ,GAAG,MAAM,EAAE,GAAG,CAAC,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE;EACzF,MAAM,cAAc,CAAC,KAAK,GAAG,SAAS;EACtC;;EAEA,IAAI,SAAS,OAAO,GAAG;EACvB,MAAM,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,CAAC;EACtG,MAAM,cAAc,CAAC,OAAO,GAAG,OAAO;EACtC,MAAM,OAAO,EAAE;EACf;;EAEA,IAAI,OAAO,cAAc;EACzB,GAAG;EACH;;EC1FA,IAAI,gBAAgB,GAAGU,aAAW,CAAC;EACnC,EAAE,KAAK,EAAE,SAAS,CAAC,EAAE,CAAC,EAAE;EACxB,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAGtB,SAAO,EAAE,CAAC,GAAGA,SAAO,CAAC;EAC/C;EACA,CAAC,CAAC;;EAEF,SAAS,eAAe,CAAC,MAAM,EAAE;EACjC,EAAE,OAAOsB,aAAW,CAAC;EACrB,IAAI,KAAK,EAAE,SAAS,CAAC,EAAE,CAAC,EAAE;EAC1B,MAAM,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;EAC1B,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1C;EACA,GAAG,CAAC;EACJ;;EAEA,SAAS,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;EAC3C,EAAE,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;EAC3B,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE;EACpB,IAAI,OAAO,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;EACnC;EACA,EAAE,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;EACpC,IAAI,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;EACjD,GAAG;EACH,EAAE,OAAO,SAAS;EAClB;;EAEA,SAAS,oBAAoB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE;EACxD,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACtD,EAAE,IAAI,QAAQ,GAAGpB,KAAG,CAAC,KAAK,CAAC;EAC3B,MAAM,QAAQ,GAAGC,KAAG,CAAC,KAAK,CAAC;EAC3B,MAAM,CAAC,GAAG,QAAQ,GAAG,CAAC;EACtB,MAAM,CAAC,GAAG,QAAQ,GAAG,CAAC;EACtB,MAAM,EAAE,GAAG,QAAQ,GAAG,CAAC;EACvB,MAAM,EAAE,GAAG,QAAQ,GAAG,CAAC;EACvB,MAAM,EAAE,GAAG,CAAC,QAAQ,GAAG,EAAE,GAAG,QAAQ,GAAG,EAAE,IAAI,CAAC;EAC9C,MAAM,EAAE,GAAG,CAAC,QAAQ,GAAG,EAAE,GAAG,QAAQ,GAAG,EAAE,IAAI,CAAC;EAC9C,EAAE,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;EAC3B,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE;EACpB,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACnD;EACA,EAAE,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;EACpC,IAAI,OAAO,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;EACrE,GAAG;EACH,EAAE,OAAO,SAAS;EAClB;;EAEe,SAAS,UAAU,CAAC,OAAO,EAAE;EAC5C,EAAE,OAAO,iBAAiB,CAAC,WAAW,EAAE,OAAO,OAAO,CAAC,EAAE,CAAC,EAAE;EAC5D;;EAEO,SAAS,iBAAiB,CAAC,SAAS,EAAE;EAC7C,EAAE,IAAI,OAAO;EACb,MAAM,CAAC,GAAG,GAAG;EACb,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG;EACtB,MAAM,MAAM,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC;EACzB,MAAM,WAAW,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAE,UAAU,GAAG,CAAC,EAAE,MAAM;EAC3D,MAAM,KAAK,GAAG,CAAC;EACf,MAAM,EAAE,GAAG,CAAC;EACZ,MAAM,EAAE,GAAG,CAAC;EACZ,MAAM,KAAK,GAAG,IAAI,EAAE,OAAO,GAAG,gBAAgB;EAC9C,MAAM,EAAE,GAAG,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,QAAQ,GAAGqB,UAAQ;EAChD,MAAM,MAAM,GAAG,GAAG;EAClB,MAAM,eAAe;EACrB,MAAM,gBAAgB;EACtB,MAAM,sBAAsB;EAC5B,MAAM,KAAK;EACX,MAAM,WAAW;;EAEjB,EAAE,SAAS,UAAU,CAAC,KAAK,EAAE;EAC7B,IAAI,OAAO,sBAAsB,CAAC,KAAK,CAAC,CAAC,CAAC,GAAGxB,SAAO,EAAE,KAAK,CAAC,CAAC,CAAC,GAAGA,SAAO,CAAC;EACzE;;EAEA,EAAE,SAAS,MAAM,CAAC,KAAK,EAAE;EACzB,IAAI,KAAK,GAAG,sBAAsB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;EAC7D,IAAI,OAAO,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAGD,SAAO,EAAE,KAAK,CAAC,CAAC,CAAC,GAAGA,SAAO,CAAC;EAC5D;;EAEA,EAAE,UAAU,CAAC,MAAM,GAAG,SAAS,MAAM,EAAE;EACvC,IAAI,OAAO,KAAK,IAAI,WAAW,KAAK,MAAM,GAAG,KAAK,GAAG,KAAK,GAAG,gBAAgB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;EAChK,GAAG;;EAEH,EAAE,UAAU,CAAC,OAAO,GAAG,SAAS,CAAC,EAAE;EACnC,IAAI,OAAO,SAAS,CAAC,MAAM,IAAI,OAAO,GAAG,CAAC,EAAE,KAAK,GAAG,SAAS,EAAE,KAAK,EAAE,IAAI,OAAO;EACjF,GAAG;;EAEH,EAAE,UAAU,CAAC,QAAQ,GAAG,SAAS,CAAC,EAAE;EACpC,IAAI,OAAO,SAAS,CAAC,MAAM,IAAI,QAAQ,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,KAAK,EAAE,IAAI,QAAQ;EAC1F,GAAG;;EAEH,EAAE,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC,EAAE;EACrC,IAAI,OAAO,SAAS,CAAC,MAAM,IAAI,OAAO,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,KAAK,GAAG,CAAC,GAAGC,SAAO,CAAC,IAAI,KAAK,GAAG,IAAI,EAAE,gBAAgB,CAAC,EAAE,KAAK,EAAE,IAAI,KAAK,GAAGD,SAAO;EAC5I,GAAG;;EAEH,EAAE,UAAU,CAAC,UAAU,GAAG,SAAS,CAAC,EAAE;EACtC,IAAI,OAAO,SAAS,CAAC,MAAM,IAAI,QAAQ,GAAG,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAEyB,UAAQ,IAAI,aAAa,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EAC3N,GAAG;;EAEH,EAAE,UAAU,CAAC,KAAK,GAAG,SAAS,CAAC,EAAE;EACjC,IAAI,OAAO,SAAS,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC;EACtD,GAAG;;EAEH,EAAE,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC,EAAE;EACrC,IAAI,OAAO,SAAS,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACzE,GAAG;;EAEH,EAAE,UAAU,CAAC,MAAM,GAAG,SAAS,CAAC,EAAE;EAClC,IAAI,OAAO,SAAS,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGxB,SAAO,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,SAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,MAAM,GAAGD,SAAO,EAAE,GAAG,GAAGA,SAAO,CAAC;EACzI,GAAG;;EAEH,EAAE,UAAU,CAAC,MAAM,GAAG,SAAS,CAAC,EAAE;EAClC,IAAI,OAAO,SAAS,CAAC,MAAM,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGC,SAAO,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,SAAO,EAAE,UAAU,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,SAAO,GAAG,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,WAAW,GAAGD,SAAO,EAAE,QAAQ,GAAGA,SAAO,EAAE,UAAU,GAAGA,SAAO,CAAC;EACzO,GAAG;;EAEH,EAAE,UAAU,CAAC,KAAK,GAAG,SAAS,CAAC,EAAE;EACjC,IAAI,OAAO,SAAS,CAAC,MAAM,IAAI,KAAK,GAAG,CAAC,GAAG,GAAG,GAAGC,SAAO,EAAE,QAAQ,EAAE,IAAI,KAAK,GAAGD,SAAO;EACvF,GAAG;;EAEH,EAAE,UAAU,CAAC,QAAQ,GAAG,SAAS,CAAC,EAAE;EACpC,IAAI,OAAO,SAAS,CAAC,MAAM,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC;EACpE,GAAG;;EAEH,EAAE,UAAU,CAAC,QAAQ,GAAG,SAAS,CAAC,EAAE;EACpC,IAAI,OAAO,SAAS,CAAC,MAAM,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC;EACpE,GAAG;;EAEH,EAAE,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC,EAAE;EACrC,IAAI,OAAO,SAAS,CAAC,MAAM,IAAI,eAAe,GAAG,QAAQ,CAAC,gBAAgB,EAAE,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,IAAIK,MAAI,CAAC,MAAM,CAAC;EACpH,GAAG;;EAEH,EAAE,UAAU,CAAC,SAAS,GAAG,SAAS,MAAM,EAAE,MAAM,EAAE;EAClD,IAAI,OAAO,SAAS,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC;EAChD,GAAG;;EAEH,EAAE,UAAU,CAAC,OAAO,GAAG,SAAS,IAAI,EAAE,MAAM,EAAE;EAC9C,IAAI,OAAO,OAAO,CAAC,UAAU,EAAE,IAAI,EAAE,MAAM,CAAC;EAC5C,GAAG;;EAEH,EAAE,UAAU,CAAC,QAAQ,GAAG,SAAS,KAAK,EAAE,MAAM,EAAE;EAChD,IAAI,OAAO,QAAQ,CAAC,UAAU,EAAE,KAAK,EAAE,MAAM,CAAC;EAC9C,GAAG;;EAEH,EAAE,UAAU,CAAC,SAAS,GAAG,SAAS,MAAM,EAAE,MAAM,EAAE;EAClD,IAAI,OAAO,SAAS,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC;EAChD,GAAG;;EAEH,EAAE,SAAS,QAAQ,GAAG;EACtB,IAAI,IAAI,MAAM,GAAG,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;EAC/F,QAAQ,SAAS,GAAG,oBAAoB,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC;EACxF,IAAI,MAAM,GAAG,aAAa,CAAC,WAAW,EAAE,QAAQ,EAAE,UAAU,CAAC;EAC7D,IAAI,gBAAgB,GAAG,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC;EAClD,IAAI,sBAAsB,GAAG,OAAO,CAAC,MAAM,EAAE,gBAAgB,CAAC;EAC9D,IAAI,eAAe,GAAG,QAAQ,CAAC,gBAAgB,EAAE,MAAM,CAAC;EACxD,IAAI,OAAO,KAAK,EAAE;EAClB;;EAEA,EAAE,SAAS,KAAK,GAAG;EACnB,IAAI,KAAK,GAAG,WAAW,GAAG,IAAI;EAC9B,IAAI,OAAO,UAAU;EACrB;;EAEA,EAAE,OAAO,WAAW;EACpB,IAAI,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC;EAC9C,IAAI,UAAU,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,MAAM;EAChD,IAAI,OAAO,QAAQ,EAAE;EACrB,GAAG;EACH;;ECjKO,SAAS,eAAe,CAAC,KAAK,EAAE;EACvC,EAAE,OAAO,SAAS,CAAC,EAAE,CAAC,EAAE;EACxB,IAAI,IAAI,CAAC,GAAGA,MAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAC/B,QAAQ,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;EACpB,QAAQ,EAAE,GAAGD,KAAG,CAAC,CAAC,CAAC;EACnB,QAAQ,EAAE,GAAGD,KAAG,CAAC,CAAC,CAAC;EACnB,IAAI,OAAO;EACX,MAAMD,OAAK,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;EAC3B,MAAMI,MAAI,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC;EAC1B,KAAK;EACL;EACA;;ECtBO,SAAS,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE;EACvC,EAAE,IAAI,EAAE,GAAGH,KAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,GAAGA,KAAG,CAAC,CAAC,CAAC,GAAG,EAAE;EACtC,EAAE,OAAO,CAAC,EAAE,GAAGC,KAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEA,KAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACtC;;EAEA,gBAAgB,CAAC,MAAM,GAAG,eAAe,CAAC,SAAS,CAAC,EAAE;EACtD,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC;;EAEa,yBAAQ,GAAG;EAC1B,EAAE,OAAO,UAAU,CAAC,gBAAgB;EACpC,OAAO,KAAK,CAAC,GAAG;EAChB,OAAO,SAAS,CAAC,GAAG,CAAC;EACrB;;ECjBA,MAAM,OAAO,GAAG,IAAI;;EAEL,MAAM,IAAI,CAAC;EAC1B,EAAE,WAAW,GAAG;EAChB,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG;EACvB,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;EAC/B,IAAI,IAAI,CAAC,CAAC,GAAG,EAAE;EACf;EACA,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;EACf,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;EACxE;EACA,EAAE,SAAS,GAAG;EACd,IAAI,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,EAAE;EAC3B,MAAM,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG;EAC9C,MAAM,IAAI,CAAC,CAAC,IAAI,GAAG;EACnB;EACA;EACA,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;EACf,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;EAClD;EACA,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;EACf,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;EAC1B,IAAI,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC;EACpB,IAAI,MAAM,EAAE,GAAG,CAAC;EAChB,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC;EACjD,IAAI,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;EACnD,SAAS,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,OAAO,EAAE,IAAI,CAAC,CAAC,IAAI,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE;EAClH,IAAI,IAAI,CAAC,CAAC,EAAE;EACZ,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;EAChG;EACA,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;EACnB,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3F;EACA,EAAE,KAAK,GAAG;EACV,IAAI,OAAO,IAAI,CAAC,CAAC,IAAI,IAAI;EACzB;EACA;;ECpCe,MAAM,OAAO,CAAC;EAC7B,EAAE,WAAW,GAAG;EAChB,IAAI,IAAI,CAAC,CAAC,GAAG,EAAE;EACf;EACA,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;EACf,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACvB;EACA,EAAE,SAAS,GAAG;EACd,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;EAClC;EACA,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;EACf,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACvB;EACA,EAAE,KAAK,GAAG;EACV,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI;EACxC;EACA;;ECbe,MAAM,OAAO,CAAC;EAC7B,EAAE,WAAW,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE;EACrE,IAAI,IAAI,EAAE,CAAC,IAAI,GAAG,CAAC,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG,CAAC,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC;EACrH,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ;EAC5B,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;EACtE,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;EAC/D,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI;EACtC,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI;EACtC,IAAI,IAAI,CAAC,KAAK,EAAE;EAChB;EACA,EAAE,MAAM,GAAG;EACX,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;EAC1B,IAAI,IAAI,CAAC,KAAK,EAAE;EAChB,IAAI,OAAO,IAAI;EACf;EACA,EAAE,KAAK,GAAG;EACV,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,SAAS,CAAC,EAAE,OAAO,CAAC,GAAG,IAAI;EAC/D,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;;EAEf;EACA,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;EACxG,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;EAC9E,MAAM,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EACjC,MAAM,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EACrC,MAAM,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EACrC,MAAM,MAAM,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;EAC3B,MAAM,MAAM,EAAE,GAAG,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;EAC/B,MAAM,MAAM,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;EAC3B,MAAM,MAAM,EAAE,GAAG,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;EAC/B,MAAM,MAAM,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;EAC3B,MAAM,MAAM,EAAE,GAAG,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;;EAE/B,MAAM,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;EACxB,MAAM,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;EACxB,MAAM,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;EACxB,MAAM,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;EACxB,MAAM,MAAM,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;;EAExC,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE;EAC/B;EACA;EACA;EACA,QAAQ,IAAI,EAAE,KAAK,SAAS,EAAE;EAC9B,UAAU,EAAE,GAAG,EAAE,GAAG,CAAC;EACrB,UAAU,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE,EAAE,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAC5E,UAAU,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,IAAI,CAAC,MAAM;EAC9C;EACA,QAAQ,MAAM,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;EAClE,QAAQ,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;EAClC,QAAQ,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;EAClC,OAAO,MAAM;EACb,QAAQ,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE;EACxB,QAAQ,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;EACpC,QAAQ,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;EACpC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;EACxC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;EACxC;EACA,MAAM,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC;EAC1B,MAAM,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EAC9B;;EAEA;EACA,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;EACjC,IAAI,IAAI,EAAE,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC;EACtB,IAAI,IAAI,EAAE,EAAE,EAAE,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;EAC9B,IAAI,IAAI,EAAE,EAAE,EAAE,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAClC,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;EACnB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;EAC1C,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;EACjB,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE;EAC/B,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAC5D,MAAM,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE;EAC7C,MAAM,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;EACjD;EACA;EACA,EAAE,MAAM,CAAC,OAAO,EAAE;EAClB,IAAI,MAAM,MAAM,GAAG,OAAO,IAAI,IAAI,GAAG,OAAO,GAAG,IAAI,IAAI,GAAG,SAAS;EACnE,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE,aAAa,EAAE,OAAO,CAAC,GAAG,IAAI;EAC/E,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,OAAO,IAAI;EACrC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;EACtD,MAAM,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;EAC5B,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE;EACjB,MAAM,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EACtC,MAAM,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EACtC,MAAM,MAAM,EAAE,GAAG,aAAa,CAAC,EAAE,CAAC;EAClC,MAAM,MAAM,EAAE,GAAG,aAAa,CAAC,EAAE,GAAG,CAAC,CAAC;EACtC,MAAM,MAAM,EAAE,GAAG,aAAa,CAAC,EAAE,CAAC;EAClC,MAAM,MAAM,EAAE,GAAG,aAAa,CAAC,EAAE,GAAG,CAAC,CAAC;EACtC,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC;EAClD;EACA,IAAI,IAAI,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;EACtC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;EAC1C,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;EAC3B,MAAM,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EAC/C,MAAM,MAAM,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;EAChC,MAAM,MAAM,CAAC,GAAG,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC;EACpC,MAAM,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC;EACtB,MAAM,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EACnE,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;EAC3D;EACA,IAAI,OAAO,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE;EACnC;EACA,EAAE,YAAY,CAAC,OAAO,EAAE;EACxB,IAAI,MAAM,MAAM,GAAG,OAAO,IAAI,IAAI,GAAG,OAAO,GAAG,IAAI,IAAI,GAAG,SAAS;EACnE,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;EACpF,IAAI,OAAO,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE;EACnC;EACA,EAAE,UAAU,CAAC,CAAC,EAAE,OAAO,EAAE;EACzB,IAAI,MAAM,MAAM,GAAG,OAAO,IAAI,IAAI,GAAG,OAAO,GAAG,IAAI,IAAI,GAAG,SAAS;EACnE,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;EAChC,IAAI,IAAI,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;EAC3C,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;EACxC,IAAI,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM;EACzB,IAAI,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC;EAClF,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;EACnC,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;EAClE,QAAQ,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAChD;EACA,IAAI,OAAO,CAAC,SAAS,EAAE;EACvB,IAAI,OAAO,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE;EACnC;EACA,EAAE,CAAC,YAAY,GAAG;EAClB,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI;EACrC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;EACvD,MAAM,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;EACtC,MAAM,IAAI,IAAI,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,MAAM,IAAI;EAC1C;EACA;EACA,EAAE,WAAW,CAAC,CAAC,EAAE;EACjB,IAAI,MAAM,OAAO,GAAG,IAAI,OAAO;EAC/B,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,OAAO,CAAC;EAC/B,IAAI,OAAO,OAAO,CAAC,KAAK,EAAE;EAC1B;EACA,EAAE,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;EAC1C,IAAI,IAAI,CAAC;EACT,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,CAAC;EACvC,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,CAAC;EACvC,IAAI,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;EAC9B,MAAM,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC;EAC5B,MAAM,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC;EAC5B,KAAK,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE;EAC9D,MAAM,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC,MAAM,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC;EACA;EACA,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;EACpB,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,KAAK;EAC5D,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC;EAC7C;EACA,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE;EAChB,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;EAC5B,IAAI,IAAI,EAAE,EAAE,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;EACxD,MAAM,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;EAC9B;EACA,MAAM,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE;EACvE,QAAQ,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE;EAC3D,UAAU,IAAI,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE;EAC9B,iBAAiB,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,GAAG,CAAC;EACzC,iBAAiB,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE;EAC5D,iBAAiB,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE;EAC/D,YAAY,MAAM,CAAC;EACnB,YAAY,MAAM,IAAI;EACtB;EACA;EACA;EACA;EACA;EACA,EAAE,KAAK,CAAC,CAAC,EAAE;EACX,IAAI,MAAM,CAAC,aAAa,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,GAAG,IAAI;EAC3E,IAAI,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC;EACzB,IAAI,IAAI,EAAE,KAAK,EAAE,EAAE,OAAO,IAAI,CAAC;EAC/B,IAAI,MAAM,MAAM,GAAG,EAAE;EACrB,IAAI,IAAI,CAAC,GAAG,EAAE;EACd,IAAI,GAAG;EACP,MAAM,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;EACjC,MAAM,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EACjE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;EACrC,MAAM,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,MAAM;EACpC,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;EACtB,KAAK,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE;EACjC,IAAI,OAAO,MAAM;EACjB;EACA,EAAE,KAAK,CAAC,CAAC,EAAE;EACX;EACA,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;EACpD,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC;EACrG;EACA,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;EAChC,IAAI,IAAI,MAAM,KAAK,IAAI,EAAE,OAAO,IAAI;EACpC,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,IAAI;EAC7B,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;EACnB,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;EACzC,UAAU,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAC1E,UAAU,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;EACtC;EACA,EAAE,WAAW,CAAC,CAAC,EAAE,MAAM,EAAE;EACzB,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM;EAC3B,IAAI,IAAI,CAAC,GAAG,IAAI;EAChB,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;EACtD,IAAI,IAAI,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,CAAC;EACzC,IAAI,IAAI,EAAE,EAAE,EAAE,GAAG,CAAC;EAClB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;EACnC,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;EAC1D,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,CAAC;EAC5C,MAAM,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;EAChC,QAAQ,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;EACvB,QAAQ,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;EAC7B,aAAa,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;EACzB,OAAO,MAAM;EACb,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EACjC,QAAQ,IAAI,EAAE,KAAK,CAAC,EAAE;EACtB,UAAU,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,MAAM,IAAI,EAAE;EACxE,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC;EAClC,SAAS,MAAM;EACf,UAAU,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,MAAM,IAAI,EAAE;EACxE,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC;EAClC,UAAU,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,CAAC;EAChD,UAAU,IAAI,EAAE,IAAI,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC;EAC1D,UAAU,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC;EACjC,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7B;EACA,QAAQ,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,CAAC;EAC9C,QAAQ,IAAI,EAAE,IAAI,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC;EACxD,QAAQ,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC;EAC/B,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;EAC3B;EACA;EACA,IAAI,IAAI,CAAC,EAAE;EACX,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9C,MAAM,IAAI,EAAE,IAAI,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC;EACtD,KAAK,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE;EAC3F,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC;EACrG;EACA,IAAI,OAAO,CAAC;EACZ;EACA,EAAE,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;EACvC;EACA,IAAI,MAAM,IAAI,GAAG,EAAE,GAAG,EAAE;EACxB,IAAI,IAAI,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACjE,IAAI,OAAO,IAAI,EAAE;EACjB,MAAM,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,OAAO,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACjF,MAAM,IAAI,EAAE,GAAG,EAAE,EAAE,OAAO,IAAI;EAC9B,MAAM,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE;EAC5B,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI;EACtF,WAAW,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI;EAC3F,WAAW,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI;EAC3F,WAAW,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI;EAC3E,MAAM,IAAI,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,CAAC;EAC3D,WAAW,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,CAAC;EACxD;EACA;EACA,EAAE,aAAa,CAAC,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;EAC/C,IAAI,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;EACjC,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACtE,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACzF,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EACpC,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;EAChG,QAAQ,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EACpD,QAAQ,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM;EACnE;EACA,KAAK,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE;EAC3F,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC;EAClG;EACA,IAAI,OAAO,CAAC;EACZ;EACA,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;EACzB,IAAI,OAAO,EAAE,KAAK,EAAE,EAAE;EACtB,MAAM,IAAI,CAAC,EAAE,CAAC;EACd,MAAM,QAAQ,EAAE;EAChB,QAAQ,KAAK,MAAM,EAAE,EAAE,GAAG,MAAM,CAAC,CAAC,SAAS;EAC3C,QAAQ,KAAK,MAAM,EAAE,EAAE,GAAG,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM;EACtE,QAAQ,KAAK,MAAM,EAAE,EAAE,GAAG,MAAM,CAAC,CAAC,SAAS;EAC3C,QAAQ,KAAK,MAAM,EAAE,EAAE,GAAG,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM;EACtE,QAAQ,KAAK,MAAM,EAAE,EAAE,GAAG,MAAM,CAAC,CAAC,SAAS;EAC3C,QAAQ,KAAK,MAAM,EAAE,EAAE,GAAG,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM;EACtE,QAAQ,KAAK,MAAM,EAAE,EAAE,GAAG,MAAM,CAAC,CAAC,SAAS;EAC3C,QAAQ,KAAK,MAAM,EAAE,EAAE,GAAG,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM;EACtE;EACA;EACA;EACA,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;EACpE,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC;EACpC;EACA;EACA,IAAI,OAAO,CAAC;EACZ;EACA,EAAE,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;EAC3B,IAAI,IAAI,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;EAC7B,IAAI,IAAI,EAAE,GAAG,CAAC,EAAE;EAChB,MAAM,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI;EACtC,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE;EAC/E,KAAK,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE;EACvB,MAAM,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI;EACtC,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE;EAC/E;EACA,IAAI,IAAI,EAAE,GAAG,CAAC,EAAE;EAChB,MAAM,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI;EACtC,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE;EAC/E,KAAK,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE;EACvB,MAAM,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI;EACtC,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE;EAC/E;EACA,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EACjB;EACA,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;EAClB,IAAI,OAAO,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,GAAG;EAC9B,UAAU,CAAC,KAAK,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,MAAM;EAC3C,WAAW,CAAC,KAAK,IAAI,CAAC,IAAI,GAAG;EAC7B,UAAU,CAAC,KAAK,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,MAAM,CAAC;EAC5C;EACA,EAAE,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE;EACpB,IAAI,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG;EAC5B,UAAU,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,MAAM;EACzC,WAAW,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG;EAC3B,UAAU,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,MAAM,CAAC;EAC1C;EACA,EAAE,SAAS,CAAC,CAAC,EAAE;EACf,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;EAC3B,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE;EAC3C,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM;EAC5D,QAAQ,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;EAC9F,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC;EAChC;EACA;EACA,MAAM,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI;EAC7B;EACA,IAAI,OAAO,CAAC;EACZ;EACA;;ECtUA,MAAM,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG;;EAEvC,SAAS,MAAM,CAAC,CAAC,EAAE;EACnB,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;EACb;;EAEA,SAAS,MAAM,CAAC,CAAC,EAAE;EACnB,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;EACb;;EAEA;EACA,SAAS,SAAS,CAAC,CAAC,EAAE;EACtB,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC;EAC/B,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;EAChD,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;EAC9B,UAAU,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;EAClC,UAAU,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;EAClC,UAAU,KAAK,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;EAC1E,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAC3E,IAAI,IAAI,KAAK,GAAG,KAAK,EAAE,OAAO,KAAK;EACnC;EACA,EAAE,OAAO,IAAI;EACb;;EAEA,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;EACzB,EAAE,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;EAC3D;;EAEe,MAAM,QAAQ,CAAC;EAC9B,EAAE,OAAO,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,MAAM,EAAE,EAAE,GAAG,MAAM,EAAE,IAAI,EAAE;EACtD,IAAI,OAAO,IAAI,QAAQ,CAAC,QAAQ,IAAI;EACpC,UAAU,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI;EACxC,UAAU,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;EAChE;EACA,EAAE,WAAW,CAAC,MAAM,EAAE;EACtB,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC;EAC7C,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;EACpD,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;EACvD,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM;EACzC,IAAI,IAAI,CAAC,KAAK,EAAE;EAChB;EACA,EAAE,MAAM,GAAG;EACX,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;EAC7B,IAAI,IAAI,CAAC,KAAK,EAAE;EAChB,IAAI,OAAO,IAAI;EACf;EACA,EAAE,KAAK,GAAG;EACV,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,MAAM,GAAG,IAAI,CAAC,MAAM;;EAEpD;EACA,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,EAAE;EACrD,MAAM,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;EAC5E,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAChG,MAAM,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;EAChF,QAAQ,MAAM,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;EACvF,QAAQ,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;EAC3E,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;EACzD,QAAQ,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7D,QAAQ,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC5B,QAAQ,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAChC;EACA,MAAM,IAAI,CAAC,WAAW,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC;EAC/C,KAAK,MAAM;EACX,MAAM,OAAO,IAAI,CAAC,SAAS;EAC3B;;EAEA,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS;EACjE,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI;EAClD,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS;EACjE,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;EACzC,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;;EAE9C;EACA;EACA;EACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;EACtD,MAAM,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACtD,MAAM,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EAClE;EACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;EACjD,MAAM,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;EAC5B;;EAEA;EACA,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;EAC7C,MAAM,IAAI,CAAC,SAAS,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;EACjD,MAAM,IAAI,CAAC,SAAS,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;EACjD,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;EACjC,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;EAC1B,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;EAC7B,QAAQ,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;EAC5B,QAAQ,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;EACnC,QAAQ,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;EACnC;EACA;EACA;EACA,EAAE,OAAO,CAAC,MAAM,EAAE;EAClB,IAAI,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;EACpC;EACA,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE;EAChB,IAAI,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,GAAG,IAAI;;EAE7E;EACA,IAAI,IAAI,SAAS,EAAE;EACnB,MAAM,MAAM,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;EACpC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,MAAM,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;EACvC,MAAM,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;EAC1D,MAAM;EACN;;EAEA,IAAI,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC;EACzB,IAAI,IAAI,EAAE,KAAK,EAAE,EAAE,OAAO;EAC1B,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE;EACvB,IAAI,GAAG;EACP,MAAM,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC;EAC7B,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;EACrC,MAAM,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,OAAO;EACrC,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;EACtB,MAAM,IAAI,CAAC,KAAK,EAAE,EAAE;EACpB,QAAQ,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC;EACzD,QAAQ,IAAI,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC;EAC7B,QAAQ;EACR;EACA,KAAK,QAAQ,CAAC,KAAK,EAAE;EACrB;EACA,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE;EACpB,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,EAAE;EACzD,IAAI,MAAM,EAAE,GAAG,CAAC;EAChB,IAAI,IAAI,CAAC;EACT,IAAI,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC;EACvE,IAAI,OAAO,CAAC;EACZ;EACA,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;EACjB,IAAI,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,GAAG,IAAI;EAC1E,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,MAAM,IAAI,CAAC,CAAC;EAClF,IAAI,IAAI,CAAC,GAAG,CAAC;EACb,IAAI,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EACtE,IAAI,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC;EACzB,IAAI,IAAI,CAAC,GAAG,EAAE;EACd,IAAI,GAAG;EACP,MAAM,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;EAC1B,MAAM,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EAC1E,MAAM,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC;EACjC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;EACrC,MAAM,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,MAAM;EACpC,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;EACtB,MAAM,IAAI,CAAC,KAAK,EAAE,EAAE;EACpB,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC;EACnD,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE;EACrB,UAAU,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC;EACtF;EACA,QAAQ;EACR;EACA,KAAK,QAAQ,CAAC,KAAK,EAAE;EACrB,IAAI,OAAO,CAAC;EACZ;EACA,EAAE,MAAM,CAAC,OAAO,EAAE;EAClB,IAAI,MAAM,MAAM,GAAG,OAAO,IAAI,IAAI,GAAG,OAAO,GAAG,IAAI,IAAI,GAAG,SAAS;EACnE,IAAI,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,GAAG,IAAI;EAC/C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;EACtD,MAAM,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;EAC5B,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE;EACjB,MAAM,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EACjC,MAAM,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EACjC,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EAChD,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EAChD;EACA,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;EAC5B,IAAI,OAAO,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE;EACnC;EACA,EAAE,YAAY,CAAC,OAAO,EAAE,CAAC,EAAE;EAC3B,IAAI,IAAI,CAAC,KAAK,SAAS,KAAK,CAAC,OAAO,IAAI,OAAO,OAAO,CAAC,MAAM,KAAK,UAAU,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,OAAO,GAAG,IAAI;EAC1G,IAAI,CAAC,GAAG,CAAC,IAAI,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;EAC/B,IAAI,MAAM,MAAM,GAAG,OAAO,IAAI,IAAI,GAAG,OAAO,GAAG,IAAI,IAAI,GAAG,SAAS;EACnE,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI;EACzB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;EACtD,MAAM,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;EAC5C,MAAM,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;EAC9B,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;EAClC;EACA,IAAI,OAAO,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE;EACnC;EACA,EAAE,UAAU,CAAC,OAAO,EAAE;EACtB,IAAI,MAAM,MAAM,GAAG,OAAO,IAAI,IAAI,GAAG,OAAO,GAAG,IAAI,IAAI,GAAG,SAAS;EACnE,IAAI,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,IAAI;EAC/B,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM;EAC1C,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAC5C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;EAChC,MAAM,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;EAC3B,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAC9C;EACA,IAAI,OAAO,CAAC,SAAS,EAAE;EACvB,IAAI,OAAO,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE;EACnC;EACA,EAAE,WAAW,GAAG;EAChB,IAAI,MAAM,OAAO,GAAG,IAAI,OAAO;EAC/B,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;EAC5B,IAAI,OAAO,OAAO,CAAC,KAAK,EAAE;EAC1B;EACA,EAAE,cAAc,CAAC,CAAC,EAAE,OAAO,EAAE;EAC7B,IAAI,MAAM,MAAM,GAAG,OAAO,IAAI,IAAI,GAAG,OAAO,GAAG,IAAI,IAAI,GAAG,SAAS;EACnE,IAAI,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,IAAI;EACpC,IAAI,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;EACpC,IAAI,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EACnC,IAAI,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EACnC,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EAC9C,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EAC9C,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EAC9C,IAAI,OAAO,CAAC,SAAS,EAAE;EACvB,IAAI,OAAO,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE;EACnC;EACA,EAAE,CAAC,gBAAgB,GAAG;EACtB,IAAI,MAAM,CAAC,SAAS,CAAC,GAAG,IAAI;EAC5B,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;EAC1D,MAAM,MAAM,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;EACnC;EACA;EACA,EAAE,eAAe,CAAC,CAAC,EAAE;EACrB,IAAI,MAAM,OAAO,GAAG,IAAI,OAAO;EAC/B,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,OAAO,CAAC;EACnC,IAAI,OAAO,OAAO,CAAC,KAAK,EAAE;EAC1B;EACA;;EAEA,SAAS,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE;EACzC,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM;EACzB,EAAE,MAAM,KAAK,GAAG,IAAI,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC;EACvC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;EAC9B,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;EACvB,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC;EAC9C,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC;EAClD;EACA,EAAE,OAAO,KAAK;EACd;;EAEA,UAAU,YAAY,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE;EAC7C,EAAE,IAAI,CAAC,GAAG,CAAC;EACX,EAAE,KAAK,MAAM,CAAC,IAAI,MAAM,EAAE;EAC1B,IAAI,MAAM,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC;EACrC,IAAI,MAAM,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC;EACrC,IAAI,EAAE,CAAC;EACP;EACA;;ECrPO,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE;EAClB,MAAM,MAAM,GAAG,EAAE,GAAG,CAAC;;EAIrB,MAAM,OAAO,GAAG,GAAG,GAAG,EAAE;EACxB,MAAM,OAAO,GAAG,EAAE,GAAG,GAAG;EAIxB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK;EACxB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG;EAKpB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG;EACpB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG;EAEpB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG;EACpB,MAAM,IAAI;EACjB,EAAE,IAAI,CAAC,IAAI;EACX,EAAE,UAAU,CAAC,EAAE;EACf,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;EACrC,GAAG;EACI,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI;;EAOtB,SAAS,IAAI,CAAC,CAAC,EAAE;EACxB,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;EACzD;;ECvBO,SAAS,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE;EACnC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAChD;;EAEO,SAAS,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE;EACrC,EAAE,OAAO;EACT,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC7B,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC7B,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC7B,GAAG;EACH;;EAOO,SAAS,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE;EACnC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAChD;;EAYO,SAAS,kBAAkB,CAAC,CAAC,EAAE;EACtC,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACvD,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACvC;;ECzBA;EACA,SAAS,SAAS,CAAC,SAAS,EAAE;EAC9B,EAAE,OAAO;EACT,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO;EAC/C,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO;EACjD,GAAG;EACH;;EAEA;EACA,SAAS,SAAS,CAAC,WAAW,EAAE;EAChC,EAAE,MAAM,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,OAAO;EACzC,IAAI,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,OAAO;EAClC,IAAI,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC;EACrB,EAAE,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;EAC/D;;EAEA;EACO,SAAS,MAAM,CAAC,QAAQ,EAAE;EACjC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC;EAC9C,EAAE,OAAOsB,YAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAEC,cAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1D;;EAEO,SAAS,WAAW,CAAC,MAAM,EAAE;EACpC,EAAE,MAAM,QAAQ,GAAG,iBAAiB,CAAC,MAAM,CAAC;EAC5C,IAAI,SAAS,GAAG,aAAa,CAAC,QAAQ,CAAC;EACvC,IAAI,KAAK,GAAG,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC;EACxC,IAAI,SAAS,GAAG,aAAa,CAAC,SAAS,EAAE,MAAM,CAAC,MAAM,CAAC;EACvD,IAAI,IAAI,GAAG,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC;EACtC;EACA,IAAI,aAAa,GAAG,iBAAiB,CAAC,SAAS,EAAE,MAAM,CAAC;EACxD,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,YAAY,CAAC,aAAa,EAAE,SAAS,EAAE,MAAM,CAAC;EAC1E,IAAI,IAAI,GAAG,QAAQ,CAAC,QAAQ,CAAC;EAC7B,IAAI,IAAI,GAAG,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC;EACtC;EACA,IAAI,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC;EAC7C,EAAE,OAAO;EACT,IAAI,QAAQ;EACZ,IAAI,KAAK;EACT,IAAI,SAAS;EACb,IAAI,OAAO;EACX,IAAI,SAAS;EACb,IAAI,QAAQ;EACZ,IAAI,IAAI;EACR,IAAI,IAAI;EACR,IAAI,QAAQ;EACZ,IAAI,IAAI;EACR,GAAG;EACH;;EAEA,SAAS,QAAQ,CAAC,SAAS,EAAE,MAAM,EAAE;EACrC,EAAE,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;EAC3B,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACvB,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACrB,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACrB,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;EAChC;;EAEA,EAAE,OAAO,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE;EACnC,IAAI,IAAI,IAAI,KAAK,SAAS,EAAE,IAAI,GAAG,CAAC;EACpC,IAAI,IAAI,IAAI;EACZ,MAAM,IAAI;EACV,MAAM,KAAK,GAAG,IAAI;EAClB,IAAI,MAAM,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACjC,IAAI,GAAG;EACP,MAAM,IAAI,GAAG,IAAI;EACjB,MAAM,IAAI,GAAG,IAAI;EACjB,MAAM,IAAI,GAAG,SAAS,CAAC,GAAG,EAAE,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;EACpD,MAAM,SAAS,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;EACrC,QAAQ,IAAI,KAAK,GAAG,SAAS,CAAC,GAAG,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;EACxD,QAAQ,IAAI,KAAK,GAAG,IAAI,EAAE;EAC1B,UAAU,IAAI,GAAG,KAAK;EACtB,UAAU,IAAI,GAAG,CAAC;EAClB,UAAU,KAAK,GAAG,CAAC;EACnB,UAAU;EACV;EACA,OAAO,CAAC;EACR,KAAK,QAAQ,IAAI,KAAK,IAAI;;EAE1B,IAAI,OAAO,KAAK;EAChB,GAAG;EACH;;EAEA,SAAS,iBAAiB,CAAC,MAAM,EAAE;EACnC,EAAE,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,OAAO,EAAE;;EAElC;EACA,EAAE,IAAI,KAAK,GAAG,CAAC;EACf,EAAE,OAAO,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC;;EAE/E,EAAE,MAAM,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;EACtC,IAAI,UAAU,GAAG,gBAAgB;EACjC,OAAO,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACvB,OAAO,KAAK,CAAC,CAAC;EACd,OAAO,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;EACjC,EAAE,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC;;EAEjC,EAAE,MAAM,KAAK,GAAG,EAAE;EAClB,EAAE,IAAI,IAAI,GAAG,CAAC;EACd,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;EACjD,IAAI,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;EACjD,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;EAC/C,SAAS,IAAI,CAAC,GAAG,IAAI,EAAE,IAAI,GAAG,CAAC;EAC/B;;EAEA,EAAE,MAAM,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;;EAE9B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;;EAE9C;EACA,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;EACvB,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;EACxB,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;;EAExB,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;;EAExC,EAAE,QAAQ,CAAC,UAAU,GAAG,UAAU;;EAElC;EACA,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,QAAQ;EAEpD,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;EACpD,IAAI,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;EAC1B,MAAM,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;EAC1C,MAAM,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;EAC1C,MAAM,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;EAC5B,MAAM,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;EAC5B,MAAM,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EACtB,MAAM,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EACtB,MAAM,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EACtC,MAAM,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EACxD,MAAM,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;EACxD,MAAM,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;EAExD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;EACtB,KAAK,MAAM,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,EAAE;EACrD,MAAM,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EAC1B;EACA;;EAEA;EACA;EACA,EAAE,OAAO,QAAQ;EACjB;;EAEA,SAAS,SAAS,CAAC,SAAS,EAAE,MAAM,EAAE;EACtC,EAAE,MAAM,MAAM,GAAG,IAAI,GAAG,EAAE;EAC1B,EAAE,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC1C,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;EAC7B,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE;EAC3B,IAAI,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;EAC/C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;EACnC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;EACrB,MAAM,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;EACpD;EACA,GAAG,CAAC;EACJ,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;EAC5D;;EAEA,SAAS,aAAa,CAAC,QAAQ,EAAE;EACjC,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,QAAQ;EAChC,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE;;EAE3B,EAAE,MAAM,aAAa,GAAG,EAAE;EAC1B,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;EACxD,IAAI,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;EAC9B,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAC9B,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAC9B,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;EAC5B,MAAM,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EACnC;EACA;EACA,EAAE,OAAO,aAAa;EACtB;;EAEA,SAAS,iBAAiB,CAAC,SAAS,EAAE,MAAM,EAAE;EAC9C;EACA,EAAE,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;EAChC,IAAI,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC;EACtD,MAAM,CAAC,GAAG,YAAY;EACtB,QAAQ,YAAY,CAACA,cAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,cAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1D,QAAQA,cAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACxB,OAAO;EACP,IAAI,OAAO,SAAS,CAACC,kBAAS,CAAC,CAAC,CAAC,CAAC;EAClC,GAAG,CAAC;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,SAAS,aAAa,CAAC,SAAS,EAAE,OAAO,EAAE;EAC3C,EAAE,MAAM,SAAS,GAAG,EAAE;EACtB,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;EAC7B,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;EAChC,MAAM,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;EACtB,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;EAC5B,MAAM,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE;EACvC,MAAM,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;EAC1B;EACA,GAAG,CAAC;;EAEJ;EACA,EAAE,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;EAC9B,IAAI,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACjE,SAAS,IAAI,OAAO,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EAC7C;;EAEA,EAAE,OAAO,SAAS;EAClB;;EAEA,SAAS,YAAY,CAAC,aAAa,EAAE,SAAS,EAAE,MAAM,EAAE;EACxD,EAAE,MAAM,QAAQ,GAAG,EAAE;;EAErB,EAAE,MAAM,OAAO,GAAG,aAAa,CAAC,KAAK,EAAE;;EAEvC,EAAE,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;EAC9B,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE;EACvD,IAAI,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;EAC7B;EACA,MAAM,MAAM,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;EACpC,QAAQ,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;EAChC,QAAQ,CAAC,GAAGA,kBAAS,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACzC,QAAQ,CAAC,GAAGA,kBAAS,CAACD,cAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClC,QAAQ,CAAC,GAAGA,cAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACvB,MAAM,MAAM,IAAI,GAAG;EACnB,QAAQ,CAAC;EACT,QAAQA,cAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACnB,QAAQA,cAAK,CAACA,cAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7B,QAAQA,cAAK,CAACA,cAAK,CAACA,cAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACvC;EACA,SAAS,GAAG,CAAC,SAAS;EACtB,SAAS,GAAG,CAAC,UAAU,CAAC;EACxB,MAAM;EACN,QAAQ,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;EAC3B,QAAQ,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,CAAC;EAC7C,QAAQ,EAAE,QAAQ,EAAE,OAAO;EAC3B;EACA;EACA;;EAEA,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK;EAChC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;EAChC,MAAM,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;EACtB,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;EAC5B,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;EAC5B,MAAM,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE;EACrC,MAAM,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC5C;EACA,GAAG,CAAC;;EAEJ;EACA,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK;EAC3C,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EAC1C;EACA,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EAC5C,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;EAC7B,UAAU,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,UAAU;EACV;EACA;EACA;;EAEA,IAAI,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;EACtB,MAAM,OAAO,CAAC;EACd,KAAK,MAAM,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,EAAE;EAC9B,MAAM,MAAM,EAAE,GAAG,UAAU;EAC3B,UAAU,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/B,UAAU,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/B,UAAU,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,SAAS;EACT,QAAQ,EAAE,GAAG,UAAU;EACvB,UAAU,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/B,UAAU,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/B,UAAU,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,SAAS;EACT,MAAM,MAAM,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC;EAC/B,QAAQ,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC;EAC3B,MAAM,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EACjC;EACA,GAAG,CAAC;;EAEJ,EAAE,SAAS,UAAU,CAAC,KAAK,EAAE;EAC7B,IAAI,IAAI,CAAC,GAAG,EAAE;EACd,IAAI,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;EAChE,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,MAAM;EAC1E,KAAK,CAAC;EACN,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;EACxD,IAAI,OAAO,CAAC;EACZ;;EAEA,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE;EACzC;;EAEA,SAAS,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;EAC7B,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;EAClB,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;EAClB,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;EAClB,EAAE,MAAM,CAAC,GAAG,IAAI,CAACD,YAAG,CAACC,cAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACrC,EAAE,OAAO,SAAS,CAACC,kBAAS,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EACnE;;EAEA,SAAS,QAAQ,CAAC,QAAQ,EAAE;EAC5B,EAAE,MAAM,IAAI,GAAG,EAAE;EACjB,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;EAC7B,IAAI,IAAI,CAAC,IAAI,EAAE;EACf,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;EACjC,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,EAAE;EACxB,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClC,MAAM,CAAC,GAAG,CAAC;EACX;EACA,GAAG,CAAC;EACJ,EAAE,OAAO,IAAI;EACb;;EAEA,SAAS,YAAY,CAAC,KAAK,EAAE,SAAS,EAAE;EACxC,EAAE,OAAO,UAAU,SAAS,EAAE;EAC9B,IAAI,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAE;EAC9B,MAAM,SAAS,GAAG,IAAI,GAAG,EAAE;EAC3B,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK;EAC/B,MAAM,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;EAC9B,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;EACnC,MAAM,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;EAC5B,KAAK,CAAC;;EAEN,IAAI,SAAS,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;EAC/B,MAAM,IAAI,CAAC,GAAG,CAAC;EACf,QAAQ,MAAM,GAAG,EAAE;EACnB,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;EAClC,QAAQ,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;EAC5D,QAAQ,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;EACjC,UAAU,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;EAC7B,UAAU,MAAM,GAAG,CAAC;EACpB;EACA;EACA,MAAM,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC;EAClC,KAAK,CAAC;;EAEN,IAAI,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;EAC7D,GAAG;EACH;;EAEA,SAAS,QAAQ,CAAC,SAAS,EAAE,MAAM,EAAE;EACrC,EAAE,MAAM,KAAK,GAAG,IAAI,GAAG,EAAE;EACzB,IAAI,IAAI,GAAG,EAAE;EACb,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;EACzB,IAAI,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK;EACzE,MAAM;EACN,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;EAChC,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;EACxC,QAAQ,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;EAC7C,WAAW,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvC;EACA,GAAG,CAAC;;EAEJ,EAAE,MAAM,MAAM,GAAG,IAAI,GAAG,EAAE;EAC1B,EAAE,IAAI,KAAK;EACX,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;EACvB,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;EAChC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1B,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;EAChB,GAAG,CAAC;;EAEJ,EAAE,IAAI,KAAK,KAAK,SAAS,EAAE,OAAO,IAAI;;EAEtC,EAAE,IAAI,IAAI,GAAG,KAAK;EAClB,EAAE,GAAG;EACL,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;EACnB,IAAI,IAAI,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;EAC5B,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;EACxB,IAAI,IAAI,GAAG,CAAC;EACZ,GAAG,QAAQ,IAAI,GAAG,EAAE,IAAI,IAAI,KAAK,KAAK;;EAEtC,EAAE,OAAO,IAAI;EACb;;EChZO,SAAS,UAAU,CAAC,IAAI,EAAE;EACjC,EAAE,MAAM,CAAC,GAAG,UAAU,IAAI,EAAE;EAC5B,IAAI,CAAC,CAAC,QAAQ,GAAG,IAAI;EACrB,IAAI,CAAC,CAAC,KAAK,GAAG,IAAI;;EAElB,IAAI,IAAI,OAAO,CAAC,CAAC,KAAK,KAAK,QAAQ,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE;EAC7E,MAAM,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,QAAQ;EAChC;EACA,IAAI,IAAI,OAAO,CAAC,CAAC,KAAK,KAAK,QAAQ,EAAE;EACrC,MAAM,MAAM,IAAI,GAAG,CAAC,CAAC;EACrB,SAAS,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EAC3C,SAAS,MAAM,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9C,MAAM,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;EACrC,MAAM,CAAC,CAAC,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC;EACxC;EACA,IAAI,OAAO,CAAC;EACZ,GAAG;;EAEH,EAAE,CAAC,CAAC,GAAG,GAAG,UAAU,CAAC,EAAE;EACvB,IAAI,IAAI,OAAO,CAAC,IAAI,QAAQ,IAAI,MAAM,IAAI,CAAC,EAAE;EAC7C,MAAM,OAAO,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B;EACA,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;EAC3B,GAAG;EACH,EAAE,CAAC,CAAC,GAAG,GAAG,UAAU,CAAC,EAAE;EACvB,IAAI,IAAI,OAAO,CAAC,IAAI,QAAQ,IAAI,MAAM,IAAI,CAAC,EAAE;EAC7C,MAAM,OAAO,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B;EACA,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;EAC3B,GAAG;;EAEH,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE;EACrB,IAAI,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG;EACxB,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;EACb,IAAI,OAAO,CAAC;EACZ,GAAG;EACH,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE;EACrB,IAAI,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG;EACxB,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;EACb,IAAI,OAAO,CAAC;EACZ,GAAG;;EAEH,EAAE,CAAC,CAAC,QAAQ,GAAG,UAAU,IAAI,EAAE;EAC/B,IAAI,IAAI,IAAI,KAAK,SAAS,EAAE;EAC5B,MAAM,CAAC,CAAC,IAAI,CAAC;EACb;;EAEA,IAAI,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,OAAO,KAAK;EACjC,IAAI,MAAM,IAAI,GAAG;EACjB,MAAM,IAAI,EAAE,mBAAmB;EAC/B,MAAM,QAAQ,EAAE,EAAE;EAClB,KAAK;EACL,IAAI,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EACzC,IAAI,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC;EACxC,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;EACzB,QAAQ,IAAI,EAAE,SAAS;EACvB,QAAQ,QAAQ,EAAE,CAAC;EACnB,YAAY;EACZ,YAAY;EACZ,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,WAAW,EAAE;EAC3B,gBAAgB,CAAC,GAAG,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;EACpE,eAAe;EACf,aAAa;EACb,QAAQ,UAAU,EAAE;EACpB,UAAU,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;EAC1B,UAAU,eAAe,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;EACtC,UAAU,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;EAC7C,SAAS;EACT,OAAO;EACP,KAAK;EACL,IAAI,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC;EAC5B,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;EACzB,QAAQ,IAAI,EAAE,SAAS;EACvB,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;EACpC,QAAQ,UAAU,EAAE;EACpB,UAAU,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;EAC1B,UAAU,eAAe,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;EACtC,UAAU,UAAU,EAAE,EAAE;EACxB,SAAS;EACT,OAAO,CAAC;EACR,IAAI,OAAO,IAAI;EACf,GAAG;;EAEH,EAAE,CAAC,CAAC,SAAS,GAAG,UAAU,IAAI,EAAE;EAChC,IAAI,IAAI,IAAI,KAAK,SAAS,EAAE;EAC5B,MAAM,CAAC,CAAC,IAAI,CAAC;EACb;EACA,IAAI,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,OAAO,KAAK;;EAEjC,IAAI,OAAO;EACX,MAAM,IAAI,EAAE,mBAAmB;EAC/B,MAAM,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC;EAC3B,SAAS,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,KAAK;EAC7B,UAAU,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;EAC3C,UAAU,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC;EAChD,UAAU,OAAO,GAAG;EACpB,SAAS;EACT,SAAS,MAAM,CAAC,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;EACxC,SAAS,GAAG,CAAC,CAAC,GAAG,MAAM;EACvB,UAAU,IAAI,EAAE,SAAS;EACzB,UAAU,UAAU,EAAE;EACtB,YAAY,YAAY,EAAE,GAAG,CAAC,MAAM;EACpC,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,SAAS;EAC3B,YAAY,WAAW,EAAE,CAAC,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3C,WAAW;EACX,SAAS,CAAC,CAAC;EACX,KAAK;EACL,GAAG;;EAEH,EAAE,CAAC,CAAC,KAAK,GAAG,UAAU,IAAI,EAAE;EAC5B,IAAI,IAAI,IAAI,KAAK,SAAS,EAAE;EAC5B,MAAM,CAAC,CAAC,IAAI,CAAC;EACb;EACA,IAAI,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,OAAO,KAAK;EACjC,IAAI,MAAM,UAAU,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EAC9C,QAAQ,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClD,OAAO;EACP,MAAM,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC;EAChD,IAAI,OAAO;EACX,MAAM,IAAI,EAAE,mBAAmB;EAC/B,MAAM,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM;EAChD,QAAQ,IAAI,EAAE,SAAS;EACvB,QAAQ,UAAU,EAAE;EACpB,UAAU,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/B,UAAU,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/B,UAAU,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC;EAC/B,UAAU,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;EACjC,SAAS;EACT,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,YAAY;EAC5B,UAAU,WAAW,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvD,SAAS;EACT,OAAO,CAAC,CAAC;EACT,KAAK;EACL,GAAG;;EAEH,EAAE,CAAC,CAAC,IAAI,GAAG,UAAU,IAAI,EAAE;EAC3B,IAAI,IAAI,IAAI,KAAK,SAAS,EAAE;EAC5B,MAAM,CAAC,CAAC,IAAI,CAAC;EACb;EACA,IAAI,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,OAAO,KAAK;EACjC,IAAI,OAAO;EACX,MAAM,IAAI,EAAE,iBAAiB;EAC7B,MAAM,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK;EAC/C,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,OAAO,CAAC;EACR,KAAK;EACL,GAAG;;EAEH,EAAE,CAAC,CAAC,QAAQ,GAAG,UAAU,IAAI,EAAE;EAC/B,IAAI,IAAI,IAAI,KAAK,SAAS,EAAE;EAC5B,MAAM,CAAC,CAAC,IAAI,CAAC;EACb;EACA,IAAI,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,OAAO,KAAK;EACjC,IAAI,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC,QAAQ;EAC5C,IAAI,MAAM,WAAW,GAAG,EAAE;EAC1B,IAAI,KAAK,MAAM,CAAC,IAAI,QAAQ,EAAE;EAC9B,MAAM,IAAI,CAAC,CAAC,EAAE;EACd,MAAM;EACN,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC;EACzD,QAAQ,CAAC,GAAG,CAAC;EACb,QAAQ,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;EAC3B,QAAQ;EACR,QAAQ,IAAI,EAAE,GAAG,EAAE,EAAE;EACrB,UAAU,WAAW,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;EACtD;EACA;EACA;EACA,IAAI,OAAO;EACX,MAAM,IAAI,EAAE,iBAAiB;EAC7B,MAAM,WAAW;EACjB,KAAK;EACL,GAAG;;EAEH,EAAE,CAAC,CAAC,MAAM,GAAG,SAAS;EACtB,EAAE,CAAC,CAAC,IAAI,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE;EACnC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC;EAC9C,IAAI,IAAI,CAAC,MAAM,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM;EACnE,MAAM,OAAO,CAAC,CAAC,MAAM;EACrB,GAAG;;EAEH,EAAE,CAAC,CAAC,IAAI,GAAG,UAAU,IAAI,EAAE;EAC3B,IAAI,IAAI,IAAI,KAAK,SAAS,EAAE;EAC5B,MAAM,CAAC,CAAC,IAAI,CAAC;EACb;EACA,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI;EAChC,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM;EACvB,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK;EAC3B,QAAQ;EACR,QAAQ;EACR,UAAU,IAAI,EAAE,SAAS;EACzB,UAAU,WAAW,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzE,SAAS;EACT,GAAG;;EAEH,EAAE,OAAc,CAAC,CAAC,IAAI,CAAC,CAAI;EAC3B;;EC5MO,SAAS,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE;EACzC,EAAE,QAAQ,SAAS,CAAC,MAAM;EAC1B,IAAI,KAAK,CAAC,EAAE;EACZ,IAAI,KAAK,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;EAChC,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;EAC/C;EACA,EAAE,OAAO,IAAI;EACb;;ECPe,eAAQ,CAAC,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE;EACzD,EAAE,WAAW,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,GAAG,SAAS;EACvD,EAAE,SAAS,CAAC,WAAW,GAAG,WAAW;EACrC;;EAEO,SAAS,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE;EAC3C,EAAE,IAAI,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC;EACjD,EAAE,KAAK,IAAI,GAAG,IAAI,UAAU,EAAE,SAAS,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC;EAC9D,EAAE,OAAO,SAAS;EAClB;;ECPO,SAAS,KAAK,GAAG;;EAEjB,IAAI,MAAM,GAAG,GAAG;EAChB,IAAI,QAAQ,GAAG,CAAC,GAAG,MAAM;;EAEhC,IAAI,GAAG,GAAG,qBAAqB;EAC/B,IAAI,GAAG,GAAG,mDAAmD;EAC7D,IAAI,GAAG,GAAG,oDAAoD;EAC9D,IAAI,KAAK,GAAG,oBAAoB;EAChC,IAAI,YAAY,GAAG,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;EAChE,IAAI,YAAY,GAAG,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;EAChE,IAAI,aAAa,GAAG,IAAI,MAAM,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;EACzE,IAAI,aAAa,GAAG,IAAI,MAAM,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;EACzE,IAAI,YAAY,GAAG,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;EAChE,IAAI,aAAa,GAAG,IAAI,MAAM,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;;EAEzE,IAAI,KAAK,GAAG;EACZ,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,YAAY,EAAE,QAAQ;EACxB,EAAE,IAAI,EAAE,QAAQ;EAChB,EAAE,UAAU,EAAE,QAAQ;EACtB,EAAE,KAAK,EAAE,QAAQ;EACjB,EAAE,KAAK,EAAE,QAAQ;EACjB,EAAE,MAAM,EAAE,QAAQ;EAClB,EAAE,KAAK,EAAE,QAAQ;EACjB,EAAE,cAAc,EAAE,QAAQ;EAC1B,EAAE,IAAI,EAAE,QAAQ;EAChB,EAAE,UAAU,EAAE,QAAQ;EACtB,EAAE,KAAK,EAAE,QAAQ;EACjB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,UAAU,EAAE,QAAQ;EACtB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,KAAK,EAAE,QAAQ;EACjB,EAAE,cAAc,EAAE,QAAQ;EAC1B,EAAE,QAAQ,EAAE,QAAQ;EACpB,EAAE,OAAO,EAAE,QAAQ;EACnB,EAAE,IAAI,EAAE,QAAQ;EAChB,EAAE,QAAQ,EAAE,QAAQ;EACpB,EAAE,QAAQ,EAAE,QAAQ;EACpB,EAAE,aAAa,EAAE,QAAQ;EACzB,EAAE,QAAQ,EAAE,QAAQ;EACpB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,QAAQ,EAAE,QAAQ;EACpB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,WAAW,EAAE,QAAQ;EACvB,EAAE,cAAc,EAAE,QAAQ;EAC1B,EAAE,UAAU,EAAE,QAAQ;EACtB,EAAE,UAAU,EAAE,QAAQ;EACtB,EAAE,OAAO,EAAE,QAAQ;EACnB,EAAE,UAAU,EAAE,QAAQ;EACtB,EAAE,YAAY,EAAE,QAAQ;EACxB,EAAE,aAAa,EAAE,QAAQ;EACzB,EAAE,aAAa,EAAE,QAAQ;EACzB,EAAE,aAAa,EAAE,QAAQ;EACzB,EAAE,aAAa,EAAE,QAAQ;EACzB,EAAE,UAAU,EAAE,QAAQ;EACtB,EAAE,QAAQ,EAAE,QAAQ;EACpB,EAAE,WAAW,EAAE,QAAQ;EACvB,EAAE,OAAO,EAAE,QAAQ;EACnB,EAAE,OAAO,EAAE,QAAQ;EACnB,EAAE,UAAU,EAAE,QAAQ;EACtB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,WAAW,EAAE,QAAQ;EACvB,EAAE,WAAW,EAAE,QAAQ;EACvB,EAAE,OAAO,EAAE,QAAQ;EACnB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,UAAU,EAAE,QAAQ;EACtB,EAAE,IAAI,EAAE,QAAQ;EAChB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,IAAI,EAAE,QAAQ;EAChB,EAAE,KAAK,EAAE,QAAQ;EACjB,EAAE,WAAW,EAAE,QAAQ;EACvB,EAAE,IAAI,EAAE,QAAQ;EAChB,EAAE,QAAQ,EAAE,QAAQ;EACpB,EAAE,OAAO,EAAE,QAAQ;EACnB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,MAAM,EAAE,QAAQ;EAClB,EAAE,KAAK,EAAE,QAAQ;EACjB,EAAE,KAAK,EAAE,QAAQ;EACjB,EAAE,QAAQ,EAAE,QAAQ;EACpB,EAAE,aAAa,EAAE,QAAQ;EACzB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,YAAY,EAAE,QAAQ;EACxB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,UAAU,EAAE,QAAQ;EACtB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,oBAAoB,EAAE,QAAQ;EAChC,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,UAAU,EAAE,QAAQ;EACtB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,WAAW,EAAE,QAAQ;EACvB,EAAE,aAAa,EAAE,QAAQ;EACzB,EAAE,YAAY,EAAE,QAAQ;EACxB,EAAE,cAAc,EAAE,QAAQ;EAC1B,EAAE,cAAc,EAAE,QAAQ;EAC1B,EAAE,cAAc,EAAE,QAAQ;EAC1B,EAAE,WAAW,EAAE,QAAQ;EACvB,EAAE,IAAI,EAAE,QAAQ;EAChB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,KAAK,EAAE,QAAQ;EACjB,EAAE,OAAO,EAAE,QAAQ;EACnB,EAAE,MAAM,EAAE,QAAQ;EAClB,EAAE,gBAAgB,EAAE,QAAQ;EAC5B,EAAE,UAAU,EAAE,QAAQ;EACtB,EAAE,YAAY,EAAE,QAAQ;EACxB,EAAE,YAAY,EAAE,QAAQ;EACxB,EAAE,cAAc,EAAE,QAAQ;EAC1B,EAAE,eAAe,EAAE,QAAQ;EAC3B,EAAE,iBAAiB,EAAE,QAAQ;EAC7B,EAAE,eAAe,EAAE,QAAQ;EAC3B,EAAE,eAAe,EAAE,QAAQ;EAC3B,EAAE,YAAY,EAAE,QAAQ;EACxB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,QAAQ,EAAE,QAAQ;EACpB,EAAE,WAAW,EAAE,QAAQ;EACvB,EAAE,IAAI,EAAE,QAAQ;EAChB,EAAE,OAAO,EAAE,QAAQ;EACnB,EAAE,KAAK,EAAE,QAAQ;EACjB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,MAAM,EAAE,QAAQ;EAClB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,MAAM,EAAE,QAAQ;EAClB,EAAE,aAAa,EAAE,QAAQ;EACzB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,aAAa,EAAE,QAAQ;EACzB,EAAE,aAAa,EAAE,QAAQ;EACzB,EAAE,UAAU,EAAE,QAAQ;EACtB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,IAAI,EAAE,QAAQ;EAChB,EAAE,IAAI,EAAE,QAAQ;EAChB,EAAE,IAAI,EAAE,QAAQ;EAChB,EAAE,UAAU,EAAE,QAAQ;EACtB,EAAE,MAAM,EAAE,QAAQ;EAClB,EAAE,aAAa,EAAE,QAAQ;EACzB,EAAE,GAAG,EAAE,QAAQ;EACf,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,WAAW,EAAE,QAAQ;EACvB,EAAE,MAAM,EAAE,QAAQ;EAClB,EAAE,UAAU,EAAE,QAAQ;EACtB,EAAE,QAAQ,EAAE,QAAQ;EACpB,EAAE,QAAQ,EAAE,QAAQ;EACpB,EAAE,MAAM,EAAE,QAAQ;EAClB,EAAE,MAAM,EAAE,QAAQ;EAClB,EAAE,OAAO,EAAE,QAAQ;EACnB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,IAAI,EAAE,QAAQ;EAChB,EAAE,WAAW,EAAE,QAAQ;EACvB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,GAAG,EAAE,QAAQ;EACf,EAAE,IAAI,EAAE,QAAQ;EAChB,EAAE,OAAO,EAAE,QAAQ;EACnB,EAAE,MAAM,EAAE,QAAQ;EAClB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,MAAM,EAAE,QAAQ;EAClB,EAAE,KAAK,EAAE,QAAQ;EACjB,EAAE,KAAK,EAAE,QAAQ;EACjB,EAAE,UAAU,EAAE,QAAQ;EACtB,EAAE,MAAM,EAAE,QAAQ;EAClB,EAAE,WAAW,EAAE;EACf,CAAC;;EAED,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE;EACrB,EAAE,IAAI,CAAC,QAAQ,EAAE;EACjB,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,QAAQ,CAAC;EAC9D,GAAG;EACH,EAAE,WAAW,GAAG;EAChB,IAAI,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE;EACnC,GAAG;EACH,EAAE,GAAG,EAAE,eAAe;EACtB,EAAE,SAAS,EAAE,eAAe;EAC5B,EAAE,UAAU,EAAE,gBAAgB;EAC9B,EAAE,SAAS,EAAE,eAAe;EAC5B,EAAE,SAAS,EAAE,eAAe;EAC5B,EAAE,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF,SAAS,eAAe,GAAG;EAC3B,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC,SAAS,EAAE;EAC/B;;EAEA,SAAS,gBAAgB,GAAG;EAC5B,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC,UAAU,EAAE;EAChC;;EAEA,SAAS,eAAe,GAAG;EAC3B,EAAE,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE;EACrC;;EAEA,SAAS,eAAe,GAAG;EAC3B,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC,SAAS,EAAE;EAC/B;;EAEe,SAAS,KAAK,CAAC,MAAM,EAAE;EACtC,EAAE,IAAI,CAAC,EAAE,CAAC;EACV,EAAE,MAAM,GAAG,CAAC,MAAM,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC,WAAW,EAAE;EAC7C,EAAE,OAAO,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;EAC/F,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;EACzH,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,EAAE,CAAC,IAAI,EAAE,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,IAAI,IAAI,IAAI,CAAC;EACxF,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI,CAAC;EAC/J,QAAQ,IAAI;EACZ,QAAQ,CAAC,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACtE,QAAQ,CAAC,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;EAC1G,QAAQ,CAAC,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACvE,QAAQ,CAAC,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3G,QAAQ,CAAC,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;EAC/E,QAAQ,CAAC,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACnF,QAAQ,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;EAC1D,QAAQ,MAAM,KAAK,aAAa,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;EAC3D,QAAQ,IAAI;EACZ;;EAEA,SAAS,IAAI,CAAC,CAAC,EAAE;EACjB,EAAE,OAAO,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;EAC5D;;EAEA,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;EAC1B,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG;EAC7B,EAAE,OAAO,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC5B;;EAEO,SAAS,UAAU,CAAC,CAAC,EAAE;EAC9B,EAAE,IAAI,EAAE,CAAC,YAAY,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;EACzC,EAAE,IAAI,CAAC,CAAC,EAAE,OAAO,IAAI,GAAG;EACxB,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE;EACb,EAAE,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC;EAC1C;;EAEO,SAASC,KAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE;EACtC,EAAE,OAAO,SAAS,CAAC,MAAM,KAAK,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,IAAI,IAAI,GAAG,CAAC,GAAG,OAAO,CAAC;EACjG;;EAEO,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE;EACtC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;EACb,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;EACb,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;EACb,EAAE,IAAI,CAAC,OAAO,GAAG,CAAC,OAAO;EACzB;;EAEA,MAAM,CAAC,GAAG,EAAEA,KAAG,EAAE,MAAM,CAAC,KAAK,EAAE;EAC/B,EAAE,QAAQ,CAAC,CAAC,EAAE;EACd,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;EACpD,IAAI,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC;EACpE,GAAG;EACH,EAAE,MAAM,CAAC,CAAC,EAAE;EACZ,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;EAChD,IAAI,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC;EACpE,GAAG;EACH,EAAE,GAAG,GAAG;EACR,IAAI,OAAO,IAAI;EACf,GAAG;EACH,EAAE,KAAK,GAAG;EACV,IAAI,OAAO,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EACxF,GAAG;EACH,EAAE,WAAW,GAAG;EAChB,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK;EAC5C,YAAY,IAAI,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK;EAC5C,YAAY,IAAI,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK;EAC5C,YAAY,CAAC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC;EACnD,GAAG;EACH,EAAE,GAAG,EAAE,aAAa;EACpB,EAAE,SAAS,EAAE,aAAa;EAC1B,EAAE,UAAU,EAAE,cAAc;EAC5B,EAAE,SAAS,EAAE,aAAa;EAC1B,EAAE,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC;;EAEH,SAAS,aAAa,GAAG;EACzB,EAAE,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD;;EAEA,SAAS,cAAc,GAAG;EAC1B,EAAE,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC;EAC5G;;EAEA,SAAS,aAAa,GAAG;EACzB,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;EAChC,EAAE,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3H;;EAEA,SAAS,MAAM,CAAC,OAAO,EAAE;EACzB,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;EAC/D;;EAEA,SAAS,MAAM,CAAC,KAAK,EAAE;EACvB,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;EAC3D;;EAEA,SAAS,GAAG,CAAC,KAAK,EAAE;EACpB,EAAE,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;EACvB,EAAE,OAAO,CAAC,KAAK,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,IAAI,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;EACrD;;EAEA,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;EAC1B,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG;EAC7B,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG;EACxC,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;EAC1B,EAAE,OAAO,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC5B;;EAEO,SAAS,UAAU,CAAC,CAAC,EAAE;EAC9B,EAAE,IAAI,CAAC,YAAY,GAAG,EAAE,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC;EAChE,EAAE,IAAI,EAAE,CAAC,YAAY,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;EACzC,EAAE,IAAI,CAAC,CAAC,EAAE,OAAO,IAAI,GAAG;EACxB,EAAE,IAAI,CAAC,YAAY,GAAG,EAAE,OAAO,CAAC;EAChC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE;EACb,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACnB,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACnB,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACnB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC7B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC7B,MAAM,CAAC,GAAG,GAAG;EACb,MAAM,CAAC,GAAG,GAAG,GAAG,GAAG;EACnB,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC;EACzB,EAAE,IAAI,CAAC,EAAE;EACT,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;EAChD,SAAS,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;EAC3C,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;EAC5B,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;EAC5C,IAAI,CAAC,IAAI,EAAE;EACX,GAAG,MAAM;EACT,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;EAC9B;EACA,EAAE,OAAO,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC;EACpC;;EAEO,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE;EACtC,EAAE,OAAO,SAAS,CAAC,MAAM,KAAK,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,IAAI,IAAI,GAAG,CAAC,GAAG,OAAO,CAAC;EACjG;;EAEA,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE;EAC/B,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;EACb,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;EACb,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;EACb,EAAE,IAAI,CAAC,OAAO,GAAG,CAAC,OAAO;EACzB;;EAEA,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,KAAK,EAAE;EAC/B,EAAE,QAAQ,CAAC,CAAC,EAAE;EACd,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;EACpD,IAAI,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC;EAC5D,GAAG;EACH,EAAE,MAAM,CAAC,CAAC,EAAE;EACZ,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;EAChD,IAAI,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC;EAC5D,GAAG;EACH,EAAE,GAAG,GAAG;EACR,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG;EAC7C,QAAQ,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;EAClD,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;EAClB,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;EAC1C,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE;EACvB,IAAI,OAAO,IAAI,GAAG;EAClB,MAAM,OAAO,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;EACnD,MAAM,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EACxB,MAAM,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;EAClD,MAAM,IAAI,CAAC;EACX,KAAK;EACL,GAAG;EACH,EAAE,KAAK,GAAG;EACV,IAAI,OAAO,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EACxF,GAAG;EACH,EAAE,WAAW,GAAG;EAChB,IAAI,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;EACvD,YAAY,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC;EACtC,YAAY,CAAC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC;EACnD,GAAG;EACH,EAAE,SAAS,GAAG;EACd,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;EAClC,IAAI,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3I;EACA,CAAC,CAAC,CAAC;;EAEH,SAAS,MAAM,CAAC,KAAK,EAAE;EACvB,EAAE,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,IAAI,GAAG;EAC5B,EAAE,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,KAAK;EACxC;;EAEA,SAAS,MAAM,CAAC,KAAK,EAAE;EACvB,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;EAC7C;;EAEA;EACA,SAAS,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE;EAC5B,EAAE,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG;EACxC,QAAQ,CAAC,GAAG,GAAG,GAAG;EAClB,QAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG;EAC/C,QAAQ,EAAE,IAAI,GAAG;EACjB;;AC3YA,iBAAe,CAAC,IAAI,MAAM,CAAC;;ECE3B,SAASC,QAAM,CAAC,CAAC,EAAE,CAAC,EAAE;EACtB,EAAE,OAAO,SAAS,CAAC,EAAE;EACrB,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC;EACpB,GAAG;EACH;;EAEA,SAAS,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;EAC9B,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,EAAE;EAC5E,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;EACjC,GAAG;EACH;;EAOO,SAAS,KAAK,CAAC,CAAC,EAAE;EACzB,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,GAAG,OAAO,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;EACnD,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACpE,GAAG;EACH;;EAEe,SAAS,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE;EACtC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;EACf,EAAE,OAAO,CAAC,GAAGA,QAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACtD;;ACvBA,YAAe,CAAC,SAAS,QAAQ,CAAC,CAAC,EAAE;EACrC,EAAE,IAAI,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;;EAEtB,EAAE,SAAS,GAAG,CAAC,KAAK,EAAE,GAAG,EAAE;EAC3B,IAAI,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,GAAGC,KAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,GAAGA,KAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;EACvE,QAAQ,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACjC,QAAQ,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACjC,QAAQ,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,CAAC;EACrD,IAAI,OAAO,SAAS,CAAC,EAAE;EACvB,MAAM,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACpB,MAAM,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACpB,MAAM,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACpB,MAAM,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC;EAChC,MAAM,OAAO,KAAK,GAAG,EAAE;EACvB,KAAK;EACL;;EAEA,EAAE,GAAG,CAAC,KAAK,GAAG,QAAQ;;EAEtB,EAAE,OAAO,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC;;ECzBU,oBAAQ,CAAC,CAAC,EAAE,CAAC,EAAE;EAC9B,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE;EAChB,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC;EAC9C,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE;EACnB,MAAM,CAAC;EACP,EAAE,OAAO,SAAS,CAAC,EAAE;EACrB,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;EAC5D,IAAI,OAAO,CAAC;EACZ,GAAG;EACH;;EAEO,SAAS,aAAa,CAAC,CAAC,EAAE;EACjC,EAAE,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,YAAY,QAAQ,CAAC;EAC1D;;ECNO,SAAS,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE;EACnC,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC;EAC3B,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC;EACzC,MAAM,CAAC,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC;EACvB,MAAM,CAAC,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC;EACvB,MAAM,CAAC;;EAEP,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAGC,WAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACnD,EAAE,OAAO,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;EAEjC,EAAE,OAAO,SAAS,CAAC,EAAE;EACrB,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3C,IAAI,OAAO,CAAC;EACZ,GAAG;EACH;;ECrBe,aAAQ,CAAC,CAAC,EAAE,CAAC,EAAE;EAC9B,EAAE,IAAI,CAAC,GAAG,IAAI,IAAI;EAClB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE;EACrC,IAAI,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;EAC5C,GAAG;EACH;;ECLe,0BAAQ,CAAC,CAAC,EAAE,CAAC,EAAE;EAC9B,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE;EACrC,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;EAC9B,GAAG;EACH;;ECFe,eAAQ,CAAC,CAAC,EAAE,CAAC,EAAE;EAC9B,EAAE,IAAI,CAAC,GAAG,EAAE;EACZ,MAAM,CAAC,GAAG,EAAE;EACZ,MAAM,CAAC;;EAEP,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC,GAAG,EAAE;EACjD,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC,GAAG,EAAE;;EAEjD,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE;EACf,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;EAChB,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGA,WAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B,KAAK,MAAM;EACX,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACjB;EACA;;EAEA,EAAE,OAAO,SAAS,CAAC,EAAE;EACrB,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/B,IAAI,OAAO,CAAC;EACZ,GAAG;EACH;;ECpBA,IAAI,GAAG,GAAG,6CAA6C;EACvD,IAAI,GAAG,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC;;EAErC,SAAS,IAAI,CAAC,CAAC,EAAE;EACjB,EAAE,OAAO,WAAW;EACpB,IAAI,OAAO,CAAC;EACZ,GAAG;EACH;;EAEA,SAAS,GAAG,CAAC,CAAC,EAAE;EAChB,EAAE,OAAO,SAAS,CAAC,EAAE;EACrB,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;EACpB,GAAG;EACH;;EAEe,eAAQ,CAAC,CAAC,EAAE,CAAC,EAAE;EAC9B,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,GAAG,CAAC;EAC5C,MAAM,EAAE;EACR,MAAM,EAAE;EACR,MAAM,EAAE;EACR,MAAM,CAAC,GAAG,EAAE;EACZ,MAAM,CAAC,GAAG,EAAE;EACZ,MAAM,CAAC,GAAG,EAAE,CAAC;;EAEb;EACA,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE;;EAExB;EACA,EAAE,OAAO,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;EAC1B,UAAU,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;EAC7B,IAAI,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,KAAK,IAAI,EAAE,EAAE;EAC9B,MAAM,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;EAC1B,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;EAC3B,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE;EACtB;EACA,IAAI,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;EACvC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;EAC3B,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE;EACtB,KAAK,MAAM;EACX,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI;EACnB,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAExC,iBAAM,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;EACvC;EACA,IAAI,EAAE,GAAG,GAAG,CAAC,SAAS;EACtB;;EAEA;EACA,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE;EACrB,IAAI,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;EACpB,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;EACzB,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE;EACpB;;EAEA;EACA;EACA,EAAE,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;EAC5B,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,QAAQ,IAAI,CAAC,CAAC,CAAC;EACf,SAAS,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE;EACnC,UAAU,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjE,UAAU,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;EAC3B,SAAS,CAAC;EACV;;ECrDe,oBAAQ,CAAC,CAAC,EAAE,CAAC,EAAE;EAC9B,EAAE,IAAI,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC;EACrB,EAAE,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,SAAS,GAAG,QAAQ,CAAC,CAAC;EAClD,QAAQ,CAAC,CAAC,KAAK,QAAQ,GAAGA;EAC1B,QAAQ,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,MAAM;EAChE,QAAQ,CAAC,YAAY,KAAK,GAAG;EAC7B,QAAQ,CAAC,YAAY,IAAI,GAAG;EAC5B,QAAQ,aAAa,CAAC,CAAC,CAAC,GAAG;EAC3B,QAAQ,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG;EAC3B,QAAQ,OAAO,CAAC,CAAC,OAAO,KAAK,UAAU,IAAI,OAAO,CAAC,CAAC,QAAQ,KAAK,UAAU,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG;EAC1F,QAAQA,iBAAM,EAAE,CAAC,EAAE,CAAC,CAAC;EACrB;;ECrBe,yBAAQ,CAAC,CAAC,EAAE,CAAC,EAAE;EAC9B,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE;EACrC,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAC1C,GAAG;EACH;;ECJe,SAAS,SAAS,CAAC,CAAC,EAAE;EACrC,EAAE,OAAO,WAAW;EACpB,IAAI,OAAO,CAAC;EACZ,GAAG;EACH;;ECJe,SAAS,MAAM,CAAC,CAAC,EAAE;EAClC,EAAE,OAAO,CAAC,CAAC;EACX;;ECGA,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;EAEV,SAASiC,UAAQ,CAAC,CAAC,EAAE;EAC5B,EAAE,OAAO,CAAC;EACV;;EAEA,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;EACzB,EAAE,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EACvB,QAAQ,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;EACzC,QAAQQ,SAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;EACtC;;EAEA,SAAS,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE;EACvB,EAAE,IAAI,CAAC;EACP,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC;EAChC,EAAE,OAAO,SAAS,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;EAC5D;;EAEA;EACA;EACA,SAAS,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE;EAC3C,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;EAClE,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,GAAG,WAAW,CAAC,EAAE,EAAE,EAAE,CAAC;EAC/D,OAAO,EAAE,GAAG,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,GAAG,WAAW,CAAC,EAAE,EAAE,EAAE,CAAC;EACvD,EAAE,OAAO,SAAS,CAAC,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC1C;;EAEA,SAAS,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE;EAC7C,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC;EACnD,MAAM,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC;EACtB,MAAM,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC;EACtB,MAAM,CAAC,GAAG,EAAE;;EAEZ;EACA,EAAE,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE;EAC7B,IAAI,MAAM,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE;EACrC,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE;EACnC;;EAEA,EAAE,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE;EAClB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAC9C,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAC9C;;EAEA,EAAE,OAAO,SAAS,CAAC,EAAE;EACrB,IAAI,IAAI,CAAC,GAAGC,WAAM,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC;EACvC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB,GAAG;EACH;;EAEO,SAAS,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE;EACrC,EAAE,OAAO;EACT,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE;EAC7B,OAAO,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE;EAC3B,OAAO,WAAW,CAAC,MAAM,CAAC,WAAW,EAAE;EACvC,OAAO,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE;EAC3B,OAAO,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;EAChC;;EAEO,SAAS,WAAW,GAAG;EAC9B,EAAE,IAAI,MAAM,GAAG,IAAI;EACnB,MAAM,KAAK,GAAG,IAAI;EAClB,MAAMC,aAAW,GAAGC,WAAgB;EACpC,MAAM,SAAS;EACf,MAAM,WAAW;EACjB,MAAM,OAAO;EACb,MAAM,KAAK,GAAGX,UAAQ;EACtB,MAAM,SAAS;EACf,MAAM,MAAM;EACZ,MAAM,KAAK;;EAEX,EAAE,SAAS,OAAO,GAAG;EACrB,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC;EACjD,IAAI,IAAI,KAAK,KAAKA,UAAQ,EAAE,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EACrE,IAAI,SAAS,GAAG,CAAC,GAAG,CAAC,GAAG,OAAO,GAAG,KAAK;EACvC,IAAI,MAAM,GAAG,KAAK,GAAG,IAAI;EACzB,IAAI,OAAO,KAAK;EAChB;;EAEA,EAAE,SAAS,KAAK,CAAC,CAAC,EAAE;EACpB,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,CAAC,MAAM,KAAK,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,KAAK,EAAEU,aAAW,CAAC,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;EAClJ;;EAEA,EAAE,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,EAAE;EAC7B,IAAI,OAAO,KAAK,CAAC,WAAW,CAAC,CAAC,KAAK,KAAK,KAAK,GAAG,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjH,GAAG;;EAEH,EAAE,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,EAAE;EAC7B,IAAI,OAAO,SAAS,CAAC,MAAM,IAAI,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,OAAO,EAAE,IAAI,MAAM,CAAC,KAAK,EAAE;EAC1F,GAAG;;EAEH,EAAE,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC,EAAE;EAC5B,IAAI,OAAO,SAAS,CAAC,MAAM,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,KAAK,CAAC,KAAK,EAAE;EAChF,GAAG;;EAEH,EAAE,KAAK,CAAC,UAAU,GAAG,SAAS,CAAC,EAAE;EACjC,IAAI,OAAO,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAEA,aAAW,GAAG,gBAAgB,EAAE,OAAO,EAAE;EAC3E,GAAG;;EAEH,EAAE,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC,EAAE;EAC5B,IAAI,OAAO,SAAS,CAAC,MAAM,IAAI,KAAK,GAAG,CAAC,GAAG,IAAI,GAAGV,UAAQ,EAAE,OAAO,EAAE,IAAI,KAAK,KAAKA,UAAQ;EAC3F,GAAG;;EAEH,EAAE,KAAK,CAAC,WAAW,GAAG,SAAS,CAAC,EAAE;EAClC,IAAI,OAAO,SAAS,CAAC,MAAM,IAAIU,aAAW,GAAG,CAAC,EAAE,OAAO,EAAE,IAAIA,aAAW;EACxE,GAAG;;EAEH,EAAE,KAAK,CAAC,OAAO,GAAG,SAAS,CAAC,EAAE;EAC9B,IAAI,OAAO,SAAS,CAAC,MAAM,IAAI,OAAO,GAAG,CAAC,EAAE,KAAK,IAAI,OAAO;EAC5D,GAAG;;EAEH,EAAE,OAAO,SAAS,CAAC,EAAE,CAAC,EAAE;EACxB,IAAI,SAAS,GAAG,CAAC,EAAE,WAAW,GAAG,CAAC;EAClC,IAAI,OAAO,OAAO,EAAE;EACpB,GAAG;EACH;;EAEe,SAAS,UAAU,GAAG;EACrC,EAAE,OAAO,WAAW,EAAE,CAACV,UAAQ,EAAEA,UAAQ,CAAC;EAC1C;;EC5He,sBAAQ,CAAC,CAAC,EAAE;EAC3B,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;EACxC,QAAQ,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE;EAC/C,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC;EACtB;;EAEA;EACA;EACA;EACO,SAAS,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE;EACzC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC;EAC/F,EAAE,IAAI,CAAC,EAAE,WAAW,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;EAEpC;EACA;EACA,EAAE,OAAO;EACT,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,WAAW;EAChF,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;EAClB,GAAG;EACH;;ECjBe,iBAAQ,CAAC,CAAC,EAAE;EAC3B,EAAE,OAAO,CAAC,GAAG,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;EAC5D;;ECJe,oBAAQ,CAAC,QAAQ,EAAE,SAAS,EAAE;EAC7C,EAAE,OAAO,SAAS,KAAK,EAAE,KAAK,EAAE;EAChC,IAAI,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM;EACxB,QAAQ,CAAC,GAAG,EAAE;EACd,QAAQ,CAAC,GAAG,CAAC;EACb,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;EACvB,QAAQ,MAAM,GAAG,CAAC;;EAElB,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;EAC3B,MAAM,IAAI,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC;EACjE,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;EAC5C,MAAM,IAAI,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,EAAE;EACrC,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC;EACjD;;EAEA,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC;EACtC,GAAG;EACH;;ECjBe,uBAAQ,CAAC,QAAQ,EAAE;EAClC,EAAE,OAAO,SAAS,KAAK,EAAE;EACzB,IAAI,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE;EAC/C,MAAM,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;EACzB,KAAK,CAAC;EACN,GAAG;EACH;;ECNA;EACA,IAAI,EAAE,GAAG,0EAA0E;;EAEpE,SAAS,eAAe,CAAC,SAAS,EAAE;EACnD,EAAE,IAAI,EAAE,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,IAAI,KAAK,CAAC,kBAAkB,GAAG,SAAS,CAAC;EACpF,EAAE,IAAI,KAAK;EACX,EAAE,OAAO,IAAI,eAAe,CAAC;EAC7B,IAAI,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;EAClB,IAAI,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;EACnB,IAAI,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;EAClB,IAAI,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;EACpB,IAAI,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;EAClB,IAAI,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;EACnB,IAAI,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;EACnB,IAAI,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;EAC5C,IAAI,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;EAClB,IAAI,IAAI,EAAE,KAAK,CAAC,EAAE;EAClB,GAAG,CAAC;EACJ;;EAEA,eAAe,CAAC,SAAS,GAAG,eAAe,CAAC,SAAS,CAAC;;EAE/C,SAAS,eAAe,CAAC,SAAS,EAAE;EAC3C,EAAE,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,KAAK,SAAS,GAAG,GAAG,GAAG,SAAS,CAAC,IAAI,GAAG,EAAE;EACtE,EAAE,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,KAAK,SAAS,GAAG,GAAG,GAAG,SAAS,CAAC,KAAK,GAAG,EAAE;EACzE,EAAE,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,KAAK,SAAS,GAAG,GAAG,GAAG,SAAS,CAAC,IAAI,GAAG,EAAE;EACtE,EAAE,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,KAAK,SAAS,GAAG,EAAE,GAAG,SAAS,CAAC,MAAM,GAAG,EAAE;EAC3E,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,SAAS,CAAC,IAAI;EAC9B,EAAE,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,KAAK,SAAS,GAAG,SAAS,GAAG,CAAC,SAAS,CAAC,KAAK;EAC3E,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,SAAS,CAAC,KAAK;EAChC,EAAE,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,KAAK,SAAS,GAAG,SAAS,GAAG,CAAC,SAAS,CAAC,SAAS;EACvF,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,SAAS,CAAC,IAAI;EAC9B,EAAE,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,KAAK,SAAS,GAAG,EAAE,GAAG,SAAS,CAAC,IAAI,GAAG,EAAE;EACrE;;EAEA,eAAe,CAAC,SAAS,CAAC,QAAQ,GAAG,WAAW;EAChD,EAAE,OAAO,IAAI,CAAC;EACd,QAAQ,IAAI,CAAC;EACb,QAAQ,IAAI,CAAC;EACb,QAAQ,IAAI,CAAC;EACb,SAAS,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,EAAE;EAC7B,SAAS,IAAI,CAAC,KAAK,KAAK,SAAS,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;EACpE,SAAS,IAAI,CAAC,KAAK,GAAG,GAAG,GAAG,EAAE;EAC9B,SAAS,IAAI,CAAC,SAAS,KAAK,SAAS,GAAG,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;EAClF,SAAS,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,EAAE;EAC7B,QAAQ,IAAI,CAAC,IAAI;EACjB,CAAC;;EC9CD;EACe,mBAAQ,CAAC,CAAC,EAAE;EAC3B,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;EAC9D,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChB,MAAM,KAAK,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;EAC7B,MAAM,KAAK,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EAC9C,MAAM,SAAS,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;EAC1D;EACA;EACA,EAAE,OAAO,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;EACtD;;ECRO,IAAI,cAAc;;EAEV,yBAAQ,CAAC,CAAC,EAAE,CAAC,EAAE;EAC9B,EAAE,IAAI,CAAC,GAAG,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC;EAClC,EAAE,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,EAAE;EACvB,EAAE,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;EACxB,MAAM,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;EACrB,MAAM,CAAC,GAAG,QAAQ,IAAI,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EACnG,MAAM,CAAC,GAAG,WAAW,CAAC,MAAM;EAC5B,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG;EACnB,QAAQ,CAAC,GAAG,CAAC,GAAG,WAAW,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG;EAC3D,QAAQ,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;EACnE,QAAQ,IAAI,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,kBAAkB,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7F;;ECbe,sBAAQ,CAAC,CAAC,EAAE,CAAC,EAAE;EAC9B,EAAE,IAAI,CAAC,GAAG,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC;EAClC,EAAE,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,EAAE;EACvB,EAAE,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;EACxB,MAAM,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;EACrB,EAAE,OAAO,QAAQ,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG;EAChE,QAAQ,WAAW,CAAC,MAAM,GAAG,QAAQ,GAAG,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,WAAW,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC;EACrH,QAAQ,WAAW,GAAG,IAAI,KAAK,CAAC,QAAQ,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;EAC5E;;ACNA,oBAAe;EACf,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;EACrC,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;EACvC,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE;EACpB,EAAE,GAAG,EAAE,aAAa;EACpB,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC;EACnC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;EAC7B,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;EACjC,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;EACvC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,aAAa,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;EAC1C,EAAE,GAAG,EAAE,aAAa;EACpB,EAAE,GAAG,EAAE,gBAAgB;EACvB,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE;EACtD,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE;EACvC,CAAC;;EClBc,iBAAQ,CAAC,CAAC,EAAE;EAC3B,EAAE,OAAO,CAAC;EACV;;ECOA,IAAI,GAAG,GAAG,KAAK,CAAC,SAAS,CAAC,GAAG;EAC7B,IAAI,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;;EAEpE,qBAAQ,CAAC,MAAM,EAAE;EAChC,EAAE,IAAI,KAAK,GAAG,MAAM,CAAC,QAAQ,KAAK,SAAS,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,GAAG,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC,SAAS,GAAG,EAAE,CAAC;EAChK,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,KAAK,SAAS,GAAG,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE;EACnF,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,KAAK,SAAS,GAAG,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE;EACnF,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,KAAK,SAAS,GAAG,GAAG,GAAG,MAAM,CAAC,OAAO,GAAG,EAAE;EACxE,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,KAAK,SAAS,GAAG,QAAQ,GAAG,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;EAC7G,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,KAAK,SAAS,GAAG,GAAG,GAAG,MAAM,CAAC,OAAO,GAAG,EAAE;EACxE,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,KAAK,SAAS,GAAG,GAAG,GAAG,MAAM,CAAC,KAAK,GAAG,EAAE;EAClE,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,KAAK,SAAS,GAAG,KAAK,GAAG,MAAM,CAAC,GAAG,GAAG,EAAE;;EAE9D,EAAE,SAAS,SAAS,CAAC,SAAS,EAAE;EAChC,IAAI,SAAS,GAAG,eAAe,CAAC,SAAS,CAAC;;EAE1C,IAAI,IAAI,IAAI,GAAG,SAAS,CAAC,IAAI;EAC7B,QAAQ,KAAK,GAAG,SAAS,CAAC,KAAK;EAC/B,QAAQ,IAAI,GAAG,SAAS,CAAC,IAAI;EAC7B,QAAQ,MAAM,GAAG,SAAS,CAAC,MAAM;EACjC,QAAQ,IAAI,GAAG,SAAS,CAAC,IAAI;EAC7B,QAAQ,KAAK,GAAG,SAAS,CAAC,KAAK;EAC/B,QAAQ,KAAK,GAAG,SAAS,CAAC,KAAK;EAC/B,QAAQ,SAAS,GAAG,SAAS,CAAC,SAAS;EACvC,QAAQ,IAAI,GAAG,SAAS,CAAC,IAAI;EAC7B,QAAQ,IAAI,GAAG,SAAS,CAAC,IAAI;;EAE7B;EACA,IAAI,IAAI,IAAI,KAAK,GAAG,EAAE,KAAK,GAAG,IAAI,EAAE,IAAI,GAAG,GAAG;;EAE9C;EACA,SAAS,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,SAAS,KAAK,SAAS,KAAK,SAAS,GAAG,EAAE,CAAC,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,GAAG;;EAErG;EACA,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,GAAG,IAAI,KAAK,KAAK,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,GAAG,EAAE,KAAK,GAAG,GAAG;;EAErF;EACA;EACA,IAAI,IAAI,MAAM,GAAG,MAAM,KAAK,GAAG,GAAG,cAAc,GAAG,MAAM,KAAK,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE;EACxH,QAAQ,MAAM,GAAG,MAAM,KAAK,GAAG,GAAG,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,OAAO,GAAG,EAAE;;EAEnF;EACA;EACA;EACA,IAAI,IAAI,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC;EACtC,QAAQ,WAAW,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;;EAE7C;EACA;EACA;EACA;EACA,IAAI,SAAS,GAAG,SAAS,KAAK,SAAS,GAAG;EAC1C,UAAU,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,SAAS,CAAC;EACnE,UAAU,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;;EAE9C,IAAI,SAAS,MAAM,CAAC,KAAK,EAAE;EAC3B,MAAM,IAAI,WAAW,GAAG,MAAM;EAC9B,UAAU,WAAW,GAAG,MAAM;EAC9B,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC;;EAEjB,MAAM,IAAI,IAAI,KAAK,GAAG,EAAE;EACxB,QAAQ,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,WAAW;EACrD,QAAQ,KAAK,GAAG,EAAE;EAClB,OAAO,MAAM;EACb,QAAQ,KAAK,GAAG,CAAC,KAAK;;EAEtB;EACA,QAAQ,IAAI,aAAa,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC;;EAEtD;EACA,QAAQ,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,SAAS,CAAC;;EAE3E;EACA,QAAQ,IAAI,IAAI,EAAE,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC;;EAE3C;EACA,QAAQ,IAAI,aAAa,IAAI,CAAC,KAAK,KAAK,CAAC,IAAI,IAAI,KAAK,GAAG,EAAE,aAAa,GAAG,KAAK;;EAEhF;EACA,QAAQ,WAAW,GAAG,CAAC,aAAa,IAAI,IAAI,KAAK,GAAG,GAAG,IAAI,GAAG,KAAK,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,GAAG,EAAE,GAAG,IAAI,IAAI,WAAW;EAC9H,QAAQ,WAAW,GAAG,CAAC,IAAI,KAAK,GAAG,GAAG,QAAQ,CAAC,CAAC,GAAG,cAAc,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,WAAW,IAAI,aAAa,IAAI,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;;EAEvI;EACA;EACA,QAAQ,IAAI,WAAW,EAAE;EACzB,UAAU,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM;EAClC,UAAU,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE;EAC1B,YAAY,IAAI,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE;EAC3D,cAAc,WAAW,GAAG,CAAC,CAAC,KAAK,EAAE,GAAG,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,WAAW;EACpG,cAAc,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACvC,cAAc;EACd;EACA;EACA;EACA;;EAEA;EACA,MAAM,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC;;EAExD;EACA,MAAM,IAAI,MAAM,GAAG,WAAW,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM;EACzE,UAAU,OAAO,GAAG,MAAM,GAAG,KAAK,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;;EAElF;EACA,MAAM,IAAI,KAAK,IAAI,IAAI,EAAE,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,KAAK,EAAE,OAAO,CAAC,MAAM,GAAG,KAAK,GAAG,WAAW,CAAC,MAAM,GAAG,QAAQ,CAAC,EAAE,OAAO,GAAG,EAAE;;EAE7H;EACA,MAAM,QAAQ,KAAK;EACnB,QAAQ,KAAK,GAAG,EAAE,KAAK,GAAG,WAAW,GAAG,KAAK,GAAG,WAAW,GAAG,OAAO,CAAC,CAAC;EACvE,QAAQ,KAAK,GAAG,EAAE,KAAK,GAAG,WAAW,GAAG,OAAO,GAAG,KAAK,GAAG,WAAW,CAAC,CAAC;EACvE,QAAQ,KAAK,GAAG,EAAE,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC,GAAG,WAAW,GAAG,KAAK,GAAG,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;EACtI,QAAQ,SAAS,KAAK,GAAG,OAAO,GAAG,WAAW,GAAG,KAAK,GAAG,WAAW,CAAC,CAAC;EACtE;;EAEA,MAAM,OAAO,QAAQ,CAAC,KAAK,CAAC;EAC5B;;EAEA,IAAI,MAAM,CAAC,QAAQ,GAAG,WAAW;EACjC,MAAM,OAAO,SAAS,GAAG,EAAE;EAC3B,KAAK;;EAEL,IAAI,OAAO,MAAM;EACjB;;EAEA,EAAE,SAAS,YAAY,CAAC,SAAS,EAAE,KAAK,EAAE;EAC1C,IAAI,IAAI,CAAC,GAAG,SAAS,EAAE,SAAS,GAAG,eAAe,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC,IAAI,GAAG,GAAG,EAAE,SAAS,EAAE;EAChG,QAAQ,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;EAC1E,QAAQ,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EAC5B,QAAQ,MAAM,GAAG,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACpC,IAAI,OAAO,SAAS,KAAK,EAAE;EAC3B,MAAM,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,MAAM;EAClC,KAAK;EACL;;EAEA,EAAE,OAAO;EACT,IAAI,MAAM,EAAE,SAAS;EACrB,IAAI,YAAY,EAAE;EAClB,GAAG;EACH;;ECjJA,IAAI,MAAM;EACH,IAAI,MAAM;EACV,IAAI,YAAY;;EAEvB,aAAa,CAAC;EACd,EAAE,SAAS,EAAE,GAAG;EAChB,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;EACf,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE;EACpB,CAAC,CAAC;;EAEa,SAAS,aAAa,CAAC,UAAU,EAAE;EAClD,EAAE,MAAM,GAAG,YAAY,CAAC,UAAU,CAAC;EACnC,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM;EACxB,EAAE,YAAY,GAAG,MAAM,CAAC,YAAY;EACpC,EAAE,OAAO,MAAM;EACf;;ECfe,uBAAQ,CAAC,IAAI,EAAE;EAC9B,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;EAC/C;;ECFe,wBAAQ,CAAC,IAAI,EAAE,KAAK,EAAE;EACrC,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;EAC/G;;ECFe,uBAAQ,CAAC,IAAI,EAAE,GAAG,EAAE;EACnC,EAAE,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI;EACnD,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;EACxD;;ECFe,SAAS,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE;EAClE,EAAE,IAAI,IAAI,GAAG,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;EACzC,MAAM,SAAS;EACf,EAAE,SAAS,GAAG,eAAe,CAAC,SAAS,IAAI,IAAI,GAAG,IAAI,GAAG,SAAS,CAAC;EACnE,EAAE,QAAQ,SAAS,CAAC,IAAI;EACxB,IAAI,KAAK,GAAG,EAAE;EACd,MAAM,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;EAC3D,MAAM,IAAI,SAAS,CAAC,SAAS,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,eAAe,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,SAAS,GAAG,SAAS;EAC1H,MAAM,OAAO,YAAY,CAAC,SAAS,EAAE,KAAK,CAAC;EAC3C;EACA,IAAI,KAAK,EAAE;EACX,IAAI,KAAK,GAAG;EACZ,IAAI,KAAK,GAAG;EACZ,IAAI,KAAK,GAAG;EACZ,IAAI,KAAK,GAAG,EAAE;EACd,MAAM,IAAI,SAAS,CAAC,SAAS,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,SAAS,GAAG,SAAS,IAAI,SAAS,CAAC,IAAI,KAAK,GAAG,CAAC;EACxL,MAAM;EACN;EACA,IAAI,KAAK,GAAG;EACZ,IAAI,KAAK,GAAG,EAAE;EACd,MAAM,IAAI,SAAS,CAAC,SAAS,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,SAAS,CAAC,SAAS,GAAG,SAAS,GAAG,CAAC,SAAS,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC;EACjJ,MAAM;EACN;EACA;EACA,EAAE,OAAO,MAAM,CAAC,SAAS,CAAC;EAC1B;;ECvBO,SAAS,SAAS,CAAC,KAAK,EAAE;EACjC,EAAE,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM;;EAE3B,EAAE,KAAK,CAAC,KAAK,GAAG,SAAS,KAAK,EAAE;EAChC,IAAI,IAAI,CAAC,GAAG,MAAM,EAAE;EACpB,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,KAAK,IAAI,IAAI,GAAG,EAAE,GAAG,KAAK,CAAC;EACnE,GAAG;;EAEH,EAAE,KAAK,CAAC,UAAU,GAAG,SAAS,KAAK,EAAE,SAAS,EAAE;EAChD,IAAI,IAAI,CAAC,GAAG,MAAM,EAAE;EACpB,IAAI,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,KAAK,IAAI,IAAI,GAAG,EAAE,GAAG,KAAK,EAAE,SAAS,CAAC;EACnF,GAAG;;EAEH,EAAE,KAAK,CAAC,IAAI,GAAG,SAAS,KAAK,EAAE;EAC/B,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE,KAAK,GAAG,EAAE;;EAEjC,IAAI,IAAI,CAAC,GAAG,MAAM,EAAE;EACpB,IAAI,IAAI,EAAE,GAAG,CAAC;EACd,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC;EACzB,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;EACrB,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC;EACpB,IAAI,IAAI,OAAO;EACf,IAAI,IAAI,IAAI;EACZ,IAAI,IAAI,OAAO,GAAG,EAAE;;EAEpB,IAAI,IAAI,IAAI,GAAG,KAAK,EAAE;EACtB,MAAM,IAAI,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI,EAAE,IAAI,GAAG,IAAI;EAC7C,MAAM,IAAI,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,IAAI;EACnC;EACA;EACA,IAAI,OAAO,OAAO,EAAE,GAAG,CAAC,EAAE;EAC1B,MAAM,IAAI,GAAG,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;EAC9C,MAAM,IAAI,IAAI,KAAK,OAAO,EAAE;EAC5B,QAAQ,CAAC,CAAC,EAAE,CAAC,GAAG;EAChB,QAAQ,CAAC,CAAC,EAAE,CAAC,GAAG;EAChB,QAAQ,OAAO,MAAM,CAAC,CAAC,CAAC;EACxB,OAAO,MAAM,IAAI,IAAI,GAAG,CAAC,EAAE;EAC3B,QAAQ,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI;EAC/C,QAAQ,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,IAAI;EAC5C,OAAO,MAAM,IAAI,IAAI,GAAG,CAAC,EAAE;EAC3B,QAAQ,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI;EAC9C,QAAQ,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,IAAI;EAC7C,OAAO,MAAM;EACb,QAAQ;EACR;EACA,MAAM,OAAO,GAAG,IAAI;EACpB;;EAEA,IAAI,OAAO,KAAK;EAChB,GAAG;;EAEH,EAAE,OAAO,KAAK;EACd;;EAEe,SAAS,MAAM,GAAG;EACjC,EAAE,IAAI,KAAK,GAAG,UAAU,EAAE;;EAE1B,EAAE,KAAK,CAAC,IAAI,GAAG,WAAW;EAC1B,IAAI,OAAO,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,CAAC;EAChC,GAAG;;EAEH,EAAE,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,SAAS,CAAC;;EAEnC,EAAE,OAAO,SAAS,CAAC,KAAK,CAAC;EACzB;;EC7DA,SAASY,qBAAqBA,CAACC,OAAO,EAE9B;EAAA,EAAA,IAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAJ,EAAE;MAAAG,eAAA,GAAAJ,IAAA,CADJK,UAAU;EAAVA,IAAAA,UAAU,GAAAD,eAAA,KAAGE,SAAAA,GAAAA,QAAQ,GAAAF,eAAA;EAErB,EAAA,IAAMG,OAAO,GAAGC,wBAAwB,CAACT,OAAO,EAAEM,UAAU,CAAC;EAE7D,EAAA,IAAMI,UAAU,GAAGvD,KAAO,CAACqD,OAAO,CAAC;EACnC,EAAA,IAAMG,WAAW,GAAGC,iBAAiB,CAACZ,OAAO,EAAEM,UAAU,CAAC;IAC1D,IAAMO,MAAM,GAAAC,EAAAA,CAAAA,MAAA,CAAAC,kBAAA,CAAOL,UAAU,CAAAK,EAAAA,kBAAA,CAAKJ,WAAW,CAAC,CAAA;EAE9C,EAAA,IAAMK,iBAAiB,GAAG;EAAEC,IAAAA,IAAI,EAAE,SAAS;EAAEC,IAAAA,WAAW,EAAElB;KAAS;EACnE,EAAA,IAAAmB,UAAA,GAA6CC,SAAS,CAACJ,iBAAiB,CAAC;MAAAK,WAAA,GAAAC,cAAA,CAAAH,UAAA,EAAA,CAAA,CAAA;MAAAI,YAAA,GAAAD,cAAA,CAAAD,WAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;EAAjEG,IAAAA,MAAM,GAAAD,YAAA,CAAA,CAAA,CAAA;EAAEE,IAAAA,MAAM,GAAAF,YAAA,CAAA,CAAA,CAAA;MAAAG,aAAA,GAAAJ,cAAA,CAAAD,WAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;EAAIM,IAAAA,MAAM,GAAAD,aAAA,CAAA,CAAA,CAAA;EAAEE,IAAAA,MAAM,GAAAF,aAAA,CAAA,CAAA,CAAA;EACxC,EAAA,IAAMG,yBAAyB,GAAGL,MAAM,GAAGG,MAAM;OAC5CC,MAAM,IAAI,EAAE;EAAC,KACbH,MAAM,IAAI,GAAG,CAAC;;IAEnB,IAAIK,OAAO,GAAG,EAAE;EAEhB,EAAA,IAAID,yBAAyB,EAAE;EAC7B;MACA,IAAME,EAAE,GAAGC,UAAU,CAACnB,MAAM,CAAC,CAACoB,SAAS,EAAE,CAAC;EAC1C,IAAA,IAAMC,MAAM,GAAG,IAAIC,GAAG,CAACtB,MAAM,CAACuB,GAAG,CAAC,UAAAC,KAAA,EAAaC,GAAG,EAAA;EAAA,MAAA,IAAAC,KAAA,GAAAjB,cAAA,CAAAe,KAAA,EAAA,CAAA,CAAA;EAAdG,QAAAA,GAAG,GAAAD,KAAA,CAAA,CAAA,CAAA;EAAEE,QAAAA,GAAG,GAAAF,KAAA,CAAA,CAAA,CAAA;QAAA,OAAW,CAAA,EAAA,CAAAzB,MAAA,CAAI0B,GAAG,EAAA,GAAA,CAAA,CAAA1B,MAAA,CAAI2B,GAAG,CAAIH,EAAAA,GAAG,CAAC;EAAA,KAAA,CAAC,CAAC;EAC/EP,IAAAA,EAAE,CAACW,QAAQ,CAACC,OAAO,CAAC,UAAAC,CAAC,EAAI;EAAA,MAAA,IAAAC,QAAA;QACvB,IAAMC,QAAQ,GAAGF,CAAC,CAACG,QAAQ,CAAC7B,WAAW,CAAC,CAAC,CAAC,CAAC8B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,OAAO,EAAE,CAAC;;QAEjE,IAAMC,IAAI,GAAG,EAAE;EACfJ,MAAAA,QAAQ,CAACH,OAAO,CAAC,UAAAQ,KAAA,EAAgB;EAAA,QAAA,IAAAC,KAAA,GAAA9B,cAAA,CAAA6B,KAAA,EAAA,CAAA,CAAA;EAAdX,UAAAA,GAAG,GAAAY,KAAA,CAAA,CAAA,CAAA;EAAEX,UAAAA,GAAG,GAAAW,KAAA,CAAA,CAAA,CAAA;UACzB,IAAMC,CAAC,MAAAvC,MAAA,CAAM0B,GAAG,EAAA1B,GAAAA,CAAAA,CAAAA,MAAA,CAAI2B,GAAG,CAAE;EACzBP,QAAAA,MAAM,CAACoB,GAAG,CAACD,CAAC,CAAC,IAAIH,IAAI,CAACK,IAAI,CAACrB,MAAM,CAACsB,GAAG,CAACH,CAAC,CAAC,CAAC;EAC3C,OAAC,CAAC;EAEF,MAAA,IAAIH,IAAI,CAAC/C,MAAM,KAAK,CAAC,EAAE,OAAO;;EAE9B;EACA,MAAA,IAAI+C,IAAI,CAACO,IAAI,CAAC,UAAAC,GAAG,EAAA;EAAA,QAAA,OAAIA,GAAG,GAAGhD,UAAU,CAACP,MAAM;EAAA,OAAA,CAAC,EAAE;EAC7C,QAAA,IAAMwD,gBAAgB,GAAGf,CAAC,CAACgB,UAAU,CAACC,YAAY;UAClD,IAAI,CAACC,WAAW,CAACH,gBAAgB,EAAE3C,iBAAiB,EAAEa,yBAAyB,CAAC,EAAE;EACpF;QAEA,CAAAgB,QAAA,GAAAf,OAAO,EAACyB,IAAI,CAAAQ,KAAA,CAAAlB,QAAA,EAAIK,IAAI,CAAC;EACvB,KAAC,CAAC;EACJ,GAAC,MAAM,IAAI,CAACvC,WAAW,CAACR,MAAM,EAAE;EAC9B;EACA,IAAA,IAAA6D,cAAA,GAAiCC,OAAa,CAACzD,OAAO,CAAC;QAA/C0D,QAAQ,GAAAF,cAAA,CAARE,QAAQ;QAAAC,oBAAA,GAAAH,cAAA,CAAEI,KAAK;EAALA,MAAAA,KAAK,GAAAD,oBAAA,KAAG,SAAA,GAAA,EAAE,GAAAA,oBAAA;MAC5BrC,OAAO,GAAGuC,MAAM,CAACH,QAAQ,EAAEE,KAAK,EAAE,CAAC,CAAC;EACtC,GAAC,MAAM;EACL;EACA,IAAA,IAAME,QAAQ,GAAGC,UAAU,CAACC,IAAI,CAAC3D,MAAM,CAAC;EAAC,IAAA,IAAA4D,KAAA,GAAA,SAAAA,KAAAC,CAAAA,CAAA,EAEyB;EAAA,MAAA,IAAAC,SAAA;EAChE,MAAA,IAAMzB,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACd,GAAG,CAAC,UAAAE,GAAG,EAAA;EAAA,QAAA,OAAIgC,QAAQ,CAACrC,SAAS,CAACyC,CAAC,GAAGpC,GAAG,CAAC;EAAA,OAAA,CAAC,CAAC;EAC/D,MAAA,IAAMQ,QAAQ,GAAGI,IAAI,CAACd,GAAG,CAAC,UAAAwC,MAAM,EAAA;UAAA,OAAI/D,MAAM,CAAC+D,MAAM,CAAC;SAAC,CAAA;;EAEnD;EACA,MAAA,IAAI1B,IAAI,CAACO,IAAI,CAAC,UAAAC,GAAG,EAAA;EAAA,QAAA,OAAIA,GAAG,GAAGhD,UAAU,CAACP,MAAM;EAAA,OAAA,CAAC,EAAE;UAC7C,IAAMwD,gBAAgB,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAACvB,GAAG,CAAC,UAAAyC,QAAQ,EAAA;EAAA,UAAA,OAAIC,IAAI,CAAChC,QAAQ,EAAE,UAAAiC,CAAC,EAAA;cAAA,OAAIA,CAAC,CAACF,QAAQ,CAAC;aAAC,CAAA;WAAC,CAAA;UACjF,IAAI,CAACf,WAAW,CAACH,gBAAgB,EAAE3C,iBAAiB,EAAEa,yBAAyB,CAAC,EAAA,OAAA,CAAA,CAAA;EAClF;EAEA,MAAA,CAAA8C,SAAA,GAAA7C,OAAO,EAACyB,IAAI,CAAAQ,KAAA,CAAAY,SAAA,EAAA5D,kBAAA,CAAImC,IAAI,CAAC,CAAA;OACtB;EAXD,IAAA,KAAK,IAAIwB,CAAC,GAAG,CAAC,EAAEM,GAAG,GAAGV,QAAQ,CAACrC,SAAS,CAAC9B,MAAM,EAAEuE,CAAC,GAAGM,GAAG,EAAEN,CAAC,IAAI,CAAC,EAAA;QAAA,IAAAD,KAAA,CAAAC,CAAA,CAOsB,EAAA;EAAS;EAKjG;;EAEA;IACA,IAAMO,UAAU,GAAGC,MAAW,CAACC,MAAM,CAACtE,MAAM,EAAE,UAAAuE,CAAC,EAAA;MAAA,OAAIA,CAAC,CAAC,CAAC,CAAC;EAAA,GAAA,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;IAChE,IAAMC,UAAU,GAAGH,MAAW,CAACC,MAAM,CAACtE,MAAM,EAAE,UAAAuE,CAAC,EAAA;MAAA,OAAIA,CAAC,CAAC,CAAC,CAAC;EAAA,GAAA,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;EAChE,EAAA,IAAME,GAAG,GAAGzE,MAAM,CAACuB,GAAG,CAAC,UAAAmD,KAAA,EAAA;EAAA,IAAA,IAAAC,KAAA,GAAAlE,cAAA,CAAAiE,KAAA,EAAA,CAAA,CAAA;EAAE/C,MAAAA,GAAG,GAAAgD,KAAA,CAAA,CAAA,CAAA;EAAE/C,MAAAA,GAAG,GAAA+C,KAAA,CAAA,CAAA,CAAA;MAAA,OAAM,CAACP,UAAU,CAACzC,GAAG,CAAC,EAAE6C,UAAU,CAAC5C,GAAG,CAAC,CAAC;KAAC,CAAA;EAE1E,EAAA,IAAMR,SAAS,GAAG;EAAEpB,IAAAA,MAAM,EAANA,MAAM;EAAEiB,IAAAA,OAAO,EAAPA,OAAO;EAAEwD,IAAAA,GAAG,EAAHA;KAAK;IAE1C,OAAO;EAAE9E,IAAAA,OAAO,EAAPA,OAAO;EAAEyB,IAAAA,SAAS,EAATA;KAAW;EAC/B;EAEA,SAASxB,wBAAwBA,CAACT,OAAO,EAAEyF,WAAW,EAAE;EACtD;EACA,EAAA,OAAOzF,OAAO,CAACoC,GAAG,CAAC,UAAAsD,MAAM,EAAI;MAC3B,IAAMC,IAAI,GAAG,EAAE;EAEf,IAAA,IAAIC,OAAO;EACXF,IAAAA,MAAM,CAAC/C,OAAO,CAAC,UAAAkD,GAAG,EAAI;EACpB,MAAA,IAAID,OAAO,EAAE;EACX,QAAA,IAAME,IAAI,GAAGC,WAAW,CAACF,GAAG,EAAED,OAAO,CAAC,GAAG,GAAG,GAAGI,IAAI,CAACC,EAAE;UACtD,IAAIH,IAAI,GAAGL,WAAW,EAAE;EACtB,UAAA,IAAMS,QAAQ,GAAGC,cAAc,CAACP,OAAO,EAAEC,GAAG,CAAC;YAC7C,IAAMO,KAAK,GAAG,CAAC,GAAGJ,IAAI,CAACK,IAAI,CAACP,IAAI,GAAGL,WAAW,CAAC;YAE/C,IAAIa,CAAC,GAAGF,KAAK;YACb,OAAOE,CAAC,GAAG,CAAC,EAAE;EACZX,YAAAA,IAAI,CAACpC,IAAI,CAAC2C,QAAQ,CAACI,CAAC,CAAC,CAAC;EACtBA,YAAAA,CAAC,IAAIF,KAAK;EACZ;EACF;EACF;EAEAT,MAAAA,IAAI,CAACpC,IAAI,CAACqC,OAAO,GAAGC,GAAG,CAAC;EAC1B,KAAC,CAAC;EAEF,IAAA,OAAOF,IAAI;EACb,GAAC,CAAC;EACJ;EAEA,SAAS/E,iBAAiBA,CAACZ,OAAO,EAAEyF,WAAW,EAAE;EAC/C,EAAA,IAAMzE,iBAAiB,GAAG;EAAEC,IAAAA,IAAI,EAAE,SAAS;EAAEC,IAAAA,WAAW,EAAElB;KAAS;EACnE,EAAA,IAAAuG,WAAA,GAA6CnF,SAAS,CAACJ,iBAAiB,CAAC;MAAAwF,WAAA,GAAAlF,cAAA,CAAAiF,WAAA,EAAA,CAAA,CAAA;MAAAE,YAAA,GAAAnF,cAAA,CAAAkF,WAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;EAAjEhF,IAAAA,MAAM,GAAAiF,YAAA,CAAA,CAAA,CAAA;EAAEhF,IAAAA,MAAM,GAAAgF,YAAA,CAAA,CAAA,CAAA;MAAAC,aAAA,GAAApF,cAAA,CAAAkF,WAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;EAAI7E,IAAAA,MAAM,GAAA+E,aAAA,CAAA,CAAA,CAAA;EAAE9E,IAAAA,MAAM,GAAA8E,aAAA,CAAA,CAAA,CAAA;;EAExC;IACA,IAAIV,IAAI,CAACW,GAAG,CAACX,IAAI,CAACY,GAAG,CAACjF,MAAM,GAAGH,MAAM,CAAC,EAAEwE,IAAI,CAACY,GAAG,CAAChF,MAAM,GAAGH,MAAM,CAAC,CAAC,GAAGgE,WAAW,EAAE,OAAO,EAAE;EAE3F,EAAA,IAAM5D,yBAAyB,GAAGL,MAAM,GAAGG,MAAM,IAAIC,MAAM,IAAI,EAAE,IAAIH,MAAM,IAAI,GAAG;IAElF,OAAOoF,gBAAgB,CAACpB,WAAW,EAAE;EAAEjE,IAAAA,MAAM,EAANA,MAAM;EAAEG,IAAAA,MAAM,EAANA,MAAM;EAAEF,IAAAA,MAAM,EAANA,MAAM;EAAEG,IAAAA,MAAM,EAANA;EAAO,GAAC,CAAC,CACrEkF,MAAM,CAAC,UAAAjB,GAAG,EAAA;EAAA,IAAA,OAAI/B,WAAW,CAAC+B,GAAG,EAAE7E,iBAAiB,EAAEa,yBAAyB,CAAC;KAAC,CAAA;EAClF;EAEA,SAASgF,gBAAgBA,CACvBE,qBAAqB,EAErB;EAAA,EAAA,IAAAC,KAAA,GAAA9G,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GADqC,EAAE;MAArCsB,MAAM,GAAAwF,KAAA,CAANxF,MAAM;MAAEG,MAAM,GAAAqF,KAAA,CAANrF,MAAM;MAAEF,MAAM,GAAAuF,KAAA,CAANvF,MAAM;MAAEG,MAAM,GAAAoF,KAAA,CAANpF,MAAM;EAEhC,EAAA,IAAMqF,SAAS,GAAGjB,IAAI,CAACkB,KAAK,CAAClB,IAAA,CAAAmB,GAAA,CAAC,GAAG,GAAGJ,qBAAqB,EAAG,CAAC,IAAGf,IAAI,CAACC,EAAE,CAAC;;EAExE;EACA,EAAA,IAAMmB,GAAG,GAAG,CAAC,CAAC,GAAGpB,IAAI,CAACjI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;;EAEnC,EAAA,IAAMsJ,SAAS,GAAG,SAAZA,SAASA,CAAG/E,GAAG,EAAA;MAAA,OAAIA,GAAG,GAAG8E,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;EAAA,GAAA;EACpD,EAAA,IAAME,SAAS,GAAG,SAAZA,SAASA,CAAGhF,GAAG,EAAA;EAAA,IAAA,OAAI0D,IAAI,CAACuB,IAAI,CAAC,CAAC,GAAGjF,GAAG,GAAG2E,SAAS,GAAG,CAAC,CAAC,GAAGjB,IAAI,CAACC,EAAE,GAAG,GAAG,GAAG,EAAE;EAAA,GAAA;EAChF,EAAA,IAAMuB,SAAS,GAAG,SAAZA,SAASA,CAAG/E,GAAG,EAAA;MAAA,OAAIwE,SAAS,IAAIjB,IAAI,CAACnI,GAAG,CAAC,CAAC4E,GAAG,GAAG,EAAE,IAAIuD,IAAI,CAACC,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EAAA,GAAA;EAEnF,EAAA,IAAMwB,WAAW,GAAG,CAClB7F,MAAM,KAAKxB,SAAS,GAAG4F,IAAI,CAACK,IAAI,CAACmB,SAAS,CAAC5F,MAAM,CAAC,CAAC,GAAG,CAAC,EACvDH,MAAM,KAAKrB,SAAS,GAAG4F,IAAI,CAAC0B,KAAK,CAACF,SAAS,CAAC/F,MAAM,CAAC,CAAC,GAAGwF,SAAS,GAAG,CAAC,CACrE;IAED,IAAMU,YAAY,GAAGnG,MAAM,KAAKpB,SAAS,IAAIuB,MAAM,KAAKvB,SAAS,GAC7D,YAAA;EAAA,IAAA,OAAM,IAAI;EAAA,GAAA,GACVoB,MAAM,KAAKpB,SAAS,GAClB,UAAAoC,GAAG,EAAA;MAAA,OAAIA,GAAG,IAAIb,MAAM;EAAA,GAAA,GACpBA,MAAM,KAAKvB,SAAS,GAClB,UAAAoC,GAAG,EAAA;MAAA,OAAIA,GAAG,IAAIhB,MAAM;EAAA,GAAA,GACpBG,MAAM,IAAIH,MAAM,GACd,UAAAgB,GAAG,EAAA;EAAA,IAAA,OAAIA,GAAG,IAAIhB,MAAM,IAAIgB,GAAG,IAAIb,MAAM;EAAA,GAAA,GACrC,UAAAa,GAAG,EAAA;EAAA,IAAA,OAAIA,GAAG,IAAIhB,MAAM,IAAIgB,GAAG,IAAIb,MAAM;EAAA,GAAA,CAAC;;IAEhD,IAAMgE,IAAI,GAAG,EAAE;EACf,EAAA,KAAK,IAAIjB,CAAC,GAAG+C,WAAW,CAAC,CAAC,CAAC,EAAE/C,CAAC,IAAI+C,WAAW,CAAC,CAAC,CAAC,EAAE/C,CAAC,EAAE,EAAE;EACrD,IAAA,IAAMlC,GAAG,GAAG6E,SAAS,CAAC3C,CAAC,CAAC;EACxBiD,IAAAA,YAAY,CAACnF,GAAG,CAAC,IAAImD,IAAI,CAACpC,IAAI,CAAC,CAACf,GAAG,EAAE8E,SAAS,CAAC5C,CAAC,CAAC,CAAC,CAAC;EACrD;EAEA,EAAA,OAAOiB,IAAI;EACb;EAEA,SAAS7B,WAAWA,CAAC+B,GAAG,EAAE7F,OAAO,EAAqC;EAAA,EAAA,IAAnC6B,yBAAyB,GAAA3B,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAG,KAAK;EAClE;EACA,EAAA,OAAO2B,yBAAyB,GAC5B+F,WAAW,CAAC5H,OAAO,EAAE6F,GAAG,CAAC,GACzBgC,qCAAkB,CAAChC,GAAG,EAAE7F,OAAO,CAAC;EACtC;;EC9JA,IAAM8H,KAAK,GAAGC,MAAM,CAACD,KAAK,GACtBC,MAAM,CAACD,KAAK;EAAC,EACb;EACFE,EAAAA,cAAc,EAAdA,oBAAc;EACdC,EAAAA,sBAAsB,EAAtBA;EACF,CAAC;;EAOD;EACA,IAAMC,cAAc,GAAG,IAAIJ,KAAK,CAACE,cAAc,EAAE,CAACG,YAAY,GAAG,cAAc,GAAG,cAAc;AAE1FC,MAAAA,oBAAoB,0BAAAC,qBAAA,EAAA;EACxB,EAAA,SAAAD,oBAAYE,CAAAA,cAAc,EAAEC,YAAY,EAAEC,SAAS,EAAEC,YAAY,EAAEC,SAAS,EAAEC,YAAY,EAAEC,mBAAmB,EAAE;EAAA,IAAA,IAAAC,KAAA;EAAAC,IAAAA,eAAA,OAAAV,oBAAA,CAAA;MAC/GS,KAAA,GAAAE,UAAA,CAAA,IAAA,EAAAX,oBAAA,CAAA;MAEAS,KAAA,CAAK5H,IAAI,GAAG,sBAAsB;MAElC4H,KAAA,CAAKG,UAAU,GAAG;EAChBV,MAAAA,cAAc,EAAdA,cAAc;EACdC,MAAAA,YAAY,EAAZA,YAAY;EACZC,MAAAA,SAAS,EAATA,SAAS;EACTC,MAAAA,YAAY,EAAZA,YAAY;EACZC,MAAAA,SAAS,EAATA,SAAS;EACTC,MAAAA,YAAY,EAAZA,YAAY;EACZC,MAAAA,mBAAmB,EAAnBA;OACD;;EAED;MACAL,YAAY,GAAGA,YAAY,IAAI,CAAC;MAChCC,SAAS,GAAGA,SAAS,IAAI,CAAC;EAC1BC,IAAAA,YAAY,GAAGA,YAAY,KAAKrI,SAAS,GAAGqI,YAAY,GAAG,IAAI;EAC/DC,IAAAA,SAAS,GAAGA,SAAS,KAAKtI,SAAS,GAAGsI,SAAS,GAAG,IAAI;EACtDC,IAAAA,YAAY,GAAGA,YAAY,KAAKvI,SAAS,GAAGuI,YAAY,GAAG,IAAI;EAC/DC,IAAAA,mBAAmB,GAAGA,mBAAmB,IAAI,CAAC,CAAC;;EAE/C;EACA,IAAA,IAAAK,qBAAA,GAA+BlJ,qBAAqB,CAACuI,cAAc,EAAE;EAAChI,QAAAA,UAAU,EAAEsI;EAAmB,OAAC,CAAC;QAA/FpI,OAAO,GAAAyI,qBAAA,CAAPzI,OAAO;QAAEyB,SAAS,GAAAgH,qBAAA,CAAThH,SAAS;EAC1B,IAAA,IAAMiH,OAAO,GAAG/L,KAAO,CAAC8E,SAAS,CAACqD,GAAG,CAAC;MAEtC,IAAIpB,QAAQ,GAAG,EAAE;MACjB,IAAIoB,GAAG,GAAG,EAAE;MACZ,IAAIxD,OAAO,GAAG,EAAE;EAChB,IAAA,IAAIqH,QAAQ,GAAG,CAAC,CAAC;;EAEjB,IAAA,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAGC,SAAS,EAAI;QAC5B,IAAMC,WAAW,GAAGtD,IAAI,CAACkB,KAAK,CAAChD,QAAQ,CAAC/D,MAAM,GAAG,CAAC,CAAC;EACnD,MAAA,IAAMoJ,UAAU,GAAGzH,OAAO,CAAC3B,MAAM;QAEjC+D,QAAQ,GAAGA,QAAQ,CAACpD,MAAM,CAACuI,SAAS,CAACnF,QAAQ,CAAC;QAC9CoB,GAAG,GAAGA,GAAG,CAACxE,MAAM,CAACuI,SAAS,CAAC/D,GAAG,CAAC;EAC/BxD,MAAAA,OAAO,GAAGA,OAAO,CAAChB,MAAM,CAAC,CAACwI,WAAW,GAAGD,SAAS,CAACvH,OAAO,GAAGuH,SAAS,CAACvH,OAAO,CAACM,GAAG,CAAC,UAAAsB,GAAG,EAAA;UAAA,OAAIA,GAAG,GAAG4F,WAAW;EAAA,OAAA,CAAC,CAAC;EAE5GT,MAAAA,KAAA,CAAKO,QAAQ,CAACG,UAAU,EAAEzH,OAAO,CAAC3B,MAAM,GAAGoJ,UAAU,EAAEJ,QAAQ,EAAE,CAAC;OACnE;EAEDR,IAAAA,YAAY,IAAIS,QAAQ,CAACI,aAAa,EAAE,CAAC;MACzCf,YAAY,IAAIW,QAAQ,CAACK,WAAW,CAAClB,YAAY,EAAE,KAAK,CAAC,CAAC;MAC1DG,SAAS,IAAIU,QAAQ,CAACK,WAAW,CAACjB,SAAS,EAAE,IAAI,CAAC,CAAC;;EAEnD;EACAK,IAAAA,KAAA,CAAKa,QAAQ,CAAC5H,OAAO,CAAC;EACtB+G,IAAAA,KAAA,CAAKX,cAAc,CAAC,CAAC,UAAU,EAAE,IAAIJ,KAAK,CAACG,sBAAsB,CAAC/D,QAAQ,EAAE,CAAC,CAAC,CAAC;EAC/E2E,IAAAA,KAAA,CAAKX,cAAc,CAAC,CAAC,IAAI,EAAE,IAAIJ,KAAK,CAACG,sBAAsB,CAAC3C,GAAG,EAAE,CAAC,CAAC,CAAC;;EAEpE;MACAuD,KAAA,CAAKc,oBAAoB,EAAE;;EAE3B;;EAEA,IAAA,SAASC,gBAAgBA,CAAC5J,OAAO,EAAE6J,QAAQ,EAAE;QAC3C,IAAMC,KAAK,GAAG,OAAOD,QAAQ,KAAK,UAAU,GAAGA,QAAQ,GAAG,YAAA;EAAA,QAAA,OAAMA,QAAQ;EAAA,OAAA;EACxE,MAAA,IAAME,QAAQ,GAAG/J,OAAO,CAACoC,GAAG,CAAC,UAAAsD,MAAM,EAAA;EAAA,QAAA,OAAIA,MAAM,CAACtD,GAAG,CAAC,UAAAnC,IAAA,EAAA;EAAA,UAAA,IAAAoC,KAAA,GAAAf,cAAA,CAAArB,IAAA,EAAA,CAAA,CAAA;EAAEuC,YAAAA,GAAG,GAAAH,KAAA,CAAA,CAAA,CAAA;EAAEI,YAAAA,GAAG,GAAAJ,KAAA,CAAA,CAAA,CAAA;EAAA,UAAA,OAAM2H,eAAe,CAACvH,GAAG,EAAED,GAAG,EAAEsH,KAAK,CAACtH,GAAG,EAAEC,GAAG,CAAC,CAAC;WAAC,CAAA;SAAC,CAAA;EAC9G;QACA,OAAOwB,OAAa,CAAC8F,QAAQ,CAAC;EAChC;MAEA,SAASP,aAAaA,GAAG;EACvB,MAAA,IAAAS,iBAAA,GAAuCL,gBAAgB,CAACpJ,OAAO,EAAE+H,YAAY,CAAC;UAA7D2B,WAAW,GAAAD,iBAAA,CAArB/F,QAAQ;UAAeE,KAAK,GAAA6F,iBAAA,CAAL7F,KAAK;EACnC,MAAA,IAAA+F,kBAAA,GAA6BP,gBAAgB,CAACpJ,OAAO,EAAEgI,SAAS,CAAC;UAAhD4B,QAAQ,GAAAD,kBAAA,CAAlBjG,QAAQ;QAEf,IAAMA,QAAQ,GAAG/G,KAAO,CAAC,CAACiN,QAAQ,EAAEF,WAAW,CAAC,CAAC;QACjD,IAAMjD,SAAS,GAAGjB,IAAI,CAACkB,KAAK,CAACkD,QAAQ,CAACjK,MAAM,GAAG,CAAC,CAAC;EAEjD,MAAA,IAAMkK,QAAQ,GAAG,IAAIC,GAAG,CAAClG,KAAK,CAAC;QAC/B,IAAImG,WAAW,GAAG,CAAC;QAEnB,IAAMzI,OAAO,GAAG,EAAE;QAClB,KAAK,IAAI0I,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGvD,SAAS,EAAEuD,KAAK,EAAE,EAAE;EAC9C,QAAA,IAAIC,KAAK,GAAGD,KAAK,GAAG,CAAC,CAAC;UACtB,IAAIC,KAAK,KAAKxD,SAAS,EAAE;YACvBwD,KAAK,GAAGF,WAAW,CAAC;WACrB,MAAM,IAAIF,QAAQ,CAAC/G,GAAG,CAACmH,KAAK,CAAC,EAAE;YAC9B,IAAMC,OAAO,GAAGD,KAAK;YACrBA,KAAK,GAAGF,WAAW,CAAC;EACpBA,UAAAA,WAAW,GAAGG,OAAO;EACvB;;EAEA;EACA5I,QAAAA,OAAO,CAACyB,IAAI,CAACiH,KAAK,EAAEA,KAAK,GAAGvD,SAAS,EAAEwD,KAAK,GAAGxD,SAAS,CAAC;UACzDnF,OAAO,CAACyB,IAAI,CAACkH,KAAK,GAAGxD,SAAS,EAAEwD,KAAK,EAAED,KAAK,CAAC;EAC/C;EAEA,MAAA,IAAMlF,GAAG,GAAG,EAAE,CAAC;EACf,MAAA,KAAK,IAAIqF,CAAC,GAAC,CAAC,EAAEA,CAAC,IAAE,CAAC,EAAEA,CAAC,EAAE,EACrB,KAAK,IAAIjG,CAAC,GAAC,CAAC,EAAEA,CAAC,GAACuC,SAAS,EAAEvC,CAAC,IAAE,CAAC,EAC7BY,GAAG,CAAC/B,IAAI,CAACmB,CAAC,IAAEuC,SAAS,GAAC,CAAC,CAAC,EAAE0D,CAAC,CAAC;QAEhC,OAAO;EAAE7I,QAAAA,OAAO,EAAPA,OAAO;EAAEoC,QAAAA,QAAQ,EAARA,QAAQ;EAAEoB,QAAAA,GAAG,EAAHA;SAAK;EACnC;MAEA,SAASmE,WAAWA,CAACmB,MAAM,EAAgB;EAAA,MAAA,IAAdC,KAAK,GAAA3K,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAG,IAAI;QACvC,OAAO;EACL;EACA4B,QAAAA,OAAO,EAAE+I,KAAK,GAAG5I,SAAS,CAACH,OAAO,GAAGG,SAAS,CAACH,OAAO,CAACkB,KAAK,EAAE,CAACC,OAAO,EAAE;EACxEiB,QAAAA,QAAQ,EAAE0F,gBAAgB,CAAC,CAAC3H,SAAS,CAACpB,MAAM,CAAC,EAAE+J,MAAM,CAAC,CAAC1G,QAAQ;EAC/DoB,QAAAA,GAAG,EAAE4D;SACN;EACH;EAAC,IAAA,OAAAL,KAAA;EACH;IAACiC,SAAA,CAAA1C,oBAAA,EAAAC,qBAAA,CAAA;IAAA,OAAA0C,YAAA,CAAA3C,oBAAA,CAAA;EAAA,CA3GgCN,CAAAA,KAAK,CAACE,cAAc,EA8GvD;EAEA,SAASgC,eAAeA,CAACvH,GAAG,EAAED,GAAG,EAAS;EAAA,EAAA,IAAPwI,CAAC,GAAA9K,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAG,CAAC;IACtC,IAAMkH,GAAG,GAAG,CAAC,EAAE,GAAG3E,GAAG,IAAIuD,IAAI,CAACC,EAAE,GAAG,GAAG;IACtC,IAAMgF,KAAK,GAAG,CAAC,EAAE,GAAGzI,GAAG,IAAIwD,IAAI,CAACC,EAAE,GAAG,GAAG;EACxC,EAAA,OAAO,CACL+E,CAAC,GAAGhF,IAAI,CAAClI,GAAG,CAACsJ,GAAG,CAAC,GAAGpB,IAAI,CAACnI,GAAG,CAACoN,KAAK,CAAC;EAAE;EACrCD,EAAAA,CAAC,GAAGhF,IAAI,CAACnI,GAAG,CAACuJ,GAAG,CAAC;EAAE;EACnB4D,EAAAA,CAAC,GAAGhF,IAAI,CAAClI,GAAG,CAACsJ,GAAG,CAAC,GAAGpB,IAAI,CAAClI,GAAG,CAACmN,KAAK,CAAC;KACpC;EACH;;;;;;;;", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89]}