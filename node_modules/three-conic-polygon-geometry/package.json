{"name": "three-conic-polygon-geometry", "version": "2.1.2", "description": "ThreeJS geometry for drawing polygons on a sphere", "type": "module", "unpkg": "dist/three-conic-polygon-geometry.min.js", "main": "dist/three-conic-polygon-geometry.mjs", "module": "dist/three-conic-polygon-geometry.mjs", "types": "dist/three-conic-polygon-geometry.d.ts", "exports": {"types": "./dist/three-conic-polygon-geometry.d.ts", "umd": "./dist/three-conic-polygon-geometry.min.js", "default": "./dist/three-conic-polygon-geometry.mjs"}, "homepage": "https://github.com/vasturiano/three-conic-polygon-geometry", "repository": {"type": "git", "url": "git+https://github.com/vasturiano/three-conic-polygon-geometry.git"}, "bugs": {"url": "https://github.com/vasturiano/three-conic-polygon-geometry/issues"}, "license": "MIT", "keywords": ["3d", "three", "polygons", "sphere", "conic", "webgl", "g<PERSON><PERSON><PERSON>"], "author": {"name": "Vasco <PERSON>turiano", "url": "https://github.com/vasturiano"}, "files": ["src/**/*", "dist/**/*", "example/**/*"], "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -w -c", "prepare": "npm run build"}, "dependencies": {"@turf/boolean-point-in-polygon": "^7.2", "d3-array": "1 - 3", "d3-geo": "1 - 3", "d3-geo-voronoi": "2", "d3-scale": "1 - 4", "delaunator": "5", "earcut": "3"}, "peerDependencies": {"three": ">=0.72.0"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.0", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.2", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-terser": "^0.4.4", "@types/three": ">=0.72.0", "rimraf": "^6.0.1", "rollup": "^4.30.1", "rollup-plugin-dts": "^6.1.1", "typescript": "^5.7.3"}, "engines": {"node": ">=12"}}