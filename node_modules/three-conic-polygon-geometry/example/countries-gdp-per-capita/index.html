<head>
  <style> body { margin: 0; } </style>

  <script type="importmap">{ "imports": {
    "three": "https://esm.sh/three",
    "three/": "https://esm.sh/three/"
  }}</script>

<!--  <script type="module">import * as THREE from 'three'; window.THREE = THREE;</script>-->
<!--  <script src="../../dist/three-conic-polygon-geometry.js" defer></script>-->
</head>

<body>
<div id="viz"></div>

<script type="module">
  import ConicPolygonGeometry from 'https://esm.sh/three-conic-polygon-geometry?external=three';
  import * as THREE from 'three';
  import { TrackballControls } from 'three/examples/jsm/controls/TrackballControls.js?external=three';

  fetch('../geojson/ne_110m_admin_0_countries.geojson').then(res => res.json()).then(countries =>
  {
    const materials = [
      new THREE.MeshBasicMaterial({ side: THREE.DoubleSide, color: 'green', opacity: 0.1, transparent: true }), // side material
      new THREE.MeshBasicMaterial({ side: THREE.DoubleSide, color: 'red', opacity: 0.7, transparent: true }), // bottom cap material
      new THREE.MeshBasicMaterial({ side: THREE.DoubleSide, color: 'red', opacity: 0.7, transparent: true }) // top cap material
    ];

    const polygonMeshes = [];
    countries.features.forEach(({ properties, geometry }) => {
      const polygons = geometry.type === 'Polygon' ? [geometry.coordinates] : geometry.coordinates;
      const alt = properties.GDP_MD_EST / Math.max(1e5, properties.POP_EST) * 1e3;

      polygons.forEach(coords => {
        polygonMeshes.push(
          new THREE.Mesh(
            new ConicPolygonGeometry(coords, 0, alt),
            materials
          )
        );
      });
    });

    // Setup renderer
    const renderer = new THREE.WebGLRenderer();
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(window.devicePixelRatio);
    document.getElementById('viz').appendChild(renderer.domElement);

    // Setup scene
    const scene = new THREE.Scene();
    polygonMeshes.forEach(mesh => scene.add(mesh));
    scene.add(new THREE.AmbientLight(0xbbbbbb));
    scene.add(new THREE.DirectionalLight(0xffffff, 0.6));

    // Setup camera
    const camera = new THREE.PerspectiveCamera();
    camera.aspect = window.innerWidth/ window.innerHeight;
    camera.updateProjectionMatrix();
    camera.position.z = 200;

    // Add camera controls
    const tbControls = new TrackballControls(camera, renderer.domElement);
    tbControls.minDistance = 1;
    tbControls.rotateSpeed = 5;
    tbControls.zoomSpeed = 0.8;

    // Kick-off renderer
    (function animate() { // IIFE
      // Frame cycle
      tbControls.update();
      renderer.render(scene, camera);
      requestAnimationFrame(animate);
    })();
  });
</script>
</body>