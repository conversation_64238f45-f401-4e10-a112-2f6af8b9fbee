// https://github.com/vasturiano/d3-octree v1.1.0
!function(t,i){"object"==typeof exports&&"undefined"!=typeof module?i(exports):"function"==typeof define&&define.amd?define(["exports"],i):i((t="undefined"!=typeof globalThis?globalThis:t||self).d3=t.d3||{})}(this,(function(t){"use strict";function i(t,i,n,s,e){if(isNaN(i)||isNaN(n)||isNaN(s))return t;var h,r,o,a,l,u,_,f,c,y,x,d,p=t._root,w={data:e},z=t._x0,N=t._y0,v=t._z0,g=t._x1,A=t._y1,b=t._z1;if(!p)return t._root=w,t;for(;p.length;)if((f=i>=(r=(z+g)/2))?z=r:g=r,(c=n>=(o=(N+A)/2))?N=o:A=o,(y=s>=(a=(v+b)/2))?v=a:b=a,h=p,!(p=p[x=y<<2|c<<1|f]))return h[x]=w,t;if(l=+t._x.call(null,p.data),u=+t._y.call(null,p.data),_=+t._z.call(null,p.data),i===l&&n===u&&s===_)return w.next=p,h?h[x]=w:t._root=w,t;do{h=h?h[x]=new Array(8):t._root=new Array(8),(f=i>=(r=(z+g)/2))?z=r:g=r,(c=n>=(o=(N+A)/2))?N=o:A=o,(y=s>=(a=(v+b)/2))?v=a:b=a}while((x=y<<2|c<<1|f)==(d=(_>=a)<<2|(u>=o)<<1|l>=r));return h[d]=p,h[x]=w,t}function n(t,i,n,s,e,h,r){this.node=t,this.x0=i,this.y0=n,this.z0=s,this.x1=e,this.y1=h,this.z1=r}const s=(t,i,n,s,e,h)=>Math.sqrt((t-s)**2+(i-e)**2+(n-h)**2);function e(t){return t[0]}function h(t){return t[1]}function r(t){return t[2]}function o(t,i,n,s){var o=new a(null==i?e:i,null==n?h:n,null==s?r:s,NaN,NaN,NaN,NaN,NaN,NaN);return null==t?o:o.addAll(t)}function a(t,i,n,s,e,h,r,o,a){this._x=t,this._y=i,this._z=n,this._x0=s,this._y0=e,this._z0=h,this._x1=r,this._y1=o,this._z1=a,this._root=void 0}function l(t){for(var i={data:t.data},n=i;t=t.next;)n=n.next={data:t.data};return i}var u=o.prototype=a.prototype;u.copy=function(){var t,i,n=new a(this._x,this._y,this._z,this._x0,this._y0,this._z0,this._x1,this._y1,this._z1),s=this._root;if(!s)return n;if(!s.length)return n._root=l(s),n;for(t=[{source:s,target:n._root=new Array(8)}];s=t.pop();)for(var e=0;e<8;++e)(i=s.source[e])&&(i.length?t.push({source:i,target:s.target[e]=new Array(8)}):s.target[e]=l(i));return n},u.add=function(t){const n=+this._x.call(null,t),s=+this._y.call(null,t),e=+this._z.call(null,t);return i(this.cover(n,s,e),n,s,e,t)},u.addAll=function(t){Array.isArray(t)||(t=Array.from(t));const n=t.length,s=new Float64Array(n),e=new Float64Array(n),h=new Float64Array(n);let r=1/0,o=1/0,a=1/0,l=-1/0,u=-1/0,_=-1/0;for(let i,f,c,y,x=0;x<n;++x)isNaN(f=+this._x.call(null,i=t[x]))||isNaN(c=+this._y.call(null,i))||isNaN(y=+this._z.call(null,i))||(s[x]=f,e[x]=c,h[x]=y,f<r&&(r=f),f>l&&(l=f),c<o&&(o=c),c>u&&(u=c),y<a&&(a=y),y>_&&(_=y));if(r>l||o>u||a>_)return this;this.cover(r,o,a).cover(l,u,_);for(let r=0;r<n;++r)i(this,s[r],e[r],h[r],t[r]);return this},u.cover=function(t,i,n){if(isNaN(t=+t)||isNaN(i=+i)||isNaN(n=+n))return this;var s=this._x0,e=this._y0,h=this._z0,r=this._x1,o=this._y1,a=this._z1;if(isNaN(s))r=(s=Math.floor(t))+1,o=(e=Math.floor(i))+1,a=(h=Math.floor(n))+1;else{for(var l,u,_=r-s||1,f=this._root;s>t||t>=r||e>i||i>=o||h>n||n>=a;)switch(u=(n<h)<<2|(i<e)<<1|t<s,(l=new Array(8))[u]=f,f=l,_*=2,u){case 0:r=s+_,o=e+_,a=h+_;break;case 1:s=r-_,o=e+_,a=h+_;break;case 2:r=s+_,e=o-_,a=h+_;break;case 3:s=r-_,e=o-_,a=h+_;break;case 4:r=s+_,o=e+_,h=a-_;break;case 5:s=r-_,o=e+_,h=a-_;break;case 6:r=s+_,e=o-_,h=a-_;break;case 7:s=r-_,e=o-_,h=a-_}this._root&&this._root.length&&(this._root=f)}return this._x0=s,this._y0=e,this._z0=h,this._x1=r,this._y1=o,this._z1=a,this},u.data=function(){var t=[];return this.visit((function(i){if(!i.length)do{t.push(i.data)}while(i=i.next)})),t},u.extent=function(t){return arguments.length?this.cover(+t[0][0],+t[0][1],+t[0][2]).cover(+t[1][0],+t[1][1],+t[1][2]):isNaN(this._x0)?void 0:[[this._x0,this._y0,this._z0],[this._x1,this._y1,this._z1]]},u.find=function(t,i,s,e){var h,r,o,a,l,u,_,f,c,y=this._x0,x=this._y0,d=this._z0,p=this._x1,w=this._y1,z=this._z1,N=[],v=this._root;for(v&&N.push(new n(v,y,x,d,p,w,z)),null==e?e=1/0:(y=t-e,x=i-e,d=s-e,p=t+e,w=i+e,z=s+e,e*=e);f=N.pop();)if(!(!(v=f.node)||(r=f.x0)>p||(o=f.y0)>w||(a=f.z0)>z||(l=f.x1)<y||(u=f.y1)<x||(_=f.z1)<d))if(v.length){var g=(r+l)/2,A=(o+u)/2,b=(a+_)/2;N.push(new n(v[7],g,A,b,l,u,_),new n(v[6],r,A,b,g,u,_),new n(v[5],g,o,b,l,A,_),new n(v[4],r,o,b,g,A,_),new n(v[3],g,A,a,l,u,b),new n(v[2],r,A,a,g,u,b),new n(v[1],g,o,a,l,A,b),new n(v[0],r,o,a,g,A,b)),(c=(s>=b)<<2|(i>=A)<<1|t>=g)&&(f=N[N.length-1],N[N.length-1]=N[N.length-1-c],N[N.length-1-c]=f)}else{var k=t-+this._x.call(null,v.data),m=i-+this._y.call(null,v.data),M=s-+this._z.call(null,v.data),F=k*k+m*m+M*M;if(F<e){var q=Math.sqrt(e=F);y=t-q,x=i-q,d=s-q,p=t+q,w=i+q,z=s+q,h=v.data}}return h},u.findAllWithinRadius=function(t,i,n,e){const h=[],r=t-e,o=i-e,a=n-e,l=t+e,u=i+e,_=n+e;return this.visit(((f,c,y,x,d,p,w)=>{if(!f.length)do{const r=f.data;s(t,i,n,this._x(r),this._y(r),this._z(r))<=e&&h.push(r)}while(f=f.next);return c>l||y>u||x>_||d<r||p<o||w<a})),h},u.remove=function(t){if(isNaN(h=+this._x.call(null,t))||isNaN(r=+this._y.call(null,t))||isNaN(o=+this._z.call(null,t)))return this;var i,n,s,e,h,r,o,a,l,u,_,f,c,y,x,d=this._root,p=this._x0,w=this._y0,z=this._z0,N=this._x1,v=this._y1,g=this._z1;if(!d)return this;if(d.length)for(;;){if((_=h>=(a=(p+N)/2))?p=a:N=a,(f=r>=(l=(w+v)/2))?w=l:v=l,(c=o>=(u=(z+g)/2))?z=u:g=u,i=d,!(d=d[y=c<<2|f<<1|_]))return this;if(!d.length)break;(i[y+1&7]||i[y+2&7]||i[y+3&7]||i[y+4&7]||i[y+5&7]||i[y+6&7]||i[y+7&7])&&(n=i,x=y)}for(;d.data!==t;)if(s=d,!(d=d.next))return this;return(e=d.next)&&delete d.next,s?(e?s.next=e:delete s.next,this):i?(e?i[y]=e:delete i[y],(d=i[0]||i[1]||i[2]||i[3]||i[4]||i[5]||i[6]||i[7])&&d===(i[7]||i[6]||i[5]||i[4]||i[3]||i[2]||i[1]||i[0])&&!d.length&&(n?n[x]=d:this._root=d),this):(this._root=e,this)},u.removeAll=function(t){for(var i=0,n=t.length;i<n;++i)this.remove(t[i]);return this},u.root=function(){return this._root},u.size=function(){var t=0;return this.visit((function(i){if(!i.length)do{++t}while(i=i.next)})),t},u.visit=function(t){var i,s,e,h,r,o,a,l,u=[],_=this._root;for(_&&u.push(new n(_,this._x0,this._y0,this._z0,this._x1,this._y1,this._z1));i=u.pop();)if(!t(_=i.node,e=i.x0,h=i.y0,r=i.z0,o=i.x1,a=i.y1,l=i.z1)&&_.length){var f=(e+o)/2,c=(h+a)/2,y=(r+l)/2;(s=_[7])&&u.push(new n(s,f,c,y,o,a,l)),(s=_[6])&&u.push(new n(s,e,c,y,f,a,l)),(s=_[5])&&u.push(new n(s,f,h,y,o,c,l)),(s=_[4])&&u.push(new n(s,e,h,y,f,c,l)),(s=_[3])&&u.push(new n(s,f,c,r,o,a,y)),(s=_[2])&&u.push(new n(s,e,c,r,f,a,y)),(s=_[1])&&u.push(new n(s,f,h,r,o,c,y)),(s=_[0])&&u.push(new n(s,e,h,r,f,c,y))}return this},u.visitAfter=function(t){var i,s=[],e=[];for(this._root&&s.push(new n(this._root,this._x0,this._y0,this._z0,this._x1,this._y1,this._z1));i=s.pop();){var h=i.node;if(h.length){var r,o=i.x0,a=i.y0,l=i.z0,u=i.x1,_=i.y1,f=i.z1,c=(o+u)/2,y=(a+_)/2,x=(l+f)/2;(r=h[0])&&s.push(new n(r,o,a,l,c,y,x)),(r=h[1])&&s.push(new n(r,c,a,l,u,y,x)),(r=h[2])&&s.push(new n(r,o,y,l,c,_,x)),(r=h[3])&&s.push(new n(r,c,y,l,u,_,x)),(r=h[4])&&s.push(new n(r,o,a,x,c,y,f)),(r=h[5])&&s.push(new n(r,c,a,x,u,y,f)),(r=h[6])&&s.push(new n(r,o,y,x,c,_,f)),(r=h[7])&&s.push(new n(r,c,y,x,u,_,f))}e.push(i)}for(;i=e.pop();)t(i.node,i.x0,i.y0,i.z0,i.x1,i.y1,i.z1);return this},u.x=function(t){return arguments.length?(this._x=t,this):this._x},u.y=function(t){return arguments.length?(this._y=t,this):this._y},u.z=function(t){return arguments.length?(this._z=t,this):this._z},t.octree=o}));
