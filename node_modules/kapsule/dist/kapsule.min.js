// Version 1.16.3 kapsule - https://github.com/vasturiano/kapsule
!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):(t="undefined"!=typeof globalThis?globalThis:t||self).Kapsule=n()}(this,(function(){"use strict";function t(t,n){(null==n||n>t.length)&&(n=t.length);for(var r=0,e=Array(n);r<n;r++)e[r]=t[r];return e}function n(t,n,r){return Object.defineProperty(t,"prototype",{writable:!1}),t}function r(n,r){return function(t){if(Array.isArray(t))return t}(n)||function(t,n){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var e,o,i,a,u=[],f=!0,c=!1;try{if(i=(r=r.call(t)).next,0===n);else for(;!(f=(e=i.call(r)).done)&&(u.push(e.value),u.length!==n);f=!0);}catch(t){c=!0,o=t}finally{try{if(!f&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return u}}(n,r)||function(n,r){if(n){if("string"==typeof n)return t(n,r);var e={}.toString.call(n).slice(8,-1);return"Object"===e&&n.constructor&&(e=n.constructor.name),"Map"===e||"Set"===e?Array.from(n):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?t(n,r):void 0}}(n,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function e(t){var n=typeof t;return null!=t&&("object"==n||"function"==n)}var o="object"==typeof global&&global&&global.Object===Object&&global,i="object"==typeof self&&self&&self.Object===Object&&self,a=o||i||Function("return this")(),u=function(){return a.Date.now()},f=/\s/;var c=/^\s+/;function l(t){return t?t.slice(0,function(t){for(var n=t.length;n--&&f.test(t.charAt(n)););return n}(t)+1).replace(c,""):t}var s=a.Symbol,v=Object.prototype,d=v.hasOwnProperty,p=v.toString,y=s?s.toStringTag:void 0;var h=Object.prototype.toString;var b=s?s.toStringTag:void 0;function g(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":b&&b in Object(t)?function(t){var n=d.call(t,y),r=t[y];try{t[y]=void 0;var e=!0}catch(t){}var o=p.call(t);return e&&(n?t[y]=r:delete t[y]),o}(t):function(t){return h.call(t)}(t)}var m=/^[-+]0x[0-9a-f]+$/i,j=/^0b[01]+$/i,O=/^0o[0-7]+$/i,w=parseInt;function T(t){if("number"==typeof t)return t;if(function(t){return"symbol"==typeof t||function(t){return null!=t&&"object"==typeof t}(t)&&"[object Symbol]"==g(t)}(t))return NaN;if(e(t)){var n="function"==typeof t.valueOf?t.valueOf():t;t=e(n)?n+"":n}if("string"!=typeof t)return 0===t?t:+t;t=l(t);var r=j.test(t);return r||O.test(t)?w(t.slice(2),r?2:8):m.test(t)?NaN:+t}var S=Math.max,A=Math.min;function x(t,n,r){var o,i,a,f,c,l,s=0,v=!1,d=!1,p=!0;if("function"!=typeof t)throw new TypeError("Expected a function");function y(n){var r=o,e=i;return o=i=void 0,s=n,f=t.apply(e,r)}function h(t){var r=t-l;return void 0===l||r>=n||r<0||d&&t-s>=a}function b(){var t=u();if(h(t))return g(t);c=setTimeout(b,function(t){var r=n-(t-l);return d?A(r,a-(t-s)):r}(t))}function g(t){return c=void 0,p&&o?y(t):(o=i=void 0,f)}function m(){var t=u(),r=h(t);if(o=arguments,i=this,l=t,r){if(void 0===c)return function(t){return s=t,c=setTimeout(b,n),v?y(t):f}(l);if(d)return clearTimeout(c),c=setTimeout(b,n),y(l)}return void 0===c&&(c=setTimeout(b,n)),f}return n=T(n)||0,e(r)&&(v=!!r.leading,a=(d="maxWait"in r)?S(T(r.maxWait)||0,n):a,p="trailing"in r?!!r.trailing:p),m.cancel=function(){void 0!==c&&clearTimeout(c),s=0,o=l=i=c=void 0},m.flush=function(){return void 0===c?f:g(u())},m}var E=n((function t(n,r){var e=r.default,o=void 0===e?null:e,i=r.triggerUpdate,a=void 0===i||i,u=r.onChange,f=void 0===u?function(t,n){}:u;!function(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}(this,t),this.name=n,this.defaultVal=o,this.triggerUpdate=a,this.onChange=f}));return function(t){var n=t.stateInit,e=void 0===n?function(){return{}}:n,o=t.props,i=void 0===o?{}:o,a=t.methods,u=void 0===a?{}:a,f=t.aliases,c=void 0===f?{}:f,l=t.init,s=void 0===l?function(){}:l,v=t.update,d=void 0===v?function(){}:v,p=Object.keys(i).map((function(t){return new E(t,i[t])}));return function t(){for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=!!(this instanceof t?this.constructor:void 0),f=a?o.shift():void 0,l=o[0],v=void 0===l?{}:l,y=Object.assign({},e instanceof Function?e(v):e,{initialised:!1}),h={};function b(t){return g(t,v),m(),b}var g=function(t,n){s.call(b,t,y,n),y.initialised=!0},m=x((function(){y.initialised&&(d.call(b,y,h),h={})}),1);return p.forEach((function(t){b[t.name]=function(t){var n=t.name,r=t.triggerUpdate,e=void 0!==r&&r,o=t.onChange,i=void 0===o?function(t,n){}:o,a=t.defaultVal,u=void 0===a?null:a;return function(t){var r=y[n];if(!arguments.length)return r;var o=void 0===t?u:t;return y[n]=o,i.call(b,o,y,r),!h.hasOwnProperty(n)&&(h[n]=r),e&&m(),b}}(t)})),Object.keys(u).forEach((function(t){b[t]=function(){for(var n,r=arguments.length,e=new Array(r),o=0;o<r;o++)e[o]=arguments[o];return(n=u[t]).call.apply(n,[b,y].concat(e))}})),Object.entries(c).forEach((function(t){var n=r(t,2),e=n[0],o=n[1];return b[e]=b[o]})),b.resetProps=function(){return p.forEach((function(t){b[t.name](t.defaultVal)})),b},b.resetProps(),y._rerender=m,a&&f&&b(f),b}}}));
