{"name": "@tweenjs/tween.js", "description": "Super simple, fast and easy to use tweening engine which incorporates optimised <PERSON>'s equations.", "version": "18.6.4", "main": "dist/tween.cjs.js", "types": "dist/tween.d.ts", "module": "dist/tween.esm.js", "files": ["dist", "README.md", "LICENSE"], "homepage": "https://github.com/tweenjs/tween.js", "repository": {"type": "git", "url": "https://github.com/tweenjs/tween.js.git"}, "bugs": {"url": "https://github.com/tweenjs/tween.js/issues"}, "license": "MIT", "keywords": ["tween", "interpolation"], "dependencies": {}, "scripts": {"dev": "(npm run tsc-watch -- --preserveWatchOutput & p1=$!; npm run rollup-build -- --watch & p2=$!; wait $p1 $p2)", "build": "rimraf dist .tmp && node scripts/write-version.js && npm run tsc && npm run rollup-build", "rollup-build": "rollup -c ./rollup.config.js", "tsc": "tsc", "tsc-watch": "tsc --watch", "examples": "npx serve .", "test": "npm run build && npm run test-unit && npm run test-lint", "test-unit": "nodeunit test/unit/nodeunitheadless.js", "test-lint": "npm run prettier -- --check && eslint 'src/**/*.ts'", "lint": "npm run prettier -- --write && eslint 'src/**/*.ts' --fix", "prettier": "prettier './**/*.{js,ts,md,json,html,css}'", "prepare": "npm run build", "version": "npm test && git add .", "release:patch": "npm version patch --message 'v%s' && npm publish && npm run _release:push-branch", "release:minor": "npm version minor --message 'v%s' && npm publish && npm run _release:push-branch", "release:major": "npm version major --message 'v%s' && npm publish && npm run _release:push-branch", "_release:push-branch": "git push --follow-tags --set-upstream origin `git rev-parse --abbrev-ref HEAD`"}, "author": "tween.js contributors (https://github.com/tweenjs/tween.js/graphs/contributors)", "devDependencies": {"@typescript-eslint/eslint-plugin": "^3.1.0", "@typescript-eslint/parser": "^3.1.0", "eslint": "^7.1.0", "eslint-config-prettier": "^6.7.0", "eslint-plugin-prettier": "^3.1.1", "nodeunit": "^0.11.3", "prettier": "^2.0.0", "rimraf": "^3.0.0", "rollup": "^2.0.0", "rollup-plugin-dts": "1.4.10", "tslib": "^1.10.0", "typescript": "^4.0.0"}}