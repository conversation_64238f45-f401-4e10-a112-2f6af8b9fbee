{"version": 3, "sources": ["../../d3-geo/src/stream.js", "../../d3-geo/src/math.js", "../../d3-geo/src/noop.js", "../../d3-geo/src/area.js", "../../d3-geo/src/cartesian.js", "../../d3-geo/src/bounds.js", "../../d3-geo/src/centroid.js", "../../d3-geo/src/compose.js", "../../d3-geo/src/rotation.js", "../../d3-geo/src/constant.js", "../../d3-geo/src/circle.js", "../../d3-geo/src/clip/buffer.js", "../../d3-geo/src/pointEqual.js", "../../d3-geo/src/clip/rejoin.js", "../../d3-geo/src/polygonContains.js", "../../d3-geo/src/clip/index.js", "../../d3-geo/src/clip/antimeridian.js", "../../d3-geo/src/clip/circle.js", "../../d3-geo/src/clip/line.js", "../../d3-geo/src/clip/rectangle.js", "../../d3-geo/src/clip/extent.js", "../../d3-geo/src/length.js", "../../d3-geo/src/distance.js", "../../d3-geo/src/contains.js", "../../d3-geo/src/graticule.js", "../../d3-geo/src/interpolate.js", "../../d3-geo/src/identity.js", "../../d3-geo/src/path/area.js", "../../d3-geo/src/path/bounds.js", "../../d3-geo/src/path/centroid.js", "../../d3-geo/src/path/context.js", "../../d3-geo/src/path/measure.js", "../../d3-geo/src/path/string.js", "../../d3-geo/src/path/index.js", "../../d3-geo/src/transform.js", "../../d3-geo/src/projection/fit.js", "../../d3-geo/src/projection/resample.js", "../../d3-geo/src/projection/index.js", "../../d3-geo/src/projection/conic.js", "../../d3-geo/src/projection/cylindricalEqualArea.js", "../../d3-geo/src/projection/conicEqualArea.js", "../../d3-geo/src/projection/albers.js", "../../d3-geo/src/projection/albersUsa.js", "../../d3-geo/src/projection/azimuthal.js", "../../d3-geo/src/projection/azimuthalEqualArea.js", "../../d3-geo/src/projection/azimuthalEquidistant.js", "../../d3-geo/src/projection/mercator.js", "../../d3-geo/src/projection/conicConformal.js", "../../d3-geo/src/projection/equirectangular.js", "../../d3-geo/src/projection/conicEquidistant.js", "../../d3-geo/src/projection/equalEarth.js", "../../d3-geo/src/projection/gnomonic.js", "../../d3-geo/src/projection/identity.js", "../../d3-geo/src/projection/naturalEarth1.js", "../../d3-geo/src/projection/orthographic.js", "../../d3-geo/src/projection/stereographic.js", "../../d3-geo/src/projection/transverseMercator.js", "../../d3-delaunay/src/path.js", "../../d3-delaunay/src/polygon.js", "../../d3-delaunay/src/voronoi.js", "../../robust-predicates/esm/util.js", "../../robust-predicates/esm/orient2d.js", "../../robust-predicates/esm/orient3d.js", "../../robust-predicates/esm/incircle.js", "../../robust-predicates/esm/insphere.js", "../../delaunator/index.js", "../../d3-delaunay/src/delaunay.js", "../../d3-scale-chromatic/src/colors.js", "../../d3-scale-chromatic/src/categorical/category10.js", "../../d3-scale-chromatic/src/categorical/Accent.js", "../../d3-scale-chromatic/src/categorical/Dark2.js", "../../d3-scale-chromatic/src/categorical/observable10.js", "../../d3-scale-chromatic/src/categorical/Paired.js", "../../d3-scale-chromatic/src/categorical/Pastel1.js", "../../d3-scale-chromatic/src/categorical/Pastel2.js", "../../d3-scale-chromatic/src/categorical/Set1.js", "../../d3-scale-chromatic/src/categorical/Set2.js", "../../d3-scale-chromatic/src/categorical/Set3.js", "../../d3-scale-chromatic/src/categorical/Tableau10.js", "../../d3-scale-chromatic/src/ramp.js", "../../d3-scale-chromatic/src/diverging/BrBG.js", "../../d3-scale-chromatic/src/diverging/PRGn.js", "../../d3-scale-chromatic/src/diverging/PiYG.js", "../../d3-scale-chromatic/src/diverging/PuOr.js", "../../d3-scale-chromatic/src/diverging/RdBu.js", "../../d3-scale-chromatic/src/diverging/RdGy.js", "../../d3-scale-chromatic/src/diverging/RdYlBu.js", "../../d3-scale-chromatic/src/diverging/RdYlGn.js", "../../d3-scale-chromatic/src/diverging/Spectral.js", "../../d3-scale-chromatic/src/sequential-multi/BuGn.js", "../../d3-scale-chromatic/src/sequential-multi/BuPu.js", "../../d3-scale-chromatic/src/sequential-multi/GnBu.js", "../../d3-scale-chromatic/src/sequential-multi/OrRd.js", "../../d3-scale-chromatic/src/sequential-multi/PuBuGn.js", "../../d3-scale-chromatic/src/sequential-multi/PuBu.js", "../../d3-scale-chromatic/src/sequential-multi/PuRd.js", "../../d3-scale-chromatic/src/sequential-multi/RdPu.js", "../../d3-scale-chromatic/src/sequential-multi/YlGnBu.js", "../../d3-scale-chromatic/src/sequential-multi/YlGn.js", "../../d3-scale-chromatic/src/sequential-multi/YlOrBr.js", "../../d3-scale-chromatic/src/sequential-multi/YlOrRd.js", "../../d3-scale-chromatic/src/sequential-single/Blues.js", "../../d3-scale-chromatic/src/sequential-single/Greens.js", "../../d3-scale-chromatic/src/sequential-single/Greys.js", "../../d3-scale-chromatic/src/sequential-single/Purples.js", "../../d3-scale-chromatic/src/sequential-single/Reds.js", "../../d3-scale-chromatic/src/sequential-single/Oranges.js", "../../d3-scale-chromatic/src/sequential-multi/cividis.js", "../../d3-scale-chromatic/src/sequential-multi/cubehelix.js", "../../d3-scale-chromatic/src/sequential-multi/rainbow.js", "../../d3-scale-chromatic/src/sequential-multi/sinebow.js", "../../d3-scale-chromatic/src/sequential-multi/turbo.js", "../../d3-scale-chromatic/src/sequential-multi/viridis.js"], "sourcesContent": ["function streamGeometry(geometry, stream) {\n  if (geometry && streamGeometryType.hasOwnProperty(geometry.type)) {\n    streamGeometryType[geometry.type](geometry, stream);\n  }\n}\n\nvar streamObjectType = {\n  Feature: function(object, stream) {\n    streamGeometry(object.geometry, stream);\n  },\n  FeatureCollection: function(object, stream) {\n    var features = object.features, i = -1, n = features.length;\n    while (++i < n) streamGeometry(features[i].geometry, stream);\n  }\n};\n\nvar streamGeometryType = {\n  Sphere: function(object, stream) {\n    stream.sphere();\n  },\n  Point: function(object, stream) {\n    object = object.coordinates;\n    stream.point(object[0], object[1], object[2]);\n  },\n  MultiPoint: function(object, stream) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) object = coordinates[i], stream.point(object[0], object[1], object[2]);\n  },\n  LineString: function(object, stream) {\n    streamLine(object.coordinates, stream, 0);\n  },\n  MultiLineString: function(object, stream) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) streamLine(coordinates[i], stream, 0);\n  },\n  Polygon: function(object, stream) {\n    streamPolygon(object.coordinates, stream);\n  },\n  MultiPolygon: function(object, stream) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) streamPolygon(coordinates[i], stream);\n  },\n  GeometryCollection: function(object, stream) {\n    var geometries = object.geometries, i = -1, n = geometries.length;\n    while (++i < n) streamGeometry(geometries[i], stream);\n  }\n};\n\nfunction streamLine(coordinates, stream, closed) {\n  var i = -1, n = coordinates.length - closed, coordinate;\n  stream.lineStart();\n  while (++i < n) coordinate = coordinates[i], stream.point(coordinate[0], coordinate[1], coordinate[2]);\n  stream.lineEnd();\n}\n\nfunction streamPolygon(coordinates, stream) {\n  var i = -1, n = coordinates.length;\n  stream.polygonStart();\n  while (++i < n) streamLine(coordinates[i], stream, 1);\n  stream.polygonEnd();\n}\n\nexport default function(object, stream) {\n  if (object && streamObjectType.hasOwnProperty(object.type)) {\n    streamObjectType[object.type](object, stream);\n  } else {\n    streamGeometry(object, stream);\n  }\n}\n", "export var epsilon = 1e-6;\nexport var epsilon2 = 1e-12;\nexport var pi = Math.PI;\nexport var halfPi = pi / 2;\nexport var quarterPi = pi / 4;\nexport var tau = pi * 2;\n\nexport var degrees = 180 / pi;\nexport var radians = pi / 180;\n\nexport var abs = Math.abs;\nexport var atan = Math.atan;\nexport var atan2 = Math.atan2;\nexport var cos = Math.cos;\nexport var ceil = Math.ceil;\nexport var exp = Math.exp;\nexport var floor = Math.floor;\nexport var hypot = Math.hypot;\nexport var log = Math.log;\nexport var pow = Math.pow;\nexport var sin = Math.sin;\nexport var sign = Math.sign || function(x) { return x > 0 ? 1 : x < 0 ? -1 : 0; };\nexport var sqrt = Math.sqrt;\nexport var tan = Math.tan;\n\nexport function acos(x) {\n  return x > 1 ? 0 : x < -1 ? pi : Math.acos(x);\n}\n\nexport function asin(x) {\n  return x > 1 ? halfPi : x < -1 ? -halfPi : Math.asin(x);\n}\n\nexport function haversin(x) {\n  return (x = sin(x / 2)) * x;\n}\n", "export default function noop() {}\n", "import {Adder} from \"d3-array\";\nimport {atan2, cos, quarterPi, radians, sin, tau} from \"./math.js\";\nimport noop from \"./noop.js\";\nimport stream from \"./stream.js\";\n\nexport var areaRingSum = new Adder();\n\n// hello?\n\nvar areaSum = new Adder(),\n    lambda00,\n    phi00,\n    lambda0,\n    cosPhi0,\n    sinPhi0;\n\nexport var areaStream = {\n  point: noop,\n  lineStart: noop,\n  lineEnd: noop,\n  polygonStart: function() {\n    areaRingSum = new Adder();\n    areaStream.lineStart = areaRingStart;\n    areaStream.lineEnd = areaRingEnd;\n  },\n  polygonEnd: function() {\n    var areaRing = +areaRingSum;\n    areaSum.add(areaRing < 0 ? tau + areaRing : areaRing);\n    this.lineStart = this.lineEnd = this.point = noop;\n  },\n  sphere: function() {\n    areaSum.add(tau);\n  }\n};\n\nfunction areaRingStart() {\n  areaStream.point = areaPointFirst;\n}\n\nfunction areaRingEnd() {\n  areaPoint(lambda00, phi00);\n}\n\nfunction areaPointFirst(lambda, phi) {\n  areaStream.point = areaPoint;\n  lambda00 = lambda, phi00 = phi;\n  lambda *= radians, phi *= radians;\n  lambda0 = lambda, cosPhi0 = cos(phi = phi / 2 + quarterPi), sinPhi0 = sin(phi);\n}\n\nfunction areaPoint(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  phi = phi / 2 + quarterPi; // half the angular distance from south pole\n\n  // Spherical excess E for a spherical triangle with vertices: south pole,\n  // previous point, current point.  Uses a formula derived from Cagnoli’s\n  // theorem.  See Todhunter, Spherical Trig. (1871), Sec. 103, Eq. (2).\n  var dLambda = lambda - lambda0,\n      sdLambda = dLambda >= 0 ? 1 : -1,\n      adLambda = sdLambda * dLambda,\n      cosPhi = cos(phi),\n      sinPhi = sin(phi),\n      k = sinPhi0 * sinPhi,\n      u = cosPhi0 * cosPhi + k * cos(adLambda),\n      v = k * sdLambda * sin(adLambda);\n  areaRingSum.add(atan2(v, u));\n\n  // Advance the previous points.\n  lambda0 = lambda, cosPhi0 = cosPhi, sinPhi0 = sinPhi;\n}\n\nexport default function(object) {\n  areaSum = new Adder();\n  stream(object, areaStream);\n  return areaSum * 2;\n}\n", "import {asin, atan2, cos, sin, sqrt} from \"./math.js\";\n\nexport function spherical(cartesian) {\n  return [atan2(cartesian[1], cartesian[0]), asin(cartesian[2])];\n}\n\nexport function cartesian(spherical) {\n  var lambda = spherical[0], phi = spherical[1], cosPhi = cos(phi);\n  return [cosPhi * cos(lambda), cosPhi * sin(lambda), sin(phi)];\n}\n\nexport function cartesianDot(a, b) {\n  return a[0] * b[0] + a[1] * b[1] + a[2] * b[2];\n}\n\nexport function cartesianCross(a, b) {\n  return [a[1] * b[2] - a[2] * b[1], a[2] * b[0] - a[0] * b[2], a[0] * b[1] - a[1] * b[0]];\n}\n\n// TODO return a\nexport function cartesianAddInPlace(a, b) {\n  a[0] += b[0], a[1] += b[1], a[2] += b[2];\n}\n\nexport function cartesianScale(vector, k) {\n  return [vector[0] * k, vector[1] * k, vector[2] * k];\n}\n\n// TODO return d\nexport function cartesianNormalizeInPlace(d) {\n  var l = sqrt(d[0] * d[0] + d[1] * d[1] + d[2] * d[2]);\n  d[0] /= l, d[1] /= l, d[2] /= l;\n}\n", "import {Adder} from \"d3-array\";\nimport {areaStream, areaRingSum} from \"./area.js\";\nimport {cartesian, cartesianCross, cartesianNormalizeInPlace, spherical} from \"./cartesian.js\";\nimport {abs, degrees, epsilon, radians} from \"./math.js\";\nimport stream from \"./stream.js\";\n\nvar lambda0, phi0, lambda1, phi1, // bounds\n    lambda2, // previous lambda-coordinate\n    lambda00, phi00, // first point\n    p0, // previous 3D point\n    deltaSum,\n    ranges,\n    range;\n\nvar boundsStream = {\n  point: boundsPoint,\n  lineStart: boundsLineStart,\n  lineEnd: boundsLineEnd,\n  polygonStart: function() {\n    boundsStream.point = boundsRingPoint;\n    boundsStream.lineStart = boundsRingStart;\n    boundsStream.lineEnd = boundsRingEnd;\n    deltaSum = new Adder();\n    areaStream.polygonStart();\n  },\n  polygonEnd: function() {\n    areaStream.polygonEnd();\n    boundsStream.point = boundsPoint;\n    boundsStream.lineStart = boundsLineStart;\n    boundsStream.lineEnd = boundsLineEnd;\n    if (areaRingSum < 0) lambda0 = -(lambda1 = 180), phi0 = -(phi1 = 90);\n    else if (deltaSum > epsilon) phi1 = 90;\n    else if (deltaSum < -epsilon) phi0 = -90;\n    range[0] = lambda0, range[1] = lambda1;\n  },\n  sphere: function() {\n    lambda0 = -(lambda1 = 180), phi0 = -(phi1 = 90);\n  }\n};\n\nfunction boundsPoint(lambda, phi) {\n  ranges.push(range = [lambda0 = lambda, lambda1 = lambda]);\n  if (phi < phi0) phi0 = phi;\n  if (phi > phi1) phi1 = phi;\n}\n\nfunction linePoint(lambda, phi) {\n  var p = cartesian([lambda * radians, phi * radians]);\n  if (p0) {\n    var normal = cartesianCross(p0, p),\n        equatorial = [normal[1], -normal[0], 0],\n        inflection = cartesianCross(equatorial, normal);\n    cartesianNormalizeInPlace(inflection);\n    inflection = spherical(inflection);\n    var delta = lambda - lambda2,\n        sign = delta > 0 ? 1 : -1,\n        lambdai = inflection[0] * degrees * sign,\n        phii,\n        antimeridian = abs(delta) > 180;\n    if (antimeridian ^ (sign * lambda2 < lambdai && lambdai < sign * lambda)) {\n      phii = inflection[1] * degrees;\n      if (phii > phi1) phi1 = phii;\n    } else if (lambdai = (lambdai + 360) % 360 - 180, antimeridian ^ (sign * lambda2 < lambdai && lambdai < sign * lambda)) {\n      phii = -inflection[1] * degrees;\n      if (phii < phi0) phi0 = phii;\n    } else {\n      if (phi < phi0) phi0 = phi;\n      if (phi > phi1) phi1 = phi;\n    }\n    if (antimeridian) {\n      if (lambda < lambda2) {\n        if (angle(lambda0, lambda) > angle(lambda0, lambda1)) lambda1 = lambda;\n      } else {\n        if (angle(lambda, lambda1) > angle(lambda0, lambda1)) lambda0 = lambda;\n      }\n    } else {\n      if (lambda1 >= lambda0) {\n        if (lambda < lambda0) lambda0 = lambda;\n        if (lambda > lambda1) lambda1 = lambda;\n      } else {\n        if (lambda > lambda2) {\n          if (angle(lambda0, lambda) > angle(lambda0, lambda1)) lambda1 = lambda;\n        } else {\n          if (angle(lambda, lambda1) > angle(lambda0, lambda1)) lambda0 = lambda;\n        }\n      }\n    }\n  } else {\n    ranges.push(range = [lambda0 = lambda, lambda1 = lambda]);\n  }\n  if (phi < phi0) phi0 = phi;\n  if (phi > phi1) phi1 = phi;\n  p0 = p, lambda2 = lambda;\n}\n\nfunction boundsLineStart() {\n  boundsStream.point = linePoint;\n}\n\nfunction boundsLineEnd() {\n  range[0] = lambda0, range[1] = lambda1;\n  boundsStream.point = boundsPoint;\n  p0 = null;\n}\n\nfunction boundsRingPoint(lambda, phi) {\n  if (p0) {\n    var delta = lambda - lambda2;\n    deltaSum.add(abs(delta) > 180 ? delta + (delta > 0 ? 360 : -360) : delta);\n  } else {\n    lambda00 = lambda, phi00 = phi;\n  }\n  areaStream.point(lambda, phi);\n  linePoint(lambda, phi);\n}\n\nfunction boundsRingStart() {\n  areaStream.lineStart();\n}\n\nfunction boundsRingEnd() {\n  boundsRingPoint(lambda00, phi00);\n  areaStream.lineEnd();\n  if (abs(deltaSum) > epsilon) lambda0 = -(lambda1 = 180);\n  range[0] = lambda0, range[1] = lambda1;\n  p0 = null;\n}\n\n// Finds the left-right distance between two longitudes.\n// This is almost the same as (lambda1 - lambda0 + 360°) % 360°, except that we want\n// the distance between ±180° to be 360°.\nfunction angle(lambda0, lambda1) {\n  return (lambda1 -= lambda0) < 0 ? lambda1 + 360 : lambda1;\n}\n\nfunction rangeCompare(a, b) {\n  return a[0] - b[0];\n}\n\nfunction rangeContains(range, x) {\n  return range[0] <= range[1] ? range[0] <= x && x <= range[1] : x < range[0] || range[1] < x;\n}\n\nexport default function(feature) {\n  var i, n, a, b, merged, deltaMax, delta;\n\n  phi1 = lambda1 = -(lambda0 = phi0 = Infinity);\n  ranges = [];\n  stream(feature, boundsStream);\n\n  // First, sort ranges by their minimum longitudes.\n  if (n = ranges.length) {\n    ranges.sort(rangeCompare);\n\n    // Then, merge any ranges that overlap.\n    for (i = 1, a = ranges[0], merged = [a]; i < n; ++i) {\n      b = ranges[i];\n      if (rangeContains(a, b[0]) || rangeContains(a, b[1])) {\n        if (angle(a[0], b[1]) > angle(a[0], a[1])) a[1] = b[1];\n        if (angle(b[0], a[1]) > angle(a[0], a[1])) a[0] = b[0];\n      } else {\n        merged.push(a = b);\n      }\n    }\n\n    // Finally, find the largest gap between the merged ranges.\n    // The final bounding box will be the inverse of this gap.\n    for (deltaMax = -Infinity, n = merged.length - 1, i = 0, a = merged[n]; i <= n; a = b, ++i) {\n      b = merged[i];\n      if ((delta = angle(a[1], b[0])) > deltaMax) deltaMax = delta, lambda0 = b[0], lambda1 = a[1];\n    }\n  }\n\n  ranges = range = null;\n\n  return lambda0 === Infinity || phi0 === Infinity\n      ? [[NaN, NaN], [NaN, NaN]]\n      : [[lambda0, phi0], [lambda1, phi1]];\n}\n", "import {Adder} from \"d3-array\";\nimport {asin, atan2, cos, degrees, epsilon, epsilon2, hypot, radians, sin, sqrt} from \"./math.js\";\nimport noop from \"./noop.js\";\nimport stream from \"./stream.js\";\n\nvar W0, W1,\n    X0, Y0, Z0,\n    X1, Y1, Z1,\n    X2, Y2, Z2,\n    lambda00, phi00, // first point\n    x0, y0, z0; // previous point\n\nvar centroidStream = {\n  sphere: noop,\n  point: centroidPoint,\n  lineStart: centroidLineStart,\n  lineEnd: centroidLineEnd,\n  polygonStart: function() {\n    centroidStream.lineStart = centroidRingStart;\n    centroidStream.lineEnd = centroidRingEnd;\n  },\n  polygonEnd: function() {\n    centroidStream.lineStart = centroidLineStart;\n    centroidStream.lineEnd = centroidLineEnd;\n  }\n};\n\n// Arithmetic mean of Cartesian vectors.\nfunction centroidPoint(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  var cosPhi = cos(phi);\n  centroidPointCartesian(cosPhi * cos(lambda), cosPhi * sin(lambda), sin(phi));\n}\n\nfunction centroidPointCartesian(x, y, z) {\n  ++W0;\n  X0 += (x - X0) / W0;\n  Y0 += (y - Y0) / W0;\n  Z0 += (z - Z0) / W0;\n}\n\nfunction centroidLineStart() {\n  centroidStream.point = centroidLinePointFirst;\n}\n\nfunction centroidLinePointFirst(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  var cosPhi = cos(phi);\n  x0 = cosPhi * cos(lambda);\n  y0 = cosPhi * sin(lambda);\n  z0 = sin(phi);\n  centroidStream.point = centroidLinePoint;\n  centroidPointCartesian(x0, y0, z0);\n}\n\nfunction centroidLinePoint(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  var cosPhi = cos(phi),\n      x = cosPhi * cos(lambda),\n      y = cosPhi * sin(lambda),\n      z = sin(phi),\n      w = atan2(sqrt((w = y0 * z - z0 * y) * w + (w = z0 * x - x0 * z) * w + (w = x0 * y - y0 * x) * w), x0 * x + y0 * y + z0 * z);\n  W1 += w;\n  X1 += w * (x0 + (x0 = x));\n  Y1 += w * (y0 + (y0 = y));\n  Z1 += w * (z0 + (z0 = z));\n  centroidPointCartesian(x0, y0, z0);\n}\n\nfunction centroidLineEnd() {\n  centroidStream.point = centroidPoint;\n}\n\n// See J. E. Brock, The Inertia Tensor for a Spherical Triangle,\n// J. Applied Mechanics 42, 239 (1975).\nfunction centroidRingStart() {\n  centroidStream.point = centroidRingPointFirst;\n}\n\nfunction centroidRingEnd() {\n  centroidRingPoint(lambda00, phi00);\n  centroidStream.point = centroidPoint;\n}\n\nfunction centroidRingPointFirst(lambda, phi) {\n  lambda00 = lambda, phi00 = phi;\n  lambda *= radians, phi *= radians;\n  centroidStream.point = centroidRingPoint;\n  var cosPhi = cos(phi);\n  x0 = cosPhi * cos(lambda);\n  y0 = cosPhi * sin(lambda);\n  z0 = sin(phi);\n  centroidPointCartesian(x0, y0, z0);\n}\n\nfunction centroidRingPoint(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  var cosPhi = cos(phi),\n      x = cosPhi * cos(lambda),\n      y = cosPhi * sin(lambda),\n      z = sin(phi),\n      cx = y0 * z - z0 * y,\n      cy = z0 * x - x0 * z,\n      cz = x0 * y - y0 * x,\n      m = hypot(cx, cy, cz),\n      w = asin(m), // line weight = angle\n      v = m && -w / m; // area weight multiplier\n  X2.add(v * cx);\n  Y2.add(v * cy);\n  Z2.add(v * cz);\n  W1 += w;\n  X1 += w * (x0 + (x0 = x));\n  Y1 += w * (y0 + (y0 = y));\n  Z1 += w * (z0 + (z0 = z));\n  centroidPointCartesian(x0, y0, z0);\n}\n\nexport default function(object) {\n  W0 = W1 =\n  X0 = Y0 = Z0 =\n  X1 = Y1 = Z1 = 0;\n  X2 = new Adder();\n  Y2 = new Adder();\n  Z2 = new Adder();\n  stream(object, centroidStream);\n\n  var x = +X2,\n      y = +Y2,\n      z = +Z2,\n      m = hypot(x, y, z);\n\n  // If the area-weighted ccentroid is undefined, fall back to length-weighted ccentroid.\n  if (m < epsilon2) {\n    x = X1, y = Y1, z = Z1;\n    // If the feature has zero length, fall back to arithmetic mean of point vectors.\n    if (W1 < epsilon) x = X0, y = Y0, z = Z0;\n    m = hypot(x, y, z);\n    // If the feature still has an undefined ccentroid, then return.\n    if (m < epsilon2) return [NaN, NaN];\n  }\n\n  return [atan2(y, x) * degrees, asin(z / m) * degrees];\n}\n", "export default function(a, b) {\n\n  function compose(x, y) {\n    return x = a(x, y), b(x[0], x[1]);\n  }\n\n  if (a.invert && b.invert) compose.invert = function(x, y) {\n    return x = b.invert(x, y), x && a.invert(x[0], x[1]);\n  };\n\n  return compose;\n}\n", "import compose from \"./compose.js\";\nimport {abs, asin, atan2, cos, degrees, pi, radians, sin, tau} from \"./math.js\";\n\nfunction rotationIdentity(lambda, phi) {\n  if (abs(lambda) > pi) lambda -= Math.round(lambda / tau) * tau;\n  return [lambda, phi];\n}\n\nrotationIdentity.invert = rotationIdentity;\n\nexport function rotateRadians(deltaLambda, deltaPhi, deltaGamma) {\n  return (deltaLambda %= tau) ? (deltaPhi || deltaGamma ? compose(rotationLambda(deltaLambda), rotationPhiGamma(deltaPhi, deltaGamma))\n    : rotationLambda(deltaLambda))\n    : (deltaPhi || deltaGamma ? rotationPhiGamma(deltaPhi, deltaGamma)\n    : rotationIdentity);\n}\n\nfunction forwardRotationLambda(deltaLambda) {\n  return function(lambda, phi) {\n    lambda += deltaLambda;\n    if (abs(lambda) > pi) lambda -= Math.round(lambda / tau) * tau;\n    return [lambda, phi];\n  };\n}\n\nfunction rotationLambda(deltaLambda) {\n  var rotation = forwardRotationLambda(deltaLambda);\n  rotation.invert = forwardRotationLambda(-deltaLambda);\n  return rotation;\n}\n\nfunction rotationPhiGamma(deltaPhi, deltaGamma) {\n  var cosDeltaPhi = cos(deltaPhi),\n      sinDeltaPhi = sin(deltaPhi),\n      cosDeltaGamma = cos(deltaGamma),\n      sinDeltaGamma = sin(deltaGamma);\n\n  function rotation(lambda, phi) {\n    var cosPhi = cos(phi),\n        x = cos(lambda) * cosPhi,\n        y = sin(lambda) * cosPhi,\n        z = sin(phi),\n        k = z * cosDeltaPhi + x * sinDeltaPhi;\n    return [\n      atan2(y * cosDeltaGamma - k * sinDeltaGamma, x * cosDeltaPhi - z * sinDeltaPhi),\n      asin(k * cosDeltaGamma + y * sinDeltaGamma)\n    ];\n  }\n\n  rotation.invert = function(lambda, phi) {\n    var cosPhi = cos(phi),\n        x = cos(lambda) * cosPhi,\n        y = sin(lambda) * cosPhi,\n        z = sin(phi),\n        k = z * cosDeltaGamma - y * sinDeltaGamma;\n    return [\n      atan2(y * cosDeltaGamma + z * sinDeltaGamma, x * cosDeltaPhi + k * sinDeltaPhi),\n      asin(k * cosDeltaPhi - x * sinDeltaPhi)\n    ];\n  };\n\n  return rotation;\n}\n\nexport default function(rotate) {\n  rotate = rotateRadians(rotate[0] * radians, rotate[1] * radians, rotate.length > 2 ? rotate[2] * radians : 0);\n\n  function forward(coordinates) {\n    coordinates = rotate(coordinates[0] * radians, coordinates[1] * radians);\n    return coordinates[0] *= degrees, coordinates[1] *= degrees, coordinates;\n  }\n\n  forward.invert = function(coordinates) {\n    coordinates = rotate.invert(coordinates[0] * radians, coordinates[1] * radians);\n    return coordinates[0] *= degrees, coordinates[1] *= degrees, coordinates;\n  };\n\n  return forward;\n}\n", "export default function(x) {\n  return function() {\n    return x;\n  };\n}\n", "import {cartesian, cartesianNormalizeInPlace, spherical} from \"./cartesian.js\";\nimport constant from \"./constant.js\";\nimport {acos, cos, degrees, epsilon, radians, sin, tau} from \"./math.js\";\nimport {rotateRadians} from \"./rotation.js\";\n\n// Generates a circle centered at [0°, 0°], with a given radius and precision.\nexport function circleStream(stream, radius, delta, direction, t0, t1) {\n  if (!delta) return;\n  var cosRadius = cos(radius),\n      sinRadius = sin(radius),\n      step = direction * delta;\n  if (t0 == null) {\n    t0 = radius + direction * tau;\n    t1 = radius - step / 2;\n  } else {\n    t0 = circleRadius(cosRadius, t0);\n    t1 = circleRadius(cosRadius, t1);\n    if (direction > 0 ? t0 < t1 : t0 > t1) t0 += direction * tau;\n  }\n  for (var point, t = t0; direction > 0 ? t > t1 : t < t1; t -= step) {\n    point = spherical([cosRadius, -sinRadius * cos(t), -sinRadius * sin(t)]);\n    stream.point(point[0], point[1]);\n  }\n}\n\n// Returns the signed angle of a cartesian point relative to [cosRadius, 0, 0].\nfunction circleRadius(cosRadius, point) {\n  point = cartesian(point), point[0] -= cosRadius;\n  cartesianNormalizeInPlace(point);\n  var radius = acos(-point[1]);\n  return ((-point[2] < 0 ? -radius : radius) + tau - epsilon) % tau;\n}\n\nexport default function() {\n  var center = constant([0, 0]),\n      radius = constant(90),\n      precision = constant(2),\n      ring,\n      rotate,\n      stream = {point: point};\n\n  function point(x, y) {\n    ring.push(x = rotate(x, y));\n    x[0] *= degrees, x[1] *= degrees;\n  }\n\n  function circle() {\n    var c = center.apply(this, arguments),\n        r = radius.apply(this, arguments) * radians,\n        p = precision.apply(this, arguments) * radians;\n    ring = [];\n    rotate = rotateRadians(-c[0] * radians, -c[1] * radians, 0).invert;\n    circleStream(stream, r, p, 1);\n    c = {type: \"Polygon\", coordinates: [ring]};\n    ring = rotate = null;\n    return c;\n  }\n\n  circle.center = function(_) {\n    return arguments.length ? (center = typeof _ === \"function\" ? _ : constant([+_[0], +_[1]]), circle) : center;\n  };\n\n  circle.radius = function(_) {\n    return arguments.length ? (radius = typeof _ === \"function\" ? _ : constant(+_), circle) : radius;\n  };\n\n  circle.precision = function(_) {\n    return arguments.length ? (precision = typeof _ === \"function\" ? _ : constant(+_), circle) : precision;\n  };\n\n  return circle;\n}\n", "import noop from \"../noop.js\";\n\nexport default function() {\n  var lines = [],\n      line;\n  return {\n    point: function(x, y, m) {\n      line.push([x, y, m]);\n    },\n    lineStart: function() {\n      lines.push(line = []);\n    },\n    lineEnd: noop,\n    rejoin: function() {\n      if (lines.length > 1) lines.push(lines.pop().concat(lines.shift()));\n    },\n    result: function() {\n      var result = lines;\n      lines = [];\n      line = null;\n      return result;\n    }\n  };\n}\n", "import {abs, epsilon} from \"./math.js\";\n\nexport default function(a, b) {\n  return abs(a[0] - b[0]) < epsilon && abs(a[1] - b[1]) < epsilon;\n}\n", "import pointEqual from \"../pointEqual.js\";\nimport {epsilon} from \"../math.js\";\n\nfunction Intersection(point, points, other, entry) {\n  this.x = point;\n  this.z = points;\n  this.o = other; // another intersection\n  this.e = entry; // is an entry?\n  this.v = false; // visited\n  this.n = this.p = null; // next & previous\n}\n\n// A generalized polygon clipping algorithm: given a polygon that has been cut\n// into its visible line segments, and rejoins the segments by interpolating\n// along the clip edge.\nexport default function(segments, compareIntersection, startInside, interpolate, stream) {\n  var subject = [],\n      clip = [],\n      i,\n      n;\n\n  segments.forEach(function(segment) {\n    if ((n = segment.length - 1) <= 0) return;\n    var n, p0 = segment[0], p1 = segment[n], x;\n\n    if (pointEqual(p0, p1)) {\n      if (!p0[2] && !p1[2]) {\n        stream.lineStart();\n        for (i = 0; i < n; ++i) stream.point((p0 = segment[i])[0], p0[1]);\n        stream.lineEnd();\n        return;\n      }\n      // handle degenerate cases by moving the point\n      p1[0] += 2 * epsilon;\n    }\n\n    subject.push(x = new Intersection(p0, segment, null, true));\n    clip.push(x.o = new Intersection(p0, null, x, false));\n    subject.push(x = new Intersection(p1, segment, null, false));\n    clip.push(x.o = new Intersection(p1, null, x, true));\n  });\n\n  if (!subject.length) return;\n\n  clip.sort(compareIntersection);\n  link(subject);\n  link(clip);\n\n  for (i = 0, n = clip.length; i < n; ++i) {\n    clip[i].e = startInside = !startInside;\n  }\n\n  var start = subject[0],\n      points,\n      point;\n\n  while (1) {\n    // Find first unvisited intersection.\n    var current = start,\n        isSubject = true;\n    while (current.v) if ((current = current.n) === start) return;\n    points = current.z;\n    stream.lineStart();\n    do {\n      current.v = current.o.v = true;\n      if (current.e) {\n        if (isSubject) {\n          for (i = 0, n = points.length; i < n; ++i) stream.point((point = points[i])[0], point[1]);\n        } else {\n          interpolate(current.x, current.n.x, 1, stream);\n        }\n        current = current.n;\n      } else {\n        if (isSubject) {\n          points = current.p.z;\n          for (i = points.length - 1; i >= 0; --i) stream.point((point = points[i])[0], point[1]);\n        } else {\n          interpolate(current.x, current.p.x, -1, stream);\n        }\n        current = current.p;\n      }\n      current = current.o;\n      points = current.z;\n      isSubject = !isSubject;\n    } while (!current.v);\n    stream.lineEnd();\n  }\n}\n\nfunction link(array) {\n  if (!(n = array.length)) return;\n  var n,\n      i = 0,\n      a = array[0],\n      b;\n  while (++i < n) {\n    a.n = b = array[i];\n    b.p = a;\n    a = b;\n  }\n  a.n = b = array[0];\n  b.p = a;\n}\n", "import {<PERSON>der} from \"d3-array\";\nimport {cartesian, cartesianCross, cartesianNormalizeInPlace} from \"./cartesian.js\";\nimport {abs, asin, atan2, cos, epsilon, epsilon2, halfPi, pi, quarterPi, sign, sin, tau} from \"./math.js\";\n\nfunction longitude(point) {\n  return abs(point[0]) <= pi ? point[0] : sign(point[0]) * ((abs(point[0]) + pi) % tau - pi);\n}\n\nexport default function(polygon, point) {\n  var lambda = longitude(point),\n      phi = point[1],\n      sinPhi = sin(phi),\n      normal = [sin(lambda), -cos(lambda), 0],\n      angle = 0,\n      winding = 0;\n\n  var sum = new Adder();\n\n  if (sinPhi === 1) phi = halfPi + epsilon;\n  else if (sinPhi === -1) phi = -halfPi - epsilon;\n\n  for (var i = 0, n = polygon.length; i < n; ++i) {\n    if (!(m = (ring = polygon[i]).length)) continue;\n    var ring,\n        m,\n        point0 = ring[m - 1],\n        lambda0 = longitude(point0),\n        phi0 = point0[1] / 2 + quarterPi,\n        sinPhi0 = sin(phi0),\n        cosPhi0 = cos(phi0);\n\n    for (var j = 0; j < m; ++j, lambda0 = lambda1, sinPhi0 = sinPhi1, cosPhi0 = cosPhi1, point0 = point1) {\n      var point1 = ring[j],\n          lambda1 = longitude(point1),\n          phi1 = point1[1] / 2 + quarterPi,\n          sinPhi1 = sin(phi1),\n          cosPhi1 = cos(phi1),\n          delta = lambda1 - lambda0,\n          sign = delta >= 0 ? 1 : -1,\n          absDelta = sign * delta,\n          antimeridian = absDelta > pi,\n          k = sinPhi0 * sinPhi1;\n\n      sum.add(atan2(k * sign * sin(absDelta), cosPhi0 * cosPhi1 + k * cos(absDelta)));\n      angle += antimeridian ? delta + sign * tau : delta;\n\n      // Are the longitudes either side of the point’s meridian (lambda),\n      // and are the latitudes smaller than the parallel (phi)?\n      if (antimeridian ^ lambda0 >= lambda ^ lambda1 >= lambda) {\n        var arc = cartesianCross(cartesian(point0), cartesian(point1));\n        cartesianNormalizeInPlace(arc);\n        var intersection = cartesianCross(normal, arc);\n        cartesianNormalizeInPlace(intersection);\n        var phiArc = (antimeridian ^ delta >= 0 ? -1 : 1) * asin(intersection[2]);\n        if (phi > phiArc || phi === phiArc && (arc[0] || arc[1])) {\n          winding += antimeridian ^ delta >= 0 ? 1 : -1;\n        }\n      }\n    }\n  }\n\n  // First, determine whether the South pole is inside or outside:\n  //\n  // It is inside if:\n  // * the polygon winds around it in a clockwise direction.\n  // * the polygon does not (cumulatively) wind around it, but has a negative\n  //   (counter-clockwise) area.\n  //\n  // Second, count the (signed) number of times a segment crosses a lambda\n  // from the point to the South pole.  If it is zero, then the point is the\n  // same side as the South pole.\n\n  return (angle < -epsilon || angle < epsilon && sum < -epsilon2) ^ (winding & 1);\n}\n", "import clipBuffer from \"./buffer.js\";\nimport clipRejoin from \"./rejoin.js\";\nimport {epsilon, halfPi} from \"../math.js\";\nimport polygonContains from \"../polygonContains.js\";\nimport {merge} from \"d3-array\";\n\nexport default function(pointVisible, clipLine, interpolate, start) {\n  return function(sink) {\n    var line = clipLine(sink),\n        ringBuffer = clipBuffer(),\n        ringSink = clipLine(ringBuffer),\n        polygonStarted = false,\n        polygon,\n        segments,\n        ring;\n\n    var clip = {\n      point: point,\n      lineStart: lineStart,\n      lineEnd: lineEnd,\n      polygonStart: function() {\n        clip.point = pointRing;\n        clip.lineStart = ringStart;\n        clip.lineEnd = ringEnd;\n        segments = [];\n        polygon = [];\n      },\n      polygonEnd: function() {\n        clip.point = point;\n        clip.lineStart = lineStart;\n        clip.lineEnd = lineEnd;\n        segments = merge(segments);\n        var startInside = polygonContains(polygon, start);\n        if (segments.length) {\n          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n          clipRejoin(segments, compareIntersection, startInside, interpolate, sink);\n        } else if (startInside) {\n          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n          sink.lineStart();\n          interpolate(null, null, 1, sink);\n          sink.lineEnd();\n        }\n        if (polygonStarted) sink.polygonEnd(), polygonStarted = false;\n        segments = polygon = null;\n      },\n      sphere: function() {\n        sink.polygonStart();\n        sink.lineStart();\n        interpolate(null, null, 1, sink);\n        sink.lineEnd();\n        sink.polygonEnd();\n      }\n    };\n\n    function point(lambda, phi) {\n      if (pointVisible(lambda, phi)) sink.point(lambda, phi);\n    }\n\n    function pointLine(lambda, phi) {\n      line.point(lambda, phi);\n    }\n\n    function lineStart() {\n      clip.point = pointLine;\n      line.lineStart();\n    }\n\n    function lineEnd() {\n      clip.point = point;\n      line.lineEnd();\n    }\n\n    function pointRing(lambda, phi) {\n      ring.push([lambda, phi]);\n      ringSink.point(lambda, phi);\n    }\n\n    function ringStart() {\n      ringSink.lineStart();\n      ring = [];\n    }\n\n    function ringEnd() {\n      pointRing(ring[0][0], ring[0][1]);\n      ringSink.lineEnd();\n\n      var clean = ringSink.clean(),\n          ringSegments = ringBuffer.result(),\n          i, n = ringSegments.length, m,\n          segment,\n          point;\n\n      ring.pop();\n      polygon.push(ring);\n      ring = null;\n\n      if (!n) return;\n\n      // No intersections.\n      if (clean & 1) {\n        segment = ringSegments[0];\n        if ((m = segment.length - 1) > 0) {\n          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n          sink.lineStart();\n          for (i = 0; i < m; ++i) sink.point((point = segment[i])[0], point[1]);\n          sink.lineEnd();\n        }\n        return;\n      }\n\n      // Rejoin connected segments.\n      // TODO reuse ringBuffer.rejoin()?\n      if (n > 1 && clean & 2) ringSegments.push(ringSegments.pop().concat(ringSegments.shift()));\n\n      segments.push(ringSegments.filter(validSegment));\n    }\n\n    return clip;\n  };\n}\n\nfunction validSegment(segment) {\n  return segment.length > 1;\n}\n\n// Intersections are sorted along the clip edge. For both antimeridian cutting\n// and circle clipping, the same comparison is used.\nfunction compareIntersection(a, b) {\n  return ((a = a.x)[0] < 0 ? a[1] - halfPi - epsilon : halfPi - a[1])\n       - ((b = b.x)[0] < 0 ? b[1] - halfPi - epsilon : halfPi - b[1]);\n}\n", "import clip from \"./index.js\";\nimport {abs, atan, cos, epsilon, halfPi, pi, sin} from \"../math.js\";\n\nexport default clip(\n  function() { return true; },\n  clipAntimeridianLine,\n  clipAntimeridianInterpolate,\n  [-pi, -halfPi]\n);\n\n// Takes a line and cuts into visible segments. Return values: 0 - there were\n// intersections or the line was empty; 1 - no intersections; 2 - there were\n// intersections, and the first and last segments should be rejoined.\nfunction clipAntimeridianLine(stream) {\n  var lambda0 = NaN,\n      phi0 = NaN,\n      sign0 = NaN,\n      clean; // no intersections\n\n  return {\n    lineStart: function() {\n      stream.lineStart();\n      clean = 1;\n    },\n    point: function(lambda1, phi1) {\n      var sign1 = lambda1 > 0 ? pi : -pi,\n          delta = abs(lambda1 - lambda0);\n      if (abs(delta - pi) < epsilon) { // line crosses a pole\n        stream.point(lambda0, phi0 = (phi0 + phi1) / 2 > 0 ? halfPi : -halfPi);\n        stream.point(sign0, phi0);\n        stream.lineEnd();\n        stream.lineStart();\n        stream.point(sign1, phi0);\n        stream.point(lambda1, phi0);\n        clean = 0;\n      } else if (sign0 !== sign1 && delta >= pi) { // line crosses antimeridian\n        if (abs(lambda0 - sign0) < epsilon) lambda0 -= sign0 * epsilon; // handle degeneracies\n        if (abs(lambda1 - sign1) < epsilon) lambda1 -= sign1 * epsilon;\n        phi0 = clipAntimeridianIntersect(lambda0, phi0, lambda1, phi1);\n        stream.point(sign0, phi0);\n        stream.lineEnd();\n        stream.lineStart();\n        stream.point(sign1, phi0);\n        clean = 0;\n      }\n      stream.point(lambda0 = lambda1, phi0 = phi1);\n      sign0 = sign1;\n    },\n    lineEnd: function() {\n      stream.lineEnd();\n      lambda0 = phi0 = NaN;\n    },\n    clean: function() {\n      return 2 - clean; // if intersections, rejoin first and last segments\n    }\n  };\n}\n\nfunction clipAntimeridianIntersect(lambda0, phi0, lambda1, phi1) {\n  var cosPhi0,\n      cosPhi1,\n      sinLambda0Lambda1 = sin(lambda0 - lambda1);\n  return abs(sinLambda0Lambda1) > epsilon\n      ? atan((sin(phi0) * (cosPhi1 = cos(phi1)) * sin(lambda1)\n          - sin(phi1) * (cosPhi0 = cos(phi0)) * sin(lambda0))\n          / (cosPhi0 * cosPhi1 * sinLambda0Lambda1))\n      : (phi0 + phi1) / 2;\n}\n\nfunction clipAntimeridianInterpolate(from, to, direction, stream) {\n  var phi;\n  if (from == null) {\n    phi = direction * halfPi;\n    stream.point(-pi, phi);\n    stream.point(0, phi);\n    stream.point(pi, phi);\n    stream.point(pi, 0);\n    stream.point(pi, -phi);\n    stream.point(0, -phi);\n    stream.point(-pi, -phi);\n    stream.point(-pi, 0);\n    stream.point(-pi, phi);\n  } else if (abs(from[0] - to[0]) > epsilon) {\n    var lambda = from[0] < to[0] ? pi : -pi;\n    phi = direction * lambda / 2;\n    stream.point(-lambda, phi);\n    stream.point(0, phi);\n    stream.point(lambda, phi);\n  } else {\n    stream.point(to[0], to[1]);\n  }\n}\n", "import {cartesian, cartesianAddInPlace, cartesianCross, cartesianDot, cartesianScale, spherical} from \"../cartesian.js\";\nimport {circleStream} from \"../circle.js\";\nimport {abs, cos, epsilon, pi, radians, sqrt} from \"../math.js\";\nimport pointEqual from \"../pointEqual.js\";\nimport clip from \"./index.js\";\n\nexport default function(radius) {\n  var cr = cos(radius),\n      delta = 2 * radians,\n      smallRadius = cr > 0,\n      notHemisphere = abs(cr) > epsilon; // TODO optimise for this common case\n\n  function interpolate(from, to, direction, stream) {\n    circleStream(stream, radius, delta, direction, from, to);\n  }\n\n  function visible(lambda, phi) {\n    return cos(lambda) * cos(phi) > cr;\n  }\n\n  // Takes a line and cuts into visible segments. Return values used for polygon\n  // clipping: 0 - there were intersections or the line was empty; 1 - no\n  // intersections 2 - there were intersections, and the first and last segments\n  // should be rejoined.\n  function clipLine(stream) {\n    var point0, // previous point\n        c0, // code for previous point\n        v0, // visibility of previous point\n        v00, // visibility of first point\n        clean; // no intersections\n    return {\n      lineStart: function() {\n        v00 = v0 = false;\n        clean = 1;\n      },\n      point: function(lambda, phi) {\n        var point1 = [lambda, phi],\n            point2,\n            v = visible(lambda, phi),\n            c = smallRadius\n              ? v ? 0 : code(lambda, phi)\n              : v ? code(lambda + (lambda < 0 ? pi : -pi), phi) : 0;\n        if (!point0 && (v00 = v0 = v)) stream.lineStart();\n        if (v !== v0) {\n          point2 = intersect(point0, point1);\n          if (!point2 || pointEqual(point0, point2) || pointEqual(point1, point2))\n            point1[2] = 1;\n        }\n        if (v !== v0) {\n          clean = 0;\n          if (v) {\n            // outside going in\n            stream.lineStart();\n            point2 = intersect(point1, point0);\n            stream.point(point2[0], point2[1]);\n          } else {\n            // inside going out\n            point2 = intersect(point0, point1);\n            stream.point(point2[0], point2[1], 2);\n            stream.lineEnd();\n          }\n          point0 = point2;\n        } else if (notHemisphere && point0 && smallRadius ^ v) {\n          var t;\n          // If the codes for two points are different, or are both zero,\n          // and there this segment intersects with the small circle.\n          if (!(c & c0) && (t = intersect(point1, point0, true))) {\n            clean = 0;\n            if (smallRadius) {\n              stream.lineStart();\n              stream.point(t[0][0], t[0][1]);\n              stream.point(t[1][0], t[1][1]);\n              stream.lineEnd();\n            } else {\n              stream.point(t[1][0], t[1][1]);\n              stream.lineEnd();\n              stream.lineStart();\n              stream.point(t[0][0], t[0][1], 3);\n            }\n          }\n        }\n        if (v && (!point0 || !pointEqual(point0, point1))) {\n          stream.point(point1[0], point1[1]);\n        }\n        point0 = point1, v0 = v, c0 = c;\n      },\n      lineEnd: function() {\n        if (v0) stream.lineEnd();\n        point0 = null;\n      },\n      // Rejoin first and last segments if there were intersections and the first\n      // and last points were visible.\n      clean: function() {\n        return clean | ((v00 && v0) << 1);\n      }\n    };\n  }\n\n  // Intersects the great circle between a and b with the clip circle.\n  function intersect(a, b, two) {\n    var pa = cartesian(a),\n        pb = cartesian(b);\n\n    // We have two planes, n1.p = d1 and n2.p = d2.\n    // Find intersection line p(t) = c1 n1 + c2 n2 + t (n1 ⨯ n2).\n    var n1 = [1, 0, 0], // normal\n        n2 = cartesianCross(pa, pb),\n        n2n2 = cartesianDot(n2, n2),\n        n1n2 = n2[0], // cartesianDot(n1, n2),\n        determinant = n2n2 - n1n2 * n1n2;\n\n    // Two polar points.\n    if (!determinant) return !two && a;\n\n    var c1 =  cr * n2n2 / determinant,\n        c2 = -cr * n1n2 / determinant,\n        n1xn2 = cartesianCross(n1, n2),\n        A = cartesianScale(n1, c1),\n        B = cartesianScale(n2, c2);\n    cartesianAddInPlace(A, B);\n\n    // Solve |p(t)|^2 = 1.\n    var u = n1xn2,\n        w = cartesianDot(A, u),\n        uu = cartesianDot(u, u),\n        t2 = w * w - uu * (cartesianDot(A, A) - 1);\n\n    if (t2 < 0) return;\n\n    var t = sqrt(t2),\n        q = cartesianScale(u, (-w - t) / uu);\n    cartesianAddInPlace(q, A);\n    q = spherical(q);\n\n    if (!two) return q;\n\n    // Two intersection points.\n    var lambda0 = a[0],\n        lambda1 = b[0],\n        phi0 = a[1],\n        phi1 = b[1],\n        z;\n\n    if (lambda1 < lambda0) z = lambda0, lambda0 = lambda1, lambda1 = z;\n\n    var delta = lambda1 - lambda0,\n        polar = abs(delta - pi) < epsilon,\n        meridian = polar || delta < epsilon;\n\n    if (!polar && phi1 < phi0) z = phi0, phi0 = phi1, phi1 = z;\n\n    // Check that the first point is between a and b.\n    if (meridian\n        ? polar\n          ? phi0 + phi1 > 0 ^ q[1] < (abs(q[0] - lambda0) < epsilon ? phi0 : phi1)\n          : phi0 <= q[1] && q[1] <= phi1\n        : delta > pi ^ (lambda0 <= q[0] && q[0] <= lambda1)) {\n      var q1 = cartesianScale(u, (-w + t) / uu);\n      cartesianAddInPlace(q1, A);\n      return [q, spherical(q1)];\n    }\n  }\n\n  // Generates a 4-bit vector representing the location of a point relative to\n  // the small circle's bounding box.\n  function code(lambda, phi) {\n    var r = smallRadius ? radius : pi - radius,\n        code = 0;\n    if (lambda < -r) code |= 1; // left\n    else if (lambda > r) code |= 2; // right\n    if (phi < -r) code |= 4; // below\n    else if (phi > r) code |= 8; // above\n    return code;\n  }\n\n  return clip(visible, clipLine, interpolate, smallRadius ? [0, -radius] : [-pi, radius - pi]);\n}\n", "export default function(a, b, x0, y0, x1, y1) {\n  var ax = a[0],\n      ay = a[1],\n      bx = b[0],\n      by = b[1],\n      t0 = 0,\n      t1 = 1,\n      dx = bx - ax,\n      dy = by - ay,\n      r;\n\n  r = x0 - ax;\n  if (!dx && r > 0) return;\n  r /= dx;\n  if (dx < 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  } else if (dx > 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  }\n\n  r = x1 - ax;\n  if (!dx && r < 0) return;\n  r /= dx;\n  if (dx < 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  } else if (dx > 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  }\n\n  r = y0 - ay;\n  if (!dy && r > 0) return;\n  r /= dy;\n  if (dy < 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  } else if (dy > 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  }\n\n  r = y1 - ay;\n  if (!dy && r < 0) return;\n  r /= dy;\n  if (dy < 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  } else if (dy > 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  }\n\n  if (t0 > 0) a[0] = ax + t0 * dx, a[1] = ay + t0 * dy;\n  if (t1 < 1) b[0] = ax + t1 * dx, b[1] = ay + t1 * dy;\n  return true;\n}\n", "import {abs, epsilon} from \"../math.js\";\nimport clipBuffer from \"./buffer.js\";\nimport clipLine from \"./line.js\";\nimport clipRejoin from \"./rejoin.js\";\nimport {merge} from \"d3-array\";\n\nvar clipMax = 1e9, clipMin = -clipMax;\n\n// TODO Use d3-polygon’s polygonContains here for the ring check?\n// TODO Eliminate duplicate buffering in clipBuffer and polygon.push?\n\nexport default function clipRectangle(x0, y0, x1, y1) {\n\n  function visible(x, y) {\n    return x0 <= x && x <= x1 && y0 <= y && y <= y1;\n  }\n\n  function interpolate(from, to, direction, stream) {\n    var a = 0, a1 = 0;\n    if (from == null\n        || (a = corner(from, direction)) !== (a1 = corner(to, direction))\n        || comparePoint(from, to) < 0 ^ direction > 0) {\n      do stream.point(a === 0 || a === 3 ? x0 : x1, a > 1 ? y1 : y0);\n      while ((a = (a + direction + 4) % 4) !== a1);\n    } else {\n      stream.point(to[0], to[1]);\n    }\n  }\n\n  function corner(p, direction) {\n    return abs(p[0] - x0) < epsilon ? direction > 0 ? 0 : 3\n        : abs(p[0] - x1) < epsilon ? direction > 0 ? 2 : 1\n        : abs(p[1] - y0) < epsilon ? direction > 0 ? 1 : 0\n        : direction > 0 ? 3 : 2; // abs(p[1] - y1) < epsilon\n  }\n\n  function compareIntersection(a, b) {\n    return comparePoint(a.x, b.x);\n  }\n\n  function comparePoint(a, b) {\n    var ca = corner(a, 1),\n        cb = corner(b, 1);\n    return ca !== cb ? ca - cb\n        : ca === 0 ? b[1] - a[1]\n        : ca === 1 ? a[0] - b[0]\n        : ca === 2 ? a[1] - b[1]\n        : b[0] - a[0];\n  }\n\n  return function(stream) {\n    var activeStream = stream,\n        bufferStream = clipBuffer(),\n        segments,\n        polygon,\n        ring,\n        x__, y__, v__, // first point\n        x_, y_, v_, // previous point\n        first,\n        clean;\n\n    var clipStream = {\n      point: point,\n      lineStart: lineStart,\n      lineEnd: lineEnd,\n      polygonStart: polygonStart,\n      polygonEnd: polygonEnd\n    };\n\n    function point(x, y) {\n      if (visible(x, y)) activeStream.point(x, y);\n    }\n\n    function polygonInside() {\n      var winding = 0;\n\n      for (var i = 0, n = polygon.length; i < n; ++i) {\n        for (var ring = polygon[i], j = 1, m = ring.length, point = ring[0], a0, a1, b0 = point[0], b1 = point[1]; j < m; ++j) {\n          a0 = b0, a1 = b1, point = ring[j], b0 = point[0], b1 = point[1];\n          if (a1 <= y1) { if (b1 > y1 && (b0 - a0) * (y1 - a1) > (b1 - a1) * (x0 - a0)) ++winding; }\n          else { if (b1 <= y1 && (b0 - a0) * (y1 - a1) < (b1 - a1) * (x0 - a0)) --winding; }\n        }\n      }\n\n      return winding;\n    }\n\n    // Buffer geometry within a polygon and then clip it en masse.\n    function polygonStart() {\n      activeStream = bufferStream, segments = [], polygon = [], clean = true;\n    }\n\n    function polygonEnd() {\n      var startInside = polygonInside(),\n          cleanInside = clean && startInside,\n          visible = (segments = merge(segments)).length;\n      if (cleanInside || visible) {\n        stream.polygonStart();\n        if (cleanInside) {\n          stream.lineStart();\n          interpolate(null, null, 1, stream);\n          stream.lineEnd();\n        }\n        if (visible) {\n          clipRejoin(segments, compareIntersection, startInside, interpolate, stream);\n        }\n        stream.polygonEnd();\n      }\n      activeStream = stream, segments = polygon = ring = null;\n    }\n\n    function lineStart() {\n      clipStream.point = linePoint;\n      if (polygon) polygon.push(ring = []);\n      first = true;\n      v_ = false;\n      x_ = y_ = NaN;\n    }\n\n    // TODO rather than special-case polygons, simply handle them separately.\n    // Ideally, coincident intersection points should be jittered to avoid\n    // clipping issues.\n    function lineEnd() {\n      if (segments) {\n        linePoint(x__, y__);\n        if (v__ && v_) bufferStream.rejoin();\n        segments.push(bufferStream.result());\n      }\n      clipStream.point = point;\n      if (v_) activeStream.lineEnd();\n    }\n\n    function linePoint(x, y) {\n      var v = visible(x, y);\n      if (polygon) ring.push([x, y]);\n      if (first) {\n        x__ = x, y__ = y, v__ = v;\n        first = false;\n        if (v) {\n          activeStream.lineStart();\n          activeStream.point(x, y);\n        }\n      } else {\n        if (v && v_) activeStream.point(x, y);\n        else {\n          var a = [x_ = Math.max(clipMin, Math.min(clipMax, x_)), y_ = Math.max(clipMin, Math.min(clipMax, y_))],\n              b = [x = Math.max(clipMin, Math.min(clipMax, x)), y = Math.max(clipMin, Math.min(clipMax, y))];\n          if (clipLine(a, b, x0, y0, x1, y1)) {\n            if (!v_) {\n              activeStream.lineStart();\n              activeStream.point(a[0], a[1]);\n            }\n            activeStream.point(b[0], b[1]);\n            if (!v) activeStream.lineEnd();\n            clean = false;\n          } else if (v) {\n            activeStream.lineStart();\n            activeStream.point(x, y);\n            clean = false;\n          }\n        }\n      }\n      x_ = x, y_ = y, v_ = v;\n    }\n\n    return clipStream;\n  };\n}\n", "import clipRectangle from \"./rectangle.js\";\n\nexport default function() {\n  var x0 = 0,\n      y0 = 0,\n      x1 = 960,\n      y1 = 500,\n      cache,\n      cacheStream,\n      clip;\n\n  return clip = {\n    stream: function(stream) {\n      return cache && cacheStream === stream ? cache : cache = clipRectangle(x0, y0, x1, y1)(cacheStream = stream);\n    },\n    extent: function(_) {\n      return arguments.length ? (x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1], cache = cacheStream = null, clip) : [[x0, y0], [x1, y1]];\n    }\n  };\n}\n", "import {Adder} from \"d3-array\";\nimport {abs, atan2, cos, radians, sin, sqrt} from \"./math.js\";\nimport noop from \"./noop.js\";\nimport stream from \"./stream.js\";\n\nvar lengthSum,\n    lambda0,\n    sinPhi0,\n    cosPhi0;\n\nvar lengthStream = {\n  sphere: noop,\n  point: noop,\n  lineStart: lengthLineStart,\n  lineEnd: noop,\n  polygonStart: noop,\n  polygonEnd: noop\n};\n\nfunction lengthLineStart() {\n  lengthStream.point = lengthPointFirst;\n  lengthStream.lineEnd = lengthLineEnd;\n}\n\nfunction lengthLineEnd() {\n  lengthStream.point = lengthStream.lineEnd = noop;\n}\n\nfunction lengthPointFirst(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  lambda0 = lambda, sinPhi0 = sin(phi), cosPhi0 = cos(phi);\n  lengthStream.point = lengthPoint;\n}\n\nfunction lengthPoint(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  var sinPhi = sin(phi),\n      cosPhi = cos(phi),\n      delta = abs(lambda - lambda0),\n      cosDelta = cos(delta),\n      sinDelta = sin(delta),\n      x = cosPhi * sinDelta,\n      y = cosPhi0 * sinPhi - sinPhi0 * cosPhi * cosDelta,\n      z = sinPhi0 * sinPhi + cosPhi0 * cosPhi * cosDelta;\n  lengthSum.add(atan2(sqrt(x * x + y * y), z));\n  lambda0 = lambda, sinPhi0 = sinPhi, cosPhi0 = cosPhi;\n}\n\nexport default function(object) {\n  lengthSum = new Adder();\n  stream(object, lengthStream);\n  return +lengthSum;\n}\n", "import length from \"./length.js\";\n\nvar coordinates = [null, null],\n    object = {type: \"LineString\", coordinates: coordinates};\n\nexport default function(a, b) {\n  coordinates[0] = a;\n  coordinates[1] = b;\n  return length(object);\n}\n", "import {default as polygonContains} from \"./polygonContains.js\";\nimport {default as distance} from \"./distance.js\";\nimport {epsilon2, radians} from \"./math.js\";\n\nvar containsObjectType = {\n  Feature: function(object, point) {\n    return containsGeometry(object.geometry, point);\n  },\n  FeatureCollection: function(object, point) {\n    var features = object.features, i = -1, n = features.length;\n    while (++i < n) if (containsGeometry(features[i].geometry, point)) return true;\n    return false;\n  }\n};\n\nvar containsGeometryType = {\n  Sphere: function() {\n    return true;\n  },\n  Point: function(object, point) {\n    return containsPoint(object.coordinates, point);\n  },\n  MultiPoint: function(object, point) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) if (containsPoint(coordinates[i], point)) return true;\n    return false;\n  },\n  LineString: function(object, point) {\n    return containsLine(object.coordinates, point);\n  },\n  MultiLineString: function(object, point) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) if (containsLine(coordinates[i], point)) return true;\n    return false;\n  },\n  Polygon: function(object, point) {\n    return containsPolygon(object.coordinates, point);\n  },\n  MultiPolygon: function(object, point) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) if (containsPolygon(coordinates[i], point)) return true;\n    return false;\n  },\n  GeometryCollection: function(object, point) {\n    var geometries = object.geometries, i = -1, n = geometries.length;\n    while (++i < n) if (containsGeometry(geometries[i], point)) return true;\n    return false;\n  }\n};\n\nfunction containsGeometry(geometry, point) {\n  return geometry && containsGeometryType.hasOwnProperty(geometry.type)\n      ? containsGeometryType[geometry.type](geometry, point)\n      : false;\n}\n\nfunction containsPoint(coordinates, point) {\n  return distance(coordinates, point) === 0;\n}\n\nfunction containsLine(coordinates, point) {\n  var ao, bo, ab;\n  for (var i = 0, n = coordinates.length; i < n; i++) {\n    bo = distance(coordinates[i], point);\n    if (bo === 0) return true;\n    if (i > 0) {\n      ab = distance(coordinates[i], coordinates[i - 1]);\n      if (\n        ab > 0 &&\n        ao <= ab &&\n        bo <= ab &&\n        (ao + bo - ab) * (1 - Math.pow((ao - bo) / ab, 2)) < epsilon2 * ab\n      )\n        return true;\n    }\n    ao = bo;\n  }\n  return false;\n}\n\nfunction containsPolygon(coordinates, point) {\n  return !!polygonContains(coordinates.map(ringRadians), pointRadians(point));\n}\n\nfunction ringRadians(ring) {\n  return ring = ring.map(pointRadians), ring.pop(), ring;\n}\n\nfunction pointRadians(point) {\n  return [point[0] * radians, point[1] * radians];\n}\n\nexport default function(object, point) {\n  return (object && containsObjectType.hasOwnProperty(object.type)\n      ? containsObjectType[object.type]\n      : containsGeometry)(object, point);\n}\n", "import {range} from \"d3-array\";\nimport {abs, ceil, epsilon} from \"./math.js\";\n\nfunction graticuleX(y0, y1, dy) {\n  var y = range(y0, y1 - epsilon, dy).concat(y1);\n  return function(x) { return y.map(function(y) { return [x, y]; }); };\n}\n\nfunction graticuleY(x0, x1, dx) {\n  var x = range(x0, x1 - epsilon, dx).concat(x1);\n  return function(y) { return x.map(function(x) { return [x, y]; }); };\n}\n\nexport default function graticule() {\n  var x1, x0, X1, X0,\n      y1, y0, Y1, Y0,\n      dx = 10, dy = dx, DX = 90, DY = 360,\n      x, y, X, Y,\n      precision = 2.5;\n\n  function graticule() {\n    return {type: \"MultiLineString\", coordinates: lines()};\n  }\n\n  function lines() {\n    return range(ceil(X0 / DX) * DX, X1, DX).map(X)\n        .concat(range(ceil(Y0 / DY) * DY, Y1, DY).map(Y))\n        .concat(range(ceil(x0 / dx) * dx, x1, dx).filter(function(x) { return abs(x % DX) > epsilon; }).map(x))\n        .concat(range(ceil(y0 / dy) * dy, y1, dy).filter(function(y) { return abs(y % DY) > epsilon; }).map(y));\n  }\n\n  graticule.lines = function() {\n    return lines().map(function(coordinates) { return {type: \"LineString\", coordinates: coordinates}; });\n  };\n\n  graticule.outline = function() {\n    return {\n      type: \"Polygon\",\n      coordinates: [\n        X(X0).concat(\n        Y(Y1).slice(1),\n        X(X1).reverse().slice(1),\n        Y(Y0).reverse().slice(1))\n      ]\n    };\n  };\n\n  graticule.extent = function(_) {\n    if (!arguments.length) return graticule.extentMinor();\n    return graticule.extentMajor(_).extentMinor(_);\n  };\n\n  graticule.extentMajor = function(_) {\n    if (!arguments.length) return [[X0, Y0], [X1, Y1]];\n    X0 = +_[0][0], X1 = +_[1][0];\n    Y0 = +_[0][1], Y1 = +_[1][1];\n    if (X0 > X1) _ = X0, X0 = X1, X1 = _;\n    if (Y0 > Y1) _ = Y0, Y0 = Y1, Y1 = _;\n    return graticule.precision(precision);\n  };\n\n  graticule.extentMinor = function(_) {\n    if (!arguments.length) return [[x0, y0], [x1, y1]];\n    x0 = +_[0][0], x1 = +_[1][0];\n    y0 = +_[0][1], y1 = +_[1][1];\n    if (x0 > x1) _ = x0, x0 = x1, x1 = _;\n    if (y0 > y1) _ = y0, y0 = y1, y1 = _;\n    return graticule.precision(precision);\n  };\n\n  graticule.step = function(_) {\n    if (!arguments.length) return graticule.stepMinor();\n    return graticule.stepMajor(_).stepMinor(_);\n  };\n\n  graticule.stepMajor = function(_) {\n    if (!arguments.length) return [DX, DY];\n    DX = +_[0], DY = +_[1];\n    return graticule;\n  };\n\n  graticule.stepMinor = function(_) {\n    if (!arguments.length) return [dx, dy];\n    dx = +_[0], dy = +_[1];\n    return graticule;\n  };\n\n  graticule.precision = function(_) {\n    if (!arguments.length) return precision;\n    precision = +_;\n    x = graticuleX(y0, y1, 90);\n    y = graticuleY(x0, x1, precision);\n    X = graticuleX(Y0, Y1, 90);\n    Y = graticuleY(X0, X1, precision);\n    return graticule;\n  };\n\n  return graticule\n      .extentMajor([[-180, -90 + epsilon], [180, 90 - epsilon]])\n      .extentMinor([[-180, -80 - epsilon], [180, 80 + epsilon]]);\n}\n\nexport function graticule10() {\n  return graticule()();\n}\n", "import {asin, atan2, cos, degrees, haversin, radians, sin, sqrt} from \"./math.js\";\n\nexport default function(a, b) {\n  var x0 = a[0] * radians,\n      y0 = a[1] * radians,\n      x1 = b[0] * radians,\n      y1 = b[1] * radians,\n      cy0 = cos(y0),\n      sy0 = sin(y0),\n      cy1 = cos(y1),\n      sy1 = sin(y1),\n      kx0 = cy0 * cos(x0),\n      ky0 = cy0 * sin(x0),\n      kx1 = cy1 * cos(x1),\n      ky1 = cy1 * sin(x1),\n      d = 2 * asin(sqrt(haversin(y1 - y0) + cy0 * cy1 * haversin(x1 - x0))),\n      k = sin(d);\n\n  var interpolate = d ? function(t) {\n    var B = sin(t *= d) / k,\n        A = sin(d - t) / k,\n        x = A * kx0 + B * kx1,\n        y = A * ky0 + B * ky1,\n        z = A * sy0 + B * sy1;\n    return [\n      atan2(y, x) * degrees,\n      atan2(z, sqrt(x * x + y * y)) * degrees\n    ];\n  } : function() {\n    return [x0 * degrees, y0 * degrees];\n  };\n\n  interpolate.distance = d;\n\n  return interpolate;\n}\n", "export default x => x;\n", "import {Adder} from \"d3-array\";\nimport {abs} from \"../math.js\";\nimport noop from \"../noop.js\";\n\nvar areaSum = new Adder(),\n    areaRingSum = new Adder(),\n    x00,\n    y00,\n    x0,\n    y0;\n\nvar areaStream = {\n  point: noop,\n  lineStart: noop,\n  lineEnd: noop,\n  polygonStart: function() {\n    areaStream.lineStart = areaRingStart;\n    areaStream.lineEnd = areaRingEnd;\n  },\n  polygonEnd: function() {\n    areaStream.lineStart = areaStream.lineEnd = areaStream.point = noop;\n    areaSum.add(abs(areaRingSum));\n    areaRingSum = new Adder();\n  },\n  result: function() {\n    var area = areaSum / 2;\n    areaSum = new Adder();\n    return area;\n  }\n};\n\nfunction areaRingStart() {\n  areaStream.point = areaPointFirst;\n}\n\nfunction areaPointFirst(x, y) {\n  areaStream.point = areaPoint;\n  x00 = x0 = x, y00 = y0 = y;\n}\n\nfunction areaPoint(x, y) {\n  areaRingSum.add(y0 * x - x0 * y);\n  x0 = x, y0 = y;\n}\n\nfunction areaRingEnd() {\n  areaPoint(x00, y00);\n}\n\nexport default areaStream;\n", "import noop from \"../noop.js\";\n\nvar x0 = Infinity,\n    y0 = x0,\n    x1 = -x0,\n    y1 = x1;\n\nvar boundsStream = {\n  point: boundsPoint,\n  lineStart: noop,\n  lineEnd: noop,\n  polygonStart: noop,\n  polygonEnd: noop,\n  result: function() {\n    var bounds = [[x0, y0], [x1, y1]];\n    x1 = y1 = -(y0 = x0 = Infinity);\n    return bounds;\n  }\n};\n\nfunction boundsPoint(x, y) {\n  if (x < x0) x0 = x;\n  if (x > x1) x1 = x;\n  if (y < y0) y0 = y;\n  if (y > y1) y1 = y;\n}\n\nexport default boundsStream;\n", "import {sqrt} from \"../math.js\";\n\n// TODO Enforce positive area for exterior, negative area for interior?\n\nvar X0 = 0,\n    Y0 = 0,\n    Z0 = 0,\n    X1 = 0,\n    Y1 = 0,\n    Z1 = 0,\n    X2 = 0,\n    Y2 = 0,\n    Z2 = 0,\n    x00,\n    y00,\n    x0,\n    y0;\n\nvar centroidStream = {\n  point: centroidPoint,\n  lineStart: centroidLineStart,\n  lineEnd: centroidLineEnd,\n  polygonStart: function() {\n    centroidStream.lineStart = centroidRingStart;\n    centroidStream.lineEnd = centroidRingEnd;\n  },\n  polygonEnd: function() {\n    centroidStream.point = centroidPoint;\n    centroidStream.lineStart = centroidLineStart;\n    centroidStream.lineEnd = centroidLineEnd;\n  },\n  result: function() {\n    var centroid = Z2 ? [X2 / Z2, Y2 / Z2]\n        : Z1 ? [X1 / Z1, Y1 / Z1]\n        : Z0 ? [X0 / Z0, Y0 / Z0]\n        : [NaN, NaN];\n    X0 = Y0 = Z0 =\n    X1 = Y1 = Z1 =\n    X2 = Y2 = Z2 = 0;\n    return centroid;\n  }\n};\n\nfunction centroidPoint(x, y) {\n  X0 += x;\n  Y0 += y;\n  ++Z0;\n}\n\nfunction centroidLineStart() {\n  centroidStream.point = centroidPointFirstLine;\n}\n\nfunction centroidPointFirstLine(x, y) {\n  centroidStream.point = centroidPointLine;\n  centroidPoint(x0 = x, y0 = y);\n}\n\nfunction centroidPointLine(x, y) {\n  var dx = x - x0, dy = y - y0, z = sqrt(dx * dx + dy * dy);\n  X1 += z * (x0 + x) / 2;\n  Y1 += z * (y0 + y) / 2;\n  Z1 += z;\n  centroidPoint(x0 = x, y0 = y);\n}\n\nfunction centroidLineEnd() {\n  centroidStream.point = centroidPoint;\n}\n\nfunction centroidRingStart() {\n  centroidStream.point = centroidPointFirstRing;\n}\n\nfunction centroidRingEnd() {\n  centroidPointRing(x00, y00);\n}\n\nfunction centroidPointFirstRing(x, y) {\n  centroidStream.point = centroidPointRing;\n  centroidPoint(x00 = x0 = x, y00 = y0 = y);\n}\n\nfunction centroidPointRing(x, y) {\n  var dx = x - x0,\n      dy = y - y0,\n      z = sqrt(dx * dx + dy * dy);\n\n  X1 += z * (x0 + x) / 2;\n  Y1 += z * (y0 + y) / 2;\n  Z1 += z;\n\n  z = y0 * x - x0 * y;\n  X2 += z * (x0 + x);\n  Y2 += z * (y0 + y);\n  Z2 += z * 3;\n  centroidPoint(x0 = x, y0 = y);\n}\n\nexport default centroidStream;\n", "import {tau} from \"../math.js\";\nimport noop from \"../noop.js\";\n\nexport default function PathContext(context) {\n  this._context = context;\n}\n\nPathContext.prototype = {\n  _radius: 4.5,\n  pointRadius: function(_) {\n    return this._radius = _, this;\n  },\n  polygonStart: function() {\n    this._line = 0;\n  },\n  polygonEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line === 0) this._context.closePath();\n    this._point = NaN;\n  },\n  point: function(x, y) {\n    switch (this._point) {\n      case 0: {\n        this._context.moveTo(x, y);\n        this._point = 1;\n        break;\n      }\n      case 1: {\n        this._context.lineTo(x, y);\n        break;\n      }\n      default: {\n        this._context.moveTo(x + this._radius, y);\n        this._context.arc(x, y, this._radius, 0, tau);\n        break;\n      }\n    }\n  },\n  result: noop\n};\n", "import {Adder} from \"d3-array\";\nimport {sqrt} from \"../math.js\";\nimport noop from \"../noop.js\";\n\nvar lengthSum = new Adder(),\n    lengthRing,\n    x00,\n    y00,\n    x0,\n    y0;\n\nvar lengthStream = {\n  point: noop,\n  lineStart: function() {\n    lengthStream.point = lengthPointFirst;\n  },\n  lineEnd: function() {\n    if (lengthRing) lengthPoint(x00, y00);\n    lengthStream.point = noop;\n  },\n  polygonStart: function() {\n    lengthRing = true;\n  },\n  polygonEnd: function() {\n    lengthRing = null;\n  },\n  result: function() {\n    var length = +lengthSum;\n    lengthSum = new Adder();\n    return length;\n  }\n};\n\nfunction lengthPointFirst(x, y) {\n  lengthStream.point = lengthPoint;\n  x00 = x0 = x, y00 = y0 = y;\n}\n\nfunction lengthPoint(x, y) {\n  x0 -= x, y0 -= y;\n  lengthSum.add(sqrt(x0 * x0 + y0 * y0));\n  x0 = x, y0 = y;\n}\n\nexport default lengthStream;\n", "// Simple caching for constant-radius points.\nlet cacheDigits, cacheAppend, cacheRadius, cacheCircle;\n\nexport default class PathString {\n  constructor(digits) {\n    this._append = digits == null ? append : appendRound(digits);\n    this._radius = 4.5;\n    this._ = \"\";\n  }\n  pointRadius(_) {\n    this._radius = +_;\n    return this;\n  }\n  polygonStart() {\n    this._line = 0;\n  }\n  polygonEnd() {\n    this._line = NaN;\n  }\n  lineStart() {\n    this._point = 0;\n  }\n  lineEnd() {\n    if (this._line === 0) this._ += \"Z\";\n    this._point = NaN;\n  }\n  point(x, y) {\n    switch (this._point) {\n      case 0: {\n        this._append`M${x},${y}`;\n        this._point = 1;\n        break;\n      }\n      case 1: {\n        this._append`L${x},${y}`;\n        break;\n      }\n      default: {\n        this._append`M${x},${y}`;\n        if (this._radius !== cacheRadius || this._append !== cacheAppend) {\n          const r = this._radius;\n          const s = this._;\n          this._ = \"\"; // stash the old string so we can cache the circle path fragment\n          this._append`m0,${r}a${r},${r} 0 1,1 0,${-2 * r}a${r},${r} 0 1,1 0,${2 * r}z`;\n          cacheRadius = r;\n          cacheAppend = this._append;\n          cacheCircle = this._;\n          this._ = s;\n        }\n        this._ += cacheCircle;\n        break;\n      }\n    }\n  }\n  result() {\n    const result = this._;\n    this._ = \"\";\n    return result.length ? result : null;\n  }\n}\n\nfunction append(strings) {\n  let i = 1;\n  this._ += strings[0];\n  for (const j = strings.length; i < j; ++i) {\n    this._ += arguments[i] + strings[i];\n  }\n}\n\nfunction appendRound(digits) {\n  const d = Math.floor(digits);\n  if (!(d >= 0)) throw new RangeError(`invalid digits: ${digits}`);\n  if (d > 15) return append;\n  if (d !== cacheDigits) {\n    const k = 10 ** d;\n    cacheDigits = d;\n    cacheAppend = function append(strings) {\n      let i = 1;\n      this._ += strings[0];\n      for (const j = strings.length; i < j; ++i) {\n        this._ += Math.round(arguments[i] * k) / k + strings[i];\n      }\n    };\n  }\n  return cacheAppend;\n}\n", "import identity from \"../identity.js\";\nimport stream from \"../stream.js\";\nimport pathArea from \"./area.js\";\nimport pathBounds from \"./bounds.js\";\nimport pathCentroid from \"./centroid.js\";\nimport PathContext from \"./context.js\";\nimport pathMeasure from \"./measure.js\";\nimport PathString from \"./string.js\";\n\nexport default function(projection, context) {\n  let digits = 3,\n      pointRadius = 4.5,\n      projectionStream,\n      contextStream;\n\n  function path(object) {\n    if (object) {\n      if (typeof pointRadius === \"function\") contextStream.pointRadius(+pointRadius.apply(this, arguments));\n      stream(object, projectionStream(contextStream));\n    }\n    return contextStream.result();\n  }\n\n  path.area = function(object) {\n    stream(object, projectionStream(pathArea));\n    return pathArea.result();\n  };\n\n  path.measure = function(object) {\n    stream(object, projectionStream(pathMeasure));\n    return pathMeasure.result();\n  };\n\n  path.bounds = function(object) {\n    stream(object, projectionStream(pathBounds));\n    return pathBounds.result();\n  };\n\n  path.centroid = function(object) {\n    stream(object, projectionStream(pathCentroid));\n    return pathCentroid.result();\n  };\n\n  path.projection = function(_) {\n    if (!arguments.length) return projection;\n    projectionStream = _ == null ? (projection = null, identity) : (projection = _).stream;\n    return path;\n  };\n\n  path.context = function(_) {\n    if (!arguments.length) return context;\n    contextStream = _ == null ? (context = null, new PathString(digits)) : new PathContext(context = _);\n    if (typeof pointRadius !== \"function\") contextStream.pointRadius(pointRadius);\n    return path;\n  };\n\n  path.pointRadius = function(_) {\n    if (!arguments.length) return pointRadius;\n    pointRadius = typeof _ === \"function\" ? _ : (contextStream.pointRadius(+_), +_);\n    return path;\n  };\n\n  path.digits = function(_) {\n    if (!arguments.length) return digits;\n    if (_ == null) digits = null;\n    else {\n      const d = Math.floor(_);\n      if (!(d >= 0)) throw new RangeError(`invalid digits: ${_}`);\n      digits = d;\n    }\n    if (context === null) contextStream = new PathString(digits);\n    return path;\n  };\n\n  return path.projection(projection).digits(digits).context(context);\n}\n", "export default function(methods) {\n  return {\n    stream: transformer(methods)\n  };\n}\n\nexport function transformer(methods) {\n  return function(stream) {\n    var s = new TransformStream;\n    for (var key in methods) s[key] = methods[key];\n    s.stream = stream;\n    return s;\n  };\n}\n\nfunction TransformStream() {}\n\nTransformStream.prototype = {\n  constructor: TransformStream,\n  point: function(x, y) { this.stream.point(x, y); },\n  sphere: function() { this.stream.sphere(); },\n  lineStart: function() { this.stream.lineStart(); },\n  lineEnd: function() { this.stream.lineEnd(); },\n  polygonStart: function() { this.stream.polygonStart(); },\n  polygonEnd: function() { this.stream.polygonEnd(); }\n};\n", "import {default as geoStream} from \"../stream.js\";\nimport boundsStream from \"../path/bounds.js\";\n\nfunction fit(projection, fitBounds, object) {\n  var clip = projection.clipExtent && projection.clipExtent();\n  projection.scale(150).translate([0, 0]);\n  if (clip != null) projection.clipExtent(null);\n  geoStream(object, projection.stream(boundsStream));\n  fitBounds(boundsStream.result());\n  if (clip != null) projection.clipExtent(clip);\n  return projection;\n}\n\nexport function fitExtent(projection, extent, object) {\n  return fit(projection, function(b) {\n    var w = extent[1][0] - extent[0][0],\n        h = extent[1][1] - extent[0][1],\n        k = Math.min(w / (b[1][0] - b[0][0]), h / (b[1][1] - b[0][1])),\n        x = +extent[0][0] + (w - k * (b[1][0] + b[0][0])) / 2,\n        y = +extent[0][1] + (h - k * (b[1][1] + b[0][1])) / 2;\n    projection.scale(150 * k).translate([x, y]);\n  }, object);\n}\n\nexport function fitSize(projection, size, object) {\n  return fitExtent(projection, [[0, 0], size], object);\n}\n\nexport function fitWidth(projection, width, object) {\n  return fit(projection, function(b) {\n    var w = +width,\n        k = w / (b[1][0] - b[0][0]),\n        x = (w - k * (b[1][0] + b[0][0])) / 2,\n        y = -k * b[0][1];\n    projection.scale(150 * k).translate([x, y]);\n  }, object);\n}\n\nexport function fitHeight(projection, height, object) {\n  return fit(projection, function(b) {\n    var h = +height,\n        k = h / (b[1][1] - b[0][1]),\n        x = -k * b[0][0],\n        y = (h - k * (b[1][1] + b[0][1])) / 2;\n    projection.scale(150 * k).translate([x, y]);\n  }, object);\n}\n", "import {cartesian} from \"../cartesian.js\";\nimport {abs, asin, atan2, cos, epsilon, radians, sqrt} from \"../math.js\";\nimport {transformer} from \"../transform.js\";\n\nvar maxDepth = 16, // maximum depth of subdivision\n    cosMinDistance = cos(30 * radians); // cos(minimum angular distance)\n\nexport default function(project, delta2) {\n  return +delta2 ? resample(project, delta2) : resampleNone(project);\n}\n\nfunction resampleNone(project) {\n  return transformer({\n    point: function(x, y) {\n      x = project(x, y);\n      this.stream.point(x[0], x[1]);\n    }\n  });\n}\n\nfunction resample(project, delta2) {\n\n  function resampleLineTo(x0, y0, lambda0, a0, b0, c0, x1, y1, lambda1, a1, b1, c1, depth, stream) {\n    var dx = x1 - x0,\n        dy = y1 - y0,\n        d2 = dx * dx + dy * dy;\n    if (d2 > 4 * delta2 && depth--) {\n      var a = a0 + a1,\n          b = b0 + b1,\n          c = c0 + c1,\n          m = sqrt(a * a + b * b + c * c),\n          phi2 = asin(c /= m),\n          lambda2 = abs(abs(c) - 1) < epsilon || abs(lambda0 - lambda1) < epsilon ? (lambda0 + lambda1) / 2 : atan2(b, a),\n          p = project(lambda2, phi2),\n          x2 = p[0],\n          y2 = p[1],\n          dx2 = x2 - x0,\n          dy2 = y2 - y0,\n          dz = dy * dx2 - dx * dy2;\n      if (dz * dz / d2 > delta2 // perpendicular projected distance\n          || abs((dx * dx2 + dy * dy2) / d2 - 0.5) > 0.3 // midpoint close to an end\n          || a0 * a1 + b0 * b1 + c0 * c1 < cosMinDistance) { // angular distance\n        resampleLineTo(x0, y0, lambda0, a0, b0, c0, x2, y2, lambda2, a /= m, b /= m, c, depth, stream);\n        stream.point(x2, y2);\n        resampleLineTo(x2, y2, lambda2, a, b, c, x1, y1, lambda1, a1, b1, c1, depth, stream);\n      }\n    }\n  }\n  return function(stream) {\n    var lambda00, x00, y00, a00, b00, c00, // first point\n        lambda0, x0, y0, a0, b0, c0; // previous point\n\n    var resampleStream = {\n      point: point,\n      lineStart: lineStart,\n      lineEnd: lineEnd,\n      polygonStart: function() { stream.polygonStart(); resampleStream.lineStart = ringStart; },\n      polygonEnd: function() { stream.polygonEnd(); resampleStream.lineStart = lineStart; }\n    };\n\n    function point(x, y) {\n      x = project(x, y);\n      stream.point(x[0], x[1]);\n    }\n\n    function lineStart() {\n      x0 = NaN;\n      resampleStream.point = linePoint;\n      stream.lineStart();\n    }\n\n    function linePoint(lambda, phi) {\n      var c = cartesian([lambda, phi]), p = project(lambda, phi);\n      resampleLineTo(x0, y0, lambda0, a0, b0, c0, x0 = p[0], y0 = p[1], lambda0 = lambda, a0 = c[0], b0 = c[1], c0 = c[2], maxDepth, stream);\n      stream.point(x0, y0);\n    }\n\n    function lineEnd() {\n      resampleStream.point = point;\n      stream.lineEnd();\n    }\n\n    function ringStart() {\n      lineStart();\n      resampleStream.point = ringPoint;\n      resampleStream.lineEnd = ringEnd;\n    }\n\n    function ringPoint(lambda, phi) {\n      linePoint(lambda00 = lambda, phi), x00 = x0, y00 = y0, a00 = a0, b00 = b0, c00 = c0;\n      resampleStream.point = linePoint;\n    }\n\n    function ringEnd() {\n      resampleLineTo(x0, y0, lambda0, a0, b0, c0, x00, y00, lambda00, a00, b00, c00, maxDepth, stream);\n      resampleStream.lineEnd = lineEnd;\n      lineEnd();\n    }\n\n    return resampleStream;\n  };\n}\n", "import clipAntimeridian from \"../clip/antimeridian.js\";\nimport clipCircle from \"../clip/circle.js\";\nimport clipRectangle from \"../clip/rectangle.js\";\nimport compose from \"../compose.js\";\nimport identity from \"../identity.js\";\nimport {cos, degrees, radians, sin, sqrt} from \"../math.js\";\nimport {rotateRadians} from \"../rotation.js\";\nimport {transformer} from \"../transform.js\";\nimport {fitExtent, fitSize, fitWidth, fitHeight} from \"./fit.js\";\nimport resample from \"./resample.js\";\n\nvar transformRadians = transformer({\n  point: function(x, y) {\n    this.stream.point(x * radians, y * radians);\n  }\n});\n\nfunction transformRotate(rotate) {\n  return transformer({\n    point: function(x, y) {\n      var r = rotate(x, y);\n      return this.stream.point(r[0], r[1]);\n    }\n  });\n}\n\nfunction scaleTranslate(k, dx, dy, sx, sy) {\n  function transform(x, y) {\n    x *= sx; y *= sy;\n    return [dx + k * x, dy - k * y];\n  }\n  transform.invert = function(x, y) {\n    return [(x - dx) / k * sx, (dy - y) / k * sy];\n  };\n  return transform;\n}\n\nfunction scaleTranslateRotate(k, dx, dy, sx, sy, alpha) {\n  if (!alpha) return scaleTranslate(k, dx, dy, sx, sy);\n  var cosAlpha = cos(alpha),\n      sinAlpha = sin(alpha),\n      a = cosAlpha * k,\n      b = sinAlpha * k,\n      ai = cosAlpha / k,\n      bi = sinAlpha / k,\n      ci = (sinAlpha * dy - cosAlpha * dx) / k,\n      fi = (sinAlpha * dx + cosAlpha * dy) / k;\n  function transform(x, y) {\n    x *= sx; y *= sy;\n    return [a * x - b * y + dx, dy - b * x - a * y];\n  }\n  transform.invert = function(x, y) {\n    return [sx * (ai * x - bi * y + ci), sy * (fi - bi * x - ai * y)];\n  };\n  return transform;\n}\n\nexport default function projection(project) {\n  return projectionMutator(function() { return project; })();\n}\n\nexport function projectionMutator(projectAt) {\n  var project,\n      k = 150, // scale\n      x = 480, y = 250, // translate\n      lambda = 0, phi = 0, // center\n      deltaLambda = 0, deltaPhi = 0, deltaGamma = 0, rotate, // pre-rotate\n      alpha = 0, // post-rotate angle\n      sx = 1, // reflectX\n      sy = 1, // reflectX\n      theta = null, preclip = clipAntimeridian, // pre-clip angle\n      x0 = null, y0, x1, y1, postclip = identity, // post-clip extent\n      delta2 = 0.5, // precision\n      projectResample,\n      projectTransform,\n      projectRotateTransform,\n      cache,\n      cacheStream;\n\n  function projection(point) {\n    return projectRotateTransform(point[0] * radians, point[1] * radians);\n  }\n\n  function invert(point) {\n    point = projectRotateTransform.invert(point[0], point[1]);\n    return point && [point[0] * degrees, point[1] * degrees];\n  }\n\n  projection.stream = function(stream) {\n    return cache && cacheStream === stream ? cache : cache = transformRadians(transformRotate(rotate)(preclip(projectResample(postclip(cacheStream = stream)))));\n  };\n\n  projection.preclip = function(_) {\n    return arguments.length ? (preclip = _, theta = undefined, reset()) : preclip;\n  };\n\n  projection.postclip = function(_) {\n    return arguments.length ? (postclip = _, x0 = y0 = x1 = y1 = null, reset()) : postclip;\n  };\n\n  projection.clipAngle = function(_) {\n    return arguments.length ? (preclip = +_ ? clipCircle(theta = _ * radians) : (theta = null, clipAntimeridian), reset()) : theta * degrees;\n  };\n\n  projection.clipExtent = function(_) {\n    return arguments.length ? (postclip = _ == null ? (x0 = y0 = x1 = y1 = null, identity) : clipRectangle(x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1]), reset()) : x0 == null ? null : [[x0, y0], [x1, y1]];\n  };\n\n  projection.scale = function(_) {\n    return arguments.length ? (k = +_, recenter()) : k;\n  };\n\n  projection.translate = function(_) {\n    return arguments.length ? (x = +_[0], y = +_[1], recenter()) : [x, y];\n  };\n\n  projection.center = function(_) {\n    return arguments.length ? (lambda = _[0] % 360 * radians, phi = _[1] % 360 * radians, recenter()) : [lambda * degrees, phi * degrees];\n  };\n\n  projection.rotate = function(_) {\n    return arguments.length ? (deltaLambda = _[0] % 360 * radians, deltaPhi = _[1] % 360 * radians, deltaGamma = _.length > 2 ? _[2] % 360 * radians : 0, recenter()) : [deltaLambda * degrees, deltaPhi * degrees, deltaGamma * degrees];\n  };\n\n  projection.angle = function(_) {\n    return arguments.length ? (alpha = _ % 360 * radians, recenter()) : alpha * degrees;\n  };\n\n  projection.reflectX = function(_) {\n    return arguments.length ? (sx = _ ? -1 : 1, recenter()) : sx < 0;\n  };\n\n  projection.reflectY = function(_) {\n    return arguments.length ? (sy = _ ? -1 : 1, recenter()) : sy < 0;\n  };\n\n  projection.precision = function(_) {\n    return arguments.length ? (projectResample = resample(projectTransform, delta2 = _ * _), reset()) : sqrt(delta2);\n  };\n\n  projection.fitExtent = function(extent, object) {\n    return fitExtent(projection, extent, object);\n  };\n\n  projection.fitSize = function(size, object) {\n    return fitSize(projection, size, object);\n  };\n\n  projection.fitWidth = function(width, object) {\n    return fitWidth(projection, width, object);\n  };\n\n  projection.fitHeight = function(height, object) {\n    return fitHeight(projection, height, object);\n  };\n\n  function recenter() {\n    var center = scaleTranslateRotate(k, 0, 0, sx, sy, alpha).apply(null, project(lambda, phi)),\n        transform = scaleTranslateRotate(k, x - center[0], y - center[1], sx, sy, alpha);\n    rotate = rotateRadians(deltaLambda, deltaPhi, deltaGamma);\n    projectTransform = compose(project, transform);\n    projectRotateTransform = compose(rotate, projectTransform);\n    projectResample = resample(projectTransform, delta2);\n    return reset();\n  }\n\n  function reset() {\n    cache = cacheStream = null;\n    return projection;\n  }\n\n  return function() {\n    project = projectAt.apply(this, arguments);\n    projection.invert = project.invert && invert;\n    return recenter();\n  };\n}\n", "import {degrees, pi, radians} from \"../math.js\";\nimport {projectionMutator} from \"./index.js\";\n\nexport function conicProjection(projectAt) {\n  var phi0 = 0,\n      phi1 = pi / 3,\n      m = projectionMutator(projectAt),\n      p = m(phi0, phi1);\n\n  p.parallels = function(_) {\n    return arguments.length ? m(phi0 = _[0] * radians, phi1 = _[1] * radians) : [phi0 * degrees, phi1 * degrees];\n  };\n\n  return p;\n}\n", "import {asin, cos, sin} from \"../math.js\";\n\nexport function cylindricalEqualAreaRaw(phi0) {\n  var cosPhi0 = cos(phi0);\n\n  function forward(lambda, phi) {\n    return [lambda * cosPhi0, sin(phi) / cosPhi0];\n  }\n\n  forward.invert = function(x, y) {\n    return [x / cosPhi0, asin(y * cosPhi0)];\n  };\n\n  return forward;\n}\n", "import {abs, asin, atan2, cos, epsilon, pi, sign, sin, sqrt} from \"../math.js\";\nimport {conicProjection} from \"./conic.js\";\nimport {cylindricalEqualAreaRaw} from \"./cylindricalEqualArea.js\";\n\nexport function conicEqualAreaRaw(y0, y1) {\n  var sy0 = sin(y0), n = (sy0 + sin(y1)) / 2;\n\n  // Are the parallels symmetrical around the Equator?\n  if (abs(n) < epsilon) return cylindricalEqualAreaRaw(y0);\n\n  var c = 1 + sy0 * (2 * n - sy0), r0 = sqrt(c) / n;\n\n  function project(x, y) {\n    var r = sqrt(c - 2 * n * sin(y)) / n;\n    return [r * sin(x *= n), r0 - r * cos(x)];\n  }\n\n  project.invert = function(x, y) {\n    var r0y = r0 - y,\n        l = atan2(x, abs(r0y)) * sign(r0y);\n    if (r0y * n < 0)\n      l -= pi * sign(x) * sign(r0y);\n    return [l / n, asin((c - (x * x + r0y * r0y) * n * n) / (2 * n))];\n  };\n\n  return project;\n}\n\nexport default function() {\n  return conicProjection(conicEqualAreaRaw)\n      .scale(155.424)\n      .center([0, 33.6442]);\n}\n", "import conicEqualArea from \"./conicEqualArea.js\";\n\nexport default function() {\n  return conicEqualArea()\n      .parallels([29.5, 45.5])\n      .scale(1070)\n      .translate([480, 250])\n      .rotate([96, 0])\n      .center([-0.6, 38.7]);\n}\n", "import {epsilon} from \"../math.js\";\nimport albers from \"./albers.js\";\nimport conicEqualArea from \"./conicEqualArea.js\";\nimport {fitExtent, fitSize, fitWidth, fitHeight} from \"./fit.js\";\n\n// The projections must have mutually exclusive clip regions on the sphere,\n// as this will avoid emitting interleaving lines and polygons.\nfunction multiplex(streams) {\n  var n = streams.length;\n  return {\n    point: function(x, y) { var i = -1; while (++i < n) streams[i].point(x, y); },\n    sphere: function() { var i = -1; while (++i < n) streams[i].sphere(); },\n    lineStart: function() { var i = -1; while (++i < n) streams[i].lineStart(); },\n    lineEnd: function() { var i = -1; while (++i < n) streams[i].lineEnd(); },\n    polygonStart: function() { var i = -1; while (++i < n) streams[i].polygonStart(); },\n    polygonEnd: function() { var i = -1; while (++i < n) streams[i].polygonEnd(); }\n  };\n}\n\n// A composite projection for the United States, configured by default for\n// 960×500. The projection also works quite well at 960×600 if you change the\n// scale to 1285 and adjust the translate accordingly. The set of standard\n// parallels for each region comes from USGS, which is published here:\n// http://egsc.usgs.gov/isb/pubs/MapProjections/projections.html#albers\nexport default function() {\n  var cache,\n      cacheStream,\n      lower48 = albers(), lower48Point,\n      alaska = conicEqualArea().rotate([154, 0]).center([-2, 58.5]).parallels([55, 65]), alaskaPoint, // EPSG:3338\n      hawaii = conicEqualArea().rotate([157, 0]).center([-3, 19.9]).parallels([8, 18]), hawaiiPoint, // ESRI:102007\n      point, pointStream = {point: function(x, y) { point = [x, y]; }};\n\n  function albersUsa(coordinates) {\n    var x = coordinates[0], y = coordinates[1];\n    return point = null,\n        (lower48Point.point(x, y), point)\n        || (alaskaPoint.point(x, y), point)\n        || (hawaiiPoint.point(x, y), point);\n  }\n\n  albersUsa.invert = function(coordinates) {\n    var k = lower48.scale(),\n        t = lower48.translate(),\n        x = (coordinates[0] - t[0]) / k,\n        y = (coordinates[1] - t[1]) / k;\n    return (y >= 0.120 && y < 0.234 && x >= -0.425 && x < -0.214 ? alaska\n        : y >= 0.166 && y < 0.234 && x >= -0.214 && x < -0.115 ? hawaii\n        : lower48).invert(coordinates);\n  };\n\n  albersUsa.stream = function(stream) {\n    return cache && cacheStream === stream ? cache : cache = multiplex([lower48.stream(cacheStream = stream), alaska.stream(stream), hawaii.stream(stream)]);\n  };\n\n  albersUsa.precision = function(_) {\n    if (!arguments.length) return lower48.precision();\n    lower48.precision(_), alaska.precision(_), hawaii.precision(_);\n    return reset();\n  };\n\n  albersUsa.scale = function(_) {\n    if (!arguments.length) return lower48.scale();\n    lower48.scale(_), alaska.scale(_ * 0.35), hawaii.scale(_);\n    return albersUsa.translate(lower48.translate());\n  };\n\n  albersUsa.translate = function(_) {\n    if (!arguments.length) return lower48.translate();\n    var k = lower48.scale(), x = +_[0], y = +_[1];\n\n    lower48Point = lower48\n        .translate(_)\n        .clipExtent([[x - 0.455 * k, y - 0.238 * k], [x + 0.455 * k, y + 0.238 * k]])\n        .stream(pointStream);\n\n    alaskaPoint = alaska\n        .translate([x - 0.307 * k, y + 0.201 * k])\n        .clipExtent([[x - 0.425 * k + epsilon, y + 0.120 * k + epsilon], [x - 0.214 * k - epsilon, y + 0.234 * k - epsilon]])\n        .stream(pointStream);\n\n    hawaiiPoint = hawaii\n        .translate([x - 0.205 * k, y + 0.212 * k])\n        .clipExtent([[x - 0.214 * k + epsilon, y + 0.166 * k + epsilon], [x - 0.115 * k - epsilon, y + 0.234 * k - epsilon]])\n        .stream(pointStream);\n\n    return reset();\n  };\n\n  albersUsa.fitExtent = function(extent, object) {\n    return fitExtent(albersUsa, extent, object);\n  };\n\n  albersUsa.fitSize = function(size, object) {\n    return fitSize(albersUsa, size, object);\n  };\n\n  albersUsa.fitWidth = function(width, object) {\n    return fitWidth(albersUsa, width, object);\n  };\n\n  albersUsa.fitHeight = function(height, object) {\n    return fitHeight(albersUsa, height, object);\n  };\n\n  function reset() {\n    cache = cacheStream = null;\n    return albersUsa;\n  }\n\n  return albersUsa.scale(1070);\n}\n", "import {asin, atan2, cos, sin, sqrt} from \"../math.js\";\n\nexport function azimuthalRaw(scale) {\n  return function(x, y) {\n    var cx = cos(x),\n        cy = cos(y),\n        k = scale(cx * cy);\n        if (k === Infinity) return [2, 0];\n    return [\n      k * cy * sin(x),\n      k * sin(y)\n    ];\n  }\n}\n\nexport function azimuthalInvert(angle) {\n  return function(x, y) {\n    var z = sqrt(x * x + y * y),\n        c = angle(z),\n        sc = sin(c),\n        cc = cos(c);\n    return [\n      atan2(x * sc, z * cc),\n      asin(z && y * sc / z)\n    ];\n  }\n}\n", "import {asin, sqrt} from \"../math.js\";\nimport {azimuthalRaw, azimuthalInvert} from \"./azimuthal.js\";\nimport projection from \"./index.js\";\n\nexport var azimuthalEqualAreaRaw = azimuthalRaw(function(cxcy) {\n  return sqrt(2 / (1 + cxcy));\n});\n\nazimuthalEqualAreaRaw.invert = azimuthalInvert(function(z) {\n  return 2 * asin(z / 2);\n});\n\nexport default function() {\n  return projection(azimuthalEqualAreaRaw)\n      .scale(124.75)\n      .clipAngle(180 - 1e-3);\n}\n", "import {acos, sin} from \"../math.js\";\nimport {azimuthalRaw, azimuthalInvert} from \"./azimuthal.js\";\nimport projection from \"./index.js\";\n\nexport var azimuthalEquidistantRaw = azimuthalRaw(function(c) {\n  return (c = acos(c)) && c / sin(c);\n});\n\nazimuthalEquidistantRaw.invert = azimuthalInvert(function(z) {\n  return z;\n});\n\nexport default function() {\n  return projection(azimuthalEquidistantRaw)\n      .scale(79.4188)\n      .clipAngle(180 - 1e-3);\n}\n", "import {atan, exp, halfPi, log, pi, tan, tau} from \"../math.js\";\nimport rotation from \"../rotation.js\";\nimport projection from \"./index.js\";\n\nexport function mercatorRaw(lambda, phi) {\n  return [lambda, log(tan((halfPi + phi) / 2))];\n}\n\nmercatorRaw.invert = function(x, y) {\n  return [x, 2 * atan(exp(y)) - halfPi];\n};\n\nexport default function() {\n  return mercatorProjection(mercatorRaw)\n      .scale(961 / tau);\n}\n\nexport function mercatorProjection(project) {\n  var m = projection(project),\n      center = m.center,\n      scale = m.scale,\n      translate = m.translate,\n      clipExtent = m.clipExtent,\n      x0 = null, y0, x1, y1; // clip extent\n\n  m.scale = function(_) {\n    return arguments.length ? (scale(_), reclip()) : scale();\n  };\n\n  m.translate = function(_) {\n    return arguments.length ? (translate(_), reclip()) : translate();\n  };\n\n  m.center = function(_) {\n    return arguments.length ? (center(_), reclip()) : center();\n  };\n\n  m.clipExtent = function(_) {\n    return arguments.length ? ((_ == null ? x0 = y0 = x1 = y1 = null : (x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1])), reclip()) : x0 == null ? null : [[x0, y0], [x1, y1]];\n  };\n\n  function reclip() {\n    var k = pi * scale(),\n        t = m(rotation(m.rotate()).invert([0, 0]));\n    return clipExtent(x0 == null\n        ? [[t[0] - k, t[1] - k], [t[0] + k, t[1] + k]] : project === mercatorRaw\n        ? [[Math.max(t[0] - k, x0), y0], [Math.min(t[0] + k, x1), y1]]\n        : [[x0, Math.max(t[1] - k, y0)], [x1, Math.min(t[1] + k, y1)]]);\n  }\n\n  return reclip();\n}\n", "import {abs, atan, atan2, cos, epsilon, halfPi, log, pi, pow, sign, sin, sqrt, tan} from \"../math.js\";\nimport {conicProjection} from \"./conic.js\";\nimport {mercatorRaw} from \"./mercator.js\";\n\nfunction tany(y) {\n  return tan((halfPi + y) / 2);\n}\n\nexport function conicConformalRaw(y0, y1) {\n  var cy0 = cos(y0),\n      n = y0 === y1 ? sin(y0) : log(cy0 / cos(y1)) / log(tany(y1) / tany(y0)),\n      f = cy0 * pow(tany(y0), n) / n;\n\n  if (!n) return mercatorRaw;\n\n  function project(x, y) {\n    if (f > 0) { if (y < -halfPi + epsilon) y = -halfPi + epsilon; }\n    else { if (y > halfPi - epsilon) y = halfPi - epsilon; }\n    var r = f / pow(tany(y), n);\n    return [r * sin(n * x), f - r * cos(n * x)];\n  }\n\n  project.invert = function(x, y) {\n    var fy = f - y, r = sign(n) * sqrt(x * x + fy * fy),\n      l = atan2(x, abs(fy)) * sign(fy);\n    if (fy * n < 0)\n      l -= pi * sign(x) * sign(fy);\n    return [l / n, 2 * atan(pow(f / r, 1 / n)) - halfPi];\n  };\n\n  return project;\n}\n\nexport default function() {\n  return conicProjection(conicConformalRaw)\n      .scale(109.5)\n      .parallels([30, 30]);\n}\n", "import projection from \"./index.js\";\n\nexport function equirectangularRaw(lambda, phi) {\n  return [lambda, phi];\n}\n\nequirectangularRaw.invert = equirectangularRaw;\n\nexport default function() {\n  return projection(equirectangularRaw)\n      .scale(152.63);\n}\n", "import {abs, atan2, cos, epsilon, pi, sign, sin, sqrt} from \"../math.js\";\nimport {conicProjection} from \"./conic.js\";\nimport {equirectangularRaw} from \"./equirectangular.js\";\n\nexport function conicEquidistantRaw(y0, y1) {\n  var cy0 = cos(y0),\n      n = y0 === y1 ? sin(y0) : (cy0 - cos(y1)) / (y1 - y0),\n      g = cy0 / n + y0;\n\n  if (abs(n) < epsilon) return equirectangularRaw;\n\n  function project(x, y) {\n    var gy = g - y, nx = n * x;\n    return [gy * sin(nx), g - gy * cos(nx)];\n  }\n\n  project.invert = function(x, y) {\n    var gy = g - y,\n        l = atan2(x, abs(gy)) * sign(gy);\n    if (gy * n < 0)\n      l -= pi * sign(x) * sign(gy);\n    return [l / n, g - sign(n) * sqrt(x * x + gy * gy)];\n  };\n\n  return project;\n}\n\nexport default function() {\n  return conicProjection(conicEquidistantRaw)\n      .scale(131.154)\n      .center([0, 13.9389]);\n}\n", "import projection from \"./index.js\";\nimport {abs, asin, cos, epsilon2, sin, sqrt} from \"../math.js\";\n\nvar A1 = 1.340264,\n    A2 = -0.081106,\n    A3 = 0.000893,\n    A4 = 0.003796,\n    M = sqrt(3) / 2,\n    iterations = 12;\n\nexport function equalEarthRaw(lambda, phi) {\n  var l = asin(M * sin(phi)), l2 = l * l, l6 = l2 * l2 * l2;\n  return [\n    lambda * cos(l) / (M * (A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2))),\n    l * (A1 + A2 * l2 + l6 * (A3 + A4 * l2))\n  ];\n}\n\nequalEarthRaw.invert = function(x, y) {\n  var l = y, l2 = l * l, l6 = l2 * l2 * l2;\n  for (var i = 0, delta, fy, fpy; i < iterations; ++i) {\n    fy = l * (A1 + A2 * l2 + l6 * (A3 + A4 * l2)) - y;\n    fpy = A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2);\n    l -= delta = fy / fpy, l2 = l * l, l6 = l2 * l2 * l2;\n    if (abs(delta) < epsilon2) break;\n  }\n  return [\n    M * x * (A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2)) / cos(l),\n    asin(sin(l) / M)\n  ];\n};\n\nexport default function() {\n  return projection(equalEarthRaw)\n      .scale(177.158);\n}\n", "import {atan, cos, sin} from \"../math.js\";\nimport {azimuthalInvert} from \"./azimuthal.js\";\nimport projection from \"./index.js\";\n\nexport function gnomonicRaw(x, y) {\n  var cy = cos(y), k = cos(x) * cy;\n  return [cy * sin(x) / k, sin(y) / k];\n}\n\ngnomonicRaw.invert = azimuthalInvert(atan);\n\nexport default function() {\n  return projection(gnomonicRaw)\n      .scale(144.049)\n      .clipAngle(60);\n}\n", "import clipRectangle from \"../clip/rectangle.js\";\nimport identity from \"../identity.js\";\nimport {transformer} from \"../transform.js\";\nimport {fitExtent, fitSize, fitWidth, fitHeight} from \"./fit.js\";\nimport {cos, degrees, radians, sin} from \"../math.js\";\n\nexport default function() {\n  var k = 1, tx = 0, ty = 0, sx = 1, sy = 1, // scale, translate and reflect\n      alpha = 0, ca, sa, // angle\n      x0 = null, y0, x1, y1, // clip extent\n      kx = 1, ky = 1,\n      transform = transformer({\n        point: function(x, y) {\n          var p = projection([x, y])\n          this.stream.point(p[0], p[1]);\n        }\n      }),\n      postclip = identity,\n      cache,\n      cacheStream;\n\n  function reset() {\n    kx = k * sx;\n    ky = k * sy;\n    cache = cacheStream = null;\n    return projection;\n  }\n\n  function projection (p) {\n    var x = p[0] * kx, y = p[1] * ky;\n    if (alpha) {\n      var t = y * ca - x * sa;\n      x = x * ca + y * sa;\n      y = t;\n    }    \n    return [x + tx, y + ty];\n  }\n  projection.invert = function(p) {\n    var x = p[0] - tx, y = p[1] - ty;\n    if (alpha) {\n      var t = y * ca + x * sa;\n      x = x * ca - y * sa;\n      y = t;\n    }\n    return [x / kx, y / ky];\n  };\n  projection.stream = function(stream) {\n    return cache && cacheStream === stream ? cache : cache = transform(postclip(cacheStream = stream));\n  };\n  projection.postclip = function(_) {\n    return arguments.length ? (postclip = _, x0 = y0 = x1 = y1 = null, reset()) : postclip;\n  };\n  projection.clipExtent = function(_) {\n    return arguments.length ? (postclip = _ == null ? (x0 = y0 = x1 = y1 = null, identity) : clipRectangle(x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1]), reset()) : x0 == null ? null : [[x0, y0], [x1, y1]];\n  };\n  projection.scale = function(_) {\n    return arguments.length ? (k = +_, reset()) : k;\n  };\n  projection.translate = function(_) {\n    return arguments.length ? (tx = +_[0], ty = +_[1], reset()) : [tx, ty];\n  }\n  projection.angle = function(_) {\n    return arguments.length ? (alpha = _ % 360 * radians, sa = sin(alpha), ca = cos(alpha), reset()) : alpha * degrees;\n  };\n  projection.reflectX = function(_) {\n    return arguments.length ? (sx = _ ? -1 : 1, reset()) : sx < 0;\n  };\n  projection.reflectY = function(_) {\n    return arguments.length ? (sy = _ ? -1 : 1, reset()) : sy < 0;\n  };\n  projection.fitExtent = function(extent, object) {\n    return fitExtent(projection, extent, object);\n  };\n  projection.fitSize = function(size, object) {\n    return fitSize(projection, size, object);\n  };\n  projection.fitWidth = function(width, object) {\n    return fitWidth(projection, width, object);\n  };\n  projection.fitHeight = function(height, object) {\n    return fitHeight(projection, height, object);\n  };\n\n  return projection;\n}\n", "import projection from \"./index.js\";\nimport {abs, epsilon} from \"../math.js\";\n\nexport function naturalEarth1Raw(lambda, phi) {\n  var phi2 = phi * phi, phi4 = phi2 * phi2;\n  return [\n    lambda * (0.8707 - 0.131979 * phi2 + phi4 * (-0.013791 + phi4 * (0.003971 * phi2 - 0.001529 * phi4))),\n    phi * (1.007226 + phi2 * (0.015085 + phi4 * (-0.044475 + 0.028874 * phi2 - 0.005916 * phi4)))\n  ];\n}\n\nnaturalEarth1Raw.invert = function(x, y) {\n  var phi = y, i = 25, delta;\n  do {\n    var phi2 = phi * phi, phi4 = phi2 * phi2;\n    phi -= delta = (phi * (1.007226 + phi2 * (0.015085 + phi4 * (-0.044475 + 0.028874 * phi2 - 0.005916 * phi4))) - y) /\n        (1.007226 + phi2 * (0.015085 * 3 + phi4 * (-0.044475 * 7 + 0.028874 * 9 * phi2 - 0.005916 * 11 * phi4)));\n  } while (abs(delta) > epsilon && --i > 0);\n  return [\n    x / (0.8707 + (phi2 = phi * phi) * (-0.131979 + phi2 * (-0.013791 + phi2 * phi2 * phi2 * (0.003971 - 0.001529 * phi2)))),\n    phi\n  ];\n};\n\nexport default function() {\n  return projection(naturalEarth1Raw)\n      .scale(175.295);\n}\n", "import {asin, cos, epsilon, sin} from \"../math.js\";\nimport {azimuthalInvert} from \"./azimuthal.js\";\nimport projection from \"./index.js\";\n\nexport function orthographicRaw(x, y) {\n  return [cos(y) * sin(x), sin(y)];\n}\n\northographicRaw.invert = azimuthalInvert(asin);\n\nexport default function() {\n  return projection(orthographicRaw)\n      .scale(249.5)\n      .clipAngle(90 + epsilon);\n}\n", "import {atan, cos, sin} from \"../math.js\";\nimport {azimuthalInvert} from \"./azimuthal.js\";\nimport projection from \"./index.js\";\n\nexport function stereographicRaw(x, y) {\n  var cy = cos(y), k = 1 + cos(x) * cy;\n  return [cy * sin(x) / k, sin(y) / k];\n}\n\nstereographicRaw.invert = azimuthalInvert(function(z) {\n  return 2 * atan(z);\n});\n\nexport default function() {\n  return projection(stereographicRaw)\n      .scale(250)\n      .clipAngle(142);\n}\n", "import {atan, exp, halfPi, log, tan} from \"../math.js\";\nimport {mercatorProjection} from \"./mercator.js\";\n\nexport function transverseMercatorRaw(lambda, phi) {\n  return [log(tan((halfPi + phi) / 2)), -lambda];\n}\n\ntransverseMercatorRaw.invert = function(x, y) {\n  return [-y, 2 * atan(exp(x)) - halfPi];\n};\n\nexport default function() {\n  var m = mercatorProjection(transverseMercatorRaw),\n      center = m.center,\n      rotate = m.rotate;\n\n  m.center = function(_) {\n    return arguments.length ? center([-_[1], _[0]]) : (_ = center(), [_[1], -_[0]]);\n  };\n\n  m.rotate = function(_) {\n    return arguments.length ? rotate([_[0], _[1], _.length > 2 ? _[2] + 90 : 90]) : (_ = rotate(), [_[0], _[1], _[2] - 90]);\n  };\n\n  return rotate([0, 0, 90])\n      .scale(159.155);\n}\n", "const epsilon = 1e-6;\n\nexport default class Path {\n  constructor() {\n    this._x0 = this._y0 = // start of current subpath\n    this._x1 = this._y1 = null; // end of current subpath\n    this._ = \"\";\n  }\n  moveTo(x, y) {\n    this._ += `M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}`;\n  }\n  closePath() {\n    if (this._x1 !== null) {\n      this._x1 = this._x0, this._y1 = this._y0;\n      this._ += \"Z\";\n    }\n  }\n  lineTo(x, y) {\n    this._ += `L${this._x1 = +x},${this._y1 = +y}`;\n  }\n  arc(x, y, r) {\n    x = +x, y = +y, r = +r;\n    const x0 = x + r;\n    const y0 = y;\n    if (r < 0) throw new Error(\"negative radius\");\n    if (this._x1 === null) this._ += `M${x0},${y0}`;\n    else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) this._ += \"L\" + x0 + \",\" + y0;\n    if (!r) return;\n    this._ += `A${r},${r},0,1,1,${x - r},${y}A${r},${r},0,1,1,${this._x1 = x0},${this._y1 = y0}`;\n  }\n  rect(x, y, w, h) {\n    this._ += `M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}h${+w}v${+h}h${-w}Z`;\n  }\n  value() {\n    return this._ || null;\n  }\n}\n", "export default class Polygon {\n  constructor() {\n    this._ = [];\n  }\n  moveTo(x, y) {\n    this._.push([x, y]);\n  }\n  closePath() {\n    this._.push(this._[0].slice());\n  }\n  lineTo(x, y) {\n    this._.push([x, y]);\n  }\n  value() {\n    return this._.length ? this._ : null;\n  }\n}\n", "import Path from \"./path.js\";\nimport Polygon from \"./polygon.js\";\n\nexport default class Voronoi {\n  constructor(delaunay, [xmin, ymin, xmax, ymax] = [0, 0, 960, 500]) {\n    if (!((xmax = +xmax) >= (xmin = +xmin)) || !((ymax = +ymax) >= (ymin = +ymin))) throw new Error(\"invalid bounds\");\n    this.delaunay = delaunay;\n    this._circumcenters = new Float64Array(delaunay.points.length * 2);\n    this.vectors = new Float64Array(delaunay.points.length * 2);\n    this.xmax = xmax, this.xmin = xmin;\n    this.ymax = ymax, this.ymin = ymin;\n    this._init();\n  }\n  update() {\n    this.delaunay.update();\n    this._init();\n    return this;\n  }\n  _init() {\n    const {delaunay: {points, hull, triangles}, vectors} = this;\n    let bx, by; // lazily computed barycenter of the hull\n\n    // Compute circumcenters.\n    const circumcenters = this.circumcenters = this._circumcenters.subarray(0, triangles.length / 3 * 2);\n    for (let i = 0, j = 0, n = triangles.length, x, y; i < n; i += 3, j += 2) {\n      const t1 = triangles[i] * 2;\n      const t2 = triangles[i + 1] * 2;\n      const t3 = triangles[i + 2] * 2;\n      const x1 = points[t1];\n      const y1 = points[t1 + 1];\n      const x2 = points[t2];\n      const y2 = points[t2 + 1];\n      const x3 = points[t3];\n      const y3 = points[t3 + 1];\n\n      const dx = x2 - x1;\n      const dy = y2 - y1;\n      const ex = x3 - x1;\n      const ey = y3 - y1;\n      const ab = (dx * ey - dy * ex) * 2;\n\n      if (Math.abs(ab) < 1e-9) {\n        // For a degenerate triangle, the circumcenter is at the infinity, in a\n        // direction orthogonal to the halfedge and away from the “center” of\n        // the diagram <bx, by>, defined as the hull’s barycenter.\n        if (bx === undefined) {\n          bx = by = 0;\n          for (const i of hull) bx += points[i * 2], by += points[i * 2 + 1];\n          bx /= hull.length, by /= hull.length;\n        }\n        const a = 1e9 * Math.sign((bx - x1) * ey - (by - y1) * ex);\n        x = (x1 + x3) / 2 - a * ey;\n        y = (y1 + y3) / 2 + a * ex;\n      } else {\n        const d = 1 / ab;\n        const bl = dx * dx + dy * dy;\n        const cl = ex * ex + ey * ey;\n        x = x1 + (ey * bl - dy * cl) * d;\n        y = y1 + (dx * cl - ex * bl) * d;\n      }\n      circumcenters[j] = x;\n      circumcenters[j + 1] = y;\n    }\n\n    // Compute exterior cell rays.\n    let h = hull[hull.length - 1];\n    let p0, p1 = h * 4;\n    let x0, x1 = points[2 * h];\n    let y0, y1 = points[2 * h + 1];\n    vectors.fill(0);\n    for (let i = 0; i < hull.length; ++i) {\n      h = hull[i];\n      p0 = p1, x0 = x1, y0 = y1;\n      p1 = h * 4, x1 = points[2 * h], y1 = points[2 * h + 1];\n      vectors[p0 + 2] = vectors[p1] = y0 - y1;\n      vectors[p0 + 3] = vectors[p1 + 1] = x1 - x0;\n    }\n  }\n  render(context) {\n    const buffer = context == null ? context = new Path : undefined;\n    const {delaunay: {halfedges, inedges, hull}, circumcenters, vectors} = this;\n    if (hull.length <= 1) return null;\n    for (let i = 0, n = halfedges.length; i < n; ++i) {\n      const j = halfedges[i];\n      if (j < i) continue;\n      const ti = Math.floor(i / 3) * 2;\n      const tj = Math.floor(j / 3) * 2;\n      const xi = circumcenters[ti];\n      const yi = circumcenters[ti + 1];\n      const xj = circumcenters[tj];\n      const yj = circumcenters[tj + 1];\n      this._renderSegment(xi, yi, xj, yj, context);\n    }\n    let h0, h1 = hull[hull.length - 1];\n    for (let i = 0; i < hull.length; ++i) {\n      h0 = h1, h1 = hull[i];\n      const t = Math.floor(inedges[h1] / 3) * 2;\n      const x = circumcenters[t];\n      const y = circumcenters[t + 1];\n      const v = h0 * 4;\n      const p = this._project(x, y, vectors[v + 2], vectors[v + 3]);\n      if (p) this._renderSegment(x, y, p[0], p[1], context);\n    }\n    return buffer && buffer.value();\n  }\n  renderBounds(context) {\n    const buffer = context == null ? context = new Path : undefined;\n    context.rect(this.xmin, this.ymin, this.xmax - this.xmin, this.ymax - this.ymin);\n    return buffer && buffer.value();\n  }\n  renderCell(i, context) {\n    const buffer = context == null ? context = new Path : undefined;\n    const points = this._clip(i);\n    if (points === null || !points.length) return;\n    context.moveTo(points[0], points[1]);\n    let n = points.length;\n    while (points[0] === points[n-2] && points[1] === points[n-1] && n > 1) n -= 2;\n    for (let i = 2; i < n; i += 2) {\n      if (points[i] !== points[i-2] || points[i+1] !== points[i-1])\n        context.lineTo(points[i], points[i + 1]);\n    }\n    context.closePath();\n    return buffer && buffer.value();\n  }\n  *cellPolygons() {\n    const {delaunay: {points}} = this;\n    for (let i = 0, n = points.length / 2; i < n; ++i) {\n      const cell = this.cellPolygon(i);\n      if (cell) cell.index = i, yield cell;\n    }\n  }\n  cellPolygon(i) {\n    const polygon = new Polygon;\n    this.renderCell(i, polygon);\n    return polygon.value();\n  }\n  _renderSegment(x0, y0, x1, y1, context) {\n    let S;\n    const c0 = this._regioncode(x0, y0);\n    const c1 = this._regioncode(x1, y1);\n    if (c0 === 0 && c1 === 0) {\n      context.moveTo(x0, y0);\n      context.lineTo(x1, y1);\n    } else if (S = this._clipSegment(x0, y0, x1, y1, c0, c1)) {\n      context.moveTo(S[0], S[1]);\n      context.lineTo(S[2], S[3]);\n    }\n  }\n  contains(i, x, y) {\n    if ((x = +x, x !== x) || (y = +y, y !== y)) return false;\n    return this.delaunay._step(i, x, y) === i;\n  }\n  *neighbors(i) {\n    const ci = this._clip(i);\n    if (ci) for (const j of this.delaunay.neighbors(i)) {\n      const cj = this._clip(j);\n      // find the common edge\n      if (cj) loop: for (let ai = 0, li = ci.length; ai < li; ai += 2) {\n        for (let aj = 0, lj = cj.length; aj < lj; aj += 2) {\n          if (ci[ai] === cj[aj]\n              && ci[ai + 1] === cj[aj + 1]\n              && ci[(ai + 2) % li] === cj[(aj + lj - 2) % lj]\n              && ci[(ai + 3) % li] === cj[(aj + lj - 1) % lj]) {\n            yield j;\n            break loop;\n          }\n        }\n      }\n    }\n  }\n  _cell(i) {\n    const {circumcenters, delaunay: {inedges, halfedges, triangles}} = this;\n    const e0 = inedges[i];\n    if (e0 === -1) return null; // coincident point\n    const points = [];\n    let e = e0;\n    do {\n      const t = Math.floor(e / 3);\n      points.push(circumcenters[t * 2], circumcenters[t * 2 + 1]);\n      e = e % 3 === 2 ? e - 2 : e + 1;\n      if (triangles[e] !== i) break; // bad triangulation\n      e = halfedges[e];\n    } while (e !== e0 && e !== -1);\n    return points;\n  }\n  _clip(i) {\n    // degenerate case (1 valid point: return the box)\n    if (i === 0 && this.delaunay.hull.length === 1) {\n      return [this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax, this.xmin, this.ymin];\n    }\n    const points = this._cell(i);\n    if (points === null) return null;\n    const {vectors: V} = this;\n    const v = i * 4;\n    return this._simplify(V[v] || V[v + 1]\n        ? this._clipInfinite(i, points, V[v], V[v + 1], V[v + 2], V[v + 3])\n        : this._clipFinite(i, points));\n  }\n  _clipFinite(i, points) {\n    const n = points.length;\n    let P = null;\n    let x0, y0, x1 = points[n - 2], y1 = points[n - 1];\n    let c0, c1 = this._regioncode(x1, y1);\n    let e0, e1 = 0;\n    for (let j = 0; j < n; j += 2) {\n      x0 = x1, y0 = y1, x1 = points[j], y1 = points[j + 1];\n      c0 = c1, c1 = this._regioncode(x1, y1);\n      if (c0 === 0 && c1 === 0) {\n        e0 = e1, e1 = 0;\n        if (P) P.push(x1, y1);\n        else P = [x1, y1];\n      } else {\n        let S, sx0, sy0, sx1, sy1;\n        if (c0 === 0) {\n          if ((S = this._clipSegment(x0, y0, x1, y1, c0, c1)) === null) continue;\n          [sx0, sy0, sx1, sy1] = S;\n        } else {\n          if ((S = this._clipSegment(x1, y1, x0, y0, c1, c0)) === null) continue;\n          [sx1, sy1, sx0, sy0] = S;\n          e0 = e1, e1 = this._edgecode(sx0, sy0);\n          if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n          if (P) P.push(sx0, sy0);\n          else P = [sx0, sy0];\n        }\n        e0 = e1, e1 = this._edgecode(sx1, sy1);\n        if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n        if (P) P.push(sx1, sy1);\n        else P = [sx1, sy1];\n      }\n    }\n    if (P) {\n      e0 = e1, e1 = this._edgecode(P[0], P[1]);\n      if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n    } else if (this.contains(i, (this.xmin + this.xmax) / 2, (this.ymin + this.ymax) / 2)) {\n      return [this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax, this.xmin, this.ymin];\n    }\n    return P;\n  }\n  _clipSegment(x0, y0, x1, y1, c0, c1) {\n    // for more robustness, always consider the segment in the same order\n    const flip = c0 < c1;\n    if (flip) [x0, y0, x1, y1, c0, c1] = [x1, y1, x0, y0, c1, c0];\n    while (true) {\n      if (c0 === 0 && c1 === 0) return flip ? [x1, y1, x0, y0] : [x0, y0, x1, y1];\n      if (c0 & c1) return null;\n      let x, y, c = c0 || c1;\n      if (c & 0b1000) x = x0 + (x1 - x0) * (this.ymax - y0) / (y1 - y0), y = this.ymax;\n      else if (c & 0b0100) x = x0 + (x1 - x0) * (this.ymin - y0) / (y1 - y0), y = this.ymin;\n      else if (c & 0b0010) y = y0 + (y1 - y0) * (this.xmax - x0) / (x1 - x0), x = this.xmax;\n      else y = y0 + (y1 - y0) * (this.xmin - x0) / (x1 - x0), x = this.xmin;\n      if (c0) x0 = x, y0 = y, c0 = this._regioncode(x0, y0);\n      else x1 = x, y1 = y, c1 = this._regioncode(x1, y1);\n    }\n  }\n  _clipInfinite(i, points, vx0, vy0, vxn, vyn) {\n    let P = Array.from(points), p;\n    if (p = this._project(P[0], P[1], vx0, vy0)) P.unshift(p[0], p[1]);\n    if (p = this._project(P[P.length - 2], P[P.length - 1], vxn, vyn)) P.push(p[0], p[1]);\n    if (P = this._clipFinite(i, P)) {\n      for (let j = 0, n = P.length, c0, c1 = this._edgecode(P[n - 2], P[n - 1]); j < n; j += 2) {\n        c0 = c1, c1 = this._edgecode(P[j], P[j + 1]);\n        if (c0 && c1) j = this._edge(i, c0, c1, P, j), n = P.length;\n      }\n    } else if (this.contains(i, (this.xmin + this.xmax) / 2, (this.ymin + this.ymax) / 2)) {\n      P = [this.xmin, this.ymin, this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax];\n    }\n    return P;\n  }\n  _edge(i, e0, e1, P, j) {\n    while (e0 !== e1) {\n      let x, y;\n      switch (e0) {\n        case 0b0101: e0 = 0b0100; continue; // top-left\n        case 0b0100: e0 = 0b0110, x = this.xmax, y = this.ymin; break; // top\n        case 0b0110: e0 = 0b0010; continue; // top-right\n        case 0b0010: e0 = 0b1010, x = this.xmax, y = this.ymax; break; // right\n        case 0b1010: e0 = 0b1000; continue; // bottom-right\n        case 0b1000: e0 = 0b1001, x = this.xmin, y = this.ymax; break; // bottom\n        case 0b1001: e0 = 0b0001; continue; // bottom-left\n        case 0b0001: e0 = 0b0101, x = this.xmin, y = this.ymin; break; // left\n      }\n      // Note: this implicitly checks for out of bounds: if P[j] or P[j+1] are\n      // undefined, the conditional statement will be executed.\n      if ((P[j] !== x || P[j + 1] !== y) && this.contains(i, x, y)) {\n        P.splice(j, 0, x, y), j += 2;\n      }\n    }\n    return j;\n  }\n  _project(x0, y0, vx, vy) {\n    let t = Infinity, c, x, y;\n    if (vy < 0) { // top\n      if (y0 <= this.ymin) return null;\n      if ((c = (this.ymin - y0) / vy) < t) y = this.ymin, x = x0 + (t = c) * vx;\n    } else if (vy > 0) { // bottom\n      if (y0 >= this.ymax) return null;\n      if ((c = (this.ymax - y0) / vy) < t) y = this.ymax, x = x0 + (t = c) * vx;\n    }\n    if (vx > 0) { // right\n      if (x0 >= this.xmax) return null;\n      if ((c = (this.xmax - x0) / vx) < t) x = this.xmax, y = y0 + (t = c) * vy;\n    } else if (vx < 0) { // left\n      if (x0 <= this.xmin) return null;\n      if ((c = (this.xmin - x0) / vx) < t) x = this.xmin, y = y0 + (t = c) * vy;\n    }\n    return [x, y];\n  }\n  _edgecode(x, y) {\n    return (x === this.xmin ? 0b0001\n        : x === this.xmax ? 0b0010 : 0b0000)\n        | (y === this.ymin ? 0b0100\n        : y === this.ymax ? 0b1000 : 0b0000);\n  }\n  _regioncode(x, y) {\n    return (x < this.xmin ? 0b0001\n        : x > this.xmax ? 0b0010 : 0b0000)\n        | (y < this.ymin ? 0b0100\n        : y > this.ymax ? 0b1000 : 0b0000);\n  }\n  _simplify(P) {\n    if (P && P.length > 4) {\n      for (let i = 0; i < P.length; i+= 2) {\n        const j = (i + 2) % P.length, k = (i + 4) % P.length;\n        if (P[i] === P[j] && P[j] === P[k] || P[i + 1] === P[j + 1] && P[j + 1] === P[k + 1]) {\n          P.splice(j, 2), i -= 2;\n        }\n      }\n      if (!P.length) P = null;\n    }\n    return P;\n  }\n}\n", "export const epsilon = 1.1102230246251565e-16;\nexport const splitter = 134217729;\nexport const resulterrbound = (3 + 8 * epsilon) * epsilon;\n\n// fast_expansion_sum_zeroelim routine from oritinal code\nexport function sum(elen, e, flen, f, h) {\n    let Q, Qnew, hh, bvirt;\n    let enow = e[0];\n    let fnow = f[0];\n    let eindex = 0;\n    let findex = 0;\n    if ((fnow > enow) === (fnow > -enow)) {\n        Q = enow;\n        enow = e[++eindex];\n    } else {\n        Q = fnow;\n        fnow = f[++findex];\n    }\n    let hindex = 0;\n    if (eindex < elen && findex < flen) {\n        if ((fnow > enow) === (fnow > -enow)) {\n            Qnew = enow + Q;\n            hh = Q - (Qnew - enow);\n            enow = e[++eindex];\n        } else {\n            Qnew = fnow + Q;\n            hh = Q - (Qnew - fnow);\n            fnow = f[++findex];\n        }\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n        while (eindex < elen && findex < flen) {\n            if ((fnow > enow) === (fnow > -enow)) {\n                Qnew = Q + enow;\n                bvirt = Qnew - Q;\n                hh = Q - (Qnew - bvirt) + (enow - bvirt);\n                enow = e[++eindex];\n            } else {\n                Qnew = Q + fnow;\n                bvirt = Qnew - Q;\n                hh = Q - (Qnew - bvirt) + (fnow - bvirt);\n                fnow = f[++findex];\n            }\n            Q = Qnew;\n            if (hh !== 0) {\n                h[hindex++] = hh;\n            }\n        }\n    }\n    while (eindex < elen) {\n        Qnew = Q + enow;\n        bvirt = Qnew - Q;\n        hh = Q - (Qnew - bvirt) + (enow - bvirt);\n        enow = e[++eindex];\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    while (findex < flen) {\n        Qnew = Q + fnow;\n        bvirt = Qnew - Q;\n        hh = Q - (Qnew - bvirt) + (fnow - bvirt);\n        fnow = f[++findex];\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    if (Q !== 0 || hindex === 0) {\n        h[hindex++] = Q;\n    }\n    return hindex;\n}\n\nexport function sum_three(alen, a, blen, b, clen, c, tmp, out) {\n    return sum(sum(alen, a, blen, b, tmp), tmp, clen, c, out);\n}\n\n// scale_expansion_zeroelim routine from oritinal code\nexport function scale(elen, e, b, h) {\n    let Q, sum, hh, product1, product0;\n    let bvirt, c, ahi, alo, bhi, blo;\n\n    c = splitter * b;\n    bhi = c - (c - b);\n    blo = b - bhi;\n    let enow = e[0];\n    Q = enow * b;\n    c = splitter * enow;\n    ahi = c - (c - enow);\n    alo = enow - ahi;\n    hh = alo * blo - (Q - ahi * bhi - alo * bhi - ahi * blo);\n    let hindex = 0;\n    if (hh !== 0) {\n        h[hindex++] = hh;\n    }\n    for (let i = 1; i < elen; i++) {\n        enow = e[i];\n        product1 = enow * b;\n        c = splitter * enow;\n        ahi = c - (c - enow);\n        alo = enow - ahi;\n        product0 = alo * blo - (product1 - ahi * bhi - alo * bhi - ahi * blo);\n        sum = Q + product0;\n        bvirt = sum - Q;\n        hh = Q - (sum - bvirt) + (product0 - bvirt);\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n        Q = product1 + sum;\n        hh = sum - (Q - product1);\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    if (Q !== 0 || hindex === 0) {\n        h[hindex++] = Q;\n    }\n    return hindex;\n}\n\nexport function negate(elen, e) {\n    for (let i = 0; i < elen; i++) e[i] = -e[i];\n    return elen;\n}\n\nexport function estimate(elen, e) {\n    let Q = e[0];\n    for (let i = 1; i < elen; i++) Q += e[i];\n    return Q;\n}\n\nexport function vec(n) {\n    return new Float64Array(n);\n}\n", "import {epsilon, splitter, resulterrbound, estimate, vec, sum} from './util.js';\n\nconst ccwerrboundA = (3 + 16 * epsilon) * epsilon;\nconst ccwerrboundB = (2 + 12 * epsilon) * epsilon;\nconst ccwerrboundC = (9 + 64 * epsilon) * epsilon * epsilon;\n\nconst B = vec(4);\nconst C1 = vec(8);\nconst C2 = vec(12);\nconst D = vec(16);\nconst u = vec(4);\n\nfunction orient2dadapt(ax, ay, bx, by, cx, cy, detsum) {\n    let acxtail, acytail, bcxtail, bcytail;\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n\n    const acx = ax - cx;\n    const bcx = bx - cx;\n    const acy = ay - cy;\n    const bcy = by - cy;\n\n    s1 = acx * bcy;\n    c = splitter * acx;\n    ahi = c - (c - acx);\n    alo = acx - ahi;\n    c = splitter * bcy;\n    bhi = c - (c - bcy);\n    blo = bcy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acy * bcx;\n    c = splitter * acy;\n    ahi = c - (c - acy);\n    alo = acy - ahi;\n    c = splitter * bcx;\n    bhi = c - (c - bcx);\n    blo = bcx - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    B[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    B[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    B[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    B[3] = u3;\n\n    let det = estimate(4, B);\n    let errbound = ccwerrboundB * detsum;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - acx;\n    acxtail = ax - (acx + bvirt) + (bvirt - cx);\n    bvirt = bx - bcx;\n    bcxtail = bx - (bcx + bvirt) + (bvirt - cx);\n    bvirt = ay - acy;\n    acytail = ay - (acy + bvirt) + (bvirt - cy);\n    bvirt = by - bcy;\n    bcytail = by - (bcy + bvirt) + (bvirt - cy);\n\n    if (acxtail === 0 && acytail === 0 && bcxtail === 0 && bcytail === 0) {\n        return det;\n    }\n\n    errbound = ccwerrboundC * detsum + resulterrbound * Math.abs(det);\n    det += (acx * bcytail + bcy * acxtail) - (acy * bcxtail + bcx * acytail);\n    if (det >= errbound || -det >= errbound) return det;\n\n    s1 = acxtail * bcy;\n    c = splitter * acxtail;\n    ahi = c - (c - acxtail);\n    alo = acxtail - ahi;\n    c = splitter * bcy;\n    bhi = c - (c - bcy);\n    blo = bcy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acytail * bcx;\n    c = splitter * acytail;\n    ahi = c - (c - acytail);\n    alo = acytail - ahi;\n    c = splitter * bcx;\n    bhi = c - (c - bcx);\n    blo = bcx - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const C1len = sum(4, B, 4, u, C1);\n\n    s1 = acx * bcytail;\n    c = splitter * acx;\n    ahi = c - (c - acx);\n    alo = acx - ahi;\n    c = splitter * bcytail;\n    bhi = c - (c - bcytail);\n    blo = bcytail - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acy * bcxtail;\n    c = splitter * acy;\n    ahi = c - (c - acy);\n    alo = acy - ahi;\n    c = splitter * bcxtail;\n    bhi = c - (c - bcxtail);\n    blo = bcxtail - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const C2len = sum(C1len, C1, 4, u, C2);\n\n    s1 = acxtail * bcytail;\n    c = splitter * acxtail;\n    ahi = c - (c - acxtail);\n    alo = acxtail - ahi;\n    c = splitter * bcytail;\n    bhi = c - (c - bcytail);\n    blo = bcytail - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acytail * bcxtail;\n    c = splitter * acytail;\n    ahi = c - (c - acytail);\n    alo = acytail - ahi;\n    c = splitter * bcxtail;\n    bhi = c - (c - bcxtail);\n    blo = bcxtail - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const Dlen = sum(C2len, C2, 4, u, D);\n\n    return D[Dlen - 1];\n}\n\nexport function orient2d(ax, ay, bx, by, cx, cy) {\n    const detleft = (ay - cy) * (bx - cx);\n    const detright = (ax - cx) * (by - cy);\n    const det = detleft - detright;\n\n    const detsum = Math.abs(detleft + detright);\n    if (Math.abs(det) >= ccwerrboundA * detsum) return det;\n\n    return -orient2dadapt(ax, ay, bx, by, cx, cy, detsum);\n}\n\nexport function orient2dfast(ax, ay, bx, by, cx, cy) {\n    return (ay - cy) * (bx - cx) - (ax - cx) * (by - cy);\n}\n", "import {epsilon, splitter, resulterrbound, estimate, vec, sum, scale} from './util.js';\n\nconst o3derrboundA = (7 + 56 * epsilon) * epsilon;\nconst o3derrboundB = (3 + 28 * epsilon) * epsilon;\nconst o3derrboundC = (26 + 288 * epsilon) * epsilon * epsilon;\n\nconst bc = vec(4);\nconst ca = vec(4);\nconst ab = vec(4);\nconst at_b = vec(4);\nconst at_c = vec(4);\nconst bt_c = vec(4);\nconst bt_a = vec(4);\nconst ct_a = vec(4);\nconst ct_b = vec(4);\nconst bct = vec(8);\nconst cat = vec(8);\nconst abt = vec(8);\nconst u = vec(4);\n\nconst _8 = vec(8);\nconst _8b = vec(8);\nconst _16 = vec(8);\nconst _12 = vec(12);\n\nlet fin = vec(192);\nlet fin2 = vec(192);\n\nfunction finadd(finlen, alen, a) {\n    finlen = sum(finlen, fin, alen, a, fin2);\n    const tmp = fin; fin = fin2; fin2 = tmp;\n    return finlen;\n}\n\nfunction tailinit(xtail, ytail, ax, ay, bx, by, a, b) {\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, t1, t0, u3, negate;\n    if (xtail === 0) {\n        if (ytail === 0) {\n            a[0] = 0;\n            b[0] = 0;\n            return 1;\n        } else {\n            negate = -ytail;\n            s1 = negate * ax;\n            c = splitter * negate;\n            ahi = c - (c - negate);\n            alo = negate - ahi;\n            c = splitter * ax;\n            bhi = c - (c - ax);\n            blo = ax - bhi;\n            a[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            a[1] = s1;\n            s1 = ytail * bx;\n            c = splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = splitter * bx;\n            bhi = c - (c - bx);\n            blo = bx - bhi;\n            b[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            b[1] = s1;\n            return 2;\n        }\n    } else {\n        if (ytail === 0) {\n            s1 = xtail * ay;\n            c = splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = splitter * ay;\n            bhi = c - (c - ay);\n            blo = ay - bhi;\n            a[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            a[1] = s1;\n            negate = -xtail;\n            s1 = negate * by;\n            c = splitter * negate;\n            ahi = c - (c - negate);\n            alo = negate - ahi;\n            c = splitter * by;\n            bhi = c - (c - by);\n            blo = by - bhi;\n            b[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            b[1] = s1;\n            return 2;\n        } else {\n            s1 = xtail * ay;\n            c = splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = splitter * ay;\n            bhi = c - (c - ay);\n            blo = ay - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = ytail * ax;\n            c = splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = splitter * ax;\n            bhi = c - (c - ax);\n            blo = ax - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            a[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            a[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            a[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            a[3] = u3;\n            s1 = ytail * bx;\n            c = splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = splitter * bx;\n            bhi = c - (c - bx);\n            blo = bx - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = xtail * by;\n            c = splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = splitter * by;\n            bhi = c - (c - by);\n            blo = by - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            b[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            b[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            b[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            b[3] = u3;\n            return 4;\n        }\n    }\n}\n\nfunction tailadd(finlen, a, b, k, z) {\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, u3;\n    s1 = a * b;\n    c = splitter * a;\n    ahi = c - (c - a);\n    alo = a - ahi;\n    c = splitter * b;\n    bhi = c - (c - b);\n    blo = b - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    c = splitter * k;\n    bhi = c - (c - k);\n    blo = k - bhi;\n    _i = s0 * k;\n    c = splitter * s0;\n    ahi = c - (c - s0);\n    alo = s0 - ahi;\n    u[0] = alo * blo - (_i - ahi * bhi - alo * bhi - ahi * blo);\n    _j = s1 * k;\n    c = splitter * s1;\n    ahi = c - (c - s1);\n    alo = s1 - ahi;\n    _0 = alo * blo - (_j - ahi * bhi - alo * bhi - ahi * blo);\n    _k = _i + _0;\n    bvirt = _k - _i;\n    u[1] = _i - (_k - bvirt) + (_0 - bvirt);\n    u3 = _j + _k;\n    u[2] = _k - (u3 - _j);\n    u[3] = u3;\n    finlen = finadd(finlen, 4, u);\n    if (z !== 0) {\n        c = splitter * z;\n        bhi = c - (c - z);\n        blo = z - bhi;\n        _i = s0 * z;\n        c = splitter * s0;\n        ahi = c - (c - s0);\n        alo = s0 - ahi;\n        u[0] = alo * blo - (_i - ahi * bhi - alo * bhi - ahi * blo);\n        _j = s1 * z;\n        c = splitter * s1;\n        ahi = c - (c - s1);\n        alo = s1 - ahi;\n        _0 = alo * blo - (_j - ahi * bhi - alo * bhi - ahi * blo);\n        _k = _i + _0;\n        bvirt = _k - _i;\n        u[1] = _i - (_k - bvirt) + (_0 - bvirt);\n        u3 = _j + _k;\n        u[2] = _k - (u3 - _j);\n        u[3] = u3;\n        finlen = finadd(finlen, 4, u);\n    }\n    return finlen;\n}\n\nfunction orient3dadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, permanent) {\n    let finlen;\n    let adxtail, bdxtail, cdxtail;\n    let adytail, bdytail, cdytail;\n    let adztail, bdztail, cdztail;\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, t1, t0, u3;\n\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n\n    s1 = bdx * cdy;\n    c = splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cdx * bdy;\n    c = splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bc[3] = u3;\n    s1 = cdx * ady;\n    c = splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = adx * cdy;\n    c = splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ca[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ca[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ca[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ca[3] = u3;\n    s1 = adx * bdy;\n    c = splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bdx * ady;\n    c = splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ab[3] = u3;\n\n    finlen = sum(\n        sum(\n            scale(4, bc, adz, _8), _8,\n            scale(4, ca, bdz, _8b), _8b, _16), _16,\n        scale(4, ab, cdz, _8), _8, fin);\n\n    let det = estimate(finlen, fin);\n    let errbound = o3derrboundB * permanent;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - adx;\n    adxtail = ax - (adx + bvirt) + (bvirt - dx);\n    bvirt = bx - bdx;\n    bdxtail = bx - (bdx + bvirt) + (bvirt - dx);\n    bvirt = cx - cdx;\n    cdxtail = cx - (cdx + bvirt) + (bvirt - dx);\n    bvirt = ay - ady;\n    adytail = ay - (ady + bvirt) + (bvirt - dy);\n    bvirt = by - bdy;\n    bdytail = by - (bdy + bvirt) + (bvirt - dy);\n    bvirt = cy - cdy;\n    cdytail = cy - (cdy + bvirt) + (bvirt - dy);\n    bvirt = az - adz;\n    adztail = az - (adz + bvirt) + (bvirt - dz);\n    bvirt = bz - bdz;\n    bdztail = bz - (bdz + bvirt) + (bvirt - dz);\n    bvirt = cz - cdz;\n    cdztail = cz - (cdz + bvirt) + (bvirt - dz);\n\n    if (adxtail === 0 && bdxtail === 0 && cdxtail === 0 &&\n        adytail === 0 && bdytail === 0 && cdytail === 0 &&\n        adztail === 0 && bdztail === 0 && cdztail === 0) {\n        return det;\n    }\n\n    errbound = o3derrboundC * permanent + resulterrbound * Math.abs(det);\n    det +=\n        adz * (bdx * cdytail + cdy * bdxtail - (bdy * cdxtail + cdx * bdytail)) + adztail * (bdx * cdy - bdy * cdx) +\n        bdz * (cdx * adytail + ady * cdxtail - (cdy * adxtail + adx * cdytail)) + bdztail * (cdx * ady - cdy * adx) +\n        cdz * (adx * bdytail + bdy * adxtail - (ady * bdxtail + bdx * adytail)) + cdztail * (adx * bdy - ady * bdx);\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    const at_len = tailinit(adxtail, adytail, bdx, bdy, cdx, cdy, at_b, at_c);\n    const bt_len = tailinit(bdxtail, bdytail, cdx, cdy, adx, ady, bt_c, bt_a);\n    const ct_len = tailinit(cdxtail, cdytail, adx, ady, bdx, bdy, ct_a, ct_b);\n\n    const bctlen = sum(bt_len, bt_c, ct_len, ct_b, bct);\n    finlen = finadd(finlen, scale(bctlen, bct, adz, _16), _16);\n\n    const catlen = sum(ct_len, ct_a, at_len, at_c, cat);\n    finlen = finadd(finlen, scale(catlen, cat, bdz, _16), _16);\n\n    const abtlen = sum(at_len, at_b, bt_len, bt_a, abt);\n    finlen = finadd(finlen, scale(abtlen, abt, cdz, _16), _16);\n\n    if (adztail !== 0) {\n        finlen = finadd(finlen, scale(4, bc, adztail, _12), _12);\n        finlen = finadd(finlen, scale(bctlen, bct, adztail, _16), _16);\n    }\n    if (bdztail !== 0) {\n        finlen = finadd(finlen, scale(4, ca, bdztail, _12), _12);\n        finlen = finadd(finlen, scale(catlen, cat, bdztail, _16), _16);\n    }\n    if (cdztail !== 0) {\n        finlen = finadd(finlen, scale(4, ab, cdztail, _12), _12);\n        finlen = finadd(finlen, scale(abtlen, abt, cdztail, _16), _16);\n    }\n\n    if (adxtail !== 0) {\n        if (bdytail !== 0) {\n            finlen = tailadd(finlen, adxtail, bdytail, cdz, cdztail);\n        }\n        if (cdytail !== 0) {\n            finlen = tailadd(finlen, -adxtail, cdytail, bdz, bdztail);\n        }\n    }\n    if (bdxtail !== 0) {\n        if (cdytail !== 0) {\n            finlen = tailadd(finlen, bdxtail, cdytail, adz, adztail);\n        }\n        if (adytail !== 0) {\n            finlen = tailadd(finlen, -bdxtail, adytail, cdz, cdztail);\n        }\n    }\n    if (cdxtail !== 0) {\n        if (adytail !== 0) {\n            finlen = tailadd(finlen, cdxtail, adytail, bdz, bdztail);\n        }\n        if (bdytail !== 0) {\n            finlen = tailadd(finlen, -cdxtail, bdytail, adz, adztail);\n        }\n    }\n\n    return fin[finlen - 1];\n}\n\nexport function orient3d(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n\n    const bdxcdy = bdx * cdy;\n    const cdxbdy = cdx * bdy;\n\n    const cdxady = cdx * ady;\n    const adxcdy = adx * cdy;\n\n    const adxbdy = adx * bdy;\n    const bdxady = bdx * ady;\n\n    const det =\n        adz * (bdxcdy - cdxbdy) +\n        bdz * (cdxady - adxcdy) +\n        cdz * (adxbdy - bdxady);\n\n    const permanent =\n        (Math.abs(bdxcdy) + Math.abs(cdxbdy)) * Math.abs(adz) +\n        (Math.abs(cdxady) + Math.abs(adxcdy)) * Math.abs(bdz) +\n        (Math.abs(adxbdy) + Math.abs(bdxady)) * Math.abs(cdz);\n\n    const errbound = o3derrboundA * permanent;\n    if (det > errbound || -det > errbound) {\n        return det;\n    }\n\n    return orient3dadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, permanent);\n}\n\nexport function orient3dfast(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n\n    return adx * (bdy * cdz - bdz * cdy) +\n        bdx * (cdy * adz - cdz * ady) +\n        cdx * (ady * bdz - adz * bdy);\n}\n", "import {epsilon, splitter, resulterrbound, estimate, vec, sum, sum_three, scale} from './util.js';\n\nconst iccerrboundA = (10 + 96 * epsilon) * epsilon;\nconst iccerrboundB = (4 + 48 * epsilon) * epsilon;\nconst iccerrboundC = (44 + 576 * epsilon) * epsilon * epsilon;\n\nconst bc = vec(4);\nconst ca = vec(4);\nconst ab = vec(4);\nconst aa = vec(4);\nconst bb = vec(4);\nconst cc = vec(4);\nconst u = vec(4);\nconst v = vec(4);\nconst axtbc = vec(8);\nconst aytbc = vec(8);\nconst bxtca = vec(8);\nconst bytca = vec(8);\nconst cxtab = vec(8);\nconst cytab = vec(8);\nconst abt = vec(8);\nconst bct = vec(8);\nconst cat = vec(8);\nconst abtt = vec(4);\nconst bctt = vec(4);\nconst catt = vec(4);\n\nconst _8 = vec(8);\nconst _16 = vec(16);\nconst _16b = vec(16);\nconst _16c = vec(16);\nconst _32 = vec(32);\nconst _32b = vec(32);\nconst _48 = vec(48);\nconst _64 = vec(64);\n\nlet fin = vec(1152);\nlet fin2 = vec(1152);\n\nfunction finadd(finlen, a, alen) {\n    finlen = sum(finlen, fin, a, alen, fin2);\n    const tmp = fin; fin = fin2; fin2 = tmp;\n    return finlen;\n}\n\nfunction incircleadapt(ax, ay, bx, by, cx, cy, dx, dy, permanent) {\n    let finlen;\n    let adxtail, bdxtail, cdxtail, adytail, bdytail, cdytail;\n    let axtbclen, aytbclen, bxtcalen, bytcalen, cxtablen, cytablen;\n    let abtlen, bctlen, catlen;\n    let abttlen, bcttlen, cattlen;\n    let n1, n0;\n\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n\n    s1 = bdx * cdy;\n    c = splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cdx * bdy;\n    c = splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bc[3] = u3;\n    s1 = cdx * ady;\n    c = splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = adx * cdy;\n    c = splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ca[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ca[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ca[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ca[3] = u3;\n    s1 = adx * bdy;\n    c = splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bdx * ady;\n    c = splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ab[3] = u3;\n\n    finlen = sum(\n        sum(\n            sum(\n                scale(scale(4, bc, adx, _8), _8, adx, _16), _16,\n                scale(scale(4, bc, ady, _8), _8, ady, _16b), _16b, _32), _32,\n            sum(\n                scale(scale(4, ca, bdx, _8), _8, bdx, _16), _16,\n                scale(scale(4, ca, bdy, _8), _8, bdy, _16b), _16b, _32b), _32b, _64), _64,\n        sum(\n            scale(scale(4, ab, cdx, _8), _8, cdx, _16), _16,\n            scale(scale(4, ab, cdy, _8), _8, cdy, _16b), _16b, _32), _32, fin);\n\n    let det = estimate(finlen, fin);\n    let errbound = iccerrboundB * permanent;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - adx;\n    adxtail = ax - (adx + bvirt) + (bvirt - dx);\n    bvirt = ay - ady;\n    adytail = ay - (ady + bvirt) + (bvirt - dy);\n    bvirt = bx - bdx;\n    bdxtail = bx - (bdx + bvirt) + (bvirt - dx);\n    bvirt = by - bdy;\n    bdytail = by - (bdy + bvirt) + (bvirt - dy);\n    bvirt = cx - cdx;\n    cdxtail = cx - (cdx + bvirt) + (bvirt - dx);\n    bvirt = cy - cdy;\n    cdytail = cy - (cdy + bvirt) + (bvirt - dy);\n    if (adxtail === 0 && bdxtail === 0 && cdxtail === 0 && adytail === 0 && bdytail === 0 && cdytail === 0) {\n        return det;\n    }\n\n    errbound = iccerrboundC * permanent + resulterrbound * Math.abs(det);\n    det += ((adx * adx + ady * ady) * ((bdx * cdytail + cdy * bdxtail) - (bdy * cdxtail + cdx * bdytail)) +\n        2 * (adx * adxtail + ady * adytail) * (bdx * cdy - bdy * cdx)) +\n        ((bdx * bdx + bdy * bdy) * ((cdx * adytail + ady * cdxtail) - (cdy * adxtail + adx * cdytail)) +\n        2 * (bdx * bdxtail + bdy * bdytail) * (cdx * ady - cdy * adx)) +\n        ((cdx * cdx + cdy * cdy) * ((adx * bdytail + bdy * adxtail) - (ady * bdxtail + bdx * adytail)) +\n        2 * (cdx * cdxtail + cdy * cdytail) * (adx * bdy - ady * bdx));\n\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    if (bdxtail !== 0 || bdytail !== 0 || cdxtail !== 0 || cdytail !== 0) {\n        s1 = adx * adx;\n        c = splitter * adx;\n        ahi = c - (c - adx);\n        alo = adx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = ady * ady;\n        c = splitter * ady;\n        ahi = c - (c - ady);\n        alo = ady - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        aa[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        aa[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        aa[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        aa[3] = u3;\n    }\n    if (cdxtail !== 0 || cdytail !== 0 || adxtail !== 0 || adytail !== 0) {\n        s1 = bdx * bdx;\n        c = splitter * bdx;\n        ahi = c - (c - bdx);\n        alo = bdx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = bdy * bdy;\n        c = splitter * bdy;\n        ahi = c - (c - bdy);\n        alo = bdy - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        bb[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        bb[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        bb[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        bb[3] = u3;\n    }\n    if (adxtail !== 0 || adytail !== 0 || bdxtail !== 0 || bdytail !== 0) {\n        s1 = cdx * cdx;\n        c = splitter * cdx;\n        ahi = c - (c - cdx);\n        alo = cdx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = cdy * cdy;\n        c = splitter * cdy;\n        ahi = c - (c - cdy);\n        alo = cdy - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        cc[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        cc[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        cc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        cc[3] = u3;\n    }\n\n    if (adxtail !== 0) {\n        axtbclen = scale(4, bc, adxtail, axtbc);\n        finlen = finadd(finlen, sum_three(\n            scale(axtbclen, axtbc, 2 * adx, _16), _16,\n            scale(scale(4, cc, adxtail, _8), _8, bdy, _16b), _16b,\n            scale(scale(4, bb, adxtail, _8), _8, -cdy, _16c), _16c, _32, _48), _48);\n    }\n    if (adytail !== 0) {\n        aytbclen = scale(4, bc, adytail, aytbc);\n        finlen = finadd(finlen, sum_three(\n            scale(aytbclen, aytbc, 2 * ady, _16), _16,\n            scale(scale(4, bb, adytail, _8), _8, cdx, _16b), _16b,\n            scale(scale(4, cc, adytail, _8), _8, -bdx, _16c), _16c, _32, _48), _48);\n    }\n    if (bdxtail !== 0) {\n        bxtcalen = scale(4, ca, bdxtail, bxtca);\n        finlen = finadd(finlen, sum_three(\n            scale(bxtcalen, bxtca, 2 * bdx, _16), _16,\n            scale(scale(4, aa, bdxtail, _8), _8, cdy, _16b), _16b,\n            scale(scale(4, cc, bdxtail, _8), _8, -ady, _16c), _16c, _32, _48), _48);\n    }\n    if (bdytail !== 0) {\n        bytcalen = scale(4, ca, bdytail, bytca);\n        finlen = finadd(finlen, sum_three(\n            scale(bytcalen, bytca, 2 * bdy, _16), _16,\n            scale(scale(4, cc, bdytail, _8), _8, adx, _16b), _16b,\n            scale(scale(4, aa, bdytail, _8), _8, -cdx, _16c), _16c, _32, _48), _48);\n    }\n    if (cdxtail !== 0) {\n        cxtablen = scale(4, ab, cdxtail, cxtab);\n        finlen = finadd(finlen, sum_three(\n            scale(cxtablen, cxtab, 2 * cdx, _16), _16,\n            scale(scale(4, bb, cdxtail, _8), _8, ady, _16b), _16b,\n            scale(scale(4, aa, cdxtail, _8), _8, -bdy, _16c), _16c, _32, _48), _48);\n    }\n    if (cdytail !== 0) {\n        cytablen = scale(4, ab, cdytail, cytab);\n        finlen = finadd(finlen, sum_three(\n            scale(cytablen, cytab, 2 * cdy, _16), _16,\n            scale(scale(4, aa, cdytail, _8), _8, bdx, _16b), _16b,\n            scale(scale(4, bb, cdytail, _8), _8, -adx, _16c), _16c, _32, _48), _48);\n    }\n\n    if (adxtail !== 0 || adytail !== 0) {\n        if (bdxtail !== 0 || bdytail !== 0 || cdxtail !== 0 || cdytail !== 0) {\n            s1 = bdxtail * cdy;\n            c = splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = splitter * cdy;\n            bhi = c - (c - cdy);\n            blo = cdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdx * cdytail;\n            c = splitter * bdx;\n            ahi = c - (c - bdx);\n            alo = bdx - ahi;\n            c = splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            s1 = cdxtail * -bdy;\n            c = splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = splitter * -bdy;\n            bhi = c - (c - -bdy);\n            blo = -bdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdx * -bdytail;\n            c = splitter * cdx;\n            ahi = c - (c - cdx);\n            alo = cdx - ahi;\n            c = splitter * -bdytail;\n            bhi = c - (c - -bdytail);\n            blo = -bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            bctlen = sum(4, u, 4, v, bct);\n            s1 = bdxtail * cdytail;\n            c = splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdxtail * bdytail;\n            c = splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            bctt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            bctt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            bctt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            bctt[3] = u3;\n            bcttlen = 4;\n        } else {\n            bct[0] = 0;\n            bctlen = 1;\n            bctt[0] = 0;\n            bcttlen = 1;\n        }\n        if (adxtail !== 0) {\n            const len = scale(bctlen, bct, adxtail, _16c);\n            finlen = finadd(finlen, sum(\n                scale(axtbclen, axtbc, adxtail, _16), _16,\n                scale(len, _16c, 2 * adx, _32), _32, _48), _48);\n\n            const len2 = scale(bcttlen, bctt, adxtail, _8);\n            finlen = finadd(finlen, sum_three(\n                scale(len2, _8, 2 * adx, _16), _16,\n                scale(len2, _8, adxtail, _16b), _16b,\n                scale(len, _16c, adxtail, _32), _32, _32b, _64), _64);\n\n            if (bdytail !== 0) {\n                finlen = finadd(finlen, scale(scale(4, cc, adxtail, _8), _8, bdytail, _16), _16);\n            }\n            if (cdytail !== 0) {\n                finlen = finadd(finlen, scale(scale(4, bb, -adxtail, _8), _8, cdytail, _16), _16);\n            }\n        }\n        if (adytail !== 0) {\n            const len = scale(bctlen, bct, adytail, _16c);\n            finlen = finadd(finlen, sum(\n                scale(aytbclen, aytbc, adytail, _16), _16,\n                scale(len, _16c, 2 * ady, _32), _32, _48), _48);\n\n            const len2 = scale(bcttlen, bctt, adytail, _8);\n            finlen = finadd(finlen, sum_three(\n                scale(len2, _8, 2 * ady, _16), _16,\n                scale(len2, _8, adytail, _16b), _16b,\n                scale(len, _16c, adytail, _32), _32, _32b, _64), _64);\n        }\n    }\n    if (bdxtail !== 0 || bdytail !== 0) {\n        if (cdxtail !== 0 || cdytail !== 0 || adxtail !== 0 || adytail !== 0) {\n            s1 = cdxtail * ady;\n            c = splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = splitter * ady;\n            bhi = c - (c - ady);\n            blo = ady - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdx * adytail;\n            c = splitter * cdx;\n            ahi = c - (c - cdx);\n            alo = cdx - ahi;\n            c = splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            n1 = -cdy;\n            n0 = -cdytail;\n            s1 = adxtail * n1;\n            c = splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = splitter * n1;\n            bhi = c - (c - n1);\n            blo = n1 - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adx * n0;\n            c = splitter * adx;\n            ahi = c - (c - adx);\n            alo = adx - ahi;\n            c = splitter * n0;\n            bhi = c - (c - n0);\n            blo = n0 - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            catlen = sum(4, u, 4, v, cat);\n            s1 = cdxtail * adytail;\n            c = splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adxtail * cdytail;\n            c = splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            catt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            catt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            catt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            catt[3] = u3;\n            cattlen = 4;\n        } else {\n            cat[0] = 0;\n            catlen = 1;\n            catt[0] = 0;\n            cattlen = 1;\n        }\n        if (bdxtail !== 0) {\n            const len = scale(catlen, cat, bdxtail, _16c);\n            finlen = finadd(finlen, sum(\n                scale(bxtcalen, bxtca, bdxtail, _16), _16,\n                scale(len, _16c, 2 * bdx, _32), _32, _48), _48);\n\n            const len2 = scale(cattlen, catt, bdxtail, _8);\n            finlen = finadd(finlen, sum_three(\n                scale(len2, _8, 2 * bdx, _16), _16,\n                scale(len2, _8, bdxtail, _16b), _16b,\n                scale(len, _16c, bdxtail, _32), _32, _32b, _64), _64);\n\n            if (cdytail !== 0) {\n                finlen = finadd(finlen, scale(scale(4, aa, bdxtail, _8), _8, cdytail, _16), _16);\n            }\n            if (adytail !== 0) {\n                finlen = finadd(finlen, scale(scale(4, cc, -bdxtail, _8), _8, adytail, _16), _16);\n            }\n        }\n        if (bdytail !== 0) {\n            const len = scale(catlen, cat, bdytail, _16c);\n            finlen = finadd(finlen, sum(\n                scale(bytcalen, bytca, bdytail, _16), _16,\n                scale(len, _16c, 2 * bdy, _32), _32, _48), _48);\n\n            const len2 = scale(cattlen, catt, bdytail, _8);\n            finlen = finadd(finlen, sum_three(\n                scale(len2, _8, 2 * bdy, _16), _16,\n                scale(len2, _8, bdytail, _16b), _16b,\n                scale(len, _16c, bdytail, _32), _32,  _32b, _64), _64);\n        }\n    }\n    if (cdxtail !== 0 || cdytail !== 0) {\n        if (adxtail !== 0 || adytail !== 0 || bdxtail !== 0 || bdytail !== 0) {\n            s1 = adxtail * bdy;\n            c = splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = splitter * bdy;\n            bhi = c - (c - bdy);\n            blo = bdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adx * bdytail;\n            c = splitter * adx;\n            ahi = c - (c - adx);\n            alo = adx - ahi;\n            c = splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            n1 = -ady;\n            n0 = -adytail;\n            s1 = bdxtail * n1;\n            c = splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = splitter * n1;\n            bhi = c - (c - n1);\n            blo = n1 - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdx * n0;\n            c = splitter * bdx;\n            ahi = c - (c - bdx);\n            alo = bdx - ahi;\n            c = splitter * n0;\n            bhi = c - (c - n0);\n            blo = n0 - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            abtlen = sum(4, u, 4, v, abt);\n            s1 = adxtail * bdytail;\n            c = splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdxtail * adytail;\n            c = splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            abtt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            abtt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            abtt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            abtt[3] = u3;\n            abttlen = 4;\n        } else {\n            abt[0] = 0;\n            abtlen = 1;\n            abtt[0] = 0;\n            abttlen = 1;\n        }\n        if (cdxtail !== 0) {\n            const len = scale(abtlen, abt, cdxtail, _16c);\n            finlen = finadd(finlen, sum(\n                scale(cxtablen, cxtab, cdxtail, _16), _16,\n                scale(len, _16c, 2 * cdx, _32), _32, _48), _48);\n\n            const len2 = scale(abttlen, abtt, cdxtail, _8);\n            finlen = finadd(finlen, sum_three(\n                scale(len2, _8, 2 * cdx, _16), _16,\n                scale(len2, _8, cdxtail, _16b), _16b,\n                scale(len, _16c, cdxtail, _32), _32, _32b, _64), _64);\n\n            if (adytail !== 0) {\n                finlen = finadd(finlen, scale(scale(4, bb, cdxtail, _8), _8, adytail, _16), _16);\n            }\n            if (bdytail !== 0) {\n                finlen = finadd(finlen, scale(scale(4, aa, -cdxtail, _8), _8, bdytail, _16), _16);\n            }\n        }\n        if (cdytail !== 0) {\n            const len = scale(abtlen, abt, cdytail, _16c);\n            finlen = finadd(finlen, sum(\n                scale(cytablen, cytab, cdytail, _16), _16,\n                scale(len, _16c, 2 * cdy, _32), _32, _48), _48);\n\n            const len2 = scale(abttlen, abtt, cdytail, _8);\n            finlen = finadd(finlen, sum_three(\n                scale(len2, _8, 2 * cdy, _16), _16,\n                scale(len2, _8, cdytail, _16b), _16b,\n                scale(len, _16c, cdytail, _32), _32, _32b, _64), _64);\n        }\n    }\n\n    return fin[finlen - 1];\n}\n\nexport function incircle(ax, ay, bx, by, cx, cy, dx, dy) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n\n    const bdxcdy = bdx * cdy;\n    const cdxbdy = cdx * bdy;\n    const alift = adx * adx + ady * ady;\n\n    const cdxady = cdx * ady;\n    const adxcdy = adx * cdy;\n    const blift = bdx * bdx + bdy * bdy;\n\n    const adxbdy = adx * bdy;\n    const bdxady = bdx * ady;\n    const clift = cdx * cdx + cdy * cdy;\n\n    const det =\n        alift * (bdxcdy - cdxbdy) +\n        blift * (cdxady - adxcdy) +\n        clift * (adxbdy - bdxady);\n\n    const permanent =\n        (Math.abs(bdxcdy) + Math.abs(cdxbdy)) * alift +\n        (Math.abs(cdxady) + Math.abs(adxcdy)) * blift +\n        (Math.abs(adxbdy) + Math.abs(bdxady)) * clift;\n\n    const errbound = iccerrboundA * permanent;\n\n    if (det > errbound || -det > errbound) {\n        return det;\n    }\n    return incircleadapt(ax, ay, bx, by, cx, cy, dx, dy, permanent);\n}\n\nexport function incirclefast(ax, ay, bx, by, cx, cy, dx, dy) {\n    const adx = ax - dx;\n    const ady = ay - dy;\n    const bdx = bx - dx;\n    const bdy = by - dy;\n    const cdx = cx - dx;\n    const cdy = cy - dy;\n\n    const abdet = adx * bdy - bdx * ady;\n    const bcdet = bdx * cdy - cdx * bdy;\n    const cadet = cdx * ady - adx * cdy;\n    const alift = adx * adx + ady * ady;\n    const blift = bdx * bdx + bdy * bdy;\n    const clift = cdx * cdx + cdy * cdy;\n\n    return alift * bcdet + blift * cadet + clift * abdet;\n}\n", "import {epsilon, splitter, resulterrbound, estimate, vec, sum, sum_three, scale, negate} from './util.js';\n\nconst isperrboundA = (16 + 224 * epsilon) * epsilon;\nconst isperrboundB = (5 + 72 * epsilon) * epsilon;\nconst isperrboundC = (71 + 1408 * epsilon) * epsilon * epsilon;\n\nconst ab = vec(4);\nconst bc = vec(4);\nconst cd = vec(4);\nconst de = vec(4);\nconst ea = vec(4);\nconst ac = vec(4);\nconst bd = vec(4);\nconst ce = vec(4);\nconst da = vec(4);\nconst eb = vec(4);\n\nconst abc = vec(24);\nconst bcd = vec(24);\nconst cde = vec(24);\nconst dea = vec(24);\nconst eab = vec(24);\nconst abd = vec(24);\nconst bce = vec(24);\nconst cda = vec(24);\nconst deb = vec(24);\nconst eac = vec(24);\n\nconst adet = vec(1152);\nconst bdet = vec(1152);\nconst cdet = vec(1152);\nconst ddet = vec(1152);\nconst edet = vec(1152);\nconst abdet = vec(2304);\nconst cddet = vec(2304);\nconst cdedet = vec(3456);\nconst deter = vec(5760);\n\nconst _8 = vec(8);\nconst _8b = vec(8);\nconst _8c = vec(8);\nconst _16 = vec(16);\nconst _24 = vec(24);\nconst _48 = vec(48);\nconst _48b = vec(48);\nconst _96 = vec(96);\nconst _192 = vec(192);\nconst _384x = vec(384);\nconst _384y = vec(384);\nconst _384z = vec(384);\nconst _768 = vec(768);\n\nfunction sum_three_scale(a, b, c, az, bz, cz, out) {\n    return sum_three(\n        scale(4, a, az, _8), _8,\n        scale(4, b, bz, _8b), _8b,\n        scale(4, c, cz, _8c), _8c, _16, out);\n}\n\nfunction liftexact(alen, a, blen, b, clen, c, dlen, d, x, y, z, out) {\n    const len = sum(\n        sum(alen, a, blen, b, _48), _48,\n        negate(sum(clen, c, dlen, d, _48b), _48b), _48b, _96);\n\n    return sum_three(\n        scale(scale(len, _96, x, _192), _192, x, _384x), _384x,\n        scale(scale(len, _96, y, _192), _192, y, _384y), _384y,\n        scale(scale(len, _96, z, _192), _192, z, _384z), _384z, _768, out);\n}\n\nfunction insphereexact(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez) {\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n\n    s1 = ax * by;\n    c = splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bx * ay;\n    c = splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ab[3] = u3;\n    s1 = bx * cy;\n    c = splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cx * by;\n    c = splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bc[3] = u3;\n    s1 = cx * dy;\n    c = splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dx * cy;\n    c = splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    cd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    cd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    cd[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    cd[3] = u3;\n    s1 = dx * ey;\n    c = splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ex * dy;\n    c = splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    de[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    de[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    de[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    de[3] = u3;\n    s1 = ex * ay;\n    c = splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ax * ey;\n    c = splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ea[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ea[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ea[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ea[3] = u3;\n    s1 = ax * cy;\n    c = splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cx * ay;\n    c = splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ac[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ac[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ac[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ac[3] = u3;\n    s1 = bx * dy;\n    c = splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dx * by;\n    c = splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bd[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bd[3] = u3;\n    s1 = cx * ey;\n    c = splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ex * cy;\n    c = splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ce[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ce[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ce[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ce[3] = u3;\n    s1 = dx * ay;\n    c = splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ax * dy;\n    c = splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    da[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    da[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    da[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    da[3] = u3;\n    s1 = ex * by;\n    c = splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bx * ey;\n    c = splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    eb[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    eb[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    eb[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    eb[3] = u3;\n\n    const abclen = sum_three_scale(ab, bc, ac, cz, az, -bz, abc);\n    const bcdlen = sum_three_scale(bc, cd, bd, dz, bz, -cz, bcd);\n    const cdelen = sum_three_scale(cd, de, ce, ez, cz, -dz, cde);\n    const dealen = sum_three_scale(de, ea, da, az, dz, -ez, dea);\n    const eablen = sum_three_scale(ea, ab, eb, bz, ez, -az, eab);\n    const abdlen = sum_three_scale(ab, bd, da, dz, az, bz, abd);\n    const bcelen = sum_three_scale(bc, ce, eb, ez, bz, cz, bce);\n    const cdalen = sum_three_scale(cd, da, ac, az, cz, dz, cda);\n    const deblen = sum_three_scale(de, eb, bd, bz, dz, ez, deb);\n    const eaclen = sum_three_scale(ea, ac, ce, cz, ez, az, eac);\n\n    const deterlen = sum_three(\n        liftexact(cdelen, cde, bcelen, bce, deblen, deb, bcdlen, bcd, ax, ay, az, adet), adet,\n        liftexact(dealen, dea, cdalen, cda, eaclen, eac, cdelen, cde, bx, by, bz, bdet), bdet,\n        sum_three(\n            liftexact(eablen, eab, deblen, deb, abdlen, abd, dealen, dea, cx, cy, cz, cdet), cdet,\n            liftexact(abclen, abc, eaclen, eac, bcelen, bce, eablen, eab, dx, dy, dz, ddet), ddet,\n            liftexact(bcdlen, bcd, abdlen, abd, cdalen, cda, abclen, abc, ex, ey, ez, edet), edet, cddet, cdedet), cdedet, abdet, deter);\n\n    return deter[deterlen - 1];\n}\n\nconst xdet = vec(96);\nconst ydet = vec(96);\nconst zdet = vec(96);\nconst fin = vec(1152);\n\nfunction liftadapt(a, b, c, az, bz, cz, x, y, z, out) {\n    const len = sum_three_scale(a, b, c, az, bz, cz, _24);\n    return sum_three(\n        scale(scale(len, _24, x, _48), _48, x, xdet), xdet,\n        scale(scale(len, _24, y, _48), _48, y, ydet), ydet,\n        scale(scale(len, _24, z, _48), _48, z, zdet), zdet, _192, out);\n}\n\nfunction insphereadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez, permanent) {\n    let ab3, bc3, cd3, da3, ac3, bd3;\n\n    let aextail, bextail, cextail, dextail;\n    let aeytail, beytail, ceytail, deytail;\n    let aeztail, beztail, ceztail, deztail;\n\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0;\n\n    const aex = ax - ex;\n    const bex = bx - ex;\n    const cex = cx - ex;\n    const dex = dx - ex;\n    const aey = ay - ey;\n    const bey = by - ey;\n    const cey = cy - ey;\n    const dey = dy - ey;\n    const aez = az - ez;\n    const bez = bz - ez;\n    const cez = cz - ez;\n    const dez = dz - ez;\n\n    s1 = aex * bey;\n    c = splitter * aex;\n    ahi = c - (c - aex);\n    alo = aex - ahi;\n    c = splitter * bey;\n    bhi = c - (c - bey);\n    blo = bey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bex * aey;\n    c = splitter * bex;\n    ahi = c - (c - bex);\n    alo = bex - ahi;\n    c = splitter * aey;\n    bhi = c - (c - aey);\n    blo = aey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    ab3 = _j + _i;\n    bvirt = ab3 - _j;\n    ab[2] = _j - (ab3 - bvirt) + (_i - bvirt);\n    ab[3] = ab3;\n    s1 = bex * cey;\n    c = splitter * bex;\n    ahi = c - (c - bex);\n    alo = bex - ahi;\n    c = splitter * cey;\n    bhi = c - (c - cey);\n    blo = cey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cex * bey;\n    c = splitter * cex;\n    ahi = c - (c - cex);\n    alo = cex - ahi;\n    c = splitter * bey;\n    bhi = c - (c - bey);\n    blo = bey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    bc3 = _j + _i;\n    bvirt = bc3 - _j;\n    bc[2] = _j - (bc3 - bvirt) + (_i - bvirt);\n    bc[3] = bc3;\n    s1 = cex * dey;\n    c = splitter * cex;\n    ahi = c - (c - cex);\n    alo = cex - ahi;\n    c = splitter * dey;\n    bhi = c - (c - dey);\n    blo = dey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dex * cey;\n    c = splitter * dex;\n    ahi = c - (c - dex);\n    alo = dex - ahi;\n    c = splitter * cey;\n    bhi = c - (c - cey);\n    blo = cey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    cd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    cd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    cd3 = _j + _i;\n    bvirt = cd3 - _j;\n    cd[2] = _j - (cd3 - bvirt) + (_i - bvirt);\n    cd[3] = cd3;\n    s1 = dex * aey;\n    c = splitter * dex;\n    ahi = c - (c - dex);\n    alo = dex - ahi;\n    c = splitter * aey;\n    bhi = c - (c - aey);\n    blo = aey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = aex * dey;\n    c = splitter * aex;\n    ahi = c - (c - aex);\n    alo = aex - ahi;\n    c = splitter * dey;\n    bhi = c - (c - dey);\n    blo = dey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    da[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    da[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    da3 = _j + _i;\n    bvirt = da3 - _j;\n    da[2] = _j - (da3 - bvirt) + (_i - bvirt);\n    da[3] = da3;\n    s1 = aex * cey;\n    c = splitter * aex;\n    ahi = c - (c - aex);\n    alo = aex - ahi;\n    c = splitter * cey;\n    bhi = c - (c - cey);\n    blo = cey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cex * aey;\n    c = splitter * cex;\n    ahi = c - (c - cex);\n    alo = cex - ahi;\n    c = splitter * aey;\n    bhi = c - (c - aey);\n    blo = aey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ac[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ac[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    ac3 = _j + _i;\n    bvirt = ac3 - _j;\n    ac[2] = _j - (ac3 - bvirt) + (_i - bvirt);\n    ac[3] = ac3;\n    s1 = bex * dey;\n    c = splitter * bex;\n    ahi = c - (c - bex);\n    alo = bex - ahi;\n    c = splitter * dey;\n    bhi = c - (c - dey);\n    blo = dey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dex * bey;\n    c = splitter * dex;\n    ahi = c - (c - dex);\n    alo = dex - ahi;\n    c = splitter * bey;\n    bhi = c - (c - bey);\n    blo = bey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    bd3 = _j + _i;\n    bvirt = bd3 - _j;\n    bd[2] = _j - (bd3 - bvirt) + (_i - bvirt);\n    bd[3] = bd3;\n\n    const finlen = sum(\n        sum(\n            negate(liftadapt(bc, cd, bd, dez, bez, -cez, aex, aey, aez, adet), adet), adet,\n            liftadapt(cd, da, ac, aez, cez, dez, bex, bey, bez, bdet), bdet, abdet), abdet,\n        sum(\n            negate(liftadapt(da, ab, bd, bez, dez, aez, cex, cey, cez, cdet), cdet), cdet,\n            liftadapt(ab, bc, ac, cez, aez, -bez, dex, dey, dez, ddet), ddet, cddet), cddet, fin);\n\n    let det = estimate(finlen, fin);\n    let errbound = isperrboundB * permanent;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - aex;\n    aextail = ax - (aex + bvirt) + (bvirt - ex);\n    bvirt = ay - aey;\n    aeytail = ay - (aey + bvirt) + (bvirt - ey);\n    bvirt = az - aez;\n    aeztail = az - (aez + bvirt) + (bvirt - ez);\n    bvirt = bx - bex;\n    bextail = bx - (bex + bvirt) + (bvirt - ex);\n    bvirt = by - bey;\n    beytail = by - (bey + bvirt) + (bvirt - ey);\n    bvirt = bz - bez;\n    beztail = bz - (bez + bvirt) + (bvirt - ez);\n    bvirt = cx - cex;\n    cextail = cx - (cex + bvirt) + (bvirt - ex);\n    bvirt = cy - cey;\n    ceytail = cy - (cey + bvirt) + (bvirt - ey);\n    bvirt = cz - cez;\n    ceztail = cz - (cez + bvirt) + (bvirt - ez);\n    bvirt = dx - dex;\n    dextail = dx - (dex + bvirt) + (bvirt - ex);\n    bvirt = dy - dey;\n    deytail = dy - (dey + bvirt) + (bvirt - ey);\n    bvirt = dz - dez;\n    deztail = dz - (dez + bvirt) + (bvirt - ez);\n    if (aextail === 0 && aeytail === 0 && aeztail === 0 &&\n        bextail === 0 && beytail === 0 && beztail === 0 &&\n        cextail === 0 && ceytail === 0 && ceztail === 0 &&\n        dextail === 0 && deytail === 0 && deztail === 0) {\n        return det;\n    }\n\n    errbound = isperrboundC * permanent + resulterrbound * Math.abs(det);\n\n    const abeps = (aex * beytail + bey * aextail) - (aey * bextail + bex * aeytail);\n    const bceps = (bex * ceytail + cey * bextail) - (bey * cextail + cex * beytail);\n    const cdeps = (cex * deytail + dey * cextail) - (cey * dextail + dex * ceytail);\n    const daeps = (dex * aeytail + aey * dextail) - (dey * aextail + aex * deytail);\n    const aceps = (aex * ceytail + cey * aextail) - (aey * cextail + cex * aeytail);\n    const bdeps = (bex * deytail + dey * bextail) - (bey * dextail + dex * beytail);\n    det +=\n        (((bex * bex + bey * bey + bez * bez) * ((cez * daeps + dez * aceps + aez * cdeps) +\n        (ceztail * da3 + deztail * ac3 + aeztail * cd3)) + (dex * dex + dey * dey + dez * dez) *\n        ((aez * bceps - bez * aceps + cez * abeps) + (aeztail * bc3 - beztail * ac3 + ceztail * ab3))) -\n        ((aex * aex + aey * aey + aez * aez) * ((bez * cdeps - cez * bdeps + dez * bceps) +\n        (beztail * cd3 - ceztail * bd3 + deztail * bc3)) + (cex * cex + cey * cey + cez * cez) *\n        ((dez * abeps + aez * bdeps + bez * daeps) + (deztail * ab3 + aeztail * bd3 + beztail * da3)))) +\n        2 * (((bex * bextail + bey * beytail + bez * beztail) * (cez * da3 + dez * ac3 + aez * cd3) +\n        (dex * dextail + dey * deytail + dez * deztail) * (aez * bc3 - bez * ac3 + cez * ab3)) -\n        ((aex * aextail + aey * aeytail + aez * aeztail) * (bez * cd3 - cez * bd3 + dez * bc3) +\n        (cex * cextail + cey * ceytail + cez * ceztail) * (dez * ab3 + aez * bd3 + bez * da3)));\n\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    return insphereexact(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez);\n}\n\nexport function insphere(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez) {\n    const aex = ax - ex;\n    const bex = bx - ex;\n    const cex = cx - ex;\n    const dex = dx - ex;\n    const aey = ay - ey;\n    const bey = by - ey;\n    const cey = cy - ey;\n    const dey = dy - ey;\n    const aez = az - ez;\n    const bez = bz - ez;\n    const cez = cz - ez;\n    const dez = dz - ez;\n\n    const aexbey = aex * bey;\n    const bexaey = bex * aey;\n    const ab = aexbey - bexaey;\n    const bexcey = bex * cey;\n    const cexbey = cex * bey;\n    const bc = bexcey - cexbey;\n    const cexdey = cex * dey;\n    const dexcey = dex * cey;\n    const cd = cexdey - dexcey;\n    const dexaey = dex * aey;\n    const aexdey = aex * dey;\n    const da = dexaey - aexdey;\n    const aexcey = aex * cey;\n    const cexaey = cex * aey;\n    const ac = aexcey - cexaey;\n    const bexdey = bex * dey;\n    const dexbey = dex * bey;\n    const bd = bexdey - dexbey;\n\n    const alift = aex * aex + aey * aey + aez * aez;\n    const blift = bex * bex + bey * bey + bez * bez;\n    const clift = cex * cex + cey * cey + cez * cez;\n    const dlift = dex * dex + dey * dey + dez * dez;\n\n    const det =\n        (clift * (dez * ab + aez * bd + bez * da) - dlift * (aez * bc - bez * ac + cez * ab)) +\n        (alift * (bez * cd - cez * bd + dez * bc) - blift * (cez * da + dez * ac + aez * cd));\n\n    const aezplus = Math.abs(aez);\n    const bezplus = Math.abs(bez);\n    const cezplus = Math.abs(cez);\n    const dezplus = Math.abs(dez);\n    const aexbeyplus = Math.abs(aexbey) + Math.abs(bexaey);\n    const bexceyplus = Math.abs(bexcey) + Math.abs(cexbey);\n    const cexdeyplus = Math.abs(cexdey) + Math.abs(dexcey);\n    const dexaeyplus = Math.abs(dexaey) + Math.abs(aexdey);\n    const aexceyplus = Math.abs(aexcey) + Math.abs(cexaey);\n    const bexdeyplus = Math.abs(bexdey) + Math.abs(dexbey);\n    const permanent =\n        (cexdeyplus * bezplus + bexdeyplus * cezplus + bexceyplus * dezplus) * alift +\n        (dexaeyplus * cezplus + aexceyplus * dezplus + cexdeyplus * aezplus) * blift +\n        (aexbeyplus * dezplus + bexdeyplus * aezplus + dexaeyplus * bezplus) * clift +\n        (bexceyplus * aezplus + aexceyplus * bezplus + aexbeyplus * cezplus) * dlift;\n\n    const errbound = isperrboundA * permanent;\n    if (det > errbound || -det > errbound) {\n        return det;\n    }\n    return -insphereadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez, permanent);\n}\n\nexport function inspherefast(pax, pay, paz, pbx, pby, pbz, pcx, pcy, pcz, pdx, pdy, pdz, pex, pey, pez) {\n    const aex = pax - pex;\n    const bex = pbx - pex;\n    const cex = pcx - pex;\n    const dex = pdx - pex;\n    const aey = pay - pey;\n    const bey = pby - pey;\n    const cey = pcy - pey;\n    const dey = pdy - pey;\n    const aez = paz - pez;\n    const bez = pbz - pez;\n    const cez = pcz - pez;\n    const dez = pdz - pez;\n\n    const ab = aex * bey - bex * aey;\n    const bc = bex * cey - cex * bey;\n    const cd = cex * dey - dex * cey;\n    const da = dex * aey - aex * dey;\n    const ac = aex * cey - cex * aey;\n    const bd = bex * dey - dex * bey;\n\n    const abc = aez * bc - bez * ac + cez * ab;\n    const bcd = bez * cd - cez * bd + dez * bc;\n    const cda = cez * da + dez * ac + aez * cd;\n    const dab = dez * ab + aez * bd + bez * da;\n\n    const alift = aex * aex + aey * aey + aez * aez;\n    const blift = bex * bex + bey * bey + bez * bez;\n    const clift = cex * cex + cey * cey + cez * cez;\n    const dlift = dex * dex + dey * dey + dez * dez;\n\n    return (clift * dab - dlift * abc) + (alift * bcd - blift * cda);\n}\n", "\nconst EPSILON = Math.pow(2, -52);\nconst EDGE_STACK = new Uint32Array(512);\n\nimport {orient2d} from 'robust-predicates';\n\nexport default class Delaunator {\n\n    static from(points, getX = defaultGetX, getY = defaultGetY) {\n        const n = points.length;\n        const coords = new Float64Array(n * 2);\n\n        for (let i = 0; i < n; i++) {\n            const p = points[i];\n            coords[2 * i] = getX(p);\n            coords[2 * i + 1] = getY(p);\n        }\n\n        return new Delaunator(coords);\n    }\n\n    constructor(coords) {\n        const n = coords.length >> 1;\n        if (n > 0 && typeof coords[0] !== 'number') throw new Error('Expected coords to contain numbers.');\n\n        this.coords = coords;\n\n        // arrays that will store the triangulation graph\n        const maxTriangles = Math.max(2 * n - 5, 0);\n        this._triangles = new Uint32Array(maxTriangles * 3);\n        this._halfedges = new Int32Array(maxTriangles * 3);\n\n        // temporary arrays for tracking the edges of the advancing convex hull\n        this._hashSize = Math.ceil(Math.sqrt(n));\n        this._hullPrev = new Uint32Array(n); // edge to prev edge\n        this._hullNext = new Uint32Array(n); // edge to next edge\n        this._hullTri = new Uint32Array(n); // edge to adjacent triangle\n        this._hullHash = new Int32Array(this._hashSize); // angular edge hash\n\n        // temporary arrays for sorting points\n        this._ids = new Uint32Array(n);\n        this._dists = new Float64Array(n);\n\n        this.update();\n    }\n\n    update() {\n        const {coords, _hullPrev: hullPrev, _hullNext: hullNext, _hullTri: hullTri, _hullHash: hullHash} =  this;\n        const n = coords.length >> 1;\n\n        // populate an array of point indices; calculate input data bbox\n        let minX = Infinity;\n        let minY = Infinity;\n        let maxX = -Infinity;\n        let maxY = -Infinity;\n\n        for (let i = 0; i < n; i++) {\n            const x = coords[2 * i];\n            const y = coords[2 * i + 1];\n            if (x < minX) minX = x;\n            if (y < minY) minY = y;\n            if (x > maxX) maxX = x;\n            if (y > maxY) maxY = y;\n            this._ids[i] = i;\n        }\n        const cx = (minX + maxX) / 2;\n        const cy = (minY + maxY) / 2;\n\n        let i0, i1, i2;\n\n        // pick a seed point close to the center\n        for (let i = 0, minDist = Infinity; i < n; i++) {\n            const d = dist(cx, cy, coords[2 * i], coords[2 * i + 1]);\n            if (d < minDist) {\n                i0 = i;\n                minDist = d;\n            }\n        }\n        const i0x = coords[2 * i0];\n        const i0y = coords[2 * i0 + 1];\n\n        // find the point closest to the seed\n        for (let i = 0, minDist = Infinity; i < n; i++) {\n            if (i === i0) continue;\n            const d = dist(i0x, i0y, coords[2 * i], coords[2 * i + 1]);\n            if (d < minDist && d > 0) {\n                i1 = i;\n                minDist = d;\n            }\n        }\n        let i1x = coords[2 * i1];\n        let i1y = coords[2 * i1 + 1];\n\n        let minRadius = Infinity;\n\n        // find the third point which forms the smallest circumcircle with the first two\n        for (let i = 0; i < n; i++) {\n            if (i === i0 || i === i1) continue;\n            const r = circumradius(i0x, i0y, i1x, i1y, coords[2 * i], coords[2 * i + 1]);\n            if (r < minRadius) {\n                i2 = i;\n                minRadius = r;\n            }\n        }\n        let i2x = coords[2 * i2];\n        let i2y = coords[2 * i2 + 1];\n\n        if (minRadius === Infinity) {\n            // order collinear points by dx (or dy if all x are identical)\n            // and return the list as a hull\n            for (let i = 0; i < n; i++) {\n                this._dists[i] = (coords[2 * i] - coords[0]) || (coords[2 * i + 1] - coords[1]);\n            }\n            quicksort(this._ids, this._dists, 0, n - 1);\n            const hull = new Uint32Array(n);\n            let j = 0;\n            for (let i = 0, d0 = -Infinity; i < n; i++) {\n                const id = this._ids[i];\n                const d = this._dists[id];\n                if (d > d0) {\n                    hull[j++] = id;\n                    d0 = d;\n                }\n            }\n            this.hull = hull.subarray(0, j);\n            this.triangles = new Uint32Array(0);\n            this.halfedges = new Uint32Array(0);\n            return;\n        }\n\n        // swap the order of the seed points for counter-clockwise orientation\n        if (orient2d(i0x, i0y, i1x, i1y, i2x, i2y) < 0) {\n            const i = i1;\n            const x = i1x;\n            const y = i1y;\n            i1 = i2;\n            i1x = i2x;\n            i1y = i2y;\n            i2 = i;\n            i2x = x;\n            i2y = y;\n        }\n\n        const center = circumcenter(i0x, i0y, i1x, i1y, i2x, i2y);\n        this._cx = center.x;\n        this._cy = center.y;\n\n        for (let i = 0; i < n; i++) {\n            this._dists[i] = dist(coords[2 * i], coords[2 * i + 1], center.x, center.y);\n        }\n\n        // sort the points by distance from the seed triangle circumcenter\n        quicksort(this._ids, this._dists, 0, n - 1);\n\n        // set up the seed triangle as the starting hull\n        this._hullStart = i0;\n        let hullSize = 3;\n\n        hullNext[i0] = hullPrev[i2] = i1;\n        hullNext[i1] = hullPrev[i0] = i2;\n        hullNext[i2] = hullPrev[i1] = i0;\n\n        hullTri[i0] = 0;\n        hullTri[i1] = 1;\n        hullTri[i2] = 2;\n\n        hullHash.fill(-1);\n        hullHash[this._hashKey(i0x, i0y)] = i0;\n        hullHash[this._hashKey(i1x, i1y)] = i1;\n        hullHash[this._hashKey(i2x, i2y)] = i2;\n\n        this.trianglesLen = 0;\n        this._addTriangle(i0, i1, i2, -1, -1, -1);\n\n        for (let k = 0, xp, yp; k < this._ids.length; k++) {\n            const i = this._ids[k];\n            const x = coords[2 * i];\n            const y = coords[2 * i + 1];\n\n            // skip near-duplicate points\n            if (k > 0 && Math.abs(x - xp) <= EPSILON && Math.abs(y - yp) <= EPSILON) continue;\n            xp = x;\n            yp = y;\n\n            // skip seed triangle points\n            if (i === i0 || i === i1 || i === i2) continue;\n\n            // find a visible edge on the convex hull using edge hash\n            let start = 0;\n            for (let j = 0, key = this._hashKey(x, y); j < this._hashSize; j++) {\n                start = hullHash[(key + j) % this._hashSize];\n                if (start !== -1 && start !== hullNext[start]) break;\n            }\n\n            start = hullPrev[start];\n            let e = start, q;\n            while (q = hullNext[e], orient2d(x, y, coords[2 * e], coords[2 * e + 1], coords[2 * q], coords[2 * q + 1]) >= 0) {\n                e = q;\n                if (e === start) {\n                    e = -1;\n                    break;\n                }\n            }\n            if (e === -1) continue; // likely a near-duplicate point; skip it\n\n            // add the first triangle from the point\n            let t = this._addTriangle(e, i, hullNext[e], -1, -1, hullTri[e]);\n\n            // recursively flip triangles from the point until they satisfy the Delaunay condition\n            hullTri[i] = this._legalize(t + 2);\n            hullTri[e] = t; // keep track of boundary triangles on the hull\n            hullSize++;\n\n            // walk forward through the hull, adding more triangles and flipping recursively\n            let n = hullNext[e];\n            while (q = hullNext[n], orient2d(x, y, coords[2 * n], coords[2 * n + 1], coords[2 * q], coords[2 * q + 1]) < 0) {\n                t = this._addTriangle(n, i, q, hullTri[i], -1, hullTri[n]);\n                hullTri[i] = this._legalize(t + 2);\n                hullNext[n] = n; // mark as removed\n                hullSize--;\n                n = q;\n            }\n\n            // walk backward from the other side, adding more triangles and flipping\n            if (e === start) {\n                while (q = hullPrev[e], orient2d(x, y, coords[2 * q], coords[2 * q + 1], coords[2 * e], coords[2 * e + 1]) < 0) {\n                    t = this._addTriangle(q, i, e, -1, hullTri[e], hullTri[q]);\n                    this._legalize(t + 2);\n                    hullTri[q] = t;\n                    hullNext[e] = e; // mark as removed\n                    hullSize--;\n                    e = q;\n                }\n            }\n\n            // update the hull indices\n            this._hullStart = hullPrev[i] = e;\n            hullNext[e] = hullPrev[n] = i;\n            hullNext[i] = n;\n\n            // save the two new edges in the hash table\n            hullHash[this._hashKey(x, y)] = i;\n            hullHash[this._hashKey(coords[2 * e], coords[2 * e + 1])] = e;\n        }\n\n        this.hull = new Uint32Array(hullSize);\n        for (let i = 0, e = this._hullStart; i < hullSize; i++) {\n            this.hull[i] = e;\n            e = hullNext[e];\n        }\n\n        // trim typed triangle mesh arrays\n        this.triangles = this._triangles.subarray(0, this.trianglesLen);\n        this.halfedges = this._halfedges.subarray(0, this.trianglesLen);\n    }\n\n    _hashKey(x, y) {\n        return Math.floor(pseudoAngle(x - this._cx, y - this._cy) * this._hashSize) % this._hashSize;\n    }\n\n    _legalize(a) {\n        const {_triangles: triangles, _halfedges: halfedges, coords} = this;\n\n        let i = 0;\n        let ar = 0;\n\n        // recursion eliminated with a fixed-size stack\n        while (true) {\n            const b = halfedges[a];\n\n            /* if the pair of triangles doesn't satisfy the Delaunay condition\n             * (p1 is inside the circumcircle of [p0, pl, pr]), flip them,\n             * then do the same check/flip recursively for the new pair of triangles\n             *\n             *           pl                    pl\n             *          /||\\                  /  \\\n             *       al/ || \\bl            al/    \\a\n             *        /  ||  \\              /      \\\n             *       /  a||b  \\    flip    /___ar___\\\n             *     p0\\   ||   /p1   =>   p0\\---bl---/p1\n             *        \\  ||  /              \\      /\n             *       ar\\ || /br             b\\    /br\n             *          \\||/                  \\  /\n             *           pr                    pr\n             */\n            const a0 = a - a % 3;\n            ar = a0 + (a + 2) % 3;\n\n            if (b === -1) { // convex hull edge\n                if (i === 0) break;\n                a = EDGE_STACK[--i];\n                continue;\n            }\n\n            const b0 = b - b % 3;\n            const al = a0 + (a + 1) % 3;\n            const bl = b0 + (b + 2) % 3;\n\n            const p0 = triangles[ar];\n            const pr = triangles[a];\n            const pl = triangles[al];\n            const p1 = triangles[bl];\n\n            const illegal = inCircle(\n                coords[2 * p0], coords[2 * p0 + 1],\n                coords[2 * pr], coords[2 * pr + 1],\n                coords[2 * pl], coords[2 * pl + 1],\n                coords[2 * p1], coords[2 * p1 + 1]);\n\n            if (illegal) {\n                triangles[a] = p1;\n                triangles[b] = p0;\n\n                const hbl = halfedges[bl];\n\n                // edge swapped on the other side of the hull (rare); fix the halfedge reference\n                if (hbl === -1) {\n                    let e = this._hullStart;\n                    do {\n                        if (this._hullTri[e] === bl) {\n                            this._hullTri[e] = a;\n                            break;\n                        }\n                        e = this._hullPrev[e];\n                    } while (e !== this._hullStart);\n                }\n                this._link(a, hbl);\n                this._link(b, halfedges[ar]);\n                this._link(ar, bl);\n\n                const br = b0 + (b + 1) % 3;\n\n                // don't worry about hitting the cap: it can only happen on extremely degenerate input\n                if (i < EDGE_STACK.length) {\n                    EDGE_STACK[i++] = br;\n                }\n            } else {\n                if (i === 0) break;\n                a = EDGE_STACK[--i];\n            }\n        }\n\n        return ar;\n    }\n\n    _link(a, b) {\n        this._halfedges[a] = b;\n        if (b !== -1) this._halfedges[b] = a;\n    }\n\n    // add a new triangle given vertex indices and adjacent half-edge ids\n    _addTriangle(i0, i1, i2, a, b, c) {\n        const t = this.trianglesLen;\n\n        this._triangles[t] = i0;\n        this._triangles[t + 1] = i1;\n        this._triangles[t + 2] = i2;\n\n        this._link(t, a);\n        this._link(t + 1, b);\n        this._link(t + 2, c);\n\n        this.trianglesLen += 3;\n\n        return t;\n    }\n}\n\n// monotonically increases with real angle, but doesn't need expensive trigonometry\nfunction pseudoAngle(dx, dy) {\n    const p = dx / (Math.abs(dx) + Math.abs(dy));\n    return (dy > 0 ? 3 - p : 1 + p) / 4; // [0..1]\n}\n\nfunction dist(ax, ay, bx, by) {\n    const dx = ax - bx;\n    const dy = ay - by;\n    return dx * dx + dy * dy;\n}\n\nfunction inCircle(ax, ay, bx, by, cx, cy, px, py) {\n    const dx = ax - px;\n    const dy = ay - py;\n    const ex = bx - px;\n    const ey = by - py;\n    const fx = cx - px;\n    const fy = cy - py;\n\n    const ap = dx * dx + dy * dy;\n    const bp = ex * ex + ey * ey;\n    const cp = fx * fx + fy * fy;\n\n    return dx * (ey * cp - bp * fy) -\n           dy * (ex * cp - bp * fx) +\n           ap * (ex * fy - ey * fx) < 0;\n}\n\nfunction circumradius(ax, ay, bx, by, cx, cy) {\n    const dx = bx - ax;\n    const dy = by - ay;\n    const ex = cx - ax;\n    const ey = cy - ay;\n\n    const bl = dx * dx + dy * dy;\n    const cl = ex * ex + ey * ey;\n    const d = 0.5 / (dx * ey - dy * ex);\n\n    const x = (ey * bl - dy * cl) * d;\n    const y = (dx * cl - ex * bl) * d;\n\n    return x * x + y * y;\n}\n\nfunction circumcenter(ax, ay, bx, by, cx, cy) {\n    const dx = bx - ax;\n    const dy = by - ay;\n    const ex = cx - ax;\n    const ey = cy - ay;\n\n    const bl = dx * dx + dy * dy;\n    const cl = ex * ex + ey * ey;\n    const d = 0.5 / (dx * ey - dy * ex);\n\n    const x = ax + (ey * bl - dy * cl) * d;\n    const y = ay + (dx * cl - ex * bl) * d;\n\n    return {x, y};\n}\n\nfunction quicksort(ids, dists, left, right) {\n    if (right - left <= 20) {\n        for (let i = left + 1; i <= right; i++) {\n            const temp = ids[i];\n            const tempDist = dists[temp];\n            let j = i - 1;\n            while (j >= left && dists[ids[j]] > tempDist) ids[j + 1] = ids[j--];\n            ids[j + 1] = temp;\n        }\n    } else {\n        const median = (left + right) >> 1;\n        let i = left + 1;\n        let j = right;\n        swap(ids, median, i);\n        if (dists[ids[left]] > dists[ids[right]]) swap(ids, left, right);\n        if (dists[ids[i]] > dists[ids[right]]) swap(ids, i, right);\n        if (dists[ids[left]] > dists[ids[i]]) swap(ids, left, i);\n\n        const temp = ids[i];\n        const tempDist = dists[temp];\n        while (true) {\n            do i++; while (dists[ids[i]] < tempDist);\n            do j--; while (dists[ids[j]] > tempDist);\n            if (j < i) break;\n            swap(ids, i, j);\n        }\n        ids[left + 1] = ids[j];\n        ids[j] = temp;\n\n        if (right - i + 1 >= j - left) {\n            quicksort(ids, dists, i, right);\n            quicksort(ids, dists, left, j - 1);\n        } else {\n            quicksort(ids, dists, left, j - 1);\n            quicksort(ids, dists, i, right);\n        }\n    }\n}\n\nfunction swap(arr, i, j) {\n    const tmp = arr[i];\n    arr[i] = arr[j];\n    arr[j] = tmp;\n}\n\nfunction defaultGetX(p) {\n    return p[0];\n}\nfunction defaultGetY(p) {\n    return p[1];\n}\n", "import Delaunator from \"delaunator\";\nimport Path from \"./path.js\";\nimport Polygon from \"./polygon.js\";\nimport Voronoi from \"./voronoi.js\";\n\nconst tau = 2 * Math.PI, pow = Math.pow;\n\nfunction pointX(p) {\n  return p[0];\n}\n\nfunction pointY(p) {\n  return p[1];\n}\n\n// A triangulation is collinear if all its triangles have a non-null area\nfunction collinear(d) {\n  const {triangles, coords} = d;\n  for (let i = 0; i < triangles.length; i += 3) {\n    const a = 2 * triangles[i],\n          b = 2 * triangles[i + 1],\n          c = 2 * triangles[i + 2],\n          cross = (coords[c] - coords[a]) * (coords[b + 1] - coords[a + 1])\n                - (coords[b] - coords[a]) * (coords[c + 1] - coords[a + 1]);\n    if (cross > 1e-10) return false;\n  }\n  return true;\n}\n\nfunction jitter(x, y, r) {\n  return [x + Math.sin(x + y) * r, y + Math.cos(x - y) * r];\n}\n\nexport default class Delaunay {\n  static from(points, fx = pointX, fy = pointY, that) {\n    return new Delaunay(\"length\" in points\n        ? flatArray(points, fx, fy, that)\n        : Float64Array.from(flatIterable(points, fx, fy, that)));\n  }\n  constructor(points) {\n    this._delaunator = new Delaunator(points);\n    this.inedges = new Int32Array(points.length / 2);\n    this._hullIndex = new Int32Array(points.length / 2);\n    this.points = this._delaunator.coords;\n    this._init();\n  }\n  update() {\n    this._delaunator.update();\n    this._init();\n    return this;\n  }\n  _init() {\n    const d = this._delaunator, points = this.points;\n\n    // check for collinear\n    if (d.hull && d.hull.length > 2 && collinear(d)) {\n      this.collinear = Int32Array.from({length: points.length/2}, (_,i) => i)\n        .sort((i, j) => points[2 * i] - points[2 * j] || points[2 * i + 1] - points[2 * j + 1]); // for exact neighbors\n      const e = this.collinear[0], f = this.collinear[this.collinear.length - 1],\n        bounds = [ points[2 * e], points[2 * e + 1], points[2 * f], points[2 * f + 1] ],\n        r = 1e-8 * Math.hypot(bounds[3] - bounds[1], bounds[2] - bounds[0]);\n      for (let i = 0, n = points.length / 2; i < n; ++i) {\n        const p = jitter(points[2 * i], points[2 * i + 1], r);\n        points[2 * i] = p[0];\n        points[2 * i + 1] = p[1];\n      }\n      this._delaunator = new Delaunator(points);\n    } else {\n      delete this.collinear;\n    }\n\n    const halfedges = this.halfedges = this._delaunator.halfedges;\n    const hull = this.hull = this._delaunator.hull;\n    const triangles = this.triangles = this._delaunator.triangles;\n    const inedges = this.inedges.fill(-1);\n    const hullIndex = this._hullIndex.fill(-1);\n\n    // Compute an index from each point to an (arbitrary) incoming halfedge\n    // Used to give the first neighbor of each point; for this reason,\n    // on the hull we give priority to exterior halfedges\n    for (let e = 0, n = halfedges.length; e < n; ++e) {\n      const p = triangles[e % 3 === 2 ? e - 2 : e + 1];\n      if (halfedges[e] === -1 || inedges[p] === -1) inedges[p] = e;\n    }\n    for (let i = 0, n = hull.length; i < n; ++i) {\n      hullIndex[hull[i]] = i;\n    }\n\n    // degenerate case: 1 or 2 (distinct) points\n    if (hull.length <= 2 && hull.length > 0) {\n      this.triangles = new Int32Array(3).fill(-1);\n      this.halfedges = new Int32Array(3).fill(-1);\n      this.triangles[0] = hull[0];\n      inedges[hull[0]] = 1;\n      if (hull.length === 2) {\n        inedges[hull[1]] = 0;\n        this.triangles[1] = hull[1];\n        this.triangles[2] = hull[1];\n      }\n    }\n  }\n  voronoi(bounds) {\n    return new Voronoi(this, bounds);\n  }\n  *neighbors(i) {\n    const {inedges, hull, _hullIndex, halfedges, triangles, collinear} = this;\n\n    // degenerate case with several collinear points\n    if (collinear) {\n      const l = collinear.indexOf(i);\n      if (l > 0) yield collinear[l - 1];\n      if (l < collinear.length - 1) yield collinear[l + 1];\n      return;\n    }\n\n    const e0 = inedges[i];\n    if (e0 === -1) return; // coincident point\n    let e = e0, p0 = -1;\n    do {\n      yield p0 = triangles[e];\n      e = e % 3 === 2 ? e - 2 : e + 1;\n      if (triangles[e] !== i) return; // bad triangulation\n      e = halfedges[e];\n      if (e === -1) {\n        const p = hull[(_hullIndex[i] + 1) % hull.length];\n        if (p !== p0) yield p;\n        return;\n      }\n    } while (e !== e0);\n  }\n  find(x, y, i = 0) {\n    if ((x = +x, x !== x) || (y = +y, y !== y)) return -1;\n    const i0 = i;\n    let c;\n    while ((c = this._step(i, x, y)) >= 0 && c !== i && c !== i0) i = c;\n    return c;\n  }\n  _step(i, x, y) {\n    const {inedges, hull, _hullIndex, halfedges, triangles, points} = this;\n    if (inedges[i] === -1 || !points.length) return (i + 1) % (points.length >> 1);\n    let c = i;\n    let dc = pow(x - points[i * 2], 2) + pow(y - points[i * 2 + 1], 2);\n    const e0 = inedges[i];\n    let e = e0;\n    do {\n      let t = triangles[e];\n      const dt = pow(x - points[t * 2], 2) + pow(y - points[t * 2 + 1], 2);\n      if (dt < dc) dc = dt, c = t;\n      e = e % 3 === 2 ? e - 2 : e + 1;\n      if (triangles[e] !== i) break; // bad triangulation\n      e = halfedges[e];\n      if (e === -1) {\n        e = hull[(_hullIndex[i] + 1) % hull.length];\n        if (e !== t) {\n          if (pow(x - points[e * 2], 2) + pow(y - points[e * 2 + 1], 2) < dc) return e;\n        }\n        break;\n      }\n    } while (e !== e0);\n    return c;\n  }\n  render(context) {\n    const buffer = context == null ? context = new Path : undefined;\n    const {points, halfedges, triangles} = this;\n    for (let i = 0, n = halfedges.length; i < n; ++i) {\n      const j = halfedges[i];\n      if (j < i) continue;\n      const ti = triangles[i] * 2;\n      const tj = triangles[j] * 2;\n      context.moveTo(points[ti], points[ti + 1]);\n      context.lineTo(points[tj], points[tj + 1]);\n    }\n    this.renderHull(context);\n    return buffer && buffer.value();\n  }\n  renderPoints(context, r) {\n    if (r === undefined && (!context || typeof context.moveTo !== \"function\")) r = context, context = null;\n    r = r == undefined ? 2 : +r;\n    const buffer = context == null ? context = new Path : undefined;\n    const {points} = this;\n    for (let i = 0, n = points.length; i < n; i += 2) {\n      const x = points[i], y = points[i + 1];\n      context.moveTo(x + r, y);\n      context.arc(x, y, r, 0, tau);\n    }\n    return buffer && buffer.value();\n  }\n  renderHull(context) {\n    const buffer = context == null ? context = new Path : undefined;\n    const {hull, points} = this;\n    const h = hull[0] * 2, n = hull.length;\n    context.moveTo(points[h], points[h + 1]);\n    for (let i = 1; i < n; ++i) {\n      const h = 2 * hull[i];\n      context.lineTo(points[h], points[h + 1]);\n    }\n    context.closePath();\n    return buffer && buffer.value();\n  }\n  hullPolygon() {\n    const polygon = new Polygon;\n    this.renderHull(polygon);\n    return polygon.value();\n  }\n  renderTriangle(i, context) {\n    const buffer = context == null ? context = new Path : undefined;\n    const {points, triangles} = this;\n    const t0 = triangles[i *= 3] * 2;\n    const t1 = triangles[i + 1] * 2;\n    const t2 = triangles[i + 2] * 2;\n    context.moveTo(points[t0], points[t0 + 1]);\n    context.lineTo(points[t1], points[t1 + 1]);\n    context.lineTo(points[t2], points[t2 + 1]);\n    context.closePath();\n    return buffer && buffer.value();\n  }\n  *trianglePolygons() {\n    const {triangles} = this;\n    for (let i = 0, n = triangles.length / 3; i < n; ++i) {\n      yield this.trianglePolygon(i);\n    }\n  }\n  trianglePolygon(i) {\n    const polygon = new Polygon;\n    this.renderTriangle(i, polygon);\n    return polygon.value();\n  }\n}\n\nfunction flatArray(points, fx, fy, that) {\n  const n = points.length;\n  const array = new Float64Array(n * 2);\n  for (let i = 0; i < n; ++i) {\n    const p = points[i];\n    array[i * 2] = fx.call(that, p, i, points);\n    array[i * 2 + 1] = fy.call(that, p, i, points);\n  }\n  return array;\n}\n\nfunction* flatIterable(points, fx, fy, that) {\n  let i = 0;\n  for (const p of points) {\n    yield fx.call(that, p, i, points);\n    yield fy.call(that, p, i, points);\n    ++i;\n  }\n}\n", "export default function(specifier) {\n  var n = specifier.length / 6 | 0, colors = new Array(n), i = 0;\n  while (i < n) colors[i] = \"#\" + specifier.slice(i * 6, ++i * 6);\n  return colors;\n}\n", "import colors from \"../colors.js\";\n\nexport default colors(\"1f77b4ff7f0e2ca02cd627289467bd8c564be377c27f7f7fbcbd2217becf\");\n", "import colors from \"../colors.js\";\n\nexport default colors(\"7fc97fbeaed4fdc086ffff99386cb0f0027fbf5b17666666\");\n", "import colors from \"../colors.js\";\n\nexport default colors(\"1b9e77d95f027570b3e7298a66a61ee6ab02a6761d666666\");\n", "import colors from \"../colors.js\";\n\nexport default colors(\"4269d0efb118ff725c6cc5b03ca951ff8ab7a463f297bbf59c6b4e9498a0\");\n", "import colors from \"../colors.js\";\n\nexport default colors(\"a6cee31f78b4b2df8a33a02cfb9a99e31a1cfdbf6fff7f00cab2d66a3d9affff99b15928\");\n", "import colors from \"../colors.js\";\n\nexport default colors(\"fbb4aeb3cde3ccebc5decbe4fed9a6ffffcce5d8bdfddaecf2f2f2\");\n", "import colors from \"../colors.js\";\n\nexport default colors(\"b3e2cdfdcdaccbd5e8f4cae4e6f5c9fff2aef1e2cccccccc\");\n", "import colors from \"../colors.js\";\n\nexport default colors(\"e41a1c377eb84daf4a984ea3ff7f00ffff33a65628f781bf999999\");\n", "import colors from \"../colors.js\";\n\nexport default colors(\"66c2a5fc8d628da0cbe78ac3a6d854ffd92fe5c494b3b3b3\");\n", "import colors from \"../colors.js\";\n\nexport default colors(\"8dd3c7ffffb3bebadafb807280b1d3fdb462b3de69fccde5d9d9d9bc80bdccebc5ffed6f\");\n", "import colors from \"../colors.js\";\n\nexport default colors(\"4e79a7f28e2ce1575976b7b259a14fedc949af7aa1ff9da79c755fbab0ab\");\n", "import {interpolateRgbBasis} from \"d3-interpolate\";\n\nexport default scheme => interpolateRgbBasis(scheme[scheme.length - 1]);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"d8b365f5f5f55ab4ac\",\n  \"a6611adfc27d80cdc1018571\",\n  \"a6611adfc27df5f5f580cdc1018571\",\n  \"8c510ad8b365f6e8c3c7eae55ab4ac01665e\",\n  \"8c510ad8b365f6e8c3f5f5f5c7eae55ab4ac01665e\",\n  \"8c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e\",\n  \"8c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e\",\n  \"5430058c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e003c30\",\n  \"5430058c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e003c30\"\n).map(colors);\n\nexport default ramp(scheme);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"af8dc3f7f7f77fbf7b\",\n  \"7b3294c2a5cfa6dba0008837\",\n  \"7b3294c2a5cff7f7f7a6dba0008837\",\n  \"762a83af8dc3e7d4e8d9f0d37fbf7b1b7837\",\n  \"762a83af8dc3e7d4e8f7f7f7d9f0d37fbf7b1b7837\",\n  \"762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b7837\",\n  \"762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b7837\",\n  \"40004b762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b783700441b\",\n  \"40004b762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b783700441b\"\n).map(colors);\n\nexport default ramp(scheme);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"e9a3c9f7f7f7a1d76a\",\n  \"d01c8bf1b6dab8e1864dac26\",\n  \"d01c8bf1b6daf7f7f7b8e1864dac26\",\n  \"c51b7de9a3c9fde0efe6f5d0a1d76a4d9221\",\n  \"c51b7de9a3c9fde0eff7f7f7e6f5d0a1d76a4d9221\",\n  \"c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221\",\n  \"c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221\",\n  \"8e0152c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221276419\",\n  \"8e0152c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221276419\"\n).map(colors);\n\nexport default ramp(scheme);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"998ec3f7f7f7f1a340\",\n  \"5e3c99b2abd2fdb863e66101\",\n  \"5e3c99b2abd2f7f7f7fdb863e66101\",\n  \"542788998ec3d8daebfee0b6f1a340b35806\",\n  \"542788998ec3d8daebf7f7f7fee0b6f1a340b35806\",\n  \"5427888073acb2abd2d8daebfee0b6fdb863e08214b35806\",\n  \"5427888073acb2abd2d8daebf7f7f7fee0b6fdb863e08214b35806\",\n  \"2d004b5427888073acb2abd2d8daebfee0b6fdb863e08214b358067f3b08\",\n  \"2d004b5427888073acb2abd2d8daebf7f7f7fee0b6fdb863e08214b358067f3b08\"\n).map(colors);\n\nexport default ramp(scheme);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"ef8a62f7f7f767a9cf\",\n  \"ca0020f4a58292c5de0571b0\",\n  \"ca0020f4a582f7f7f792c5de0571b0\",\n  \"b2182bef8a62fddbc7d1e5f067a9cf2166ac\",\n  \"b2182bef8a62fddbc7f7f7f7d1e5f067a9cf2166ac\",\n  \"b2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac\",\n  \"b2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac\",\n  \"67001fb2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac053061\",\n  \"67001fb2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac053061\"\n).map(colors);\n\nexport default ramp(scheme);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"ef8a62ffffff999999\",\n  \"ca0020f4a582bababa404040\",\n  \"ca0020f4a582ffffffbababa404040\",\n  \"b2182bef8a62fddbc7e0e0e09999994d4d4d\",\n  \"b2182bef8a62fddbc7ffffffe0e0e09999994d4d4d\",\n  \"b2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d\",\n  \"b2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d\",\n  \"67001fb2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d1a1a1a\",\n  \"67001fb2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d1a1a1a\"\n).map(colors);\n\nexport default ramp(scheme);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"fc8d59ffffbf91bfdb\",\n  \"d7191cfdae61abd9e92c7bb6\",\n  \"d7191cfdae61ffffbfabd9e92c7bb6\",\n  \"d73027fc8d59fee090e0f3f891bfdb4575b4\",\n  \"d73027fc8d59fee090ffffbfe0f3f891bfdb4575b4\",\n  \"d73027f46d43fdae61fee090e0f3f8abd9e974add14575b4\",\n  \"d73027f46d43fdae61fee090ffffbfe0f3f8abd9e974add14575b4\",\n  \"a50026d73027f46d43fdae61fee090e0f3f8abd9e974add14575b4313695\",\n  \"a50026d73027f46d43fdae61fee090ffffbfe0f3f8abd9e974add14575b4313695\"\n).map(colors);\n\nexport default ramp(scheme);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"fc8d59ffffbf91cf60\",\n  \"d7191cfdae61a6d96a1a9641\",\n  \"d7191cfdae61ffffbfa6d96a1a9641\",\n  \"d73027fc8d59fee08bd9ef8b91cf601a9850\",\n  \"d73027fc8d59fee08bffffbfd9ef8b91cf601a9850\",\n  \"d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850\",\n  \"d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850\",\n  \"a50026d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850006837\",\n  \"a50026d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850006837\"\n).map(colors);\n\nexport default ramp(scheme);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"fc8d59ffffbf99d594\",\n  \"d7191cfdae61abdda42b83ba\",\n  \"d7191cfdae61ffffbfabdda42b83ba\",\n  \"d53e4ffc8d59fee08be6f59899d5943288bd\",\n  \"d53e4ffc8d59fee08bffffbfe6f59899d5943288bd\",\n  \"d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd\",\n  \"d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd\",\n  \"9e0142d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd5e4fa2\",\n  \"9e0142d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd5e4fa2\"\n).map(colors);\n\nexport default ramp(scheme);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"e5f5f999d8c92ca25f\",\n  \"edf8fbb2e2e266c2a4238b45\",\n  \"edf8fbb2e2e266c2a42ca25f006d2c\",\n  \"edf8fbccece699d8c966c2a42ca25f006d2c\",\n  \"edf8fbccece699d8c966c2a441ae76238b45005824\",\n  \"f7fcfde5f5f9ccece699d8c966c2a441ae76238b45005824\",\n  \"f7fcfde5f5f9ccece699d8c966c2a441ae76238b45006d2c00441b\"\n).map(colors);\n\nexport default ramp(scheme);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"e0ecf49ebcda8856a7\",\n  \"edf8fbb3cde38c96c688419d\",\n  \"edf8fbb3cde38c96c68856a7810f7c\",\n  \"edf8fbbfd3e69ebcda8c96c68856a7810f7c\",\n  \"edf8fbbfd3e69ebcda8c96c68c6bb188419d6e016b\",\n  \"f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d6e016b\",\n  \"f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d810f7c4d004b\"\n).map(colors);\n\nexport default ramp(scheme);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"e0f3dba8ddb543a2ca\",\n  \"f0f9e8bae4bc7bccc42b8cbe\",\n  \"f0f9e8bae4bc7bccc443a2ca0868ac\",\n  \"f0f9e8ccebc5a8ddb57bccc443a2ca0868ac\",\n  \"f0f9e8ccebc5a8ddb57bccc44eb3d32b8cbe08589e\",\n  \"f7fcf0e0f3dbccebc5a8ddb57bccc44eb3d32b8cbe08589e\",\n  \"f7fcf0e0f3dbccebc5a8ddb57bccc44eb3d32b8cbe0868ac084081\"\n).map(colors);\n\nexport default ramp(scheme);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"fee8c8fdbb84e34a33\",\n  \"fef0d9fdcc8afc8d59d7301f\",\n  \"fef0d9fdcc8afc8d59e34a33b30000\",\n  \"fef0d9fdd49efdbb84fc8d59e34a33b30000\",\n  \"fef0d9fdd49efdbb84fc8d59ef6548d7301f990000\",\n  \"fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301f990000\",\n  \"fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301fb300007f0000\"\n).map(colors);\n\nexport default ramp(scheme);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"ece2f0a6bddb1c9099\",\n  \"f6eff7bdc9e167a9cf02818a\",\n  \"f6eff7bdc9e167a9cf1c9099016c59\",\n  \"f6eff7d0d1e6a6bddb67a9cf1c9099016c59\",\n  \"f6eff7d0d1e6a6bddb67a9cf3690c002818a016450\",\n  \"fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016450\",\n  \"fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016c59014636\"\n).map(colors);\n\nexport default ramp(scheme);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"ece7f2a6bddb2b8cbe\",\n  \"f1eef6bdc9e174a9cf0570b0\",\n  \"f1eef6bdc9e174a9cf2b8cbe045a8d\",\n  \"f1eef6d0d1e6a6bddb74a9cf2b8cbe045a8d\",\n  \"f1eef6d0d1e6a6bddb74a9cf3690c00570b0034e7b\",\n  \"fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0034e7b\",\n  \"fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0045a8d023858\"\n).map(colors);\n\nexport default ramp(scheme);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"e7e1efc994c7dd1c77\",\n  \"f1eef6d7b5d8df65b0ce1256\",\n  \"f1eef6d7b5d8df65b0dd1c77980043\",\n  \"f1eef6d4b9dac994c7df65b0dd1c77980043\",\n  \"f1eef6d4b9dac994c7df65b0e7298ace125691003f\",\n  \"f7f4f9e7e1efd4b9dac994c7df65b0e7298ace125691003f\",\n  \"f7f4f9e7e1efd4b9dac994c7df65b0e7298ace125698004367001f\"\n).map(colors);\n\nexport default ramp(scheme);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"fde0ddfa9fb5c51b8a\",\n  \"feebe2fbb4b9f768a1ae017e\",\n  \"feebe2fbb4b9f768a1c51b8a7a0177\",\n  \"feebe2fcc5c0fa9fb5f768a1c51b8a7a0177\",\n  \"feebe2fcc5c0fa9fb5f768a1dd3497ae017e7a0177\",\n  \"fff7f3fde0ddfcc5c0fa9fb5f768a1dd3497ae017e7a0177\",\n  \"fff7f3fde0ddfcc5c0fa9fb5f768a1dd3497ae017e7a017749006a\"\n).map(colors);\n\nexport default ramp(scheme);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"edf8b17fcdbb2c7fb8\",\n  \"ffffcca1dab441b6c4225ea8\",\n  \"ffffcca1dab441b6c42c7fb8253494\",\n  \"ffffccc7e9b47fcdbb41b6c42c7fb8253494\",\n  \"ffffccc7e9b47fcdbb41b6c41d91c0225ea80c2c84\",\n  \"ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea80c2c84\",\n  \"ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea8253494081d58\"\n).map(colors);\n\nexport default ramp(scheme);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"f7fcb9addd8e31a354\",\n  \"ffffccc2e69978c679238443\",\n  \"ffffccc2e69978c67931a354006837\",\n  \"ffffccd9f0a3addd8e78c67931a354006837\",\n  \"ffffccd9f0a3addd8e78c67941ab5d238443005a32\",\n  \"ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443005a32\",\n  \"ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443006837004529\"\n).map(colors);\n\nexport default ramp(scheme);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"fff7bcfec44fd95f0e\",\n  \"ffffd4fed98efe9929cc4c02\",\n  \"ffffd4fed98efe9929d95f0e993404\",\n  \"ffffd4fee391fec44ffe9929d95f0e993404\",\n  \"ffffd4fee391fec44ffe9929ec7014cc4c028c2d04\",\n  \"ffffe5fff7bcfee391fec44ffe9929ec7014cc4c028c2d04\",\n  \"ffffe5fff7bcfee391fec44ffe9929ec7014cc4c02993404662506\"\n).map(colors);\n\nexport default ramp(scheme);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"ffeda0feb24cf03b20\",\n  \"ffffb2fecc5cfd8d3ce31a1c\",\n  \"ffffb2fecc5cfd8d3cf03b20bd0026\",\n  \"ffffb2fed976feb24cfd8d3cf03b20bd0026\",\n  \"ffffb2fed976feb24cfd8d3cfc4e2ae31a1cb10026\",\n  \"ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cb10026\",\n  \"ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cbd0026800026\"\n).map(colors);\n\nexport default ramp(scheme);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"deebf79ecae13182bd\",\n  \"eff3ffbdd7e76baed62171b5\",\n  \"eff3ffbdd7e76baed63182bd08519c\",\n  \"eff3ffc6dbef9ecae16baed63182bd08519c\",\n  \"eff3ffc6dbef9ecae16baed64292c62171b5084594\",\n  \"f7fbffdeebf7c6dbef9ecae16baed64292c62171b5084594\",\n  \"f7fbffdeebf7c6dbef9ecae16baed64292c62171b508519c08306b\"\n).map(colors);\n\nexport default ramp(scheme);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"e5f5e0a1d99b31a354\",\n  \"edf8e9bae4b374c476238b45\",\n  \"edf8e9bae4b374c47631a354006d2c\",\n  \"edf8e9c7e9c0a1d99b74c47631a354006d2c\",\n  \"edf8e9c7e9c0a1d99b74c47641ab5d238b45005a32\",\n  \"f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45005a32\",\n  \"f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45006d2c00441b\"\n).map(colors);\n\nexport default ramp(scheme);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"f0f0f0bdbdbd636363\",\n  \"f7f7f7cccccc969696525252\",\n  \"f7f7f7cccccc969696636363252525\",\n  \"f7f7f7d9d9d9bdbdbd969696636363252525\",\n  \"f7f7f7d9d9d9bdbdbd969696737373525252252525\",\n  \"fffffff0f0f0d9d9d9bdbdbd969696737373525252252525\",\n  \"fffffff0f0f0d9d9d9bdbdbd969696737373525252252525000000\"\n).map(colors);\n\nexport default ramp(scheme);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"efedf5bcbddc756bb1\",\n  \"f2f0f7cbc9e29e9ac86a51a3\",\n  \"f2f0f7cbc9e29e9ac8756bb154278f\",\n  \"f2f0f7dadaebbcbddc9e9ac8756bb154278f\",\n  \"f2f0f7dadaebbcbddc9e9ac8807dba6a51a34a1486\",\n  \"fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a34a1486\",\n  \"fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a354278f3f007d\"\n).map(colors);\n\nexport default ramp(scheme);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"fee0d2fc9272de2d26\",\n  \"fee5d9fcae91fb6a4acb181d\",\n  \"fee5d9fcae91fb6a4ade2d26a50f15\",\n  \"fee5d9fcbba1fc9272fb6a4ade2d26a50f15\",\n  \"fee5d9fcbba1fc9272fb6a4aef3b2ccb181d99000d\",\n  \"fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181d99000d\",\n  \"fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181da50f1567000d\"\n).map(colors);\n\nexport default ramp(scheme);\n", "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"fee6cefdae6be6550d\",\n  \"feeddefdbe85fd8d3cd94701\",\n  \"feeddefdbe85fd8d3ce6550da63603\",\n  \"feeddefdd0a2fdae6bfd8d3ce6550da63603\",\n  \"feeddefdd0a2fdae6bfd8d3cf16913d948018c2d04\",\n  \"fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d948018c2d04\",\n  \"fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d94801a636037f2704\"\n).map(colors);\n\nexport default ramp(scheme);\n", "export default function(t) {\n  t = Math.max(0, Math.min(1, t));\n  return \"rgb(\"\n      + Math.max(0, Math.min(255, Math.round(-4.54 - t * (35.34 - t * (2381.73 - t * (6402.7 - t * (7024.72 - t * 2710.57))))))) + \", \"\n      + Math.max(0, Math.min(255, Math.round(32.49 + t * (170.73 + t * (52.82 - t * (131.46 - t * (176.58 - t * 67.37))))))) + \", \"\n      + Math.max(0, Math.min(255, Math.round(81.24 + t * (442.36 - t * (2482.43 - t * (6167.24 - t * (6614.94 - t * 2475.67)))))))\n      + \")\";\n}\n", "import {cubehelix} from \"d3-color\";\nimport {interpolateCubehelixLong} from \"d3-interpolate\";\n\nexport default interpolateCubehelixLong(cubehelix(300, 0.5, 0.0), cubehelix(-240, 0.5, 1.0));\n", "import {cubehelix} from \"d3-color\";\nimport {interpolateCubehelixLong} from \"d3-interpolate\";\n\nexport var warm = interpolateCubehelixLong(cubehelix(-100, 0.75, 0.35), cubehelix(80, 1.50, 0.8));\n\nexport var cool = interpolateCubehelixLong(cubehelix(260, 0.75, 0.35), cubehelix(80, 1.50, 0.8));\n\nvar c = cubehelix();\n\nexport default function(t) {\n  if (t < 0 || t > 1) t -= Math.floor(t);\n  var ts = Math.abs(t - 0.5);\n  c.h = 360 * t - 100;\n  c.s = 1.5 - 1.5 * ts;\n  c.l = 0.8 - 0.9 * ts;\n  return c + \"\";\n}\n", "import {rgb} from \"d3-color\";\n\nvar c = rgb(),\n    pi_1_3 = Math.PI / 3,\n    pi_2_3 = Math.PI * 2 / 3;\n\nexport default function(t) {\n  var x;\n  t = (0.5 - t) * Math.PI;\n  c.r = 255 * (x = Math.sin(t)) * x;\n  c.g = 255 * (x = Math.sin(t + pi_1_3)) * x;\n  c.b = 255 * (x = Math.sin(t + pi_2_3)) * x;\n  return c + \"\";\n}\n", "export default function(t) {\n  t = Math.max(0, Math.min(1, t));\n  return \"rgb(\"\n      + Math.max(0, Math.min(255, Math.round(34.61 + t * (1172.33 - t * (10793.56 - t * (33300.12 - t * (38394.49 - t * 14825.05))))))) + \", \"\n      + Math.max(0, Math.min(255, Math.round(23.31 + t * (557.33 + t * (1225.33 - t * (3574.96 - t * (1073.77 + t * 707.56))))))) + \", \"\n      + Math.max(0, Math.min(255, Math.round(27.2 + t * (3211.1 - t * (15327.97 - t * (27814 - t * (22569.18 - t * 6838.66)))))))\n      + \")\";\n}\n", "import colors from \"../colors.js\";\n\nfunction ramp(range) {\n  var n = range.length;\n  return function(t) {\n    return range[Math.max(0, Math.min(n - 1, Math.floor(t * n)))];\n  };\n}\n\nexport default ramp(colors(\"44015444025645045745055946075a46085c460a5d460b5e470d60470e6147106347116447136548146748166848176948186a481a6c481b6d481c6e481d6f481f70482071482173482374482475482576482677482878482979472a7a472c7a472d7b472e7c472f7d46307e46327e46337f463480453581453781453882443983443a83443b84433d84433e85423f854240864241864142874144874045884046883f47883f48893e49893e4a893e4c8a3d4d8a3d4e8a3c4f8a3c508b3b518b3b528b3a538b3a548c39558c39568c38588c38598c375a8c375b8d365c8d365d8d355e8d355f8d34608d34618d33628d33638d32648e32658e31668e31678e31688e30698e306a8e2f6b8e2f6c8e2e6d8e2e6e8e2e6f8e2d708e2d718e2c718e2c728e2c738e2b748e2b758e2a768e2a778e2a788e29798e297a8e297b8e287c8e287d8e277e8e277f8e27808e26818e26828e26828e25838e25848e25858e24868e24878e23888e23898e238a8d228b8d228c8d228d8d218e8d218f8d21908d21918c20928c20928c20938c1f948c1f958b1f968b1f978b1f988b1f998a1f9a8a1e9b8a1e9c891e9d891f9e891f9f881fa0881fa1881fa1871fa28720a38620a48621a58521a68522a78522a88423a98324aa8325ab8225ac8226ad8127ad8128ae8029af7f2ab07f2cb17e2db27d2eb37c2fb47c31b57b32b67a34b67935b77937b87838b9773aba763bbb753dbc743fbc7340bd7242be7144bf7046c06f48c16e4ac16d4cc26c4ec36b50c46a52c56954c56856c66758c7655ac8645cc8635ec96260ca6063cb5f65cb5e67cc5c69cd5b6ccd5a6ece5870cf5773d05675d05477d1537ad1517cd2507fd34e81d34d84d44b86d54989d5488bd6468ed64590d74393d74195d84098d83e9bd93c9dd93ba0da39a2da37a5db36a8db34aadc32addc30b0dd2fb2dd2db5de2bb8de29bade28bddf26c0df25c2df23c5e021c8e020cae11fcde11dd0e11cd2e21bd5e21ad8e219dae319dde318dfe318e2e418e5e419e7e419eae51aece51befe51cf1e51df4e61ef6e620f8e621fbe723fde725\"));\n\nexport var magma = ramp(colors(\"00000401000501010601010802010902020b02020d03030f03031204041405041606051806051a07061c08071e0907200a08220b09240c09260d0a290e0b2b100b2d110c2f120d31130d34140e36150e38160f3b180f3d19103f1a10421c10441d11471e114920114b21114e22115024125325125527125829115a2a115c2c115f2d11612f116331116533106734106936106b38106c390f6e3b0f703d0f713f0f72400f74420f75440f764510774710784910784a10794c117a4e117b4f127b51127c52137c54137d56147d57157e59157e5a167e5c167f5d177f5f187f601880621980641a80651a80671b80681c816a1c816b1d816d1d816e1e81701f81721f817320817521817621817822817922827b23827c23827e24828025828125818326818426818627818827818928818b29818c29818e2a81902a81912b81932b80942c80962c80982d80992d809b2e7f9c2e7f9e2f7fa02f7fa1307ea3307ea5317ea6317da8327daa337dab337cad347cae347bb0357bb2357bb3367ab5367ab73779b83779ba3878bc3978bd3977bf3a77c03a76c23b75c43c75c53c74c73d73c83e73ca3e72cc3f71cd4071cf4070d0416fd2426fd3436ed5446dd6456cd8456cd9466bdb476adc4869de4968df4a68e04c67e24d66e34e65e44f64e55064e75263e85362e95462ea5661eb5760ec5860ed5a5fee5b5eef5d5ef05f5ef1605df2625df2645cf3655cf4675cf4695cf56b5cf66c5cf66e5cf7705cf7725cf8745cf8765cf9785df9795df97b5dfa7d5efa7f5efa815ffb835ffb8560fb8761fc8961fc8a62fc8c63fc8e64fc9065fd9266fd9467fd9668fd9869fd9a6afd9b6bfe9d6cfe9f6dfea16efea36ffea571fea772fea973feaa74feac76feae77feb078feb27afeb47bfeb67cfeb77efeb97ffebb81febd82febf84fec185fec287fec488fec68afec88cfeca8dfecc8ffecd90fecf92fed194fed395fed597fed799fed89afdda9cfddc9efddea0fde0a1fde2a3fde3a5fde5a7fde7a9fde9aafdebacfcecaefceeb0fcf0b2fcf2b4fcf4b6fcf6b8fcf7b9fcf9bbfcfbbdfcfdbf\"));\n\nexport var inferno = ramp(colors(\"00000401000501010601010802010a02020c02020e03021004031204031405041706041907051b08051d09061f0a07220b07240c08260d08290e092b10092d110a30120a32140b34150b37160b39180c3c190c3e1b0c411c0c431e0c451f0c48210c4a230c4c240c4f260c51280b53290b552b0b572d0b592f0a5b310a5c320a5e340a5f3609613809623909633b09643d09653e0966400a67420a68440a68450a69470b6a490b6a4a0c6b4c0c6b4d0d6c4f0d6c510e6c520e6d540f6d550f6d57106e59106e5a116e5c126e5d126e5f136e61136e62146e64156e65156e67166e69166e6a176e6c186e6d186e6f196e71196e721a6e741a6e751b6e771c6d781c6d7a1d6d7c1d6d7d1e6d7f1e6c801f6c82206c84206b85216b87216b88226a8a226a8c23698d23698f24699025689225689326679526679727669827669a28659b29649d29649f2a63a02a63a22b62a32c61a52c60a62d60a82e5fa92e5eab2f5ead305dae305cb0315bb1325ab3325ab43359b63458b73557b93556ba3655bc3754bd3853bf3952c03a51c13a50c33b4fc43c4ec63d4dc73e4cc83f4bca404acb4149cc4248ce4347cf4446d04545d24644d34743d44842d54a41d74b3fd84c3ed94d3dda4e3cdb503bdd513ade5238df5337e05536e15635e25734e35933e45a31e55c30e65d2fe75e2ee8602de9612bea632aeb6429eb6628ec6726ed6925ee6a24ef6c23ef6e21f06f20f1711ff1731df2741cf3761bf37819f47918f57b17f57d15f67e14f68013f78212f78410f8850ff8870ef8890cf98b0bf98c0af98e09fa9008fa9207fa9407fb9606fb9706fb9906fb9b06fb9d07fc9f07fca108fca309fca50afca60cfca80dfcaa0ffcac11fcae12fcb014fcb216fcb418fbb61afbb81dfbba1ffbbc21fbbe23fac026fac228fac42afac62df9c72ff9c932f9cb35f8cd37f8cf3af7d13df7d340f6d543f6d746f5d949f5db4cf4dd4ff4df53f4e156f3e35af3e55df2e661f2e865f2ea69f1ec6df1ed71f1ef75f1f179f2f27df2f482f3f586f3f68af4f88ef5f992f6fa96f8fb9af9fc9dfafda1fcffa4\"));\n\nexport var plasma = ramp(colors(\"0d088710078813078916078a19068c1b068d1d068e20068f2206902406912605912805922a05932c05942e05952f059631059733059735049837049938049a3a049a3c049b3e049c3f049c41049d43039e44039e46039f48039f4903a04b03a14c02a14e02a25002a25102a35302a35502a45601a45801a45901a55b01a55c01a65e01a66001a66100a76300a76400a76600a76700a86900a86a00a86c00a86e00a86f00a87100a87201a87401a87501a87701a87801a87a02a87b02a87d03a87e03a88004a88104a78305a78405a78606a68707a68808a68a09a58b0aa58d0ba58e0ca48f0da4910ea3920fa39410a29511a19613a19814a099159f9a169f9c179e9d189d9e199da01a9ca11b9ba21d9aa31e9aa51f99a62098a72197a82296aa2395ab2494ac2694ad2793ae2892b02991b12a90b22b8fb32c8eb42e8db52f8cb6308bb7318ab83289ba3388bb3488bc3587bd3786be3885bf3984c03a83c13b82c23c81c33d80c43e7fc5407ec6417dc7427cc8437bc9447aca457acb4679cc4778cc4977cd4a76ce4b75cf4c74d04d73d14e72d24f71d35171d45270d5536fd5546ed6556dd7566cd8576bd9586ada5a6ada5b69db5c68dc5d67dd5e66de5f65de6164df6263e06363e16462e26561e26660e3685fe4695ee56a5de56b5de66c5ce76e5be76f5ae87059e97158e97257ea7457eb7556eb7655ec7754ed7953ed7a52ee7b51ef7c51ef7e50f07f4ff0804ef1814df1834cf2844bf3854bf3874af48849f48948f58b47f58c46f68d45f68f44f79044f79143f79342f89441f89540f9973ff9983ef99a3efa9b3dfa9c3cfa9e3bfb9f3afba139fba238fca338fca537fca636fca835fca934fdab33fdac33fdae32fdaf31fdb130fdb22ffdb42ffdb52efeb72dfeb82cfeba2cfebb2bfebd2afebe2afec029fdc229fdc328fdc527fdc627fdc827fdca26fdcb26fccd25fcce25fcd025fcd225fbd324fbd524fbd724fad824fada24f9dc24f9dd25f8df25f8e125f7e225f7e425f6e626f6e826f5e926f5eb27f4ed27f3ee27f3f027f2f227f1f426f1f525f0f724f0f921\"));\n"], "mappings": ";;;;;;;;;;;AAAA,SAAS,eAAe,UAAU,QAAQ;AACxC,MAAI,YAAY,mBAAmB,eAAe,SAAS,IAAI,GAAG;AAChE,uBAAmB,SAAS,IAAI,EAAE,UAAU,MAAM;AAAA,EACpD;AACF;AAEA,IAAI,mBAAmB;AAAA,EACrB,SAAS,SAASA,SAAQ,QAAQ;AAChC,mBAAeA,QAAO,UAAU,MAAM;AAAA,EACxC;AAAA,EACA,mBAAmB,SAASA,SAAQ,QAAQ;AAC1C,QAAI,WAAWA,QAAO,UAAU,IAAI,IAAI,IAAI,SAAS;AACrD,WAAO,EAAE,IAAI,EAAG,gBAAe,SAAS,CAAC,EAAE,UAAU,MAAM;AAAA,EAC7D;AACF;AAEA,IAAI,qBAAqB;AAAA,EACvB,QAAQ,SAASA,SAAQ,QAAQ;AAC/B,WAAO,OAAO;AAAA,EAChB;AAAA,EACA,OAAO,SAASA,SAAQ,QAAQ;AAC9B,IAAAA,UAASA,QAAO;AAChB,WAAO,MAAMA,QAAO,CAAC,GAAGA,QAAO,CAAC,GAAGA,QAAO,CAAC,CAAC;AAAA,EAC9C;AAAA,EACA,YAAY,SAASA,SAAQ,QAAQ;AACnC,QAAIC,eAAcD,QAAO,aAAa,IAAI,IAAI,IAAIC,aAAY;AAC9D,WAAO,EAAE,IAAI,EAAG,CAAAD,UAASC,aAAY,CAAC,GAAG,OAAO,MAAMD,QAAO,CAAC,GAAGA,QAAO,CAAC,GAAGA,QAAO,CAAC,CAAC;AAAA,EACvF;AAAA,EACA,YAAY,SAASA,SAAQ,QAAQ;AACnC,eAAWA,QAAO,aAAa,QAAQ,CAAC;AAAA,EAC1C;AAAA,EACA,iBAAiB,SAASA,SAAQ,QAAQ;AACxC,QAAIC,eAAcD,QAAO,aAAa,IAAI,IAAI,IAAIC,aAAY;AAC9D,WAAO,EAAE,IAAI,EAAG,YAAWA,aAAY,CAAC,GAAG,QAAQ,CAAC;AAAA,EACtD;AAAA,EACA,SAAS,SAASD,SAAQ,QAAQ;AAChC,kBAAcA,QAAO,aAAa,MAAM;AAAA,EAC1C;AAAA,EACA,cAAc,SAASA,SAAQ,QAAQ;AACrC,QAAIC,eAAcD,QAAO,aAAa,IAAI,IAAI,IAAIC,aAAY;AAC9D,WAAO,EAAE,IAAI,EAAG,eAAcA,aAAY,CAAC,GAAG,MAAM;AAAA,EACtD;AAAA,EACA,oBAAoB,SAASD,SAAQ,QAAQ;AAC3C,QAAI,aAAaA,QAAO,YAAY,IAAI,IAAI,IAAI,WAAW;AAC3D,WAAO,EAAE,IAAI,EAAG,gBAAe,WAAW,CAAC,GAAG,MAAM;AAAA,EACtD;AACF;AAEA,SAAS,WAAWC,cAAa,QAAQ,QAAQ;AAC/C,MAAI,IAAI,IAAI,IAAIA,aAAY,SAAS,QAAQ;AAC7C,SAAO,UAAU;AACjB,SAAO,EAAE,IAAI,EAAG,cAAaA,aAAY,CAAC,GAAG,OAAO,MAAM,WAAW,CAAC,GAAG,WAAW,CAAC,GAAG,WAAW,CAAC,CAAC;AACrG,SAAO,QAAQ;AACjB;AAEA,SAAS,cAAcA,cAAa,QAAQ;AAC1C,MAAI,IAAI,IAAI,IAAIA,aAAY;AAC5B,SAAO,aAAa;AACpB,SAAO,EAAE,IAAI,EAAG,YAAWA,aAAY,CAAC,GAAG,QAAQ,CAAC;AACpD,SAAO,WAAW;AACpB;AAEe,SAAR,eAAiBD,SAAQ,QAAQ;AACtC,MAAIA,WAAU,iBAAiB,eAAeA,QAAO,IAAI,GAAG;AAC1D,qBAAiBA,QAAO,IAAI,EAAEA,SAAQ,MAAM;AAAA,EAC9C,OAAO;AACL,mBAAeA,SAAQ,MAAM;AAAA,EAC/B;AACF;;;ACpEO,IAAI,UAAU;AACd,IAAI,WAAW;AACf,IAAI,KAAK,KAAK;AACd,IAAI,SAAS,KAAK;AAClB,IAAI,YAAY,KAAK;AACrB,IAAI,MAAM,KAAK;AAEf,IAAI,UAAU,MAAM;AACpB,IAAI,UAAU,KAAK;AAEnB,IAAI,MAAM,KAAK;AACf,IAAI,OAAO,KAAK;AAChB,IAAI,QAAQ,KAAK;AACjB,IAAI,MAAM,KAAK;AACf,IAAI,OAAO,KAAK;AAChB,IAAI,MAAM,KAAK;AAEf,IAAI,QAAQ,KAAK;AACjB,IAAI,MAAM,KAAK;AACf,IAAI,MAAM,KAAK;AACf,IAAI,MAAM,KAAK;AACf,IAAI,OAAO,KAAK,QAAQ,SAAS,GAAG;AAAE,SAAO,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK;AAAG;AACzE,IAAI,OAAO,KAAK;AAChB,IAAI,MAAM,KAAK;AAEf,SAAS,KAAK,GAAG;AACtB,SAAO,IAAI,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,CAAC;AAC9C;AAEO,SAAS,KAAK,GAAG;AACtB,SAAO,IAAI,IAAI,SAAS,IAAI,KAAK,CAAC,SAAS,KAAK,KAAK,CAAC;AACxD;AAEO,SAAS,SAAS,GAAG;AAC1B,UAAQ,IAAI,IAAI,IAAI,CAAC,KAAK;AAC5B;;;ACnCe,SAAR,OAAwB;AAAC;;;ACKzB,IAAI,cAAc,IAAI,MAAM;AAInC,IAAI,UAAU,IAAI,MAAM;AAAxB,IACI;AADJ,IAEI;AAFJ,IAGI;AAHJ,IAII;AAJJ,IAKI;AAEG,IAAI,aAAa;AAAA,EACtB,OAAO;AAAA,EACP,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc,WAAW;AACvB,kBAAc,IAAI,MAAM;AACxB,eAAW,YAAY;AACvB,eAAW,UAAU;AAAA,EACvB;AAAA,EACA,YAAY,WAAW;AACrB,QAAI,WAAW,CAAC;AAChB,YAAQ,IAAI,WAAW,IAAI,MAAM,WAAW,QAAQ;AACpD,SAAK,YAAY,KAAK,UAAU,KAAK,QAAQ;AAAA,EAC/C;AAAA,EACA,QAAQ,WAAW;AACjB,YAAQ,IAAI,GAAG;AAAA,EACjB;AACF;AAEA,SAAS,gBAAgB;AACvB,aAAW,QAAQ;AACrB;AAEA,SAAS,cAAc;AACrB,YAAU,UAAU,KAAK;AAC3B;AAEA,SAAS,eAAe,QAAQ,KAAK;AACnC,aAAW,QAAQ;AACnB,aAAW,QAAQ,QAAQ;AAC3B,YAAU,SAAS,OAAO;AAC1B,YAAU,QAAQ,UAAU,IAAI,MAAM,MAAM,IAAI,SAAS,GAAG,UAAU,IAAI,GAAG;AAC/E;AAEA,SAAS,UAAU,QAAQ,KAAK;AAC9B,YAAU,SAAS,OAAO;AAC1B,QAAM,MAAM,IAAI;AAKhB,MAAI,UAAU,SAAS,SACnB,WAAW,WAAW,IAAI,IAAI,IAC9B,WAAW,WAAW,SACtB,SAAS,IAAI,GAAG,GAChB,SAAS,IAAI,GAAG,GAChB,IAAI,UAAU,QACdE,KAAI,UAAU,SAAS,IAAI,IAAI,QAAQ,GACvCC,KAAI,IAAI,WAAW,IAAI,QAAQ;AACnC,cAAY,IAAI,MAAMA,IAAGD,EAAC,CAAC;AAG3B,YAAU,QAAQ,UAAU,QAAQ,UAAU;AAChD;AAEe,SAAR,aAAiBE,SAAQ;AAC9B,YAAU,IAAI,MAAM;AACpB,iBAAOA,SAAQ,UAAU;AACzB,SAAO,UAAU;AACnB;;;ACzEO,SAAS,UAAUC,YAAW;AACnC,SAAO,CAAC,MAAMA,WAAU,CAAC,GAAGA,WAAU,CAAC,CAAC,GAAG,KAAKA,WAAU,CAAC,CAAC,CAAC;AAC/D;AAEO,SAAS,UAAUC,YAAW;AACnC,MAAI,SAASA,WAAU,CAAC,GAAG,MAAMA,WAAU,CAAC,GAAG,SAAS,IAAI,GAAG;AAC/D,SAAO,CAAC,SAAS,IAAI,MAAM,GAAG,SAAS,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC;AAC9D;AAEO,SAAS,aAAa,GAAG,GAAG;AACjC,SAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAC/C;AAEO,SAAS,eAAe,GAAG,GAAG;AACnC,SAAO,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AACzF;AAGO,SAAS,oBAAoB,GAAG,GAAG;AACxC,IAAE,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;AACzC;AAEO,SAAS,eAAe,QAAQ,GAAG;AACxC,SAAO,CAAC,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;AACrD;AAGO,SAAS,0BAA0B,GAAG;AAC3C,MAAI,IAAI,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AACpD,IAAE,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK;AAChC;;;AC1BA,IAAIC;AAAJ,IAAa;AAAb,IAAmB;AAAnB,IAA4B;AAA5B,IACI;AADJ,IAEIC;AAFJ,IAEcC;AAFd,IAGI;AAHJ,IAII;AAJJ,IAKI;AALJ,IAMIC;AAEJ,IAAI,eAAe;AAAA,EACjB,OAAO;AAAA,EACP,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc,WAAW;AACvB,iBAAa,QAAQ;AACrB,iBAAa,YAAY;AACzB,iBAAa,UAAU;AACvB,eAAW,IAAI,MAAM;AACrB,eAAW,aAAa;AAAA,EAC1B;AAAA,EACA,YAAY,WAAW;AACrB,eAAW,WAAW;AACtB,iBAAa,QAAQ;AACrB,iBAAa,YAAY;AACzB,iBAAa,UAAU;AACvB,QAAI,cAAc,EAAG,CAAAH,WAAU,EAAE,UAAU,MAAM,OAAO,EAAE,OAAO;AAAA,aACxD,WAAW,QAAS,QAAO;AAAA,aAC3B,WAAW,CAAC,QAAS,QAAO;AACrC,IAAAG,OAAM,CAAC,IAAIH,UAASG,OAAM,CAAC,IAAI;AAAA,EACjC;AAAA,EACA,QAAQ,WAAW;AACjB,IAAAH,WAAU,EAAE,UAAU,MAAM,OAAO,EAAE,OAAO;AAAA,EAC9C;AACF;AAEA,SAAS,YAAY,QAAQ,KAAK;AAChC,SAAO,KAAKG,SAAQ,CAACH,WAAU,QAAQ,UAAU,MAAM,CAAC;AACxD,MAAI,MAAM,KAAM,QAAO;AACvB,MAAI,MAAM,KAAM,QAAO;AACzB;AAEA,SAAS,UAAU,QAAQ,KAAK;AAC9B,MAAI,IAAI,UAAU,CAAC,SAAS,SAAS,MAAM,OAAO,CAAC;AACnD,MAAI,IAAI;AACN,QAAI,SAAS,eAAe,IAAI,CAAC,GAC7B,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,GACtC,aAAa,eAAe,YAAY,MAAM;AAClD,8BAA0B,UAAU;AACpC,iBAAa,UAAU,UAAU;AACjC,QAAI,QAAQ,SAAS,SACjBI,QAAO,QAAQ,IAAI,IAAI,IACvB,UAAU,WAAW,CAAC,IAAI,UAAUA,OACpC,MACA,eAAe,IAAI,KAAK,IAAI;AAChC,QAAI,gBAAgBA,QAAO,UAAU,WAAW,UAAUA,QAAO,SAAS;AACxE,aAAO,WAAW,CAAC,IAAI;AACvB,UAAI,OAAO,KAAM,QAAO;AAAA,IAC1B,WAAW,WAAW,UAAU,OAAO,MAAM,KAAK,gBAAgBA,QAAO,UAAU,WAAW,UAAUA,QAAO,SAAS;AACtH,aAAO,CAAC,WAAW,CAAC,IAAI;AACxB,UAAI,OAAO,KAAM,QAAO;AAAA,IAC1B,OAAO;AACL,UAAI,MAAM,KAAM,QAAO;AACvB,UAAI,MAAM,KAAM,QAAO;AAAA,IACzB;AACA,QAAI,cAAc;AAChB,UAAI,SAAS,SAAS;AACpB,YAAI,MAAMJ,UAAS,MAAM,IAAI,MAAMA,UAAS,OAAO,EAAG,WAAU;AAAA,MAClE,OAAO;AACL,YAAI,MAAM,QAAQ,OAAO,IAAI,MAAMA,UAAS,OAAO,EAAG,CAAAA,WAAU;AAAA,MAClE;AAAA,IACF,OAAO;AACL,UAAI,WAAWA,UAAS;AACtB,YAAI,SAASA,SAAS,CAAAA,WAAU;AAChC,YAAI,SAAS,QAAS,WAAU;AAAA,MAClC,OAAO;AACL,YAAI,SAAS,SAAS;AACpB,cAAI,MAAMA,UAAS,MAAM,IAAI,MAAMA,UAAS,OAAO,EAAG,WAAU;AAAA,QAClE,OAAO;AACL,cAAI,MAAM,QAAQ,OAAO,IAAI,MAAMA,UAAS,OAAO,EAAG,CAAAA,WAAU;AAAA,QAClE;AAAA,MACF;AAAA,IACF;AAAA,EACF,OAAO;AACL,WAAO,KAAKG,SAAQ,CAACH,WAAU,QAAQ,UAAU,MAAM,CAAC;AAAA,EAC1D;AACA,MAAI,MAAM,KAAM,QAAO;AACvB,MAAI,MAAM,KAAM,QAAO;AACvB,OAAK,GAAG,UAAU;AACpB;AAEA,SAAS,kBAAkB;AACzB,eAAa,QAAQ;AACvB;AAEA,SAAS,gBAAgB;AACvB,EAAAG,OAAM,CAAC,IAAIH,UAASG,OAAM,CAAC,IAAI;AAC/B,eAAa,QAAQ;AACrB,OAAK;AACP;AAEA,SAAS,gBAAgB,QAAQ,KAAK;AACpC,MAAI,IAAI;AACN,QAAI,QAAQ,SAAS;AACrB,aAAS,IAAI,IAAI,KAAK,IAAI,MAAM,SAAS,QAAQ,IAAI,MAAM,QAAQ,KAAK;AAAA,EAC1E,OAAO;AACL,IAAAF,YAAW,QAAQC,SAAQ;AAAA,EAC7B;AACA,aAAW,MAAM,QAAQ,GAAG;AAC5B,YAAU,QAAQ,GAAG;AACvB;AAEA,SAAS,kBAAkB;AACzB,aAAW,UAAU;AACvB;AAEA,SAAS,gBAAgB;AACvB,kBAAgBD,WAAUC,MAAK;AAC/B,aAAW,QAAQ;AACnB,MAAI,IAAI,QAAQ,IAAI,QAAS,CAAAF,WAAU,EAAE,UAAU;AACnD,EAAAG,OAAM,CAAC,IAAIH,UAASG,OAAM,CAAC,IAAI;AAC/B,OAAK;AACP;AAKA,SAAS,MAAMH,UAASK,UAAS;AAC/B,UAAQA,YAAWL,YAAW,IAAIK,WAAU,MAAMA;AACpD;AAEA,SAAS,aAAa,GAAG,GAAG;AAC1B,SAAO,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB;AAEA,SAAS,cAAcF,QAAO,GAAG;AAC/B,SAAOA,OAAM,CAAC,KAAKA,OAAM,CAAC,IAAIA,OAAM,CAAC,KAAK,KAAK,KAAKA,OAAM,CAAC,IAAI,IAAIA,OAAM,CAAC,KAAKA,OAAM,CAAC,IAAI;AAC5F;AAEe,SAAR,eAAiB,SAAS;AAC/B,MAAI,GAAG,GAAG,GAAG,GAAG,QAAQ,UAAU;AAElC,SAAO,UAAU,EAAEH,WAAU,OAAO;AACpC,WAAS,CAAC;AACV,iBAAO,SAAS,YAAY;AAG5B,MAAI,IAAI,OAAO,QAAQ;AACrB,WAAO,KAAK,YAAY;AAGxB,SAAK,IAAI,GAAG,IAAI,OAAO,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG;AACnD,UAAI,OAAO,CAAC;AACZ,UAAI,cAAc,GAAG,EAAE,CAAC,CAAC,KAAK,cAAc,GAAG,EAAE,CAAC,CAAC,GAAG;AACpD,YAAI,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AACrD,YAAI,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,MACvD,OAAO;AACL,eAAO,KAAK,IAAI,CAAC;AAAA,MACnB;AAAA,IACF;AAIA,SAAK,WAAW,WAAW,IAAI,OAAO,SAAS,GAAG,IAAI,GAAG,IAAI,OAAO,CAAC,GAAG,KAAK,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1F,UAAI,OAAO,CAAC;AACZ,WAAK,QAAQ,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,SAAU,YAAW,OAAOA,WAAU,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC;AAAA,IAC7F;AAAA,EACF;AAEA,WAASG,SAAQ;AAEjB,SAAOH,aAAY,YAAY,SAAS,WAClC,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,GAAG,CAAC,IACvB,CAAC,CAACA,UAAS,IAAI,GAAG,CAAC,SAAS,IAAI,CAAC;AACzC;;;AC7KA,IAAI;AAAJ,IAAQ;AAAR,IACI;AADJ,IACQ;AADR,IACY;AADZ,IAEI;AAFJ,IAEQ;AAFR,IAEY;AAFZ,IAGI;AAHJ,IAGQ;AAHR,IAGY;AAHZ,IAIIM;AAJJ,IAIcC;AAJd,IAKI;AALJ,IAKQ;AALR,IAKY;AAEZ,IAAI,iBAAiB;AAAA,EACnB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc,WAAW;AACvB,mBAAe,YAAY;AAC3B,mBAAe,UAAU;AAAA,EAC3B;AAAA,EACA,YAAY,WAAW;AACrB,mBAAe,YAAY;AAC3B,mBAAe,UAAU;AAAA,EAC3B;AACF;AAGA,SAAS,cAAc,QAAQ,KAAK;AAClC,YAAU,SAAS,OAAO;AAC1B,MAAI,SAAS,IAAI,GAAG;AACpB,yBAAuB,SAAS,IAAI,MAAM,GAAG,SAAS,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC;AAC7E;AAEA,SAAS,uBAAuB,GAAG,GAAG,GAAG;AACvC,IAAE;AACF,SAAO,IAAI,MAAM;AACjB,SAAO,IAAI,MAAM;AACjB,SAAO,IAAI,MAAM;AACnB;AAEA,SAAS,oBAAoB;AAC3B,iBAAe,QAAQ;AACzB;AAEA,SAAS,uBAAuB,QAAQ,KAAK;AAC3C,YAAU,SAAS,OAAO;AAC1B,MAAI,SAAS,IAAI,GAAG;AACpB,OAAK,SAAS,IAAI,MAAM;AACxB,OAAK,SAAS,IAAI,MAAM;AACxB,OAAK,IAAI,GAAG;AACZ,iBAAe,QAAQ;AACvB,yBAAuB,IAAI,IAAI,EAAE;AACnC;AAEA,SAAS,kBAAkB,QAAQ,KAAK;AACtC,YAAU,SAAS,OAAO;AAC1B,MAAI,SAAS,IAAI,GAAG,GAChB,IAAI,SAAS,IAAI,MAAM,GACvB,IAAI,SAAS,IAAI,MAAM,GACvB,IAAI,IAAI,GAAG,GACX,IAAI,MAAM,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC;AAC/H,QAAM;AACN,QAAM,KAAK,MAAM,KAAK;AACtB,QAAM,KAAK,MAAM,KAAK;AACtB,QAAM,KAAK,MAAM,KAAK;AACtB,yBAAuB,IAAI,IAAI,EAAE;AACnC;AAEA,SAAS,kBAAkB;AACzB,iBAAe,QAAQ;AACzB;AAIA,SAAS,oBAAoB;AAC3B,iBAAe,QAAQ;AACzB;AAEA,SAAS,kBAAkB;AACzB,oBAAkBD,WAAUC,MAAK;AACjC,iBAAe,QAAQ;AACzB;AAEA,SAAS,uBAAuB,QAAQ,KAAK;AAC3C,EAAAD,YAAW,QAAQC,SAAQ;AAC3B,YAAU,SAAS,OAAO;AAC1B,iBAAe,QAAQ;AACvB,MAAI,SAAS,IAAI,GAAG;AACpB,OAAK,SAAS,IAAI,MAAM;AACxB,OAAK,SAAS,IAAI,MAAM;AACxB,OAAK,IAAI,GAAG;AACZ,yBAAuB,IAAI,IAAI,EAAE;AACnC;AAEA,SAAS,kBAAkB,QAAQ,KAAK;AACtC,YAAU,SAAS,OAAO;AAC1B,MAAI,SAAS,IAAI,GAAG,GAChB,IAAI,SAAS,IAAI,MAAM,GACvB,IAAI,SAAS,IAAI,MAAM,GACvB,IAAI,IAAI,GAAG,GACX,KAAK,KAAK,IAAI,KAAK,GACnB,KAAK,KAAK,IAAI,KAAK,GACnB,KAAK,KAAK,IAAI,KAAK,GACnB,IAAI,MAAM,IAAI,IAAI,EAAE,GACpB,IAAI,KAAK,CAAC,GACVC,KAAI,KAAK,CAAC,IAAI;AAClB,KAAG,IAAIA,KAAI,EAAE;AACb,KAAG,IAAIA,KAAI,EAAE;AACb,KAAG,IAAIA,KAAI,EAAE;AACb,QAAM;AACN,QAAM,KAAK,MAAM,KAAK;AACtB,QAAM,KAAK,MAAM,KAAK;AACtB,QAAM,KAAK,MAAM,KAAK;AACtB,yBAAuB,IAAI,IAAI,EAAE;AACnC;AAEe,SAAR,iBAAiBC,SAAQ;AAC9B,OAAK,KACL,KAAK,KAAK,KACV,KAAK,KAAK,KAAK;AACf,OAAK,IAAI,MAAM;AACf,OAAK,IAAI,MAAM;AACf,OAAK,IAAI,MAAM;AACf,iBAAOA,SAAQ,cAAc;AAE7B,MAAI,IAAI,CAAC,IACL,IAAI,CAAC,IACL,IAAI,CAAC,IACL,IAAI,MAAM,GAAG,GAAG,CAAC;AAGrB,MAAI,IAAI,UAAU;AAChB,QAAI,IAAI,IAAI,IAAI,IAAI;AAEpB,QAAI,KAAK,QAAS,KAAI,IAAI,IAAI,IAAI,IAAI;AACtC,QAAI,MAAM,GAAG,GAAG,CAAC;AAEjB,QAAI,IAAI,SAAU,QAAO,CAAC,KAAK,GAAG;AAAA,EACpC;AAEA,SAAO,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,KAAK,IAAI,CAAC,IAAI,OAAO;AACtD;;;AC9Ie,SAAR,gBAAiB,GAAG,GAAG;AAE5B,WAAS,QAAQ,GAAG,GAAG;AACrB,WAAO,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,EAClC;AAEA,MAAI,EAAE,UAAU,EAAE,OAAQ,SAAQ,SAAS,SAAS,GAAG,GAAG;AACxD,WAAO,IAAI,EAAE,OAAO,GAAG,CAAC,GAAG,KAAK,EAAE,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,EACrD;AAEA,SAAO;AACT;;;ACRA,SAAS,iBAAiB,QAAQ,KAAK;AACrC,MAAI,IAAI,MAAM,IAAI,GAAI,WAAU,KAAK,MAAM,SAAS,GAAG,IAAI;AAC3D,SAAO,CAAC,QAAQ,GAAG;AACrB;AAEA,iBAAiB,SAAS;AAEnB,SAAS,cAAc,aAAa,UAAU,YAAY;AAC/D,UAAQ,eAAe,OAAQ,YAAY,aAAa,gBAAQ,eAAe,WAAW,GAAG,iBAAiB,UAAU,UAAU,CAAC,IAC/H,eAAe,WAAW,IACzB,YAAY,aAAa,iBAAiB,UAAU,UAAU,IAC/D;AACN;AAEA,SAAS,sBAAsB,aAAa;AAC1C,SAAO,SAAS,QAAQ,KAAK;AAC3B,cAAU;AACV,QAAI,IAAI,MAAM,IAAI,GAAI,WAAU,KAAK,MAAM,SAAS,GAAG,IAAI;AAC3D,WAAO,CAAC,QAAQ,GAAG;AAAA,EACrB;AACF;AAEA,SAAS,eAAe,aAAa;AACnC,MAAI,WAAW,sBAAsB,WAAW;AAChD,WAAS,SAAS,sBAAsB,CAAC,WAAW;AACpD,SAAO;AACT;AAEA,SAAS,iBAAiB,UAAU,YAAY;AAC9C,MAAI,cAAc,IAAI,QAAQ,GAC1B,cAAc,IAAI,QAAQ,GAC1B,gBAAgB,IAAI,UAAU,GAC9B,gBAAgB,IAAI,UAAU;AAElC,WAAS,SAAS,QAAQ,KAAK;AAC7B,QAAI,SAAS,IAAI,GAAG,GAChB,IAAI,IAAI,MAAM,IAAI,QAClB,IAAI,IAAI,MAAM,IAAI,QAClB,IAAI,IAAI,GAAG,GACX,IAAI,IAAI,cAAc,IAAI;AAC9B,WAAO;AAAA,MACL,MAAM,IAAI,gBAAgB,IAAI,eAAe,IAAI,cAAc,IAAI,WAAW;AAAA,MAC9E,KAAK,IAAI,gBAAgB,IAAI,aAAa;AAAA,IAC5C;AAAA,EACF;AAEA,WAAS,SAAS,SAAS,QAAQ,KAAK;AACtC,QAAI,SAAS,IAAI,GAAG,GAChB,IAAI,IAAI,MAAM,IAAI,QAClB,IAAI,IAAI,MAAM,IAAI,QAClB,IAAI,IAAI,GAAG,GACX,IAAI,IAAI,gBAAgB,IAAI;AAChC,WAAO;AAAA,MACL,MAAM,IAAI,gBAAgB,IAAI,eAAe,IAAI,cAAc,IAAI,WAAW;AAAA,MAC9E,KAAK,IAAI,cAAc,IAAI,WAAW;AAAA,IACxC;AAAA,EACF;AAEA,SAAO;AACT;AAEe,SAAR,iBAAiB,QAAQ;AAC9B,WAAS,cAAc,OAAO,CAAC,IAAI,SAAS,OAAO,CAAC,IAAI,SAAS,OAAO,SAAS,IAAI,OAAO,CAAC,IAAI,UAAU,CAAC;AAE5G,WAAS,QAAQC,cAAa;AAC5B,IAAAA,eAAc,OAAOA,aAAY,CAAC,IAAI,SAASA,aAAY,CAAC,IAAI,OAAO;AACvE,WAAOA,aAAY,CAAC,KAAK,SAASA,aAAY,CAAC,KAAK,SAASA;AAAA,EAC/D;AAEA,UAAQ,SAAS,SAASA,cAAa;AACrC,IAAAA,eAAc,OAAO,OAAOA,aAAY,CAAC,IAAI,SAASA,aAAY,CAAC,IAAI,OAAO;AAC9E,WAAOA,aAAY,CAAC,KAAK,SAASA,aAAY,CAAC,KAAK,SAASA;AAAA,EAC/D;AAEA,SAAO;AACT;;;AC9Ee,SAAR,iBAAiB,GAAG;AACzB,SAAO,WAAW;AAChB,WAAO;AAAA,EACT;AACF;;;ACEO,SAAS,aAAa,QAAQ,QAAQ,OAAO,WAAW,IAAI,IAAI;AACrE,MAAI,CAAC,MAAO;AACZ,MAAI,YAAY,IAAI,MAAM,GACtB,YAAY,IAAI,MAAM,GACtB,OAAO,YAAY;AACvB,MAAI,MAAM,MAAM;AACd,SAAK,SAAS,YAAY;AAC1B,SAAK,SAAS,OAAO;AAAA,EACvB,OAAO;AACL,SAAK,aAAa,WAAW,EAAE;AAC/B,SAAK,aAAa,WAAW,EAAE;AAC/B,QAAI,YAAY,IAAI,KAAK,KAAK,KAAK,GAAI,OAAM,YAAY;AAAA,EAC3D;AACA,WAAS,OAAO,IAAI,IAAI,YAAY,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM;AAClE,YAAQ,UAAU,CAAC,WAAW,CAAC,YAAY,IAAI,CAAC,GAAG,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC;AACvE,WAAO,MAAM,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,EACjC;AACF;AAGA,SAAS,aAAa,WAAW,OAAO;AACtC,UAAQ,UAAU,KAAK,GAAG,MAAM,CAAC,KAAK;AACtC,4BAA0B,KAAK;AAC/B,MAAI,SAAS,KAAK,CAAC,MAAM,CAAC,CAAC;AAC3B,WAAS,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,SAAS,UAAU,MAAM,WAAW;AAChE;AAEe,SAAR,iBAAmB;AACxB,MAAI,SAAS,iBAAS,CAAC,GAAG,CAAC,CAAC,GACxB,SAAS,iBAAS,EAAE,GACpB,YAAY,iBAAS,CAAC,GACtB,MACA,QACA,SAAS,EAAC,MAAY;AAE1B,WAAS,MAAM,GAAG,GAAG;AACnB,SAAK,KAAK,IAAI,OAAO,GAAG,CAAC,CAAC;AAC1B,MAAE,CAAC,KAAK,SAAS,EAAE,CAAC,KAAK;AAAA,EAC3B;AAEA,WAAS,SAAS;AAChB,QAAIC,KAAI,OAAO,MAAM,MAAM,SAAS,GAChC,IAAI,OAAO,MAAM,MAAM,SAAS,IAAI,SACpC,IAAI,UAAU,MAAM,MAAM,SAAS,IAAI;AAC3C,WAAO,CAAC;AACR,aAAS,cAAc,CAACA,GAAE,CAAC,IAAI,SAAS,CAACA,GAAE,CAAC,IAAI,SAAS,CAAC,EAAE;AAC5D,iBAAa,QAAQ,GAAG,GAAG,CAAC;AAC5B,IAAAA,KAAI,EAAC,MAAM,WAAW,aAAa,CAAC,IAAI,EAAC;AACzC,WAAO,SAAS;AAChB,WAAOA;AAAA,EACT;AAEA,SAAO,SAAS,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,SAAS,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU;AAAA,EACxG;AAEA,SAAO,SAAS,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,SAAS,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,GAAG,UAAU;AAAA,EAC5F;AAEA,SAAO,YAAY,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,YAAY,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,GAAG,UAAU;AAAA,EAC/F;AAEA,SAAO;AACT;;;ACrEe,SAAR,iBAAmB;AACxB,MAAI,QAAQ,CAAC,GACT;AACJ,SAAO;AAAA,IACL,OAAO,SAAS,GAAG,GAAG,GAAG;AACvB,WAAK,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,IACrB;AAAA,IACA,WAAW,WAAW;AACpB,YAAM,KAAK,OAAO,CAAC,CAAC;AAAA,IACtB;AAAA,IACA,SAAS;AAAA,IACT,QAAQ,WAAW;AACjB,UAAI,MAAM,SAAS,EAAG,OAAM,KAAK,MAAM,IAAI,EAAE,OAAO,MAAM,MAAM,CAAC,CAAC;AAAA,IACpE;AAAA,IACA,QAAQ,WAAW;AACjB,UAAI,SAAS;AACb,cAAQ,CAAC;AACT,aAAO;AACP,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;ACrBe,SAAR,mBAAiB,GAAG,GAAG;AAC5B,SAAO,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,WAAW,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI;AAC1D;;;ACDA,SAAS,aAAa,OAAO,QAAQ,OAAO,OAAO;AACjD,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI,KAAK,IAAI;AACpB;AAKe,SAAR,eAAiB,UAAUC,sBAAqB,aAAa,aAAa,QAAQ;AACvF,MAAI,UAAU,CAAC,GACX,OAAO,CAAC,GACR,GACA;AAEJ,WAAS,QAAQ,SAAS,SAAS;AACjC,SAAKC,KAAI,QAAQ,SAAS,MAAM,EAAG;AACnC,QAAIA,IAAGC,MAAK,QAAQ,CAAC,GAAG,KAAK,QAAQD,EAAC,GAAG;AAEzC,QAAI,mBAAWC,KAAI,EAAE,GAAG;AACtB,UAAI,CAACA,IAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG;AACpB,eAAO,UAAU;AACjB,aAAK,IAAI,GAAG,IAAID,IAAG,EAAE,EAAG,QAAO,OAAOC,MAAK,QAAQ,CAAC,GAAG,CAAC,GAAGA,IAAG,CAAC,CAAC;AAChE,eAAO,QAAQ;AACf;AAAA,MACF;AAEA,SAAG,CAAC,KAAK,IAAI;AAAA,IACf;AAEA,YAAQ,KAAK,IAAI,IAAI,aAAaA,KAAI,SAAS,MAAM,IAAI,CAAC;AAC1D,SAAK,KAAK,EAAE,IAAI,IAAI,aAAaA,KAAI,MAAM,GAAG,KAAK,CAAC;AACpD,YAAQ,KAAK,IAAI,IAAI,aAAa,IAAI,SAAS,MAAM,KAAK,CAAC;AAC3D,SAAK,KAAK,EAAE,IAAI,IAAI,aAAa,IAAI,MAAM,GAAG,IAAI,CAAC;AAAA,EACrD,CAAC;AAED,MAAI,CAAC,QAAQ,OAAQ;AAErB,OAAK,KAAKF,oBAAmB;AAC7B,OAAK,OAAO;AACZ,OAAK,IAAI;AAET,OAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,EAAE,GAAG;AACvC,SAAK,CAAC,EAAE,IAAI,cAAc,CAAC;AAAA,EAC7B;AAEA,MAAI,QAAQ,QAAQ,CAAC,GACjB,QACA;AAEJ,SAAO,GAAG;AAER,QAAI,UAAU,OACV,YAAY;AAChB,WAAO,QAAQ,EAAG,MAAK,UAAU,QAAQ,OAAO,MAAO;AACvD,aAAS,QAAQ;AACjB,WAAO,UAAU;AACjB,OAAG;AACD,cAAQ,IAAI,QAAQ,EAAE,IAAI;AAC1B,UAAI,QAAQ,GAAG;AACb,YAAI,WAAW;AACb,eAAK,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,EAAG,QAAO,OAAO,QAAQ,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,QAC1F,OAAO;AACL,sBAAY,QAAQ,GAAG,QAAQ,EAAE,GAAG,GAAG,MAAM;AAAA,QAC/C;AACA,kBAAU,QAAQ;AAAA,MACpB,OAAO;AACL,YAAI,WAAW;AACb,mBAAS,QAAQ,EAAE;AACnB,eAAK,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,EAAE,EAAG,QAAO,OAAO,QAAQ,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,QACxF,OAAO;AACL,sBAAY,QAAQ,GAAG,QAAQ,EAAE,GAAG,IAAI,MAAM;AAAA,QAChD;AACA,kBAAU,QAAQ;AAAA,MACpB;AACA,gBAAU,QAAQ;AAClB,eAAS,QAAQ;AACjB,kBAAY,CAAC;AAAA,IACf,SAAS,CAAC,QAAQ;AAClB,WAAO,QAAQ;AAAA,EACjB;AACF;AAEA,SAAS,KAAK,OAAO;AACnB,MAAI,EAAE,IAAI,MAAM,QAAS;AACzB,MAAI,GACA,IAAI,GACJ,IAAI,MAAM,CAAC,GACX;AACJ,SAAO,EAAE,IAAI,GAAG;AACd,MAAE,IAAI,IAAI,MAAM,CAAC;AACjB,MAAE,IAAI;AACN,QAAI;AAAA,EACN;AACA,IAAE,IAAI,IAAI,MAAM,CAAC;AACjB,IAAE,IAAI;AACR;;;AClGA,SAAS,UAAU,OAAO;AACxB,SAAO,IAAI,MAAM,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC,IAAI,MAAM,MAAM;AACzF;AAEe,SAAR,wBAAiB,SAAS,OAAO;AACtC,MAAI,SAAS,UAAU,KAAK,GACxB,MAAM,MAAM,CAAC,GACb,SAAS,IAAI,GAAG,GAChB,SAAS,CAAC,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,GACtCG,SAAQ,GACR,UAAU;AAEd,MAAIC,OAAM,IAAI,MAAM;AAEpB,MAAI,WAAW,EAAG,OAAM,SAAS;AAAA,WACxB,WAAW,GAAI,OAAM,CAAC,SAAS;AAExC,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC9C,QAAI,EAAE,KAAK,OAAO,QAAQ,CAAC,GAAG,QAAS;AACvC,QAAI,MACA,GACA,SAAS,KAAK,IAAI,CAAC,GACnBC,WAAU,UAAU,MAAM,GAC1BC,QAAO,OAAO,CAAC,IAAI,IAAI,WACvBC,WAAU,IAAID,KAAI,GAClBE,WAAU,IAAIF,KAAI;AAEtB,aAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAGD,WAAUI,UAASF,WAAU,SAASC,WAAU,SAAS,SAAS,QAAQ;AACpG,UAAI,SAAS,KAAK,CAAC,GACfC,WAAU,UAAU,MAAM,GAC1BC,QAAO,OAAO,CAAC,IAAI,IAAI,WACvB,UAAU,IAAIA,KAAI,GAClB,UAAU,IAAIA,KAAI,GAClB,QAAQD,WAAUJ,UAClBM,QAAO,SAAS,IAAI,IAAI,IACxB,WAAWA,QAAO,OAClB,eAAe,WAAW,IAC1B,IAAIJ,WAAU;AAElB,MAAAH,KAAI,IAAI,MAAM,IAAIO,QAAO,IAAI,QAAQ,GAAGH,WAAU,UAAU,IAAI,IAAI,QAAQ,CAAC,CAAC;AAC9E,MAAAL,UAAS,eAAe,QAAQQ,QAAO,MAAM;AAI7C,UAAI,eAAeN,YAAW,SAASI,YAAW,QAAQ;AACxD,YAAI,MAAM,eAAe,UAAU,MAAM,GAAG,UAAU,MAAM,CAAC;AAC7D,kCAA0B,GAAG;AAC7B,YAAI,eAAe,eAAe,QAAQ,GAAG;AAC7C,kCAA0B,YAAY;AACtC,YAAI,UAAU,eAAe,SAAS,IAAI,KAAK,KAAK,KAAK,aAAa,CAAC,CAAC;AACxE,YAAI,MAAM,UAAU,QAAQ,WAAW,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI;AACxD,qBAAW,eAAe,SAAS,IAAI,IAAI;AAAA,QAC7C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAaA,UAAQN,SAAQ,CAAC,WAAWA,SAAQ,WAAWC,OAAM,CAAC,YAAa,UAAU;AAC/E;;;ACnEe,SAAR,aAAiB,cAAc,UAAU,aAAa,OAAO;AAClE,SAAO,SAAS,MAAM;AACpB,QAAI,OAAO,SAAS,IAAI,GACpB,aAAa,eAAW,GACxB,WAAW,SAAS,UAAU,GAC9B,iBAAiB,OACjB,SACA,UACA;AAEJ,QAAI,OAAO;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc,WAAW;AACvB,aAAK,QAAQ;AACb,aAAK,YAAY;AACjB,aAAK,UAAU;AACf,mBAAW,CAAC;AACZ,kBAAU,CAAC;AAAA,MACb;AAAA,MACA,YAAY,WAAW;AACrB,aAAK,QAAQ;AACb,aAAK,YAAY;AACjB,aAAK,UAAU;AACf,mBAAW,MAAM,QAAQ;AACzB,YAAI,cAAc,wBAAgB,SAAS,KAAK;AAChD,YAAI,SAAS,QAAQ;AACnB,cAAI,CAAC,eAAgB,MAAK,aAAa,GAAG,iBAAiB;AAC3D,yBAAW,UAAU,qBAAqB,aAAa,aAAa,IAAI;AAAA,QAC1E,WAAW,aAAa;AACtB,cAAI,CAAC,eAAgB,MAAK,aAAa,GAAG,iBAAiB;AAC3D,eAAK,UAAU;AACf,sBAAY,MAAM,MAAM,GAAG,IAAI;AAC/B,eAAK,QAAQ;AAAA,QACf;AACA,YAAI,eAAgB,MAAK,WAAW,GAAG,iBAAiB;AACxD,mBAAW,UAAU;AAAA,MACvB;AAAA,MACA,QAAQ,WAAW;AACjB,aAAK,aAAa;AAClB,aAAK,UAAU;AACf,oBAAY,MAAM,MAAM,GAAG,IAAI;AAC/B,aAAK,QAAQ;AACb,aAAK,WAAW;AAAA,MAClB;AAAA,IACF;AAEA,aAAS,MAAM,QAAQ,KAAK;AAC1B,UAAI,aAAa,QAAQ,GAAG,EAAG,MAAK,MAAM,QAAQ,GAAG;AAAA,IACvD;AAEA,aAAS,UAAU,QAAQ,KAAK;AAC9B,WAAK,MAAM,QAAQ,GAAG;AAAA,IACxB;AAEA,aAAS,YAAY;AACnB,WAAK,QAAQ;AACb,WAAK,UAAU;AAAA,IACjB;AAEA,aAAS,UAAU;AACjB,WAAK,QAAQ;AACb,WAAK,QAAQ;AAAA,IACf;AAEA,aAAS,UAAU,QAAQ,KAAK;AAC9B,WAAK,KAAK,CAAC,QAAQ,GAAG,CAAC;AACvB,eAAS,MAAM,QAAQ,GAAG;AAAA,IAC5B;AAEA,aAAS,YAAY;AACnB,eAAS,UAAU;AACnB,aAAO,CAAC;AAAA,IACV;AAEA,aAAS,UAAU;AACjB,gBAAU,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC;AAChC,eAAS,QAAQ;AAEjB,UAAI,QAAQ,SAAS,MAAM,GACvB,eAAe,WAAW,OAAO,GACjC,GAAG,IAAI,aAAa,QAAQ,GAC5B,SACAQ;AAEJ,WAAK,IAAI;AACT,cAAQ,KAAK,IAAI;AACjB,aAAO;AAEP,UAAI,CAAC,EAAG;AAGR,UAAI,QAAQ,GAAG;AACb,kBAAU,aAAa,CAAC;AACxB,aAAK,IAAI,QAAQ,SAAS,KAAK,GAAG;AAChC,cAAI,CAAC,eAAgB,MAAK,aAAa,GAAG,iBAAiB;AAC3D,eAAK,UAAU;AACf,eAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,MAAK,OAAOA,SAAQ,QAAQ,CAAC,GAAG,CAAC,GAAGA,OAAM,CAAC,CAAC;AACpE,eAAK,QAAQ;AAAA,QACf;AACA;AAAA,MACF;AAIA,UAAI,IAAI,KAAK,QAAQ,EAAG,cAAa,KAAK,aAAa,IAAI,EAAE,OAAO,aAAa,MAAM,CAAC,CAAC;AAEzF,eAAS,KAAK,aAAa,OAAO,YAAY,CAAC;AAAA,IACjD;AAEA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,aAAa,SAAS;AAC7B,SAAO,QAAQ,SAAS;AAC1B;AAIA,SAAS,oBAAoB,GAAG,GAAG;AACjC,WAAS,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,SAAS,UAAU,SAAS,EAAE,CAAC,OACxD,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,SAAS,UAAU,SAAS,EAAE,CAAC;AACnE;;;AC/HA,IAAO,uBAAQ;AAAA,EACb,WAAW;AAAE,WAAO;AAAA,EAAM;AAAA,EAC1B;AAAA,EACA;AAAA,EACA,CAAC,CAAC,IAAI,CAAC,MAAM;AACf;AAKA,SAAS,qBAAqB,QAAQ;AACpC,MAAIC,WAAU,KACVC,QAAO,KACP,QAAQ,KACR;AAEJ,SAAO;AAAA,IACL,WAAW,WAAW;AACpB,aAAO,UAAU;AACjB,cAAQ;AAAA,IACV;AAAA,IACA,OAAO,SAASC,UAASC,OAAM;AAC7B,UAAI,QAAQD,WAAU,IAAI,KAAK,CAAC,IAC5B,QAAQ,IAAIA,WAAUF,QAAO;AACjC,UAAI,IAAI,QAAQ,EAAE,IAAI,SAAS;AAC7B,eAAO,MAAMA,UAASC,SAAQA,QAAOE,SAAQ,IAAI,IAAI,SAAS,CAAC,MAAM;AACrE,eAAO,MAAM,OAAOF,KAAI;AACxB,eAAO,QAAQ;AACf,eAAO,UAAU;AACjB,eAAO,MAAM,OAAOA,KAAI;AACxB,eAAO,MAAMC,UAASD,KAAI;AAC1B,gBAAQ;AAAA,MACV,WAAW,UAAU,SAAS,SAAS,IAAI;AACzC,YAAI,IAAID,WAAU,KAAK,IAAI,QAAS,CAAAA,YAAW,QAAQ;AACvD,YAAI,IAAIE,WAAU,KAAK,IAAI,QAAS,CAAAA,YAAW,QAAQ;AACvD,QAAAD,QAAO,0BAA0BD,UAASC,OAAMC,UAASC,KAAI;AAC7D,eAAO,MAAM,OAAOF,KAAI;AACxB,eAAO,QAAQ;AACf,eAAO,UAAU;AACjB,eAAO,MAAM,OAAOA,KAAI;AACxB,gBAAQ;AAAA,MACV;AACA,aAAO,MAAMD,WAAUE,UAASD,QAAOE,KAAI;AAC3C,cAAQ;AAAA,IACV;AAAA,IACA,SAAS,WAAW;AAClB,aAAO,QAAQ;AACf,MAAAH,WAAUC,QAAO;AAAA,IACnB;AAAA,IACA,OAAO,WAAW;AAChB,aAAO,IAAI;AAAA,IACb;AAAA,EACF;AACF;AAEA,SAAS,0BAA0BD,UAASC,OAAMC,UAASC,OAAM;AAC/D,MAAIC,UACA,SACA,oBAAoB,IAAIJ,WAAUE,QAAO;AAC7C,SAAO,IAAI,iBAAiB,IAAI,UAC1B,MAAM,IAAID,KAAI,KAAK,UAAU,IAAIE,KAAI,KAAK,IAAID,QAAO,IACjD,IAAIC,KAAI,KAAKC,WAAU,IAAIH,KAAI,KAAK,IAAID,QAAO,MAC9CI,WAAU,UAAU,kBAAkB,KAC1CH,QAAOE,SAAQ;AACxB;AAEA,SAAS,4BAA4B,MAAM,IAAI,WAAW,QAAQ;AAChE,MAAI;AACJ,MAAI,QAAQ,MAAM;AAChB,UAAM,YAAY;AAClB,WAAO,MAAM,CAAC,IAAI,GAAG;AACrB,WAAO,MAAM,GAAG,GAAG;AACnB,WAAO,MAAM,IAAI,GAAG;AACpB,WAAO,MAAM,IAAI,CAAC;AAClB,WAAO,MAAM,IAAI,CAAC,GAAG;AACrB,WAAO,MAAM,GAAG,CAAC,GAAG;AACpB,WAAO,MAAM,CAAC,IAAI,CAAC,GAAG;AACtB,WAAO,MAAM,CAAC,IAAI,CAAC;AACnB,WAAO,MAAM,CAAC,IAAI,GAAG;AAAA,EACvB,WAAW,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,SAAS;AACzC,QAAI,SAAS,KAAK,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,CAAC;AACrC,UAAM,YAAY,SAAS;AAC3B,WAAO,MAAM,CAAC,QAAQ,GAAG;AACzB,WAAO,MAAM,GAAG,GAAG;AACnB,WAAO,MAAM,QAAQ,GAAG;AAAA,EAC1B,OAAO;AACL,WAAO,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,EAC3B;AACF;;;ACrFe,SAARE,gBAAiB,QAAQ;AAC9B,MAAI,KAAK,IAAI,MAAM,GACf,QAAQ,IAAI,SACZ,cAAc,KAAK,GACnB,gBAAgB,IAAI,EAAE,IAAI;AAE9B,WAAS,YAAY,MAAM,IAAI,WAAW,QAAQ;AAChD,iBAAa,QAAQ,QAAQ,OAAO,WAAW,MAAM,EAAE;AAAA,EACzD;AAEA,WAAS,QAAQ,QAAQ,KAAK;AAC5B,WAAO,IAAI,MAAM,IAAI,IAAI,GAAG,IAAI;AAAA,EAClC;AAMA,WAAS,SAAS,QAAQ;AACxB,QAAI,QACA,IACA,IACA,KACA;AACJ,WAAO;AAAA,MACL,WAAW,WAAW;AACpB,cAAM,KAAK;AACX,gBAAQ;AAAA,MACV;AAAA,MACA,OAAO,SAAS,QAAQ,KAAK;AAC3B,YAAI,SAAS,CAAC,QAAQ,GAAG,GACrB,QACAC,KAAI,QAAQ,QAAQ,GAAG,GACvBC,KAAI,cACAD,KAAI,IAAI,KAAK,QAAQ,GAAG,IACxBA,KAAI,KAAK,UAAU,SAAS,IAAI,KAAK,CAAC,KAAK,GAAG,IAAI;AAC1D,YAAI,CAAC,WAAW,MAAM,KAAKA,IAAI,QAAO,UAAU;AAChD,YAAIA,OAAM,IAAI;AACZ,mBAAS,UAAU,QAAQ,MAAM;AACjC,cAAI,CAAC,UAAU,mBAAW,QAAQ,MAAM,KAAK,mBAAW,QAAQ,MAAM;AACpE,mBAAO,CAAC,IAAI;AAAA,QAChB;AACA,YAAIA,OAAM,IAAI;AACZ,kBAAQ;AACR,cAAIA,IAAG;AAEL,mBAAO,UAAU;AACjB,qBAAS,UAAU,QAAQ,MAAM;AACjC,mBAAO,MAAM,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,UACnC,OAAO;AAEL,qBAAS,UAAU,QAAQ,MAAM;AACjC,mBAAO,MAAM,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC;AACpC,mBAAO,QAAQ;AAAA,UACjB;AACA,mBAAS;AAAA,QACX,WAAW,iBAAiB,UAAU,cAAcA,IAAG;AACrD,cAAI;AAGJ,cAAI,EAAEC,KAAI,QAAQ,IAAI,UAAU,QAAQ,QAAQ,IAAI,IAAI;AACtD,oBAAQ;AACR,gBAAI,aAAa;AACf,qBAAO,UAAU;AACjB,qBAAO,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7B,qBAAO,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7B,qBAAO,QAAQ;AAAA,YACjB,OAAO;AACL,qBAAO,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7B,qBAAO,QAAQ;AACf,qBAAO,UAAU;AACjB,qBAAO,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC;AAAA,YAClC;AAAA,UACF;AAAA,QACF;AACA,YAAID,OAAM,CAAC,UAAU,CAAC,mBAAW,QAAQ,MAAM,IAAI;AACjD,iBAAO,MAAM,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,QACnC;AACA,iBAAS,QAAQ,KAAKA,IAAG,KAAKC;AAAA,MAChC;AAAA,MACA,SAAS,WAAW;AAClB,YAAI,GAAI,QAAO,QAAQ;AACvB,iBAAS;AAAA,MACX;AAAA;AAAA;AAAA,MAGA,OAAO,WAAW;AAChB,eAAO,SAAU,OAAO,OAAO;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAGA,WAAS,UAAU,GAAG,GAAG,KAAK;AAC5B,QAAI,KAAK,UAAU,CAAC,GAChB,KAAK,UAAU,CAAC;AAIpB,QAAI,KAAK,CAAC,GAAG,GAAG,CAAC,GACb,KAAK,eAAe,IAAI,EAAE,GAC1B,OAAO,aAAa,IAAI,EAAE,GAC1B,OAAO,GAAG,CAAC,GACX,cAAc,OAAO,OAAO;AAGhC,QAAI,CAAC,YAAa,QAAO,CAAC,OAAO;AAEjC,QAAI,KAAM,KAAK,OAAO,aAClBC,MAAK,CAAC,KAAK,OAAO,aAClB,QAAQ,eAAe,IAAI,EAAE,GAC7B,IAAI,eAAe,IAAI,EAAE,GACzBC,KAAI,eAAe,IAAID,GAAE;AAC7B,wBAAoB,GAAGC,EAAC;AAGxB,QAAIC,KAAI,OACJ,IAAI,aAAa,GAAGA,EAAC,GACrB,KAAK,aAAaA,IAAGA,EAAC,GACtB,KAAK,IAAI,IAAI,MAAM,aAAa,GAAG,CAAC,IAAI;AAE5C,QAAI,KAAK,EAAG;AAEZ,QAAI,IAAI,KAAK,EAAE,GACX,IAAI,eAAeA,KAAI,CAAC,IAAI,KAAK,EAAE;AACvC,wBAAoB,GAAG,CAAC;AACxB,QAAI,UAAU,CAAC;AAEf,QAAI,CAAC,IAAK,QAAO;AAGjB,QAAIC,WAAU,EAAE,CAAC,GACbC,WAAU,EAAE,CAAC,GACbC,QAAO,EAAE,CAAC,GACVC,QAAO,EAAE,CAAC,GACV;AAEJ,QAAIF,WAAUD,SAAS,KAAIA,UAASA,WAAUC,UAASA,WAAU;AAEjE,QAAIG,SAAQH,WAAUD,UAClB,QAAQ,IAAII,SAAQ,EAAE,IAAI,SAC1B,WAAW,SAASA,SAAQ;AAEhC,QAAI,CAAC,SAASD,QAAOD,MAAM,KAAIA,OAAMA,QAAOC,OAAMA,QAAO;AAGzD,QAAI,WACE,QACED,QAAOC,QAAO,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,IAAIH,QAAO,IAAI,UAAUE,QAAOC,SACjED,SAAQ,EAAE,CAAC,KAAK,EAAE,CAAC,KAAKC,QAC1BC,SAAQ,MAAMJ,YAAW,EAAE,CAAC,KAAK,EAAE,CAAC,KAAKC,WAAU;AACvD,UAAI,KAAK,eAAeF,KAAI,CAAC,IAAI,KAAK,EAAE;AACxC,0BAAoB,IAAI,CAAC;AACzB,aAAO,CAAC,GAAG,UAAU,EAAE,CAAC;AAAA,IAC1B;AAAA,EACF;AAIA,WAAS,KAAK,QAAQ,KAAK;AACzB,QAAI,IAAI,cAAc,SAAS,KAAK,QAChCM,QAAO;AACX,QAAI,SAAS,CAAC,EAAG,CAAAA,SAAQ;AAAA,aAChB,SAAS,EAAG,CAAAA,SAAQ;AAC7B,QAAI,MAAM,CAAC,EAAG,CAAAA,SAAQ;AAAA,aACb,MAAM,EAAG,CAAAA,SAAQ;AAC1B,WAAOA;AAAA,EACT;AAEA,SAAO,aAAK,SAAS,UAAU,aAAa,cAAc,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC,IAAI,SAAS,EAAE,CAAC;AAC7F;;;AChLe,SAAR,aAAiB,GAAG,GAAGC,KAAIC,KAAIC,KAAIC,KAAI;AAC5C,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,GACL,KAAK,GACL,KAAK,KAAK,IACV,KAAK,KAAK,IACV;AAEJ,MAAIH,MAAK;AACT,MAAI,CAAC,MAAM,IAAI,EAAG;AAClB,OAAK;AACL,MAAI,KAAK,GAAG;AACV,QAAI,IAAI,GAAI;AACZ,QAAI,IAAI,GAAI,MAAK;AAAA,EACnB,WAAW,KAAK,GAAG;AACjB,QAAI,IAAI,GAAI;AACZ,QAAI,IAAI,GAAI,MAAK;AAAA,EACnB;AAEA,MAAIE,MAAK;AACT,MAAI,CAAC,MAAM,IAAI,EAAG;AAClB,OAAK;AACL,MAAI,KAAK,GAAG;AACV,QAAI,IAAI,GAAI;AACZ,QAAI,IAAI,GAAI,MAAK;AAAA,EACnB,WAAW,KAAK,GAAG;AACjB,QAAI,IAAI,GAAI;AACZ,QAAI,IAAI,GAAI,MAAK;AAAA,EACnB;AAEA,MAAID,MAAK;AACT,MAAI,CAAC,MAAM,IAAI,EAAG;AAClB,OAAK;AACL,MAAI,KAAK,GAAG;AACV,QAAI,IAAI,GAAI;AACZ,QAAI,IAAI,GAAI,MAAK;AAAA,EACnB,WAAW,KAAK,GAAG;AACjB,QAAI,IAAI,GAAI;AACZ,QAAI,IAAI,GAAI,MAAK;AAAA,EACnB;AAEA,MAAIE,MAAK;AACT,MAAI,CAAC,MAAM,IAAI,EAAG;AAClB,OAAK;AACL,MAAI,KAAK,GAAG;AACV,QAAI,IAAI,GAAI;AACZ,QAAI,IAAI,GAAI,MAAK;AAAA,EACnB,WAAW,KAAK,GAAG;AACjB,QAAI,IAAI,GAAI;AACZ,QAAI,IAAI,GAAI,MAAK;AAAA,EACnB;AAEA,MAAI,KAAK,EAAG,GAAE,CAAC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC,IAAI,KAAK,KAAK;AAClD,MAAI,KAAK,EAAG,GAAE,CAAC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC,IAAI,KAAK,KAAK;AAClD,SAAO;AACT;;;ACpDA,IAAI,UAAU;AAAd,IAAmB,UAAU,CAAC;AAKf,SAAR,cAA+BC,KAAIC,KAAIC,KAAIC,KAAI;AAEpD,WAAS,QAAQ,GAAG,GAAG;AACrB,WAAOH,OAAM,KAAK,KAAKE,OAAMD,OAAM,KAAK,KAAKE;AAAA,EAC/C;AAEA,WAAS,YAAY,MAAM,IAAI,WAAW,QAAQ;AAChD,QAAI,IAAI,GAAG,KAAK;AAChB,QAAI,QAAQ,SACJ,IAAI,OAAO,MAAM,SAAS,QAAQ,KAAK,OAAO,IAAI,SAAS,MAC5D,aAAa,MAAM,EAAE,IAAI,IAAI,YAAY,GAAG;AACjD;AAAG,eAAO,MAAM,MAAM,KAAK,MAAM,IAAIH,MAAKE,KAAI,IAAI,IAAIC,MAAKF,GAAE;AAAA,cACrD,KAAK,IAAI,YAAY,KAAK,OAAO;AAAA,IAC3C,OAAO;AACL,aAAO,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,IAC3B;AAAA,EACF;AAEA,WAAS,OAAO,GAAG,WAAW;AAC5B,WAAO,IAAI,EAAE,CAAC,IAAID,GAAE,IAAI,UAAU,YAAY,IAAI,IAAI,IAChD,IAAI,EAAE,CAAC,IAAIE,GAAE,IAAI,UAAU,YAAY,IAAI,IAAI,IAC/C,IAAI,EAAE,CAAC,IAAID,GAAE,IAAI,UAAU,YAAY,IAAI,IAAI,IAC/C,YAAY,IAAI,IAAI;AAAA,EAC5B;AAEA,WAASG,qBAAoB,GAAG,GAAG;AACjC,WAAO,aAAa,EAAE,GAAG,EAAE,CAAC;AAAA,EAC9B;AAEA,WAAS,aAAa,GAAG,GAAG;AAC1B,QAAIC,MAAK,OAAO,GAAG,CAAC,GAChB,KAAK,OAAO,GAAG,CAAC;AACpB,WAAOA,QAAO,KAAKA,MAAK,KAClBA,QAAO,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IACrBA,QAAO,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IACrBA,QAAO,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IACrB,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,EAClB;AAEA,SAAO,SAAS,QAAQ;AACtB,QAAI,eAAe,QACf,eAAe,eAAW,GAC1B,UACA,SACA,MACA,KAAK,KAAK,KACV,IAAI,IAAI,IACR,OACA;AAEJ,QAAI,aAAa;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,aAAS,MAAM,GAAG,GAAG;AACnB,UAAI,QAAQ,GAAG,CAAC,EAAG,cAAa,MAAM,GAAG,CAAC;AAAA,IAC5C;AAEA,aAAS,gBAAgB;AACvB,UAAI,UAAU;AAEd,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC9C,iBAASC,QAAO,QAAQ,CAAC,GAAG,IAAI,GAAG,IAAIA,MAAK,QAAQC,SAAQD,MAAK,CAAC,GAAG,IAAI,IAAI,KAAKC,OAAM,CAAC,GAAG,KAAKA,OAAM,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG;AACrH,eAAK,IAAI,KAAK,IAAIA,SAAQD,MAAK,CAAC,GAAG,KAAKC,OAAM,CAAC,GAAG,KAAKA,OAAM,CAAC;AAC9D,cAAI,MAAMJ,KAAI;AAAE,gBAAI,KAAKA,QAAO,KAAK,OAAOA,MAAK,OAAO,KAAK,OAAOH,MAAK,IAAK,GAAE;AAAA,UAAS,OACpF;AAAE,gBAAI,MAAMG,QAAO,KAAK,OAAOA,MAAK,OAAO,KAAK,OAAOH,MAAK,IAAK,GAAE;AAAA,UAAS;AAAA,QACnF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAGA,aAAS,eAAe;AACtB,qBAAe,cAAc,WAAW,CAAC,GAAG,UAAU,CAAC,GAAG,QAAQ;AAAA,IACpE;AAEA,aAAS,aAAa;AACpB,UAAI,cAAc,cAAc,GAC5B,cAAc,SAAS,aACvBQ,YAAW,WAAW,MAAM,QAAQ,GAAG;AAC3C,UAAI,eAAeA,UAAS;AAC1B,eAAO,aAAa;AACpB,YAAI,aAAa;AACf,iBAAO,UAAU;AACjB,sBAAY,MAAM,MAAM,GAAG,MAAM;AACjC,iBAAO,QAAQ;AAAA,QACjB;AACA,YAAIA,UAAS;AACX,yBAAW,UAAUJ,sBAAqB,aAAa,aAAa,MAAM;AAAA,QAC5E;AACA,eAAO,WAAW;AAAA,MACpB;AACA,qBAAe,QAAQ,WAAW,UAAU,OAAO;AAAA,IACrD;AAEA,aAAS,YAAY;AACnB,iBAAW,QAAQK;AACnB,UAAI,QAAS,SAAQ,KAAK,OAAO,CAAC,CAAC;AACnC,cAAQ;AACR,WAAK;AACL,WAAK,KAAK;AAAA,IACZ;AAKA,aAAS,UAAU;AACjB,UAAI,UAAU;AACZ,QAAAA,WAAU,KAAK,GAAG;AAClB,YAAI,OAAO,GAAI,cAAa,OAAO;AACnC,iBAAS,KAAK,aAAa,OAAO,CAAC;AAAA,MACrC;AACA,iBAAW,QAAQ;AACnB,UAAI,GAAI,cAAa,QAAQ;AAAA,IAC/B;AAEA,aAASA,WAAU,GAAG,GAAG;AACvB,UAAIC,KAAI,QAAQ,GAAG,CAAC;AACpB,UAAI,QAAS,MAAK,KAAK,CAAC,GAAG,CAAC,CAAC;AAC7B,UAAI,OAAO;AACT,cAAM,GAAG,MAAM,GAAG,MAAMA;AACxB,gBAAQ;AACR,YAAIA,IAAG;AACL,uBAAa,UAAU;AACvB,uBAAa,MAAM,GAAG,CAAC;AAAA,QACzB;AAAA,MACF,OAAO;AACL,YAAIA,MAAK,GAAI,cAAa,MAAM,GAAG,CAAC;AAAA,aAC/B;AACH,cAAI,IAAI,CAAC,KAAK,KAAK,IAAI,SAAS,KAAK,IAAI,SAAS,EAAE,CAAC,GAAG,KAAK,KAAK,IAAI,SAAS,KAAK,IAAI,SAAS,EAAE,CAAC,CAAC,GACjG,IAAI,CAAC,IAAI,KAAK,IAAI,SAAS,KAAK,IAAI,SAAS,CAAC,CAAC,GAAG,IAAI,KAAK,IAAI,SAAS,KAAK,IAAI,SAAS,CAAC,CAAC,CAAC;AACjG,cAAI,aAAS,GAAG,GAAGV,KAAIC,KAAIC,KAAIC,GAAE,GAAG;AAClC,gBAAI,CAAC,IAAI;AACP,2BAAa,UAAU;AACvB,2BAAa,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,YAC/B;AACA,yBAAa,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC7B,gBAAI,CAACO,GAAG,cAAa,QAAQ;AAC7B,oBAAQ;AAAA,UACV,WAAWA,IAAG;AACZ,yBAAa,UAAU;AACvB,yBAAa,MAAM,GAAG,CAAC;AACvB,oBAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF;AACA,WAAK,GAAG,KAAK,GAAG,KAAKA;AAAA,IACvB;AAEA,WAAO;AAAA,EACT;AACF;;;ACrKe,SAAR,iBAAmB;AACxB,MAAIC,MAAK,GACLC,MAAK,GACLC,MAAK,KACLC,MAAK,KACL,OACA,aACA;AAEJ,SAAO,OAAO;AAAA,IACZ,QAAQ,SAAS,QAAQ;AACvB,aAAO,SAAS,gBAAgB,SAAS,QAAQ,QAAQ,cAAcH,KAAIC,KAAIC,KAAIC,GAAE,EAAE,cAAc,MAAM;AAAA,IAC7G;AAAA,IACA,QAAQ,SAAS,GAAG;AAClB,aAAO,UAAU,UAAUH,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,QAAQ,cAAc,MAAM,QAAQ,CAAC,CAACH,KAAIC,GAAE,GAAG,CAACC,KAAIC,GAAE,CAAC;AAAA,IAChJ;AAAA,EACF;AACF;;;ACdA,IAAI;AAAJ,IACIC;AADJ,IAEIC;AAFJ,IAGIC;AAEJ,IAAI,eAAe;AAAA,EACjB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc;AAAA,EACd,YAAY;AACd;AAEA,SAAS,kBAAkB;AACzB,eAAa,QAAQ;AACrB,eAAa,UAAU;AACzB;AAEA,SAAS,gBAAgB;AACvB,eAAa,QAAQ,aAAa,UAAU;AAC9C;AAEA,SAAS,iBAAiB,QAAQ,KAAK;AACrC,YAAU,SAAS,OAAO;AAC1B,EAAAF,WAAU,QAAQC,WAAU,IAAI,GAAG,GAAGC,WAAU,IAAI,GAAG;AACvD,eAAa,QAAQ;AACvB;AAEA,SAAS,YAAY,QAAQ,KAAK;AAChC,YAAU,SAAS,OAAO;AAC1B,MAAI,SAAS,IAAI,GAAG,GAChB,SAAS,IAAI,GAAG,GAChB,QAAQ,IAAI,SAASF,QAAO,GAC5B,WAAW,IAAI,KAAK,GACpB,WAAW,IAAI,KAAK,GACpB,IAAI,SAAS,UACb,IAAIE,WAAU,SAASD,WAAU,SAAS,UAC1C,IAAIA,WAAU,SAASC,WAAU,SAAS;AAC9C,YAAU,IAAI,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3C,EAAAF,WAAU,QAAQC,WAAU,QAAQC,WAAU;AAChD;AAEe,SAAR,eAAiBC,SAAQ;AAC9B,cAAY,IAAI,MAAM;AACtB,iBAAOA,SAAQ,YAAY;AAC3B,SAAO,CAAC;AACV;;;AClDA,IAAI,cAAc,CAAC,MAAM,IAAI;AAA7B,IACI,SAAS,EAAC,MAAM,cAAc,YAAwB;AAE3C,SAAR,iBAAiB,GAAG,GAAG;AAC5B,cAAY,CAAC,IAAI;AACjB,cAAY,CAAC,IAAI;AACjB,SAAO,eAAO,MAAM;AACtB;;;ACLA,IAAI,qBAAqB;AAAA,EACvB,SAAS,SAASC,SAAQ,OAAO;AAC/B,WAAO,iBAAiBA,QAAO,UAAU,KAAK;AAAA,EAChD;AAAA,EACA,mBAAmB,SAASA,SAAQ,OAAO;AACzC,QAAI,WAAWA,QAAO,UAAU,IAAI,IAAI,IAAI,SAAS;AACrD,WAAO,EAAE,IAAI,EAAG,KAAI,iBAAiB,SAAS,CAAC,EAAE,UAAU,KAAK,EAAG,QAAO;AAC1E,WAAO;AAAA,EACT;AACF;AAEA,IAAI,uBAAuB;AAAA,EACzB,QAAQ,WAAW;AACjB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,SAASA,SAAQ,OAAO;AAC7B,WAAO,cAAcA,QAAO,aAAa,KAAK;AAAA,EAChD;AAAA,EACA,YAAY,SAASA,SAAQ,OAAO;AAClC,QAAIC,eAAcD,QAAO,aAAa,IAAI,IAAI,IAAIC,aAAY;AAC9D,WAAO,EAAE,IAAI,EAAG,KAAI,cAAcA,aAAY,CAAC,GAAG,KAAK,EAAG,QAAO;AACjE,WAAO;AAAA,EACT;AAAA,EACA,YAAY,SAASD,SAAQ,OAAO;AAClC,WAAO,aAAaA,QAAO,aAAa,KAAK;AAAA,EAC/C;AAAA,EACA,iBAAiB,SAASA,SAAQ,OAAO;AACvC,QAAIC,eAAcD,QAAO,aAAa,IAAI,IAAI,IAAIC,aAAY;AAC9D,WAAO,EAAE,IAAI,EAAG,KAAI,aAAaA,aAAY,CAAC,GAAG,KAAK,EAAG,QAAO;AAChE,WAAO;AAAA,EACT;AAAA,EACA,SAAS,SAASD,SAAQ,OAAO;AAC/B,WAAO,gBAAgBA,QAAO,aAAa,KAAK;AAAA,EAClD;AAAA,EACA,cAAc,SAASA,SAAQ,OAAO;AACpC,QAAIC,eAAcD,QAAO,aAAa,IAAI,IAAI,IAAIC,aAAY;AAC9D,WAAO,EAAE,IAAI,EAAG,KAAI,gBAAgBA,aAAY,CAAC,GAAG,KAAK,EAAG,QAAO;AACnE,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB,SAASD,SAAQ,OAAO;AAC1C,QAAI,aAAaA,QAAO,YAAY,IAAI,IAAI,IAAI,WAAW;AAC3D,WAAO,EAAE,IAAI,EAAG,KAAI,iBAAiB,WAAW,CAAC,GAAG,KAAK,EAAG,QAAO;AACnE,WAAO;AAAA,EACT;AACF;AAEA,SAAS,iBAAiB,UAAU,OAAO;AACzC,SAAO,YAAY,qBAAqB,eAAe,SAAS,IAAI,IAC9D,qBAAqB,SAAS,IAAI,EAAE,UAAU,KAAK,IACnD;AACR;AAEA,SAAS,cAAcC,cAAa,OAAO;AACzC,SAAO,iBAASA,cAAa,KAAK,MAAM;AAC1C;AAEA,SAAS,aAAaA,cAAa,OAAO;AACxC,MAAI,IAAI,IAAIC;AACZ,WAAS,IAAI,GAAG,IAAID,aAAY,QAAQ,IAAI,GAAG,KAAK;AAClD,SAAK,iBAASA,aAAY,CAAC,GAAG,KAAK;AACnC,QAAI,OAAO,EAAG,QAAO;AACrB,QAAI,IAAI,GAAG;AACT,MAAAC,MAAK,iBAASD,aAAY,CAAC,GAAGA,aAAY,IAAI,CAAC,CAAC;AAChD,UACEC,MAAK,KACL,MAAMA,OACN,MAAMA,QACL,KAAK,KAAKA,QAAO,IAAI,KAAK,KAAK,KAAK,MAAMA,KAAI,CAAC,KAAK,WAAWA;AAEhE,eAAO;AAAA,IACX;AACA,SAAK;AAAA,EACP;AACA,SAAO;AACT;AAEA,SAAS,gBAAgBD,cAAa,OAAO;AAC3C,SAAO,CAAC,CAAC,wBAAgBA,aAAY,IAAI,WAAW,GAAG,aAAa,KAAK,CAAC;AAC5E;AAEA,SAAS,YAAY,MAAM;AACzB,SAAO,OAAO,KAAK,IAAI,YAAY,GAAG,KAAK,IAAI,GAAG;AACpD;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,CAAC,MAAM,CAAC,IAAI,SAAS,MAAM,CAAC,IAAI,OAAO;AAChD;AAEe,SAAR,iBAAiBD,SAAQ,OAAO;AACrC,UAAQA,WAAU,mBAAmB,eAAeA,QAAO,IAAI,IACzD,mBAAmBA,QAAO,IAAI,IAC9B,kBAAkBA,SAAQ,KAAK;AACvC;;;AC7FA,SAAS,WAAWG,KAAIC,KAAI,IAAI;AAC9B,MAAI,IAAI,MAAMD,KAAIC,MAAK,SAAS,EAAE,EAAE,OAAOA,GAAE;AAC7C,SAAO,SAAS,GAAG;AAAE,WAAO,EAAE,IAAI,SAASC,IAAG;AAAE,aAAO,CAAC,GAAGA,EAAC;AAAA,IAAG,CAAC;AAAA,EAAG;AACrE;AAEA,SAAS,WAAWC,KAAIC,KAAI,IAAI;AAC9B,MAAI,IAAI,MAAMD,KAAIC,MAAK,SAAS,EAAE,EAAE,OAAOA,GAAE;AAC7C,SAAO,SAAS,GAAG;AAAE,WAAO,EAAE,IAAI,SAASC,IAAG;AAAE,aAAO,CAACA,IAAG,CAAC;AAAA,IAAG,CAAC;AAAA,EAAG;AACrE;AAEe,SAAR,YAA6B;AAClC,MAAID,KAAID,KAAIG,KAAIC,KACZN,KAAID,KAAIQ,KAAIC,KACZ,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,KAChC,GAAG,GAAG,GAAG,GACT,YAAY;AAEhB,WAASC,aAAY;AACnB,WAAO,EAAC,MAAM,mBAAmB,aAAa,MAAM,EAAC;AAAA,EACvD;AAEA,WAAS,QAAQ;AACf,WAAO,MAAM,KAAKH,MAAK,EAAE,IAAI,IAAID,KAAI,EAAE,EAAE,IAAI,CAAC,EACzC,OAAO,MAAM,KAAKG,MAAK,EAAE,IAAI,IAAID,KAAI,EAAE,EAAE,IAAI,CAAC,CAAC,EAC/C,OAAO,MAAM,KAAKL,MAAK,EAAE,IAAI,IAAIC,KAAI,EAAE,EAAE,OAAO,SAASC,IAAG;AAAE,aAAO,IAAIA,KAAI,EAAE,IAAI;AAAA,IAAS,CAAC,EAAE,IAAI,CAAC,CAAC,EACrG,OAAO,MAAM,KAAKL,MAAK,EAAE,IAAI,IAAIC,KAAI,EAAE,EAAE,OAAO,SAASC,IAAG;AAAE,aAAO,IAAIA,KAAI,EAAE,IAAI;AAAA,IAAS,CAAC,EAAE,IAAI,CAAC,CAAC;AAAA,EAC5G;AAEA,EAAAQ,WAAU,QAAQ,WAAW;AAC3B,WAAO,MAAM,EAAE,IAAI,SAASC,cAAa;AAAE,aAAO,EAAC,MAAM,cAAc,aAAaA,aAAW;AAAA,IAAG,CAAC;AAAA,EACrG;AAEA,EAAAD,WAAU,UAAU,WAAW;AAC7B,WAAO;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,QACX,EAAEH,GAAE,EAAE;AAAA,UACN,EAAEC,GAAE,EAAE,MAAM,CAAC;AAAA,UACb,EAAEF,GAAE,EAAE,QAAQ,EAAE,MAAM,CAAC;AAAA,UACvB,EAAEG,GAAE,EAAE,QAAQ,EAAE,MAAM,CAAC;AAAA,QAAC;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AAEA,EAAAC,WAAU,SAAS,SAAS,GAAG;AAC7B,QAAI,CAAC,UAAU,OAAQ,QAAOA,WAAU,YAAY;AACpD,WAAOA,WAAU,YAAY,CAAC,EAAE,YAAY,CAAC;AAAA,EAC/C;AAEA,EAAAA,WAAU,cAAc,SAAS,GAAG;AAClC,QAAI,CAAC,UAAU,OAAQ,QAAO,CAAC,CAACH,KAAIE,GAAE,GAAG,CAACH,KAAIE,GAAE,CAAC;AACjD,IAAAD,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGD,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AAC3B,IAAAG,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGD,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AAC3B,QAAID,MAAKD,IAAI,KAAIC,KAAIA,MAAKD,KAAIA,MAAK;AACnC,QAAIG,MAAKD,IAAI,KAAIC,KAAIA,MAAKD,KAAIA,MAAK;AACnC,WAAOE,WAAU,UAAU,SAAS;AAAA,EACtC;AAEA,EAAAA,WAAU,cAAc,SAAS,GAAG;AAClC,QAAI,CAAC,UAAU,OAAQ,QAAO,CAAC,CAACP,KAAIH,GAAE,GAAG,CAACI,KAAIH,GAAE,CAAC;AACjD,IAAAE,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AAC3B,IAAAJ,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AAC3B,QAAIE,MAAKC,IAAI,KAAID,KAAIA,MAAKC,KAAIA,MAAK;AACnC,QAAIJ,MAAKC,IAAI,KAAID,KAAIA,MAAKC,KAAIA,MAAK;AACnC,WAAOS,WAAU,UAAU,SAAS;AAAA,EACtC;AAEA,EAAAA,WAAU,OAAO,SAAS,GAAG;AAC3B,QAAI,CAAC,UAAU,OAAQ,QAAOA,WAAU,UAAU;AAClD,WAAOA,WAAU,UAAU,CAAC,EAAE,UAAU,CAAC;AAAA,EAC3C;AAEA,EAAAA,WAAU,YAAY,SAAS,GAAG;AAChC,QAAI,CAAC,UAAU,OAAQ,QAAO,CAAC,IAAI,EAAE;AACrC,SAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC;AACrB,WAAOA;AAAA,EACT;AAEA,EAAAA,WAAU,YAAY,SAAS,GAAG;AAChC,QAAI,CAAC,UAAU,OAAQ,QAAO,CAAC,IAAI,EAAE;AACrC,SAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC;AACrB,WAAOA;AAAA,EACT;AAEA,EAAAA,WAAU,YAAY,SAAS,GAAG;AAChC,QAAI,CAAC,UAAU,OAAQ,QAAO;AAC9B,gBAAY,CAAC;AACb,QAAI,WAAWV,KAAIC,KAAI,EAAE;AACzB,QAAI,WAAWE,KAAIC,KAAI,SAAS;AAChC,QAAI,WAAWK,KAAID,KAAI,EAAE;AACzB,QAAI,WAAWD,KAAID,KAAI,SAAS;AAChC,WAAOI;AAAA,EACT;AAEA,SAAOA,WACF,YAAY,CAAC,CAAC,MAAM,MAAM,OAAO,GAAG,CAAC,KAAK,KAAK,OAAO,CAAC,CAAC,EACxD,YAAY,CAAC,CAAC,MAAM,MAAM,OAAO,GAAG,CAAC,KAAK,KAAK,OAAO,CAAC,CAAC;AAC/D;AAEO,SAAS,cAAc;AAC5B,SAAO,UAAU,EAAE;AACrB;;;ACtGe,SAAR,oBAAiB,GAAG,GAAG;AAC5B,MAAIE,MAAK,EAAE,CAAC,IAAI,SACZC,MAAK,EAAE,CAAC,IAAI,SACZC,MAAK,EAAE,CAAC,IAAI,SACZC,MAAK,EAAE,CAAC,IAAI,SACZ,MAAM,IAAIF,GAAE,GACZ,MAAM,IAAIA,GAAE,GACZ,MAAM,IAAIE,GAAE,GACZ,MAAM,IAAIA,GAAE,GACZ,MAAM,MAAM,IAAIH,GAAE,GAClB,MAAM,MAAM,IAAIA,GAAE,GAClB,MAAM,MAAM,IAAIE,GAAE,GAClB,MAAM,MAAM,IAAIA,GAAE,GAClB,IAAI,IAAI,KAAK,KAAK,SAASC,MAAKF,GAAE,IAAI,MAAM,MAAM,SAASC,MAAKF,GAAE,CAAC,CAAC,GACpE,IAAI,IAAI,CAAC;AAEb,MAAI,cAAc,IAAI,SAAS,GAAG;AAChC,QAAII,KAAI,IAAI,KAAK,CAAC,IAAI,GAClB,IAAI,IAAI,IAAI,CAAC,IAAI,GACjB,IAAI,IAAI,MAAMA,KAAI,KAClB,IAAI,IAAI,MAAMA,KAAI,KAClB,IAAI,IAAI,MAAMA,KAAI;AACtB,WAAO;AAAA,MACL,MAAM,GAAG,CAAC,IAAI;AAAA,MACd,MAAM,GAAG,KAAK,IAAI,IAAI,IAAI,CAAC,CAAC,IAAI;AAAA,IAClC;AAAA,EACF,IAAI,WAAW;AACb,WAAO,CAACJ,MAAK,SAASC,MAAK,OAAO;AAAA,EACpC;AAEA,cAAY,WAAW;AAEvB,SAAO;AACT;;;ACnCA,IAAO,mBAAQ,OAAK;;;ACIpB,IAAII,WAAU,IAAI,MAAM;AAAxB,IACIC,eAAc,IAAI,MAAM;AAD5B,IAEI;AAFJ,IAGI;AAHJ,IAIIC;AAJJ,IAKIC;AAEJ,IAAIC,cAAa;AAAA,EACf,OAAO;AAAA,EACP,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc,WAAW;AACvB,IAAAA,YAAW,YAAYC;AACvB,IAAAD,YAAW,UAAUE;AAAA,EACvB;AAAA,EACA,YAAY,WAAW;AACrB,IAAAF,YAAW,YAAYA,YAAW,UAAUA,YAAW,QAAQ;AAC/D,IAAAJ,SAAQ,IAAI,IAAIC,YAAW,CAAC;AAC5B,IAAAA,eAAc,IAAI,MAAM;AAAA,EAC1B;AAAA,EACA,QAAQ,WAAW;AACjB,QAAI,OAAOD,WAAU;AACrB,IAAAA,WAAU,IAAI,MAAM;AACpB,WAAO;AAAA,EACT;AACF;AAEA,SAASK,iBAAgB;AACvB,EAAAD,YAAW,QAAQG;AACrB;AAEA,SAASA,gBAAe,GAAG,GAAG;AAC5B,EAAAH,YAAW,QAAQI;AACnB,QAAMN,MAAK,GAAG,MAAMC,MAAK;AAC3B;AAEA,SAASK,WAAU,GAAG,GAAG;AACvB,EAAAP,aAAY,IAAIE,MAAK,IAAID,MAAK,CAAC;AAC/B,EAAAA,MAAK,GAAGC,MAAK;AACf;AAEA,SAASG,eAAc;AACrB,EAAAE,WAAU,KAAK,GAAG;AACpB;AAEA,IAAOC,gBAAQL;;;AC/Cf,IAAIM,MAAK;AAAT,IACIC,MAAKD;AADT,IAEI,KAAK,CAACA;AAFV,IAGI,KAAK;AAET,IAAIE,gBAAe;AAAA,EACjB,OAAOC;AAAA,EACP,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,QAAQ,WAAW;AACjB,QAAI,SAAS,CAAC,CAACH,KAAIC,GAAE,GAAG,CAAC,IAAI,EAAE,CAAC;AAChC,SAAK,KAAK,EAAEA,MAAKD,MAAK;AACtB,WAAO;AAAA,EACT;AACF;AAEA,SAASG,aAAY,GAAG,GAAG;AACzB,MAAI,IAAIH,IAAI,CAAAA,MAAK;AACjB,MAAI,IAAI,GAAI,MAAK;AACjB,MAAI,IAAIC,IAAI,CAAAA,MAAK;AACjB,MAAI,IAAI,GAAI,MAAK;AACnB;AAEA,IAAOG,kBAAQF;;;ACvBf,IAAIG,MAAK;AAAT,IACIC,MAAK;AADT,IAEIC,MAAK;AAFT,IAGIC,MAAK;AAHT,IAIIC,MAAK;AAJT,IAKIC,MAAK;AALT,IAMIC,MAAK;AANT,IAOIC,MAAK;AAPT,IAQIC,MAAK;AART,IASIC;AATJ,IAUIC;AAVJ,IAWIC;AAXJ,IAYIC;AAEJ,IAAIC,kBAAiB;AAAA,EACnB,OAAOC;AAAA,EACP,WAAWC;AAAA,EACX,SAASC;AAAA,EACT,cAAc,WAAW;AACvB,IAAAH,gBAAe,YAAYI;AAC3B,IAAAJ,gBAAe,UAAUK;AAAA,EAC3B;AAAA,EACA,YAAY,WAAW;AACrB,IAAAL,gBAAe,QAAQC;AACvB,IAAAD,gBAAe,YAAYE;AAC3B,IAAAF,gBAAe,UAAUG;AAAA,EAC3B;AAAA,EACA,QAAQ,WAAW;AACjB,QAAI,WAAWR,MAAK,CAACF,MAAKE,KAAID,MAAKC,GAAE,IAC/BH,MAAK,CAACF,MAAKE,KAAID,MAAKC,GAAE,IACtBH,MAAK,CAACF,MAAKE,KAAID,MAAKC,GAAE,IACtB,CAAC,KAAK,GAAG;AACf,IAAAF,MAAKC,MAAKC,MACVC,MAAKC,MAAKC,MACVC,MAAKC,MAAKC,MAAK;AACf,WAAO;AAAA,EACT;AACF;AAEA,SAASM,eAAc,GAAG,GAAG;AAC3B,EAAAd,OAAM;AACN,EAAAC,OAAM;AACN,IAAEC;AACJ;AAEA,SAASa,qBAAoB;AAC3B,EAAAF,gBAAe,QAAQ;AACzB;AAEA,SAAS,uBAAuB,GAAG,GAAG;AACpC,EAAAA,gBAAe,QAAQ;AACvB,EAAAC,eAAcH,MAAK,GAAGC,MAAK,CAAC;AAC9B;AAEA,SAAS,kBAAkB,GAAG,GAAG;AAC/B,MAAI,KAAK,IAAID,KAAI,KAAK,IAAIC,KAAI,IAAI,KAAK,KAAK,KAAK,KAAK,EAAE;AACxD,EAAAT,OAAM,KAAKQ,MAAK,KAAK;AACrB,EAAAP,OAAM,KAAKQ,MAAK,KAAK;AACrB,EAAAP,OAAM;AACN,EAAAS,eAAcH,MAAK,GAAGC,MAAK,CAAC;AAC9B;AAEA,SAASI,mBAAkB;AACzB,EAAAH,gBAAe,QAAQC;AACzB;AAEA,SAASG,qBAAoB;AAC3B,EAAAJ,gBAAe,QAAQ;AACzB;AAEA,SAASK,mBAAkB;AACzB,oBAAkBT,MAAKC,IAAG;AAC5B;AAEA,SAAS,uBAAuB,GAAG,GAAG;AACpC,EAAAG,gBAAe,QAAQ;AACvB,EAAAC,eAAcL,OAAME,MAAK,GAAGD,OAAME,MAAK,CAAC;AAC1C;AAEA,SAAS,kBAAkB,GAAG,GAAG;AAC/B,MAAI,KAAK,IAAID,KACT,KAAK,IAAIC,KACT,IAAI,KAAK,KAAK,KAAK,KAAK,EAAE;AAE9B,EAAAT,OAAM,KAAKQ,MAAK,KAAK;AACrB,EAAAP,OAAM,KAAKQ,MAAK,KAAK;AACrB,EAAAP,OAAM;AAEN,MAAIO,MAAK,IAAID,MAAK;AAClB,EAAAL,OAAM,KAAKK,MAAK;AAChB,EAAAJ,OAAM,KAAKK,MAAK;AAChB,EAAAJ,OAAM,IAAI;AACV,EAAAM,eAAcH,MAAK,GAAGC,MAAK,CAAC;AAC9B;AAEA,IAAOO,oBAAQN;;;AChGA,SAAR,YAA6B,SAAS;AAC3C,OAAK,WAAW;AAClB;AAEA,YAAY,YAAY;AAAA,EACtB,SAAS;AAAA,EACT,aAAa,SAAS,GAAG;AACvB,WAAO,KAAK,UAAU,GAAG;AAAA,EAC3B;AAAA,EACA,cAAc,WAAW;AACvB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,YAAY,WAAW;AACrB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,QAAI,KAAK,UAAU,EAAG,MAAK,SAAS,UAAU;AAC9C,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,OAAO,SAAS,GAAG,GAAG;AACpB,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK,GAAG;AACN,aAAK,SAAS,OAAO,GAAG,CAAC;AACzB,aAAK,SAAS;AACd;AAAA,MACF;AAAA,MACA,KAAK,GAAG;AACN,aAAK,SAAS,OAAO,GAAG,CAAC;AACzB;AAAA,MACF;AAAA,MACA,SAAS;AACP,aAAK,SAAS,OAAO,IAAI,KAAK,SAAS,CAAC;AACxC,aAAK,SAAS,IAAI,GAAG,GAAG,KAAK,SAAS,GAAG,GAAG;AAC5C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ;AACV;;;ACxCA,IAAIO,aAAY,IAAI,MAAM;AAA1B,IACI;AADJ,IAEIC;AAFJ,IAGIC;AAHJ,IAIIC;AAJJ,IAKIC;AAEJ,IAAIC,gBAAe;AAAA,EACjB,OAAO;AAAA,EACP,WAAW,WAAW;AACpB,IAAAA,cAAa,QAAQC;AAAA,EACvB;AAAA,EACA,SAAS,WAAW;AAClB,QAAI,WAAY,CAAAC,aAAYN,MAAKC,IAAG;AACpC,IAAAG,cAAa,QAAQ;AAAA,EACvB;AAAA,EACA,cAAc,WAAW;AACvB,iBAAa;AAAA,EACf;AAAA,EACA,YAAY,WAAW;AACrB,iBAAa;AAAA,EACf;AAAA,EACA,QAAQ,WAAW;AACjB,QAAI,SAAS,CAACL;AACd,IAAAA,aAAY,IAAI,MAAM;AACtB,WAAO;AAAA,EACT;AACF;AAEA,SAASM,kBAAiB,GAAG,GAAG;AAC9B,EAAAD,cAAa,QAAQE;AACrB,EAAAN,OAAME,MAAK,GAAGD,OAAME,MAAK;AAC3B;AAEA,SAASG,aAAY,GAAG,GAAG;AACzB,EAAAJ,OAAM,GAAGC,OAAM;AACf,EAAAJ,WAAU,IAAI,KAAKG,MAAKA,MAAKC,MAAKA,GAAE,CAAC;AACrC,EAAAD,MAAK,GAAGC,MAAK;AACf;AAEA,IAAO,kBAAQC;;;AC3Cf,IAAI;AAAJ,IAAiB;AAAjB,IAA8B;AAA9B,IAA2C;AAE3C,IAAqB,aAArB,MAAgC;AAAA,EAC9B,YAAY,QAAQ;AAClB,SAAK,UAAU,UAAU,OAAO,SAAS,YAAY,MAAM;AAC3D,SAAK,UAAU;AACf,SAAK,IAAI;AAAA,EACX;AAAA,EACA,YAAY,GAAG;AACb,SAAK,UAAU,CAAC;AAChB,WAAO;AAAA,EACT;AAAA,EACA,eAAe;AACb,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,aAAa;AACX,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,YAAY;AACV,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,UAAU;AACR,QAAI,KAAK,UAAU,EAAG,MAAK,KAAK;AAChC,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,MAAM,GAAG,GAAG;AACV,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK,GAAG;AACN,aAAK,WAAW,CAAC,IAAI,CAAC;AACtB,aAAK,SAAS;AACd;AAAA,MACF;AAAA,MACA,KAAK,GAAG;AACN,aAAK,WAAW,CAAC,IAAI,CAAC;AACtB;AAAA,MACF;AAAA,MACA,SAAS;AACP,aAAK,WAAW,CAAC,IAAI,CAAC;AACtB,YAAI,KAAK,YAAY,eAAe,KAAK,YAAY,aAAa;AAChE,gBAAM,IAAI,KAAK;AACf,gBAAM,IAAI,KAAK;AACf,eAAK,IAAI;AACT,eAAK,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC;AAC1E,wBAAc;AACd,wBAAc,KAAK;AACnB,wBAAc,KAAK;AACnB,eAAK,IAAI;AAAA,QACX;AACA,aAAK,KAAK;AACV;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS;AACP,UAAM,SAAS,KAAK;AACpB,SAAK,IAAI;AACT,WAAO,OAAO,SAAS,SAAS;AAAA,EAClC;AACF;AAEA,SAAS,OAAO,SAAS;AACvB,MAAI,IAAI;AACR,OAAK,KAAK,QAAQ,CAAC;AACnB,aAAW,IAAI,QAAQ,QAAQ,IAAI,GAAG,EAAE,GAAG;AACzC,SAAK,KAAK,UAAU,CAAC,IAAI,QAAQ,CAAC;AAAA,EACpC;AACF;AAEA,SAAS,YAAY,QAAQ;AAC3B,QAAM,IAAI,KAAK,MAAM,MAAM;AAC3B,MAAI,EAAE,KAAK,GAAI,OAAM,IAAI,WAAW,mBAAmB,MAAM,EAAE;AAC/D,MAAI,IAAI,GAAI,QAAO;AACnB,MAAI,MAAM,aAAa;AACrB,UAAM,IAAI,MAAM;AAChB,kBAAc;AACd,kBAAc,SAASG,QAAO,SAAS;AACrC,UAAI,IAAI;AACR,WAAK,KAAK,QAAQ,CAAC;AACnB,iBAAW,IAAI,QAAQ,QAAQ,IAAI,GAAG,EAAE,GAAG;AACzC,aAAK,KAAK,KAAK,MAAM,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,QAAQ,CAAC;AAAA,MACxD;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;;;AC5Ee,SAAR,aAAiBC,aAAY,SAAS;AAC3C,MAAI,SAAS,GACT,cAAc,KACd,kBACA;AAEJ,WAAS,KAAKC,SAAQ;AACpB,QAAIA,SAAQ;AACV,UAAI,OAAO,gBAAgB,WAAY,eAAc,YAAY,CAAC,YAAY,MAAM,MAAM,SAAS,CAAC;AACpG,qBAAOA,SAAQ,iBAAiB,aAAa,CAAC;AAAA,IAChD;AACA,WAAO,cAAc,OAAO;AAAA,EAC9B;AAEA,OAAK,OAAO,SAASA,SAAQ;AAC3B,mBAAOA,SAAQ,iBAAiBC,aAAQ,CAAC;AACzC,WAAOA,cAAS,OAAO;AAAA,EACzB;AAEA,OAAK,UAAU,SAASD,SAAQ;AAC9B,mBAAOA,SAAQ,iBAAiB,eAAW,CAAC;AAC5C,WAAO,gBAAY,OAAO;AAAA,EAC5B;AAEA,OAAK,SAAS,SAASA,SAAQ;AAC7B,mBAAOA,SAAQ,iBAAiBE,eAAU,CAAC;AAC3C,WAAOA,gBAAW,OAAO;AAAA,EAC3B;AAEA,OAAK,WAAW,SAASF,SAAQ;AAC/B,mBAAOA,SAAQ,iBAAiBG,iBAAY,CAAC;AAC7C,WAAOA,kBAAa,OAAO;AAAA,EAC7B;AAEA,OAAK,aAAa,SAAS,GAAG;AAC5B,QAAI,CAAC,UAAU,OAAQ,QAAOJ;AAC9B,uBAAmB,KAAK,QAAQA,cAAa,MAAM,qBAAaA,cAAa,GAAG;AAChF,WAAO;AAAA,EACT;AAEA,OAAK,UAAU,SAAS,GAAG;AACzB,QAAI,CAAC,UAAU,OAAQ,QAAO;AAC9B,oBAAgB,KAAK,QAAQ,UAAU,MAAM,IAAI,WAAW,MAAM,KAAK,IAAI,YAAY,UAAU,CAAC;AAClG,QAAI,OAAO,gBAAgB,WAAY,eAAc,YAAY,WAAW;AAC5E,WAAO;AAAA,EACT;AAEA,OAAK,cAAc,SAAS,GAAG;AAC7B,QAAI,CAAC,UAAU,OAAQ,QAAO;AAC9B,kBAAc,OAAO,MAAM,aAAa,KAAK,cAAc,YAAY,CAAC,CAAC,GAAG,CAAC;AAC7E,WAAO;AAAA,EACT;AAEA,OAAK,SAAS,SAAS,GAAG;AACxB,QAAI,CAAC,UAAU,OAAQ,QAAO;AAC9B,QAAI,KAAK,KAAM,UAAS;AAAA,SACnB;AACH,YAAM,IAAI,KAAK,MAAM,CAAC;AACtB,UAAI,EAAE,KAAK,GAAI,OAAM,IAAI,WAAW,mBAAmB,CAAC,EAAE;AAC1D,eAAS;AAAA,IACX;AACA,QAAI,YAAY,KAAM,iBAAgB,IAAI,WAAW,MAAM;AAC3D,WAAO;AAAA,EACT;AAEA,SAAO,KAAK,WAAWA,WAAU,EAAE,OAAO,MAAM,EAAE,QAAQ,OAAO;AACnE;;;AC3Ee,SAAR,kBAAiB,SAAS;AAC/B,SAAO;AAAA,IACL,QAAQ,YAAY,OAAO;AAAA,EAC7B;AACF;AAEO,SAAS,YAAY,SAAS;AACnC,SAAO,SAAS,QAAQ;AACtB,QAAI,IAAI,IAAI;AACZ,aAAS,OAAO,QAAS,GAAE,GAAG,IAAI,QAAQ,GAAG;AAC7C,MAAE,SAAS;AACX,WAAO;AAAA,EACT;AACF;AAEA,SAAS,kBAAkB;AAAC;AAE5B,gBAAgB,YAAY;AAAA,EAC1B,aAAa;AAAA,EACb,OAAO,SAAS,GAAG,GAAG;AAAE,SAAK,OAAO,MAAM,GAAG,CAAC;AAAA,EAAG;AAAA,EACjD,QAAQ,WAAW;AAAE,SAAK,OAAO,OAAO;AAAA,EAAG;AAAA,EAC3C,WAAW,WAAW;AAAE,SAAK,OAAO,UAAU;AAAA,EAAG;AAAA,EACjD,SAAS,WAAW;AAAE,SAAK,OAAO,QAAQ;AAAA,EAAG;AAAA,EAC7C,cAAc,WAAW;AAAE,SAAK,OAAO,aAAa;AAAA,EAAG;AAAA,EACvD,YAAY,WAAW;AAAE,SAAK,OAAO,WAAW;AAAA,EAAG;AACrD;;;ACtBA,SAAS,IAAIK,aAAY,WAAWC,SAAQ;AAC1C,MAAI,OAAOD,YAAW,cAAcA,YAAW,WAAW;AAC1D,EAAAA,YAAW,MAAM,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;AACtC,MAAI,QAAQ,KAAM,CAAAA,YAAW,WAAW,IAAI;AAC5C,iBAAUC,SAAQD,YAAW,OAAOE,eAAY,CAAC;AACjD,YAAUA,gBAAa,OAAO,CAAC;AAC/B,MAAI,QAAQ,KAAM,CAAAF,YAAW,WAAW,IAAI;AAC5C,SAAOA;AACT;AAEO,SAAS,UAAUA,aAAY,QAAQC,SAAQ;AACpD,SAAO,IAAID,aAAY,SAAS,GAAG;AACjC,QAAI,IAAI,OAAO,CAAC,EAAE,CAAC,IAAI,OAAO,CAAC,EAAE,CAAC,GAC9B,IAAI,OAAO,CAAC,EAAE,CAAC,IAAI,OAAO,CAAC,EAAE,CAAC,GAC9B,IAAI,KAAK,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,GAC7D,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,MAAM,GACpD,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,MAAM;AACxD,IAAAA,YAAW,MAAM,MAAM,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;AAAA,EAC5C,GAAGC,OAAM;AACX;AAEO,SAAS,QAAQD,aAAY,MAAMC,SAAQ;AAChD,SAAO,UAAUD,aAAY,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,GAAGC,OAAM;AACrD;AAEO,SAAS,SAASD,aAAY,OAAOC,SAAQ;AAClD,SAAO,IAAID,aAAY,SAAS,GAAG;AACjC,QAAI,IAAI,CAAC,OACL,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IACzB,KAAK,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,MAAM,GACpC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AACnB,IAAAA,YAAW,MAAM,MAAM,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;AAAA,EAC5C,GAAGC,OAAM;AACX;AAEO,SAAS,UAAUD,aAAY,QAAQC,SAAQ;AACpD,SAAO,IAAID,aAAY,SAAS,GAAG;AACjC,QAAI,IAAI,CAAC,QACL,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IACzB,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,GACf,KAAK,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,MAAM;AACxC,IAAAA,YAAW,MAAM,MAAM,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;AAAA,EAC5C,GAAGC,OAAM;AACX;;;AC1CA,IAAI,WAAW;AAAf,IACI,iBAAiB,IAAI,KAAK,OAAO;AAEtB,SAAR,iBAAiB,SAAS,QAAQ;AACvC,SAAO,CAAC,SAAS,SAAS,SAAS,MAAM,IAAI,aAAa,OAAO;AACnE;AAEA,SAAS,aAAa,SAAS;AAC7B,SAAO,YAAY;AAAA,IACjB,OAAO,SAAS,GAAG,GAAG;AACpB,UAAI,QAAQ,GAAG,CAAC;AAChB,WAAK,OAAO,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,IAC9B;AAAA,EACF,CAAC;AACH;AAEA,SAAS,SAAS,SAAS,QAAQ;AAEjC,WAAS,eAAeE,KAAIC,KAAIC,UAAS,IAAI,IAAI,IAAIC,KAAIC,KAAIC,UAAS,IAAI,IAAI,IAAI,OAAO,QAAQ;AAC/F,QAAI,KAAKF,MAAKH,KACV,KAAKI,MAAKH,KACV,KAAK,KAAK,KAAK,KAAK;AACxB,QAAI,KAAK,IAAI,UAAU,SAAS;AAC9B,UAAI,IAAI,KAAK,IACT,IAAI,KAAK,IACTK,KAAI,KAAK,IACT,IAAI,KAAK,IAAI,IAAI,IAAI,IAAIA,KAAIA,EAAC,GAC9B,OAAO,KAAKA,MAAK,CAAC,GAClBC,WAAU,IAAI,IAAID,EAAC,IAAI,CAAC,IAAI,WAAW,IAAIJ,WAAUG,QAAO,IAAI,WAAWH,WAAUG,YAAW,IAAI,MAAM,GAAG,CAAC,GAC9G,IAAI,QAAQE,UAAS,IAAI,GACzB,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,MAAM,KAAKP,KACX,MAAM,KAAKC,KACX,KAAK,KAAK,MAAM,KAAK;AACzB,UAAI,KAAK,KAAK,KAAK,UACZ,KAAK,KAAK,MAAM,KAAK,OAAO,KAAK,GAAG,IAAI,OACxC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,gBAAgB;AACnD,uBAAeD,KAAIC,KAAIC,UAAS,IAAI,IAAI,IAAI,IAAI,IAAIK,UAAS,KAAK,GAAG,KAAK,GAAGD,IAAG,OAAO,MAAM;AAC7F,eAAO,MAAM,IAAI,EAAE;AACnB,uBAAe,IAAI,IAAIC,UAAS,GAAG,GAAGD,IAAGH,KAAIC,KAAIC,UAAS,IAAI,IAAI,IAAI,OAAO,MAAM;AAAA,MACrF;AAAA,IACF;AAAA,EACF;AACA,SAAO,SAAS,QAAQ;AACtB,QAAIG,WAAUC,MAAKC,MAAK,KAAK,KAAK,KAC9BR,UAASF,KAAIC,KAAI,IAAI,IAAI;AAE7B,QAAI,iBAAiB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc,WAAW;AAAE,eAAO,aAAa;AAAG,uBAAe,YAAY;AAAA,MAAW;AAAA,MACxF,YAAY,WAAW;AAAE,eAAO,WAAW;AAAG,uBAAe,YAAY;AAAA,MAAW;AAAA,IACtF;AAEA,aAAS,MAAM,GAAG,GAAG;AACnB,UAAI,QAAQ,GAAG,CAAC;AAChB,aAAO,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,IACzB;AAEA,aAAS,YAAY;AACnB,MAAAD,MAAK;AACL,qBAAe,QAAQW;AACvB,aAAO,UAAU;AAAA,IACnB;AAEA,aAASA,WAAU,QAAQ,KAAK;AAC9B,UAAIL,KAAI,UAAU,CAAC,QAAQ,GAAG,CAAC,GAAG,IAAI,QAAQ,QAAQ,GAAG;AACzD,qBAAeN,KAAIC,KAAIC,UAAS,IAAI,IAAI,IAAIF,MAAK,EAAE,CAAC,GAAGC,MAAK,EAAE,CAAC,GAAGC,WAAU,QAAQ,KAAKI,GAAE,CAAC,GAAG,KAAKA,GAAE,CAAC,GAAG,KAAKA,GAAE,CAAC,GAAG,UAAU,MAAM;AACrI,aAAO,MAAMN,KAAIC,GAAE;AAAA,IACrB;AAEA,aAAS,UAAU;AACjB,qBAAe,QAAQ;AACvB,aAAO,QAAQ;AAAA,IACjB;AAEA,aAAS,YAAY;AACnB,gBAAU;AACV,qBAAe,QAAQ;AACvB,qBAAe,UAAU;AAAA,IAC3B;AAEA,aAAS,UAAU,QAAQ,KAAK;AAC9B,MAAAU,WAAUH,YAAW,QAAQ,GAAG,GAAGC,OAAMT,KAAIU,OAAMT,KAAI,MAAM,IAAI,MAAM,IAAI,MAAM;AACjF,qBAAe,QAAQU;AAAA,IACzB;AAEA,aAAS,UAAU;AACjB,qBAAeX,KAAIC,KAAIC,UAAS,IAAI,IAAI,IAAIO,MAAKC,MAAKF,WAAU,KAAK,KAAK,KAAK,UAAU,MAAM;AAC/F,qBAAe,UAAU;AACzB,cAAQ;AAAA,IACV;AAEA,WAAO;AAAA,EACT;AACF;;;AC1FA,IAAI,mBAAmB,YAAY;AAAA,EACjC,OAAO,SAAS,GAAG,GAAG;AACpB,SAAK,OAAO,MAAM,IAAI,SAAS,IAAI,OAAO;AAAA,EAC5C;AACF,CAAC;AAED,SAAS,gBAAgB,QAAQ;AAC/B,SAAO,YAAY;AAAA,IACjB,OAAO,SAAS,GAAG,GAAG;AACpB,UAAI,IAAI,OAAO,GAAG,CAAC;AACnB,aAAO,KAAK,OAAO,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,IACrC;AAAA,EACF,CAAC;AACH;AAEA,SAAS,eAAe,GAAG,IAAI,IAAI,IAAI,IAAI;AACzC,WAAS,UAAU,GAAG,GAAG;AACvB,SAAK;AAAI,SAAK;AACd,WAAO,CAAC,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC;AAAA,EAChC;AACA,YAAU,SAAS,SAAS,GAAG,GAAG;AAChC,WAAO,EAAE,IAAI,MAAM,IAAI,KAAK,KAAK,KAAK,IAAI,EAAE;AAAA,EAC9C;AACA,SAAO;AACT;AAEA,SAAS,qBAAqB,GAAG,IAAI,IAAI,IAAI,IAAI,OAAO;AACtD,MAAI,CAAC,MAAO,QAAO,eAAe,GAAG,IAAI,IAAI,IAAI,EAAE;AACnD,MAAI,WAAW,IAAI,KAAK,GACpB,WAAW,IAAI,KAAK,GACpB,IAAI,WAAW,GACf,IAAI,WAAW,GACf,KAAK,WAAW,GAChB,KAAK,WAAW,GAChB,MAAM,WAAW,KAAK,WAAW,MAAM,GACvC,MAAM,WAAW,KAAK,WAAW,MAAM;AAC3C,WAAS,UAAU,GAAG,GAAG;AACvB,SAAK;AAAI,SAAK;AACd,WAAO,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC;AAAA,EAChD;AACA,YAAU,SAAS,SAAS,GAAG,GAAG;AAChC,WAAO,CAAC,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,MAAM,KAAK,KAAK,IAAI,KAAK,EAAE;AAAA,EAClE;AACA,SAAO;AACT;AAEe,SAAR,WAA4B,SAAS;AAC1C,SAAO,kBAAkB,WAAW;AAAE,WAAO;AAAA,EAAS,CAAC,EAAE;AAC3D;AAEO,SAAS,kBAAkB,WAAW;AAC3C,MAAI,SACA,IAAI,KACJ,IAAI,KAAK,IAAI,KACb,SAAS,GAAG,MAAM,GAClB,cAAc,GAAG,WAAW,GAAG,aAAa,GAAG,QAC/C,QAAQ,GACR,KAAK,GACL,KAAK,GACL,QAAQ,MAAM,UAAU,sBACxBI,MAAK,MAAMC,KAAIC,KAAIC,KAAI,WAAW,kBAClC,SAAS,KACT,iBACA,kBACA,wBACA,OACA;AAEJ,WAASC,YAAW,OAAO;AACzB,WAAO,uBAAuB,MAAM,CAAC,IAAI,SAAS,MAAM,CAAC,IAAI,OAAO;AAAA,EACtE;AAEA,WAAS,OAAO,OAAO;AACrB,YAAQ,uBAAuB,OAAO,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AACxD,WAAO,SAAS,CAAC,MAAM,CAAC,IAAI,SAAS,MAAM,CAAC,IAAI,OAAO;AAAA,EACzD;AAEA,EAAAA,YAAW,SAAS,SAAS,QAAQ;AACnC,WAAO,SAAS,gBAAgB,SAAS,QAAQ,QAAQ,iBAAiB,gBAAgB,MAAM,EAAE,QAAQ,gBAAgB,SAAS,cAAc,MAAM,CAAC,CAAC,CAAC,CAAC;AAAA,EAC7J;AAEA,EAAAA,YAAW,UAAU,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,UAAU,GAAG,QAAQ,QAAW,MAAM,KAAK;AAAA,EACxE;AAEA,EAAAA,YAAW,WAAW,SAAS,GAAG;AAChC,WAAO,UAAU,UAAU,WAAW,GAAGJ,MAAKC,MAAKC,MAAKC,MAAK,MAAM,MAAM,KAAK;AAAA,EAChF;AAEA,EAAAC,YAAW,YAAY,SAAS,GAAG;AACjC,WAAO,UAAU,UAAU,UAAU,CAAC,IAAIC,gBAAW,QAAQ,IAAI,OAAO,KAAK,QAAQ,MAAM,uBAAmB,MAAM,KAAK,QAAQ;AAAA,EACnI;AAEA,EAAAD,YAAW,aAAa,SAAS,GAAG;AAClC,WAAO,UAAU,UAAU,WAAW,KAAK,QAAQJ,MAAKC,MAAKC,MAAKC,MAAK,MAAM,oBAAY,cAAcH,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,KAAKH,OAAM,OAAO,OAAO,CAAC,CAACA,KAAIC,GAAE,GAAG,CAACC,KAAIC,GAAE,CAAC;AAAA,EACxN;AAEA,EAAAC,YAAW,QAAQ,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,IAAI,CAAC,GAAG,SAAS,KAAK;AAAA,EACnD;AAEA,EAAAA,YAAW,YAAY,SAAS,GAAG;AACjC,WAAO,UAAU,UAAU,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,CAAC;AAAA,EACtE;AAEA,EAAAA,YAAW,SAAS,SAAS,GAAG;AAC9B,WAAO,UAAU,UAAU,SAAS,EAAE,CAAC,IAAI,MAAM,SAAS,MAAM,EAAE,CAAC,IAAI,MAAM,SAAS,SAAS,KAAK,CAAC,SAAS,SAAS,MAAM,OAAO;AAAA,EACtI;AAEA,EAAAA,YAAW,SAAS,SAAS,GAAG;AAC9B,WAAO,UAAU,UAAU,cAAc,EAAE,CAAC,IAAI,MAAM,SAAS,WAAW,EAAE,CAAC,IAAI,MAAM,SAAS,aAAa,EAAE,SAAS,IAAI,EAAE,CAAC,IAAI,MAAM,UAAU,GAAG,SAAS,KAAK,CAAC,cAAc,SAAS,WAAW,SAAS,aAAa,OAAO;AAAA,EACtO;AAEA,EAAAA,YAAW,QAAQ,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,QAAQ,IAAI,MAAM,SAAS,SAAS,KAAK,QAAQ;AAAA,EAC9E;AAEA,EAAAA,YAAW,WAAW,SAAS,GAAG;AAChC,WAAO,UAAU,UAAU,KAAK,IAAI,KAAK,GAAG,SAAS,KAAK,KAAK;AAAA,EACjE;AAEA,EAAAA,YAAW,WAAW,SAAS,GAAG;AAChC,WAAO,UAAU,UAAU,KAAK,IAAI,KAAK,GAAG,SAAS,KAAK,KAAK;AAAA,EACjE;AAEA,EAAAA,YAAW,YAAY,SAAS,GAAG;AACjC,WAAO,UAAU,UAAU,kBAAkB,iBAAS,kBAAkB,SAAS,IAAI,CAAC,GAAG,MAAM,KAAK,KAAK,MAAM;AAAA,EACjH;AAEA,EAAAA,YAAW,YAAY,SAAS,QAAQE,SAAQ;AAC9C,WAAO,UAAUF,aAAY,QAAQE,OAAM;AAAA,EAC7C;AAEA,EAAAF,YAAW,UAAU,SAAS,MAAME,SAAQ;AAC1C,WAAO,QAAQF,aAAY,MAAME,OAAM;AAAA,EACzC;AAEA,EAAAF,YAAW,WAAW,SAAS,OAAOE,SAAQ;AAC5C,WAAO,SAASF,aAAY,OAAOE,OAAM;AAAA,EAC3C;AAEA,EAAAF,YAAW,YAAY,SAAS,QAAQE,SAAQ;AAC9C,WAAO,UAAUF,aAAY,QAAQE,OAAM;AAAA,EAC7C;AAEA,WAAS,WAAW;AAClB,QAAI,SAAS,qBAAqB,GAAG,GAAG,GAAG,IAAI,IAAI,KAAK,EAAE,MAAM,MAAM,QAAQ,QAAQ,GAAG,CAAC,GACtF,YAAY,qBAAqB,GAAG,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,KAAK;AACnF,aAAS,cAAc,aAAa,UAAU,UAAU;AACxD,uBAAmB,gBAAQ,SAAS,SAAS;AAC7C,6BAAyB,gBAAQ,QAAQ,gBAAgB;AACzD,sBAAkB,iBAAS,kBAAkB,MAAM;AACnD,WAAO,MAAM;AAAA,EACf;AAEA,WAAS,QAAQ;AACf,YAAQ,cAAc;AACtB,WAAOF;AAAA,EACT;AAEA,SAAO,WAAW;AAChB,cAAU,UAAU,MAAM,MAAM,SAAS;AACzC,IAAAA,YAAW,SAAS,QAAQ,UAAU;AACtC,WAAO,SAAS;AAAA,EAClB;AACF;;;AC7KO,SAAS,gBAAgB,WAAW;AACzC,MAAIG,QAAO,GACPC,QAAO,KAAK,GACZ,IAAI,kBAAkB,SAAS,GAC/B,IAAI,EAAED,OAAMC,KAAI;AAEpB,IAAE,YAAY,SAAS,GAAG;AACxB,WAAO,UAAU,SAAS,EAAED,QAAO,EAAE,CAAC,IAAI,SAASC,QAAO,EAAE,CAAC,IAAI,OAAO,IAAI,CAACD,QAAO,SAASC,QAAO,OAAO;AAAA,EAC7G;AAEA,SAAO;AACT;;;ACZO,SAAS,wBAAwBC,OAAM;AAC5C,MAAIC,WAAU,IAAID,KAAI;AAEtB,WAAS,QAAQ,QAAQ,KAAK;AAC5B,WAAO,CAAC,SAASC,UAAS,IAAI,GAAG,IAAIA,QAAO;AAAA,EAC9C;AAEA,UAAQ,SAAS,SAAS,GAAG,GAAG;AAC9B,WAAO,CAAC,IAAIA,UAAS,KAAK,IAAIA,QAAO,CAAC;AAAA,EACxC;AAEA,SAAO;AACT;;;ACVO,SAAS,kBAAkBC,KAAIC,KAAI;AACxC,MAAI,MAAM,IAAID,GAAE,GAAG,KAAK,MAAM,IAAIC,GAAE,KAAK;AAGzC,MAAI,IAAI,CAAC,IAAI,QAAS,QAAO,wBAAwBD,GAAE;AAEvD,MAAIE,KAAI,IAAI,OAAO,IAAI,IAAI,MAAM,KAAK,KAAKA,EAAC,IAAI;AAEhD,WAAS,QAAQ,GAAG,GAAG;AACrB,QAAI,IAAI,KAAKA,KAAI,IAAI,IAAI,IAAI,CAAC,CAAC,IAAI;AACnC,WAAO,CAAC,IAAI,IAAI,KAAK,CAAC,GAAG,KAAK,IAAI,IAAI,CAAC,CAAC;AAAA,EAC1C;AAEA,UAAQ,SAAS,SAAS,GAAG,GAAG;AAC9B,QAAI,MAAM,KAAK,GACX,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,GAAG;AACrC,QAAI,MAAM,IAAI;AACZ,WAAK,KAAK,KAAK,CAAC,IAAI,KAAK,GAAG;AAC9B,WAAO,CAAC,IAAI,GAAG,MAAMA,MAAK,IAAI,IAAI,MAAM,OAAO,IAAI,MAAM,IAAI,EAAE,CAAC;AAAA,EAClE;AAEA,SAAO;AACT;AAEe,SAAR,yBAAmB;AACxB,SAAO,gBAAgB,iBAAiB,EACnC,MAAM,OAAO,EACb,OAAO,CAAC,GAAG,OAAO,CAAC;AAC1B;;;AC9Be,SAAR,iBAAmB;AACxB,SAAO,uBAAe,EACjB,UAAU,CAAC,MAAM,IAAI,CAAC,EACtB,MAAM,IAAI,EACV,UAAU,CAAC,KAAK,GAAG,CAAC,EACpB,OAAO,CAAC,IAAI,CAAC,CAAC,EACd,OAAO,CAAC,MAAM,IAAI,CAAC;AAC1B;;;ACFA,SAAS,UAAU,SAAS;AAC1B,MAAI,IAAI,QAAQ;AAChB,SAAO;AAAA,IACL,OAAO,SAAS,GAAG,GAAG;AAAE,UAAI,IAAI;AAAI,aAAO,EAAE,IAAI,EAAG,SAAQ,CAAC,EAAE,MAAM,GAAG,CAAC;AAAA,IAAG;AAAA,IAC5E,QAAQ,WAAW;AAAE,UAAI,IAAI;AAAI,aAAO,EAAE,IAAI,EAAG,SAAQ,CAAC,EAAE,OAAO;AAAA,IAAG;AAAA,IACtE,WAAW,WAAW;AAAE,UAAI,IAAI;AAAI,aAAO,EAAE,IAAI,EAAG,SAAQ,CAAC,EAAE,UAAU;AAAA,IAAG;AAAA,IAC5E,SAAS,WAAW;AAAE,UAAI,IAAI;AAAI,aAAO,EAAE,IAAI,EAAG,SAAQ,CAAC,EAAE,QAAQ;AAAA,IAAG;AAAA,IACxE,cAAc,WAAW;AAAE,UAAI,IAAI;AAAI,aAAO,EAAE,IAAI,EAAG,SAAQ,CAAC,EAAE,aAAa;AAAA,IAAG;AAAA,IAClF,YAAY,WAAW;AAAE,UAAI,IAAI;AAAI,aAAO,EAAE,IAAI,EAAG,SAAQ,CAAC,EAAE,WAAW;AAAA,IAAG;AAAA,EAChF;AACF;AAOe,SAAR,oBAAmB;AACxB,MAAI,OACA,aACA,UAAU,eAAO,GAAG,cACpB,SAAS,uBAAe,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,CAAC,IAAI,EAAE,CAAC,GAAG,aACnF,SAAS,uBAAe,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,CAAC,GAAG,EAAE,CAAC,GAAG,aAClF,OAAO,cAAc,EAAC,OAAO,SAAS,GAAG,GAAG;AAAE,YAAQ,CAAC,GAAG,CAAC;AAAA,EAAG,EAAC;AAEnE,WAAS,UAAUC,cAAa;AAC9B,QAAI,IAAIA,aAAY,CAAC,GAAG,IAAIA,aAAY,CAAC;AACzC,WAAO,QAAQ,OACV,aAAa,MAAM,GAAG,CAAC,GAAG,WACvB,YAAY,MAAM,GAAG,CAAC,GAAG,WACzB,YAAY,MAAM,GAAG,CAAC,GAAG;AAAA,EACnC;AAEA,YAAU,SAAS,SAASA,cAAa;AACvC,QAAI,IAAI,QAAQ,MAAM,GAClB,IAAI,QAAQ,UAAU,GACtB,KAAKA,aAAY,CAAC,IAAI,EAAE,CAAC,KAAK,GAC9B,KAAKA,aAAY,CAAC,IAAI,EAAE,CAAC,KAAK;AAClC,YAAQ,KAAK,QAAS,IAAI,SAAS,KAAK,UAAU,IAAI,SAAS,SACzD,KAAK,SAAS,IAAI,SAAS,KAAK,UAAU,IAAI,SAAS,SACvD,SAAS,OAAOA,YAAW;AAAA,EACnC;AAEA,YAAU,SAAS,SAAS,QAAQ;AAClC,WAAO,SAAS,gBAAgB,SAAS,QAAQ,QAAQ,UAAU,CAAC,QAAQ,OAAO,cAAc,MAAM,GAAG,OAAO,OAAO,MAAM,GAAG,OAAO,OAAO,MAAM,CAAC,CAAC;AAAA,EACzJ;AAEA,YAAU,YAAY,SAAS,GAAG;AAChC,QAAI,CAAC,UAAU,OAAQ,QAAO,QAAQ,UAAU;AAChD,YAAQ,UAAU,CAAC,GAAG,OAAO,UAAU,CAAC,GAAG,OAAO,UAAU,CAAC;AAC7D,WAAO,MAAM;AAAA,EACf;AAEA,YAAU,QAAQ,SAAS,GAAG;AAC5B,QAAI,CAAC,UAAU,OAAQ,QAAO,QAAQ,MAAM;AAC5C,YAAQ,MAAM,CAAC,GAAG,OAAO,MAAM,IAAI,IAAI,GAAG,OAAO,MAAM,CAAC;AACxD,WAAO,UAAU,UAAU,QAAQ,UAAU,CAAC;AAAA,EAChD;AAEA,YAAU,YAAY,SAAS,GAAG;AAChC,QAAI,CAAC,UAAU,OAAQ,QAAO,QAAQ,UAAU;AAChD,QAAI,IAAI,QAAQ,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;AAE5C,mBAAe,QACV,UAAU,CAAC,EACX,WAAW,CAAC,CAAC,IAAI,QAAQ,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,QAAQ,GAAG,IAAI,QAAQ,CAAC,CAAC,CAAC,EAC3E,OAAO,WAAW;AAEvB,kBAAc,OACT,UAAU,CAAC,IAAI,QAAQ,GAAG,IAAI,QAAQ,CAAC,CAAC,EACxC,WAAW,CAAC,CAAC,IAAI,QAAQ,IAAI,SAAS,IAAI,OAAQ,IAAI,OAAO,GAAG,CAAC,IAAI,QAAQ,IAAI,SAAS,IAAI,QAAQ,IAAI,OAAO,CAAC,CAAC,EACnH,OAAO,WAAW;AAEvB,kBAAc,OACT,UAAU,CAAC,IAAI,QAAQ,GAAG,IAAI,QAAQ,CAAC,CAAC,EACxC,WAAW,CAAC,CAAC,IAAI,QAAQ,IAAI,SAAS,IAAI,QAAQ,IAAI,OAAO,GAAG,CAAC,IAAI,QAAQ,IAAI,SAAS,IAAI,QAAQ,IAAI,OAAO,CAAC,CAAC,EACnH,OAAO,WAAW;AAEvB,WAAO,MAAM;AAAA,EACf;AAEA,YAAU,YAAY,SAAS,QAAQC,SAAQ;AAC7C,WAAO,UAAU,WAAW,QAAQA,OAAM;AAAA,EAC5C;AAEA,YAAU,UAAU,SAAS,MAAMA,SAAQ;AACzC,WAAO,QAAQ,WAAW,MAAMA,OAAM;AAAA,EACxC;AAEA,YAAU,WAAW,SAAS,OAAOA,SAAQ;AAC3C,WAAO,SAAS,WAAW,OAAOA,OAAM;AAAA,EAC1C;AAEA,YAAU,YAAY,SAAS,QAAQA,SAAQ;AAC7C,WAAO,UAAU,WAAW,QAAQA,OAAM;AAAA,EAC5C;AAEA,WAAS,QAAQ;AACf,YAAQ,cAAc;AACtB,WAAO;AAAA,EACT;AAEA,SAAO,UAAU,MAAM,IAAI;AAC7B;;;AC5GO,SAAS,aAAaC,QAAO;AAClC,SAAO,SAAS,GAAG,GAAG;AACpB,QAAI,KAAK,IAAI,CAAC,GACV,KAAK,IAAI,CAAC,GACV,IAAIA,OAAM,KAAK,EAAE;AACjB,QAAI,MAAM,SAAU,QAAO,CAAC,GAAG,CAAC;AACpC,WAAO;AAAA,MACL,IAAI,KAAK,IAAI,CAAC;AAAA,MACd,IAAI,IAAI,CAAC;AAAA,IACX;AAAA,EACF;AACF;AAEO,SAAS,gBAAgBC,QAAO;AACrC,SAAO,SAAS,GAAG,GAAG;AACpB,QAAI,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,GACtBC,KAAID,OAAM,CAAC,GACX,KAAK,IAAIC,EAAC,GACVC,MAAK,IAAID,EAAC;AACd,WAAO;AAAA,MACL,MAAM,IAAI,IAAI,IAAIC,GAAE;AAAA,MACpB,KAAK,KAAK,IAAI,KAAK,CAAC;AAAA,IACtB;AAAA,EACF;AACF;;;ACtBO,IAAI,wBAAwB,aAAa,SAAS,MAAM;AAC7D,SAAO,KAAK,KAAK,IAAI,KAAK;AAC5B,CAAC;AAED,sBAAsB,SAAS,gBAAgB,SAAS,GAAG;AACzD,SAAO,IAAI,KAAK,IAAI,CAAC;AACvB,CAAC;AAEc,SAAR,6BAAmB;AACxB,SAAO,WAAW,qBAAqB,EAClC,MAAM,MAAM,EACZ,UAAU,MAAM,IAAI;AAC3B;;;ACZO,IAAI,0BAA0B,aAAa,SAASC,IAAG;AAC5D,UAAQA,KAAI,KAAKA,EAAC,MAAMA,KAAI,IAAIA,EAAC;AACnC,CAAC;AAED,wBAAwB,SAAS,gBAAgB,SAAS,GAAG;AAC3D,SAAO;AACT,CAAC;AAEc,SAAR,+BAAmB;AACxB,SAAO,WAAW,uBAAuB,EACpC,MAAM,OAAO,EACb,UAAU,MAAM,IAAI;AAC3B;;;ACZO,SAAS,YAAY,QAAQ,KAAK;AACvC,SAAO,CAAC,QAAQ,IAAI,KAAK,SAAS,OAAO,CAAC,CAAC,CAAC;AAC9C;AAEA,YAAY,SAAS,SAAS,GAAG,GAAG;AAClC,SAAO,CAAC,GAAG,IAAI,KAAK,IAAI,CAAC,CAAC,IAAI,MAAM;AACtC;AAEe,SAAR,mBAAmB;AACxB,SAAO,mBAAmB,WAAW,EAChC,MAAM,MAAM,GAAG;AACtB;AAEO,SAAS,mBAAmB,SAAS;AAC1C,MAAI,IAAI,WAAW,OAAO,GACtB,SAAS,EAAE,QACXC,SAAQ,EAAE,OACV,YAAY,EAAE,WACd,aAAa,EAAE,YACfC,MAAK,MAAMC,KAAIC,KAAIC;AAEvB,IAAE,QAAQ,SAAS,GAAG;AACpB,WAAO,UAAU,UAAUJ,OAAM,CAAC,GAAG,OAAO,KAAKA,OAAM;AAAA,EACzD;AAEA,IAAE,YAAY,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,UAAU,CAAC,GAAG,OAAO,KAAK,UAAU;AAAA,EACjE;AAEA,IAAE,SAAS,SAAS,GAAG;AACrB,WAAO,UAAU,UAAU,OAAO,CAAC,GAAG,OAAO,KAAK,OAAO;AAAA,EAC3D;AAEA,IAAE,aAAa,SAAS,GAAG;AACzB,WAAO,UAAU,UAAW,KAAK,OAAOC,MAAKC,MAAKC,MAAKC,MAAK,QAAQH,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,IAAK,OAAO,KAAKH,OAAM,OAAO,OAAO,CAAC,CAACA,KAAIC,GAAE,GAAG,CAACC,KAAIC,GAAE,CAAC;AAAA,EACvL;AAEA,WAAS,SAAS;AAChB,QAAI,IAAI,KAAKJ,OAAM,GACf,IAAI,EAAE,iBAAS,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7C,WAAO,WAAWC,OAAM,OAClB,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,YAAY,cAC3D,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC,IAAI,GAAGA,GAAE,GAAGC,GAAE,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,IAAI,GAAGC,GAAE,GAAGC,GAAE,CAAC,IAC3D,CAAC,CAACH,KAAI,KAAK,IAAI,EAAE,CAAC,IAAI,GAAGC,GAAE,CAAC,GAAG,CAACC,KAAI,KAAK,IAAI,EAAE,CAAC,IAAI,GAAGC,GAAE,CAAC,CAAC,CAAC;AAAA,EACpE;AAEA,SAAO,OAAO;AAChB;;;AC/CA,SAAS,KAAK,GAAG;AACf,SAAO,KAAK,SAAS,KAAK,CAAC;AAC7B;AAEO,SAAS,kBAAkBC,KAAIC,KAAI;AACxC,MAAI,MAAM,IAAID,GAAE,GACZ,IAAIA,QAAOC,MAAK,IAAID,GAAE,IAAI,IAAI,MAAM,IAAIC,GAAE,CAAC,IAAI,IAAI,KAAKA,GAAE,IAAI,KAAKD,GAAE,CAAC,GACtE,IAAI,MAAM,IAAI,KAAKA,GAAE,GAAG,CAAC,IAAI;AAEjC,MAAI,CAAC,EAAG,QAAO;AAEf,WAAS,QAAQ,GAAG,GAAG;AACrB,QAAI,IAAI,GAAG;AAAE,UAAI,IAAI,CAAC,SAAS,QAAS,KAAI,CAAC,SAAS;AAAA,IAAS,OAC1D;AAAE,UAAI,IAAI,SAAS,QAAS,KAAI,SAAS;AAAA,IAAS;AACvD,QAAI,IAAI,IAAI,IAAI,KAAK,CAAC,GAAG,CAAC;AAC1B,WAAO,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC;AAAA,EAC5C;AAEA,UAAQ,SAAS,SAAS,GAAG,GAAG;AAC9B,QAAI,KAAK,IAAI,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,IAAI,KAAK,EAAE,GAChD,IAAI,MAAM,GAAG,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE;AACjC,QAAI,KAAK,IAAI;AACX,WAAK,KAAK,KAAK,CAAC,IAAI,KAAK,EAAE;AAC7B,WAAO,CAAC,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,IAAI,MAAM;AAAA,EACrD;AAEA,SAAO;AACT;AAEe,SAAR,yBAAmB;AACxB,SAAO,gBAAgB,iBAAiB,EACnC,MAAM,KAAK,EACX,UAAU,CAAC,IAAI,EAAE,CAAC;AACzB;;;ACnCO,SAAS,mBAAmB,QAAQ,KAAK;AAC9C,SAAO,CAAC,QAAQ,GAAG;AACrB;AAEA,mBAAmB,SAAS;AAEb,SAAR,0BAAmB;AACxB,SAAO,WAAW,kBAAkB,EAC/B,MAAM,MAAM;AACnB;;;ACPO,SAAS,oBAAoBE,KAAIC,KAAI;AAC1C,MAAI,MAAM,IAAID,GAAE,GACZ,IAAIA,QAAOC,MAAK,IAAID,GAAE,KAAK,MAAM,IAAIC,GAAE,MAAMA,MAAKD,MAClD,IAAI,MAAM,IAAIA;AAElB,MAAI,IAAI,CAAC,IAAI,QAAS,QAAO;AAE7B,WAAS,QAAQ,GAAG,GAAG;AACrB,QAAI,KAAK,IAAI,GAAG,KAAK,IAAI;AACzB,WAAO,CAAC,KAAK,IAAI,EAAE,GAAG,IAAI,KAAK,IAAI,EAAE,CAAC;AAAA,EACxC;AAEA,UAAQ,SAAS,SAAS,GAAG,GAAG;AAC9B,QAAI,KAAK,IAAI,GACT,IAAI,MAAM,GAAG,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE;AACnC,QAAI,KAAK,IAAI;AACX,WAAK,KAAK,KAAK,CAAC,IAAI,KAAK,EAAE;AAC7B,WAAO,CAAC,IAAI,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,IAAI,KAAK,EAAE,CAAC;AAAA,EACpD;AAEA,SAAO;AACT;AAEe,SAAR,2BAAmB;AACxB,SAAO,gBAAgB,mBAAmB,EACrC,MAAM,OAAO,EACb,OAAO,CAAC,GAAG,OAAO,CAAC;AAC1B;;;AC5BA,IAAI,KAAK;AAAT,IACI,KAAK;AADT,IAEI,KAAK;AAFT,IAGI,KAAK;AAHT,IAII,IAAI,KAAK,CAAC,IAAI;AAJlB,IAKI,aAAa;AAEV,SAAS,cAAc,QAAQ,KAAK;AACzC,MAAI,IAAI,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,KAAK,KAAK;AACvD,SAAO;AAAA,IACL,SAAS,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK;AAAA,IACnE,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK;AAAA,EACtC;AACF;AAEA,cAAc,SAAS,SAAS,GAAG,GAAG;AACpC,MAAI,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,KAAK,KAAK;AACtC,WAAS,IAAI,GAAG,OAAO,IAAI,KAAK,IAAI,YAAY,EAAE,GAAG;AACnD,SAAK,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,OAAO;AAChD,UAAM,KAAK,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK;AACjD,SAAK,QAAQ,KAAK,KAAK,KAAK,IAAI,GAAG,KAAK,KAAK,KAAK;AAClD,QAAI,IAAI,KAAK,IAAI,SAAU;AAAA,EAC7B;AACA,SAAO;AAAA,IACL,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK,OAAO,IAAI,CAAC;AAAA,IAChE,KAAK,IAAI,CAAC,IAAI,CAAC;AAAA,EACjB;AACF;AAEe,SAAR,qBAAmB;AACxB,SAAO,WAAW,aAAa,EAC1B,MAAM,OAAO;AACpB;;;AC/BO,SAAS,YAAY,GAAG,GAAG;AAChC,MAAI,KAAK,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI;AAC9B,SAAO,CAAC,KAAK,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACrC;AAEA,YAAY,SAAS,gBAAgB,IAAI;AAE1B,SAAR,mBAAmB;AACxB,SAAO,WAAW,WAAW,EACxB,MAAM,OAAO,EACb,UAAU,EAAE;AACnB;;;ACTe,SAARE,oBAAmB;AACxB,MAAI,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GACpC,QAAQ,GAAGC,KAAI,IACfC,MAAK,MAAMC,KAAIC,KAAIC,KACnB,KAAK,GAAG,KAAK,GACb,YAAY,YAAY;AAAA,IACtB,OAAO,SAAS,GAAG,GAAG;AACpB,UAAI,IAAIC,YAAW,CAAC,GAAG,CAAC,CAAC;AACzB,WAAK,OAAO,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,IAC9B;AAAA,EACF,CAAC,GACD,WAAW,kBACX,OACA;AAEJ,WAAS,QAAQ;AACf,SAAK,IAAI;AACT,SAAK,IAAI;AACT,YAAQ,cAAc;AACtB,WAAOA;AAAA,EACT;AAEA,WAASA,YAAY,GAAG;AACtB,QAAI,IAAI,EAAE,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,IAAI;AAC9B,QAAI,OAAO;AACT,UAAI,IAAI,IAAIL,MAAK,IAAI;AACrB,UAAI,IAAIA,MAAK,IAAI;AACjB,UAAI;AAAA,IACN;AACA,WAAO,CAAC,IAAI,IAAI,IAAI,EAAE;AAAA,EACxB;AACA,EAAAK,YAAW,SAAS,SAAS,GAAG;AAC9B,QAAI,IAAI,EAAE,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,IAAI;AAC9B,QAAI,OAAO;AACT,UAAI,IAAI,IAAIL,MAAK,IAAI;AACrB,UAAI,IAAIA,MAAK,IAAI;AACjB,UAAI;AAAA,IACN;AACA,WAAO,CAAC,IAAI,IAAI,IAAI,EAAE;AAAA,EACxB;AACA,EAAAK,YAAW,SAAS,SAAS,QAAQ;AACnC,WAAO,SAAS,gBAAgB,SAAS,QAAQ,QAAQ,UAAU,SAAS,cAAc,MAAM,CAAC;AAAA,EACnG;AACA,EAAAA,YAAW,WAAW,SAAS,GAAG;AAChC,WAAO,UAAU,UAAU,WAAW,GAAGJ,MAAKC,MAAKC,MAAKC,MAAK,MAAM,MAAM,KAAK;AAAA,EAChF;AACA,EAAAC,YAAW,aAAa,SAAS,GAAG;AAClC,WAAO,UAAU,UAAU,WAAW,KAAK,QAAQJ,MAAKC,MAAKC,MAAKC,MAAK,MAAM,oBAAY,cAAcH,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,KAAKH,OAAM,OAAO,OAAO,CAAC,CAACA,KAAIC,GAAE,GAAG,CAACC,KAAIC,GAAE,CAAC;AAAA,EACxN;AACA,EAAAC,YAAW,QAAQ,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,IAAI,CAAC,GAAG,MAAM,KAAK;AAAA,EAChD;AACA,EAAAA,YAAW,YAAY,SAAS,GAAG;AACjC,WAAO,UAAU,UAAU,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE;AAAA,EACvE;AACA,EAAAA,YAAW,QAAQ,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,QAAQ,IAAI,MAAM,SAAS,KAAK,IAAI,KAAK,GAAGL,MAAK,IAAI,KAAK,GAAG,MAAM,KAAK,QAAQ;AAAA,EAC7G;AACA,EAAAK,YAAW,WAAW,SAAS,GAAG;AAChC,WAAO,UAAU,UAAU,KAAK,IAAI,KAAK,GAAG,MAAM,KAAK,KAAK;AAAA,EAC9D;AACA,EAAAA,YAAW,WAAW,SAAS,GAAG;AAChC,WAAO,UAAU,UAAU,KAAK,IAAI,KAAK,GAAG,MAAM,KAAK,KAAK;AAAA,EAC9D;AACA,EAAAA,YAAW,YAAY,SAAS,QAAQC,SAAQ;AAC9C,WAAO,UAAUD,aAAY,QAAQC,OAAM;AAAA,EAC7C;AACA,EAAAD,YAAW,UAAU,SAAS,MAAMC,SAAQ;AAC1C,WAAO,QAAQD,aAAY,MAAMC,OAAM;AAAA,EACzC;AACA,EAAAD,YAAW,WAAW,SAAS,OAAOC,SAAQ;AAC5C,WAAO,SAASD,aAAY,OAAOC,OAAM;AAAA,EAC3C;AACA,EAAAD,YAAW,YAAY,SAAS,QAAQC,SAAQ;AAC9C,WAAO,UAAUD,aAAY,QAAQC,OAAM;AAAA,EAC7C;AAEA,SAAOD;AACT;;;ACjFO,SAAS,iBAAiB,QAAQ,KAAK;AAC5C,MAAI,OAAO,MAAM,KAAK,OAAO,OAAO;AACpC,SAAO;AAAA,IACL,UAAU,SAAS,WAAW,OAAO,QAAQ,YAAY,QAAQ,UAAW,OAAO,UAAW;AAAA,IAC9F,OAAO,WAAW,QAAQ,WAAW,QAAQ,YAAY,WAAW,OAAO,UAAW;AAAA,EACxF;AACF;AAEA,iBAAiB,SAAS,SAAS,GAAG,GAAG;AACvC,MAAI,MAAM,GAAG,IAAI,IAAI;AACrB,KAAG;AACD,QAAI,OAAO,MAAM,KAAK,OAAO,OAAO;AACpC,WAAO,SAAS,OAAO,WAAW,QAAQ,WAAW,QAAQ,YAAY,WAAW,OAAO,UAAW,UAAU,MAC3G,WAAW,QAAQ,WAAW,IAAI,QAAQ,YAAY,IAAI,WAAW,IAAI,OAAO,UAAW,KAAK;AAAA,EACvG,SAAS,IAAI,KAAK,IAAI,WAAW,EAAE,IAAI;AACvC,SAAO;AAAA,IACL,KAAK,UAAU,OAAO,MAAM,QAAQ,YAAY,QAAQ,YAAY,OAAO,OAAO,QAAQ,UAAW,UAAW;AAAA,IAChH;AAAA,EACF;AACF;AAEe,SAAR,wBAAmB;AACxB,SAAO,WAAW,gBAAgB,EAC7B,MAAM,OAAO;AACpB;;;ACvBO,SAAS,gBAAgB,GAAG,GAAG;AACpC,SAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AACjC;AAEA,gBAAgB,SAAS,gBAAgB,IAAI;AAE9B,SAAR,uBAAmB;AACxB,SAAO,WAAW,eAAe,EAC5B,MAAM,KAAK,EACX,UAAU,KAAK,OAAO;AAC7B;;;ACVO,SAAS,iBAAiB,GAAG,GAAG;AACrC,MAAI,KAAK,IAAI,CAAC,GAAG,IAAI,IAAI,IAAI,CAAC,IAAI;AAClC,SAAO,CAAC,KAAK,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACrC;AAEA,iBAAiB,SAAS,gBAAgB,SAAS,GAAG;AACpD,SAAO,IAAI,KAAK,CAAC;AACnB,CAAC;AAEc,SAAR,wBAAmB;AACxB,SAAO,WAAW,gBAAgB,EAC7B,MAAM,GAAG,EACT,UAAU,GAAG;AACpB;;;ACdO,SAAS,sBAAsB,QAAQ,KAAK;AACjD,SAAO,CAAC,IAAI,KAAK,SAAS,OAAO,CAAC,CAAC,GAAG,CAAC,MAAM;AAC/C;AAEA,sBAAsB,SAAS,SAAS,GAAG,GAAG;AAC5C,SAAO,CAAC,CAAC,GAAG,IAAI,KAAK,IAAI,CAAC,CAAC,IAAI,MAAM;AACvC;AAEe,SAAR,6BAAmB;AACxB,MAAI,IAAI,mBAAmB,qBAAqB,GAC5C,SAAS,EAAE,QACX,SAAS,EAAE;AAEf,IAAE,SAAS,SAAS,GAAG;AACrB,WAAO,UAAU,SAAS,OAAO,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAAA,EAC/E;AAEA,IAAE,SAAS,SAAS,GAAG;AACrB,WAAO,UAAU,SAAS,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,SAAS,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE;AAAA,EACvH;AAEA,SAAO,OAAO,CAAC,GAAG,GAAG,EAAE,CAAC,EACnB,MAAM,OAAO;AACpB;;;AC1BA,IAAME,WAAU;AAEhB,IAAqB,OAArB,MAA0B;AAAA,EACxB,cAAc;AACZ,SAAK,MAAM,KAAK;AAAA,IAChB,KAAK,MAAM,KAAK,MAAM;AACtB,SAAK,IAAI;AAAA,EACX;AAAA,EACA,OAAO,GAAG,GAAG;AACX,SAAK,KAAK,IAAI,KAAK,MAAM,KAAK,MAAM,CAAC,CAAC,IAAI,KAAK,MAAM,KAAK,MAAM,CAAC,CAAC;AAAA,EACpE;AAAA,EACA,YAAY;AACV,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK;AACrC,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA,EACA,OAAO,GAAG,GAAG;AACX,SAAK,KAAK,IAAI,KAAK,MAAM,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,EAC9C;AAAA,EACA,IAAI,GAAG,GAAG,GAAG;AACX,QAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;AACrB,UAAMC,MAAK,IAAI;AACf,UAAMC,MAAK;AACX,QAAI,IAAI,EAAG,OAAM,IAAI,MAAM,iBAAiB;AAC5C,QAAI,KAAK,QAAQ,KAAM,MAAK,KAAK,IAAID,GAAE,IAAIC,GAAE;AAAA,aACpC,KAAK,IAAI,KAAK,MAAMD,GAAE,IAAID,YAAW,KAAK,IAAI,KAAK,MAAME,GAAE,IAAIF,SAAS,MAAK,KAAK,MAAMC,MAAK,MAAMC;AAC5G,QAAI,CAAC,EAAG;AACR,SAAK,KAAK,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,KAAK,MAAMD,GAAE,IAAI,KAAK,MAAMC,GAAE;AAAA,EAC5F;AAAA,EACA,KAAK,GAAG,GAAG,GAAG,GAAG;AACf,SAAK,KAAK,IAAI,KAAK,MAAM,KAAK,MAAM,CAAC,CAAC,IAAI,KAAK,MAAM,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAAA,EACtF;AAAA,EACA,QAAQ;AACN,WAAO,KAAK,KAAK;AAAA,EACnB;AACF;;;ACpCA,IAAqB,UAArB,MAA6B;AAAA,EAC3B,cAAc;AACZ,SAAK,IAAI,CAAC;AAAA,EACZ;AAAA,EACA,OAAO,GAAG,GAAG;AACX,SAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,EACpB;AAAA,EACA,YAAY;AACV,SAAK,EAAE,KAAK,KAAK,EAAE,CAAC,EAAE,MAAM,CAAC;AAAA,EAC/B;AAAA,EACA,OAAO,GAAG,GAAG;AACX,SAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,EACpB;AAAA,EACA,QAAQ;AACN,WAAO,KAAK,EAAE,SAAS,KAAK,IAAI;AAAA,EAClC;AACF;;;ACbA,IAAqB,UAArB,MAA6B;AAAA,EAC3B,YAAY,UAAU,CAAC,MAAM,MAAM,MAAM,IAAI,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG,GAAG;AACjE,QAAI,GAAG,OAAO,CAAC,UAAU,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,OAAO,CAAC,OAAQ,OAAM,IAAI,MAAM,gBAAgB;AAChH,SAAK,WAAW;AAChB,SAAK,iBAAiB,IAAI,aAAa,SAAS,OAAO,SAAS,CAAC;AACjE,SAAK,UAAU,IAAI,aAAa,SAAS,OAAO,SAAS,CAAC;AAC1D,SAAK,OAAO,MAAM,KAAK,OAAO;AAC9B,SAAK,OAAO,MAAM,KAAK,OAAO;AAC9B,SAAK,MAAM;AAAA,EACb;AAAA,EACA,SAAS;AACP,SAAK,SAAS,OAAO;AACrB,SAAK,MAAM;AACX,WAAO;AAAA,EACT;AAAA,EACA,QAAQ;AACN,UAAM,EAAC,UAAU,EAAC,QAAQ,MAAM,UAAS,GAAG,QAAO,IAAI;AACvD,QAAI,IAAI;AAGR,UAAM,gBAAgB,KAAK,gBAAgB,KAAK,eAAe,SAAS,GAAG,UAAU,SAAS,IAAI,CAAC;AACnG,aAAS,IAAI,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,GAAG,GAAG,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG;AACxE,YAAM,KAAK,UAAU,CAAC,IAAI;AAC1B,YAAM,KAAK,UAAU,IAAI,CAAC,IAAI;AAC9B,YAAM,KAAK,UAAU,IAAI,CAAC,IAAI;AAC9B,YAAMC,MAAK,OAAO,EAAE;AACpB,YAAMC,MAAK,OAAO,KAAK,CAAC;AACxB,YAAM,KAAK,OAAO,EAAE;AACpB,YAAM,KAAK,OAAO,KAAK,CAAC;AACxB,YAAM,KAAK,OAAO,EAAE;AACpB,YAAM,KAAK,OAAO,KAAK,CAAC;AAExB,YAAM,KAAK,KAAKD;AAChB,YAAM,KAAK,KAAKC;AAChB,YAAM,KAAK,KAAKD;AAChB,YAAM,KAAK,KAAKC;AAChB,YAAMC,OAAM,KAAK,KAAK,KAAK,MAAM;AAEjC,UAAI,KAAK,IAAIA,GAAE,IAAI,MAAM;AAIvB,YAAI,OAAO,QAAW;AACpB,eAAK,KAAK;AACV,qBAAWC,MAAK,KAAM,OAAM,OAAOA,KAAI,CAAC,GAAG,MAAM,OAAOA,KAAI,IAAI,CAAC;AACjE,gBAAM,KAAK,QAAQ,MAAM,KAAK;AAAA,QAChC;AACA,cAAM,IAAI,MAAM,KAAK,MAAM,KAAKH,OAAM,MAAM,KAAKC,OAAM,EAAE;AACzD,aAAKD,MAAK,MAAM,IAAI,IAAI;AACxB,aAAKC,MAAK,MAAM,IAAI,IAAI;AAAA,MAC1B,OAAO;AACL,cAAM,IAAI,IAAIC;AACd,cAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,cAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,YAAIF,OAAM,KAAK,KAAK,KAAK,MAAM;AAC/B,YAAIC,OAAM,KAAK,KAAK,KAAK,MAAM;AAAA,MACjC;AACA,oBAAc,CAAC,IAAI;AACnB,oBAAc,IAAI,CAAC,IAAI;AAAA,IACzB;AAGA,QAAI,IAAI,KAAK,KAAK,SAAS,CAAC;AAC5B,QAAIG,KAAI,KAAK,IAAI;AACjB,QAAIC,KAAIL,MAAK,OAAO,IAAI,CAAC;AACzB,QAAIM,KAAIL,MAAK,OAAO,IAAI,IAAI,CAAC;AAC7B,YAAQ,KAAK,CAAC;AACd,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,UAAI,KAAK,CAAC;AACV,MAAAG,MAAK,IAAIC,MAAKL,KAAIM,MAAKL;AACvB,WAAK,IAAI,GAAGD,MAAK,OAAO,IAAI,CAAC,GAAGC,MAAK,OAAO,IAAI,IAAI,CAAC;AACrD,cAAQG,MAAK,CAAC,IAAI,QAAQ,EAAE,IAAIE,MAAKL;AACrC,cAAQG,MAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,IAAIJ,MAAKK;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,OAAO,SAAS;AACd,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,UAAM,EAAC,UAAU,EAAC,WAAW,SAAS,KAAI,GAAG,eAAe,QAAO,IAAI;AACvE,QAAI,KAAK,UAAU,EAAG,QAAO;AAC7B,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,EAAE,GAAG;AAChD,YAAM,IAAI,UAAU,CAAC;AACrB,UAAI,IAAI,EAAG;AACX,YAAM,KAAK,KAAK,MAAM,IAAI,CAAC,IAAI;AAC/B,YAAM,KAAK,KAAK,MAAM,IAAI,CAAC,IAAI;AAC/B,YAAM,KAAK,cAAc,EAAE;AAC3B,YAAM,KAAK,cAAc,KAAK,CAAC;AAC/B,YAAM,KAAK,cAAc,EAAE;AAC3B,YAAM,KAAK,cAAc,KAAK,CAAC;AAC/B,WAAK,eAAe,IAAI,IAAI,IAAI,IAAI,OAAO;AAAA,IAC7C;AACA,QAAI,IAAI,KAAK,KAAK,KAAK,SAAS,CAAC;AACjC,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,WAAK,IAAI,KAAK,KAAK,CAAC;AACpB,YAAM,IAAI,KAAK,MAAM,QAAQ,EAAE,IAAI,CAAC,IAAI;AACxC,YAAM,IAAI,cAAc,CAAC;AACzB,YAAM,IAAI,cAAc,IAAI,CAAC;AAC7B,YAAME,KAAI,KAAK;AACf,YAAM,IAAI,KAAK,SAAS,GAAG,GAAG,QAAQA,KAAI,CAAC,GAAG,QAAQA,KAAI,CAAC,CAAC;AAC5D,UAAI,EAAG,MAAK,eAAe,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,OAAO;AAAA,IACtD;AACA,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,aAAa,SAAS;AACpB,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,YAAQ,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,KAAK,OAAO,KAAK,IAAI;AAC/E,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,WAAW,GAAG,SAAS;AACrB,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,UAAM,SAAS,KAAK,MAAM,CAAC;AAC3B,QAAI,WAAW,QAAQ,CAAC,OAAO,OAAQ;AACvC,YAAQ,OAAO,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AACnC,QAAI,IAAI,OAAO;AACf,WAAO,OAAO,CAAC,MAAM,OAAO,IAAE,CAAC,KAAK,OAAO,CAAC,MAAM,OAAO,IAAE,CAAC,KAAK,IAAI,EAAG,MAAK;AAC7E,aAASJ,KAAI,GAAGA,KAAI,GAAGA,MAAK,GAAG;AAC7B,UAAI,OAAOA,EAAC,MAAM,OAAOA,KAAE,CAAC,KAAK,OAAOA,KAAE,CAAC,MAAM,OAAOA,KAAE,CAAC;AACzD,gBAAQ,OAAO,OAAOA,EAAC,GAAG,OAAOA,KAAI,CAAC,CAAC;AAAA,IAC3C;AACA,YAAQ,UAAU;AAClB,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,CAAC,eAAe;AACd,UAAM,EAAC,UAAU,EAAC,OAAM,EAAC,IAAI;AAC7B,aAAS,IAAI,GAAG,IAAI,OAAO,SAAS,GAAG,IAAI,GAAG,EAAE,GAAG;AACjD,YAAM,OAAO,KAAK,YAAY,CAAC;AAC/B,UAAI,KAAM,MAAK,QAAQ,GAAG,MAAM;AAAA,IAClC;AAAA,EACF;AAAA,EACA,YAAY,GAAG;AACb,UAAM,UAAU,IAAI;AACpB,SAAK,WAAW,GAAG,OAAO;AAC1B,WAAO,QAAQ,MAAM;AAAA,EACvB;AAAA,EACA,eAAeE,KAAIC,KAAIN,KAAIC,KAAI,SAAS;AACtC,QAAI;AACJ,UAAM,KAAK,KAAK,YAAYI,KAAIC,GAAE;AAClC,UAAM,KAAK,KAAK,YAAYN,KAAIC,GAAE;AAClC,QAAI,OAAO,KAAK,OAAO,GAAG;AACxB,cAAQ,OAAOI,KAAIC,GAAE;AACrB,cAAQ,OAAON,KAAIC,GAAE;AAAA,IACvB,WAAW,IAAI,KAAK,aAAaI,KAAIC,KAAIN,KAAIC,KAAI,IAAI,EAAE,GAAG;AACxD,cAAQ,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACzB,cAAQ,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,SAAS,GAAG,GAAG,GAAG;AAChB,SAAK,IAAI,CAAC,GAAG,MAAM,OAAO,IAAI,CAAC,GAAG,MAAM,GAAI,QAAO;AACnD,WAAO,KAAK,SAAS,MAAM,GAAG,GAAG,CAAC,MAAM;AAAA,EAC1C;AAAA,EACA,CAAC,UAAU,GAAG;AACZ,UAAM,KAAK,KAAK,MAAM,CAAC;AACvB,QAAI,GAAI,YAAW,KAAK,KAAK,SAAS,UAAU,CAAC,GAAG;AAClD,YAAM,KAAK,KAAK,MAAM,CAAC;AAEvB,UAAI,GAAI,MAAM,UAAS,KAAK,GAAG,KAAK,GAAG,QAAQ,KAAK,IAAI,MAAM,GAAG;AAC/D,iBAAS,KAAK,GAAG,KAAK,GAAG,QAAQ,KAAK,IAAI,MAAM,GAAG;AACjD,cAAI,GAAG,EAAE,MAAM,GAAG,EAAE,KACb,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,KACxB,IAAI,KAAK,KAAK,EAAE,MAAM,IAAI,KAAK,KAAK,KAAK,EAAE,KAC3C,IAAI,KAAK,KAAK,EAAE,MAAM,IAAI,KAAK,KAAK,KAAK,EAAE,GAAG;AACnD,kBAAM;AACN,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,MAAM,GAAG;AACP,UAAM,EAAC,eAAe,UAAU,EAAC,SAAS,WAAW,UAAS,EAAC,IAAI;AACnE,UAAM,KAAK,QAAQ,CAAC;AACpB,QAAI,OAAO,GAAI,QAAO;AACtB,UAAM,SAAS,CAAC;AAChB,QAAI,IAAI;AACR,OAAG;AACD,YAAM,IAAI,KAAK,MAAM,IAAI,CAAC;AAC1B,aAAO,KAAK,cAAc,IAAI,CAAC,GAAG,cAAc,IAAI,IAAI,CAAC,CAAC;AAC1D,UAAI,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI;AAC9B,UAAI,UAAU,CAAC,MAAM,EAAG;AACxB,UAAI,UAAU,CAAC;AAAA,IACjB,SAAS,MAAM,MAAM,MAAM;AAC3B,WAAO;AAAA,EACT;AAAA,EACA,MAAM,GAAG;AAEP,QAAI,MAAM,KAAK,KAAK,SAAS,KAAK,WAAW,GAAG;AAC9C,aAAO,CAAC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;AAAA,IAChG;AACA,UAAM,SAAS,KAAK,MAAM,CAAC;AAC3B,QAAI,WAAW,KAAM,QAAO;AAC5B,UAAM,EAAC,SAAS,EAAC,IAAI;AACrB,UAAMM,KAAI,IAAI;AACd,WAAO,KAAK,UAAU,EAAEA,EAAC,KAAK,EAAEA,KAAI,CAAC,IAC/B,KAAK,cAAc,GAAG,QAAQ,EAAEA,EAAC,GAAG,EAAEA,KAAI,CAAC,GAAG,EAAEA,KAAI,CAAC,GAAG,EAAEA,KAAI,CAAC,CAAC,IAChE,KAAK,YAAY,GAAG,MAAM,CAAC;AAAA,EACnC;AAAA,EACA,YAAY,GAAG,QAAQ;AACrB,UAAM,IAAI,OAAO;AACjB,QAAI,IAAI;AACR,QAAIF,KAAIC,KAAIN,MAAK,OAAO,IAAI,CAAC,GAAGC,MAAK,OAAO,IAAI,CAAC;AACjD,QAAI,IAAI,KAAK,KAAK,YAAYD,KAAIC,GAAE;AACpC,QAAI,IAAI,KAAK;AACb,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AAC7B,MAAAI,MAAKL,KAAIM,MAAKL,KAAID,MAAK,OAAO,CAAC,GAAGC,MAAK,OAAO,IAAI,CAAC;AACnD,WAAK,IAAI,KAAK,KAAK,YAAYD,KAAIC,GAAE;AACrC,UAAI,OAAO,KAAK,OAAO,GAAG;AACxB,aAAK,IAAI,KAAK;AACd,YAAI,EAAG,GAAE,KAAKD,KAAIC,GAAE;AAAA,YACf,KAAI,CAACD,KAAIC,GAAE;AAAA,MAClB,OAAO;AACL,YAAI,GAAG,KAAK,KAAK,KAAK;AACtB,YAAI,OAAO,GAAG;AACZ,eAAK,IAAI,KAAK,aAAaI,KAAIC,KAAIN,KAAIC,KAAI,IAAI,EAAE,OAAO,KAAM;AAC9D,WAAC,KAAK,KAAK,KAAK,GAAG,IAAI;AAAA,QACzB,OAAO;AACL,eAAK,IAAI,KAAK,aAAaD,KAAIC,KAAII,KAAIC,KAAI,IAAI,EAAE,OAAO,KAAM;AAC9D,WAAC,KAAK,KAAK,KAAK,GAAG,IAAI;AACvB,eAAK,IAAI,KAAK,KAAK,UAAU,KAAK,GAAG;AACrC,cAAI,MAAM,GAAI,MAAK,MAAM,GAAG,IAAI,IAAI,GAAG,EAAE,MAAM;AAC/C,cAAI,EAAG,GAAE,KAAK,KAAK,GAAG;AAAA,cACjB,KAAI,CAAC,KAAK,GAAG;AAAA,QACpB;AACA,aAAK,IAAI,KAAK,KAAK,UAAU,KAAK,GAAG;AACrC,YAAI,MAAM,GAAI,MAAK,MAAM,GAAG,IAAI,IAAI,GAAG,EAAE,MAAM;AAC/C,YAAI,EAAG,GAAE,KAAK,KAAK,GAAG;AAAA,YACjB,KAAI,CAAC,KAAK,GAAG;AAAA,MACpB;AAAA,IACF;AACA,QAAI,GAAG;AACL,WAAK,IAAI,KAAK,KAAK,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACvC,UAAI,MAAM,GAAI,MAAK,MAAM,GAAG,IAAI,IAAI,GAAG,EAAE,MAAM;AAAA,IACjD,WAAW,KAAK,SAAS,IAAI,KAAK,OAAO,KAAK,QAAQ,IAAI,KAAK,OAAO,KAAK,QAAQ,CAAC,GAAG;AACrF,aAAO,CAAC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;AAAA,IAChG;AACA,WAAO;AAAA,EACT;AAAA,EACA,aAAaD,KAAIC,KAAIN,KAAIC,KAAI,IAAI,IAAI;AAEnC,UAAM,OAAO,KAAK;AAClB,QAAI,KAAM,EAACI,KAAIC,KAAIN,KAAIC,KAAI,IAAI,EAAE,IAAI,CAACD,KAAIC,KAAII,KAAIC,KAAI,IAAI,EAAE;AAC5D,WAAO,MAAM;AACX,UAAI,OAAO,KAAK,OAAO,EAAG,QAAO,OAAO,CAACN,KAAIC,KAAII,KAAIC,GAAE,IAAI,CAACD,KAAIC,KAAIN,KAAIC,GAAE;AAC1E,UAAI,KAAK,GAAI,QAAO;AACpB,UAAI,GAAG,GAAGO,KAAI,MAAM;AACpB,UAAIA,KAAI,EAAQ,KAAIH,OAAML,MAAKK,QAAO,KAAK,OAAOC,QAAOL,MAAKK,MAAK,IAAI,KAAK;AAAA,eACnEE,KAAI,EAAQ,KAAIH,OAAML,MAAKK,QAAO,KAAK,OAAOC,QAAOL,MAAKK,MAAK,IAAI,KAAK;AAAA,eACxEE,KAAI,EAAQ,KAAIF,OAAML,MAAKK,QAAO,KAAK,OAAOD,QAAOL,MAAKK,MAAK,IAAI,KAAK;AAAA,UAC5E,KAAIC,OAAML,MAAKK,QAAO,KAAK,OAAOD,QAAOL,MAAKK,MAAK,IAAI,KAAK;AACjE,UAAI,GAAI,CAAAA,MAAK,GAAGC,MAAK,GAAG,KAAK,KAAK,YAAYD,KAAIC,GAAE;AAAA,UAC/C,CAAAN,MAAK,GAAGC,MAAK,GAAG,KAAK,KAAK,YAAYD,KAAIC,GAAE;AAAA,IACnD;AAAA,EACF;AAAA,EACA,cAAc,GAAG,QAAQ,KAAK,KAAK,KAAK,KAAK;AAC3C,QAAI,IAAI,MAAM,KAAK,MAAM,GAAG;AAC5B,QAAI,IAAI,KAAK,SAAS,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,GAAG,EAAG,GAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACjE,QAAI,IAAI,KAAK,SAAS,EAAE,EAAE,SAAS,CAAC,GAAG,EAAE,EAAE,SAAS,CAAC,GAAG,KAAK,GAAG,EAAG,GAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACpF,QAAI,IAAI,KAAK,YAAY,GAAG,CAAC,GAAG;AAC9B,eAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAI,KAAK,KAAK,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,GAAG;AACxF,aAAK,IAAI,KAAK,KAAK,UAAU,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAC3C,YAAI,MAAM,GAAI,KAAI,KAAK,MAAM,GAAG,IAAI,IAAI,GAAG,CAAC,GAAG,IAAI,EAAE;AAAA,MACvD;AAAA,IACF,WAAW,KAAK,SAAS,IAAI,KAAK,OAAO,KAAK,QAAQ,IAAI,KAAK,OAAO,KAAK,QAAQ,CAAC,GAAG;AACrF,UAAI,CAAC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;AAAA,IAC7F;AACA,WAAO;AAAA,EACT;AAAA,EACA,MAAM,GAAG,IAAI,IAAI,GAAG,GAAG;AACrB,WAAO,OAAO,IAAI;AAChB,UAAI,GAAG;AACP,cAAQ,IAAI;AAAA,QACV,KAAK;AAAQ,eAAK;AAAQ;AAAA,QAC1B,KAAK;AAAQ,eAAK,GAAQ,IAAI,KAAK,MAAM,IAAI,KAAK;AAAM;AAAA,QACxD,KAAK;AAAQ,eAAK;AAAQ;AAAA,QAC1B,KAAK;AAAQ,eAAK,IAAQ,IAAI,KAAK,MAAM,IAAI,KAAK;AAAM;AAAA,QACxD,KAAK;AAAQ,eAAK;AAAQ;AAAA,QAC1B,KAAK;AAAQ,eAAK,GAAQ,IAAI,KAAK,MAAM,IAAI,KAAK;AAAM;AAAA,QACxD,KAAK;AAAQ,eAAK;AAAQ;AAAA,QAC1B,KAAK;AAAQ,eAAK,GAAQ,IAAI,KAAK,MAAM,IAAI,KAAK;AAAM;AAAA,MAC1D;AAGA,WAAK,EAAE,CAAC,MAAM,KAAK,EAAE,IAAI,CAAC,MAAM,MAAM,KAAK,SAAS,GAAG,GAAG,CAAC,GAAG;AAC5D,UAAE,OAAO,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK;AAAA,MAC7B;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,SAASI,KAAIC,KAAI,IAAI,IAAI;AACvB,QAAI,IAAI,UAAUE,IAAG,GAAG;AACxB,QAAI,KAAK,GAAG;AACV,UAAIF,OAAM,KAAK,KAAM,QAAO;AAC5B,WAAKE,MAAK,KAAK,OAAOF,OAAM,MAAM,EAAG,KAAI,KAAK,MAAM,IAAID,OAAM,IAAIG,MAAK;AAAA,IACzE,WAAW,KAAK,GAAG;AACjB,UAAIF,OAAM,KAAK,KAAM,QAAO;AAC5B,WAAKE,MAAK,KAAK,OAAOF,OAAM,MAAM,EAAG,KAAI,KAAK,MAAM,IAAID,OAAM,IAAIG,MAAK;AAAA,IACzE;AACA,QAAI,KAAK,GAAG;AACV,UAAIH,OAAM,KAAK,KAAM,QAAO;AAC5B,WAAKG,MAAK,KAAK,OAAOH,OAAM,MAAM,EAAG,KAAI,KAAK,MAAM,IAAIC,OAAM,IAAIE,MAAK;AAAA,IACzE,WAAW,KAAK,GAAG;AACjB,UAAIH,OAAM,KAAK,KAAM,QAAO;AAC5B,WAAKG,MAAK,KAAK,OAAOH,OAAM,MAAM,EAAG,KAAI,KAAK,MAAM,IAAIC,OAAM,IAAIE,MAAK;AAAA,IACzE;AACA,WAAO,CAAC,GAAG,CAAC;AAAA,EACd;AAAA,EACA,UAAU,GAAG,GAAG;AACd,YAAQ,MAAM,KAAK,OAAO,IACpB,MAAM,KAAK,OAAO,IAAS,MAC1B,MAAM,KAAK,OAAO,IACnB,MAAM,KAAK,OAAO,IAAS;AAAA,EACnC;AAAA,EACA,YAAY,GAAG,GAAG;AAChB,YAAQ,IAAI,KAAK,OAAO,IAClB,IAAI,KAAK,OAAO,IAAS,MACxB,IAAI,KAAK,OAAO,IACjB,IAAI,KAAK,OAAO,IAAS;AAAA,EACjC;AAAA,EACA,UAAU,GAAG;AACX,QAAI,KAAK,EAAE,SAAS,GAAG;AACrB,eAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAI,GAAG;AACnC,cAAM,KAAK,IAAI,KAAK,EAAE,QAAQ,KAAK,IAAI,KAAK,EAAE;AAC9C,YAAI,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG;AACpF,YAAE,OAAO,GAAG,CAAC,GAAG,KAAK;AAAA,QACvB;AAAA,MACF;AACA,UAAI,CAAC,EAAE,OAAQ,KAAI;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AACF;;;AC3UO,IAAMC,WAAU;AAChB,IAAM,WAAW;AACjB,IAAM,kBAAkB,IAAI,IAAIA,YAAWA;AAG3C,SAAS,IAAI,MAAM,GAAG,MAAM,GAAG,GAAG;AACrC,MAAI,GAAG,MAAM,IAAI;AACjB,MAAI,OAAO,EAAE,CAAC;AACd,MAAI,OAAO,EAAE,CAAC;AACd,MAAI,SAAS;AACb,MAAI,SAAS;AACb,MAAK,OAAO,SAAW,OAAO,CAAC,MAAO;AAClC,QAAI;AACJ,WAAO,EAAE,EAAE,MAAM;AAAA,EACrB,OAAO;AACH,QAAI;AACJ,WAAO,EAAE,EAAE,MAAM;AAAA,EACrB;AACA,MAAI,SAAS;AACb,MAAI,SAAS,QAAQ,SAAS,MAAM;AAChC,QAAK,OAAO,SAAW,OAAO,CAAC,MAAO;AAClC,aAAO,OAAO;AACd,WAAK,KAAK,OAAO;AACjB,aAAO,EAAE,EAAE,MAAM;AAAA,IACrB,OAAO;AACH,aAAO,OAAO;AACd,WAAK,KAAK,OAAO;AACjB,aAAO,EAAE,EAAE,MAAM;AAAA,IACrB;AACA,QAAI;AACJ,QAAI,OAAO,GAAG;AACV,QAAE,QAAQ,IAAI;AAAA,IAClB;AACA,WAAO,SAAS,QAAQ,SAAS,MAAM;AACnC,UAAK,OAAO,SAAW,OAAO,CAAC,MAAO;AAClC,eAAO,IAAI;AACX,gBAAQ,OAAO;AACf,aAAK,KAAK,OAAO,UAAU,OAAO;AAClC,eAAO,EAAE,EAAE,MAAM;AAAA,MACrB,OAAO;AACH,eAAO,IAAI;AACX,gBAAQ,OAAO;AACf,aAAK,KAAK,OAAO,UAAU,OAAO;AAClC,eAAO,EAAE,EAAE,MAAM;AAAA,MACrB;AACA,UAAI;AACJ,UAAI,OAAO,GAAG;AACV,UAAE,QAAQ,IAAI;AAAA,MAClB;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,SAAS,MAAM;AAClB,WAAO,IAAI;AACX,YAAQ,OAAO;AACf,SAAK,KAAK,OAAO,UAAU,OAAO;AAClC,WAAO,EAAE,EAAE,MAAM;AACjB,QAAI;AACJ,QAAI,OAAO,GAAG;AACV,QAAE,QAAQ,IAAI;AAAA,IAClB;AAAA,EACJ;AACA,SAAO,SAAS,MAAM;AAClB,WAAO,IAAI;AACX,YAAQ,OAAO;AACf,SAAK,KAAK,OAAO,UAAU,OAAO;AAClC,WAAO,EAAE,EAAE,MAAM;AACjB,QAAI;AACJ,QAAI,OAAO,GAAG;AACV,QAAE,QAAQ,IAAI;AAAA,IAClB;AAAA,EACJ;AACA,MAAI,MAAM,KAAK,WAAW,GAAG;AACzB,MAAE,QAAQ,IAAI;AAAA,EAClB;AACA,SAAO;AACX;AAsDO,SAAS,SAAS,MAAM,GAAG;AAC9B,MAAI,IAAI,EAAE,CAAC;AACX,WAAS,IAAI,GAAG,IAAI,MAAM,IAAK,MAAK,EAAE,CAAC;AACvC,SAAO;AACX;AAEO,SAAS,IAAI,GAAG;AACnB,SAAO,IAAI,aAAa,CAAC;AAC7B;;;ACvIA,IAAM,gBAAgB,IAAI,KAAKC,YAAWA;AAC1C,IAAM,gBAAgB,IAAI,KAAKA,YAAWA;AAC1C,IAAM,gBAAgB,IAAI,KAAKA,YAAWA,WAAUA;AAEpD,IAAM,IAAI,IAAI,CAAC;AACf,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,EAAE;AACjB,IAAM,IAAI,IAAI,EAAE;AAChB,IAAM,IAAI,IAAI,CAAC;AAEf,SAAS,cAAc,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,QAAQ;AACnD,MAAI,SAAS,SAAS,SAAS;AAC/B,MAAI,OAAOC,IAAG,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAIC;AAE9D,QAAM,MAAM,KAAK;AACjB,QAAM,MAAM,KAAK;AACjB,QAAM,MAAM,KAAK;AACjB,QAAM,MAAM,KAAK;AAEjB,OAAK,MAAM;AACX,EAAAD,KAAI,WAAW;AACf,QAAMA,MAAKA,KAAI;AACf,QAAM,MAAM;AACZ,EAAAA,KAAI,WAAW;AACf,QAAMA,MAAKA,KAAI;AACf,QAAM,MAAM;AACZ,OAAK,MAAM,OAAO,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AACrD,OAAK,MAAM;AACX,EAAAA,KAAI,WAAW;AACf,QAAMA,MAAKA,KAAI;AACf,QAAM,MAAM;AACZ,EAAAA,KAAI,WAAW;AACf,QAAMA,MAAKA,KAAI;AACf,QAAM,MAAM;AACZ,OAAK,MAAM,OAAO,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AACrD,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,IAAE,CAAC,IAAI,MAAM,KAAK,UAAU,QAAQ;AACpC,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,OAAK,MAAM,KAAK,UAAU,KAAK;AAC/B,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,IAAE,CAAC,IAAI,MAAM,KAAK,UAAU,QAAQ;AACpC,EAAAC,MAAK,KAAK;AACV,UAAQA,MAAK;AACb,IAAE,CAAC,IAAI,MAAMA,MAAK,UAAU,KAAK;AACjC,IAAE,CAAC,IAAIA;AAEP,MAAI,MAAM,SAAS,GAAG,CAAC;AACvB,MAAI,WAAW,eAAe;AAC9B,MAAI,OAAO,YAAY,CAAC,OAAO,UAAU;AACrC,WAAO;AAAA,EACX;AAEA,UAAQ,KAAK;AACb,YAAU,MAAM,MAAM,UAAU,QAAQ;AACxC,UAAQ,KAAK;AACb,YAAU,MAAM,MAAM,UAAU,QAAQ;AACxC,UAAQ,KAAK;AACb,YAAU,MAAM,MAAM,UAAU,QAAQ;AACxC,UAAQ,KAAK;AACb,YAAU,MAAM,MAAM,UAAU,QAAQ;AAExC,MAAI,YAAY,KAAK,YAAY,KAAK,YAAY,KAAK,YAAY,GAAG;AAClE,WAAO;AAAA,EACX;AAEA,aAAW,eAAe,SAAS,iBAAiB,KAAK,IAAI,GAAG;AAChE,SAAQ,MAAM,UAAU,MAAM,WAAY,MAAM,UAAU,MAAM;AAChE,MAAI,OAAO,YAAY,CAAC,OAAO,SAAU,QAAO;AAEhD,OAAK,UAAU;AACf,EAAAD,KAAI,WAAW;AACf,QAAMA,MAAKA,KAAI;AACf,QAAM,UAAU;AAChB,EAAAA,KAAI,WAAW;AACf,QAAMA,MAAKA,KAAI;AACf,QAAM,MAAM;AACZ,OAAK,MAAM,OAAO,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AACrD,OAAK,UAAU;AACf,EAAAA,KAAI,WAAW;AACf,QAAMA,MAAKA,KAAI;AACf,QAAM,UAAU;AAChB,EAAAA,KAAI,WAAW;AACf,QAAMA,MAAKA,KAAI;AACf,QAAM,MAAM;AACZ,OAAK,MAAM,OAAO,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AACrD,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,IAAE,CAAC,IAAI,MAAM,KAAK,UAAU,QAAQ;AACpC,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,OAAK,MAAM,KAAK,UAAU,KAAK;AAC/B,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,IAAE,CAAC,IAAI,MAAM,KAAK,UAAU,QAAQ;AACpC,EAAAC,MAAK,KAAK;AACV,UAAQA,MAAK;AACb,IAAE,CAAC,IAAI,MAAMA,MAAK,UAAU,KAAK;AACjC,IAAE,CAAC,IAAIA;AACP,QAAM,QAAQ,IAAI,GAAG,GAAG,GAAG,GAAG,EAAE;AAEhC,OAAK,MAAM;AACX,EAAAD,KAAI,WAAW;AACf,QAAMA,MAAKA,KAAI;AACf,QAAM,MAAM;AACZ,EAAAA,KAAI,WAAW;AACf,QAAMA,MAAKA,KAAI;AACf,QAAM,UAAU;AAChB,OAAK,MAAM,OAAO,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AACrD,OAAK,MAAM;AACX,EAAAA,KAAI,WAAW;AACf,QAAMA,MAAKA,KAAI;AACf,QAAM,MAAM;AACZ,EAAAA,KAAI,WAAW;AACf,QAAMA,MAAKA,KAAI;AACf,QAAM,UAAU;AAChB,OAAK,MAAM,OAAO,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AACrD,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,IAAE,CAAC,IAAI,MAAM,KAAK,UAAU,QAAQ;AACpC,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,OAAK,MAAM,KAAK,UAAU,KAAK;AAC/B,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,IAAE,CAAC,IAAI,MAAM,KAAK,UAAU,QAAQ;AACpC,EAAAC,MAAK,KAAK;AACV,UAAQA,MAAK;AACb,IAAE,CAAC,IAAI,MAAMA,MAAK,UAAU,KAAK;AACjC,IAAE,CAAC,IAAIA;AACP,QAAM,QAAQ,IAAI,OAAO,IAAI,GAAG,GAAG,EAAE;AAErC,OAAK,UAAU;AACf,EAAAD,KAAI,WAAW;AACf,QAAMA,MAAKA,KAAI;AACf,QAAM,UAAU;AAChB,EAAAA,KAAI,WAAW;AACf,QAAMA,MAAKA,KAAI;AACf,QAAM,UAAU;AAChB,OAAK,MAAM,OAAO,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AACrD,OAAK,UAAU;AACf,EAAAA,KAAI,WAAW;AACf,QAAMA,MAAKA,KAAI;AACf,QAAM,UAAU;AAChB,EAAAA,KAAI,WAAW;AACf,QAAMA,MAAKA,KAAI;AACf,QAAM,UAAU;AAChB,OAAK,MAAM,OAAO,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AACrD,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,IAAE,CAAC,IAAI,MAAM,KAAK,UAAU,QAAQ;AACpC,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,OAAK,MAAM,KAAK,UAAU,KAAK;AAC/B,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,IAAE,CAAC,IAAI,MAAM,KAAK,UAAU,QAAQ;AACpC,EAAAC,MAAK,KAAK;AACV,UAAQA,MAAK;AACb,IAAE,CAAC,IAAI,MAAMA,MAAK,UAAU,KAAK;AACjC,IAAE,CAAC,IAAIA;AACP,QAAM,OAAO,IAAI,OAAO,IAAI,GAAG,GAAG,CAAC;AAEnC,SAAO,EAAE,OAAO,CAAC;AACrB;AAEO,SAAS,SAAS,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC7C,QAAM,WAAW,KAAK,OAAO,KAAK;AAClC,QAAM,YAAY,KAAK,OAAO,KAAK;AACnC,QAAM,MAAM,UAAU;AAEtB,QAAM,SAAS,KAAK,IAAI,UAAU,QAAQ;AAC1C,MAAI,KAAK,IAAI,GAAG,KAAK,eAAe,OAAQ,QAAO;AAEnD,SAAO,CAAC,cAAc,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM;AACxD;;;ACjLA,IAAM,gBAAgB,IAAI,KAAKC,YAAWA;AAC1C,IAAM,gBAAgB,IAAI,KAAKA,YAAWA;AAC1C,IAAM,gBAAgB,KAAK,MAAMA,YAAWA,WAAUA;AAEtD,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,OAAO,IAAI,CAAC;AAClB,IAAM,OAAO,IAAI,CAAC;AAClB,IAAM,OAAO,IAAI,CAAC;AAClB,IAAM,OAAO,IAAI,CAAC;AAClB,IAAM,OAAO,IAAI,CAAC;AAClB,IAAM,OAAO,IAAI,CAAC;AAClB,IAAM,MAAM,IAAI,CAAC;AACjB,IAAM,MAAM,IAAI,CAAC;AACjB,IAAM,MAAM,IAAI,CAAC;AACjB,IAAMC,KAAI,IAAI,CAAC;AAEf,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,MAAM,IAAI,CAAC;AACjB,IAAM,MAAM,IAAI,CAAC;AACjB,IAAM,MAAM,IAAI,EAAE;AAElB,IAAI,MAAM,IAAI,GAAG;AACjB,IAAI,OAAO,IAAI,GAAG;;;ACxBlB,IAAM,gBAAgB,KAAK,KAAKC,YAAWA;AAC3C,IAAM,gBAAgB,IAAI,KAAKA,YAAWA;AAC1C,IAAM,gBAAgB,KAAK,MAAMA,YAAWA,WAAUA;AAEtD,IAAMC,MAAK,IAAI,CAAC;AAChB,IAAMC,MAAK,IAAI,CAAC;AAChB,IAAMC,MAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAMC,KAAI,IAAI,CAAC;AACf,IAAM,IAAI,IAAI,CAAC;AACf,IAAM,QAAQ,IAAI,CAAC;AACnB,IAAM,QAAQ,IAAI,CAAC;AACnB,IAAM,QAAQ,IAAI,CAAC;AACnB,IAAM,QAAQ,IAAI,CAAC;AACnB,IAAM,QAAQ,IAAI,CAAC;AACnB,IAAM,QAAQ,IAAI,CAAC;AACnB,IAAMC,OAAM,IAAI,CAAC;AACjB,IAAMC,OAAM,IAAI,CAAC;AACjB,IAAMC,OAAM,IAAI,CAAC;AACjB,IAAM,OAAO,IAAI,CAAC;AAClB,IAAM,OAAO,IAAI,CAAC;AAClB,IAAM,OAAO,IAAI,CAAC;AAElB,IAAMC,MAAK,IAAI,CAAC;AAChB,IAAMC,OAAM,IAAI,EAAE;AAClB,IAAM,OAAO,IAAI,EAAE;AACnB,IAAM,OAAO,IAAI,EAAE;AACnB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,OAAO,IAAI,EAAE;AACnB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAElB,IAAIC,OAAM,IAAI,IAAI;AAClB,IAAIC,QAAO,IAAI,IAAI;;;ACnCnB,IAAM,gBAAgB,KAAK,MAAMC,YAAWA;AAC5C,IAAM,gBAAgB,IAAI,KAAKA,YAAWA;AAC1C,IAAM,gBAAgB,KAAK,OAAOA,YAAWA,WAAUA;AAEvD,IAAMC,MAAK,IAAI,CAAC;AAChB,IAAMC,MAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAEhB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAElB,IAAM,OAAO,IAAI,IAAI;AACrB,IAAM,OAAO,IAAI,IAAI;AACrB,IAAM,OAAO,IAAI,IAAI;AACrB,IAAM,OAAO,IAAI,IAAI;AACrB,IAAM,OAAO,IAAI,IAAI;AACrB,IAAM,QAAQ,IAAI,IAAI;AACtB,IAAM,QAAQ,IAAI,IAAI;AACtB,IAAM,SAAS,IAAI,IAAI;AACvB,IAAM,QAAQ,IAAI,IAAI;AAEtB,IAAMC,MAAK,IAAI,CAAC;AAChB,IAAMC,OAAM,IAAI,CAAC;AACjB,IAAM,MAAM,IAAI,CAAC;AACjB,IAAMC,OAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAMC,OAAM,IAAI,EAAE;AAClB,IAAM,OAAO,IAAI,EAAE;AACnB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,OAAO,IAAI,GAAG;AACpB,IAAM,QAAQ,IAAI,GAAG;AACrB,IAAM,QAAQ,IAAI,GAAG;AACrB,IAAM,QAAQ,IAAI,GAAG;AACrB,IAAM,OAAO,IAAI,GAAG;AAgVpB,IAAM,OAAO,IAAI,EAAE;AACnB,IAAM,OAAO,IAAI,EAAE;AACnB,IAAM,OAAO,IAAI,EAAE;AACnB,IAAMC,OAAM,IAAI,IAAI;;;ACpYpB,IAAM,UAAU,KAAK,IAAI,GAAG,GAAG;AAC/B,IAAM,aAAa,IAAI,YAAY,GAAG;AAItC,IAAqB,aAArB,MAAqB,YAAW;AAAA,EAE5B,OAAO,KAAK,QAAQ,OAAO,aAAa,OAAO,aAAa;AACxD,UAAM,IAAI,OAAO;AACjB,UAAM,SAAS,IAAI,aAAa,IAAI,CAAC;AAErC,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,YAAM,IAAI,OAAO,CAAC;AAClB,aAAO,IAAI,CAAC,IAAI,KAAK,CAAC;AACtB,aAAO,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC;AAAA,IAC9B;AAEA,WAAO,IAAI,YAAW,MAAM;AAAA,EAChC;AAAA,EAEA,YAAY,QAAQ;AAChB,UAAM,IAAI,OAAO,UAAU;AAC3B,QAAI,IAAI,KAAK,OAAO,OAAO,CAAC,MAAM,SAAU,OAAM,IAAI,MAAM,qCAAqC;AAEjG,SAAK,SAAS;AAGd,UAAM,eAAe,KAAK,IAAI,IAAI,IAAI,GAAG,CAAC;AAC1C,SAAK,aAAa,IAAI,YAAY,eAAe,CAAC;AAClD,SAAK,aAAa,IAAI,WAAW,eAAe,CAAC;AAGjD,SAAK,YAAY,KAAK,KAAK,KAAK,KAAK,CAAC,CAAC;AACvC,SAAK,YAAY,IAAI,YAAY,CAAC;AAClC,SAAK,YAAY,IAAI,YAAY,CAAC;AAClC,SAAK,WAAW,IAAI,YAAY,CAAC;AACjC,SAAK,YAAY,IAAI,WAAW,KAAK,SAAS;AAG9C,SAAK,OAAO,IAAI,YAAY,CAAC;AAC7B,SAAK,SAAS,IAAI,aAAa,CAAC;AAEhC,SAAK,OAAO;AAAA,EAChB;AAAA,EAEA,SAAS;AACL,UAAM,EAAC,QAAQ,WAAW,UAAU,WAAW,UAAU,UAAU,SAAS,WAAW,SAAQ,IAAK;AACpG,UAAM,IAAI,OAAO,UAAU;AAG3B,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QAAI,OAAO;AAEX,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,YAAM,IAAI,OAAO,IAAI,CAAC;AACtB,YAAM,IAAI,OAAO,IAAI,IAAI,CAAC;AAC1B,UAAI,IAAI,KAAM,QAAO;AACrB,UAAI,IAAI,KAAM,QAAO;AACrB,UAAI,IAAI,KAAM,QAAO;AACrB,UAAI,IAAI,KAAM,QAAO;AACrB,WAAK,KAAK,CAAC,IAAI;AAAA,IACnB;AACA,UAAM,MAAM,OAAO,QAAQ;AAC3B,UAAM,MAAM,OAAO,QAAQ;AAE3B,QAAI,IAAI,IAAI;AAGZ,aAAS,IAAI,GAAG,UAAU,UAAU,IAAI,GAAG,KAAK;AAC5C,YAAM,IAAI,KAAK,IAAI,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC;AACvD,UAAI,IAAI,SAAS;AACb,aAAK;AACL,kBAAU;AAAA,MACd;AAAA,IACJ;AACA,UAAM,MAAM,OAAO,IAAI,EAAE;AACzB,UAAM,MAAM,OAAO,IAAI,KAAK,CAAC;AAG7B,aAAS,IAAI,GAAG,UAAU,UAAU,IAAI,GAAG,KAAK;AAC5C,UAAI,MAAM,GAAI;AACd,YAAM,IAAI,KAAK,KAAK,KAAK,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC;AACzD,UAAI,IAAI,WAAW,IAAI,GAAG;AACtB,aAAK;AACL,kBAAU;AAAA,MACd;AAAA,IACJ;AACA,QAAI,MAAM,OAAO,IAAI,EAAE;AACvB,QAAI,MAAM,OAAO,IAAI,KAAK,CAAC;AAE3B,QAAI,YAAY;AAGhB,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,UAAI,MAAM,MAAM,MAAM,GAAI;AAC1B,YAAM,IAAI,aAAa,KAAK,KAAK,KAAK,KAAK,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC;AAC3E,UAAI,IAAI,WAAW;AACf,aAAK;AACL,oBAAY;AAAA,MAChB;AAAA,IACJ;AACA,QAAI,MAAM,OAAO,IAAI,EAAE;AACvB,QAAI,MAAM,OAAO,IAAI,KAAK,CAAC;AAE3B,QAAI,cAAc,UAAU;AAGxB,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,aAAK,OAAO,CAAC,IAAK,OAAO,IAAI,CAAC,IAAI,OAAO,CAAC,KAAO,OAAO,IAAI,IAAI,CAAC,IAAI,OAAO,CAAC;AAAA,MACjF;AACA,gBAAU,KAAK,MAAM,KAAK,QAAQ,GAAG,IAAI,CAAC;AAC1C,YAAM,OAAO,IAAI,YAAY,CAAC;AAC9B,UAAI,IAAI;AACR,eAAS,IAAI,GAAG,KAAK,WAAW,IAAI,GAAG,KAAK;AACxC,cAAM,KAAK,KAAK,KAAK,CAAC;AACtB,cAAM,IAAI,KAAK,OAAO,EAAE;AACxB,YAAI,IAAI,IAAI;AACR,eAAK,GAAG,IAAI;AACZ,eAAK;AAAA,QACT;AAAA,MACJ;AACA,WAAK,OAAO,KAAK,SAAS,GAAG,CAAC;AAC9B,WAAK,YAAY,IAAI,YAAY,CAAC;AAClC,WAAK,YAAY,IAAI,YAAY,CAAC;AAClC;AAAA,IACJ;AAGA,QAAI,SAAS,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,IAAI,GAAG;AAC5C,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,WAAK;AACL,YAAM;AACN,YAAM;AACN,WAAK;AACL,YAAM;AACN,YAAM;AAAA,IACV;AAEA,UAAM,SAAS,aAAa,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AACxD,SAAK,MAAM,OAAO;AAClB,SAAK,MAAM,OAAO;AAElB,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,WAAK,OAAO,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,CAAC;AAAA,IAC9E;AAGA,cAAU,KAAK,MAAM,KAAK,QAAQ,GAAG,IAAI,CAAC;AAG1C,SAAK,aAAa;AAClB,QAAI,WAAW;AAEf,aAAS,EAAE,IAAI,SAAS,EAAE,IAAI;AAC9B,aAAS,EAAE,IAAI,SAAS,EAAE,IAAI;AAC9B,aAAS,EAAE,IAAI,SAAS,EAAE,IAAI;AAE9B,YAAQ,EAAE,IAAI;AACd,YAAQ,EAAE,IAAI;AACd,YAAQ,EAAE,IAAI;AAEd,aAAS,KAAK,EAAE;AAChB,aAAS,KAAK,SAAS,KAAK,GAAG,CAAC,IAAI;AACpC,aAAS,KAAK,SAAS,KAAK,GAAG,CAAC,IAAI;AACpC,aAAS,KAAK,SAAS,KAAK,GAAG,CAAC,IAAI;AAEpC,SAAK,eAAe;AACpB,SAAK,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAExC,aAAS,IAAI,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,QAAQ,KAAK;AAC/C,YAAM,IAAI,KAAK,KAAK,CAAC;AACrB,YAAM,IAAI,OAAO,IAAI,CAAC;AACtB,YAAM,IAAI,OAAO,IAAI,IAAI,CAAC;AAG1B,UAAI,IAAI,KAAK,KAAK,IAAI,IAAI,EAAE,KAAK,WAAW,KAAK,IAAI,IAAI,EAAE,KAAK,QAAS;AACzE,WAAK;AACL,WAAK;AAGL,UAAI,MAAM,MAAM,MAAM,MAAM,MAAM,GAAI;AAGtC,UAAI,QAAQ;AACZ,eAAS,IAAI,GAAG,MAAM,KAAK,SAAS,GAAG,CAAC,GAAG,IAAI,KAAK,WAAW,KAAK;AAChE,gBAAQ,UAAU,MAAM,KAAK,KAAK,SAAS;AAC3C,YAAI,UAAU,MAAM,UAAU,SAAS,KAAK,EAAG;AAAA,MACnD;AAEA,cAAQ,SAAS,KAAK;AACtB,UAAI,IAAI,OAAO;AACf,aAAO,IAAI,SAAS,CAAC,GAAG,SAAS,GAAG,GAAG,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,GAAG,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC,KAAK,GAAG;AAC7G,YAAI;AACJ,YAAI,MAAM,OAAO;AACb,cAAI;AACJ;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,MAAM,GAAI;AAGd,UAAI,IAAI,KAAK,aAAa,GAAG,GAAG,SAAS,CAAC,GAAG,IAAI,IAAI,QAAQ,CAAC,CAAC;AAG/D,cAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC;AACjC,cAAQ,CAAC,IAAI;AACb;AAGA,UAAIC,KAAI,SAAS,CAAC;AAClB,aAAO,IAAI,SAASA,EAAC,GAAG,SAAS,GAAG,GAAG,OAAO,IAAIA,EAAC,GAAG,OAAO,IAAIA,KAAI,CAAC,GAAG,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC,IAAI,GAAG;AAC5G,YAAI,KAAK,aAAaA,IAAG,GAAG,GAAG,QAAQ,CAAC,GAAG,IAAI,QAAQA,EAAC,CAAC;AACzD,gBAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC;AACjC,iBAASA,EAAC,IAAIA;AACd;AACA,QAAAA,KAAI;AAAA,MACR;AAGA,UAAI,MAAM,OAAO;AACb,eAAO,IAAI,SAAS,CAAC,GAAG,SAAS,GAAG,GAAG,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,GAAG,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC,IAAI,GAAG;AAC5G,cAAI,KAAK,aAAa,GAAG,GAAG,GAAG,IAAI,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC;AACzD,eAAK,UAAU,IAAI,CAAC;AACpB,kBAAQ,CAAC,IAAI;AACb,mBAAS,CAAC,IAAI;AACd;AACA,cAAI;AAAA,QACR;AAAA,MACJ;AAGA,WAAK,aAAa,SAAS,CAAC,IAAI;AAChC,eAAS,CAAC,IAAI,SAASA,EAAC,IAAI;AAC5B,eAAS,CAAC,IAAIA;AAGd,eAAS,KAAK,SAAS,GAAG,CAAC,CAAC,IAAI;AAChC,eAAS,KAAK,SAAS,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI;AAAA,IAChE;AAEA,SAAK,OAAO,IAAI,YAAY,QAAQ;AACpC,aAAS,IAAI,GAAG,IAAI,KAAK,YAAY,IAAI,UAAU,KAAK;AACpD,WAAK,KAAK,CAAC,IAAI;AACf,UAAI,SAAS,CAAC;AAAA,IAClB;AAGA,SAAK,YAAY,KAAK,WAAW,SAAS,GAAG,KAAK,YAAY;AAC9D,SAAK,YAAY,KAAK,WAAW,SAAS,GAAG,KAAK,YAAY;AAAA,EAClE;AAAA,EAEA,SAAS,GAAG,GAAG;AACX,WAAO,KAAK,MAAM,YAAY,IAAI,KAAK,KAAK,IAAI,KAAK,GAAG,IAAI,KAAK,SAAS,IAAI,KAAK;AAAA,EACvF;AAAA,EAEA,UAAU,GAAG;AACT,UAAM,EAAC,YAAY,WAAW,YAAY,WAAW,OAAM,IAAI;AAE/D,QAAI,IAAI;AACR,QAAI,KAAK;AAGT,WAAO,MAAM;AACT,YAAM,IAAI,UAAU,CAAC;AAiBrB,YAAM,KAAK,IAAI,IAAI;AACnB,WAAK,MAAM,IAAI,KAAK;AAEpB,UAAI,MAAM,IAAI;AACV,YAAI,MAAM,EAAG;AACb,YAAI,WAAW,EAAE,CAAC;AAClB;AAAA,MACJ;AAEA,YAAM,KAAK,IAAI,IAAI;AACnB,YAAM,KAAK,MAAM,IAAI,KAAK;AAC1B,YAAM,KAAK,MAAM,IAAI,KAAK;AAE1B,YAAMC,MAAK,UAAU,EAAE;AACvB,YAAM,KAAK,UAAU,CAAC;AACtB,YAAM,KAAK,UAAU,EAAE;AACvB,YAAM,KAAK,UAAU,EAAE;AAEvB,YAAM,UAAU;AAAA,QACZ,OAAO,IAAIA,GAAE;AAAA,QAAG,OAAO,IAAIA,MAAK,CAAC;AAAA,QACjC,OAAO,IAAI,EAAE;AAAA,QAAG,OAAO,IAAI,KAAK,CAAC;AAAA,QACjC,OAAO,IAAI,EAAE;AAAA,QAAG,OAAO,IAAI,KAAK,CAAC;AAAA,QACjC,OAAO,IAAI,EAAE;AAAA,QAAG,OAAO,IAAI,KAAK,CAAC;AAAA,MAAC;AAEtC,UAAI,SAAS;AACT,kBAAU,CAAC,IAAI;AACf,kBAAU,CAAC,IAAIA;AAEf,cAAM,MAAM,UAAU,EAAE;AAGxB,YAAI,QAAQ,IAAI;AACZ,cAAI,IAAI,KAAK;AACb,aAAG;AACC,gBAAI,KAAK,SAAS,CAAC,MAAM,IAAI;AACzB,mBAAK,SAAS,CAAC,IAAI;AACnB;AAAA,YACJ;AACA,gBAAI,KAAK,UAAU,CAAC;AAAA,UACxB,SAAS,MAAM,KAAK;AAAA,QACxB;AACA,aAAK,MAAM,GAAG,GAAG;AACjB,aAAK,MAAM,GAAG,UAAU,EAAE,CAAC;AAC3B,aAAK,MAAM,IAAI,EAAE;AAEjB,cAAM,KAAK,MAAM,IAAI,KAAK;AAG1B,YAAI,IAAI,WAAW,QAAQ;AACvB,qBAAW,GAAG,IAAI;AAAA,QACtB;AAAA,MACJ,OAAO;AACH,YAAI,MAAM,EAAG;AACb,YAAI,WAAW,EAAE,CAAC;AAAA,MACtB;AAAA,IACJ;AAEA,WAAO;AAAA,EACX;AAAA,EAEA,MAAM,GAAG,GAAG;AACR,SAAK,WAAW,CAAC,IAAI;AACrB,QAAI,MAAM,GAAI,MAAK,WAAW,CAAC,IAAI;AAAA,EACvC;AAAA;AAAA,EAGA,aAAa,IAAI,IAAI,IAAI,GAAG,GAAGC,IAAG;AAC9B,UAAM,IAAI,KAAK;AAEf,SAAK,WAAW,CAAC,IAAI;AACrB,SAAK,WAAW,IAAI,CAAC,IAAI;AACzB,SAAK,WAAW,IAAI,CAAC,IAAI;AAEzB,SAAK,MAAM,GAAG,CAAC;AACf,SAAK,MAAM,IAAI,GAAG,CAAC;AACnB,SAAK,MAAM,IAAI,GAAGA,EAAC;AAEnB,SAAK,gBAAgB;AAErB,WAAO;AAAA,EACX;AACJ;AAGA,SAAS,YAAY,IAAI,IAAI;AACzB,QAAM,IAAI,MAAM,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE;AAC1C,UAAQ,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK;AACtC;AAEA,SAAS,KAAK,IAAI,IAAI,IAAI,IAAI;AAC1B,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,SAAO,KAAK,KAAK,KAAK;AAC1B;AAEA,SAAS,SAAS,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC9C,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAEhB,QAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,QAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,QAAM,KAAK,KAAK,KAAK,KAAK;AAE1B,SAAO,MAAM,KAAK,KAAK,KAAK,MACrB,MAAM,KAAK,KAAK,KAAK,MACrB,MAAM,KAAK,KAAK,KAAK,MAAM;AACtC;AAEA,SAAS,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC1C,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAEhB,QAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,QAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,QAAM,IAAI,OAAO,KAAK,KAAK,KAAK;AAEhC,QAAM,KAAK,KAAK,KAAK,KAAK,MAAM;AAChC,QAAM,KAAK,KAAK,KAAK,KAAK,MAAM;AAEhC,SAAO,IAAI,IAAI,IAAI;AACvB;AAEA,SAAS,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC1C,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAEhB,QAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,QAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,QAAM,IAAI,OAAO,KAAK,KAAK,KAAK;AAEhC,QAAM,IAAI,MAAM,KAAK,KAAK,KAAK,MAAM;AACrC,QAAM,IAAI,MAAM,KAAK,KAAK,KAAK,MAAM;AAErC,SAAO,EAAC,GAAG,EAAC;AAChB;AAEA,SAAS,UAAU,KAAK,OAAO,MAAM,OAAO;AACxC,MAAI,QAAQ,QAAQ,IAAI;AACpB,aAAS,IAAI,OAAO,GAAG,KAAK,OAAO,KAAK;AACpC,YAAM,OAAO,IAAI,CAAC;AAClB,YAAM,WAAW,MAAM,IAAI;AAC3B,UAAI,IAAI,IAAI;AACZ,aAAO,KAAK,QAAQ,MAAM,IAAI,CAAC,CAAC,IAAI,SAAU,KAAI,IAAI,CAAC,IAAI,IAAI,GAAG;AAClE,UAAI,IAAI,CAAC,IAAI;AAAA,IACjB;AAAA,EACJ,OAAO;AACH,UAAM,SAAU,OAAO,SAAU;AACjC,QAAI,IAAI,OAAO;AACf,QAAI,IAAI;AACR,SAAK,KAAK,QAAQ,CAAC;AACnB,QAAI,MAAM,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,KAAK,CAAC,EAAG,MAAK,KAAK,MAAM,KAAK;AAC/D,QAAI,MAAM,IAAI,CAAC,CAAC,IAAI,MAAM,IAAI,KAAK,CAAC,EAAG,MAAK,KAAK,GAAG,KAAK;AACzD,QAAI,MAAM,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,CAAC,EAAG,MAAK,KAAK,MAAM,CAAC;AAEvD,UAAM,OAAO,IAAI,CAAC;AAClB,UAAM,WAAW,MAAM,IAAI;AAC3B,WAAO,MAAM;AACT;AAAG;AAAA,aAAY,MAAM,IAAI,CAAC,CAAC,IAAI;AAC/B;AAAG;AAAA,aAAY,MAAM,IAAI,CAAC,CAAC,IAAI;AAC/B,UAAI,IAAI,EAAG;AACX,WAAK,KAAK,GAAG,CAAC;AAAA,IAClB;AACA,QAAI,OAAO,CAAC,IAAI,IAAI,CAAC;AACrB,QAAI,CAAC,IAAI;AAET,QAAI,QAAQ,IAAI,KAAK,IAAI,MAAM;AAC3B,gBAAU,KAAK,OAAO,GAAG,KAAK;AAC9B,gBAAU,KAAK,OAAO,MAAM,IAAI,CAAC;AAAA,IACrC,OAAO;AACH,gBAAU,KAAK,OAAO,MAAM,IAAI,CAAC;AACjC,gBAAU,KAAK,OAAO,GAAG,KAAK;AAAA,IAClC;AAAA,EACJ;AACJ;AAEA,SAAS,KAAK,KAAK,GAAG,GAAG;AACrB,QAAM,MAAM,IAAI,CAAC;AACjB,MAAI,CAAC,IAAI,IAAI,CAAC;AACd,MAAI,CAAC,IAAI;AACb;AAEA,SAAS,YAAY,GAAG;AACpB,SAAO,EAAE,CAAC;AACd;AACA,SAAS,YAAY,GAAG;AACpB,SAAO,EAAE,CAAC;AACd;;;AC1dA,IAAMC,OAAM,IAAI,KAAK;AAArB,IAAyBC,OAAM,KAAK;AAEpC,SAAS,OAAO,GAAG;AACjB,SAAO,EAAE,CAAC;AACZ;AAEA,SAAS,OAAO,GAAG;AACjB,SAAO,EAAE,CAAC;AACZ;AAGA,SAAS,UAAU,GAAG;AACpB,QAAM,EAAC,WAAW,OAAM,IAAI;AAC5B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK,GAAG;AAC5C,UAAM,IAAI,IAAI,UAAU,CAAC,GACnB,IAAI,IAAI,UAAU,IAAI,CAAC,GACvBC,KAAI,IAAI,UAAU,IAAI,CAAC,GACvB,SAAS,OAAOA,EAAC,IAAI,OAAO,CAAC,MAAM,OAAO,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,MACtD,OAAO,CAAC,IAAI,OAAO,CAAC,MAAM,OAAOA,KAAI,CAAC,IAAI,OAAO,IAAI,CAAC;AACrE,QAAI,QAAQ,MAAO,QAAO;AAAA,EAC5B;AACA,SAAO;AACT;AAEA,SAAS,OAAO,GAAG,GAAG,GAAG;AACvB,SAAO,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC;AAC1D;AAEA,IAAqB,WAArB,MAAqB,UAAS;AAAA,EAC5B,OAAO,KAAK,QAAQ,KAAK,QAAQ,KAAK,QAAQ,MAAM;AAClD,WAAO,IAAI,UAAS,YAAY,SAC1B,UAAU,QAAQ,IAAI,IAAI,IAAI,IAC9B,aAAa,KAAK,aAAa,QAAQ,IAAI,IAAI,IAAI,CAAC,CAAC;AAAA,EAC7D;AAAA,EACA,YAAY,QAAQ;AAClB,SAAK,cAAc,IAAI,WAAW,MAAM;AACxC,SAAK,UAAU,IAAI,WAAW,OAAO,SAAS,CAAC;AAC/C,SAAK,aAAa,IAAI,WAAW,OAAO,SAAS,CAAC;AAClD,SAAK,SAAS,KAAK,YAAY;AAC/B,SAAK,MAAM;AAAA,EACb;AAAA,EACA,SAAS;AACP,SAAK,YAAY,OAAO;AACxB,SAAK,MAAM;AACX,WAAO;AAAA,EACT;AAAA,EACA,QAAQ;AACN,UAAM,IAAI,KAAK,aAAa,SAAS,KAAK;AAG1C,QAAI,EAAE,QAAQ,EAAE,KAAK,SAAS,KAAK,UAAU,CAAC,GAAG;AAC/C,WAAK,YAAY,WAAW,KAAK,EAAC,QAAQ,OAAO,SAAO,EAAC,GAAG,CAAC,GAAE,MAAM,CAAC,EACnE,KAAK,CAAC,GAAG,MAAM,OAAO,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,KAAK,OAAO,IAAI,IAAI,CAAC,IAAI,OAAO,IAAI,IAAI,CAAC,CAAC;AACxF,YAAM,IAAI,KAAK,UAAU,CAAC,GAAG,IAAI,KAAK,UAAU,KAAK,UAAU,SAAS,CAAC,GACvE,SAAS,CAAE,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,GAAG,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,CAAE,GAC9E,IAAI,OAAO,KAAK,MAAM,OAAO,CAAC,IAAI,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,OAAO,CAAC,CAAC;AACpE,eAAS,IAAI,GAAG,IAAI,OAAO,SAAS,GAAG,IAAI,GAAG,EAAE,GAAG;AACjD,cAAM,IAAI,OAAO,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC;AACpD,eAAO,IAAI,CAAC,IAAI,EAAE,CAAC;AACnB,eAAO,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;AAAA,MACzB;AACA,WAAK,cAAc,IAAI,WAAW,MAAM;AAAA,IAC1C,OAAO;AACL,aAAO,KAAK;AAAA,IACd;AAEA,UAAM,YAAY,KAAK,YAAY,KAAK,YAAY;AACpD,UAAM,OAAO,KAAK,OAAO,KAAK,YAAY;AAC1C,UAAM,YAAY,KAAK,YAAY,KAAK,YAAY;AACpD,UAAM,UAAU,KAAK,QAAQ,KAAK,EAAE;AACpC,UAAM,YAAY,KAAK,WAAW,KAAK,EAAE;AAKzC,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,EAAE,GAAG;AAChD,YAAM,IAAI,UAAU,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC;AAC/C,UAAI,UAAU,CAAC,MAAM,MAAM,QAAQ,CAAC,MAAM,GAAI,SAAQ,CAAC,IAAI;AAAA,IAC7D;AACA,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC3C,gBAAU,KAAK,CAAC,CAAC,IAAI;AAAA,IACvB;AAGA,QAAI,KAAK,UAAU,KAAK,KAAK,SAAS,GAAG;AACvC,WAAK,YAAY,IAAI,WAAW,CAAC,EAAE,KAAK,EAAE;AAC1C,WAAK,YAAY,IAAI,WAAW,CAAC,EAAE,KAAK,EAAE;AAC1C,WAAK,UAAU,CAAC,IAAI,KAAK,CAAC;AAC1B,cAAQ,KAAK,CAAC,CAAC,IAAI;AACnB,UAAI,KAAK,WAAW,GAAG;AACrB,gBAAQ,KAAK,CAAC,CAAC,IAAI;AACnB,aAAK,UAAU,CAAC,IAAI,KAAK,CAAC;AAC1B,aAAK,UAAU,CAAC,IAAI,KAAK,CAAC;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ,QAAQ;AACd,WAAO,IAAI,QAAQ,MAAM,MAAM;AAAA,EACjC;AAAA,EACA,CAAC,UAAU,GAAG;AACZ,UAAM,EAAC,SAAS,MAAM,YAAY,WAAW,WAAW,WAAAC,WAAS,IAAI;AAGrE,QAAIA,YAAW;AACb,YAAM,IAAIA,WAAU,QAAQ,CAAC;AAC7B,UAAI,IAAI,EAAG,OAAMA,WAAU,IAAI,CAAC;AAChC,UAAI,IAAIA,WAAU,SAAS,EAAG,OAAMA,WAAU,IAAI,CAAC;AACnD;AAAA,IACF;AAEA,UAAM,KAAK,QAAQ,CAAC;AACpB,QAAI,OAAO,GAAI;AACf,QAAI,IAAI,IAAIC,MAAK;AACjB,OAAG;AACD,YAAMA,MAAK,UAAU,CAAC;AACtB,UAAI,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI;AAC9B,UAAI,UAAU,CAAC,MAAM,EAAG;AACxB,UAAI,UAAU,CAAC;AACf,UAAI,MAAM,IAAI;AACZ,cAAM,IAAI,MAAM,WAAW,CAAC,IAAI,KAAK,KAAK,MAAM;AAChD,YAAI,MAAMA,IAAI,OAAM;AACpB;AAAA,MACF;AAAA,IACF,SAAS,MAAM;AAAA,EACjB;AAAA,EACA,KAAK,GAAG,GAAG,IAAI,GAAG;AAChB,SAAK,IAAI,CAAC,GAAG,MAAM,OAAO,IAAI,CAAC,GAAG,MAAM,GAAI,QAAO;AACnD,UAAM,KAAK;AACX,QAAIF;AACJ,YAAQA,KAAI,KAAK,MAAM,GAAG,GAAG,CAAC,MAAM,KAAKA,OAAM,KAAKA,OAAM,GAAI,KAAIA;AAClE,WAAOA;AAAA,EACT;AAAA,EACA,MAAM,GAAG,GAAG,GAAG;AACb,UAAM,EAAC,SAAS,MAAM,YAAY,WAAW,WAAW,OAAM,IAAI;AAClE,QAAI,QAAQ,CAAC,MAAM,MAAM,CAAC,OAAO,OAAQ,SAAQ,IAAI,MAAM,OAAO,UAAU;AAC5E,QAAIA,KAAI;AACR,QAAI,KAAKD,KAAI,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,IAAIA,KAAI,IAAI,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC;AACjE,UAAM,KAAK,QAAQ,CAAC;AACpB,QAAI,IAAI;AACR,OAAG;AACD,UAAI,IAAI,UAAU,CAAC;AACnB,YAAM,KAAKA,KAAI,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,IAAIA,KAAI,IAAI,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC;AACnE,UAAI,KAAK,GAAI,MAAK,IAAIC,KAAI;AAC1B,UAAI,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI;AAC9B,UAAI,UAAU,CAAC,MAAM,EAAG;AACxB,UAAI,UAAU,CAAC;AACf,UAAI,MAAM,IAAI;AACZ,YAAI,MAAM,WAAW,CAAC,IAAI,KAAK,KAAK,MAAM;AAC1C,YAAI,MAAM,GAAG;AACX,cAAID,KAAI,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,IAAIA,KAAI,IAAI,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,GAAI,QAAO;AAAA,QAC7E;AACA;AAAA,MACF;AAAA,IACF,SAAS,MAAM;AACf,WAAOC;AAAA,EACT;AAAA,EACA,OAAO,SAAS;AACd,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,UAAM,EAAC,QAAQ,WAAW,UAAS,IAAI;AACvC,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,EAAE,GAAG;AAChD,YAAM,IAAI,UAAU,CAAC;AACrB,UAAI,IAAI,EAAG;AACX,YAAM,KAAK,UAAU,CAAC,IAAI;AAC1B,YAAM,KAAK,UAAU,CAAC,IAAI;AAC1B,cAAQ,OAAO,OAAO,EAAE,GAAG,OAAO,KAAK,CAAC,CAAC;AACzC,cAAQ,OAAO,OAAO,EAAE,GAAG,OAAO,KAAK,CAAC,CAAC;AAAA,IAC3C;AACA,SAAK,WAAW,OAAO;AACvB,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,aAAa,SAAS,GAAG;AACvB,QAAI,MAAM,WAAc,CAAC,WAAW,OAAO,QAAQ,WAAW,YAAa,KAAI,SAAS,UAAU;AAClG,QAAI,KAAK,SAAY,IAAI,CAAC;AAC1B,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,UAAM,EAAC,OAAM,IAAI;AACjB,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK,GAAG;AAChD,YAAM,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,IAAI,CAAC;AACrC,cAAQ,OAAO,IAAI,GAAG,CAAC;AACvB,cAAQ,IAAI,GAAG,GAAG,GAAG,GAAGF,IAAG;AAAA,IAC7B;AACA,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,WAAW,SAAS;AAClB,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,UAAM,EAAC,MAAM,OAAM,IAAI;AACvB,UAAM,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,KAAK;AAChC,YAAQ,OAAO,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC;AACvC,aAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,YAAMK,KAAI,IAAI,KAAK,CAAC;AACpB,cAAQ,OAAO,OAAOA,EAAC,GAAG,OAAOA,KAAI,CAAC,CAAC;AAAA,IACzC;AACA,YAAQ,UAAU;AAClB,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,cAAc;AACZ,UAAM,UAAU,IAAI;AACpB,SAAK,WAAW,OAAO;AACvB,WAAO,QAAQ,MAAM;AAAA,EACvB;AAAA,EACA,eAAe,GAAG,SAAS;AACzB,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,UAAM,EAAC,QAAQ,UAAS,IAAI;AAC5B,UAAM,KAAK,UAAU,KAAK,CAAC,IAAI;AAC/B,UAAM,KAAK,UAAU,IAAI,CAAC,IAAI;AAC9B,UAAM,KAAK,UAAU,IAAI,CAAC,IAAI;AAC9B,YAAQ,OAAO,OAAO,EAAE,GAAG,OAAO,KAAK,CAAC,CAAC;AACzC,YAAQ,OAAO,OAAO,EAAE,GAAG,OAAO,KAAK,CAAC,CAAC;AACzC,YAAQ,OAAO,OAAO,EAAE,GAAG,OAAO,KAAK,CAAC,CAAC;AACzC,YAAQ,UAAU;AAClB,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,CAAC,mBAAmB;AAClB,UAAM,EAAC,UAAS,IAAI;AACpB,aAAS,IAAI,GAAG,IAAI,UAAU,SAAS,GAAG,IAAI,GAAG,EAAE,GAAG;AACpD,YAAM,KAAK,gBAAgB,CAAC;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,gBAAgB,GAAG;AACjB,UAAM,UAAU,IAAI;AACpB,SAAK,eAAe,GAAG,OAAO;AAC9B,WAAO,QAAQ,MAAM;AAAA,EACvB;AACF;AAEA,SAAS,UAAU,QAAQ,IAAI,IAAI,MAAM;AACvC,QAAM,IAAI,OAAO;AACjB,QAAM,QAAQ,IAAI,aAAa,IAAI,CAAC;AACpC,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,UAAM,IAAI,OAAO,CAAC;AAClB,UAAM,IAAI,CAAC,IAAI,GAAG,KAAK,MAAM,GAAG,GAAG,MAAM;AACzC,UAAM,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK,MAAM,GAAG,GAAG,MAAM;AAAA,EAC/C;AACA,SAAO;AACT;AAEA,UAAU,aAAa,QAAQ,IAAI,IAAI,MAAM;AAC3C,MAAI,IAAI;AACR,aAAW,KAAK,QAAQ;AACtB,UAAM,GAAG,KAAK,MAAM,GAAG,GAAG,MAAM;AAChC,UAAM,GAAG,KAAK,MAAM,GAAG,GAAG,MAAM;AAChC,MAAE;AAAA,EACJ;AACF;;;ACvPe,SAAR,eAAiB,WAAW;AACjC,MAAI,IAAI,UAAU,SAAS,IAAI,GAAG,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;AAC7D,SAAO,IAAI,EAAG,QAAO,CAAC,IAAI,MAAM,UAAU,MAAM,IAAI,GAAG,EAAE,IAAI,CAAC;AAC9D,SAAO;AACT;;;ACFA,IAAO,qBAAQ,eAAO,8DAA8D;;;ACApF,IAAO,iBAAQ,eAAO,kDAAkD;;;ACAxE,IAAO,gBAAQ,eAAO,kDAAkD;;;ACAxE,IAAO,uBAAQ,eAAO,8DAA8D;;;ACApF,IAAO,iBAAQ,eAAO,0EAA0E;;;ACAhG,IAAO,kBAAQ,eAAO,wDAAwD;;;ACA9E,IAAO,kBAAQ,eAAO,kDAAkD;;;ACAxE,IAAO,eAAQ,eAAO,wDAAwD;;;ACA9E,IAAO,eAAQ,eAAO,kDAAkD;;;ACAxE,IAAO,eAAQ,eAAO,0EAA0E;;;ACAhG,IAAO,oBAAQ,eAAO,8DAA8D;;;ACApF,IAAO,eAAQ,CAAAC,aAAU,SAAoBA,SAAOA,SAAO,SAAS,CAAC,CAAC;;;ACC/D,IAAI,SAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,eAAQ,aAAK,MAAM;;;ACZnB,IAAIC,UAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,eAAQ,aAAKA,OAAM;;;ACZnB,IAAIC,UAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,eAAQ,aAAKA,OAAM;;;ACZnB,IAAIC,UAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,eAAQ,aAAKA,OAAM;;;ACZnB,IAAIC,UAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,eAAQ,aAAKA,OAAM;;;ACZnB,IAAIC,UAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,eAAQ,aAAKA,OAAM;;;ACZnB,IAAIC,UAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,iBAAQ,aAAKA,OAAM;;;ACZnB,IAAIC,UAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,iBAAQ,aAAKA,OAAM;;;ACZnB,IAAIC,UAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,mBAAQ,aAAKA,OAAM;;;ACZnB,IAAIC,WAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,eAAQ,aAAKA,QAAM;;;ACVnB,IAAIC,WAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,eAAQ,aAAKA,QAAM;;;ACVnB,IAAIC,WAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,eAAQ,aAAKA,QAAM;;;ACVnB,IAAIC,WAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,eAAQ,aAAKA,QAAM;;;ACVnB,IAAIC,WAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,iBAAQ,aAAKA,QAAM;;;ACVnB,IAAIC,WAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,eAAQ,aAAKA,QAAM;;;ACVnB,IAAIC,WAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,eAAQ,aAAKA,QAAM;;;ACVnB,IAAIC,WAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,eAAQ,aAAKA,QAAM;;;ACVnB,IAAIC,WAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,iBAAQ,aAAKA,QAAM;;;ACVnB,IAAIC,WAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,eAAQ,aAAKA,QAAM;;;ACVnB,IAAIC,WAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,iBAAQ,aAAKA,QAAM;;;ACVnB,IAAIC,WAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,iBAAQ,aAAKA,QAAM;;;ACVnB,IAAIC,WAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,gBAAQ,aAAKA,QAAM;;;ACVnB,IAAIC,WAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,iBAAQ,aAAKA,QAAM;;;ACVnB,IAAIC,WAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,gBAAQ,aAAKA,QAAM;;;ACVnB,IAAIC,WAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,kBAAQ,aAAKA,QAAM;;;ACVnB,IAAIC,WAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,eAAQ,aAAKA,QAAM;;;ACVnB,IAAIC,WAAS,IAAI,MAAM,CAAC,EAAE;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,IAAI,cAAM;AAEZ,IAAO,kBAAQ,aAAKA,QAAM;;;ACbX,SAAR,gBAAiB,GAAG;AACzB,MAAI,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;AAC9B,SAAO,SACD,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,QAAQ,KAAK,QAAQ,KAAK,UAAU,KAAK,SAAS,KAAK,UAAU,IAAI,WAAW,CAAC,CAAC,IAAI,OAC3H,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,QAAQ,KAAK,SAAS,KAAK,QAAQ,KAAK,SAAS,KAAK,SAAS,IAAI,SAAS,CAAC,CAAC,IAAI,OACvH,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,QAAQ,KAAK,SAAS,KAAK,UAAU,KAAK,UAAU,KAAK,UAAU,IAAI,WAAW,CAAC,CAAC,IACzH;AACR;;;ACJA,IAAO,oBAAQ,cAAyB,UAAU,KAAK,KAAK,CAAG,GAAG,UAAU,MAAM,KAAK,CAAG,CAAC;;;ACApF,IAAI,OAAO,cAAyB,UAAU,MAAM,MAAM,IAAI,GAAG,UAAU,IAAI,KAAM,GAAG,CAAC;AAEzF,IAAI,OAAO,cAAyB,UAAU,KAAK,MAAM,IAAI,GAAG,UAAU,IAAI,KAAM,GAAG,CAAC;AAE/F,IAAI,IAAI,UAAU;AAEH,SAAR,gBAAiB,GAAG;AACzB,MAAI,IAAI,KAAK,IAAI,EAAG,MAAK,KAAK,MAAM,CAAC;AACrC,MAAI,KAAK,KAAK,IAAI,IAAI,GAAG;AACzB,IAAE,IAAI,MAAM,IAAI;AAChB,IAAE,IAAI,MAAM,MAAM;AAClB,IAAE,IAAI,MAAM,MAAM;AAClB,SAAO,IAAI;AACb;;;ACdA,IAAIC,KAAI,IAAI;AAAZ,IACI,SAAS,KAAK,KAAK;AADvB,IAEI,SAAS,KAAK,KAAK,IAAI;AAEZ,SAAR,gBAAiB,GAAG;AACzB,MAAI;AACJ,OAAK,MAAM,KAAK,KAAK;AACrB,EAAAA,GAAE,IAAI,OAAO,IAAI,KAAK,IAAI,CAAC,KAAK;AAChC,EAAAA,GAAE,IAAI,OAAO,IAAI,KAAK,IAAI,IAAI,MAAM,KAAK;AACzC,EAAAA,GAAE,IAAI,OAAO,IAAI,KAAK,IAAI,IAAI,MAAM,KAAK;AACzC,SAAOA,KAAI;AACb;;;ACbe,SAAR,cAAiB,GAAG;AACzB,MAAI,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;AAC9B,SAAO,SACD,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,QAAQ,KAAK,UAAU,KAAK,WAAW,KAAK,WAAW,KAAK,WAAW,IAAI,YAAY,CAAC,CAAC,IAAI,OAClI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,QAAQ,KAAK,SAAS,KAAK,UAAU,KAAK,UAAU,KAAK,UAAU,IAAI,UAAU,CAAC,CAAC,IAAI,OAC5H,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,OAAO,KAAK,SAAS,KAAK,WAAW,KAAK,QAAQ,KAAK,WAAW,IAAI,WAAW,CAAC,CAAC,IACxH;AACR;;;ACLA,SAAS,KAAKC,QAAO;AACnB,MAAI,IAAIA,OAAM;AACd,SAAO,SAAS,GAAG;AACjB,WAAOA,OAAM,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC;AAAA,EAC9D;AACF;AAEA,IAAO,kBAAQ,KAAK,eAAO,kgDAAkgD,CAAC;AAEvhD,IAAI,QAAQ,KAAK,eAAO,kgDAAkgD,CAAC;AAE3hD,IAAI,UAAU,KAAK,eAAO,kgDAAkgD,CAAC;AAE7hD,IAAI,SAAS,KAAK,eAAO,kgDAAkgD,CAAC;", "names": ["object", "coordinates", "u", "v", "object", "cartesian", "spherical", "lambda0", "lambda00", "phi00", "range", "sign", "lambda1", "lambda00", "phi00", "v", "object", "coordinates", "c", "compareIntersection", "n", "p0", "angle", "sum", "lambda0", "phi0", "sinPhi0", "cosPhi0", "lambda1", "phi1", "sign", "point", "lambda0", "phi0", "lambda1", "phi1", "cosPhi0", "circle_default", "v", "c", "c2", "B", "u", "lambda0", "lambda1", "phi0", "phi1", "delta", "code", "x0", "y0", "x1", "y1", "x0", "y0", "x1", "y1", "compareIntersection", "ca", "ring", "point", "visible", "linePoint", "v", "x0", "y0", "x1", "y1", "lambda0", "sinPhi0", "cosPhi0", "object", "object", "coordinates", "ab", "y0", "y1", "y", "x0", "x1", "x", "X1", "X0", "Y1", "Y0", "graticule", "coordinates", "x0", "y0", "x1", "y1", "B", "areaSum", "areaRingSum", "x0", "y0", "areaStream", "areaRingStart", "areaRingEnd", "areaPointFirst", "areaPoint", "area_default", "x0", "y0", "boundsStream", "boundsPoint", "bounds_default", "X0", "Y0", "Z0", "X1", "Y1", "Z1", "X2", "Y2", "Z2", "x00", "y00", "x0", "y0", "centroidStream", "centroidPoint", "centroidLineStart", "centroidLineEnd", "centroidRingStart", "centroidRingEnd", "centroid_default", "lengthSum", "x00", "y00", "x0", "y0", "lengthStream", "lengthPointFirst", "lengthPoint", "append", "projection", "object", "area_default", "bounds_default", "centroid_default", "projection", "object", "bounds_default", "x0", "y0", "lambda0", "x1", "y1", "lambda1", "c", "lambda2", "lambda00", "x00", "y00", "linePoint", "x0", "y0", "x1", "y1", "projection", "circle_default", "object", "phi0", "phi1", "phi0", "cosPhi0", "y0", "y1", "c", "coordinates", "object", "scale", "angle", "c", "cc", "c", "scale", "x0", "y0", "x1", "y1", "y0", "y1", "y0", "y1", "identity_default", "ca", "x0", "y0", "x1", "y1", "projection", "object", "epsilon", "x0", "y0", "x1", "y1", "ab", "i", "p0", "x0", "y0", "v", "c", "epsilon", "epsilon", "c", "u3", "epsilon", "u", "epsilon", "bc", "ca", "ab", "u", "abt", "bct", "cat", "_8", "_16", "fin", "fin2", "epsilon", "ab", "bc", "_8", "_8b", "_16", "_48", "fin", "n", "p0", "c", "tau", "pow", "c", "collinear", "p0", "h", "scheme", "scheme", "scheme", "scheme", "scheme", "scheme", "scheme", "scheme", "scheme", "scheme", "scheme", "scheme", "scheme", "scheme", "scheme", "scheme", "scheme", "scheme", "scheme", "scheme", "scheme", "scheme", "scheme", "scheme", "scheme", "scheme", "scheme", "c", "range"]}