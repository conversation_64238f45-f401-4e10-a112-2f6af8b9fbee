{"version": 3, "sources": ["../../d3-axis/src/identity.js", "../../d3-axis/src/axis.js", "../../d3-dispatch/src/dispatch.js", "../../d3-drag/src/noevent.js", "../../d3-drag/src/nodrag.js", "../../d3-drag/src/constant.js", "../../d3-drag/src/event.js", "../../d3-drag/src/drag.js", "../../d3-timer/src/timer.js", "../../d3-timer/src/timeout.js", "../../d3-timer/src/interval.js", "../../d3-transition/src/transition/schedule.js", "../../d3-transition/src/interrupt.js", "../../d3-transition/src/selection/interrupt.js", "../../d3-transition/src/transition/tween.js", "../../d3-transition/src/transition/interpolate.js", "../../d3-transition/src/transition/attr.js", "../../d3-transition/src/transition/attrTween.js", "../../d3-transition/src/transition/delay.js", "../../d3-transition/src/transition/duration.js", "../../d3-transition/src/transition/ease.js", "../../d3-transition/src/transition/easeVarying.js", "../../d3-transition/src/transition/filter.js", "../../d3-transition/src/transition/merge.js", "../../d3-transition/src/transition/on.js", "../../d3-transition/src/transition/remove.js", "../../d3-transition/src/transition/select.js", "../../d3-transition/src/transition/selectAll.js", "../../d3-transition/src/transition/selection.js", "../../d3-transition/src/transition/style.js", "../../d3-transition/src/transition/styleTween.js", "../../d3-transition/src/transition/text.js", "../../d3-transition/src/transition/textTween.js", "../../d3-transition/src/transition/transition.js", "../../d3-transition/src/transition/end.js", "../../d3-transition/src/transition/index.js", "../../d3-ease/src/linear.js", "../../d3-ease/src/quad.js", "../../d3-ease/src/cubic.js", "../../d3-ease/src/poly.js", "../../d3-ease/src/sin.js", "../../d3-ease/src/math.js", "../../d3-ease/src/exp.js", "../../d3-ease/src/circle.js", "../../d3-ease/src/bounce.js", "../../d3-ease/src/back.js", "../../d3-ease/src/elastic.js", "../../d3-transition/src/selection/transition.js", "../../d3-transition/src/selection/index.js", "../../d3-transition/src/active.js", "../../d3-brush/src/constant.js", "../../d3-brush/src/event.js", "../../d3-brush/src/noevent.js", "../../d3-brush/src/brush.js", "../../d3-chord/src/math.js", "../../d3-chord/src/chord.js", "../../d3-chord/src/array.js", "../../d3-chord/src/constant.js", "../../d3-chord/src/ribbon.js", "../../d3-contour/src/array.js", "../../d3-contour/src/ascending.js", "../../d3-contour/src/area.js", "../../d3-contour/src/constant.js", "../../d3-contour/src/contains.js", "../../d3-contour/src/noop.js", "../../d3-contour/src/contours.js", "../../d3-contour/src/density.js", "../../d3-dsv/src/dsv.js", "../../d3-dsv/src/csv.js", "../../d3-dsv/src/tsv.js", "../../d3-dsv/src/autoType.js", "../../d3-fetch/src/blob.js", "../../d3-fetch/src/buffer.js", "../../d3-fetch/src/text.js", "../../d3-fetch/src/dsv.js", "../../d3-fetch/src/image.js", "../../d3-fetch/src/json.js", "../../d3-fetch/src/xml.js", "../../d3-force/src/center.js", "../../d3-quadtree/src/add.js", "../../d3-quadtree/src/cover.js", "../../d3-quadtree/src/data.js", "../../d3-quadtree/src/extent.js", "../../d3-quadtree/src/quad.js", "../../d3-quadtree/src/find.js", "../../d3-quadtree/src/remove.js", "../../d3-quadtree/src/root.js", "../../d3-quadtree/src/size.js", "../../d3-quadtree/src/visit.js", "../../d3-quadtree/src/visitAfter.js", "../../d3-quadtree/src/x.js", "../../d3-quadtree/src/y.js", "../../d3-quadtree/src/quadtree.js", "../../d3-force/src/constant.js", "../../d3-force/src/jiggle.js", "../../d3-force/src/collide.js", "../../d3-force/src/link.js", "../../d3-force/src/lcg.js", "../../d3-force/src/simulation.js", "../../d3-force/src/manyBody.js", "../../d3-force/src/radial.js", "../../d3-force/src/x.js", "../../d3-force/src/y.js", "../../d3-hierarchy/src/cluster.js", "../../d3-hierarchy/src/hierarchy/count.js", "../../d3-hierarchy/src/hierarchy/each.js", "../../d3-hierarchy/src/hierarchy/eachBefore.js", "../../d3-hierarchy/src/hierarchy/eachAfter.js", "../../d3-hierarchy/src/hierarchy/find.js", "../../d3-hierarchy/src/hierarchy/sum.js", "../../d3-hierarchy/src/hierarchy/sort.js", "../../d3-hierarchy/src/hierarchy/path.js", "../../d3-hierarchy/src/hierarchy/ancestors.js", "../../d3-hierarchy/src/hierarchy/descendants.js", "../../d3-hierarchy/src/hierarchy/leaves.js", "../../d3-hierarchy/src/hierarchy/links.js", "../../d3-hierarchy/src/hierarchy/iterator.js", "../../d3-hierarchy/src/hierarchy/index.js", "../../d3-hierarchy/src/accessors.js", "../../d3-hierarchy/src/constant.js", "../../d3-hierarchy/src/lcg.js", "../../d3-hierarchy/src/array.js", "../../d3-hierarchy/src/pack/enclose.js", "../../d3-hierarchy/src/pack/siblings.js", "../../d3-hierarchy/src/pack/index.js", "../../d3-hierarchy/src/treemap/round.js", "../../d3-hierarchy/src/treemap/dice.js", "../../d3-hierarchy/src/partition.js", "../../d3-hierarchy/src/stratify.js", "../../d3-hierarchy/src/tree.js", "../../d3-hierarchy/src/treemap/slice.js", "../../d3-hierarchy/src/treemap/squarify.js", "../../d3-hierarchy/src/treemap/index.js", "../../d3-hierarchy/src/treemap/binary.js", "../../d3-hierarchy/src/treemap/sliceDice.js", "../../d3-hierarchy/src/treemap/resquarify.js", "../../d3-polygon/src/area.js", "../../d3-polygon/src/centroid.js", "../../d3-polygon/src/cross.js", "../../d3-polygon/src/hull.js", "../../d3-polygon/src/contains.js", "../../d3-polygon/src/length.js", "../../d3-random/src/defaultSource.js", "../../d3-random/src/uniform.js", "../../d3-random/src/int.js", "../../d3-random/src/normal.js", "../../d3-random/src/logNormal.js", "../../d3-random/src/irwinHall.js", "../../d3-random/src/bates.js", "../../d3-random/src/exponential.js", "../../d3-random/src/pareto.js", "../../d3-random/src/bernoulli.js", "../../d3-random/src/geometric.js", "../../d3-random/src/gamma.js", "../../d3-random/src/beta.js", "../../d3-random/src/binomial.js", "../../d3-random/src/weibull.js", "../../d3-random/src/cauchy.js", "../../d3-random/src/logistic.js", "../../d3-random/src/poisson.js", "../../d3-random/src/lcg.js", "../../d3-zoom/src/constant.js", "../../d3-zoom/src/event.js", "../../d3-zoom/src/transform.js", "../../d3-zoom/src/noevent.js", "../../d3-zoom/src/zoom.js"], "sourcesContent": ["export default function(x) {\n  return x;\n}\n", "import identity from \"./identity.js\";\n\nvar top = 1,\n    right = 2,\n    bottom = 3,\n    left = 4,\n    epsilon = 1e-6;\n\nfunction translateX(x) {\n  return \"translate(\" + x + \",0)\";\n}\n\nfunction translateY(y) {\n  return \"translate(0,\" + y + \")\";\n}\n\nfunction number(scale) {\n  return d => +scale(d);\n}\n\nfunction center(scale, offset) {\n  offset = Math.max(0, scale.bandwidth() - offset * 2) / 2;\n  if (scale.round()) offset = Math.round(offset);\n  return d => +scale(d) + offset;\n}\n\nfunction entering() {\n  return !this.__axis;\n}\n\nfunction axis(orient, scale) {\n  var tickArguments = [],\n      tickValues = null,\n      tickFormat = null,\n      tickSizeInner = 6,\n      tickSizeOuter = 6,\n      tickPadding = 3,\n      offset = typeof window !== \"undefined\" && window.devicePixelRatio > 1 ? 0 : 0.5,\n      k = orient === top || orient === left ? -1 : 1,\n      x = orient === left || orient === right ? \"x\" : \"y\",\n      transform = orient === top || orient === bottom ? translateX : translateY;\n\n  function axis(context) {\n    var values = tickValues == null ? (scale.ticks ? scale.ticks.apply(scale, tickArguments) : scale.domain()) : tickValues,\n        format = tickFormat == null ? (scale.tickFormat ? scale.tickFormat.apply(scale, tickArguments) : identity) : tickFormat,\n        spacing = Math.max(tickSizeInner, 0) + tickPadding,\n        range = scale.range(),\n        range0 = +range[0] + offset,\n        range1 = +range[range.length - 1] + offset,\n        position = (scale.bandwidth ? center : number)(scale.copy(), offset),\n        selection = context.selection ? context.selection() : context,\n        path = selection.selectAll(\".domain\").data([null]),\n        tick = selection.selectAll(\".tick\").data(values, scale).order(),\n        tickExit = tick.exit(),\n        tickEnter = tick.enter().append(\"g\").attr(\"class\", \"tick\"),\n        line = tick.select(\"line\"),\n        text = tick.select(\"text\");\n\n    path = path.merge(path.enter().insert(\"path\", \".tick\")\n        .attr(\"class\", \"domain\")\n        .attr(\"stroke\", \"currentColor\"));\n\n    tick = tick.merge(tickEnter);\n\n    line = line.merge(tickEnter.append(\"line\")\n        .attr(\"stroke\", \"currentColor\")\n        .attr(x + \"2\", k * tickSizeInner));\n\n    text = text.merge(tickEnter.append(\"text\")\n        .attr(\"fill\", \"currentColor\")\n        .attr(x, k * spacing)\n        .attr(\"dy\", orient === top ? \"0em\" : orient === bottom ? \"0.71em\" : \"0.32em\"));\n\n    if (context !== selection) {\n      path = path.transition(context);\n      tick = tick.transition(context);\n      line = line.transition(context);\n      text = text.transition(context);\n\n      tickExit = tickExit.transition(context)\n          .attr(\"opacity\", epsilon)\n          .attr(\"transform\", function(d) { return isFinite(d = position(d)) ? transform(d + offset) : this.getAttribute(\"transform\"); });\n\n      tickEnter\n          .attr(\"opacity\", epsilon)\n          .attr(\"transform\", function(d) { var p = this.parentNode.__axis; return transform((p && isFinite(p = p(d)) ? p : position(d)) + offset); });\n    }\n\n    tickExit.remove();\n\n    path\n        .attr(\"d\", orient === left || orient === right\n            ? (tickSizeOuter ? \"M\" + k * tickSizeOuter + \",\" + range0 + \"H\" + offset + \"V\" + range1 + \"H\" + k * tickSizeOuter : \"M\" + offset + \",\" + range0 + \"V\" + range1)\n            : (tickSizeOuter ? \"M\" + range0 + \",\" + k * tickSizeOuter + \"V\" + offset + \"H\" + range1 + \"V\" + k * tickSizeOuter : \"M\" + range0 + \",\" + offset + \"H\" + range1));\n\n    tick\n        .attr(\"opacity\", 1)\n        .attr(\"transform\", function(d) { return transform(position(d) + offset); });\n\n    line\n        .attr(x + \"2\", k * tickSizeInner);\n\n    text\n        .attr(x, k * spacing)\n        .text(format);\n\n    selection.filter(entering)\n        .attr(\"fill\", \"none\")\n        .attr(\"font-size\", 10)\n        .attr(\"font-family\", \"sans-serif\")\n        .attr(\"text-anchor\", orient === right ? \"start\" : orient === left ? \"end\" : \"middle\");\n\n    selection\n        .each(function() { this.__axis = position; });\n  }\n\n  axis.scale = function(_) {\n    return arguments.length ? (scale = _, axis) : scale;\n  };\n\n  axis.ticks = function() {\n    return tickArguments = Array.from(arguments), axis;\n  };\n\n  axis.tickArguments = function(_) {\n    return arguments.length ? (tickArguments = _ == null ? [] : Array.from(_), axis) : tickArguments.slice();\n  };\n\n  axis.tickValues = function(_) {\n    return arguments.length ? (tickValues = _ == null ? null : Array.from(_), axis) : tickValues && tickValues.slice();\n  };\n\n  axis.tickFormat = function(_) {\n    return arguments.length ? (tickFormat = _, axis) : tickFormat;\n  };\n\n  axis.tickSize = function(_) {\n    return arguments.length ? (tickSizeInner = tickSizeOuter = +_, axis) : tickSizeInner;\n  };\n\n  axis.tickSizeInner = function(_) {\n    return arguments.length ? (tickSizeInner = +_, axis) : tickSizeInner;\n  };\n\n  axis.tickSizeOuter = function(_) {\n    return arguments.length ? (tickSizeOuter = +_, axis) : tickSizeOuter;\n  };\n\n  axis.tickPadding = function(_) {\n    return arguments.length ? (tickPadding = +_, axis) : tickPadding;\n  };\n\n  axis.offset = function(_) {\n    return arguments.length ? (offset = +_, axis) : offset;\n  };\n\n  return axis;\n}\n\nexport function axisTop(scale) {\n  return axis(top, scale);\n}\n\nexport function axisRight(scale) {\n  return axis(right, scale);\n}\n\nexport function axisBottom(scale) {\n  return axis(bottom, scale);\n}\n\nexport function axisLeft(scale) {\n  return axis(left, scale);\n}\n", "var noop = {value: () => {}};\n\nfunction dispatch() {\n  for (var i = 0, n = arguments.length, _ = {}, t; i < n; ++i) {\n    if (!(t = arguments[i] + \"\") || (t in _) || /[\\s.]/.test(t)) throw new Error(\"illegal type: \" + t);\n    _[t] = [];\n  }\n  return new Dispatch(_);\n}\n\nfunction Dispatch(_) {\n  this._ = _;\n}\n\nfunction parseTypenames(typenames, types) {\n  return typenames.trim().split(/^|\\s+/).map(function(t) {\n    var name = \"\", i = t.indexOf(\".\");\n    if (i >= 0) name = t.slice(i + 1), t = t.slice(0, i);\n    if (t && !types.hasOwnProperty(t)) throw new Error(\"unknown type: \" + t);\n    return {type: t, name: name};\n  });\n}\n\nDispatch.prototype = dispatch.prototype = {\n  constructor: Dispatch,\n  on: function(typename, callback) {\n    var _ = this._,\n        T = parseTypenames(typename + \"\", _),\n        t,\n        i = -1,\n        n = T.length;\n\n    // If no callback was specified, return the callback of the given type and name.\n    if (arguments.length < 2) {\n      while (++i < n) if ((t = (typename = T[i]).type) && (t = get(_[t], typename.name))) return t;\n      return;\n    }\n\n    // If a type was specified, set the callback for the given type and name.\n    // Otherwise, if a null callback was specified, remove callbacks of the given name.\n    if (callback != null && typeof callback !== \"function\") throw new Error(\"invalid callback: \" + callback);\n    while (++i < n) {\n      if (t = (typename = T[i]).type) _[t] = set(_[t], typename.name, callback);\n      else if (callback == null) for (t in _) _[t] = set(_[t], typename.name, null);\n    }\n\n    return this;\n  },\n  copy: function() {\n    var copy = {}, _ = this._;\n    for (var t in _) copy[t] = _[t].slice();\n    return new Dispatch(copy);\n  },\n  call: function(type, that) {\n    if ((n = arguments.length - 2) > 0) for (var args = new Array(n), i = 0, n, t; i < n; ++i) args[i] = arguments[i + 2];\n    if (!this._.hasOwnProperty(type)) throw new Error(\"unknown type: \" + type);\n    for (t = this._[type], i = 0, n = t.length; i < n; ++i) t[i].value.apply(that, args);\n  },\n  apply: function(type, that, args) {\n    if (!this._.hasOwnProperty(type)) throw new Error(\"unknown type: \" + type);\n    for (var t = this._[type], i = 0, n = t.length; i < n; ++i) t[i].value.apply(that, args);\n  }\n};\n\nfunction get(type, name) {\n  for (var i = 0, n = type.length, c; i < n; ++i) {\n    if ((c = type[i]).name === name) {\n      return c.value;\n    }\n  }\n}\n\nfunction set(type, name, callback) {\n  for (var i = 0, n = type.length; i < n; ++i) {\n    if (type[i].name === name) {\n      type[i] = noop, type = type.slice(0, i).concat(type.slice(i + 1));\n      break;\n    }\n  }\n  if (callback != null) type.push({name: name, value: callback});\n  return type;\n}\n\nexport default dispatch;\n", "// These are typically used in conjunction with noevent to ensure that we can\n// preventDefault on the event.\nexport const nonpassive = {passive: false};\nexport const nonpassivecapture = {capture: true, passive: false};\n\nexport function nopropagation(event) {\n  event.stopImmediatePropagation();\n}\n\nexport default function(event) {\n  event.preventDefault();\n  event.stopImmediatePropagation();\n}\n", "import {select} from \"d3-selection\";\nimport noevent, {nonpassivecapture} from \"./noevent.js\";\n\nexport default function(view) {\n  var root = view.document.documentElement,\n      selection = select(view).on(\"dragstart.drag\", noevent, nonpassivecapture);\n  if (\"onselectstart\" in root) {\n    selection.on(\"selectstart.drag\", noevent, nonpassivecapture);\n  } else {\n    root.__noselect = root.style.MozUserSelect;\n    root.style.MozUserSelect = \"none\";\n  }\n}\n\nexport function yesdrag(view, noclick) {\n  var root = view.document.documentElement,\n      selection = select(view).on(\"dragstart.drag\", null);\n  if (noclick) {\n    selection.on(\"click.drag\", noevent, nonpassivecapture);\n    setTimeout(function() { selection.on(\"click.drag\", null); }, 0);\n  }\n  if (\"onselectstart\" in root) {\n    selection.on(\"selectstart.drag\", null);\n  } else {\n    root.style.MozUserSelect = root.__noselect;\n    delete root.__noselect;\n  }\n}\n", "export default x => () => x;\n", "export default function DragEvent(type, {\n  sourceEvent,\n  subject,\n  target,\n  identifier,\n  active,\n  x, y, dx, dy,\n  dispatch\n}) {\n  Object.defineProperties(this, {\n    type: {value: type, enumerable: true, configurable: true},\n    sourceEvent: {value: sourceEvent, enumerable: true, configurable: true},\n    subject: {value: subject, enumerable: true, configurable: true},\n    target: {value: target, enumerable: true, configurable: true},\n    identifier: {value: identifier, enumerable: true, configurable: true},\n    active: {value: active, enumerable: true, configurable: true},\n    x: {value: x, enumerable: true, configurable: true},\n    y: {value: y, enumerable: true, configurable: true},\n    dx: {value: dx, enumerable: true, configurable: true},\n    dy: {value: dy, enumerable: true, configurable: true},\n    _: {value: dispatch}\n  });\n}\n\nDragEvent.prototype.on = function() {\n  var value = this._.on.apply(this._, arguments);\n  return value === this._ ? this : value;\n};\n", "import {dispatch} from \"d3-dispatch\";\nimport {select, pointer} from \"d3-selection\";\nimport nodrag, {yesdrag} from \"./nodrag.js\";\nimport noevent, {nonpassive, nonpassivecapture, nopropagation} from \"./noevent.js\";\nimport constant from \"./constant.js\";\nimport DragEvent from \"./event.js\";\n\n// Ignore right-click, since that should open the context menu.\nfunction defaultFilter(event) {\n  return !event.ctrlKey && !event.button;\n}\n\nfunction defaultContainer() {\n  return this.parentNode;\n}\n\nfunction defaultSubject(event, d) {\n  return d == null ? {x: event.x, y: event.y} : d;\n}\n\nfunction defaultTouchable() {\n  return navigator.maxTouchPoints || (\"ontouchstart\" in this);\n}\n\nexport default function() {\n  var filter = defaultFilter,\n      container = defaultContainer,\n      subject = defaultSubject,\n      touchable = defaultTouchable,\n      gestures = {},\n      listeners = dispatch(\"start\", \"drag\", \"end\"),\n      active = 0,\n      mousedownx,\n      mousedowny,\n      mousemoving,\n      touchending,\n      clickDistance2 = 0;\n\n  function drag(selection) {\n    selection\n        .on(\"mousedown.drag\", mousedowned)\n      .filter(touchable)\n        .on(\"touchstart.drag\", touchstarted)\n        .on(\"touchmove.drag\", touchmoved, nonpassive)\n        .on(\"touchend.drag touchcancel.drag\", touchended)\n        .style(\"touch-action\", \"none\")\n        .style(\"-webkit-tap-highlight-color\", \"rgba(0,0,0,0)\");\n  }\n\n  function mousedowned(event, d) {\n    if (touchending || !filter.call(this, event, d)) return;\n    var gesture = beforestart(this, container.call(this, event, d), event, d, \"mouse\");\n    if (!gesture) return;\n    select(event.view)\n      .on(\"mousemove.drag\", mousemoved, nonpassivecapture)\n      .on(\"mouseup.drag\", mouseupped, nonpassivecapture);\n    nodrag(event.view);\n    nopropagation(event);\n    mousemoving = false;\n    mousedownx = event.clientX;\n    mousedowny = event.clientY;\n    gesture(\"start\", event);\n  }\n\n  function mousemoved(event) {\n    noevent(event);\n    if (!mousemoving) {\n      var dx = event.clientX - mousedownx, dy = event.clientY - mousedowny;\n      mousemoving = dx * dx + dy * dy > clickDistance2;\n    }\n    gestures.mouse(\"drag\", event);\n  }\n\n  function mouseupped(event) {\n    select(event.view).on(\"mousemove.drag mouseup.drag\", null);\n    yesdrag(event.view, mousemoving);\n    noevent(event);\n    gestures.mouse(\"end\", event);\n  }\n\n  function touchstarted(event, d) {\n    if (!filter.call(this, event, d)) return;\n    var touches = event.changedTouches,\n        c = container.call(this, event, d),\n        n = touches.length, i, gesture;\n\n    for (i = 0; i < n; ++i) {\n      if (gesture = beforestart(this, c, event, d, touches[i].identifier, touches[i])) {\n        nopropagation(event);\n        gesture(\"start\", event, touches[i]);\n      }\n    }\n  }\n\n  function touchmoved(event) {\n    var touches = event.changedTouches,\n        n = touches.length, i, gesture;\n\n    for (i = 0; i < n; ++i) {\n      if (gesture = gestures[touches[i].identifier]) {\n        noevent(event);\n        gesture(\"drag\", event, touches[i]);\n      }\n    }\n  }\n\n  function touchended(event) {\n    var touches = event.changedTouches,\n        n = touches.length, i, gesture;\n\n    if (touchending) clearTimeout(touchending);\n    touchending = setTimeout(function() { touchending = null; }, 500); // Ghost clicks are delayed!\n    for (i = 0; i < n; ++i) {\n      if (gesture = gestures[touches[i].identifier]) {\n        nopropagation(event);\n        gesture(\"end\", event, touches[i]);\n      }\n    }\n  }\n\n  function beforestart(that, container, event, d, identifier, touch) {\n    var dispatch = listeners.copy(),\n        p = pointer(touch || event, container), dx, dy,\n        s;\n\n    if ((s = subject.call(that, new DragEvent(\"beforestart\", {\n        sourceEvent: event,\n        target: drag,\n        identifier,\n        active,\n        x: p[0],\n        y: p[1],\n        dx: 0,\n        dy: 0,\n        dispatch\n      }), d)) == null) return;\n\n    dx = s.x - p[0] || 0;\n    dy = s.y - p[1] || 0;\n\n    return function gesture(type, event, touch) {\n      var p0 = p, n;\n      switch (type) {\n        case \"start\": gestures[identifier] = gesture, n = active++; break;\n        case \"end\": delete gestures[identifier], --active; // falls through\n        case \"drag\": p = pointer(touch || event, container), n = active; break;\n      }\n      dispatch.call(\n        type,\n        that,\n        new DragEvent(type, {\n          sourceEvent: event,\n          subject: s,\n          target: drag,\n          identifier,\n          active: n,\n          x: p[0] + dx,\n          y: p[1] + dy,\n          dx: p[0] - p0[0],\n          dy: p[1] - p0[1],\n          dispatch\n        }),\n        d\n      );\n    };\n  }\n\n  drag.filter = function(_) {\n    return arguments.length ? (filter = typeof _ === \"function\" ? _ : constant(!!_), drag) : filter;\n  };\n\n  drag.container = function(_) {\n    return arguments.length ? (container = typeof _ === \"function\" ? _ : constant(_), drag) : container;\n  };\n\n  drag.subject = function(_) {\n    return arguments.length ? (subject = typeof _ === \"function\" ? _ : constant(_), drag) : subject;\n  };\n\n  drag.touchable = function(_) {\n    return arguments.length ? (touchable = typeof _ === \"function\" ? _ : constant(!!_), drag) : touchable;\n  };\n\n  drag.on = function() {\n    var value = listeners.on.apply(listeners, arguments);\n    return value === listeners ? drag : value;\n  };\n\n  drag.clickDistance = function(_) {\n    return arguments.length ? (clickDistance2 = (_ = +_) * _, drag) : Math.sqrt(clickDistance2);\n  };\n\n  return drag;\n}\n", "var frame = 0, // is an animation frame pending?\n    timeout = 0, // is a timeout pending?\n    interval = 0, // are any timers active?\n    pokeDelay = 1000, // how frequently we check for clock skew\n    taskHead,\n    taskTail,\n    clockLast = 0,\n    clockNow = 0,\n    clockSkew = 0,\n    clock = typeof performance === \"object\" && performance.now ? performance : Date,\n    setFrame = typeof window === \"object\" && window.requestAnimationFrame ? window.requestAnimationFrame.bind(window) : function(f) { setTimeout(f, 17); };\n\nexport function now() {\n  return clockNow || (setFrame(clearNow), clockNow = clock.now() + clockSkew);\n}\n\nfunction clearNow() {\n  clockNow = 0;\n}\n\nexport function Timer() {\n  this._call =\n  this._time =\n  this._next = null;\n}\n\nTimer.prototype = timer.prototype = {\n  constructor: Timer,\n  restart: function(callback, delay, time) {\n    if (typeof callback !== \"function\") throw new TypeError(\"callback is not a function\");\n    time = (time == null ? now() : +time) + (delay == null ? 0 : +delay);\n    if (!this._next && taskTail !== this) {\n      if (taskTail) taskTail._next = this;\n      else taskHead = this;\n      taskTail = this;\n    }\n    this._call = callback;\n    this._time = time;\n    sleep();\n  },\n  stop: function() {\n    if (this._call) {\n      this._call = null;\n      this._time = Infinity;\n      sleep();\n    }\n  }\n};\n\nexport function timer(callback, delay, time) {\n  var t = new Timer;\n  t.restart(callback, delay, time);\n  return t;\n}\n\nexport function timerFlush() {\n  now(); // Get the current time, if not already set.\n  ++frame; // Pretend we’ve set an alarm, if we haven’t already.\n  var t = taskHead, e;\n  while (t) {\n    if ((e = clockNow - t._time) >= 0) t._call.call(undefined, e);\n    t = t._next;\n  }\n  --frame;\n}\n\nfunction wake() {\n  clockNow = (clockLast = clock.now()) + clockSkew;\n  frame = timeout = 0;\n  try {\n    timerFlush();\n  } finally {\n    frame = 0;\n    nap();\n    clockNow = 0;\n  }\n}\n\nfunction poke() {\n  var now = clock.now(), delay = now - clockLast;\n  if (delay > pokeDelay) clockSkew -= delay, clockLast = now;\n}\n\nfunction nap() {\n  var t0, t1 = taskHead, t2, time = Infinity;\n  while (t1) {\n    if (t1._call) {\n      if (time > t1._time) time = t1._time;\n      t0 = t1, t1 = t1._next;\n    } else {\n      t2 = t1._next, t1._next = null;\n      t1 = t0 ? t0._next = t2 : taskHead = t2;\n    }\n  }\n  taskTail = t0;\n  sleep(time);\n}\n\nfunction sleep(time) {\n  if (frame) return; // Soonest alarm already set, or will be.\n  if (timeout) timeout = clearTimeout(timeout);\n  var delay = time - clockNow; // Strictly less than if we recomputed clockNow.\n  if (delay > 24) {\n    if (time < Infinity) timeout = setTimeout(wake, time - clock.now() - clockSkew);\n    if (interval) interval = clearInterval(interval);\n  } else {\n    if (!interval) clockLast = clock.now(), interval = setInterval(poke, pokeDelay);\n    frame = 1, setFrame(wake);\n  }\n}\n", "import {Timer} from \"./timer.js\";\n\nexport default function(callback, delay, time) {\n  var t = new Timer;\n  delay = delay == null ? 0 : +delay;\n  t.restart(elapsed => {\n    t.stop();\n    callback(elapsed + delay);\n  }, delay, time);\n  return t;\n}\n", "import {Timer, now} from \"./timer.js\";\n\nexport default function(callback, delay, time) {\n  var t = new Timer, total = delay;\n  if (delay == null) return t.restart(callback, delay, time), t;\n  t._restart = t.restart;\n  t.restart = function(callback, delay, time) {\n    delay = +delay, time = time == null ? now() : +time;\n    t._restart(function tick(elapsed) {\n      elapsed += total;\n      t._restart(tick, total += delay, time);\n      callback(elapsed);\n    }, delay, time);\n  }\n  t.restart(callback, delay, time);\n  return t;\n}\n", "import {dispatch} from \"d3-dispatch\";\nimport {timer, timeout} from \"d3-timer\";\n\nvar emptyOn = dispatch(\"start\", \"end\", \"cancel\", \"interrupt\");\nvar emptyTween = [];\n\nexport var CREATED = 0;\nexport var SCHEDULED = 1;\nexport var STARTING = 2;\nexport var STARTED = 3;\nexport var RUNNING = 4;\nexport var ENDING = 5;\nexport var ENDED = 6;\n\nexport default function(node, name, id, index, group, timing) {\n  var schedules = node.__transition;\n  if (!schedules) node.__transition = {};\n  else if (id in schedules) return;\n  create(node, id, {\n    name: name,\n    index: index, // For context during callback.\n    group: group, // For context during callback.\n    on: emptyOn,\n    tween: emptyTween,\n    time: timing.time,\n    delay: timing.delay,\n    duration: timing.duration,\n    ease: timing.ease,\n    timer: null,\n    state: CREATED\n  });\n}\n\nexport function init(node, id) {\n  var schedule = get(node, id);\n  if (schedule.state > CREATED) throw new Error(\"too late; already scheduled\");\n  return schedule;\n}\n\nexport function set(node, id) {\n  var schedule = get(node, id);\n  if (schedule.state > STARTED) throw new Error(\"too late; already running\");\n  return schedule;\n}\n\nexport function get(node, id) {\n  var schedule = node.__transition;\n  if (!schedule || !(schedule = schedule[id])) throw new Error(\"transition not found\");\n  return schedule;\n}\n\nfunction create(node, id, self) {\n  var schedules = node.__transition,\n      tween;\n\n  // Initialize the self timer when the transition is created.\n  // Note the actual delay is not known until the first callback!\n  schedules[id] = self;\n  self.timer = timer(schedule, 0, self.time);\n\n  function schedule(elapsed) {\n    self.state = SCHEDULED;\n    self.timer.restart(start, self.delay, self.time);\n\n    // If the elapsed delay is less than our first sleep, start immediately.\n    if (self.delay <= elapsed) start(elapsed - self.delay);\n  }\n\n  function start(elapsed) {\n    var i, j, n, o;\n\n    // If the state is not SCHEDULED, then we previously errored on start.\n    if (self.state !== SCHEDULED) return stop();\n\n    for (i in schedules) {\n      o = schedules[i];\n      if (o.name !== self.name) continue;\n\n      // While this element already has a starting transition during this frame,\n      // defer starting an interrupting transition until that transition has a\n      // chance to tick (and possibly end); see d3/d3-transition#54!\n      if (o.state === STARTED) return timeout(start);\n\n      // Interrupt the active transition, if any.\n      if (o.state === RUNNING) {\n        o.state = ENDED;\n        o.timer.stop();\n        o.on.call(\"interrupt\", node, node.__data__, o.index, o.group);\n        delete schedules[i];\n      }\n\n      // Cancel any pre-empted transitions.\n      else if (+i < id) {\n        o.state = ENDED;\n        o.timer.stop();\n        o.on.call(\"cancel\", node, node.__data__, o.index, o.group);\n        delete schedules[i];\n      }\n    }\n\n    // Defer the first tick to end of the current frame; see d3/d3#1576.\n    // Note the transition may be canceled after start and before the first tick!\n    // Note this must be scheduled before the start event; see d3/d3-transition#16!\n    // Assuming this is successful, subsequent callbacks go straight to tick.\n    timeout(function() {\n      if (self.state === STARTED) {\n        self.state = RUNNING;\n        self.timer.restart(tick, self.delay, self.time);\n        tick(elapsed);\n      }\n    });\n\n    // Dispatch the start event.\n    // Note this must be done before the tween are initialized.\n    self.state = STARTING;\n    self.on.call(\"start\", node, node.__data__, self.index, self.group);\n    if (self.state !== STARTING) return; // interrupted\n    self.state = STARTED;\n\n    // Initialize the tween, deleting null tween.\n    tween = new Array(n = self.tween.length);\n    for (i = 0, j = -1; i < n; ++i) {\n      if (o = self.tween[i].value.call(node, node.__data__, self.index, self.group)) {\n        tween[++j] = o;\n      }\n    }\n    tween.length = j + 1;\n  }\n\n  function tick(elapsed) {\n    var t = elapsed < self.duration ? self.ease.call(null, elapsed / self.duration) : (self.timer.restart(stop), self.state = ENDING, 1),\n        i = -1,\n        n = tween.length;\n\n    while (++i < n) {\n      tween[i].call(node, t);\n    }\n\n    // Dispatch the end event.\n    if (self.state === ENDING) {\n      self.on.call(\"end\", node, node.__data__, self.index, self.group);\n      stop();\n    }\n  }\n\n  function stop() {\n    self.state = ENDED;\n    self.timer.stop();\n    delete schedules[id];\n    for (var i in schedules) return; // eslint-disable-line no-unused-vars\n    delete node.__transition;\n  }\n}\n", "import {STARTING, ENDING, ENDED} from \"./transition/schedule.js\";\n\nexport default function(node, name) {\n  var schedules = node.__transition,\n      schedule,\n      active,\n      empty = true,\n      i;\n\n  if (!schedules) return;\n\n  name = name == null ? null : name + \"\";\n\n  for (i in schedules) {\n    if ((schedule = schedules[i]).name !== name) { empty = false; continue; }\n    active = schedule.state > STARTING && schedule.state < ENDING;\n    schedule.state = ENDED;\n    schedule.timer.stop();\n    schedule.on.call(active ? \"interrupt\" : \"cancel\", node, node.__data__, schedule.index, schedule.group);\n    delete schedules[i];\n  }\n\n  if (empty) delete node.__transition;\n}\n", "import interrupt from \"../interrupt.js\";\n\nexport default function(name) {\n  return this.each(function() {\n    interrupt(this, name);\n  });\n}\n", "import {get, set} from \"./schedule.js\";\n\nfunction tweenRemove(id, name) {\n  var tween0, tween1;\n  return function() {\n    var schedule = set(this, id),\n        tween = schedule.tween;\n\n    // If this node shared tween with the previous node,\n    // just assign the updated shared tween and we’re done!\n    // Otherwise, copy-on-write.\n    if (tween !== tween0) {\n      tween1 = tween0 = tween;\n      for (var i = 0, n = tween1.length; i < n; ++i) {\n        if (tween1[i].name === name) {\n          tween1 = tween1.slice();\n          tween1.splice(i, 1);\n          break;\n        }\n      }\n    }\n\n    schedule.tween = tween1;\n  };\n}\n\nfunction tweenFunction(id, name, value) {\n  var tween0, tween1;\n  if (typeof value !== \"function\") throw new Error;\n  return function() {\n    var schedule = set(this, id),\n        tween = schedule.tween;\n\n    // If this node shared tween with the previous node,\n    // just assign the updated shared tween and we’re done!\n    // Otherwise, copy-on-write.\n    if (tween !== tween0) {\n      tween1 = (tween0 = tween).slice();\n      for (var t = {name: name, value: value}, i = 0, n = tween1.length; i < n; ++i) {\n        if (tween1[i].name === name) {\n          tween1[i] = t;\n          break;\n        }\n      }\n      if (i === n) tween1.push(t);\n    }\n\n    schedule.tween = tween1;\n  };\n}\n\nexport default function(name, value) {\n  var id = this._id;\n\n  name += \"\";\n\n  if (arguments.length < 2) {\n    var tween = get(this.node(), id).tween;\n    for (var i = 0, n = tween.length, t; i < n; ++i) {\n      if ((t = tween[i]).name === name) {\n        return t.value;\n      }\n    }\n    return null;\n  }\n\n  return this.each((value == null ? tweenRemove : tweenFunction)(id, name, value));\n}\n\nexport function tweenValue(transition, name, value) {\n  var id = transition._id;\n\n  transition.each(function() {\n    var schedule = set(this, id);\n    (schedule.value || (schedule.value = {}))[name] = value.apply(this, arguments);\n  });\n\n  return function(node) {\n    return get(node, id).value[name];\n  };\n}\n", "import {color} from \"d3-color\";\nimport {interpolateNumber, interpolateRgb, interpolateString} from \"d3-interpolate\";\n\nexport default function(a, b) {\n  var c;\n  return (typeof b === \"number\" ? interpolateNumber\n      : b instanceof color ? interpolateRgb\n      : (c = color(b)) ? (b = c, interpolateRgb)\n      : interpolateString)(a, b);\n}\n", "import {interpolateTransformSvg as interpolateTransform} from \"d3-interpolate\";\nimport {namespace} from \"d3-selection\";\nimport {tweenValue} from \"./tween.js\";\nimport interpolate from \"./interpolate.js\";\n\nfunction attrRemove(name) {\n  return function() {\n    this.removeAttribute(name);\n  };\n}\n\nfunction attrRemoveNS(fullname) {\n  return function() {\n    this.removeAttributeNS(fullname.space, fullname.local);\n  };\n}\n\nfunction attrConstant(name, interpolate, value1) {\n  var string00,\n      string1 = value1 + \"\",\n      interpolate0;\n  return function() {\n    var string0 = this.getAttribute(name);\n    return string0 === string1 ? null\n        : string0 === string00 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, value1);\n  };\n}\n\nfunction attrConstantNS(fullname, interpolate, value1) {\n  var string00,\n      string1 = value1 + \"\",\n      interpolate0;\n  return function() {\n    var string0 = this.getAttributeNS(fullname.space, fullname.local);\n    return string0 === string1 ? null\n        : string0 === string00 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, value1);\n  };\n}\n\nfunction attrFunction(name, interpolate, value) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0, value1 = value(this), string1;\n    if (value1 == null) return void this.removeAttribute(name);\n    string0 = this.getAttribute(name);\n    string1 = value1 + \"\";\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : (string10 = string1, interpolate0 = interpolate(string00 = string0, value1));\n  };\n}\n\nfunction attrFunctionNS(fullname, interpolate, value) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0, value1 = value(this), string1;\n    if (value1 == null) return void this.removeAttributeNS(fullname.space, fullname.local);\n    string0 = this.getAttributeNS(fullname.space, fullname.local);\n    string1 = value1 + \"\";\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : (string10 = string1, interpolate0 = interpolate(string00 = string0, value1));\n  };\n}\n\nexport default function(name, value) {\n  var fullname = namespace(name), i = fullname === \"transform\" ? interpolateTransform : interpolate;\n  return this.attrTween(name, typeof value === \"function\"\n      ? (fullname.local ? attrFunctionNS : attrFunction)(fullname, i, tweenValue(this, \"attr.\" + name, value))\n      : value == null ? (fullname.local ? attrRemoveNS : attrRemove)(fullname)\n      : (fullname.local ? attrConstantNS : attrConstant)(fullname, i, value));\n}\n", "import {namespace} from \"d3-selection\";\n\nfunction attrInterpolate(name, i) {\n  return function(t) {\n    this.setAttribute(name, i.call(this, t));\n  };\n}\n\nfunction attrInterpolateNS(fullname, i) {\n  return function(t) {\n    this.setAttributeNS(fullname.space, fullname.local, i.call(this, t));\n  };\n}\n\nfunction attrTweenNS(fullname, value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && attrInterpolateNS(fullname, i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\n\nfunction attrTween(name, value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && attrInterpolate(name, i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\n\nexport default function(name, value) {\n  var key = \"attr.\" + name;\n  if (arguments.length < 2) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error;\n  var fullname = namespace(name);\n  return this.tween(key, (fullname.local ? attrTweenNS : attrTween)(fullname, value));\n}\n", "import {get, init} from \"./schedule.js\";\n\nfunction delayFunction(id, value) {\n  return function() {\n    init(this, id).delay = +value.apply(this, arguments);\n  };\n}\n\nfunction delayConstant(id, value) {\n  return value = +value, function() {\n    init(this, id).delay = value;\n  };\n}\n\nexport default function(value) {\n  var id = this._id;\n\n  return arguments.length\n      ? this.each((typeof value === \"function\"\n          ? delayFunction\n          : delayConstant)(id, value))\n      : get(this.node(), id).delay;\n}\n", "import {get, set} from \"./schedule.js\";\n\nfunction durationFunction(id, value) {\n  return function() {\n    set(this, id).duration = +value.apply(this, arguments);\n  };\n}\n\nfunction durationConstant(id, value) {\n  return value = +value, function() {\n    set(this, id).duration = value;\n  };\n}\n\nexport default function(value) {\n  var id = this._id;\n\n  return arguments.length\n      ? this.each((typeof value === \"function\"\n          ? durationFunction\n          : durationConstant)(id, value))\n      : get(this.node(), id).duration;\n}\n", "import {get, set} from \"./schedule.js\";\n\nfunction easeConstant(id, value) {\n  if (typeof value !== \"function\") throw new Error;\n  return function() {\n    set(this, id).ease = value;\n  };\n}\n\nexport default function(value) {\n  var id = this._id;\n\n  return arguments.length\n      ? this.each(easeConstant(id, value))\n      : get(this.node(), id).ease;\n}\n", "import {set} from \"./schedule.js\";\n\nfunction easeVarying(id, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (typeof v !== \"function\") throw new Error;\n    set(this, id).ease = v;\n  };\n}\n\nexport default function(value) {\n  if (typeof value !== \"function\") throw new Error;\n  return this.each(easeVarying(this._id, value));\n}\n", "import {matcher} from \"d3-selection\";\nimport {Transition} from \"./index.js\";\n\nexport default function(match) {\n  if (typeof match !== \"function\") match = matcher(match);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = [], node, i = 0; i < n; ++i) {\n      if ((node = group[i]) && match.call(node, node.__data__, i, group)) {\n        subgroup.push(node);\n      }\n    }\n  }\n\n  return new Transition(subgroups, this._parents, this._name, this._id);\n}\n", "import {Transition} from \"./index.js\";\n\nexport default function(transition) {\n  if (transition._id !== this._id) throw new Error;\n\n  for (var groups0 = this._groups, groups1 = transition._groups, m0 = groups0.length, m1 = groups1.length, m = Math.min(m0, m1), merges = new Array(m0), j = 0; j < m; ++j) {\n    for (var group0 = groups0[j], group1 = groups1[j], n = group0.length, merge = merges[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group0[i] || group1[i]) {\n        merge[i] = node;\n      }\n    }\n  }\n\n  for (; j < m0; ++j) {\n    merges[j] = groups0[j];\n  }\n\n  return new Transition(merges, this._parents, this._name, this._id);\n}\n", "import {get, set, init} from \"./schedule.js\";\n\nfunction start(name) {\n  return (name + \"\").trim().split(/^|\\s+/).every(function(t) {\n    var i = t.indexOf(\".\");\n    if (i >= 0) t = t.slice(0, i);\n    return !t || t === \"start\";\n  });\n}\n\nfunction onFunction(id, name, listener) {\n  var on0, on1, sit = start(name) ? init : set;\n  return function() {\n    var schedule = sit(this, id),\n        on = schedule.on;\n\n    // If this node shared a dispatch with the previous node,\n    // just assign the updated shared dispatch and we’re done!\n    // Otherwise, copy-on-write.\n    if (on !== on0) (on1 = (on0 = on).copy()).on(name, listener);\n\n    schedule.on = on1;\n  };\n}\n\nexport default function(name, listener) {\n  var id = this._id;\n\n  return arguments.length < 2\n      ? get(this.node(), id).on.on(name)\n      : this.each(onFunction(id, name, listener));\n}\n", "function removeFunction(id) {\n  return function() {\n    var parent = this.parentNode;\n    for (var i in this.__transition) if (+i !== id) return;\n    if (parent) parent.removeChild(this);\n  };\n}\n\nexport default function() {\n  return this.on(\"end.remove\", removeFunction(this._id));\n}\n", "import {selector} from \"d3-selection\";\nimport {Transition} from \"./index.js\";\nimport schedule, {get} from \"./schedule.js\";\n\nexport default function(select) {\n  var name = this._name,\n      id = this._id;\n\n  if (typeof select !== \"function\") select = selector(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = new Array(n), node, subnode, i = 0; i < n; ++i) {\n      if ((node = group[i]) && (subnode = select.call(node, node.__data__, i, group))) {\n        if (\"__data__\" in node) subnode.__data__ = node.__data__;\n        subgroup[i] = subnode;\n        schedule(subgroup[i], name, id, i, subgroup, get(node, id));\n      }\n    }\n  }\n\n  return new Transition(subgroups, this._parents, name, id);\n}\n", "import {selectorAll} from \"d3-selection\";\nimport {Transition} from \"./index.js\";\nimport schedule, {get} from \"./schedule.js\";\n\nexport default function(select) {\n  var name = this._name,\n      id = this._id;\n\n  if (typeof select !== \"function\") select = selectorAll(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = [], parents = [], j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        for (var children = select.call(node, node.__data__, i, group), child, inherit = get(node, id), k = 0, l = children.length; k < l; ++k) {\n          if (child = children[k]) {\n            schedule(child, name, id, k, children, inherit);\n          }\n        }\n        subgroups.push(children);\n        parents.push(node);\n      }\n    }\n  }\n\n  return new Transition(subgroups, parents, name, id);\n}\n", "import {selection} from \"d3-selection\";\n\nvar Selection = selection.prototype.constructor;\n\nexport default function() {\n  return new Selection(this._groups, this._parents);\n}\n", "import {interpolateTransformCss as interpolateTransform} from \"d3-interpolate\";\nimport {style} from \"d3-selection\";\nimport {set} from \"./schedule.js\";\nimport {tweenValue} from \"./tween.js\";\nimport interpolate from \"./interpolate.js\";\n\nfunction styleNull(name, interpolate) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0 = style(this, name),\n        string1 = (this.style.removeProperty(name), style(this, name));\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, string10 = string1);\n  };\n}\n\nfunction styleRemove(name) {\n  return function() {\n    this.style.removeProperty(name);\n  };\n}\n\nfunction styleConstant(name, interpolate, value1) {\n  var string00,\n      string1 = value1 + \"\",\n      interpolate0;\n  return function() {\n    var string0 = style(this, name);\n    return string0 === string1 ? null\n        : string0 === string00 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, value1);\n  };\n}\n\nfunction styleFunction(name, interpolate, value) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0 = style(this, name),\n        value1 = value(this),\n        string1 = value1 + \"\";\n    if (value1 == null) string1 = value1 = (this.style.removeProperty(name), style(this, name));\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : (string10 = string1, interpolate0 = interpolate(string00 = string0, value1));\n  };\n}\n\nfunction styleMaybeRemove(id, name) {\n  var on0, on1, listener0, key = \"style.\" + name, event = \"end.\" + key, remove;\n  return function() {\n    var schedule = set(this, id),\n        on = schedule.on,\n        listener = schedule.value[key] == null ? remove || (remove = styleRemove(name)) : undefined;\n\n    // If this node shared a dispatch with the previous node,\n    // just assign the updated shared dispatch and we’re done!\n    // Otherwise, copy-on-write.\n    if (on !== on0 || listener0 !== listener) (on1 = (on0 = on).copy()).on(event, listener0 = listener);\n\n    schedule.on = on1;\n  };\n}\n\nexport default function(name, value, priority) {\n  var i = (name += \"\") === \"transform\" ? interpolateTransform : interpolate;\n  return value == null ? this\n      .styleTween(name, styleNull(name, i))\n      .on(\"end.style.\" + name, styleRemove(name))\n    : typeof value === \"function\" ? this\n      .styleTween(name, styleFunction(name, i, tweenValue(this, \"style.\" + name, value)))\n      .each(styleMaybeRemove(this._id, name))\n    : this\n      .styleTween(name, styleConstant(name, i, value), priority)\n      .on(\"end.style.\" + name, null);\n}\n", "function styleInterpolate(name, i, priority) {\n  return function(t) {\n    this.style.setProperty(name, i.call(this, t), priority);\n  };\n}\n\nfunction styleTween(name, value, priority) {\n  var t, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t = (i0 = i) && styleInterpolate(name, i, priority);\n    return t;\n  }\n  tween._value = value;\n  return tween;\n}\n\nexport default function(name, value, priority) {\n  var key = \"style.\" + (name += \"\");\n  if (arguments.length < 2) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error;\n  return this.tween(key, styleTween(name, value, priority == null ? \"\" : priority));\n}\n", "import {tweenValue} from \"./tween.js\";\n\nfunction textConstant(value) {\n  return function() {\n    this.textContent = value;\n  };\n}\n\nfunction textFunction(value) {\n  return function() {\n    var value1 = value(this);\n    this.textContent = value1 == null ? \"\" : value1;\n  };\n}\n\nexport default function(value) {\n  return this.tween(\"text\", typeof value === \"function\"\n      ? textFunction(tweenValue(this, \"text\", value))\n      : textConstant(value == null ? \"\" : value + \"\"));\n}\n", "function textInterpolate(i) {\n  return function(t) {\n    this.textContent = i.call(this, t);\n  };\n}\n\nfunction textTween(value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && textInterpolate(i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\n\nexport default function(value) {\n  var key = \"text\";\n  if (arguments.length < 1) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error;\n  return this.tween(key, textTween(value));\n}\n", "import {Transition, newId} from \"./index.js\";\nimport schedule, {get} from \"./schedule.js\";\n\nexport default function() {\n  var name = this._name,\n      id0 = this._id,\n      id1 = newId();\n\n  for (var groups = this._groups, m = groups.length, j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        var inherit = get(node, id0);\n        schedule(node, name, id1, i, group, {\n          time: inherit.time + inherit.delay + inherit.duration,\n          delay: 0,\n          duration: inherit.duration,\n          ease: inherit.ease\n        });\n      }\n    }\n  }\n\n  return new Transition(groups, this._parents, name, id1);\n}\n", "import {set} from \"./schedule.js\";\n\nexport default function() {\n  var on0, on1, that = this, id = that._id, size = that.size();\n  return new Promise(function(resolve, reject) {\n    var cancel = {value: reject},\n        end = {value: function() { if (--size === 0) resolve(); }};\n\n    that.each(function() {\n      var schedule = set(this, id),\n          on = schedule.on;\n\n      // If this node shared a dispatch with the previous node,\n      // just assign the updated shared dispatch and we’re done!\n      // Otherwise, copy-on-write.\n      if (on !== on0) {\n        on1 = (on0 = on).copy();\n        on1._.cancel.push(cancel);\n        on1._.interrupt.push(cancel);\n        on1._.end.push(end);\n      }\n\n      schedule.on = on1;\n    });\n\n    // The selection was empty, resolve end immediately\n    if (size === 0) resolve();\n  });\n}\n", "import {selection} from \"d3-selection\";\nimport transition_attr from \"./attr.js\";\nimport transition_attrTween from \"./attrTween.js\";\nimport transition_delay from \"./delay.js\";\nimport transition_duration from \"./duration.js\";\nimport transition_ease from \"./ease.js\";\nimport transition_easeVarying from \"./easeVarying.js\";\nimport transition_filter from \"./filter.js\";\nimport transition_merge from \"./merge.js\";\nimport transition_on from \"./on.js\";\nimport transition_remove from \"./remove.js\";\nimport transition_select from \"./select.js\";\nimport transition_selectAll from \"./selectAll.js\";\nimport transition_selection from \"./selection.js\";\nimport transition_style from \"./style.js\";\nimport transition_styleTween from \"./styleTween.js\";\nimport transition_text from \"./text.js\";\nimport transition_textTween from \"./textTween.js\";\nimport transition_transition from \"./transition.js\";\nimport transition_tween from \"./tween.js\";\nimport transition_end from \"./end.js\";\n\nvar id = 0;\n\nexport function Transition(groups, parents, name, id) {\n  this._groups = groups;\n  this._parents = parents;\n  this._name = name;\n  this._id = id;\n}\n\nexport default function transition(name) {\n  return selection().transition(name);\n}\n\nexport function newId() {\n  return ++id;\n}\n\nvar selection_prototype = selection.prototype;\n\nTransition.prototype = transition.prototype = {\n  constructor: Transition,\n  select: transition_select,\n  selectAll: transition_selectAll,\n  selectChild: selection_prototype.selectChild,\n  selectChildren: selection_prototype.selectChildren,\n  filter: transition_filter,\n  merge: transition_merge,\n  selection: transition_selection,\n  transition: transition_transition,\n  call: selection_prototype.call,\n  nodes: selection_prototype.nodes,\n  node: selection_prototype.node,\n  size: selection_prototype.size,\n  empty: selection_prototype.empty,\n  each: selection_prototype.each,\n  on: transition_on,\n  attr: transition_attr,\n  attrTween: transition_attrTween,\n  style: transition_style,\n  styleTween: transition_styleTween,\n  text: transition_text,\n  textTween: transition_textTween,\n  remove: transition_remove,\n  tween: transition_tween,\n  delay: transition_delay,\n  duration: transition_duration,\n  ease: transition_ease,\n  easeVarying: transition_easeVarying,\n  end: transition_end,\n  [Symbol.iterator]: selection_prototype[Symbol.iterator]\n};\n", "export const linear = t => +t;\n", "export function quadIn(t) {\n  return t * t;\n}\n\nexport function quadOut(t) {\n  return t * (2 - t);\n}\n\nexport function quadInOut(t) {\n  return ((t *= 2) <= 1 ? t * t : --t * (2 - t) + 1) / 2;\n}\n", "export function cubicIn(t) {\n  return t * t * t;\n}\n\nexport function cubicOut(t) {\n  return --t * t * t + 1;\n}\n\nexport function cubicInOut(t) {\n  return ((t *= 2) <= 1 ? t * t * t : (t -= 2) * t * t + 2) / 2;\n}\n", "var exponent = 3;\n\nexport var polyIn = (function custom(e) {\n  e = +e;\n\n  function polyIn(t) {\n    return Math.pow(t, e);\n  }\n\n  polyIn.exponent = custom;\n\n  return polyIn;\n})(exponent);\n\nexport var polyOut = (function custom(e) {\n  e = +e;\n\n  function polyOut(t) {\n    return 1 - Math.pow(1 - t, e);\n  }\n\n  polyOut.exponent = custom;\n\n  return polyOut;\n})(exponent);\n\nexport var polyInOut = (function custom(e) {\n  e = +e;\n\n  function polyInOut(t) {\n    return ((t *= 2) <= 1 ? Math.pow(t, e) : 2 - Math.pow(2 - t, e)) / 2;\n  }\n\n  polyInOut.exponent = custom;\n\n  return polyInOut;\n})(exponent);\n", "var pi = Math.PI,\n    halfPi = pi / 2;\n\nexport function sinIn(t) {\n  return (+t === 1) ? 1 : 1 - Math.cos(t * halfPi);\n}\n\nexport function sinOut(t) {\n  return Math.sin(t * halfPi);\n}\n\nexport function sinInOut(t) {\n  return (1 - Math.cos(pi * t)) / 2;\n}\n", "// tpmt is two power minus ten times t scaled to [0,1]\nexport function tpmt(x) {\n  return (Math.pow(2, -10 * x) - 0.0009765625) * 1.0009775171065494;\n}\n", "import {tpmt} from \"./math.js\";\n\nexport function expIn(t) {\n  return tpmt(1 - +t);\n}\n\nexport function expOut(t) {\n  return 1 - tpmt(t);\n}\n\nexport function expInOut(t) {\n  return ((t *= 2) <= 1 ? tpmt(1 - t) : 2 - tpmt(t - 1)) / 2;\n}\n", "export function circleIn(t) {\n  return 1 - Math.sqrt(1 - t * t);\n}\n\nexport function circleOut(t) {\n  return Math.sqrt(1 - --t * t);\n}\n\nexport function circleInOut(t) {\n  return ((t *= 2) <= 1 ? 1 - Math.sqrt(1 - t * t) : Math.sqrt(1 - (t -= 2) * t) + 1) / 2;\n}\n", "var b1 = 4 / 11,\n    b2 = 6 / 11,\n    b3 = 8 / 11,\n    b4 = 3 / 4,\n    b5 = 9 / 11,\n    b6 = 10 / 11,\n    b7 = 15 / 16,\n    b8 = 21 / 22,\n    b9 = 63 / 64,\n    b0 = 1 / b1 / b1;\n\nexport function bounceIn(t) {\n  return 1 - bounceOut(1 - t);\n}\n\nexport function bounceOut(t) {\n  return (t = +t) < b1 ? b0 * t * t : t < b3 ? b0 * (t -= b2) * t + b4 : t < b6 ? b0 * (t -= b5) * t + b7 : b0 * (t -= b8) * t + b9;\n}\n\nexport function bounceInOut(t) {\n  return ((t *= 2) <= 1 ? 1 - bounceOut(1 - t) : bounceOut(t - 1) + 1) / 2;\n}\n", "var overshoot = 1.70158;\n\nexport var backIn = (function custom(s) {\n  s = +s;\n\n  function backIn(t) {\n    return (t = +t) * t * (s * (t - 1) + t);\n  }\n\n  backIn.overshoot = custom;\n\n  return backIn;\n})(overshoot);\n\nexport var backOut = (function custom(s) {\n  s = +s;\n\n  function backOut(t) {\n    return --t * t * ((t + 1) * s + t) + 1;\n  }\n\n  backOut.overshoot = custom;\n\n  return backOut;\n})(overshoot);\n\nexport var backInOut = (function custom(s) {\n  s = +s;\n\n  function backInOut(t) {\n    return ((t *= 2) < 1 ? t * t * ((s + 1) * t - s) : (t -= 2) * t * ((s + 1) * t + s) + 2) / 2;\n  }\n\n  backInOut.overshoot = custom;\n\n  return backInOut;\n})(overshoot);\n", "import {tpmt} from \"./math.js\";\n\nvar tau = 2 * Math.PI,\n    amplitude = 1,\n    period = 0.3;\n\nexport var elasticIn = (function custom(a, p) {\n  var s = Math.asin(1 / (a = Math.max(1, a))) * (p /= tau);\n\n  function elasticIn(t) {\n    return a * tpmt(-(--t)) * Math.sin((s - t) / p);\n  }\n\n  elasticIn.amplitude = function(a) { return custom(a, p * tau); };\n  elasticIn.period = function(p) { return custom(a, p); };\n\n  return elasticIn;\n})(amplitude, period);\n\nexport var elasticOut = (function custom(a, p) {\n  var s = Math.asin(1 / (a = Math.max(1, a))) * (p /= tau);\n\n  function elasticOut(t) {\n    return 1 - a * tpmt(t = +t) * Math.sin((t + s) / p);\n  }\n\n  elasticOut.amplitude = function(a) { return custom(a, p * tau); };\n  elasticOut.period = function(p) { return custom(a, p); };\n\n  return elasticOut;\n})(amplitude, period);\n\nexport var elasticInOut = (function custom(a, p) {\n  var s = Math.asin(1 / (a = Math.max(1, a))) * (p /= tau);\n\n  function elasticInOut(t) {\n    return ((t = t * 2 - 1) < 0\n        ? a * tpmt(-t) * Math.sin((s - t) / p)\n        : 2 - a * tpmt(t) * Math.sin((s + t) / p)) / 2;\n  }\n\n  elasticInOut.amplitude = function(a) { return custom(a, p * tau); };\n  elasticInOut.period = function(p) { return custom(a, p); };\n\n  return elasticInOut;\n})(amplitude, period);\n", "import {Transition, newId} from \"../transition/index.js\";\nimport schedule from \"../transition/schedule.js\";\nimport {easeCubicInOut} from \"d3-ease\";\nimport {now} from \"d3-timer\";\n\nvar defaultTiming = {\n  time: null, // Set on use.\n  delay: 0,\n  duration: 250,\n  ease: easeCubicInOut\n};\n\nfunction inherit(node, id) {\n  var timing;\n  while (!(timing = node.__transition) || !(timing = timing[id])) {\n    if (!(node = node.parentNode)) {\n      throw new Error(`transition ${id} not found`);\n    }\n  }\n  return timing;\n}\n\nexport default function(name) {\n  var id,\n      timing;\n\n  if (name instanceof Transition) {\n    id = name._id, name = name._name;\n  } else {\n    id = newId(), (timing = defaultTiming).time = now(), name = name == null ? null : name + \"\";\n  }\n\n  for (var groups = this._groups, m = groups.length, j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        schedule(node, name, id, i, group, timing || inherit(node, id));\n      }\n    }\n  }\n\n  return new Transition(groups, this._parents, name, id);\n}\n", "import {selection} from \"d3-selection\";\nimport selection_interrupt from \"./interrupt.js\";\nimport selection_transition from \"./transition.js\";\n\nselection.prototype.interrupt = selection_interrupt;\nselection.prototype.transition = selection_transition;\n", "import {Transition} from \"./transition/index.js\";\nimport {SCHEDULED} from \"./transition/schedule.js\";\n\nvar root = [null];\n\nexport default function(node, name) {\n  var schedules = node.__transition,\n      schedule,\n      i;\n\n  if (schedules) {\n    name = name == null ? null : name + \"\";\n    for (i in schedules) {\n      if ((schedule = schedules[i]).state > SCHEDULED && schedule.name === name) {\n        return new Transition([[node]], root, name, +i);\n      }\n    }\n  }\n\n  return null;\n}\n", "export default x => () => x;\n", "export default function BrushEvent(type, {\n  sourceEvent,\n  target,\n  selection,\n  mode,\n  dispatch\n}) {\n  Object.defineProperties(this, {\n    type: {value: type, enumerable: true, configurable: true},\n    sourceEvent: {value: sourceEvent, enumerable: true, configurable: true},\n    target: {value: target, enumerable: true, configurable: true},\n    selection: {value: selection, enumerable: true, configurable: true},\n    mode: {value: mode, enumerable: true, configurable: true},\n    _: {value: dispatch}\n  });\n}\n", "export function nopropagation(event) {\n  event.stopImmediatePropagation();\n}\n\nexport default function(event) {\n  event.preventDefault();\n  event.stopImmediatePropagation();\n}\n", "import {dispatch} from \"d3-dispatch\";\nimport {dragDisable, dragEnable} from \"d3-drag\";\nimport {interpolate} from \"d3-interpolate\";\nimport {pointer, select} from \"d3-selection\";\nimport {interrupt} from \"d3-transition\";\nimport constant from \"./constant.js\";\nimport BrushEvent from \"./event.js\";\nimport noevent, {nopropagation} from \"./noevent.js\";\n\nvar MODE_DRAG = {name: \"drag\"},\n    MODE_SPACE = {name: \"space\"},\n    MODE_HANDLE = {name: \"handle\"},\n    MODE_CENTER = {name: \"center\"};\n\nconst {abs, max, min} = Math;\n\nfunction number1(e) {\n  return [+e[0], +e[1]];\n}\n\nfunction number2(e) {\n  return [number1(e[0]), number1(e[1])];\n}\n\nvar X = {\n  name: \"x\",\n  handles: [\"w\", \"e\"].map(type),\n  input: function(x, e) { return x == null ? null : [[+x[0], e[0][1]], [+x[1], e[1][1]]]; },\n  output: function(xy) { return xy && [xy[0][0], xy[1][0]]; }\n};\n\nvar Y = {\n  name: \"y\",\n  handles: [\"n\", \"s\"].map(type),\n  input: function(y, e) { return y == null ? null : [[e[0][0], +y[0]], [e[1][0], +y[1]]]; },\n  output: function(xy) { return xy && [xy[0][1], xy[1][1]]; }\n};\n\nvar XY = {\n  name: \"xy\",\n  handles: [\"n\", \"w\", \"e\", \"s\", \"nw\", \"ne\", \"sw\", \"se\"].map(type),\n  input: function(xy) { return xy == null ? null : number2(xy); },\n  output: function(xy) { return xy; }\n};\n\nvar cursors = {\n  overlay: \"crosshair\",\n  selection: \"move\",\n  n: \"ns-resize\",\n  e: \"ew-resize\",\n  s: \"ns-resize\",\n  w: \"ew-resize\",\n  nw: \"nwse-resize\",\n  ne: \"nesw-resize\",\n  se: \"nwse-resize\",\n  sw: \"nesw-resize\"\n};\n\nvar flipX = {\n  e: \"w\",\n  w: \"e\",\n  nw: \"ne\",\n  ne: \"nw\",\n  se: \"sw\",\n  sw: \"se\"\n};\n\nvar flipY = {\n  n: \"s\",\n  s: \"n\",\n  nw: \"sw\",\n  ne: \"se\",\n  se: \"ne\",\n  sw: \"nw\"\n};\n\nvar signsX = {\n  overlay: +1,\n  selection: +1,\n  n: null,\n  e: +1,\n  s: null,\n  w: -1,\n  nw: -1,\n  ne: +1,\n  se: +1,\n  sw: -1\n};\n\nvar signsY = {\n  overlay: +1,\n  selection: +1,\n  n: -1,\n  e: null,\n  s: +1,\n  w: null,\n  nw: -1,\n  ne: -1,\n  se: +1,\n  sw: +1\n};\n\nfunction type(t) {\n  return {type: t};\n}\n\n// Ignore right-click, since that should open the context menu.\nfunction defaultFilter(event) {\n  return !event.ctrlKey && !event.button;\n}\n\nfunction defaultExtent() {\n  var svg = this.ownerSVGElement || this;\n  if (svg.hasAttribute(\"viewBox\")) {\n    svg = svg.viewBox.baseVal;\n    return [[svg.x, svg.y], [svg.x + svg.width, svg.y + svg.height]];\n  }\n  return [[0, 0], [svg.width.baseVal.value, svg.height.baseVal.value]];\n}\n\nfunction defaultTouchable() {\n  return navigator.maxTouchPoints || (\"ontouchstart\" in this);\n}\n\n// Like d3.local, but with the name “__brush” rather than auto-generated.\nfunction local(node) {\n  while (!node.__brush) if (!(node = node.parentNode)) return;\n  return node.__brush;\n}\n\nfunction empty(extent) {\n  return extent[0][0] === extent[1][0]\n      || extent[0][1] === extent[1][1];\n}\n\nexport function brushSelection(node) {\n  var state = node.__brush;\n  return state ? state.dim.output(state.selection) : null;\n}\n\nexport function brushX() {\n  return brush(X);\n}\n\nexport function brushY() {\n  return brush(Y);\n}\n\nexport default function() {\n  return brush(XY);\n}\n\nfunction brush(dim) {\n  var extent = defaultExtent,\n      filter = defaultFilter,\n      touchable = defaultTouchable,\n      keys = true,\n      listeners = dispatch(\"start\", \"brush\", \"end\"),\n      handleSize = 6,\n      touchending;\n\n  function brush(group) {\n    var overlay = group\n        .property(\"__brush\", initialize)\n      .selectAll(\".overlay\")\n      .data([type(\"overlay\")]);\n\n    overlay.enter().append(\"rect\")\n        .attr(\"class\", \"overlay\")\n        .attr(\"pointer-events\", \"all\")\n        .attr(\"cursor\", cursors.overlay)\n      .merge(overlay)\n        .each(function() {\n          var extent = local(this).extent;\n          select(this)\n              .attr(\"x\", extent[0][0])\n              .attr(\"y\", extent[0][1])\n              .attr(\"width\", extent[1][0] - extent[0][0])\n              .attr(\"height\", extent[1][1] - extent[0][1]);\n        });\n\n    group.selectAll(\".selection\")\n      .data([type(\"selection\")])\n      .enter().append(\"rect\")\n        .attr(\"class\", \"selection\")\n        .attr(\"cursor\", cursors.selection)\n        .attr(\"fill\", \"#777\")\n        .attr(\"fill-opacity\", 0.3)\n        .attr(\"stroke\", \"#fff\")\n        .attr(\"shape-rendering\", \"crispEdges\");\n\n    var handle = group.selectAll(\".handle\")\n      .data(dim.handles, function(d) { return d.type; });\n\n    handle.exit().remove();\n\n    handle.enter().append(\"rect\")\n        .attr(\"class\", function(d) { return \"handle handle--\" + d.type; })\n        .attr(\"cursor\", function(d) { return cursors[d.type]; });\n\n    group\n        .each(redraw)\n        .attr(\"fill\", \"none\")\n        .attr(\"pointer-events\", \"all\")\n        .on(\"mousedown.brush\", started)\n      .filter(touchable)\n        .on(\"touchstart.brush\", started)\n        .on(\"touchmove.brush\", touchmoved)\n        .on(\"touchend.brush touchcancel.brush\", touchended)\n        .style(\"touch-action\", \"none\")\n        .style(\"-webkit-tap-highlight-color\", \"rgba(0,0,0,0)\");\n  }\n\n  brush.move = function(group, selection, event) {\n    if (group.tween) {\n      group\n          .on(\"start.brush\", function(event) { emitter(this, arguments).beforestart().start(event); })\n          .on(\"interrupt.brush end.brush\", function(event) { emitter(this, arguments).end(event); })\n          .tween(\"brush\", function() {\n            var that = this,\n                state = that.__brush,\n                emit = emitter(that, arguments),\n                selection0 = state.selection,\n                selection1 = dim.input(typeof selection === \"function\" ? selection.apply(this, arguments) : selection, state.extent),\n                i = interpolate(selection0, selection1);\n\n            function tween(t) {\n              state.selection = t === 1 && selection1 === null ? null : i(t);\n              redraw.call(that);\n              emit.brush();\n            }\n\n            return selection0 !== null && selection1 !== null ? tween : tween(1);\n          });\n    } else {\n      group\n          .each(function() {\n            var that = this,\n                args = arguments,\n                state = that.__brush,\n                selection1 = dim.input(typeof selection === \"function\" ? selection.apply(that, args) : selection, state.extent),\n                emit = emitter(that, args).beforestart();\n\n            interrupt(that);\n            state.selection = selection1 === null ? null : selection1;\n            redraw.call(that);\n            emit.start(event).brush(event).end(event);\n          });\n    }\n  };\n\n  brush.clear = function(group, event) {\n    brush.move(group, null, event);\n  };\n\n  function redraw() {\n    var group = select(this),\n        selection = local(this).selection;\n\n    if (selection) {\n      group.selectAll(\".selection\")\n          .style(\"display\", null)\n          .attr(\"x\", selection[0][0])\n          .attr(\"y\", selection[0][1])\n          .attr(\"width\", selection[1][0] - selection[0][0])\n          .attr(\"height\", selection[1][1] - selection[0][1]);\n\n      group.selectAll(\".handle\")\n          .style(\"display\", null)\n          .attr(\"x\", function(d) { return d.type[d.type.length - 1] === \"e\" ? selection[1][0] - handleSize / 2 : selection[0][0] - handleSize / 2; })\n          .attr(\"y\", function(d) { return d.type[0] === \"s\" ? selection[1][1] - handleSize / 2 : selection[0][1] - handleSize / 2; })\n          .attr(\"width\", function(d) { return d.type === \"n\" || d.type === \"s\" ? selection[1][0] - selection[0][0] + handleSize : handleSize; })\n          .attr(\"height\", function(d) { return d.type === \"e\" || d.type === \"w\" ? selection[1][1] - selection[0][1] + handleSize : handleSize; });\n    }\n\n    else {\n      group.selectAll(\".selection,.handle\")\n          .style(\"display\", \"none\")\n          .attr(\"x\", null)\n          .attr(\"y\", null)\n          .attr(\"width\", null)\n          .attr(\"height\", null);\n    }\n  }\n\n  function emitter(that, args, clean) {\n    var emit = that.__brush.emitter;\n    return emit && (!clean || !emit.clean) ? emit : new Emitter(that, args, clean);\n  }\n\n  function Emitter(that, args, clean) {\n    this.that = that;\n    this.args = args;\n    this.state = that.__brush;\n    this.active = 0;\n    this.clean = clean;\n  }\n\n  Emitter.prototype = {\n    beforestart: function() {\n      if (++this.active === 1) this.state.emitter = this, this.starting = true;\n      return this;\n    },\n    start: function(event, mode) {\n      if (this.starting) this.starting = false, this.emit(\"start\", event, mode);\n      else this.emit(\"brush\", event);\n      return this;\n    },\n    brush: function(event, mode) {\n      this.emit(\"brush\", event, mode);\n      return this;\n    },\n    end: function(event, mode) {\n      if (--this.active === 0) delete this.state.emitter, this.emit(\"end\", event, mode);\n      return this;\n    },\n    emit: function(type, event, mode) {\n      var d = select(this.that).datum();\n      listeners.call(\n        type,\n        this.that,\n        new BrushEvent(type, {\n          sourceEvent: event,\n          target: brush,\n          selection: dim.output(this.state.selection),\n          mode,\n          dispatch: listeners\n        }),\n        d\n      );\n    }\n  };\n\n  function started(event) {\n    if (touchending && !event.touches) return;\n    if (!filter.apply(this, arguments)) return;\n\n    var that = this,\n        type = event.target.__data__.type,\n        mode = (keys && event.metaKey ? type = \"overlay\" : type) === \"selection\" ? MODE_DRAG : (keys && event.altKey ? MODE_CENTER : MODE_HANDLE),\n        signX = dim === Y ? null : signsX[type],\n        signY = dim === X ? null : signsY[type],\n        state = local(that),\n        extent = state.extent,\n        selection = state.selection,\n        W = extent[0][0], w0, w1,\n        N = extent[0][1], n0, n1,\n        E = extent[1][0], e0, e1,\n        S = extent[1][1], s0, s1,\n        dx = 0,\n        dy = 0,\n        moving,\n        shifting = signX && signY && keys && event.shiftKey,\n        lockX,\n        lockY,\n        points = Array.from(event.touches || [event], t => {\n          const i = t.identifier;\n          t = pointer(t, that);\n          t.point0 = t.slice();\n          t.identifier = i;\n          return t;\n        });\n\n    interrupt(that);\n    var emit = emitter(that, arguments, true).beforestart();\n\n    if (type === \"overlay\") {\n      if (selection) moving = true;\n      const pts = [points[0], points[1] || points[0]];\n      state.selection = selection = [[\n          w0 = dim === Y ? W : min(pts[0][0], pts[1][0]),\n          n0 = dim === X ? N : min(pts[0][1], pts[1][1])\n        ], [\n          e0 = dim === Y ? E : max(pts[0][0], pts[1][0]),\n          s0 = dim === X ? S : max(pts[0][1], pts[1][1])\n        ]];\n      if (points.length > 1) move(event);\n    } else {\n      w0 = selection[0][0];\n      n0 = selection[0][1];\n      e0 = selection[1][0];\n      s0 = selection[1][1];\n    }\n\n    w1 = w0;\n    n1 = n0;\n    e1 = e0;\n    s1 = s0;\n\n    var group = select(that)\n        .attr(\"pointer-events\", \"none\");\n\n    var overlay = group.selectAll(\".overlay\")\n        .attr(\"cursor\", cursors[type]);\n\n    if (event.touches) {\n      emit.moved = moved;\n      emit.ended = ended;\n    } else {\n      var view = select(event.view)\n          .on(\"mousemove.brush\", moved, true)\n          .on(\"mouseup.brush\", ended, true);\n      if (keys) view\n          .on(\"keydown.brush\", keydowned, true)\n          .on(\"keyup.brush\", keyupped, true)\n\n      dragDisable(event.view);\n    }\n\n    redraw.call(that);\n    emit.start(event, mode.name);\n\n    function moved(event) {\n      for (const p of event.changedTouches || [event]) {\n        for (const d of points)\n          if (d.identifier === p.identifier) d.cur = pointer(p, that);\n      }\n      if (shifting && !lockX && !lockY && points.length === 1) {\n        const point = points[0];\n        if (abs(point.cur[0] - point[0]) > abs(point.cur[1] - point[1]))\n          lockY = true;\n        else\n          lockX = true;\n      }\n      for (const point of points)\n        if (point.cur) point[0] = point.cur[0], point[1] = point.cur[1];\n      moving = true;\n      noevent(event);\n      move(event);\n    }\n\n    function move(event) {\n      const point = points[0], point0 = point.point0;\n      var t;\n\n      dx = point[0] - point0[0];\n      dy = point[1] - point0[1];\n\n      switch (mode) {\n        case MODE_SPACE:\n        case MODE_DRAG: {\n          if (signX) dx = max(W - w0, min(E - e0, dx)), w1 = w0 + dx, e1 = e0 + dx;\n          if (signY) dy = max(N - n0, min(S - s0, dy)), n1 = n0 + dy, s1 = s0 + dy;\n          break;\n        }\n        case MODE_HANDLE: {\n          if (points[1]) {\n            if (signX) w1 = max(W, min(E, points[0][0])), e1 = max(W, min(E, points[1][0])), signX = 1;\n            if (signY) n1 = max(N, min(S, points[0][1])), s1 = max(N, min(S, points[1][1])), signY = 1;\n          } else {\n            if (signX < 0) dx = max(W - w0, min(E - w0, dx)), w1 = w0 + dx, e1 = e0;\n            else if (signX > 0) dx = max(W - e0, min(E - e0, dx)), w1 = w0, e1 = e0 + dx;\n            if (signY < 0) dy = max(N - n0, min(S - n0, dy)), n1 = n0 + dy, s1 = s0;\n            else if (signY > 0) dy = max(N - s0, min(S - s0, dy)), n1 = n0, s1 = s0 + dy;\n          }\n          break;\n        }\n        case MODE_CENTER: {\n          if (signX) w1 = max(W, min(E, w0 - dx * signX)), e1 = max(W, min(E, e0 + dx * signX));\n          if (signY) n1 = max(N, min(S, n0 - dy * signY)), s1 = max(N, min(S, s0 + dy * signY));\n          break;\n        }\n      }\n\n      if (e1 < w1) {\n        signX *= -1;\n        t = w0, w0 = e0, e0 = t;\n        t = w1, w1 = e1, e1 = t;\n        if (type in flipX) overlay.attr(\"cursor\", cursors[type = flipX[type]]);\n      }\n\n      if (s1 < n1) {\n        signY *= -1;\n        t = n0, n0 = s0, s0 = t;\n        t = n1, n1 = s1, s1 = t;\n        if (type in flipY) overlay.attr(\"cursor\", cursors[type = flipY[type]]);\n      }\n\n      if (state.selection) selection = state.selection; // May be set by brush.move!\n      if (lockX) w1 = selection[0][0], e1 = selection[1][0];\n      if (lockY) n1 = selection[0][1], s1 = selection[1][1];\n\n      if (selection[0][0] !== w1\n          || selection[0][1] !== n1\n          || selection[1][0] !== e1\n          || selection[1][1] !== s1) {\n        state.selection = [[w1, n1], [e1, s1]];\n        redraw.call(that);\n        emit.brush(event, mode.name);\n      }\n    }\n\n    function ended(event) {\n      nopropagation(event);\n      if (event.touches) {\n        if (event.touches.length) return;\n        if (touchending) clearTimeout(touchending);\n        touchending = setTimeout(function() { touchending = null; }, 500); // Ghost clicks are delayed!\n      } else {\n        dragEnable(event.view, moving);\n        view.on(\"keydown.brush keyup.brush mousemove.brush mouseup.brush\", null);\n      }\n      group.attr(\"pointer-events\", \"all\");\n      overlay.attr(\"cursor\", cursors.overlay);\n      if (state.selection) selection = state.selection; // May be set by brush.move (on start)!\n      if (empty(selection)) state.selection = null, redraw.call(that);\n      emit.end(event, mode.name);\n    }\n\n    function keydowned(event) {\n      switch (event.keyCode) {\n        case 16: { // SHIFT\n          shifting = signX && signY;\n          break;\n        }\n        case 18: { // ALT\n          if (mode === MODE_HANDLE) {\n            if (signX) e0 = e1 - dx * signX, w0 = w1 + dx * signX;\n            if (signY) s0 = s1 - dy * signY, n0 = n1 + dy * signY;\n            mode = MODE_CENTER;\n            move(event);\n          }\n          break;\n        }\n        case 32: { // SPACE; takes priority over ALT\n          if (mode === MODE_HANDLE || mode === MODE_CENTER) {\n            if (signX < 0) e0 = e1 - dx; else if (signX > 0) w0 = w1 - dx;\n            if (signY < 0) s0 = s1 - dy; else if (signY > 0) n0 = n1 - dy;\n            mode = MODE_SPACE;\n            overlay.attr(\"cursor\", cursors.selection);\n            move(event);\n          }\n          break;\n        }\n        default: return;\n      }\n      noevent(event);\n    }\n\n    function keyupped(event) {\n      switch (event.keyCode) {\n        case 16: { // SHIFT\n          if (shifting) {\n            lockX = lockY = shifting = false;\n            move(event);\n          }\n          break;\n        }\n        case 18: { // ALT\n          if (mode === MODE_CENTER) {\n            if (signX < 0) e0 = e1; else if (signX > 0) w0 = w1;\n            if (signY < 0) s0 = s1; else if (signY > 0) n0 = n1;\n            mode = MODE_HANDLE;\n            move(event);\n          }\n          break;\n        }\n        case 32: { // SPACE\n          if (mode === MODE_SPACE) {\n            if (event.altKey) {\n              if (signX) e0 = e1 - dx * signX, w0 = w1 + dx * signX;\n              if (signY) s0 = s1 - dy * signY, n0 = n1 + dy * signY;\n              mode = MODE_CENTER;\n            } else {\n              if (signX < 0) e0 = e1; else if (signX > 0) w0 = w1;\n              if (signY < 0) s0 = s1; else if (signY > 0) n0 = n1;\n              mode = MODE_HANDLE;\n            }\n            overlay.attr(\"cursor\", cursors[type]);\n            move(event);\n          }\n          break;\n        }\n        default: return;\n      }\n      noevent(event);\n    }\n  }\n\n  function touchmoved(event) {\n    emitter(this, arguments).moved(event);\n  }\n\n  function touchended(event) {\n    emitter(this, arguments).ended(event);\n  }\n\n  function initialize() {\n    var state = this.__brush || {selection: null};\n    state.extent = number2(extent.apply(this, arguments));\n    state.dim = dim;\n    return state;\n  }\n\n  brush.extent = function(_) {\n    return arguments.length ? (extent = typeof _ === \"function\" ? _ : constant(number2(_)), brush) : extent;\n  };\n\n  brush.filter = function(_) {\n    return arguments.length ? (filter = typeof _ === \"function\" ? _ : constant(!!_), brush) : filter;\n  };\n\n  brush.touchable = function(_) {\n    return arguments.length ? (touchable = typeof _ === \"function\" ? _ : constant(!!_), brush) : touchable;\n  };\n\n  brush.handleSize = function(_) {\n    return arguments.length ? (handleSize = +_, brush) : handleSize;\n  };\n\n  brush.keyModifiers = function(_) {\n    return arguments.length ? (keys = !!_, brush) : keys;\n  };\n\n  brush.on = function() {\n    var value = listeners.on.apply(listeners, arguments);\n    return value === listeners ? brush : value;\n  };\n\n  return brush;\n}\n", "export var abs = Math.abs;\nexport var cos = Math.cos;\nexport var sin = Math.sin;\nexport var pi = Math.PI;\nexport var halfPi = pi / 2;\nexport var tau = pi * 2;\nexport var max = Math.max;\nexport var epsilon = 1e-12;\n", "import {max, tau} from \"./math.js\";\n\nfunction range(i, j) {\n  return Array.from({length: j - i}, (_, k) => i + k);\n}\n\nfunction compareValue(compare) {\n  return function(a, b) {\n    return compare(\n      a.source.value + a.target.value,\n      b.source.value + b.target.value\n    );\n  };\n}\n\nexport default function() {\n  return chord(false, false);\n}\n\nexport function chordTranspose() {\n  return chord(false, true);\n}\n\nexport function chordDirected() {\n  return chord(true, false);\n}\n\nfunction chord(directed, transpose) {\n  var padAngle = 0,\n      sortGroups = null,\n      sortSubgroups = null,\n      sortChords = null;\n\n  function chord(matrix) {\n    var n = matrix.length,\n        groupSums = new Array(n),\n        groupIndex = range(0, n),\n        chords = new Array(n * n),\n        groups = new Array(n),\n        k = 0, dx;\n\n    matrix = Float64Array.from({length: n * n}, transpose\n        ? (_, i) => matrix[i % n][i / n | 0]\n        : (_, i) => matrix[i / n | 0][i % n]);\n\n    // Compute the scaling factor from value to angle in [0, 2pi].\n    for (let i = 0; i < n; ++i) {\n      let x = 0;\n      for (let j = 0; j < n; ++j) x += matrix[i * n + j] + directed * matrix[j * n + i];\n      k += groupSums[i] = x;\n    }\n    k = max(0, tau - padAngle * n) / k;\n    dx = k ? padAngle : tau / n;\n\n    // Compute the angles for each group and constituent chord.\n    {\n      let x = 0;\n      if (sortGroups) groupIndex.sort((a, b) => sortGroups(groupSums[a], groupSums[b]));\n      for (const i of groupIndex) {\n        const x0 = x;\n        if (directed) {\n          const subgroupIndex = range(~n + 1, n).filter(j => j < 0 ? matrix[~j * n + i] : matrix[i * n + j]);\n          if (sortSubgroups) subgroupIndex.sort((a, b) => sortSubgroups(a < 0 ? -matrix[~a * n + i] : matrix[i * n + a], b < 0 ? -matrix[~b * n + i] : matrix[i * n + b]));\n          for (const j of subgroupIndex) {\n            if (j < 0) {\n              const chord = chords[~j * n + i] || (chords[~j * n + i] = {source: null, target: null});\n              chord.target = {index: i, startAngle: x, endAngle: x += matrix[~j * n + i] * k, value: matrix[~j * n + i]};\n            } else {\n              const chord = chords[i * n + j] || (chords[i * n + j] = {source: null, target: null});\n              chord.source = {index: i, startAngle: x, endAngle: x += matrix[i * n + j] * k, value: matrix[i * n + j]};\n            }\n          }\n          groups[i] = {index: i, startAngle: x0, endAngle: x, value: groupSums[i]};\n        } else {\n          const subgroupIndex = range(0, n).filter(j => matrix[i * n + j] || matrix[j * n + i]);\n          if (sortSubgroups) subgroupIndex.sort((a, b) => sortSubgroups(matrix[i * n + a], matrix[i * n + b]));\n          for (const j of subgroupIndex) {\n            let chord;\n            if (i < j) {\n              chord = chords[i * n + j] || (chords[i * n + j] = {source: null, target: null});\n              chord.source = {index: i, startAngle: x, endAngle: x += matrix[i * n + j] * k, value: matrix[i * n + j]};\n            } else {\n              chord = chords[j * n + i] || (chords[j * n + i] = {source: null, target: null});\n              chord.target = {index: i, startAngle: x, endAngle: x += matrix[i * n + j] * k, value: matrix[i * n + j]};\n              if (i === j) chord.source = chord.target;\n            }\n            if (chord.source && chord.target && chord.source.value < chord.target.value) {\n              const source = chord.source;\n              chord.source = chord.target;\n              chord.target = source;\n            }\n          }\n          groups[i] = {index: i, startAngle: x0, endAngle: x, value: groupSums[i]};\n        }\n        x += dx;\n      }\n    }\n\n    // Remove empty chords.\n    chords = Object.values(chords);\n    chords.groups = groups;\n    return sortChords ? chords.sort(sortChords) : chords;\n  }\n\n  chord.padAngle = function(_) {\n    return arguments.length ? (padAngle = max(0, _), chord) : padAngle;\n  };\n\n  chord.sortGroups = function(_) {\n    return arguments.length ? (sortGroups = _, chord) : sortGroups;\n  };\n\n  chord.sortSubgroups = function(_) {\n    return arguments.length ? (sortSubgroups = _, chord) : sortSubgroups;\n  };\n\n  chord.sortChords = function(_) {\n    return arguments.length ? (_ == null ? sortChords = null : (sortChords = compareValue(_))._ = _, chord) : sortChords && sortChords._;\n  };\n\n  return chord;\n}\n", "export var slice = Array.prototype.slice;\n", "export default function(x) {\n  return function() {\n    return x;\n  };\n}\n", "import {path} from \"d3-path\";\nimport {slice} from \"./array.js\";\nimport constant from \"./constant.js\";\nimport {abs, cos, epsilon, halfPi, sin} from \"./math.js\";\n\nfunction defaultSource(d) {\n  return d.source;\n}\n\nfunction defaultTarget(d) {\n  return d.target;\n}\n\nfunction defaultRadius(d) {\n  return d.radius;\n}\n\nfunction defaultStartAngle(d) {\n  return d.startAngle;\n}\n\nfunction defaultEndAngle(d) {\n  return d.endAngle;\n}\n\nfunction defaultPadAngle() {\n  return 0;\n}\n\nfunction defaultArrowheadRadius() {\n  return 10;\n}\n\nfunction ribbon(headRadius) {\n  var source = defaultSource,\n      target = defaultTarget,\n      sourceRadius = defaultRadius,\n      targetRadius = defaultRadius,\n      startAngle = defaultStartAngle,\n      endAngle = defaultEndAngle,\n      padAngle = defaultPadAngle,\n      context = null;\n\n  function ribbon() {\n    var buffer,\n        s = source.apply(this, arguments),\n        t = target.apply(this, arguments),\n        ap = padAngle.apply(this, arguments) / 2,\n        argv = slice.call(arguments),\n        sr = +sourceRadius.apply(this, (argv[0] = s, argv)),\n        sa0 = startAngle.apply(this, argv) - halfPi,\n        sa1 = endAngle.apply(this, argv) - halfPi,\n        tr = +targetRadius.apply(this, (argv[0] = t, argv)),\n        ta0 = startAngle.apply(this, argv) - halfPi,\n        ta1 = endAngle.apply(this, argv) - halfPi;\n\n    if (!context) context = buffer = path();\n\n    if (ap > epsilon) {\n      if (abs(sa1 - sa0) > ap * 2 + epsilon) sa1 > sa0 ? (sa0 += ap, sa1 -= ap) : (sa0 -= ap, sa1 += ap);\n      else sa0 = sa1 = (sa0 + sa1) / 2;\n      if (abs(ta1 - ta0) > ap * 2 + epsilon) ta1 > ta0 ? (ta0 += ap, ta1 -= ap) : (ta0 -= ap, ta1 += ap);\n      else ta0 = ta1 = (ta0 + ta1) / 2;\n    }\n\n    context.moveTo(sr * cos(sa0), sr * sin(sa0));\n    context.arc(0, 0, sr, sa0, sa1);\n    if (sa0 !== ta0 || sa1 !== ta1) {\n      if (headRadius) {\n        var hr = +headRadius.apply(this, arguments), tr2 = tr - hr, ta2 = (ta0 + ta1) / 2;\n        context.quadraticCurveTo(0, 0, tr2 * cos(ta0), tr2 * sin(ta0));\n        context.lineTo(tr * cos(ta2), tr * sin(ta2));\n        context.lineTo(tr2 * cos(ta1), tr2 * sin(ta1));\n      } else {\n        context.quadraticCurveTo(0, 0, tr * cos(ta0), tr * sin(ta0));\n        context.arc(0, 0, tr, ta0, ta1);\n      }\n    }\n    context.quadraticCurveTo(0, 0, sr * cos(sa0), sr * sin(sa0));\n    context.closePath();\n\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  if (headRadius) ribbon.headRadius = function(_) {\n    return arguments.length ? (headRadius = typeof _ === \"function\" ? _ : constant(+_), ribbon) : headRadius;\n  };\n\n  ribbon.radius = function(_) {\n    return arguments.length ? (sourceRadius = targetRadius = typeof _ === \"function\" ? _ : constant(+_), ribbon) : sourceRadius;\n  };\n\n  ribbon.sourceRadius = function(_) {\n    return arguments.length ? (sourceRadius = typeof _ === \"function\" ? _ : constant(+_), ribbon) : sourceRadius;\n  };\n\n  ribbon.targetRadius = function(_) {\n    return arguments.length ? (targetRadius = typeof _ === \"function\" ? _ : constant(+_), ribbon) : targetRadius;\n  };\n\n  ribbon.startAngle = function(_) {\n    return arguments.length ? (startAngle = typeof _ === \"function\" ? _ : constant(+_), ribbon) : startAngle;\n  };\n\n  ribbon.endAngle = function(_) {\n    return arguments.length ? (endAngle = typeof _ === \"function\" ? _ : constant(+_), ribbon) : endAngle;\n  };\n\n  ribbon.padAngle = function(_) {\n    return arguments.length ? (padAngle = typeof _ === \"function\" ? _ : constant(+_), ribbon) : padAngle;\n  };\n\n  ribbon.source = function(_) {\n    return arguments.length ? (source = _, ribbon) : source;\n  };\n\n  ribbon.target = function(_) {\n    return arguments.length ? (target = _, ribbon) : target;\n  };\n\n  ribbon.context = function(_) {\n    return arguments.length ? ((context = _ == null ? null : _), ribbon) : context;\n  };\n\n  return ribbon;\n}\n\nexport default function() {\n  return ribbon();\n}\n\nexport function ribbonArrow() {\n  return ribbon(defaultArrowheadRadius);\n}\n", "var array = Array.prototype;\n\nexport var slice = array.slice;\n", "export default function(a, b) {\n  return a - b;\n}\n", "export default function(ring) {\n  var i = 0, n = ring.length, area = ring[n - 1][1] * ring[0][0] - ring[n - 1][0] * ring[0][1];\n  while (++i < n) area += ring[i - 1][1] * ring[i][0] - ring[i - 1][0] * ring[i][1];\n  return area;\n}\n", "export default x => () => x;\n", "export default function(ring, hole) {\n  var i = -1, n = hole.length, c;\n  while (++i < n) if (c = ringContains(ring, hole[i])) return c;\n  return 0;\n}\n\nfunction ringContains(ring, point) {\n  var x = point[0], y = point[1], contains = -1;\n  for (var i = 0, n = ring.length, j = n - 1; i < n; j = i++) {\n    var pi = ring[i], xi = pi[0], yi = pi[1], pj = ring[j], xj = pj[0], yj = pj[1];\n    if (segmentContains(pi, pj, point)) return 0;\n    if (((yi > y) !== (yj > y)) && ((x < (xj - xi) * (y - yi) / (yj - yi) + xi))) contains = -contains;\n  }\n  return contains;\n}\n\nfunction segmentContains(a, b, c) {\n  var i; return collinear(a, b, c) && within(a[i = +(a[0] === b[0])], c[i], b[i]);\n}\n\nfunction collinear(a, b, c) {\n  return (b[0] - a[0]) * (c[1] - a[1]) === (c[0] - a[0]) * (b[1] - a[1]);\n}\n\nfunction within(p, q, r) {\n  return p <= q && q <= r || r <= q && q <= p;\n}\n", "export default function() {}\n", "import {extent, nice, thresholdSturges, ticks} from \"d3-array\";\nimport {slice} from \"./array.js\";\nimport ascending from \"./ascending.js\";\nimport area from \"./area.js\";\nimport constant from \"./constant.js\";\nimport contains from \"./contains.js\";\nimport noop from \"./noop.js\";\n\nvar cases = [\n  [],\n  [[[1.0, 1.5], [0.5, 1.0]]],\n  [[[1.5, 1.0], [1.0, 1.5]]],\n  [[[1.5, 1.0], [0.5, 1.0]]],\n  [[[1.0, 0.5], [1.5, 1.0]]],\n  [[[1.0, 1.5], [0.5, 1.0]], [[1.0, 0.5], [1.5, 1.0]]],\n  [[[1.0, 0.5], [1.0, 1.5]]],\n  [[[1.0, 0.5], [0.5, 1.0]]],\n  [[[0.5, 1.0], [1.0, 0.5]]],\n  [[[1.0, 1.5], [1.0, 0.5]]],\n  [[[0.5, 1.0], [1.0, 0.5]], [[1.5, 1.0], [1.0, 1.5]]],\n  [[[1.5, 1.0], [1.0, 0.5]]],\n  [[[0.5, 1.0], [1.5, 1.0]]],\n  [[[1.0, 1.5], [1.5, 1.0]]],\n  [[[0.5, 1.0], [1.0, 1.5]]],\n  []\n];\n\nexport default function() {\n  var dx = 1,\n      dy = 1,\n      threshold = thresholdSturges,\n      smooth = smoothLinear;\n\n  function contours(values) {\n    var tz = threshold(values);\n\n    // Convert number of thresholds into uniform thresholds.\n    if (!Array.isArray(tz)) {\n      const e = extent(values, finite);\n      tz = ticks(...nice(e[0], e[1], tz), tz);\n      while (tz[tz.length - 1] >= e[1]) tz.pop();\n      while (tz[1] < e[0]) tz.shift();\n    } else {\n      tz = tz.slice().sort(ascending);\n    }\n\n    return tz.map(value => contour(values, value));\n  }\n\n  // Accumulate, smooth contour rings, assign holes to exterior rings.\n  // Based on https://github.com/mbostock/shapefile/blob/v0.6.2/shp/polygon.js\n  function contour(values, value) {\n    const v = value == null ? NaN : +value;\n    if (isNaN(v)) throw new Error(`invalid value: ${value}`);\n\n    var polygons = [],\n        holes = [];\n\n    isorings(values, v, function(ring) {\n      smooth(ring, values, v);\n      if (area(ring) > 0) polygons.push([ring]);\n      else holes.push(ring);\n    });\n\n    holes.forEach(function(hole) {\n      for (var i = 0, n = polygons.length, polygon; i < n; ++i) {\n        if (contains((polygon = polygons[i])[0], hole) !== -1) {\n          polygon.push(hole);\n          return;\n        }\n      }\n    });\n\n    return {\n      type: \"MultiPolygon\",\n      value: value,\n      coordinates: polygons\n    };\n  }\n\n  // Marching squares with isolines stitched into rings.\n  // Based on https://github.com/topojson/topojson-client/blob/v3.0.0/src/stitch.js\n  function isorings(values, value, callback) {\n    var fragmentByStart = new Array,\n        fragmentByEnd = new Array,\n        x, y, t0, t1, t2, t3;\n\n    // Special case for the first row (y = -1, t2 = t3 = 0).\n    x = y = -1;\n    t1 = above(values[0], value);\n    cases[t1 << 1].forEach(stitch);\n    while (++x < dx - 1) {\n      t0 = t1, t1 = above(values[x + 1], value);\n      cases[t0 | t1 << 1].forEach(stitch);\n    }\n    cases[t1 << 0].forEach(stitch);\n\n    // General case for the intermediate rows.\n    while (++y < dy - 1) {\n      x = -1;\n      t1 = above(values[y * dx + dx], value);\n      t2 = above(values[y * dx], value);\n      cases[t1 << 1 | t2 << 2].forEach(stitch);\n      while (++x < dx - 1) {\n        t0 = t1, t1 = above(values[y * dx + dx + x + 1], value);\n        t3 = t2, t2 = above(values[y * dx + x + 1], value);\n        cases[t0 | t1 << 1 | t2 << 2 | t3 << 3].forEach(stitch);\n      }\n      cases[t1 | t2 << 3].forEach(stitch);\n    }\n\n    // Special case for the last row (y = dy - 1, t0 = t1 = 0).\n    x = -1;\n    t2 = values[y * dx] >= value;\n    cases[t2 << 2].forEach(stitch);\n    while (++x < dx - 1) {\n      t3 = t2, t2 = above(values[y * dx + x + 1], value);\n      cases[t2 << 2 | t3 << 3].forEach(stitch);\n    }\n    cases[t2 << 3].forEach(stitch);\n\n    function stitch(line) {\n      var start = [line[0][0] + x, line[0][1] + y],\n          end = [line[1][0] + x, line[1][1] + y],\n          startIndex = index(start),\n          endIndex = index(end),\n          f, g;\n      if (f = fragmentByEnd[startIndex]) {\n        if (g = fragmentByStart[endIndex]) {\n          delete fragmentByEnd[f.end];\n          delete fragmentByStart[g.start];\n          if (f === g) {\n            f.ring.push(end);\n            callback(f.ring);\n          } else {\n            fragmentByStart[f.start] = fragmentByEnd[g.end] = {start: f.start, end: g.end, ring: f.ring.concat(g.ring)};\n          }\n        } else {\n          delete fragmentByEnd[f.end];\n          f.ring.push(end);\n          fragmentByEnd[f.end = endIndex] = f;\n        }\n      } else if (f = fragmentByStart[endIndex]) {\n        if (g = fragmentByEnd[startIndex]) {\n          delete fragmentByStart[f.start];\n          delete fragmentByEnd[g.end];\n          if (f === g) {\n            f.ring.push(end);\n            callback(f.ring);\n          } else {\n            fragmentByStart[g.start] = fragmentByEnd[f.end] = {start: g.start, end: f.end, ring: g.ring.concat(f.ring)};\n          }\n        } else {\n          delete fragmentByStart[f.start];\n          f.ring.unshift(start);\n          fragmentByStart[f.start = startIndex] = f;\n        }\n      } else {\n        fragmentByStart[startIndex] = fragmentByEnd[endIndex] = {start: startIndex, end: endIndex, ring: [start, end]};\n      }\n    }\n  }\n\n  function index(point) {\n    return point[0] * 2 + point[1] * (dx + 1) * 4;\n  }\n\n  function smoothLinear(ring, values, value) {\n    ring.forEach(function(point) {\n      var x = point[0],\n          y = point[1],\n          xt = x | 0,\n          yt = y | 0,\n          v1 = valid(values[yt * dx + xt]);\n      if (x > 0 && x < dx && xt === x) {\n        point[0] = smooth1(x, valid(values[yt * dx + xt - 1]), v1, value);\n      }\n      if (y > 0 && y < dy && yt === y) {\n        point[1] = smooth1(y, valid(values[(yt - 1) * dx + xt]), v1, value);\n      }\n    });\n  }\n\n  contours.contour = contour;\n\n  contours.size = function(_) {\n    if (!arguments.length) return [dx, dy];\n    var _0 = Math.floor(_[0]), _1 = Math.floor(_[1]);\n    if (!(_0 >= 0 && _1 >= 0)) throw new Error(\"invalid size\");\n    return dx = _0, dy = _1, contours;\n  };\n\n  contours.thresholds = function(_) {\n    return arguments.length ? (threshold = typeof _ === \"function\" ? _ : Array.isArray(_) ? constant(slice.call(_)) : constant(_), contours) : threshold;\n  };\n\n  contours.smooth = function(_) {\n    return arguments.length ? (smooth = _ ? smoothLinear : noop, contours) : smooth === smoothLinear;\n  };\n\n  return contours;\n}\n\n// When computing the extent, ignore infinite values (as well as invalid ones).\nfunction finite(x) {\n  return isFinite(x) ? x : NaN;\n}\n\n// Is the (possibly invalid) x greater than or equal to the (known valid) value?\n// Treat any invalid value as below negative infinity.\nfunction above(x, value) {\n  return x == null ? false : +x >= value;\n}\n\n// During smoothing, treat any invalid value as negative infinity.\nfunction valid(v) {\n  return v == null || isNaN(v = +v) ? -Infinity : v;\n}\n\nfunction smooth1(x, v0, v1, value) {\n  const a = value - v0;\n  const b = v1 - v0;\n  const d = isFinite(a) || isFinite(b) ? a / b : Math.sign(a) / Math.sign(b);\n  return isNaN(d) ? x : x + d - 0.5;\n}\n", "import {blur2, max, ticks} from \"d3-array\";\nimport {slice} from \"./array.js\";\nimport constant from \"./constant.js\";\nimport Contours from \"./contours.js\";\n\nfunction defaultX(d) {\n  return d[0];\n}\n\nfunction defaultY(d) {\n  return d[1];\n}\n\nfunction defaultWeight() {\n  return 1;\n}\n\nexport default function() {\n  var x = defaultX,\n      y = defaultY,\n      weight = defaultWeight,\n      dx = 960,\n      dy = 500,\n      r = 20, // blur radius\n      k = 2, // log2(grid cell size)\n      o = r * 3, // grid offset, to pad for blur\n      n = (dx + o * 2) >> k, // grid width\n      m = (dy + o * 2) >> k, // grid height\n      threshold = constant(20);\n\n  function grid(data) {\n    var values = new Float32Array(n * m),\n        pow2k = Math.pow(2, -k),\n        i = -1;\n\n    for (const d of data) {\n      var xi = (x(d, ++i, data) + o) * pow2k,\n          yi = (y(d, i, data) + o) * pow2k,\n          wi = +weight(d, i, data);\n      if (wi && xi >= 0 && xi < n && yi >= 0 && yi < m) {\n        var x0 = Math.floor(xi),\n            y0 = Math.floor(yi),\n            xt = xi - x0 - 0.5,\n            yt = yi - y0 - 0.5;\n        values[x0 + y0 * n] += (1 - xt) * (1 - yt) * wi;\n        values[x0 + 1 + y0 * n] += xt * (1 - yt) * wi;\n        values[x0 + 1 + (y0 + 1) * n] += xt * yt * wi;\n        values[x0 + (y0 + 1) * n] += (1 - xt) * yt * wi;\n      }\n    }\n\n    blur2({data: values, width: n, height: m}, r * pow2k);\n    return values;\n  }\n\n  function density(data) {\n    var values = grid(data),\n        tz = threshold(values),\n        pow4k = Math.pow(2, 2 * k);\n\n    // Convert number of thresholds into uniform thresholds.\n    if (!Array.isArray(tz)) {\n      tz = ticks(Number.MIN_VALUE, max(values) / pow4k, tz);\n    }\n\n    return Contours()\n        .size([n, m])\n        .thresholds(tz.map(d => d * pow4k))\n      (values)\n        .map((c, i) => (c.value = +tz[i], transform(c)));\n  }\n\n  density.contours = function(data) {\n    var values = grid(data),\n        contours = Contours().size([n, m]),\n        pow4k = Math.pow(2, 2 * k),\n        contour = value => {\n          value = +value;\n          var c = transform(contours.contour(values, value * pow4k));\n          c.value = value; // preserve exact threshold value\n          return c;\n        };\n    Object.defineProperty(contour, \"max\", {get: () => max(values) / pow4k});\n    return contour;\n  };\n\n  function transform(geometry) {\n    geometry.coordinates.forEach(transformPolygon);\n    return geometry;\n  }\n\n  function transformPolygon(coordinates) {\n    coordinates.forEach(transformRing);\n  }\n\n  function transformRing(coordinates) {\n    coordinates.forEach(transformPoint);\n  }\n\n  // TODO Optimize.\n  function transformPoint(coordinates) {\n    coordinates[0] = coordinates[0] * Math.pow(2, k) - o;\n    coordinates[1] = coordinates[1] * Math.pow(2, k) - o;\n  }\n\n  function resize() {\n    o = r * 3;\n    n = (dx + o * 2) >> k;\n    m = (dy + o * 2) >> k;\n    return density;\n  }\n\n  density.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), density) : x;\n  };\n\n  density.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), density) : y;\n  };\n\n  density.weight = function(_) {\n    return arguments.length ? (weight = typeof _ === \"function\" ? _ : constant(+_), density) : weight;\n  };\n\n  density.size = function(_) {\n    if (!arguments.length) return [dx, dy];\n    var _0 = +_[0], _1 = +_[1];\n    if (!(_0 >= 0 && _1 >= 0)) throw new Error(\"invalid size\");\n    return dx = _0, dy = _1, resize();\n  };\n\n  density.cellSize = function(_) {\n    if (!arguments.length) return 1 << k;\n    if (!((_ = +_) >= 1)) throw new Error(\"invalid cell size\");\n    return k = Math.floor(Math.log(_) / Math.LN2), resize();\n  };\n\n  density.thresholds = function(_) {\n    return arguments.length ? (threshold = typeof _ === \"function\" ? _ : Array.isArray(_) ? constant(slice.call(_)) : constant(_), density) : threshold;\n  };\n\n  density.bandwidth = function(_) {\n    if (!arguments.length) return Math.sqrt(r * (r + 1));\n    if (!((_ = +_) >= 0)) throw new Error(\"invalid bandwidth\");\n    return r = (Math.sqrt(4 * _ * _ + 1) - 1) / 2, resize();\n  };\n\n  return density;\n}\n", "var EOL = {},\n    EOF = {},\n    QUOTE = 34,\n    NEWLINE = 10,\n    RETURN = 13;\n\nfunction objectConverter(columns) {\n  return new Function(\"d\", \"return {\" + columns.map(function(name, i) {\n    return JSON.stringify(name) + \": d[\" + i + \"] || \\\"\\\"\";\n  }).join(\",\") + \"}\");\n}\n\nfunction customConverter(columns, f) {\n  var object = objectConverter(columns);\n  return function(row, i) {\n    return f(object(row), i, columns);\n  };\n}\n\n// Compute unique columns in order of discovery.\nfunction inferColumns(rows) {\n  var columnSet = Object.create(null),\n      columns = [];\n\n  rows.forEach(function(row) {\n    for (var column in row) {\n      if (!(column in columnSet)) {\n        columns.push(columnSet[column] = column);\n      }\n    }\n  });\n\n  return columns;\n}\n\nfunction pad(value, width) {\n  var s = value + \"\", length = s.length;\n  return length < width ? new Array(width - length + 1).join(0) + s : s;\n}\n\nfunction formatYear(year) {\n  return year < 0 ? \"-\" + pad(-year, 6)\n    : year > 9999 ? \"+\" + pad(year, 6)\n    : pad(year, 4);\n}\n\nfunction formatDate(date) {\n  var hours = date.getUTCHours(),\n      minutes = date.getUTCMinutes(),\n      seconds = date.getUTCSeconds(),\n      milliseconds = date.getUTCMilliseconds();\n  return isNaN(date) ? \"Invalid Date\"\n      : formatYear(date.getUTCFullYear(), 4) + \"-\" + pad(date.getUTCMonth() + 1, 2) + \"-\" + pad(date.getUTCDate(), 2)\n      + (milliseconds ? \"T\" + pad(hours, 2) + \":\" + pad(minutes, 2) + \":\" + pad(seconds, 2) + \".\" + pad(milliseconds, 3) + \"Z\"\n      : seconds ? \"T\" + pad(hours, 2) + \":\" + pad(minutes, 2) + \":\" + pad(seconds, 2) + \"Z\"\n      : minutes || hours ? \"T\" + pad(hours, 2) + \":\" + pad(minutes, 2) + \"Z\"\n      : \"\");\n}\n\nexport default function(delimiter) {\n  var reFormat = new RegExp(\"[\\\"\" + delimiter + \"\\n\\r]\"),\n      DELIMITER = delimiter.charCodeAt(0);\n\n  function parse(text, f) {\n    var convert, columns, rows = parseRows(text, function(row, i) {\n      if (convert) return convert(row, i - 1);\n      columns = row, convert = f ? customConverter(row, f) : objectConverter(row);\n    });\n    rows.columns = columns || [];\n    return rows;\n  }\n\n  function parseRows(text, f) {\n    var rows = [], // output rows\n        N = text.length,\n        I = 0, // current character index\n        n = 0, // current line number\n        t, // current token\n        eof = N <= 0, // current token followed by EOF?\n        eol = false; // current token followed by EOL?\n\n    // Strip the trailing newline.\n    if (text.charCodeAt(N - 1) === NEWLINE) --N;\n    if (text.charCodeAt(N - 1) === RETURN) --N;\n\n    function token() {\n      if (eof) return EOF;\n      if (eol) return eol = false, EOL;\n\n      // Unescape quotes.\n      var i, j = I, c;\n      if (text.charCodeAt(j) === QUOTE) {\n        while (I++ < N && text.charCodeAt(I) !== QUOTE || text.charCodeAt(++I) === QUOTE);\n        if ((i = I) >= N) eof = true;\n        else if ((c = text.charCodeAt(I++)) === NEWLINE) eol = true;\n        else if (c === RETURN) { eol = true; if (text.charCodeAt(I) === NEWLINE) ++I; }\n        return text.slice(j + 1, i - 1).replace(/\"\"/g, \"\\\"\");\n      }\n\n      // Find next delimiter or newline.\n      while (I < N) {\n        if ((c = text.charCodeAt(i = I++)) === NEWLINE) eol = true;\n        else if (c === RETURN) { eol = true; if (text.charCodeAt(I) === NEWLINE) ++I; }\n        else if (c !== DELIMITER) continue;\n        return text.slice(j, i);\n      }\n\n      // Return last token before EOF.\n      return eof = true, text.slice(j, N);\n    }\n\n    while ((t = token()) !== EOF) {\n      var row = [];\n      while (t !== EOL && t !== EOF) row.push(t), t = token();\n      if (f && (row = f(row, n++)) == null) continue;\n      rows.push(row);\n    }\n\n    return rows;\n  }\n\n  function preformatBody(rows, columns) {\n    return rows.map(function(row) {\n      return columns.map(function(column) {\n        return formatValue(row[column]);\n      }).join(delimiter);\n    });\n  }\n\n  function format(rows, columns) {\n    if (columns == null) columns = inferColumns(rows);\n    return [columns.map(formatValue).join(delimiter)].concat(preformatBody(rows, columns)).join(\"\\n\");\n  }\n\n  function formatBody(rows, columns) {\n    if (columns == null) columns = inferColumns(rows);\n    return preformatBody(rows, columns).join(\"\\n\");\n  }\n\n  function formatRows(rows) {\n    return rows.map(formatRow).join(\"\\n\");\n  }\n\n  function formatRow(row) {\n    return row.map(formatValue).join(delimiter);\n  }\n\n  function formatValue(value) {\n    return value == null ? \"\"\n        : value instanceof Date ? formatDate(value)\n        : reFormat.test(value += \"\") ? \"\\\"\" + value.replace(/\"/g, \"\\\"\\\"\") + \"\\\"\"\n        : value;\n  }\n\n  return {\n    parse: parse,\n    parseRows: parseRows,\n    format: format,\n    formatBody: formatBody,\n    formatRows: formatRows,\n    formatRow: formatRow,\n    formatValue: formatValue\n  };\n}\n", "import dsv from \"./dsv.js\";\n\nvar csv = dsv(\",\");\n\nexport var csvParse = csv.parse;\nexport var csvParseRows = csv.parseRows;\nexport var csvFormat = csv.format;\nexport var csvFormatBody = csv.formatBody;\nexport var csvFormatRows = csv.formatRows;\nexport var csvFormatRow = csv.formatRow;\nexport var csvFormatValue = csv.formatValue;\n", "import dsv from \"./dsv.js\";\n\nvar tsv = dsv(\"\\t\");\n\nexport var tsvParse = tsv.parse;\nexport var tsvParseRows = tsv.parseRows;\nexport var tsvFormat = tsv.format;\nexport var tsvFormatBody = tsv.formatBody;\nexport var tsvFormatRows = tsv.formatRows;\nexport var tsvFormatRow = tsv.formatRow;\nexport var tsvFormatValue = tsv.formatValue;\n", "export default function autoType(object) {\n  for (var key in object) {\n    var value = object[key].trim(), number, m;\n    if (!value) value = null;\n    else if (value === \"true\") value = true;\n    else if (value === \"false\") value = false;\n    else if (value === \"NaN\") value = NaN;\n    else if (!isNaN(number = +value)) value = number;\n    else if (m = value.match(/^([-+]\\d{2})?\\d{4}(-\\d{2}(-\\d{2})?)?(T\\d{2}:\\d{2}(:\\d{2}(\\.\\d{3})?)?(Z|[-+]\\d{2}:\\d{2})?)?$/)) {\n      if (fixtz && !!m[4] && !m[7]) value = value.replace(/-/g, \"/\").replace(/T/, \" \");\n      value = new Date(value);\n    }\n    else continue;\n    object[key] = value;\n  }\n  return object;\n}\n\n// https://github.com/d3/d3-dsv/issues/45\nconst fixtz = new Date(\"2019-01-01T00:00\").getHours() || new Date(\"2019-07-01T00:00\").getHours();", "function responseBlob(response) {\n  if (!response.ok) throw new Error(response.status + \" \" + response.statusText);\n  return response.blob();\n}\n\nexport default function(input, init) {\n  return fetch(input, init).then(responseBlob);\n}\n", "function responseArrayBuffer(response) {\n  if (!response.ok) throw new Error(response.status + \" \" + response.statusText);\n  return response.arrayBuffer();\n}\n\nexport default function(input, init) {\n  return fetch(input, init).then(responseArrayBuffer);\n}\n", "function responseText(response) {\n  if (!response.ok) throw new Error(response.status + \" \" + response.statusText);\n  return response.text();\n}\n\nexport default function(input, init) {\n  return fetch(input, init).then(responseText);\n}\n", "import {csvParse, dsvFormat, tsvParse} from \"d3-dsv\";\nimport text from \"./text.js\";\n\nfunction dsvParse(parse) {\n  return function(input, init, row) {\n    if (arguments.length === 2 && typeof init === \"function\") row = init, init = undefined;\n    return text(input, init).then(function(response) {\n      return parse(response, row);\n    });\n  };\n}\n\nexport default function dsv(delimiter, input, init, row) {\n  if (arguments.length === 3 && typeof init === \"function\") row = init, init = undefined;\n  var format = dsvFormat(delimiter);\n  return text(input, init).then(function(response) {\n    return format.parse(response, row);\n  });\n}\n\nexport var csv = dsvParse(csvParse);\nexport var tsv = dsvParse(tsvParse);\n", "export default function(input, init) {\n  return new Promise(function(resolve, reject) {\n    var image = new Image;\n    for (var key in init) image[key] = init[key];\n    image.onerror = reject;\n    image.onload = function() { resolve(image); };\n    image.src = input;\n  });\n}\n", "function responseJson(response) {\n  if (!response.ok) throw new Error(response.status + \" \" + response.statusText);\n  if (response.status === 204 || response.status === 205) return;\n  return response.json();\n}\n\nexport default function(input, init) {\n  return fetch(input, init).then(responseJson);\n}\n", "import text from \"./text.js\";\n\nfunction parser(type) {\n  return (input, init) => text(input, init)\n    .then(text => (new DOMParser).parseFromString(text, type));\n}\n\nexport default parser(\"application/xml\");\n\nexport var html = parser(\"text/html\");\n\nexport var svg = parser(\"image/svg+xml\");\n", "export default function(x, y) {\n  var nodes, strength = 1;\n\n  if (x == null) x = 0;\n  if (y == null) y = 0;\n\n  function force() {\n    var i,\n        n = nodes.length,\n        node,\n        sx = 0,\n        sy = 0;\n\n    for (i = 0; i < n; ++i) {\n      node = nodes[i], sx += node.x, sy += node.y;\n    }\n\n    for (sx = (sx / n - x) * strength, sy = (sy / n - y) * strength, i = 0; i < n; ++i) {\n      node = nodes[i], node.x -= sx, node.y -= sy;\n    }\n  }\n\n  force.initialize = function(_) {\n    nodes = _;\n  };\n\n  force.x = function(_) {\n    return arguments.length ? (x = +_, force) : x;\n  };\n\n  force.y = function(_) {\n    return arguments.length ? (y = +_, force) : y;\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = +_, force) : strength;\n  };\n\n  return force;\n}\n", "export default function(d) {\n  const x = +this._x.call(null, d),\n      y = +this._y.call(null, d);\n  return add(this.cover(x, y), x, y, d);\n}\n\nfunction add(tree, x, y, d) {\n  if (isNaN(x) || isNaN(y)) return tree; // ignore invalid points\n\n  var parent,\n      node = tree._root,\n      leaf = {data: d},\n      x0 = tree._x0,\n      y0 = tree._y0,\n      x1 = tree._x1,\n      y1 = tree._y1,\n      xm,\n      ym,\n      xp,\n      yp,\n      right,\n      bottom,\n      i,\n      j;\n\n  // If the tree is empty, initialize the root as a leaf.\n  if (!node) return tree._root = leaf, tree;\n\n  // Find the existing leaf for the new point, or add it.\n  while (node.length) {\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm; else x1 = xm;\n    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym; else y1 = ym;\n    if (parent = node, !(node = node[i = bottom << 1 | right])) return parent[i] = leaf, tree;\n  }\n\n  // Is the new point is exactly coincident with the existing point?\n  xp = +tree._x.call(null, node.data);\n  yp = +tree._y.call(null, node.data);\n  if (x === xp && y === yp) return leaf.next = node, parent ? parent[i] = leaf : tree._root = leaf, tree;\n\n  // Otherwise, split the leaf node until the old and new point are separated.\n  do {\n    parent = parent ? parent[i] = new Array(4) : tree._root = new Array(4);\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm; else x1 = xm;\n    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym; else y1 = ym;\n  } while ((i = bottom << 1 | right) === (j = (yp >= ym) << 1 | (xp >= xm)));\n  return parent[j] = node, parent[i] = leaf, tree;\n}\n\nexport function addAll(data) {\n  var d, i, n = data.length,\n      x,\n      y,\n      xz = new Array(n),\n      yz = new Array(n),\n      x0 = Infinity,\n      y0 = Infinity,\n      x1 = -Infinity,\n      y1 = -Infinity;\n\n  // Compute the points and their extent.\n  for (i = 0; i < n; ++i) {\n    if (isNaN(x = +this._x.call(null, d = data[i])) || isNaN(y = +this._y.call(null, d))) continue;\n    xz[i] = x;\n    yz[i] = y;\n    if (x < x0) x0 = x;\n    if (x > x1) x1 = x;\n    if (y < y0) y0 = y;\n    if (y > y1) y1 = y;\n  }\n\n  // If there were no (valid) points, abort.\n  if (x0 > x1 || y0 > y1) return this;\n\n  // Expand the tree to cover the new points.\n  this.cover(x0, y0).cover(x1, y1);\n\n  // Add the new points.\n  for (i = 0; i < n; ++i) {\n    add(this, xz[i], yz[i], data[i]);\n  }\n\n  return this;\n}\n", "export default function(x, y) {\n  if (isNaN(x = +x) || isNaN(y = +y)) return this; // ignore invalid points\n\n  var x0 = this._x0,\n      y0 = this._y0,\n      x1 = this._x1,\n      y1 = this._y1;\n\n  // If the quadtree has no extent, initialize them.\n  // Integer extent are necessary so that if we later double the extent,\n  // the existing quadrant boundaries don’t change due to floating point error!\n  if (isNaN(x0)) {\n    x1 = (x0 = Math.floor(x)) + 1;\n    y1 = (y0 = Math.floor(y)) + 1;\n  }\n\n  // Otherwise, double repeatedly to cover.\n  else {\n    var z = x1 - x0 || 1,\n        node = this._root,\n        parent,\n        i;\n\n    while (x0 > x || x >= x1 || y0 > y || y >= y1) {\n      i = (y < y0) << 1 | (x < x0);\n      parent = new Array(4), parent[i] = node, node = parent, z *= 2;\n      switch (i) {\n        case 0: x1 = x0 + z, y1 = y0 + z; break;\n        case 1: x0 = x1 - z, y1 = y0 + z; break;\n        case 2: x1 = x0 + z, y0 = y1 - z; break;\n        case 3: x0 = x1 - z, y0 = y1 - z; break;\n      }\n    }\n\n    if (this._root && this._root.length) this._root = node;\n  }\n\n  this._x0 = x0;\n  this._y0 = y0;\n  this._x1 = x1;\n  this._y1 = y1;\n  return this;\n}\n", "export default function() {\n  var data = [];\n  this.visit(function(node) {\n    if (!node.length) do data.push(node.data); while (node = node.next)\n  });\n  return data;\n}\n", "export default function(_) {\n  return arguments.length\n      ? this.cover(+_[0][0], +_[0][1]).cover(+_[1][0], +_[1][1])\n      : isNaN(this._x0) ? undefined : [[this._x0, this._y0], [this._x1, this._y1]];\n}\n", "export default function(node, x0, y0, x1, y1) {\n  this.node = node;\n  this.x0 = x0;\n  this.y0 = y0;\n  this.x1 = x1;\n  this.y1 = y1;\n}\n", "import Quad from \"./quad.js\";\n\nexport default function(x, y, radius) {\n  var data,\n      x0 = this._x0,\n      y0 = this._y0,\n      x1,\n      y1,\n      x2,\n      y2,\n      x3 = this._x1,\n      y3 = this._y1,\n      quads = [],\n      node = this._root,\n      q,\n      i;\n\n  if (node) quads.push(new Quad(node, x0, y0, x3, y3));\n  if (radius == null) radius = Infinity;\n  else {\n    x0 = x - radius, y0 = y - radius;\n    x3 = x + radius, y3 = y + radius;\n    radius *= radius;\n  }\n\n  while (q = quads.pop()) {\n\n    // Stop searching if this quadrant can’t contain a closer node.\n    if (!(node = q.node)\n        || (x1 = q.x0) > x3\n        || (y1 = q.y0) > y3\n        || (x2 = q.x1) < x0\n        || (y2 = q.y1) < y0) continue;\n\n    // Bisect the current quadrant.\n    if (node.length) {\n      var xm = (x1 + x2) / 2,\n          ym = (y1 + y2) / 2;\n\n      quads.push(\n        new Quad(node[3], xm, ym, x2, y2),\n        new Quad(node[2], x1, ym, xm, y2),\n        new Quad(node[1], xm, y1, x2, ym),\n        new Quad(node[0], x1, y1, xm, ym)\n      );\n\n      // Visit the closest quadrant first.\n      if (i = (y >= ym) << 1 | (x >= xm)) {\n        q = quads[quads.length - 1];\n        quads[quads.length - 1] = quads[quads.length - 1 - i];\n        quads[quads.length - 1 - i] = q;\n      }\n    }\n\n    // Visit this point. (Visiting coincident points isn’t necessary!)\n    else {\n      var dx = x - +this._x.call(null, node.data),\n          dy = y - +this._y.call(null, node.data),\n          d2 = dx * dx + dy * dy;\n      if (d2 < radius) {\n        var d = Math.sqrt(radius = d2);\n        x0 = x - d, y0 = y - d;\n        x3 = x + d, y3 = y + d;\n        data = node.data;\n      }\n    }\n  }\n\n  return data;\n}\n", "export default function(d) {\n  if (isNaN(x = +this._x.call(null, d)) || isNaN(y = +this._y.call(null, d))) return this; // ignore invalid points\n\n  var parent,\n      node = this._root,\n      retainer,\n      previous,\n      next,\n      x0 = this._x0,\n      y0 = this._y0,\n      x1 = this._x1,\n      y1 = this._y1,\n      x,\n      y,\n      xm,\n      ym,\n      right,\n      bottom,\n      i,\n      j;\n\n  // If the tree is empty, initialize the root as a leaf.\n  if (!node) return this;\n\n  // Find the leaf node for the point.\n  // While descending, also retain the deepest parent with a non-removed sibling.\n  if (node.length) while (true) {\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm; else x1 = xm;\n    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym; else y1 = ym;\n    if (!(parent = node, node = node[i = bottom << 1 | right])) return this;\n    if (!node.length) break;\n    if (parent[(i + 1) & 3] || parent[(i + 2) & 3] || parent[(i + 3) & 3]) retainer = parent, j = i;\n  }\n\n  // Find the point to remove.\n  while (node.data !== d) if (!(previous = node, node = node.next)) return this;\n  if (next = node.next) delete node.next;\n\n  // If there are multiple coincident points, remove just the point.\n  if (previous) return (next ? previous.next = next : delete previous.next), this;\n\n  // If this is the root point, remove it.\n  if (!parent) return this._root = next, this;\n\n  // Remove this leaf.\n  next ? parent[i] = next : delete parent[i];\n\n  // If the parent now contains exactly one leaf, collapse superfluous parents.\n  if ((node = parent[0] || parent[1] || parent[2] || parent[3])\n      && node === (parent[3] || parent[2] || parent[1] || parent[0])\n      && !node.length) {\n    if (retainer) retainer[j] = node;\n    else this._root = node;\n  }\n\n  return this;\n}\n\nexport function removeAll(data) {\n  for (var i = 0, n = data.length; i < n; ++i) this.remove(data[i]);\n  return this;\n}\n", "export default function() {\n  return this._root;\n}\n", "export default function() {\n  var size = 0;\n  this.visit(function(node) {\n    if (!node.length) do ++size; while (node = node.next)\n  });\n  return size;\n}\n", "import Quad from \"./quad.js\";\n\nexport default function(callback) {\n  var quads = [], q, node = this._root, child, x0, y0, x1, y1;\n  if (node) quads.push(new Quad(node, this._x0, this._y0, this._x1, this._y1));\n  while (q = quads.pop()) {\n    if (!callback(node = q.node, x0 = q.x0, y0 = q.y0, x1 = q.x1, y1 = q.y1) && node.length) {\n      var xm = (x0 + x1) / 2, ym = (y0 + y1) / 2;\n      if (child = node[3]) quads.push(new Quad(child, xm, ym, x1, y1));\n      if (child = node[2]) quads.push(new Quad(child, x0, ym, xm, y1));\n      if (child = node[1]) quads.push(new Quad(child, xm, y0, x1, ym));\n      if (child = node[0]) quads.push(new Quad(child, x0, y0, xm, ym));\n    }\n  }\n  return this;\n}\n", "import Quad from \"./quad.js\";\n\nexport default function(callback) {\n  var quads = [], next = [], q;\n  if (this._root) quads.push(new Quad(this._root, this._x0, this._y0, this._x1, this._y1));\n  while (q = quads.pop()) {\n    var node = q.node;\n    if (node.length) {\n      var child, x0 = q.x0, y0 = q.y0, x1 = q.x1, y1 = q.y1, xm = (x0 + x1) / 2, ym = (y0 + y1) / 2;\n      if (child = node[0]) quads.push(new Quad(child, x0, y0, xm, ym));\n      if (child = node[1]) quads.push(new Quad(child, xm, y0, x1, ym));\n      if (child = node[2]) quads.push(new Quad(child, x0, ym, xm, y1));\n      if (child = node[3]) quads.push(new Quad(child, xm, ym, x1, y1));\n    }\n    next.push(q);\n  }\n  while (q = next.pop()) {\n    callback(q.node, q.x0, q.y0, q.x1, q.y1);\n  }\n  return this;\n}\n", "export function defaultX(d) {\n  return d[0];\n}\n\nexport default function(_) {\n  return arguments.length ? (this._x = _, this) : this._x;\n}\n", "export function defaultY(d) {\n  return d[1];\n}\n\nexport default function(_) {\n  return arguments.length ? (this._y = _, this) : this._y;\n}\n", "import tree_add, {addAll as tree_addAll} from \"./add.js\";\nimport tree_cover from \"./cover.js\";\nimport tree_data from \"./data.js\";\nimport tree_extent from \"./extent.js\";\nimport tree_find from \"./find.js\";\nimport tree_remove, {removeAll as tree_removeAll} from \"./remove.js\";\nimport tree_root from \"./root.js\";\nimport tree_size from \"./size.js\";\nimport tree_visit from \"./visit.js\";\nimport tree_visitAfter from \"./visitAfter.js\";\nimport tree_x, {defaultX} from \"./x.js\";\nimport tree_y, {defaultY} from \"./y.js\";\n\nexport default function quadtree(nodes, x, y) {\n  var tree = new Quadtree(x == null ? defaultX : x, y == null ? defaultY : y, NaN, NaN, NaN, NaN);\n  return nodes == null ? tree : tree.addAll(nodes);\n}\n\nfunction Quadtree(x, y, x0, y0, x1, y1) {\n  this._x = x;\n  this._y = y;\n  this._x0 = x0;\n  this._y0 = y0;\n  this._x1 = x1;\n  this._y1 = y1;\n  this._root = undefined;\n}\n\nfunction leaf_copy(leaf) {\n  var copy = {data: leaf.data}, next = copy;\n  while (leaf = leaf.next) next = next.next = {data: leaf.data};\n  return copy;\n}\n\nvar treeProto = quadtree.prototype = Quadtree.prototype;\n\ntreeProto.copy = function() {\n  var copy = new Quadtree(this._x, this._y, this._x0, this._y0, this._x1, this._y1),\n      node = this._root,\n      nodes,\n      child;\n\n  if (!node) return copy;\n\n  if (!node.length) return copy._root = leaf_copy(node), copy;\n\n  nodes = [{source: node, target: copy._root = new Array(4)}];\n  while (node = nodes.pop()) {\n    for (var i = 0; i < 4; ++i) {\n      if (child = node.source[i]) {\n        if (child.length) nodes.push({source: child, target: node.target[i] = new Array(4)});\n        else node.target[i] = leaf_copy(child);\n      }\n    }\n  }\n\n  return copy;\n};\n\ntreeProto.add = tree_add;\ntreeProto.addAll = tree_addAll;\ntreeProto.cover = tree_cover;\ntreeProto.data = tree_data;\ntreeProto.extent = tree_extent;\ntreeProto.find = tree_find;\ntreeProto.remove = tree_remove;\ntreeProto.removeAll = tree_removeAll;\ntreeProto.root = tree_root;\ntreeProto.size = tree_size;\ntreeProto.visit = tree_visit;\ntreeProto.visitAfter = tree_visitAfter;\ntreeProto.x = tree_x;\ntreeProto.y = tree_y;\n", "export default function(x) {\n  return function() {\n    return x;\n  };\n}\n", "export default function(random) {\n  return (random() - 0.5) * 1e-6;\n}\n", "import {quadtree} from \"d3-quadtree\";\nimport constant from \"./constant.js\";\nimport jiggle from \"./jiggle.js\";\n\nfunction x(d) {\n  return d.x + d.vx;\n}\n\nfunction y(d) {\n  return d.y + d.vy;\n}\n\nexport default function(radius) {\n  var nodes,\n      radii,\n      random,\n      strength = 1,\n      iterations = 1;\n\n  if (typeof radius !== \"function\") radius = constant(radius == null ? 1 : +radius);\n\n  function force() {\n    var i, n = nodes.length,\n        tree,\n        node,\n        xi,\n        yi,\n        ri,\n        ri2;\n\n    for (var k = 0; k < iterations; ++k) {\n      tree = quadtree(nodes, x, y).visitAfter(prepare);\n      for (i = 0; i < n; ++i) {\n        node = nodes[i];\n        ri = radii[node.index], ri2 = ri * ri;\n        xi = node.x + node.vx;\n        yi = node.y + node.vy;\n        tree.visit(apply);\n      }\n    }\n\n    function apply(quad, x0, y0, x1, y1) {\n      var data = quad.data, rj = quad.r, r = ri + rj;\n      if (data) {\n        if (data.index > node.index) {\n          var x = xi - data.x - data.vx,\n              y = yi - data.y - data.vy,\n              l = x * x + y * y;\n          if (l < r * r) {\n            if (x === 0) x = jiggle(random), l += x * x;\n            if (y === 0) y = jiggle(random), l += y * y;\n            l = (r - (l = Math.sqrt(l))) / l * strength;\n            node.vx += (x *= l) * (r = (rj *= rj) / (ri2 + rj));\n            node.vy += (y *= l) * r;\n            data.vx -= x * (r = 1 - r);\n            data.vy -= y * r;\n          }\n        }\n        return;\n      }\n      return x0 > xi + r || x1 < xi - r || y0 > yi + r || y1 < yi - r;\n    }\n  }\n\n  function prepare(quad) {\n    if (quad.data) return quad.r = radii[quad.data.index];\n    for (var i = quad.r = 0; i < 4; ++i) {\n      if (quad[i] && quad[i].r > quad.r) {\n        quad.r = quad[i].r;\n      }\n    }\n  }\n\n  function initialize() {\n    if (!nodes) return;\n    var i, n = nodes.length, node;\n    radii = new Array(n);\n    for (i = 0; i < n; ++i) node = nodes[i], radii[node.index] = +radius(node, i, nodes);\n  }\n\n  force.initialize = function(_nodes, _random) {\n    nodes = _nodes;\n    random = _random;\n    initialize();\n  };\n\n  force.iterations = function(_) {\n    return arguments.length ? (iterations = +_, force) : iterations;\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = +_, force) : strength;\n  };\n\n  force.radius = function(_) {\n    return arguments.length ? (radius = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : radius;\n  };\n\n  return force;\n}\n", "import constant from \"./constant.js\";\nimport jiggle from \"./jiggle.js\";\n\nfunction index(d) {\n  return d.index;\n}\n\nfunction find(nodeById, nodeId) {\n  var node = nodeById.get(nodeId);\n  if (!node) throw new Error(\"node not found: \" + nodeId);\n  return node;\n}\n\nexport default function(links) {\n  var id = index,\n      strength = defaultStrength,\n      strengths,\n      distance = constant(30),\n      distances,\n      nodes,\n      count,\n      bias,\n      random,\n      iterations = 1;\n\n  if (links == null) links = [];\n\n  function defaultStrength(link) {\n    return 1 / Math.min(count[link.source.index], count[link.target.index]);\n  }\n\n  function force(alpha) {\n    for (var k = 0, n = links.length; k < iterations; ++k) {\n      for (var i = 0, link, source, target, x, y, l, b; i < n; ++i) {\n        link = links[i], source = link.source, target = link.target;\n        x = target.x + target.vx - source.x - source.vx || jiggle(random);\n        y = target.y + target.vy - source.y - source.vy || jiggle(random);\n        l = Math.sqrt(x * x + y * y);\n        l = (l - distances[i]) / l * alpha * strengths[i];\n        x *= l, y *= l;\n        target.vx -= x * (b = bias[i]);\n        target.vy -= y * b;\n        source.vx += x * (b = 1 - b);\n        source.vy += y * b;\n      }\n    }\n  }\n\n  function initialize() {\n    if (!nodes) return;\n\n    var i,\n        n = nodes.length,\n        m = links.length,\n        nodeById = new Map(nodes.map((d, i) => [id(d, i, nodes), d])),\n        link;\n\n    for (i = 0, count = new Array(n); i < m; ++i) {\n      link = links[i], link.index = i;\n      if (typeof link.source !== \"object\") link.source = find(nodeById, link.source);\n      if (typeof link.target !== \"object\") link.target = find(nodeById, link.target);\n      count[link.source.index] = (count[link.source.index] || 0) + 1;\n      count[link.target.index] = (count[link.target.index] || 0) + 1;\n    }\n\n    for (i = 0, bias = new Array(m); i < m; ++i) {\n      link = links[i], bias[i] = count[link.source.index] / (count[link.source.index] + count[link.target.index]);\n    }\n\n    strengths = new Array(m), initializeStrength();\n    distances = new Array(m), initializeDistance();\n  }\n\n  function initializeStrength() {\n    if (!nodes) return;\n\n    for (var i = 0, n = links.length; i < n; ++i) {\n      strengths[i] = +strength(links[i], i, links);\n    }\n  }\n\n  function initializeDistance() {\n    if (!nodes) return;\n\n    for (var i = 0, n = links.length; i < n; ++i) {\n      distances[i] = +distance(links[i], i, links);\n    }\n  }\n\n  force.initialize = function(_nodes, _random) {\n    nodes = _nodes;\n    random = _random;\n    initialize();\n  };\n\n  force.links = function(_) {\n    return arguments.length ? (links = _, initialize(), force) : links;\n  };\n\n  force.id = function(_) {\n    return arguments.length ? (id = _, force) : id;\n  };\n\n  force.iterations = function(_) {\n    return arguments.length ? (iterations = +_, force) : iterations;\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initializeStrength(), force) : strength;\n  };\n\n  force.distance = function(_) {\n    return arguments.length ? (distance = typeof _ === \"function\" ? _ : constant(+_), initializeDistance(), force) : distance;\n  };\n\n  return force;\n}\n", "// https://en.wikipedia.org/wiki/Linear_congruential_generator#Parameters_in_common_use\nconst a = 1664525;\nconst c = 1013904223;\nconst m = 4294967296; // 2^32\n\nexport default function() {\n  let s = 1;\n  return () => (s = (a * s + c) % m) / m;\n}\n", "import {dispatch} from \"d3-dispatch\";\nimport {timer} from \"d3-timer\";\nimport lcg from \"./lcg.js\";\n\nexport function x(d) {\n  return d.x;\n}\n\nexport function y(d) {\n  return d.y;\n}\n\nvar initialRadius = 10,\n    initialAngle = Math.PI * (3 - Math.sqrt(5));\n\nexport default function(nodes) {\n  var simulation,\n      alpha = 1,\n      alphaMin = 0.001,\n      alphaDecay = 1 - Math.pow(alphaMin, 1 / 300),\n      alphaTarget = 0,\n      velocityDecay = 0.6,\n      forces = new Map(),\n      stepper = timer(step),\n      event = dispatch(\"tick\", \"end\"),\n      random = lcg();\n\n  if (nodes == null) nodes = [];\n\n  function step() {\n    tick();\n    event.call(\"tick\", simulation);\n    if (alpha < alphaMin) {\n      stepper.stop();\n      event.call(\"end\", simulation);\n    }\n  }\n\n  function tick(iterations) {\n    var i, n = nodes.length, node;\n\n    if (iterations === undefined) iterations = 1;\n\n    for (var k = 0; k < iterations; ++k) {\n      alpha += (alphaTarget - alpha) * alphaDecay;\n\n      forces.forEach(function(force) {\n        force(alpha);\n      });\n\n      for (i = 0; i < n; ++i) {\n        node = nodes[i];\n        if (node.fx == null) node.x += node.vx *= velocityDecay;\n        else node.x = node.fx, node.vx = 0;\n        if (node.fy == null) node.y += node.vy *= velocityDecay;\n        else node.y = node.fy, node.vy = 0;\n      }\n    }\n\n    return simulation;\n  }\n\n  function initializeNodes() {\n    for (var i = 0, n = nodes.length, node; i < n; ++i) {\n      node = nodes[i], node.index = i;\n      if (node.fx != null) node.x = node.fx;\n      if (node.fy != null) node.y = node.fy;\n      if (isNaN(node.x) || isNaN(node.y)) {\n        var radius = initialRadius * Math.sqrt(0.5 + i), angle = i * initialAngle;\n        node.x = radius * Math.cos(angle);\n        node.y = radius * Math.sin(angle);\n      }\n      if (isNaN(node.vx) || isNaN(node.vy)) {\n        node.vx = node.vy = 0;\n      }\n    }\n  }\n\n  function initializeForce(force) {\n    if (force.initialize) force.initialize(nodes, random);\n    return force;\n  }\n\n  initializeNodes();\n\n  return simulation = {\n    tick: tick,\n\n    restart: function() {\n      return stepper.restart(step), simulation;\n    },\n\n    stop: function() {\n      return stepper.stop(), simulation;\n    },\n\n    nodes: function(_) {\n      return arguments.length ? (nodes = _, initializeNodes(), forces.forEach(initializeForce), simulation) : nodes;\n    },\n\n    alpha: function(_) {\n      return arguments.length ? (alpha = +_, simulation) : alpha;\n    },\n\n    alphaMin: function(_) {\n      return arguments.length ? (alphaMin = +_, simulation) : alphaMin;\n    },\n\n    alphaDecay: function(_) {\n      return arguments.length ? (alphaDecay = +_, simulation) : +alphaDecay;\n    },\n\n    alphaTarget: function(_) {\n      return arguments.length ? (alphaTarget = +_, simulation) : alphaTarget;\n    },\n\n    velocityDecay: function(_) {\n      return arguments.length ? (velocityDecay = 1 - _, simulation) : 1 - velocityDecay;\n    },\n\n    randomSource: function(_) {\n      return arguments.length ? (random = _, forces.forEach(initializeForce), simulation) : random;\n    },\n\n    force: function(name, _) {\n      return arguments.length > 1 ? ((_ == null ? forces.delete(name) : forces.set(name, initializeForce(_))), simulation) : forces.get(name);\n    },\n\n    find: function(x, y, radius) {\n      var i = 0,\n          n = nodes.length,\n          dx,\n          dy,\n          d2,\n          node,\n          closest;\n\n      if (radius == null) radius = Infinity;\n      else radius *= radius;\n\n      for (i = 0; i < n; ++i) {\n        node = nodes[i];\n        dx = x - node.x;\n        dy = y - node.y;\n        d2 = dx * dx + dy * dy;\n        if (d2 < radius) closest = node, radius = d2;\n      }\n\n      return closest;\n    },\n\n    on: function(name, _) {\n      return arguments.length > 1 ? (event.on(name, _), simulation) : event.on(name);\n    }\n  };\n}\n", "import {quadtree} from \"d3-quadtree\";\nimport constant from \"./constant.js\";\nimport jiggle from \"./jiggle.js\";\nimport {x, y} from \"./simulation.js\";\n\nexport default function() {\n  var nodes,\n      node,\n      random,\n      alpha,\n      strength = constant(-30),\n      strengths,\n      distanceMin2 = 1,\n      distanceMax2 = Infinity,\n      theta2 = 0.81;\n\n  function force(_) {\n    var i, n = nodes.length, tree = quadtree(nodes, x, y).visitAfter(accumulate);\n    for (alpha = _, i = 0; i < n; ++i) node = nodes[i], tree.visit(apply);\n  }\n\n  function initialize() {\n    if (!nodes) return;\n    var i, n = nodes.length, node;\n    strengths = new Array(n);\n    for (i = 0; i < n; ++i) node = nodes[i], strengths[node.index] = +strength(node, i, nodes);\n  }\n\n  function accumulate(quad) {\n    var strength = 0, q, c, weight = 0, x, y, i;\n\n    // For internal nodes, accumulate forces from child quadrants.\n    if (quad.length) {\n      for (x = y = i = 0; i < 4; ++i) {\n        if ((q = quad[i]) && (c = Math.abs(q.value))) {\n          strength += q.value, weight += c, x += c * q.x, y += c * q.y;\n        }\n      }\n      quad.x = x / weight;\n      quad.y = y / weight;\n    }\n\n    // For leaf nodes, accumulate forces from coincident quadrants.\n    else {\n      q = quad;\n      q.x = q.data.x;\n      q.y = q.data.y;\n      do strength += strengths[q.data.index];\n      while (q = q.next);\n    }\n\n    quad.value = strength;\n  }\n\n  function apply(quad, x1, _, x2) {\n    if (!quad.value) return true;\n\n    var x = quad.x - node.x,\n        y = quad.y - node.y,\n        w = x2 - x1,\n        l = x * x + y * y;\n\n    // Apply the Barnes-Hut approximation if possible.\n    // Limit forces for very close nodes; randomize direction if coincident.\n    if (w * w / theta2 < l) {\n      if (l < distanceMax2) {\n        if (x === 0) x = jiggle(random), l += x * x;\n        if (y === 0) y = jiggle(random), l += y * y;\n        if (l < distanceMin2) l = Math.sqrt(distanceMin2 * l);\n        node.vx += x * quad.value * alpha / l;\n        node.vy += y * quad.value * alpha / l;\n      }\n      return true;\n    }\n\n    // Otherwise, process points directly.\n    else if (quad.length || l >= distanceMax2) return;\n\n    // Limit forces for very close nodes; randomize direction if coincident.\n    if (quad.data !== node || quad.next) {\n      if (x === 0) x = jiggle(random), l += x * x;\n      if (y === 0) y = jiggle(random), l += y * y;\n      if (l < distanceMin2) l = Math.sqrt(distanceMin2 * l);\n    }\n\n    do if (quad.data !== node) {\n      w = strengths[quad.data.index] * alpha / l;\n      node.vx += x * w;\n      node.vy += y * w;\n    } while (quad = quad.next);\n  }\n\n  force.initialize = function(_nodes, _random) {\n    nodes = _nodes;\n    random = _random;\n    initialize();\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : strength;\n  };\n\n  force.distanceMin = function(_) {\n    return arguments.length ? (distanceMin2 = _ * _, force) : Math.sqrt(distanceMin2);\n  };\n\n  force.distanceMax = function(_) {\n    return arguments.length ? (distanceMax2 = _ * _, force) : Math.sqrt(distanceMax2);\n  };\n\n  force.theta = function(_) {\n    return arguments.length ? (theta2 = _ * _, force) : Math.sqrt(theta2);\n  };\n\n  return force;\n}\n", "import constant from \"./constant.js\";\n\nexport default function(radius, x, y) {\n  var nodes,\n      strength = constant(0.1),\n      strengths,\n      radiuses;\n\n  if (typeof radius !== \"function\") radius = constant(+radius);\n  if (x == null) x = 0;\n  if (y == null) y = 0;\n\n  function force(alpha) {\n    for (var i = 0, n = nodes.length; i < n; ++i) {\n      var node = nodes[i],\n          dx = node.x - x || 1e-6,\n          dy = node.y - y || 1e-6,\n          r = Math.sqrt(dx * dx + dy * dy),\n          k = (radiuses[i] - r) * strengths[i] * alpha / r;\n      node.vx += dx * k;\n      node.vy += dy * k;\n    }\n  }\n\n  function initialize() {\n    if (!nodes) return;\n    var i, n = nodes.length;\n    strengths = new Array(n);\n    radiuses = new Array(n);\n    for (i = 0; i < n; ++i) {\n      radiuses[i] = +radius(nodes[i], i, nodes);\n      strengths[i] = isNaN(radiuses[i]) ? 0 : +strength(nodes[i], i, nodes);\n    }\n  }\n\n  force.initialize = function(_) {\n    nodes = _, initialize();\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : strength;\n  };\n\n  force.radius = function(_) {\n    return arguments.length ? (radius = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : radius;\n  };\n\n  force.x = function(_) {\n    return arguments.length ? (x = +_, force) : x;\n  };\n\n  force.y = function(_) {\n    return arguments.length ? (y = +_, force) : y;\n  };\n\n  return force;\n}\n", "import constant from \"./constant.js\";\n\nexport default function(x) {\n  var strength = constant(0.1),\n      nodes,\n      strengths,\n      xz;\n\n  if (typeof x !== \"function\") x = constant(x == null ? 0 : +x);\n\n  function force(alpha) {\n    for (var i = 0, n = nodes.length, node; i < n; ++i) {\n      node = nodes[i], node.vx += (xz[i] - node.x) * strengths[i] * alpha;\n    }\n  }\n\n  function initialize() {\n    if (!nodes) return;\n    var i, n = nodes.length;\n    strengths = new Array(n);\n    xz = new Array(n);\n    for (i = 0; i < n; ++i) {\n      strengths[i] = isNaN(xz[i] = +x(nodes[i], i, nodes)) ? 0 : +strength(nodes[i], i, nodes);\n    }\n  }\n\n  force.initialize = function(_) {\n    nodes = _;\n    initialize();\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : strength;\n  };\n\n  force.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : x;\n  };\n\n  return force;\n}\n", "import constant from \"./constant.js\";\n\nexport default function(y) {\n  var strength = constant(0.1),\n      nodes,\n      strengths,\n      yz;\n\n  if (typeof y !== \"function\") y = constant(y == null ? 0 : +y);\n\n  function force(alpha) {\n    for (var i = 0, n = nodes.length, node; i < n; ++i) {\n      node = nodes[i], node.vy += (yz[i] - node.y) * strengths[i] * alpha;\n    }\n  }\n\n  function initialize() {\n    if (!nodes) return;\n    var i, n = nodes.length;\n    strengths = new Array(n);\n    yz = new Array(n);\n    for (i = 0; i < n; ++i) {\n      strengths[i] = isNaN(yz[i] = +y(nodes[i], i, nodes)) ? 0 : +strength(nodes[i], i, nodes);\n    }\n  }\n\n  force.initialize = function(_) {\n    nodes = _;\n    initialize();\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : strength;\n  };\n\n  force.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : y;\n  };\n\n  return force;\n}\n", "function defaultSeparation(a, b) {\n  return a.parent === b.parent ? 1 : 2;\n}\n\nfunction meanX(children) {\n  return children.reduce(meanXReduce, 0) / children.length;\n}\n\nfunction meanXReduce(x, c) {\n  return x + c.x;\n}\n\nfunction maxY(children) {\n  return 1 + children.reduce(maxYReduce, 0);\n}\n\nfunction maxYReduce(y, c) {\n  return Math.max(y, c.y);\n}\n\nfunction leafLeft(node) {\n  var children;\n  while (children = node.children) node = children[0];\n  return node;\n}\n\nfunction leafRight(node) {\n  var children;\n  while (children = node.children) node = children[children.length - 1];\n  return node;\n}\n\nexport default function() {\n  var separation = defaultSeparation,\n      dx = 1,\n      dy = 1,\n      nodeSize = false;\n\n  function cluster(root) {\n    var previousNode,\n        x = 0;\n\n    // First walk, computing the initial x & y values.\n    root.eachAfter(function(node) {\n      var children = node.children;\n      if (children) {\n        node.x = meanX(children);\n        node.y = maxY(children);\n      } else {\n        node.x = previousNode ? x += separation(node, previousNode) : 0;\n        node.y = 0;\n        previousNode = node;\n      }\n    });\n\n    var left = leafLeft(root),\n        right = leafRight(root),\n        x0 = left.x - separation(left, right) / 2,\n        x1 = right.x + separation(right, left) / 2;\n\n    // Second walk, normalizing x & y to the desired size.\n    return root.eachAfter(nodeSize ? function(node) {\n      node.x = (node.x - root.x) * dx;\n      node.y = (root.y - node.y) * dy;\n    } : function(node) {\n      node.x = (node.x - x0) / (x1 - x0) * dx;\n      node.y = (1 - (root.y ? node.y / root.y : 1)) * dy;\n    });\n  }\n\n  cluster.separation = function(x) {\n    return arguments.length ? (separation = x, cluster) : separation;\n  };\n\n  cluster.size = function(x) {\n    return arguments.length ? (nodeSize = false, dx = +x[0], dy = +x[1], cluster) : (nodeSize ? null : [dx, dy]);\n  };\n\n  cluster.nodeSize = function(x) {\n    return arguments.length ? (nodeSize = true, dx = +x[0], dy = +x[1], cluster) : (nodeSize ? [dx, dy] : null);\n  };\n\n  return cluster;\n}\n", "function count(node) {\n  var sum = 0,\n      children = node.children,\n      i = children && children.length;\n  if (!i) sum = 1;\n  else while (--i >= 0) sum += children[i].value;\n  node.value = sum;\n}\n\nexport default function() {\n  return this.eachAfter(count);\n}\n", "export default function(callback, that) {\n  let index = -1;\n  for (const node of this) {\n    callback.call(that, node, ++index, this);\n  }\n  return this;\n}\n", "export default function(callback, that) {\n  var node = this, nodes = [node], children, i, index = -1;\n  while (node = nodes.pop()) {\n    callback.call(that, node, ++index, this);\n    if (children = node.children) {\n      for (i = children.length - 1; i >= 0; --i) {\n        nodes.push(children[i]);\n      }\n    }\n  }\n  return this;\n}\n", "export default function(callback, that) {\n  var node = this, nodes = [node], next = [], children, i, n, index = -1;\n  while (node = nodes.pop()) {\n    next.push(node);\n    if (children = node.children) {\n      for (i = 0, n = children.length; i < n; ++i) {\n        nodes.push(children[i]);\n      }\n    }\n  }\n  while (node = next.pop()) {\n    callback.call(that, node, ++index, this);\n  }\n  return this;\n}\n", "export default function(callback, that) {\n  let index = -1;\n  for (const node of this) {\n    if (callback.call(that, node, ++index, this)) {\n      return node;\n    }\n  }\n}\n", "export default function(value) {\n  return this.eachAfter(function(node) {\n    var sum = +value(node.data) || 0,\n        children = node.children,\n        i = children && children.length;\n    while (--i >= 0) sum += children[i].value;\n    node.value = sum;\n  });\n}\n", "export default function(compare) {\n  return this.eachBefore(function(node) {\n    if (node.children) {\n      node.children.sort(compare);\n    }\n  });\n}\n", "export default function(end) {\n  var start = this,\n      ancestor = leastCommonAncestor(start, end),\n      nodes = [start];\n  while (start !== ancestor) {\n    start = start.parent;\n    nodes.push(start);\n  }\n  var k = nodes.length;\n  while (end !== ancestor) {\n    nodes.splice(k, 0, end);\n    end = end.parent;\n  }\n  return nodes;\n}\n\nfunction leastCommonAncestor(a, b) {\n  if (a === b) return a;\n  var aNodes = a.ancestors(),\n      bNodes = b.ancestors(),\n      c = null;\n  a = aNodes.pop();\n  b = bNodes.pop();\n  while (a === b) {\n    c = a;\n    a = aNodes.pop();\n    b = bNodes.pop();\n  }\n  return c;\n}\n", "export default function() {\n  var node = this, nodes = [node];\n  while (node = node.parent) {\n    nodes.push(node);\n  }\n  return nodes;\n}\n", "export default function() {\n  return Array.from(this);\n}\n", "export default function() {\n  var leaves = [];\n  this.eachBefore(function(node) {\n    if (!node.children) {\n      leaves.push(node);\n    }\n  });\n  return leaves;\n}\n", "export default function() {\n  var root = this, links = [];\n  root.each(function(node) {\n    if (node !== root) { // Don’t include the root’s parent, if any.\n      links.push({source: node.parent, target: node});\n    }\n  });\n  return links;\n}\n", "export default function*() {\n  var node = this, current, next = [node], children, i, n;\n  do {\n    current = next.reverse(), next = [];\n    while (node = current.pop()) {\n      yield node;\n      if (children = node.children) {\n        for (i = 0, n = children.length; i < n; ++i) {\n          next.push(children[i]);\n        }\n      }\n    }\n  } while (next.length);\n}\n", "import node_count from \"./count.js\";\nimport node_each from \"./each.js\";\nimport node_eachBefore from \"./eachBefore.js\";\nimport node_eachAfter from \"./eachAfter.js\";\nimport node_find from \"./find.js\";\nimport node_sum from \"./sum.js\";\nimport node_sort from \"./sort.js\";\nimport node_path from \"./path.js\";\nimport node_ancestors from \"./ancestors.js\";\nimport node_descendants from \"./descendants.js\";\nimport node_leaves from \"./leaves.js\";\nimport node_links from \"./links.js\";\nimport node_iterator from \"./iterator.js\";\n\nexport default function hierarchy(data, children) {\n  if (data instanceof Map) {\n    data = [undefined, data];\n    if (children === undefined) children = mapChildren;\n  } else if (children === undefined) {\n    children = objectChildren;\n  }\n\n  var root = new Node(data),\n      node,\n      nodes = [root],\n      child,\n      childs,\n      i,\n      n;\n\n  while (node = nodes.pop()) {\n    if ((childs = children(node.data)) && (n = (childs = Array.from(childs)).length)) {\n      node.children = childs;\n      for (i = n - 1; i >= 0; --i) {\n        nodes.push(child = childs[i] = new Node(childs[i]));\n        child.parent = node;\n        child.depth = node.depth + 1;\n      }\n    }\n  }\n\n  return root.eachBefore(computeHeight);\n}\n\nfunction node_copy() {\n  return hierarchy(this).eachBefore(copyData);\n}\n\nfunction objectChildren(d) {\n  return d.children;\n}\n\nfunction mapChildren(d) {\n  return Array.isArray(d) ? d[1] : null;\n}\n\nfunction copyData(node) {\n  if (node.data.value !== undefined) node.value = node.data.value;\n  node.data = node.data.data;\n}\n\nexport function computeHeight(node) {\n  var height = 0;\n  do node.height = height;\n  while ((node = node.parent) && (node.height < ++height));\n}\n\nexport function Node(data) {\n  this.data = data;\n  this.depth =\n  this.height = 0;\n  this.parent = null;\n}\n\nNode.prototype = hierarchy.prototype = {\n  constructor: Node,\n  count: node_count,\n  each: node_each,\n  eachAfter: node_eachAfter,\n  eachBefore: node_eachBefore,\n  find: node_find,\n  sum: node_sum,\n  sort: node_sort,\n  path: node_path,\n  ancestors: node_ancestors,\n  descendants: node_descendants,\n  leaves: node_leaves,\n  links: node_links,\n  copy: node_copy,\n  [Symbol.iterator]: node_iterator\n};\n", "export function optional(f) {\n  return f == null ? null : required(f);\n}\n\nexport function required(f) {\n  if (typeof f !== \"function\") throw new Error;\n  return f;\n}\n", "export function constantZero() {\n  return 0;\n}\n\nexport default function(x) {\n  return function() {\n    return x;\n  };\n}\n", "// https://en.wikipedia.org/wiki/Linear_congruential_generator#Parameters_in_common_use\nconst a = 1664525;\nconst c = 1013904223;\nconst m = 4294967296; // 2^32\n\nexport default function() {\n  let s = 1;\n  return () => (s = (a * s + c) % m) / m;\n}\n", "export default function(x) {\n  return typeof x === \"object\" && \"length\" in x\n    ? x // Array, TypedArray, NodeList, array-like\n    : Array.from(x); // Map, Set, iterable, string, or anything else\n}\n\nexport function shuffle(array, random) {\n  let m = array.length,\n      t,\n      i;\n\n  while (m) {\n    i = random() * m-- | 0;\n    t = array[m];\n    array[m] = array[i];\n    array[i] = t;\n  }\n\n  return array;\n}\n", "import {shuffle} from \"../array.js\";\nimport lcg from \"../lcg.js\";\n\nexport default function(circles) {\n  return packEncloseRandom(circles, lcg());\n}\n\nexport function packEncloseRandom(circles, random) {\n  var i = 0, n = (circles = shuffle(Array.from(circles), random)).length, B = [], p, e;\n\n  while (i < n) {\n    p = circles[i];\n    if (e && enclosesWeak(e, p)) ++i;\n    else e = encloseBasis(B = extendBasis(B, p)), i = 0;\n  }\n\n  return e;\n}\n\nfunction extendBasis(B, p) {\n  var i, j;\n\n  if (enclosesWeakAll(p, B)) return [p];\n\n  // If we get here then B must have at least one element.\n  for (i = 0; i < B.length; ++i) {\n    if (enclosesNot(p, B[i])\n        && enclosesWeakAll(encloseBasis2(B[i], p), B)) {\n      return [B[i], p];\n    }\n  }\n\n  // If we get here then B must have at least two elements.\n  for (i = 0; i < B.length - 1; ++i) {\n    for (j = i + 1; j < B.length; ++j) {\n      if (enclosesNot(encloseBasis2(B[i], B[j]), p)\n          && enclosesNot(encloseBasis2(B[i], p), B[j])\n          && enclosesNot(encloseBasis2(B[j], p), B[i])\n          && enclosesWeakAll(encloseBasis3(B[i], B[j], p), B)) {\n        return [B[i], B[j], p];\n      }\n    }\n  }\n\n  // If we get here then something is very wrong.\n  throw new Error;\n}\n\nfunction enclosesNot(a, b) {\n  var dr = a.r - b.r, dx = b.x - a.x, dy = b.y - a.y;\n  return dr < 0 || dr * dr < dx * dx + dy * dy;\n}\n\nfunction enclosesWeak(a, b) {\n  var dr = a.r - b.r + Math.max(a.r, b.r, 1) * 1e-9, dx = b.x - a.x, dy = b.y - a.y;\n  return dr > 0 && dr * dr > dx * dx + dy * dy;\n}\n\nfunction enclosesWeakAll(a, B) {\n  for (var i = 0; i < B.length; ++i) {\n    if (!enclosesWeak(a, B[i])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction encloseBasis(B) {\n  switch (B.length) {\n    case 1: return encloseBasis1(B[0]);\n    case 2: return encloseBasis2(B[0], B[1]);\n    case 3: return encloseBasis3(B[0], B[1], B[2]);\n  }\n}\n\nfunction encloseBasis1(a) {\n  return {\n    x: a.x,\n    y: a.y,\n    r: a.r\n  };\n}\n\nfunction encloseBasis2(a, b) {\n  var x1 = a.x, y1 = a.y, r1 = a.r,\n      x2 = b.x, y2 = b.y, r2 = b.r,\n      x21 = x2 - x1, y21 = y2 - y1, r21 = r2 - r1,\n      l = Math.sqrt(x21 * x21 + y21 * y21);\n  return {\n    x: (x1 + x2 + x21 / l * r21) / 2,\n    y: (y1 + y2 + y21 / l * r21) / 2,\n    r: (l + r1 + r2) / 2\n  };\n}\n\nfunction encloseBasis3(a, b, c) {\n  var x1 = a.x, y1 = a.y, r1 = a.r,\n      x2 = b.x, y2 = b.y, r2 = b.r,\n      x3 = c.x, y3 = c.y, r3 = c.r,\n      a2 = x1 - x2,\n      a3 = x1 - x3,\n      b2 = y1 - y2,\n      b3 = y1 - y3,\n      c2 = r2 - r1,\n      c3 = r3 - r1,\n      d1 = x1 * x1 + y1 * y1 - r1 * r1,\n      d2 = d1 - x2 * x2 - y2 * y2 + r2 * r2,\n      d3 = d1 - x3 * x3 - y3 * y3 + r3 * r3,\n      ab = a3 * b2 - a2 * b3,\n      xa = (b2 * d3 - b3 * d2) / (ab * 2) - x1,\n      xb = (b3 * c2 - b2 * c3) / ab,\n      ya = (a3 * d2 - a2 * d3) / (ab * 2) - y1,\n      yb = (a2 * c3 - a3 * c2) / ab,\n      A = xb * xb + yb * yb - 1,\n      B = 2 * (r1 + xa * xb + ya * yb),\n      C = xa * xa + ya * ya - r1 * r1,\n      r = -(Math.abs(A) > 1e-6 ? (B + Math.sqrt(B * B - 4 * A * C)) / (2 * A) : C / B);\n  return {\n    x: x1 + xa + xb * r,\n    y: y1 + ya + yb * r,\n    r: r\n  };\n}\n", "import array from \"../array.js\";\nimport lcg from \"../lcg.js\";\nimport {packEncloseRandom} from \"./enclose.js\";\n\nfunction place(b, a, c) {\n  var dx = b.x - a.x, x, a2,\n      dy = b.y - a.y, y, b2,\n      d2 = dx * dx + dy * dy;\n  if (d2) {\n    a2 = a.r + c.r, a2 *= a2;\n    b2 = b.r + c.r, b2 *= b2;\n    if (a2 > b2) {\n      x = (d2 + b2 - a2) / (2 * d2);\n      y = Math.sqrt(Math.max(0, b2 / d2 - x * x));\n      c.x = b.x - x * dx - y * dy;\n      c.y = b.y - x * dy + y * dx;\n    } else {\n      x = (d2 + a2 - b2) / (2 * d2);\n      y = Math.sqrt(Math.max(0, a2 / d2 - x * x));\n      c.x = a.x + x * dx - y * dy;\n      c.y = a.y + x * dy + y * dx;\n    }\n  } else {\n    c.x = a.x + c.r;\n    c.y = a.y;\n  }\n}\n\nfunction intersects(a, b) {\n  var dr = a.r + b.r - 1e-6, dx = b.x - a.x, dy = b.y - a.y;\n  return dr > 0 && dr * dr > dx * dx + dy * dy;\n}\n\nfunction score(node) {\n  var a = node._,\n      b = node.next._,\n      ab = a.r + b.r,\n      dx = (a.x * b.r + b.x * a.r) / ab,\n      dy = (a.y * b.r + b.y * a.r) / ab;\n  return dx * dx + dy * dy;\n}\n\nfunction Node(circle) {\n  this._ = circle;\n  this.next = null;\n  this.previous = null;\n}\n\nexport function packSiblingsRandom(circles, random) {\n  if (!(n = (circles = array(circles)).length)) return 0;\n\n  var a, b, c, n, aa, ca, i, j, k, sj, sk;\n\n  // Place the first circle.\n  a = circles[0], a.x = 0, a.y = 0;\n  if (!(n > 1)) return a.r;\n\n  // Place the second circle.\n  b = circles[1], a.x = -b.r, b.x = a.r, b.y = 0;\n  if (!(n > 2)) return a.r + b.r;\n\n  // Place the third circle.\n  place(b, a, c = circles[2]);\n\n  // Initialize the front-chain using the first three circles a, b and c.\n  a = new Node(a), b = new Node(b), c = new Node(c);\n  a.next = c.previous = b;\n  b.next = a.previous = c;\n  c.next = b.previous = a;\n\n  // Attempt to place each remaining circle…\n  pack: for (i = 3; i < n; ++i) {\n    place(a._, b._, c = circles[i]), c = new Node(c);\n\n    // Find the closest intersecting circle on the front-chain, if any.\n    // “Closeness” is determined by linear distance along the front-chain.\n    // “Ahead” or “behind” is likewise determined by linear distance.\n    j = b.next, k = a.previous, sj = b._.r, sk = a._.r;\n    do {\n      if (sj <= sk) {\n        if (intersects(j._, c._)) {\n          b = j, a.next = b, b.previous = a, --i;\n          continue pack;\n        }\n        sj += j._.r, j = j.next;\n      } else {\n        if (intersects(k._, c._)) {\n          a = k, a.next = b, b.previous = a, --i;\n          continue pack;\n        }\n        sk += k._.r, k = k.previous;\n      }\n    } while (j !== k.next);\n\n    // Success! Insert the new circle c between a and b.\n    c.previous = a, c.next = b, a.next = b.previous = b = c;\n\n    // Compute the new closest circle pair to the centroid.\n    aa = score(a);\n    while ((c = c.next) !== b) {\n      if ((ca = score(c)) < aa) {\n        a = c, aa = ca;\n      }\n    }\n    b = a.next;\n  }\n\n  // Compute the enclosing circle of the front chain.\n  a = [b._], c = b; while ((c = c.next) !== b) a.push(c._); c = packEncloseRandom(a, random);\n\n  // Translate the circles to put the enclosing circle around the origin.\n  for (i = 0; i < n; ++i) a = circles[i], a.x -= c.x, a.y -= c.y;\n\n  return c.r;\n}\n\nexport default function(circles) {\n  packSiblingsRandom(circles, lcg());\n  return circles;\n}\n", "import {optional} from \"../accessors.js\";\nimport constant, {constant<PERSON>ero} from \"../constant.js\";\nimport lcg from \"../lcg.js\";\nimport {packSiblingsRandom} from \"./siblings.js\";\n\nfunction defaultRadius(d) {\n  return Math.sqrt(d.value);\n}\n\nexport default function() {\n  var radius = null,\n      dx = 1,\n      dy = 1,\n      padding = constantZero;\n\n  function pack(root) {\n    const random = lcg();\n    root.x = dx / 2, root.y = dy / 2;\n    if (radius) {\n      root.eachBefore(radiusLeaf(radius))\n          .eachAfter(packChildrenRandom(padding, 0.5, random))\n          .eachBefore(translateChild(1));\n    } else {\n      root.eachBefore(radiusLeaf(defaultRadius))\n          .eachAfter(packChildrenRandom(constantZero, 1, random))\n          .eachAfter(packChildrenRandom(padding, root.r / Math.min(dx, dy), random))\n          .eachBefore(translateChild(Math.min(dx, dy) / (2 * root.r)));\n    }\n    return root;\n  }\n\n  pack.radius = function(x) {\n    return arguments.length ? (radius = optional(x), pack) : radius;\n  };\n\n  pack.size = function(x) {\n    return arguments.length ? (dx = +x[0], dy = +x[1], pack) : [dx, dy];\n  };\n\n  pack.padding = function(x) {\n    return arguments.length ? (padding = typeof x === \"function\" ? x : constant(+x), pack) : padding;\n  };\n\n  return pack;\n}\n\nfunction radiusLeaf(radius) {\n  return function(node) {\n    if (!node.children) {\n      node.r = Math.max(0, +radius(node) || 0);\n    }\n  };\n}\n\nfunction packChildrenRandom(padding, k, random) {\n  return function(node) {\n    if (children = node.children) {\n      var children,\n          i,\n          n = children.length,\n          r = padding(node) * k || 0,\n          e;\n\n      if (r) for (i = 0; i < n; ++i) children[i].r += r;\n      e = packSiblingsRandom(children, random);\n      if (r) for (i = 0; i < n; ++i) children[i].r -= r;\n      node.r = e + r;\n    }\n  };\n}\n\nfunction translateChild(k) {\n  return function(node) {\n    var parent = node.parent;\n    node.r *= k;\n    if (parent) {\n      node.x = parent.x + k * node.x;\n      node.y = parent.y + k * node.y;\n    }\n  };\n}\n", "export default function(node) {\n  node.x0 = Math.round(node.x0);\n  node.y0 = Math.round(node.y0);\n  node.x1 = Math.round(node.x1);\n  node.y1 = Math.round(node.y1);\n}\n", "export default function(parent, x0, y0, x1, y1) {\n  var nodes = parent.children,\n      node,\n      i = -1,\n      n = nodes.length,\n      k = parent.value && (x1 - x0) / parent.value;\n\n  while (++i < n) {\n    node = nodes[i], node.y0 = y0, node.y1 = y1;\n    node.x0 = x0, node.x1 = x0 += node.value * k;\n  }\n}\n", "import roundNode from \"./treemap/round.js\";\nimport treemapDice from \"./treemap/dice.js\";\n\nexport default function() {\n  var dx = 1,\n      dy = 1,\n      padding = 0,\n      round = false;\n\n  function partition(root) {\n    var n = root.height + 1;\n    root.x0 =\n    root.y0 = padding;\n    root.x1 = dx;\n    root.y1 = dy / n;\n    root.eachBefore(positionNode(dy, n));\n    if (round) root.eachBefore(roundNode);\n    return root;\n  }\n\n  function positionNode(dy, n) {\n    return function(node) {\n      if (node.children) {\n        treemapDice(node, node.x0, dy * (node.depth + 1) / n, node.x1, dy * (node.depth + 2) / n);\n      }\n      var x0 = node.x0,\n          y0 = node.y0,\n          x1 = node.x1 - padding,\n          y1 = node.y1 - padding;\n      if (x1 < x0) x0 = x1 = (x0 + x1) / 2;\n      if (y1 < y0) y0 = y1 = (y0 + y1) / 2;\n      node.x0 = x0;\n      node.y0 = y0;\n      node.x1 = x1;\n      node.y1 = y1;\n    };\n  }\n\n  partition.round = function(x) {\n    return arguments.length ? (round = !!x, partition) : round;\n  };\n\n  partition.size = function(x) {\n    return arguments.length ? (dx = +x[0], dy = +x[1], partition) : [dx, dy];\n  };\n\n  partition.padding = function(x) {\n    return arguments.length ? (padding = +x, partition) : padding;\n  };\n\n  return partition;\n}\n", "import {optional} from \"./accessors.js\";\nimport {Node, computeHeight} from \"./hierarchy/index.js\";\n\nvar preroot = {depth: -1},\n    ambiguous = {},\n    imputed = {};\n\nfunction defaultId(d) {\n  return d.id;\n}\n\nfunction defaultParentId(d) {\n  return d.parentId;\n}\n\nexport default function() {\n  var id = defaultId,\n      parentId = defaultParentId,\n      path;\n\n  function stratify(data) {\n    var nodes = Array.from(data),\n        currentId = id,\n        currentParentId = parentId,\n        n,\n        d,\n        i,\n        root,\n        parent,\n        node,\n        nodeId,\n        nodeKey,\n        nodeByKey = new Map;\n\n    if (path != null) {\n      const I = nodes.map((d, i) => normalize(path(d, i, data)));\n      const P = I.map(parentof);\n      const S = new Set(I).add(\"\");\n      for (const i of P) {\n        if (!S.has(i)) {\n          S.add(i);\n          I.push(i);\n          P.push(parentof(i));\n          nodes.push(imputed);\n        }\n      }\n      currentId = (_, i) => I[i];\n      currentParentId = (_, i) => P[i];\n    }\n\n    for (i = 0, n = nodes.length; i < n; ++i) {\n      d = nodes[i], node = nodes[i] = new Node(d);\n      if ((nodeId = currentId(d, i, data)) != null && (nodeId += \"\")) {\n        nodeKey = node.id = nodeId;\n        nodeByKey.set(nodeKey, nodeByKey.has(nodeKey) ? ambiguous : node);\n      }\n      if ((nodeId = currentParentId(d, i, data)) != null && (nodeId += \"\")) {\n        node.parent = nodeId;\n      }\n    }\n\n    for (i = 0; i < n; ++i) {\n      node = nodes[i];\n      if (nodeId = node.parent) {\n        parent = nodeByKey.get(nodeId);\n        if (!parent) throw new Error(\"missing: \" + nodeId);\n        if (parent === ambiguous) throw new Error(\"ambiguous: \" + nodeId);\n        if (parent.children) parent.children.push(node);\n        else parent.children = [node];\n        node.parent = parent;\n      } else {\n        if (root) throw new Error(\"multiple roots\");\n        root = node;\n      }\n    }\n\n    if (!root) throw new Error(\"no root\");\n\n    // When imputing internal nodes, only introduce roots if needed.\n    // Then replace the imputed marker data with null.\n    if (path != null) {\n      while (root.data === imputed && root.children.length === 1) {\n        root = root.children[0], --n;\n      }\n      for (let i = nodes.length - 1; i >= 0; --i) {\n        node = nodes[i];\n        if (node.data !== imputed) break;\n        node.data = null;\n      }\n    }\n\n    root.parent = preroot;\n    root.eachBefore(function(node) { node.depth = node.parent.depth + 1; --n; }).eachBefore(computeHeight);\n    root.parent = null;\n    if (n > 0) throw new Error(\"cycle\");\n\n    return root;\n  }\n\n  stratify.id = function(x) {\n    return arguments.length ? (id = optional(x), stratify) : id;\n  };\n\n  stratify.parentId = function(x) {\n    return arguments.length ? (parentId = optional(x), stratify) : parentId;\n  };\n\n  stratify.path = function(x) {\n    return arguments.length ? (path = optional(x), stratify) : path;\n  };\n\n  return stratify;\n}\n\n// To normalize a path, we coerce to a string, strip the trailing slash if any\n// (as long as the trailing slash is not immediately preceded by another slash),\n// and add leading slash if missing.\nfunction normalize(path) {\n  path = `${path}`;\n  let i = path.length;\n  if (slash(path, i - 1) && !slash(path, i - 2)) path = path.slice(0, -1);\n  return path[0] === \"/\" ? path : `/${path}`;\n}\n\n// Walk backwards to find the first slash that is not the leading slash, e.g.:\n// \"/foo/bar\" ⇥ \"/foo\", \"/foo\" ⇥ \"/\", \"/\" ↦ \"\". (The root is special-cased\n// because the id of the root must be a truthy value.)\nfunction parentof(path) {\n  let i = path.length;\n  if (i < 2) return \"\";\n  while (--i > 1) if (slash(path, i)) break;\n  return path.slice(0, i);\n}\n\n// Slashes can be escaped; to determine whether a slash is a path delimiter, we\n// count the number of preceding backslashes escaping the forward slash: an odd\n// number indicates an escaped forward slash.\nfunction slash(path, i) {\n  if (path[i] === \"/\") {\n    let k = 0;\n    while (i > 0 && path[--i] === \"\\\\\") ++k;\n    if ((k & 1) === 0) return true;\n  }\n  return false;\n}\n", "import {Node} from \"./hierarchy/index.js\";\n\nfunction defaultSeparation(a, b) {\n  return a.parent === b.parent ? 1 : 2;\n}\n\n// function radialSeparation(a, b) {\n//   return (a.parent === b.parent ? 1 : 2) / a.depth;\n// }\n\n// This function is used to traverse the left contour of a subtree (or\n// subforest). It returns the successor of v on this contour. This successor is\n// either given by the leftmost child of v or by the thread of v. The function\n// returns null if and only if v is on the highest level of its subtree.\nfunction nextLeft(v) {\n  var children = v.children;\n  return children ? children[0] : v.t;\n}\n\n// This function works analogously to nextLeft.\nfunction nextRight(v) {\n  var children = v.children;\n  return children ? children[children.length - 1] : v.t;\n}\n\n// Shifts the current subtree rooted at w+. This is done by increasing\n// prelim(w+) and mod(w+) by shift.\nfunction moveSubtree(wm, wp, shift) {\n  var change = shift / (wp.i - wm.i);\n  wp.c -= change;\n  wp.s += shift;\n  wm.c += change;\n  wp.z += shift;\n  wp.m += shift;\n}\n\n// All other shifts, applied to the smaller subtrees between w- and w+, are\n// performed by this function. To prepare the shifts, we have to adjust\n// change(w+), shift(w+), and change(w-).\nfunction executeShifts(v) {\n  var shift = 0,\n      change = 0,\n      children = v.children,\n      i = children.length,\n      w;\n  while (--i >= 0) {\n    w = children[i];\n    w.z += shift;\n    w.m += shift;\n    shift += w.s + (change += w.c);\n  }\n}\n\n// If vi-’s ancestor is a sibling of v, returns vi-’s ancestor. Otherwise,\n// returns the specified (default) ancestor.\nfunction nextAncestor(vim, v, ancestor) {\n  return vim.a.parent === v.parent ? vim.a : ancestor;\n}\n\nfunction TreeNode(node, i) {\n  this._ = node;\n  this.parent = null;\n  this.children = null;\n  this.A = null; // default ancestor\n  this.a = this; // ancestor\n  this.z = 0; // prelim\n  this.m = 0; // mod\n  this.c = 0; // change\n  this.s = 0; // shift\n  this.t = null; // thread\n  this.i = i; // number\n}\n\nTreeNode.prototype = Object.create(Node.prototype);\n\nfunction treeRoot(root) {\n  var tree = new TreeNode(root, 0),\n      node,\n      nodes = [tree],\n      child,\n      children,\n      i,\n      n;\n\n  while (node = nodes.pop()) {\n    if (children = node._.children) {\n      node.children = new Array(n = children.length);\n      for (i = n - 1; i >= 0; --i) {\n        nodes.push(child = node.children[i] = new TreeNode(children[i], i));\n        child.parent = node;\n      }\n    }\n  }\n\n  (tree.parent = new TreeNode(null, 0)).children = [tree];\n  return tree;\n}\n\n// Node-link tree diagram using the Reingold-Tilford \"tidy\" algorithm\nexport default function() {\n  var separation = defaultSeparation,\n      dx = 1,\n      dy = 1,\n      nodeSize = null;\n\n  function tree(root) {\n    var t = treeRoot(root);\n\n    // Compute the layout using Buchheim et al.’s algorithm.\n    t.eachAfter(firstWalk), t.parent.m = -t.z;\n    t.eachBefore(secondWalk);\n\n    // If a fixed node size is specified, scale x and y.\n    if (nodeSize) root.eachBefore(sizeNode);\n\n    // If a fixed tree size is specified, scale x and y based on the extent.\n    // Compute the left-most, right-most, and depth-most nodes for extents.\n    else {\n      var left = root,\n          right = root,\n          bottom = root;\n      root.eachBefore(function(node) {\n        if (node.x < left.x) left = node;\n        if (node.x > right.x) right = node;\n        if (node.depth > bottom.depth) bottom = node;\n      });\n      var s = left === right ? 1 : separation(left, right) / 2,\n          tx = s - left.x,\n          kx = dx / (right.x + s + tx),\n          ky = dy / (bottom.depth || 1);\n      root.eachBefore(function(node) {\n        node.x = (node.x + tx) * kx;\n        node.y = node.depth * ky;\n      });\n    }\n\n    return root;\n  }\n\n  // Computes a preliminary x-coordinate for v. Before that, FIRST WALK is\n  // applied recursively to the children of v, as well as the function\n  // APPORTION. After spacing out the children by calling EXECUTE SHIFTS, the\n  // node v is placed to the midpoint of its outermost children.\n  function firstWalk(v) {\n    var children = v.children,\n        siblings = v.parent.children,\n        w = v.i ? siblings[v.i - 1] : null;\n    if (children) {\n      executeShifts(v);\n      var midpoint = (children[0].z + children[children.length - 1].z) / 2;\n      if (w) {\n        v.z = w.z + separation(v._, w._);\n        v.m = v.z - midpoint;\n      } else {\n        v.z = midpoint;\n      }\n    } else if (w) {\n      v.z = w.z + separation(v._, w._);\n    }\n    v.parent.A = apportion(v, w, v.parent.A || siblings[0]);\n  }\n\n  // Computes all real x-coordinates by summing up the modifiers recursively.\n  function secondWalk(v) {\n    v._.x = v.z + v.parent.m;\n    v.m += v.parent.m;\n  }\n\n  // The core of the algorithm. Here, a new subtree is combined with the\n  // previous subtrees. Threads are used to traverse the inside and outside\n  // contours of the left and right subtree up to the highest common level. The\n  // vertices used for the traversals are vi+, vi-, vo-, and vo+, where the\n  // superscript o means outside and i means inside, the subscript - means left\n  // subtree and + means right subtree. For summing up the modifiers along the\n  // contour, we use respective variables si+, si-, so-, and so+. Whenever two\n  // nodes of the inside contours conflict, we compute the left one of the\n  // greatest uncommon ancestors using the function ANCESTOR and call MOVE\n  // SUBTREE to shift the subtree and prepare the shifts of smaller subtrees.\n  // Finally, we add a new thread (if necessary).\n  function apportion(v, w, ancestor) {\n    if (w) {\n      var vip = v,\n          vop = v,\n          vim = w,\n          vom = vip.parent.children[0],\n          sip = vip.m,\n          sop = vop.m,\n          sim = vim.m,\n          som = vom.m,\n          shift;\n      while (vim = nextRight(vim), vip = nextLeft(vip), vim && vip) {\n        vom = nextLeft(vom);\n        vop = nextRight(vop);\n        vop.a = v;\n        shift = vim.z + sim - vip.z - sip + separation(vim._, vip._);\n        if (shift > 0) {\n          moveSubtree(nextAncestor(vim, v, ancestor), v, shift);\n          sip += shift;\n          sop += shift;\n        }\n        sim += vim.m;\n        sip += vip.m;\n        som += vom.m;\n        sop += vop.m;\n      }\n      if (vim && !nextRight(vop)) {\n        vop.t = vim;\n        vop.m += sim - sop;\n      }\n      if (vip && !nextLeft(vom)) {\n        vom.t = vip;\n        vom.m += sip - som;\n        ancestor = v;\n      }\n    }\n    return ancestor;\n  }\n\n  function sizeNode(node) {\n    node.x *= dx;\n    node.y = node.depth * dy;\n  }\n\n  tree.separation = function(x) {\n    return arguments.length ? (separation = x, tree) : separation;\n  };\n\n  tree.size = function(x) {\n    return arguments.length ? (nodeSize = false, dx = +x[0], dy = +x[1], tree) : (nodeSize ? null : [dx, dy]);\n  };\n\n  tree.nodeSize = function(x) {\n    return arguments.length ? (nodeSize = true, dx = +x[0], dy = +x[1], tree) : (nodeSize ? [dx, dy] : null);\n  };\n\n  return tree;\n}\n", "export default function(parent, x0, y0, x1, y1) {\n  var nodes = parent.children,\n      node,\n      i = -1,\n      n = nodes.length,\n      k = parent.value && (y1 - y0) / parent.value;\n\n  while (++i < n) {\n    node = nodes[i], node.x0 = x0, node.x1 = x1;\n    node.y0 = y0, node.y1 = y0 += node.value * k;\n  }\n}\n", "import treemapDice from \"./dice.js\";\nimport treemapSlice from \"./slice.js\";\n\nexport var phi = (1 + Math.sqrt(5)) / 2;\n\nexport function squarifyRatio(ratio, parent, x0, y0, x1, y1) {\n  var rows = [],\n      nodes = parent.children,\n      row,\n      nodeValue,\n      i0 = 0,\n      i1 = 0,\n      n = nodes.length,\n      dx, dy,\n      value = parent.value,\n      sumValue,\n      minValue,\n      maxValue,\n      newRatio,\n      minRatio,\n      alpha,\n      beta;\n\n  while (i0 < n) {\n    dx = x1 - x0, dy = y1 - y0;\n\n    // Find the next non-empty node.\n    do sumValue = nodes[i1++].value; while (!sumValue && i1 < n);\n    minValue = maxValue = sumValue;\n    alpha = Math.max(dy / dx, dx / dy) / (value * ratio);\n    beta = sumValue * sumValue * alpha;\n    minRatio = Math.max(maxValue / beta, beta / minValue);\n\n    // Keep adding nodes while the aspect ratio maintains or improves.\n    for (; i1 < n; ++i1) {\n      sumValue += nodeValue = nodes[i1].value;\n      if (nodeValue < minValue) minValue = nodeValue;\n      if (nodeValue > maxValue) maxValue = nodeValue;\n      beta = sumValue * sumValue * alpha;\n      newRatio = Math.max(maxValue / beta, beta / minValue);\n      if (newRatio > minRatio) { sumValue -= nodeValue; break; }\n      minRatio = newRatio;\n    }\n\n    // Position and record the row orientation.\n    rows.push(row = {value: sumValue, dice: dx < dy, children: nodes.slice(i0, i1)});\n    if (row.dice) treemapDice(row, x0, y0, x1, value ? y0 += dy * sumValue / value : y1);\n    else treemapSlice(row, x0, y0, value ? x0 += dx * sumValue / value : x1, y1);\n    value -= sumValue, i0 = i1;\n  }\n\n  return rows;\n}\n\nexport default (function custom(ratio) {\n\n  function squarify(parent, x0, y0, x1, y1) {\n    squarifyRatio(ratio, parent, x0, y0, x1, y1);\n  }\n\n  squarify.ratio = function(x) {\n    return custom((x = +x) > 1 ? x : 1);\n  };\n\n  return squarify;\n})(phi);\n", "import roundNode from \"./round.js\";\nimport squarify from \"./squarify.js\";\nimport {required} from \"../accessors.js\";\nimport constant, {constantZero} from \"../constant.js\";\n\nexport default function() {\n  var tile = squarify,\n      round = false,\n      dx = 1,\n      dy = 1,\n      paddingStack = [0],\n      paddingInner = constantZero,\n      paddingTop = constantZero,\n      paddingRight = constantZero,\n      paddingBottom = constantZero,\n      paddingLeft = constantZero;\n\n  function treemap(root) {\n    root.x0 =\n    root.y0 = 0;\n    root.x1 = dx;\n    root.y1 = dy;\n    root.eachBefore(positionNode);\n    paddingStack = [0];\n    if (round) root.eachBefore(roundNode);\n    return root;\n  }\n\n  function positionNode(node) {\n    var p = paddingStack[node.depth],\n        x0 = node.x0 + p,\n        y0 = node.y0 + p,\n        x1 = node.x1 - p,\n        y1 = node.y1 - p;\n    if (x1 < x0) x0 = x1 = (x0 + x1) / 2;\n    if (y1 < y0) y0 = y1 = (y0 + y1) / 2;\n    node.x0 = x0;\n    node.y0 = y0;\n    node.x1 = x1;\n    node.y1 = y1;\n    if (node.children) {\n      p = paddingStack[node.depth + 1] = paddingInner(node) / 2;\n      x0 += paddingLeft(node) - p;\n      y0 += paddingTop(node) - p;\n      x1 -= paddingRight(node) - p;\n      y1 -= paddingBottom(node) - p;\n      if (x1 < x0) x0 = x1 = (x0 + x1) / 2;\n      if (y1 < y0) y0 = y1 = (y0 + y1) / 2;\n      tile(node, x0, y0, x1, y1);\n    }\n  }\n\n  treemap.round = function(x) {\n    return arguments.length ? (round = !!x, treemap) : round;\n  };\n\n  treemap.size = function(x) {\n    return arguments.length ? (dx = +x[0], dy = +x[1], treemap) : [dx, dy];\n  };\n\n  treemap.tile = function(x) {\n    return arguments.length ? (tile = required(x), treemap) : tile;\n  };\n\n  treemap.padding = function(x) {\n    return arguments.length ? treemap.paddingInner(x).paddingOuter(x) : treemap.paddingInner();\n  };\n\n  treemap.paddingInner = function(x) {\n    return arguments.length ? (paddingInner = typeof x === \"function\" ? x : constant(+x), treemap) : paddingInner;\n  };\n\n  treemap.paddingOuter = function(x) {\n    return arguments.length ? treemap.paddingTop(x).paddingRight(x).paddingBottom(x).paddingLeft(x) : treemap.paddingTop();\n  };\n\n  treemap.paddingTop = function(x) {\n    return arguments.length ? (paddingTop = typeof x === \"function\" ? x : constant(+x), treemap) : paddingTop;\n  };\n\n  treemap.paddingRight = function(x) {\n    return arguments.length ? (paddingRight = typeof x === \"function\" ? x : constant(+x), treemap) : paddingRight;\n  };\n\n  treemap.paddingBottom = function(x) {\n    return arguments.length ? (paddingBottom = typeof x === \"function\" ? x : constant(+x), treemap) : paddingBottom;\n  };\n\n  treemap.paddingLeft = function(x) {\n    return arguments.length ? (paddingLeft = typeof x === \"function\" ? x : constant(+x), treemap) : paddingLeft;\n  };\n\n  return treemap;\n}\n", "export default function(parent, x0, y0, x1, y1) {\n  var nodes = parent.children,\n      i, n = nodes.length,\n      sum, sums = new Array(n + 1);\n\n  for (sums[0] = sum = i = 0; i < n; ++i) {\n    sums[i + 1] = sum += nodes[i].value;\n  }\n\n  partition(0, n, parent.value, x0, y0, x1, y1);\n\n  function partition(i, j, value, x0, y0, x1, y1) {\n    if (i >= j - 1) {\n      var node = nodes[i];\n      node.x0 = x0, node.y0 = y0;\n      node.x1 = x1, node.y1 = y1;\n      return;\n    }\n\n    var valueOffset = sums[i],\n        valueTarget = (value / 2) + valueOffset,\n        k = i + 1,\n        hi = j - 1;\n\n    while (k < hi) {\n      var mid = k + hi >>> 1;\n      if (sums[mid] < valueTarget) k = mid + 1;\n      else hi = mid;\n    }\n\n    if ((valueTarget - sums[k - 1]) < (sums[k] - valueTarget) && i + 1 < k) --k;\n\n    var valueLeft = sums[k] - valueOffset,\n        valueRight = value - valueLeft;\n\n    if ((x1 - x0) > (y1 - y0)) {\n      var xk = value ? (x0 * valueRight + x1 * valueLeft) / value : x1;\n      partition(i, k, valueLeft, x0, y0, xk, y1);\n      partition(k, j, valueRight, xk, y0, x1, y1);\n    } else {\n      var yk = value ? (y0 * valueRight + y1 * valueLeft) / value : y1;\n      partition(i, k, valueLeft, x0, y0, x1, yk);\n      partition(k, j, valueRight, x0, yk, x1, y1);\n    }\n  }\n}\n", "import dice from \"./dice.js\";\nimport slice from \"./slice.js\";\n\nexport default function(parent, x0, y0, x1, y1) {\n  (parent.depth & 1 ? slice : dice)(parent, x0, y0, x1, y1);\n}\n", "import treemapDice from \"./dice.js\";\nimport treemapSlice from \"./slice.js\";\nimport {phi, squarifyRatio} from \"./squarify.js\";\n\nexport default (function custom(ratio) {\n\n  function resquarify(parent, x0, y0, x1, y1) {\n    if ((rows = parent._squarify) && (rows.ratio === ratio)) {\n      var rows,\n          row,\n          nodes,\n          i,\n          j = -1,\n          n,\n          m = rows.length,\n          value = parent.value;\n\n      while (++j < m) {\n        row = rows[j], nodes = row.children;\n        for (i = row.value = 0, n = nodes.length; i < n; ++i) row.value += nodes[i].value;\n        if (row.dice) treemapDice(row, x0, y0, x1, value ? y0 += (y1 - y0) * row.value / value : y1);\n        else treemapSlice(row, x0, y0, value ? x0 += (x1 - x0) * row.value / value : x1, y1);\n        value -= row.value;\n      }\n    } else {\n      parent._squarify = rows = squarifyRatio(ratio, parent, x0, y0, x1, y1);\n      rows.ratio = ratio;\n    }\n  }\n\n  resquarify.ratio = function(x) {\n    return custom((x = +x) > 1 ? x : 1);\n  };\n\n  return resquarify;\n})(phi);\n", "export default function(polygon) {\n  var i = -1,\n      n = polygon.length,\n      a,\n      b = polygon[n - 1],\n      area = 0;\n\n  while (++i < n) {\n    a = b;\n    b = polygon[i];\n    area += a[1] * b[0] - a[0] * b[1];\n  }\n\n  return area / 2;\n}\n", "export default function(polygon) {\n  var i = -1,\n      n = polygon.length,\n      x = 0,\n      y = 0,\n      a,\n      b = polygon[n - 1],\n      c,\n      k = 0;\n\n  while (++i < n) {\n    a = b;\n    b = polygon[i];\n    k += c = a[0] * b[1] - b[0] * a[1];\n    x += (a[0] + b[0]) * c;\n    y += (a[1] + b[1]) * c;\n  }\n\n  return k *= 3, [x / k, y / k];\n}\n", "// Returns the 2D cross product of AB and AC vectors, i.e., the z-component of\n// the 3D cross product in a quadrant I Cartesian coordinate system (+x is\n// right, +y is up). Returns a positive value if ABC is counter-clockwise,\n// negative if clockwise, and zero if the points are collinear.\nexport default function(a, b, c) {\n  return (b[0] - a[0]) * (c[1] - a[1]) - (b[1] - a[1]) * (c[0] - a[0]);\n}\n", "import cross from \"./cross.js\";\n\nfunction lexicographicOrder(a, b) {\n  return a[0] - b[0] || a[1] - b[1];\n}\n\n// Computes the upper convex hull per the monotone chain algorithm.\n// Assumes points.length >= 3, is sorted by x, unique in y.\n// Returns an array of indices into points in left-to-right order.\nfunction computeUpperHullIndexes(points) {\n  const n = points.length,\n      indexes = [0, 1];\n  let size = 2, i;\n\n  for (i = 2; i < n; ++i) {\n    while (size > 1 && cross(points[indexes[size - 2]], points[indexes[size - 1]], points[i]) <= 0) --size;\n    indexes[size++] = i;\n  }\n\n  return indexes.slice(0, size); // remove popped points\n}\n\nexport default function(points) {\n  if ((n = points.length) < 3) return null;\n\n  var i,\n      n,\n      sortedPoints = new Array(n),\n      flippedPoints = new Array(n);\n\n  for (i = 0; i < n; ++i) sortedPoints[i] = [+points[i][0], +points[i][1], i];\n  sortedPoints.sort(lexicographicOrder);\n  for (i = 0; i < n; ++i) flippedPoints[i] = [sortedPoints[i][0], -sortedPoints[i][1]];\n\n  var upperIndexes = computeUpperHullIndexes(sortedPoints),\n      lowerIndexes = computeUpperHullIndexes(flippedPoints);\n\n  // Construct the hull polygon, removing possible duplicate endpoints.\n  var skipLeft = lowerIndexes[0] === upperIndexes[0],\n      skipRight = lowerIndexes[lowerIndexes.length - 1] === upperIndexes[upperIndexes.length - 1],\n      hull = [];\n\n  // Add upper hull in right-to-l order.\n  // Then add lower hull in left-to-right order.\n  for (i = upperIndexes.length - 1; i >= 0; --i) hull.push(points[sortedPoints[upperIndexes[i]][2]]);\n  for (i = +skipLeft; i < lowerIndexes.length - skipRight; ++i) hull.push(points[sortedPoints[lowerIndexes[i]][2]]);\n\n  return hull;\n}\n", "export default function(polygon, point) {\n  var n = polygon.length,\n      p = polygon[n - 1],\n      x = point[0], y = point[1],\n      x0 = p[0], y0 = p[1],\n      x1, y1,\n      inside = false;\n\n  for (var i = 0; i < n; ++i) {\n    p = polygon[i], x1 = p[0], y1 = p[1];\n    if (((y1 > y) !== (y0 > y)) && (x < (x0 - x1) * (y - y1) / (y0 - y1) + x1)) inside = !inside;\n    x0 = x1, y0 = y1;\n  }\n\n  return inside;\n}\n", "export default function(polygon) {\n  var i = -1,\n      n = polygon.length,\n      b = polygon[n - 1],\n      xa,\n      ya,\n      xb = b[0],\n      yb = b[1],\n      perimeter = 0;\n\n  while (++i < n) {\n    xa = xb;\n    ya = yb;\n    b = polygon[i];\n    xb = b[0];\n    yb = b[1];\n    xa -= xb;\n    ya -= yb;\n    perimeter += Math.hypot(xa, ya);\n  }\n\n  return perimeter;\n}\n", "export default Math.random;\n", "import defaultSource from \"./defaultSource.js\";\n\nexport default (function sourceRandomUniform(source) {\n  function randomUniform(min, max) {\n    min = min == null ? 0 : +min;\n    max = max == null ? 1 : +max;\n    if (arguments.length === 1) max = min, min = 0;\n    else max -= min;\n    return function() {\n      return source() * max + min;\n    };\n  }\n\n  randomUniform.source = sourceRandomUniform;\n\n  return randomUniform;\n})(defaultSource);\n", "import defaultSource from \"./defaultSource.js\";\n\nexport default (function sourceRandomInt(source) {\n  function randomInt(min, max) {\n    if (arguments.length < 2) max = min, min = 0;\n    min = Math.floor(min);\n    max = Math.floor(max) - min;\n    return function() {\n      return Math.floor(source() * max + min);\n    };\n  }\n\n  randomInt.source = sourceRandomInt;\n\n  return randomInt;\n})(defaultSource);\n", "import defaultSource from \"./defaultSource.js\";\n\nexport default (function sourceRandomNormal(source) {\n  function randomNormal(mu, sigma) {\n    var x, r;\n    mu = mu == null ? 0 : +mu;\n    sigma = sigma == null ? 1 : +sigma;\n    return function() {\n      var y;\n\n      // If available, use the second previously-generated uniform random.\n      if (x != null) y = x, x = null;\n\n      // Otherwise, generate a new x and y.\n      else do {\n        x = source() * 2 - 1;\n        y = source() * 2 - 1;\n        r = x * x + y * y;\n      } while (!r || r > 1);\n\n      return mu + sigma * y * Math.sqrt(-2 * Math.log(r) / r);\n    };\n  }\n\n  randomNormal.source = sourceRandomNormal;\n\n  return randomNormal;\n})(defaultSource);\n", "import defaultSource from \"./defaultSource.js\";\nimport normal from \"./normal.js\";\n\nexport default (function sourceRandomLogNormal(source) {\n  var N = normal.source(source);\n\n  function randomLogNormal() {\n    var randomNormal = N.apply(this, arguments);\n    return function() {\n      return Math.exp(randomNormal());\n    };\n  }\n\n  randomLogNormal.source = sourceRandomLogNormal;\n\n  return randomLogNormal;\n})(defaultSource);\n", "import defaultSource from \"./defaultSource.js\";\n\nexport default (function sourceRandomIrwinHall(source) {\n  function randomIrwinHall(n) {\n    if ((n = +n) <= 0) return () => 0;\n    return function() {\n      for (var sum = 0, i = n; i > 1; --i) sum += source();\n      return sum + i * source();\n    };\n  }\n\n  randomIrwinHall.source = sourceRandomIrwinHall;\n\n  return randomIrwinHall;\n})(defaultSource);\n", "import defaultSource from \"./defaultSource.js\";\nimport irwinHall from \"./irwinHall.js\";\n\nexport default (function sourceRandomBates(source) {\n  var I = irwinHall.source(source);\n\n  function randomBates(n) {\n    // use limiting distribution at n === 0\n    if ((n = +n) === 0) return source;\n    var randomIrwinHall = I(n);\n    return function() {\n      return randomIrwinHall() / n;\n    };\n  }\n\n  randomBates.source = sourceRandomBates;\n\n  return randomBates;\n})(defaultSource);\n", "import defaultSource from \"./defaultSource.js\";\n\nexport default (function sourceRandomExponential(source) {\n  function randomExponential(lambda) {\n    return function() {\n      return -Math.log1p(-source()) / lambda;\n    };\n  }\n\n  randomExponential.source = sourceRandomExponential;\n\n  return randomExponential;\n})(defaultSource);\n", "import defaultSource from \"./defaultSource.js\";\n\nexport default (function sourceRandomPareto(source) {\n  function randomPareto(alpha) {\n    if ((alpha = +alpha) < 0) throw new RangeError(\"invalid alpha\");\n    alpha = 1 / -alpha;\n    return function() {\n      return Math.pow(1 - source(), alpha);\n    };\n  }\n\n  randomPareto.source = sourceRandomPareto;\n\n  return randomPareto;\n})(defaultSource);\n", "import defaultSource from \"./defaultSource.js\";\n\nexport default (function sourceRandomBernoulli(source) {\n  function randomBernoulli(p) {\n    if ((p = +p) < 0 || p > 1) throw new RangeError(\"invalid p\");\n    return function() {\n      return Math.floor(source() + p);\n    };\n  }\n\n  randomBernoulli.source = sourceRandomBernoulli;\n\n  return randomBernoulli;\n})(defaultSource);\n", "import defaultSource from \"./defaultSource.js\";\n\nexport default (function sourceRandomGeometric(source) {\n  function randomGeometric(p) {\n    if ((p = +p) < 0 || p > 1) throw new RangeError(\"invalid p\");\n    if (p === 0) return () => Infinity;\n    if (p === 1) return () => 1;\n    p = Math.log1p(-p);\n    return function() {\n      return 1 + Math.floor(Math.log1p(-source()) / p);\n    };\n  }\n\n  randomGeometric.source = sourceRandomGeometric;\n\n  return randomGeometric;\n})(defaultSource);\n", "import defaultSource from \"./defaultSource.js\";\nimport normal from \"./normal.js\";\n\nexport default (function sourceRandomGamma(source) {\n  var randomNormal = normal.source(source)();\n\n  function randomGamma(k, theta) {\n    if ((k = +k) < 0) throw new RangeError(\"invalid k\");\n    // degenerate distribution if k === 0\n    if (k === 0) return () => 0;\n    theta = theta == null ? 1 : +theta;\n    // exponential distribution if k === 1\n    if (k === 1) return () => -Math.log1p(-source()) * theta;\n\n    var d = (k < 1 ? k + 1 : k) - 1 / 3,\n        c = 1 / (3 * Math.sqrt(d)),\n        multiplier = k < 1 ? () => Math.pow(source(), 1 / k) : () => 1;\n    return function() {\n      do {\n        do {\n          var x = randomNormal(),\n              v = 1 + c * x;\n        } while (v <= 0);\n        v *= v * v;\n        var u = 1 - source();\n      } while (u >= 1 - 0.0331 * x * x * x * x && Math.log(u) >= 0.5 * x * x + d * (1 - v + Math.log(v)));\n      return d * v * multiplier() * theta;\n    };\n  }\n\n  randomGamma.source = sourceRandomGamma;\n\n  return randomGamma;\n})(defaultSource);\n", "import defaultSource from \"./defaultSource.js\";\nimport gamma from \"./gamma.js\";\n\nexport default (function sourceRandomBeta(source) {\n  var G = gamma.source(source);\n\n  function randomBeta(alpha, beta) {\n    var X = G(alpha),\n        Y = G(beta);\n    return function() {\n      var x = X();\n      return x === 0 ? 0 : x / (x + Y());\n    };\n  }\n\n  randomBeta.source = sourceRandomBeta;\n\n  return randomBeta;\n})(defaultSource);\n", "import defaultSource from \"./defaultSource.js\";\nimport beta from \"./beta.js\";\nimport geometric from \"./geometric.js\";\n\nexport default (function sourceRandomBinomial(source) {\n  var G = geometric.source(source),\n      B = beta.source(source);\n\n  function randomBinomial(n, p) {\n    n = +n;\n    if ((p = +p) >= 1) return () => n;\n    if (p <= 0) return () => 0;\n    return function() {\n      var acc = 0, nn = n, pp = p;\n      while (nn * pp > 16 && nn * (1 - pp) > 16) {\n        var i = Math.floor((nn + 1) * pp),\n            y = B(i, nn - i + 1)();\n        if (y <= pp) {\n          acc += i;\n          nn -= i;\n          pp = (pp - y) / (1 - y);\n        } else {\n          nn = i - 1;\n          pp /= y;\n        }\n      }\n      var sign = pp < 0.5,\n          pFinal = sign ? pp : 1 - pp,\n          g = G(pFinal);\n      for (var s = g(), k = 0; s <= nn; ++k) s += g();\n      return acc + (sign ? k : nn - k);\n    };\n  }\n\n  randomBinomial.source = sourceRandomBinomial;\n\n  return randomBinomial;\n})(defaultSource);\n", "import defaultSource from \"./defaultSource.js\";\n\nexport default (function sourceRandom<PERSON>eibull(source) {\n  function randomWeibull(k, a, b) {\n    var outerFunc;\n    if ((k = +k) === 0) {\n      outerFunc = x => -Math.log(x);\n    } else {\n      k = 1 / k;\n      outerFunc = x => Math.pow(x, k);\n    }\n    a = a == null ? 0 : +a;\n    b = b == null ? 1 : +b;\n    return function() {\n      return a + b * outerFunc(-Math.log1p(-source()));\n    };\n  }\n\n  randomWeibull.source = sourceRandomWeibull;\n\n  return randomWeibull;\n})(defaultSource);\n", "import defaultSource from \"./defaultSource.js\";\n\nexport default (function sourceRandomCauchy(source) {\n  function randomCauchy(a, b) {\n    a = a == null ? 0 : +a;\n    b = b == null ? 1 : +b;\n    return function() {\n      return a + b * Math.tan(Math.PI * source());\n    };\n  }\n\n  randomCauchy.source = sourceRandomCauchy;\n\n  return randomCauchy;\n})(defaultSource);\n", "import defaultSource from \"./defaultSource.js\";\n\nexport default (function sourceRandomLogistic(source) {\n  function randomLogistic(a, b) {\n    a = a == null ? 0 : +a;\n    b = b == null ? 1 : +b;\n    return function() {\n      var u = source();\n      return a + b * Math.log(u / (1 - u));\n    };\n  }\n\n  randomLogistic.source = sourceRandomLogistic;\n\n  return randomLogistic;\n})(defaultSource);\n", "import defaultSource from \"./defaultSource.js\";\nimport binomial from \"./binomial.js\";\nimport gamma from \"./gamma.js\";\n\nexport default (function sourceRandomPoisson(source) {\n  var G = gamma.source(source),\n      B = binomial.source(source);\n\n  function randomPoisson(lambda) {\n    return function() {\n      var acc = 0, l = lambda;\n      while (l > 16) {\n        var n = Math.floor(0.875 * l),\n            t = G(n)();\n        if (t > l) return acc + B(n - 1, l / t)();\n        acc += n;\n        l -= t;\n      }\n      for (var s = -Math.log1p(-source()), k = 0; s <= l; ++k) s -= Math.log1p(-source());\n      return acc + k;\n    };\n  }\n\n  randomPoisson.source = sourceRandomPoisson;\n\n  return randomPoisson;\n})(defaultSource);\n", "// https://en.wikipedia.org/wiki/Linear_congruential_generator#Parameters_in_common_use\nconst mul = 0x19660D;\nconst inc = 0x3C6EF35F;\nconst eps = 1 / 0x100000000;\n\nexport default function lcg(seed = Math.random()) {\n  let state = (0 <= seed && seed < 1 ? seed / eps : Math.abs(seed)) | 0;\n  return () => (state = mul * state + inc | 0, eps * (state >>> 0));\n}\n", "export default x => () => x;\n", "export default function ZoomEvent(type, {\n  sourceEvent,\n  target,\n  transform,\n  dispatch\n}) {\n  Object.defineProperties(this, {\n    type: {value: type, enumerable: true, configurable: true},\n    sourceEvent: {value: sourceEvent, enumerable: true, configurable: true},\n    target: {value: target, enumerable: true, configurable: true},\n    transform: {value: transform, enumerable: true, configurable: true},\n    _: {value: dispatch}\n  });\n}\n", "export function Transform(k, x, y) {\n  this.k = k;\n  this.x = x;\n  this.y = y;\n}\n\nTransform.prototype = {\n  constructor: Transform,\n  scale: function(k) {\n    return k === 1 ? this : new Transform(this.k * k, this.x, this.y);\n  },\n  translate: function(x, y) {\n    return x === 0 & y === 0 ? this : new Transform(this.k, this.x + this.k * x, this.y + this.k * y);\n  },\n  apply: function(point) {\n    return [point[0] * this.k + this.x, point[1] * this.k + this.y];\n  },\n  applyX: function(x) {\n    return x * this.k + this.x;\n  },\n  applyY: function(y) {\n    return y * this.k + this.y;\n  },\n  invert: function(location) {\n    return [(location[0] - this.x) / this.k, (location[1] - this.y) / this.k];\n  },\n  invertX: function(x) {\n    return (x - this.x) / this.k;\n  },\n  invertY: function(y) {\n    return (y - this.y) / this.k;\n  },\n  rescaleX: function(x) {\n    return x.copy().domain(x.range().map(this.invertX, this).map(x.invert, x));\n  },\n  rescaleY: function(y) {\n    return y.copy().domain(y.range().map(this.invertY, this).map(y.invert, y));\n  },\n  toString: function() {\n    return \"translate(\" + this.x + \",\" + this.y + \") scale(\" + this.k + \")\";\n  }\n};\n\nexport var identity = new Transform(1, 0, 0);\n\ntransform.prototype = Transform.prototype;\n\nexport default function transform(node) {\n  while (!node.__zoom) if (!(node = node.parentNode)) return identity;\n  return node.__zoom;\n}\n", "export function nopropagation(event) {\n  event.stopImmediatePropagation();\n}\n\nexport default function(event) {\n  event.preventDefault();\n  event.stopImmediatePropagation();\n}\n", "import {dispatch} from \"d3-dispatch\";\nimport {dragDisable, dragEnable} from \"d3-drag\";\nimport {interpolateZoom} from \"d3-interpolate\";\nimport {select, pointer} from \"d3-selection\";\nimport {interrupt} from \"d3-transition\";\nimport constant from \"./constant.js\";\nimport ZoomEvent from \"./event.js\";\nimport {Transform, identity} from \"./transform.js\";\nimport noevent, {nopropagation} from \"./noevent.js\";\n\n// Ignore right-click, since that should open the context menu.\n// except for pinch-to-zoom, which is sent as a wheel+ctrlKey event\nfunction defaultFilter(event) {\n  return (!event.ctrlKey || event.type === 'wheel') && !event.button;\n}\n\nfunction defaultExtent() {\n  var e = this;\n  if (e instanceof SVGElement) {\n    e = e.ownerSVGElement || e;\n    if (e.hasAttribute(\"viewBox\")) {\n      e = e.viewBox.baseVal;\n      return [[e.x, e.y], [e.x + e.width, e.y + e.height]];\n    }\n    return [[0, 0], [e.width.baseVal.value, e.height.baseVal.value]];\n  }\n  return [[0, 0], [e.clientWidth, e.clientHeight]];\n}\n\nfunction defaultTransform() {\n  return this.__zoom || identity;\n}\n\nfunction defaultWheelDelta(event) {\n  return -event.deltaY * (event.deltaMode === 1 ? 0.05 : event.deltaMode ? 1 : 0.002) * (event.ctrlKey ? 10 : 1);\n}\n\nfunction defaultTouchable() {\n  return navigator.maxTouchPoints || (\"ontouchstart\" in this);\n}\n\nfunction defaultConstrain(transform, extent, translateExtent) {\n  var dx0 = transform.invertX(extent[0][0]) - translateExtent[0][0],\n      dx1 = transform.invertX(extent[1][0]) - translateExtent[1][0],\n      dy0 = transform.invertY(extent[0][1]) - translateExtent[0][1],\n      dy1 = transform.invertY(extent[1][1]) - translateExtent[1][1];\n  return transform.translate(\n    dx1 > dx0 ? (dx0 + dx1) / 2 : Math.min(0, dx0) || Math.max(0, dx1),\n    dy1 > dy0 ? (dy0 + dy1) / 2 : Math.min(0, dy0) || Math.max(0, dy1)\n  );\n}\n\nexport default function() {\n  var filter = defaultFilter,\n      extent = defaultExtent,\n      constrain = defaultConstrain,\n      wheelDelta = defaultWheelDelta,\n      touchable = defaultTouchable,\n      scaleExtent = [0, Infinity],\n      translateExtent = [[-Infinity, -Infinity], [Infinity, Infinity]],\n      duration = 250,\n      interpolate = interpolateZoom,\n      listeners = dispatch(\"start\", \"zoom\", \"end\"),\n      touchstarting,\n      touchfirst,\n      touchending,\n      touchDelay = 500,\n      wheelDelay = 150,\n      clickDistance2 = 0,\n      tapDistance = 10;\n\n  function zoom(selection) {\n    selection\n        .property(\"__zoom\", defaultTransform)\n        .on(\"wheel.zoom\", wheeled, {passive: false})\n        .on(\"mousedown.zoom\", mousedowned)\n        .on(\"dblclick.zoom\", dblclicked)\n      .filter(touchable)\n        .on(\"touchstart.zoom\", touchstarted)\n        .on(\"touchmove.zoom\", touchmoved)\n        .on(\"touchend.zoom touchcancel.zoom\", touchended)\n        .style(\"-webkit-tap-highlight-color\", \"rgba(0,0,0,0)\");\n  }\n\n  zoom.transform = function(collection, transform, point, event) {\n    var selection = collection.selection ? collection.selection() : collection;\n    selection.property(\"__zoom\", defaultTransform);\n    if (collection !== selection) {\n      schedule(collection, transform, point, event);\n    } else {\n      selection.interrupt().each(function() {\n        gesture(this, arguments)\n          .event(event)\n          .start()\n          .zoom(null, typeof transform === \"function\" ? transform.apply(this, arguments) : transform)\n          .end();\n      });\n    }\n  };\n\n  zoom.scaleBy = function(selection, k, p, event) {\n    zoom.scaleTo(selection, function() {\n      var k0 = this.__zoom.k,\n          k1 = typeof k === \"function\" ? k.apply(this, arguments) : k;\n      return k0 * k1;\n    }, p, event);\n  };\n\n  zoom.scaleTo = function(selection, k, p, event) {\n    zoom.transform(selection, function() {\n      var e = extent.apply(this, arguments),\n          t0 = this.__zoom,\n          p0 = p == null ? centroid(e) : typeof p === \"function\" ? p.apply(this, arguments) : p,\n          p1 = t0.invert(p0),\n          k1 = typeof k === \"function\" ? k.apply(this, arguments) : k;\n      return constrain(translate(scale(t0, k1), p0, p1), e, translateExtent);\n    }, p, event);\n  };\n\n  zoom.translateBy = function(selection, x, y, event) {\n    zoom.transform(selection, function() {\n      return constrain(this.__zoom.translate(\n        typeof x === \"function\" ? x.apply(this, arguments) : x,\n        typeof y === \"function\" ? y.apply(this, arguments) : y\n      ), extent.apply(this, arguments), translateExtent);\n    }, null, event);\n  };\n\n  zoom.translateTo = function(selection, x, y, p, event) {\n    zoom.transform(selection, function() {\n      var e = extent.apply(this, arguments),\n          t = this.__zoom,\n          p0 = p == null ? centroid(e) : typeof p === \"function\" ? p.apply(this, arguments) : p;\n      return constrain(identity.translate(p0[0], p0[1]).scale(t.k).translate(\n        typeof x === \"function\" ? -x.apply(this, arguments) : -x,\n        typeof y === \"function\" ? -y.apply(this, arguments) : -y\n      ), e, translateExtent);\n    }, p, event);\n  };\n\n  function scale(transform, k) {\n    k = Math.max(scaleExtent[0], Math.min(scaleExtent[1], k));\n    return k === transform.k ? transform : new Transform(k, transform.x, transform.y);\n  }\n\n  function translate(transform, p0, p1) {\n    var x = p0[0] - p1[0] * transform.k, y = p0[1] - p1[1] * transform.k;\n    return x === transform.x && y === transform.y ? transform : new Transform(transform.k, x, y);\n  }\n\n  function centroid(extent) {\n    return [(+extent[0][0] + +extent[1][0]) / 2, (+extent[0][1] + +extent[1][1]) / 2];\n  }\n\n  function schedule(transition, transform, point, event) {\n    transition\n        .on(\"start.zoom\", function() { gesture(this, arguments).event(event).start(); })\n        .on(\"interrupt.zoom end.zoom\", function() { gesture(this, arguments).event(event).end(); })\n        .tween(\"zoom\", function() {\n          var that = this,\n              args = arguments,\n              g = gesture(that, args).event(event),\n              e = extent.apply(that, args),\n              p = point == null ? centroid(e) : typeof point === \"function\" ? point.apply(that, args) : point,\n              w = Math.max(e[1][0] - e[0][0], e[1][1] - e[0][1]),\n              a = that.__zoom,\n              b = typeof transform === \"function\" ? transform.apply(that, args) : transform,\n              i = interpolate(a.invert(p).concat(w / a.k), b.invert(p).concat(w / b.k));\n          return function(t) {\n            if (t === 1) t = b; // Avoid rounding error on end.\n            else { var l = i(t), k = w / l[2]; t = new Transform(k, p[0] - l[0] * k, p[1] - l[1] * k); }\n            g.zoom(null, t);\n          };\n        });\n  }\n\n  function gesture(that, args, clean) {\n    return (!clean && that.__zooming) || new Gesture(that, args);\n  }\n\n  function Gesture(that, args) {\n    this.that = that;\n    this.args = args;\n    this.active = 0;\n    this.sourceEvent = null;\n    this.extent = extent.apply(that, args);\n    this.taps = 0;\n  }\n\n  Gesture.prototype = {\n    event: function(event) {\n      if (event) this.sourceEvent = event;\n      return this;\n    },\n    start: function() {\n      if (++this.active === 1) {\n        this.that.__zooming = this;\n        this.emit(\"start\");\n      }\n      return this;\n    },\n    zoom: function(key, transform) {\n      if (this.mouse && key !== \"mouse\") this.mouse[1] = transform.invert(this.mouse[0]);\n      if (this.touch0 && key !== \"touch\") this.touch0[1] = transform.invert(this.touch0[0]);\n      if (this.touch1 && key !== \"touch\") this.touch1[1] = transform.invert(this.touch1[0]);\n      this.that.__zoom = transform;\n      this.emit(\"zoom\");\n      return this;\n    },\n    end: function() {\n      if (--this.active === 0) {\n        delete this.that.__zooming;\n        this.emit(\"end\");\n      }\n      return this;\n    },\n    emit: function(type) {\n      var d = select(this.that).datum();\n      listeners.call(\n        type,\n        this.that,\n        new ZoomEvent(type, {\n          sourceEvent: this.sourceEvent,\n          target: zoom,\n          type,\n          transform: this.that.__zoom,\n          dispatch: listeners\n        }),\n        d\n      );\n    }\n  };\n\n  function wheeled(event, ...args) {\n    if (!filter.apply(this, arguments)) return;\n    var g = gesture(this, args).event(event),\n        t = this.__zoom,\n        k = Math.max(scaleExtent[0], Math.min(scaleExtent[1], t.k * Math.pow(2, wheelDelta.apply(this, arguments)))),\n        p = pointer(event);\n\n    // If the mouse is in the same location as before, reuse it.\n    // If there were recent wheel events, reset the wheel idle timeout.\n    if (g.wheel) {\n      if (g.mouse[0][0] !== p[0] || g.mouse[0][1] !== p[1]) {\n        g.mouse[1] = t.invert(g.mouse[0] = p);\n      }\n      clearTimeout(g.wheel);\n    }\n\n    // If this wheel event won’t trigger a transform change, ignore it.\n    else if (t.k === k) return;\n\n    // Otherwise, capture the mouse point and location at the start.\n    else {\n      g.mouse = [p, t.invert(p)];\n      interrupt(this);\n      g.start();\n    }\n\n    noevent(event);\n    g.wheel = setTimeout(wheelidled, wheelDelay);\n    g.zoom(\"mouse\", constrain(translate(scale(t, k), g.mouse[0], g.mouse[1]), g.extent, translateExtent));\n\n    function wheelidled() {\n      g.wheel = null;\n      g.end();\n    }\n  }\n\n  function mousedowned(event, ...args) {\n    if (touchending || !filter.apply(this, arguments)) return;\n    var currentTarget = event.currentTarget,\n        g = gesture(this, args, true).event(event),\n        v = select(event.view).on(\"mousemove.zoom\", mousemoved, true).on(\"mouseup.zoom\", mouseupped, true),\n        p = pointer(event, currentTarget),\n        x0 = event.clientX,\n        y0 = event.clientY;\n\n    dragDisable(event.view);\n    nopropagation(event);\n    g.mouse = [p, this.__zoom.invert(p)];\n    interrupt(this);\n    g.start();\n\n    function mousemoved(event) {\n      noevent(event);\n      if (!g.moved) {\n        var dx = event.clientX - x0, dy = event.clientY - y0;\n        g.moved = dx * dx + dy * dy > clickDistance2;\n      }\n      g.event(event)\n       .zoom(\"mouse\", constrain(translate(g.that.__zoom, g.mouse[0] = pointer(event, currentTarget), g.mouse[1]), g.extent, translateExtent));\n    }\n\n    function mouseupped(event) {\n      v.on(\"mousemove.zoom mouseup.zoom\", null);\n      dragEnable(event.view, g.moved);\n      noevent(event);\n      g.event(event).end();\n    }\n  }\n\n  function dblclicked(event, ...args) {\n    if (!filter.apply(this, arguments)) return;\n    var t0 = this.__zoom,\n        p0 = pointer(event.changedTouches ? event.changedTouches[0] : event, this),\n        p1 = t0.invert(p0),\n        k1 = t0.k * (event.shiftKey ? 0.5 : 2),\n        t1 = constrain(translate(scale(t0, k1), p0, p1), extent.apply(this, args), translateExtent);\n\n    noevent(event);\n    if (duration > 0) select(this).transition().duration(duration).call(schedule, t1, p0, event);\n    else select(this).call(zoom.transform, t1, p0, event);\n  }\n\n  function touchstarted(event, ...args) {\n    if (!filter.apply(this, arguments)) return;\n    var touches = event.touches,\n        n = touches.length,\n        g = gesture(this, args, event.changedTouches.length === n).event(event),\n        started, i, t, p;\n\n    nopropagation(event);\n    for (i = 0; i < n; ++i) {\n      t = touches[i], p = pointer(t, this);\n      p = [p, this.__zoom.invert(p), t.identifier];\n      if (!g.touch0) g.touch0 = p, started = true, g.taps = 1 + !!touchstarting;\n      else if (!g.touch1 && g.touch0[2] !== p[2]) g.touch1 = p, g.taps = 0;\n    }\n\n    if (touchstarting) touchstarting = clearTimeout(touchstarting);\n\n    if (started) {\n      if (g.taps < 2) touchfirst = p[0], touchstarting = setTimeout(function() { touchstarting = null; }, touchDelay);\n      interrupt(this);\n      g.start();\n    }\n  }\n\n  function touchmoved(event, ...args) {\n    if (!this.__zooming) return;\n    var g = gesture(this, args).event(event),\n        touches = event.changedTouches,\n        n = touches.length, i, t, p, l;\n\n    noevent(event);\n    for (i = 0; i < n; ++i) {\n      t = touches[i], p = pointer(t, this);\n      if (g.touch0 && g.touch0[2] === t.identifier) g.touch0[0] = p;\n      else if (g.touch1 && g.touch1[2] === t.identifier) g.touch1[0] = p;\n    }\n    t = g.that.__zoom;\n    if (g.touch1) {\n      var p0 = g.touch0[0], l0 = g.touch0[1],\n          p1 = g.touch1[0], l1 = g.touch1[1],\n          dp = (dp = p1[0] - p0[0]) * dp + (dp = p1[1] - p0[1]) * dp,\n          dl = (dl = l1[0] - l0[0]) * dl + (dl = l1[1] - l0[1]) * dl;\n      t = scale(t, Math.sqrt(dp / dl));\n      p = [(p0[0] + p1[0]) / 2, (p0[1] + p1[1]) / 2];\n      l = [(l0[0] + l1[0]) / 2, (l0[1] + l1[1]) / 2];\n    }\n    else if (g.touch0) p = g.touch0[0], l = g.touch0[1];\n    else return;\n\n    g.zoom(\"touch\", constrain(translate(t, p, l), g.extent, translateExtent));\n  }\n\n  function touchended(event, ...args) {\n    if (!this.__zooming) return;\n    var g = gesture(this, args).event(event),\n        touches = event.changedTouches,\n        n = touches.length, i, t;\n\n    nopropagation(event);\n    if (touchending) clearTimeout(touchending);\n    touchending = setTimeout(function() { touchending = null; }, touchDelay);\n    for (i = 0; i < n; ++i) {\n      t = touches[i];\n      if (g.touch0 && g.touch0[2] === t.identifier) delete g.touch0;\n      else if (g.touch1 && g.touch1[2] === t.identifier) delete g.touch1;\n    }\n    if (g.touch1 && !g.touch0) g.touch0 = g.touch1, delete g.touch1;\n    if (g.touch0) g.touch0[1] = this.__zoom.invert(g.touch0[0]);\n    else {\n      g.end();\n      // If this was a dbltap, reroute to the (optional) dblclick.zoom handler.\n      if (g.taps === 2) {\n        t = pointer(t, this);\n        if (Math.hypot(touchfirst[0] - t[0], touchfirst[1] - t[1]) < tapDistance) {\n          var p = select(this).on(\"dblclick.zoom\");\n          if (p) p.apply(this, arguments);\n        }\n      }\n    }\n  }\n\n  zoom.wheelDelta = function(_) {\n    return arguments.length ? (wheelDelta = typeof _ === \"function\" ? _ : constant(+_), zoom) : wheelDelta;\n  };\n\n  zoom.filter = function(_) {\n    return arguments.length ? (filter = typeof _ === \"function\" ? _ : constant(!!_), zoom) : filter;\n  };\n\n  zoom.touchable = function(_) {\n    return arguments.length ? (touchable = typeof _ === \"function\" ? _ : constant(!!_), zoom) : touchable;\n  };\n\n  zoom.extent = function(_) {\n    return arguments.length ? (extent = typeof _ === \"function\" ? _ : constant([[+_[0][0], +_[0][1]], [+_[1][0], +_[1][1]]]), zoom) : extent;\n  };\n\n  zoom.scaleExtent = function(_) {\n    return arguments.length ? (scaleExtent[0] = +_[0], scaleExtent[1] = +_[1], zoom) : [scaleExtent[0], scaleExtent[1]];\n  };\n\n  zoom.translateExtent = function(_) {\n    return arguments.length ? (translateExtent[0][0] = +_[0][0], translateExtent[1][0] = +_[1][0], translateExtent[0][1] = +_[0][1], translateExtent[1][1] = +_[1][1], zoom) : [[translateExtent[0][0], translateExtent[0][1]], [translateExtent[1][0], translateExtent[1][1]]];\n  };\n\n  zoom.constrain = function(_) {\n    return arguments.length ? (constrain = _, zoom) : constrain;\n  };\n\n  zoom.duration = function(_) {\n    return arguments.length ? (duration = +_, zoom) : duration;\n  };\n\n  zoom.interpolate = function(_) {\n    return arguments.length ? (interpolate = _, zoom) : interpolate;\n  };\n\n  zoom.on = function() {\n    var value = listeners.on.apply(listeners, arguments);\n    return value === listeners ? zoom : value;\n  };\n\n  zoom.clickDistance = function(_) {\n    return arguments.length ? (clickDistance2 = (_ = +_) * _, zoom) : Math.sqrt(clickDistance2);\n  };\n\n  zoom.tapDistance = function(_) {\n    return arguments.length ? (tapDistance = +_, zoom) : tapDistance;\n  };\n\n  return zoom;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAe,SAARA,kBAAiBC,IAAG;AACzB,SAAOA;AACT;;;ACAA,IAAI,MAAM;AAAV,IACI,QAAQ;AADZ,IAEI,SAAS;AAFb,IAGI,OAAO;AAHX,IAII,UAAU;AAEd,SAAS,WAAWC,IAAG;AACrB,SAAO,eAAeA,KAAI;AAC5B;AAEA,SAAS,WAAWC,IAAG;AACrB,SAAO,iBAAiBA,KAAI;AAC9B;AAEA,SAAS,OAAO,OAAO;AACrB,SAAO,OAAK,CAAC,MAAM,CAAC;AACtB;AAEA,SAAS,OAAO,OAAO,QAAQ;AAC7B,WAAS,KAAK,IAAI,GAAG,MAAM,UAAU,IAAI,SAAS,CAAC,IAAI;AACvD,MAAI,MAAM,MAAM,EAAG,UAAS,KAAK,MAAM,MAAM;AAC7C,SAAO,OAAK,CAAC,MAAM,CAAC,IAAI;AAC1B;AAEA,SAAS,WAAW;AAClB,SAAO,CAAC,KAAK;AACf;AAEA,SAAS,KAAK,QAAQ,OAAO;AAC3B,MAAI,gBAAgB,CAAC,GACjB,aAAa,MACbC,cAAa,MACb,gBAAgB,GAChB,gBAAgB,GAChB,cAAc,GACd,SAAS,OAAO,WAAW,eAAe,OAAO,mBAAmB,IAAI,IAAI,KAC5E,IAAI,WAAW,OAAO,WAAW,OAAO,KAAK,GAC7CF,KAAI,WAAW,QAAQ,WAAW,QAAQ,MAAM,KAChDG,aAAY,WAAW,OAAO,WAAW,SAAS,aAAa;AAEnE,WAASC,MAAK,SAAS;AACrB,QAAI,SAAS,cAAc,OAAQ,MAAM,QAAQ,MAAM,MAAM,MAAM,OAAO,aAAa,IAAI,MAAM,OAAO,IAAK,YACzGC,UAASH,eAAc,OAAQ,MAAM,aAAa,MAAM,WAAW,MAAM,OAAO,aAAa,IAAII,oBAAYJ,aAC7G,UAAU,KAAK,IAAI,eAAe,CAAC,IAAI,aACvCK,SAAQ,MAAM,MAAM,GACpB,SAAS,CAACA,OAAM,CAAC,IAAI,QACrB,SAAS,CAACA,OAAMA,OAAM,SAAS,CAAC,IAAI,QACpC,YAAY,MAAM,YAAY,SAAS,QAAQ,MAAM,KAAK,GAAG,MAAM,GACnE,YAAY,QAAQ,YAAY,QAAQ,UAAU,IAAI,SACtDC,QAAO,UAAU,UAAU,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,GACjD,OAAO,UAAU,UAAU,OAAO,EAAE,KAAK,QAAQ,KAAK,EAAE,MAAM,GAC9D,WAAW,KAAK,KAAK,GACrB,YAAY,KAAK,MAAM,EAAE,OAAO,GAAG,EAAE,KAAK,SAAS,MAAM,GACzD,OAAO,KAAK,OAAO,MAAM,GACzB,OAAO,KAAK,OAAO,MAAM;AAE7B,IAAAA,QAAOA,MAAK,MAAMA,MAAK,MAAM,EAAE,OAAO,QAAQ,OAAO,EAChD,KAAK,SAAS,QAAQ,EACtB,KAAK,UAAU,cAAc,CAAC;AAEnC,WAAO,KAAK,MAAM,SAAS;AAE3B,WAAO,KAAK,MAAM,UAAU,OAAO,MAAM,EACpC,KAAK,UAAU,cAAc,EAC7B,KAAKR,KAAI,KAAK,IAAI,aAAa,CAAC;AAErC,WAAO,KAAK,MAAM,UAAU,OAAO,MAAM,EACpC,KAAK,QAAQ,cAAc,EAC3B,KAAKA,IAAG,IAAI,OAAO,EACnB,KAAK,MAAM,WAAW,MAAM,QAAQ,WAAW,SAAS,WAAW,QAAQ,CAAC;AAEjF,QAAI,YAAY,WAAW;AACzB,MAAAQ,QAAOA,MAAK,WAAW,OAAO;AAC9B,aAAO,KAAK,WAAW,OAAO;AAC9B,aAAO,KAAK,WAAW,OAAO;AAC9B,aAAO,KAAK,WAAW,OAAO;AAE9B,iBAAW,SAAS,WAAW,OAAO,EACjC,KAAK,WAAW,OAAO,EACvB,KAAK,aAAa,SAAS,GAAG;AAAE,eAAO,SAAS,IAAI,SAAS,CAAC,CAAC,IAAIL,WAAU,IAAI,MAAM,IAAI,KAAK,aAAa,WAAW;AAAA,MAAG,CAAC;AAEjI,gBACK,KAAK,WAAW,OAAO,EACvB,KAAK,aAAa,SAAS,GAAG;AAAE,YAAI,IAAI,KAAK,WAAW;AAAQ,eAAOA,YAAW,KAAK,SAAS,IAAI,EAAE,CAAC,CAAC,IAAI,IAAI,SAAS,CAAC,KAAK,MAAM;AAAA,MAAG,CAAC;AAAA,IAChJ;AAEA,aAAS,OAAO;AAEhB,IAAAK,MACK,KAAK,KAAK,WAAW,QAAQ,WAAW,QAClC,gBAAgB,MAAM,IAAI,gBAAgB,MAAM,SAAS,MAAM,SAAS,MAAM,SAAS,MAAM,IAAI,gBAAgB,MAAM,SAAS,MAAM,SAAS,MAAM,SACrJ,gBAAgB,MAAM,SAAS,MAAM,IAAI,gBAAgB,MAAM,SAAS,MAAM,SAAS,MAAM,IAAI,gBAAgB,MAAM,SAAS,MAAM,SAAS,MAAM,MAAO;AAEvK,SACK,KAAK,WAAW,CAAC,EACjB,KAAK,aAAa,SAAS,GAAG;AAAE,aAAOL,WAAU,SAAS,CAAC,IAAI,MAAM;AAAA,IAAG,CAAC;AAE9E,SACK,KAAKH,KAAI,KAAK,IAAI,aAAa;AAEpC,SACK,KAAKA,IAAG,IAAI,OAAO,EACnB,KAAKK,OAAM;AAEhB,cAAU,OAAO,QAAQ,EACpB,KAAK,QAAQ,MAAM,EACnB,KAAK,aAAa,EAAE,EACpB,KAAK,eAAe,YAAY,EAChC,KAAK,eAAe,WAAW,QAAQ,UAAU,WAAW,OAAO,QAAQ,QAAQ;AAExF,cACK,KAAK,WAAW;AAAE,WAAK,SAAS;AAAA,IAAU,CAAC;AAAA,EAClD;AAEA,EAAAD,MAAK,QAAQ,SAAS,GAAG;AACvB,WAAO,UAAU,UAAU,QAAQ,GAAGA,SAAQ;AAAA,EAChD;AAEA,EAAAA,MAAK,QAAQ,WAAW;AACtB,WAAO,gBAAgB,MAAM,KAAK,SAAS,GAAGA;AAAA,EAChD;AAEA,EAAAA,MAAK,gBAAgB,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,gBAAgB,KAAK,OAAO,CAAC,IAAI,MAAM,KAAK,CAAC,GAAGA,SAAQ,cAAc,MAAM;AAAA,EACzG;AAEA,EAAAA,MAAK,aAAa,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAU,aAAa,KAAK,OAAO,OAAO,MAAM,KAAK,CAAC,GAAGA,SAAQ,cAAc,WAAW,MAAM;AAAA,EACnH;AAEA,EAAAA,MAAK,aAAa,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAUF,cAAa,GAAGE,SAAQF;AAAA,EACrD;AAEA,EAAAE,MAAK,WAAW,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,gBAAgB,gBAAgB,CAAC,GAAGA,SAAQ;AAAA,EACzE;AAEA,EAAAA,MAAK,gBAAgB,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,gBAAgB,CAAC,GAAGA,SAAQ;AAAA,EACzD;AAEA,EAAAA,MAAK,gBAAgB,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,gBAAgB,CAAC,GAAGA,SAAQ;AAAA,EACzD;AAEA,EAAAA,MAAK,cAAc,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,cAAc,CAAC,GAAGA,SAAQ;AAAA,EACvD;AAEA,EAAAA,MAAK,SAAS,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,SAAS,CAAC,GAAGA,SAAQ;AAAA,EAClD;AAEA,SAAOA;AACT;AAEO,SAAS,QAAQ,OAAO;AAC7B,SAAO,KAAK,KAAK,KAAK;AACxB;AAEO,SAAS,UAAU,OAAO;AAC/B,SAAO,KAAK,OAAO,KAAK;AAC1B;AAEO,SAAS,WAAW,OAAO;AAChC,SAAO,KAAK,QAAQ,KAAK;AAC3B;AAEO,SAAS,SAAS,OAAO;AAC9B,SAAO,KAAK,MAAM,KAAK;AACzB;;;AC7KA,IAAI,OAAO,EAAC,OAAO,MAAM;AAAC,EAAC;AAE3B,SAAS,WAAW;AAClB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG,EAAE,GAAG;AAC3D,QAAI,EAAE,IAAI,UAAU,CAAC,IAAI,OAAQ,KAAK,KAAM,QAAQ,KAAK,CAAC,EAAG,OAAM,IAAI,MAAM,mBAAmB,CAAC;AACjG,MAAE,CAAC,IAAI,CAAC;AAAA,EACV;AACA,SAAO,IAAI,SAAS,CAAC;AACvB;AAEA,SAAS,SAAS,GAAG;AACnB,OAAK,IAAI;AACX;AAEA,SAAS,eAAe,WAAW,OAAO;AACxC,SAAO,UAAU,KAAK,EAAE,MAAM,OAAO,EAAE,IAAI,SAAS,GAAG;AACrD,QAAI,OAAO,IAAI,IAAI,EAAE,QAAQ,GAAG;AAChC,QAAI,KAAK,EAAG,QAAO,EAAE,MAAM,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,GAAG,CAAC;AACnD,QAAI,KAAK,CAAC,MAAM,eAAe,CAAC,EAAG,OAAM,IAAI,MAAM,mBAAmB,CAAC;AACvE,WAAO,EAAC,MAAM,GAAG,KAAU;AAAA,EAC7B,CAAC;AACH;AAEA,SAAS,YAAY,SAAS,YAAY;AAAA,EACxC,aAAa;AAAA,EACb,IAAI,SAAS,UAAU,UAAU;AAC/B,QAAI,IAAI,KAAK,GACT,IAAI,eAAe,WAAW,IAAI,CAAC,GACnC,GACA,IAAI,IACJ,IAAI,EAAE;AAGV,QAAI,UAAU,SAAS,GAAG;AACxB,aAAO,EAAE,IAAI,EAAG,MAAK,KAAK,WAAW,EAAE,CAAC,GAAG,UAAU,IAAI,IAAI,EAAE,CAAC,GAAG,SAAS,IAAI,GAAI,QAAO;AAC3F;AAAA,IACF;AAIA,QAAI,YAAY,QAAQ,OAAO,aAAa,WAAY,OAAM,IAAI,MAAM,uBAAuB,QAAQ;AACvG,WAAO,EAAE,IAAI,GAAG;AACd,UAAI,KAAK,WAAW,EAAE,CAAC,GAAG,KAAM,GAAE,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,SAAS,MAAM,QAAQ;AAAA,eAC/D,YAAY,KAAM,MAAK,KAAK,EAAG,GAAE,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,SAAS,MAAM,IAAI;AAAA,IAC9E;AAEA,WAAO;AAAA,EACT;AAAA,EACA,MAAM,WAAW;AACf,QAAI,OAAO,CAAC,GAAG,IAAI,KAAK;AACxB,aAAS,KAAK,EAAG,MAAK,CAAC,IAAI,EAAE,CAAC,EAAE,MAAM;AACtC,WAAO,IAAI,SAAS,IAAI;AAAA,EAC1B;AAAA,EACA,MAAM,SAASK,OAAM,MAAM;AACzB,SAAK,IAAI,UAAU,SAAS,KAAK,EAAG,UAAS,OAAO,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,EAAE,EAAG,MAAK,CAAC,IAAI,UAAU,IAAI,CAAC;AACpH,QAAI,CAAC,KAAK,EAAE,eAAeA,KAAI,EAAG,OAAM,IAAI,MAAM,mBAAmBA,KAAI;AACzE,SAAK,IAAI,KAAK,EAAEA,KAAI,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAI,GAAG,EAAE,EAAG,GAAE,CAAC,EAAE,MAAM,MAAM,MAAM,IAAI;AAAA,EACrF;AAAA,EACA,OAAO,SAASA,OAAM,MAAM,MAAM;AAChC,QAAI,CAAC,KAAK,EAAE,eAAeA,KAAI,EAAG,OAAM,IAAI,MAAM,mBAAmBA,KAAI;AACzE,aAAS,IAAI,KAAK,EAAEA,KAAI,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAI,GAAG,EAAE,EAAG,GAAE,CAAC,EAAE,MAAM,MAAM,MAAM,IAAI;AAAA,EACzF;AACF;AAEA,SAAS,IAAIA,OAAM,MAAM;AACvB,WAAS,IAAI,GAAG,IAAIA,MAAK,QAAQC,IAAG,IAAI,GAAG,EAAE,GAAG;AAC9C,SAAKA,KAAID,MAAK,CAAC,GAAG,SAAS,MAAM;AAC/B,aAAOC,GAAE;AAAA,IACX;AAAA,EACF;AACF;AAEA,SAAS,IAAID,OAAM,MAAM,UAAU;AACjC,WAAS,IAAI,GAAG,IAAIA,MAAK,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC3C,QAAIA,MAAK,CAAC,EAAE,SAAS,MAAM;AACzB,MAAAA,MAAK,CAAC,IAAI,MAAMA,QAAOA,MAAK,MAAM,GAAG,CAAC,EAAE,OAAOA,MAAK,MAAM,IAAI,CAAC,CAAC;AAChE;AAAA,IACF;AAAA,EACF;AACA,MAAI,YAAY,KAAM,CAAAA,MAAK,KAAK,EAAC,MAAY,OAAO,SAAQ,CAAC;AAC7D,SAAOA;AACT;AAEA,IAAO,mBAAQ;;;ACjFR,IAAM,aAAa,EAAC,SAAS,MAAK;AAClC,IAAM,oBAAoB,EAAC,SAAS,MAAM,SAAS,MAAK;AAExD,SAAS,cAAc,OAAO;AACnC,QAAM,yBAAyB;AACjC;AAEe,SAAR,gBAAiB,OAAO;AAC7B,QAAM,eAAe;AACrB,QAAM,yBAAyB;AACjC;;;ACTe,SAAR,eAAiB,MAAM;AAC5B,MAAIE,QAAO,KAAK,SAAS,iBACrB,YAAY,eAAO,IAAI,EAAE,GAAG,kBAAkB,iBAAS,iBAAiB;AAC5E,MAAI,mBAAmBA,OAAM;AAC3B,cAAU,GAAG,oBAAoB,iBAAS,iBAAiB;AAAA,EAC7D,OAAO;AACL,IAAAA,MAAK,aAAaA,MAAK,MAAM;AAC7B,IAAAA,MAAK,MAAM,gBAAgB;AAAA,EAC7B;AACF;AAEO,SAAS,QAAQ,MAAM,SAAS;AACrC,MAAIA,QAAO,KAAK,SAAS,iBACrB,YAAY,eAAO,IAAI,EAAE,GAAG,kBAAkB,IAAI;AACtD,MAAI,SAAS;AACX,cAAU,GAAG,cAAc,iBAAS,iBAAiB;AACrD,eAAW,WAAW;AAAE,gBAAU,GAAG,cAAc,IAAI;AAAA,IAAG,GAAG,CAAC;AAAA,EAChE;AACA,MAAI,mBAAmBA,OAAM;AAC3B,cAAU,GAAG,oBAAoB,IAAI;AAAA,EACvC,OAAO;AACL,IAAAA,MAAK,MAAM,gBAAgBA,MAAK;AAChC,WAAOA,MAAK;AAAA,EACd;AACF;;;AC3BA,IAAO,mBAAQ,CAAAC,OAAK,MAAMA;;;ACAX,SAAR,UAA2BC,OAAM;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,GAAAC;AAAA,EAAG,GAAAC;AAAA,EAAG;AAAA,EAAI;AAAA,EACV,UAAAC;AACF,GAAG;AACD,SAAO,iBAAiB,MAAM;AAAA,IAC5B,MAAM,EAAC,OAAOH,OAAM,YAAY,MAAM,cAAc,KAAI;AAAA,IACxD,aAAa,EAAC,OAAO,aAAa,YAAY,MAAM,cAAc,KAAI;AAAA,IACtE,SAAS,EAAC,OAAO,SAAS,YAAY,MAAM,cAAc,KAAI;AAAA,IAC9D,QAAQ,EAAC,OAAO,QAAQ,YAAY,MAAM,cAAc,KAAI;AAAA,IAC5D,YAAY,EAAC,OAAO,YAAY,YAAY,MAAM,cAAc,KAAI;AAAA,IACpE,QAAQ,EAAC,OAAO,QAAQ,YAAY,MAAM,cAAc,KAAI;AAAA,IAC5D,GAAG,EAAC,OAAOC,IAAG,YAAY,MAAM,cAAc,KAAI;AAAA,IAClD,GAAG,EAAC,OAAOC,IAAG,YAAY,MAAM,cAAc,KAAI;AAAA,IAClD,IAAI,EAAC,OAAO,IAAI,YAAY,MAAM,cAAc,KAAI;AAAA,IACpD,IAAI,EAAC,OAAO,IAAI,YAAY,MAAM,cAAc,KAAI;AAAA,IACpD,GAAG,EAAC,OAAOC,UAAQ;AAAA,EACrB,CAAC;AACH;AAEA,UAAU,UAAU,KAAK,WAAW;AAClC,MAAI,QAAQ,KAAK,EAAE,GAAG,MAAM,KAAK,GAAG,SAAS;AAC7C,SAAO,UAAU,KAAK,IAAI,OAAO;AACnC;;;ACnBA,SAAS,cAAc,OAAO;AAC5B,SAAO,CAAC,MAAM,WAAW,CAAC,MAAM;AAClC;AAEA,SAAS,mBAAmB;AAC1B,SAAO,KAAK;AACd;AAEA,SAAS,eAAe,OAAO,GAAG;AAChC,SAAO,KAAK,OAAO,EAAC,GAAG,MAAM,GAAG,GAAG,MAAM,EAAC,IAAI;AAChD;AAEA,SAAS,mBAAmB;AAC1B,SAAO,UAAU,kBAAmB,kBAAkB;AACxD;AAEe,SAAR,eAAmB;AACxB,MAAIC,UAAS,eACT,YAAY,kBACZ,UAAU,gBACV,YAAY,kBACZ,WAAW,CAAC,GACZ,YAAY,iBAAS,SAAS,QAAQ,KAAK,GAC3C,SAAS,GACT,YACA,YACA,aACA,aACA,iBAAiB;AAErB,WAAS,KAAK,WAAW;AACvB,cACK,GAAG,kBAAkB,WAAW,EAClC,OAAO,SAAS,EACd,GAAG,mBAAmB,YAAY,EAClC,GAAG,kBAAkB,YAAY,UAAU,EAC3C,GAAG,kCAAkC,UAAU,EAC/C,MAAM,gBAAgB,MAAM,EAC5B,MAAM,+BAA+B,eAAe;AAAA,EAC3D;AAEA,WAAS,YAAY,OAAO,GAAG;AAC7B,QAAI,eAAe,CAACA,QAAO,KAAK,MAAM,OAAO,CAAC,EAAG;AACjD,QAAI,UAAU,YAAY,MAAM,UAAU,KAAK,MAAM,OAAO,CAAC,GAAG,OAAO,GAAG,OAAO;AACjF,QAAI,CAAC,QAAS;AACd,mBAAO,MAAM,IAAI,EACd,GAAG,kBAAkB,YAAY,iBAAiB,EAClD,GAAG,gBAAgB,YAAY,iBAAiB;AACnD,mBAAO,MAAM,IAAI;AACjB,kBAAc,KAAK;AACnB,kBAAc;AACd,iBAAa,MAAM;AACnB,iBAAa,MAAM;AACnB,YAAQ,SAAS,KAAK;AAAA,EACxB;AAEA,WAAS,WAAW,OAAO;AACzB,oBAAQ,KAAK;AACb,QAAI,CAAC,aAAa;AAChB,UAAI,KAAK,MAAM,UAAU,YAAY,KAAK,MAAM,UAAU;AAC1D,oBAAc,KAAK,KAAK,KAAK,KAAK;AAAA,IACpC;AACA,aAAS,MAAM,QAAQ,KAAK;AAAA,EAC9B;AAEA,WAAS,WAAW,OAAO;AACzB,mBAAO,MAAM,IAAI,EAAE,GAAG,+BAA+B,IAAI;AACzD,YAAQ,MAAM,MAAM,WAAW;AAC/B,oBAAQ,KAAK;AACb,aAAS,MAAM,OAAO,KAAK;AAAA,EAC7B;AAEA,WAAS,aAAa,OAAO,GAAG;AAC9B,QAAI,CAACA,QAAO,KAAK,MAAM,OAAO,CAAC,EAAG;AAClC,QAAI,UAAU,MAAM,gBAChBC,KAAI,UAAU,KAAK,MAAM,OAAO,CAAC,GACjC,IAAI,QAAQ,QAAQ,GAAG;AAE3B,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,UAAI,UAAU,YAAY,MAAMA,IAAG,OAAO,GAAG,QAAQ,CAAC,EAAE,YAAY,QAAQ,CAAC,CAAC,GAAG;AAC/E,sBAAc,KAAK;AACnB,gBAAQ,SAAS,OAAO,QAAQ,CAAC,CAAC;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AAEA,WAAS,WAAW,OAAO;AACzB,QAAI,UAAU,MAAM,gBAChB,IAAI,QAAQ,QAAQ,GAAG;AAE3B,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,UAAI,UAAU,SAAS,QAAQ,CAAC,EAAE,UAAU,GAAG;AAC7C,wBAAQ,KAAK;AACb,gBAAQ,QAAQ,OAAO,QAAQ,CAAC,CAAC;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AAEA,WAAS,WAAW,OAAO;AACzB,QAAI,UAAU,MAAM,gBAChB,IAAI,QAAQ,QAAQ,GAAG;AAE3B,QAAI,YAAa,cAAa,WAAW;AACzC,kBAAc,WAAW,WAAW;AAAE,oBAAc;AAAA,IAAM,GAAG,GAAG;AAChE,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,UAAI,UAAU,SAAS,QAAQ,CAAC,EAAE,UAAU,GAAG;AAC7C,sBAAc,KAAK;AACnB,gBAAQ,OAAO,OAAO,QAAQ,CAAC,CAAC;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAEA,WAAS,YAAY,MAAMC,YAAW,OAAO,GAAG,YAAY,OAAO;AACjE,QAAIC,YAAW,UAAU,KAAK,GAC1B,IAAI,gBAAQ,SAAS,OAAOD,UAAS,GAAG,IAAI,IAC5C;AAEJ,SAAK,IAAI,QAAQ,KAAK,MAAM,IAAI,UAAU,eAAe;AAAA,MACrD,aAAa;AAAA,MACb,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA,GAAG,EAAE,CAAC;AAAA,MACN,GAAG,EAAE,CAAC;AAAA,MACN,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,UAAAC;AAAA,IACF,CAAC,GAAG,CAAC,MAAM,KAAM;AAEnB,SAAK,EAAE,IAAI,EAAE,CAAC,KAAK;AACnB,SAAK,EAAE,IAAI,EAAE,CAAC,KAAK;AAEnB,WAAO,SAAS,QAAQC,OAAMC,QAAOC,QAAO;AAC1C,UAAI,KAAK,GAAG;AACZ,cAAQF,OAAM;AAAA,QACZ,KAAK;AAAS,mBAAS,UAAU,IAAI,SAAS,IAAI;AAAU;AAAA,QAC5D,KAAK;AAAO,iBAAO,SAAS,UAAU,GAAG,EAAE;AAAA,QAC3C,KAAK;AAAQ,cAAI,gBAAQE,UAASD,QAAOH,UAAS,GAAG,IAAI;AAAQ;AAAA,MACnE;AACA,MAAAC,UAAS;AAAA,QACPC;AAAA,QACA;AAAA,QACA,IAAI,UAAUA,OAAM;AAAA,UAClB,aAAaC;AAAA,UACb,SAAS;AAAA,UACT,QAAQ;AAAA,UACR;AAAA,UACA,QAAQ;AAAA,UACR,GAAG,EAAE,CAAC,IAAI;AAAA,UACV,GAAG,EAAE,CAAC,IAAI;AAAA,UACV,IAAI,EAAE,CAAC,IAAI,GAAG,CAAC;AAAA,UACf,IAAI,EAAE,CAAC,IAAI,GAAG,CAAC;AAAA,UACf,UAAAF;AAAA,QACF,CAAC;AAAA,QACD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,OAAK,SAAS,SAAS,GAAG;AACxB,WAAO,UAAU,UAAUH,UAAS,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,CAAC,GAAG,QAAQA;AAAA,EAC3F;AAEA,OAAK,YAAY,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,YAAY,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,GAAG,QAAQ;AAAA,EAC5F;AAEA,OAAK,UAAU,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,UAAU,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,GAAG,QAAQ;AAAA,EAC1F;AAEA,OAAK,YAAY,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,YAAY,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,CAAC,GAAG,QAAQ;AAAA,EAC9F;AAEA,OAAK,KAAK,WAAW;AACnB,QAAI,QAAQ,UAAU,GAAG,MAAM,WAAW,SAAS;AACnD,WAAO,UAAU,YAAY,OAAO;AAAA,EACtC;AAEA,OAAK,gBAAgB,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,kBAAkB,IAAI,CAAC,KAAK,GAAG,QAAQ,KAAK,KAAK,cAAc;AAAA,EAC5F;AAEA,SAAO;AACT;;;ACjMA,IAAI,QAAQ;AAAZ,IACI,UAAU;AADd,IAEI,WAAW;AAFf,IAGI,YAAY;AAHhB,IAII;AAJJ,IAKI;AALJ,IAMI,YAAY;AANhB,IAOI,WAAW;AAPf,IAQI,YAAY;AARhB,IASI,QAAQ,OAAO,gBAAgB,YAAY,YAAY,MAAM,cAAc;AAT/E,IAUI,WAAW,OAAO,WAAW,YAAY,OAAO,wBAAwB,OAAO,sBAAsB,KAAK,MAAM,IAAI,SAAS,GAAG;AAAE,aAAW,GAAG,EAAE;AAAG;AAElJ,SAAS,MAAM;AACpB,SAAO,aAAa,SAAS,QAAQ,GAAG,WAAW,MAAM,IAAI,IAAI;AACnE;AAEA,SAAS,WAAW;AAClB,aAAW;AACb;AAEO,SAAS,QAAQ;AACtB,OAAK,QACL,KAAK,QACL,KAAK,QAAQ;AACf;AAEA,MAAM,YAAY,MAAM,YAAY;AAAA,EAClC,aAAa;AAAA,EACb,SAAS,SAAS,UAAU,OAAOO,OAAM;AACvC,QAAI,OAAO,aAAa,WAAY,OAAM,IAAI,UAAU,4BAA4B;AACpF,IAAAA,SAAQA,SAAQ,OAAO,IAAI,IAAI,CAACA,UAAS,SAAS,OAAO,IAAI,CAAC;AAC9D,QAAI,CAAC,KAAK,SAAS,aAAa,MAAM;AACpC,UAAI,SAAU,UAAS,QAAQ;AAAA,UAC1B,YAAW;AAChB,iBAAW;AAAA,IACb;AACA,SAAK,QAAQ;AACb,SAAK,QAAQA;AACb,UAAM;AAAA,EACR;AAAA,EACA,MAAM,WAAW;AACf,QAAI,KAAK,OAAO;AACd,WAAK,QAAQ;AACb,WAAK,QAAQ;AACb,YAAM;AAAA,IACR;AAAA,EACF;AACF;AAEO,SAAS,MAAM,UAAU,OAAOA,OAAM;AAC3C,MAAI,IAAI,IAAI;AACZ,IAAE,QAAQ,UAAU,OAAOA,KAAI;AAC/B,SAAO;AACT;AAEO,SAAS,aAAa;AAC3B,MAAI;AACJ,IAAE;AACF,MAAI,IAAI,UAAU;AAClB,SAAO,GAAG;AACR,SAAK,IAAI,WAAW,EAAE,UAAU,EAAG,GAAE,MAAM,KAAK,QAAW,CAAC;AAC5D,QAAI,EAAE;AAAA,EACR;AACA,IAAE;AACJ;AAEA,SAAS,OAAO;AACd,cAAY,YAAY,MAAM,IAAI,KAAK;AACvC,UAAQ,UAAU;AAClB,MAAI;AACF,eAAW;AAAA,EACb,UAAE;AACA,YAAQ;AACR,QAAI;AACJ,eAAW;AAAA,EACb;AACF;AAEA,SAAS,OAAO;AACd,MAAIC,OAAM,MAAM,IAAI,GAAG,QAAQA,OAAM;AACrC,MAAI,QAAQ,UAAW,cAAa,OAAO,YAAYA;AACzD;AAEA,SAAS,MAAM;AACb,MAAI,IAAI,KAAK,UAAU,IAAID,QAAO;AAClC,SAAO,IAAI;AACT,QAAI,GAAG,OAAO;AACZ,UAAIA,QAAO,GAAG,MAAO,CAAAA,QAAO,GAAG;AAC/B,WAAK,IAAI,KAAK,GAAG;AAAA,IACnB,OAAO;AACL,WAAK,GAAG,OAAO,GAAG,QAAQ;AAC1B,WAAK,KAAK,GAAG,QAAQ,KAAK,WAAW;AAAA,IACvC;AAAA,EACF;AACA,aAAW;AACX,QAAMA,KAAI;AACZ;AAEA,SAAS,MAAMA,OAAM;AACnB,MAAI,MAAO;AACX,MAAI,QAAS,WAAU,aAAa,OAAO;AAC3C,MAAI,QAAQA,QAAO;AACnB,MAAI,QAAQ,IAAI;AACd,QAAIA,QAAO,SAAU,WAAU,WAAW,MAAMA,QAAO,MAAM,IAAI,IAAI,SAAS;AAC9E,QAAI,SAAU,YAAW,cAAc,QAAQ;AAAA,EACjD,OAAO;AACL,QAAI,CAAC,SAAU,aAAY,MAAM,IAAI,GAAG,WAAW,YAAY,MAAM,SAAS;AAC9E,YAAQ,GAAG,SAAS,IAAI;AAAA,EAC1B;AACF;;;AC3Ge,SAAR,gBAAiB,UAAU,OAAOE,OAAM;AAC7C,MAAI,IAAI,IAAI;AACZ,UAAQ,SAAS,OAAO,IAAI,CAAC;AAC7B,IAAE,QAAQ,aAAW;AACnB,MAAE,KAAK;AACP,aAAS,UAAU,KAAK;AAAA,EAC1B,GAAG,OAAOA,KAAI;AACd,SAAO;AACT;;;ACRe,SAAR,iBAAiB,UAAU,OAAOC,OAAM;AAC7C,MAAI,IAAI,IAAI,SAAO,QAAQ;AAC3B,MAAI,SAAS,KAAM,QAAO,EAAE,QAAQ,UAAU,OAAOA,KAAI,GAAG;AAC5D,IAAE,WAAW,EAAE;AACf,IAAE,UAAU,SAASC,WAAUC,QAAOF,OAAM;AAC1C,IAAAE,SAAQ,CAACA,QAAOF,QAAOA,SAAQ,OAAO,IAAI,IAAI,CAACA;AAC/C,MAAE,SAAS,SAAS,KAAK,SAAS;AAChC,iBAAW;AACX,QAAE,SAAS,MAAM,SAASE,QAAOF,KAAI;AACrC,MAAAC,UAAS,OAAO;AAAA,IAClB,GAAGC,QAAOF,KAAI;AAAA,EAChB;AACA,IAAE,QAAQ,UAAU,OAAOA,KAAI;AAC/B,SAAO;AACT;;;ACbA,IAAI,UAAU,iBAAS,SAAS,OAAO,UAAU,WAAW;AAC5D,IAAI,aAAa,CAAC;AAEX,IAAI,UAAU;AACd,IAAI,YAAY;AAChB,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,QAAQ;AAEJ,SAAR,iBAAiB,MAAM,MAAMG,KAAIC,QAAOC,QAAO,QAAQ;AAC5D,MAAI,YAAY,KAAK;AACrB,MAAI,CAAC,UAAW,MAAK,eAAe,CAAC;AAAA,WAC5BF,OAAM,UAAW;AAC1B,SAAO,MAAMA,KAAI;AAAA,IACf;AAAA,IACA,OAAOC;AAAA;AAAA,IACP,OAAOC;AAAA;AAAA,IACP,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,MAAM,OAAO;AAAA,IACb,OAAO,OAAO;AAAA,IACd,UAAU,OAAO;AAAA,IACjB,MAAM,OAAO;AAAA,IACb,OAAO;AAAA,IACP,OAAO;AAAA,EACT,CAAC;AACH;AAEO,SAAS,KAAK,MAAMF,KAAI;AAC7B,MAAI,WAAWG,KAAI,MAAMH,GAAE;AAC3B,MAAI,SAAS,QAAQ,QAAS,OAAM,IAAI,MAAM,6BAA6B;AAC3E,SAAO;AACT;AAEO,SAASI,KAAI,MAAMJ,KAAI;AAC5B,MAAI,WAAWG,KAAI,MAAMH,GAAE;AAC3B,MAAI,SAAS,QAAQ,QAAS,OAAM,IAAI,MAAM,2BAA2B;AACzE,SAAO;AACT;AAEO,SAASG,KAAI,MAAMH,KAAI;AAC5B,MAAI,WAAW,KAAK;AACpB,MAAI,CAAC,YAAY,EAAE,WAAW,SAASA,GAAE,GAAI,OAAM,IAAI,MAAM,sBAAsB;AACnF,SAAO;AACT;AAEA,SAAS,OAAO,MAAMA,KAAI,MAAM;AAC9B,MAAI,YAAY,KAAK,cACjB;AAIJ,YAAUA,GAAE,IAAI;AAChB,OAAK,QAAQ,MAAM,UAAU,GAAG,KAAK,IAAI;AAEzC,WAAS,SAAS,SAAS;AACzB,SAAK,QAAQ;AACb,SAAK,MAAM,QAAQK,QAAO,KAAK,OAAO,KAAK,IAAI;AAG/C,QAAI,KAAK,SAAS,QAAS,CAAAA,OAAM,UAAU,KAAK,KAAK;AAAA,EACvD;AAEA,WAASA,OAAM,SAAS;AACtB,QAAI,GAAG,GAAG,GAAG;AAGb,QAAI,KAAK,UAAU,UAAW,QAAO,KAAK;AAE1C,SAAK,KAAK,WAAW;AACnB,UAAI,UAAU,CAAC;AACf,UAAI,EAAE,SAAS,KAAK,KAAM;AAK1B,UAAI,EAAE,UAAU,QAAS,QAAO,gBAAQA,MAAK;AAG7C,UAAI,EAAE,UAAU,SAAS;AACvB,UAAE,QAAQ;AACV,UAAE,MAAM,KAAK;AACb,UAAE,GAAG,KAAK,aAAa,MAAM,KAAK,UAAU,EAAE,OAAO,EAAE,KAAK;AAC5D,eAAO,UAAU,CAAC;AAAA,MACpB,WAGS,CAAC,IAAIL,KAAI;AAChB,UAAE,QAAQ;AACV,UAAE,MAAM,KAAK;AACb,UAAE,GAAG,KAAK,UAAU,MAAM,KAAK,UAAU,EAAE,OAAO,EAAE,KAAK;AACzD,eAAO,UAAU,CAAC;AAAA,MACpB;AAAA,IACF;AAMA,oBAAQ,WAAW;AACjB,UAAI,KAAK,UAAU,SAAS;AAC1B,aAAK,QAAQ;AACb,aAAK,MAAM,QAAQ,MAAM,KAAK,OAAO,KAAK,IAAI;AAC9C,aAAK,OAAO;AAAA,MACd;AAAA,IACF,CAAC;AAID,SAAK,QAAQ;AACb,SAAK,GAAG,KAAK,SAAS,MAAM,KAAK,UAAU,KAAK,OAAO,KAAK,KAAK;AACjE,QAAI,KAAK,UAAU,SAAU;AAC7B,SAAK,QAAQ;AAGb,YAAQ,IAAI,MAAM,IAAI,KAAK,MAAM,MAAM;AACvC,SAAK,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG;AAC9B,UAAI,IAAI,KAAK,MAAM,CAAC,EAAE,MAAM,KAAK,MAAM,KAAK,UAAU,KAAK,OAAO,KAAK,KAAK,GAAG;AAC7E,cAAM,EAAE,CAAC,IAAI;AAAA,MACf;AAAA,IACF;AACA,UAAM,SAAS,IAAI;AAAA,EACrB;AAEA,WAAS,KAAK,SAAS;AACrB,QAAI,IAAI,UAAU,KAAK,WAAW,KAAK,KAAK,KAAK,MAAM,UAAU,KAAK,QAAQ,KAAK,KAAK,MAAM,QAAQ,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAC9H,IAAI,IACJ,IAAI,MAAM;AAEd,WAAO,EAAE,IAAI,GAAG;AACd,YAAM,CAAC,EAAE,KAAK,MAAM,CAAC;AAAA,IACvB;AAGA,QAAI,KAAK,UAAU,QAAQ;AACzB,WAAK,GAAG,KAAK,OAAO,MAAM,KAAK,UAAU,KAAK,OAAO,KAAK,KAAK;AAC/D,WAAK;AAAA,IACP;AAAA,EACF;AAEA,WAAS,OAAO;AACd,SAAK,QAAQ;AACb,SAAK,MAAM,KAAK;AAChB,WAAO,UAAUA,GAAE;AACnB,aAAS,KAAK,UAAW;AACzB,WAAO,KAAK;AAAA,EACd;AACF;;;ACtJe,SAAR,kBAAiB,MAAM,MAAM;AAClC,MAAI,YAAY,KAAK,cACjB,UACA,QACAM,SAAQ,MACR;AAEJ,MAAI,CAAC,UAAW;AAEhB,SAAO,QAAQ,OAAO,OAAO,OAAO;AAEpC,OAAK,KAAK,WAAW;AACnB,SAAK,WAAW,UAAU,CAAC,GAAG,SAAS,MAAM;AAAE,MAAAA,SAAQ;AAAO;AAAA,IAAU;AACxE,aAAS,SAAS,QAAQ,YAAY,SAAS,QAAQ;AACvD,aAAS,QAAQ;AACjB,aAAS,MAAM,KAAK;AACpB,aAAS,GAAG,KAAK,SAAS,cAAc,UAAU,MAAM,KAAK,UAAU,SAAS,OAAO,SAAS,KAAK;AACrG,WAAO,UAAU,CAAC;AAAA,EACpB;AAEA,MAAIA,OAAO,QAAO,KAAK;AACzB;;;ACrBe,SAARC,mBAAiB,MAAM;AAC5B,SAAO,KAAK,KAAK,WAAW;AAC1B,sBAAU,MAAM,IAAI;AAAA,EACtB,CAAC;AACH;;;ACJA,SAAS,YAAYC,KAAI,MAAM;AAC7B,MAAI,QAAQ;AACZ,SAAO,WAAW;AAChB,QAAI,WAAWC,KAAI,MAAMD,GAAE,GACvB,QAAQ,SAAS;AAKrB,QAAI,UAAU,QAAQ;AACpB,eAAS,SAAS;AAClB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC7C,YAAI,OAAO,CAAC,EAAE,SAAS,MAAM;AAC3B,mBAAS,OAAO,MAAM;AACtB,iBAAO,OAAO,GAAG,CAAC;AAClB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,aAAS,QAAQ;AAAA,EACnB;AACF;AAEA,SAAS,cAAcA,KAAI,MAAM,OAAO;AACtC,MAAI,QAAQ;AACZ,MAAI,OAAO,UAAU,WAAY,OAAM,IAAI;AAC3C,SAAO,WAAW;AAChB,QAAI,WAAWC,KAAI,MAAMD,GAAE,GACvB,QAAQ,SAAS;AAKrB,QAAI,UAAU,QAAQ;AACpB,gBAAU,SAAS,OAAO,MAAM;AAChC,eAAS,IAAI,EAAC,MAAY,MAAY,GAAG,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC7E,YAAI,OAAO,CAAC,EAAE,SAAS,MAAM;AAC3B,iBAAO,CAAC,IAAI;AACZ;AAAA,QACF;AAAA,MACF;AACA,UAAI,MAAM,EAAG,QAAO,KAAK,CAAC;AAAA,IAC5B;AAEA,aAAS,QAAQ;AAAA,EACnB;AACF;AAEe,SAAR,cAAiB,MAAM,OAAO;AACnC,MAAIA,MAAK,KAAK;AAEd,UAAQ;AAER,MAAI,UAAU,SAAS,GAAG;AACxB,QAAI,QAAQE,KAAI,KAAK,KAAK,GAAGF,GAAE,EAAE;AACjC,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/C,WAAK,IAAI,MAAM,CAAC,GAAG,SAAS,MAAM;AAChC,eAAO,EAAE;AAAA,MACX;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,SAAO,KAAK,MAAM,SAAS,OAAO,cAAc,eAAeA,KAAI,MAAM,KAAK,CAAC;AACjF;AAEO,SAAS,WAAWG,aAAY,MAAM,OAAO;AAClD,MAAIH,MAAKG,YAAW;AAEpB,EAAAA,YAAW,KAAK,WAAW;AACzB,QAAI,WAAWF,KAAI,MAAMD,GAAE;AAC3B,KAAC,SAAS,UAAU,SAAS,QAAQ,CAAC,IAAI,IAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AAAA,EAC/E,CAAC;AAED,SAAO,SAAS,MAAM;AACpB,WAAOE,KAAI,MAAMF,GAAE,EAAE,MAAM,IAAI;AAAA,EACjC;AACF;;;AC7Ee,SAARI,qBAAiBC,IAAG,GAAG;AAC5B,MAAIC;AACJ,UAAQ,OAAO,MAAM,WAAW,iBAC1B,aAAa,QAAQ,eACpBA,KAAI,MAAM,CAAC,MAAM,IAAIA,IAAG,eACzB,gBAAmBD,IAAG,CAAC;AAC/B;;;ACJA,SAAS,WAAW,MAAM;AACxB,SAAO,WAAW;AAChB,SAAK,gBAAgB,IAAI;AAAA,EAC3B;AACF;AAEA,SAAS,aAAa,UAAU;AAC9B,SAAO,WAAW;AAChB,SAAK,kBAAkB,SAAS,OAAO,SAAS,KAAK;AAAA,EACvD;AACF;AAEA,SAAS,aAAa,MAAM,aAAa,QAAQ;AAC/C,MAAI,UACA,UAAU,SAAS,IACnB;AACJ,SAAO,WAAW;AAChB,QAAI,UAAU,KAAK,aAAa,IAAI;AACpC,WAAO,YAAY,UAAU,OACvB,YAAY,WAAW,eACvB,eAAe,YAAY,WAAW,SAAS,MAAM;AAAA,EAC7D;AACF;AAEA,SAAS,eAAe,UAAU,aAAa,QAAQ;AACrD,MAAI,UACA,UAAU,SAAS,IACnB;AACJ,SAAO,WAAW;AAChB,QAAI,UAAU,KAAK,eAAe,SAAS,OAAO,SAAS,KAAK;AAChE,WAAO,YAAY,UAAU,OACvB,YAAY,WAAW,eACvB,eAAe,YAAY,WAAW,SAAS,MAAM;AAAA,EAC7D;AACF;AAEA,SAAS,aAAa,MAAM,aAAa,OAAO;AAC9C,MAAI,UACA,UACA;AACJ,SAAO,WAAW;AAChB,QAAI,SAAS,SAAS,MAAM,IAAI,GAAG;AACnC,QAAI,UAAU,KAAM,QAAO,KAAK,KAAK,gBAAgB,IAAI;AACzD,cAAU,KAAK,aAAa,IAAI;AAChC,cAAU,SAAS;AACnB,WAAO,YAAY,UAAU,OACvB,YAAY,YAAY,YAAY,WAAW,gBAC9C,WAAW,SAAS,eAAe,YAAY,WAAW,SAAS,MAAM;AAAA,EAClF;AACF;AAEA,SAAS,eAAe,UAAU,aAAa,OAAO;AACpD,MAAI,UACA,UACA;AACJ,SAAO,WAAW;AAChB,QAAI,SAAS,SAAS,MAAM,IAAI,GAAG;AACnC,QAAI,UAAU,KAAM,QAAO,KAAK,KAAK,kBAAkB,SAAS,OAAO,SAAS,KAAK;AACrF,cAAU,KAAK,eAAe,SAAS,OAAO,SAAS,KAAK;AAC5D,cAAU,SAAS;AACnB,WAAO,YAAY,UAAU,OACvB,YAAY,YAAY,YAAY,WAAW,gBAC9C,WAAW,SAAS,eAAe,YAAY,WAAW,SAAS,MAAM;AAAA,EAClF;AACF;AAEe,SAAR,aAAiB,MAAM,OAAO;AACnC,MAAI,WAAW,kBAAU,IAAI,GAAG,IAAI,aAAa,cAAc,0BAAuBE;AACtF,SAAO,KAAK,UAAU,MAAM,OAAO,UAAU,cACtC,SAAS,QAAQ,iBAAiB,cAAc,UAAU,GAAG,WAAW,MAAM,UAAU,MAAM,KAAK,CAAC,IACrG,SAAS,QAAQ,SAAS,QAAQ,eAAe,YAAY,QAAQ,KACpE,SAAS,QAAQ,iBAAiB,cAAc,UAAU,GAAG,KAAK,CAAC;AAC5E;;;AC3EA,SAAS,gBAAgB,MAAM,GAAG;AAChC,SAAO,SAAS,GAAG;AACjB,SAAK,aAAa,MAAM,EAAE,KAAK,MAAM,CAAC,CAAC;AAAA,EACzC;AACF;AAEA,SAAS,kBAAkB,UAAU,GAAG;AACtC,SAAO,SAAS,GAAG;AACjB,SAAK,eAAe,SAAS,OAAO,SAAS,OAAO,EAAE,KAAK,MAAM,CAAC,CAAC;AAAA,EACrE;AACF;AAEA,SAAS,YAAY,UAAU,OAAO;AACpC,MAAI,IAAI;AACR,WAAS,QAAQ;AACf,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,MAAM,GAAI,OAAM,KAAK,MAAM,kBAAkB,UAAU,CAAC;AAC5D,WAAO;AAAA,EACT;AACA,QAAM,SAAS;AACf,SAAO;AACT;AAEA,SAAS,UAAU,MAAM,OAAO;AAC9B,MAAI,IAAI;AACR,WAAS,QAAQ;AACf,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,MAAM,GAAI,OAAM,KAAK,MAAM,gBAAgB,MAAM,CAAC;AACtD,WAAO;AAAA,EACT;AACA,QAAM,SAAS;AACf,SAAO;AACT;AAEe,SAAR,kBAAiB,MAAM,OAAO;AACnC,MAAI,MAAM,UAAU;AACpB,MAAI,UAAU,SAAS,EAAG,SAAQ,MAAM,KAAK,MAAM,GAAG,MAAM,IAAI;AAChE,MAAI,SAAS,KAAM,QAAO,KAAK,MAAM,KAAK,IAAI;AAC9C,MAAI,OAAO,UAAU,WAAY,OAAM,IAAI;AAC3C,MAAI,WAAW,kBAAU,IAAI;AAC7B,SAAO,KAAK,MAAM,MAAM,SAAS,QAAQ,cAAc,WAAW,UAAU,KAAK,CAAC;AACpF;;;ACzCA,SAAS,cAAcC,KAAI,OAAO;AAChC,SAAO,WAAW;AAChB,SAAK,MAAMA,GAAE,EAAE,QAAQ,CAAC,MAAM,MAAM,MAAM,SAAS;AAAA,EACrD;AACF;AAEA,SAAS,cAAcA,KAAI,OAAO;AAChC,SAAO,QAAQ,CAAC,OAAO,WAAW;AAChC,SAAK,MAAMA,GAAE,EAAE,QAAQ;AAAA,EACzB;AACF;AAEe,SAAR,cAAiB,OAAO;AAC7B,MAAIA,MAAK,KAAK;AAEd,SAAO,UAAU,SACX,KAAK,MAAM,OAAO,UAAU,aACxB,gBACA,eAAeA,KAAI,KAAK,CAAC,IAC7BC,KAAI,KAAK,KAAK,GAAGD,GAAE,EAAE;AAC7B;;;ACpBA,SAAS,iBAAiBE,KAAI,OAAO;AACnC,SAAO,WAAW;AAChB,IAAAC,KAAI,MAAMD,GAAE,EAAE,WAAW,CAAC,MAAM,MAAM,MAAM,SAAS;AAAA,EACvD;AACF;AAEA,SAAS,iBAAiBA,KAAI,OAAO;AACnC,SAAO,QAAQ,CAAC,OAAO,WAAW;AAChC,IAAAC,KAAI,MAAMD,GAAE,EAAE,WAAW;AAAA,EAC3B;AACF;AAEe,SAAR,iBAAiB,OAAO;AAC7B,MAAIA,MAAK,KAAK;AAEd,SAAO,UAAU,SACX,KAAK,MAAM,OAAO,UAAU,aACxB,mBACA,kBAAkBA,KAAI,KAAK,CAAC,IAChCE,KAAI,KAAK,KAAK,GAAGF,GAAE,EAAE;AAC7B;;;ACpBA,SAAS,aAAaG,KAAI,OAAO;AAC/B,MAAI,OAAO,UAAU,WAAY,OAAM,IAAI;AAC3C,SAAO,WAAW;AAChB,IAAAC,KAAI,MAAMD,GAAE,EAAE,OAAO;AAAA,EACvB;AACF;AAEe,SAAR,aAAiB,OAAO;AAC7B,MAAIA,MAAK,KAAK;AAEd,SAAO,UAAU,SACX,KAAK,KAAK,aAAaA,KAAI,KAAK,CAAC,IACjCE,KAAI,KAAK,KAAK,GAAGF,GAAE,EAAE;AAC7B;;;ACbA,SAAS,YAAYG,KAAI,OAAO;AAC9B,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,OAAO,MAAM,WAAY,OAAM,IAAI;AACvC,IAAAC,KAAI,MAAMD,GAAE,EAAE,OAAO;AAAA,EACvB;AACF;AAEe,SAAR,oBAAiB,OAAO;AAC7B,MAAI,OAAO,UAAU,WAAY,OAAM,IAAI;AAC3C,SAAO,KAAK,KAAK,YAAY,KAAK,KAAK,KAAK,CAAC;AAC/C;;;ACVe,SAAR,eAAiB,OAAO;AAC7B,MAAI,OAAO,UAAU,WAAY,SAAQ,gBAAQ,KAAK;AAEtD,WAASE,UAAS,KAAK,SAASC,KAAID,QAAO,QAAQ,YAAY,IAAI,MAAMC,EAAC,GAAG,IAAI,GAAG,IAAIA,IAAG,EAAE,GAAG;AAC9F,aAASC,SAAQF,QAAO,CAAC,GAAG,IAAIE,OAAM,QAAQ,WAAW,UAAU,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACnG,WAAK,OAAOA,OAAM,CAAC,MAAM,MAAM,KAAK,MAAM,KAAK,UAAU,GAAGA,MAAK,GAAG;AAClE,iBAAS,KAAK,IAAI;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,WAAW,WAAW,KAAK,UAAU,KAAK,OAAO,KAAK,GAAG;AACtE;;;ACbe,SAAR,cAAiBC,aAAY;AAClC,MAAIA,YAAW,QAAQ,KAAK,IAAK,OAAM,IAAI;AAE3C,WAAS,UAAU,KAAK,SAAS,UAAUA,YAAW,SAAS,KAAK,QAAQ,QAAQ,KAAK,QAAQ,QAAQC,KAAI,KAAK,IAAI,IAAI,EAAE,GAAG,SAAS,IAAI,MAAM,EAAE,GAAG,IAAI,GAAG,IAAIA,IAAG,EAAE,GAAG;AACxK,aAAS,SAAS,QAAQ,CAAC,GAAG,SAAS,QAAQ,CAAC,GAAG,IAAI,OAAO,QAAQC,SAAQ,OAAO,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/H,UAAI,OAAO,OAAO,CAAC,KAAK,OAAO,CAAC,GAAG;AACjC,QAAAA,OAAM,CAAC,IAAI;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,IAAI,EAAE,GAAG;AAClB,WAAO,CAAC,IAAI,QAAQ,CAAC;AAAA,EACvB;AAEA,SAAO,IAAI,WAAW,QAAQ,KAAK,UAAU,KAAK,OAAO,KAAK,GAAG;AACnE;;;AChBA,SAAS,MAAM,MAAM;AACnB,UAAQ,OAAO,IAAI,KAAK,EAAE,MAAM,OAAO,EAAE,MAAM,SAAS,GAAG;AACzD,QAAI,IAAI,EAAE,QAAQ,GAAG;AACrB,QAAI,KAAK,EAAG,KAAI,EAAE,MAAM,GAAG,CAAC;AAC5B,WAAO,CAAC,KAAK,MAAM;AAAA,EACrB,CAAC;AACH;AAEA,SAAS,WAAWC,KAAI,MAAM,UAAU;AACtC,MAAI,KAAK,KAAK,MAAM,MAAM,IAAI,IAAI,OAAOC;AACzC,SAAO,WAAW;AAChB,QAAI,WAAW,IAAI,MAAMD,GAAE,GACvB,KAAK,SAAS;AAKlB,QAAI,OAAO,IAAK,EAAC,OAAO,MAAM,IAAI,KAAK,GAAG,GAAG,MAAM,QAAQ;AAE3D,aAAS,KAAK;AAAA,EAChB;AACF;AAEe,SAAR,WAAiB,MAAM,UAAU;AACtC,MAAIA,MAAK,KAAK;AAEd,SAAO,UAAU,SAAS,IACpBE,KAAI,KAAK,KAAK,GAAGF,GAAE,EAAE,GAAG,GAAG,IAAI,IAC/B,KAAK,KAAK,WAAWA,KAAI,MAAM,QAAQ,CAAC;AAChD;;;AC/BA,SAAS,eAAeG,KAAI;AAC1B,SAAO,WAAW;AAChB,QAAI,SAAS,KAAK;AAClB,aAAS,KAAK,KAAK,aAAc,KAAI,CAAC,MAAMA,IAAI;AAChD,QAAI,OAAQ,QAAO,YAAY,IAAI;AAAA,EACrC;AACF;AAEe,SAAR,iBAAmB;AACxB,SAAO,KAAK,GAAG,cAAc,eAAe,KAAK,GAAG,CAAC;AACvD;;;ACNe,SAARC,gBAAiB,QAAQ;AAC9B,MAAI,OAAO,KAAK,OACZC,MAAK,KAAK;AAEd,MAAI,OAAO,WAAW,WAAY,UAAS,iBAAS,MAAM;AAE1D,WAASC,UAAS,KAAK,SAASC,KAAID,QAAO,QAAQ,YAAY,IAAI,MAAMC,EAAC,GAAG,IAAI,GAAG,IAAIA,IAAG,EAAE,GAAG;AAC9F,aAASC,SAAQF,QAAO,CAAC,GAAG,IAAIE,OAAM,QAAQ,WAAW,UAAU,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,MAAM,SAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtH,WAAK,OAAOA,OAAM,CAAC,OAAO,UAAU,OAAO,KAAK,MAAM,KAAK,UAAU,GAAGA,MAAK,IAAI;AAC/E,YAAI,cAAc,KAAM,SAAQ,WAAW,KAAK;AAChD,iBAAS,CAAC,IAAI;AACd,yBAAS,SAAS,CAAC,GAAG,MAAMH,KAAI,GAAG,UAAUI,KAAI,MAAMJ,GAAE,CAAC;AAAA,MAC5D;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,WAAW,WAAW,KAAK,UAAU,MAAMA,GAAE;AAC1D;;;ACjBe,SAARK,mBAAiB,QAAQ;AAC9B,MAAI,OAAO,KAAK,OACZC,MAAK,KAAK;AAEd,MAAI,OAAO,WAAW,WAAY,UAAS,oBAAY,MAAM;AAE7D,WAASC,UAAS,KAAK,SAASC,KAAID,QAAO,QAAQ,YAAY,CAAC,GAAG,UAAU,CAAC,GAAG,IAAI,GAAG,IAAIC,IAAG,EAAE,GAAG;AAClG,aAASC,SAAQF,QAAO,CAAC,GAAG,IAAIE,OAAM,QAAQ,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAOA,OAAM,CAAC,GAAG;AACnB,iBAAS,WAAW,OAAO,KAAK,MAAM,KAAK,UAAU,GAAGA,MAAK,GAAG,OAAOC,WAAUC,KAAI,MAAML,GAAE,GAAG,IAAI,GAAG,IAAI,SAAS,QAAQ,IAAI,GAAG,EAAE,GAAG;AACtI,cAAI,QAAQ,SAAS,CAAC,GAAG;AACvB,6BAAS,OAAO,MAAMA,KAAI,GAAG,UAAUI,QAAO;AAAA,UAChD;AAAA,QACF;AACA,kBAAU,KAAK,QAAQ;AACvB,gBAAQ,KAAK,IAAI;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,WAAW,WAAW,SAAS,MAAMJ,GAAE;AACpD;;;ACvBA,IAAI,YAAY,kBAAU,UAAU;AAErB,SAARM,qBAAmB;AACxB,SAAO,IAAI,UAAU,KAAK,SAAS,KAAK,QAAQ;AAClD;;;ACAA,SAAS,UAAU,MAAM,aAAa;AACpC,MAAI,UACA,UACA;AACJ,SAAO,WAAW;AAChB,QAAI,UAAU,WAAM,MAAM,IAAI,GAC1B,WAAW,KAAK,MAAM,eAAe,IAAI,GAAG,WAAM,MAAM,IAAI;AAChE,WAAO,YAAY,UAAU,OACvB,YAAY,YAAY,YAAY,WAAW,eAC/C,eAAe,YAAY,WAAW,SAAS,WAAW,OAAO;AAAA,EACzE;AACF;AAEA,SAAS,YAAY,MAAM;AACzB,SAAO,WAAW;AAChB,SAAK,MAAM,eAAe,IAAI;AAAA,EAChC;AACF;AAEA,SAAS,cAAc,MAAM,aAAa,QAAQ;AAChD,MAAI,UACA,UAAU,SAAS,IACnB;AACJ,SAAO,WAAW;AAChB,QAAI,UAAU,WAAM,MAAM,IAAI;AAC9B,WAAO,YAAY,UAAU,OACvB,YAAY,WAAW,eACvB,eAAe,YAAY,WAAW,SAAS,MAAM;AAAA,EAC7D;AACF;AAEA,SAAS,cAAc,MAAM,aAAa,OAAO;AAC/C,MAAI,UACA,UACA;AACJ,SAAO,WAAW;AAChB,QAAI,UAAU,WAAM,MAAM,IAAI,GAC1B,SAAS,MAAM,IAAI,GACnB,UAAU,SAAS;AACvB,QAAI,UAAU,KAAM,WAAU,UAAU,KAAK,MAAM,eAAe,IAAI,GAAG,WAAM,MAAM,IAAI;AACzF,WAAO,YAAY,UAAU,OACvB,YAAY,YAAY,YAAY,WAAW,gBAC9C,WAAW,SAAS,eAAe,YAAY,WAAW,SAAS,MAAM;AAAA,EAClF;AACF;AAEA,SAAS,iBAAiBC,KAAI,MAAM;AAClC,MAAI,KAAK,KAAK,WAAW,MAAM,WAAW,MAAM,QAAQ,SAAS,KAAK;AACtE,SAAO,WAAW;AAChB,QAAI,WAAWC,KAAI,MAAMD,GAAE,GACvB,KAAK,SAAS,IACd,WAAW,SAAS,MAAM,GAAG,KAAK,OAAO,WAAW,SAAS,YAAY,IAAI,KAAK;AAKtF,QAAI,OAAO,OAAO,cAAc,SAAU,EAAC,OAAO,MAAM,IAAI,KAAK,GAAG,GAAG,OAAO,YAAY,QAAQ;AAElG,aAAS,KAAK;AAAA,EAChB;AACF;AAEe,SAAR,cAAiB,MAAM,OAAO,UAAU;AAC7C,MAAI,KAAK,QAAQ,QAAQ,cAAc,0BAAuBE;AAC9D,SAAO,SAAS,OAAO,KAClB,WAAW,MAAM,UAAU,MAAM,CAAC,CAAC,EACnC,GAAG,eAAe,MAAM,YAAY,IAAI,CAAC,IAC1C,OAAO,UAAU,aAAa,KAC7B,WAAW,MAAM,cAAc,MAAM,GAAG,WAAW,MAAM,WAAW,MAAM,KAAK,CAAC,CAAC,EACjF,KAAK,iBAAiB,KAAK,KAAK,IAAI,CAAC,IACtC,KACC,WAAW,MAAM,cAAc,MAAM,GAAG,KAAK,GAAG,QAAQ,EACxD,GAAG,eAAe,MAAM,IAAI;AACnC;;;AC/EA,SAAS,iBAAiB,MAAM,GAAG,UAAU;AAC3C,SAAO,SAAS,GAAG;AACjB,SAAK,MAAM,YAAY,MAAM,EAAE,KAAK,MAAM,CAAC,GAAG,QAAQ;AAAA,EACxD;AACF;AAEA,SAAS,WAAW,MAAM,OAAO,UAAU;AACzC,MAAI,GAAG;AACP,WAAS,QAAQ;AACf,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,MAAM,GAAI,MAAK,KAAK,MAAM,iBAAiB,MAAM,GAAG,QAAQ;AAChE,WAAO;AAAA,EACT;AACA,QAAM,SAAS;AACf,SAAO;AACT;AAEe,SAAR,mBAAiB,MAAM,OAAO,UAAU;AAC7C,MAAI,MAAM,YAAY,QAAQ;AAC9B,MAAI,UAAU,SAAS,EAAG,SAAQ,MAAM,KAAK,MAAM,GAAG,MAAM,IAAI;AAChE,MAAI,SAAS,KAAM,QAAO,KAAK,MAAM,KAAK,IAAI;AAC9C,MAAI,OAAO,UAAU,WAAY,OAAM,IAAI;AAC3C,SAAO,KAAK,MAAM,KAAK,WAAW,MAAM,OAAO,YAAY,OAAO,KAAK,QAAQ,CAAC;AAClF;;;ACrBA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,SAAK,cAAc;AAAA,EACrB;AACF;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,QAAI,SAAS,MAAM,IAAI;AACvB,SAAK,cAAc,UAAU,OAAO,KAAK;AAAA,EAC3C;AACF;AAEe,SAAR,aAAiB,OAAO;AAC7B,SAAO,KAAK,MAAM,QAAQ,OAAO,UAAU,aACrC,aAAa,WAAW,MAAM,QAAQ,KAAK,CAAC,IAC5C,aAAa,SAAS,OAAO,KAAK,QAAQ,EAAE,CAAC;AACrD;;;ACnBA,SAAS,gBAAgB,GAAG;AAC1B,SAAO,SAAS,GAAG;AACjB,SAAK,cAAc,EAAE,KAAK,MAAM,CAAC;AAAA,EACnC;AACF;AAEA,SAAS,UAAU,OAAO;AACxB,MAAI,IAAI;AACR,WAAS,QAAQ;AACf,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,MAAM,GAAI,OAAM,KAAK,MAAM,gBAAgB,CAAC;AAChD,WAAO;AAAA,EACT;AACA,QAAM,SAAS;AACf,SAAO;AACT;AAEe,SAAR,kBAAiB,OAAO;AAC7B,MAAI,MAAM;AACV,MAAI,UAAU,SAAS,EAAG,SAAQ,MAAM,KAAK,MAAM,GAAG,MAAM,IAAI;AAChE,MAAI,SAAS,KAAM,QAAO,KAAK,MAAM,KAAK,IAAI;AAC9C,MAAI,OAAO,UAAU,WAAY,OAAM,IAAI;AAC3C,SAAO,KAAK,MAAM,KAAK,UAAU,KAAK,CAAC;AACzC;;;ACpBe,SAAR,qBAAmB;AACxB,MAAI,OAAO,KAAK,OACZ,MAAM,KAAK,KACX,MAAM,MAAM;AAEhB,WAASC,UAAS,KAAK,SAASC,KAAID,QAAO,QAAQ,IAAI,GAAG,IAAIC,IAAG,EAAE,GAAG;AACpE,aAASC,SAAQF,QAAO,CAAC,GAAG,IAAIE,OAAM,QAAQ,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAOA,OAAM,CAAC,GAAG;AACnB,YAAIC,WAAUC,KAAI,MAAM,GAAG;AAC3B,yBAAS,MAAM,MAAM,KAAK,GAAGF,QAAO;AAAA,UAClC,MAAMC,SAAQ,OAAOA,SAAQ,QAAQA,SAAQ;AAAA,UAC7C,OAAO;AAAA,UACP,UAAUA,SAAQ;AAAA,UAClB,MAAMA,SAAQ;AAAA,QAChB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,WAAWH,SAAQ,KAAK,UAAU,MAAM,GAAG;AACxD;;;ACrBe,SAAR,cAAmB;AACxB,MAAI,KAAK,KAAK,OAAO,MAAMK,MAAK,KAAK,KAAK,OAAO,KAAK,KAAK;AAC3D,SAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAC3C,QAAI,SAAS,EAAC,OAAO,OAAM,GACvB,MAAM,EAAC,OAAO,WAAW;AAAE,UAAI,EAAE,SAAS,EAAG,SAAQ;AAAA,IAAG,EAAC;AAE7D,SAAK,KAAK,WAAW;AACnB,UAAI,WAAWC,KAAI,MAAMD,GAAE,GACvB,KAAK,SAAS;AAKlB,UAAI,OAAO,KAAK;AACd,eAAO,MAAM,IAAI,KAAK;AACtB,YAAI,EAAE,OAAO,KAAK,MAAM;AACxB,YAAI,EAAE,UAAU,KAAK,MAAM;AAC3B,YAAI,EAAE,IAAI,KAAK,GAAG;AAAA,MACpB;AAEA,eAAS,KAAK;AAAA,IAChB,CAAC;AAGD,QAAI,SAAS,EAAG,SAAQ;AAAA,EAC1B,CAAC;AACH;;;ACNA,IAAI,KAAK;AAEF,SAAS,WAAWE,SAAQ,SAAS,MAAMC,KAAI;AACpD,OAAK,UAAUD;AACf,OAAK,WAAW;AAChB,OAAK,QAAQ;AACb,OAAK,MAAMC;AACb;AAEe,SAAR,WAA4B,MAAM;AACvC,SAAO,kBAAU,EAAE,WAAW,IAAI;AACpC;AAEO,SAAS,QAAQ;AACtB,SAAO,EAAE;AACX;AAEA,IAAI,sBAAsB,kBAAU;AAEpC,WAAW,YAAY,WAAW,YAAY;AAAA,EAC5C,aAAa;AAAA,EACb,QAAQC;AAAA,EACR,WAAWC;AAAA,EACX,aAAa,oBAAoB;AAAA,EACjC,gBAAgB,oBAAoB;AAAA,EACpC,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,WAAWC;AAAA,EACX,YAAY;AAAA,EACZ,MAAM,oBAAoB;AAAA,EAC1B,OAAO,oBAAoB;AAAA,EAC3B,MAAM,oBAAoB;AAAA,EAC1B,MAAM,oBAAoB;AAAA,EAC1B,OAAO,oBAAoB;AAAA,EAC3B,MAAM,oBAAoB;AAAA,EAC1B,IAAI;AAAA,EACJ,MAAM;AAAA,EACN,WAAW;AAAA,EACX,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,UAAU;AAAA,EACV,MAAM;AAAA,EACN,aAAa;AAAA,EACb,KAAK;AAAA,EACL,CAAC,OAAO,QAAQ,GAAG,oBAAoB,OAAO,QAAQ;AACxD;;;ACxEO,IAAMC,UAAS,OAAK,CAAC;;;ACArB,SAAS,OAAO,GAAG;AACxB,SAAO,IAAI;AACb;AAEO,SAAS,QAAQ,GAAG;AACzB,SAAO,KAAK,IAAI;AAClB;AAEO,SAAS,UAAU,GAAG;AAC3B,WAAS,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE,KAAK,IAAI,KAAK,KAAK;AACvD;;;ACVO,SAAS,QAAQ,GAAG;AACzB,SAAO,IAAI,IAAI;AACjB;AAEO,SAAS,SAAS,GAAG;AAC1B,SAAO,EAAE,IAAI,IAAI,IAAI;AACvB;AAEO,SAAS,WAAW,GAAG;AAC5B,WAAS,KAAK,MAAM,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK;AAC9D;;;ACVA,IAAI,WAAW;AAER,IAAI,SAAU,SAAS,OAAO,GAAG;AACtC,MAAI,CAAC;AAEL,WAASC,QAAO,GAAG;AACjB,WAAO,KAAK,IAAI,GAAG,CAAC;AAAA,EACtB;AAEA,EAAAA,QAAO,WAAW;AAElB,SAAOA;AACT,EAAG,QAAQ;AAEJ,IAAI,UAAW,SAASC,QAAO,GAAG;AACvC,MAAI,CAAC;AAEL,WAASC,SAAQ,GAAG;AAClB,WAAO,IAAI,KAAK,IAAI,IAAI,GAAG,CAAC;AAAA,EAC9B;AAEA,EAAAA,SAAQ,WAAWD;AAEnB,SAAOC;AACT,EAAG,QAAQ;AAEJ,IAAI,YAAa,SAASD,QAAO,GAAG;AACzC,MAAI,CAAC;AAEL,WAASE,WAAU,GAAG;AACpB,aAAS,KAAK,MAAM,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI,IAAI,KAAK,IAAI,IAAI,GAAG,CAAC,KAAK;AAAA,EACrE;AAEA,EAAAA,WAAU,WAAWF;AAErB,SAAOE;AACT,EAAG,QAAQ;;;ACpCX,IAAI,KAAK,KAAK;AAAd,IACI,SAAS,KAAK;AAEX,SAAS,MAAM,GAAG;AACvB,SAAQ,CAAC,MAAM,IAAK,IAAI,IAAI,KAAK,IAAI,IAAI,MAAM;AACjD;AAEO,SAAS,OAAO,GAAG;AACxB,SAAO,KAAK,IAAI,IAAI,MAAM;AAC5B;AAEO,SAAS,SAAS,GAAG;AAC1B,UAAQ,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK;AAClC;;;ACZO,SAAS,KAAKC,IAAG;AACtB,UAAQ,KAAK,IAAI,GAAG,MAAMA,EAAC,IAAI,eAAgB;AACjD;;;ACDO,SAAS,MAAM,GAAG;AACvB,SAAO,KAAK,IAAI,CAAC,CAAC;AACpB;AAEO,SAAS,OAAO,GAAG;AACxB,SAAO,IAAI,KAAK,CAAC;AACnB;AAEO,SAAS,SAAS,GAAG;AAC1B,WAAS,KAAK,MAAM,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI,CAAC,KAAK;AAC3D;;;ACZO,SAAS,SAAS,GAAG;AAC1B,SAAO,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC;AAChC;AAEO,SAAS,UAAU,GAAG;AAC3B,SAAO,KAAK,KAAK,IAAI,EAAE,IAAI,CAAC;AAC9B;AAEO,SAAS,YAAY,GAAG;AAC7B,WAAS,KAAK,MAAM,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,CAAC,IAAI,KAAK;AACxF;;;ACVA,IAAI,KAAK,IAAI;AAAb,IACI,KAAK,IAAI;AADb,IAEI,KAAK,IAAI;AAFb,IAGI,KAAK,IAAI;AAHb,IAII,KAAK,IAAI;AAJb,IAKI,KAAK,KAAK;AALd,IAMI,KAAK,KAAK;AANd,IAOI,KAAK,KAAK;AAPd,IAQI,KAAK,KAAK;AARd,IASI,KAAK,IAAI,KAAK;AAEX,SAAS,SAAS,GAAG;AAC1B,SAAO,IAAI,UAAU,IAAI,CAAC;AAC5B;AAEO,SAAS,UAAU,GAAG;AAC3B,UAAQ,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,KAAK,MAAM,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK,MAAM,KAAK,MAAM,IAAI,KAAK,MAAM,KAAK,MAAM,IAAI;AACjI;AAEO,SAAS,YAAY,GAAG;AAC7B,WAAS,KAAK,MAAM,IAAI,IAAI,UAAU,IAAI,CAAC,IAAI,UAAU,IAAI,CAAC,IAAI,KAAK;AACzE;;;ACrBA,IAAI,YAAY;AAET,IAAI,SAAU,SAASC,QAAO,GAAG;AACtC,MAAI,CAAC;AAEL,WAASC,QAAO,GAAG;AACjB,YAAQ,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI,KAAK;AAAA,EACvC;AAEA,EAAAA,QAAO,YAAYD;AAEnB,SAAOC;AACT,EAAG,SAAS;AAEL,IAAI,UAAW,SAASD,QAAO,GAAG;AACvC,MAAI,CAAC;AAEL,WAASE,SAAQ,GAAG;AAClB,WAAO,EAAE,IAAI,MAAM,IAAI,KAAK,IAAI,KAAK;AAAA,EACvC;AAEA,EAAAA,SAAQ,YAAYF;AAEpB,SAAOE;AACT,EAAG,SAAS;AAEL,IAAI,YAAa,SAASF,QAAO,GAAG;AACzC,MAAI,CAAC;AAEL,WAASG,WAAU,GAAG;AACpB,aAAS,KAAK,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK;AAAA,EAC7F;AAEA,EAAAA,WAAU,YAAYH;AAEtB,SAAOG;AACT,EAAG,SAAS;;;AClCZ,IAAI,MAAM,IAAI,KAAK;AAAnB,IACI,YAAY;AADhB,IAEI,SAAS;AAEN,IAAI,YAAa,SAASC,QAAOC,IAAG,GAAG;AAC5C,MAAI,IAAI,KAAK,KAAK,KAAKA,KAAI,KAAK,IAAI,GAAGA,EAAC,EAAE,KAAK,KAAK;AAEpD,WAASC,WAAU,GAAG;AACpB,WAAOD,KAAI,KAAK,EAAE,EAAE,CAAE,IAAI,KAAK,KAAK,IAAI,KAAK,CAAC;AAAA,EAChD;AAEA,EAAAC,WAAU,YAAY,SAASD,IAAG;AAAE,WAAOD,QAAOC,IAAG,IAAI,GAAG;AAAA,EAAG;AAC/D,EAAAC,WAAU,SAAS,SAASC,IAAG;AAAE,WAAOH,QAAOC,IAAGE,EAAC;AAAA,EAAG;AAEtD,SAAOD;AACT,EAAG,WAAW,MAAM;AAEb,IAAI,aAAc,SAASF,QAAOC,IAAG,GAAG;AAC7C,MAAI,IAAI,KAAK,KAAK,KAAKA,KAAI,KAAK,IAAI,GAAGA,EAAC,EAAE,KAAK,KAAK;AAEpD,WAASG,YAAW,GAAG;AACrB,WAAO,IAAIH,KAAI,KAAK,IAAI,CAAC,CAAC,IAAI,KAAK,KAAK,IAAI,KAAK,CAAC;AAAA,EACpD;AAEA,EAAAG,YAAW,YAAY,SAASH,IAAG;AAAE,WAAOD,QAAOC,IAAG,IAAI,GAAG;AAAA,EAAG;AAChE,EAAAG,YAAW,SAAS,SAASD,IAAG;AAAE,WAAOH,QAAOC,IAAGE,EAAC;AAAA,EAAG;AAEvD,SAAOC;AACT,EAAG,WAAW,MAAM;AAEb,IAAI,eAAgB,SAASJ,QAAOC,IAAG,GAAG;AAC/C,MAAI,IAAI,KAAK,KAAK,KAAKA,KAAI,KAAK,IAAI,GAAGA,EAAC,EAAE,KAAK,KAAK;AAEpD,WAASI,cAAa,GAAG;AACvB,aAAS,IAAI,IAAI,IAAI,KAAK,IACpBJ,KAAI,KAAK,CAAC,CAAC,IAAI,KAAK,KAAK,IAAI,KAAK,CAAC,IACnC,IAAIA,KAAI,KAAK,CAAC,IAAI,KAAK,KAAK,IAAI,KAAK,CAAC,KAAK;AAAA,EACnD;AAEA,EAAAI,cAAa,YAAY,SAASJ,IAAG;AAAE,WAAOD,QAAOC,IAAG,IAAI,GAAG;AAAA,EAAG;AAClE,EAAAI,cAAa,SAAS,SAASF,IAAG;AAAE,WAAOH,QAAOC,IAAGE,EAAC;AAAA,EAAG;AAEzD,SAAOE;AACT,EAAG,WAAW,MAAM;;;ACxCpB,IAAI,gBAAgB;AAAA,EAClB,MAAM;AAAA;AAAA,EACN,OAAO;AAAA,EACP,UAAU;AAAA,EACV,MAAM;AACR;AAEA,SAAS,QAAQ,MAAMC,KAAI;AACzB,MAAI;AACJ,SAAO,EAAE,SAAS,KAAK,iBAAiB,EAAE,SAAS,OAAOA,GAAE,IAAI;AAC9D,QAAI,EAAE,OAAO,KAAK,aAAa;AAC7B,YAAM,IAAI,MAAM,cAAcA,GAAE,YAAY;AAAA,IAC9C;AAAA,EACF;AACA,SAAO;AACT;AAEe,SAARC,oBAAiB,MAAM;AAC5B,MAAID,KACA;AAEJ,MAAI,gBAAgB,YAAY;AAC9B,IAAAA,MAAK,KAAK,KAAK,OAAO,KAAK;AAAA,EAC7B,OAAO;AACL,IAAAA,MAAK,MAAM,IAAI,SAAS,eAAe,OAAO,IAAI,GAAG,OAAO,QAAQ,OAAO,OAAO,OAAO;AAAA,EAC3F;AAEA,WAASE,UAAS,KAAK,SAASC,KAAID,QAAO,QAAQ,IAAI,GAAG,IAAIC,IAAG,EAAE,GAAG;AACpE,aAASC,SAAQF,QAAO,CAAC,GAAG,IAAIE,OAAM,QAAQ,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAOA,OAAM,CAAC,GAAG;AACnB,yBAAS,MAAM,MAAMJ,KAAI,GAAGI,QAAO,UAAU,QAAQ,MAAMJ,GAAE,CAAC;AAAA,MAChE;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,WAAWE,SAAQ,KAAK,UAAU,MAAMF,GAAE;AACvD;;;ACrCA,kBAAU,UAAU,YAAYK;AAChC,kBAAU,UAAU,aAAaC;;;ACFjC,IAAI,OAAO,CAAC,IAAI;AAED,SAAR,eAAiB,MAAM,MAAM;AAClC,MAAI,YAAY,KAAK,cACjB,UACA;AAEJ,MAAI,WAAW;AACb,WAAO,QAAQ,OAAO,OAAO,OAAO;AACpC,SAAK,KAAK,WAAW;AACnB,WAAK,WAAW,UAAU,CAAC,GAAG,QAAQ,aAAa,SAAS,SAAS,MAAM;AACzE,eAAO,IAAI,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,MAAM,MAAM,CAAC,CAAC;AAAA,MAChD;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;ACpBA,IAAOC,oBAAQ,CAAAC,OAAK,MAAMA;;;ACAX,SAAR,WAA4BC,OAAM;AAAA,EACvC;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAAC;AAAA,EACA,UAAAC;AACF,GAAG;AACD,SAAO,iBAAiB,MAAM;AAAA,IAC5B,MAAM,EAAC,OAAOF,OAAM,YAAY,MAAM,cAAc,KAAI;AAAA,IACxD,aAAa,EAAC,OAAO,aAAa,YAAY,MAAM,cAAc,KAAI;AAAA,IACtE,QAAQ,EAAC,OAAO,QAAQ,YAAY,MAAM,cAAc,KAAI;AAAA,IAC5D,WAAW,EAAC,OAAO,WAAW,YAAY,MAAM,cAAc,KAAI;AAAA,IAClE,MAAM,EAAC,OAAOC,OAAM,YAAY,MAAM,cAAc,KAAI;AAAA,IACxD,GAAG,EAAC,OAAOC,UAAQ;AAAA,EACrB,CAAC;AACH;;;ACfO,SAASC,eAAc,OAAO;AACnC,QAAM,yBAAyB;AACjC;AAEe,SAARC,iBAAiB,OAAO;AAC7B,QAAM,eAAe;AACrB,QAAM,yBAAyB;AACjC;;;ACEA,IAAI,YAAY,EAAC,MAAM,OAAM;AAA7B,IACI,aAAa,EAAC,MAAM,QAAO;AAD/B,IAEI,cAAc,EAAC,MAAM,SAAQ;AAFjC,IAGI,cAAc,EAAC,MAAM,SAAQ;AAEjC,IAAM,EAAC,KAAK,KAAAC,MAAK,KAAAC,KAAG,IAAI;AAExB,SAAS,QAAQ,GAAG;AAClB,SAAO,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AACtB;AAEA,SAAS,QAAQ,GAAG;AAClB,SAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,GAAG,QAAQ,EAAE,CAAC,CAAC,CAAC;AACtC;AAEA,IAAI,IAAI;AAAA,EACN,MAAM;AAAA,EACN,SAAS,CAAC,KAAK,GAAG,EAAE,IAAI,IAAI;AAAA,EAC5B,OAAO,SAASC,IAAG,GAAG;AAAE,WAAOA,MAAK,OAAO,OAAO,CAAC,CAAC,CAACA,GAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAACA,GAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA,EAAG;AAAA,EACxF,QAAQ,SAAS,IAAI;AAAE,WAAO,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AAAA,EAAG;AAC5D;AAEA,IAAI,IAAI;AAAA,EACN,MAAM;AAAA,EACN,SAAS,CAAC,KAAK,GAAG,EAAE,IAAI,IAAI;AAAA,EAC5B,OAAO,SAASC,IAAG,GAAG;AAAE,WAAOA,MAAK,OAAO,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAACA,GAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAACA,GAAE,CAAC,CAAC,CAAC;AAAA,EAAG;AAAA,EACxF,QAAQ,SAAS,IAAI;AAAE,WAAO,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AAAA,EAAG;AAC5D;AAEA,IAAI,KAAK;AAAA,EACP,MAAM;AAAA,EACN,SAAS,CAAC,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,MAAM,IAAI,EAAE,IAAI,IAAI;AAAA,EAC9D,OAAO,SAAS,IAAI;AAAE,WAAO,MAAM,OAAO,OAAO,QAAQ,EAAE;AAAA,EAAG;AAAA,EAC9D,QAAQ,SAAS,IAAI;AAAE,WAAO;AAAA,EAAI;AACpC;AAEA,IAAI,UAAU;AAAA,EACZ,SAAS;AAAA,EACT,WAAW;AAAA,EACX,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AACN;AAEA,IAAI,QAAQ;AAAA,EACV,GAAG;AAAA,EACH,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AACN;AAEA,IAAI,QAAQ;AAAA,EACV,GAAG;AAAA,EACH,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AACN;AAEA,IAAI,SAAS;AAAA,EACX,SAAS;AAAA,EACT,WAAW;AAAA,EACX,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AACN;AAEA,IAAI,SAAS;AAAA,EACX,SAAS;AAAA,EACT,WAAW;AAAA,EACX,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AACN;AAEA,SAAS,KAAK,GAAG;AACf,SAAO,EAAC,MAAM,EAAC;AACjB;AAGA,SAASC,eAAc,OAAO;AAC5B,SAAO,CAAC,MAAM,WAAW,CAAC,MAAM;AAClC;AAEA,SAAS,gBAAgB;AACvB,MAAIC,OAAM,KAAK,mBAAmB;AAClC,MAAIA,KAAI,aAAa,SAAS,GAAG;AAC/B,IAAAA,OAAMA,KAAI,QAAQ;AAClB,WAAO,CAAC,CAACA,KAAI,GAAGA,KAAI,CAAC,GAAG,CAACA,KAAI,IAAIA,KAAI,OAAOA,KAAI,IAAIA,KAAI,MAAM,CAAC;AAAA,EACjE;AACA,SAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAACA,KAAI,MAAM,QAAQ,OAAOA,KAAI,OAAO,QAAQ,KAAK,CAAC;AACrE;AAEA,SAASC,oBAAmB;AAC1B,SAAO,UAAU,kBAAmB,kBAAkB;AACxD;AAGA,SAASC,OAAM,MAAM;AACnB,SAAO,CAAC,KAAK,QAAS,KAAI,EAAE,OAAO,KAAK,YAAa;AACrD,SAAO,KAAK;AACd;AAEA,SAAS,MAAMC,SAAQ;AACrB,SAAOA,QAAO,CAAC,EAAE,CAAC,MAAMA,QAAO,CAAC,EAAE,CAAC,KAC5BA,QAAO,CAAC,EAAE,CAAC,MAAMA,QAAO,CAAC,EAAE,CAAC;AACrC;AAEO,SAAS,eAAe,MAAM;AACnC,MAAI,QAAQ,KAAK;AACjB,SAAO,QAAQ,MAAM,IAAI,OAAO,MAAM,SAAS,IAAI;AACrD;AAEO,SAAS,SAAS;AACvB,SAAO,MAAM,CAAC;AAChB;AAEO,SAAS,SAAS;AACvB,SAAO,MAAM,CAAC;AAChB;AAEe,SAAR,gBAAmB;AACxB,SAAO,MAAM,EAAE;AACjB;AAEA,SAAS,MAAM,KAAK;AAClB,MAAIA,UAAS,eACTC,UAASL,gBACT,YAAYE,mBACZ,OAAO,MACP,YAAY,iBAAS,SAAS,SAAS,KAAK,GAC5C,aAAa,GACb;AAEJ,WAASI,OAAMC,QAAO;AACpB,QAAI,UAAUA,OACT,SAAS,WAAW,UAAU,EAChC,UAAU,UAAU,EACpB,KAAK,CAAC,KAAK,SAAS,CAAC,CAAC;AAEzB,YAAQ,MAAM,EAAE,OAAO,MAAM,EACxB,KAAK,SAAS,SAAS,EACvB,KAAK,kBAAkB,KAAK,EAC5B,KAAK,UAAU,QAAQ,OAAO,EAChC,MAAM,OAAO,EACX,KAAK,WAAW;AACf,UAAIH,UAASD,OAAM,IAAI,EAAE;AACzB,qBAAO,IAAI,EACN,KAAK,KAAKC,QAAO,CAAC,EAAE,CAAC,CAAC,EACtB,KAAK,KAAKA,QAAO,CAAC,EAAE,CAAC,CAAC,EACtB,KAAK,SAASA,QAAO,CAAC,EAAE,CAAC,IAAIA,QAAO,CAAC,EAAE,CAAC,CAAC,EACzC,KAAK,UAAUA,QAAO,CAAC,EAAE,CAAC,IAAIA,QAAO,CAAC,EAAE,CAAC,CAAC;AAAA,IACjD,CAAC;AAEL,IAAAG,OAAM,UAAU,YAAY,EACzB,KAAK,CAAC,KAAK,WAAW,CAAC,CAAC,EACxB,MAAM,EAAE,OAAO,MAAM,EACnB,KAAK,SAAS,WAAW,EACzB,KAAK,UAAU,QAAQ,SAAS,EAChC,KAAK,QAAQ,MAAM,EACnB,KAAK,gBAAgB,GAAG,EACxB,KAAK,UAAU,MAAM,EACrB,KAAK,mBAAmB,YAAY;AAEzC,QAAI,SAASA,OAAM,UAAU,SAAS,EACnC,KAAK,IAAI,SAAS,SAAS,GAAG;AAAE,aAAO,EAAE;AAAA,IAAM,CAAC;AAEnD,WAAO,KAAK,EAAE,OAAO;AAErB,WAAO,MAAM,EAAE,OAAO,MAAM,EACvB,KAAK,SAAS,SAAS,GAAG;AAAE,aAAO,oBAAoB,EAAE;AAAA,IAAM,CAAC,EAChE,KAAK,UAAU,SAAS,GAAG;AAAE,aAAO,QAAQ,EAAE,IAAI;AAAA,IAAG,CAAC;AAE3D,IAAAA,OACK,KAAK,MAAM,EACX,KAAK,QAAQ,MAAM,EACnB,KAAK,kBAAkB,KAAK,EAC5B,GAAG,mBAAmB,OAAO,EAC/B,OAAO,SAAS,EACd,GAAG,oBAAoB,OAAO,EAC9B,GAAG,mBAAmB,UAAU,EAChC,GAAG,oCAAoC,UAAU,EACjD,MAAM,gBAAgB,MAAM,EAC5B,MAAM,+BAA+B,eAAe;AAAA,EAC3D;AAEA,EAAAD,OAAM,OAAO,SAASC,QAAO,WAAW,OAAO;AAC7C,QAAIA,OAAM,OAAO;AACf,MAAAA,OACK,GAAG,eAAe,SAASC,QAAO;AAAE,gBAAQ,MAAM,SAAS,EAAE,YAAY,EAAE,MAAMA,MAAK;AAAA,MAAG,CAAC,EAC1F,GAAG,6BAA6B,SAASA,QAAO;AAAE,gBAAQ,MAAM,SAAS,EAAE,IAAIA,MAAK;AAAA,MAAG,CAAC,EACxF,MAAM,SAAS,WAAW;AACzB,YAAI,OAAO,MACP,QAAQ,KAAK,SACb,OAAO,QAAQ,MAAM,SAAS,GAC9B,aAAa,MAAM,WACnB,aAAa,IAAI,MAAM,OAAO,cAAc,aAAa,UAAU,MAAM,MAAM,SAAS,IAAI,WAAW,MAAM,MAAM,GACnH,IAAI,cAAY,YAAY,UAAU;AAE1C,iBAAS,MAAM,GAAG;AAChB,gBAAM,YAAY,MAAM,KAAK,eAAe,OAAO,OAAO,EAAE,CAAC;AAC7D,iBAAO,KAAK,IAAI;AAChB,eAAK,MAAM;AAAA,QACb;AAEA,eAAO,eAAe,QAAQ,eAAe,OAAO,QAAQ,MAAM,CAAC;AAAA,MACrE,CAAC;AAAA,IACP,OAAO;AACL,MAAAD,OACK,KAAK,WAAW;AACf,YAAI,OAAO,MACP,OAAO,WACP,QAAQ,KAAK,SACb,aAAa,IAAI,MAAM,OAAO,cAAc,aAAa,UAAU,MAAM,MAAM,IAAI,IAAI,WAAW,MAAM,MAAM,GAC9G,OAAO,QAAQ,MAAM,IAAI,EAAE,YAAY;AAE3C,0BAAU,IAAI;AACd,cAAM,YAAY,eAAe,OAAO,OAAO;AAC/C,eAAO,KAAK,IAAI;AAChB,aAAK,MAAM,KAAK,EAAE,MAAM,KAAK,EAAE,IAAI,KAAK;AAAA,MAC1C,CAAC;AAAA,IACP;AAAA,EACF;AAEA,EAAAD,OAAM,QAAQ,SAASC,QAAO,OAAO;AACnC,IAAAD,OAAM,KAAKC,QAAO,MAAM,KAAK;AAAA,EAC/B;AAEA,WAAS,SAAS;AAChB,QAAIA,SAAQ,eAAO,IAAI,GACnB,YAAYJ,OAAM,IAAI,EAAE;AAE5B,QAAI,WAAW;AACb,MAAAI,OAAM,UAAU,YAAY,EACvB,MAAM,WAAW,IAAI,EACrB,KAAK,KAAK,UAAU,CAAC,EAAE,CAAC,CAAC,EACzB,KAAK,KAAK,UAAU,CAAC,EAAE,CAAC,CAAC,EACzB,KAAK,SAAS,UAAU,CAAC,EAAE,CAAC,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC,EAC/C,KAAK,UAAU,UAAU,CAAC,EAAE,CAAC,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;AAErD,MAAAA,OAAM,UAAU,SAAS,EACpB,MAAM,WAAW,IAAI,EACrB,KAAK,KAAK,SAAS,GAAG;AAAE,eAAO,EAAE,KAAK,EAAE,KAAK,SAAS,CAAC,MAAM,MAAM,UAAU,CAAC,EAAE,CAAC,IAAI,aAAa,IAAI,UAAU,CAAC,EAAE,CAAC,IAAI,aAAa;AAAA,MAAG,CAAC,EACzI,KAAK,KAAK,SAAS,GAAG;AAAE,eAAO,EAAE,KAAK,CAAC,MAAM,MAAM,UAAU,CAAC,EAAE,CAAC,IAAI,aAAa,IAAI,UAAU,CAAC,EAAE,CAAC,IAAI,aAAa;AAAA,MAAG,CAAC,EACzH,KAAK,SAAS,SAAS,GAAG;AAAE,eAAO,EAAE,SAAS,OAAO,EAAE,SAAS,MAAM,UAAU,CAAC,EAAE,CAAC,IAAI,UAAU,CAAC,EAAE,CAAC,IAAI,aAAa;AAAA,MAAY,CAAC,EACpI,KAAK,UAAU,SAAS,GAAG;AAAE,eAAO,EAAE,SAAS,OAAO,EAAE,SAAS,MAAM,UAAU,CAAC,EAAE,CAAC,IAAI,UAAU,CAAC,EAAE,CAAC,IAAI,aAAa;AAAA,MAAY,CAAC;AAAA,IAC5I,OAEK;AACH,MAAAA,OAAM,UAAU,oBAAoB,EAC/B,MAAM,WAAW,MAAM,EACvB,KAAK,KAAK,IAAI,EACd,KAAK,KAAK,IAAI,EACd,KAAK,SAAS,IAAI,EAClB,KAAK,UAAU,IAAI;AAAA,IAC1B;AAAA,EACF;AAEA,WAAS,QAAQ,MAAM,MAAM,OAAO;AAClC,QAAI,OAAO,KAAK,QAAQ;AACxB,WAAO,SAAS,CAAC,SAAS,CAAC,KAAK,SAAS,OAAO,IAAI,QAAQ,MAAM,MAAM,KAAK;AAAA,EAC/E;AAEA,WAAS,QAAQ,MAAM,MAAM,OAAO;AAClC,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,QAAQ,KAAK;AAClB,SAAK,SAAS;AACd,SAAK,QAAQ;AAAA,EACf;AAEA,UAAQ,YAAY;AAAA,IAClB,aAAa,WAAW;AACtB,UAAI,EAAE,KAAK,WAAW,EAAG,MAAK,MAAM,UAAU,MAAM,KAAK,WAAW;AACpE,aAAO;AAAA,IACT;AAAA,IACA,OAAO,SAAS,OAAOE,OAAM;AAC3B,UAAI,KAAK,SAAU,MAAK,WAAW,OAAO,KAAK,KAAK,SAAS,OAAOA,KAAI;AAAA,UACnE,MAAK,KAAK,SAAS,KAAK;AAC7B,aAAO;AAAA,IACT;AAAA,IACA,OAAO,SAAS,OAAOA,OAAM;AAC3B,WAAK,KAAK,SAAS,OAAOA,KAAI;AAC9B,aAAO;AAAA,IACT;AAAA,IACA,KAAK,SAAS,OAAOA,OAAM;AACzB,UAAI,EAAE,KAAK,WAAW,EAAG,QAAO,KAAK,MAAM,SAAS,KAAK,KAAK,OAAO,OAAOA,KAAI;AAChF,aAAO;AAAA,IACT;AAAA,IACA,MAAM,SAASC,OAAM,OAAOD,OAAM;AAChC,UAAI,IAAI,eAAO,KAAK,IAAI,EAAE,MAAM;AAChC,gBAAU;AAAA,QACRC;AAAA,QACA,KAAK;AAAA,QACL,IAAI,WAAWA,OAAM;AAAA,UACnB,aAAa;AAAA,UACb,QAAQJ;AAAA,UACR,WAAW,IAAI,OAAO,KAAK,MAAM,SAAS;AAAA,UAC1C,MAAAG;AAAA,UACA,UAAU;AAAA,QACZ,CAAC;AAAA,QACD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,WAAS,QAAQ,OAAO;AACtB,QAAI,eAAe,CAAC,MAAM,QAAS;AACnC,QAAI,CAACJ,QAAO,MAAM,MAAM,SAAS,EAAG;AAEpC,QAAI,OAAO,MACPK,QAAO,MAAM,OAAO,SAAS,MAC7BD,SAAQ,QAAQ,MAAM,UAAUC,QAAO,YAAYA,WAAU,cAAc,YAAa,QAAQ,MAAM,SAAS,cAAc,aAC7H,QAAQ,QAAQ,IAAI,OAAO,OAAOA,KAAI,GACtC,QAAQ,QAAQ,IAAI,OAAO,OAAOA,KAAI,GACtC,QAAQP,OAAM,IAAI,GAClBC,UAAS,MAAM,QACf,YAAY,MAAM,WAClB,IAAIA,QAAO,CAAC,EAAE,CAAC,GAAG,IAAI,IACtB,IAAIA,QAAO,CAAC,EAAE,CAAC,GAAG,IAAI,IACtB,IAAIA,QAAO,CAAC,EAAE,CAAC,GAAG,IAAI,IACtB,IAAIA,QAAO,CAAC,EAAE,CAAC,GAAG,IAAI,IACtB,KAAK,GACL,KAAK,GACL,QACA,WAAW,SAAS,SAAS,QAAQ,MAAM,UAC3C,OACA,OACA,SAAS,MAAM,KAAK,MAAM,WAAW,CAAC,KAAK,GAAG,OAAK;AACjD,YAAM,IAAI,EAAE;AACZ,UAAI,gBAAQ,GAAG,IAAI;AACnB,QAAE,SAAS,EAAE,MAAM;AACnB,QAAE,aAAa;AACf,aAAO;AAAA,IACT,CAAC;AAEL,sBAAU,IAAI;AACd,QAAI,OAAO,QAAQ,MAAM,WAAW,IAAI,EAAE,YAAY;AAEtD,QAAIM,UAAS,WAAW;AACtB,UAAI,UAAW,UAAS;AACxB,YAAM,MAAM,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,KAAK,OAAO,CAAC,CAAC;AAC9C,YAAM,YAAY,YAAY,CAAC;AAAA,QAC3B,KAAK,QAAQ,IAAI,IAAIb,KAAI,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;AAAA,QAC7C,KAAK,QAAQ,IAAI,IAAIA,KAAI,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;AAAA,MAC/C,GAAG;AAAA,QACD,KAAK,QAAQ,IAAI,IAAID,KAAI,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;AAAA,QAC7C,KAAK,QAAQ,IAAI,IAAIA,KAAI,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;AAAA,MAC/C,CAAC;AACH,UAAI,OAAO,SAAS,EAAG,MAAK,KAAK;AAAA,IACnC,OAAO;AACL,WAAK,UAAU,CAAC,EAAE,CAAC;AACnB,WAAK,UAAU,CAAC,EAAE,CAAC;AACnB,WAAK,UAAU,CAAC,EAAE,CAAC;AACnB,WAAK,UAAU,CAAC,EAAE,CAAC;AAAA,IACrB;AAEA,SAAK;AACL,SAAK;AACL,SAAK;AACL,SAAK;AAEL,QAAIW,SAAQ,eAAO,IAAI,EAClB,KAAK,kBAAkB,MAAM;AAElC,QAAI,UAAUA,OAAM,UAAU,UAAU,EACnC,KAAK,UAAU,QAAQG,KAAI,CAAC;AAEjC,QAAI,MAAM,SAAS;AACjB,WAAK,QAAQ;AACb,WAAK,QAAQ;AAAA,IACf,OAAO;AACL,UAAI,OAAO,eAAO,MAAM,IAAI,EACvB,GAAG,mBAAmB,OAAO,IAAI,EACjC,GAAG,iBAAiB,OAAO,IAAI;AACpC,UAAI,KAAM,MACL,GAAG,iBAAiB,WAAW,IAAI,EACnC,GAAG,eAAe,UAAU,IAAI;AAErC,qBAAY,MAAM,IAAI;AAAA,IACxB;AAEA,WAAO,KAAK,IAAI;AAChB,SAAK,MAAM,OAAOD,MAAK,IAAI;AAE3B,aAAS,MAAMD,QAAO;AACpB,iBAAW,KAAKA,OAAM,kBAAkB,CAACA,MAAK,GAAG;AAC/C,mBAAW,KAAK;AACd,cAAI,EAAE,eAAe,EAAE,WAAY,GAAE,MAAM,gBAAQ,GAAG,IAAI;AAAA,MAC9D;AACA,UAAI,YAAY,CAAC,SAAS,CAAC,SAAS,OAAO,WAAW,GAAG;AACvD,cAAMG,SAAQ,OAAO,CAAC;AACtB,YAAI,IAAIA,OAAM,IAAI,CAAC,IAAIA,OAAM,CAAC,CAAC,IAAI,IAAIA,OAAM,IAAI,CAAC,IAAIA,OAAM,CAAC,CAAC;AAC5D,kBAAQ;AAAA;AAER,kBAAQ;AAAA,MACZ;AACA,iBAAWA,UAAS;AAClB,YAAIA,OAAM,IAAK,CAAAA,OAAM,CAAC,IAAIA,OAAM,IAAI,CAAC,GAAGA,OAAM,CAAC,IAAIA,OAAM,IAAI,CAAC;AAChE,eAAS;AACT,MAAAC,iBAAQJ,MAAK;AACb,WAAKA,MAAK;AAAA,IACZ;AAEA,aAAS,KAAKA,QAAO;AACnB,YAAMG,SAAQ,OAAO,CAAC,GAAG,SAASA,OAAM;AACxC,UAAI;AAEJ,WAAKA,OAAM,CAAC,IAAI,OAAO,CAAC;AACxB,WAAKA,OAAM,CAAC,IAAI,OAAO,CAAC;AAExB,cAAQF,OAAM;AAAA,QACZ,KAAK;AAAA,QACL,KAAK,WAAW;AACd,cAAI,MAAO,MAAKb,KAAI,IAAI,IAAIC,KAAI,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,KAAK;AACtE,cAAI,MAAO,MAAKD,KAAI,IAAI,IAAIC,KAAI,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,KAAK;AACtE;AAAA,QACF;AAAA,QACA,KAAK,aAAa;AAChB,cAAI,OAAO,CAAC,GAAG;AACb,gBAAI,MAAO,MAAKD,KAAI,GAAGC,KAAI,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,KAAKD,KAAI,GAAGC,KAAI,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,QAAQ;AACzF,gBAAI,MAAO,MAAKD,KAAI,GAAGC,KAAI,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,KAAKD,KAAI,GAAGC,KAAI,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,QAAQ;AAAA,UAC3F,OAAO;AACL,gBAAI,QAAQ,EAAG,MAAKD,KAAI,IAAI,IAAIC,KAAI,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK;AAAA,qBAC5D,QAAQ,EAAG,MAAKD,KAAI,IAAI,IAAIC,KAAI,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,IAAI,KAAK,KAAK;AAC1E,gBAAI,QAAQ,EAAG,MAAKD,KAAI,IAAI,IAAIC,KAAI,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK;AAAA,qBAC5D,QAAQ,EAAG,MAAKD,KAAI,IAAI,IAAIC,KAAI,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,IAAI,KAAK,KAAK;AAAA,UAC5E;AACA;AAAA,QACF;AAAA,QACA,KAAK,aAAa;AAChB,cAAI,MAAO,MAAKD,KAAI,GAAGC,KAAI,GAAG,KAAK,KAAK,KAAK,CAAC,GAAG,KAAKD,KAAI,GAAGC,KAAI,GAAG,KAAK,KAAK,KAAK,CAAC;AACpF,cAAI,MAAO,MAAKD,KAAI,GAAGC,KAAI,GAAG,KAAK,KAAK,KAAK,CAAC,GAAG,KAAKD,KAAI,GAAGC,KAAI,GAAG,KAAK,KAAK,KAAK,CAAC;AACpF;AAAA,QACF;AAAA,MACF;AAEA,UAAI,KAAK,IAAI;AACX,iBAAS;AACT,YAAI,IAAI,KAAK,IAAI,KAAK;AACtB,YAAI,IAAI,KAAK,IAAI,KAAK;AACtB,YAAIa,SAAQ,MAAO,SAAQ,KAAK,UAAU,QAAQA,QAAO,MAAMA,KAAI,CAAC,CAAC;AAAA,MACvE;AAEA,UAAI,KAAK,IAAI;AACX,iBAAS;AACT,YAAI,IAAI,KAAK,IAAI,KAAK;AACtB,YAAI,IAAI,KAAK,IAAI,KAAK;AACtB,YAAIA,SAAQ,MAAO,SAAQ,KAAK,UAAU,QAAQA,QAAO,MAAMA,KAAI,CAAC,CAAC;AAAA,MACvE;AAEA,UAAI,MAAM,UAAW,aAAY,MAAM;AACvC,UAAI,MAAO,MAAK,UAAU,CAAC,EAAE,CAAC,GAAG,KAAK,UAAU,CAAC,EAAE,CAAC;AACpD,UAAI,MAAO,MAAK,UAAU,CAAC,EAAE,CAAC,GAAG,KAAK,UAAU,CAAC,EAAE,CAAC;AAEpD,UAAI,UAAU,CAAC,EAAE,CAAC,MAAM,MACjB,UAAU,CAAC,EAAE,CAAC,MAAM,MACpB,UAAU,CAAC,EAAE,CAAC,MAAM,MACpB,UAAU,CAAC,EAAE,CAAC,MAAM,IAAI;AAC7B,cAAM,YAAY,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC;AACrC,eAAO,KAAK,IAAI;AAChB,aAAK,MAAMF,QAAOC,MAAK,IAAI;AAAA,MAC7B;AAAA,IACF;AAEA,aAAS,MAAMD,QAAO;AACpB,MAAAK,eAAcL,MAAK;AACnB,UAAIA,OAAM,SAAS;AACjB,YAAIA,OAAM,QAAQ,OAAQ;AAC1B,YAAI,YAAa,cAAa,WAAW;AACzC,sBAAc,WAAW,WAAW;AAAE,wBAAc;AAAA,QAAM,GAAG,GAAG;AAAA,MAClE,OAAO;AACL,gBAAWA,OAAM,MAAM,MAAM;AAC7B,aAAK,GAAG,2DAA2D,IAAI;AAAA,MACzE;AACA,MAAAD,OAAM,KAAK,kBAAkB,KAAK;AAClC,cAAQ,KAAK,UAAU,QAAQ,OAAO;AACtC,UAAI,MAAM,UAAW,aAAY,MAAM;AACvC,UAAI,MAAM,SAAS,EAAG,OAAM,YAAY,MAAM,OAAO,KAAK,IAAI;AAC9D,WAAK,IAAIC,QAAOC,MAAK,IAAI;AAAA,IAC3B;AAEA,aAAS,UAAUD,QAAO;AACxB,cAAQA,OAAM,SAAS;AAAA,QACrB,KAAK,IAAI;AACP,qBAAW,SAAS;AACpB;AAAA,QACF;AAAA,QACA,KAAK,IAAI;AACP,cAAIC,UAAS,aAAa;AACxB,gBAAI,MAAO,MAAK,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK;AAChD,gBAAI,MAAO,MAAK,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK;AAChD,YAAAA,QAAO;AACP,iBAAKD,MAAK;AAAA,UACZ;AACA;AAAA,QACF;AAAA,QACA,KAAK,IAAI;AACP,cAAIC,UAAS,eAAeA,UAAS,aAAa;AAChD,gBAAI,QAAQ,EAAG,MAAK,KAAK;AAAA,qBAAa,QAAQ,EAAG,MAAK,KAAK;AAC3D,gBAAI,QAAQ,EAAG,MAAK,KAAK;AAAA,qBAAa,QAAQ,EAAG,MAAK,KAAK;AAC3D,YAAAA,QAAO;AACP,oBAAQ,KAAK,UAAU,QAAQ,SAAS;AACxC,iBAAKD,MAAK;AAAA,UACZ;AACA;AAAA,QACF;AAAA,QACA;AAAS;AAAA,MACX;AACA,MAAAI,iBAAQJ,MAAK;AAAA,IACf;AAEA,aAAS,SAASA,QAAO;AACvB,cAAQA,OAAM,SAAS;AAAA,QACrB,KAAK,IAAI;AACP,cAAI,UAAU;AACZ,oBAAQ,QAAQ,WAAW;AAC3B,iBAAKA,MAAK;AAAA,UACZ;AACA;AAAA,QACF;AAAA,QACA,KAAK,IAAI;AACP,cAAIC,UAAS,aAAa;AACxB,gBAAI,QAAQ,EAAG,MAAK;AAAA,qBAAa,QAAQ,EAAG,MAAK;AACjD,gBAAI,QAAQ,EAAG,MAAK;AAAA,qBAAa,QAAQ,EAAG,MAAK;AACjD,YAAAA,QAAO;AACP,iBAAKD,MAAK;AAAA,UACZ;AACA;AAAA,QACF;AAAA,QACA,KAAK,IAAI;AACP,cAAIC,UAAS,YAAY;AACvB,gBAAID,OAAM,QAAQ;AAChB,kBAAI,MAAO,MAAK,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK;AAChD,kBAAI,MAAO,MAAK,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK;AAChD,cAAAC,QAAO;AAAA,YACT,OAAO;AACL,kBAAI,QAAQ,EAAG,MAAK;AAAA,uBAAa,QAAQ,EAAG,MAAK;AACjD,kBAAI,QAAQ,EAAG,MAAK;AAAA,uBAAa,QAAQ,EAAG,MAAK;AACjD,cAAAA,QAAO;AAAA,YACT;AACA,oBAAQ,KAAK,UAAU,QAAQC,KAAI,CAAC;AACpC,iBAAKF,MAAK;AAAA,UACZ;AACA;AAAA,QACF;AAAA,QACA;AAAS;AAAA,MACX;AACA,MAAAI,iBAAQJ,MAAK;AAAA,IACf;AAAA,EACF;AAEA,WAAS,WAAW,OAAO;AACzB,YAAQ,MAAM,SAAS,EAAE,MAAM,KAAK;AAAA,EACtC;AAEA,WAAS,WAAW,OAAO;AACzB,YAAQ,MAAM,SAAS,EAAE,MAAM,KAAK;AAAA,EACtC;AAEA,WAAS,aAAa;AACpB,QAAI,QAAQ,KAAK,WAAW,EAAC,WAAW,KAAI;AAC5C,UAAM,SAAS,QAAQJ,QAAO,MAAM,MAAM,SAAS,CAAC;AACpD,UAAM,MAAM;AACZ,WAAO;AAAA,EACT;AAEA,EAAAE,OAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAUF,UAAS,OAAO,MAAM,aAAa,IAAIU,kBAAS,QAAQ,CAAC,CAAC,GAAGR,UAASF;AAAA,EACnG;AAEA,EAAAE,OAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAUD,UAAS,OAAO,MAAM,aAAa,IAAIS,kBAAS,CAAC,CAAC,CAAC,GAAGR,UAASD;AAAA,EAC5F;AAEA,EAAAC,OAAM,YAAY,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAU,YAAY,OAAO,MAAM,aAAa,IAAIQ,kBAAS,CAAC,CAAC,CAAC,GAAGR,UAAS;AAAA,EAC/F;AAEA,EAAAA,OAAM,aAAa,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,aAAa,CAAC,GAAGA,UAAS;AAAA,EACvD;AAEA,EAAAA,OAAM,eAAe,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,OAAO,CAAC,CAAC,GAAGA,UAAS;AAAA,EAClD;AAEA,EAAAA,OAAM,KAAK,WAAW;AACpB,QAAI,QAAQ,UAAU,GAAG,MAAM,WAAW,SAAS;AACnD,WAAO,UAAU,YAAYA,SAAQ;AAAA,EACvC;AAEA,SAAOA;AACT;;;AC5mBO,IAAIS,OAAM,KAAK;AACf,IAAI,MAAM,KAAK;AACf,IAAI,MAAM,KAAK;AACf,IAAIC,MAAK,KAAK;AACd,IAAIC,UAASD,MAAK;AAClB,IAAIE,OAAMF,MAAK;AACf,IAAIG,OAAM,KAAK;AACf,IAAIC,WAAU;;;ACLrB,SAASC,OAAM,GAAG,GAAG;AACnB,SAAO,MAAM,KAAK,EAAC,QAAQ,IAAI,EAAC,GAAG,CAAC,GAAG,MAAM,IAAI,CAAC;AACpD;AAEA,SAAS,aAAa,SAAS;AAC7B,SAAO,SAASC,IAAG,GAAG;AACpB,WAAO;AAAA,MACLA,GAAE,OAAO,QAAQA,GAAE,OAAO;AAAA,MAC1B,EAAE,OAAO,QAAQ,EAAE,OAAO;AAAA,IAC5B;AAAA,EACF;AACF;AAEe,SAAR,gBAAmB;AACxB,SAAO,MAAM,OAAO,KAAK;AAC3B;AAEO,SAAS,iBAAiB;AAC/B,SAAO,MAAM,OAAO,IAAI;AAC1B;AAEO,SAAS,gBAAgB;AAC9B,SAAO,MAAM,MAAM,KAAK;AAC1B;AAEA,SAAS,MAAM,UAAUC,YAAW;AAClC,MAAI,WAAW,GACX,aAAa,MACb,gBAAgB,MAChB,aAAa;AAEjB,WAASC,OAAM,QAAQ;AACrB,QAAI,IAAI,OAAO,QACX,YAAY,IAAI,MAAM,CAAC,GACvB,aAAaH,OAAM,GAAG,CAAC,GACvB,SAAS,IAAI,MAAM,IAAI,CAAC,GACxBI,UAAS,IAAI,MAAM,CAAC,GACpB,IAAI,GAAG;AAEX,aAAS,aAAa,KAAK,EAAC,QAAQ,IAAI,EAAC,GAAGF,aACtC,CAAC,GAAG,MAAM,OAAO,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,IACjC,CAAC,GAAG,MAAM,OAAO,IAAI,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AAGxC,aAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,UAAIG,KAAI;AACR,eAAS,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,CAAAA,MAAK,OAAO,IAAI,IAAI,CAAC,IAAI,WAAW,OAAO,IAAI,IAAI,CAAC;AAChF,WAAK,UAAU,CAAC,IAAIA;AAAA,IACtB;AACA,QAAIC,KAAI,GAAGC,OAAM,WAAW,CAAC,IAAI;AACjC,SAAK,IAAI,WAAWA,OAAM;AAG1B;AACE,UAAIF,KAAI;AACR,UAAI,WAAY,YAAW,KAAK,CAACJ,IAAG,MAAM,WAAW,UAAUA,EAAC,GAAG,UAAU,CAAC,CAAC,CAAC;AAChF,iBAAW,KAAK,YAAY;AAC1B,cAAM,KAAKI;AACX,YAAI,UAAU;AACZ,gBAAM,gBAAgBL,OAAM,CAAC,IAAI,GAAG,CAAC,EAAE,OAAO,OAAK,IAAI,IAAI,OAAO,CAAC,IAAI,IAAI,CAAC,IAAI,OAAO,IAAI,IAAI,CAAC,CAAC;AACjG,cAAI,cAAe,eAAc,KAAK,CAACC,IAAG,MAAM,cAAcA,KAAI,IAAI,CAAC,OAAO,CAACA,KAAI,IAAI,CAAC,IAAI,OAAO,IAAI,IAAIA,EAAC,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,IAAI,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC;AAC/J,qBAAW,KAAK,eAAe;AAC7B,gBAAI,IAAI,GAAG;AACT,oBAAME,SAAQ,OAAO,CAAC,IAAI,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,IAAI,CAAC,IAAI,EAAC,QAAQ,MAAM,QAAQ,KAAI;AACrF,cAAAA,OAAM,SAAS,EAAC,OAAO,GAAG,YAAYE,IAAG,UAAUA,MAAK,OAAO,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG,OAAO,OAAO,CAAC,IAAI,IAAI,CAAC,EAAC;AAAA,YAC3G,OAAO;AACL,oBAAMF,SAAQ,OAAO,IAAI,IAAI,CAAC,MAAM,OAAO,IAAI,IAAI,CAAC,IAAI,EAAC,QAAQ,MAAM,QAAQ,KAAI;AACnF,cAAAA,OAAM,SAAS,EAAC,OAAO,GAAG,YAAYE,IAAG,UAAUA,MAAK,OAAO,IAAI,IAAI,CAAC,IAAI,GAAG,OAAO,OAAO,IAAI,IAAI,CAAC,EAAC;AAAA,YACzG;AAAA,UACF;AACA,UAAAD,QAAO,CAAC,IAAI,EAAC,OAAO,GAAG,YAAY,IAAI,UAAUC,IAAG,OAAO,UAAU,CAAC,EAAC;AAAA,QACzE,OAAO;AACL,gBAAM,gBAAgBL,OAAM,GAAG,CAAC,EAAE,OAAO,OAAK,OAAO,IAAI,IAAI,CAAC,KAAK,OAAO,IAAI,IAAI,CAAC,CAAC;AACpF,cAAI,cAAe,eAAc,KAAK,CAACC,IAAG,MAAM,cAAc,OAAO,IAAI,IAAIA,EAAC,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC;AACnG,qBAAW,KAAK,eAAe;AAC7B,gBAAIE;AACJ,gBAAI,IAAI,GAAG;AACT,cAAAA,SAAQ,OAAO,IAAI,IAAI,CAAC,MAAM,OAAO,IAAI,IAAI,CAAC,IAAI,EAAC,QAAQ,MAAM,QAAQ,KAAI;AAC7E,cAAAA,OAAM,SAAS,EAAC,OAAO,GAAG,YAAYE,IAAG,UAAUA,MAAK,OAAO,IAAI,IAAI,CAAC,IAAI,GAAG,OAAO,OAAO,IAAI,IAAI,CAAC,EAAC;AAAA,YACzG,OAAO;AACL,cAAAF,SAAQ,OAAO,IAAI,IAAI,CAAC,MAAM,OAAO,IAAI,IAAI,CAAC,IAAI,EAAC,QAAQ,MAAM,QAAQ,KAAI;AAC7E,cAAAA,OAAM,SAAS,EAAC,OAAO,GAAG,YAAYE,IAAG,UAAUA,MAAK,OAAO,IAAI,IAAI,CAAC,IAAI,GAAG,OAAO,OAAO,IAAI,IAAI,CAAC,EAAC;AACvG,kBAAI,MAAM,EAAG,CAAAF,OAAM,SAASA,OAAM;AAAA,YACpC;AACA,gBAAIA,OAAM,UAAUA,OAAM,UAAUA,OAAM,OAAO,QAAQA,OAAM,OAAO,OAAO;AAC3E,oBAAM,SAASA,OAAM;AACrB,cAAAA,OAAM,SAASA,OAAM;AACrB,cAAAA,OAAM,SAAS;AAAA,YACjB;AAAA,UACF;AACA,UAAAC,QAAO,CAAC,IAAI,EAAC,OAAO,GAAG,YAAY,IAAI,UAAUC,IAAG,OAAO,UAAU,CAAC,EAAC;AAAA,QACzE;AACA,QAAAA,MAAK;AAAA,MACP;AAAA,IACF;AAGA,aAAS,OAAO,OAAO,MAAM;AAC7B,WAAO,SAASD;AAChB,WAAO,aAAa,OAAO,KAAK,UAAU,IAAI;AAAA,EAChD;AAEA,EAAAD,OAAM,WAAW,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,WAAWG,KAAI,GAAG,CAAC,GAAGH,UAAS;AAAA,EAC5D;AAEA,EAAAA,OAAM,aAAa,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,aAAa,GAAGA,UAAS;AAAA,EACtD;AAEA,EAAAA,OAAM,gBAAgB,SAAS,GAAG;AAChC,WAAO,UAAU,UAAU,gBAAgB,GAAGA,UAAS;AAAA,EACzD;AAEA,EAAAA,OAAM,aAAa,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,KAAK,OAAO,aAAa,QAAQ,aAAa,aAAa,CAAC,GAAG,IAAI,GAAGA,UAAS,cAAc,WAAW;AAAA,EACrI;AAEA,SAAOA;AACT;;;ACzHO,IAAI,QAAQ,MAAM,UAAU;;;ACApB,SAARK,kBAAiBC,IAAG;AACzB,SAAO,WAAW;AAChB,WAAOA;AAAA,EACT;AACF;;;ACCA,SAAS,cAAc,GAAG;AACxB,SAAO,EAAE;AACX;AAEA,SAAS,cAAc,GAAG;AACxB,SAAO,EAAE;AACX;AAEA,SAAS,cAAc,GAAG;AACxB,SAAO,EAAE;AACX;AAEA,SAAS,kBAAkB,GAAG;AAC5B,SAAO,EAAE;AACX;AAEA,SAAS,gBAAgB,GAAG;AAC1B,SAAO,EAAE;AACX;AAEA,SAAS,kBAAkB;AACzB,SAAO;AACT;AAEA,SAAS,yBAAyB;AAChC,SAAO;AACT;AAEA,SAAS,OAAO,YAAY;AAC1B,MAAI,SAAS,eACT,SAAS,eACT,eAAe,eACf,eAAe,eACf,aAAa,mBACb,WAAW,iBACX,WAAW,iBACX,UAAU;AAEd,WAASC,UAAS;AAChB,QAAI,QACA,IAAI,OAAO,MAAM,MAAM,SAAS,GAChC,IAAI,OAAO,MAAM,MAAM,SAAS,GAChC,KAAK,SAAS,MAAM,MAAM,SAAS,IAAI,GACvC,OAAO,MAAM,KAAK,SAAS,GAC3B,KAAK,CAAC,aAAa,MAAM,OAAO,KAAK,CAAC,IAAI,GAAG,KAAK,GAClD,MAAM,WAAW,MAAM,MAAM,IAAI,IAAIC,SACrC,MAAM,SAAS,MAAM,MAAM,IAAI,IAAIA,SACnC,KAAK,CAAC,aAAa,MAAM,OAAO,KAAK,CAAC,IAAI,GAAG,KAAK,GAClD,MAAM,WAAW,MAAM,MAAM,IAAI,IAAIA,SACrC,MAAM,SAAS,MAAM,MAAM,IAAI,IAAIA;AAEvC,QAAI,CAAC,QAAS,WAAU,SAAS,KAAK;AAEtC,QAAI,KAAKC,UAAS;AAChB,UAAIC,KAAI,MAAM,GAAG,IAAI,KAAK,IAAID,SAAS,OAAM,OAAO,OAAO,IAAI,OAAO,OAAO,OAAO,IAAI,OAAO;AAAA,UAC1F,OAAM,OAAO,MAAM,OAAO;AAC/B,UAAIC,KAAI,MAAM,GAAG,IAAI,KAAK,IAAID,SAAS,OAAM,OAAO,OAAO,IAAI,OAAO,OAAO,OAAO,IAAI,OAAO;AAAA,UAC1F,OAAM,OAAO,MAAM,OAAO;AAAA,IACjC;AAEA,YAAQ,OAAO,KAAK,IAAI,GAAG,GAAG,KAAK,IAAI,GAAG,CAAC;AAC3C,YAAQ,IAAI,GAAG,GAAG,IAAI,KAAK,GAAG;AAC9B,QAAI,QAAQ,OAAO,QAAQ,KAAK;AAC9B,UAAI,YAAY;AACd,YAAI,KAAK,CAAC,WAAW,MAAM,MAAM,SAAS,GAAG,MAAM,KAAK,IAAI,OAAO,MAAM,OAAO;AAChF,gBAAQ,iBAAiB,GAAG,GAAG,MAAM,IAAI,GAAG,GAAG,MAAM,IAAI,GAAG,CAAC;AAC7D,gBAAQ,OAAO,KAAK,IAAI,GAAG,GAAG,KAAK,IAAI,GAAG,CAAC;AAC3C,gBAAQ,OAAO,MAAM,IAAI,GAAG,GAAG,MAAM,IAAI,GAAG,CAAC;AAAA,MAC/C,OAAO;AACL,gBAAQ,iBAAiB,GAAG,GAAG,KAAK,IAAI,GAAG,GAAG,KAAK,IAAI,GAAG,CAAC;AAC3D,gBAAQ,IAAI,GAAG,GAAG,IAAI,KAAK,GAAG;AAAA,MAChC;AAAA,IACF;AACA,YAAQ,iBAAiB,GAAG,GAAG,KAAK,IAAI,GAAG,GAAG,KAAK,IAAI,GAAG,CAAC;AAC3D,YAAQ,UAAU;AAElB,QAAI,OAAQ,QAAO,UAAU,MAAM,SAAS,MAAM;AAAA,EACpD;AAEA,MAAI,WAAY,CAAAF,QAAO,aAAa,SAAS,GAAG;AAC9C,WAAO,UAAU,UAAU,aAAa,OAAO,MAAM,aAAa,IAAII,kBAAS,CAAC,CAAC,GAAGJ,WAAU;AAAA,EAChG;AAEA,EAAAA,QAAO,SAAS,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,eAAe,eAAe,OAAO,MAAM,aAAa,IAAII,kBAAS,CAAC,CAAC,GAAGJ,WAAU;AAAA,EACjH;AAEA,EAAAA,QAAO,eAAe,SAAS,GAAG;AAChC,WAAO,UAAU,UAAU,eAAe,OAAO,MAAM,aAAa,IAAII,kBAAS,CAAC,CAAC,GAAGJ,WAAU;AAAA,EAClG;AAEA,EAAAA,QAAO,eAAe,SAAS,GAAG;AAChC,WAAO,UAAU,UAAU,eAAe,OAAO,MAAM,aAAa,IAAII,kBAAS,CAAC,CAAC,GAAGJ,WAAU;AAAA,EAClG;AAEA,EAAAA,QAAO,aAAa,SAAS,GAAG;AAC9B,WAAO,UAAU,UAAU,aAAa,OAAO,MAAM,aAAa,IAAII,kBAAS,CAAC,CAAC,GAAGJ,WAAU;AAAA,EAChG;AAEA,EAAAA,QAAO,WAAW,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAU,WAAW,OAAO,MAAM,aAAa,IAAII,kBAAS,CAAC,CAAC,GAAGJ,WAAU;AAAA,EAC9F;AAEA,EAAAA,QAAO,WAAW,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAU,WAAW,OAAO,MAAM,aAAa,IAAII,kBAAS,CAAC,CAAC,GAAGJ,WAAU;AAAA,EAC9F;AAEA,EAAAA,QAAO,SAAS,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,SAAS,GAAGA,WAAU;AAAA,EACnD;AAEA,EAAAA,QAAO,SAAS,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,SAAS,GAAGA,WAAU;AAAA,EACnD;AAEA,EAAAA,QAAO,UAAU,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAW,UAAU,KAAK,OAAO,OAAO,GAAIA,WAAU;AAAA,EACzE;AAEA,SAAOA;AACT;AAEe,SAAR,iBAAmB;AACxB,SAAO,OAAO;AAChB;AAEO,SAAS,cAAc;AAC5B,SAAO,OAAO,sBAAsB;AACtC;;;ACrIA,IAAI,QAAQ,MAAM;AAEX,IAAIK,SAAQ,MAAM;;;ACFV,SAARC,mBAAiBC,IAAG,GAAG;AAC5B,SAAOA,KAAI;AACb;;;ACFe,SAARC,cAAiB,MAAM;AAC5B,MAAI,IAAI,GAAG,IAAI,KAAK,QAAQ,OAAO,KAAK,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAC3F,SAAO,EAAE,IAAI,EAAG,SAAQ,KAAK,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAChF,SAAO;AACT;;;ACJA,IAAOC,oBAAQ,CAAAC,OAAK,MAAMA;;;ACAX,SAARC,kBAAiB,MAAM,MAAM;AAClC,MAAI,IAAI,IAAI,IAAI,KAAK,QAAQC;AAC7B,SAAO,EAAE,IAAI,EAAG,KAAIA,KAAI,aAAa,MAAM,KAAK,CAAC,CAAC,EAAG,QAAOA;AAC5D,SAAO;AACT;AAEA,SAAS,aAAa,MAAMC,QAAO;AACjC,MAAIC,KAAID,OAAM,CAAC,GAAGE,KAAIF,OAAM,CAAC,GAAG,WAAW;AAC3C,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK;AAC1D,QAAIG,MAAK,KAAK,CAAC,GAAG,KAAKA,IAAG,CAAC,GAAG,KAAKA,IAAG,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC;AAC7E,QAAI,gBAAgBA,KAAI,IAAIH,MAAK,EAAG,QAAO;AAC3C,QAAM,KAAKE,OAAQ,KAAKA,MAASD,MAAK,KAAK,OAAOC,KAAI,OAAO,KAAK,MAAM,GAAM,YAAW,CAAC;AAAA,EAC5F;AACA,SAAO;AACT;AAEA,SAAS,gBAAgBE,IAAG,GAAGL,IAAG;AAChC,MAAI;AAAG,SAAO,UAAUK,IAAG,GAAGL,EAAC,KAAK,OAAOK,GAAE,IAAI,EAAEA,GAAE,CAAC,MAAM,EAAE,CAAC,EAAE,GAAGL,GAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAChF;AAEA,SAAS,UAAUK,IAAG,GAAGL,IAAG;AAC1B,UAAQ,EAAE,CAAC,IAAIK,GAAE,CAAC,MAAML,GAAE,CAAC,IAAIK,GAAE,CAAC,QAAQL,GAAE,CAAC,IAAIK,GAAE,CAAC,MAAM,EAAE,CAAC,IAAIA,GAAE,CAAC;AACtE;AAEA,SAAS,OAAO,GAAG,GAAG,GAAG;AACvB,SAAO,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C;;;AC1Be,SAAR,eAAmB;AAAC;;;ACQ3B,IAAI,QAAQ;AAAA,EACV,CAAC;AAAA,EACD,CAAC,CAAC,CAAC,GAAK,GAAG,GAAG,CAAC,KAAK,CAAG,CAAC,CAAC;AAAA,EACzB,CAAC,CAAC,CAAC,KAAK,CAAG,GAAG,CAAC,GAAK,GAAG,CAAC,CAAC;AAAA,EACzB,CAAC,CAAC,CAAC,KAAK,CAAG,GAAG,CAAC,KAAK,CAAG,CAAC,CAAC;AAAA,EACzB,CAAC,CAAC,CAAC,GAAK,GAAG,GAAG,CAAC,KAAK,CAAG,CAAC,CAAC;AAAA,EACzB,CAAC,CAAC,CAAC,GAAK,GAAG,GAAG,CAAC,KAAK,CAAG,CAAC,GAAG,CAAC,CAAC,GAAK,GAAG,GAAG,CAAC,KAAK,CAAG,CAAC,CAAC;AAAA,EACnD,CAAC,CAAC,CAAC,GAAK,GAAG,GAAG,CAAC,GAAK,GAAG,CAAC,CAAC;AAAA,EACzB,CAAC,CAAC,CAAC,GAAK,GAAG,GAAG,CAAC,KAAK,CAAG,CAAC,CAAC;AAAA,EACzB,CAAC,CAAC,CAAC,KAAK,CAAG,GAAG,CAAC,GAAK,GAAG,CAAC,CAAC;AAAA,EACzB,CAAC,CAAC,CAAC,GAAK,GAAG,GAAG,CAAC,GAAK,GAAG,CAAC,CAAC;AAAA,EACzB,CAAC,CAAC,CAAC,KAAK,CAAG,GAAG,CAAC,GAAK,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,CAAG,GAAG,CAAC,GAAK,GAAG,CAAC,CAAC;AAAA,EACnD,CAAC,CAAC,CAAC,KAAK,CAAG,GAAG,CAAC,GAAK,GAAG,CAAC,CAAC;AAAA,EACzB,CAAC,CAAC,CAAC,KAAK,CAAG,GAAG,CAAC,KAAK,CAAG,CAAC,CAAC;AAAA,EACzB,CAAC,CAAC,CAAC,GAAK,GAAG,GAAG,CAAC,KAAK,CAAG,CAAC,CAAC;AAAA,EACzB,CAAC,CAAC,CAAC,KAAK,CAAG,GAAG,CAAC,GAAK,GAAG,CAAC,CAAC;AAAA,EACzB,CAAC;AACH;AAEe,SAAR,mBAAmB;AACxB,MAAI,KAAK,GACL,KAAK,GACLC,aAAY,kBACZ,SAAS;AAEb,WAAS,SAAS,QAAQ;AACxB,QAAI,KAAKA,WAAU,MAAM;AAGzB,QAAI,CAAC,MAAM,QAAQ,EAAE,GAAG;AACtB,YAAM,IAAI,OAAO,QAAQ,MAAM;AAC/B,WAAK,MAAM,GAAG,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE;AACtC,aAAO,GAAG,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC,EAAG,IAAG,IAAI;AACzC,aAAO,GAAG,CAAC,IAAI,EAAE,CAAC,EAAG,IAAG,MAAM;AAAA,IAChC,OAAO;AACL,WAAK,GAAG,MAAM,EAAE,KAAKC,kBAAS;AAAA,IAChC;AAEA,WAAO,GAAG,IAAI,WAAS,QAAQ,QAAQ,KAAK,CAAC;AAAA,EAC/C;AAIA,WAAS,QAAQ,QAAQ,OAAO;AAC9B,UAAM,IAAI,SAAS,OAAO,MAAM,CAAC;AACjC,QAAI,MAAM,CAAC,EAAG,OAAM,IAAI,MAAM,kBAAkB,KAAK,EAAE;AAEvD,QAAI,WAAW,CAAC,GACZ,QAAQ,CAAC;AAEb,aAAS,QAAQ,GAAG,SAAS,MAAM;AACjC,aAAO,MAAM,QAAQ,CAAC;AACtB,UAAIC,cAAK,IAAI,IAAI,EAAG,UAAS,KAAK,CAAC,IAAI,CAAC;AAAA,UACnC,OAAM,KAAK,IAAI;AAAA,IACtB,CAAC;AAED,UAAM,QAAQ,SAAS,MAAM;AAC3B,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,SAAS,IAAI,GAAG,EAAE,GAAG;AACxD,YAAIC,mBAAU,UAAU,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,MAAM,IAAI;AACrD,kBAAQ,KAAK,IAAI;AACjB;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAED,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,MACA,aAAa;AAAA,IACf;AAAA,EACF;AAIA,WAAS,SAAS,QAAQ,OAAO,UAAU;AACzC,QAAI,kBAAkB,IAAI,SACtB,gBAAgB,IAAI,SACpBC,IAAGC,IAAG,IAAI,IAAI,IAAI;AAGtB,IAAAD,KAAIC,KAAI;AACR,SAAK,MAAM,OAAO,CAAC,GAAG,KAAK;AAC3B,UAAM,MAAM,CAAC,EAAE,QAAQ,MAAM;AAC7B,WAAO,EAAED,KAAI,KAAK,GAAG;AACnB,WAAK,IAAI,KAAK,MAAM,OAAOA,KAAI,CAAC,GAAG,KAAK;AACxC,YAAM,KAAK,MAAM,CAAC,EAAE,QAAQ,MAAM;AAAA,IACpC;AACA,UAAM,MAAM,CAAC,EAAE,QAAQ,MAAM;AAG7B,WAAO,EAAEC,KAAI,KAAK,GAAG;AACnB,MAAAD,KAAI;AACJ,WAAK,MAAM,OAAOC,KAAI,KAAK,EAAE,GAAG,KAAK;AACrC,WAAK,MAAM,OAAOA,KAAI,EAAE,GAAG,KAAK;AAChC,YAAM,MAAM,IAAI,MAAM,CAAC,EAAE,QAAQ,MAAM;AACvC,aAAO,EAAED,KAAI,KAAK,GAAG;AACnB,aAAK,IAAI,KAAK,MAAM,OAAOC,KAAI,KAAK,KAAKD,KAAI,CAAC,GAAG,KAAK;AACtD,aAAK,IAAI,KAAK,MAAM,OAAOC,KAAI,KAAKD,KAAI,CAAC,GAAG,KAAK;AACjD,cAAM,KAAK,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,EAAE,QAAQ,MAAM;AAAA,MACxD;AACA,YAAM,KAAK,MAAM,CAAC,EAAE,QAAQ,MAAM;AAAA,IACpC;AAGA,IAAAA,KAAI;AACJ,SAAK,OAAOC,KAAI,EAAE,KAAK;AACvB,UAAM,MAAM,CAAC,EAAE,QAAQ,MAAM;AAC7B,WAAO,EAAED,KAAI,KAAK,GAAG;AACnB,WAAK,IAAI,KAAK,MAAM,OAAOC,KAAI,KAAKD,KAAI,CAAC,GAAG,KAAK;AACjD,YAAM,MAAM,IAAI,MAAM,CAAC,EAAE,QAAQ,MAAM;AAAA,IACzC;AACA,UAAM,MAAM,CAAC,EAAE,QAAQ,MAAM;AAE7B,aAAS,OAAO,MAAM;AACpB,UAAIE,SAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,IAAIF,IAAG,KAAK,CAAC,EAAE,CAAC,IAAIC,EAAC,GACvC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,IAAID,IAAG,KAAK,CAAC,EAAE,CAAC,IAAIC,EAAC,GACrC,aAAaE,OAAMD,MAAK,GACxB,WAAWC,OAAM,GAAG,GACpB,GAAG;AACP,UAAI,IAAI,cAAc,UAAU,GAAG;AACjC,YAAI,IAAI,gBAAgB,QAAQ,GAAG;AACjC,iBAAO,cAAc,EAAE,GAAG;AAC1B,iBAAO,gBAAgB,EAAE,KAAK;AAC9B,cAAI,MAAM,GAAG;AACX,cAAE,KAAK,KAAK,GAAG;AACf,qBAAS,EAAE,IAAI;AAAA,UACjB,OAAO;AACL,4BAAgB,EAAE,KAAK,IAAI,cAAc,EAAE,GAAG,IAAI,EAAC,OAAO,EAAE,OAAO,KAAK,EAAE,KAAK,MAAM,EAAE,KAAK,OAAO,EAAE,IAAI,EAAC;AAAA,UAC5G;AAAA,QACF,OAAO;AACL,iBAAO,cAAc,EAAE,GAAG;AAC1B,YAAE,KAAK,KAAK,GAAG;AACf,wBAAc,EAAE,MAAM,QAAQ,IAAI;AAAA,QACpC;AAAA,MACF,WAAW,IAAI,gBAAgB,QAAQ,GAAG;AACxC,YAAI,IAAI,cAAc,UAAU,GAAG;AACjC,iBAAO,gBAAgB,EAAE,KAAK;AAC9B,iBAAO,cAAc,EAAE,GAAG;AAC1B,cAAI,MAAM,GAAG;AACX,cAAE,KAAK,KAAK,GAAG;AACf,qBAAS,EAAE,IAAI;AAAA,UACjB,OAAO;AACL,4BAAgB,EAAE,KAAK,IAAI,cAAc,EAAE,GAAG,IAAI,EAAC,OAAO,EAAE,OAAO,KAAK,EAAE,KAAK,MAAM,EAAE,KAAK,OAAO,EAAE,IAAI,EAAC;AAAA,UAC5G;AAAA,QACF,OAAO;AACL,iBAAO,gBAAgB,EAAE,KAAK;AAC9B,YAAE,KAAK,QAAQD,MAAK;AACpB,0BAAgB,EAAE,QAAQ,UAAU,IAAI;AAAA,QAC1C;AAAA,MACF,OAAO;AACL,wBAAgB,UAAU,IAAI,cAAc,QAAQ,IAAI,EAAC,OAAO,YAAY,KAAK,UAAU,MAAM,CAACA,QAAO,GAAG,EAAC;AAAA,MAC/G;AAAA,IACF;AAAA,EACF;AAEA,WAASC,OAAMC,QAAO;AACpB,WAAOA,OAAM,CAAC,IAAI,IAAIA,OAAM,CAAC,KAAK,KAAK,KAAK;AAAA,EAC9C;AAEA,WAAS,aAAa,MAAM,QAAQ,OAAO;AACzC,SAAK,QAAQ,SAASA,QAAO;AAC3B,UAAIJ,KAAII,OAAM,CAAC,GACXH,KAAIG,OAAM,CAAC,GACX,KAAKJ,KAAI,GACT,KAAKC,KAAI,GACT,KAAK,MAAM,OAAO,KAAK,KAAK,EAAE,CAAC;AACnC,UAAID,KAAI,KAAKA,KAAI,MAAM,OAAOA,IAAG;AAC/B,QAAAI,OAAM,CAAC,IAAI,QAAQJ,IAAG,MAAM,OAAO,KAAK,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,KAAK;AAAA,MAClE;AACA,UAAIC,KAAI,KAAKA,KAAI,MAAM,OAAOA,IAAG;AAC/B,QAAAG,OAAM,CAAC,IAAI,QAAQH,IAAG,MAAM,QAAQ,KAAK,KAAK,KAAK,EAAE,CAAC,GAAG,IAAI,KAAK;AAAA,MACpE;AAAA,IACF,CAAC;AAAA,EACH;AAEA,WAAS,UAAU;AAEnB,WAAS,OAAO,SAAS,GAAG;AAC1B,QAAI,CAAC,UAAU,OAAQ,QAAO,CAAC,IAAI,EAAE;AACrC,QAAI,KAAK,KAAK,MAAM,EAAE,CAAC,CAAC,GAAG,KAAK,KAAK,MAAM,EAAE,CAAC,CAAC;AAC/C,QAAI,EAAE,MAAM,KAAK,MAAM,GAAI,OAAM,IAAI,MAAM,cAAc;AACzD,WAAO,KAAK,IAAI,KAAK,IAAI;AAAA,EAC3B;AAEA,WAAS,aAAa,SAAS,GAAG;AAChC,WAAO,UAAU,UAAUL,aAAY,OAAO,MAAM,aAAa,IAAI,MAAM,QAAQ,CAAC,IAAIS,kBAASC,OAAM,KAAK,CAAC,CAAC,IAAID,kBAAS,CAAC,GAAG,YAAYT;AAAA,EAC7I;AAEA,WAAS,SAAS,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAU,SAAS,IAAI,eAAe,cAAM,YAAY,WAAW;AAAA,EACtF;AAEA,SAAO;AACT;AAGA,SAAS,OAAOI,IAAG;AACjB,SAAO,SAASA,EAAC,IAAIA,KAAI;AAC3B;AAIA,SAAS,MAAMA,IAAG,OAAO;AACvB,SAAOA,MAAK,OAAO,QAAQ,CAACA,MAAK;AACnC;AAGA,SAAS,MAAM,GAAG;AAChB,SAAO,KAAK,QAAQ,MAAM,IAAI,CAAC,CAAC,IAAI,YAAY;AAClD;AAEA,SAAS,QAAQA,IAAG,IAAI,IAAI,OAAO;AACjC,QAAMO,KAAI,QAAQ;AAClB,QAAM,IAAI,KAAK;AACf,QAAM,IAAI,SAASA,EAAC,KAAK,SAAS,CAAC,IAAIA,KAAI,IAAI,KAAK,KAAKA,EAAC,IAAI,KAAK,KAAK,CAAC;AACzE,SAAO,MAAM,CAAC,IAAIP,KAAIA,KAAI,IAAI;AAChC;;;AC3NA,SAAS,SAAS,GAAG;AACnB,SAAO,EAAE,CAAC;AACZ;AAEA,SAAS,SAAS,GAAG;AACnB,SAAO,EAAE,CAAC;AACZ;AAEA,SAAS,gBAAgB;AACvB,SAAO;AACT;AAEe,SAAR,kBAAmB;AACxB,MAAIQ,KAAI,UACJC,KAAI,UACJ,SAAS,eACT,KAAK,KACL,KAAK,KACL,IAAI,IACJ,IAAI,GACJ,IAAI,IAAI,GACR,IAAK,KAAK,IAAI,KAAM,GACpBC,KAAK,KAAK,IAAI,KAAM,GACpBC,aAAYC,kBAAS,EAAE;AAE3B,WAAS,KAAK,MAAM;AAClB,QAAI,SAAS,IAAI,aAAa,IAAIF,EAAC,GAC/B,QAAQ,KAAK,IAAI,GAAG,CAAC,CAAC,GACtB,IAAI;AAER,eAAW,KAAK,MAAM;AACpB,UAAI,MAAMF,GAAE,GAAG,EAAE,GAAG,IAAI,IAAI,KAAK,OAC7B,MAAMC,GAAE,GAAG,GAAG,IAAI,IAAI,KAAK,OAC3B,KAAK,CAAC,OAAO,GAAG,GAAG,IAAI;AAC3B,UAAI,MAAM,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAKC,IAAG;AAChD,YAAI,KAAK,KAAK,MAAM,EAAE,GAClB,KAAK,KAAK,MAAM,EAAE,GAClB,KAAK,KAAK,KAAK,KACf,KAAK,KAAK,KAAK;AACnB,eAAO,KAAK,KAAK,CAAC,MAAM,IAAI,OAAO,IAAI,MAAM;AAC7C,eAAO,KAAK,IAAI,KAAK,CAAC,KAAK,MAAM,IAAI,MAAM;AAC3C,eAAO,KAAK,KAAK,KAAK,KAAK,CAAC,KAAK,KAAK,KAAK;AAC3C,eAAO,MAAM,KAAK,KAAK,CAAC,MAAM,IAAI,MAAM,KAAK;AAAA,MAC/C;AAAA,IACF;AAEA,UAAM,EAAC,MAAM,QAAQ,OAAO,GAAG,QAAQA,GAAC,GAAG,IAAI,KAAK;AACpD,WAAO;AAAA,EACT;AAEA,WAAS,QAAQ,MAAM;AACrB,QAAI,SAAS,KAAK,IAAI,GAClB,KAAKC,WAAU,MAAM,GACrB,QAAQ,KAAK,IAAI,GAAG,IAAI,CAAC;AAG7B,QAAI,CAAC,MAAM,QAAQ,EAAE,GAAG;AACtB,WAAK,MAAM,OAAO,WAAW,IAAI,MAAM,IAAI,OAAO,EAAE;AAAA,IACtD;AAEA,WAAO,iBAAS,EACX,KAAK,CAAC,GAAGD,EAAC,CAAC,EACX,WAAW,GAAG,IAAI,OAAK,IAAI,KAAK,CAAC,EACnC,MAAM,EACJ,IAAI,CAACG,IAAG,OAAOA,GAAE,QAAQ,CAAC,GAAG,CAAC,GAAGC,WAAUD,EAAC,EAAE;AAAA,EACrD;AAEA,UAAQ,WAAW,SAAS,MAAM;AAChC,QAAI,SAAS,KAAK,IAAI,GAClB,WAAW,iBAAS,EAAE,KAAK,CAAC,GAAGH,EAAC,CAAC,GACjC,QAAQ,KAAK,IAAI,GAAG,IAAI,CAAC,GACzB,UAAU,WAAS;AACjB,cAAQ,CAAC;AACT,UAAIG,KAAIC,WAAU,SAAS,QAAQ,QAAQ,QAAQ,KAAK,CAAC;AACzD,MAAAD,GAAE,QAAQ;AACV,aAAOA;AAAA,IACT;AACJ,WAAO,eAAe,SAAS,OAAO,EAAC,KAAK,MAAM,IAAI,MAAM,IAAI,MAAK,CAAC;AACtE,WAAO;AAAA,EACT;AAEA,WAASC,WAAU,UAAU;AAC3B,aAAS,YAAY,QAAQ,gBAAgB;AAC7C,WAAO;AAAA,EACT;AAEA,WAAS,iBAAiB,aAAa;AACrC,gBAAY,QAAQ,aAAa;AAAA,EACnC;AAEA,WAAS,cAAc,aAAa;AAClC,gBAAY,QAAQ,cAAc;AAAA,EACpC;AAGA,WAAS,eAAe,aAAa;AACnC,gBAAY,CAAC,IAAI,YAAY,CAAC,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI;AACnD,gBAAY,CAAC,IAAI,YAAY,CAAC,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI;AAAA,EACrD;AAEA,WAAS,SAAS;AAChB,QAAI,IAAI;AACR,QAAK,KAAK,IAAI,KAAM;AACpB,IAAAJ,KAAK,KAAK,IAAI,KAAM;AACpB,WAAO;AAAA,EACT;AAEA,UAAQ,IAAI,SAAS,GAAG;AACtB,WAAO,UAAU,UAAUF,KAAI,OAAO,MAAM,aAAa,IAAII,kBAAS,CAAC,CAAC,GAAG,WAAWJ;AAAA,EACxF;AAEA,UAAQ,IAAI,SAAS,GAAG;AACtB,WAAO,UAAU,UAAUC,KAAI,OAAO,MAAM,aAAa,IAAIG,kBAAS,CAAC,CAAC,GAAG,WAAWH;AAAA,EACxF;AAEA,UAAQ,SAAS,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,SAAS,OAAO,MAAM,aAAa,IAAIG,kBAAS,CAAC,CAAC,GAAG,WAAW;AAAA,EAC7F;AAEA,UAAQ,OAAO,SAAS,GAAG;AACzB,QAAI,CAAC,UAAU,OAAQ,QAAO,CAAC,IAAI,EAAE;AACrC,QAAI,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC;AACzB,QAAI,EAAE,MAAM,KAAK,MAAM,GAAI,OAAM,IAAI,MAAM,cAAc;AACzD,WAAO,KAAK,IAAI,KAAK,IAAI,OAAO;AAAA,EAClC;AAEA,UAAQ,WAAW,SAAS,GAAG;AAC7B,QAAI,CAAC,UAAU,OAAQ,QAAO,KAAK;AACnC,QAAI,GAAG,IAAI,CAAC,MAAM,GAAI,OAAM,IAAI,MAAM,mBAAmB;AACzD,WAAO,IAAI,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,GAAG,OAAO;AAAA,EACxD;AAEA,UAAQ,aAAa,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAUD,aAAY,OAAO,MAAM,aAAa,IAAI,MAAM,QAAQ,CAAC,IAAIC,kBAASG,OAAM,KAAK,CAAC,CAAC,IAAIH,kBAAS,CAAC,GAAG,WAAWD;AAAA,EAC5I;AAEA,UAAQ,YAAY,SAAS,GAAG;AAC9B,QAAI,CAAC,UAAU,OAAQ,QAAO,KAAK,KAAK,KAAK,IAAI,EAAE;AACnD,QAAI,GAAG,IAAI,CAAC,MAAM,GAAI,OAAM,IAAI,MAAM,mBAAmB;AACzD,WAAO,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,GAAG,OAAO;AAAA,EACxD;AAEA,SAAO;AACT;;;ACpJA,IAAI,MAAM,CAAC;AAAX,IACI,MAAM,CAAC;AADX,IAEI,QAAQ;AAFZ,IAGI,UAAU;AAHd,IAII,SAAS;AAEb,SAAS,gBAAgB,SAAS;AAChC,SAAO,IAAI,SAAS,KAAK,aAAa,QAAQ,IAAI,SAAS,MAAM,GAAG;AAClE,WAAO,KAAK,UAAU,IAAI,IAAI,SAAS,IAAI;AAAA,EAC7C,CAAC,EAAE,KAAK,GAAG,IAAI,GAAG;AACpB;AAEA,SAAS,gBAAgB,SAAS,GAAG;AACnC,MAAI,SAAS,gBAAgB,OAAO;AACpC,SAAO,SAAS,KAAK,GAAG;AACtB,WAAO,EAAE,OAAO,GAAG,GAAG,GAAG,OAAO;AAAA,EAClC;AACF;AAGA,SAAS,aAAa,MAAM;AAC1B,MAAI,YAAY,uBAAO,OAAO,IAAI,GAC9B,UAAU,CAAC;AAEf,OAAK,QAAQ,SAAS,KAAK;AACzB,aAAS,UAAU,KAAK;AACtB,UAAI,EAAE,UAAU,YAAY;AAC1B,gBAAQ,KAAK,UAAU,MAAM,IAAI,MAAM;AAAA,MACzC;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAEA,SAAS,IAAI,OAAO,OAAO;AACzB,MAAI,IAAI,QAAQ,IAAI,SAAS,EAAE;AAC/B,SAAO,SAAS,QAAQ,IAAI,MAAM,QAAQ,SAAS,CAAC,EAAE,KAAK,CAAC,IAAI,IAAI;AACtE;AAEA,SAAS,WAAW,MAAM;AACxB,SAAO,OAAO,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,IAChC,OAAO,OAAO,MAAM,IAAI,MAAM,CAAC,IAC/B,IAAI,MAAM,CAAC;AACjB;AAEA,SAAS,WAAW,MAAM;AACxB,MAAI,QAAQ,KAAK,YAAY,GACzB,UAAU,KAAK,cAAc,GAC7BK,WAAU,KAAK,cAAc,GAC7BC,gBAAe,KAAK,mBAAmB;AAC3C,SAAO,MAAM,IAAI,IAAI,iBACf,WAAW,KAAK,eAAe,GAAG,CAAC,IAAI,MAAM,IAAI,KAAK,YAAY,IAAI,GAAG,CAAC,IAAI,MAAM,IAAI,KAAK,WAAW,GAAG,CAAC,KAC3GA,gBAAe,MAAM,IAAI,OAAO,CAAC,IAAI,MAAM,IAAI,SAAS,CAAC,IAAI,MAAM,IAAID,UAAS,CAAC,IAAI,MAAM,IAAIC,eAAc,CAAC,IAAI,MACnHD,WAAU,MAAM,IAAI,OAAO,CAAC,IAAI,MAAM,IAAI,SAAS,CAAC,IAAI,MAAM,IAAIA,UAAS,CAAC,IAAI,MAChF,WAAW,QAAQ,MAAM,IAAI,OAAO,CAAC,IAAI,MAAM,IAAI,SAAS,CAAC,IAAI,MACjE;AACR;AAEe,SAAR,YAAiB,WAAW;AACjC,MAAI,WAAW,IAAI,OAAO,OAAQ,YAAY,OAAO,GACjD,YAAY,UAAU,WAAW,CAAC;AAEtC,WAAS,MAAM,MAAM,GAAG;AACtB,QAAI,SAAS,SAAS,OAAO,UAAU,MAAM,SAAS,KAAK,GAAG;AAC5D,UAAI,QAAS,QAAO,QAAQ,KAAK,IAAI,CAAC;AACtC,gBAAU,KAAK,UAAU,IAAI,gBAAgB,KAAK,CAAC,IAAI,gBAAgB,GAAG;AAAA,IAC5E,CAAC;AACD,SAAK,UAAU,WAAW,CAAC;AAC3B,WAAO;AAAA,EACT;AAEA,WAAS,UAAU,MAAM,GAAG;AAC1B,QAAI,OAAO,CAAC,GACR,IAAI,KAAK,QACT,IAAI,GACJ,IAAI,GACJ,GACA,MAAM,KAAK,GACX,MAAM;AAGV,QAAI,KAAK,WAAW,IAAI,CAAC,MAAM,QAAS,GAAE;AAC1C,QAAI,KAAK,WAAW,IAAI,CAAC,MAAM,OAAQ,GAAE;AAEzC,aAAS,QAAQ;AACf,UAAI,IAAK,QAAO;AAChB,UAAI,IAAK,QAAO,MAAM,OAAO;AAG7B,UAAI,GAAG,IAAI,GAAGE;AACd,UAAI,KAAK,WAAW,CAAC,MAAM,OAAO;AAChC,eAAO,MAAM,KAAK,KAAK,WAAW,CAAC,MAAM,SAAS,KAAK,WAAW,EAAE,CAAC,MAAM,MAAM;AACjF,aAAK,IAAI,MAAM,EAAG,OAAM;AAAA,kBACdA,KAAI,KAAK,WAAW,GAAG,OAAO,QAAS,OAAM;AAAA,iBAC9CA,OAAM,QAAQ;AAAE,gBAAM;AAAM,cAAI,KAAK,WAAW,CAAC,MAAM,QAAS,GAAE;AAAA,QAAG;AAC9E,eAAO,KAAK,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE,QAAQ,OAAO,GAAI;AAAA,MACrD;AAGA,aAAO,IAAI,GAAG;AACZ,aAAKA,KAAI,KAAK,WAAW,IAAI,GAAG,OAAO,QAAS,OAAM;AAAA,iBAC7CA,OAAM,QAAQ;AAAE,gBAAM;AAAM,cAAI,KAAK,WAAW,CAAC,MAAM,QAAS,GAAE;AAAA,QAAG,WACrEA,OAAM,UAAW;AAC1B,eAAO,KAAK,MAAM,GAAG,CAAC;AAAA,MACxB;AAGA,aAAO,MAAM,MAAM,KAAK,MAAM,GAAG,CAAC;AAAA,IACpC;AAEA,YAAQ,IAAI,MAAM,OAAO,KAAK;AAC5B,UAAI,MAAM,CAAC;AACX,aAAO,MAAM,OAAO,MAAM,IAAK,KAAI,KAAK,CAAC,GAAG,IAAI,MAAM;AACtD,UAAI,MAAM,MAAM,EAAE,KAAK,GAAG,MAAM,KAAM;AACtC,WAAK,KAAK,GAAG;AAAA,IACf;AAEA,WAAO;AAAA,EACT;AAEA,WAAS,cAAc,MAAM,SAAS;AACpC,WAAO,KAAK,IAAI,SAAS,KAAK;AAC5B,aAAO,QAAQ,IAAI,SAAS,QAAQ;AAClC,eAAO,YAAY,IAAI,MAAM,CAAC;AAAA,MAChC,CAAC,EAAE,KAAK,SAAS;AAAA,IACnB,CAAC;AAAA,EACH;AAEA,WAASC,QAAO,MAAM,SAAS;AAC7B,QAAI,WAAW,KAAM,WAAU,aAAa,IAAI;AAChD,WAAO,CAAC,QAAQ,IAAI,WAAW,EAAE,KAAK,SAAS,CAAC,EAAE,OAAO,cAAc,MAAM,OAAO,CAAC,EAAE,KAAK,IAAI;AAAA,EAClG;AAEA,WAAS,WAAW,MAAM,SAAS;AACjC,QAAI,WAAW,KAAM,WAAU,aAAa,IAAI;AAChD,WAAO,cAAc,MAAM,OAAO,EAAE,KAAK,IAAI;AAAA,EAC/C;AAEA,WAAS,WAAW,MAAM;AACxB,WAAO,KAAK,IAAI,SAAS,EAAE,KAAK,IAAI;AAAA,EACtC;AAEA,WAAS,UAAU,KAAK;AACtB,WAAO,IAAI,IAAI,WAAW,EAAE,KAAK,SAAS;AAAA,EAC5C;AAEA,WAAS,YAAY,OAAO;AAC1B,WAAO,SAAS,OAAO,KACjB,iBAAiB,OAAO,WAAW,KAAK,IACxC,SAAS,KAAK,SAAS,EAAE,IAAI,MAAO,MAAM,QAAQ,MAAM,IAAM,IAAI,MAClE;AAAA,EACR;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,QAAQA;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;ACjKA,IAAI,MAAM,YAAI,GAAG;AAEV,IAAI,WAAW,IAAI;AACnB,IAAI,eAAe,IAAI;AACvB,IAAI,YAAY,IAAI;AACpB,IAAI,gBAAgB,IAAI;AACxB,IAAI,gBAAgB,IAAI;AACxB,IAAI,eAAe,IAAI;AACvB,IAAI,iBAAiB,IAAI;;;ACRhC,IAAI,MAAM,YAAI,GAAI;AAEX,IAAI,WAAW,IAAI;AACnB,IAAI,eAAe,IAAI;AACvB,IAAI,YAAY,IAAI;AACpB,IAAI,gBAAgB,IAAI;AACxB,IAAI,gBAAgB,IAAI;AACxB,IAAI,eAAe,IAAI;AACvB,IAAI,iBAAiB,IAAI;;;ACVjB,SAAR,SAA0B,QAAQ;AACvC,WAAS,OAAO,QAAQ;AACtB,QAAI,QAAQ,OAAO,GAAG,EAAE,KAAK,GAAGC,SAAQC;AACxC,QAAI,CAAC,MAAO,SAAQ;AAAA,aACX,UAAU,OAAQ,SAAQ;AAAA,aAC1B,UAAU,QAAS,SAAQ;AAAA,aAC3B,UAAU,MAAO,SAAQ;AAAA,aACzB,CAAC,MAAMD,UAAS,CAAC,KAAK,EAAG,SAAQA;AAAA,aACjCC,KAAI,MAAM,MAAM,6FAA6F,GAAG;AACvH,UAAI,SAAS,CAAC,CAACA,GAAE,CAAC,KAAK,CAACA,GAAE,CAAC,EAAG,SAAQ,MAAM,QAAQ,MAAM,GAAG,EAAE,QAAQ,KAAK,GAAG;AAC/E,cAAQ,IAAI,KAAK,KAAK;AAAA,IACxB,MACK;AACL,WAAO,GAAG,IAAI;AAAA,EAChB;AACA,SAAO;AACT;AAGA,IAAM,SAAQ,oBAAI,KAAK,kBAAkB,GAAE,SAAS,MAAK,oBAAI,KAAK,kBAAkB,GAAE,SAAS;;;ACnB/F,SAAS,aAAa,UAAU;AAC9B,MAAI,CAAC,SAAS,GAAI,OAAM,IAAI,MAAM,SAAS,SAAS,MAAM,SAAS,UAAU;AAC7E,SAAO,SAAS,KAAK;AACvB;AAEe,SAAR,aAAiB,OAAOC,OAAM;AACnC,SAAO,MAAM,OAAOA,KAAI,EAAE,KAAK,YAAY;AAC7C;;;ACPA,SAAS,oBAAoB,UAAU;AACrC,MAAI,CAAC,SAAS,GAAI,OAAM,IAAI,MAAM,SAAS,SAAS,MAAM,SAAS,UAAU;AAC7E,SAAO,SAAS,YAAY;AAC9B;AAEe,SAAR,eAAiB,OAAOC,OAAM;AACnC,SAAO,MAAM,OAAOA,KAAI,EAAE,KAAK,mBAAmB;AACpD;;;ACPA,SAAS,aAAa,UAAU;AAC9B,MAAI,CAAC,SAAS,GAAI,OAAM,IAAI,MAAM,SAAS,SAAS,MAAM,SAAS,UAAU;AAC7E,SAAO,SAAS,KAAK;AACvB;AAEe,SAARC,cAAiB,OAAOC,OAAM;AACnC,SAAO,MAAM,OAAOA,KAAI,EAAE,KAAK,YAAY;AAC7C;;;ACJA,SAAS,SAAS,OAAO;AACvB,SAAO,SAAS,OAAOC,OAAM,KAAK;AAChC,QAAI,UAAU,WAAW,KAAK,OAAOA,UAAS,WAAY,OAAMA,OAAMA,QAAO;AAC7E,WAAOC,cAAK,OAAOD,KAAI,EAAE,KAAK,SAAS,UAAU;AAC/C,aAAO,MAAM,UAAU,GAAG;AAAA,IAC5B,CAAC;AAAA,EACH;AACF;AAEe,SAAR,IAAqB,WAAW,OAAOA,OAAM,KAAK;AACvD,MAAI,UAAU,WAAW,KAAK,OAAOA,UAAS,WAAY,OAAMA,OAAMA,QAAO;AAC7E,MAAIE,UAAS,YAAU,SAAS;AAChC,SAAOD,cAAK,OAAOD,KAAI,EAAE,KAAK,SAAS,UAAU;AAC/C,WAAOE,QAAO,MAAM,UAAU,GAAG;AAAA,EACnC,CAAC;AACH;AAEO,IAAIC,OAAM,SAAS,QAAQ;AAC3B,IAAIC,OAAM,SAAS,QAAQ;;;ACrBnB,SAAR,cAAiB,OAAOC,OAAM;AACnC,SAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAC3C,QAAI,QAAQ,IAAI;AAChB,aAAS,OAAOA,MAAM,OAAM,GAAG,IAAIA,MAAK,GAAG;AAC3C,UAAM,UAAU;AAChB,UAAM,SAAS,WAAW;AAAE,cAAQ,KAAK;AAAA,IAAG;AAC5C,UAAM,MAAM;AAAA,EACd,CAAC;AACH;;;ACRA,SAAS,aAAa,UAAU;AAC9B,MAAI,CAAC,SAAS,GAAI,OAAM,IAAI,MAAM,SAAS,SAAS,MAAM,SAAS,UAAU;AAC7E,MAAI,SAAS,WAAW,OAAO,SAAS,WAAW,IAAK;AACxD,SAAO,SAAS,KAAK;AACvB;AAEe,SAAR,aAAiB,OAAOC,OAAM;AACnC,SAAO,MAAM,OAAOA,KAAI,EAAE,KAAK,YAAY;AAC7C;;;ACNA,SAAS,OAAOC,OAAM;AACpB,SAAO,CAAC,OAAOC,UAASC,cAAK,OAAOD,KAAI,EACrC,KAAK,UAAS,IAAI,YAAW,gBAAgB,MAAMD,KAAI,CAAC;AAC7D;AAEA,IAAO,cAAQ,OAAO,iBAAiB;AAEhC,IAAI,OAAO,OAAO,WAAW;AAE7B,IAAI,MAAM,OAAO,eAAe;;;ACXxB,SAAR,eAAiBG,IAAGC,IAAG;AAC5B,MAAI,OAAO,WAAW;AAEtB,MAAID,MAAK,KAAM,CAAAA,KAAI;AACnB,MAAIC,MAAK,KAAM,CAAAA,KAAI;AAEnB,WAAS,QAAQ;AACf,QAAI,GACA,IAAI,MAAM,QACV,MACA,KAAK,GACL,KAAK;AAET,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,aAAO,MAAM,CAAC,GAAG,MAAM,KAAK,GAAG,MAAM,KAAK;AAAA,IAC5C;AAEA,SAAK,MAAM,KAAK,IAAID,MAAK,UAAU,MAAM,KAAK,IAAIC,MAAK,UAAU,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAClF,aAAO,MAAM,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,KAAK;AAAA,IAC3C;AAAA,EACF;AAEA,QAAM,aAAa,SAAS,GAAG;AAC7B,YAAQ;AAAA,EACV;AAEA,QAAM,IAAI,SAAS,GAAG;AACpB,WAAO,UAAU,UAAUD,KAAI,CAAC,GAAG,SAASA;AAAA,EAC9C;AAEA,QAAM,IAAI,SAAS,GAAG;AACpB,WAAO,UAAU,UAAUC,KAAI,CAAC,GAAG,SAASA;AAAA,EAC9C;AAEA,QAAM,WAAW,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,WAAW,CAAC,GAAG,SAAS;AAAA,EACrD;AAEA,SAAO;AACT;;;ACvCe,SAAR,YAAiB,GAAG;AACzB,QAAMC,KAAI,CAAC,KAAK,GAAG,KAAK,MAAM,CAAC,GAC3BC,KAAI,CAAC,KAAK,GAAG,KAAK,MAAM,CAAC;AAC7B,SAAO,IAAI,KAAK,MAAMD,IAAGC,EAAC,GAAGD,IAAGC,IAAG,CAAC;AACtC;AAEA,SAAS,IAAI,MAAMD,IAAGC,IAAG,GAAG;AAC1B,MAAI,MAAMD,EAAC,KAAK,MAAMC,EAAC,EAAG,QAAO;AAEjC,MAAI,QACA,OAAO,KAAK,OACZ,OAAO,EAAC,MAAM,EAAC,GACf,KAAK,KAAK,KACV,KAAK,KAAK,KACV,KAAK,KAAK,KACV,KAAK,KAAK,KACV,IACA,IACA,IACA,IACAC,QACAC,SACA,GACA;AAGJ,MAAI,CAAC,KAAM,QAAO,KAAK,QAAQ,MAAM;AAGrC,SAAO,KAAK,QAAQ;AAClB,QAAID,SAAQF,OAAM,MAAM,KAAK,MAAM,GAAI,MAAK;AAAA,QAAS,MAAK;AAC1D,QAAIG,UAASF,OAAM,MAAM,KAAK,MAAM,GAAI,MAAK;AAAA,QAAS,MAAK;AAC3D,QAAI,SAAS,MAAM,EAAE,OAAO,KAAK,IAAIE,WAAU,IAAID,MAAK,GAAI,QAAO,OAAO,CAAC,IAAI,MAAM;AAAA,EACvF;AAGA,OAAK,CAAC,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI;AAClC,OAAK,CAAC,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI;AAClC,MAAIF,OAAM,MAAMC,OAAM,GAAI,QAAO,KAAK,OAAO,MAAM,SAAS,OAAO,CAAC,IAAI,OAAO,KAAK,QAAQ,MAAM;AAGlG,KAAG;AACD,aAAS,SAAS,OAAO,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC;AACrE,QAAIC,SAAQF,OAAM,MAAM,KAAK,MAAM,GAAI,MAAK;AAAA,QAAS,MAAK;AAC1D,QAAIG,UAASF,OAAM,MAAM,KAAK,MAAM,GAAI,MAAK;AAAA,QAAS,MAAK;AAAA,EAC7D,UAAU,IAAIE,WAAU,IAAID,aAAY,KAAK,MAAM,OAAO,IAAK,MAAM;AACrE,SAAO,OAAO,CAAC,IAAI,MAAM,OAAO,CAAC,IAAI,MAAM;AAC7C;AAEO,SAAS,OAAO,MAAM;AAC3B,MAAI,GAAG,GAAG,IAAI,KAAK,QACfF,IACAC,IACA,KAAK,IAAI,MAAM,CAAC,GAChB,KAAK,IAAI,MAAM,CAAC,GAChB,KAAK,UACL,KAAK,UACL,KAAK,WACL,KAAK;AAGT,OAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,QAAI,MAAMD,KAAI,CAAC,KAAK,GAAG,KAAK,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,MAAMC,KAAI,CAAC,KAAK,GAAG,KAAK,MAAM,CAAC,CAAC,EAAG;AACtF,OAAG,CAAC,IAAID;AACR,OAAG,CAAC,IAAIC;AACR,QAAID,KAAI,GAAI,MAAKA;AACjB,QAAIA,KAAI,GAAI,MAAKA;AACjB,QAAIC,KAAI,GAAI,MAAKA;AACjB,QAAIA,KAAI,GAAI,MAAKA;AAAA,EACnB;AAGA,MAAI,KAAK,MAAM,KAAK,GAAI,QAAO;AAG/B,OAAK,MAAM,IAAI,EAAE,EAAE,MAAM,IAAI,EAAE;AAG/B,OAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,QAAI,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,EACjC;AAEA,SAAO;AACT;;;ACnFe,SAAR,cAAiBG,IAAGC,IAAG;AAC5B,MAAI,MAAMD,KAAI,CAACA,EAAC,KAAK,MAAMC,KAAI,CAACA,EAAC,EAAG,QAAO;AAE3C,MAAI,KAAK,KAAK,KACV,KAAK,KAAK,KACV,KAAK,KAAK,KACV,KAAK,KAAK;AAKd,MAAI,MAAM,EAAE,GAAG;AACb,UAAM,KAAK,KAAK,MAAMD,EAAC,KAAK;AAC5B,UAAM,KAAK,KAAK,MAAMC,EAAC,KAAK;AAAA,EAC9B,OAGK;AACH,QAAI,IAAI,KAAK,MAAM,GACf,OAAO,KAAK,OACZ,QACA;AAEJ,WAAO,KAAKD,MAAKA,MAAK,MAAM,KAAKC,MAAKA,MAAK,IAAI;AAC7C,WAAKA,KAAI,OAAO,IAAKD,KAAI;AACzB,eAAS,IAAI,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,MAAM,OAAO,QAAQ,KAAK;AAC7D,cAAQ,GAAG;AAAA,QACT,KAAK;AAAG,eAAK,KAAK,GAAG,KAAK,KAAK;AAAG;AAAA,QAClC,KAAK;AAAG,eAAK,KAAK,GAAG,KAAK,KAAK;AAAG;AAAA,QAClC,KAAK;AAAG,eAAK,KAAK,GAAG,KAAK,KAAK;AAAG;AAAA,QAClC,KAAK;AAAG,eAAK,KAAK,GAAG,KAAK,KAAK;AAAG;AAAA,MACpC;AAAA,IACF;AAEA,QAAI,KAAK,SAAS,KAAK,MAAM,OAAQ,MAAK,QAAQ;AAAA,EACpD;AAEA,OAAK,MAAM;AACX,OAAK,MAAM;AACX,OAAK,MAAM;AACX,OAAK,MAAM;AACX,SAAO;AACT;;;AC1Ce,SAAR,eAAmB;AACxB,MAAI,OAAO,CAAC;AACZ,OAAK,MAAM,SAAS,MAAM;AACxB,QAAI,CAAC,KAAK,OAAQ;AAAG,WAAK,KAAK,KAAK,IAAI;AAAA,WAAU,OAAO,KAAK;AAAA,EAChE,CAAC;AACD,SAAO;AACT;;;ACNe,SAARE,gBAAiB,GAAG;AACzB,SAAO,UAAU,SACX,KAAK,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,IACvD,MAAM,KAAK,GAAG,IAAI,SAAY,CAAC,CAAC,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,KAAK,KAAK,KAAK,GAAG,CAAC;AACjF;;;ACJe,SAAR,aAAiB,MAAM,IAAI,IAAI,IAAI,IAAI;AAC5C,OAAK,OAAO;AACZ,OAAK,KAAK;AACV,OAAK,KAAK;AACV,OAAK,KAAK;AACV,OAAK,KAAK;AACZ;;;ACJe,SAAR,aAAiBC,IAAGC,IAAG,QAAQ;AACpC,MAAI,MACA,KAAK,KAAK,KACV,KAAK,KAAK,KACV,IACA,IACAC,KACAC,KACAC,MAAK,KAAK,KACVC,MAAK,KAAK,KACV,QAAQ,CAAC,GACT,OAAO,KAAK,OACZ,GACA;AAEJ,MAAI,KAAM,OAAM,KAAK,IAAI,aAAK,MAAM,IAAI,IAAID,KAAIC,GAAE,CAAC;AACnD,MAAI,UAAU,KAAM,UAAS;AAAA,OACxB;AACH,SAAKL,KAAI,QAAQ,KAAKC,KAAI;AAC1B,IAAAG,MAAKJ,KAAI,QAAQK,MAAKJ,KAAI;AAC1B,cAAU;AAAA,EACZ;AAEA,SAAO,IAAI,MAAM,IAAI,GAAG;AAGtB,QAAI,EAAE,OAAO,EAAE,UACP,KAAK,EAAE,MAAMG,QACb,KAAK,EAAE,MAAMC,QACbH,MAAK,EAAE,MAAM,OACbC,MAAK,EAAE,MAAM,GAAI;AAGzB,QAAI,KAAK,QAAQ;AACf,UAAI,MAAM,KAAKD,OAAM,GACjB,MAAM,KAAKC,OAAM;AAErB,YAAM;AAAA,QACJ,IAAI,aAAK,KAAK,CAAC,GAAG,IAAI,IAAID,KAAIC,GAAE;AAAA,QAChC,IAAI,aAAK,KAAK,CAAC,GAAG,IAAI,IAAI,IAAIA,GAAE;AAAA,QAChC,IAAI,aAAK,KAAK,CAAC,GAAG,IAAI,IAAID,KAAI,EAAE;AAAA,QAChC,IAAI,aAAK,KAAK,CAAC,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,MAClC;AAGA,UAAI,KAAKD,MAAK,OAAO,IAAKD,MAAK,IAAK;AAClC,YAAI,MAAM,MAAM,SAAS,CAAC;AAC1B,cAAM,MAAM,SAAS,CAAC,IAAI,MAAM,MAAM,SAAS,IAAI,CAAC;AACpD,cAAM,MAAM,SAAS,IAAI,CAAC,IAAI;AAAA,MAChC;AAAA,IACF,OAGK;AACH,UAAI,KAAKA,KAAI,CAAC,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,GACtC,KAAKC,KAAI,CAAC,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,GACtC,KAAK,KAAK,KAAK,KAAK;AACxB,UAAI,KAAK,QAAQ;AACf,YAAI,IAAI,KAAK,KAAK,SAAS,EAAE;AAC7B,aAAKD,KAAI,GAAG,KAAKC,KAAI;AACrB,QAAAG,MAAKJ,KAAI,GAAGK,MAAKJ,KAAI;AACrB,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;ACrEe,SAARK,gBAAiB,GAAG;AACzB,MAAI,MAAMC,KAAI,CAAC,KAAK,GAAG,KAAK,MAAM,CAAC,CAAC,KAAK,MAAMC,KAAI,CAAC,KAAK,GAAG,KAAK,MAAM,CAAC,CAAC,EAAG,QAAO;AAEnF,MAAI,QACA,OAAO,KAAK,OACZ,UACA,UACA,MACA,KAAK,KAAK,KACV,KAAK,KAAK,KACV,KAAK,KAAK,KACV,KAAK,KAAK,KACVD,IACAC,IACA,IACA,IACAC,QACAC,SACA,GACA;AAGJ,MAAI,CAAC,KAAM,QAAO;AAIlB,MAAI,KAAK,OAAQ,QAAO,MAAM;AAC5B,QAAID,SAAQF,OAAM,MAAM,KAAK,MAAM,GAAI,MAAK;AAAA,QAAS,MAAK;AAC1D,QAAIG,UAASF,OAAM,MAAM,KAAK,MAAM,GAAI,MAAK;AAAA,QAAS,MAAK;AAC3D,QAAI,EAAE,SAAS,MAAM,OAAO,KAAK,IAAIE,WAAU,IAAID,MAAK,GAAI,QAAO;AACnE,QAAI,CAAC,KAAK,OAAQ;AAClB,QAAI,OAAQ,IAAI,IAAK,CAAC,KAAK,OAAQ,IAAI,IAAK,CAAC,KAAK,OAAQ,IAAI,IAAK,CAAC,EAAG,YAAW,QAAQ,IAAI;AAAA,EAChG;AAGA,SAAO,KAAK,SAAS,EAAG,KAAI,EAAE,WAAW,MAAM,OAAO,KAAK,MAAO,QAAO;AACzE,MAAI,OAAO,KAAK,KAAM,QAAO,KAAK;AAGlC,MAAI,SAAU,QAAQ,OAAO,SAAS,OAAO,OAAO,OAAO,SAAS,MAAO;AAG3E,MAAI,CAAC,OAAQ,QAAO,KAAK,QAAQ,MAAM;AAGvC,SAAO,OAAO,CAAC,IAAI,OAAO,OAAO,OAAO,CAAC;AAGzC,OAAK,OAAO,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC,MACpD,UAAU,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC,MACzD,CAAC,KAAK,QAAQ;AACnB,QAAI,SAAU,UAAS,CAAC,IAAI;AAAA,QACvB,MAAK,QAAQ;AAAA,EACpB;AAEA,SAAO;AACT;AAEO,SAAS,UAAU,MAAM;AAC9B,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,EAAE,EAAG,MAAK,OAAO,KAAK,CAAC,CAAC;AAChE,SAAO;AACT;;;AC7De,SAAR,eAAmB;AACxB,SAAO,KAAK;AACd;;;ACFe,SAAR,eAAmB;AACxB,MAAI,OAAO;AACX,OAAK,MAAM,SAAS,MAAM;AACxB,QAAI,CAAC,KAAK,OAAQ;AAAG,QAAE;AAAA,WAAa,OAAO,KAAK;AAAA,EAClD,CAAC;AACD,SAAO;AACT;;;ACJe,SAAR,cAAiB,UAAU;AAChC,MAAI,QAAQ,CAAC,GAAG,GAAG,OAAO,KAAK,OAAO,OAAO,IAAI,IAAI,IAAI;AACzD,MAAI,KAAM,OAAM,KAAK,IAAI,aAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,CAAC;AAC3E,SAAO,IAAI,MAAM,IAAI,GAAG;AACtB,QAAI,CAAC,SAAS,OAAO,EAAE,MAAM,KAAK,EAAE,IAAI,KAAK,EAAE,IAAI,KAAK,EAAE,IAAI,KAAK,EAAE,EAAE,KAAK,KAAK,QAAQ;AACvF,UAAI,MAAM,KAAK,MAAM,GAAG,MAAM,KAAK,MAAM;AACzC,UAAI,QAAQ,KAAK,CAAC,EAAG,OAAM,KAAK,IAAI,aAAK,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC;AAC/D,UAAI,QAAQ,KAAK,CAAC,EAAG,OAAM,KAAK,IAAI,aAAK,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC;AAC/D,UAAI,QAAQ,KAAK,CAAC,EAAG,OAAM,KAAK,IAAI,aAAK,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC;AAC/D,UAAI,QAAQ,KAAK,CAAC,EAAG,OAAM,KAAK,IAAI,aAAK,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC;AAAA,IACjE;AAAA,EACF;AACA,SAAO;AACT;;;ACbe,SAAR,mBAAiB,UAAU;AAChC,MAAI,QAAQ,CAAC,GAAG,OAAO,CAAC,GAAG;AAC3B,MAAI,KAAK,MAAO,OAAM,KAAK,IAAI,aAAK,KAAK,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,CAAC;AACvF,SAAO,IAAI,MAAM,IAAI,GAAG;AACtB,QAAI,OAAO,EAAE;AACb,QAAI,KAAK,QAAQ;AACf,UAAI,OAAO,KAAK,EAAE,IAAI,KAAK,EAAE,IAAI,KAAK,EAAE,IAAI,KAAK,EAAE,IAAI,MAAM,KAAK,MAAM,GAAG,MAAM,KAAK,MAAM;AAC5F,UAAI,QAAQ,KAAK,CAAC,EAAG,OAAM,KAAK,IAAI,aAAK,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC;AAC/D,UAAI,QAAQ,KAAK,CAAC,EAAG,OAAM,KAAK,IAAI,aAAK,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC;AAC/D,UAAI,QAAQ,KAAK,CAAC,EAAG,OAAM,KAAK,IAAI,aAAK,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC;AAC/D,UAAI,QAAQ,KAAK,CAAC,EAAG,OAAM,KAAK,IAAI,aAAK,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC;AAAA,IACjE;AACA,SAAK,KAAK,CAAC;AAAA,EACb;AACA,SAAO,IAAI,KAAK,IAAI,GAAG;AACrB,aAAS,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE;AAAA,EACzC;AACA,SAAO;AACT;;;ACpBO,SAASE,UAAS,GAAG;AAC1B,SAAO,EAAE,CAAC;AACZ;AAEe,SAAR,UAAiB,GAAG;AACzB,SAAO,UAAU,UAAU,KAAK,KAAK,GAAG,QAAQ,KAAK;AACvD;;;ACNO,SAASC,UAAS,GAAG;AAC1B,SAAO,EAAE,CAAC;AACZ;AAEe,SAAR,UAAiB,GAAG;AACzB,SAAO,UAAU,UAAU,KAAK,KAAK,GAAG,QAAQ,KAAK;AACvD;;;ACOe,SAAR,SAA0B,OAAOC,IAAGC,IAAG;AAC5C,MAAI,OAAO,IAAI,SAASD,MAAK,OAAOE,YAAWF,IAAGC,MAAK,OAAOE,YAAWF,IAAG,KAAK,KAAK,KAAK,GAAG;AAC9F,SAAO,SAAS,OAAO,OAAO,KAAK,OAAO,KAAK;AACjD;AAEA,SAAS,SAASD,IAAGC,IAAG,IAAI,IAAI,IAAI,IAAI;AACtC,OAAK,KAAKD;AACV,OAAK,KAAKC;AACV,OAAK,MAAM;AACX,OAAK,MAAM;AACX,OAAK,MAAM;AACX,OAAK,MAAM;AACX,OAAK,QAAQ;AACf;AAEA,SAAS,UAAU,MAAM;AACvB,MAAI,OAAO,EAAC,MAAM,KAAK,KAAI,GAAG,OAAO;AACrC,SAAO,OAAO,KAAK,KAAM,QAAO,KAAK,OAAO,EAAC,MAAM,KAAK,KAAI;AAC5D,SAAO;AACT;AAEA,IAAI,YAAY,SAAS,YAAY,SAAS;AAE9C,UAAU,OAAO,WAAW;AAC1B,MAAI,OAAO,IAAI,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAC5E,OAAO,KAAK,OACZ,OACA;AAEJ,MAAI,CAAC,KAAM,QAAO;AAElB,MAAI,CAAC,KAAK,OAAQ,QAAO,KAAK,QAAQ,UAAU,IAAI,GAAG;AAEvD,UAAQ,CAAC,EAAC,QAAQ,MAAM,QAAQ,KAAK,QAAQ,IAAI,MAAM,CAAC,EAAC,CAAC;AAC1D,SAAO,OAAO,MAAM,IAAI,GAAG;AACzB,aAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,UAAI,QAAQ,KAAK,OAAO,CAAC,GAAG;AAC1B,YAAI,MAAM,OAAQ,OAAM,KAAK,EAAC,QAAQ,OAAO,QAAQ,KAAK,OAAO,CAAC,IAAI,IAAI,MAAM,CAAC,EAAC,CAAC;AAAA,YAC9E,MAAK,OAAO,CAAC,IAAI,UAAU,KAAK;AAAA,MACvC;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAEA,UAAU,MAAM;AAChB,UAAU,SAAS;AACnB,UAAU,QAAQ;AAClB,UAAU,OAAO;AACjB,UAAU,SAASG;AACnB,UAAU,OAAO;AACjB,UAAU,SAASC;AACnB,UAAU,YAAY;AACtB,UAAU,OAAO;AACjB,UAAU,OAAO;AACjB,UAAU,QAAQ;AAClB,UAAU,aAAa;AACvB,UAAU,IAAI;AACd,UAAU,IAAI;;;ACxEC,SAARC,kBAAiBC,IAAG;AACzB,SAAO,WAAW;AAChB,WAAOA;AAAA,EACT;AACF;;;ACJe,SAAR,eAAiB,QAAQ;AAC9B,UAAQ,OAAO,IAAI,OAAO;AAC5B;;;ACEA,SAAS,EAAE,GAAG;AACZ,SAAO,EAAE,IAAI,EAAE;AACjB;AAEA,SAAS,EAAE,GAAG;AACZ,SAAO,EAAE,IAAI,EAAE;AACjB;AAEe,SAAR,gBAAiB,QAAQ;AAC9B,MAAI,OACA,OACA,QACA,WAAW,GACX,aAAa;AAEjB,MAAI,OAAO,WAAW,WAAY,UAASC,kBAAS,UAAU,OAAO,IAAI,CAAC,MAAM;AAEhF,WAAS,QAAQ;AACf,QAAI,GAAG,IAAI,MAAM,QACb,MACA,MACA,IACA,IACA,IACA;AAEJ,aAAS,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AACnC,aAAO,SAAS,OAAO,GAAG,CAAC,EAAE,WAAW,OAAO;AAC/C,WAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,eAAO,MAAM,CAAC;AACd,aAAK,MAAM,KAAK,KAAK,GAAG,MAAM,KAAK;AACnC,aAAK,KAAK,IAAI,KAAK;AACnB,aAAK,KAAK,IAAI,KAAK;AACnB,aAAK,MAAM,KAAK;AAAA,MAClB;AAAA,IACF;AAEA,aAAS,MAAM,MAAM,IAAI,IAAI,IAAI,IAAI;AACnC,UAAI,OAAO,KAAK,MAAM,KAAK,KAAK,GAAG,IAAI,KAAK;AAC5C,UAAI,MAAM;AACR,YAAI,KAAK,QAAQ,KAAK,OAAO;AAC3B,cAAIC,KAAI,KAAK,KAAK,IAAI,KAAK,IACvBC,KAAI,KAAK,KAAK,IAAI,KAAK,IACvB,IAAID,KAAIA,KAAIC,KAAIA;AACpB,cAAI,IAAI,IAAI,GAAG;AACb,gBAAID,OAAM,EAAG,CAAAA,KAAI,eAAO,MAAM,GAAG,KAAKA,KAAIA;AAC1C,gBAAIC,OAAM,EAAG,CAAAA,KAAI,eAAO,MAAM,GAAG,KAAKA,KAAIA;AAC1C,iBAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,IAAI;AACnC,iBAAK,OAAOD,MAAK,MAAM,KAAK,MAAM,OAAO,MAAM;AAC/C,iBAAK,OAAOC,MAAK,KAAK;AACtB,iBAAK,MAAMD,MAAK,IAAI,IAAI;AACxB,iBAAK,MAAMC,KAAI;AAAA,UACjB;AAAA,QACF;AACA;AAAA,MACF;AACA,aAAO,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAAA,IAChE;AAAA,EACF;AAEA,WAAS,QAAQ,MAAM;AACrB,QAAI,KAAK,KAAM,QAAO,KAAK,IAAI,MAAM,KAAK,KAAK,KAAK;AACpD,aAAS,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACnC,UAAI,KAAK,CAAC,KAAK,KAAK,CAAC,EAAE,IAAI,KAAK,GAAG;AACjC,aAAK,IAAI,KAAK,CAAC,EAAE;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAEA,WAAS,aAAa;AACpB,QAAI,CAAC,MAAO;AACZ,QAAI,GAAG,IAAI,MAAM,QAAQ;AACzB,YAAQ,IAAI,MAAM,CAAC;AACnB,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,QAAO,MAAM,CAAC,GAAG,MAAM,KAAK,KAAK,IAAI,CAAC,OAAO,MAAM,GAAG,KAAK;AAAA,EACrF;AAEA,QAAM,aAAa,SAAS,QAAQ,SAAS;AAC3C,YAAQ;AACR,aAAS;AACT,eAAW;AAAA,EACb;AAEA,QAAM,aAAa,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,aAAa,CAAC,GAAG,SAAS;AAAA,EACvD;AAEA,QAAM,WAAW,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,WAAW,CAAC,GAAG,SAAS;AAAA,EACrD;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,SAAS,OAAO,MAAM,aAAa,IAAIF,kBAAS,CAAC,CAAC,GAAG,WAAW,GAAG,SAAS;AAAA,EACzG;AAEA,SAAO;AACT;;;AChGA,SAASG,OAAM,GAAG;AAChB,SAAO,EAAE;AACX;AAEA,SAAS,KAAK,UAAU,QAAQ;AAC9B,MAAI,OAAO,SAAS,IAAI,MAAM;AAC9B,MAAI,CAAC,KAAM,OAAM,IAAI,MAAM,qBAAqB,MAAM;AACtD,SAAO;AACT;AAEe,SAAR,aAAiB,OAAO;AAC7B,MAAIC,MAAKD,QACL,WAAW,iBACX,WACA,WAAWE,kBAAS,EAAE,GACtB,WACA,OACAC,QACA,MACA,QACA,aAAa;AAEjB,MAAI,SAAS,KAAM,SAAQ,CAAC;AAE5B,WAAS,gBAAgBC,OAAM;AAC7B,WAAO,IAAI,KAAK,IAAID,OAAMC,MAAK,OAAO,KAAK,GAAGD,OAAMC,MAAK,OAAO,KAAK,CAAC;AAAA,EACxE;AAEA,WAAS,MAAM,OAAO;AACpB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,YAAY,EAAE,GAAG;AACrD,eAAS,IAAI,GAAGA,OAAM,QAAQ,QAAQC,IAAGC,IAAG,GAAG,GAAG,IAAI,GAAG,EAAE,GAAG;AAC5D,QAAAF,QAAO,MAAM,CAAC,GAAG,SAASA,MAAK,QAAQ,SAASA,MAAK;AACrD,QAAAC,KAAI,OAAO,IAAI,OAAO,KAAK,OAAO,IAAI,OAAO,MAAM,eAAO,MAAM;AAChE,QAAAC,KAAI,OAAO,IAAI,OAAO,KAAK,OAAO,IAAI,OAAO,MAAM,eAAO,MAAM;AAChE,YAAI,KAAK,KAAKD,KAAIA,KAAIC,KAAIA,EAAC;AAC3B,aAAK,IAAI,UAAU,CAAC,KAAK,IAAI,QAAQ,UAAU,CAAC;AAChD,QAAAD,MAAK,GAAGC,MAAK;AACb,eAAO,MAAMD,MAAK,IAAI,KAAK,CAAC;AAC5B,eAAO,MAAMC,KAAI;AACjB,eAAO,MAAMD,MAAK,IAAI,IAAI;AAC1B,eAAO,MAAMC,KAAI;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAEA,WAAS,aAAa;AACpB,QAAI,CAAC,MAAO;AAEZ,QAAI,GACA,IAAI,MAAM,QACVC,KAAI,MAAM,QACV,WAAW,IAAI,IAAI,MAAM,IAAI,CAAC,GAAGC,OAAM,CAACP,IAAG,GAAGO,IAAG,KAAK,GAAG,CAAC,CAAC,CAAC,GAC5DJ;AAEJ,SAAK,IAAI,GAAGD,SAAQ,IAAI,MAAM,CAAC,GAAG,IAAII,IAAG,EAAE,GAAG;AAC5C,MAAAH,QAAO,MAAM,CAAC,GAAGA,MAAK,QAAQ;AAC9B,UAAI,OAAOA,MAAK,WAAW,SAAU,CAAAA,MAAK,SAAS,KAAK,UAAUA,MAAK,MAAM;AAC7E,UAAI,OAAOA,MAAK,WAAW,SAAU,CAAAA,MAAK,SAAS,KAAK,UAAUA,MAAK,MAAM;AAC7E,MAAAD,OAAMC,MAAK,OAAO,KAAK,KAAKD,OAAMC,MAAK,OAAO,KAAK,KAAK,KAAK;AAC7D,MAAAD,OAAMC,MAAK,OAAO,KAAK,KAAKD,OAAMC,MAAK,OAAO,KAAK,KAAK,KAAK;AAAA,IAC/D;AAEA,SAAK,IAAI,GAAG,OAAO,IAAI,MAAMG,EAAC,GAAG,IAAIA,IAAG,EAAE,GAAG;AAC3C,MAAAH,QAAO,MAAM,CAAC,GAAG,KAAK,CAAC,IAAID,OAAMC,MAAK,OAAO,KAAK,KAAKD,OAAMC,MAAK,OAAO,KAAK,IAAID,OAAMC,MAAK,OAAO,KAAK;AAAA,IAC3G;AAEA,gBAAY,IAAI,MAAMG,EAAC,GAAG,mBAAmB;AAC7C,gBAAY,IAAI,MAAMA,EAAC,GAAG,mBAAmB;AAAA,EAC/C;AAEA,WAAS,qBAAqB;AAC5B,QAAI,CAAC,MAAO;AAEZ,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC5C,gBAAU,CAAC,IAAI,CAAC,SAAS,MAAM,CAAC,GAAG,GAAG,KAAK;AAAA,IAC7C;AAAA,EACF;AAEA,WAAS,qBAAqB;AAC5B,QAAI,CAAC,MAAO;AAEZ,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC5C,gBAAU,CAAC,IAAI,CAAC,SAAS,MAAM,CAAC,GAAG,GAAG,KAAK;AAAA,IAC7C;AAAA,EACF;AAEA,QAAM,aAAa,SAAS,QAAQ,SAAS;AAC3C,YAAQ;AACR,aAAS;AACT,eAAW;AAAA,EACb;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,QAAQ,GAAG,WAAW,GAAG,SAAS;AAAA,EAC/D;AAEA,QAAM,KAAK,SAAS,GAAG;AACrB,WAAO,UAAU,UAAUN,MAAK,GAAG,SAASA;AAAA,EAC9C;AAEA,QAAM,aAAa,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,aAAa,CAAC,GAAG,SAAS;AAAA,EACvD;AAEA,QAAM,WAAW,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,WAAW,OAAO,MAAM,aAAa,IAAIC,kBAAS,CAAC,CAAC,GAAG,mBAAmB,GAAG,SAAS;AAAA,EACnH;AAEA,QAAM,WAAW,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,WAAW,OAAO,MAAM,aAAa,IAAIA,kBAAS,CAAC,CAAC,GAAG,mBAAmB,GAAG,SAAS;AAAA,EACnH;AAEA,SAAO;AACT;;;ACnHA,IAAM,IAAI;AACV,IAAM,IAAI;AACV,IAAM,IAAI;AAEK,SAAR,cAAmB;AACxB,MAAI,IAAI;AACR,SAAO,OAAO,KAAK,IAAI,IAAI,KAAK,KAAK;AACvC;;;ACJO,SAASO,GAAE,GAAG;AACnB,SAAO,EAAE;AACX;AAEO,SAASC,GAAE,GAAG;AACnB,SAAO,EAAE;AACX;AAEA,IAAI,gBAAgB;AAApB,IACI,eAAe,KAAK,MAAM,IAAI,KAAK,KAAK,CAAC;AAE9B,SAAR,mBAAiB,OAAO;AAC7B,MAAI,YACA,QAAQ,GACR,WAAW,MACX,aAAa,IAAI,KAAK,IAAI,UAAU,IAAI,GAAG,GAC3C,cAAc,GACd,gBAAgB,KAChB,SAAS,oBAAI,IAAI,GACjB,UAAU,MAAM,IAAI,GACpB,QAAQ,iBAAS,QAAQ,KAAK,GAC9B,SAAS,YAAI;AAEjB,MAAI,SAAS,KAAM,SAAQ,CAAC;AAE5B,WAAS,OAAO;AACd,SAAK;AACL,UAAM,KAAK,QAAQ,UAAU;AAC7B,QAAI,QAAQ,UAAU;AACpB,cAAQ,KAAK;AACb,YAAM,KAAK,OAAO,UAAU;AAAA,IAC9B;AAAA,EACF;AAEA,WAAS,KAAK,YAAY;AACxB,QAAI,GAAG,IAAI,MAAM,QAAQ;AAEzB,QAAI,eAAe,OAAW,cAAa;AAE3C,aAAS,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AACnC,gBAAU,cAAc,SAAS;AAEjC,aAAO,QAAQ,SAAS,OAAO;AAC7B,cAAM,KAAK;AAAA,MACb,CAAC;AAED,WAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,eAAO,MAAM,CAAC;AACd,YAAI,KAAK,MAAM,KAAM,MAAK,KAAK,KAAK,MAAM;AAAA,YACrC,MAAK,IAAI,KAAK,IAAI,KAAK,KAAK;AACjC,YAAI,KAAK,MAAM,KAAM,MAAK,KAAK,KAAK,MAAM;AAAA,YACrC,MAAK,IAAI,KAAK,IAAI,KAAK,KAAK;AAAA,MACnC;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,WAAS,kBAAkB;AACzB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,EAAE,GAAG;AAClD,aAAO,MAAM,CAAC,GAAG,KAAK,QAAQ;AAC9B,UAAI,KAAK,MAAM,KAAM,MAAK,IAAI,KAAK;AACnC,UAAI,KAAK,MAAM,KAAM,MAAK,IAAI,KAAK;AACnC,UAAI,MAAM,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,GAAG;AAClC,YAAI,SAAS,gBAAgB,KAAK,KAAK,MAAM,CAAC,GAAG,QAAQ,IAAI;AAC7D,aAAK,IAAI,SAAS,KAAK,IAAI,KAAK;AAChC,aAAK,IAAI,SAAS,KAAK,IAAI,KAAK;AAAA,MAClC;AACA,UAAI,MAAM,KAAK,EAAE,KAAK,MAAM,KAAK,EAAE,GAAG;AACpC,aAAK,KAAK,KAAK,KAAK;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AAEA,WAAS,gBAAgB,OAAO;AAC9B,QAAI,MAAM,WAAY,OAAM,WAAW,OAAO,MAAM;AACpD,WAAO;AAAA,EACT;AAEA,kBAAgB;AAEhB,SAAO,aAAa;AAAA,IAClB;AAAA,IAEA,SAAS,WAAW;AAClB,aAAO,QAAQ,QAAQ,IAAI,GAAG;AAAA,IAChC;AAAA,IAEA,MAAM,WAAW;AACf,aAAO,QAAQ,KAAK,GAAG;AAAA,IACzB;AAAA,IAEA,OAAO,SAAS,GAAG;AACjB,aAAO,UAAU,UAAU,QAAQ,GAAG,gBAAgB,GAAG,OAAO,QAAQ,eAAe,GAAG,cAAc;AAAA,IAC1G;AAAA,IAEA,OAAO,SAAS,GAAG;AACjB,aAAO,UAAU,UAAU,QAAQ,CAAC,GAAG,cAAc;AAAA,IACvD;AAAA,IAEA,UAAU,SAAS,GAAG;AACpB,aAAO,UAAU,UAAU,WAAW,CAAC,GAAG,cAAc;AAAA,IAC1D;AAAA,IAEA,YAAY,SAAS,GAAG;AACtB,aAAO,UAAU,UAAU,aAAa,CAAC,GAAG,cAAc,CAAC;AAAA,IAC7D;AAAA,IAEA,aAAa,SAAS,GAAG;AACvB,aAAO,UAAU,UAAU,cAAc,CAAC,GAAG,cAAc;AAAA,IAC7D;AAAA,IAEA,eAAe,SAAS,GAAG;AACzB,aAAO,UAAU,UAAU,gBAAgB,IAAI,GAAG,cAAc,IAAI;AAAA,IACtE;AAAA,IAEA,cAAc,SAAS,GAAG;AACxB,aAAO,UAAU,UAAU,SAAS,GAAG,OAAO,QAAQ,eAAe,GAAG,cAAc;AAAA,IACxF;AAAA,IAEA,OAAO,SAAS,MAAM,GAAG;AACvB,aAAO,UAAU,SAAS,KAAM,KAAK,OAAO,OAAO,OAAO,IAAI,IAAI,OAAO,IAAI,MAAM,gBAAgB,CAAC,CAAC,GAAI,cAAc,OAAO,IAAI,IAAI;AAAA,IACxI;AAAA,IAEA,MAAM,SAASD,IAAGC,IAAG,QAAQ;AAC3B,UAAI,IAAI,GACJ,IAAI,MAAM,QACV,IACA,IACA,IACA,MACA;AAEJ,UAAI,UAAU,KAAM,UAAS;AAAA,UACxB,WAAU;AAEf,WAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,eAAO,MAAM,CAAC;AACd,aAAKD,KAAI,KAAK;AACd,aAAKC,KAAI,KAAK;AACd,aAAK,KAAK,KAAK,KAAK;AACpB,YAAI,KAAK,OAAQ,WAAU,MAAM,SAAS;AAAA,MAC5C;AAEA,aAAO;AAAA,IACT;AAAA,IAEA,IAAI,SAAS,MAAM,GAAG;AACpB,aAAO,UAAU,SAAS,KAAK,MAAM,GAAG,MAAM,CAAC,GAAG,cAAc,MAAM,GAAG,IAAI;AAAA,IAC/E;AAAA,EACF;AACF;;;ACtJe,SAAR,mBAAmB;AACxB,MAAI,OACA,MACA,QACA,OACA,WAAWC,kBAAS,GAAG,GACvB,WACA,eAAe,GACf,eAAe,UACf,SAAS;AAEb,WAAS,MAAM,GAAG;AAChB,QAAI,GAAG,IAAI,MAAM,QAAQ,OAAO,SAAS,OAAOC,IAAGC,EAAC,EAAE,WAAW,UAAU;AAC3E,SAAK,QAAQ,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,QAAO,MAAM,CAAC,GAAG,KAAK,MAAM,KAAK;AAAA,EACtE;AAEA,WAAS,aAAa;AACpB,QAAI,CAAC,MAAO;AACZ,QAAI,GAAG,IAAI,MAAM,QAAQC;AACzB,gBAAY,IAAI,MAAM,CAAC;AACvB,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,CAAAA,QAAO,MAAM,CAAC,GAAG,UAAUA,MAAK,KAAK,IAAI,CAAC,SAASA,OAAM,GAAG,KAAK;AAAA,EAC3F;AAEA,WAAS,WAAW,MAAM;AACxB,QAAIC,YAAW,GAAG,GAAGC,IAAG,SAAS,GAAGJ,IAAGC,IAAG;AAG1C,QAAI,KAAK,QAAQ;AACf,WAAKD,KAAIC,KAAI,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC9B,aAAK,IAAI,KAAK,CAAC,OAAOG,KAAI,KAAK,IAAI,EAAE,KAAK,IAAI;AAC5C,UAAAD,aAAY,EAAE,OAAO,UAAUC,IAAGJ,MAAKI,KAAI,EAAE,GAAGH,MAAKG,KAAI,EAAE;AAAA,QAC7D;AAAA,MACF;AACA,WAAK,IAAIJ,KAAI;AACb,WAAK,IAAIC,KAAI;AAAA,IACf,OAGK;AACH,UAAI;AACJ,QAAE,IAAI,EAAE,KAAK;AACb,QAAE,IAAI,EAAE,KAAK;AACb;AAAG,QAAAE,aAAY,UAAU,EAAE,KAAK,KAAK;AAAA,aAC9B,IAAI,EAAE;AAAA,IACf;AAEA,SAAK,QAAQA;AAAA,EACf;AAEA,WAAS,MAAM,MAAM,IAAI,GAAGE,KAAI;AAC9B,QAAI,CAAC,KAAK,MAAO,QAAO;AAExB,QAAIL,KAAI,KAAK,IAAI,KAAK,GAClBC,KAAI,KAAK,IAAI,KAAK,GAClB,IAAII,MAAK,IACT,IAAIL,KAAIA,KAAIC,KAAIA;AAIpB,QAAI,IAAI,IAAI,SAAS,GAAG;AACtB,UAAI,IAAI,cAAc;AACpB,YAAID,OAAM,EAAG,CAAAA,KAAI,eAAO,MAAM,GAAG,KAAKA,KAAIA;AAC1C,YAAIC,OAAM,EAAG,CAAAA,KAAI,eAAO,MAAM,GAAG,KAAKA,KAAIA;AAC1C,YAAI,IAAI,aAAc,KAAI,KAAK,KAAK,eAAe,CAAC;AACpD,aAAK,MAAMD,KAAI,KAAK,QAAQ,QAAQ;AACpC,aAAK,MAAMC,KAAI,KAAK,QAAQ,QAAQ;AAAA,MACtC;AACA,aAAO;AAAA,IACT,WAGS,KAAK,UAAU,KAAK,aAAc;AAG3C,QAAI,KAAK,SAAS,QAAQ,KAAK,MAAM;AACnC,UAAID,OAAM,EAAG,CAAAA,KAAI,eAAO,MAAM,GAAG,KAAKA,KAAIA;AAC1C,UAAIC,OAAM,EAAG,CAAAA,KAAI,eAAO,MAAM,GAAG,KAAKA,KAAIA;AAC1C,UAAI,IAAI,aAAc,KAAI,KAAK,KAAK,eAAe,CAAC;AAAA,IACtD;AAEA;AAAG,UAAI,KAAK,SAAS,MAAM;AACzB,YAAI,UAAU,KAAK,KAAK,KAAK,IAAI,QAAQ;AACzC,aAAK,MAAMD,KAAI;AACf,aAAK,MAAMC,KAAI;AAAA,MACjB;AAAA,WAAS,OAAO,KAAK;AAAA,EACvB;AAEA,QAAM,aAAa,SAAS,QAAQ,SAAS;AAC3C,YAAQ;AACR,aAAS;AACT,eAAW;AAAA,EACb;AAEA,QAAM,WAAW,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,WAAW,OAAO,MAAM,aAAa,IAAIF,kBAAS,CAAC,CAAC,GAAG,WAAW,GAAG,SAAS;AAAA,EAC3G;AAEA,QAAM,cAAc,SAAS,GAAG;AAC9B,WAAO,UAAU,UAAU,eAAe,IAAI,GAAG,SAAS,KAAK,KAAK,YAAY;AAAA,EAClF;AAEA,QAAM,cAAc,SAAS,GAAG;AAC9B,WAAO,UAAU,UAAU,eAAe,IAAI,GAAG,SAAS,KAAK,KAAK,YAAY;AAAA,EAClF;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,SAAS,IAAI,GAAG,SAAS,KAAK,KAAK,MAAM;AAAA,EACtE;AAEA,SAAO;AACT;;;ACjHe,SAAR,eAAiB,QAAQO,IAAGC,IAAG;AACpC,MAAI,OACA,WAAWC,kBAAS,GAAG,GACvB,WACA;AAEJ,MAAI,OAAO,WAAW,WAAY,UAASA,kBAAS,CAAC,MAAM;AAC3D,MAAIF,MAAK,KAAM,CAAAA,KAAI;AACnB,MAAIC,MAAK,KAAM,CAAAA,KAAI;AAEnB,WAAS,MAAM,OAAO;AACpB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC5C,UAAI,OAAO,MAAM,CAAC,GACd,KAAK,KAAK,IAAID,MAAK,MACnB,KAAK,KAAK,IAAIC,MAAK,MACnB,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,GAC/B,KAAK,SAAS,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI,QAAQ;AACnD,WAAK,MAAM,KAAK;AAChB,WAAK,MAAM,KAAK;AAAA,IAClB;AAAA,EACF;AAEA,WAAS,aAAa;AACpB,QAAI,CAAC,MAAO;AACZ,QAAI,GAAG,IAAI,MAAM;AACjB,gBAAY,IAAI,MAAM,CAAC;AACvB,eAAW,IAAI,MAAM,CAAC;AACtB,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,eAAS,CAAC,IAAI,CAAC,OAAO,MAAM,CAAC,GAAG,GAAG,KAAK;AACxC,gBAAU,CAAC,IAAI,MAAM,SAAS,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,MAAM,CAAC,GAAG,GAAG,KAAK;AAAA,IACtE;AAAA,EACF;AAEA,QAAM,aAAa,SAAS,GAAG;AAC7B,YAAQ,GAAG,WAAW;AAAA,EACxB;AAEA,QAAM,WAAW,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,WAAW,OAAO,MAAM,aAAa,IAAIC,kBAAS,CAAC,CAAC,GAAG,WAAW,GAAG,SAAS;AAAA,EAC3G;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,SAAS,OAAO,MAAM,aAAa,IAAIA,kBAAS,CAAC,CAAC,GAAG,WAAW,GAAG,SAAS;AAAA,EACzG;AAEA,QAAM,IAAI,SAAS,GAAG;AACpB,WAAO,UAAU,UAAUF,KAAI,CAAC,GAAG,SAASA;AAAA,EAC9C;AAEA,QAAM,IAAI,SAAS,GAAG;AACpB,WAAO,UAAU,UAAUC,KAAI,CAAC,GAAG,SAASA;AAAA,EAC9C;AAEA,SAAO;AACT;;;ACtDe,SAARE,WAAiBC,IAAG;AACzB,MAAI,WAAWC,kBAAS,GAAG,GACvB,OACA,WACA;AAEJ,MAAI,OAAOD,OAAM,WAAY,CAAAA,KAAIC,kBAASD,MAAK,OAAO,IAAI,CAACA,EAAC;AAE5D,WAAS,MAAM,OAAO;AACpB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,EAAE,GAAG;AAClD,aAAO,MAAM,CAAC,GAAG,KAAK,OAAO,GAAG,CAAC,IAAI,KAAK,KAAK,UAAU,CAAC,IAAI;AAAA,IAChE;AAAA,EACF;AAEA,WAAS,aAAa;AACpB,QAAI,CAAC,MAAO;AACZ,QAAI,GAAG,IAAI,MAAM;AACjB,gBAAY,IAAI,MAAM,CAAC;AACvB,SAAK,IAAI,MAAM,CAAC;AAChB,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,gBAAU,CAAC,IAAI,MAAM,GAAG,CAAC,IAAI,CAACA,GAAE,MAAM,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,IAAI,CAAC,SAAS,MAAM,CAAC,GAAG,GAAG,KAAK;AAAA,IACzF;AAAA,EACF;AAEA,QAAM,aAAa,SAAS,GAAG;AAC7B,YAAQ;AACR,eAAW;AAAA,EACb;AAEA,QAAM,WAAW,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,WAAW,OAAO,MAAM,aAAa,IAAIC,kBAAS,CAAC,CAAC,GAAG,WAAW,GAAG,SAAS;AAAA,EAC3G;AAEA,QAAM,IAAI,SAAS,GAAG;AACpB,WAAO,UAAU,UAAUD,KAAI,OAAO,MAAM,aAAa,IAAIC,kBAAS,CAAC,CAAC,GAAG,WAAW,GAAG,SAASD;AAAA,EACpG;AAEA,SAAO;AACT;;;ACtCe,SAARE,WAAiBC,IAAG;AACzB,MAAI,WAAWC,kBAAS,GAAG,GACvB,OACA,WACA;AAEJ,MAAI,OAAOD,OAAM,WAAY,CAAAA,KAAIC,kBAASD,MAAK,OAAO,IAAI,CAACA,EAAC;AAE5D,WAAS,MAAM,OAAO;AACpB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,EAAE,GAAG;AAClD,aAAO,MAAM,CAAC,GAAG,KAAK,OAAO,GAAG,CAAC,IAAI,KAAK,KAAK,UAAU,CAAC,IAAI;AAAA,IAChE;AAAA,EACF;AAEA,WAAS,aAAa;AACpB,QAAI,CAAC,MAAO;AACZ,QAAI,GAAG,IAAI,MAAM;AACjB,gBAAY,IAAI,MAAM,CAAC;AACvB,SAAK,IAAI,MAAM,CAAC;AAChB,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,gBAAU,CAAC,IAAI,MAAM,GAAG,CAAC,IAAI,CAACA,GAAE,MAAM,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,IAAI,CAAC,SAAS,MAAM,CAAC,GAAG,GAAG,KAAK;AAAA,IACzF;AAAA,EACF;AAEA,QAAM,aAAa,SAAS,GAAG;AAC7B,YAAQ;AACR,eAAW;AAAA,EACb;AAEA,QAAM,WAAW,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,WAAW,OAAO,MAAM,aAAa,IAAIC,kBAAS,CAAC,CAAC,GAAG,WAAW,GAAG,SAAS;AAAA,EAC3G;AAEA,QAAM,IAAI,SAAS,GAAG;AACpB,WAAO,UAAU,UAAUD,KAAI,OAAO,MAAM,aAAa,IAAIC,kBAAS,CAAC,CAAC,GAAG,WAAW,GAAG,SAASD;AAAA,EACpG;AAEA,SAAO;AACT;;;ACxCA,SAAS,kBAAkBE,IAAG,GAAG;AAC/B,SAAOA,GAAE,WAAW,EAAE,SAAS,IAAI;AACrC;AAEA,SAAS,MAAM,UAAU;AACvB,SAAO,SAAS,OAAO,aAAa,CAAC,IAAI,SAAS;AACpD;AAEA,SAAS,YAAYC,IAAGC,IAAG;AACzB,SAAOD,KAAIC,GAAE;AACf;AAEA,SAAS,KAAK,UAAU;AACtB,SAAO,IAAI,SAAS,OAAO,YAAY,CAAC;AAC1C;AAEA,SAAS,WAAWC,IAAGD,IAAG;AACxB,SAAO,KAAK,IAAIC,IAAGD,GAAE,CAAC;AACxB;AAEA,SAAS,SAAS,MAAM;AACtB,MAAI;AACJ,SAAO,WAAW,KAAK,SAAU,QAAO,SAAS,CAAC;AAClD,SAAO;AACT;AAEA,SAAS,UAAU,MAAM;AACvB,MAAI;AACJ,SAAO,WAAW,KAAK,SAAU,QAAO,SAAS,SAAS,SAAS,CAAC;AACpE,SAAO;AACT;AAEe,SAAR,kBAAmB;AACxB,MAAI,aAAa,mBACb,KAAK,GACL,KAAK,GACL,WAAW;AAEf,WAAS,QAAQE,OAAM;AACrB,QAAI,cACAH,KAAI;AAGR,IAAAG,MAAK,UAAU,SAAS,MAAM;AAC5B,UAAI,WAAW,KAAK;AACpB,UAAI,UAAU;AACZ,aAAK,IAAI,MAAM,QAAQ;AACvB,aAAK,IAAI,KAAK,QAAQ;AAAA,MACxB,OAAO;AACL,aAAK,IAAI,eAAeH,MAAK,WAAW,MAAM,YAAY,IAAI;AAC9D,aAAK,IAAI;AACT,uBAAe;AAAA,MACjB;AAAA,IACF,CAAC;AAED,QAAII,QAAO,SAASD,KAAI,GACpBE,SAAQ,UAAUF,KAAI,GACtB,KAAKC,MAAK,IAAI,WAAWA,OAAMC,MAAK,IAAI,GACxC,KAAKA,OAAM,IAAI,WAAWA,QAAOD,KAAI,IAAI;AAG7C,WAAOD,MAAK,UAAU,WAAW,SAAS,MAAM;AAC9C,WAAK,KAAK,KAAK,IAAIA,MAAK,KAAK;AAC7B,WAAK,KAAKA,MAAK,IAAI,KAAK,KAAK;AAAA,IAC/B,IAAI,SAAS,MAAM;AACjB,WAAK,KAAK,KAAK,IAAI,OAAO,KAAK,MAAM;AACrC,WAAK,KAAK,KAAKA,MAAK,IAAI,KAAK,IAAIA,MAAK,IAAI,MAAM;AAAA,IAClD,CAAC;AAAA,EACH;AAEA,UAAQ,aAAa,SAASH,IAAG;AAC/B,WAAO,UAAU,UAAU,aAAaA,IAAG,WAAW;AAAA,EACxD;AAEA,UAAQ,OAAO,SAASA,IAAG;AACzB,WAAO,UAAU,UAAU,WAAW,OAAO,KAAK,CAACA,GAAE,CAAC,GAAG,KAAK,CAACA,GAAE,CAAC,GAAG,WAAY,WAAW,OAAO,CAAC,IAAI,EAAE;AAAA,EAC5G;AAEA,UAAQ,WAAW,SAASA,IAAG;AAC7B,WAAO,UAAU,UAAU,WAAW,MAAM,KAAK,CAACA,GAAE,CAAC,GAAG,KAAK,CAACA,GAAE,CAAC,GAAG,WAAY,WAAW,CAAC,IAAI,EAAE,IAAI;AAAA,EACxG;AAEA,SAAO;AACT;;;ACnFA,SAASM,OAAM,MAAM;AACnB,MAAIC,OAAM,GACN,WAAW,KAAK,UAChB,IAAI,YAAY,SAAS;AAC7B,MAAI,CAAC,EAAG,CAAAA,OAAM;AAAA,MACT,QAAO,EAAE,KAAK,EAAG,CAAAA,QAAO,SAAS,CAAC,EAAE;AACzC,OAAK,QAAQA;AACf;AAEe,SAAR,gBAAmB;AACxB,SAAO,KAAK,UAAUD,MAAK;AAC7B;;;ACXe,SAAR,aAAiB,UAAU,MAAM;AACtC,MAAIE,SAAQ;AACZ,aAAW,QAAQ,MAAM;AACvB,aAAS,KAAK,MAAM,MAAM,EAAEA,QAAO,IAAI;AAAA,EACzC;AACA,SAAO;AACT;;;ACNe,SAAR,mBAAiB,UAAU,MAAM;AACtC,MAAI,OAAO,MAAM,QAAQ,CAAC,IAAI,GAAG,UAAU,GAAGC,SAAQ;AACtD,SAAO,OAAO,MAAM,IAAI,GAAG;AACzB,aAAS,KAAK,MAAM,MAAM,EAAEA,QAAO,IAAI;AACvC,QAAI,WAAW,KAAK,UAAU;AAC5B,WAAK,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACzC,cAAM,KAAK,SAAS,CAAC,CAAC;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;;;ACXe,SAAR,kBAAiB,UAAU,MAAM;AACtC,MAAI,OAAO,MAAM,QAAQ,CAAC,IAAI,GAAG,OAAO,CAAC,GAAG,UAAU,GAAG,GAAGC,SAAQ;AACpE,SAAO,OAAO,MAAM,IAAI,GAAG;AACzB,SAAK,KAAK,IAAI;AACd,QAAI,WAAW,KAAK,UAAU;AAC5B,WAAK,IAAI,GAAG,IAAI,SAAS,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC3C,cAAM,KAAK,SAAS,CAAC,CAAC;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AACA,SAAO,OAAO,KAAK,IAAI,GAAG;AACxB,aAAS,KAAK,MAAM,MAAM,EAAEA,QAAO,IAAI;AAAA,EACzC;AACA,SAAO;AACT;;;ACde,SAARC,cAAiB,UAAU,MAAM;AACtC,MAAIC,SAAQ;AACZ,aAAW,QAAQ,MAAM;AACvB,QAAI,SAAS,KAAK,MAAM,MAAM,EAAEA,QAAO,IAAI,GAAG;AAC5C,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;ACPe,SAAR,YAAiB,OAAO;AAC7B,SAAO,KAAK,UAAU,SAAS,MAAM;AACnC,QAAIC,OAAM,CAAC,MAAM,KAAK,IAAI,KAAK,GAC3B,WAAW,KAAK,UAChB,IAAI,YAAY,SAAS;AAC7B,WAAO,EAAE,KAAK,EAAG,CAAAA,QAAO,SAAS,CAAC,EAAE;AACpC,SAAK,QAAQA;AAAA,EACf,CAAC;AACH;;;ACRe,SAAR,aAAiB,SAAS;AAC/B,SAAO,KAAK,WAAW,SAAS,MAAM;AACpC,QAAI,KAAK,UAAU;AACjB,WAAK,SAAS,KAAK,OAAO;AAAA,IAC5B;AAAA,EACF,CAAC;AACH;;;ACNe,SAARC,cAAiB,KAAK;AAC3B,MAAIC,SAAQ,MACR,WAAW,oBAAoBA,QAAO,GAAG,GACzC,QAAQ,CAACA,MAAK;AAClB,SAAOA,WAAU,UAAU;AACzB,IAAAA,SAAQA,OAAM;AACd,UAAM,KAAKA,MAAK;AAAA,EAClB;AACA,MAAI,IAAI,MAAM;AACd,SAAO,QAAQ,UAAU;AACvB,UAAM,OAAO,GAAG,GAAG,GAAG;AACtB,UAAM,IAAI;AAAA,EACZ;AACA,SAAO;AACT;AAEA,SAAS,oBAAoBC,IAAG,GAAG;AACjC,MAAIA,OAAM,EAAG,QAAOA;AACpB,MAAI,SAASA,GAAE,UAAU,GACrB,SAAS,EAAE,UAAU,GACrBC,KAAI;AACR,EAAAD,KAAI,OAAO,IAAI;AACf,MAAI,OAAO,IAAI;AACf,SAAOA,OAAM,GAAG;AACd,IAAAC,KAAID;AACJ,IAAAA,KAAI,OAAO,IAAI;AACf,QAAI,OAAO,IAAI;AAAA,EACjB;AACA,SAAOC;AACT;;;AC7Be,SAAR,oBAAmB;AACxB,MAAI,OAAO,MAAM,QAAQ,CAAC,IAAI;AAC9B,SAAO,OAAO,KAAK,QAAQ;AACzB,UAAM,KAAK,IAAI;AAAA,EACjB;AACA,SAAO;AACT;;;ACNe,SAAR,sBAAmB;AACxB,SAAO,MAAM,KAAK,IAAI;AACxB;;;ACFe,SAAR,iBAAmB;AACxB,MAAI,SAAS,CAAC;AACd,OAAK,WAAW,SAAS,MAAM;AAC7B,QAAI,CAAC,KAAK,UAAU;AAClB,aAAO,KAAK,IAAI;AAAA,IAClB;AAAA,EACF,CAAC;AACD,SAAO;AACT;;;ACRe,SAAR,gBAAmB;AACxB,MAAIC,QAAO,MAAM,QAAQ,CAAC;AAC1B,EAAAA,MAAK,KAAK,SAAS,MAAM;AACvB,QAAI,SAASA,OAAM;AACjB,YAAM,KAAK,EAAC,QAAQ,KAAK,QAAQ,QAAQ,KAAI,CAAC;AAAA,IAChD;AAAA,EACF,CAAC;AACD,SAAO;AACT;;;ACRe,UAAR,mBAAoB;AACzB,MAAI,OAAO,MAAM,SAAS,OAAO,CAAC,IAAI,GAAG,UAAU,GAAG;AACtD,KAAG;AACD,cAAU,KAAK,QAAQ,GAAG,OAAO,CAAC;AAClC,WAAO,OAAO,QAAQ,IAAI,GAAG;AAC3B,YAAM;AACN,UAAI,WAAW,KAAK,UAAU;AAC5B,aAAK,IAAI,GAAG,IAAI,SAAS,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC3C,eAAK,KAAK,SAAS,CAAC,CAAC;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF,SAAS,KAAK;AAChB;;;ACCe,SAAR,UAA2B,MAAM,UAAU;AAChD,MAAI,gBAAgB,KAAK;AACvB,WAAO,CAAC,QAAW,IAAI;AACvB,QAAI,aAAa,OAAW,YAAW;AAAA,EACzC,WAAW,aAAa,QAAW;AACjC,eAAW;AAAA,EACb;AAEA,MAAIC,QAAO,IAAI,KAAK,IAAI,GACpB,MACA,QAAQ,CAACA,KAAI,GACb,OACA,QACA,GACA;AAEJ,SAAO,OAAO,MAAM,IAAI,GAAG;AACzB,SAAK,SAAS,SAAS,KAAK,IAAI,OAAO,KAAK,SAAS,MAAM,KAAK,MAAM,GAAG,SAAS;AAChF,WAAK,WAAW;AAChB,WAAK,IAAI,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AAC3B,cAAM,KAAK,QAAQ,OAAO,CAAC,IAAI,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC;AAClD,cAAM,SAAS;AACf,cAAM,QAAQ,KAAK,QAAQ;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAEA,SAAOA,MAAK,WAAW,aAAa;AACtC;AAEA,SAAS,YAAY;AACnB,SAAO,UAAU,IAAI,EAAE,WAAW,QAAQ;AAC5C;AAEA,SAAS,eAAe,GAAG;AACzB,SAAO,EAAE;AACX;AAEA,SAAS,YAAY,GAAG;AACtB,SAAO,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI;AACnC;AAEA,SAAS,SAAS,MAAM;AACtB,MAAI,KAAK,KAAK,UAAU,OAAW,MAAK,QAAQ,KAAK,KAAK;AAC1D,OAAK,OAAO,KAAK,KAAK;AACxB;AAEO,SAAS,cAAc,MAAM;AAClC,MAAI,SAAS;AACb;AAAG,SAAK,SAAS;AAAA,UACT,OAAO,KAAK,WAAY,KAAK,SAAS,EAAE;AAClD;AAEO,SAAS,KAAK,MAAM;AACzB,OAAK,OAAO;AACZ,OAAK,QACL,KAAK,SAAS;AACd,OAAK,SAAS;AAChB;AAEA,KAAK,YAAY,UAAU,YAAY;AAAA,EACrC,aAAa;AAAA,EACb,OAAO;AAAA,EACP,MAAM;AAAA,EACN,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,MAAMC;AAAA,EACN,KAAK;AAAA,EACL,MAAM;AAAA,EACN,MAAMC;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,MAAM;AAAA,EACN,CAAC,OAAO,QAAQ,GAAG;AACrB;;;AC1FO,SAAS,SAAS,GAAG;AAC1B,SAAO,KAAK,OAAO,OAAO,SAAS,CAAC;AACtC;AAEO,SAAS,SAAS,GAAG;AAC1B,MAAI,OAAO,MAAM,WAAY,OAAM,IAAI;AACvC,SAAO;AACT;;;ACPO,SAAS,eAAe;AAC7B,SAAO;AACT;AAEe,SAARC,kBAAiBC,IAAG;AACzB,SAAO,WAAW;AAChB,WAAOA;AAAA,EACT;AACF;;;ACPA,IAAMC,KAAI;AACV,IAAMC,KAAI;AACV,IAAMC,KAAI;AAEK,SAARC,eAAmB;AACxB,MAAI,IAAI;AACR,SAAO,OAAO,KAAKH,KAAI,IAAIC,MAAKC,MAAKA;AACvC;;;ACRe,SAARE,eAAiBC,IAAG;AACzB,SAAO,OAAOA,OAAM,YAAY,YAAYA,KACxCA,KACA,MAAM,KAAKA,EAAC;AAClB;AAEO,SAAS,QAAQC,QAAO,QAAQ;AACrC,MAAIC,KAAID,OAAM,QACV,GACA;AAEJ,SAAOC,IAAG;AACR,QAAI,OAAO,IAAIA,OAAM;AACrB,QAAID,OAAMC,EAAC;AACX,IAAAD,OAAMC,EAAC,IAAID,OAAM,CAAC;AAClB,IAAAA,OAAM,CAAC,IAAI;AAAA,EACb;AAEA,SAAOA;AACT;;;AChBe,SAAR,gBAAiB,SAAS;AAC/B,SAAO,kBAAkB,SAASE,aAAI,CAAC;AACzC;AAEO,SAAS,kBAAkB,SAAS,QAAQ;AACjD,MAAI,IAAI,GAAG,KAAK,UAAU,QAAQ,MAAM,KAAK,OAAO,GAAG,MAAM,GAAG,QAAQ,IAAI,CAAC,GAAG,GAAG;AAEnF,SAAO,IAAI,GAAG;AACZ,QAAI,QAAQ,CAAC;AACb,QAAI,KAAK,aAAa,GAAG,CAAC,EAAG,GAAE;AAAA,QAC1B,KAAI,aAAa,IAAI,YAAY,GAAG,CAAC,CAAC,GAAG,IAAI;AAAA,EACpD;AAEA,SAAO;AACT;AAEA,SAAS,YAAY,GAAG,GAAG;AACzB,MAAI,GAAG;AAEP,MAAI,gBAAgB,GAAG,CAAC,EAAG,QAAO,CAAC,CAAC;AAGpC,OAAK,IAAI,GAAG,IAAI,EAAE,QAAQ,EAAE,GAAG;AAC7B,QAAI,YAAY,GAAG,EAAE,CAAC,CAAC,KAChB,gBAAgB,cAAc,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG;AACjD,aAAO,CAAC,EAAE,CAAC,GAAG,CAAC;AAAA,IACjB;AAAA,EACF;AAGA,OAAK,IAAI,GAAG,IAAI,EAAE,SAAS,GAAG,EAAE,GAAG;AACjC,SAAK,IAAI,IAAI,GAAG,IAAI,EAAE,QAAQ,EAAE,GAAG;AACjC,UAAI,YAAY,cAAc,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,KACrC,YAAY,cAAc,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,KACxC,YAAY,cAAc,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,KACxC,gBAAgB,cAAc,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG;AACvD,eAAO,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AAGA,QAAM,IAAI;AACZ;AAEA,SAAS,YAAYC,IAAG,GAAG;AACzB,MAAI,KAAKA,GAAE,IAAI,EAAE,GAAG,KAAK,EAAE,IAAIA,GAAE,GAAG,KAAK,EAAE,IAAIA,GAAE;AACjD,SAAO,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C;AAEA,SAAS,aAAaA,IAAG,GAAG;AAC1B,MAAI,KAAKA,GAAE,IAAI,EAAE,IAAI,KAAK,IAAIA,GAAE,GAAG,EAAE,GAAG,CAAC,IAAI,MAAM,KAAK,EAAE,IAAIA,GAAE,GAAG,KAAK,EAAE,IAAIA,GAAE;AAChF,SAAO,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C;AAEA,SAAS,gBAAgBA,IAAG,GAAG;AAC7B,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,EAAE,GAAG;AACjC,QAAI,CAAC,aAAaA,IAAG,EAAE,CAAC,CAAC,GAAG;AAC1B,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,aAAa,GAAG;AACvB,UAAQ,EAAE,QAAQ;AAAA,IAChB,KAAK;AAAG,aAAO,cAAc,EAAE,CAAC,CAAC;AAAA,IACjC,KAAK;AAAG,aAAO,cAAc,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,IACvC,KAAK;AAAG,aAAO,cAAc,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,EAC/C;AACF;AAEA,SAAS,cAAcA,IAAG;AACxB,SAAO;AAAA,IACL,GAAGA,GAAE;AAAA,IACL,GAAGA,GAAE;AAAA,IACL,GAAGA,GAAE;AAAA,EACP;AACF;AAEA,SAAS,cAAcA,IAAG,GAAG;AAC3B,MAAI,KAAKA,GAAE,GAAG,KAAKA,GAAE,GAAG,KAAKA,GAAE,GAC3BC,MAAK,EAAE,GAAGC,MAAK,EAAE,GAAG,KAAK,EAAE,GAC3B,MAAMD,MAAK,IAAI,MAAMC,MAAK,IAAI,MAAM,KAAK,IACzC,IAAI,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG;AACvC,SAAO;AAAA,IACL,IAAI,KAAKD,MAAK,MAAM,IAAI,OAAO;AAAA,IAC/B,IAAI,KAAKC,MAAK,MAAM,IAAI,OAAO;AAAA,IAC/B,IAAI,IAAI,KAAK,MAAM;AAAA,EACrB;AACF;AAEA,SAAS,cAAcF,IAAG,GAAGG,IAAG;AAC9B,MAAI,KAAKH,GAAE,GAAG,KAAKA,GAAE,GAAG,KAAKA,GAAE,GAC3BC,MAAK,EAAE,GAAGC,MAAK,EAAE,GAAG,KAAK,EAAE,GAC3B,KAAKC,GAAE,GAAG,KAAKA,GAAE,GAAG,KAAKA,GAAE,GAC3BC,MAAK,KAAKH,KACVI,MAAK,KAAK,IACVC,MAAK,KAAKJ,KACVK,MAAK,KAAK,IACVC,MAAK,KAAK,IACVC,MAAK,KAAK,IACV,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAC9B,KAAK,KAAKR,MAAKA,MAAKC,MAAKA,MAAK,KAAK,IACnC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IACnC,KAAKG,MAAKC,MAAKF,MAAKG,KACpB,MAAMD,MAAK,KAAKC,MAAK,OAAO,KAAK,KAAK,IACtC,MAAMA,MAAKC,MAAKF,MAAKG,OAAM,IAC3B,MAAMJ,MAAK,KAAKD,MAAK,OAAO,KAAK,KAAK,IACtC,MAAMA,MAAKK,MAAKJ,MAAKG,OAAM,IAC3B,IAAI,KAAK,KAAK,KAAK,KAAK,GACxB,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAC7B,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAC7B,IAAI,EAAE,KAAK,IAAI,CAAC,IAAI,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,IAAI;AAClF,SAAO;AAAA,IACL,GAAG,KAAK,KAAK,KAAK;AAAA,IAClB,GAAG,KAAK,KAAK,KAAK;AAAA,IAClB;AAAA,EACF;AACF;;;ACtHA,SAAS,MAAM,GAAGE,IAAGC,IAAG;AACtB,MAAI,KAAK,EAAE,IAAID,GAAE,GAAGE,IAAGC,KACnB,KAAK,EAAE,IAAIH,GAAE,GAAGI,IAAGC,KACnB,KAAK,KAAK,KAAK,KAAK;AACxB,MAAI,IAAI;AACN,IAAAF,MAAKH,GAAE,IAAIC,GAAE,GAAGE,OAAMA;AACtB,IAAAE,MAAK,EAAE,IAAIJ,GAAE,GAAGI,OAAMA;AACtB,QAAIF,MAAKE,KAAI;AACX,MAAAH,MAAK,KAAKG,MAAKF,QAAO,IAAI;AAC1B,MAAAC,KAAI,KAAK,KAAK,KAAK,IAAI,GAAGC,MAAK,KAAKH,KAAIA,EAAC,CAAC;AAC1C,MAAAD,GAAE,IAAI,EAAE,IAAIC,KAAI,KAAKE,KAAI;AACzB,MAAAH,GAAE,IAAI,EAAE,IAAIC,KAAI,KAAKE,KAAI;AAAA,IAC3B,OAAO;AACL,MAAAF,MAAK,KAAKC,MAAKE,QAAO,IAAI;AAC1B,MAAAD,KAAI,KAAK,KAAK,KAAK,IAAI,GAAGD,MAAK,KAAKD,KAAIA,EAAC,CAAC;AAC1C,MAAAD,GAAE,IAAID,GAAE,IAAIE,KAAI,KAAKE,KAAI;AACzB,MAAAH,GAAE,IAAID,GAAE,IAAIE,KAAI,KAAKE,KAAI;AAAA,IAC3B;AAAA,EACF,OAAO;AACL,IAAAH,GAAE,IAAID,GAAE,IAAIC,GAAE;AACd,IAAAA,GAAE,IAAID,GAAE;AAAA,EACV;AACF;AAEA,SAAS,WAAWA,IAAG,GAAG;AACxB,MAAI,KAAKA,GAAE,IAAI,EAAE,IAAI,MAAM,KAAK,EAAE,IAAIA,GAAE,GAAG,KAAK,EAAE,IAAIA,GAAE;AACxD,SAAO,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C;AAEA,SAAS,MAAM,MAAM;AACnB,MAAIA,KAAI,KAAK,GACT,IAAI,KAAK,KAAK,GACd,KAAKA,GAAE,IAAI,EAAE,GACb,MAAMA,GAAE,IAAI,EAAE,IAAI,EAAE,IAAIA,GAAE,KAAK,IAC/B,MAAMA,GAAE,IAAI,EAAE,IAAI,EAAE,IAAIA,GAAE,KAAK;AACnC,SAAO,KAAK,KAAK,KAAK;AACxB;AAEA,SAASM,MAAK,QAAQ;AACpB,OAAK,IAAI;AACT,OAAK,OAAO;AACZ,OAAK,WAAW;AAClB;AAEO,SAAS,mBAAmB,SAAS,QAAQ;AAClD,MAAI,EAAE,KAAK,UAAUC,eAAM,OAAO,GAAG,QAAS,QAAO;AAErD,MAAIP,IAAG,GAAGC,IAAG,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI;AAGrC,EAAAD,KAAI,QAAQ,CAAC,GAAGA,GAAE,IAAI,GAAGA,GAAE,IAAI;AAC/B,MAAI,EAAE,IAAI,GAAI,QAAOA,GAAE;AAGvB,MAAI,QAAQ,CAAC,GAAGA,GAAE,IAAI,CAAC,EAAE,GAAG,EAAE,IAAIA,GAAE,GAAG,EAAE,IAAI;AAC7C,MAAI,EAAE,IAAI,GAAI,QAAOA,GAAE,IAAI,EAAE;AAG7B,QAAM,GAAGA,IAAGC,KAAI,QAAQ,CAAC,CAAC;AAG1B,EAAAD,KAAI,IAAIM,MAAKN,EAAC,GAAG,IAAI,IAAIM,MAAK,CAAC,GAAGL,KAAI,IAAIK,MAAKL,EAAC;AAChD,EAAAD,GAAE,OAAOC,GAAE,WAAW;AACtB,IAAE,OAAOD,GAAE,WAAWC;AACtB,EAAAA,GAAE,OAAO,EAAE,WAAWD;AAGtB,OAAM,MAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC5B,UAAMA,GAAE,GAAG,EAAE,GAAGC,KAAI,QAAQ,CAAC,CAAC,GAAGA,KAAI,IAAIK,MAAKL,EAAC;AAK/C,QAAI,EAAE,MAAM,IAAID,GAAE,UAAU,KAAK,EAAE,EAAE,GAAG,KAAKA,GAAE,EAAE;AACjD,OAAG;AACD,UAAI,MAAM,IAAI;AACZ,YAAI,WAAW,EAAE,GAAGC,GAAE,CAAC,GAAG;AACxB,cAAI,GAAGD,GAAE,OAAO,GAAG,EAAE,WAAWA,IAAG,EAAE;AACrC,mBAAS;AAAA,QACX;AACA,cAAM,EAAE,EAAE,GAAG,IAAI,EAAE;AAAA,MACrB,OAAO;AACL,YAAI,WAAW,EAAE,GAAGC,GAAE,CAAC,GAAG;AACxB,UAAAD,KAAI,GAAGA,GAAE,OAAO,GAAG,EAAE,WAAWA,IAAG,EAAE;AACrC,mBAAS;AAAA,QACX;AACA,cAAM,EAAE,EAAE,GAAG,IAAI,EAAE;AAAA,MACrB;AAAA,IACF,SAAS,MAAM,EAAE;AAGjB,IAAAC,GAAE,WAAWD,IAAGC,GAAE,OAAO,GAAGD,GAAE,OAAO,EAAE,WAAW,IAAIC;AAGtD,SAAK,MAAMD,EAAC;AACZ,YAAQC,KAAIA,GAAE,UAAU,GAAG;AACzB,WAAK,KAAK,MAAMA,EAAC,KAAK,IAAI;AACxB,QAAAD,KAAIC,IAAG,KAAK;AAAA,MACd;AAAA,IACF;AACA,QAAID,GAAE;AAAA,EACR;AAGA,EAAAA,KAAI,CAAC,EAAE,CAAC,GAAGC,KAAI;AAAG,UAAQA,KAAIA,GAAE,UAAU,EAAG,CAAAD,GAAE,KAAKC,GAAE,CAAC;AAAG,EAAAA,KAAI,kBAAkBD,IAAG,MAAM;AAGzF,OAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,CAAAA,KAAI,QAAQ,CAAC,GAAGA,GAAE,KAAKC,GAAE,GAAGD,GAAE,KAAKC,GAAE;AAE7D,SAAOA,GAAE;AACX;AAEe,SAAR,iBAAiB,SAAS;AAC/B,qBAAmB,SAASO,aAAI,CAAC;AACjC,SAAO;AACT;;;AClHA,SAASC,eAAc,GAAG;AACxB,SAAO,KAAK,KAAK,EAAE,KAAK;AAC1B;AAEe,SAAR,eAAmB;AACxB,MAAI,SAAS,MACT,KAAK,GACL,KAAK,GACL,UAAU;AAEd,WAAS,KAAKC,OAAM;AAClB,UAAM,SAASC,aAAI;AACnB,IAAAD,MAAK,IAAI,KAAK,GAAGA,MAAK,IAAI,KAAK;AAC/B,QAAI,QAAQ;AACV,MAAAA,MAAK,WAAW,WAAW,MAAM,CAAC,EAC7B,UAAU,mBAAmB,SAAS,KAAK,MAAM,CAAC,EAClD,WAAW,eAAe,CAAC,CAAC;AAAA,IACnC,OAAO;AACL,MAAAA,MAAK,WAAW,WAAWD,cAAa,CAAC,EACpC,UAAU,mBAAmB,cAAc,GAAG,MAAM,CAAC,EACrD,UAAU,mBAAmB,SAASC,MAAK,IAAI,KAAK,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,EACxE,WAAW,eAAe,KAAK,IAAI,IAAI,EAAE,KAAK,IAAIA,MAAK,EAAE,CAAC;AAAA,IACjE;AACA,WAAOA;AAAA,EACT;AAEA,OAAK,SAAS,SAASE,IAAG;AACxB,WAAO,UAAU,UAAU,SAAS,SAASA,EAAC,GAAG,QAAQ;AAAA,EAC3D;AAEA,OAAK,OAAO,SAASA,IAAG;AACtB,WAAO,UAAU,UAAU,KAAK,CAACA,GAAE,CAAC,GAAG,KAAK,CAACA,GAAE,CAAC,GAAG,QAAQ,CAAC,IAAI,EAAE;AAAA,EACpE;AAEA,OAAK,UAAU,SAASA,IAAG;AACzB,WAAO,UAAU,UAAU,UAAU,OAAOA,OAAM,aAAaA,KAAIC,kBAAS,CAACD,EAAC,GAAG,QAAQ;AAAA,EAC3F;AAEA,SAAO;AACT;AAEA,SAAS,WAAW,QAAQ;AAC1B,SAAO,SAAS,MAAM;AACpB,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,IAAI,KAAK,IAAI,GAAG,CAAC,OAAO,IAAI,KAAK,CAAC;AAAA,IACzC;AAAA,EACF;AACF;AAEA,SAAS,mBAAmB,SAAS,GAAG,QAAQ;AAC9C,SAAO,SAAS,MAAM;AACpB,QAAI,WAAW,KAAK,UAAU;AAC5B,UAAI,UACA,GACA,IAAI,SAAS,QACb,IAAI,QAAQ,IAAI,IAAI,KAAK,GACzB;AAEJ,UAAI,EAAG,MAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,UAAS,CAAC,EAAE,KAAK;AAChD,UAAI,mBAAmB,UAAU,MAAM;AACvC,UAAI,EAAG,MAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,UAAS,CAAC,EAAE,KAAK;AAChD,WAAK,IAAI,IAAI;AAAA,IACf;AAAA,EACF;AACF;AAEA,SAAS,eAAe,GAAG;AACzB,SAAO,SAAS,MAAM;AACpB,QAAI,SAAS,KAAK;AAClB,SAAK,KAAK;AACV,QAAI,QAAQ;AACV,WAAK,IAAI,OAAO,IAAI,IAAI,KAAK;AAC7B,WAAK,IAAI,OAAO,IAAI,IAAI,KAAK;AAAA,IAC/B;AAAA,EACF;AACF;;;AChFe,SAARE,eAAiB,MAAM;AAC5B,OAAK,KAAK,KAAK,MAAM,KAAK,EAAE;AAC5B,OAAK,KAAK,KAAK,MAAM,KAAK,EAAE;AAC5B,OAAK,KAAK,KAAK,MAAM,KAAK,EAAE;AAC5B,OAAK,KAAK,KAAK,MAAM,KAAK,EAAE;AAC9B;;;ACLe,SAAR,aAAiB,QAAQ,IAAI,IAAI,IAAI,IAAI;AAC9C,MAAI,QAAQ,OAAO,UACf,MACA,IAAI,IACJ,IAAI,MAAM,QACV,IAAI,OAAO,UAAU,KAAK,MAAM,OAAO;AAE3C,SAAO,EAAE,IAAI,GAAG;AACd,WAAO,MAAM,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,KAAK;AACzC,SAAK,KAAK,IAAI,KAAK,KAAK,MAAM,KAAK,QAAQ;AAAA,EAC7C;AACF;;;ACRe,SAAR,oBAAmB;AACxB,MAAI,KAAK,GACL,KAAK,GACL,UAAU,GACV,QAAQ;AAEZ,WAAS,UAAUC,OAAM;AACvB,QAAI,IAAIA,MAAK,SAAS;AACtB,IAAAA,MAAK,KACLA,MAAK,KAAK;AACV,IAAAA,MAAK,KAAK;AACV,IAAAA,MAAK,KAAK,KAAK;AACf,IAAAA,MAAK,WAAW,aAAa,IAAI,CAAC,CAAC;AACnC,QAAI,MAAO,CAAAA,MAAK,WAAWC,cAAS;AACpC,WAAOD;AAAA,EACT;AAEA,WAAS,aAAaE,KAAI,GAAG;AAC3B,WAAO,SAAS,MAAM;AACpB,UAAI,KAAK,UAAU;AACjB,qBAAY,MAAM,KAAK,IAAIA,OAAM,KAAK,QAAQ,KAAK,GAAG,KAAK,IAAIA,OAAM,KAAK,QAAQ,KAAK,CAAC;AAAA,MAC1F;AACA,UAAI,KAAK,KAAK,IACV,KAAK,KAAK,IACV,KAAK,KAAK,KAAK,SACf,KAAK,KAAK,KAAK;AACnB,UAAI,KAAK,GAAI,MAAK,MAAM,KAAK,MAAM;AACnC,UAAI,KAAK,GAAI,MAAK,MAAM,KAAK,MAAM;AACnC,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAEA,YAAU,QAAQ,SAASC,IAAG;AAC5B,WAAO,UAAU,UAAU,QAAQ,CAAC,CAACA,IAAG,aAAa;AAAA,EACvD;AAEA,YAAU,OAAO,SAASA,IAAG;AAC3B,WAAO,UAAU,UAAU,KAAK,CAACA,GAAE,CAAC,GAAG,KAAK,CAACA,GAAE,CAAC,GAAG,aAAa,CAAC,IAAI,EAAE;AAAA,EACzE;AAEA,YAAU,UAAU,SAASA,IAAG;AAC9B,WAAO,UAAU,UAAU,UAAU,CAACA,IAAG,aAAa;AAAA,EACxD;AAEA,SAAO;AACT;;;AChDA,IAAI,UAAU,EAAC,OAAO,GAAE;AAAxB,IACI,YAAY,CAAC;AADjB,IAEI,UAAU,CAAC;AAEf,SAAS,UAAU,GAAG;AACpB,SAAO,EAAE;AACX;AAEA,SAAS,gBAAgB,GAAG;AAC1B,SAAO,EAAE;AACX;AAEe,SAAR,mBAAmB;AACxB,MAAIC,MAAK,WACL,WAAW,iBACXC;AAEJ,WAAS,SAAS,MAAM;AACtB,QAAI,QAAQ,MAAM,KAAK,IAAI,GACvB,YAAYD,KACZ,kBAAkB,UAClB,GACA,GACA,GACAE,OACA,QACA,MACA,QACA,SACA,YAAY,oBAAI;AAEpB,QAAID,SAAQ,MAAM;AAChB,YAAM,IAAI,MAAM,IAAI,CAACE,IAAGC,OAAM,UAAUH,MAAKE,IAAGC,IAAG,IAAI,CAAC,CAAC;AACzD,YAAM,IAAI,EAAE,IAAI,QAAQ;AACxB,YAAM,IAAI,IAAI,IAAI,CAAC,EAAE,IAAI,EAAE;AAC3B,iBAAWA,MAAK,GAAG;AACjB,YAAI,CAAC,EAAE,IAAIA,EAAC,GAAG;AACb,YAAE,IAAIA,EAAC;AACP,YAAE,KAAKA,EAAC;AACR,YAAE,KAAK,SAASA,EAAC,CAAC;AAClB,gBAAM,KAAK,OAAO;AAAA,QACpB;AAAA,MACF;AACA,kBAAY,CAAC,GAAGA,OAAM,EAAEA,EAAC;AACzB,wBAAkB,CAAC,GAAGA,OAAM,EAAEA,EAAC;AAAA,IACjC;AAEA,SAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,EAAE,GAAG;AACxC,UAAI,MAAM,CAAC,GAAG,OAAO,MAAM,CAAC,IAAI,IAAI,KAAK,CAAC;AAC1C,WAAK,SAAS,UAAU,GAAG,GAAG,IAAI,MAAM,SAAS,UAAU,KAAK;AAC9D,kBAAU,KAAK,KAAK;AACpB,kBAAU,IAAI,SAAS,UAAU,IAAI,OAAO,IAAI,YAAY,IAAI;AAAA,MAClE;AACA,WAAK,SAAS,gBAAgB,GAAG,GAAG,IAAI,MAAM,SAAS,UAAU,KAAK;AACpE,aAAK,SAAS;AAAA,MAChB;AAAA,IACF;AAEA,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,aAAO,MAAM,CAAC;AACd,UAAI,SAAS,KAAK,QAAQ;AACxB,iBAAS,UAAU,IAAI,MAAM;AAC7B,YAAI,CAAC,OAAQ,OAAM,IAAI,MAAM,cAAc,MAAM;AACjD,YAAI,WAAW,UAAW,OAAM,IAAI,MAAM,gBAAgB,MAAM;AAChE,YAAI,OAAO,SAAU,QAAO,SAAS,KAAK,IAAI;AAAA,YACzC,QAAO,WAAW,CAAC,IAAI;AAC5B,aAAK,SAAS;AAAA,MAChB,OAAO;AACL,YAAIF,MAAM,OAAM,IAAI,MAAM,gBAAgB;AAC1C,QAAAA,QAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,CAACA,MAAM,OAAM,IAAI,MAAM,SAAS;AAIpC,QAAID,SAAQ,MAAM;AAChB,aAAOC,MAAK,SAAS,WAAWA,MAAK,SAAS,WAAW,GAAG;AAC1D,QAAAA,QAAOA,MAAK,SAAS,CAAC,GAAG,EAAE;AAAA,MAC7B;AACA,eAASE,KAAI,MAAM,SAAS,GAAGA,MAAK,GAAG,EAAEA,IAAG;AAC1C,eAAO,MAAMA,EAAC;AACd,YAAI,KAAK,SAAS,QAAS;AAC3B,aAAK,OAAO;AAAA,MACd;AAAA,IACF;AAEA,IAAAF,MAAK,SAAS;AACd,IAAAA,MAAK,WAAW,SAASG,OAAM;AAAE,MAAAA,MAAK,QAAQA,MAAK,OAAO,QAAQ;AAAG,QAAE;AAAA,IAAG,CAAC,EAAE,WAAW,aAAa;AACrG,IAAAH,MAAK,SAAS;AACd,QAAI,IAAI,EAAG,OAAM,IAAI,MAAM,OAAO;AAElC,WAAOA;AAAA,EACT;AAEA,WAAS,KAAK,SAASI,IAAG;AACxB,WAAO,UAAU,UAAUN,MAAK,SAASM,EAAC,GAAG,YAAYN;AAAA,EAC3D;AAEA,WAAS,WAAW,SAASM,IAAG;AAC9B,WAAO,UAAU,UAAU,WAAW,SAASA,EAAC,GAAG,YAAY;AAAA,EACjE;AAEA,WAAS,OAAO,SAASA,IAAG;AAC1B,WAAO,UAAU,UAAUL,QAAO,SAASK,EAAC,GAAG,YAAYL;AAAA,EAC7D;AAEA,SAAO;AACT;AAKA,SAAS,UAAUA,OAAM;AACvB,EAAAA,QAAO,GAAGA,KAAI;AACd,MAAI,IAAIA,MAAK;AACb,MAAI,MAAMA,OAAM,IAAI,CAAC,KAAK,CAAC,MAAMA,OAAM,IAAI,CAAC,EAAG,CAAAA,QAAOA,MAAK,MAAM,GAAG,EAAE;AACtE,SAAOA,MAAK,CAAC,MAAM,MAAMA,QAAO,IAAIA,KAAI;AAC1C;AAKA,SAAS,SAASA,OAAM;AACtB,MAAI,IAAIA,MAAK;AACb,MAAI,IAAI,EAAG,QAAO;AAClB,SAAO,EAAE,IAAI,EAAG,KAAI,MAAMA,OAAM,CAAC,EAAG;AACpC,SAAOA,MAAK,MAAM,GAAG,CAAC;AACxB;AAKA,SAAS,MAAMA,OAAM,GAAG;AACtB,MAAIA,MAAK,CAAC,MAAM,KAAK;AACnB,QAAI,IAAI;AACR,WAAO,IAAI,KAAKA,MAAK,EAAE,CAAC,MAAM,KAAM,GAAE;AACtC,SAAK,IAAI,OAAO,EAAG,QAAO;AAAA,EAC5B;AACA,SAAO;AACT;;;AC9IA,SAASM,mBAAkBC,IAAG,GAAG;AAC/B,SAAOA,GAAE,WAAW,EAAE,SAAS,IAAI;AACrC;AAUA,SAAS,SAAS,GAAG;AACnB,MAAI,WAAW,EAAE;AACjB,SAAO,WAAW,SAAS,CAAC,IAAI,EAAE;AACpC;AAGA,SAAS,UAAU,GAAG;AACpB,MAAI,WAAW,EAAE;AACjB,SAAO,WAAW,SAAS,SAAS,SAAS,CAAC,IAAI,EAAE;AACtD;AAIA,SAAS,YAAY,IAAI,IAAI,OAAO;AAClC,MAAI,SAAS,SAAS,GAAG,IAAI,GAAG;AAChC,KAAG,KAAK;AACR,KAAG,KAAK;AACR,KAAG,KAAK;AACR,KAAG,KAAK;AACR,KAAG,KAAK;AACV;AAKA,SAAS,cAAc,GAAG;AACxB,MAAI,QAAQ,GACR,SAAS,GACT,WAAW,EAAE,UACb,IAAI,SAAS,QACb;AACJ,SAAO,EAAE,KAAK,GAAG;AACf,QAAI,SAAS,CAAC;AACd,MAAE,KAAK;AACP,MAAE,KAAK;AACP,aAAS,EAAE,KAAK,UAAU,EAAE;AAAA,EAC9B;AACF;AAIA,SAAS,aAAa,KAAK,GAAG,UAAU;AACtC,SAAO,IAAI,EAAE,WAAW,EAAE,SAAS,IAAI,IAAI;AAC7C;AAEA,SAAS,SAAS,MAAM,GAAG;AACzB,OAAK,IAAI;AACT,OAAK,SAAS;AACd,OAAK,WAAW;AAChB,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI;AACX;AAEA,SAAS,YAAY,OAAO,OAAO,KAAK,SAAS;AAEjD,SAAS,SAASC,OAAM;AACtB,MAAI,OAAO,IAAI,SAASA,OAAM,CAAC,GAC3B,MACA,QAAQ,CAAC,IAAI,GACb,OACA,UACA,GACA;AAEJ,SAAO,OAAO,MAAM,IAAI,GAAG;AACzB,QAAI,WAAW,KAAK,EAAE,UAAU;AAC9B,WAAK,WAAW,IAAI,MAAM,IAAI,SAAS,MAAM;AAC7C,WAAK,IAAI,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AAC3B,cAAM,KAAK,QAAQ,KAAK,SAAS,CAAC,IAAI,IAAI,SAAS,SAAS,CAAC,GAAG,CAAC,CAAC;AAClE,cAAM,SAAS;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AAEA,GAAC,KAAK,SAAS,IAAI,SAAS,MAAM,CAAC,GAAG,WAAW,CAAC,IAAI;AACtD,SAAO;AACT;AAGe,SAAR,eAAmB;AACxB,MAAI,aAAaF,oBACb,KAAK,GACL,KAAK,GACL,WAAW;AAEf,WAAS,KAAKE,OAAM;AAClB,QAAI,IAAI,SAASA,KAAI;AAGrB,MAAE,UAAU,SAAS,GAAG,EAAE,OAAO,IAAI,CAAC,EAAE;AACxC,MAAE,WAAW,UAAU;AAGvB,QAAI,SAAU,CAAAA,MAAK,WAAW,QAAQ;AAAA,SAIjC;AACH,UAAIC,QAAOD,OACPE,SAAQF,OACRG,UAASH;AACb,MAAAA,MAAK,WAAW,SAAS,MAAM;AAC7B,YAAI,KAAK,IAAIC,MAAK,EAAG,CAAAA,QAAO;AAC5B,YAAI,KAAK,IAAIC,OAAM,EAAG,CAAAA,SAAQ;AAC9B,YAAI,KAAK,QAAQC,QAAO,MAAO,CAAAA,UAAS;AAAA,MAC1C,CAAC;AACD,UAAI,IAAIF,UAASC,SAAQ,IAAI,WAAWD,OAAMC,MAAK,IAAI,GACnD,KAAK,IAAID,MAAK,GACd,KAAK,MAAMC,OAAM,IAAI,IAAI,KACzB,KAAK,MAAMC,QAAO,SAAS;AAC/B,MAAAH,MAAK,WAAW,SAAS,MAAM;AAC7B,aAAK,KAAK,KAAK,IAAI,MAAM;AACzB,aAAK,IAAI,KAAK,QAAQ;AAAA,MACxB,CAAC;AAAA,IACH;AAEA,WAAOA;AAAA,EACT;AAMA,WAAS,UAAU,GAAG;AACpB,QAAI,WAAW,EAAE,UACb,WAAW,EAAE,OAAO,UACpB,IAAI,EAAE,IAAI,SAAS,EAAE,IAAI,CAAC,IAAI;AAClC,QAAI,UAAU;AACZ,oBAAc,CAAC;AACf,UAAI,YAAY,SAAS,CAAC,EAAE,IAAI,SAAS,SAAS,SAAS,CAAC,EAAE,KAAK;AACnE,UAAI,GAAG;AACL,UAAE,IAAI,EAAE,IAAI,WAAW,EAAE,GAAG,EAAE,CAAC;AAC/B,UAAE,IAAI,EAAE,IAAI;AAAA,MACd,OAAO;AACL,UAAE,IAAI;AAAA,MACR;AAAA,IACF,WAAW,GAAG;AACZ,QAAE,IAAI,EAAE,IAAI,WAAW,EAAE,GAAG,EAAE,CAAC;AAAA,IACjC;AACA,MAAE,OAAO,IAAI,UAAU,GAAG,GAAG,EAAE,OAAO,KAAK,SAAS,CAAC,CAAC;AAAA,EACxD;AAGA,WAAS,WAAW,GAAG;AACrB,MAAE,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO;AACvB,MAAE,KAAK,EAAE,OAAO;AAAA,EAClB;AAaA,WAAS,UAAU,GAAG,GAAG,UAAU;AACjC,QAAI,GAAG;AACL,UAAI,MAAM,GACN,MAAM,GACN,MAAM,GACN,MAAM,IAAI,OAAO,SAAS,CAAC,GAC3B,MAAM,IAAI,GACV,MAAM,IAAI,GACV,MAAM,IAAI,GACV,MAAM,IAAI,GACV;AACJ,aAAO,MAAM,UAAU,GAAG,GAAG,MAAM,SAAS,GAAG,GAAG,OAAO,KAAK;AAC5D,cAAM,SAAS,GAAG;AAClB,cAAM,UAAU,GAAG;AACnB,YAAI,IAAI;AACR,gBAAQ,IAAI,IAAI,MAAM,IAAI,IAAI,MAAM,WAAW,IAAI,GAAG,IAAI,CAAC;AAC3D,YAAI,QAAQ,GAAG;AACb,sBAAY,aAAa,KAAK,GAAG,QAAQ,GAAG,GAAG,KAAK;AACpD,iBAAO;AACP,iBAAO;AAAA,QACT;AACA,eAAO,IAAI;AACX,eAAO,IAAI;AACX,eAAO,IAAI;AACX,eAAO,IAAI;AAAA,MACb;AACA,UAAI,OAAO,CAAC,UAAU,GAAG,GAAG;AAC1B,YAAI,IAAI;AACR,YAAI,KAAK,MAAM;AAAA,MACjB;AACA,UAAI,OAAO,CAAC,SAAS,GAAG,GAAG;AACzB,YAAI,IAAI;AACR,YAAI,KAAK,MAAM;AACf,mBAAW;AAAA,MACb;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,WAAS,SAAS,MAAM;AACtB,SAAK,KAAK;AACV,SAAK,IAAI,KAAK,QAAQ;AAAA,EACxB;AAEA,OAAK,aAAa,SAASI,IAAG;AAC5B,WAAO,UAAU,UAAU,aAAaA,IAAG,QAAQ;AAAA,EACrD;AAEA,OAAK,OAAO,SAASA,IAAG;AACtB,WAAO,UAAU,UAAU,WAAW,OAAO,KAAK,CAACA,GAAE,CAAC,GAAG,KAAK,CAACA,GAAE,CAAC,GAAG,QAAS,WAAW,OAAO,CAAC,IAAI,EAAE;AAAA,EACzG;AAEA,OAAK,WAAW,SAASA,IAAG;AAC1B,WAAO,UAAU,UAAU,WAAW,MAAM,KAAK,CAACA,GAAE,CAAC,GAAG,KAAK,CAACA,GAAE,CAAC,GAAG,QAAS,WAAW,CAAC,IAAI,EAAE,IAAI;AAAA,EACrG;AAEA,SAAO;AACT;;;AC5Oe,SAAR,cAAiB,QAAQ,IAAI,IAAI,IAAI,IAAI;AAC9C,MAAI,QAAQ,OAAO,UACf,MACA,IAAI,IACJ,IAAI,MAAM,QACV,IAAI,OAAO,UAAU,KAAK,MAAM,OAAO;AAE3C,SAAO,EAAE,IAAI,GAAG;AACd,WAAO,MAAM,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,KAAK;AACzC,SAAK,KAAK,IAAI,KAAK,KAAK,MAAM,KAAK,QAAQ;AAAA,EAC7C;AACF;;;ACRO,IAAI,OAAO,IAAI,KAAK,KAAK,CAAC,KAAK;AAE/B,SAAS,cAAc,OAAO,QAAQ,IAAI,IAAI,IAAI,IAAI;AAC3D,MAAI,OAAO,CAAC,GACR,QAAQ,OAAO,UACf,KACA,WACA,KAAK,GACL,KAAK,GACL,IAAI,MAAM,QACV,IAAI,IACJ,QAAQ,OAAO,OACf,UACA,UACA,UACA,UACA,UACA,OACA;AAEJ,SAAO,KAAK,GAAG;AACb,SAAK,KAAK,IAAI,KAAK,KAAK;AAGxB;AAAG,iBAAW,MAAM,IAAI,EAAE;AAAA,WAAc,CAAC,YAAY,KAAK;AAC1D,eAAW,WAAW;AACtB,YAAQ,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE,KAAK,QAAQ;AAC9C,WAAO,WAAW,WAAW;AAC7B,eAAW,KAAK,IAAI,WAAW,MAAM,OAAO,QAAQ;AAGpD,WAAO,KAAK,GAAG,EAAE,IAAI;AACnB,kBAAY,YAAY,MAAM,EAAE,EAAE;AAClC,UAAI,YAAY,SAAU,YAAW;AACrC,UAAI,YAAY,SAAU,YAAW;AACrC,aAAO,WAAW,WAAW;AAC7B,iBAAW,KAAK,IAAI,WAAW,MAAM,OAAO,QAAQ;AACpD,UAAI,WAAW,UAAU;AAAE,oBAAY;AAAW;AAAA,MAAO;AACzD,iBAAW;AAAA,IACb;AAGA,SAAK,KAAK,MAAM,EAAC,OAAO,UAAU,MAAM,KAAK,IAAI,UAAU,MAAM,MAAM,IAAI,EAAE,EAAC,CAAC;AAC/E,QAAI,IAAI,KAAM,cAAY,KAAK,IAAI,IAAI,IAAI,QAAQ,MAAM,KAAK,WAAW,QAAQ,EAAE;AAAA,QAC9E,eAAa,KAAK,IAAI,IAAI,QAAQ,MAAM,KAAK,WAAW,QAAQ,IAAI,EAAE;AAC3E,aAAS,UAAU,KAAK;AAAA,EAC1B;AAEA,SAAO;AACT;AAEA,IAAO,mBAAS,SAASC,SAAO,OAAO;AAErC,WAAS,SAAS,QAAQ,IAAI,IAAI,IAAI,IAAI;AACxC,kBAAc,OAAO,QAAQ,IAAI,IAAI,IAAI,EAAE;AAAA,EAC7C;AAEA,WAAS,QAAQ,SAASC,IAAG;AAC3B,WAAOD,UAAQC,KAAI,CAACA,MAAK,IAAIA,KAAI,CAAC;AAAA,EACpC;AAEA,SAAO;AACT,EAAG,GAAG;;;AC5DS,SAAR,kBAAmB;AACxB,MAAI,OAAO,kBACP,QAAQ,OACR,KAAK,GACL,KAAK,GACL,eAAe,CAAC,CAAC,GACjB,eAAe,cACf,aAAa,cACb,eAAe,cACf,gBAAgB,cAChB,cAAc;AAElB,WAAS,QAAQC,OAAM;AACrB,IAAAA,MAAK,KACLA,MAAK,KAAK;AACV,IAAAA,MAAK,KAAK;AACV,IAAAA,MAAK,KAAK;AACV,IAAAA,MAAK,WAAW,YAAY;AAC5B,mBAAe,CAAC,CAAC;AACjB,QAAI,MAAO,CAAAA,MAAK,WAAWC,cAAS;AACpC,WAAOD;AAAA,EACT;AAEA,WAAS,aAAa,MAAM;AAC1B,QAAI,IAAI,aAAa,KAAK,KAAK,GAC3B,KAAK,KAAK,KAAK,GACf,KAAK,KAAK,KAAK,GACf,KAAK,KAAK,KAAK,GACf,KAAK,KAAK,KAAK;AACnB,QAAI,KAAK,GAAI,MAAK,MAAM,KAAK,MAAM;AACnC,QAAI,KAAK,GAAI,MAAK,MAAM,KAAK,MAAM;AACnC,SAAK,KAAK;AACV,SAAK,KAAK;AACV,SAAK,KAAK;AACV,SAAK,KAAK;AACV,QAAI,KAAK,UAAU;AACjB,UAAI,aAAa,KAAK,QAAQ,CAAC,IAAI,aAAa,IAAI,IAAI;AACxD,YAAM,YAAY,IAAI,IAAI;AAC1B,YAAM,WAAW,IAAI,IAAI;AACzB,YAAM,aAAa,IAAI,IAAI;AAC3B,YAAM,cAAc,IAAI,IAAI;AAC5B,UAAI,KAAK,GAAI,MAAK,MAAM,KAAK,MAAM;AACnC,UAAI,KAAK,GAAI,MAAK,MAAM,KAAK,MAAM;AACnC,WAAK,MAAM,IAAI,IAAI,IAAI,EAAE;AAAA,IAC3B;AAAA,EACF;AAEA,UAAQ,QAAQ,SAASE,IAAG;AAC1B,WAAO,UAAU,UAAU,QAAQ,CAAC,CAACA,IAAG,WAAW;AAAA,EACrD;AAEA,UAAQ,OAAO,SAASA,IAAG;AACzB,WAAO,UAAU,UAAU,KAAK,CAACA,GAAE,CAAC,GAAG,KAAK,CAACA,GAAE,CAAC,GAAG,WAAW,CAAC,IAAI,EAAE;AAAA,EACvE;AAEA,UAAQ,OAAO,SAASA,IAAG;AACzB,WAAO,UAAU,UAAU,OAAO,SAASA,EAAC,GAAG,WAAW;AAAA,EAC5D;AAEA,UAAQ,UAAU,SAASA,IAAG;AAC5B,WAAO,UAAU,SAAS,QAAQ,aAAaA,EAAC,EAAE,aAAaA,EAAC,IAAI,QAAQ,aAAa;AAAA,EAC3F;AAEA,UAAQ,eAAe,SAASA,IAAG;AACjC,WAAO,UAAU,UAAU,eAAe,OAAOA,OAAM,aAAaA,KAAIC,kBAAS,CAACD,EAAC,GAAG,WAAW;AAAA,EACnG;AAEA,UAAQ,eAAe,SAASA,IAAG;AACjC,WAAO,UAAU,SAAS,QAAQ,WAAWA,EAAC,EAAE,aAAaA,EAAC,EAAE,cAAcA,EAAC,EAAE,YAAYA,EAAC,IAAI,QAAQ,WAAW;AAAA,EACvH;AAEA,UAAQ,aAAa,SAASA,IAAG;AAC/B,WAAO,UAAU,UAAU,aAAa,OAAOA,OAAM,aAAaA,KAAIC,kBAAS,CAACD,EAAC,GAAG,WAAW;AAAA,EACjG;AAEA,UAAQ,eAAe,SAASA,IAAG;AACjC,WAAO,UAAU,UAAU,eAAe,OAAOA,OAAM,aAAaA,KAAIC,kBAAS,CAACD,EAAC,GAAG,WAAW;AAAA,EACnG;AAEA,UAAQ,gBAAgB,SAASA,IAAG;AAClC,WAAO,UAAU,UAAU,gBAAgB,OAAOA,OAAM,aAAaA,KAAIC,kBAAS,CAACD,EAAC,GAAG,WAAW;AAAA,EACpG;AAEA,UAAQ,cAAc,SAASA,IAAG;AAChC,WAAO,UAAU,UAAU,cAAc,OAAOA,OAAM,aAAaA,KAAIC,kBAAS,CAACD,EAAC,GAAG,WAAW;AAAA,EAClG;AAEA,SAAO;AACT;;;AC7Fe,SAAR,eAAiB,QAAQ,IAAI,IAAI,IAAI,IAAI;AAC9C,MAAI,QAAQ,OAAO,UACf,GAAG,IAAI,MAAM,QACbE,MAAK,OAAO,IAAI,MAAM,IAAI,CAAC;AAE/B,OAAK,KAAK,CAAC,IAAIA,OAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtC,SAAK,IAAI,CAAC,IAAIA,QAAO,MAAM,CAAC,EAAE;AAAA,EAChC;AAEA,YAAU,GAAG,GAAG,OAAO,OAAO,IAAI,IAAI,IAAI,EAAE;AAE5C,WAAS,UAAUC,IAAG,GAAG,OAAOC,KAAIC,KAAIC,KAAIC,KAAI;AAC9C,QAAIJ,MAAK,IAAI,GAAG;AACd,UAAI,OAAO,MAAMA,EAAC;AAClB,WAAK,KAAKC,KAAI,KAAK,KAAKC;AACxB,WAAK,KAAKC,KAAI,KAAK,KAAKC;AACxB;AAAA,IACF;AAEA,QAAI,cAAc,KAAKJ,EAAC,GACpB,cAAe,QAAQ,IAAK,aAC5B,IAAIA,KAAI,GACR,KAAK,IAAI;AAEb,WAAO,IAAI,IAAI;AACb,UAAI,MAAM,IAAI,OAAO;AACrB,UAAI,KAAK,GAAG,IAAI,YAAa,KAAI,MAAM;AAAA,UAClC,MAAK;AAAA,IACZ;AAEA,QAAK,cAAc,KAAK,IAAI,CAAC,IAAM,KAAK,CAAC,IAAI,eAAgBA,KAAI,IAAI,EAAG,GAAE;AAE1E,QAAI,YAAY,KAAK,CAAC,IAAI,aACtB,aAAa,QAAQ;AAEzB,QAAKG,MAAKF,MAAOG,MAAKF,KAAK;AACzB,UAAI,KAAK,SAASD,MAAK,aAAaE,MAAK,aAAa,QAAQA;AAC9D,gBAAUH,IAAG,GAAG,WAAWC,KAAIC,KAAI,IAAIE,GAAE;AACzC,gBAAU,GAAG,GAAG,YAAY,IAAIF,KAAIC,KAAIC,GAAE;AAAA,IAC5C,OAAO;AACL,UAAI,KAAK,SAASF,MAAK,aAAaE,MAAK,aAAa,QAAQA;AAC9D,gBAAUJ,IAAG,GAAG,WAAWC,KAAIC,KAAIC,KAAI,EAAE;AACzC,gBAAU,GAAG,GAAG,YAAYF,KAAI,IAAIE,KAAIC,GAAE;AAAA,IAC5C;AAAA,EACF;AACF;;;AC1Ce,SAAR,kBAAiB,QAAQ,IAAI,IAAI,IAAI,IAAI;AAC9C,GAAC,OAAO,QAAQ,IAAI,gBAAQ,cAAM,QAAQ,IAAI,IAAI,IAAI,EAAE;AAC1D;;;ACDA,IAAO,qBAAS,SAASC,SAAO,OAAO;AAErC,WAAS,WAAW,QAAQ,IAAI,IAAI,IAAI,IAAI;AAC1C,SAAK,OAAO,OAAO,cAAe,KAAK,UAAU,OAAQ;AACvD,UAAI,MACA,KACA,OACA,GACA,IAAI,IACJ,GACAC,KAAI,KAAK,QACT,QAAQ,OAAO;AAEnB,aAAO,EAAE,IAAIA,IAAG;AACd,cAAM,KAAK,CAAC,GAAG,QAAQ,IAAI;AAC3B,aAAK,IAAI,IAAI,QAAQ,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,EAAE,EAAG,KAAI,SAAS,MAAM,CAAC,EAAE;AAC5E,YAAI,IAAI,KAAM,cAAY,KAAK,IAAI,IAAI,IAAI,QAAQ,OAAO,KAAK,MAAM,IAAI,QAAQ,QAAQ,EAAE;AAAA,YACtF,eAAa,KAAK,IAAI,IAAI,QAAQ,OAAO,KAAK,MAAM,IAAI,QAAQ,QAAQ,IAAI,EAAE;AACnF,iBAAS,IAAI;AAAA,MACf;AAAA,IACF,OAAO;AACL,aAAO,YAAY,OAAO,cAAc,OAAO,QAAQ,IAAI,IAAI,IAAI,EAAE;AACrE,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAEA,aAAW,QAAQ,SAASC,IAAG;AAC7B,WAAOF,UAAQE,KAAI,CAACA,MAAK,IAAIA,KAAI,CAAC;AAAA,EACpC;AAEA,SAAO;AACT,EAAG,GAAG;;;ACnCS,SAARC,cAAiB,SAAS;AAC/B,MAAI,IAAI,IACJ,IAAI,QAAQ,QACZC,IACA,IAAI,QAAQ,IAAI,CAAC,GACjB,OAAO;AAEX,SAAO,EAAE,IAAI,GAAG;AACd,IAAAA,KAAI;AACJ,QAAI,QAAQ,CAAC;AACb,YAAQA,GAAE,CAAC,IAAI,EAAE,CAAC,IAAIA,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,EAClC;AAEA,SAAO,OAAO;AAChB;;;ACde,SAARC,kBAAiB,SAAS;AAC/B,MAAI,IAAI,IACJ,IAAI,QAAQ,QACZC,KAAI,GACJC,KAAI,GACJC,IACA,IAAI,QAAQ,IAAI,CAAC,GACjBC,IACA,IAAI;AAER,SAAO,EAAE,IAAI,GAAG;AACd,IAAAD,KAAI;AACJ,QAAI,QAAQ,CAAC;AACb,SAAKC,KAAID,GAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA,GAAE,CAAC;AACjC,IAAAF,OAAME,GAAE,CAAC,IAAI,EAAE,CAAC,KAAKC;AACrB,IAAAF,OAAMC,GAAE,CAAC,IAAI,EAAE,CAAC,KAAKC;AAAA,EACvB;AAEA,SAAO,KAAK,GAAG,CAACH,KAAI,GAAGC,KAAI,CAAC;AAC9B;;;ACfe,SAARG,eAAiBC,IAAG,GAAGC,IAAG;AAC/B,UAAQ,EAAE,CAAC,IAAID,GAAE,CAAC,MAAMC,GAAE,CAAC,IAAID,GAAE,CAAC,MAAM,EAAE,CAAC,IAAIA,GAAE,CAAC,MAAMC,GAAE,CAAC,IAAID,GAAE,CAAC;AACpE;;;ACJA,SAAS,mBAAmBE,IAAG,GAAG;AAChC,SAAOA,GAAE,CAAC,IAAI,EAAE,CAAC,KAAKA,GAAE,CAAC,IAAI,EAAE,CAAC;AAClC;AAKA,SAAS,wBAAwB,QAAQ;AACvC,QAAM,IAAI,OAAO,QACbC,WAAU,CAAC,GAAG,CAAC;AACnB,MAAI,OAAO,GAAG;AAEd,OAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,WAAO,OAAO,KAAKC,eAAM,OAAOD,SAAQ,OAAO,CAAC,CAAC,GAAG,OAAOA,SAAQ,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,KAAK,EAAG,GAAE;AAClG,IAAAA,SAAQ,MAAM,IAAI;AAAA,EACpB;AAEA,SAAOA,SAAQ,MAAM,GAAG,IAAI;AAC9B;AAEe,SAAR,aAAiB,QAAQ;AAC9B,OAAK,IAAI,OAAO,UAAU,EAAG,QAAO;AAEpC,MAAI,GACA,GACA,eAAe,IAAI,MAAM,CAAC,GAC1B,gBAAgB,IAAI,MAAM,CAAC;AAE/B,OAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,cAAa,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC;AAC1E,eAAa,KAAK,kBAAkB;AACpC,OAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,eAAc,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;AAEnF,MAAI,eAAe,wBAAwB,YAAY,GACnD,eAAe,wBAAwB,aAAa;AAGxD,MAAI,WAAW,aAAa,CAAC,MAAM,aAAa,CAAC,GAC7C,YAAY,aAAa,aAAa,SAAS,CAAC,MAAM,aAAa,aAAa,SAAS,CAAC,GAC1F,OAAO,CAAC;AAIZ,OAAK,IAAI,aAAa,SAAS,GAAG,KAAK,GAAG,EAAE,EAAG,MAAK,KAAK,OAAO,aAAa,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACjG,OAAK,IAAI,CAAC,UAAU,IAAI,aAAa,SAAS,WAAW,EAAE,EAAG,MAAK,KAAK,OAAO,aAAa,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAEhH,SAAO;AACT;;;AChDe,SAARE,kBAAiB,SAASC,QAAO;AACtC,MAAI,IAAI,QAAQ,QACZ,IAAI,QAAQ,IAAI,CAAC,GACjBC,KAAID,OAAM,CAAC,GAAGE,KAAIF,OAAM,CAAC,GACzB,KAAK,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,GACnB,IAAI,IACJ,SAAS;AAEb,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,QAAI,QAAQ,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC;AACnC,QAAM,KAAKE,OAAQ,KAAKA,MAAQD,MAAK,KAAK,OAAOC,KAAI,OAAO,KAAK,MAAM,GAAK,UAAS,CAAC;AACtF,SAAK,IAAI,KAAK;AAAA,EAChB;AAEA,SAAO;AACT;;;ACfe,SAARC,gBAAiB,SAAS;AAC/B,MAAI,IAAI,IACJ,IAAI,QAAQ,QACZ,IAAI,QAAQ,IAAI,CAAC,GACjB,IACA,IACA,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,YAAY;AAEhB,SAAO,EAAE,IAAI,GAAG;AACd,SAAK;AACL,SAAK;AACL,QAAI,QAAQ,CAAC;AACb,SAAK,EAAE,CAAC;AACR,SAAK,EAAE,CAAC;AACR,UAAM;AACN,UAAM;AACN,iBAAa,KAAK,MAAM,IAAI,EAAE;AAAA,EAChC;AAEA,SAAO;AACT;;;ACtBA,IAAO,wBAAQ,KAAK;;;ACEpB,IAAO,kBAAS,SAAS,oBAAoB,QAAQ;AACnD,WAAS,cAAcC,MAAKC,MAAK;AAC/B,IAAAD,OAAMA,QAAO,OAAO,IAAI,CAACA;AACzB,IAAAC,OAAMA,QAAO,OAAO,IAAI,CAACA;AACzB,QAAI,UAAU,WAAW,EAAG,CAAAA,OAAMD,MAAKA,OAAM;AAAA,QACxC,CAAAC,QAAOD;AACZ,WAAO,WAAW;AAChB,aAAO,OAAO,IAAIC,OAAMD;AAAA,IAC1B;AAAA,EACF;AAEA,gBAAc,SAAS;AAEvB,SAAO;AACT,EAAG,qBAAa;;;ACdhB,IAAO,cAAS,SAAS,gBAAgB,QAAQ;AAC/C,WAAS,UAAUE,MAAKC,MAAK;AAC3B,QAAI,UAAU,SAAS,EAAG,CAAAA,OAAMD,MAAKA,OAAM;AAC3C,IAAAA,OAAM,KAAK,MAAMA,IAAG;AACpB,IAAAC,OAAM,KAAK,MAAMA,IAAG,IAAID;AACxB,WAAO,WAAW;AAChB,aAAO,KAAK,MAAM,OAAO,IAAIC,OAAMD,IAAG;AAAA,IACxC;AAAA,EACF;AAEA,YAAU,SAAS;AAEnB,SAAO;AACT,EAAG,qBAAa;;;ACbhB,IAAO,iBAAS,SAAS,mBAAmB,QAAQ;AAClD,WAAS,aAAa,IAAI,OAAO;AAC/B,QAAIE,IAAG;AACP,SAAK,MAAM,OAAO,IAAI,CAAC;AACvB,YAAQ,SAAS,OAAO,IAAI,CAAC;AAC7B,WAAO,WAAW;AAChB,UAAIC;AAGJ,UAAID,MAAK,KAAM,CAAAC,KAAID,IAAGA,KAAI;AAAA,UAGrB,IAAG;AACN,QAAAA,KAAI,OAAO,IAAI,IAAI;AACnB,QAAAC,KAAI,OAAO,IAAI,IAAI;AACnB,YAAID,KAAIA,KAAIC,KAAIA;AAAA,MAClB,SAAS,CAAC,KAAK,IAAI;AAEnB,aAAO,KAAK,QAAQA,KAAI,KAAK,KAAK,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC;AAAA,IACxD;AAAA,EACF;AAEA,eAAa,SAAS;AAEtB,SAAO;AACT,EAAG,qBAAa;;;ACxBhB,IAAO,oBAAS,SAAS,sBAAsB,QAAQ;AACrD,MAAI,IAAI,eAAO,OAAO,MAAM;AAE5B,WAAS,kBAAkB;AACzB,QAAI,eAAe,EAAE,MAAM,MAAM,SAAS;AAC1C,WAAO,WAAW;AAChB,aAAO,KAAK,IAAI,aAAa,CAAC;AAAA,IAChC;AAAA,EACF;AAEA,kBAAgB,SAAS;AAEzB,SAAO;AACT,EAAG,qBAAa;;;ACdhB,IAAO,oBAAS,SAAS,sBAAsB,QAAQ;AACrD,WAAS,gBAAgB,GAAG;AAC1B,SAAK,IAAI,CAAC,MAAM,EAAG,QAAO,MAAM;AAChC,WAAO,WAAW;AAChB,eAASC,OAAM,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,CAAAA,QAAO,OAAO;AACnD,aAAOA,OAAM,IAAI,OAAO;AAAA,IAC1B;AAAA,EACF;AAEA,kBAAgB,SAAS;AAEzB,SAAO;AACT,EAAG,qBAAa;;;ACXhB,IAAO,gBAAS,SAAS,kBAAkB,QAAQ;AACjD,MAAI,IAAI,kBAAU,OAAO,MAAM;AAE/B,WAAS,YAAY,GAAG;AAEtB,SAAK,IAAI,CAAC,OAAO,EAAG,QAAO;AAC3B,QAAI,kBAAkB,EAAE,CAAC;AACzB,WAAO,WAAW;AAChB,aAAO,gBAAgB,IAAI;AAAA,IAC7B;AAAA,EACF;AAEA,cAAY,SAAS;AAErB,SAAO;AACT,EAAG,qBAAa;;;AChBhB,IAAO,sBAAS,SAAS,wBAAwB,QAAQ;AACvD,WAAS,kBAAkB,QAAQ;AACjC,WAAO,WAAW;AAChB,aAAO,CAAC,KAAK,MAAM,CAAC,OAAO,CAAC,IAAI;AAAA,IAClC;AAAA,EACF;AAEA,oBAAkB,SAAS;AAE3B,SAAO;AACT,EAAG,qBAAa;;;ACVhB,IAAO,iBAAS,SAAS,mBAAmB,QAAQ;AAClD,WAAS,aAAa,OAAO;AAC3B,SAAK,QAAQ,CAAC,SAAS,EAAG,OAAM,IAAI,WAAW,eAAe;AAC9D,YAAQ,IAAI,CAAC;AACb,WAAO,WAAW;AAChB,aAAO,KAAK,IAAI,IAAI,OAAO,GAAG,KAAK;AAAA,IACrC;AAAA,EACF;AAEA,eAAa,SAAS;AAEtB,SAAO;AACT,EAAG,qBAAa;;;ACZhB,IAAO,oBAAS,SAAS,sBAAsB,QAAQ;AACrD,WAAS,gBAAgB,GAAG;AAC1B,SAAK,IAAI,CAAC,KAAK,KAAK,IAAI,EAAG,OAAM,IAAI,WAAW,WAAW;AAC3D,WAAO,WAAW;AAChB,aAAO,KAAK,MAAM,OAAO,IAAI,CAAC;AAAA,IAChC;AAAA,EACF;AAEA,kBAAgB,SAAS;AAEzB,SAAO;AACT,EAAG,qBAAa;;;ACXhB,IAAO,oBAAS,SAAS,sBAAsB,QAAQ;AACrD,WAAS,gBAAgB,GAAG;AAC1B,SAAK,IAAI,CAAC,KAAK,KAAK,IAAI,EAAG,OAAM,IAAI,WAAW,WAAW;AAC3D,QAAI,MAAM,EAAG,QAAO,MAAM;AAC1B,QAAI,MAAM,EAAG,QAAO,MAAM;AAC1B,QAAI,KAAK,MAAM,CAAC,CAAC;AACjB,WAAO,WAAW;AAChB,aAAO,IAAI,KAAK,MAAM,KAAK,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC;AAAA,IACjD;AAAA,EACF;AAEA,kBAAgB,SAAS;AAEzB,SAAO;AACT,EAAG,qBAAa;;;ACbhB,IAAO,gBAAS,SAAS,kBAAkB,QAAQ;AACjD,MAAI,eAAe,eAAO,OAAO,MAAM,EAAE;AAEzC,WAAS,YAAY,GAAG,OAAO;AAC7B,SAAK,IAAI,CAAC,KAAK,EAAG,OAAM,IAAI,WAAW,WAAW;AAElD,QAAI,MAAM,EAAG,QAAO,MAAM;AAC1B,YAAQ,SAAS,OAAO,IAAI,CAAC;AAE7B,QAAI,MAAM,EAAG,QAAO,MAAM,CAAC,KAAK,MAAM,CAAC,OAAO,CAAC,IAAI;AAEnD,QAAI,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,GAC9BC,KAAI,KAAK,IAAI,KAAK,KAAK,CAAC,IACxB,aAAa,IAAI,IAAI,MAAM,KAAK,IAAI,OAAO,GAAG,IAAI,CAAC,IAAI,MAAM;AACjE,WAAO,WAAW;AAChB,SAAG;AACD,WAAG;AACD,cAAIC,KAAI,aAAa,GACjB,IAAI,IAAID,KAAIC;AAAA,QAClB,SAAS,KAAK;AACd,aAAK,IAAI;AACT,YAAI,IAAI,IAAI,OAAO;AAAA,MACrB,SAAS,KAAK,IAAI,SAASA,KAAIA,KAAIA,KAAIA,MAAK,KAAK,IAAI,CAAC,KAAK,MAAMA,KAAIA,KAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC;AAChG,aAAO,IAAI,IAAI,WAAW,IAAI;AAAA,IAChC;AAAA,EACF;AAEA,cAAY,SAAS;AAErB,SAAO;AACT,EAAG,qBAAa;;;AC9BhB,IAAO,eAAS,SAAS,iBAAiB,QAAQ;AAChD,MAAI,IAAI,cAAM,OAAO,MAAM;AAE3B,WAAS,WAAW,OAAO,MAAM;AAC/B,QAAIC,KAAI,EAAE,KAAK,GACXC,KAAI,EAAE,IAAI;AACd,WAAO,WAAW;AAChB,UAAIC,KAAIF,GAAE;AACV,aAAOE,OAAM,IAAI,IAAIA,MAAKA,KAAID,GAAE;AAAA,IAClC;AAAA,EACF;AAEA,aAAW,SAAS;AAEpB,SAAO;AACT,EAAG,qBAAa;;;ACdhB,IAAO,mBAAS,SAAS,qBAAqB,QAAQ;AACpD,MAAI,IAAI,kBAAU,OAAO,MAAM,GAC3B,IAAI,aAAK,OAAO,MAAM;AAE1B,WAAS,eAAe,GAAG,GAAG;AAC5B,QAAI,CAAC;AACL,SAAK,IAAI,CAAC,MAAM,EAAG,QAAO,MAAM;AAChC,QAAI,KAAK,EAAG,QAAO,MAAM;AACzB,WAAO,WAAW;AAChB,UAAI,MAAM,GAAG,KAAK,GAAG,KAAK;AAC1B,aAAO,KAAK,KAAK,MAAM,MAAM,IAAI,MAAM,IAAI;AACzC,YAAI,IAAI,KAAK,OAAO,KAAK,KAAK,EAAE,GAC5BE,KAAI,EAAE,GAAG,KAAK,IAAI,CAAC,EAAE;AACzB,YAAIA,MAAK,IAAI;AACX,iBAAO;AACP,gBAAM;AACN,gBAAM,KAAKA,OAAM,IAAIA;AAAA,QACvB,OAAO;AACL,eAAK,IAAI;AACT,gBAAMA;AAAA,QACR;AAAA,MACF;AACA,UAAI,OAAO,KAAK,KACZ,SAAS,OAAO,KAAK,IAAI,IACzB,IAAI,EAAE,MAAM;AAChB,eAAS,IAAI,EAAE,GAAG,IAAI,GAAG,KAAK,IAAI,EAAE,EAAG,MAAK,EAAE;AAC9C,aAAO,OAAO,OAAO,IAAI,KAAK;AAAA,IAChC;AAAA,EACF;AAEA,iBAAe,SAAS;AAExB,SAAO;AACT,EAAG,qBAAa;;;ACnChB,IAAO,kBAAS,SAAS,oBAAoB,QAAQ;AACnD,WAAS,cAAc,GAAGC,IAAG,GAAG;AAC9B,QAAI;AACJ,SAAK,IAAI,CAAC,OAAO,GAAG;AAClB,kBAAY,CAAAC,OAAK,CAAC,KAAK,IAAIA,EAAC;AAAA,IAC9B,OAAO;AACL,UAAI,IAAI;AACR,kBAAY,CAAAA,OAAK,KAAK,IAAIA,IAAG,CAAC;AAAA,IAChC;AACA,IAAAD,KAAIA,MAAK,OAAO,IAAI,CAACA;AACrB,QAAI,KAAK,OAAO,IAAI,CAAC;AACrB,WAAO,WAAW;AAChB,aAAOA,KAAI,IAAI,UAAU,CAAC,KAAK,MAAM,CAAC,OAAO,CAAC,CAAC;AAAA,IACjD;AAAA,EACF;AAEA,gBAAc,SAAS;AAEvB,SAAO;AACT,EAAG,qBAAa;;;ACnBhB,IAAO,iBAAS,SAAS,mBAAmB,QAAQ;AAClD,WAAS,aAAaE,IAAG,GAAG;AAC1B,IAAAA,KAAIA,MAAK,OAAO,IAAI,CAACA;AACrB,QAAI,KAAK,OAAO,IAAI,CAAC;AACrB,WAAO,WAAW;AAChB,aAAOA,KAAI,IAAI,KAAK,IAAI,KAAK,KAAK,OAAO,CAAC;AAAA,IAC5C;AAAA,EACF;AAEA,eAAa,SAAS;AAEtB,SAAO;AACT,EAAG,qBAAa;;;ACZhB,IAAO,mBAAS,SAAS,qBAAqB,QAAQ;AACpD,WAAS,eAAeC,IAAG,GAAG;AAC5B,IAAAA,KAAIA,MAAK,OAAO,IAAI,CAACA;AACrB,QAAI,KAAK,OAAO,IAAI,CAAC;AACrB,WAAO,WAAW;AAChB,UAAI,IAAI,OAAO;AACf,aAAOA,KAAI,IAAI,KAAK,IAAI,KAAK,IAAI,EAAE;AAAA,IACrC;AAAA,EACF;AAEA,iBAAe,SAAS;AAExB,SAAO;AACT,EAAG,qBAAa;;;ACXhB,IAAO,kBAAS,SAAS,oBAAoB,QAAQ;AACnD,MAAI,IAAI,cAAM,OAAO,MAAM,GACvB,IAAI,iBAAS,OAAO,MAAM;AAE9B,WAAS,cAAc,QAAQ;AAC7B,WAAO,WAAW;AAChB,UAAI,MAAM,GAAG,IAAI;AACjB,aAAO,IAAI,IAAI;AACb,YAAI,IAAI,KAAK,MAAM,QAAQ,CAAC,GACxB,IAAI,EAAE,CAAC,EAAE;AACb,YAAI,IAAI,EAAG,QAAO,MAAM,EAAE,IAAI,GAAG,IAAI,CAAC,EAAE;AACxC,eAAO;AACP,aAAK;AAAA,MACP;AACA,eAAS,IAAI,CAAC,KAAK,MAAM,CAAC,OAAO,CAAC,GAAG,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG,MAAK,KAAK,MAAM,CAAC,OAAO,CAAC;AAClF,aAAO,MAAM;AAAA,IACf;AAAA,EACF;AAEA,gBAAc,SAAS;AAEvB,SAAO;AACT,EAAG,qBAAa;;;ACzBhB,IAAM,MAAM;AACZ,IAAM,MAAM;AACZ,IAAM,MAAM,IAAI;AAED,SAAR,IAAqB,OAAO,KAAK,OAAO,GAAG;AAChD,MAAI,SAAS,KAAK,QAAQ,OAAO,IAAI,OAAO,MAAM,KAAK,IAAI,IAAI,KAAK;AACpE,SAAO,OAAO,QAAQ,MAAM,QAAQ,MAAM,GAAG,OAAO,UAAU;AAChE;;;ACRA,IAAOC,oBAAQ,CAAAC,OAAK,MAAMA;;;ACAX,SAAR,UAA2BC,OAAM;AAAA,EACtC;AAAA,EACA;AAAA,EACA,WAAAC;AAAA,EACA,UAAAC;AACF,GAAG;AACD,SAAO,iBAAiB,MAAM;AAAA,IAC5B,MAAM,EAAC,OAAOF,OAAM,YAAY,MAAM,cAAc,KAAI;AAAA,IACxD,aAAa,EAAC,OAAO,aAAa,YAAY,MAAM,cAAc,KAAI;AAAA,IACtE,QAAQ,EAAC,OAAO,QAAQ,YAAY,MAAM,cAAc,KAAI;AAAA,IAC5D,WAAW,EAAC,OAAOC,YAAW,YAAY,MAAM,cAAc,KAAI;AAAA,IAClE,GAAG,EAAC,OAAOC,UAAQ;AAAA,EACrB,CAAC;AACH;;;ACbO,SAAS,UAAU,GAAGC,IAAGC,IAAG;AACjC,OAAK,IAAI;AACT,OAAK,IAAID;AACT,OAAK,IAAIC;AACX;AAEA,UAAU,YAAY;AAAA,EACpB,aAAa;AAAA,EACb,OAAO,SAAS,GAAG;AACjB,WAAO,MAAM,IAAI,OAAO,IAAI,UAAU,KAAK,IAAI,GAAG,KAAK,GAAG,KAAK,CAAC;AAAA,EAClE;AAAA,EACA,WAAW,SAASD,IAAGC,IAAG;AACxB,WAAOD,OAAM,IAAIC,OAAM,IAAI,OAAO,IAAI,UAAU,KAAK,GAAG,KAAK,IAAI,KAAK,IAAID,IAAG,KAAK,IAAI,KAAK,IAAIC,EAAC;AAAA,EAClG;AAAA,EACA,OAAO,SAASC,QAAO;AACrB,WAAO,CAACA,OAAM,CAAC,IAAI,KAAK,IAAI,KAAK,GAAGA,OAAM,CAAC,IAAI,KAAK,IAAI,KAAK,CAAC;AAAA,EAChE;AAAA,EACA,QAAQ,SAASF,IAAG;AAClB,WAAOA,KAAI,KAAK,IAAI,KAAK;AAAA,EAC3B;AAAA,EACA,QAAQ,SAASC,IAAG;AAClB,WAAOA,KAAI,KAAK,IAAI,KAAK;AAAA,EAC3B;AAAA,EACA,QAAQ,SAAS,UAAU;AACzB,WAAO,EAAE,SAAS,CAAC,IAAI,KAAK,KAAK,KAAK,IAAI,SAAS,CAAC,IAAI,KAAK,KAAK,KAAK,CAAC;AAAA,EAC1E;AAAA,EACA,SAAS,SAASD,IAAG;AACnB,YAAQA,KAAI,KAAK,KAAK,KAAK;AAAA,EAC7B;AAAA,EACA,SAAS,SAASC,IAAG;AACnB,YAAQA,KAAI,KAAK,KAAK,KAAK;AAAA,EAC7B;AAAA,EACA,UAAU,SAASD,IAAG;AACpB,WAAOA,GAAE,KAAK,EAAE,OAAOA,GAAE,MAAM,EAAE,IAAI,KAAK,SAAS,IAAI,EAAE,IAAIA,GAAE,QAAQA,EAAC,CAAC;AAAA,EAC3E;AAAA,EACA,UAAU,SAASC,IAAG;AACpB,WAAOA,GAAE,KAAK,EAAE,OAAOA,GAAE,MAAM,EAAE,IAAI,KAAK,SAAS,IAAI,EAAE,IAAIA,GAAE,QAAQA,EAAC,CAAC;AAAA,EAC3E;AAAA,EACA,UAAU,WAAW;AACnB,WAAO,eAAe,KAAK,IAAI,MAAM,KAAK,IAAI,aAAa,KAAK,IAAI;AAAA,EACtE;AACF;AAEO,IAAIE,YAAW,IAAI,UAAU,GAAG,GAAG,CAAC;AAE3C,UAAU,YAAY,UAAU;AAEjB,SAAR,UAA2B,MAAM;AACtC,SAAO,CAAC,KAAK,OAAQ,KAAI,EAAE,OAAO,KAAK,YAAa,QAAOA;AAC3D,SAAO,KAAK;AACd;;;AClDO,SAASC,eAAc,OAAO;AACnC,QAAM,yBAAyB;AACjC;AAEe,SAARC,iBAAiB,OAAO;AAC7B,QAAM,eAAe;AACrB,QAAM,yBAAyB;AACjC;;;ACKA,SAASC,eAAc,OAAO;AAC5B,UAAQ,CAAC,MAAM,WAAW,MAAM,SAAS,YAAY,CAAC,MAAM;AAC9D;AAEA,SAASC,iBAAgB;AACvB,MAAI,IAAI;AACR,MAAI,aAAa,YAAY;AAC3B,QAAI,EAAE,mBAAmB;AACzB,QAAI,EAAE,aAAa,SAAS,GAAG;AAC7B,UAAI,EAAE,QAAQ;AACd,aAAO,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC;AAAA,IACrD;AACA,WAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,MAAM,QAAQ,OAAO,EAAE,OAAO,QAAQ,KAAK,CAAC;AAAA,EACjE;AACA,SAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,aAAa,EAAE,YAAY,CAAC;AACjD;AAEA,SAAS,mBAAmB;AAC1B,SAAO,KAAK,UAAUC;AACxB;AAEA,SAAS,kBAAkB,OAAO;AAChC,SAAO,CAAC,MAAM,UAAU,MAAM,cAAc,IAAI,OAAO,MAAM,YAAY,IAAI,SAAU,MAAM,UAAU,KAAK;AAC9G;AAEA,SAASC,oBAAmB;AAC1B,SAAO,UAAU,kBAAmB,kBAAkB;AACxD;AAEA,SAAS,iBAAiBC,YAAWC,SAAQ,iBAAiB;AAC5D,MAAI,MAAMD,WAAU,QAAQC,QAAO,CAAC,EAAE,CAAC,CAAC,IAAI,gBAAgB,CAAC,EAAE,CAAC,GAC5D,MAAMD,WAAU,QAAQC,QAAO,CAAC,EAAE,CAAC,CAAC,IAAI,gBAAgB,CAAC,EAAE,CAAC,GAC5D,MAAMD,WAAU,QAAQC,QAAO,CAAC,EAAE,CAAC,CAAC,IAAI,gBAAgB,CAAC,EAAE,CAAC,GAC5D,MAAMD,WAAU,QAAQC,QAAO,CAAC,EAAE,CAAC,CAAC,IAAI,gBAAgB,CAAC,EAAE,CAAC;AAChE,SAAOD,WAAU;AAAA,IACf,MAAM,OAAO,MAAM,OAAO,IAAI,KAAK,IAAI,GAAG,GAAG,KAAK,KAAK,IAAI,GAAG,GAAG;AAAA,IACjE,MAAM,OAAO,MAAM,OAAO,IAAI,KAAK,IAAI,GAAG,GAAG,KAAK,KAAK,IAAI,GAAG,GAAG;AAAA,EACnE;AACF;AAEe,SAARE,gBAAmB;AACxB,MAAIC,UAASP,gBACTK,UAASJ,gBACT,YAAY,kBACZ,aAAa,mBACb,YAAYE,mBACZ,cAAc,CAAC,GAAG,QAAQ,GAC1B,kBAAkB,CAAC,CAAC,WAAW,SAAS,GAAG,CAAC,UAAU,QAAQ,CAAC,GAC/D,WAAW,KACX,cAAc,cACd,YAAY,iBAAS,SAAS,QAAQ,KAAK,GAC3C,eACA,YACA,aACA,aAAa,KACb,aAAa,KACb,iBAAiB,GACjB,cAAc;AAElB,WAAS,KAAK,WAAW;AACvB,cACK,SAAS,UAAU,gBAAgB,EACnC,GAAG,cAAc,SAAS,EAAC,SAAS,MAAK,CAAC,EAC1C,GAAG,kBAAkB,WAAW,EAChC,GAAG,iBAAiB,UAAU,EAChC,OAAO,SAAS,EACd,GAAG,mBAAmB,YAAY,EAClC,GAAG,kBAAkB,UAAU,EAC/B,GAAG,kCAAkC,UAAU,EAC/C,MAAM,+BAA+B,eAAe;AAAA,EAC3D;AAEA,OAAK,YAAY,SAAS,YAAYC,YAAWI,QAAO,OAAO;AAC7D,QAAI,YAAY,WAAW,YAAY,WAAW,UAAU,IAAI;AAChE,cAAU,SAAS,UAAU,gBAAgB;AAC7C,QAAI,eAAe,WAAW;AAC5B,eAAS,YAAYJ,YAAWI,QAAO,KAAK;AAAA,IAC9C,OAAO;AACL,gBAAU,UAAU,EAAE,KAAK,WAAW;AACpC,gBAAQ,MAAM,SAAS,EACpB,MAAM,KAAK,EACX,MAAM,EACN,KAAK,MAAM,OAAOJ,eAAc,aAAaA,WAAU,MAAM,MAAM,SAAS,IAAIA,UAAS,EACzF,IAAI;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF;AAEA,OAAK,UAAU,SAAS,WAAW,GAAG,GAAG,OAAO;AAC9C,SAAK,QAAQ,WAAW,WAAW;AACjC,UAAI,KAAK,KAAK,OAAO,GACjB,KAAK,OAAO,MAAM,aAAa,EAAE,MAAM,MAAM,SAAS,IAAI;AAC9D,aAAO,KAAK;AAAA,IACd,GAAG,GAAG,KAAK;AAAA,EACb;AAEA,OAAK,UAAU,SAAS,WAAW,GAAG,GAAG,OAAO;AAC9C,SAAK,UAAU,WAAW,WAAW;AACnC,UAAI,IAAIC,QAAO,MAAM,MAAM,SAAS,GAChC,KAAK,KAAK,QACV,KAAK,KAAK,OAAO,SAAS,CAAC,IAAI,OAAO,MAAM,aAAa,EAAE,MAAM,MAAM,SAAS,IAAI,GACpF,KAAK,GAAG,OAAO,EAAE,GACjB,KAAK,OAAO,MAAM,aAAa,EAAE,MAAM,MAAM,SAAS,IAAI;AAC9D,aAAO,UAAU,UAAU,MAAM,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,GAAG,eAAe;AAAA,IACvE,GAAG,GAAG,KAAK;AAAA,EACb;AAEA,OAAK,cAAc,SAAS,WAAWI,IAAGC,IAAG,OAAO;AAClD,SAAK,UAAU,WAAW,WAAW;AACnC,aAAO,UAAU,KAAK,OAAO;AAAA,QAC3B,OAAOD,OAAM,aAAaA,GAAE,MAAM,MAAM,SAAS,IAAIA;AAAA,QACrD,OAAOC,OAAM,aAAaA,GAAE,MAAM,MAAM,SAAS,IAAIA;AAAA,MACvD,GAAGL,QAAO,MAAM,MAAM,SAAS,GAAG,eAAe;AAAA,IACnD,GAAG,MAAM,KAAK;AAAA,EAChB;AAEA,OAAK,cAAc,SAAS,WAAWI,IAAGC,IAAG,GAAG,OAAO;AACrD,SAAK,UAAU,WAAW,WAAW;AACnC,UAAI,IAAIL,QAAO,MAAM,MAAM,SAAS,GAChC,IAAI,KAAK,QACT,KAAK,KAAK,OAAO,SAAS,CAAC,IAAI,OAAO,MAAM,aAAa,EAAE,MAAM,MAAM,SAAS,IAAI;AACxF,aAAO,UAAUH,UAAS,UAAU,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;AAAA,QAC3D,OAAOO,OAAM,aAAa,CAACA,GAAE,MAAM,MAAM,SAAS,IAAI,CAACA;AAAA,QACvD,OAAOC,OAAM,aAAa,CAACA,GAAE,MAAM,MAAM,SAAS,IAAI,CAACA;AAAA,MACzD,GAAG,GAAG,eAAe;AAAA,IACvB,GAAG,GAAG,KAAK;AAAA,EACb;AAEA,WAAS,MAAMN,YAAW,GAAG;AAC3B,QAAI,KAAK,IAAI,YAAY,CAAC,GAAG,KAAK,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC;AACxD,WAAO,MAAMA,WAAU,IAAIA,aAAY,IAAI,UAAU,GAAGA,WAAU,GAAGA,WAAU,CAAC;AAAA,EAClF;AAEA,WAAS,UAAUA,YAAW,IAAI,IAAI;AACpC,QAAIK,KAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAIL,WAAU,GAAGM,KAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAIN,WAAU;AACnE,WAAOK,OAAML,WAAU,KAAKM,OAAMN,WAAU,IAAIA,aAAY,IAAI,UAAUA,WAAU,GAAGK,IAAGC,EAAC;AAAA,EAC7F;AAEA,WAAS,SAASL,SAAQ;AACxB,WAAO,EAAE,CAACA,QAAO,CAAC,EAAE,CAAC,IAAI,CAACA,QAAO,CAAC,EAAE,CAAC,KAAK,IAAI,CAACA,QAAO,CAAC,EAAE,CAAC,IAAI,CAACA,QAAO,CAAC,EAAE,CAAC,KAAK,CAAC;AAAA,EAClF;AAEA,WAAS,SAASM,aAAYP,YAAWI,QAAO,OAAO;AACrD,IAAAG,YACK,GAAG,cAAc,WAAW;AAAE,cAAQ,MAAM,SAAS,EAAE,MAAM,KAAK,EAAE,MAAM;AAAA,IAAG,CAAC,EAC9E,GAAG,2BAA2B,WAAW;AAAE,cAAQ,MAAM,SAAS,EAAE,MAAM,KAAK,EAAE,IAAI;AAAA,IAAG,CAAC,EACzF,MAAM,QAAQ,WAAW;AACxB,UAAI,OAAO,MACP,OAAO,WACP,IAAI,QAAQ,MAAM,IAAI,EAAE,MAAM,KAAK,GACnC,IAAIN,QAAO,MAAM,MAAM,IAAI,GAC3B,IAAIG,UAAS,OAAO,SAAS,CAAC,IAAI,OAAOA,WAAU,aAAaA,OAAM,MAAM,MAAM,IAAI,IAAIA,QAC1F,IAAI,KAAK,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,GACjDI,KAAI,KAAK,QACT,IAAI,OAAOR,eAAc,aAAaA,WAAU,MAAM,MAAM,IAAI,IAAIA,YACpE,IAAI,YAAYQ,GAAE,OAAO,CAAC,EAAE,OAAO,IAAIA,GAAE,CAAC,GAAG,EAAE,OAAO,CAAC,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC;AAC5E,aAAO,SAAS,GAAG;AACjB,YAAI,MAAM,EAAG,KAAI;AAAA,aACZ;AAAE,cAAI,IAAI,EAAE,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;AAAG,cAAI,IAAI,UAAU,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC;AAAA,QAAG;AAC3F,UAAE,KAAK,MAAM,CAAC;AAAA,MAChB;AAAA,IACF,CAAC;AAAA,EACP;AAEA,WAAS,QAAQ,MAAM,MAAM,OAAO;AAClC,WAAQ,CAAC,SAAS,KAAK,aAAc,IAAI,QAAQ,MAAM,IAAI;AAAA,EAC7D;AAEA,WAAS,QAAQ,MAAM,MAAM;AAC3B,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,SAAK,SAASP,QAAO,MAAM,MAAM,IAAI;AACrC,SAAK,OAAO;AAAA,EACd;AAEA,UAAQ,YAAY;AAAA,IAClB,OAAO,SAAS,OAAO;AACrB,UAAI,MAAO,MAAK,cAAc;AAC9B,aAAO;AAAA,IACT;AAAA,IACA,OAAO,WAAW;AAChB,UAAI,EAAE,KAAK,WAAW,GAAG;AACvB,aAAK,KAAK,YAAY;AACtB,aAAK,KAAK,OAAO;AAAA,MACnB;AACA,aAAO;AAAA,IACT;AAAA,IACA,MAAM,SAAS,KAAKD,YAAW;AAC7B,UAAI,KAAK,SAAS,QAAQ,QAAS,MAAK,MAAM,CAAC,IAAIA,WAAU,OAAO,KAAK,MAAM,CAAC,CAAC;AACjF,UAAI,KAAK,UAAU,QAAQ,QAAS,MAAK,OAAO,CAAC,IAAIA,WAAU,OAAO,KAAK,OAAO,CAAC,CAAC;AACpF,UAAI,KAAK,UAAU,QAAQ,QAAS,MAAK,OAAO,CAAC,IAAIA,WAAU,OAAO,KAAK,OAAO,CAAC,CAAC;AACpF,WAAK,KAAK,SAASA;AACnB,WAAK,KAAK,MAAM;AAChB,aAAO;AAAA,IACT;AAAA,IACA,KAAK,WAAW;AACd,UAAI,EAAE,KAAK,WAAW,GAAG;AACvB,eAAO,KAAK,KAAK;AACjB,aAAK,KAAK,KAAK;AAAA,MACjB;AACA,aAAO;AAAA,IACT;AAAA,IACA,MAAM,SAASS,OAAM;AACnB,UAAI,IAAI,eAAO,KAAK,IAAI,EAAE,MAAM;AAChC,gBAAU;AAAA,QACRA;AAAA,QACA,KAAK;AAAA,QACL,IAAI,UAAUA,OAAM;AAAA,UAClB,aAAa,KAAK;AAAA,UAClB,QAAQ;AAAA,UACR,MAAAA;AAAA,UACA,WAAW,KAAK,KAAK;AAAA,UACrB,UAAU;AAAA,QACZ,CAAC;AAAA,QACD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,WAAS,QAAQ,UAAU,MAAM;AAC/B,QAAI,CAACN,QAAO,MAAM,MAAM,SAAS,EAAG;AACpC,QAAI,IAAI,QAAQ,MAAM,IAAI,EAAE,MAAM,KAAK,GACnC,IAAI,KAAK,QACT,IAAI,KAAK,IAAI,YAAY,CAAC,GAAG,KAAK,IAAI,YAAY,CAAC,GAAG,EAAE,IAAI,KAAK,IAAI,GAAG,WAAW,MAAM,MAAM,SAAS,CAAC,CAAC,CAAC,GAC3G,IAAI,gBAAQ,KAAK;AAIrB,QAAI,EAAE,OAAO;AACX,UAAI,EAAE,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG;AACpD,UAAE,MAAM,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC;AAAA,MACtC;AACA,mBAAa,EAAE,KAAK;AAAA,IACtB,WAGS,EAAE,MAAM,EAAG;AAAA,SAGf;AACH,QAAE,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AACzB,wBAAU,IAAI;AACd,QAAE,MAAM;AAAA,IACV;AAEA,IAAAO,iBAAQ,KAAK;AACb,MAAE,QAAQ,WAAW,YAAY,UAAU;AAC3C,MAAE,KAAK,SAAS,UAAU,UAAU,MAAM,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,EAAE,QAAQ,eAAe,CAAC;AAEpG,aAAS,aAAa;AACpB,QAAE,QAAQ;AACV,QAAE,IAAI;AAAA,IACR;AAAA,EACF;AAEA,WAAS,YAAY,UAAU,MAAM;AACnC,QAAI,eAAe,CAACP,QAAO,MAAM,MAAM,SAAS,EAAG;AACnD,QAAI,gBAAgB,MAAM,eACtB,IAAI,QAAQ,MAAM,MAAM,IAAI,EAAE,MAAM,KAAK,GACzC,IAAI,eAAO,MAAM,IAAI,EAAE,GAAG,kBAAkB,YAAY,IAAI,EAAE,GAAG,gBAAgB,YAAY,IAAI,GACjG,IAAI,gBAAQ,OAAO,aAAa,GAChC,KAAK,MAAM,SACX,KAAK,MAAM;AAEf,mBAAY,MAAM,IAAI;AACtB,IAAAQ,eAAc,KAAK;AACnB,MAAE,QAAQ,CAAC,GAAG,KAAK,OAAO,OAAO,CAAC,CAAC;AACnC,sBAAU,IAAI;AACd,MAAE,MAAM;AAER,aAAS,WAAWC,QAAO;AACzB,MAAAF,iBAAQE,MAAK;AACb,UAAI,CAAC,EAAE,OAAO;AACZ,YAAI,KAAKA,OAAM,UAAU,IAAI,KAAKA,OAAM,UAAU;AAClD,UAAE,QAAQ,KAAK,KAAK,KAAK,KAAK;AAAA,MAChC;AACA,QAAE,MAAMA,MAAK,EACX,KAAK,SAAS,UAAU,UAAU,EAAE,KAAK,QAAQ,EAAE,MAAM,CAAC,IAAI,gBAAQA,QAAO,aAAa,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,EAAE,QAAQ,eAAe,CAAC;AAAA,IACxI;AAEA,aAAS,WAAWA,QAAO;AACzB,QAAE,GAAG,+BAA+B,IAAI;AACxC,cAAWA,OAAM,MAAM,EAAE,KAAK;AAC9B,MAAAF,iBAAQE,MAAK;AACb,QAAE,MAAMA,MAAK,EAAE,IAAI;AAAA,IACrB;AAAA,EACF;AAEA,WAAS,WAAW,UAAU,MAAM;AAClC,QAAI,CAACT,QAAO,MAAM,MAAM,SAAS,EAAG;AACpC,QAAI,KAAK,KAAK,QACV,KAAK,gBAAQ,MAAM,iBAAiB,MAAM,eAAe,CAAC,IAAI,OAAO,IAAI,GACzE,KAAK,GAAG,OAAO,EAAE,GACjB,KAAK,GAAG,KAAK,MAAM,WAAW,MAAM,IACpC,KAAK,UAAU,UAAU,MAAM,IAAI,EAAE,GAAG,IAAI,EAAE,GAAGF,QAAO,MAAM,MAAM,IAAI,GAAG,eAAe;AAE9F,IAAAS,iBAAQ,KAAK;AACb,QAAI,WAAW,EAAG,gBAAO,IAAI,EAAE,WAAW,EAAE,SAAS,QAAQ,EAAE,KAAK,UAAU,IAAI,IAAI,KAAK;AAAA,QACtF,gBAAO,IAAI,EAAE,KAAK,KAAK,WAAW,IAAI,IAAI,KAAK;AAAA,EACtD;AAEA,WAAS,aAAa,UAAU,MAAM;AACpC,QAAI,CAACP,QAAO,MAAM,MAAM,SAAS,EAAG;AACpC,QAAI,UAAU,MAAM,SAChB,IAAI,QAAQ,QACZ,IAAI,QAAQ,MAAM,MAAM,MAAM,eAAe,WAAW,CAAC,EAAE,MAAM,KAAK,GACtE,SAAS,GAAG,GAAG;AAEnB,IAAAQ,eAAc,KAAK;AACnB,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,UAAI,QAAQ,CAAC,GAAG,IAAI,gBAAQ,GAAG,IAAI;AACnC,UAAI,CAAC,GAAG,KAAK,OAAO,OAAO,CAAC,GAAG,EAAE,UAAU;AAC3C,UAAI,CAAC,EAAE,OAAQ,GAAE,SAAS,GAAG,UAAU,MAAM,EAAE,OAAO,IAAI,CAAC,CAAC;AAAA,eACnD,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,EAAG,GAAE,SAAS,GAAG,EAAE,OAAO;AAAA,IACrE;AAEA,QAAI,cAAe,iBAAgB,aAAa,aAAa;AAE7D,QAAI,SAAS;AACX,UAAI,EAAE,OAAO,EAAG,cAAa,EAAE,CAAC,GAAG,gBAAgB,WAAW,WAAW;AAAE,wBAAgB;AAAA,MAAM,GAAG,UAAU;AAC9G,wBAAU,IAAI;AACd,QAAE,MAAM;AAAA,IACV;AAAA,EACF;AAEA,WAAS,WAAW,UAAU,MAAM;AAClC,QAAI,CAAC,KAAK,UAAW;AACrB,QAAI,IAAI,QAAQ,MAAM,IAAI,EAAE,MAAM,KAAK,GACnC,UAAU,MAAM,gBAChB,IAAI,QAAQ,QAAQ,GAAG,GAAG,GAAG;AAEjC,IAAAD,iBAAQ,KAAK;AACb,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,UAAI,QAAQ,CAAC,GAAG,IAAI,gBAAQ,GAAG,IAAI;AACnC,UAAI,EAAE,UAAU,EAAE,OAAO,CAAC,MAAM,EAAE,WAAY,GAAE,OAAO,CAAC,IAAI;AAAA,eACnD,EAAE,UAAU,EAAE,OAAO,CAAC,MAAM,EAAE,WAAY,GAAE,OAAO,CAAC,IAAI;AAAA,IACnE;AACA,QAAI,EAAE,KAAK;AACX,QAAI,EAAE,QAAQ;AACZ,UAAI,KAAK,EAAE,OAAO,CAAC,GAAG,KAAK,EAAE,OAAO,CAAC,GACjC,KAAK,EAAE,OAAO,CAAC,GAAG,KAAK,EAAE,OAAO,CAAC,GACjC,MAAM,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,IACxD,MAAM,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK;AAC5D,UAAI,MAAM,GAAG,KAAK,KAAK,KAAK,EAAE,CAAC;AAC/B,UAAI,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC;AAC7C,UAAI,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC;AAAA,IAC/C,WACS,EAAE,OAAQ,KAAI,EAAE,OAAO,CAAC,GAAG,IAAI,EAAE,OAAO,CAAC;AAAA,QAC7C;AAEL,MAAE,KAAK,SAAS,UAAU,UAAU,GAAG,GAAG,CAAC,GAAG,EAAE,QAAQ,eAAe,CAAC;AAAA,EAC1E;AAEA,WAAS,WAAW,UAAU,MAAM;AAClC,QAAI,CAAC,KAAK,UAAW;AACrB,QAAI,IAAI,QAAQ,MAAM,IAAI,EAAE,MAAM,KAAK,GACnC,UAAU,MAAM,gBAChB,IAAI,QAAQ,QAAQ,GAAG;AAE3B,IAAAC,eAAc,KAAK;AACnB,QAAI,YAAa,cAAa,WAAW;AACzC,kBAAc,WAAW,WAAW;AAAE,oBAAc;AAAA,IAAM,GAAG,UAAU;AACvE,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,UAAI,QAAQ,CAAC;AACb,UAAI,EAAE,UAAU,EAAE,OAAO,CAAC,MAAM,EAAE,WAAY,QAAO,EAAE;AAAA,eAC9C,EAAE,UAAU,EAAE,OAAO,CAAC,MAAM,EAAE,WAAY,QAAO,EAAE;AAAA,IAC9D;AACA,QAAI,EAAE,UAAU,CAAC,EAAE,OAAQ,GAAE,SAAS,EAAE,QAAQ,OAAO,EAAE;AACzD,QAAI,EAAE,OAAQ,GAAE,OAAO,CAAC,IAAI,KAAK,OAAO,OAAO,EAAE,OAAO,CAAC,CAAC;AAAA,SACrD;AACH,QAAE,IAAI;AAEN,UAAI,EAAE,SAAS,GAAG;AAChB,YAAI,gBAAQ,GAAG,IAAI;AACnB,YAAI,KAAK,MAAM,WAAW,CAAC,IAAI,EAAE,CAAC,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,aAAa;AACxE,cAAI,IAAI,eAAO,IAAI,EAAE,GAAG,eAAe;AACvC,cAAI,EAAG,GAAE,MAAM,MAAM,SAAS;AAAA,QAChC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,OAAK,aAAa,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAU,aAAa,OAAO,MAAM,aAAa,IAAIE,kBAAS,CAAC,CAAC,GAAG,QAAQ;AAAA,EAC9F;AAEA,OAAK,SAAS,SAAS,GAAG;AACxB,WAAO,UAAU,UAAUV,UAAS,OAAO,MAAM,aAAa,IAAIU,kBAAS,CAAC,CAAC,CAAC,GAAG,QAAQV;AAAA,EAC3F;AAEA,OAAK,YAAY,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,YAAY,OAAO,MAAM,aAAa,IAAIU,kBAAS,CAAC,CAAC,CAAC,GAAG,QAAQ;AAAA,EAC9F;AAEA,OAAK,SAAS,SAAS,GAAG;AACxB,WAAO,UAAU,UAAUZ,UAAS,OAAO,MAAM,aAAa,IAAIY,kBAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQZ;AAAA,EACpI;AAEA,OAAK,cAAc,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AAAA,EACpH;AAEA,OAAK,kBAAkB,SAAS,GAAG;AACjC,WAAO,UAAU,UAAU,gBAAgB,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA,EAC5Q;AAEA,OAAK,YAAY,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,YAAY,GAAG,QAAQ;AAAA,EACpD;AAEA,OAAK,WAAW,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,WAAW,CAAC,GAAG,QAAQ;AAAA,EACpD;AAEA,OAAK,cAAc,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,cAAc,GAAG,QAAQ;AAAA,EACtD;AAEA,OAAK,KAAK,WAAW;AACnB,QAAI,QAAQ,UAAU,GAAG,MAAM,WAAW,SAAS;AACnD,WAAO,UAAU,YAAY,OAAO;AAAA,EACtC;AAEA,OAAK,gBAAgB,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,kBAAkB,IAAI,CAAC,KAAK,GAAG,QAAQ,KAAK,KAAK,cAAc;AAAA,EAC5F;AAEA,OAAK,cAAc,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,cAAc,CAAC,GAAG,QAAQ;AAAA,EACvD;AAEA,SAAO;AACT;", "names": ["identity_default", "x", "x", "y", "tickFormat", "transform", "axis", "format", "identity_default", "range", "path", "type", "c", "root", "x", "type", "x", "y", "dispatch", "filter", "c", "container", "dispatch", "type", "event", "touch", "time", "now", "time", "time", "callback", "delay", "id", "index", "group", "get", "set", "start", "empty", "interrupt_default", "id", "set", "get", "transition", "interpolate_default", "a", "c", "interpolate_default", "id", "get", "id", "set", "get", "id", "set", "get", "id", "set", "groups", "m", "group", "transition", "m", "merge", "id", "set", "get", "id", "select_default", "id", "groups", "m", "group", "get", "selectAll_default", "id", "groups", "m", "group", "inherit", "get", "selection_default", "id", "set", "interpolate_default", "groups", "m", "group", "inherit", "get", "id", "set", "groups", "id", "select_default", "selectAll_default", "selection_default", "linear", "polyIn", "custom", "polyOut", "polyInOut", "x", "custom", "backIn", "backOut", "backInOut", "custom", "a", "elasticIn", "p", "elasticOut", "elasticInOut", "id", "transition_default", "groups", "m", "group", "interrupt_default", "transition_default", "constant_default", "x", "type", "mode", "dispatch", "nopropagation", "noevent_default", "max", "min", "x", "y", "defaultFilter", "svg", "defaultTouchable", "local", "extent", "filter", "brush", "group", "event", "mode", "type", "point", "noevent_default", "nopropagation", "constant_default", "abs", "pi", "halfPi", "tau", "max", "epsilon", "range", "a", "transpose", "chord", "groups", "x", "max", "tau", "constant_default", "x", "ribbon", "halfPi", "epsilon", "abs", "constant_default", "slice", "ascending_default", "a", "area_default", "constant_default", "x", "contains_default", "c", "point", "x", "y", "pi", "a", "threshold", "ascending_default", "area_default", "contains_default", "x", "y", "start", "index", "point", "constant_default", "slice", "a", "x", "y", "m", "threshold", "constant_default", "c", "transform", "slice", "seconds", "milliseconds", "c", "format", "number", "m", "init", "init", "text_default", "init", "init", "text_default", "format", "csv", "tsv", "init", "init", "type", "init", "text_default", "x", "y", "x", "y", "right", "bottom", "x", "y", "extent_default", "x", "y", "x2", "y2", "x3", "y3", "remove_default", "x", "y", "right", "bottom", "defaultX", "defaultY", "x", "y", "defaultX", "defaultY", "extent_default", "remove_default", "constant_default", "x", "constant_default", "x", "y", "index", "id", "constant_default", "count", "link", "x", "y", "m", "i", "x", "y", "constant_default", "x", "y", "node", "strength", "c", "x2", "x", "y", "constant_default", "x_default", "x", "constant_default", "y_default", "y", "constant_default", "a", "x", "c", "y", "root", "left", "right", "count", "sum", "index", "index", "index", "find_default", "index", "sum", "path_default", "start", "a", "c", "root", "root", "find_default", "path_default", "constant_default", "x", "a", "c", "m", "lcg_default", "array_default", "x", "array", "m", "lcg_default", "a", "x2", "y2", "c", "a2", "a3", "b2", "b3", "c2", "c3", "a", "c", "x", "a2", "y", "b2", "Node", "array_default", "lcg_default", "defaultRadius", "root", "lcg_default", "x", "constant_default", "round_default", "root", "round_default", "dy", "x", "id", "path", "root", "d", "i", "node", "x", "defaultSeparation", "a", "root", "left", "right", "bottom", "x", "custom", "x", "root", "round_default", "x", "constant_default", "sum", "i", "x0", "y0", "x1", "y1", "custom", "m", "x", "area_default", "a", "centroid_default", "x", "y", "a", "c", "cross_default", "a", "c", "a", "indexes", "cross_default", "contains_default", "point", "x", "y", "length_default", "min", "max", "min", "max", "x", "y", "sum", "c", "x", "X", "Y", "x", "y", "a", "x", "a", "a", "constant_default", "x", "type", "transform", "dispatch", "x", "y", "point", "identity", "nopropagation", "noevent_default", "defaultFilter", "defaultExtent", "identity", "defaultTouchable", "transform", "extent", "zoom_default", "filter", "point", "x", "y", "transition", "a", "type", "noevent_default", "nopropagation", "event", "constant_default"]}