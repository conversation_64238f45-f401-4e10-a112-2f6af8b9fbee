import {
  pointer_default,
  select_default
} from "./chunk-6YBWNIRW.js";
import {
  _assertThisInitialized,
  _extends,
  _inheritsLoose,
  _setPrototypeOf,
  require_prop_types
} from "./chunk-UEE2ADB3.js";
import {
  OrbitControls
} from "./chunk-KL67KYPB.js";
import {
  BufferGeometryUtils_exports,
  CSS2DObject,
  CSS2DRenderer,
  ConicPolygonGeometry,
  DataBindMapper,
  Easing,
  Font,
  GeoJsonGeometry,
  Group as Group2,
  Line2,
  LineGeometry,
  LineMaterial,
  TextGeometry,
  ThreeSlippyMapGlobe,
  Tween,
  cellToBoundary,
  cellToLatLng,
  index,
  index2,
  index3,
  latLngToCell,
  polygonToCells,
  require_FrameTicker,
  tinycolor
} from "./chunk-CVHY6WEB.js";
import {
  distance_default,
  graticule10,
  interpolate_default,
  turbo_default
} from "./chunk-2YBOPMHX.js";
import {
  array_default,
  color,
  linear,
  max,
  quantize,
  sum
} from "./chunk-CKIB5I5P.js";
import {
  AmbientLight,
  BackSide,
  Box3,
  BoxGeometry,
  BufferAttribute,
  BufferGeometry,
  Camera,
  CircleGeometry,
  Clock,
  Color,
  CubicBezierCurve3,
  Curve,
  CylinderGeometry,
  DirectionalLight,
  DoubleSide,
  Euler,
  EventDispatcher,
  Float32BufferAttribute,
  Group,
  Line,
  LineBasicMaterial,
  LineSegments,
  MOUSE,
  MathUtils,
  Matrix4,
  Mesh,
  MeshBasicMaterial,
  MeshLambertMaterial,
  MeshPhongMaterial,
  NormalBlending,
  Object3D,
  OrthographicCamera,
  PerspectiveCamera,
  Points,
  PointsMaterial,
  Quaternion,
  REVISION,
  Raycaster,
  SRGBColorSpace,
  Scene,
  ShaderChunk,
  ShaderMaterial,
  SphereGeometry,
  TextureLoader,
  TubeGeometry,
  UniformsUtils,
  Vector2,
  Vector3,
  WebGLRenderTarget,
  WebGLRenderer
} from "./chunk-ARWCJGNC.js";
import {
  require_react
} from "./chunk-DRWLMN53.js";
import {
  __toESM
} from "./chunk-G3PMV62Z.js";

// node_modules/react-kapsule/dist/react-kapsule.mjs
var import_react = __toESM(require_react(), 1);

// node_modules/jerrypick/dist/jerrypick.mjs
function _iterableToArrayLimit(arr, i2) {
  var _i = null == arr ? null : "undefined" != typeof Symbol && arr[Symbol.iterator] || arr["@@iterator"];
  if (null != _i) {
    var _s, _e, _x, _r, _arr = [], _n = true, _d = false;
    try {
      if (_x = (_i = _i.call(arr)).next, 0 === i2) {
        if (Object(_i) !== _i) return;
        _n = false;
      } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i2); _n = true) ;
    } catch (err) {
      _d = true, _e = err;
    } finally {
      try {
        if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return;
      } finally {
        if (_d) throw _e;
      }
    }
    return _arr;
  }
}
function _defineProperty(obj, key, value) {
  key = _toPropertyKey(key);
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}
function _slicedToArray(arr, i2) {
  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i2) || _unsupportedIterableToArray(arr, i2) || _nonIterableRest();
}
function _toConsumableArray(arr) {
  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();
}
function _arrayWithoutHoles(arr) {
  if (Array.isArray(arr)) return _arrayLikeToArray(arr);
}
function _arrayWithHoles(arr) {
  if (Array.isArray(arr)) return arr;
}
function _iterableToArray(iter) {
  if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter);
}
function _unsupportedIterableToArray(o2, minLen) {
  if (!o2) return;
  if (typeof o2 === "string") return _arrayLikeToArray(o2, minLen);
  var n2 = Object.prototype.toString.call(o2).slice(8, -1);
  if (n2 === "Object" && o2.constructor) n2 = o2.constructor.name;
  if (n2 === "Map" || n2 === "Set") return Array.from(o2);
  if (n2 === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n2)) return _arrayLikeToArray(o2, minLen);
}
function _arrayLikeToArray(arr, len) {
  if (len == null || len > arr.length) len = arr.length;
  for (var i2 = 0, arr2 = new Array(len); i2 < len; i2++) arr2[i2] = arr[i2];
  return arr2;
}
function _nonIterableSpread() {
  throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _nonIterableRest() {
  throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _toPrimitive(input, hint) {
  if (typeof input !== "object" || input === null) return input;
  var prim = input[Symbol.toPrimitive];
  if (prim !== void 0) {
    var res = prim.call(input, hint || "default");
    if (typeof res !== "object") return res;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return (hint === "string" ? String : Number)(input);
}
function _toPropertyKey(arg) {
  var key = _toPrimitive(arg, "string");
  return typeof key === "symbol" ? key : String(key);
}
var omit = function omit2(obj, keys) {
  var keySet = new Set(keys);
  return Object.assign.apply(Object, [{}].concat(_toConsumableArray(Object.entries(obj).filter(function(_ref2) {
    var _ref3 = _slicedToArray(_ref2, 1), key = _ref3[0];
    return !keySet.has(key);
  }).map(function(_ref4) {
    var _ref5 = _slicedToArray(_ref4, 2), key = _ref5[0], val = _ref5[1];
    return _defineProperty({}, key, val);
  }))));
};

// node_modules/react-kapsule/dist/react-kapsule.mjs
function _arrayLikeToArray2(r2, a2) {
  (null == a2 || a2 > r2.length) && (a2 = r2.length);
  for (var e2 = 0, n2 = Array(a2); e2 < a2; e2++) n2[e2] = r2[e2];
  return n2;
}
function _arrayWithHoles2(r2) {
  if (Array.isArray(r2)) return r2;
}
function _arrayWithoutHoles2(r2) {
  if (Array.isArray(r2)) return _arrayLikeToArray2(r2);
}
function _iterableToArray2(r2) {
  if ("undefined" != typeof Symbol && null != r2[Symbol.iterator] || null != r2["@@iterator"]) return Array.from(r2);
}
function _iterableToArrayLimit2(r2, l2) {
  var t2 = null == r2 ? null : "undefined" != typeof Symbol && r2[Symbol.iterator] || r2["@@iterator"];
  if (null != t2) {
    var e2, n2, i2, u2, a2 = [], f2 = true, o2 = false;
    try {
      if (i2 = (t2 = t2.call(r2)).next, 0 === l2) ;
      else for (; !(f2 = (e2 = i2.call(t2)).done) && (a2.push(e2.value), a2.length !== l2); f2 = true) ;
    } catch (r3) {
      o2 = true, n2 = r3;
    } finally {
      try {
        if (!f2 && null != t2.return && (u2 = t2.return(), Object(u2) !== u2)) return;
      } finally {
        if (o2) throw n2;
      }
    }
    return a2;
  }
}
function _nonIterableRest2() {
  throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _nonIterableSpread2() {
  throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _slicedToArray2(r2, e2) {
  return _arrayWithHoles2(r2) || _iterableToArrayLimit2(r2, e2) || _unsupportedIterableToArray2(r2, e2) || _nonIterableRest2();
}
function _toConsumableArray2(r2) {
  return _arrayWithoutHoles2(r2) || _iterableToArray2(r2) || _unsupportedIterableToArray2(r2) || _nonIterableSpread2();
}
function _unsupportedIterableToArray2(r2, a2) {
  if (r2) {
    if ("string" == typeof r2) return _arrayLikeToArray2(r2, a2);
    var t2 = {}.toString.call(r2).slice(8, -1);
    return "Object" === t2 && r2.constructor && (t2 = r2.constructor.name), "Map" === t2 || "Set" === t2 ? Array.from(r2) : "Arguments" === t2 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t2) ? _arrayLikeToArray2(r2, a2) : void 0;
  }
}
function index4(kapsuleComponent) {
  var _ref = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, _ref$wrapperElementTy = _ref.wrapperElementType, wrapperElementType = _ref$wrapperElementTy === void 0 ? "div" : _ref$wrapperElementTy, _ref$nodeMapper = _ref.nodeMapper, nodeMapper = _ref$nodeMapper === void 0 ? function(node) {
    return node;
  } : _ref$nodeMapper, _ref$methodNames = _ref.methodNames, methodNames = _ref$methodNames === void 0 ? [] : _ref$methodNames, _ref$initPropNames = _ref.initPropNames, initPropNames = _ref$initPropNames === void 0 ? [] : _ref$initPropNames;
  return (0, import_react.forwardRef)(function(props, ref) {
    var domEl = (0, import_react.useRef)();
    var comp = (0, import_react.useMemo)(function() {
      var configOptions = Object.fromEntries(initPropNames.filter(function(p2) {
        return props.hasOwnProperty(p2);
      }).map(function(prop) {
        return [prop, props[prop]];
      }));
      return kapsuleComponent(configOptions);
    }, []);
    useEffectOnce(function() {
      comp(nodeMapper(domEl.current));
    }, import_react.useLayoutEffect);
    useEffectOnce(function() {
      return comp._destructor instanceof Function ? comp._destructor : void 0;
    });
    var _call = (0, import_react.useCallback)(
      function(method) {
        for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
          args[_key - 1] = arguments[_key];
        }
        return comp[method] instanceof Function ? comp[method].apply(comp, args) : void 0;
      },
      [comp]
    );
    var prevPropsRef = (0, import_react.useRef)({});
    Object.keys(omit(props, [].concat(_toConsumableArray2(methodNames), _toConsumableArray2(initPropNames)))).filter(function(p2) {
      return prevPropsRef.current[p2] !== props[p2];
    }).forEach(function(p2) {
      return _call(p2, props[p2]);
    });
    prevPropsRef.current = props;
    (0, import_react.useImperativeHandle)(ref, function() {
      return Object.fromEntries(methodNames.map(function(method) {
        return [method, function() {
          for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
            args[_key2] = arguments[_key2];
          }
          return _call.apply(void 0, [method].concat(args));
        }];
      }));
    }, [_call]);
    return import_react.default.createElement(wrapperElementType, {
      ref: domEl
    });
  });
}
function useEffectOnce(effect) {
  var useEffectFn = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : import_react.useEffect;
  var destroyFunc = (0, import_react.useRef)();
  var effectCalled = (0, import_react.useRef)(false);
  var renderAfterCalled = (0, import_react.useRef)(false);
  var _useState = (0, import_react.useState)(0), _useState2 = _slicedToArray2(_useState, 2);
  _useState2[0];
  var setVal = _useState2[1];
  if (effectCalled.current) {
    renderAfterCalled.current = true;
  }
  useEffectFn(function() {
    if (!effectCalled.current) {
      destroyFunc.current = effect();
      effectCalled.current = true;
    }
    setVal(function(val) {
      return val + 1;
    });
    return function() {
      if (!renderAfterCalled.current) return;
      if (destroyFunc.current) destroyFunc.current();
    };
  }, []);
}

// node_modules/globe.gl/node_modules/three-globe/dist/three-globe.mjs
var import_frame_ticker = __toESM(require_FrameTicker(), 1);

// src/utils/three-webgpu-stub.js
var WebGPURenderer = class WebGPURenderer2 {
  constructor() {
    console.warn("WebGPURenderer is not available in Three.js 0.152.0");
  }
};
var StorageInstancedBufferAttribute = class StorageInstancedBufferAttribute2 {
  constructor() {
    console.warn("StorageInstancedBufferAttribute is not available in Three.js 0.152.0");
  }
};

// node_modules/globe.gl/node_modules/three-globe/dist/three-globe.mjs
function _arrayLikeToArray3(r2, a2) {
  (null == a2 || a2 > r2.length) && (a2 = r2.length);
  for (var e2 = 0, n2 = Array(a2); e2 < a2; e2++) n2[e2] = r2[e2];
  return n2;
}
function _arrayWithHoles3(r2) {
  if (Array.isArray(r2)) return r2;
}
function _arrayWithoutHoles3(r2) {
  if (Array.isArray(r2)) return _arrayLikeToArray3(r2);
}
function _assertClassBrand(e2, t2, n2) {
  if ("function" == typeof e2 ? e2 === t2 : e2.has(t2)) return arguments.length < 3 ? t2 : n2;
  throw new TypeError("Private element is not present on this object");
}
function _assertThisInitialized2(e2) {
  if (void 0 === e2) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
  return e2;
}
function asyncGeneratorStep(n2, t2, e2, r2, o2, a2, c2) {
  try {
    var i2 = n2[a2](c2), u2 = i2.value;
  } catch (n3) {
    return void e2(n3);
  }
  i2.done ? t2(u2) : Promise.resolve(u2).then(r2, o2);
}
function _asyncToGenerator(n2) {
  return function() {
    var t2 = this, e2 = arguments;
    return new Promise(function(r2, o2) {
      var a2 = n2.apply(t2, e2);
      function _next(n3) {
        asyncGeneratorStep(a2, r2, o2, _next, _throw, "next", n3);
      }
      function _throw(n3) {
        asyncGeneratorStep(a2, r2, o2, _next, _throw, "throw", n3);
      }
      _next(void 0);
    });
  };
}
function _callSuper(t2, o2, e2) {
  return o2 = _getPrototypeOf(o2), _possibleConstructorReturn(t2, _isNativeReflectConstruct() ? Reflect.construct(o2, e2 || [], _getPrototypeOf(t2).constructor) : o2.apply(t2, e2));
}
function _checkPrivateRedeclaration(e2, t2) {
  if (t2.has(e2)) throw new TypeError("Cannot initialize the same private elements twice on an object");
}
function _classCallCheck(a2, n2) {
  if (!(a2 instanceof n2)) throw new TypeError("Cannot call a class as a function");
}
function _classPrivateFieldGet2(s2, a2) {
  return s2.get(_assertClassBrand(s2, a2));
}
function _classPrivateFieldInitSpec(e2, t2, a2) {
  _checkPrivateRedeclaration(e2, t2), t2.set(e2, a2);
}
function _classPrivateFieldSet2(s2, a2, r2) {
  return s2.set(_assertClassBrand(s2, a2), r2), r2;
}
function _construct(t2, e2, r2) {
  if (_isNativeReflectConstruct()) return Reflect.construct.apply(null, arguments);
  var o2 = [null];
  o2.push.apply(o2, e2);
  var p2 = new (t2.bind.apply(t2, o2))();
  return p2;
}
function _defineProperties(e2, r2) {
  for (var t2 = 0; t2 < r2.length; t2++) {
    var o2 = r2[t2];
    o2.enumerable = o2.enumerable || false, o2.configurable = true, "value" in o2 && (o2.writable = true), Object.defineProperty(e2, _toPropertyKey2(o2.key), o2);
  }
}
function _createClass(e2, r2, t2) {
  return r2 && _defineProperties(e2.prototype, r2), Object.defineProperty(e2, "prototype", {
    writable: false
  }), e2;
}
function _defineProperty2(e2, r2, t2) {
  return (r2 = _toPropertyKey2(r2)) in e2 ? Object.defineProperty(e2, r2, {
    value: t2,
    enumerable: true,
    configurable: true,
    writable: true
  }) : e2[r2] = t2, e2;
}
function _get() {
  return _get = "undefined" != typeof Reflect && Reflect.get ? Reflect.get.bind() : function(e2, t2, r2) {
    var p2 = _superPropBase(e2, t2);
    if (p2) {
      var n2 = Object.getOwnPropertyDescriptor(p2, t2);
      return n2.get ? n2.get.call(arguments.length < 3 ? e2 : r2) : n2.value;
    }
  }, _get.apply(null, arguments);
}
function _getPrototypeOf(t2) {
  return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(t3) {
    return t3.__proto__ || Object.getPrototypeOf(t3);
  }, _getPrototypeOf(t2);
}
function _inherits(t2, e2) {
  if ("function" != typeof e2 && null !== e2) throw new TypeError("Super expression must either be null or a function");
  t2.prototype = Object.create(e2 && e2.prototype, {
    constructor: {
      value: t2,
      writable: true,
      configurable: true
    }
  }), Object.defineProperty(t2, "prototype", {
    writable: false
  }), e2 && _setPrototypeOf2(t2, e2);
}
function _isNativeReflectConstruct() {
  try {
    var t2 = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
    }));
  } catch (t3) {
  }
  return (_isNativeReflectConstruct = function() {
    return !!t2;
  })();
}
function _iterableToArray3(r2) {
  if ("undefined" != typeof Symbol && null != r2[Symbol.iterator] || null != r2["@@iterator"]) return Array.from(r2);
}
function _iterableToArrayLimit3(r2, l2) {
  var t2 = null == r2 ? null : "undefined" != typeof Symbol && r2[Symbol.iterator] || r2["@@iterator"];
  if (null != t2) {
    var e2, n2, i2, u2, a2 = [], f2 = true, o2 = false;
    try {
      if (i2 = (t2 = t2.call(r2)).next, 0 === l2) {
        if (Object(t2) !== t2) return;
        f2 = false;
      } else for (; !(f2 = (e2 = i2.call(t2)).done) && (a2.push(e2.value), a2.length !== l2); f2 = true) ;
    } catch (r3) {
      o2 = true, n2 = r3;
    } finally {
      try {
        if (!f2 && null != t2.return && (u2 = t2.return(), Object(u2) !== u2)) return;
      } finally {
        if (o2) throw n2;
      }
    }
    return a2;
  }
}
function _nonIterableRest3() {
  throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _nonIterableSpread3() {
  throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function ownKeys(e2, r2) {
  var t2 = Object.keys(e2);
  if (Object.getOwnPropertySymbols) {
    var o2 = Object.getOwnPropertySymbols(e2);
    r2 && (o2 = o2.filter(function(r3) {
      return Object.getOwnPropertyDescriptor(e2, r3).enumerable;
    })), t2.push.apply(t2, o2);
  }
  return t2;
}
function _objectSpread2(e2) {
  for (var r2 = 1; r2 < arguments.length; r2++) {
    var t2 = null != arguments[r2] ? arguments[r2] : {};
    r2 % 2 ? ownKeys(Object(t2), true).forEach(function(r3) {
      _defineProperty2(e2, r3, t2[r3]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e2, Object.getOwnPropertyDescriptors(t2)) : ownKeys(Object(t2)).forEach(function(r3) {
      Object.defineProperty(e2, r3, Object.getOwnPropertyDescriptor(t2, r3));
    });
  }
  return e2;
}
function _objectWithoutProperties(e2, t2) {
  if (null == e2) return {};
  var o2, r2, i2 = _objectWithoutPropertiesLoose(e2, t2);
  if (Object.getOwnPropertySymbols) {
    var n2 = Object.getOwnPropertySymbols(e2);
    for (r2 = 0; r2 < n2.length; r2++) o2 = n2[r2], -1 === t2.indexOf(o2) && {}.propertyIsEnumerable.call(e2, o2) && (i2[o2] = e2[o2]);
  }
  return i2;
}
function _objectWithoutPropertiesLoose(r2, e2) {
  if (null == r2) return {};
  var t2 = {};
  for (var n2 in r2) if ({}.hasOwnProperty.call(r2, n2)) {
    if (-1 !== e2.indexOf(n2)) continue;
    t2[n2] = r2[n2];
  }
  return t2;
}
function _possibleConstructorReturn(t2, e2) {
  if (e2 && ("object" == typeof e2 || "function" == typeof e2)) return e2;
  if (void 0 !== e2) throw new TypeError("Derived constructors may only return object or undefined");
  return _assertThisInitialized2(t2);
}
function _regeneratorRuntime() {
  _regeneratorRuntime = function() {
    return r2;
  };
  var t2, r2 = {}, e2 = Object.prototype, n2 = e2.hasOwnProperty, o2 = "function" == typeof Symbol ? Symbol : {}, i2 = o2.iterator || "@@iterator", a2 = o2.asyncIterator || "@@asyncIterator", u2 = o2.toStringTag || "@@toStringTag";
  function c2(t3, r3, e3, n3) {
    return Object.defineProperty(t3, r3, {
      value: e3,
      enumerable: !n3,
      configurable: !n3,
      writable: !n3
    });
  }
  try {
    c2({}, "");
  } catch (t3) {
    c2 = function(t4, r3, e3) {
      return t4[r3] = e3;
    };
  }
  function h2(r3, e3, n3, o3) {
    var i3 = e3 && e3.prototype instanceof Generator ? e3 : Generator, a3 = Object.create(i3.prototype);
    return c2(a3, "_invoke", /* @__PURE__ */ function(r4, e4, n4) {
      var o4 = 1;
      return function(i4, a4) {
        if (3 === o4) throw Error("Generator is already running");
        if (4 === o4) {
          if ("throw" === i4) throw a4;
          return {
            value: t2,
            done: true
          };
        }
        for (n4.method = i4, n4.arg = a4; ; ) {
          var u3 = n4.delegate;
          if (u3) {
            var c3 = d2(u3, n4);
            if (c3) {
              if (c3 === f2) continue;
              return c3;
            }
          }
          if ("next" === n4.method) n4.sent = n4._sent = n4.arg;
          else if ("throw" === n4.method) {
            if (1 === o4) throw o4 = 4, n4.arg;
            n4.dispatchException(n4.arg);
          } else "return" === n4.method && n4.abrupt("return", n4.arg);
          o4 = 3;
          var h3 = s2(r4, e4, n4);
          if ("normal" === h3.type) {
            if (o4 = n4.done ? 4 : 2, h3.arg === f2) continue;
            return {
              value: h3.arg,
              done: n4.done
            };
          }
          "throw" === h3.type && (o4 = 4, n4.method = "throw", n4.arg = h3.arg);
        }
      };
    }(r3, n3, new Context(o3 || [])), true), a3;
  }
  function s2(t3, r3, e3) {
    try {
      return {
        type: "normal",
        arg: t3.call(r3, e3)
      };
    } catch (t4) {
      return {
        type: "throw",
        arg: t4
      };
    }
  }
  r2.wrap = h2;
  var f2 = {};
  function Generator() {
  }
  function GeneratorFunction() {
  }
  function GeneratorFunctionPrototype() {
  }
  var l2 = {};
  c2(l2, i2, function() {
    return this;
  });
  var p2 = Object.getPrototypeOf, y2 = p2 && p2(p2(x2([])));
  y2 && y2 !== e2 && n2.call(y2, i2) && (l2 = y2);
  var v2 = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(l2);
  function g2(t3) {
    ["next", "throw", "return"].forEach(function(r3) {
      c2(t3, r3, function(t4) {
        return this._invoke(r3, t4);
      });
    });
  }
  function AsyncIterator(t3, r3) {
    function e3(o4, i3, a3, u3) {
      var c3 = s2(t3[o4], t3, i3);
      if ("throw" !== c3.type) {
        var h3 = c3.arg, f3 = h3.value;
        return f3 && "object" == typeof f3 && n2.call(f3, "__await") ? r3.resolve(f3.__await).then(function(t4) {
          e3("next", t4, a3, u3);
        }, function(t4) {
          e3("throw", t4, a3, u3);
        }) : r3.resolve(f3).then(function(t4) {
          h3.value = t4, a3(h3);
        }, function(t4) {
          return e3("throw", t4, a3, u3);
        });
      }
      u3(c3.arg);
    }
    var o3;
    c2(this, "_invoke", function(t4, n3) {
      function i3() {
        return new r3(function(r4, o4) {
          e3(t4, n3, r4, o4);
        });
      }
      return o3 = o3 ? o3.then(i3, i3) : i3();
    }, true);
  }
  function d2(r3, e3) {
    var n3 = e3.method, o3 = r3.i[n3];
    if (o3 === t2) return e3.delegate = null, "throw" === n3 && r3.i.return && (e3.method = "return", e3.arg = t2, d2(r3, e3), "throw" === e3.method) || "return" !== n3 && (e3.method = "throw", e3.arg = new TypeError("The iterator does not provide a '" + n3 + "' method")), f2;
    var i3 = s2(o3, r3.i, e3.arg);
    if ("throw" === i3.type) return e3.method = "throw", e3.arg = i3.arg, e3.delegate = null, f2;
    var a3 = i3.arg;
    return a3 ? a3.done ? (e3[r3.r] = a3.value, e3.next = r3.n, "return" !== e3.method && (e3.method = "next", e3.arg = t2), e3.delegate = null, f2) : a3 : (e3.method = "throw", e3.arg = new TypeError("iterator result is not an object"), e3.delegate = null, f2);
  }
  function w2(t3) {
    this.tryEntries.push(t3);
  }
  function m2(r3) {
    var e3 = r3[4] || {};
    e3.type = "normal", e3.arg = t2, r3[4] = e3;
  }
  function Context(t3) {
    this.tryEntries = [[-1]], t3.forEach(w2, this), this.reset(true);
  }
  function x2(r3) {
    if (null != r3) {
      var e3 = r3[i2];
      if (e3) return e3.call(r3);
      if ("function" == typeof r3.next) return r3;
      if (!isNaN(r3.length)) {
        var o3 = -1, a3 = function e4() {
          for (; ++o3 < r3.length; ) if (n2.call(r3, o3)) return e4.value = r3[o3], e4.done = false, e4;
          return e4.value = t2, e4.done = true, e4;
        };
        return a3.next = a3;
      }
    }
    throw new TypeError(typeof r3 + " is not iterable");
  }
  return GeneratorFunction.prototype = GeneratorFunctionPrototype, c2(v2, "constructor", GeneratorFunctionPrototype), c2(GeneratorFunctionPrototype, "constructor", GeneratorFunction), GeneratorFunction.displayName = c2(GeneratorFunctionPrototype, u2, "GeneratorFunction"), r2.isGeneratorFunction = function(t3) {
    var r3 = "function" == typeof t3 && t3.constructor;
    return !!r3 && (r3 === GeneratorFunction || "GeneratorFunction" === (r3.displayName || r3.name));
  }, r2.mark = function(t3) {
    return Object.setPrototypeOf ? Object.setPrototypeOf(t3, GeneratorFunctionPrototype) : (t3.__proto__ = GeneratorFunctionPrototype, c2(t3, u2, "GeneratorFunction")), t3.prototype = Object.create(v2), t3;
  }, r2.awrap = function(t3) {
    return {
      __await: t3
    };
  }, g2(AsyncIterator.prototype), c2(AsyncIterator.prototype, a2, function() {
    return this;
  }), r2.AsyncIterator = AsyncIterator, r2.async = function(t3, e3, n3, o3, i3) {
    void 0 === i3 && (i3 = Promise);
    var a3 = new AsyncIterator(h2(t3, e3, n3, o3), i3);
    return r2.isGeneratorFunction(e3) ? a3 : a3.next().then(function(t4) {
      return t4.done ? t4.value : a3.next();
    });
  }, g2(v2), c2(v2, u2, "Generator"), c2(v2, i2, function() {
    return this;
  }), c2(v2, "toString", function() {
    return "[object Generator]";
  }), r2.keys = function(t3) {
    var r3 = Object(t3), e3 = [];
    for (var n3 in r3) e3.unshift(n3);
    return function t4() {
      for (; e3.length; ) if ((n3 = e3.pop()) in r3) return t4.value = n3, t4.done = false, t4;
      return t4.done = true, t4;
    };
  }, r2.values = x2, Context.prototype = {
    constructor: Context,
    reset: function(r3) {
      if (this.prev = this.next = 0, this.sent = this._sent = t2, this.done = false, this.delegate = null, this.method = "next", this.arg = t2, this.tryEntries.forEach(m2), !r3) for (var e3 in this) "t" === e3.charAt(0) && n2.call(this, e3) && !isNaN(+e3.slice(1)) && (this[e3] = t2);
    },
    stop: function() {
      this.done = true;
      var t3 = this.tryEntries[0][4];
      if ("throw" === t3.type) throw t3.arg;
      return this.rval;
    },
    dispatchException: function(r3) {
      if (this.done) throw r3;
      var e3 = this;
      function n3(t3) {
        a3.type = "throw", a3.arg = r3, e3.next = t3;
      }
      for (var o3 = e3.tryEntries.length - 1; o3 >= 0; --o3) {
        var i3 = this.tryEntries[o3], a3 = i3[4], u3 = this.prev, c3 = i3[1], h3 = i3[2];
        if (-1 === i3[0]) return n3("end"), false;
        if (!c3 && !h3) throw Error("try statement without catch or finally");
        if (null != i3[0] && i3[0] <= u3) {
          if (u3 < c3) return this.method = "next", this.arg = t2, n3(c3), true;
          if (u3 < h3) return n3(h3), false;
        }
      }
    },
    abrupt: function(t3, r3) {
      for (var e3 = this.tryEntries.length - 1; e3 >= 0; --e3) {
        var n3 = this.tryEntries[e3];
        if (n3[0] > -1 && n3[0] <= this.prev && this.prev < n3[2]) {
          var o3 = n3;
          break;
        }
      }
      o3 && ("break" === t3 || "continue" === t3) && o3[0] <= r3 && r3 <= o3[2] && (o3 = null);
      var i3 = o3 ? o3[4] : {};
      return i3.type = t3, i3.arg = r3, o3 ? (this.method = "next", this.next = o3[2], f2) : this.complete(i3);
    },
    complete: function(t3, r3) {
      if ("throw" === t3.type) throw t3.arg;
      return "break" === t3.type || "continue" === t3.type ? this.next = t3.arg : "return" === t3.type ? (this.rval = this.arg = t3.arg, this.method = "return", this.next = "end") : "normal" === t3.type && r3 && (this.next = r3), f2;
    },
    finish: function(t3) {
      for (var r3 = this.tryEntries.length - 1; r3 >= 0; --r3) {
        var e3 = this.tryEntries[r3];
        if (e3[2] === t3) return this.complete(e3[4], e3[3]), m2(e3), f2;
      }
    },
    catch: function(t3) {
      for (var r3 = this.tryEntries.length - 1; r3 >= 0; --r3) {
        var e3 = this.tryEntries[r3];
        if (e3[0] === t3) {
          var n3 = e3[4];
          if ("throw" === n3.type) {
            var o3 = n3.arg;
            m2(e3);
          }
          return o3;
        }
      }
      throw Error("illegal catch attempt");
    },
    delegateYield: function(r3, e3, n3) {
      return this.delegate = {
        i: x2(r3),
        r: e3,
        n: n3
      }, "next" === this.method && (this.arg = t2), f2;
    }
  }, r2;
}
function _setPrototypeOf2(t2, e2) {
  return _setPrototypeOf2 = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(t3, e3) {
    return t3.__proto__ = e3, t3;
  }, _setPrototypeOf2(t2, e2);
}
function _slicedToArray3(r2, e2) {
  return _arrayWithHoles3(r2) || _iterableToArrayLimit3(r2, e2) || _unsupportedIterableToArray3(r2, e2) || _nonIterableRest3();
}
function _superPropBase(t2, o2) {
  for (; !{}.hasOwnProperty.call(t2, o2) && null !== (t2 = _getPrototypeOf(t2)); ) ;
  return t2;
}
function _superPropGet(t2, o2, e2, r2) {
  var p2 = _get(_getPrototypeOf(t2.prototype), o2, e2);
  return "function" == typeof p2 ? function(t3) {
    return p2.apply(e2, t3);
  } : p2;
}
function _toConsumableArray3(r2) {
  return _arrayWithoutHoles3(r2) || _iterableToArray3(r2) || _unsupportedIterableToArray3(r2) || _nonIterableSpread3();
}
function _toPrimitive2(t2, r2) {
  if ("object" != typeof t2 || !t2) return t2;
  var e2 = t2[Symbol.toPrimitive];
  if (void 0 !== e2) {
    var i2 = e2.call(t2, r2);
    if ("object" != typeof i2) return i2;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return ("string" === r2 ? String : Number)(t2);
}
function _toPropertyKey2(t2) {
  var i2 = _toPrimitive2(t2, "string");
  return "symbol" == typeof i2 ? i2 : i2 + "";
}
function _unsupportedIterableToArray3(r2, a2) {
  if (r2) {
    if ("string" == typeof r2) return _arrayLikeToArray3(r2, a2);
    var t2 = {}.toString.call(r2).slice(8, -1);
    return "Object" === t2 && r2.constructor && (t2 = r2.constructor.name), "Map" === t2 || "Set" === t2 ? Array.from(r2) : "Arguments" === t2 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t2) ? _arrayLikeToArray3(r2, a2) : void 0;
  }
}
var _materialDispose = function materialDispose(material) {
  if (material instanceof Array) {
    material.forEach(_materialDispose);
  } else {
    if (material.map) {
      material.map.dispose();
    }
    material.dispose();
  }
};
var _deallocate = function deallocate(obj) {
  if (obj.geometry) {
    obj.geometry.dispose();
  }
  if (obj.material) {
    _materialDispose(obj.material);
  }
  if (obj.texture) {
    obj.texture.dispose();
  }
  if (obj.children) {
    obj.children.forEach(_deallocate);
  }
};
var emptyObject = function emptyObject2(obj) {
  if (obj && obj.children) while (obj.children.length) {
    var childObj = obj.children[0];
    obj.remove(childObj);
    _deallocate(childObj);
  }
};
function linkKapsule(kapsulePropName, kapsuleType) {
  var dummyK = new kapsuleType();
  return {
    linkProp: function linkProp(prop) {
      return {
        "default": dummyK[prop](),
        onChange: function onChange15(v2, state) {
          state[kapsulePropName][prop](v2);
        },
        triggerUpdate: false
      };
    },
    linkMethod: function linkMethod(method) {
      return function(state) {
        var kapsuleInstance = state[kapsulePropName];
        for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
          args[_key - 1] = arguments[_key];
        }
        var returnVal = kapsuleInstance[method].apply(kapsuleInstance, args);
        return returnVal === kapsuleInstance ? this : returnVal;
      };
    }
  };
}
var GLOBE_RADIUS = 100;
function getGlobeRadius() {
  return GLOBE_RADIUS;
}
function polar2Cartesian(lat, lng) {
  var relAltitude = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;
  var phi = (90 - lat) * Math.PI / 180;
  var theta = (90 - lng) * Math.PI / 180;
  var r2 = GLOBE_RADIUS * (1 + relAltitude);
  var phiSin = Math.sin(phi);
  return {
    x: r2 * phiSin * Math.cos(theta),
    y: r2 * Math.cos(phi),
    z: r2 * phiSin * Math.sin(theta)
  };
}
function cartesian2Polar(_ref) {
  var x2 = _ref.x, y2 = _ref.y, z2 = _ref.z;
  var r2 = Math.sqrt(x2 * x2 + y2 * y2 + z2 * z2);
  var phi = Math.acos(y2 / r2);
  var theta = Math.atan2(z2, x2);
  return {
    lat: 90 - phi * 180 / Math.PI,
    lng: 90 - theta * 180 / Math.PI - (theta < -Math.PI / 2 ? 360 : 0),
    // keep within [-180, 180] boundaries
    altitude: r2 / GLOBE_RADIUS - 1
  };
}
function deg2Rad$1(deg) {
  return deg * Math.PI / 180;
}
var THREE$i = window.THREE ? window.THREE : {
  BackSide,
  BufferAttribute,
  Color,
  Mesh,
  ShaderMaterial
};
var vertexShader = "\nuniform float hollowRadius;\n\nvarying vec3 vVertexWorldPosition;\nvarying vec3 vVertexNormal;\nvarying float vCameraDistanceToObjCenter;\nvarying float vVertexAngularDistanceToHollowRadius;\n\nvoid main() {    \n  vVertexNormal	= normalize(normalMatrix * normal);\n  vVertexWorldPosition = (modelMatrix * vec4(position, 1.0)).xyz;\n  \n  vec4 objCenterViewPosition = modelViewMatrix * vec4(0.0, 0.0, 0.0, 1.0);\n  vCameraDistanceToObjCenter = length(objCenterViewPosition);\n  \n  float edgeAngle = atan(hollowRadius / vCameraDistanceToObjCenter);\n  float vertexAngle = acos(dot(normalize(modelViewMatrix * vec4(position, 1.0)), normalize(objCenterViewPosition)));\n  vVertexAngularDistanceToHollowRadius = vertexAngle - edgeAngle;\n\n  gl_Position	= projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n}";
var fragmentShader = "\nuniform vec3 color;\nuniform float coefficient;\nuniform float power;\nuniform float hollowRadius;\n\nvarying vec3 vVertexNormal;\nvarying vec3 vVertexWorldPosition;\nvarying float vCameraDistanceToObjCenter;\nvarying float vVertexAngularDistanceToHollowRadius;\n\nvoid main() {\n  if (vCameraDistanceToObjCenter < hollowRadius) discard; // inside the hollowRadius\n  if (vVertexAngularDistanceToHollowRadius < 0.0) discard; // frag position is within the hollow radius\n\n  vec3 worldCameraToVertex = vVertexWorldPosition - cameraPosition;\n  vec3 viewCameraToVertex	= (viewMatrix * vec4(worldCameraToVertex, 0.0)).xyz;\n  viewCameraToVertex = normalize(viewCameraToVertex);\n  float intensity	= pow(\n    coefficient + dot(vVertexNormal, viewCameraToVertex),\n    power\n  );\n  gl_FragColor = vec4(color, intensity);\n}";
function createGlowMaterial(coefficient, color2, power, hollowRadius) {
  return new THREE$i.ShaderMaterial({
    depthWrite: false,
    transparent: true,
    vertexShader,
    fragmentShader,
    uniforms: {
      coefficient: {
        value: coefficient
      },
      color: {
        value: new THREE$i.Color(color2)
      },
      power: {
        value: power
      },
      hollowRadius: {
        value: hollowRadius
      }
    }
  });
}
function createGlowGeometry(geometry, size) {
  var glowGeometry = geometry.clone();
  var position = new Float32Array(geometry.attributes.position.count * 3);
  for (var idx = 0, len = position.length; idx < len; idx++) {
    var normal = geometry.attributes.normal.array[idx];
    var curPos = geometry.attributes.position.array[idx];
    position[idx] = curPos + normal * size;
  }
  glowGeometry.setAttribute("position", new THREE$i.BufferAttribute(position, 3));
  return glowGeometry;
}
var GlowMesh = function(_THREE$Mesh) {
  function GlowMesh2(geometry) {
    var _this;
    var _ref = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, _ref$color = _ref.color, color2 = _ref$color === void 0 ? "gold" : _ref$color, _ref$size = _ref.size, size = _ref$size === void 0 ? 2 : _ref$size, _ref$coefficient = _ref.coefficient, coefficient = _ref$coefficient === void 0 ? 0.5 : _ref$coefficient, _ref$power = _ref.power, power = _ref$power === void 0 ? 1 : _ref$power, _ref$hollowRadius = _ref.hollowRadius, hollowRadius = _ref$hollowRadius === void 0 ? 0 : _ref$hollowRadius, _ref$backside = _ref.backside, backside = _ref$backside === void 0 ? true : _ref$backside;
    _classCallCheck(this, GlowMesh2);
    _this = _callSuper(this, GlowMesh2);
    var glowGeometry = createGlowGeometry(geometry, size);
    var glowMaterial = createGlowMaterial(coefficient, color2, power, hollowRadius);
    backside && (glowMaterial.side = THREE$i.BackSide);
    _this.geometry = glowGeometry;
    _this.material = glowMaterial;
    return _this;
  }
  _inherits(GlowMesh2, _THREE$Mesh);
  return _createClass(GlowMesh2);
}(THREE$i.Mesh);
var THREE$h = window.THREE ? window.THREE : {
  Color,
  Group,
  LineBasicMaterial,
  LineSegments,
  Mesh,
  MeshPhongMaterial,
  SphereGeometry,
  SRGBColorSpace,
  TextureLoader
};
var GlobeLayerKapsule = index({
  props: {
    globeImageUrl: {},
    bumpImageUrl: {},
    showGlobe: {
      "default": true,
      onChange: function onChange(showGlobe, state) {
        state.globeGroup.visible = !!showGlobe;
      },
      triggerUpdate: false
    },
    showGraticules: {
      "default": false,
      onChange: function onChange2(showGraticules, state) {
        state.graticulesObj.visible = !!showGraticules;
      },
      triggerUpdate: false
    },
    showAtmosphere: {
      "default": true,
      onChange: function onChange3(showAtmosphere, state) {
        state.atmosphereObj && (state.atmosphereObj.visible = !!showAtmosphere);
      },
      triggerUpdate: false
    },
    atmosphereColor: {
      "default": "lightskyblue"
    },
    atmosphereAltitude: {
      "default": 0.15
    },
    globeTileEngineUrl: {
      onChange: function onChange4(v2, state) {
        state.tileEngine.tileUrl = v2;
      }
    },
    globeTileEngineMaxLevel: {
      "default": 17,
      onChange: function onChange5(v2, state) {
        state.tileEngine.maxLevel = v2;
      },
      triggerUpdate: false
    },
    updatePov: {
      onChange: function onChange6(v2, state) {
        state.tileEngine.updatePov(v2);
      },
      triggerUpdate: false
    },
    onReady: {
      "default": function _default() {
      },
      triggerUpdate: false
    }
  },
  methods: {
    globeMaterial: function globeMaterial(state, _globeMaterial) {
      if (_globeMaterial !== void 0) {
        state.globeObj.material = _globeMaterial || state.defaultGlobeMaterial;
        return this;
      }
      return state.globeObj.material;
    },
    _destructor: function _destructor(state) {
      emptyObject(state.globeObj);
      emptyObject(state.tileEngine);
      emptyObject(state.graticulesObj);
    }
  },
  stateInit: function stateInit() {
    var globeGeometry = new THREE$h.SphereGeometry(GLOBE_RADIUS, 75, 75);
    var defaultGlobeMaterial = new THREE$h.MeshPhongMaterial({
      color: 0
    });
    var globeObj = new THREE$h.Mesh(globeGeometry, defaultGlobeMaterial);
    globeObj.rotation.y = -Math.PI / 2;
    var tileEngine = new ThreeSlippyMapGlobe(GLOBE_RADIUS);
    var globeGroup = new THREE$h.Group();
    globeGroup.__globeObjType = "globe";
    globeGroup.add(globeObj);
    globeGroup.add(tileEngine);
    var graticulesObj = new THREE$h.LineSegments(new GeoJsonGeometry(graticule10(), GLOBE_RADIUS, 2), new THREE$h.LineBasicMaterial({
      color: "lightgrey",
      transparent: true,
      opacity: 0.1
    }));
    return {
      globeGroup,
      globeObj,
      graticulesObj,
      defaultGlobeMaterial,
      tileEngine
    };
  },
  init: function init(threeObj, state) {
    emptyObject(threeObj);
    state.scene = threeObj;
    state.scene.add(state.globeGroup);
    state.scene.add(state.graticulesObj);
    state.ready = false;
  },
  update: function update(state, changedProps) {
    var globeMaterial2 = state.globeObj.material;
    state.tileEngine.visible = !(state.globeObj.visible = !state.globeTileEngineUrl);
    if (changedProps.hasOwnProperty("globeImageUrl")) {
      if (!state.globeImageUrl) {
        !globeMaterial2.color && (globeMaterial2.color = new THREE$h.Color(0));
      } else {
        new THREE$h.TextureLoader().load(state.globeImageUrl, function(texture) {
          texture.colorSpace = THREE$h.SRGBColorSpace;
          globeMaterial2.map = texture;
          globeMaterial2.color = null;
          globeMaterial2.needsUpdate = true;
          !state.ready && (state.ready = true) && setTimeout(state.onReady);
        });
      }
    }
    if (changedProps.hasOwnProperty("bumpImageUrl")) {
      if (!state.bumpImageUrl) {
        globeMaterial2.bumpMap = null;
        globeMaterial2.needsUpdate = true;
      } else {
        state.bumpImageUrl && new THREE$h.TextureLoader().load(state.bumpImageUrl, function(texture) {
          globeMaterial2.bumpMap = texture;
          globeMaterial2.needsUpdate = true;
        });
      }
    }
    if (changedProps.hasOwnProperty("atmosphereColor") || changedProps.hasOwnProperty("atmosphereAltitude")) {
      if (state.atmosphereObj) {
        state.scene.remove(state.atmosphereObj);
        emptyObject(state.atmosphereObj);
      }
      if (state.atmosphereColor && state.atmosphereAltitude) {
        var obj = state.atmosphereObj = new GlowMesh(state.globeObj.geometry, {
          color: state.atmosphereColor,
          size: GLOBE_RADIUS * state.atmosphereAltitude,
          hollowRadius: GLOBE_RADIUS,
          coefficient: 0.1,
          power: 3.5
          // dispersion
        });
        obj.visible = !!state.showAtmosphere;
        obj.__globeObjType = "atmosphere";
        state.scene.add(obj);
      }
    }
    if (!state.ready && (!state.globeImageUrl || state.globeTileEngineUrl)) {
      state.ready = true;
      state.onReady();
    }
  }
});
var colorStr2Hex = function colorStr2Hex2(str) {
  return isNaN(str) ? parseInt(tinycolor(str).toHex(), 16) : str;
};
var colorAlpha = function colorAlpha2(str) {
  return str && isNaN(str) ? color(str).opacity : 1;
};
var color2ShaderArr = function color2ShaderArr2(str) {
  var includeAlpha = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;
  var sRGBColorSpace = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;
  var color2;
  var alpha = 1;
  var rgbaMatch = /^rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.eE+-]+)\s*\)$/.exec(str.trim().toLowerCase());
  if (rgbaMatch) {
    var _rgbaMatch$slice = rgbaMatch.slice(1), _rgbaMatch$slice2 = _slicedToArray3(_rgbaMatch$slice, 4), r2 = _rgbaMatch$slice2[0], g2 = _rgbaMatch$slice2[1], b = _rgbaMatch$slice2[2], a2 = _rgbaMatch$slice2[3];
    color2 = new Color("rgb(".concat(+r2, ",").concat(+g2, ",").concat(+b, ")"));
    alpha = Math.min(+a2, 1);
  } else {
    color2 = new Color(str);
  }
  sRGBColorSpace && color2.convertLinearToSRGB();
  var rgbArr = color2.toArray();
  return includeAlpha ? [].concat(_toConsumableArray3(rgbArr), [alpha]) : rgbArr;
};
function setMaterialOpacity(material, opacity, depthWrite) {
  material.opacity = opacity;
  material.transparent = opacity < 1;
  material.depthWrite = opacity >= 1;
  return material;
}
var THREE$g = window.THREE ? window.THREE : {
  BufferAttribute
};
function array2BufferAttr(data) {
  var itemSize = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;
  var ArrayClass = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : Float32Array;
  if (itemSize === 1) {
    return new THREE$g.BufferAttribute(new ArrayClass(data), itemSize);
  }
  var ba = new THREE$g.BufferAttribute(new ArrayClass(data.length * itemSize), itemSize);
  for (var idx = 0, l2 = data.length; idx < l2; idx++) {
    ba.set(data[idx], idx * itemSize);
  }
  return ba;
}
function bufferAttr2Array(ba) {
  var itemSize = ba.itemSize;
  var res = [];
  for (var i2 = 0; i2 < ba.count; i2++) {
    res.push(ba.array.slice(i2 * itemSize, (i2 + 1) * itemSize));
  }
  return res;
}
var _dataBindAttr = /* @__PURE__ */ new WeakMap();
var _objBindAttr = /* @__PURE__ */ new WeakMap();
var _removeDelay = /* @__PURE__ */ new WeakMap();
var ThreeDigest = function(_DataBindMapper) {
  function ThreeDigest2(scene3) {
    var _this;
    var _ref = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, _ref$dataBindAttr = _ref.dataBindAttr, dataBindAttr = _ref$dataBindAttr === void 0 ? "__data" : _ref$dataBindAttr, _ref$objBindAttr = _ref.objBindAttr, objBindAttr = _ref$objBindAttr === void 0 ? "__threeObj" : _ref$objBindAttr, _ref$removeDelay = _ref.removeDelay, removeDelay = _ref$removeDelay === void 0 ? 0 : _ref$removeDelay;
    _classCallCheck(this, ThreeDigest2);
    _this = _callSuper(this, ThreeDigest2);
    _defineProperty2(_this, "scene", void 0);
    _classPrivateFieldInitSpec(_this, _dataBindAttr, void 0);
    _classPrivateFieldInitSpec(_this, _objBindAttr, void 0);
    _classPrivateFieldInitSpec(_this, _removeDelay, void 0);
    _this.scene = scene3;
    _classPrivateFieldSet2(_dataBindAttr, _this, dataBindAttr);
    _classPrivateFieldSet2(_objBindAttr, _this, objBindAttr);
    _classPrivateFieldSet2(_removeDelay, _this, removeDelay);
    _this.onRemoveObj(function() {
    });
    return _this;
  }
  _inherits(ThreeDigest2, _DataBindMapper);
  return _createClass(ThreeDigest2, [{
    key: "onCreateObj",
    value: function onCreateObj(fn) {
      var _this2 = this;
      _superPropGet(ThreeDigest2, "onCreateObj", this)([function(d2) {
        var obj = fn(d2);
        d2[_classPrivateFieldGet2(_objBindAttr, _this2)] = obj;
        obj[_classPrivateFieldGet2(_dataBindAttr, _this2)] = d2;
        _this2.scene.add(obj);
        return obj;
      }]);
      return this;
    }
  }, {
    key: "onRemoveObj",
    value: function onRemoveObj(fn) {
      var _this3 = this;
      _superPropGet(ThreeDigest2, "onRemoveObj", this)([function(obj, dId) {
        var d2 = _superPropGet(ThreeDigest2, "getData", _this3)([obj]);
        fn(obj, dId);
        var removeFn = function removeFn2() {
          _this3.scene.remove(obj);
          emptyObject(obj);
          delete d2[_classPrivateFieldGet2(_objBindAttr, _this3)];
        };
        _classPrivateFieldGet2(_removeDelay, _this3) ? setTimeout(removeFn, _classPrivateFieldGet2(_removeDelay, _this3)) : removeFn();
      }]);
      return this;
    }
  }]);
}(DataBindMapper);
var THREE$f = window.THREE ? window.THREE : {
  BufferGeometry,
  CylinderGeometry,
  Matrix4,
  Mesh,
  MeshLambertMaterial,
  Object3D,
  Vector3
};
var bfg$2 = Object.assign({}, BufferGeometryUtils_exports);
var BufferGeometryUtils$2 = bfg$2.BufferGeometryUtils || bfg$2;
var PointsLayerKapsule = index({
  props: {
    pointsData: {
      "default": []
    },
    pointLat: {
      "default": "lat"
    },
    pointLng: {
      "default": "lng"
    },
    pointColor: {
      "default": function _default2() {
        return "#ffffaa";
      }
    },
    pointAltitude: {
      "default": 0.1
    },
    // in units of globe radius
    pointRadius: {
      "default": 0.25
    },
    // in deg
    pointResolution: {
      "default": 12,
      triggerUpdate: false
    },
    // how many slice segments in the cylinder's circumference
    pointsMerge: {
      "default": false
    },
    // boolean. Whether to merge all points into a single mesh for rendering performance
    pointsTransitionDuration: {
      "default": 1e3,
      triggerUpdate: false
    }
    // ms
  },
  init: function init2(threeObj, state, _ref) {
    var tweenGroup = _ref.tweenGroup;
    emptyObject(threeObj);
    state.scene = threeObj;
    state.tweenGroup = tweenGroup;
    state.dataMapper = new ThreeDigest(threeObj, {
      objBindAttr: "__threeObjPoint"
    });
  },
  update: function update2(state, changedProps) {
    var latAccessor = index2(state.pointLat);
    var lngAccessor = index2(state.pointLng);
    var altitudeAccessor = index2(state.pointAltitude);
    var radiusAccessor = index2(state.pointRadius);
    var colorAccessor = index2(state.pointColor);
    var pointGeometry = new THREE$f.CylinderGeometry(1, 1, 1, state.pointResolution);
    pointGeometry.applyMatrix4(new THREE$f.Matrix4().makeRotationX(Math.PI / 2));
    pointGeometry.applyMatrix4(new THREE$f.Matrix4().makeTranslation(0, 0, -0.5));
    var pxPerDeg = 2 * Math.PI * GLOBE_RADIUS / 360;
    var pointMaterials = {};
    if (!state.pointsMerge && changedProps.hasOwnProperty("pointsMerge")) {
      emptyObject(state.scene);
    }
    state.dataMapper.scene = state.pointsMerge ? new THREE$f.Object3D() : state.scene;
    state.dataMapper.onCreateObj(createObj).onUpdateObj(updateObj).digest(state.pointsData);
    if (state.pointsMerge) {
      var pointsGeometry = !state.pointsData.length ? new THREE$f.BufferGeometry() : (BufferGeometryUtils$2.mergeGeometries || BufferGeometryUtils$2.mergeBufferGeometries)(state.pointsData.map(function(d2) {
        var obj = state.dataMapper.getObj(d2);
        var geom = obj.geometry.clone();
        obj.updateMatrix();
        geom.applyMatrix4(obj.matrix);
        var color2 = color2ShaderArr(colorAccessor(d2));
        geom.setAttribute("color", array2BufferAttr(Array(geom.getAttribute("position").count).fill(color2), 4));
        return geom;
      }));
      var points = new THREE$f.Mesh(pointsGeometry, new THREE$f.MeshLambertMaterial({
        color: 16777215,
        transparent: true,
        vertexColors: true
      }));
      points.__globeObjType = "points";
      points.__data = state.pointsData;
      state.dataMapper.clear();
      emptyObject(state.scene);
      state.scene.add(points);
    }
    function createObj() {
      var obj = new THREE$f.Mesh(pointGeometry);
      obj.__globeObjType = "point";
      return obj;
    }
    function updateObj(obj, d2) {
      var applyUpdate = function applyUpdate2(td) {
        var _obj$__currentTargetD = obj.__currentTargetD = td, r2 = _obj$__currentTargetD.r, alt = _obj$__currentTargetD.alt, lat = _obj$__currentTargetD.lat, lng = _obj$__currentTargetD.lng;
        Object.assign(obj.position, polar2Cartesian(lat, lng));
        var globeCenter = state.pointsMerge ? new THREE$f.Vector3(0, 0, 0) : state.scene.localToWorld(new THREE$f.Vector3(0, 0, 0));
        obj.lookAt(globeCenter);
        obj.scale.x = obj.scale.y = Math.min(30, r2) * pxPerDeg;
        obj.scale.z = Math.max(alt * GLOBE_RADIUS, 0.1);
      };
      var targetD = {
        alt: +altitudeAccessor(d2),
        r: +radiusAccessor(d2),
        lat: +latAccessor(d2),
        lng: +lngAccessor(d2)
      };
      var currentTargetD = obj.__currentTargetD || Object.assign({}, targetD, {
        alt: -1e-3
      });
      if (Object.keys(targetD).some(function(k2) {
        return currentTargetD[k2] !== targetD[k2];
      })) {
        if (state.pointsMerge || !state.pointsTransitionDuration || state.pointsTransitionDuration < 0) {
          applyUpdate(targetD);
        } else {
          state.tweenGroup.add(new Tween(currentTargetD).to(targetD, state.pointsTransitionDuration).easing(Easing.Quadratic.InOut).onUpdate(applyUpdate).start());
        }
      }
      if (!state.pointsMerge) {
        var color2 = colorAccessor(d2);
        var opacity = color2 ? colorAlpha(color2) : 0;
        var showCyl = !!opacity;
        obj.visible = showCyl;
        if (showCyl) {
          if (!pointMaterials.hasOwnProperty(color2)) {
            pointMaterials[color2] = new THREE$f.MeshLambertMaterial({
              color: colorStr2Hex(color2),
              transparent: opacity < 1,
              opacity
            });
          }
          obj.material = pointMaterials[color2];
        }
      }
    }
  }
});
var dashedLineShaders = function dashedLineShaders2() {
  return {
    uniforms: {
      // dash param defaults, all relative to full length
      dashOffset: {
        value: 0
      },
      dashSize: {
        value: 1
      },
      gapSize: {
        value: 0
      },
      dashTranslate: {
        value: 0
      }
      // used for animating the dash
    },
    vertexShader: "\n    ".concat(ShaderChunk.common, "\n    ").concat(ShaderChunk.logdepthbuf_pars_vertex, "\n  \n    uniform float dashTranslate; \n\n    attribute vec4 color;\n    varying vec4 vColor;\n    \n    attribute float relDistance;\n    varying float vRelDistance;\n\n    void main() {\n      // pass through colors and distances\n      vColor = color;\n      vRelDistance = relDistance + dashTranslate;\n      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n  \n      ").concat(ShaderChunk.logdepthbuf_vertex, "\n    }\n  "),
    fragmentShader: "\n    ".concat(ShaderChunk.logdepthbuf_pars_fragment, "\n\n    uniform float dashOffset; \n    uniform float dashSize;\n    uniform float gapSize; \n    \n    varying vec4 vColor;\n    varying float vRelDistance;\n    \n    void main() {\n      // ignore pixels in the gap\n      if (vRelDistance < dashOffset) discard;\n      if (mod(vRelDistance - dashOffset, dashSize + gapSize) > dashSize) discard;\n    \n      // set px color: [r, g, b, a], interpolated between vertices \n      gl_FragColor = vColor; \n  \n      ").concat(ShaderChunk.logdepthbuf_fragment, "\n    }\n  ")
  };
};
var invisibleUndergroundShaderExtend = function invisibleUndergroundShaderExtend2(shader) {
  shader.uniforms.surfaceRadius = {
    type: "float",
    value: 0
  };
  shader.vertexShader = ("attribute float surfaceRadius;\nvarying float vSurfaceRadius;\nvarying vec3 vPos;\n" + shader.vertexShader).replace("void main() {", ["void main() {", "vSurfaceRadius = surfaceRadius;", "vPos = position;"].join("\n"));
  shader.fragmentShader = ("uniform float surfaceRadius;\nvarying float vSurfaceRadius;\nvarying vec3 vPos;\n" + shader.fragmentShader).replace("void main() {", ["void main() {", "if (length(vPos) < max(surfaceRadius, vSurfaceRadius)) discard;"].join("\n"));
  return shader;
};
var setRadiusShaderExtend = function setRadiusShaderExtend2(shader) {
  shader.vertexShader = "\n    attribute float r;\n    \n    const float PI = 3.1415926535897932384626433832795;\n    float toRad(in float a) {\n      return a * PI / 180.0;\n    }\n    \n    vec3 Polar2Cartesian(in vec3 c) { // [lat, lng, r]\n      float phi = toRad(90.0 - c.x);\n      float theta = toRad(90.0 - c.y);\n      float r = c.z;\n      return vec3( // x,y,z\n        r * sin(phi) * cos(theta),\n        r * cos(phi),\n        r * sin(phi) * sin(theta)\n      );\n    }\n    \n    vec2 Cartesian2Polar(in vec3 p) {\n      float r = sqrt(p.x * p.x + p.y * p.y + p.z * p.z);\n      float phi = acos(p.y / r);\n      float theta = atan(p.z, p.x);\n      return vec2( // lat,lng\n        90.0 - phi * 180.0 / PI,\n        90.0 - theta * 180.0 / PI - (theta < -PI / 2.0 ? 360.0 : 0.0)\n      );\n    }\n    ".concat(shader.vertexShader.replace("}", "                  \n        vec3 pos = Polar2Cartesian(vec3(Cartesian2Polar(position), r));\n        gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);\n      }\n    "), "\n  ");
  return shader;
};
var applyShaderExtensionToMaterial = function applyShaderExtensionToMaterial2(material, extensionFn) {
  material.onBeforeCompile = function(shader) {
    material.userData.shader = extensionFn(shader);
  };
  return material;
};
var setExtendedMaterialUniforms = function setExtendedMaterialUniforms2(material) {
  var uniformsFn = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : function(u2) {
    return u2;
  };
  if (material.userData.shader) {
    uniformsFn(material.userData.shader.uniforms);
  } else {
    var curFn = material.onBeforeCompile;
    material.onBeforeCompile = function(shader) {
      curFn(shader);
      uniformsFn(shader.uniforms);
    };
  }
};
var _excluded = ["stroke"];
var THREE$e = window.THREE ? window.THREE : {
  BufferGeometry,
  CubicBezierCurve3,
  Curve,
  Group,
  Line,
  Mesh,
  NormalBlending,
  ShaderMaterial,
  TubeGeometry,
  Vector3
};
var FrameTicker$2 = import_frame_ticker.default["default"] || import_frame_ticker.default;
var ArcsLayerKapsule = index({
  props: {
    arcsData: {
      "default": []
    },
    arcStartLat: {
      "default": "startLat"
    },
    arcStartLng: {
      "default": "startLng"
    },
    arcEndLat: {
      "default": "endLat"
    },
    arcEndLng: {
      "default": "endLng"
    },
    arcColor: {
      "default": function _default3() {
        return "#ffffaa";
      }
    },
    // single color, array of colors or color interpolation fn
    arcAltitude: {},
    // in units of globe radius
    arcAltitudeAutoScale: {
      "default": 0.5
    },
    // scale altitude proportional to great-arc distance between the two points
    arcStroke: {},
    // in deg
    arcCurveResolution: {
      "default": 64,
      triggerUpdate: false
    },
    // how many straight segments in the curve
    arcCircularResolution: {
      "default": 6,
      triggerUpdate: false
    },
    // how many slice segments in the tube's circumference
    arcDashLength: {
      "default": 1
    },
    // in units of line length
    arcDashGap: {
      "default": 0
    },
    arcDashInitialGap: {
      "default": 0
    },
    arcDashAnimateTime: {
      "default": 0
    },
    // ms
    arcsTransitionDuration: {
      "default": 1e3,
      triggerUpdate: false
    }
    // ms
  },
  methods: {
    pauseAnimation: function pauseAnimation(state) {
      var _state$ticker;
      (_state$ticker = state.ticker) === null || _state$ticker === void 0 || _state$ticker.pause();
    },
    resumeAnimation: function resumeAnimation(state) {
      var _state$ticker2;
      (_state$ticker2 = state.ticker) === null || _state$ticker2 === void 0 || _state$ticker2.resume();
    },
    _destructor: function _destructor2(state) {
      var _state$ticker3;
      state.sharedMaterial.dispose();
      (_state$ticker3 = state.ticker) === null || _state$ticker3 === void 0 || _state$ticker3.dispose();
    }
  },
  stateInit: function stateInit2(_ref) {
    var tweenGroup = _ref.tweenGroup;
    return {
      tweenGroup,
      ticker: new FrameTicker$2(),
      sharedMaterial: new THREE$e.ShaderMaterial(_objectSpread2(_objectSpread2({}, dashedLineShaders()), {}, {
        transparent: true,
        blending: THREE$e.NormalBlending
      }))
    };
  },
  init: function init3(threeObj, state) {
    emptyObject(threeObj);
    state.scene = threeObj;
    state.dataMapper = new ThreeDigest(threeObj, {
      objBindAttr: "__threeObjArc"
    }).onCreateObj(function() {
      var obj = new THREE$e.Group();
      obj.__globeObjType = "arc";
      return obj;
    });
    state.ticker.onTick.add(function(_2, timeDelta) {
      state.dataMapper.entries().map(function(_ref2) {
        var _ref3 = _slicedToArray3(_ref2, 2), obj = _ref3[1];
        return obj;
      }).filter(function(o2) {
        return o2.children.length && o2.children[0].material && o2.children[0].__dashAnimateStep;
      }).forEach(function(o2) {
        var obj = o2.children[0];
        var step = obj.__dashAnimateStep * timeDelta;
        var curTranslate = obj.material.uniforms.dashTranslate.value % 1e9;
        obj.material.uniforms.dashTranslate.value = curTranslate + step;
      });
    });
  },
  update: function update3(state) {
    var startLatAccessor = index2(state.arcStartLat);
    var startLngAccessor = index2(state.arcStartLng);
    var endLatAccessor = index2(state.arcEndLat);
    var endLngAccessor = index2(state.arcEndLng);
    var altitudeAccessor = index2(state.arcAltitude);
    var altitudeAutoScaleAccessor = index2(state.arcAltitudeAutoScale);
    var strokeAccessor = index2(state.arcStroke);
    var colorAccessor = index2(state.arcColor);
    var dashLengthAccessor = index2(state.arcDashLength);
    var dashGapAccessor = index2(state.arcDashGap);
    var dashInitialGapAccessor = index2(state.arcDashInitialGap);
    var dashAnimateTimeAccessor = index2(state.arcDashAnimateTime);
    state.dataMapper.onUpdateObj(function(group, arc) {
      var stroke = strokeAccessor(arc);
      var useTube = stroke !== null && stroke !== void 0;
      if (!group.children.length || useTube !== (group.children[0].type === "Mesh")) {
        emptyObject(group);
        var _obj = useTube ? new THREE$e.Mesh() : new THREE$e.Line(new THREE$e.BufferGeometry());
        _obj.material = state.sharedMaterial.clone();
        group.add(_obj);
      }
      var obj = group.children[0];
      Object.assign(obj.material.uniforms, {
        dashSize: {
          value: dashLengthAccessor(arc)
        },
        gapSize: {
          value: dashGapAccessor(arc)
        },
        dashOffset: {
          value: dashInitialGapAccessor(arc)
        }
      });
      var dashAnimateTime = dashAnimateTimeAccessor(arc);
      obj.__dashAnimateStep = dashAnimateTime > 0 ? 1e3 / dashAnimateTime : 0;
      var vertexColorArray = calcColorVertexArray(
        colorAccessor(arc),
        // single, array of colors or interpolator
        state.arcCurveResolution,
        // numSegments
        useTube ? state.arcCircularResolution + 1 : 1
        // num vertices per segment
      );
      var vertexRelDistanceArray = calcVertexRelDistances(
        state.arcCurveResolution,
        // numSegments
        useTube ? state.arcCircularResolution + 1 : 1,
        // num vertices per segment
        true
        // run from end to start, to animate in the correct direction
      );
      obj.geometry.setAttribute("color", vertexColorArray);
      obj.geometry.setAttribute("relDistance", vertexRelDistanceArray);
      var applyUpdate = function applyUpdate2(td) {
        var _group$__currentTarge = group.__currentTargetD = td, stroke2 = _group$__currentTarge.stroke, curveD = _objectWithoutProperties(_group$__currentTarge, _excluded);
        var curve = calcCurve(curveD);
        if (useTube) {
          obj.geometry && obj.geometry.dispose();
          obj.geometry = new THREE$e.TubeGeometry(curve, state.arcCurveResolution, stroke2 / 2, state.arcCircularResolution);
          obj.geometry.setAttribute("color", vertexColorArray);
          obj.geometry.setAttribute("relDistance", vertexRelDistanceArray);
        } else {
          obj.geometry.setFromPoints(curve.getPoints(state.arcCurveResolution));
        }
      };
      var targetD = {
        stroke,
        alt: altitudeAccessor(arc),
        altAutoScale: +altitudeAutoScaleAccessor(arc),
        startLat: +startLatAccessor(arc),
        startLng: +startLngAccessor(arc),
        endLat: +endLatAccessor(arc),
        endLng: +endLngAccessor(arc)
      };
      var currentTargetD = group.__currentTargetD || Object.assign({}, targetD, {
        altAutoScale: -1e-3
      });
      if (Object.keys(targetD).some(function(k2) {
        return currentTargetD[k2] !== targetD[k2];
      })) {
        if (!state.arcsTransitionDuration || state.arcsTransitionDuration < 0) {
          applyUpdate(targetD);
        } else {
          state.tweenGroup.add(new Tween(currentTargetD).to(targetD, state.arcsTransitionDuration).easing(Easing.Quadratic.InOut).onUpdate(applyUpdate).start());
        }
      }
    }).digest(state.arcsData);
    function calcCurve(_ref4) {
      var alt = _ref4.alt, altAutoScale = _ref4.altAutoScale, startLat = _ref4.startLat, startLng = _ref4.startLng, endLat = _ref4.endLat, endLng = _ref4.endLng;
      var getVec = function getVec2(_ref5) {
        var _ref6 = _slicedToArray3(_ref5, 3), lng = _ref6[0], lat = _ref6[1], alt2 = _ref6[2];
        var _polar2Cartesian = polar2Cartesian(lat, lng, alt2), x2 = _polar2Cartesian.x, y2 = _polar2Cartesian.y, z2 = _polar2Cartesian.z;
        return new THREE$e.Vector3(x2, y2, z2);
      };
      var startPnt = [startLng, startLat];
      var endPnt = [endLng, endLat];
      var altitude = alt;
      (altitude === null || altitude === void 0) && // by default set altitude proportional to the great-arc distance
      (altitude = distance_default(startPnt, endPnt) / 2 * altAutoScale);
      if (altitude) {
        var interpolate = interpolate_default(startPnt, endPnt);
        var _map = [0.25, 0.75].map(function(t2) {
          return [].concat(_toConsumableArray3(interpolate(t2)), [altitude * 1.5]);
        }), _map2 = _slicedToArray3(_map, 2), m1Pnt = _map2[0], m2Pnt = _map2[1];
        var curve = _construct(THREE$e.CubicBezierCurve3, _toConsumableArray3([startPnt, m1Pnt, m2Pnt, endPnt].map(getVec)));
        return curve;
      } else {
        var _alt = 1e-3;
        return calcSphereArc.apply(void 0, _toConsumableArray3([[].concat(startPnt, [_alt]), [].concat(endPnt, [_alt])].map(getVec)));
      }
      function calcSphereArc(startVec, endVec) {
        var angle = startVec.angleTo(endVec);
        var getGreatCirclePoint = angle === 0 ? function() {
          return startVec.clone();
        } : function(t2) {
          return new THREE$e.Vector3().addVectors(startVec.clone().multiplyScalar(Math.sin((1 - t2) * angle)), endVec.clone().multiplyScalar(Math.sin(t2 * angle))).divideScalar(Math.sin(angle));
        };
        var sphereArc = new THREE$e.Curve();
        sphereArc.getPoint = getGreatCirclePoint;
        return sphereArc;
      }
    }
    function calcColorVertexArray(colors, numSegments) {
      var numVerticesPerSegment = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1;
      var numVerticesGroup = numSegments + 1;
      var getVertexColor;
      if (colors instanceof Array || colors instanceof Function) {
        var colorInterpolator = colors instanceof Array ? linear().domain(colors.map(function(_2, idx) {
          return idx / (colors.length - 1);
        })).range(colors) : colors;
        getVertexColor = function getVertexColor2(t2) {
          return color2ShaderArr(colorInterpolator(t2), true, true);
        };
      } else {
        var vertexColor = color2ShaderArr(colors, true, true);
        getVertexColor = function getVertexColor2() {
          return vertexColor;
        };
      }
      var vertexColors = [];
      for (var v2 = 0, l2 = numVerticesGroup; v2 < l2; v2++) {
        var _vertexColor = getVertexColor(v2 / (l2 - 1));
        for (var s2 = 0; s2 < numVerticesPerSegment; s2++) {
          vertexColors.push(_vertexColor);
        }
      }
      return array2BufferAttr(vertexColors, 4);
    }
    function calcVertexRelDistances(numSegments) {
      var numVerticesPerSegment = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;
      var invert = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;
      var numVerticesGroup = numSegments + 1;
      var vertexDistances = [];
      for (var v2 = 0, l2 = numVerticesGroup; v2 < l2; v2++) {
        var relDistance = v2 / (l2 - 1);
        for (var s2 = 0; s2 < numVerticesPerSegment; s2++) {
          vertexDistances.push(relDistance);
        }
      }
      invert && vertexDistances.reverse();
      return array2BufferAttr(vertexDistances, 1);
    }
  }
});
var THREE$d = window.THREE ? window.THREE : {
  BufferGeometry,
  DoubleSide,
  Mesh,
  MeshLambertMaterial,
  Object3D
};
var bfg$1 = Object.assign({}, BufferGeometryUtils_exports);
var BufferGeometryUtils$1 = bfg$1.BufferGeometryUtils || bfg$1;
var HexBinLayerKapsule = index({
  props: {
    hexBinPointsData: {
      "default": []
    },
    hexBinPointLat: {
      "default": "lat"
    },
    hexBinPointLng: {
      "default": "lng"
    },
    hexBinPointWeight: {
      "default": 1
    },
    hexBinResolution: {
      "default": 4
    },
    // 0-15. Level 0 partitions the earth in 122 (mostly) hexagonal cells. Each subsequent level sub-divides the previous in roughly 7 hexagons.
    hexMargin: {
      "default": 0.2
    },
    // in fraction of diameter
    hexTopCurvatureResolution: {
      "default": 5
    },
    // in angular degrees
    hexTopColor: {
      "default": function _default4() {
        return "#ffffaa";
      }
    },
    hexSideColor: {
      "default": function _default5() {
        return "#ffffaa";
      }
    },
    hexAltitude: {
      "default": function _default6(_ref) {
        var sumWeight = _ref.sumWeight;
        return sumWeight * 0.01;
      }
    },
    // in units of globe radius
    hexBinMerge: {
      "default": false
    },
    // boolean. Whether to merge all hex geometries into a single mesh for rendering performance
    hexTransitionDuration: {
      "default": 1e3,
      triggerUpdate: false
    }
    // ms
  },
  init: function init4(threeObj, state, _ref2) {
    var tweenGroup = _ref2.tweenGroup;
    emptyObject(threeObj);
    state.scene = threeObj;
    state.tweenGroup = tweenGroup;
    state.dataMapper = new ThreeDigest(threeObj, {
      objBindAttr: "__threeObjHexbin"
    });
  },
  update: function update4(state, changedProps) {
    var latAccessor = index2(state.hexBinPointLat);
    var lngAccessor = index2(state.hexBinPointLng);
    var weightAccessor = index2(state.hexBinPointWeight);
    var altitudeAccessor = index2(state.hexAltitude);
    var topColorAccessor = index2(state.hexTopColor);
    var sideColorAccessor = index2(state.hexSideColor);
    var marginAccessor = index2(state.hexMargin);
    var byH3Idx = index3(state.hexBinPointsData.map(function(d2) {
      return _objectSpread2(_objectSpread2({}, d2), {}, {
        h3Idx: latLngToCell(latAccessor(d2), lngAccessor(d2), state.hexBinResolution)
      });
    }), "h3Idx");
    var hexBins = Object.entries(byH3Idx).map(function(_ref3) {
      var _ref4 = _slicedToArray3(_ref3, 2), h3Idx = _ref4[0], points = _ref4[1];
      return {
        h3Idx,
        points,
        sumWeight: points.reduce(function(agg, d2) {
          return agg + +weightAccessor(d2);
        }, 0)
      };
    });
    var hexMaterials = {};
    if (!state.hexBinMerge && changedProps.hasOwnProperty("hexBinMerge")) {
      emptyObject(state.scene);
    }
    state.dataMapper.scene = state.hexBinMerge ? new THREE$d.Object3D() : state.scene;
    state.dataMapper.id(function(d2) {
      return d2.h3Idx;
    }).onCreateObj(createObj).onUpdateObj(updateObj).digest(hexBins);
    if (state.hexBinMerge) {
      var hexPointsGeometry = !hexBins.length ? new THREE$d.BufferGeometry() : (BufferGeometryUtils$1.mergeGeometries || BufferGeometryUtils$1.mergeBufferGeometries)(hexBins.map(function(d2) {
        var obj = state.dataMapper.getObj(d2);
        var geom = obj.geometry.toNonIndexed();
        obj.updateMatrix();
        geom.applyMatrix4(obj.matrix);
        var topColor = color2ShaderArr(topColorAccessor(d2));
        var sideColor = color2ShaderArr(sideColorAccessor(d2));
        var nVertices = geom.getAttribute("position").count;
        var topFaceIdx = geom.groups[0].count;
        geom.setAttribute("color", array2BufferAttr(_toConsumableArray3(new Array(nVertices)).map(function(_2, idx) {
          return idx >= topFaceIdx ? topColor : sideColor;
        }), 4));
        return geom;
      }));
      var hexMaterial = new THREE$d.MeshLambertMaterial({
        color: 16777215,
        transparent: true,
        vertexColors: true,
        side: THREE$d.DoubleSide
      });
      hexMaterial.onBeforeCompile = function(shader) {
        hexMaterial.userData.shader = invisibleUndergroundShaderExtend(shader);
      };
      var hexPoints = new THREE$d.Mesh(hexPointsGeometry, hexMaterial);
      hexPoints.__globeObjType = "hexBinPoints";
      hexPoints.__data = hexBins;
      state.dataMapper.clear();
      emptyObject(state.scene);
      state.scene.add(hexPoints);
    }
    function createObj(d2) {
      var obj = new THREE$d.Mesh();
      obj.__hexCenter = cellToLatLng(d2.h3Idx);
      obj.__hexGeoJson = cellToBoundary(d2.h3Idx, true).reverse();
      var centerLng = obj.__hexCenter[1];
      obj.__hexGeoJson.forEach(function(d3) {
        var edgeLng = d3[0];
        if (Math.abs(centerLng - edgeLng) > 170) {
          d3[0] += centerLng > edgeLng ? 360 : -360;
        }
      });
      obj.__globeObjType = "hexbin";
      return obj;
    }
    function updateObj(obj, d2) {
      var relNum = function relNum2(st, end, rat) {
        return st - (st - end) * rat;
      };
      var margin = Math.max(0, Math.min(1, +marginAccessor(d2)));
      var _obj$__hexCenter = _slicedToArray3(obj.__hexCenter, 2), clat = _obj$__hexCenter[0], clng = _obj$__hexCenter[1];
      var geoJson = margin === 0 ? obj.__hexGeoJson : obj.__hexGeoJson.map(function(_ref5) {
        var _ref6 = _slicedToArray3(_ref5, 2), elng = _ref6[0], elat = _ref6[1];
        return [[elng, clng], [elat, clat]].map(function(_ref7) {
          var _ref8 = _slicedToArray3(_ref7, 2), st = _ref8[0], end = _ref8[1];
          return relNum(st, end, margin);
        });
      });
      var topCurvatureResolution = state.hexTopCurvatureResolution;
      obj.geometry && obj.geometry.dispose();
      obj.geometry = new ConicPolygonGeometry([geoJson], 0, GLOBE_RADIUS, false, true, true, topCurvatureResolution);
      var targetD = {
        alt: +altitudeAccessor(d2)
      };
      var applyUpdate = function applyUpdate2(td) {
        var _obj$__currentTargetD = obj.__currentTargetD = td, alt = _obj$__currentTargetD.alt;
        obj.scale.x = obj.scale.y = obj.scale.z = 1 + alt;
        var vertexSurfaceRadius = GLOBE_RADIUS / (alt + 1);
        obj.geometry.setAttribute("surfaceRadius", array2BufferAttr(Array(obj.geometry.getAttribute("position").count).fill(vertexSurfaceRadius), 1));
      };
      var currentTargetD = obj.__currentTargetD || Object.assign({}, targetD, {
        alt: -1e-3
      });
      if (Object.keys(targetD).some(function(k2) {
        return currentTargetD[k2] !== targetD[k2];
      })) {
        if (state.hexBinMerge || !state.hexTransitionDuration || state.hexTransitionDuration < 0) {
          applyUpdate(targetD);
        } else {
          state.tweenGroup.add(new Tween(currentTargetD).to(targetD, state.hexTransitionDuration).easing(Easing.Quadratic.InOut).onUpdate(applyUpdate).start());
        }
      }
      if (!state.hexBinMerge) {
        var sideColor = sideColorAccessor(d2);
        var topColor = topColorAccessor(d2);
        [sideColor, topColor].forEach(function(color2) {
          if (!hexMaterials.hasOwnProperty(color2)) {
            var opacity = colorAlpha(color2);
            hexMaterials[color2] = applyShaderExtensionToMaterial(new THREE$d.MeshLambertMaterial({
              color: colorStr2Hex(color2),
              transparent: opacity < 1,
              opacity,
              side: THREE$d.DoubleSide
            }), invisibleUndergroundShaderExtend);
          }
        });
        obj.material = [sideColor, topColor].map(function(color2) {
          return hexMaterials[color2];
        });
      }
    }
  }
});
var sq = function sq2(x2) {
  return x2 * x2;
};
var toRad = function toRad2(x2) {
  return x2 * Math.PI / 180;
};
function geoDistance(a2, b) {
  var sqrt2 = Math.sqrt;
  var cos2 = Math.cos;
  var hav = function hav2(x2) {
    return sq(Math.sin(x2 / 2));
  };
  var latA = toRad(a2[1]);
  var latB = toRad(b[1]);
  var lngA = toRad(a2[0]);
  var lngB = toRad(b[0]);
  return 2 * Math.asin(sqrt2(hav(latB - latA) + cos2(latA) * cos2(latB) * hav(lngB - lngA)));
}
var sqrt2PI = Math.sqrt(2 * Math.PI);
function gaussianKernel(x2, bw) {
  return Math.exp(-sq(x2 / bw) / 2) / (bw * sqrt2PI);
}
var getGeoKDE = function getGeoKDE2(_ref) {
  var _ref2 = _slicedToArray3(_ref, 2), lng = _ref2[0], lat = _ref2[1];
  var data = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];
  var _ref3 = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {}, _ref3$lngAccessor = _ref3.lngAccessor, lngAccessor = _ref3$lngAccessor === void 0 ? function(d2) {
    return d2[0];
  } : _ref3$lngAccessor, _ref3$latAccessor = _ref3.latAccessor, latAccessor = _ref3$latAccessor === void 0 ? function(d2) {
    return d2[1];
  } : _ref3$latAccessor, _ref3$weightAccessor = _ref3.weightAccessor, weightAccessor = _ref3$weightAccessor === void 0 ? function() {
    return 1;
  } : _ref3$weightAccessor, bandwidth = _ref3.bandwidth;
  var pnt = [lng, lat];
  var bwRad = bandwidth * Math.PI / 180;
  return sum(data.map(function(d2) {
    var weight = weightAccessor(d2);
    if (!weight) return 0;
    var dist = geoDistance(pnt, [lngAccessor(d2), latAccessor(d2)]);
    return gaussianKernel(dist, bwRad) * weight;
  }));
};
var computeGeoKde = function() {
  var _ref4 = _asyncToGenerator(_regeneratorRuntime().mark(function _callee(vertexGeoCoords) {
    var _navigator;
    var data, _ref5, _ref5$lngAccessor, lngAccessor, _ref5$latAccessor, latAccessor, _ref5$weightAccessor, weightAccessor, bandwidth, BW_RADIUS_INFLUENCE, Fn2, If2, uniform2, storage2, _float, instanceIndex2, Loop2, sqrt2, sin2, cos2, asin2, exp2, negate2, sCoords, sData, res, sRes, PI, sqrt2PI2, sq3, hav, geoDistance2, gaussianKernel2, bwRad, maxRRad, n2, computeShaderFn, computeNode, renderer3, _args = arguments;
    return _regeneratorRuntime().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          data = _args.length > 1 && _args[1] !== void 0 ? _args[1] : [];
          _ref5 = _args.length > 2 && _args[2] !== void 0 ? _args[2] : {}, _ref5$lngAccessor = _ref5.lngAccessor, lngAccessor = _ref5$lngAccessor === void 0 ? function(d2) {
            return d2[0];
          } : _ref5$lngAccessor, _ref5$latAccessor = _ref5.latAccessor, latAccessor = _ref5$latAccessor === void 0 ? function(d2) {
            return d2[1];
          } : _ref5$latAccessor, _ref5$weightAccessor = _ref5.weightAccessor, weightAccessor = _ref5$weightAccessor === void 0 ? function() {
            return 1;
          } : _ref5$weightAccessor, bandwidth = _ref5.bandwidth;
          if ((_navigator = navigator) !== null && _navigator !== void 0 && _navigator.gpu) {
            _context.next = 5;
            break;
          }
          console.warn("WebGPU not enabled in browser. Please consider enabling it to improve performance.");
          return _context.abrupt("return", vertexGeoCoords.map(function(coords) {
            return getGeoKDE(coords, data, {
              lngAccessor,
              latAccessor,
              weightAccessor,
              bandwidth
            });
          }));
        case 5:
          BW_RADIUS_INFLUENCE = 4;
          Fn2 = void 0, If2 = void 0, uniform2 = void 0, storage2 = void 0, _float = void 0, instanceIndex2 = void 0, Loop2 = void 0, sqrt2 = void 0, sin2 = void 0, cos2 = void 0, asin2 = void 0, exp2 = void 0, negate2 = void 0;
          sCoords = storage2(new StorageInstancedBufferAttribute(new Float32Array(vertexGeoCoords.flat().map(toRad)), 2), "vec2", vertexGeoCoords.length);
          sData = storage2(new StorageInstancedBufferAttribute(new Float32Array(data.map(function(d2) {
            return [toRad(lngAccessor(d2)), toRad(latAccessor(d2)), weightAccessor(d2)];
          }).flat()), 3), "vec3", data.length);
          res = new StorageInstancedBufferAttribute(vertexGeoCoords.length, 1);
          sRes = storage2(res, "float", vertexGeoCoords.length);
          PI = _float(Math.PI);
          sqrt2PI2 = sqrt2(PI.mul(2));
          sq3 = function sq4(x2) {
            return x2.mul(x2);
          };
          hav = function hav2(x2) {
            return sq3(sin2(x2.div(2)));
          };
          geoDistance2 = function geoDistance3(a2, b) {
            var latA = _float(a2[1]);
            var latB = _float(b[1]);
            var lngA = _float(a2[0]);
            var lngB = _float(b[0]);
            return _float(2).mul(asin2(sqrt2(hav(latB.sub(latA)).add(cos2(latA).mul(cos2(latB)).mul(hav(lngB.sub(lngA)))))));
          };
          gaussianKernel2 = function gaussianKernel3(x2, bw) {
            return exp2(negate2(sq3(x2.div(bw)).div(2))).div(bw.mul(sqrt2PI2));
          };
          bwRad = uniform2(toRad(bandwidth));
          maxRRad = uniform2(toRad(bandwidth * BW_RADIUS_INFLUENCE));
          n2 = uniform2(data.length);
          computeShaderFn = Fn2(function() {
            var coords = sCoords.element(instanceIndex2);
            var res2 = sRes.element(instanceIndex2);
            res2.assign(0);
            Loop2(n2, function(_ref6) {
              var i2 = _ref6.i;
              var d2 = sData.element(i2);
              var weight = d2.z;
              If2(weight, function() {
                var dist = geoDistance2(d2.xy, coords.xy);
                If2(dist && dist.lessThan(maxRRad), function() {
                  res2.addAssign(gaussianKernel2(dist, bwRad).mul(weight));
                });
              });
            });
          });
          computeNode = computeShaderFn().compute(vertexGeoCoords.length);
          renderer3 = new WebGPURenderer();
          _context.next = 25;
          return renderer3.computeAsync(computeNode);
        case 25:
          _context.t0 = Array;
          _context.t1 = Float32Array;
          _context.next = 29;
          return renderer3.getArrayBufferAsync(res);
        case 29:
          _context.t2 = _context.sent;
          _context.t3 = new _context.t1(_context.t2);
          return _context.abrupt("return", _context.t0.from.call(_context.t0, _context.t3));
        case 32:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function computeGeoKde2(_x) {
    return _ref4.apply(this, arguments);
  };
}();
var THREE$c = window.THREE ? window.THREE : {
  Mesh,
  MeshLambertMaterial,
  SphereGeometry
};
var RES_BW_FACTOR = 3.5;
var MIN_RESOLUTION = 0.1;
var NUM_COLORS = 100;
var defaultColorInterpolator = function defaultColorInterpolator2(t2) {
  var c2 = color(turbo_default(t2));
  c2.opacity = Math.cbrt(t2);
  return c2.formatRgb();
};
var HeatmapsLayerKapsule = index({
  props: {
    heatmapsData: {
      "default": []
    },
    heatmapPoints: {
      "default": function _default7(pnts) {
        return pnts;
      }
    },
    heatmapPointLat: {
      "default": function _default8(d2) {
        return d2[0];
      }
    },
    heatmapPointLng: {
      "default": function _default9(d2) {
        return d2[1];
      }
    },
    heatmapPointWeight: {
      "default": 1
    },
    heatmapBandwidth: {
      "default": 2.5
    },
    // Gaussian kernel bandwidth, in angular degrees
    heatmapColorFn: {
      "default": function _default10() {
        return defaultColorInterpolator;
      }
    },
    heatmapColorSaturation: {
      "default": 1.5
    },
    // multiplier for color scale max
    heatmapBaseAltitude: {
      "default": 0.01
    },
    // in units of globe radius
    heatmapTopAltitude: {},
    // in units of globe radius
    heatmapsTransitionDuration: {
      "default": 0,
      triggerUpdate: false
    }
    // ms
  },
  init: function init5(threeObj, state, _ref) {
    var tweenGroup = _ref.tweenGroup;
    emptyObject(threeObj);
    state.scene = threeObj;
    state.tweenGroup = tweenGroup;
    state.dataMapper = new ThreeDigest(threeObj, {
      objBindAttr: "__threeObjHeatmap"
    }).onCreateObj(function() {
      var obj = new THREE$c.Mesh(new THREE$c.SphereGeometry(GLOBE_RADIUS), applyShaderExtensionToMaterial(new THREE$c.MeshLambertMaterial({
        vertexColors: true,
        transparent: true
      }), setRadiusShaderExtend));
      obj.__globeObjType = "heatmap";
      return obj;
    });
  },
  update: function update5(state) {
    var pointsAccessor = index2(state.heatmapPoints);
    var latPntAccessor = index2(state.heatmapPointLat);
    var lngPntAccessor = index2(state.heatmapPointLng);
    var weightPntAccessor = index2(state.heatmapPointWeight);
    var bandwidthAccessor = index2(state.heatmapBandwidth);
    var colorFnAccessor = index2(state.heatmapColorFn);
    var saturationAccessor = index2(state.heatmapColorSaturation);
    var baseAltitudeAccessor = index2(state.heatmapBaseAltitude);
    var topAltitudeAccessor = index2(state.heatmapTopAltitude);
    state.dataMapper.onUpdateObj(function(obj, d2) {
      var bandwidth = bandwidthAccessor(d2);
      var colorFn = colorFnAccessor(d2);
      var saturation = saturationAccessor(d2);
      var baseAlt = baseAltitudeAccessor(d2);
      var topAlt = topAltitudeAccessor(d2);
      var pnts = pointsAccessor(d2).map(function(pnt) {
        var lat = latPntAccessor(pnt);
        var lng = lngPntAccessor(pnt);
        var _polar2Cartesian = polar2Cartesian(lat, lng), x2 = _polar2Cartesian.x, y2 = _polar2Cartesian.y, z2 = _polar2Cartesian.z;
        return {
          x: x2,
          y: y2,
          z: z2,
          lat,
          lng,
          weight: weightPntAccessor(pnt)
        };
      });
      var resolution2 = Math.max(MIN_RESOLUTION, bandwidth / RES_BW_FACTOR);
      var equatorNumSegments = Math.ceil(360 / (resolution2 || -1));
      if (obj.geometry.parameters.widthSegments !== equatorNumSegments) {
        obj.geometry.dispose();
        obj.geometry = new THREE$c.SphereGeometry(GLOBE_RADIUS, equatorNumSegments, equatorNumSegments / 2);
      }
      var vertexCoords = bufferAttr2Array(obj.geometry.getAttribute("position"));
      var vertexGeoCoords = vertexCoords.map(function(_ref2) {
        var _ref3 = _slicedToArray3(_ref2, 3), x2 = _ref3[0], y2 = _ref3[1], z2 = _ref3[2];
        var _cartesian2Polar = cartesian2Polar({
          x: x2,
          y: y2,
          z: z2
        }), lng = _cartesian2Polar.lng, lat = _cartesian2Polar.lat;
        return [lng, lat];
      });
      computeGeoKde(vertexGeoCoords, pnts, {
        latAccessor: function latAccessor(d3) {
          return d3.lat;
        },
        lngAccessor: function lngAccessor(d3) {
          return d3.lng;
        },
        weightAccessor: function weightAccessor(d3) {
          return d3.weight;
        },
        bandwidth
      }).then(function(kdeVals) {
        var colors = _toConsumableArray3(new Array(NUM_COLORS)).map(function(_2, idx) {
          return color2ShaderArr(colorFn(idx / (NUM_COLORS - 1)));
        });
        var applyUpdate = function applyUpdate2(td) {
          var _obj$__currentTargetD = obj.__currentTargetD = td, kdeVals2 = _obj$__currentTargetD.kdeVals, topAlt2 = _obj$__currentTargetD.topAlt, saturation2 = _obj$__currentTargetD.saturation;
          var maxVal = max(kdeVals2.map(Math.abs)) || 1e-15;
          var colorScale = quantize([0, maxVal / saturation2], colors);
          obj.geometry.setAttribute("color", array2BufferAttr(kdeVals2.map(function(v2) {
            return colorScale(Math.abs(v2));
          }), 4));
          var rScale = linear([0, maxVal], [GLOBE_RADIUS * (1 + baseAlt), GLOBE_RADIUS * (1 + (topAlt2 || baseAlt))]);
          obj.geometry.setAttribute("r", array2BufferAttr(kdeVals2.map(rScale)));
        };
        var targetD = {
          kdeVals,
          topAlt,
          saturation
        };
        var currentTargetD = obj.__currentTargetD || Object.assign({}, targetD, {
          kdeVals: kdeVals.map(function() {
            return 0;
          }),
          topAlt: !topAlt ? topAlt : baseAlt,
          saturation: 0.5
        });
        currentTargetD.kdeVals.length !== kdeVals.length && (currentTargetD.kdeVals = kdeVals.slice());
        if (Object.keys(targetD).some(function(k2) {
          return currentTargetD[k2] !== targetD[k2];
        })) {
          if (!state.heatmapsTransitionDuration || state.heatmapsTransitionDuration < 0) {
            applyUpdate(targetD);
          } else {
            state.tweenGroup.add(new Tween(currentTargetD).to(targetD, state.heatmapsTransitionDuration).easing(Easing.Quadratic.InOut).onUpdate(applyUpdate).start());
          }
        }
      });
    }).digest(state.heatmapsData);
  }
});
var THREE$b = window.THREE ? window.THREE : {
  DoubleSide,
  Group,
  LineBasicMaterial,
  LineSegments,
  Mesh,
  MeshBasicMaterial
};
var PolygonsLayerKapsule = index({
  props: {
    polygonsData: {
      "default": []
    },
    polygonGeoJsonGeometry: {
      "default": "geometry"
    },
    polygonSideColor: {
      "default": function _default11() {
        return "#ffffaa";
      }
    },
    polygonSideMaterial: {},
    polygonCapColor: {
      "default": function _default12() {
        return "#ffffaa";
      }
    },
    polygonCapMaterial: {},
    polygonStrokeColor: {},
    polygonAltitude: {
      "default": 0.01
    },
    // in units of globe radius
    polygonCapCurvatureResolution: {
      "default": 5
    },
    // in angular degrees
    polygonsTransitionDuration: {
      "default": 1e3,
      triggerUpdate: false
    }
    // ms
  },
  init: function init6(threeObj, state, _ref) {
    var tweenGroup = _ref.tweenGroup;
    emptyObject(threeObj);
    state.scene = threeObj;
    state.tweenGroup = tweenGroup;
    state.dataMapper = new ThreeDigest(threeObj, {
      objBindAttr: "__threeObjPolygon"
    }).id(function(d2) {
      return d2.id;
    }).onCreateObj(function() {
      var obj = new THREE$b.Group();
      obj.__defaultSideMaterial = applyShaderExtensionToMaterial(new THREE$b.MeshBasicMaterial({
        side: THREE$b.DoubleSide,
        depthWrite: true
      }), invisibleUndergroundShaderExtend);
      obj.__defaultCapMaterial = new THREE$b.MeshBasicMaterial({
        side: THREE$b.DoubleSide,
        depthWrite: true
      });
      obj.add(new THREE$b.Mesh(void 0, [
        obj.__defaultSideMaterial,
        // side material
        obj.__defaultCapMaterial
        // cap material
      ]));
      obj.add(new THREE$b.LineSegments(void 0, new THREE$b.LineBasicMaterial()));
      obj.__globeObjType = "polygon";
      return obj;
    });
  },
  update: function update6(state) {
    var geoJsonAccessor = index2(state.polygonGeoJsonGeometry);
    var altitudeAccessor = index2(state.polygonAltitude);
    var capCurvatureResolutionAccessor = index2(state.polygonCapCurvatureResolution);
    var capColorAccessor = index2(state.polygonCapColor);
    var capMaterialAccessor = index2(state.polygonCapMaterial);
    var sideColorAccessor = index2(state.polygonSideColor);
    var sideMaterialAccessor = index2(state.polygonSideMaterial);
    var strokeColorAccessor = index2(state.polygonStrokeColor);
    var singlePolygons = [];
    state.polygonsData.forEach(function(polygon) {
      var objAttrs = {
        data: polygon,
        capColor: capColorAccessor(polygon),
        capMaterial: capMaterialAccessor(polygon),
        sideColor: sideColorAccessor(polygon),
        sideMaterial: sideMaterialAccessor(polygon),
        strokeColor: strokeColorAccessor(polygon),
        altitude: +altitudeAccessor(polygon),
        capCurvatureResolution: +capCurvatureResolutionAccessor(polygon)
      };
      var geoJson = geoJsonAccessor(polygon);
      var geoId = polygon.__id || "".concat(Math.round(Math.random() * 1e9));
      polygon.__id = geoId;
      if (geoJson.type === "Polygon") {
        singlePolygons.push(_objectSpread2({
          id: "".concat(geoId, "_0"),
          coords: geoJson.coordinates
        }, objAttrs));
      } else if (geoJson.type === "MultiPolygon") {
        singlePolygons.push.apply(singlePolygons, _toConsumableArray3(geoJson.coordinates.map(function(coords, idx) {
          return _objectSpread2({
            id: "".concat(geoId, "_").concat(idx),
            coords
          }, objAttrs);
        })));
      } else {
        console.warn("Unsupported GeoJson geometry type: ".concat(geoJson.type, ". Skipping geometry..."));
      }
    });
    state.dataMapper.onUpdateObj(function(obj, _ref2) {
      var coords = _ref2.coords, capColor = _ref2.capColor, capMaterial = _ref2.capMaterial, sideColor = _ref2.sideColor, sideMaterial = _ref2.sideMaterial, strokeColor = _ref2.strokeColor, altitude = _ref2.altitude, capCurvatureResolution = _ref2.capCurvatureResolution;
      var _obj$children = _slicedToArray3(obj.children, 2), conicObj = _obj$children[0], strokeObj = _obj$children[1];
      var addStroke = !!strokeColor;
      strokeObj.visible = addStroke;
      var hasCap = !!(capColor || capMaterial);
      var hasSide = !!(sideColor || sideMaterial);
      if (!objMatch(conicObj.geometry.parameters || {}, {
        polygonGeoJson: coords,
        curvatureResolution: capCurvatureResolution,
        closedTop: hasCap,
        includeSides: hasSide
      })) {
        conicObj.geometry && conicObj.geometry.dispose();
        conicObj.geometry = new ConicPolygonGeometry(coords, 0, GLOBE_RADIUS, false, hasCap, hasSide, capCurvatureResolution);
      }
      if (addStroke && (!strokeObj.geometry.parameters || strokeObj.geometry.parameters.geoJson.coordinates !== coords || strokeObj.geometry.parameters.resolution !== capCurvatureResolution)) {
        strokeObj.geometry && strokeObj.geometry.dispose();
        strokeObj.geometry = new GeoJsonGeometry({
          type: "Polygon",
          coordinates: coords
        }, GLOBE_RADIUS, capCurvatureResolution);
      }
      var sideIdx = hasSide ? 0 : -1;
      var capIdx = !hasCap ? -1 : hasSide ? 1 : 0;
      sideIdx >= 0 && (conicObj.material[sideIdx] = sideMaterial || obj.__defaultSideMaterial);
      capIdx >= 0 && (conicObj.material[capIdx] = capMaterial || obj.__defaultCapMaterial);
      [[!sideMaterial && sideColor, sideIdx], [!capMaterial && capColor, capIdx]].forEach(function(_ref3) {
        var _ref4 = _slicedToArray3(_ref3, 2), color2 = _ref4[0], materialIdx = _ref4[1];
        if (!color2 || materialIdx < 0) return;
        var material2 = conicObj.material[materialIdx];
        var opacity2 = colorAlpha(color2);
        material2.color.set(colorStr2Hex(color2));
        material2.transparent = opacity2 < 1;
        material2.opacity = opacity2;
      });
      if (addStroke) {
        var material = strokeObj.material;
        var opacity = colorAlpha(strokeColor);
        material.color.set(colorStr2Hex(strokeColor));
        material.transparent = opacity < 1;
        material.opacity = opacity;
      }
      var targetD = {
        alt: altitude
      };
      var applyUpdate = function applyUpdate2(td) {
        var _obj$__currentTargetD = obj.__currentTargetD = td, alt = _obj$__currentTargetD.alt;
        conicObj.scale.x = conicObj.scale.y = conicObj.scale.z = 1 + alt;
        addStroke && (strokeObj.scale.x = strokeObj.scale.y = strokeObj.scale.z = 1 + alt + 1e-4);
        setExtendedMaterialUniforms(obj.__defaultSideMaterial, function(uniforms) {
          return uniforms.surfaceRadius.value = GLOBE_RADIUS / (alt + 1);
        });
      };
      var currentTargetD = obj.__currentTargetD || Object.assign({}, targetD, {
        alt: -1e-3
      });
      if (Object.keys(targetD).some(function(k2) {
        return currentTargetD[k2] !== targetD[k2];
      })) {
        if (!state.polygonsTransitionDuration || state.polygonsTransitionDuration < 0 || currentTargetD.alt === targetD.alt) {
          applyUpdate(targetD);
        } else {
          state.tweenGroup.add(new Tween(currentTargetD).to(targetD, state.polygonsTransitionDuration).easing(Easing.Quadratic.InOut).onUpdate(applyUpdate).start());
        }
      }
    }).digest(singlePolygons);
  }
});
function objMatch(obj, attrs) {
  var compFn = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : function() {
    return function(a2, b) {
      return a2 === b;
    };
  };
  return Object.entries(attrs).every(function(_ref5) {
    var _ref6 = _slicedToArray3(_ref5, 2), k2 = _ref6[0], v2 = _ref6[1];
    return obj.hasOwnProperty(k2) && compFn(k2)(obj[k2], v2);
  });
}
var THREE$a = window.THREE ? window.THREE : {
  BufferGeometry,
  DoubleSide,
  Mesh,
  MeshLambertMaterial,
  Vector3
};
var bfg = Object.assign({}, BufferGeometryUtils_exports);
var BufferGeometryUtils = bfg.BufferGeometryUtils || bfg;
var HexedPolygonsLayerKapsule = index({
  props: {
    hexPolygonsData: {
      "default": []
    },
    hexPolygonGeoJsonGeometry: {
      "default": "geometry"
    },
    hexPolygonColor: {
      "default": function _default13() {
        return "#ffffaa";
      }
    },
    hexPolygonAltitude: {
      "default": 1e-3
    },
    // in units of globe radius
    hexPolygonResolution: {
      "default": 3
    },
    // 0-15. Level 0 partitions the earth in 122 (mostly) hexagonal cells. Each subsequent level sub-divides the previous in roughly 7 hexagons.
    hexPolygonMargin: {
      "default": 0.2
    },
    // in fraction of hex diameter
    hexPolygonUseDots: {
      "default": false
    },
    // if points should be circular instead of hexagonal
    hexPolygonCurvatureResolution: {
      "default": 5
    },
    // in angular degrees, only relevant for hex tops
    hexPolygonDotResolution: {
      "default": 12
    },
    // how many slice segments in the dot circle's circumference
    hexPolygonsTransitionDuration: {
      "default": 0,
      triggerUpdate: false
    }
    // ms
  },
  init: function init7(threeObj, state, _ref) {
    var tweenGroup = _ref.tweenGroup;
    emptyObject(threeObj);
    state.scene = threeObj;
    state.tweenGroup = tweenGroup;
    state.dataMapper = new ThreeDigest(threeObj, {
      objBindAttr: "__threeObjHexPolygon"
    }).onCreateObj(function() {
      var obj = new THREE$a.Mesh(void 0, new THREE$a.MeshLambertMaterial({
        side: THREE$a.DoubleSide
      }));
      obj.__globeObjType = "hexPolygon";
      return obj;
    });
  },
  update: function update7(state) {
    var geoJsonAccessor = index2(state.hexPolygonGeoJsonGeometry);
    var colorAccessor = index2(state.hexPolygonColor);
    var altitudeAccessor = index2(state.hexPolygonAltitude);
    var resolutionAccessor = index2(state.hexPolygonResolution);
    var marginAccessor = index2(state.hexPolygonMargin);
    var useDotsAccessor = index2(state.hexPolygonUseDots);
    var curvatureResolutionAccessor = index2(state.hexPolygonCurvatureResolution);
    var dotResolutionAccessor = index2(state.hexPolygonDotResolution);
    state.dataMapper.onUpdateObj(function(obj, d2) {
      var geoJson = geoJsonAccessor(d2);
      var h3Res = resolutionAccessor(d2);
      var alt = altitudeAccessor(d2);
      var margin = Math.max(0, Math.min(1, +marginAccessor(d2)));
      var useDots = useDotsAccessor(d2);
      var curvatureResolution = curvatureResolutionAccessor(d2);
      var dotResolution = dotResolutionAccessor(d2);
      var color2 = colorAccessor(d2);
      var opacity = colorAlpha(color2);
      obj.material.color.set(colorStr2Hex(color2));
      obj.material.transparent = opacity < 1;
      obj.material.opacity = opacity;
      var targetD = {
        alt,
        margin,
        curvatureResolution
      };
      var memD = {
        geoJson,
        h3Res
      };
      var currentTargetD = obj.__currentTargetD || Object.assign({}, targetD, {
        alt: -1e-3
      });
      var currentMemD = obj.__currentMemD || memD;
      if (Object.keys(targetD).some(function(k2) {
        return currentTargetD[k2] !== targetD[k2];
      }) || Object.keys(memD).some(function(k2) {
        return currentMemD[k2] !== memD[k2];
      })) {
        obj.__currentMemD = memD;
        var h3Idxs = [];
        if (geoJson.type === "Polygon") {
          polygonToCells(geoJson.coordinates, h3Res, true).forEach(function(idx) {
            return h3Idxs.push(idx);
          });
        } else if (geoJson.type === "MultiPolygon") {
          geoJson.coordinates.forEach(function(coords) {
            return polygonToCells(coords, h3Res, true).forEach(function(idx) {
              return h3Idxs.push(idx);
            });
          });
        } else {
          console.warn("Unsupported GeoJson geometry type: ".concat(geoJson.type, ". Skipping geometry..."));
        }
        var hexBins = h3Idxs.map(function(h3Idx) {
          var hexCenter = cellToLatLng(h3Idx);
          var hexGeoJson = cellToBoundary(h3Idx, true).reverse();
          var centerLng = hexCenter[1];
          hexGeoJson.forEach(function(d3) {
            var edgeLng = d3[0];
            if (Math.abs(centerLng - edgeLng) > 170) {
              d3[0] += centerLng > edgeLng ? 360 : -360;
            }
          });
          return {
            h3Idx,
            hexCenter,
            hexGeoJson
          };
        });
        var applyUpdate = function applyUpdate2(td) {
          var _obj$__currentTargetD = obj.__currentTargetD = td, alt2 = _obj$__currentTargetD.alt, margin2 = _obj$__currentTargetD.margin, curvatureResolution2 = _obj$__currentTargetD.curvatureResolution;
          obj.geometry && obj.geometry.dispose();
          obj.geometry = !hexBins.length ? new THREE$a.BufferGeometry() : (BufferGeometryUtils.mergeGeometries || BufferGeometryUtils.mergeBufferGeometries)(hexBins.map(function(h2) {
            var _h$hexCenter = _slicedToArray3(h2.hexCenter, 2), clat = _h$hexCenter[0], clng = _h$hexCenter[1];
            if (useDots) {
              var centerPos = polar2Cartesian(clat, clng, alt2);
              var edgePos = polar2Cartesian(h2.hexGeoJson[0][1], h2.hexGeoJson[0][0], alt2);
              var r2 = 0.85 * (1 - margin2) * new THREE$a.Vector3(centerPos.x, centerPos.y, centerPos.z).distanceTo(new THREE$a.Vector3(edgePos.x, edgePos.y, edgePos.z));
              var geometry = new CircleGeometry(r2, dotResolution);
              geometry.rotateX(deg2Rad$1(-clat));
              geometry.rotateY(deg2Rad$1(clng));
              geometry.translate(centerPos.x, centerPos.y, centerPos.z);
              return geometry;
            } else {
              var relNum = function relNum2(st, end, rat) {
                return st - (st - end) * rat;
              };
              var _geoJson = margin2 === 0 ? h2.hexGeoJson : h2.hexGeoJson.map(function(_ref2) {
                var _ref3 = _slicedToArray3(_ref2, 2), elng = _ref3[0], elat = _ref3[1];
                return [[elng, clng], [elat, clat]].map(function(_ref4) {
                  var _ref5 = _slicedToArray3(_ref4, 2), st = _ref5[0], end = _ref5[1];
                  return relNum(st, end, margin2);
                });
              });
              return new ConicPolygonGeometry([_geoJson], GLOBE_RADIUS, GLOBE_RADIUS * (1 + alt2), false, true, false, curvatureResolution2);
            }
          }));
        };
        if (!state.hexPolygonsTransitionDuration || state.hexPolygonsTransitionDuration < 0) {
          applyUpdate(targetD);
        } else {
          state.tweenGroup.add(new Tween(currentTargetD).to(targetD, state.hexPolygonsTransitionDuration).easing(Easing.Quadratic.InOut).onUpdate(applyUpdate).start());
        }
      }
    }).digest(state.hexPolygonsData);
  }
});
var THREE$9 = window.THREE ? window.THREE : {
  Vector3
};
function interpolateVectors(fromPnts, toPnts) {
  var extendArr = function extendArr2(arr, length) {
    var repeatItem = arr[arr.length - 1];
    return [].concat(_toConsumableArray3(arr), _toConsumableArray3(Array(length - arr.length).fill(repeatItem)));
  };
  var arrLength = Math.max(fromPnts.length, toPnts.length);
  var interpolator = array_default.apply(void 0, _toConsumableArray3([fromPnts, toPnts].map(function(pnts) {
    return pnts.map(function(_ref) {
      var x2 = _ref.x, y2 = _ref.y, z2 = _ref.z;
      return [x2, y2, z2];
    });
  }).map(function(arr) {
    return extendArr(arr, arrLength);
  })));
  return function(k2) {
    return k2 === 0 ? fromPnts : k2 === 1 ? toPnts : interpolator(k2).map(function(_ref2) {
      var _ref3 = _slicedToArray3(_ref2, 3), x2 = _ref3[0], y2 = _ref3[1], z2 = _ref3[2];
      return new THREE$9.Vector3(x2, y2, z2);
    });
  };
}
var THREE$8 = window.THREE ? window.THREE : {
  BufferGeometry,
  Color,
  Group,
  Line,
  NormalBlending,
  ShaderMaterial,
  Vector3
};
var FrameTicker$1 = import_frame_ticker.default["default"] || import_frame_ticker.default;
var PathsLayerKapsule = index({
  props: {
    pathsData: {
      "default": []
    },
    pathPoints: {
      "default": function _default14(pnts) {
        return pnts;
      }
    },
    pathPointLat: {
      "default": function _default15(arr) {
        return arr[0];
      }
    },
    pathPointLng: {
      "default": function _default16(arr) {
        return arr[1];
      }
    },
    pathPointAlt: {
      "default": 1e-3
    },
    pathResolution: {
      "default": 2
    },
    // in deg
    pathColor: {
      "default": function _default17() {
        return "#ffffaa";
      }
    },
    // single color, array of colors or color interpolation fn
    pathStroke: {},
    // in deg
    pathDashLength: {
      "default": 1
    },
    // in units of line length
    pathDashGap: {
      "default": 0
    },
    pathDashInitialGap: {
      "default": 0
    },
    pathDashAnimateTime: {
      "default": 0
    },
    // ms
    pathTransitionDuration: {
      "default": 1e3,
      triggerUpdate: false
    },
    // ms
    rendererSize: {}
    // necessary to set correct fatline proportions
  },
  methods: {
    pauseAnimation: function pauseAnimation2(state) {
      var _state$ticker;
      (_state$ticker = state.ticker) === null || _state$ticker === void 0 || _state$ticker.pause();
    },
    resumeAnimation: function resumeAnimation2(state) {
      var _state$ticker2;
      (_state$ticker2 = state.ticker) === null || _state$ticker2 === void 0 || _state$ticker2.resume();
    },
    _destructor: function _destructor3(state) {
      var _state$ticker3;
      (_state$ticker3 = state.ticker) === null || _state$ticker3 === void 0 || _state$ticker3.dispose();
    }
  },
  stateInit: function stateInit3(_ref) {
    var tweenGroup = _ref.tweenGroup;
    return {
      tweenGroup,
      ticker: new FrameTicker$1(),
      sharedMaterial: new THREE$8.ShaderMaterial(_objectSpread2(_objectSpread2({}, dashedLineShaders()), {}, {
        transparent: true,
        blending: THREE$8.NormalBlending
      }))
    };
  },
  init: function init8(threeObj, state) {
    emptyObject(threeObj);
    state.scene = threeObj;
    state.dataMapper = new ThreeDigest(threeObj, {
      objBindAttr: "__threeObjPath"
    }).onCreateObj(function() {
      var obj = new THREE$8.Group();
      obj.__globeObjType = "path";
      return obj;
    });
    state.ticker.onTick.add(function(_2, timeDelta) {
      state.dataMapper.entries().map(function(_ref2) {
        var _ref3 = _slicedToArray3(_ref2, 2), obj = _ref3[1];
        return obj;
      }).filter(function(o2) {
        return o2.children.length && o2.children[0].material && o2.children[0].__dashAnimateStep;
      }).forEach(function(o2) {
        var obj = o2.children[0];
        var step = obj.__dashAnimateStep * timeDelta;
        if (obj.type === "Line") {
          var curTranslate = obj.material.uniforms.dashTranslate.value % 1e9;
          obj.material.uniforms.dashTranslate.value = curTranslate + step;
        } else if (obj.type === "Line2") {
          var offset = obj.material.dashOffset - step;
          var dashLength = obj.material.dashSize + obj.material.gapSize;
          while (offset <= -dashLength) offset += dashLength;
          obj.material.dashOffset = offset;
        }
      });
    });
  },
  update: function update8(state) {
    var pointsAccessor = index2(state.pathPoints);
    var pointLatAccessor = index2(state.pathPointLat);
    var pointLngAccessor = index2(state.pathPointLng);
    var pointAltAccessor = index2(state.pathPointAlt);
    var strokeAccessor = index2(state.pathStroke);
    var colorAccessor = index2(state.pathColor);
    var dashLengthAccessor = index2(state.pathDashLength);
    var dashGapAccessor = index2(state.pathDashGap);
    var dashInitialGapAccessor = index2(state.pathDashInitialGap);
    var dashAnimateTimeAccessor = index2(state.pathDashAnimateTime);
    state.dataMapper.onUpdateObj(function(group, path) {
      var stroke = strokeAccessor(path);
      var useFatLine = stroke !== null && stroke !== void 0;
      if (!group.children.length || useFatLine === (group.children[0].type === "Line")) {
        emptyObject(group);
        var _obj = useFatLine ? new Line2(new LineGeometry(), new LineMaterial()) : new THREE$8.Line(
          new THREE$8.BufferGeometry(),
          state.sharedMaterial.clone()
          // Separate material instance per object to have dedicated uniforms (but shared shaders)
        );
        group.add(_obj);
      }
      var obj = group.children[0];
      var points = calcPath(pointsAccessor(path), pointLatAccessor, pointLngAccessor, pointAltAccessor, state.pathResolution);
      var dashAnimateTime = dashAnimateTimeAccessor(path);
      obj.__dashAnimateStep = dashAnimateTime > 0 ? 1e3 / dashAnimateTime : 0;
      if (!useFatLine) {
        Object.assign(obj.material.uniforms, {
          dashSize: {
            value: dashLengthAccessor(path)
          },
          gapSize: {
            value: dashGapAccessor(path)
          },
          dashOffset: {
            value: dashInitialGapAccessor(path)
          }
        });
        var vertexColorArray = calcColorVertexArray(
          colorAccessor(path),
          // single, array of colors or interpolator
          points.length
          // numSegments
        );
        var vertexRelDistanceArray = calcVertexRelDistances(
          points.length,
          // numSegments
          1,
          // num vertices per segment
          true
          // run from end to start, to animate in the correct direction
        );
        obj.geometry.setAttribute("color", vertexColorArray);
        obj.geometry.setAttribute("relDistance", vertexRelDistanceArray);
      } else {
        obj.material.resolution = state.rendererSize;
        {
          var dashLength = dashLengthAccessor(path);
          var dashGap = dashGapAccessor(path);
          var dashInitialGap = dashInitialGapAccessor(path);
          obj.material.dashed = dashGap > 0;
          obj.material.dashed ? obj.material.defines.USE_DASH = "" : delete obj.material.defines.USE_DASH;
          if (obj.material.dashed) {
            obj.material.dashScale = 1 / calcLineDistance(points);
            obj.material.dashSize = dashLength;
            obj.material.gapSize = dashGap;
            obj.material.dashOffset = -dashInitialGap;
          }
        }
        {
          var colors = colorAccessor(path);
          if (colors instanceof Array) {
            var _vertexColorArray = calcColorVertexArray(
              colorAccessor(path),
              // single, array of colors or interpolator
              points.length - 1,
              // numSegments
              1,
              // num vertices per segment
              false
            );
            obj.geometry.setColors(_vertexColorArray.array);
            obj.material.vertexColors = true;
          } else {
            var color2 = colors;
            var opacity = colorAlpha(color2);
            obj.material.color = new THREE$8.Color(colorStr2Hex(color2));
            obj.material.transparent = opacity < 1;
            obj.material.opacity = opacity;
            obj.material.vertexColors = false;
          }
        }
        obj.material.needsUpdate = true;
      }
      var pointsInterpolator = interpolateVectors(group.__currentTargetD && group.__currentTargetD.points || [points[0]], points);
      var applyUpdate = function applyUpdate2(td) {
        var _group$__currentTarge = group.__currentTargetD = td, stroke2 = _group$__currentTarge.stroke, interpolK = _group$__currentTarge.interpolK;
        var kPoints = group.__currentTargetD.points = pointsInterpolator(interpolK);
        if (useFatLine) {
          var _ref4;
          obj.geometry.setPositions((_ref4 = []).concat.apply(_ref4, _toConsumableArray3(kPoints.map(function(_ref5) {
            var x2 = _ref5.x, y2 = _ref5.y, z2 = _ref5.z;
            return [x2, y2, z2];
          }))));
          obj.material.linewidth = stroke2;
          obj.material.dashed && obj.computeLineDistances();
        } else {
          obj.geometry.setFromPoints(kPoints);
          obj.geometry.computeBoundingSphere();
        }
      };
      var targetD = {
        stroke,
        interpolK: 1
      };
      var currentTargetD = Object.assign({}, group.__currentTargetD || targetD, {
        interpolK: 0
      });
      if (Object.keys(targetD).some(function(k2) {
        return currentTargetD[k2] !== targetD[k2];
      })) {
        if (!state.pathTransitionDuration || state.pathTransitionDuration < 0) {
          applyUpdate(targetD);
        } else {
          state.tweenGroup.add(new Tween(currentTargetD).to(targetD, state.pathTransitionDuration).easing(Easing.Quadratic.InOut).onUpdate(applyUpdate).start());
        }
      }
    }).digest(state.pathsData);
    function calcLineDistance(pnts) {
      var totalDist = 0;
      var prevPnt;
      pnts.forEach(function(pnt) {
        prevPnt && (totalDist += prevPnt.distanceTo(pnt));
        prevPnt = pnt;
      });
      return totalDist;
    }
    function calcPath(points, latAccessor, lngAccessor, altAccessor, angularResolution) {
      var getInterpolatedVals = function getInterpolatedVals2(start, end, numPnts) {
        var result = [];
        for (var i2 = 1; i2 <= numPnts; i2++) {
          result.push(start + (end - start) * i2 / (numPnts + 1));
        }
        return result;
      };
      var interpolateLine = function interpolateLine2() {
        var lineCoords = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];
        var maxDegDistance = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;
        var result = [];
        var prevPnt = null;
        lineCoords.forEach(function(pnt) {
          if (prevPnt) {
            while (Math.abs(prevPnt[1] - pnt[1]) > 180) prevPnt[1] += 360 * (prevPnt[1] < pnt[1] ? 1 : -1);
            var dist = Math.sqrt(Math.pow(pnt[0] - prevPnt[0], 2) + Math.pow(pnt[1] - prevPnt[1], 2));
            if (dist > maxDegDistance) {
              var numAdditionalPnts = Math.floor(dist / maxDegDistance);
              var lats = getInterpolatedVals(prevPnt[0], pnt[0], numAdditionalPnts);
              var lngs = getInterpolatedVals(prevPnt[1], pnt[1], numAdditionalPnts);
              var alts = getInterpolatedVals(prevPnt[2], pnt[2], numAdditionalPnts);
              for (var i2 = 0, len = lats.length; i2 < len; i2++) {
                result.push([lats[i2], lngs[i2], alts[i2]]);
              }
            }
          }
          result.push(prevPnt = pnt);
        });
        return result;
      };
      var getVec = function getVec2(_ref6) {
        var _ref7 = _slicedToArray3(_ref6, 3), lat = _ref7[0], lng = _ref7[1], alt = _ref7[2];
        var _polar2Cartesian = polar2Cartesian(lat, lng, alt), x2 = _polar2Cartesian.x, y2 = _polar2Cartesian.y, z2 = _polar2Cartesian.z;
        return new THREE$8.Vector3(x2, y2, z2);
      };
      return interpolateLine(points.map(function(pnt) {
        return [latAccessor(pnt), lngAccessor(pnt), altAccessor(pnt)];
      }), angularResolution).map(getVec);
    }
    function calcColorVertexArray(colors, numSegments) {
      var numVerticesPerSegment = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1;
      var includeAlpha = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : true;
      var numVerticesGroup = numSegments + 1;
      var getVertexColor;
      if (colors instanceof Array || colors instanceof Function) {
        var colorInterpolator = colors instanceof Array ? linear().domain(colors.map(function(_2, idx) {
          return idx / (colors.length - 1);
        })).range(colors) : colors;
        getVertexColor = function getVertexColor2(t2) {
          return color2ShaderArr(colorInterpolator(t2), includeAlpha, true);
        };
      } else {
        var vertexColor = color2ShaderArr(colors, includeAlpha, true);
        getVertexColor = function getVertexColor2() {
          return vertexColor;
        };
      }
      var vertexColors = [];
      for (var v2 = 0, l2 = numVerticesGroup; v2 < l2; v2++) {
        var _vertexColor = getVertexColor(v2 / (l2 - 1));
        for (var s2 = 0; s2 < numVerticesPerSegment; s2++) {
          vertexColors.push(_vertexColor);
        }
      }
      return array2BufferAttr(vertexColors, includeAlpha ? 4 : 3);
    }
    function calcVertexRelDistances(numSegments) {
      var numVerticesPerSegment = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;
      var invert = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;
      var numVerticesGroup = numSegments + 1;
      var vertexDistances = [];
      for (var v2 = 0, l2 = numVerticesGroup; v2 < l2; v2++) {
        var relDistance = v2 / (l2 - 1);
        for (var s2 = 0; s2 < numVerticesPerSegment; s2++) {
          vertexDistances.push(relDistance);
        }
      }
      invert && vertexDistances.reverse();
      return array2BufferAttr(vertexDistances, 1);
    }
  }
});
var THREE$7 = window.THREE ? window.THREE : {
  Euler,
  Mesh,
  MeshLambertMaterial,
  SphereGeometry
};
var TilesLayerKapsule = index({
  props: {
    tilesData: {
      "default": []
    },
    tileLat: {
      "default": "lat"
    },
    // tile centroid
    tileLng: {
      "default": "lng"
    },
    tileAltitude: {
      "default": 0.01
    },
    // in units of globe radius
    tileWidth: {
      "default": 1
    },
    // in lng degrees
    tileHeight: {
      "default": 1
    },
    // in lat degrees
    tileUseGlobeProjection: {
      "default": true
    },
    // whether to size tiles relative to the globe coordinate system, or independently
    tileMaterial: {
      "default": function _default18() {
        return new THREE$7.MeshLambertMaterial({
          color: "#ffbb88",
          opacity: 0.4,
          transparent: true
        });
      }
    },
    tileCurvatureResolution: {
      "default": 5
    },
    // in angular degrees
    tilesTransitionDuration: {
      "default": 1e3,
      triggerUpdate: false
    }
    // ms
  },
  init: function init9(threeObj, state, _ref) {
    var tweenGroup = _ref.tweenGroup;
    emptyObject(threeObj);
    state.scene = threeObj;
    state.tweenGroup = tweenGroup;
    state.dataMapper = new ThreeDigest(threeObj, {
      objBindAttr: "__threeObjTile"
    }).onCreateObj(function() {
      var obj = new THREE$7.Mesh();
      obj.__globeObjType = "tile";
      return obj;
    });
  },
  update: function update9(state) {
    var latAccessor = index2(state.tileLat);
    var lngAccessor = index2(state.tileLng);
    var altitudeAccessor = index2(state.tileAltitude);
    var widthAccessor = index2(state.tileWidth);
    var heightAccessor = index2(state.tileHeight);
    var useGlobeProjectionAccessor = index2(state.tileUseGlobeProjection);
    var materialAccessor = index2(state.tileMaterial);
    var curvatureResolutionAccessor = index2(state.tileCurvatureResolution);
    state.dataMapper.onUpdateObj(function(obj, d2) {
      obj.material = materialAccessor(d2);
      var useGlobeProjection = useGlobeProjectionAccessor(d2);
      var curvatureResolution = curvatureResolutionAccessor(d2);
      var applyPosition = function applyPosition2(td) {
        var _obj$__currentTargetD = obj.__currentTargetD = td, lat = _obj$__currentTargetD.lat, lng = _obj$__currentTargetD.lng, alt = _obj$__currentTargetD.alt, width = _obj$__currentTargetD.width, height = _obj$__currentTargetD.height;
        var rotLng = deg2Rad(lng);
        var rotLat = deg2Rad(-lat);
        obj.geometry && obj.geometry.dispose();
        obj.geometry = new THREE$7.SphereGeometry(GLOBE_RADIUS * (1 + alt), Math.ceil(width / (curvatureResolution || -1)), Math.ceil(height / (curvatureResolution || -1)), deg2Rad(90 - width / 2) + (useGlobeProjection ? rotLng : 0), deg2Rad(width), deg2Rad(90 - height / 2) + (useGlobeProjection ? rotLat : 0), deg2Rad(height));
        if (!useGlobeProjection) {
          obj.setRotationFromEuler(new THREE$7.Euler(rotLat, rotLng, 0, "YXZ"));
        }
      };
      var targetD = {
        lat: +latAccessor(d2),
        lng: +lngAccessor(d2),
        alt: +altitudeAccessor(d2),
        width: +widthAccessor(d2),
        height: +heightAccessor(d2)
      };
      var currentTargetD = obj.__currentTargetD || Object.assign({}, targetD, {
        width: 0,
        height: 0
      });
      if (Object.keys(targetD).some(function(k2) {
        return currentTargetD[k2] !== targetD[k2];
      })) {
        if (!state.tilesTransitionDuration || state.tilesTransitionDuration < 0) {
          applyPosition(targetD);
        } else {
          state.tweenGroup.add(new Tween(currentTargetD).to(targetD, state.tilesTransitionDuration).easing(Easing.Quadratic.InOut).onUpdate(applyPosition).start());
        }
      }
    }).digest(state.tilesData);
  }
});
var deg2Rad = function deg2Rad2(deg) {
  return deg * Math.PI / 180;
};
var THREE$6 = window.THREE ? window.THREE : {
  BufferGeometry,
  Color,
  Points,
  PointsMaterial
};
var ParticlesLayerKapsule = index({
  props: {
    particlesData: {
      "default": []
    },
    particlesList: {
      "default": function _default19(d2) {
        return d2;
      }
    },
    // arrays of arrays
    particleLat: {
      "default": "lat"
    },
    particleLng: {
      "default": "lng"
    },
    particleAltitude: {
      "default": 0.01
    },
    // in units of globe radius
    particlesSize: {
      "default": 0.5
    },
    particlesSizeAttenuation: {
      "default": true
    },
    particlesColor: {
      "default": function _default20() {
        return "white";
      }
    },
    particlesTexture: {}
  },
  init: function init10(threeObj, state) {
    emptyObject(threeObj);
    state.scene = threeObj;
    state.dataMapper = new ThreeDigest(threeObj, {
      objBindAttr: "__threeObjParticles"
    }).onCreateObj(function() {
      var obj = new THREE$6.Points(new THREE$6.BufferGeometry(), new THREE$6.PointsMaterial());
      obj.__globeObjType = "particles";
      return obj;
    }).onUpdateObj(function(obj, d2) {
      var particlesListAccessor = index2(state.particlesList);
      var latAccessor = index2(state.particleLat);
      var lngAccessor = index2(state.particleLng);
      var altitudeAccessor = index2(state.particleAltitude);
      obj.geometry.setAttribute("position", array2BufferAttr(particlesListAccessor(d2).map(function(p2) {
        return Object.values(polar2Cartesian(latAccessor(p2), lngAccessor(p2), altitudeAccessor(p2)));
      }), 3));
    });
  },
  update: function update10(state, changedProps) {
    if (["particlesData", "particlesList", "particleLat", "particleLng", "particleAltitude"].some(function(p2) {
      return changedProps.hasOwnProperty(p2);
    })) {
      state.dataMapper.digest(state.particlesData);
    }
    var colorAccessor = index2(state.particlesColor);
    var sizeAccessor = index2(state.particlesSize);
    var sizeAttenuationAccessor = index2(state.particlesSizeAttenuation);
    var textureAccessor = index2(state.particlesTexture);
    state.dataMapper.entries().forEach(function(_ref) {
      var _ref2 = _slicedToArray3(_ref, 2), d2 = _ref2[0], obj = _ref2[1];
      obj.material.size = sizeAccessor(d2);
      obj.material.sizeAttenuation = sizeAttenuationAccessor(d2);
      if (!state.particlesTexture) {
        var color2 = colorAccessor(d2);
        var opacity = colorAlpha(color2);
        obj.material.color = new THREE$6.Color(colorStr2Hex(color2));
        obj.material.transparent = opacity < 1;
        obj.material.opacity = opacity;
        obj.material.alphaTest = 0;
      } else {
        obj.material.color = new THREE$6.Color(16777215);
        obj.material.transparent = false;
        obj.material.alphaTest = 0.5;
        obj.material.map = textureAccessor(d2);
      }
    });
  }
});
var THREE$5 = window.THREE ? window.THREE : {
  BufferGeometry
};
var CircleLineGeometry = function(_THREE$BufferGeometry) {
  function CircleLineGeometry2() {
    var _this;
    var radius = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;
    var segmentCount = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 32;
    _classCallCheck(this, CircleLineGeometry2);
    _this = _callSuper(this, CircleLineGeometry2);
    _this.type = "CircleLineGeometry";
    _this.parameters = {
      radius,
      segmentCount
    };
    var points = [];
    for (var i2 = 0; i2 <= segmentCount; i2++) {
      var theta = (i2 / segmentCount - 0.25) * Math.PI * 2;
      points.push({
        x: Math.cos(theta) * radius,
        y: Math.sin(theta) * radius,
        z: 0
      });
    }
    _this.setFromPoints(points);
    return _this;
  }
  _inherits(CircleLineGeometry2, _THREE$BufferGeometry);
  return _createClass(CircleLineGeometry2);
}(THREE$5.BufferGeometry);
var THREE$4 = window.THREE ? window.THREE : {
  Color,
  Group,
  Line,
  LineBasicMaterial,
  Vector3
};
var FrameTicker = import_frame_ticker.default["default"] || import_frame_ticker.default;
var RingsLayerKapsule = index({
  props: {
    ringsData: {
      "default": []
    },
    ringLat: {
      "default": "lat"
    },
    ringLng: {
      "default": "lng"
    },
    ringAltitude: {
      "default": 15e-4
    },
    ringColor: {
      "default": function _default21() {
        return "#ffffaa";
      },
      triggerUpdate: false
    },
    // single color, array of colors or color interpolation fn
    ringResolution: {
      "default": 64,
      triggerUpdate: false
    },
    // how many slice segments in each circle's circumference
    ringMaxRadius: {
      "default": 2,
      triggerUpdate: false
    },
    // degrees
    ringPropagationSpeed: {
      "default": 1,
      triggerUpdate: false
    },
    // degrees/s
    ringRepeatPeriod: {
      "default": 700,
      triggerUpdate: false
    }
    // ms
  },
  methods: {
    pauseAnimation: function pauseAnimation3(state) {
      var _state$ticker;
      (_state$ticker = state.ticker) === null || _state$ticker === void 0 || _state$ticker.pause();
    },
    resumeAnimation: function resumeAnimation3(state) {
      var _state$ticker2;
      (_state$ticker2 = state.ticker) === null || _state$ticker2 === void 0 || _state$ticker2.resume();
    },
    _destructor: function _destructor4(state) {
      var _state$ticker3;
      (_state$ticker3 = state.ticker) === null || _state$ticker3 === void 0 || _state$ticker3.dispose();
    }
  },
  init: function init11(threeObj, state, _ref) {
    var tweenGroup = _ref.tweenGroup;
    emptyObject(threeObj);
    state.scene = threeObj;
    state.tweenGroup = tweenGroup;
    state.dataMapper = new ThreeDigest(threeObj, {
      objBindAttr: "__threeObjRing",
      removeDelay: 3e4
      // wait until all rings are gone
    }).onCreateObj(function() {
      var obj = new THREE$4.Group();
      obj.__globeObjType = "ring";
      return obj;
    });
    state.ticker = new FrameTicker();
    state.ticker.onTick.add(function(time) {
      if (!state.ringsData.length) return;
      var colorAccessor = index2(state.ringColor);
      var altitudeAccessor = index2(state.ringAltitude);
      var maxRadiusAccessor = index2(state.ringMaxRadius);
      var propagationSpeedAccessor = index2(state.ringPropagationSpeed);
      var repeatPeriodAccessor = index2(state.ringRepeatPeriod);
      state.dataMapper.entries().filter(function(_ref2) {
        var _ref3 = _slicedToArray3(_ref2, 2), o2 = _ref3[1];
        return o2;
      }).forEach(function(_ref4) {
        var _ref5 = _slicedToArray3(_ref4, 2), d2 = _ref5[0], obj = _ref5[1];
        if ((obj.__nextRingTime || 0) <= time) {
          var periodSecs = repeatPeriodAccessor(d2) / 1e3;
          obj.__nextRingTime = time + (periodSecs <= 0 ? Infinity : periodSecs);
          var circleObj = new THREE$4.Line(new CircleLineGeometry(1, state.ringResolution), new THREE$4.LineBasicMaterial());
          var colors = colorAccessor(d2);
          var isMultiColor = colors instanceof Array || colors instanceof Function;
          var colorInterpolator;
          if (!isMultiColor) {
            circleObj.material.color = new THREE$4.Color(colorStr2Hex(colors));
            setMaterialOpacity(circleObj.material, colorAlpha(colors));
          } else {
            if (colors instanceof Array) {
              colorInterpolator = linear().domain(colors.map(function(_2, idx) {
                return idx / (colors.length - 1);
              })).range(colors);
              circleObj.material.transparent = colors.some(function(c2) {
                return colorAlpha(c2) < 1;
              });
            } else {
              colorInterpolator = colors;
              circleObj.material.transparent = true;
            }
          }
          var curveR = GLOBE_RADIUS * (1 + altitudeAccessor(d2));
          var maxRadius = maxRadiusAccessor(d2);
          var maxAngle = maxRadius * Math.PI / 180;
          var propagationSpeed = propagationSpeedAccessor(d2);
          var isReverse = propagationSpeed <= 0;
          var updateFn = function updateFn2(_ref6) {
            var t2 = _ref6.t;
            var ang = (isReverse ? 1 - t2 : t2) * maxAngle;
            circleObj.scale.x = circleObj.scale.y = curveR * Math.sin(ang);
            circleObj.position.z = curveR * (1 - Math.cos(ang));
            if (isMultiColor) {
              var color2 = colorInterpolator(t2);
              circleObj.material.color = new THREE$4.Color(colorStr2Hex(color2));
              circleObj.material.transparent && (circleObj.material.opacity = colorAlpha(color2));
            }
          };
          if (propagationSpeed === 0) {
            updateFn({
              t: 0
            });
            obj.add(circleObj);
          } else {
            var transitionTime = Math.abs(maxRadius / propagationSpeed) * 1e3;
            state.tweenGroup.add(new Tween({
              t: 0
            }).to({
              t: 1
            }, transitionTime).onUpdate(updateFn).onStart(function() {
              return obj.add(circleObj);
            }).onComplete(function() {
              obj.remove(circleObj);
              _deallocate(circleObj);
            }).start());
          }
        }
      });
    });
  },
  update: function update11(state) {
    var latAccessor = index2(state.ringLat);
    var lngAccessor = index2(state.ringLng);
    var altitudeAccessor = index2(state.ringAltitude);
    var globeCenter = state.scene.localToWorld(new THREE$4.Vector3(0, 0, 0));
    state.dataMapper.onUpdateObj(function(obj, d2) {
      var lat = latAccessor(d2);
      var lng = lngAccessor(d2);
      var alt = altitudeAccessor(d2);
      Object.assign(obj.position, polar2Cartesian(lat, lng, alt));
      obj.lookAt(globeCenter);
    }).digest(state.ringsData);
  }
});
var glyphs = { "0": { x_min: 73, x_max: 715, ha: 792, o: "m 394 -29 q 153 129 242 -29 q 73 479 73 272 q 152 829 73 687 q 394 989 241 989 q 634 829 545 989 q 715 479 715 684 q 635 129 715 270 q 394 -29 546 -29 m 394 89 q 546 211 489 89 q 598 479 598 322 q 548 748 598 640 q 394 871 491 871 q 241 748 298 871 q 190 479 190 637 q 239 211 190 319 q 394 89 296 89 " }, "1": { x_min: 215.671875, x_max: 574, ha: 792, o: "m 574 0 l 442 0 l 442 697 l 215 697 l 215 796 q 386 833 330 796 q 475 986 447 875 l 574 986 l 574 0 " }, "2": { x_min: 59, x_max: 731, ha: 792, o: "m 731 0 l 59 0 q 197 314 59 188 q 457 487 199 315 q 598 691 598 580 q 543 819 598 772 q 411 867 488 867 q 272 811 328 867 q 209 630 209 747 l 81 630 q 182 901 81 805 q 408 986 271 986 q 629 909 536 986 q 731 694 731 826 q 613 449 731 541 q 378 316 495 383 q 201 122 235 234 l 731 122 l 731 0 " }, "3": { x_min: 54, x_max: 737, ha: 792, o: "m 737 284 q 635 55 737 141 q 399 -25 541 -25 q 156 52 248 -25 q 54 308 54 140 l 185 308 q 245 147 185 202 q 395 96 302 96 q 539 140 484 96 q 602 280 602 190 q 510 429 602 390 q 324 454 451 454 l 324 565 q 487 584 441 565 q 565 719 565 617 q 515 835 565 791 q 395 879 466 879 q 255 824 307 879 q 203 661 203 769 l 78 661 q 166 909 78 822 q 387 992 250 992 q 603 921 513 992 q 701 723 701 844 q 669 607 701 656 q 578 524 637 558 q 696 434 655 499 q 737 284 737 369 " }, "4": { x_min: 48, x_max: 742.453125, ha: 792, o: "m 742 243 l 602 243 l 602 0 l 476 0 l 476 243 l 48 243 l 48 368 l 476 958 l 602 958 l 602 354 l 742 354 l 742 243 m 476 354 l 476 792 l 162 354 l 476 354 " }, "5": { x_min: 54.171875, x_max: 738, ha: 792, o: "m 738 314 q 626 60 738 153 q 382 -23 526 -23 q 155 47 248 -23 q 54 256 54 125 l 183 256 q 259 132 204 174 q 382 91 314 91 q 533 149 471 91 q 602 314 602 213 q 538 469 602 411 q 386 528 475 528 q 284 506 332 528 q 197 439 237 484 l 81 439 l 159 958 l 684 958 l 684 840 l 254 840 l 214 579 q 306 627 258 612 q 407 643 354 643 q 636 552 540 643 q 738 314 738 457 " }, "6": { x_min: 53, x_max: 739, ha: 792, o: "m 739 312 q 633 62 739 162 q 400 -31 534 -31 q 162 78 257 -31 q 53 439 53 206 q 178 859 53 712 q 441 986 284 986 q 643 912 559 986 q 732 713 732 833 l 601 713 q 544 830 594 786 q 426 875 494 875 q 268 793 331 875 q 193 517 193 697 q 301 597 240 570 q 427 624 362 624 q 643 540 552 624 q 739 312 739 451 m 603 298 q 540 461 603 400 q 404 516 484 516 q 268 461 323 516 q 207 300 207 401 q 269 137 207 198 q 405 83 325 83 q 541 137 486 83 q 603 298 603 197 " }, "7": { x_min: 58.71875, x_max: 730.953125, ha: 792, o: "m 730 839 q 469 448 560 641 q 335 0 378 255 l 192 0 q 328 441 235 252 q 593 830 421 630 l 58 830 l 58 958 l 730 958 l 730 839 " }, "8": { x_min: 55, x_max: 736, ha: 792, o: "m 571 527 q 694 424 652 491 q 736 280 736 358 q 648 71 736 158 q 395 -26 551 -26 q 142 69 238 -26 q 55 279 55 157 q 96 425 55 359 q 220 527 138 491 q 120 615 153 562 q 88 726 88 668 q 171 904 88 827 q 395 986 261 986 q 618 905 529 986 q 702 727 702 830 q 670 616 702 667 q 571 527 638 565 m 394 565 q 519 610 475 565 q 563 717 563 655 q 521 823 563 781 q 392 872 474 872 q 265 824 312 872 q 224 720 224 783 q 265 613 224 656 q 394 565 312 565 m 395 91 q 545 150 488 91 q 597 280 597 204 q 546 408 597 355 q 395 465 492 465 q 244 408 299 465 q 194 280 194 356 q 244 150 194 203 q 395 91 299 91 " }, "9": { x_min: 53, x_max: 739, ha: 792, o: "m 739 524 q 619 94 739 241 q 362 -32 516 -32 q 150 47 242 -32 q 59 244 59 126 l 191 244 q 246 129 191 176 q 373 82 301 82 q 526 161 466 82 q 597 440 597 255 q 363 334 501 334 q 130 432 216 334 q 53 650 53 521 q 134 880 53 786 q 383 986 226 986 q 659 841 566 986 q 739 524 739 719 m 388 449 q 535 514 480 449 q 585 658 585 573 q 535 805 585 744 q 388 873 480 873 q 242 809 294 873 q 191 658 191 745 q 239 514 191 572 q 388 449 292 449 " }, "ο": { x_min: 0, x_max: 712, ha: 815, o: "m 356 -25 q 96 88 192 -25 q 0 368 0 201 q 92 642 0 533 q 356 761 192 761 q 617 644 517 761 q 712 368 712 533 q 619 91 712 201 q 356 -25 520 -25 m 356 85 q 527 175 465 85 q 583 369 583 255 q 528 562 583 484 q 356 651 466 651 q 189 560 250 651 q 135 369 135 481 q 187 177 135 257 q 356 85 250 85 " }, S: { x_min: 0, x_max: 788, ha: 890, o: "m 788 291 q 662 54 788 144 q 397 -26 550 -26 q 116 68 226 -26 q 0 337 0 168 l 131 337 q 200 152 131 220 q 384 85 269 85 q 557 129 479 85 q 650 270 650 183 q 490 429 650 379 q 194 513 341 470 q 33 739 33 584 q 142 964 33 881 q 388 1041 242 1041 q 644 957 543 1041 q 756 716 756 867 l 625 716 q 561 874 625 816 q 395 933 497 933 q 243 891 309 933 q 164 759 164 841 q 325 609 164 656 q 625 526 475 568 q 788 291 788 454 " }, "¦": { x_min: 343, x_max: 449, ha: 792, o: "m 449 462 l 343 462 l 343 986 l 449 986 l 449 462 m 449 -242 l 343 -242 l 343 280 l 449 280 l 449 -242 " }, "/": { x_min: 183.25, x_max: 608.328125, ha: 792, o: "m 608 1041 l 266 -129 l 183 -129 l 520 1041 l 608 1041 " }, "Τ": { x_min: -0.4375, x_max: 777.453125, ha: 839, o: "m 777 893 l 458 893 l 458 0 l 319 0 l 319 892 l 0 892 l 0 1013 l 777 1013 l 777 893 " }, y: { x_min: 0, x_max: 684.78125, ha: 771, o: "m 684 738 l 388 -83 q 311 -216 356 -167 q 173 -279 252 -279 q 97 -266 133 -279 l 97 -149 q 132 -155 109 -151 q 168 -160 155 -160 q 240 -114 213 -160 q 274 -26 248 -98 l 0 738 l 137 737 l 341 139 l 548 737 l 684 738 " }, "Π": { x_min: 0, x_max: 803, ha: 917, o: "m 803 0 l 667 0 l 667 886 l 140 886 l 140 0 l 0 0 l 0 1012 l 803 1012 l 803 0 " }, "ΐ": { x_min: -111, x_max: 339, ha: 361, o: "m 339 800 l 229 800 l 229 925 l 339 925 l 339 800 m -1 800 l -111 800 l -111 925 l -1 925 l -1 800 m 284 3 q 233 -10 258 -5 q 182 -15 207 -15 q 85 26 119 -15 q 42 200 42 79 l 42 737 l 167 737 l 168 215 q 172 141 168 157 q 226 101 183 101 q 248 103 239 101 q 284 112 257 104 l 284 3 m 302 1040 l 113 819 l 30 819 l 165 1040 l 302 1040 " }, g: { x_min: 0, x_max: 686, ha: 838, o: "m 686 34 q 586 -213 686 -121 q 331 -306 487 -306 q 131 -252 216 -306 q 31 -84 31 -190 l 155 -84 q 228 -174 166 -138 q 345 -207 284 -207 q 514 -109 454 -207 q 564 89 564 -27 q 461 6 521 36 q 335 -23 401 -23 q 88 100 184 -23 q 0 370 0 215 q 87 634 0 522 q 330 758 183 758 q 457 728 398 758 q 564 644 515 699 l 564 737 l 686 737 l 686 34 m 582 367 q 529 560 582 481 q 358 652 468 652 q 189 561 250 652 q 135 369 135 482 q 189 176 135 255 q 361 85 251 85 q 529 176 468 85 q 582 367 582 255 " }, "²": { x_min: 0, x_max: 442, ha: 539, o: "m 442 383 l 0 383 q 91 566 0 492 q 260 668 176 617 q 354 798 354 727 q 315 875 354 845 q 227 905 277 905 q 136 869 173 905 q 99 761 99 833 l 14 761 q 82 922 14 864 q 232 974 141 974 q 379 926 316 974 q 442 797 442 878 q 351 635 442 704 q 183 539 321 611 q 92 455 92 491 l 442 455 l 442 383 " }, "–": { x_min: 0, x_max: 705.5625, ha: 803, o: "m 705 334 l 0 334 l 0 410 l 705 410 l 705 334 " }, "Κ": { x_min: 0, x_max: 819.5625, ha: 893, o: "m 819 0 l 650 0 l 294 509 l 139 356 l 139 0 l 0 0 l 0 1013 l 139 1013 l 139 526 l 626 1013 l 809 1013 l 395 600 l 819 0 " }, "ƒ": { x_min: -46.265625, x_max: 392, ha: 513, o: "m 392 651 l 259 651 l 79 -279 l -46 -278 l 134 651 l 14 651 l 14 751 l 135 751 q 151 948 135 900 q 304 1041 185 1041 q 334 1040 319 1041 q 392 1034 348 1039 l 392 922 q 337 931 360 931 q 271 883 287 931 q 260 793 260 853 l 260 751 l 392 751 l 392 651 " }, e: { x_min: 0, x_max: 714, ha: 813, o: "m 714 326 l 140 326 q 200 157 140 227 q 359 87 260 87 q 488 130 431 87 q 561 245 545 174 l 697 245 q 577 48 670 123 q 358 -26 484 -26 q 97 85 195 -26 q 0 363 0 197 q 94 642 0 529 q 358 765 195 765 q 626 627 529 765 q 714 326 714 503 m 576 429 q 507 583 564 522 q 355 650 445 650 q 206 583 266 650 q 140 429 152 522 l 576 429 " }, "ό": { x_min: 0, x_max: 712, ha: 815, o: "m 356 -25 q 94 91 194 -25 q 0 368 0 202 q 92 642 0 533 q 356 761 192 761 q 617 644 517 761 q 712 368 712 533 q 619 91 712 201 q 356 -25 520 -25 m 356 85 q 527 175 465 85 q 583 369 583 255 q 528 562 583 484 q 356 651 466 651 q 189 560 250 651 q 135 369 135 481 q 187 177 135 257 q 356 85 250 85 m 576 1040 l 387 819 l 303 819 l 438 1040 l 576 1040 " }, J: { x_min: 0, x_max: 588, ha: 699, o: "m 588 279 q 287 -26 588 -26 q 58 73 126 -26 q 0 327 0 158 l 133 327 q 160 172 133 227 q 288 96 198 96 q 426 171 391 96 q 449 336 449 219 l 449 1013 l 588 1013 l 588 279 " }, "»": { x_min: -1, x_max: 503, ha: 601, o: "m 503 302 l 280 136 l 281 256 l 429 373 l 281 486 l 280 608 l 503 440 l 503 302 m 221 302 l 0 136 l 0 255 l 145 372 l 0 486 l -1 608 l 221 440 l 221 302 " }, "©": { x_min: -3, x_max: 1008, ha: 1106, o: "m 502 -7 q 123 151 263 -7 q -3 501 -3 294 q 123 851 -3 706 q 502 1011 263 1011 q 881 851 739 1011 q 1008 501 1008 708 q 883 151 1008 292 q 502 -7 744 -7 m 502 60 q 830 197 709 60 q 940 501 940 322 q 831 805 940 681 q 502 944 709 944 q 174 805 296 944 q 65 501 65 680 q 173 197 65 320 q 502 60 294 60 m 741 394 q 661 246 731 302 q 496 190 591 190 q 294 285 369 190 q 228 497 228 370 q 295 714 228 625 q 499 813 370 813 q 656 762 588 813 q 733 625 724 711 l 634 625 q 589 704 629 673 q 498 735 550 735 q 377 666 421 735 q 334 504 334 597 q 374 340 334 408 q 490 272 415 272 q 589 304 549 272 q 638 394 628 337 l 741 394 " }, "ώ": { x_min: 0, x_max: 922, ha: 1030, o: "m 687 1040 l 498 819 l 415 819 l 549 1040 l 687 1040 m 922 339 q 856 97 922 203 q 650 -26 780 -26 q 538 9 587 -26 q 461 103 489 44 q 387 12 436 46 q 277 -22 339 -22 q 69 97 147 -22 q 0 338 0 202 q 45 551 0 444 q 161 737 84 643 l 302 737 q 175 552 219 647 q 124 336 124 446 q 155 179 124 248 q 275 88 197 88 q 375 163 341 88 q 400 294 400 219 l 400 572 l 524 572 l 524 294 q 561 135 524 192 q 643 88 591 88 q 762 182 719 88 q 797 341 797 257 q 745 555 797 450 q 619 737 705 637 l 760 737 q 874 551 835 640 q 922 339 922 444 " }, "^": { x_min: 193.0625, x_max: 598.609375, ha: 792, o: "m 598 772 l 515 772 l 395 931 l 277 772 l 193 772 l 326 1013 l 462 1013 l 598 772 " }, "«": { x_min: 0, x_max: 507.203125, ha: 604, o: "m 506 136 l 284 302 l 284 440 l 506 608 l 507 485 l 360 371 l 506 255 l 506 136 m 222 136 l 0 302 l 0 440 l 222 608 l 221 486 l 73 373 l 222 256 l 222 136 " }, D: { x_min: 0, x_max: 828, ha: 935, o: "m 389 1013 q 714 867 593 1013 q 828 521 828 729 q 712 161 828 309 q 382 0 587 0 l 0 0 l 0 1013 l 389 1013 m 376 124 q 607 247 523 124 q 681 510 681 355 q 607 771 681 662 q 376 896 522 896 l 139 896 l 139 124 l 376 124 " }, "∙": { x_min: 0, x_max: 142, ha: 239, o: "m 142 585 l 0 585 l 0 738 l 142 738 l 142 585 " }, "ÿ": { x_min: 0, x_max: 47, ha: 125, o: "m 47 3 q 37 -7 47 -7 q 28 0 30 -7 q 39 -4 32 -4 q 45 3 45 -1 l 37 0 q 28 9 28 0 q 39 19 28 19 l 47 16 l 47 19 l 47 3 m 37 1 q 44 8 44 1 q 37 16 44 16 q 30 8 30 16 q 37 1 30 1 m 26 1 l 23 22 l 14 0 l 3 22 l 3 3 l 0 25 l 13 1 l 22 25 l 26 1 " }, w: { x_min: 0, x_max: 1009.71875, ha: 1100, o: "m 1009 738 l 783 0 l 658 0 l 501 567 l 345 0 l 222 0 l 0 738 l 130 738 l 284 174 l 432 737 l 576 738 l 721 173 l 881 737 l 1009 738 " }, $: { x_min: 0, x_max: 700, ha: 793, o: "m 664 717 l 542 717 q 490 825 531 785 q 381 872 450 865 l 381 551 q 620 446 540 522 q 700 241 700 370 q 618 45 700 116 q 381 -25 536 -25 l 381 -152 l 307 -152 l 307 -25 q 81 62 162 -25 q 0 297 0 149 l 124 297 q 169 146 124 204 q 307 81 215 89 l 307 441 q 80 536 148 469 q 13 725 13 603 q 96 910 13 839 q 307 982 180 982 l 307 1077 l 381 1077 l 381 982 q 574 917 494 982 q 664 717 664 845 m 307 565 l 307 872 q 187 831 233 872 q 142 724 142 791 q 180 618 142 656 q 307 565 218 580 m 381 76 q 562 237 562 96 q 517 361 562 313 q 381 423 472 409 l 381 76 " }, "\\": { x_min: -0.015625, x_max: 425.0625, ha: 522, o: "m 425 -129 l 337 -129 l 0 1041 l 83 1041 l 425 -129 " }, "µ": { x_min: 0, x_max: 697.21875, ha: 747, o: "m 697 -4 q 629 -14 658 -14 q 498 97 513 -14 q 422 9 470 41 q 313 -23 374 -23 q 207 4 258 -23 q 119 81 156 32 l 119 -278 l 0 -278 l 0 738 l 124 738 l 124 343 q 165 173 124 246 q 308 83 216 83 q 452 178 402 83 q 493 359 493 255 l 493 738 l 617 738 l 617 214 q 623 136 617 160 q 673 92 637 92 q 697 96 684 92 l 697 -4 " }, "Ι": { x_min: 42, x_max: 181, ha: 297, o: "m 181 0 l 42 0 l 42 1013 l 181 1013 l 181 0 " }, "Ύ": { x_min: 0, x_max: 1144.5, ha: 1214, o: "m 1144 1012 l 807 416 l 807 0 l 667 0 l 667 416 l 325 1012 l 465 1012 l 736 533 l 1004 1012 l 1144 1012 m 277 1040 l 83 799 l 0 799 l 140 1040 l 277 1040 " }, "’": { x_min: 0, x_max: 139, ha: 236, o: "m 139 851 q 102 737 139 784 q 0 669 65 690 l 0 734 q 59 787 42 741 q 72 873 72 821 l 0 873 l 0 1013 l 139 1013 l 139 851 " }, "Ν": { x_min: 0, x_max: 801, ha: 915, o: "m 801 0 l 651 0 l 131 822 l 131 0 l 0 0 l 0 1013 l 151 1013 l 670 191 l 670 1013 l 801 1013 l 801 0 " }, "-": { x_min: 8.71875, x_max: 350.390625, ha: 478, o: "m 350 317 l 8 317 l 8 428 l 350 428 l 350 317 " }, Q: { x_min: 0, x_max: 968, ha: 1072, o: "m 954 5 l 887 -79 l 744 35 q 622 -11 687 2 q 483 -26 556 -26 q 127 130 262 -26 q 0 504 0 279 q 127 880 0 728 q 484 1041 262 1041 q 841 884 708 1041 q 968 507 968 735 q 933 293 968 398 q 832 104 899 188 l 954 5 m 723 191 q 802 330 777 248 q 828 499 828 412 q 744 790 828 673 q 483 922 650 922 q 228 791 322 922 q 142 505 142 673 q 227 221 142 337 q 487 91 323 91 q 632 123 566 91 l 520 215 l 587 301 l 723 191 " }, "ς": { x_min: 1, x_max: 676.28125, ha: 740, o: "m 676 460 l 551 460 q 498 595 542 546 q 365 651 448 651 q 199 578 263 651 q 136 401 136 505 q 266 178 136 241 q 508 106 387 142 q 640 -50 640 62 q 625 -158 640 -105 q 583 -278 611 -211 l 465 -278 q 498 -182 490 -211 q 515 -80 515 -126 q 381 12 515 -15 q 134 91 197 51 q 1 388 1 179 q 100 651 1 542 q 354 761 199 761 q 587 680 498 761 q 676 460 676 599 " }, M: { x_min: 0, x_max: 954, ha: 1067, o: "m 954 0 l 819 0 l 819 869 l 537 0 l 405 0 l 128 866 l 128 0 l 0 0 l 0 1013 l 200 1013 l 472 160 l 757 1013 l 954 1013 l 954 0 " }, "Ψ": { x_min: 0, x_max: 1006, ha: 1094, o: "m 1006 678 q 914 319 1006 429 q 571 200 814 200 l 571 0 l 433 0 l 433 200 q 92 319 194 200 q 0 678 0 429 l 0 1013 l 139 1013 l 139 679 q 191 417 139 492 q 433 326 255 326 l 433 1013 l 571 1013 l 571 326 l 580 326 q 813 423 747 326 q 868 679 868 502 l 868 1013 l 1006 1013 l 1006 678 " }, C: { x_min: 0, x_max: 886, ha: 944, o: "m 886 379 q 760 87 886 201 q 455 -26 634 -26 q 112 136 236 -26 q 0 509 0 283 q 118 882 0 737 q 469 1041 245 1041 q 748 955 630 1041 q 879 708 879 859 l 745 708 q 649 862 724 805 q 473 920 573 920 q 219 791 312 920 q 136 509 136 675 q 217 229 136 344 q 470 99 311 99 q 672 179 591 99 q 753 379 753 259 l 886 379 " }, "!": { x_min: 0, x_max: 138, ha: 236, o: "m 138 684 q 116 409 138 629 q 105 244 105 299 l 33 244 q 16 465 33 313 q 0 684 0 616 l 0 1013 l 138 1013 l 138 684 m 138 0 l 0 0 l 0 151 l 138 151 l 138 0 " }, "{": { x_min: 0, x_max: 480.5625, ha: 578, o: "m 480 -286 q 237 -213 303 -286 q 187 -45 187 -159 q 194 48 187 -15 q 201 141 201 112 q 164 264 201 225 q 0 314 118 314 l 0 417 q 164 471 119 417 q 201 605 201 514 q 199 665 201 644 q 193 772 193 769 q 241 941 193 887 q 480 1015 308 1015 l 480 915 q 336 866 375 915 q 306 742 306 828 q 310 662 306 717 q 314 577 314 606 q 288 452 314 500 q 176 365 256 391 q 289 275 257 337 q 314 143 314 226 q 313 84 314 107 q 310 -11 310 -5 q 339 -131 310 -94 q 480 -182 377 -182 l 480 -286 " }, X: { x_min: -0.015625, x_max: 854.15625, ha: 940, o: "m 854 0 l 683 0 l 423 409 l 166 0 l 0 0 l 347 519 l 18 1013 l 186 1013 l 428 637 l 675 1013 l 836 1013 l 504 520 l 854 0 " }, "#": { x_min: 0, x_max: 963.890625, ha: 1061, o: "m 963 690 l 927 590 l 719 590 l 655 410 l 876 410 l 840 310 l 618 310 l 508 -3 l 393 -2 l 506 309 l 329 310 l 215 -2 l 102 -3 l 212 310 l 0 310 l 36 410 l 248 409 l 312 590 l 86 590 l 120 690 l 347 690 l 459 1006 l 573 1006 l 462 690 l 640 690 l 751 1006 l 865 1006 l 754 690 l 963 690 m 606 590 l 425 590 l 362 410 l 543 410 l 606 590 " }, "ι": { x_min: 42, x_max: 284, ha: 361, o: "m 284 3 q 233 -10 258 -5 q 182 -15 207 -15 q 85 26 119 -15 q 42 200 42 79 l 42 738 l 167 738 l 168 215 q 172 141 168 157 q 226 101 183 101 q 248 103 239 101 q 284 112 257 104 l 284 3 " }, "Ά": { x_min: 0, x_max: 906.953125, ha: 982, o: "m 283 1040 l 88 799 l 5 799 l 145 1040 l 283 1040 m 906 0 l 756 0 l 650 303 l 251 303 l 143 0 l 0 0 l 376 1012 l 529 1012 l 906 0 m 609 421 l 452 866 l 293 421 l 609 421 " }, ")": { x_min: 0, x_max: 318, ha: 415, o: "m 318 365 q 257 25 318 191 q 87 -290 197 -141 l 0 -290 q 140 21 93 -128 q 193 360 193 189 q 141 704 193 537 q 0 1024 97 850 l 87 1024 q 257 706 197 871 q 318 365 318 542 " }, "ε": { x_min: 0, x_max: 634.71875, ha: 714, o: "m 634 234 q 527 38 634 110 q 300 -25 433 -25 q 98 29 183 -25 q 0 204 0 93 q 37 314 0 265 q 128 390 67 353 q 56 460 82 419 q 26 555 26 505 q 114 712 26 654 q 295 763 191 763 q 499 700 416 763 q 589 515 589 631 l 478 515 q 419 618 464 580 q 307 657 374 657 q 207 630 253 657 q 151 547 151 598 q 238 445 151 469 q 389 434 280 434 l 389 331 l 349 331 q 206 315 255 331 q 125 210 125 287 q 183 107 125 145 q 302 76 233 76 q 436 117 379 76 q 509 234 493 159 l 634 234 " }, "Δ": { x_min: 0, x_max: 952.78125, ha: 1028, o: "m 952 0 l 0 0 l 400 1013 l 551 1013 l 952 0 m 762 124 l 476 867 l 187 124 l 762 124 " }, "}": { x_min: 0, x_max: 481, ha: 578, o: "m 481 314 q 318 262 364 314 q 282 136 282 222 q 284 65 282 97 q 293 -58 293 -48 q 241 -217 293 -166 q 0 -286 174 -286 l 0 -182 q 143 -130 105 -182 q 171 -2 171 -93 q 168 81 171 22 q 165 144 165 140 q 188 275 165 229 q 306 365 220 339 q 191 455 224 391 q 165 588 165 505 q 168 681 165 624 q 171 742 171 737 q 141 865 171 827 q 0 915 102 915 l 0 1015 q 243 942 176 1015 q 293 773 293 888 q 287 675 293 741 q 282 590 282 608 q 318 466 282 505 q 481 417 364 417 l 481 314 " }, "‰": { x_min: -3, x_max: 1672, ha: 1821, o: "m 846 0 q 664 76 732 0 q 603 244 603 145 q 662 412 603 344 q 846 489 729 489 q 1027 412 959 489 q 1089 244 1089 343 q 1029 76 1089 144 q 846 0 962 0 m 845 103 q 945 143 910 103 q 981 243 981 184 q 947 340 981 301 q 845 385 910 385 q 745 342 782 385 q 709 243 709 300 q 742 147 709 186 q 845 103 781 103 m 888 986 l 284 -25 l 199 -25 l 803 986 l 888 986 m 241 468 q 58 545 126 468 q -3 715 -3 615 q 56 881 -3 813 q 238 958 124 958 q 421 881 353 958 q 483 712 483 813 q 423 544 483 612 q 241 468 356 468 m 241 855 q 137 811 175 855 q 100 710 100 768 q 136 612 100 653 q 240 572 172 572 q 344 614 306 572 q 382 713 382 656 q 347 810 382 771 q 241 855 308 855 m 1428 0 q 1246 76 1314 0 q 1185 244 1185 145 q 1244 412 1185 344 q 1428 489 1311 489 q 1610 412 1542 489 q 1672 244 1672 343 q 1612 76 1672 144 q 1428 0 1545 0 m 1427 103 q 1528 143 1492 103 q 1564 243 1564 184 q 1530 340 1564 301 q 1427 385 1492 385 q 1327 342 1364 385 q 1291 243 1291 300 q 1324 147 1291 186 q 1427 103 1363 103 " }, a: { x_min: 0, x_max: 698.609375, ha: 794, o: "m 698 0 q 661 -12 679 -7 q 615 -17 643 -17 q 536 12 564 -17 q 500 96 508 41 q 384 6 456 37 q 236 -25 312 -25 q 65 31 130 -25 q 0 194 0 88 q 118 390 0 334 q 328 435 180 420 q 488 483 476 451 q 495 523 495 504 q 442 619 495 584 q 325 654 389 654 q 209 617 257 654 q 152 513 161 580 l 33 513 q 123 705 33 633 q 332 772 207 772 q 528 712 448 772 q 617 531 617 645 l 617 163 q 624 108 617 126 q 664 90 632 90 l 698 94 l 698 0 m 491 262 l 491 372 q 272 329 350 347 q 128 201 128 294 q 166 113 128 144 q 264 83 205 83 q 414 130 346 83 q 491 262 491 183 " }, "—": { x_min: 0, x_max: 941.671875, ha: 1039, o: "m 941 334 l 0 334 l 0 410 l 941 410 l 941 334 " }, "=": { x_min: 8.71875, x_max: 780.953125, ha: 792, o: "m 780 510 l 8 510 l 8 606 l 780 606 l 780 510 m 780 235 l 8 235 l 8 332 l 780 332 l 780 235 " }, N: { x_min: 0, x_max: 801, ha: 914, o: "m 801 0 l 651 0 l 131 823 l 131 0 l 0 0 l 0 1013 l 151 1013 l 670 193 l 670 1013 l 801 1013 l 801 0 " }, "ρ": { x_min: 0, x_max: 712, ha: 797, o: "m 712 369 q 620 94 712 207 q 362 -26 521 -26 q 230 2 292 -26 q 119 83 167 30 l 119 -278 l 0 -278 l 0 362 q 91 643 0 531 q 355 764 190 764 q 617 647 517 764 q 712 369 712 536 m 583 366 q 530 559 583 480 q 359 651 469 651 q 190 562 252 651 q 135 370 135 483 q 189 176 135 257 q 359 85 250 85 q 528 175 466 85 q 583 366 583 254 " }, "¯": { x_min: 0, x_max: 941.671875, ha: 938, o: "m 941 1033 l 0 1033 l 0 1109 l 941 1109 l 941 1033 " }, Z: { x_min: 0, x_max: 779, ha: 849, o: "m 779 0 l 0 0 l 0 113 l 621 896 l 40 896 l 40 1013 l 779 1013 l 778 887 l 171 124 l 779 124 l 779 0 " }, u: { x_min: 0, x_max: 617, ha: 729, o: "m 617 0 l 499 0 l 499 110 q 391 10 460 45 q 246 -25 322 -25 q 61 58 127 -25 q 0 258 0 136 l 0 738 l 125 738 l 125 284 q 156 148 125 202 q 273 82 197 82 q 433 165 369 82 q 493 340 493 243 l 493 738 l 617 738 l 617 0 " }, k: { x_min: 0, x_max: 612.484375, ha: 697, o: "m 612 738 l 338 465 l 608 0 l 469 0 l 251 382 l 121 251 l 121 0 l 0 0 l 0 1013 l 121 1013 l 121 402 l 456 738 l 612 738 " }, "Η": { x_min: 0, x_max: 803, ha: 917, o: "m 803 0 l 667 0 l 667 475 l 140 475 l 140 0 l 0 0 l 0 1013 l 140 1013 l 140 599 l 667 599 l 667 1013 l 803 1013 l 803 0 " }, "Α": { x_min: 0, x_max: 906.953125, ha: 985, o: "m 906 0 l 756 0 l 650 303 l 251 303 l 143 0 l 0 0 l 376 1013 l 529 1013 l 906 0 m 609 421 l 452 866 l 293 421 l 609 421 " }, s: { x_min: 0, x_max: 604, ha: 697, o: "m 604 217 q 501 36 604 104 q 292 -23 411 -23 q 86 43 166 -23 q 0 238 0 114 l 121 237 q 175 122 121 164 q 300 85 223 85 q 415 112 363 85 q 479 207 479 147 q 361 309 479 276 q 140 372 141 370 q 21 544 21 426 q 111 708 21 647 q 298 761 190 761 q 492 705 413 761 q 583 531 583 643 l 462 531 q 412 625 462 594 q 298 657 363 657 q 199 636 242 657 q 143 558 143 608 q 262 454 143 486 q 484 394 479 397 q 604 217 604 341 " }, B: { x_min: 0, x_max: 778, ha: 876, o: "m 580 546 q 724 469 670 535 q 778 311 778 403 q 673 83 778 171 q 432 0 575 0 l 0 0 l 0 1013 l 411 1013 q 629 957 541 1013 q 732 768 732 892 q 691 633 732 693 q 580 546 650 572 m 393 899 l 139 899 l 139 588 l 379 588 q 521 624 462 588 q 592 744 592 667 q 531 859 592 819 q 393 899 471 899 m 419 124 q 566 169 504 124 q 635 303 635 219 q 559 436 635 389 q 402 477 494 477 l 139 477 l 139 124 l 419 124 " }, "…": { x_min: 0, x_max: 614, ha: 708, o: "m 142 0 l 0 0 l 0 151 l 142 151 l 142 0 m 378 0 l 236 0 l 236 151 l 378 151 l 378 0 m 614 0 l 472 0 l 472 151 l 614 151 l 614 0 " }, "?": { x_min: 0, x_max: 607, ha: 704, o: "m 607 777 q 543 599 607 674 q 422 474 482 537 q 357 272 357 391 l 236 272 q 297 487 236 395 q 411 619 298 490 q 474 762 474 691 q 422 885 474 838 q 301 933 371 933 q 179 880 228 933 q 124 706 124 819 l 0 706 q 94 963 0 872 q 302 1044 177 1044 q 511 973 423 1044 q 607 777 607 895 m 370 0 l 230 0 l 230 151 l 370 151 l 370 0 " }, H: { x_min: 0, x_max: 803, ha: 915, o: "m 803 0 l 667 0 l 667 475 l 140 475 l 140 0 l 0 0 l 0 1013 l 140 1013 l 140 599 l 667 599 l 667 1013 l 803 1013 l 803 0 " }, "ν": { x_min: 0, x_max: 675, ha: 761, o: "m 675 738 l 404 0 l 272 0 l 0 738 l 133 738 l 340 147 l 541 738 l 675 738 " }, c: { x_min: 1, x_max: 701.390625, ha: 775, o: "m 701 264 q 584 53 681 133 q 353 -26 487 -26 q 91 91 188 -26 q 1 370 1 201 q 92 645 1 537 q 353 761 190 761 q 572 688 479 761 q 690 493 666 615 l 556 493 q 487 606 545 562 q 356 650 428 650 q 186 563 246 650 q 134 372 134 487 q 188 179 134 258 q 359 88 250 88 q 492 136 437 88 q 566 264 548 185 l 701 264 " }, "¶": { x_min: 0, x_max: 566.671875, ha: 678, o: "m 21 892 l 52 892 l 98 761 l 145 892 l 176 892 l 178 741 l 157 741 l 157 867 l 108 741 l 88 741 l 40 871 l 40 741 l 21 741 l 21 892 m 308 854 l 308 731 q 252 691 308 691 q 227 691 240 691 q 207 696 213 695 l 207 712 l 253 706 q 288 733 288 706 l 288 763 q 244 741 279 741 q 193 797 193 741 q 261 860 193 860 q 287 860 273 860 q 308 854 302 855 m 288 842 l 263 843 q 213 796 213 843 q 248 756 213 756 q 288 796 288 756 l 288 842 m 566 988 l 502 988 l 502 -1 l 439 -1 l 439 988 l 317 988 l 317 -1 l 252 -1 l 252 602 q 81 653 155 602 q 0 805 0 711 q 101 989 0 918 q 309 1053 194 1053 l 566 1053 l 566 988 " }, "β": { x_min: 0, x_max: 660, ha: 745, o: "m 471 550 q 610 450 561 522 q 660 280 660 378 q 578 64 660 151 q 367 -22 497 -22 q 239 5 299 -22 q 126 82 178 32 l 126 -278 l 0 -278 l 0 593 q 54 903 0 801 q 318 1042 127 1042 q 519 964 436 1042 q 603 771 603 887 q 567 644 603 701 q 471 550 532 586 m 337 79 q 476 138 418 79 q 535 279 535 198 q 427 437 535 386 q 226 477 344 477 l 226 583 q 398 620 329 583 q 486 762 486 668 q 435 884 486 833 q 312 935 384 935 q 169 861 219 935 q 126 698 126 797 l 126 362 q 170 169 126 242 q 337 79 224 79 " }, "Μ": { x_min: 0, x_max: 954, ha: 1068, o: "m 954 0 l 819 0 l 819 868 l 537 0 l 405 0 l 128 865 l 128 0 l 0 0 l 0 1013 l 199 1013 l 472 158 l 758 1013 l 954 1013 l 954 0 " }, "Ό": { x_min: 0.109375, x_max: 1120, ha: 1217, o: "m 1120 505 q 994 132 1120 282 q 642 -29 861 -29 q 290 130 422 -29 q 167 505 167 280 q 294 883 167 730 q 650 1046 430 1046 q 999 882 868 1046 q 1120 505 1120 730 m 977 504 q 896 784 977 669 q 644 915 804 915 q 391 785 484 915 q 307 504 307 669 q 391 224 307 339 q 644 95 486 95 q 894 224 803 95 q 977 504 977 339 m 277 1040 l 83 799 l 0 799 l 140 1040 l 277 1040 " }, "Ή": { x_min: 0, x_max: 1158, ha: 1275, o: "m 1158 0 l 1022 0 l 1022 475 l 496 475 l 496 0 l 356 0 l 356 1012 l 496 1012 l 496 599 l 1022 599 l 1022 1012 l 1158 1012 l 1158 0 m 277 1040 l 83 799 l 0 799 l 140 1040 l 277 1040 " }, "•": { x_min: 0, x_max: 663.890625, ha: 775, o: "m 663 529 q 566 293 663 391 q 331 196 469 196 q 97 294 194 196 q 0 529 0 393 q 96 763 0 665 q 331 861 193 861 q 566 763 469 861 q 663 529 663 665 " }, "¥": { x_min: 0.1875, x_max: 819.546875, ha: 886, o: "m 563 561 l 697 561 l 696 487 l 520 487 l 482 416 l 482 380 l 697 380 l 695 308 l 482 308 l 482 0 l 342 0 l 342 308 l 125 308 l 125 380 l 342 380 l 342 417 l 303 487 l 125 487 l 125 561 l 258 561 l 0 1013 l 140 1013 l 411 533 l 679 1013 l 819 1013 l 563 561 " }, "(": { x_min: 0, x_max: 318.0625, ha: 415, o: "m 318 -290 l 230 -290 q 61 23 122 -142 q 0 365 0 190 q 62 712 0 540 q 230 1024 119 869 l 318 1024 q 175 705 219 853 q 125 360 125 542 q 176 22 125 187 q 318 -290 223 -127 " }, U: { x_min: 0, x_max: 796, ha: 904, o: "m 796 393 q 681 93 796 212 q 386 -25 566 -25 q 101 95 208 -25 q 0 393 0 211 l 0 1013 l 138 1013 l 138 391 q 204 191 138 270 q 394 107 276 107 q 586 191 512 107 q 656 391 656 270 l 656 1013 l 796 1013 l 796 393 " }, "γ": { x_min: 0.5, x_max: 744.953125, ha: 822, o: "m 744 737 l 463 54 l 463 -278 l 338 -278 l 338 54 l 154 495 q 104 597 124 569 q 13 651 67 651 l 0 651 l 0 751 l 39 753 q 168 711 121 753 q 242 594 207 676 l 403 208 l 617 737 l 744 737 " }, "α": { x_min: 0, x_max: 765.5625, ha: 809, o: "m 765 -4 q 698 -14 726 -14 q 564 97 586 -14 q 466 7 525 40 q 337 -26 407 -26 q 88 98 186 -26 q 0 369 0 212 q 88 637 0 525 q 337 760 184 760 q 465 728 407 760 q 563 637 524 696 l 563 739 l 685 739 l 685 222 q 693 141 685 168 q 748 94 708 94 q 765 96 760 94 l 765 -4 m 584 371 q 531 562 584 485 q 360 653 470 653 q 192 566 254 653 q 135 379 135 489 q 186 181 135 261 q 358 84 247 84 q 528 176 465 84 q 584 371 584 260 " }, F: { x_min: 0, x_max: 683.328125, ha: 717, o: "m 683 888 l 140 888 l 140 583 l 613 583 l 613 458 l 140 458 l 140 0 l 0 0 l 0 1013 l 683 1013 l 683 888 " }, "­": { x_min: 0, x_max: 705.5625, ha: 803, o: "m 705 334 l 0 334 l 0 410 l 705 410 l 705 334 " }, ":": { x_min: 0, x_max: 142, ha: 239, o: "m 142 585 l 0 585 l 0 738 l 142 738 l 142 585 m 142 0 l 0 0 l 0 151 l 142 151 l 142 0 " }, "Χ": { x_min: 0, x_max: 854.171875, ha: 935, o: "m 854 0 l 683 0 l 423 409 l 166 0 l 0 0 l 347 519 l 18 1013 l 186 1013 l 427 637 l 675 1013 l 836 1013 l 504 521 l 854 0 " }, "*": { x_min: 116, x_max: 674, ha: 792, o: "m 674 768 l 475 713 l 610 544 l 517 477 l 394 652 l 272 478 l 178 544 l 314 713 l 116 766 l 153 876 l 341 812 l 342 1013 l 446 1013 l 446 811 l 635 874 l 674 768 " }, "†": { x_min: 0, x_max: 777, ha: 835, o: "m 458 804 l 777 804 l 777 683 l 458 683 l 458 0 l 319 0 l 319 681 l 0 683 l 0 804 l 319 804 l 319 1015 l 458 1013 l 458 804 " }, "°": { x_min: 0, x_max: 347, ha: 444, o: "m 173 802 q 43 856 91 802 q 0 977 0 905 q 45 1101 0 1049 q 173 1153 90 1153 q 303 1098 255 1153 q 347 977 347 1049 q 303 856 347 905 q 173 802 256 802 m 173 884 q 238 910 214 884 q 262 973 262 937 q 239 1038 262 1012 q 173 1064 217 1064 q 108 1037 132 1064 q 85 973 85 1010 q 108 910 85 937 q 173 884 132 884 " }, V: { x_min: 0, x_max: 862.71875, ha: 940, o: "m 862 1013 l 505 0 l 361 0 l 0 1013 l 143 1013 l 434 165 l 718 1012 l 862 1013 " }, "Ξ": { x_min: 0, x_max: 734.71875, ha: 763, o: "m 723 889 l 9 889 l 9 1013 l 723 1013 l 723 889 m 673 463 l 61 463 l 61 589 l 673 589 l 673 463 m 734 0 l 0 0 l 0 124 l 734 124 l 734 0 " }, " ": { x_min: 0, x_max: 0, ha: 853 }, "Ϋ": { x_min: 0.328125, x_max: 819.515625, ha: 889, o: "m 588 1046 l 460 1046 l 460 1189 l 588 1189 l 588 1046 m 360 1046 l 232 1046 l 232 1189 l 360 1189 l 360 1046 m 819 1012 l 482 416 l 482 0 l 342 0 l 342 416 l 0 1012 l 140 1012 l 411 533 l 679 1012 l 819 1012 " }, "”": { x_min: 0, x_max: 347, ha: 454, o: "m 139 851 q 102 737 139 784 q 0 669 65 690 l 0 734 q 59 787 42 741 q 72 873 72 821 l 0 873 l 0 1013 l 139 1013 l 139 851 m 347 851 q 310 737 347 784 q 208 669 273 690 l 208 734 q 267 787 250 741 q 280 873 280 821 l 208 873 l 208 1013 l 347 1013 l 347 851 " }, "@": { x_min: 0, x_max: 1260, ha: 1357, o: "m 1098 -45 q 877 -160 1001 -117 q 633 -203 752 -203 q 155 -29 327 -203 q 0 360 0 127 q 176 802 0 616 q 687 1008 372 1008 q 1123 854 969 1008 q 1260 517 1260 718 q 1155 216 1260 341 q 868 82 1044 82 q 772 106 801 82 q 737 202 737 135 q 647 113 700 144 q 527 82 594 82 q 367 147 420 82 q 314 312 314 212 q 401 565 314 452 q 639 690 498 690 q 810 588 760 690 l 849 668 l 938 668 q 877 441 900 532 q 833 226 833 268 q 853 182 833 198 q 902 167 873 167 q 1088 272 1012 167 q 1159 512 1159 372 q 1051 793 1159 681 q 687 925 925 925 q 248 747 415 925 q 97 361 97 586 q 226 26 97 159 q 627 -122 370 -122 q 856 -87 737 -122 q 1061 8 976 -53 l 1098 -45 m 786 488 q 738 580 777 545 q 643 615 700 615 q 483 517 548 615 q 425 322 425 430 q 457 203 425 250 q 552 156 490 156 q 722 273 665 156 q 786 488 738 309 " }, "Ί": { x_min: 0, x_max: 499, ha: 613, o: "m 277 1040 l 83 799 l 0 799 l 140 1040 l 277 1040 m 499 0 l 360 0 l 360 1012 l 499 1012 l 499 0 " }, i: { x_min: 14, x_max: 136, ha: 275, o: "m 136 873 l 14 873 l 14 1013 l 136 1013 l 136 873 m 136 0 l 14 0 l 14 737 l 136 737 l 136 0 " }, "Β": { x_min: 0, x_max: 778, ha: 877, o: "m 580 545 q 724 468 671 534 q 778 310 778 402 q 673 83 778 170 q 432 0 575 0 l 0 0 l 0 1013 l 411 1013 q 629 957 541 1013 q 732 768 732 891 q 691 632 732 692 q 580 545 650 571 m 393 899 l 139 899 l 139 587 l 379 587 q 521 623 462 587 q 592 744 592 666 q 531 859 592 819 q 393 899 471 899 m 419 124 q 566 169 504 124 q 635 302 635 219 q 559 435 635 388 q 402 476 494 476 l 139 476 l 139 124 l 419 124 " }, "υ": { x_min: 0, x_max: 617, ha: 725, o: "m 617 352 q 540 94 617 199 q 308 -24 455 -24 q 76 94 161 -24 q 0 352 0 199 l 0 739 l 126 739 l 126 355 q 169 185 126 257 q 312 98 220 98 q 451 185 402 98 q 492 355 492 257 l 492 739 l 617 739 l 617 352 " }, "]": { x_min: 0, x_max: 275, ha: 372, o: "m 275 -281 l 0 -281 l 0 -187 l 151 -187 l 151 920 l 0 920 l 0 1013 l 275 1013 l 275 -281 " }, m: { x_min: 0, x_max: 1019, ha: 1128, o: "m 1019 0 l 897 0 l 897 454 q 860 591 897 536 q 739 660 816 660 q 613 586 659 660 q 573 436 573 522 l 573 0 l 447 0 l 447 455 q 412 591 447 535 q 294 657 372 657 q 165 586 213 657 q 122 437 122 521 l 122 0 l 0 0 l 0 738 l 117 738 l 117 640 q 202 730 150 697 q 316 763 254 763 q 437 730 381 763 q 525 642 494 697 q 621 731 559 700 q 753 763 682 763 q 943 694 867 763 q 1019 512 1019 625 l 1019 0 " }, "χ": { x_min: 8.328125, x_max: 780.5625, ha: 815, o: "m 780 -278 q 715 -294 747 -294 q 616 -257 663 -294 q 548 -175 576 -227 l 379 133 l 143 -277 l 9 -277 l 313 254 l 163 522 q 127 586 131 580 q 36 640 91 640 q 8 637 27 640 l 8 752 l 52 757 q 162 719 113 757 q 236 627 200 690 l 383 372 l 594 737 l 726 737 l 448 250 l 625 -69 q 670 -153 647 -110 q 743 -188 695 -188 q 780 -184 759 -188 l 780 -278 " }, "ί": { x_min: 42, x_max: 326.71875, ha: 361, o: "m 284 3 q 233 -10 258 -5 q 182 -15 207 -15 q 85 26 119 -15 q 42 200 42 79 l 42 737 l 167 737 l 168 215 q 172 141 168 157 q 226 101 183 101 q 248 102 239 101 q 284 112 257 104 l 284 3 m 326 1040 l 137 819 l 54 819 l 189 1040 l 326 1040 " }, "Ζ": { x_min: 0, x_max: 779.171875, ha: 850, o: "m 779 0 l 0 0 l 0 113 l 620 896 l 40 896 l 40 1013 l 779 1013 l 779 887 l 170 124 l 779 124 l 779 0 " }, R: { x_min: 0, x_max: 781.953125, ha: 907, o: "m 781 0 l 623 0 q 587 242 590 52 q 407 433 585 433 l 138 433 l 138 0 l 0 0 l 0 1013 l 396 1013 q 636 946 539 1013 q 749 731 749 868 q 711 597 749 659 q 608 502 674 534 q 718 370 696 474 q 729 207 722 352 q 781 26 736 62 l 781 0 m 373 551 q 533 594 465 551 q 614 731 614 645 q 532 859 614 815 q 373 896 465 896 l 138 896 l 138 551 l 373 551 " }, o: { x_min: 0, x_max: 713, ha: 821, o: "m 357 -25 q 94 91 194 -25 q 0 368 0 202 q 93 642 0 533 q 357 761 193 761 q 618 644 518 761 q 713 368 713 533 q 619 91 713 201 q 357 -25 521 -25 m 357 85 q 528 175 465 85 q 584 369 584 255 q 529 562 584 484 q 357 651 467 651 q 189 560 250 651 q 135 369 135 481 q 187 177 135 257 q 357 85 250 85 " }, K: { x_min: 0, x_max: 819.46875, ha: 906, o: "m 819 0 l 649 0 l 294 509 l 139 355 l 139 0 l 0 0 l 0 1013 l 139 1013 l 139 526 l 626 1013 l 809 1013 l 395 600 l 819 0 " }, ",": { x_min: 0, x_max: 142, ha: 239, o: "m 142 -12 q 105 -132 142 -82 q 0 -205 68 -182 l 0 -138 q 57 -82 40 -124 q 70 0 70 -51 l 0 0 l 0 151 l 142 151 l 142 -12 " }, d: { x_min: 0, x_max: 683, ha: 796, o: "m 683 0 l 564 0 l 564 93 q 456 6 516 38 q 327 -25 395 -25 q 87 100 181 -25 q 0 365 0 215 q 90 639 0 525 q 343 763 187 763 q 564 647 486 763 l 564 1013 l 683 1013 l 683 0 m 582 373 q 529 562 582 484 q 361 653 468 653 q 190 561 253 653 q 135 365 135 479 q 189 175 135 254 q 358 85 251 85 q 529 178 468 85 q 582 373 582 258 " }, "¨": { x_min: -109, x_max: 247, ha: 232, o: "m 247 1046 l 119 1046 l 119 1189 l 247 1189 l 247 1046 m 19 1046 l -109 1046 l -109 1189 l 19 1189 l 19 1046 " }, E: { x_min: 0, x_max: 736.109375, ha: 789, o: "m 736 0 l 0 0 l 0 1013 l 725 1013 l 725 889 l 139 889 l 139 585 l 677 585 l 677 467 l 139 467 l 139 125 l 736 125 l 736 0 " }, Y: { x_min: 0, x_max: 820, ha: 886, o: "m 820 1013 l 482 416 l 482 0 l 342 0 l 342 416 l 0 1013 l 140 1013 l 411 534 l 679 1012 l 820 1013 " }, '"': { x_min: 0, x_max: 299, ha: 396, o: "m 299 606 l 203 606 l 203 988 l 299 988 l 299 606 m 96 606 l 0 606 l 0 988 l 96 988 l 96 606 " }, "‹": { x_min: 17.984375, x_max: 773.609375, ha: 792, o: "m 773 40 l 18 376 l 17 465 l 773 799 l 773 692 l 159 420 l 773 149 l 773 40 " }, "„": { x_min: 0, x_max: 364, ha: 467, o: "m 141 -12 q 104 -132 141 -82 q 0 -205 67 -182 l 0 -138 q 56 -82 40 -124 q 69 0 69 -51 l 0 0 l 0 151 l 141 151 l 141 -12 m 364 -12 q 327 -132 364 -82 q 222 -205 290 -182 l 222 -138 q 279 -82 262 -124 q 292 0 292 -51 l 222 0 l 222 151 l 364 151 l 364 -12 " }, "δ": { x_min: 1, x_max: 710, ha: 810, o: "m 710 360 q 616 87 710 196 q 356 -28 518 -28 q 99 82 197 -28 q 1 356 1 192 q 100 606 1 509 q 355 703 199 703 q 180 829 288 754 q 70 903 124 866 l 70 1012 l 643 1012 l 643 901 l 258 901 q 462 763 422 794 q 636 592 577 677 q 710 360 710 485 m 584 365 q 552 501 584 447 q 451 602 521 555 q 372 611 411 611 q 197 541 258 611 q 136 355 136 472 q 190 171 136 245 q 358 85 252 85 q 528 173 465 85 q 584 365 584 252 " }, "έ": { x_min: 0, x_max: 634.71875, ha: 714, o: "m 634 234 q 527 38 634 110 q 300 -25 433 -25 q 98 29 183 -25 q 0 204 0 93 q 37 313 0 265 q 128 390 67 352 q 56 459 82 419 q 26 555 26 505 q 114 712 26 654 q 295 763 191 763 q 499 700 416 763 q 589 515 589 631 l 478 515 q 419 618 464 580 q 307 657 374 657 q 207 630 253 657 q 151 547 151 598 q 238 445 151 469 q 389 434 280 434 l 389 331 l 349 331 q 206 315 255 331 q 125 210 125 287 q 183 107 125 145 q 302 76 233 76 q 436 117 379 76 q 509 234 493 159 l 634 234 m 520 1040 l 331 819 l 248 819 l 383 1040 l 520 1040 " }, "ω": { x_min: 0, x_max: 922, ha: 1031, o: "m 922 339 q 856 97 922 203 q 650 -26 780 -26 q 538 9 587 -26 q 461 103 489 44 q 387 12 436 46 q 277 -22 339 -22 q 69 97 147 -22 q 0 339 0 203 q 45 551 0 444 q 161 738 84 643 l 302 738 q 175 553 219 647 q 124 336 124 446 q 155 179 124 249 q 275 88 197 88 q 375 163 341 88 q 400 294 400 219 l 400 572 l 524 572 l 524 294 q 561 135 524 192 q 643 88 591 88 q 762 182 719 88 q 797 342 797 257 q 745 556 797 450 q 619 738 705 638 l 760 738 q 874 551 835 640 q 922 339 922 444 " }, "´": { x_min: 0, x_max: 96, ha: 251, o: "m 96 606 l 0 606 l 0 988 l 96 988 l 96 606 " }, "±": { x_min: 11, x_max: 781, ha: 792, o: "m 781 490 l 446 490 l 446 255 l 349 255 l 349 490 l 11 490 l 11 586 l 349 586 l 349 819 l 446 819 l 446 586 l 781 586 l 781 490 m 781 21 l 11 21 l 11 115 l 781 115 l 781 21 " }, "|": { x_min: 343, x_max: 449, ha: 792, o: "m 449 462 l 343 462 l 343 986 l 449 986 l 449 462 m 449 -242 l 343 -242 l 343 280 l 449 280 l 449 -242 " }, "ϋ": { x_min: 0, x_max: 617, ha: 725, o: "m 482 800 l 372 800 l 372 925 l 482 925 l 482 800 m 239 800 l 129 800 l 129 925 l 239 925 l 239 800 m 617 352 q 540 93 617 199 q 308 -24 455 -24 q 76 93 161 -24 q 0 352 0 199 l 0 738 l 126 738 l 126 354 q 169 185 126 257 q 312 98 220 98 q 451 185 402 98 q 492 354 492 257 l 492 738 l 617 738 l 617 352 " }, "§": { x_min: 0, x_max: 593, ha: 690, o: "m 593 425 q 554 312 593 369 q 467 233 516 254 q 537 83 537 172 q 459 -74 537 -12 q 288 -133 387 -133 q 115 -69 184 -133 q 47 96 47 -6 l 166 96 q 199 7 166 40 q 288 -26 232 -26 q 371 -5 332 -26 q 420 60 420 21 q 311 201 420 139 q 108 309 210 255 q 0 490 0 383 q 33 602 0 551 q 124 687 66 654 q 75 743 93 712 q 58 812 58 773 q 133 984 58 920 q 300 1043 201 1043 q 458 987 394 1043 q 529 814 529 925 l 411 814 q 370 908 404 877 q 289 939 336 939 q 213 911 246 939 q 180 841 180 883 q 286 720 180 779 q 484 612 480 615 q 593 425 593 534 m 467 409 q 355 544 467 473 q 196 630 228 612 q 146 587 162 609 q 124 525 124 558 q 239 387 124 462 q 398 298 369 315 q 448 345 429 316 q 467 409 467 375 " }, b: { x_min: 0, x_max: 685, ha: 783, o: "m 685 372 q 597 99 685 213 q 347 -25 501 -25 q 219 5 277 -25 q 121 93 161 36 l 121 0 l 0 0 l 0 1013 l 121 1013 l 121 634 q 214 723 157 692 q 341 754 272 754 q 591 637 493 754 q 685 372 685 526 m 554 356 q 499 550 554 470 q 328 644 437 644 q 162 556 223 644 q 108 369 108 478 q 160 176 108 256 q 330 83 221 83 q 498 169 435 83 q 554 356 554 245 " }, q: { x_min: 0, x_max: 683, ha: 876, o: "m 683 -278 l 564 -278 l 564 97 q 474 8 533 39 q 345 -23 415 -23 q 91 93 188 -23 q 0 364 0 203 q 87 635 0 522 q 337 760 184 760 q 466 727 408 760 q 564 637 523 695 l 564 737 l 683 737 l 683 -278 m 582 375 q 527 564 582 488 q 358 652 466 652 q 190 565 253 652 q 135 377 135 488 q 189 179 135 261 q 361 84 251 84 q 530 179 469 84 q 582 375 582 260 " }, "Ω": { x_min: -0.171875, x_max: 969.5625, ha: 1068, o: "m 969 0 l 555 0 l 555 123 q 744 308 675 194 q 814 558 814 423 q 726 812 814 709 q 484 922 633 922 q 244 820 334 922 q 154 567 154 719 q 223 316 154 433 q 412 123 292 199 l 412 0 l 0 0 l 0 124 l 217 124 q 68 327 122 210 q 15 572 15 444 q 144 911 15 781 q 484 1041 274 1041 q 822 909 691 1041 q 953 569 953 777 q 899 326 953 443 q 750 124 846 210 l 969 124 l 969 0 " }, "ύ": { x_min: 0, x_max: 617, ha: 725, o: "m 617 352 q 540 93 617 199 q 308 -24 455 -24 q 76 93 161 -24 q 0 352 0 199 l 0 738 l 126 738 l 126 354 q 169 185 126 257 q 312 98 220 98 q 451 185 402 98 q 492 354 492 257 l 492 738 l 617 738 l 617 352 m 535 1040 l 346 819 l 262 819 l 397 1040 l 535 1040 " }, z: { x_min: -0.015625, x_max: 613.890625, ha: 697, o: "m 613 0 l 0 0 l 0 100 l 433 630 l 20 630 l 20 738 l 594 738 l 593 636 l 163 110 l 613 110 l 613 0 " }, "™": { x_min: 0, x_max: 894, ha: 1e3, o: "m 389 951 l 229 951 l 229 503 l 160 503 l 160 951 l 0 951 l 0 1011 l 389 1011 l 389 951 m 894 503 l 827 503 l 827 939 l 685 503 l 620 503 l 481 937 l 481 503 l 417 503 l 417 1011 l 517 1011 l 653 580 l 796 1010 l 894 1011 l 894 503 " }, "ή": { x_min: 0.78125, x_max: 697, ha: 810, o: "m 697 -278 l 572 -278 l 572 454 q 540 587 572 536 q 425 650 501 650 q 271 579 337 650 q 206 420 206 509 l 206 0 l 81 0 l 81 489 q 73 588 81 562 q 0 644 56 644 l 0 741 q 68 755 38 755 q 158 721 124 755 q 200 630 193 687 q 297 726 234 692 q 434 761 359 761 q 620 692 544 761 q 697 516 697 624 l 697 -278 m 479 1040 l 290 819 l 207 819 l 341 1040 l 479 1040 " }, "Θ": { x_min: 0, x_max: 960, ha: 1056, o: "m 960 507 q 833 129 960 280 q 476 -32 698 -32 q 123 129 255 -32 q 0 507 0 280 q 123 883 0 732 q 476 1045 255 1045 q 832 883 696 1045 q 960 507 960 732 m 817 500 q 733 789 817 669 q 476 924 639 924 q 223 792 317 924 q 142 507 142 675 q 222 222 142 339 q 476 89 315 89 q 730 218 636 89 q 817 500 817 334 m 716 449 l 243 449 l 243 571 l 716 571 l 716 449 " }, "®": { x_min: -3, x_max: 1008, ha: 1106, o: "m 503 532 q 614 562 566 532 q 672 658 672 598 q 614 747 672 716 q 503 772 569 772 l 338 772 l 338 532 l 503 532 m 502 -7 q 123 151 263 -7 q -3 501 -3 294 q 123 851 -3 706 q 502 1011 263 1011 q 881 851 739 1011 q 1008 501 1008 708 q 883 151 1008 292 q 502 -7 744 -7 m 502 60 q 830 197 709 60 q 940 501 940 322 q 831 805 940 681 q 502 944 709 944 q 174 805 296 944 q 65 501 65 680 q 173 197 65 320 q 502 60 294 60 m 788 146 l 678 146 q 653 316 655 183 q 527 449 652 449 l 338 449 l 338 146 l 241 146 l 241 854 l 518 854 q 688 808 621 854 q 766 658 766 755 q 739 563 766 607 q 668 497 713 519 q 751 331 747 472 q 788 164 756 190 l 788 146 " }, "~": { x_min: 0, x_max: 833, ha: 931, o: "m 833 958 q 778 753 833 831 q 594 665 716 665 q 402 761 502 665 q 240 857 302 857 q 131 795 166 857 q 104 665 104 745 l 0 665 q 54 867 0 789 q 237 958 116 958 q 429 861 331 958 q 594 765 527 765 q 704 827 670 765 q 729 958 729 874 l 833 958 " }, "Ε": { x_min: 0, x_max: 736.21875, ha: 778, o: "m 736 0 l 0 0 l 0 1013 l 725 1013 l 725 889 l 139 889 l 139 585 l 677 585 l 677 467 l 139 467 l 139 125 l 736 125 l 736 0 " }, "³": { x_min: 0, x_max: 450, ha: 547, o: "m 450 552 q 379 413 450 464 q 220 366 313 366 q 69 414 130 366 q 0 567 0 470 l 85 567 q 126 470 85 504 q 225 437 168 437 q 320 467 280 437 q 360 552 360 498 q 318 632 360 608 q 213 657 276 657 q 195 657 203 657 q 176 657 181 657 l 176 722 q 279 733 249 722 q 334 815 334 752 q 300 881 334 856 q 220 907 267 907 q 133 875 169 907 q 97 781 97 844 l 15 781 q 78 926 15 875 q 220 972 135 972 q 364 930 303 972 q 426 817 426 888 q 344 697 426 733 q 421 642 392 681 q 450 552 450 603 " }, "[": { x_min: 0, x_max: 273.609375, ha: 371, o: "m 273 -281 l 0 -281 l 0 1013 l 273 1013 l 273 920 l 124 920 l 124 -187 l 273 -187 l 273 -281 " }, L: { x_min: 0, x_max: 645.828125, ha: 696, o: "m 645 0 l 0 0 l 0 1013 l 140 1013 l 140 126 l 645 126 l 645 0 " }, "σ": { x_min: 0, x_max: 803.390625, ha: 894, o: "m 803 628 l 633 628 q 713 368 713 512 q 618 93 713 204 q 357 -25 518 -25 q 94 91 194 -25 q 0 368 0 201 q 94 644 0 533 q 356 761 194 761 q 481 750 398 761 q 608 739 564 739 l 803 739 l 803 628 m 360 85 q 529 180 467 85 q 584 374 584 262 q 527 566 584 490 q 352 651 463 651 q 187 559 247 651 q 135 368 135 478 q 189 175 135 254 q 360 85 251 85 " }, "ζ": { x_min: 0, x_max: 573, ha: 642, o: "m 573 -40 q 553 -162 573 -97 q 510 -278 543 -193 l 400 -278 q 441 -187 428 -219 q 462 -90 462 -132 q 378 -14 462 -14 q 108 45 197 -14 q 0 290 0 117 q 108 631 0 462 q 353 901 194 767 l 55 901 l 55 1012 l 561 1012 l 561 924 q 261 669 382 831 q 128 301 128 489 q 243 117 128 149 q 458 98 350 108 q 573 -40 573 80 " }, "θ": { x_min: 0, x_max: 674, ha: 778, o: "m 674 496 q 601 160 674 304 q 336 -26 508 -26 q 73 153 165 -26 q 0 485 0 296 q 72 840 0 683 q 343 1045 166 1045 q 605 844 516 1045 q 674 496 674 692 m 546 579 q 498 798 546 691 q 336 935 437 935 q 178 798 237 935 q 126 579 137 701 l 546 579 m 546 475 l 126 475 q 170 233 126 348 q 338 80 230 80 q 504 233 447 80 q 546 475 546 346 " }, "Ο": { x_min: 0, x_max: 958, ha: 1054, o: "m 485 1042 q 834 883 703 1042 q 958 511 958 735 q 834 136 958 287 q 481 -26 701 -26 q 126 130 261 -26 q 0 504 0 279 q 127 880 0 729 q 485 1042 263 1042 m 480 98 q 731 225 638 98 q 815 504 815 340 q 733 783 815 670 q 480 913 640 913 q 226 785 321 913 q 142 504 142 671 q 226 224 142 339 q 480 98 319 98 " }, "Γ": { x_min: 0, x_max: 705.28125, ha: 749, o: "m 705 886 l 140 886 l 140 0 l 0 0 l 0 1012 l 705 1012 l 705 886 " }, " ": { x_min: 0, x_max: 0, ha: 375 }, "%": { x_min: -3, x_max: 1089, ha: 1186, o: "m 845 0 q 663 76 731 0 q 602 244 602 145 q 661 412 602 344 q 845 489 728 489 q 1027 412 959 489 q 1089 244 1089 343 q 1029 76 1089 144 q 845 0 962 0 m 844 103 q 945 143 909 103 q 981 243 981 184 q 947 340 981 301 q 844 385 909 385 q 744 342 781 385 q 708 243 708 300 q 741 147 708 186 q 844 103 780 103 m 888 986 l 284 -25 l 199 -25 l 803 986 l 888 986 m 241 468 q 58 545 126 468 q -3 715 -3 615 q 56 881 -3 813 q 238 958 124 958 q 421 881 353 958 q 483 712 483 813 q 423 544 483 612 q 241 468 356 468 m 241 855 q 137 811 175 855 q 100 710 100 768 q 136 612 100 653 q 240 572 172 572 q 344 614 306 572 q 382 713 382 656 q 347 810 382 771 q 241 855 308 855 " }, P: { x_min: 0, x_max: 726, ha: 806, o: "m 424 1013 q 640 931 555 1013 q 726 719 726 850 q 637 506 726 587 q 413 426 548 426 l 140 426 l 140 0 l 0 0 l 0 1013 l 424 1013 m 379 889 l 140 889 l 140 548 l 372 548 q 522 589 459 548 q 593 720 593 637 q 528 845 593 801 q 379 889 463 889 " }, "Έ": { x_min: 0, x_max: 1078.21875, ha: 1118, o: "m 1078 0 l 342 0 l 342 1013 l 1067 1013 l 1067 889 l 481 889 l 481 585 l 1019 585 l 1019 467 l 481 467 l 481 125 l 1078 125 l 1078 0 m 277 1040 l 83 799 l 0 799 l 140 1040 l 277 1040 " }, "Ώ": { x_min: 0.125, x_max: 1136.546875, ha: 1235, o: "m 1136 0 l 722 0 l 722 123 q 911 309 842 194 q 981 558 981 423 q 893 813 981 710 q 651 923 800 923 q 411 821 501 923 q 321 568 321 720 q 390 316 321 433 q 579 123 459 200 l 579 0 l 166 0 l 166 124 l 384 124 q 235 327 289 210 q 182 572 182 444 q 311 912 182 782 q 651 1042 441 1042 q 989 910 858 1042 q 1120 569 1120 778 q 1066 326 1120 443 q 917 124 1013 210 l 1136 124 l 1136 0 m 277 1040 l 83 800 l 0 800 l 140 1041 l 277 1040 " }, _: { x_min: 0, x_max: 705.5625, ha: 803, o: "m 705 -334 l 0 -334 l 0 -234 l 705 -234 l 705 -334 " }, "Ϊ": { x_min: -110, x_max: 246, ha: 275, o: "m 246 1046 l 118 1046 l 118 1189 l 246 1189 l 246 1046 m 18 1046 l -110 1046 l -110 1189 l 18 1189 l 18 1046 m 136 0 l 0 0 l 0 1012 l 136 1012 l 136 0 " }, "+": { x_min: 23, x_max: 768, ha: 792, o: "m 768 372 l 444 372 l 444 0 l 347 0 l 347 372 l 23 372 l 23 468 l 347 468 l 347 840 l 444 840 l 444 468 l 768 468 l 768 372 " }, "½": { x_min: 0, x_max: 1050, ha: 1149, o: "m 1050 0 l 625 0 q 712 178 625 108 q 878 277 722 187 q 967 385 967 328 q 932 456 967 429 q 850 484 897 484 q 759 450 798 484 q 721 352 721 416 l 640 352 q 706 502 640 448 q 851 551 766 551 q 987 509 931 551 q 1050 385 1050 462 q 976 251 1050 301 q 829 179 902 215 q 717 68 740 133 l 1050 68 l 1050 0 m 834 985 l 215 -28 l 130 -28 l 750 984 l 834 985 m 224 422 l 142 422 l 142 811 l 0 811 l 0 867 q 104 889 62 867 q 164 973 157 916 l 224 973 l 224 422 " }, "Ρ": { x_min: 0, x_max: 720, ha: 783, o: "m 424 1013 q 637 933 554 1013 q 720 723 720 853 q 633 508 720 591 q 413 426 546 426 l 140 426 l 140 0 l 0 0 l 0 1013 l 424 1013 m 378 889 l 140 889 l 140 548 l 371 548 q 521 589 458 548 q 592 720 592 637 q 527 845 592 801 q 378 889 463 889 " }, "'": { x_min: 0, x_max: 139, ha: 236, o: "m 139 851 q 102 737 139 784 q 0 669 65 690 l 0 734 q 59 787 42 741 q 72 873 72 821 l 0 873 l 0 1013 l 139 1013 l 139 851 " }, "ª": { x_min: 0, x_max: 350, ha: 397, o: "m 350 625 q 307 616 328 616 q 266 631 281 616 q 247 673 251 645 q 190 628 225 644 q 116 613 156 613 q 32 641 64 613 q 0 722 0 669 q 72 826 0 800 q 247 866 159 846 l 247 887 q 220 934 247 916 q 162 953 194 953 q 104 934 129 953 q 76 882 80 915 l 16 882 q 60 976 16 941 q 166 1011 104 1011 q 266 979 224 1011 q 308 891 308 948 l 308 706 q 311 679 308 688 q 331 670 315 670 l 350 672 l 350 625 m 247 757 l 247 811 q 136 790 175 798 q 64 726 64 773 q 83 682 64 697 q 132 667 103 667 q 207 690 174 667 q 247 757 247 718 " }, "΅": { x_min: 0, x_max: 450, ha: 553, o: "m 450 800 l 340 800 l 340 925 l 450 925 l 450 800 m 406 1040 l 212 800 l 129 800 l 269 1040 l 406 1040 m 110 800 l 0 800 l 0 925 l 110 925 l 110 800 " }, T: { x_min: 0, x_max: 777, ha: 835, o: "m 777 894 l 458 894 l 458 0 l 319 0 l 319 894 l 0 894 l 0 1013 l 777 1013 l 777 894 " }, "Φ": { x_min: 0, x_max: 915, ha: 997, o: "m 527 0 l 389 0 l 389 122 q 110 231 220 122 q 0 509 0 340 q 110 785 0 677 q 389 893 220 893 l 389 1013 l 527 1013 l 527 893 q 804 786 693 893 q 915 509 915 679 q 805 231 915 341 q 527 122 696 122 l 527 0 m 527 226 q 712 310 641 226 q 779 507 779 389 q 712 705 779 627 q 527 787 641 787 l 527 226 m 389 226 l 389 787 q 205 698 275 775 q 136 505 136 620 q 206 308 136 391 q 389 226 276 226 " }, "⁋": { x_min: 0, x_max: 0, ha: 694 }, j: { x_min: -77.78125, x_max: 167, ha: 349, o: "m 167 871 l 42 871 l 42 1013 l 167 1013 l 167 871 m 167 -80 q 121 -231 167 -184 q -26 -278 76 -278 l -77 -278 l -77 -164 l -41 -164 q 26 -143 11 -164 q 42 -65 42 -122 l 42 737 l 167 737 l 167 -80 " }, "Σ": { x_min: 0, x_max: 756.953125, ha: 819, o: "m 756 0 l 0 0 l 0 107 l 395 523 l 22 904 l 22 1013 l 745 1013 l 745 889 l 209 889 l 566 523 l 187 125 l 756 125 l 756 0 " }, "›": { x_min: 18.0625, x_max: 774, ha: 792, o: "m 774 376 l 18 40 l 18 149 l 631 421 l 18 692 l 18 799 l 774 465 l 774 376 " }, "<": { x_min: 17.984375, x_max: 773.609375, ha: 792, o: "m 773 40 l 18 376 l 17 465 l 773 799 l 773 692 l 159 420 l 773 149 l 773 40 " }, "£": { x_min: 0, x_max: 704.484375, ha: 801, o: "m 704 41 q 623 -10 664 5 q 543 -26 583 -26 q 359 15 501 -26 q 243 36 288 36 q 158 23 197 36 q 73 -21 119 10 l 6 76 q 125 195 90 150 q 175 331 175 262 q 147 443 175 383 l 0 443 l 0 512 l 108 512 q 43 734 43 623 q 120 929 43 854 q 358 1010 204 1010 q 579 936 487 1010 q 678 729 678 857 l 678 684 l 552 684 q 504 838 552 780 q 362 896 457 896 q 216 852 263 896 q 176 747 176 815 q 199 627 176 697 q 248 512 217 574 l 468 512 l 468 443 l 279 443 q 297 356 297 398 q 230 194 297 279 q 153 107 211 170 q 227 133 190 125 q 293 142 264 142 q 410 119 339 142 q 516 96 482 96 q 579 105 550 96 q 648 142 608 115 l 704 41 " }, t: { x_min: 0, x_max: 367, ha: 458, o: "m 367 0 q 312 -5 339 -2 q 262 -8 284 -8 q 145 28 183 -8 q 108 143 108 64 l 108 638 l 0 638 l 0 738 l 108 738 l 108 944 l 232 944 l 232 738 l 367 738 l 367 638 l 232 638 l 232 185 q 248 121 232 140 q 307 102 264 102 q 345 104 330 102 q 367 107 360 107 l 367 0 " }, "¬": { x_min: 0, x_max: 706, ha: 803, o: "m 706 411 l 706 158 l 630 158 l 630 335 l 0 335 l 0 411 l 706 411 " }, "λ": { x_min: 0, x_max: 750, ha: 803, o: "m 750 -7 q 679 -15 716 -15 q 538 59 591 -15 q 466 214 512 97 l 336 551 l 126 0 l 0 0 l 270 705 q 223 837 247 770 q 116 899 190 899 q 90 898 100 899 l 90 1004 q 152 1011 125 1011 q 298 938 244 1011 q 373 783 326 901 l 605 192 q 649 115 629 136 q 716 95 669 95 l 736 95 q 750 97 745 97 l 750 -7 " }, W: { x_min: 0, x_max: 1263.890625, ha: 1351, o: "m 1263 1013 l 995 0 l 859 0 l 627 837 l 405 0 l 265 0 l 0 1013 l 136 1013 l 342 202 l 556 1013 l 701 1013 l 921 207 l 1133 1012 l 1263 1013 " }, ">": { x_min: 18.0625, x_max: 774, ha: 792, o: "m 774 376 l 18 40 l 18 149 l 631 421 l 18 692 l 18 799 l 774 465 l 774 376 " }, v: { x_min: 0, x_max: 675.15625, ha: 761, o: "m 675 738 l 404 0 l 272 0 l 0 738 l 133 737 l 340 147 l 541 737 l 675 738 " }, "τ": { x_min: 0.28125, x_max: 644.5, ha: 703, o: "m 644 628 l 382 628 l 382 179 q 388 120 382 137 q 436 91 401 91 q 474 94 447 91 q 504 97 501 97 l 504 0 q 454 -9 482 -5 q 401 -14 426 -14 q 278 67 308 -14 q 260 233 260 118 l 260 628 l 0 628 l 0 739 l 644 739 l 644 628 " }, "ξ": { x_min: 0, x_max: 624.9375, ha: 699, o: "m 624 -37 q 608 -153 624 -96 q 563 -278 593 -211 l 454 -278 q 491 -183 486 -200 q 511 -83 511 -126 q 484 -23 511 -44 q 370 1 452 1 q 323 0 354 1 q 283 -1 293 -1 q 84 76 169 -1 q 0 266 0 154 q 56 431 0 358 q 197 538 108 498 q 94 613 134 562 q 54 730 54 665 q 77 823 54 780 q 143 901 101 867 l 27 901 l 27 1012 l 576 1012 l 576 901 l 380 901 q 244 863 303 901 q 178 745 178 820 q 312 600 178 636 q 532 582 380 582 l 532 479 q 276 455 361 479 q 118 281 118 410 q 165 173 118 217 q 274 120 208 133 q 494 101 384 110 q 624 -37 624 76 " }, "&": { x_min: -3, x_max: 894.25, ha: 992, o: "m 894 0 l 725 0 l 624 123 q 471 0 553 40 q 306 -41 390 -41 q 168 -7 231 -41 q 62 92 105 26 q 14 187 31 139 q -3 276 -3 235 q 55 433 -3 358 q 248 581 114 508 q 170 689 196 640 q 137 817 137 751 q 214 985 137 922 q 384 1041 284 1041 q 548 988 483 1041 q 622 824 622 928 q 563 666 622 739 q 431 556 516 608 l 621 326 q 649 407 639 361 q 663 493 653 426 l 781 493 q 703 229 781 352 l 894 0 m 504 818 q 468 908 504 877 q 384 940 433 940 q 293 907 331 940 q 255 818 255 875 q 289 714 255 767 q 363 628 313 678 q 477 729 446 682 q 504 818 504 771 m 556 209 l 314 499 q 179 395 223 449 q 135 283 135 341 q 146 222 135 253 q 183 158 158 192 q 333 80 241 80 q 556 209 448 80 " }, "Λ": { x_min: 0, x_max: 862.5, ha: 942, o: "m 862 0 l 719 0 l 426 847 l 143 0 l 0 0 l 356 1013 l 501 1013 l 862 0 " }, I: { x_min: 41, x_max: 180, ha: 293, o: "m 180 0 l 41 0 l 41 1013 l 180 1013 l 180 0 " }, G: { x_min: 0, x_max: 921, ha: 1011, o: "m 921 0 l 832 0 l 801 136 q 655 15 741 58 q 470 -28 568 -28 q 126 133 259 -28 q 0 499 0 284 q 125 881 0 731 q 486 1043 259 1043 q 763 957 647 1043 q 905 709 890 864 l 772 709 q 668 866 747 807 q 486 926 589 926 q 228 795 322 926 q 142 507 142 677 q 228 224 142 342 q 483 94 323 94 q 712 195 625 94 q 796 435 796 291 l 477 435 l 477 549 l 921 549 l 921 0 " }, "ΰ": { x_min: 0, x_max: 617, ha: 725, o: "m 524 800 l 414 800 l 414 925 l 524 925 l 524 800 m 183 800 l 73 800 l 73 925 l 183 925 l 183 800 m 617 352 q 540 93 617 199 q 308 -24 455 -24 q 76 93 161 -24 q 0 352 0 199 l 0 738 l 126 738 l 126 354 q 169 185 126 257 q 312 98 220 98 q 451 185 402 98 q 492 354 492 257 l 492 738 l 617 738 l 617 352 m 489 1040 l 300 819 l 216 819 l 351 1040 l 489 1040 " }, "`": { x_min: 0, x_max: 138.890625, ha: 236, o: "m 138 699 l 0 699 l 0 861 q 36 974 0 929 q 138 1041 72 1020 l 138 977 q 82 931 95 969 q 69 839 69 893 l 138 839 l 138 699 " }, "·": { x_min: 0, x_max: 142, ha: 239, o: "m 142 585 l 0 585 l 0 738 l 142 738 l 142 585 " }, "Υ": { x_min: 0.328125, x_max: 819.515625, ha: 889, o: "m 819 1013 l 482 416 l 482 0 l 342 0 l 342 416 l 0 1013 l 140 1013 l 411 533 l 679 1013 l 819 1013 " }, r: { x_min: 0, x_max: 355.5625, ha: 432, o: "m 355 621 l 343 621 q 179 569 236 621 q 122 411 122 518 l 122 0 l 0 0 l 0 737 l 117 737 l 117 604 q 204 719 146 686 q 355 753 262 753 l 355 621 " }, x: { x_min: 0, x_max: 675, ha: 764, o: "m 675 0 l 525 0 l 331 286 l 144 0 l 0 0 l 256 379 l 12 738 l 157 737 l 336 473 l 516 738 l 661 738 l 412 380 l 675 0 " }, "μ": { x_min: 0, x_max: 696.609375, ha: 747, o: "m 696 -4 q 628 -14 657 -14 q 498 97 513 -14 q 422 8 470 41 q 313 -24 374 -24 q 207 3 258 -24 q 120 80 157 31 l 120 -278 l 0 -278 l 0 738 l 124 738 l 124 343 q 165 172 124 246 q 308 82 216 82 q 451 177 402 82 q 492 358 492 254 l 492 738 l 616 738 l 616 214 q 623 136 616 160 q 673 92 636 92 q 696 95 684 92 l 696 -4 " }, h: { x_min: 0, x_max: 615, ha: 724, o: "m 615 472 l 615 0 l 490 0 l 490 454 q 456 590 490 535 q 338 654 416 654 q 186 588 251 654 q 122 436 122 522 l 122 0 l 0 0 l 0 1013 l 122 1013 l 122 633 q 218 727 149 694 q 362 760 287 760 q 552 676 484 760 q 615 472 615 600 " }, ".": { x_min: 0, x_max: 142, ha: 239, o: "m 142 0 l 0 0 l 0 151 l 142 151 l 142 0 " }, "φ": { x_min: -2, x_max: 878, ha: 974, o: "m 496 -279 l 378 -279 l 378 -17 q 101 88 204 -17 q -2 367 -2 194 q 68 626 -2 510 q 283 758 151 758 l 283 646 q 167 537 209 626 q 133 373 133 462 q 192 177 133 254 q 378 93 259 93 l 378 758 q 445 764 426 763 q 476 765 464 765 q 765 659 653 765 q 878 377 878 553 q 771 96 878 209 q 496 -17 665 -17 l 496 -279 m 496 93 l 514 93 q 687 183 623 93 q 746 380 746 265 q 691 569 746 491 q 522 658 629 658 l 496 656 l 496 93 " }, ";": { x_min: 0, x_max: 142, ha: 239, o: "m 142 585 l 0 585 l 0 738 l 142 738 l 142 585 m 142 -12 q 105 -132 142 -82 q 0 -206 68 -182 l 0 -138 q 58 -82 43 -123 q 68 0 68 -56 l 0 0 l 0 151 l 142 151 l 142 -12 " }, f: { x_min: 0, x_max: 378, ha: 472, o: "m 378 638 l 246 638 l 246 0 l 121 0 l 121 638 l 0 638 l 0 738 l 121 738 q 137 935 121 887 q 290 1028 171 1028 q 320 1027 305 1028 q 378 1021 334 1026 l 378 908 q 323 918 346 918 q 257 870 273 918 q 246 780 246 840 l 246 738 l 378 738 l 378 638 " }, "“": { x_min: 1, x_max: 348.21875, ha: 454, o: "m 140 670 l 1 670 l 1 830 q 37 943 1 897 q 140 1011 74 990 l 140 947 q 82 900 97 940 q 68 810 68 861 l 140 810 l 140 670 m 348 670 l 209 670 l 209 830 q 245 943 209 897 q 348 1011 282 990 l 348 947 q 290 900 305 940 q 276 810 276 861 l 348 810 l 348 670 " }, A: { x_min: 0.03125, x_max: 906.953125, ha: 1008, o: "m 906 0 l 756 0 l 648 303 l 251 303 l 142 0 l 0 0 l 376 1013 l 529 1013 l 906 0 m 610 421 l 452 867 l 293 421 l 610 421 " }, "‘": { x_min: 1, x_max: 139.890625, ha: 236, o: "m 139 670 l 1 670 l 1 830 q 37 943 1 897 q 139 1011 74 990 l 139 947 q 82 900 97 940 q 68 810 68 861 l 139 810 l 139 670 " }, "ϊ": { x_min: -70, x_max: 283, ha: 361, o: "m 283 800 l 173 800 l 173 925 l 283 925 l 283 800 m 40 800 l -70 800 l -70 925 l 40 925 l 40 800 m 283 3 q 232 -10 257 -5 q 181 -15 206 -15 q 84 26 118 -15 q 41 200 41 79 l 41 737 l 166 737 l 167 215 q 171 141 167 157 q 225 101 182 101 q 247 103 238 101 q 283 112 256 104 l 283 3 " }, "π": { x_min: -0.21875, x_max: 773.21875, ha: 857, o: "m 773 -7 l 707 -11 q 575 40 607 -11 q 552 174 552 77 l 552 226 l 552 626 l 222 626 l 222 0 l 97 0 l 97 626 l 0 626 l 0 737 l 773 737 l 773 626 l 676 626 l 676 171 q 695 103 676 117 q 773 90 714 90 l 773 -7 " }, "ά": { x_min: 0, x_max: 765.5625, ha: 809, o: "m 765 -4 q 698 -14 726 -14 q 564 97 586 -14 q 466 7 525 40 q 337 -26 407 -26 q 88 98 186 -26 q 0 369 0 212 q 88 637 0 525 q 337 760 184 760 q 465 727 407 760 q 563 637 524 695 l 563 738 l 685 738 l 685 222 q 693 141 685 168 q 748 94 708 94 q 765 95 760 94 l 765 -4 m 584 371 q 531 562 584 485 q 360 653 470 653 q 192 566 254 653 q 135 379 135 489 q 186 181 135 261 q 358 84 247 84 q 528 176 465 84 q 584 371 584 260 m 604 1040 l 415 819 l 332 819 l 466 1040 l 604 1040 " }, O: { x_min: 0, x_max: 958, ha: 1057, o: "m 485 1041 q 834 882 702 1041 q 958 512 958 734 q 834 136 958 287 q 481 -26 702 -26 q 126 130 261 -26 q 0 504 0 279 q 127 880 0 728 q 485 1041 263 1041 m 480 98 q 731 225 638 98 q 815 504 815 340 q 733 783 815 669 q 480 912 640 912 q 226 784 321 912 q 142 504 142 670 q 226 224 142 339 q 480 98 319 98 " }, n: { x_min: 0, x_max: 615, ha: 724, o: "m 615 463 l 615 0 l 490 0 l 490 454 q 453 592 490 537 q 331 656 410 656 q 178 585 240 656 q 117 421 117 514 l 117 0 l 0 0 l 0 738 l 117 738 l 117 630 q 218 728 150 693 q 359 764 286 764 q 552 675 484 764 q 615 463 615 593 " }, l: { x_min: 41, x_max: 166, ha: 279, o: "m 166 0 l 41 0 l 41 1013 l 166 1013 l 166 0 " }, "¤": { x_min: 40.09375, x_max: 728.796875, ha: 825, o: "m 728 304 l 649 224 l 512 363 q 383 331 458 331 q 256 363 310 331 l 119 224 l 40 304 l 177 441 q 150 553 150 493 q 184 673 150 621 l 40 818 l 119 898 l 267 749 q 321 766 291 759 q 384 773 351 773 q 447 766 417 773 q 501 749 477 759 l 649 898 l 728 818 l 585 675 q 612 618 604 648 q 621 553 621 587 q 591 441 621 491 l 728 304 m 384 682 q 280 643 318 682 q 243 551 243 604 q 279 461 243 499 q 383 423 316 423 q 487 461 449 423 q 525 553 525 500 q 490 641 525 605 q 384 682 451 682 " }, "κ": { x_min: 0, x_max: 632.328125, ha: 679, o: "m 632 0 l 482 0 l 225 384 l 124 288 l 124 0 l 0 0 l 0 738 l 124 738 l 124 446 l 433 738 l 596 738 l 312 466 l 632 0 " }, p: { x_min: 0, x_max: 685, ha: 786, o: "m 685 364 q 598 96 685 205 q 350 -23 504 -23 q 121 89 205 -23 l 121 -278 l 0 -278 l 0 738 l 121 738 l 121 633 q 220 726 159 691 q 351 761 280 761 q 598 636 504 761 q 685 364 685 522 m 557 371 q 501 560 557 481 q 330 651 437 651 q 162 559 223 651 q 108 366 108 479 q 162 177 108 254 q 333 87 224 87 q 502 178 441 87 q 557 371 557 258 " }, "‡": { x_min: 0, x_max: 777, ha: 835, o: "m 458 238 l 458 0 l 319 0 l 319 238 l 0 238 l 0 360 l 319 360 l 319 681 l 0 683 l 0 804 l 319 804 l 319 1015 l 458 1013 l 458 804 l 777 804 l 777 683 l 458 683 l 458 360 l 777 360 l 777 238 l 458 238 " }, "ψ": { x_min: 0, x_max: 808, ha: 907, o: "m 465 -278 l 341 -278 l 341 -15 q 87 102 180 -15 q 0 378 0 210 l 0 739 l 133 739 l 133 379 q 182 195 133 275 q 341 98 242 98 l 341 922 l 465 922 l 465 98 q 623 195 563 98 q 675 382 675 278 l 675 742 l 808 742 l 808 381 q 720 104 808 213 q 466 -13 627 -13 l 465 -278 " }, "η": { x_min: 0.78125, x_max: 697, ha: 810, o: "m 697 -278 l 572 -278 l 572 454 q 540 587 572 536 q 425 650 501 650 q 271 579 337 650 q 206 420 206 509 l 206 0 l 81 0 l 81 489 q 73 588 81 562 q 0 644 56 644 l 0 741 q 68 755 38 755 q 158 720 124 755 q 200 630 193 686 q 297 726 234 692 q 434 761 359 761 q 620 692 544 761 q 697 516 697 624 l 697 -278 " } };
var cssFontWeight = "normal";
var ascender = 1189;
var underlinePosition = -100;
var cssFontStyle = "normal";
var boundingBox = { yMin: -334, xMin: -111, yMax: 1189, xMax: 1672 };
var resolution = 1e3;
var original_font_information = { postscript_name: "Helvetiker-Regular", version_string: "Version 1.00 2004 initial release", vendor_url: "http://www.magenta.gr/", full_font_name: "Helvetiker", font_family_name: "Helvetiker", copyright: "Copyright (c) Μagenta ltd, 2004", description: "", trademark: "", designer: "", designer_url: "", unique_font_identifier: "Μagenta ltd:Helvetiker:22-10-104", license_url: "http://www.ellak.gr/fonts/MgOpen/license.html", license_description: 'Copyright (c) 2004 by MAGENTA Ltd. All Rights Reserved.\r\n\r\nPermission is hereby granted, free of charge, to any person obtaining a copy of the fonts accompanying this license ("Fonts") and associated documentation files (the "Font Software"), to reproduce and distribute the Font Software, including without limitation the rights to use, copy, merge, publish, distribute, and/or sell copies of the Font Software, and to permit persons to whom the Font Software is furnished to do so, subject to the following conditions: \r\n\r\nThe above copyright and this permission notice shall be included in all copies of one or more of the Font Software typefaces.\r\n\r\nThe Font Software may be modified, altered, or added to, and in particular the designs of glyphs or characters in the Fonts may be modified and additional glyphs or characters may be added to the Fonts, only if the fonts are renamed to names not containing the word "MgOpen", or if the modifications are accepted for inclusion in the Font Software itself by the each appointed Administrator.\r\n\r\nThis License becomes null and void to the extent applicable to Fonts or Font Software that has been modified and is distributed under the "MgOpen" name.\r\n\r\nThe Font Software may be sold as part of a larger software package but no copy of one or more of the Font Software typefaces may be sold by itself. \r\n\r\nTHE FONT SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO ANY WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT OF COPYRIGHT, PATENT, TRADEMARK, OR OTHER RIGHT. IN NO EVENT SHALL MAGENTA OR PERSONS OR BODIES IN CHARGE OF ADMINISTRATION AND MAINTENANCE OF THE FONT SOFTWARE BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, INCLUDING ANY GENERAL, SPECIAL, INDIRECT, INCIDENTAL, OR CONSEQUENTIAL DAMAGES, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF THE USE OR INABILITY TO USE THE FONT SOFTWARE OR FROM OTHER DEALINGS IN THE FONT SOFTWARE.', manufacturer_name: "Μagenta ltd", font_sub_family_name: "Regular" };
var descender = -334;
var familyName = "Helvetiker";
var lineHeight = 1522;
var underlineThickness = 50;
var defaultTypeFace = { glyphs, cssFontWeight, ascender, underlinePosition, cssFontStyle, boundingBox, resolution, original_font_information, descender, familyName, lineHeight, underlineThickness };
var THREE$3 = _objectSpread2(_objectSpread2({}, window.THREE ? window.THREE : {
  BoxGeometry,
  CircleGeometry,
  DoubleSide,
  Group,
  Mesh,
  MeshLambertMaterial,
  TextGeometry,
  Vector3
}), {}, {
  Font,
  TextGeometry
});
var LabelsLayerKapsule = index({
  props: {
    labelsData: {
      "default": []
    },
    labelLat: {
      "default": "lat"
    },
    labelLng: {
      "default": "lng"
    },
    labelAltitude: {
      "default": 2e-3
    },
    // in units of globe radius
    labelText: {
      "default": "text"
    },
    labelSize: {
      "default": 0.5
    },
    // text height in deg
    labelTypeFace: {
      "default": defaultTypeFace,
      onChange: function onChange7(tf, state) {
        state.font = new THREE$3.Font(tf);
      }
    },
    labelColor: {
      "default": function _default22() {
        return "lightgrey";
      }
    },
    labelRotation: {
      "default": 0
    },
    // clockwise degrees, relative to the latitute parallel plane
    labelResolution: {
      "default": 3
    },
    // how many segments in the text's curves
    labelIncludeDot: {
      "default": true
    },
    labelDotRadius: {
      "default": 0.1
    },
    // in deg
    labelDotOrientation: {
      "default": function _default23() {
        return "bottom";
      }
    },
    // right, top, bottom
    labelsTransitionDuration: {
      "default": 1e3,
      triggerUpdate: false
    }
    // ms
  },
  init: function init12(threeObj, state, _ref) {
    var tweenGroup = _ref.tweenGroup;
    emptyObject(threeObj);
    state.scene = threeObj;
    state.tweenGroup = tweenGroup;
    var circleGeometry = new THREE$3.CircleGeometry(1, 32);
    state.dataMapper = new ThreeDigest(threeObj, {
      objBindAttr: "__threeObjLabel"
    }).onCreateObj(function() {
      var material = new THREE$3.MeshLambertMaterial();
      material.side = DoubleSide;
      var obj = new THREE$3.Group();
      obj.add(new THREE$3.Mesh(circleGeometry, material));
      var textObj = new THREE$3.Mesh(void 0, material);
      obj.add(textObj);
      var bbObj = new THREE$3.Mesh();
      bbObj.visible = false;
      textObj.add(bbObj);
      obj.__globeObjType = "label";
      return obj;
    });
  },
  update: function update12(state) {
    var latAccessor = index2(state.labelLat);
    var lngAccessor = index2(state.labelLng);
    var altitudeAccessor = index2(state.labelAltitude);
    var textAccessor = index2(state.labelText);
    var sizeAccessor = index2(state.labelSize);
    var rotationAccessor = index2(state.labelRotation);
    var colorAccessor = index2(state.labelColor);
    var includeDotAccessor = index2(state.labelIncludeDot);
    var dotRadiusAccessor = index2(state.labelDotRadius);
    var dotOrientationAccessor = index2(state.labelDotOrientation);
    var orientations = /* @__PURE__ */ new Set(["right", "top", "bottom"]);
    var pxPerDeg = 2 * Math.PI * GLOBE_RADIUS / 360;
    state.dataMapper.onUpdateObj(function(obj, d2) {
      var _obj$children = _slicedToArray3(obj.children, 2), dotObj = _obj$children[0], textObj = _obj$children[1];
      var _textObj$children = _slicedToArray3(textObj.children, 1), bbObj = _textObj$children[0];
      var color2 = colorAccessor(d2);
      var opacity = colorAlpha(color2);
      textObj.material.color.set(colorStr2Hex(color2));
      textObj.material.transparent = opacity < 1;
      textObj.material.opacity = opacity;
      var includeDot = includeDotAccessor(d2);
      var dotOrient = dotOrientationAccessor(d2);
      !includeDot || !orientations.has(dotOrient) && (dotOrient = "bottom");
      var dotR = includeDot ? +dotRadiusAccessor(d2) * pxPerDeg : 1e-12;
      dotObj.scale.x = dotObj.scale.y = dotR;
      var textHeight = +sizeAccessor(d2) * pxPerDeg;
      textObj.geometry && textObj.geometry.dispose();
      textObj.geometry = new THREE$3.TextGeometry(textAccessor(d2), {
        font: state.font,
        size: textHeight,
        depth: 0,
        bevelEnabled: true,
        bevelThickness: 0,
        bevelSize: 0,
        curveSegments: state.labelResolution
      });
      bbObj.geometry && bbObj.geometry.dispose();
      textObj.geometry.computeBoundingBox();
      bbObj.geometry = _construct(THREE$3.BoxGeometry, _toConsumableArray3(new THREE$3.Vector3().subVectors(textObj.geometry.boundingBox.max, textObj.geometry.boundingBox.min).clampScalar(0, Infinity).toArray()));
      dotOrient !== "right" && textObj.geometry.center();
      if (includeDot) {
        var padding = dotR + textHeight / 2;
        dotOrient === "right" && (textObj.position.x = padding);
        textObj.position.y = {
          right: -textHeight / 2,
          // center vertically
          top: padding + textHeight / 2,
          bottom: -padding - textHeight / 2
        }[dotOrient];
      }
      var applyPosition = function applyPosition2(td) {
        var _obj$__currentTargetD = obj.__currentTargetD = td, lat = _obj$__currentTargetD.lat, lng = _obj$__currentTargetD.lng, alt = _obj$__currentTargetD.alt, rot = _obj$__currentTargetD.rot, scale = _obj$__currentTargetD.scale;
        Object.assign(obj.position, polar2Cartesian(lat, lng, alt));
        obj.lookAt(state.scene.localToWorld(new THREE$3.Vector3(0, 0, 0)));
        obj.rotateY(Math.PI);
        obj.rotateZ(-rot * Math.PI / 180);
        obj.scale.x = obj.scale.y = obj.scale.z = scale;
      };
      var targetD = {
        lat: +latAccessor(d2),
        lng: +lngAccessor(d2),
        alt: +altitudeAccessor(d2),
        rot: +rotationAccessor(d2),
        scale: 1
      };
      var currentTargetD = obj.__currentTargetD || Object.assign({}, targetD, {
        scale: 1e-12
      });
      if (Object.keys(targetD).some(function(k2) {
        return currentTargetD[k2] !== targetD[k2];
      })) {
        if (!state.labelsTransitionDuration || state.labelsTransitionDuration < 0) {
          applyPosition(targetD);
        } else {
          state.tweenGroup.add(new Tween(currentTargetD).to(targetD, state.labelsTransitionDuration).easing(Easing.Quadratic.InOut).onUpdate(applyPosition).start());
        }
      }
    }).digest(state.labelsData);
  }
});
var THREE$2 = _objectSpread2(_objectSpread2({}, window.THREE ? window.THREE : {}), {}, {
  CSS2DObject
});
var HtmlElementsLayerKapsule = index({
  props: {
    htmlElementsData: {
      "default": []
    },
    htmlLat: {
      "default": "lat"
    },
    htmlLng: {
      "default": "lng"
    },
    htmlAltitude: {
      "default": 0
    },
    // in units of globe radius
    htmlElement: {},
    htmlElementVisibilityModifier: {
      triggerUpdate: false
    },
    htmlTransitionDuration: {
      "default": 1e3,
      triggerUpdate: false
    },
    // ms
    isBehindGlobe: {
      onChange: function onChange8() {
        this.updateObjVisibility();
      },
      triggerUpdate: false
    }
  },
  methods: {
    updateObjVisibility: function updateObjVisibility(state, obj) {
      if (!state.dataMapper) return;
      var objs = obj ? [obj] : state.dataMapper.entries().map(function(_ref) {
        var _ref2 = _slicedToArray3(_ref, 2), o2 = _ref2[1];
        return o2;
      }).filter(function(d2) {
        return d2;
      });
      objs.forEach(function(obj2) {
        var isVisible = !state.isBehindGlobe || !state.isBehindGlobe(obj2.position);
        if (state.htmlElementVisibilityModifier) {
          obj2.visible = true;
          state.htmlElementVisibilityModifier(obj2.element, isVisible);
        } else {
          obj2.visible = isVisible;
        }
      });
    }
  },
  init: function init13(threeObj, state, _ref3) {
    var tweenGroup = _ref3.tweenGroup;
    emptyObject(threeObj);
    state.scene = threeObj;
    state.tweenGroup = tweenGroup;
    state.dataMapper = new ThreeDigest(threeObj, {
      objBindAttr: "__threeObjHtml"
    }).onCreateObj(function(d2) {
      var elem = index2(state.htmlElement)(d2);
      var obj = new THREE$2.CSS2DObject(elem);
      obj.__globeObjType = "html";
      return obj;
    });
  },
  update: function update13(state, changedProps) {
    var _this = this;
    var latAccessor = index2(state.htmlLat);
    var lngAccessor = index2(state.htmlLng);
    var altitudeAccessor = index2(state.htmlAltitude);
    changedProps.hasOwnProperty("htmlElement") && state.dataMapper.clear();
    state.dataMapper.onUpdateObj(function(obj, d2) {
      var applyUpdate = function applyUpdate2(td) {
        var _obj$__currentTargetD = obj.__currentTargetD = td, alt = _obj$__currentTargetD.alt, lat = _obj$__currentTargetD.lat, lng = _obj$__currentTargetD.lng;
        Object.assign(obj.position, polar2Cartesian(lat, lng, alt));
        _this.updateObjVisibility(obj);
      };
      var targetD = {
        lat: +latAccessor(d2),
        lng: +lngAccessor(d2),
        alt: +altitudeAccessor(d2)
      };
      if (!state.htmlTransitionDuration || state.htmlTransitionDuration < 0 || !obj.__currentTargetD) {
        applyUpdate(targetD);
      } else {
        state.tweenGroup.add(new Tween(obj.__currentTargetD).to(targetD, state.htmlTransitionDuration).easing(Easing.Quadratic.InOut).onUpdate(applyUpdate).start());
      }
    }).digest(state.htmlElementsData);
  }
});
var THREE$1 = window.THREE ? window.THREE : {
  Group,
  Mesh,
  MeshLambertMaterial,
  SphereGeometry
};
var ObjectsLayerKapsule = index({
  props: {
    objectsData: {
      "default": []
    },
    objectLat: {
      "default": "lat"
    },
    objectLng: {
      "default": "lng"
    },
    objectAltitude: {
      "default": 0.01
    },
    // in units of globe radius
    objectFacesSurface: {
      "default": true
    },
    objectRotation: {},
    objectThreeObject: {
      "default": new THREE$1.Mesh(
        // default object: yellow sphere
        new THREE$1.SphereGeometry(1, 16, 8),
        new THREE$1.MeshLambertMaterial({
          color: "#ffffaa",
          transparent: true,
          opacity: 0.7
        })
      )
    }
  },
  init: function init14(threeObj, state) {
    emptyObject(threeObj);
    state.scene = threeObj;
    state.dataMapper = new ThreeDigest(threeObj, {
      objBindAttr: "__threeObjObject"
    }).onCreateObj(function(d2) {
      var obj = index2(state.objectThreeObject)(d2);
      if (state.objectThreeObject === obj) {
        obj = obj.clone();
      }
      var g2 = new THREE$1.Group();
      g2.add(obj);
      g2.__globeObjType = "object";
      return g2;
    });
  },
  update: function update14(state, changedProps) {
    var latAccessor = index2(state.objectLat);
    var lngAccessor = index2(state.objectLng);
    var altitudeAccessor = index2(state.objectAltitude);
    var parallelAccessor = index2(state.objectFacesSurface);
    var rotationAccessor = index2(state.objectRotation);
    changedProps.hasOwnProperty("objectThreeObject") && state.dataMapper.clear();
    state.dataMapper.onUpdateObj(function(objG, d2) {
      var lat = +latAccessor(d2);
      var lng = +lngAccessor(d2);
      var alt = +altitudeAccessor(d2);
      Object.assign(objG.position, polar2Cartesian(lat, lng, alt));
      parallelAccessor(d2) ? objG.setRotationFromEuler(new Euler(deg2Rad$1(-lat), deg2Rad$1(lng), 0, "YXZ")) : objG.rotation.set(0, 0, 0);
      var obj = objG.children[0];
      var rot = rotationAccessor(d2);
      rot && obj.setRotationFromEuler(new Euler(deg2Rad$1(rot.x || 0), deg2Rad$1(rot.y || 0), deg2Rad$1(rot.z || 0)));
    }).digest(state.objectsData);
  }
});
var CustomLayerKapsule = index({
  props: {
    customLayerData: {
      "default": []
    },
    customThreeObject: {},
    customThreeObjectUpdate: {
      triggerUpdate: false
    }
  },
  init: function init15(threeObj, state) {
    emptyObject(threeObj);
    state.scene = threeObj;
    state.dataMapper = new ThreeDigest(threeObj, {
      objBindAttr: "__threeObjCustom"
    }).onCreateObj(function(d2) {
      var obj = index2(state.customThreeObject)(d2, GLOBE_RADIUS);
      if (obj) {
        if (state.customThreeObject === obj) {
          obj = obj.clone();
        }
        obj.__globeObjType = "custom";
      }
      return obj;
    });
  },
  update: function update15(state, changedProps) {
    if (!state.customThreeObjectUpdate) {
      emptyObject(state.scene);
    }
    var customObjectUpdateAccessor = index2(state.customThreeObjectUpdate);
    changedProps.hasOwnProperty("customThreeObject") && state.dataMapper.clear();
    state.dataMapper.onUpdateObj(function(obj, d2) {
      return customObjectUpdateAccessor(obj, d2, GLOBE_RADIUS);
    }).digest(state.customLayerData);
  }
});
var THREE = window.THREE ? window.THREE : {
  Camera,
  Group,
  Vector2,
  Vector3
};
var layers = ["globeLayer", "pointsLayer", "arcsLayer", "hexBinLayer", "heatmapsLayer", "polygonsLayer", "hexedPolygonsLayer", "pathsLayer", "tilesLayer", "particlesLayer", "ringsLayer", "labelsLayer", "htmlElementsLayer", "objectsLayer", "customLayer"];
var bindGlobeLayer = linkKapsule("globeLayer", GlobeLayerKapsule);
var linkedGlobeLayerProps = Object.assign.apply(Object, _toConsumableArray3(["globeImageUrl", "bumpImageUrl", "globeTileEngineUrl", "globeTileEngineMaxLevel", "showGlobe", "showGraticules", "showAtmosphere", "atmosphereColor", "atmosphereAltitude"].map(function(p2) {
  return _defineProperty2({}, p2, bindGlobeLayer.linkProp(p2));
})));
var linkedGlobeLayerMethods = Object.assign.apply(Object, _toConsumableArray3(["globeMaterial"].map(function(p2) {
  return _defineProperty2({}, p2, bindGlobeLayer.linkMethod(p2));
})));
var bindPointsLayer = linkKapsule("pointsLayer", PointsLayerKapsule);
var linkedPointsLayerProps = Object.assign.apply(Object, _toConsumableArray3(["pointsData", "pointLat", "pointLng", "pointColor", "pointAltitude", "pointRadius", "pointResolution", "pointsMerge", "pointsTransitionDuration"].map(function(p2) {
  return _defineProperty2({}, p2, bindPointsLayer.linkProp(p2));
})));
var bindArcsLayer = linkKapsule("arcsLayer", ArcsLayerKapsule);
var linkedArcsLayerProps = Object.assign.apply(Object, _toConsumableArray3(["arcsData", "arcStartLat", "arcStartLng", "arcEndLat", "arcEndLng", "arcColor", "arcAltitude", "arcAltitudeAutoScale", "arcStroke", "arcCurveResolution", "arcCircularResolution", "arcDashLength", "arcDashGap", "arcDashInitialGap", "arcDashAnimateTime", "arcsTransitionDuration"].map(function(p2) {
  return _defineProperty2({}, p2, bindArcsLayer.linkProp(p2));
})));
var bindHexBinLayer = linkKapsule("hexBinLayer", HexBinLayerKapsule);
var linkedHexBinLayerProps = Object.assign.apply(Object, _toConsumableArray3(["hexBinPointsData", "hexBinPointLat", "hexBinPointLng", "hexBinPointWeight", "hexBinResolution", "hexMargin", "hexTopCurvatureResolution", "hexTopColor", "hexSideColor", "hexAltitude", "hexBinMerge", "hexTransitionDuration"].map(function(p2) {
  return _defineProperty2({}, p2, bindHexBinLayer.linkProp(p2));
})));
var bindHeatmapsLayer = linkKapsule("heatmapsLayer", HeatmapsLayerKapsule);
var linkedHeatmapsLayerProps = Object.assign.apply(Object, _toConsumableArray3(["heatmapsData", "heatmapPoints", "heatmapPointLat", "heatmapPointLng", "heatmapPointWeight", "heatmapBandwidth", "heatmapColorFn", "heatmapColorSaturation", "heatmapBaseAltitude", "heatmapTopAltitude", "heatmapsTransitionDuration"].map(function(p2) {
  return _defineProperty2({}, p2, bindHeatmapsLayer.linkProp(p2));
})));
var bindHexedPolygonsLayer = linkKapsule("hexedPolygonsLayer", HexedPolygonsLayerKapsule);
var linkedHexedPolygonsLayerProps = Object.assign.apply(Object, _toConsumableArray3(["hexPolygonsData", "hexPolygonGeoJsonGeometry", "hexPolygonColor", "hexPolygonAltitude", "hexPolygonResolution", "hexPolygonMargin", "hexPolygonUseDots", "hexPolygonCurvatureResolution", "hexPolygonDotResolution", "hexPolygonsTransitionDuration"].map(function(p2) {
  return _defineProperty2({}, p2, bindHexedPolygonsLayer.linkProp(p2));
})));
var bindPolygonsLayer = linkKapsule("polygonsLayer", PolygonsLayerKapsule);
var linkedPolygonsLayerProps = Object.assign.apply(Object, _toConsumableArray3(["polygonsData", "polygonGeoJsonGeometry", "polygonCapColor", "polygonCapMaterial", "polygonSideColor", "polygonSideMaterial", "polygonStrokeColor", "polygonAltitude", "polygonCapCurvatureResolution", "polygonsTransitionDuration"].map(function(p2) {
  return _defineProperty2({}, p2, bindPolygonsLayer.linkProp(p2));
})));
var bindPathsLayer = linkKapsule("pathsLayer", PathsLayerKapsule);
var linkedPathsLayerProps = Object.assign.apply(Object, _toConsumableArray3(["pathsData", "pathPoints", "pathPointLat", "pathPointLng", "pathPointAlt", "pathResolution", "pathColor", "pathStroke", "pathDashLength", "pathDashGap", "pathDashInitialGap", "pathDashAnimateTime", "pathTransitionDuration"].map(function(p2) {
  return _defineProperty2({}, p2, bindPathsLayer.linkProp(p2));
})));
var bindTilesLayer = linkKapsule("tilesLayer", TilesLayerKapsule);
var linkedTilesLayerProps = Object.assign.apply(Object, _toConsumableArray3(["tilesData", "tileLat", "tileLng", "tileAltitude", "tileWidth", "tileHeight", "tileUseGlobeProjection", "tileMaterial", "tileCurvatureResolution", "tilesTransitionDuration"].map(function(p2) {
  return _defineProperty2({}, p2, bindTilesLayer.linkProp(p2));
})));
var bindParticlesLayer = linkKapsule("particlesLayer", ParticlesLayerKapsule);
var linkedParticlesLayerProps = Object.assign.apply(Object, _toConsumableArray3(["particlesData", "particlesList", "particleLat", "particleLng", "particleAltitude", "particlesSize", "particlesSizeAttenuation", "particlesColor", "particlesTexture"].map(function(p2) {
  return _defineProperty2({}, p2, bindParticlesLayer.linkProp(p2));
})));
var bindRingsLayer = linkKapsule("ringsLayer", RingsLayerKapsule);
var linkedRingsLayerProps = Object.assign.apply(Object, _toConsumableArray3(["ringsData", "ringLat", "ringLng", "ringAltitude", "ringColor", "ringResolution", "ringMaxRadius", "ringPropagationSpeed", "ringRepeatPeriod"].map(function(p2) {
  return _defineProperty2({}, p2, bindRingsLayer.linkProp(p2));
})));
var bindLabelsLayer = linkKapsule("labelsLayer", LabelsLayerKapsule);
var linkedLabelsLayerProps = Object.assign.apply(Object, _toConsumableArray3(["labelsData", "labelLat", "labelLng", "labelAltitude", "labelRotation", "labelText", "labelSize", "labelTypeFace", "labelColor", "labelResolution", "labelIncludeDot", "labelDotRadius", "labelDotOrientation", "labelsTransitionDuration"].map(function(p2) {
  return _defineProperty2({}, p2, bindLabelsLayer.linkProp(p2));
})));
var bindHtmlElementsLayer = linkKapsule("htmlElementsLayer", HtmlElementsLayerKapsule);
var linkedHtmlElementsLayerProps = Object.assign.apply(Object, _toConsumableArray3(["htmlElementsData", "htmlLat", "htmlLng", "htmlAltitude", "htmlElement", "htmlElementVisibilityModifier", "htmlTransitionDuration"].map(function(p2) {
  return _defineProperty2({}, p2, bindHtmlElementsLayer.linkProp(p2));
})));
var bindObjectsLayer = linkKapsule("objectsLayer", ObjectsLayerKapsule);
var linkedObjectsLayerProps = Object.assign.apply(Object, _toConsumableArray3(["objectsData", "objectLat", "objectLng", "objectAltitude", "objectRotation", "objectFacesSurface", "objectThreeObject"].map(function(p2) {
  return _defineProperty2({}, p2, bindObjectsLayer.linkProp(p2));
})));
var bindCustomLayer = linkKapsule("customLayer", CustomLayerKapsule);
var linkedCustomLayerProps = Object.assign.apply(Object, _toConsumableArray3(["customLayerData", "customThreeObject", "customThreeObjectUpdate"].map(function(p2) {
  return _defineProperty2({}, p2, bindCustomLayer.linkProp(p2));
})));
var Globe = index({
  props: _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({
    onGlobeReady: {
      triggerUpdate: false
    },
    rendererSize: {
      "default": new THREE.Vector2(window.innerWidth, window.innerHeight),
      onChange: function onChange9(rendererSize, state) {
        state.pathsLayer.rendererSize(rendererSize);
      },
      triggerUpdate: false
    }
  }, linkedGlobeLayerProps), linkedPointsLayerProps), linkedArcsLayerProps), linkedHexBinLayerProps), linkedHeatmapsLayerProps), linkedPolygonsLayerProps), linkedHexedPolygonsLayerProps), linkedPathsLayerProps), linkedTilesLayerProps), linkedParticlesLayerProps), linkedRingsLayerProps), linkedLabelsLayerProps), linkedHtmlElementsLayerProps), linkedObjectsLayerProps), linkedCustomLayerProps),
  methods: _objectSpread2({
    getGlobeRadius,
    getCoords: function getCoords(state) {
      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
        args[_key - 1] = arguments[_key];
      }
      return polar2Cartesian.apply(void 0, args);
    },
    toGeoCoords: function toGeoCoords(state) {
      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {
        args[_key2 - 1] = arguments[_key2];
      }
      return cartesian2Polar.apply(void 0, args);
    },
    setPointOfView: function setPointOfView(state, camera3) {
      var globalPov = camera3 instanceof THREE.Camera ? camera3.position : camera3;
      var globeRadius = getGlobeRadius();
      var isBehindGlobe = void 0;
      if (state.scene && globalPov) {
        var pov, povDist, povEdgeDist, povEdgeAngle;
        isBehindGlobe = function isBehindGlobe2(pos) {
          pov === void 0 && (pov = globalPov.clone().applyMatrix4(state.scene.matrixWorld.clone().invert()));
          povDist === void 0 && (povDist = pov.length());
          povEdgeDist === void 0 && (povEdgeDist = Math.sqrt(Math.pow(povDist, 2) - Math.pow(globeRadius, 2)));
          povEdgeAngle === void 0 && (povEdgeAngle = Math.acos(povEdgeDist / povDist));
          var povPosDist = pov.distanceTo(pos);
          if (povPosDist < povEdgeDist) return false;
          var posDist = pos.length();
          var povPosAngle = Math.acos((Math.pow(povDist, 2) + Math.pow(povPosDist, 2) - Math.pow(posDist, 2)) / (2 * povDist * povPosDist));
          return povPosAngle < povEdgeAngle;
        };
      }
      state.layersThatNeedUpdatePov.forEach(function(l2) {
        return l2.updatePov(camera3);
      });
      state.layersThatNeedBehindGlobeChecker.forEach(function(l2) {
        return l2.isBehindGlobe(isBehindGlobe);
      });
    },
    pauseAnimation: function pauseAnimation4(state) {
      if (state.animationFrameRequestId !== null) {
        cancelAnimationFrame(state.animationFrameRequestId);
        state.animationFrameRequestId = null;
      }
      state.pausableLayers.forEach(function(l2) {
        var _l$pauseAnimation;
        return (_l$pauseAnimation = l2.pauseAnimation) === null || _l$pauseAnimation === void 0 ? void 0 : _l$pauseAnimation.call(l2);
      });
      return this;
    },
    resumeAnimation: function resumeAnimation4(state) {
      if (state.animationFrameRequestId === null) {
        this._animationCycle();
      }
      state.pausableLayers.forEach(function(l2) {
        var _l$resumeAnimation;
        return (_l$resumeAnimation = l2.resumeAnimation) === null || _l$resumeAnimation === void 0 ? void 0 : _l$resumeAnimation.call(l2);
      });
      return this;
    },
    _animationCycle: function _animationCycle(state) {
      state.animationFrameRequestId = requestAnimationFrame(this._animationCycle);
      state.tweenGroup.update();
    },
    _destructor: function _destructor5(state) {
      this.pauseAnimation();
      state.destructableLayers.forEach(function(l2) {
        return l2._destructor();
      });
    }
  }, linkedGlobeLayerMethods),
  stateInit: function stateInit4() {
    var tweenGroup = new Group2();
    var initProps = {
      tweenGroup
    };
    var layers2 = {
      globeLayer: GlobeLayerKapsule(initProps),
      pointsLayer: PointsLayerKapsule(initProps),
      arcsLayer: ArcsLayerKapsule(initProps),
      hexBinLayer: HexBinLayerKapsule(initProps),
      heatmapsLayer: HeatmapsLayerKapsule(initProps),
      polygonsLayer: PolygonsLayerKapsule(initProps),
      hexedPolygonsLayer: HexedPolygonsLayerKapsule(initProps),
      pathsLayer: PathsLayerKapsule(initProps),
      tilesLayer: TilesLayerKapsule(initProps),
      particlesLayer: ParticlesLayerKapsule(initProps),
      ringsLayer: RingsLayerKapsule(initProps),
      labelsLayer: LabelsLayerKapsule(initProps),
      htmlElementsLayer: HtmlElementsLayerKapsule(initProps),
      objectsLayer: ObjectsLayerKapsule(initProps),
      customLayer: CustomLayerKapsule(initProps)
    };
    return _objectSpread2(_objectSpread2({
      tweenGroup
    }, layers2), {}, {
      layersThatNeedUpdatePov: Object.values(layers2).filter(function(l2) {
        return l2.hasOwnProperty("updatePov");
      }),
      layersThatNeedBehindGlobeChecker: Object.values(layers2).filter(function(l2) {
        return l2.hasOwnProperty("isBehindGlobe");
      }),
      destructableLayers: Object.values(layers2).filter(function(l2) {
        return l2.hasOwnProperty("_destructor");
      }),
      pausableLayers: Object.values(layers2).filter(function(l2) {
        return l2.hasOwnProperty("pauseAnimation");
      })
    });
  },
  init: function init16(threeObj, state, _ref15) {
    var _ref15$animateIn = _ref15.animateIn, animateIn = _ref15$animateIn === void 0 ? true : _ref15$animateIn, _ref15$waitForGlobeRe = _ref15.waitForGlobeReady, waitForGlobeReady = _ref15$waitForGlobeRe === void 0 ? true : _ref15$waitForGlobeRe;
    emptyObject(threeObj);
    state.scene = threeObj;
    state.scene.visible = false;
    layers.forEach(function(layer) {
      var g2 = new THREE.Group();
      state.scene.add(g2);
      state[layer](g2);
    });
    var initGlobe = function initGlobe2() {
      if (animateIn) {
        state.scene.scale.set(1e-6, 1e-6, 1e-6);
        state.tweenGroup.add(new Tween({
          k: 1e-6
        }).to({
          k: 1
        }, 600).easing(Easing.Quadratic.Out).onUpdate(function(_ref16) {
          var k2 = _ref16.k;
          return state.scene.scale.set(k2, k2, k2);
        }).start());
        var rotAxis = new THREE.Vector3(0, 1, 0);
        state.tweenGroup.add(new Tween({
          rot: Math.PI * 2
        }).to({
          rot: 0
        }, 1200).easing(Easing.Quintic.Out).onUpdate(function(_ref17) {
          var rot = _ref17.rot;
          return state.scene.setRotationFromAxisAngle(rotAxis, rot);
        }).start());
      }
      state.scene.visible = true;
      state.onGlobeReady && state.onGlobeReady();
    };
    waitForGlobeReady ? state.globeLayer.onReady(initGlobe) : initGlobe();
    this._animationCycle();
  },
  update: function update16(state) {
  }
});
function fromKapsule(kapsule) {
  var baseClass = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : Object;
  var initKapsuleWithSelf = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;
  var Globe3 = function(_baseClass) {
    function Globe4() {
      var _this;
      _classCallCheck(this, Globe4);
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      _this = _callSuper(this, Globe4, [].concat(args));
      _this.__kapsuleInstance = _construct(kapsule, [].concat(_toConsumableArray3(initKapsuleWithSelf ? [_this] : []), args));
      return _this;
    }
    _inherits(Globe4, _baseClass);
    return _createClass(Globe4);
  }(baseClass);
  Object.keys(kapsule()).forEach(function(m2) {
    return Globe3.prototype[m2] = function() {
      var _this$__kapsuleInstan;
      var returnVal = (_this$__kapsuleInstan = this.__kapsuleInstance)[m2].apply(_this$__kapsuleInstan, arguments);
      return returnVal === this.__kapsuleInstance ? this : returnVal;
    };
  });
  return Globe3;
}
var three = window.THREE ? window.THREE : {
  Group
};
var threeGlobe = fromKapsule(Globe, three.Group, true);

// node_modules/three/examples/jsm/controls/TrackballControls.js
var _changeEvent = { type: "change" };
var _startEvent = { type: "start" };
var _endEvent = { type: "end" };
var TrackballControls = class extends EventDispatcher {
  constructor(object, domElement) {
    super();
    const scope = this;
    const STATE = { NONE: -1, ROTATE: 0, ZOOM: 1, PAN: 2, TOUCH_ROTATE: 3, TOUCH_ZOOM_PAN: 4 };
    this.object = object;
    this.domElement = domElement;
    this.domElement.style.touchAction = "none";
    this.enabled = true;
    this.screen = { left: 0, top: 0, width: 0, height: 0 };
    this.rotateSpeed = 1;
    this.zoomSpeed = 1.2;
    this.panSpeed = 0.3;
    this.noRotate = false;
    this.noZoom = false;
    this.noPan = false;
    this.staticMoving = false;
    this.dynamicDampingFactor = 0.2;
    this.minDistance = 0;
    this.maxDistance = Infinity;
    this.minZoom = 0;
    this.maxZoom = Infinity;
    this.keys = [
      "KeyA",
      "KeyS",
      "KeyD"
      /*D*/
    ];
    this.mouseButtons = { LEFT: MOUSE.ROTATE, MIDDLE: MOUSE.DOLLY, RIGHT: MOUSE.PAN };
    this.target = new Vector3();
    const EPS = 1e-6;
    const lastPosition = new Vector3();
    let lastZoom = 1;
    let _state = STATE.NONE, _keyState = STATE.NONE, _touchZoomDistanceStart = 0, _touchZoomDistanceEnd = 0, _lastAngle = 0;
    const _eye = new Vector3(), _movePrev = new Vector2(), _moveCurr = new Vector2(), _lastAxis = new Vector3(), _zoomStart = new Vector2(), _zoomEnd = new Vector2(), _panStart = new Vector2(), _panEnd = new Vector2(), _pointers = [], _pointerPositions = {};
    this.target0 = this.target.clone();
    this.position0 = this.object.position.clone();
    this.up0 = this.object.up.clone();
    this.zoom0 = this.object.zoom;
    this.handleResize = function() {
      const box = scope.domElement.getBoundingClientRect();
      const d2 = scope.domElement.ownerDocument.documentElement;
      scope.screen.left = box.left + window.pageXOffset - d2.clientLeft;
      scope.screen.top = box.top + window.pageYOffset - d2.clientTop;
      scope.screen.width = box.width;
      scope.screen.height = box.height;
    };
    const getMouseOnScreen = function() {
      const vector = new Vector2();
      return function getMouseOnScreen2(pageX, pageY) {
        vector.set(
          (pageX - scope.screen.left) / scope.screen.width,
          (pageY - scope.screen.top) / scope.screen.height
        );
        return vector;
      };
    }();
    const getMouseOnCircle = function() {
      const vector = new Vector2();
      return function getMouseOnCircle2(pageX, pageY) {
        vector.set(
          (pageX - scope.screen.width * 0.5 - scope.screen.left) / (scope.screen.width * 0.5),
          (scope.screen.height + 2 * (scope.screen.top - pageY)) / scope.screen.width
          // screen.width intentional
        );
        return vector;
      };
    }();
    this.rotateCamera = function() {
      const axis = new Vector3(), quaternion = new Quaternion(), eyeDirection = new Vector3(), objectUpDirection = new Vector3(), objectSidewaysDirection = new Vector3(), moveDirection = new Vector3();
      return function rotateCamera() {
        moveDirection.set(_moveCurr.x - _movePrev.x, _moveCurr.y - _movePrev.y, 0);
        let angle = moveDirection.length();
        if (angle) {
          _eye.copy(scope.object.position).sub(scope.target);
          eyeDirection.copy(_eye).normalize();
          objectUpDirection.copy(scope.object.up).normalize();
          objectSidewaysDirection.crossVectors(objectUpDirection, eyeDirection).normalize();
          objectUpDirection.setLength(_moveCurr.y - _movePrev.y);
          objectSidewaysDirection.setLength(_moveCurr.x - _movePrev.x);
          moveDirection.copy(objectUpDirection.add(objectSidewaysDirection));
          axis.crossVectors(moveDirection, _eye).normalize();
          angle *= scope.rotateSpeed;
          quaternion.setFromAxisAngle(axis, angle);
          _eye.applyQuaternion(quaternion);
          scope.object.up.applyQuaternion(quaternion);
          _lastAxis.copy(axis);
          _lastAngle = angle;
        } else if (!scope.staticMoving && _lastAngle) {
          _lastAngle *= Math.sqrt(1 - scope.dynamicDampingFactor);
          _eye.copy(scope.object.position).sub(scope.target);
          quaternion.setFromAxisAngle(_lastAxis, _lastAngle);
          _eye.applyQuaternion(quaternion);
          scope.object.up.applyQuaternion(quaternion);
        }
        _movePrev.copy(_moveCurr);
      };
    }();
    this.zoomCamera = function() {
      let factor;
      if (_state === STATE.TOUCH_ZOOM_PAN) {
        factor = _touchZoomDistanceStart / _touchZoomDistanceEnd;
        _touchZoomDistanceStart = _touchZoomDistanceEnd;
        if (scope.object.isPerspectiveCamera) {
          _eye.multiplyScalar(factor);
        } else if (scope.object.isOrthographicCamera) {
          scope.object.zoom = MathUtils.clamp(scope.object.zoom / factor, scope.minZoom, scope.maxZoom);
          if (lastZoom !== scope.object.zoom) {
            scope.object.updateProjectionMatrix();
          }
        } else {
          console.warn("THREE.TrackballControls: Unsupported camera type");
        }
      } else {
        factor = 1 + (_zoomEnd.y - _zoomStart.y) * scope.zoomSpeed;
        if (factor !== 1 && factor > 0) {
          if (scope.object.isPerspectiveCamera) {
            _eye.multiplyScalar(factor);
          } else if (scope.object.isOrthographicCamera) {
            scope.object.zoom = MathUtils.clamp(scope.object.zoom / factor, scope.minZoom, scope.maxZoom);
            if (lastZoom !== scope.object.zoom) {
              scope.object.updateProjectionMatrix();
            }
          } else {
            console.warn("THREE.TrackballControls: Unsupported camera type");
          }
        }
        if (scope.staticMoving) {
          _zoomStart.copy(_zoomEnd);
        } else {
          _zoomStart.y += (_zoomEnd.y - _zoomStart.y) * this.dynamicDampingFactor;
        }
      }
    };
    this.panCamera = function() {
      const mouseChange = new Vector2(), objectUp = new Vector3(), pan = new Vector3();
      return function panCamera() {
        mouseChange.copy(_panEnd).sub(_panStart);
        if (mouseChange.lengthSq()) {
          if (scope.object.isOrthographicCamera) {
            const scale_x = (scope.object.right - scope.object.left) / scope.object.zoom / scope.domElement.clientWidth;
            const scale_y = (scope.object.top - scope.object.bottom) / scope.object.zoom / scope.domElement.clientWidth;
            mouseChange.x *= scale_x;
            mouseChange.y *= scale_y;
          }
          mouseChange.multiplyScalar(_eye.length() * scope.panSpeed);
          pan.copy(_eye).cross(scope.object.up).setLength(mouseChange.x);
          pan.add(objectUp.copy(scope.object.up).setLength(mouseChange.y));
          scope.object.position.add(pan);
          scope.target.add(pan);
          if (scope.staticMoving) {
            _panStart.copy(_panEnd);
          } else {
            _panStart.add(mouseChange.subVectors(_panEnd, _panStart).multiplyScalar(scope.dynamicDampingFactor));
          }
        }
      };
    }();
    this.checkDistances = function() {
      if (!scope.noZoom || !scope.noPan) {
        if (_eye.lengthSq() > scope.maxDistance * scope.maxDistance) {
          scope.object.position.addVectors(scope.target, _eye.setLength(scope.maxDistance));
          _zoomStart.copy(_zoomEnd);
        }
        if (_eye.lengthSq() < scope.minDistance * scope.minDistance) {
          scope.object.position.addVectors(scope.target, _eye.setLength(scope.minDistance));
          _zoomStart.copy(_zoomEnd);
        }
      }
    };
    this.update = function() {
      _eye.subVectors(scope.object.position, scope.target);
      if (!scope.noRotate) {
        scope.rotateCamera();
      }
      if (!scope.noZoom) {
        scope.zoomCamera();
      }
      if (!scope.noPan) {
        scope.panCamera();
      }
      scope.object.position.addVectors(scope.target, _eye);
      if (scope.object.isPerspectiveCamera) {
        scope.checkDistances();
        scope.object.lookAt(scope.target);
        if (lastPosition.distanceToSquared(scope.object.position) > EPS) {
          scope.dispatchEvent(_changeEvent);
          lastPosition.copy(scope.object.position);
        }
      } else if (scope.object.isOrthographicCamera) {
        scope.object.lookAt(scope.target);
        if (lastPosition.distanceToSquared(scope.object.position) > EPS || lastZoom !== scope.object.zoom) {
          scope.dispatchEvent(_changeEvent);
          lastPosition.copy(scope.object.position);
          lastZoom = scope.object.zoom;
        }
      } else {
        console.warn("THREE.TrackballControls: Unsupported camera type");
      }
    };
    this.reset = function() {
      _state = STATE.NONE;
      _keyState = STATE.NONE;
      scope.target.copy(scope.target0);
      scope.object.position.copy(scope.position0);
      scope.object.up.copy(scope.up0);
      scope.object.zoom = scope.zoom0;
      scope.object.updateProjectionMatrix();
      _eye.subVectors(scope.object.position, scope.target);
      scope.object.lookAt(scope.target);
      scope.dispatchEvent(_changeEvent);
      lastPosition.copy(scope.object.position);
      lastZoom = scope.object.zoom;
    };
    function onPointerDown(event) {
      if (scope.enabled === false) return;
      if (_pointers.length === 0) {
        scope.domElement.setPointerCapture(event.pointerId);
        scope.domElement.addEventListener("pointermove", onPointerMove);
        scope.domElement.addEventListener("pointerup", onPointerUp);
      }
      addPointer(event);
      if (event.pointerType === "touch") {
        onTouchStart(event);
      } else {
        onMouseDown(event);
      }
    }
    function onPointerMove(event) {
      if (scope.enabled === false) return;
      if (event.pointerType === "touch") {
        onTouchMove(event);
      } else {
        onMouseMove(event);
      }
    }
    function onPointerUp(event) {
      if (scope.enabled === false) return;
      if (event.pointerType === "touch") {
        onTouchEnd(event);
      } else {
        onMouseUp();
      }
      removePointer(event);
      if (_pointers.length === 0) {
        scope.domElement.releasePointerCapture(event.pointerId);
        scope.domElement.removeEventListener("pointermove", onPointerMove);
        scope.domElement.removeEventListener("pointerup", onPointerUp);
      }
    }
    function onPointerCancel(event) {
      removePointer(event);
    }
    function keydown(event) {
      if (scope.enabled === false) return;
      window.removeEventListener("keydown", keydown);
      if (_keyState !== STATE.NONE) {
        return;
      } else if (event.code === scope.keys[STATE.ROTATE] && !scope.noRotate) {
        _keyState = STATE.ROTATE;
      } else if (event.code === scope.keys[STATE.ZOOM] && !scope.noZoom) {
        _keyState = STATE.ZOOM;
      } else if (event.code === scope.keys[STATE.PAN] && !scope.noPan) {
        _keyState = STATE.PAN;
      }
    }
    function keyup() {
      if (scope.enabled === false) return;
      _keyState = STATE.NONE;
      window.addEventListener("keydown", keydown);
    }
    function onMouseDown(event) {
      if (_state === STATE.NONE) {
        switch (event.button) {
          case scope.mouseButtons.LEFT:
            _state = STATE.ROTATE;
            break;
          case scope.mouseButtons.MIDDLE:
            _state = STATE.ZOOM;
            break;
          case scope.mouseButtons.RIGHT:
            _state = STATE.PAN;
            break;
        }
      }
      const state = _keyState !== STATE.NONE ? _keyState : _state;
      if (state === STATE.ROTATE && !scope.noRotate) {
        _moveCurr.copy(getMouseOnCircle(event.pageX, event.pageY));
        _movePrev.copy(_moveCurr);
      } else if (state === STATE.ZOOM && !scope.noZoom) {
        _zoomStart.copy(getMouseOnScreen(event.pageX, event.pageY));
        _zoomEnd.copy(_zoomStart);
      } else if (state === STATE.PAN && !scope.noPan) {
        _panStart.copy(getMouseOnScreen(event.pageX, event.pageY));
        _panEnd.copy(_panStart);
      }
      scope.dispatchEvent(_startEvent);
    }
    function onMouseMove(event) {
      const state = _keyState !== STATE.NONE ? _keyState : _state;
      if (state === STATE.ROTATE && !scope.noRotate) {
        _movePrev.copy(_moveCurr);
        _moveCurr.copy(getMouseOnCircle(event.pageX, event.pageY));
      } else if (state === STATE.ZOOM && !scope.noZoom) {
        _zoomEnd.copy(getMouseOnScreen(event.pageX, event.pageY));
      } else if (state === STATE.PAN && !scope.noPan) {
        _panEnd.copy(getMouseOnScreen(event.pageX, event.pageY));
      }
    }
    function onMouseUp() {
      _state = STATE.NONE;
      scope.dispatchEvent(_endEvent);
    }
    function onMouseWheel(event) {
      if (scope.enabled === false) return;
      if (scope.noZoom === true) return;
      event.preventDefault();
      switch (event.deltaMode) {
        case 2:
          _zoomStart.y -= event.deltaY * 0.025;
          break;
        case 1:
          _zoomStart.y -= event.deltaY * 0.01;
          break;
        default:
          _zoomStart.y -= event.deltaY * 25e-5;
          break;
      }
      scope.dispatchEvent(_startEvent);
      scope.dispatchEvent(_endEvent);
    }
    function onTouchStart(event) {
      trackPointer(event);
      switch (_pointers.length) {
        case 1:
          _state = STATE.TOUCH_ROTATE;
          _moveCurr.copy(getMouseOnCircle(_pointers[0].pageX, _pointers[0].pageY));
          _movePrev.copy(_moveCurr);
          break;
        default:
          _state = STATE.TOUCH_ZOOM_PAN;
          const dx = _pointers[0].pageX - _pointers[1].pageX;
          const dy = _pointers[0].pageY - _pointers[1].pageY;
          _touchZoomDistanceEnd = _touchZoomDistanceStart = Math.sqrt(dx * dx + dy * dy);
          const x2 = (_pointers[0].pageX + _pointers[1].pageX) / 2;
          const y2 = (_pointers[0].pageY + _pointers[1].pageY) / 2;
          _panStart.copy(getMouseOnScreen(x2, y2));
          _panEnd.copy(_panStart);
          break;
      }
      scope.dispatchEvent(_startEvent);
    }
    function onTouchMove(event) {
      trackPointer(event);
      switch (_pointers.length) {
        case 1:
          _movePrev.copy(_moveCurr);
          _moveCurr.copy(getMouseOnCircle(event.pageX, event.pageY));
          break;
        default:
          const position = getSecondPointerPosition(event);
          const dx = event.pageX - position.x;
          const dy = event.pageY - position.y;
          _touchZoomDistanceEnd = Math.sqrt(dx * dx + dy * dy);
          const x2 = (event.pageX + position.x) / 2;
          const y2 = (event.pageY + position.y) / 2;
          _panEnd.copy(getMouseOnScreen(x2, y2));
          break;
      }
    }
    function onTouchEnd(event) {
      switch (_pointers.length) {
        case 0:
          _state = STATE.NONE;
          break;
        case 1:
          _state = STATE.TOUCH_ROTATE;
          _moveCurr.copy(getMouseOnCircle(event.pageX, event.pageY));
          _movePrev.copy(_moveCurr);
          break;
        case 2:
          _state = STATE.TOUCH_ZOOM_PAN;
          for (let i2 = 0; i2 < _pointers.length; i2++) {
            if (_pointers[i2].pointerId !== event.pointerId) {
              const position = _pointerPositions[_pointers[i2].pointerId];
              _moveCurr.copy(getMouseOnCircle(position.x, position.y));
              _movePrev.copy(_moveCurr);
              break;
            }
          }
          break;
      }
      scope.dispatchEvent(_endEvent);
    }
    function contextmenu2(event) {
      if (scope.enabled === false) return;
      event.preventDefault();
    }
    function addPointer(event) {
      _pointers.push(event);
    }
    function removePointer(event) {
      delete _pointerPositions[event.pointerId];
      for (let i2 = 0; i2 < _pointers.length; i2++) {
        if (_pointers[i2].pointerId == event.pointerId) {
          _pointers.splice(i2, 1);
          return;
        }
      }
    }
    function trackPointer(event) {
      let position = _pointerPositions[event.pointerId];
      if (position === void 0) {
        position = new Vector2();
        _pointerPositions[event.pointerId] = position;
      }
      position.set(event.pageX, event.pageY);
    }
    function getSecondPointerPosition(event) {
      const pointer = event.pointerId === _pointers[0].pointerId ? _pointers[1] : _pointers[0];
      return _pointerPositions[pointer.pointerId];
    }
    this.dispose = function() {
      scope.domElement.removeEventListener("contextmenu", contextmenu2);
      scope.domElement.removeEventListener("pointerdown", onPointerDown);
      scope.domElement.removeEventListener("pointercancel", onPointerCancel);
      scope.domElement.removeEventListener("wheel", onMouseWheel);
      scope.domElement.removeEventListener("pointermove", onPointerMove);
      scope.domElement.removeEventListener("pointerup", onPointerUp);
      window.removeEventListener("keydown", keydown);
      window.removeEventListener("keyup", keyup);
    };
    this.domElement.addEventListener("contextmenu", contextmenu2);
    this.domElement.addEventListener("pointerdown", onPointerDown);
    this.domElement.addEventListener("pointercancel", onPointerCancel);
    this.domElement.addEventListener("wheel", onMouseWheel, { passive: false });
    window.addEventListener("keydown", keydown);
    window.addEventListener("keyup", keyup);
    this.handleResize();
    this.update();
  }
};

// node_modules/three/examples/jsm/controls/FlyControls.js
var _changeEvent2 = { type: "change" };
var FlyControls = class extends EventDispatcher {
  constructor(object, domElement) {
    super();
    this.object = object;
    this.domElement = domElement;
    this.movementSpeed = 1;
    this.rollSpeed = 5e-3;
    this.dragToLook = false;
    this.autoForward = false;
    const scope = this;
    const EPS = 1e-6;
    const lastQuaternion = new Quaternion();
    const lastPosition = new Vector3();
    this.tmpQuaternion = new Quaternion();
    this.status = 0;
    this.moveState = { up: 0, down: 0, left: 0, right: 0, forward: 0, back: 0, pitchUp: 0, pitchDown: 0, yawLeft: 0, yawRight: 0, rollLeft: 0, rollRight: 0 };
    this.moveVector = new Vector3(0, 0, 0);
    this.rotationVector = new Vector3(0, 0, 0);
    this.keydown = function(event) {
      if (event.altKey) {
        return;
      }
      switch (event.code) {
        case "ShiftLeft":
        case "ShiftRight":
          this.movementSpeedMultiplier = 0.1;
          break;
        case "KeyW":
          this.moveState.forward = 1;
          break;
        case "KeyS":
          this.moveState.back = 1;
          break;
        case "KeyA":
          this.moveState.left = 1;
          break;
        case "KeyD":
          this.moveState.right = 1;
          break;
        case "KeyR":
          this.moveState.up = 1;
          break;
        case "KeyF":
          this.moveState.down = 1;
          break;
        case "ArrowUp":
          this.moveState.pitchUp = 1;
          break;
        case "ArrowDown":
          this.moveState.pitchDown = 1;
          break;
        case "ArrowLeft":
          this.moveState.yawLeft = 1;
          break;
        case "ArrowRight":
          this.moveState.yawRight = 1;
          break;
        case "KeyQ":
          this.moveState.rollLeft = 1;
          break;
        case "KeyE":
          this.moveState.rollRight = 1;
          break;
      }
      this.updateMovementVector();
      this.updateRotationVector();
    };
    this.keyup = function(event) {
      switch (event.code) {
        case "ShiftLeft":
        case "ShiftRight":
          this.movementSpeedMultiplier = 1;
          break;
        case "KeyW":
          this.moveState.forward = 0;
          break;
        case "KeyS":
          this.moveState.back = 0;
          break;
        case "KeyA":
          this.moveState.left = 0;
          break;
        case "KeyD":
          this.moveState.right = 0;
          break;
        case "KeyR":
          this.moveState.up = 0;
          break;
        case "KeyF":
          this.moveState.down = 0;
          break;
        case "ArrowUp":
          this.moveState.pitchUp = 0;
          break;
        case "ArrowDown":
          this.moveState.pitchDown = 0;
          break;
        case "ArrowLeft":
          this.moveState.yawLeft = 0;
          break;
        case "ArrowRight":
          this.moveState.yawRight = 0;
          break;
        case "KeyQ":
          this.moveState.rollLeft = 0;
          break;
        case "KeyE":
          this.moveState.rollRight = 0;
          break;
      }
      this.updateMovementVector();
      this.updateRotationVector();
    };
    this.pointerdown = function(event) {
      if (this.dragToLook) {
        this.status++;
      } else {
        switch (event.button) {
          case 0:
            this.moveState.forward = 1;
            break;
          case 2:
            this.moveState.back = 1;
            break;
        }
        this.updateMovementVector();
      }
    };
    this.pointermove = function(event) {
      if (!this.dragToLook || this.status > 0) {
        const container = this.getContainerDimensions();
        const halfWidth = container.size[0] / 2;
        const halfHeight = container.size[1] / 2;
        this.moveState.yawLeft = -(event.pageX - container.offset[0] - halfWidth) / halfWidth;
        this.moveState.pitchDown = (event.pageY - container.offset[1] - halfHeight) / halfHeight;
        this.updateRotationVector();
      }
    };
    this.pointerup = function(event) {
      if (this.dragToLook) {
        this.status--;
        this.moveState.yawLeft = this.moveState.pitchDown = 0;
      } else {
        switch (event.button) {
          case 0:
            this.moveState.forward = 0;
            break;
          case 2:
            this.moveState.back = 0;
            break;
        }
        this.updateMovementVector();
      }
      this.updateRotationVector();
    };
    this.update = function(delta) {
      const moveMult = delta * scope.movementSpeed;
      const rotMult = delta * scope.rollSpeed;
      scope.object.translateX(scope.moveVector.x * moveMult);
      scope.object.translateY(scope.moveVector.y * moveMult);
      scope.object.translateZ(scope.moveVector.z * moveMult);
      scope.tmpQuaternion.set(scope.rotationVector.x * rotMult, scope.rotationVector.y * rotMult, scope.rotationVector.z * rotMult, 1).normalize();
      scope.object.quaternion.multiply(scope.tmpQuaternion);
      if (lastPosition.distanceToSquared(scope.object.position) > EPS || 8 * (1 - lastQuaternion.dot(scope.object.quaternion)) > EPS) {
        scope.dispatchEvent(_changeEvent2);
        lastQuaternion.copy(scope.object.quaternion);
        lastPosition.copy(scope.object.position);
      }
    };
    this.updateMovementVector = function() {
      const forward = this.moveState.forward || this.autoForward && !this.moveState.back ? 1 : 0;
      this.moveVector.x = -this.moveState.left + this.moveState.right;
      this.moveVector.y = -this.moveState.down + this.moveState.up;
      this.moveVector.z = -forward + this.moveState.back;
    };
    this.updateRotationVector = function() {
      this.rotationVector.x = -this.moveState.pitchDown + this.moveState.pitchUp;
      this.rotationVector.y = -this.moveState.yawRight + this.moveState.yawLeft;
      this.rotationVector.z = -this.moveState.rollRight + this.moveState.rollLeft;
    };
    this.getContainerDimensions = function() {
      if (this.domElement != document) {
        return {
          size: [this.domElement.offsetWidth, this.domElement.offsetHeight],
          offset: [this.domElement.offsetLeft, this.domElement.offsetTop]
        };
      } else {
        return {
          size: [window.innerWidth, window.innerHeight],
          offset: [0, 0]
        };
      }
    };
    this.dispose = function() {
      this.domElement.removeEventListener("contextmenu", contextmenu);
      this.domElement.removeEventListener("pointerdown", _pointerdown);
      this.domElement.removeEventListener("pointermove", _pointermove);
      this.domElement.removeEventListener("pointerup", _pointerup);
      window.removeEventListener("keydown", _keydown);
      window.removeEventListener("keyup", _keyup);
    };
    const _pointermove = this.pointermove.bind(this);
    const _pointerdown = this.pointerdown.bind(this);
    const _pointerup = this.pointerup.bind(this);
    const _keydown = this.keydown.bind(this);
    const _keyup = this.keyup.bind(this);
    this.domElement.addEventListener("contextmenu", contextmenu);
    this.domElement.addEventListener("pointerdown", _pointerdown);
    this.domElement.addEventListener("pointermove", _pointermove);
    this.domElement.addEventListener("pointerup", _pointerup);
    window.addEventListener("keydown", _keydown);
    window.addEventListener("keyup", _keyup);
    this.updateMovementVector();
    this.updateRotationVector();
  }
};
function contextmenu(event) {
  event.preventDefault();
}

// node_modules/three/examples/jsm/shaders/CopyShader.js
var CopyShader = {
  uniforms: {
    "tDiffuse": { value: null },
    "opacity": { value: 1 }
  },
  vertexShader: (
    /* glsl */
    `

		varying vec2 vUv;

		void main() {

			vUv = uv;
			gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );

		}`
  ),
  fragmentShader: (
    /* glsl */
    `

		uniform float opacity;

		uniform sampler2D tDiffuse;

		varying vec2 vUv;

		void main() {

			gl_FragColor = texture2D( tDiffuse, vUv );
			gl_FragColor.a *= opacity;


		}`
  )
};

// node_modules/three/examples/jsm/postprocessing/Pass.js
var Pass = class {
  constructor() {
    this.isPass = true;
    this.enabled = true;
    this.needsSwap = true;
    this.clear = false;
    this.renderToScreen = false;
  }
  setSize() {
  }
  render() {
    console.error("THREE.Pass: .render() must be implemented in derived pass.");
  }
  dispose() {
  }
};
var _camera = new OrthographicCamera(-1, 1, 1, -1, 0, 1);
var _geometry = new BufferGeometry();
_geometry.setAttribute("position", new Float32BufferAttribute([-1, 3, 0, -1, -1, 0, 3, -1, 0], 3));
_geometry.setAttribute("uv", new Float32BufferAttribute([0, 2, 0, 0, 2, 0], 2));
var FullScreenQuad = class {
  constructor(material) {
    this._mesh = new Mesh(_geometry, material);
  }
  dispose() {
    this._mesh.geometry.dispose();
  }
  render(renderer3) {
    renderer3.render(this._mesh, _camera);
  }
  get material() {
    return this._mesh.material;
  }
  set material(value) {
    this._mesh.material = value;
  }
};

// node_modules/three/examples/jsm/postprocessing/ShaderPass.js
var ShaderPass = class extends Pass {
  constructor(shader, textureID) {
    super();
    this.textureID = textureID !== void 0 ? textureID : "tDiffuse";
    if (shader instanceof ShaderMaterial) {
      this.uniforms = shader.uniforms;
      this.material = shader;
    } else if (shader) {
      this.uniforms = UniformsUtils.clone(shader.uniforms);
      this.material = new ShaderMaterial({
        defines: Object.assign({}, shader.defines),
        uniforms: this.uniforms,
        vertexShader: shader.vertexShader,
        fragmentShader: shader.fragmentShader
      });
    }
    this.fsQuad = new FullScreenQuad(this.material);
  }
  render(renderer3, writeBuffer, readBuffer) {
    if (this.uniforms[this.textureID]) {
      this.uniforms[this.textureID].value = readBuffer.texture;
    }
    this.fsQuad.material = this.material;
    if (this.renderToScreen) {
      renderer3.setRenderTarget(null);
      this.fsQuad.render(renderer3);
    } else {
      renderer3.setRenderTarget(writeBuffer);
      if (this.clear) renderer3.clear(renderer3.autoClearColor, renderer3.autoClearDepth, renderer3.autoClearStencil);
      this.fsQuad.render(renderer3);
    }
  }
  dispose() {
    this.material.dispose();
    this.fsQuad.dispose();
  }
};

// node_modules/three/examples/jsm/postprocessing/MaskPass.js
var MaskPass = class extends Pass {
  constructor(scene3, camera3) {
    super();
    this.scene = scene3;
    this.camera = camera3;
    this.clear = true;
    this.needsSwap = false;
    this.inverse = false;
  }
  render(renderer3, writeBuffer, readBuffer) {
    const context = renderer3.getContext();
    const state = renderer3.state;
    state.buffers.color.setMask(false);
    state.buffers.depth.setMask(false);
    state.buffers.color.setLocked(true);
    state.buffers.depth.setLocked(true);
    let writeValue, clearValue;
    if (this.inverse) {
      writeValue = 0;
      clearValue = 1;
    } else {
      writeValue = 1;
      clearValue = 0;
    }
    state.buffers.stencil.setTest(true);
    state.buffers.stencil.setOp(context.REPLACE, context.REPLACE, context.REPLACE);
    state.buffers.stencil.setFunc(context.ALWAYS, writeValue, 4294967295);
    state.buffers.stencil.setClear(clearValue);
    state.buffers.stencil.setLocked(true);
    renderer3.setRenderTarget(readBuffer);
    if (this.clear) renderer3.clear();
    renderer3.render(this.scene, this.camera);
    renderer3.setRenderTarget(writeBuffer);
    if (this.clear) renderer3.clear();
    renderer3.render(this.scene, this.camera);
    state.buffers.color.setLocked(false);
    state.buffers.depth.setLocked(false);
    state.buffers.stencil.setLocked(false);
    state.buffers.stencil.setFunc(context.EQUAL, 1, 4294967295);
    state.buffers.stencil.setOp(context.KEEP, context.KEEP, context.KEEP);
    state.buffers.stencil.setLocked(true);
  }
};
var ClearMaskPass = class extends Pass {
  constructor() {
    super();
    this.needsSwap = false;
  }
  render(renderer3) {
    renderer3.state.buffers.stencil.setLocked(false);
    renderer3.state.buffers.stencil.setTest(false);
  }
};

// node_modules/three/examples/jsm/postprocessing/EffectComposer.js
var EffectComposer = class {
  constructor(renderer3, renderTarget) {
    this.renderer = renderer3;
    this._pixelRatio = renderer3.getPixelRatio();
    if (renderTarget === void 0) {
      const size = renderer3.getSize(new Vector2());
      this._width = size.width;
      this._height = size.height;
      renderTarget = new WebGLRenderTarget(this._width * this._pixelRatio, this._height * this._pixelRatio);
      renderTarget.texture.name = "EffectComposer.rt1";
    } else {
      this._width = renderTarget.width;
      this._height = renderTarget.height;
    }
    this.renderTarget1 = renderTarget;
    this.renderTarget2 = renderTarget.clone();
    this.renderTarget2.texture.name = "EffectComposer.rt2";
    this.writeBuffer = this.renderTarget1;
    this.readBuffer = this.renderTarget2;
    this.renderToScreen = true;
    this.passes = [];
    this.copyPass = new ShaderPass(CopyShader);
    this.clock = new Clock();
  }
  swapBuffers() {
    const tmp = this.readBuffer;
    this.readBuffer = this.writeBuffer;
    this.writeBuffer = tmp;
  }
  addPass(pass) {
    this.passes.push(pass);
    pass.setSize(this._width * this._pixelRatio, this._height * this._pixelRatio);
  }
  insertPass(pass, index6) {
    this.passes.splice(index6, 0, pass);
    pass.setSize(this._width * this._pixelRatio, this._height * this._pixelRatio);
  }
  removePass(pass) {
    const index6 = this.passes.indexOf(pass);
    if (index6 !== -1) {
      this.passes.splice(index6, 1);
    }
  }
  isLastEnabledPass(passIndex) {
    for (let i2 = passIndex + 1; i2 < this.passes.length; i2++) {
      if (this.passes[i2].enabled) {
        return false;
      }
    }
    return true;
  }
  render(deltaTime) {
    if (deltaTime === void 0) {
      deltaTime = this.clock.getDelta();
    }
    const currentRenderTarget = this.renderer.getRenderTarget();
    let maskActive = false;
    for (let i2 = 0, il = this.passes.length; i2 < il; i2++) {
      const pass = this.passes[i2];
      if (pass.enabled === false) continue;
      pass.renderToScreen = this.renderToScreen && this.isLastEnabledPass(i2);
      pass.render(this.renderer, this.writeBuffer, this.readBuffer, deltaTime, maskActive);
      if (pass.needsSwap) {
        if (maskActive) {
          const context = this.renderer.getContext();
          const stencil = this.renderer.state.buffers.stencil;
          stencil.setFunc(context.NOTEQUAL, 1, 4294967295);
          this.copyPass.render(this.renderer, this.writeBuffer, this.readBuffer, deltaTime);
          stencil.setFunc(context.EQUAL, 1, 4294967295);
        }
        this.swapBuffers();
      }
      if (MaskPass !== void 0) {
        if (pass instanceof MaskPass) {
          maskActive = true;
        } else if (pass instanceof ClearMaskPass) {
          maskActive = false;
        }
      }
    }
    this.renderer.setRenderTarget(currentRenderTarget);
  }
  reset(renderTarget) {
    if (renderTarget === void 0) {
      const size = this.renderer.getSize(new Vector2());
      this._pixelRatio = this.renderer.getPixelRatio();
      this._width = size.width;
      this._height = size.height;
      renderTarget = this.renderTarget1.clone();
      renderTarget.setSize(this._width * this._pixelRatio, this._height * this._pixelRatio);
    }
    this.renderTarget1.dispose();
    this.renderTarget2.dispose();
    this.renderTarget1 = renderTarget;
    this.renderTarget2 = renderTarget.clone();
    this.writeBuffer = this.renderTarget1;
    this.readBuffer = this.renderTarget2;
  }
  setSize(width, height) {
    this._width = width;
    this._height = height;
    const effectiveWidth = this._width * this._pixelRatio;
    const effectiveHeight = this._height * this._pixelRatio;
    this.renderTarget1.setSize(effectiveWidth, effectiveHeight);
    this.renderTarget2.setSize(effectiveWidth, effectiveHeight);
    for (let i2 = 0; i2 < this.passes.length; i2++) {
      this.passes[i2].setSize(effectiveWidth, effectiveHeight);
    }
  }
  setPixelRatio(pixelRatio) {
    this._pixelRatio = pixelRatio;
    this.setSize(this._width, this._height);
  }
  dispose() {
    this.renderTarget1.dispose();
    this.renderTarget2.dispose();
    this.copyPass.dispose();
  }
};

// node_modules/three/examples/jsm/postprocessing/RenderPass.js
var RenderPass = class extends Pass {
  constructor(scene3, camera3, overrideMaterial, clearColor, clearAlpha) {
    super();
    this.scene = scene3;
    this.camera = camera3;
    this.overrideMaterial = overrideMaterial;
    this.clearColor = clearColor;
    this.clearAlpha = clearAlpha !== void 0 ? clearAlpha : 0;
    this.clear = true;
    this.clearDepth = false;
    this.needsSwap = false;
    this._oldClearColor = new Color();
  }
  render(renderer3, writeBuffer, readBuffer) {
    const oldAutoClear = renderer3.autoClear;
    renderer3.autoClear = false;
    let oldClearAlpha, oldOverrideMaterial;
    if (this.overrideMaterial !== void 0) {
      oldOverrideMaterial = this.scene.overrideMaterial;
      this.scene.overrideMaterial = this.overrideMaterial;
    }
    if (this.clearColor) {
      renderer3.getClearColor(this._oldClearColor);
      oldClearAlpha = renderer3.getClearAlpha();
      renderer3.setClearColor(this.clearColor, this.clearAlpha);
    }
    if (this.clearDepth) {
      renderer3.clearDepth();
    }
    renderer3.setRenderTarget(this.renderToScreen ? null : readBuffer);
    if (this.clear) renderer3.clear(renderer3.autoClearColor, renderer3.autoClearDepth, renderer3.autoClearStencil);
    renderer3.render(this.scene, this.camera);
    if (this.clearColor) {
      renderer3.setClearColor(this._oldClearColor, oldClearAlpha);
    }
    if (this.overrideMaterial !== void 0) {
      this.scene.overrideMaterial = oldOverrideMaterial;
    }
    renderer3.autoClear = oldAutoClear;
  }
};

// node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js
function _getPrototypeOf2(t2) {
  return _getPrototypeOf2 = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(t3) {
    return t3.__proto__ || Object.getPrototypeOf(t3);
  }, _getPrototypeOf2(t2);
}

// node_modules/@babel/runtime/helpers/esm/isNativeFunction.js
function _isNativeFunction(t2) {
  try {
    return -1 !== Function.toString.call(t2).indexOf("[native code]");
  } catch (n2) {
    return "function" == typeof t2;
  }
}

// node_modules/@babel/runtime/helpers/esm/isNativeReflectConstruct.js
function _isNativeReflectConstruct2() {
  try {
    var t2 = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
    }));
  } catch (t3) {
  }
  return (_isNativeReflectConstruct2 = function _isNativeReflectConstruct3() {
    return !!t2;
  })();
}

// node_modules/@babel/runtime/helpers/esm/construct.js
function _construct2(t2, e2, r2) {
  if (_isNativeReflectConstruct2()) return Reflect.construct.apply(null, arguments);
  var o2 = [null];
  o2.push.apply(o2, e2);
  var p2 = new (t2.bind.apply(t2, o2))();
  return r2 && _setPrototypeOf(p2, r2.prototype), p2;
}

// node_modules/@babel/runtime/helpers/esm/wrapNativeSuper.js
function _wrapNativeSuper(t2) {
  var r2 = "function" == typeof Map ? /* @__PURE__ */ new Map() : void 0;
  return _wrapNativeSuper = function _wrapNativeSuper2(t3) {
    if (null === t3 || !_isNativeFunction(t3)) return t3;
    if ("function" != typeof t3) throw new TypeError("Super expression must either be null or a function");
    if (void 0 !== r2) {
      if (r2.has(t3)) return r2.get(t3);
      r2.set(t3, Wrapper);
    }
    function Wrapper() {
      return _construct2(t3, arguments, _getPrototypeOf2(this).constructor);
    }
    return Wrapper.prototype = Object.create(t3.prototype, {
      constructor: {
        value: Wrapper,
        enumerable: false,
        writable: true,
        configurable: true
      }
    }), _setPrototypeOf(Wrapper, t3);
  }, _wrapNativeSuper(t2);
}

// node_modules/polished/dist/polished.esm.js
var ERRORS = {
  "1": "Passed invalid arguments to hsl, please pass multiple numbers e.g. hsl(360, 0.75, 0.4) or an object e.g. rgb({ hue: 255, saturation: 0.4, lightness: 0.75 }).\n\n",
  "2": "Passed invalid arguments to hsla, please pass multiple numbers e.g. hsla(360, 0.75, 0.4, 0.7) or an object e.g. rgb({ hue: 255, saturation: 0.4, lightness: 0.75, alpha: 0.7 }).\n\n",
  "3": "Passed an incorrect argument to a color function, please pass a string representation of a color.\n\n",
  "4": "Couldn't generate valid rgb string from %s, it returned %s.\n\n",
  "5": "Couldn't parse the color string. Please provide the color as a string in hex, rgb, rgba, hsl or hsla notation.\n\n",
  "6": "Passed invalid arguments to rgb, please pass multiple numbers e.g. rgb(255, 205, 100) or an object e.g. rgb({ red: 255, green: 205, blue: 100 }).\n\n",
  "7": "Passed invalid arguments to rgba, please pass multiple numbers e.g. rgb(255, 205, 100, 0.75) or an object e.g. rgb({ red: 255, green: 205, blue: 100, alpha: 0.75 }).\n\n",
  "8": "Passed invalid argument to toColorString, please pass a RgbColor, RgbaColor, HslColor or HslaColor object.\n\n",
  "9": "Please provide a number of steps to the modularScale helper.\n\n",
  "10": "Please pass a number or one of the predefined scales to the modularScale helper as the ratio.\n\n",
  "11": 'Invalid value passed as base to modularScale, expected number or em string but got "%s"\n\n',
  "12": 'Expected a string ending in "px" or a number passed as the first argument to %s(), got "%s" instead.\n\n',
  "13": 'Expected a string ending in "px" or a number passed as the second argument to %s(), got "%s" instead.\n\n',
  "14": 'Passed invalid pixel value ("%s") to %s(), please pass a value like "12px" or 12.\n\n',
  "15": 'Passed invalid base value ("%s") to %s(), please pass a value like "12px" or 12.\n\n',
  "16": "You must provide a template to this method.\n\n",
  "17": "You passed an unsupported selector state to this method.\n\n",
  "18": "minScreen and maxScreen must be provided as stringified numbers with the same units.\n\n",
  "19": "fromSize and toSize must be provided as stringified numbers with the same units.\n\n",
  "20": "expects either an array of objects or a single object with the properties prop, fromSize, and toSize.\n\n",
  "21": "expects the objects in the first argument array to have the properties `prop`, `fromSize`, and `toSize`.\n\n",
  "22": "expects the first argument object to have the properties `prop`, `fromSize`, and `toSize`.\n\n",
  "23": "fontFace expects a name of a font-family.\n\n",
  "24": "fontFace expects either the path to the font file(s) or a name of a local copy.\n\n",
  "25": "fontFace expects localFonts to be an array.\n\n",
  "26": "fontFace expects fileFormats to be an array.\n\n",
  "27": "radialGradient requries at least 2 color-stops to properly render.\n\n",
  "28": "Please supply a filename to retinaImage() as the first argument.\n\n",
  "29": "Passed invalid argument to triangle, please pass correct pointingDirection e.g. 'right'.\n\n",
  "30": "Passed an invalid value to `height` or `width`. Please provide a pixel based unit.\n\n",
  "31": "The animation shorthand only takes 8 arguments. See the specification for more information: http://mdn.io/animation\n\n",
  "32": "To pass multiple animations please supply them in arrays, e.g. animation(['rotate', '2s'], ['move', '1s'])\nTo pass a single animation please supply them in simple values, e.g. animation('rotate', '2s')\n\n",
  "33": "The animation shorthand arrays can only have 8 elements. See the specification for more information: http://mdn.io/animation\n\n",
  "34": "borderRadius expects a radius value as a string or number as the second argument.\n\n",
  "35": 'borderRadius expects one of "top", "bottom", "left" or "right" as the first argument.\n\n',
  "36": "Property must be a string value.\n\n",
  "37": "Syntax Error at %s.\n\n",
  "38": "Formula contains a function that needs parentheses at %s.\n\n",
  "39": "Formula is missing closing parenthesis at %s.\n\n",
  "40": "Formula has too many closing parentheses at %s.\n\n",
  "41": "All values in a formula must have the same unit or be unitless.\n\n",
  "42": "Please provide a number of steps to the modularScale helper.\n\n",
  "43": "Please pass a number or one of the predefined scales to the modularScale helper as the ratio.\n\n",
  "44": "Invalid value passed as base to modularScale, expected number or em/rem string but got %s.\n\n",
  "45": "Passed invalid argument to hslToColorString, please pass a HslColor or HslaColor object.\n\n",
  "46": "Passed invalid argument to rgbToColorString, please pass a RgbColor or RgbaColor object.\n\n",
  "47": "minScreen and maxScreen must be provided as stringified numbers with the same units.\n\n",
  "48": "fromSize and toSize must be provided as stringified numbers with the same units.\n\n",
  "49": "Expects either an array of objects or a single object with the properties prop, fromSize, and toSize.\n\n",
  "50": "Expects the objects in the first argument array to have the properties prop, fromSize, and toSize.\n\n",
  "51": "Expects the first argument object to have the properties prop, fromSize, and toSize.\n\n",
  "52": "fontFace expects either the path to the font file(s) or a name of a local copy.\n\n",
  "53": "fontFace expects localFonts to be an array.\n\n",
  "54": "fontFace expects fileFormats to be an array.\n\n",
  "55": "fontFace expects a name of a font-family.\n\n",
  "56": "linearGradient requries at least 2 color-stops to properly render.\n\n",
  "57": "radialGradient requries at least 2 color-stops to properly render.\n\n",
  "58": "Please supply a filename to retinaImage() as the first argument.\n\n",
  "59": "Passed invalid argument to triangle, please pass correct pointingDirection e.g. 'right'.\n\n",
  "60": "Passed an invalid value to `height` or `width`. Please provide a pixel based unit.\n\n",
  "61": "Property must be a string value.\n\n",
  "62": "borderRadius expects a radius value as a string or number as the second argument.\n\n",
  "63": 'borderRadius expects one of "top", "bottom", "left" or "right" as the first argument.\n\n',
  "64": "The animation shorthand only takes 8 arguments. See the specification for more information: http://mdn.io/animation.\n\n",
  "65": "To pass multiple animations please supply them in arrays, e.g. animation(['rotate', '2s'], ['move', '1s'])\\nTo pass a single animation please supply them in simple values, e.g. animation('rotate', '2s').\n\n",
  "66": "The animation shorthand arrays can only have 8 elements. See the specification for more information: http://mdn.io/animation.\n\n",
  "67": "You must provide a template to this method.\n\n",
  "68": "You passed an unsupported selector state to this method.\n\n",
  "69": 'Expected a string ending in "px" or a number passed as the first argument to %s(), got %s instead.\n\n',
  "70": 'Expected a string ending in "px" or a number passed as the second argument to %s(), got %s instead.\n\n',
  "71": 'Passed invalid pixel value %s to %s(), please pass a value like "12px" or 12.\n\n',
  "72": 'Passed invalid base value %s to %s(), please pass a value like "12px" or 12.\n\n',
  "73": "Please provide a valid CSS variable.\n\n",
  "74": "CSS variable not found and no default was provided.\n\n",
  "75": "important requires a valid style object, got a %s instead.\n\n",
  "76": "fromSize and toSize must be provided as stringified numbers with the same units as minScreen and maxScreen.\n\n",
  "77": 'remToPx expects a value in "rem" but you provided it in "%s".\n\n',
  "78": 'base must be set in "px" or "%" but you set it in "%s".\n'
};
function format() {
  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
    args[_key] = arguments[_key];
  }
  var a2 = args[0];
  var b = [];
  var c2;
  for (c2 = 1; c2 < args.length; c2 += 1) {
    b.push(args[c2]);
  }
  b.forEach(function(d2) {
    a2 = a2.replace(/%[a-z]/, d2);
  });
  return a2;
}
var PolishedError = function(_Error) {
  _inheritsLoose(PolishedError2, _Error);
  function PolishedError2(code) {
    var _this;
    if (false) {
      _this = _Error.call(this, "An error occurred. See https://github.com/styled-components/polished/blob/main/src/internalHelpers/errors.md#" + code + " for more information.") || this;
    } else {
      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {
        args[_key2 - 1] = arguments[_key2];
      }
      _this = _Error.call(this, format.apply(void 0, [ERRORS[code]].concat(args))) || this;
    }
    return _assertThisInitialized(_this);
  }
  return PolishedError2;
}(_wrapNativeSuper(Error));
function endsWith(string, suffix) {
  return string.substr(-suffix.length) === suffix;
}
var cssRegex$1 = /^([+-]?(?:\d+|\d*\.\d+))([a-z]*|%)$/;
function stripUnit(value) {
  if (typeof value !== "string") return value;
  var matchedValue = value.match(cssRegex$1);
  return matchedValue ? parseFloat(value) : value;
}
var pxtoFactory = function pxtoFactory2(to) {
  return function(pxval, base) {
    if (base === void 0) {
      base = "16px";
    }
    var newPxval = pxval;
    var newBase = base;
    if (typeof pxval === "string") {
      if (!endsWith(pxval, "px")) {
        throw new PolishedError(69, to, pxval);
      }
      newPxval = stripUnit(pxval);
    }
    if (typeof base === "string") {
      if (!endsWith(base, "px")) {
        throw new PolishedError(70, to, base);
      }
      newBase = stripUnit(base);
    }
    if (typeof newPxval === "string") {
      throw new PolishedError(71, pxval, to);
    }
    if (typeof newBase === "string") {
      throw new PolishedError(72, base, to);
    }
    return "" + newPxval / newBase + to;
  };
};
var pixelsto = pxtoFactory;
var em = pixelsto("em");
var rem = pixelsto("rem");
function colorToInt(color2) {
  return Math.round(color2 * 255);
}
function convertToInt(red, green, blue) {
  return colorToInt(red) + "," + colorToInt(green) + "," + colorToInt(blue);
}
function hslToRgb(hue, saturation, lightness, convert) {
  if (convert === void 0) {
    convert = convertToInt;
  }
  if (saturation === 0) {
    return convert(lightness, lightness, lightness);
  }
  var huePrime = (hue % 360 + 360) % 360 / 60;
  var chroma = (1 - Math.abs(2 * lightness - 1)) * saturation;
  var secondComponent = chroma * (1 - Math.abs(huePrime % 2 - 1));
  var red = 0;
  var green = 0;
  var blue = 0;
  if (huePrime >= 0 && huePrime < 1) {
    red = chroma;
    green = secondComponent;
  } else if (huePrime >= 1 && huePrime < 2) {
    red = secondComponent;
    green = chroma;
  } else if (huePrime >= 2 && huePrime < 3) {
    green = chroma;
    blue = secondComponent;
  } else if (huePrime >= 3 && huePrime < 4) {
    green = secondComponent;
    blue = chroma;
  } else if (huePrime >= 4 && huePrime < 5) {
    red = secondComponent;
    blue = chroma;
  } else if (huePrime >= 5 && huePrime < 6) {
    red = chroma;
    blue = secondComponent;
  }
  var lightnessModification = lightness - chroma / 2;
  var finalRed = red + lightnessModification;
  var finalGreen = green + lightnessModification;
  var finalBlue = blue + lightnessModification;
  return convert(finalRed, finalGreen, finalBlue);
}
var namedColorMap = {
  aliceblue: "f0f8ff",
  antiquewhite: "faebd7",
  aqua: "00ffff",
  aquamarine: "7fffd4",
  azure: "f0ffff",
  beige: "f5f5dc",
  bisque: "ffe4c4",
  black: "000",
  blanchedalmond: "ffebcd",
  blue: "0000ff",
  blueviolet: "8a2be2",
  brown: "a52a2a",
  burlywood: "deb887",
  cadetblue: "5f9ea0",
  chartreuse: "7fff00",
  chocolate: "d2691e",
  coral: "ff7f50",
  cornflowerblue: "6495ed",
  cornsilk: "fff8dc",
  crimson: "dc143c",
  cyan: "00ffff",
  darkblue: "00008b",
  darkcyan: "008b8b",
  darkgoldenrod: "b8860b",
  darkgray: "a9a9a9",
  darkgreen: "006400",
  darkgrey: "a9a9a9",
  darkkhaki: "bdb76b",
  darkmagenta: "8b008b",
  darkolivegreen: "556b2f",
  darkorange: "ff8c00",
  darkorchid: "9932cc",
  darkred: "8b0000",
  darksalmon: "e9967a",
  darkseagreen: "8fbc8f",
  darkslateblue: "483d8b",
  darkslategray: "2f4f4f",
  darkslategrey: "2f4f4f",
  darkturquoise: "00ced1",
  darkviolet: "9400d3",
  deeppink: "ff1493",
  deepskyblue: "00bfff",
  dimgray: "696969",
  dimgrey: "696969",
  dodgerblue: "1e90ff",
  firebrick: "b22222",
  floralwhite: "fffaf0",
  forestgreen: "228b22",
  fuchsia: "ff00ff",
  gainsboro: "dcdcdc",
  ghostwhite: "f8f8ff",
  gold: "ffd700",
  goldenrod: "daa520",
  gray: "808080",
  green: "008000",
  greenyellow: "adff2f",
  grey: "808080",
  honeydew: "f0fff0",
  hotpink: "ff69b4",
  indianred: "cd5c5c",
  indigo: "4b0082",
  ivory: "fffff0",
  khaki: "f0e68c",
  lavender: "e6e6fa",
  lavenderblush: "fff0f5",
  lawngreen: "7cfc00",
  lemonchiffon: "fffacd",
  lightblue: "add8e6",
  lightcoral: "f08080",
  lightcyan: "e0ffff",
  lightgoldenrodyellow: "fafad2",
  lightgray: "d3d3d3",
  lightgreen: "90ee90",
  lightgrey: "d3d3d3",
  lightpink: "ffb6c1",
  lightsalmon: "ffa07a",
  lightseagreen: "20b2aa",
  lightskyblue: "87cefa",
  lightslategray: "789",
  lightslategrey: "789",
  lightsteelblue: "b0c4de",
  lightyellow: "ffffe0",
  lime: "0f0",
  limegreen: "32cd32",
  linen: "faf0e6",
  magenta: "f0f",
  maroon: "800000",
  mediumaquamarine: "66cdaa",
  mediumblue: "0000cd",
  mediumorchid: "ba55d3",
  mediumpurple: "9370db",
  mediumseagreen: "3cb371",
  mediumslateblue: "7b68ee",
  mediumspringgreen: "00fa9a",
  mediumturquoise: "48d1cc",
  mediumvioletred: "c71585",
  midnightblue: "191970",
  mintcream: "f5fffa",
  mistyrose: "ffe4e1",
  moccasin: "ffe4b5",
  navajowhite: "ffdead",
  navy: "000080",
  oldlace: "fdf5e6",
  olive: "808000",
  olivedrab: "6b8e23",
  orange: "ffa500",
  orangered: "ff4500",
  orchid: "da70d6",
  palegoldenrod: "eee8aa",
  palegreen: "98fb98",
  paleturquoise: "afeeee",
  palevioletred: "db7093",
  papayawhip: "ffefd5",
  peachpuff: "ffdab9",
  peru: "cd853f",
  pink: "ffc0cb",
  plum: "dda0dd",
  powderblue: "b0e0e6",
  purple: "800080",
  rebeccapurple: "639",
  red: "f00",
  rosybrown: "bc8f8f",
  royalblue: "4169e1",
  saddlebrown: "8b4513",
  salmon: "fa8072",
  sandybrown: "f4a460",
  seagreen: "2e8b57",
  seashell: "fff5ee",
  sienna: "a0522d",
  silver: "c0c0c0",
  skyblue: "87ceeb",
  slateblue: "6a5acd",
  slategray: "708090",
  slategrey: "708090",
  snow: "fffafa",
  springgreen: "00ff7f",
  steelblue: "4682b4",
  tan: "d2b48c",
  teal: "008080",
  thistle: "d8bfd8",
  tomato: "ff6347",
  turquoise: "40e0d0",
  violet: "ee82ee",
  wheat: "f5deb3",
  white: "fff",
  whitesmoke: "f5f5f5",
  yellow: "ff0",
  yellowgreen: "9acd32"
};
function nameToHex(color2) {
  if (typeof color2 !== "string") return color2;
  var normalizedColorName = color2.toLowerCase();
  return namedColorMap[normalizedColorName] ? "#" + namedColorMap[normalizedColorName] : color2;
}
var hexRegex = /^#[a-fA-F0-9]{6}$/;
var hexRgbaRegex = /^#[a-fA-F0-9]{8}$/;
var reducedHexRegex = /^#[a-fA-F0-9]{3}$/;
var reducedRgbaHexRegex = /^#[a-fA-F0-9]{4}$/;
var rgbRegex = /^rgb\(\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*\)$/i;
var rgbaRegex = /^rgb(?:a)?\(\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,|\/)\s*([-+]?\d*[.]?\d+[%]?)\s*\)$/i;
var hslRegex = /^hsl\(\s*(\d{0,3}[.]?[0-9]+(?:deg)?)\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*\)$/i;
var hslaRegex = /^hsl(?:a)?\(\s*(\d{0,3}[.]?[0-9]+(?:deg)?)\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,|\/)\s*([-+]?\d*[.]?\d+[%]?)\s*\)$/i;
function parseToRgb(color2) {
  if (typeof color2 !== "string") {
    throw new PolishedError(3);
  }
  var normalizedColor = nameToHex(color2);
  if (normalizedColor.match(hexRegex)) {
    return {
      red: parseInt("" + normalizedColor[1] + normalizedColor[2], 16),
      green: parseInt("" + normalizedColor[3] + normalizedColor[4], 16),
      blue: parseInt("" + normalizedColor[5] + normalizedColor[6], 16)
    };
  }
  if (normalizedColor.match(hexRgbaRegex)) {
    var alpha = parseFloat((parseInt("" + normalizedColor[7] + normalizedColor[8], 16) / 255).toFixed(2));
    return {
      red: parseInt("" + normalizedColor[1] + normalizedColor[2], 16),
      green: parseInt("" + normalizedColor[3] + normalizedColor[4], 16),
      blue: parseInt("" + normalizedColor[5] + normalizedColor[6], 16),
      alpha
    };
  }
  if (normalizedColor.match(reducedHexRegex)) {
    return {
      red: parseInt("" + normalizedColor[1] + normalizedColor[1], 16),
      green: parseInt("" + normalizedColor[2] + normalizedColor[2], 16),
      blue: parseInt("" + normalizedColor[3] + normalizedColor[3], 16)
    };
  }
  if (normalizedColor.match(reducedRgbaHexRegex)) {
    var _alpha = parseFloat((parseInt("" + normalizedColor[4] + normalizedColor[4], 16) / 255).toFixed(2));
    return {
      red: parseInt("" + normalizedColor[1] + normalizedColor[1], 16),
      green: parseInt("" + normalizedColor[2] + normalizedColor[2], 16),
      blue: parseInt("" + normalizedColor[3] + normalizedColor[3], 16),
      alpha: _alpha
    };
  }
  var rgbMatched = rgbRegex.exec(normalizedColor);
  if (rgbMatched) {
    return {
      red: parseInt("" + rgbMatched[1], 10),
      green: parseInt("" + rgbMatched[2], 10),
      blue: parseInt("" + rgbMatched[3], 10)
    };
  }
  var rgbaMatched = rgbaRegex.exec(normalizedColor.substring(0, 50));
  if (rgbaMatched) {
    return {
      red: parseInt("" + rgbaMatched[1], 10),
      green: parseInt("" + rgbaMatched[2], 10),
      blue: parseInt("" + rgbaMatched[3], 10),
      alpha: parseFloat("" + rgbaMatched[4]) > 1 ? parseFloat("" + rgbaMatched[4]) / 100 : parseFloat("" + rgbaMatched[4])
    };
  }
  var hslMatched = hslRegex.exec(normalizedColor);
  if (hslMatched) {
    var hue = parseInt("" + hslMatched[1], 10);
    var saturation = parseInt("" + hslMatched[2], 10) / 100;
    var lightness = parseInt("" + hslMatched[3], 10) / 100;
    var rgbColorString = "rgb(" + hslToRgb(hue, saturation, lightness) + ")";
    var hslRgbMatched = rgbRegex.exec(rgbColorString);
    if (!hslRgbMatched) {
      throw new PolishedError(4, normalizedColor, rgbColorString);
    }
    return {
      red: parseInt("" + hslRgbMatched[1], 10),
      green: parseInt("" + hslRgbMatched[2], 10),
      blue: parseInt("" + hslRgbMatched[3], 10)
    };
  }
  var hslaMatched = hslaRegex.exec(normalizedColor.substring(0, 50));
  if (hslaMatched) {
    var _hue = parseInt("" + hslaMatched[1], 10);
    var _saturation = parseInt("" + hslaMatched[2], 10) / 100;
    var _lightness = parseInt("" + hslaMatched[3], 10) / 100;
    var _rgbColorString = "rgb(" + hslToRgb(_hue, _saturation, _lightness) + ")";
    var _hslRgbMatched = rgbRegex.exec(_rgbColorString);
    if (!_hslRgbMatched) {
      throw new PolishedError(4, normalizedColor, _rgbColorString);
    }
    return {
      red: parseInt("" + _hslRgbMatched[1], 10),
      green: parseInt("" + _hslRgbMatched[2], 10),
      blue: parseInt("" + _hslRgbMatched[3], 10),
      alpha: parseFloat("" + hslaMatched[4]) > 1 ? parseFloat("" + hslaMatched[4]) / 100 : parseFloat("" + hslaMatched[4])
    };
  }
  throw new PolishedError(5);
}
function rgbToHsl(color2) {
  var red = color2.red / 255;
  var green = color2.green / 255;
  var blue = color2.blue / 255;
  var max2 = Math.max(red, green, blue);
  var min = Math.min(red, green, blue);
  var lightness = (max2 + min) / 2;
  if (max2 === min) {
    if (color2.alpha !== void 0) {
      return {
        hue: 0,
        saturation: 0,
        lightness,
        alpha: color2.alpha
      };
    } else {
      return {
        hue: 0,
        saturation: 0,
        lightness
      };
    }
  }
  var hue;
  var delta = max2 - min;
  var saturation = lightness > 0.5 ? delta / (2 - max2 - min) : delta / (max2 + min);
  switch (max2) {
    case red:
      hue = (green - blue) / delta + (green < blue ? 6 : 0);
      break;
    case green:
      hue = (blue - red) / delta + 2;
      break;
    default:
      hue = (red - green) / delta + 4;
      break;
  }
  hue *= 60;
  if (color2.alpha !== void 0) {
    return {
      hue,
      saturation,
      lightness,
      alpha: color2.alpha
    };
  }
  return {
    hue,
    saturation,
    lightness
  };
}
function parseToHsl(color2) {
  return rgbToHsl(parseToRgb(color2));
}
var reduceHexValue = function reduceHexValue2(value) {
  if (value.length === 7 && value[1] === value[2] && value[3] === value[4] && value[5] === value[6]) {
    return "#" + value[1] + value[3] + value[5];
  }
  return value;
};
var reduceHexValue$1 = reduceHexValue;
function numberToHex(value) {
  var hex = value.toString(16);
  return hex.length === 1 ? "0" + hex : hex;
}
function colorToHex(color2) {
  return numberToHex(Math.round(color2 * 255));
}
function convertToHex(red, green, blue) {
  return reduceHexValue$1("#" + colorToHex(red) + colorToHex(green) + colorToHex(blue));
}
function hslToHex(hue, saturation, lightness) {
  return hslToRgb(hue, saturation, lightness, convertToHex);
}
function hsl(value, saturation, lightness) {
  if (typeof value === "number" && typeof saturation === "number" && typeof lightness === "number") {
    return hslToHex(value, saturation, lightness);
  } else if (typeof value === "object" && saturation === void 0 && lightness === void 0) {
    return hslToHex(value.hue, value.saturation, value.lightness);
  }
  throw new PolishedError(1);
}
function hsla(value, saturation, lightness, alpha) {
  if (typeof value === "number" && typeof saturation === "number" && typeof lightness === "number" && typeof alpha === "number") {
    return alpha >= 1 ? hslToHex(value, saturation, lightness) : "rgba(" + hslToRgb(value, saturation, lightness) + "," + alpha + ")";
  } else if (typeof value === "object" && saturation === void 0 && lightness === void 0 && alpha === void 0) {
    return value.alpha >= 1 ? hslToHex(value.hue, value.saturation, value.lightness) : "rgba(" + hslToRgb(value.hue, value.saturation, value.lightness) + "," + value.alpha + ")";
  }
  throw new PolishedError(2);
}
function rgb(value, green, blue) {
  if (typeof value === "number" && typeof green === "number" && typeof blue === "number") {
    return reduceHexValue$1("#" + numberToHex(value) + numberToHex(green) + numberToHex(blue));
  } else if (typeof value === "object" && green === void 0 && blue === void 0) {
    return reduceHexValue$1("#" + numberToHex(value.red) + numberToHex(value.green) + numberToHex(value.blue));
  }
  throw new PolishedError(6);
}
function rgba(firstValue, secondValue, thirdValue, fourthValue) {
  if (typeof firstValue === "string" && typeof secondValue === "number") {
    var rgbValue = parseToRgb(firstValue);
    return "rgba(" + rgbValue.red + "," + rgbValue.green + "," + rgbValue.blue + "," + secondValue + ")";
  } else if (typeof firstValue === "number" && typeof secondValue === "number" && typeof thirdValue === "number" && typeof fourthValue === "number") {
    return fourthValue >= 1 ? rgb(firstValue, secondValue, thirdValue) : "rgba(" + firstValue + "," + secondValue + "," + thirdValue + "," + fourthValue + ")";
  } else if (typeof firstValue === "object" && secondValue === void 0 && thirdValue === void 0 && fourthValue === void 0) {
    return firstValue.alpha >= 1 ? rgb(firstValue.red, firstValue.green, firstValue.blue) : "rgba(" + firstValue.red + "," + firstValue.green + "," + firstValue.blue + "," + firstValue.alpha + ")";
  }
  throw new PolishedError(7);
}
var isRgb = function isRgb2(color2) {
  return typeof color2.red === "number" && typeof color2.green === "number" && typeof color2.blue === "number" && (typeof color2.alpha !== "number" || typeof color2.alpha === "undefined");
};
var isRgba = function isRgba2(color2) {
  return typeof color2.red === "number" && typeof color2.green === "number" && typeof color2.blue === "number" && typeof color2.alpha === "number";
};
var isHsl = function isHsl2(color2) {
  return typeof color2.hue === "number" && typeof color2.saturation === "number" && typeof color2.lightness === "number" && (typeof color2.alpha !== "number" || typeof color2.alpha === "undefined");
};
var isHsla = function isHsla2(color2) {
  return typeof color2.hue === "number" && typeof color2.saturation === "number" && typeof color2.lightness === "number" && typeof color2.alpha === "number";
};
function toColorString(color2) {
  if (typeof color2 !== "object") throw new PolishedError(8);
  if (isRgba(color2)) return rgba(color2);
  if (isRgb(color2)) return rgb(color2);
  if (isHsla(color2)) return hsla(color2);
  if (isHsl(color2)) return hsl(color2);
  throw new PolishedError(8);
}
function curried(f2, length, acc) {
  return function fn() {
    var combined = acc.concat(Array.prototype.slice.call(arguments));
    return combined.length >= length ? f2.apply(this, combined) : curried(f2, length, combined);
  };
}
function curry(f2) {
  return curried(f2, f2.length, []);
}
function adjustHue(degree, color2) {
  if (color2 === "transparent") return color2;
  var hslColor = parseToHsl(color2);
  return toColorString(_extends({}, hslColor, {
    hue: hslColor.hue + parseFloat(degree)
  }));
}
var curriedAdjustHue = curry(adjustHue);
function guard(lowerBoundary, upperBoundary, value) {
  return Math.max(lowerBoundary, Math.min(upperBoundary, value));
}
function darken(amount, color2) {
  if (color2 === "transparent") return color2;
  var hslColor = parseToHsl(color2);
  return toColorString(_extends({}, hslColor, {
    lightness: guard(0, 1, hslColor.lightness - parseFloat(amount))
  }));
}
var curriedDarken = curry(darken);
function desaturate(amount, color2) {
  if (color2 === "transparent") return color2;
  var hslColor = parseToHsl(color2);
  return toColorString(_extends({}, hslColor, {
    saturation: guard(0, 1, hslColor.saturation - parseFloat(amount))
  }));
}
var curriedDesaturate = curry(desaturate);
function lighten(amount, color2) {
  if (color2 === "transparent") return color2;
  var hslColor = parseToHsl(color2);
  return toColorString(_extends({}, hslColor, {
    lightness: guard(0, 1, hslColor.lightness + parseFloat(amount))
  }));
}
var curriedLighten = curry(lighten);
function mix(weight, color2, otherColor) {
  if (color2 === "transparent") return otherColor;
  if (otherColor === "transparent") return color2;
  if (weight === 0) return otherColor;
  var parsedColor1 = parseToRgb(color2);
  var color1 = _extends({}, parsedColor1, {
    alpha: typeof parsedColor1.alpha === "number" ? parsedColor1.alpha : 1
  });
  var parsedColor2 = parseToRgb(otherColor);
  var color22 = _extends({}, parsedColor2, {
    alpha: typeof parsedColor2.alpha === "number" ? parsedColor2.alpha : 1
  });
  var alphaDelta = color1.alpha - color22.alpha;
  var x2 = parseFloat(weight) * 2 - 1;
  var y2 = x2 * alphaDelta === -1 ? x2 : x2 + alphaDelta;
  var z2 = 1 + x2 * alphaDelta;
  var weight1 = (y2 / z2 + 1) / 2;
  var weight2 = 1 - weight1;
  var mixedColor = {
    red: Math.floor(color1.red * weight1 + color22.red * weight2),
    green: Math.floor(color1.green * weight1 + color22.green * weight2),
    blue: Math.floor(color1.blue * weight1 + color22.blue * weight2),
    alpha: color1.alpha * parseFloat(weight) + color22.alpha * (1 - parseFloat(weight))
  };
  return rgba(mixedColor);
}
var curriedMix = curry(mix);
var mix$1 = curriedMix;
function opacify(amount, color2) {
  if (color2 === "transparent") return color2;
  var parsedColor = parseToRgb(color2);
  var alpha = typeof parsedColor.alpha === "number" ? parsedColor.alpha : 1;
  var colorWithAlpha = _extends({}, parsedColor, {
    alpha: guard(0, 1, (alpha * 100 + parseFloat(amount) * 100) / 100)
  });
  return rgba(colorWithAlpha);
}
var curriedOpacify = curry(opacify);
var curriedOpacify$1 = curriedOpacify;
function saturate(amount, color2) {
  if (color2 === "transparent") return color2;
  var hslColor = parseToHsl(color2);
  return toColorString(_extends({}, hslColor, {
    saturation: guard(0, 1, hslColor.saturation + parseFloat(amount))
  }));
}
var curriedSaturate = curry(saturate);
function setHue(hue, color2) {
  if (color2 === "transparent") return color2;
  return toColorString(_extends({}, parseToHsl(color2), {
    hue: parseFloat(hue)
  }));
}
var curriedSetHue = curry(setHue);
function setLightness(lightness, color2) {
  if (color2 === "transparent") return color2;
  return toColorString(_extends({}, parseToHsl(color2), {
    lightness: parseFloat(lightness)
  }));
}
var curriedSetLightness = curry(setLightness);
function setSaturation(saturation, color2) {
  if (color2 === "transparent") return color2;
  return toColorString(_extends({}, parseToHsl(color2), {
    saturation: parseFloat(saturation)
  }));
}
var curriedSetSaturation = curry(setSaturation);
function shade(percentage, color2) {
  if (color2 === "transparent") return color2;
  return mix$1(parseFloat(percentage), "rgb(0, 0, 0)", color2);
}
var curriedShade = curry(shade);
function tint(percentage, color2) {
  if (color2 === "transparent") return color2;
  return mix$1(parseFloat(percentage), "rgb(255, 255, 255)", color2);
}
var curriedTint = curry(tint);
function transparentize(amount, color2) {
  if (color2 === "transparent") return color2;
  var parsedColor = parseToRgb(color2);
  var alpha = typeof parsedColor.alpha === "number" ? parsedColor.alpha : 1;
  var colorWithAlpha = _extends({}, parsedColor, {
    alpha: guard(0, 1, +(alpha * 100 - parseFloat(amount) * 100).toFixed(2) / 100)
  });
  return rgba(colorWithAlpha);
}
var curriedTransparentize = curry(transparentize);

// node_modules/preact/dist/preact.module.js
var n;
var l;
var u;
var t;
var i;
var r;
var o;
var e;
var f;
var c;
var s;
var a;
var h;
var p = {};
var v = [];
var y = /acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;
var w = Array.isArray;
function d(n2, l2) {
  for (var u2 in l2) n2[u2] = l2[u2];
  return n2;
}
function g(n2) {
  n2 && n2.parentNode && n2.parentNode.removeChild(n2);
}
function _(l2, u2, t2) {
  var i2, r2, o2, e2 = {};
  for (o2 in u2) "key" == o2 ? i2 = u2[o2] : "ref" == o2 ? r2 = u2[o2] : e2[o2] = u2[o2];
  if (arguments.length > 2 && (e2.children = arguments.length > 3 ? n.call(arguments, 2) : t2), "function" == typeof l2 && null != l2.defaultProps) for (o2 in l2.defaultProps) void 0 === e2[o2] && (e2[o2] = l2.defaultProps[o2]);
  return m(l2, e2, i2, r2, null);
}
function m(n2, t2, i2, r2, o2) {
  var e2 = { type: n2, props: t2, key: i2, ref: r2, __k: null, __: null, __b: 0, __e: null, __c: null, constructor: void 0, __v: null == o2 ? ++u : o2, __i: -1, __u: 0 };
  return null == o2 && null != l.vnode && l.vnode(e2), e2;
}
function k(n2) {
  return n2.children;
}
function x(n2, l2) {
  this.props = n2, this.context = l2;
}
function S(n2, l2) {
  if (null == l2) return n2.__ ? S(n2.__, n2.__i + 1) : null;
  for (var u2; l2 < n2.__k.length; l2++) if (null != (u2 = n2.__k[l2]) && null != u2.__e) return u2.__e;
  return "function" == typeof n2.type ? S(n2) : null;
}
function C(n2) {
  var l2, u2;
  if (null != (n2 = n2.__) && null != n2.__c) {
    for (n2.__e = n2.__c.base = null, l2 = 0; l2 < n2.__k.length; l2++) if (null != (u2 = n2.__k[l2]) && null != u2.__e) {
      n2.__e = n2.__c.base = u2.__e;
      break;
    }
    return C(n2);
  }
}
function M(n2) {
  (!n2.__d && (n2.__d = true) && i.push(n2) && !$.__r++ || r != l.debounceRendering) && ((r = l.debounceRendering) || o)($);
}
function $() {
  for (var n2, u2, t2, r2, o2, f2, c2, s2 = 1; i.length; ) i.length > s2 && i.sort(e), n2 = i.shift(), s2 = i.length, n2.__d && (t2 = void 0, o2 = (r2 = (u2 = n2).__v).__e, f2 = [], c2 = [], u2.__P && ((t2 = d({}, r2)).__v = r2.__v + 1, l.vnode && l.vnode(t2), O(u2.__P, t2, r2, u2.__n, u2.__P.namespaceURI, 32 & r2.__u ? [o2] : null, f2, null == o2 ? S(r2) : o2, !!(32 & r2.__u), c2), t2.__v = r2.__v, t2.__.__k[t2.__i] = t2, z(f2, t2, c2), t2.__e != o2 && C(t2)));
  $.__r = 0;
}
function I(n2, l2, u2, t2, i2, r2, o2, e2, f2, c2, s2) {
  var a2, h2, y2, w2, d2, g2, _2 = t2 && t2.__k || v, m2 = l2.length;
  for (f2 = P(u2, l2, _2, f2, m2), a2 = 0; a2 < m2; a2++) null != (y2 = u2.__k[a2]) && (h2 = -1 == y2.__i ? p : _2[y2.__i] || p, y2.__i = a2, g2 = O(n2, y2, h2, i2, r2, o2, e2, f2, c2, s2), w2 = y2.__e, y2.ref && h2.ref != y2.ref && (h2.ref && q(h2.ref, null, y2), s2.push(y2.ref, y2.__c || w2, y2)), null == d2 && null != w2 && (d2 = w2), 4 & y2.__u || h2.__k === y2.__k ? f2 = A(y2, f2, n2) : "function" == typeof y2.type && void 0 !== g2 ? f2 = g2 : w2 && (f2 = w2.nextSibling), y2.__u &= -7);
  return u2.__e = d2, f2;
}
function P(n2, l2, u2, t2, i2) {
  var r2, o2, e2, f2, c2, s2 = u2.length, a2 = s2, h2 = 0;
  for (n2.__k = new Array(i2), r2 = 0; r2 < i2; r2++) null != (o2 = l2[r2]) && "boolean" != typeof o2 && "function" != typeof o2 ? (f2 = r2 + h2, (o2 = n2.__k[r2] = "string" == typeof o2 || "number" == typeof o2 || "bigint" == typeof o2 || o2.constructor == String ? m(null, o2, null, null, null) : w(o2) ? m(k, { children: o2 }, null, null, null) : null == o2.constructor && o2.__b > 0 ? m(o2.type, o2.props, o2.key, o2.ref ? o2.ref : null, o2.__v) : o2).__ = n2, o2.__b = n2.__b + 1, e2 = null, -1 != (c2 = o2.__i = L(o2, u2, f2, a2)) && (a2--, (e2 = u2[c2]) && (e2.__u |= 2)), null == e2 || null == e2.__v ? (-1 == c2 && (i2 > s2 ? h2-- : i2 < s2 && h2++), "function" != typeof o2.type && (o2.__u |= 4)) : c2 != f2 && (c2 == f2 - 1 ? h2-- : c2 == f2 + 1 ? h2++ : (c2 > f2 ? h2-- : h2++, o2.__u |= 4))) : n2.__k[r2] = null;
  if (a2) for (r2 = 0; r2 < s2; r2++) null != (e2 = u2[r2]) && 0 == (2 & e2.__u) && (e2.__e == t2 && (t2 = S(e2)), B(e2, e2));
  return t2;
}
function A(n2, l2, u2) {
  var t2, i2;
  if ("function" == typeof n2.type) {
    for (t2 = n2.__k, i2 = 0; t2 && i2 < t2.length; i2++) t2[i2] && (t2[i2].__ = n2, l2 = A(t2[i2], l2, u2));
    return l2;
  }
  n2.__e != l2 && (l2 && n2.type && !u2.contains(l2) && (l2 = S(n2)), u2.insertBefore(n2.__e, l2 || null), l2 = n2.__e);
  do {
    l2 = l2 && l2.nextSibling;
  } while (null != l2 && 8 == l2.nodeType);
  return l2;
}
function L(n2, l2, u2, t2) {
  var i2, r2, o2 = n2.key, e2 = n2.type, f2 = l2[u2];
  if (null === f2 && null == n2.key || f2 && o2 == f2.key && e2 == f2.type && 0 == (2 & f2.__u)) return u2;
  if (t2 > (null != f2 && 0 == (2 & f2.__u) ? 1 : 0)) for (i2 = u2 - 1, r2 = u2 + 1; i2 >= 0 || r2 < l2.length; ) {
    if (i2 >= 0) {
      if ((f2 = l2[i2]) && 0 == (2 & f2.__u) && o2 == f2.key && e2 == f2.type) return i2;
      i2--;
    }
    if (r2 < l2.length) {
      if ((f2 = l2[r2]) && 0 == (2 & f2.__u) && o2 == f2.key && e2 == f2.type) return r2;
      r2++;
    }
  }
  return -1;
}
function T(n2, l2, u2) {
  "-" == l2[0] ? n2.setProperty(l2, null == u2 ? "" : u2) : n2[l2] = null == u2 ? "" : "number" != typeof u2 || y.test(l2) ? u2 : u2 + "px";
}
function j(n2, l2, u2, t2, i2) {
  var r2, o2;
  n: if ("style" == l2) if ("string" == typeof u2) n2.style.cssText = u2;
  else {
    if ("string" == typeof t2 && (n2.style.cssText = t2 = ""), t2) for (l2 in t2) u2 && l2 in u2 || T(n2.style, l2, "");
    if (u2) for (l2 in u2) t2 && u2[l2] == t2[l2] || T(n2.style, l2, u2[l2]);
  }
  else if ("o" == l2[0] && "n" == l2[1]) r2 = l2 != (l2 = l2.replace(f, "$1")), o2 = l2.toLowerCase(), l2 = o2 in n2 || "onFocusOut" == l2 || "onFocusIn" == l2 ? o2.slice(2) : l2.slice(2), n2.l || (n2.l = {}), n2.l[l2 + r2] = u2, u2 ? t2 ? u2.u = t2.u : (u2.u = c, n2.addEventListener(l2, r2 ? a : s, r2)) : n2.removeEventListener(l2, r2 ? a : s, r2);
  else {
    if ("http://www.w3.org/2000/svg" == i2) l2 = l2.replace(/xlink(H|:h)/, "h").replace(/sName$/, "s");
    else if ("width" != l2 && "height" != l2 && "href" != l2 && "list" != l2 && "form" != l2 && "tabIndex" != l2 && "download" != l2 && "rowSpan" != l2 && "colSpan" != l2 && "role" != l2 && "popover" != l2 && l2 in n2) try {
      n2[l2] = null == u2 ? "" : u2;
      break n;
    } catch (n3) {
    }
    "function" == typeof u2 || (null == u2 || false === u2 && "-" != l2[4] ? n2.removeAttribute(l2) : n2.setAttribute(l2, "popover" == l2 && 1 == u2 ? "" : u2));
  }
}
function F(n2) {
  return function(u2) {
    if (this.l) {
      var t2 = this.l[u2.type + n2];
      if (null == u2.t) u2.t = c++;
      else if (u2.t < t2.u) return;
      return t2(l.event ? l.event(u2) : u2);
    }
  };
}
function O(n2, u2, t2, i2, r2, o2, e2, f2, c2, s2) {
  var a2, h2, p2, v2, y2, _2, m2, b, S2, C2, M2, $2, P2, A2, H, L2, T2, j2 = u2.type;
  if (null != u2.constructor) return null;
  128 & t2.__u && (c2 = !!(32 & t2.__u), o2 = [f2 = u2.__e = t2.__e]), (a2 = l.__b) && a2(u2);
  n: if ("function" == typeof j2) try {
    if (b = u2.props, S2 = "prototype" in j2 && j2.prototype.render, C2 = (a2 = j2.contextType) && i2[a2.__c], M2 = a2 ? C2 ? C2.props.value : a2.__ : i2, t2.__c ? m2 = (h2 = u2.__c = t2.__c).__ = h2.__E : (S2 ? u2.__c = h2 = new j2(b, M2) : (u2.__c = h2 = new x(b, M2), h2.constructor = j2, h2.render = D), C2 && C2.sub(h2), h2.props = b, h2.state || (h2.state = {}), h2.context = M2, h2.__n = i2, p2 = h2.__d = true, h2.__h = [], h2._sb = []), S2 && null == h2.__s && (h2.__s = h2.state), S2 && null != j2.getDerivedStateFromProps && (h2.__s == h2.state && (h2.__s = d({}, h2.__s)), d(h2.__s, j2.getDerivedStateFromProps(b, h2.__s))), v2 = h2.props, y2 = h2.state, h2.__v = u2, p2) S2 && null == j2.getDerivedStateFromProps && null != h2.componentWillMount && h2.componentWillMount(), S2 && null != h2.componentDidMount && h2.__h.push(h2.componentDidMount);
    else {
      if (S2 && null == j2.getDerivedStateFromProps && b !== v2 && null != h2.componentWillReceiveProps && h2.componentWillReceiveProps(b, M2), !h2.__e && null != h2.shouldComponentUpdate && false === h2.shouldComponentUpdate(b, h2.__s, M2) || u2.__v == t2.__v) {
        for (u2.__v != t2.__v && (h2.props = b, h2.state = h2.__s, h2.__d = false), u2.__e = t2.__e, u2.__k = t2.__k, u2.__k.some(function(n3) {
          n3 && (n3.__ = u2);
        }), $2 = 0; $2 < h2._sb.length; $2++) h2.__h.push(h2._sb[$2]);
        h2._sb = [], h2.__h.length && e2.push(h2);
        break n;
      }
      null != h2.componentWillUpdate && h2.componentWillUpdate(b, h2.__s, M2), S2 && null != h2.componentDidUpdate && h2.__h.push(function() {
        h2.componentDidUpdate(v2, y2, _2);
      });
    }
    if (h2.context = M2, h2.props = b, h2.__P = n2, h2.__e = false, P2 = l.__r, A2 = 0, S2) {
      for (h2.state = h2.__s, h2.__d = false, P2 && P2(u2), a2 = h2.render(h2.props, h2.state, h2.context), H = 0; H < h2._sb.length; H++) h2.__h.push(h2._sb[H]);
      h2._sb = [];
    } else do {
      h2.__d = false, P2 && P2(u2), a2 = h2.render(h2.props, h2.state, h2.context), h2.state = h2.__s;
    } while (h2.__d && ++A2 < 25);
    h2.state = h2.__s, null != h2.getChildContext && (i2 = d(d({}, i2), h2.getChildContext())), S2 && !p2 && null != h2.getSnapshotBeforeUpdate && (_2 = h2.getSnapshotBeforeUpdate(v2, y2)), L2 = a2, null != a2 && a2.type === k && null == a2.key && (L2 = N(a2.props.children)), f2 = I(n2, w(L2) ? L2 : [L2], u2, t2, i2, r2, o2, e2, f2, c2, s2), h2.base = u2.__e, u2.__u &= -161, h2.__h.length && e2.push(h2), m2 && (h2.__E = h2.__ = null);
  } catch (n3) {
    if (u2.__v = null, c2 || null != o2) if (n3.then) {
      for (u2.__u |= c2 ? 160 : 128; f2 && 8 == f2.nodeType && f2.nextSibling; ) f2 = f2.nextSibling;
      o2[o2.indexOf(f2)] = null, u2.__e = f2;
    } else for (T2 = o2.length; T2--; ) g(o2[T2]);
    else u2.__e = t2.__e, u2.__k = t2.__k;
    l.__e(n3, u2, t2);
  }
  else null == o2 && u2.__v == t2.__v ? (u2.__k = t2.__k, u2.__e = t2.__e) : f2 = u2.__e = V(t2.__e, u2, t2, i2, r2, o2, e2, c2, s2);
  return (a2 = l.diffed) && a2(u2), 128 & u2.__u ? void 0 : f2;
}
function z(n2, u2, t2) {
  for (var i2 = 0; i2 < t2.length; i2++) q(t2[i2], t2[++i2], t2[++i2]);
  l.__c && l.__c(u2, n2), n2.some(function(u3) {
    try {
      n2 = u3.__h, u3.__h = [], n2.some(function(n3) {
        n3.call(u3);
      });
    } catch (n3) {
      l.__e(n3, u3.__v);
    }
  });
}
function N(n2) {
  return "object" != typeof n2 || null == n2 || n2.__b && n2.__b > 0 ? n2 : w(n2) ? n2.map(N) : d({}, n2);
}
function V(u2, t2, i2, r2, o2, e2, f2, c2, s2) {
  var a2, h2, v2, y2, d2, _2, m2, b = i2.props, k2 = t2.props, x2 = t2.type;
  if ("svg" == x2 ? o2 = "http://www.w3.org/2000/svg" : "math" == x2 ? o2 = "http://www.w3.org/1998/Math/MathML" : o2 || (o2 = "http://www.w3.org/1999/xhtml"), null != e2) {
    for (a2 = 0; a2 < e2.length; a2++) if ((d2 = e2[a2]) && "setAttribute" in d2 == !!x2 && (x2 ? d2.localName == x2 : 3 == d2.nodeType)) {
      u2 = d2, e2[a2] = null;
      break;
    }
  }
  if (null == u2) {
    if (null == x2) return document.createTextNode(k2);
    u2 = document.createElementNS(o2, x2, k2.is && k2), c2 && (l.__m && l.__m(t2, e2), c2 = false), e2 = null;
  }
  if (null == x2) b === k2 || c2 && u2.data == k2 || (u2.data = k2);
  else {
    if (e2 = e2 && n.call(u2.childNodes), b = i2.props || p, !c2 && null != e2) for (b = {}, a2 = 0; a2 < u2.attributes.length; a2++) b[(d2 = u2.attributes[a2]).name] = d2.value;
    for (a2 in b) if (d2 = b[a2], "children" == a2) ;
    else if ("dangerouslySetInnerHTML" == a2) v2 = d2;
    else if (!(a2 in k2)) {
      if ("value" == a2 && "defaultValue" in k2 || "checked" == a2 && "defaultChecked" in k2) continue;
      j(u2, a2, null, d2, o2);
    }
    for (a2 in k2) d2 = k2[a2], "children" == a2 ? y2 = d2 : "dangerouslySetInnerHTML" == a2 ? h2 = d2 : "value" == a2 ? _2 = d2 : "checked" == a2 ? m2 = d2 : c2 && "function" != typeof d2 || b[a2] === d2 || j(u2, a2, d2, b[a2], o2);
    if (h2) c2 || v2 && (h2.__html == v2.__html || h2.__html == u2.innerHTML) || (u2.innerHTML = h2.__html), t2.__k = [];
    else if (v2 && (u2.innerHTML = ""), I("template" == t2.type ? u2.content : u2, w(y2) ? y2 : [y2], t2, i2, r2, "foreignObject" == x2 ? "http://www.w3.org/1999/xhtml" : o2, e2, f2, e2 ? e2[0] : i2.__k && S(i2, 0), c2, s2), null != e2) for (a2 = e2.length; a2--; ) g(e2[a2]);
    c2 || (a2 = "value", "progress" == x2 && null == _2 ? u2.removeAttribute("value") : null != _2 && (_2 !== u2[a2] || "progress" == x2 && !_2 || "option" == x2 && _2 != b[a2]) && j(u2, a2, _2, b[a2], o2), a2 = "checked", null != m2 && m2 != u2[a2] && j(u2, a2, m2, b[a2], o2));
  }
  return u2;
}
function q(n2, u2, t2) {
  try {
    if ("function" == typeof n2) {
      var i2 = "function" == typeof n2.__u;
      i2 && n2.__u(), i2 && null == u2 || (n2.__u = n2(u2));
    } else n2.current = u2;
  } catch (n3) {
    l.__e(n3, t2);
  }
}
function B(n2, u2, t2) {
  var i2, r2;
  if (l.unmount && l.unmount(n2), (i2 = n2.ref) && (i2.current && i2.current != n2.__e || q(i2, null, u2)), null != (i2 = n2.__c)) {
    if (i2.componentWillUnmount) try {
      i2.componentWillUnmount();
    } catch (n3) {
      l.__e(n3, u2);
    }
    i2.base = i2.__P = null;
  }
  if (i2 = n2.__k) for (r2 = 0; r2 < i2.length; r2++) i2[r2] && B(i2[r2], u2, t2 || "function" != typeof n2.type);
  t2 || g(n2.__e), n2.__c = n2.__ = n2.__e = void 0;
}
function D(n2, l2, u2) {
  return this.constructor(n2, u2);
}
function E(u2, t2, i2) {
  var r2, o2, e2, f2;
  t2 == document && (t2 = document.documentElement), l.__ && l.__(u2, t2), o2 = (r2 = "function" == typeof i2) ? null : i2 && i2.__k || t2.__k, e2 = [], f2 = [], O(t2, u2 = (!r2 && i2 || t2).__k = _(k, null, [u2]), o2 || p, p, t2.namespaceURI, !r2 && i2 ? [i2] : o2 ? null : t2.firstChild ? n.call(t2.childNodes) : null, e2, !r2 && i2 ? i2 : o2 ? o2.__e : t2.firstChild, r2, f2), z(e2, u2, f2);
}
function J(l2, u2, t2) {
  var i2, r2, o2, e2, f2 = d({}, l2.props);
  for (o2 in l2.type && l2.type.defaultProps && (e2 = l2.type.defaultProps), u2) "key" == o2 ? i2 = u2[o2] : "ref" == o2 ? r2 = u2[o2] : f2[o2] = void 0 === u2[o2] && null != e2 ? e2[o2] : u2[o2];
  return arguments.length > 2 && (f2.children = arguments.length > 3 ? n.call(arguments, 2) : t2), m(l2.type, f2, i2 || l2.key, r2 || l2.ref, null);
}
n = v.slice, l = { __e: function(n2, l2, u2, t2) {
  for (var i2, r2, o2; l2 = l2.__; ) if ((i2 = l2.__c) && !i2.__) try {
    if ((r2 = i2.constructor) && null != r2.getDerivedStateFromError && (i2.setState(r2.getDerivedStateFromError(n2)), o2 = i2.__d), null != i2.componentDidCatch && (i2.componentDidCatch(n2, t2 || {}), o2 = i2.__d), o2) return i2.__E = i2;
  } catch (l3) {
    n2 = l3;
  }
  throw n2;
} }, u = 0, t = function(n2) {
  return null != n2 && null == n2.constructor;
}, x.prototype.setState = function(n2, l2) {
  var u2;
  u2 = null != this.__s && this.__s != this.state ? this.__s : this.__s = d({}, this.state), "function" == typeof n2 && (n2 = n2(d({}, u2), this.props)), n2 && d(u2, n2), null != n2 && this.__v && (l2 && this._sb.push(l2), M(this));
}, x.prototype.forceUpdate = function(n2) {
  this.__v && (this.__e = true, n2 && this.__h.push(n2), M(this));
}, x.prototype.render = k, i = [], o = "function" == typeof Promise ? Promise.prototype.then.bind(Promise.resolve()) : setTimeout, e = function(n2, l2) {
  return n2.__v.__b - l2.__v.__b;
}, $.__r = 0, f = /(PointerCapture)$|Capture$/i, c = 0, s = F(false), a = F(true), h = 0;

// node_modules/float-tooltip/dist/float-tooltip.mjs
function _arrayLikeToArray4(r2, a2) {
  (null == a2 || a2 > r2.length) && (a2 = r2.length);
  for (var e2 = 0, n2 = Array(a2); e2 < a2; e2++) n2[e2] = r2[e2];
  return n2;
}
function _arrayWithHoles4(r2) {
  if (Array.isArray(r2)) return r2;
}
function _defineProperty3(e2, r2, t2) {
  return (r2 = _toPropertyKey3(r2)) in e2 ? Object.defineProperty(e2, r2, {
    value: t2,
    enumerable: true,
    configurable: true,
    writable: true
  }) : e2[r2] = t2, e2;
}
function _iterableToArrayLimit4(r2, l2) {
  var t2 = null == r2 ? null : "undefined" != typeof Symbol && r2[Symbol.iterator] || r2["@@iterator"];
  if (null != t2) {
    var e2, n2, i2, u2, a2 = [], f2 = true, o2 = false;
    try {
      if (i2 = (t2 = t2.call(r2)).next, 0 === l2) ;
      else for (; !(f2 = (e2 = i2.call(t2)).done) && (a2.push(e2.value), a2.length !== l2); f2 = true) ;
    } catch (r3) {
      o2 = true, n2 = r3;
    } finally {
      try {
        if (!f2 && null != t2.return && (u2 = t2.return(), Object(u2) !== u2)) return;
      } finally {
        if (o2) throw n2;
      }
    }
    return a2;
  }
}
function _nonIterableRest4() {
  throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function ownKeys2(e2, r2) {
  var t2 = Object.keys(e2);
  if (Object.getOwnPropertySymbols) {
    var o2 = Object.getOwnPropertySymbols(e2);
    r2 && (o2 = o2.filter(function(r3) {
      return Object.getOwnPropertyDescriptor(e2, r3).enumerable;
    })), t2.push.apply(t2, o2);
  }
  return t2;
}
function _objectSpread22(e2) {
  for (var r2 = 1; r2 < arguments.length; r2++) {
    var t2 = null != arguments[r2] ? arguments[r2] : {};
    r2 % 2 ? ownKeys2(Object(t2), true).forEach(function(r3) {
      _defineProperty3(e2, r3, t2[r3]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e2, Object.getOwnPropertyDescriptors(t2)) : ownKeys2(Object(t2)).forEach(function(r3) {
      Object.defineProperty(e2, r3, Object.getOwnPropertyDescriptor(t2, r3));
    });
  }
  return e2;
}
function _slicedToArray4(r2, e2) {
  return _arrayWithHoles4(r2) || _iterableToArrayLimit4(r2, e2) || _unsupportedIterableToArray4(r2, e2) || _nonIterableRest4();
}
function _toPrimitive3(t2, r2) {
  if ("object" != typeof t2 || !t2) return t2;
  var e2 = t2[Symbol.toPrimitive];
  if (void 0 !== e2) {
    var i2 = e2.call(t2, r2);
    if ("object" != typeof i2) return i2;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return ("string" === r2 ? String : Number)(t2);
}
function _toPropertyKey3(t2) {
  var i2 = _toPrimitive3(t2, "string");
  return "symbol" == typeof i2 ? i2 : i2 + "";
}
function _typeof(o2) {
  "@babel/helpers - typeof";
  return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o3) {
    return typeof o3;
  } : function(o3) {
    return o3 && "function" == typeof Symbol && o3.constructor === Symbol && o3 !== Symbol.prototype ? "symbol" : typeof o3;
  }, _typeof(o2);
}
function _unsupportedIterableToArray4(r2, a2) {
  if (r2) {
    if ("string" == typeof r2) return _arrayLikeToArray4(r2, a2);
    var t2 = {}.toString.call(r2).slice(8, -1);
    return "Object" === t2 && r2.constructor && (t2 = r2.constructor.name), "Map" === t2 || "Set" === t2 ? Array.from(r2) : "Arguments" === t2 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t2) ? _arrayLikeToArray4(r2, a2) : void 0;
  }
}
var _reactElement2VNode = function reactElement2VNode(el) {
  if (!(_typeof(el) === "object")) return el;
  var res = J(el);
  if (res.props) {
    var _res$props;
    res.props = _objectSpread22({}, res.props);
    if (res !== null && res !== void 0 && (_res$props = res.props) !== null && _res$props !== void 0 && _res$props.children) {
      res.props.children = Array.isArray(res.props.children) ? res.props.children.map(_reactElement2VNode) : _reactElement2VNode(res.props.children);
    }
  }
  return res;
};
var isReactRenderable = function isReactRenderable2(o2) {
  return t(J(o2));
};
var render = function render2(jsx, domEl) {
  delete domEl.__k;
  E(_reactElement2VNode(jsx), domEl);
};
function styleInject(css, ref) {
  if (ref === void 0) ref = {};
  var insertAt = ref.insertAt;
  if (typeof document === "undefined") {
    return;
  }
  var head = document.head || document.getElementsByTagName("head")[0];
  var style = document.createElement("style");
  style.type = "text/css";
  if (insertAt === "top") {
    if (head.firstChild) {
      head.insertBefore(style, head.firstChild);
    } else {
      head.appendChild(style);
    }
  } else {
    head.appendChild(style);
  }
  if (style.styleSheet) {
    style.styleSheet.cssText = css;
  } else {
    style.appendChild(document.createTextNode(css));
  }
}
var css_248z = ".float-tooltip-kap {\n  position: absolute;\n  width: max-content; /* prevent shrinking near right edge */\n  max-width: max(50%, 150px);\n  padding: 3px 5px;\n  border-radius: 3px;\n  font: 12px sans-serif;\n  color: #eee;\n  background: rgba(0,0,0,0.6);\n  pointer-events: none;\n}\n";
styleInject(css_248z);
var index5 = index({
  props: {
    content: {
      "default": false
    },
    offsetX: {
      triggerUpdate: false
    },
    // null or number
    offsetY: {
      triggerUpdate: false
    }
    // null or number
  },
  init: function init17(domNode, state) {
    var _ref = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {}, _ref$style = _ref.style, style = _ref$style === void 0 ? {} : _ref$style;
    var isD3Selection = !!domNode && _typeof(domNode) === "object" && !!domNode.node && typeof domNode.node === "function";
    var el = select_default(isD3Selection ? domNode.node() : domNode);
    el.style("position") === "static" && el.style("position", "relative");
    state.tooltipEl = el.append("div").attr("class", "float-tooltip-kap");
    Object.entries(style).forEach(function(_ref2) {
      var _ref3 = _slicedToArray4(_ref2, 2), k2 = _ref3[0], v2 = _ref3[1];
      return state.tooltipEl.style(k2, v2);
    });
    state.tooltipEl.style("left", "-10000px").style("display", "none");
    var evSuffix = "tooltip-".concat(Math.round(Math.random() * 1e12));
    state.mouseInside = false;
    el.on("mousemove.".concat(evSuffix), function(ev) {
      state.mouseInside = true;
      var mousePos = pointer_default(ev);
      var domNode2 = el.node();
      var canvasWidth = domNode2.offsetWidth;
      var canvasHeight = domNode2.offsetHeight;
      var translate = [state.offsetX === null || state.offsetX === void 0 ? "-".concat(mousePos[0] / canvasWidth * 100, "%") : typeof state.offsetX === "number" ? "calc(-50% + ".concat(state.offsetX, "px)") : state.offsetX, state.offsetY === null || state.offsetY === void 0 ? canvasHeight > 130 && canvasHeight - mousePos[1] < 100 ? "calc(-100% - 6px)" : "21px" : typeof state.offsetY === "number" ? state.offsetY < 0 ? "calc(-100% - ".concat(Math.abs(state.offsetY), "px)") : "".concat(state.offsetY, "px") : state.offsetY];
      state.tooltipEl.style("left", mousePos[0] + "px").style("top", mousePos[1] + "px").style("transform", "translate(".concat(translate.join(","), ")"));
      state.content && state.tooltipEl.style("display", "inline");
    });
    el.on("mouseover.".concat(evSuffix), function() {
      state.mouseInside = true;
      state.content && state.tooltipEl.style("display", "inline");
    });
    el.on("mouseout.".concat(evSuffix), function() {
      state.mouseInside = false;
      state.tooltipEl.style("display", "none");
    });
  },
  update: function update17(state) {
    state.tooltipEl.style("display", !!state.content && state.mouseInside ? "inline" : "none");
    if (!state.content) {
      state.tooltipEl.text("");
    } else if (state.content instanceof HTMLElement) {
      state.tooltipEl.text("");
      state.tooltipEl.append(function() {
        return state.content;
      });
    } else if (typeof state.content === "string") {
      state.tooltipEl.html(state.content);
    } else if (isReactRenderable(state.content)) {
      state.tooltipEl.text("");
      render(state.content, state.tooltipEl.node());
    } else {
      state.tooltipEl.style("display", "none");
      console.warn("Tooltip content is invalid, skipping.", state.content, state.content.toString());
    }
  }
});

// node_modules/three-render-objects/dist/three-render-objects.mjs
function styleInject2(css, ref) {
  if (ref === void 0) ref = {};
  var insertAt = ref.insertAt;
  if (typeof document === "undefined") {
    return;
  }
  var head = document.head || document.getElementsByTagName("head")[0];
  var style = document.createElement("style");
  style.type = "text/css";
  if (insertAt === "top") {
    if (head.firstChild) {
      head.insertBefore(style, head.firstChild);
    } else {
      head.appendChild(style);
    }
  } else {
    head.appendChild(style);
  }
  if (style.styleSheet) {
    style.styleSheet.cssText = css;
  } else {
    style.appendChild(document.createTextNode(css));
  }
}
var css_248z2 = ".scene-nav-info {\n  position: absolute;\n  bottom: 5px;\n  width: 100%;\n  text-align: center;\n  color: slategrey;\n  opacity: 0.7;\n  font-size: 10px;\n  font-family: sans-serif;\n  pointer-events: none;\n  user-select: none;\n}\n\n.scene-container canvas:focus {\n  outline: none;\n}";
styleInject2(css_248z2);
function _arrayLikeToArray5(r2, a2) {
  (null == a2 || a2 > r2.length) && (a2 = r2.length);
  for (var e2 = 0, n2 = Array(a2); e2 < a2; e2++) n2[e2] = r2[e2];
  return n2;
}
function _arrayWithHoles5(r2) {
  if (Array.isArray(r2)) return r2;
}
function _arrayWithoutHoles4(r2) {
  if (Array.isArray(r2)) return _arrayLikeToArray5(r2);
}
function _defineProperty4(e2, r2, t2) {
  return (r2 = _toPropertyKey4(r2)) in e2 ? Object.defineProperty(e2, r2, {
    value: t2,
    enumerable: true,
    configurable: true,
    writable: true
  }) : e2[r2] = t2, e2;
}
function _iterableToArray4(r2) {
  if ("undefined" != typeof Symbol && null != r2[Symbol.iterator] || null != r2["@@iterator"]) return Array.from(r2);
}
function _iterableToArrayLimit5(r2, l2) {
  var t2 = null == r2 ? null : "undefined" != typeof Symbol && r2[Symbol.iterator] || r2["@@iterator"];
  if (null != t2) {
    var e2, n2, i2, u2, a2 = [], f2 = true, o2 = false;
    try {
      if (i2 = (t2 = t2.call(r2)).next, 0 === l2) ;
      else for (; !(f2 = (e2 = i2.call(t2)).done) && (a2.push(e2.value), a2.length !== l2); f2 = true) ;
    } catch (r3) {
      o2 = true, n2 = r3;
    } finally {
      try {
        if (!f2 && null != t2.return && (u2 = t2.return(), Object(u2) !== u2)) return;
      } finally {
        if (o2) throw n2;
      }
    }
    return a2;
  }
}
function _nonIterableRest5() {
  throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _nonIterableSpread4() {
  throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _slicedToArray5(r2, e2) {
  return _arrayWithHoles5(r2) || _iterableToArrayLimit5(r2, e2) || _unsupportedIterableToArray5(r2, e2) || _nonIterableRest5();
}
function _toConsumableArray4(r2) {
  return _arrayWithoutHoles4(r2) || _iterableToArray4(r2) || _unsupportedIterableToArray5(r2) || _nonIterableSpread4();
}
function _toPrimitive4(t2, r2) {
  if ("object" != typeof t2 || !t2) return t2;
  var e2 = t2[Symbol.toPrimitive];
  if (void 0 !== e2) {
    var i2 = e2.call(t2, r2);
    if ("object" != typeof i2) return i2;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return ("string" === r2 ? String : Number)(t2);
}
function _toPropertyKey4(t2) {
  var i2 = _toPrimitive4(t2, "string");
  return "symbol" == typeof i2 ? i2 : i2 + "";
}
function _unsupportedIterableToArray5(r2, a2) {
  if (r2) {
    if ("string" == typeof r2) return _arrayLikeToArray5(r2, a2);
    var t2 = {}.toString.call(r2).slice(8, -1);
    return "Object" === t2 && r2.constructor && (t2 = r2.constructor.name), "Map" === t2 || "Set" === t2 ? Array.from(r2) : "Arguments" === t2 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t2) ? _arrayLikeToArray5(r2, a2) : void 0;
  }
}
var three2 = window.THREE ? window.THREE : {
  WebGLRenderer,
  Scene,
  PerspectiveCamera,
  Raycaster,
  SRGBColorSpace,
  TextureLoader,
  Vector2,
  Vector3,
  Box3,
  Color,
  Mesh,
  SphereGeometry,
  MeshBasicMaterial,
  BackSide,
  Clock
};
var threeRenderObjects = index({
  props: {
    width: {
      "default": window.innerWidth,
      onChange: function onChange10(width, state, prevWidth) {
        isNaN(width) && (state.width = prevWidth);
      }
    },
    height: {
      "default": window.innerHeight,
      onChange: function onChange11(height, state, prevHeight) {
        isNaN(height) && (state.height = prevHeight);
      }
    },
    viewOffset: {
      "default": [0, 0]
    },
    backgroundColor: {
      "default": "#000011"
    },
    backgroundImageUrl: {},
    onBackgroundImageLoaded: {},
    showNavInfo: {
      "default": true
    },
    skyRadius: {
      "default": 5e4
    },
    objects: {
      "default": []
    },
    lights: {
      "default": []
    },
    enablePointerInteraction: {
      "default": true,
      onChange: function onChange12(_2, state) {
        state.hoverObj = null;
        state.tooltip && state.tooltip.content(null);
      },
      triggerUpdate: false
    },
    pointerRaycasterThrottleMs: {
      "default": 50,
      triggerUpdate: false
    },
    lineHoverPrecision: {
      "default": 1,
      triggerUpdate: false
    },
    pointsHoverPrecision: {
      "default": 1,
      triggerUpdate: false
    },
    hoverOrderComparator: {
      triggerUpdate: false
    },
    // keep existing order by default
    hoverFilter: {
      "default": function _default24() {
        return true;
      },
      triggerUpdate: false
    },
    // exclude objects from interaction
    tooltipContent: {
      triggerUpdate: false
    },
    hoverDuringDrag: {
      "default": false,
      triggerUpdate: false
    },
    clickAfterDrag: {
      "default": false,
      triggerUpdate: false
    },
    onHover: {
      "default": function _default25() {
      },
      triggerUpdate: false
    },
    onClick: {
      "default": function _default26() {
      },
      triggerUpdate: false
    },
    onRightClick: {
      triggerUpdate: false
    }
  },
  methods: {
    tick: function tick(state) {
      if (state.initialised) {
        state.controls.enabled && state.controls.update && state.controls.update(Math.min(1, state.clock.getDelta()));
        state.postProcessingComposer ? state.postProcessingComposer.render() : state.renderer.render(state.scene, state.camera);
        state.extraRenderers.forEach(function(r2) {
          return r2.render(state.scene, state.camera);
        });
        var now = +/* @__PURE__ */ new Date();
        if (state.enablePointerInteraction && now - state.lastRaycasterCheck >= state.pointerRaycasterThrottleMs) {
          state.lastRaycasterCheck = now;
          var topObject = null;
          if (state.hoverDuringDrag || !state.isPointerDragging) {
            var intersects = this.intersectingObjects(state.pointerPos.x, state.pointerPos.y);
            state.hoverOrderComparator && intersects.sort(function(a2, b) {
              return state.hoverOrderComparator(a2.object, b.object);
            });
            var topIntersect = intersects.find(function(d2) {
              return state.hoverFilter(d2.object);
            }) || null;
            topObject = topIntersect ? topIntersect.object : null;
            state.intersection = topIntersect || null;
          }
          if (topObject !== state.hoverObj) {
            state.onHover(topObject, state.hoverObj, state.intersection);
            state.tooltip.content(topObject ? index2(state.tooltipContent)(topObject, state.intersection) || null : null);
            state.hoverObj = topObject;
          }
        }
        state.tweenGroup.update();
      }
      return this;
    },
    getPointerPos: function getPointerPos(state) {
      var _state$pointerPos = state.pointerPos, x2 = _state$pointerPos.x, y2 = _state$pointerPos.y;
      return {
        x: x2,
        y: y2
      };
    },
    cameraPosition: function cameraPosition(state, position, lookAt, transitionDuration) {
      var camera3 = state.camera;
      if (position && state.initialised) {
        var finalPos = position;
        var finalLookAt = lookAt || {
          x: 0,
          y: 0,
          z: 0
        };
        if (!transitionDuration) {
          setCameraPos(finalPos);
          setLookAt(finalLookAt);
        } else {
          var camPos = Object.assign({}, camera3.position);
          var camLookAt = getLookAt();
          state.tweenGroup.add(new Tween(camPos).to(finalPos, transitionDuration).easing(Easing.Quadratic.Out).onUpdate(setCameraPos).start());
          state.tweenGroup.add(new Tween(camLookAt).to(finalLookAt, transitionDuration / 3).easing(Easing.Quadratic.Out).onUpdate(setLookAt).start());
        }
        return this;
      }
      return Object.assign({}, camera3.position, {
        lookAt: getLookAt()
      });
      function setCameraPos(pos) {
        var x2 = pos.x, y2 = pos.y, z2 = pos.z;
        if (x2 !== void 0) camera3.position.x = x2;
        if (y2 !== void 0) camera3.position.y = y2;
        if (z2 !== void 0) camera3.position.z = z2;
      }
      function setLookAt(lookAt2) {
        var lookAtVect = new three2.Vector3(lookAt2.x, lookAt2.y, lookAt2.z);
        if (state.controls.target) {
          state.controls.target = lookAtVect;
        } else {
          camera3.lookAt(lookAtVect);
        }
      }
      function getLookAt() {
        return Object.assign(new three2.Vector3(0, 0, -1e3).applyQuaternion(camera3.quaternion).add(camera3.position));
      }
    },
    zoomToFit: function zoomToFit(state) {
      var transitionDuration = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;
      var padding = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 10;
      for (var _len = arguments.length, bboxArgs = new Array(_len > 3 ? _len - 3 : 0), _key = 3; _key < _len; _key++) {
        bboxArgs[_key - 3] = arguments[_key];
      }
      return this.fitToBbox(this.getBbox.apply(this, bboxArgs), transitionDuration, padding);
    },
    fitToBbox: function fitToBbox(state, bbox) {
      var transitionDuration = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;
      var padding = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 10;
      var camera3 = state.camera;
      if (bbox) {
        var center = new three2.Vector3(0, 0, 0);
        var maxBoxSide = Math.max.apply(Math, _toConsumableArray4(Object.entries(bbox).map(function(_ref) {
          var _ref2 = _slicedToArray5(_ref, 2), coordType = _ref2[0], coords = _ref2[1];
          return Math.max.apply(Math, _toConsumableArray4(coords.map(function(c2) {
            return Math.abs(center[coordType] - c2);
          })));
        }))) * 2;
        var paddedFov = (1 - padding * 2 / state.height) * camera3.fov;
        var fitHeightDistance = maxBoxSide / Math.atan(paddedFov * Math.PI / 180);
        var fitWidthDistance = fitHeightDistance / camera3.aspect;
        var distance = Math.max(fitHeightDistance, fitWidthDistance);
        if (distance > 0) {
          var newCameraPosition = center.clone().sub(camera3.position).normalize().multiplyScalar(-distance);
          this.cameraPosition(newCameraPosition, center, transitionDuration);
        }
      }
      return this;
    },
    getBbox: function getBbox(state) {
      var objFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : function() {
        return true;
      };
      var box = new three2.Box3(new three2.Vector3(0, 0, 0), new three2.Vector3(0, 0, 0));
      var objs = state.objects.filter(objFilter);
      if (!objs.length) return null;
      objs.forEach(function(obj) {
        return box.expandByObject(obj);
      });
      return Object.assign.apply(Object, _toConsumableArray4(["x", "y", "z"].map(function(c2) {
        return _defineProperty4({}, c2, [box.min[c2], box.max[c2]]);
      })));
    },
    getScreenCoords: function getScreenCoords(state, x2, y2, z2) {
      var vec = new three2.Vector3(x2, y2, z2);
      vec.project(this.camera());
      return {
        // align relative pos to canvas dimensions
        x: (vec.x + 1) * state.width / 2,
        y: -(vec.y - 1) * state.height / 2
      };
    },
    getSceneCoords: function getSceneCoords(state, screenX, screenY) {
      var distance = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 0;
      var relCoords = new three2.Vector2(screenX / state.width * 2 - 1, -(screenY / state.height) * 2 + 1);
      var raycaster = new three2.Raycaster();
      raycaster.setFromCamera(relCoords, state.camera);
      return Object.assign({}, raycaster.ray.at(distance, new three2.Vector3()));
    },
    intersectingObjects: function intersectingObjects(state, x2, y2) {
      var relCoords = new three2.Vector2(x2 / state.width * 2 - 1, -(y2 / state.height) * 2 + 1);
      var raycaster = new three2.Raycaster();
      raycaster.params.Line.threshold = state.lineHoverPrecision;
      raycaster.params.Points.threshold = state.pointsHoverPrecision;
      raycaster.setFromCamera(relCoords, state.camera);
      return raycaster.intersectObjects(state.objects, true);
    },
    renderer: function renderer(state) {
      return state.renderer;
    },
    scene: function scene(state) {
      return state.scene;
    },
    camera: function camera(state) {
      return state.camera;
    },
    postProcessingComposer: function postProcessingComposer(state) {
      return state.postProcessingComposer;
    },
    controls: function controls(state) {
      return state.controls;
    },
    tbControls: function tbControls(state) {
      return state.controls;
    }
    // to be deprecated
  },
  stateInit: function stateInit5() {
    return {
      scene: new three2.Scene(),
      camera: new three2.PerspectiveCamera(),
      clock: new three2.Clock(),
      tweenGroup: new Group2(),
      lastRaycasterCheck: 0
    };
  },
  init: function init18(domNode, state) {
    var _ref4 = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {}, _ref4$controlType = _ref4.controlType, controlType = _ref4$controlType === void 0 ? "trackball" : _ref4$controlType, _ref4$useWebGPU = _ref4.useWebGPU, useWebGPU = _ref4$useWebGPU === void 0 ? false : _ref4$useWebGPU, _ref4$rendererConfig = _ref4.rendererConfig, rendererConfig = _ref4$rendererConfig === void 0 ? {} : _ref4$rendererConfig, _ref4$extraRenderers = _ref4.extraRenderers, extraRenderers = _ref4$extraRenderers === void 0 ? [] : _ref4$extraRenderers, _ref4$waitForLoadComp = _ref4.waitForLoadComplete, waitForLoadComplete = _ref4$waitForLoadComp === void 0 ? true : _ref4$waitForLoadComp;
    domNode.innerHTML = "";
    domNode.appendChild(state.container = document.createElement("div"));
    state.container.className = "scene-container";
    state.container.style.position = "relative";
    state.container.appendChild(state.navInfo = document.createElement("div"));
    state.navInfo.className = "scene-nav-info";
    state.navInfo.textContent = {
      orbit: "Left-click: rotate, Mouse-wheel/middle-click: zoom, Right-click: pan",
      trackball: "Left-click: rotate, Mouse-wheel/middle-click: zoom, Right-click: pan",
      fly: "WASD: move, R|F: up | down, Q|E: roll, up|down: pitch, left|right: yaw"
    }[controlType] || "";
    state.navInfo.style.display = state.showNavInfo ? null : "none";
    state.tooltip = new index5(state.container);
    state.pointerPos = new three2.Vector2();
    state.pointerPos.x = -2;
    state.pointerPos.y = -2;
    ["pointermove", "pointerdown"].forEach(function(evType) {
      return state.container.addEventListener(evType, function(ev) {
        evType === "pointerdown" && (state.isPointerPressed = true);
        !state.isPointerDragging && ev.type === "pointermove" && (ev.pressure > 0 || state.isPointerPressed) && (ev.pointerType !== "touch" || ev.movementX === void 0 || [ev.movementX, ev.movementY].some(function(m2) {
          return Math.abs(m2) > 1;
        })) && (state.isPointerDragging = true);
        if (state.enablePointerInteraction) {
          var offset = getOffset(state.container);
          state.pointerPos.x = ev.pageX - offset.left;
          state.pointerPos.y = ev.pageY - offset.top;
        }
        function getOffset(el) {
          var rect = el.getBoundingClientRect(), scrollLeft = window.pageXOffset || document.documentElement.scrollLeft, scrollTop = window.pageYOffset || document.documentElement.scrollTop;
          return {
            top: rect.top + scrollTop,
            left: rect.left + scrollLeft
          };
        }
      }, {
        passive: true
      });
    });
    state.container.addEventListener("pointerup", function(ev) {
      if (!state.isPointerPressed) return;
      state.isPointerPressed = false;
      if (state.isPointerDragging) {
        state.isPointerDragging = false;
        if (!state.clickAfterDrag) return;
      }
      requestAnimationFrame(function() {
        if (ev.button === 0) {
          state.onClick(state.hoverObj || null, ev, state.intersection);
        }
        if (ev.button === 2 && state.onRightClick) {
          state.onRightClick(state.hoverObj || null, ev, state.intersection);
        }
      });
    }, {
      passive: true,
      capture: true
    });
    state.container.addEventListener("contextmenu", function(ev) {
      if (state.onRightClick) ev.preventDefault();
    });
    state.renderer = new (useWebGPU ? WebGPURenderer : three2.WebGLRenderer)(Object.assign({
      antialias: true,
      alpha: true
    }, rendererConfig));
    state.renderer.setPixelRatio(Math.min(2, window.devicePixelRatio));
    state.container.appendChild(state.renderer.domElement);
    state.extraRenderers = extraRenderers;
    state.extraRenderers.forEach(function(r2) {
      r2.domElement.style.position = "absolute";
      r2.domElement.style.top = "0px";
      r2.domElement.style.pointerEvents = "none";
      state.container.appendChild(r2.domElement);
    });
    state.postProcessingComposer = new EffectComposer(state.renderer);
    state.postProcessingComposer.addPass(new RenderPass(state.scene, state.camera));
    state.controls = new {
      trackball: TrackballControls,
      orbit: OrbitControls,
      fly: FlyControls
    }[controlType](state.camera, state.renderer.domElement);
    if (controlType === "fly") {
      state.controls.movementSpeed = 300;
      state.controls.rollSpeed = Math.PI / 6;
      state.controls.dragToLook = true;
    }
    if (controlType === "trackball" || controlType === "orbit") {
      state.controls.minDistance = 0.1;
      state.controls.maxDistance = state.skyRadius;
      state.controls.addEventListener("start", function() {
        state.controlsEngaged = true;
      });
      state.controls.addEventListener("change", function() {
        if (state.controlsEngaged) {
          state.controlsDragging = true;
        }
      });
      state.controls.addEventListener("end", function() {
        state.controlsEngaged = false;
        state.controlsDragging = false;
      });
    }
    [state.renderer, state.postProcessingComposer].concat(_toConsumableArray4(state.extraRenderers)).forEach(function(r2) {
      return r2.setSize(state.width, state.height);
    });
    state.camera.aspect = state.width / state.height;
    state.camera.updateProjectionMatrix();
    state.camera.position.z = 1e3;
    state.scene.add(state.skysphere = new three2.Mesh());
    state.skysphere.visible = false;
    state.loadComplete = state.scene.visible = !waitForLoadComplete;
    window.scene = state.scene;
  },
  update: function update18(state, changedProps) {
    if (state.width && state.height && (changedProps.hasOwnProperty("width") || changedProps.hasOwnProperty("height"))) {
      var _state$camera;
      var w2 = state.width;
      var h2 = state.height;
      state.container.style.width = "".concat(w2, "px");
      state.container.style.height = "".concat(h2, "px");
      [state.renderer, state.postProcessingComposer].concat(_toConsumableArray4(state.extraRenderers)).forEach(function(r2) {
        return r2.setSize(w2, h2);
      });
      state.camera.aspect = w2 / h2;
      var o2 = state.viewOffset.slice(0, 2);
      o2.some(function(n2) {
        return n2;
      }) && (_state$camera = state.camera).setViewOffset.apply(_state$camera, [w2, h2].concat(_toConsumableArray4(o2), [w2, h2]));
      state.camera.updateProjectionMatrix();
    }
    if (changedProps.hasOwnProperty("viewOffset")) {
      var _state$camera2;
      var _w = state.width;
      var _h = state.height;
      var _o = state.viewOffset.slice(0, 2);
      _o.some(function(n2) {
        return n2;
      }) ? (_state$camera2 = state.camera).setViewOffset.apply(_state$camera2, [_w, _h].concat(_toConsumableArray4(_o), [_w, _h])) : state.camera.clearViewOffset();
    }
    if (changedProps.hasOwnProperty("skyRadius") && state.skyRadius) {
      state.controls.hasOwnProperty("maxDistance") && changedProps.skyRadius && (state.controls.maxDistance = Math.min(state.controls.maxDistance, state.skyRadius));
      state.camera.far = state.skyRadius * 2.5;
      state.camera.updateProjectionMatrix();
      state.skysphere.geometry = new three2.SphereGeometry(state.skyRadius);
    }
    if (changedProps.hasOwnProperty("backgroundColor")) {
      var alpha = parseToRgb(state.backgroundColor).alpha;
      if (alpha === void 0) alpha = 1;
      state.renderer.setClearColor(new three2.Color(curriedOpacify$1(1, state.backgroundColor)), alpha);
    }
    if (changedProps.hasOwnProperty("backgroundImageUrl")) {
      if (!state.backgroundImageUrl) {
        state.skysphere.visible = false;
        state.skysphere.material.map = null;
        !state.loadComplete && finishLoad();
      } else {
        new three2.TextureLoader().load(state.backgroundImageUrl, function(texture) {
          texture.colorSpace = three2.SRGBColorSpace;
          state.skysphere.material = new three2.MeshBasicMaterial({
            map: texture,
            side: three2.BackSide
          });
          state.skysphere.visible = true;
          state.onBackgroundImageLoaded && setTimeout(state.onBackgroundImageLoaded);
          !state.loadComplete && finishLoad();
        });
      }
    }
    changedProps.hasOwnProperty("showNavInfo") && (state.navInfo.style.display = state.showNavInfo ? null : "none");
    if (changedProps.hasOwnProperty("lights")) {
      (changedProps.lights || []).forEach(function(light) {
        return state.scene.remove(light);
      });
      state.lights.forEach(function(light) {
        return state.scene.add(light);
      });
    }
    if (changedProps.hasOwnProperty("objects")) {
      (changedProps.objects || []).forEach(function(obj) {
        return state.scene.remove(obj);
      });
      state.objects.forEach(function(obj) {
        return state.scene.add(obj);
      });
    }
    function finishLoad() {
      state.loadComplete = state.scene.visible = true;
    }
  }
});

// node_modules/globe.gl/dist/globe.gl.mjs
function styleInject3(css, ref) {
  if (ref === void 0) ref = {};
  var insertAt = ref.insertAt;
  if (typeof document === "undefined") {
    return;
  }
  var head = document.head || document.getElementsByTagName("head")[0];
  var style = document.createElement("style");
  style.type = "text/css";
  if (insertAt === "top") {
    if (head.firstChild) {
      head.insertBefore(style, head.firstChild);
    } else {
      head.appendChild(style);
    }
  } else {
    head.appendChild(style);
  }
  if (style.styleSheet) {
    style.styleSheet.cssText = css;
  } else {
    style.appendChild(document.createTextNode(css));
  }
}
var css_248z3 = ".scene-container .clickable {\n  cursor: pointer;\n}";
styleInject3(css_248z3);
function _arrayLikeToArray6(r2, a2) {
  (null == a2 || a2 > r2.length) && (a2 = r2.length);
  for (var e2 = 0, n2 = Array(a2); e2 < a2; e2++) n2[e2] = r2[e2];
  return n2;
}
function _arrayWithoutHoles5(r2) {
  if (Array.isArray(r2)) return _arrayLikeToArray6(r2);
}
function _defineProperty5(e2, r2, t2) {
  return (r2 = _toPropertyKey5(r2)) in e2 ? Object.defineProperty(e2, r2, {
    value: t2,
    enumerable: true,
    configurable: true,
    writable: true
  }) : e2[r2] = t2, e2;
}
function _iterableToArray5(r2) {
  if ("undefined" != typeof Symbol && null != r2[Symbol.iterator] || null != r2["@@iterator"]) return Array.from(r2);
}
function _nonIterableSpread5() {
  throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function ownKeys3(e2, r2) {
  var t2 = Object.keys(e2);
  if (Object.getOwnPropertySymbols) {
    var o2 = Object.getOwnPropertySymbols(e2);
    r2 && (o2 = o2.filter(function(r3) {
      return Object.getOwnPropertyDescriptor(e2, r3).enumerable;
    })), t2.push.apply(t2, o2);
  }
  return t2;
}
function _objectSpread23(e2) {
  for (var r2 = 1; r2 < arguments.length; r2++) {
    var t2 = null != arguments[r2] ? arguments[r2] : {};
    r2 % 2 ? ownKeys3(Object(t2), true).forEach(function(r3) {
      _defineProperty5(e2, r3, t2[r3]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e2, Object.getOwnPropertyDescriptors(t2)) : ownKeys3(Object(t2)).forEach(function(r3) {
      Object.defineProperty(e2, r3, Object.getOwnPropertyDescriptor(t2, r3));
    });
  }
  return e2;
}
function _objectWithoutProperties2(e2, t2) {
  if (null == e2) return {};
  var o2, r2, i2 = _objectWithoutPropertiesLoose2(e2, t2);
  if (Object.getOwnPropertySymbols) {
    var n2 = Object.getOwnPropertySymbols(e2);
    for (r2 = 0; r2 < n2.length; r2++) o2 = n2[r2], -1 === t2.indexOf(o2) && {}.propertyIsEnumerable.call(e2, o2) && (i2[o2] = e2[o2]);
  }
  return i2;
}
function _objectWithoutPropertiesLoose2(r2, e2) {
  if (null == r2) return {};
  var t2 = {};
  for (var n2 in r2) if ({}.hasOwnProperty.call(r2, n2)) {
    if (-1 !== e2.indexOf(n2)) continue;
    t2[n2] = r2[n2];
  }
  return t2;
}
function _toConsumableArray5(r2) {
  return _arrayWithoutHoles5(r2) || _iterableToArray5(r2) || _unsupportedIterableToArray6(r2) || _nonIterableSpread5();
}
function _toPrimitive5(t2, r2) {
  if ("object" != typeof t2 || !t2) return t2;
  var e2 = t2[Symbol.toPrimitive];
  if (void 0 !== e2) {
    var i2 = e2.call(t2, r2);
    if ("object" != typeof i2) return i2;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return ("string" === r2 ? String : Number)(t2);
}
function _toPropertyKey5(t2) {
  var i2 = _toPrimitive5(t2, "string");
  return "symbol" == typeof i2 ? i2 : i2 + "";
}
function _unsupportedIterableToArray6(r2, a2) {
  if (r2) {
    if ("string" == typeof r2) return _arrayLikeToArray6(r2, a2);
    var t2 = {}.toString.call(r2).slice(8, -1);
    return "Object" === t2 && r2.constructor && (t2 = r2.constructor.name), "Map" === t2 || "Set" === t2 ? Array.from(r2) : "Arguments" === t2 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t2) ? _arrayLikeToArray6(r2, a2) : void 0;
  }
}
function linkKapsule2(kapsulePropName, kapsuleType) {
  var dummyK = new kapsuleType();
  dummyK._destructor && dummyK._destructor();
  return {
    linkProp: function linkProp(prop) {
      return {
        "default": dummyK[prop](),
        onChange: function onChange15(v2, state) {
          state[kapsulePropName][prop](v2);
        },
        triggerUpdate: false
      };
    },
    linkMethod: function linkMethod(method) {
      return function(state) {
        var kapsuleInstance = state[kapsulePropName];
        for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
          args[_key - 1] = arguments[_key];
        }
        var returnVal = kapsuleInstance[method].apply(kapsuleInstance, args);
        return returnVal === kapsuleInstance ? this : returnVal;
      };
    }
  };
}
var _excluded2 = ["rendererConfig", "waitForGlobeReady"];
var THREE2 = _objectSpread23(_objectSpread23({}, window.THREE ? window.THREE : {
  AmbientLight,
  DirectionalLight,
  Vector2,
  REVISION
}), {}, {
  CSS2DRenderer
});
var bindGlobe = linkKapsule2("globe", threeGlobe);
var linkedGlobeProps = Object.assign.apply(Object, _toConsumableArray5(["globeImageUrl", "bumpImageUrl", "globeTileEngineUrl", "globeTileEngineMaxLevel", "showGlobe", "showGraticules", "showAtmosphere", "atmosphereColor", "atmosphereAltitude", "onGlobeReady", "pointsData", "pointLat", "pointLng", "pointColor", "pointAltitude", "pointRadius", "pointResolution", "pointsMerge", "pointsTransitionDuration", "arcsData", "arcStartLat", "arcStartLng", "arcEndLat", "arcEndLng", "arcColor", "arcAltitude", "arcAltitudeAutoScale", "arcStroke", "arcCurveResolution", "arcCircularResolution", "arcDashLength", "arcDashGap", "arcDashInitialGap", "arcDashAnimateTime", "arcsTransitionDuration", "polygonsData", "polygonGeoJsonGeometry", "polygonCapColor", "polygonCapMaterial", "polygonSideColor", "polygonSideMaterial", "polygonStrokeColor", "polygonAltitude", "polygonCapCurvatureResolution", "polygonsTransitionDuration", "pathsData", "pathPoints", "pathPointLat", "pathPointLng", "pathPointAlt", "pathResolution", "pathColor", "pathStroke", "pathDashLength", "pathDashGap", "pathDashInitialGap", "pathDashAnimateTime", "pathTransitionDuration", "heatmapsData", "heatmapPoints", "heatmapPointLat", "heatmapPointLng", "heatmapPointWeight", "heatmapBandwidth", "heatmapColorFn", "heatmapColorSaturation", "heatmapBaseAltitude", "heatmapTopAltitude", "heatmapsTransitionDuration", "hexBinPointsData", "hexBinPointLat", "hexBinPointLng", "hexBinPointWeight", "hexBinResolution", "hexMargin", "hexTopCurvatureResolution", "hexTopColor", "hexSideColor", "hexAltitude", "hexBinMerge", "hexTransitionDuration", "hexPolygonsData", "hexPolygonGeoJsonGeometry", "hexPolygonColor", "hexPolygonAltitude", "hexPolygonResolution", "hexPolygonMargin", "hexPolygonUseDots", "hexPolygonCurvatureResolution", "hexPolygonDotResolution", "hexPolygonsTransitionDuration", "tilesData", "tileLat", "tileLng", "tileAltitude", "tileWidth", "tileHeight", "tileUseGlobeProjection", "tileMaterial", "tileCurvatureResolution", "tilesTransitionDuration", "particlesData", "particlesList", "particleLat", "particleLng", "particleAltitude", "particlesSize", "particlesSizeAttenuation", "particlesColor", "particlesTexture", "ringsData", "ringLat", "ringLng", "ringAltitude", "ringColor", "ringResolution", "ringMaxRadius", "ringPropagationSpeed", "ringRepeatPeriod", "labelsData", "labelLat", "labelLng", "labelAltitude", "labelRotation", "labelText", "labelSize", "labelTypeFace", "labelColor", "labelResolution", "labelIncludeDot", "labelDotRadius", "labelDotOrientation", "labelsTransitionDuration", "htmlElementsData", "htmlLat", "htmlLng", "htmlAltitude", "htmlElement", "htmlElementVisibilityModifier", "htmlTransitionDuration", "objectsData", "objectLat", "objectLng", "objectAltitude", "objectRotation", "objectFacesSurface", "objectThreeObject", "customLayerData", "customThreeObject", "customThreeObjectUpdate"].map(function(p2) {
  return _defineProperty5({}, p2, bindGlobe.linkProp(p2));
})));
var linkedGlobeMethods = Object.assign.apply(Object, _toConsumableArray5(["globeMaterial", "getGlobeRadius", "getCoords", "toGeoCoords"].map(function(p2) {
  return _defineProperty5({}, p2, bindGlobe.linkMethod(p2));
})));
var bindRenderObjs = linkKapsule2("renderObjs", threeRenderObjects);
var linkedRenderObjsProps = Object.assign.apply(Object, _toConsumableArray5(["width", "height", "backgroundColor", "backgroundImageUrl", "enablePointerInteraction"].map(function(p2) {
  return _defineProperty5({}, p2, bindRenderObjs.linkProp(p2));
})));
var linkedRenderObjsMethods = Object.assign.apply(Object, _toConsumableArray5(["lights", "postProcessingComposer"].map(function(p2) {
  return _defineProperty5({}, p2, bindRenderObjs.linkMethod(p2));
})));
var globe = index({
  props: _objectSpread23(_objectSpread23({
    onZoom: {
      triggerUpdate: false
    },
    onGlobeClick: {
      triggerUpdate: false
    },
    onGlobeRightClick: {
      triggerUpdate: false
    },
    pointLabel: {
      "default": "name",
      triggerUpdate: false
    },
    onPointClick: {
      triggerUpdate: false
    },
    onPointRightClick: {
      triggerUpdate: false
    },
    onPointHover: {
      triggerUpdate: false
    },
    arcLabel: {
      "default": "name",
      triggerUpdate: false
    },
    onArcClick: {
      triggerUpdate: false
    },
    onArcRightClick: {
      triggerUpdate: false
    },
    onArcHover: {
      triggerUpdate: false
    },
    polygonLabel: {
      "default": "name",
      triggerUpdate: false
    },
    onPolygonClick: {
      triggerUpdate: false
    },
    onPolygonRightClick: {
      triggerUpdate: false
    },
    onPolygonHover: {
      triggerUpdate: false
    },
    pathLabel: {
      "default": "name",
      triggerUpdate: false
    },
    onPathClick: {
      triggerUpdate: false
    },
    onPathRightClick: {
      triggerUpdate: false
    },
    onPathHover: {
      triggerUpdate: false
    },
    onHeatmapClick: {
      triggerUpdate: false
    },
    onHeatmapRightClick: {
      triggerUpdate: false
    },
    onHeatmapHover: {
      triggerUpdate: false
    },
    hexLabel: {
      triggerUpdate: false
    },
    onHexClick: {
      triggerUpdate: false
    },
    onHexRightClick: {
      triggerUpdate: false
    },
    onHexHover: {
      triggerUpdate: false
    },
    hexPolygonLabel: {
      triggerUpdate: false
    },
    onHexPolygonClick: {
      triggerUpdate: false
    },
    onHexPolygonRightClick: {
      triggerUpdate: false
    },
    onHexPolygonHover: {
      triggerUpdate: false
    },
    tileLabel: {
      "default": "name",
      triggerUpdate: false
    },
    onTileClick: {
      triggerUpdate: false
    },
    onTileRightClick: {
      triggerUpdate: false
    },
    onTileHover: {
      triggerUpdate: false
    },
    particleLabel: {
      "default": "name",
      triggerUpdate: false
    },
    onParticleClick: {
      triggerUpdate: false
    },
    onParticleRightClick: {
      triggerUpdate: false
    },
    onParticleHover: {
      triggerUpdate: false
    },
    labelLabel: {
      triggerUpdate: false
    },
    onLabelClick: {
      triggerUpdate: false
    },
    onLabelRightClick: {
      triggerUpdate: false
    },
    onLabelHover: {
      triggerUpdate: false
    },
    objectLabel: {
      "default": "name",
      triggerUpdate: false
    },
    onObjectClick: {
      triggerUpdate: false
    },
    onObjectRightClick: {
      triggerUpdate: false
    },
    onObjectHover: {
      triggerUpdate: false
    },
    customLayerLabel: {
      "default": "name",
      triggerUpdate: false
    },
    onCustomLayerClick: {
      triggerUpdate: false
    },
    onCustomLayerRightClick: {
      triggerUpdate: false
    },
    onCustomLayerHover: {
      triggerUpdate: false
    },
    pointerEventsFilter: {
      "default": function _default27() {
        return true;
      },
      triggerUpdate: false
    },
    lineHoverPrecision: {
      "default": 0.2,
      triggerUpdate: false,
      onChange: function onChange13(val, state) {
        state.renderObjs.lineHoverPrecision(val);
        state.renderObjs.pointsHoverPrecision(val);
      }
    },
    globeOffset: {
      "default": [0, 0],
      triggerUpdate: false,
      onChange: function onChange14(o2, state) {
        return Array.isArray(o2) && o2.length === 2 && state.renderObjs.viewOffset(o2.map(function(v2) {
          return -v2;
        }));
      }
    }
  }, linkedGlobeProps), linkedRenderObjsProps),
  methods: _objectSpread23(_objectSpread23({
    pauseAnimation: function pauseAnimation5(state) {
      var _state$globe;
      if (state.animationFrameRequestId !== null) {
        cancelAnimationFrame(state.animationFrameRequestId);
        state.animationFrameRequestId = null;
      }
      (_state$globe = state.globe) === null || _state$globe === void 0 || _state$globe.pauseAnimation();
      return this;
    },
    resumeAnimation: function resumeAnimation5(state) {
      var _state$globe2;
      if (state.animationFrameRequestId === null) {
        this._animationCycle();
      }
      (_state$globe2 = state.globe) === null || _state$globe2 === void 0 || _state$globe2.resumeAnimation();
      return this;
    },
    _animationCycle: function _animationCycle2(state) {
      state.renderObjs.tick();
      state.tweenGroup.update();
      state.animationFrameRequestId = requestAnimationFrame(this._animationCycle);
    },
    pointOfView: function pointOfView(state) {
      var geoCoords = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
      var transitionDuration = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;
      var curGeoCoords = getGeoCoords();
      if (geoCoords.lat === void 0 && geoCoords.lng === void 0 && geoCoords.altitude === void 0) {
        return curGeoCoords;
      } else {
        var finalGeoCoords = Object.assign({}, curGeoCoords, geoCoords);
        ["lat", "lng", "altitude"].forEach(function(p2) {
          return finalGeoCoords[p2] = +finalGeoCoords[p2];
        });
        if (!transitionDuration) {
          setCameraPos(finalGeoCoords);
        } else {
          while (curGeoCoords.lng - finalGeoCoords.lng > 180) curGeoCoords.lng -= 360;
          while (curGeoCoords.lng - finalGeoCoords.lng < -180) curGeoCoords.lng += 360;
          state.tweenGroup.add(new Tween(curGeoCoords).to(finalGeoCoords, transitionDuration).easing(Easing.Cubic.InOut).onUpdate(setCameraPos).start());
        }
        return this;
      }
      function getGeoCoords() {
        return state.globe.toGeoCoords(state.renderObjs.cameraPosition());
      }
      function setCameraPos(_ref5) {
        var lat = _ref5.lat, lng = _ref5.lng, altitude = _ref5.altitude;
        state.renderObjs.cameraPosition(state.globe.getCoords(lat, lng, altitude));
        state.globe.setPointOfView(state.renderObjs.camera());
      }
    },
    getScreenCoords: function getScreenCoords2(state) {
      var _state$globe3;
      for (var _len = arguments.length, geoCoords = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
        geoCoords[_key - 1] = arguments[_key];
      }
      var cartesianCoords = (_state$globe3 = state.globe).getCoords.apply(_state$globe3, geoCoords);
      return state.renderObjs.getScreenCoords(cartesianCoords.x, cartesianCoords.y, cartesianCoords.z);
    },
    toGlobeCoords: function toGlobeCoords(state, x2, y2) {
      var globeIntersects = state.renderObjs.intersectingObjects(x2, y2).find(function(d2) {
        return (d2.object.__globeObjType || d2.object.parent.__globeObjType) === "globe";
      });
      if (!globeIntersects) return null;
      var _state$globe$toGeoCoo = state.globe.toGeoCoords(globeIntersects.point), lat = _state$globe$toGeoCoo.lat, lng = _state$globe$toGeoCoo.lng;
      return {
        lat,
        lng
      };
    },
    scene: function scene2(state) {
      return state.renderObjs.scene();
    },
    // Expose scene
    camera: function camera2(state) {
      return state.renderObjs.camera();
    },
    // Expose camera
    renderer: function renderer2(state) {
      return state.renderObjs.renderer();
    },
    // Expose renderer
    controls: function controls2(state) {
      return state.renderObjs.controls();
    },
    // Expose controls
    _destructor: function _destructor6(state) {
      state.globe._destructor();
      this.pauseAnimation();
      this.pointsData([]);
      this.arcsData([]);
      this.polygonsData([]);
      this.pathsData([]);
      this.heatmapsData([]);
      this.hexBinPointsData([]);
      this.hexPolygonsData([]);
      this.tilesData([]);
      this.particlesData([]);
      this.labelsData([]);
      this.htmlElementsData([]);
      this.objectsData([]);
      this.customLayerData([]);
    }
  }, linkedGlobeMethods), linkedRenderObjsMethods),
  stateInit: function stateInit6(_ref6) {
    var rendererConfig = _ref6.rendererConfig, _ref6$waitForGlobeRea = _ref6.waitForGlobeReady, waitForGlobeReady = _ref6$waitForGlobeRea === void 0 ? true : _ref6$waitForGlobeRea, globeInitConfig = _objectWithoutProperties2(_ref6, _excluded2);
    var globe2 = new threeGlobe(_objectSpread23({
      waitForGlobeReady
    }, globeInitConfig));
    return {
      globe: globe2,
      renderObjs: threeRenderObjects({
        controlType: "orbit",
        rendererConfig,
        waitForLoadComplete: waitForGlobeReady,
        extraRenderers: [new THREE2.CSS2DRenderer()]
        // Used in HTML elements layer
      }).skyRadius(globe2.getGlobeRadius() * 500).showNavInfo(false).objects([globe2]).lights([new THREE2.AmbientLight(13421772, Math.PI), new THREE2.DirectionalLight(16777215, 0.6 * Math.PI)]),
      tweenGroup: new Group2()
    };
  },
  init: function init19(domNode, state) {
    var _this = this;
    domNode.innerHTML = "";
    domNode.appendChild(state.container = document.createElement("div"));
    state.container.style.position = "relative";
    var roDomNode = document.createElement("div");
    state.container.appendChild(roDomNode);
    state.renderObjs(roDomNode);
    state.globe.rendererSize(state.renderObjs.renderer().getSize(new THREE2.Vector2()));
    this.pointOfView({
      altitude: 2.5
    });
    var globeR = state.globe.getGlobeRadius();
    var controls3 = state.renderObjs.controls();
    state.renderObjs.camera().near = 0.05;
    controls3.minDistance = globeR + Math.max(1e-3, state.renderObjs.camera().near * 1.1);
    controls3.maxDistance = globeR * 100;
    controls3.enablePan = false;
    controls3.enableDamping = true;
    controls3.dampingFactor = 0.1;
    controls3.rotateSpeed = 0.3;
    controls3.zoomSpeed = 0.3;
    controls3.zoomToCursor = true;
    controls3.addEventListener("change", function() {
      controls3.target.setScalar(0);
      var pov = _this.pointOfView();
      controls3.rotateSpeed = pov.altitude * 0.3;
      controls3.zoomSpeed = Math.sqrt(pov.altitude) * 0.5;
      state.globe.setPointOfView(state.renderObjs.camera());
      state.onZoom && state.onZoom(pov);
    });
    var getGlobeObj = function getGlobeObj2(object) {
      var obj = object;
      while (obj && !obj.hasOwnProperty("__globeObjType")) {
        obj = obj.parent;
      }
      return obj;
    };
    var dataAccessors = {
      point: function point(d2) {
        return d2;
      },
      arc: function arc(d2) {
        return d2;
      },
      polygon: function polygon(d2) {
        return d2.data;
      },
      path: function path(d2) {
        return d2;
      },
      heatmap: function heatmap(d2) {
        return d2;
      },
      hexbin: function hexbin(d2) {
        return d2;
      },
      hexPolygon: function hexPolygon(d2) {
        return d2;
      },
      tile: function tile(d2) {
        return d2;
      },
      particles: function particles(d2, intersection) {
        return !intersection || !intersection.hasOwnProperty("index") || d2.length <= intersection.index ? d2 : d2[intersection.index];
      },
      label: function label(d2) {
        return d2;
      },
      object: function object(d2) {
        return d2;
      },
      custom: function custom(d2) {
        return d2;
      }
    };
    THREE2.REVISION < 155 && (state.renderObjs.renderer().useLegacyLights = false);
    state.renderObjs.hoverFilter(function(obj) {
      var o2 = getGlobeObj(obj);
      if (!o2) return false;
      var type = o2.__globeObjType;
      if (type !== "globe" && !dataAccessors.hasOwnProperty(type)) return false;
      var d2 = dataAccessors.hasOwnProperty(type) && o2.__data ? dataAccessors[type](o2.__data) : null;
      if (["points", "hexBinPoints"].some(function(t2) {
        return t2 === type;
      }) && Array.isArray(d2)) return false;
      return state.pointerEventsFilter(o2, d2);
    }).tooltipContent(function(obj, intersection) {
      var objAccessors = {
        point: state.pointLabel,
        arc: state.arcLabel,
        polygon: state.polygonLabel,
        path: state.pathLabel,
        hexbin: state.hexLabel,
        hexPolygon: state.hexPolygonLabel,
        tile: state.tileLabel,
        particles: state.particleLabel,
        label: state.labelLabel,
        object: state.objectLabel,
        custom: state.customLayerLabel
      };
      var globeObj = getGlobeObj(obj);
      var objType = globeObj && globeObj.__globeObjType;
      return globeObj && objType && objAccessors.hasOwnProperty(objType) && dataAccessors.hasOwnProperty(objType) ? index2(objAccessors[objType])(dataAccessors[objType](globeObj.__data, intersection)) || "" : "";
    }).onHover(function(obj, _2, intersection) {
      var hoverObjFns = {
        point: state.onPointHover,
        arc: state.onArcHover,
        polygon: state.onPolygonHover,
        path: state.onPathHover,
        heatmap: state.onHeatmapHover,
        hexbin: state.onHexHover,
        hexPolygon: state.onHexPolygonHover,
        tile: state.onTileHover,
        particles: state.onParticleHover,
        label: state.onLabelHover,
        object: state.onObjectHover,
        custom: state.onCustomLayerHover
      };
      var clickObjFns = {
        globe: state.onGlobeClick,
        point: state.onPointClick,
        arc: state.onArcClick,
        polygon: state.onPolygonClick,
        path: state.onPathClick,
        heatmap: state.onHeatmapClick,
        hexbin: state.onHexClick,
        hexPolygon: state.onHexPolygonClick,
        tile: state.onTileClick,
        particles: state.onParticleClick,
        label: state.onLabelClick,
        object: state.onObjectClick,
        custom: state.onCustomLayerClick
      };
      var hoverObj = getGlobeObj(obj);
      hoverObj && !hoverObjFns.hasOwnProperty(hoverObj.__globeObjType) && (hoverObj = null);
      if (hoverObj !== state.hoverObj) {
        var prevObjType = state.hoverObj ? state.hoverObj.__globeObjType : null;
        var prevObjData = state.hoverData;
        var objType = hoverObj ? hoverObj.__globeObjType : null;
        var objData = hoverObj ? dataAccessors[objType](hoverObj.__data, intersection) : null;
        if (prevObjType && prevObjType !== objType) {
          hoverObjFns[prevObjType] && hoverObjFns[prevObjType](null, prevObjData || null);
        }
        if (objType) {
          hoverObjFns[objType] && hoverObjFns[objType](objData, prevObjType === objType ? prevObjData : null);
        }
        state.renderObjs.renderer().domElement.classList[objType && clickObjFns[objType] ? "add" : "remove"]("clickable");
        state.hoverObj = hoverObj;
        state.hoverData = objData;
      }
    }).onClick(function(obj, ev, intersection) {
      if (!obj) return;
      var objFns = {
        globe: state.onGlobeClick,
        point: state.onPointClick,
        arc: state.onArcClick,
        polygon: state.onPolygonClick,
        path: state.onPathClick,
        heatmap: state.onHeatmapClick,
        hexbin: state.onHexClick,
        hexPolygon: state.onHexPolygonClick,
        tile: state.onTileClick,
        particles: state.onParticleClick,
        label: state.onLabelClick,
        object: state.onObjectClick,
        custom: state.onCustomLayerClick
      };
      var globeObj = getGlobeObj(obj);
      var objType = globeObj.__globeObjType;
      if (globeObj && objFns.hasOwnProperty(objType) && objFns[objType]) {
        var args = [ev];
        var point = intersection !== null && intersection !== void 0 && intersection.isVector3 ? intersection : intersection === null || intersection === void 0 ? void 0 : intersection.point;
        if (objType === "globe") {
          var _this$toGeoCoords = _this.toGeoCoords(point), lat = _this$toGeoCoords.lat, lng = _this$toGeoCoords.lng;
          args.unshift({
            lat,
            lng
          });
        } else {
          args.push(_this.toGeoCoords(point));
        }
        dataAccessors.hasOwnProperty(objType) && args.unshift(dataAccessors[objType](globeObj.__data, intersection));
        objFns[objType].apply(objFns, args);
      }
    }).onRightClick(function(obj, ev, intersection) {
      if (!obj) return;
      var objFns = {
        globe: state.onGlobeRightClick,
        point: state.onPointRightClick,
        arc: state.onArcRightClick,
        polygon: state.onPolygonRightClick,
        path: state.onPathRightClick,
        heatmap: state.onHeatmapRightClick,
        hexbin: state.onHexRightClick,
        hexPolygon: state.onHexPolygonRightClick,
        tile: state.onTileRightClick,
        particles: state.onParticleRightClick,
        label: state.onLabelRightClick,
        object: state.onObjectRightClick,
        custom: state.onCustomLayerRightClick
      };
      var globeObj = getGlobeObj(obj);
      var objType = globeObj.__globeObjType;
      if (globeObj && objFns.hasOwnProperty(objType) && objFns[objType]) {
        var args = [ev];
        var point = intersection !== null && intersection !== void 0 && intersection.isVector3 ? intersection : intersection === null || intersection === void 0 ? void 0 : intersection.point;
        if (objType === "globe") {
          var _this$toGeoCoords2 = _this.toGeoCoords(point), lat = _this$toGeoCoords2.lat, lng = _this$toGeoCoords2.lng;
          args.unshift({
            lat,
            lng
          });
        } else {
          args.push(_this.toGeoCoords(point));
        }
        dataAccessors.hasOwnProperty(objType) && args.unshift(dataAccessors[objType](globeObj.__data, intersection));
        objFns[objType].apply(objFns, args);
      }
    });
    this._animationCycle();
  }
});

// node_modules/react-globe.gl/dist/react-globe.gl.mjs
var import_prop_types = __toESM(require_prop_types(), 1);
var GlobePropTypes = {
  width: import_prop_types.default.number,
  height: import_prop_types.default.number,
  globeOffset: import_prop_types.default.arrayOf(import_prop_types.default.number),
  backgroundColor: import_prop_types.default.string,
  backgroundImageUrl: import_prop_types.default.string,
  globeImageUrl: import_prop_types.default.string,
  bumpImageUrl: import_prop_types.default.string,
  globeTileEngineUrl: import_prop_types.default.func,
  showGlobe: import_prop_types.default.bool,
  showGraticules: import_prop_types.default.bool,
  showAtmosphere: import_prop_types.default.bool,
  atmosphereColor: import_prop_types.default.string,
  atmosphereAltitude: import_prop_types.default.number,
  globeMaterial: import_prop_types.default.object,
  onGlobeReady: import_prop_types.default.func,
  onGlobeClick: import_prop_types.default.func,
  onGlobeRightClick: import_prop_types.default.func,
  pointsData: import_prop_types.default.arrayOf(import_prop_types.default.object),
  pointLat: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  pointLng: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  pointColor: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  pointAltitude: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  pointRadius: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  pointResolution: import_prop_types.default.number,
  pointsMerge: import_prop_types.default.bool,
  pointsTransitionDuration: import_prop_types.default.number,
  pointLabel: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  onPointClick: import_prop_types.default.func,
  onPointRightClick: import_prop_types.default.func,
  onPointHover: import_prop_types.default.func,
  arcsData: import_prop_types.default.arrayOf(import_prop_types.default.object),
  arcStartLat: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  arcStartLng: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  arcEndLat: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  arcEndLng: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  arcColor: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.arrayOf(import_prop_types.default.string), import_prop_types.default.func]),
  arcAltitude: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  arcAltitudeAutoScale: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  arcStroke: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  arcCurveResolution: import_prop_types.default.number,
  arcCircularResolution: import_prop_types.default.number,
  arcDashLength: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  arcDashGap: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  arcDashInitialGap: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  arcDashAnimateTime: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  arcsTransitionDuration: import_prop_types.default.number,
  arcLabel: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  onArcClick: import_prop_types.default.func,
  onArcRightClick: import_prop_types.default.func,
  onArcHover: import_prop_types.default.func,
  polygonsData: import_prop_types.default.arrayOf(import_prop_types.default.object),
  polygonGeoJsonGeometry: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  polygonCapColor: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  polygonCapMaterial: import_prop_types.default.oneOfType([import_prop_types.default.object, import_prop_types.default.string, import_prop_types.default.func]),
  polygonSideColor: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  polygonSideMaterial: import_prop_types.default.oneOfType([import_prop_types.default.object, import_prop_types.default.string, import_prop_types.default.func]),
  polygonStrokeColor: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  polygonAltitude: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  polygonCapCurvatureResolution: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  polygonsTransitionDuration: import_prop_types.default.number,
  polygonLabel: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  onPolygonClick: import_prop_types.default.func,
  onPolygonRightClick: import_prop_types.default.func,
  onPolygonHover: import_prop_types.default.func,
  pathsData: import_prop_types.default.array,
  pathPoints: import_prop_types.default.oneOfType([import_prop_types.default.array, import_prop_types.default.string, import_prop_types.default.func]),
  pathPointLat: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  pathPointLng: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  pathPointAlt: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  pathResolution: import_prop_types.default.number,
  pathColor: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.arrayOf(import_prop_types.default.string), import_prop_types.default.func]),
  pathStroke: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  pathDashLength: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  pathDashGap: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  pathDashInitialGap: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  pathDashAnimateTime: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  pathTransitionDuration: import_prop_types.default.number,
  pathLabel: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  onPathClick: import_prop_types.default.func,
  onPathRightClick: import_prop_types.default.func,
  onPathHover: import_prop_types.default.func,
  heatmapsData: import_prop_types.default.array,
  heatmapPoints: import_prop_types.default.oneOfType([import_prop_types.default.array, import_prop_types.default.string, import_prop_types.default.func]),
  heatmapPointLat: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  heatmapPointLng: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  heatmapPointWeight: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  heatmapBandwidth: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  heatmapColorFn: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  heatmapColorSaturation: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  heatmapBaseAltitude: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  heatmapTopAltitude: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  heatmapsTransitionDuration: import_prop_types.default.number,
  onHeatmapClick: import_prop_types.default.func,
  onHeatmapRightClick: import_prop_types.default.func,
  onHeatmapHover: import_prop_types.default.func,
  hexBinPointsData: import_prop_types.default.arrayOf(import_prop_types.default.object),
  hexBinPointLat: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  hexBinPointLng: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  hexBinPointWeight: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  hexBinResolution: import_prop_types.default.number,
  hexMargin: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.func]),
  hexTopColor: import_prop_types.default.func,
  hexSideColor: import_prop_types.default.func,
  hexAltitude: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.func]),
  hexTopCurvatureResolution: import_prop_types.default.number,
  hexBinMerge: import_prop_types.default.bool,
  hexTransitionDuration: import_prop_types.default.number,
  hexLabel: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  onHexClick: import_prop_types.default.func,
  onHexRightClick: import_prop_types.default.func,
  onHexHover: import_prop_types.default.func,
  hexPolygonsData: import_prop_types.default.arrayOf(import_prop_types.default.object),
  hexPolygonGeoJsonGeometry: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  hexPolygonColor: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  hexPolygonAltitude: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  hexPolygonResolution: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  hexPolygonMargin: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  hexPolygonUseDots: import_prop_types.default.oneOfType([import_prop_types.default.bool, import_prop_types.default.string, import_prop_types.default.func]),
  hexPolygonCurvatureResolution: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  hexPolygonDotResolution: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  hexPolygonsTransitionDuration: import_prop_types.default.number,
  hexPolygonLabel: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  onHexPolygonClick: import_prop_types.default.func,
  onHexPolygonRightClick: import_prop_types.default.func,
  onHexPolygonHover: import_prop_types.default.func,
  tilesData: import_prop_types.default.arrayOf(import_prop_types.default.object),
  tileLat: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  tileLng: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  tileAltitude: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  tileWidth: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  tileHeight: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  tileUseGlobeProjection: import_prop_types.default.oneOfType([import_prop_types.default.bool, import_prop_types.default.string, import_prop_types.default.func]),
  tileMaterial: import_prop_types.default.oneOfType([import_prop_types.default.object, import_prop_types.default.string, import_prop_types.default.func]),
  tileCurvatureResolution: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  tilesTransitionDuration: import_prop_types.default.number,
  tileLabel: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  onTileClick: import_prop_types.default.func,
  onTileRightClick: import_prop_types.default.func,
  onTileHover: import_prop_types.default.func,
  particlesData: import_prop_types.default.arrayOf(import_prop_types.default.object),
  particlesList: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  particleLat: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  particleLng: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  particleAltitude: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  particlesSize: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  particlesSizeAttenuation: import_prop_types.default.oneOfType([import_prop_types.default.bool, import_prop_types.default.string, import_prop_types.default.func]),
  particlesColor: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  particlesTexture: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  particleLabel: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  onParticleClick: import_prop_types.default.func,
  onParticleRightClick: import_prop_types.default.func,
  onParticleHover: import_prop_types.default.func,
  ringsData: import_prop_types.default.arrayOf(import_prop_types.default.object),
  ringLat: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  ringLng: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  ringAltitude: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  ringColor: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.arrayOf(import_prop_types.default.string), import_prop_types.default.func]),
  ringResolution: import_prop_types.default.number,
  ringMaxRadius: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  ringPropagationSpeed: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  ringRepeatPeriod: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  labelsData: import_prop_types.default.arrayOf(import_prop_types.default.object),
  labelLat: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  labelLng: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  labelAltitude: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  labelRotation: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  labelText: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  labelSize: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  labelTypeFace: import_prop_types.default.object,
  labelColor: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  labelResolution: import_prop_types.default.number,
  labelIncludeDot: import_prop_types.default.oneOfType([import_prop_types.default.bool, import_prop_types.default.string, import_prop_types.default.func]),
  labelDotRadius: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  labelDotOrientation: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  labelsTransitionDuration: import_prop_types.default.number,
  labelLabel: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  onLabelClick: import_prop_types.default.func,
  onLabelRightClick: import_prop_types.default.func,
  onLabelHover: import_prop_types.default.func,
  htmlElementsData: import_prop_types.default.arrayOf(import_prop_types.default.object),
  htmlLat: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  htmlLng: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  htmlAltitude: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  htmlElement: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  htmlElementVisibilityModifier: import_prop_types.default.func,
  htmlTransitionDuration: import_prop_types.default.number,
  objectsData: import_prop_types.default.arrayOf(import_prop_types.default.object),
  objectLat: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  objectLng: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  objectAltitude: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.func]),
  objectRotation: import_prop_types.default.oneOfType([import_prop_types.default.shape({
    x: import_prop_types.default.number,
    y: import_prop_types.default.number,
    z: import_prop_types.default.number
  }), import_prop_types.default.string, import_prop_types.default.func]),
  objectFacesSurface: import_prop_types.default.oneOfType([import_prop_types.default.bool, import_prop_types.default.string, import_prop_types.default.func]),
  objectThreeObject: import_prop_types.default.oneOfType([import_prop_types.default.object, import_prop_types.default.string, import_prop_types.default.func]),
  objectLabel: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  onObjectClick: import_prop_types.default.func,
  onObjectRightClick: import_prop_types.default.func,
  onObjectHover: import_prop_types.default.func,
  customLayerData: import_prop_types.default.arrayOf(import_prop_types.default.object),
  customThreeObject: import_prop_types.default.oneOfType([import_prop_types.default.object, import_prop_types.default.string, import_prop_types.default.func]),
  customThreeObjectUpdate: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  customLayerLabel: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.func]),
  onCustomLayerClick: import_prop_types.default.func,
  onCustomLayerRightClick: import_prop_types.default.func,
  onCustomLayerHover: import_prop_types.default.func,
  enablePointerInteraction: import_prop_types.default.bool,
  pointerEventsFilter: import_prop_types.default.func,
  lineHoverPrecision: import_prop_types.default.number,
  onZoom: import_prop_types.default.func
};
var Globe2 = index4(globe, {
  methodNames: [
    // bind methods
    "pauseAnimation",
    "resumeAnimation",
    "pointOfView",
    "lights",
    "scene",
    "camera",
    "renderer",
    "postProcessingComposer",
    "controls",
    "getGlobeRadius",
    "getCoords",
    "getScreenCoords",
    "toGeoCoords",
    "toGlobeCoords"
  ],
  initPropNames: ["animateIn", "waitForGlobeReady", "rendererConfig"]
});
Globe2.displayName = "Globe";
Globe2.propTypes = GlobePropTypes;
export {
  Globe2 as default
};
//# sourceMappingURL=react-globe__gl.js.map
