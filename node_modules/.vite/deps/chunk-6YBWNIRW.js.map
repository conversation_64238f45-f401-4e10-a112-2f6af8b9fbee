{"version": 3, "sources": ["../../d3-selection/src/namespaces.js", "../../d3-selection/src/namespace.js", "../../d3-selection/src/creator.js", "../../d3-selection/src/selector.js", "../../d3-selection/src/selectorAll.js", "../../d3-selection/src/matcher.js", "../../d3-selection/src/window.js", "../../d3-selection/src/selection/style.js", "../../d3-selection/src/selection/select.js", "../../d3-selection/src/array.js", "../../d3-selection/src/selection/selectAll.js", "../../d3-selection/src/selection/selectChild.js", "../../d3-selection/src/selection/selectChildren.js", "../../d3-selection/src/selection/filter.js", "../../d3-selection/src/selection/sparse.js", "../../d3-selection/src/selection/enter.js", "../../d3-selection/src/constant.js", "../../d3-selection/src/selection/data.js", "../../d3-selection/src/selection/exit.js", "../../d3-selection/src/selection/join.js", "../../d3-selection/src/selection/merge.js", "../../d3-selection/src/selection/order.js", "../../d3-selection/src/selection/sort.js", "../../d3-selection/src/selection/call.js", "../../d3-selection/src/selection/nodes.js", "../../d3-selection/src/selection/node.js", "../../d3-selection/src/selection/size.js", "../../d3-selection/src/selection/empty.js", "../../d3-selection/src/selection/each.js", "../../d3-selection/src/selection/attr.js", "../../d3-selection/src/selection/property.js", "../../d3-selection/src/selection/classed.js", "../../d3-selection/src/selection/text.js", "../../d3-selection/src/selection/html.js", "../../d3-selection/src/selection/raise.js", "../../d3-selection/src/selection/lower.js", "../../d3-selection/src/selection/append.js", "../../d3-selection/src/selection/insert.js", "../../d3-selection/src/selection/remove.js", "../../d3-selection/src/selection/clone.js", "../../d3-selection/src/selection/datum.js", "../../d3-selection/src/selection/on.js", "../../d3-selection/src/selection/dispatch.js", "../../d3-selection/src/selection/iterator.js", "../../d3-selection/src/selection/index.js", "../../d3-selection/src/select.js", "../../d3-selection/src/create.js", "../../d3-selection/src/local.js", "../../d3-selection/src/sourceEvent.js", "../../d3-selection/src/pointer.js", "../../d3-selection/src/pointers.js", "../../d3-selection/src/selectAll.js"], "sourcesContent": ["export var xhtml = \"http://www.w3.org/1999/xhtml\";\n\nexport default {\n  svg: \"http://www.w3.org/2000/svg\",\n  xhtml: xhtml,\n  xlink: \"http://www.w3.org/1999/xlink\",\n  xml: \"http://www.w3.org/XML/1998/namespace\",\n  xmlns: \"http://www.w3.org/2000/xmlns/\"\n};\n", "import namespaces from \"./namespaces.js\";\n\nexport default function(name) {\n  var prefix = name += \"\", i = prefix.indexOf(\":\");\n  if (i >= 0 && (prefix = name.slice(0, i)) !== \"xmlns\") name = name.slice(i + 1);\n  return namespaces.hasOwnProperty(prefix) ? {space: namespaces[prefix], local: name} : name; // eslint-disable-line no-prototype-builtins\n}\n", "import namespace from \"./namespace.js\";\nimport {xhtml} from \"./namespaces.js\";\n\nfunction creatorInherit(name) {\n  return function() {\n    var document = this.ownerDocument,\n        uri = this.namespaceURI;\n    return uri === xhtml && document.documentElement.namespaceURI === xhtml\n        ? document.createElement(name)\n        : document.createElementNS(uri, name);\n  };\n}\n\nfunction creatorFixed(fullname) {\n  return function() {\n    return this.ownerDocument.createElementNS(fullname.space, fullname.local);\n  };\n}\n\nexport default function(name) {\n  var fullname = namespace(name);\n  return (fullname.local\n      ? creatorFixed\n      : creatorInherit)(fullname);\n}\n", "function none() {}\n\nexport default function(selector) {\n  return selector == null ? none : function() {\n    return this.querySelector(selector);\n  };\n}\n", "function empty() {\n  return [];\n}\n\nexport default function(selector) {\n  return selector == null ? empty : function() {\n    return this.querySelectorAll(selector);\n  };\n}\n", "export default function(selector) {\n  return function() {\n    return this.matches(selector);\n  };\n}\n\nexport function childMatcher(selector) {\n  return function(node) {\n    return node.matches(selector);\n  };\n}\n\n", "export default function(node) {\n  return (node.ownerDocument && node.ownerDocument.defaultView) // node is a Node\n      || (node.document && node) // node is a Window\n      || node.defaultView; // node is a Document\n}\n", "import defaultView from \"../window.js\";\n\nfunction styleRemove(name) {\n  return function() {\n    this.style.removeProperty(name);\n  };\n}\n\nfunction styleConstant(name, value, priority) {\n  return function() {\n    this.style.setProperty(name, value, priority);\n  };\n}\n\nfunction styleFunction(name, value, priority) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.style.removeProperty(name);\n    else this.style.setProperty(name, v, priority);\n  };\n}\n\nexport default function(name, value, priority) {\n  return arguments.length > 1\n      ? this.each((value == null\n            ? styleRemove : typeof value === \"function\"\n            ? styleFunction\n            : styleConstant)(name, value, priority == null ? \"\" : priority))\n      : styleValue(this.node(), name);\n}\n\nexport function styleValue(node, name) {\n  return node.style.getPropertyValue(name)\n      || defaultView(node).getComputedStyle(node, null).getPropertyValue(name);\n}\n", "import {Selection} from \"./index.js\";\nimport selector from \"../selector.js\";\n\nexport default function(select) {\n  if (typeof select !== \"function\") select = selector(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = new Array(n), node, subnode, i = 0; i < n; ++i) {\n      if ((node = group[i]) && (subnode = select.call(node, node.__data__, i, group))) {\n        if (\"__data__\" in node) subnode.__data__ = node.__data__;\n        subgroup[i] = subnode;\n      }\n    }\n  }\n\n  return new Selection(subgroups, this._parents);\n}\n", "// Given something array like (or null), returns something that is strictly an\n// array. This is used to ensure that array-like objects passed to d3.selectAll\n// or selection.selectAll are converted into proper arrays when creating a\n// selection; we don’t ever want to create a selection backed by a live\n// HTMLCollection or NodeList. However, note that selection.selectAll will use a\n// static NodeList as a group, since it safely derived from querySelectorAll.\nexport default function array(x) {\n  return x == null ? [] : Array.isArray(x) ? x : Array.from(x);\n}\n", "import {Selection} from \"./index.js\";\nimport array from \"../array.js\";\nimport selectorAll from \"../selectorAll.js\";\n\nfunction arrayAll(select) {\n  return function() {\n    return array(select.apply(this, arguments));\n  };\n}\n\nexport default function(select) {\n  if (typeof select === \"function\") select = arrayAll(select);\n  else select = selectorAll(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = [], parents = [], j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        subgroups.push(select.call(node, node.__data__, i, group));\n        parents.push(node);\n      }\n    }\n  }\n\n  return new Selection(subgroups, parents);\n}\n", "import {childMatcher} from \"../matcher.js\";\n\nvar find = Array.prototype.find;\n\nfunction childFind(match) {\n  return function() {\n    return find.call(this.children, match);\n  };\n}\n\nfunction childFirst() {\n  return this.firstElementChild;\n}\n\nexport default function(match) {\n  return this.select(match == null ? childFirst\n      : childFind(typeof match === \"function\" ? match : childMatcher(match)));\n}\n", "import {childMatcher} from \"../matcher.js\";\n\nvar filter = Array.prototype.filter;\n\nfunction children() {\n  return Array.from(this.children);\n}\n\nfunction childrenFilter(match) {\n  return function() {\n    return filter.call(this.children, match);\n  };\n}\n\nexport default function(match) {\n  return this.selectAll(match == null ? children\n      : childrenFilter(typeof match === \"function\" ? match : childMatcher(match)));\n}\n", "import {Selection} from \"./index.js\";\nimport matcher from \"../matcher.js\";\n\nexport default function(match) {\n  if (typeof match !== \"function\") match = matcher(match);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = [], node, i = 0; i < n; ++i) {\n      if ((node = group[i]) && match.call(node, node.__data__, i, group)) {\n        subgroup.push(node);\n      }\n    }\n  }\n\n  return new Selection(subgroups, this._parents);\n}\n", "export default function(update) {\n  return new Array(update.length);\n}\n", "import sparse from \"./sparse.js\";\nimport {Selection} from \"./index.js\";\n\nexport default function() {\n  return new Selection(this._enter || this._groups.map(sparse), this._parents);\n}\n\nexport function EnterNode(parent, datum) {\n  this.ownerDocument = parent.ownerDocument;\n  this.namespaceURI = parent.namespaceURI;\n  this._next = null;\n  this._parent = parent;\n  this.__data__ = datum;\n}\n\nEnterNode.prototype = {\n  constructor: EnterNode,\n  appendChild: function(child) { return this._parent.insertBefore(child, this._next); },\n  insertBefore: function(child, next) { return this._parent.insertBefore(child, next); },\n  querySelector: function(selector) { return this._parent.querySelector(selector); },\n  querySelectorAll: function(selector) { return this._parent.querySelectorAll(selector); }\n};\n", "export default function(x) {\n  return function() {\n    return x;\n  };\n}\n", "import {Selection} from \"./index.js\";\nimport {EnterNode} from \"./enter.js\";\nimport constant from \"../constant.js\";\n\nfunction bindIndex(parent, group, enter, update, exit, data) {\n  var i = 0,\n      node,\n      groupLength = group.length,\n      dataLength = data.length;\n\n  // Put any non-null nodes that fit into update.\n  // Put any null nodes into enter.\n  // Put any remaining data into enter.\n  for (; i < dataLength; ++i) {\n    if (node = group[i]) {\n      node.__data__ = data[i];\n      update[i] = node;\n    } else {\n      enter[i] = new EnterNode(parent, data[i]);\n    }\n  }\n\n  // Put any non-null nodes that don’t fit into exit.\n  for (; i < groupLength; ++i) {\n    if (node = group[i]) {\n      exit[i] = node;\n    }\n  }\n}\n\nfunction bindKey(parent, group, enter, update, exit, data, key) {\n  var i,\n      node,\n      nodeByKeyValue = new Map,\n      groupLength = group.length,\n      dataLength = data.length,\n      keyValues = new Array(groupLength),\n      keyValue;\n\n  // Compute the key for each node.\n  // If multiple nodes have the same key, the duplicates are added to exit.\n  for (i = 0; i < groupLength; ++i) {\n    if (node = group[i]) {\n      keyValues[i] = keyValue = key.call(node, node.__data__, i, group) + \"\";\n      if (nodeByKeyValue.has(keyValue)) {\n        exit[i] = node;\n      } else {\n        nodeByKeyValue.set(keyValue, node);\n      }\n    }\n  }\n\n  // Compute the key for each datum.\n  // If there a node associated with this key, join and add it to update.\n  // If there is not (or the key is a duplicate), add it to enter.\n  for (i = 0; i < dataLength; ++i) {\n    keyValue = key.call(parent, data[i], i, data) + \"\";\n    if (node = nodeByKeyValue.get(keyValue)) {\n      update[i] = node;\n      node.__data__ = data[i];\n      nodeByKeyValue.delete(keyValue);\n    } else {\n      enter[i] = new EnterNode(parent, data[i]);\n    }\n  }\n\n  // Add any remaining nodes that were not bound to data to exit.\n  for (i = 0; i < groupLength; ++i) {\n    if ((node = group[i]) && (nodeByKeyValue.get(keyValues[i]) === node)) {\n      exit[i] = node;\n    }\n  }\n}\n\nfunction datum(node) {\n  return node.__data__;\n}\n\nexport default function(value, key) {\n  if (!arguments.length) return Array.from(this, datum);\n\n  var bind = key ? bindKey : bindIndex,\n      parents = this._parents,\n      groups = this._groups;\n\n  if (typeof value !== \"function\") value = constant(value);\n\n  for (var m = groups.length, update = new Array(m), enter = new Array(m), exit = new Array(m), j = 0; j < m; ++j) {\n    var parent = parents[j],\n        group = groups[j],\n        groupLength = group.length,\n        data = arraylike(value.call(parent, parent && parent.__data__, j, parents)),\n        dataLength = data.length,\n        enterGroup = enter[j] = new Array(dataLength),\n        updateGroup = update[j] = new Array(dataLength),\n        exitGroup = exit[j] = new Array(groupLength);\n\n    bind(parent, group, enterGroup, updateGroup, exitGroup, data, key);\n\n    // Now connect the enter nodes to their following update node, such that\n    // appendChild can insert the materialized enter node before this node,\n    // rather than at the end of the parent node.\n    for (var i0 = 0, i1 = 0, previous, next; i0 < dataLength; ++i0) {\n      if (previous = enterGroup[i0]) {\n        if (i0 >= i1) i1 = i0 + 1;\n        while (!(next = updateGroup[i1]) && ++i1 < dataLength);\n        previous._next = next || null;\n      }\n    }\n  }\n\n  update = new Selection(update, parents);\n  update._enter = enter;\n  update._exit = exit;\n  return update;\n}\n\n// Given some data, this returns an array-like view of it: an object that\n// exposes a length property and allows numeric indexing. Note that unlike\n// selectAll, this isn’t worried about “live” collections because the resulting\n// array will only be used briefly while data is being bound. (It is possible to\n// cause the data to change while iterating by using a key function, but please\n// don’t; we’d rather avoid a gratuitous copy.)\nfunction arraylike(data) {\n  return typeof data === \"object\" && \"length\" in data\n    ? data // Array, TypedArray, NodeList, array-like\n    : Array.from(data); // Map, Set, iterable, string, or anything else\n}\n", "import sparse from \"./sparse.js\";\nimport {Selection} from \"./index.js\";\n\nexport default function() {\n  return new Selection(this._exit || this._groups.map(sparse), this._parents);\n}\n", "export default function(onenter, onupdate, onexit) {\n  var enter = this.enter(), update = this, exit = this.exit();\n  if (typeof onenter === \"function\") {\n    enter = onenter(enter);\n    if (enter) enter = enter.selection();\n  } else {\n    enter = enter.append(onenter + \"\");\n  }\n  if (onupdate != null) {\n    update = onupdate(update);\n    if (update) update = update.selection();\n  }\n  if (onexit == null) exit.remove(); else onexit(exit);\n  return enter && update ? enter.merge(update).order() : update;\n}\n", "import {Selection} from \"./index.js\";\n\nexport default function(context) {\n  var selection = context.selection ? context.selection() : context;\n\n  for (var groups0 = this._groups, groups1 = selection._groups, m0 = groups0.length, m1 = groups1.length, m = Math.min(m0, m1), merges = new Array(m0), j = 0; j < m; ++j) {\n    for (var group0 = groups0[j], group1 = groups1[j], n = group0.length, merge = merges[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group0[i] || group1[i]) {\n        merge[i] = node;\n      }\n    }\n  }\n\n  for (; j < m0; ++j) {\n    merges[j] = groups0[j];\n  }\n\n  return new Selection(merges, this._parents);\n}\n", "export default function() {\n\n  for (var groups = this._groups, j = -1, m = groups.length; ++j < m;) {\n    for (var group = groups[j], i = group.length - 1, next = group[i], node; --i >= 0;) {\n      if (node = group[i]) {\n        if (next && node.compareDocumentPosition(next) ^ 4) next.parentNode.insertBefore(node, next);\n        next = node;\n      }\n    }\n  }\n\n  return this;\n}\n", "import {Selection} from \"./index.js\";\n\nexport default function(compare) {\n  if (!compare) compare = ascending;\n\n  function compareNode(a, b) {\n    return a && b ? compare(a.__data__, b.__data__) : !a - !b;\n  }\n\n  for (var groups = this._groups, m = groups.length, sortgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, sortgroup = sortgroups[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        sortgroup[i] = node;\n      }\n    }\n    sortgroup.sort(compareNode);\n  }\n\n  return new Selection(sortgroups, this._parents).order();\n}\n\nfunction ascending(a, b) {\n  return a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\n", "export default function() {\n  var callback = arguments[0];\n  arguments[0] = this;\n  callback.apply(null, arguments);\n  return this;\n}\n", "export default function() {\n  return Array.from(this);\n}\n", "export default function() {\n\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length; i < n; ++i) {\n      var node = group[i];\n      if (node) return node;\n    }\n  }\n\n  return null;\n}\n", "export default function() {\n  let size = 0;\n  for (const node of this) ++size; // eslint-disable-line no-unused-vars\n  return size;\n}\n", "export default function() {\n  return !this.node();\n}\n", "export default function(callback) {\n\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length, node; i < n; ++i) {\n      if (node = group[i]) callback.call(node, node.__data__, i, group);\n    }\n  }\n\n  return this;\n}\n", "import namespace from \"../namespace.js\";\n\nfunction attrRemove(name) {\n  return function() {\n    this.removeAttribute(name);\n  };\n}\n\nfunction attrRemoveNS(fullname) {\n  return function() {\n    this.removeAttributeNS(fullname.space, fullname.local);\n  };\n}\n\nfunction attrConstant(name, value) {\n  return function() {\n    this.setAttribute(name, value);\n  };\n}\n\nfunction attrConstantNS(fullname, value) {\n  return function() {\n    this.setAttributeNS(fullname.space, fullname.local, value);\n  };\n}\n\nfunction attrFunction(name, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.removeAttribute(name);\n    else this.setAttribute(name, v);\n  };\n}\n\nfunction attrFunctionNS(fullname, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.removeAttributeNS(fullname.space, fullname.local);\n    else this.setAttributeNS(fullname.space, fullname.local, v);\n  };\n}\n\nexport default function(name, value) {\n  var fullname = namespace(name);\n\n  if (arguments.length < 2) {\n    var node = this.node();\n    return fullname.local\n        ? node.getAttributeNS(fullname.space, fullname.local)\n        : node.getAttribute(fullname);\n  }\n\n  return this.each((value == null\n      ? (fullname.local ? attrRemoveNS : attrRemove) : (typeof value === \"function\"\n      ? (fullname.local ? attrFunctionNS : attrFunction)\n      : (fullname.local ? attrConstantNS : attrConstant)))(fullname, value));\n}\n", "function propertyRemove(name) {\n  return function() {\n    delete this[name];\n  };\n}\n\nfunction propertyConstant(name, value) {\n  return function() {\n    this[name] = value;\n  };\n}\n\nfunction propertyFunction(name, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) delete this[name];\n    else this[name] = v;\n  };\n}\n\nexport default function(name, value) {\n  return arguments.length > 1\n      ? this.each((value == null\n          ? propertyRemove : typeof value === \"function\"\n          ? propertyFunction\n          : propertyConstant)(name, value))\n      : this.node()[name];\n}\n", "function classArray(string) {\n  return string.trim().split(/^|\\s+/);\n}\n\nfunction classList(node) {\n  return node.classList || new ClassList(node);\n}\n\nfunction ClassList(node) {\n  this._node = node;\n  this._names = classArray(node.getAttribute(\"class\") || \"\");\n}\n\nClassList.prototype = {\n  add: function(name) {\n    var i = this._names.indexOf(name);\n    if (i < 0) {\n      this._names.push(name);\n      this._node.setAttribute(\"class\", this._names.join(\" \"));\n    }\n  },\n  remove: function(name) {\n    var i = this._names.indexOf(name);\n    if (i >= 0) {\n      this._names.splice(i, 1);\n      this._node.setAttribute(\"class\", this._names.join(\" \"));\n    }\n  },\n  contains: function(name) {\n    return this._names.indexOf(name) >= 0;\n  }\n};\n\nfunction classedAdd(node, names) {\n  var list = classList(node), i = -1, n = names.length;\n  while (++i < n) list.add(names[i]);\n}\n\nfunction classedRemove(node, names) {\n  var list = classList(node), i = -1, n = names.length;\n  while (++i < n) list.remove(names[i]);\n}\n\nfunction classedTrue(names) {\n  return function() {\n    classedAdd(this, names);\n  };\n}\n\nfunction classedFalse(names) {\n  return function() {\n    classedRemove(this, names);\n  };\n}\n\nfunction classedFunction(names, value) {\n  return function() {\n    (value.apply(this, arguments) ? classedAdd : classedRemove)(this, names);\n  };\n}\n\nexport default function(name, value) {\n  var names = classArray(name + \"\");\n\n  if (arguments.length < 2) {\n    var list = classList(this.node()), i = -1, n = names.length;\n    while (++i < n) if (!list.contains(names[i])) return false;\n    return true;\n  }\n\n  return this.each((typeof value === \"function\"\n      ? classedFunction : value\n      ? classedTrue\n      : classedFalse)(names, value));\n}\n", "function textRemove() {\n  this.textContent = \"\";\n}\n\nfunction textConstant(value) {\n  return function() {\n    this.textContent = value;\n  };\n}\n\nfunction textFunction(value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    this.textContent = v == null ? \"\" : v;\n  };\n}\n\nexport default function(value) {\n  return arguments.length\n      ? this.each(value == null\n          ? textRemove : (typeof value === \"function\"\n          ? textFunction\n          : textConstant)(value))\n      : this.node().textContent;\n}\n", "function htmlRemove() {\n  this.innerHTML = \"\";\n}\n\nfunction htmlConstant(value) {\n  return function() {\n    this.innerHTML = value;\n  };\n}\n\nfunction htmlFunction(value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    this.innerHTML = v == null ? \"\" : v;\n  };\n}\n\nexport default function(value) {\n  return arguments.length\n      ? this.each(value == null\n          ? htmlRemove : (typeof value === \"function\"\n          ? htmlFunction\n          : htmlConstant)(value))\n      : this.node().innerHTML;\n}\n", "function raise() {\n  if (this.nextSibling) this.parentNode.appendChild(this);\n}\n\nexport default function() {\n  return this.each(raise);\n}\n", "function lower() {\n  if (this.previousSibling) this.parentNode.insertBefore(this, this.parentNode.firstChild);\n}\n\nexport default function() {\n  return this.each(lower);\n}\n", "import creator from \"../creator.js\";\n\nexport default function(name) {\n  var create = typeof name === \"function\" ? name : creator(name);\n  return this.select(function() {\n    return this.appendChild(create.apply(this, arguments));\n  });\n}\n", "import creator from \"../creator.js\";\nimport selector from \"../selector.js\";\n\nfunction constantNull() {\n  return null;\n}\n\nexport default function(name, before) {\n  var create = typeof name === \"function\" ? name : creator(name),\n      select = before == null ? constantNull : typeof before === \"function\" ? before : selector(before);\n  return this.select(function() {\n    return this.insertBefore(create.apply(this, arguments), select.apply(this, arguments) || null);\n  });\n}\n", "function remove() {\n  var parent = this.parentNode;\n  if (parent) parent.removeChild(this);\n}\n\nexport default function() {\n  return this.each(remove);\n}\n", "function selection_cloneShallow() {\n  var clone = this.cloneNode(false), parent = this.parentNode;\n  return parent ? parent.insertBefore(clone, this.nextSibling) : clone;\n}\n\nfunction selection_cloneDeep() {\n  var clone = this.cloneNode(true), parent = this.parentNode;\n  return parent ? parent.insertBefore(clone, this.nextSibling) : clone;\n}\n\nexport default function(deep) {\n  return this.select(deep ? selection_cloneDeep : selection_cloneShallow);\n}\n", "export default function(value) {\n  return arguments.length\n      ? this.property(\"__data__\", value)\n      : this.node().__data__;\n}\n", "function contextListener(listener) {\n  return function(event) {\n    listener.call(this, event, this.__data__);\n  };\n}\n\nfunction parseTypenames(typenames) {\n  return typenames.trim().split(/^|\\s+/).map(function(t) {\n    var name = \"\", i = t.indexOf(\".\");\n    if (i >= 0) name = t.slice(i + 1), t = t.slice(0, i);\n    return {type: t, name: name};\n  });\n}\n\nfunction onRemove(typename) {\n  return function() {\n    var on = this.__on;\n    if (!on) return;\n    for (var j = 0, i = -1, m = on.length, o; j < m; ++j) {\n      if (o = on[j], (!typename.type || o.type === typename.type) && o.name === typename.name) {\n        this.removeEventListener(o.type, o.listener, o.options);\n      } else {\n        on[++i] = o;\n      }\n    }\n    if (++i) on.length = i;\n    else delete this.__on;\n  };\n}\n\nfunction onAdd(typename, value, options) {\n  return function() {\n    var on = this.__on, o, listener = contextListener(value);\n    if (on) for (var j = 0, m = on.length; j < m; ++j) {\n      if ((o = on[j]).type === typename.type && o.name === typename.name) {\n        this.removeEventListener(o.type, o.listener, o.options);\n        this.addEventListener(o.type, o.listener = listener, o.options = options);\n        o.value = value;\n        return;\n      }\n    }\n    this.addEventListener(typename.type, listener, options);\n    o = {type: typename.type, name: typename.name, value: value, listener: listener, options: options};\n    if (!on) this.__on = [o];\n    else on.push(o);\n  };\n}\n\nexport default function(typename, value, options) {\n  var typenames = parseTypenames(typename + \"\"), i, n = typenames.length, t;\n\n  if (arguments.length < 2) {\n    var on = this.node().__on;\n    if (on) for (var j = 0, m = on.length, o; j < m; ++j) {\n      for (i = 0, o = on[j]; i < n; ++i) {\n        if ((t = typenames[i]).type === o.type && t.name === o.name) {\n          return o.value;\n        }\n      }\n    }\n    return;\n  }\n\n  on = value ? onAdd : onRemove;\n  for (i = 0; i < n; ++i) this.each(on(typenames[i], value, options));\n  return this;\n}\n", "import defaultView from \"../window.js\";\n\nfunction dispatchEvent(node, type, params) {\n  var window = defaultView(node),\n      event = window.CustomEvent;\n\n  if (typeof event === \"function\") {\n    event = new event(type, params);\n  } else {\n    event = window.document.createEvent(\"Event\");\n    if (params) event.initEvent(type, params.bubbles, params.cancelable), event.detail = params.detail;\n    else event.initEvent(type, false, false);\n  }\n\n  node.dispatchEvent(event);\n}\n\nfunction dispatchConstant(type, params) {\n  return function() {\n    return dispatchEvent(this, type, params);\n  };\n}\n\nfunction dispatchFunction(type, params) {\n  return function() {\n    return dispatchEvent(this, type, params.apply(this, arguments));\n  };\n}\n\nexport default function(type, params) {\n  return this.each((typeof params === \"function\"\n      ? dispatchFunction\n      : dispatchConstant)(type, params));\n}\n", "export default function*() {\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length, node; i < n; ++i) {\n      if (node = group[i]) yield node;\n    }\n  }\n}\n", "import selection_select from \"./select.js\";\nimport selection_selectAll from \"./selectAll.js\";\nimport selection_selectChild from \"./selectChild.js\";\nimport selection_selectChildren from \"./selectChildren.js\";\nimport selection_filter from \"./filter.js\";\nimport selection_data from \"./data.js\";\nimport selection_enter from \"./enter.js\";\nimport selection_exit from \"./exit.js\";\nimport selection_join from \"./join.js\";\nimport selection_merge from \"./merge.js\";\nimport selection_order from \"./order.js\";\nimport selection_sort from \"./sort.js\";\nimport selection_call from \"./call.js\";\nimport selection_nodes from \"./nodes.js\";\nimport selection_node from \"./node.js\";\nimport selection_size from \"./size.js\";\nimport selection_empty from \"./empty.js\";\nimport selection_each from \"./each.js\";\nimport selection_attr from \"./attr.js\";\nimport selection_style from \"./style.js\";\nimport selection_property from \"./property.js\";\nimport selection_classed from \"./classed.js\";\nimport selection_text from \"./text.js\";\nimport selection_html from \"./html.js\";\nimport selection_raise from \"./raise.js\";\nimport selection_lower from \"./lower.js\";\nimport selection_append from \"./append.js\";\nimport selection_insert from \"./insert.js\";\nimport selection_remove from \"./remove.js\";\nimport selection_clone from \"./clone.js\";\nimport selection_datum from \"./datum.js\";\nimport selection_on from \"./on.js\";\nimport selection_dispatch from \"./dispatch.js\";\nimport selection_iterator from \"./iterator.js\";\n\nexport var root = [null];\n\nexport function Selection(groups, parents) {\n  this._groups = groups;\n  this._parents = parents;\n}\n\nfunction selection() {\n  return new Selection([[document.documentElement]], root);\n}\n\nfunction selection_selection() {\n  return this;\n}\n\nSelection.prototype = selection.prototype = {\n  constructor: Selection,\n  select: selection_select,\n  selectAll: selection_selectAll,\n  selectChild: selection_selectChild,\n  selectChildren: selection_selectChildren,\n  filter: selection_filter,\n  data: selection_data,\n  enter: selection_enter,\n  exit: selection_exit,\n  join: selection_join,\n  merge: selection_merge,\n  selection: selection_selection,\n  order: selection_order,\n  sort: selection_sort,\n  call: selection_call,\n  nodes: selection_nodes,\n  node: selection_node,\n  size: selection_size,\n  empty: selection_empty,\n  each: selection_each,\n  attr: selection_attr,\n  style: selection_style,\n  property: selection_property,\n  classed: selection_classed,\n  text: selection_text,\n  html: selection_html,\n  raise: selection_raise,\n  lower: selection_lower,\n  append: selection_append,\n  insert: selection_insert,\n  remove: selection_remove,\n  clone: selection_clone,\n  datum: selection_datum,\n  on: selection_on,\n  dispatch: selection_dispatch,\n  [Symbol.iterator]: selection_iterator\n};\n\nexport default selection;\n", "import {Selection, root} from \"./selection/index.js\";\n\nexport default function(selector) {\n  return typeof selector === \"string\"\n      ? new Selection([[document.querySelector(selector)]], [document.documentElement])\n      : new Selection([[selector]], root);\n}\n", "import creator from \"./creator.js\";\nimport select from \"./select.js\";\n\nexport default function(name) {\n  return select(creator(name).call(document.documentElement));\n}\n", "var nextId = 0;\n\nexport default function local() {\n  return new Local;\n}\n\nfunction Local() {\n  this._ = \"@\" + (++nextId).toString(36);\n}\n\nLocal.prototype = local.prototype = {\n  constructor: Local,\n  get: function(node) {\n    var id = this._;\n    while (!(id in node)) if (!(node = node.parentNode)) return;\n    return node[id];\n  },\n  set: function(node, value) {\n    return node[this._] = value;\n  },\n  remove: function(node) {\n    return this._ in node && delete node[this._];\n  },\n  toString: function() {\n    return this._;\n  }\n};\n", "export default function(event) {\n  let sourceEvent;\n  while (sourceEvent = event.sourceEvent) event = sourceEvent;\n  return event;\n}\n", "import sourceEvent from \"./sourceEvent.js\";\n\nexport default function(event, node) {\n  event = sourceEvent(event);\n  if (node === undefined) node = event.currentTarget;\n  if (node) {\n    var svg = node.ownerSVGElement || node;\n    if (svg.createSVGPoint) {\n      var point = svg.createSVGPoint();\n      point.x = event.clientX, point.y = event.clientY;\n      point = point.matrixTransform(node.getScreenCTM().inverse());\n      return [point.x, point.y];\n    }\n    if (node.getBoundingClientRect) {\n      var rect = node.getBoundingClientRect();\n      return [event.clientX - rect.left - node.clientLeft, event.clientY - rect.top - node.clientTop];\n    }\n  }\n  return [event.pageX, event.pageY];\n}\n", "import pointer from \"./pointer.js\";\nimport sourceEvent from \"./sourceEvent.js\";\n\nexport default function(events, node) {\n  if (events.target) { // i.e., instanceof Event, not TouchList or iterable\n    events = sourceEvent(events);\n    if (node === undefined) node = events.currentTarget;\n    events = events.touches || [events];\n  }\n  return Array.from(events, event => pointer(event, node));\n}\n", "import array from \"./array.js\";\nimport {Selection, root} from \"./selection/index.js\";\n\nexport default function(selector) {\n  return typeof selector === \"string\"\n      ? new Selection([document.querySelectorAll(selector)], [document.documentElement])\n      : new Selection([array(selector)], root);\n}\n"], "mappings": ";AAAO,IAAI,QAAQ;AAEnB,IAAO,qBAAQ;AAAA,EACb,KAAK;AAAA,EACL;AAAA,EACA,OAAO;AAAA,EACP,KAAK;AAAA,EACL,OAAO;AACT;;;ACNe,SAAR,kBAAiB,MAAM;AAC5B,MAAI,SAAS,QAAQ,IAAI,IAAI,OAAO,QAAQ,GAAG;AAC/C,MAAI,KAAK,MAAM,SAAS,KAAK,MAAM,GAAG,CAAC,OAAO,QAAS,QAAO,KAAK,MAAM,IAAI,CAAC;AAC9E,SAAO,mBAAW,eAAe,MAAM,IAAI,EAAC,OAAO,mBAAW,MAAM,GAAG,OAAO,KAAI,IAAI;AACxF;;;ACHA,SAAS,eAAe,MAAM;AAC5B,SAAO,WAAW;AAChB,QAAIA,YAAW,KAAK,eAChB,MAAM,KAAK;AACf,WAAO,QAAQ,SAASA,UAAS,gBAAgB,iBAAiB,QAC5DA,UAAS,cAAc,IAAI,IAC3BA,UAAS,gBAAgB,KAAK,IAAI;AAAA,EAC1C;AACF;AAEA,SAAS,aAAa,UAAU;AAC9B,SAAO,WAAW;AAChB,WAAO,KAAK,cAAc,gBAAgB,SAAS,OAAO,SAAS,KAAK;AAAA,EAC1E;AACF;AAEe,SAAR,gBAAiB,MAAM;AAC5B,MAAI,WAAW,kBAAU,IAAI;AAC7B,UAAQ,SAAS,QACX,eACA,gBAAgB,QAAQ;AAChC;;;ACxBA,SAAS,OAAO;AAAC;AAEF,SAAR,iBAAiB,UAAU;AAChC,SAAO,YAAY,OAAO,OAAO,WAAW;AAC1C,WAAO,KAAK,cAAc,QAAQ;AAAA,EACpC;AACF;;;ACNA,SAAS,QAAQ;AACf,SAAO,CAAC;AACV;AAEe,SAAR,oBAAiB,UAAU;AAChC,SAAO,YAAY,OAAO,QAAQ,WAAW;AAC3C,WAAO,KAAK,iBAAiB,QAAQ;AAAA,EACvC;AACF;;;ACRe,SAAR,gBAAiB,UAAU;AAChC,SAAO,WAAW;AAChB,WAAO,KAAK,QAAQ,QAAQ;AAAA,EAC9B;AACF;AAEO,SAAS,aAAa,UAAU;AACrC,SAAO,SAAS,MAAM;AACpB,WAAO,KAAK,QAAQ,QAAQ;AAAA,EAC9B;AACF;;;ACVe,SAAR,eAAiB,MAAM;AAC5B,SAAQ,KAAK,iBAAiB,KAAK,cAAc,eACzC,KAAK,YAAY,QAClB,KAAK;AACd;;;ACFA,SAAS,YAAY,MAAM;AACzB,SAAO,WAAW;AAChB,SAAK,MAAM,eAAe,IAAI;AAAA,EAChC;AACF;AAEA,SAAS,cAAc,MAAM,OAAO,UAAU;AAC5C,SAAO,WAAW;AAChB,SAAK,MAAM,YAAY,MAAM,OAAO,QAAQ;AAAA,EAC9C;AACF;AAEA,SAAS,cAAc,MAAM,OAAO,UAAU;AAC5C,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,KAAK,KAAM,MAAK,MAAM,eAAe,IAAI;AAAA,QACxC,MAAK,MAAM,YAAY,MAAM,GAAG,QAAQ;AAAA,EAC/C;AACF;AAEe,SAAR,cAAiB,MAAM,OAAO,UAAU;AAC7C,SAAO,UAAU,SAAS,IACpB,KAAK,MAAM,SAAS,OACd,cAAc,OAAO,UAAU,aAC/B,gBACA,eAAe,MAAM,OAAO,YAAY,OAAO,KAAK,QAAQ,CAAC,IACnE,WAAW,KAAK,KAAK,GAAG,IAAI;AACpC;AAEO,SAAS,WAAW,MAAM,MAAM;AACrC,SAAO,KAAK,MAAM,iBAAiB,IAAI,KAChC,eAAY,IAAI,EAAE,iBAAiB,MAAM,IAAI,EAAE,iBAAiB,IAAI;AAC7E;;;AC/Be,SAAR,eAAiB,QAAQ;AAC9B,MAAI,OAAO,WAAW,WAAY,UAAS,iBAAS,MAAM;AAE1D,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,YAAY,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC9F,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,WAAW,UAAU,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,MAAM,SAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtH,WAAK,OAAO,MAAM,CAAC,OAAO,UAAU,OAAO,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,IAAI;AAC/E,YAAI,cAAc,KAAM,SAAQ,WAAW,KAAK;AAChD,iBAAS,CAAC,IAAI;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,UAAU,WAAW,KAAK,QAAQ;AAC/C;;;ACVe,SAAR,MAAuB,GAAG;AAC/B,SAAO,KAAK,OAAO,CAAC,IAAI,MAAM,QAAQ,CAAC,IAAI,IAAI,MAAM,KAAK,CAAC;AAC7D;;;ACJA,SAAS,SAAS,QAAQ;AACxB,SAAO,WAAW;AAChB,WAAO,MAAM,OAAO,MAAM,MAAM,SAAS,CAAC;AAAA,EAC5C;AACF;AAEe,SAAR,kBAAiB,QAAQ;AAC9B,MAAI,OAAO,WAAW,WAAY,UAAS,SAAS,MAAM;AAAA,MACrD,UAAS,oBAAY,MAAM;AAEhC,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,YAAY,CAAC,GAAG,UAAU,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAClG,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAO,MAAM,CAAC,GAAG;AACnB,kBAAU,KAAK,OAAO,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,CAAC;AACzD,gBAAQ,KAAK,IAAI;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,UAAU,WAAW,OAAO;AACzC;;;ACtBA,IAAI,OAAO,MAAM,UAAU;AAE3B,SAAS,UAAU,OAAO;AACxB,SAAO,WAAW;AAChB,WAAO,KAAK,KAAK,KAAK,UAAU,KAAK;AAAA,EACvC;AACF;AAEA,SAAS,aAAa;AACpB,SAAO,KAAK;AACd;AAEe,SAAR,oBAAiB,OAAO;AAC7B,SAAO,KAAK,OAAO,SAAS,OAAO,aAC7B,UAAU,OAAO,UAAU,aAAa,QAAQ,aAAa,KAAK,CAAC,CAAC;AAC5E;;;ACfA,IAAI,SAAS,MAAM,UAAU;AAE7B,SAAS,WAAW;AAClB,SAAO,MAAM,KAAK,KAAK,QAAQ;AACjC;AAEA,SAAS,eAAe,OAAO;AAC7B,SAAO,WAAW;AAChB,WAAO,OAAO,KAAK,KAAK,UAAU,KAAK;AAAA,EACzC;AACF;AAEe,SAAR,uBAAiB,OAAO;AAC7B,SAAO,KAAK,UAAU,SAAS,OAAO,WAChC,eAAe,OAAO,UAAU,aAAa,QAAQ,aAAa,KAAK,CAAC,CAAC;AACjF;;;ACde,SAAR,eAAiB,OAAO;AAC7B,MAAI,OAAO,UAAU,WAAY,SAAQ,gBAAQ,KAAK;AAEtD,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,YAAY,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC9F,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,WAAW,UAAU,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACnG,WAAK,OAAO,MAAM,CAAC,MAAM,MAAM,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,GAAG;AAClE,iBAAS,KAAK,IAAI;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,UAAU,WAAW,KAAK,QAAQ;AAC/C;;;ACfe,SAAR,eAAiB,QAAQ;AAC9B,SAAO,IAAI,MAAM,OAAO,MAAM;AAChC;;;ACCe,SAAR,gBAAmB;AACxB,SAAO,IAAI,UAAU,KAAK,UAAU,KAAK,QAAQ,IAAI,cAAM,GAAG,KAAK,QAAQ;AAC7E;AAEO,SAAS,UAAU,QAAQC,QAAO;AACvC,OAAK,gBAAgB,OAAO;AAC5B,OAAK,eAAe,OAAO;AAC3B,OAAK,QAAQ;AACb,OAAK,UAAU;AACf,OAAK,WAAWA;AAClB;AAEA,UAAU,YAAY;AAAA,EACpB,aAAa;AAAA,EACb,aAAa,SAAS,OAAO;AAAE,WAAO,KAAK,QAAQ,aAAa,OAAO,KAAK,KAAK;AAAA,EAAG;AAAA,EACpF,cAAc,SAAS,OAAO,MAAM;AAAE,WAAO,KAAK,QAAQ,aAAa,OAAO,IAAI;AAAA,EAAG;AAAA,EACrF,eAAe,SAAS,UAAU;AAAE,WAAO,KAAK,QAAQ,cAAc,QAAQ;AAAA,EAAG;AAAA,EACjF,kBAAkB,SAAS,UAAU;AAAE,WAAO,KAAK,QAAQ,iBAAiB,QAAQ;AAAA,EAAG;AACzF;;;ACrBe,SAAR,iBAAiB,GAAG;AACzB,SAAO,WAAW;AAChB,WAAO;AAAA,EACT;AACF;;;ACAA,SAAS,UAAU,QAAQ,OAAO,OAAO,QAAQ,MAAM,MAAM;AAC3D,MAAI,IAAI,GACJ,MACA,cAAc,MAAM,QACpB,aAAa,KAAK;AAKtB,SAAO,IAAI,YAAY,EAAE,GAAG;AAC1B,QAAI,OAAO,MAAM,CAAC,GAAG;AACnB,WAAK,WAAW,KAAK,CAAC;AACtB,aAAO,CAAC,IAAI;AAAA,IACd,OAAO;AACL,YAAM,CAAC,IAAI,IAAI,UAAU,QAAQ,KAAK,CAAC,CAAC;AAAA,IAC1C;AAAA,EACF;AAGA,SAAO,IAAI,aAAa,EAAE,GAAG;AAC3B,QAAI,OAAO,MAAM,CAAC,GAAG;AACnB,WAAK,CAAC,IAAI;AAAA,IACZ;AAAA,EACF;AACF;AAEA,SAAS,QAAQ,QAAQ,OAAO,OAAO,QAAQ,MAAM,MAAM,KAAK;AAC9D,MAAI,GACA,MACA,iBAAiB,oBAAI,OACrB,cAAc,MAAM,QACpB,aAAa,KAAK,QAClB,YAAY,IAAI,MAAM,WAAW,GACjC;AAIJ,OAAK,IAAI,GAAG,IAAI,aAAa,EAAE,GAAG;AAChC,QAAI,OAAO,MAAM,CAAC,GAAG;AACnB,gBAAU,CAAC,IAAI,WAAW,IAAI,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,IAAI;AACpE,UAAI,eAAe,IAAI,QAAQ,GAAG;AAChC,aAAK,CAAC,IAAI;AAAA,MACZ,OAAO;AACL,uBAAe,IAAI,UAAU,IAAI;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AAKA,OAAK,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AAC/B,eAAW,IAAI,KAAK,QAAQ,KAAK,CAAC,GAAG,GAAG,IAAI,IAAI;AAChD,QAAI,OAAO,eAAe,IAAI,QAAQ,GAAG;AACvC,aAAO,CAAC,IAAI;AACZ,WAAK,WAAW,KAAK,CAAC;AACtB,qBAAe,OAAO,QAAQ;AAAA,IAChC,OAAO;AACL,YAAM,CAAC,IAAI,IAAI,UAAU,QAAQ,KAAK,CAAC,CAAC;AAAA,IAC1C;AAAA,EACF;AAGA,OAAK,IAAI,GAAG,IAAI,aAAa,EAAE,GAAG;AAChC,SAAK,OAAO,MAAM,CAAC,MAAO,eAAe,IAAI,UAAU,CAAC,CAAC,MAAM,MAAO;AACpE,WAAK,CAAC,IAAI;AAAA,IACZ;AAAA,EACF;AACF;AAEA,SAAS,MAAM,MAAM;AACnB,SAAO,KAAK;AACd;AAEe,SAAR,aAAiB,OAAO,KAAK;AAClC,MAAI,CAAC,UAAU,OAAQ,QAAO,MAAM,KAAK,MAAM,KAAK;AAEpD,MAAI,OAAO,MAAM,UAAU,WACvB,UAAU,KAAK,UACf,SAAS,KAAK;AAElB,MAAI,OAAO,UAAU,WAAY,SAAQ,iBAAS,KAAK;AAEvD,WAAS,IAAI,OAAO,QAAQ,SAAS,IAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,MAAM,CAAC,GAAG,OAAO,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/G,QAAI,SAAS,QAAQ,CAAC,GAClB,QAAQ,OAAO,CAAC,GAChB,cAAc,MAAM,QACpB,OAAO,UAAU,MAAM,KAAK,QAAQ,UAAU,OAAO,UAAU,GAAG,OAAO,CAAC,GAC1E,aAAa,KAAK,QAClB,aAAa,MAAM,CAAC,IAAI,IAAI,MAAM,UAAU,GAC5C,cAAc,OAAO,CAAC,IAAI,IAAI,MAAM,UAAU,GAC9C,YAAY,KAAK,CAAC,IAAI,IAAI,MAAM,WAAW;AAE/C,SAAK,QAAQ,OAAO,YAAY,aAAa,WAAW,MAAM,GAAG;AAKjE,aAAS,KAAK,GAAG,KAAK,GAAG,UAAU,MAAM,KAAK,YAAY,EAAE,IAAI;AAC9D,UAAI,WAAW,WAAW,EAAE,GAAG;AAC7B,YAAI,MAAM,GAAI,MAAK,KAAK;AACxB,eAAO,EAAE,OAAO,YAAY,EAAE,MAAM,EAAE,KAAK,WAAW;AACtD,iBAAS,QAAQ,QAAQ;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAEA,WAAS,IAAI,UAAU,QAAQ,OAAO;AACtC,SAAO,SAAS;AAChB,SAAO,QAAQ;AACf,SAAO;AACT;AAQA,SAAS,UAAU,MAAM;AACvB,SAAO,OAAO,SAAS,YAAY,YAAY,OAC3C,OACA,MAAM,KAAK,IAAI;AACrB;;;AC5He,SAAR,eAAmB;AACxB,SAAO,IAAI,UAAU,KAAK,SAAS,KAAK,QAAQ,IAAI,cAAM,GAAG,KAAK,QAAQ;AAC5E;;;ACLe,SAAR,aAAiB,SAAS,UAAU,QAAQ;AACjD,MAAI,QAAQ,KAAK,MAAM,GAAG,SAAS,MAAM,OAAO,KAAK,KAAK;AAC1D,MAAI,OAAO,YAAY,YAAY;AACjC,YAAQ,QAAQ,KAAK;AACrB,QAAI,MAAO,SAAQ,MAAM,UAAU;AAAA,EACrC,OAAO;AACL,YAAQ,MAAM,OAAO,UAAU,EAAE;AAAA,EACnC;AACA,MAAI,YAAY,MAAM;AACpB,aAAS,SAAS,MAAM;AACxB,QAAI,OAAQ,UAAS,OAAO,UAAU;AAAA,EACxC;AACA,MAAI,UAAU,KAAM,MAAK,OAAO;AAAA,MAAQ,QAAO,IAAI;AACnD,SAAO,SAAS,SAAS,MAAM,MAAM,MAAM,EAAE,MAAM,IAAI;AACzD;;;ACZe,SAAR,cAAiB,SAAS;AAC/B,MAAIC,aAAY,QAAQ,YAAY,QAAQ,UAAU,IAAI;AAE1D,WAAS,UAAU,KAAK,SAAS,UAAUA,WAAU,SAAS,KAAK,QAAQ,QAAQ,KAAK,QAAQ,QAAQ,IAAI,KAAK,IAAI,IAAI,EAAE,GAAG,SAAS,IAAI,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACvK,aAAS,SAAS,QAAQ,CAAC,GAAG,SAAS,QAAQ,CAAC,GAAG,IAAI,OAAO,QAAQ,QAAQ,OAAO,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/H,UAAI,OAAO,OAAO,CAAC,KAAK,OAAO,CAAC,GAAG;AACjC,cAAM,CAAC,IAAI;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,IAAI,EAAE,GAAG;AAClB,WAAO,CAAC,IAAI,QAAQ,CAAC;AAAA,EACvB;AAEA,SAAO,IAAI,UAAU,QAAQ,KAAK,QAAQ;AAC5C;;;AClBe,SAAR,gBAAmB;AAExB,WAAS,SAAS,KAAK,SAAS,IAAI,IAAI,IAAI,OAAO,QAAQ,EAAE,IAAI,KAAI;AACnE,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,SAAS,GAAG,OAAO,MAAM,CAAC,GAAG,MAAM,EAAE,KAAK,KAAI;AAClF,UAAI,OAAO,MAAM,CAAC,GAAG;AACnB,YAAI,QAAQ,KAAK,wBAAwB,IAAI,IAAI,EAAG,MAAK,WAAW,aAAa,MAAM,IAAI;AAC3F,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;ACVe,SAAR,aAAiB,SAAS;AAC/B,MAAI,CAAC,QAAS,WAAU;AAExB,WAAS,YAAY,GAAG,GAAG;AACzB,WAAO,KAAK,IAAI,QAAQ,EAAE,UAAU,EAAE,QAAQ,IAAI,CAAC,IAAI,CAAC;AAAA,EAC1D;AAEA,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,aAAa,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/F,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,YAAY,WAAW,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/G,UAAI,OAAO,MAAM,CAAC,GAAG;AACnB,kBAAU,CAAC,IAAI;AAAA,MACjB;AAAA,IACF;AACA,cAAU,KAAK,WAAW;AAAA,EAC5B;AAEA,SAAO,IAAI,UAAU,YAAY,KAAK,QAAQ,EAAE,MAAM;AACxD;AAEA,SAAS,UAAU,GAAG,GAAG;AACvB,SAAO,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;AAC/C;;;ACvBe,SAAR,eAAmB;AACxB,MAAI,WAAW,UAAU,CAAC;AAC1B,YAAU,CAAC,IAAI;AACf,WAAS,MAAM,MAAM,SAAS;AAC9B,SAAO;AACT;;;ACLe,SAAR,gBAAmB;AACxB,SAAO,MAAM,KAAK,IAAI;AACxB;;;ACFe,SAAR,eAAmB;AAExB,WAAS,SAAS,KAAK,SAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AACpE,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC/D,UAAI,OAAO,MAAM,CAAC;AAClB,UAAI,KAAM,QAAO;AAAA,IACnB;AAAA,EACF;AAEA,SAAO;AACT;;;ACVe,SAAR,eAAmB;AACxB,MAAI,OAAO;AACX,aAAW,QAAQ,KAAM,GAAE;AAC3B,SAAO;AACT;;;ACJe,SAAR,gBAAmB;AACxB,SAAO,CAAC,KAAK,KAAK;AACpB;;;ACFe,SAAR,aAAiB,UAAU;AAEhC,WAAS,SAAS,KAAK,SAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AACpE,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAO,MAAM,CAAC,EAAG,UAAS,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK;AAAA,IAClE;AAAA,EACF;AAEA,SAAO;AACT;;;ACPA,SAAS,WAAW,MAAM;AACxB,SAAO,WAAW;AAChB,SAAK,gBAAgB,IAAI;AAAA,EAC3B;AACF;AAEA,SAAS,aAAa,UAAU;AAC9B,SAAO,WAAW;AAChB,SAAK,kBAAkB,SAAS,OAAO,SAAS,KAAK;AAAA,EACvD;AACF;AAEA,SAAS,aAAa,MAAM,OAAO;AACjC,SAAO,WAAW;AAChB,SAAK,aAAa,MAAM,KAAK;AAAA,EAC/B;AACF;AAEA,SAAS,eAAe,UAAU,OAAO;AACvC,SAAO,WAAW;AAChB,SAAK,eAAe,SAAS,OAAO,SAAS,OAAO,KAAK;AAAA,EAC3D;AACF;AAEA,SAAS,aAAa,MAAM,OAAO;AACjC,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,KAAK,KAAM,MAAK,gBAAgB,IAAI;AAAA,QACnC,MAAK,aAAa,MAAM,CAAC;AAAA,EAChC;AACF;AAEA,SAAS,eAAe,UAAU,OAAO;AACvC,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,KAAK,KAAM,MAAK,kBAAkB,SAAS,OAAO,SAAS,KAAK;AAAA,QAC/D,MAAK,eAAe,SAAS,OAAO,SAAS,OAAO,CAAC;AAAA,EAC5D;AACF;AAEe,SAAR,aAAiB,MAAM,OAAO;AACnC,MAAI,WAAW,kBAAU,IAAI;AAE7B,MAAI,UAAU,SAAS,GAAG;AACxB,QAAI,OAAO,KAAK,KAAK;AACrB,WAAO,SAAS,QACV,KAAK,eAAe,SAAS,OAAO,SAAS,KAAK,IAClD,KAAK,aAAa,QAAQ;AAAA,EAClC;AAEA,SAAO,KAAK,MAAM,SAAS,OACpB,SAAS,QAAQ,eAAe,aAAe,OAAO,UAAU,aAChE,SAAS,QAAQ,iBAAiB,eAClC,SAAS,QAAQ,iBAAiB,cAAgB,UAAU,KAAK,CAAC;AAC3E;;;ACxDA,SAAS,eAAe,MAAM;AAC5B,SAAO,WAAW;AAChB,WAAO,KAAK,IAAI;AAAA,EAClB;AACF;AAEA,SAAS,iBAAiB,MAAM,OAAO;AACrC,SAAO,WAAW;AAChB,SAAK,IAAI,IAAI;AAAA,EACf;AACF;AAEA,SAAS,iBAAiB,MAAM,OAAO;AACrC,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,KAAK,KAAM,QAAO,KAAK,IAAI;AAAA,QAC1B,MAAK,IAAI,IAAI;AAAA,EACpB;AACF;AAEe,SAAR,iBAAiB,MAAM,OAAO;AACnC,SAAO,UAAU,SAAS,IACpB,KAAK,MAAM,SAAS,OAChB,iBAAiB,OAAO,UAAU,aAClC,mBACA,kBAAkB,MAAM,KAAK,CAAC,IAClC,KAAK,KAAK,EAAE,IAAI;AACxB;;;AC3BA,SAAS,WAAW,QAAQ;AAC1B,SAAO,OAAO,KAAK,EAAE,MAAM,OAAO;AACpC;AAEA,SAAS,UAAU,MAAM;AACvB,SAAO,KAAK,aAAa,IAAI,UAAU,IAAI;AAC7C;AAEA,SAAS,UAAU,MAAM;AACvB,OAAK,QAAQ;AACb,OAAK,SAAS,WAAW,KAAK,aAAa,OAAO,KAAK,EAAE;AAC3D;AAEA,UAAU,YAAY;AAAA,EACpB,KAAK,SAAS,MAAM;AAClB,QAAI,IAAI,KAAK,OAAO,QAAQ,IAAI;AAChC,QAAI,IAAI,GAAG;AACT,WAAK,OAAO,KAAK,IAAI;AACrB,WAAK,MAAM,aAAa,SAAS,KAAK,OAAO,KAAK,GAAG,CAAC;AAAA,IACxD;AAAA,EACF;AAAA,EACA,QAAQ,SAAS,MAAM;AACrB,QAAI,IAAI,KAAK,OAAO,QAAQ,IAAI;AAChC,QAAI,KAAK,GAAG;AACV,WAAK,OAAO,OAAO,GAAG,CAAC;AACvB,WAAK,MAAM,aAAa,SAAS,KAAK,OAAO,KAAK,GAAG,CAAC;AAAA,IACxD;AAAA,EACF;AAAA,EACA,UAAU,SAAS,MAAM;AACvB,WAAO,KAAK,OAAO,QAAQ,IAAI,KAAK;AAAA,EACtC;AACF;AAEA,SAAS,WAAW,MAAM,OAAO;AAC/B,MAAI,OAAO,UAAU,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM;AAC9C,SAAO,EAAE,IAAI,EAAG,MAAK,IAAI,MAAM,CAAC,CAAC;AACnC;AAEA,SAAS,cAAc,MAAM,OAAO;AAClC,MAAI,OAAO,UAAU,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM;AAC9C,SAAO,EAAE,IAAI,EAAG,MAAK,OAAO,MAAM,CAAC,CAAC;AACtC;AAEA,SAAS,YAAY,OAAO;AAC1B,SAAO,WAAW;AAChB,eAAW,MAAM,KAAK;AAAA,EACxB;AACF;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,kBAAc,MAAM,KAAK;AAAA,EAC3B;AACF;AAEA,SAAS,gBAAgB,OAAO,OAAO;AACrC,SAAO,WAAW;AAChB,KAAC,MAAM,MAAM,MAAM,SAAS,IAAI,aAAa,eAAe,MAAM,KAAK;AAAA,EACzE;AACF;AAEe,SAAR,gBAAiB,MAAM,OAAO;AACnC,MAAI,QAAQ,WAAW,OAAO,EAAE;AAEhC,MAAI,UAAU,SAAS,GAAG;AACxB,QAAI,OAAO,UAAU,KAAK,KAAK,CAAC,GAAG,IAAI,IAAI,IAAI,MAAM;AACrD,WAAO,EAAE,IAAI,EAAG,KAAI,CAAC,KAAK,SAAS,MAAM,CAAC,CAAC,EAAG,QAAO;AACrD,WAAO;AAAA,EACT;AAEA,SAAO,KAAK,MAAM,OAAO,UAAU,aAC7B,kBAAkB,QAClB,cACA,cAAc,OAAO,KAAK,CAAC;AACnC;;;AC1EA,SAAS,aAAa;AACpB,OAAK,cAAc;AACrB;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,SAAK,cAAc;AAAA,EACrB;AACF;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,SAAK,cAAc,KAAK,OAAO,KAAK;AAAA,EACtC;AACF;AAEe,SAAR,aAAiB,OAAO;AAC7B,SAAO,UAAU,SACX,KAAK,KAAK,SAAS,OACf,cAAc,OAAO,UAAU,aAC/B,eACA,cAAc,KAAK,CAAC,IACxB,KAAK,KAAK,EAAE;AACpB;;;ACxBA,SAAS,aAAa;AACpB,OAAK,YAAY;AACnB;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,SAAK,YAAY;AAAA,EACnB;AACF;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,SAAK,YAAY,KAAK,OAAO,KAAK;AAAA,EACpC;AACF;AAEe,SAAR,aAAiB,OAAO;AAC7B,SAAO,UAAU,SACX,KAAK,KAAK,SAAS,OACf,cAAc,OAAO,UAAU,aAC/B,eACA,cAAc,KAAK,CAAC,IACxB,KAAK,KAAK,EAAE;AACpB;;;ACxBA,SAAS,QAAQ;AACf,MAAI,KAAK,YAAa,MAAK,WAAW,YAAY,IAAI;AACxD;AAEe,SAAR,gBAAmB;AACxB,SAAO,KAAK,KAAK,KAAK;AACxB;;;ACNA,SAAS,QAAQ;AACf,MAAI,KAAK,gBAAiB,MAAK,WAAW,aAAa,MAAM,KAAK,WAAW,UAAU;AACzF;AAEe,SAAR,gBAAmB;AACxB,SAAO,KAAK,KAAK,KAAK;AACxB;;;ACJe,SAAR,eAAiB,MAAM;AAC5B,MAAI,SAAS,OAAO,SAAS,aAAa,OAAO,gBAAQ,IAAI;AAC7D,SAAO,KAAK,OAAO,WAAW;AAC5B,WAAO,KAAK,YAAY,OAAO,MAAM,MAAM,SAAS,CAAC;AAAA,EACvD,CAAC;AACH;;;ACJA,SAAS,eAAe;AACtB,SAAO;AACT;AAEe,SAAR,eAAiB,MAAM,QAAQ;AACpC,MAAI,SAAS,OAAO,SAAS,aAAa,OAAO,gBAAQ,IAAI,GACzD,SAAS,UAAU,OAAO,eAAe,OAAO,WAAW,aAAa,SAAS,iBAAS,MAAM;AACpG,SAAO,KAAK,OAAO,WAAW;AAC5B,WAAO,KAAK,aAAa,OAAO,MAAM,MAAM,SAAS,GAAG,OAAO,MAAM,MAAM,SAAS,KAAK,IAAI;AAAA,EAC/F,CAAC;AACH;;;ACbA,SAAS,SAAS;AAChB,MAAI,SAAS,KAAK;AAClB,MAAI,OAAQ,QAAO,YAAY,IAAI;AACrC;AAEe,SAAR,iBAAmB;AACxB,SAAO,KAAK,KAAK,MAAM;AACzB;;;ACPA,SAAS,yBAAyB;AAChC,MAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,SAAS,KAAK;AACjD,SAAO,SAAS,OAAO,aAAa,OAAO,KAAK,WAAW,IAAI;AACjE;AAEA,SAAS,sBAAsB;AAC7B,MAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,SAAS,KAAK;AAChD,SAAO,SAAS,OAAO,aAAa,OAAO,KAAK,WAAW,IAAI;AACjE;AAEe,SAAR,cAAiB,MAAM;AAC5B,SAAO,KAAK,OAAO,OAAO,sBAAsB,sBAAsB;AACxE;;;ACZe,SAAR,cAAiB,OAAO;AAC7B,SAAO,UAAU,SACX,KAAK,SAAS,YAAY,KAAK,IAC/B,KAAK,KAAK,EAAE;AACpB;;;ACJA,SAAS,gBAAgB,UAAU;AACjC,SAAO,SAAS,OAAO;AACrB,aAAS,KAAK,MAAM,OAAO,KAAK,QAAQ;AAAA,EAC1C;AACF;AAEA,SAAS,eAAe,WAAW;AACjC,SAAO,UAAU,KAAK,EAAE,MAAM,OAAO,EAAE,IAAI,SAAS,GAAG;AACrD,QAAI,OAAO,IAAI,IAAI,EAAE,QAAQ,GAAG;AAChC,QAAI,KAAK,EAAG,QAAO,EAAE,MAAM,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,GAAG,CAAC;AACnD,WAAO,EAAC,MAAM,GAAG,KAAU;AAAA,EAC7B,CAAC;AACH;AAEA,SAAS,SAAS,UAAU;AAC1B,SAAO,WAAW;AAChB,QAAI,KAAK,KAAK;AACd,QAAI,CAAC,GAAI;AACT,aAAS,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,QAAQ,GAAG,IAAI,GAAG,EAAE,GAAG;AACpD,UAAI,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,QAAQ,EAAE,SAAS,SAAS,SAAS,EAAE,SAAS,SAAS,MAAM;AACvF,aAAK,oBAAoB,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO;AAAA,MACxD,OAAO;AACL,WAAG,EAAE,CAAC,IAAI;AAAA,MACZ;AAAA,IACF;AACA,QAAI,EAAE,EAAG,IAAG,SAAS;AAAA,QAChB,QAAO,KAAK;AAAA,EACnB;AACF;AAEA,SAAS,MAAM,UAAU,OAAO,SAAS;AACvC,SAAO,WAAW;AAChB,QAAI,KAAK,KAAK,MAAM,GAAG,WAAW,gBAAgB,KAAK;AACvD,QAAI,GAAI,UAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,IAAI,GAAG,EAAE,GAAG;AACjD,WAAK,IAAI,GAAG,CAAC,GAAG,SAAS,SAAS,QAAQ,EAAE,SAAS,SAAS,MAAM;AAClE,aAAK,oBAAoB,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO;AACtD,aAAK,iBAAiB,EAAE,MAAM,EAAE,WAAW,UAAU,EAAE,UAAU,OAAO;AACxE,UAAE,QAAQ;AACV;AAAA,MACF;AAAA,IACF;AACA,SAAK,iBAAiB,SAAS,MAAM,UAAU,OAAO;AACtD,QAAI,EAAC,MAAM,SAAS,MAAM,MAAM,SAAS,MAAM,OAAc,UAAoB,QAAgB;AACjG,QAAI,CAAC,GAAI,MAAK,OAAO,CAAC,CAAC;AAAA,QAClB,IAAG,KAAK,CAAC;AAAA,EAChB;AACF;AAEe,SAAR,WAAiB,UAAU,OAAO,SAAS;AAChD,MAAI,YAAY,eAAe,WAAW,EAAE,GAAG,GAAG,IAAI,UAAU,QAAQ;AAExE,MAAI,UAAU,SAAS,GAAG;AACxB,QAAI,KAAK,KAAK,KAAK,EAAE;AACrB,QAAI,GAAI,UAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,GAAG,IAAI,GAAG,EAAE,GAAG;AACpD,WAAK,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG;AACjC,aAAK,IAAI,UAAU,CAAC,GAAG,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM;AAC3D,iBAAO,EAAE;AAAA,QACX;AAAA,MACF;AAAA,IACF;AACA;AAAA,EACF;AAEA,OAAK,QAAQ,QAAQ;AACrB,OAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,MAAK,KAAK,GAAG,UAAU,CAAC,GAAG,OAAO,OAAO,CAAC;AAClE,SAAO;AACT;;;AChEA,SAAS,cAAc,MAAM,MAAM,QAAQ;AACzC,MAAI,SAAS,eAAY,IAAI,GACzB,QAAQ,OAAO;AAEnB,MAAI,OAAO,UAAU,YAAY;AAC/B,YAAQ,IAAI,MAAM,MAAM,MAAM;AAAA,EAChC,OAAO;AACL,YAAQ,OAAO,SAAS,YAAY,OAAO;AAC3C,QAAI,OAAQ,OAAM,UAAU,MAAM,OAAO,SAAS,OAAO,UAAU,GAAG,MAAM,SAAS,OAAO;AAAA,QACvF,OAAM,UAAU,MAAM,OAAO,KAAK;AAAA,EACzC;AAEA,OAAK,cAAc,KAAK;AAC1B;AAEA,SAAS,iBAAiB,MAAM,QAAQ;AACtC,SAAO,WAAW;AAChB,WAAO,cAAc,MAAM,MAAM,MAAM;AAAA,EACzC;AACF;AAEA,SAAS,iBAAiB,MAAM,QAAQ;AACtC,SAAO,WAAW;AAChB,WAAO,cAAc,MAAM,MAAM,OAAO,MAAM,MAAM,SAAS,CAAC;AAAA,EAChE;AACF;AAEe,SAAR,iBAAiB,MAAM,QAAQ;AACpC,SAAO,KAAK,MAAM,OAAO,WAAW,aAC9B,mBACA,kBAAkB,MAAM,MAAM,CAAC;AACvC;;;ACjCe,UAAR,mBAAoB;AACzB,WAAS,SAAS,KAAK,SAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AACpE,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAO,MAAM,CAAC,EAAG,OAAM;AAAA,IAC7B;AAAA,EACF;AACF;;;AC6BO,IAAI,OAAO,CAAC,IAAI;AAEhB,SAAS,UAAU,QAAQ,SAAS;AACzC,OAAK,UAAU;AACf,OAAK,WAAW;AAClB;AAEA,SAAS,YAAY;AACnB,SAAO,IAAI,UAAU,CAAC,CAAC,SAAS,eAAe,CAAC,GAAG,IAAI;AACzD;AAEA,SAAS,sBAAsB;AAC7B,SAAO;AACT;AAEA,UAAU,YAAY,UAAU,YAAY;AAAA,EAC1C,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,WAAW;AAAA,EACX,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,UAAU;AAAA,EACV,SAAS;AAAA,EACT,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,UAAU;AAAA,EACV,CAAC,OAAO,QAAQ,GAAG;AACrB;AAEA,IAAO,oBAAQ;;;ACvFA,SAARC,gBAAiB,UAAU;AAChC,SAAO,OAAO,aAAa,WACrB,IAAI,UAAU,CAAC,CAAC,SAAS,cAAc,QAAQ,CAAC,CAAC,GAAG,CAAC,SAAS,eAAe,CAAC,IAC9E,IAAI,UAAU,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI;AACxC;;;ACHe,SAAR,eAAiB,MAAM;AAC5B,SAAOC,gBAAO,gBAAQ,IAAI,EAAE,KAAK,SAAS,eAAe,CAAC;AAC5D;;;ACLA,IAAI,SAAS;AAEE,SAAR,QAAyB;AAC9B,SAAO,IAAI;AACb;AAEA,SAAS,QAAQ;AACf,OAAK,IAAI,OAAO,EAAE,QAAQ,SAAS,EAAE;AACvC;AAEA,MAAM,YAAY,MAAM,YAAY;AAAA,EAClC,aAAa;AAAA,EACb,KAAK,SAAS,MAAM;AAClB,QAAI,KAAK,KAAK;AACd,WAAO,EAAE,MAAM,MAAO,KAAI,EAAE,OAAO,KAAK,YAAa;AACrD,WAAO,KAAK,EAAE;AAAA,EAChB;AAAA,EACA,KAAK,SAAS,MAAM,OAAO;AACzB,WAAO,KAAK,KAAK,CAAC,IAAI;AAAA,EACxB;AAAA,EACA,QAAQ,SAAS,MAAM;AACrB,WAAO,KAAK,KAAK,QAAQ,OAAO,KAAK,KAAK,CAAC;AAAA,EAC7C;AAAA,EACA,UAAU,WAAW;AACnB,WAAO,KAAK;AAAA,EACd;AACF;;;AC1Be,SAAR,oBAAiB,OAAO;AAC7B,MAAI;AACJ,SAAO,cAAc,MAAM,YAAa,SAAQ;AAChD,SAAO;AACT;;;ACFe,SAAR,gBAAiB,OAAO,MAAM;AACnC,UAAQ,oBAAY,KAAK;AACzB,MAAI,SAAS,OAAW,QAAO,MAAM;AACrC,MAAI,MAAM;AACR,QAAI,MAAM,KAAK,mBAAmB;AAClC,QAAI,IAAI,gBAAgB;AACtB,UAAI,QAAQ,IAAI,eAAe;AAC/B,YAAM,IAAI,MAAM,SAAS,MAAM,IAAI,MAAM;AACzC,cAAQ,MAAM,gBAAgB,KAAK,aAAa,EAAE,QAAQ,CAAC;AAC3D,aAAO,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,IAC1B;AACA,QAAI,KAAK,uBAAuB;AAC9B,UAAI,OAAO,KAAK,sBAAsB;AACtC,aAAO,CAAC,MAAM,UAAU,KAAK,OAAO,KAAK,YAAY,MAAM,UAAU,KAAK,MAAM,KAAK,SAAS;AAAA,IAChG;AAAA,EACF;AACA,SAAO,CAAC,MAAM,OAAO,MAAM,KAAK;AAClC;;;AChBe,SAAR,iBAAiB,QAAQ,MAAM;AACpC,MAAI,OAAO,QAAQ;AACjB,aAAS,oBAAY,MAAM;AAC3B,QAAI,SAAS,OAAW,QAAO,OAAO;AACtC,aAAS,OAAO,WAAW,CAAC,MAAM;AAAA,EACpC;AACA,SAAO,MAAM,KAAK,QAAQ,WAAS,gBAAQ,OAAO,IAAI,CAAC;AACzD;;;ACPe,SAARC,mBAAiB,UAAU;AAChC,SAAO,OAAO,aAAa,WACrB,IAAI,UAAU,CAAC,SAAS,iBAAiB,QAAQ,CAAC,GAAG,CAAC,SAAS,eAAe,CAAC,IAC/E,IAAI,UAAU,CAAC,MAAM,QAAQ,CAAC,GAAG,IAAI;AAC7C;", "names": ["document", "datum", "selection", "select_default", "select_default", "selectAll_default"]}